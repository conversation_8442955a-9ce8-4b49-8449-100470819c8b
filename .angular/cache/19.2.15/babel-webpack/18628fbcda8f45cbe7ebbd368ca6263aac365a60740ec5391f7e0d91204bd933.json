{"ast": null, "code": "var _SwuiTdIconWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./icon.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./icon.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdIconWidgetComponent = (_SwuiTdIconWidgetComponent = class SwuiTdIconWidgetComponent {\n  constructor({\n    row,\n    schema,\n    value\n  }) {\n    var _schema$td, _schema$td2, _schema$td3, _schema$td4, _schema$td5, _schema$td6, _schema$td7, _schema$td8, _schema$td9;\n    const activeFn = (_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.canActivateFn;\n    const titleFn = (_schema$td2 = schema.td) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.titleFn;\n    const linkFn = (_schema$td3 = schema.td) === null || _schema$td3 === void 0 ? void 0 : _schema$td3.linkFn;\n    const classFn = (_schema$td4 = schema.td) === null || _schema$td4 === void 0 ? void 0 : _schema$td4.classFn;\n    this.icon = (_schema$td5 = schema.td) !== null && _schema$td5 !== void 0 && _schema$td5.icon ? schema.td.icon : undefined;\n    this.svgIcon = (_schema$td6 = schema.td) !== null && _schema$td6 !== void 0 && _schema$td6.svgIcon ? schema.td.svgIcon : undefined;\n    this.fontSet = (_schema$td7 = schema.td) !== null && _schema$td7 !== void 0 && _schema$td7.fontSet ? schema.td.fontSet : undefined;\n    this.fontIcon = (_schema$td8 = schema.td) !== null && _schema$td8 !== void 0 && _schema$td8.fontIcon ? schema.td.fontIcon : undefined;\n    this.title = titleFn && titleFn(row, schema) || value;\n    this.canActivate = activeFn && activeFn(row, schema) || value;\n    this.routerLinkData = linkFn && linkFn(row, schema) || [];\n    this.classObj = classFn && classFn(row, schema);\n    this.useTranslate = schema.td && 'useTranslate' in schema.td ? ((_schema$td9 = schema.td) === null || _schema$td9 === void 0 ? void 0 : _schema$td9.useTranslate) || false : true;\n  }\n}, _SwuiTdIconWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdIconWidgetComponent);\nSwuiTdIconWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-icon-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdIconWidgetComponent);\nexport { SwuiTdIconWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdIconWidgetComponent", "_SwuiTdIconWidgetComponent", "constructor", "row", "schema", "value", "_schema$td", "_schema$td2", "_schema$td3", "_schema$td4", "_schema$td5", "_schema$td6", "_schema$td7", "_schema$td8", "_schema$td9", "activeFn", "td", "canActivateFn", "titleFn", "linkFn", "classFn", "icon", "undefined", "svgIcon", "fontSet", "fontIcon", "title", "canActivate", "routerLinkData", "classObj", "useTranslate", "ctorParameters", "type", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/icon/icon.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./icon.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./icon.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdIconWidgetComponent = class SwuiTdIconWidgetComponent {\n    constructor({ row, schema, value }) {\n        const activeFn = schema.td?.canActivateFn;\n        const titleFn = schema.td?.titleFn;\n        const linkFn = schema.td?.linkFn;\n        const classFn = schema.td?.classFn;\n        this.icon = schema.td?.icon ? schema.td.icon : undefined;\n        this.svgIcon = schema.td?.svgIcon ? schema.td.svgIcon : undefined;\n        this.fontSet = schema.td?.fontSet ? schema.td.fontSet : undefined;\n        this.fontIcon = schema.td?.fontIcon ? schema.td.fontIcon : undefined;\n        this.title = (titleFn && titleFn(row, schema)) || value;\n        this.canActivate = (activeFn && activeFn(row, schema)) || value;\n        this.routerLinkData = linkFn && linkFn(row, schema) || [];\n        this.classObj = classFn && classFn(row, schema);\n        this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdIconWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-icon-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdIconWidgetComponent);\nexport { SwuiTdIconWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,CAAC;EAC5DE,WAAWA,CAAC;IAAEC,GAAG;IAAEC,MAAM;IAAEC;EAAM,CAAC,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;IAChC,MAAMC,QAAQ,IAAAT,UAAA,GAAGF,MAAM,CAACY,EAAE,cAAAV,UAAA,uBAATA,UAAA,CAAWW,aAAa;IACzC,MAAMC,OAAO,IAAAX,WAAA,GAAGH,MAAM,CAACY,EAAE,cAAAT,WAAA,uBAATA,WAAA,CAAWW,OAAO;IAClC,MAAMC,MAAM,IAAAX,WAAA,GAAGJ,MAAM,CAACY,EAAE,cAAAR,WAAA,uBAATA,WAAA,CAAWW,MAAM;IAChC,MAAMC,OAAO,IAAAX,WAAA,GAAGL,MAAM,CAACY,EAAE,cAAAP,WAAA,uBAATA,WAAA,CAAWW,OAAO;IAClC,IAAI,CAACC,IAAI,GAAG,CAAAX,WAAA,GAAAN,MAAM,CAACY,EAAE,cAAAN,WAAA,eAATA,WAAA,CAAWW,IAAI,GAAGjB,MAAM,CAACY,EAAE,CAACK,IAAI,GAAGC,SAAS;IACxD,IAAI,CAACC,OAAO,GAAG,CAAAZ,WAAA,GAAAP,MAAM,CAACY,EAAE,cAAAL,WAAA,eAATA,WAAA,CAAWY,OAAO,GAAGnB,MAAM,CAACY,EAAE,CAACO,OAAO,GAAGD,SAAS;IACjE,IAAI,CAACE,OAAO,GAAG,CAAAZ,WAAA,GAAAR,MAAM,CAACY,EAAE,cAAAJ,WAAA,eAATA,WAAA,CAAWY,OAAO,GAAGpB,MAAM,CAACY,EAAE,CAACQ,OAAO,GAAGF,SAAS;IACjE,IAAI,CAACG,QAAQ,GAAG,CAAAZ,WAAA,GAAAT,MAAM,CAACY,EAAE,cAAAH,WAAA,eAATA,WAAA,CAAWY,QAAQ,GAAGrB,MAAM,CAACY,EAAE,CAACS,QAAQ,GAAGH,SAAS;IACpE,IAAI,CAACI,KAAK,GAAIR,OAAO,IAAIA,OAAO,CAACf,GAAG,EAAEC,MAAM,CAAC,IAAKC,KAAK;IACvD,IAAI,CAACsB,WAAW,GAAIZ,QAAQ,IAAIA,QAAQ,CAACZ,GAAG,EAAEC,MAAM,CAAC,IAAKC,KAAK;IAC/D,IAAI,CAACuB,cAAc,GAAGT,MAAM,IAAIA,MAAM,CAAChB,GAAG,EAAEC,MAAM,CAAC,IAAI,EAAE;IACzD,IAAI,CAACyB,QAAQ,GAAGT,OAAO,IAAIA,OAAO,CAACjB,GAAG,EAAEC,MAAM,CAAC;IAC/C,IAAI,CAAC0B,YAAY,GAAG1B,MAAM,CAACY,EAAE,IAAI,cAAc,IAAIZ,MAAM,CAACY,EAAE,GAAG,EAAAF,WAAA,GAAAV,MAAM,CAACY,EAAE,cAAAF,WAAA,uBAATA,WAAA,CAAWgB,YAAY,KAAI,KAAK,GAAG,IAAI;EAC1G;AAIJ,CAAC,EAHY7B,0BAAA,CAAK8B,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEV,SAAS;EAAEW,UAAU,EAAE,CAAC;IAAED,IAAI,EAAElC,MAAM;IAAEoC,IAAI,EAAE,CAACnC,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,0BAAA,CACJ;AACDD,yBAAyB,GAAGN,UAAU,CAAC,CACnCG,SAAS,CAAC;EACNsC,QAAQ,EAAE,yBAAyB;EACnCC,QAAQ,EAAEzC,oBAAoB;EAC9B0C,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC1C,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEI,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}