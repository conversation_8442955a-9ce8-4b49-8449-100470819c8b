{"ast": null, "code": "var _I18nModule;\nvar I18nModule_1;\nimport { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';\nimport { of } from 'rxjs';\nexport class I18nLoader {\n  constructor(translations) {\n    this.translations = translations;\n  }\n  getTranslation(lang) {\n    return of(this.translations[lang]);\n  }\n}\nexport function i18nLoaderFactory(config) {\n  return () => new I18nLoader(config.translations);\n}\nlet I18nModule = (_I18nModule = class I18nModule {\n  constructor(translate) {\n    translate.setDefaultLang('en');\n    translate.use('en');\n  }\n  static forRoot(config = {\n    translations: {\n      en: {}\n    }\n  }) {\n    return {\n      ngModule: I18nModule_1,\n      providers: [{\n        provide: TranslateLoader,\n        useFactory: i18nLoaderFactory(config)\n      }]\n    };\n  }\n}, I18nModule_1 = _I18nModule, _I18nModule.ctorParameters = () => [{\n  type: TranslateService\n}], _I18nModule);\nI18nModule = I18nModule_1 = __decorate([NgModule({\n  imports: [TranslateModule.forRoot()],\n  exports: [TranslateModule]\n})], I18nModule);\nexport { I18nModule };", "map": {"version": 3, "names": ["I18nModule_1", "__decorate", "NgModule", "Translate<PERSON><PERSON><PERSON>", "TranslateModule", "TranslateService", "of", "I18nLoader", "constructor", "translations", "getTranslation", "lang", "i18nLoaderFactory", "config", "I18nModule", "_I18nModule", "translate", "setDefaultLang", "use", "forRoot", "en", "ngModule", "providers", "provide", "useFactory", "ctorParameters", "type", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/i18n.module.ts"], "sourcesContent": ["var I18nModule_1;\nimport { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';\nimport { of } from 'rxjs';\nexport class I18nLoader {\n    constructor(translations) {\n        this.translations = translations;\n    }\n    getTranslation(lang) {\n        return of(this.translations[lang]);\n    }\n}\nexport function i18nLoaderFactory(config) {\n    return () => new I18nLoader(config.translations);\n}\nlet I18nModule = class I18nModule {\n    static { I18nModule_1 = this; }\n    constructor(translate) {\n        translate.setDefaultLang('en');\n        translate.use('en');\n    }\n    static forRoot(config = { translations: { en: {} } }) {\n        return {\n            ngModule: I18nModule_1,\n            providers: [\n                { provide: TranslateLoader, useFactory: i18nLoaderFactory(config) }\n            ]\n        };\n    }\n    static { this.ctorParameters = () => [\n        { type: TranslateService }\n    ]; }\n};\nI18nModule = I18nModule_1 = __decorate([\n    NgModule({\n        imports: [\n            TranslateModule.forRoot(),\n        ],\n        exports: [\n            TranslateModule\n        ]\n    })\n], I18nModule);\nexport { I18nModule };\n"], "mappings": ";AAAA,IAAIA,YAAY;AAChB,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,qBAAqB;AACxF,SAASC,EAAE,QAAQ,MAAM;AACzB,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;EACAC,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAOL,EAAE,CAAC,IAAI,CAACG,YAAY,CAACE,IAAI,CAAC,CAAC;EACtC;AACJ;AACA,OAAO,SAASC,iBAAiBA,CAACC,MAAM,EAAE;EACtC,OAAO,MAAM,IAAIN,UAAU,CAACM,MAAM,CAACJ,YAAY,CAAC;AACpD;AACA,IAAIK,UAAU,IAAAC,WAAA,GAAG,MAAMD,UAAU,CAAC;EAE9BN,WAAWA,CAACQ,SAAS,EAAE;IACnBA,SAAS,CAACC,cAAc,CAAC,IAAI,CAAC;IAC9BD,SAAS,CAACE,GAAG,CAAC,IAAI,CAAC;EACvB;EACA,OAAOC,OAAOA,CAACN,MAAM,GAAG;IAAEJ,YAAY,EAAE;MAAEW,EAAE,EAAE,CAAC;IAAE;EAAE,CAAC,EAAE;IAClD,OAAO;MACHC,QAAQ,EAAErB,YAAY;MACtBsB,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEpB,eAAe;QAAEqB,UAAU,EAAEZ,iBAAiB,CAACC,MAAM;MAAE,CAAC;IAE3E,CAAC;EACL;AAIJ,CAAC,EAhBYb,YAAY,GAAAe,WAAO,EAanBA,WAAA,CAAKU,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAErB;AAAiB,CAAC,CAC7B,EAAAU,WAAA,CACJ;AACDD,UAAU,GAAGd,YAAY,GAAGC,UAAU,CAAC,CACnCC,QAAQ,CAAC;EACLyB,OAAO,EAAE,CACLvB,eAAe,CAACe,OAAO,CAAC,CAAC,CAC5B;EACDS,OAAO,EAAE,CACLxB,eAAe;AAEvB,CAAC,CAAC,CACL,EAAEU,UAAU,CAAC;AACd,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}