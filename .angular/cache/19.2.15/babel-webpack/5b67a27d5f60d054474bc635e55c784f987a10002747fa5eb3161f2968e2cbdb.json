{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _SharedResizeObserver;\nimport * as i0 from '@angular/core';\nimport { inject, NgZone, RendererFactory2, Injectable } from '@angular/core';\nimport { Subject, Observable } from 'rxjs';\nimport { filter, shareReplay, takeUntil } from 'rxjs/operators';\n\n/**\n * <PERSON><PERSON> that logs \"ResizeObserver loop limit exceeded\" errors.\n * These errors are not shown in the Chrome console, so we log them to ensure developers are aware.\n * @param e The error\n */\nconst loopLimitExceededErrorHandler = e => {\n  if (e instanceof ErrorEvent && e.message === 'ResizeObserver loop limit exceeded') {\n    console.error(`${e.message}. This could indicate a performance issue with your app. See https://github.com/WICG/resize-observer/blob/master/explainer.md#error-handling`);\n  }\n};\n/**\n * A shared ResizeObserver to be used for a particular box type (content-box, border-box, or\n * device-pixel-content-box)\n */\nclass SingleBoxSharedResizeObserver {\n  constructor(/** The box type to observe for resizes. */\n  _box) {\n    _defineProperty(this, \"_box\", void 0);\n    /** Stream that emits when the shared observer is destroyed. */\n    _defineProperty(this, \"_destroyed\", new Subject());\n    /** Stream of all events from the ResizeObserver. */\n    _defineProperty(this, \"_resizeSubject\", new Subject());\n    /** ResizeObserver used to observe element resize events. */\n    _defineProperty(this, \"_resizeObserver\", void 0);\n    /** A map of elements to streams of their resize events. */\n    _defineProperty(this, \"_elementObservables\", new Map());\n    this._box = _box;\n    if (typeof ResizeObserver !== 'undefined') {\n      this._resizeObserver = new ResizeObserver(entries => this._resizeSubject.next(entries));\n    }\n  }\n  /**\n   * Gets a stream of resize events for the given element.\n   * @param target The element to observe.\n   * @return The stream of resize events for the element.\n   */\n  observe(target) {\n    if (!this._elementObservables.has(target)) {\n      this._elementObservables.set(target, new Observable(observer => {\n        var _this$_resizeObserver;\n        const subscription = this._resizeSubject.subscribe(observer);\n        (_this$_resizeObserver = this._resizeObserver) === null || _this$_resizeObserver === void 0 || _this$_resizeObserver.observe(target, {\n          box: this._box\n        });\n        return () => {\n          var _this$_resizeObserver2;\n          (_this$_resizeObserver2 = this._resizeObserver) === null || _this$_resizeObserver2 === void 0 || _this$_resizeObserver2.unobserve(target);\n          subscription.unsubscribe();\n          this._elementObservables.delete(target);\n        };\n      }).pipe(filter(entries => entries.some(entry => entry.target === target)),\n      // Share a replay of the last event so that subsequent calls to observe the same element\n      // receive initial sizing info like the first one. Also enable ref counting so the\n      // element will be automatically unobserved when there are no more subscriptions.\n      shareReplay({\n        bufferSize: 1,\n        refCount: true\n      }), takeUntil(this._destroyed)));\n    }\n    return this._elementObservables.get(target);\n  }\n  /** Destroys this instance. */\n  destroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._resizeSubject.complete();\n    this._elementObservables.clear();\n  }\n}\n/**\n * Allows observing resize events on multiple elements using a shared set of ResizeObserver.\n * Sharing a ResizeObserver instance is recommended for better performance (see\n * https://github.com/WICG/resize-observer/issues/59).\n *\n * Rather than share a single `ResizeObserver`, this class creates one `ResizeObserver` per type\n * of observed box ('content-box', 'border-box', and 'device-pixel-content-box'). This avoids\n * later calls to `observe` with a different box type from influencing the events dispatched to\n * earlier calls.\n */\nclass SharedResizeObserver {\n  constructor() {\n    _defineProperty(this, \"_cleanupErrorListener\", void 0);\n    /** Map of box type to shared resize observer. */\n    _defineProperty(this, \"_observers\", new Map());\n    /** The Angular zone. */\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      this._ngZone.runOutsideAngular(() => {\n        const renderer = inject(RendererFactory2).createRenderer(null, null);\n        this._cleanupErrorListener = renderer.listen('window', 'error', loopLimitExceededErrorHandler);\n      });\n    }\n  }\n  ngOnDestroy() {\n    var _this$_cleanupErrorLi;\n    for (const [, observer] of this._observers) {\n      observer.destroy();\n    }\n    this._observers.clear();\n    (_this$_cleanupErrorLi = this._cleanupErrorListener) === null || _this$_cleanupErrorLi === void 0 || _this$_cleanupErrorLi.call(this);\n  }\n  /**\n   * Gets a stream of resize events for the given target element and box type.\n   * @param target The element to observe for resizes.\n   * @param options Options to pass to the `ResizeObserver`\n   * @return The stream of resize events for the element.\n   */\n  observe(target, options) {\n    const box = (options === null || options === void 0 ? void 0 : options.box) || 'content-box';\n    if (!this._observers.has(box)) {\n      this._observers.set(box, new SingleBoxSharedResizeObserver(box));\n    }\n    return this._observers.get(box).observe(target);\n  }\n}\n_SharedResizeObserver = SharedResizeObserver;\n_defineProperty(SharedResizeObserver, \"\\u0275fac\", function _SharedResizeObserver_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SharedResizeObserver)();\n});\n_defineProperty(SharedResizeObserver, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _SharedResizeObserver,\n  factory: _SharedResizeObserver.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedResizeObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { SharedResizeObserver };", "map": {"version": 3, "names": ["i0", "inject", "NgZone", "RendererFactory2", "Injectable", "Subject", "Observable", "filter", "shareReplay", "takeUntil", "loopLimitExceededErrorHandler", "e", "ErrorEvent", "message", "console", "error", "SingleBoxSharedResizeObserver", "constructor", "_box", "_defineProperty", "Map", "ResizeObserver", "_resizeObserver", "entries", "_resizeSubject", "next", "observe", "target", "_elementObservables", "has", "set", "observer", "_this$_resizeObserver", "subscription", "subscribe", "box", "_this$_resizeObserver2", "unobserve", "unsubscribe", "delete", "pipe", "some", "entry", "bufferSize", "refCount", "_destroyed", "get", "destroy", "complete", "clear", "SharedResizeObserver", "ngDevMode", "_ngZone", "runOutsideAngular", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "_cleanupErrorListener", "listen", "ngOnDestroy", "_this$_cleanupErrorLi", "_observers", "call", "options", "_SharedResizeObserver", "_SharedResizeObserver_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ɵsetClassMetadata", "type", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/observers/private.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Ng<PERSON><PERSON>, RendererFactory2, Injectable } from '@angular/core';\nimport { Subject, Observable } from 'rxjs';\nimport { filter, shareReplay, takeUntil } from 'rxjs/operators';\n\n/**\n * <PERSON><PERSON> that logs \"ResizeObserver loop limit exceeded\" errors.\n * These errors are not shown in the Chrome console, so we log them to ensure developers are aware.\n * @param e The error\n */\nconst loopLimitExceededErrorHandler = (e) => {\n    if (e instanceof ErrorEvent && e.message === 'ResizeObserver loop limit exceeded') {\n        console.error(`${e.message}. This could indicate a performance issue with your app. See https://github.com/WICG/resize-observer/blob/master/explainer.md#error-handling`);\n    }\n};\n/**\n * A shared ResizeObserver to be used for a particular box type (content-box, border-box, or\n * device-pixel-content-box)\n */\nclass SingleBoxSharedResizeObserver {\n    _box;\n    /** Stream that emits when the shared observer is destroyed. */\n    _destroyed = new Subject();\n    /** Stream of all events from the ResizeObserver. */\n    _resizeSubject = new Subject();\n    /** ResizeObserver used to observe element resize events. */\n    _resizeObserver;\n    /** A map of elements to streams of their resize events. */\n    _elementObservables = new Map();\n    constructor(\n    /** The box type to observe for resizes. */\n    _box) {\n        this._box = _box;\n        if (typeof ResizeObserver !== 'undefined') {\n            this._resizeObserver = new ResizeObserver(entries => this._resizeSubject.next(entries));\n        }\n    }\n    /**\n     * Gets a stream of resize events for the given element.\n     * @param target The element to observe.\n     * @return The stream of resize events for the element.\n     */\n    observe(target) {\n        if (!this._elementObservables.has(target)) {\n            this._elementObservables.set(target, new Observable(observer => {\n                const subscription = this._resizeSubject.subscribe(observer);\n                this._resizeObserver?.observe(target, { box: this._box });\n                return () => {\n                    this._resizeObserver?.unobserve(target);\n                    subscription.unsubscribe();\n                    this._elementObservables.delete(target);\n                };\n            }).pipe(filter(entries => entries.some(entry => entry.target === target)), \n            // Share a replay of the last event so that subsequent calls to observe the same element\n            // receive initial sizing info like the first one. Also enable ref counting so the\n            // element will be automatically unobserved when there are no more subscriptions.\n            shareReplay({ bufferSize: 1, refCount: true }), takeUntil(this._destroyed)));\n        }\n        return this._elementObservables.get(target);\n    }\n    /** Destroys this instance. */\n    destroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._resizeSubject.complete();\n        this._elementObservables.clear();\n    }\n}\n/**\n * Allows observing resize events on multiple elements using a shared set of ResizeObserver.\n * Sharing a ResizeObserver instance is recommended for better performance (see\n * https://github.com/WICG/resize-observer/issues/59).\n *\n * Rather than share a single `ResizeObserver`, this class creates one `ResizeObserver` per type\n * of observed box ('content-box', 'border-box', and 'device-pixel-content-box'). This avoids\n * later calls to `observe` with a different box type from influencing the events dispatched to\n * earlier calls.\n */\nclass SharedResizeObserver {\n    _cleanupErrorListener;\n    /** Map of box type to shared resize observer. */\n    _observers = new Map();\n    /** The Angular zone. */\n    _ngZone = inject(NgZone);\n    constructor() {\n        if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            this._ngZone.runOutsideAngular(() => {\n                const renderer = inject(RendererFactory2).createRenderer(null, null);\n                this._cleanupErrorListener = renderer.listen('window', 'error', loopLimitExceededErrorHandler);\n            });\n        }\n    }\n    ngOnDestroy() {\n        for (const [, observer] of this._observers) {\n            observer.destroy();\n        }\n        this._observers.clear();\n        this._cleanupErrorListener?.();\n    }\n    /**\n     * Gets a stream of resize events for the given target element and box type.\n     * @param target The element to observe for resizes.\n     * @param options Options to pass to the `ResizeObserver`\n     * @return The stream of resize events for the element.\n     */\n    observe(target, options) {\n        const box = options?.box || 'content-box';\n        if (!this._observers.has(box)) {\n            this._observers.set(box, new SingleBoxSharedResizeObserver(box));\n        }\n        return this._observers.get(box).observe(target);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: SharedResizeObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: SharedResizeObserver, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: SharedResizeObserver, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [] });\n\nexport { SharedResizeObserver };\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AAC5E,SAASC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC1C,SAASC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;;AAE/D;AACA;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAIC,CAAC,IAAK;EACzC,IAAIA,CAAC,YAAYC,UAAU,IAAID,CAAC,CAACE,OAAO,KAAK,oCAAoC,EAAE;IAC/EC,OAAO,CAACC,KAAK,CAAC,GAAGJ,CAAC,CAACE,OAAO,8IAA8I,CAAC;EAC7K;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMG,6BAA6B,CAAC;EAUhCC,WAAWA,CACX;EACAC,IAAI,EAAE;IAAAC,eAAA;IAVN;IAAAA,eAAA,qBACa,IAAId,OAAO,CAAC,CAAC;IAC1B;IAAAc,eAAA,yBACiB,IAAId,OAAO,CAAC,CAAC;IAC9B;IAAAc,eAAA;IAEA;IAAAA,eAAA,8BACsB,IAAIC,GAAG,CAAC,CAAC;IAI3B,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,OAAOG,cAAc,KAAK,WAAW,EAAE;MACvC,IAAI,CAACC,eAAe,GAAG,IAAID,cAAc,CAACE,OAAO,IAAI,IAAI,CAACC,cAAc,CAACC,IAAI,CAACF,OAAO,CAAC,CAAC;IAC3F;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIG,OAAOA,CAACC,MAAM,EAAE;IACZ,IAAI,CAAC,IAAI,CAACC,mBAAmB,CAACC,GAAG,CAACF,MAAM,CAAC,EAAE;MACvC,IAAI,CAACC,mBAAmB,CAACE,GAAG,CAACH,MAAM,EAAE,IAAIrB,UAAU,CAACyB,QAAQ,IAAI;QAAA,IAAAC,qBAAA;QAC5D,MAAMC,YAAY,GAAG,IAAI,CAACT,cAAc,CAACU,SAAS,CAACH,QAAQ,CAAC;QAC5D,CAAAC,qBAAA,OAAI,CAACV,eAAe,cAAAU,qBAAA,eAApBA,qBAAA,CAAsBN,OAAO,CAACC,MAAM,EAAE;UAAEQ,GAAG,EAAE,IAAI,CAACjB;QAAK,CAAC,CAAC;QACzD,OAAO,MAAM;UAAA,IAAAkB,sBAAA;UACT,CAAAA,sBAAA,OAAI,CAACd,eAAe,cAAAc,sBAAA,eAApBA,sBAAA,CAAsBC,SAAS,CAACV,MAAM,CAAC;UACvCM,YAAY,CAACK,WAAW,CAAC,CAAC;UAC1B,IAAI,CAACV,mBAAmB,CAACW,MAAM,CAACZ,MAAM,CAAC;QAC3C,CAAC;MACL,CAAC,CAAC,CAACa,IAAI,CAACjC,MAAM,CAACgB,OAAO,IAAIA,OAAO,CAACkB,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACf,MAAM,KAAKA,MAAM,CAAC,CAAC;MACzE;MACA;MACA;MACAnB,WAAW,CAAC;QAAEmC,UAAU,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC,EAAEnC,SAAS,CAAC,IAAI,CAACoC,UAAU,CAAC,CAAC,CAAC;IAChF;IACA,OAAO,IAAI,CAACjB,mBAAmB,CAACkB,GAAG,CAACnB,MAAM,CAAC;EAC/C;EACA;EACAoB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,UAAU,CAACpB,IAAI,CAAC,CAAC;IACtB,IAAI,CAACoB,UAAU,CAACG,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACxB,cAAc,CAACwB,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAACpB,mBAAmB,CAACqB,KAAK,CAAC,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EAMvBjC,WAAWA,CAAA,EAAG;IAAAE,eAAA;IAJd;IAAAA,eAAA,qBACa,IAAIC,GAAG,CAAC,CAAC;IACtB;IAAAD,eAAA,kBACUlB,MAAM,CAACC,MAAM,CAAC;IAEpB,IAAI,OAAOmB,cAAc,KAAK,WAAW,KAAK,OAAO8B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1F,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,MAAM;QACjC,MAAMC,QAAQ,GAAGrD,MAAM,CAACE,gBAAgB,CAAC,CAACoD,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;QACpE,IAAI,CAACC,qBAAqB,GAAGF,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE/C,6BAA6B,CAAC;MAClG,CAAC,CAAC;IACN;EACJ;EACAgD,WAAWA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACV,KAAK,MAAM,GAAG5B,QAAQ,CAAC,IAAI,IAAI,CAAC6B,UAAU,EAAE;MACxC7B,QAAQ,CAACgB,OAAO,CAAC,CAAC;IACtB;IACA,IAAI,CAACa,UAAU,CAACX,KAAK,CAAC,CAAC;IACvB,CAAAU,qBAAA,OAAI,CAACH,qBAAqB,cAAAG,qBAAA,eAA1BA,qBAAA,CAAAE,IAAA,KAA6B,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;EACInC,OAAOA,CAACC,MAAM,EAAEmC,OAAO,EAAE;IACrB,MAAM3B,GAAG,GAAG,CAAA2B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE3B,GAAG,KAAI,aAAa;IACzC,IAAI,CAAC,IAAI,CAACyB,UAAU,CAAC/B,GAAG,CAACM,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACyB,UAAU,CAAC9B,GAAG,CAACK,GAAG,EAAE,IAAInB,6BAA6B,CAACmB,GAAG,CAAC,CAAC;IACpE;IACA,OAAO,IAAI,CAACyB,UAAU,CAACd,GAAG,CAACX,GAAG,CAAC,CAACT,OAAO,CAACC,MAAM,CAAC;EACnD;AAGJ;AAACoC,qBAAA,GApCKb,oBAAoB;AAAA/B,eAAA,CAApB+B,oBAAoB,wBAAAc,8BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAkC6Ef,qBAAoB;AAAA;AAAA/B,eAAA,CAlCrH+B,oBAAoB,+BAqCuDlD,EAAE,CAAAkE,kBAAA;EAAAC,KAAA,EAFwBjB,qBAAoB;EAAAkB,OAAA,EAApBlB,qBAAoB,CAAAmB,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEnJ;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KAAiFnD,EAAE,CAAAuE,iBAAA,CAAQrB,oBAAoB,EAAc,CAAC;IAClHsB,IAAI,EAAEpE,UAAU;IAChBqE,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASpB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}