{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { TranslateModule } from '@ngx-translate/core';\nimport * as moment from 'moment';\nimport { NEVER } from 'rxjs';\nimport { CustomPeriodComponent } from './custom-period.component';\nimport { MODULES } from './custom-period.module';\nimport { SwuiTopFilterDataService } from '../../swui-schema-top-filter/top-filter-data.service';\ndescribe('CustomPeriodComponent', () => {\n  let component;\n  let fixture;\n  let testDateRange;\n  let testDate;\n  let customPeriod;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [TranslateModule.forRoot(), ...MODULES],\n      providers: [{\n        provide: SwuiTopFilterDataService,\n        useValue: {\n          onReset: NEVER\n        }\n      }],\n      declarations: [CustomPeriodComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(CustomPeriodComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    testDate = moment.utc().clone();\n    testDateRange = {\n      from: testDate.add(-1, 'd'),\n      to: testDate.add(1, 'd')\n    };\n    customPeriod = {\n      title: 'Test period',\n      fn: () => {\n        return testDateRange;\n      }\n    };\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should close menu', () => {\n    component.onClick(createFakeEvent('click'), customPeriod);\n    const menuTrigger = component.trigger;\n    expect(menuTrigger.menuOpen).toBe(false);\n  });\n  it('should set current period', () => {\n    component.onClick(createFakeEvent('click'), customPeriod);\n    expect(component.currentPeriod).toEqual(customPeriod);\n  });\n  it('should emit current period', () => {\n    spyOn(component.periodChange, 'emit');\n    component.onClick(createFakeEvent('click'), customPeriod);\n    expect(component.periodChange.emit).toHaveBeenCalled();\n  });\n  it('should reset current period', () => {\n    component.currentPeriod = customPeriod;\n    component.reset();\n    expect(component.currentPeriod).toBeUndefined();\n  });\n});\nfunction createFakeEvent(type) {\n  const event = document.createEvent('Event');\n  event.initEvent(type, true, true);\n  return event;\n}", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "TranslateModule", "moment", "NEVER", "CustomPeriodComponent", "MODULES", "SwuiTopFilterDataService", "describe", "component", "fixture", "testDateRange", "testDate", "customPeriod", "beforeEach", "configureTestingModule", "imports", "forRoot", "providers", "provide", "useValue", "onReset", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "utc", "clone", "from", "add", "to", "title", "fn", "it", "expect", "toBeTruthy", "onClick", "createFakeEvent", "menuTrigger", "trigger", "menuOpen", "toBe", "currentPeriod", "toEqual", "spyOn", "periodChange", "emit", "toHaveBeenCalled", "reset", "toBeUndefined", "type", "event", "document", "createEvent", "initEvent"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-range/custom-period/custom-period.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { TranslateModule } from '@ngx-translate/core';\nimport * as moment from 'moment';\nimport { NEVER } from 'rxjs';\n\nimport { CustomPeriodComponent } from './custom-period.component';\nimport { CustomPeriod } from './custom-period.interface';\nimport { MODULES } from './custom-period.module';\nimport { SwuiTopFilterDataService } from '../../swui-schema-top-filter/top-filter-data.service';\n\n\ndescribe('CustomPeriodComponent', () => {\n  let component: CustomPeriodComponent;\n  let fixture: ComponentFixture<CustomPeriodComponent>;\n  let testDateRange: any;\n  let testDate: moment.Moment;\n  let customPeriod: CustomPeriod;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [\n        TranslateModule.forRoot(),\n        ...MODULES,\n      ],\n      providers: [\n        {\n          provide: SwuiTopFilterDataService,\n          useValue: {\n            onReset: NEVER\n          }\n        }\n      ],\n      declarations: [CustomPeriodComponent],\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(CustomPeriodComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    testDate = moment.utc().clone();\n    testDateRange = { from: testDate.add(-1, 'd'), to: testDate.add(1, 'd') };\n    customPeriod = {\n      title: 'Test period',\n      fn: () => {\n        return testDateRange;\n      }\n    };\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should close menu', () => {\n    component.onClick(createFakeEvent('click'), customPeriod);\n    const menuTrigger = component.trigger as MatMenuTrigger;\n    expect(menuTrigger.menuOpen).toBe(false);\n  });\n\n  it('should set current period', () => {\n    component.onClick(createFakeEvent('click'), customPeriod);\n    expect(component.currentPeriod).toEqual(customPeriod);\n  });\n\n  it('should emit current period', () => {\n    spyOn(component.periodChange, 'emit');\n    component.onClick(createFakeEvent('click'), customPeriod);\n    expect(component.periodChange.emit).toHaveBeenCalled();\n  });\n\n  it('should reset current period', () => {\n    component.currentPeriod = customPeriod;\n    component.reset();\n    expect(component.currentPeriod).toBeUndefined();\n  });\n\n});\n\nfunction createFakeEvent( type: string ) {\n  const event = document.createEvent('Event');\n  event.initEvent(type, true, true);\n  return event;\n}\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,eAAe,QAAQ,qBAAqB;AACrD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,KAAK,QAAQ,MAAM;AAE5B,SAASC,qBAAqB,QAAQ,2BAA2B;AAEjE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,wBAAwB,QAAQ,sDAAsD;AAG/FC,QAAQ,CAAC,uBAAuB,EAAE,MAAK;EACrC,IAAIC,SAAgC;EACpC,IAAIC,OAAgD;EACpD,IAAIC,aAAkB;EACtB,IAAIC,QAAuB;EAC3B,IAAIC,YAA0B;EAE9BC,UAAU,CAACb,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACe,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPd,eAAe,CAACe,OAAO,EAAE,EACzB,GAAGX,OAAO,CACX;MACDY,SAAS,EAAE,CACT;QACEC,OAAO,EAAEZ,wBAAwB;QACjCa,QAAQ,EAAE;UACRC,OAAO,EAAEjB;;OAEZ,CACF;MACDkB,YAAY,EAAE,CAACjB,qBAAqB;KACrC,CAAC,CACCkB,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHT,UAAU,CAAC,MAAK;IACdJ,OAAO,GAAGV,OAAO,CAACwB,eAAe,CAACnB,qBAAqB,CAAC;IACxDI,SAAS,GAAGC,OAAO,CAACe,iBAAiB;IACrCf,OAAO,CAACgB,aAAa,EAAE;IACvBd,QAAQ,GAAGT,MAAM,CAACwB,GAAG,EAAE,CAACC,KAAK,EAAE;IAC/BjB,aAAa,GAAG;MAAEkB,IAAI,EAAEjB,QAAQ,CAACkB,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;MAAEC,EAAE,EAAEnB,QAAQ,CAACkB,GAAG,CAAC,CAAC,EAAE,GAAG;IAAC,CAAE;IACzEjB,YAAY,GAAG;MACbmB,KAAK,EAAE,aAAa;MACpBC,EAAE,EAAEA,CAAA,KAAK;QACP,OAAOtB,aAAa;MACtB;KACD;EACH,CAAC,CAAC;EAEFuB,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC1B,SAAS,CAAC,CAAC2B,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BzB,SAAS,CAAC4B,OAAO,CAACC,eAAe,CAAC,OAAO,CAAC,EAAEzB,YAAY,CAAC;IACzD,MAAM0B,WAAW,GAAG9B,SAAS,CAAC+B,OAAyB;IACvDL,MAAM,CAACI,WAAW,CAACE,QAAQ,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;EAC1C,CAAC,CAAC;EAEFR,EAAE,CAAC,2BAA2B,EAAE,MAAK;IACnCzB,SAAS,CAAC4B,OAAO,CAACC,eAAe,CAAC,OAAO,CAAC,EAAEzB,YAAY,CAAC;IACzDsB,MAAM,CAAC1B,SAAS,CAACkC,aAAa,CAAC,CAACC,OAAO,CAAC/B,YAAY,CAAC;EACvD,CAAC,CAAC;EAEFqB,EAAE,CAAC,4BAA4B,EAAE,MAAK;IACpCW,KAAK,CAACpC,SAAS,CAACqC,YAAY,EAAE,MAAM,CAAC;IACrCrC,SAAS,CAAC4B,OAAO,CAACC,eAAe,CAAC,OAAO,CAAC,EAAEzB,YAAY,CAAC;IACzDsB,MAAM,CAAC1B,SAAS,CAACqC,YAAY,CAACC,IAAI,CAAC,CAACC,gBAAgB,EAAE;EACxD,CAAC,CAAC;EAEFd,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrCzB,SAAS,CAACkC,aAAa,GAAG9B,YAAY;IACtCJ,SAAS,CAACwC,KAAK,EAAE;IACjBd,MAAM,CAAC1B,SAAS,CAACkC,aAAa,CAAC,CAACO,aAAa,EAAE;EACjD,CAAC,CAAC;AAEJ,CAAC,CAAC;AAEF,SAASZ,eAAeA,CAAEa,IAAY;EACpC,MAAMC,KAAK,GAAGC,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC;EAC3CF,KAAK,CAACG,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjC,OAAOC,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}