{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { signal, QueryList, isSignal, effect } from '@angular/core';\nimport { Subscription, Subject } from 'rxjs';\nimport { T as Typeahead } from './typeahead-9ZW4Dtsf.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { P as PAGE_DOWN, a as PAGE_UP, E as END, H as HOME, L as LEFT_ARROW, R as RIGHT_ARROW, U as UP_ARROW, D as DOWN_ARROW, T as TAB } from './keycodes-CpHkExLC.mjs';\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n  constructor(_items, injector) {\n    _defineProperty(this, \"_items\", void 0);\n    _defineProperty(this, \"_activeItemIndex\", -1);\n    _defineProperty(this, \"_activeItem\", signal(null));\n    _defineProperty(this, \"_wrap\", false);\n    _defineProperty(this, \"_typeaheadSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_itemChangesSubscription\", void 0);\n    _defineProperty(this, \"_vertical\", true);\n    _defineProperty(this, \"_horizontal\", void 0);\n    _defineProperty(this, \"_allowedModifierKeys\", []);\n    _defineProperty(this, \"_homeAndEnd\", false);\n    _defineProperty(this, \"_pageUpAndDown\", {\n      enabled: false,\n      delta: 10\n    });\n    _defineProperty(this, \"_effectRef\", void 0);\n    _defineProperty(this, \"_typeahead\", void 0);\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager. By default, disabled items are skipped.\n     */\n    _defineProperty(this, \"_skipPredicateFn\", item => item.disabled);\n    /**\n     * Stream that emits any time the TAB key is pressed, so components can react\n     * when focus is shifted off of the list.\n     */\n    _defineProperty(this, \"tabOut\", new Subject());\n    /** Stream that emits whenever the active item of the list manager changes. */\n    _defineProperty(this, \"change\", new Subject());\n    this._items = _items;\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe(newItems => this._itemsChanged(newItems.toArray()));\n    } else if (isSignal(_items)) {\n      if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('ListKeyManager constructed with a signal must receive an injector');\n      }\n      this._effectRef = effect(() => this._itemsChanged(_items()), {\n        injector\n      });\n    }\n  }\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval = 200) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const items = this._getItemsArray();\n      if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n        throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n      }\n    }\n    this._typeaheadSubscription.unsubscribe();\n    const items = this._getItemsArray();\n    this._typeahead = new Typeahead(items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.setActiveItem(item);\n    });\n    return this;\n  }\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead() {\n    var _this$_typeahead;\n    (_this$_typeahead = this._typeahead) === null || _this$_typeahead === void 0 || _this$_typeahead.reset();\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled = true, delta = 10) {\n    this._pageUpAndDown = {\n      enabled,\n      delta\n    };\n    return this;\n  }\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem();\n    this.updateActiveItem(item);\n    if (this._activeItem() !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event) {\n    var _this$_typeahead3;\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          var _this$_typeahead2;\n          (_this$_typeahead2 = this._typeahead) === null || _this$_typeahead2 === void 0 || _this$_typeahead2.handleKey(event);\n        }\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n    (_this$_typeahead3 = this._typeahead) === null || _this$_typeahead3 === void 0 || _this$_typeahead3.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  get activeItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The active item. */\n  get activeItem() {\n    return this._activeItem();\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return !!this._typeahead && this._typeahead.isTyping();\n  }\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive() {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive() {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n  updateActiveItem(item) {\n    var _this$_typeahead4;\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem.set(activeItem == null ? null : activeItem);\n    this._activeItemIndex = index;\n    (_this$_typeahead4 = this._typeahead) === null || _this$_typeahead4 === void 0 || _this$_typeahead4.setCurrentSelectedItemIndex(index);\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    var _this$_itemChangesSub, _this$_effectRef, _this$_typeahead5;\n    this._typeaheadSubscription.unsubscribe();\n    (_this$_itemChangesSub = this._itemChangesSubscription) === null || _this$_itemChangesSub === void 0 || _this$_itemChangesSub.unsubscribe();\n    (_this$_effectRef = this._effectRef) === null || _this$_effectRef === void 0 || _this$_effectRef.destroy();\n    (_this$_typeahead5 = this._typeahead) === null || _this$_typeahead5 === void 0 || _this$_typeahead5.destroy();\n    this.tabOut.complete();\n    this.change.complete();\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n    if (!items[index]) {\n      return;\n    }\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n      if (!items[index]) {\n        return;\n      }\n    }\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n  _getItemsArray() {\n    if (isSignal(this._items)) {\n      return this._items();\n    }\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n  /** Callback for when the items have changed. */\n  _itemsChanged(newItems) {\n    var _this$_typeahead6;\n    (_this$_typeahead6 = this._typeahead) === null || _this$_typeahead6 === void 0 || _this$_typeahead6.setItems(newItems);\n    const activeItem = this._activeItem();\n    if (activeItem) {\n      const newIndex = newItems.indexOf(activeItem);\n      if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n        var _this$_typeahead7;\n        this._activeItemIndex = newIndex;\n        (_this$_typeahead7 = this._typeahead) === null || _this$_typeahead7 === void 0 || _this$_typeahead7.setCurrentSelectedItemIndex(newIndex);\n      }\n    }\n  }\n}\nexport { ListKeyManager as L };\n//# sourceMappingURL=list-key-manager-CyOIXo8P.mjs.map", "map": {"version": 3, "names": ["signal", "QueryList", "isSignal", "effect", "Subscription", "Subject", "T", "Typeahead", "hasModifierKey", "P", "PAGE_DOWN", "a", "PAGE_UP", "E", "END", "H", "HOME", "L", "LEFT_ARROW", "R", "RIGHT_ARROW", "U", "UP_ARROW", "D", "DOWN_ARROW", "TAB", "ListKeyManager", "constructor", "_items", "injector", "_defineProperty", "EMPTY", "enabled", "delta", "item", "disabled", "_itemChangesSubscription", "changes", "subscribe", "newItems", "_itemsChanged", "toArray", "ngDevMode", "Error", "_effectRef", "skipPredicate", "predicate", "_skipPredicateFn", "withWrap", "shouldWrap", "_wrap", "withVerticalOrientation", "_vertical", "withHorizontalOrientation", "direction", "_horizontal", "withAllowedModifierKeys", "keys", "_allowedModifierKeys", "withTypeAhead", "debounceInterval", "items", "_getItemsArray", "length", "some", "get<PERSON><PERSON><PERSON>", "_typeaheadSubscription", "unsubscribe", "_typeahead", "undefined", "selectedItem", "setActiveItem", "cancelTypeahead", "_this$_typeahead", "reset", "withHomeAndEnd", "_homeAndEnd", "withPageUpDown", "_pageUpAndDown", "previousActiveItem", "_activeItem", "updateActiveItem", "change", "next", "_activeItemIndex", "onKeydown", "event", "_this$_typeahead3", "keyCode", "modifiers", "isModifierAllowed", "every", "modifier", "indexOf", "tabOut", "setNextItemActive", "setPreviousItemActive", "setFirstItemActive", "setLastItemActive", "targetIndex", "_setActiveItemByIndex", "itemsLength", "_this$_typeahead2", "handle<PERSON>ey", "preventDefault", "activeItemIndex", "activeItem", "isTyping", "_setActiveItemByDelta", "_this$_typeahead4", "itemArray", "index", "set", "setCurrentSelectedItemIndex", "destroy", "_this$_itemChangesSub", "_this$_effectRef", "_this$_typeahead5", "complete", "_setActiveInWrapMode", "_setActiveInDefaultMode", "i", "fallback<PERSON><PERSON><PERSON>", "_this$_typeahead6", "setItems", "newIndex", "_this$_typeahead7"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/list-key-manager-CyOIXo8P.mjs"], "sourcesContent": ["import { signal, QueryList, isSignal, effect } from '@angular/core';\nimport { Subscription, Subject } from 'rxjs';\nimport { T as Typeahead } from './typeahead-9ZW4Dtsf.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { P as PAGE_DOWN, a as PAGE_UP, E as END, H as HOME, L as LEFT_ARROW, R as RIGHT_ARROW, U as UP_ARROW, D as DOWN_ARROW, T as TAB } from './keycodes-CpHkExLC.mjs';\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n    _items;\n    _activeItemIndex = -1;\n    _activeItem = signal(null);\n    _wrap = false;\n    _typeaheadSubscription = Subscription.EMPTY;\n    _itemChangesSubscription;\n    _vertical = true;\n    _horizontal;\n    _allowedModifierKeys = [];\n    _homeAndEnd = false;\n    _pageUpAndDown = { enabled: false, delta: 10 };\n    _effectRef;\n    _typeahead;\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager. By default, disabled items are skipped.\n     */\n    _skipPredicateFn = (item) => item.disabled;\n    constructor(_items, injector) {\n        this._items = _items;\n        // We allow for the items to be an array because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (_items instanceof QueryList) {\n            this._itemChangesSubscription = _items.changes.subscribe((newItems) => this._itemsChanged(newItems.toArray()));\n        }\n        else if (isSignal(_items)) {\n            if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw new Error('ListKeyManager constructed with a signal must receive an injector');\n            }\n            this._effectRef = effect(() => this._itemsChanged(_items()), { injector });\n        }\n    }\n    /**\n     * Stream that emits any time the TAB key is pressed, so components can react\n     * when focus is shifted off of the list.\n     */\n    tabOut = new Subject();\n    /** Stream that emits whenever the active item of the list manager changes. */\n    change = new Subject();\n    /**\n     * Sets the predicate function that determines which items should be skipped by the\n     * list key manager.\n     * @param predicate Function that determines whether the given item should be skipped.\n     */\n    skipPredicate(predicate) {\n        this._skipPredicateFn = predicate;\n        return this;\n    }\n    /**\n     * Configures wrapping mode, which determines whether the active item will wrap to\n     * the other end of list when there are no more items in the given direction.\n     * @param shouldWrap Whether the list should wrap when reaching the end.\n     */\n    withWrap(shouldWrap = true) {\n        this._wrap = shouldWrap;\n        return this;\n    }\n    /**\n     * Configures whether the key manager should be able to move the selection vertically.\n     * @param enabled Whether vertical selection should be enabled.\n     */\n    withVerticalOrientation(enabled = true) {\n        this._vertical = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to move the selection horizontally.\n     * Passing in `null` will disable horizontal movement.\n     * @param direction Direction in which the selection can be moved.\n     */\n    withHorizontalOrientation(direction) {\n        this._horizontal = direction;\n        return this;\n    }\n    /**\n     * Modifier keys which are allowed to be held down and whose default actions will be prevented\n     * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n     */\n    withAllowedModifierKeys(keys) {\n        this._allowedModifierKeys = keys;\n        return this;\n    }\n    /**\n     * Turns on typeahead mode which allows users to set the active item by typing.\n     * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n     */\n    withTypeAhead(debounceInterval = 200) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const items = this._getItemsArray();\n            if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n                throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n            }\n        }\n        this._typeaheadSubscription.unsubscribe();\n        const items = this._getItemsArray();\n        this._typeahead = new Typeahead(items, {\n            debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n            skipPredicate: item => this._skipPredicateFn(item),\n        });\n        this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n            this.setActiveItem(item);\n        });\n        return this;\n    }\n    /** Cancels the current typeahead sequence. */\n    cancelTypeahead() {\n        this._typeahead?.reset();\n        return this;\n    }\n    /**\n     * Configures the key manager to activate the first and last items\n     * respectively when the Home or End key is pressed.\n     * @param enabled Whether pressing the Home or End key activates the first/last item.\n     */\n    withHomeAndEnd(enabled = true) {\n        this._homeAndEnd = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n     * respectively when the Page-Up or Page-Down key is pressed.\n     * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n     * @param delta Whether pressing the Home or End key activates the first/last item.\n     */\n    withPageUpDown(enabled = true, delta = 10) {\n        this._pageUpAndDown = { enabled, delta };\n        return this;\n    }\n    setActiveItem(item) {\n        const previousActiveItem = this._activeItem();\n        this.updateActiveItem(item);\n        if (this._activeItem() !== previousActiveItem) {\n            this.change.next(this._activeItemIndex);\n        }\n    }\n    /**\n     * Sets the active item depending on the key event passed in.\n     * @param event Keyboard event to be used for determining which element should be active.\n     */\n    onKeydown(event) {\n        const keyCode = event.keyCode;\n        const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n        const isModifierAllowed = modifiers.every(modifier => {\n            return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n        });\n        switch (keyCode) {\n            case TAB:\n                this.tabOut.next();\n                return;\n            case DOWN_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case UP_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case RIGHT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case LEFT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case HOME:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setFirstItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case END:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setLastItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_UP:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n                    this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_DOWN:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n                    const itemsLength = this._getItemsArray().length;\n                    this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            default:\n                if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n                    this._typeahead?.handleKey(event);\n                }\n                // Note that we return here, in order to avoid preventing\n                // the default action of non-navigational keys.\n                return;\n        }\n        this._typeahead?.reset();\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    get activeItemIndex() {\n        return this._activeItemIndex;\n    }\n    /** The active item. */\n    get activeItem() {\n        return this._activeItem();\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return !!this._typeahead && this._typeahead.isTyping();\n    }\n    /** Sets the active item to the first enabled item in the list. */\n    setFirstItemActive() {\n        this._setActiveItemByIndex(0, 1);\n    }\n    /** Sets the active item to the last enabled item in the list. */\n    setLastItemActive() {\n        this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n    }\n    /** Sets the active item to the next enabled item in the list. */\n    setNextItemActive() {\n        this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n    }\n    /** Sets the active item to a previous enabled item in the list. */\n    setPreviousItemActive() {\n        this._activeItemIndex < 0 && this._wrap\n            ? this.setLastItemActive()\n            : this._setActiveItemByDelta(-1);\n    }\n    updateActiveItem(item) {\n        const itemArray = this._getItemsArray();\n        const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n        const activeItem = itemArray[index];\n        // Explicitly check for `null` and `undefined` because other falsy values are valid.\n        this._activeItem.set(activeItem == null ? null : activeItem);\n        this._activeItemIndex = index;\n        this._typeahead?.setCurrentSelectedItemIndex(index);\n    }\n    /** Cleans up the key manager. */\n    destroy() {\n        this._typeaheadSubscription.unsubscribe();\n        this._itemChangesSubscription?.unsubscribe();\n        this._effectRef?.destroy();\n        this._typeahead?.destroy();\n        this.tabOut.complete();\n        this.change.complete();\n    }\n    /**\n     * This method sets the active item, given a list of items and the delta between the\n     * currently active item and the new active item. It will calculate differently\n     * depending on whether wrap mode is turned on.\n     */\n    _setActiveItemByDelta(delta) {\n        this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n    }\n    /**\n     * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n     * down the list until it finds an item that is not disabled, and it will wrap if it\n     * encounters either end of the list.\n     */\n    _setActiveInWrapMode(delta) {\n        const items = this._getItemsArray();\n        for (let i = 1; i <= items.length; i++) {\n            const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n            const item = items[index];\n            if (!this._skipPredicateFn(item)) {\n                this.setActiveItem(index);\n                return;\n            }\n        }\n    }\n    /**\n     * Sets the active item properly given the default mode. In other words, it will\n     * continue to move down the list until it finds an item that is not disabled. If\n     * it encounters either end of the list, it will stop and not wrap.\n     */\n    _setActiveInDefaultMode(delta) {\n        this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n    }\n    /**\n     * Sets the active item to the first enabled item starting at the index specified. If the\n     * item is disabled, it will move in the fallbackDelta direction until it either\n     * finds an enabled item or encounters the end of the list.\n     */\n    _setActiveItemByIndex(index, fallbackDelta) {\n        const items = this._getItemsArray();\n        if (!items[index]) {\n            return;\n        }\n        while (this._skipPredicateFn(items[index])) {\n            index += fallbackDelta;\n            if (!items[index]) {\n                return;\n            }\n        }\n        this.setActiveItem(index);\n    }\n    /** Returns the items as an array. */\n    _getItemsArray() {\n        if (isSignal(this._items)) {\n            return this._items();\n        }\n        return this._items instanceof QueryList ? this._items.toArray() : this._items;\n    }\n    /** Callback for when the items have changed. */\n    _itemsChanged(newItems) {\n        this._typeahead?.setItems(newItems);\n        const activeItem = this._activeItem();\n        if (activeItem) {\n            const newIndex = newItems.indexOf(activeItem);\n            if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n                this._activeItemIndex = newIndex;\n                this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n            }\n        }\n    }\n}\n\nexport { ListKeyManager as L };\n//# sourceMappingURL=list-key-manager-CyOIXo8P.mjs.map\n"], "mappings": ";AAAA,SAASA,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AACnE,SAASC,YAAY,EAAEC,OAAO,QAAQ,MAAM;AAC5C,SAASC,CAAC,IAAIC,SAAS,QAAQ,0BAA0B;AACzD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,UAAU,EAAElB,CAAC,IAAImB,GAAG,QAAQ,yBAAyB;;AAExK;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EAmBjBC,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAE;IAAAC,eAAA;IAAAA,eAAA,2BAjBX,CAAC,CAAC;IAAAA,eAAA,sBACP9B,MAAM,CAAC,IAAI,CAAC;IAAA8B,eAAA,gBAClB,KAAK;IAAAA,eAAA,iCACY1B,YAAY,CAAC2B,KAAK;IAAAD,eAAA;IAAAA,eAAA,oBAE/B,IAAI;IAAAA,eAAA;IAAAA,eAAA,+BAEO,EAAE;IAAAA,eAAA,sBACX,KAAK;IAAAA,eAAA,yBACF;MAAEE,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAG,CAAC;IAAAH,eAAA;IAAAA,eAAA;IAG9C;AACJ;AACA;AACA;IAHIA,eAAA,2BAIoBI,IAAI,IAAKA,IAAI,CAACC,QAAQ;IAgB1C;AACJ;AACA;AACA;IAHIL,eAAA,iBAIS,IAAIzB,OAAO,CAAC,CAAC;IACtB;IAAAyB,eAAA,iBACS,IAAIzB,OAAO,CAAC,CAAC;IApBlB,IAAI,CAACuB,MAAM,GAAGA,MAAM;IACpB;IACA;IACA;IACA,IAAIA,MAAM,YAAY3B,SAAS,EAAE;MAC7B,IAAI,CAACmC,wBAAwB,GAAGR,MAAM,CAACS,OAAO,CAACC,SAAS,CAAEC,QAAQ,IAAK,IAAI,CAACC,aAAa,CAACD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;IAClH,CAAC,MACI,IAAIvC,QAAQ,CAAC0B,MAAM,CAAC,EAAE;MACvB,IAAI,CAACC,QAAQ,KAAK,OAAOa,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC9D,MAAM,IAAIC,KAAK,CAAC,mEAAmE,CAAC;MACxF;MACA,IAAI,CAACC,UAAU,GAAGzC,MAAM,CAAC,MAAM,IAAI,CAACqC,aAAa,CAACZ,MAAM,CAAC,CAAC,CAAC,EAAE;QAAEC;MAAS,CAAC,CAAC;IAC9E;EACJ;EAQA;AACJ;AACA;AACA;AACA;EACIgB,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAACC,gBAAgB,GAAGD,SAAS;IACjC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIE,QAAQA,CAACC,UAAU,GAAG,IAAI,EAAE;IACxB,IAAI,CAACC,KAAK,GAAGD,UAAU;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,uBAAuBA,CAACnB,OAAO,GAAG,IAAI,EAAE;IACpC,IAAI,CAACoB,SAAS,GAAGpB,OAAO;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIqB,yBAAyBA,CAACC,SAAS,EAAE;IACjC,IAAI,CAACC,WAAW,GAAGD,SAAS;IAC5B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,uBAAuBA,CAACC,IAAI,EAAE;IAC1B,IAAI,CAACC,oBAAoB,GAAGD,IAAI;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,aAAaA,CAACC,gBAAgB,GAAG,GAAG,EAAE;IAClC,IAAI,OAAOlB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMmB,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,IAAIF,KAAK,CAACG,IAAI,CAAC9B,IAAI,IAAI,OAAOA,IAAI,CAAC+B,QAAQ,KAAK,UAAU,CAAC,EAAE;QAC7E,MAAMtB,KAAK,CAAC,8EAA8E,CAAC;MAC/F;IACJ;IACA,IAAI,CAACuB,sBAAsB,CAACC,WAAW,CAAC,CAAC;IACzC,MAAMN,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,IAAI,CAACM,UAAU,GAAG,IAAI7D,SAAS,CAACsD,KAAK,EAAE;MACnCD,gBAAgB,EAAE,OAAOA,gBAAgB,KAAK,QAAQ,GAAGA,gBAAgB,GAAGS,SAAS;MACrFxB,aAAa,EAAEX,IAAI,IAAI,IAAI,CAACa,gBAAgB,CAACb,IAAI;IACrD,CAAC,CAAC;IACF,IAAI,CAACgC,sBAAsB,GAAG,IAAI,CAACE,UAAU,CAACE,YAAY,CAAChC,SAAS,CAACJ,IAAI,IAAI;MACzE,IAAI,CAACqC,aAAa,CAACrC,IAAI,CAAC;IAC5B,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACA;EACAsC,eAAeA,CAAA,EAAG;IAAA,IAAAC,gBAAA;IACd,CAAAA,gBAAA,OAAI,CAACL,UAAU,cAAAK,gBAAA,eAAfA,gBAAA,CAAiBC,KAAK,CAAC,CAAC;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,cAAcA,CAAC3C,OAAO,GAAG,IAAI,EAAE;IAC3B,IAAI,CAAC4C,WAAW,GAAG5C,OAAO;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6C,cAAcA,CAAC7C,OAAO,GAAG,IAAI,EAAEC,KAAK,GAAG,EAAE,EAAE;IACvC,IAAI,CAAC6C,cAAc,GAAG;MAAE9C,OAAO;MAAEC;IAAM,CAAC;IACxC,OAAO,IAAI;EACf;EACAsC,aAAaA,CAACrC,IAAI,EAAE;IAChB,MAAM6C,kBAAkB,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IAC7C,IAAI,CAACC,gBAAgB,CAAC/C,IAAI,CAAC;IAC3B,IAAI,IAAI,CAAC8C,WAAW,CAAC,CAAC,KAAKD,kBAAkB,EAAE;MAC3C,IAAI,CAACG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAAC;IAC3C;EACJ;EACA;AACJ;AACA;AACA;EACIC,SAASA,CAACC,KAAK,EAAE;IAAA,IAAAC,iBAAA;IACb,MAAMC,OAAO,GAAGF,KAAK,CAACE,OAAO;IAC7B,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;IAC9D,MAAMC,iBAAiB,GAAGD,SAAS,CAACE,KAAK,CAACC,QAAQ,IAAI;MAClD,OAAO,CAACN,KAAK,CAACM,QAAQ,CAAC,IAAI,IAAI,CAAClC,oBAAoB,CAACmC,OAAO,CAACD,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC/E,CAAC,CAAC;IACF,QAAQJ,OAAO;MACX,KAAK/D,GAAG;QACJ,IAAI,CAACqE,MAAM,CAACX,IAAI,CAAC,CAAC;QAClB;MACJ,KAAK3D,UAAU;QACX,IAAI,IAAI,CAAC4B,SAAS,IAAIsC,iBAAiB,EAAE;UACrC,IAAI,CAACK,iBAAiB,CAAC,CAAC;UACxB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKzE,QAAQ;QACT,IAAI,IAAI,CAAC8B,SAAS,IAAIsC,iBAAiB,EAAE;UACrC,IAAI,CAACM,qBAAqB,CAAC,CAAC;UAC5B;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK5E,WAAW;QACZ,IAAI,IAAI,CAACmC,WAAW,IAAImC,iBAAiB,EAAE;UACvC,IAAI,CAACnC,WAAW,KAAK,KAAK,GAAG,IAAI,CAACyC,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAACD,iBAAiB,CAAC,CAAC;UACpF;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK7E,UAAU;QACX,IAAI,IAAI,CAACqC,WAAW,IAAImC,iBAAiB,EAAE;UACvC,IAAI,CAACnC,WAAW,KAAK,KAAK,GAAG,IAAI,CAACwC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;UACpF;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKhF,IAAI;QACL,IAAI,IAAI,CAAC4D,WAAW,IAAIc,iBAAiB,EAAE;UACvC,IAAI,CAACO,kBAAkB,CAAC,CAAC;UACzB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKnF,GAAG;QACJ,IAAI,IAAI,CAAC8D,WAAW,IAAIc,iBAAiB,EAAE;UACvC,IAAI,CAACQ,iBAAiB,CAAC,CAAC;UACxB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKtF,OAAO;QACR,IAAI,IAAI,CAACkE,cAAc,CAAC9C,OAAO,IAAI0D,iBAAiB,EAAE;UAClD,MAAMS,WAAW,GAAG,IAAI,CAACf,gBAAgB,GAAG,IAAI,CAACN,cAAc,CAAC7C,KAAK;UACrE,IAAI,CAACmE,qBAAqB,CAACD,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;UAChE;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKzF,SAAS;QACV,IAAI,IAAI,CAACoE,cAAc,CAAC9C,OAAO,IAAI0D,iBAAiB,EAAE;UAClD,MAAMS,WAAW,GAAG,IAAI,CAACf,gBAAgB,GAAG,IAAI,CAACN,cAAc,CAAC7C,KAAK;UACrE,MAAMoE,WAAW,GAAG,IAAI,CAACvC,cAAc,CAAC,CAAC,CAACC,MAAM;UAChD,IAAI,CAACqC,qBAAqB,CAACD,WAAW,GAAGE,WAAW,GAAGF,WAAW,GAAGE,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;UACzF;QACJ,CAAC,MACI;UACD;QACJ;MACJ;QACI,IAAIX,iBAAiB,IAAIlF,cAAc,CAAC8E,KAAK,EAAE,UAAU,CAAC,EAAE;UAAA,IAAAgB,iBAAA;UACxD,CAAAA,iBAAA,OAAI,CAAClC,UAAU,cAAAkC,iBAAA,eAAfA,iBAAA,CAAiBC,SAAS,CAACjB,KAAK,CAAC;QACrC;QACA;QACA;QACA;IACR;IACA,CAAAC,iBAAA,OAAI,CAACnB,UAAU,cAAAmB,iBAAA,eAAfA,iBAAA,CAAiBb,KAAK,CAAC,CAAC;IACxBY,KAAK,CAACkB,cAAc,CAAC,CAAC;EAC1B;EACA;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACrB,gBAAgB;EAChC;EACA;EACA,IAAIsB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC1B,WAAW,CAAC,CAAC;EAC7B;EACA;EACA2B,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,CAAC,IAAI,CAACvC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACuC,QAAQ,CAAC,CAAC;EAC1D;EACA;EACAV,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACG,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;EACpC;EACA;EACAF,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAACtC,cAAc,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EACpE;EACA;EACAgC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACX,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACa,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACW,qBAAqB,CAAC,CAAC,CAAC;EACzF;EACA;EACAZ,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACZ,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAAClC,KAAK,GACjC,IAAI,CAACgD,iBAAiB,CAAC,CAAC,GACxB,IAAI,CAACU,qBAAqB,CAAC,CAAC,CAAC,CAAC;EACxC;EACA3B,gBAAgBA,CAAC/C,IAAI,EAAE;IAAA,IAAA2E,iBAAA;IACnB,MAAMC,SAAS,GAAG,IAAI,CAAChD,cAAc,CAAC,CAAC;IACvC,MAAMiD,KAAK,GAAG,OAAO7E,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAG4E,SAAS,CAACjB,OAAO,CAAC3D,IAAI,CAAC;IACvE,MAAMwE,UAAU,GAAGI,SAAS,CAACC,KAAK,CAAC;IACnC;IACA,IAAI,CAAC/B,WAAW,CAACgC,GAAG,CAACN,UAAU,IAAI,IAAI,GAAG,IAAI,GAAGA,UAAU,CAAC;IAC5D,IAAI,CAACtB,gBAAgB,GAAG2B,KAAK;IAC7B,CAAAF,iBAAA,OAAI,CAACzC,UAAU,cAAAyC,iBAAA,eAAfA,iBAAA,CAAiBI,2BAA2B,CAACF,KAAK,CAAC;EACvD;EACA;EACAG,OAAOA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA;IACN,IAAI,CAACnD,sBAAsB,CAACC,WAAW,CAAC,CAAC;IACzC,CAAAgD,qBAAA,OAAI,CAAC/E,wBAAwB,cAAA+E,qBAAA,eAA7BA,qBAAA,CAA+BhD,WAAW,CAAC,CAAC;IAC5C,CAAAiD,gBAAA,OAAI,CAACxE,UAAU,cAAAwE,gBAAA,eAAfA,gBAAA,CAAiBF,OAAO,CAAC,CAAC;IAC1B,CAAAG,iBAAA,OAAI,CAACjD,UAAU,cAAAiD,iBAAA,eAAfA,iBAAA,CAAiBH,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACpB,MAAM,CAACwB,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACpC,MAAM,CAACoC,QAAQ,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIV,qBAAqBA,CAAC3E,KAAK,EAAE;IACzB,IAAI,CAACiB,KAAK,GAAG,IAAI,CAACqE,oBAAoB,CAACtF,KAAK,CAAC,GAAG,IAAI,CAACuF,uBAAuB,CAACvF,KAAK,CAAC;EACvF;EACA;AACJ;AACA;AACA;AACA;EACIsF,oBAAoBA,CAACtF,KAAK,EAAE;IACxB,MAAM4B,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,KAAK,IAAI2D,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI5D,KAAK,CAACE,MAAM,EAAE0D,CAAC,EAAE,EAAE;MACpC,MAAMV,KAAK,GAAG,CAAC,IAAI,CAAC3B,gBAAgB,GAAGnD,KAAK,GAAGwF,CAAC,GAAG5D,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACE,MAAM;MAC/E,MAAM7B,IAAI,GAAG2B,KAAK,CAACkD,KAAK,CAAC;MACzB,IAAI,CAAC,IAAI,CAAChE,gBAAgB,CAACb,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACqC,aAAa,CAACwC,KAAK,CAAC;QACzB;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIS,uBAAuBA,CAACvF,KAAK,EAAE;IAC3B,IAAI,CAACmE,qBAAqB,CAAC,IAAI,CAAChB,gBAAgB,GAAGnD,KAAK,EAAEA,KAAK,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;EACImE,qBAAqBA,CAACW,KAAK,EAAEW,aAAa,EAAE;IACxC,MAAM7D,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,IAAI,CAACD,KAAK,CAACkD,KAAK,CAAC,EAAE;MACf;IACJ;IACA,OAAO,IAAI,CAAChE,gBAAgB,CAACc,KAAK,CAACkD,KAAK,CAAC,CAAC,EAAE;MACxCA,KAAK,IAAIW,aAAa;MACtB,IAAI,CAAC7D,KAAK,CAACkD,KAAK,CAAC,EAAE;QACf;MACJ;IACJ;IACA,IAAI,CAACxC,aAAa,CAACwC,KAAK,CAAC;EAC7B;EACA;EACAjD,cAAcA,CAAA,EAAG;IACb,IAAI5D,QAAQ,CAAC,IAAI,CAAC0B,MAAM,CAAC,EAAE;MACvB,OAAO,IAAI,CAACA,MAAM,CAAC,CAAC;IACxB;IACA,OAAO,IAAI,CAACA,MAAM,YAAY3B,SAAS,GAAG,IAAI,CAAC2B,MAAM,CAACa,OAAO,CAAC,CAAC,GAAG,IAAI,CAACb,MAAM;EACjF;EACA;EACAY,aAAaA,CAACD,QAAQ,EAAE;IAAA,IAAAoF,iBAAA;IACpB,CAAAA,iBAAA,OAAI,CAACvD,UAAU,cAAAuD,iBAAA,eAAfA,iBAAA,CAAiBC,QAAQ,CAACrF,QAAQ,CAAC;IACnC,MAAMmE,UAAU,GAAG,IAAI,CAAC1B,WAAW,CAAC,CAAC;IACrC,IAAI0B,UAAU,EAAE;MACZ,MAAMmB,QAAQ,GAAGtF,QAAQ,CAACsD,OAAO,CAACa,UAAU,CAAC;MAC7C,IAAImB,QAAQ,GAAG,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,CAACzC,gBAAgB,EAAE;QAAA,IAAA0C,iBAAA;QACrD,IAAI,CAAC1C,gBAAgB,GAAGyC,QAAQ;QAChC,CAAAC,iBAAA,OAAI,CAAC1D,UAAU,cAAA0D,iBAAA,eAAfA,iBAAA,CAAiBb,2BAA2B,CAACY,QAAQ,CAAC;MAC1D;IACJ;EACJ;AACJ;AAEA,SAASnG,cAAc,IAAIT,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}