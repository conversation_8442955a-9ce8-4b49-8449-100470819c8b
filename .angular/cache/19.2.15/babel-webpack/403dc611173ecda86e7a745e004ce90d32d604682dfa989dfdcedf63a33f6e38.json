{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiCalendarComponent } from './swui-calendar.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nexport const CALENDAR_MODULES = [MatButtonModule, MatIconModule];\nlet SwuiCalendarModule = class SwuiCalendarModule {};\nSwuiCalendarModule = __decorate([NgModule({\n  declarations: [SwuiCalendarComponent],\n  imports: [CommonModule, ...CALENDAR_MODULES],\n  exports: [SwuiCalendarComponent]\n})], SwuiCalendarModule);\nexport { SwuiCalendarModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "SwuiCalendarComponent", "MatIconModule", "MatButtonModule", "CALENDAR_MODULES", "SwuiCalendarModule", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-calendar/swui-calendar.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiCalendarComponent } from './swui-calendar.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nexport const CALENDAR_MODULES = [\n    MatButtonModule,\n    MatIconModule,\n];\nlet SwuiCalendarModule = class SwuiCalendarModule {\n};\nSwuiCalendarModule = __decorate([\n    NgModule({\n        declarations: [SwuiCalendarComponent],\n        imports: [\n            CommonModule,\n            ...CALENDAR_MODULES,\n        ],\n        exports: [\n            SwuiCalendarComponent,\n        ]\n    })\n], SwuiCalendarModule);\nexport { SwuiCalendarModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,gBAAgB,GAAG,CAC5BD,eAAe,EACfD,aAAa,CAChB;AACD,IAAIG,kBAAkB,GAAG,MAAMA,kBAAkB,CAAC,EACjD;AACDA,kBAAkB,GAAGP,UAAU,CAAC,CAC5BC,QAAQ,CAAC;EACLO,YAAY,EAAE,CAACL,qBAAqB,CAAC;EACrCM,OAAO,EAAE,CACLP,YAAY,EACZ,GAAGI,gBAAgB,CACtB;EACDI,OAAO,EAAE,CACLP,qBAAqB;AAE7B,CAAC,CAAC,CACL,EAAEI,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}