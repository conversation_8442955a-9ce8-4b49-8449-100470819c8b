{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _DOMTestComponentRenderer, _BrowserTestingModule;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵgetDOM as _getDOM, DOCUMENT, PlatformLocation } from '@angular/common';\nimport { MockPlatformLocation } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { Inject, Injectable, createPlatformFactory, APP_ID, ɵinternalProvideZoneChangeDetection as _internalProvideZoneChangeDetection, ɵChangeDetectionSchedulerImpl as _ChangeDetectionSchedulerImpl, ɵChangeDetectionScheduler as _ChangeDetectionScheduler, NgModule } from '@angular/core';\nimport { TestComponentRenderer } from '@angular/core/testing';\nimport { platformBrowser, BrowserModule } from './browser-D-u-fknz.mjs';\nimport './dom_renderer-DGKzginR.mjs';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\nclass DOMTestComponentRenderer extends TestComponentRenderer {\n  constructor(_doc) {\n    super();\n    _defineProperty(this, \"_doc\", void 0);\n    this._doc = _doc;\n  }\n  insertRootElement(rootElId) {\n    this.removeAllRootElementsImpl();\n    const rootElement = _getDOM().getDefaultDocument().createElement('div');\n    rootElement.setAttribute('id', rootElId);\n    this._doc.body.appendChild(rootElement);\n  }\n  removeAllRootElements() {\n    // Check whether the `DOCUMENT` instance retrieved from DI contains\n    // the necessary function to complete the cleanup. In tests that don't\n    // interact with DOM, the `DOCUMENT` might be mocked and some functions\n    // might be missing. For such tests, DOM cleanup is not required and\n    // we skip the logic if there are missing functions.\n    if (typeof this._doc.querySelectorAll === 'function') {\n      this.removeAllRootElementsImpl();\n    }\n  }\n  removeAllRootElementsImpl() {\n    const oldRoots = this._doc.querySelectorAll('[id^=root]');\n    for (let i = 0; i < oldRoots.length; i++) {\n      _getDOM().remove(oldRoots[i]);\n    }\n  }\n}\n_DOMTestComponentRenderer = DOMTestComponentRenderer;\n_defineProperty(DOMTestComponentRenderer, \"\\u0275fac\", function _DOMTestComponentRenderer_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _DOMTestComponentRenderer)(i0.ɵɵinject(DOCUMENT));\n});\n_defineProperty(DOMTestComponentRenderer, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _DOMTestComponentRenderer,\n  factory: _DOMTestComponentRenderer.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DOMTestComponentRenderer, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Platform for testing\n *\n * @publicApi\n */\nconst platformBrowserTesting = createPlatformFactory(platformBrowser, 'browserTesting');\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserTestingModule {}\n_BrowserTestingModule = BrowserTestingModule;\n_defineProperty(BrowserTestingModule, \"\\u0275fac\", function _BrowserTestingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BrowserTestingModule)();\n});\n_defineProperty(BrowserTestingModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _BrowserTestingModule,\n  exports: [BrowserModule]\n}));\n_defineProperty(BrowserTestingModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: APP_ID,\n    useValue: 'a'\n  }, _internalProvideZoneChangeDetection({}), {\n    provide: _ChangeDetectionScheduler,\n    useExisting: _ChangeDetectionSchedulerImpl\n  }, {\n    provide: PlatformLocation,\n    useClass: MockPlatformLocation\n  }, {\n    provide: TestComponentRenderer,\n    useClass: DOMTestComponentRenderer\n  }],\n  imports: [BrowserModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: [{\n        provide: APP_ID,\n        useValue: 'a'\n      }, _internalProvideZoneChangeDetection({}), {\n        provide: _ChangeDetectionScheduler,\n        useExisting: _ChangeDetectionSchedulerImpl\n      }, {\n        provide: PlatformLocation,\n        useClass: MockPlatformLocation\n      }, {\n        provide: TestComponentRenderer,\n        useClass: DOMTestComponentRenderer\n      }]\n    }]\n  }], null, null);\n})();\nexport { BrowserTestingModule, platformBrowserTesting };", "map": {"version": 3, "names": ["ɵgetDOM", "_getDOM", "DOCUMENT", "PlatformLocation", "MockPlatformLocation", "i0", "Inject", "Injectable", "createPlatformFactory", "APP_ID", "ɵinternalProvideZoneChangeDetection", "_internalProvideZoneChangeDetection", "ɵChangeDetectionSchedulerImpl", "_ChangeDetectionSchedulerImpl", "ɵChangeDetectionScheduler", "_ChangeDetectionScheduler", "NgModule", "TestComponent<PERSON><PERSON><PERSON>", "platformBrowser", "BrowserModule", "DOMTestComponentRenderer", "constructor", "_doc", "_defineProperty", "insertRootElement", "rootElId", "removeAllRootElementsImpl", "rootElement", "getDefaultDocument", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "removeAllRootElements", "querySelectorAll", "oldRoots", "i", "length", "remove", "_DOMTestComponent<PERSON><PERSON>er", "_DOMTestComponentRenderer_Factory", "__ngFactoryType__", "ɵɵinject", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "ngDevMode", "ɵsetClassMetadata", "type", "undefined", "decorators", "args", "platformBrowserTesting", "BrowserTestingModule", "_BrowserTestingModule", "_BrowserTestingModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "providers", "provide", "useValue", "useExisting", "useClass", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/platform-browser/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵgetDOM as _getDOM, DOCUMENT, PlatformLocation } from '@angular/common';\nimport { MockPlatformLocation } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { Inject, Injectable, createPlatformFactory, APP_ID, ɵinternalProvideZoneChangeDetection as _internalProvideZoneChangeDetection, ɵChangeDetectionSchedulerImpl as _ChangeDetectionSchedulerImpl, ɵChangeDetectionScheduler as _ChangeDetectionScheduler, NgModule } from '@angular/core';\nimport { TestComponentRenderer } from '@angular/core/testing';\nimport { platformBrowser, BrowserModule } from './browser-D-u-fknz.mjs';\nimport './dom_renderer-DGKzginR.mjs';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\nclass DOMTestComponentRenderer extends TestComponentRenderer {\n    _doc;\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    insertRootElement(rootElId) {\n        this.removeAllRootElementsImpl();\n        const rootElement = _getDOM().getDefaultDocument().createElement('div');\n        rootElement.setAttribute('id', rootElId);\n        this._doc.body.appendChild(rootElement);\n    }\n    removeAllRootElements() {\n        // Check whether the `DOCUMENT` instance retrieved from DI contains\n        // the necessary function to complete the cleanup. In tests that don't\n        // interact with DOM, the `DOCUMENT` might be mocked and some functions\n        // might be missing. For such tests, DOM cleanup is not required and\n        // we skip the logic if there are missing functions.\n        if (typeof this._doc.querySelectorAll === 'function') {\n            this.removeAllRootElementsImpl();\n        }\n    }\n    removeAllRootElementsImpl() {\n        const oldRoots = this._doc.querySelectorAll('[id^=root]');\n        for (let i = 0; i < oldRoots.length; i++) {\n            _getDOM().remove(oldRoots[i]);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DOMTestComponentRenderer, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DOMTestComponentRenderer });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DOMTestComponentRenderer, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Platform for testing\n *\n * @publicApi\n */\nconst platformBrowserTesting = createPlatformFactory(platformBrowser, 'browserTesting');\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserTestingModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserTestingModule, exports: [BrowserModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserTestingModule, providers: [\n            { provide: APP_ID, useValue: 'a' },\n            _internalProvideZoneChangeDetection({}),\n            { provide: _ChangeDetectionScheduler, useExisting: _ChangeDetectionSchedulerImpl },\n            { provide: PlatformLocation, useClass: MockPlatformLocation },\n            { provide: TestComponentRenderer, useClass: DOMTestComponentRenderer },\n        ], imports: [BrowserModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: [\n                        { provide: APP_ID, useValue: 'a' },\n                        _internalProvideZoneChangeDetection({}),\n                        { provide: _ChangeDetectionScheduler, useExisting: _ChangeDetectionSchedulerImpl },\n                        { provide: PlatformLocation, useClass: MockPlatformLocation },\n                        { provide: TestComponentRenderer, useClass: DOMTestComponentRenderer },\n                    ],\n                }]\n        }] });\n\nexport { BrowserTestingModule, platformBrowserTesting };\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAO,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,gBAAgB,QAAQ,iBAAiB;AAChF,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,mCAAmC,IAAIC,mCAAmC,EAAEC,6BAA6B,IAAIC,6BAA6B,EAAEC,yBAAyB,IAAIC,yBAAyB,EAAEC,QAAQ,QAAQ,eAAe;AAC/R,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,eAAe,EAAEC,aAAa,QAAQ,wBAAwB;AACvE,OAAO,6BAA6B;;AAEpC;AACA;AACA;AACA,MAAMC,wBAAwB,SAASH,qBAAqB,CAAC;EAEzDI,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IAACC,eAAA;IACR,IAAI,CAACD,IAAI,GAAGA,IAAI;EACpB;EACAE,iBAAiBA,CAACC,QAAQ,EAAE;IACxB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAChC,MAAMC,WAAW,GAAG1B,OAAO,CAAC,CAAC,CAAC2B,kBAAkB,CAAC,CAAC,CAACC,aAAa,CAAC,KAAK,CAAC;IACvEF,WAAW,CAACG,YAAY,CAAC,IAAI,EAAEL,QAAQ,CAAC;IACxC,IAAI,CAACH,IAAI,CAACS,IAAI,CAACC,WAAW,CAACL,WAAW,CAAC;EAC3C;EACAM,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA;IACA;IACA;IACA,IAAI,OAAO,IAAI,CAACX,IAAI,CAACY,gBAAgB,KAAK,UAAU,EAAE;MAClD,IAAI,CAACR,yBAAyB,CAAC,CAAC;IACpC;EACJ;EACAA,yBAAyBA,CAAA,EAAG;IACxB,MAAMS,QAAQ,GAAG,IAAI,CAACb,IAAI,CAACY,gBAAgB,CAAC,YAAY,CAAC;IACzD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACtCnC,OAAO,CAAC,CAAC,CAACqC,MAAM,CAACH,QAAQ,CAACC,CAAC,CAAC,CAAC;IACjC;EACJ;AAGJ;AAACG,yBAAA,GA9BKnB,wBAAwB;AAAAG,eAAA,CAAxBH,wBAAwB,wBAAAoB,kCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA4B0ErB,yBAAwB,EAG9Cf,EAAE,CAAAqC,QAAA,CAH8DxC,QAAQ;AAAA;AAAAqB,eAAA,CA5BpJH,wBAAwB,+BA+BoDf,EAAE,CAAAsC,kBAAA;EAAAC,KAAA,EAFwBxB,yBAAwB;EAAAyB,OAAA,EAAxBzB,yBAAwB,CAAA0B;AAAA;AAEpI;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAkF1C,EAAE,CAAA2C,iBAAA,CAAQ5B,wBAAwB,EAAc,CAAC;IACvH6B,IAAI,EAAE1C;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE0C,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CF,IAAI,EAAE3C,MAAM;MACZ8C,IAAI,EAAE,CAAClD,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA,MAAMmD,sBAAsB,GAAG7C,qBAAqB,CAACU,eAAe,EAAE,gBAAgB,CAAC;AACvF;AACA;AACA;AACA;AACA;AACA,MAAMoC,oBAAoB,CAAC;AAU1BC,qBAAA,GAVKD,oBAAoB;AAAA/B,eAAA,CAApB+B,oBAAoB,wBAAAE,8BAAAf,iBAAA;EAAA,YAAAA,iBAAA,IAC8Ea,qBAAoB;AAAA;AAAA/B,eAAA,CADtH+B,oBAAoB,8BAlBwDjD,EAAE,CAAAoD,gBAAA;EAAAR,IAAA,EAoBqBK,qBAAoB;EAAAI,OAAA,GAAYvC,aAAa;AAAA;AAAAI,eAAA,CAFhJ+B,oBAAoB,8BAlBwDjD,EAAE,CAAAsD,gBAAA;EAAAC,SAAA,EAqBsD,CAC9H;IAAEC,OAAO,EAAEpD,MAAM;IAAEqD,QAAQ,EAAE;EAAI,CAAC,EAClCnD,mCAAmC,CAAC,CAAC,CAAC,CAAC,EACvC;IAAEkD,OAAO,EAAE9C,yBAAyB;IAAEgD,WAAW,EAAElD;EAA8B,CAAC,EAClF;IAAEgD,OAAO,EAAE1D,gBAAgB;IAAE6D,QAAQ,EAAE5D;EAAqB,CAAC,EAC7D;IAAEyD,OAAO,EAAE5C,qBAAqB;IAAE+C,QAAQ,EAAE5C;EAAyB,CAAC,CACzE;EAAA6C,OAAA,GAAY9C,aAAa;AAAA;AAElC;EAAA,QAAA4B,SAAA,oBAAAA,SAAA,KA7BkF1C,EAAE,CAAA2C,iBAAA,CA6BQM,oBAAoB,EAAc,CAAC;IACnHL,IAAI,EAAEjC,QAAQ;IACdoC,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAACvC,aAAa,CAAC;MACxByC,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEpD,MAAM;QAAEqD,QAAQ,EAAE;MAAI,CAAC,EAClCnD,mCAAmC,CAAC,CAAC,CAAC,CAAC,EACvC;QAAEkD,OAAO,EAAE9C,yBAAyB;QAAEgD,WAAW,EAAElD;MAA8B,CAAC,EAClF;QAAEgD,OAAO,EAAE1D,gBAAgB;QAAE6D,QAAQ,EAAE5D;MAAqB,CAAC,EAC7D;QAAEyD,OAAO,EAAE5C,qBAAqB;QAAE+C,QAAQ,EAAE5C;MAAyB,CAAC;IAE9E,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASkC,oBAAoB,EAAED,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}