{"ast": null, "code": "var _DocsChipComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs-chip.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../docs.component.scss?ngResource\";\nimport __NG_CLI_RESOURCE__2 from \"./docs-chip.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsChipComponent = (_DocsChipComponent = class DocsChipComponent {\n  constructor() {}\n  ngOnInit() {}\n}, _DocsChipComponent.ctorParameters = () => [], _DocsChipComponent);\nDocsChipComponent = __decorate([Component({\n  selector: 'lib-docs-chip',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1, __NG_CLI_RESOURCE__2]\n})], DocsChipComponent);\nexport { DocsChipComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "__NG_CLI_RESOURCE__2", "Component", "DocsChipComponent", "_DocsChipComponent", "constructor", "ngOnInit", "ctorParameters", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/styles/docs/docs-chip/docs-chip.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs-chip.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../docs.component.scss?ngResource\";\nimport __NG_CLI_RESOURCE__2 from \"./docs-chip.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsChipComponent = class DocsChipComponent {\n    constructor() { }\n    ngOnInit() {\n    }\n    static { this.ctorParameters = () => []; }\n};\nDocsChipComponent = __decorate([\n    Component({\n        selector: 'lib-docs-chip',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1, __NG_CLI_RESOURCE__2]\n    })\n], DocsChipComponent);\nexport { DocsChipComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,SAASC,SAAS,QAAQ,eAAe;AACzC,IAAIC,iBAAiB,IAAAC,kBAAA,GAAG,MAAMD,iBAAiB,CAAC;EAC5CE,WAAWA,CAAA,EAAG,CAAE;EAChBC,QAAQA,CAAA,EAAG,CACX;AAEJ,CAAC,EADYF,kBAAA,CAAKG,cAAc,GAAG,MAAM,EAAE,EAAAH,kBAAA,CAC1C;AACDD,iBAAiB,GAAGL,UAAU,CAAC,CAC3BI,SAAS,CAAC;EACNM,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAEV,oBAAoB;EAC9BW,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACX,oBAAoB,EAAEC,oBAAoB;AACvD,CAAC,CAAC,CACL,EAAEE,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}