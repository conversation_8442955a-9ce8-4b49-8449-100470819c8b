{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nvar QueueAction = function (_super) {\n  __extends(QueueAction, _super);\n  function QueueAction(scheduler, work) {\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    return _this;\n  }\n  QueueAction.prototype.schedule = function (state, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay > 0) {\n      return _super.prototype.schedule.call(this, state, delay);\n    }\n    this.delay = delay;\n    this.state = state;\n    this.scheduler.flush(this);\n    return this;\n  };\n  QueueAction.prototype.execute = function (state, delay) {\n    return delay > 0 || this.closed ? _super.prototype.execute.call(this, state, delay) : this._execute(state, delay);\n  };\n  QueueAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay != null && delay > 0 || delay == null && this.delay > 0) {\n      return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n    }\n    scheduler.flush(this);\n    return 0;\n  };\n  return QueueAction;\n}(AsyncAction);\nexport { QueueAction };\n//# sourceMappingURL=QueueAction.js.map", "map": {"version": 3, "names": ["__extends", "AsyncAction", "QueueAction", "_super", "scheduler", "work", "_this", "call", "prototype", "schedule", "state", "delay", "flush", "execute", "closed", "_execute", "requestAsyncId", "id"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/QueueAction.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nvar QueueAction = (function (_super) {\n    __extends(QueueAction, _super);\n    function QueueAction(scheduler, work) {\n        var _this = _super.call(this, scheduler, work) || this;\n        _this.scheduler = scheduler;\n        _this.work = work;\n        return _this;\n    }\n    QueueAction.prototype.schedule = function (state, delay) {\n        if (delay === void 0) { delay = 0; }\n        if (delay > 0) {\n            return _super.prototype.schedule.call(this, state, delay);\n        }\n        this.delay = delay;\n        this.state = state;\n        this.scheduler.flush(this);\n        return this;\n    };\n    QueueAction.prototype.execute = function (state, delay) {\n        return delay > 0 || this.closed ? _super.prototype.execute.call(this, state, delay) : this._execute(state, delay);\n    };\n    QueueAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        if ((delay != null && delay > 0) || (delay == null && this.delay > 0)) {\n            return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n        }\n        scheduler.flush(this);\n        return 0;\n    };\n    return QueueAction;\n}(AsyncAction));\nexport { QueueAction };\n//# sourceMappingURL=QueueAction.js.map"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,WAAW,QAAQ,eAAe;AAC3C,IAAIC,WAAW,GAAI,UAAUC,MAAM,EAAE;EACjCH,SAAS,CAACE,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAACE,SAAS,EAAEC,IAAI,EAAE;IAClC,IAAIC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAAC,IAAI,EAAEH,SAAS,EAAEC,IAAI,CAAC,IAAI,IAAI;IACtDC,KAAK,CAACF,SAAS,GAAGA,SAAS;IAC3BE,KAAK,CAACD,IAAI,GAAGA,IAAI;IACjB,OAAOC,KAAK;EAChB;EACAJ,WAAW,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACrD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX,OAAOR,MAAM,CAACK,SAAS,CAACC,QAAQ,CAACF,IAAI,CAAC,IAAI,EAAEG,KAAK,EAAEC,KAAK,CAAC;IAC7D;IACA,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACN,SAAS,CAACQ,KAAK,CAAC,IAAI,CAAC;IAC1B,OAAO,IAAI;EACf,CAAC;EACDV,WAAW,CAACM,SAAS,CAACK,OAAO,GAAG,UAAUH,KAAK,EAAEC,KAAK,EAAE;IACpD,OAAOA,KAAK,GAAG,CAAC,IAAI,IAAI,CAACG,MAAM,GAAGX,MAAM,CAACK,SAAS,CAACK,OAAO,CAACN,IAAI,CAAC,IAAI,EAAEG,KAAK,EAAEC,KAAK,CAAC,GAAG,IAAI,CAACI,QAAQ,CAACL,KAAK,EAAEC,KAAK,CAAC;EACrH,CAAC;EACDT,WAAW,CAACM,SAAS,CAACQ,cAAc,GAAG,UAAUZ,SAAS,EAAEa,EAAE,EAAEN,KAAK,EAAE;IACnE,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAKA,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAG,CAAC,IAAMA,KAAK,IAAI,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAAE,EAAE;MACnE,OAAOR,MAAM,CAACK,SAAS,CAACQ,cAAc,CAACT,IAAI,CAAC,IAAI,EAAEH,SAAS,EAAEa,EAAE,EAAEN,KAAK,CAAC;IAC3E;IACAP,SAAS,CAACQ,KAAK,CAAC,IAAI,CAAC;IACrB,OAAO,CAAC;EACZ,CAAC;EACD,OAAOV,WAAW;AACtB,CAAC,CAACD,WAAW,CAAE;AACf,SAASC,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}