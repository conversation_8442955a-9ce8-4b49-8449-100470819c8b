{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { MatButtonModule } from '@angular/material/button';\nimport { NgModule } from '@angular/core';\nimport { SwuiNotificationsModule } from '../swui-notifications.module';\nimport { SwuiNotificationsTemplateComponent } from './swui-notifications-template.component';\nlet SwuiNotificationsTemplateModule = class SwuiNotificationsTemplateModule {};\nSwuiNotificationsTemplateModule = __decorate([NgModule({\n  imports: [BrowserAnimationsModule, MatButtonModule, SwuiNotificationsModule],\n  declarations: [SwuiNotificationsTemplateComponent]\n})], SwuiNotificationsTemplateModule);\nexport { SwuiNotificationsTemplateModule };", "map": {"version": 3, "names": ["__decorate", "BrowserAnimationsModule", "MatButtonModule", "NgModule", "SwuiNotificationsModule", "SwuiNotificationsTemplateComponent", "SwuiNotificationsTemplateModule", "imports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-notifications/stories/swui-notifications-template.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { MatButtonModule } from '@angular/material/button';\nimport { NgModule } from '@angular/core';\nimport { SwuiNotificationsModule } from '../swui-notifications.module';\nimport { SwuiNotificationsTemplateComponent } from './swui-notifications-template.component';\nlet SwuiNotificationsTemplateModule = class SwuiNotificationsTemplateModule {\n};\nSwuiNotificationsTemplateModule = __decorate([\n    NgModule({\n        imports: [\n            BrowserAnimationsModule,\n            MatButtonModule,\n            SwuiNotificationsModule,\n        ],\n        declarations: [\n            SwuiNotificationsTemplateComponent\n        ],\n    })\n], SwuiNotificationsTemplateModule);\nexport { SwuiNotificationsTemplateModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,kCAAkC,QAAQ,yCAAyC;AAC5F,IAAIC,+BAA+B,GAAG,MAAMA,+BAA+B,CAAC,EAC3E;AACDA,+BAA+B,GAAGN,UAAU,CAAC,CACzCG,QAAQ,CAAC;EACLI,OAAO,EAAE,CACLN,uBAAuB,EACvBC,eAAe,EACfE,uBAAuB,CAC1B;EACDI,YAAY,EAAE,CACVH,kCAAkC;AAE1C,CAAC,CAAC,CACL,EAAEC,+BAA+B,CAAC;AACnC,SAASA,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}