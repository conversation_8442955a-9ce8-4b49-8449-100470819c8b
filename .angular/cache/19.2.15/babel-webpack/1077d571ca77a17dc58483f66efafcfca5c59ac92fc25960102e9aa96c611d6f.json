{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { SwuiMenuSelectModule } from '../../swui-menu-select/swui-menu-select.module';\nimport { SwuiColumnsManagementComponent } from './columns-management.component';\ndescribe('SwuiColumnsManagementComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [TranslateModule.forRoot(), MatButtonModule, MatTooltipModule, MatMenuModule, SwuiMenuSelectModule]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiColumnsManagementComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "TranslateModule", "MatButtonModule", "MatTooltipModule", "MatMenuModule", "SwuiMenuSelectModule", "SwuiColumnsManagementComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "forRoot", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/columns-management/columns-management.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { SwuiMenuSelectModule } from '../../swui-menu-select/swui-menu-select.module';\nimport { SwuiColumnsManagementComponent } from './columns-management.component';\n\n\ndescribe('SwuiColumnsManagementComponent', () => {\n  let component: SwuiColumnsManagementComponent;\n  let fixture: ComponentFixture<SwuiColumnsManagementComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [\n        TranslateModule.forRoot(),\n        MatButtonModule,\n        MatTooltipModule,\n        MatMenuModule,\n        SwuiMenuSelectModule,\n      ]\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiColumnsManagementComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAC/E,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,oBAAoB,QAAQ,gDAAgD;AACrF,SAASC,8BAA8B,QAAQ,gCAAgC;AAG/EC,QAAQ,CAAC,gCAAgC,EAAE,MAAK;EAC9C,IAAIC,SAAyC;EAC7C,IAAIC,OAAyD;EAE7DC,UAAU,CAACV,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACY,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPX,eAAe,CAACY,OAAO,EAAE,EACzBX,eAAe,EACfC,gBAAgB,EAChBC,aAAa,EACbC,oBAAoB;KAEvB,CAAC,CACCS,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHJ,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGV,OAAO,CAACgB,eAAe,CAACT,8BAA8B,CAAC;IACjEE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}