{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiDateTimeRangeComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-date-time-range.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-date-time-range.component.scss?ngResource\";\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild, ViewEncapsulation } from '@angular/core';\nimport { UntypedFormBuilder, FormGroupDirective, NgControl } from '@angular/forms';\nimport { takeUntil } from 'rxjs/operators';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport * as moment from 'moment';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nfunction toMoment(value) {\n  if (typeof value === 'undefined' || value === null) {\n    return null;\n  }\n  if (moment.isMoment(value)) {\n    return value;\n  }\n  const date = moment.parseZone(value);\n  if (date.isValid()) {\n    return date;\n  }\n  return null;\n}\nconst CONTROL_NAME = 'lib-swui-date-time-range';\nlet nextUniqueId = 0;\nlet SwuiDateTimeRangeComponent = (_SwuiDateTimeRangeComponent = class SwuiDateTimeRangeComponent extends SwuiMatFormFieldControl {\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    this.writeValue(value);\n    this.stateChanges.next(undefined);\n  }\n  get minDate() {\n    return this._minDate;\n  }\n  set minDate(value) {\n    this._minDate = value;\n    this.processToMinDate(this.fromControl.value);\n  }\n  get maxDate() {\n    return this._maxDate;\n  }\n  set maxDate(value) {\n    this._maxDate = value;\n    this.processFromMaxDate(this.toControl.value);\n  }\n  get empty() {\n    return !this.fromControl.value && !this.toControl.value;\n  }\n  get shouldLabelFloat() {\n    return true;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, fb) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.isInline = false;\n    this.hideCustomPeriods = false;\n    this.smallCustomPeriodsButton = false;\n    this.from = 'from';\n    this.to = 'to';\n    this.fromPlaceholder = '';\n    this.toPlaceholder = '';\n    this.disableTime = {\n      hour: false,\n      minute: false,\n      second: false\n    };\n    this.hideTime = false;\n    this.controlType = CONTROL_NAME;\n    this.fromLabelRised = false;\n    this.toLabelRised = false;\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this.tabindex = 0;\n    this.form = fb.group({\n      from: [null],\n      to: [null]\n    });\n  }\n  ngOnInit() {\n    this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(val => {\n      this.fromLabelRised = !!val.from;\n      this.toLabelRised = !!val.to;\n      this.processFromMaxDate(val.to);\n      this.processToMinDate(val.from);\n      this.onChange({\n        [this.from]: val.from,\n        [this.to]: val.to\n      });\n    });\n  }\n  writeValue(obj) {\n    if (obj === null) {\n      this.form.reset();\n    }\n    const value = _objectSpread({\n      [this.from]: null,\n      [this.to]: null\n    }, obj);\n    this._value = value;\n    this.form.patchValue(value);\n  }\n  onContainerClick(event) {\n    if (!this.disabled && event.target.tagName.toLowerCase() !== 'input') {\n      this.elRef.nativeElement.focus();\n    }\n  }\n  get config() {\n    return {\n      dateFormat: this.dateFormat,\n      timeFormat: this.timeFormat,\n      timeDisableLevel: this.disableTime,\n      disableTimepicker: this.hideTime,\n      timeZone: this.timeZone\n    };\n  }\n  onPeriodChange(val) {\n    this.form.setValue(val);\n  }\n  onResetClick(event, control) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.onTouched();\n    control.setValue(null);\n  }\n  get fromControl() {\n    return this.form.get('from');\n  }\n  get toControl() {\n    return this.form.get('to');\n  }\n  onDisabledState(disabled) {\n    disabled ? this.form.disable() : this.form.enable();\n  }\n  isErrorState() {\n    if (this.fromInput && this.toInput) {\n      return this.fromInput.errorState || this.toInput.errorState;\n    }\n    return false;\n  }\n  processToMinDate(value) {\n    const min = toMoment(this.minDate) || undefined;\n    this.processedToMinDate = min && min.diff(value) > 0 || !value ? min : value;\n  }\n  processFromMaxDate(value) {\n    const max = toMoment(this.maxDate) || undefined;\n    this.processedFromMaxDate = max && max.diff(value) < 0 || !value ? max : value;\n  }\n}, _SwuiDateTimeRangeComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}, {\n  type: UntypedFormBuilder\n}], _SwuiDateTimeRangeComponent.propDecorators = {\n  value: [{\n    type: Input\n  }],\n  minDate: [{\n    type: Input\n  }],\n  maxDate: [{\n    type: Input\n  }],\n  isInline: [{\n    type: Input\n  }],\n  hideCustomPeriods: [{\n    type: Input\n  }],\n  smallCustomPeriodsButton: [{\n    type: Input\n  }],\n  from: [{\n    type: Input\n  }],\n  to: [{\n    type: Input\n  }],\n  fromPlaceholder: [{\n    type: Input\n  }],\n  toPlaceholder: [{\n    type: Input\n  }],\n  dateFormat: [{\n    type: Input\n  }],\n  timeFormat: [{\n    type: Input\n  }],\n  timeZone: [{\n    type: Input\n  }],\n  disableTime: [{\n    type: Input\n  }],\n  hideTime: [{\n    type: Input\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  tabindex: [{\n    type: HostBinding,\n    args: ['attr.tabindex']\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }],\n  fromLabelRef: [{\n    type: ViewChild,\n    args: ['fromLabel']\n  }],\n  fromInput: [{\n    type: ViewChild,\n    args: ['from']\n  }],\n  toInput: [{\n    type: ViewChild,\n    args: ['to']\n  }]\n}, _SwuiDateTimeRangeComponent);\nSwuiDateTimeRangeComponent = __decorate([Component({\n  selector: 'lib-swui-date-time-range',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiDateTimeRangeComponent\n  }],\n  encapsulation: ViewEncapsulation.None,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiDateTimeRangeComponent);\nexport { SwuiDateTimeRangeComponent };", "map": {"version": 3, "names": ["Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "ViewEncapsulation", "UntypedFormBuilder", "FormGroupDirective", "NgControl", "takeUntil", "MatFormFieldControl", "FocusMonitor", "moment", "SwuiMatFormFieldControl", "ErrorStateMatcher", "toMoment", "value", "isMoment", "date", "parseZone", "<PERSON><PERSON><PERSON><PERSON>", "CONTROL_NAME", "nextUniqueId", "SwuiDateTimeRangeComponent", "_SwuiDateTimeRangeComponent", "_value", "writeValue", "stateChanges", "next", "undefined", "minDate", "_minDate", "processToMinDate", "fromControl", "maxDate", "_maxDate", "processFromMaxDate", "toControl", "empty", "shouldLabelFloat", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "fb", "isInline", "hideCustomPeriods", "smallCustomPeriodsButton", "from", "to", "fromPlaceholder", "toPlaceholder", "disableTime", "hour", "minute", "second", "hideTime", "controlType", "fromLabelRised", "toLabelRised", "id", "tabindex", "form", "group", "ngOnInit", "valueChanges", "pipe", "destroyed$", "subscribe", "val", "onChange", "obj", "reset", "_objectSpread", "patchValue", "onContainerClick", "event", "disabled", "target", "tagName", "toLowerCase", "nativeElement", "focus", "config", "dateFormat", "timeFormat", "timeDisableLevel", "disable<PERSON><PERSON><PERSON><PERSON>", "timeZone", "onPeriodChange", "setValue", "onResetClick", "control", "preventDefault", "stopPropagation", "onTouched", "get", "onDisabledState", "disable", "enable", "isErrorState", "fromInput", "toInput", "errorState", "min", "processedToMinDate", "diff", "max", "processedFromMaxDate", "type", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "provide", "useExisting", "encapsulation", "None", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-range/swui-date-time-range.component.ts"], "sourcesContent": ["import { Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild, ViewEncapsulation } from '@angular/core';\nimport { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, FormGroupDirective, NgControl } from '@angular/forms';\nimport { takeUntil } from 'rxjs/operators';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport * as moment from 'moment';\nimport { SwuiDateTimepickerConfig } from '../swui-datetimepicker/swui-datetimepicker.interface';\nimport { SwuiTimepickerTimeDisableLevel } from '../swui-timepicker/swui-timepicker.interface';\nimport { CustomPeriods } from './custom-period/custom-period.interface';\nimport { SwuiDatetimepickerComponent } from '../swui-datetimepicker/swui-datetimepicker.component';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\n\n\nfunction toMoment( value: moment.Moment | string | undefined | null ): moment.Moment | null {\n  if (typeof value === 'undefined' || value === null) {\n    return null;\n  }\n  if (moment.isMoment(value)) {\n    return value;\n  }\n  const date = moment.parseZone(value);\n  if (date.isValid()) {\n    return date;\n  }\n  return null;\n}\n\nconst CONTROL_NAME = 'lib-swui-date-time-range';\nlet nextUniqueId = 0;\n\n@Component({\n    selector: 'lib-swui-date-time-range',\n    templateUrl: './swui-date-time-range.component.html',\n    styleUrls: ['./swui-date-time-range.component.scss'],\n    providers: [{ provide: MatFormFieldControl, useExisting: SwuiDateTimeRangeComponent }],\n    encapsulation: ViewEncapsulation.None,\n    standalone: false\n})\nexport class SwuiDateTimeRangeComponent extends SwuiMatFormFieldControl<any> implements OnInit {\n  @Input()\n  get value(): any {\n    return this._value;\n  }\n\n  set value( value: any ) {\n    this.writeValue(value);\n    this.stateChanges.next(undefined);\n  }\n\n  @Input()\n  get minDate(): moment.Moment | string | undefined {\n    return this._minDate;\n  }\n\n  set minDate( value: moment.Moment | string | undefined ) {\n    this._minDate = value;\n    this.processToMinDate(this.fromControl.value);\n  }\n\n  @Input()\n  get maxDate(): moment.Moment | string | undefined {\n    return this._maxDate;\n  }\n\n  set maxDate( value: moment.Moment | string | undefined ) {\n    this._maxDate = value;\n    this.processFromMaxDate(this.toControl.value);\n  }\n\n  @Input() isInline = false;\n  @Input() hideCustomPeriods = false;\n  @Input() smallCustomPeriodsButton = false;\n\n  @Input() from = 'from';\n  @Input() to = 'to';\n\n  @Input() fromPlaceholder = '';\n  @Input() toPlaceholder = '';\n\n  @Input() dateFormat?: string;\n  @Input() timeFormat?: string;\n  @Input() timeZone?: string;\n\n  @Input() disableTime?: SwuiTimepickerTimeDisableLevel = {\n    hour: false,\n    minute: false,\n    second: false\n  };\n  @Input() hideTime = false;\n\n  get empty() {\n    return !this.fromControl.value && !this.toControl.value;\n  }\n\n  readonly controlType = CONTROL_NAME;\n\n  processedToMinDate?: moment.Moment;\n  processedFromMaxDate?: moment.Moment;\n  fromLabelRised = false;\n  toLabelRised = false;\n\n  readonly form: UntypedFormGroup;\n\n  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;\n  @HostBinding('attr.tabindex') tabindex = 0;\n\n  @HostBinding('class.floating')\n  get shouldLabelFloat() {\n    return true;\n  }\n\n  @ViewChild('fromLabel') fromLabelRef?: ElementRef<HTMLElement>;\n  @ViewChild('from') fromInput?: SwuiDatetimepickerComponent;\n  @ViewChild('to') toInput?: SwuiDatetimepickerComponent;\n\n  private _value: any;\n\n  private _minDate: moment.Moment | string | undefined;\n  private _maxDate: moment.Moment | string | undefined;\n\n  constructor( fm: FocusMonitor,\n               elRef: ElementRef<HTMLElement>,\n               @Optional() @Self() ngControl: NgControl,\n               @Optional() parentFormGroup: FormGroupDirective,\n               errorStateMatcher: ErrorStateMatcher,\n               fb: UntypedFormBuilder ) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.form = fb.group({\n      from: [null],\n      to: [null]\n    });\n  }\n\n  ngOnInit(): void {\n    this.form.valueChanges\n      .pipe(takeUntil(this.destroyed$))\n      .subscribe(( val: { from: moment.Moment, to: moment.Moment } ) => {\n\n        this.fromLabelRised = !!val.from;\n        this.toLabelRised = !!val.to;\n\n        this.processFromMaxDate(val.to);\n        this.processToMinDate(val.from);\n        this.onChange({ [this.from]: val.from, [this.to]: val.to });\n      });\n  }\n\n  writeValue( obj: any ): void {\n    if (obj === null) {\n      this.form.reset();\n    }\n    const value = { [this.from]: null, [this.to]: null, ...obj };\n    this._value = value;\n    this.form.patchValue(value);\n  }\n\n  onContainerClick( event: Event ) {\n    if (!this.disabled && (event.target as Element).tagName.toLowerCase() !== 'input') {\n      this.elRef.nativeElement.focus();\n    }\n  }\n\n  get config(): SwuiDateTimepickerConfig {\n    return {\n      dateFormat: this.dateFormat,\n      timeFormat: this.timeFormat,\n      timeDisableLevel: this.disableTime,\n      disableTimepicker: this.hideTime,\n      timeZone: this.timeZone\n    };\n  }\n\n  onPeriodChange( val: CustomPeriods ) {\n    this.form.setValue(val);\n  }\n\n  onResetClick( event: Event, control: UntypedFormControl ) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.onTouched();\n    control.setValue(null);\n  }\n\n  get fromControl(): UntypedFormControl {\n    return this.form.get('from') as UntypedFormControl;\n  }\n\n  get toControl(): UntypedFormControl {\n    return this.form.get('to') as UntypedFormControl;\n  }\n\n  protected onDisabledState( disabled: boolean ): void {\n    disabled ? this.form.disable() : this.form.enable();\n  }\n\n  protected isErrorState(): boolean {\n    if (this.fromInput && this.toInput) {\n      return this.fromInput.errorState || this.toInput.errorState;\n    }\n    return false;\n  }\n\n  private processToMinDate( value: moment.Moment ) {\n    const min = toMoment(this.minDate) || undefined;\n    this.processedToMinDate = min && min.diff(value) > 0 || !value ? min : value;\n  }\n\n  private processFromMaxDate( value: moment.Moment ) {\n    const max = toMoment(this.maxDate) || undefined;\n    this.processedFromMaxDate = max && max.diff(value) < 0 || !value ? max : value;\n  }\n}\n"], "mappings": ";;;;;AAAA,SAASA,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAUC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,EAAEC,iBAAiB,QAAQ,eAAe;AAC/H,SAASC,kBAAkB,EAAwCC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AACxH,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAKhC,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAG1D,SAASC,QAAQA,CAAEC,KAAgD;EACjE,IAAI,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,EAAE;IAClD,OAAO,IAAI;EACb;EACA,IAAIJ,MAAM,CAACK,QAAQ,CAACD,KAAK,CAAC,EAAE;IAC1B,OAAOA,KAAK;EACd;EACA,MAAME,IAAI,GAAGN,MAAM,CAACO,SAAS,CAACH,KAAK,CAAC;EACpC,IAAIE,IAAI,CAACE,OAAO,EAAE,EAAE;IAClB,OAAOF,IAAI;EACb;EACA,OAAO,IAAI;AACb;AAEA,MAAMG,YAAY,GAAG,0BAA0B;AAC/C,IAAIC,YAAY,GAAG,CAAC;AAUb,IAAMC,0BAA0B,IAAAC,2BAAA,GAAhC,MAAMD,0BAA2B,SAAQV,uBAA4B;MAEtEG,KAAKA,CAAA;IACP,OAAO,IAAI,CAACS,MAAM;EACpB;EAEA,IAAIT,KAAKA,CAAEA,KAAU;IACnB,IAAI,CAACU,UAAU,CAACV,KAAK,CAAC;IACtB,IAAI,CAACW,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;EACnC;MAGIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACC,QAAQ;EACtB;EAEA,IAAID,OAAOA,CAAEd,KAAyC;IACpD,IAAI,CAACe,QAAQ,GAAGf,KAAK;IACrB,IAAI,CAACgB,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAACjB,KAAK,CAAC;EAC/C;MAGIkB,OAAOA,CAAA;IACT,OAAO,IAAI,CAACC,QAAQ;EACtB;EAEA,IAAID,OAAOA,CAAElB,KAAyC;IACpD,IAAI,CAACmB,QAAQ,GAAGnB,KAAK;IACrB,IAAI,CAACoB,kBAAkB,CAAC,IAAI,CAACC,SAAS,CAACrB,KAAK,CAAC;EAC/C;EAuBA,IAAIsB,KAAKA,CAAA;IACP,OAAO,CAAC,IAAI,CAACL,WAAW,CAACjB,KAAK,IAAI,CAAC,IAAI,CAACqB,SAAS,CAACrB,KAAK;EACzD;MAeIuB,gBAAgBA,CAAA;IAClB,OAAO,IAAI;EACb;EAWAC,YAAaC,EAAgB,EAChBC,KAA8B,EACVC,SAAoB,EAC5BC,eAAmC,EAC/CC,iBAAoC,EACpCC,EAAsB;IACjC,KAAK,CAACL,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAzDxD,KAAAE,QAAQ,GAAG,KAAK;IAChB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,wBAAwB,GAAG,KAAK;IAEhC,KAAAC,IAAI,GAAG,MAAM;IACb,KAAAC,EAAE,GAAG,IAAI;IAET,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,aAAa,GAAG,EAAE;IAMlB,KAAAC,WAAW,GAAoC;MACtDC,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;KACT;IACQ,KAAAC,QAAQ,GAAG,KAAK;IAMhB,KAAAC,WAAW,GAAGtC,YAAY;IAInC,KAAAuC,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IAII,KAAAC,EAAE,GAAG,GAAGzC,YAAY,IAAIC,YAAY,EAAE,EAAE;IAClC,KAAAyC,QAAQ,GAAG,CAAC;IAuBxC,IAAI,CAACC,IAAI,GAAGlB,EAAE,CAACmB,KAAK,CAAC;MACnBf,IAAI,EAAE,CAAC,IAAI,CAAC;MACZC,EAAE,EAAE,CAAC,IAAI;KACV,CAAC;EACJ;EAEAe,QAAQA,CAAA;IACN,IAAI,CAACF,IAAI,CAACG,YAAY,CACnBC,IAAI,CAAC3D,SAAS,CAAC,IAAI,CAAC4D,UAAU,CAAC,CAAC,CAChCC,SAAS,CAAGC,GAA+C,IAAK;MAE/D,IAAI,CAACX,cAAc,GAAG,CAAC,CAACW,GAAG,CAACrB,IAAI;MAChC,IAAI,CAACW,YAAY,GAAG,CAAC,CAACU,GAAG,CAACpB,EAAE;MAE5B,IAAI,CAACf,kBAAkB,CAACmC,GAAG,CAACpB,EAAE,CAAC;MAC/B,IAAI,CAACnB,gBAAgB,CAACuC,GAAG,CAACrB,IAAI,CAAC;MAC/B,IAAI,CAACsB,QAAQ,CAAC;QAAE,CAAC,IAAI,CAACtB,IAAI,GAAGqB,GAAG,CAACrB,IAAI;QAAE,CAAC,IAAI,CAACC,EAAE,GAAGoB,GAAG,CAACpB;MAAE,CAAE,CAAC;IAC7D,CAAC,CAAC;EACN;EAEAzB,UAAUA,CAAE+C,GAAQ;IAClB,IAAIA,GAAG,KAAK,IAAI,EAAE;MAChB,IAAI,CAACT,IAAI,CAACU,KAAK,EAAE;IACnB;IACA,MAAM1D,KAAK,GAAA2D,aAAA;MAAK,CAAC,IAAI,CAACzB,IAAI,GAAG,IAAI;MAAE,CAAC,IAAI,CAACC,EAAE,GAAG;IAAI,GAAKsB,GAAG,CAAE;IAC5D,IAAI,CAAChD,MAAM,GAAGT,KAAK;IACnB,IAAI,CAACgD,IAAI,CAACY,UAAU,CAAC5D,KAAK,CAAC;EAC7B;EAEA6D,gBAAgBA,CAAEC,KAAY;IAC5B,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAKD,KAAK,CAACE,MAAkB,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,EAAE;MACjF,IAAI,CAACxC,KAAK,CAACyC,aAAa,CAACC,KAAK,EAAE;IAClC;EACF;EAEA,IAAIC,MAAMA,CAAA;IACR,OAAO;MACLC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,gBAAgB,EAAE,IAAI,CAAClC,WAAW;MAClCmC,iBAAiB,EAAE,IAAI,CAAC/B,QAAQ;MAChCgC,QAAQ,EAAE,IAAI,CAACA;KAChB;EACH;EAEAC,cAAcA,CAAEpB,GAAkB;IAChC,IAAI,CAACP,IAAI,CAAC4B,QAAQ,CAACrB,GAAG,CAAC;EACzB;EAEAsB,YAAYA,CAAEf,KAAY,EAAEgB,OAA2B;IACrDhB,KAAK,CAACiB,cAAc,EAAE;IACtBjB,KAAK,CAACkB,eAAe,EAAE;IACvB,IAAI,CAACC,SAAS,EAAE;IAChBH,OAAO,CAACF,QAAQ,CAAC,IAAI,CAAC;EACxB;EAEA,IAAI3D,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC+B,IAAI,CAACkC,GAAG,CAAC,MAAM,CAAuB;EACpD;EAEA,IAAI7D,SAASA,CAAA;IACX,OAAO,IAAI,CAAC2B,IAAI,CAACkC,GAAG,CAAC,IAAI,CAAuB;EAClD;EAEUC,eAAeA,CAAEpB,QAAiB;IAC1CA,QAAQ,GAAG,IAAI,CAACf,IAAI,CAACoC,OAAO,EAAE,GAAG,IAAI,CAACpC,IAAI,CAACqC,MAAM,EAAE;EACrD;EAEUC,YAAYA,CAAA;IACpB,IAAI,IAAI,CAACC,SAAS,IAAI,IAAI,CAACC,OAAO,EAAE;MAClC,OAAO,IAAI,CAACD,SAAS,CAACE,UAAU,IAAI,IAAI,CAACD,OAAO,CAACC,UAAU;IAC7D;IACA,OAAO,KAAK;EACd;EAEQzE,gBAAgBA,CAAEhB,KAAoB;IAC5C,MAAM0F,GAAG,GAAG3F,QAAQ,CAAC,IAAI,CAACe,OAAO,CAAC,IAAID,SAAS;IAC/C,IAAI,CAAC8E,kBAAkB,GAAGD,GAAG,IAAIA,GAAG,CAACE,IAAI,CAAC5F,KAAK,CAAC,GAAG,CAAC,IAAI,CAACA,KAAK,GAAG0F,GAAG,GAAG1F,KAAK;EAC9E;EAEQoB,kBAAkBA,CAAEpB,KAAoB;IAC9C,MAAM6F,GAAG,GAAG9F,QAAQ,CAAC,IAAI,CAACmB,OAAO,CAAC,IAAIL,SAAS;IAC/C,IAAI,CAACiF,oBAAoB,GAAGD,GAAG,IAAIA,GAAG,CAACD,IAAI,CAAC5F,KAAK,CAAC,GAAG,CAAC,IAAI,CAACA,KAAK,GAAG6F,GAAG,GAAG7F,KAAK;EAChF;;;;;;;;UAxFcd;EAAQ;IAAA6G,IAAA,EAAI5G;EAAI;AAAA,G;;;UAChBD;EAAQ;AAAA,G;;;;;;UApFrBD;EAAK;;UAULA;EAAK;;UAULA;EAAK;;UAULA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UAKLA;EAAK;;UAeLD;EAAW;;UACXA,WAAW;IAAAgH,IAAA,GAAC,eAAe;EAAA;;UAE3BhH,WAAW;IAAAgH,IAAA,GAAC,gBAAgB;EAAA;;UAK5B5G,SAAS;IAAA4G,IAAA,GAAC,WAAW;EAAA;;UACrB5G,SAAS;IAAA4G,IAAA,GAAC,MAAM;EAAA;;UAChB5G,SAAS;IAAA4G,IAAA,GAAC,IAAI;EAAA;;AA3EJzF,0BAA0B,GAAA0F,UAAA,EARtCnH,SAAS,CAAC;EACPoH,QAAQ,EAAE,0BAA0B;EACpCC,QAAA,EAAAC,oBAAoD;EAEpDC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAE5G,mBAAmB;IAAE6G,WAAW,EAAEhG;EAA0B,CAAE,CAAC;EACtFiG,aAAa,EAAEnH,iBAAiB,CAACoH,IAAI;EACrCC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWnG,0BAA0B,CA6KtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}