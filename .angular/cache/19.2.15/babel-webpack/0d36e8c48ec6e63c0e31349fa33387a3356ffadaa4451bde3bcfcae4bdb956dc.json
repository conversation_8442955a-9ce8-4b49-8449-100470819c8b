{"ast": null, "code": "import { createServiceFactory } from '@ngneat/spectator';\nimport { SwHubAuthService } from './sw-hub-auth.service';\nimport * as CryptoJS from 'crypto-js';\nfunction jwt(payload) {\n  function base64url(source) {\n    let value = CryptoJS.enc.Base64.stringify(source);\n    value = value.replace(/=+$/, '');\n    value = value.replace(/\\+/g, '-');\n    value = value.replace(/\\//g, '_');\n    return value;\n  }\n  const header = base64url(CryptoJS.enc.Utf8.parse(JSON.stringify({\n    alg: 'HS256',\n    typ: 'JWT'\n  })));\n  const data = base64url(CryptoJS.enc.Utf8.parse(JSON.stringify(payload)));\n  const token = `${header}.${data}`;\n  const signature = base64url(CryptoJS.HmacSHA256(token, ''));\n  return `${token}.${signature}`;\n}\ndescribe('SwHubAuthService', () => {\n  const grantedPermissions = ['disable:entity:game-history:balances', 'disable:keyentity:game-history:balances', 'keyentity:gamecategory:change-ordering', 'keyentity:gamecategory:view', 'user:create'];\n  const token = jwt({\n    userId: '141491',\n    entityId: '126080',\n    username: 'SUPERADMIN',\n    grantedPermissions,\n    exp: Math.floor(Date.now() / 1000) + 60\n  });\n  const expiredToken = jwt({\n    exp: Math.floor(Date.now() / 1000) - 60\n  });\n  console.log(token);\n  console.log(expiredToken);\n  let spectator;\n  const createService = createServiceFactory({\n    service: SwHubAuthService\n  });\n  beforeEach(() => spectator = createService());\n  it('should be created', () => {\n    expect(spectator.service).toBeTruthy();\n  });\n  it('isLogged should return false', () => {\n    expect(spectator.service.isLogged()).toEqual(false);\n  });\n  it('isLogged should return true', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.isLogged()).toEqual(true);\n  });\n  it('isLogged should return false from expired token', () => {\n    spectator.service.setToken(expiredToken);\n    expect(spectator.service.isLogged()).toEqual(false);\n  });\n  it('tokenGetter should return token', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.tokenGetter()()).toEqual(token);\n  });\n  it('should setToken', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.accessToken).toEqual(token);\n    expect(spectator.service.username).toEqual('SUPERADMIN');\n    expect(spectator.service.isSuperAdmin).toEqual(false);\n  });\n  it('should not setToken', () => {\n    spectator.service.setToken(undefined);\n    expect(spectator.service.accessToken).toBeUndefined();\n    expect(spectator.service.entityKey).toBeUndefined();\n    expect(spectator.service.username).toBeUndefined();\n    expect(spectator.service.isSuperAdmin).toEqual(false);\n  });\n  it('should logout', () => {\n    spectator.service.logout();\n    expect(spectator.service.accessToken).toBeUndefined();\n    expect(spectator.service.entityKey).toBeUndefined();\n    expect(spectator.service.username).toBeUndefined();\n    expect(spectator.service.isSuperAdmin).toEqual(false);\n  });\n  it('getGrantedPermissions return grantedPermissions', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.getGrantedPermissions()).toEqual(grantedPermissions);\n  });\n  it('getGrantedPermissions return []', () => {\n    expect(spectator.service.getGrantedPermissions()).toEqual([]);\n  });\n  it('allowedTo should return false', () => {\n    expect(spectator.service.allowedTo([])).toEqual(false);\n  });\n  it('allowedTo should return true', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.allowedTo(grantedPermissions)).toEqual(true);\n  });\n  it('areGranted should return true', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.areGranted(['granted:all'])).toEqual(true);\n  });\n  it('areGranted should return false', () => {\n    expect(spectator.service.areGranted(['granted:all'])).toEqual(false);\n  });\n  it('decode should called from setToken', () => {\n    spyOn(spectator.service, 'decode');\n    spectator.service.setToken(token);\n    expect(spectator.service['decode']).toHaveBeenCalledTimes(1);\n  });\n});", "map": {"version": 3, "names": ["createServiceFactory", "SwHubAuthService", "CryptoJS", "jwt", "payload", "base64url", "source", "value", "enc", "Base64", "stringify", "replace", "header", "Utf8", "parse", "JSON", "alg", "typ", "data", "token", "signature", "HmacSHA256", "describe", "grantedPermissions", "userId", "entityId", "username", "exp", "Math", "floor", "Date", "now", "expiredToken", "console", "log", "spectator", "createService", "service", "beforeEach", "it", "expect", "toBeTruthy", "isLogged", "toEqual", "setToken", "tokenGetter", "accessToken", "isSuperAdmin", "undefined", "toBeUndefined", "entityKey", "logout", "getGrantedPermissions", "allowedTo", "areGranted", "spyOn", "toHaveBeenCalledTimes"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-auth/sw-hub-auth.service.spec.ts"], "sourcesContent": ["import { createServiceFactory, SpectatorService } from '@ngneat/spectator';\nimport { SwHubAuthService } from './sw-hub-auth.service';\nimport * as CryptoJS from 'crypto-js';\n\n\nfunction jwt( payload: { [key: string]: any } ): string {\n  function base64url( source: any ): string {\n    let value = CryptoJS.enc.Base64.stringify(source);\n    value = value.replace(/=+$/, '');\n    value = value.replace(/\\+/g, '-');\n    value = value.replace(/\\//g, '_');\n    return value;\n  }\n\n  const header = base64url(CryptoJS.enc.Utf8.parse(JSON.stringify({\n    alg: 'HS256',\n    typ: 'JWT'\n  })));\n  const data = base64url(CryptoJS.enc.Utf8.parse(JSON.stringify(payload)));\n  const token = `${header}.${data}`;\n  const signature = base64url(CryptoJS.HmacSHA256(token, ''));\n  return `${token}.${signature}`;\n}\n\ndescribe('SwHubAuthService', () => {\n  const grantedPermissions = [\n    'disable:entity:game-history:balances',\n    'disable:keyentity:game-history:balances',\n    'keyentity:gamecategory:change-ordering',\n    'keyentity:gamecategory:view',\n    'user:create'\n  ];\n  const token = jwt({\n    userId: '141491',\n    entityId: '126080',\n    username: 'SUPERADMIN',\n    grantedPermissions,\n    exp: Math.floor(Date.now() / 1000) + 60\n  });\n  const expiredToken = jwt({\n    exp: Math.floor(Date.now() / 1000) - 60\n  });\n  console.log(token);\n  console.log(expiredToken);\n  let spectator: SpectatorService<SwHubAuthService>;\n  const createService = createServiceFactory({\n    service: SwHubAuthService\n  });\n\n  beforeEach(() => spectator = createService());\n\n  it('should be created', () => {\n    expect(spectator.service).toBeTruthy();\n  });\n\n  it('isLogged should return false', () => {\n    expect(spectator.service.isLogged()).toEqual(false);\n  });\n\n  it('isLogged should return true', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.isLogged()).toEqual(true);\n  });\n\n  it('isLogged should return false from expired token', () => {\n    spectator.service.setToken(expiredToken);\n    expect(spectator.service.isLogged()).toEqual(false);\n  });\n\n  it('tokenGetter should return token', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.tokenGetter()()).toEqual(token);\n  });\n\n  it('should setToken', () => {\n    spectator.service.setToken(token);\n\n    expect(spectator.service.accessToken).toEqual(token);\n    expect(spectator.service.username).toEqual('SUPERADMIN');\n    expect(spectator.service.isSuperAdmin).toEqual(false);\n  });\n\n  it('should not setToken', () => {\n    spectator.service.setToken(undefined);\n\n    expect(spectator.service.accessToken).toBeUndefined();\n    expect(spectator.service.entityKey).toBeUndefined();\n    expect(spectator.service.username).toBeUndefined();\n    expect(spectator.service.isSuperAdmin).toEqual(false);\n  });\n\n  it('should logout', () => {\n    spectator.service.logout();\n\n    expect(spectator.service.accessToken).toBeUndefined();\n    expect(spectator.service.entityKey).toBeUndefined();\n    expect(spectator.service.username).toBeUndefined();\n    expect(spectator.service.isSuperAdmin).toEqual(false);\n  });\n\n  it('getGrantedPermissions return grantedPermissions', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.getGrantedPermissions()).toEqual(grantedPermissions);\n  });\n\n  it('getGrantedPermissions return []', () => {\n    expect(spectator.service.getGrantedPermissions()).toEqual([]);\n  });\n\n  it('allowedTo should return false', () => {\n    expect(spectator.service.allowedTo([])).toEqual(false);\n  });\n\n  it('allowedTo should return true', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.allowedTo(grantedPermissions)).toEqual(true);\n  });\n\n  it('areGranted should return true', () => {\n    spectator.service.setToken(token);\n    expect(spectator.service.areGranted(['granted:all'])).toEqual(true);\n  });\n\n  it('areGranted should return false', () => {\n    expect(spectator.service.areGranted(['granted:all'])).toEqual(false);\n  });\n\n  it('decode should called from setToken', () => {\n    spyOn<any>(spectator.service, 'decode');\n    spectator.service.setToken(token);\n    expect(spectator.service['decode']).toHaveBeenCalledTimes(1);\n  });\n});\n"], "mappings": "AAAA,SAASA,oBAAoB,QAA0B,mBAAmB;AAC1E,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,KAAKC,QAAQ,MAAM,WAAW;AAGrC,SAASC,GAAGA,CAAEC,OAA+B;EAC3C,SAASC,SAASA,CAAEC,MAAW;IAC7B,IAAIC,KAAK,GAAGL,QAAQ,CAACM,GAAG,CAACC,MAAM,CAACC,SAAS,CAACJ,MAAM,CAAC;IACjDC,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAChCJ,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACjCJ,KAAK,GAAGA,KAAK,CAACI,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACjC,OAAOJ,KAAK;EACd;EAEA,MAAMK,MAAM,GAAGP,SAAS,CAACH,QAAQ,CAACM,GAAG,CAACK,IAAI,CAACC,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IAC9DM,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE;GACN,CAAC,CAAC,CAAC;EACJ,MAAMC,IAAI,GAAGb,SAAS,CAACH,QAAQ,CAACM,GAAG,CAACK,IAAI,CAACC,KAAK,CAACC,IAAI,CAACL,SAAS,CAACN,OAAO,CAAC,CAAC,CAAC;EACxE,MAAMe,KAAK,GAAG,GAAGP,MAAM,IAAIM,IAAI,EAAE;EACjC,MAAME,SAAS,GAAGf,SAAS,CAACH,QAAQ,CAACmB,UAAU,CAACF,KAAK,EAAE,EAAE,CAAC,CAAC;EAC3D,OAAO,GAAGA,KAAK,IAAIC,SAAS,EAAE;AAChC;AAEAE,QAAQ,CAAC,kBAAkB,EAAE,MAAK;EAChC,MAAMC,kBAAkB,GAAG,CACzB,sCAAsC,EACtC,yCAAyC,EACzC,wCAAwC,EACxC,6BAA6B,EAC7B,aAAa,CACd;EACD,MAAMJ,KAAK,GAAGhB,GAAG,CAAC;IAChBqB,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,YAAY;IACtBH,kBAAkB;IAClBI,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG;GACtC,CAAC;EACF,MAAMC,YAAY,GAAG7B,GAAG,CAAC;IACvBwB,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG;GACtC,CAAC;EACFE,OAAO,CAACC,GAAG,CAACf,KAAK,CAAC;EAClBc,OAAO,CAACC,GAAG,CAACF,YAAY,CAAC;EACzB,IAAIG,SAA6C;EACjD,MAAMC,aAAa,GAAGpC,oBAAoB,CAAC;IACzCqC,OAAO,EAAEpC;GACV,CAAC;EAEFqC,UAAU,CAAC,MAAMH,SAAS,GAAGC,aAAa,EAAE,CAAC;EAE7CG,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACL,SAAS,CAACE,OAAO,CAAC,CAACI,UAAU,EAAE;EACxC,CAAC,CAAC;EAEFF,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtCC,MAAM,CAACL,SAAS,CAACE,OAAO,CAACK,QAAQ,EAAE,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC;EACrD,CAAC,CAAC;EAEFJ,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrCJ,SAAS,CAACE,OAAO,CAACO,QAAQ,CAACzB,KAAK,CAAC;IACjCqB,MAAM,CAACL,SAAS,CAACE,OAAO,CAACK,QAAQ,EAAE,CAAC,CAACC,OAAO,CAAC,IAAI,CAAC;EACpD,CAAC,CAAC;EAEFJ,EAAE,CAAC,iDAAiD,EAAE,MAAK;IACzDJ,SAAS,CAACE,OAAO,CAACO,QAAQ,CAACZ,YAAY,CAAC;IACxCQ,MAAM,CAACL,SAAS,CAACE,OAAO,CAACK,QAAQ,EAAE,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC;EACrD,CAAC,CAAC;EAEFJ,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzCJ,SAAS,CAACE,OAAO,CAACO,QAAQ,CAACzB,KAAK,CAAC;IACjCqB,MAAM,CAACL,SAAS,CAACE,OAAO,CAACQ,WAAW,EAAE,EAAE,CAAC,CAACF,OAAO,CAACxB,KAAK,CAAC;EAC1D,CAAC,CAAC;EAEFoB,EAAE,CAAC,iBAAiB,EAAE,MAAK;IACzBJ,SAAS,CAACE,OAAO,CAACO,QAAQ,CAACzB,KAAK,CAAC;IAEjCqB,MAAM,CAACL,SAAS,CAACE,OAAO,CAACS,WAAW,CAAC,CAACH,OAAO,CAACxB,KAAK,CAAC;IACpDqB,MAAM,CAACL,SAAS,CAACE,OAAO,CAACX,QAAQ,CAAC,CAACiB,OAAO,CAAC,YAAY,CAAC;IACxDH,MAAM,CAACL,SAAS,CAACE,OAAO,CAACU,YAAY,CAAC,CAACJ,OAAO,CAAC,KAAK,CAAC;EACvD,CAAC,CAAC;EAEFJ,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7BJ,SAAS,CAACE,OAAO,CAACO,QAAQ,CAACI,SAAS,CAAC;IAErCR,MAAM,CAACL,SAAS,CAACE,OAAO,CAACS,WAAW,CAAC,CAACG,aAAa,EAAE;IACrDT,MAAM,CAACL,SAAS,CAACE,OAAO,CAACa,SAAS,CAAC,CAACD,aAAa,EAAE;IACnDT,MAAM,CAACL,SAAS,CAACE,OAAO,CAACX,QAAQ,CAAC,CAACuB,aAAa,EAAE;IAClDT,MAAM,CAACL,SAAS,CAACE,OAAO,CAACU,YAAY,CAAC,CAACJ,OAAO,CAAC,KAAK,CAAC;EACvD,CAAC,CAAC;EAEFJ,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBJ,SAAS,CAACE,OAAO,CAACc,MAAM,EAAE;IAE1BX,MAAM,CAACL,SAAS,CAACE,OAAO,CAACS,WAAW,CAAC,CAACG,aAAa,EAAE;IACrDT,MAAM,CAACL,SAAS,CAACE,OAAO,CAACa,SAAS,CAAC,CAACD,aAAa,EAAE;IACnDT,MAAM,CAACL,SAAS,CAACE,OAAO,CAACX,QAAQ,CAAC,CAACuB,aAAa,EAAE;IAClDT,MAAM,CAACL,SAAS,CAACE,OAAO,CAACU,YAAY,CAAC,CAACJ,OAAO,CAAC,KAAK,CAAC;EACvD,CAAC,CAAC;EAEFJ,EAAE,CAAC,iDAAiD,EAAE,MAAK;IACzDJ,SAAS,CAACE,OAAO,CAACO,QAAQ,CAACzB,KAAK,CAAC;IACjCqB,MAAM,CAACL,SAAS,CAACE,OAAO,CAACe,qBAAqB,EAAE,CAAC,CAACT,OAAO,CAACpB,kBAAkB,CAAC;EAC/E,CAAC,CAAC;EAEFgB,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzCC,MAAM,CAACL,SAAS,CAACE,OAAO,CAACe,qBAAqB,EAAE,CAAC,CAACT,OAAO,CAAC,EAAE,CAAC;EAC/D,CAAC,CAAC;EAEFJ,EAAE,CAAC,+BAA+B,EAAE,MAAK;IACvCC,MAAM,CAACL,SAAS,CAACE,OAAO,CAACgB,SAAS,CAAC,EAAE,CAAC,CAAC,CAACV,OAAO,CAAC,KAAK,CAAC;EACxD,CAAC,CAAC;EAEFJ,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtCJ,SAAS,CAACE,OAAO,CAACO,QAAQ,CAACzB,KAAK,CAAC;IACjCqB,MAAM,CAACL,SAAS,CAACE,OAAO,CAACgB,SAAS,CAAC9B,kBAAkB,CAAC,CAAC,CAACoB,OAAO,CAAC,IAAI,CAAC;EACvE,CAAC,CAAC;EAEFJ,EAAE,CAAC,+BAA+B,EAAE,MAAK;IACvCJ,SAAS,CAACE,OAAO,CAACO,QAAQ,CAACzB,KAAK,CAAC;IACjCqB,MAAM,CAACL,SAAS,CAACE,OAAO,CAACiB,UAAU,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAACX,OAAO,CAAC,IAAI,CAAC;EACrE,CAAC,CAAC;EAEFJ,EAAE,CAAC,gCAAgC,EAAE,MAAK;IACxCC,MAAM,CAACL,SAAS,CAACE,OAAO,CAACiB,UAAU,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAACX,OAAO,CAAC,KAAK,CAAC;EACtE,CAAC,CAAC;EAEFJ,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CgB,KAAK,CAAMpB,SAAS,CAACE,OAAO,EAAE,QAAQ,CAAC;IACvCF,SAAS,CAACE,OAAO,CAACO,QAAQ,CAACzB,KAAK,CAAC;IACjCqB,MAAM,CAACL,SAAS,CAACE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAACmB,qBAAqB,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}