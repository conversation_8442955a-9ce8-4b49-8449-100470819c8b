{"ast": null, "code": "var _SwuiMenuComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-menu.component.scss?ngResource\";\nimport { Component, Input, ViewChild } from '@angular/core';\nimport { NavigationEnd, Router } from '@angular/router';\nimport { NestedTreeControl } from '@angular/cdk/tree';\nimport { MatTreeNestedDataSource } from '@angular/material/tree';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nlet SwuiMenuComponent = (_SwuiMenuComponent = class SwuiMenuComponent {\n  get isSidebarCollapsed() {\n    return this._isSidebarCollapsed;\n  }\n  set isSidebarCollapsed(value) {\n    this._isSidebarCollapsed = value;\n    if (!value && this.currentParent) {\n      this.treeControl.expand(this.currentParent);\n    } else {\n      this.collapseAll();\n    }\n  }\n  get isSidebarHovered() {\n    return this._isSidebarHovered;\n  }\n  set isSidebarHovered(value) {\n    this._isSidebarHovered = value;\n    if (this.isSidebarCollapsed && !value) {\n      this.collapseAll();\n    } else if (this.currentParent) {\n      this.treeControl.expand(this.currentParent);\n    }\n  }\n  set items(items) {\n    if (!items) {\n      return;\n    }\n    this.loading = false;\n    this.dataSource.data = items;\n    this.expandParentItem();\n  }\n  constructor(router) {\n    this.router = router;\n    this.loading = true;\n    this.loadingArray = Array;\n    this.treeControl = new NestedTreeControl(item => item.children);\n    this.dataSource = new MatTreeNestedDataSource();\n    this._isSidebarCollapsed = false;\n    this._isSidebarHovered = false;\n    this.destroy$ = new Subject();\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe(event => {\n      const item = this.dataSource.data.find(({\n        url\n      }) => event.url.indexOf(url) !== -1);\n      if (item && this.currentParent !== item) {\n        var _item$children;\n        if ((_item$children = item.children) !== null && _item$children !== void 0 && _item$children.length) {\n          this.onParentClick(item);\n        } else {\n          this.onItemClick(item);\n        }\n        this.expandParentItem();\n      }\n    });\n  }\n  ngOnInit() {\n    if (this.menuRef) {\n      this.menuRef.nativeElement.style.setProperty('--active-color', this.activeColor || '#ffffff');\n    }\n  }\n  ngOnDestroy() {\n    if (this.menuRef) {\n      this.menuRef.nativeElement.style.removeProperty('--active-color');\n    }\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n  hasChild(_, item) {\n    return !!item.children && item.children.length > 0;\n  }\n  collapseAll() {\n    this.treeControl.collapseAll();\n  }\n  onItemClick(item) {\n    this.currentParent = this.getParentMenuItem(item.url);\n    this.collapseOther(this.currentParent);\n  }\n  onParentClick(item) {\n    this.treeControl.expand(item);\n    this.collapseOther(item);\n  }\n  collapseOther(item) {\n    this.dataSource.data.forEach(el => {\n      if (el !== item && el.children) {\n        this.treeControl.collapse(el);\n      }\n    });\n  }\n  getParentMenuItem(url) {\n    return this.dataSource.data.find(item => {\n      if (item.children && item.children.length > 0) {\n        return item.children.some(childItem => url.includes(childItem.url));\n      } else {\n        return item.url === url;\n      }\n    });\n  }\n  expandParentItem() {\n    this.currentParent = this.getParentMenuItem(this.router.url);\n    if (this.currentParent && !this.isSidebarCollapsed) {\n      this.treeControl.expand(this.currentParent);\n    }\n  }\n}, _SwuiMenuComponent.ctorParameters = () => [{\n  type: Router\n}], _SwuiMenuComponent.propDecorators = {\n  activeColor: [{\n    type: Input\n  }],\n  isSidebarCollapsed: [{\n    type: Input\n  }],\n  isSidebarHovered: [{\n    type: Input\n  }],\n  items: [{\n    type: Input\n  }],\n  menuRef: [{\n    type: ViewChild,\n    args: ['menu', {\n      static: true\n    }]\n  }]\n}, _SwuiMenuComponent);\nSwuiMenuComponent = __decorate([Component({\n  selector: 'lib-swui-menu',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiMenuComponent);\nexport { SwuiMenuComponent };", "map": {"version": 3, "names": ["Component", "Input", "ViewChild", "NavigationEnd", "Router", "NestedTreeControl", "MatTreeNestedDataSource", "Subject", "filter", "takeUntil", "SwuiMenuComponent", "_SwuiMenuComponent", "isSidebarCollapsed", "_isSidebarCollapsed", "value", "currentParent", "treeControl", "expand", "collapseAll", "isSidebarHovered", "_isSidebarHovered", "items", "loading", "dataSource", "data", "expandParentItem", "constructor", "router", "loadingArray", "Array", "item", "children", "destroy$", "events", "pipe", "event", "subscribe", "find", "url", "indexOf", "_item$children", "length", "onParentClick", "onItemClick", "ngOnInit", "menuRef", "nativeElement", "style", "setProperty", "activeColor", "ngOnDestroy", "removeProperty", "next", "undefined", "complete", "<PERSON><PERSON><PERSON><PERSON>", "_", "getParentMenuItem", "collapseOther", "for<PERSON>ach", "el", "collapse", "some", "childItem", "includes", "args", "static", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-menu/swui-menu.component.ts"], "sourcesContent": ["import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';\nimport { NavigationEnd, Router } from '@angular/router';\nimport { NestedTreeControl } from '@angular/cdk/tree';\nimport { MatTreeNestedDataSource } from '@angular/material/tree';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\n\nimport { SwuiMenuItem } from './swui-menu.interface';\n\n\n@Component({\n    selector: 'lib-swui-menu',\n    templateUrl: './swui-menu.component.html',\n    styleUrls: ['./swui-menu.component.scss'],\n    standalone: false\n})\nexport class SwuiMenuComponent implements OnInit, OnDestroy {\n  @Input() activeColor?: string;\n\n  @Input()\n  get isSidebarCollapsed(): boolean {\n    return this._isSidebarCollapsed;\n  }\n\n  set isSidebarCollapsed( value: boolean ) {\n    this._isSidebarCollapsed = value;\n    if (!value && this.currentParent) {\n      this.treeControl.expand(this.currentParent);\n    } else {\n      this.collapseAll();\n    }\n  }\n\n  @Input()\n  get isSidebarHovered(): boolean {\n    return this._isSidebarHovered;\n  }\n\n  set isSidebarHovered( value: boolean ) {\n    this._isSidebarHovered = value;\n    if (this.isSidebarCollapsed && !value) {\n      this.collapseAll();\n    } else if (this.currentParent) {\n      this.treeControl.expand(this.currentParent);\n    }\n  }\n\n  @Input()\n  set items( items: SwuiMenuItem[] ) {\n    if (!items) {\n      return;\n    }\n    this.loading = false;\n    this.dataSource.data = items;\n    this.expandParentItem();\n  }\n\n  loading = true;\n  loadingArray = Array;\n  treeControl = new NestedTreeControl<SwuiMenuItem>(item => item.children);\n  dataSource = new MatTreeNestedDataSource<SwuiMenuItem>();\n  currentParent: SwuiMenuItem | undefined;\n\n  @ViewChild('menu', { static: true }) menuRef: ElementRef | undefined;\n\n  private _isSidebarCollapsed = false;\n  private _isSidebarHovered = false;\n  private destroy$ = new Subject();\n\n  constructor( private router: Router ) {\n    this.router.events\n      .pipe(\n        filter(event => event instanceof NavigationEnd),\n        takeUntil(this.destroy$),\n      ).subscribe(( event: any ) => {\n      const item = this.dataSource.data.find(( { url } ) => event.url.indexOf(url) !== -1);\n\n      if (item && this.currentParent !== item) {\n        if (item.children?.length){\n          this.onParentClick(item);\n        } else {\n          this.onItemClick(item);\n        }\n\n        this.expandParentItem();\n      }\n    });\n  }\n\n  ngOnInit(): void {\n    if (this.menuRef) {\n      this.menuRef.nativeElement.style.setProperty('--active-color', this.activeColor || '#ffffff');\n    }\n  }\n\n  ngOnDestroy(): void {\n    if (this.menuRef) {\n      this.menuRef.nativeElement.style.removeProperty('--active-color');\n    }\n\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n\n  hasChild( _: number, item: SwuiMenuItem ) {\n    return !!item.children && item.children.length > 0;\n  }\n\n  collapseAll() {\n    this.treeControl.collapseAll();\n  }\n\n  onItemClick( item: SwuiMenuItem ) {\n    this.currentParent = this.getParentMenuItem(item.url);\n    this.collapseOther(this.currentParent);\n  }\n\n  onParentClick( item: SwuiMenuItem ) {\n    this.treeControl.expand(item);\n    this.collapseOther(item);\n  }\n\n  private collapseOther( item: SwuiMenuItem | undefined ) {\n    this.dataSource.data.forEach(( el: SwuiMenuItem ) => {\n      if ((el !== item) && el.children) {\n        this.treeControl.collapse(el);\n      }\n    });\n  }\n\n  private getParentMenuItem( url: string ): SwuiMenuItem | undefined {\n    return this.dataSource.data.find(( item: SwuiMenuItem ) => {\n      if (item.children && item.children.length > 0) {\n        return item.children.some(( childItem: SwuiMenuItem ) => url.includes(childItem.url));\n      } else {\n        return item.url === url;\n      }\n    });\n  }\n\n  private expandParentItem() {\n    this.currentParent = this.getParentMenuItem(this.router.url);\n    if (this.currentParent && !this.isSidebarCollapsed) {\n      this.treeControl.expand(this.currentParent);\n    }\n  }\n\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAcC,KAAK,EAAqBC,SAAS,QAAQ,eAAe;AAC1F,SAASC,aAAa,EAAEC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAW3C,IAAMC,iBAAiB,IAAAC,kBAAA,GAAvB,MAAMD,iBAAiB;MAIxBE,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACC,mBAAmB;EACjC;EAEA,IAAID,kBAAkBA,CAAEE,KAAc;IACpC,IAAI,CAACD,mBAAmB,GAAGC,KAAK;IAChC,IAAI,CAACA,KAAK,IAAI,IAAI,CAACC,aAAa,EAAE;MAChC,IAAI,CAACC,WAAW,CAACC,MAAM,CAAC,IAAI,CAACF,aAAa,CAAC;IAC7C,CAAC,MAAM;MACL,IAAI,CAACG,WAAW,EAAE;IACpB;EACF;MAGIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,iBAAiB;EAC/B;EAEA,IAAID,gBAAgBA,CAAEL,KAAc;IAClC,IAAI,CAACM,iBAAiB,GAAGN,KAAK;IAC9B,IAAI,IAAI,CAACF,kBAAkB,IAAI,CAACE,KAAK,EAAE;MACrC,IAAI,CAACI,WAAW,EAAE;IACpB,CAAC,MAAM,IAAI,IAAI,CAACH,aAAa,EAAE;MAC7B,IAAI,CAACC,WAAW,CAACC,MAAM,CAAC,IAAI,CAACF,aAAa,CAAC;IAC7C;EACF;MAGIM,KAAKA,CAAEA,KAAqB;IAC9B,IAAI,CAACA,KAAK,EAAE;MACV;IACF;IACA,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,UAAU,CAACC,IAAI,GAAGH,KAAK;IAC5B,IAAI,CAACI,gBAAgB,EAAE;EACzB;EAcAC,YAAqBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAZ3B,KAAAL,OAAO,GAAG,IAAI;IACd,KAAAM,YAAY,GAAGC,KAAK;IACpB,KAAAb,WAAW,GAAG,IAAIX,iBAAiB,CAAeyB,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;IACxE,KAAAR,UAAU,GAAG,IAAIjB,uBAAuB,EAAgB;IAKhD,KAAAO,mBAAmB,GAAG,KAAK;IAC3B,KAAAO,iBAAiB,GAAG,KAAK;IACzB,KAAAY,QAAQ,GAAG,IAAIzB,OAAO,EAAE;IAG9B,IAAI,CAACoB,MAAM,CAACM,MAAM,CACfC,IAAI,CACH1B,MAAM,CAAC2B,KAAK,IAAIA,KAAK,YAAYhC,aAAa,CAAC,EAC/CM,SAAS,CAAC,IAAI,CAACuB,QAAQ,CAAC,CACzB,CAACI,SAAS,CAAGD,KAAU,IAAK;MAC7B,MAAML,IAAI,GAAG,IAAI,CAACP,UAAU,CAACC,IAAI,CAACa,IAAI,CAAC,CAAE;QAAEC;MAAG,CAAE,KAAMH,KAAK,CAACG,GAAG,CAACC,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;MAEpF,IAAIR,IAAI,IAAI,IAAI,CAACf,aAAa,KAAKe,IAAI,EAAE;QAAA,IAAAU,cAAA;QACvC,KAAAA,cAAA,GAAIV,IAAI,CAACC,QAAQ,cAAAS,cAAA,eAAbA,cAAA,CAAeC,MAAM,EAAC;UACxB,IAAI,CAACC,aAAa,CAACZ,IAAI,CAAC;QAC1B,CAAC,MAAM;UACL,IAAI,CAACa,WAAW,CAACb,IAAI,CAAC;QACxB;QAEA,IAAI,CAACL,gBAAgB,EAAE;MACzB;IACF,CAAC,CAAC;EACJ;EAEAmB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,aAAa,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAACC,WAAW,IAAI,SAAS,CAAC;IAC/F;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACL,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,aAAa,CAACC,KAAK,CAACI,cAAc,CAAC,gBAAgB,CAAC;IACnE;IAEA,IAAI,CAACnB,QAAQ,CAACoB,IAAI,CAACC,SAAS,CAAC;IAC7B,IAAI,CAACrB,QAAQ,CAACsB,QAAQ,EAAE;EAC1B;EAEAC,QAAQA,CAAEC,CAAS,EAAE1B,IAAkB;IACrC,OAAO,CAAC,CAACA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,CAACU,MAAM,GAAG,CAAC;EACpD;EAEAvB,WAAWA,CAAA;IACT,IAAI,CAACF,WAAW,CAACE,WAAW,EAAE;EAChC;EAEAyB,WAAWA,CAAEb,IAAkB;IAC7B,IAAI,CAACf,aAAa,GAAG,IAAI,CAAC0C,iBAAiB,CAAC3B,IAAI,CAACQ,GAAG,CAAC;IACrD,IAAI,CAACoB,aAAa,CAAC,IAAI,CAAC3C,aAAa,CAAC;EACxC;EAEA2B,aAAaA,CAAEZ,IAAkB;IAC/B,IAAI,CAACd,WAAW,CAACC,MAAM,CAACa,IAAI,CAAC;IAC7B,IAAI,CAAC4B,aAAa,CAAC5B,IAAI,CAAC;EAC1B;EAEQ4B,aAAaA,CAAE5B,IAA8B;IACnD,IAAI,CAACP,UAAU,CAACC,IAAI,CAACmC,OAAO,CAAGC,EAAgB,IAAK;MAClD,IAAKA,EAAE,KAAK9B,IAAI,IAAK8B,EAAE,CAAC7B,QAAQ,EAAE;QAChC,IAAI,CAACf,WAAW,CAAC6C,QAAQ,CAACD,EAAE,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ;EAEQH,iBAAiBA,CAAEnB,GAAW;IACpC,OAAO,IAAI,CAACf,UAAU,CAACC,IAAI,CAACa,IAAI,CAAGP,IAAkB,IAAK;MACxD,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,CAACU,MAAM,GAAG,CAAC,EAAE;QAC7C,OAAOX,IAAI,CAACC,QAAQ,CAAC+B,IAAI,CAAGC,SAAuB,IAAMzB,GAAG,CAAC0B,QAAQ,CAACD,SAAS,CAACzB,GAAG,CAAC,CAAC;MACvF,CAAC,MAAM;QACL,OAAOR,IAAI,CAACQ,GAAG,KAAKA,GAAG;MACzB;IACF,CAAC,CAAC;EACJ;EAEQb,gBAAgBA,CAAA;IACtB,IAAI,CAACV,aAAa,GAAG,IAAI,CAAC0C,iBAAiB,CAAC,IAAI,CAAC9B,MAAM,CAACW,GAAG,CAAC;IAC5D,IAAI,IAAI,CAACvB,aAAa,IAAI,CAAC,IAAI,CAACH,kBAAkB,EAAE;MAClD,IAAI,CAACI,WAAW,CAACC,MAAM,CAAC,IAAI,CAACF,aAAa,CAAC;IAC7C;EACF;;;;;UAhICd;EAAK;;UAELA;EAAK;;UAcLA;EAAK;;UAcLA;EAAK;;UAgBLC,SAAS;IAAA+D,IAAA,GAAC,MAAM,EAAE;MAAEC,MAAM,EAAE;IAAI,CAAE;EAAA;;AA/CxBxD,iBAAiB,GAAAyD,UAAA,EAN7BnE,SAAS,CAAC;EACPoE,QAAQ,EAAE,eAAe;EACzBC,QAAA,EAAAC,oBAAyC;EAEzCC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACW7D,iBAAiB,CAmI7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}