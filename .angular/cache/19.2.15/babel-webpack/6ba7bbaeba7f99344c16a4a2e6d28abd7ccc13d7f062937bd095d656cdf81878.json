{"ast": null, "code": "import { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { TestBed, waitForAsync } from '@angular/core/testing';\nimport { SwuiSidebarComponent } from './swui-sidebar.component';\nimport { SwuiSidebarService } from './swui-sidebar.service';\ndescribe('SwuiSidebarComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SwuiSidebarComponent],\n      schemas: [NO_ERRORS_SCHEMA],\n      providers: [SwuiSidebarService]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiSidebarComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["NO_ERRORS_SCHEMA", "TestBed", "waitForAsync", "SwuiSidebarComponent", "SwuiSidebarService", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "schemas", "providers", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-sidebar/swui-sidebar.component.spec.ts"], "sourcesContent": ["import { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\n\nimport { SwuiSidebarComponent } from './swui-sidebar.component';\nimport { SwuiSidebarService } from './swui-sidebar.service';\n\n\ndescribe('SwuiSidebarComponent', () => {\n  let component: SwuiSidebarComponent;\n  let fixture: ComponentFixture<SwuiSidebarComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SwuiSidebarComponent],\n      schemas: [NO_ERRORS_SCHEMA],\n      providers: [SwuiSidebarService],\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiSidebarComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,eAAe;AAChD,SAA2BC,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAG3DC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;EACpC,IAAIC,SAA+B;EACnC,IAAIC,OAA+C;EAEnDC,UAAU,CAACN,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACQ,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACP,oBAAoB,CAAC;MACpCQ,OAAO,EAAE,CAACX,gBAAgB,CAAC;MAC3BY,SAAS,EAAE,CAACR,kBAAkB;KAC/B,CAAC,CACCS,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHL,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGN,OAAO,CAACa,eAAe,CAACX,oBAAoB,CAAC;IACvDG,SAAS,GAAGC,OAAO,CAACQ,iBAAiB;IACrCR,OAAO,CAACS,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACZ,SAAS,CAAC,CAACa,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}