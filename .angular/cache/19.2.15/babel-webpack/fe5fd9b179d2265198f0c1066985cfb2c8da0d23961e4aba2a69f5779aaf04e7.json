{"ast": null, "code": "/* tslint:disable:quotemark */\nexport const labels = [{\n  \"id\": \"W4RkGRen\",\n  \"title\": \"slot\",\n  \"group\": \"class\"\n}, {\n  \"id\": \"v5R2nRkE\",\n  \"title\": \"fish game\",\n  \"group\": \"class\"\n}, {\n  \"id\": \"kx3o8qeK\",\n  \"title\": \"table game\",\n  \"group\": \"class\"\n}, {\n  \"id\": \"4DR4LR1K\",\n  \"title\": \"roulette\",\n  \"group\": \"class\"\n}, {\n  \"id\": \"JyRgY3XZ\",\n  \"title\": \"live\",\n  \"group\": \"class\"\n}, {\n  \"id\": \"okB7nR6J\",\n  \"title\": \"card\",\n  \"group\": \"class\"\n}, {\n  \"id\": \"NlqP5RZj\",\n  \"title\": \"arcade\",\n  \"group\": \"class\"\n}, {\n  \"id\": \"p2qOeBEP\",\n  \"title\": \"mini game\",\n  \"group\": \"class\"\n}, {\n  \"id\": \"zx3Ybq1v\",\n  \"title\": \"html5\",\n  \"group\": \"platform\"\n}, {\n  \"id\": \"z9R67BjP\",\n  \"title\": \"flash\",\n  \"group\": \"platform\"\n}, {\n  \"id\": \"zbqLpq7A\",\n  \"title\": \"downloadable\",\n  \"group\": \"platform\"\n}, {\n  \"id\": \"1G3jaBXk\",\n  \"title\": \"progressive\",\n  \"group\": \"feature\"\n}, {\n  \"id\": \"pQ3513OE\",\n  \"title\": \"jackpot\",\n  \"group\": \"feature\"\n}, {\n  \"id\": \"D7q1QqZv\",\n  \"title\": \"branded\",\n  \"group\": \"feature\"\n}, {\n  \"id\": \"g5BJXq9r\",\n  \"title\": \"html\",\n  \"group\": \"platform\"\n}, {\n  \"id\": \"QX3xOBaJ\",\n  \"title\": \"BRANDED\",\n  \"group\": \"feature\"\n}, {\n  \"id\": \"1A3nPRQ7\",\n  \"title\": \"BRANDEDв\",\n  \"group\": \"feature\"\n}, {\n  \"id\": \"n0Bd6qkY\",\n  \"title\": \"self-owned\",\n  \"group\": \"feature\"\n}, {\n  \"id\": \"voRVgBkp\",\n  \"title\": \"test\",\n  \"group\": \"platform\"\n}, {\n  \"id\": \"4DR4aLB1\",\n  \"title\": \"html51\",\n  \"group\": \"platform\"\n}, {\n  \"id\": \"wYRD5BbO\",\n  \"title\": \"GP-test\",\n  \"group\": \"platform\"\n}];", "map": {"version": 3, "names": ["labels"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/.storybook/game-labels.data.ts"], "sourcesContent": ["/* tslint:disable:quotemark */\nexport const labels = [\n    {\n        \"id\": \"W4RkGRen\",\n        \"title\": \"slot\",\n        \"group\": \"class\"\n    },\n    {\n        \"id\": \"v5R2nRkE\",\n        \"title\": \"fish game\",\n        \"group\": \"class\"\n    },\n    {\n        \"id\": \"kx3o8qeK\",\n        \"title\": \"table game\",\n        \"group\": \"class\"\n    },\n    {\n        \"id\": \"4DR4LR1K\",\n        \"title\": \"roulette\",\n        \"group\": \"class\"\n    },\n    {\n        \"id\": \"JyRgY3XZ\",\n        \"title\": \"live\",\n        \"group\": \"class\"\n    },\n    {\n        \"id\": \"okB7nR6J\",\n        \"title\": \"card\",\n        \"group\": \"class\"\n    },\n    {\n        \"id\": \"NlqP5RZj\",\n        \"title\": \"arcade\",\n        \"group\": \"class\"\n    },\n    {\n        \"id\": \"p2qOeBEP\",\n        \"title\": \"mini game\",\n        \"group\": \"class\"\n    },\n    {\n        \"id\": \"zx3Ybq1v\",\n        \"title\": \"html5\",\n        \"group\": \"platform\"\n    },\n    {\n        \"id\": \"z9R67BjP\",\n        \"title\": \"flash\",\n        \"group\": \"platform\"\n    },\n    {\n        \"id\": \"zbqLpq7A\",\n        \"title\": \"downloadable\",\n        \"group\": \"platform\"\n    },\n    {\n        \"id\": \"1G3jaBXk\",\n        \"title\": \"progressive\",\n        \"group\": \"feature\"\n    },\n    {\n        \"id\": \"pQ3513OE\",\n        \"title\": \"jackpot\",\n        \"group\": \"feature\"\n    },\n    {\n        \"id\": \"D7q1QqZv\",\n        \"title\": \"branded\",\n        \"group\": \"feature\"\n    },\n    {\n        \"id\": \"g5BJXq9r\",\n        \"title\": \"html\",\n        \"group\": \"platform\"\n    },\n    {\n        \"id\": \"QX3xOBaJ\",\n        \"title\": \"BRANDED\",\n        \"group\": \"feature\"\n    },\n    {\n        \"id\": \"1A3nPRQ7\",\n        \"title\": \"BRANDEDв\",\n        \"group\": \"feature\"\n    },\n    {\n        \"id\": \"n0Bd6qkY\",\n        \"title\": \"self-owned\",\n        \"group\": \"feature\"\n    },\n    {\n        \"id\": \"voRVgBkp\",\n        \"title\": \"test\",\n        \"group\": \"platform\"\n    },\n    {\n        \"id\": \"4DR4aLB1\",\n        \"title\": \"html51\",\n        \"group\": \"platform\"\n    },\n    {\n        \"id\": \"wYRD5BbO\",\n        \"title\": \"GP-test\",\n        \"group\": \"platform\"\n    }\n];\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,MAAM,GAAG,CAClB;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,MAAM;EACf,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,WAAW;EACpB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,YAAY;EACrB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,UAAU;EACnB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,MAAM;EACf,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,MAAM;EACf,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,QAAQ;EACjB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,WAAW;EACpB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,OAAO;EAChB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,OAAO;EAChB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,cAAc;EACvB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,aAAa;EACtB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,SAAS;EAClB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,SAAS;EAClB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,MAAM;EACf,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,SAAS;EAClB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,UAAU;EACnB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,YAAY;EACrB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,MAAM;EACf,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,QAAQ;EACjB,OAAO,EAAE;AACb,CAAC,EACD;EACI,IAAI,EAAE,UAAU;EAChB,OAAO,EAAE,SAAS;EAClB,OAAO,EAAE;AACb,CAAC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}