{"ast": null, "code": "export var SchemaFilterMatchEnum;\n(function (SchemaFilterMatchEnum) {\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"Equals\"] = 0] = \"Equals\";\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"Contains\"] = 1] = \"Contains\";\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"NotContains\"] = 2] = \"NotContains\";\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"GreaterThan\"] = 3] = \"GreaterThan\";\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"LessThan\"] = 4] = \"LessThan\";\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"In\"] = 5] = \"In\";\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"GreaterThanEquals\"] = 6] = \"GreaterThanEquals\";\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"LessThanEquals\"] = 7] = \"LessThanEquals\";\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"Ne\"] = 8] = \"Ne\";\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"Text\"] = 9] = \"Text\";\n  SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"Fields\"] = 10] = \"Fields\";\n})(SchemaFilterMatchEnum || (SchemaFilterMatchEnum = {}));", "map": {"version": 3, "names": ["SchemaFilterMatchEnum"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-top-filter/swui-schema-top-filter.model.ts"], "sourcesContent": ["export var SchemaFilterMatchEnum;\n(function (SchemaFilterMatchEnum) {\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"Equals\"] = 0] = \"Equals\";\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"Contains\"] = 1] = \"Contains\";\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"NotContains\"] = 2] = \"NotContains\";\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"GreaterThan\"] = 3] = \"GreaterThan\";\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"LessThan\"] = 4] = \"LessThan\";\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"In\"] = 5] = \"In\";\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"GreaterThanEquals\"] = 6] = \"GreaterThanEquals\";\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"LessThanEquals\"] = 7] = \"LessThanEquals\";\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"Ne\"] = 8] = \"Ne\";\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"Text\"] = 9] = \"Text\";\n    SchemaFilterMatchEnum[SchemaFilterMatchEnum[\"Fields\"] = 10] = \"Fields\";\n})(SchemaFilterMatchEnum || (SchemaFilterMatchEnum = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,qBAAqB;AAChC,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAACA,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACrEA,qBAAqB,CAACA,qBAAqB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzEA,qBAAqB,CAACA,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC/EA,qBAAqB,CAACA,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC/EA,qBAAqB,CAACA,qBAAqB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzEA,qBAAqB,CAACA,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EAC7DA,qBAAqB,CAACA,qBAAqB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;EAC3FA,qBAAqB,CAACA,qBAAqB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACrFA,qBAAqB,CAACA,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EAC7DA,qBAAqB,CAACA,qBAAqB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjEA,qBAAqB,CAACA,qBAAqB,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,QAAQ;AAC1E,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}