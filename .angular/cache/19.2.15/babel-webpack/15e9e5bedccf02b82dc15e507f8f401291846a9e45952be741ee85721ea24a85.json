{"ast": null, "code": "import { InjectionToken } from '@angular/core';\nexport const SWUI_GRID_WIDGET_CONFIG = new InjectionToken('grid-widget-config');", "map": {"version": 3, "names": ["InjectionToken", "SWUI_GRID_WIDGET_CONFIG"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid.model.ts"], "sourcesContent": ["import { InjectionToken } from '@angular/core';\nexport const SWUI_GRID_WIDGET_CONFIG = new InjectionToken('grid-widget-config');\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,eAAe;AAC9C,OAAO,MAAMC,uBAAuB,GAAG,IAAID,cAAc,CAAC,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}