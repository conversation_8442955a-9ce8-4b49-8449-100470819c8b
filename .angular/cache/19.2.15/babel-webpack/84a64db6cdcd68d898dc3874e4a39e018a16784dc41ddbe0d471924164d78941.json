{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CustomPeriodModule } from './custom-period/custom-period.module';\nimport { SwuiDatetimepickerModule } from '../swui-datetimepicker/swui-datetimepicker.module';\nimport { SwuiDateTimeRangeComponent } from './swui-date-time-range.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const DATE_TIME_RANGE_MODULES = [TranslateModule.forChild(), MatFormFieldModule, MatIconModule, MatButtonModule, MatInputModule, MatRippleModule, SwuiDatetimepickerModule, CustomPeriodModule, ReactiveFormsModule];\nlet SwuiDateTimeRangeModule = class SwuiDateTimeRangeModule {};\nSwuiDateTimeRangeModule = __decorate([NgModule({\n  imports: [CommonModule, ...DATE_TIME_RANGE_MODULES],\n  exports: [SwuiDateTimeRangeComponent],\n  declarations: [SwuiDateTimeRangeComponent]\n})], SwuiDateTimeRangeModule);\nexport { SwuiDateTimeRangeModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ReactiveFormsModule", "TranslateModule", "CustomPeriodModule", "SwuiDatetimepickerModule", "SwuiDateTimeRangeComponent", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatButtonModule", "MatRippleModule", "DATE_TIME_RANGE_MODULES", "<PERSON><PERSON><PERSON><PERSON>", "SwuiDateTimeRangeModule", "__decorate", "imports", "exports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-range/swui-date-time-range.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { CustomPeriodModule } from './custom-period/custom-period.module';\nimport { SwuiDatetimepickerModule } from '../swui-datetimepicker/swui-datetimepicker.module';\nimport { SwuiDateTimeRangeComponent } from './swui-date-time-range.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\n\n\nexport const DATE_TIME_RANGE_MODULES = [\n  TranslateModule.forChild(),\n  MatFormFieldModule,\n  MatIconModule,\n  MatButtonModule,\n  MatInputModule,\n  MatRippleModule,\n  SwuiDatetimepickerModule,\n  CustomPeriodModule,\n  ReactiveFormsModule,\n];\n\n@NgModule({\n  imports: [\n    CommonModule,\n    ...DATE_TIME_RANGE_MODULES,\n  ],\n  exports: [\n    SwuiDateTimeRangeComponent\n  ],\n  declarations: [SwuiDateTimeRangeComponent]\n})\nexport class SwuiDateTimeRangeModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,wBAAwB,QAAQ,mDAAmD;AAC5F,SAASC,0BAA0B,QAAQ,kCAAkC;AAC7E,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,wBAAwB;AAGxD,OAAO,MAAMC,uBAAuB,GAAG,CACrCT,eAAe,CAACU,QAAQ,EAAE,EAC1BN,kBAAkB,EAClBC,aAAa,EACbE,eAAe,EACfD,cAAc,EACdE,eAAe,EACfN,wBAAwB,EACxBD,kBAAkB,EAClBF,mBAAmB,CACpB;AAYM,IAAMY,uBAAuB,GAA7B,MAAMA,uBAAuB,GACnC;AADYA,uBAAuB,GAAAC,UAAA,EAVnCf,QAAQ,CAAC;EACRgB,OAAO,EAAE,CACPf,YAAY,EACZ,GAAGW,uBAAuB,CAC3B;EACDK,OAAO,EAAE,CACPX,0BAA0B,CAC3B;EACDY,YAAY,EAAE,CAACZ,0BAA0B;CAC1C,CAAC,C,EACWQ,uBAAuB,CACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}