{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { CommonModule } from '@angular/common';\nimport { JwtModule } from '@auth0/angular-jwt';\nimport { SwHubInitService } from './sw-hub-init.service';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwHubAuthModule } from '../sw-hub-auth/sw-hub-auth.module';\nimport { SettingsService } from '../settings/settings.service';\nimport { SwuiSidebarModule } from '../../swui-sidebar/swui-sidebar.module';\nimport { provideHttpClientTesting } from '@angular/common/http/testing';\nimport { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';\nimport { SWUI_HUB_MESSAGE_CONFIG } from './sw-hub-init.token';\nimport { SwHubEntityService } from '../sw-hub-entity/sw-hub-entity.service';\nimport { SwuiSidebarService } from '../../swui-sidebar/swui-sidebar.service';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nexport function tokenGetter() {\n  return '';\n}\ndescribe('SwHubInitService', () => {\n  let service;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [CommonModule, RouterTestingModule, TranslateModule.forRoot(), JwtModule.forRoot({\n        config: {\n          tokenGetter: tokenGetter\n        }\n      }), SwHubAuthModule, SwuiSidebarModule],\n      providers: [SwHubInitService, SwHubConfigService, SettingsService, SwHubEntityService, SwuiSidebarService, {\n        provide: SWUI_HUB_MESSAGE_CONFIG,\n        useValue: {\n          langs: []\n        }\n      }, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()]\n    });\n    service = TestBed.inject(SwHubInitService);\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "CommonModule", "JwtModule", "SwHubInitService", "RouterTestingModule", "TranslateModule", "SwHubAuthModule", "SettingsService", "SwuiSidebarModule", "provideHttpClientTesting", "SwHubConfigService", "SWUI_HUB_MESSAGE_CONFIG", "SwHubEntityService", "SwuiSidebarService", "provideHttpClient", "withInterceptorsFromDi", "tokenGetter", "describe", "service", "beforeEach", "configureTestingModule", "imports", "forRoot", "config", "providers", "provide", "useValue", "langs", "inject", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-init/sw-hub-init.service.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\nimport { CommonModule } from '@angular/common';\nimport { JwtModule } from '@auth0/angular-jwt';\nimport { SwHubInitService } from './sw-hub-init.service';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwHubAuthModule } from '../sw-hub-auth/sw-hub-auth.module';\nimport { SettingsService } from '../settings/settings.service';\nimport { SwuiSidebarModule } from '../../swui-sidebar/swui-sidebar.module';\nimport { provideHttpClientTesting } from '@angular/common/http/testing';\nimport { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';\nimport { SWUI_HUB_MESSAGE_CONFIG } from './sw-hub-init.token';\nimport { SwHubEntityService } from '../sw-hub-entity/sw-hub-entity.service';\nimport { SwuiSidebarService } from '../../swui-sidebar/swui-sidebar.service';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\n\nexport function tokenGetter() {\n  return '';\n}\n\ndescribe('SwHubInitService', () => {\n  let service: SwHubInitService;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n    imports: [CommonModule,\n        RouterTestingModule,\n        TranslateModule.forRoot(),\n        JwtModule.forRoot({\n            config: {\n                tokenGetter: tokenGetter,\n            }\n        }),\n        SwHubAuthModule,\n        SwuiSidebarModule],\n    providers: [\n        SwHubInitService,\n        SwHubConfigService,\n        SettingsService,\n        SwHubEntityService,\n        SwuiSidebarService,\n        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: { langs: [] } },\n        provideHttpClient(withInterceptorsFromDi()),\n        provideHttpClientTesting()\n    ]\n});\n    service = TestBed.inject(SwHubInitService);\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,sBAAsB;AAEhF,OAAM,SAAUC,WAAWA,CAAA;EACzB,OAAO,EAAE;AACX;AAEAC,QAAQ,CAAC,kBAAkB,EAAE,MAAK;EAChC,IAAIC,OAAyB;EAC7BC,UAAU,CAAC,MAAK;IACdnB,OAAO,CAACoB,sBAAsB,CAAC;MAC/BC,OAAO,EAAE,CAACpB,YAAY,EAClBG,mBAAmB,EACnBC,eAAe,CAACiB,OAAO,EAAE,EACzBpB,SAAS,CAACoB,OAAO,CAAC;QACdC,MAAM,EAAE;UACJP,WAAW,EAAEA;;OAEpB,CAAC,EACFV,eAAe,EACfE,iBAAiB,CAAC;MACtBgB,SAAS,EAAE,CACPrB,gBAAgB,EAChBO,kBAAkB,EAClBH,eAAe,EACfK,kBAAkB,EAClBC,kBAAkB,EAClB;QAAEY,OAAO,EAAEd,uBAAuB;QAAEe,QAAQ,EAAE;UAAEC,KAAK,EAAE;QAAE;MAAE,CAAE,EAC7Db,iBAAiB,CAACC,sBAAsB,EAAE,CAAC,EAC3CN,wBAAwB,EAAE;KAEjC,CAAC;IACES,OAAO,GAAGlB,OAAO,CAAC4B,MAAM,CAACzB,gBAAgB,CAAC;EAC5C,CAAC,CAAC;EAEF0B,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACZ,OAAO,CAAC,CAACa,UAAU,EAAE;EAC9B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}