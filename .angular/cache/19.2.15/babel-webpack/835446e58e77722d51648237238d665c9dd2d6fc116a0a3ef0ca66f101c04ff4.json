{"ast": null, "code": "export class MatDynamicFormWidgetComponent {\n  constructor({\n    control,\n    id,\n    option,\n    readonly,\n    submitted\n  }) {\n    this.id = id;\n    this.control = control;\n    this.option = option;\n    this.readonly = readonly;\n    this.submitted = submitted;\n  }\n}", "map": {"version": 3, "names": ["MatDynamicFormWidgetComponent", "constructor", "control", "id", "option", "readonly", "submitted"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/mat-dynamic-form-widget.component.ts"], "sourcesContent": ["export class MatDynamicFormWidgetComponent {\n    constructor({ control, id, option, readonly, submitted }) {\n        this.id = id;\n        this.control = control;\n        this.option = option;\n        this.readonly = readonly;\n        this.submitted = submitted;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,6BAA6B,CAAC;EACvCC,WAAWA,CAAC;IAAEC,OAAO;IAAEC,EAAE;IAAEC,MAAM;IAAEC,QAAQ;IAAEC;EAAU,CAAC,EAAE;IACtD,IAAI,CAACH,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}