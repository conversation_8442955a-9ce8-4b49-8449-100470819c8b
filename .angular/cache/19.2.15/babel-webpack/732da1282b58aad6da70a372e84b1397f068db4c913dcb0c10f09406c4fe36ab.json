{"ast": null, "code": "var _SwuiStartTimeComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-start-time.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-start-time.component.scss?ngResource\";\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormBuilder, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport * as moment from 'moment';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { takeUntil } from 'rxjs/operators';\nconst CONTROL_NAME = 'lib-swui-start-time';\nlet nextUniqueId = 0;\nlet SwuiStartTimeComponent = (_SwuiStartTimeComponent = class SwuiStartTimeComponent extends SwuiMatFormFieldControl {\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    if (!value) {\n      return;\n    }\n    this._value = value;\n    this.patchForm(this._value);\n    this.stateChanges.next(undefined);\n  }\n  set disableSeconds(val) {\n    this._disableSeconds = coerceBooleanProperty(val);\n    !this._disableSeconds && !this.form.disabled ? this.secondsControl.enable() : this.secondsControl.disable();\n  }\n  get disableSeconds() {\n    return this._disableSeconds;\n  }\n  get empty() {\n    return !this.hoursControl.value && !this.minutesControl.value && !this.secondsControl.value;\n  }\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, fb) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.controlType = CONTROL_NAME;\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._disableSeconds = false;\n    this.form = fb.group({\n      hours: ['00'],\n      minutes: ['00'],\n      seconds: ['00']\n    });\n  }\n  ngOnInit() {\n    this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(val => {\n      this._value = this.processForm(val);\n      this.onChange(this._value);\n    });\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.hoursInput && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.hoursInput.nativeElement.focus();\n    }\n  }\n  writeValue(value) {\n    if (!value) {\n      return;\n    }\n    this._value = value;\n    this.patchForm(value);\n  }\n  processInputValue(el) {\n    const control = this.form.get(el);\n    const value = control.value;\n    const maxLength = 2;\n    const maxValue = 59;\n    const maxHoursValue = 23;\n    const reg = /^\\d+$/;\n    if (!value || value === '0') {\n      control.setValue('00');\n    }\n    if (value && value.length > maxLength && value.toString().charAt(0) === '0') {\n      control.setValue(value.substr(1));\n      if (el !== 'hours' && value > maxValue) {\n        control.setValue(maxValue.toString());\n      } else if (el === 'hours' && value > maxValue) {\n        control.setValue(maxHoursValue.toString());\n      }\n    }\n    if (value && value > maxValue && el !== 'hours' && value.toString().charAt(0) !== '0') {\n      control.setValue(maxValue.toString());\n    }\n    if (value && value > maxHoursValue && el === 'hours') {\n      control.setValue(maxHoursValue.toString());\n    }\n    if (value && !value.toString().match(reg)) {\n      control.setValue(value.replace(/[^\\d,]/g, ''));\n    }\n  }\n  get hoursControl() {\n    return this.form.get('hours');\n  }\n  get minutesControl() {\n    return this.form.get('minutes');\n  }\n  get secondsControl() {\n    return this.form.get('seconds');\n  }\n  onDisabledState(disabled) {\n    disabled ? this.form.disable() : this.form.enable();\n  }\n  isErrorState() {\n    if (this.hoursInput && this.minutesInput && this.secondsInput) {\n      return this.hoursInput.nativeElement.errorState || this.minutesInput.nativeElement.errorState || this.secondsInput.nativeElement.errorState;\n    }\n    return false;\n  }\n  patchForm(value) {\n    const processValue = v => v < 10 ? '0' + v.toString() : v.toString();\n    const duration = moment.duration(value, 'milliseconds');\n    this.form.patchValue({\n      hours: processValue(duration.hours()),\n      minutes: processValue(duration.minutes()),\n      seconds: processValue(duration.seconds())\n    });\n  }\n  processForm(val) {\n    const hours = val.hours ? parseInt(val.hours, 10) : undefined;\n    const minutes = val.minutes ? parseInt(val.minutes, 10) : undefined;\n    const seconds = val.seconds && !this.disableSeconds ? parseInt(val.seconds, 10) : undefined;\n    return hours !== undefined || minutes !== undefined || seconds !== undefined ? moment.duration(hours, 'hours').asMilliseconds() + moment.duration(minutes, 'minutes').asMilliseconds() + moment.duration(seconds, 'seconds').asMilliseconds() : undefined;\n  }\n}, _SwuiStartTimeComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}, {\n  type: UntypedFormBuilder\n}], _SwuiStartTimeComponent.propDecorators = {\n  value: [{\n    type: Input\n  }],\n  disableSeconds: [{\n    type: Input\n  }],\n  hoursInput: [{\n    type: ViewChild,\n    args: ['hours']\n  }],\n  minutesInput: [{\n    type: ViewChild,\n    args: ['minutes']\n  }],\n  secondsInput: [{\n    type: ViewChild,\n    args: ['seconds']\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiStartTimeComponent);\nSwuiStartTimeComponent = __decorate([Component({\n  selector: 'lib-swui-start-time',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiStartTimeComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiStartTimeComponent);\nexport { SwuiStartTimeComponent };", "map": {"version": 3, "names": ["Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormBuilder", "FormGroupDirective", "NgControl", "MatFormFieldControl", "coerceBooleanProperty", "FocusMonitor", "moment", "SwuiMatFormFieldControl", "ErrorStateMatcher", "takeUntil", "CONTROL_NAME", "nextUniqueId", "SwuiStartTimeComponent", "_SwuiStartTimeComponent", "value", "_value", "patchForm", "stateChanges", "next", "undefined", "disableSeconds", "val", "_disableSeconds", "form", "disabled", "secondsControl", "enable", "disable", "empty", "hoursControl", "minutesControl", "shouldLabelFloat", "focused", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "fb", "controlType", "id", "group", "hours", "minutes", "seconds", "ngOnInit", "valueChanges", "pipe", "destroyed$", "subscribe", "processForm", "onChange", "onContainerClick", "event", "stopPropagation", "hoursInput", "target", "tagName", "toLowerCase", "nativeElement", "focus", "writeValue", "processInputValue", "el", "control", "get", "max<PERSON><PERSON><PERSON>", "maxValue", "maxHoursValue", "reg", "setValue", "length", "toString", "char<PERSON>t", "substr", "match", "replace", "onDisabledState", "isErrorState", "minutesInput", "secondsInput", "errorState", "processValue", "v", "duration", "patchValue", "parseInt", "asMilliseconds", "type", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "provide", "useExisting", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-start-time/swui-start-time.component.ts"], "sourcesContent": ["import { Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport * as moment from 'moment';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { takeUntil } from 'rxjs/operators';\n\ninterface StartTime {\n  hours: string;\n  minutes: string;\n  seconds?: string;\n}\n\nconst CONTROL_NAME = 'lib-swui-start-time';\nlet nextUniqueId = 0;\n\n@Component({\n    selector: 'lib-swui-start-time',\n    templateUrl: './swui-start-time.component.html',\n    styleUrls: ['./swui-start-time.component.scss'],\n    providers: [{ provide: MatFormFieldControl, useExisting: SwuiStartTimeComponent }],\n    standalone: false\n})\nexport class SwuiStartTimeComponent extends SwuiMatFormFieldControl<number | undefined> implements OnInit {\n  @Input()\n  get value(): number | undefined {\n    return this._value;\n  }\n\n  set value( value: number | undefined ) {\n    if (!value) {\n      return;\n    }\n    this._value = value;\n    this.patchForm(this._value);\n    this.stateChanges.next(undefined);\n  }\n\n  @Input()\n  set disableSeconds( val: boolean ) {\n    this._disableSeconds = coerceBooleanProperty(val);\n    !this._disableSeconds && !this.form.disabled ? this.secondsControl.enable() : this.secondsControl.disable();\n  }\n\n  get disableSeconds(): boolean {\n    return this._disableSeconds;\n  }\n\n  get empty() {\n    return !this.hoursControl.value && !this.minutesControl.value && !this.secondsControl.value;\n  }\n\n  readonly controlType = CONTROL_NAME;\n  readonly form: UntypedFormGroup;\n\n  @ViewChild('hours') hoursInput?: ElementRef;\n  @ViewChild('minutes') minutesInput?: ElementRef;\n  @ViewChild('seconds') secondsInput?: ElementRef;\n\n  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;\n\n  @HostBinding('class.floating')\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n\n  private _value: number | undefined;\n  private _disableSeconds = false;\n\n  constructor( fm: FocusMonitor,\n               elRef: ElementRef<HTMLElement>,\n               @Optional() @Self() ngControl: NgControl,\n               @Optional() parentFormGroup: FormGroupDirective,\n               errorStateMatcher: ErrorStateMatcher,\n               fb: UntypedFormBuilder ) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.form = fb.group({\n      hours: ['00'],\n      minutes: ['00'],\n      seconds: ['00']\n    });\n  }\n\n  ngOnInit() {\n    this.form.valueChanges.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(( val: StartTime ) => {\n      this._value = this.processForm(val);\n      this.onChange(this._value);\n    });\n  }\n\n  onContainerClick( event: Event ) {\n    event.stopPropagation();\n    if (this.hoursInput && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.hoursInput.nativeElement.focus();\n    }\n  }\n\n  writeValue( value: number ): void {\n    if (!value) {\n      return;\n    }\n    this._value = value;\n    this.patchForm(value);\n  }\n\n  processInputValue( el: string ) {\n    const control = this.form.get(el) as UntypedFormControl;\n    const value = control.value;\n    const maxLength = 2;\n    const maxValue = 59;\n    const maxHoursValue = 23;\n    const reg = /^\\d+$/;\n\n    if (!value || value === '0') {\n      control.setValue('00');\n    }\n\n    if (value && value.length > maxLength && value.toString().charAt(0) === '0') {\n      control.setValue(value.substr(1));\n      if (el !== 'hours' && value > maxValue) {\n        control.setValue(maxValue.toString());\n      } else if (el === 'hours' && value > maxValue) {\n        control.setValue(maxHoursValue.toString());\n      }\n    }\n\n    if (value && value > maxValue && el !== 'hours' && value.toString().charAt(0) !== '0') {\n      control.setValue(maxValue.toString());\n    }\n\n    if (value && value > maxHoursValue && el === 'hours') {\n      control.setValue(maxHoursValue.toString());\n    }\n\n    if (value && !value.toString().match(reg)) {\n      control.setValue(value.replace(/[^\\d,]/g, ''));\n    }\n  }\n\n  get hoursControl(): UntypedFormControl {\n    return this.form.get('hours') as UntypedFormControl;\n  }\n\n  get minutesControl(): UntypedFormControl {\n    return this.form.get('minutes') as UntypedFormControl;\n  }\n\n  get secondsControl(): UntypedFormControl {\n    return this.form.get('seconds') as UntypedFormControl;\n  }\n\n  protected onDisabledState( disabled: boolean ) {\n    disabled ? this.form.disable() : this.form.enable();\n  }\n\n  protected isErrorState(): boolean {\n    if (this.hoursInput && this.minutesInput && this.secondsInput) {\n      return this.hoursInput.nativeElement.errorState ||\n        this.minutesInput.nativeElement.errorState ||\n        this.secondsInput.nativeElement.errorState;\n    }\n    return false;\n  }\n\n  private patchForm( value: number ) {\n    const processValue = ( v: number ): string => v < 10 ? '0' + v.toString() : v.toString();\n    const duration = moment.duration(value, 'milliseconds');\n    this.form.patchValue({\n      hours: processValue(duration.hours()),\n      minutes: processValue(duration.minutes()),\n      seconds: processValue(duration.seconds())\n    });\n  }\n\n  private processForm( val: StartTime ): number | undefined {\n    const hours = val.hours ? parseInt(val.hours, 10) : undefined;\n    const minutes = val.minutes ? parseInt(val.minutes, 10) : undefined;\n    const seconds = val.seconds && !this.disableSeconds ? parseInt(val.seconds, 10) : undefined;\n\n    return hours !== undefined || minutes !== undefined || seconds !== undefined ?\n      moment.duration(hours, 'hours').asMilliseconds() +\n      moment.duration(minutes, 'minutes').asMilliseconds() +\n      moment.duration(seconds, 'seconds').asMilliseconds() :\n      undefined;\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAUC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AAC5G,SAASC,kBAAkB,EAAwCC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AACxH,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,SAAS,QAAQ,gBAAgB;AAQ1C,MAAMC,YAAY,GAAG,qBAAqB;AAC1C,IAAIC,YAAY,GAAG,CAAC;AASb,IAAMC,sBAAsB,IAAAC,uBAAA,GAA5B,MAAMD,sBAAuB,SAAQL,uBAA2C;MAEjFO,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,MAAM;EACpB;EAEA,IAAID,KAAKA,CAAEA,KAAyB;IAClC,IAAI,CAACA,KAAK,EAAE;MACV;IACF;IACA,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,SAAS,CAAC,IAAI,CAACD,MAAM,CAAC;IAC3B,IAAI,CAACE,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;EACnC;MAGIC,cAAcA,CAAEC,GAAY;IAC9B,IAAI,CAACC,eAAe,GAAGlB,qBAAqB,CAACiB,GAAG,CAAC;IACjD,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,IAAI,CAACC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,MAAM,EAAE,GAAG,IAAI,CAACD,cAAc,CAACE,OAAO,EAAE;EAC7G;EAEA,IAAIP,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACE,eAAe;EAC7B;EAEA,IAAIM,KAAKA,CAAA;IACP,OAAO,CAAC,IAAI,CAACC,YAAY,CAACf,KAAK,IAAI,CAAC,IAAI,CAACgB,cAAc,CAAChB,KAAK,IAAI,CAAC,IAAI,CAACW,cAAc,CAACX,KAAK;EAC7F;MAYIiB,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACJ,KAAK;EACpC;EAKAK,YAAaC,EAAgB,EAChBC,KAA8B,EACVC,SAAoB,EAC5BC,eAAmC,EAC/CC,iBAAoC,EACpCC,EAAsB;IACjC,KAAK,CAACL,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAvBxD,KAAAE,WAAW,GAAG9B,YAAY;IAOX,KAAA+B,EAAE,GAAG,GAAG/B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAQxD,KAAAW,eAAe,GAAG,KAAK;IAS7B,IAAI,CAACC,IAAI,GAAGgB,EAAE,CAACG,KAAK,CAAC;MACnBC,KAAK,EAAE,CAAC,IAAI,CAAC;MACbC,OAAO,EAAE,CAAC,IAAI,CAAC;MACfC,OAAO,EAAE,CAAC,IAAI;KACf,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACvB,IAAI,CAACwB,YAAY,CAACC,IAAI,CACzBvC,SAAS,CAAC,IAAI,CAACwC,UAAU,CAAC,CAC3B,CAACC,SAAS,CAAG7B,GAAc,IAAK;MAC/B,IAAI,CAACN,MAAM,GAAG,IAAI,CAACoC,WAAW,CAAC9B,GAAG,CAAC;MACnC,IAAI,CAAC+B,QAAQ,CAAC,IAAI,CAACrC,MAAM,CAAC;IAC5B,CAAC,CAAC;EACJ;EAEAsC,gBAAgBA,CAAEC,KAAY;IAC5BA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,IAAI,CAACC,UAAU,IAAKF,KAAK,CAACG,MAAkB,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,IAAI,CAAC,IAAI,CAACnC,QAAQ,EAAE;MACpG,IAAI,CAACgC,UAAU,CAACI,aAAa,CAACC,KAAK,EAAE;IACvC;EACF;EAEAC,UAAUA,CAAEhD,KAAa;IACvB,IAAI,CAACA,KAAK,EAAE;MACV;IACF;IACA,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,SAAS,CAACF,KAAK,CAAC;EACvB;EAEAiD,iBAAiBA,CAAEC,EAAU;IAC3B,MAAMC,OAAO,GAAG,IAAI,CAAC1C,IAAI,CAAC2C,GAAG,CAACF,EAAE,CAAuB;IACvD,MAAMlD,KAAK,GAAGmD,OAAO,CAACnD,KAAK;IAC3B,MAAMqD,SAAS,GAAG,CAAC;IACnB,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,GAAG,GAAG,OAAO;IAEnB,IAAI,CAACxD,KAAK,IAAIA,KAAK,KAAK,GAAG,EAAE;MAC3BmD,OAAO,CAACM,QAAQ,CAAC,IAAI,CAAC;IACxB;IAEA,IAAIzD,KAAK,IAAIA,KAAK,CAAC0D,MAAM,GAAGL,SAAS,IAAIrD,KAAK,CAAC2D,QAAQ,EAAE,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC3ET,OAAO,CAACM,QAAQ,CAACzD,KAAK,CAAC6D,MAAM,CAAC,CAAC,CAAC,CAAC;MACjC,IAAIX,EAAE,KAAK,OAAO,IAAIlD,KAAK,GAAGsD,QAAQ,EAAE;QACtCH,OAAO,CAACM,QAAQ,CAACH,QAAQ,CAACK,QAAQ,EAAE,CAAC;MACvC,CAAC,MAAM,IAAIT,EAAE,KAAK,OAAO,IAAIlD,KAAK,GAAGsD,QAAQ,EAAE;QAC7CH,OAAO,CAACM,QAAQ,CAACF,aAAa,CAACI,QAAQ,EAAE,CAAC;MAC5C;IACF;IAEA,IAAI3D,KAAK,IAAIA,KAAK,GAAGsD,QAAQ,IAAIJ,EAAE,KAAK,OAAO,IAAIlD,KAAK,CAAC2D,QAAQ,EAAE,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACrFT,OAAO,CAACM,QAAQ,CAACH,QAAQ,CAACK,QAAQ,EAAE,CAAC;IACvC;IAEA,IAAI3D,KAAK,IAAIA,KAAK,GAAGuD,aAAa,IAAIL,EAAE,KAAK,OAAO,EAAE;MACpDC,OAAO,CAACM,QAAQ,CAACF,aAAa,CAACI,QAAQ,EAAE,CAAC;IAC5C;IAEA,IAAI3D,KAAK,IAAI,CAACA,KAAK,CAAC2D,QAAQ,EAAE,CAACG,KAAK,CAACN,GAAG,CAAC,EAAE;MACzCL,OAAO,CAACM,QAAQ,CAACzD,KAAK,CAAC+D,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IAChD;EACF;EAEA,IAAIhD,YAAYA,CAAA;IACd,OAAO,IAAI,CAACN,IAAI,CAAC2C,GAAG,CAAC,OAAO,CAAuB;EACrD;EAEA,IAAIpC,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACP,IAAI,CAAC2C,GAAG,CAAC,SAAS,CAAuB;EACvD;EAEA,IAAIzC,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACF,IAAI,CAAC2C,GAAG,CAAC,SAAS,CAAuB;EACvD;EAEUY,eAAeA,CAAEtD,QAAiB;IAC1CA,QAAQ,GAAG,IAAI,CAACD,IAAI,CAACI,OAAO,EAAE,GAAG,IAAI,CAACJ,IAAI,CAACG,MAAM,EAAE;EACrD;EAEUqD,YAAYA,CAAA;IACpB,IAAI,IAAI,CAACvB,UAAU,IAAI,IAAI,CAACwB,YAAY,IAAI,IAAI,CAACC,YAAY,EAAE;MAC7D,OAAO,IAAI,CAACzB,UAAU,CAACI,aAAa,CAACsB,UAAU,IAC7C,IAAI,CAACF,YAAY,CAACpB,aAAa,CAACsB,UAAU,IAC1C,IAAI,CAACD,YAAY,CAACrB,aAAa,CAACsB,UAAU;IAC9C;IACA,OAAO,KAAK;EACd;EAEQlE,SAASA,CAAEF,KAAa;IAC9B,MAAMqE,YAAY,GAAKC,CAAS,IAAcA,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGA,CAAC,CAACX,QAAQ,EAAE,GAAGW,CAAC,CAACX,QAAQ,EAAE;IACxF,MAAMY,QAAQ,GAAG/E,MAAM,CAAC+E,QAAQ,CAACvE,KAAK,EAAE,cAAc,CAAC;IACvD,IAAI,CAACS,IAAI,CAAC+D,UAAU,CAAC;MACnB3C,KAAK,EAAEwC,YAAY,CAACE,QAAQ,CAAC1C,KAAK,EAAE,CAAC;MACrCC,OAAO,EAAEuC,YAAY,CAACE,QAAQ,CAACzC,OAAO,EAAE,CAAC;MACzCC,OAAO,EAAEsC,YAAY,CAACE,QAAQ,CAACxC,OAAO,EAAE;KACzC,CAAC;EACJ;EAEQM,WAAWA,CAAE9B,GAAc;IACjC,MAAMsB,KAAK,GAAGtB,GAAG,CAACsB,KAAK,GAAG4C,QAAQ,CAAClE,GAAG,CAACsB,KAAK,EAAE,EAAE,CAAC,GAAGxB,SAAS;IAC7D,MAAMyB,OAAO,GAAGvB,GAAG,CAACuB,OAAO,GAAG2C,QAAQ,CAAClE,GAAG,CAACuB,OAAO,EAAE,EAAE,CAAC,GAAGzB,SAAS;IACnE,MAAM0B,OAAO,GAAGxB,GAAG,CAACwB,OAAO,IAAI,CAAC,IAAI,CAACzB,cAAc,GAAGmE,QAAQ,CAAClE,GAAG,CAACwB,OAAO,EAAE,EAAE,CAAC,GAAG1B,SAAS;IAE3F,OAAOwB,KAAK,KAAKxB,SAAS,IAAIyB,OAAO,KAAKzB,SAAS,IAAI0B,OAAO,KAAK1B,SAAS,GAC1Eb,MAAM,CAAC+E,QAAQ,CAAC1C,KAAK,EAAE,OAAO,CAAC,CAAC6C,cAAc,EAAE,GAChDlF,MAAM,CAAC+E,QAAQ,CAACzC,OAAO,EAAE,SAAS,CAAC,CAAC4C,cAAc,EAAE,GACpDlF,MAAM,CAAC+E,QAAQ,CAACxC,OAAO,EAAE,SAAS,CAAC,CAAC2C,cAAc,EAAE,GACpDrE,SAAS;EACb;;;;;;;;UAnHctB;EAAQ;IAAA4F,IAAA,EAAI3F;EAAI;AAAA,G;;;UAChBD;EAAQ;AAAA,G;;;;;;UAhDrBD;EAAK;;UAcLA;EAAK;;UAiBLG,SAAS;IAAA2F,IAAA,GAAC,OAAO;EAAA;;UACjB3F,SAAS;IAAA2F,IAAA,GAAC,SAAS;EAAA;;UACnB3F,SAAS;IAAA2F,IAAA,GAAC,SAAS;EAAA;;UAEnB/F;EAAW;;UAEXA,WAAW;IAAA+F,IAAA,GAAC,gBAAgB;EAAA;;AAtClB9E,sBAAsB,GAAA+E,UAAA,EAPlClG,SAAS,CAAC;EACPmG,QAAQ,EAAE,qBAAqB;EAC/BC,QAAA,EAAAC,oBAA+C;EAE/CC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAE7F,mBAAmB;IAAE8F,WAAW,EAAErF;EAAsB,CAAE,CAAC;EAClFsF,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWtF,sBAAsB,CAoKlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}