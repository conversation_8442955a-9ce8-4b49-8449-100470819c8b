{"ast": null, "code": "var _SwuiMatFormFieldControl;\nimport { __decorate } from \"tslib\";\nimport { FormGroupDirective, NgControl } from '@angular/forms';\nimport { Directive, ElementRef, HostBinding, Input } from '@angular/core';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { Subject } from 'rxjs';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nlet SwuiMatFormFieldControl = (_SwuiMatFormFieldControl = class SwuiMatFormFieldControl {\n  set required(required) {\n    this._required = coerceBooleanProperty(required);\n    this.stateChanges.next(undefined);\n  }\n  get required() {\n    return this._required;\n  }\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this.onDisabledState(value);\n    this.stateChanges.next(undefined);\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n    this.fm = fm;\n    this.elRef = elRef;\n    this.ngControl = ngControl;\n    this.parentFormGroup = parentFormGroup;\n    this.errorStateMatcher = errorStateMatcher;\n    this.placeholder = '';\n    this.describedBy = '';\n    this.errorState = false;\n    this.focused = false;\n    this.stateChanges = new Subject();\n    this.destroyed$ = new Subject();\n    this._required = false;\n    this._disabled = false;\n    this.onChange = _ => {};\n    this.onTouched = () => {};\n    if (this.ngControl !== null) {\n      this.ngControl.valueAccessor = this;\n    }\n    fm.monitor(elRef.nativeElement, true).subscribe(origin => {\n      if (this.focused && !origin) {\n        this.onTouched();\n      }\n      this.focused = !!origin;\n      this.stateChanges.next(undefined);\n    });\n  }\n  ngDoCheck() {\n    if (this.ngControl) {\n      const oldState = this.errorState;\n      const parent = this.parentFormGroup;\n      const matcher = this.errorStateMatcher;\n      const control = this.ngControl ? this.ngControl.control : null;\n      const newState = matcher.isErrorState(control, parent) || this.isErrorState();\n      if (newState !== oldState) {\n        this.errorState = newState;\n        this.stateChanges.next(undefined);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.fm.stopMonitoring(this.elRef.nativeElement);\n    this.stateChanges.complete();\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(value) {\n    this._disabled = value;\n    this.onDisabledState(value);\n  }\n  setDescribedByIds(ids) {\n    this.describedBy = ids.join(' ');\n  }\n  onDisabledState(_) {}\n  isErrorState() {\n    return false;\n  }\n}, _SwuiMatFormFieldControl.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl\n}, {\n  type: FormGroupDirective\n}, {\n  type: ErrorStateMatcher\n}], _SwuiMatFormFieldControl.propDecorators = {\n  placeholder: [{\n    type: Input\n  }],\n  required: [{\n    type: Input\n  }],\n  disabled: [{\n    type: Input\n  }],\n  describedBy: [{\n    type: HostBinding,\n    args: ['attr.aria-describedby']\n  }]\n}, _SwuiMatFormFieldControl);\nSwuiMatFormFieldControl = __decorate([Directive()\n// tslint:disable-next-line:directive-class-suffix\n], SwuiMatFormFieldControl);\nexport { SwuiMatFormFieldControl };", "map": {"version": 3, "names": ["__decorate", "FormGroupDirective", "NgControl", "Directive", "ElementRef", "HostBinding", "Input", "ErrorStateMatcher", "Subject", "FocusMonitor", "coerceBooleanProperty", "SwuiMatFormFieldControl", "_SwuiMatFormFieldControl", "required", "_required", "stateChanges", "next", "undefined", "disabled", "_disabled", "value", "onDisabledState", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "placeholder", "describedBy", "errorState", "focused", "destroyed$", "onChange", "_", "onTouched", "valueAccessor", "monitor", "nativeElement", "subscribe", "origin", "ngDoCheck", "oldState", "parent", "matcher", "control", "newState", "isErrorState", "ngOnDestroy", "stopMonitoring", "complete", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "setDescribedByIds", "ids", "join", "ctorParameters", "type", "propDecorators", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/common/swui-mat-form-field-control.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { FormGroupDirective, NgControl } from '@angular/forms';\nimport { Directive, ElementRef, HostBinding, Input } from '@angular/core';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { Subject } from 'rxjs';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nlet SwuiMatFormFieldControl = class SwuiMatFormFieldControl {\n    set required(required) {\n        this._required = coerceBooleanProperty(required);\n        this.stateChanges.next(undefined);\n    }\n    get required() {\n        return this._required;\n    }\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this.onDisabledState(value);\n        this.stateChanges.next(undefined);\n    }\n    constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n        this.fm = fm;\n        this.elRef = elRef;\n        this.ngControl = ngControl;\n        this.parentFormGroup = parentFormGroup;\n        this.errorStateMatcher = errorStateMatcher;\n        this.placeholder = '';\n        this.describedBy = '';\n        this.errorState = false;\n        this.focused = false;\n        this.stateChanges = new Subject();\n        this.destroyed$ = new Subject();\n        this._required = false;\n        this._disabled = false;\n        this.onChange = (_) => {\n        };\n        this.onTouched = () => {\n        };\n        if (this.ngControl !== null) {\n            this.ngControl.valueAccessor = this;\n        }\n        fm.monitor(elRef.nativeElement, true).subscribe(origin => {\n            if (this.focused && !origin) {\n                this.onTouched();\n            }\n            this.focused = !!origin;\n            this.stateChanges.next(undefined);\n        });\n    }\n    ngDoCheck() {\n        if (this.ngControl) {\n            const oldState = this.errorState;\n            const parent = this.parentFormGroup;\n            const matcher = this.errorStateMatcher;\n            const control = this.ngControl ? this.ngControl.control : null;\n            const newState = matcher.isErrorState(control, parent) || this.isErrorState();\n            if (newState !== oldState) {\n                this.errorState = newState;\n                this.stateChanges.next(undefined);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this.fm.stopMonitoring(this.elRef.nativeElement);\n        this.stateChanges.complete();\n        this.destroyed$.next(undefined);\n        this.destroyed$.complete();\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(value) {\n        this._disabled = value;\n        this.onDisabledState(value);\n    }\n    setDescribedByIds(ids) {\n        this.describedBy = ids.join(' ');\n    }\n    onDisabledState(_) {\n    }\n    isErrorState() {\n        return false;\n    }\n    static { this.ctorParameters = () => [\n        { type: FocusMonitor },\n        { type: ElementRef },\n        { type: NgControl },\n        { type: FormGroupDirective },\n        { type: ErrorStateMatcher }\n    ]; }\n    static { this.propDecorators = {\n        placeholder: [{ type: Input }],\n        required: [{ type: Input }],\n        disabled: [{ type: Input }],\n        describedBy: [{ type: HostBinding, args: ['attr.aria-describedby',] }]\n    }; }\n};\nSwuiMatFormFieldControl = __decorate([\n    Directive()\n    // tslint:disable-next-line:directive-class-suffix\n], SwuiMatFormFieldControl);\nexport { SwuiMatFormFieldControl };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAC9D,SAASC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,QAAQ,eAAe;AACzE,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,IAAIC,uBAAuB,IAAAC,wBAAA,GAAG,MAAMD,uBAAuB,CAAC;EACxD,IAAIE,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACC,SAAS,GAAGJ,qBAAqB,CAACG,QAAQ,CAAC;IAChD,IAAI,CAACE,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;EACrC;EACA,IAAIJ,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAII,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGT,qBAAqB,CAACU,KAAK,CAAC;IAC7C,IAAI,CAACC,eAAe,CAACD,KAAK,CAAC;IAC3B,IAAI,CAACL,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;EACrC;EACAK,WAAWA,CAACC,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,EAAE;IAClE,IAAI,CAACJ,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAAChB,YAAY,GAAG,IAAIP,OAAO,CAAC,CAAC;IACjC,IAAI,CAACwB,UAAU,GAAG,IAAIxB,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACM,SAAS,GAAG,KAAK;IACtB,IAAI,CAACK,SAAS,GAAG,KAAK;IACtB,IAAI,CAACc,QAAQ,GAAIC,CAAC,IAAK,CACvB,CAAC;IACD,IAAI,CAACC,SAAS,GAAG,MAAM,CACvB,CAAC;IACD,IAAI,IAAI,CAACV,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAACA,SAAS,CAACW,aAAa,GAAG,IAAI;IACvC;IACAb,EAAE,CAACc,OAAO,CAACb,KAAK,CAACc,aAAa,EAAE,IAAI,CAAC,CAACC,SAAS,CAACC,MAAM,IAAI;MACtD,IAAI,IAAI,CAACT,OAAO,IAAI,CAACS,MAAM,EAAE;QACzB,IAAI,CAACL,SAAS,CAAC,CAAC;MACpB;MACA,IAAI,CAACJ,OAAO,GAAG,CAAC,CAACS,MAAM;MACvB,IAAI,CAACzB,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;IACrC,CAAC,CAAC;EACN;EACAwB,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAChB,SAAS,EAAE;MAChB,MAAMiB,QAAQ,GAAG,IAAI,CAACZ,UAAU;MAChC,MAAMa,MAAM,GAAG,IAAI,CAACjB,eAAe;MACnC,MAAMkB,OAAO,GAAG,IAAI,CAACjB,iBAAiB;MACtC,MAAMkB,OAAO,GAAG,IAAI,CAACpB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACoB,OAAO,GAAG,IAAI;MAC9D,MAAMC,QAAQ,GAAGF,OAAO,CAACG,YAAY,CAACF,OAAO,EAAEF,MAAM,CAAC,IAAI,IAAI,CAACI,YAAY,CAAC,CAAC;MAC7E,IAAID,QAAQ,KAAKJ,QAAQ,EAAE;QACvB,IAAI,CAACZ,UAAU,GAAGgB,QAAQ;QAC1B,IAAI,CAAC/B,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;MACrC;IACJ;EACJ;EACA+B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzB,EAAE,CAAC0B,cAAc,CAAC,IAAI,CAACzB,KAAK,CAACc,aAAa,CAAC;IAChD,IAAI,CAACvB,YAAY,CAACmC,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAClB,UAAU,CAAChB,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACe,UAAU,CAACkB,QAAQ,CAAC,CAAC;EAC9B;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACnB,QAAQ,GAAGmB,EAAE;EACtB;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACjB,SAAS,GAAGiB,EAAE;EACvB;EACAE,gBAAgBA,CAAClC,KAAK,EAAE;IACpB,IAAI,CAACD,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACC,eAAe,CAACD,KAAK,CAAC;EAC/B;EACAmC,iBAAiBA,CAACC,GAAG,EAAE;IACnB,IAAI,CAAC3B,WAAW,GAAG2B,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC;EACpC;EACApC,eAAeA,CAACa,CAAC,EAAE,CACnB;EACAa,YAAYA,CAAA,EAAG;IACX,OAAO,KAAK;EAChB;AAcJ,CAAC,EAbYnC,wBAAA,CAAK8C,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAElD;AAAa,CAAC,EACtB;EAAEkD,IAAI,EAAEvD;AAAW,CAAC,EACpB;EAAEuD,IAAI,EAAEzD;AAAU,CAAC,EACnB;EAAEyD,IAAI,EAAE1D;AAAmB,CAAC,EAC5B;EAAE0D,IAAI,EAAEpD;AAAkB,CAAC,CAC9B,EACQK,wBAAA,CAAKgD,cAAc,GAAG;EAC3BhC,WAAW,EAAE,CAAC;IAAE+B,IAAI,EAAErD;EAAM,CAAC,CAAC;EAC9BO,QAAQ,EAAE,CAAC;IAAE8C,IAAI,EAAErD;EAAM,CAAC,CAAC;EAC3BY,QAAQ,EAAE,CAAC;IAAEyC,IAAI,EAAErD;EAAM,CAAC,CAAC;EAC3BuB,WAAW,EAAE,CAAC;IAAE8B,IAAI,EAAEtD,WAAW;IAAEwD,IAAI,EAAE,CAAC,uBAAuB;EAAG,CAAC;AACzE,CAAC,EAAAjD,wBAAA,CACJ;AACDD,uBAAuB,GAAGX,UAAU,CAAC,CACjCG,SAAS,CAAC;AACV;AAAA,CACH,EAAEQ,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}