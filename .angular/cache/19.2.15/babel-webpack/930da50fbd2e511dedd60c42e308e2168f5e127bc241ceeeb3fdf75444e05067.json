{"ast": null, "code": "var _AvailableLabelsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./available-labels.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../games-select-manager.scss?ngResource\";\nimport { Component, Input, ViewChild } from '@angular/core';\nimport { GamesSelectManagerService } from '../games-select-manager.service';\nimport { getLabelClass, getUniqueLabelGames } from '../game-select-item/game-select-item.model';\nimport { fromEvent } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';\nlet AvailableLabelsComponent = (_AvailableLabelsComponent = class AvailableLabelsComponent {\n  constructor(service) {\n    this.service = service;\n    this.disabled = false;\n    this.height = '500px';\n    this.searchTerm = '';\n    this.labels = [];\n    this.checkedLabels = [];\n    this.checkedLabelsGames = [];\n    this.intersection = null;\n    this.gamesPreviewVisible = false;\n    this.intersectionPreviewVisible = false;\n    this.subscriptions = [];\n  }\n  get scrollPortHeight() {\n    return parseFloat(this.height) - 58;\n  }\n  ngOnInit() {\n    this.createSearchStream();\n    this.subscribeToAvailableLabels();\n    this.subscribeToIntersection();\n    this.subscribeToAddedItems();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  labelCheckChanged() {\n    const checked = (this.labels || []).filter(item => item.checked);\n    this.setCheckedLabels(checked);\n  }\n  labelCheck(label) {\n    label.toggleCheck();\n    this.labelCheckChanged();\n  }\n  getLabelClass(label) {\n    return getLabelClass(label);\n  }\n  removeCheckedLabels() {\n    this.checkedLabels.forEach(label => label.checked = false);\n    this.checkedLabels = [];\n    this.checkedLabelsGames = [];\n    this.service.calculateCheckedAvailableLabels(this.checkedLabels);\n  }\n  setCheckedLabels(checked) {\n    this.checkedLabels = checked;\n    this.checkedLabelsGames = getUniqueLabelGames(this.checkedLabels).sort((a, b) => a.title > b.title ? 1 : -1);\n    this.service.calculateCheckedAvailableLabels(this.checkedLabels);\n    this.checkForIntersection();\n  }\n  /**\n   * addedItems contains items which were added to category\n   * we need to clean some available labels if they were affected\n   */\n  subscribeToAddedItems() {\n    this.service.addedItems$.subscribe(added => {\n      let labelsWereAdded = added.some(item => item.isLabel);\n      if (labelsWereAdded) {\n        this.removeCheckedLabels();\n      }\n      this.service.calculateCheckedAvailableLabels(this.checkedLabels);\n    });\n  }\n  subscribeToIntersection() {\n    this.service.createdIntersection$.subscribe(item => {\n      this.intersection = item;\n      this.service.calculateCheckedAvailableIntersection(item);\n    });\n    this.service.intersectionDestroyed$.subscribe(({\n      uncheckLabels\n    }) => {\n      this.intersection = null;\n      this.service.calculateCheckedAvailableIntersection(null);\n      if (uncheckLabels) {\n        this.removeCheckedLabels();\n      }\n    });\n  }\n  subscribeToAvailableLabels() {\n    this.service.availableLabels$.subscribe(labels => {\n      // this.uncheckCheckedLabels();\n      this.labels = labels;\n    });\n  }\n  checkForIntersection() {\n    if (this.checkedLabels.length > 1) {\n      this.createIntersection();\n    } else {\n      this.removeIntersection();\n    }\n  }\n  createIntersection() {\n    this.service.createIntersection(this.checkedLabels);\n  }\n  removeIntersection() {\n    if (this.intersection) {\n      this.service.removeAvailableIntersection();\n    }\n  }\n  createSearchStream() {\n    if (this.searchInput) {\n      let sub = fromEvent(this.searchInput.nativeElement, 'input').pipe(map(e => e['currentTarget']['value']), debounceTime(100), distinctUntilChanged()).subscribe(search => {\n        this.searchTerm = search;\n      });\n      this.subscriptions.push(sub);\n    }\n  }\n}, _AvailableLabelsComponent.ctorParameters = () => [{\n  type: GamesSelectManagerService\n}], _AvailableLabelsComponent.propDecorators = {\n  disabled: [{\n    type: Input\n  }],\n  height: [{\n    type: Input\n  }],\n  searchInput: [{\n    type: ViewChild,\n    args: ['searchInput', {\n      static: true\n    }]\n  }]\n}, _AvailableLabelsComponent);\nAvailableLabelsComponent = __decorate([Component({\n  // tslint:disable-next-line:component-selector\n  selector: 'sw-available-labels',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AvailableLabelsComponent);\nexport { AvailableLabelsComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "ViewChild", "GamesSelectManagerService", "getLabelClass", "getUniqueLabelGames", "fromEvent", "debounceTime", "distinctUntilChanged", "map", "AvailableLabelsComponent", "_AvailableLabelsComponent", "constructor", "service", "disabled", "height", "searchTerm", "labels", "checked<PERSON><PERSON><PERSON>", "checkedLabelsGames", "intersection", "gamesPreviewVisible", "intersectionPreviewVisible", "subscriptions", "scrollPortHeight", "parseFloat", "ngOnInit", "createSearchStream", "subscribeToAvailableLabels", "subscribeToIntersection", "subscribeToAddedItems", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "labelCheckChanged", "checked", "filter", "item", "set<PERSON><PERSON>cked<PERSON>abels", "labelCheck", "label", "to<PERSON><PERSON><PERSON><PERSON>", "removeCheckedLabels", "calculateCheckedAvailableLabels", "sort", "a", "b", "title", "checkForIntersection", "addedItems$", "subscribe", "added", "labelsWereAdded", "some", "isLabel", "createdIntersection$", "calculateCheckedAvailableIntersection", "intersectionDestroyed$", "uncheckLabels", "availableLabels$", "length", "createIntersection", "removeIntersection", "removeAvailableIntersection", "searchInput", "nativeElement", "pipe", "e", "search", "push", "ctorParameters", "type", "propDecorators", "args", "static", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/available-labels/available-labels.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./available-labels.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../games-select-manager.scss?ngResource\";\nimport { Component, Input, ViewChild } from '@angular/core';\nimport { GamesSelectManagerService } from '../games-select-manager.service';\nimport { getLabelClass, getUniqueLabelGames } from '../game-select-item/game-select-item.model';\nimport { fromEvent } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';\nlet AvailableLabelsComponent = class AvailableLabelsComponent {\n    constructor(service) {\n        this.service = service;\n        this.disabled = false;\n        this.height = '500px';\n        this.searchTerm = '';\n        this.labels = [];\n        this.checkedLabels = [];\n        this.checkedLabelsGames = [];\n        this.intersection = null;\n        this.gamesPreviewVisible = false;\n        this.intersectionPreviewVisible = false;\n        this.subscriptions = [];\n    }\n    get scrollPortHeight() {\n        return parseFloat(this.height) - 58;\n    }\n    ngOnInit() {\n        this.createSearchStream();\n        this.subscribeToAvailableLabels();\n        this.subscribeToIntersection();\n        this.subscribeToAddedItems();\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    labelCheckChanged() {\n        const checked = (this.labels || []).filter(item => item.checked);\n        this.setCheckedLabels(checked);\n    }\n    labelCheck(label) {\n        label.toggleCheck();\n        this.labelCheckChanged();\n    }\n    getLabelClass(label) {\n        return getLabelClass(label);\n    }\n    removeCheckedLabels() {\n        this.checkedLabels.forEach(label => label.checked = false);\n        this.checkedLabels = [];\n        this.checkedLabelsGames = [];\n        this.service.calculateCheckedAvailableLabels(this.checkedLabels);\n    }\n    setCheckedLabels(checked) {\n        this.checkedLabels = checked;\n        this.checkedLabelsGames = getUniqueLabelGames(this.checkedLabels)\n            .sort((a, b) => a.title > b.title ? 1 : -1);\n        this.service.calculateCheckedAvailableLabels(this.checkedLabels);\n        this.checkForIntersection();\n    }\n    /**\n     * addedItems contains items which were added to category\n     * we need to clean some available labels if they were affected\n     */\n    subscribeToAddedItems() {\n        this.service.addedItems$.subscribe((added) => {\n            let labelsWereAdded = added.some(item => item.isLabel);\n            if (labelsWereAdded) {\n                this.removeCheckedLabels();\n            }\n            this.service.calculateCheckedAvailableLabels(this.checkedLabels);\n        });\n    }\n    subscribeToIntersection() {\n        this.service.createdIntersection$.subscribe(item => {\n            this.intersection = item;\n            this.service.calculateCheckedAvailableIntersection(item);\n        });\n        this.service.intersectionDestroyed$.subscribe(({ uncheckLabels }) => {\n            this.intersection = null;\n            this.service.calculateCheckedAvailableIntersection(null);\n            if (uncheckLabels) {\n                this.removeCheckedLabels();\n            }\n        });\n    }\n    subscribeToAvailableLabels() {\n        this.service.availableLabels$.subscribe(labels => {\n            // this.uncheckCheckedLabels();\n            this.labels = labels;\n        });\n    }\n    checkForIntersection() {\n        if (this.checkedLabels.length > 1) {\n            this.createIntersection();\n        }\n        else {\n            this.removeIntersection();\n        }\n    }\n    createIntersection() {\n        this.service.createIntersection(this.checkedLabels);\n    }\n    removeIntersection() {\n        if (this.intersection) {\n            this.service.removeAvailableIntersection();\n        }\n    }\n    createSearchStream() {\n        if (this.searchInput) {\n            let sub = fromEvent(this.searchInput.nativeElement, 'input')\n                .pipe(map((e) => e['currentTarget']['value']), debounceTime(100), distinctUntilChanged())\n                .subscribe((search) => {\n                this.searchTerm = search;\n            });\n            this.subscriptions.push(sub);\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: GamesSelectManagerService }\n    ]; }\n    static { this.propDecorators = {\n        disabled: [{ type: Input }],\n        height: [{ type: Input }],\n        searchInput: [{ type: ViewChild, args: ['searchInput', { static: true },] }]\n    }; }\n};\nAvailableLabelsComponent = __decorate([\n    Component({\n        // tslint:disable-next-line:component-selector\n        selector: 'sw-available-labels',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], AvailableLabelsComponent);\nexport { AvailableLabelsComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,SAASC,SAAS,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AAC3D,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,4CAA4C;AAC/F,SAASC,SAAS,QAAQ,MAAM;AAChC,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,QAAQ,gBAAgB;AACxE,IAAIC,wBAAwB,IAAAC,yBAAA,GAAG,MAAMD,wBAAwB,CAAC;EAC1DE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,MAAM,GAAG,OAAO;IACrB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,0BAA0B,GAAG,KAAK;IACvC,IAAI,CAACC,aAAa,GAAG,EAAE;EAC3B;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAOC,UAAU,CAAC,IAAI,CAACV,MAAM,CAAC,GAAG,EAAE;EACvC;EACAW,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,aAAa,CAACS,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC;EACxD;EACAC,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,OAAO,GAAG,CAAC,IAAI,CAACnB,MAAM,IAAI,EAAE,EAAEoB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACF,OAAO,CAAC;IAChE,IAAI,CAACG,gBAAgB,CAACH,OAAO,CAAC;EAClC;EACAI,UAAUA,CAACC,KAAK,EAAE;IACdA,KAAK,CAACC,WAAW,CAAC,CAAC;IACnB,IAAI,CAACP,iBAAiB,CAAC,CAAC;EAC5B;EACA/B,aAAaA,CAACqC,KAAK,EAAE;IACjB,OAAOrC,aAAa,CAACqC,KAAK,CAAC;EAC/B;EACAE,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACzB,aAAa,CAACc,OAAO,CAACS,KAAK,IAAIA,KAAK,CAACL,OAAO,GAAG,KAAK,CAAC;IAC1D,IAAI,CAAClB,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACN,OAAO,CAAC+B,+BAA+B,CAAC,IAAI,CAAC1B,aAAa,CAAC;EACpE;EACAqB,gBAAgBA,CAACH,OAAO,EAAE;IACtB,IAAI,CAAClB,aAAa,GAAGkB,OAAO;IAC5B,IAAI,CAACjB,kBAAkB,GAAGd,mBAAmB,CAAC,IAAI,CAACa,aAAa,CAAC,CAC5D2B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,KAAK,GAAGD,CAAC,CAACC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACnC,OAAO,CAAC+B,+BAA+B,CAAC,IAAI,CAAC1B,aAAa,CAAC;IAChE,IAAI,CAAC+B,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACInB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACjB,OAAO,CAACqC,WAAW,CAACC,SAAS,CAAEC,KAAK,IAAK;MAC1C,IAAIC,eAAe,GAAGD,KAAK,CAACE,IAAI,CAAChB,IAAI,IAAIA,IAAI,CAACiB,OAAO,CAAC;MACtD,IAAIF,eAAe,EAAE;QACjB,IAAI,CAACV,mBAAmB,CAAC,CAAC;MAC9B;MACA,IAAI,CAAC9B,OAAO,CAAC+B,+BAA+B,CAAC,IAAI,CAAC1B,aAAa,CAAC;IACpE,CAAC,CAAC;EACN;EACAW,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAChB,OAAO,CAAC2C,oBAAoB,CAACL,SAAS,CAACb,IAAI,IAAI;MAChD,IAAI,CAAClB,YAAY,GAAGkB,IAAI;MACxB,IAAI,CAACzB,OAAO,CAAC4C,qCAAqC,CAACnB,IAAI,CAAC;IAC5D,CAAC,CAAC;IACF,IAAI,CAACzB,OAAO,CAAC6C,sBAAsB,CAACP,SAAS,CAAC,CAAC;MAAEQ;IAAc,CAAC,KAAK;MACjE,IAAI,CAACvC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACP,OAAO,CAAC4C,qCAAqC,CAAC,IAAI,CAAC;MACxD,IAAIE,aAAa,EAAE;QACf,IAAI,CAAChB,mBAAmB,CAAC,CAAC;MAC9B;IACJ,CAAC,CAAC;EACN;EACAf,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAACf,OAAO,CAAC+C,gBAAgB,CAACT,SAAS,CAAClC,MAAM,IAAI;MAC9C;MACA,IAAI,CAACA,MAAM,GAAGA,MAAM;IACxB,CAAC,CAAC;EACN;EACAgC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC/B,aAAa,CAAC2C,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACAD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACjD,OAAO,CAACiD,kBAAkB,CAAC,IAAI,CAAC5C,aAAa,CAAC;EACvD;EACA6C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC3C,YAAY,EAAE;MACnB,IAAI,CAACP,OAAO,CAACmD,2BAA2B,CAAC,CAAC;IAC9C;EACJ;EACArC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACsC,WAAW,EAAE;MAClB,IAAIhC,GAAG,GAAG3B,SAAS,CAAC,IAAI,CAAC2D,WAAW,CAACC,aAAa,EAAE,OAAO,CAAC,CACvDC,IAAI,CAAC1D,GAAG,CAAE2D,CAAC,IAAKA,CAAC,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE7D,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,CAAC,CAAC,CAAC,CACxF2C,SAAS,CAAEkB,MAAM,IAAK;QACvB,IAAI,CAACrD,UAAU,GAAGqD,MAAM;MAC5B,CAAC,CAAC;MACF,IAAI,CAAC9C,aAAa,CAAC+C,IAAI,CAACrC,GAAG,CAAC;IAChC;EACJ;AASJ,CAAC,EARYtB,yBAAA,CAAK4D,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAErE;AAA0B,CAAC,CACtC,EACQQ,yBAAA,CAAK8D,cAAc,GAAG;EAC3B3D,QAAQ,EAAE,CAAC;IAAE0D,IAAI,EAAEvE;EAAM,CAAC,CAAC;EAC3Bc,MAAM,EAAE,CAAC;IAAEyD,IAAI,EAAEvE;EAAM,CAAC,CAAC;EACzBgE,WAAW,EAAE,CAAC;IAAEO,IAAI,EAAEtE,SAAS;IAAEwE,IAAI,EAAE,CAAC,aAAa,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC;AAC/E,CAAC,EAAAhE,yBAAA,CACJ;AACDD,wBAAwB,GAAGb,UAAU,CAAC,CAClCG,SAAS,CAAC;EACN;EACA4E,QAAQ,EAAE,qBAAqB;EAC/BC,QAAQ,EAAE/E,oBAAoB;EAC9BgF,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAChF,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEW,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}