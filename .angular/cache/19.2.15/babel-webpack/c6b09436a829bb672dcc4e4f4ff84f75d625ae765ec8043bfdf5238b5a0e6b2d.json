{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _RouterTestingModule, _RootFixtureService, _RootCmp;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { NgModule, signal, Injectable, ViewChild, Component } from '@angular/core';\nimport { TestBed } from '@angular/core/testing';\nimport { ROUTES, ROUTER_CONFIGURATION, Router, afterNextNavigation, RouterOutlet } from './router-Dwfin5Au.mjs';\nexport { ɵEmptyOutletComponent as ɵɵEmptyOutletComponent } from './router-Dwfin5Au.mjs';\nimport { RouterModule, ROUTER_PROVIDERS, withPreloading, NoPreloading } from './router_module-DTJgGWLd.mjs';\nexport { RouterLink as ɵɵRouterLink, RouterLinkActive as ɵɵRouterLinkActive } from './router_module-DTJgGWLd.mjs';\nimport { provideLocationMocks } from '@angular/common/testing';\nimport '@angular/common';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/platform-browser';\n\n/**\n * @description\n *\n * Sets up the router to be used for testing.\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of `Location` and `LocationStrategy`.\n *\n * @usageNotes\n * ### Example\n *\n * ```ts\n * beforeEach(() => {\n *   TestBed.configureTestingModule({\n *     imports: [\n *       RouterModule.forRoot(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @publicApi\n * @deprecated Use `provideRouter` or `RouterModule`/`RouterModule.forRoot` instead.\n * This module was previously used to provide a helpful collection of test fakes,\n * most notably those for `Location` and `LocationStrategy`.  These are generally not\n * required anymore, as `MockPlatformLocation` is provided in `TestBed` by default.\n * However, you can use them directly with `provideLocationMocks`.\n */\nclass RouterTestingModule {\n  static withRoutes(routes, config) {\n    return {\n      ngModule: RouterTestingModule,\n      providers: [{\n        provide: ROUTES,\n        multi: true,\n        useValue: routes\n      }, {\n        provide: ROUTER_CONFIGURATION,\n        useValue: config ? config : {}\n      }]\n    };\n  }\n}\n_RouterTestingModule = RouterTestingModule;\n_defineProperty(RouterTestingModule, \"\\u0275fac\", function _RouterTestingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RouterTestingModule)();\n});\n_defineProperty(RouterTestingModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _RouterTestingModule,\n  exports: [RouterModule]\n}));\n_defineProperty(RouterTestingModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [ROUTER_PROVIDERS, provideLocationMocks(), withPreloading(NoPreloading).ɵproviders, {\n    provide: ROUTES,\n    multi: true,\n    useValue: []\n  }],\n  imports: [RouterModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [RouterModule],\n      providers: [ROUTER_PROVIDERS, provideLocationMocks(), withPreloading(NoPreloading).ɵproviders, {\n        provide: ROUTES,\n        multi: true,\n        useValue: []\n      }]\n    }]\n  }], null, null);\n})();\nclass RootFixtureService {\n  constructor() {\n    _defineProperty(this, \"fixture\", void 0);\n    _defineProperty(this, \"harness\", void 0);\n  }\n  createHarness() {\n    if (this.harness) {\n      throw new Error('Only one harness should be created per test.');\n    }\n    this.harness = new RouterTestingHarness(this.getRootFixture());\n    return this.harness;\n  }\n  getRootFixture() {\n    if (this.fixture !== undefined) {\n      return this.fixture;\n    }\n    this.fixture = TestBed.createComponent(RootCmp);\n    this.fixture.detectChanges();\n    return this.fixture;\n  }\n}\n_RootFixtureService = RootFixtureService;\n_defineProperty(RootFixtureService, \"\\u0275fac\", function _RootFixtureService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RootFixtureService)();\n});\n_defineProperty(RootFixtureService, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _RootFixtureService,\n  factory: _RootFixtureService.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RootFixtureService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass RootCmp {\n  constructor() {\n    _defineProperty(this, \"outlet\", void 0);\n    _defineProperty(this, \"routerOutletData\", signal(undefined));\n  }\n}\n_RootCmp = RootCmp;\n_defineProperty(RootCmp, \"\\u0275fac\", function _RootCmp_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RootCmp)();\n});\n_defineProperty(RootCmp, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _RootCmp,\n  selectors: [[\"ng-component\"]],\n  viewQuery: function _RootCmp_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(RouterOutlet, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.outlet = _t.first);\n    }\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"routerOutletData\"]],\n  template: function _RootCmp_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"router-outlet\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"routerOutletData\", ctx.routerOutletData());\n    }\n  },\n  dependencies: [RouterOutlet],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RootCmp, [{\n    type: Component,\n    args: [{\n      template: '<router-outlet [routerOutletData]=\"routerOutletData()\"></router-outlet>',\n      imports: [RouterOutlet]\n    }]\n  }], null, {\n    outlet: [{\n      type: ViewChild,\n      args: [RouterOutlet]\n    }]\n  });\n})();\n/**\n * A testing harness for the `Router` to reduce the boilerplate needed to test routes and routed\n * components.\n *\n * @publicApi\n */\nclass RouterTestingHarness {\n  /**\n   * Creates a `RouterTestingHarness` instance.\n   *\n   * The `RouterTestingHarness` also creates its own root component with a `RouterOutlet` for the\n   * purposes of rendering route components.\n   *\n   * Throws an error if an instance has already been created.\n   * Use of this harness also requires `destroyAfterEach: true` in the `ModuleTeardownOptions`\n   *\n   * @param initialUrl The target of navigation to trigger before returning the harness.\n   */\n  static create(initialUrl) {\n    return _asyncToGenerator(function* () {\n      const harness = TestBed.inject(RootFixtureService).createHarness();\n      if (initialUrl !== undefined) {\n        yield harness.navigateByUrl(initialUrl);\n      }\n      return harness;\n    })();\n  }\n  /**\n   * Fixture of the root component of the RouterTestingHarness\n   */\n\n  /** @internal */\n  constructor(fixture) {\n    _defineProperty(this, \"fixture\", void 0);\n    this.fixture = fixture;\n  }\n  /** Instructs the root fixture to run change detection. */\n  detectChanges() {\n    this.fixture.detectChanges();\n  }\n  /** The `DebugElement` of the `RouterOutlet` component. `null` if the outlet is not activated. */\n  get routeDebugElement() {\n    const outlet = this.fixture.componentInstance.outlet;\n    if (!outlet || !outlet.isActivated) {\n      return null;\n    }\n    return this.fixture.debugElement.query(v => v.componentInstance === outlet.component);\n  }\n  /** The native element of the `RouterOutlet` component. `null` if the outlet is not activated. */\n  get routeNativeElement() {\n    var _this$routeDebugEleme, _this$routeDebugEleme2;\n    return (_this$routeDebugEleme = (_this$routeDebugEleme2 = this.routeDebugElement) === null || _this$routeDebugEleme2 === void 0 ? void 0 : _this$routeDebugEleme2.nativeElement) !== null && _this$routeDebugEleme !== void 0 ? _this$routeDebugEleme : null;\n  }\n  navigateByUrl(url, requiredRoutedComponentType) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const router = TestBed.inject(Router);\n      let resolveFn;\n      const redirectTrackingPromise = new Promise(resolve => {\n        resolveFn = resolve;\n      });\n      afterNextNavigation(TestBed.inject(Router), resolveFn);\n      yield router.navigateByUrl(url);\n      yield redirectTrackingPromise;\n      _this.fixture.detectChanges();\n      const outlet = _this.fixture.componentInstance.outlet;\n      // The outlet might not be activated if the user is testing a navigation for a guard that\n      // rejects\n      if (outlet && outlet.isActivated && outlet.activatedRoute.component) {\n        const activatedComponent = outlet.component;\n        if (requiredRoutedComponentType !== undefined && !(activatedComponent instanceof requiredRoutedComponentType)) {\n          throw new Error(`Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but got ${activatedComponent.constructor.name}`);\n        }\n        return activatedComponent;\n      } else {\n        if (requiredRoutedComponentType !== undefined) {\n          throw new Error(`Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but the navigation did not activate any component.`);\n        }\n        return null;\n      }\n    })();\n  }\n}\nexport { RouterTestingHarness, RouterTestingModule, RouterOutlet as ɵɵRouterOutlet };", "map": {"version": 3, "names": ["i0", "NgModule", "signal", "Injectable", "ViewChild", "Component", "TestBed", "ROUTES", "ROUTER_CONFIGURATION", "Router", "afterNextNavigation", "RouterOutlet", "ɵEmptyOutletComponent", "ɵɵEmptyOutletComponent", "RouterModule", "ROUTER_PROVIDERS", "withPreloading", "NoPreloading", "RouterLink", "ɵɵRouterLink", "RouterLinkActive", "ɵɵRouterLinkActive", "provideLocationMocks", "RouterTestingModule", "with<PERSON>out<PERSON>", "routes", "config", "ngModule", "providers", "provide", "multi", "useValue", "_RouterTestingModule", "_defineProperty", "_RouterTestingModule_Factory", "__ngFactoryType__", "ɵɵdefineNgModule", "type", "exports", "ɵɵdefineInjector", "ɵproviders", "imports", "ngDevMode", "ɵsetClassMetadata", "args", "RootFixtureService", "constructor", "createHarness", "harness", "Error", "RouterTestingHarness", "getRootFixture", "fixture", "undefined", "createComponent", "RootCmp", "detectChanges", "_RootFixtureService", "_RootFixtureService_Factory", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "_RootCmp", "_RootCmp_Factory", "ɵɵdefineComponent", "selectors", "viewQuery", "_RootCmp_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "outlet", "first", "decls", "vars", "consts", "template", "_RootCmp_Template", "ɵɵelement", "ɵɵproperty", "routerOutletData", "dependencies", "encapsulation", "create", "initialUrl", "_asyncToGenerator", "inject", "navigateByUrl", "routeDebugElement", "componentInstance", "isActivated", "debugElement", "query", "v", "component", "routeNativeElement", "_this$routeDebugEleme", "_this$routeDebugEleme2", "nativeElement", "url", "requiredRoutedComponentType", "_this", "router", "resolveFn", "redirectTrackingPromise", "Promise", "resolve", "activatedRoute", "activatedComponent", "name", "ɵɵRouterOutlet"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/router/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { NgModule, signal, Injectable, ViewChild, Component } from '@angular/core';\nimport { TestBed } from '@angular/core/testing';\nimport { ROUTES, ROUTER_CONFIGURATION, Router, afterNextNavigation, RouterOutlet } from './router-Dwfin5Au.mjs';\nexport { ɵEmptyOutletComponent as ɵɵEmptyOutletComponent } from './router-Dwfin5Au.mjs';\nimport { RouterModule, ROUTER_PROVIDERS, withPreloading, NoPreloading } from './router_module-DTJgGWLd.mjs';\nexport { RouterLink as ɵɵRouterLink, RouterLinkActive as ɵɵRouterLinkActive } from './router_module-DTJgGWLd.mjs';\nimport { provideLocationMocks } from '@angular/common/testing';\nimport '@angular/common';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/platform-browser';\n\n/**\n * @description\n *\n * Sets up the router to be used for testing.\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of `Location` and `LocationStrategy`.\n *\n * @usageNotes\n * ### Example\n *\n * ```ts\n * beforeEach(() => {\n *   TestBed.configureTestingModule({\n *     imports: [\n *       RouterModule.forRoot(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @publicApi\n * @deprecated Use `provideRouter` or `RouterModule`/`RouterModule.forRoot` instead.\n * This module was previously used to provide a helpful collection of test fakes,\n * most notably those for `Location` and `LocationStrategy`.  These are generally not\n * required anymore, as `MockPlatformLocation` is provided in `TestBed` by default.\n * However, you can use them directly with `provideLocationMocks`.\n */\nclass RouterTestingModule {\n    static withRoutes(routes, config) {\n        return {\n            ngModule: RouterTestingModule,\n            providers: [\n                { provide: ROUTES, multi: true, useValue: routes },\n                { provide: ROUTER_CONFIGURATION, useValue: config ? config : {} },\n            ],\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterTestingModule, exports: [RouterModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterTestingModule, providers: [\n            ROUTER_PROVIDERS,\n            provideLocationMocks(),\n            withPreloading(NoPreloading).ɵproviders,\n            { provide: ROUTES, multi: true, useValue: [] },\n        ], imports: [RouterModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [RouterModule],\n                    providers: [\n                        ROUTER_PROVIDERS,\n                        provideLocationMocks(),\n                        withPreloading(NoPreloading).ɵproviders,\n                        { provide: ROUTES, multi: true, useValue: [] },\n                    ],\n                }]\n        }] });\n\nclass RootFixtureService {\n    fixture;\n    harness;\n    createHarness() {\n        if (this.harness) {\n            throw new Error('Only one harness should be created per test.');\n        }\n        this.harness = new RouterTestingHarness(this.getRootFixture());\n        return this.harness;\n    }\n    getRootFixture() {\n        if (this.fixture !== undefined) {\n            return this.fixture;\n        }\n        this.fixture = TestBed.createComponent(RootCmp);\n        this.fixture.detectChanges();\n        return this.fixture;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RootFixtureService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RootFixtureService, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RootFixtureService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\nclass RootCmp {\n    outlet;\n    routerOutletData = signal(undefined);\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RootCmp, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.14\", type: RootCmp, isStandalone: true, selector: \"ng-component\", viewQueries: [{ propertyName: \"outlet\", first: true, predicate: RouterOutlet, descendants: true }], ngImport: i0, template: '<router-outlet [routerOutletData]=\"routerOutletData()\"></router-outlet>', isInline: true, dependencies: [{ kind: \"directive\", type: RouterOutlet, selector: \"router-outlet\", inputs: [\"name\", \"routerOutletData\"], outputs: [\"activate\", \"deactivate\", \"attach\", \"detach\"], exportAs: [\"outlet\"] }] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RootCmp, decorators: [{\n            type: Component,\n            args: [{\n                    template: '<router-outlet [routerOutletData]=\"routerOutletData()\"></router-outlet>',\n                    imports: [RouterOutlet],\n                }]\n        }], propDecorators: { outlet: [{\n                type: ViewChild,\n                args: [RouterOutlet]\n            }] } });\n/**\n * A testing harness for the `Router` to reduce the boilerplate needed to test routes and routed\n * components.\n *\n * @publicApi\n */\nclass RouterTestingHarness {\n    /**\n     * Creates a `RouterTestingHarness` instance.\n     *\n     * The `RouterTestingHarness` also creates its own root component with a `RouterOutlet` for the\n     * purposes of rendering route components.\n     *\n     * Throws an error if an instance has already been created.\n     * Use of this harness also requires `destroyAfterEach: true` in the `ModuleTeardownOptions`\n     *\n     * @param initialUrl The target of navigation to trigger before returning the harness.\n     */\n    static async create(initialUrl) {\n        const harness = TestBed.inject(RootFixtureService).createHarness();\n        if (initialUrl !== undefined) {\n            await harness.navigateByUrl(initialUrl);\n        }\n        return harness;\n    }\n    /**\n     * Fixture of the root component of the RouterTestingHarness\n     */\n    fixture;\n    /** @internal */\n    constructor(fixture) {\n        this.fixture = fixture;\n    }\n    /** Instructs the root fixture to run change detection. */\n    detectChanges() {\n        this.fixture.detectChanges();\n    }\n    /** The `DebugElement` of the `RouterOutlet` component. `null` if the outlet is not activated. */\n    get routeDebugElement() {\n        const outlet = this.fixture.componentInstance.outlet;\n        if (!outlet || !outlet.isActivated) {\n            return null;\n        }\n        return this.fixture.debugElement.query((v) => v.componentInstance === outlet.component);\n    }\n    /** The native element of the `RouterOutlet` component. `null` if the outlet is not activated. */\n    get routeNativeElement() {\n        return this.routeDebugElement?.nativeElement ?? null;\n    }\n    async navigateByUrl(url, requiredRoutedComponentType) {\n        const router = TestBed.inject(Router);\n        let resolveFn;\n        const redirectTrackingPromise = new Promise((resolve) => {\n            resolveFn = resolve;\n        });\n        afterNextNavigation(TestBed.inject(Router), resolveFn);\n        await router.navigateByUrl(url);\n        await redirectTrackingPromise;\n        this.fixture.detectChanges();\n        const outlet = this.fixture.componentInstance.outlet;\n        // The outlet might not be activated if the user is testing a navigation for a guard that\n        // rejects\n        if (outlet && outlet.isActivated && outlet.activatedRoute.component) {\n            const activatedComponent = outlet.component;\n            if (requiredRoutedComponentType !== undefined &&\n                !(activatedComponent instanceof requiredRoutedComponentType)) {\n                throw new Error(`Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but got ${activatedComponent.constructor.name}`);\n            }\n            return activatedComponent;\n        }\n        else {\n            if (requiredRoutedComponentType !== undefined) {\n                throw new Error(`Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but the navigation did not activate any component.`);\n            }\n            return null;\n        }\n    }\n}\n\nexport { RouterTestingHarness, RouterTestingModule, RouterOutlet as ɵɵRouterOutlet };\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,eAAe;AAClF,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,MAAM,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,YAAY,QAAQ,uBAAuB;AAC/G,SAASC,qBAAqB,IAAIC,sBAAsB,QAAQ,uBAAuB;AACvF,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,8BAA8B;AAC3G,SAASC,UAAU,IAAIC,YAAY,EAAEC,gBAAgB,IAAIC,kBAAkB,QAAQ,8BAA8B;AACjH,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,iBAAiB;AACxB,OAAO,MAAM;AACb,OAAO,gBAAgB;AACvB,OAAO,2BAA2B;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtB,OAAOC,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;IAC9B,OAAO;MACHC,QAAQ,EAAEJ,mBAAmB;MAC7BK,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEtB,MAAM;QAAEuB,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAEN;MAAO,CAAC,EAClD;QAAEI,OAAO,EAAErB,oBAAoB;QAAEuB,QAAQ,EAAEL,MAAM,GAAGA,MAAM,GAAG,CAAC;MAAE,CAAC;IAEzE,CAAC;EACL;AASJ;AAACM,oBAAA,GAlBKT,mBAAmB;AAAAU,eAAA,CAAnBV,mBAAmB,wBAAAW,6BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAU+EZ,oBAAmB;AAAA;AAAAU,eAAA,CAVrHV,mBAAmB,8BAmByDvB,EAAE,CAAAoC,gBAAA;EAAAC,IAAA,EARqBd,oBAAmB;EAAAe,OAAA,GAAYxB,YAAY;AAAA;AAAAmB,eAAA,CAX9IV,mBAAmB,8BAmByDvB,EAAE,CAAAuC,gBAAA;EAAAX,SAAA,EAPqD,CAC7Hb,gBAAgB,EAChBO,oBAAoB,CAAC,CAAC,EACtBN,cAAc,CAACC,YAAY,CAAC,CAACuB,UAAU,EACvC;IAAEX,OAAO,EAAEtB,MAAM;IAAEuB,KAAK,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAG,CAAC,CACjD;EAAAU,OAAA,GAAY3B,YAAY;AAAA;AAEjC;EAAA,QAAA4B,SAAA,oBAAAA,SAAA,KAAkF1C,EAAE,CAAA2C,iBAAA,CAAQpB,mBAAmB,EAAc,CAAC;IAClHc,IAAI,EAAEpC,QAAQ;IACd2C,IAAI,EAAE,CAAC;MACCN,OAAO,EAAE,CAACxB,YAAY,CAAC;MACvBc,SAAS,EAAE,CACPb,gBAAgB,EAChBO,oBAAoB,CAAC,CAAC,EACtBN,cAAc,CAACC,YAAY,CAAC,CAACuB,UAAU,EACvC;QAAEX,OAAO,EAAEtB,MAAM;QAAEuB,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAG,CAAC;IAEtD,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMc,kBAAkB,CAAC;EAAAC,YAAA;IAAAb,eAAA;IAAAA,eAAA;EAAA;EAGrBc,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;IACnE;IACA,IAAI,CAACD,OAAO,GAAG,IAAIE,oBAAoB,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC9D,OAAO,IAAI,CAACH,OAAO;EACvB;EACAG,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACC,OAAO,KAAKC,SAAS,EAAE;MAC5B,OAAO,IAAI,CAACD,OAAO;IACvB;IACA,IAAI,CAACA,OAAO,GAAG9C,OAAO,CAACgD,eAAe,CAACC,OAAO,CAAC;IAC/C,IAAI,CAACH,OAAO,CAACI,aAAa,CAAC,CAAC;IAC5B,OAAO,IAAI,CAACJ,OAAO;EACvB;AAGJ;AAACK,mBAAA,GApBKZ,kBAAkB;AAAAZ,eAAA,CAAlBY,kBAAkB,wBAAAa,4BAAAvB,iBAAA;EAAA,YAAAA,iBAAA,IAkBgFU,mBAAkB;AAAA;AAAAZ,eAAA,CAlBpHY,kBAAkB,+BAb0D7C,EAAE,CAAA2D,kBAAA;EAAAC,KAAA,EAgCwBf,mBAAkB;EAAAgB,OAAA,EAAlBhB,mBAAkB,CAAAiB,IAAA;EAAAC,UAAA,EAAc;AAAM;AAElJ;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAlCkF1C,EAAE,CAAA2C,iBAAA,CAkCQE,kBAAkB,EAAc,CAAC;IACjHR,IAAI,EAAElC,UAAU;IAChByC,IAAI,EAAE,CAAC;MAAEmB,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV,MAAMR,OAAO,CAAC;EAAAT,YAAA;IAAAb,eAAA;IAAAA,eAAA,2BAES/B,MAAM,CAACmD,SAAS,CAAC;EAAA;AAGxC;AAACW,QAAA,GALKT,OAAO;AAAAtB,eAAA,CAAPsB,OAAO,wBAAAU,iBAAA9B,iBAAA;EAAA,YAAAA,iBAAA,IAG2FoB,QAAO;AAAA;AAAAtB,eAAA,CAHzGsB,OAAO,8BAtCqEvD,EAAE,CAAAkE,iBAAA;EAAA7B,IAAA,EA0CQkB,QAAO;EAAAY,SAAA;EAAAC,SAAA,WAAAC,eAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA1CjBtE,EAAE,CAAAwE,WAAA,CA0C+H7D,YAAY;IAAA;IAAA,IAAA2D,EAAA;MAAA,IAAAG,EAAA;MA1C7IzE,EAAE,CAAA0E,cAAA,CAAAD,EAAA,GAAFzE,EAAE,CAAA2E,WAAA,QAAAJ,GAAA,CAAAK,MAAA,GAAAH,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,kBAAAZ,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFtE,EAAE,CAAAmF,SAAA,sBA0CkQ,CAAC;IAAA;IAAA,IAAAb,EAAA;MA1CrQtE,EAAE,CAAAoF,UAAA,qBAAAb,GAAA,CAAAc,gBAAA,EA0CiP,CAAC;IAAA;EAAA;EAAAC,YAAA,GAA8E3E,YAAY;EAAA4E,aAAA;AAAA;AAEha;EAAA,QAAA7C,SAAA,oBAAAA,SAAA,KA5CkF1C,EAAE,CAAA2C,iBAAA,CA4CQY,OAAO,EAAc,CAAC;IACtGlB,IAAI,EAAEhC,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCqC,QAAQ,EAAE,yEAAyE;MACnFxC,OAAO,EAAE,CAAC9B,YAAY;IAC1B,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEiE,MAAM,EAAE,CAAC;MACvBvC,IAAI,EAAEjC,SAAS;MACfwC,IAAI,EAAE,CAACjC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuC,oBAAoB,CAAC;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAasC,MAAMA,CAACC,UAAU,EAAE;IAAA,OAAAC,iBAAA;MAC5B,MAAM1C,OAAO,GAAG1C,OAAO,CAACqF,MAAM,CAAC9C,kBAAkB,CAAC,CAACE,aAAa,CAAC,CAAC;MAClE,IAAI0C,UAAU,KAAKpC,SAAS,EAAE;QAC1B,MAAML,OAAO,CAAC4C,aAAa,CAACH,UAAU,CAAC;MAC3C;MACA,OAAOzC,OAAO;IAAC;EACnB;EACA;AACJ;AACA;;EAEI;EACAF,WAAWA,CAACM,OAAO,EAAE;IAAAnB,eAAA;IACjB,IAAI,CAACmB,OAAO,GAAGA,OAAO;EAC1B;EACA;EACAI,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACJ,OAAO,CAACI,aAAa,CAAC,CAAC;EAChC;EACA;EACA,IAAIqC,iBAAiBA,CAAA,EAAG;IACpB,MAAMjB,MAAM,GAAG,IAAI,CAACxB,OAAO,CAAC0C,iBAAiB,CAAClB,MAAM;IACpD,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACmB,WAAW,EAAE;MAChC,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAAC3C,OAAO,CAAC4C,YAAY,CAACC,KAAK,CAAEC,CAAC,IAAKA,CAAC,CAACJ,iBAAiB,KAAKlB,MAAM,CAACuB,SAAS,CAAC;EAC3F;EACA;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACrB,QAAAD,qBAAA,IAAAC,sBAAA,GAAO,IAAI,CAACT,iBAAiB,cAAAS,sBAAA,uBAAtBA,sBAAA,CAAwBC,aAAa,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,IAAI;EACxD;EACMT,aAAaA,CAACY,GAAG,EAAEC,2BAA2B,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAhB,iBAAA;MAClD,MAAMiB,MAAM,GAAGrG,OAAO,CAACqF,MAAM,CAAClF,MAAM,CAAC;MACrC,IAAImG,SAAS;MACb,MAAMC,uBAAuB,GAAG,IAAIC,OAAO,CAAEC,OAAO,IAAK;QACrDH,SAAS,GAAGG,OAAO;MACvB,CAAC,CAAC;MACFrG,mBAAmB,CAACJ,OAAO,CAACqF,MAAM,CAAClF,MAAM,CAAC,EAAEmG,SAAS,CAAC;MACtD,MAAMD,MAAM,CAACf,aAAa,CAACY,GAAG,CAAC;MAC/B,MAAMK,uBAAuB;MAC7BH,KAAI,CAACtD,OAAO,CAACI,aAAa,CAAC,CAAC;MAC5B,MAAMoB,MAAM,GAAG8B,KAAI,CAACtD,OAAO,CAAC0C,iBAAiB,CAAClB,MAAM;MACpD;MACA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAACmB,WAAW,IAAInB,MAAM,CAACoC,cAAc,CAACb,SAAS,EAAE;QACjE,MAAMc,kBAAkB,GAAGrC,MAAM,CAACuB,SAAS;QAC3C,IAAIM,2BAA2B,KAAKpD,SAAS,IACzC,EAAE4D,kBAAkB,YAAYR,2BAA2B,CAAC,EAAE;UAC9D,MAAM,IAAIxD,KAAK,CAAC,8CAA8CwD,2BAA2B,CAACS,IAAI,YAAYD,kBAAkB,CAACnE,WAAW,CAACoE,IAAI,EAAE,CAAC;QACpJ;QACA,OAAOD,kBAAkB;MAC7B,CAAC,MACI;QACD,IAAIR,2BAA2B,KAAKpD,SAAS,EAAE;UAC3C,MAAM,IAAIJ,KAAK,CAAC,8CAA8CwD,2BAA2B,CAACS,IAAI,qDAAqD,CAAC;QACxJ;QACA,OAAO,IAAI;MACf;IAAC;EACL;AACJ;AAEA,SAAShE,oBAAoB,EAAE3B,mBAAmB,EAAEZ,YAAY,IAAIwG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}