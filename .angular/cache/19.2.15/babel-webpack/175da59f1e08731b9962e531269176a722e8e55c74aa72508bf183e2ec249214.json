{"ast": null, "code": "var _SwuiTdPercentEditableWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./percent-editable.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./percent-editable.widget.scss?ngResource\";\nimport { Component, Inject, ViewChild } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nconst MIN = 0;\nconst MAX = 100;\nconst STEP = 0.1;\nlet SwuiTdPercentEditableWidgetComponent = (_SwuiTdPercentEditableWidgetComponent = class SwuiTdPercentEditableWidgetComponent {\n  constructor({\n    row,\n    field,\n    action,\n    schema,\n    value\n  }) {\n    var _schema$td, _schema$td2;\n    this.mode = 'view';\n    this.loading = false;\n    this.action = action;\n    this.field = field;\n    this.row = row;\n    this.formatted = schema.td && 'formatted' in schema.td ? schema.td.formatted || false : true;\n    this.remoteSave = ((_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.remoteSave) || false;\n    this.showUndefinedWarning = ((_schema$td2 = schema.td) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.showUndefinedWarning) || false;\n    this.delimiter = schema.delimiter;\n    this.fractionCount = schema.fractionCount || 0;\n    this.value = value || 0;\n    if (!this.formatted) {\n      this.value *= 100;\n    }\n    this._percentValue = parseFloat(this.sanitizeValue(this.value).toFixed(this.fractionCount));\n    this.initialValue = this.percentValue;\n  }\n  set percentValue(value) {\n    this._percentValue = parseFloat(this.sanitizeValue(value).toFixed(this.fractionCount));\n  }\n  get percentValue() {\n    return this._percentValue;\n  }\n  onEditClick(event) {\n    event.preventDefault();\n    this.mode = 'edit';\n  }\n  valueDown() {\n    this.percentValue -= STEP;\n  }\n  valueUp() {\n    this.percentValue += STEP;\n  }\n  valueChanged() {\n    if (this.input) {\n      this.percentValue = this.input.nativeElement.value;\n    }\n  }\n  saveChanges() {\n    if (this.initialValue === this.percentValue) {\n      this.mode = 'view';\n      return;\n    }\n    this.value = this.formatted ? this.percentValue : this.percentValue / 100;\n    if (this.remoteSave) {\n      this.loading = true;\n    }\n    this.action.emit({\n      field: this.field,\n      row: this.row,\n      payload: {\n        value: this.percentValue,\n        onCompleteFn: () => {\n          this.loading = false;\n          this.mode = 'view';\n        }\n      }\n    });\n  }\n  onKeyPress() {\n    this.valueChanged();\n    this.saveChanges();\n  }\n  sanitizeValue(value) {\n    if (typeof value === 'string') {\n      value = +parseFloat(value).toFixed(this.fractionCount);\n    }\n    if (isNaN(value)) {\n      value = 0;\n    }\n    if (value > MAX) {\n      value = MAX;\n    }\n    if (value < MIN) {\n      value = MIN;\n    }\n    return value;\n  }\n}, _SwuiTdPercentEditableWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdPercentEditableWidgetComponent.propDecorators = {\n  input: [{\n    type: ViewChild,\n    args: ['input']\n  }]\n}, _SwuiTdPercentEditableWidgetComponent);\nSwuiTdPercentEditableWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-percent-editable-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdPercentEditableWidgetComponent);\nexport { SwuiTdPercentEditableWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "ViewChild", "SWUI_GRID_WIDGET_CONFIG", "MIN", "MAX", "STEP", "SwuiTdPercentEditableWidgetComponent", "_SwuiTdPercentEditableWidgetComponent", "constructor", "row", "field", "action", "schema", "value", "_schema$td", "_schema$td2", "mode", "loading", "formatted", "td", "remoteSave", "showUndefinedWarning", "delimiter", "fractionCount", "_percentValue", "parseFloat", "sanitizeValue", "toFixed", "initialValue", "percentValue", "onEditClick", "event", "preventDefault", "valueDown", "valueUp", "valueChanged", "input", "nativeElement", "saveChanges", "emit", "payload", "onCompleteFn", "onKeyPress", "isNaN", "ctorParameters", "type", "undefined", "decorators", "args", "propDecorators", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/percent-editable/percent-editable.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./percent-editable.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./percent-editable.widget.scss?ngResource\";\nimport { Component, Inject, ViewChild } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nconst MIN = 0;\nconst MAX = 100;\nconst STEP = 0.1;\nlet SwuiTdPercentEditableWidgetComponent = class SwuiTdPercentEditableWidgetComponent {\n    constructor({ row, field, action, schema, value }) {\n        this.mode = 'view';\n        this.loading = false;\n        this.action = action;\n        this.field = field;\n        this.row = row;\n        this.formatted = schema.td && 'formatted' in schema.td ? schema.td.formatted || false : true;\n        this.remoteSave = schema.td?.remoteSave || false;\n        this.showUndefinedWarning = schema.td?.showUndefinedWarning || false;\n        this.delimiter = schema.delimiter;\n        this.fractionCount = schema.fractionCount || 0;\n        this.value = value || 0;\n        if (!this.formatted) {\n            this.value *= 100;\n        }\n        this._percentValue = parseFloat(this.sanitizeValue(this.value).toFixed(this.fractionCount));\n        this.initialValue = this.percentValue;\n    }\n    set percentValue(value) {\n        this._percentValue = parseFloat(this.sanitizeValue(value).toFixed(this.fractionCount));\n    }\n    get percentValue() {\n        return this._percentValue;\n    }\n    onEditClick(event) {\n        event.preventDefault();\n        this.mode = 'edit';\n    }\n    valueDown() {\n        this.percentValue -= STEP;\n    }\n    valueUp() {\n        this.percentValue += STEP;\n    }\n    valueChanged() {\n        if (this.input) {\n            this.percentValue = this.input.nativeElement.value;\n        }\n    }\n    saveChanges() {\n        if (this.initialValue === this.percentValue) {\n            this.mode = 'view';\n            return;\n        }\n        this.value = this.formatted ? this.percentValue : this.percentValue / 100;\n        if (this.remoteSave) {\n            this.loading = true;\n        }\n        this.action.emit({\n            field: this.field,\n            row: this.row,\n            payload: {\n                value: this.percentValue,\n                onCompleteFn: () => {\n                    this.loading = false;\n                    this.mode = 'view';\n                }\n            }\n        });\n    }\n    onKeyPress() {\n        this.valueChanged();\n        this.saveChanges();\n    }\n    sanitizeValue(value) {\n        if (typeof value === 'string') {\n            value = +parseFloat(value).toFixed(this.fractionCount);\n        }\n        if (isNaN(value)) {\n            value = 0;\n        }\n        if (value > MAX) {\n            value = MAX;\n        }\n        if (value < MIN) {\n            value = MIN;\n        }\n        return value;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n    static { this.propDecorators = {\n        input: [{ type: ViewChild, args: ['input',] }]\n    }; }\n};\nSwuiTdPercentEditableWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-percent-editable-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdPercentEditableWidgetComponent);\nexport { SwuiTdPercentEditableWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,SAASC,SAAS,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAC5D,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,MAAMC,GAAG,GAAG,CAAC;AACb,MAAMC,GAAG,GAAG,GAAG;AACf,MAAMC,IAAI,GAAG,GAAG;AAChB,IAAIC,oCAAoC,IAAAC,qCAAA,GAAG,MAAMD,oCAAoC,CAAC;EAClFE,WAAWA,CAAC;IAAEC,GAAG;IAAEC,KAAK;IAAEC,MAAM;IAAEC,MAAM;IAAEC;EAAM,CAAC,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA;IAC/C,IAAI,CAACC,IAAI,GAAG,MAAM;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACN,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACS,SAAS,GAAGN,MAAM,CAACO,EAAE,IAAI,WAAW,IAAIP,MAAM,CAACO,EAAE,GAAGP,MAAM,CAACO,EAAE,CAACD,SAAS,IAAI,KAAK,GAAG,IAAI;IAC5F,IAAI,CAACE,UAAU,GAAG,EAAAN,UAAA,GAAAF,MAAM,CAACO,EAAE,cAAAL,UAAA,uBAATA,UAAA,CAAWM,UAAU,KAAI,KAAK;IAChD,IAAI,CAACC,oBAAoB,GAAG,EAAAN,WAAA,GAAAH,MAAM,CAACO,EAAE,cAAAJ,WAAA,uBAATA,WAAA,CAAWM,oBAAoB,KAAI,KAAK;IACpE,IAAI,CAACC,SAAS,GAAGV,MAAM,CAACU,SAAS;IACjC,IAAI,CAACC,aAAa,GAAGX,MAAM,CAACW,aAAa,IAAI,CAAC;IAC9C,IAAI,CAACV,KAAK,GAAGA,KAAK,IAAI,CAAC;IACvB,IAAI,CAAC,IAAI,CAACK,SAAS,EAAE;MACjB,IAAI,CAACL,KAAK,IAAI,GAAG;IACrB;IACA,IAAI,CAACW,aAAa,GAAGC,UAAU,CAAC,IAAI,CAACC,aAAa,CAAC,IAAI,CAACb,KAAK,CAAC,CAACc,OAAO,CAAC,IAAI,CAACJ,aAAa,CAAC,CAAC;IAC3F,IAAI,CAACK,YAAY,GAAG,IAAI,CAACC,YAAY;EACzC;EACA,IAAIA,YAAYA,CAAChB,KAAK,EAAE;IACpB,IAAI,CAACW,aAAa,GAAGC,UAAU,CAAC,IAAI,CAACC,aAAa,CAACb,KAAK,CAAC,CAACc,OAAO,CAAC,IAAI,CAACJ,aAAa,CAAC,CAAC;EAC1F;EACA,IAAIM,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACL,aAAa;EAC7B;EACAM,WAAWA,CAACC,KAAK,EAAE;IACfA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAAChB,IAAI,GAAG,MAAM;EACtB;EACAiB,SAASA,CAAA,EAAG;IACR,IAAI,CAACJ,YAAY,IAAIxB,IAAI;EAC7B;EACA6B,OAAOA,CAAA,EAAG;IACN,IAAI,CAACL,YAAY,IAAIxB,IAAI;EAC7B;EACA8B,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,IAAI,CAACP,YAAY,GAAG,IAAI,CAACO,KAAK,CAACC,aAAa,CAACxB,KAAK;IACtD;EACJ;EACAyB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACV,YAAY,KAAK,IAAI,CAACC,YAAY,EAAE;MACzC,IAAI,CAACb,IAAI,GAAG,MAAM;MAClB;IACJ;IACA,IAAI,CAACH,KAAK,GAAG,IAAI,CAACK,SAAS,GAAG,IAAI,CAACW,YAAY,GAAG,IAAI,CAACA,YAAY,GAAG,GAAG;IACzE,IAAI,IAAI,CAACT,UAAU,EAAE;MACjB,IAAI,CAACH,OAAO,GAAG,IAAI;IACvB;IACA,IAAI,CAACN,MAAM,CAAC4B,IAAI,CAAC;MACb7B,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBD,GAAG,EAAE,IAAI,CAACA,GAAG;MACb+B,OAAO,EAAE;QACL3B,KAAK,EAAE,IAAI,CAACgB,YAAY;QACxBY,YAAY,EAAEA,CAAA,KAAM;UAChB,IAAI,CAACxB,OAAO,GAAG,KAAK;UACpB,IAAI,CAACD,IAAI,GAAG,MAAM;QACtB;MACJ;IACJ,CAAC,CAAC;EACN;EACA0B,UAAUA,CAAA,EAAG;IACT,IAAI,CAACP,YAAY,CAAC,CAAC;IACnB,IAAI,CAACG,WAAW,CAAC,CAAC;EACtB;EACAZ,aAAaA,CAACb,KAAK,EAAE;IACjB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3BA,KAAK,GAAG,CAACY,UAAU,CAACZ,KAAK,CAAC,CAACc,OAAO,CAAC,IAAI,CAACJ,aAAa,CAAC;IAC1D;IACA,IAAIoB,KAAK,CAAC9B,KAAK,CAAC,EAAE;MACdA,KAAK,GAAG,CAAC;IACb;IACA,IAAIA,KAAK,GAAGT,GAAG,EAAE;MACbS,KAAK,GAAGT,GAAG;IACf;IACA,IAAIS,KAAK,GAAGV,GAAG,EAAE;MACbU,KAAK,GAAGV,GAAG;IACf;IACA,OAAOU,KAAK;EAChB;AAOJ,CAAC,EANYN,qCAAA,CAAKqC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAE7C,MAAM;IAAEgD,IAAI,EAAE,CAAC9C,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EACQK,qCAAA,CAAK0C,cAAc,GAAG;EAC3Bb,KAAK,EAAE,CAAC;IAAES,IAAI,EAAE5C,SAAS;IAAE+C,IAAI,EAAE,CAAC,OAAO;EAAG,CAAC;AACjD,CAAC,EAAAzC,qCAAA,CACJ;AACDD,oCAAoC,GAAGV,UAAU,CAAC,CAC9CG,SAAS,CAAC;EACNmD,QAAQ,EAAE,qCAAqC;EAC/CC,QAAQ,EAAEtD,oBAAoB;EAC9BuD,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACvD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEQ,oCAAoC,CAAC;AACxC,SAASA,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}