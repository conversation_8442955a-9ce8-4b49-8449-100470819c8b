{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageSelectorModule } from '../language-selector/language-selector.module';\nimport { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';\nimport { UserMenuComponent } from './user-menu.component';\nlet UserMenuModule = class UserMenuModule {};\nUserMenuModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, MatRippleModule, MatIconModule, MatMenuModule, MatDividerModule, LanguageSelectorModule, SwuiSettingsDialogModule, MatTooltipModule],\n  declarations: [UserMenuComponent],\n  exports: [UserMenuComponent]\n})], UserMenuModule);\nexport { UserMenuModule };", "map": {"version": 3, "names": ["CommonModule", "NgModule", "MatRippleModule", "MatDividerModule", "MatIconModule", "MatMenuModule", "MatTooltipModule", "TranslateModule", "LanguageSelectorModule", "SwuiSettingsDialogModule", "UserMenuComponent", "UserMenuModule", "__decorate", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/user-menu/user-menu.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageSelectorModule } from '../language-selector/language-selector.module';\nimport { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';\nimport { UserMenuComponent } from './user-menu.component';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    MatRippleModule,\n    MatIconModule,\n    MatMenuModule,\n    MatDividerModule,\n    LanguageSelectorModule,\n    SwuiSettingsDialogModule,\n    MatTooltipModule\n  ],\n  declarations: [UserMenuComponent],\n  exports: [UserMenuComponent],\n})\nexport class UserMenuModule {\n}\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,sBAAsB,QAAQ,+CAA+C;AACtF,SAASC,wBAAwB,QAAQ,gDAAgD;AACzF,SAASC,iBAAiB,QAAQ,uBAAuB;AAkBlD,IAAMC,cAAc,GAApB,MAAMA,cAAc,GAC1B;AADYA,cAAc,GAAAC,UAAA,EAf1BX,QAAQ,CAAC;EACRY,OAAO,EAAE,CACPb,YAAY,EACZO,eAAe,EACfL,eAAe,EACfE,aAAa,EACbC,aAAa,EACbF,gBAAgB,EAChBK,sBAAsB,EACtBC,wBAAwB,EACxBH,gBAAgB,CACjB;EACDQ,YAAY,EAAE,CAACJ,iBAAiB,CAAC;EACjCK,OAAO,EAAE,CAACL,iBAAiB;CAC5B,CAAC,C,EACWC,cAAc,CAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}