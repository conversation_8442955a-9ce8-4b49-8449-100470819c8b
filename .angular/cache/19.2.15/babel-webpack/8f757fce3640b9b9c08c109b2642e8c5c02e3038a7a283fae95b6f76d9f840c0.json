{"ast": null, "code": "import { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { TestBed, waitForAsync } from '@angular/core/testing';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { LanguageSelectorModule } from '../language-selector/language-selector.module';\nimport { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';\nimport { UserMenuComponent } from './user-menu.component';\ndescribe('UserMenuComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      schemas: [NO_ERRORS_SCHEMA],\n      declarations: [UserMenuComponent],\n      imports: [TranslateModule.forRoot(), MatRippleModule, MatIconModule, MatMenuModule, MatDividerModule, MatDialogModule, LanguageSelectorModule, SwuiSettingsDialogModule],\n      providers: [{\n        provide: MatDialog,\n        useValue: {\n          open: () => {}\n        }\n      }, {\n        provide: SwHubConfigService,\n        useValue: {}\n      }, {\n        provide: SWUI_HUB_MESSAGE_CONFIG,\n        useValue: {}\n      }, SwHubAuthService]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(UserMenuComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["NO_ERRORS_SCHEMA", "TestBed", "waitForAsync", "MatRippleModule", "MatDialog", "MatDialogModule", "MatDividerModule", "MatIconModule", "MatMenuModule", "TranslateModule", "SwHubAuthService", "SwHubConfigService", "SWUI_HUB_MESSAGE_CONFIG", "LanguageSelectorModule", "SwuiSettingsDialogModule", "UserMenuComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "schemas", "declarations", "imports", "forRoot", "providers", "provide", "useValue", "open", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/user-menu/user-menu.component.spec.ts"], "sourcesContent": ["import { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { LanguageSelectorModule } from '../language-selector/language-selector.module';\nimport { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';\nimport { UserMenuComponent } from './user-menu.component';\n\n\ndescribe('UserMenuComponent', () => {\n  let component: UserMenuComponent;\n  let fixture: ComponentFixture<UserMenuComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      schemas: [NO_ERRORS_SCHEMA],\n      declarations: [UserMenuComponent],\n      imports: [\n        TranslateModule.forRoot(),\n        MatRippleModule,\n        MatIconModule,\n        MatMenuModule,\n        MatDividerModule,\n        MatDialogModule,\n        LanguageSelectorModule,\n        SwuiSettingsDialogModule\n      ],\n      providers: [\n        {\n          provide: MatDialog, useValue: {\n            open: () => {\n            }\n          }\n        },\n        { provide: SwHubConfigService, useValue: {} },\n        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: {} },\n        SwHubAuthService,\n      ]\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(UserMenuComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,eAAe;AAChD,SAA2BC,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAC/E,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,SAAS,EAAEC,eAAe,QAAQ,0BAA0B;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,sBAAsB,QAAQ,+CAA+C;AACtF,SAASC,wBAAwB,QAAQ,gDAAgD;AACzF,SAASC,iBAAiB,QAAQ,uBAAuB;AAGzDC,QAAQ,CAAC,mBAAmB,EAAE,MAAK;EACjC,IAAIC,SAA4B;EAChC,IAAIC,OAA4C;EAEhDC,UAAU,CAACjB,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACmB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACrB,gBAAgB,CAAC;MAC3BsB,YAAY,EAAE,CAACP,iBAAiB,CAAC;MACjCQ,OAAO,EAAE,CACPd,eAAe,CAACe,OAAO,EAAE,EACzBrB,eAAe,EACfI,aAAa,EACbC,aAAa,EACbF,gBAAgB,EAChBD,eAAe,EACfQ,sBAAsB,EACtBC,wBAAwB,CACzB;MACDW,SAAS,EAAE,CACT;QACEC,OAAO,EAAEtB,SAAS;QAAEuB,QAAQ,EAAE;UAC5BC,IAAI,EAAEA,CAAA,KAAK,CACX;;OAEH,EACD;QAAEF,OAAO,EAAEf,kBAAkB;QAAEgB,QAAQ,EAAE;MAAE,CAAE,EAC7C;QAAED,OAAO,EAAEd,uBAAuB;QAAEe,QAAQ,EAAE;MAAE,CAAE,EAClDjB,gBAAgB;KAEnB,CAAC,CACCmB,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHV,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGjB,OAAO,CAAC6B,eAAe,CAACf,iBAAiB,CAAC;IACpDE,SAAS,GAAGC,OAAO,CAACa,iBAAiB;IACrCb,OAAO,CAACc,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACjB,SAAS,CAAC,CAACkB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}