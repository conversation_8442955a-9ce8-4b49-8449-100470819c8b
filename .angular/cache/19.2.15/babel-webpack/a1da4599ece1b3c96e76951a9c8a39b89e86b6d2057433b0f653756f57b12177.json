{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\nexport function bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler).apply(this, args).pipe(mapOneOrManyArgs(resultSelector));\n      };\n    }\n  }\n  if (scheduler) {\n    return function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return bindCallbackInternals(isNodeStyle, callbackFunc).apply(this, args).pipe(subscribeOn(scheduler), observeOn(scheduler));\n    };\n  }\n  return function () {\n    var _this = this;\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var subject = new AsyncSubject();\n    var uninitialized = true;\n    return new Observable(function (subscriber) {\n      var subs = subject.subscribe(subscriber);\n      if (uninitialized) {\n        uninitialized = false;\n        var isAsync_1 = false;\n        var isComplete_1 = false;\n        callbackFunc.apply(_this, __spreadArray(__spreadArray([], __read(args)), [function () {\n          var results = [];\n          for (var _i = 0; _i < arguments.length; _i++) {\n            results[_i] = arguments[_i];\n          }\n          if (isNodeStyle) {\n            var err = results.shift();\n            if (err != null) {\n              subject.error(err);\n              return;\n            }\n          }\n          subject.next(1 < results.length ? results : results[0]);\n          isComplete_1 = true;\n          if (isAsync_1) {\n            subject.complete();\n          }\n        }]));\n        if (isComplete_1) {\n          subject.complete();\n        }\n        isAsync_1 = true;\n      }\n      return subs;\n    });\n  };\n}\n//# sourceMappingURL=bindCallbackInternals.js.map", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "isScheduler", "Observable", "subscribeOn", "mapOneOrManyArgs", "observeOn", "AsyncSubject", "bindCallbackInternals", "isNodeStyle", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler", "args", "_i", "arguments", "length", "apply", "pipe", "_this", "subject", "uninitialized", "subscriber", "subs", "subscribe", "isAsync_1", "isComplete_1", "results", "err", "shift", "error", "next", "complete"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/bindCallbackInternals.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\nexport function bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n    if (resultSelector) {\n        if (isScheduler(resultSelector)) {\n            scheduler = resultSelector;\n        }\n        else {\n            return function () {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler)\n                    .apply(this, args)\n                    .pipe(mapOneOrManyArgs(resultSelector));\n            };\n        }\n    }\n    if (scheduler) {\n        return function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return bindCallbackInternals(isNodeStyle, callbackFunc)\n                .apply(this, args)\n                .pipe(subscribeOn(scheduler), observeOn(scheduler));\n        };\n    }\n    return function () {\n        var _this = this;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var subject = new AsyncSubject();\n        var uninitialized = true;\n        return new Observable(function (subscriber) {\n            var subs = subject.subscribe(subscriber);\n            if (uninitialized) {\n                uninitialized = false;\n                var isAsync_1 = false;\n                var isComplete_1 = false;\n                callbackFunc.apply(_this, __spreadArray(__spreadArray([], __read(args)), [\n                    function () {\n                        var results = [];\n                        for (var _i = 0; _i < arguments.length; _i++) {\n                            results[_i] = arguments[_i];\n                        }\n                        if (isNodeStyle) {\n                            var err = results.shift();\n                            if (err != null) {\n                                subject.error(err);\n                                return;\n                            }\n                        }\n                        subject.next(1 < results.length ? results : results[0]);\n                        isComplete_1 = true;\n                        if (isAsync_1) {\n                            subject.complete();\n                        }\n                    },\n                ]));\n                if (isComplete_1) {\n                    subject.complete();\n                }\n                isAsync_1 = true;\n            }\n            return subs;\n        });\n    };\n}\n//# sourceMappingURL=bindCallbackInternals.js.map"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,SAASC,qBAAqBA,CAACC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,SAAS,EAAE;EACxF,IAAID,cAAc,EAAE;IAChB,IAAIT,WAAW,CAACS,cAAc,CAAC,EAAE;MAC7BC,SAAS,GAAGD,cAAc;IAC9B,CAAC,MACI;MACD,OAAO,YAAY;QACf,IAAIE,IAAI,GAAG,EAAE;QACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;UAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;QAC5B;QACA,OAAON,qBAAqB,CAACC,WAAW,EAAEC,YAAY,EAAEE,SAAS,CAAC,CAC7DK,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,CACjBK,IAAI,CAACb,gBAAgB,CAACM,cAAc,CAAC,CAAC;MAC/C,CAAC;IACL;EACJ;EACA,IAAIC,SAAS,EAAE;IACX,OAAO,YAAY;MACf,IAAIC,IAAI,GAAG,EAAE;MACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;QAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;MAC5B;MACA,OAAON,qBAAqB,CAACC,WAAW,EAAEC,YAAY,CAAC,CAClDO,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,CACjBK,IAAI,CAACd,WAAW,CAACQ,SAAS,CAAC,EAAEN,SAAS,CAACM,SAAS,CAAC,CAAC;IAC3D,CAAC;EACL;EACA,OAAO,YAAY;IACf,IAAIO,KAAK,GAAG,IAAI;IAChB,IAAIN,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC5B;IACA,IAAIM,OAAO,GAAG,IAAIb,YAAY,CAAC,CAAC;IAChC,IAAIc,aAAa,GAAG,IAAI;IACxB,OAAO,IAAIlB,UAAU,CAAC,UAAUmB,UAAU,EAAE;MACxC,IAAIC,IAAI,GAAGH,OAAO,CAACI,SAAS,CAACF,UAAU,CAAC;MACxC,IAAID,aAAa,EAAE;QACfA,aAAa,GAAG,KAAK;QACrB,IAAII,SAAS,GAAG,KAAK;QACrB,IAAIC,YAAY,GAAG,KAAK;QACxBhB,YAAY,CAACO,KAAK,CAACE,KAAK,EAAElB,aAAa,CAACA,aAAa,CAAC,EAAE,EAAED,MAAM,CAACa,IAAI,CAAC,CAAC,EAAE,CACrE,YAAY;UACR,IAAIc,OAAO,GAAG,EAAE;UAChB,KAAK,IAAIb,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;YAC1Ca,OAAO,CAACb,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;UAC/B;UACA,IAAIL,WAAW,EAAE;YACb,IAAImB,GAAG,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC;YACzB,IAAID,GAAG,IAAI,IAAI,EAAE;cACbR,OAAO,CAACU,KAAK,CAACF,GAAG,CAAC;cAClB;YACJ;UACJ;UACAR,OAAO,CAACW,IAAI,CAAC,CAAC,GAAGJ,OAAO,CAACX,MAAM,GAAGW,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,CAAC;UACvDD,YAAY,GAAG,IAAI;UACnB,IAAID,SAAS,EAAE;YACXL,OAAO,CAACY,QAAQ,CAAC,CAAC;UACtB;QACJ,CAAC,CACJ,CAAC,CAAC;QACH,IAAIN,YAAY,EAAE;UACdN,OAAO,CAACY,QAAQ,CAAC,CAAC;QACtB;QACAP,SAAS,GAAG,IAAI;MACpB;MACA,OAAOF,IAAI;IACf,CAAC,CAAC;EACN,CAAC;AACL;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}