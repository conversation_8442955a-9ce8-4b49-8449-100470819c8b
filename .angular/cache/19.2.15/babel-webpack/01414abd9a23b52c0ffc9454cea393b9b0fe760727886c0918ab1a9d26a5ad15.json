{"ast": null, "code": "import { L as ListKeyManager } from './list-key-manager-CyOIXo8P.mjs';\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\nexport { ActiveDescendantKeyManager as A };\n//# sourceMappingURL=activedescendant-key-manager-DC3-fwQI.mjs.map", "map": {"version": 3, "names": ["L", "ListKeyManager", "ActiveDescendantKeyManager", "setActiveItem", "index", "activeItem", "setInactiveStyles", "setActiveStyles", "A"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/activedescendant-key-manager-DC3-fwQI.mjs"], "sourcesContent": ["import { L as ListKeyManager } from './list-key-manager-CyOIXo8P.mjs';\n\nclass ActiveDescendantKeyManager extends ListKeyManager {\n    setActiveItem(index) {\n        if (this.activeItem) {\n            this.activeItem.setInactiveStyles();\n        }\n        super.setActiveItem(index);\n        if (this.activeItem) {\n            this.activeItem.setActiveStyles();\n        }\n    }\n}\n\nexport { ActiveDescendantKeyManager as A };\n//# sourceMappingURL=activedescendant-key-manager-DC3-fwQI.mjs.map\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,cAAc,QAAQ,iCAAiC;AAErE,MAAMC,0BAA0B,SAASD,cAAc,CAAC;EACpDE,aAAaA,CAACC,KAAK,EAAE;IACjB,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACC,iBAAiB,CAAC,CAAC;IACvC;IACA,KAAK,CAACH,aAAa,CAACC,KAAK,CAAC;IAC1B,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACE,eAAe,CAAC,CAAC;IACrC;EACJ;AACJ;AAEA,SAASL,0BAA0B,IAAIM,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}