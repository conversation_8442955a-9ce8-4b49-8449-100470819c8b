{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\n/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nclass _ErrorStateTracker {\n  constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {\n    _defineProperty(this, \"_defaultMatcher\", void 0);\n    _defineProperty(this, \"ngControl\", void 0);\n    _defineProperty(this, \"_parentFormGroup\", void 0);\n    _defineProperty(this, \"_parentForm\", void 0);\n    _defineProperty(this, \"_stateChanges\", void 0);\n    /** Whether the tracker is currently in an error state. */\n    _defineProperty(this, \"errorState\", false);\n    /** User-defined matcher for the error state. */\n    _defineProperty(this, \"matcher\", void 0);\n    this._defaultMatcher = _defaultMatcher;\n    this.ngControl = ngControl;\n    this._parentFormGroup = _parentFormGroup;\n    this._parentForm = _parentForm;\n    this._stateChanges = _stateChanges;\n  }\n  /** Updates the error state based on the provided error state matcher. */\n  updateErrorState() {\n    var _matcher$isErrorState;\n    const oldState = this.errorState;\n    const parent = this._parentFormGroup || this._parentForm;\n    const matcher = this.matcher || this._defaultMatcher;\n    const control = this.ngControl ? this.ngControl.control : null;\n    const newState = (_matcher$isErrorState = matcher === null || matcher === void 0 ? void 0 : matcher.isErrorState(control, parent)) !== null && _matcher$isErrorState !== void 0 ? _matcher$isErrorState : false;\n    if (newState !== oldState) {\n      this.errorState = newState;\n      this._stateChanges.next();\n    }\n  }\n}\nexport { _ErrorStateTracker as _ };", "map": {"version": 3, "names": ["_ErrorStateTracker", "constructor", "_defaultMatcher", "ngControl", "_parentFormGroup", "_parentForm", "_stateChanges", "_defineProperty", "updateErrorState", "_matcher$isErrorState", "oldState", "errorState", "parent", "matcher", "control", "newState", "isErrorState", "next", "_"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/error-state-Dtb1IHM-.mjs"], "sourcesContent": ["/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nclass _ErrorStateTracker {\n    _defaultMatcher;\n    ngControl;\n    _parentFormGroup;\n    _parentForm;\n    _stateChanges;\n    /** Whether the tracker is currently in an error state. */\n    errorState = false;\n    /** User-defined matcher for the error state. */\n    matcher;\n    constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {\n        this._defaultMatcher = _defaultMatcher;\n        this.ngControl = ngControl;\n        this._parentFormGroup = _parentFormGroup;\n        this._parentForm = _parentForm;\n        this._stateChanges = _stateChanges;\n    }\n    /** Updates the error state based on the provided error state matcher. */\n    updateErrorState() {\n        const oldState = this.errorState;\n        const parent = this._parentFormGroup || this._parentForm;\n        const matcher = this.matcher || this._defaultMatcher;\n        const control = this.ngControl ? this.ngControl.control : null;\n        const newState = matcher?.isErrorState(control, parent) ?? false;\n        if (newState !== oldState) {\n            this.errorState = newState;\n            this._stateChanges.next();\n        }\n    }\n}\n\nexport { _ErrorStateTracker as _ };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,MAAMA,kBAAkB,CAAC;EAUrBC,WAAWA,CAACC,eAAe,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,aAAa,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAJtF;IAAAA,eAAA,qBACa,KAAK;IAClB;IAAAA,eAAA;IAGI,IAAI,CAACL,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACA;EACAE,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACf,MAAMC,QAAQ,GAAG,IAAI,CAACC,UAAU;IAChC,MAAMC,MAAM,GAAG,IAAI,CAACR,gBAAgB,IAAI,IAAI,CAACC,WAAW;IACxD,MAAMQ,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,IAAI,CAACX,eAAe;IACpD,MAAMY,OAAO,GAAG,IAAI,CAACX,SAAS,GAAG,IAAI,CAACA,SAAS,CAACW,OAAO,GAAG,IAAI;IAC9D,MAAMC,QAAQ,IAAAN,qBAAA,GAAGI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,YAAY,CAACF,OAAO,EAAEF,MAAM,CAAC,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,KAAK;IAChE,IAAIM,QAAQ,KAAKL,QAAQ,EAAE;MACvB,IAAI,CAACC,UAAU,GAAGI,QAAQ;MAC1B,IAAI,CAACT,aAAa,CAACW,IAAI,CAAC,CAAC;IAC7B;EACJ;AACJ;AAEA,SAASjB,kBAAkB,IAAIkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}