{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _RouterLink, _RouterLinkActive, _PreloadAllModules, _NoPreloading, _RouterPreloader, _RouterScroller, _RouterModule;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i3 from '@angular/common';\nimport { LOCATION_INITIALIZED, HashLocationStrategy, LocationStrategy, ViewportScroller, Location, PathLocationStrategy } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, ɵɵsanitizeUrlOrResourceUrl as __sanitizeUrlOrResourceUrl, booleanAttribute, HostListener, Input, HostBinding, Attribute, Directive, EventEmitter, Output, ContentChildren, Optional, createEnvironmentInjector, Injectable, InjectionToken, ɵperformanceMarkFeature as _performanceMarkFeature, makeEnvironmentProviders, APP_BOOTSTRAP_LISTENER, ENVIRONMENT_INITIALIZER, provideAppInitializer, inject, Injector, ApplicationRef, InjectFlags, NgZone, SkipSelf, NgModule } from '@angular/core';\nimport { NavigationEnd, isUrlTree, Router, ActivatedRoute, RouterConfigLoader, NavigationStart, NavigationSkipped, NavigationSkippedCode, Scroll, UrlSerializer, NavigationTransitions, ROUTES, afterNextNavigation, ROUTER_CONFIGURATION, NAVIGATION_ERROR_HANDLER, RoutedComponentInputBinder, INPUT_BINDER, createViewTransition, CREATE_VIEW_TRANSITION, VIEW_TRANSITION_OPTIONS, stringifyEvent, DefaultUrlSerializer, ChildrenOutletContexts, RouterOutlet, ɵEmptyOutletComponent as _EmptyOutletComponent } from './router-Dwfin5Au.mjs';\nimport { Subject, of, from } from 'rxjs';\nimport { mergeAll, catchError, filter, concatMap, mergeMap } from 'rxjs/operators';\n\n/**\n * @description\n *\n * When applied to an element in a template, makes that element a link\n * that initiates navigation to a route. Navigation opens one or more routed components\n * in one or more `<router-outlet>` locations on the page.\n *\n * Given a route configuration `[{ path: 'user/:name', component: UserCmp }]`,\n * the following creates a static link to the route:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n *\n * You can use dynamic values to generate the link.\n * For a dynamic link, pass an array of path segments,\n * followed by the params for each segment.\n * For example, `['/team', teamId, 'user', userName, {details: true}]`\n * generates a link to `/team/11/user/bob;details=true`.\n *\n * Multiple static segments can be merged into one term and combined with dynamic segments.\n * For example, `['/team/11/user', userName, {details: true}]`\n *\n * The input that you provide to the link is treated as a delta to the current URL.\n * For instance, suppose the current URL is `/user/(box//aux:team)`.\n * The link `<a [routerLink]=\"['/user/jim']\">Jim</a>` creates the URL\n * `/user/(jim//aux:team)`.\n * See {@link Router#createUrlTree} for more information.\n *\n * @usageNotes\n *\n * You can use absolute or relative paths in a link, set query parameters,\n * control how parameters are handled, and keep a history of navigation states.\n *\n * ### Relative link paths\n *\n * The first segment name can be prepended with `/`, `./`, or `../`.\n * * If the first segment begins with `/`, the router looks up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router\n *   looks in the children of the current activated route.\n * * If the first segment begins with `../`, the router goes up one level in the route tree.\n *\n * ### Setting and handling query params and fragments\n *\n * The following link adds a query parameter and a fragment to the generated URL:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * By default, the directive constructs the new URL using the given query parameters.\n * The example generates the link: `/user/bob?debug=true#education`.\n *\n * You can instruct the directive to handle query parameters differently\n * by specifying the `queryParamsHandling` option in the link.\n * Allowed values are:\n *\n *  - `'merge'`: Merge the given `queryParams` into the current query params.\n *  - `'preserve'`: Preserve the current query params.\n *\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n *\n * `queryParams`, `fragment`, `queryParamsHandling`, `preserveFragment`, and `relativeTo`\n * cannot be used when the `routerLink` input is a `UrlTree`.\n *\n * See {@link UrlCreationOptions#queryParamsHandling}.\n *\n * ### Preserving navigation history\n *\n * You can provide a `state` value to be persisted to the browser's\n * [`History.state` property](https://developer.mozilla.org/en-US/docs/Web/API/History#Properties).\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [state]=\"{tracingId: 123}\">\n *   link to user component\n * </a>\n * ```\n *\n * Use {@link Router#getCurrentNavigation} to retrieve a saved\n * navigation-state value. For example, to capture the `tracingId` during the `NavigationStart`\n * event:\n *\n * ```ts\n * // Get NavigationStart events\n * router.events.pipe(filter(e => e instanceof NavigationStart)).subscribe(e => {\n *   const navigation = router.getCurrentNavigation();\n *   tracingService.trace({id: navigation.extras.state.tracingId});\n * });\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLink {\n  constructor(router, route, tabIndexAttribute, renderer, el, locationStrategy) {\n    var _el$nativeElement$tag;\n    _defineProperty(this, \"router\", void 0);\n    _defineProperty(this, \"route\", void 0);\n    _defineProperty(this, \"tabIndexAttribute\", void 0);\n    _defineProperty(this, \"renderer\", void 0);\n    _defineProperty(this, \"el\", void 0);\n    _defineProperty(this, \"locationStrategy\", void 0);\n    /**\n     * Represents an `href` attribute value applied to a host element,\n     * when a host element is `<a>`. For other tags, the value is `null`.\n     */\n    _defineProperty(this, \"href\", null);\n    /**\n     * Represents the `target` attribute on a host element.\n     * This is only used when the host element is an `<a>` tag.\n     */\n    _defineProperty(this, \"target\", void 0);\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParams}\n     * @see {@link Router#createUrlTree}\n     */\n    _defineProperty(this, \"queryParams\", void 0);\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#fragment}\n     * @see {@link Router#createUrlTree}\n     */\n    _defineProperty(this, \"fragment\", void 0);\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParamsHandling}\n     * @see {@link Router#createUrlTree}\n     */\n    _defineProperty(this, \"queryParamsHandling\", void 0);\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#state}\n     * @see {@link Router#navigateByUrl}\n     */\n    _defineProperty(this, \"state\", void 0);\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#info}\n     * @see {@link Router#navigateByUrl}\n     */\n    _defineProperty(this, \"info\", void 0);\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * Specify a value here when you do not want to use the default value\n     * for `routerLink`, which is the current activated route.\n     * Note that a value of `undefined` here will use the `routerLink` default.\n     * @see {@link UrlCreationOptions#relativeTo}\n     * @see {@link Router#createUrlTree}\n     */\n    _defineProperty(this, \"relativeTo\", void 0);\n    /** Whether a host element is an `<a>` tag. */\n    _defineProperty(this, \"isAnchorElement\", void 0);\n    _defineProperty(this, \"subscription\", void 0);\n    /** @internal */\n    _defineProperty(this, \"onChanges\", new Subject());\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#preserveFragment}\n     * @see {@link Router#createUrlTree}\n     */\n    _defineProperty(this, \"preserveFragment\", false);\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#skipLocationChange}\n     * @see {@link Router#navigateByUrl}\n     */\n    _defineProperty(this, \"skipLocationChange\", false);\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#replaceUrl}\n     * @see {@link Router#navigateByUrl}\n     */\n    _defineProperty(this, \"replaceUrl\", false);\n    _defineProperty(this, \"routerLinkInput\", null);\n    this.router = router;\n    this.route = route;\n    this.tabIndexAttribute = tabIndexAttribute;\n    this.renderer = renderer;\n    this.el = el;\n    this.locationStrategy = locationStrategy;\n    const tagName = (_el$nativeElement$tag = el.nativeElement.tagName) === null || _el$nativeElement$tag === void 0 ? void 0 : _el$nativeElement$tag.toLowerCase();\n    this.isAnchorElement = tagName === 'a' || tagName === 'area';\n    if (this.isAnchorElement) {\n      this.subscription = router.events.subscribe(s => {\n        if (s instanceof NavigationEnd) {\n          this.updateHref();\n        }\n      });\n    } else {\n      this.setTabIndexIfNotOnNativeEl('0');\n    }\n  }\n  /**\n   * Modifies the tab index if there was not a tabindex attribute on the element during\n   * instantiation.\n   */\n  setTabIndexIfNotOnNativeEl(newTabIndex) {\n    if (this.tabIndexAttribute != null /* both `null` and `undefined` */ || this.isAnchorElement) {\n      return;\n    }\n    this.applyAttributeValue('tabindex', newTabIndex);\n  }\n  /** @docs-private */\n  // TODO(atscott): Remove changes parameter in major version as a breaking change.\n  ngOnChanges(changes) {\n    if (ngDevMode && isUrlTree(this.routerLinkInput) && (this.fragment !== undefined || this.queryParams || this.queryParamsHandling || this.preserveFragment || this.relativeTo)) {\n      throw new _RuntimeError(4016 /* RuntimeErrorCode.INVALID_ROUTER_LINK_INPUTS */, 'Cannot configure queryParams or fragment when using a UrlTree as the routerLink input value.');\n    }\n    if (this.isAnchorElement) {\n      this.updateHref();\n    }\n    // This is subscribed to by `RouterLinkActive` so that it knows to update when there are changes\n    // to the RouterLinks it's tracking.\n    this.onChanges.next(this);\n  }\n  /**\n   * Commands to pass to {@link Router#createUrlTree} or a `UrlTree`.\n   *   - **array**: commands to pass to {@link Router#createUrlTree}.\n   *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n   *   - **UrlTree**: a `UrlTree` for this link rather than creating one from the commands\n   *     and other inputs that correspond to properties of `UrlCreationOptions`.\n   *   - **null|undefined**: effectively disables the `routerLink`\n   * @see {@link Router#createUrlTree}\n   */\n  set routerLink(commandsOrUrlTree) {\n    if (commandsOrUrlTree == null) {\n      this.routerLinkInput = null;\n      this.setTabIndexIfNotOnNativeEl(null);\n    } else {\n      if (isUrlTree(commandsOrUrlTree)) {\n        this.routerLinkInput = commandsOrUrlTree;\n      } else {\n        this.routerLinkInput = Array.isArray(commandsOrUrlTree) ? commandsOrUrlTree : [commandsOrUrlTree];\n      }\n      this.setTabIndexIfNotOnNativeEl('0');\n    }\n  }\n  /** @docs-private */\n  onClick(button, ctrlKey, shiftKey, altKey, metaKey) {\n    const urlTree = this.urlTree;\n    if (urlTree === null) {\n      return true;\n    }\n    if (this.isAnchorElement) {\n      if (button !== 0 || ctrlKey || shiftKey || altKey || metaKey) {\n        return true;\n      }\n      if (typeof this.target === 'string' && this.target != '_self') {\n        return true;\n      }\n    }\n    const extras = {\n      skipLocationChange: this.skipLocationChange,\n      replaceUrl: this.replaceUrl,\n      state: this.state,\n      info: this.info\n    };\n    this.router.navigateByUrl(urlTree, extras);\n    // Return `false` for `<a>` elements to prevent default action\n    // and cancel the native behavior, since the navigation is handled\n    // by the Router.\n    return !this.isAnchorElement;\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    var _this$subscription;\n    (_this$subscription = this.subscription) === null || _this$subscription === void 0 || _this$subscription.unsubscribe();\n  }\n  updateHref() {\n    var _this$locationStrateg;\n    const urlTree = this.urlTree;\n    this.href = urlTree !== null && this.locationStrategy ? (_this$locationStrateg = this.locationStrategy) === null || _this$locationStrateg === void 0 ? void 0 : _this$locationStrateg.prepareExternalUrl(this.router.serializeUrl(urlTree)) : null;\n    const sanitizedValue = this.href === null ? null :\n    // This class represents a directive that can be added to both `<a>` elements,\n    // as well as other elements. As a result, we can't define security context at\n    // compile time. So the security context is deferred to runtime.\n    // The `ɵɵsanitizeUrlOrResourceUrl` selects the necessary sanitizer function\n    // based on the tag and property names. The logic mimics the one from\n    // `packages/compiler/src/schema/dom_security_schema.ts`, which is used at compile time.\n    //\n    // Note: we should investigate whether we can switch to using `@HostBinding('attr.href')`\n    // instead of applying a value via a renderer, after a final merge of the\n    // `RouterLinkWithHref` directive.\n    __sanitizeUrlOrResourceUrl(this.href, this.el.nativeElement.tagName.toLowerCase(), 'href');\n    this.applyAttributeValue('href', sanitizedValue);\n  }\n  applyAttributeValue(attrName, attrValue) {\n    const renderer = this.renderer;\n    const nativeElement = this.el.nativeElement;\n    if (attrValue !== null) {\n      renderer.setAttribute(nativeElement, attrName, attrValue);\n    } else {\n      renderer.removeAttribute(nativeElement, attrName);\n    }\n  }\n  get urlTree() {\n    if (this.routerLinkInput === null) {\n      return null;\n    } else if (isUrlTree(this.routerLinkInput)) {\n      return this.routerLinkInput;\n    }\n    return this.router.createUrlTree(this.routerLinkInput, {\n      // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n      // Otherwise, we should use the value provided by the user in the input.\n      relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n      queryParams: this.queryParams,\n      fragment: this.fragment,\n      queryParamsHandling: this.queryParamsHandling,\n      preserveFragment: this.preserveFragment\n    });\n  }\n}\n_RouterLink = RouterLink;\n_defineProperty(RouterLink, \"\\u0275fac\", function _RouterLink_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RouterLink)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(ActivatedRoute), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.LocationStrategy));\n});\n_defineProperty(RouterLink, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _RouterLink,\n  selectors: [[\"\", \"routerLink\", \"\"]],\n  hostVars: 1,\n  hostBindings: function _RouterLink_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _RouterLink_click_HostBindingHandler($event) {\n        return ctx.onClick($event.button, $event.ctrlKey, $event.shiftKey, $event.altKey, $event.metaKey);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"target\", ctx.target);\n    }\n  },\n  inputs: {\n    target: \"target\",\n    queryParams: \"queryParams\",\n    fragment: \"fragment\",\n    queryParamsHandling: \"queryParamsHandling\",\n    state: \"state\",\n    info: \"info\",\n    relativeTo: \"relativeTo\",\n    preserveFragment: [2, \"preserveFragment\", \"preserveFragment\", booleanAttribute],\n    skipLocationChange: [2, \"skipLocationChange\", \"skipLocationChange\", booleanAttribute],\n    replaceUrl: [2, \"replaceUrl\", \"replaceUrl\", booleanAttribute],\n    routerLink: \"routerLink\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLink, [{\n    type: Directive,\n    args: [{\n      selector: '[routerLink]'\n    }]\n  }], () => [{\n    type: Router\n  }, {\n    type: ActivatedRoute\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i3.LocationStrategy\n  }], {\n    target: [{\n      type: HostBinding,\n      args: ['attr.target']\n    }, {\n      type: Input\n    }],\n    queryParams: [{\n      type: Input\n    }],\n    fragment: [{\n      type: Input\n    }],\n    queryParamsHandling: [{\n      type: Input\n    }],\n    state: [{\n      type: Input\n    }],\n    info: [{\n      type: Input\n    }],\n    relativeTo: [{\n      type: Input\n    }],\n    preserveFragment: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    skipLocationChange: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    replaceUrl: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    routerLink: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event.button', '$event.ctrlKey', '$event.shiftKey', '$event.altKey', '$event.metaKey']]\n    }]\n  });\n})();\n\n/**\n *\n * @description\n *\n * Tracks whether the linked route of an element is currently active, and allows you\n * to specify one or more CSS classes to add to the element when the linked route\n * is active.\n *\n * Use this directive to create a visual distinction for elements associated with an active route.\n * For example, the following code highlights the word \"Bob\" when the router\n * activates the associated route:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n *\n * Whenever the URL is either '/user' or '/user/bob', the \"active-link\" class is\n * added to the anchor tag. If the URL changes, the class is removed.\n *\n * You can set more than one class using a space-separated string or an array.\n * For example:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n *\n * To add the classes only when the URL matches the link exactly, add the option `exact: true`:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n *\n * To directly check the `isActive` status of the link, assign the `RouterLinkActive`\n * instance to a template variable.\n * For example, the following checks the status without assigning any CSS classes:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n *\n * You can apply the `RouterLinkActive` directive to an ancestor of linked elements.\n * For example, the following sets the active-link class on the `<div>`  parent tag\n * when the URL is either '/user/jim' or '/user/bob'.\n *\n * ```html\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n *\n * The `RouterLinkActive` directive can also be used to set the aria-current attribute\n * to provide an alternative distinction for active elements to visually impaired users.\n *\n * For example, the following code adds the 'active' class to the Home Page link when it is\n * indeed active and in such case also sets its aria-current attribute to 'page':\n *\n * ```html\n * <a routerLink=\"/\" routerLinkActive=\"active\" ariaCurrentWhenActive=\"page\">Home Page</a>\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLinkActive {\n  get isActive() {\n    return this._isActive;\n  }\n  /**\n   * Options to configure how to determine if the router link is active.\n   *\n   * These options are passed to the `Router.isActive()` function.\n   *\n   * @see {@link Router#isActive}\n   */\n\n  constructor(router, element, renderer, cdr, link) {\n    _defineProperty(this, \"router\", void 0);\n    _defineProperty(this, \"element\", void 0);\n    _defineProperty(this, \"renderer\", void 0);\n    _defineProperty(this, \"cdr\", void 0);\n    _defineProperty(this, \"link\", void 0);\n    _defineProperty(this, \"links\", void 0);\n    _defineProperty(this, \"classes\", []);\n    _defineProperty(this, \"routerEventsSubscription\", void 0);\n    _defineProperty(this, \"linkInputChangesSubscription\", void 0);\n    _defineProperty(this, \"_isActive\", false);\n    _defineProperty(this, \"routerLinkActiveOptions\", {\n      exact: false\n    });\n    /**\n     * Aria-current attribute to apply when the router link is active.\n     *\n     * Possible values: `'page'` | `'step'` | `'location'` | `'date'` | `'time'` | `true` | `false`.\n     *\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-current}\n     */\n    _defineProperty(this, \"ariaCurrentWhenActive\", void 0);\n    /**\n     *\n     * You can use the output `isActiveChange` to get notified each time the link becomes\n     * active or inactive.\n     *\n     * Emits:\n     * true  -> Route is active\n     * false -> Route is inactive\n     *\n     * ```html\n     * <a\n     *  routerLink=\"/user/bob\"\n     *  routerLinkActive=\"active-link\"\n     *  (isActiveChange)=\"this.onRouterLinkActive($event)\">Bob</a>\n     * ```\n     */\n    _defineProperty(this, \"isActiveChange\", new EventEmitter());\n    this.router = router;\n    this.element = element;\n    this.renderer = renderer;\n    this.cdr = cdr;\n    this.link = link;\n    this.routerEventsSubscription = router.events.subscribe(s => {\n      if (s instanceof NavigationEnd) {\n        this.update();\n      }\n    });\n  }\n  /** @docs-private */\n  ngAfterContentInit() {\n    // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).\n    of(this.links.changes, of(null)).pipe(mergeAll()).subscribe(_ => {\n      this.update();\n      this.subscribeToEachLinkOnChanges();\n    });\n  }\n  subscribeToEachLinkOnChanges() {\n    var _this$linkInputChange;\n    (_this$linkInputChange = this.linkInputChangesSubscription) === null || _this$linkInputChange === void 0 || _this$linkInputChange.unsubscribe();\n    const allLinkChanges = [...this.links.toArray(), this.link].filter(link => !!link).map(link => link.onChanges);\n    this.linkInputChangesSubscription = from(allLinkChanges).pipe(mergeAll()).subscribe(link => {\n      if (this._isActive !== this.isLinkActive(this.router)(link)) {\n        this.update();\n      }\n    });\n  }\n  set routerLinkActive(data) {\n    const classes = Array.isArray(data) ? data : data.split(' ');\n    this.classes = classes.filter(c => !!c);\n  }\n  /** @docs-private */\n  ngOnChanges(changes) {\n    this.update();\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    var _this$linkInputChange2;\n    this.routerEventsSubscription.unsubscribe();\n    (_this$linkInputChange2 = this.linkInputChangesSubscription) === null || _this$linkInputChange2 === void 0 || _this$linkInputChange2.unsubscribe();\n  }\n  update() {\n    if (!this.links || !this.router.navigated) return;\n    queueMicrotask(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      this.classes.forEach(c => {\n        if (hasActiveLinks) {\n          this.renderer.addClass(this.element.nativeElement, c);\n        } else {\n          this.renderer.removeClass(this.element.nativeElement, c);\n        }\n      });\n      if (hasActiveLinks && this.ariaCurrentWhenActive !== undefined) {\n        this.renderer.setAttribute(this.element.nativeElement, 'aria-current', this.ariaCurrentWhenActive.toString());\n      } else {\n        this.renderer.removeAttribute(this.element.nativeElement, 'aria-current');\n      }\n      // Only emit change if the active state changed.\n      if (this._isActive !== hasActiveLinks) {\n        this._isActive = hasActiveLinks;\n        this.cdr.markForCheck();\n        // Emit on isActiveChange after classes are updated\n        this.isActiveChange.emit(hasActiveLinks);\n      }\n    });\n  }\n  isLinkActive(router) {\n    const options = isActiveMatchOptions(this.routerLinkActiveOptions) ? this.routerLinkActiveOptions :\n    // While the types should disallow `undefined` here, it's possible without strict inputs\n    this.routerLinkActiveOptions.exact || false;\n    return link => {\n      const urlTree = link.urlTree;\n      return urlTree ? router.isActive(urlTree, options) : false;\n    };\n  }\n  hasActiveLinks() {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return this.link && isActiveCheckFn(this.link) || this.links.some(isActiveCheckFn);\n  }\n}\n_RouterLinkActive = RouterLinkActive;\n_defineProperty(RouterLinkActive, \"\\u0275fac\", function _RouterLinkActive_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RouterLinkActive)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(RouterLink, 8));\n});\n_defineProperty(RouterLinkActive, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _RouterLinkActive,\n  selectors: [[\"\", \"routerLinkActive\", \"\"]],\n  contentQueries: function _RouterLinkActive_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, RouterLink, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.links = _t);\n    }\n  },\n  inputs: {\n    routerLinkActiveOptions: \"routerLinkActiveOptions\",\n    ariaCurrentWhenActive: \"ariaCurrentWhenActive\",\n    routerLinkActive: \"routerLinkActive\"\n  },\n  outputs: {\n    isActiveChange: \"isActiveChange\"\n  },\n  exportAs: [\"routerLinkActive\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkActive, [{\n    type: Directive,\n    args: [{\n      selector: '[routerLinkActive]',\n      exportAs: 'routerLinkActive'\n    }]\n  }], () => [{\n    type: Router\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: RouterLink,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    links: [{\n      type: ContentChildren,\n      args: [RouterLink, {\n        descendants: true\n      }]\n    }],\n    routerLinkActiveOptions: [{\n      type: Input\n    }],\n    ariaCurrentWhenActive: [{\n      type: Input\n    }],\n    isActiveChange: [{\n      type: Output\n    }],\n    routerLinkActive: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Use instead of `'paths' in options` to be compatible with property renaming\n */\nfunction isActiveMatchOptions(options) {\n  return !!options.paths;\n}\n\n/**\n * @description\n *\n * Provides a preloading strategy.\n *\n * @publicApi\n */\nclass PreloadingStrategy {}\n/**\n * @description\n *\n * Provides a preloading strategy that preloads all modules as quickly as possible.\n *\n * ```ts\n * RouterModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n *\n * @publicApi\n */\nclass PreloadAllModules {\n  preload(route, fn) {\n    return fn().pipe(catchError(() => of(null)));\n  }\n}\n_PreloadAllModules = PreloadAllModules;\n_defineProperty(PreloadAllModules, \"\\u0275fac\", function _PreloadAllModules_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PreloadAllModules)();\n});\n_defineProperty(PreloadAllModules, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _PreloadAllModules,\n  factory: _PreloadAllModules.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PreloadAllModules, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @description\n *\n * Provides a preloading strategy that does not preload any modules.\n *\n * This strategy is enabled by default.\n *\n * @publicApi\n */\nclass NoPreloading {\n  preload(route, fn) {\n    return of(null);\n  }\n}\n_NoPreloading = NoPreloading;\n_defineProperty(NoPreloading, \"\\u0275fac\", function _NoPreloading_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _NoPreloading)();\n});\n_defineProperty(NoPreloading, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _NoPreloading,\n  factory: _NoPreloading.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoPreloading, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n *\n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n *\n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n *\n * @publicApi\n */\nclass RouterPreloader {\n  constructor(router, injector, preloadingStrategy, loader) {\n    _defineProperty(this, \"router\", void 0);\n    _defineProperty(this, \"injector\", void 0);\n    _defineProperty(this, \"preloadingStrategy\", void 0);\n    _defineProperty(this, \"loader\", void 0);\n    _defineProperty(this, \"subscription\", void 0);\n    this.router = router;\n    this.injector = injector;\n    this.preloadingStrategy = preloadingStrategy;\n    this.loader = loader;\n  }\n  setUpPreloading() {\n    this.subscription = this.router.events.pipe(filter(e => e instanceof NavigationEnd), concatMap(() => this.preload())).subscribe(() => {});\n  }\n  preload() {\n    return this.processRoutes(this.injector, this.router.config);\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  processRoutes(injector, routes) {\n    const res = [];\n    for (const route of routes) {\n      var _route$_injector, _route$_loadedInjecto;\n      if (route.providers && !route._injector) {\n        route._injector = createEnvironmentInjector(route.providers, injector, `Route: ${route.path}`);\n      }\n      const injectorForCurrentRoute = (_route$_injector = route._injector) !== null && _route$_injector !== void 0 ? _route$_injector : injector;\n      const injectorForChildren = (_route$_loadedInjecto = route._loadedInjector) !== null && _route$_loadedInjecto !== void 0 ? _route$_loadedInjecto : injectorForCurrentRoute;\n      // Note that `canLoad` is only checked as a condition that prevents `loadChildren` and not\n      // `loadComponent`. `canLoad` guards only block loading of child routes by design. This\n      // happens as a consequence of needing to descend into children for route matching immediately\n      // while component loading is deferred until route activation. Because `canLoad` guards can\n      // have side effects, we cannot execute them here so we instead skip preloading altogether\n      // when present. Lastly, it remains to be decided whether `canLoad` should behave this way\n      // at all. Code splitting and lazy loading is separate from client-side authorization checks\n      // and should not be used as a security measure to prevent loading of code.\n      if (route.loadChildren && !route._loadedRoutes && route.canLoad === undefined || route.loadComponent && !route._loadedComponent) {\n        res.push(this.preloadConfig(injectorForCurrentRoute, route));\n      }\n      if (route.children || route._loadedRoutes) {\n        var _route$children;\n        res.push(this.processRoutes(injectorForChildren, (_route$children = route.children) !== null && _route$children !== void 0 ? _route$children : route._loadedRoutes));\n      }\n    }\n    return from(res).pipe(mergeAll());\n  }\n  preloadConfig(injector, route) {\n    return this.preloadingStrategy.preload(route, () => {\n      let loadedChildren$;\n      if (route.loadChildren && route.canLoad === undefined) {\n        loadedChildren$ = this.loader.loadChildren(injector, route);\n      } else {\n        loadedChildren$ = of(null);\n      }\n      const recursiveLoadChildren$ = loadedChildren$.pipe(mergeMap(config => {\n        var _config$injector;\n        if (config === null) {\n          return of(void 0);\n        }\n        route._loadedRoutes = config.routes;\n        route._loadedInjector = config.injector;\n        // If the loaded config was a module, use that as the module/module injector going\n        // forward. Otherwise, continue using the current module/module injector.\n        return this.processRoutes((_config$injector = config.injector) !== null && _config$injector !== void 0 ? _config$injector : injector, config.routes);\n      }));\n      if (route.loadComponent && !route._loadedComponent) {\n        const loadComponent$ = this.loader.loadComponent(route);\n        return from([recursiveLoadChildren$, loadComponent$]).pipe(mergeAll());\n      } else {\n        return recursiveLoadChildren$;\n      }\n    });\n  }\n}\n_RouterPreloader = RouterPreloader;\n_defineProperty(RouterPreloader, \"\\u0275fac\", function _RouterPreloader_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RouterPreloader)(i0.ɵɵinject(Router), i0.ɵɵinject(i0.EnvironmentInjector), i0.ɵɵinject(PreloadingStrategy), i0.ɵɵinject(RouterConfigLoader));\n});\n_defineProperty(RouterPreloader, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _RouterPreloader,\n  factory: _RouterPreloader.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterPreloader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: Router\n  }, {\n    type: i0.EnvironmentInjector\n  }, {\n    type: PreloadingStrategy\n  }, {\n    type: RouterConfigLoader\n  }], null);\n})();\nconst ROUTER_SCROLLER = new InjectionToken('');\nclass RouterScroller {\n  /** @docs-private */\n  constructor(urlSerializer, transitions, viewportScroller, zone, options = {}) {\n    _defineProperty(this, \"urlSerializer\", void 0);\n    _defineProperty(this, \"transitions\", void 0);\n    _defineProperty(this, \"viewportScroller\", void 0);\n    _defineProperty(this, \"zone\", void 0);\n    _defineProperty(this, \"options\", void 0);\n    _defineProperty(this, \"routerEventsSubscription\", void 0);\n    _defineProperty(this, \"scrollEventsSubscription\", void 0);\n    _defineProperty(this, \"lastId\", 0);\n    _defineProperty(this, \"lastSource\", 'imperative');\n    _defineProperty(this, \"restoredId\", 0);\n    _defineProperty(this, \"store\", {});\n    this.urlSerializer = urlSerializer;\n    this.transitions = transitions;\n    this.viewportScroller = viewportScroller;\n    this.zone = zone;\n    this.options = options;\n    // Default both options to 'disabled'\n    options.scrollPositionRestoration || (options.scrollPositionRestoration = 'disabled');\n    options.anchorScrolling || (options.anchorScrolling = 'disabled');\n  }\n  init() {\n    // we want to disable the automatic scrolling because having two places\n    // responsible for scrolling results race conditions, especially given\n    // that browser don't implement this behavior consistently\n    if (this.options.scrollPositionRestoration !== 'disabled') {\n      this.viewportScroller.setHistoryScrollRestoration('manual');\n    }\n    this.routerEventsSubscription = this.createScrollEvents();\n    this.scrollEventsSubscription = this.consumeScrollEvents();\n  }\n  createScrollEvents() {\n    return this.transitions.events.subscribe(e => {\n      if (e instanceof NavigationStart) {\n        // store the scroll position of the current stable navigations.\n        this.store[this.lastId] = this.viewportScroller.getScrollPosition();\n        this.lastSource = e.navigationTrigger;\n        this.restoredId = e.restoredState ? e.restoredState.navigationId : 0;\n      } else if (e instanceof NavigationEnd) {\n        this.lastId = e.id;\n        this.scheduleScrollEvent(e, this.urlSerializer.parse(e.urlAfterRedirects).fragment);\n      } else if (e instanceof NavigationSkipped && e.code === NavigationSkippedCode.IgnoredSameUrlNavigation) {\n        this.lastSource = undefined;\n        this.restoredId = 0;\n        this.scheduleScrollEvent(e, this.urlSerializer.parse(e.url).fragment);\n      }\n    });\n  }\n  consumeScrollEvents() {\n    return this.transitions.events.subscribe(e => {\n      if (!(e instanceof Scroll)) return;\n      // a popstate event. The pop state event will always ignore anchor scrolling.\n      if (e.position) {\n        if (this.options.scrollPositionRestoration === 'top') {\n          this.viewportScroller.scrollToPosition([0, 0]);\n        } else if (this.options.scrollPositionRestoration === 'enabled') {\n          this.viewportScroller.scrollToPosition(e.position);\n        }\n        // imperative navigation \"forward\"\n      } else {\n        if (e.anchor && this.options.anchorScrolling === 'enabled') {\n          this.viewportScroller.scrollToAnchor(e.anchor);\n        } else if (this.options.scrollPositionRestoration !== 'disabled') {\n          this.viewportScroller.scrollToPosition([0, 0]);\n        }\n      }\n    });\n  }\n  scheduleScrollEvent(routerEvent, anchor) {\n    this.zone.runOutsideAngular(() => {\n      // The scroll event needs to be delayed until after change detection. Otherwise, we may\n      // attempt to restore the scroll position before the router outlet has fully rendered the\n      // component by executing its update block of the template function.\n      setTimeout(() => {\n        this.zone.run(() => {\n          this.transitions.events.next(new Scroll(routerEvent, this.lastSource === 'popstate' ? this.store[this.restoredId] : null, anchor));\n        });\n      }, 0);\n    });\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    var _this$routerEventsSub, _this$scrollEventsSub;\n    (_this$routerEventsSub = this.routerEventsSubscription) === null || _this$routerEventsSub === void 0 || _this$routerEventsSub.unsubscribe();\n    (_this$scrollEventsSub = this.scrollEventsSubscription) === null || _this$scrollEventsSub === void 0 || _this$scrollEventsSub.unsubscribe();\n  }\n}\n_RouterScroller = RouterScroller;\n_defineProperty(RouterScroller, \"\\u0275fac\", function _RouterScroller_Factory(__ngFactoryType__) {\n  i0.ɵɵinvalidFactory();\n});\n_defineProperty(RouterScroller, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _RouterScroller,\n  factory: _RouterScroller.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterScroller, [{\n    type: Injectable\n  }], () => [{\n    type: UrlSerializer\n  }, {\n    type: NavigationTransitions\n  }, {\n    type: i3.ViewportScroller\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined\n  }], null);\n})();\n\n/**\n * Sets up providers necessary to enable `Router` functionality for the application.\n * Allows to configure a set of routes as well as extra features that should be enabled.\n *\n * @usageNotes\n *\n * Basic example of how you can add a Router to your application:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent, {\n *   providers: [provideRouter(appRoutes)]\n * });\n * ```\n *\n * You can also enable optional features in the Router by adding functions from the `RouterFeatures`\n * type:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes,\n *         withDebugTracing(),\n *         withRouterConfig({paramsInheritanceStrategy: 'always'}))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link RouterFeatures}\n *\n * @publicApi\n * @param routes A set of `Route`s to use for the application routing table.\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to setup a Router.\n */\nfunction provideRouter(routes, ...features) {\n  return makeEnvironmentProviders([{\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }, typeof ngDevMode === 'undefined' || ngDevMode ? {\n    provide: ROUTER_IS_PROVIDED,\n    useValue: true\n  } : [], {\n    provide: ActivatedRoute,\n    useFactory: rootRoute,\n    deps: [Router]\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useFactory: getBootstrapListener\n  }, features.map(feature => feature.ɵproviders)]);\n}\nfunction rootRoute(router) {\n  return router.routerState.root;\n}\n/**\n * Helper function to create an object that represents a Router feature.\n */\nfunction routerFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * An Injection token used to indicate whether `provideRouter` or `RouterModule.forRoot` was ever\n * called.\n */\nconst ROUTER_IS_PROVIDED = new InjectionToken('', {\n  providedIn: 'root',\n  factory: () => false\n});\nconst routerIsProvidedDevModeCheck = {\n  provide: ENVIRONMENT_INITIALIZER,\n  multi: true,\n  useFactory() {\n    return () => {\n      if (!inject(ROUTER_IS_PROVIDED)) {\n        console.warn('`provideRoutes` was called without `provideRouter` or `RouterModule.forRoot`. ' + 'This is likely a mistake.');\n      }\n    };\n  }\n};\n/**\n * Registers a DI provider for a set of routes.\n * @param routes The route configuration to provide.\n *\n * @usageNotes\n *\n * ```ts\n * @NgModule({\n *   providers: [provideRoutes(ROUTES)]\n * })\n * class LazyLoadedChildModule {}\n * ```\n *\n * @deprecated If necessary, provide routes using the `ROUTES` `InjectionToken`.\n * @see {@link ROUTES}\n * @publicApi\n */\nfunction provideRoutes(routes) {\n  return [{\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }, typeof ngDevMode === 'undefined' || ngDevMode ? routerIsProvidedDevModeCheck : []];\n}\n/**\n * Enables customizable scrolling behavior for router navigations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable scrolling feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withInMemoryScrolling())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link ViewportScroller}\n *\n * @publicApi\n * @param options Set of configuration parameters to customize scrolling behavior, see\n *     `InMemoryScrollingOptions` for additional information.\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withInMemoryScrolling(options = {}) {\n  const providers = [{\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, options);\n    }\n  }];\n  return routerFeature(4 /* RouterFeatureKind.InMemoryScrollingFeature */, providers);\n}\nfunction getBootstrapListener() {\n  const injector = inject(Injector);\n  return bootstrappedComponentRef => {\n    var _injector$get, _injector$get2;\n    const ref = injector.get(ApplicationRef);\n    if (bootstrappedComponentRef !== ref.components[0]) {\n      return;\n    }\n    const router = injector.get(Router);\n    const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n    if (injector.get(INITIAL_NAVIGATION) === 1 /* InitialNavigation.EnabledNonBlocking */) {\n      router.initialNavigation();\n    }\n    (_injector$get = injector.get(ROUTER_PRELOADER, null, InjectFlags.Optional)) === null || _injector$get === void 0 || _injector$get.setUpPreloading();\n    (_injector$get2 = injector.get(ROUTER_SCROLLER, null, InjectFlags.Optional)) === null || _injector$get2 === void 0 || _injector$get2.init();\n    router.resetRootComponentType(ref.componentTypes[0]);\n    if (!bootstrapDone.closed) {\n      bootstrapDone.next();\n      bootstrapDone.complete();\n      bootstrapDone.unsubscribe();\n    }\n  };\n}\n/**\n * A subject used to indicate that the bootstrapping phase is done. When initial navigation is\n * `enabledBlocking`, the first navigation waits until bootstrapping is finished before continuing\n * to the activation phase.\n */\nconst BOOTSTRAP_DONE = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'bootstrap done indicator' : '', {\n  factory: () => {\n    return new Subject();\n  }\n});\nconst INITIAL_NAVIGATION = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'initial navigation' : '', {\n  providedIn: 'root',\n  factory: () => 1 /* InitialNavigation.EnabledNonBlocking */\n});\n/**\n * Configures initial navigation to start before the root component is created.\n *\n * The bootstrap is blocked until the initial navigation is complete. This should be set in case\n * you use [server-side rendering](guide/ssr), but do not enable [hydration](guide/hydration) for\n * your application.\n *\n * @usageNotes\n *\n * Basic example of how you can enable this navigation behavior:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withEnabledBlockingInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @publicApi\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withEnabledBlockingInitialNavigation() {\n  const providers = [{\n    provide: INITIAL_NAVIGATION,\n    useValue: 0 /* InitialNavigation.EnabledBlocking */\n  }, provideAppInitializer(() => {\n    const injector = inject(Injector);\n    const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve());\n    return locationInitialized.then(() => {\n      return new Promise(resolve => {\n        const router = injector.get(Router);\n        const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n        afterNextNavigation(router, () => {\n          // Unblock APP_INITIALIZER in case the initial navigation was canceled or errored\n          // without a redirect.\n          resolve(true);\n        });\n        injector.get(NavigationTransitions).afterPreactivation = () => {\n          // Unblock APP_INITIALIZER once we get to `afterPreactivation`. At this point, we\n          // assume activation will complete successfully (even though this is not\n          // guaranteed).\n          resolve(true);\n          return bootstrapDone.closed ? of(void 0) : bootstrapDone;\n        };\n        router.initialNavigation();\n      });\n    });\n  })];\n  return routerFeature(2 /* RouterFeatureKind.EnabledBlockingInitialNavigationFeature */, providers);\n}\n/**\n * Disables initial navigation.\n *\n * Use if there is a reason to have more control over when the router starts its initial navigation\n * due to some complex initialization logic.\n *\n * @usageNotes\n *\n * Basic example of how you can disable initial navigation:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDisabledInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDisabledInitialNavigation() {\n  const providers = [provideAppInitializer(() => {\n    inject(Router).setUpLocationChangeListener();\n  }), {\n    provide: INITIAL_NAVIGATION,\n    useValue: 2 /* InitialNavigation.Disabled */\n  }];\n  return routerFeature(3 /* RouterFeatureKind.DisabledInitialNavigationFeature */, providers);\n}\n/**\n * Enables logging of all internal navigation events to the console.\n * Extra logging might be useful for debugging purposes to inspect Router event sequence.\n *\n * @usageNotes\n *\n * Basic example of how you can enable debug tracing:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDebugTracing())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDebugTracing() {\n  let providers = [];\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    providers = [{\n      provide: ENVIRONMENT_INITIALIZER,\n      multi: true,\n      useFactory: () => {\n        const router = inject(Router);\n        return () => router.events.subscribe(e => {\n          var _console$group, _console, _console$groupEnd, _console2;\n          // tslint:disable:no-console\n          (_console$group = (_console = console).group) === null || _console$group === void 0 || _console$group.call(_console, `Router Event: ${e.constructor.name}`);\n          console.log(stringifyEvent(e));\n          console.log(e);\n          (_console$groupEnd = (_console2 = console).groupEnd) === null || _console$groupEnd === void 0 || _console$groupEnd.call(_console2);\n          // tslint:enable:no-console\n        });\n      }\n    }];\n  } else {\n    providers = [];\n  }\n  return routerFeature(1 /* RouterFeatureKind.DebugTracingFeature */, providers);\n}\nconst ROUTER_PRELOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router preloader' : '');\n/**\n * Allows to configure a preloading strategy to use. The strategy is configured by providing a\n * reference to a class that implements a `PreloadingStrategy`.\n *\n * @usageNotes\n *\n * Basic example of how you can configure preloading:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withPreloading(PreloadAllModules))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param preloadingStrategy A reference to a class that implements a `PreloadingStrategy` that\n *     should be used.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withPreloading(preloadingStrategy) {\n  const providers = [{\n    provide: ROUTER_PRELOADER,\n    useExisting: RouterPreloader\n  }, {\n    provide: PreloadingStrategy,\n    useExisting: preloadingStrategy\n  }];\n  return routerFeature(0 /* RouterFeatureKind.PreloadingFeature */, providers);\n}\n/**\n * Allows to provide extra parameters to configure Router.\n *\n * @usageNotes\n *\n * Basic example of how you can provide extra configuration options:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withRouterConfig({\n *          onSameUrlNavigation: 'reload'\n *       }))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param options A set of parameters to configure Router, see `RouterConfigOptions` for\n *     additional information.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withRouterConfig(options) {\n  const providers = [{\n    provide: ROUTER_CONFIGURATION,\n    useValue: options\n  }];\n  return routerFeature(5 /* RouterFeatureKind.RouterConfigurationFeature */, providers);\n}\n/**\n * Provides the location strategy that uses the URL fragment instead of the history API.\n *\n * @usageNotes\n *\n * Basic example of how you can use the hash location option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withHashLocation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link /api/common/HashLocationStrategy HashLocationStrategy}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withHashLocation() {\n  const providers = [{\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  }];\n  return routerFeature(6 /* RouterFeatureKind.RouterHashLocationFeature */, providers);\n}\n/**\n * Provides a function which is called when a navigation error occurs.\n *\n * This function is run inside application's [injection context](guide/di/dependency-injection-context)\n * so you can use the [`inject`](api/core/inject) function.\n *\n * This function can return a `RedirectCommand` to convert the error to a redirect, similar to returning\n * a `UrlTree` or `RedirectCommand` from a guard. This will also prevent the `Router` from emitting\n * `NavigationError`; it will instead emit `NavigationCancel` with code NavigationCancellationCode.Redirect.\n * Return values other than `RedirectCommand` are ignored and do not change any behavior with respect to\n * how the `Router` handles the error.\n *\n * @usageNotes\n *\n * Basic example of how you can use the error handler option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withNavigationErrorHandler((e: NavigationError) =>\n * inject(MyErrorTracker).trackError(e)))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link NavigationError}\n * @see {@link /api/core/inject inject}\n * @see {@link runInInjectionContext}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withNavigationErrorHandler(handler) {\n  const providers = [{\n    provide: NAVIGATION_ERROR_HANDLER,\n    useValue: handler\n  }];\n  return routerFeature(7 /* RouterFeatureKind.NavigationErrorHandlerFeature */, providers);\n}\n/**\n * Enables binding information from the `Router` state directly to the inputs of the component in\n * `Route` configurations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withComponentInputBinding())\n *     ]\n *   }\n * );\n * ```\n *\n * The router bindings information from any of the following sources:\n *\n *  - query parameters\n *  - path and matrix parameters\n *  - static route data\n *  - data from resolvers\n *\n * Duplicate keys are resolved in the same order from above, from least to greatest,\n * meaning that resolvers have the highest precedence and override any of the other information\n * from the route.\n *\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. This prevents previous information from being\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n * Default values can be provided with a resolver on the route to ensure the value is always present\n * or an input and use an input transform in the component.\n *\n * @see {@link /guide/components/inputs#input-transforms Input Transforms}\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withComponentInputBinding() {\n  const providers = [RoutedComponentInputBinder, {\n    provide: INPUT_BINDER,\n    useExisting: RoutedComponentInputBinder\n  }];\n  return routerFeature(8 /* RouterFeatureKind.ComponentInputBindingFeature */, providers);\n}\n/**\n * Enables view transitions in the Router by running the route activation and deactivation inside of\n * `document.startViewTransition`.\n *\n * Note: The View Transitions API is not available in all browsers. If the browser does not support\n * view transitions, the Router will not attempt to start a view transition and continue processing\n * the navigation as usual.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withViewTransitions())\n *     ]\n *   }\n * );\n * ```\n *\n * @returns A set of providers for use with `provideRouter`.\n * @see https://developer.chrome.com/docs/web-platform/view-transitions/\n * @see https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API\n * @developerPreview\n */\nfunction withViewTransitions(options) {\n  _performanceMarkFeature('NgRouterViewTransitions');\n  const providers = [{\n    provide: CREATE_VIEW_TRANSITION,\n    useValue: createViewTransition\n  }, {\n    provide: VIEW_TRANSITION_OPTIONS,\n    useValue: _objectSpread({\n      skipNextTransition: !!(options !== null && options !== void 0 && options.skipInitialTransition)\n    }, options)\n  }];\n  return routerFeature(9 /* RouterFeatureKind.ViewTransitionsFeature */, providers);\n}\n\n/**\n * The directives defined in the `RouterModule`.\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent];\n/**\n * @docsNotRequired\n */\nconst ROUTER_FORROOT_GUARD = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router duplicate forRoot guard' : '');\n// TODO(atscott): All of these except `ActivatedRoute` are `providedIn: 'root'`. They are only kept\n// here to avoid a breaking change whereby the provider order matters based on where the\n// `RouterModule`/`RouterTestingModule` is imported. These can/should be removed as a \"breaking\"\n// change in a major version.\nconst ROUTER_PROVIDERS = [Location, {\n  provide: UrlSerializer,\n  useClass: DefaultUrlSerializer\n}, Router, ChildrenOutletContexts, {\n  provide: ActivatedRoute,\n  useFactory: rootRoute,\n  deps: [Router]\n}, RouterConfigLoader,\n// Only used to warn when `provideRoutes` is used without `RouterModule` or `provideRouter`. Can\n// be removed when `provideRoutes` is removed.\ntypeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: ROUTER_IS_PROVIDED,\n  useValue: true\n} : []];\n/**\n * @description\n *\n * Adds directives and providers for in-app navigation among views defined in an application.\n * Use the Angular `Router` service to declaratively specify application states and manage state\n * transitions.\n *\n * You can import this NgModule multiple times, once for each lazy-loaded bundle.\n * However, only one `Router` service can be active.\n * To ensure this, there are two ways to register routes when importing this module:\n *\n * * The `forRoot()` method creates an `NgModule` that contains all the directives, the given\n * routes, and the `Router` service itself.\n * * The `forChild()` method creates an `NgModule` that contains all the directives and the given\n * routes, but does not include the `Router` service.\n *\n * @see [Routing and Navigation guide](guide/routing/common-router-tasks) for an\n * overview of how the `Router` service should be used.\n *\n * @publicApi\n */\nclass RouterModule {\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      inject(ROUTER_FORROOT_GUARD, {\n        optional: true\n      });\n    }\n  }\n  /**\n   * Creates and configures a module with all the router providers and directives.\n   * Optionally sets up an application listener to perform an initial navigation.\n   *\n   * When registering the NgModule at the root, import as follows:\n   *\n   * ```ts\n   * @NgModule({\n   *   imports: [RouterModule.forRoot(ROUTES)]\n   * })\n   * class MyNgModule {}\n   * ```\n   *\n   * @param routes An array of `Route` objects that define the navigation paths for the application.\n   * @param config An `ExtraOptions` configuration object that controls how navigation is performed.\n   * @return The new `NgModule`.\n   *\n   */\n  static forRoot(routes, config) {\n    return {\n      ngModule: RouterModule,\n      providers: [ROUTER_PROVIDERS, typeof ngDevMode === 'undefined' || ngDevMode ? config !== null && config !== void 0 && config.enableTracing ? withDebugTracing().ɵproviders : [] : [], {\n        provide: ROUTES,\n        multi: true,\n        useValue: routes\n      }, typeof ngDevMode === 'undefined' || ngDevMode ? {\n        provide: ROUTER_FORROOT_GUARD,\n        useFactory: provideForRootGuard,\n        deps: [[Router, new Optional(), new SkipSelf()]]\n      } : [], config !== null && config !== void 0 && config.errorHandler ? {\n        provide: NAVIGATION_ERROR_HANDLER,\n        useValue: config.errorHandler\n      } : [], {\n        provide: ROUTER_CONFIGURATION,\n        useValue: config ? config : {}\n      }, config !== null && config !== void 0 && config.useHash ? provideHashLocationStrategy() : providePathLocationStrategy(), provideRouterScroller(), config !== null && config !== void 0 && config.preloadingStrategy ? withPreloading(config.preloadingStrategy).ɵproviders : [], config !== null && config !== void 0 && config.initialNavigation ? provideInitialNavigation(config) : [], config !== null && config !== void 0 && config.bindToComponentInputs ? withComponentInputBinding().ɵproviders : [], config !== null && config !== void 0 && config.enableViewTransitions ? withViewTransitions().ɵproviders : [], provideRouterInitializer()]\n    };\n  }\n  /**\n   * Creates a module with all the router directives and a provider registering routes,\n   * without creating a new Router service.\n   * When registering for submodules and lazy-loaded submodules, create the NgModule as follows:\n   *\n   * ```ts\n   * @NgModule({\n   *   imports: [RouterModule.forChild(ROUTES)]\n   * })\n   * class MyNgModule {}\n   * ```\n   *\n   * @param routes An array of `Route` objects that define the navigation paths for the submodule.\n   * @return The new NgModule.\n   *\n   */\n  static forChild(routes) {\n    return {\n      ngModule: RouterModule,\n      providers: [{\n        provide: ROUTES,\n        multi: true,\n        useValue: routes\n      }]\n    };\n  }\n}\n_RouterModule = RouterModule;\n_defineProperty(RouterModule, \"\\u0275fac\", function _RouterModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RouterModule)();\n});\n_defineProperty(RouterModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _RouterModule,\n  imports: [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent],\n  exports: [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent]\n}));\n_defineProperty(RouterModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterModule, [{\n    type: NgModule,\n    args: [{\n      imports: ROUTER_DIRECTIVES,\n      exports: ROUTER_DIRECTIVES\n    }]\n  }], () => [], null);\n})();\n/**\n * For internal use by `RouterModule` only. Note that this differs from `withInMemoryRouterScroller`\n * because it reads from the `ExtraOptions` which should not be used in the standalone world.\n */\nfunction provideRouterScroller() {\n  return {\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const config = inject(ROUTER_CONFIGURATION);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      if (config.scrollOffset) {\n        viewportScroller.setOffset(config.scrollOffset);\n      }\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, config);\n    }\n  };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` should\n// provide hash location directly via `{provide: LocationStrategy, useClass: HashLocationStrategy}`.\nfunction provideHashLocationStrategy() {\n  return {\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` does not\n// need this at all because `PathLocationStrategy` is the default factory for `LocationStrategy`.\nfunction providePathLocationStrategy() {\n  return {\n    provide: LocationStrategy,\n    useClass: PathLocationStrategy\n  };\n}\nfunction provideForRootGuard(router) {\n  if (router) {\n    throw new _RuntimeError(4007 /* RuntimeErrorCode.FOR_ROOT_CALLED_TWICE */, `The Router was provided more than once. This can happen if 'forRoot' is used outside of the root injector.` + ` Lazy loaded modules should use RouterModule.forChild() instead.`);\n  }\n  return 'guarded';\n}\n// Note: For internal use only with `RouterModule`. Standalone router setup with `provideRouter`\n// users call `withXInitialNavigation` directly.\nfunction provideInitialNavigation(config) {\n  return [config.initialNavigation === 'disabled' ? withDisabledInitialNavigation().ɵproviders : [], config.initialNavigation === 'enabledBlocking' ? withEnabledBlockingInitialNavigation().ɵproviders : []];\n}\n// TODO(atscott): This should not be in the public API\n/**\n * A DI token for the router initializer that\n * is called after the app is bootstrapped.\n *\n * @publicApi\n */\nconst ROUTER_INITIALIZER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Router Initializer' : '');\nfunction provideRouterInitializer() {\n  return [\n  // ROUTER_INITIALIZER token should be removed. It's public API but shouldn't be. We can just\n  // have `getBootstrapListener` directly attached to APP_BOOTSTRAP_LISTENER.\n  {\n    provide: ROUTER_INITIALIZER,\n    useFactory: getBootstrapListener\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useExisting: ROUTER_INITIALIZER\n  }];\n}\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, ROUTER_PROVIDERS, RouterLink, RouterLinkActive, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions };\n//# sourceMappingURL=router_module-DTJgGWLd.mjs.map", "map": {"version": 3, "names": ["i3", "LOCATION_INITIALIZED", "HashLocationStrategy", "LocationStrategy", "ViewportScroller", "Location", "PathLocationStrategy", "i0", "ɵRuntimeError", "_RuntimeError", "ɵɵsanitizeUrlOrResourceUrl", "__sanitizeUrlOrResourceUrl", "booleanAttribute", "HostListener", "Input", "HostBinding", "Attribute", "Directive", "EventEmitter", "Output", "ContentChildren", "Optional", "createEnvironmentInjector", "Injectable", "InjectionToken", "ɵperformanceMarkFeature", "_performanceMarkFeature", "makeEnvironmentProviders", "APP_BOOTSTRAP_LISTENER", "ENVIRONMENT_INITIALIZER", "provideAppInitializer", "inject", "Injector", "ApplicationRef", "InjectFlags", "NgZone", "SkipSelf", "NgModule", "NavigationEnd", "isUrlTree", "Router", "ActivatedRoute", "RouterConfigLoader", "NavigationStart", "NavigationSkipped", "NavigationSkippedCode", "<PERSON><PERSON>", "UrlSerializer", "NavigationTransitions", "ROUTES", "afterNextNavigation", "ROUTER_CONFIGURATION", "NAVIGATION_ERROR_HANDLER", "RoutedComponentInputBinder", "INPUT_BINDER", "createViewTransition", "CREATE_VIEW_TRANSITION", "VIEW_TRANSITION_OPTIONS", "stringifyEvent", "DefaultUrlSerializer", "ChildrenOutletContexts", "RouterOutlet", "ɵEmptyOutletComponent", "_EmptyOutletComponent", "Subject", "of", "from", "mergeAll", "catchError", "filter", "concatMap", "mergeMap", "RouterLink", "constructor", "router", "route", "tabIndexAttribute", "renderer", "el", "locationStrategy", "_el$nativeElement$tag", "_defineProperty", "tagName", "nativeElement", "toLowerCase", "isAnchorElement", "subscription", "events", "subscribe", "s", "updateHref", "setTabIndexIfNotOnNativeEl", "newTabIndex", "applyAttributeValue", "ngOnChanges", "changes", "ngDevMode", "routerLinkInput", "fragment", "undefined", "queryParams", "queryParamsHandling", "preserveFragment", "relativeTo", "onChanges", "next", "routerLink", "commandsOrUrlTree", "Array", "isArray", "onClick", "button", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "urlTree", "target", "extras", "skipLocationChange", "replaceUrl", "state", "info", "navigateByUrl", "ngOnDestroy", "_this$subscription", "unsubscribe", "_this$locationStrateg", "href", "prepareExternalUrl", "serializeUrl", "sanitizedValue", "attrName", "attrValue", "setAttribute", "removeAttribute", "createUrlTree", "_RouterLink", "_RouterLink_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ɵɵinjectAttribute", "Renderer2", "ElementRef", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "_RouterLink_HostBindings", "rf", "ctx", "ɵɵlistener", "_RouterLink_click_HostBindingHandler", "$event", "ɵɵattribute", "inputs", "features", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "args", "selector", "decorators", "transform", "RouterLinkActive", "isActive", "_isActive", "element", "cdr", "link", "exact", "routerEventsSubscription", "update", "ngAfterContentInit", "links", "pipe", "_", "subscribeToEachLinkOnChanges", "_this$linkInputChange", "linkInputChangesSubscription", "allLinkChanges", "toArray", "map", "isLinkActive", "routerLinkActive", "data", "classes", "split", "c", "_this$linkInputChange2", "navigated", "queueMicrotask", "hasActiveLinks", "for<PERSON>ach", "addClass", "removeClass", "ariaCurrentWhenActive", "toString", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isActiveChange", "emit", "options", "isActiveMatchOptions", "routerLinkActiveOptions", "isActiveCheckFn", "some", "_RouterLinkActive", "_RouterLinkActive_Factory", "ChangeDetectorRef", "contentQueries", "_RouterLinkActive_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "outputs", "exportAs", "descendants", "paths", "PreloadingStrategy", "PreloadAllModules", "preload", "fn", "_PreloadAllModules", "_PreloadAllModules_Factory", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "NoPreloading", "_NoPreloading", "_NoPreloading_Factory", "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "injector", "preloadingStrategy", "loader", "setUpPreloading", "e", "processRoutes", "config", "routes", "res", "_route$_injector", "_route$_loadedInjecto", "providers", "_injector", "path", "injectorForCurrentRoute", "injectorForChildren", "_loadedInjector", "loadChildren", "_loadedRoutes", "canLoad", "loadComponent", "_loadedComponent", "push", "preloadConfig", "children", "_route$children", "loadedChildren$", "recursiveLoadChildren$", "_config$injector", "loadComponent$", "_<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "_RouterPreloader_Factory", "ɵɵinject", "EnvironmentInjector", "ROUTER_SCROLLER", "RouterS<PERSON>roller", "urlSerializer", "transitions", "viewportScroller", "zone", "scrollPositionRestoration", "anchorScrolling", "init", "setHistoryScrollRestoration", "createScrollEvents", "scrollEventsSubscription", "consumeScrollEvents", "store", "lastId", "getScrollPosition", "lastSource", "navigationTrigger", "restoredId", "restoredState", "navigationId", "id", "scheduleScrollEvent", "parse", "urlAfterRedirects", "code", "IgnoredSameUrlNavigation", "url", "position", "scrollToPosition", "anchor", "scrollToAnchor", "routerEvent", "runOutsideAngular", "setTimeout", "run", "_this$routerEventsSub", "_this$scrollEventsSub", "_Router<PERSON><PERSON>roller", "_RouterScroller_Factory", "ɵɵinvalidFactory", "provideRouter", "provide", "multi", "useValue", "ROUTER_IS_PROVIDED", "useFactory", "rootRoute", "deps", "getBootstrapListener", "feature", "ɵproviders", "routerState", "root", "routerFeature", "kind", "ɵkind", "routerIsProvidedDevModeCheck", "console", "warn", "provideRoutes", "withInMemoryScrolling", "bootstrappedComponentRef", "_injector$get", "_injector$get2", "ref", "get", "components", "bootstrapDone", "BOOTSTRAP_DONE", "INITIAL_NAVIGATION", "initialNavigation", "ROUTER_PRELOADER", "resetRootComponentType", "componentTypes", "closed", "complete", "withEnabledBlockingInitialNavigation", "locationInitialized", "Promise", "resolve", "then", "afterPreactivation", "withDisabledInitialNavigation", "setUpLocationChangeListener", "withDebugTracing", "_console$group", "_console", "_console$groupEnd", "_console2", "group", "call", "name", "log", "groupEnd", "withPreloading", "useExisting", "withRouterConfig", "withHashLocation", "useClass", "withNavigationErrorHandler", "handler", "withComponentInputBinding", "withViewTransitions", "_objectSpread", "skipNextTransition", "skipInitialTransition", "ROUTER_DIRECTIVES", "ROUTER_FORROOT_GUARD", "ROUTER_PROVIDERS", "RouterModule", "optional", "forRoot", "ngModule", "enableTracing", "provideForRootGuard", "<PERSON><PERSON><PERSON><PERSON>", "useHash", "provideHashLocationStrategy", "providePathLocationStrategy", "provideRouterScroller", "provideInitialNavigation", "bindToComponentInputs", "enableViewTransitions", "provideRouterInitializer", "<PERSON><PERSON><PERSON><PERSON>", "_RouterModule", "_RouterModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector", "scrollOffset", "setOffset", "ROUTER_INITIALIZER"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/router/fesm2022/router_module-DTJgGWLd.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i3 from '@angular/common';\nimport { LOCATION_INITIALIZED, HashLocationStrategy, LocationStrategy, ViewportScroller, Location, PathLocationStrategy } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, ɵɵsanitizeUrlOrResourceUrl as __sanitizeUrlOrResourceUrl, booleanAttribute, HostListener, Input, HostBinding, Attribute, Directive, EventEmitter, Output, ContentChildren, Optional, createEnvironmentInjector, Injectable, InjectionToken, ɵperformanceMarkFeature as _performanceMarkFeature, makeEnvironmentProviders, APP_BOOTSTRAP_LISTENER, ENVIRONMENT_INITIALIZER, provideAppInitializer, inject, Injector, ApplicationRef, InjectFlags, NgZone, SkipSelf, NgModule } from '@angular/core';\nimport { NavigationEnd, isUrlTree, Router, ActivatedRoute, RouterConfigLoader, NavigationStart, NavigationSkipped, NavigationSkippedCode, Scroll, UrlSerializer, NavigationTransitions, ROUTES, afterNextNavigation, ROUTER_CONFIGURATION, NAVIGATION_ERROR_HANDLER, RoutedComponentInputBinder, INPUT_BINDER, createViewTransition, CREATE_VIEW_TRANSITION, VIEW_TRANSITION_OPTIONS, stringifyEvent, DefaultUrlSerializer, ChildrenOutletContexts, RouterOutlet, ɵEmptyOutletComponent as _EmptyOutletComponent } from './router-Dwfin5Au.mjs';\nimport { Subject, of, from } from 'rxjs';\nimport { mergeAll, catchError, filter, concatMap, mergeMap } from 'rxjs/operators';\n\n/**\n * @description\n *\n * When applied to an element in a template, makes that element a link\n * that initiates navigation to a route. Navigation opens one or more routed components\n * in one or more `<router-outlet>` locations on the page.\n *\n * Given a route configuration `[{ path: 'user/:name', component: UserCmp }]`,\n * the following creates a static link to the route:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n *\n * You can use dynamic values to generate the link.\n * For a dynamic link, pass an array of path segments,\n * followed by the params for each segment.\n * For example, `['/team', teamId, 'user', userName, {details: true}]`\n * generates a link to `/team/11/user/bob;details=true`.\n *\n * Multiple static segments can be merged into one term and combined with dynamic segments.\n * For example, `['/team/11/user', userName, {details: true}]`\n *\n * The input that you provide to the link is treated as a delta to the current URL.\n * For instance, suppose the current URL is `/user/(box//aux:team)`.\n * The link `<a [routerLink]=\"['/user/jim']\">Jim</a>` creates the URL\n * `/user/(jim//aux:team)`.\n * See {@link Router#createUrlTree} for more information.\n *\n * @usageNotes\n *\n * You can use absolute or relative paths in a link, set query parameters,\n * control how parameters are handled, and keep a history of navigation states.\n *\n * ### Relative link paths\n *\n * The first segment name can be prepended with `/`, `./`, or `../`.\n * * If the first segment begins with `/`, the router looks up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router\n *   looks in the children of the current activated route.\n * * If the first segment begins with `../`, the router goes up one level in the route tree.\n *\n * ### Setting and handling query params and fragments\n *\n * The following link adds a query parameter and a fragment to the generated URL:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * By default, the directive constructs the new URL using the given query parameters.\n * The example generates the link: `/user/bob?debug=true#education`.\n *\n * You can instruct the directive to handle query parameters differently\n * by specifying the `queryParamsHandling` option in the link.\n * Allowed values are:\n *\n *  - `'merge'`: Merge the given `queryParams` into the current query params.\n *  - `'preserve'`: Preserve the current query params.\n *\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n *\n * `queryParams`, `fragment`, `queryParamsHandling`, `preserveFragment`, and `relativeTo`\n * cannot be used when the `routerLink` input is a `UrlTree`.\n *\n * See {@link UrlCreationOptions#queryParamsHandling}.\n *\n * ### Preserving navigation history\n *\n * You can provide a `state` value to be persisted to the browser's\n * [`History.state` property](https://developer.mozilla.org/en-US/docs/Web/API/History#Properties).\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [state]=\"{tracingId: 123}\">\n *   link to user component\n * </a>\n * ```\n *\n * Use {@link Router#getCurrentNavigation} to retrieve a saved\n * navigation-state value. For example, to capture the `tracingId` during the `NavigationStart`\n * event:\n *\n * ```ts\n * // Get NavigationStart events\n * router.events.pipe(filter(e => e instanceof NavigationStart)).subscribe(e => {\n *   const navigation = router.getCurrentNavigation();\n *   tracingService.trace({id: navigation.extras.state.tracingId});\n * });\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLink {\n    router;\n    route;\n    tabIndexAttribute;\n    renderer;\n    el;\n    locationStrategy;\n    /**\n     * Represents an `href` attribute value applied to a host element,\n     * when a host element is `<a>`. For other tags, the value is `null`.\n     */\n    href = null;\n    /**\n     * Represents the `target` attribute on a host element.\n     * This is only used when the host element is an `<a>` tag.\n     */\n    target;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParams}\n     * @see {@link Router#createUrlTree}\n     */\n    queryParams;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#fragment}\n     * @see {@link Router#createUrlTree}\n     */\n    fragment;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParamsHandling}\n     * @see {@link Router#createUrlTree}\n     */\n    queryParamsHandling;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#state}\n     * @see {@link Router#navigateByUrl}\n     */\n    state;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#info}\n     * @see {@link Router#navigateByUrl}\n     */\n    info;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * Specify a value here when you do not want to use the default value\n     * for `routerLink`, which is the current activated route.\n     * Note that a value of `undefined` here will use the `routerLink` default.\n     * @see {@link UrlCreationOptions#relativeTo}\n     * @see {@link Router#createUrlTree}\n     */\n    relativeTo;\n    /** Whether a host element is an `<a>` tag. */\n    isAnchorElement;\n    subscription;\n    /** @internal */\n    onChanges = new Subject();\n    constructor(router, route, tabIndexAttribute, renderer, el, locationStrategy) {\n        this.router = router;\n        this.route = route;\n        this.tabIndexAttribute = tabIndexAttribute;\n        this.renderer = renderer;\n        this.el = el;\n        this.locationStrategy = locationStrategy;\n        const tagName = el.nativeElement.tagName?.toLowerCase();\n        this.isAnchorElement = tagName === 'a' || tagName === 'area';\n        if (this.isAnchorElement) {\n            this.subscription = router.events.subscribe((s) => {\n                if (s instanceof NavigationEnd) {\n                    this.updateHref();\n                }\n            });\n        }\n        else {\n            this.setTabIndexIfNotOnNativeEl('0');\n        }\n    }\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#preserveFragment}\n     * @see {@link Router#createUrlTree}\n     */\n    preserveFragment = false;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#skipLocationChange}\n     * @see {@link Router#navigateByUrl}\n     */\n    skipLocationChange = false;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#replaceUrl}\n     * @see {@link Router#navigateByUrl}\n     */\n    replaceUrl = false;\n    /**\n     * Modifies the tab index if there was not a tabindex attribute on the element during\n     * instantiation.\n     */\n    setTabIndexIfNotOnNativeEl(newTabIndex) {\n        if (this.tabIndexAttribute != null /* both `null` and `undefined` */ || this.isAnchorElement) {\n            return;\n        }\n        this.applyAttributeValue('tabindex', newTabIndex);\n    }\n    /** @docs-private */\n    // TODO(atscott): Remove changes parameter in major version as a breaking change.\n    ngOnChanges(changes) {\n        if (ngDevMode &&\n            isUrlTree(this.routerLinkInput) &&\n            (this.fragment !== undefined ||\n                this.queryParams ||\n                this.queryParamsHandling ||\n                this.preserveFragment ||\n                this.relativeTo)) {\n            throw new _RuntimeError(4016 /* RuntimeErrorCode.INVALID_ROUTER_LINK_INPUTS */, 'Cannot configure queryParams or fragment when using a UrlTree as the routerLink input value.');\n        }\n        if (this.isAnchorElement) {\n            this.updateHref();\n        }\n        // This is subscribed to by `RouterLinkActive` so that it knows to update when there are changes\n        // to the RouterLinks it's tracking.\n        this.onChanges.next(this);\n    }\n    routerLinkInput = null;\n    /**\n     * Commands to pass to {@link Router#createUrlTree} or a `UrlTree`.\n     *   - **array**: commands to pass to {@link Router#createUrlTree}.\n     *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n     *   - **UrlTree**: a `UrlTree` for this link rather than creating one from the commands\n     *     and other inputs that correspond to properties of `UrlCreationOptions`.\n     *   - **null|undefined**: effectively disables the `routerLink`\n     * @see {@link Router#createUrlTree}\n     */\n    set routerLink(commandsOrUrlTree) {\n        if (commandsOrUrlTree == null) {\n            this.routerLinkInput = null;\n            this.setTabIndexIfNotOnNativeEl(null);\n        }\n        else {\n            if (isUrlTree(commandsOrUrlTree)) {\n                this.routerLinkInput = commandsOrUrlTree;\n            }\n            else {\n                this.routerLinkInput = Array.isArray(commandsOrUrlTree)\n                    ? commandsOrUrlTree\n                    : [commandsOrUrlTree];\n            }\n            this.setTabIndexIfNotOnNativeEl('0');\n        }\n    }\n    /** @docs-private */\n    onClick(button, ctrlKey, shiftKey, altKey, metaKey) {\n        const urlTree = this.urlTree;\n        if (urlTree === null) {\n            return true;\n        }\n        if (this.isAnchorElement) {\n            if (button !== 0 || ctrlKey || shiftKey || altKey || metaKey) {\n                return true;\n            }\n            if (typeof this.target === 'string' && this.target != '_self') {\n                return true;\n            }\n        }\n        const extras = {\n            skipLocationChange: this.skipLocationChange,\n            replaceUrl: this.replaceUrl,\n            state: this.state,\n            info: this.info,\n        };\n        this.router.navigateByUrl(urlTree, extras);\n        // Return `false` for `<a>` elements to prevent default action\n        // and cancel the native behavior, since the navigation is handled\n        // by the Router.\n        return !this.isAnchorElement;\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        this.subscription?.unsubscribe();\n    }\n    updateHref() {\n        const urlTree = this.urlTree;\n        this.href =\n            urlTree !== null && this.locationStrategy\n                ? this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(urlTree))\n                : null;\n        const sanitizedValue = this.href === null\n            ? null\n            : // This class represents a directive that can be added to both `<a>` elements,\n                // as well as other elements. As a result, we can't define security context at\n                // compile time. So the security context is deferred to runtime.\n                // The `ɵɵsanitizeUrlOrResourceUrl` selects the necessary sanitizer function\n                // based on the tag and property names. The logic mimics the one from\n                // `packages/compiler/src/schema/dom_security_schema.ts`, which is used at compile time.\n                //\n                // Note: we should investigate whether we can switch to using `@HostBinding('attr.href')`\n                // instead of applying a value via a renderer, after a final merge of the\n                // `RouterLinkWithHref` directive.\n                __sanitizeUrlOrResourceUrl(this.href, this.el.nativeElement.tagName.toLowerCase(), 'href');\n        this.applyAttributeValue('href', sanitizedValue);\n    }\n    applyAttributeValue(attrName, attrValue) {\n        const renderer = this.renderer;\n        const nativeElement = this.el.nativeElement;\n        if (attrValue !== null) {\n            renderer.setAttribute(nativeElement, attrName, attrValue);\n        }\n        else {\n            renderer.removeAttribute(nativeElement, attrName);\n        }\n    }\n    get urlTree() {\n        if (this.routerLinkInput === null) {\n            return null;\n        }\n        else if (isUrlTree(this.routerLinkInput)) {\n            return this.routerLinkInput;\n        }\n        return this.router.createUrlTree(this.routerLinkInput, {\n            // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n            // Otherwise, we should use the value provided by the user in the input.\n            relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n            queryParams: this.queryParams,\n            fragment: this.fragment,\n            queryParamsHandling: this.queryParamsHandling,\n            preserveFragment: this.preserveFragment,\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterLink, deps: [{ token: Router }, { token: ActivatedRoute }, { token: 'tabindex', attribute: true }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i3.LocationStrategy }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.14\", type: RouterLink, isStandalone: true, selector: \"[routerLink]\", inputs: { target: \"target\", queryParams: \"queryParams\", fragment: \"fragment\", queryParamsHandling: \"queryParamsHandling\", state: \"state\", info: \"info\", relativeTo: \"relativeTo\", preserveFragment: [\"preserveFragment\", \"preserveFragment\", booleanAttribute], skipLocationChange: [\"skipLocationChange\", \"skipLocationChange\", booleanAttribute], replaceUrl: [\"replaceUrl\", \"replaceUrl\", booleanAttribute], routerLink: \"routerLink\" }, host: { listeners: { \"click\": \"onClick($event.button,$event.ctrlKey,$event.shiftKey,$event.altKey,$event.metaKey)\" }, properties: { \"attr.target\": \"this.target\" } }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterLink, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[routerLink]',\n                }]\n        }], ctorParameters: () => [{ type: Router }, { type: ActivatedRoute }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i3.LocationStrategy }], propDecorators: { target: [{\n                type: HostBinding,\n                args: ['attr.target']\n            }, {\n                type: Input\n            }], queryParams: [{\n                type: Input\n            }], fragment: [{\n                type: Input\n            }], queryParamsHandling: [{\n                type: Input\n            }], state: [{\n                type: Input\n            }], info: [{\n                type: Input\n            }], relativeTo: [{\n                type: Input\n            }], preserveFragment: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], skipLocationChange: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], replaceUrl: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], routerLink: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', [\n                        '$event.button',\n                        '$event.ctrlKey',\n                        '$event.shiftKey',\n                        '$event.altKey',\n                        '$event.metaKey',\n                    ]]\n            }] } });\n\n/**\n *\n * @description\n *\n * Tracks whether the linked route of an element is currently active, and allows you\n * to specify one or more CSS classes to add to the element when the linked route\n * is active.\n *\n * Use this directive to create a visual distinction for elements associated with an active route.\n * For example, the following code highlights the word \"Bob\" when the router\n * activates the associated route:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n *\n * Whenever the URL is either '/user' or '/user/bob', the \"active-link\" class is\n * added to the anchor tag. If the URL changes, the class is removed.\n *\n * You can set more than one class using a space-separated string or an array.\n * For example:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n *\n * To add the classes only when the URL matches the link exactly, add the option `exact: true`:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n *\n * To directly check the `isActive` status of the link, assign the `RouterLinkActive`\n * instance to a template variable.\n * For example, the following checks the status without assigning any CSS classes:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n *\n * You can apply the `RouterLinkActive` directive to an ancestor of linked elements.\n * For example, the following sets the active-link class on the `<div>`  parent tag\n * when the URL is either '/user/jim' or '/user/bob'.\n *\n * ```html\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n *\n * The `RouterLinkActive` directive can also be used to set the aria-current attribute\n * to provide an alternative distinction for active elements to visually impaired users.\n *\n * For example, the following code adds the 'active' class to the Home Page link when it is\n * indeed active and in such case also sets its aria-current attribute to 'page':\n *\n * ```html\n * <a routerLink=\"/\" routerLinkActive=\"active\" ariaCurrentWhenActive=\"page\">Home Page</a>\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLinkActive {\n    router;\n    element;\n    renderer;\n    cdr;\n    link;\n    links;\n    classes = [];\n    routerEventsSubscription;\n    linkInputChangesSubscription;\n    _isActive = false;\n    get isActive() {\n        return this._isActive;\n    }\n    /**\n     * Options to configure how to determine if the router link is active.\n     *\n     * These options are passed to the `Router.isActive()` function.\n     *\n     * @see {@link Router#isActive}\n     */\n    routerLinkActiveOptions = { exact: false };\n    /**\n     * Aria-current attribute to apply when the router link is active.\n     *\n     * Possible values: `'page'` | `'step'` | `'location'` | `'date'` | `'time'` | `true` | `false`.\n     *\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-current}\n     */\n    ariaCurrentWhenActive;\n    /**\n     *\n     * You can use the output `isActiveChange` to get notified each time the link becomes\n     * active or inactive.\n     *\n     * Emits:\n     * true  -> Route is active\n     * false -> Route is inactive\n     *\n     * ```html\n     * <a\n     *  routerLink=\"/user/bob\"\n     *  routerLinkActive=\"active-link\"\n     *  (isActiveChange)=\"this.onRouterLinkActive($event)\">Bob</a>\n     * ```\n     */\n    isActiveChange = new EventEmitter();\n    constructor(router, element, renderer, cdr, link) {\n        this.router = router;\n        this.element = element;\n        this.renderer = renderer;\n        this.cdr = cdr;\n        this.link = link;\n        this.routerEventsSubscription = router.events.subscribe((s) => {\n            if (s instanceof NavigationEnd) {\n                this.update();\n            }\n        });\n    }\n    /** @docs-private */\n    ngAfterContentInit() {\n        // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).\n        of(this.links.changes, of(null))\n            .pipe(mergeAll())\n            .subscribe((_) => {\n            this.update();\n            this.subscribeToEachLinkOnChanges();\n        });\n    }\n    subscribeToEachLinkOnChanges() {\n        this.linkInputChangesSubscription?.unsubscribe();\n        const allLinkChanges = [...this.links.toArray(), this.link]\n            .filter((link) => !!link)\n            .map((link) => link.onChanges);\n        this.linkInputChangesSubscription = from(allLinkChanges)\n            .pipe(mergeAll())\n            .subscribe((link) => {\n            if (this._isActive !== this.isLinkActive(this.router)(link)) {\n                this.update();\n            }\n        });\n    }\n    set routerLinkActive(data) {\n        const classes = Array.isArray(data) ? data : data.split(' ');\n        this.classes = classes.filter((c) => !!c);\n    }\n    /** @docs-private */\n    ngOnChanges(changes) {\n        this.update();\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        this.routerEventsSubscription.unsubscribe();\n        this.linkInputChangesSubscription?.unsubscribe();\n    }\n    update() {\n        if (!this.links || !this.router.navigated)\n            return;\n        queueMicrotask(() => {\n            const hasActiveLinks = this.hasActiveLinks();\n            this.classes.forEach((c) => {\n                if (hasActiveLinks) {\n                    this.renderer.addClass(this.element.nativeElement, c);\n                }\n                else {\n                    this.renderer.removeClass(this.element.nativeElement, c);\n                }\n            });\n            if (hasActiveLinks && this.ariaCurrentWhenActive !== undefined) {\n                this.renderer.setAttribute(this.element.nativeElement, 'aria-current', this.ariaCurrentWhenActive.toString());\n            }\n            else {\n                this.renderer.removeAttribute(this.element.nativeElement, 'aria-current');\n            }\n            // Only emit change if the active state changed.\n            if (this._isActive !== hasActiveLinks) {\n                this._isActive = hasActiveLinks;\n                this.cdr.markForCheck();\n                // Emit on isActiveChange after classes are updated\n                this.isActiveChange.emit(hasActiveLinks);\n            }\n        });\n    }\n    isLinkActive(router) {\n        const options = isActiveMatchOptions(this.routerLinkActiveOptions)\n            ? this.routerLinkActiveOptions\n            : // While the types should disallow `undefined` here, it's possible without strict inputs\n                this.routerLinkActiveOptions.exact || false;\n        return (link) => {\n            const urlTree = link.urlTree;\n            return urlTree ? router.isActive(urlTree, options) : false;\n        };\n    }\n    hasActiveLinks() {\n        const isActiveCheckFn = this.isLinkActive(this.router);\n        return (this.link && isActiveCheckFn(this.link)) || this.links.some(isActiveCheckFn);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterLinkActive, deps: [{ token: Router }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: RouterLink, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.14\", type: RouterLinkActive, isStandalone: true, selector: \"[routerLinkActive]\", inputs: { routerLinkActiveOptions: \"routerLinkActiveOptions\", ariaCurrentWhenActive: \"ariaCurrentWhenActive\", routerLinkActive: \"routerLinkActive\" }, outputs: { isActiveChange: \"isActiveChange\" }, queries: [{ propertyName: \"links\", predicate: RouterLink, descendants: true }], exportAs: [\"routerLinkActive\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterLinkActive, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[routerLinkActive]',\n                    exportAs: 'routerLinkActive',\n                }]\n        }], ctorParameters: () => [{ type: Router }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: RouterLink, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { links: [{\n                type: ContentChildren,\n                args: [RouterLink, { descendants: true }]\n            }], routerLinkActiveOptions: [{\n                type: Input\n            }], ariaCurrentWhenActive: [{\n                type: Input\n            }], isActiveChange: [{\n                type: Output\n            }], routerLinkActive: [{\n                type: Input\n            }] } });\n/**\n * Use instead of `'paths' in options` to be compatible with property renaming\n */\nfunction isActiveMatchOptions(options) {\n    return !!options.paths;\n}\n\n/**\n * @description\n *\n * Provides a preloading strategy.\n *\n * @publicApi\n */\nclass PreloadingStrategy {\n}\n/**\n * @description\n *\n * Provides a preloading strategy that preloads all modules as quickly as possible.\n *\n * ```ts\n * RouterModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n *\n * @publicApi\n */\nclass PreloadAllModules {\n    preload(route, fn) {\n        return fn().pipe(catchError(() => of(null)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreloadAllModules, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreloadAllModules, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreloadAllModules, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * @description\n *\n * Provides a preloading strategy that does not preload any modules.\n *\n * This strategy is enabled by default.\n *\n * @publicApi\n */\nclass NoPreloading {\n    preload(route, fn) {\n        return of(null);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: NoPreloading, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: NoPreloading, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: NoPreloading, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n *\n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n *\n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n *\n * @publicApi\n */\nclass RouterPreloader {\n    router;\n    injector;\n    preloadingStrategy;\n    loader;\n    subscription;\n    constructor(router, injector, preloadingStrategy, loader) {\n        this.router = router;\n        this.injector = injector;\n        this.preloadingStrategy = preloadingStrategy;\n        this.loader = loader;\n    }\n    setUpPreloading() {\n        this.subscription = this.router.events\n            .pipe(filter((e) => e instanceof NavigationEnd), concatMap(() => this.preload()))\n            .subscribe(() => { });\n    }\n    preload() {\n        return this.processRoutes(this.injector, this.router.config);\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n    processRoutes(injector, routes) {\n        const res = [];\n        for (const route of routes) {\n            if (route.providers && !route._injector) {\n                route._injector = createEnvironmentInjector(route.providers, injector, `Route: ${route.path}`);\n            }\n            const injectorForCurrentRoute = route._injector ?? injector;\n            const injectorForChildren = route._loadedInjector ?? injectorForCurrentRoute;\n            // Note that `canLoad` is only checked as a condition that prevents `loadChildren` and not\n            // `loadComponent`. `canLoad` guards only block loading of child routes by design. This\n            // happens as a consequence of needing to descend into children for route matching immediately\n            // while component loading is deferred until route activation. Because `canLoad` guards can\n            // have side effects, we cannot execute them here so we instead skip preloading altogether\n            // when present. Lastly, it remains to be decided whether `canLoad` should behave this way\n            // at all. Code splitting and lazy loading is separate from client-side authorization checks\n            // and should not be used as a security measure to prevent loading of code.\n            if ((route.loadChildren && !route._loadedRoutes && route.canLoad === undefined) ||\n                (route.loadComponent && !route._loadedComponent)) {\n                res.push(this.preloadConfig(injectorForCurrentRoute, route));\n            }\n            if (route.children || route._loadedRoutes) {\n                res.push(this.processRoutes(injectorForChildren, (route.children ?? route._loadedRoutes)));\n            }\n        }\n        return from(res).pipe(mergeAll());\n    }\n    preloadConfig(injector, route) {\n        return this.preloadingStrategy.preload(route, () => {\n            let loadedChildren$;\n            if (route.loadChildren && route.canLoad === undefined) {\n                loadedChildren$ = this.loader.loadChildren(injector, route);\n            }\n            else {\n                loadedChildren$ = of(null);\n            }\n            const recursiveLoadChildren$ = loadedChildren$.pipe(mergeMap((config) => {\n                if (config === null) {\n                    return of(void 0);\n                }\n                route._loadedRoutes = config.routes;\n                route._loadedInjector = config.injector;\n                // If the loaded config was a module, use that as the module/module injector going\n                // forward. Otherwise, continue using the current module/module injector.\n                return this.processRoutes(config.injector ?? injector, config.routes);\n            }));\n            if (route.loadComponent && !route._loadedComponent) {\n                const loadComponent$ = this.loader.loadComponent(route);\n                return from([recursiveLoadChildren$, loadComponent$]).pipe(mergeAll());\n            }\n            else {\n                return recursiveLoadChildren$;\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterPreloader, deps: [{ token: Router }, { token: i0.EnvironmentInjector }, { token: PreloadingStrategy }, { token: RouterConfigLoader }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterPreloader, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterPreloader, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: Router }, { type: i0.EnvironmentInjector }, { type: PreloadingStrategy }, { type: RouterConfigLoader }] });\n\nconst ROUTER_SCROLLER = new InjectionToken('');\nclass RouterScroller {\n    urlSerializer;\n    transitions;\n    viewportScroller;\n    zone;\n    options;\n    routerEventsSubscription;\n    scrollEventsSubscription;\n    lastId = 0;\n    lastSource = 'imperative';\n    restoredId = 0;\n    store = {};\n    /** @docs-private */\n    constructor(urlSerializer, transitions, viewportScroller, zone, options = {}) {\n        this.urlSerializer = urlSerializer;\n        this.transitions = transitions;\n        this.viewportScroller = viewportScroller;\n        this.zone = zone;\n        this.options = options;\n        // Default both options to 'disabled'\n        options.scrollPositionRestoration ||= 'disabled';\n        options.anchorScrolling ||= 'disabled';\n    }\n    init() {\n        // we want to disable the automatic scrolling because having two places\n        // responsible for scrolling results race conditions, especially given\n        // that browser don't implement this behavior consistently\n        if (this.options.scrollPositionRestoration !== 'disabled') {\n            this.viewportScroller.setHistoryScrollRestoration('manual');\n        }\n        this.routerEventsSubscription = this.createScrollEvents();\n        this.scrollEventsSubscription = this.consumeScrollEvents();\n    }\n    createScrollEvents() {\n        return this.transitions.events.subscribe((e) => {\n            if (e instanceof NavigationStart) {\n                // store the scroll position of the current stable navigations.\n                this.store[this.lastId] = this.viewportScroller.getScrollPosition();\n                this.lastSource = e.navigationTrigger;\n                this.restoredId = e.restoredState ? e.restoredState.navigationId : 0;\n            }\n            else if (e instanceof NavigationEnd) {\n                this.lastId = e.id;\n                this.scheduleScrollEvent(e, this.urlSerializer.parse(e.urlAfterRedirects).fragment);\n            }\n            else if (e instanceof NavigationSkipped &&\n                e.code === NavigationSkippedCode.IgnoredSameUrlNavigation) {\n                this.lastSource = undefined;\n                this.restoredId = 0;\n                this.scheduleScrollEvent(e, this.urlSerializer.parse(e.url).fragment);\n            }\n        });\n    }\n    consumeScrollEvents() {\n        return this.transitions.events.subscribe((e) => {\n            if (!(e instanceof Scroll))\n                return;\n            // a popstate event. The pop state event will always ignore anchor scrolling.\n            if (e.position) {\n                if (this.options.scrollPositionRestoration === 'top') {\n                    this.viewportScroller.scrollToPosition([0, 0]);\n                }\n                else if (this.options.scrollPositionRestoration === 'enabled') {\n                    this.viewportScroller.scrollToPosition(e.position);\n                }\n                // imperative navigation \"forward\"\n            }\n            else {\n                if (e.anchor && this.options.anchorScrolling === 'enabled') {\n                    this.viewportScroller.scrollToAnchor(e.anchor);\n                }\n                else if (this.options.scrollPositionRestoration !== 'disabled') {\n                    this.viewportScroller.scrollToPosition([0, 0]);\n                }\n            }\n        });\n    }\n    scheduleScrollEvent(routerEvent, anchor) {\n        this.zone.runOutsideAngular(() => {\n            // The scroll event needs to be delayed until after change detection. Otherwise, we may\n            // attempt to restore the scroll position before the router outlet has fully rendered the\n            // component by executing its update block of the template function.\n            setTimeout(() => {\n                this.zone.run(() => {\n                    this.transitions.events.next(new Scroll(routerEvent, this.lastSource === 'popstate' ? this.store[this.restoredId] : null, anchor));\n                });\n            }, 0);\n        });\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        this.routerEventsSubscription?.unsubscribe();\n        this.scrollEventsSubscription?.unsubscribe();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterScroller, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterScroller });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterScroller, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: UrlSerializer }, { type: NavigationTransitions }, { type: i3.ViewportScroller }, { type: i0.NgZone }, { type: undefined }] });\n\n/**\n * Sets up providers necessary to enable `Router` functionality for the application.\n * Allows to configure a set of routes as well as extra features that should be enabled.\n *\n * @usageNotes\n *\n * Basic example of how you can add a Router to your application:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent, {\n *   providers: [provideRouter(appRoutes)]\n * });\n * ```\n *\n * You can also enable optional features in the Router by adding functions from the `RouterFeatures`\n * type:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes,\n *         withDebugTracing(),\n *         withRouterConfig({paramsInheritanceStrategy: 'always'}))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link RouterFeatures}\n *\n * @publicApi\n * @param routes A set of `Route`s to use for the application routing table.\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to setup a Router.\n */\nfunction provideRouter(routes, ...features) {\n    return makeEnvironmentProviders([\n        { provide: ROUTES, multi: true, useValue: routes },\n        typeof ngDevMode === 'undefined' || ngDevMode\n            ? { provide: ROUTER_IS_PROVIDED, useValue: true }\n            : [],\n        { provide: ActivatedRoute, useFactory: rootRoute, deps: [Router] },\n        { provide: APP_BOOTSTRAP_LISTENER, multi: true, useFactory: getBootstrapListener },\n        features.map((feature) => feature.ɵproviders),\n    ]);\n}\nfunction rootRoute(router) {\n    return router.routerState.root;\n}\n/**\n * Helper function to create an object that represents a Router feature.\n */\nfunction routerFeature(kind, providers) {\n    return { ɵkind: kind, ɵproviders: providers };\n}\n/**\n * An Injection token used to indicate whether `provideRouter` or `RouterModule.forRoot` was ever\n * called.\n */\nconst ROUTER_IS_PROVIDED = new InjectionToken('', {\n    providedIn: 'root',\n    factory: () => false,\n});\nconst routerIsProvidedDevModeCheck = {\n    provide: ENVIRONMENT_INITIALIZER,\n    multi: true,\n    useFactory() {\n        return () => {\n            if (!inject(ROUTER_IS_PROVIDED)) {\n                console.warn('`provideRoutes` was called without `provideRouter` or `RouterModule.forRoot`. ' +\n                    'This is likely a mistake.');\n            }\n        };\n    },\n};\n/**\n * Registers a DI provider for a set of routes.\n * @param routes The route configuration to provide.\n *\n * @usageNotes\n *\n * ```ts\n * @NgModule({\n *   providers: [provideRoutes(ROUTES)]\n * })\n * class LazyLoadedChildModule {}\n * ```\n *\n * @deprecated If necessary, provide routes using the `ROUTES` `InjectionToken`.\n * @see {@link ROUTES}\n * @publicApi\n */\nfunction provideRoutes(routes) {\n    return [\n        { provide: ROUTES, multi: true, useValue: routes },\n        typeof ngDevMode === 'undefined' || ngDevMode ? routerIsProvidedDevModeCheck : [],\n    ];\n}\n/**\n * Enables customizable scrolling behavior for router navigations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable scrolling feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withInMemoryScrolling())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link ViewportScroller}\n *\n * @publicApi\n * @param options Set of configuration parameters to customize scrolling behavior, see\n *     `InMemoryScrollingOptions` for additional information.\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withInMemoryScrolling(options = {}) {\n    const providers = [\n        {\n            provide: ROUTER_SCROLLER,\n            useFactory: () => {\n                const viewportScroller = inject(ViewportScroller);\n                const zone = inject(NgZone);\n                const transitions = inject(NavigationTransitions);\n                const urlSerializer = inject(UrlSerializer);\n                return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, options);\n            },\n        },\n    ];\n    return routerFeature(4 /* RouterFeatureKind.InMemoryScrollingFeature */, providers);\n}\nfunction getBootstrapListener() {\n    const injector = inject(Injector);\n    return (bootstrappedComponentRef) => {\n        const ref = injector.get(ApplicationRef);\n        if (bootstrappedComponentRef !== ref.components[0]) {\n            return;\n        }\n        const router = injector.get(Router);\n        const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n        if (injector.get(INITIAL_NAVIGATION) === 1 /* InitialNavigation.EnabledNonBlocking */) {\n            router.initialNavigation();\n        }\n        injector.get(ROUTER_PRELOADER, null, InjectFlags.Optional)?.setUpPreloading();\n        injector.get(ROUTER_SCROLLER, null, InjectFlags.Optional)?.init();\n        router.resetRootComponentType(ref.componentTypes[0]);\n        if (!bootstrapDone.closed) {\n            bootstrapDone.next();\n            bootstrapDone.complete();\n            bootstrapDone.unsubscribe();\n        }\n    };\n}\n/**\n * A subject used to indicate that the bootstrapping phase is done. When initial navigation is\n * `enabledBlocking`, the first navigation waits until bootstrapping is finished before continuing\n * to the activation phase.\n */\nconst BOOTSTRAP_DONE = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'bootstrap done indicator' : '', {\n    factory: () => {\n        return new Subject();\n    },\n});\nconst INITIAL_NAVIGATION = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'initial navigation' : '', { providedIn: 'root', factory: () => 1 /* InitialNavigation.EnabledNonBlocking */ });\n/**\n * Configures initial navigation to start before the root component is created.\n *\n * The bootstrap is blocked until the initial navigation is complete. This should be set in case\n * you use [server-side rendering](guide/ssr), but do not enable [hydration](guide/hydration) for\n * your application.\n *\n * @usageNotes\n *\n * Basic example of how you can enable this navigation behavior:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withEnabledBlockingInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @publicApi\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withEnabledBlockingInitialNavigation() {\n    const providers = [\n        { provide: INITIAL_NAVIGATION, useValue: 0 /* InitialNavigation.EnabledBlocking */ },\n        provideAppInitializer(() => {\n            const injector = inject(Injector);\n            const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve());\n            return locationInitialized.then(() => {\n                return new Promise((resolve) => {\n                    const router = injector.get(Router);\n                    const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n                    afterNextNavigation(router, () => {\n                        // Unblock APP_INITIALIZER in case the initial navigation was canceled or errored\n                        // without a redirect.\n                        resolve(true);\n                    });\n                    injector.get(NavigationTransitions).afterPreactivation = () => {\n                        // Unblock APP_INITIALIZER once we get to `afterPreactivation`. At this point, we\n                        // assume activation will complete successfully (even though this is not\n                        // guaranteed).\n                        resolve(true);\n                        return bootstrapDone.closed ? of(void 0) : bootstrapDone;\n                    };\n                    router.initialNavigation();\n                });\n            });\n        }),\n    ];\n    return routerFeature(2 /* RouterFeatureKind.EnabledBlockingInitialNavigationFeature */, providers);\n}\n/**\n * Disables initial navigation.\n *\n * Use if there is a reason to have more control over when the router starts its initial navigation\n * due to some complex initialization logic.\n *\n * @usageNotes\n *\n * Basic example of how you can disable initial navigation:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDisabledInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDisabledInitialNavigation() {\n    const providers = [\n        provideAppInitializer(() => {\n            inject(Router).setUpLocationChangeListener();\n        }),\n        { provide: INITIAL_NAVIGATION, useValue: 2 /* InitialNavigation.Disabled */ },\n    ];\n    return routerFeature(3 /* RouterFeatureKind.DisabledInitialNavigationFeature */, providers);\n}\n/**\n * Enables logging of all internal navigation events to the console.\n * Extra logging might be useful for debugging purposes to inspect Router event sequence.\n *\n * @usageNotes\n *\n * Basic example of how you can enable debug tracing:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDebugTracing())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDebugTracing() {\n    let providers = [];\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        providers = [\n            {\n                provide: ENVIRONMENT_INITIALIZER,\n                multi: true,\n                useFactory: () => {\n                    const router = inject(Router);\n                    return () => router.events.subscribe((e) => {\n                        // tslint:disable:no-console\n                        console.group?.(`Router Event: ${e.constructor.name}`);\n                        console.log(stringifyEvent(e));\n                        console.log(e);\n                        console.groupEnd?.();\n                        // tslint:enable:no-console\n                    });\n                },\n            },\n        ];\n    }\n    else {\n        providers = [];\n    }\n    return routerFeature(1 /* RouterFeatureKind.DebugTracingFeature */, providers);\n}\nconst ROUTER_PRELOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router preloader' : '');\n/**\n * Allows to configure a preloading strategy to use. The strategy is configured by providing a\n * reference to a class that implements a `PreloadingStrategy`.\n *\n * @usageNotes\n *\n * Basic example of how you can configure preloading:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withPreloading(PreloadAllModules))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param preloadingStrategy A reference to a class that implements a `PreloadingStrategy` that\n *     should be used.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withPreloading(preloadingStrategy) {\n    const providers = [\n        { provide: ROUTER_PRELOADER, useExisting: RouterPreloader },\n        { provide: PreloadingStrategy, useExisting: preloadingStrategy },\n    ];\n    return routerFeature(0 /* RouterFeatureKind.PreloadingFeature */, providers);\n}\n/**\n * Allows to provide extra parameters to configure Router.\n *\n * @usageNotes\n *\n * Basic example of how you can provide extra configuration options:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withRouterConfig({\n *          onSameUrlNavigation: 'reload'\n *       }))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param options A set of parameters to configure Router, see `RouterConfigOptions` for\n *     additional information.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withRouterConfig(options) {\n    const providers = [{ provide: ROUTER_CONFIGURATION, useValue: options }];\n    return routerFeature(5 /* RouterFeatureKind.RouterConfigurationFeature */, providers);\n}\n/**\n * Provides the location strategy that uses the URL fragment instead of the history API.\n *\n * @usageNotes\n *\n * Basic example of how you can use the hash location option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withHashLocation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link /api/common/HashLocationStrategy HashLocationStrategy}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withHashLocation() {\n    const providers = [{ provide: LocationStrategy, useClass: HashLocationStrategy }];\n    return routerFeature(6 /* RouterFeatureKind.RouterHashLocationFeature */, providers);\n}\n/**\n * Provides a function which is called when a navigation error occurs.\n *\n * This function is run inside application's [injection context](guide/di/dependency-injection-context)\n * so you can use the [`inject`](api/core/inject) function.\n *\n * This function can return a `RedirectCommand` to convert the error to a redirect, similar to returning\n * a `UrlTree` or `RedirectCommand` from a guard. This will also prevent the `Router` from emitting\n * `NavigationError`; it will instead emit `NavigationCancel` with code NavigationCancellationCode.Redirect.\n * Return values other than `RedirectCommand` are ignored and do not change any behavior with respect to\n * how the `Router` handles the error.\n *\n * @usageNotes\n *\n * Basic example of how you can use the error handler option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withNavigationErrorHandler((e: NavigationError) =>\n * inject(MyErrorTracker).trackError(e)))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link NavigationError}\n * @see {@link /api/core/inject inject}\n * @see {@link runInInjectionContext}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withNavigationErrorHandler(handler) {\n    const providers = [\n        {\n            provide: NAVIGATION_ERROR_HANDLER,\n            useValue: handler,\n        },\n    ];\n    return routerFeature(7 /* RouterFeatureKind.NavigationErrorHandlerFeature */, providers);\n}\n/**\n * Enables binding information from the `Router` state directly to the inputs of the component in\n * `Route` configurations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withComponentInputBinding())\n *     ]\n *   }\n * );\n * ```\n *\n * The router bindings information from any of the following sources:\n *\n *  - query parameters\n *  - path and matrix parameters\n *  - static route data\n *  - data from resolvers\n *\n * Duplicate keys are resolved in the same order from above, from least to greatest,\n * meaning that resolvers have the highest precedence and override any of the other information\n * from the route.\n *\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. This prevents previous information from being\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n * Default values can be provided with a resolver on the route to ensure the value is always present\n * or an input and use an input transform in the component.\n *\n * @see {@link /guide/components/inputs#input-transforms Input Transforms}\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withComponentInputBinding() {\n    const providers = [\n        RoutedComponentInputBinder,\n        { provide: INPUT_BINDER, useExisting: RoutedComponentInputBinder },\n    ];\n    return routerFeature(8 /* RouterFeatureKind.ComponentInputBindingFeature */, providers);\n}\n/**\n * Enables view transitions in the Router by running the route activation and deactivation inside of\n * `document.startViewTransition`.\n *\n * Note: The View Transitions API is not available in all browsers. If the browser does not support\n * view transitions, the Router will not attempt to start a view transition and continue processing\n * the navigation as usual.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withViewTransitions())\n *     ]\n *   }\n * );\n * ```\n *\n * @returns A set of providers for use with `provideRouter`.\n * @see https://developer.chrome.com/docs/web-platform/view-transitions/\n * @see https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API\n * @developerPreview\n */\nfunction withViewTransitions(options) {\n    _performanceMarkFeature('NgRouterViewTransitions');\n    const providers = [\n        { provide: CREATE_VIEW_TRANSITION, useValue: createViewTransition },\n        {\n            provide: VIEW_TRANSITION_OPTIONS,\n            useValue: { skipNextTransition: !!options?.skipInitialTransition, ...options },\n        },\n    ];\n    return routerFeature(9 /* RouterFeatureKind.ViewTransitionsFeature */, providers);\n}\n\n/**\n * The directives defined in the `RouterModule`.\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent];\n/**\n * @docsNotRequired\n */\nconst ROUTER_FORROOT_GUARD = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router duplicate forRoot guard' : '');\n// TODO(atscott): All of these except `ActivatedRoute` are `providedIn: 'root'`. They are only kept\n// here to avoid a breaking change whereby the provider order matters based on where the\n// `RouterModule`/`RouterTestingModule` is imported. These can/should be removed as a \"breaking\"\n// change in a major version.\nconst ROUTER_PROVIDERS = [\n    Location,\n    { provide: UrlSerializer, useClass: DefaultUrlSerializer },\n    Router,\n    ChildrenOutletContexts,\n    { provide: ActivatedRoute, useFactory: rootRoute, deps: [Router] },\n    RouterConfigLoader,\n    // Only used to warn when `provideRoutes` is used without `RouterModule` or `provideRouter`. Can\n    // be removed when `provideRoutes` is removed.\n    typeof ngDevMode === 'undefined' || ngDevMode\n        ? { provide: ROUTER_IS_PROVIDED, useValue: true }\n        : [],\n];\n/**\n * @description\n *\n * Adds directives and providers for in-app navigation among views defined in an application.\n * Use the Angular `Router` service to declaratively specify application states and manage state\n * transitions.\n *\n * You can import this NgModule multiple times, once for each lazy-loaded bundle.\n * However, only one `Router` service can be active.\n * To ensure this, there are two ways to register routes when importing this module:\n *\n * * The `forRoot()` method creates an `NgModule` that contains all the directives, the given\n * routes, and the `Router` service itself.\n * * The `forChild()` method creates an `NgModule` that contains all the directives and the given\n * routes, but does not include the `Router` service.\n *\n * @see [Routing and Navigation guide](guide/routing/common-router-tasks) for an\n * overview of how the `Router` service should be used.\n *\n * @publicApi\n */\nclass RouterModule {\n    constructor() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            inject(ROUTER_FORROOT_GUARD, { optional: true });\n        }\n    }\n    /**\n     * Creates and configures a module with all the router providers and directives.\n     * Optionally sets up an application listener to perform an initial navigation.\n     *\n     * When registering the NgModule at the root, import as follows:\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [RouterModule.forRoot(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the application.\n     * @param config An `ExtraOptions` configuration object that controls how navigation is performed.\n     * @return The new `NgModule`.\n     *\n     */\n    static forRoot(routes, config) {\n        return {\n            ngModule: RouterModule,\n            providers: [\n                ROUTER_PROVIDERS,\n                typeof ngDevMode === 'undefined' || ngDevMode\n                    ? config?.enableTracing\n                        ? withDebugTracing().ɵproviders\n                        : []\n                    : [],\n                { provide: ROUTES, multi: true, useValue: routes },\n                typeof ngDevMode === 'undefined' || ngDevMode\n                    ? {\n                        provide: ROUTER_FORROOT_GUARD,\n                        useFactory: provideForRootGuard,\n                        deps: [[Router, new Optional(), new SkipSelf()]],\n                    }\n                    : [],\n                config?.errorHandler\n                    ? {\n                        provide: NAVIGATION_ERROR_HANDLER,\n                        useValue: config.errorHandler,\n                    }\n                    : [],\n                { provide: ROUTER_CONFIGURATION, useValue: config ? config : {} },\n                config?.useHash ? provideHashLocationStrategy() : providePathLocationStrategy(),\n                provideRouterScroller(),\n                config?.preloadingStrategy ? withPreloading(config.preloadingStrategy).ɵproviders : [],\n                config?.initialNavigation ? provideInitialNavigation(config) : [],\n                config?.bindToComponentInputs ? withComponentInputBinding().ɵproviders : [],\n                config?.enableViewTransitions ? withViewTransitions().ɵproviders : [],\n                provideRouterInitializer(),\n            ],\n        };\n    }\n    /**\n     * Creates a module with all the router directives and a provider registering routes,\n     * without creating a new Router service.\n     * When registering for submodules and lazy-loaded submodules, create the NgModule as follows:\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [RouterModule.forChild(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the submodule.\n     * @return The new NgModule.\n     *\n     */\n    static forChild(routes) {\n        return {\n            ngModule: RouterModule,\n            providers: [{ provide: ROUTES, multi: true, useValue: routes }],\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterModule, imports: [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent], exports: [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: ROUTER_DIRECTIVES,\n                    exports: ROUTER_DIRECTIVES,\n                }]\n        }], ctorParameters: () => [] });\n/**\n * For internal use by `RouterModule` only. Note that this differs from `withInMemoryRouterScroller`\n * because it reads from the `ExtraOptions` which should not be used in the standalone world.\n */\nfunction provideRouterScroller() {\n    return {\n        provide: ROUTER_SCROLLER,\n        useFactory: () => {\n            const viewportScroller = inject(ViewportScroller);\n            const zone = inject(NgZone);\n            const config = inject(ROUTER_CONFIGURATION);\n            const transitions = inject(NavigationTransitions);\n            const urlSerializer = inject(UrlSerializer);\n            if (config.scrollOffset) {\n                viewportScroller.setOffset(config.scrollOffset);\n            }\n            return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, config);\n        },\n    };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` should\n// provide hash location directly via `{provide: LocationStrategy, useClass: HashLocationStrategy}`.\nfunction provideHashLocationStrategy() {\n    return { provide: LocationStrategy, useClass: HashLocationStrategy };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` does not\n// need this at all because `PathLocationStrategy` is the default factory for `LocationStrategy`.\nfunction providePathLocationStrategy() {\n    return { provide: LocationStrategy, useClass: PathLocationStrategy };\n}\nfunction provideForRootGuard(router) {\n    if (router) {\n        throw new _RuntimeError(4007 /* RuntimeErrorCode.FOR_ROOT_CALLED_TWICE */, `The Router was provided more than once. This can happen if 'forRoot' is used outside of the root injector.` +\n            ` Lazy loaded modules should use RouterModule.forChild() instead.`);\n    }\n    return 'guarded';\n}\n// Note: For internal use only with `RouterModule`. Standalone router setup with `provideRouter`\n// users call `withXInitialNavigation` directly.\nfunction provideInitialNavigation(config) {\n    return [\n        config.initialNavigation === 'disabled' ? withDisabledInitialNavigation().ɵproviders : [],\n        config.initialNavigation === 'enabledBlocking'\n            ? withEnabledBlockingInitialNavigation().ɵproviders\n            : [],\n    ];\n}\n// TODO(atscott): This should not be in the public API\n/**\n * A DI token for the router initializer that\n * is called after the app is bootstrapped.\n *\n * @publicApi\n */\nconst ROUTER_INITIALIZER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Router Initializer' : '');\nfunction provideRouterInitializer() {\n    return [\n        // ROUTER_INITIALIZER token should be removed. It's public API but shouldn't be. We can just\n        // have `getBootstrapListener` directly attached to APP_BOOTSTRAP_LISTENER.\n        { provide: ROUTER_INITIALIZER, useFactory: getBootstrapListener },\n        { provide: APP_BOOTSTRAP_LISTENER, multi: true, useExisting: ROUTER_INITIALIZER },\n    ];\n}\n\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, ROUTER_PROVIDERS, RouterLink, RouterLinkActive, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions };\n//# sourceMappingURL=router_module-DTJgGWLd.mjs.map\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,oBAAoB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,iBAAiB;AAChJ,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,aAAa,IAAIC,aAAa,EAAEC,0BAA0B,IAAIC,0BAA0B,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,KAAK,EAAEC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,yBAAyB,EAAEC,UAAU,EAAEC,cAAc,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,wBAAwB,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAC3gB,SAASC,aAAa,EAAEC,SAAS,EAAEC,MAAM,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,wBAAwB,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,YAAY,EAAEC,qBAAqB,IAAIC,qBAAqB,QAAQ,uBAAuB;AAC/gB,SAASC,OAAO,EAAEC,EAAE,EAAEC,IAAI,QAAQ,MAAM;AACxC,SAASC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;;AAElF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EAmEbC,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,gBAAgB,EAAE;IAAA,IAAAC,qBAAA;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IA5D9E;AACJ;AACA;AACA;IAHIA,eAAA,eAIO,IAAI;IACX;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAOA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAOA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAOA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAOA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAOA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IARIA,eAAA;IAUA;IAAAA,eAAA;IAAAA,eAAA;IAGA;IAAAA,eAAA,oBACY,IAAIjB,OAAO,CAAC,CAAC;IAqBzB;AACJ;AACA;AACA;AACA;AACA;IALIiB,eAAA,2BAMmB,KAAK;IACxB;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,6BAMqB,KAAK;IAC1B;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,qBAMa,KAAK;IAAAA,eAAA,0BA8BA,IAAI;IArElB,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,MAAMG,OAAO,IAAAF,qBAAA,GAAGF,EAAE,CAACK,aAAa,CAACD,OAAO,cAAAF,qBAAA,uBAAxBA,qBAAA,CAA0BI,WAAW,CAAC,CAAC;IACvD,IAAI,CAACC,eAAe,GAAGH,OAAO,KAAK,GAAG,IAAIA,OAAO,KAAK,MAAM;IAC5D,IAAI,IAAI,CAACG,eAAe,EAAE;MACtB,IAAI,CAACC,YAAY,GAAGZ,MAAM,CAACa,MAAM,CAACC,SAAS,CAAEC,CAAC,IAAK;QAC/C,IAAIA,CAAC,YAAYnD,aAAa,EAAE;UAC5B,IAAI,CAACoD,UAAU,CAAC,CAAC;QACrB;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACC,0BAA0B,CAAC,GAAG,CAAC;IACxC;EACJ;EAsBA;AACJ;AACA;AACA;EACIA,0BAA0BA,CAACC,WAAW,EAAE;IACpC,IAAI,IAAI,CAAChB,iBAAiB,IAAI,IAAI,CAAC,qCAAqC,IAAI,CAACS,eAAe,EAAE;MAC1F;IACJ;IACA,IAAI,CAACQ,mBAAmB,CAAC,UAAU,EAAED,WAAW,CAAC;EACrD;EACA;EACA;EACAE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIC,SAAS,IACTzD,SAAS,CAAC,IAAI,CAAC0D,eAAe,CAAC,KAC9B,IAAI,CAACC,QAAQ,KAAKC,SAAS,IACxB,IAAI,CAACC,WAAW,IAChB,IAAI,CAACC,mBAAmB,IACxB,IAAI,CAACC,gBAAgB,IACrB,IAAI,CAACC,UAAU,CAAC,EAAE;MACtB,MAAM,IAAI9F,aAAa,CAAC,IAAI,CAAC,mDAAmD,8FAA8F,CAAC;IACnL;IACA,IAAI,IAAI,CAAC4E,eAAe,EAAE;MACtB,IAAI,CAACK,UAAU,CAAC,CAAC;IACrB;IACA;IACA;IACA,IAAI,CAACc,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;EAC7B;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,UAAUA,CAACC,iBAAiB,EAAE;IAC9B,IAAIA,iBAAiB,IAAI,IAAI,EAAE;MAC3B,IAAI,CAACV,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACN,0BAA0B,CAAC,IAAI,CAAC;IACzC,CAAC,MACI;MACD,IAAIpD,SAAS,CAACoE,iBAAiB,CAAC,EAAE;QAC9B,IAAI,CAACV,eAAe,GAAGU,iBAAiB;MAC5C,CAAC,MACI;QACD,IAAI,CAACV,eAAe,GAAGW,KAAK,CAACC,OAAO,CAACF,iBAAiB,CAAC,GACjDA,iBAAiB,GACjB,CAACA,iBAAiB,CAAC;MAC7B;MACA,IAAI,CAAChB,0BAA0B,CAAC,GAAG,CAAC;IACxC;EACJ;EACA;EACAmB,OAAOA,CAACC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;IAChD,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,KAAK,IAAI,EAAE;MAClB,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAAC/B,eAAe,EAAE;MACtB,IAAI0B,MAAM,KAAK,CAAC,IAAIC,OAAO,IAAIC,QAAQ,IAAIC,MAAM,IAAIC,OAAO,EAAE;QAC1D,OAAO,IAAI;MACf;MACA,IAAI,OAAO,IAAI,CAACE,MAAM,KAAK,QAAQ,IAAI,IAAI,CAACA,MAAM,IAAI,OAAO,EAAE;QAC3D,OAAO,IAAI;MACf;IACJ;IACA,MAAMC,MAAM,GAAG;MACXC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;IACD,IAAI,CAAChD,MAAM,CAACiD,aAAa,CAACP,OAAO,EAAEE,MAAM,CAAC;IAC1C;IACA;IACA;IACA,OAAO,CAAC,IAAI,CAACjC,eAAe;EAChC;EACA;EACAuC,WAAWA,CAAA,EAAG;IAAA,IAAAC,kBAAA;IACV,CAAAA,kBAAA,OAAI,CAACvC,YAAY,cAAAuC,kBAAA,eAAjBA,kBAAA,CAAmBC,WAAW,CAAC,CAAC;EACpC;EACApC,UAAUA,CAAA,EAAG;IAAA,IAAAqC,qBAAA;IACT,MAAMX,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACY,IAAI,GACLZ,OAAO,KAAK,IAAI,IAAI,IAAI,CAACrC,gBAAgB,IAAAgD,qBAAA,GACnC,IAAI,CAAChD,gBAAgB,cAAAgD,qBAAA,uBAArBA,qBAAA,CAAuBE,kBAAkB,CAAC,IAAI,CAACvD,MAAM,CAACwD,YAAY,CAACd,OAAO,CAAC,CAAC,GAC5E,IAAI;IACd,MAAMe,cAAc,GAAG,IAAI,CAACH,IAAI,KAAK,IAAI,GACnC,IAAI;IACJ;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACArH,0BAA0B,CAAC,IAAI,CAACqH,IAAI,EAAE,IAAI,CAAClD,EAAE,CAACK,aAAa,CAACD,OAAO,CAACE,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC;IAClG,IAAI,CAACS,mBAAmB,CAAC,MAAM,EAAEsC,cAAc,CAAC;EACpD;EACAtC,mBAAmBA,CAACuC,QAAQ,EAAEC,SAAS,EAAE;IACrC,MAAMxD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMM,aAAa,GAAG,IAAI,CAACL,EAAE,CAACK,aAAa;IAC3C,IAAIkD,SAAS,KAAK,IAAI,EAAE;MACpBxD,QAAQ,CAACyD,YAAY,CAACnD,aAAa,EAAEiD,QAAQ,EAAEC,SAAS,CAAC;IAC7D,CAAC,MACI;MACDxD,QAAQ,CAAC0D,eAAe,CAACpD,aAAa,EAAEiD,QAAQ,CAAC;IACrD;EACJ;EACA,IAAIhB,OAAOA,CAAA,EAAG;IACV,IAAI,IAAI,CAACnB,eAAe,KAAK,IAAI,EAAE;MAC/B,OAAO,IAAI;IACf,CAAC,MACI,IAAI1D,SAAS,CAAC,IAAI,CAAC0D,eAAe,CAAC,EAAE;MACtC,OAAO,IAAI,CAACA,eAAe;IAC/B;IACA,OAAO,IAAI,CAACvB,MAAM,CAAC8D,aAAa,CAAC,IAAI,CAACvC,eAAe,EAAE;MACnD;MACA;MACAM,UAAU,EAAE,IAAI,CAACA,UAAU,KAAKJ,SAAS,GAAG,IAAI,CAACI,UAAU,GAAG,IAAI,CAAC5B,KAAK;MACxEyB,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBG,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,gBAAgB,EAAE,IAAI,CAACA;IAC3B,CAAC,CAAC;EACN;AAGJ;AAACmC,WAAA,GApPKjE,UAAU;AAAAS,eAAA,CAAVT,UAAU,wBAAAkE,oBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAkPwFnE,WAAU,EAGhCjE,EAAE,CAAAqI,iBAAA,CAHgDpG,MAAM,GAGxDjC,EAAE,CAAAqI,iBAAA,CAHmEnG,cAAc,GAGnFlC,EAAE,CAAAsI,iBAAA,CAH8F,UAAU,GAG1GtI,EAAE,CAAAqI,iBAAA,CAHsIrI,EAAE,CAACuI,SAAS,GAGpJvI,EAAE,CAAAqI,iBAAA,CAH+JrI,EAAE,CAACwI,UAAU,GAG9KxI,EAAE,CAAAqI,iBAAA,CAHyL5I,EAAE,CAACG,gBAAgB;AAAA;AAAA8E,eAAA,CAlP1RT,UAAU,8BAqPkEjE,EAAE,CAAAyI,iBAAA;EAAAC,IAAA,EAFQzE,WAAU;EAAA0E,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAEpB/I,EAAE,CAAAiJ,UAAA,mBAAAC,qCAAAC,MAAA;QAAA,OAFQH,GAAA,CAAAzC,OAAA,CAAA4C,MAAA,CAAA3C,MAAA,EAAA2C,MAAA,CAAA1C,OAAA,EAAA0C,MAAA,CAAAzC,QAAA,EAAAyC,MAAA,CAAAxC,MAAA,EAAAwC,MAAA,CAAAvC,OAAiF,CAAC;MAAA,CAAzE,CAAC;IAAA;IAAA,IAAAmC,EAAA;MAEpB/I,EAAE,CAAAoJ,WAAA,WAAAJ,GAAA,CAAAlC,MAAA;IAAA;EAAA;EAAAuC,MAAA;IAAAvC,MAAA;IAAAjB,WAAA;IAAAF,QAAA;IAAAG,mBAAA;IAAAoB,KAAA;IAAAC,IAAA;IAAAnB,UAAA;IAAAD,gBAAA,8CAF+S1F,gBAAgB;IAAA2G,kBAAA,kDAAoE3G,gBAAgB;IAAA4G,UAAA,kCAA4C5G,gBAAgB;IAAA8F,UAAA;EAAA;EAAAmD,QAAA,GAEjdtJ,EAAE,CAAAuJ,oBAAA;AAAA;AAApF;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KAAkFzF,EAAE,CAAAwJ,iBAAA,CAAQvF,UAAU,EAAc,CAAC;IACzGyE,IAAI,EAAEhI,SAAS;IACf+I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhB,IAAI,EAAEzG;EAAO,CAAC,EAAE;IAAEyG,IAAI,EAAExG;EAAe,CAAC,EAAE;IAAEwG,IAAI,EAAE9C,SAAS;IAAE+D,UAAU,EAAE,CAAC;MAC3FjB,IAAI,EAAEjI,SAAS;MACfgJ,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAEf,IAAI,EAAE1I,EAAE,CAACuI;EAAU,CAAC,EAAE;IAAEG,IAAI,EAAE1I,EAAE,CAACwI;EAAW,CAAC,EAAE;IAAEE,IAAI,EAAEjJ,EAAE,CAACG;EAAiB,CAAC,CAAC,EAAkB;IAAEkH,MAAM,EAAE,CAAC;MAClH4B,IAAI,EAAElI,WAAW;MACjBiJ,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,EAAE;MACCf,IAAI,EAAEnI;IACV,CAAC,CAAC;IAAEsF,WAAW,EAAE,CAAC;MACd6C,IAAI,EAAEnI;IACV,CAAC,CAAC;IAAEoF,QAAQ,EAAE,CAAC;MACX+C,IAAI,EAAEnI;IACV,CAAC,CAAC;IAAEuF,mBAAmB,EAAE,CAAC;MACtB4C,IAAI,EAAEnI;IACV,CAAC,CAAC;IAAE2G,KAAK,EAAE,CAAC;MACRwB,IAAI,EAAEnI;IACV,CAAC,CAAC;IAAE4G,IAAI,EAAE,CAAC;MACPuB,IAAI,EAAEnI;IACV,CAAC,CAAC;IAAEyF,UAAU,EAAE,CAAC;MACb0C,IAAI,EAAEnI;IACV,CAAC,CAAC;IAAEwF,gBAAgB,EAAE,CAAC;MACnB2C,IAAI,EAAEnI,KAAK;MACXkJ,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAEvJ;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2G,kBAAkB,EAAE,CAAC;MACrB0B,IAAI,EAAEnI,KAAK;MACXkJ,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAEvJ;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4G,UAAU,EAAE,CAAC;MACbyB,IAAI,EAAEnI,KAAK;MACXkJ,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAEvJ;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8F,UAAU,EAAE,CAAC;MACbuC,IAAI,EAAEnI;IACV,CAAC,CAAC;IAAEgG,OAAO,EAAE,CAAC;MACVmC,IAAI,EAAEpI,YAAY;MAClBmJ,IAAI,EAAE,CAAC,OAAO,EAAE,CACR,eAAe,EACf,gBAAgB,EAChB,iBAAiB,EACjB,eAAe,EACf,gBAAgB,CACnB;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,gBAAgB,CAAC;EAWnB,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;;EA2BI7F,WAAWA,CAACC,MAAM,EAAE6F,OAAO,EAAE1F,QAAQ,EAAE2F,GAAG,EAAEC,IAAI,EAAE;IAAAxF,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,kBAxCxC,EAAE;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,oBAGA,KAAK;IAAAA,eAAA,kCAWS;MAAEyF,KAAK,EAAE;IAAM,CAAC;IAC1C;AACJ;AACA;AACA;AACA;AACA;AACA;IANIzF,eAAA;IAQA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAfIA,eAAA,yBAgBiB,IAAI/D,YAAY,CAAC,CAAC;IAE/B,IAAI,CAACwD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC6F,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC1F,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC2F,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,wBAAwB,GAAGjG,MAAM,CAACa,MAAM,CAACC,SAAS,CAAEC,CAAC,IAAK;MAC3D,IAAIA,CAAC,YAAYnD,aAAa,EAAE;QAC5B,IAAI,CAACsI,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB;IACA5G,EAAE,CAAC,IAAI,CAAC6G,KAAK,CAAC/E,OAAO,EAAE9B,EAAE,CAAC,IAAI,CAAC,CAAC,CAC3B8G,IAAI,CAAC5G,QAAQ,CAAC,CAAC,CAAC,CAChBqB,SAAS,CAAEwF,CAAC,IAAK;MAClB,IAAI,CAACJ,MAAM,CAAC,CAAC;MACb,IAAI,CAACK,4BAA4B,CAAC,CAAC;IACvC,CAAC,CAAC;EACN;EACAA,4BAA4BA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,OAAI,CAACC,4BAA4B,cAAAD,qBAAA,eAAjCA,qBAAA,CAAmCpD,WAAW,CAAC,CAAC;IAChD,MAAMsD,cAAc,GAAG,CAAC,GAAG,IAAI,CAACN,KAAK,CAACO,OAAO,CAAC,CAAC,EAAE,IAAI,CAACZ,IAAI,CAAC,CACtDpG,MAAM,CAAEoG,IAAI,IAAK,CAAC,CAACA,IAAI,CAAC,CACxBa,GAAG,CAAEb,IAAI,IAAKA,IAAI,CAACjE,SAAS,CAAC;IAClC,IAAI,CAAC2E,4BAA4B,GAAGjH,IAAI,CAACkH,cAAc,CAAC,CACnDL,IAAI,CAAC5G,QAAQ,CAAC,CAAC,CAAC,CAChBqB,SAAS,CAAEiF,IAAI,IAAK;MACrB,IAAI,IAAI,CAACH,SAAS,KAAK,IAAI,CAACiB,YAAY,CAAC,IAAI,CAAC7G,MAAM,CAAC,CAAC+F,IAAI,CAAC,EAAE;QACzD,IAAI,CAACG,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA,IAAIY,gBAAgBA,CAACC,IAAI,EAAE;IACvB,MAAMC,OAAO,GAAG9E,KAAK,CAACC,OAAO,CAAC4E,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;IAC5D,IAAI,CAACD,OAAO,GAAGA,OAAO,CAACrH,MAAM,CAAEuH,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;EAC7C;EACA;EACA9F,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAAC6E,MAAM,CAAC,CAAC;EACjB;EACA;EACAhD,WAAWA,CAAA,EAAG;IAAA,IAAAiE,sBAAA;IACV,IAAI,CAAClB,wBAAwB,CAAC7C,WAAW,CAAC,CAAC;IAC3C,CAAA+D,sBAAA,OAAI,CAACV,4BAA4B,cAAAU,sBAAA,eAAjCA,sBAAA,CAAmC/D,WAAW,CAAC,CAAC;EACpD;EACA8C,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACE,KAAK,IAAI,CAAC,IAAI,CAACpG,MAAM,CAACoH,SAAS,EACrC;IACJC,cAAc,CAAC,MAAM;MACjB,MAAMC,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;MAC5C,IAAI,CAACN,OAAO,CAACO,OAAO,CAAEL,CAAC,IAAK;QACxB,IAAII,cAAc,EAAE;UAChB,IAAI,CAACnH,QAAQ,CAACqH,QAAQ,CAAC,IAAI,CAAC3B,OAAO,CAACpF,aAAa,EAAEyG,CAAC,CAAC;QACzD,CAAC,MACI;UACD,IAAI,CAAC/G,QAAQ,CAACsH,WAAW,CAAC,IAAI,CAAC5B,OAAO,CAACpF,aAAa,EAAEyG,CAAC,CAAC;QAC5D;MACJ,CAAC,CAAC;MACF,IAAII,cAAc,IAAI,IAAI,CAACI,qBAAqB,KAAKjG,SAAS,EAAE;QAC5D,IAAI,CAACtB,QAAQ,CAACyD,YAAY,CAAC,IAAI,CAACiC,OAAO,CAACpF,aAAa,EAAE,cAAc,EAAE,IAAI,CAACiH,qBAAqB,CAACC,QAAQ,CAAC,CAAC,CAAC;MACjH,CAAC,MACI;QACD,IAAI,CAACxH,QAAQ,CAAC0D,eAAe,CAAC,IAAI,CAACgC,OAAO,CAACpF,aAAa,EAAE,cAAc,CAAC;MAC7E;MACA;MACA,IAAI,IAAI,CAACmF,SAAS,KAAK0B,cAAc,EAAE;QACnC,IAAI,CAAC1B,SAAS,GAAG0B,cAAc;QAC/B,IAAI,CAACxB,GAAG,CAAC8B,YAAY,CAAC,CAAC;QACvB;QACA,IAAI,CAACC,cAAc,CAACC,IAAI,CAACR,cAAc,CAAC;MAC5C;IACJ,CAAC,CAAC;EACN;EACAT,YAAYA,CAAC7G,MAAM,EAAE;IACjB,MAAM+H,OAAO,GAAGC,oBAAoB,CAAC,IAAI,CAACC,uBAAuB,CAAC,GAC5D,IAAI,CAACA,uBAAuB;IAC5B;IACE,IAAI,CAACA,uBAAuB,CAACjC,KAAK,IAAI,KAAK;IACnD,OAAQD,IAAI,IAAK;MACb,MAAMrD,OAAO,GAAGqD,IAAI,CAACrD,OAAO;MAC5B,OAAOA,OAAO,GAAG1C,MAAM,CAAC2F,QAAQ,CAACjD,OAAO,EAAEqF,OAAO,CAAC,GAAG,KAAK;IAC9D,CAAC;EACL;EACAT,cAAcA,CAAA,EAAG;IACb,MAAMY,eAAe,GAAG,IAAI,CAACrB,YAAY,CAAC,IAAI,CAAC7G,MAAM,CAAC;IACtD,OAAQ,IAAI,CAAC+F,IAAI,IAAImC,eAAe,CAAC,IAAI,CAACnC,IAAI,CAAC,IAAK,IAAI,CAACK,KAAK,CAAC+B,IAAI,CAACD,eAAe,CAAC;EACxF;AAGJ;AAACE,iBAAA,GA3IK1C,gBAAgB;AAAAnF,eAAA,CAAhBmF,gBAAgB,wBAAA2C,0BAAApE,iBAAA;EAAA,YAAAA,iBAAA,IAyIkFyB,iBAAgB,EA7PtC7J,EAAE,CAAAqI,iBAAA,CA6PsDpG,MAAM,GA7P9DjC,EAAE,CAAAqI,iBAAA,CA6PyErI,EAAE,CAACwI,UAAU,GA7PxFxI,EAAE,CAAAqI,iBAAA,CA6PmGrI,EAAE,CAACuI,SAAS,GA7PjHvI,EAAE,CAAAqI,iBAAA,CA6P4HrI,EAAE,CAACyM,iBAAiB,GA7PlJzM,EAAE,CAAAqI,iBAAA,CA6P6JpE,UAAU;AAAA;AAAAS,eAAA,CAzIrPmF,gBAAgB,8BApH4D7J,EAAE,CAAAyI,iBAAA;EAAAC,IAAA,EA8PQmB,iBAAgB;EAAAlB,SAAA;EAAA+D,cAAA,WAAAC,iCAAA5D,EAAA,EAAAC,GAAA,EAAA4D,QAAA;IAAA,IAAA7D,EAAA;MA9P1B/I,EAAE,CAAA6M,cAAA,CAAAD,QAAA,EA8PiU3I,UAAU;IAAA;IAAA,IAAA8E,EAAA;MAAA,IAAA+D,EAAA;MA9P7U9M,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAhE,GAAA,CAAAuB,KAAA,GAAAuC,EAAA;IAAA;EAAA;EAAAzD,MAAA;IAAA+C,uBAAA;IAAAP,qBAAA;IAAAZ,gBAAA;EAAA;EAAAgC,OAAA;IAAAjB,cAAA;EAAA;EAAAkB,QAAA;EAAA5D,QAAA,GAAFtJ,EAAE,CAAAuJ,oBAAA;AAAA;AAgQpF;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KAhQkFzF,EAAE,CAAAwJ,iBAAA,CAgQQK,gBAAgB,EAAc,CAAC;IAC/GnB,IAAI,EAAEhI,SAAS;IACf+I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BwD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAExE,IAAI,EAAEzG;EAAO,CAAC,EAAE;IAAEyG,IAAI,EAAE1I,EAAE,CAACwI;EAAW,CAAC,EAAE;IAAEE,IAAI,EAAE1I,EAAE,CAACuI;EAAU,CAAC,EAAE;IAAEG,IAAI,EAAE1I,EAAE,CAACyM;EAAkB,CAAC,EAAE;IAAE/D,IAAI,EAAEzE,UAAU;IAAE0F,UAAU,EAAE,CAAC;MACnJjB,IAAI,EAAE5H;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEyJ,KAAK,EAAE,CAAC;MACjC7B,IAAI,EAAE7H,eAAe;MACrB4I,IAAI,EAAE,CAACxF,UAAU,EAAE;QAAEkJ,WAAW,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAEf,uBAAuB,EAAE,CAAC;MAC1B1D,IAAI,EAAEnI;IACV,CAAC,CAAC;IAAEsL,qBAAqB,EAAE,CAAC;MACxBnD,IAAI,EAAEnI;IACV,CAAC,CAAC;IAAEyL,cAAc,EAAE,CAAC;MACjBtD,IAAI,EAAE9H;IACV,CAAC,CAAC;IAAEqK,gBAAgB,EAAE,CAAC;MACnBvC,IAAI,EAAEnI;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,SAAS4L,oBAAoBA,CAACD,OAAO,EAAE;EACnC,OAAO,CAAC,CAACA,OAAO,CAACkB,KAAK;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpBC,OAAOA,CAACnJ,KAAK,EAAEoJ,EAAE,EAAE;IACf,OAAOA,EAAE,CAAC,CAAC,CAAChD,IAAI,CAAC3G,UAAU,CAAC,MAAMH,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD;AAGJ;AAAC+J,kBAAA,GANKH,iBAAiB;AAAA5I,eAAA,CAAjB4I,iBAAiB,wBAAAI,2BAAAtF,iBAAA;EAAA,YAAAA,iBAAA,IAIiFkF,kBAAiB;AAAA;AAAA5I,eAAA,CAJnH4I,iBAAiB,+BA/S2DtN,EAAE,CAAA2N,kBAAA;EAAAC,KAAA,EAoTwBN,kBAAiB;EAAAO,OAAA,EAAjBP,kBAAiB,CAAAQ,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEjJ;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KAtTkFzF,EAAE,CAAAwJ,iBAAA,CAsTQ8D,iBAAiB,EAAc,CAAC;IAChH5E,IAAI,EAAE1H,UAAU;IAChByI,IAAI,EAAE,CAAC;MAAEsE,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACfT,OAAOA,CAACnJ,KAAK,EAAEoJ,EAAE,EAAE;IACf,OAAO9J,EAAE,CAAC,IAAI,CAAC;EACnB;AAGJ;AAACuK,aAAA,GANKD,YAAY;AAAAtJ,eAAA,CAAZsJ,YAAY,wBAAAE,sBAAA9F,iBAAA;EAAA,YAAAA,iBAAA,IAIsF4F,aAAY;AAAA;AAAAtJ,eAAA,CAJ9GsJ,YAAY,+BAnUgEhO,EAAE,CAAA2N,kBAAA;EAAAC,KAAA,EAwUwBI,aAAY;EAAAH,OAAA,EAAZG,aAAY,CAAAF,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE5I;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KA1UkFzF,EAAE,CAAAwJ,iBAAA,CA0UQwE,YAAY,EAAc,CAAC;IAC3GtF,IAAI,EAAE1H,UAAU;IAChByI,IAAI,EAAE,CAAC;MAAEsE,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,eAAe,CAAC;EAMlBjK,WAAWA,CAACC,MAAM,EAAEiK,QAAQ,EAAEC,kBAAkB,EAAEC,MAAM,EAAE;IAAA5J,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACtD,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACiK,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACxJ,YAAY,GAAG,IAAI,CAACZ,MAAM,CAACa,MAAM,CACjCwF,IAAI,CAAC1G,MAAM,CAAE0K,CAAC,IAAKA,CAAC,YAAYzM,aAAa,CAAC,EAAEgC,SAAS,CAAC,MAAM,IAAI,CAACwJ,OAAO,CAAC,CAAC,CAAC,CAAC,CAChFtI,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;EAC7B;EACAsI,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACkB,aAAa,CAAC,IAAI,CAACL,QAAQ,EAAE,IAAI,CAACjK,MAAM,CAACuK,MAAM,CAAC;EAChE;EACA;EACArH,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACtC,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACwC,WAAW,CAAC,CAAC;IACnC;EACJ;EACAkH,aAAaA,CAACL,QAAQ,EAAEO,MAAM,EAAE;IAC5B,MAAMC,GAAG,GAAG,EAAE;IACd,KAAK,MAAMxK,KAAK,IAAIuK,MAAM,EAAE;MAAA,IAAAE,gBAAA,EAAAC,qBAAA;MACxB,IAAI1K,KAAK,CAAC2K,SAAS,IAAI,CAAC3K,KAAK,CAAC4K,SAAS,EAAE;QACrC5K,KAAK,CAAC4K,SAAS,GAAGjO,yBAAyB,CAACqD,KAAK,CAAC2K,SAAS,EAAEX,QAAQ,EAAE,UAAUhK,KAAK,CAAC6K,IAAI,EAAE,CAAC;MAClG;MACA,MAAMC,uBAAuB,IAAAL,gBAAA,GAAGzK,KAAK,CAAC4K,SAAS,cAAAH,gBAAA,cAAAA,gBAAA,GAAIT,QAAQ;MAC3D,MAAMe,mBAAmB,IAAAL,qBAAA,GAAG1K,KAAK,CAACgL,eAAe,cAAAN,qBAAA,cAAAA,qBAAA,GAAII,uBAAuB;MAC5E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAK9K,KAAK,CAACiL,YAAY,IAAI,CAACjL,KAAK,CAACkL,aAAa,IAAIlL,KAAK,CAACmL,OAAO,KAAK3J,SAAS,IACzExB,KAAK,CAACoL,aAAa,IAAI,CAACpL,KAAK,CAACqL,gBAAiB,EAAE;QAClDb,GAAG,CAACc,IAAI,CAAC,IAAI,CAACC,aAAa,CAACT,uBAAuB,EAAE9K,KAAK,CAAC,CAAC;MAChE;MACA,IAAIA,KAAK,CAACwL,QAAQ,IAAIxL,KAAK,CAACkL,aAAa,EAAE;QAAA,IAAAO,eAAA;QACvCjB,GAAG,CAACc,IAAI,CAAC,IAAI,CAACjB,aAAa,CAACU,mBAAmB,GAAAU,eAAA,GAAGzL,KAAK,CAACwL,QAAQ,cAAAC,eAAA,cAAAA,eAAA,GAAIzL,KAAK,CAACkL,aAAc,CAAC,CAAC;MAC9F;IACJ;IACA,OAAO3L,IAAI,CAACiL,GAAG,CAAC,CAACpE,IAAI,CAAC5G,QAAQ,CAAC,CAAC,CAAC;EACrC;EACA+L,aAAaA,CAACvB,QAAQ,EAAEhK,KAAK,EAAE;IAC3B,OAAO,IAAI,CAACiK,kBAAkB,CAACd,OAAO,CAACnJ,KAAK,EAAE,MAAM;MAChD,IAAI0L,eAAe;MACnB,IAAI1L,KAAK,CAACiL,YAAY,IAAIjL,KAAK,CAACmL,OAAO,KAAK3J,SAAS,EAAE;QACnDkK,eAAe,GAAG,IAAI,CAACxB,MAAM,CAACe,YAAY,CAACjB,QAAQ,EAAEhK,KAAK,CAAC;MAC/D,CAAC,MACI;QACD0L,eAAe,GAAGpM,EAAE,CAAC,IAAI,CAAC;MAC9B;MACA,MAAMqM,sBAAsB,GAAGD,eAAe,CAACtF,IAAI,CAACxG,QAAQ,CAAE0K,MAAM,IAAK;QAAA,IAAAsB,gBAAA;QACrE,IAAItB,MAAM,KAAK,IAAI,EAAE;UACjB,OAAOhL,EAAE,CAAC,KAAK,CAAC,CAAC;QACrB;QACAU,KAAK,CAACkL,aAAa,GAAGZ,MAAM,CAACC,MAAM;QACnCvK,KAAK,CAACgL,eAAe,GAAGV,MAAM,CAACN,QAAQ;QACvC;QACA;QACA,OAAO,IAAI,CAACK,aAAa,EAAAuB,gBAAA,GAACtB,MAAM,CAACN,QAAQ,cAAA4B,gBAAA,cAAAA,gBAAA,GAAI5B,QAAQ,EAAEM,MAAM,CAACC,MAAM,CAAC;MACzE,CAAC,CAAC,CAAC;MACH,IAAIvK,KAAK,CAACoL,aAAa,IAAI,CAACpL,KAAK,CAACqL,gBAAgB,EAAE;QAChD,MAAMQ,cAAc,GAAG,IAAI,CAAC3B,MAAM,CAACkB,aAAa,CAACpL,KAAK,CAAC;QACvD,OAAOT,IAAI,CAAC,CAACoM,sBAAsB,EAAEE,cAAc,CAAC,CAAC,CAACzF,IAAI,CAAC5G,QAAQ,CAAC,CAAC,CAAC;MAC1E,CAAC,MACI;QACD,OAAOmM,sBAAsB;MACjC;IACJ,CAAC,CAAC;EACN;AAGJ;AAACG,gBAAA,GAlFK/B,eAAe;AAAAzJ,eAAA,CAAfyJ,eAAe,wBAAAgC,yBAAA/H,iBAAA;EAAA,YAAAA,iBAAA,IAgFmF+F,gBAAe,EA1arCnO,EAAE,CAAAoQ,QAAA,CA0aqDnO,MAAM,GA1a7DjC,EAAE,CAAAoQ,QAAA,CA0awEpQ,EAAE,CAACqQ,mBAAmB,GA1ahGrQ,EAAE,CAAAoQ,QAAA,CA0a2G/C,kBAAkB,GA1a/HrN,EAAE,CAAAoQ,QAAA,CA0a0IjO,kBAAkB;AAAA;AAAAuC,eAAA,CAhF1OyJ,eAAe,+BA1V6DnO,EAAE,CAAA2N,kBAAA;EAAAC,KAAA,EA2awBO,gBAAe;EAAAN,OAAA,EAAfM,gBAAe,CAAAL,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE/I;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KA7akFzF,EAAE,CAAAwJ,iBAAA,CA6aQ2E,eAAe,EAAc,CAAC;IAC9GzF,IAAI,EAAE1H,UAAU;IAChByI,IAAI,EAAE,CAAC;MAAEsE,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAErF,IAAI,EAAEzG;EAAO,CAAC,EAAE;IAAEyG,IAAI,EAAE1I,EAAE,CAACqQ;EAAoB,CAAC,EAAE;IAAE3H,IAAI,EAAE2E;EAAmB,CAAC,EAAE;IAAE3E,IAAI,EAAEvG;EAAmB,CAAC,CAAC;AAAA;AAElJ,MAAMmO,eAAe,GAAG,IAAIrP,cAAc,CAAC,EAAE,CAAC;AAC9C,MAAMsP,cAAc,CAAC;EAYjB;EACArM,WAAWA,CAACsM,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,IAAI,EAAEzE,OAAO,GAAG,CAAC,CAAC,EAAE;IAAAxH,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,iBALrE,CAAC;IAAAA,eAAA,qBACG,YAAY;IAAAA,eAAA,qBACZ,CAAC;IAAAA,eAAA,gBACN,CAAC,CAAC;IAGN,IAAI,CAAC8L,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACzE,OAAO,GAAGA,OAAO;IACtB;IACAA,OAAO,CAAC0E,yBAAyB,KAAjC1E,OAAO,CAAC0E,yBAAyB,GAAK,UAAU;IAChD1E,OAAO,CAAC2E,eAAe,KAAvB3E,OAAO,CAAC2E,eAAe,GAAK,UAAU;EAC1C;EACAC,IAAIA,CAAA,EAAG;IACH;IACA;IACA;IACA,IAAI,IAAI,CAAC5E,OAAO,CAAC0E,yBAAyB,KAAK,UAAU,EAAE;MACvD,IAAI,CAACF,gBAAgB,CAACK,2BAA2B,CAAC,QAAQ,CAAC;IAC/D;IACA,IAAI,CAAC3G,wBAAwB,GAAG,IAAI,CAAC4G,kBAAkB,CAAC,CAAC;IACzD,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9D;EACAF,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACP,WAAW,CAACzL,MAAM,CAACC,SAAS,CAAEuJ,CAAC,IAAK;MAC5C,IAAIA,CAAC,YAAYpM,eAAe,EAAE;QAC9B;QACA,IAAI,CAAC+O,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,GAAG,IAAI,CAACV,gBAAgB,CAACW,iBAAiB,CAAC,CAAC;QACnE,IAAI,CAACC,UAAU,GAAG9C,CAAC,CAAC+C,iBAAiB;QACrC,IAAI,CAACC,UAAU,GAAGhD,CAAC,CAACiD,aAAa,GAAGjD,CAAC,CAACiD,aAAa,CAACC,YAAY,GAAG,CAAC;MACxE,CAAC,MACI,IAAIlD,CAAC,YAAYzM,aAAa,EAAE;QACjC,IAAI,CAACqP,MAAM,GAAG5C,CAAC,CAACmD,EAAE;QAClB,IAAI,CAACC,mBAAmB,CAACpD,CAAC,EAAE,IAAI,CAACgC,aAAa,CAACqB,KAAK,CAACrD,CAAC,CAACsD,iBAAiB,CAAC,CAACnM,QAAQ,CAAC;MACvF,CAAC,MACI,IAAI6I,CAAC,YAAYnM,iBAAiB,IACnCmM,CAAC,CAACuD,IAAI,KAAKzP,qBAAqB,CAAC0P,wBAAwB,EAAE;QAC3D,IAAI,CAACV,UAAU,GAAG1L,SAAS;QAC3B,IAAI,CAAC4L,UAAU,GAAG,CAAC;QACnB,IAAI,CAACI,mBAAmB,CAACpD,CAAC,EAAE,IAAI,CAACgC,aAAa,CAACqB,KAAK,CAACrD,CAAC,CAACyD,GAAG,CAAC,CAACtM,QAAQ,CAAC;MACzE;IACJ,CAAC,CAAC;EACN;EACAuL,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACT,WAAW,CAACzL,MAAM,CAACC,SAAS,CAAEuJ,CAAC,IAAK;MAC5C,IAAI,EAAEA,CAAC,YAAYjM,MAAM,CAAC,EACtB;MACJ;MACA,IAAIiM,CAAC,CAAC0D,QAAQ,EAAE;QACZ,IAAI,IAAI,CAAChG,OAAO,CAAC0E,yBAAyB,KAAK,KAAK,EAAE;UAClD,IAAI,CAACF,gBAAgB,CAACyB,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClD,CAAC,MACI,IAAI,IAAI,CAACjG,OAAO,CAAC0E,yBAAyB,KAAK,SAAS,EAAE;UAC3D,IAAI,CAACF,gBAAgB,CAACyB,gBAAgB,CAAC3D,CAAC,CAAC0D,QAAQ,CAAC;QACtD;QACA;MACJ,CAAC,MACI;QACD,IAAI1D,CAAC,CAAC4D,MAAM,IAAI,IAAI,CAAClG,OAAO,CAAC2E,eAAe,KAAK,SAAS,EAAE;UACxD,IAAI,CAACH,gBAAgB,CAAC2B,cAAc,CAAC7D,CAAC,CAAC4D,MAAM,CAAC;QAClD,CAAC,MACI,IAAI,IAAI,CAAClG,OAAO,CAAC0E,yBAAyB,KAAK,UAAU,EAAE;UAC5D,IAAI,CAACF,gBAAgB,CAACyB,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClD;MACJ;IACJ,CAAC,CAAC;EACN;EACAP,mBAAmBA,CAACU,WAAW,EAAEF,MAAM,EAAE;IACrC,IAAI,CAACzB,IAAI,CAAC4B,iBAAiB,CAAC,MAAM;MAC9B;MACA;MACA;MACAC,UAAU,CAAC,MAAM;QACb,IAAI,CAAC7B,IAAI,CAAC8B,GAAG,CAAC,MAAM;UAChB,IAAI,CAAChC,WAAW,CAACzL,MAAM,CAACkB,IAAI,CAAC,IAAI3D,MAAM,CAAC+P,WAAW,EAAE,IAAI,CAAChB,UAAU,KAAK,UAAU,GAAG,IAAI,CAACH,KAAK,CAAC,IAAI,CAACK,UAAU,CAAC,GAAG,IAAI,EAAEY,MAAM,CAAC,CAAC;QACtI,CAAC,CAAC;MACN,CAAC,EAAE,CAAC,CAAC;IACT,CAAC,CAAC;EACN;EACA;EACA/K,WAAWA,CAAA,EAAG;IAAA,IAAAqL,qBAAA,EAAAC,qBAAA;IACV,CAAAD,qBAAA,OAAI,CAACtI,wBAAwB,cAAAsI,qBAAA,eAA7BA,qBAAA,CAA+BnL,WAAW,CAAC,CAAC;IAC5C,CAAAoL,qBAAA,OAAI,CAAC1B,wBAAwB,cAAA0B,qBAAA,eAA7BA,qBAAA,CAA+BpL,WAAW,CAAC,CAAC;EAChD;AAGJ;AAACqL,eAAA,GAhGKrC,cAAc;AAAA7L,eAAA,CAAd6L,cAAc,wBAAAsC,wBAAAzK,iBAAA;EAnb8DpI,EAAE,CAAA8S,gBAAA;AAAA;AAAApO,eAAA,CAmb9E6L,cAAc,+BAnb8DvQ,EAAE,CAAA2N,kBAAA;EAAAC,KAAA,EAkhBwB2C,eAAc;EAAA1C,OAAA,EAAd0C,eAAc,CAAAzC;AAAA;AAE1H;EAAA,QAAArI,SAAA,oBAAAA,SAAA,KAphBkFzF,EAAE,CAAAwJ,iBAAA,CAohBQ+G,cAAc,EAAc,CAAC;IAC7G7H,IAAI,EAAE1H;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE0H,IAAI,EAAElG;EAAc,CAAC,EAAE;IAAEkG,IAAI,EAAEjG;EAAsB,CAAC,EAAE;IAAEiG,IAAI,EAAEjJ,EAAE,CAACI;EAAiB,CAAC,EAAE;IAAE6I,IAAI,EAAE1I,EAAE,CAAC4B;EAAO,CAAC,EAAE;IAAE8G,IAAI,EAAE9C;EAAU,CAAC,CAAC;AAAA;;AAErK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmN,aAAaA,CAACpE,MAAM,EAAE,GAAGrF,QAAQ,EAAE;EACxC,OAAOlI,wBAAwB,CAAC,CAC5B;IAAE4R,OAAO,EAAEtQ,MAAM;IAAEuQ,KAAK,EAAE,IAAI;IAAEC,QAAQ,EAAEvE;EAAO,CAAC,EAClD,OAAOlJ,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;IAAEuN,OAAO,EAAEG,kBAAkB;IAAED,QAAQ,EAAE;EAAK,CAAC,GAC/C,EAAE,EACR;IAAEF,OAAO,EAAE9Q,cAAc;IAAEkR,UAAU,EAAEC,SAAS;IAAEC,IAAI,EAAE,CAACrR,MAAM;EAAE,CAAC,EAClE;IAAE+Q,OAAO,EAAE3R,sBAAsB;IAAE4R,KAAK,EAAE,IAAI;IAAEG,UAAU,EAAEG;EAAqB,CAAC,EAClFjK,QAAQ,CAACyB,GAAG,CAAEyI,OAAO,IAAKA,OAAO,CAACC,UAAU,CAAC,CAChD,CAAC;AACN;AACA,SAASJ,SAASA,CAAClP,MAAM,EAAE;EACvB,OAAOA,MAAM,CAACuP,WAAW,CAACC,IAAI;AAClC;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,IAAI,EAAE9E,SAAS,EAAE;EACpC,OAAO;IAAE+E,KAAK,EAAED,IAAI;IAAEJ,UAAU,EAAE1E;EAAU,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,MAAMoE,kBAAkB,GAAG,IAAIlS,cAAc,CAAC,EAAE,EAAE;EAC9C8M,UAAU,EAAE,MAAM;EAClBF,OAAO,EAAEA,CAAA,KAAM;AACnB,CAAC,CAAC;AACF,MAAMkG,4BAA4B,GAAG;EACjCf,OAAO,EAAE1R,uBAAuB;EAChC2R,KAAK,EAAE,IAAI;EACXG,UAAUA,CAAA,EAAG;IACT,OAAO,MAAM;MACT,IAAI,CAAC5R,MAAM,CAAC2R,kBAAkB,CAAC,EAAE;QAC7Ba,OAAO,CAACC,IAAI,CAAC,gFAAgF,GACzF,2BAA2B,CAAC;MACpC;IACJ,CAAC;EACL;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACvF,MAAM,EAAE;EAC3B,OAAO,CACH;IAAEqE,OAAO,EAAEtQ,MAAM;IAAEuQ,KAAK,EAAE,IAAI;IAAEC,QAAQ,EAAEvE;EAAO,CAAC,EAClD,OAAOlJ,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAGsO,4BAA4B,GAAG,EAAE,CACpF;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,qBAAqBA,CAACjI,OAAO,GAAG,CAAC,CAAC,EAAE;EACzC,MAAM6C,SAAS,GAAG,CACd;IACIiE,OAAO,EAAE1C,eAAe;IACxB8C,UAAU,EAAEA,CAAA,KAAM;MACd,MAAM1C,gBAAgB,GAAGlP,MAAM,CAAC3B,gBAAgB,CAAC;MACjD,MAAM8Q,IAAI,GAAGnP,MAAM,CAACI,MAAM,CAAC;MAC3B,MAAM6O,WAAW,GAAGjP,MAAM,CAACiB,qBAAqB,CAAC;MACjD,MAAM+N,aAAa,GAAGhP,MAAM,CAACgB,aAAa,CAAC;MAC3C,OAAO,IAAI+N,cAAc,CAACC,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,IAAI,EAAEzE,OAAO,CAAC;IAC1F;EACJ,CAAC,CACJ;EACD,OAAO0H,aAAa,CAAC,CAAC,CAAC,kDAAkD7E,SAAS,CAAC;AACvF;AACA,SAASwE,oBAAoBA,CAAA,EAAG;EAC5B,MAAMnF,QAAQ,GAAG5M,MAAM,CAACC,QAAQ,CAAC;EACjC,OAAQ2S,wBAAwB,IAAK;IAAA,IAAAC,aAAA,EAAAC,cAAA;IACjC,MAAMC,GAAG,GAAGnG,QAAQ,CAACoG,GAAG,CAAC9S,cAAc,CAAC;IACxC,IAAI0S,wBAAwB,KAAKG,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MAChD;IACJ;IACA,MAAMtQ,MAAM,GAAGiK,QAAQ,CAACoG,GAAG,CAACvS,MAAM,CAAC;IACnC,MAAMyS,aAAa,GAAGtG,QAAQ,CAACoG,GAAG,CAACG,cAAc,CAAC;IAClD,IAAIvG,QAAQ,CAACoG,GAAG,CAACI,kBAAkB,CAAC,KAAK,CAAC,CAAC,4CAA4C;MACnFzQ,MAAM,CAAC0Q,iBAAiB,CAAC,CAAC;IAC9B;IACA,CAAAR,aAAA,GAAAjG,QAAQ,CAACoG,GAAG,CAACM,gBAAgB,EAAE,IAAI,EAAEnT,WAAW,CAACb,QAAQ,CAAC,cAAAuT,aAAA,eAA1DA,aAAA,CAA4D9F,eAAe,CAAC,CAAC;IAC7E,CAAA+F,cAAA,GAAAlG,QAAQ,CAACoG,GAAG,CAAClE,eAAe,EAAE,IAAI,EAAE3O,WAAW,CAACb,QAAQ,CAAC,cAAAwT,cAAA,eAAzDA,cAAA,CAA2DxD,IAAI,CAAC,CAAC;IACjE3M,MAAM,CAAC4Q,sBAAsB,CAACR,GAAG,CAACS,cAAc,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,CAACN,aAAa,CAACO,MAAM,EAAE;MACvBP,aAAa,CAACxO,IAAI,CAAC,CAAC;MACpBwO,aAAa,CAACQ,QAAQ,CAAC,CAAC;MACxBR,aAAa,CAACnN,WAAW,CAAC,CAAC;IAC/B;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoN,cAAc,GAAG,IAAI1T,cAAc,CAAC,OAAOwE,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,0BAA0B,GAAG,EAAE,EAAE;EACvHoI,OAAO,EAAEA,CAAA,KAAM;IACX,OAAO,IAAIpK,OAAO,CAAC,CAAC;EACxB;AACJ,CAAC,CAAC;AACF,MAAMmR,kBAAkB,GAAG,IAAI3T,cAAc,CAAC,OAAOwE,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,oBAAoB,GAAG,EAAE,EAAE;EAAEsI,UAAU,EAAE,MAAM;EAAEF,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAC;AAA2C,CAAC,CAAC;AAC7M;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsH,oCAAoCA,CAAA,EAAG;EAC5C,MAAMpG,SAAS,GAAG,CACd;IAAEiE,OAAO,EAAE4B,kBAAkB;IAAE1B,QAAQ,EAAE,CAAC,CAAC;EAAwC,CAAC,EACpF3R,qBAAqB,CAAC,MAAM;IACxB,MAAM6M,QAAQ,GAAG5M,MAAM,CAACC,QAAQ,CAAC;IACjC,MAAM2T,mBAAmB,GAAGhH,QAAQ,CAACoG,GAAG,CAAC9U,oBAAoB,EAAE2V,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;IACjF,OAAOF,mBAAmB,CAACG,IAAI,CAAC,MAAM;MAClC,OAAO,IAAIF,OAAO,CAAEC,OAAO,IAAK;QAC5B,MAAMnR,MAAM,GAAGiK,QAAQ,CAACoG,GAAG,CAACvS,MAAM,CAAC;QACnC,MAAMyS,aAAa,GAAGtG,QAAQ,CAACoG,GAAG,CAACG,cAAc,CAAC;QAClDhS,mBAAmB,CAACwB,MAAM,EAAE,MAAM;UAC9B;UACA;UACAmR,OAAO,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC;QACFlH,QAAQ,CAACoG,GAAG,CAAC/R,qBAAqB,CAAC,CAAC+S,kBAAkB,GAAG,MAAM;UAC3D;UACA;UACA;UACAF,OAAO,CAAC,IAAI,CAAC;UACb,OAAOZ,aAAa,CAACO,MAAM,GAAGvR,EAAE,CAAC,KAAK,CAAC,CAAC,GAAGgR,aAAa;QAC5D,CAAC;QACDvQ,MAAM,CAAC0Q,iBAAiB,CAAC,CAAC;MAC9B,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC,CAAC,CACL;EACD,OAAOjB,aAAa,CAAC,CAAC,CAAC,iEAAiE7E,SAAS,CAAC;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0G,6BAA6BA,CAAA,EAAG;EACrC,MAAM1G,SAAS,GAAG,CACdxN,qBAAqB,CAAC,MAAM;IACxBC,MAAM,CAACS,MAAM,CAAC,CAACyT,2BAA2B,CAAC,CAAC;EAChD,CAAC,CAAC,EACF;IAAE1C,OAAO,EAAE4B,kBAAkB;IAAE1B,QAAQ,EAAE,CAAC,CAAC;EAAiC,CAAC,CAChF;EACD,OAAOU,aAAa,CAAC,CAAC,CAAC,0DAA0D7E,SAAS,CAAC;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4G,gBAAgBA,CAAA,EAAG;EACxB,IAAI5G,SAAS,GAAG,EAAE;EAClB,IAAI,OAAOtJ,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;IAC/CsJ,SAAS,GAAG,CACR;MACIiE,OAAO,EAAE1R,uBAAuB;MAChC2R,KAAK,EAAE,IAAI;MACXG,UAAU,EAAEA,CAAA,KAAM;QACd,MAAMjP,MAAM,GAAG3C,MAAM,CAACS,MAAM,CAAC;QAC7B,OAAO,MAAMkC,MAAM,CAACa,MAAM,CAACC,SAAS,CAAEuJ,CAAC,IAAK;UAAA,IAAAoH,cAAA,EAAAC,QAAA,EAAAC,iBAAA,EAAAC,SAAA;UACxC;UACA,CAAAH,cAAA,IAAAC,QAAA,GAAA7B,OAAO,EAACgC,KAAK,cAAAJ,cAAA,eAAbA,cAAA,CAAAK,IAAA,CAAAJ,QAAA,EAAgB,iBAAiBrH,CAAC,CAACtK,WAAW,CAACgS,IAAI,EAAE,CAAC;UACtDlC,OAAO,CAACmC,GAAG,CAAChT,cAAc,CAACqL,CAAC,CAAC,CAAC;UAC9BwF,OAAO,CAACmC,GAAG,CAAC3H,CAAC,CAAC;UACd,CAAAsH,iBAAA,IAAAC,SAAA,GAAA/B,OAAO,EAACoC,QAAQ,cAAAN,iBAAA,eAAhBA,iBAAA,CAAAG,IAAA,CAAAF,SAAmB,CAAC;UACpB;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CACJ;EACL,CAAC,MACI;IACDhH,SAAS,GAAG,EAAE;EAClB;EACA,OAAO6E,aAAa,CAAC,CAAC,CAAC,6CAA6C7E,SAAS,CAAC;AAClF;AACA,MAAM+F,gBAAgB,GAAG,IAAI7T,cAAc,CAAC,OAAOwE,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,kBAAkB,GAAG,EAAE,CAAC;AACpH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4Q,cAAcA,CAAChI,kBAAkB,EAAE;EACxC,MAAMU,SAAS,GAAG,CACd;IAAEiE,OAAO,EAAE8B,gBAAgB;IAAEwB,WAAW,EAAEnI;EAAgB,CAAC,EAC3D;IAAE6E,OAAO,EAAE3F,kBAAkB;IAAEiJ,WAAW,EAAEjI;EAAmB,CAAC,CACnE;EACD,OAAOuF,aAAa,CAAC,CAAC,CAAC,2CAA2C7E,SAAS,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwH,gBAAgBA,CAACrK,OAAO,EAAE;EAC/B,MAAM6C,SAAS,GAAG,CAAC;IAAEiE,OAAO,EAAEpQ,oBAAoB;IAAEsQ,QAAQ,EAAEhH;EAAQ,CAAC,CAAC;EACxE,OAAO0H,aAAa,CAAC,CAAC,CAAC,oDAAoD7E,SAAS,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyH,gBAAgBA,CAAA,EAAG;EACxB,MAAMzH,SAAS,GAAG,CAAC;IAAEiE,OAAO,EAAEpT,gBAAgB;IAAE6W,QAAQ,EAAE9W;EAAqB,CAAC,CAAC;EACjF,OAAOiU,aAAa,CAAC,CAAC,CAAC,mDAAmD7E,SAAS,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2H,0BAA0BA,CAACC,OAAO,EAAE;EACzC,MAAM5H,SAAS,GAAG,CACd;IACIiE,OAAO,EAAEnQ,wBAAwB;IACjCqQ,QAAQ,EAAEyD;EACd,CAAC,CACJ;EACD,OAAO/C,aAAa,CAAC,CAAC,CAAC,uDAAuD7E,SAAS,CAAC;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6H,yBAAyBA,CAAA,EAAG;EACjC,MAAM7H,SAAS,GAAG,CACdjM,0BAA0B,EAC1B;IAAEkQ,OAAO,EAAEjQ,YAAY;IAAEuT,WAAW,EAAExT;EAA2B,CAAC,CACrE;EACD,OAAO8Q,aAAa,CAAC,CAAC,CAAC,sDAAsD7E,SAAS,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8H,mBAAmBA,CAAC3K,OAAO,EAAE;EAClC/K,uBAAuB,CAAC,yBAAyB,CAAC;EAClD,MAAM4N,SAAS,GAAG,CACd;IAAEiE,OAAO,EAAE/P,sBAAsB;IAAEiQ,QAAQ,EAAElQ;EAAqB,CAAC,EACnE;IACIgQ,OAAO,EAAE9P,uBAAuB;IAChCgQ,QAAQ,EAAA4D,aAAA;MAAIC,kBAAkB,EAAE,CAAC,EAAC7K,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE8K,qBAAqB;IAAA,GAAK9K,OAAO;EAChF,CAAC,CACJ;EACD,OAAO0H,aAAa,CAAC,CAAC,CAAC,gDAAgD7E,SAAS,CAAC;AACrF;;AAEA;AACA;AACA;AACA,MAAMkI,iBAAiB,GAAG,CAAC3T,YAAY,EAAEW,UAAU,EAAE4F,gBAAgB,EAAErG,qBAAqB,CAAC;AAC7F;AACA;AACA;AACA,MAAM0T,oBAAoB,GAAG,IAAIjW,cAAc,CAAC,OAAOwE,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,gCAAgC,GAAG,EAAE,CAAC;AACtI;AACA;AACA;AACA;AACA,MAAM0R,gBAAgB,GAAG,CACrBrX,QAAQ,EACR;EAAEkT,OAAO,EAAExQ,aAAa;EAAEiU,QAAQ,EAAErT;AAAqB,CAAC,EAC1DnB,MAAM,EACNoB,sBAAsB,EACtB;EAAE2P,OAAO,EAAE9Q,cAAc;EAAEkR,UAAU,EAAEC,SAAS;EAAEC,IAAI,EAAE,CAACrR,MAAM;AAAE,CAAC,EAClEE,kBAAkB;AAClB;AACA;AACA,OAAOsD,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;EAAEuN,OAAO,EAAEG,kBAAkB;EAAED,QAAQ,EAAE;AAAK,CAAC,GAC/C,EAAE,CACX;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkE,YAAY,CAAC;EACflT,WAAWA,CAAA,EAAG;IACV,IAAI,OAAOuB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CjE,MAAM,CAAC0V,oBAAoB,EAAE;QAAEG,QAAQ,EAAE;MAAK,CAAC,CAAC;IACpD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,OAAOA,CAAC3I,MAAM,EAAED,MAAM,EAAE;IAC3B,OAAO;MACH6I,QAAQ,EAAEH,YAAY;MACtBrI,SAAS,EAAE,CACPoI,gBAAgB,EAChB,OAAO1R,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvCiJ,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE8I,aAAa,GACjB7B,gBAAgB,CAAC,CAAC,CAAClC,UAAU,GAC7B,EAAE,GACN,EAAE,EACR;QAAET,OAAO,EAAEtQ,MAAM;QAAEuQ,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAEvE;MAAO,CAAC,EAClD,OAAOlJ,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;QACEuN,OAAO,EAAEkE,oBAAoB;QAC7B9D,UAAU,EAAEqE,mBAAmB;QAC/BnE,IAAI,EAAE,CAAC,CAACrR,MAAM,EAAE,IAAInB,QAAQ,CAAC,CAAC,EAAE,IAAIe,QAAQ,CAAC,CAAC,CAAC;MACnD,CAAC,GACC,EAAE,EACR6M,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEgJ,YAAY,GACd;QACE1E,OAAO,EAAEnQ,wBAAwB;QACjCqQ,QAAQ,EAAExE,MAAM,CAACgJ;MACrB,CAAC,GACC,EAAE,EACR;QAAE1E,OAAO,EAAEpQ,oBAAoB;QAAEsQ,QAAQ,EAAExE,MAAM,GAAGA,MAAM,GAAG,CAAC;MAAE,CAAC,EACjEA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEiJ,OAAO,GAAGC,2BAA2B,CAAC,CAAC,GAAGC,2BAA2B,CAAC,CAAC,EAC/EC,qBAAqB,CAAC,CAAC,EACvBpJ,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEL,kBAAkB,GAAGgI,cAAc,CAAC3H,MAAM,CAACL,kBAAkB,CAAC,CAACoF,UAAU,GAAG,EAAE,EACtF/E,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEmG,iBAAiB,GAAGkD,wBAAwB,CAACrJ,MAAM,CAAC,GAAG,EAAE,EACjEA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEsJ,qBAAqB,GAAGpB,yBAAyB,CAAC,CAAC,CAACnD,UAAU,GAAG,EAAE,EAC3E/E,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEuJ,qBAAqB,GAAGpB,mBAAmB,CAAC,CAAC,CAACpD,UAAU,GAAG,EAAE,EACrEyE,wBAAwB,CAAC,CAAC;IAElC,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,QAAQA,CAACxJ,MAAM,EAAE;IACpB,OAAO;MACH4I,QAAQ,EAAEH,YAAY;MACtBrI,SAAS,EAAE,CAAC;QAAEiE,OAAO,EAAEtQ,MAAM;QAAEuQ,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAEvE;MAAO,CAAC;IAClE,CAAC;EACL;AAIJ;AAACyJ,aAAA,GApFKhB,YAAY;AAAA1S,eAAA,CAAZ0S,YAAY,wBAAAiB,sBAAAjQ,iBAAA;EAAA,YAAAA,iBAAA,IAiFsFgP,aAAY;AAAA;AAAA1S,eAAA,CAjF9G0S,YAAY,8BA3lCgEpX,EAAE,CAAAsY,gBAAA;EAAA5P,IAAA,EA6qCqB0O,aAAY;EAAAmB,OAAA,GAAYjV,YAAY,EAAEW,UAAU,EAAE4F,gBAAgB,EAAErG,qBAAqB;EAAAgV,OAAA,GAAalV,YAAY,EAAEW,UAAU,EAAE4F,gBAAgB,EAAErG,qBAAqB;AAAA;AAAAkB,eAAA,CAlF1Q0S,YAAY,8BA3lCgEpX,EAAE,CAAAyY,gBAAA;AAgrCpF;EAAA,QAAAhT,SAAA,oBAAAA,SAAA,KAhrCkFzF,EAAE,CAAAwJ,iBAAA,CAgrCQ4N,YAAY,EAAc,CAAC;IAC3G1O,IAAI,EAAE5G,QAAQ;IACd2H,IAAI,EAAE,CAAC;MACC8O,OAAO,EAAEtB,iBAAiB;MAC1BuB,OAAO,EAAEvB;IACb,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,SAASa,qBAAqBA,CAAA,EAAG;EAC7B,OAAO;IACH9E,OAAO,EAAE1C,eAAe;IACxB8C,UAAU,EAAEA,CAAA,KAAM;MACd,MAAM1C,gBAAgB,GAAGlP,MAAM,CAAC3B,gBAAgB,CAAC;MACjD,MAAM8Q,IAAI,GAAGnP,MAAM,CAACI,MAAM,CAAC;MAC3B,MAAM8M,MAAM,GAAGlN,MAAM,CAACoB,oBAAoB,CAAC;MAC3C,MAAM6N,WAAW,GAAGjP,MAAM,CAACiB,qBAAqB,CAAC;MACjD,MAAM+N,aAAa,GAAGhP,MAAM,CAACgB,aAAa,CAAC;MAC3C,IAAIkM,MAAM,CAACgK,YAAY,EAAE;QACrBhI,gBAAgB,CAACiI,SAAS,CAACjK,MAAM,CAACgK,YAAY,CAAC;MACnD;MACA,OAAO,IAAInI,cAAc,CAACC,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,IAAI,EAAEjC,MAAM,CAAC;IACzF;EACJ,CAAC;AACL;AACA;AACA;AACA,SAASkJ,2BAA2BA,CAAA,EAAG;EACnC,OAAO;IAAE5E,OAAO,EAAEpT,gBAAgB;IAAE6W,QAAQ,EAAE9W;EAAqB,CAAC;AACxE;AACA;AACA;AACA,SAASkY,2BAA2BA,CAAA,EAAG;EACnC,OAAO;IAAE7E,OAAO,EAAEpT,gBAAgB;IAAE6W,QAAQ,EAAE1W;EAAqB,CAAC;AACxE;AACA,SAAS0X,mBAAmBA,CAACtT,MAAM,EAAE;EACjC,IAAIA,MAAM,EAAE;IACR,MAAM,IAAIjE,aAAa,CAAC,IAAI,CAAC,8CAA8C,4GAA4G,GACnL,kEAAkE,CAAC;EAC3E;EACA,OAAO,SAAS;AACpB;AACA;AACA;AACA,SAAS6X,wBAAwBA,CAACrJ,MAAM,EAAE;EACtC,OAAO,CACHA,MAAM,CAACmG,iBAAiB,KAAK,UAAU,GAAGY,6BAA6B,CAAC,CAAC,CAAChC,UAAU,GAAG,EAAE,EACzF/E,MAAM,CAACmG,iBAAiB,KAAK,iBAAiB,GACxCM,oCAAoC,CAAC,CAAC,CAAC1B,UAAU,GACjD,EAAE,CACX;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmF,kBAAkB,GAAG,IAAI3X,cAAc,CAAC,OAAOwE,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,oBAAoB,GAAG,EAAE,CAAC;AACxH,SAASyS,wBAAwBA,CAAA,EAAG;EAChC,OAAO;EACH;EACA;EACA;IAAElF,OAAO,EAAE4F,kBAAkB;IAAExF,UAAU,EAAEG;EAAqB,CAAC,EACjE;IAAEP,OAAO,EAAE3R,sBAAsB;IAAE4R,KAAK,EAAE,IAAI;IAAEqD,WAAW,EAAEsC;EAAmB,CAAC,CACpF;AACL;AAEA,SAAS5K,YAAY,EAAEV,iBAAiB,EAAED,kBAAkB,EAAEuL,kBAAkB,EAAEzB,gBAAgB,EAAElT,UAAU,EAAE4F,gBAAgB,EAAEuN,YAAY,EAAEjJ,eAAe,EAAE4E,aAAa,EAAEmB,aAAa,EAAE0C,yBAAyB,EAAEjB,gBAAgB,EAAEF,6BAA6B,EAAEN,oCAAoC,EAAEqB,gBAAgB,EAAErC,qBAAqB,EAAEuC,0BAA0B,EAAEL,cAAc,EAAEE,gBAAgB,EAAEM,mBAAmB;AAC3a", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}