{"ast": null, "code": "import { DEFAULT_SETTINGS, SettingsService } from './settings.service';\nimport { TestBed } from '@angular/core/testing';\nimport * as moment from 'moment';\ndescribe('SwuiSettingsService', () => {\n  const test_settings = {\n    pageSize: 10,\n    currencyFormat: window.navigator.language,\n    dateFormat: 'DD.MM.YYYY',\n    timeFormat: 'HH:mm:ss',\n    timezoneName: moment.tz.guess()\n  };\n  let service;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      providers: [SettingsService]\n    });\n    service = TestBed.inject(SettingsService);\n  });\n  it('it should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  it('it should return default settings value', () => {\n    expect(service.resolve()).toBe(DEFAULT_SETTINGS);\n    expect(service.appSettings).toBe(DEFAULT_SETTINGS);\n  });\n  it('it should return new settings value from getter', () => {\n    service.use(test_settings);\n    expect(service.resolve()).toBe(test_settings);\n    expect(service.appSettings).toBe(test_settings);\n  });\n  it('it should return default settings value from observable', () => {\n    service.appSettings$.subscribe(value => expect(value).toBe(DEFAULT_SETTINGS));\n  });\n  it('it should return test settings value from observable', () => {\n    service.use(test_settings);\n    service.appSettings$.subscribe(value => expect(value).toBe(test_settings));\n  });\n});", "map": {"version": 3, "names": ["DEFAULT_SETTINGS", "SettingsService", "TestBed", "moment", "describe", "test_settings", "pageSize", "currencyFormat", "window", "navigator", "language", "dateFormat", "timeFormat", "timezoneName", "tz", "guess", "service", "beforeEach", "configureTestingModule", "providers", "inject", "it", "expect", "toBeTruthy", "resolve", "toBe", "appSettings", "use", "appSettings$", "subscribe", "value"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/settings/setting.service.spec.ts"], "sourcesContent": ["import { DEFAULT_SETTINGS, SettingsService } from './settings.service';\nimport { TestBed } from '@angular/core/testing';\nimport { AppSettings } from './app-settings';\nimport * as moment from 'moment';\n\ndescribe( 'SwuiSettingsService', () => {\n  const test_settings: AppSettings = {\n    pageSize: 10,\n    currencyFormat: window.navigator.language,\n    dateFormat: 'DD.MM.YYYY',\n    timeFormat: 'HH:mm:ss',\n    timezoneName: moment.tz.guess()\n  };\n  let service: SettingsService;\n\n  beforeEach( () => {\n    TestBed.configureTestingModule( {\n      providers: [\n        SettingsService\n      ]\n    });\n    service = TestBed.inject( SettingsService );\n  });\n\n  it( 'it should be created', () => {\n    expect( service ).toBeTruthy();\n  });\n\n  it( 'it should return default settings value', () => {\n    expect( service.resolve() ).toBe( DEFAULT_SETTINGS );\n    expect( service.appSettings ).toBe( DEFAULT_SETTINGS );\n  });\n\n  it( 'it should return new settings value from getter', () => {\n    service.use( test_settings );\n    expect( service.resolve() ).toBe( test_settings );\n    expect( service.appSettings ).toBe( test_settings );\n  });\n\n  it( 'it should return default settings value from observable', () => {\n    service.appSettings$.subscribe( ( value: AppSettings ) => expect( value ).toBe( DEFAULT_SETTINGS ));\n  });\n\n  it( 'it should return test settings value from observable', () => {\n    service.use( test_settings );\n    service.appSettings$.subscribe( ( value: AppSettings ) => expect( value ).toBe( test_settings ));\n  });\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,eAAe,QAAQ,oBAAoB;AACtE,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAEhCC,QAAQ,CAAE,qBAAqB,EAAE,MAAK;EACpC,MAAMC,aAAa,GAAgB;IACjCC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAEC,MAAM,CAACC,SAAS,CAACC,QAAQ;IACzCC,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE,UAAU;IACtBC,YAAY,EAAEV,MAAM,CAACW,EAAE,CAACC,KAAK;GAC9B;EACD,IAAIC,OAAwB;EAE5BC,UAAU,CAAE,MAAK;IACff,OAAO,CAACgB,sBAAsB,CAAE;MAC9BC,SAAS,EAAE,CACTlB,eAAe;KAElB,CAAC;IACFe,OAAO,GAAGd,OAAO,CAACkB,MAAM,CAAEnB,eAAe,CAAE;EAC7C,CAAC,CAAC;EAEFoB,EAAE,CAAE,sBAAsB,EAAE,MAAK;IAC/BC,MAAM,CAAEN,OAAO,CAAE,CAACO,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAE,yCAAyC,EAAE,MAAK;IAClDC,MAAM,CAAEN,OAAO,CAACQ,OAAO,EAAE,CAAE,CAACC,IAAI,CAAEzB,gBAAgB,CAAE;IACpDsB,MAAM,CAAEN,OAAO,CAACU,WAAW,CAAE,CAACD,IAAI,CAAEzB,gBAAgB,CAAE;EACxD,CAAC,CAAC;EAEFqB,EAAE,CAAE,iDAAiD,EAAE,MAAK;IAC1DL,OAAO,CAACW,GAAG,CAAEtB,aAAa,CAAE;IAC5BiB,MAAM,CAAEN,OAAO,CAACQ,OAAO,EAAE,CAAE,CAACC,IAAI,CAAEpB,aAAa,CAAE;IACjDiB,MAAM,CAAEN,OAAO,CAACU,WAAW,CAAE,CAACD,IAAI,CAAEpB,aAAa,CAAE;EACrD,CAAC,CAAC;EAEFgB,EAAE,CAAE,yDAAyD,EAAE,MAAK;IAClEL,OAAO,CAACY,YAAY,CAACC,SAAS,CAAIC,KAAkB,IAAMR,MAAM,CAAEQ,KAAK,CAAE,CAACL,IAAI,CAAEzB,gBAAgB,CAAE,CAAC;EACrG,CAAC,CAAC;EAEFqB,EAAE,CAAE,sDAAsD,EAAE,MAAK;IAC/DL,OAAO,CAACW,GAAG,CAAEtB,aAAa,CAAE;IAC5BW,OAAO,CAACY,YAAY,CAACC,SAAS,CAAIC,KAAkB,IAAMR,MAAM,CAAEQ,KAAK,CAAE,CAACL,IAAI,CAAEpB,aAAa,CAAE,CAAC;EAClG,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}