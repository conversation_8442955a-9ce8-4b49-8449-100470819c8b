{"ast": null, "code": "var _DocsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./docs.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsComponent = (_DocsComponent = class DocsComponent {\n  constructor() {}\n  ngOnInit() {}\n}, _DocsComponent.ctorParameters = () => [], _DocsComponent);\nDocsComponent = __decorate([Component({\n  selector: 'lib-docs',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DocsComponent);\nexport { DocsComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "DocsComponent", "_DocsComponent", "constructor", "ngOnInit", "ctorParameters", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/styles/docs/docs.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./docs.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsComponent = class DocsComponent {\n    constructor() { }\n    ngOnInit() {\n    }\n    static { this.ctorParameters = () => []; }\n};\nDocsComponent = __decorate([\n    Component({\n        selector: 'lib-docs',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], DocsComponent);\nexport { DocsComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,SAASC,SAAS,QAAQ,eAAe;AACzC,IAAIC,aAAa,IAAAC,cAAA,GAAG,MAAMD,aAAa,CAAC;EACpCE,WAAWA,CAAA,EAAG,CAAE;EAChBC,QAAQA,CAAA,EAAG,CACX;AAEJ,CAAC,EADYF,cAAA,CAAKG,cAAc,GAAG,MAAM,EAAE,EAAAH,cAAA,CAC1C;AACDD,aAAa,GAAGJ,UAAU,CAAC,CACvBG,SAAS,CAAC;EACNM,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAET,oBAAoB;EAC9BU,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACV,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEE,aAAa,CAAC;AACjB,SAASA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}