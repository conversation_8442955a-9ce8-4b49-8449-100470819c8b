{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Pi<PERSON> } from '@angular/core';\nimport { SwuiConstantsService } from '../../services/swui-constants.service';\nlet SwuiCurrencySymbolPipe = class SwuiCurrencySymbolPipe {\n  transform(currency) {\n    if (!currency) {\n      return '';\n    }\n    return `${SwuiConstantsService.currencySymbol(currency)}`;\n  }\n};\nSwuiCurrencySymbolPipe = __decorate([Pipe({\n  name: 'currencySymbol',\n  standalone: false\n})], SwuiCurrencySymbolPipe);\nexport { SwuiCurrencySymbolPipe };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "SwuiConstantsService", "SwuiCurrencySymbolPipe", "transform", "currency", "currencySymbol", "__decorate", "name", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/pipes/swui-currency-symbol/swui-currency-symbol.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\nimport { SwuiConstantsService } from '../../services/swui-constants.service';\n\n@Pipe({\n    name: 'currencySymbol',\n    standalone: false\n})\nexport class SwuiCurrencySymbolPipe implements PipeTransform {\n\n  transform( currency: string ): string {\n    if (!currency) { return ''; }\n    return `${SwuiConstantsService.currencySymbol(currency)}`;\n  }\n\n}\n"], "mappings": ";AAAA,SAASA,IAAI,QAAuB,eAAe;AACnD,SAASC,oBAAoB,QAAQ,uCAAuC;AAMrE,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAEjCC,SAASA,CAAEC,QAAgB;IACzB,IAAI,CAACA,QAAQ,EAAE;MAAE,OAAO,EAAE;IAAE;IAC5B,OAAO,GAAGH,oBAAoB,CAACI,cAAc,CAACD,QAAQ,CAAC,EAAE;EAC3D;CAED;AAPYF,sBAAsB,GAAAI,UAAA,EAJlCN,IAAI,CAAC;EACFO,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;CACf,CAAC,C,EACWN,sBAAsB,CAOlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}