{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Subscription } from '../Subscription';\nvar Action = function (_super) {\n  __extends(Action, _super);\n  function Action(scheduler, work) {\n    return _super.call(this) || this;\n  }\n  Action.prototype.schedule = function (state, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return this;\n  };\n  return Action;\n}(Subscription);\nexport { Action };\n//# sourceMappingURL=Action.js.map", "map": {"version": 3, "names": ["__extends", "Subscription", "Action", "_super", "scheduler", "work", "call", "prototype", "schedule", "state", "delay"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/Action.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { Subscription } from '../Subscription';\nvar Action = (function (_super) {\n    __extends(Action, _super);\n    function Action(scheduler, work) {\n        return _super.call(this) || this;\n    }\n    Action.prototype.schedule = function (state, delay) {\n        if (delay === void 0) { delay = 0; }\n        return this;\n    };\n    return Action;\n}(Subscription));\nexport { Action };\n//# sourceMappingURL=Action.js.map"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,IAAIC,MAAM,GAAI,UAAUC,MAAM,EAAE;EAC5BH,SAAS,CAACE,MAAM,EAAEC,MAAM,CAAC;EACzB,SAASD,MAAMA,CAACE,SAAS,EAAEC,IAAI,EAAE;IAC7B,OAAOF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;EACpC;EACAJ,MAAM,CAACK,SAAS,CAACC,QAAQ,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAChD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,OAAO,IAAI;EACf,CAAC;EACD,OAAOR,MAAM;AACjB,CAAC,CAACD,YAAY,CAAE;AAChB,SAASC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}