{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var UnsubscriptionError = createErrorClass(function (_super) {\n  return function UnsubscriptionErrorImpl(errors) {\n    _super(this);\n    this.message = errors ? errors.length + \" errors occurred during unsubscription:\\n\" + errors.map(function (err, i) {\n      return i + 1 + \") \" + err.toString();\n    }).join('\\n  ') : '';\n    this.name = 'UnsubscriptionError';\n    this.errors = errors;\n  };\n});\n//# sourceMappingURL=UnsubscriptionError.js.map", "map": {"version": 3, "names": ["createErrorClass", "UnsubscriptionError", "_super", "UnsubscriptionErrorImpl", "errors", "message", "length", "map", "err", "i", "toString", "join", "name"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/UnsubscriptionError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport var UnsubscriptionError = createErrorClass(function (_super) {\n    return function UnsubscriptionErrorImpl(errors) {\n        _super(this);\n        this.message = errors\n            ? errors.length + \" errors occurred during unsubscription:\\n\" + errors.map(function (err, i) { return i + 1 + \") \" + err.toString(); }).join('\\n  ')\n            : '';\n        this.name = 'UnsubscriptionError';\n        this.errors = errors;\n    };\n});\n//# sourceMappingURL=UnsubscriptionError.js.map"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,IAAIC,mBAAmB,GAAGD,gBAAgB,CAAC,UAAUE,MAAM,EAAE;EAChE,OAAO,SAASC,uBAAuBA,CAACC,MAAM,EAAE;IAC5CF,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACG,OAAO,GAAGD,MAAM,GACfA,MAAM,CAACE,MAAM,GAAG,2CAA2C,GAAGF,MAAM,CAACG,GAAG,CAAC,UAAUC,GAAG,EAAEC,CAAC,EAAE;MAAE,OAAOA,CAAC,GAAG,CAAC,GAAG,IAAI,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC;IAAE,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC,GAClJ,EAAE;IACR,IAAI,CAACC,IAAI,GAAG,qBAAqB;IACjC,IAAI,CAACR,MAAM,GAAGA,MAAM;EACxB,CAAC;AACL,CAAC,CAAC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}