{"ast": null, "code": "export { A, d as ALT, aX as APOSTROPHE, a1 as AT_SIGN, a2 as B, a_ as BACKSLASH, B as BACKSPACE, a3 as C, y as CAPS_LOCK, a$ as CLOSE_SQUARE_BRACKET, u as COMM<PERSON>, as as CONTEXT_MENU, C as CONTROL, a4 as D, aV as DASH, i as DELETE, D as DOWN_ARROW, a5 as E, Y as EIGHT, E as END, c as ENTER, aU as EQUALS, g as ESCAPE, a6 as F, F as F1, r as F10, s as F11, t as F12, j as F2, k as F3, l as F4, m as F5, n as F6, o as F7, p as F8, q as F9, $ as FF_EQUALS, aL as FF_MINUS, aP as FF_MUTE, _ as FF_SEMICOLON, aQ as FF_VOLUME_DOWN, aS as FF_VOLUME_UP, aK as FIRST_MEDIA, V as FIVE, Q as FOUR, a7 as G, a8 as H, H as HOME, a9 as I, I as INSERT, aa as J, ab as K, ac as L, aR as LAST_MEDIA, L as LEFT_ARROW, ad as M, v as MAC_ENTER, M as MAC_META, aq as MAC_WK_CMD_LEFT, ar as MAC_WK_CMD_RIGHT, e as META, aM as MUTE, ae as N, N as NINE, aH as NUMPAD_DIVIDE, aB as NUMPAD_EIGHT, ay as NUMPAD_FIVE, ax as NUMPAD_FOUR, aF as NUMPAD_MINUS, aD as NUMPAD_MULTIPLY, aC as NUMPAD_NINE, au as NUMPAD_ONE, aG as NUMPAD_PERIOD, aE as NUMPAD_PLUS, aA as NUMPAD_SEVEN, az as NUMPAD_SIX, aw as NUMPAD_THREE, av as NUMPAD_TWO, at as NUMPAD_ZERO, w as NUM_CENTER, aI as NUM_LOCK, af as O, O as ONE, aZ as OPEN_SQUARE_BRACKET, ag as P, P as PAGE_DOWN, a as PAGE_UP, x as PAUSE, h as PERIOD, z as PLUS_SIGN, G as PRINT_SCREEN, ah as Q, a0 as QUESTION_MARK, ai as R, R as RIGHT_ARROW, aj as S, aJ as SCROLL_LOCK, aT as SEMICOLON, X as SEVEN, f as SHIFT, b0 as SINGLE_QUOTE, W as SIX, aW as SLASH, S as SPACE, ak as T, T as TAB, K as THREE, aY as TILDE, J as TWO, al as U, U as UP_ARROW, am as V, aN as VOLUME_DOWN, aO as VOLUME_UP, an as W, ao as X, ap as Y, Z, b as ZERO } from './keycodes-CpHkExLC.mjs';\n\n/**\n * Checks whether a modifier key is pressed.\n * @param event Event to be checked.\n */\nfunction hasModifierKey(event, ...modifiers) {\n  if (modifiers.length) {\n    return modifiers.some(modifier => event[modifier]);\n  }\n  return event.altKey || event.shiftKey || event.ctrlKey || event.metaKey;\n}\nexport { hasModifierKey };", "map": {"version": 3, "names": ["A", "d", "ALT", "aX", "APOSTROPHE", "a1", "AT_SIGN", "a2", "B", "a_", "BACKSLASH", "BACKSPACE", "a3", "C", "y", "CAPS_LOCK", "a$", "CLOSE_SQUARE_BRACKET", "u", "COMMA", "as", "CONTEXT_MENU", "CONTROL", "a4", "D", "aV", "DASH", "i", "DELETE", "DOWN_ARROW", "a5", "E", "Y", "EIGHT", "END", "c", "ENTER", "aU", "EQUALS", "g", "ESCAPE", "a6", "F", "F1", "r", "F10", "s", "F11", "t", "F12", "j", "F2", "k", "F3", "l", "F4", "m", "F5", "n", "F6", "o", "F7", "p", "F8", "q", "F9", "$", "FF_EQUALS", "aL", "FF_MINUS", "aP", "FF_MUTE", "_", "FF_SEMICOLON", "aQ", "FF_VOLUME_DOWN", "aS", "FF_VOLUME_UP", "aK", "FIRST_MEDIA", "V", "FIVE", "Q", "FOUR", "a7", "G", "a8", "H", "HOME", "a9", "I", "INSERT", "aa", "J", "ab", "K", "ac", "L", "aR", "LAST_MEDIA", "LEFT_ARROW", "ad", "M", "v", "MAC_ENTER", "MAC_META", "aq", "MAC_WK_CMD_LEFT", "ar", "MAC_WK_CMD_RIGHT", "e", "META", "aM", "MUTE", "ae", "N", "NINE", "aH", "NUMPAD_DIVIDE", "aB", "NUMPAD_EIGHT", "ay", "NUMPAD_FIVE", "ax", "NUMPAD_FOUR", "aF", "NUMPAD_MINUS", "aD", "NUMPAD_MULTIPLY", "aC", "NUMPAD_NINE", "au", "NUMPAD_ONE", "aG", "NUMPAD_PERIOD", "aE", "NUMPAD_PLUS", "aA", "NUMPAD_SEVEN", "az", "NUMPAD_SIX", "aw", "NUMPAD_THREE", "av", "NUMPAD_TWO", "at", "NUMPAD_ZERO", "w", "NUM_CENTER", "aI", "NUM_LOCK", "af", "O", "ONE", "aZ", "OPEN_SQUARE_BRACKET", "ag", "P", "PAGE_DOWN", "a", "PAGE_UP", "x", "PAUSE", "h", "PERIOD", "z", "PLUS_SIGN", "PRINT_SCREEN", "ah", "a0", "QUESTION_MARK", "ai", "R", "RIGHT_ARROW", "aj", "S", "aJ", "SCROLL_LOCK", "aT", "SEMICOLON", "X", "SEVEN", "f", "SHIFT", "b0", "SINGLE_QUOTE", "W", "SIX", "aW", "SLASH", "SPACE", "ak", "T", "TAB", "THREE", "aY", "TILDE", "TWO", "al", "U", "UP_ARROW", "am", "aN", "VOLUME_DOWN", "aO", "VOLUME_UP", "an", "ao", "ap", "Z", "b", "ZERO", "hasModifierKey", "event", "modifiers", "length", "some", "modifier", "altKey", "shift<PERSON>ey", "ctrl<PERSON>ey", "metaKey"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/keycodes.mjs"], "sourcesContent": ["export { A, d as ALT, aX as APOSTROPHE, a1 as AT_SIGN, a2 as B, a_ as BACKSLASH, B as BACKSPACE, a3 as C, y as CAPS_LOCK, a$ as CLOSE_SQUARE_BRACKET, u as COMM<PERSON>, as as CONTEXT_MENU, C as CONTROL, a4 as D, aV as DASH, i as DELETE, D as DOWN_ARROW, a5 as E, Y as EIGHT, E as END, c as ENTER, aU as EQUALS, g as ESCAPE, a6 as F, F as F1, r as F10, s as F11, t as F12, j as F2, k as F3, l as F4, m as F5, n as F6, o as F7, p as F8, q as F9, $ as FF_EQUALS, aL as FF_MINUS, aP as FF_MUTE, _ as FF_SEMICOLON, aQ as FF_VOLUME_DOWN, aS as FF_VOLUME_UP, aK as FIRST_MEDIA, V as FIVE, Q as FOUR, a7 as G, a8 as H, H as HOME, a9 as I, I as INSERT, aa as J, ab as K, ac as L, aR as LAST_MEDIA, L as LEFT_ARROW, ad as M, v as MAC_ENTER, M as MAC_META, aq as MAC_WK_CMD_LEFT, ar as MAC_WK_CMD_RIGHT, e as META, aM as MUTE, ae as N, N as NINE, aH as NUMPAD_DIVIDE, aB as NUMPAD_EIGHT, ay as NUMPAD_FIVE, ax as NUMPAD_FOUR, aF as NUMPAD_MINUS, aD as NUMPAD_MULTIPLY, aC as NUMPAD_NINE, au as NUMPAD_ONE, aG as NUMPAD_PERIOD, aE as NUMPAD_PLUS, aA as NUMPAD_SEVEN, az as NUMPAD_SIX, aw as NUMPAD_THREE, av as NUMPAD_TWO, at as NUMPAD_ZERO, w as NUM_CENTER, aI as NUM_LOCK, af as O, O as ONE, aZ as OPEN_SQUARE_BRACKET, ag as P, P as PAGE_DOWN, a as PAGE_UP, x as PAUSE, h as PERIOD, z as PLUS_SIGN, G as PRINT_SCREEN, ah as Q, a0 as QUESTION_MARK, ai as R, R as RIGHT_ARROW, aj as S, aJ as SCROLL_LOCK, aT as SEMICOLON, X as SEVEN, f as SHIFT, b0 as SINGLE_QUOTE, W as SIX, aW as SLASH, S as SPACE, ak as T, T as TAB, K as THREE, aY as TILDE, J as TWO, al as U, U as UP_ARROW, am as V, aN as VOLUME_DOWN, aO as VOLUME_UP, an as W, ao as X, ap as Y, Z, b as ZERO } from './keycodes-CpHkExLC.mjs';\n\n/**\n * Checks whether a modifier key is pressed.\n * @param event Event to be checked.\n */\nfunction hasModifierKey(event, ...modifiers) {\n    if (modifiers.length) {\n        return modifiers.some(modifier => event[modifier]);\n    }\n    return event.altKey || event.shiftKey || event.ctrlKey || event.metaKey;\n}\n\nexport { hasModifierKey };\n"], "mappings": "AAAA,SAASA,CAAC,EAAEC,CAAC,IAAIC,GAAG,EAAEC,EAAE,IAAIC,UAAU,EAAEC,EAAE,IAAIC,OAAO,EAAEC,EAAE,IAAIC,CAAC,EAAEC,EAAE,IAAIC,SAAS,EAAEF,CAAC,IAAIG,SAAS,EAAEC,EAAE,IAAIC,CAAC,EAAEC,CAAC,IAAIC,SAAS,EAAEC,EAAE,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,KAAK,EAAEC,EAAE,IAAIC,YAAY,EAAER,CAAC,IAAIS,OAAO,EAAEC,EAAE,IAAIC,CAAC,EAAEC,EAAE,IAAIC,IAAI,EAAEC,CAAC,IAAIC,MAAM,EAAEJ,CAAC,IAAIK,UAAU,EAAEC,EAAE,IAAIC,CAAC,EAAEC,CAAC,IAAIC,KAAK,EAAEF,CAAC,IAAIG,GAAG,EAAEC,CAAC,IAAIC,KAAK,EAAEC,EAAE,IAAIC,MAAM,EAAEC,CAAC,IAAIC,MAAM,EAAEC,EAAE,IAAIC,CAAC,EAAEA,CAAC,IAAIC,EAAE,EAAEC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,EAAE,EAAEC,CAAC,IAAIC,EAAE,EAAEC,CAAC,IAAIC,EAAE,EAAEC,CAAC,IAAIC,EAAE,EAAEC,CAAC,IAAIC,EAAE,EAAEC,CAAC,IAAIC,EAAE,EAAEC,CAAC,IAAIC,EAAE,EAAEC,CAAC,IAAIC,EAAE,EAAEC,CAAC,IAAIC,SAAS,EAAEC,EAAE,IAAIC,QAAQ,EAAEC,EAAE,IAAIC,OAAO,EAAEC,CAAC,IAAIC,YAAY,EAAEC,EAAE,IAAIC,cAAc,EAAEC,EAAE,IAAIC,YAAY,EAAEC,EAAE,IAAIC,WAAW,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,IAAI,EAAEC,EAAE,IAAIC,CAAC,EAAEC,EAAE,IAAIC,CAAC,EAAEA,CAAC,IAAIC,IAAI,EAAEC,EAAE,IAAIC,CAAC,EAAEA,CAAC,IAAIC,MAAM,EAAEC,EAAE,IAAIC,CAAC,EAAEC,EAAE,IAAIC,CAAC,EAAEC,EAAE,IAAIC,CAAC,EAAEC,EAAE,IAAIC,UAAU,EAAEF,CAAC,IAAIG,UAAU,EAAEC,EAAE,IAAIC,CAAC,EAAEC,CAAC,IAAIC,SAAS,EAAEF,CAAC,IAAIG,QAAQ,EAAEC,EAAE,IAAIC,eAAe,EAAEC,EAAE,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,IAAI,EAAEC,EAAE,IAAIC,IAAI,EAAEC,EAAE,IAAIC,CAAC,EAAEA,CAAC,IAAIC,IAAI,EAAEC,EAAE,IAAIC,aAAa,EAAEC,EAAE,IAAIC,YAAY,EAAEC,EAAE,IAAIC,WAAW,EAAEC,EAAE,IAAIC,WAAW,EAAEC,EAAE,IAAIC,YAAY,EAAEC,EAAE,IAAIC,eAAe,EAAEC,EAAE,IAAIC,WAAW,EAAEC,EAAE,IAAIC,UAAU,EAAEC,EAAE,IAAIC,aAAa,EAAEC,EAAE,IAAIC,WAAW,EAAEC,EAAE,IAAIC,YAAY,EAAEC,EAAE,IAAIC,UAAU,EAAEC,EAAE,IAAIC,YAAY,EAAEC,EAAE,IAAIC,UAAU,EAAEC,EAAE,IAAIC,WAAW,EAAEC,CAAC,IAAIC,UAAU,EAAEC,EAAE,IAAIC,QAAQ,EAAEC,EAAE,IAAIC,CAAC,EAAEA,CAAC,IAAIC,GAAG,EAAEC,EAAE,IAAIC,mBAAmB,EAAEC,EAAE,IAAIC,CAAC,EAAEA,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,KAAK,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,SAAS,EAAEjF,CAAC,IAAIkF,YAAY,EAAEC,EAAE,IAAItF,CAAC,EAAEuF,EAAE,IAAIC,aAAa,EAAEC,EAAE,IAAIC,CAAC,EAAEA,CAAC,IAAIC,WAAW,EAAEC,EAAE,IAAIC,CAAC,EAAEC,EAAE,IAAIC,WAAW,EAAEC,EAAE,IAAIC,SAAS,EAAEC,CAAC,IAAIC,KAAK,EAAEC,CAAC,IAAIC,KAAK,EAAEC,EAAE,IAAIC,YAAY,EAAEC,CAAC,IAAIC,GAAG,EAAEC,EAAE,IAAIC,KAAK,EAAEd,CAAC,IAAIe,KAAK,EAAEC,EAAE,IAAIC,CAAC,EAAEA,CAAC,IAAIC,GAAG,EAAElG,CAAC,IAAImG,KAAK,EAAEC,EAAE,IAAIC,KAAK,EAAEvG,CAAC,IAAIwG,GAAG,EAAEC,EAAE,IAAIC,CAAC,EAAEA,CAAC,IAAIC,QAAQ,EAAEC,EAAE,IAAIzH,CAAC,EAAE0H,EAAE,IAAIC,WAAW,EAAEC,EAAE,IAAIC,SAAS,EAAEC,EAAE,IAAIpB,CAAC,EAAEqB,EAAE,IAAI3B,CAAC,EAAE4B,EAAE,IAAIhL,CAAC,EAAEiL,CAAC,EAAEC,CAAC,IAAIC,IAAI,QAAQ,yBAAyB;;AAE7oD;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE,GAAGC,SAAS,EAAE;EACzC,IAAIA,SAAS,CAACC,MAAM,EAAE;IAClB,OAAOD,SAAS,CAACE,IAAI,CAACC,QAAQ,IAAIJ,KAAK,CAACI,QAAQ,CAAC,CAAC;EACtD;EACA,OAAOJ,KAAK,CAACK,MAAM,IAAIL,KAAK,CAACM,QAAQ,IAAIN,KAAK,CAACO,OAAO,IAAIP,KAAK,CAACQ,OAAO;AAC3E;AAEA,SAAST,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}