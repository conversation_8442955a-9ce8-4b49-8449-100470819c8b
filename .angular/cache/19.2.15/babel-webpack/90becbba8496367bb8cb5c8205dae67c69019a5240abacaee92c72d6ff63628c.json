{"ast": null, "code": "var _SwuiTdIconPopoverWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./icon-popover.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdIconPopoverWidgetComponent = (_SwuiTdIconPopoverWidgetComponent = class SwuiTdIconPopoverWidgetComponent {\n  constructor({\n    field,\n    schema,\n    row,\n    value\n  }, sanitizer) {\n    var _schema$td, _schema$td2, _schema$td3;\n    const titleFn = (_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.titleFn;\n    const classFn = (_schema$td2 = schema.td) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.classFn;\n    this.value = value;\n    this.popoverText = titleFn && titleFn(row, field);\n    this.classObj = classFn && classFn(row, field);\n    if ((_schema$td3 = schema.td) !== null && _schema$td3 !== void 0 && _schema$td3.sanitizeValue) {\n      this.popoverText = sanitizer.bypassSecurityTrustHtml(this.popoverText);\n    }\n  }\n}, _SwuiTdIconPopoverWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}, {\n  type: DomSanitizer\n}], _SwuiTdIconPopoverWidgetComponent);\nSwuiTdIconPopoverWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-icon-popover-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdIconPopoverWidgetComponent);\nexport { SwuiTdIconPopoverWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "Dom<PERSON><PERSON><PERSON>zer", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdIconPopoverWidgetComponent", "_SwuiTdIconPopoverWidgetComponent", "constructor", "field", "schema", "row", "value", "sanitizer", "_schema$td", "_schema$td2", "_schema$td3", "titleFn", "td", "classFn", "popoverText", "classObj", "sanitizeValue", "bypassSecurityTrustHtml", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/icon-popover/icon-popover.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./icon-popover.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdIconPopoverWidgetComponent = class SwuiTdIconPopoverWidgetComponent {\n    constructor({ field, schema, row, value }, sanitizer) {\n        const titleFn = schema.td?.titleFn;\n        const classFn = schema.td?.classFn;\n        this.value = value;\n        this.popoverText = titleFn && titleFn(row, field);\n        this.classObj = classFn && classFn(row, field);\n        if (schema.td?.sanitizeValue) {\n            this.popoverText = sanitizer.bypassSecurityTrustHtml(this.popoverText);\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] },\n        { type: DomSanitizer }\n    ]; }\n};\nSwuiTdIconPopoverWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-icon-popover-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdIconPopoverWidgetComponent);\nexport { SwuiTdIconPopoverWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,gCAAgC,IAAAC,iCAAA,GAAG,MAAMD,gCAAgC,CAAC;EAC1EE,WAAWA,CAAC;IAAEC,KAAK;IAAEC,MAAM;IAAEC,GAAG;IAAEC;EAAM,CAAC,EAAEC,SAAS,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA;IAClD,MAAMC,OAAO,IAAAH,UAAA,GAAGJ,MAAM,CAACQ,EAAE,cAAAJ,UAAA,uBAATA,UAAA,CAAWG,OAAO;IAClC,MAAME,OAAO,IAAAJ,WAAA,GAAGL,MAAM,CAACQ,EAAE,cAAAH,WAAA,uBAATA,WAAA,CAAWI,OAAO;IAClC,IAAI,CAACP,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACQ,WAAW,GAAGH,OAAO,IAAIA,OAAO,CAACN,GAAG,EAAEF,KAAK,CAAC;IACjD,IAAI,CAACY,QAAQ,GAAGF,OAAO,IAAIA,OAAO,CAACR,GAAG,EAAEF,KAAK,CAAC;IAC9C,KAAAO,WAAA,GAAIN,MAAM,CAACQ,EAAE,cAAAF,WAAA,eAATA,WAAA,CAAWM,aAAa,EAAE;MAC1B,IAAI,CAACF,WAAW,GAAGP,SAAS,CAACU,uBAAuB,CAAC,IAAI,CAACH,WAAW,CAAC;IAC1E;EACJ;AAKJ,CAAC,EAJYb,iCAAA,CAAKiB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEtB,MAAM;IAAEyB,IAAI,EAAE,CAACvB,uBAAuB;EAAG,CAAC;AAAE,CAAC,EACrF;EAAEoB,IAAI,EAAErB;AAAa,CAAC,CACzB,EAAAG,iCAAA,CACJ;AACDD,gCAAgC,GAAGN,UAAU,CAAC,CAC1CE,SAAS,CAAC;EACN2B,QAAQ,EAAE,iCAAiC;EAC3CC,QAAQ,EAAE7B,oBAAoB;EAC9B8B,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEzB,gCAAgC,CAAC;AACpC,SAASA,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}