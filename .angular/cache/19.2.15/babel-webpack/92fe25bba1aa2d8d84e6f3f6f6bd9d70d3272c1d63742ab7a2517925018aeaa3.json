{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiTranslationsManagerComponent } from './swui-translations-manager.component';\nlet SwuiTranslationsManagerModule = class SwuiTranslationsManagerModule {};\nSwuiTranslationsManagerModule = __decorate([NgModule({\n  imports: [CommonModule, ReactiveFormsModule, TranslateModule.forChild(), MatTabsModule, MatFormFieldModule, MatInputModule, MatIconModule, MatButtonModule, MatRippleModule, MatMenuModule],\n  exports: [SwuiTranslationsManagerComponent],\n  declarations: [SwuiTranslationsManagerComponent]\n})], SwuiTranslationsManagerModule);\nexport { SwuiTranslationsManagerModule };", "map": {"version": 3, "names": ["__decorate", "CommonModule", "NgModule", "ReactiveFormsModule", "MatButtonModule", "MatRippleModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatMenuModule", "MatTabsModule", "TranslateModule", "SwuiTranslationsManagerComponent", "SwuiTranslationsManagerModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-translations-manager/swui-translations-manager.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiTranslationsManagerComponent } from './swui-translations-manager.component';\nlet SwuiTranslationsManagerModule = class SwuiTranslationsManagerModule {\n};\nSwuiTranslationsManagerModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            ReactiveFormsModule,\n            TranslateModule.forChild(),\n            MatTabsModule,\n            MatFormFieldModule,\n            MatInputModule,\n            MatIconModule,\n            MatButtonModule,\n            MatRippleModule,\n            MatMenuModule,\n        ],\n        exports: [\n            SwuiTranslationsManagerComponent,\n        ],\n        declarations: [\n            SwuiTranslationsManagerComponent,\n        ]\n    })\n], SwuiTranslationsManagerModule);\nexport { SwuiTranslationsManagerModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,gCAAgC,QAAQ,uCAAuC;AACxF,IAAIC,6BAA6B,GAAG,MAAMA,6BAA6B,CAAC,EACvE;AACDA,6BAA6B,GAAGb,UAAU,CAAC,CACvCE,QAAQ,CAAC;EACLY,OAAO,EAAE,CACLb,YAAY,EACZE,mBAAmB,EACnBQ,eAAe,CAACI,QAAQ,CAAC,CAAC,EAC1BL,aAAa,EACbJ,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbH,eAAe,EACfC,eAAe,EACfI,aAAa,CAChB;EACDO,OAAO,EAAE,CACLJ,gCAAgC,CACnC;EACDK,YAAY,EAAE,CACVL,gCAAgC;AAExC,CAAC,CAAC,CACL,EAAEC,6BAA6B,CAAC;AACjC,SAASA,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}