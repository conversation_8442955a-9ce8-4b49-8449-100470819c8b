{"ast": null, "code": "import { moduleMetadata } from '@storybook/angular';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport moment from 'moment';\nimport { I18nModule } from '../i18n.module';\nimport { SwuiDateRangeModule } from './swui-date-range.module';\nimport { SwuiDateRangeComponent } from './swui-date-range.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nconst EN = require('./locale.json');\nexport const template = `\n  <mat-card style=\"margin: 32px\">\n    <mat-form-field appearance=\"outline\" style=\"width: 500px\">\n    <mat-label>Test control label</mat-label>\n    <lib-swui-date-range\n      [title]=\"title\"\n      [config]=\"config\"\n      [disabled]=\"disabled\"\n      [minDate]=\"minDate\"\n      [maxDate]=\"maxDate\"\n      [customPeriods]=\"config?.customPeriods\"\n      [value]=\"value\">\n    </lib-swui-date-range>\n    <mat-icon matPrefix>search</mat-icon>\n  </mat-form-field>\n</mat-card>\n`;\nconst meta = {\n  title: 'Date/Date Range',\n  component: SwuiDateRangeComponent,\n  decorators: [moduleMetadata({\n    imports: [BrowserAnimationsModule, I18nModule.forRoot({\n      translations: {\n        en: EN\n      }\n    }), SwuiDateRangeModule, MatFormFieldModule, MatCardModule, MatIconModule]\n  })],\n  parameters: {\n    layout: 'centered'\n  },\n  tags: ['autodocs']\n};\nexport default meta;\nexport const Default = {\n  args: {\n    title: 'Test title'\n  },\n  render: args => ({\n    props: args,\n    template: template\n  })\n};\nexport const WithValue = {\n  args: {\n    title: 'Test title',\n    value: {\n      from: '2020-08-13T23:00:00.000Z',\n      to: '2020-08-21T23:00:00.000Z'\n    }\n  },\n  render: args => ({\n    props: args,\n    template: template\n  })\n};\nexport const WithConfig = {\n  args: {\n    title: 'Test title',\n    value: {\n      from: '2020-08-13T00:00:00.000Z',\n      to: '2020-08-21T00:00:00.000Z'\n    },\n    config: {\n      dateFormat: 'YY/MM/DD',\n      timePicker: true,\n      timeFormat: 'HH:mm z',\n      timeZone: 'Asia/Taipei',\n      timeDisableLevel: {\n        hour: true,\n        minute: false,\n        second: false\n      }\n    }\n  },\n  render: args => ({\n    props: args,\n    template: template\n  })\n};\nexport const Disabled = {\n  args: {\n    title: 'Test title',\n    value: {\n      from: '2020-08-13T23:00:00.000Z',\n      to: '2020-08-21T23:00:00.000Z'\n    },\n    disabled: true\n  },\n  render: args => ({\n    props: args,\n    template: template\n  })\n};\nexport const MinMax = {\n  args: {\n    title: 'Test title',\n    minDate: '2020-08-10T00:00:00.000Z',\n    maxDate: '2020-08-28T00:00:00.000Z'\n  },\n  render: args => ({\n    props: args,\n    template: template\n  })\n};\nexport const MaxMonthPeriod = {\n  args: {\n    title: 'Test title',\n    value: {\n      from: '2022-05-15T11:00:00.000Z',\n      to: '2022-06-15T10:59:59.000Z'\n    },\n    config: {\n      maxPeriod: 'month',\n      timePicker: true,\n      timeDisableLevel: {\n        hour: true,\n        minute: true,\n        second: true\n      },\n      timeZone: 'Asia/Tbilisi'\n    }\n  },\n  render: args => ({\n    props: args,\n    template: template\n  })\n};\nexport const CustomPeriods = {\n  args: {\n    title: 'Test title',\n    config: {\n      customPeriods: [{\n        title: 'COMPONENTS.DATE_RANGE.last7Days',\n        fn: timezone => {\n          const date = timezone ? moment().tz(timezone) : moment.utc();\n          const to = date.clone().endOf('day');\n          const from = date.clone().startOf('day').subtract(6, 'd');\n          return {\n            from: from.toISOString(),\n            to: to.milliseconds(0).toISOString()\n          };\n        }\n      }]\n    }\n  },\n  render: args => ({\n    props: args,\n    template: template\n  })\n};", "map": {"version": 3, "names": ["moduleMetadata", "BrowserAnimationsModule", "moment", "I18nModule", "SwuiDateRangeModule", "SwuiDateRangeComponent", "MatFormFieldModule", "MatIconModule", "MatCardModule", "EN", "require", "template", "meta", "title", "component", "decorators", "imports", "forRoot", "translations", "en", "parameters", "layout", "tags", "<PERSON><PERSON><PERSON>", "args", "render", "props", "WithValue", "value", "from", "to", "WithConfig", "config", "dateFormat", "timePicker", "timeFormat", "timeZone", "timeDisableLevel", "hour", "minute", "second", "Disabled", "disabled", "MinMax", "minDate", "maxDate", "MaxMonthPeriod", "max<PERSON><PERSON><PERSON>", "CustomPeriods", "customPeriods", "fn", "timezone", "date", "tz", "utc", "clone", "endOf", "startOf", "subtract", "toISOString", "milliseconds"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-range/swui-date-range.stories.ts"], "sourcesContent": ["import { moduleMetadata } from '@storybook/angular';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport moment from 'moment';\nimport { I18nModule } from '../i18n.module';\nimport { SwuiDateRangeModule } from './swui-date-range.module';\nimport { SwuiDateRangeComponent } from './swui-date-range.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nconst EN = require('./locale.json');\nexport const template = `\n  <mat-card style=\"margin: 32px\">\n    <mat-form-field appearance=\"outline\" style=\"width: 500px\">\n    <mat-label>Test control label</mat-label>\n    <lib-swui-date-range\n      [title]=\"title\"\n      [config]=\"config\"\n      [disabled]=\"disabled\"\n      [minDate]=\"minDate\"\n      [maxDate]=\"maxDate\"\n      [customPeriods]=\"config?.customPeriods\"\n      [value]=\"value\">\n    </lib-swui-date-range>\n    <mat-icon matPrefix>search</mat-icon>\n  </mat-form-field>\n</mat-card>\n`;\nconst meta = {\n    title: 'Date/Date Range',\n    component: SwuiDateRangeComponent,\n    decorators: [\n        moduleMetadata({\n            imports: [\n                BrowserAnimationsModule,\n                I18nModule.forRoot({ translations: { en: EN } }),\n                SwuiDateRangeModule,\n                MatFormFieldModule,\n                MatCardModule,\n                MatIconModule,\n            ],\n        })\n    ],\n    parameters: {\n        layout: 'centered',\n    },\n    tags: ['autodocs'],\n};\nexport default meta;\nexport const Default = {\n    args: {\n        title: 'Test title'\n    },\n    render: (args) => ({\n        props: args,\n        template: template,\n    }),\n};\nexport const WithValue = {\n    args: {\n        title: 'Test title',\n        value: { from: '2020-08-13T23:00:00.000Z', to: '2020-08-21T23:00:00.000Z' }\n    },\n    render: (args) => ({\n        props: args,\n        template: template,\n    }),\n};\nexport const WithConfig = {\n    args: {\n        title: 'Test title',\n        value: { from: '2020-08-13T00:00:00.000Z', to: '2020-08-21T00:00:00.000Z' },\n        config: {\n            dateFormat: 'YY/MM/DD',\n            timePicker: true,\n            timeFormat: 'HH:mm z',\n            timeZone: 'Asia/Taipei',\n            timeDisableLevel: {\n                hour: true,\n                minute: false,\n                second: false\n            }\n        }\n    },\n    render: (args) => ({\n        props: args,\n        template: template,\n    }),\n};\nexport const Disabled = {\n    args: {\n        title: 'Test title',\n        value: { from: '2020-08-13T23:00:00.000Z', to: '2020-08-21T23:00:00.000Z' },\n        disabled: true\n    },\n    render: (args) => ({\n        props: args,\n        template: template,\n    }),\n};\nexport const MinMax = {\n    args: {\n        title: 'Test title',\n        minDate: '2020-08-10T00:00:00.000Z',\n        maxDate: '2020-08-28T00:00:00.000Z'\n    },\n    render: (args) => ({\n        props: args,\n        template: template,\n    }),\n};\nexport const MaxMonthPeriod = {\n    args: {\n        title: 'Test title',\n        value: { from: '2022-05-15T11:00:00.000Z', to: '2022-06-15T10:59:59.000Z' },\n        config: {\n            maxPeriod: 'month',\n            timePicker: true,\n            timeDisableLevel: {\n                hour: true,\n                minute: true,\n                second: true\n            },\n            timeZone: 'Asia/Tbilisi'\n        },\n    },\n    render: (args) => ({\n        props: args,\n        template: template,\n    }),\n};\nexport const CustomPeriods = {\n    args: {\n        title: 'Test title',\n        config: {\n            customPeriods: [\n                {\n                    title: 'COMPONENTS.DATE_RANGE.last7Days',\n                    fn: (timezone) => {\n                        const date = timezone ? moment().tz(timezone) : moment.utc();\n                        const to = date.clone().endOf('day');\n                        const from = date.clone().startOf('day').subtract(6, 'd');\n                        return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };\n                    },\n                },\n            ]\n        }\n    },\n    render: (args) => ({\n        props: args,\n        template: template,\n    }),\n};\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,MAAMC,EAAE,GAAGC,OAAO,CAAC,eAAe,CAAC;AACnC,OAAO,MAAMC,QAAQ,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,MAAMC,IAAI,GAAG;EACTC,KAAK,EAAE,iBAAiB;EACxBC,SAAS,EAAET,sBAAsB;EACjCU,UAAU,EAAE,CACRf,cAAc,CAAC;IACXgB,OAAO,EAAE,CACLf,uBAAuB,EACvBE,UAAU,CAACc,OAAO,CAAC;MAAEC,YAAY,EAAE;QAAEC,EAAE,EAAEV;MAAG;IAAE,CAAC,CAAC,EAChDL,mBAAmB,EACnBE,kBAAkB,EAClBE,aAAa,EACbD,aAAa;EAErB,CAAC,CAAC,CACL;EACDa,UAAU,EAAE;IACRC,MAAM,EAAE;EACZ,CAAC;EACDC,IAAI,EAAE,CAAC,UAAU;AACrB,CAAC;AACD,eAAeV,IAAI;AACnB,OAAO,MAAMW,OAAO,GAAG;EACnBC,IAAI,EAAE;IACFX,KAAK,EAAE;EACX,CAAC;EACDY,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXb,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC;AACD,OAAO,MAAMgB,SAAS,GAAG;EACrBH,IAAI,EAAE;IACFX,KAAK,EAAE,YAAY;IACnBe,KAAK,EAAE;MAAEC,IAAI,EAAE,0BAA0B;MAAEC,EAAE,EAAE;IAA2B;EAC9E,CAAC;EACDL,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXb,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC;AACD,OAAO,MAAMoB,UAAU,GAAG;EACtBP,IAAI,EAAE;IACFX,KAAK,EAAE,YAAY;IACnBe,KAAK,EAAE;MAAEC,IAAI,EAAE,0BAA0B;MAAEC,EAAE,EAAE;IAA2B,CAAC;IAC3EE,MAAM,EAAE;MACJC,UAAU,EAAE,UAAU;MACtBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,SAAS;MACrBC,QAAQ,EAAE,aAAa;MACvBC,gBAAgB,EAAE;QACdC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE;MACZ;IACJ;EACJ,CAAC;EACDf,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXb,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC;AACD,OAAO,MAAM8B,QAAQ,GAAG;EACpBjB,IAAI,EAAE;IACFX,KAAK,EAAE,YAAY;IACnBe,KAAK,EAAE;MAAEC,IAAI,EAAE,0BAA0B;MAAEC,EAAE,EAAE;IAA2B,CAAC;IAC3EY,QAAQ,EAAE;EACd,CAAC;EACDjB,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXb,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC;AACD,OAAO,MAAMgC,MAAM,GAAG;EAClBnB,IAAI,EAAE;IACFX,KAAK,EAAE,YAAY;IACnB+B,OAAO,EAAE,0BAA0B;IACnCC,OAAO,EAAE;EACb,CAAC;EACDpB,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXb,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC;AACD,OAAO,MAAMmC,cAAc,GAAG;EAC1BtB,IAAI,EAAE;IACFX,KAAK,EAAE,YAAY;IACnBe,KAAK,EAAE;MAAEC,IAAI,EAAE,0BAA0B;MAAEC,EAAE,EAAE;IAA2B,CAAC;IAC3EE,MAAM,EAAE;MACJe,SAAS,EAAE,OAAO;MAClBb,UAAU,EAAE,IAAI;MAChBG,gBAAgB,EAAE;QACdC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;MACZ,CAAC;MACDJ,QAAQ,EAAE;IACd;EACJ,CAAC;EACDX,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXb,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC;AACD,OAAO,MAAMqC,aAAa,GAAG;EACzBxB,IAAI,EAAE;IACFX,KAAK,EAAE,YAAY;IACnBmB,MAAM,EAAE;MACJiB,aAAa,EAAE,CACX;QACIpC,KAAK,EAAE,iCAAiC;QACxCqC,EAAE,EAAGC,QAAQ,IAAK;UACd,MAAMC,IAAI,GAAGD,QAAQ,GAAGjD,MAAM,CAAC,CAAC,CAACmD,EAAE,CAACF,QAAQ,CAAC,GAAGjD,MAAM,CAACoD,GAAG,CAAC,CAAC;UAC5D,MAAMxB,EAAE,GAAGsB,IAAI,CAACG,KAAK,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;UACpC,MAAM3B,IAAI,GAAGuB,IAAI,CAACG,KAAK,CAAC,CAAC,CAACE,OAAO,CAAC,KAAK,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UACzD,OAAO;YAAE7B,IAAI,EAAEA,IAAI,CAAC8B,WAAW,CAAC,CAAC;YAAE7B,EAAE,EAAEA,EAAE,CAAC8B,YAAY,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC;UAAE,CAAC;QAC7E;MACJ,CAAC;IAET;EACJ,CAAC;EACDlC,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXb,QAAQ,EAAEA;EACd,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}