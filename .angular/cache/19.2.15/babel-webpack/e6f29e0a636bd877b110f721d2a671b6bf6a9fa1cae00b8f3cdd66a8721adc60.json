{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nexport function forkJoin() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var resultSelector = popResultSelector(args);\n  var _a = argsArgArrayOrObject(args),\n    sources = _a.args,\n    keys = _a.keys;\n  var result = new Observable(function (subscriber) {\n    var length = sources.length;\n    if (!length) {\n      subscriber.complete();\n      return;\n    }\n    var values = new Array(length);\n    var remainingCompletions = length;\n    var remainingEmissions = length;\n    var _loop_1 = function (sourceIndex) {\n      var hasValue = false;\n      innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, function (value) {\n        if (!hasValue) {\n          hasValue = true;\n          remainingEmissions--;\n        }\n        values[sourceIndex] = value;\n      }, function () {\n        return remainingCompletions--;\n      }, undefined, function () {\n        if (!remainingCompletions || !hasValue) {\n          if (!remainingEmissions) {\n            subscriber.next(keys ? createObject(keys, values) : values);\n          }\n          subscriber.complete();\n        }\n      }));\n    };\n    for (var sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n      _loop_1(sourceIndex);\n    }\n  });\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\n//# sourceMappingURL=forkJoin.js.map", "map": {"version": 3, "names": ["Observable", "argsArgArrayOrObject", "innerFrom", "popResultSelector", "createOperatorSubscriber", "mapOneOrManyArgs", "createObject", "fork<PERSON><PERSON>n", "args", "_i", "arguments", "length", "resultSelector", "_a", "sources", "keys", "result", "subscriber", "complete", "values", "Array", "remainingCompletions", "remainingEmissions", "_loop_1", "sourceIndex", "hasValue", "subscribe", "value", "undefined", "next", "pipe"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/forkJoin.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nexport function forkJoin() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var resultSelector = popResultSelector(args);\n    var _a = argsArgArrayOrObject(args), sources = _a.args, keys = _a.keys;\n    var result = new Observable(function (subscriber) {\n        var length = sources.length;\n        if (!length) {\n            subscriber.complete();\n            return;\n        }\n        var values = new Array(length);\n        var remainingCompletions = length;\n        var remainingEmissions = length;\n        var _loop_1 = function (sourceIndex) {\n            var hasValue = false;\n            innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, function (value) {\n                if (!hasValue) {\n                    hasValue = true;\n                    remainingEmissions--;\n                }\n                values[sourceIndex] = value;\n            }, function () { return remainingCompletions--; }, undefined, function () {\n                if (!remainingCompletions || !hasValue) {\n                    if (!remainingEmissions) {\n                        subscriber.next(keys ? createObject(keys, values) : values);\n                    }\n                    subscriber.complete();\n                }\n            }));\n        };\n        for (var sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n            _loop_1(sourceIndex);\n        }\n    });\n    return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\n//# sourceMappingURL=forkJoin.js.map"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,YAAY,QAAQ,sBAAsB;AACnD,OAAO,SAASC,QAAQA,CAAA,EAAG;EACvB,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,IAAIG,cAAc,GAAGT,iBAAiB,CAACK,IAAI,CAAC;EAC5C,IAAIK,EAAE,GAAGZ,oBAAoB,CAACO,IAAI,CAAC;IAAEM,OAAO,GAAGD,EAAE,CAACL,IAAI;IAAEO,IAAI,GAAGF,EAAE,CAACE,IAAI;EACtE,IAAIC,MAAM,GAAG,IAAIhB,UAAU,CAAC,UAAUiB,UAAU,EAAE;IAC9C,IAAIN,MAAM,GAAGG,OAAO,CAACH,MAAM;IAC3B,IAAI,CAACA,MAAM,EAAE;MACTM,UAAU,CAACC,QAAQ,CAAC,CAAC;MACrB;IACJ;IACA,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAACT,MAAM,CAAC;IAC9B,IAAIU,oBAAoB,GAAGV,MAAM;IACjC,IAAIW,kBAAkB,GAAGX,MAAM;IAC/B,IAAIY,OAAO,GAAG,SAAAA,CAAUC,WAAW,EAAE;MACjC,IAAIC,QAAQ,GAAG,KAAK;MACpBvB,SAAS,CAACY,OAAO,CAACU,WAAW,CAAC,CAAC,CAACE,SAAS,CAACtB,wBAAwB,CAACa,UAAU,EAAE,UAAUU,KAAK,EAAE;QAC5F,IAAI,CAACF,QAAQ,EAAE;UACXA,QAAQ,GAAG,IAAI;UACfH,kBAAkB,EAAE;QACxB;QACAH,MAAM,CAACK,WAAW,CAAC,GAAGG,KAAK;MAC/B,CAAC,EAAE,YAAY;QAAE,OAAON,oBAAoB,EAAE;MAAE,CAAC,EAAEO,SAAS,EAAE,YAAY;QACtE,IAAI,CAACP,oBAAoB,IAAI,CAACI,QAAQ,EAAE;UACpC,IAAI,CAACH,kBAAkB,EAAE;YACrBL,UAAU,CAACY,IAAI,CAACd,IAAI,GAAGT,YAAY,CAACS,IAAI,EAAEI,MAAM,CAAC,GAAGA,MAAM,CAAC;UAC/D;UACAF,UAAU,CAACC,QAAQ,CAAC,CAAC;QACzB;MACJ,CAAC,CAAC,CAAC;IACP,CAAC;IACD,KAAK,IAAIM,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGb,MAAM,EAAEa,WAAW,EAAE,EAAE;MAC3DD,OAAO,CAACC,WAAW,CAAC;IACxB;EACJ,CAAC,CAAC;EACF,OAAOZ,cAAc,GAAGI,MAAM,CAACc,IAAI,CAACzB,gBAAgB,CAACO,cAAc,CAAC,CAAC,GAAGI,MAAM;AAClF;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}