{"ast": null, "code": "export const settingsNames = {\n  filter: 'filter'\n};", "map": {"version": 3, "names": ["settingsNames", "filter"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-dexie/dexie-types.ts"], "sourcesContent": ["export const settingsNames = {\n    filter: 'filter'\n};\n"], "mappings": "AAAA,OAAO,MAAMA,aAAa,GAAG;EACzBC,MAAM,EAAE;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}