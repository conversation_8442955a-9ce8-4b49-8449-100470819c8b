{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuSelectComponent } from './swui-menu-select.component';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const MENU_SELECT_MODULES = [FormsModule, ReactiveFormsModule, MatInputModule, MatIconModule, MatCheckboxModule, MatSelectModule, MatButtonModule, MatRippleModule];\nlet SwuiMenuSelectModule = class SwuiMenuSelectModule {};\nSwuiMenuSelectModule = __decorate([NgModule({\n  declarations: [SwuiMenuSelectComponent],\n  imports: [CommonModule, TranslateModule.forChild(), ...MENU_SELECT_MODULES],\n  exports: [SwuiMenuSelectComponent]\n})], SwuiMenuSelectModule);\nexport { SwuiMenuSelectModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "FormsModule", "ReactiveFormsModule", "TranslateModule", "SwuiMenuSelectComponent", "MatCheckboxModule", "MatSelectModule", "MatIconModule", "MatInputModule", "MatButtonModule", "MatRippleModule", "MENU_SELECT_MODULES", "SwuiMenuSelectModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-menu-select/swui-menu-select.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { SwuiMenuSelectComponent } from './swui-menu-select.component';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\n\n\nexport const MENU_SELECT_MODULES = [\n  FormsModule,\n  ReactiveFormsModule,\n  MatInputModule,\n  MatIconModule,\n  MatCheckboxModule,\n  MatSelectModule,\n  MatButtonModule,\n  MatRippleModule,\n];\n\n@NgModule({\n  declarations: [SwuiMenuSelectComponent],\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    ...MENU_SELECT_MODULES\n  ],\n  exports: [SwuiMenuSelectComponent],\n})\nexport class SwuiMenuSelectModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,wBAAwB;AAGxD,OAAO,MAAMC,mBAAmB,GAAG,CACjCV,WAAW,EACXC,mBAAmB,EACnBM,cAAc,EACdD,aAAa,EACbF,iBAAiB,EACjBC,eAAe,EACfG,eAAe,EACfC,eAAe,CAChB;AAWM,IAAME,oBAAoB,GAA1B,MAAMA,oBAAoB,GAChC;AADYA,oBAAoB,GAAAC,UAAA,EAThCd,QAAQ,CAAC;EACRe,YAAY,EAAE,CAACV,uBAAuB,CAAC;EACvCW,OAAO,EAAE,CACPf,YAAY,EACZG,eAAe,CAACa,QAAQ,EAAE,EAC1B,GAAGL,mBAAmB,CACvB;EACDM,OAAO,EAAE,CAACb,uBAAuB;CAClC,CAAC,C,EACWQ,oBAAoB,CAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}