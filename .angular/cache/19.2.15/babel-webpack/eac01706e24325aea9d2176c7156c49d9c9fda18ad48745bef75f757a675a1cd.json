{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiAutoselectComponent } from './swui-autoselect.component';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nexport const MOULES_AUTOSELECT = [MatAutocompleteModule, MatInputModule, MatButtonModule, ReactiveFormsModule];\nlet SwuiAutoselectModule = class SwuiAutoselectModule {};\nSwuiAutoselectModule = __decorate([NgModule({\n  declarations: [SwuiAutoselectComponent],\n  exports: [SwuiAutoselectComponent],\n  imports: [CommonModule, ...MOULES_AUTOSELECT]\n})], SwuiAutoselectModule);\nexport { SwuiAutoselectModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "ReactiveFormsModule", "SwuiAutoselectComponent", "MatAutocompleteModule", "MatInputModule", "MatButtonModule", "MOULES_AUTOSELECT", "SwuiAutoselectModule", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-autoselect/swui-autoselect.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiAutoselectComponent } from './swui-autoselect.component';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nexport const MOULES_AUTOSELECT = [\n    MatAutocompleteModule,\n    MatInputModule,\n    MatButtonModule,\n    ReactiveFormsModule,\n];\nlet SwuiAutoselectModule = class SwuiAutoselectModule {\n};\nSwuiAutoselectModule = __decorate([\n    NgModule({\n        declarations: [SwuiAutoselectComponent],\n        exports: [SwuiAutoselectComponent],\n        imports: [\n            CommonModule,\n            ...MOULES_AUTOSELECT,\n        ]\n    })\n], SwuiAutoselectModule);\nexport { SwuiAutoselectModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,iBAAiB,GAAG,CAC7BH,qBAAqB,EACrBC,cAAc,EACdC,eAAe,EACfJ,mBAAmB,CACtB;AACD,IAAIM,oBAAoB,GAAG,MAAMA,oBAAoB,CAAC,EACrD;AACDA,oBAAoB,GAAGT,UAAU,CAAC,CAC9BC,QAAQ,CAAC;EACLS,YAAY,EAAE,CAACN,uBAAuB,CAAC;EACvCO,OAAO,EAAE,CAACP,uBAAuB,CAAC;EAClCQ,OAAO,EAAE,CACLV,YAAY,EACZ,GAAGM,iBAAiB;AAE5B,CAAC,CAAC,CACL,EAAEC,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}