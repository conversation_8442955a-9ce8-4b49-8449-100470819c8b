{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesComponent } from './swui-control-messages.component';\nlet SwuiControlMessagesModule = class SwuiControlMessagesModule {};\nSwuiControlMessagesModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule],\n  declarations: [SwuiControlMessagesComponent],\n  exports: [SwuiControlMessagesComponent]\n})], SwuiControlMessagesModule);\nexport { SwuiControlMessagesModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "TranslateModule", "SwuiControlMessagesComponent", "SwuiControlMessagesModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-control-messages/swui-control-messages.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesComponent } from './swui-control-messages.component';\nlet SwuiControlMessagesModule = class SwuiControlMessagesModule {\n};\nSwuiControlMessagesModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule,\n        ],\n        declarations: [\n            SwuiControlMessagesComponent,\n        ],\n        exports: [\n            SwuiControlMessagesComponent\n        ]\n    })\n], SwuiControlMessagesModule);\nexport { SwuiControlMessagesModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,4BAA4B,QAAQ,mCAAmC;AAChF,IAAIC,yBAAyB,GAAG,MAAMA,yBAAyB,CAAC,EAC/D;AACDA,yBAAyB,GAAGL,UAAU,CAAC,CACnCC,QAAQ,CAAC;EACLK,OAAO,EAAE,CACLJ,YAAY,EACZC,eAAe,CAClB;EACDI,YAAY,EAAE,CACVH,4BAA4B,CAC/B;EACDI,OAAO,EAAE,CACLJ,4BAA4B;AAEpC,CAAC,CAAC,CACL,EAAEC,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}