{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatPseudoCheckboxModule;\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-CJ7seqQH.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nclass MatPseudoCheckboxModule {}\n_MatPseudoCheckboxModule = MatPseudoCheckboxModule;\n_defineProperty(MatPseudoCheckboxModule, \"\\u0275fac\", function _MatPseudoCheckboxModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatPseudoCheckboxModule)();\n});\n_defineProperty(MatPseudoCheckboxModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatPseudoCheckboxModule,\n  imports: [MatCommonModule, MatPseudoCheckbox],\n  exports: [MatPseudoCheckbox]\n}));\n_defineProperty(MatPseudoCheckboxModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatPseudoCheckbox],\n      exports: [MatPseudoCheckbox]\n    }]\n  }], null, null);\n})();\nexport { MatPseudoCheckboxModule as M };", "map": {"version": 3, "names": ["i0", "NgModule", "M", "MatPseudoCheckbox", "MatCommonModule", "MatPseudoCheckboxModule", "_MatPseudoCheckboxModule", "_defineProperty", "_MatPseudoCheckboxModule_Factory", "__ngFactoryType__", "ɵɵdefineNgModule", "type", "imports", "exports", "ɵɵdefineInjector", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/pseudo-checkbox-module-CAX2sutq.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-CJ7seqQH.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\n\nclass MatPseudoCheckboxModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPseudoCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPseudoCheckboxModule, imports: [MatCommonModule, MatPseudoCheckbox], exports: [MatPseudoCheckbox] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPseudoCheckboxModule, imports: [MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPseudoCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatPseudoCheckbox],\n                    exports: [MatPseudoCheckbox],\n                }]\n        }] });\n\nexport { MatPseudoCheckboxModule as M };\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,gCAAgC;AACvE,SAASD,CAAC,IAAIE,eAAe,QAAQ,8BAA8B;AAEnE,MAAMC,uBAAuB,CAAC;AAI7BC,wBAAA,GAJKD,uBAAuB;AAAAE,eAAA,CAAvBF,uBAAuB,wBAAAG,iCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAC0EJ,wBAAuB;AAAA;AAAAE,eAAA,CADxHF,uBAAuB,8BAKoDL,EAAE,CAAAU,gBAAA;EAAAC,IAAA,EAHqBN,wBAAuB;EAAAO,OAAA,GAAYR,eAAe,EAAED,iBAAiB;EAAAU,OAAA,GAAaV,iBAAiB;AAAA;AAAAI,eAAA,CAFrMF,uBAAuB,8BAKoDL,EAAE,CAAAc,gBAAA;EAAAF,OAAA,GAFwDR,eAAe;AAAA;AAE1J;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KAAiFf,EAAE,CAAAgB,iBAAA,CAAQX,uBAAuB,EAAc,CAAC;IACrHM,IAAI,EAAEV,QAAQ;IACdgB,IAAI,EAAE,CAAC;MACCL,OAAO,EAAE,CAACR,eAAe,EAAED,iBAAiB,CAAC;MAC7CU,OAAO,EAAE,CAACV,iBAAiB;IAC/B,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASE,uBAAuB,IAAIH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}