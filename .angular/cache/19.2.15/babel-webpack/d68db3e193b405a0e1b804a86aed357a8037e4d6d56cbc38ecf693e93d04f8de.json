{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _HttpClientTestingBackend, _HttpClientTestingModule;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, NgModule } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport { HttpHeaders, HttpResponse, HttpStatusCode, HttpErrorResponse, HttpEventType, HttpBackend, REQUESTS_CONTRIBUTE_TO_STABILITY, HttpClientModule } from '../module-z3bvLlVg.mjs';\nimport 'rxjs/operators';\nimport '../xhr-BfNfxNDv.mjs';\nimport '../dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Controller to be injected into tests, that allows for mocking and flushing\n * of requests.\n *\n * @publicApi\n */\nclass HttpTestingController {}\n\n/**\n * A mock requests that was received and is ready to be answered.\n *\n * This interface allows access to the underlying `HttpRequest`, and allows\n * responding with `HttpEvent`s or `HttpErrorResponse`s.\n *\n * @publicApi\n */\nclass TestRequest {\n  /**\n   * Whether the request was cancelled after it was sent.\n   */\n  get cancelled() {\n    return this._cancelled;\n  }\n  /**\n   * @internal set by `HttpClientTestingBackend`\n   */\n\n  constructor(request, observer) {\n    _defineProperty(this, \"request\", void 0);\n    _defineProperty(this, \"observer\", void 0);\n    _defineProperty(this, \"_cancelled\", false);\n    this.request = request;\n    this.observer = observer;\n  }\n  /**\n   * Resolve the request by returning a body plus additional HTTP information (such as response\n   * headers) if provided.\n   * If the request specifies an expected body type, the body is converted into the requested type.\n   * Otherwise, the body is converted to `JSON` by default.\n   *\n   * Both successful and unsuccessful responses can be delivered via `flush()`.\n   */\n  flush(body, opts = {}) {\n    if (this.cancelled) {\n      throw new Error(`Cannot flush a cancelled request.`);\n    }\n    const url = this.request.urlWithParams;\n    const headers = opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n    body = _maybeConvertBody(this.request.responseType, body);\n    let statusText = opts.statusText;\n    let status = opts.status !== undefined ? opts.status : HttpStatusCode.Ok;\n    if (opts.status === undefined) {\n      if (body === null) {\n        status = HttpStatusCode.NoContent;\n        statusText || (statusText = 'No Content');\n      } else {\n        statusText || (statusText = 'OK');\n      }\n    }\n    if (statusText === undefined) {\n      throw new Error('statusText is required when setting a custom status.');\n    }\n    if (status >= 200 && status < 300) {\n      this.observer.next(new HttpResponse({\n        body,\n        headers,\n        status,\n        statusText,\n        url\n      }));\n      this.observer.complete();\n    } else {\n      this.observer.error(new HttpErrorResponse({\n        error: body,\n        headers,\n        status,\n        statusText,\n        url\n      }));\n    }\n  }\n  error(error, opts = {}) {\n    if (this.cancelled) {\n      throw new Error(`Cannot return an error for a cancelled request.`);\n    }\n    if (opts.status && opts.status >= 200 && opts.status < 300) {\n      throw new Error(`error() called with a successful status.`);\n    }\n    const headers = opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n    this.observer.error(new HttpErrorResponse({\n      error,\n      headers,\n      status: opts.status || 0,\n      statusText: opts.statusText || '',\n      url: this.request.urlWithParams\n    }));\n  }\n  /**\n   * Deliver an arbitrary `HttpEvent` (such as a progress event) on the response stream for this\n   * request.\n   */\n  event(event) {\n    if (this.cancelled) {\n      throw new Error(`Cannot send events to a cancelled request.`);\n    }\n    this.observer.next(event);\n  }\n}\n/**\n * Helper function to convert a response body to an ArrayBuffer.\n */\nfunction _toArrayBufferBody(body) {\n  if (typeof ArrayBuffer === 'undefined') {\n    throw new Error('ArrayBuffer responses are not supported on this platform.');\n  }\n  if (body instanceof ArrayBuffer) {\n    return body;\n  }\n  throw new Error('Automatic conversion to ArrayBuffer is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to a Blob.\n */\nfunction _toBlob(body) {\n  if (typeof Blob === 'undefined') {\n    throw new Error('Blob responses are not supported on this platform.');\n  }\n  if (body instanceof Blob) {\n    return body;\n  }\n  if (ArrayBuffer && body instanceof ArrayBuffer) {\n    return new Blob([body]);\n  }\n  throw new Error('Automatic conversion to Blob is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to JSON data.\n */\nfunction _toJsonBody(body, format = 'JSON') {\n  if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n    throw new Error(`Automatic conversion to ${format} is not supported for ArrayBuffers.`);\n  }\n  if (typeof Blob !== 'undefined' && body instanceof Blob) {\n    throw new Error(`Automatic conversion to ${format} is not supported for Blobs.`);\n  }\n  if (typeof body === 'string' || typeof body === 'number' || typeof body === 'object' || typeof body === 'boolean' || Array.isArray(body)) {\n    return body;\n  }\n  throw new Error(`Automatic conversion to ${format} is not supported for response type.`);\n}\n/**\n * Helper function to convert a response body to a string.\n */\nfunction _toTextBody(body) {\n  if (typeof body === 'string') {\n    return body;\n  }\n  if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n    throw new Error('Automatic conversion to text is not supported for ArrayBuffers.');\n  }\n  if (typeof Blob !== 'undefined' && body instanceof Blob) {\n    throw new Error('Automatic conversion to text is not supported for Blobs.');\n  }\n  return JSON.stringify(_toJsonBody(body, 'text'));\n}\n/**\n * Convert a response body to the requested type.\n */\nfunction _maybeConvertBody(responseType, body) {\n  if (body === null) {\n    return null;\n  }\n  switch (responseType) {\n    case 'arraybuffer':\n      return _toArrayBufferBody(body);\n    case 'blob':\n      return _toBlob(body);\n    case 'json':\n      return _toJsonBody(body);\n    case 'text':\n      return _toTextBody(body);\n    default:\n      throw new Error(`Unsupported responseType: ${responseType}`);\n  }\n}\n\n/**\n * A testing backend for `HttpClient` which both acts as an `HttpBackend`\n * and as the `HttpTestingController`.\n *\n * `HttpClientTestingBackend` works by keeping a list of all open requests.\n * As requests come in, they're added to the list. Users can assert that specific\n * requests were made and then flush them. In the end, a verify() method asserts\n * that no unexpected requests were made.\n *\n *\n */\nclass HttpClientTestingBackend {\n  constructor() {\n    /**\n     * List of pending requests which have not yet been expected.\n     */\n    _defineProperty(this, \"open\", []);\n    /**\n     * Used when checking if we need to throw the NOT_USING_FETCH_BACKEND_IN_SSR error\n     */\n    _defineProperty(this, \"isTestingBackend\", true);\n  }\n  /**\n   * Handle an incoming request by queueing it in the list of open requests.\n   */\n  handle(req) {\n    return new Observable(observer => {\n      const testReq = new TestRequest(req, observer);\n      this.open.push(testReq);\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      return () => {\n        testReq._cancelled = true;\n      };\n    });\n  }\n  /**\n   * Helper function to search for requests in the list of open requests.\n   */\n  _match(match) {\n    if (typeof match === 'string') {\n      return this.open.filter(testReq => testReq.request.urlWithParams === match);\n    } else if (typeof match === 'function') {\n      return this.open.filter(testReq => match(testReq.request));\n    } else {\n      return this.open.filter(testReq => (!match.method || testReq.request.method === match.method.toUpperCase()) && (!match.url || testReq.request.urlWithParams === match.url));\n    }\n  }\n  /**\n   * Search for requests in the list of open requests, and return all that match\n   * without asserting anything about the number of matches.\n   */\n  match(match) {\n    const results = this._match(match);\n    results.forEach(result => {\n      const index = this.open.indexOf(result);\n      if (index !== -1) {\n        this.open.splice(index, 1);\n      }\n    });\n    return results;\n  }\n  /**\n   * Expect that a single outstanding request matches the given matcher, and return\n   * it.\n   *\n   * Requests returned through this API will no longer be in the list of open requests,\n   * and thus will not match twice.\n   */\n  expectOne(match, description) {\n    description || (description = this.descriptionFromMatcher(match));\n    const matches = this.match(match);\n    if (matches.length > 1) {\n      throw new Error(`Expected one matching request for criteria \"${description}\", found ${matches.length} requests.`);\n    }\n    if (matches.length === 0) {\n      let message = `Expected one matching request for criteria \"${description}\", found none.`;\n      if (this.open.length > 0) {\n        // Show the methods and URLs of open requests in the error, for convenience.\n        const requests = this.open.map(describeRequest).join(', ');\n        message += ` Requests received are: ${requests}.`;\n      }\n      throw new Error(message);\n    }\n    return matches[0];\n  }\n  /**\n   * Expect that no outstanding requests match the given matcher, and throw an error\n   * if any do.\n   */\n  expectNone(match, description) {\n    description || (description = this.descriptionFromMatcher(match));\n    const matches = this.match(match);\n    if (matches.length > 0) {\n      throw new Error(`Expected zero matching requests for criteria \"${description}\", found ${matches.length}.`);\n    }\n  }\n  /**\n   * Validate that there are no outstanding requests.\n   */\n  verify(opts = {}) {\n    let open = this.open;\n    // It's possible that some requests may be cancelled, and this is expected.\n    // The user can ask to ignore open requests which have been cancelled.\n    if (opts.ignoreCancelled) {\n      open = open.filter(testReq => !testReq.cancelled);\n    }\n    if (open.length > 0) {\n      // Show the methods and URLs of open requests in the error, for convenience.\n      const requests = open.map(describeRequest).join(', ');\n      throw new Error(`Expected no open requests, found ${open.length}: ${requests}`);\n    }\n  }\n  descriptionFromMatcher(matcher) {\n    if (typeof matcher === 'string') {\n      return `Match URL: ${matcher}`;\n    } else if (typeof matcher === 'object') {\n      const method = matcher.method || '(any)';\n      const url = matcher.url || '(any)';\n      return `Match method: ${method}, URL: ${url}`;\n    } else {\n      return `Match by function: ${matcher.name}`;\n    }\n  }\n}\n_HttpClientTestingBackend = HttpClientTestingBackend;\n_defineProperty(HttpClientTestingBackend, \"\\u0275fac\", function _HttpClientTestingBackend_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HttpClientTestingBackend)();\n});\n_defineProperty(HttpClientTestingBackend, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _HttpClientTestingBackend,\n  factory: _HttpClientTestingBackend.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientTestingBackend, [{\n    type: Injectable\n  }], null, null);\n})();\nfunction describeRequest(testRequest) {\n  const url = testRequest.request.urlWithParams;\n  const method = testRequest.request.method;\n  return `${method} ${url}`;\n}\nfunction provideHttpClientTesting() {\n  return [HttpClientTestingBackend, {\n    provide: HttpBackend,\n    useExisting: HttpClientTestingBackend\n  }, {\n    provide: HttpTestingController,\n    useExisting: HttpClientTestingBackend\n  }, {\n    provide: REQUESTS_CONTRIBUTE_TO_STABILITY,\n    useValue: false\n  }];\n}\n\n/**\n * Configures `HttpClientTestingBackend` as the `HttpBackend` used by `HttpClient`.\n *\n * Inject `HttpTestingController` to expect and flush requests in your tests.\n *\n * @publicApi\n *\n * @deprecated Add `provideHttpClientTesting()` to your providers instead.\n */\nclass HttpClientTestingModule {}\n_HttpClientTestingModule = HttpClientTestingModule;\n_defineProperty(HttpClientTestingModule, \"\\u0275fac\", function _HttpClientTestingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HttpClientTestingModule)();\n});\n_defineProperty(HttpClientTestingModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _HttpClientTestingModule,\n  imports: [HttpClientModule]\n}));\n_defineProperty(HttpClientTestingModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [provideHttpClientTesting()],\n  imports: [HttpClientModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientTestingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [HttpClientModule],\n      providers: [provideHttpClientTesting()]\n    }]\n  }], null, null);\n})();\nexport { HttpClientTestingModule, HttpTestingController, TestRequest, provideHttpClientTesting };", "map": {"version": 3, "names": ["i0", "Injectable", "NgModule", "Observable", "HttpHeaders", "HttpResponse", "HttpStatusCode", "HttpErrorResponse", "HttpEventType", "HttpBackend", "REQUESTS_CONTRIBUTE_TO_STABILITY", "HttpClientModule", "HttpTestingController", "TestRequest", "cancelled", "_cancelled", "constructor", "request", "observer", "_defineProperty", "flush", "body", "opts", "Error", "url", "urlWithParams", "headers", "_maybeConvertBody", "responseType", "statusText", "status", "undefined", "Ok", "NoContent", "next", "complete", "error", "event", "_toArrayBufferBody", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_toBlob", "Blob", "_toJsonBody", "format", "Array", "isArray", "_toTextBody", "JSON", "stringify", "HttpClientTestingBackend", "handle", "req", "testReq", "open", "push", "type", "<PERSON><PERSON>", "_match", "match", "filter", "method", "toUpperCase", "results", "for<PERSON>ach", "result", "index", "indexOf", "splice", "expectOne", "description", "descriptionFromMatcher", "matches", "length", "message", "requests", "map", "describeRequest", "join", "expectNone", "verify", "ignoreCancelled", "matcher", "name", "_HttpClientTestingBackend", "_HttpClientTestingBackend_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "ngDevMode", "ɵsetClassMetadata", "testRequest", "provideHttpClientTesting", "provide", "useExisting", "useValue", "HttpClientTestingModule", "_HttpClientTestingModule", "_HttpClientTestingModule_Factory", "ɵɵdefineNgModule", "imports", "ɵɵdefineInjector", "providers", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/common/fesm2022/http/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, NgModule } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport { HttpHeaders, HttpResponse, HttpStatusCode, HttpErrorResponse, HttpEventType, HttpBackend, REQUESTS_CONTRIBUTE_TO_STABILITY, HttpClientModule } from '../module-z3bvLlVg.mjs';\nimport 'rxjs/operators';\nimport '../xhr-BfNfxNDv.mjs';\nimport '../dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Controller to be injected into tests, that allows for mocking and flushing\n * of requests.\n *\n * @publicApi\n */\nclass HttpTestingController {\n}\n\n/**\n * A mock requests that was received and is ready to be answered.\n *\n * This interface allows access to the underlying `HttpRequest`, and allows\n * responding with `HttpEvent`s or `HttpErrorResponse`s.\n *\n * @publicApi\n */\nclass TestRequest {\n    request;\n    observer;\n    /**\n     * Whether the request was cancelled after it was sent.\n     */\n    get cancelled() {\n        return this._cancelled;\n    }\n    /**\n     * @internal set by `HttpClientTestingBackend`\n     */\n    _cancelled = false;\n    constructor(request, observer) {\n        this.request = request;\n        this.observer = observer;\n    }\n    /**\n     * Resolve the request by returning a body plus additional HTTP information (such as response\n     * headers) if provided.\n     * If the request specifies an expected body type, the body is converted into the requested type.\n     * Otherwise, the body is converted to `JSON` by default.\n     *\n     * Both successful and unsuccessful responses can be delivered via `flush()`.\n     */\n    flush(body, opts = {}) {\n        if (this.cancelled) {\n            throw new Error(`Cannot flush a cancelled request.`);\n        }\n        const url = this.request.urlWithParams;\n        const headers = opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n        body = _maybeConvertBody(this.request.responseType, body);\n        let statusText = opts.statusText;\n        let status = opts.status !== undefined ? opts.status : HttpStatusCode.Ok;\n        if (opts.status === undefined) {\n            if (body === null) {\n                status = HttpStatusCode.NoContent;\n                statusText ||= 'No Content';\n            }\n            else {\n                statusText ||= 'OK';\n            }\n        }\n        if (statusText === undefined) {\n            throw new Error('statusText is required when setting a custom status.');\n        }\n        if (status >= 200 && status < 300) {\n            this.observer.next(new HttpResponse({ body, headers, status, statusText, url }));\n            this.observer.complete();\n        }\n        else {\n            this.observer.error(new HttpErrorResponse({ error: body, headers, status, statusText, url }));\n        }\n    }\n    error(error, opts = {}) {\n        if (this.cancelled) {\n            throw new Error(`Cannot return an error for a cancelled request.`);\n        }\n        if (opts.status && opts.status >= 200 && opts.status < 300) {\n            throw new Error(`error() called with a successful status.`);\n        }\n        const headers = opts.headers instanceof HttpHeaders ? opts.headers : new HttpHeaders(opts.headers);\n        this.observer.error(new HttpErrorResponse({\n            error,\n            headers,\n            status: opts.status || 0,\n            statusText: opts.statusText || '',\n            url: this.request.urlWithParams,\n        }));\n    }\n    /**\n     * Deliver an arbitrary `HttpEvent` (such as a progress event) on the response stream for this\n     * request.\n     */\n    event(event) {\n        if (this.cancelled) {\n            throw new Error(`Cannot send events to a cancelled request.`);\n        }\n        this.observer.next(event);\n    }\n}\n/**\n * Helper function to convert a response body to an ArrayBuffer.\n */\nfunction _toArrayBufferBody(body) {\n    if (typeof ArrayBuffer === 'undefined') {\n        throw new Error('ArrayBuffer responses are not supported on this platform.');\n    }\n    if (body instanceof ArrayBuffer) {\n        return body;\n    }\n    throw new Error('Automatic conversion to ArrayBuffer is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to a Blob.\n */\nfunction _toBlob(body) {\n    if (typeof Blob === 'undefined') {\n        throw new Error('Blob responses are not supported on this platform.');\n    }\n    if (body instanceof Blob) {\n        return body;\n    }\n    if (ArrayBuffer && body instanceof ArrayBuffer) {\n        return new Blob([body]);\n    }\n    throw new Error('Automatic conversion to Blob is not supported for response type.');\n}\n/**\n * Helper function to convert a response body to JSON data.\n */\nfunction _toJsonBody(body, format = 'JSON') {\n    if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n        throw new Error(`Automatic conversion to ${format} is not supported for ArrayBuffers.`);\n    }\n    if (typeof Blob !== 'undefined' && body instanceof Blob) {\n        throw new Error(`Automatic conversion to ${format} is not supported for Blobs.`);\n    }\n    if (typeof body === 'string' ||\n        typeof body === 'number' ||\n        typeof body === 'object' ||\n        typeof body === 'boolean' ||\n        Array.isArray(body)) {\n        return body;\n    }\n    throw new Error(`Automatic conversion to ${format} is not supported for response type.`);\n}\n/**\n * Helper function to convert a response body to a string.\n */\nfunction _toTextBody(body) {\n    if (typeof body === 'string') {\n        return body;\n    }\n    if (typeof ArrayBuffer !== 'undefined' && body instanceof ArrayBuffer) {\n        throw new Error('Automatic conversion to text is not supported for ArrayBuffers.');\n    }\n    if (typeof Blob !== 'undefined' && body instanceof Blob) {\n        throw new Error('Automatic conversion to text is not supported for Blobs.');\n    }\n    return JSON.stringify(_toJsonBody(body, 'text'));\n}\n/**\n * Convert a response body to the requested type.\n */\nfunction _maybeConvertBody(responseType, body) {\n    if (body === null) {\n        return null;\n    }\n    switch (responseType) {\n        case 'arraybuffer':\n            return _toArrayBufferBody(body);\n        case 'blob':\n            return _toBlob(body);\n        case 'json':\n            return _toJsonBody(body);\n        case 'text':\n            return _toTextBody(body);\n        default:\n            throw new Error(`Unsupported responseType: ${responseType}`);\n    }\n}\n\n/**\n * A testing backend for `HttpClient` which both acts as an `HttpBackend`\n * and as the `HttpTestingController`.\n *\n * `HttpClientTestingBackend` works by keeping a list of all open requests.\n * As requests come in, they're added to the list. Users can assert that specific\n * requests were made and then flush them. In the end, a verify() method asserts\n * that no unexpected requests were made.\n *\n *\n */\nclass HttpClientTestingBackend {\n    /**\n     * List of pending requests which have not yet been expected.\n     */\n    open = [];\n    /**\n     * Used when checking if we need to throw the NOT_USING_FETCH_BACKEND_IN_SSR error\n     */\n    isTestingBackend = true;\n    /**\n     * Handle an incoming request by queueing it in the list of open requests.\n     */\n    handle(req) {\n        return new Observable((observer) => {\n            const testReq = new TestRequest(req, observer);\n            this.open.push(testReq);\n            observer.next({ type: HttpEventType.Sent });\n            return () => {\n                testReq._cancelled = true;\n            };\n        });\n    }\n    /**\n     * Helper function to search for requests in the list of open requests.\n     */\n    _match(match) {\n        if (typeof match === 'string') {\n            return this.open.filter((testReq) => testReq.request.urlWithParams === match);\n        }\n        else if (typeof match === 'function') {\n            return this.open.filter((testReq) => match(testReq.request));\n        }\n        else {\n            return this.open.filter((testReq) => (!match.method || testReq.request.method === match.method.toUpperCase()) &&\n                (!match.url || testReq.request.urlWithParams === match.url));\n        }\n    }\n    /**\n     * Search for requests in the list of open requests, and return all that match\n     * without asserting anything about the number of matches.\n     */\n    match(match) {\n        const results = this._match(match);\n        results.forEach((result) => {\n            const index = this.open.indexOf(result);\n            if (index !== -1) {\n                this.open.splice(index, 1);\n            }\n        });\n        return results;\n    }\n    /**\n     * Expect that a single outstanding request matches the given matcher, and return\n     * it.\n     *\n     * Requests returned through this API will no longer be in the list of open requests,\n     * and thus will not match twice.\n     */\n    expectOne(match, description) {\n        description ||= this.descriptionFromMatcher(match);\n        const matches = this.match(match);\n        if (matches.length > 1) {\n            throw new Error(`Expected one matching request for criteria \"${description}\", found ${matches.length} requests.`);\n        }\n        if (matches.length === 0) {\n            let message = `Expected one matching request for criteria \"${description}\", found none.`;\n            if (this.open.length > 0) {\n                // Show the methods and URLs of open requests in the error, for convenience.\n                const requests = this.open.map(describeRequest).join(', ');\n                message += ` Requests received are: ${requests}.`;\n            }\n            throw new Error(message);\n        }\n        return matches[0];\n    }\n    /**\n     * Expect that no outstanding requests match the given matcher, and throw an error\n     * if any do.\n     */\n    expectNone(match, description) {\n        description ||= this.descriptionFromMatcher(match);\n        const matches = this.match(match);\n        if (matches.length > 0) {\n            throw new Error(`Expected zero matching requests for criteria \"${description}\", found ${matches.length}.`);\n        }\n    }\n    /**\n     * Validate that there are no outstanding requests.\n     */\n    verify(opts = {}) {\n        let open = this.open;\n        // It's possible that some requests may be cancelled, and this is expected.\n        // The user can ask to ignore open requests which have been cancelled.\n        if (opts.ignoreCancelled) {\n            open = open.filter((testReq) => !testReq.cancelled);\n        }\n        if (open.length > 0) {\n            // Show the methods and URLs of open requests in the error, for convenience.\n            const requests = open.map(describeRequest).join(', ');\n            throw new Error(`Expected no open requests, found ${open.length}: ${requests}`);\n        }\n    }\n    descriptionFromMatcher(matcher) {\n        if (typeof matcher === 'string') {\n            return `Match URL: ${matcher}`;\n        }\n        else if (typeof matcher === 'object') {\n            const method = matcher.method || '(any)';\n            const url = matcher.url || '(any)';\n            return `Match method: ${method}, URL: ${url}`;\n        }\n        else {\n            return `Match by function: ${matcher.name}`;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingBackend, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingBackend });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingBackend, decorators: [{\n            type: Injectable\n        }] });\nfunction describeRequest(testRequest) {\n    const url = testRequest.request.urlWithParams;\n    const method = testRequest.request.method;\n    return `${method} ${url}`;\n}\n\nfunction provideHttpClientTesting() {\n    return [\n        HttpClientTestingBackend,\n        { provide: HttpBackend, useExisting: HttpClientTestingBackend },\n        { provide: HttpTestingController, useExisting: HttpClientTestingBackend },\n        { provide: REQUESTS_CONTRIBUTE_TO_STABILITY, useValue: false },\n    ];\n}\n\n/**\n * Configures `HttpClientTestingBackend` as the `HttpBackend` used by `HttpClient`.\n *\n * Inject `HttpTestingController` to expect and flush requests in your tests.\n *\n * @publicApi\n *\n * @deprecated Add `provideHttpClientTesting()` to your providers instead.\n */\nclass HttpClientTestingModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingModule, imports: [HttpClientModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingModule, providers: [provideHttpClientTesting()], imports: [HttpClientModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [HttpClientModule],\n                    providers: [provideHttpClientTesting()],\n                }]\n        }] });\n\nexport { HttpClientTestingModule, HttpTestingController, TestRequest, provideHttpClientTesting };\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AACpD,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,WAAW,EAAEC,gCAAgC,EAAEC,gBAAgB,QAAQ,wBAAwB;AACrL,OAAO,gBAAgB;AACvB,OAAO,qBAAqB;AAC5B,OAAO,4BAA4B;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;;AAG5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EAGd;AACJ;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA;AACJ;AACA;;EAEIC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA,qBADlB,KAAK;IAEd,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,KAAKA,CAACC,IAAI,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,IAAI,IAAI,CAACR,SAAS,EAAE;MAChB,MAAM,IAAIS,KAAK,CAAC,mCAAmC,CAAC;IACxD;IACA,MAAMC,GAAG,GAAG,IAAI,CAACP,OAAO,CAACQ,aAAa;IACtC,MAAMC,OAAO,GAAGJ,IAAI,CAACI,OAAO,YAAYtB,WAAW,GAAGkB,IAAI,CAACI,OAAO,GAAG,IAAItB,WAAW,CAACkB,IAAI,CAACI,OAAO,CAAC;IAClGL,IAAI,GAAGM,iBAAiB,CAAC,IAAI,CAACV,OAAO,CAACW,YAAY,EAAEP,IAAI,CAAC;IACzD,IAAIQ,UAAU,GAAGP,IAAI,CAACO,UAAU;IAChC,IAAIC,MAAM,GAAGR,IAAI,CAACQ,MAAM,KAAKC,SAAS,GAAGT,IAAI,CAACQ,MAAM,GAAGxB,cAAc,CAAC0B,EAAE;IACxE,IAAIV,IAAI,CAACQ,MAAM,KAAKC,SAAS,EAAE;MAC3B,IAAIV,IAAI,KAAK,IAAI,EAAE;QACfS,MAAM,GAAGxB,cAAc,CAAC2B,SAAS;QACjCJ,UAAU,KAAVA,UAAU,GAAK,YAAY;MAC/B,CAAC,MACI;QACDA,UAAU,KAAVA,UAAU,GAAK,IAAI;MACvB;IACJ;IACA,IAAIA,UAAU,KAAKE,SAAS,EAAE;MAC1B,MAAM,IAAIR,KAAK,CAAC,sDAAsD,CAAC;IAC3E;IACA,IAAIO,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,EAAE;MAC/B,IAAI,CAACZ,QAAQ,CAACgB,IAAI,CAAC,IAAI7B,YAAY,CAAC;QAAEgB,IAAI;QAAEK,OAAO;QAAEI,MAAM;QAAED,UAAU;QAAEL;MAAI,CAAC,CAAC,CAAC;MAChF,IAAI,CAACN,QAAQ,CAACiB,QAAQ,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,IAAI,CAACjB,QAAQ,CAACkB,KAAK,CAAC,IAAI7B,iBAAiB,CAAC;QAAE6B,KAAK,EAAEf,IAAI;QAAEK,OAAO;QAAEI,MAAM;QAAED,UAAU;QAAEL;MAAI,CAAC,CAAC,CAAC;IACjG;EACJ;EACAY,KAAKA,CAACA,KAAK,EAAEd,IAAI,GAAG,CAAC,CAAC,EAAE;IACpB,IAAI,IAAI,CAACR,SAAS,EAAE;MAChB,MAAM,IAAIS,KAAK,CAAC,iDAAiD,CAAC;IACtE;IACA,IAAID,IAAI,CAACQ,MAAM,IAAIR,IAAI,CAACQ,MAAM,IAAI,GAAG,IAAIR,IAAI,CAACQ,MAAM,GAAG,GAAG,EAAE;MACxD,MAAM,IAAIP,KAAK,CAAC,0CAA0C,CAAC;IAC/D;IACA,MAAMG,OAAO,GAAGJ,IAAI,CAACI,OAAO,YAAYtB,WAAW,GAAGkB,IAAI,CAACI,OAAO,GAAG,IAAItB,WAAW,CAACkB,IAAI,CAACI,OAAO,CAAC;IAClG,IAAI,CAACR,QAAQ,CAACkB,KAAK,CAAC,IAAI7B,iBAAiB,CAAC;MACtC6B,KAAK;MACLV,OAAO;MACPI,MAAM,EAAER,IAAI,CAACQ,MAAM,IAAI,CAAC;MACxBD,UAAU,EAAEP,IAAI,CAACO,UAAU,IAAI,EAAE;MACjCL,GAAG,EAAE,IAAI,CAACP,OAAO,CAACQ;IACtB,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;EACIY,KAAKA,CAACA,KAAK,EAAE;IACT,IAAI,IAAI,CAACvB,SAAS,EAAE;MAChB,MAAM,IAAIS,KAAK,CAAC,4CAA4C,CAAC;IACjE;IACA,IAAI,CAACL,QAAQ,CAACgB,IAAI,CAACG,KAAK,CAAC;EAC7B;AACJ;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACjB,IAAI,EAAE;EAC9B,IAAI,OAAOkB,WAAW,KAAK,WAAW,EAAE;IACpC,MAAM,IAAIhB,KAAK,CAAC,2DAA2D,CAAC;EAChF;EACA,IAAIF,IAAI,YAAYkB,WAAW,EAAE;IAC7B,OAAOlB,IAAI;EACf;EACA,MAAM,IAAIE,KAAK,CAAC,yEAAyE,CAAC;AAC9F;AACA;AACA;AACA;AACA,SAASiB,OAAOA,CAACnB,IAAI,EAAE;EACnB,IAAI,OAAOoB,IAAI,KAAK,WAAW,EAAE;IAC7B,MAAM,IAAIlB,KAAK,CAAC,oDAAoD,CAAC;EACzE;EACA,IAAIF,IAAI,YAAYoB,IAAI,EAAE;IACtB,OAAOpB,IAAI;EACf;EACA,IAAIkB,WAAW,IAAIlB,IAAI,YAAYkB,WAAW,EAAE;IAC5C,OAAO,IAAIE,IAAI,CAAC,CAACpB,IAAI,CAAC,CAAC;EAC3B;EACA,MAAM,IAAIE,KAAK,CAAC,kEAAkE,CAAC;AACvF;AACA;AACA;AACA;AACA,SAASmB,WAAWA,CAACrB,IAAI,EAAEsB,MAAM,GAAG,MAAM,EAAE;EACxC,IAAI,OAAOJ,WAAW,KAAK,WAAW,IAAIlB,IAAI,YAAYkB,WAAW,EAAE;IACnE,MAAM,IAAIhB,KAAK,CAAC,2BAA2BoB,MAAM,qCAAqC,CAAC;EAC3F;EACA,IAAI,OAAOF,IAAI,KAAK,WAAW,IAAIpB,IAAI,YAAYoB,IAAI,EAAE;IACrD,MAAM,IAAIlB,KAAK,CAAC,2BAA2BoB,MAAM,8BAA8B,CAAC;EACpF;EACA,IAAI,OAAOtB,IAAI,KAAK,QAAQ,IACxB,OAAOA,IAAI,KAAK,QAAQ,IACxB,OAAOA,IAAI,KAAK,QAAQ,IACxB,OAAOA,IAAI,KAAK,SAAS,IACzBuB,KAAK,CAACC,OAAO,CAACxB,IAAI,CAAC,EAAE;IACrB,OAAOA,IAAI;EACf;EACA,MAAM,IAAIE,KAAK,CAAC,2BAA2BoB,MAAM,sCAAsC,CAAC;AAC5F;AACA;AACA;AACA;AACA,SAASG,WAAWA,CAACzB,IAAI,EAAE;EACvB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI;EACf;EACA,IAAI,OAAOkB,WAAW,KAAK,WAAW,IAAIlB,IAAI,YAAYkB,WAAW,EAAE;IACnE,MAAM,IAAIhB,KAAK,CAAC,iEAAiE,CAAC;EACtF;EACA,IAAI,OAAOkB,IAAI,KAAK,WAAW,IAAIpB,IAAI,YAAYoB,IAAI,EAAE;IACrD,MAAM,IAAIlB,KAAK,CAAC,0DAA0D,CAAC;EAC/E;EACA,OAAOwB,IAAI,CAACC,SAAS,CAACN,WAAW,CAACrB,IAAI,EAAE,MAAM,CAAC,CAAC;AACpD;AACA;AACA;AACA;AACA,SAASM,iBAAiBA,CAACC,YAAY,EAAEP,IAAI,EAAE;EAC3C,IAAIA,IAAI,KAAK,IAAI,EAAE;IACf,OAAO,IAAI;EACf;EACA,QAAQO,YAAY;IAChB,KAAK,aAAa;MACd,OAAOU,kBAAkB,CAACjB,IAAI,CAAC;IACnC,KAAK,MAAM;MACP,OAAOmB,OAAO,CAACnB,IAAI,CAAC;IACxB,KAAK,MAAM;MACP,OAAOqB,WAAW,CAACrB,IAAI,CAAC;IAC5B,KAAK,MAAM;MACP,OAAOyB,WAAW,CAACzB,IAAI,CAAC;IAC5B;MACI,MAAM,IAAIE,KAAK,CAAC,6BAA6BK,YAAY,EAAE,CAAC;EACpE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,wBAAwB,CAAC;EAAAjC,YAAA;IAC3B;AACJ;AACA;IAFIG,eAAA,eAGO,EAAE;IACT;AACJ;AACA;IAFIA,eAAA,2BAGmB,IAAI;EAAA;EACvB;AACJ;AACA;EACI+B,MAAMA,CAACC,GAAG,EAAE;IACR,OAAO,IAAIhD,UAAU,CAAEe,QAAQ,IAAK;MAChC,MAAMkC,OAAO,GAAG,IAAIvC,WAAW,CAACsC,GAAG,EAAEjC,QAAQ,CAAC;MAC9C,IAAI,CAACmC,IAAI,CAACC,IAAI,CAACF,OAAO,CAAC;MACvBlC,QAAQ,CAACgB,IAAI,CAAC;QAAEqB,IAAI,EAAE/C,aAAa,CAACgD;MAAK,CAAC,CAAC;MAC3C,OAAO,MAAM;QACTJ,OAAO,CAACrC,UAAU,GAAG,IAAI;MAC7B,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACI0C,MAAMA,CAACC,KAAK,EAAE;IACV,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAO,IAAI,CAACL,IAAI,CAACM,MAAM,CAAEP,OAAO,IAAKA,OAAO,CAACnC,OAAO,CAACQ,aAAa,KAAKiC,KAAK,CAAC;IACjF,CAAC,MACI,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAClC,OAAO,IAAI,CAACL,IAAI,CAACM,MAAM,CAAEP,OAAO,IAAKM,KAAK,CAACN,OAAO,CAACnC,OAAO,CAAC,CAAC;IAChE,CAAC,MACI;MACD,OAAO,IAAI,CAACoC,IAAI,CAACM,MAAM,CAAEP,OAAO,IAAK,CAAC,CAACM,KAAK,CAACE,MAAM,IAAIR,OAAO,CAACnC,OAAO,CAAC2C,MAAM,KAAKF,KAAK,CAACE,MAAM,CAACC,WAAW,CAAC,CAAC,MACvG,CAACH,KAAK,CAAClC,GAAG,IAAI4B,OAAO,CAACnC,OAAO,CAACQ,aAAa,KAAKiC,KAAK,CAAClC,GAAG,CAAC,CAAC;IACpE;EACJ;EACA;AACJ;AACA;AACA;EACIkC,KAAKA,CAACA,KAAK,EAAE;IACT,MAAMI,OAAO,GAAG,IAAI,CAACL,MAAM,CAACC,KAAK,CAAC;IAClCI,OAAO,CAACC,OAAO,CAAEC,MAAM,IAAK;MACxB,MAAMC,KAAK,GAAG,IAAI,CAACZ,IAAI,CAACa,OAAO,CAACF,MAAM,CAAC;MACvC,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;QACd,IAAI,CAACZ,IAAI,CAACc,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC9B;IACJ,CAAC,CAAC;IACF,OAAOH,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,SAASA,CAACV,KAAK,EAAEW,WAAW,EAAE;IAC1BA,WAAW,KAAXA,WAAW,GAAK,IAAI,CAACC,sBAAsB,CAACZ,KAAK,CAAC;IAClD,MAAMa,OAAO,GAAG,IAAI,CAACb,KAAK,CAACA,KAAK,CAAC;IACjC,IAAIa,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM,IAAIjD,KAAK,CAAC,+CAA+C8C,WAAW,YAAYE,OAAO,CAACC,MAAM,YAAY,CAAC;IACrH;IACA,IAAID,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MACtB,IAAIC,OAAO,GAAG,+CAA+CJ,WAAW,gBAAgB;MACxF,IAAI,IAAI,CAAChB,IAAI,CAACmB,MAAM,GAAG,CAAC,EAAE;QACtB;QACA,MAAME,QAAQ,GAAG,IAAI,CAACrB,IAAI,CAACsB,GAAG,CAACC,eAAe,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAC1DJ,OAAO,IAAI,2BAA2BC,QAAQ,GAAG;MACrD;MACA,MAAM,IAAInD,KAAK,CAACkD,OAAO,CAAC;IAC5B;IACA,OAAOF,OAAO,CAAC,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;EACIO,UAAUA,CAACpB,KAAK,EAAEW,WAAW,EAAE;IAC3BA,WAAW,KAAXA,WAAW,GAAK,IAAI,CAACC,sBAAsB,CAACZ,KAAK,CAAC;IAClD,MAAMa,OAAO,GAAG,IAAI,CAACb,KAAK,CAACA,KAAK,CAAC;IACjC,IAAIa,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM,IAAIjD,KAAK,CAAC,iDAAiD8C,WAAW,YAAYE,OAAO,CAACC,MAAM,GAAG,CAAC;IAC9G;EACJ;EACA;AACJ;AACA;EACIO,MAAMA,CAACzD,IAAI,GAAG,CAAC,CAAC,EAAE;IACd,IAAI+B,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB;IACA;IACA,IAAI/B,IAAI,CAAC0D,eAAe,EAAE;MACtB3B,IAAI,GAAGA,IAAI,CAACM,MAAM,CAAEP,OAAO,IAAK,CAACA,OAAO,CAACtC,SAAS,CAAC;IACvD;IACA,IAAIuC,IAAI,CAACmB,MAAM,GAAG,CAAC,EAAE;MACjB;MACA,MAAME,QAAQ,GAAGrB,IAAI,CAACsB,GAAG,CAACC,eAAe,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACrD,MAAM,IAAItD,KAAK,CAAC,oCAAoC8B,IAAI,CAACmB,MAAM,KAAKE,QAAQ,EAAE,CAAC;IACnF;EACJ;EACAJ,sBAAsBA,CAACW,OAAO,EAAE;IAC5B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC7B,OAAO,cAAcA,OAAO,EAAE;IAClC,CAAC,MACI,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAClC,MAAMrB,MAAM,GAAGqB,OAAO,CAACrB,MAAM,IAAI,OAAO;MACxC,MAAMpC,GAAG,GAAGyD,OAAO,CAACzD,GAAG,IAAI,OAAO;MAClC,OAAO,iBAAiBoC,MAAM,UAAUpC,GAAG,EAAE;IACjD,CAAC,MACI;MACD,OAAO,sBAAsByD,OAAO,CAACC,IAAI,EAAE;IAC/C;EACJ;AAGJ;AAACC,yBAAA,GArHKlC,wBAAwB;AAAA9B,eAAA,CAAxB8B,wBAAwB,wBAAAmC,kCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAmH0EpC,yBAAwB;AAAA;AAAA9B,eAAA,CAnH1H8B,wBAAwB,+BAsHoDjD,EAAE,CAAAsF,kBAAA;EAAAC,KAAA,EAFwBtC,yBAAwB;EAAAuC,OAAA,EAAxBvC,yBAAwB,CAAAwC;AAAA;AAEpI;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAkF1F,EAAE,CAAA2F,iBAAA,CAAQ1C,wBAAwB,EAAc,CAAC;IACvHM,IAAI,EAAEtD;EACV,CAAC,CAAC;AAAA;AACV,SAAS2E,eAAeA,CAACgB,WAAW,EAAE;EAClC,MAAMpE,GAAG,GAAGoE,WAAW,CAAC3E,OAAO,CAACQ,aAAa;EAC7C,MAAMmC,MAAM,GAAGgC,WAAW,CAAC3E,OAAO,CAAC2C,MAAM;EACzC,OAAO,GAAGA,MAAM,IAAIpC,GAAG,EAAE;AAC7B;AAEA,SAASqE,wBAAwBA,CAAA,EAAG;EAChC,OAAO,CACH5C,wBAAwB,EACxB;IAAE6C,OAAO,EAAErF,WAAW;IAAEsF,WAAW,EAAE9C;EAAyB,CAAC,EAC/D;IAAE6C,OAAO,EAAElF,qBAAqB;IAAEmF,WAAW,EAAE9C;EAAyB,CAAC,EACzE;IAAE6C,OAAO,EAAEpF,gCAAgC;IAAEsF,QAAQ,EAAE;EAAM,CAAC,CACjE;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;AAI7BC,wBAAA,GAJKD,uBAAuB;AAAA9E,eAAA,CAAvB8E,uBAAuB,wBAAAE,iCAAAd,iBAAA;EAAA,YAAAA,iBAAA,IAC2EY,wBAAuB;AAAA;AAAA9E,eAAA,CADzH8E,uBAAuB,8BA3BqDjG,EAAE,CAAAoG,gBAAA;EAAA7C,IAAA,EA6BqB0C,wBAAuB;EAAAI,OAAA,GAAY1F,gBAAgB;AAAA;AAAAQ,eAAA,CAFtJ8E,uBAAuB,8BA3BqDjG,EAAE,CAAAsG,gBAAA;EAAAC,SAAA,EA8ByD,CAACV,wBAAwB,CAAC,CAAC,CAAC;EAAAQ,OAAA,GAAY1F,gBAAgB;AAAA;AAErM;EAAA,QAAA+E,SAAA,oBAAAA,SAAA,KAhCkF1F,EAAE,CAAA2F,iBAAA,CAgCQM,uBAAuB,EAAc,CAAC;IACtH1C,IAAI,EAAErD,QAAQ;IACdsG,IAAI,EAAE,CAAC;MACCH,OAAO,EAAE,CAAC1F,gBAAgB,CAAC;MAC3B4F,SAAS,EAAE,CAACV,wBAAwB,CAAC,CAAC;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASI,uBAAuB,EAAErF,qBAAqB,EAAEC,WAAW,EAAEgF,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}