{"ast": null, "code": "export function arrRemove(arr, item) {\n  if (arr) {\n    var index = arr.indexOf(item);\n    0 <= index && arr.splice(index, 1);\n  }\n}\n//# sourceMappingURL=arrRemove.js.map", "map": {"version": 3, "names": ["arr<PERSON><PERSON><PERSON>", "arr", "item", "index", "indexOf", "splice"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/arrRemove.js"], "sourcesContent": ["export function arrRemove(arr, item) {\n    if (arr) {\n        var index = arr.indexOf(item);\n        0 <= index && arr.splice(index, 1);\n    }\n}\n//# sourceMappingURL=arrRemove.js.map"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACjC,IAAID,GAAG,EAAE;IACL,IAAIE,KAAK,GAAGF,GAAG,CAACG,OAAO,CAACF,IAAI,CAAC;IAC7B,CAAC,IAAIC,KAAK,IAAIF,GAAG,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EACtC;AACJ;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}