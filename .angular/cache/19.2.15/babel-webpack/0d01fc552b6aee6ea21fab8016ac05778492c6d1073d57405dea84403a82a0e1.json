{"ast": null, "code": "var _SwuiGridWidgetChooserComponent;\nimport { __decorate } from \"tslib\";\nimport { Component, ComponentFactoryResolver, Injector, Input, ViewChild, ViewContainerRef } from '@angular/core';\nlet SwuiGridWidgetChooserComponent = (_SwuiGridWidgetChooserComponent = class SwuiGridWidgetChooserComponent {\n  constructor(resolver) {\n    this.resolver = resolver;\n  }\n  ngOnInit() {\n    if (this.viewRef && this.registry && this.schema && this.type) {\n      const typeSchema = this.schema[this.type];\n      const config = Object.assign({}, {\n        field: this.field || this.schema.field,\n        title: this.schema.title,\n        value: this.schema.value\n      }, typeSchema ? {\n        title: typeSchema.title,\n        value: typeSchema.value\n      } : {}, {\n        row: this.row,\n        schema: this.schema,\n        action: this.action\n      });\n      if (config.value === undefined && this.row) {\n        config.value = this.row[config.field];\n      }\n      if (typeof this.dataSource !== 'undefined' && this.type === 'footer') {\n        config.dataSource = this.dataSource;\n      }\n      const {\n        component,\n        fn\n      } = this.registry.getWidgetRegistryConfig(this.type, this.schema);\n      this.viewRef.createComponent(this.resolver.resolveComponentFactory(component), 0, Injector.create({\n        providers: fn(config)\n      }));\n    }\n  }\n}, _SwuiGridWidgetChooserComponent.ctorParameters = () => [{\n  type: ComponentFactoryResolver\n}], _SwuiGridWidgetChooserComponent.propDecorators = {\n  registry: [{\n    type: Input\n  }],\n  type: [{\n    type: Input\n  }],\n  schema: [{\n    type: Input\n  }],\n  row: [{\n    type: Input\n  }],\n  field: [{\n    type: Input\n  }],\n  action: [{\n    type: Input\n  }],\n  dataSource: [{\n    type: Input\n  }],\n  viewRef: [{\n    type: ViewChild,\n    args: ['target', {\n      read: ViewContainerRef,\n      static: true\n    }]\n  }]\n}, _SwuiGridWidgetChooserComponent);\nSwuiGridWidgetChooserComponent = __decorate([Component({\n  // tslint:disable-next-line:component-selector\n  selector: '[grid-widget-chooser]',\n  template: '<div #target></div>',\n  standalone: false\n})], SwuiGridWidgetChooserComponent);\nexport { SwuiGridWidgetChooserComponent };", "map": {"version": 3, "names": ["__decorate", "Component", "ComponentFactoryResolver", "Injector", "Input", "ViewChild", "ViewContainerRef", "SwuiGridWidgetChooserComponent", "_SwuiGridWidgetChooserComponent", "constructor", "resolver", "ngOnInit", "viewRef", "registry", "schema", "type", "typeSchema", "config", "Object", "assign", "field", "title", "value", "row", "action", "undefined", "dataSource", "component", "fn", "getWidgetRegistryConfig", "createComponent", "resolveComponentFactory", "create", "providers", "ctorParameters", "propDecorators", "args", "read", "static", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/widget-chooser/widget-chooser.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Component, ComponentFactoryResolver, Injector, Input, ViewChild, ViewContainerRef } from '@angular/core';\nlet SwuiGridWidgetChooserComponent = class SwuiGridWidgetChooserComponent {\n    constructor(resolver) {\n        this.resolver = resolver;\n    }\n    ngOnInit() {\n        if (this.viewRef && this.registry && this.schema && this.type) {\n            const typeSchema = this.schema[this.type];\n            const config = Object.assign({}, {\n                field: this.field || this.schema.field,\n                title: this.schema.title,\n                value: this.schema.value,\n            }, typeSchema ? {\n                title: typeSchema.title,\n                value: typeSchema.value\n            } : {}, {\n                row: this.row,\n                schema: this.schema,\n                action: this.action\n            });\n            if (config.value === undefined && this.row) {\n                config.value = this.row[config.field];\n            }\n            if (typeof this.dataSource !== 'undefined' && this.type === 'footer') {\n                config.dataSource = this.dataSource;\n            }\n            const { component, fn } = this.registry.getWidgetRegistryConfig(this.type, this.schema);\n            this.viewRef.createComponent(this.resolver.resolveComponentFactory(component), 0, Injector.create({\n                providers: fn(config)\n            }));\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: ComponentFactoryResolver }\n    ]; }\n    static { this.propDecorators = {\n        registry: [{ type: Input }],\n        type: [{ type: Input }],\n        schema: [{ type: Input }],\n        row: [{ type: Input }],\n        field: [{ type: Input }],\n        action: [{ type: Input }],\n        dataSource: [{ type: Input }],\n        viewRef: [{ type: ViewChild, args: ['target', { read: ViewContainerRef, static: true },] }]\n    }; }\n};\nSwuiGridWidgetChooserComponent = __decorate([\n    Component({\n        // tslint:disable-next-line:component-selector\n        selector: '[grid-widget-chooser]',\n        template: '<div #target></div>',\n        standalone: false\n    })\n], SwuiGridWidgetChooserComponent);\nexport { SwuiGridWidgetChooserComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,wBAAwB,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,QAAQ,eAAe;AACjH,IAAIC,8BAA8B,IAAAC,+BAAA,GAAG,MAAMD,8BAA8B,CAAC;EACtEE,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,CAACC,IAAI,EAAE;MAC3D,MAAMC,UAAU,GAAG,IAAI,CAACF,MAAM,CAAC,IAAI,CAACC,IAAI,CAAC;MACzC,MAAME,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE;QAC7BC,KAAK,EAAE,IAAI,CAACA,KAAK,IAAI,IAAI,CAACN,MAAM,CAACM,KAAK;QACtCC,KAAK,EAAE,IAAI,CAACP,MAAM,CAACO,KAAK;QACxBC,KAAK,EAAE,IAAI,CAACR,MAAM,CAACQ;MACvB,CAAC,EAAEN,UAAU,GAAG;QACZK,KAAK,EAAEL,UAAU,CAACK,KAAK;QACvBC,KAAK,EAAEN,UAAU,CAACM;MACtB,CAAC,GAAG,CAAC,CAAC,EAAE;QACJC,GAAG,EAAE,IAAI,CAACA,GAAG;QACbT,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBU,MAAM,EAAE,IAAI,CAACA;MACjB,CAAC,CAAC;MACF,IAAIP,MAAM,CAACK,KAAK,KAAKG,SAAS,IAAI,IAAI,CAACF,GAAG,EAAE;QACxCN,MAAM,CAACK,KAAK,GAAG,IAAI,CAACC,GAAG,CAACN,MAAM,CAACG,KAAK,CAAC;MACzC;MACA,IAAI,OAAO,IAAI,CAACM,UAAU,KAAK,WAAW,IAAI,IAAI,CAACX,IAAI,KAAK,QAAQ,EAAE;QAClEE,MAAM,CAACS,UAAU,GAAG,IAAI,CAACA,UAAU;MACvC;MACA,MAAM;QAAEC,SAAS;QAAEC;MAAG,CAAC,GAAG,IAAI,CAACf,QAAQ,CAACgB,uBAAuB,CAAC,IAAI,CAACd,IAAI,EAAE,IAAI,CAACD,MAAM,CAAC;MACvF,IAAI,CAACF,OAAO,CAACkB,eAAe,CAAC,IAAI,CAACpB,QAAQ,CAACqB,uBAAuB,CAACJ,SAAS,CAAC,EAAE,CAAC,EAAExB,QAAQ,CAAC6B,MAAM,CAAC;QAC9FC,SAAS,EAAEL,EAAE,CAACX,MAAM;MACxB,CAAC,CAAC,CAAC;IACP;EACJ;AAcJ,CAAC,EAbYT,+BAAA,CAAK0B,cAAc,GAAG,MAAM,CACjC;EAAEnB,IAAI,EAAEb;AAAyB,CAAC,CACrC,EACQM,+BAAA,CAAK2B,cAAc,GAAG;EAC3BtB,QAAQ,EAAE,CAAC;IAAEE,IAAI,EAAEX;EAAM,CAAC,CAAC;EAC3BW,IAAI,EAAE,CAAC;IAAEA,IAAI,EAAEX;EAAM,CAAC,CAAC;EACvBU,MAAM,EAAE,CAAC;IAAEC,IAAI,EAAEX;EAAM,CAAC,CAAC;EACzBmB,GAAG,EAAE,CAAC;IAAER,IAAI,EAAEX;EAAM,CAAC,CAAC;EACtBgB,KAAK,EAAE,CAAC;IAAEL,IAAI,EAAEX;EAAM,CAAC,CAAC;EACxBoB,MAAM,EAAE,CAAC;IAAET,IAAI,EAAEX;EAAM,CAAC,CAAC;EACzBsB,UAAU,EAAE,CAAC;IAAEX,IAAI,EAAEX;EAAM,CAAC,CAAC;EAC7BQ,OAAO,EAAE,CAAC;IAAEG,IAAI,EAAEV,SAAS;IAAE+B,IAAI,EAAE,CAAC,QAAQ,EAAE;MAAEC,IAAI,EAAE/B,gBAAgB;MAAEgC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC;AAC9F,CAAC,EAAA9B,+BAAA,CACJ;AACDD,8BAA8B,GAAGP,UAAU,CAAC,CACxCC,SAAS,CAAC;EACN;EACAsC,QAAQ,EAAE,uBAAuB;EACjCC,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAElC,8BAA8B,CAAC;AAClC,SAASA,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}