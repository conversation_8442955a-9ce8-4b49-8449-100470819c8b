{"ast": null, "code": "var _SwuiGridRowActionsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./row-actions.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./row-actions.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nexport class RowAction {\n  constructor(value) {\n    const config = value || {};\n    this.title = config.title || '';\n    this.icon = config.icon || undefined;\n    this.svgIcon = config.svgIcon || undefined;\n    this.fontSet = config.fontSet || undefined;\n    this.fontIcon = config.fontIcon || undefined;\n    this.inMenu = typeof config.inMenu !== 'undefined' ? config.inMenu : true;\n    this.fn = config.fn || (() => true);\n    this.canActivateFn = config.canActivateFn || (() => true);\n    this.availableFn = config.availableFn || (() => true);\n    this.showOnHover = config.showOnHover || false;\n  }\n}\nlet SwuiGridRowActionsComponent = (_SwuiGridRowActionsComponent = class SwuiGridRowActionsComponent {\n  set actions(actionsItems) {\n    if (!Array.isArray(actionsItems) || !actionsItems.length) {\n      return;\n    }\n    this._actions = actionsItems.map(actionItem => new RowAction(actionItem));\n  }\n  get actions() {\n    return this._actions;\n  }\n  constructor() {\n    this.menuIcon = 'more_horiz';\n    this.ignorePlainLink = false;\n    this.standaloneActions = [];\n    this.menuActions = [];\n    this._actions = [];\n  }\n  ngOnInit() {\n    this.processActions();\n  }\n  handleMenuClick(event, action) {\n    event.preventDefault();\n    if (action.canActivateFn(this.row)) {\n      action.fn(this.row);\n    }\n  }\n  ngOnChanges(changes) {\n    if ('actions' in changes && !changes['actions'].isFirstChange()) {\n      this.processActions();\n    }\n  }\n  processActions() {\n    this._actions = this._actions.filter(action => {\n      let filtered = true;\n      if (!!action.availableFn) {\n        filtered = action.availableFn(this.row);\n      }\n      return filtered;\n    });\n    this.populateActions();\n  }\n  populateActions() {\n    this.menuActions = this._actions.filter(item => item.inMenu);\n    this.standaloneActions = this._actions.filter(item => !item.inMenu);\n  }\n}, _SwuiGridRowActionsComponent.ctorParameters = () => [], _SwuiGridRowActionsComponent.propDecorators = {\n  actions: [{\n    type: Input\n  }],\n  row: [{\n    type: Input\n  }],\n  menuIcon: [{\n    type: Input\n  }],\n  ignorePlainLink: [{\n    type: Input\n  }]\n}, _SwuiGridRowActionsComponent);\nSwuiGridRowActionsComponent = __decorate([Component({\n  selector: 'lib-swui-grid-row-actions',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiGridRowActionsComponent);\nexport { SwuiGridRowActionsComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "RowAction", "constructor", "value", "config", "title", "icon", "undefined", "svgIcon", "fontSet", "fontIcon", "inMenu", "fn", "canActivateFn", "availableFn", "showOnHover", "SwuiGridRowActionsComponent", "_SwuiGridRowActionsComponent", "actions", "actionsItems", "Array", "isArray", "length", "_actions", "map", "actionItem", "menuIcon", "ignorePlainLink", "standaloneActions", "menuActions", "ngOnInit", "processActions", "handleMenuClick", "event", "action", "preventDefault", "row", "ngOnChanges", "changes", "isFirstChange", "filter", "filtered", "populateActions", "item", "ctorParameters", "propDecorators", "type", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/row-actions/row-actions.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./row-actions.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./row-actions.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nexport class RowAction {\n    constructor(value) {\n        const config = value || {};\n        this.title = config.title || '';\n        this.icon = config.icon || undefined;\n        this.svgIcon = config.svgIcon || undefined;\n        this.fontSet = config.fontSet || undefined;\n        this.fontIcon = config.fontIcon || undefined;\n        this.inMenu = typeof config.inMenu !== 'undefined' ? config.inMenu : true;\n        this.fn = config.fn || (() => true);\n        this.canActivateFn = config.canActivateFn || (() => true);\n        this.availableFn = config.availableFn || (() => true);\n        this.showOnHover = config.showOnHover || false;\n    }\n}\nlet SwuiGridRowActionsComponent = class SwuiGridRowActionsComponent {\n    set actions(actionsItems) {\n        if (!Array.isArray(actionsItems) || !actionsItems.length) {\n            return;\n        }\n        this._actions = actionsItems.map(actionItem => new RowAction(actionItem));\n    }\n    get actions() {\n        return this._actions;\n    }\n    constructor() {\n        this.menuIcon = 'more_horiz';\n        this.ignorePlainLink = false;\n        this.standaloneActions = [];\n        this.menuActions = [];\n        this._actions = [];\n    }\n    ngOnInit() {\n        this.processActions();\n    }\n    handleMenuClick(event, action) {\n        event.preventDefault();\n        if (action.canActivateFn(this.row)) {\n            action.fn(this.row);\n        }\n    }\n    ngOnChanges(changes) {\n        if ('actions' in changes && !changes['actions'].isFirstChange()) {\n            this.processActions();\n        }\n    }\n    processActions() {\n        this._actions = this._actions\n            .filter((action) => {\n            let filtered = true;\n            if (!!action.availableFn) {\n                filtered = action.availableFn(this.row);\n            }\n            return filtered;\n        });\n        this.populateActions();\n    }\n    populateActions() {\n        this.menuActions = this._actions.filter(item => item.inMenu);\n        this.standaloneActions = this._actions.filter(item => !item.inMenu);\n    }\n    static { this.ctorParameters = () => []; }\n    static { this.propDecorators = {\n        actions: [{ type: Input }],\n        row: [{ type: Input }],\n        menuIcon: [{ type: Input }],\n        ignorePlainLink: [{ type: Input }]\n    }; }\n};\nSwuiGridRowActionsComponent = __decorate([\n    Component({\n        selector: 'lib-swui-grid-row-actions',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiGridRowActionsComponent);\nexport { SwuiGridRowActionsComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACC,KAAK,EAAE;IACf,MAAMC,MAAM,GAAGD,KAAK,IAAI,CAAC,CAAC;IAC1B,IAAI,CAACE,KAAK,GAAGD,MAAM,CAACC,KAAK,IAAI,EAAE;IAC/B,IAAI,CAACC,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAIC,SAAS;IACpC,IAAI,CAACC,OAAO,GAAGJ,MAAM,CAACI,OAAO,IAAID,SAAS;IAC1C,IAAI,CAACE,OAAO,GAAGL,MAAM,CAACK,OAAO,IAAIF,SAAS;IAC1C,IAAI,CAACG,QAAQ,GAAGN,MAAM,CAACM,QAAQ,IAAIH,SAAS;IAC5C,IAAI,CAACI,MAAM,GAAG,OAAOP,MAAM,CAACO,MAAM,KAAK,WAAW,GAAGP,MAAM,CAACO,MAAM,GAAG,IAAI;IACzE,IAAI,CAACC,EAAE,GAAGR,MAAM,CAACQ,EAAE,KAAK,MAAM,IAAI,CAAC;IACnC,IAAI,CAACC,aAAa,GAAGT,MAAM,CAACS,aAAa,KAAK,MAAM,IAAI,CAAC;IACzD,IAAI,CAACC,WAAW,GAAGV,MAAM,CAACU,WAAW,KAAK,MAAM,IAAI,CAAC;IACrD,IAAI,CAACC,WAAW,GAAGX,MAAM,CAACW,WAAW,IAAI,KAAK;EAClD;AACJ;AACA,IAAIC,2BAA2B,IAAAC,4BAAA,GAAG,MAAMD,2BAA2B,CAAC;EAChE,IAAIE,OAAOA,CAACC,YAAY,EAAE;IACtB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,IAAI,CAACA,YAAY,CAACG,MAAM,EAAE;MACtD;IACJ;IACA,IAAI,CAACC,QAAQ,GAAGJ,YAAY,CAACK,GAAG,CAACC,UAAU,IAAI,IAAIxB,SAAS,CAACwB,UAAU,CAAC,CAAC;EAC7E;EACA,IAAIP,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACK,QAAQ;EACxB;EACArB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwB,QAAQ,GAAG,YAAY;IAC5B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACN,QAAQ,GAAG,EAAE;EACtB;EACAO,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB;EACAC,eAAeA,CAACC,KAAK,EAAEC,MAAM,EAAE;IAC3BD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtB,IAAID,MAAM,CAACrB,aAAa,CAAC,IAAI,CAACuB,GAAG,CAAC,EAAE;MAChCF,MAAM,CAACtB,EAAE,CAAC,IAAI,CAACwB,GAAG,CAAC;IACvB;EACJ;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,SAAS,IAAIA,OAAO,IAAI,CAACA,OAAO,CAAC,SAAS,CAAC,CAACC,aAAa,CAAC,CAAC,EAAE;MAC7D,IAAI,CAACR,cAAc,CAAC,CAAC;IACzB;EACJ;EACAA,cAAcA,CAAA,EAAG;IACb,IAAI,CAACR,QAAQ,GAAG,IAAI,CAACA,QAAQ,CACxBiB,MAAM,CAAEN,MAAM,IAAK;MACpB,IAAIO,QAAQ,GAAG,IAAI;MACnB,IAAI,CAAC,CAACP,MAAM,CAACpB,WAAW,EAAE;QACtB2B,QAAQ,GAAGP,MAAM,CAACpB,WAAW,CAAC,IAAI,CAACsB,GAAG,CAAC;MAC3C;MACA,OAAOK,QAAQ;IACnB,CAAC,CAAC;IACF,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACAA,eAAeA,CAAA,EAAG;IACd,IAAI,CAACb,WAAW,GAAG,IAAI,CAACN,QAAQ,CAACiB,MAAM,CAACG,IAAI,IAAIA,IAAI,CAAChC,MAAM,CAAC;IAC5D,IAAI,CAACiB,iBAAiB,GAAG,IAAI,CAACL,QAAQ,CAACiB,MAAM,CAACG,IAAI,IAAI,CAACA,IAAI,CAAChC,MAAM,CAAC;EACvE;AAQJ,CAAC,EAPYM,4BAAA,CAAK2B,cAAc,GAAG,MAAM,EAAE,EAC9B3B,4BAAA,CAAK4B,cAAc,GAAG;EAC3B3B,OAAO,EAAE,CAAC;IAAE4B,IAAI,EAAE9C;EAAM,CAAC,CAAC;EAC1BoC,GAAG,EAAE,CAAC;IAAEU,IAAI,EAAE9C;EAAM,CAAC,CAAC;EACtB0B,QAAQ,EAAE,CAAC;IAAEoB,IAAI,EAAE9C;EAAM,CAAC,CAAC;EAC3B2B,eAAe,EAAE,CAAC;IAAEmB,IAAI,EAAE9C;EAAM,CAAC;AACrC,CAAC,EAAAiB,4BAAA,CACJ;AACDD,2BAA2B,GAAGpB,UAAU,CAAC,CACrCG,SAAS,CAAC;EACNgD,QAAQ,EAAE,2BAA2B;EACrCC,QAAQ,EAAEnD,oBAAoB;EAC9BoD,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACpD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEkB,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}