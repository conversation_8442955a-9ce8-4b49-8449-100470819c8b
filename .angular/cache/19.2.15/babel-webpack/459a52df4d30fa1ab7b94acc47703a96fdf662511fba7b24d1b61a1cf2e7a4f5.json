{"ast": null, "code": "var _SwuiBreadcrumbsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-breadcrumbs.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-breadcrumbs.component.scss?ngResource\";\nimport { ActivatedRoute, NavigationEnd, Router } from '@angular/router';\nimport { Component, ViewEncapsulation } from '@angular/core';\nimport { distinctUntilChanged, filter } from 'rxjs/operators';\nlet SwuiBreadcrumbsComponent = (_SwuiBreadcrumbsComponent = class SwuiBreadcrumbsComponent {\n  constructor(router, activatedRoute) {\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.breadcrumbs = this.buildBreadcrumbs(this.activatedRoute.root);\n  }\n  ngOnInit() {\n    this.initBreadcrumbs();\n  }\n  initBreadcrumbs() {\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), distinctUntilChanged()).subscribe(() => {\n      this.breadcrumbs = this.buildBreadcrumbs(this.activatedRoute.root);\n    });\n  }\n  buildBreadcrumbs(route, url = '', breadcrumbs = []) {\n    let label = route.routeConfig && route.routeConfig.data ? route.routeConfig.data.breadcrumb : '';\n    let path = route.routeConfig && route.routeConfig.data ? route.routeConfig.path : '';\n    if (path) {\n      const lastRoutePart = path.split('/').pop();\n      if (lastRoutePart) {\n        const isDynamicRoute = lastRoutePart.startsWith(':');\n        if (isDynamicRoute && !!route.snapshot) {\n          const paramName = lastRoutePart.split(':')[1];\n          path = path.replace(lastRoutePart, route.snapshot.params[paramName]);\n          label = route.snapshot.params[paramName];\n        }\n      }\n    }\n    const nextUrl = path ? `${url}/${path}` : url;\n    const breadcrumb = {\n      label: label,\n      url: nextUrl\n    };\n    const newBreadcrumbs = breadcrumb.label ? [...breadcrumbs, breadcrumb] : [...breadcrumbs];\n    if (route.firstChild) {\n      return this.buildBreadcrumbs(route.firstChild, nextUrl, newBreadcrumbs);\n    }\n    return newBreadcrumbs;\n  }\n}, _SwuiBreadcrumbsComponent.ctorParameters = () => [{\n  type: Router\n}, {\n  type: ActivatedRoute\n}], _SwuiBreadcrumbsComponent);\nSwuiBreadcrumbsComponent = __decorate([Component({\n  selector: 'lib-swui-breadcrumbs',\n  template: __NG_CLI_RESOURCE__0,\n  encapsulation: ViewEncapsulation.None,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiBreadcrumbsComponent);\nexport { SwuiBreadcrumbsComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ActivatedRoute", "NavigationEnd", "Router", "Component", "ViewEncapsulation", "distinctUntilChanged", "filter", "SwuiBreadcrumbsComponent", "_SwuiBreadcrumbsComponent", "constructor", "router", "activatedRoute", "breadcrumbs", "buildBreadcrumbs", "root", "ngOnInit", "initBreadcrumbs", "events", "pipe", "event", "subscribe", "route", "url", "label", "routeConfig", "data", "breadcrumb", "path", "lastRoutePart", "split", "pop", "isDynamicRoute", "startsWith", "snapshot", "paramName", "replace", "params", "nextUrl", "newBreadcrumbs", "<PERSON><PERSON><PERSON><PERSON>", "ctorParameters", "type", "selector", "template", "encapsulation", "None", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-breadcrumbs/swui-breadcrumbs.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-breadcrumbs.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-breadcrumbs.component.scss?ngResource\";\nimport { ActivatedRoute, NavigationEnd, Router } from '@angular/router';\nimport { Component, ViewEncapsulation } from '@angular/core';\nimport { distinctUntilChanged, filter } from 'rxjs/operators';\nlet SwuiBreadcrumbsComponent = class SwuiBreadcrumbsComponent {\n    constructor(router, activatedRoute) {\n        this.router = router;\n        this.activatedRoute = activatedRoute;\n        this.breadcrumbs = this.buildBreadcrumbs(this.activatedRoute.root);\n    }\n    ngOnInit() {\n        this.initBreadcrumbs();\n    }\n    initBreadcrumbs() {\n        this.router.events.pipe(filter((event) => event instanceof NavigationEnd), distinctUntilChanged()).subscribe(() => {\n            this.breadcrumbs = this.buildBreadcrumbs(this.activatedRoute.root);\n        });\n    }\n    buildBreadcrumbs(route, url = '', breadcrumbs = []) {\n        let label = route.routeConfig && route.routeConfig.data ? route.routeConfig.data.breadcrumb : '';\n        let path = route.routeConfig && route.routeConfig.data ? route.routeConfig.path : '';\n        if (path) {\n            const lastRoutePart = path.split('/').pop();\n            if (lastRoutePart) {\n                const isDynamicRoute = lastRoutePart.startsWith(':');\n                if (isDynamicRoute && !!route.snapshot) {\n                    const paramName = lastRoutePart.split(':')[1];\n                    path = path.replace(lastRoutePart, route.snapshot.params[paramName]);\n                    label = route.snapshot.params[paramName];\n                }\n            }\n        }\n        const nextUrl = path ? `${url}/${path}` : url;\n        const breadcrumb = {\n            label: label,\n            url: nextUrl,\n        };\n        const newBreadcrumbs = breadcrumb.label ? [...breadcrumbs, breadcrumb] : [...breadcrumbs];\n        if (route.firstChild) {\n            return this.buildBreadcrumbs(route.firstChild, nextUrl, newBreadcrumbs);\n        }\n        return newBreadcrumbs;\n    }\n    static { this.ctorParameters = () => [\n        { type: Router },\n        { type: ActivatedRoute }\n    ]; }\n};\nSwuiBreadcrumbsComponent = __decorate([\n    Component({\n        selector: 'lib-swui-breadcrumbs',\n        template: __NG_CLI_RESOURCE__0,\n        encapsulation: ViewEncapsulation.None,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiBreadcrumbsComponent);\nexport { SwuiBreadcrumbsComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,SAASC,cAAc,EAAEC,aAAa,EAAEC,MAAM,QAAQ,iBAAiB;AACvE,SAASC,SAAS,EAAEC,iBAAiB,QAAQ,eAAe;AAC5D,SAASC,oBAAoB,EAAEC,MAAM,QAAQ,gBAAgB;AAC7D,IAAIC,wBAAwB,IAAAC,yBAAA,GAAG,MAAMD,wBAAwB,CAAC;EAC1DE,WAAWA,CAACC,MAAM,EAAEC,cAAc,EAAE;IAChC,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACF,cAAc,CAACG,IAAI,CAAC;EACtE;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACAA,eAAeA,CAAA,EAAG;IACd,IAAI,CAACN,MAAM,CAACO,MAAM,CAACC,IAAI,CAACZ,MAAM,CAAEa,KAAK,IAAKA,KAAK,YAAYlB,aAAa,CAAC,EAAEI,oBAAoB,CAAC,CAAC,CAAC,CAACe,SAAS,CAAC,MAAM;MAC/G,IAAI,CAACR,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACF,cAAc,CAACG,IAAI,CAAC;IACtE,CAAC,CAAC;EACN;EACAD,gBAAgBA,CAACQ,KAAK,EAAEC,GAAG,GAAG,EAAE,EAAEV,WAAW,GAAG,EAAE,EAAE;IAChD,IAAIW,KAAK,GAAGF,KAAK,CAACG,WAAW,IAAIH,KAAK,CAACG,WAAW,CAACC,IAAI,GAAGJ,KAAK,CAACG,WAAW,CAACC,IAAI,CAACC,UAAU,GAAG,EAAE;IAChG,IAAIC,IAAI,GAAGN,KAAK,CAACG,WAAW,IAAIH,KAAK,CAACG,WAAW,CAACC,IAAI,GAAGJ,KAAK,CAACG,WAAW,CAACG,IAAI,GAAG,EAAE;IACpF,IAAIA,IAAI,EAAE;MACN,MAAMC,aAAa,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MAC3C,IAAIF,aAAa,EAAE;QACf,MAAMG,cAAc,GAAGH,aAAa,CAACI,UAAU,CAAC,GAAG,CAAC;QACpD,IAAID,cAAc,IAAI,CAAC,CAACV,KAAK,CAACY,QAAQ,EAAE;UACpC,MAAMC,SAAS,GAAGN,aAAa,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC7CF,IAAI,GAAGA,IAAI,CAACQ,OAAO,CAACP,aAAa,EAAEP,KAAK,CAACY,QAAQ,CAACG,MAAM,CAACF,SAAS,CAAC,CAAC;UACpEX,KAAK,GAAGF,KAAK,CAACY,QAAQ,CAACG,MAAM,CAACF,SAAS,CAAC;QAC5C;MACJ;IACJ;IACA,MAAMG,OAAO,GAAGV,IAAI,GAAG,GAAGL,GAAG,IAAIK,IAAI,EAAE,GAAGL,GAAG;IAC7C,MAAMI,UAAU,GAAG;MACfH,KAAK,EAAEA,KAAK;MACZD,GAAG,EAAEe;IACT,CAAC;IACD,MAAMC,cAAc,GAAGZ,UAAU,CAACH,KAAK,GAAG,CAAC,GAAGX,WAAW,EAAEc,UAAU,CAAC,GAAG,CAAC,GAAGd,WAAW,CAAC;IACzF,IAAIS,KAAK,CAACkB,UAAU,EAAE;MAClB,OAAO,IAAI,CAAC1B,gBAAgB,CAACQ,KAAK,CAACkB,UAAU,EAAEF,OAAO,EAAEC,cAAc,CAAC;IAC3E;IACA,OAAOA,cAAc;EACzB;AAKJ,CAAC,EAJY9B,yBAAA,CAAKgC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEvC;AAAO,CAAC,EAChB;EAAEuC,IAAI,EAAEzC;AAAe,CAAC,CAC3B,EAAAQ,yBAAA,CACJ;AACDD,wBAAwB,GAAGV,UAAU,CAAC,CAClCM,SAAS,CAAC;EACNuC,QAAQ,EAAE,sBAAsB;EAChCC,QAAQ,EAAE7C,oBAAoB;EAC9B8C,aAAa,EAAExC,iBAAiB,CAACyC,IAAI;EACrCC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAChD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEQ,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}