{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatMenuItem, _MatMenuContent, _MatMenu, _MatMenuTrigger, _MatMenuModule;\nconst _c0 = [\"mat-menu-item\", \"\"];\nconst _c1 = [[[\"mat-icon\"], [\"\", \"matMenuItemIcon\", \"\"]], \"*\"];\nconst _c2 = [\"mat-icon, [matMenuItemIcon]\", \"*\"];\nfunction _MatMenuItem_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 2);\n    i0.ɵɵelement(1, \"polygon\", 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = [\"*\"];\nfunction _MatMenu_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"click\", function _MatMenu_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closed.emit(\"click\"));\n    })(\"animationstart\", function _MatMenu_ng_template_0_Template_div_animationstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationStart($event.animationName));\n    })(\"animationend\", function _MatMenu_ng_template_0_Template_div_animationend_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationDone($event.animationName));\n    })(\"animationcancel\", function _MatMenu_ng_template_0_Template_div_animationcancel_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationDone($event.animationName));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1._classList);\n    i0.ɵɵclassProp(\"mat-menu-panel-animations-disabled\", ctx_r1._animationsDisabled)(\"mat-menu-panel-exit-animation\", ctx_r1._panelAnimationState === \"void\")(\"mat-menu-panel-animating\", ctx_r1._isAnimating);\n    i0.ɵɵproperty(\"id\", ctx_r1.panelId);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1.ariaLabelledby || null)(\"aria-describedby\", ctx_r1.ariaDescribedby || null);\n  }\n}\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, ChangeDetectorRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, TemplateRef, ApplicationRef, Injector, ViewContainerRef, Directive, QueryList, EventEmitter, ANIMATION_MODULE_TYPE, afterNextRender, ContentChildren, ViewChild, ContentChild, Output, NgZone, Renderer2, NgModule } from '@angular/core';\nimport { FocusMonitor, _IdGenerator, FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of } from 'rxjs';\nimport { startWith, switchMap, takeUntil, take, filter } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { _bindEventWithOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/coercion';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem {\n  constructor() {\n    var _this$_parentMenu, _this$_parentMenu$add;\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_focusMonitor\", inject(FocusMonitor));\n    _defineProperty(this, \"_parentMenu\", inject(MAT_MENU_PANEL, {\n      optional: true\n    }));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    /** ARIA role for the menu item. */\n    _defineProperty(this, \"role\", 'menuitem');\n    /** Whether the menu item is disabled. */\n    _defineProperty(this, \"disabled\", false);\n    /** Whether ripples are disabled on the menu item. */\n    _defineProperty(this, \"disableRipple\", false);\n    /** Stream that emits when the menu item is hovered. */\n    _defineProperty(this, \"_hovered\", new Subject());\n    /** Stream that emits when the menu item is focused. */\n    _defineProperty(this, \"_focused\", new Subject());\n    /** Whether the menu item is highlighted. */\n    _defineProperty(this, \"_highlighted\", false);\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n    _defineProperty(this, \"_triggersSubmenu\", false);\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    (_this$_parentMenu = this._parentMenu) === null || _this$_parentMenu === void 0 || (_this$_parentMenu$add = _this$_parentMenu.addItem) === null || _this$_parentMenu$add === void 0 || _this$_parentMenu$add.call(_this$_parentMenu, this);\n  }\n  /** Focuses the menu item. */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n    } else {\n      this._getHostElement().focus(options);\n    }\n    this._focused.next(this);\n  }\n  ngAfterViewInit() {\n    if (this._focusMonitor) {\n      // Start monitoring the element, so it gets the appropriate focused classes. We want\n      // to show the focus style for menu items only when the focus was not caused by a\n      // mouse or touch interaction.\n      this._focusMonitor.monitor(this._elementRef, false);\n    }\n  }\n  ngOnDestroy() {\n    if (this._focusMonitor) {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    if (this._parentMenu && this._parentMenu.removeItem) {\n      this._parentMenu.removeItem(this);\n    }\n    this._hovered.complete();\n    this._focused.complete();\n  }\n  /** Used to set the `tabindex`. */\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Returns the host DOM element. */\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n  /** Prevents the default element actions if it is disabled. */\n  _checkDisabled(event) {\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  /** Emits to the hover stream. */\n  _handleMouseEnter() {\n    this._hovered.next(this);\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    var _clone$textContent;\n    const clone = this._elementRef.nativeElement.cloneNode(true);\n    const icons = clone.querySelectorAll('mat-icon, .material-icons');\n    // Strip away icons, so they don't show up in the text.\n    for (let i = 0; i < icons.length; i++) {\n      icons[i].remove();\n    }\n    return ((_clone$textContent = clone.textContent) === null || _clone$textContent === void 0 ? void 0 : _clone$textContent.trim()) || '';\n  }\n  _setHighlighted(isHighlighted) {\n    // We need to mark this for check for the case where the content is coming from a\n    // `matMenuContent` whose change detection tree is at the declaration position,\n    // not the insertion position. See #23175.\n    this._highlighted = isHighlighted;\n    this._changeDetectorRef.markForCheck();\n  }\n  _setTriggersSubmenu(triggersSubmenu) {\n    this._triggersSubmenu = triggersSubmenu;\n    this._changeDetectorRef.markForCheck();\n  }\n  _hasFocus() {\n    return this._document && this._document.activeElement === this._getHostElement();\n  }\n}\n_MatMenuItem = MatMenuItem;\n_defineProperty(MatMenuItem, \"\\u0275fac\", function _MatMenuItem_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatMenuItem)();\n});\n_defineProperty(MatMenuItem, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatMenuItem,\n  selectors: [[\"\", \"mat-menu-item\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-menu-item\", \"mat-focus-indicator\"],\n  hostVars: 8,\n  hostBindings: function _MatMenuItem_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _MatMenuItem_click_HostBindingHandler($event) {\n        return ctx._checkDisabled($event);\n      })(\"mouseenter\", function _MatMenuItem_mouseenter_HostBindingHandler() {\n        return ctx._handleMouseEnter();\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx._getTabIndex())(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx.disabled || null);\n      i0.ɵɵclassProp(\"mat-mdc-menu-item-highlighted\", ctx._highlighted)(\"mat-mdc-menu-item-submenu-trigger\", ctx._triggersSubmenu);\n    }\n  },\n  inputs: {\n    role: \"role\",\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n    disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute]\n  },\n  exportAs: [\"matMenuItem\"],\n  attrs: _c0,\n  ngContentSelectors: _c2,\n  decls: 5,\n  vars: 3,\n  consts: [[1, \"mat-mdc-menu-item-text\"], [\"matRipple\", \"\", 1, \"mat-mdc-menu-ripple\", 3, \"matRippleDisabled\", \"matRippleTrigger\"], [\"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-menu-submenu-icon\"], [\"points\", \"0,0 5,5 0,10\"]],\n  template: function _MatMenuItem_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c1);\n      i0.ɵɵprojection(0);\n      i0.ɵɵelementStart(1, \"span\", 0);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(3, \"div\", 1);\n      i0.ɵɵtemplate(4, _MatMenuItem_Conditional_4_Template, 2, 0, \":svg:svg\", 2);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleTrigger\", ctx._getHostElement());\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx._triggersSubmenu ? 4 : -1);\n    }\n  },\n  dependencies: [MatRipple],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuItem, [{\n    type: Component,\n    args: [{\n      selector: '[mat-menu-item]',\n      exportAs: 'matMenuItem',\n      host: {\n        '[attr.role]': 'role',\n        'class': 'mat-mdc-menu-item mat-focus-indicator',\n        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n        '[attr.tabindex]': '_getTabIndex()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.disabled]': 'disabled || null',\n        '(click)': '_checkDisabled($event)',\n        '(mouseenter)': '_handleMouseEnter()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [MatRipple],\n      template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\"\n    }]\n  }], () => [], {\n    role: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n  throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` + `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent {\n  constructor() {\n    _defineProperty(this, \"_template\", inject(TemplateRef));\n    _defineProperty(this, \"_appRef\", inject(ApplicationRef));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_viewContainerRef\", inject(ViewContainerRef));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_portal\", void 0);\n    _defineProperty(this, \"_outlet\", void 0);\n    /** Emits when the menu content has been attached. */\n    _defineProperty(this, \"_attached\", new Subject());\n  }\n  /**\n   * Attaches the content with a particular context.\n   * @docs-private\n   */\n  attach(context = {}) {\n    if (!this._portal) {\n      this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    }\n    this.detach();\n    if (!this._outlet) {\n      this._outlet = new DomPortalOutlet(this._document.createElement('div'), null, this._appRef, this._injector);\n    }\n    const element = this._template.elementRef.nativeElement;\n    // Because we support opening the same menu from different triggers (which in turn have their\n    // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n    // risk it staying attached to a pane that's no longer in the DOM.\n    element.parentNode.insertBefore(this._outlet.outletElement, element);\n    // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n    // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n    // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n    // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n    // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n    this._changeDetectorRef.markForCheck();\n    this._portal.attach(this._outlet, context);\n    this._attached.next();\n  }\n  /**\n   * Detaches the content.\n   * @docs-private\n   */\n  detach() {\n    var _this$_portal;\n    if ((_this$_portal = this._portal) !== null && _this$_portal !== void 0 && _this$_portal.isAttached) {\n      this._portal.detach();\n    }\n  }\n  ngOnDestroy() {\n    var _this$_outlet;\n    this.detach();\n    (_this$_outlet = this._outlet) === null || _this$_outlet === void 0 || _this$_outlet.dispose();\n  }\n}\n_MatMenuContent = MatMenuContent;\n_defineProperty(MatMenuContent, \"\\u0275fac\", function _MatMenuContent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatMenuContent)();\n});\n_defineProperty(MatMenuContent, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatMenuContent,\n  selectors: [[\"ng-template\", \"matMenuContent\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_MENU_CONTENT,\n    useExisting: _MatMenuContent\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matMenuContent]',\n      providers: [{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }]\n    }]\n  }], () => [], null);\n})();\n\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n  providedIn: 'root',\n  factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop'\n  };\n}\n/** Name of the enter animation `@keyframes`. */\nconst ENTER_ANIMATION = '_mat-menu-enter';\n/** Name of the exit animation `@keyframes`. */\nconst EXIT_ANIMATION = '_mat-menu-exit';\nclass MatMenu {\n  /** Position of the menu in the X axis. */\n  get xPosition() {\n    return this._xPosition;\n  }\n  set xPosition(value) {\n    if (value !== 'before' && value !== 'after' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionX();\n    }\n    this._xPosition = value;\n    this.setPositionClasses();\n  }\n  /** Position of the menu in the Y axis. */\n  get yPosition() {\n    return this._yPosition;\n  }\n  set yPosition(value) {\n    if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionY();\n    }\n    this._yPosition = value;\n    this.setPositionClasses();\n  }\n  /** @docs-private */\n\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @param classes list of class names\n   */\n  set panelClass(classes) {\n    const previousPanelClass = this._previousPanelClass;\n    const newClassList = _objectSpread({}, this._classList);\n    if (previousPanelClass && previousPanelClass.length) {\n      previousPanelClass.split(' ').forEach(className => {\n        newClassList[className] = false;\n      });\n    }\n    this._previousPanelClass = classes;\n    if (classes && classes.length) {\n      classes.split(' ').forEach(className => {\n        newClassList[className] = true;\n      });\n      this._elementRef.nativeElement.className = '';\n    }\n    this._classList = newClassList;\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @deprecated Use `panelClass` instead.\n   * @breaking-change 8.0.0\n   */\n  get classList() {\n    return this.panelClass;\n  }\n  set classList(classes) {\n    this.panelClass = classes;\n  }\n  /** Event emitted when the menu is closed. */\n\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_keyManager\", void 0);\n    _defineProperty(this, \"_xPosition\", void 0);\n    _defineProperty(this, \"_yPosition\", void 0);\n    _defineProperty(this, \"_firstItemFocusRef\", void 0);\n    _defineProperty(this, \"_exitFallbackTimeout\", void 0);\n    /** Whether animations are currently disabled. */\n    _defineProperty(this, \"_animationsDisabled\", void 0);\n    /** All items inside the menu. Includes items nested inside another menu. */\n    _defineProperty(this, \"_allItems\", void 0);\n    /** Only the direct descendant menu items. */\n    _defineProperty(this, \"_directDescendantItems\", new QueryList());\n    /** Classes to be applied to the menu panel. */\n    _defineProperty(this, \"_classList\", {});\n    /** Current state of the panel animation. */\n    _defineProperty(this, \"_panelAnimationState\", 'void');\n    /** Emits whenever an animation on the menu completes. */\n    _defineProperty(this, \"_animationDone\", new Subject());\n    /** Whether the menu is animating. */\n    _defineProperty(this, \"_isAnimating\", false);\n    /** Parent menu of the current menu panel. */\n    _defineProperty(this, \"parentMenu\", void 0);\n    /** Layout direction of the menu. */\n    _defineProperty(this, \"direction\", void 0);\n    /** Class or list of classes to be added to the overlay panel. */\n    _defineProperty(this, \"overlayPanelClass\", void 0);\n    /** Class to be added to the backdrop element. */\n    _defineProperty(this, \"backdropClass\", void 0);\n    /** aria-label for the menu panel. */\n    _defineProperty(this, \"ariaLabel\", void 0);\n    /** aria-labelledby for the menu panel. */\n    _defineProperty(this, \"ariaLabelledby\", void 0);\n    /** aria-describedby for the menu panel. */\n    _defineProperty(this, \"ariaDescribedby\", void 0);\n    _defineProperty(this, \"templateRef\", void 0);\n    /**\n     * List of the items inside of a menu.\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    _defineProperty(this, \"items\", void 0);\n    /**\n     * Menu content that will be rendered lazily.\n     * @docs-private\n     */\n    _defineProperty(this, \"lazyContent\", void 0);\n    /** Whether the menu should overlap its trigger. */\n    _defineProperty(this, \"overlapTrigger\", void 0);\n    /** Whether the menu has a backdrop. */\n    _defineProperty(this, \"hasBackdrop\", void 0);\n    _defineProperty(this, \"_previousPanelClass\", void 0);\n    _defineProperty(this, \"closed\", new EventEmitter());\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n    _defineProperty(this, \"close\", this.closed);\n    _defineProperty(this, \"panelId\", inject(_IdGenerator).getId('mat-menu-panel-'));\n    const defaultOptions = inject(MAT_MENU_DEFAULT_OPTIONS);\n    this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n    this._xPosition = defaultOptions.xPosition;\n    this._yPosition = defaultOptions.yPosition;\n    this.backdropClass = defaultOptions.backdropClass;\n    this.overlapTrigger = defaultOptions.overlapTrigger;\n    this.hasBackdrop = defaultOptions.hasBackdrop;\n    this._animationsDisabled = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }) === 'NoopAnimations';\n  }\n  ngOnInit() {\n    this.setPositionClasses();\n  }\n  ngAfterContentInit() {\n    this._updateDirectDescendants();\n    this._keyManager = new FocusKeyManager(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd();\n    this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n    // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n    // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n    // is internal and we know that it gets completed on destroy.\n    this._directDescendantItems.changes.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._focused)))).subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n    this._directDescendantItems.changes.subscribe(itemsList => {\n      var _manager$activeItem;\n      // Move focus to another item, if the active item is removed from the list.\n      // We need to debounce the callback, because multiple items might be removed\n      // in quick succession.\n      const manager = this._keyManager;\n      if (this._panelAnimationState === 'enter' && (_manager$activeItem = manager.activeItem) !== null && _manager$activeItem !== void 0 && _manager$activeItem._hasFocus()) {\n        const items = itemsList.toArray();\n        const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n        if (items[index] && !items[index].disabled) {\n          manager.setActiveItem(index);\n        } else {\n          manager.setNextItemActive();\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    var _this$_keyManager, _this$_firstItemFocus;\n    (_this$_keyManager = this._keyManager) === null || _this$_keyManager === void 0 || _this$_keyManager.destroy();\n    this._directDescendantItems.destroy();\n    this.closed.complete();\n    (_this$_firstItemFocus = this._firstItemFocusRef) === null || _this$_firstItemFocus === void 0 || _this$_firstItemFocus.destroy();\n    clearTimeout(this._exitFallbackTimeout);\n  }\n  /** Stream that emits whenever the hovered menu item changes. */\n  _hovered() {\n    // Coerce the `changes` property because Angular types it as `Observable<any>`\n    const itemChanges = this._directDescendantItems.changes;\n    return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._hovered))));\n  }\n  /*\n   * Registers a menu item with the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  addItem(_item) {}\n  /**\n   * Removes an item from the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  removeItem(_item) {}\n  /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n    switch (keyCode) {\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.closed.emit('keydown');\n        }\n        break;\n      case LEFT_ARROW:\n        if (this.parentMenu && this.direction === 'ltr') {\n          this.closed.emit('keydown');\n        }\n        break;\n      case RIGHT_ARROW:\n        if (this.parentMenu && this.direction === 'rtl') {\n          this.closed.emit('keydown');\n        }\n        break;\n      default:\n        if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n          manager.setFocusOrigin('keyboard');\n        }\n        manager.onKeydown(event);\n        return;\n    }\n  }\n  /**\n   * Focus the first item in the menu.\n   * @param origin Action from which the focus originated. Used to set the correct styling.\n   */\n  focusFirstItem(origin = 'program') {\n    var _this$_firstItemFocus2;\n    // Wait for `afterNextRender` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n    (_this$_firstItemFocus2 = this._firstItemFocusRef) === null || _this$_firstItemFocus2 === void 0 || _this$_firstItemFocus2.destroy();\n    this._firstItemFocusRef = afterNextRender(() => {\n      const menuPanel = this._resolvePanel();\n      // If an item in the menuPanel is already focused, avoid overriding the focus.\n      if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n        const manager = this._keyManager;\n        manager.setFocusOrigin(origin).setFirstItemActive();\n        // If there's no active item at this point, it means that all the items are disabled.\n        // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n        // give _some_ feedback to screen readers.\n        if (!manager.activeItem && menuPanel) {\n          menuPanel.focus();\n        }\n      }\n    }, {\n      injector: this._injector\n    });\n  }\n  /**\n   * Resets the active item in the menu. This is used when the menu is opened, allowing\n   * the user to start from the first option when pressing the down arrow.\n   */\n  resetActiveItem() {\n    this._keyManager.setActiveItem(-1);\n  }\n  /**\n   * @deprecated No longer used and will be removed.\n   * @breaking-change 21.0.0\n   */\n  setElevation(_depth) {}\n  /**\n   * Adds classes to the menu panel based on its position. Can be used by\n   * consumers to add specific styling based on the position.\n   * @param posX Position of the menu along the x axis.\n   * @param posY Position of the menu along the y axis.\n   * @docs-private\n   */\n  setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n    this._classList = _objectSpread(_objectSpread({}, this._classList), {}, {\n      ['mat-menu-before']: posX === 'before',\n      ['mat-menu-after']: posX === 'after',\n      ['mat-menu-above']: posY === 'above',\n      ['mat-menu-below']: posY === 'below'\n    });\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Callback that is invoked when the panel animation completes. */\n  _onAnimationDone(state) {\n    const isExit = state === EXIT_ANIMATION;\n    if (isExit || state === ENTER_ANIMATION) {\n      if (isExit) {\n        clearTimeout(this._exitFallbackTimeout);\n        this._exitFallbackTimeout = undefined;\n      }\n      this._animationDone.next(isExit ? 'void' : 'enter');\n      this._isAnimating = false;\n    }\n  }\n  _onAnimationStart(state) {\n    if (state === ENTER_ANIMATION || state === EXIT_ANIMATION) {\n      this._isAnimating = true;\n    }\n  }\n  _setIsOpen(isOpen) {\n    this._panelAnimationState = isOpen ? 'enter' : 'void';\n    if (isOpen) {\n      if (this._keyManager.activeItemIndex === 0) {\n        // Scroll the content element to the top as soon as the animation starts. This is necessary,\n        // because we move focus to the first item while it's still being animated, which can throw\n        // the browser off when it determines the scroll position. Alternatively we can move focus\n        // when the animation is done, however moving focus asynchronously will interrupt screen\n        // readers which are in the process of reading out the menu already. We take the `element`\n        // from the `event` since we can't use a `ViewChild` to access the pane.\n        const menuPanel = this._resolvePanel();\n        if (menuPanel) {\n          menuPanel.scrollTop = 0;\n        }\n      }\n    } else if (!this._animationsDisabled) {\n      // Some apps do `* { animation: none !important; }` in tests which will prevent the\n      // `animationend` event from firing. Since the exit animation is loading-bearing for\n      // removing the content from the DOM, add a fallback timer.\n      this._exitFallbackTimeout = setTimeout(() => this._onAnimationDone(EXIT_ANIMATION), 200);\n    }\n    // Animation events won't fire when animations are disabled so we simulate them.\n    if (this._animationsDisabled) {\n      setTimeout(() => {\n        this._onAnimationDone(isOpen ? ENTER_ANIMATION : EXIT_ANIMATION);\n      });\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Sets up a stream that will keep track of any newly-added menu items and will update the list\n   * of direct descendants. We collect the descendants this way, because `_allItems` can include\n   * items that are part of child menus, and using a custom way of registering items is unreliable\n   * when it comes to maintaining the item order.\n   */\n  _updateDirectDescendants() {\n    this._allItems.changes.pipe(startWith(this._allItems)).subscribe(items => {\n      this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n      this._directDescendantItems.notifyOnChanges();\n    });\n  }\n  /** Gets the menu panel DOM node. */\n  _resolvePanel() {\n    let menuPanel = null;\n    if (this._directDescendantItems.length) {\n      // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n      // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n      // because the panel is inside an `ng-template`. We work around it by starting from one of\n      // the items and walking up the DOM.\n      menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n    }\n    return menuPanel;\n  }\n}\n_MatMenu = MatMenu;\n_defineProperty(MatMenu, \"\\u0275fac\", function _MatMenu_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatMenu)();\n});\n_defineProperty(MatMenu, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatMenu,\n  selectors: [[\"mat-menu\"]],\n  contentQueries: function _MatMenu_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_MENU_CONTENT, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 4);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allItems = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n    }\n  },\n  viewQuery: function _MatMenu_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n    }\n  },\n  hostVars: 3,\n  hostBindings: function _MatMenu_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n    }\n  },\n  inputs: {\n    backdropClass: \"backdropClass\",\n    ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n    ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n    xPosition: \"xPosition\",\n    yPosition: \"yPosition\",\n    overlapTrigger: [2, \"overlapTrigger\", \"overlapTrigger\", booleanAttribute],\n    hasBackdrop: [2, \"hasBackdrop\", \"hasBackdrop\", value => value == null ? null : booleanAttribute(value)],\n    panelClass: [0, \"class\", \"panelClass\"],\n    classList: \"classList\"\n  },\n  outputs: {\n    closed: \"closed\",\n    close: \"close\"\n  },\n  exportAs: [\"matMenu\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_MENU_PANEL,\n    useExisting: _MatMenu\n  }])],\n  ngContentSelectors: _c3,\n  decls: 1,\n  vars: 0,\n  consts: [[\"tabindex\", \"-1\", \"role\", \"menu\", 1, \"mat-mdc-menu-panel\", 3, \"click\", \"animationstart\", \"animationend\", \"animationcancel\", \"id\"], [1, \"mat-mdc-menu-content\"]],\n  template: function _MatMenu_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, _MatMenu_ng_template_0_Template, 3, 12, \"ng-template\");\n    }\n  },\n  styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenu, [{\n    type: Component,\n    args: [{\n      selector: 'mat-menu',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matMenu',\n      host: {\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null'\n      },\n      providers: [{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }],\n      template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-menu-panel-animations-disabled]=\\\"_animationsDisabled\\\"\\n    [class.mat-menu-panel-exit-animation]=\\\"_panelAnimationState === 'void'\\\"\\n    [class.mat-menu-panel-animating]=\\\"_isAnimating\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    (animationstart)=\\\"_onAnimationStart($event.animationName)\\\"\\n    (animationend)=\\\"_onAnimationDone($event.animationName)\\\"\\n    (animationcancel)=\\\"_onAnimationDone($event.animationName)\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"]\n    }]\n  }], () => [], {\n    _allItems: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: true\n      }]\n    }],\n    backdropClass: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    xPosition: [{\n      type: Input\n    }],\n    yPosition: [{\n      type: Input\n    }],\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef]\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: false\n      }]\n    }],\n    lazyContent: [{\n      type: ContentChild,\n      args: [MAT_MENU_CONTENT]\n    }],\n    overlapTrigger: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? null : booleanAttribute(value)\n      }]\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['class']\n    }],\n    classList: [{\n      type: Input\n    }],\n    closed: [{\n      type: Output\n    }],\n    close: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY\n};\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = {\n  passive: true\n};\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Mapping between menu panels and the last trigger that opened them. */\nconst PANELS_TO_TRIGGERS = new WeakMap();\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger {\n  /**\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  get _deprecatedMatMenuTriggerFor() {\n    return this.menu;\n  }\n  set _deprecatedMatMenuTriggerFor(v) {\n    this.menu = v;\n  }\n  /** References the menu instance that the trigger is associated with. */\n  get menu() {\n    return this._menu;\n  }\n  set menu(menu) {\n    var _this$_menuItemInstan;\n    if (menu === this._menu) {\n      return;\n    }\n    this._menu = menu;\n    this._menuCloseSubscription.unsubscribe();\n    if (menu) {\n      if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuRecursiveError();\n      }\n      this._menuCloseSubscription = menu.close.subscribe(reason => {\n        this._destroyMenu(reason);\n        // If a click closed the menu, we should close the entire chain of nested menus.\n        if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n          this._parentMaterialMenu.closed.emit(reason);\n        }\n      });\n    }\n    (_this$_menuItemInstan = this._menuItemInstance) === null || _this$_menuItemInstan === void 0 || _this$_menuItemInstan._setTriggersSubmenu(this.triggersSubmenu());\n  }\n  constructor() {\n    _defineProperty(this, \"_overlay\", inject(Overlay));\n    _defineProperty(this, \"_element\", inject(ElementRef));\n    _defineProperty(this, \"_viewContainerRef\", inject(ViewContainerRef));\n    _defineProperty(this, \"_menuItemInstance\", inject(MatMenuItem, {\n      optional: true,\n      self: true\n    }));\n    _defineProperty(this, \"_dir\", inject(Directionality, {\n      optional: true\n    }));\n    _defineProperty(this, \"_focusMonitor\", inject(FocusMonitor));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_scrollStrategy\", inject(MAT_MENU_SCROLL_STRATEGY));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_cleanupTouchstart\", void 0);\n    _defineProperty(this, \"_portal\", void 0);\n    _defineProperty(this, \"_overlayRef\", null);\n    _defineProperty(this, \"_menuOpen\", false);\n    _defineProperty(this, \"_closingActionsSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_hoverSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_menuCloseSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_pendingRemoval\", void 0);\n    /**\n     * We're specifically looking for a `MatMenu` here since the generic `MatMenuPanel`\n     * interface lacks some functionality around nested menus and animations.\n     */\n    _defineProperty(this, \"_parentMaterialMenu\", void 0);\n    /**\n     * Cached value of the padding of the parent menu panel.\n     * Used to offset sub-menus to compensate for the padding.\n     */\n    _defineProperty(this, \"_parentInnerPadding\", void 0);\n    // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n    _defineProperty(this, \"_openedBy\", undefined);\n    _defineProperty(this, \"_menu\", void 0);\n    /** Data to be passed along to any lazily-rendered content. */\n    _defineProperty(this, \"menuData\", void 0);\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n    _defineProperty(this, \"restoreFocus\", true);\n    /** Event emitted when the associated menu is opened. */\n    _defineProperty(this, \"menuOpened\", new EventEmitter());\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    _defineProperty(this, \"onMenuOpen\", this.menuOpened);\n    /** Event emitted when the associated menu is closed. */\n    _defineProperty(this, \"menuClosed\", new EventEmitter());\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    _defineProperty(this, \"onMenuClose\", this.menuClosed);\n    const parentMenu = inject(MAT_MENU_PANEL, {\n      optional: true\n    });\n    const renderer = inject(Renderer2);\n    this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n    this._cleanupTouchstart = _bindEventWithOptions(renderer, this._element.nativeElement, 'touchstart', event => {\n      if (!isFakeTouchstartFromScreenReader(event)) {\n        this._openedBy = 'touch';\n      }\n    }, passiveEventListenerOptions);\n  }\n  ngAfterContentInit() {\n    this._handleHover();\n  }\n  ngOnDestroy() {\n    var _this$_pendingRemoval;\n    if (this.menu && this._ownsMenu(this.menu)) {\n      PANELS_TO_TRIGGERS.delete(this.menu);\n    }\n    this._cleanupTouchstart();\n    (_this$_pendingRemoval = this._pendingRemoval) === null || _this$_pendingRemoval === void 0 || _this$_pendingRemoval.unsubscribe();\n    this._menuCloseSubscription.unsubscribe();\n    this._closingActionsSubscription.unsubscribe();\n    this._hoverSubscription.unsubscribe();\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n  }\n  /** Whether the menu is open. */\n  get menuOpen() {\n    return this._menuOpen;\n  }\n  /** The text direction of the containing app. */\n  get dir() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the menu triggers a sub-menu or a top-level one. */\n  triggersSubmenu() {\n    return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n  }\n  /** Toggles the menu between the open and closed states. */\n  toggleMenu() {\n    return this._menuOpen ? this.closeMenu() : this.openMenu();\n  }\n  /** Opens the menu. */\n  openMenu() {\n    var _this$_pendingRemoval2;\n    const menu = this.menu;\n    if (this._menuOpen || !menu) {\n      return;\n    }\n    (_this$_pendingRemoval2 = this._pendingRemoval) === null || _this$_pendingRemoval2 === void 0 || _this$_pendingRemoval2.unsubscribe();\n    const previousTrigger = PANELS_TO_TRIGGERS.get(menu);\n    PANELS_TO_TRIGGERS.set(menu, this);\n    // If the same menu is currently attached to another trigger,\n    // we need to close it so it doesn't end up in a broken state.\n    if (previousTrigger && previousTrigger !== this) {\n      previousTrigger.closeMenu();\n    }\n    const overlayRef = this._createOverlay(menu);\n    const overlayConfig = overlayRef.getConfig();\n    const positionStrategy = overlayConfig.positionStrategy;\n    this._setPosition(menu, positionStrategy);\n    overlayConfig.hasBackdrop = menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n    // We need the `hasAttached` check for the case where the user kicked off a removal animation,\n    // but re-entered the menu. Re-attaching the same portal will trigger an error otherwise.\n    if (!overlayRef.hasAttached()) {\n      var _menu$lazyContent;\n      overlayRef.attach(this._getPortal(menu));\n      (_menu$lazyContent = menu.lazyContent) === null || _menu$lazyContent === void 0 || _menu$lazyContent.attach(this.menuData);\n    }\n    this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n    menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n    menu.direction = this.dir;\n    menu.focusFirstItem(this._openedBy || 'program');\n    this._setIsMenuOpen(true);\n    if (menu instanceof MatMenu) {\n      menu._setIsOpen(true);\n      menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n        // Re-adjust the position without locking when the amount of items\n        // changes so that the overlay is allowed to pick a new optimal position.\n        positionStrategy.withLockedPosition(false).reapplyLastPosition();\n        positionStrategy.withLockedPosition(true);\n      });\n    }\n  }\n  /** Closes the menu. */\n  closeMenu() {\n    var _this$menu;\n    (_this$menu = this.menu) === null || _this$menu === void 0 || _this$menu.close.emit();\n  }\n  /**\n   * Focuses the menu trigger.\n   * @param origin Source of the menu trigger's focus.\n   */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Updates the position of the menu to ensure that it fits all options within the viewport.\n   */\n  updatePosition() {\n    var _this$_overlayRef;\n    (_this$_overlayRef = this._overlayRef) === null || _this$_overlayRef === void 0 || _this$_overlayRef.updatePosition();\n  }\n  /** Closes the menu and does the necessary cleanup. */\n  _destroyMenu(reason) {\n    var _this$_pendingRemoval3;\n    const overlayRef = this._overlayRef;\n    const menu = this._menu;\n    if (!overlayRef || !this.menuOpen) {\n      return;\n    }\n    this._closingActionsSubscription.unsubscribe();\n    (_this$_pendingRemoval3 = this._pendingRemoval) === null || _this$_pendingRemoval3 === void 0 || _this$_pendingRemoval3.unsubscribe();\n    // Note that we don't wait for the animation to finish if another trigger took\n    // over the menu, because the panel will end up empty which looks glitchy.\n    if (menu instanceof MatMenu && this._ownsMenu(menu)) {\n      this._pendingRemoval = menu._animationDone.pipe(take(1)).subscribe(() => {\n        var _menu$lazyContent2;\n        overlayRef.detach();\n        (_menu$lazyContent2 = menu.lazyContent) === null || _menu$lazyContent2 === void 0 || _menu$lazyContent2.detach();\n      });\n      menu._setIsOpen(false);\n    } else {\n      var _menu$lazyContent3;\n      overlayRef.detach();\n      menu === null || menu === void 0 || (_menu$lazyContent3 = menu.lazyContent) === null || _menu$lazyContent3 === void 0 || _menu$lazyContent3.detach();\n    }\n    if (menu && this._ownsMenu(menu)) {\n      PANELS_TO_TRIGGERS.delete(menu);\n    }\n    // Always restore focus if the user is navigating using the keyboard or the menu was opened\n    // programmatically. We don't restore for non-root triggers, because it can prevent focus\n    // from making it back to the root trigger when closing a long chain of menus by clicking\n    // on the backdrop.\n    if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n      this.focus(this._openedBy);\n    }\n    this._openedBy = undefined;\n    this._setIsMenuOpen(false);\n  }\n  // set state rather than toggle to support triggers sharing a menu\n  _setIsMenuOpen(isOpen) {\n    if (isOpen !== this._menuOpen) {\n      this._menuOpen = isOpen;\n      this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n      if (this.triggersSubmenu()) {\n        this._menuItemInstance._setHighlighted(isOpen);\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method creates the overlay from the provided menu's template and saves its\n   * OverlayRef so that it can be attached to the DOM when openMenu is called.\n   */\n  _createOverlay(menu) {\n    if (!this._overlayRef) {\n      const config = this._getOverlayConfig(menu);\n      this._subscribeToPositions(menu, config.positionStrategy);\n      this._overlayRef = this._overlay.create(config);\n      this._overlayRef.keydownEvents().subscribe(event => {\n        if (this.menu instanceof MatMenu) {\n          this.menu._handleKeydown(event);\n        }\n      });\n    }\n    return this._overlayRef;\n  }\n  /**\n   * This method builds the configuration object needed to create the overlay, the OverlayState.\n   * @returns OverlayConfig\n   */\n  _getOverlayConfig(menu) {\n    return new OverlayConfig({\n      positionStrategy: this._overlay.position().flexibleConnectedTo(this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n      backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n      panelClass: menu.overlayPanelClass,\n      scrollStrategy: this._scrollStrategy(),\n      direction: this._dir || 'ltr'\n    });\n  }\n  /**\n   * Listens to changes in the position of the overlay and sets the correct classes\n   * on the menu based on the new position. This ensures the animation origin is always\n   * correct, even if a fallback position is used for the overlay.\n   */\n  _subscribeToPositions(menu, position) {\n    if (menu.setPositionClasses) {\n      position.positionChanges.subscribe(change => {\n        this._ngZone.run(() => {\n          const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n          const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n          menu.setPositionClasses(posX, posY);\n        });\n      });\n    }\n  }\n  /**\n   * Sets the appropriate positions on a position strategy\n   * so the overlay connects with the trigger correctly.\n   * @param positionStrategy Strategy whose position to update.\n   */\n  _setPosition(menu, positionStrategy) {\n    let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n    let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n    let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n    let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n    let offsetY = 0;\n    if (this.triggersSubmenu()) {\n      // When the menu is a sub-menu, it should always align itself\n      // to the edges of the trigger, instead of overlapping it.\n      overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n      originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n      if (this._parentMaterialMenu) {\n        if (this._parentInnerPadding == null) {\n          const firstItem = this._parentMaterialMenu.items.first;\n          this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n        }\n        offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n      }\n    } else if (!menu.overlapTrigger) {\n      originY = overlayY === 'top' ? 'bottom' : 'top';\n      originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n    }\n    positionStrategy.withPositions([{\n      originX,\n      originY,\n      overlayX,\n      overlayY,\n      offsetY\n    }, {\n      originX: originFallbackX,\n      originY,\n      overlayX: overlayFallbackX,\n      overlayY,\n      offsetY\n    }, {\n      originX,\n      originY: originFallbackY,\n      overlayX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }, {\n      originX: originFallbackX,\n      originY: originFallbackY,\n      overlayX: overlayFallbackX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }]);\n  }\n  /** Returns a stream that emits whenever an action that should close the menu occurs. */\n  _menuClosingActions() {\n    const backdrop = this._overlayRef.backdropClick();\n    const detachments = this._overlayRef.detachments();\n    const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n    const hover = this._parentMaterialMenu ? this._parentMaterialMenu._hovered().pipe(filter(active => this._menuOpen && active !== this._menuItemInstance)) : of();\n    return merge(backdrop, parentClose, hover, detachments);\n  }\n  /** Handles mouse presses on the trigger. */\n  _handleMousedown(event) {\n    if (!isFakeMousedownFromScreenReader(event)) {\n      // Since right or middle button clicks won't trigger the `click` event,\n      // we shouldn't consider the menu as opened by mouse in those cases.\n      this._openedBy = event.button === 0 ? 'mouse' : undefined;\n      // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n      // we should prevent focus from moving onto it via click to avoid the\n      // highlight from lingering on the menu item.\n      if (this.triggersSubmenu()) {\n        event.preventDefault();\n      }\n    }\n  }\n  /** Handles key presses on the trigger. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    // Pressing enter on the trigger will trigger the click handler later.\n    if (keyCode === ENTER || keyCode === SPACE) {\n      this._openedBy = 'keyboard';\n    }\n    if (this.triggersSubmenu() && (keyCode === RIGHT_ARROW && this.dir === 'ltr' || keyCode === LEFT_ARROW && this.dir === 'rtl')) {\n      this._openedBy = 'keyboard';\n      this.openMenu();\n    }\n  }\n  /** Handles click events on the trigger. */\n  _handleClick(event) {\n    if (this.triggersSubmenu()) {\n      // Stop event propagation to avoid closing the parent menu.\n      event.stopPropagation();\n      this.openMenu();\n    } else {\n      this.toggleMenu();\n    }\n  }\n  /** Handles the cases where the user hovers over the trigger. */\n  _handleHover() {\n    // Subscribe to changes in the hovered item in order to toggle the panel.\n    if (this.triggersSubmenu() && this._parentMaterialMenu) {\n      this._hoverSubscription = this._parentMaterialMenu._hovered().subscribe(active => {\n        if (active === this._menuItemInstance && !active.disabled) {\n          this._openedBy = 'mouse';\n          this.openMenu();\n        }\n      });\n    }\n  }\n  /** Gets the portal that should be attached to the overlay. */\n  _getPortal(menu) {\n    // Note that we can avoid this check by keeping the portal on the menu panel.\n    // While it would be cleaner, we'd have to introduce another required method on\n    // `MatMenuPanel`, making it harder to consume.\n    if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n      this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n    }\n    return this._portal;\n  }\n  /**\n   * Determines whether the trigger owns a specific menu panel, at the current point in time.\n   * This allows us to distinguish the case where the same panel is passed into multiple triggers\n   * and multiple are open at a time.\n   */\n  _ownsMenu(menu) {\n    return PANELS_TO_TRIGGERS.get(menu) === this;\n  }\n}\n_MatMenuTrigger = MatMenuTrigger;\n_defineProperty(MatMenuTrigger, \"\\u0275fac\", function _MatMenuTrigger_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatMenuTrigger)();\n});\n_defineProperty(MatMenuTrigger, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatMenuTrigger,\n  selectors: [[\"\", \"mat-menu-trigger-for\", \"\"], [\"\", \"matMenuTriggerFor\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-menu-trigger\"],\n  hostVars: 3,\n  hostBindings: function _MatMenuTrigger_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _MatMenuTrigger_click_HostBindingHandler($event) {\n        return ctx._handleClick($event);\n      })(\"mousedown\", function _MatMenuTrigger_mousedown_HostBindingHandler($event) {\n        return ctx._handleMousedown($event);\n      })(\"keydown\", function _MatMenuTrigger_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-haspopup\", ctx.menu ? \"menu\" : null)(\"aria-expanded\", ctx.menuOpen)(\"aria-controls\", ctx.menuOpen ? ctx.menu.panelId : null);\n    }\n  },\n  inputs: {\n    _deprecatedMatMenuTriggerFor: [0, \"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"],\n    menu: [0, \"matMenuTriggerFor\", \"menu\"],\n    menuData: [0, \"matMenuTriggerData\", \"menuData\"],\n    restoreFocus: [0, \"matMenuTriggerRestoreFocus\", \"restoreFocus\"]\n  },\n  outputs: {\n    menuOpened: \"menuOpened\",\n    onMenuOpen: \"onMenuOpen\",\n    menuClosed: \"menuClosed\",\n    onMenuClose: \"onMenuClose\"\n  },\n  exportAs: [\"matMenuTrigger\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n      host: {\n        'class': 'mat-mdc-menu-trigger',\n        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n        '[attr.aria-expanded]': 'menuOpen',\n        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n        '(click)': '_handleClick($event)',\n        '(mousedown)': '_handleMousedown($event)',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      exportAs: 'matMenuTrigger'\n    }]\n  }], () => [], {\n    _deprecatedMatMenuTriggerFor: [{\n      type: Input,\n      args: ['mat-menu-trigger-for']\n    }],\n    menu: [{\n      type: Input,\n      args: ['matMenuTriggerFor']\n    }],\n    menuData: [{\n      type: Input,\n      args: ['matMenuTriggerData']\n    }],\n    restoreFocus: [{\n      type: Input,\n      args: ['matMenuTriggerRestoreFocus']\n    }],\n    menuOpened: [{\n      type: Output\n    }],\n    onMenuOpen: [{\n      type: Output\n    }],\n    menuClosed: [{\n      type: Output\n    }],\n    onMenuClose: [{\n      type: Output\n    }]\n  });\n})();\nclass MatMenuModule {}\n_MatMenuModule = MatMenuModule;\n_defineProperty(MatMenuModule, \"\\u0275fac\", function _MatMenuModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatMenuModule)();\n});\n_defineProperty(MatMenuModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatMenuModule,\n  imports: [MatRippleModule, MatCommonModule, OverlayModule, MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n  exports: [CdkScrollableModule, MatMenu, MatCommonModule, MatMenuItem, MatMenuContent, MatMenuTrigger]\n}));\n_defineProperty(MatMenuModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n  imports: [MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, MatCommonModule, OverlayModule, MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      exports: [CdkScrollableModule, MatMenu, MatCommonModule, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matMenuAnimations = {\n  // Represents:\n  // trigger('transformMenu', [\n  //   state(\n  //     'void',\n  //     style({\n  //       opacity: 0,\n  //       transform: 'scale(0.8)',\n  //     }),\n  //   ),\n  //   transition(\n  //     'void => enter',\n  //     animate(\n  //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n  //       style({\n  //         opacity: 1,\n  //         transform: 'scale(1)',\n  //       }),\n  //     ),\n  //   ),\n  //   transition('* => void', animate('100ms 25ms linear', style({opacity: 0}))),\n  // ])\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: {\n    type: 7,\n    name: 'transformMenu',\n    definitions: [{\n      type: 0,\n      name: 'void',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'scale(0.8)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => enter',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 1,\n            transform: 'scale(1)'\n          },\n          offset: null\n        },\n        timings: '120ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '100ms 25ms linear'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('fadeInItems', [\n  //   // TODO(crisbeto): this is inside the `transformMenu`\n  //   // now. Remove next time we do breaking changes.\n  //   state('showing', style({opacity: 1})),\n  //   transition('void => *', [\n  //     style({opacity: 0}),\n  //     animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n  //   ]),\n  // ])\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: {\n    type: 7,\n    name: 'fadeInItems',\n    definitions: [{\n      type: 0,\n      name: 'showing',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 1\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => *',\n      animation: [{\n        type: 6,\n        styles: {\n          opacity: 0\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, fadeInItems, matMenuAnimations, transformMenu };\n//# sourceMappingURL=menu.mjs.map", "map": {"version": 3, "names": ["i0", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "_c3", "_MatMenu_ng_template_0_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "_MatMenu_ng_template_0_Template_div_click_0_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "closed", "emit", "_MatMenu_ng_template_0_Template_div_animationstart_0_listener", "$event", "_onAnimationStart", "animationName", "_MatMenu_ng_template_0_Template_div_animationend_0_listener", "_onAnimationDone", "_MatMenu_ng_template_0_Template_div_animationcancel_0_listener", "ɵɵprojection", "ɵɵclassMap", "_classList", "ɵɵclassProp", "_animationsDisabled", "_panelAnimationState", "_isAnimating", "ɵɵproperty", "panelId", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "InjectionToken", "inject", "ElementRef", "ChangeDetectorRef", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "TemplateRef", "ApplicationRef", "Injector", "ViewContainerRef", "Directive", "QueryList", "EventEmitter", "ANIMATION_MODULE_TYPE", "afterNextRender", "ContentChildren", "ViewChild", "ContentChild", "Output", "NgZone", "Renderer2", "NgModule", "FocusMonitor", "_IdGenerator", "FocusKeyManager", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "UP_ARROW", "DOWN_ARROW", "RIGHT_ARROW", "LEFT_ARROW", "ESCAPE", "hasModifierKey", "ENTER", "SPACE", "Subject", "merge", "Subscription", "of", "startWith", "switchMap", "takeUntil", "take", "filter", "DOCUMENT", "_CdkPrivateStyleLoader", "_", "_StructuralStylesLoader", "M", "<PERSON><PERSON><PERSON><PERSON>", "TemplatePortal", "DomPortalOutlet", "Directionality", "Overlay", "OverlayConfig", "OverlayModule", "_bindEventWithOptions", "CdkScrollableModule", "MatRippleModule", "MatCommonModule", "MAT_MENU_PANEL", "MatMenuItem", "constructor", "_this$_parentMenu", "_this$_parentMenu$add", "_defineProperty", "optional", "load", "_parentMenu", "addItem", "call", "focus", "origin", "options", "_focusMonitor", "focusVia", "_getHostElement", "_focused", "next", "ngAfterViewInit", "monitor", "_elementRef", "ngOnDestroy", "stopMonitoring", "removeItem", "_hovered", "complete", "_getTabIndex", "disabled", "nativeElement", "_checkDisabled", "event", "preventDefault", "stopPropagation", "_handleMouseEnter", "get<PERSON><PERSON><PERSON>", "_clone$textContent", "clone", "cloneNode", "icons", "querySelectorAll", "i", "length", "remove", "textContent", "trim", "_setHighlighted", "isHighlighted", "_highlighted", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_setTriggersSubmenu", "triggersSubmenu", "_triggersSubmenu", "_hasFocus", "_document", "activeElement", "_MatMenuItem", "_MatMenuItem_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "_MatMenuItem_HostBindings", "_MatMenuItem_click_HostBindingHandler", "_MatMenuItem_mouseenter_HostBindingHandler", "role", "inputs", "disable<PERSON><PERSON><PERSON>", "exportAs", "attrs", "_c0", "ngContentSelectors", "_c2", "decls", "vars", "consts", "template", "_MatMenuItem_Template", "ɵɵprojectionDef", "_c1", "ɵɵtemplate", "_MatMenuItem_Conditional_4_Template", "ɵɵadvance", "ɵɵconditional", "dependencies", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "imports", "transform", "throwMatMenuInvalidPositionX", "Error", "throwMatMenuInvalidPositionY", "throwMatMenuRecursiveError", "MAT_MENU_CONTENT", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attach", "context", "_portal", "_template", "_viewContainerRef", "detach", "_outlet", "createElement", "_appRef", "_injector", "element", "elementRef", "parentNode", "insertBefore", "outletElement", "_attached", "_this$_portal", "isAttached", "_this$_outlet", "dispose", "_Mat<PERSON><PERSON>u<PERSON><PERSON>nt", "_MatMenuContent_Factory", "ɵɵdefineDirective", "features", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "MAT_MENU_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_MENU_DEFAULT_OPTIONS_FACTORY", "overlapTrigger", "xPosition", "yPosition", "backdropClass", "ENTER_ANIMATION", "EXIT_ANIMATION", "MatMenu", "_xPosition", "value", "setPositionClasses", "_yPosition", "panelClass", "classes", "previousPanelClass", "_previousPanelClass", "newClassList", "_objectSpread", "split", "for<PERSON>ach", "className", "classList", "getId", "defaultOptions", "overlayPanelClass", "hasBackdrop", "ngOnInit", "ngAfterContentInit", "_updateDirectDescendants", "_keyManager", "_directDescendantItems", "withWrap", "withTypeAhead", "withHomeAndEnd", "tabOut", "subscribe", "changes", "pipe", "items", "map", "item", "focusedItem", "updateActiveItem", "itemsList", "_manager$activeItem", "manager", "activeItem", "toArray", "index", "Math", "max", "min", "activeItemIndex", "setActiveItem", "setNextItemActive", "_this$_keyManager", "_this$_firstItemFocus", "destroy", "_firstItemFocusRef", "clearTimeout", "_exitFallbackTimeout", "itemChanges", "_item", "_handleKeydown", "keyCode", "parentMenu", "direction", "setFocusOrigin", "onKeydown", "focusFirstItem", "_this$_firstItemFocus2", "menuPanel", "_resolvePanel", "contains", "document", "setFirstItemActive", "injector", "resetActiveItem", "setElevation", "_depth", "posX", "posY", "state", "isExit", "undefined", "_animationDone", "_setIsOpen", "isOpen", "scrollTop", "setTimeout", "_allItems", "reset", "notifyOn<PERSON><PERSON>es", "first", "closest", "_MatMenu", "_MatMenu_Factory", "contentQueries", "_MatMenu_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "lazyContent", "viewQuery", "_MatMenu_Query", "ɵɵviewQuery", "templateRef", "_MatMenu_HostBindings", "outputs", "close", "_MatMenu_Template", "styles", "descendants", "MAT_MENU_SCROLL_STRATEGY", "overlay", "scrollStrategies", "reposition", "MAT_MENU_SCROLL_STRATEGY_FACTORY", "MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "passiveEventListenerOptions", "passive", "MENU_PANEL_TOP_PADDING", "PANELS_TO_TRIGGERS", "WeakMap", "MatMenuTrigger", "_deprecatedMatMenuTriggerFor", "menu", "v", "_menu", "_this$_menuItemInstan", "_menuCloseSubscription", "unsubscribe", "_parentMaterialMenu", "reason", "_destroyMenu", "_menuItemInstance", "self", "EMPTY", "menuOpened", "menuClosed", "renderer", "_cleanupTouchstart", "_element", "_openedBy", "_handleHover", "_this$_pendingRemoval", "_ownsMenu", "delete", "_pending<PERSON><PERSON><PERSON><PERSON>", "_closingActionsSubscription", "_hoverSubscription", "_overlayRef", "menuOpen", "_menuOpen", "dir", "_dir", "toggleMenu", "closeMenu", "openMenu", "_this$_pendingRemoval2", "previousTrigger", "get", "set", "overlayRef", "_createOverlay", "overlayConfig", "getConfig", "positionStrategy", "_setPosition", "has<PERSON>tta<PERSON>", "_menu$lazyContent", "_getPortal", "menuData", "_menuClosingActions", "_setIsMenuOpen", "withLockedPosition", "reapplyLastPosition", "_this$menu", "updatePosition", "_this$_overlayRef", "_this$_pendingRemoval3", "_menu$lazyContent2", "_menu$lazyContent3", "restoreFocus", "config", "_getOverlayConfig", "_subscribeToPositions", "_overlay", "create", "keydownEvents", "position", "flexibleConnectedTo", "withGrowAfterOpen", "withTransformOriginOn", "scrollStrategy", "_scrollStrategy", "position<PERSON><PERSON>es", "change", "_ngZone", "run", "connectionPair", "overlayX", "overlayY", "originX", "originFallbackX", "overlayFallbackY", "originY", "originFallbackY", "overlayFallbackX", "offsetY", "_parentInnerPadding", "firstItem", "offsetTop", "withPositions", "backdrop", "backdropClick", "detachments", "parentClose", "hover", "active", "_handleMousedown", "button", "_handleClick", "_MatMenuTrigger", "_MatMenuTrigger_Factory", "_MatMenuTrigger_HostBindings", "_MatMenuTrigger_click_HostBindingHandler", "_MatMenuTrigger_mousedown_HostBindingHandler", "_MatMenuTrigger_keydown_HostBindingHandler", "onMenuOpen", "onMenuClose", "MatMenuModule", "_MatMenuModule", "_MatMenuModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "matMenuAnimations", "transformMenu", "name", "definitions", "opacity", "offset", "expr", "animation", "timings", "fadeInItems"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/menu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, ChangeDetectorRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, TemplateRef, ApplicationRef, Injector, ViewContainerRef, Directive, QueryList, EventEmitter, ANIMATION_MODULE_TYPE, afterNextRender, ContentChildren, ViewChild, ContentChild, Output, NgZone, Renderer2, NgModule } from '@angular/core';\nimport { FocusMonitor, _IdGenerator, FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of } from 'rxjs';\nimport { startWith, switchMap, takeUntil, take, filter } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { _bindEventWithOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/coercion';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem {\n    _elementRef = inject(ElementRef);\n    _document = inject(DOCUMENT);\n    _focusMonitor = inject(FocusMonitor);\n    _parentMenu = inject(MAT_MENU_PANEL, { optional: true });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    /** ARIA role for the menu item. */\n    role = 'menuitem';\n    /** Whether the menu item is disabled. */\n    disabled = false;\n    /** Whether ripples are disabled on the menu item. */\n    disableRipple = false;\n    /** Stream that emits when the menu item is hovered. */\n    _hovered = new Subject();\n    /** Stream that emits when the menu item is focused. */\n    _focused = new Subject();\n    /** Whether the menu item is highlighted. */\n    _highlighted = false;\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n    _triggersSubmenu = false;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        this._parentMenu?.addItem?.(this);\n    }\n    /** Focuses the menu item. */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n        }\n        else {\n            this._getHostElement().focus(options);\n        }\n        this._focused.next(this);\n    }\n    ngAfterViewInit() {\n        if (this._focusMonitor) {\n            // Start monitoring the element, so it gets the appropriate focused classes. We want\n            // to show the focus style for menu items only when the focus was not caused by a\n            // mouse or touch interaction.\n            this._focusMonitor.monitor(this._elementRef, false);\n        }\n    }\n    ngOnDestroy() {\n        if (this._focusMonitor) {\n            this._focusMonitor.stopMonitoring(this._elementRef);\n        }\n        if (this._parentMenu && this._parentMenu.removeItem) {\n            this._parentMenu.removeItem(this);\n        }\n        this._hovered.complete();\n        this._focused.complete();\n    }\n    /** Used to set the `tabindex`. */\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Returns the host DOM element. */\n    _getHostElement() {\n        return this._elementRef.nativeElement;\n    }\n    /** Prevents the default element actions if it is disabled. */\n    _checkDisabled(event) {\n        if (this.disabled) {\n            event.preventDefault();\n            event.stopPropagation();\n        }\n    }\n    /** Emits to the hover stream. */\n    _handleMouseEnter() {\n        this._hovered.next(this);\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        const clone = this._elementRef.nativeElement.cloneNode(true);\n        const icons = clone.querySelectorAll('mat-icon, .material-icons');\n        // Strip away icons, so they don't show up in the text.\n        for (let i = 0; i < icons.length; i++) {\n            icons[i].remove();\n        }\n        return clone.textContent?.trim() || '';\n    }\n    _setHighlighted(isHighlighted) {\n        // We need to mark this for check for the case where the content is coming from a\n        // `matMenuContent` whose change detection tree is at the declaration position,\n        // not the insertion position. See #23175.\n        this._highlighted = isHighlighted;\n        this._changeDetectorRef.markForCheck();\n    }\n    _setTriggersSubmenu(triggersSubmenu) {\n        this._triggersSubmenu = triggersSubmenu;\n        this._changeDetectorRef.markForCheck();\n    }\n    _hasFocus() {\n        return this._document && this._document.activeElement === this._getHostElement();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenuItem, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatMenuItem, isStandalone: true, selector: \"[mat-menu-item]\", inputs: { role: \"role\", disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute] }, host: { listeners: { \"click\": \"_checkDisabled($event)\", \"mouseenter\": \"_handleMouseEnter()\" }, properties: { \"attr.role\": \"role\", \"class.mat-mdc-menu-item-highlighted\": \"_highlighted\", \"class.mat-mdc-menu-item-submenu-trigger\": \"_triggersSubmenu\", \"attr.tabindex\": \"_getTabIndex()\", \"attr.aria-disabled\": \"disabled\", \"attr.disabled\": \"disabled || null\" }, classAttribute: \"mat-mdc-menu-item mat-focus-indicator\" }, exportAs: [\"matMenuItem\"], ngImport: i0, template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\", dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenuItem, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-menu-item]', exportAs: 'matMenuItem', host: {\n                        '[attr.role]': 'role',\n                        'class': 'mat-mdc-menu-item mat-focus-indicator',\n                        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n                        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n                        '[attr.tabindex]': '_getTabIndex()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.disabled]': 'disabled || null',\n                        '(click)': '_checkDisabled($event)',\n                        '(mouseenter)': '_handleMouseEnter()',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, imports: [MatRipple], template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { role: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n    throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n    throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n    throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` +\n        `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent {\n    _template = inject(TemplateRef);\n    _appRef = inject(ApplicationRef);\n    _injector = inject(Injector);\n    _viewContainerRef = inject(ViewContainerRef);\n    _document = inject(DOCUMENT);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _portal;\n    _outlet;\n    /** Emits when the menu content has been attached. */\n    _attached = new Subject();\n    constructor() { }\n    /**\n     * Attaches the content with a particular context.\n     * @docs-private\n     */\n    attach(context = {}) {\n        if (!this._portal) {\n            this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n        }\n        this.detach();\n        if (!this._outlet) {\n            this._outlet = new DomPortalOutlet(this._document.createElement('div'), null, this._appRef, this._injector);\n        }\n        const element = this._template.elementRef.nativeElement;\n        // Because we support opening the same menu from different triggers (which in turn have their\n        // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n        // risk it staying attached to a pane that's no longer in the DOM.\n        element.parentNode.insertBefore(this._outlet.outletElement, element);\n        // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n        // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n        // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n        // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n        // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n        this._changeDetectorRef.markForCheck();\n        this._portal.attach(this._outlet, context);\n        this._attached.next();\n    }\n    /**\n     * Detaches the content.\n     * @docs-private\n     */\n    detach() {\n        if (this._portal?.isAttached) {\n            this._portal.detach();\n        }\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._outlet?.dispose();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenuContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatMenuContent, isStandalone: true, selector: \"ng-template[matMenuContent]\", providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenuContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matMenuContent]',\n                    providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }],\n                }]\n        }], ctorParameters: () => [] });\n\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n    providedIn: 'root',\n    factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        overlapTrigger: false,\n        xPosition: 'after',\n        yPosition: 'below',\n        backdropClass: 'cdk-overlay-transparent-backdrop',\n    };\n}\n/** Name of the enter animation `@keyframes`. */\nconst ENTER_ANIMATION = '_mat-menu-enter';\n/** Name of the exit animation `@keyframes`. */\nconst EXIT_ANIMATION = '_mat-menu-exit';\nclass MatMenu {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _keyManager;\n    _xPosition;\n    _yPosition;\n    _firstItemFocusRef;\n    _exitFallbackTimeout;\n    /** Whether animations are currently disabled. */\n    _animationsDisabled;\n    /** All items inside the menu. Includes items nested inside another menu. */\n    _allItems;\n    /** Only the direct descendant menu items. */\n    _directDescendantItems = new QueryList();\n    /** Classes to be applied to the menu panel. */\n    _classList = {};\n    /** Current state of the panel animation. */\n    _panelAnimationState = 'void';\n    /** Emits whenever an animation on the menu completes. */\n    _animationDone = new Subject();\n    /** Whether the menu is animating. */\n    _isAnimating = false;\n    /** Parent menu of the current menu panel. */\n    parentMenu;\n    /** Layout direction of the menu. */\n    direction;\n    /** Class or list of classes to be added to the overlay panel. */\n    overlayPanelClass;\n    /** Class to be added to the backdrop element. */\n    backdropClass;\n    /** aria-label for the menu panel. */\n    ariaLabel;\n    /** aria-labelledby for the menu panel. */\n    ariaLabelledby;\n    /** aria-describedby for the menu panel. */\n    ariaDescribedby;\n    /** Position of the menu in the X axis. */\n    get xPosition() {\n        return this._xPosition;\n    }\n    set xPosition(value) {\n        if (value !== 'before' &&\n            value !== 'after' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionX();\n        }\n        this._xPosition = value;\n        this.setPositionClasses();\n    }\n    /** Position of the menu in the Y axis. */\n    get yPosition() {\n        return this._yPosition;\n    }\n    set yPosition(value) {\n        if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionY();\n        }\n        this._yPosition = value;\n        this.setPositionClasses();\n    }\n    /** @docs-private */\n    templateRef;\n    /**\n     * List of the items inside of a menu.\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    items;\n    /**\n     * Menu content that will be rendered lazily.\n     * @docs-private\n     */\n    lazyContent;\n    /** Whether the menu should overlap its trigger. */\n    overlapTrigger;\n    /** Whether the menu has a backdrop. */\n    hasBackdrop;\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @param classes list of class names\n     */\n    set panelClass(classes) {\n        const previousPanelClass = this._previousPanelClass;\n        const newClassList = { ...this._classList };\n        if (previousPanelClass && previousPanelClass.length) {\n            previousPanelClass.split(' ').forEach((className) => {\n                newClassList[className] = false;\n            });\n        }\n        this._previousPanelClass = classes;\n        if (classes && classes.length) {\n            classes.split(' ').forEach((className) => {\n                newClassList[className] = true;\n            });\n            this._elementRef.nativeElement.className = '';\n        }\n        this._classList = newClassList;\n    }\n    _previousPanelClass;\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @deprecated Use `panelClass` instead.\n     * @breaking-change 8.0.0\n     */\n    get classList() {\n        return this.panelClass;\n    }\n    set classList(classes) {\n        this.panelClass = classes;\n    }\n    /** Event emitted when the menu is closed. */\n    closed = new EventEmitter();\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n    close = this.closed;\n    panelId = inject(_IdGenerator).getId('mat-menu-panel-');\n    constructor() {\n        const defaultOptions = inject(MAT_MENU_DEFAULT_OPTIONS);\n        this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n        this._xPosition = defaultOptions.xPosition;\n        this._yPosition = defaultOptions.yPosition;\n        this.backdropClass = defaultOptions.backdropClass;\n        this.overlapTrigger = defaultOptions.overlapTrigger;\n        this.hasBackdrop = defaultOptions.hasBackdrop;\n        this._animationsDisabled = inject(ANIMATION_MODULE_TYPE, { optional: true }) === 'NoopAnimations';\n    }\n    ngOnInit() {\n        this.setPositionClasses();\n    }\n    ngAfterContentInit() {\n        this._updateDirectDescendants();\n        this._keyManager = new FocusKeyManager(this._directDescendantItems)\n            .withWrap()\n            .withTypeAhead()\n            .withHomeAndEnd();\n        this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n        // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n        // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n        // is internal and we know that it gets completed on destroy.\n        this._directDescendantItems.changes\n            .pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._focused))))\n            .subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n        this._directDescendantItems.changes.subscribe((itemsList) => {\n            // Move focus to another item, if the active item is removed from the list.\n            // We need to debounce the callback, because multiple items might be removed\n            // in quick succession.\n            const manager = this._keyManager;\n            if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n                const items = itemsList.toArray();\n                const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n                if (items[index] && !items[index].disabled) {\n                    manager.setActiveItem(index);\n                }\n                else {\n                    manager.setNextItemActive();\n                }\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._directDescendantItems.destroy();\n        this.closed.complete();\n        this._firstItemFocusRef?.destroy();\n        clearTimeout(this._exitFallbackTimeout);\n    }\n    /** Stream that emits whenever the hovered menu item changes. */\n    _hovered() {\n        // Coerce the `changes` property because Angular types it as `Observable<any>`\n        const itemChanges = this._directDescendantItems.changes;\n        return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._hovered))));\n    }\n    /*\n     * Registers a menu item with the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    addItem(_item) { }\n    /**\n     * Removes an item from the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    removeItem(_item) { }\n    /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        switch (keyCode) {\n            case ESCAPE:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this.closed.emit('keydown');\n                }\n                break;\n            case LEFT_ARROW:\n                if (this.parentMenu && this.direction === 'ltr') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            case RIGHT_ARROW:\n                if (this.parentMenu && this.direction === 'rtl') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            default:\n                if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n                    manager.setFocusOrigin('keyboard');\n                }\n                manager.onKeydown(event);\n                return;\n        }\n    }\n    /**\n     * Focus the first item in the menu.\n     * @param origin Action from which the focus originated. Used to set the correct styling.\n     */\n    focusFirstItem(origin = 'program') {\n        // Wait for `afterNextRender` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n        this._firstItemFocusRef?.destroy();\n        this._firstItemFocusRef = afterNextRender(() => {\n            const menuPanel = this._resolvePanel();\n            // If an item in the menuPanel is already focused, avoid overriding the focus.\n            if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n                const manager = this._keyManager;\n                manager.setFocusOrigin(origin).setFirstItemActive();\n                // If there's no active item at this point, it means that all the items are disabled.\n                // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n                // give _some_ feedback to screen readers.\n                if (!manager.activeItem && menuPanel) {\n                    menuPanel.focus();\n                }\n            }\n        }, { injector: this._injector });\n    }\n    /**\n     * Resets the active item in the menu. This is used when the menu is opened, allowing\n     * the user to start from the first option when pressing the down arrow.\n     */\n    resetActiveItem() {\n        this._keyManager.setActiveItem(-1);\n    }\n    /**\n     * @deprecated No longer used and will be removed.\n     * @breaking-change 21.0.0\n     */\n    setElevation(_depth) { }\n    /**\n     * Adds classes to the menu panel based on its position. Can be used by\n     * consumers to add specific styling based on the position.\n     * @param posX Position of the menu along the x axis.\n     * @param posY Position of the menu along the y axis.\n     * @docs-private\n     */\n    setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n        this._classList = {\n            ...this._classList,\n            ['mat-menu-before']: posX === 'before',\n            ['mat-menu-after']: posX === 'after',\n            ['mat-menu-above']: posY === 'above',\n            ['mat-menu-below']: posY === 'below',\n        };\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Callback that is invoked when the panel animation completes. */\n    _onAnimationDone(state) {\n        const isExit = state === EXIT_ANIMATION;\n        if (isExit || state === ENTER_ANIMATION) {\n            if (isExit) {\n                clearTimeout(this._exitFallbackTimeout);\n                this._exitFallbackTimeout = undefined;\n            }\n            this._animationDone.next(isExit ? 'void' : 'enter');\n            this._isAnimating = false;\n        }\n    }\n    _onAnimationStart(state) {\n        if (state === ENTER_ANIMATION || state === EXIT_ANIMATION) {\n            this._isAnimating = true;\n        }\n    }\n    _setIsOpen(isOpen) {\n        this._panelAnimationState = isOpen ? 'enter' : 'void';\n        if (isOpen) {\n            if (this._keyManager.activeItemIndex === 0) {\n                // Scroll the content element to the top as soon as the animation starts. This is necessary,\n                // because we move focus to the first item while it's still being animated, which can throw\n                // the browser off when it determines the scroll position. Alternatively we can move focus\n                // when the animation is done, however moving focus asynchronously will interrupt screen\n                // readers which are in the process of reading out the menu already. We take the `element`\n                // from the `event` since we can't use a `ViewChild` to access the pane.\n                const menuPanel = this._resolvePanel();\n                if (menuPanel) {\n                    menuPanel.scrollTop = 0;\n                }\n            }\n        }\n        else if (!this._animationsDisabled) {\n            // Some apps do `* { animation: none !important; }` in tests which will prevent the\n            // `animationend` event from firing. Since the exit animation is loading-bearing for\n            // removing the content from the DOM, add a fallback timer.\n            this._exitFallbackTimeout = setTimeout(() => this._onAnimationDone(EXIT_ANIMATION), 200);\n        }\n        // Animation events won't fire when animations are disabled so we simulate them.\n        if (this._animationsDisabled) {\n            setTimeout(() => {\n                this._onAnimationDone(isOpen ? ENTER_ANIMATION : EXIT_ANIMATION);\n            });\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Sets up a stream that will keep track of any newly-added menu items and will update the list\n     * of direct descendants. We collect the descendants this way, because `_allItems` can include\n     * items that are part of child menus, and using a custom way of registering items is unreliable\n     * when it comes to maintaining the item order.\n     */\n    _updateDirectDescendants() {\n        this._allItems.changes\n            .pipe(startWith(this._allItems))\n            .subscribe((items) => {\n            this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n            this._directDescendantItems.notifyOnChanges();\n        });\n    }\n    /** Gets the menu panel DOM node. */\n    _resolvePanel() {\n        let menuPanel = null;\n        if (this._directDescendantItems.length) {\n            // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n            // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n            // because the panel is inside an `ng-template`. We work around it by starting from one of\n            // the items and walking up the DOM.\n            menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n        }\n        return menuPanel;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenu, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatMenu, isStandalone: true, selector: \"mat-menu\", inputs: { backdropClass: \"backdropClass\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], xPosition: \"xPosition\", yPosition: \"yPosition\", overlapTrigger: [\"overlapTrigger\", \"overlapTrigger\", booleanAttribute], hasBackdrop: [\"hasBackdrop\", \"hasBackdrop\", (value) => (value == null ? null : booleanAttribute(value))], panelClass: [\"class\", \"panelClass\"], classList: \"classList\" }, outputs: { closed: \"closed\", close: \"close\" }, host: { properties: { \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.aria-describedby\": \"null\" } }, providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], queries: [{ propertyName: \"lazyContent\", first: true, predicate: MAT_MENU_CONTENT, descendants: true }, { propertyName: \"_allItems\", predicate: MatMenuItem, descendants: true }, { propertyName: \"items\", predicate: MatMenuItem }], viewQueries: [{ propertyName: \"templateRef\", first: true, predicate: TemplateRef, descendants: true }], exportAs: [\"matMenu\"], ngImport: i0, template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-menu-panel-animations-disabled]=\\\"_animationsDisabled\\\"\\n    [class.mat-menu-panel-exit-animation]=\\\"_panelAnimationState === 'void'\\\"\\n    [class.mat-menu-panel-animating]=\\\"_isAnimating\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    (animationstart)=\\\"_onAnimationStart($event.animationName)\\\"\\n    (animationend)=\\\"_onAnimationDone($event.animationName)\\\"\\n    (animationcancel)=\\\"_onAnimationDone($event.animationName)\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-menu', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, exportAs: 'matMenu', host: {\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.aria-describedby]': 'null',\n                    }, providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-menu-panel-animations-disabled]=\\\"_animationsDisabled\\\"\\n    [class.mat-menu-panel-exit-animation]=\\\"_panelAnimationState === 'void'\\\"\\n    [class.mat-menu-panel-animating]=\\\"_isAnimating\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    (animationstart)=\\\"_onAnimationStart($event.animationName)\\\"\\n    (animationend)=\\\"_onAnimationDone($event.animationName)\\\"\\n    (animationcancel)=\\\"_onAnimationDone($event.animationName)\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _allItems: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: true }]\n            }], backdropClass: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], xPosition: [{\n                type: Input\n            }], yPosition: [{\n                type: Input\n            }], templateRef: [{\n                type: ViewChild,\n                args: [TemplateRef]\n            }], items: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: false }]\n            }], lazyContent: [{\n                type: ContentChild,\n                args: [MAT_MENU_CONTENT]\n            }], overlapTrigger: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hasBackdrop: [{\n                type: Input,\n                args: [{ transform: (value) => (value == null ? null : booleanAttribute(value)) }]\n            }], panelClass: [{\n                type: Input,\n                args: ['class']\n            }], classList: [{\n                type: Input\n            }], closed: [{\n                type: Output\n            }], close: [{\n                type: Output\n            }] } });\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition();\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_MENU_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY,\n};\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = { passive: true };\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Mapping between menu panels and the last trigger that opened them. */\nconst PANELS_TO_TRIGGERS = new WeakMap();\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger {\n    _overlay = inject(Overlay);\n    _element = inject(ElementRef);\n    _viewContainerRef = inject(ViewContainerRef);\n    _menuItemInstance = inject(MatMenuItem, { optional: true, self: true });\n    _dir = inject(Directionality, { optional: true });\n    _focusMonitor = inject(FocusMonitor);\n    _ngZone = inject(NgZone);\n    _scrollStrategy = inject(MAT_MENU_SCROLL_STRATEGY);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _cleanupTouchstart;\n    _portal;\n    _overlayRef = null;\n    _menuOpen = false;\n    _closingActionsSubscription = Subscription.EMPTY;\n    _hoverSubscription = Subscription.EMPTY;\n    _menuCloseSubscription = Subscription.EMPTY;\n    _pendingRemoval;\n    /**\n     * We're specifically looking for a `MatMenu` here since the generic `MatMenuPanel`\n     * interface lacks some functionality around nested menus and animations.\n     */\n    _parentMaterialMenu;\n    /**\n     * Cached value of the padding of the parent menu panel.\n     * Used to offset sub-menus to compensate for the padding.\n     */\n    _parentInnerPadding;\n    // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n    _openedBy = undefined;\n    /**\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    get _deprecatedMatMenuTriggerFor() {\n        return this.menu;\n    }\n    set _deprecatedMatMenuTriggerFor(v) {\n        this.menu = v;\n    }\n    /** References the menu instance that the trigger is associated with. */\n    get menu() {\n        return this._menu;\n    }\n    set menu(menu) {\n        if (menu === this._menu) {\n            return;\n        }\n        this._menu = menu;\n        this._menuCloseSubscription.unsubscribe();\n        if (menu) {\n            if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwMatMenuRecursiveError();\n            }\n            this._menuCloseSubscription = menu.close.subscribe((reason) => {\n                this._destroyMenu(reason);\n                // If a click closed the menu, we should close the entire chain of nested menus.\n                if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n                    this._parentMaterialMenu.closed.emit(reason);\n                }\n            });\n        }\n        this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n    }\n    _menu;\n    /** Data to be passed along to any lazily-rendered content. */\n    menuData;\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n    restoreFocus = true;\n    /** Event emitted when the associated menu is opened. */\n    menuOpened = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    onMenuOpen = this.menuOpened;\n    /** Event emitted when the associated menu is closed. */\n    menuClosed = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    onMenuClose = this.menuClosed;\n    constructor() {\n        const parentMenu = inject(MAT_MENU_PANEL, { optional: true });\n        const renderer = inject(Renderer2);\n        this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n        this._cleanupTouchstart = _bindEventWithOptions(renderer, this._element.nativeElement, 'touchstart', (event) => {\n            if (!isFakeTouchstartFromScreenReader(event)) {\n                this._openedBy = 'touch';\n            }\n        }, passiveEventListenerOptions);\n    }\n    ngAfterContentInit() {\n        this._handleHover();\n    }\n    ngOnDestroy() {\n        if (this.menu && this._ownsMenu(this.menu)) {\n            PANELS_TO_TRIGGERS.delete(this.menu);\n        }\n        this._cleanupTouchstart();\n        this._pendingRemoval?.unsubscribe();\n        this._menuCloseSubscription.unsubscribe();\n        this._closingActionsSubscription.unsubscribe();\n        this._hoverSubscription.unsubscribe();\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n    }\n    /** Whether the menu is open. */\n    get menuOpen() {\n        return this._menuOpen;\n    }\n    /** The text direction of the containing app. */\n    get dir() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the menu triggers a sub-menu or a top-level one. */\n    triggersSubmenu() {\n        return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n    }\n    /** Toggles the menu between the open and closed states. */\n    toggleMenu() {\n        return this._menuOpen ? this.closeMenu() : this.openMenu();\n    }\n    /** Opens the menu. */\n    openMenu() {\n        const menu = this.menu;\n        if (this._menuOpen || !menu) {\n            return;\n        }\n        this._pendingRemoval?.unsubscribe();\n        const previousTrigger = PANELS_TO_TRIGGERS.get(menu);\n        PANELS_TO_TRIGGERS.set(menu, this);\n        // If the same menu is currently attached to another trigger,\n        // we need to close it so it doesn't end up in a broken state.\n        if (previousTrigger && previousTrigger !== this) {\n            previousTrigger.closeMenu();\n        }\n        const overlayRef = this._createOverlay(menu);\n        const overlayConfig = overlayRef.getConfig();\n        const positionStrategy = overlayConfig.positionStrategy;\n        this._setPosition(menu, positionStrategy);\n        overlayConfig.hasBackdrop =\n            menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n        // We need the `hasAttached` check for the case where the user kicked off a removal animation,\n        // but re-entered the menu. Re-attaching the same portal will trigger an error otherwise.\n        if (!overlayRef.hasAttached()) {\n            overlayRef.attach(this._getPortal(menu));\n            menu.lazyContent?.attach(this.menuData);\n        }\n        this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n        menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n        menu.direction = this.dir;\n        menu.focusFirstItem(this._openedBy || 'program');\n        this._setIsMenuOpen(true);\n        if (menu instanceof MatMenu) {\n            menu._setIsOpen(true);\n            menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n                // Re-adjust the position without locking when the amount of items\n                // changes so that the overlay is allowed to pick a new optimal position.\n                positionStrategy.withLockedPosition(false).reapplyLastPosition();\n                positionStrategy.withLockedPosition(true);\n            });\n        }\n    }\n    /** Closes the menu. */\n    closeMenu() {\n        this.menu?.close.emit();\n    }\n    /**\n     * Focuses the menu trigger.\n     * @param origin Source of the menu trigger's focus.\n     */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    /**\n     * Updates the position of the menu to ensure that it fits all options within the viewport.\n     */\n    updatePosition() {\n        this._overlayRef?.updatePosition();\n    }\n    /** Closes the menu and does the necessary cleanup. */\n    _destroyMenu(reason) {\n        const overlayRef = this._overlayRef;\n        const menu = this._menu;\n        if (!overlayRef || !this.menuOpen) {\n            return;\n        }\n        this._closingActionsSubscription.unsubscribe();\n        this._pendingRemoval?.unsubscribe();\n        // Note that we don't wait for the animation to finish if another trigger took\n        // over the menu, because the panel will end up empty which looks glitchy.\n        if (menu instanceof MatMenu && this._ownsMenu(menu)) {\n            this._pendingRemoval = menu._animationDone.pipe(take(1)).subscribe(() => {\n                overlayRef.detach();\n                menu.lazyContent?.detach();\n            });\n            menu._setIsOpen(false);\n        }\n        else {\n            overlayRef.detach();\n            menu?.lazyContent?.detach();\n        }\n        if (menu && this._ownsMenu(menu)) {\n            PANELS_TO_TRIGGERS.delete(menu);\n        }\n        // Always restore focus if the user is navigating using the keyboard or the menu was opened\n        // programmatically. We don't restore for non-root triggers, because it can prevent focus\n        // from making it back to the root trigger when closing a long chain of menus by clicking\n        // on the backdrop.\n        if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n            this.focus(this._openedBy);\n        }\n        this._openedBy = undefined;\n        this._setIsMenuOpen(false);\n    }\n    // set state rather than toggle to support triggers sharing a menu\n    _setIsMenuOpen(isOpen) {\n        if (isOpen !== this._menuOpen) {\n            this._menuOpen = isOpen;\n            this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n            if (this.triggersSubmenu()) {\n                this._menuItemInstance._setHighlighted(isOpen);\n            }\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method creates the overlay from the provided menu's template and saves its\n     * OverlayRef so that it can be attached to the DOM when openMenu is called.\n     */\n    _createOverlay(menu) {\n        if (!this._overlayRef) {\n            const config = this._getOverlayConfig(menu);\n            this._subscribeToPositions(menu, config.positionStrategy);\n            this._overlayRef = this._overlay.create(config);\n            this._overlayRef.keydownEvents().subscribe(event => {\n                if (this.menu instanceof MatMenu) {\n                    this.menu._handleKeydown(event);\n                }\n            });\n        }\n        return this._overlayRef;\n    }\n    /**\n     * This method builds the configuration object needed to create the overlay, the OverlayState.\n     * @returns OverlayConfig\n     */\n    _getOverlayConfig(menu) {\n        return new OverlayConfig({\n            positionStrategy: this._overlay\n                .position()\n                .flexibleConnectedTo(this._element)\n                .withLockedPosition()\n                .withGrowAfterOpen()\n                .withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n            backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n            panelClass: menu.overlayPanelClass,\n            scrollStrategy: this._scrollStrategy(),\n            direction: this._dir || 'ltr',\n        });\n    }\n    /**\n     * Listens to changes in the position of the overlay and sets the correct classes\n     * on the menu based on the new position. This ensures the animation origin is always\n     * correct, even if a fallback position is used for the overlay.\n     */\n    _subscribeToPositions(menu, position) {\n        if (menu.setPositionClasses) {\n            position.positionChanges.subscribe(change => {\n                this._ngZone.run(() => {\n                    const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n                    const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n                    menu.setPositionClasses(posX, posY);\n                });\n            });\n        }\n    }\n    /**\n     * Sets the appropriate positions on a position strategy\n     * so the overlay connects with the trigger correctly.\n     * @param positionStrategy Strategy whose position to update.\n     */\n    _setPosition(menu, positionStrategy) {\n        let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n        let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n        let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n        let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n        let offsetY = 0;\n        if (this.triggersSubmenu()) {\n            // When the menu is a sub-menu, it should always align itself\n            // to the edges of the trigger, instead of overlapping it.\n            overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n            originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n            if (this._parentMaterialMenu) {\n                if (this._parentInnerPadding == null) {\n                    const firstItem = this._parentMaterialMenu.items.first;\n                    this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n                }\n                offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n            }\n        }\n        else if (!menu.overlapTrigger) {\n            originY = overlayY === 'top' ? 'bottom' : 'top';\n            originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n        }\n        positionStrategy.withPositions([\n            { originX, originY, overlayX, overlayY, offsetY },\n            { originX: originFallbackX, originY, overlayX: overlayFallbackX, overlayY, offsetY },\n            {\n                originX,\n                originY: originFallbackY,\n                overlayX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n            {\n                originX: originFallbackX,\n                originY: originFallbackY,\n                overlayX: overlayFallbackX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n        ]);\n    }\n    /** Returns a stream that emits whenever an action that should close the menu occurs. */\n    _menuClosingActions() {\n        const backdrop = this._overlayRef.backdropClick();\n        const detachments = this._overlayRef.detachments();\n        const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n        const hover = this._parentMaterialMenu\n            ? this._parentMaterialMenu\n                ._hovered()\n                .pipe(filter(active => this._menuOpen && active !== this._menuItemInstance))\n            : of();\n        return merge(backdrop, parentClose, hover, detachments);\n    }\n    /** Handles mouse presses on the trigger. */\n    _handleMousedown(event) {\n        if (!isFakeMousedownFromScreenReader(event)) {\n            // Since right or middle button clicks won't trigger the `click` event,\n            // we shouldn't consider the menu as opened by mouse in those cases.\n            this._openedBy = event.button === 0 ? 'mouse' : undefined;\n            // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n            // we should prevent focus from moving onto it via click to avoid the\n            // highlight from lingering on the menu item.\n            if (this.triggersSubmenu()) {\n                event.preventDefault();\n            }\n        }\n    }\n    /** Handles key presses on the trigger. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        // Pressing enter on the trigger will trigger the click handler later.\n        if (keyCode === ENTER || keyCode === SPACE) {\n            this._openedBy = 'keyboard';\n        }\n        if (this.triggersSubmenu() &&\n            ((keyCode === RIGHT_ARROW && this.dir === 'ltr') ||\n                (keyCode === LEFT_ARROW && this.dir === 'rtl'))) {\n            this._openedBy = 'keyboard';\n            this.openMenu();\n        }\n    }\n    /** Handles click events on the trigger. */\n    _handleClick(event) {\n        if (this.triggersSubmenu()) {\n            // Stop event propagation to avoid closing the parent menu.\n            event.stopPropagation();\n            this.openMenu();\n        }\n        else {\n            this.toggleMenu();\n        }\n    }\n    /** Handles the cases where the user hovers over the trigger. */\n    _handleHover() {\n        // Subscribe to changes in the hovered item in order to toggle the panel.\n        if (this.triggersSubmenu() && this._parentMaterialMenu) {\n            this._hoverSubscription = this._parentMaterialMenu._hovered().subscribe(active => {\n                if (active === this._menuItemInstance && !active.disabled) {\n                    this._openedBy = 'mouse';\n                    this.openMenu();\n                }\n            });\n        }\n    }\n    /** Gets the portal that should be attached to the overlay. */\n    _getPortal(menu) {\n        // Note that we can avoid this check by keeping the portal on the menu panel.\n        // While it would be cleaner, we'd have to introduce another required method on\n        // `MatMenuPanel`, making it harder to consume.\n        if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n            this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n        }\n        return this._portal;\n    }\n    /**\n     * Determines whether the trigger owns a specific menu panel, at the current point in time.\n     * This allows us to distinguish the case where the same panel is passed into multiple triggers\n     * and multiple are open at a time.\n     */\n    _ownsMenu(menu) {\n        return PANELS_TO_TRIGGERS.get(menu) === this;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenuTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatMenuTrigger, isStandalone: true, selector: \"[mat-menu-trigger-for], [matMenuTriggerFor]\", inputs: { _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"], menu: [\"matMenuTriggerFor\", \"menu\"], menuData: [\"matMenuTriggerData\", \"menuData\"], restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"] }, outputs: { menuOpened: \"menuOpened\", onMenuOpen: \"onMenuOpen\", menuClosed: \"menuClosed\", onMenuClose: \"onMenuClose\" }, host: { listeners: { \"click\": \"_handleClick($event)\", \"mousedown\": \"_handleMousedown($event)\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-haspopup\": \"menu ? \\\"menu\\\" : null\", \"attr.aria-expanded\": \"menuOpen\", \"attr.aria-controls\": \"menuOpen ? menu.panelId : null\" }, classAttribute: \"mat-mdc-menu-trigger\" }, exportAs: [\"matMenuTrigger\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenuTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n                    host: {\n                        'class': 'mat-mdc-menu-trigger',\n                        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n                        '[attr.aria-expanded]': 'menuOpen',\n                        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n                        '(click)': '_handleClick($event)',\n                        '(mousedown)': '_handleMousedown($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                    },\n                    exportAs: 'matMenuTrigger',\n                }]\n        }], ctorParameters: () => [], propDecorators: { _deprecatedMatMenuTriggerFor: [{\n                type: Input,\n                args: ['mat-menu-trigger-for']\n            }], menu: [{\n                type: Input,\n                args: ['matMenuTriggerFor']\n            }], menuData: [{\n                type: Input,\n                args: ['matMenuTriggerData']\n            }], restoreFocus: [{\n                type: Input,\n                args: ['matMenuTriggerRestoreFocus']\n            }], menuOpened: [{\n                type: Output\n            }], onMenuOpen: [{\n                type: Output\n            }], menuClosed: [{\n                type: Output\n            }], onMenuClose: [{\n                type: Output\n            }] } });\n\nclass MatMenuModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenuModule, imports: [MatRippleModule,\n            MatCommonModule,\n            OverlayModule,\n            MatMenu,\n            MatMenuItem,\n            MatMenuContent,\n            MatMenuTrigger], exports: [CdkScrollableModule,\n            MatMenu,\n            MatCommonModule,\n            MatMenuItem,\n            MatMenuContent,\n            MatMenuTrigger] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenuModule, providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [MatRippleModule,\n            MatCommonModule,\n            OverlayModule, CdkScrollableModule,\n            MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatRippleModule,\n                        MatCommonModule,\n                        OverlayModule,\n                        MatMenu,\n                        MatMenuItem,\n                        MatMenuContent,\n                        MatMenuTrigger,\n                    ],\n                    exports: [\n                        CdkScrollableModule,\n                        MatMenu,\n                        MatCommonModule,\n                        MatMenuItem,\n                        MatMenuContent,\n                        MatMenuTrigger,\n                    ],\n                    providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matMenuAnimations = {\n    // Represents:\n    // trigger('transformMenu', [\n    //   state(\n    //     'void',\n    //     style({\n    //       opacity: 0,\n    //       transform: 'scale(0.8)',\n    //     }),\n    //   ),\n    //   transition(\n    //     'void => enter',\n    //     animate(\n    //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n    //       style({\n    //         opacity: 1,\n    //         transform: 'scale(1)',\n    //       }),\n    //     ),\n    //   ),\n    //   transition('* => void', animate('100ms 25ms linear', style({opacity: 0}))),\n    // ])\n    /**\n     * This animation controls the menu panel's entry and exit from the page.\n     *\n     * When the menu panel is added to the DOM, it scales in and fades in its border.\n     *\n     * When the menu panel is removed from the DOM, it simply fades out after a brief\n     * delay to display the ripple.\n     */\n    transformMenu: {\n        type: 7,\n        name: 'transformMenu',\n        definitions: [\n            {\n                type: 0,\n                name: 'void',\n                styles: { type: 6, styles: { opacity: 0, transform: 'scale(0.8)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'void => enter',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 1, transform: 'scale(1)' }, offset: null },\n                    timings: '120ms cubic-bezier(0, 0, 0.2, 1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => void',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                    timings: '100ms 25ms linear',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('fadeInItems', [\n    //   // TODO(crisbeto): this is inside the `transformMenu`\n    //   // now. Remove next time we do breaking changes.\n    //   state('showing', style({opacity: 1})),\n    //   transition('void => *', [\n    //     style({opacity: 0}),\n    //     animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n    //   ]),\n    // ])\n    /**\n     * This animation fades in the background color and content of the menu panel\n     * after its containing element is scaled in.\n     */\n    fadeInItems: {\n        type: 7,\n        name: 'fadeInItems',\n        definitions: [\n            {\n                type: 0,\n                name: 'showing',\n                styles: { type: 6, styles: { opacity: 1 }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'void => *',\n                animation: [\n                    { type: 6, styles: { opacity: 0 }, offset: null },\n                    { type: 4, styles: null, timings: '400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)' },\n                ],\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, fadeInItems, matMenuAnimations, transformMenu };\n//# sourceMappingURL=menu.mjs.map\n"], "mappings": ";;;;;;;;IA8HiFA,EAAE,CAAAC,cAAA;IAAFD,EAAE,CAAAE,cAAA,YAF8nC,CAAC;IAEjoCF,EAAE,CAAAG,SAAA,gBAFgqC,CAAC;IAEnqCH,EAAE,CAAAI,YAAA,CAFsqC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,gCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAEzqCT,EAAE,CAAAU,gBAAA;IAAFV,EAAE,CAAAE,cAAA,YA0eu2D,CAAC;IA1e12DF,EAAE,CAAAW,UAAA,mBAAAC,qDAAA;MAAFZ,EAAE,CAAAa,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFd,EAAE,CAAAe,aAAA;MAAA,OAAFf,EAAE,CAAAgB,WAAA,CA0ey8CF,MAAA,CAAAG,MAAA,CAAAC,IAAA,CAAY,OAAO,CAAC;IAAA,CAAC,CAAC,4BAAAC,8DAAAC,MAAA;MA1ej+CpB,EAAE,CAAAa,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFd,EAAE,CAAAe,aAAA;MAAA,OAAFf,EAAE,CAAAgB,WAAA,CA0egiDF,MAAA,CAAAO,iBAAA,CAAAD,MAAA,CAAAE,aAAsC,CAAC;IAAA,CAAC,CAAC,0BAAAC,4DAAAH,MAAA;MA1e3kDpB,EAAE,CAAAa,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFd,EAAE,CAAAe,aAAA;MAAA,OAAFf,EAAE,CAAAgB,WAAA,CA0egmDF,MAAA,CAAAU,gBAAA,CAAAJ,MAAA,CAAAE,aAAqC,CAAC;IAAA,CAAC,CAAC,6BAAAG,+DAAAL,MAAA;MA1e1oDpB,EAAE,CAAAa,aAAA,CAAAJ,GAAA;MAAA,MAAAK,MAAA,GAAFd,EAAE,CAAAe,aAAA;MAAA,OAAFf,EAAE,CAAAgB,WAAA,CA0ekqDF,MAAA,CAAAU,gBAAA,CAAAJ,MAAA,CAAAE,aAAqC,CAAC;IAAA,CAAC,CAAC;IA1e5sDtB,EAAE,CAAAE,cAAA,YA0ei5D,CAAC;IA1ep5DF,EAAE,CAAA0B,YAAA,EA0ek7D,CAAC;IA1er7D1B,EAAE,CAAAI,YAAA,CA0e87D,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAG,EAAA;IAAA,MAAAO,MAAA,GA1e38Dd,EAAE,CAAAe,aAAA;IAAFf,EAAE,CAAA2B,UAAA,CAAAb,MAAA,CAAAc,UA0e0uC,CAAC;IA1e7uC5B,EAAE,CAAA6B,WAAA,uCAAAf,MAAA,CAAAgB,mBA0ekzC,CAAC,kCAAAhB,MAAA,CAAAiB,oBAAA,WAA8E,CAAC,6BAAAjB,MAAA,CAAAkB,YAAsD,CAAC;IA1e37ChC,EAAE,CAAAiC,UAAA,OAAAnB,MAAA,CAAAoB,OA0e8sC,CAAC;IA1ejtClC,EAAE,CAAAmC,WAAA,eAAArB,MAAA,CAAAsB,SAAA,6BAAAtB,MAAA,CAAAuB,cAAA,8BAAAvB,MAAA,CAAAwB,eAAA;EAAA;AAAA;AA9HnF,OAAO,KAAKtC,EAAE,MAAM,eAAe;AACnC,SAASuC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,WAAW,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACzX,SAASC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EAAEC,gCAAgC,EAAEC,+BAA+B,QAAQ,mBAAmB;AAClJ,SAASC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AAC3H,SAASC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEC,EAAE,QAAQ,MAAM;AACvD,SAASC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,gBAAgB;AAC9E,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,SAAS,QAAQ,uBAAuB;AACtD,SAASC,cAAc,EAAEC,eAAe,QAAQ,qBAAqB;AACrE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AAC5E,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAAST,CAAC,IAAIU,eAAe,QAAQ,sBAAsB;AAC3D,SAASV,CAAC,IAAIW,eAAe,QAAQ,8BAA8B;AACnE,OAAO,uBAAuB;;AAE9B;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,IAAI/D,cAAc,CAAC,gBAAgB,CAAC;;AAE3D;AACA;AACA;AACA,MAAMgE,WAAW,CAAC;EAoBdC,WAAWA,CAAA,EAAG;IAAA,IAAAC,iBAAA,EAAAC,qBAAA;IAAAC,eAAA,sBAnBAnE,MAAM,CAACC,UAAU,CAAC;IAAAkE,eAAA,oBACpBnE,MAAM,CAAC8C,QAAQ,CAAC;IAAAqB,eAAA,wBACZnE,MAAM,CAACwB,YAAY,CAAC;IAAA2C,eAAA,sBACtBnE,MAAM,CAAC8D,cAAc,EAAE;MAAEM,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAD,eAAA,6BACnCnE,MAAM,CAACE,iBAAiB,CAAC;IAC9C;IAAAiE,eAAA,eACO,UAAU;IACjB;IAAAA,eAAA,mBACW,KAAK;IAChB;IAAAA,eAAA,wBACgB,KAAK;IACrB;IAAAA,eAAA,mBACW,IAAI9B,OAAO,CAAC,CAAC;IACxB;IAAA8B,eAAA,mBACW,IAAI9B,OAAO,CAAC,CAAC;IACxB;IAAA8B,eAAA,uBACe,KAAK;IACpB;IAAAA,eAAA,2BACmB,KAAK;IAEpBnE,MAAM,CAAC+C,sBAAsB,CAAC,CAACsB,IAAI,CAACpB,uBAAuB,CAAC;IAC5D,CAAAgB,iBAAA,OAAI,CAACK,WAAW,cAAAL,iBAAA,gBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBM,OAAO,cAAAL,qBAAA,eAAzBA,qBAAA,CAAAM,IAAA,CAAAP,iBAAA,EAA4B,IAAI,CAAC;EACrC;EACA;EACAQ,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACC,aAAa,IAAIF,MAAM,EAAE;MAC9B,IAAI,CAACE,aAAa,CAACC,QAAQ,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAEJ,MAAM,EAAEC,OAAO,CAAC;IACxE,CAAC,MACI;MACD,IAAI,CAACG,eAAe,CAAC,CAAC,CAACL,KAAK,CAACE,OAAO,CAAC;IACzC;IACA,IAAI,CAACI,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;EAC5B;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACL,aAAa,EAAE;MACpB;MACA;MACA;MACA,IAAI,CAACA,aAAa,CAACM,OAAO,CAAC,IAAI,CAACC,WAAW,EAAE,KAAK,CAAC;IACvD;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACR,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACS,cAAc,CAAC,IAAI,CAACF,WAAW,CAAC;IACvD;IACA,IAAI,IAAI,CAACb,WAAW,IAAI,IAAI,CAACA,WAAW,CAACgB,UAAU,EAAE;MACjD,IAAI,CAAChB,WAAW,CAACgB,UAAU,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACT,QAAQ,CAACS,QAAQ,CAAC,CAAC;EAC5B;EACA;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,QAAQ,GAAG,IAAI,GAAG,GAAG;EACrC;EACA;EACAZ,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACK,WAAW,CAACQ,aAAa;EACzC;EACA;EACAC,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,IAAI,CAACH,QAAQ,EAAE;MACfG,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IAC3B;EACJ;EACA;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACT,QAAQ,CAACP,IAAI,CAAC,IAAI,CAAC;EAC5B;EACA;EACAiB,QAAQA,CAAA,EAAG;IAAA,IAAAC,kBAAA;IACP,MAAMC,KAAK,GAAG,IAAI,CAAChB,WAAW,CAACQ,aAAa,CAACS,SAAS,CAAC,IAAI,CAAC;IAC5D,MAAMC,KAAK,GAAGF,KAAK,CAACG,gBAAgB,CAAC,2BAA2B,CAAC;IACjE;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCF,KAAK,CAACE,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;IACrB;IACA,OAAO,EAAAP,kBAAA,GAAAC,KAAK,CAACO,WAAW,cAAAR,kBAAA,uBAAjBA,kBAAA,CAAmBS,IAAI,CAAC,CAAC,KAAI,EAAE;EAC1C;EACAC,eAAeA,CAACC,aAAa,EAAE;IAC3B;IACA;IACA;IACA,IAAI,CAACC,YAAY,GAAGD,aAAa;IACjC,IAAI,CAACE,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACAC,mBAAmBA,CAACC,eAAe,EAAE;IACjC,IAAI,CAACC,gBAAgB,GAAGD,eAAe;IACvC,IAAI,CAACH,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACAI,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACC,aAAa,KAAK,IAAI,CAACxC,eAAe,CAAC,CAAC;EACpF;AAGJ;AAACyC,YAAA,GAjGKxD,WAAW;AAAAI,eAAA,CAAXJ,WAAW,wBAAAyD,qBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA+FsF1D,YAAW;AAAA;AAAAI,eAAA,CA/F5GJ,WAAW,8BAkGgEvG,EAAE,CAAAkK,iBAAA;EAAAC,IAAA,EAFQ5D,YAAW;EAAA6D,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,0BAAAjK,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAErBP,EAAE,CAAAW,UAAA,mBAAA8J,sCAAArJ,MAAA;QAAA,OAFQZ,GAAA,CAAA4H,cAAA,CAAAhH,MAAqB,CAAC;MAAA,CAAZ,CAAC,wBAAAsJ,2CAAA;QAAA,OAAXlK,GAAA,CAAAgI,iBAAA,CAAkB,CAAC;MAAA,CAAT,CAAC;IAAA;IAAA,IAAAjI,EAAA;MAErBP,EAAE,CAAAmC,WAAA,SAAA3B,GAAA,CAAAmK,IAAA,cAFQnK,GAAA,CAAAyH,YAAA,CAAa,CAAC,mBAAAzH,GAAA,CAAA0H,QAAA,cAAA1H,GAAA,CAAA0H,QAAA,IAAF,IAAI;MAE1BlI,EAAE,CAAA6B,WAAA,kCAAArB,GAAA,CAAA8I,YAFkB,CAAC,sCAAA9I,GAAA,CAAAmJ,gBAAD,CAAC;IAAA;EAAA;EAAAiB,MAAA;IAAAD,IAAA;IAAAzC,QAAA,8BAA8GvF,gBAAgB;IAAAkI,aAAA,wCAAqDlI,gBAAgB;EAAA;EAAAmI,QAAA;EAAAC,KAAA,EAAAC,GAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,sBAAAhL,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAExNP,EAAE,CAAAwL,eAAA,CAAAC,GAAA;MAAFzL,EAAE,CAAA0B,YAAA,EAF6uB,CAAC;MAEhvB1B,EAAE,CAAAE,cAAA,aAFsxB,CAAC;MAEzxBF,EAAE,CAAA0B,YAAA,KAF+yB,CAAC;MAElzB1B,EAAE,CAAAI,YAAA,CAFszB,CAAC;MAEzzBJ,EAAE,CAAAG,SAAA,YAFo9B,CAAC;MAEv9BH,EAAE,CAAA0L,UAAA,IAAAC,mCAAA,qBAFg/B,CAAC;IAAA;IAAA,IAAApL,EAAA;MAEn/BP,EAAE,CAAA4L,SAAA,EAF45B,CAAC;MAE/5B5L,EAAE,CAAAiC,UAAA,sBAAAzB,GAAA,CAAAqK,aAAA,IAAArK,GAAA,CAAA0H,QAF45B,CAAC,qBAAA1H,GAAA,CAAA8G,eAAA,EAA8C,CAAC;MAE98BtH,EAAE,CAAA4L,SAAA,CAFyqC,CAAC;MAE5qC5L,EAAE,CAAA6L,aAAA,CAAArL,GAAA,CAAAmJ,gBAAA,SAFyqC,CAAC;IAAA;EAAA;EAAAmC,YAAA,GAA+CnG,SAAS;EAAAoG,aAAA;EAAAC,eAAA;AAAA;AAErzC;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFjM,EAAE,CAAAkM,iBAAA,CAAQ3F,WAAW,EAAc,CAAC;IACzG4D,IAAI,EAAEvH,SAAS;IACfuJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAEtB,QAAQ,EAAE,aAAa;MAAEuB,IAAI,EAAE;QACzD,aAAa,EAAE,MAAM;QACrB,OAAO,EAAE,uCAAuC;QAChD,uCAAuC,EAAE,cAAc;QACvD,2CAA2C,EAAE,kBAAkB;QAC/D,iBAAiB,EAAE,gBAAgB;QACnC,sBAAsB,EAAE,UAAU;QAClC,iBAAiB,EAAE,kBAAkB;QACrC,SAAS,EAAE,wBAAwB;QACnC,cAAc,EAAE;MACpB,CAAC;MAAEL,eAAe,EAAEnJ,uBAAuB,CAACyJ,MAAM;MAAEP,aAAa,EAAEjJ,iBAAiB,CAACyJ,IAAI;MAAEC,OAAO,EAAE,CAAC7G,SAAS,CAAC;MAAE2F,QAAQ,EAAE;IAAigB,CAAC;EACzoB,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEX,IAAI,EAAE,CAAC;MAC/CR,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAEmF,QAAQ,EAAE,CAAC;MACXiC,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE9J;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkI,aAAa,EAAE,CAAC;MAChBV,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE9J;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,SAAS+J,4BAA4BA,CAAA,EAAG;EACpC,MAAMC,KAAK,CAAC;AAChB,wEAAwE,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAAA,EAAG;EACpC,MAAMD,KAAK,CAAC;AAChB,uEAAuE,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,0BAA0BA,CAAA,EAAG;EAClC,MAAMF,KAAK,CAAC,gFAAgF,GACxF,sEAAsE,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,GAAG,IAAIvK,cAAc,CAAC,gBAAgB,CAAC;AAC7D;AACA,MAAMwK,cAAc,CAAC;EAWjBvG,WAAWA,CAAA,EAAG;IAAAG,eAAA,oBAVFnE,MAAM,CAACQ,WAAW,CAAC;IAAA2D,eAAA,kBACrBnE,MAAM,CAACS,cAAc,CAAC;IAAA0D,eAAA,oBACpBnE,MAAM,CAACU,QAAQ,CAAC;IAAAyD,eAAA,4BACRnE,MAAM,CAACW,gBAAgB,CAAC;IAAAwD,eAAA,oBAChCnE,MAAM,CAAC8C,QAAQ,CAAC;IAAAqB,eAAA,6BACPnE,MAAM,CAACE,iBAAiB,CAAC;IAAAiE,eAAA;IAAAA,eAAA;IAG9C;IAAAA,eAAA,oBACY,IAAI9B,OAAO,CAAC,CAAC;EACT;EAChB;AACJ;AACA;AACA;EACImI,MAAMA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAItH,cAAc,CAAC,IAAI,CAACuH,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAAC;IAC7E;IACA,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAIzH,eAAe,CAAC,IAAI,CAACgE,SAAS,CAAC0D,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC;IAC/G;IACA,MAAMC,OAAO,GAAG,IAAI,CAACP,SAAS,CAACQ,UAAU,CAACxF,aAAa;IACvD;IACA;IACA;IACAuF,OAAO,CAACE,UAAU,CAACC,YAAY,CAAC,IAAI,CAACP,OAAO,CAACQ,aAAa,EAAEJ,OAAO,CAAC;IACpE;IACA;IACA;IACA;IACA;IACA,IAAI,CAACnE,kBAAkB,CAACC,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC0D,OAAO,CAACF,MAAM,CAAC,IAAI,CAACM,OAAO,EAAEL,OAAO,CAAC;IAC1C,IAAI,CAACc,SAAS,CAACvG,IAAI,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;EACI6F,MAAMA,CAAA,EAAG;IAAA,IAAAW,aAAA;IACL,KAAAA,aAAA,GAAI,IAAI,CAACd,OAAO,cAAAc,aAAA,eAAZA,aAAA,CAAcC,UAAU,EAAE;MAC1B,IAAI,CAACf,OAAO,CAACG,MAAM,CAAC,CAAC;IACzB;EACJ;EACAzF,WAAWA,CAAA,EAAG;IAAA,IAAAsG,aAAA;IACV,IAAI,CAACb,MAAM,CAAC,CAAC;IACb,CAAAa,aAAA,OAAI,CAACZ,OAAO,cAAAY,aAAA,eAAZA,aAAA,CAAcC,OAAO,CAAC,CAAC;EAC3B;AAGJ;AAACC,eAAA,GArDKrB,cAAc;AAAApG,eAAA,CAAdoG,cAAc,wBAAAsB,wBAAApE,iBAAA;EAAA,YAAAA,iBAAA,IAmDmF8C,eAAc;AAAA;AAAApG,eAAA,CAnD/GoG,cAAc,8BA1D6D/M,EAAE,CAAAsO,iBAAA;EAAAnE,IAAA,EA8GQ4C,eAAc;EAAA3C,SAAA;EAAAmE,QAAA,GA9GxBvO,EAAE,CAAAwO,kBAAA,CA8GgG,CAAC;IAAEC,OAAO,EAAE3B,gBAAgB;IAAE4B,WAAW,EAAE3B;EAAe,CAAC,CAAC;AAAA;AAE/O;EAAA,QAAAd,SAAA,oBAAAA,SAAA,KAhHiFjM,EAAE,CAAAkM,iBAAA,CAgHQa,cAAc,EAAc,CAAC;IAC5G5C,IAAI,EAAE/G,SAAS;IACf+I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6BAA6B;MACvCuC,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAE3B,gBAAgB;QAAE4B,WAAW,EAAE3B;MAAe,CAAC;IAC1E,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAM6B,wBAAwB,GAAG,IAAIrM,cAAc,CAAC,0BAA0B,EAAE;EAC5EsM,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,gCAAgCA,CAAA,EAAG;EACxC,OAAO;IACHC,cAAc,EAAE,KAAK;IACrBC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,OAAO;IAClBC,aAAa,EAAE;EACnB,CAAC;AACL;AACA;AACA,MAAMC,eAAe,GAAG,iBAAiB;AACzC;AACA,MAAMC,cAAc,GAAG,gBAAgB;AACvC,MAAMC,OAAO,CAAC;EAqCV;EACA,IAAIL,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACM,UAAU;EAC1B;EACA,IAAIN,SAASA,CAACO,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,OAAO,KAChB,OAAOvD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjDS,4BAA4B,CAAC,CAAC;IAClC;IACA,IAAI,CAAC6C,UAAU,GAAGC,KAAK;IACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACA,IAAIP,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACQ,UAAU;EAC1B;EACA,IAAIR,SAASA,CAACM,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,OAAO,KAAK,OAAOvD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC3FW,4BAA4B,CAAC,CAAC;IAClC;IACA,IAAI,CAAC8C,UAAU,GAAGF,KAAK;IACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACA;;EAiBA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIE,UAAUA,CAACC,OAAO,EAAE;IACpB,MAAMC,kBAAkB,GAAG,IAAI,CAACC,mBAAmB;IACnD,MAAMC,YAAY,GAAAC,aAAA,KAAQ,IAAI,CAACpO,UAAU,CAAE;IAC3C,IAAIiO,kBAAkB,IAAIA,kBAAkB,CAAC7G,MAAM,EAAE;MACjD6G,kBAAkB,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;QACjDJ,YAAY,CAACI,SAAS,CAAC,GAAG,KAAK;MACnC,CAAC,CAAC;IACN;IACA,IAAI,CAACL,mBAAmB,GAAGF,OAAO;IAClC,IAAIA,OAAO,IAAIA,OAAO,CAAC5G,MAAM,EAAE;MAC3B4G,OAAO,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;QACtCJ,YAAY,CAACI,SAAS,CAAC,GAAG,IAAI;MAClC,CAAC,CAAC;MACF,IAAI,CAACxI,WAAW,CAACQ,aAAa,CAACgI,SAAS,GAAG,EAAE;IACjD;IACA,IAAI,CAACvO,UAAU,GAAGmO,YAAY;EAClC;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIK,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACT,UAAU;EAC1B;EACA,IAAIS,SAASA,CAACR,OAAO,EAAE;IACnB,IAAI,CAACD,UAAU,GAAGC,OAAO;EAC7B;EACA;;EASApJ,WAAWA,CAAA,EAAG;IAAAG,eAAA,sBA3HAnE,MAAM,CAACC,UAAU,CAAC;IAAAkE,eAAA,6BACXnE,MAAM,CAACE,iBAAiB,CAAC;IAAAiE,eAAA,oBAClCnE,MAAM,CAACU,QAAQ,CAAC;IAAAyD,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAM5B;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,iCACyB,IAAItD,SAAS,CAAC,CAAC;IACxC;IAAAsD,eAAA,qBACa,CAAC,CAAC;IACf;IAAAA,eAAA,+BACuB,MAAM;IAC7B;IAAAA,eAAA,yBACiB,IAAI9B,OAAO,CAAC,CAAC;IAC9B;IAAA8B,eAAA,uBACe,KAAK;IACpB;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAAAA,eAAA;IA4BA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,iBAwCS,IAAIrD,YAAY,CAAC,CAAC;IAC3B;AACJ;AACA;AACA;AACA;IAJIqD,eAAA,gBAKQ,IAAI,CAAC1F,MAAM;IAAA0F,eAAA,kBACTnE,MAAM,CAACyB,YAAY,CAAC,CAACoM,KAAK,CAAC,iBAAiB,CAAC;IAEnD,MAAMC,cAAc,GAAG9N,MAAM,CAACoM,wBAAwB,CAAC;IACvD,IAAI,CAAC2B,iBAAiB,GAAGD,cAAc,CAACC,iBAAiB,IAAI,EAAE;IAC/D,IAAI,CAAChB,UAAU,GAAGe,cAAc,CAACrB,SAAS;IAC1C,IAAI,CAACS,UAAU,GAAGY,cAAc,CAACpB,SAAS;IAC1C,IAAI,CAACC,aAAa,GAAGmB,cAAc,CAACnB,aAAa;IACjD,IAAI,CAACH,cAAc,GAAGsB,cAAc,CAACtB,cAAc;IACnD,IAAI,CAACwB,WAAW,GAAGF,cAAc,CAACE,WAAW;IAC7C,IAAI,CAAC1O,mBAAmB,GAAGU,MAAM,CAACe,qBAAqB,EAAE;MAAEqD,QAAQ,EAAE;IAAK,CAAC,CAAC,KAAK,gBAAgB;EACrG;EACA6J,QAAQA,CAAA,EAAG;IACP,IAAI,CAAChB,kBAAkB,CAAC,CAAC;EAC7B;EACAiB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG,IAAI1M,eAAe,CAAC,IAAI,CAAC2M,sBAAsB,CAAC,CAC9DC,QAAQ,CAAC,CAAC,CACVC,aAAa,CAAC,CAAC,CACfC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACJ,WAAW,CAACK,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACjQ,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChE;IACA;IACA;IACA,IAAI,CAAC2P,sBAAsB,CAACM,OAAO,CAC9BC,IAAI,CAACnM,SAAS,CAAC,IAAI,CAAC4L,sBAAsB,CAAC,EAAE3L,SAAS,CAACmM,KAAK,IAAIvM,KAAK,CAAC,GAAGuM,KAAK,CAACC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAAChK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9G2J,SAAS,CAACM,WAAW,IAAI,IAAI,CAACZ,WAAW,CAACa,gBAAgB,CAACD,WAAW,CAAC,CAAC;IAC7E,IAAI,CAACX,sBAAsB,CAACM,OAAO,CAACD,SAAS,CAAEQ,SAAS,IAAK;MAAA,IAAAC,mBAAA;MACzD;MACA;MACA;MACA,MAAMC,OAAO,GAAG,IAAI,CAAChB,WAAW;MAChC,IAAI,IAAI,CAAC7O,oBAAoB,KAAK,OAAO,KAAA4P,mBAAA,GAAIC,OAAO,CAACC,UAAU,cAAAF,mBAAA,eAAlBA,mBAAA,CAAoB/H,SAAS,CAAC,CAAC,EAAE;QAC1E,MAAMyH,KAAK,GAAGK,SAAS,CAACI,OAAO,CAAC,CAAC;QACjC,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACb,KAAK,CAACrI,MAAM,GAAG,CAAC,EAAE4I,OAAO,CAACO,eAAe,IAAI,CAAC,CAAC,CAAC;QACnF,IAAId,KAAK,CAACU,KAAK,CAAC,IAAI,CAACV,KAAK,CAACU,KAAK,CAAC,CAAC7J,QAAQ,EAAE;UACxC0J,OAAO,CAACQ,aAAa,CAACL,KAAK,CAAC;QAChC,CAAC,MACI;UACDH,OAAO,CAACS,iBAAiB,CAAC,CAAC;QAC/B;MACJ;IACJ,CAAC,CAAC;EACN;EACAzK,WAAWA,CAAA,EAAG;IAAA,IAAA0K,iBAAA,EAAAC,qBAAA;IACV,CAAAD,iBAAA,OAAI,CAAC1B,WAAW,cAAA0B,iBAAA,eAAhBA,iBAAA,CAAkBE,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC3B,sBAAsB,CAAC2B,OAAO,CAAC,CAAC;IACrC,IAAI,CAACvR,MAAM,CAAC+G,QAAQ,CAAC,CAAC;IACtB,CAAAuK,qBAAA,OAAI,CAACE,kBAAkB,cAAAF,qBAAA,eAAvBA,qBAAA,CAAyBC,OAAO,CAAC,CAAC;IAClCE,YAAY,CAAC,IAAI,CAACC,oBAAoB,CAAC;EAC3C;EACA;EACA5K,QAAQA,CAAA,EAAG;IACP;IACA,MAAM6K,WAAW,GAAG,IAAI,CAAC/B,sBAAsB,CAACM,OAAO;IACvD,OAAOyB,WAAW,CAACxB,IAAI,CAACnM,SAAS,CAAC,IAAI,CAAC4L,sBAAsB,CAAC,EAAE3L,SAAS,CAACmM,KAAK,IAAIvM,KAAK,CAAC,GAAGuM,KAAK,CAACC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACxJ,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrI;EACA;AACJ;AACA;AACA;AACA;AACA;EACIhB,OAAOA,CAAC8L,KAAK,EAAE,CAAE;EACjB;AACJ;AACA;AACA;AACA;AACA;EACI/K,UAAUA,CAAC+K,KAAK,EAAE,CAAE;EACpB;EACAC,cAAcA,CAACzK,KAAK,EAAE;IAClB,MAAM0K,OAAO,GAAG1K,KAAK,CAAC0K,OAAO;IAC7B,MAAMnB,OAAO,GAAG,IAAI,CAAChB,WAAW;IAChC,QAAQmC,OAAO;MACX,KAAKtO,MAAM;QACP,IAAI,CAACC,cAAc,CAAC2D,KAAK,CAAC,EAAE;UACxBA,KAAK,CAACC,cAAc,CAAC,CAAC;UACtB,IAAI,CAACrH,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ,KAAKsD,UAAU;QACX,IAAI,IAAI,CAACwO,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;UAC7C,IAAI,CAAChS,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ,KAAKqD,WAAW;QACZ,IAAI,IAAI,CAACyO,UAAU,IAAI,IAAI,CAACC,SAAS,KAAK,KAAK,EAAE;UAC7C,IAAI,CAAChS,MAAM,CAACC,IAAI,CAAC,SAAS,CAAC;QAC/B;QACA;MACJ;QACI,IAAI6R,OAAO,KAAK1O,QAAQ,IAAI0O,OAAO,KAAKzO,UAAU,EAAE;UAChDsN,OAAO,CAACsB,cAAc,CAAC,UAAU,CAAC;QACtC;QACAtB,OAAO,CAACuB,SAAS,CAAC9K,KAAK,CAAC;QACxB;IACR;EACJ;EACA;AACJ;AACA;AACA;EACI+K,cAAcA,CAAClM,MAAM,GAAG,SAAS,EAAE;IAAA,IAAAmM,sBAAA;IAC/B;IACA,CAAAA,sBAAA,OAAI,CAACZ,kBAAkB,cAAAY,sBAAA,eAAvBA,sBAAA,CAAyBb,OAAO,CAAC,CAAC;IAClC,IAAI,CAACC,kBAAkB,GAAGjP,eAAe,CAAC,MAAM;MAC5C,MAAM8P,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MACtC;MACA,IAAI,CAACD,SAAS,IAAI,CAACA,SAAS,CAACE,QAAQ,CAACC,QAAQ,CAAC3J,aAAa,CAAC,EAAE;QAC3D,MAAM8H,OAAO,GAAG,IAAI,CAAChB,WAAW;QAChCgB,OAAO,CAACsB,cAAc,CAAChM,MAAM,CAAC,CAACwM,kBAAkB,CAAC,CAAC;QACnD;QACA;QACA;QACA,IAAI,CAAC9B,OAAO,CAACC,UAAU,IAAIyB,SAAS,EAAE;UAClCA,SAAS,CAACrM,KAAK,CAAC,CAAC;QACrB;MACJ;IACJ,CAAC,EAAE;MAAE0M,QAAQ,EAAE,IAAI,CAAClG;IAAU,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACImG,eAAeA,CAAA,EAAG;IACd,IAAI,CAAChD,WAAW,CAACwB,aAAa,CAAC,CAAC,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACIyB,YAAYA,CAACC,MAAM,EAAE,CAAE;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIrE,kBAAkBA,CAACsE,IAAI,GAAG,IAAI,CAAC9E,SAAS,EAAE+E,IAAI,GAAG,IAAI,CAAC9E,SAAS,EAAE;IAC7D,IAAI,CAACtN,UAAU,GAAAoO,aAAA,CAAAA,aAAA,KACR,IAAI,CAACpO,UAAU;MAClB,CAAC,iBAAiB,GAAGmS,IAAI,KAAK,QAAQ;MACtC,CAAC,gBAAgB,GAAGA,IAAI,KAAK,OAAO;MACpC,CAAC,gBAAgB,GAAGC,IAAI,KAAK,OAAO;MACpC,CAAC,gBAAgB,GAAGA,IAAI,KAAK;IAAO,EACvC;IACD,IAAI,CAACzK,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACAhI,gBAAgBA,CAACyS,KAAK,EAAE;IACpB,MAAMC,MAAM,GAAGD,KAAK,KAAK5E,cAAc;IACvC,IAAI6E,MAAM,IAAID,KAAK,KAAK7E,eAAe,EAAE;MACrC,IAAI8E,MAAM,EAAE;QACRxB,YAAY,CAAC,IAAI,CAACC,oBAAoB,CAAC;QACvC,IAAI,CAACA,oBAAoB,GAAGwB,SAAS;MACzC;MACA,IAAI,CAACC,cAAc,CAAC5M,IAAI,CAAC0M,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;MACnD,IAAI,CAAClS,YAAY,GAAG,KAAK;IAC7B;EACJ;EACAX,iBAAiBA,CAAC4S,KAAK,EAAE;IACrB,IAAIA,KAAK,KAAK7E,eAAe,IAAI6E,KAAK,KAAK5E,cAAc,EAAE;MACvD,IAAI,CAACrN,YAAY,GAAG,IAAI;IAC5B;EACJ;EACAqS,UAAUA,CAACC,MAAM,EAAE;IACf,IAAI,CAACvS,oBAAoB,GAAGuS,MAAM,GAAG,OAAO,GAAG,MAAM;IACrD,IAAIA,MAAM,EAAE;MACR,IAAI,IAAI,CAAC1D,WAAW,CAACuB,eAAe,KAAK,CAAC,EAAE;QACxC;QACA;QACA;QACA;QACA;QACA;QACA,MAAMmB,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;QACtC,IAAID,SAAS,EAAE;UACXA,SAAS,CAACiB,SAAS,GAAG,CAAC;QAC3B;MACJ;IACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACzS,mBAAmB,EAAE;MAChC;MACA;MACA;MACA,IAAI,CAAC6Q,oBAAoB,GAAG6B,UAAU,CAAC,MAAM,IAAI,CAAChT,gBAAgB,CAAC6N,cAAc,CAAC,EAAE,GAAG,CAAC;IAC5F;IACA;IACA,IAAI,IAAI,CAACvN,mBAAmB,EAAE;MAC1B0S,UAAU,CAAC,MAAM;QACb,IAAI,CAAChT,gBAAgB,CAAC8S,MAAM,GAAGlF,eAAe,GAAGC,cAAc,CAAC;MACpE,CAAC,CAAC;IACN;IACA,IAAI,CAAC9F,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;EACImH,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAAC8D,SAAS,CAACtD,OAAO,CACjBC,IAAI,CAACnM,SAAS,CAAC,IAAI,CAACwP,SAAS,CAAC,CAAC,CAC/BvD,SAAS,CAAEG,KAAK,IAAK;MACtB,IAAI,CAACR,sBAAsB,CAAC6D,KAAK,CAACrD,KAAK,CAAChM,MAAM,CAACkM,IAAI,IAAIA,IAAI,CAACzK,WAAW,KAAK,IAAI,CAAC,CAAC;MAClF,IAAI,CAAC+J,sBAAsB,CAAC8D,eAAe,CAAC,CAAC;IACjD,CAAC,CAAC;EACN;EACA;EACApB,aAAaA,CAAA,EAAG;IACZ,IAAID,SAAS,GAAG,IAAI;IACpB,IAAI,IAAI,CAACzC,sBAAsB,CAAC7H,MAAM,EAAE;MACpC;MACA;MACA;MACA;MACAsK,SAAS,GAAG,IAAI,CAACzC,sBAAsB,CAAC+D,KAAK,CAACtN,eAAe,CAAC,CAAC,CAACuN,OAAO,CAAC,eAAe,CAAC;IAC5F;IACA,OAAOvB,SAAS;EACpB;AAGJ;AAACwB,QAAA,GA7VKxF,OAAO;AAAA3I,eAAA,CAAP2I,OAAO,wBAAAyF,iBAAA9K,iBAAA;EAAA,YAAAA,iBAAA,IA2V0FqF,QAAO;AAAA;AAAA3I,eAAA,CA3VxG2I,OAAO,8BA9IoEtP,EAAE,CAAAkK,iBAAA;EAAAC,IAAA,EA0eQmF,QAAO;EAAAlF,SAAA;EAAA4K,cAAA,WAAAC,wBAAA1U,EAAA,EAAAC,GAAA,EAAA0U,QAAA;IAAA,IAAA3U,EAAA;MA1ejBP,EAAE,CAAAmV,cAAA,CAAAD,QAAA,EA0eq0BpI,gBAAgB;MA1ev1B9M,EAAE,CAAAmV,cAAA,CAAAD,QAAA,EA0eo5B3O,WAAW;MA1ej6BvG,EAAE,CAAAmV,cAAA,CAAAD,QAAA,EA0e09B3O,WAAW;IAAA;IAAA,IAAAhG,EAAA;MAAA,IAAA6U,EAAA;MA1ev+BpV,EAAE,CAAAqV,cAAA,CAAAD,EAAA,GAAFpV,EAAE,CAAAsV,WAAA,QAAA9U,GAAA,CAAA+U,WAAA,GAAAH,EAAA,CAAAR,KAAA;MAAF5U,EAAE,CAAAqV,cAAA,CAAAD,EAAA,GAAFpV,EAAE,CAAAsV,WAAA,QAAA9U,GAAA,CAAAiU,SAAA,GAAAW,EAAA;MAAFpV,EAAE,CAAAqV,cAAA,CAAAD,EAAA,GAAFpV,EAAE,CAAAsV,WAAA,QAAA9U,GAAA,CAAA6Q,KAAA,GAAA+D,EAAA;IAAA;EAAA;EAAAI,SAAA,WAAAC,eAAAlV,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFP,EAAE,CAAA0V,WAAA,CA0e+iC1S,WAAW;IAAA;IAAA,IAAAzC,EAAA;MAAA,IAAA6U,EAAA;MA1e5jCpV,EAAE,CAAAqV,cAAA,CAAAD,EAAA,GAAFpV,EAAE,CAAAsV,WAAA,QAAA9U,GAAA,CAAAmV,WAAA,GAAAP,EAAA,CAAAR,KAAA;IAAA;EAAA;EAAAtK,QAAA;EAAAC,YAAA,WAAAqL,sBAAArV,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFP,EAAE,CAAAmC,WAAA,eA0eQ,IAAI,qBAAJ,IAAI,sBAAJ,IAAI;IAAA;EAAA;EAAAyI,MAAA;IAAAuE,aAAA;IAAA/M,SAAA;IAAAC,cAAA;IAAAC,eAAA;IAAA2M,SAAA;IAAAC,SAAA;IAAAF,cAAA,0CAAuVrM,gBAAgB;IAAA6N,WAAA,oCAAgDhB,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG7M,gBAAgB,CAAC6M,KAAK,CAAE;IAAAG,UAAA;IAAAS,SAAA;EAAA;EAAAyF,OAAA;IAAA5U,MAAA;IAAA6U,KAAA;EAAA;EAAAhL,QAAA;EAAAyD,QAAA,GA1e/dvO,EAAE,CAAAwO,kBAAA,CA0e+sB,CAAC;IAAEC,OAAO,EAAEnI,cAAc;IAAEoI,WAAW,EAAEY;EAAQ,CAAC,CAAC;EAAArE,kBAAA,EAAA5K,GAAA;EAAA8K,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAyK,kBAAAxV,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA1epwBP,EAAE,CAAAwL,eAAA;MAAFxL,EAAE,CAAA0L,UAAA,IAAApL,+BAAA,sBA0e8oC,CAAC;IAAA;EAAA;EAAA0V,MAAA;EAAAjK,aAAA;EAAAC,eAAA;AAAA;AAEluC;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5eiFjM,EAAE,CAAAkM,iBAAA,CA4eQoD,OAAO,EAAc,CAAC;IACrGnF,IAAI,EAAEvH,SAAS;IACfuJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEJ,eAAe,EAAEnJ,uBAAuB,CAACyJ,MAAM;MAAEP,aAAa,EAAEjJ,iBAAiB,CAACyJ,IAAI;MAAEzB,QAAQ,EAAE,SAAS;MAAEuB,IAAI,EAAE;QACtI,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,yBAAyB,EAAE;MAC/B,CAAC;MAAEsC,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEnI,cAAc;QAAEoI,WAAW,EAAEY;MAAQ,CAAC,CAAC;MAAEhE,QAAQ,EAAE,21BAA21B;MAAE0K,MAAM,EAAE,CAAC,08JAA08J;IAAE,CAAC;EAC54L,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEvB,SAAS,EAAE,CAAC;MACpDtK,IAAI,EAAE1G,eAAe;MACrB0I,IAAI,EAAE,CAAC5F,WAAW,EAAE;QAAE0P,WAAW,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC;IAAE9G,aAAa,EAAE,CAAC;MAChBhF,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAEX,SAAS,EAAE,CAAC;MACZ+H,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE9J,cAAc,EAAE,CAAC;MACjB8H,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE7J,eAAe,EAAE,CAAC;MAClB6H,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE8C,SAAS,EAAE,CAAC;MACZ9E,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAEmM,SAAS,EAAE,CAAC;MACZ/E,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAE4S,WAAW,EAAE,CAAC;MACdxL,IAAI,EAAEzG,SAAS;MACfyI,IAAI,EAAE,CAACnJ,WAAW;IACtB,CAAC,CAAC;IAAEqO,KAAK,EAAE,CAAC;MACRlH,IAAI,EAAE1G,eAAe;MACrB0I,IAAI,EAAE,CAAC5F,WAAW,EAAE;QAAE0P,WAAW,EAAE;MAAM,CAAC;IAC9C,CAAC,CAAC;IAAEV,WAAW,EAAE,CAAC;MACdpL,IAAI,EAAExG,YAAY;MAClBwI,IAAI,EAAE,CAACW,gBAAgB;IAC3B,CAAC,CAAC;IAAEkC,cAAc,EAAE,CAAC;MACjB7E,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE9J;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6N,WAAW,EAAE,CAAC;MACdrG,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAG+C,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG7M,gBAAgB,CAAC6M,KAAK;MAAG,CAAC;IACrF,CAAC,CAAC;IAAEG,UAAU,EAAE,CAAC;MACbxF,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEiE,SAAS,EAAE,CAAC;MACZjG,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAE9B,MAAM,EAAE,CAAC;MACTkJ,IAAI,EAAEvG;IACV,CAAC,CAAC;IAAEkS,KAAK,EAAE,CAAC;MACR3L,IAAI,EAAEvG;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMsS,wBAAwB,GAAG,IAAI3T,cAAc,CAAC,0BAA0B,EAAE;EAC5EsM,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMqH,OAAO,GAAG3T,MAAM,CAACuD,OAAO,CAAC;IAC/B,OAAO,MAAMoQ,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;EACtD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASC,gCAAgCA,CAACH,OAAO,EAAE;EAC/C,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,yCAAyC,GAAG;EAC9C9H,OAAO,EAAEyH,wBAAwB;EACjCM,IAAI,EAAE,CAACzQ,OAAO,CAAC;EACf0Q,UAAU,EAAEH;AAChB,CAAC;AACD;AACA,MAAMI,2BAA2B,GAAG;EAAEC,OAAO,EAAE;AAAK,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,CAAC;AAChC;AACA,MAAMC,kBAAkB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACxC;AACA,MAAMC,cAAc,CAAC;EA+BjB;AACJ;AACA;AACA;EACI,IAAIC,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACC,IAAI;EACpB;EACA,IAAID,4BAA4BA,CAACE,CAAC,EAAE;IAChC,IAAI,CAACD,IAAI,GAAGC,CAAC;EACjB;EACA;EACA,IAAID,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACE,KAAK;EACrB;EACA,IAAIF,IAAIA,CAACA,IAAI,EAAE;IAAA,IAAAG,qBAAA;IACX,IAAIH,IAAI,KAAK,IAAI,CAACE,KAAK,EAAE;MACrB;IACJ;IACA,IAAI,CAACA,KAAK,GAAGF,IAAI;IACjB,IAAI,CAACI,sBAAsB,CAACC,WAAW,CAAC,CAAC;IACzC,IAAIL,IAAI,EAAE;MACN,IAAIA,IAAI,KAAK,IAAI,CAACM,mBAAmB,KAAK,OAAOtL,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACtFY,0BAA0B,CAAC,CAAC;MAChC;MACA,IAAI,CAACwK,sBAAsB,GAAGJ,IAAI,CAACnB,KAAK,CAAC5E,SAAS,CAAEsG,MAAM,IAAK;QAC3D,IAAI,CAACC,YAAY,CAACD,MAAM,CAAC;QACzB;QACA,IAAI,CAACA,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,KAAK,KAAK,IAAI,CAACD,mBAAmB,EAAE;UACtE,IAAI,CAACA,mBAAmB,CAACtW,MAAM,CAACC,IAAI,CAACsW,MAAM,CAAC;QAChD;MACJ,CAAC,CAAC;IACN;IACA,CAAAJ,qBAAA,OAAI,CAACM,iBAAiB,cAAAN,qBAAA,eAAtBA,qBAAA,CAAwB3N,mBAAmB,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;EACvE;EA4BAlD,WAAWA,CAAA,EAAG;IAAAG,eAAA,mBA3FHnE,MAAM,CAACuD,OAAO,CAAC;IAAAY,eAAA,mBACfnE,MAAM,CAACC,UAAU,CAAC;IAAAkE,eAAA,4BACTnE,MAAM,CAACW,gBAAgB,CAAC;IAAAwD,eAAA,4BACxBnE,MAAM,CAAC+D,WAAW,EAAE;MAAEK,QAAQ,EAAE,IAAI;MAAE+Q,IAAI,EAAE;IAAK,CAAC,CAAC;IAAAhR,eAAA,eAChEnE,MAAM,CAACsD,cAAc,EAAE;MAAEc,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAD,eAAA,wBACjCnE,MAAM,CAACwB,YAAY,CAAC;IAAA2C,eAAA,kBAC1BnE,MAAM,CAACqB,MAAM,CAAC;IAAA8C,eAAA,0BACNnE,MAAM,CAAC0T,wBAAwB,CAAC;IAAAvP,eAAA,6BAC7BnE,MAAM,CAACE,iBAAiB,CAAC;IAAAiE,eAAA;IAAAA,eAAA;IAAAA,eAAA,sBAGhC,IAAI;IAAAA,eAAA,oBACN,KAAK;IAAAA,eAAA,sCACa5B,YAAY,CAAC6S,KAAK;IAAAjR,eAAA,6BAC3B5B,YAAY,CAAC6S,KAAK;IAAAjR,eAAA,iCACd5B,YAAY,CAAC6S,KAAK;IAAAjR,eAAA;IAE3C;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IACA;IAAAA,eAAA,oBACYwN,SAAS;IAAAxN,eAAA;IAoCrB;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;IAJIA,eAAA,uBAKe,IAAI;IACnB;IAAAA,eAAA,qBACa,IAAIrD,YAAY,CAAC,CAAC;IAC/B;AACJ;AACA;AACA;AACA;IACI;IAAAqD,eAAA,qBACa,IAAI,CAACkR,UAAU;IAC5B;IAAAlR,eAAA,qBACa,IAAIrD,YAAY,CAAC,CAAC;IAC/B;AACJ;AACA;AACA;AACA;IACI;IAAAqD,eAAA,sBACc,IAAI,CAACmR,UAAU;IAEzB,MAAM9E,UAAU,GAAGxQ,MAAM,CAAC8D,cAAc,EAAE;MAAEM,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC7D,MAAMmR,QAAQ,GAAGvV,MAAM,CAACsB,SAAS,CAAC;IAClC,IAAI,CAACyT,mBAAmB,GAAGvE,UAAU,YAAY1D,OAAO,GAAG0D,UAAU,GAAGmB,SAAS;IACjF,IAAI,CAAC6D,kBAAkB,GAAG9R,qBAAqB,CAAC6R,QAAQ,EAAE,IAAI,CAACE,QAAQ,CAAC9P,aAAa,EAAE,YAAY,EAAGE,KAAK,IAAK;MAC5G,IAAI,CAAClE,gCAAgC,CAACkE,KAAK,CAAC,EAAE;QAC1C,IAAI,CAAC6P,SAAS,GAAG,OAAO;MAC5B;IACJ,CAAC,EAAExB,2BAA2B,CAAC;EACnC;EACAhG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACyH,YAAY,CAAC,CAAC;EACvB;EACAvQ,WAAWA,CAAA,EAAG;IAAA,IAAAwQ,qBAAA;IACV,IAAI,IAAI,CAACnB,IAAI,IAAI,IAAI,CAACoB,SAAS,CAAC,IAAI,CAACpB,IAAI,CAAC,EAAE;MACxCJ,kBAAkB,CAACyB,MAAM,CAAC,IAAI,CAACrB,IAAI,CAAC;IACxC;IACA,IAAI,CAACe,kBAAkB,CAAC,CAAC;IACzB,CAAAI,qBAAA,OAAI,CAACG,eAAe,cAAAH,qBAAA,eAApBA,qBAAA,CAAsBd,WAAW,CAAC,CAAC;IACnC,IAAI,CAACD,sBAAsB,CAACC,WAAW,CAAC,CAAC;IACzC,IAAI,CAACkB,2BAA2B,CAAClB,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACmB,kBAAkB,CAACnB,WAAW,CAAC,CAAC;IACrC,IAAI,IAAI,CAACoB,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACvK,OAAO,CAAC,CAAC;MAC1B,IAAI,CAACuK,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA;EACA,IAAIC,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACC,IAAI,IAAI,IAAI,CAACA,IAAI,CAACtJ,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACA9F,eAAeA,CAAA,EAAG;IACd,OAAO,CAAC,EAAE,IAAI,CAACgO,iBAAiB,IAAI,IAAI,CAACH,mBAAmB,IAAI,IAAI,CAACN,IAAI,CAAC;EAC9E;EACA;EACA8B,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACH,SAAS,GAAG,IAAI,CAACI,SAAS,CAAC,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC9D;EACA;EACAA,QAAQA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACP,MAAMjC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,IAAI,CAAC2B,SAAS,IAAI,CAAC3B,IAAI,EAAE;MACzB;IACJ;IACA,CAAAiC,sBAAA,OAAI,CAACX,eAAe,cAAAW,sBAAA,eAApBA,sBAAA,CAAsB5B,WAAW,CAAC,CAAC;IACnC,MAAM6B,eAAe,GAAGtC,kBAAkB,CAACuC,GAAG,CAACnC,IAAI,CAAC;IACpDJ,kBAAkB,CAACwC,GAAG,CAACpC,IAAI,EAAE,IAAI,CAAC;IAClC;IACA;IACA,IAAIkC,eAAe,IAAIA,eAAe,KAAK,IAAI,EAAE;MAC7CA,eAAe,CAACH,SAAS,CAAC,CAAC;IAC/B;IACA,MAAMM,UAAU,GAAG,IAAI,CAACC,cAAc,CAACtC,IAAI,CAAC;IAC5C,MAAMuC,aAAa,GAAGF,UAAU,CAACG,SAAS,CAAC,CAAC;IAC5C,MAAMC,gBAAgB,GAAGF,aAAa,CAACE,gBAAgB;IACvD,IAAI,CAACC,YAAY,CAAC1C,IAAI,EAAEyC,gBAAgB,CAAC;IACzCF,aAAa,CAAChJ,WAAW,GACrByG,IAAI,CAACzG,WAAW,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC9G,eAAe,CAAC,CAAC,GAAGuN,IAAI,CAACzG,WAAW;IACzE;IACA;IACA,IAAI,CAAC8I,UAAU,CAACM,WAAW,CAAC,CAAC,EAAE;MAAA,IAAAC,iBAAA;MAC3BP,UAAU,CAACtM,MAAM,CAAC,IAAI,CAAC8M,UAAU,CAAC7C,IAAI,CAAC,CAAC;MACxC,CAAA4C,iBAAA,GAAA5C,IAAI,CAAC1B,WAAW,cAAAsE,iBAAA,eAAhBA,iBAAA,CAAkB7M,MAAM,CAAC,IAAI,CAAC+M,QAAQ,CAAC;IAC3C;IACA,IAAI,CAACvB,2BAA2B,GAAG,IAAI,CAACwB,mBAAmB,CAAC,CAAC,CAAC9I,SAAS,CAAC,MAAM,IAAI,CAAC8H,SAAS,CAAC,CAAC,CAAC;IAC/F/B,IAAI,CAACjE,UAAU,GAAG,IAAI,CAACtJ,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC6N,mBAAmB,GAAGpD,SAAS;IAC/E8C,IAAI,CAAChE,SAAS,GAAG,IAAI,CAAC4F,GAAG;IACzB5B,IAAI,CAAC7D,cAAc,CAAC,IAAI,CAAC8E,SAAS,IAAI,SAAS,CAAC;IAChD,IAAI,CAAC+B,cAAc,CAAC,IAAI,CAAC;IACzB,IAAIhD,IAAI,YAAY3H,OAAO,EAAE;MACzB2H,IAAI,CAAC5C,UAAU,CAAC,IAAI,CAAC;MACrB4C,IAAI,CAACpG,sBAAsB,CAACM,OAAO,CAACC,IAAI,CAACjM,SAAS,CAAC8R,IAAI,CAACnB,KAAK,CAAC,CAAC,CAAC5E,SAAS,CAAC,MAAM;QAC5E;QACA;QACAwI,gBAAgB,CAACQ,kBAAkB,CAAC,KAAK,CAAC,CAACC,mBAAmB,CAAC,CAAC;QAChET,gBAAgB,CAACQ,kBAAkB,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC;IACN;EACJ;EACA;EACAlB,SAASA,CAAA,EAAG;IAAA,IAAAoB,UAAA;IACR,CAAAA,UAAA,OAAI,CAACnD,IAAI,cAAAmD,UAAA,eAATA,UAAA,CAAWtE,KAAK,CAAC5U,IAAI,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;AACA;EACI+F,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACC,aAAa,IAAIF,MAAM,EAAE;MAC9B,IAAI,CAACE,aAAa,CAACC,QAAQ,CAAC,IAAI,CAAC4Q,QAAQ,EAAE/Q,MAAM,EAAEC,OAAO,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAAC8Q,QAAQ,CAAC9P,aAAa,CAAClB,KAAK,CAACE,OAAO,CAAC;IAC9C;EACJ;EACA;AACJ;AACA;EACIkT,cAAcA,CAAA,EAAG;IAAA,IAAAC,iBAAA;IACb,CAAAA,iBAAA,OAAI,CAAC5B,WAAW,cAAA4B,iBAAA,eAAhBA,iBAAA,CAAkBD,cAAc,CAAC,CAAC;EACtC;EACA;EACA5C,YAAYA,CAACD,MAAM,EAAE;IAAA,IAAA+C,sBAAA;IACjB,MAAMjB,UAAU,GAAG,IAAI,CAACZ,WAAW;IACnC,MAAMzB,IAAI,GAAG,IAAI,CAACE,KAAK;IACvB,IAAI,CAACmC,UAAU,IAAI,CAAC,IAAI,CAACX,QAAQ,EAAE;MAC/B;IACJ;IACA,IAAI,CAACH,2BAA2B,CAAClB,WAAW,CAAC,CAAC;IAC9C,CAAAiD,sBAAA,OAAI,CAAChC,eAAe,cAAAgC,sBAAA,eAApBA,sBAAA,CAAsBjD,WAAW,CAAC,CAAC;IACnC;IACA;IACA,IAAIL,IAAI,YAAY3H,OAAO,IAAI,IAAI,CAAC+I,SAAS,CAACpB,IAAI,CAAC,EAAE;MACjD,IAAI,CAACsB,eAAe,GAAGtB,IAAI,CAAC7C,cAAc,CAAChD,IAAI,CAAChM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC8L,SAAS,CAAC,MAAM;QAAA,IAAAsJ,kBAAA;QACrElB,UAAU,CAACjM,MAAM,CAAC,CAAC;QACnB,CAAAmN,kBAAA,GAAAvD,IAAI,CAAC1B,WAAW,cAAAiF,kBAAA,eAAhBA,kBAAA,CAAkBnN,MAAM,CAAC,CAAC;MAC9B,CAAC,CAAC;MACF4J,IAAI,CAAC5C,UAAU,CAAC,KAAK,CAAC;IAC1B,CAAC,MACI;MAAA,IAAAoG,kBAAA;MACDnB,UAAU,CAACjM,MAAM,CAAC,CAAC;MACnB4J,IAAI,aAAJA,IAAI,gBAAAwD,kBAAA,GAAJxD,IAAI,CAAE1B,WAAW,cAAAkF,kBAAA,eAAjBA,kBAAA,CAAmBpN,MAAM,CAAC,CAAC;IAC/B;IACA,IAAI4J,IAAI,IAAI,IAAI,CAACoB,SAAS,CAACpB,IAAI,CAAC,EAAE;MAC9BJ,kBAAkB,CAACyB,MAAM,CAACrB,IAAI,CAAC;IACnC;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACyD,YAAY,KAAKlD,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI,CAACU,SAAS,IAAI,CAAC,IAAI,CAACxO,eAAe,CAAC,CAAC,CAAC,EAAE;MAC3F,IAAI,CAACzC,KAAK,CAAC,IAAI,CAACiR,SAAS,CAAC;IAC9B;IACA,IAAI,CAACA,SAAS,GAAG/D,SAAS;IAC1B,IAAI,CAAC8F,cAAc,CAAC,KAAK,CAAC;EAC9B;EACA;EACAA,cAAcA,CAAC3F,MAAM,EAAE;IACnB,IAAIA,MAAM,KAAK,IAAI,CAACsE,SAAS,EAAE;MAC3B,IAAI,CAACA,SAAS,GAAGtE,MAAM;MACvB,IAAI,CAACsE,SAAS,GAAG,IAAI,CAACf,UAAU,CAAC3W,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC4W,UAAU,CAAC5W,IAAI,CAAC,CAAC;MAChE,IAAI,IAAI,CAACwI,eAAe,CAAC,CAAC,EAAE;QACxB,IAAI,CAACgO,iBAAiB,CAACtO,eAAe,CAACkL,MAAM,CAAC;MAClD;MACA,IAAI,CAAC/K,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;EACI+P,cAAcA,CAACtC,IAAI,EAAE;IACjB,IAAI,CAAC,IAAI,CAACyB,WAAW,EAAE;MACnB,MAAMiC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAAC3D,IAAI,CAAC;MAC3C,IAAI,CAAC4D,qBAAqB,CAAC5D,IAAI,EAAE0D,MAAM,CAACjB,gBAAgB,CAAC;MACzD,IAAI,CAAChB,WAAW,GAAG,IAAI,CAACoC,QAAQ,CAACC,MAAM,CAACJ,MAAM,CAAC;MAC/C,IAAI,CAACjC,WAAW,CAACsC,aAAa,CAAC,CAAC,CAAC9J,SAAS,CAAC7I,KAAK,IAAI;QAChD,IAAI,IAAI,CAAC4O,IAAI,YAAY3H,OAAO,EAAE;UAC9B,IAAI,CAAC2H,IAAI,CAACnE,cAAc,CAACzK,KAAK,CAAC;QACnC;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACqQ,WAAW;EAC3B;EACA;AACJ;AACA;AACA;EACIkC,iBAAiBA,CAAC3D,IAAI,EAAE;IACpB,OAAO,IAAIjR,aAAa,CAAC;MACrB0T,gBAAgB,EAAE,IAAI,CAACoB,QAAQ,CAC1BG,QAAQ,CAAC,CAAC,CACVC,mBAAmB,CAAC,IAAI,CAACjD,QAAQ,CAAC,CAClCiC,kBAAkB,CAAC,CAAC,CACpBiB,iBAAiB,CAAC,CAAC,CACnBC,qBAAqB,CAAC,sCAAsC,CAAC;MAClEjM,aAAa,EAAE8H,IAAI,CAAC9H,aAAa,IAAI,kCAAkC;MACvEQ,UAAU,EAAEsH,IAAI,CAAC1G,iBAAiB;MAClC8K,cAAc,EAAE,IAAI,CAACC,eAAe,CAAC,CAAC;MACtCrI,SAAS,EAAE,IAAI,CAAC6F,IAAI,IAAI;IAC5B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI+B,qBAAqBA,CAAC5D,IAAI,EAAEgE,QAAQ,EAAE;IAClC,IAAIhE,IAAI,CAACxH,kBAAkB,EAAE;MACzBwL,QAAQ,CAACM,eAAe,CAACrK,SAAS,CAACsK,MAAM,IAAI;QACzC,IAAI,CAACC,OAAO,CAACC,GAAG,CAAC,MAAM;UACnB,MAAM3H,IAAI,GAAGyH,MAAM,CAACG,cAAc,CAACC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;UAC5E,MAAM5H,IAAI,GAAGwH,MAAM,CAACG,cAAc,CAACE,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;UACzE5E,IAAI,CAACxH,kBAAkB,CAACsE,IAAI,EAAEC,IAAI,CAAC;QACvC,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2F,YAAYA,CAAC1C,IAAI,EAAEyC,gBAAgB,EAAE;IACjC,IAAI,CAACoC,OAAO,EAAEC,eAAe,CAAC,GAAG9E,IAAI,CAAChI,SAAS,KAAK,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;IAClG,IAAI,CAAC4M,QAAQ,EAAEG,gBAAgB,CAAC,GAAG/E,IAAI,CAAC/H,SAAS,KAAK,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;IACrG,IAAI,CAAC+M,OAAO,EAAEC,eAAe,CAAC,GAAG,CAACL,QAAQ,EAAEG,gBAAgB,CAAC;IAC7D,IAAI,CAACJ,QAAQ,EAAEO,gBAAgB,CAAC,GAAG,CAACL,OAAO,EAAEC,eAAe,CAAC;IAC7D,IAAIK,OAAO,GAAG,CAAC;IACf,IAAI,IAAI,CAAC1S,eAAe,CAAC,CAAC,EAAE;MACxB;MACA;MACAyS,gBAAgB,GAAGL,OAAO,GAAG7E,IAAI,CAAChI,SAAS,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAK;MAC1E8M,eAAe,GAAGH,QAAQ,GAAGE,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK;MAChE,IAAI,IAAI,CAACvE,mBAAmB,EAAE;QAC1B,IAAI,IAAI,CAAC8E,mBAAmB,IAAI,IAAI,EAAE;UAClC,MAAMC,SAAS,GAAG,IAAI,CAAC/E,mBAAmB,CAAClG,KAAK,CAACuD,KAAK;UACtD,IAAI,CAACyH,mBAAmB,GAAGC,SAAS,GAAGA,SAAS,CAAChV,eAAe,CAAC,CAAC,CAACiV,SAAS,GAAG,CAAC;QACpF;QACAH,OAAO,GAAGP,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAACQ,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;MAC1F;IACJ,CAAC,MACI,IAAI,CAACpF,IAAI,CAACjI,cAAc,EAAE;MAC3BiN,OAAO,GAAGJ,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;MAC/CK,eAAe,GAAGF,gBAAgB,KAAK,KAAK,GAAG,QAAQ,GAAG,KAAK;IACnE;IACAtC,gBAAgB,CAAC8C,aAAa,CAAC,CAC3B;MAAEV,OAAO;MAAEG,OAAO;MAAEL,QAAQ;MAAEC,QAAQ;MAAEO;IAAQ,CAAC,EACjD;MAAEN,OAAO,EAAEC,eAAe;MAAEE,OAAO;MAAEL,QAAQ,EAAEO,gBAAgB;MAAEN,QAAQ;MAAEO;IAAQ,CAAC,EACpF;MACIN,OAAO;MACPG,OAAO,EAAEC,eAAe;MACxBN,QAAQ;MACRC,QAAQ,EAAEG,gBAAgB;MAC1BI,OAAO,EAAE,CAACA;IACd,CAAC,EACD;MACIN,OAAO,EAAEC,eAAe;MACxBE,OAAO,EAAEC,eAAe;MACxBN,QAAQ,EAAEO,gBAAgB;MAC1BN,QAAQ,EAAEG,gBAAgB;MAC1BI,OAAO,EAAE,CAACA;IACd,CAAC,CACJ,CAAC;EACN;EACA;EACApC,mBAAmBA,CAAA,EAAG;IAClB,MAAMyC,QAAQ,GAAG,IAAI,CAAC/D,WAAW,CAACgE,aAAa,CAAC,CAAC;IACjD,MAAMC,WAAW,GAAG,IAAI,CAACjE,WAAW,CAACiE,WAAW,CAAC,CAAC;IAClD,MAAMC,WAAW,GAAG,IAAI,CAACrF,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACtW,MAAM,GAAG+D,EAAE,CAAC,CAAC;IACrF,MAAM6X,KAAK,GAAG,IAAI,CAACtF,mBAAmB,GAChC,IAAI,CAACA,mBAAmB,CACrBxP,QAAQ,CAAC,CAAC,CACVqJ,IAAI,CAAC/L,MAAM,CAACyX,MAAM,IAAI,IAAI,CAAClE,SAAS,IAAIkE,MAAM,KAAK,IAAI,CAACpF,iBAAiB,CAAC,CAAC,GAC9E1S,EAAE,CAAC,CAAC;IACV,OAAOF,KAAK,CAAC2X,QAAQ,EAAEG,WAAW,EAAEC,KAAK,EAAEF,WAAW,CAAC;EAC3D;EACA;EACAI,gBAAgBA,CAAC1U,KAAK,EAAE;IACpB,IAAI,CAACjE,+BAA+B,CAACiE,KAAK,CAAC,EAAE;MACzC;MACA;MACA,IAAI,CAAC6P,SAAS,GAAG7P,KAAK,CAAC2U,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG7I,SAAS;MACzD;MACA;MACA;MACA,IAAI,IAAI,CAACzK,eAAe,CAAC,CAAC,EAAE;QACxBrB,KAAK,CAACC,cAAc,CAAC,CAAC;MAC1B;IACJ;EACJ;EACA;EACAwK,cAAcA,CAACzK,KAAK,EAAE;IAClB,MAAM0K,OAAO,GAAG1K,KAAK,CAAC0K,OAAO;IAC7B;IACA,IAAIA,OAAO,KAAKpO,KAAK,IAAIoO,OAAO,KAAKnO,KAAK,EAAE;MACxC,IAAI,CAACsT,SAAS,GAAG,UAAU;IAC/B;IACA,IAAI,IAAI,CAACxO,eAAe,CAAC,CAAC,KACpBqJ,OAAO,KAAKxO,WAAW,IAAI,IAAI,CAACsU,GAAG,KAAK,KAAK,IAC1C9F,OAAO,KAAKvO,UAAU,IAAI,IAAI,CAACqU,GAAG,KAAK,KAAM,CAAC,EAAE;MACrD,IAAI,CAACX,SAAS,GAAG,UAAU;MAC3B,IAAI,CAACe,QAAQ,CAAC,CAAC;IACnB;EACJ;EACA;EACAgE,YAAYA,CAAC5U,KAAK,EAAE;IAChB,IAAI,IAAI,CAACqB,eAAe,CAAC,CAAC,EAAE;MACxB;MACArB,KAAK,CAACE,eAAe,CAAC,CAAC;MACvB,IAAI,CAAC0Q,QAAQ,CAAC,CAAC;IACnB,CAAC,MACI;MACD,IAAI,CAACF,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;EACAZ,YAAYA,CAAA,EAAG;IACX;IACA,IAAI,IAAI,CAACzO,eAAe,CAAC,CAAC,IAAI,IAAI,CAAC6N,mBAAmB,EAAE;MACpD,IAAI,CAACkB,kBAAkB,GAAG,IAAI,CAAClB,mBAAmB,CAACxP,QAAQ,CAAC,CAAC,CAACmJ,SAAS,CAAC4L,MAAM,IAAI;QAC9E,IAAIA,MAAM,KAAK,IAAI,CAACpF,iBAAiB,IAAI,CAACoF,MAAM,CAAC5U,QAAQ,EAAE;UACvD,IAAI,CAACgQ,SAAS,GAAG,OAAO;UACxB,IAAI,CAACe,QAAQ,CAAC,CAAC;QACnB;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAa,UAAUA,CAAC7C,IAAI,EAAE;IACb;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC/J,OAAO,IAAI,IAAI,CAACA,OAAO,CAACyI,WAAW,KAAKsB,IAAI,CAACtB,WAAW,EAAE;MAChE,IAAI,CAACzI,OAAO,GAAG,IAAItH,cAAc,CAACqR,IAAI,CAACtB,WAAW,EAAE,IAAI,CAACvI,iBAAiB,CAAC;IAC/E;IACA,OAAO,IAAI,CAACF,OAAO;EACvB;EACA;AACJ;AACA;AACA;AACA;EACImL,SAASA,CAACpB,IAAI,EAAE;IACZ,OAAOJ,kBAAkB,CAACuC,GAAG,CAACnC,IAAI,CAAC,KAAK,IAAI;EAChD;AAGJ;AAACiG,eAAA,GAzaKnG,cAAc;AAAApQ,eAAA,CAAdoQ,cAAc,wBAAAoG,wBAAAlT,iBAAA;EAAA,YAAAA,iBAAA,IAuamF8M,eAAc;AAAA;AAAApQ,eAAA,CAva/GoQ,cAAc,8BApkB6D/W,EAAE,CAAAsO,iBAAA;EAAAnE,IAAA,EA4+BQ4M,eAAc;EAAA3M,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAA6S,6BAAA7c,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA5+BxBP,EAAE,CAAAW,UAAA,mBAAA0c,yCAAAjc,MAAA;QAAA,OA4+BQZ,GAAA,CAAAyc,YAAA,CAAA7b,MAAmB,CAAC;MAAA,CAAP,CAAC,uBAAAkc,6CAAAlc,MAAA;QAAA,OAAdZ,GAAA,CAAAuc,gBAAA,CAAA3b,MAAuB,CAAC;MAAA,CAAX,CAAC,qBAAAmc,2CAAAnc,MAAA;QAAA,OAAdZ,GAAA,CAAAsS,cAAA,CAAA1R,MAAqB,CAAC;MAAA,CAAT,CAAC;IAAA;IAAA,IAAAb,EAAA;MA5+BxBP,EAAE,CAAAmC,WAAA,kBAAA3B,GAAA,CAAAyW,IAAA,GA4+Be,MAAM,GAAG,IAAI,mBAAAzW,GAAA,CAAAmY,QAAA,mBAAAnY,GAAA,CAAAmY,QAAA,GAAAnY,GAAA,CAAAyW,IAAA,CAAA/U,OAAA,GAAM,IAAI;IAAA;EAAA;EAAA0I,MAAA;IAAAoM,4BAAA;IAAAC,IAAA;IAAA8C,QAAA;IAAAW,YAAA;EAAA;EAAA7E,OAAA;IAAAgC,UAAA;IAAA2F,UAAA;IAAA1F,UAAA;IAAA2F,WAAA;EAAA;EAAA3S,QAAA;AAAA;AAEzH;EAAA,QAAAmB,SAAA,oBAAAA,SAAA,KA9+BiFjM,EAAE,CAAAkM,iBAAA,CA8+BQ6K,cAAc,EAAc,CAAC;IAC5G5M,IAAI,EAAE/G,SAAS;IACf+I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6CAA6C;MACvDC,IAAI,EAAE;QACF,OAAO,EAAE,sBAAsB;QAC/B,sBAAsB,EAAE,sBAAsB;QAC9C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,gCAAgC;QACxD,SAAS,EAAE,sBAAsB;QACjC,aAAa,EAAE,0BAA0B;QACzC,WAAW,EAAE;MACjB,CAAC;MACDvB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEkM,4BAA4B,EAAE,CAAC;MACvE7M,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAE8K,IAAI,EAAE,CAAC;MACP9M,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE4N,QAAQ,EAAE,CAAC;MACX5P,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEuO,YAAY,EAAE,CAAC;MACfvQ,IAAI,EAAEpH,KAAK;MACXoJ,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAE0L,UAAU,EAAE,CAAC;MACb1N,IAAI,EAAEvG;IACV,CAAC,CAAC;IAAE4Z,UAAU,EAAE,CAAC;MACbrT,IAAI,EAAEvG;IACV,CAAC,CAAC;IAAEkU,UAAU,EAAE,CAAC;MACb3N,IAAI,EAAEvG;IACV,CAAC,CAAC;IAAE6Z,WAAW,EAAE,CAAC;MACdtT,IAAI,EAAEvG;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8Z,aAAa,CAAC;AAkBnBC,cAAA,GAlBKD,aAAa;AAAA/W,eAAA,CAAb+W,aAAa,wBAAAE,uBAAA3T,iBAAA;EAAA,YAAAA,iBAAA,IACoFyT,cAAa;AAAA;AAAA/W,eAAA,CAD9G+W,aAAa,8BAnhC8D1d,EAAE,CAAA6d,gBAAA;EAAA1T,IAAA,EAqhCqBuT,cAAa;EAAAlR,OAAA,GAAYpG,eAAe,EACpIC,eAAe,EACfJ,aAAa,EACbqJ,OAAO,EACP/I,WAAW,EACXwG,cAAc,EACdgK,cAAc;EAAA+G,OAAA,GAAa3X,mBAAmB,EAC9CmJ,OAAO,EACPjJ,eAAe,EACfE,WAAW,EACXwG,cAAc,EACdgK,cAAc;AAAA;AAAApQ,eAAA,CAbpB+W,aAAa,8BAnhC8D1d,EAAE,CAAA+d,gBAAA;EAAApP,SAAA,EAiiC+C,CAAC4H,yCAAyC,CAAC;EAAA/J,OAAA,GAAYpG,eAAe,EAC5LC,eAAe,EACfJ,aAAa,EAAEE,mBAAmB,EAClCE,eAAe;AAAA;AAE3B;EAAA,QAAA4F,SAAA,oBAAAA,SAAA,KAtiCiFjM,EAAE,CAAAkM,iBAAA,CAsiCQwR,aAAa,EAAc,CAAC;IAC3GvT,IAAI,EAAEpG,QAAQ;IACdoI,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CACLpG,eAAe,EACfC,eAAe,EACfJ,aAAa,EACbqJ,OAAO,EACP/I,WAAW,EACXwG,cAAc,EACdgK,cAAc,CACjB;MACD+G,OAAO,EAAE,CACL3X,mBAAmB,EACnBmJ,OAAO,EACPjJ,eAAe,EACfE,WAAW,EACXwG,cAAc,EACdgK,cAAc,CACjB;MACDpI,SAAS,EAAE,CAAC4H,yCAAyC;IACzD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyH,iBAAiB,GAAG;EACtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,aAAa,EAAE;IACX9T,IAAI,EAAE,CAAC;IACP+T,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE,CACT;MACIhU,IAAI,EAAE,CAAC;MACP+T,IAAI,EAAE,MAAM;MACZlI,MAAM,EAAE;QAAE7L,IAAI,EAAE,CAAC;QAAE6L,MAAM,EAAE;UAAEoI,OAAO,EAAE,CAAC;UAAE3R,SAAS,EAAE;QAAa,CAAC;QAAE4R,MAAM,EAAE;MAAK;IACrF,CAAC,EACD;MACIlU,IAAI,EAAE,CAAC;MACPmU,IAAI,EAAE,eAAe;MACrBC,SAAS,EAAE;QACPpU,IAAI,EAAE,CAAC;QACP6L,MAAM,EAAE;UAAE7L,IAAI,EAAE,CAAC;UAAE6L,MAAM,EAAE;YAAEoI,OAAO,EAAE,CAAC;YAAE3R,SAAS,EAAE;UAAW,CAAC;UAAE4R,MAAM,EAAE;QAAK,CAAC;QAChFG,OAAO,EAAE;MACb,CAAC;MACDrX,OAAO,EAAE;IACb,CAAC,EACD;MACIgD,IAAI,EAAE,CAAC;MACPmU,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE;QACPpU,IAAI,EAAE,CAAC;QACP6L,MAAM,EAAE;UAAE7L,IAAI,EAAE,CAAC;UAAE6L,MAAM,EAAE;YAAEoI,OAAO,EAAE;UAAE,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAC;QACzDG,OAAO,EAAE;MACb,CAAC;MACDrX,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;EACIsX,WAAW,EAAE;IACTtU,IAAI,EAAE,CAAC;IACP+T,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,CACT;MACIhU,IAAI,EAAE,CAAC;MACP+T,IAAI,EAAE,SAAS;MACflI,MAAM,EAAE;QAAE7L,IAAI,EAAE,CAAC;QAAE6L,MAAM,EAAE;UAAEoI,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK;IAC5D,CAAC,EACD;MACIlU,IAAI,EAAE,CAAC;MACPmU,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE,CACP;QAAEpU,IAAI,EAAE,CAAC;QAAE6L,MAAM,EAAE;UAAEoI,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK,CAAC,EACjD;QAAElU,IAAI,EAAE,CAAC;QAAE6L,MAAM,EAAE,IAAI;QAAEwI,OAAO,EAAE;MAA+C,CAAC,CACrF;MACDrX,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMsX,WAAW,GAAGT,iBAAiB,CAACS,WAAW;AACjD;AACA;AACA;AACA;AACA;AACA,MAAMR,aAAa,GAAGD,iBAAiB,CAACC,aAAa;AAErD,SAASnR,gBAAgB,EAAE8B,wBAAwB,EAAEtI,cAAc,EAAE4P,wBAAwB,EAAEK,yCAAyC,EAAEK,sBAAsB,EAAEtH,OAAO,EAAEvC,cAAc,EAAExG,WAAW,EAAEmX,aAAa,EAAE3G,cAAc,EAAE0H,WAAW,EAAET,iBAAiB,EAAEC,aAAa;AACpR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}