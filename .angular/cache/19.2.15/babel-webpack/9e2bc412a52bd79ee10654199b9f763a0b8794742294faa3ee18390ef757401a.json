{"ast": null, "code": "var _InputNumberComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-number.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-number.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nlet InputNumberComponent = (_InputNumberComponent = class InputNumberComponent {\n  constructor() {\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n  }\n  set componentOptions(value) {\n    var _value$validation;\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.clearable = value === null || value === void 0 ? void 0 : value.clearable;\n    this.min = value === null || value === void 0 ? void 0 : value.min;\n    this.max = value === null || value === void 0 ? void 0 : value.max;\n    this.step = value === null || value === void 0 ? void 0 : value.step;\n    this.required = value === null || value === void 0 ? void 0 : value.required;\n    this.errorMessages = value === null || value === void 0 || (_value$validation = value.validation) === null || _value$validation === void 0 ? void 0 : _value$validation.messages;\n  }\n  clear(event) {\n    event.stopPropagation();\n    if (this.control) {\n      this.control.setValue(null);\n    }\n  }\n}, _InputNumberComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }]\n}, _InputNumberComponent);\nInputNumberComponent = __decorate([Component({\n  selector: 'lib-input-number',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputNumberComponent);\nexport { InputNumberComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "InputNumberComponent", "_InputNumberComponent", "constructor", "id", "readonly", "submitted", "componentOptions", "value", "_value$validation", "title", "clearable", "min", "max", "step", "required", "errorMessages", "validation", "messages", "clear", "event", "stopPropagation", "control", "setValue", "propDecorators", "type", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-number/input-number.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-number.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-number.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nlet InputNumberComponent = class InputNumberComponent {\n    constructor() {\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n    }\n    set componentOptions(value) {\n        this.title = value?.title;\n        this.clearable = value?.clearable;\n        this.min = value?.min;\n        this.max = value?.max;\n        this.step = value?.step;\n        this.required = value?.required;\n        this.errorMessages = value?.validation?.messages;\n    }\n    clear(event) {\n        event.stopPropagation();\n        if (this.control) {\n            this.control.setValue(null);\n        }\n    }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }]\n    }; }\n};\nInputNumberComponent = __decorate([\n    Component({\n        selector: 'lib-input-number',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputNumberComponent);\nexport { InputNumberComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,IAAIC,oBAAoB,IAAAC,qBAAA,GAAG,MAAMD,oBAAoB,CAAC;EAClDE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;EAC1B;EACA,IAAIC,gBAAgBA,CAACC,KAAK,EAAE;IAAA,IAAAC,iBAAA;IACxB,IAAI,CAACC,KAAK,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,KAAK;IACzB,IAAI,CAACC,SAAS,GAAGH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,SAAS;IACjC,IAAI,CAACC,GAAG,GAAGJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,GAAG;IACrB,IAAI,CAACC,GAAG,GAAGL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEK,GAAG;IACrB,IAAI,CAACC,IAAI,GAAGN,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,IAAI;IACvB,IAAI,CAACC,QAAQ,GAAGP,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEO,QAAQ;IAC/B,IAAI,CAACC,aAAa,GAAGR,KAAK,aAALA,KAAK,gBAAAC,iBAAA,GAALD,KAAK,CAAES,UAAU,cAAAR,iBAAA,uBAAjBA,iBAAA,CAAmBS,QAAQ;EACpD;EACAC,KAAKA,CAACC,KAAK,EAAE;IACTA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,QAAQ,CAAC,IAAI,CAAC;IAC/B;EACJ;AAQJ,CAAC,EAPYrB,qBAAA,CAAKsB,cAAc,GAAG;EAC3BF,OAAO,EAAE,CAAC;IAAEG,IAAI,EAAEzB;EAAM,CAAC,CAAC;EAC1BI,EAAE,EAAE,CAAC;IAAEqB,IAAI,EAAEzB;EAAM,CAAC,CAAC;EACrBK,QAAQ,EAAE,CAAC;IAAEoB,IAAI,EAAEzB;EAAM,CAAC,CAAC;EAC3BM,SAAS,EAAE,CAAC;IAAEmB,IAAI,EAAEzB;EAAM,CAAC,CAAC;EAC5BO,gBAAgB,EAAE,CAAC;IAAEkB,IAAI,EAAEzB;EAAM,CAAC;AACtC,CAAC,EAAAE,qBAAA,CACJ;AACDD,oBAAoB,GAAGL,UAAU,CAAC,CAC9BG,SAAS,CAAC;EACN2B,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE9B,oBAAoB;EAC9B+B,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC/B,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEG,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}