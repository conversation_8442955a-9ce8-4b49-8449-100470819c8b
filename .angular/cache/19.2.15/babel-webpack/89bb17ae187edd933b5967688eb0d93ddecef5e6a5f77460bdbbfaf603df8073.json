{"ast": null, "code": "import { InjectionToken } from '@angular/core';\nimport { getDefaultWidgetsList } from './registry/default-list';\nexport const WidgetAddTypes = {\n  EXPAND: 'EXPAND',\n  REPLACE: 'REPLACE'\n};\nexport const GRID_CONFIG = new InjectionToken('SWUI_GRID_CONFIG');\nexport function getValidGridConfig(config) {\n  let configWidgets;\n  let widgetAddType = WidgetAddTypes.EXPAND;\n  let widgets = getDefaultWidgetsList();\n  if (config) {\n    if (config.widgets) {\n      configWidgets = config.widgets;\n    }\n    if (config.widgetAddType) {\n      widgetAddType = config.widgetAddType;\n    }\n  }\n  if (configWidgets) {\n    if (widgetAddType === WidgetAddTypes.REPLACE) {\n      widgets = configWidgets;\n    }\n    if (widgetAddType === WidgetAddTypes.EXPAND) {\n      // needs to perform override if it's required\n      const customTypes = configWidgets.map(item => item.type);\n      widgets = widgets.filter(item => customTypes.indexOf(item.type) === -1);\n      widgets = [...widgets, ...configWidgets];\n    }\n  }\n  return {\n    widgets,\n    widgetAddType\n  };\n}", "map": {"version": 3, "names": ["InjectionToken", "getDefaultWidgetsList", "WidgetAddTypes", "EXPAND", "REPLACE", "GRID_CONFIG", "getValidGridConfig", "config", "configWidgets", "widgetAddType", "widgets", "customTypes", "map", "item", "type", "filter", "indexOf"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid.config.ts"], "sourcesContent": ["import { InjectionToken } from '@angular/core';\nimport { getDefaultWidgetsList } from './registry/default-list';\nimport { WidgetListItem } from './registry/registry';\n\nexport type WidgetAddType = 'EXPAND' | 'REPLACE';\nexport const WidgetAddTypes: { EXPAND: WidgetAddType, REPLACE: WidgetAddType, } = {\n  EXPAND: 'EXPAND',\n  REPLACE: 'REPLACE'\n};\n\nexport const GRID_CONFIG = new InjectionToken<SwuiGridConfig>('SWUI_GRID_CONFIG');\n\nexport interface SwuiGridConfig {\n  widgets?: WidgetListItem[];\n  widgetAddType?: WidgetAddType;\n}\nexport interface SwuiValidGridConfig extends SwuiGridConfig {\n  widgets: WidgetListItem[];\n  widgetAddType: WidgetAddType;\n}\n\nexport function getValidGridConfig( config?: SwuiGridConfig ): SwuiValidGridConfig {\n  let configWidgets;\n  let widgetAddType = WidgetAddTypes.EXPAND;\n  let widgets = getDefaultWidgetsList();\n\n  if (config) {\n    if (config.widgets) {\n      configWidgets = config.widgets;\n    }\n    if (config.widgetAddType) {\n      widgetAddType = config.widgetAddType;\n    }\n  }\n\n  if (configWidgets) {\n    if (widgetAddType === WidgetAddTypes.REPLACE) {\n      widgets = configWidgets;\n    }\n    if (widgetAddType === WidgetAddTypes.EXPAND) {\n      // needs to perform override if it's required\n      const customTypes = configWidgets.map((item) => item.type);\n      widgets = widgets.filter((item) => customTypes.indexOf(item.type) === -1);\n\n      widgets = [...widgets, ...configWidgets];\n    }\n  }\n\n  return { widgets, widgetAddType };\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,eAAe;AAC9C,SAASC,qBAAqB,QAAQ,yBAAyB;AAI/D,OAAO,MAAMC,cAAc,GAAuD;EAChFC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE;CACV;AAED,OAAO,MAAMC,WAAW,GAAG,IAAIL,cAAc,CAAiB,kBAAkB,CAAC;AAWjF,OAAM,SAAUM,kBAAkBA,CAAEC,MAAuB;EACzD,IAAIC,aAAa;EACjB,IAAIC,aAAa,GAAGP,cAAc,CAACC,MAAM;EACzC,IAAIO,OAAO,GAAGT,qBAAqB,EAAE;EAErC,IAAIM,MAAM,EAAE;IACV,IAAIA,MAAM,CAACG,OAAO,EAAE;MAClBF,aAAa,GAAGD,MAAM,CAACG,OAAO;IAChC;IACA,IAAIH,MAAM,CAACE,aAAa,EAAE;MACxBA,aAAa,GAAGF,MAAM,CAACE,aAAa;IACtC;EACF;EAEA,IAAID,aAAa,EAAE;IACjB,IAAIC,aAAa,KAAKP,cAAc,CAACE,OAAO,EAAE;MAC5CM,OAAO,GAAGF,aAAa;IACzB;IACA,IAAIC,aAAa,KAAKP,cAAc,CAACC,MAAM,EAAE;MAC3C;MACA,MAAMQ,WAAW,GAAGH,aAAa,CAACI,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,CAAC;MAC1DJ,OAAO,GAAGA,OAAO,CAACK,MAAM,CAAEF,IAAI,IAAKF,WAAW,CAACK,OAAO,CAACH,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;MAEzEJ,OAAO,GAAG,CAAC,GAAGA,OAAO,EAAE,GAAGF,aAAa,CAAC;IAC1C;EACF;EAEA,OAAO;IAAEE,OAAO;IAAED;EAAa,CAAE;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}