{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { InjectionToken } from '@angular/core';\nimport { UntypedFormArray, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nexport const SWUI_DYNAMIC_FORM_CONFIG = new InjectionToken('SWUI_DYNAMIC_FORM_CONFIG');\nexport const SWUI_DYNAMIC_FORM_WIDGET_CONFIG = new InjectionToken('SWUI_DYNAMIC_FORM_WIDGET_CONFIG');\nexport class ControlInputItem {\n  constructor(option) {\n    this.option = option;\n    this.type = 'input';\n    this.control = createFormControl(option, option.value);\n  }\n}\nexport class ControlSequenceItem {\n  constructor(option) {\n    this.option = option;\n    this.type = 'sequence';\n    this.control = new UntypedFormArray([]);\n    this.items = [];\n    (option.value || []).forEach(value => {\n      this.add(value);\n    });\n  }\n  add(values) {\n    const newItems = Object.entries(this.option.inputs || {}).map(([key, input]) => createControlItem(_objectSpread(_objectSpread({}, input), {}, {\n      key,\n      value: values ? values[key] : undefined\n    })));\n    if (newItems.length) {\n      this.items.push(newItems);\n      this.control.push(new UntypedFormGroup(newItems.reduce((result, item) => _objectSpread(_objectSpread({}, result), {}, {\n        [item.option.key]: item.control\n      }), {})));\n    }\n  }\n  remove(index) {\n    this.items.splice(index, 1);\n    this.control.removeAt(index);\n  }\n}\nexport class ControlCollectionItem {\n  constructor(option) {\n    this.option = option;\n    this.type = 'collection';\n    this.items = Object.entries(option.inputs || {}).map(([key, input]) => createControlItem(_objectSpread(_objectSpread({}, input), {}, {\n      key,\n      value: option.value ? option.value[key] : undefined\n    })));\n    this.control = new UntypedFormGroup(this.items.reduce((result, item) => _objectSpread(_objectSpread({}, result), {}, {\n      [item.option.key]: item.control\n    }), {}));\n  }\n}\nexport function createControlItem(option) {\n  if (option.type === 'sequence') {\n    return new ControlSequenceItem(option);\n  } else if (option.type === 'collection') {\n    return new ControlCollectionItem(option);\n  } else {\n    return new ControlInputItem(option);\n  }\n}\nexport function createFormControl(option, value) {\n  const {\n    validation,\n    required,\n    type,\n    defaultValue\n  } = option;\n  const formValidators = validation === null || validation === void 0 ? void 0 : validation.validators;\n  const validators = formValidators ? Array.isArray(formValidators) ? formValidators : [formValidators] : [];\n  if (required) {\n    validators.push(Validators.required);\n  }\n  if (type === 'base64image') {\n    validators.push(fileFormatValidator);\n  }\n  return new UntypedFormControl(value === null || value === undefined ? defaultValue : value, {\n    updateOn: validation === null || validation === void 0 ? void 0 : validation.updateOn,\n    validators,\n    asyncValidators: validation === null || validation === void 0 ? void 0 : validation.asyncValidators\n  });\n}\nfunction fileFormatValidator(control) {\n  const value = control.value;\n  if (!value || !(value instanceof Error)) {\n    return null;\n  }\n  return {\n    fileFormatNotSupported: true\n  };\n}", "map": {"version": 3, "names": ["InjectionToken", "UntypedFormArray", "UntypedFormControl", "UntypedFormGroup", "Validators", "SWUI_DYNAMIC_FORM_CONFIG", "SWUI_DYNAMIC_FORM_WIDGET_CONFIG", "ControlInputItem", "constructor", "option", "type", "control", "createFormControl", "value", "ControlSequenceItem", "items", "for<PERSON>ach", "add", "values", "newItems", "Object", "entries", "inputs", "map", "key", "input", "createControlItem", "_objectSpread", "undefined", "length", "push", "reduce", "result", "item", "remove", "index", "splice", "removeAt", "ControlCollectionItem", "validation", "required", "defaultValue", "formValidators", "validators", "Array", "isArray", "fileFormatValidator", "updateOn", "asyncValidators", "Error", "fileFormatNotSupported"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/dynamic-form.model.ts"], "sourcesContent": ["import { InjectionToken } from '@angular/core';\nimport { UntypedForm<PERSON>rray, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nexport const SWUI_DYNAMIC_FORM_CONFIG = new InjectionToken('SWUI_DYNAMIC_FORM_CONFIG');\nexport const SWUI_DYNAMIC_FORM_WIDGET_CONFIG = new InjectionToken('SWUI_DYNAMIC_FORM_WIDGET_CONFIG');\nexport class ControlInputItem {\n    constructor(option) {\n        this.option = option;\n        this.type = 'input';\n        this.control = createFormControl(option, option.value);\n    }\n}\nexport class ControlSequenceItem {\n    constructor(option) {\n        this.option = option;\n        this.type = 'sequence';\n        this.control = new UntypedFormArray([]);\n        this.items = [];\n        (option.value || []).forEach(value => {\n            this.add(value);\n        });\n    }\n    add(values) {\n        const newItems = Object.entries(this.option.inputs || {}).map(([key, input]) => createControlItem({\n            ...input,\n            key,\n            value: values ? values[key] : undefined\n        }));\n        if (newItems.length) {\n            this.items.push(newItems);\n            this.control.push(new UntypedFormGroup(newItems.reduce((result, item) => ({\n                ...result,\n                [item.option.key]: item.control\n            }), {})));\n        }\n    }\n    remove(index) {\n        this.items.splice(index, 1);\n        this.control.removeAt(index);\n    }\n}\nexport class ControlCollectionItem {\n    constructor(option) {\n        this.option = option;\n        this.type = 'collection';\n        this.items = Object.entries(option.inputs || {}).map(([key, input]) => createControlItem({\n            ...input,\n            key,\n            value: option.value ? option.value[key] : undefined\n        }));\n        this.control = new UntypedFormGroup(this.items.reduce((result, item) => ({\n            ...result,\n            [item.option.key]: item.control\n        }), {}));\n    }\n}\nexport function createControlItem(option) {\n    if (option.type === 'sequence') {\n        return new ControlSequenceItem(option);\n    }\n    else if (option.type === 'collection') {\n        return new ControlCollectionItem(option);\n    }\n    else {\n        return new ControlInputItem(option);\n    }\n}\nexport function createFormControl(option, value) {\n    const { validation, required, type, defaultValue } = option;\n    const formValidators = validation?.validators;\n    const validators = formValidators ? Array.isArray(formValidators) ? formValidators : [formValidators] : [];\n    if (required) {\n        validators.push(Validators.required);\n    }\n    if (type === 'base64image') {\n        validators.push(fileFormatValidator);\n    }\n    return new UntypedFormControl(value === null || value === undefined ? defaultValue : value, {\n        updateOn: validation?.updateOn,\n        validators,\n        asyncValidators: validation?.asyncValidators\n    });\n}\nfunction fileFormatValidator(control) {\n    const value = control.value;\n    if (!value || !(value instanceof Error)) {\n        return null;\n    }\n    return { fileFormatNotSupported: true };\n}\n"], "mappings": ";AAAA,SAASA,cAAc,QAAQ,eAAe;AAC9C,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AACnG,OAAO,MAAMC,wBAAwB,GAAG,IAAIL,cAAc,CAAC,0BAA0B,CAAC;AACtF,OAAO,MAAMM,+BAA+B,GAAG,IAAIN,cAAc,CAAC,iCAAiC,CAAC;AACpG,OAAO,MAAMO,gBAAgB,CAAC;EAC1BC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAG,OAAO;IACnB,IAAI,CAACC,OAAO,GAAGC,iBAAiB,CAACH,MAAM,EAAEA,MAAM,CAACI,KAAK,CAAC;EAC1D;AACJ;AACA,OAAO,MAAMC,mBAAmB,CAAC;EAC7BN,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB,IAAI,CAACC,OAAO,GAAG,IAAIV,gBAAgB,CAAC,EAAE,CAAC;IACvC,IAAI,CAACc,KAAK,GAAG,EAAE;IACf,CAACN,MAAM,CAACI,KAAK,IAAI,EAAE,EAAEG,OAAO,CAACH,KAAK,IAAI;MAClC,IAAI,CAACI,GAAG,CAACJ,KAAK,CAAC;IACnB,CAAC,CAAC;EACN;EACAI,GAAGA,CAACC,MAAM,EAAE;IACR,MAAMC,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACZ,MAAM,CAACa,MAAM,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAKC,iBAAiB,CAAAC,aAAA,CAAAA,aAAA,KAC1FF,KAAK;MACRD,GAAG;MACHX,KAAK,EAAEK,MAAM,GAAGA,MAAM,CAACM,GAAG,CAAC,GAAGI;IAAS,EAC1C,CAAC,CAAC;IACH,IAAIT,QAAQ,CAACU,MAAM,EAAE;MACjB,IAAI,CAACd,KAAK,CAACe,IAAI,CAACX,QAAQ,CAAC;MACzB,IAAI,CAACR,OAAO,CAACmB,IAAI,CAAC,IAAI3B,gBAAgB,CAACgB,QAAQ,CAACY,MAAM,CAAC,CAACC,MAAM,EAAEC,IAAI,KAAAN,aAAA,CAAAA,aAAA,KAC7DK,MAAM;QACT,CAACC,IAAI,CAACxB,MAAM,CAACe,GAAG,GAAGS,IAAI,CAACtB;MAAO,EACjC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb;EACJ;EACAuB,MAAMA,CAACC,KAAK,EAAE;IACV,IAAI,CAACpB,KAAK,CAACqB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAC3B,IAAI,CAACxB,OAAO,CAAC0B,QAAQ,CAACF,KAAK,CAAC;EAChC;AACJ;AACA,OAAO,MAAMG,qBAAqB,CAAC;EAC/B9B,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAG,YAAY;IACxB,IAAI,CAACK,KAAK,GAAGK,MAAM,CAACC,OAAO,CAACZ,MAAM,CAACa,MAAM,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAKC,iBAAiB,CAAAC,aAAA,CAAAA,aAAA,KACjFF,KAAK;MACRD,GAAG;MACHX,KAAK,EAAEJ,MAAM,CAACI,KAAK,GAAGJ,MAAM,CAACI,KAAK,CAACW,GAAG,CAAC,GAAGI;IAAS,EACtD,CAAC,CAAC;IACH,IAAI,CAACjB,OAAO,GAAG,IAAIR,gBAAgB,CAAC,IAAI,CAACY,KAAK,CAACgB,MAAM,CAAC,CAACC,MAAM,EAAEC,IAAI,KAAAN,aAAA,CAAAA,aAAA,KAC5DK,MAAM;MACT,CAACC,IAAI,CAACxB,MAAM,CAACe,GAAG,GAAGS,IAAI,CAACtB;IAAO,EACjC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ;AACJ;AACA,OAAO,SAASe,iBAAiBA,CAACjB,MAAM,EAAE;EACtC,IAAIA,MAAM,CAACC,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAO,IAAII,mBAAmB,CAACL,MAAM,CAAC;EAC1C,CAAC,MACI,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,EAAE;IACnC,OAAO,IAAI4B,qBAAqB,CAAC7B,MAAM,CAAC;EAC5C,CAAC,MACI;IACD,OAAO,IAAIF,gBAAgB,CAACE,MAAM,CAAC;EACvC;AACJ;AACA,OAAO,SAASG,iBAAiBA,CAACH,MAAM,EAAEI,KAAK,EAAE;EAC7C,MAAM;IAAE0B,UAAU;IAAEC,QAAQ;IAAE9B,IAAI;IAAE+B;EAAa,CAAC,GAAGhC,MAAM;EAC3D,MAAMiC,cAAc,GAAGH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEI,UAAU;EAC7C,MAAMA,UAAU,GAAGD,cAAc,GAAGE,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,GAAGA,cAAc,GAAG,CAACA,cAAc,CAAC,GAAG,EAAE;EAC1G,IAAIF,QAAQ,EAAE;IACVG,UAAU,CAACb,IAAI,CAAC1B,UAAU,CAACoC,QAAQ,CAAC;EACxC;EACA,IAAI9B,IAAI,KAAK,aAAa,EAAE;IACxBiC,UAAU,CAACb,IAAI,CAACgB,mBAAmB,CAAC;EACxC;EACA,OAAO,IAAI5C,kBAAkB,CAACW,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKe,SAAS,GAAGa,YAAY,GAAG5B,KAAK,EAAE;IACxFkC,QAAQ,EAAER,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEQ,QAAQ;IAC9BJ,UAAU;IACVK,eAAe,EAAET,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES;EACjC,CAAC,CAAC;AACN;AACA,SAASF,mBAAmBA,CAACnC,OAAO,EAAE;EAClC,MAAME,KAAK,GAAGF,OAAO,CAACE,KAAK;EAC3B,IAAI,CAACA,KAAK,IAAI,EAAEA,KAAK,YAAYoC,KAAK,CAAC,EAAE;IACrC,OAAO,IAAI;EACf;EACA,OAAO;IAAEC,sBAAsB,EAAE;EAAK,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}