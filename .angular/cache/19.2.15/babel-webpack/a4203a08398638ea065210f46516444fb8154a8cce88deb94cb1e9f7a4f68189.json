{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _StructuralStylesLoader2;\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load structural styles for focus indicators.\n * @docs-private\n */\nclass _StructuralStylesLoader {}\n_StructuralStylesLoader2 = _StructuralStylesLoader;\n_defineProperty(_StructuralStylesLoader, \"\\u0275fac\", function _StructuralStylesLoader2_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _StructuralStylesLoader2)();\n});\n_defineProperty(_StructuralStylesLoader, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _StructuralStylesLoader2,\n  selectors: [[\"structural-styles\"]],\n  decls: 0,\n  vars: 0,\n  template: function _StructuralStylesLoader2_Template(rf, ctx) {},\n  styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_StructuralStylesLoader, [{\n    type: Component,\n    args: [{\n      selector: 'structural-styles',\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { _StructuralStylesLoader as _ };\n//# sourceMappingURL=structural-styles-BQUT6wsL.mjs.map", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "_StructuralStylesLoader", "_StructuralStylesLoader2", "_defineProperty", "_StructuralStylesLoader2_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "decls", "vars", "template", "_StructuralStylesLoader2_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "_"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/structural-styles-BQUT6wsL.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load structural styles for focus indicators.\n * @docs-private\n */\nclass _StructuralStylesLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _StructuralStylesLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: _StructuralStylesLoader, isStandalone: true, selector: \"structural-styles\", ngImport: i0, template: '', isInline: true, styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _StructuralStylesLoader, decorators: [{\n            type: Component,\n            args: [{ selector: 'structural-styles', encapsulation: ViewEncapsulation.None, template: '', changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"] }]\n        }] });\n\nexport { _StructuralStylesLoader as _ };\n//# sourceMappingURL=structural-styles-BQUT6wsL.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,QAAQ,eAAe;;AAErF;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;AAG7BC,wBAAA,GAHKD,uBAAuB;AAAAE,eAAA,CAAvBF,uBAAuB,wBAAAG,iCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAC0EJ,wBAAuB;AAAA;AAAAE,eAAA,CADxHF,uBAAuB,8BAIoDJ,EAAE,CAAAS,iBAAA;EAAAC,IAAA,EAFQN,wBAAuB;EAAAO,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;EAAAC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAElH;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFrB,EAAE,CAAAsB,iBAAA,CAAQlB,uBAAuB,EAAc,CAAC;IACrHM,IAAI,EAAET,SAAS;IACfsB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEL,aAAa,EAAEjB,iBAAiB,CAACuB,IAAI;MAAEX,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAEjB,uBAAuB,CAACuB,MAAM;MAAER,MAAM,EAAE,CAAC,gkBAAgkB;IAAE,CAAC;EAC9tB,CAAC,CAAC;AAAA;AAEV,SAASd,uBAAuB,IAAIuB,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}