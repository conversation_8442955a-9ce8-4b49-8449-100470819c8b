{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { LanguageSelectorComponent } from './language-selector.component';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\ndescribe('LanguageSelectorComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      schemas: [NO_ERRORS_SCHEMA],\n      declarations: [LanguageSelectorComponent],\n      imports: [TranslateModule.forRoot(), MatRippleModule, MatMenuModule, MatIconModule],\n      providers: [{\n        provide: SWUI_HUB_MESSAGE_CONFIG,\n        useValue: {}\n      }]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(LanguageSelectorComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "LanguageSelectorComponent", "NO_ERRORS_SCHEMA", "TranslateModule", "SWUI_HUB_MESSAGE_CONFIG", "MatMenuModule", "MatIconModule", "MatRippleModule", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "schemas", "declarations", "imports", "forRoot", "providers", "provide", "useValue", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/language-selector/language-selector.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\n\nimport { LanguageSelectorComponent } from './language-selector.component';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\n\n\ndescribe('LanguageSelectorComponent', () => {\n  let component: LanguageSelectorComponent;\n  let fixture: ComponentFixture<LanguageSelectorComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      schemas: [NO_ERRORS_SCHEMA],\n      declarations: [LanguageSelectorComponent],\n      imports: [\n        TranslateModule.forRoot(),\n        MatRippleModule,\n        MatMenuModule,\n        MatIconModule,\n      ],\n      providers: [\n        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: {} }\n      ]\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(LanguageSelectorComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AAGxDC,QAAQ,CAAC,2BAA2B,EAAE,MAAK;EACzC,IAAIC,SAAoC;EACxC,IAAIC,OAAoD;EAExDC,UAAU,CAACX,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACa,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACX,gBAAgB,CAAC;MAC3BY,YAAY,EAAE,CAACb,yBAAyB,CAAC;MACzCc,OAAO,EAAE,CACPZ,eAAe,CAACa,OAAO,EAAE,EACzBT,eAAe,EACfF,aAAa,EACbC,aAAa,CACd;MACDW,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEd,uBAAuB;QAAEe,QAAQ,EAAE;MAAE,CAAE;KAErD,CAAC,CACCC,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHT,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGX,OAAO,CAACsB,eAAe,CAACpB,yBAAyB,CAAC;IAC5DQ,SAAS,GAAGC,OAAO,CAACY,iBAAiB;IACrCZ,OAAO,CAACa,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAChB,SAAS,CAAC,CAACiB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}