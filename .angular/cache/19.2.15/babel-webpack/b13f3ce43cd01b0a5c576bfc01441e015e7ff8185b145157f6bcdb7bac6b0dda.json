{"ast": null, "code": "var _SelectedItemsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./selected-items.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../games-select-manager.scss?ngResource\";\nimport { moveItemInArray } from '@angular/cdk/drag-drop';\nimport { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { gameSelectItemTypes } from '../game-select-item/game-select-item-types';\nimport { getLabelClass } from '../game-select-item/game-select-item.model';\nimport { GamesSelectManagerService } from '../games-select-manager.service';\nlet SelectedItemsComponent = (_SelectedItemsComponent = class SelectedItemsComponent {\n  constructor(service) {\n    this.service = service;\n    this.disabled = false;\n    this.emptyGamesText = 'COMPONENTS.GAMES_SELECT_MANAGER.emptySelectedGames';\n    this.height = '500px';\n    this.dropListDropped = new EventEmitter();\n    this.items = [];\n    this.allChecked = false;\n    this.itemTypes = gameSelectItemTypes;\n    this.service.selectedItems$.subscribe(games => {\n      this.items = games;\n      this.refreshAllChecked();\n      this.calculateCheckedItems();\n    });\n  }\n  ngOnInit() {}\n  checkAllChanged() {\n    if (this.disabled) {\n      return;\n    }\n    this.items.forEach(item => item.checked = this.allChecked);\n    this.calculateCheckedItems();\n  }\n  getLabelClass(label) {\n    return [getLabelClass(label)];\n  }\n  itemSelectionChanged() {\n    this.refreshAllChecked();\n    this.calculateCheckedItems();\n  }\n  extraColumnValueChanged() {\n    this.service.refreshSelected();\n  }\n  drop(event) {\n    moveItemInArray(this.items, event.previousIndex, event.currentIndex);\n    this.dropListDropped.emit(this.items);\n  }\n  refreshAllChecked() {\n    this.allChecked = this.items && this.items.length > 0 && this.items.every(item => item.checked);\n  }\n  calculateCheckedItems() {\n    const checked = this.items.filter(item => item.checked);\n    const labelTypes = [gameSelectItemTypes.PROVIDER, gameSelectItemTypes.LABEL];\n    this.service.calculateCheckedSelectedGames(checked.filter(i => i.type === gameSelectItemTypes.GAME));\n    this.service.calculateCheckedSelectedLabels(checked.filter(i => labelTypes.indexOf(i.type) > -1));\n    this.service.calculateCheckedSelectedIntersections(checked.filter(i => i.type === gameSelectItemTypes.INTERSECTION));\n  }\n}, _SelectedItemsComponent.ctorParameters = () => [{\n  type: GamesSelectManagerService\n}], _SelectedItemsComponent.propDecorators = {\n  extraColumn: [{\n    type: Input\n  }],\n  disabled: [{\n    type: Input\n  }],\n  emptyGamesText: [{\n    type: Input\n  }],\n  height: [{\n    type: Input\n  }],\n  dropListDropped: [{\n    type: Output\n  }],\n  table: [{\n    type: ViewChild,\n    args: ['scrollTable', {\n      static: true\n    }]\n  }]\n}, _SelectedItemsComponent);\nSelectedItemsComponent = __decorate([Component({\n  // tslint:disable-next-line:component-selector\n  selector: 'sw-selected-items',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SelectedItemsComponent);\nexport { SelectedItemsComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "moveItemInArray", "Component", "EventEmitter", "Input", "Output", "ViewChild", "gameSelectItemTypes", "getLabelClass", "GamesSelectManagerService", "SelectedItemsComponent", "_SelectedItemsComponent", "constructor", "service", "disabled", "emptyGamesText", "height", "dropListDropped", "items", "allChecked", "itemTypes", "selectedItems$", "subscribe", "games", "refreshAllChecked", "calculateCheckedItems", "ngOnInit", "checkAllChanged", "for<PERSON>ach", "item", "checked", "label", "itemSelectionChanged", "extraColumnValueChanged", "refreshSelected", "drop", "event", "previousIndex", "currentIndex", "emit", "length", "every", "filter", "labelTypes", "PROVIDER", "LABEL", "calculateCheckedSelectedGames", "i", "type", "GAME", "calculateCheckedSelectedLabels", "indexOf", "calculateCheckedSelectedIntersections", "INTERSECTION", "ctorParameters", "propDecorators", "extraColumn", "table", "args", "static", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/selected-items/selected-items.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./selected-items.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../games-select-manager.scss?ngResource\";\nimport { moveItemInArray } from '@angular/cdk/drag-drop';\nimport { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { gameSelectItemTypes } from '../game-select-item/game-select-item-types';\nimport { getLabelClass } from '../game-select-item/game-select-item.model';\nimport { GamesSelectManagerService } from '../games-select-manager.service';\nlet SelectedItemsComponent = class SelectedItemsComponent {\n    constructor(service) {\n        this.service = service;\n        this.disabled = false;\n        this.emptyGamesText = 'COMPONENTS.GAMES_SELECT_MANAGER.emptySelectedGames';\n        this.height = '500px';\n        this.dropListDropped = new EventEmitter();\n        this.items = [];\n        this.allChecked = false;\n        this.itemTypes = gameSelectItemTypes;\n        this.service.selectedItems$.subscribe((games) => {\n            this.items = games;\n            this.refreshAllChecked();\n            this.calculateCheckedItems();\n        });\n    }\n    ngOnInit() {\n    }\n    checkAllChanged() {\n        if (this.disabled) {\n            return;\n        }\n        this.items.forEach(item => item.checked = this.allChecked);\n        this.calculateCheckedItems();\n    }\n    getLabelClass(label) {\n        return [\n            getLabelClass(label)\n        ];\n    }\n    itemSelectionChanged() {\n        this.refreshAllChecked();\n        this.calculateCheckedItems();\n    }\n    extraColumnValueChanged() {\n        this.service.refreshSelected();\n    }\n    drop(event) {\n        moveItemInArray(this.items, event.previousIndex, event.currentIndex);\n        this.dropListDropped.emit(this.items);\n    }\n    refreshAllChecked() {\n        this.allChecked = this.items && this.items.length > 0 && this.items.every(item => item.checked);\n    }\n    calculateCheckedItems() {\n        const checked = this.items.filter(item => item.checked);\n        const labelTypes = [\n            gameSelectItemTypes.PROVIDER,\n            gameSelectItemTypes.LABEL\n        ];\n        this.service.calculateCheckedSelectedGames(checked.filter(i => i.type === gameSelectItemTypes.GAME));\n        this.service.calculateCheckedSelectedLabels(checked.filter(i => labelTypes.indexOf(i.type) > -1));\n        this.service.calculateCheckedSelectedIntersections(checked.filter(i => i.type === gameSelectItemTypes.INTERSECTION));\n    }\n    static { this.ctorParameters = () => [\n        { type: GamesSelectManagerService }\n    ]; }\n    static { this.propDecorators = {\n        extraColumn: [{ type: Input }],\n        disabled: [{ type: Input }],\n        emptyGamesText: [{ type: Input }],\n        height: [{ type: Input }],\n        dropListDropped: [{ type: Output }],\n        table: [{ type: ViewChild, args: ['scrollTable', { static: true },] }]\n    }; }\n};\nSelectedItemsComponent = __decorate([\n    Component({\n        // tslint:disable-next-line:component-selector\n        selector: 'sw-selected-items',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SelectedItemsComponent);\nexport { SelectedItemsComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACjF,SAASC,mBAAmB,QAAQ,4CAA4C;AAChF,SAASC,aAAa,QAAQ,4CAA4C;AAC1E,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,IAAIC,sBAAsB,IAAAC,uBAAA,GAAG,MAAMD,sBAAsB,CAAC;EACtDE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,cAAc,GAAG,oDAAoD;IAC1E,IAAI,CAACC,MAAM,GAAG,OAAO;IACrB,IAAI,CAACC,eAAe,GAAG,IAAId,YAAY,CAAC,CAAC;IACzC,IAAI,CAACe,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAGb,mBAAmB;IACpC,IAAI,CAACM,OAAO,CAACQ,cAAc,CAACC,SAAS,CAAEC,KAAK,IAAK;MAC7C,IAAI,CAACL,KAAK,GAAGK,KAAK;MAClB,IAAI,CAACC,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG,CACX;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACb,QAAQ,EAAE;MACf;IACJ;IACA,IAAI,CAACI,KAAK,CAACU,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACX,UAAU,CAAC;IAC1D,IAAI,CAACM,qBAAqB,CAAC,CAAC;EAChC;EACAjB,aAAaA,CAACuB,KAAK,EAAE;IACjB,OAAO,CACHvB,aAAa,CAACuB,KAAK,CAAC,CACvB;EACL;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACR,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EACAQ,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACpB,OAAO,CAACqB,eAAe,CAAC,CAAC;EAClC;EACAC,IAAIA,CAACC,KAAK,EAAE;IACRnC,eAAe,CAAC,IAAI,CAACiB,KAAK,EAAEkB,KAAK,CAACC,aAAa,EAAED,KAAK,CAACE,YAAY,CAAC;IACpE,IAAI,CAACrB,eAAe,CAACsB,IAAI,CAAC,IAAI,CAACrB,KAAK,CAAC;EACzC;EACAM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACL,UAAU,GAAG,IAAI,CAACD,KAAK,IAAI,IAAI,CAACA,KAAK,CAACsB,MAAM,GAAG,CAAC,IAAI,IAAI,CAACtB,KAAK,CAACuB,KAAK,CAACZ,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC;EACnG;EACAL,qBAAqBA,CAAA,EAAG;IACpB,MAAMK,OAAO,GAAG,IAAI,CAACZ,KAAK,CAACwB,MAAM,CAACb,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC;IACvD,MAAMa,UAAU,GAAG,CACfpC,mBAAmB,CAACqC,QAAQ,EAC5BrC,mBAAmB,CAACsC,KAAK,CAC5B;IACD,IAAI,CAAChC,OAAO,CAACiC,6BAA6B,CAAChB,OAAO,CAACY,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKzC,mBAAmB,CAAC0C,IAAI,CAAC,CAAC;IACpG,IAAI,CAACpC,OAAO,CAACqC,8BAA8B,CAACpB,OAAO,CAACY,MAAM,CAACK,CAAC,IAAIJ,UAAU,CAACQ,OAAO,CAACJ,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjG,IAAI,CAACnC,OAAO,CAACuC,qCAAqC,CAACtB,OAAO,CAACY,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKzC,mBAAmB,CAAC8C,YAAY,CAAC,CAAC;EACxH;AAYJ,CAAC,EAXY1C,uBAAA,CAAK2C,cAAc,GAAG,MAAM,CACjC;EAAEN,IAAI,EAAEvC;AAA0B,CAAC,CACtC,EACQE,uBAAA,CAAK4C,cAAc,GAAG;EAC3BC,WAAW,EAAE,CAAC;IAAER,IAAI,EAAE5C;EAAM,CAAC,CAAC;EAC9BU,QAAQ,EAAE,CAAC;IAAEkC,IAAI,EAAE5C;EAAM,CAAC,CAAC;EAC3BW,cAAc,EAAE,CAAC;IAAEiC,IAAI,EAAE5C;EAAM,CAAC,CAAC;EACjCY,MAAM,EAAE,CAAC;IAAEgC,IAAI,EAAE5C;EAAM,CAAC,CAAC;EACzBa,eAAe,EAAE,CAAC;IAAE+B,IAAI,EAAE3C;EAAO,CAAC,CAAC;EACnCoD,KAAK,EAAE,CAAC;IAAET,IAAI,EAAE1C,SAAS;IAAEoD,IAAI,EAAE,CAAC,aAAa,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC;AACzE,CAAC,EAAAhD,uBAAA,CACJ;AACDD,sBAAsB,GAAGZ,UAAU,CAAC,CAChCI,SAAS,CAAC;EACN;EACA0D,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAE9D,oBAAoB;EAC9B+D,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC/D,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEU,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}