{"ast": null, "code": "var _SwuiInputSequenceMapComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-input-sequence-map.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-input-sequence-map.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, forwardRef, Input } from '@angular/core';\nimport { UntypedFormArray, UntypedFormControl, UntypedFormGroup, NG_VALUE_ACCESSOR, Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nlet SwuiInputSequenceMapComponent = (_SwuiInputSequenceMapComponent = class SwuiInputSequenceMapComponent {\n  constructor() {\n    this.readonly = false;\n    this.submitted = false;\n    this.formArray = new UntypedFormArray([]);\n    this.destroyed$ = new Subject();\n    this.onChange = _value => {};\n    this.onTouched = () => {};\n  }\n  ngOnInit() {\n    this.writeValue({});\n    this.formArray.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n      const data = {};\n      this.formArray.controls.forEach(control => {\n        var _group$get, _group$get2;\n        const group = control;\n        const source = (_group$get = group.get('source')) === null || _group$get === void 0 || (_group$get = _group$get.value) === null || _group$get === void 0 ? void 0 : _group$get.trim();\n        const target = (_group$get2 = group.get('target')) === null || _group$get2 === void 0 || (_group$get2 = _group$get2.value) === null || _group$get2 === void 0 ? void 0 : _group$get2.trim();\n        if (source && target) {\n          data[source] = target;\n        }\n      });\n      this.onChange(data);\n      this.onTouched();\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  // ControlValueAccessor implementation\n  writeValue(value) {\n    while (this.formArray.length !== 0) {\n      this.formArray.removeAt(0);\n    }\n    Object.entries(value || {}).forEach(([source, target]) => {\n      this.addPair(source, target);\n    });\n    if (this.formArray.length === 0) {\n      this.addPair();\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.readonly = isDisabled;\n    if (isDisabled) {\n      this.formArray.disable();\n    } else {\n      this.formArray.enable();\n    }\n  }\n  addPair(source = '', target = '') {\n    this.formArray.push(new UntypedFormGroup({\n      source: new UntypedFormControl(source, [Validators.required]),\n      target: new UntypedFormControl(target, [Validators.required])\n    }));\n  }\n  removePair(index) {\n    if (this.formArray.length > 1) {\n      this.formArray.removeAt(index);\n    }\n  }\n  getSourceControl(index) {\n    var _this$formArray$at;\n    return (_this$formArray$at = this.formArray.at(index)) === null || _this$formArray$at === void 0 ? void 0 : _this$formArray$at.get('source');\n  }\n  getTargetControl(index) {\n    var _this$formArray$at2;\n    return (_this$formArray$at2 = this.formArray.at(index)) === null || _this$formArray$at2 === void 0 ? void 0 : _this$formArray$at2.get('target');\n  }\n  hasSourceError(index) {\n    const control = this.getSourceControl(index);\n    return Boolean((control === null || control === void 0 ? void 0 : control.invalid) && ((control === null || control === void 0 ? void 0 : control.dirty) || (control === null || control === void 0 ? void 0 : control.touched) || this.submitted));\n  }\n  hasTargetError(index) {\n    const control = this.getSourceControl(index);\n    return Boolean((control === null || control === void 0 ? void 0 : control.invalid) && ((control === null || control === void 0 ? void 0 : control.dirty) || (control === null || control === void 0 ? void 0 : control.touched) || this.submitted));\n  }\n  getSourceErrorMessage(index) {\n    const control = this.getSourceControl(index);\n    if (control !== null && control !== void 0 && control.hasError('required')) {\n      return 'required';\n    }\n    return '';\n  }\n  getTargetErrorMessage(index) {\n    const control = this.getTargetControl(index);\n    if (control !== null && control !== void 0 && control.hasError('required')) {\n      return 'required';\n    }\n    return '';\n  }\n}, _SwuiInputSequenceMapComponent.propDecorators = {\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  options: [{\n    type: Input\n  }]\n}, _SwuiInputSequenceMapComponent);\nSwuiInputSequenceMapComponent = __decorate([Component({\n  selector: 'lib-swui-input-sequence-map',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SwuiInputSequenceMapComponent),\n    multi: true\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiInputSequenceMapComponent);\nexport { SwuiInputSequenceMapComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectionStrategy", "Component", "forwardRef", "Input", "UntypedFormArray", "UntypedFormControl", "UntypedFormGroup", "NG_VALUE_ACCESSOR", "Validators", "Subject", "takeUntil", "SwuiInputSequenceMapComponent", "_SwuiInputSequenceMapComponent", "constructor", "readonly", "submitted", "formArray", "destroyed$", "onChange", "_value", "onTouched", "ngOnInit", "writeValue", "valueChanges", "pipe", "subscribe", "data", "controls", "for<PERSON>ach", "control", "_group$get", "_group$get2", "group", "source", "get", "value", "trim", "target", "ngOnDestroy", "next", "undefined", "complete", "length", "removeAt", "Object", "entries", "addPair", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "disable", "enable", "push", "required", "removePair", "index", "getSourceControl", "_this$formArray$at", "at", "getTargetControl", "_this$formArray$at2", "hasSourceError", "Boolean", "invalid", "dirty", "touched", "hasTargetError", "getSourceErrorMessage", "<PERSON><PERSON><PERSON><PERSON>", "getTargetErrorMessage", "propDecorators", "id", "type", "options", "selector", "template", "changeDetection", "OnPush", "providers", "provide", "useExisting", "multi", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-input-sequence-map/swui-input-sequence-map.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-input-sequence-map.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-input-sequence-map.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, forwardRef, Input } from '@angular/core';\nimport { UntypedFormArray, UntypedFormControl, UntypedFormGroup, NG_VALUE_ACCESSOR, Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nlet SwuiInputSequenceMapComponent = class SwuiInputSequenceMapComponent {\n    constructor() {\n        this.readonly = false;\n        this.submitted = false;\n        this.formArray = new UntypedFormArray([]);\n        this.destroyed$ = new Subject();\n        this.onChange = (_value) => { };\n        this.onTouched = () => { };\n    }\n    ngOnInit() {\n        this.writeValue({});\n        this.formArray.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n            const data = {};\n            this.formArray.controls.forEach(control => {\n                const group = control;\n                const source = group.get('source')?.value?.trim();\n                const target = group.get('target')?.value?.trim();\n                if (source && target) {\n                    data[source] = target;\n                }\n            });\n            this.onChange(data);\n            this.onTouched();\n        });\n    }\n    ngOnDestroy() {\n        this.destroyed$.next(undefined);\n        this.destroyed$.complete();\n    }\n    // ControlValueAccessor implementation\n    writeValue(value) {\n        while (this.formArray.length !== 0) {\n            this.formArray.removeAt(0);\n        }\n        Object.entries(value || {}).forEach(([source, target]) => {\n            this.addPair(source, target);\n        });\n        if (this.formArray.length === 0) {\n            this.addPair();\n        }\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(isDisabled) {\n        this.readonly = isDisabled;\n        if (isDisabled) {\n            this.formArray.disable();\n        }\n        else {\n            this.formArray.enable();\n        }\n    }\n    addPair(source = '', target = '') {\n        this.formArray.push(new UntypedFormGroup({\n            source: new UntypedFormControl(source, [Validators.required]),\n            target: new UntypedFormControl(target, [Validators.required])\n        }));\n    }\n    removePair(index) {\n        if (this.formArray.length > 1) {\n            this.formArray.removeAt(index);\n        }\n    }\n    getSourceControl(index) {\n        return this.formArray.at(index)?.get('source');\n    }\n    getTargetControl(index) {\n        return this.formArray.at(index)?.get('target');\n    }\n    hasSourceError(index) {\n        const control = this.getSourceControl(index);\n        return Boolean(control?.invalid && (control?.dirty || control?.touched || this.submitted));\n    }\n    hasTargetError(index) {\n        const control = this.getSourceControl(index);\n        return Boolean(control?.invalid && (control?.dirty || control?.touched || this.submitted));\n    }\n    getSourceErrorMessage(index) {\n        const control = this.getSourceControl(index);\n        if (control?.hasError('required')) {\n            return 'required';\n        }\n        return '';\n    }\n    getTargetErrorMessage(index) {\n        const control = this.getTargetControl(index);\n        if (control?.hasError('required')) {\n            return 'required';\n        }\n        return '';\n    }\n    static { this.propDecorators = {\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        options: [{ type: Input }]\n    }; }\n};\nSwuiInputSequenceMapComponent = __decorate([\n    Component({\n        selector: 'lib-swui-input-sequence-map',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        providers: [\n            {\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => SwuiInputSequenceMapComponent),\n                multi: true\n            }\n        ],\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiInputSequenceMapComponent);\nexport { SwuiInputSequenceMapComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,qDAAqD;AACtF,OAAOC,oBAAoB,MAAM,qDAAqD;AACtF,SAASC,uBAAuB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,KAAK,QAAQ,eAAe;AACrF,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,gBAAgB;AACtH,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,IAAIC,6BAA6B,IAAAC,8BAAA,GAAG,MAAMD,6BAA6B,CAAC;EACpEE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,IAAIZ,gBAAgB,CAAC,EAAE,CAAC;IACzC,IAAI,CAACa,UAAU,GAAG,IAAIR,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACS,QAAQ,GAAIC,MAAM,IAAK,CAAE,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAG,MAAM,CAAE,CAAC;EAC9B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;IACnB,IAAI,CAACN,SAAS,CAACO,YAAY,CAACC,IAAI,CAACd,SAAS,CAAC,IAAI,CAACO,UAAU,CAAC,CAAC,CAACQ,SAAS,CAAC,MAAM;MACzE,MAAMC,IAAI,GAAG,CAAC,CAAC;MACf,IAAI,CAACV,SAAS,CAACW,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAI;QAAA,IAAAC,UAAA,EAAAC,WAAA;QACvC,MAAMC,KAAK,GAAGH,OAAO;QACrB,MAAMI,MAAM,IAAAH,UAAA,GAAGE,KAAK,CAACE,GAAG,CAAC,QAAQ,CAAC,cAAAJ,UAAA,gBAAAA,UAAA,GAAnBA,UAAA,CAAqBK,KAAK,cAAAL,UAAA,uBAA1BA,UAAA,CAA4BM,IAAI,CAAC,CAAC;QACjD,MAAMC,MAAM,IAAAN,WAAA,GAAGC,KAAK,CAACE,GAAG,CAAC,QAAQ,CAAC,cAAAH,WAAA,gBAAAA,WAAA,GAAnBA,WAAA,CAAqBI,KAAK,cAAAJ,WAAA,uBAA1BA,WAAA,CAA4BK,IAAI,CAAC,CAAC;QACjD,IAAIH,MAAM,IAAII,MAAM,EAAE;UAClBX,IAAI,CAACO,MAAM,CAAC,GAAGI,MAAM;QACzB;MACJ,CAAC,CAAC;MACF,IAAI,CAACnB,QAAQ,CAACQ,IAAI,CAAC;MACnB,IAAI,CAACN,SAAS,CAAC,CAAC;IACpB,CAAC,CAAC;EACN;EACAkB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACrB,UAAU,CAACsB,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACvB,UAAU,CAACwB,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAnB,UAAUA,CAACa,KAAK,EAAE;IACd,OAAO,IAAI,CAACnB,SAAS,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAAC1B,SAAS,CAAC2B,QAAQ,CAAC,CAAC,CAAC;IAC9B;IACAC,MAAM,CAACC,OAAO,CAACV,KAAK,IAAI,CAAC,CAAC,CAAC,CAACP,OAAO,CAAC,CAAC,CAACK,MAAM,EAAEI,MAAM,CAAC,KAAK;MACtD,IAAI,CAACS,OAAO,CAACb,MAAM,EAAEI,MAAM,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,IAAI,CAACrB,SAAS,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACI,OAAO,CAAC,CAAC;IAClB;EACJ;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAC9B,QAAQ,GAAG8B,EAAE;EACtB;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAAC5B,SAAS,GAAG4B,EAAE;EACvB;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACrC,QAAQ,GAAGqC,UAAU;IAC1B,IAAIA,UAAU,EAAE;MACZ,IAAI,CAACnC,SAAS,CAACoC,OAAO,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,IAAI,CAACpC,SAAS,CAACqC,MAAM,CAAC,CAAC;IAC3B;EACJ;EACAP,OAAOA,CAACb,MAAM,GAAG,EAAE,EAAEI,MAAM,GAAG,EAAE,EAAE;IAC9B,IAAI,CAACrB,SAAS,CAACsC,IAAI,CAAC,IAAIhD,gBAAgB,CAAC;MACrC2B,MAAM,EAAE,IAAI5B,kBAAkB,CAAC4B,MAAM,EAAE,CAACzB,UAAU,CAAC+C,QAAQ,CAAC,CAAC;MAC7DlB,MAAM,EAAE,IAAIhC,kBAAkB,CAACgC,MAAM,EAAE,CAAC7B,UAAU,CAAC+C,QAAQ,CAAC;IAChE,CAAC,CAAC,CAAC;EACP;EACAC,UAAUA,CAACC,KAAK,EAAE;IACd,IAAI,IAAI,CAACzC,SAAS,CAAC0B,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAI,CAAC1B,SAAS,CAAC2B,QAAQ,CAACc,KAAK,CAAC;IAClC;EACJ;EACAC,gBAAgBA,CAACD,KAAK,EAAE;IAAA,IAAAE,kBAAA;IACpB,QAAAA,kBAAA,GAAO,IAAI,CAAC3C,SAAS,CAAC4C,EAAE,CAACH,KAAK,CAAC,cAAAE,kBAAA,uBAAxBA,kBAAA,CAA0BzB,GAAG,CAAC,QAAQ,CAAC;EAClD;EACA2B,gBAAgBA,CAACJ,KAAK,EAAE;IAAA,IAAAK,mBAAA;IACpB,QAAAA,mBAAA,GAAO,IAAI,CAAC9C,SAAS,CAAC4C,EAAE,CAACH,KAAK,CAAC,cAAAK,mBAAA,uBAAxBA,mBAAA,CAA0B5B,GAAG,CAAC,QAAQ,CAAC;EAClD;EACA6B,cAAcA,CAACN,KAAK,EAAE;IAClB,MAAM5B,OAAO,GAAG,IAAI,CAAC6B,gBAAgB,CAACD,KAAK,CAAC;IAC5C,OAAOO,OAAO,CAAC,CAAAnC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoC,OAAO,MAAK,CAAApC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqC,KAAK,MAAIrC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,OAAO,KAAI,IAAI,CAACpD,SAAS,CAAC,CAAC;EAC9F;EACAqD,cAAcA,CAACX,KAAK,EAAE;IAClB,MAAM5B,OAAO,GAAG,IAAI,CAAC6B,gBAAgB,CAACD,KAAK,CAAC;IAC5C,OAAOO,OAAO,CAAC,CAAAnC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoC,OAAO,MAAK,CAAApC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqC,KAAK,MAAIrC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,OAAO,KAAI,IAAI,CAACpD,SAAS,CAAC,CAAC;EAC9F;EACAsD,qBAAqBA,CAACZ,KAAK,EAAE;IACzB,MAAM5B,OAAO,GAAG,IAAI,CAAC6B,gBAAgB,CAACD,KAAK,CAAC;IAC5C,IAAI5B,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEyC,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC/B,OAAO,UAAU;IACrB;IACA,OAAO,EAAE;EACb;EACAC,qBAAqBA,CAACd,KAAK,EAAE;IACzB,MAAM5B,OAAO,GAAG,IAAI,CAACgC,gBAAgB,CAACJ,KAAK,CAAC;IAC5C,IAAI5B,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEyC,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC/B,OAAO,UAAU;IACrB;IACA,OAAO,EAAE;EACb;AAOJ,CAAC,EANY1D,8BAAA,CAAK4D,cAAc,GAAG;EAC3BC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAEvE;EAAM,CAAC,CAAC;EACrBW,QAAQ,EAAE,CAAC;IAAE4D,IAAI,EAAEvE;EAAM,CAAC,CAAC;EAC3BY,SAAS,EAAE,CAAC;IAAE2D,IAAI,EAAEvE;EAAM,CAAC,CAAC;EAC5BwE,OAAO,EAAE,CAAC;IAAED,IAAI,EAAEvE;EAAM,CAAC;AAC7B,CAAC,EAAAS,8BAAA,CACJ;AACDD,6BAA6B,GAAGd,UAAU,CAAC,CACvCI,SAAS,CAAC;EACN2E,QAAQ,EAAE,6BAA6B;EACvCC,QAAQ,EAAE/E,oBAAoB;EAC9BgF,eAAe,EAAE9E,uBAAuB,CAAC+E,MAAM;EAC/CC,SAAS,EAAE,CACP;IACIC,OAAO,EAAE1E,iBAAiB;IAC1B2E,WAAW,EAAEhF,UAAU,CAAC,MAAMS,6BAA6B,CAAC;IAC5DwE,KAAK,EAAE;EACX,CAAC,CACJ;EACDC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACtF,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEY,6BAA6B,CAAC;AACjC,SAASA,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}