{"ast": null, "code": "var SwDexieModule_1;\nimport { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { DEXI_CONFIG, SwDexieService } from './sw-dexie.service';\nlet SwDexieModule = SwDexieModule_1 = class SwDexieModule {\n  static forRoot(hubName) {\n    return {\n      ngModule: SwDexieModule_1,\n      providers: [{\n        provide: DEXI_CONFIG,\n        useValue: hubName\n      }, SwDexieService]\n    };\n  }\n};\nSwDexieModule = SwDexieModule_1 = __decorate([NgModule()], SwDexieModule);\nexport { SwDexieModule };", "map": {"version": 3, "names": ["SwDexieModule_1", "__decorate", "NgModule", "DEXI_CONFIG", "SwDexieService", "SwDexieModule", "forRoot", "hubName", "ngModule", "providers", "provide", "useValue"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-dexie/sw-dexie.module.ts"], "sourcesContent": ["var SwDexieModule_1;\nimport { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { DEXI_CONFIG, SwDexieService } from './sw-dexie.service';\nlet SwDexieModule = SwDexieModule_1 = class SwDexieModule {\n    static forRoot(hubName) {\n        return {\n            ngModule: SwDexieModule_1,\n            providers: [\n                { provide: DEXI_CONFIG, useValue: hubName },\n                SwDexieService,\n            ]\n        };\n    }\n};\nSwDexieModule = SwDexieModule_1 = __decorate([\n    NgModule()\n], SwDexieModule);\nexport { SwDexieModule };\n"], "mappings": "AAAA,IAAIA,eAAe;AACnB,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,EAAEC,cAAc,QAAQ,oBAAoB;AAChE,IAAIC,aAAa,GAAGL,eAAe,GAAG,MAAMK,aAAa,CAAC;EACtD,OAAOC,OAAOA,CAACC,OAAO,EAAE;IACpB,OAAO;MACHC,QAAQ,EAAER,eAAe;MACzBS,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEP,WAAW;QAAEQ,QAAQ,EAAEJ;MAAQ,CAAC,EAC3CH,cAAc;IAEtB,CAAC;EACL;AACJ,CAAC;AACDC,aAAa,GAAGL,eAAe,GAAGC,UAAU,CAAC,CACzCC,QAAQ,CAAC,CAAC,CACb,EAAEG,aAAa,CAAC;AACjB,SAASA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}