{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { SwuiNumericRangeComponent } from './swui-numeric-range.component';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ControlContainer, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';\nimport { SwuiControlMessagesModule } from '../swui-control-messages/swui-control-messages.module';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\ndescribe('SwuiNumericRangeComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SwuiNumericRangeComponent],\n      imports: [BrowserAnimationsModule, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatIconModule, MatButtonModule, SwuiControlMessagesModule],\n      providers: [{\n        provide: ControlContainer,\n        useExisting: FormGroupDirective,\n        multi: true\n      }]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiNumericRangeComponent);\n    component = fixture.componentInstance;\n    component.ngOnInit();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "SwuiNumericRangeComponent", "BrowserAnimationsModule", "ControlContainer", "FormGroupDirective", "ReactiveFormsModule", "SwuiControlMessagesModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatButtonModule", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "imports", "providers", "provide", "useExisting", "multi", "compileComponents", "createComponent", "componentInstance", "ngOnInit"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-numeric-range/swui-numeric-range.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\n\nimport { SwuiNumericRangeComponent } from './swui-numeric-range.component';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ControlContainer, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';\nimport { SwuiControlMessagesModule } from '../swui-control-messages/swui-control-messages.module';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\n\ndescribe('SwuiNumericRangeComponent', () => {\n  let component: SwuiNumericRangeComponent;\n  let fixture: ComponentFixture<SwuiNumericRangeComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SwuiNumericRangeComponent],\n      imports: [\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatIconModule,\n        MatButtonModule,\n        SwuiControlMessagesModule\n      ],\n      providers: [\n        {\n          provide: ControlContainer,\n          useExisting: FormGroupDirective, multi: true\n        }\n      ]\n    }).compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiNumericRangeComponent);\n    component = fixture.componentInstance;\n    component.ngOnInit();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,mBAAmB,QAAQ,gBAAgB;AAC1F,SAASC,yBAAyB,QAAQ,uDAAuD;AACjG,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAE1DC,QAAQ,CAAC,2BAA2B,EAAE,MAAK;EACzC,IAAIC,SAAoC;EACxC,IAAIC,OAAoD;EAExDC,UAAU,CAACd,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACgB,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACf,yBAAyB,CAAC;MACzCgB,OAAO,EAAE,CACPf,uBAAuB,EACvBG,mBAAmB,EACnBE,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbE,eAAe,EACfJ,yBAAyB,CAC1B;MACDY,SAAS,EAAE,CACT;QACEC,OAAO,EAAEhB,gBAAgB;QACzBiB,WAAW,EAAEhB,kBAAkB;QAAEiB,KAAK,EAAE;OACzC;KAEJ,CAAC,CAACC,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHR,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGd,OAAO,CAACwB,eAAe,CAACtB,yBAAyB,CAAC;IAC5DW,SAAS,GAAGC,OAAO,CAACW,iBAAiB;IACrCZ,SAAS,CAACa,QAAQ,EAAE;EACtB,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}