{"ast": null, "code": "var _UserMenuComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./user-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./user-menu.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { take } from 'rxjs/operators';\nimport { PERMISSIONS_NAMES } from '../../services/sw-hub-auth/permissions';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SwuiSettingsDialogComponent } from '../settings-dialog/swui-settings-dialog.component';\nexport const MASTER_ID = '1';\nlet UserMenuComponent = (_UserMenuComponent = class UserMenuComponent {\n  constructor(dialog, configService, auth) {\n    this.dialog = dialog;\n    this.configService = configService;\n    this.auth = auth;\n    this.languageChanges = new EventEmitter();\n    this.settingsChanges = new EventEmitter();\n    this.logout = new EventEmitter();\n  }\n  showSettings() {\n    this.dialog.open(SwuiSettingsDialogComponent, {\n      width: '700px'\n    }).afterClosed().pipe(take(1)).subscribe(settings => {\n      if (settings) {\n        this.settingsChanges.emit(settings);\n      }\n    });\n  }\n  twoFactorSettings() {\n    var _this$configService$l;\n    location.href = `${(_this$configService$l = this.configService.loginUrl) === null || _this$configService$l === void 0 ? void 0 : _this$configService$l.replace('/login', '')}/twofactorsettings`;\n  }\n  changePasswordAllowed() {\n    return this.entityKey !== MASTER_ID && this.auth.allowedTo([PERMISSIONS_NAMES.KEYENTITY_USER_CHANGE_PASSWORD]);\n  }\n  changePassword() {\n    var _this$configService$l2;\n    location.href = `${(_this$configService$l2 = this.configService.loginUrl) === null || _this$configService$l2 === void 0 ? void 0 : _this$configService$l2.replace('/login', '')}/changepassword`;\n  }\n}, _UserMenuComponent.ctorParameters = () => [{\n  type: MatDialog\n}, {\n  type: SwHubConfigService\n}, {\n  type: SwHubAuthService\n}], _UserMenuComponent.propDecorators = {\n  username: [{\n    type: Input\n  }],\n  entityKey: [{\n    type: Input\n  }],\n  hasTwoFactor: [{\n    type: Input\n  }],\n  languageChanges: [{\n    type: Output\n  }],\n  settingsChanges: [{\n    type: Output\n  }],\n  logout: [{\n    type: Output\n  }]\n}, _UserMenuComponent);\nUserMenuComponent = __decorate([Component({\n  selector: 'lib-swui-user-menu',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], UserMenuComponent);\nexport { UserMenuComponent };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "Component", "EventEmitter", "Input", "Output", "MatDialog", "take", "PERMISSIONS_NAMES", "SwHubAuthService", "SwHubConfigService", "SwuiSettingsDialogComponent", "MASTER_ID", "UserMenuComponent", "_UserMenuComponent", "constructor", "dialog", "configService", "auth", "languageChanges", "settingsChanges", "logout", "showSettings", "open", "width", "afterClosed", "pipe", "subscribe", "settings", "emit", "twoFactorSettings", "_this$configService$l", "location", "href", "loginUrl", "replace", "changePasswordAllowed", "entityKey", "allowedTo", "KEYENTITY_USER_CHANGE_PASSWORD", "changePassword", "_this$configService$l2", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "changeDetection", "OnPush", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/user-menu/user-menu.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { take } from 'rxjs/operators';\nimport { AppSettings } from '../../services/settings/app-settings';\nimport { PERMISSIONS_NAMES } from '../../services/sw-hub-auth/permissions';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SwuiSettingsDialogComponent } from '../settings-dialog/swui-settings-dialog.component';\n\nexport const MASTER_ID = '1';\n\n@Component({\n    selector: 'lib-swui-user-menu',\n    templateUrl: './user-menu.component.html',\n    styleUrls: ['./user-menu.component.scss'],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    standalone: false\n})\nexport class UserMenuComponent {\n  @Input() username: string | undefined;\n  @Input() entityKey: string | undefined;\n  @Input() hasTwoFactor: string | undefined;\n  @Output() languageChanges = new EventEmitter<string>();\n  @Output() settingsChanges = new EventEmitter<AppSettings>();\n  @Output() logout = new EventEmitter<void>();\n\n  constructor( private readonly dialog: MatDialog,\n               private readonly configService: SwHubConfigService,\n               private readonly auth: SwHubAuthService,\n  ) {\n  }\n\n  showSettings() {\n    this.dialog.open(SwuiSettingsDialogComponent, {\n      width: '700px'\n    }).afterClosed().pipe(\n      take(1)\n    ).subscribe(settings => {\n      if (settings) {\n        this.settingsChanges.emit(settings);\n      }\n    });\n  }\n\n  twoFactorSettings() {\n    location.href = `${this.configService.loginUrl?.replace('/login', '')}/twofactorsettings`;\n  }\n\n  changePasswordAllowed(): boolean {\n    return this.entityKey !== MASTER_ID && this.auth.allowedTo([PERMISSIONS_NAMES.KEYENTITY_USER_CHANGE_PASSWORD]);\n  }\n\n  changePassword() {\n    location.href = `${this.configService.loginUrl?.replace('/login', '')}/changepassword`;\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,uBAAuB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC/F,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,IAAI,QAAQ,gBAAgB;AAErC,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,2BAA2B,QAAQ,mDAAmD;AAE/F,OAAO,MAAMC,SAAS,GAAG,GAAG;AASrB,IAAMC,iBAAiB,IAAAC,kBAAA,GAAvB,MAAMD,iBAAiB;EAQ5BE,YAA8BC,MAAiB,EACjBC,aAAiC,EACjCC,IAAsB;IAFtB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,IAAI,GAAJA,IAAI;IANxB,KAAAC,eAAe,GAAG,IAAIhB,YAAY,EAAU;IAC5C,KAAAiB,eAAe,GAAG,IAAIjB,YAAY,EAAe;IACjD,KAAAkB,MAAM,GAAG,IAAIlB,YAAY,EAAQ;EAM3C;EAEAmB,YAAYA,CAAA;IACV,IAAI,CAACN,MAAM,CAACO,IAAI,CAACZ,2BAA2B,EAAE;MAC5Ca,KAAK,EAAE;KACR,CAAC,CAACC,WAAW,EAAE,CAACC,IAAI,CACnBnB,IAAI,CAAC,CAAC,CAAC,CACR,CAACoB,SAAS,CAACC,QAAQ,IAAG;MACrB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACR,eAAe,CAACS,IAAI,CAACD,QAAQ,CAAC;MACrC;IACF,CAAC,CAAC;EACJ;EAEAE,iBAAiBA,CAAA;IAAA,IAAAC,qBAAA;IACfC,QAAQ,CAACC,IAAI,GAAG,IAAAF,qBAAA,GAAG,IAAI,CAACd,aAAa,CAACiB,QAAQ,cAAAH,qBAAA,uBAA3BA,qBAAA,CAA6BI,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,oBAAoB;EAC3F;EAEAC,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACC,SAAS,KAAKzB,SAAS,IAAI,IAAI,CAACM,IAAI,CAACoB,SAAS,CAAC,CAAC9B,iBAAiB,CAAC+B,8BAA8B,CAAC,CAAC;EAChH;EAEAC,cAAcA,CAAA;IAAA,IAAAC,sBAAA;IACZT,QAAQ,CAACC,IAAI,GAAG,IAAAQ,sBAAA,GAAG,IAAI,CAACxB,aAAa,CAACiB,QAAQ,cAAAO,sBAAA,uBAA3BA,sBAAA,CAA6BN,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,iBAAiB;EACxF;;;;;;;;;UAnCC/B;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLC;EAAM;;UACNA;EAAM;;UACNA;EAAM;;AANIQ,iBAAiB,GAAA6B,UAAA,EAP7BxC,SAAS,CAAC;EACPyC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAA,EAAAC,oBAAyC;EAEzCC,eAAe,EAAE7C,uBAAuB,CAAC8C,MAAM;EAC/CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWnC,iBAAiB,CAqC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}