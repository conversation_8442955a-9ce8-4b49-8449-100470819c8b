{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { Immediate } from '../util/Immediate';\nvar setImmediate = Immediate.setImmediate,\n  clearImmediate = Immediate.clearImmediate;\nexport var immediateProvider = {\n  setImmediate: function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var delegate = immediateProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate).apply(void 0, __spreadArray([], __read(args)));\n  },\n  clearImmediate: function (handle) {\n    var delegate = immediateProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n  },\n  delegate: undefined\n};\n//# sourceMappingURL=immediateProvider.js.map", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "Immediate", "setImmediate", "clearImmediate", "immediate<PERSON>rovider", "args", "_i", "arguments", "length", "delegate", "apply", "handle", "undefined"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/immediateProvider.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { Immediate } from '../util/Immediate';\nvar setImmediate = Immediate.setImmediate, clearImmediate = Immediate.clearImmediate;\nexport var immediateProvider = {\n    setImmediate: function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var delegate = immediateProvider.delegate;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate).apply(void 0, __spreadArray([], __read(args)));\n    },\n    clearImmediate: function (handle) {\n        var delegate = immediateProvider.delegate;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n    },\n    delegate: undefined,\n};\n//# sourceMappingURL=immediateProvider.js.map"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,IAAIC,YAAY,GAAGD,SAAS,CAACC,YAAY;EAAEC,cAAc,GAAGF,SAAS,CAACE,cAAc;AACpF,OAAO,IAAIC,iBAAiB,GAAG;EAC3BF,YAAY,EAAE,SAAAA,CAAA,EAAY;IACtB,IAAIG,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC5B;IACA,IAAIG,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACzC,OAAO,CAAC,CAACA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACP,YAAY,KAAKA,YAAY,EAAEQ,KAAK,CAAC,KAAK,CAAC,EAAEV,aAAa,CAAC,EAAE,EAAED,MAAM,CAACM,IAAI,CAAC,CAAC,CAAC;EACvJ,CAAC;EACDF,cAAc,EAAE,SAAAA,CAAUQ,MAAM,EAAE;IAC9B,IAAIF,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACzC,OAAO,CAAC,CAACA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACN,cAAc,KAAKA,cAAc,EAAEQ,MAAM,CAAC;EACpH,CAAC;EACDF,QAAQ,EAAEG;AACd,CAAC;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}