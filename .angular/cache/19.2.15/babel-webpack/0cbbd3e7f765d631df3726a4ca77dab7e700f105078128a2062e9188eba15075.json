{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"hubs\"];\nvar _SwHubConfigService;\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { catchError, first, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { PERMISSIONS_NAMES } from '../sw-hub-auth/permissions';\nclass BaseHubConfig {}\nexport class HubConfig extends BaseHubConfig {}\nlet SwHubConfigService = (_SwHubConfigService = class SwHubConfigService extends HubConfig {\n  constructor(http) {\n    super();\n    this.http = http;\n  }\n  fetch() {\n    return new Promise((resolve, reject) => {\n      this.http.get('/api/config').pipe(first(), map(config => {\n        if (!config) {\n          return undefined;\n        }\n        const {\n            hubs\n          } = config,\n          data = _objectWithoutProperties(config, _excluded);\n        return _objectSpread(_objectSpread({}, data), {}, {\n          hubs: _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, hubs !== null && hubs !== void 0 && hubs.casino ? {\n            casino: {\n              url: hubs.casino,\n              name: 'HUBS.casino',\n              cssClass: 'hub-casino',\n              permission: PERMISSIONS_NAMES.HUB_CASINO\n            }\n          } : {}), hubs !== null && hubs !== void 0 && hubs.engagement ? {\n            engagement: {\n              url: hubs.engagement,\n              name: 'HUBS.engagement',\n              cssClass: 'hub-engagement',\n              permission: PERMISSIONS_NAMES.HUB_ENGAGEMENT\n            }\n          } : {}), hubs !== null && hubs !== void 0 && hubs.analytics ? {\n            analytics: {\n              url: hubs.analytics,\n              name: 'HUBS.analytics',\n              cssClass: 'hub-analytics',\n              permission: PERMISSIONS_NAMES.HUB_ANALYTICS\n            }\n          } : {}), hubs !== null && hubs !== void 0 && hubs.studio ? {\n            studio: {\n              url: hubs.studio,\n              name: 'HUBS.studio',\n              cssClass: 'hub-studio',\n              permission: PERMISSIONS_NAMES.HUB_STUDIO\n            }\n          } : {})\n        });\n      }), catchError(err => {\n        reject(err);\n        return of(undefined);\n      })).subscribe(config => {\n        Object.assign(this, config);\n        resolve(true);\n      });\n    });\n  }\n}, _SwHubConfigService.ctorParameters = () => [{\n  type: HttpClient\n}], _SwHubConfigService);\nSwHubConfigService = __decorate([Injectable()], SwHubConfigService);\nexport { SwHubConfigService };", "map": {"version": 3, "names": ["__decorate", "Injectable", "HttpClient", "catchError", "first", "map", "of", "PERMISSIONS_NAMES", "BaseHubConfig", "HubConfig", "SwHubConfigService", "_SwHubConfigService", "constructor", "http", "fetch", "Promise", "resolve", "reject", "get", "pipe", "config", "undefined", "hubs", "data", "_objectWithoutProperties", "_excluded", "_objectSpread", "casino", "url", "name", "cssClass", "permission", "HUB_CASINO", "engagement", "HUB_ENGAGEMENT", "analytics", "HUB_ANALYTICS", "studio", "HUB_STUDIO", "err", "subscribe", "Object", "assign", "ctorParameters", "type"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-config/sw-hub-config.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { catchError, first, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { PERMISSIONS_NAMES } from '../sw-hub-auth/permissions';\nclass BaseHubConfig {\n}\nexport class HubConfig extends BaseHubConfig {\n}\nlet SwHubConfigService = class SwHubConfigService extends HubConfig {\n    constructor(http) {\n        super();\n        this.http = http;\n    }\n    fetch() {\n        return new Promise((resolve, reject) => {\n            this.http.get('/api/config').pipe(first(), map(config => {\n                if (!config) {\n                    return undefined;\n                }\n                const { hubs, ...data } = config;\n                return {\n                    ...data,\n                    hubs: {\n                        ...(hubs?.casino ? {\n                            casino: {\n                                url: hubs.casino,\n                                name: 'HUBS.casino',\n                                cssClass: 'hub-casino',\n                                permission: PERMISSIONS_NAMES.HUB_CASINO\n                            }\n                        } : {}),\n                        ...(hubs?.engagement ? {\n                            engagement: {\n                                url: hubs.engagement,\n                                name: 'HUBS.engagement',\n                                cssClass: 'hub-engagement',\n                                permission: PERMISSIONS_NAMES.HUB_ENGAGEMENT\n                            }\n                        } : {}),\n                        ...(hubs?.analytics ? {\n                            analytics: {\n                                url: hubs.analytics,\n                                name: 'HUBS.analytics',\n                                cssClass: 'hub-analytics',\n                                permission: PERMISSIONS_NAMES.HUB_ANALYTICS\n                            }\n                        } : {}),\n                        ...(hubs?.studio ? {\n                            studio: {\n                                url: hubs.studio,\n                                name: 'HUBS.studio',\n                                cssClass: 'hub-studio',\n                                permission: PERMISSIONS_NAMES.HUB_STUDIO\n                            }\n                        } : {}),\n                    }\n                };\n            }), catchError(err => {\n                reject(err);\n                return of(undefined);\n            })).subscribe(config => {\n                Object.assign(this, config);\n                resolve(true);\n            });\n        });\n    }\n    static { this.ctorParameters = () => [\n        { type: HttpClient }\n    ]; }\n};\nSwHubConfigService = __decorate([\n    Injectable()\n], SwHubConfigService);\nexport { SwHubConfigService };\n"], "mappings": ";;;;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,UAAU,EAAEC,KAAK,EAAEC,GAAG,QAAQ,gBAAgB;AACvD,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,MAAMC,aAAa,CAAC;AAEpB,OAAO,MAAMC,SAAS,SAASD,aAAa,CAAC;AAE7C,IAAIE,kBAAkB,IAAAC,mBAAA,GAAG,MAAMD,kBAAkB,SAASD,SAAS,CAAC;EAChEG,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAC,aAAa,CAAC,CAACC,IAAI,CAACf,KAAK,CAAC,CAAC,EAAEC,GAAG,CAACe,MAAM,IAAI;QACrD,IAAI,CAACA,MAAM,EAAE;UACT,OAAOC,SAAS;QACpB;QACA,MAAM;YAAEC;UAAc,CAAC,GAAGF,MAAM;UAAfG,IAAI,GAAAC,wBAAA,CAAKJ,MAAM,EAAAK,SAAA;QAChC,OAAAC,aAAA,CAAAA,aAAA,KACOH,IAAI;UACPD,IAAI,EAAAI,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACIJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,MAAM,GAAG;YACfA,MAAM,EAAE;cACJC,GAAG,EAAEN,IAAI,CAACK,MAAM;cAChBE,IAAI,EAAE,aAAa;cACnBC,QAAQ,EAAE,YAAY;cACtBC,UAAU,EAAExB,iBAAiB,CAACyB;YAClC;UACJ,CAAC,GAAG,CAAC,CAAC,GACFV,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEW,UAAU,GAAG;YACnBA,UAAU,EAAE;cACRL,GAAG,EAAEN,IAAI,CAACW,UAAU;cACpBJ,IAAI,EAAE,iBAAiB;cACvBC,QAAQ,EAAE,gBAAgB;cAC1BC,UAAU,EAAExB,iBAAiB,CAAC2B;YAClC;UACJ,CAAC,GAAG,CAAC,CAAC,GACFZ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEa,SAAS,GAAG;YAClBA,SAAS,EAAE;cACPP,GAAG,EAAEN,IAAI,CAACa,SAAS;cACnBN,IAAI,EAAE,gBAAgB;cACtBC,QAAQ,EAAE,eAAe;cACzBC,UAAU,EAAExB,iBAAiB,CAAC6B;YAClC;UACJ,CAAC,GAAG,CAAC,CAAC,GACFd,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEe,MAAM,GAAG;YACfA,MAAM,EAAE;cACJT,GAAG,EAAEN,IAAI,CAACe,MAAM;cAChBR,IAAI,EAAE,aAAa;cACnBC,QAAQ,EAAE,YAAY;cACtBC,UAAU,EAAExB,iBAAiB,CAAC+B;YAClC;UACJ,CAAC,GAAG,CAAC,CAAC;QACT;MAET,CAAC,CAAC,EAAEnC,UAAU,CAACoC,GAAG,IAAI;QAClBtB,MAAM,CAACsB,GAAG,CAAC;QACX,OAAOjC,EAAE,CAACe,SAAS,CAAC;MACxB,CAAC,CAAC,CAAC,CAACmB,SAAS,CAACpB,MAAM,IAAI;QACpBqB,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEtB,MAAM,CAAC;QAC3BJ,OAAO,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AAIJ,CAAC,EAHYL,mBAAA,CAAKgC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE1C;AAAW,CAAC,CACvB,EAAAS,mBAAA,CACJ;AACDD,kBAAkB,GAAGV,UAAU,CAAC,CAC5BC,UAAU,CAAC,CAAC,CACf,EAAES,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}