{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiTimepickerComponent } from './swui-timepicker.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatOptionModule } from '@angular/material/core';\nimport { MatSelectModule } from '@angular/material/select';\nlet SwuiTimepickerModule = class SwuiTimepickerModule {};\nSwuiTimepickerModule = __decorate([NgModule({\n  declarations: [SwuiTimepickerComponent],\n  imports: [CommonModule, ReactiveFormsModule, MatFormFieldModule, MatOptionModule, MatSelectModule],\n  exports: [SwuiTimepickerComponent]\n})], SwuiTimepickerModule);\nexport { SwuiTimepickerModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "ReactiveFormsModule", "SwuiTimepickerComponent", "MatFormFieldModule", "MatOptionModule", "MatSelectModule", "SwuiTimepickerModule", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-timepicker/swui-timepicker.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiTimepickerComponent } from './swui-timepicker.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatOptionModule } from '@angular/material/core';\nimport { MatSelectModule } from '@angular/material/select';\nlet SwuiTimepickerModule = class SwuiTimepickerModule {\n};\nSwuiTimepickerModule = __decorate([\n    NgModule({\n        declarations: [SwuiTimepickerComponent],\n        imports: [\n            CommonModule,\n            ReactiveFormsModule,\n            MatFormFieldModule,\n            MatOptionModule,\n            MatSelectModule,\n        ],\n        exports: [SwuiTimepickerComponent]\n    })\n], SwuiTimepickerModule);\nexport { SwuiTimepickerModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,IAAIC,oBAAoB,GAAG,MAAMA,oBAAoB,CAAC,EACrD;AACDA,oBAAoB,GAAGR,UAAU,CAAC,CAC9BC,QAAQ,CAAC;EACLQ,YAAY,EAAE,CAACL,uBAAuB,CAAC;EACvCM,OAAO,EAAE,CACLR,YAAY,EACZC,mBAAmB,EACnBE,kBAAkB,EAClBC,eAAe,EACfC,eAAe,CAClB;EACDI,OAAO,EAAE,CAACP,uBAAuB;AACrC,CAAC,CAAC,CACL,EAAEI,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}