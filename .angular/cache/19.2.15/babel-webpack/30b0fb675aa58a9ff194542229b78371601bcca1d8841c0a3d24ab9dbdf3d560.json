{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { zip } from './zip';\nexport function zipWith() {\n  var otherInputs = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    otherInputs[_i] = arguments[_i];\n  }\n  return zip.apply(void 0, __spreadArray([], __read(otherInputs)));\n}\n//# sourceMappingURL=zipWith.js.map", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "zip", "zipWith", "otherInputs", "_i", "arguments", "length", "apply"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/operators/zipWith.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { zip } from './zip';\nexport function zipWith() {\n    var otherInputs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        otherInputs[_i] = arguments[_i];\n    }\n    return zip.apply(void 0, __spreadArray([], __read(otherInputs)));\n}\n//# sourceMappingURL=zipWith.js.map"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,GAAG,QAAQ,OAAO;AAC3B,OAAO,SAASC,OAAOA,CAAA,EAAG;EACtB,IAAIC,WAAW,GAAG,EAAE;EACpB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,WAAW,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EACnC;EACA,OAAOH,GAAG,CAACM,KAAK,CAAC,KAAK,CAAC,EAAEP,aAAa,CAAC,EAAE,EAAED,MAAM,CAACI,WAAW,CAAC,CAAC,CAAC;AACpE;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}