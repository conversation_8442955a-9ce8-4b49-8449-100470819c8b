{"ast": null, "code": "var _SwuiSelectComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-select.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-select.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostBinding, Input, isDevMode, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { TranslateService } from '@ngx-translate/core';\nimport { ReplaySubject } from 'rxjs';\nimport { filter, map, take, takeUntil } from 'rxjs/operators';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nconst CONTROL_NAME = 'lib-swui-select';\nlet nextUniqueId = 0;\nlet SwuiSelectComponent = (_SwuiSelectComponent = class SwuiSelectComponent extends SwuiMatFormFieldControl {\n  set multiple(value) {\n    const newMultiple = coerceBooleanProperty(value);\n    if (newMultiple !== this._multiple) {\n      if (isDevMode() && this._contentInitialized) {\n        throw new Error('Cannot change `multiple` mode of `lib-swui-select` after initialization.');\n      }\n      this._multiple = newMultiple;\n      this.itemHeight = newMultiple ? 48 : 42;\n    }\n  }\n  get multiple() {\n    return this._multiple;\n  }\n  set data(val) {\n    var _this$options;\n    this._data = val || [];\n    this.options = this._data;\n    if (this.multiple && !this.disableAllOption) {\n      var _this$value;\n      this.allChecked = ((_this$value = this.value) === null || _this$value === void 0 ? void 0 : _this$value.length) === this.enabledData.length;\n    }\n    (_this$options = this.options) === null || _this$options === void 0 || _this$options.forEach(option => {\n      var _this$value2;\n      return option.state = {\n        checked: (_this$value2 = this.value) === null || _this$value2 === void 0 ? void 0 : _this$value2.includes(option.id)\n      };\n    });\n    if (Array.isArray(this.options) && this.options.length) {\n      this.onDataReceived.next(this.options);\n    }\n  }\n  get data() {\n    return this._data;\n  }\n  set value(val) {\n    this.patchSelectControl(val);\n  }\n  get value() {\n    var _this$selectControl$v, _this$selectControl$v2;\n    return this.multiple ? (_this$selectControl$v = this.selectControl.value) === null || _this$selectControl$v === void 0 ? void 0 : _this$selectControl$v.map(v => v.id) : this.selectControl.value && ((_this$selectControl$v2 = this.selectControl.value[0]) === null || _this$selectControl$v2 === void 0 ? void 0 : _this$selectControl$v2.id);\n  }\n  get empty() {\n    var _this$selectControl$v3;\n    return !((_this$selectControl$v3 = this.selectControl.value) !== null && _this$selectControl$v3 !== void 0 && _this$selectControl$v3.length);\n  }\n  get viewportHeight() {\n    let length = this.data.length;\n    if (!this.multiple && !this.disableEmptyOption) {\n      length += 1;\n    }\n    length = Math.floor(length / 5) && 5 || length % 5;\n    if (length === 5) {\n      if (this.showSearch) {\n        length -= 1;\n      }\n      if (this.multiple) {\n        length -= 1;\n      }\n    }\n    return length * this.itemHeight;\n  }\n  get shouldLabelFloat() {\n    return this.triggerInputControl.value;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, cd, translate) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.cd = cd;\n    this.translate = translate;\n    this.searchPlaceholder = 'Search';\n    this.showSearch = false;\n    this.startSearchLength = 0;\n    this.emptyOptionPlaceholder = 'None';\n    this.disableEmptyOption = false;\n    this.disableAllOption = false;\n    this.allChecked = false;\n    this.options = [];\n    this.itemHeight = 48;\n    this.onDataReceived = new ReplaySubject(1);\n    this.controlType = CONTROL_NAME;\n    this.triggerInputControl = new UntypedFormControl();\n    this.searchControl = new UntypedFormControl();\n    this.selectControl = new UntypedFormControl();\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._contentInitialized = false;\n    this._multiple = false;\n    this._data = [];\n    this.previousSelected = null;\n  }\n  ngOnInit() {\n    this.searchControl.valueChanges.pipe(filter(data => this.showSearch && (data.length >= (this.startSearchLength || 0) || data.length === 0)), map(searchString => searchString.toLowerCase()), takeUntil(this.destroyed$)).subscribe(search => {\n      this.previousSelected = this.multiple && search ? this.selectControl.value : null;\n      this.options = this.data.filter(option => {\n        return option.text && option.text.toLowerCase().indexOf(search) > -1;\n      });\n      this.cd.markForCheck();\n    });\n    this.selectControl.valueChanges.pipe(map(val => {\n      if (this.multiple && Array.isArray(this.previousSelected) && Array.isArray(val)) {\n        const previousSelected = this.previousSelected.filter(id => !this.options.some(option => option.id === id));\n        const values = this.enabledData.filter(({\n          id\n        }) => previousSelected.some(item => item === id));\n        return [...values, ...val];\n      }\n      return val;\n    }), map(val => val.map(item => item ? item.id : '')), takeUntil(this.destroyed$)).subscribe(val => {\n      this.setVisibleValue(val);\n      this.onChange(this.multiple ? val : val[0] || null);\n      if (this.multiple && !this.disableAllOption) {\n        this.allChecked = this.enabledData.every(({\n          id\n        }) => {\n          var _this$value3;\n          return (_this$value3 = this.value) === null || _this$value3 === void 0 ? void 0 : _this$value3.includes(id);\n        });\n      }\n      this.cd.detectChanges();\n    });\n  }\n  ngAfterContentInit() {\n    this._contentInitialized = true;\n  }\n  setVisibleValue(val, options) {\n    if (options) {\n      this.patchTriggerInputControl(val, options);\n    } else {\n      this.onDataReceived.pipe(take(1)).subscribe(data => {\n        this.patchTriggerInputControl(val, data);\n      });\n    }\n  }\n  patchTriggerInputControl(val, options) {\n    let option = options === null || options === void 0 ? void 0 : options.find(opt => opt.id === val[0]);\n    let text = option ? this.translate.instant(option.text) : val[0];\n    let visibleValue = (val === null || val === void 0 ? void 0 : val.length) > 1 ? `${text} (+${val.length - 1} ${this.translate.instant('ALL.more')})` : text;\n    this.triggerInputControl.patchValue(visibleValue);\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.elRef && this.trigger && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n      this.trigger.openMenu();\n    }\n  }\n  writeValue(val) {\n    this.patchSelectControl(val);\n  }\n  patchSelectControl(val) {\n    this.onDataReceived.pipe(take(1)).subscribe(options => {\n      if (this.multiple && !this.disableAllOption) {\n        this.allChecked = Array.isArray(val) && val.length === this.enabledData.length;\n      }\n      const values = coerceArray(val || []);\n      options === null || options === void 0 || options.forEach(option => option.state = {\n        checked: values === null || values === void 0 ? void 0 : values.includes(option.id)\n      });\n      const value = options.filter(opt => values.includes(opt.id));\n      this.selectControl.patchValue(value, {\n        emitEvent: false\n      });\n      this.setVisibleValue(values, options);\n      this.cd.detectChanges();\n    });\n  }\n  toggleAll(event) {\n    event === null || event === void 0 || event.preventDefault();\n    event === null || event === void 0 || event.stopPropagation();\n    this.allChecked = !this.allChecked;\n    this.options.forEach(option => {\n      if (!option.disabled) {\n        option.state.checked = this.allChecked;\n      }\n    });\n    let checkedOptions = this.options.filter(option => option.state.checked);\n    this.selectControl.setValue(checkedOptions);\n  }\n  onOpened() {\n    if (this.searchRef) {\n      const input = this.searchRef.nativeElement;\n      input.focus();\n    }\n    setTimeout(() => {\n      if (this.virtualScroll) {\n        if (this.selectControl.value && this.selectControl.value.length) {\n          this.virtualScroll.scrollToIndex(this.options.map(option => option.id).indexOf(this.selectControl.value[0].id));\n        } else {\n          this.virtualScroll.scrollToOffset(1);\n        }\n      }\n    }, 100);\n  }\n  onClosed() {\n    this.searchControl.reset('', {\n      emitEvent: false\n    });\n    this.options = this.data;\n    this.previousSelected = null;\n  }\n  onSelectMultiple(event, option) {\n    var _this$data;\n    event.preventDefault();\n    event.stopPropagation();\n    if (option.state) {\n      option.state.checked = !option.state.checked;\n    }\n    this.selectControl.patchValue((_this$data = this.data) === null || _this$data === void 0 ? void 0 : _this$data.filter(item => {\n      var _item$state;\n      return (_item$state = item.state) === null || _item$state === void 0 ? void 0 : _item$state.checked;\n    }));\n  }\n  onSelect(option) {\n    this.options.forEach(opt => opt.state.checked = opt.id === (option === null || option === void 0 ? void 0 : option.id));\n    this.selectControl.patchValue(coerceArray(option || []));\n  }\n  stopPropagation(event) {\n    event.stopPropagation();\n  }\n  onDisabledState(disabled) {\n    disabled ? this.triggerInputControl.disable({\n      emitEvent: false\n    }) : this.triggerInputControl.enable({\n      emitEvent: false\n    });\n  }\n  get enabledData() {\n    return this.data.filter(({\n      disabled\n    }) => !disabled);\n  }\n}, _SwuiSelectComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: TranslateService\n}], _SwuiSelectComponent.propDecorators = {\n  searchPlaceholder: [{\n    type: Input\n  }],\n  showSearch: [{\n    type: Input\n  }],\n  startSearchLength: [{\n    type: Input\n  }],\n  emptyOptionPlaceholder: [{\n    type: Input\n  }],\n  disableEmptyOption: [{\n    type: Input\n  }],\n  disableAllOption: [{\n    type: Input\n  }],\n  multiple: [{\n    type: Input\n  }],\n  data: [{\n    type: Input\n  }],\n  value: [{\n    type: Input\n  }],\n  trigger: [{\n    type: ViewChild,\n    args: ['trigger']\n  }],\n  searchRef: [{\n    type: ViewChild,\n    args: ['search']\n  }],\n  virtualScroll: [{\n    type: ViewChild,\n    args: [CdkVirtualScrollViewport, {\n      static: false\n    }]\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiSelectComponent);\nSwuiSelectComponent = __decorate([Component({\n  selector: 'lib-swui-select',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiSelectComponent\n  }],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSelectComponent);\nexport { SwuiSelectComponent };", "map": {"version": 3, "names": ["FocusMonitor", "coerce<PERSON><PERSON><PERSON>", "coerceBooleanProperty", "CdkVirtualScrollViewport", "ChangeDetectionStrategy", "ChangeDetectorRef", "Component", "ElementRef", "HostBinding", "Input", "isDevMode", "Optional", "Self", "ViewChild", "UntypedFormControl", "FormGroupDirective", "NgControl", "ErrorStateMatcher", "MatFormFieldControl", "TranslateService", "ReplaySubject", "filter", "map", "take", "takeUntil", "SwuiMatFormFieldControl", "CONTROL_NAME", "nextUniqueId", "SwuiSelectComponent", "_SwuiSelectComponent", "multiple", "value", "newMultiple", "_multiple", "_contentInitialized", "Error", "itemHeight", "data", "val", "_this$options", "_data", "options", "disableAllOption", "_this$value", "allChecked", "length", "enabledData", "for<PERSON>ach", "option", "_this$value2", "state", "checked", "includes", "id", "Array", "isArray", "onDataReceived", "next", "patchSelectControl", "_this$selectControl$v", "_this$selectControl$v2", "selectControl", "v", "empty", "_this$selectControl$v3", "viewportHeight", "disableEmptyOption", "Math", "floor", "showSearch", "shouldLabelFloat", "triggerInputControl", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "cd", "translate", "searchPlaceholder", "startSearchLength", "emptyOptionPlaceholder", "controlType", "searchControl", "previousSelected", "ngOnInit", "valueChanges", "pipe", "searchString", "toLowerCase", "destroyed$", "subscribe", "search", "text", "indexOf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "some", "values", "item", "setVisibleValue", "onChange", "every", "_this$value3", "detectChanges", "ngAfterContentInit", "patchTriggerInputControl", "find", "opt", "instant", "visibleValue", "patchValue", "onContainerClick", "event", "stopPropagation", "trigger", "target", "tagName", "disabled", "nativeElement", "focus", "openMenu", "writeValue", "emitEvent", "toggleAll", "preventDefault", "checkedOptions", "setValue", "onOpened", "searchRef", "input", "setTimeout", "virtualScroll", "scrollToIndex", "scrollToOffset", "onClosed", "reset", "onSelectMultiple", "_this$data", "_item$state", "onSelect", "onDisabledState", "disable", "enable", "type", "args", "static", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "provide", "useExisting", "changeDetection", "OnPush", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-select/swui-select.component.ts"], "sourcesContent": ["import { FocusMonitor } from '@angular/cdk/a11y';\nimport { coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport {\n  AfterContentInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostBinding, Input, isDevMode,\n  OnInit, Optional, Self, ViewChild\n} from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { TranslateService } from '@ngx-translate/core';\nimport { ReplaySubject } from 'rxjs';\nimport { filter, map, take, takeUntil } from 'rxjs/operators';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { SwuiSelectOption } from './swui-select.interface';\n\nconst CONTROL_NAME = 'lib-swui-select';\nlet nextUniqueId = 0;\n\n@Component({\n    selector: 'lib-swui-select',\n    templateUrl: './swui-select.component.html',\n    styleUrls: ['./swui-select.component.scss'],\n    providers: [{ provide: MatFormFieldControl, useExisting: SwuiSelectComponent }],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    standalone: false\n})\nexport class SwuiSelectComponent extends SwuiMatFormFieldControl<string | string[] | undefined> implements OnInit, AfterContentInit {\n  @Input() searchPlaceholder = 'Search';\n  @Input() showSearch = false;\n  @Input() startSearchLength = 0;\n  @Input() emptyOptionPlaceholder = 'None';\n  @Input() disableEmptyOption = false;\n  @Input() disableAllOption = false;\n\n  @Input()\n  set multiple( value: boolean ) {\n    const newMultiple = coerceBooleanProperty(value);\n    if (newMultiple !== this._multiple) {\n      if (isDevMode() && this._contentInitialized) {\n        throw new Error('Cannot change `multiple` mode of `lib-swui-select` after initialization.');\n      }\n      this._multiple = newMultiple;\n      this.itemHeight = newMultiple ? 48 : 42;\n    }\n  }\n\n  get multiple(): boolean {\n    return this._multiple;\n  }\n\n  @Input()\n  set data( val: SwuiSelectOption[] ) {\n    this._data = val || [];\n    this.options = this._data;\n\n    if (this.multiple && !this.disableAllOption) {\n      this.allChecked = this.value?.length === this.enabledData.length;\n    }\n\n    this.options?.forEach(option => option.state = {\n      checked: this.value?.includes(option.id),\n    });\n\n    if (Array.isArray(this.options) && this.options.length) {\n      this.onDataReceived.next(this.options);\n    }\n  }\n\n  get data(): SwuiSelectOption[] {\n    return this._data;\n  }\n\n  @Input()\n  set value( val: string | string[] | undefined ) {\n    this.patchSelectControl(val);\n  }\n\n  get value(): string | string[] | undefined {\n    return this.multiple ?\n      this.selectControl.value?.map(( v: SwuiSelectOption ) => v.id) :\n      this.selectControl.value && this.selectControl.value[0]?.id;\n  }\n\n  get empty() {\n    return !this.selectControl.value?.length;\n  }\n\n  get viewportHeight(): number {\n    let length = this.data.length;\n    if (!this.multiple && !this.disableEmptyOption) {\n      length += 1;\n    }\n    length = Math.floor(length / 5) && 5 || length % 5;\n    if (length === 5) {\n      if (this.showSearch) {\n        length -= 1;\n      }\n      if (this.multiple) {\n        length -= 1;\n      }\n    }\n    return length * this.itemHeight;\n  }\n\n  allChecked = false;\n  options: SwuiSelectOption[] = [];\n\n  itemHeight = 48;\n\n  onDataReceived = new ReplaySubject<SwuiSelectOption[]>(1);\n\n  readonly controlType = CONTROL_NAME;\n  readonly triggerInputControl = new UntypedFormControl();\n  readonly searchControl = new UntypedFormControl();\n  readonly selectControl = new UntypedFormControl();\n\n  @ViewChild('trigger') trigger?: MatMenuTrigger;\n  @ViewChild('search') searchRef?: ElementRef;\n  @ViewChild(CdkVirtualScrollViewport, { static: false }) virtualScroll?: CdkVirtualScrollViewport;\n\n  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;\n\n  @HostBinding('class.floating')\n  get shouldLabelFloat() {\n    return this.triggerInputControl.value;\n  }\n\n  private _contentInitialized = false;\n  private _multiple = false;\n  private _data: SwuiSelectOption[] = [];\n\n  private previousSelected: string[] | null = null;\n\n  constructor( fm: FocusMonitor,\n               elRef: ElementRef<HTMLElement>,\n               @Optional() @Self() ngControl: NgControl,\n               @Optional() parentFormGroup: FormGroupDirective,\n               errorStateMatcher: ErrorStateMatcher,\n               private readonly cd: ChangeDetectorRef,\n               private readonly translate: TranslateService\n  ) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n  }\n\n  ngOnInit(): void {\n    this.searchControl.valueChanges.pipe(\n      filter<string>(data => this.showSearch && (data.length >= (this.startSearchLength || 0) || data.length === 0)),\n      map<string, string>(searchString => searchString.toLowerCase()),\n      takeUntil(this.destroyed$)\n    ).subscribe(search => {\n      this.previousSelected = this.multiple && search ? this.selectControl.value : null;\n      this.options = this.data.filter(option => {\n        return option.text && option.text.toLowerCase().indexOf(search) > -1;\n      });\n      this.cd.markForCheck();\n    });\n\n    this.selectControl.valueChanges.pipe(\n      map<SwuiSelectOption[], SwuiSelectOption[]>(val => {\n        if (this.multiple && Array.isArray(this.previousSelected) && Array.isArray(val)) {\n          const previousSelected = this.previousSelected.filter(id => !this.options.some(option => option.id === id));\n          const values = this.enabledData.filter(( { id } ) => previousSelected.some(item => item === id));\n          return [...values, ...val];\n        }\n\n        return val;\n      }),\n      map<SwuiSelectOption[], string[]>(val => val.map(item => item ? item.id : '')),\n      takeUntil(this.destroyed$)\n    ).subscribe(val => {\n      this.setVisibleValue(val);\n\n      this.onChange(this.multiple ? val : val[0] || null);\n\n      if (this.multiple && !this.disableAllOption) {\n        this.allChecked = this.enabledData.every(({id}) => this.value?.includes(id));\n      }\n      this.cd.detectChanges();\n    });\n  }\n\n  ngAfterContentInit(): void {\n    this._contentInitialized = true;\n  }\n\n  setVisibleValue( val: string[], options?: SwuiSelectOption[] ) {\n    if (options) {\n      this.patchTriggerInputControl(val, options);\n    } else {\n      this.onDataReceived\n        .pipe(\n          take(1),\n        )\n        .subscribe(\n          data => {\n            this.patchTriggerInputControl(val, data);\n          });\n    }\n  }\n\n  patchTriggerInputControl( val: string[], options: SwuiSelectOption[] ) {\n    let option = options?.find(opt => opt.id === val[0]);\n\n    let text = option ?\n      this.translate.instant(option.text) :\n      val[0];\n\n    let visibleValue = val?.length > 1 ?\n      `${text} (+${val.length - 1} ${this.translate.instant('ALL.more')})` :\n      text;\n\n    this.triggerInputControl.patchValue(visibleValue);\n  }\n\n  onContainerClick( event: MouseEvent ): void {\n    event.stopPropagation();\n\n    if (this.elRef && this.trigger && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n      this.trigger.openMenu();\n    }\n  }\n\n  writeValue( val: string | string[] | undefined ): void {\n    this.patchSelectControl(val);\n  }\n\n  patchSelectControl( val: string | string[] | undefined ) {\n    this.onDataReceived\n      .pipe(\n        take(1),\n      )\n      .subscribe(\n        options => {\n          if (this.multiple && !this.disableAllOption) {\n            this.allChecked = Array.isArray(val) && val.length === this.enabledData.length;\n          }\n\n          const values = coerceArray(val || []);\n\n          options?.forEach(option => option.state = {\n            checked: values?.includes(option.id),\n          });\n\n          const value = options.filter(opt => values.includes(opt.id));\n\n          this.selectControl.patchValue(value, { emitEvent: false });\n          this.setVisibleValue(values, options);\n\n          this.cd.detectChanges();\n        }\n      );\n  }\n\n  toggleAll( event?: MouseEvent ) {\n    event?.preventDefault();\n    event?.stopPropagation();\n\n    this.allChecked = !this.allChecked;\n    this.options.forEach(option => {\n      if (!option.disabled) {\n        option.state.checked = this.allChecked;\n      }\n    });\n\n    let checkedOptions = this.options.filter(option => option.state.checked);\n\n    this.selectControl.setValue(checkedOptions);\n  }\n\n  onOpened() {\n    if (this.searchRef) {\n      const input = this.searchRef.nativeElement as HTMLInputElement;\n      input.focus();\n    }\n\n    setTimeout(() => {\n      if (this.virtualScroll) {\n        if (this.selectControl.value && this.selectControl.value.length) {\n          this.virtualScroll.scrollToIndex(\n            this.options.map(option => option.id).indexOf(this.selectControl.value[0].id));\n        } else {\n          this.virtualScroll.scrollToOffset(1);\n        }\n      }\n    }, 100);\n  }\n\n  onClosed() {\n    this.searchControl.reset('', { emitEvent: false });\n    this.options = this.data;\n    this.previousSelected = null;\n  }\n\n  onSelectMultiple( event: MouseEvent, option: SwuiSelectOption ) {\n    event.preventDefault();\n    event.stopPropagation();\n\n    if (option.state) {\n      option.state.checked = !option.state.checked;\n    }\n\n    this.selectControl.patchValue(this.data?.filter(item => item.state?.checked));\n  }\n\n  onSelect( option: SwuiSelectOption | null ) {\n    this.options.forEach(opt => opt.state.checked = opt.id === option?.id);\n\n    this.selectControl.patchValue(coerceArray(option || []));\n  }\n\n  stopPropagation( event: Event ) {\n    event.stopPropagation();\n  }\n\n  protected onDisabledState( disabled: boolean ) {\n    disabled ?\n      this.triggerInputControl.disable({ emitEvent: false }) :\n      this.triggerInputControl.enable({ emitEvent: false });\n  }\n\n  private get enabledData(): SwuiSelectOption[] {\n    return this.data.filter(( { disabled } ) => !disabled);\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,WAAW,EAAEC,qBAAqB,QAAQ,uBAAuB;AAC1E,SAASC,wBAAwB,QAAQ,wBAAwB;AACjE,SACoBC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,SAAS,EAC1GC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAC5B,eAAe;AACtB,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAClF,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAElE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,aAAa,QAAQ,MAAM;AACpC,SAASC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAC7D,SAASC,uBAAuB,QAAQ,uCAAuC;AAG/E,MAAMC,YAAY,GAAG,iBAAiB;AACtC,IAAIC,YAAY,GAAG,CAAC;AAUb,IAAMC,mBAAmB,IAAAC,oBAAA,GAAzB,MAAMD,mBAAoB,SAAQH,uBAAsD;MASzFK,QAAQA,CAAEC,KAAc;IAC1B,MAAMC,WAAW,GAAG9B,qBAAqB,CAAC6B,KAAK,CAAC;IAChD,IAAIC,WAAW,KAAK,IAAI,CAACC,SAAS,EAAE;MAClC,IAAIvB,SAAS,EAAE,IAAI,IAAI,CAACwB,mBAAmB,EAAE;QAC3C,MAAM,IAAIC,KAAK,CAAC,0EAA0E,CAAC;MAC7F;MACA,IAAI,CAACF,SAAS,GAAGD,WAAW;MAC5B,IAAI,CAACI,UAAU,GAAGJ,WAAW,GAAG,EAAE,GAAG,EAAE;IACzC;EACF;EAEA,IAAIF,QAAQA,CAAA;IACV,OAAO,IAAI,CAACG,SAAS;EACvB;MAGII,IAAIA,CAAEC,GAAuB;IAAA,IAAAC,aAAA;IAC/B,IAAI,CAACC,KAAK,GAAGF,GAAG,IAAI,EAAE;IACtB,IAAI,CAACG,OAAO,GAAG,IAAI,CAACD,KAAK;IAEzB,IAAI,IAAI,CAACV,QAAQ,IAAI,CAAC,IAAI,CAACY,gBAAgB,EAAE;MAAA,IAAAC,WAAA;MAC3C,IAAI,CAACC,UAAU,GAAG,EAAAD,WAAA,OAAI,CAACZ,KAAK,cAAAY,WAAA,uBAAVA,WAAA,CAAYE,MAAM,MAAK,IAAI,CAACC,WAAW,CAACD,MAAM;IAClE;IAEA,CAAAN,aAAA,OAAI,CAACE,OAAO,cAAAF,aAAA,eAAZA,aAAA,CAAcQ,OAAO,CAACC,MAAM;MAAA,IAAAC,YAAA;MAAA,OAAID,MAAM,CAACE,KAAK,GAAG;QAC7CC,OAAO,GAAAF,YAAA,GAAE,IAAI,CAAClB,KAAK,cAAAkB,YAAA,uBAAVA,YAAA,CAAYG,QAAQ,CAACJ,MAAM,CAACK,EAAE;OACxC;IAAA,EAAC;IAEF,IAAIC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACd,OAAO,CAAC,IAAI,IAAI,CAACA,OAAO,CAACI,MAAM,EAAE;MACtD,IAAI,CAACW,cAAc,CAACC,IAAI,CAAC,IAAI,CAAChB,OAAO,CAAC;IACxC;EACF;EAEA,IAAIJ,IAAIA,CAAA;IACN,OAAO,IAAI,CAACG,KAAK;EACnB;MAGIT,KAAKA,CAAEO,GAAkC;IAC3C,IAAI,CAACoB,kBAAkB,CAACpB,GAAG,CAAC;EAC9B;EAEA,IAAIP,KAAKA,CAAA;IAAA,IAAA4B,qBAAA,EAAAC,sBAAA;IACP,OAAO,IAAI,CAAC9B,QAAQ,IAAA6B,qBAAA,GAClB,IAAI,CAACE,aAAa,CAAC9B,KAAK,cAAA4B,qBAAA,uBAAxBA,qBAAA,CAA0BrC,GAAG,CAAGwC,CAAmB,IAAMA,CAAC,CAACT,EAAE,CAAC,GAC9D,IAAI,CAACQ,aAAa,CAAC9B,KAAK,MAAA6B,sBAAA,GAAI,IAAI,CAACC,aAAa,CAAC9B,KAAK,CAAC,CAAC,CAAC,cAAA6B,sBAAA,uBAA3BA,sBAAA,CAA6BP,EAAE;EAC/D;EAEA,IAAIU,KAAKA,CAAA;IAAA,IAAAC,sBAAA;IACP,OAAO,GAAAA,sBAAA,GAAC,IAAI,CAACH,aAAa,CAAC9B,KAAK,cAAAiC,sBAAA,eAAxBA,sBAAA,CAA0BnB,MAAM;EAC1C;EAEA,IAAIoB,cAAcA,CAAA;IAChB,IAAIpB,MAAM,GAAG,IAAI,CAACR,IAAI,CAACQ,MAAM;IAC7B,IAAI,CAAC,IAAI,CAACf,QAAQ,IAAI,CAAC,IAAI,CAACoC,kBAAkB,EAAE;MAC9CrB,MAAM,IAAI,CAAC;IACb;IACAA,MAAM,GAAGsB,IAAI,CAACC,KAAK,CAACvB,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,IAAIA,MAAM,GAAG,CAAC;IAClD,IAAIA,MAAM,KAAK,CAAC,EAAE;MAChB,IAAI,IAAI,CAACwB,UAAU,EAAE;QACnBxB,MAAM,IAAI,CAAC;MACb;MACA,IAAI,IAAI,CAACf,QAAQ,EAAE;QACjBe,MAAM,IAAI,CAAC;MACb;IACF;IACA,OAAOA,MAAM,GAAG,IAAI,CAACT,UAAU;EACjC;MAqBIkC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,mBAAmB,CAACxC,KAAK;EACvC;EAQAyC,YAAaC,EAAgB,EAChBC,KAA8B,EACVC,SAAoB,EAC5BC,eAAmC,EAC/CC,iBAAoC,EACnBC,EAAqB,EACrBC,SAA2B;IAEvD,KAAK,CAACN,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAHnC,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,SAAS,GAATA,SAAS;IAhH9B,KAAAC,iBAAiB,GAAG,QAAQ;IAC5B,KAAAX,UAAU,GAAG,KAAK;IAClB,KAAAY,iBAAiB,GAAG,CAAC;IACrB,KAAAC,sBAAsB,GAAG,MAAM;IAC/B,KAAAhB,kBAAkB,GAAG,KAAK;IAC1B,KAAAxB,gBAAgB,GAAG,KAAK;IAwEjC,KAAAE,UAAU,GAAG,KAAK;IAClB,KAAAH,OAAO,GAAuB,EAAE;IAEhC,KAAAL,UAAU,GAAG,EAAE;IAEf,KAAAoB,cAAc,GAAG,IAAIpC,aAAa,CAAqB,CAAC,CAAC;IAEhD,KAAA+D,WAAW,GAAGzD,YAAY;IAC1B,KAAA6C,mBAAmB,GAAG,IAAIzD,kBAAkB,EAAE;IAC9C,KAAAsE,aAAa,GAAG,IAAItE,kBAAkB,EAAE;IACxC,KAAA+C,aAAa,GAAG,IAAI/C,kBAAkB,EAAE;IAMzB,KAAAuC,EAAE,GAAG,GAAG3B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAOxD,KAAAO,mBAAmB,GAAG,KAAK;IAC3B,KAAAD,SAAS,GAAG,KAAK;IACjB,KAAAO,KAAK,GAAuB,EAAE;IAE9B,KAAA6C,gBAAgB,GAAoB,IAAI;EAWhD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACF,aAAa,CAACG,YAAY,CAACC,IAAI,CAClCnE,MAAM,CAASgB,IAAI,IAAI,IAAI,CAACgC,UAAU,KAAKhC,IAAI,CAACQ,MAAM,KAAK,IAAI,CAACoC,iBAAiB,IAAI,CAAC,CAAC,IAAI5C,IAAI,CAACQ,MAAM,KAAK,CAAC,CAAC,CAAC,EAC9GvB,GAAG,CAAiBmE,YAAY,IAAIA,YAAY,CAACC,WAAW,EAAE,CAAC,EAC/DlE,SAAS,CAAC,IAAI,CAACmE,UAAU,CAAC,CAC3B,CAACC,SAAS,CAACC,MAAM,IAAG;MACnB,IAAI,CAACR,gBAAgB,GAAG,IAAI,CAACvD,QAAQ,IAAI+D,MAAM,GAAG,IAAI,CAAChC,aAAa,CAAC9B,KAAK,GAAG,IAAI;MACjF,IAAI,CAACU,OAAO,GAAG,IAAI,CAACJ,IAAI,CAAChB,MAAM,CAAC2B,MAAM,IAAG;QACvC,OAAOA,MAAM,CAAC8C,IAAI,IAAI9C,MAAM,CAAC8C,IAAI,CAACJ,WAAW,EAAE,CAACK,OAAO,CAACF,MAAM,CAAC,GAAG,CAAC,CAAC;MACtE,CAAC,CAAC;MACF,IAAI,CAACf,EAAE,CAACkB,YAAY,EAAE;IACxB,CAAC,CAAC;IAEF,IAAI,CAACnC,aAAa,CAAC0B,YAAY,CAACC,IAAI,CAClClE,GAAG,CAAyCgB,GAAG,IAAG;MAChD,IAAI,IAAI,CAACR,QAAQ,IAAIwB,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC8B,gBAAgB,CAAC,IAAI/B,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,EAAE;QAC/E,MAAM+C,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAChE,MAAM,CAACgC,EAAE,IAAI,CAAC,IAAI,CAACZ,OAAO,CAACwD,IAAI,CAACjD,MAAM,IAAIA,MAAM,CAACK,EAAE,KAAKA,EAAE,CAAC,CAAC;QAC3G,MAAM6C,MAAM,GAAG,IAAI,CAACpD,WAAW,CAACzB,MAAM,CAAC,CAAE;UAAEgC;QAAE,CAAE,KAAMgC,gBAAgB,CAACY,IAAI,CAACE,IAAI,IAAIA,IAAI,KAAK9C,EAAE,CAAC,CAAC;QAChG,OAAO,CAAC,GAAG6C,MAAM,EAAE,GAAG5D,GAAG,CAAC;MAC5B;MAEA,OAAOA,GAAG;IACZ,CAAC,CAAC,EACFhB,GAAG,CAA+BgB,GAAG,IAAIA,GAAG,CAAChB,GAAG,CAAC6E,IAAI,IAAIA,IAAI,GAAGA,IAAI,CAAC9C,EAAE,GAAG,EAAE,CAAC,CAAC,EAC9E7B,SAAS,CAAC,IAAI,CAACmE,UAAU,CAAC,CAC3B,CAACC,SAAS,CAACtD,GAAG,IAAG;MAChB,IAAI,CAAC8D,eAAe,CAAC9D,GAAG,CAAC;MAEzB,IAAI,CAAC+D,QAAQ,CAAC,IAAI,CAACvE,QAAQ,GAAGQ,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;MAEnD,IAAI,IAAI,CAACR,QAAQ,IAAI,CAAC,IAAI,CAACY,gBAAgB,EAAE;QAC3C,IAAI,CAACE,UAAU,GAAG,IAAI,CAACE,WAAW,CAACwD,KAAK,CAAC,CAAC;UAACjD;QAAE,CAAC;UAAA,IAAAkD,YAAA;UAAA,QAAAA,YAAA,GAAK,IAAI,CAACxE,KAAK,cAAAwE,YAAA,uBAAVA,YAAA,CAAYnD,QAAQ,CAACC,EAAE,CAAC;QAAA,EAAC;MAC9E;MACA,IAAI,CAACyB,EAAE,CAAC0B,aAAa,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,CAACvE,mBAAmB,GAAG,IAAI;EACjC;EAEAkE,eAAeA,CAAE9D,GAAa,EAAEG,OAA4B;IAC1D,IAAIA,OAAO,EAAE;MACX,IAAI,CAACiE,wBAAwB,CAACpE,GAAG,EAAEG,OAAO,CAAC;IAC7C,CAAC,MAAM;MACL,IAAI,CAACe,cAAc,CAChBgC,IAAI,CACHjE,IAAI,CAAC,CAAC,CAAC,CACR,CACAqE,SAAS,CACRvD,IAAI,IAAG;QACL,IAAI,CAACqE,wBAAwB,CAACpE,GAAG,EAAED,IAAI,CAAC;MAC1C,CAAC,CAAC;IACR;EACF;EAEAqE,wBAAwBA,CAAEpE,GAAa,EAAEG,OAA2B;IAClE,IAAIO,MAAM,GAAGP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACvD,EAAE,KAAKf,GAAG,CAAC,CAAC,CAAC,CAAC;IAEpD,IAAIwD,IAAI,GAAG9C,MAAM,GACf,IAAI,CAAC+B,SAAS,CAAC8B,OAAO,CAAC7D,MAAM,CAAC8C,IAAI,CAAC,GACnCxD,GAAG,CAAC,CAAC,CAAC;IAER,IAAIwE,YAAY,GAAG,CAAAxE,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEO,MAAM,IAAG,CAAC,GAChC,GAAGiD,IAAI,MAAMxD,GAAG,CAACO,MAAM,GAAG,CAAC,IAAI,IAAI,CAACkC,SAAS,CAAC8B,OAAO,CAAC,UAAU,CAAC,GAAG,GACpEf,IAAI;IAEN,IAAI,CAACvB,mBAAmB,CAACwC,UAAU,CAACD,YAAY,CAAC;EACnD;EAEAE,gBAAgBA,CAAEC,KAAiB;IACjCA,KAAK,CAACC,eAAe,EAAE;IAEvB,IAAI,IAAI,CAACxC,KAAK,IAAI,IAAI,CAACyC,OAAO,IAAKF,KAAK,CAACG,MAAkB,CAACC,OAAO,CAAC3B,WAAW,EAAE,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC4B,QAAQ,EAAE;MAC/G,IAAI,CAAC5C,KAAK,CAAC6C,aAAa,CAACC,KAAK,EAAE;MAChC,IAAI,CAACL,OAAO,CAACM,QAAQ,EAAE;IACzB;EACF;EAEAC,UAAUA,CAAEpF,GAAkC;IAC5C,IAAI,CAACoB,kBAAkB,CAACpB,GAAG,CAAC;EAC9B;EAEAoB,kBAAkBA,CAAEpB,GAAkC;IACpD,IAAI,CAACkB,cAAc,CAChBgC,IAAI,CACHjE,IAAI,CAAC,CAAC,CAAC,CACR,CACAqE,SAAS,CACRnD,OAAO,IAAG;MACR,IAAI,IAAI,CAACX,QAAQ,IAAI,CAAC,IAAI,CAACY,gBAAgB,EAAE;QAC3C,IAAI,CAACE,UAAU,GAAGU,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,IAAIA,GAAG,CAACO,MAAM,KAAK,IAAI,CAACC,WAAW,CAACD,MAAM;MAChF;MAEA,MAAMqD,MAAM,GAAGjG,WAAW,CAACqC,GAAG,IAAI,EAAE,CAAC;MAErCG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEM,OAAO,CAACC,MAAM,IAAIA,MAAM,CAACE,KAAK,GAAG;QACxCC,OAAO,EAAE+C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE9C,QAAQ,CAACJ,MAAM,CAACK,EAAE;OACpC,CAAC;MAEF,MAAMtB,KAAK,GAAGU,OAAO,CAACpB,MAAM,CAACuF,GAAG,IAAIV,MAAM,CAAC9C,QAAQ,CAACwD,GAAG,CAACvD,EAAE,CAAC,CAAC;MAE5D,IAAI,CAACQ,aAAa,CAACkD,UAAU,CAAChF,KAAK,EAAE;QAAE4F,SAAS,EAAE;MAAK,CAAE,CAAC;MAC1D,IAAI,CAACvB,eAAe,CAACF,MAAM,EAAEzD,OAAO,CAAC;MAErC,IAAI,CAACqC,EAAE,CAAC0B,aAAa,EAAE;IACzB,CAAC,CACF;EACL;EAEAoB,SAASA,CAAEX,KAAkB;IAC3BA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEY,cAAc,EAAE;IACvBZ,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,eAAe,EAAE;IAExB,IAAI,CAACtE,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,OAAO,CAACM,OAAO,CAACC,MAAM,IAAG;MAC5B,IAAI,CAACA,MAAM,CAACsE,QAAQ,EAAE;QACpBtE,MAAM,CAACE,KAAK,CAACC,OAAO,GAAG,IAAI,CAACP,UAAU;MACxC;IACF,CAAC,CAAC;IAEF,IAAIkF,cAAc,GAAG,IAAI,CAACrF,OAAO,CAACpB,MAAM,CAAC2B,MAAM,IAAIA,MAAM,CAACE,KAAK,CAACC,OAAO,CAAC;IAExE,IAAI,CAACU,aAAa,CAACkE,QAAQ,CAACD,cAAc,CAAC;EAC7C;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,SAAS,EAAE;MAClB,MAAMC,KAAK,GAAG,IAAI,CAACD,SAAS,CAACV,aAAiC;MAC9DW,KAAK,CAACV,KAAK,EAAE;IACf;IAEAW,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,aAAa,EAAE;QACtB,IAAI,IAAI,CAACvE,aAAa,CAAC9B,KAAK,IAAI,IAAI,CAAC8B,aAAa,CAAC9B,KAAK,CAACc,MAAM,EAAE;UAC/D,IAAI,CAACuF,aAAa,CAACC,aAAa,CAC9B,IAAI,CAAC5F,OAAO,CAACnB,GAAG,CAAC0B,MAAM,IAAIA,MAAM,CAACK,EAAE,CAAC,CAAC0C,OAAO,CAAC,IAAI,CAAClC,aAAa,CAAC9B,KAAK,CAAC,CAAC,CAAC,CAACsB,EAAE,CAAC,CAAC;QAClF,CAAC,MAAM;UACL,IAAI,CAAC+E,aAAa,CAACE,cAAc,CAAC,CAAC,CAAC;QACtC;MACF;IACF,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACnD,aAAa,CAACoD,KAAK,CAAC,EAAE,EAAE;MAAEb,SAAS,EAAE;IAAK,CAAE,CAAC;IAClD,IAAI,CAAClF,OAAO,GAAG,IAAI,CAACJ,IAAI;IACxB,IAAI,CAACgD,gBAAgB,GAAG,IAAI;EAC9B;EAEAoD,gBAAgBA,CAAExB,KAAiB,EAAEjE,MAAwB;IAAA,IAAA0F,UAAA;IAC3DzB,KAAK,CAACY,cAAc,EAAE;IACtBZ,KAAK,CAACC,eAAe,EAAE;IAEvB,IAAIlE,MAAM,CAACE,KAAK,EAAE;MAChBF,MAAM,CAACE,KAAK,CAACC,OAAO,GAAG,CAACH,MAAM,CAACE,KAAK,CAACC,OAAO;IAC9C;IAEA,IAAI,CAACU,aAAa,CAACkD,UAAU,EAAA2B,UAAA,GAAC,IAAI,CAACrG,IAAI,cAAAqG,UAAA,uBAATA,UAAA,CAAWrH,MAAM,CAAC8E,IAAI;MAAA,IAAAwC,WAAA;MAAA,QAAAA,WAAA,GAAIxC,IAAI,CAACjD,KAAK,cAAAyF,WAAA,uBAAVA,WAAA,CAAYxF,OAAO;IAAA,EAAC,CAAC;EAC/E;EAEAyF,QAAQA,CAAE5F,MAA+B;IACvC,IAAI,CAACP,OAAO,CAACM,OAAO,CAAC6D,GAAG,IAAIA,GAAG,CAAC1D,KAAK,CAACC,OAAO,GAAGyD,GAAG,CAACvD,EAAE,MAAKL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,EAAE,EAAC;IAEtE,IAAI,CAACQ,aAAa,CAACkD,UAAU,CAAC9G,WAAW,CAAC+C,MAAM,IAAI,EAAE,CAAC,CAAC;EAC1D;EAEAkE,eAAeA,CAAED,KAAY;IAC3BA,KAAK,CAACC,eAAe,EAAE;EACzB;EAEU2B,eAAeA,CAAEvB,QAAiB;IAC1CA,QAAQ,GACN,IAAI,CAAC/C,mBAAmB,CAACuE,OAAO,CAAC;MAAEnB,SAAS,EAAE;IAAK,CAAE,CAAC,GACtD,IAAI,CAACpD,mBAAmB,CAACwE,MAAM,CAAC;MAAEpB,SAAS,EAAE;IAAK,CAAE,CAAC;EACzD;EAEA,IAAY7E,WAAWA,CAAA;IACrB,OAAO,IAAI,CAACT,IAAI,CAAChB,MAAM,CAAC,CAAE;MAAEiG;IAAQ,CAAE,KAAM,CAACA,QAAQ,CAAC;EACxD;;;;;;;;UA5Lc3G;EAAQ;IAAAqI,IAAA,EAAIpI;EAAI;AAAA,G;;;UAChBD;EAAQ;AAAA,G;;;;;;;;UA7GrBF;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UAgBLA;EAAK;;UAsBLA;EAAK;;UA4CLI,SAAS;IAAAoI,IAAA,GAAC,SAAS;EAAA;;UACnBpI,SAAS;IAAAoI,IAAA,GAAC,QAAQ;EAAA;;UAClBpI,SAAS;IAAAoI,IAAA,GAAC9I,wBAAwB,EAAE;MAAE+I,MAAM,EAAE;IAAK,CAAE;EAAA;;UAErD1I;EAAW;;UAEXA,WAAW;IAAAyI,IAAA,GAAC,gBAAgB;EAAA;;AAhGlBrH,mBAAmB,GAAAuH,UAAA,EAR/B7I,SAAS,CAAC;EACP8I,QAAQ,EAAE,iBAAiB;EAC3BC,QAAA,EAAAC,oBAA2C;EAE3CC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAEtI,mBAAmB;IAAEuI,WAAW,EAAE7H;EAAmB,CAAE,CAAC;EAC/E8H,eAAe,EAAEtJ,uBAAuB,CAACuJ,MAAM;EAC/CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWhI,mBAAmB,CA0S/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}