{"ast": null, "code": "import { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nvar Scheduler = function () {\n  function Scheduler(schedulerActionCtor, now) {\n    if (now === void 0) {\n      now = Scheduler.now;\n    }\n    this.schedulerActionCtor = schedulerActionCtor;\n    this.now = now;\n  }\n  Scheduler.prototype.schedule = function (work, delay, state) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return new this.schedulerActionCtor(this, work).schedule(state, delay);\n  };\n  Scheduler.now = dateTimestampProvider.now;\n  return Scheduler;\n}();\nexport { Scheduler };\n//# sourceMappingURL=Scheduler.js.map", "map": {"version": 3, "names": ["dateTimestampProvider", "Scheduler", "schedulerActionCtor", "now", "prototype", "schedule", "work", "delay", "state"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/Scheduler.js"], "sourcesContent": ["import { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nvar Scheduler = (function () {\n    function Scheduler(schedulerActionCtor, now) {\n        if (now === void 0) { now = Scheduler.now; }\n        this.schedulerActionCtor = schedulerActionCtor;\n        this.now = now;\n    }\n    Scheduler.prototype.schedule = function (work, delay, state) {\n        if (delay === void 0) { delay = 0; }\n        return new this.schedulerActionCtor(this, work).schedule(state, delay);\n    };\n    Scheduler.now = dateTimestampProvider.now;\n    return Scheduler;\n}());\nexport { Scheduler };\n//# sourceMappingURL=Scheduler.js.map"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,mCAAmC;AACzE,IAAIC,SAAS,GAAI,YAAY;EACzB,SAASA,SAASA,CAACC,mBAAmB,EAAEC,GAAG,EAAE;IACzC,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAAEA,GAAG,GAAGF,SAAS,CAACE,GAAG;IAAE;IAC3C,IAAI,CAACD,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;EACAF,SAAS,CAACG,SAAS,CAACC,QAAQ,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;IACzD,IAAID,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,OAAO,IAAI,IAAI,CAACL,mBAAmB,CAAC,IAAI,EAAEI,IAAI,CAAC,CAACD,QAAQ,CAACG,KAAK,EAAED,KAAK,CAAC;EAC1E,CAAC;EACDN,SAAS,CAACE,GAAG,GAAGH,qBAAqB,CAACG,GAAG;EACzC,OAAOF,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,SAASA,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}