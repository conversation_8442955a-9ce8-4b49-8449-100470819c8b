{"ast": null, "code": "export var isArrayLike = function (x) {\n  return x && typeof x.length === 'number' && typeof x !== 'function';\n};\n//# sourceMappingURL=isArrayLike.js.map", "map": {"version": 3, "names": ["isArrayLike", "x", "length"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/isArrayLike.js"], "sourcesContent": ["export var isArrayLike = (function (x) { return x && typeof x.length === 'number' && typeof x !== 'function'; });\n//# sourceMappingURL=isArrayLike.js.map"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAI,SAAAA,CAAUC,CAAC,EAAE;EAAE,OAAOA,CAAC,IAAI,OAAOA,CAAC,CAACC,MAAM,KAAK,QAAQ,IAAI,OAAOD,CAAC,KAAK,UAAU;AAAE,CAAE;AAChH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}