{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _AriaDescriber, _FocusTrapManager, _ConfigurableFocusTrapFactory;\nexport { C as CdkMonitorFocus, d as FOCUS_MONITOR_DEFAULT_OPTIONS, F as FocusMonitor, c as FocusMonitorDetectionMode, a as INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, b as INPUT_MODALITY_DETECTOR_OPTIONS, I as InputModalityDetector } from './focus-monitor-e2l_RpN3.mjs';\nimport { a as FocusTrap, I as InteractivityChecker } from './a11y-module-BYox5gpI.mjs';\nexport { A as A11yModule, d as CdkAriaLive, C as CdkTrapFocus, F as FocusTrapFactory, b as HighContrastMode, H as HighContrastModeDetector, c as IsFocusableConfig, g as LIVE_ANNOUNCER_DEFAULT_OPTIONS, e as LIVE_ANNOUNCER_ELEMENT_TOKEN, f as LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, L as LiveAnnouncer } from './a11y-module-BYox5gpI.mjs';\nexport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, InjectionToken, NgZone, Injector } from '@angular/core';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nexport { A as ActiveDescendantKeyManager } from './activedescendant-key-manager-DC3-fwQI.mjs';\nexport { F as FocusKeyManager } from './focus-key-manager-C1rAQJ5z.mjs';\nexport { L as ListKeyManager } from './list-key-manager-CyOIXo8P.mjs';\nimport { Subject } from 'rxjs';\nimport { T as TREE_KEY_MANAGER } from './tree-key-manager-KnCoIkIC.mjs';\nexport { b as TREE_KEY_MANAGER_FACTORY, c as TREE_KEY_MANAGER_FACTORY_PROVIDER, a as TreeKeyManager } from './tree-key-manager-KnCoIkIC.mjs';\nexport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport 'rxjs/operators';\nimport './keycodes-CpHkExLC.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport './element-x4z00URv.mjs';\nimport './breakpoints-observer-CljOfYGy.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './typeahead-9ZW4Dtsf.mjs';\nimport './keycodes.mjs';\nimport './coercion/private.mjs';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  var _attrValue$match;\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return (_attrValue$match = attrValue === null || attrValue === void 0 ? void 0 : attrValue.match(/\\S+/g)) !== null && _attrValue$match !== void 0 ? _attrValue$match : [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n  constructor() {\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    /** Map of all registered message elements that have been placed into the document. */\n    _defineProperty(this, \"_messageRegistry\", new Map());\n    /** Container for all registered messages. */\n    _defineProperty(this, \"_messagesContainer\", null);\n    /** Unique ID for the service. */\n    _defineProperty(this, \"_id\", `${nextId++}`);\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n  removeDescription(hostElement, message, role) {\n    var _this$_messagesContai;\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n    if (((_this$_messagesContai = this._messagesContainer) === null || _this$_messagesContai === void 0 ? void 0 : _this$_messagesContai.childNodes.length) === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    var _this$_messagesContai2;\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    (_this$_messagesContai2 = this._messagesContainer) === null || _this$_messagesContai2 === void 0 || _this$_messagesContai2.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n    this._createMessagesContainer();\n    this._messagesContainer.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n  _deleteMessageElement(key) {\n    var _this$_messageRegistr;\n    (_this$_messageRegistr = this._messageRegistry.get(key)) === null || _this$_messageRegistr === void 0 || (_this$_messageRegistr = _this$_messageRegistr.messageElement) === null || _this$_messageRegistr === void 0 || _this$_messageRegistr.remove();\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n    const messagesContainer = this._document.createElement('div');\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n    if (!this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n}\n_AriaDescriber = AriaDescriber;\n_defineProperty(AriaDescriber, \"\\u0275fac\", function _AriaDescriber_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AriaDescriber)();\n});\n_defineProperty(AriaDescriber, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _AriaDescriber,\n  factory: _AriaDescriber.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n  constructor() {\n    _defineProperty(this, \"_isNoopTreeKeyManager\", true);\n    // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n    // implementation that does not emit to streams.\n    _defineProperty(this, \"change\", new Subject());\n  }\n  destroy() {\n    this.change.complete();\n  }\n  onKeydown() {\n    // noop\n  }\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  focusItem() {\n    // noop\n  }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n  return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY\n};\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    _defineProperty(this, \"_focusTrapManager\", void 0);\n    _defineProperty(this, \"_inertStrategy\", void 0);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  constructor() {\n    /** Focus event handler. */\n    _defineProperty(this, \"_listener\", null);\n  }\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    var _target$closest;\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !((_target$closest = target.closest) !== null && _target$closest !== void 0 && _target$closest.call(target, 'div.cdk-overlay-pane'))) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n  constructor() {\n    // A stack of the FocusTraps on the page. Only the FocusTrap at the\n    // top of the stack is active.\n    _defineProperty(this, \"_focusTrapStack\", []);\n  }\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap) {\n    focusTrap._disable();\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n}\n_FocusTrapManager = FocusTrapManager;\n_defineProperty(FocusTrapManager, \"\\u0275fac\", function _FocusTrapManager_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FocusTrapManager)();\n});\n_defineProperty(FocusTrapManager, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _FocusTrapManager,\n  factory: _FocusTrapManager.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n  constructor() {\n    _defineProperty(this, \"_checker\", inject(InteractivityChecker));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_focusTrapManager\", inject(FocusTrapManager));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_inertStrategy\", void 0);\n    _defineProperty(this, \"_injector\", inject(Injector));\n    const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, {\n      optional: true\n    });\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n  }\n}\n_ConfigurableFocusTrapFactory = ConfigurableFocusTrapFactory;\n_defineProperty(ConfigurableFocusTrapFactory, \"\\u0275fac\", function _ConfigurableFocusTrapFactory_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ConfigurableFocusTrapFactory)();\n});\n_defineProperty(ConfigurableFocusTrapFactory, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _ConfigurableFocusTrapFactory,\n  factory: _ConfigurableFocusTrapFactory.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_TRAP_INERT_STRATEGY, FocusTrap, InteractivityChecker, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId };\n//# sourceMappingURL=a11y.mjs.map", "map": {"version": 3, "names": ["C", "CdkMonitorFocus", "d", "FOCUS_MONITOR_DEFAULT_OPTIONS", "F", "FocusMonitor", "c", "FocusMonitorDetectionMode", "a", "INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS", "b", "INPUT_MODALITY_DETECTOR_OPTIONS", "I", "InputModalityDetector", "FocusTrap", "InteractivityChecker", "A", "A11yModule", "CdkAriaLive", "CdkTrapFocus", "FocusTrapFactory", "HighContrastMode", "H", "HighContrastModeDetector", "IsFocusableConfig", "g", "LIVE_ANNOUNCER_DEFAULT_OPTIONS", "e", "LIVE_ANNOUNCER_ELEMENT_TOKEN", "f", "LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY", "L", "LiveAnnouncer", "_", "_IdGenerator", "DOCUMENT", "i0", "inject", "APP_ID", "Injectable", "InjectionToken", "NgZone", "Injector", "P", "Platform", "_CdkPrivateStyleLoader", "_VisuallyHiddenLoader", "ActiveDescendantKeyManager", "FocusKeyManager", "ListKeyManager", "Subject", "T", "TREE_KEY_MANAGER", "TREE_KEY_MANAGER_FACTORY", "TREE_KEY_MANAGER_FACTORY_PROVIDER", "TreeKeyManager", "i", "isFakeMousedownFromScreenReader", "isFakeTouchstartFromScreenReader", "ID_DELIMITER", "addAriaReferencedId", "el", "attr", "id", "ids", "getAriaReferenceIds", "trim", "some", "existingId", "push", "setAttribute", "join", "removeAriaReferencedId", "filteredIds", "filter", "val", "length", "removeAttribute", "_attrValue$match", "attrValue", "getAttribute", "match", "MESSAGES_CONTAINER_ID", "CDK_DESCRIBEDBY_ID_PREFIX", "CDK_DESCRIBEDBY_HOST_ATTRIBUTE", "nextId", "AriaDescriber", "constructor", "_defineProperty", "Map", "load", "_id", "describe", "hostElement", "message", "role", "_canBeDescribed", "key", "<PERSON><PERSON><PERSON>", "setMessageId", "_messageRegistry", "set", "messageElement", "referenceCount", "has", "_createMessageElement", "_isElementDescribedByMessage", "_addMessageReference", "removeDescription", "_this$_messagesContai", "_isElementNode", "_removeMessageReference", "registeredMessage", "get", "_deleteMessageElement", "_messagesContainer", "childNodes", "remove", "ngOnDestroy", "_this$_messagesContai2", "describedE<PERSON>s", "_document", "querySelectorAll", "_removeCdkDescribedByReferenceIds", "clear", "createElement", "textContent", "_createMessagesContainer", "append<PERSON><PERSON><PERSON>", "_this$_messageRegistr", "delete", "containerClassName", "serverContainers", "messagesContainer", "style", "visibility", "classList", "add", "_platform", "<PERSON><PERSON><PERSON><PERSON>", "body", "element", "originalReferenceIds", "indexOf", "referenceIds", "messageId", "trimmedMessage", "aria<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "_AriaDescriber", "_AriaDescriber_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "serviceId", "NoopTreeKeyManager", "destroy", "change", "complete", "onKeydown", "getActiveItemIndex", "getActiveItem", "focusItem", "NOOP_TREE_KEY_MANAGER_FACTORY", "NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER", "provide", "useFactory", "ConfigurableFocusTrap", "enabled", "_enabled", "value", "_focusTrapManager", "register", "deregister", "_element", "_checker", "_ngZone", "_inertStrategy", "config", "injector", "defer", "_enable", "preventFocus", "toggleAnchors", "_disable", "allowFocus", "EventListenerFocusTrapInertStrategy", "focusTrap", "_listener", "removeEventListener", "_trapFocus", "runOutsideAngular", "addEventListener", "event", "_target$closest", "target", "focusTrapRoot", "contains", "closest", "call", "setTimeout", "activeElement", "focusFirstTabbableElement", "FOCUS_TRAP_INERT_STRATEGY", "FocusTrapManager", "_focusTrapStack", "ft", "stack", "splice", "_FocusTrapManager", "_FocusTrapManager_Factory", "ConfigurableFocusTrapFactory", "inertStrategy", "optional", "create", "configObject", "_injector", "_ConfigurableFocusTrapFactory", "_ConfigurableFocusTrapFactory_Factory"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/a11y.mjs"], "sourcesContent": ["export { C as CdkMonitorFocus, d as FOCUS_MONITOR_DEFAULT_OPTIONS, F as FocusMonitor, c as FocusMonitorDetectionMode, a as INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, b as INPUT_MODALITY_DETECTOR_OPTIONS, I as InputModalityDetector } from './focus-monitor-e2l_RpN3.mjs';\nimport { a as FocusTrap, I as InteractivityChecker } from './a11y-module-BYox5gpI.mjs';\nexport { A as A11yModule, d as CdkAriaLive, C as CdkTrapFocus, F as FocusTrapFactory, b as HighContrastMode, H as HighContrastModeDetector, c as IsFocusableConfig, g as LIVE_ANNOUNCER_DEFAULT_OPTIONS, e as LIVE_ANNOUNCER_ELEMENT_TOKEN, f as LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, L as LiveAnnouncer } from './a11y-module-BYox5gpI.mjs';\nexport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, InjectionToken, NgZone, Injector } from '@angular/core';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nexport { A as ActiveDescendantKeyManager } from './activedescendant-key-manager-DC3-fwQI.mjs';\nexport { F as FocusKeyManager } from './focus-key-manager-C1rAQJ5z.mjs';\nexport { L as ListKeyManager } from './list-key-manager-CyOIXo8P.mjs';\nimport { Subject } from 'rxjs';\nimport { T as TREE_KEY_MANAGER } from './tree-key-manager-KnCoIkIC.mjs';\nexport { b as TREE_KEY_MANAGER_FACTORY, c as TREE_KEY_MANAGER_FACTORY_PROVIDER, a as TreeKeyManager } from './tree-key-manager-KnCoIkIC.mjs';\nexport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport 'rxjs/operators';\nimport './keycodes-CpHkExLC.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport './element-x4z00URv.mjs';\nimport './breakpoints-observer-CljOfYGy.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './typeahead-9ZW4Dtsf.mjs';\nimport './keycodes.mjs';\nimport './coercion/private.mjs';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    id = id.trim();\n    if (ids.some(existingId => existingId.trim() === id)) {\n        return;\n    }\n    ids.push(id);\n    el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    id = id.trim();\n    const filteredIds = ids.filter(val => val !== id);\n    if (filteredIds.length) {\n        el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n    }\n    else {\n        el.removeAttribute(attr);\n    }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n    // Get string array of all individual ids (whitespace delimited) in the attribute value\n    const attrValue = el.getAttribute(attr);\n    return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT);\n    /** Map of all registered message elements that have been placed into the document. */\n    _messageRegistry = new Map();\n    /** Container for all registered messages. */\n    _messagesContainer = null;\n    /** Unique ID for the service. */\n    _id = `${nextId++}`;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n        this._id = inject(APP_ID) + '-' + nextId++;\n    }\n    describe(hostElement, message, role) {\n        if (!this._canBeDescribed(hostElement, message)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (typeof message !== 'string') {\n            // We need to ensure that the element has an ID.\n            setMessageId(message, this._id);\n            this._messageRegistry.set(key, { messageElement: message, referenceCount: 0 });\n        }\n        else if (!this._messageRegistry.has(key)) {\n            this._createMessageElement(message, role);\n        }\n        if (!this._isElementDescribedByMessage(hostElement, key)) {\n            this._addMessageReference(hostElement, key);\n        }\n    }\n    removeDescription(hostElement, message, role) {\n        if (!message || !this._isElementNode(hostElement)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (this._isElementDescribedByMessage(hostElement, key)) {\n            this._removeMessageReference(hostElement, key);\n        }\n        // If the message is a string, it means that it's one that we created for the\n        // consumer so we can remove it safely, otherwise we should leave it in place.\n        if (typeof message === 'string') {\n            const registeredMessage = this._messageRegistry.get(key);\n            if (registeredMessage && registeredMessage.referenceCount === 0) {\n                this._deleteMessageElement(key);\n            }\n        }\n        if (this._messagesContainer?.childNodes.length === 0) {\n            this._messagesContainer.remove();\n            this._messagesContainer = null;\n        }\n    }\n    /** Unregisters all created message elements and removes the message container. */\n    ngOnDestroy() {\n        const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n        for (let i = 0; i < describedElements.length; i++) {\n            this._removeCdkDescribedByReferenceIds(describedElements[i]);\n            describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n        }\n        this._messagesContainer?.remove();\n        this._messagesContainer = null;\n        this._messageRegistry.clear();\n    }\n    /**\n     * Creates a new element in the visually hidden message container element with the message\n     * as its content and adds it to the message registry.\n     */\n    _createMessageElement(message, role) {\n        const messageElement = this._document.createElement('div');\n        setMessageId(messageElement, this._id);\n        messageElement.textContent = message;\n        if (role) {\n            messageElement.setAttribute('role', role);\n        }\n        this._createMessagesContainer();\n        this._messagesContainer.appendChild(messageElement);\n        this._messageRegistry.set(getKey(message, role), { messageElement, referenceCount: 0 });\n    }\n    /** Deletes the message element from the global messages container. */\n    _deleteMessageElement(key) {\n        this._messageRegistry.get(key)?.messageElement?.remove();\n        this._messageRegistry.delete(key);\n    }\n    /** Creates the global container for all aria-describedby messages. */\n    _createMessagesContainer() {\n        if (this._messagesContainer) {\n            return;\n        }\n        const containerClassName = 'cdk-describedby-message-container';\n        const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n        for (let i = 0; i < serverContainers.length; i++) {\n            // When going from the server to the client, we may end up in a situation where there's\n            // already a container on the page, but we don't have a reference to it. Clear the\n            // old container so we don't get duplicates. Doing this, instead of emptying the previous\n            // container, should be slightly faster.\n            serverContainers[i].remove();\n        }\n        const messagesContainer = this._document.createElement('div');\n        // We add `visibility: hidden` in order to prevent text in this container from\n        // being searchable by the browser's Ctrl + F functionality.\n        // Screen-readers will still read the description for elements with aria-describedby even\n        // when the description element is not visible.\n        messagesContainer.style.visibility = 'hidden';\n        // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n        // the description element doesn't impact page layout.\n        messagesContainer.classList.add(containerClassName);\n        messagesContainer.classList.add('cdk-visually-hidden');\n        if (!this._platform.isBrowser) {\n            messagesContainer.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(messagesContainer);\n        this._messagesContainer = messagesContainer;\n    }\n    /** Removes all cdk-describedby messages that are hosted through the element. */\n    _removeCdkDescribedByReferenceIds(element) {\n        // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n        const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n        element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n    }\n    /**\n     * Adds a message reference to the element using aria-describedby and increments the registered\n     * message's reference count.\n     */\n    _addMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        // Add the aria-describedby reference and set the\n        // describedby_host attribute to mark the element.\n        addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n        registeredMessage.referenceCount++;\n    }\n    /**\n     * Removes a message reference from the element using aria-describedby\n     * and decrements the registered message's reference count.\n     */\n    _removeMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        registeredMessage.referenceCount--;\n        removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    /** Returns true if the element has been described by the provided message ID. */\n    _isElementDescribedByMessage(element, key) {\n        const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n        const registeredMessage = this._messageRegistry.get(key);\n        const messageId = registeredMessage && registeredMessage.messageElement.id;\n        return !!messageId && referenceIds.indexOf(messageId) != -1;\n    }\n    /** Determines whether a message can be described on a particular element. */\n    _canBeDescribed(element, message) {\n        if (!this._isElementNode(element)) {\n            return false;\n        }\n        if (message && typeof message === 'object') {\n            // We'd have to make some assumptions about the description element's text, if the consumer\n            // passed in an element. Assume that if an element is passed in, the consumer has verified\n            // that it can be used as a description.\n            return true;\n        }\n        const trimmedMessage = message == null ? '' : `${message}`.trim();\n        const ariaLabel = element.getAttribute('aria-label');\n        // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n        // element, because screen readers will end up reading out the same text twice in a row.\n        return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n    }\n    /** Checks whether a node is an Element node. */\n    _isElementNode(element) {\n        return element.nodeType === this._document.ELEMENT_NODE;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: AriaDescriber, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: AriaDescriber, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: AriaDescriber, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n    return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n    if (!element.id) {\n        element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n    }\n}\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n    _isNoopTreeKeyManager = true;\n    // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n    // implementation that does not emit to streams.\n    change = new Subject();\n    destroy() {\n        this.change.complete();\n    }\n    onKeydown() {\n        // noop\n    }\n    getActiveItemIndex() {\n        // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n        // the active item.\n        return null;\n    }\n    getActiveItem() {\n        // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n        // the active item.\n        return null;\n    }\n    focusItem() {\n        // noop\n    }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n    return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n    provide: TREE_KEY_MANAGER,\n    useFactory: NOOP_TREE_KEY_MANAGER_FACTORY,\n};\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n    _focusTrapManager;\n    _inertStrategy;\n    /** Whether the FocusTrap is enabled. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._enabled) {\n            this._focusTrapManager.register(this);\n        }\n        else {\n            this._focusTrapManager.deregister(this);\n        }\n    }\n    constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n        super(_element, _checker, _ngZone, _document, config.defer, injector);\n        this._focusTrapManager = _focusTrapManager;\n        this._inertStrategy = _inertStrategy;\n        this._focusTrapManager.register(this);\n    }\n    /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n    destroy() {\n        this._focusTrapManager.deregister(this);\n        super.destroy();\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _enable() {\n        this._inertStrategy.preventFocus(this);\n        this.toggleAnchors(true);\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _disable() {\n        this._inertStrategy.allowFocus(this);\n        this.toggleAnchors(false);\n    }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n    /** Focus event handler. */\n    _listener = null;\n    /** Adds a document event listener that keeps focus inside the FocusTrap. */\n    preventFocus(focusTrap) {\n        // Ensure there's only one listener per document\n        if (this._listener) {\n            focusTrap._document.removeEventListener('focus', this._listener, true);\n        }\n        this._listener = (e) => this._trapFocus(focusTrap, e);\n        focusTrap._ngZone.runOutsideAngular(() => {\n            focusTrap._document.addEventListener('focus', this._listener, true);\n        });\n    }\n    /** Removes the event listener added in preventFocus. */\n    allowFocus(focusTrap) {\n        if (!this._listener) {\n            return;\n        }\n        focusTrap._document.removeEventListener('focus', this._listener, true);\n        this._listener = null;\n    }\n    /**\n     * Refocuses the first element in the FocusTrap if the focus event target was outside\n     * the FocusTrap.\n     *\n     * This is an event listener callback. The event listener is added in runOutsideAngular,\n     * so all this code runs outside Angular as well.\n     */\n    _trapFocus(focusTrap, event) {\n        const target = event.target;\n        const focusTrapRoot = focusTrap._element;\n        // Don't refocus if target was in an overlay, because the overlay might be associated\n        // with an element inside the FocusTrap, ex. mat-select.\n        if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n            // Some legacy FocusTrap usages have logic that focuses some element on the page\n            // just before FocusTrap is destroyed. For backwards compatibility, wait\n            // to be sure FocusTrap is still enabled before refocusing.\n            setTimeout(() => {\n                // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n                if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n                    focusTrap.focusFirstTabbableElement();\n                }\n            });\n        }\n    }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n    // A stack of the FocusTraps on the page. Only the FocusTrap at the\n    // top of the stack is active.\n    _focusTrapStack = [];\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n    register(focusTrap) {\n        // Dedupe focusTraps that register multiple times.\n        this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n        let stack = this._focusTrapStack;\n        if (stack.length) {\n            stack[stack.length - 1]._disable();\n        }\n        stack.push(focusTrap);\n        focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n    deregister(focusTrap) {\n        focusTrap._disable();\n        const stack = this._focusTrapStack;\n        const i = stack.indexOf(focusTrap);\n        if (i !== -1) {\n            stack.splice(i, 1);\n            if (stack.length) {\n                stack[stack.length - 1]._enable();\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusTrapManager, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusTrapManager, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusTrapManager, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n    _checker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _focusTrapManager = inject(FocusTrapManager);\n    _document = inject(DOCUMENT);\n    _inertStrategy;\n    _injector = inject(Injector);\n    constructor() {\n        const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, { optional: true });\n        // TODO split up the strategies into different modules, similar to DateAdapter.\n        this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n    }\n    create(element, config = { defer: false }) {\n        let configObject;\n        if (typeof config === 'boolean') {\n            configObject = { defer: config };\n        }\n        else {\n            configObject = config;\n        }\n        return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ConfigurableFocusTrapFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ConfigurableFocusTrapFactory, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ConfigurableFocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nexport { AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_TRAP_INERT_STRATEGY, FocusTrap, InteractivityChecker, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId };\n//# sourceMappingURL=a11y.mjs.map\n"], "mappings": ";;AAAA,SAASA,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,6BAA6B,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,uCAAuC,EAAEC,CAAC,IAAIC,+BAA+B,EAAEC,CAAC,IAAIC,qBAAqB,QAAQ,8BAA8B;AAC1Q,SAASL,CAAC,IAAIM,SAAS,EAAEF,CAAC,IAAIG,oBAAoB,QAAQ,4BAA4B;AACtF,SAASC,CAAC,IAAIC,UAAU,EAAEf,CAAC,IAAIgB,WAAW,EAAElB,CAAC,IAAImB,YAAY,EAAEf,CAAC,IAAIgB,gBAAgB,EAAEV,CAAC,IAAIW,gBAAgB,EAAEC,CAAC,IAAIC,wBAAwB,EAAEjB,CAAC,IAAIkB,iBAAiB,EAAEC,CAAC,IAAIC,8BAA8B,EAAEC,CAAC,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,oCAAoC,EAAEC,CAAC,IAAIC,aAAa,QAAQ,4BAA4B;AAC7U,SAASC,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC5F,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASX,CAAC,IAAIY,sBAAsB,QAAQ,6BAA6B;AACzE,SAASC,qBAAqB,QAAQ,eAAe;AACrD,SAAS9B,CAAC,IAAI+B,0BAA0B,QAAQ,6CAA6C;AAC7F,SAAS3C,CAAC,IAAI4C,eAAe,QAAQ,kCAAkC;AACvE,SAASjB,CAAC,IAAIkB,cAAc,QAAQ,iCAAiC;AACrE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,CAAC,IAAIC,gBAAgB,QAAQ,iCAAiC;AACvE,SAAS1C,CAAC,IAAI2C,wBAAwB,EAAE/C,CAAC,IAAIgD,iCAAiC,EAAE9C,CAAC,IAAI+C,cAAc,QAAQ,iCAAiC;AAC5I,SAASC,CAAC,IAAIC,+BAA+B,EAAEjD,CAAC,IAAIkD,gCAAgC,QAAQ,qCAAqC;AACjI,OAAO,gBAAgB;AACvB,OAAO,yBAAyB;AAChC,OAAO,2BAA2B;AAClC,OAAO,wCAAwC;AAC/C,OAAO,kCAAkC;AACzC,OAAO,wBAAwB;AAC/B,OAAO,qCAAqC;AAC5C,OAAO,sBAAsB;AAC7B,OAAO,iBAAiB;AACxB,OAAO,0BAA0B;AACjC,OAAO,gBAAgB;AACvB,OAAO,wBAAwB;;AAE/B;AACA,MAAMC,YAAY,GAAG,GAAG;AACxB;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EACvC,MAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAE,EAAEC,IAAI,CAAC;EACzCC,EAAE,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC;EACd,IAAIF,GAAG,CAACG,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACF,IAAI,CAAC,CAAC,KAAKH,EAAE,CAAC,EAAE;IAClD;EACJ;EACAC,GAAG,CAACK,IAAI,CAACN,EAAE,CAAC;EACZF,EAAE,CAACS,YAAY,CAACR,IAAI,EAAEE,GAAG,CAACO,IAAI,CAACZ,YAAY,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASa,sBAAsBA,CAACX,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EAC1C,MAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAE,EAAEC,IAAI,CAAC;EACzCC,EAAE,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC;EACd,MAAMO,WAAW,GAAGT,GAAG,CAACU,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKZ,EAAE,CAAC;EACjD,IAAIU,WAAW,CAACG,MAAM,EAAE;IACpBf,EAAE,CAACS,YAAY,CAACR,IAAI,EAAEW,WAAW,CAACF,IAAI,CAACZ,YAAY,CAAC,CAAC;EACzD,CAAC,MACI;IACDE,EAAE,CAACgB,eAAe,CAACf,IAAI,CAAC;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,SAASG,mBAAmBA,CAACJ,EAAE,EAAEC,IAAI,EAAE;EAAA,IAAAgB,gBAAA;EACnC;EACA,MAAMC,SAAS,GAAGlB,EAAE,CAACmB,YAAY,CAAClB,IAAI,CAAC;EACvC,QAAAgB,gBAAA,GAAOC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,KAAK,CAAC,MAAM,CAAC,cAAAH,gBAAA,cAAAA,gBAAA,GAAI,EAAE;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,qBAAqB,GAAG,mCAAmC;AACjE;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,yBAAyB;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAMC,8BAA8B,GAAG,sBAAsB;AAC7D;AACA,IAAIC,MAAM,GAAG,CAAC;AACd;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAShBC,WAAWA,CAAA,EAAG;IAAAC,eAAA,oBARFnD,MAAM,CAACO,QAAQ,CAAC;IAAA4C,eAAA,oBAChBnD,MAAM,CAACF,QAAQ,CAAC;IAC5B;IAAAqD,eAAA,2BACmB,IAAIC,GAAG,CAAC,CAAC;IAC5B;IAAAD,eAAA,6BACqB,IAAI;IACzB;IAAAA,eAAA,cACM,GAAGH,MAAM,EAAE,EAAE;IAEfhD,MAAM,CAACQ,sBAAsB,CAAC,CAAC6C,IAAI,CAAC5C,qBAAqB,CAAC;IAC1D,IAAI,CAAC6C,GAAG,GAAGtD,MAAM,CAACC,MAAM,CAAC,GAAG,GAAG,GAAG+C,MAAM,EAAE;EAC9C;EACAO,QAAQA,CAACC,WAAW,EAAEC,OAAO,EAAEC,IAAI,EAAE;IACjC,IAAI,CAAC,IAAI,CAACC,eAAe,CAACH,WAAW,EAAEC,OAAO,CAAC,EAAE;MAC7C;IACJ;IACA,MAAMG,GAAG,GAAGC,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;MAC7B;MACAK,YAAY,CAACL,OAAO,EAAE,IAAI,CAACH,GAAG,CAAC;MAC/B,IAAI,CAACS,gBAAgB,CAACC,GAAG,CAACJ,GAAG,EAAE;QAAEK,cAAc,EAAER,OAAO;QAAES,cAAc,EAAE;MAAE,CAAC,CAAC;IAClF,CAAC,MACI,IAAI,CAAC,IAAI,CAACH,gBAAgB,CAACI,GAAG,CAACP,GAAG,CAAC,EAAE;MACtC,IAAI,CAACQ,qBAAqB,CAACX,OAAO,EAAEC,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC,IAAI,CAACW,4BAA4B,CAACb,WAAW,EAAEI,GAAG,CAAC,EAAE;MACtD,IAAI,CAACU,oBAAoB,CAACd,WAAW,EAAEI,GAAG,CAAC;IAC/C;EACJ;EACAW,iBAAiBA,CAACf,WAAW,EAAEC,OAAO,EAAEC,IAAI,EAAE;IAAA,IAAAc,qBAAA;IAC1C,IAAI,CAACf,OAAO,IAAI,CAAC,IAAI,CAACgB,cAAc,CAACjB,WAAW,CAAC,EAAE;MAC/C;IACJ;IACA,MAAMI,GAAG,GAAGC,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,IAAI,IAAI,CAACW,4BAA4B,CAACb,WAAW,EAAEI,GAAG,CAAC,EAAE;MACrD,IAAI,CAACc,uBAAuB,CAAClB,WAAW,EAAEI,GAAG,CAAC;IAClD;IACA;IACA;IACA,IAAI,OAAOH,OAAO,KAAK,QAAQ,EAAE;MAC7B,MAAMkB,iBAAiB,GAAG,IAAI,CAACZ,gBAAgB,CAACa,GAAG,CAAChB,GAAG,CAAC;MACxD,IAAIe,iBAAiB,IAAIA,iBAAiB,CAACT,cAAc,KAAK,CAAC,EAAE;QAC7D,IAAI,CAACW,qBAAqB,CAACjB,GAAG,CAAC;MACnC;IACJ;IACA,IAAI,EAAAY,qBAAA,OAAI,CAACM,kBAAkB,cAAAN,qBAAA,uBAAvBA,qBAAA,CAAyBO,UAAU,CAACxC,MAAM,MAAK,CAAC,EAAE;MAClD,IAAI,CAACuC,kBAAkB,CAACE,MAAM,CAAC,CAAC;MAChC,IAAI,CAACF,kBAAkB,GAAG,IAAI;IAClC;EACJ;EACA;EACAG,WAAWA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACV,MAAMC,iBAAiB,GAAG,IAAI,CAACC,SAAS,CAACC,gBAAgB,CAAC,IAAItC,8BAA8B,KAAK,IAAI,CAACO,GAAG,IAAI,CAAC;IAC9G,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,iBAAiB,CAAC5C,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAC/C,IAAI,CAACmE,iCAAiC,CAACH,iBAAiB,CAAChE,CAAC,CAAC,CAAC;MAC5DgE,iBAAiB,CAAChE,CAAC,CAAC,CAACqB,eAAe,CAACO,8BAA8B,CAAC;IACxE;IACA,CAAAmC,sBAAA,OAAI,CAACJ,kBAAkB,cAAAI,sBAAA,eAAvBA,sBAAA,CAAyBF,MAAM,CAAC,CAAC;IACjC,IAAI,CAACF,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACf,gBAAgB,CAACwB,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACInB,qBAAqBA,CAACX,OAAO,EAAEC,IAAI,EAAE;IACjC,MAAMO,cAAc,GAAG,IAAI,CAACmB,SAAS,CAACI,aAAa,CAAC,KAAK,CAAC;IAC1D1B,YAAY,CAACG,cAAc,EAAE,IAAI,CAACX,GAAG,CAAC;IACtCW,cAAc,CAACwB,WAAW,GAAGhC,OAAO;IACpC,IAAIC,IAAI,EAAE;MACNO,cAAc,CAAChC,YAAY,CAAC,MAAM,EAAEyB,IAAI,CAAC;IAC7C;IACA,IAAI,CAACgC,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACZ,kBAAkB,CAACa,WAAW,CAAC1B,cAAc,CAAC;IACnD,IAAI,CAACF,gBAAgB,CAACC,GAAG,CAACH,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC,EAAE;MAAEO,cAAc;MAAEC,cAAc,EAAE;IAAE,CAAC,CAAC;EAC3F;EACA;EACAW,qBAAqBA,CAACjB,GAAG,EAAE;IAAA,IAAAgC,qBAAA;IACvB,CAAAA,qBAAA,OAAI,CAAC7B,gBAAgB,CAACa,GAAG,CAAChB,GAAG,CAAC,cAAAgC,qBAAA,gBAAAA,qBAAA,GAA9BA,qBAAA,CAAgC3B,cAAc,cAAA2B,qBAAA,eAA9CA,qBAAA,CAAgDZ,MAAM,CAAC,CAAC;IACxD,IAAI,CAACjB,gBAAgB,CAAC8B,MAAM,CAACjC,GAAG,CAAC;EACrC;EACA;EACA8B,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACZ,kBAAkB,EAAE;MACzB;IACJ;IACA,MAAMgB,kBAAkB,GAAG,mCAAmC;IAC9D,MAAMC,gBAAgB,GAAG,IAAI,CAACX,SAAS,CAACC,gBAAgB,CAAC,IAAIS,kBAAkB,qBAAqB,CAAC;IACrG,KAAK,IAAI3E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4E,gBAAgB,CAACxD,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAC9C;MACA;MACA;MACA;MACA4E,gBAAgB,CAAC5E,CAAC,CAAC,CAAC6D,MAAM,CAAC,CAAC;IAChC;IACA,MAAMgB,iBAAiB,GAAG,IAAI,CAACZ,SAAS,CAACI,aAAa,CAAC,KAAK,CAAC;IAC7D;IACA;IACA;IACA;IACAQ,iBAAiB,CAACC,KAAK,CAACC,UAAU,GAAG,QAAQ;IAC7C;IACA;IACAF,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAACN,kBAAkB,CAAC;IACnDE,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACtD,IAAI,CAAC,IAAI,CAACC,SAAS,CAACC,SAAS,EAAE;MAC3BN,iBAAiB,CAAC/D,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IACxD;IACA,IAAI,CAACmD,SAAS,CAACmB,IAAI,CAACZ,WAAW,CAACK,iBAAiB,CAAC;IAClD,IAAI,CAAClB,kBAAkB,GAAGkB,iBAAiB;EAC/C;EACA;EACAV,iCAAiCA,CAACkB,OAAO,EAAE;IACvC;IACA,MAAMC,oBAAoB,GAAG7E,mBAAmB,CAAC4E,OAAO,EAAE,kBAAkB,CAAC,CAACnE,MAAM,CAACX,EAAE,IAAIA,EAAE,CAACgF,OAAO,CAAC5D,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACtI0D,OAAO,CAACvE,YAAY,CAAC,kBAAkB,EAAEwE,oBAAoB,CAACvE,IAAI,CAAC,GAAG,CAAC,CAAC;EAC5E;EACA;AACJ;AACA;AACA;EACIoC,oBAAoBA,CAACkC,OAAO,EAAE5C,GAAG,EAAE;IAC/B,MAAMe,iBAAiB,GAAG,IAAI,CAACZ,gBAAgB,CAACa,GAAG,CAAChB,GAAG,CAAC;IACxD;IACA;IACArC,mBAAmB,CAACiF,OAAO,EAAE,kBAAkB,EAAE7B,iBAAiB,CAACV,cAAc,CAACvC,EAAE,CAAC;IACrF8E,OAAO,CAACvE,YAAY,CAACc,8BAA8B,EAAE,IAAI,CAACO,GAAG,CAAC;IAC9DqB,iBAAiB,CAACT,cAAc,EAAE;EACtC;EACA;AACJ;AACA;AACA;EACIQ,uBAAuBA,CAAC8B,OAAO,EAAE5C,GAAG,EAAE;IAClC,MAAMe,iBAAiB,GAAG,IAAI,CAACZ,gBAAgB,CAACa,GAAG,CAAChB,GAAG,CAAC;IACxDe,iBAAiB,CAACT,cAAc,EAAE;IAClC/B,sBAAsB,CAACqE,OAAO,EAAE,kBAAkB,EAAE7B,iBAAiB,CAACV,cAAc,CAACvC,EAAE,CAAC;IACxF8E,OAAO,CAAChE,eAAe,CAACO,8BAA8B,CAAC;EAC3D;EACA;EACAsB,4BAA4BA,CAACmC,OAAO,EAAE5C,GAAG,EAAE;IACvC,MAAM+C,YAAY,GAAG/E,mBAAmB,CAAC4E,OAAO,EAAE,kBAAkB,CAAC;IACrE,MAAM7B,iBAAiB,GAAG,IAAI,CAACZ,gBAAgB,CAACa,GAAG,CAAChB,GAAG,CAAC;IACxD,MAAMgD,SAAS,GAAGjC,iBAAiB,IAAIA,iBAAiB,CAACV,cAAc,CAACvC,EAAE;IAC1E,OAAO,CAAC,CAACkF,SAAS,IAAID,YAAY,CAACD,OAAO,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;EAC/D;EACA;EACAjD,eAAeA,CAAC6C,OAAO,EAAE/C,OAAO,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACgB,cAAc,CAAC+B,OAAO,CAAC,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA,IAAI/C,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MACxC;MACA;MACA;MACA,OAAO,IAAI;IACf;IACA,MAAMoD,cAAc,GAAGpD,OAAO,IAAI,IAAI,GAAG,EAAE,GAAG,GAAGA,OAAO,EAAE,CAAC5B,IAAI,CAAC,CAAC;IACjE,MAAMiF,SAAS,GAAGN,OAAO,CAAC7D,YAAY,CAAC,YAAY,CAAC;IACpD;IACA;IACA,OAAOkE,cAAc,GAAG,CAACC,SAAS,IAAIA,SAAS,CAACjF,IAAI,CAAC,CAAC,KAAKgF,cAAc,GAAG,KAAK;EACrF;EACA;EACApC,cAAcA,CAAC+B,OAAO,EAAE;IACpB,OAAOA,OAAO,CAACO,QAAQ,KAAK,IAAI,CAAC3B,SAAS,CAAC4B,YAAY;EAC3D;AAGJ;AAACC,cAAA,GA1KKhE,aAAa;AAAAE,eAAA,CAAbF,aAAa,wBAAAiE,uBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAwKoFlE,cAAa;AAAA;AAAAE,eAAA,CAxK9GF,aAAa,+BA2K8DlD,EAAE,CAAAqH,kBAAA;EAAAC,KAAA,EAFwBpE,cAAa;EAAAqE,OAAA,EAAbrE,cAAa,CAAAsE,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE5I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF1H,EAAE,CAAA2H,iBAAA,CAAQzE,aAAa,EAAc,CAAC;IAC3G0E,IAAI,EAAEzH,UAAU;IAChB0H,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,SAAS3D,MAAMA,CAACJ,OAAO,EAAEC,IAAI,EAAE;EAC3B,OAAO,OAAOD,OAAO,KAAK,QAAQ,GAAG,GAAGC,IAAI,IAAI,EAAE,IAAID,OAAO,EAAE,GAAGA,OAAO;AAC7E;AACA;AACA,SAASK,YAAYA,CAAC0C,OAAO,EAAEqB,SAAS,EAAE;EACtC,IAAI,CAACrB,OAAO,CAAC9E,EAAE,EAAE;IACb8E,OAAO,CAAC9E,EAAE,GAAG,GAAGoB,yBAAyB,IAAI+E,SAAS,IAAI7E,MAAM,EAAE,EAAE;EACxE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8E,kBAAkB,CAAC;EAAA5E,YAAA;IAAAC,eAAA,gCACG,IAAI;IAC5B;IACA;IAAAA,eAAA,iBACS,IAAItC,OAAO,CAAC,CAAC;EAAA;EACtBkH,OAAOA,CAAA,EAAG;IACN,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC;EAC1B;EACAC,SAASA,CAAA,EAAG;IACR;EAAA;EAEJC,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA,OAAO,IAAI;EACf;EACAC,aAAaA,CAAA,EAAG;IACZ;IACA;IACA,OAAO,IAAI;EACf;EACAC,SAASA,CAAA,EAAG;IACR;EAAA;AAER;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAAA,EAAG;EACrC,OAAO,MAAM,IAAIR,kBAAkB,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,sCAAsC,GAAG;EAC3CC,OAAO,EAAEzH,gBAAgB;EACzB0H,UAAU,EAAEH;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,qBAAqB,SAASjK,SAAS,CAAC;EAG1C;EACA,IAAIkK,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGC,KAAK;IACrB,IAAI,IAAI,CAACD,QAAQ,EAAE;MACf,IAAI,CAACE,iBAAiB,CAACC,QAAQ,CAAC,IAAI,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAACD,iBAAiB,CAACE,UAAU,CAAC,IAAI,CAAC;IAC3C;EACJ;EACA9F,WAAWA,CAAC+F,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE/D,SAAS,EAAE0D,iBAAiB,EAAEM,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IACrG,KAAK,CAACL,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE/D,SAAS,EAAEiE,MAAM,CAACE,KAAK,EAAED,QAAQ,CAAC;IAACnG,eAAA;IAAAA,eAAA;IACtE,IAAI,CAAC2F,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACM,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACN,iBAAiB,CAACC,QAAQ,CAAC,IAAI,CAAC;EACzC;EACA;EACAhB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACe,iBAAiB,CAACE,UAAU,CAAC,IAAI,CAAC;IACvC,KAAK,CAACjB,OAAO,CAAC,CAAC;EACnB;EACA;EACAyB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACJ,cAAc,CAACK,YAAY,CAAC,IAAI,CAAC;IACtC,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAC5B;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACP,cAAc,CAACQ,UAAU,CAAC,IAAI,CAAC;IACpC,IAAI,CAACF,aAAa,CAAC,KAAK,CAAC;EAC7B;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMG,mCAAmC,CAAC;EAAA3G,YAAA;IACtC;IAAAC,eAAA,oBACY,IAAI;EAAA;EAChB;EACAsG,YAAYA,CAACK,SAAS,EAAE;IACpB;IACA,IAAI,IAAI,CAACC,SAAS,EAAE;MAChBD,SAAS,CAAC1E,SAAS,CAAC4E,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACD,SAAS,EAAE,IAAI,CAAC;IAC1E;IACA,IAAI,CAACA,SAAS,GAAIzK,CAAC,IAAK,IAAI,CAAC2K,UAAU,CAACH,SAAS,EAAExK,CAAC,CAAC;IACrDwK,SAAS,CAACX,OAAO,CAACe,iBAAiB,CAAC,MAAM;MACtCJ,SAAS,CAAC1E,SAAS,CAAC+E,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACJ,SAAS,EAAE,IAAI,CAAC;IACvE,CAAC,CAAC;EACN;EACA;EACAH,UAAUA,CAACE,SAAS,EAAE;IAClB,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB;IACJ;IACAD,SAAS,CAAC1E,SAAS,CAAC4E,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACD,SAAS,EAAE,IAAI,CAAC;IACtE,IAAI,CAACA,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,UAAUA,CAACH,SAAS,EAAEM,KAAK,EAAE;IAAA,IAAAC,eAAA;IACzB,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAAM;IAC3B,MAAMC,aAAa,GAAGT,SAAS,CAACb,QAAQ;IACxC;IACA;IACA,IAAIqB,MAAM,IAAI,CAACC,aAAa,CAACC,QAAQ,CAACF,MAAM,CAAC,IAAI,GAAAD,eAAA,GAACC,MAAM,CAACG,OAAO,cAAAJ,eAAA,eAAdA,eAAA,CAAAK,IAAA,CAAAJ,MAAM,EAAW,sBAAsB,CAAC,GAAE;MACxF;MACA;MACA;MACAK,UAAU,CAAC,MAAM;QACb;QACA,IAAIb,SAAS,CAACnB,OAAO,IAAI,CAAC4B,aAAa,CAACC,QAAQ,CAACV,SAAS,CAAC1E,SAAS,CAACwF,aAAa,CAAC,EAAE;UACjFd,SAAS,CAACe,yBAAyB,CAAC,CAAC;QACzC;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;;AAEA;AACA,MAAMC,yBAAyB,GAAG,IAAI3K,cAAc,CAAC,2BAA2B,CAAC;;AAEjF;AACA,MAAM4K,gBAAgB,CAAC;EAAA7H,YAAA;IACnB;IACA;IAAAC,eAAA,0BACkB,EAAE;EAAA;EACpB;AACJ;AACA;AACA;EACI4F,QAAQA,CAACe,SAAS,EAAE;IAChB;IACA,IAAI,CAACkB,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC3I,MAAM,CAAC4I,EAAE,IAAIA,EAAE,KAAKnB,SAAS,CAAC;IAC1E,IAAIoB,KAAK,GAAG,IAAI,CAACF,eAAe;IAChC,IAAIE,KAAK,CAAC3I,MAAM,EAAE;MACd2I,KAAK,CAACA,KAAK,CAAC3I,MAAM,GAAG,CAAC,CAAC,CAACoH,QAAQ,CAAC,CAAC;IACtC;IACAuB,KAAK,CAAClJ,IAAI,CAAC8H,SAAS,CAAC;IACrBA,SAAS,CAACN,OAAO,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;EACIR,UAAUA,CAACc,SAAS,EAAE;IAClBA,SAAS,CAACH,QAAQ,CAAC,CAAC;IACpB,MAAMuB,KAAK,GAAG,IAAI,CAACF,eAAe;IAClC,MAAM7J,CAAC,GAAG+J,KAAK,CAACxE,OAAO,CAACoD,SAAS,CAAC;IAClC,IAAI3I,CAAC,KAAK,CAAC,CAAC,EAAE;MACV+J,KAAK,CAACC,MAAM,CAAChK,CAAC,EAAE,CAAC,CAAC;MAClB,IAAI+J,KAAK,CAAC3I,MAAM,EAAE;QACd2I,KAAK,CAACA,KAAK,CAAC3I,MAAM,GAAG,CAAC,CAAC,CAACiH,OAAO,CAAC,CAAC;MACrC;IACJ;EACJ;AAGJ;AAAC4B,iBAAA,GAnCKL,gBAAgB;AAAA5H,eAAA,CAAhB4H,gBAAgB,wBAAAM,0BAAAlE,iBAAA;EAAA,YAAAA,iBAAA,IAiCiF4D,iBAAgB;AAAA;AAAA5H,eAAA,CAjCjH4H,gBAAgB,+BAvM2DhL,EAAE,CAAAqH,kBAAA;EAAAC,KAAA,EAyOwB0D,iBAAgB;EAAAzD,OAAA,EAAhByD,iBAAgB,CAAAxD,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE/I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3OiF1H,EAAE,CAAA2H,iBAAA,CA2OQqD,gBAAgB,EAAc,CAAC;IAC9GpD,IAAI,EAAEzH,UAAU;IAChB0H,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA,MAAM8D,4BAA4B,CAAC;EAO/BpI,WAAWA,CAAA,EAAG;IAAAC,eAAA,mBANHnD,MAAM,CAACtB,oBAAoB,CAAC;IAAAyE,eAAA,kBAC7BnD,MAAM,CAACI,MAAM,CAAC;IAAA+C,eAAA,4BACJnD,MAAM,CAAC+K,gBAAgB,CAAC;IAAA5H,eAAA,oBAChCnD,MAAM,CAACF,QAAQ,CAAC;IAAAqD,eAAA;IAAAA,eAAA,oBAEhBnD,MAAM,CAACK,QAAQ,CAAC;IAExB,MAAMkL,aAAa,GAAGvL,MAAM,CAAC8K,yBAAyB,EAAE;MAAEU,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3E;IACA,IAAI,CAACpC,cAAc,GAAGmC,aAAa,IAAI,IAAI1B,mCAAmC,CAAC,CAAC;EACpF;EACA4B,MAAMA,CAACjF,OAAO,EAAE6C,MAAM,GAAG;IAAEE,KAAK,EAAE;EAAM,CAAC,EAAE;IACvC,IAAImC,YAAY;IAChB,IAAI,OAAOrC,MAAM,KAAK,SAAS,EAAE;MAC7BqC,YAAY,GAAG;QAAEnC,KAAK,EAAEF;MAAO,CAAC;IACpC,CAAC,MACI;MACDqC,YAAY,GAAGrC,MAAM;IACzB;IACA,OAAO,IAAIX,qBAAqB,CAAClC,OAAO,EAAE,IAAI,CAAC0C,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC/D,SAAS,EAAE,IAAI,CAAC0D,iBAAiB,EAAE,IAAI,CAACM,cAAc,EAAEsC,YAAY,EAAE,IAAI,CAACC,SAAS,CAAC;EACrK;AAGJ;AAACC,6BAAA,GAxBKN,4BAA4B;AAAAnI,eAAA,CAA5BmI,4BAA4B,wBAAAO,sCAAA1E,iBAAA;EAAA,YAAAA,iBAAA,IAsBqEmE,6BAA4B;AAAA;AAAAnI,eAAA,CAtB7HmI,4BAA4B,+BAjP+CvL,EAAE,CAAAqH,kBAAA;EAAAC,KAAA,EAwQwBiE,6BAA4B;EAAAhE,OAAA,EAA5BgE,6BAA4B,CAAA/D,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE3J;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1QiF1H,EAAE,CAAA2H,iBAAA,CA0QQ4D,4BAA4B,EAAc,CAAC;IAC1H3D,IAAI,EAAEzH,UAAU;IAChB0H,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASvE,aAAa,EAAEF,8BAA8B,EAAED,yBAAyB,EAAE4F,qBAAqB,EAAE4C,4BAA4B,EAAEzB,mCAAmC,EAAEiB,yBAAyB,EAAErM,SAAS,EAAEC,oBAAoB,EAAEmE,qBAAqB,EAAEyF,6BAA6B,EAAEC,sCAAsC,EAAET,kBAAkB,EAAE/G,gBAAgB,EAAEQ,mBAAmB,EAAEK,mBAAmB,EAAEO,sBAAsB;AAC7a", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}