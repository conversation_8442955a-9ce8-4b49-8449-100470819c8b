{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiTimepickerComponent } from './swui-timepicker.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatOptionModule } from '@angular/material/core';\nimport { MatSelectModule } from '@angular/material/select';\nlet SwuiTimepickerModule = class SwuiTimepickerModule {};\nSwuiTimepickerModule = __decorate([NgModule({\n  declarations: [SwuiTimepickerComponent],\n  imports: [CommonModule, ReactiveFormsModule, MatFormFieldModule, MatOptionModule, MatSelectModule],\n  exports: [SwuiTimepickerComponent]\n})], SwuiTimepickerModule);\nexport { SwuiTimepickerModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ReactiveFormsModule", "SwuiTimepickerComponent", "MatFormFieldModule", "MatOptionModule", "MatSelectModule", "SwuiTimepickerModule", "__decorate", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-timepicker/swui-timepicker.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\n\nimport { SwuiTimepickerComponent } from './swui-timepicker.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatOptionModule } from '@angular/material/core';\nimport { MatSelectModule } from '@angular/material/select';\n\n@NgModule({\n  declarations: [SwuiTimepickerComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatFormFieldModule,\n    MatOptionModule,\n    MatSelectModule,\n  ],\n  exports: [SwuiTimepickerComponent]\n})\nexport class SwuiTimepickerModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAanD,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB,GAAI;AAAxBA,oBAAoB,GAAAC,UAAA,EAXhCR,QAAQ,CAAC;EACRS,YAAY,EAAE,CAACN,uBAAuB,CAAC;EACvCO,OAAO,EAAE,CACPT,YAAY,EACZC,mBAAmB,EACnBE,kBAAkB,EAClBC,eAAe,EACfC,eAAe,CAChB;EACDK,OAAO,EAAE,CAACR,uBAAuB;CAClC,CAAC,C,EACWI,oBAAoB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}