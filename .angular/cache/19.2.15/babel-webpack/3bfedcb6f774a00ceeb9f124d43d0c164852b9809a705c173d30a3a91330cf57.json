{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _FullscreenOverlayContainer;\nimport { O as OverlayContainer } from './overlay-module-BUj0D19H.mjs';\nexport { B as BlockScrollStrategy, b as CdkConnectedOverlay, C as CdkOverlayOrigin, l as CloseScrollStrategy, i as ConnectedOverlayPositionChange, g as ConnectionPositionPair, F as FlexibleConnectedPositionStrategy, G as GlobalPositionStrategy, N as NoopScrollStrategy, a as Overlay, f as OverlayConfig, o as OverlayKeyboardDispatcher, m as OverlayModule, n as OverlayOutsideClickDispatcher, d as OverlayPositionBuilder, c as OverlayRef, R as RepositionScrollStrategy, S as STANDARD_DROPDOWN_ADJACENT_POSITIONS, e as STANDARD_DROPDOWN_BELOW_POSITIONS, k as ScrollStrategyOptions, h as ScrollingVisibility, j as validateHorizontalPosition, v as validateVerticalPosition } from './overlay-module-BUj0D19H.mjs';\nimport * as i0 from '@angular/core';\nimport { inject, RendererFactory2, Injectable } from '@angular/core';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler, CdkFixedSizeVirtualScroll as ɵɵCdkFixedSizeVirtualScroll, CdkScrollableModule as ɵɵCdkScrollableModule, CdkVirtualForOf as ɵɵCdkVirtualForOf, CdkVirtualScrollViewport as ɵɵCdkVirtualScrollViewport, CdkVirtualScrollableElement as ɵɵCdkVirtualScrollableElement, CdkVirtualScrollableWindow as ɵɵCdkVirtualScrollableWindow } from './scrolling.mjs';\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport '@angular/common';\nimport './platform-DmdVEw_C.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './test-environment-CT0XxPyp.mjs';\nimport './style-loader-Cu9AvjH9.mjs';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './portal-directives-Bw5woq8I.mjs';\nimport './directionality-CBXD4hga.mjs';\nimport './id-generator-Dw_9dSDu.mjs';\nimport './keycodes-CpHkExLC.mjs';\nimport './keycodes.mjs';\nimport './element-x4z00URv.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n  constructor() {\n    super();\n    _defineProperty(this, \"_renderer\", inject(RendererFactory2).createRenderer(null, null));\n    _defineProperty(this, \"_fullScreenEventName\", void 0);\n    _defineProperty(this, \"_cleanupFullScreenListener\", void 0);\n  }\n  ngOnDestroy() {\n    var _this$_cleanupFullScr;\n    super.ngOnDestroy();\n    (_this$_cleanupFullScr = this._cleanupFullScreenListener) === null || _this$_cleanupFullScr === void 0 || _this$_cleanupFullScr.call(this);\n  }\n  _createContainer() {\n    const eventName = this._getEventName();\n    super._createContainer();\n    this._adjustParentForFullscreenChange();\n    if (eventName) {\n      var _this$_cleanupFullScr2;\n      (_this$_cleanupFullScr2 = this._cleanupFullScreenListener) === null || _this$_cleanupFullScr2 === void 0 || _this$_cleanupFullScr2.call(this);\n      this._cleanupFullScreenListener = this._renderer.listen('document', eventName, () => {\n        this._adjustParentForFullscreenChange();\n      });\n    }\n  }\n  _adjustParentForFullscreenChange() {\n    if (this._containerElement) {\n      const fullscreenElement = this.getFullscreenElement();\n      const parent = fullscreenElement || this._document.body;\n      parent.appendChild(this._containerElement);\n    }\n  }\n  _getEventName() {\n    if (!this._fullScreenEventName) {\n      const _document = this._document;\n      if (_document.fullscreenEnabled) {\n        this._fullScreenEventName = 'fullscreenchange';\n      } else if (_document.webkitFullscreenEnabled) {\n        this._fullScreenEventName = 'webkitfullscreenchange';\n      } else if (_document.mozFullScreenEnabled) {\n        this._fullScreenEventName = 'mozfullscreenchange';\n      } else if (_document.msFullscreenEnabled) {\n        this._fullScreenEventName = 'MSFullscreenChange';\n      }\n    }\n    return this._fullScreenEventName;\n  }\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n  getFullscreenElement() {\n    const _document = this._document;\n    return _document.fullscreenElement || _document.webkitFullscreenElement || _document.mozFullScreenElement || _document.msFullscreenElement || null;\n  }\n}\n_FullscreenOverlayContainer = FullscreenOverlayContainer;\n_defineProperty(FullscreenOverlayContainer, \"\\u0275fac\", function _FullscreenOverlayContainer_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FullscreenOverlayContainer)();\n});\n_defineProperty(FullscreenOverlayContainer, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _FullscreenOverlayContainer,\n  factory: _FullscreenOverlayContainer.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullscreenOverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { FullscreenOverlayContainer, OverlayContainer };", "map": {"version": 3, "names": ["O", "OverlayContainer", "B", "BlockScrollStrategy", "b", "CdkConnectedOverlay", "C", "CdkOverlayOrigin", "l", "CloseScrollStrategy", "i", "ConnectedOverlayPositionChange", "g", "ConnectionPositionPair", "F", "FlexibleConnectedPositionStrategy", "G", "GlobalPositionStrategy", "N", "NoopScrollStrategy", "a", "Overlay", "f", "OverlayConfig", "o", "OverlayKeyboardDispatcher", "m", "OverlayModule", "n", "OverlayOutsideClickDispatcher", "d", "OverlayPositionBuilder", "c", "OverlayRef", "R", "RepositionScrollStrategy", "S", "STANDARD_DROPDOWN_ADJACENT_POSITIONS", "e", "STANDARD_DROPDOWN_BELOW_POSITIONS", "k", "ScrollStrategyOptions", "h", "ScrollingVisibility", "j", "validateHorizontalPosition", "v", "validateVerticalPosition", "i0", "inject", "RendererFactory2", "Injectable", "CdkScrollable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "CdkFixedSizeVirtualScroll", "ɵɵCdkFixedSizeVirtualScroll", "CdkScrollableModule", "ɵɵCdkScrollableModule", "CdkVirtualForOf", "ɵɵCdkVirtualForOf", "CdkVirtualScrollViewport", "ɵɵCdkVirtualScrollViewport", "CdkVirtualScrollableElement", "ɵɵCdkVirtualScrollableElement", "CdkVirtualScrollableWindow", "ɵɵCdkVirtualScrollableWindow", "<PERSON><PERSON>", "ɵɵDir", "FullscreenOverlayContainer", "constructor", "_defineProperty", "<PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "_this$_cleanupFullScr", "_cleanupFullScreenListener", "call", "_createContainer", "eventName", "_getEventName", "_adjustParentForFullscreenChange", "_this$_cleanupFullScr2", "_renderer", "listen", "_containerElement", "fullscreenElement", "getFullscreenElement", "parent", "_document", "body", "append<PERSON><PERSON><PERSON>", "_fullScreenEventName", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "_FullscreenOverlayContainer", "_FullscreenOverlayContainer_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/overlay.mjs"], "sourcesContent": ["import { O as OverlayContainer } from './overlay-module-BUj0D19H.mjs';\nexport { B as BlockScrollStrategy, b as CdkConnectedOverlay, C as CdkOverlayOrigin, l as CloseScrollStrategy, i as ConnectedOverlayPositionChange, g as ConnectionPositionPair, F as FlexibleConnectedPositionStrategy, G as GlobalPositionStrategy, N as NoopScrollStrategy, a as Overlay, f as OverlayConfig, o as OverlayKeyboardDispatcher, m as OverlayModule, n as OverlayOutsideClickDispatcher, d as OverlayPositionBuilder, c as OverlayRef, R as RepositionScrollStrategy, S as STANDARD_DROPDOWN_ADJACENT_POSITIONS, e as STANDARD_DROPDOWN_BELOW_POSITIONS, k as ScrollStrategyOptions, h as ScrollingVisibility, j as validateHorizontalPosition, v as validateVerticalPosition } from './overlay-module-BUj0D19H.mjs';\nimport * as i0 from '@angular/core';\nimport { inject, RendererFactory2, Injectable } from '@angular/core';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler, CdkFixedSizeVirtualScroll as ɵɵCdkFixedSizeVirtualScroll, CdkScrollableModule as ɵɵCdkScrollableModule, CdkVirtualForOf as ɵɵCdkVirtualForOf, CdkVirtualScrollViewport as ɵɵCdkVirtualScrollViewport, CdkVirtualScrollableElement as ɵɵCdkVirtualScrollableElement, CdkVirtualScrollableWindow as ɵɵCdkVirtualScrollableWindow } from './scrolling.mjs';\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport '@angular/common';\nimport './platform-DmdVEw_C.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './test-environment-CT0XxPyp.mjs';\nimport './style-loader-Cu9AvjH9.mjs';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './portal-directives-Bw5woq8I.mjs';\nimport './directionality-CBXD4hga.mjs';\nimport './id-generator-Dw_9dSDu.mjs';\nimport './keycodes-CpHkExLC.mjs';\nimport './keycodes.mjs';\nimport './element-x4z00URv.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _fullScreenEventName;\n    _cleanupFullScreenListener;\n    constructor() {\n        super();\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._cleanupFullScreenListener?.();\n    }\n    _createContainer() {\n        const eventName = this._getEventName();\n        super._createContainer();\n        this._adjustParentForFullscreenChange();\n        if (eventName) {\n            this._cleanupFullScreenListener?.();\n            this._cleanupFullScreenListener = this._renderer.listen('document', eventName, () => {\n                this._adjustParentForFullscreenChange();\n            });\n        }\n    }\n    _adjustParentForFullscreenChange() {\n        if (this._containerElement) {\n            const fullscreenElement = this.getFullscreenElement();\n            const parent = fullscreenElement || this._document.body;\n            parent.appendChild(this._containerElement);\n        }\n    }\n    _getEventName() {\n        if (!this._fullScreenEventName) {\n            const _document = this._document;\n            if (_document.fullscreenEnabled) {\n                this._fullScreenEventName = 'fullscreenchange';\n            }\n            else if (_document.webkitFullscreenEnabled) {\n                this._fullScreenEventName = 'webkitfullscreenchange';\n            }\n            else if (_document.mozFullScreenEnabled) {\n                this._fullScreenEventName = 'mozfullscreenchange';\n            }\n            else if (_document.msFullscreenEnabled) {\n                this._fullScreenEventName = 'MSFullscreenChange';\n            }\n        }\n        return this._fullScreenEventName;\n    }\n    /**\n     * When the page is put into fullscreen mode, a specific element is specified.\n     * Only that element and its children are visible when in fullscreen mode.\n     */\n    getFullscreenElement() {\n        const _document = this._document;\n        return (_document.fullscreenElement ||\n            _document.webkitFullscreenElement ||\n            _document.mozFullScreenElement ||\n            _document.msFullscreenElement ||\n            null);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FullscreenOverlayContainer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FullscreenOverlayContainer, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FullscreenOverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nexport { FullscreenOverlayContainer, OverlayContainer };\n"], "mappings": ";;AAAA,SAASA,CAAC,IAAIC,gBAAgB,QAAQ,+BAA+B;AACrE,SAASC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,8BAA8B,EAAEC,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,iCAAiC,EAAEC,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,6BAA6B,EAAEC,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,oCAAoC,EAAEC,CAAC,IAAIC,iCAAiC,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,0BAA0B,EAAEC,CAAC,IAAIC,wBAAwB,QAAQ,+BAA+B;AACnsB,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AACpE,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,yBAAyB,IAAIC,2BAA2B,EAAEC,mBAAmB,IAAIC,qBAAqB,EAAEC,eAAe,IAAIC,iBAAiB,EAAEC,wBAAwB,IAAIC,0BAA0B,EAAEC,2BAA2B,IAAIC,6BAA6B,EAAEC,0BAA0B,IAAIC,4BAA4B,QAAQ,iBAAiB;AAChZ,SAASC,GAAG,IAAIC,KAAK,QAAQ,YAAY;AACzC,OAAO,iBAAiB;AACxB,OAAO,yBAAyB;AAChC,OAAO,wCAAwC;AAC/C,OAAO,2BAA2B;AAClC,OAAO,iCAAiC;AACxC,OAAO,6BAA6B;AACpC,OAAO,MAAM;AACb,OAAO,gBAAgB;AACvB,OAAO,gCAAgC;AACvC,OAAO,sBAAsB;AAC7B,OAAO,0BAA0B;AACjC,OAAO,kCAAkC;AACzC,OAAO,+BAA+B;AACtC,OAAO,6BAA6B;AACpC,OAAO,yBAAyB;AAChC,OAAO,gBAAgB;AACvB,OAAO,wBAAwB;AAC/B,OAAO,+CAA+C;AACtD,OAAO,4BAA4B;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,SAASpE,gBAAgB,CAAC;EAItDqE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IAACC,eAAA,oBAJAtB,MAAM,CAACC,gBAAgB,CAAC,CAACsB,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IAAAD,eAAA;IAAAA,eAAA;EAK/D;EACAE,WAAWA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACV,KAAK,CAACD,WAAW,CAAC,CAAC;IACnB,CAAAC,qBAAA,OAAI,CAACC,0BAA0B,cAAAD,qBAAA,eAA/BA,qBAAA,CAAAE,IAAA,KAAkC,CAAC;EACvC;EACAC,gBAAgBA,CAAA,EAAG;IACf,MAAMC,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACtC,KAAK,CAACF,gBAAgB,CAAC,CAAC;IACxB,IAAI,CAACG,gCAAgC,CAAC,CAAC;IACvC,IAAIF,SAAS,EAAE;MAAA,IAAAG,sBAAA;MACX,CAAAA,sBAAA,OAAI,CAACN,0BAA0B,cAAAM,sBAAA,eAA/BA,sBAAA,CAAAL,IAAA,KAAkC,CAAC;MACnC,IAAI,CAACD,0BAA0B,GAAG,IAAI,CAACO,SAAS,CAACC,MAAM,CAAC,UAAU,EAAEL,SAAS,EAAE,MAAM;QACjF,IAAI,CAACE,gCAAgC,CAAC,CAAC;MAC3C,CAAC,CAAC;IACN;EACJ;EACAA,gCAAgCA,CAAA,EAAG;IAC/B,IAAI,IAAI,CAACI,iBAAiB,EAAE;MACxB,MAAMC,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;MACrD,MAAMC,MAAM,GAAGF,iBAAiB,IAAI,IAAI,CAACG,SAAS,CAACC,IAAI;MACvDF,MAAM,CAACG,WAAW,CAAC,IAAI,CAACN,iBAAiB,CAAC;IAC9C;EACJ;EACAL,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACY,oBAAoB,EAAE;MAC5B,MAAMH,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAIA,SAAS,CAACI,iBAAiB,EAAE;QAC7B,IAAI,CAACD,oBAAoB,GAAG,kBAAkB;MAClD,CAAC,MACI,IAAIH,SAAS,CAACK,uBAAuB,EAAE;QACxC,IAAI,CAACF,oBAAoB,GAAG,wBAAwB;MACxD,CAAC,MACI,IAAIH,SAAS,CAACM,oBAAoB,EAAE;QACrC,IAAI,CAACH,oBAAoB,GAAG,qBAAqB;MACrD,CAAC,MACI,IAAIH,SAAS,CAACO,mBAAmB,EAAE;QACpC,IAAI,CAACJ,oBAAoB,GAAG,oBAAoB;MACpD;IACJ;IACA,OAAO,IAAI,CAACA,oBAAoB;EACpC;EACA;AACJ;AACA;AACA;EACIL,oBAAoBA,CAAA,EAAG;IACnB,MAAME,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,OAAQA,SAAS,CAACH,iBAAiB,IAC/BG,SAAS,CAACQ,uBAAuB,IACjCR,SAAS,CAACS,oBAAoB,IAC9BT,SAAS,CAACU,mBAAmB,IAC7B,IAAI;EACZ;AAGJ;AAACC,2BAAA,GA7DK9B,0BAA0B;AAAAE,eAAA,CAA1BF,0BAA0B,wBAAA+B,oCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA2DuEhC,2BAA0B;AAAA;AAAAE,eAAA,CA3D3HF,0BAA0B,+BA8DiDrB,EAAE,CAAAsD,kBAAA;EAAAC,KAAA,EAFwBlC,2BAA0B;EAAAmC,OAAA,EAA1BnC,2BAA0B,CAAAoC,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEzJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF3D,EAAE,CAAA4D,iBAAA,CAAQvC,0BAA0B,EAAc,CAAC;IACxHwC,IAAI,EAAE1D,UAAU;IAChB2D,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASrC,0BAA0B,EAAEpE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}