{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridRowActionsComponent } from './row-actions.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatButtonModule } from '@angular/material/button';\nlet SwuiGridRowActionsModule = class SwuiGridRowActionsModule {};\nSwuiGridRowActionsModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule.forChild(), MatIconModule, MatButtonModule, MatMenuModule, MatTooltipModule],\n  declarations: [SwuiGridRowActionsComponent],\n  exports: [SwuiGridRowActionsComponent]\n})], SwuiGridRowActionsModule);\nexport { SwuiGridRowActionsModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "TranslateModule", "SwuiGridRowActionsComponent", "MatMenuModule", "MatIconModule", "MatTooltipModule", "MatButtonModule", "SwuiGridRowActionsModule", "__decorate", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/row-actions/row-actions.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridRowActionsComponent } from './row-actions.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatButtonModule } from '@angular/material/button';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    MatIconModule,\n    MatButtonModule,\n    MatMenuModule,\n    MatTooltipModule,\n  ],\n  declarations: [\n    SwuiGridRowActionsComponent\n  ],\n  exports: [\n    SwuiGridRowActionsComponent\n  ]\n})\nexport class SwuiGridRowActionsModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,2BAA2B,QAAQ,yBAAyB;AACrE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAmBnD,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB,GACpC;AADYA,wBAAwB,GAAAC,UAAA,EAhBpCT,QAAQ,CAAC;EACRU,OAAO,EAAE,CACPT,YAAY,EACZC,eAAe,CAACS,QAAQ,EAAE,EAC1BN,aAAa,EACbE,eAAe,EACfH,aAAa,EACbE,gBAAgB,CACjB;EACDM,YAAY,EAAE,CACZT,2BAA2B,CAC5B;EACDU,OAAO,EAAE,CACPV,2BAA2B;CAE9B,CAAC,C,EACWK,wBAAwB,CACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}