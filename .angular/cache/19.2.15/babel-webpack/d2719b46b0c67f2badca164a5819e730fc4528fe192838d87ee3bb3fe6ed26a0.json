{"ast": null, "code": "export function identity(x) {\n  return x;\n}\n//# sourceMappingURL=identity.js.map", "map": {"version": 3, "names": ["identity", "x"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/identity.js"], "sourcesContent": ["export function identity(x) {\n    return x;\n}\n//# sourceMappingURL=identity.js.map"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,CAAC,EAAE;EACxB,OAAOA,CAAC;AACZ;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}