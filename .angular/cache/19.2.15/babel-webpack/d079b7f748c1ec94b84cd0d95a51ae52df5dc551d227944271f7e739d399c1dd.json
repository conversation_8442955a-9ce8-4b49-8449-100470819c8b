{"ast": null, "code": "var _SwuiTdGamesLabelsWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./games-labels.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdGamesLabelsWidgetComponent = (_SwuiTdGamesLabelsWidgetComponent = class SwuiTdGamesLabelsWidgetComponent {\n  constructor({\n    field,\n    row,\n    schema: {\n      classMap\n    }\n  }) {\n    this.items = row[field].map(item => ({\n      title: item.title,\n      class: classMap && classMap.hasOwnProperty(item.group) ? classMap[item.group] : ''\n    }));\n  }\n}, _SwuiTdGamesLabelsWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdGamesLabelsWidgetComponent);\nSwuiTdGamesLabelsWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-game-labels-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdGamesLabelsWidgetComponent);\nexport { SwuiTdGamesLabelsWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdGamesLabelsWidgetComponent", "_SwuiTdGamesLabelsWidgetComponent", "constructor", "field", "row", "schema", "classMap", "items", "map", "item", "title", "class", "hasOwnProperty", "group", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/games-labels/games-labels.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./games-labels.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdGamesLabelsWidgetComponent = class SwuiTdGamesLabelsWidgetComponent {\n    constructor({ field, row, schema: { classMap } }) {\n        this.items = row[field].map(item => ({\n            title: item.title,\n            class: classMap && classMap.hasOwnProperty(item.group) ? classMap[item.group] : ''\n        }));\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdGamesLabelsWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-game-labels-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdGamesLabelsWidgetComponent);\nexport { SwuiTdGamesLabelsWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,gCAAgC,IAAAC,iCAAA,GAAG,MAAMD,gCAAgC,CAAC;EAC1EE,WAAWA,CAAC;IAAEC,KAAK;IAAEC,GAAG;IAAEC,MAAM,EAAE;MAAEC;IAAS;EAAE,CAAC,EAAE;IAC9C,IAAI,CAACC,KAAK,GAAGH,GAAG,CAACD,KAAK,CAAC,CAACK,GAAG,CAACC,IAAI,KAAK;MACjCC,KAAK,EAAED,IAAI,CAACC,KAAK;MACjBC,KAAK,EAAEL,QAAQ,IAAIA,QAAQ,CAACM,cAAc,CAACH,IAAI,CAACI,KAAK,CAAC,GAAGP,QAAQ,CAACG,IAAI,CAACI,KAAK,CAAC,GAAG;IACpF,CAAC,CAAC,CAAC;EACP;AAIJ,CAAC,EAHYZ,iCAAA,CAAKa,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEjB,MAAM;IAAEoB,IAAI,EAAE,CAACnB,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,iCAAA,CACJ;AACDD,gCAAgC,GAAGL,UAAU,CAAC,CAC1CE,SAAS,CAAC;EACNsB,QAAQ,EAAE,gCAAgC;EAC1CC,QAAQ,EAAExB,oBAAoB;EAC9ByB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAErB,gCAAgC,CAAC;AACpC,SAASA,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}