{"ast": null, "code": "var _LocalDataService;\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpHeaders, HttpResponse } from '@angular/common/http';\nimport { of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nlet LocalDataService = (_LocalDataService = class LocalDataService {\n  constructor() {\n    this.localData = [];\n  }\n  getGridData(params) {\n    const offset = Number(params.get('offset'));\n    const limit = Number(params.get('limit') || Infinity);\n    const sortBy = params.get('sortBy') || '';\n    const sortOrder = params.get('sortOrder') || '';\n    return of([...this.localData]).pipe(map(list => {\n      const dataExist = list.length > 0;\n      const sortOrderIsSet = typeof sortOrder !== 'undefined' && sortOrder !== '';\n      const sortByIsCorrect = dataExist && sortBy in list[0];\n      if (dataExist && sortOrderIsSet && sortByIsCorrect) {\n        const mod = sortOrder.toLowerCase() === 'asc' ? 1 : -1;\n        list = list.sort((a, b) => {\n          return a[sortBy] > b[sortBy] ? mod : -1 * mod;\n        });\n      }\n      return list;\n    }), map(list => {\n      return list.slice(offset, offset + limit);\n    }), map(body => {\n      let headers = new HttpHeaders();\n      headers = headers.append('x-paging-limit', limit ? limit.toString() : '20');\n      headers = headers.append('x-paging-offset', offset ? offset.toString() : '0');\n      headers = headers.append('x-paging-total', this.localData.length.toString());\n      return new HttpResponse({\n        body,\n        headers\n      });\n    }));\n  }\n}, _LocalDataService.ctorParameters = () => [], _LocalDataService);\nLocalDataService = __decorate([Injectable()], LocalDataService);\nexport { LocalDataService };", "map": {"version": 3, "names": ["__decorate", "Injectable", "HttpHeaders", "HttpResponse", "of", "map", "LocalDataService", "_LocalDataService", "constructor", "localData", "getGridData", "params", "offset", "Number", "get", "limit", "Infinity", "sortBy", "sortOrder", "pipe", "list", "dataExist", "length", "sortOrderIsSet", "sortByIsCorrect", "mod", "toLowerCase", "sort", "a", "b", "slice", "body", "headers", "append", "toString", "ctorParameters"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/services/local-data.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpHeaders, HttpResponse } from '@angular/common/http';\nimport { of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nlet LocalDataService = class LocalDataService {\n    constructor() {\n        this.localData = [];\n    }\n    getGridData(params) {\n        const offset = Number(params.get('offset'));\n        const limit = Number(params.get('limit') || Infinity);\n        const sortBy = params.get('sortBy') || '';\n        const sortOrder = params.get('sortOrder') || '';\n        return of([...this.localData])\n            .pipe(map((list) => {\n            const dataExist = list.length > 0;\n            const sortOrderIsSet = typeof sortOrder !== 'undefined' && sortOrder !== '';\n            const sortByIsCorrect = dataExist && sortBy in list[0];\n            if (dataExist && sortOrderIsSet && sortByIsCorrect) {\n                const mod = sortOrder.toLowerCase() === 'asc' ? 1 : -1;\n                list = list.sort((a, b) => {\n                    return a[sortBy] > b[sortBy] ? mod : -1 * mod;\n                });\n            }\n            return list;\n        }), map((list) => {\n            return list.slice(offset, offset + limit);\n        }), map((body) => {\n            let headers = new HttpHeaders();\n            headers = headers.append('x-paging-limit', (limit ? limit.toString() : '20'));\n            headers = headers.append('x-paging-offset', (offset ? offset.toString() : '0'));\n            headers = headers.append('x-paging-total', this.localData.length.toString());\n            return new HttpResponse({ body, headers });\n        }));\n    }\n    static { this.ctorParameters = () => []; }\n};\nLocalDataService = __decorate([\n    Injectable()\n], LocalDataService);\nexport { LocalDataService };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,EAAEC,YAAY,QAAQ,sBAAsB;AAChE,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,GAAG,QAAQ,gBAAgB;AACpC,IAAIC,gBAAgB,IAAAC,iBAAA,GAAG,MAAMD,gBAAgB,CAAC;EAC1CE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,GAAG,EAAE;EACvB;EACAC,WAAWA,CAACC,MAAM,EAAE;IAChB,MAAMC,MAAM,GAAGC,MAAM,CAACF,MAAM,CAACG,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC3C,MAAMC,KAAK,GAAGF,MAAM,CAACF,MAAM,CAACG,GAAG,CAAC,OAAO,CAAC,IAAIE,QAAQ,CAAC;IACrD,MAAMC,MAAM,GAAGN,MAAM,CAACG,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;IACzC,MAAMI,SAAS,GAAGP,MAAM,CAACG,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE;IAC/C,OAAOV,EAAE,CAAC,CAAC,GAAG,IAAI,CAACK,SAAS,CAAC,CAAC,CACzBU,IAAI,CAACd,GAAG,CAAEe,IAAI,IAAK;MACpB,MAAMC,SAAS,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC;MACjC,MAAMC,cAAc,GAAG,OAAOL,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,EAAE;MAC3E,MAAMM,eAAe,GAAGH,SAAS,IAAIJ,MAAM,IAAIG,IAAI,CAAC,CAAC,CAAC;MACtD,IAAIC,SAAS,IAAIE,cAAc,IAAIC,eAAe,EAAE;QAChD,MAAMC,GAAG,GAAGP,SAAS,CAACQ,WAAW,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;QACtDN,IAAI,GAAGA,IAAI,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACvB,OAAOD,CAAC,CAACX,MAAM,CAAC,GAAGY,CAAC,CAACZ,MAAM,CAAC,GAAGQ,GAAG,GAAG,CAAC,CAAC,GAAGA,GAAG;QACjD,CAAC,CAAC;MACN;MACA,OAAOL,IAAI;IACf,CAAC,CAAC,EAAEf,GAAG,CAAEe,IAAI,IAAK;MACd,OAAOA,IAAI,CAACU,KAAK,CAAClB,MAAM,EAAEA,MAAM,GAAGG,KAAK,CAAC;IAC7C,CAAC,CAAC,EAAEV,GAAG,CAAE0B,IAAI,IAAK;MACd,IAAIC,OAAO,GAAG,IAAI9B,WAAW,CAAC,CAAC;MAC/B8B,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,gBAAgB,EAAGlB,KAAK,GAAGA,KAAK,CAACmB,QAAQ,CAAC,CAAC,GAAG,IAAK,CAAC;MAC7EF,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,iBAAiB,EAAGrB,MAAM,GAAGA,MAAM,CAACsB,QAAQ,CAAC,CAAC,GAAG,GAAI,CAAC;MAC/EF,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAACxB,SAAS,CAACa,MAAM,CAACY,QAAQ,CAAC,CAAC,CAAC;MAC5E,OAAO,IAAI/B,YAAY,CAAC;QAAE4B,IAAI;QAAEC;MAAQ,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;EACP;AAEJ,CAAC,EADYzB,iBAAA,CAAK4B,cAAc,GAAG,MAAM,EAAE,EAAA5B,iBAAA,CAC1C;AACDD,gBAAgB,GAAGN,UAAU,CAAC,CAC1BC,UAAU,CAAC,CAAC,CACf,EAAEK,gBAAgB,CAAC;AACpB,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}