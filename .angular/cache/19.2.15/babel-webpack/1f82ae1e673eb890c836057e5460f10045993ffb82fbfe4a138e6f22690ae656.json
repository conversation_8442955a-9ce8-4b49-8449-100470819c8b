{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _NumericRangeMenuComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-numeric-range-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-numeric-range-menu.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { ChangeDetectionStrategy, Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormBuilder, UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { BehaviorSubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nfunction toControlValue(val) {\n  const {\n    from,\n    to\n  } = val || {};\n  return `${from || ''}${!!from && !!to ? ' - ' : ''}${to || ''}`;\n}\nconst CONTROL_NAME = 'lib-swui-numeric-range-menu';\nlet nextUniqueId = 0;\nlet NumericRangeMenuComponent = (_NumericRangeMenuComponent = class NumericRangeMenuComponent extends SwuiMatFormFieldControl {\n  set value(val) {\n    const value = _objectSpread({}, val);\n    this.transformFromValue(value);\n    this.valueControl.setValue(toControlValue(value));\n    this._value$.next(value);\n  }\n  get value() {\n    return this._value$.value;\n  }\n  set divider(divider) {\n    this._divider = divider || 1;\n    const value = _objectSpread({}, this.form.value);\n    this.transformToValue(value);\n    this.onChange(value);\n  }\n  get divider() {\n    return this._divider || 1;\n  }\n  set requiredField(value) {\n    this._requiredField = value;\n    if (!value) {\n      this.form.enable({\n        emitEvent: false\n      });\n      return;\n    }\n    this.form.disable({\n      emitEvent: false\n    });\n  }\n  get requiredField() {\n    return this._requiredField;\n  }\n  get empty() {\n    return !this.valueControl.value;\n  }\n  get shouldLabelFloat() {\n    return !this.empty;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, fb) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.title = '';\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this.controlType = CONTROL_NAME;\n    this.valueControl = new UntypedFormControl('');\n    this.selectedIndex = 0;\n    this._divider = 1;\n    this._value$ = new BehaviorSubject({\n      from: '',\n      to: ''\n    });\n    this._requiredField = '';\n    this.form = fb.group({\n      from: [],\n      to: []\n    });\n  }\n  ngOnInit() {\n    this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(val => {\n      this._value$.next(val);\n    });\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.elRef && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n    }\n  }\n  writeValue(val) {\n    const empty = {\n      from: '',\n      to: ''\n    };\n    const value = _objectSpread(_objectSpread({}, empty), val);\n    this.transformFromValue(value);\n    this.form.setValue(value, {\n      emitEvent: false\n    });\n    this.valueControl.setValue(toControlValue(value));\n  }\n  get fromControl() {\n    return this.form.get('from');\n  }\n  get toControl() {\n    return this.form.get('to');\n  }\n  prevent(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  clear(event) {\n    event.preventDefault();\n    this.form.setValue({\n      from: '',\n      to: ''\n    });\n  }\n  cancel(event) {\n    event.preventDefault();\n    if (this.menuTriggerRef) {\n      this.menuTriggerRef.closeMenu();\n    }\n  }\n  apply(event) {\n    event.preventDefault();\n    this._value$.next(this.form.value);\n    const value = _objectSpread({}, this.form.value);\n    this.transformToValue(value);\n    this.valueControl.setValue(toControlValue(this.form.value));\n    this.onChange(value);\n    if (this.menuTriggerRef) {\n      this.menuTriggerRef.closeMenu();\n    }\n  }\n  onSelectedIndexChange(tabIndex) {\n    this.selectedIndex = tabIndex;\n  }\n  onDisabledState(disabled) {\n    disabled ? this.valueControl.disable() : this.valueControl.enable();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n  transformFromValue(value) {\n    value.from = value.from && `${Number(value.from) / this.divider}`;\n    value.to = value.to && `${Number(value.to) / this.divider}`;\n  }\n  transformToValue(value) {\n    value.from = value.from && `${Number(value.from) * this.divider}`;\n    value.to = value.to && `${Number(value.to) * this.divider}`;\n  }\n}, _NumericRangeMenuComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}, {\n  type: UntypedFormBuilder\n}], _NumericRangeMenuComponent.propDecorators = {\n  value: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  divider: [{\n    type: Input\n  }],\n  requiredField: [{\n    type: Input\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }],\n  menuTriggerRef: [{\n    type: ViewChild,\n    args: ['range', {\n      read: MatMenuTrigger\n    }]\n  }],\n  input: [{\n    type: ViewChild,\n    args: ['input']\n  }]\n}, _NumericRangeMenuComponent);\nNumericRangeMenuComponent = __decorate([Component({\n  selector: 'lib-swui-numeric-range-menu',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: NumericRangeMenuComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], NumericRangeMenuComponent);\nexport { NumericRangeMenuComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "FocusMonitor", "ChangeDetectionStrategy", "Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormBuilder", "UntypedFormControl", "FormGroupDirective", "NgControl", "MatFormFieldControl", "MatMenuTrigger", "BehaviorSubject", "takeUntil", "SwuiMatFormFieldControl", "ErrorStateMatcher", "toControlValue", "val", "from", "to", "CONTROL_NAME", "nextUniqueId", "NumericRangeMenuComponent", "_NumericRangeMenuComponent", "value", "_objectSpread", "transformFromValue", "valueControl", "setValue", "_value$", "next", "divider", "_divider", "form", "transformToValue", "onChange", "requiredField", "_requiredField", "enable", "emitEvent", "disable", "empty", "shouldLabelFloat", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "fb", "title", "id", "controlType", "selectedIndex", "group", "ngOnInit", "valueChanges", "pipe", "destroyed$", "subscribe", "onContainerClick", "event", "stopPropagation", "target", "tagName", "toLowerCase", "disabled", "nativeElement", "focus", "writeValue", "fromControl", "get", "toControl", "prevent", "preventDefault", "clear", "cancel", "menuTriggerRef", "closeMenu", "apply", "onSelectedIndexChange", "tabIndex", "onDisabledState", "isErrorState", "input", "errorState", "Number", "ctorParameters", "type", "decorators", "propDecorators", "args", "read", "selector", "template", "changeDetection", "OnPush", "providers", "provide", "useExisting", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-numeric-range-menu/swui-numeric-range-menu.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-numeric-range-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-numeric-range-menu.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { ChangeDetectionStrategy, Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormBuilder, UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { BehaviorSubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nfunction toControlValue(val) {\n    const { from, to } = val || {};\n    return `${from || ''}${!!from && !!to ? ' - ' : ''}${to || ''}`;\n}\nconst CONTROL_NAME = 'lib-swui-numeric-range-menu';\nlet nextUniqueId = 0;\nlet NumericRangeMenuComponent = class NumericRangeMenuComponent extends SwuiMatFormFieldControl {\n    set value(val) {\n        const value = { ...val };\n        this.transformFromValue(value);\n        this.valueControl.setValue(toControlValue(value));\n        this._value$.next(value);\n    }\n    get value() {\n        return this._value$.value;\n    }\n    set divider(divider) {\n        this._divider = divider || 1;\n        const value = { ...this.form.value };\n        this.transformToValue(value);\n        this.onChange(value);\n    }\n    get divider() {\n        return this._divider || 1;\n    }\n    set requiredField(value) {\n        this._requiredField = value;\n        if (!value) {\n            this.form.enable({ emitEvent: false });\n            return;\n        }\n        this.form.disable({ emitEvent: false });\n    }\n    get requiredField() {\n        return this._requiredField;\n    }\n    get empty() {\n        return !this.valueControl.value;\n    }\n    get shouldLabelFloat() {\n        return !this.empty;\n    }\n    constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, fb) {\n        super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n        this.title = '';\n        this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n        this.controlType = CONTROL_NAME;\n        this.valueControl = new UntypedFormControl('');\n        this.selectedIndex = 0;\n        this._divider = 1;\n        this._value$ = new BehaviorSubject({ from: '', to: '' });\n        this._requiredField = '';\n        this.form = fb.group({\n            from: [],\n            to: []\n        });\n    }\n    ngOnInit() {\n        this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(val => {\n            this._value$.next(val);\n        });\n    }\n    onContainerClick(event) {\n        event.stopPropagation();\n        if (this.elRef && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n            this.elRef.nativeElement.focus();\n        }\n    }\n    writeValue(val) {\n        const empty = { from: '', to: '' };\n        const value = { ...empty, ...val };\n        this.transformFromValue(value);\n        this.form.setValue(value, { emitEvent: false });\n        this.valueControl.setValue(toControlValue(value));\n    }\n    get fromControl() {\n        return this.form.get('from');\n    }\n    get toControl() {\n        return this.form.get('to');\n    }\n    prevent(event) {\n        event.preventDefault();\n        event.stopPropagation();\n    }\n    clear(event) {\n        event.preventDefault();\n        this.form.setValue({ from: '', to: '' });\n    }\n    cancel(event) {\n        event.preventDefault();\n        if (this.menuTriggerRef) {\n            this.menuTriggerRef.closeMenu();\n        }\n    }\n    apply(event) {\n        event.preventDefault();\n        this._value$.next(this.form.value);\n        const value = { ...this.form.value };\n        this.transformToValue(value);\n        this.valueControl.setValue(toControlValue(this.form.value));\n        this.onChange(value);\n        if (this.menuTriggerRef) {\n            this.menuTriggerRef.closeMenu();\n        }\n    }\n    onSelectedIndexChange(tabIndex) {\n        this.selectedIndex = tabIndex;\n    }\n    onDisabledState(disabled) {\n        disabled ? this.valueControl.disable() : this.valueControl.enable();\n    }\n    isErrorState() {\n        if (this.input) {\n            return this.input.errorState;\n        }\n        return false;\n    }\n    transformFromValue(value) {\n        value.from = value.from && `${Number(value.from) / this.divider}`;\n        value.to = value.to && `${Number(value.to) / this.divider}`;\n    }\n    transformToValue(value) {\n        value.from = value.from && `${Number(value.from) * this.divider}`;\n        value.to = value.to && `${Number(value.to) * this.divider}`;\n    }\n    static { this.ctorParameters = () => [\n        { type: FocusMonitor },\n        { type: ElementRef },\n        { type: NgControl, decorators: [{ type: Optional }, { type: Self }] },\n        { type: FormGroupDirective, decorators: [{ type: Optional }] },\n        { type: ErrorStateMatcher },\n        { type: UntypedFormBuilder }\n    ]; }\n    static { this.propDecorators = {\n        value: [{ type: Input }],\n        title: [{ type: Input }],\n        divider: [{ type: Input }],\n        requiredField: [{ type: Input }],\n        id: [{ type: HostBinding }],\n        shouldLabelFloat: [{ type: HostBinding, args: ['class.floating',] }],\n        menuTriggerRef: [{ type: ViewChild, args: ['range', { read: MatMenuTrigger },] }],\n        input: [{ type: ViewChild, args: ['input',] }]\n    }; }\n};\nNumericRangeMenuComponent = __decorate([\n    Component({\n        selector: 'lib-swui-numeric-range-menu',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        providers: [{ provide: MatFormFieldControl, useExisting: NumericRangeMenuComponent }],\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], NumericRangeMenuComponent);\nexport { NumericRangeMenuComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,qDAAqD;AACtF,OAAOC,oBAAoB,MAAM,qDAAqD;AACtF,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,uBAAuB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AAC7H,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AACtG,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,cAAcA,CAACC,GAAG,EAAE;EACzB,MAAM;IAAEC,IAAI;IAAEC;EAAG,CAAC,GAAGF,GAAG,IAAI,CAAC,CAAC;EAC9B,OAAO,GAAGC,IAAI,IAAI,EAAE,GAAG,CAAC,CAACA,IAAI,IAAI,CAAC,CAACC,EAAE,GAAG,KAAK,GAAG,EAAE,GAAGA,EAAE,IAAI,EAAE,EAAE;AACnE;AACA,MAAMC,YAAY,GAAG,6BAA6B;AAClD,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,SAASR,uBAAuB,CAAC;EAC5F,IAAIU,KAAKA,CAACP,GAAG,EAAE;IACX,MAAMO,KAAK,GAAAC,aAAA,KAAQR,GAAG,CAAE;IACxB,IAAI,CAACS,kBAAkB,CAACF,KAAK,CAAC;IAC9B,IAAI,CAACG,YAAY,CAACC,QAAQ,CAACZ,cAAc,CAACQ,KAAK,CAAC,CAAC;IACjD,IAAI,CAACK,OAAO,CAACC,IAAI,CAACN,KAAK,CAAC;EAC5B;EACA,IAAIA,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACK,OAAO,CAACL,KAAK;EAC7B;EACA,IAAIO,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAGD,OAAO,IAAI,CAAC;IAC5B,MAAMP,KAAK,GAAAC,aAAA,KAAQ,IAAI,CAACQ,IAAI,CAACT,KAAK,CAAE;IACpC,IAAI,CAACU,gBAAgB,CAACV,KAAK,CAAC;IAC5B,IAAI,CAACW,QAAQ,CAACX,KAAK,CAAC;EACxB;EACA,IAAIO,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ,IAAI,CAAC;EAC7B;EACA,IAAII,aAAaA,CAACZ,KAAK,EAAE;IACrB,IAAI,CAACa,cAAc,GAAGb,KAAK;IAC3B,IAAI,CAACA,KAAK,EAAE;MACR,IAAI,CAACS,IAAI,CAACK,MAAM,CAAC;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MACtC;IACJ;IACA,IAAI,CAACN,IAAI,CAACO,OAAO,CAAC;MAAED,SAAS,EAAE;IAAM,CAAC,CAAC;EAC3C;EACA,IAAIH,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAII,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACd,YAAY,CAACH,KAAK;EACnC;EACA,IAAIkB,gBAAgBA,CAAA,EAAG;IACnB,OAAO,CAAC,IAAI,CAACD,KAAK;EACtB;EACAE,WAAWA,CAACC,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,EAAE,EAAE;IACtE,KAAK,CAACL,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAC/D,IAAI,CAACE,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,EAAE,GAAG,GAAG/B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAC7C,IAAI,CAAC+B,WAAW,GAAGhC,YAAY;IAC/B,IAAI,CAACO,YAAY,GAAG,IAAIpB,kBAAkB,CAAC,EAAE,CAAC;IAC9C,IAAI,CAAC8C,aAAa,GAAG,CAAC;IACtB,IAAI,CAACrB,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACH,OAAO,GAAG,IAAIjB,eAAe,CAAC;MAAEM,IAAI,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC,CAAC;IACxD,IAAI,CAACkB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACJ,IAAI,GAAGgB,EAAE,CAACK,KAAK,CAAC;MACjBpC,IAAI,EAAE,EAAE;MACRC,EAAE,EAAE;IACR,CAAC,CAAC;EACN;EACAoC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACtB,IAAI,CAACuB,YAAY,CAACC,IAAI,CAAC5C,SAAS,CAAC,IAAI,CAAC6C,UAAU,CAAC,CAAC,CAACC,SAAS,CAAC1C,GAAG,IAAI;MACrE,IAAI,CAACY,OAAO,CAACC,IAAI,CAACb,GAAG,CAAC;IAC1B,CAAC,CAAC;EACN;EACA2C,gBAAgBA,CAACC,KAAK,EAAE;IACpBA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACjB,KAAK,IAAIgB,KAAK,CAACE,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAChF,IAAI,CAACrB,KAAK,CAACsB,aAAa,CAACC,KAAK,CAAC,CAAC;IACpC;EACJ;EACAC,UAAUA,CAACpD,GAAG,EAAE;IACZ,MAAMwB,KAAK,GAAG;MAAEvB,IAAI,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IAClC,MAAMK,KAAK,GAAAC,aAAA,CAAAA,aAAA,KAAQgB,KAAK,GAAKxB,GAAG,CAAE;IAClC,IAAI,CAACS,kBAAkB,CAACF,KAAK,CAAC;IAC9B,IAAI,CAACS,IAAI,CAACL,QAAQ,CAACJ,KAAK,EAAE;MAAEe,SAAS,EAAE;IAAM,CAAC,CAAC;IAC/C,IAAI,CAACZ,YAAY,CAACC,QAAQ,CAACZ,cAAc,CAACQ,KAAK,CAAC,CAAC;EACrD;EACA,IAAI8C,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrC,IAAI,CAACsC,GAAG,CAAC,MAAM,CAAC;EAChC;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACvC,IAAI,CAACsC,GAAG,CAAC,IAAI,CAAC;EAC9B;EACAE,OAAOA,CAACZ,KAAK,EAAE;IACXA,KAAK,CAACa,cAAc,CAAC,CAAC;IACtBb,KAAK,CAACC,eAAe,CAAC,CAAC;EAC3B;EACAa,KAAKA,CAACd,KAAK,EAAE;IACTA,KAAK,CAACa,cAAc,CAAC,CAAC;IACtB,IAAI,CAACzC,IAAI,CAACL,QAAQ,CAAC;MAAEV,IAAI,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC,CAAC;EAC5C;EACAyD,MAAMA,CAACf,KAAK,EAAE;IACVA,KAAK,CAACa,cAAc,CAAC,CAAC;IACtB,IAAI,IAAI,CAACG,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACC,SAAS,CAAC,CAAC;IACnC;EACJ;EACAC,KAAKA,CAAClB,KAAK,EAAE;IACTA,KAAK,CAACa,cAAc,CAAC,CAAC;IACtB,IAAI,CAAC7C,OAAO,CAACC,IAAI,CAAC,IAAI,CAACG,IAAI,CAACT,KAAK,CAAC;IAClC,MAAMA,KAAK,GAAAC,aAAA,KAAQ,IAAI,CAACQ,IAAI,CAACT,KAAK,CAAE;IACpC,IAAI,CAACU,gBAAgB,CAACV,KAAK,CAAC;IAC5B,IAAI,CAACG,YAAY,CAACC,QAAQ,CAACZ,cAAc,CAAC,IAAI,CAACiB,IAAI,CAACT,KAAK,CAAC,CAAC;IAC3D,IAAI,CAACW,QAAQ,CAACX,KAAK,CAAC;IACpB,IAAI,IAAI,CAACqD,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACC,SAAS,CAAC,CAAC;IACnC;EACJ;EACAE,qBAAqBA,CAACC,QAAQ,EAAE;IAC5B,IAAI,CAAC5B,aAAa,GAAG4B,QAAQ;EACjC;EACAC,eAAeA,CAAChB,QAAQ,EAAE;IACtBA,QAAQ,GAAG,IAAI,CAACvC,YAAY,CAACa,OAAO,CAAC,CAAC,GAAG,IAAI,CAACb,YAAY,CAACW,MAAM,CAAC,CAAC;EACvE;EACA6C,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,OAAO,IAAI,CAACA,KAAK,CAACC,UAAU;IAChC;IACA,OAAO,KAAK;EAChB;EACA3D,kBAAkBA,CAACF,KAAK,EAAE;IACtBA,KAAK,CAACN,IAAI,GAAGM,KAAK,CAACN,IAAI,IAAI,GAAGoE,MAAM,CAAC9D,KAAK,CAACN,IAAI,CAAC,GAAG,IAAI,CAACa,OAAO,EAAE;IACjEP,KAAK,CAACL,EAAE,GAAGK,KAAK,CAACL,EAAE,IAAI,GAAGmE,MAAM,CAAC9D,KAAK,CAACL,EAAE,CAAC,GAAG,IAAI,CAACY,OAAO,EAAE;EAC/D;EACAG,gBAAgBA,CAACV,KAAK,EAAE;IACpBA,KAAK,CAACN,IAAI,GAAGM,KAAK,CAACN,IAAI,IAAI,GAAGoE,MAAM,CAAC9D,KAAK,CAACN,IAAI,CAAC,GAAG,IAAI,CAACa,OAAO,EAAE;IACjEP,KAAK,CAACL,EAAE,GAAGK,KAAK,CAACL,EAAE,IAAI,GAAGmE,MAAM,CAAC9D,KAAK,CAACL,EAAE,CAAC,GAAG,IAAI,CAACY,OAAO,EAAE;EAC/D;AAmBJ,CAAC,EAlBYR,0BAAA,CAAKgE,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE3F;AAAa,CAAC,EACtB;EAAE2F,IAAI,EAAExF;AAAW,CAAC,EACpB;EAAEwF,IAAI,EAAE/E,SAAS;EAAEgF,UAAU,EAAE,CAAC;IAAED,IAAI,EAAErF;EAAS,CAAC,EAAE;IAAEqF,IAAI,EAAEpF;EAAK,CAAC;AAAE,CAAC,EACrE;EAAEoF,IAAI,EAAEhF,kBAAkB;EAAEiF,UAAU,EAAE,CAAC;IAAED,IAAI,EAAErF;EAAS,CAAC;AAAE,CAAC,EAC9D;EAAEqF,IAAI,EAAEzE;AAAkB,CAAC,EAC3B;EAAEyE,IAAI,EAAElF;AAAmB,CAAC,CAC/B,EACQiB,0BAAA,CAAKmE,cAAc,GAAG;EAC3BlE,KAAK,EAAE,CAAC;IAAEgE,IAAI,EAAEtF;EAAM,CAAC,CAAC;EACxBgD,KAAK,EAAE,CAAC;IAAEsC,IAAI,EAAEtF;EAAM,CAAC,CAAC;EACxB6B,OAAO,EAAE,CAAC;IAAEyD,IAAI,EAAEtF;EAAM,CAAC,CAAC;EAC1BkC,aAAa,EAAE,CAAC;IAAEoD,IAAI,EAAEtF;EAAM,CAAC,CAAC;EAChCiD,EAAE,EAAE,CAAC;IAAEqC,IAAI,EAAEvF;EAAY,CAAC,CAAC;EAC3ByC,gBAAgB,EAAE,CAAC;IAAE8C,IAAI,EAAEvF,WAAW;IAAE0F,IAAI,EAAE,CAAC,gBAAgB;EAAG,CAAC,CAAC;EACpEd,cAAc,EAAE,CAAC;IAAEW,IAAI,EAAEnF,SAAS;IAAEsF,IAAI,EAAE,CAAC,OAAO,EAAE;MAAEC,IAAI,EAAEjF;IAAe,CAAC;EAAG,CAAC,CAAC;EACjFyE,KAAK,EAAE,CAAC;IAAEI,IAAI,EAAEnF,SAAS;IAAEsF,IAAI,EAAE,CAAC,OAAO;EAAG,CAAC;AACjD,CAAC,EAAApE,0BAAA,CACJ;AACDD,yBAAyB,GAAG5B,UAAU,CAAC,CACnCK,SAAS,CAAC;EACN8F,QAAQ,EAAE,6BAA6B;EACvCC,QAAQ,EAAEnG,oBAAoB;EAC9BoG,eAAe,EAAEjG,uBAAuB,CAACkG,MAAM;EAC/CC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAExF,mBAAmB;IAAEyF,WAAW,EAAE7E;EAA0B,CAAC,CAAC;EACrF8E,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACzG,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAE0B,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}