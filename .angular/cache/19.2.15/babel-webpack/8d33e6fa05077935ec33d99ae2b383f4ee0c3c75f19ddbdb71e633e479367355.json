{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nlet SwuiIsControlInvalidService = class SwuiIsControlInvalidService {\n  isControlInvalid(control) {\n    if (control) {\n      return control.touched && control.invalid;\n    }\n    return false;\n  }\n};\nSwuiIsControlInvalidService = __decorate([Injectable({\n  providedIn: 'root'\n})], SwuiIsControlInvalidService);\nexport { SwuiIsControlInvalidService };", "map": {"version": 3, "names": ["__decorate", "Injectable", "SwuiIsControlInvalidService", "isControlInvalid", "control", "touched", "invalid", "providedIn"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-is-control-invalid/swui-is-control-invalid.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nlet SwuiIsControlInvalidService = class SwuiIsControlInvalidService {\n    isControlInvalid(control) {\n        if (control) {\n            return control.touched && control.invalid;\n        }\n        return false;\n    }\n};\nSwuiIsControlInvalidService = __decorate([\n    Injectable({ providedIn: 'root' })\n], SwuiIsControlInvalidService);\nexport { SwuiIsControlInvalidService };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,IAAIC,2BAA2B,GAAG,MAAMA,2BAA2B,CAAC;EAChEC,gBAAgBA,CAACC,OAAO,EAAE;IACtB,IAAIA,OAAO,EAAE;MACT,OAAOA,OAAO,CAACC,OAAO,IAAID,OAAO,CAACE,OAAO;IAC7C;IACA,OAAO,KAAK;EAChB;AACJ,CAAC;AACDJ,2BAA2B,GAAGF,UAAU,CAAC,CACrCC,UAAU,CAAC;EAAEM,UAAU,EAAE;AAAO,CAAC,CAAC,CACrC,EAAEL,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}