{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BulkActionsComponent } from './bulk-actions.component';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { ConfirmDialogComponent } from './dialogs/confirm-dialog.component';\nimport { LessDialogComponent } from './dialogs/less-dialog.component';\nimport { NoDataDialogComponent } from './dialogs/no-data-dialog.component';\nexport const matModules = [MatMenuModule, MatDialogModule, MatButtonModule, MatIconModule, MatTooltipModule];\nlet SwuiGridBulkActionsModule = class SwuiGridBulkActionsModule {};\nSwuiGridBulkActionsModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, ...matModules],\n  declarations: [BulkActionsComponent, ConfirmDialogComponent, LessDialogComponent, NoDataDialogComponent],\n  exports: [BulkActionsComponent]\n})], SwuiGridBulkActionsModule);\nexport { SwuiGridBulkActionsModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "BulkActionsComponent", "CommonModule", "TranslateModule", "MatButtonModule", "MatDialogModule", "MatIconModule", "MatMenuModule", "MatTooltipModule", "ConfirmDialogComponent", "LessDialogComponent", "NoDataDialogComponent", "matModules", "SwuiGridBulkActionsModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/bulk-actions/bulk-actions.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BulkActionsComponent } from './bulk-actions.component';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { ConfirmDialogComponent } from './dialogs/confirm-dialog.component';\nimport { LessDialogComponent } from './dialogs/less-dialog.component';\nimport { NoDataDialogComponent } from './dialogs/no-data-dialog.component';\nexport const matModules = [\n    MatMenuModule,\n    MatDialogModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTooltipModule,\n];\nlet SwuiGridBulkActionsModule = class SwuiGridBulkActionsModule {\n};\nSwuiGridBulkActionsModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule,\n            ...matModules,\n        ],\n        declarations: [\n            BulkActionsComponent,\n            ConfirmDialogComponent,\n            LessDialogComponent,\n            NoDataDialogComponent,\n        ],\n        exports: [\n            BulkActionsComponent,\n        ]\n    })\n], SwuiGridBulkActionsModule);\nexport { SwuiGridBulkActionsModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,OAAO,MAAMC,UAAU,GAAG,CACtBL,aAAa,EACbF,eAAe,EACfD,eAAe,EACfE,aAAa,EACbE,gBAAgB,CACnB;AACD,IAAIK,yBAAyB,GAAG,MAAMA,yBAAyB,CAAC,EAC/D;AACDA,yBAAyB,GAAGd,UAAU,CAAC,CACnCC,QAAQ,CAAC;EACLc,OAAO,EAAE,CACLZ,YAAY,EACZC,eAAe,EACf,GAAGS,UAAU,CAChB;EACDG,YAAY,EAAE,CACVd,oBAAoB,EACpBQ,sBAAsB,EACtBC,mBAAmB,EACnBC,qBAAqB,CACxB;EACDK,OAAO,EAAE,CACLf,oBAAoB;AAE5B,CAAC,CAAC,CACL,EAAEY,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}