{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatOptionModule;\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatOption, a as MatOptgroup } from './option-ChV6uQgD.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-CAX2sutq.mjs';\nclass MatOptionModule {}\n_MatOptionModule = MatOptionModule;\n_defineProperty(MatOptionModule, \"\\u0275fac\", function _MatOptionModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatOptionModule)();\n});\n_defineProperty(MatOptionModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatOptionModule,\n  imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n  exports: [MatOption, MatOptgroup]\n}));\n_defineProperty(MatOptionModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n      exports: [MatOption, MatOptgroup]\n    }]\n  }], null, null);\n})();\nexport { MatOptionModule as M };\n//# sourceMappingURL=index-DOxJc1m4.mjs.map", "map": {"version": 3, "names": ["i0", "NgModule", "M", "MatRippleModule", "MatCommonModule", "MatOption", "a", "MatOptgroup", "MatPseudoCheckboxModule", "MatOptionModule", "_MatOptionModule", "_defineProperty", "_MatOptionModule_Factory", "__ngFactoryType__", "ɵɵdefineNgModule", "type", "imports", "exports", "ɵɵdefineInjector", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/index-DOxJc1m4.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatOption, a as MatOptgroup } from './option-ChV6uQgD.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-CAX2sutq.mjs';\n\nclass MatOptionModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatOptionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatOptionModule, imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup], exports: [MatOption, MatOptgroup] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatOptionModule, imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatOptionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n                    exports: [MatOption, MatOptgroup],\n                }]\n        }] });\n\nexport { MatOptionModule as M };\n//# sourceMappingURL=index-DOxJc1m4.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,eAAe,QAAQ,sBAAsB;AAC3D,SAASD,CAAC,IAAIE,eAAe,QAAQ,8BAA8B;AACnE,SAASF,CAAC,IAAIG,SAAS,EAAEC,CAAC,IAAIC,WAAW,QAAQ,uBAAuB;AACxE,SAASL,CAAC,IAAIM,uBAAuB,QAAQ,uCAAuC;AAEpF,MAAMC,eAAe,CAAC;AAIrBC,gBAAA,GAJKD,eAAe;AAAAE,eAAA,CAAfF,eAAe,wBAAAG,yBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IACkFJ,gBAAe;AAAA;AAAAE,eAAA,CADhHF,eAAe,8BAK4DT,EAAE,CAAAc,gBAAA;EAAAC,IAAA,EAHqBN,gBAAe;EAAAO,OAAA,GAAYb,eAAe,EAAEC,eAAe,EAAEI,uBAAuB,EAAEH,SAAS,EAAEE,WAAW;EAAAU,OAAA,GAAaZ,SAAS,EAAEE,WAAW;AAAA;AAAAI,eAAA,CAFjPF,eAAe,8BAK4DT,EAAE,CAAAkB,gBAAA;EAAAF,OAAA,GAFgDb,eAAe,EAAEC,eAAe,EAAEI,uBAAuB;AAAA;AAE5L;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KAAiFnB,EAAE,CAAAoB,iBAAA,CAAQX,eAAe,EAAc,CAAC;IAC7GM,IAAI,EAAEd,QAAQ;IACdoB,IAAI,EAAE,CAAC;MACCL,OAAO,EAAE,CAACb,eAAe,EAAEC,eAAe,EAAEI,uBAAuB,EAAEH,SAAS,EAAEE,WAAW,CAAC;MAC5FU,OAAO,EAAE,CAACZ,SAAS,EAAEE,WAAW;IACpC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASE,eAAe,IAAIP,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}