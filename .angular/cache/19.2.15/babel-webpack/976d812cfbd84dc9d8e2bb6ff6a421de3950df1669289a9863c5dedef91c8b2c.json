{"ast": null, "code": "import { __asyncValues, __awaiter, __generator, __values } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n  if (input instanceof Observable) {\n    return input;\n  }\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return fromInteropObservable(input);\n    }\n    if (isArrayLike(input)) {\n      return fromArrayLike(input);\n    }\n    if (isPromise(input)) {\n      return fromPromise(input);\n    }\n    if (isAsyncIterable(input)) {\n      return fromAsyncIterable(input);\n    }\n    if (isIterable(input)) {\n      return fromIterable(input);\n    }\n    if (isReadableStreamLike(input)) {\n      return fromReadableStreamLike(input);\n    }\n  }\n  throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n  return new Observable(function (subscriber) {\n    var obs = obj[Symbol_observable]();\n    if (isFunction(obs.subscribe)) {\n      return obs.subscribe(subscriber);\n    }\n    throw new TypeError('Provided object does not correctly implement Symbol.observable');\n  });\n}\nexport function fromArrayLike(array) {\n  return new Observable(function (subscriber) {\n    for (var i = 0; i < array.length && !subscriber.closed; i++) {\n      subscriber.next(array[i]);\n    }\n    subscriber.complete();\n  });\n}\nexport function fromPromise(promise) {\n  return new Observable(function (subscriber) {\n    promise.then(function (value) {\n      if (!subscriber.closed) {\n        subscriber.next(value);\n        subscriber.complete();\n      }\n    }, function (err) {\n      return subscriber.error(err);\n    }).then(null, reportUnhandledError);\n  });\n}\nexport function fromIterable(iterable) {\n  return new Observable(function (subscriber) {\n    var e_1, _a;\n    try {\n      for (var iterable_1 = __values(iterable), iterable_1_1 = iterable_1.next(); !iterable_1_1.done; iterable_1_1 = iterable_1.next()) {\n        var value = iterable_1_1.value;\n        subscriber.next(value);\n        if (subscriber.closed) {\n          return;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (iterable_1_1 && !iterable_1_1.done && (_a = iterable_1.return)) _a.call(iterable_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    subscriber.complete();\n  });\n}\nexport function fromAsyncIterable(asyncIterable) {\n  return new Observable(function (subscriber) {\n    process(asyncIterable, subscriber).catch(function (err) {\n      return subscriber.error(err);\n    });\n  });\n}\nexport function fromReadableStreamLike(readableStream) {\n  return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\nfunction process(asyncIterable, subscriber) {\n  var asyncIterable_1, asyncIterable_1_1;\n  var e_2, _a;\n  return __awaiter(this, void 0, void 0, function () {\n    var value, e_2_1;\n    return __generator(this, function (_b) {\n      switch (_b.label) {\n        case 0:\n          _b.trys.push([0, 5, 6, 11]);\n          asyncIterable_1 = __asyncValues(asyncIterable);\n          _b.label = 1;\n        case 1:\n          return [4, asyncIterable_1.next()];\n        case 2:\n          if (!(asyncIterable_1_1 = _b.sent(), !asyncIterable_1_1.done)) return [3, 4];\n          value = asyncIterable_1_1.value;\n          subscriber.next(value);\n          if (subscriber.closed) {\n            return [2];\n          }\n          _b.label = 3;\n        case 3:\n          return [3, 1];\n        case 4:\n          return [3, 11];\n        case 5:\n          e_2_1 = _b.sent();\n          e_2 = {\n            error: e_2_1\n          };\n          return [3, 11];\n        case 6:\n          _b.trys.push([6,, 9, 10]);\n          if (!(asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return))) return [3, 8];\n          return [4, _a.call(asyncIterable_1)];\n        case 7:\n          _b.sent();\n          _b.label = 8;\n        case 8:\n          return [3, 10];\n        case 9:\n          if (e_2) throw e_2.error;\n          return [7];\n        case 10:\n          return [7];\n        case 11:\n          subscriber.complete();\n          return [2];\n      }\n    });\n  });\n}\n//# sourceMappingURL=innerFrom.js.map", "map": {"version": 3, "names": ["__asyncValues", "__awaiter", "__generator", "__values", "isArrayLike", "isPromise", "Observable", "isInteropObservable", "isAsyncIterable", "createInvalidObservableTypeError", "isIterable", "isReadableStreamLike", "readableStreamLikeToAsyncGenerator", "isFunction", "reportUnhandledError", "observable", "Symbol_observable", "innerFrom", "input", "fromInteropObservable", "fromArrayLike", "fromPromise", "fromAsyncIterable", "fromIterable", "fromReadableStreamLike", "obj", "subscriber", "obs", "subscribe", "TypeError", "array", "i", "length", "closed", "next", "complete", "promise", "then", "value", "err", "error", "iterable", "e_1", "_a", "iterable_1", "iterable_1_1", "done", "e_1_1", "return", "call", "asyncIterable", "process", "catch", "readableStream", "asyncIterable_1", "asyncIterable_1_1", "e_2", "e_2_1", "_b", "label", "trys", "push", "sent"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/innerFrom.js"], "sourcesContent": ["import { __asyncValues, __awaiter, __generator, __values } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n    if (input instanceof Observable) {\n        return input;\n    }\n    if (input != null) {\n        if (isInteropObservable(input)) {\n            return fromInteropObservable(input);\n        }\n        if (isArrayLike(input)) {\n            return fromArrayLike(input);\n        }\n        if (isPromise(input)) {\n            return fromPromise(input);\n        }\n        if (isAsyncIterable(input)) {\n            return fromAsyncIterable(input);\n        }\n        if (isIterable(input)) {\n            return fromIterable(input);\n        }\n        if (isReadableStreamLike(input)) {\n            return fromReadableStreamLike(input);\n        }\n    }\n    throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n    return new Observable(function (subscriber) {\n        var obs = obj[Symbol_observable]();\n        if (isFunction(obs.subscribe)) {\n            return obs.subscribe(subscriber);\n        }\n        throw new TypeError('Provided object does not correctly implement Symbol.observable');\n    });\n}\nexport function fromArrayLike(array) {\n    return new Observable(function (subscriber) {\n        for (var i = 0; i < array.length && !subscriber.closed; i++) {\n            subscriber.next(array[i]);\n        }\n        subscriber.complete();\n    });\n}\nexport function fromPromise(promise) {\n    return new Observable(function (subscriber) {\n        promise\n            .then(function (value) {\n            if (!subscriber.closed) {\n                subscriber.next(value);\n                subscriber.complete();\n            }\n        }, function (err) { return subscriber.error(err); })\n            .then(null, reportUnhandledError);\n    });\n}\nexport function fromIterable(iterable) {\n    return new Observable(function (subscriber) {\n        var e_1, _a;\n        try {\n            for (var iterable_1 = __values(iterable), iterable_1_1 = iterable_1.next(); !iterable_1_1.done; iterable_1_1 = iterable_1.next()) {\n                var value = iterable_1_1.value;\n                subscriber.next(value);\n                if (subscriber.closed) {\n                    return;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (iterable_1_1 && !iterable_1_1.done && (_a = iterable_1.return)) _a.call(iterable_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        subscriber.complete();\n    });\n}\nexport function fromAsyncIterable(asyncIterable) {\n    return new Observable(function (subscriber) {\n        process(asyncIterable, subscriber).catch(function (err) { return subscriber.error(err); });\n    });\n}\nexport function fromReadableStreamLike(readableStream) {\n    return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\nfunction process(asyncIterable, subscriber) {\n    var asyncIterable_1, asyncIterable_1_1;\n    var e_2, _a;\n    return __awaiter(this, void 0, void 0, function () {\n        var value, e_2_1;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    _b.trys.push([0, 5, 6, 11]);\n                    asyncIterable_1 = __asyncValues(asyncIterable);\n                    _b.label = 1;\n                case 1: return [4, asyncIterable_1.next()];\n                case 2:\n                    if (!(asyncIterable_1_1 = _b.sent(), !asyncIterable_1_1.done)) return [3, 4];\n                    value = asyncIterable_1_1.value;\n                    subscriber.next(value);\n                    if (subscriber.closed) {\n                        return [2];\n                    }\n                    _b.label = 3;\n                case 3: return [3, 1];\n                case 4: return [3, 11];\n                case 5:\n                    e_2_1 = _b.sent();\n                    e_2 = { error: e_2_1 };\n                    return [3, 11];\n                case 6:\n                    _b.trys.push([6, , 9, 10]);\n                    if (!(asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return))) return [3, 8];\n                    return [4, _a.call(asyncIterable_1)];\n                case 7:\n                    _b.sent();\n                    _b.label = 8;\n                case 8: return [3, 10];\n                case 9:\n                    if (e_2) throw e_2.error;\n                    return [7];\n                case 10: return [7];\n                case 11:\n                    subscriber.complete();\n                    return [2];\n            }\n        });\n    });\n}\n//# sourceMappingURL=innerFrom.js.map"], "mappings": "AAAA,SAASA,aAAa,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,OAAO;AACvE,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,gCAAgC,QAAQ,gCAAgC;AACjF,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,oBAAoB,EAAEC,kCAAkC,QAAQ,8BAA8B;AACvG,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,sBAAsB;AACtE,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC7B,IAAIA,KAAK,YAAYZ,UAAU,EAAE;IAC7B,OAAOY,KAAK;EAChB;EACA,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,IAAIX,mBAAmB,CAACW,KAAK,CAAC,EAAE;MAC5B,OAAOC,qBAAqB,CAACD,KAAK,CAAC;IACvC;IACA,IAAId,WAAW,CAACc,KAAK,CAAC,EAAE;MACpB,OAAOE,aAAa,CAACF,KAAK,CAAC;IAC/B;IACA,IAAIb,SAAS,CAACa,KAAK,CAAC,EAAE;MAClB,OAAOG,WAAW,CAACH,KAAK,CAAC;IAC7B;IACA,IAAIV,eAAe,CAACU,KAAK,CAAC,EAAE;MACxB,OAAOI,iBAAiB,CAACJ,KAAK,CAAC;IACnC;IACA,IAAIR,UAAU,CAACQ,KAAK,CAAC,EAAE;MACnB,OAAOK,YAAY,CAACL,KAAK,CAAC;IAC9B;IACA,IAAIP,oBAAoB,CAACO,KAAK,CAAC,EAAE;MAC7B,OAAOM,sBAAsB,CAACN,KAAK,CAAC;IACxC;EACJ;EACA,MAAMT,gCAAgC,CAACS,KAAK,CAAC;AACjD;AACA,OAAO,SAASC,qBAAqBA,CAACM,GAAG,EAAE;EACvC,OAAO,IAAInB,UAAU,CAAC,UAAUoB,UAAU,EAAE;IACxC,IAAIC,GAAG,GAAGF,GAAG,CAACT,iBAAiB,CAAC,CAAC,CAAC;IAClC,IAAIH,UAAU,CAACc,GAAG,CAACC,SAAS,CAAC,EAAE;MAC3B,OAAOD,GAAG,CAACC,SAAS,CAACF,UAAU,CAAC;IACpC;IACA,MAAM,IAAIG,SAAS,CAAC,gEAAgE,CAAC;EACzF,CAAC,CAAC;AACN;AACA,OAAO,SAAST,aAAaA,CAACU,KAAK,EAAE;EACjC,OAAO,IAAIxB,UAAU,CAAC,UAAUoB,UAAU,EAAE;IACxC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,IAAI,CAACN,UAAU,CAACO,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzDL,UAAU,CAACQ,IAAI,CAACJ,KAAK,CAACC,CAAC,CAAC,CAAC;IAC7B;IACAL,UAAU,CAACS,QAAQ,CAAC,CAAC;EACzB,CAAC,CAAC;AACN;AACA,OAAO,SAASd,WAAWA,CAACe,OAAO,EAAE;EACjC,OAAO,IAAI9B,UAAU,CAAC,UAAUoB,UAAU,EAAE;IACxCU,OAAO,CACFC,IAAI,CAAC,UAAUC,KAAK,EAAE;MACvB,IAAI,CAACZ,UAAU,CAACO,MAAM,EAAE;QACpBP,UAAU,CAACQ,IAAI,CAACI,KAAK,CAAC;QACtBZ,UAAU,CAACS,QAAQ,CAAC,CAAC;MACzB;IACJ,CAAC,EAAE,UAAUI,GAAG,EAAE;MAAE,OAAOb,UAAU,CAACc,KAAK,CAACD,GAAG,CAAC;IAAE,CAAC,CAAC,CAC/CF,IAAI,CAAC,IAAI,EAAEvB,oBAAoB,CAAC;EACzC,CAAC,CAAC;AACN;AACA,OAAO,SAASS,YAAYA,CAACkB,QAAQ,EAAE;EACnC,OAAO,IAAInC,UAAU,CAAC,UAAUoB,UAAU,EAAE;IACxC,IAAIgB,GAAG,EAAEC,EAAE;IACX,IAAI;MACA,KAAK,IAAIC,UAAU,GAAGzC,QAAQ,CAACsC,QAAQ,CAAC,EAAEI,YAAY,GAAGD,UAAU,CAACV,IAAI,CAAC,CAAC,EAAE,CAACW,YAAY,CAACC,IAAI,EAAED,YAAY,GAAGD,UAAU,CAACV,IAAI,CAAC,CAAC,EAAE;QAC9H,IAAII,KAAK,GAAGO,YAAY,CAACP,KAAK;QAC9BZ,UAAU,CAACQ,IAAI,CAACI,KAAK,CAAC;QACtB,IAAIZ,UAAU,CAACO,MAAM,EAAE;UACnB;QACJ;MACJ;IACJ,CAAC,CACD,OAAOc,KAAK,EAAE;MAAEL,GAAG,GAAG;QAAEF,KAAK,EAAEO;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIF,YAAY,IAAI,CAACA,YAAY,CAACC,IAAI,KAAKH,EAAE,GAAGC,UAAU,CAACI,MAAM,CAAC,EAAEL,EAAE,CAACM,IAAI,CAACL,UAAU,CAAC;MAC3F,CAAC,SACO;QAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACF,KAAK;MAAE;IACxC;IACAd,UAAU,CAACS,QAAQ,CAAC,CAAC;EACzB,CAAC,CAAC;AACN;AACA,OAAO,SAASb,iBAAiBA,CAAC4B,aAAa,EAAE;EAC7C,OAAO,IAAI5C,UAAU,CAAC,UAAUoB,UAAU,EAAE;IACxCyB,OAAO,CAACD,aAAa,EAAExB,UAAU,CAAC,CAAC0B,KAAK,CAAC,UAAUb,GAAG,EAAE;MAAE,OAAOb,UAAU,CAACc,KAAK,CAACD,GAAG,CAAC;IAAE,CAAC,CAAC;EAC9F,CAAC,CAAC;AACN;AACA,OAAO,SAASf,sBAAsBA,CAAC6B,cAAc,EAAE;EACnD,OAAO/B,iBAAiB,CAACV,kCAAkC,CAACyC,cAAc,CAAC,CAAC;AAChF;AACA,SAASF,OAAOA,CAACD,aAAa,EAAExB,UAAU,EAAE;EACxC,IAAI4B,eAAe,EAAEC,iBAAiB;EACtC,IAAIC,GAAG,EAAEb,EAAE;EACX,OAAO1C,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;IAC/C,IAAIqC,KAAK,EAAEmB,KAAK;IAChB,OAAOvD,WAAW,CAAC,IAAI,EAAE,UAAUwD,EAAE,EAAE;MACnC,QAAQA,EAAE,CAACC,KAAK;QACZ,KAAK,CAAC;UACFD,EAAE,CAACE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;UAC3BP,eAAe,GAAGtD,aAAa,CAACkD,aAAa,CAAC;UAC9CQ,EAAE,CAACC,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAEL,eAAe,CAACpB,IAAI,CAAC,CAAC,CAAC;QAC1C,KAAK,CAAC;UACF,IAAI,EAAEqB,iBAAiB,GAAGG,EAAE,CAACI,IAAI,CAAC,CAAC,EAAE,CAACP,iBAAiB,CAACT,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;UAC5ER,KAAK,GAAGiB,iBAAiB,CAACjB,KAAK;UAC/BZ,UAAU,CAACQ,IAAI,CAACI,KAAK,CAAC;UACtB,IAAIZ,UAAU,CAACO,MAAM,EAAE;YACnB,OAAO,CAAC,CAAC,CAAC;UACd;UACAyB,EAAE,CAACC,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC;QACtB,KAAK,CAAC;UACFF,KAAK,GAAGC,EAAE,CAACI,IAAI,CAAC,CAAC;UACjBN,GAAG,GAAG;YAAEhB,KAAK,EAAEiB;UAAM,CAAC;UACtB,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC;QAClB,KAAK,CAAC;UACFC,EAAE,CAACE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAI,CAAC,EAAE,EAAE,CAAC,CAAC;UAC1B,IAAI,EAAEN,iBAAiB,IAAI,CAACA,iBAAiB,CAACT,IAAI,KAAKH,EAAE,GAAGW,eAAe,CAACN,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;UACnG,OAAO,CAAC,CAAC,EAAEL,EAAE,CAACM,IAAI,CAACK,eAAe,CAAC,CAAC;QACxC,KAAK,CAAC;UACFI,EAAE,CAACI,IAAI,CAAC,CAAC;UACTJ,EAAE,CAACC,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC;QACtB,KAAK,CAAC;UACF,IAAIH,GAAG,EAAE,MAAMA,GAAG,CAAChB,KAAK;UACxB,OAAO,CAAC,CAAC,CAAC;QACd,KAAK,EAAE;UAAE,OAAO,CAAC,CAAC,CAAC;QACnB,KAAK,EAAE;UACHd,UAAU,CAACS,QAAQ,CAAC,CAAC;UACrB,OAAO,CAAC,CAAC,CAAC;MAClB;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}