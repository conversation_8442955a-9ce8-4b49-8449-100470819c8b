{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { DefaultWidgetComponent } from './default-widget.component';\nimport { CommonModule } from '@angular/common';\nlet SwuiDefaultWidgetModule = class SwuiDefaultWidgetModule {};\nSwuiDefaultWidgetModule = __decorate([NgModule({\n  imports: [CommonModule],\n  exports: [],\n  declarations: [DefaultWidgetComponent],\n  providers: []\n})], SwuiDefaultWidgetModule);\nexport { SwuiDefaultWidgetModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "DefaultWidgetComponent", "CommonModule", "SwuiDefaultWidgetModule", "imports", "exports", "declarations", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/default-widget/default-widget.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { DefaultWidgetComponent } from './default-widget.component';\nimport { CommonModule } from '@angular/common';\nlet SwuiDefaultWidgetModule = class SwuiDefaultWidgetModule {\n};\nSwuiDefaultWidgetModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule\n        ],\n        exports: [],\n        declarations: [\n            DefaultWidgetComponent\n        ],\n        providers: []\n    })\n], SwuiDefaultWidgetModule);\nexport { SwuiDefaultWidgetModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,IAAIC,uBAAuB,GAAG,MAAMA,uBAAuB,CAAC,EAC3D;AACDA,uBAAuB,GAAGJ,UAAU,CAAC,CACjCC,QAAQ,CAAC;EACLI,OAAO,EAAE,CACLF,YAAY,CACf;EACDG,OAAO,EAAE,EAAE;EACXC,YAAY,EAAE,CACVL,sBAAsB,CACzB;EACDM,SAAS,EAAE;AACf,CAAC,CAAC,CACL,EAAEJ,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}