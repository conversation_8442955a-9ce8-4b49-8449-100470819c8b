{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _CdkPortal, _TemplatePortalDirective, _CdkPortalOutlet, _PortalHostDirective, _PortalModule;\nimport * as i0 from '@angular/core';\nimport { ElementRef, NgModuleRef, EnvironmentInjector, createComponent, Injector, inject, TemplateRef, ViewContainerRef, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' + 'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n  constructor() {\n    _defineProperty(this, \"_attachedHost\", void 0);\n  }\n  /** Attach this portal to a host. */\n  attach(host) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n    this._attachedHost = host;\n    return host.attach(this);\n  }\n  /** Detach this portal from its host */\n  detach() {\n    let host = this._attachedHost;\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n  constructor(component, viewContainerRef, injector,\n  /**\n   * @deprecated No longer in use. To be removed.\n   * @breaking-change 18.0.0\n   */\n  _componentFactoryResolver, projectableNodes) {\n    super();\n    /** The type of the component that will be instantiated for attachment. */\n    _defineProperty(this, \"component\", void 0);\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This is different from where the component *renders*, which is determined by the PortalOutlet.\n     * The origin is necessary when the host is outside of the Angular application context.\n     */\n    _defineProperty(this, \"viewContainerRef\", void 0);\n    /** Injector used for the instantiation of the component. */\n    _defineProperty(this, \"injector\", void 0);\n    /**\n     * @deprecated No longer in use. To be removed.\n     * @breaking-change 18.0.0\n     */\n    _defineProperty(this, \"componentFactoryResolver\", void 0);\n    /**\n     * List of DOM nodes that should be projected through `<ng-content>` of the attached component.\n     */\n    _defineProperty(this, \"projectableNodes\", void 0);\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.projectableNodes = projectableNodes;\n  }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n  constructor(/** The embedded template that will be used to instantiate an embedded View in the host. */\n  templateRef, /** Reference to the ViewContainer into which the template will be stamped out. */\n  viewContainerRef, /** Contextual data to be passed in to the embedded view. */\n  context, /** The injector to use for the embedded view. */\n  injector) {\n    super();\n    _defineProperty(this, \"templateRef\", void 0);\n    _defineProperty(this, \"viewContainerRef\", void 0);\n    _defineProperty(this, \"context\", void 0);\n    _defineProperty(this, \"injector\", void 0);\n    this.templateRef = templateRef;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n    this.injector = injector;\n  }\n  get origin() {\n    return this.templateRef.elementRef;\n  }\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  attach(host, context = this.context) {\n    this.context = context;\n    return super.attach(host);\n  }\n  detach() {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n  constructor(element) {\n    super();\n    /** DOM node hosting the portal's content. */\n    _defineProperty(this, \"element\", void 0);\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n  constructor() {\n    /** The portal currently attached to the host. */\n    _defineProperty(this, \"_attachedPortal\", void 0);\n    /** A function that will permanently dispose this host. */\n    _defineProperty(this, \"_disposeFn\", void 0);\n    /** Whether this host has already been permanently disposed. */\n    _defineProperty(this, \"_isDisposed\", false);\n    // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n    _defineProperty(this, \"attachDomPortal\", null);\n  }\n  /** Whether this host has an attached portal. */\n  hasAttached() {\n    return !!this._attachedPortal;\n  }\n  /** Attaches a portal. */\n  attach(portal) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n      // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n  /** Detaches a previously attached portal. */\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n    this._invokeDisposeFn();\n  }\n  /** Permanently dispose of this portal host. */\n  dispose() {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n  /** @docs-private */\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n  _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass BasePortalHost extends BasePortalOutlet {}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _unusedComponentFactoryResolver Used to resolve the component factory.\n   *   Only required when attaching component portals.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n   *   become a required parameter.\n   */\n  constructor(/** Element into which the content is projected. */\n  outletElement,\n  /**\n   * @deprecated No longer in use. To be removed.\n   * @breaking-change 18.0.0\n   */\n  _unusedComponentFactoryResolver, _appRef, _defaultInjector,\n  /**\n   * @deprecated `_document` Parameter to be made required.\n   * @breaking-change 10.0.0\n   */\n  _document) {\n    super();\n    _defineProperty(this, \"outletElement\", void 0);\n    _defineProperty(this, \"_appRef\", void 0);\n    _defineProperty(this, \"_defaultInjector\", void 0);\n    _defineProperty(this, \"_document\", void 0);\n    /**\n     * Attaches a DOM portal by transferring its content into the outlet.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    _defineProperty(this, \"attachDomPortal\", portal => {\n      const element = portal.element;\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      }\n      // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n      const anchorNode = this._document.createComment('dom-portal');\n      element.parentNode.insertBefore(anchorNode, element);\n      this.outletElement.appendChild(element);\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        // We can't use `replaceWith` here because IE doesn't support it.\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    });\n    this.outletElement = outletElement;\n    this._appRef = _appRef;\n    this._defaultInjector = _defaultInjector;\n    this._document = _document;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    let componentRef;\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      const injector = portal.injector || portal.viewContainerRef.injector;\n      const ngModuleRef = injector.get(NgModuleRef, null, {\n        optional: true\n      }) || undefined;\n      componentRef = portal.viewContainerRef.createComponent(portal.component, {\n        index: portal.viewContainerRef.length,\n        injector,\n        ngModuleRef,\n        projectableNodes: portal.projectableNodes || undefined\n      });\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n      const appRef = this._appRef;\n      const elementInjector = portal.injector || this._defaultInjector || Injector.NULL;\n      const environmentInjector = elementInjector.get(EnvironmentInjector, appRef.injector);\n      componentRef = createComponent(portal.component, {\n        elementInjector,\n        environmentInjector,\n        projectableNodes: portal.projectableNodes || undefined\n      });\n      appRef.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (appRef.viewCount > 0) {\n          appRef.detachView(componentRef.hostView);\n        }\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n    return componentRef;\n  }\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n    // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n    viewRef.detectChanges();\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n    this._attachedPortal = portal;\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n  /**\n   * Clears out a portal from the DOM.\n   */\n  dispose() {\n    super.dispose();\n    this.outletElement.remove();\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass DomPortalHost extends DomPortalOutlet {}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n  constructor() {\n    const templateRef = inject(TemplateRef);\n    const viewContainerRef = inject(ViewContainerRef);\n    super(templateRef, viewContainerRef);\n  }\n}\n_CdkPortal = CdkPortal;\n_defineProperty(CdkPortal, \"\\u0275fac\", function _CdkPortal_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkPortal)();\n});\n_defineProperty(CdkPortal, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkPortal,\n  selectors: [[\"\", \"cdkPortal\", \"\"]],\n  exportAs: [\"cdkPortal\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortal]',\n      exportAs: 'cdkPortal'\n    }]\n  }], () => [], null);\n})();\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {}\n_TemplatePortalDirective = TemplatePortalDirective;\n_defineProperty(TemplatePortalDirective, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_TemplatePortalDirective_BaseFactory;\n  return function _TemplatePortalDirective_Factory(__ngFactoryType__) {\n    return (ɵ_TemplatePortalDirective_BaseFactory || (ɵ_TemplatePortalDirective_BaseFactory = i0.ɵɵgetInheritedFactory(_TemplatePortalDirective)))(__ngFactoryType__ || _TemplatePortalDirective);\n  };\n})());\n_defineProperty(TemplatePortalDirective, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _TemplatePortalDirective,\n  selectors: [[\"\", \"cdk-portal\", \"\"], [\"\", \"portal\", \"\"]],\n  exportAs: [\"cdkPortal\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkPortal,\n    useExisting: _TemplatePortalDirective\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TemplatePortalDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-portal], [portal]',\n      exportAs: 'cdkPortal',\n      providers: [{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n  constructor() {\n    super();\n    _defineProperty(this, \"_moduleRef\", inject(NgModuleRef, {\n      optional: true\n    }));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_viewContainerRef\", inject(ViewContainerRef));\n    /** Whether the portal component is initialized. */\n    _defineProperty(this, \"_isInitialized\", false);\n    /** Reference to the currently-attached component/view ref. */\n    _defineProperty(this, \"_attachedRef\", void 0);\n    /** Emits when a portal is attached to the outlet. */\n    _defineProperty(this, \"attached\", new EventEmitter());\n    /**\n     * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    _defineProperty(this, \"attachDomPortal\", portal => {\n      const element = portal.element;\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      }\n      // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n      const anchorNode = this._document.createComment('dom-portal');\n      portal.setAttachedHost(this);\n      element.parentNode.insertBefore(anchorNode, element);\n      this._getRootNode().appendChild(element);\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    });\n  }\n  /** Portal associated with the Portal outlet. */\n  get portal() {\n    return this._attachedPortal;\n  }\n  set portal(portal) {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n    if (this.hasAttached()) {\n      super.detach();\n    }\n    if (portal) {\n      super.attach(portal);\n    }\n    this._attachedPortal = portal || null;\n  }\n  /** Component or view reference that is attached to the portal. */\n  get attachedRef() {\n    return this._attachedRef;\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedRef = this._attachedPortal = null;\n  }\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    portal.setAttachedHost(this);\n    // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n    const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n    const ref = viewContainerRef.createComponent(portal.component, {\n      index: viewContainerRef.length,\n      injector: portal.injector || viewContainerRef.injector,\n      projectableNodes: portal.projectableNodes || undefined,\n      ngModuleRef: this._moduleRef || undefined\n    });\n    // If we're using a view container that's different from the injected one (e.g. when the portal\n    // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n    // inside of the alternate view container.\n    if (viewContainerRef !== this._viewContainerRef) {\n      this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n    }\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n    return ref;\n  }\n  /**\n   * Attach the given TemplatePortal to this PortalHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    portal.setAttachedHost(this);\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n    return viewRef;\n  }\n  /** Gets the root node of the portal outlet. */\n  _getRootNode() {\n    const nativeElement = this._viewContainerRef.element.nativeElement;\n    // The directive could be set on a template which will result in a comment\n    // node being the root. Use the comment's parent node if that is the case.\n    return nativeElement.nodeType === nativeElement.ELEMENT_NODE ? nativeElement : nativeElement.parentNode;\n  }\n}\n_CdkPortalOutlet = CdkPortalOutlet;\n_defineProperty(CdkPortalOutlet, \"\\u0275fac\", function _CdkPortalOutlet_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkPortalOutlet)();\n});\n_defineProperty(CdkPortalOutlet, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkPortalOutlet,\n  selectors: [[\"\", \"cdkPortalOutlet\", \"\"]],\n  inputs: {\n    portal: [0, \"cdkPortalOutlet\", \"portal\"]\n  },\n  outputs: {\n    attached: \"attached\"\n  },\n  exportAs: [\"cdkPortalOutlet\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortalOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalOutlet]',\n      exportAs: 'cdkPortalOutlet'\n    }]\n  }], () => [], {\n    portal: [{\n      type: Input,\n      args: ['cdkPortalOutlet']\n    }],\n    attached: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {}\n_PortalHostDirective = PortalHostDirective;\n_defineProperty(PortalHostDirective, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_PortalHostDirective_BaseFactory;\n  return function _PortalHostDirective_Factory(__ngFactoryType__) {\n    return (ɵ_PortalHostDirective_BaseFactory || (ɵ_PortalHostDirective_BaseFactory = i0.ɵɵgetInheritedFactory(_PortalHostDirective)))(__ngFactoryType__ || _PortalHostDirective);\n  };\n})());\n_defineProperty(PortalHostDirective, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _PortalHostDirective,\n  selectors: [[\"\", \"cdkPortalHost\", \"\"], [\"\", \"portalHost\", \"\"]],\n  inputs: {\n    portal: [0, \"cdkPortalHost\", \"portal\"]\n  },\n  exportAs: [\"cdkPortalHost\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkPortalOutlet,\n    useExisting: _PortalHostDirective\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalHostDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalHost], [portalHost]',\n      exportAs: 'cdkPortalHost',\n      inputs: [{\n        name: 'portal',\n        alias: 'cdkPortalHost'\n      }],\n      providers: [{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }]\n    }]\n  }], null, null);\n})();\nclass PortalModule {}\n_PortalModule = PortalModule;\n_defineProperty(PortalModule, \"\\u0275fac\", function _PortalModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PortalModule)();\n});\n_defineProperty(PortalModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _PortalModule,\n  imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n  exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n}));\n_defineProperty(PortalModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n      exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n    }]\n  }], null, null);\n})();\nexport { BasePortalOutlet as B, ComponentPortal as C, DomPortal as D, Portal as P, TemplatePortal as T, BasePortalHost as a, DomPortalOutlet as b, DomPortalHost as c, CdkPortal as d, TemplatePortalDirective as e, CdkPortalOutlet as f, PortalHostDirective as g, PortalModule as h };\n//# sourceMappingURL=portal-directives-Bw5woq8I.mjs.map", "map": {"version": 3, "names": ["i0", "ElementRef", "NgModuleRef", "EnvironmentInjector", "createComponent", "Injector", "inject", "TemplateRef", "ViewContainerRef", "Directive", "EventEmitter", "Input", "Output", "NgModule", "DOCUMENT", "throwNullPortalError", "Error", "throwPortalAlreadyAttachedError", "throwPortalOutletAlreadyDisposedError", "throwUnknownPortalTypeError", "throwNullPortalOutletError", "throwNoPortalAttachedError", "Portal", "constructor", "_defineProperty", "attach", "host", "ngDevMode", "has<PERSON>tta<PERSON>", "_attachedHost", "detach", "isAttached", "setAttachedHost", "ComponentPortal", "component", "viewContainerRef", "injector", "_componentFactoryResolver", "projectableNodes", "TemplatePortal", "templateRef", "context", "origin", "elementRef", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "element", "nativeElement", "BasePortalOutlet", "_attachedPortal", "portal", "_isDisposed", "attachComponentPortal", "attachTemplatePortal", "attachDomPortal", "_invokeDisposeFn", "dispose", "setDisposeFn", "fn", "_disposeFn", "BasePortalHost", "DomPortalOutlet", "outletElement", "_unusedComponentFactoryResolver", "_appRef", "_defaultInjector", "_document", "parentNode", "anchorNode", "createComment", "insertBefore", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "componentRef", "ngModuleRef", "get", "optional", "index", "length", "destroy", "appRef", "elementInjector", "NULL", "environmentInjector", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "viewCount", "detach<PERSON>iew", "_getComponentRootNode", "viewContainer", "viewRef", "createEmbeddedView", "rootNodes", "for<PERSON>ach", "rootNode", "detectChanges", "indexOf", "remove", "DomPortalHost", "CdkPortal", "_CdkPortal", "_CdkPortal_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "exportAs", "features", "ɵɵInheritDefinitionFeature", "ɵsetClassMetadata", "args", "selector", "TemplatePortalDirective", "_TemplatePortalDirective", "ɵ_TemplatePortalDirective_BaseFactory", "_TemplatePortalDirective_Factory", "ɵɵgetInheritedFactory", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "CdkPortalOutlet", "_getRootNode", "_isInitialized", "attachedRef", "_attachedRef", "ngOnInit", "ngOnDestroy", "_viewContainerRef", "ref", "_moduleRef", "attached", "emit", "clear", "nodeType", "ELEMENT_NODE", "_CdkPortalOutlet", "_CdkPortalOutlet_Factory", "inputs", "outputs", "PortalHostDirective", "_PortalHostDirective", "ɵ_PortalHostDirective_BaseFactory", "_PortalHostDirective_Factory", "name", "alias", "PortalModule", "_PortalModule", "_PortalModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector", "B", "C", "D", "P", "T", "a", "b", "c", "d", "e", "f", "g", "h"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/portal-directives-Bw5woq8I.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ElementRef, NgModuleRef, EnvironmentInjector, createComponent, Injector, inject, TemplateRef, ViewContainerRef, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n    throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n    throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n    throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n    throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' +\n        'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n    throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n    throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n    _attachedHost;\n    /** Attach this portal to a host. */\n    attach(host) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (host == null) {\n                throwNullPortalOutletError();\n            }\n            if (host.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n        }\n        this._attachedHost = host;\n        return host.attach(this);\n    }\n    /** Detach this portal from its host */\n    detach() {\n        let host = this._attachedHost;\n        if (host != null) {\n            this._attachedHost = null;\n            host.detach();\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwNoPortalAttachedError();\n        }\n    }\n    /** Whether this portal is attached to a host. */\n    get isAttached() {\n        return this._attachedHost != null;\n    }\n    /**\n     * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n     * the PortalOutlet when it is performing an `attach()` or `detach()`.\n     */\n    setAttachedHost(host) {\n        this._attachedHost = host;\n    }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n    /** The type of the component that will be instantiated for attachment. */\n    component;\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This is different from where the component *renders*, which is determined by the PortalOutlet.\n     * The origin is necessary when the host is outside of the Angular application context.\n     */\n    viewContainerRef;\n    /** Injector used for the instantiation of the component. */\n    injector;\n    /**\n     * @deprecated No longer in use. To be removed.\n     * @breaking-change 18.0.0\n     */\n    componentFactoryResolver;\n    /**\n     * List of DOM nodes that should be projected through `<ng-content>` of the attached component.\n     */\n    projectableNodes;\n    constructor(component, viewContainerRef, injector, \n    /**\n     * @deprecated No longer in use. To be removed.\n     * @breaking-change 18.0.0\n     */\n    _componentFactoryResolver, projectableNodes) {\n        super();\n        this.component = component;\n        this.viewContainerRef = viewContainerRef;\n        this.injector = injector;\n        this.projectableNodes = projectableNodes;\n    }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n    templateRef;\n    viewContainerRef;\n    context;\n    injector;\n    constructor(\n    /** The embedded template that will be used to instantiate an embedded View in the host. */\n    templateRef, \n    /** Reference to the ViewContainer into which the template will be stamped out. */\n    viewContainerRef, \n    /** Contextual data to be passed in to the embedded view. */\n    context, \n    /** The injector to use for the embedded view. */\n    injector) {\n        super();\n        this.templateRef = templateRef;\n        this.viewContainerRef = viewContainerRef;\n        this.context = context;\n        this.injector = injector;\n    }\n    get origin() {\n        return this.templateRef.elementRef;\n    }\n    /**\n     * Attach the portal to the provided `PortalOutlet`.\n     * When a context is provided it will override the `context` property of the `TemplatePortal`\n     * instance.\n     */\n    attach(host, context = this.context) {\n        this.context = context;\n        return super.attach(host);\n    }\n    detach() {\n        this.context = undefined;\n        return super.detach();\n    }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n    /** DOM node hosting the portal's content. */\n    element;\n    constructor(element) {\n        super();\n        this.element = element instanceof ElementRef ? element.nativeElement : element;\n    }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n    /** The portal currently attached to the host. */\n    _attachedPortal;\n    /** A function that will permanently dispose this host. */\n    _disposeFn;\n    /** Whether this host has already been permanently disposed. */\n    _isDisposed = false;\n    /** Whether this host has an attached portal. */\n    hasAttached() {\n        return !!this._attachedPortal;\n    }\n    /** Attaches a portal. */\n    attach(portal) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!portal) {\n                throwNullPortalError();\n            }\n            if (this.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n            if (this._isDisposed) {\n                throwPortalOutletAlreadyDisposedError();\n            }\n        }\n        if (portal instanceof ComponentPortal) {\n            this._attachedPortal = portal;\n            return this.attachComponentPortal(portal);\n        }\n        else if (portal instanceof TemplatePortal) {\n            this._attachedPortal = portal;\n            return this.attachTemplatePortal(portal);\n            // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n        }\n        else if (this.attachDomPortal && portal instanceof DomPortal) {\n            this._attachedPortal = portal;\n            return this.attachDomPortal(portal);\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwUnknownPortalTypeError();\n        }\n    }\n    // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n    attachDomPortal = null;\n    /** Detaches a previously attached portal. */\n    detach() {\n        if (this._attachedPortal) {\n            this._attachedPortal.setAttachedHost(null);\n            this._attachedPortal = null;\n        }\n        this._invokeDisposeFn();\n    }\n    /** Permanently dispose of this portal host. */\n    dispose() {\n        if (this.hasAttached()) {\n            this.detach();\n        }\n        this._invokeDisposeFn();\n        this._isDisposed = true;\n    }\n    /** @docs-private */\n    setDisposeFn(fn) {\n        this._disposeFn = fn;\n    }\n    _invokeDisposeFn() {\n        if (this._disposeFn) {\n            this._disposeFn();\n            this._disposeFn = null;\n        }\n    }\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass BasePortalHost extends BasePortalOutlet {\n}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n    outletElement;\n    _appRef;\n    _defaultInjector;\n    _document;\n    /**\n     * @param outletElement Element into which the content is projected.\n     * @param _unusedComponentFactoryResolver Used to resolve the component factory.\n     *   Only required when attaching component portals.\n     * @param _appRef Reference to the application. Only used in component portals when there\n     *   is no `ViewContainerRef` available.\n     * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n     *   have one. Only used for component portals.\n     * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n     *   become a required parameter.\n     */\n    constructor(\n    /** Element into which the content is projected. */\n    outletElement, \n    /**\n     * @deprecated No longer in use. To be removed.\n     * @breaking-change 18.0.0\n     */\n    _unusedComponentFactoryResolver, _appRef, _defaultInjector, \n    /**\n     * @deprecated `_document` Parameter to be made required.\n     * @breaking-change 10.0.0\n     */\n    _document) {\n        super();\n        this.outletElement = outletElement;\n        this._appRef = _appRef;\n        this._defaultInjector = _defaultInjector;\n        this._document = _document;\n    }\n    /**\n     * Attach the given ComponentPortal to DOM element.\n     * @param portal Portal to be attached\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        let componentRef;\n        // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n        // for the component (in terms of Angular's component tree, not rendering).\n        // When the ViewContainerRef is missing, we use the factory to create the component directly\n        // and then manually attach the view to the application.\n        if (portal.viewContainerRef) {\n            const injector = portal.injector || portal.viewContainerRef.injector;\n            const ngModuleRef = injector.get(NgModuleRef, null, { optional: true }) || undefined;\n            componentRef = portal.viewContainerRef.createComponent(portal.component, {\n                index: portal.viewContainerRef.length,\n                injector,\n                ngModuleRef,\n                projectableNodes: portal.projectableNodes || undefined,\n            });\n            this.setDisposeFn(() => componentRef.destroy());\n        }\n        else {\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n                throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n            }\n            const appRef = this._appRef;\n            const elementInjector = portal.injector || this._defaultInjector || Injector.NULL;\n            const environmentInjector = elementInjector.get(EnvironmentInjector, appRef.injector);\n            componentRef = createComponent(portal.component, {\n                elementInjector,\n                environmentInjector,\n                projectableNodes: portal.projectableNodes || undefined,\n            });\n            appRef.attachView(componentRef.hostView);\n            this.setDisposeFn(() => {\n                // Verify that the ApplicationRef has registered views before trying to detach a host view.\n                // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n                if (appRef.viewCount > 0) {\n                    appRef.detachView(componentRef.hostView);\n                }\n                componentRef.destroy();\n            });\n        }\n        // At this point the component has been instantiated, so we move it to the location in the DOM\n        // where we want it to be rendered.\n        this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n        this._attachedPortal = portal;\n        return componentRef;\n    }\n    /**\n     * Attaches a template portal to the DOM as an embedded view.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        let viewContainer = portal.viewContainerRef;\n        let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n            injector: portal.injector,\n        });\n        // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n        // But for the DomPortalOutlet the view can be added everywhere in the DOM\n        // (e.g Overlay Container) To move the view to the specified host element. We just\n        // re-append the existing root nodes.\n        viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n        // Note that we want to detect changes after the nodes have been moved so that\n        // any directives inside the portal that are looking at the DOM inside a lifecycle\n        // hook won't be invoked too early.\n        viewRef.detectChanges();\n        this.setDisposeFn(() => {\n            let index = viewContainer.indexOf(viewRef);\n            if (index !== -1) {\n                viewContainer.remove(index);\n            }\n        });\n        this._attachedPortal = portal;\n        // TODO(jelbourn): Return locals from view.\n        return viewRef;\n    }\n    /**\n     * Attaches a DOM portal by transferring its content into the outlet.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        const element = portal.element;\n        if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('DOM portal content must be attached to a parent node.');\n        }\n        // Anchor used to save the element's previous position so\n        // that we can restore it when the portal is detached.\n        const anchorNode = this._document.createComment('dom-portal');\n        element.parentNode.insertBefore(anchorNode, element);\n        this.outletElement.appendChild(element);\n        this._attachedPortal = portal;\n        super.setDisposeFn(() => {\n            // We can't use `replaceWith` here because IE doesn't support it.\n            if (anchorNode.parentNode) {\n                anchorNode.parentNode.replaceChild(element, anchorNode);\n            }\n        });\n    };\n    /**\n     * Clears out a portal from the DOM.\n     */\n    dispose() {\n        super.dispose();\n        this.outletElement.remove();\n    }\n    /** Gets the root HTMLElement for an instantiated component. */\n    _getComponentRootNode(componentRef) {\n        return componentRef.hostView.rootNodes[0];\n    }\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass DomPortalHost extends DomPortalOutlet {\n}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n    constructor() {\n        const templateRef = inject(TemplateRef);\n        const viewContainerRef = inject(ViewContainerRef);\n        super(templateRef, viewContainerRef);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkPortal, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkPortal, isStandalone: true, selector: \"[cdkPortal]\", exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortal]',\n                    exportAs: 'cdkPortal',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TemplatePortalDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: TemplatePortalDirective, isStandalone: true, selector: \"[cdk-portal], [portal]\", providers: [\n            {\n                provide: CdkPortal,\n                useExisting: TemplatePortalDirective,\n            },\n        ], exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TemplatePortalDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-portal], [portal]',\n                    exportAs: 'cdkPortal',\n                    providers: [\n                        {\n                            provide: CdkPortal,\n                            useExisting: TemplatePortalDirective,\n                        },\n                    ],\n                }]\n        }] });\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n    _moduleRef = inject(NgModuleRef, { optional: true });\n    _document = inject(DOCUMENT);\n    _viewContainerRef = inject(ViewContainerRef);\n    /** Whether the portal component is initialized. */\n    _isInitialized = false;\n    /** Reference to the currently-attached component/view ref. */\n    _attachedRef;\n    constructor() {\n        super();\n    }\n    /** Portal associated with the Portal outlet. */\n    get portal() {\n        return this._attachedPortal;\n    }\n    set portal(portal) {\n        // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n        // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n        // and attach a portal programmatically in the parent component. When Angular does the first CD\n        // round, it will fire the setter with empty string, causing the user's content to be cleared.\n        if (this.hasAttached() && !portal && !this._isInitialized) {\n            return;\n        }\n        if (this.hasAttached()) {\n            super.detach();\n        }\n        if (portal) {\n            super.attach(portal);\n        }\n        this._attachedPortal = portal || null;\n    }\n    /** Emits when a portal is attached to the outlet. */\n    attached = new EventEmitter();\n    /** Component or view reference that is attached to the portal. */\n    get attachedRef() {\n        return this._attachedRef;\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        super.dispose();\n        this._attachedRef = this._attachedPortal = null;\n    }\n    /**\n     * Attach the given ComponentPortal to this PortalOutlet.\n     *\n     * @param portal Portal to be attached to the portal outlet.\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        portal.setAttachedHost(this);\n        // If the portal specifies an origin, use that as the logical location of the component\n        // in the application tree. Otherwise use the location of this PortalOutlet.\n        const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n        const ref = viewContainerRef.createComponent(portal.component, {\n            index: viewContainerRef.length,\n            injector: portal.injector || viewContainerRef.injector,\n            projectableNodes: portal.projectableNodes || undefined,\n            ngModuleRef: this._moduleRef || undefined,\n        });\n        // If we're using a view container that's different from the injected one (e.g. when the portal\n        // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n        // inside of the alternate view container.\n        if (viewContainerRef !== this._viewContainerRef) {\n            this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n        }\n        super.setDisposeFn(() => ref.destroy());\n        this._attachedPortal = portal;\n        this._attachedRef = ref;\n        this.attached.emit(ref);\n        return ref;\n    }\n    /**\n     * Attach the given TemplatePortal to this PortalHost as an embedded View.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        portal.setAttachedHost(this);\n        const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n            injector: portal.injector,\n        });\n        super.setDisposeFn(() => this._viewContainerRef.clear());\n        this._attachedPortal = portal;\n        this._attachedRef = viewRef;\n        this.attached.emit(viewRef);\n        return viewRef;\n    }\n    /**\n     * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        const element = portal.element;\n        if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('DOM portal content must be attached to a parent node.');\n        }\n        // Anchor used to save the element's previous position so\n        // that we can restore it when the portal is detached.\n        const anchorNode = this._document.createComment('dom-portal');\n        portal.setAttachedHost(this);\n        element.parentNode.insertBefore(anchorNode, element);\n        this._getRootNode().appendChild(element);\n        this._attachedPortal = portal;\n        super.setDisposeFn(() => {\n            if (anchorNode.parentNode) {\n                anchorNode.parentNode.replaceChild(element, anchorNode);\n            }\n        });\n    };\n    /** Gets the root node of the portal outlet. */\n    _getRootNode() {\n        const nativeElement = this._viewContainerRef.element.nativeElement;\n        // The directive could be set on a template which will result in a comment\n        // node being the root. Use the comment's parent node if that is the case.\n        return (nativeElement.nodeType === nativeElement.ELEMENT_NODE\n            ? nativeElement\n            : nativeElement.parentNode);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkPortalOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkPortalOutlet, isStandalone: true, selector: \"[cdkPortalOutlet]\", inputs: { portal: [\"cdkPortalOutlet\", \"portal\"] }, outputs: { attached: \"attached\" }, exportAs: [\"cdkPortalOutlet\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkPortalOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalOutlet]',\n                    exportAs: 'cdkPortalOutlet',\n                }]\n        }], ctorParameters: () => [], propDecorators: { portal: [{\n                type: Input,\n                args: ['cdkPortalOutlet']\n            }], attached: [{\n                type: Output\n            }] } });\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalHostDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: PortalHostDirective, isStandalone: true, selector: \"[cdkPortalHost], [portalHost]\", inputs: { portal: [\"cdkPortalHost\", \"portal\"] }, providers: [\n            {\n                provide: CdkPortalOutlet,\n                useExisting: PortalHostDirective,\n            },\n        ], exportAs: [\"cdkPortalHost\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalHostDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalHost], [portalHost]',\n                    exportAs: 'cdkPortalHost',\n                    inputs: [{ name: 'portal', alias: 'cdkPortalHost' }],\n                    providers: [\n                        {\n                            provide: CdkPortalOutlet,\n                            useExisting: PortalHostDirective,\n                        },\n                    ],\n                }]\n        }] });\nclass PortalModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalModule, imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective], exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PortalModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                    exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                }]\n        }] });\n\nexport { BasePortalOutlet as B, ComponentPortal as C, DomPortal as D, Portal as P, TemplatePortal as T, BasePortalHost as a, DomPortalOutlet as b, DomPortalHost as c, CdkPortal as d, TemplatePortalDirective as e, CdkPortalOutlet as f, PortalHostDirective as g, PortalModule as h };\n//# sourceMappingURL=portal-directives-Bw5woq8I.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAChM,SAASC,QAAQ,QAAQ,iBAAiB;;AAE1C;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAMC,KAAK,CAAC,iCAAiC,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACvC,MAAMD,KAAK,CAAC,oCAAoC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,SAASE,qCAAqCA,CAAA,EAAG;EAC7C,MAAMF,KAAK,CAAC,6CAA6C,CAAC;AAC9D;AACA;AACA;AACA;AACA;AACA,SAASG,2BAA2BA,CAAA,EAAG;EACnC,MAAMH,KAAK,CAAC,+EAA+E,GACvF,wCAAwC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASI,0BAA0BA,CAAA,EAAG;EAClC,MAAMJ,KAAK,CAAC,sDAAsD,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,SAASK,0BAA0BA,CAAA,EAAG;EAClC,MAAML,KAAK,CAAC,8DAA8D,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA,MAAMM,MAAM,CAAC;EAAAC,YAAA;IAAAC,eAAA;EAAA;EAET;EACAC,MAAMA,CAACC,IAAI,EAAE;IACT,IAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAID,IAAI,IAAI,IAAI,EAAE;QACdN,0BAA0B,CAAC,CAAC;MAChC;MACA,IAAIM,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;QACpBX,+BAA+B,CAAC,CAAC;MACrC;IACJ;IACA,IAAI,CAACY,aAAa,GAAGH,IAAI;IACzB,OAAOA,IAAI,CAACD,MAAM,CAAC,IAAI,CAAC;EAC5B;EACA;EACAK,MAAMA,CAAA,EAAG;IACL,IAAIJ,IAAI,GAAG,IAAI,CAACG,aAAa;IAC7B,IAAIH,IAAI,IAAI,IAAI,EAAE;MACd,IAAI,CAACG,aAAa,GAAG,IAAI;MACzBH,IAAI,CAACI,MAAM,CAAC,CAAC;IACjB,CAAC,MACI,IAAI,OAAOH,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpDN,0BAA0B,CAAC,CAAC;IAChC;EACJ;EACA;EACA,IAAIU,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACF,aAAa,IAAI,IAAI;EACrC;EACA;AACJ;AACA;AACA;EACIG,eAAeA,CAACN,IAAI,EAAE;IAClB,IAAI,CAACG,aAAa,GAAGH,IAAI;EAC7B;AACJ;AACA;AACA;AACA;AACA,MAAMO,eAAe,SAASX,MAAM,CAAC;EAoBjCC,WAAWA,CAACW,SAAS,EAAEC,gBAAgB,EAAEC,QAAQ;EACjD;AACJ;AACA;AACA;EACIC,yBAAyB,EAAEC,gBAAgB,EAAE;IACzC,KAAK,CAAC,CAAC;IAzBX;IAAAd,eAAA;IAEA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;IAFIA,eAAA;IAWI,IAAI,CAACU,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,gBAAgB,GAAGA,gBAAgB;EAC5C;AACJ;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAASjB,MAAM,CAAC;EAKhCC,WAAWA,CACX;EACAiB,WAAW,EACX;EACAL,gBAAgB,EAChB;EACAM,OAAO,EACP;EACAL,QAAQ,EAAE;IACN,KAAK,CAAC,CAAC;IAACZ,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACR,IAAI,CAACgB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACL,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACM,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACL,QAAQ,GAAGA,QAAQ;EAC5B;EACA,IAAIM,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,WAAW,CAACG,UAAU;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIlB,MAAMA,CAACC,IAAI,EAAEe,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE;IACjC,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,OAAO,KAAK,CAAChB,MAAM,CAACC,IAAI,CAAC;EAC7B;EACAI,MAAMA,CAAA,EAAG;IACL,IAAI,CAACW,OAAO,GAAGG,SAAS;IACxB,OAAO,KAAK,CAACd,MAAM,CAAC,CAAC;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,SAAS,SAASvB,MAAM,CAAC;EAG3BC,WAAWA,CAACuB,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IAHX;IAAAtB,eAAA;IAII,IAAI,CAACsB,OAAO,GAAGA,OAAO,YAAY7C,UAAU,GAAG6C,OAAO,CAACC,aAAa,GAAGD,OAAO;EAClF;AACJ;AACA;AACA;AACA;AACA;AACA,MAAME,gBAAgB,CAAC;EAAAzB,YAAA;IACnB;IAAAC,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,sBACc,KAAK;IAmCnB;IAAAA,eAAA,0BACkB,IAAI;EAAA;EAnCtB;EACAI,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACqB,eAAe;EACjC;EACA;EACAxB,MAAMA,CAACyB,MAAM,EAAE;IACX,IAAI,OAAOvB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAACuB,MAAM,EAAE;QACTnC,oBAAoB,CAAC,CAAC;MAC1B;MACA,IAAI,IAAI,CAACa,WAAW,CAAC,CAAC,EAAE;QACpBX,+BAA+B,CAAC,CAAC;MACrC;MACA,IAAI,IAAI,CAACkC,WAAW,EAAE;QAClBjC,qCAAqC,CAAC,CAAC;MAC3C;IACJ;IACA,IAAIgC,MAAM,YAAYjB,eAAe,EAAE;MACnC,IAAI,CAACgB,eAAe,GAAGC,MAAM;MAC7B,OAAO,IAAI,CAACE,qBAAqB,CAACF,MAAM,CAAC;IAC7C,CAAC,MACI,IAAIA,MAAM,YAAYX,cAAc,EAAE;MACvC,IAAI,CAACU,eAAe,GAAGC,MAAM;MAC7B,OAAO,IAAI,CAACG,oBAAoB,CAACH,MAAM,CAAC;MACxC;IACJ,CAAC,MACI,IAAI,IAAI,CAACI,eAAe,IAAIJ,MAAM,YAAYL,SAAS,EAAE;MAC1D,IAAI,CAACI,eAAe,GAAGC,MAAM;MAC7B,OAAO,IAAI,CAACI,eAAe,CAACJ,MAAM,CAAC;IACvC;IACA,IAAI,OAAOvB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CR,2BAA2B,CAAC,CAAC;IACjC;EACJ;EAGA;EACAW,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACmB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACjB,eAAe,CAAC,IAAI,CAAC;MAC1C,IAAI,CAACiB,eAAe,GAAG,IAAI;IAC/B;IACA,IAAI,CAACM,gBAAgB,CAAC,CAAC;EAC3B;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC5B,WAAW,CAAC,CAAC,EAAE;MACpB,IAAI,CAACE,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAACyB,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACJ,WAAW,GAAG,IAAI;EAC3B;EACA;EACAM,YAAYA,CAACC,EAAE,EAAE;IACb,IAAI,CAACC,UAAU,GAAGD,EAAE;EACxB;EACAH,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACI,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC,CAAC;MACjB,IAAI,CAACA,UAAU,GAAG,IAAI;IAC1B;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAASZ,gBAAgB,CAAC;;AAG9C;AACA;AACA;AACA;AACA,MAAMa,eAAe,SAASb,gBAAgB,CAAC;EAK3C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIzB,WAAWA,CACX;EACAuC,aAAa;EACb;AACJ;AACA;AACA;EACIC,+BAA+B,EAAEC,OAAO,EAAEC,gBAAgB;EAC1D;AACJ;AACA;AACA;EACIC,SAAS,EAAE;IACP,KAAK,CAAC,CAAC;IAAC1C,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAqFZ;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,0BAMmB0B,MAAM,IAAK;MAC1B,MAAMJ,OAAO,GAAGI,MAAM,CAACJ,OAAO;MAC9B,IAAI,CAACA,OAAO,CAACqB,UAAU,KAAK,OAAOxC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACxE,MAAMX,KAAK,CAAC,uDAAuD,CAAC;MACxE;MACA;MACA;MACA,MAAMoD,UAAU,GAAG,IAAI,CAACF,SAAS,CAACG,aAAa,CAAC,YAAY,CAAC;MAC7DvB,OAAO,CAACqB,UAAU,CAACG,YAAY,CAACF,UAAU,EAAEtB,OAAO,CAAC;MACpD,IAAI,CAACgB,aAAa,CAACS,WAAW,CAACzB,OAAO,CAAC;MACvC,IAAI,CAACG,eAAe,GAAGC,MAAM;MAC7B,KAAK,CAACO,YAAY,CAAC,MAAM;QACrB;QACA,IAAIW,UAAU,CAACD,UAAU,EAAE;UACvBC,UAAU,CAACD,UAAU,CAACK,YAAY,CAAC1B,OAAO,EAAEsB,UAAU,CAAC;QAC3D;MACJ,CAAC,CAAC;IACN,CAAC;IA3GG,IAAI,CAACN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACId,qBAAqBA,CAACF,MAAM,EAAE;IAC1B,IAAIuB,YAAY;IAChB;IACA;IACA;IACA;IACA,IAAIvB,MAAM,CAACf,gBAAgB,EAAE;MACzB,MAAMC,QAAQ,GAAGc,MAAM,CAACd,QAAQ,IAAIc,MAAM,CAACf,gBAAgB,CAACC,QAAQ;MACpE,MAAMsC,WAAW,GAAGtC,QAAQ,CAACuC,GAAG,CAACzE,WAAW,EAAE,IAAI,EAAE;QAAE0E,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAIhC,SAAS;MACpF6B,YAAY,GAAGvB,MAAM,CAACf,gBAAgB,CAAC/B,eAAe,CAAC8C,MAAM,CAAChB,SAAS,EAAE;QACrE2C,KAAK,EAAE3B,MAAM,CAACf,gBAAgB,CAAC2C,MAAM;QACrC1C,QAAQ;QACRsC,WAAW;QACXpC,gBAAgB,EAAEY,MAAM,CAACZ,gBAAgB,IAAIM;MACjD,CAAC,CAAC;MACF,IAAI,CAACa,YAAY,CAAC,MAAMgB,YAAY,CAACM,OAAO,CAAC,CAAC,CAAC;IACnD,CAAC,MACI;MACD,IAAI,CAAC,OAAOpD,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,CAAC,IAAI,CAACqC,OAAO,EAAE;QAClE,MAAMhD,KAAK,CAAC,qEAAqE,CAAC;MACtF;MACA,MAAMgE,MAAM,GAAG,IAAI,CAAChB,OAAO;MAC3B,MAAMiB,eAAe,GAAG/B,MAAM,CAACd,QAAQ,IAAI,IAAI,CAAC6B,gBAAgB,IAAI5D,QAAQ,CAAC6E,IAAI;MACjF,MAAMC,mBAAmB,GAAGF,eAAe,CAACN,GAAG,CAACxE,mBAAmB,EAAE6E,MAAM,CAAC5C,QAAQ,CAAC;MACrFqC,YAAY,GAAGrE,eAAe,CAAC8C,MAAM,CAAChB,SAAS,EAAE;QAC7C+C,eAAe;QACfE,mBAAmB;QACnB7C,gBAAgB,EAAEY,MAAM,CAACZ,gBAAgB,IAAIM;MACjD,CAAC,CAAC;MACFoC,MAAM,CAACI,UAAU,CAACX,YAAY,CAACY,QAAQ,CAAC;MACxC,IAAI,CAAC5B,YAAY,CAAC,MAAM;QACpB;QACA;QACA,IAAIuB,MAAM,CAACM,SAAS,GAAG,CAAC,EAAE;UACtBN,MAAM,CAACO,UAAU,CAACd,YAAY,CAACY,QAAQ,CAAC;QAC5C;QACAZ,YAAY,CAACM,OAAO,CAAC,CAAC;MAC1B,CAAC,CAAC;IACN;IACA;IACA;IACA,IAAI,CAACjB,aAAa,CAACS,WAAW,CAAC,IAAI,CAACiB,qBAAqB,CAACf,YAAY,CAAC,CAAC;IACxE,IAAI,CAACxB,eAAe,GAAGC,MAAM;IAC7B,OAAOuB,YAAY;EACvB;EACA;AACJ;AACA;AACA;AACA;EACIpB,oBAAoBA,CAACH,MAAM,EAAE;IACzB,IAAIuC,aAAa,GAAGvC,MAAM,CAACf,gBAAgB;IAC3C,IAAIuD,OAAO,GAAGD,aAAa,CAACE,kBAAkB,CAACzC,MAAM,CAACV,WAAW,EAAEU,MAAM,CAACT,OAAO,EAAE;MAC/EL,QAAQ,EAAEc,MAAM,CAACd;IACrB,CAAC,CAAC;IACF;IACA;IACA;IACA;IACAsD,OAAO,CAACE,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI,IAAI,CAAChC,aAAa,CAACS,WAAW,CAACuB,QAAQ,CAAC,CAAC;IAC/E;IACA;IACA;IACAJ,OAAO,CAACK,aAAa,CAAC,CAAC;IACvB,IAAI,CAACtC,YAAY,CAAC,MAAM;MACpB,IAAIoB,KAAK,GAAGY,aAAa,CAACO,OAAO,CAACN,OAAO,CAAC;MAC1C,IAAIb,KAAK,KAAK,CAAC,CAAC,EAAE;QACdY,aAAa,CAACQ,MAAM,CAACpB,KAAK,CAAC;MAC/B;IACJ,CAAC,CAAC;IACF,IAAI,CAAC5B,eAAe,GAAGC,MAAM;IAC7B;IACA,OAAOwC,OAAO;EAClB;EAyBA;AACJ;AACA;EACIlC,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACM,aAAa,CAACmC,MAAM,CAAC,CAAC;EAC/B;EACA;EACAT,qBAAqBA,CAACf,YAAY,EAAE;IAChC,OAAOA,YAAY,CAACY,QAAQ,CAACO,SAAS,CAAC,CAAC,CAAC;EAC7C;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMM,aAAa,SAASrC,eAAe,CAAC;;AAG5C;AACA;AACA;AACA;AACA,MAAMsC,SAAS,SAAS5D,cAAc,CAAC;EACnChB,WAAWA,CAAA,EAAG;IACV,MAAMiB,WAAW,GAAGlC,MAAM,CAACC,WAAW,CAAC;IACvC,MAAM4B,gBAAgB,GAAG7B,MAAM,CAACE,gBAAgB,CAAC;IACjD,KAAK,CAACgC,WAAW,EAAEL,gBAAgB,CAAC;EACxC;AAGJ;AAACiE,UAAA,GARKD,SAAS;AAAA3E,eAAA,CAAT2E,SAAS,wBAAAE,mBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAMwFH,UAAS;AAAA;AAAA3E,eAAA,CAN1G2E,SAAS,8BASkEnG,EAAE,CAAAuG,iBAAA;EAAAC,IAAA,EAFQL,UAAS;EAAAM,SAAA;EAAAC,QAAA;EAAAC,QAAA,GAEnB3G,EAAE,CAAA4G,0BAAA;AAAA;AAAnF;EAAA,QAAAjF,SAAA,oBAAAA,SAAA,KAAiF3B,EAAE,CAAA6G,iBAAA,CAAQV,SAAS,EAAc,CAAC;IACvGK,IAAI,EAAE/F,SAAS;IACfqG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBL,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMM,uBAAuB,SAASb,SAAS,CAAC;AAQ/Cc,wBAAA,GARKD,uBAAuB;AAAAxF,eAAA,CAAvBwF,uBAAuB;EAAA,IAAAE,qCAAA;EAAA,gBAAAC,iCAAAb,iBAAA;IAAA,QAAAY,qCAAA,KAAAA,qCAAA,GAXoDlH,EAAE,CAAAoH,qBAAA,CAYoBJ,wBAAuB,IAAAV,iBAAA,IAAvBU,wBAAuB;EAAA;AAAA;AAAAxF,eAAA,CADxHwF,uBAAuB,8BAXoDhH,EAAE,CAAAuG,iBAAA;EAAAC,IAAA,EAaQQ,wBAAuB;EAAAP,SAAA;EAAAC,QAAA;EAAAC,QAAA,GAbjC3G,EAAE,CAAAqH,kBAAA,CAaoG,CAC3K;IACIC,OAAO,EAAEnB,SAAS;IAClBoB,WAAW,EAAEP;EACjB,CAAC,CACJ,GAlBwEhH,EAAE,CAAA4G,0BAAA;AAAA;AAoBnF;EAAA,QAAAjF,SAAA,oBAAAA,SAAA,KApBiF3B,EAAE,CAAA6G,iBAAA,CAoBQG,uBAAuB,EAAc,CAAC;IACrHR,IAAI,EAAE/F,SAAS;IACfqG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCL,QAAQ,EAAE,WAAW;MACrBc,SAAS,EAAE,CACP;QACIF,OAAO,EAAEnB,SAAS;QAClBoB,WAAW,EAAEP;MACjB,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,eAAe,SAASzE,gBAAgB,CAAC;EAQ3CzB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IAACC,eAAA,qBARClB,MAAM,CAACJ,WAAW,EAAE;MAAE0E,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAApD,eAAA,oBACxClB,MAAM,CAACQ,QAAQ,CAAC;IAAAU,eAAA,4BACRlB,MAAM,CAACE,gBAAgB,CAAC;IAC5C;IAAAgB,eAAA,yBACiB,KAAK;IACtB;IAAAA,eAAA;IAyBA;IAAAA,eAAA,mBACW,IAAId,YAAY,CAAC,CAAC;IAyD7B;AACJ;AACA;AACA;AACA;AACA;IALIc,eAAA,0BAMmB0B,MAAM,IAAK;MAC1B,MAAMJ,OAAO,GAAGI,MAAM,CAACJ,OAAO;MAC9B,IAAI,CAACA,OAAO,CAACqB,UAAU,KAAK,OAAOxC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACxE,MAAMX,KAAK,CAAC,uDAAuD,CAAC;MACxE;MACA;MACA;MACA,MAAMoD,UAAU,GAAG,IAAI,CAACF,SAAS,CAACG,aAAa,CAAC,YAAY,CAAC;MAC7DnB,MAAM,CAAClB,eAAe,CAAC,IAAI,CAAC;MAC5Bc,OAAO,CAACqB,UAAU,CAACG,YAAY,CAACF,UAAU,EAAEtB,OAAO,CAAC;MACpD,IAAI,CAAC4E,YAAY,CAAC,CAAC,CAACnD,WAAW,CAACzB,OAAO,CAAC;MACxC,IAAI,CAACG,eAAe,GAAGC,MAAM;MAC7B,KAAK,CAACO,YAAY,CAAC,MAAM;QACrB,IAAIW,UAAU,CAACD,UAAU,EAAE;UACvBC,UAAU,CAACD,UAAU,CAACK,YAAY,CAAC1B,OAAO,EAAEsB,UAAU,CAAC;QAC3D;MACJ,CAAC,CAAC;IACN,CAAC;EAtGD;EACA;EACA,IAAIlB,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACD,eAAe;EAC/B;EACA,IAAIC,MAAMA,CAACA,MAAM,EAAE;IACf;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACtB,WAAW,CAAC,CAAC,IAAI,CAACsB,MAAM,IAAI,CAAC,IAAI,CAACyE,cAAc,EAAE;MACvD;IACJ;IACA,IAAI,IAAI,CAAC/F,WAAW,CAAC,CAAC,EAAE;MACpB,KAAK,CAACE,MAAM,CAAC,CAAC;IAClB;IACA,IAAIoB,MAAM,EAAE;MACR,KAAK,CAACzB,MAAM,CAACyB,MAAM,CAAC;IACxB;IACA,IAAI,CAACD,eAAe,GAAGC,MAAM,IAAI,IAAI;EACzC;EAGA;EACA,IAAI0E,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACH,cAAc,GAAG,IAAI;EAC9B;EACAI,WAAWA,CAAA,EAAG;IACV,KAAK,CAACvE,OAAO,CAAC,CAAC;IACf,IAAI,CAACqE,YAAY,GAAG,IAAI,CAAC5E,eAAe,GAAG,IAAI;EACnD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,qBAAqBA,CAACF,MAAM,EAAE;IAC1BA,MAAM,CAAClB,eAAe,CAAC,IAAI,CAAC;IAC5B;IACA;IACA,MAAMG,gBAAgB,GAAGe,MAAM,CAACf,gBAAgB,IAAI,IAAI,GAAGe,MAAM,CAACf,gBAAgB,GAAG,IAAI,CAAC6F,iBAAiB;IAC3G,MAAMC,GAAG,GAAG9F,gBAAgB,CAAC/B,eAAe,CAAC8C,MAAM,CAAChB,SAAS,EAAE;MAC3D2C,KAAK,EAAE1C,gBAAgB,CAAC2C,MAAM;MAC9B1C,QAAQ,EAAEc,MAAM,CAACd,QAAQ,IAAID,gBAAgB,CAACC,QAAQ;MACtDE,gBAAgB,EAAEY,MAAM,CAACZ,gBAAgB,IAAIM,SAAS;MACtD8B,WAAW,EAAE,IAAI,CAACwD,UAAU,IAAItF;IACpC,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAIT,gBAAgB,KAAK,IAAI,CAAC6F,iBAAiB,EAAE;MAC7C,IAAI,CAACN,YAAY,CAAC,CAAC,CAACnD,WAAW,CAAC0D,GAAG,CAAC5C,QAAQ,CAACO,SAAS,CAAC,CAAC,CAAC,CAAC;IAC9D;IACA,KAAK,CAACnC,YAAY,CAAC,MAAMwE,GAAG,CAAClD,OAAO,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC9B,eAAe,GAAGC,MAAM;IAC7B,IAAI,CAAC2E,YAAY,GAAGI,GAAG;IACvB,IAAI,CAACE,QAAQ,CAACC,IAAI,CAACH,GAAG,CAAC;IACvB,OAAOA,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;EACI5E,oBAAoBA,CAACH,MAAM,EAAE;IACzBA,MAAM,CAAClB,eAAe,CAAC,IAAI,CAAC;IAC5B,MAAM0D,OAAO,GAAG,IAAI,CAACsC,iBAAiB,CAACrC,kBAAkB,CAACzC,MAAM,CAACV,WAAW,EAAEU,MAAM,CAACT,OAAO,EAAE;MAC1FL,QAAQ,EAAEc,MAAM,CAACd;IACrB,CAAC,CAAC;IACF,KAAK,CAACqB,YAAY,CAAC,MAAM,IAAI,CAACuE,iBAAiB,CAACK,KAAK,CAAC,CAAC,CAAC;IACxD,IAAI,CAACpF,eAAe,GAAGC,MAAM;IAC7B,IAAI,CAAC2E,YAAY,GAAGnC,OAAO;IAC3B,IAAI,CAACyC,QAAQ,CAACC,IAAI,CAAC1C,OAAO,CAAC;IAC3B,OAAOA,OAAO;EAClB;EAyBA;EACAgC,YAAYA,CAAA,EAAG;IACX,MAAM3E,aAAa,GAAG,IAAI,CAACiF,iBAAiB,CAAClF,OAAO,CAACC,aAAa;IAClE;IACA;IACA,OAAQA,aAAa,CAACuF,QAAQ,KAAKvF,aAAa,CAACwF,YAAY,GACvDxF,aAAa,GACbA,aAAa,CAACoB,UAAU;EAClC;AAGJ;AAACqE,gBAAA,GA5HKf,eAAe;AAAAjG,eAAA,CAAfiG,eAAe,wBAAAgB,yBAAAnC,iBAAA;EAAA,YAAAA,iBAAA,IA0HkFmB,gBAAe;AAAA;AAAAjG,eAAA,CA1HhHiG,eAAe,8BAxC4DzH,EAAE,CAAAuG,iBAAA;EAAAC,IAAA,EAmKQiB,gBAAe;EAAAhB,SAAA;EAAAiC,MAAA;IAAAxF,MAAA;EAAA;EAAAyF,OAAA;IAAAR,QAAA;EAAA;EAAAzB,QAAA;EAAAC,QAAA,GAnKzB3G,EAAE,CAAA4G,0BAAA;AAAA;AAqKnF;EAAA,QAAAjF,SAAA,oBAAAA,SAAA,KArKiF3B,EAAE,CAAA6G,iBAAA,CAqKQY,eAAe,EAAc,CAAC;IAC7GjB,IAAI,EAAE/F,SAAS;IACfqG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BL,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAExD,MAAM,EAAE,CAAC;MACjDsD,IAAI,EAAE7F,KAAK;MACXmG,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEqB,QAAQ,EAAE,CAAC;MACX3B,IAAI,EAAE5F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMgI,mBAAmB,SAASnB,eAAe,CAAC;AAQjDoB,oBAAA,GARKD,mBAAmB;AAAApH,eAAA,CAAnBoH,mBAAmB;EAAA,IAAAE,iCAAA;EAAA,gBAAAC,6BAAAzC,iBAAA;IAAA,QAAAwC,iCAAA,KAAAA,iCAAA,GArLwD9I,EAAE,CAAAoH,qBAAA,CAsLoBwB,oBAAmB,IAAAtC,iBAAA,IAAnBsC,oBAAmB;EAAA;AAAA;AAAApH,eAAA,CADpHoH,mBAAmB,8BArLwD5I,EAAE,CAAAuG,iBAAA;EAAAC,IAAA,EAuLQoC,oBAAmB;EAAAnC,SAAA;EAAAiC,MAAA;IAAAxF,MAAA;EAAA;EAAAwD,QAAA;EAAAC,QAAA,GAvL7B3G,EAAE,CAAAqH,kBAAA,CAuLwJ,CAC/N;IACIC,OAAO,EAAEG,eAAe;IACxBF,WAAW,EAAEqB;EACjB,CAAC,CACJ,GA5LwE5I,EAAE,CAAA4G,0BAAA;AAAA;AA8LnF;EAAA,QAAAjF,SAAA,oBAAAA,SAAA,KA9LiF3B,EAAE,CAAA6G,iBAAA,CA8LQ+B,mBAAmB,EAAc,CAAC;IACjHpC,IAAI,EAAE/F,SAAS;IACfqG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+BAA+B;MACzCL,QAAQ,EAAE,eAAe;MACzBgC,MAAM,EAAE,CAAC;QAAEM,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAgB,CAAC,CAAC;MACpDzB,SAAS,EAAE,CACP;QACIF,OAAO,EAAEG,eAAe;QACxBF,WAAW,EAAEqB;MACjB,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMM,YAAY,CAAC;AAIlBC,aAAA,GAJKD,YAAY;AAAA1H,eAAA,CAAZ0H,YAAY,wBAAAE,sBAAA9C,iBAAA;EAAA,YAAAA,iBAAA,IACqF4C,aAAY;AAAA;AAAA1H,eAAA,CAD7G0H,YAAY,8BA5M+DlJ,EAAE,CAAAqJ,gBAAA;EAAA7C,IAAA,EA8MqB0C,aAAY;EAAAI,OAAA,GAAYnD,SAAS,EAAEsB,eAAe,EAAET,uBAAuB,EAAE4B,mBAAmB;EAAAW,OAAA,GAAapD,SAAS,EAAEsB,eAAe,EAAET,uBAAuB,EAAE4B,mBAAmB;AAAA;AAAApH,eAAA,CAFvR0H,YAAY,8BA5M+DlJ,EAAE,CAAAwJ,gBAAA;AAiNnF;EAAA,QAAA7H,SAAA,oBAAAA,SAAA,KAjNiF3B,EAAE,CAAA6G,iBAAA,CAiNQqC,YAAY,EAAc,CAAC;IAC1G1C,IAAI,EAAE3F,QAAQ;IACdiG,IAAI,EAAE,CAAC;MACCwC,OAAO,EAAE,CAACnD,SAAS,EAAEsB,eAAe,EAAET,uBAAuB,EAAE4B,mBAAmB,CAAC;MACnFW,OAAO,EAAE,CAACpD,SAAS,EAAEsB,eAAe,EAAET,uBAAuB,EAAE4B,mBAAmB;IACtF,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAAS5F,gBAAgB,IAAIyG,CAAC,EAAExH,eAAe,IAAIyH,CAAC,EAAE7G,SAAS,IAAI8G,CAAC,EAAErI,MAAM,IAAIsI,CAAC,EAAErH,cAAc,IAAIsH,CAAC,EAAEjG,cAAc,IAAIkG,CAAC,EAAEjG,eAAe,IAAIkG,CAAC,EAAE7D,aAAa,IAAI8D,CAAC,EAAE7D,SAAS,IAAI8D,CAAC,EAAEjD,uBAAuB,IAAIkD,CAAC,EAAEzC,eAAe,IAAI0C,CAAC,EAAEvB,mBAAmB,IAAIwB,CAAC,EAAElB,YAAY,IAAImB,CAAC;AACtR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}