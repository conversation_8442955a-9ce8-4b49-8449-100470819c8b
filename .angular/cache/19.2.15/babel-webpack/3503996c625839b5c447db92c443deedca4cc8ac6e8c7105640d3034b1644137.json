{"ast": null, "code": "var _SwuiPagePanelComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-page-panel.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-page-panel.component.scss?ngResource\";\nimport { Location } from '@angular/common';\nimport { Component, Input } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { Router } from '@angular/router';\nimport { filter, take } from 'rxjs/operators';\nimport { ActionConfirmDialogComponent } from './action-confirm-dialog/action-confirm-dialog.component';\nlet SwuiPagePanelComponent = (_SwuiPagePanelComponent = class SwuiPagePanelComponent {\n  set actions(actions) {\n    if (actions) {\n      this._actions = actions.filter(item => {\n        let available = typeof item.availableFn === 'undefined';\n        if (!available && item.availableFn) {\n          available = item.availableFn();\n        }\n        return available;\n      });\n    }\n  }\n  get actions() {\n    return this._actions;\n  }\n  constructor(router, dialog, location) {\n    this.router = router;\n    this.dialog = dialog;\n    this.location = location;\n    this.title = '';\n    this.back = false;\n    this.actionsMenuButtonTitle = '';\n    this.actionsMenuButtonColor = 'primary';\n    this.layout = 'separate';\n    this._actions = [];\n  }\n  ngOnInit() {}\n  goBack($event) {\n    $event.preventDefault();\n    if (this.backUrl) {\n      this.router.navigate([this.backUrl]);\n    } else {\n      this.location.back();\n    }\n  }\n  performAction($event, action) {\n    $event.preventDefault();\n    if (action.confirm) {\n      this.confirmDialogRef = this.dialog.open(ActionConfirmDialogComponent, {\n        width: '350px',\n        data: {\n          action\n        }\n      });\n      this.confirmDialogRef.afterClosed().pipe(filter(confirmResult => confirmResult.confirmed), take(1)).subscribe(() => this.callAction(action));\n    } else {\n      this.callAction(action);\n    }\n  }\n  isDisabled(action) {\n    return typeof action.disabledFn !== 'undefined' ? action.disabledFn() : false;\n  }\n  callAction(action) {\n    if (action.actionUrl) {\n      this.router.navigate([action.actionUrl]);\n    } else if (typeof action.actionFn !== 'undefined') {\n      action.actionFn();\n    }\n  }\n}, _SwuiPagePanelComponent.ctorParameters = () => [{\n  type: Router\n}, {\n  type: MatDialog\n}, {\n  type: Location\n}], _SwuiPagePanelComponent.propDecorators = {\n  actions: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  back: [{\n    type: Input\n  }],\n  backUrl: [{\n    type: Input\n  }],\n  actionsMenuButtonTitle: [{\n    type: Input\n  }],\n  actionsMenuButtonColor: [{\n    type: Input\n  }],\n  layout: [{\n    type: Input\n  }]\n}, _SwuiPagePanelComponent);\nSwuiPagePanelComponent = __decorate([Component({\n  selector: 'lib-swui-page-panel',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiPagePanelComponent);\nexport { SwuiPagePanelComponent };", "map": {"version": 3, "names": ["Location", "Component", "Input", "MatDialog", "Router", "filter", "take", "ActionConfirmDialogComponent", "SwuiPagePanelComponent", "_SwuiPagePanelComponent", "actions", "_actions", "item", "available", "availableFn", "constructor", "router", "dialog", "location", "title", "back", "actionsMenuButtonTitle", "actionsMenuButtonColor", "layout", "ngOnInit", "goBack", "$event", "preventDefault", "backUrl", "navigate", "performAction", "action", "confirm", "confirmDialogRef", "open", "width", "data", "afterClosed", "pipe", "confirmResult", "confirmed", "subscribe", "callAction", "isDisabled", "disabledFn", "actionUrl", "actionFn", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-page-panel/swui-page-panel.component.ts"], "sourcesContent": ["import { Location } from '@angular/common';\nimport { Component, Input, OnInit } from '@angular/core';\nimport { ThemePalette } from '@angular/material/core';\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\nimport { Router } from '@angular/router';\nimport { filter, take } from 'rxjs/operators';\nimport {\n  ActionConfirmDialogComponent, ConfirmDialogResult\n} from './action-confirm-dialog/action-confirm-dialog.component';\n\nexport type PagePanelButtonLayout = 'menu' | 'separate';\n\nexport interface PanelAction {\n  title: string;\n  hover?: string;\n  icon?: string;\n  svgIcon?: string;\n  fontIcon?: string;\n  fontSet?: string;\n  color?: ThemePalette;\n  iconFontSize?: string;\n  iconWidth?: string;\n  iconHeight?: string;\n  iconRightMargin?: string;\n  actionUrl?: string;\n  actionFn?: Function;\n  disabledFn?: Function;\n  availableFn?: Function;\n  confirm?: boolean;\n  confirmText?: string;\n  getStyle?: () => Record<string, string>;\n}\n\n@Component({\n    selector: 'lib-swui-page-panel',\n    templateUrl: 'swui-page-panel.component.html',\n    styleUrls: [\n        './swui-page-panel.component.scss',\n    ],\n    standalone: false\n})\n\nexport class SwuiPagePanelComponent implements OnInit {\n  @Input()\n  set actions( actions: PanelAction[] ) {\n    if (actions) {\n      this._actions = actions.filter(item => {\n        let available = typeof item.availableFn === 'undefined';\n        if (!available && item.availableFn) {\n          available = item.availableFn();\n        }\n        return available;\n      });\n    }\n  }\n\n  get actions(): PanelAction[] {\n    return this._actions;\n  }\n\n  @Input() title = '';\n  @Input() back = false;\n  @Input() backUrl: string | undefined;\n\n  @Input() actionsMenuButtonTitle = '';\n  @Input() actionsMenuButtonColor: ThemePalette = 'primary';\n  @Input() layout: PagePanelButtonLayout = 'separate';\n\n  private _actions: PanelAction[] = [];\n  private confirmDialogRef!: MatDialogRef<ActionConfirmDialogComponent>;\n\n  constructor(\n    private router: Router,\n    private dialog: MatDialog,\n    private location: Location\n  ) {\n  }\n\n  ngOnInit() {\n  }\n\n  goBack( $event: MouseEvent ) {\n    $event.preventDefault();\n\n    if (this.backUrl) {\n      this.router.navigate([this.backUrl]);\n    } else {\n      this.location.back();\n    }\n  }\n\n  performAction( $event: MouseEvent, action: PanelAction ) {\n    $event.preventDefault();\n\n    if (action.confirm) {\n      this.confirmDialogRef = this.dialog.open(ActionConfirmDialogComponent, {\n        width: '350px',\n        data: { action }\n      });\n      this.confirmDialogRef.afterClosed().pipe(\n        filter(( confirmResult: ConfirmDialogResult ) => confirmResult.confirmed),\n        take(1)\n      ).subscribe(() => this.callAction(action));\n    } else {\n      this.callAction(action);\n    }\n  }\n\n  isDisabled( action: PanelAction ): boolean {\n    return typeof action.disabledFn !== 'undefined' ? action.disabledFn() : false;\n  }\n\n  private callAction( action: PanelAction ) {\n    if (action.actionUrl) {\n      this.router.navigate([action.actionUrl]);\n    } else if (typeof action.actionFn !== 'undefined') {\n      action.actionFn();\n    }\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,SAAS,EAAEC,KAAK,QAAgB,eAAe;AAExD,SAASC,SAAS,QAAsB,0BAA0B;AAClE,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,MAAM,EAAEC,IAAI,QAAQ,gBAAgB;AAC7C,SACEC,4BAA4B,QACvB,yDAAyD;AAkCzD,IAAMC,sBAAsB,IAAAC,uBAAA,GAA5B,MAAMD,sBAAsB;MAE7BE,OAAOA,CAAEA,OAAsB;IACjC,IAAIA,OAAO,EAAE;MACX,IAAI,CAACC,QAAQ,GAAGD,OAAO,CAACL,MAAM,CAACO,IAAI,IAAG;QACpC,IAAIC,SAAS,GAAG,OAAOD,IAAI,CAACE,WAAW,KAAK,WAAW;QACvD,IAAI,CAACD,SAAS,IAAID,IAAI,CAACE,WAAW,EAAE;UAClCD,SAAS,GAAGD,IAAI,CAACE,WAAW,EAAE;QAChC;QACA,OAAOD,SAAS;MAClB,CAAC,CAAC;IACJ;EACF;EAEA,IAAIH,OAAOA,CAAA;IACT,OAAO,IAAI,CAACC,QAAQ;EACtB;EAaAI,YACUC,MAAc,EACdC,MAAiB,EACjBC,QAAkB;IAFlB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAdT,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,IAAI,GAAG,KAAK;IAGZ,KAAAC,sBAAsB,GAAG,EAAE;IAC3B,KAAAC,sBAAsB,GAAiB,SAAS;IAChD,KAAAC,MAAM,GAA0B,UAAU;IAE3C,KAAAZ,QAAQ,GAAkB,EAAE;EAQpC;EAEAa,QAAQA,CAAA,GACR;EAEAC,MAAMA,CAAEC,MAAkB;IACxBA,MAAM,CAACC,cAAc,EAAE;IAEvB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,IAAI,CAACD,OAAO,CAAC,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAACV,QAAQ,CAACE,IAAI,EAAE;IACtB;EACF;EAEAU,aAAaA,CAAEJ,MAAkB,EAAEK,MAAmB;IACpDL,MAAM,CAACC,cAAc,EAAE;IAEvB,IAAII,MAAM,CAACC,OAAO,EAAE;MAClB,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAAChB,MAAM,CAACiB,IAAI,CAAC3B,4BAA4B,EAAE;QACrE4B,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;UAAEL;QAAM;OACf,CAAC;MACF,IAAI,CAACE,gBAAgB,CAACI,WAAW,EAAE,CAACC,IAAI,CACtCjC,MAAM,CAAGkC,aAAkC,IAAMA,aAAa,CAACC,SAAS,CAAC,EACzElC,IAAI,CAAC,CAAC,CAAC,CACR,CAACmC,SAAS,CAAC,MAAM,IAAI,CAACC,UAAU,CAACX,MAAM,CAAC,CAAC;IAC5C,CAAC,MAAM;MACL,IAAI,CAACW,UAAU,CAACX,MAAM,CAAC;IACzB;EACF;EAEAY,UAAUA,CAAEZ,MAAmB;IAC7B,OAAO,OAAOA,MAAM,CAACa,UAAU,KAAK,WAAW,GAAGb,MAAM,CAACa,UAAU,EAAE,GAAG,KAAK;EAC/E;EAEQF,UAAUA,CAAEX,MAAmB;IACrC,IAAIA,MAAM,CAACc,SAAS,EAAE;MACpB,IAAI,CAAC7B,MAAM,CAACa,QAAQ,CAAC,CAACE,MAAM,CAACc,SAAS,CAAC,CAAC;IAC1C,CAAC,MAAM,IAAI,OAAOd,MAAM,CAACe,QAAQ,KAAK,WAAW,EAAE;MACjDf,MAAM,CAACe,QAAQ,EAAE;IACnB;EACF;;;;;;;;;UA3EC5C;EAAK;;UAiBLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;AAxBKM,sBAAsB,GAAAuC,UAAA,EATlC9C,SAAS,CAAC;EACP+C,QAAQ,EAAE,qBAAqB;EAC/BC,QAAA,EAAAC,oBAA6C;EAI7CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EAEW3C,sBAAsB,CA6ElC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}