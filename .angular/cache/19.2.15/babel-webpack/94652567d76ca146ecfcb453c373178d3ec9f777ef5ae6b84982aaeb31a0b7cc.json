{"ast": null, "code": "var _SwuiTdTimestampWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./timestamp.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/timestamp/timestamp.widget.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=Lm5vLXdyYXAge3doaXRlLXNwYWNlOiBub3dyYXB9!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/timestamp/timestamp.widget.ts\";\nimport { Component, Inject, Optional } from '@angular/core';\nimport moment from 'moment';\nimport 'moment-timezone';\nimport { SettingsService } from '../../../services/settings/settings.service';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nconst FORMAT_DATETIME_SMALL = 'DD/MM/YY HH:mm:ss';\nlet SwuiTdTimestampWidgetComponent = (_SwuiTdTimestampWidgetComponent = class SwuiTdTimestampWidgetComponent {\n  constructor({\n    row,\n    field,\n    value: value1,\n    schema\n  }, settings) {\n    var _schema$td, _schema$td2, _schema$td3, _schema$td4, _schema$td6;\n    this.settings = settings;\n    this.formatted = '';\n    this.noDatePlaceholder = '';\n    this.showTimeZone = false;\n    this.nowrap = false;\n    this.destroyed$ = new Subject();\n    if ('_meta' in row && field in row._meta) {\n      this.value = row._meta[field];\n    } else {\n      this.value = value1;\n    }\n    const classFn = (_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.classFn;\n    this.useTranslate = ((_schema$td2 = schema.td) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.useTranslate) || false;\n    this.nowrap = ((_schema$td3 = schema.td) === null || _schema$td3 === void 0 ? void 0 : _schema$td3.nowrap) || false;\n    this.classObj = classFn && classFn(row, schema);\n    if ((_schema$td4 = schema.td) !== null && _schema$td4 !== void 0 && _schema$td4.noDatePlaceholder) {\n      var _schema$td5;\n      this.noDatePlaceholder = (_schema$td5 = schema.td) === null || _schema$td5 === void 0 ? void 0 : _schema$td5.noDatePlaceholder;\n    }\n    this.showTimeZone = ((_schema$td6 = schema.td) === null || _schema$td6 === void 0 ? void 0 : _schema$td6.showTimeZone) || false;\n    if (schema.td && schema.td.dateOptions) {\n      this.timeDisableLevel = schema.td.dateOptions.timeDisableLevel;\n    }\n    this.schema = schema;\n  }\n  ngOnInit() {\n    if (this.settings) {\n      this.subscribeForSettings();\n    } else {\n      this.formatted = this.formatValue(this.schema.td || {});\n    }\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  formatValue({\n    dateFormat,\n    timeFormat,\n    timeZone\n  }) {\n    let format;\n    if (dateFormat && timeFormat) {\n      timeFormat = this.filterTimeFormat(timeFormat);\n      format = `${dateFormat} ${timeFormat}`;\n    } else {\n      format = this.filterTimeFormat(FORMAT_DATETIME_SMALL);\n    }\n    let abr = '';\n    if (this.showTimeZone && timeFormat && timeZone) {\n      const zone = moment.tz.zone(timeZone);\n      const offset = moment.tz(this.value, timeZone).utcOffset();\n      abr = zone ? ' ' + zone.abbr(offset) : '';\n    }\n    if (this.value) {\n      const date = timeZone ? moment.tz(moment.utc(this.value), timeZone) : moment(this.value);\n      return `${date.format(format)}${abr}`;\n    }\n    return '';\n  }\n  filterTimeFormat(format) {\n    if (this.timeDisableLevel === 'seconds') {\n      format = format.replace(/([\\:|\\.]ss)/, '');\n    }\n    return format;\n  }\n  subscribeForSettings() {\n    this.settings.appSettings$.pipe(filter(settings => !!settings), takeUntil(this.destroyed$)).subscribe(({\n      dateFormat,\n      timeFormat,\n      timezoneName\n    }) => {\n      this.formatted = this.formatValue({\n        dateFormat,\n        timeFormat,\n        timeZone: timezoneName\n      });\n    });\n  }\n}, _SwuiTdTimestampWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}, {\n  type: SettingsService,\n  decorators: [{\n    type: Optional\n  }]\n}], _SwuiTdTimestampWidgetComponent);\nSwuiTdTimestampWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-timestamp-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdTimestampWidgetComponent);\nexport { SwuiTdTimestampWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "Optional", "moment", "SettingsService", "filter", "takeUntil", "Subject", "SWUI_GRID_WIDGET_CONFIG", "FORMAT_DATETIME_SMALL", "SwuiTdTimestampWidgetComponent", "_SwuiTdTimestampWidgetComponent", "constructor", "row", "field", "value", "value1", "schema", "settings", "_schema$td", "_schema$td2", "_schema$td3", "_schema$td4", "_schema$td6", "formatted", "noDatePlaceholder", "showTimeZone", "nowrap", "destroyed$", "_meta", "classFn", "td", "useTranslate", "classObj", "_schema$td5", "dateOptions", "timeDisableLevel", "ngOnInit", "subscribeForSettings", "formatValue", "ngOnDestroy", "next", "undefined", "complete", "dateFormat", "timeFormat", "timeZone", "format", "filterTimeFormat", "abr", "zone", "tz", "offset", "utcOffset", "abbr", "date", "utc", "replace", "appSettings$", "pipe", "subscribe", "timezoneName", "ctorParameters", "type", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/timestamp/timestamp.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./timestamp.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/timestamp/timestamp.widget.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=Lm5vLXdyYXAge3doaXRlLXNwYWNlOiBub3dyYXB9!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/timestamp/timestamp.widget.ts\";\nimport { Component, Inject, Optional } from '@angular/core';\nimport moment from 'moment';\nimport 'moment-timezone';\nimport { SettingsService } from '../../../services/settings/settings.service';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nconst FORMAT_DATETIME_SMALL = 'DD/MM/YY HH:mm:ss';\nlet SwuiTdTimestampWidgetComponent = class SwuiTdTimestampWidgetComponent {\n    constructor({ row, field, value: value1, schema }, settings) {\n        this.settings = settings;\n        this.formatted = '';\n        this.noDatePlaceholder = '';\n        this.showTimeZone = false;\n        this.nowrap = false;\n        this.destroyed$ = new Subject();\n        if ('_meta' in row && field in row._meta) {\n            this.value = row._meta[field];\n        }\n        else {\n            this.value = value1;\n        }\n        const classFn = schema.td?.classFn;\n        this.useTranslate = schema.td?.useTranslate || false;\n        this.nowrap = schema.td?.nowrap || false;\n        this.classObj = classFn && classFn(row, schema);\n        if (schema.td?.noDatePlaceholder) {\n            this.noDatePlaceholder = schema.td?.noDatePlaceholder;\n        }\n        this.showTimeZone = schema.td?.showTimeZone || false;\n        if (schema.td && schema.td.dateOptions) {\n            this.timeDisableLevel = schema.td.dateOptions.timeDisableLevel;\n        }\n        this.schema = schema;\n    }\n    ngOnInit() {\n        if (this.settings) {\n            this.subscribeForSettings();\n        }\n        else {\n            this.formatted = this.formatValue(this.schema.td || {});\n        }\n    }\n    ngOnDestroy() {\n        this.destroyed$.next(undefined);\n        this.destroyed$.complete();\n    }\n    formatValue({ dateFormat, timeFormat, timeZone }) {\n        let format;\n        if (dateFormat && timeFormat) {\n            timeFormat = this.filterTimeFormat(timeFormat);\n            format = `${dateFormat} ${timeFormat}`;\n        }\n        else {\n            format = this.filterTimeFormat(FORMAT_DATETIME_SMALL);\n        }\n        let abr = '';\n        if (this.showTimeZone && timeFormat && timeZone) {\n            const zone = moment.tz.zone(timeZone);\n            const offset = moment.tz(this.value, timeZone).utcOffset();\n            abr = zone ? ' ' + zone.abbr(offset) : '';\n        }\n        if (this.value) {\n            const date = timeZone ? moment.tz(moment.utc(this.value), timeZone) : moment(this.value);\n            return `${date.format(format)}${abr}`;\n        }\n        return '';\n    }\n    filterTimeFormat(format) {\n        if (this.timeDisableLevel === 'seconds') {\n            format = format.replace(/([\\:|\\.]ss)/, '');\n        }\n        return format;\n    }\n    subscribeForSettings() {\n        this.settings.appSettings$\n            .pipe(filter(settings => !!settings), takeUntil(this.destroyed$))\n            .subscribe(({ dateFormat, timeFormat, timezoneName }) => {\n            this.formatted = this.formatValue({\n                dateFormat,\n                timeFormat,\n                timeZone: timezoneName,\n            });\n        });\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] },\n        { type: SettingsService, decorators: [{ type: Optional }] }\n    ]; }\n};\nSwuiTdTimestampWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-timestamp-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdTimestampWidgetComponent);\nexport { SwuiTdTimestampWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,OAAOC,oBAAoB,MAAM,8dAA8d;AAC/f,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC3D,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAO,iBAAiB;AACxB,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAClD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,MAAMC,qBAAqB,GAAG,mBAAmB;AACjD,IAAIC,8BAA8B,IAAAC,+BAAA,GAAG,MAAMD,8BAA8B,CAAC;EACtEE,WAAWA,CAAC;IAAEC,GAAG;IAAEC,KAAK;IAAEC,KAAK,EAAEC,MAAM;IAAEC;EAAO,CAAC,EAAEC,QAAQ,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;IACzD,IAAI,CAACL,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACM,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,UAAU,GAAG,IAAIrB,OAAO,CAAC,CAAC;IAC/B,IAAI,OAAO,IAAIM,GAAG,IAAIC,KAAK,IAAID,GAAG,CAACgB,KAAK,EAAE;MACtC,IAAI,CAACd,KAAK,GAAGF,GAAG,CAACgB,KAAK,CAACf,KAAK,CAAC;IACjC,CAAC,MACI;MACD,IAAI,CAACC,KAAK,GAAGC,MAAM;IACvB;IACA,MAAMc,OAAO,IAAAX,UAAA,GAAGF,MAAM,CAACc,EAAE,cAAAZ,UAAA,uBAATA,UAAA,CAAWW,OAAO;IAClC,IAAI,CAACE,YAAY,GAAG,EAAAZ,WAAA,GAAAH,MAAM,CAACc,EAAE,cAAAX,WAAA,uBAATA,WAAA,CAAWY,YAAY,KAAI,KAAK;IACpD,IAAI,CAACL,MAAM,GAAG,EAAAN,WAAA,GAAAJ,MAAM,CAACc,EAAE,cAAAV,WAAA,uBAATA,WAAA,CAAWM,MAAM,KAAI,KAAK;IACxC,IAAI,CAACM,QAAQ,GAAGH,OAAO,IAAIA,OAAO,CAACjB,GAAG,EAAEI,MAAM,CAAC;IAC/C,KAAAK,WAAA,GAAIL,MAAM,CAACc,EAAE,cAAAT,WAAA,eAATA,WAAA,CAAWG,iBAAiB,EAAE;MAAA,IAAAS,WAAA;MAC9B,IAAI,CAACT,iBAAiB,IAAAS,WAAA,GAAGjB,MAAM,CAACc,EAAE,cAAAG,WAAA,uBAATA,WAAA,CAAWT,iBAAiB;IACzD;IACA,IAAI,CAACC,YAAY,GAAG,EAAAH,WAAA,GAAAN,MAAM,CAACc,EAAE,cAAAR,WAAA,uBAATA,WAAA,CAAWG,YAAY,KAAI,KAAK;IACpD,IAAIT,MAAM,CAACc,EAAE,IAAId,MAAM,CAACc,EAAE,CAACI,WAAW,EAAE;MACpC,IAAI,CAACC,gBAAgB,GAAGnB,MAAM,CAACc,EAAE,CAACI,WAAW,CAACC,gBAAgB;IAClE;IACA,IAAI,CAACnB,MAAM,GAAGA,MAAM;EACxB;EACAoB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACnB,QAAQ,EAAE;MACf,IAAI,CAACoB,oBAAoB,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACd,SAAS,GAAG,IAAI,CAACe,WAAW,CAAC,IAAI,CAACtB,MAAM,CAACc,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3D;EACJ;EACAS,WAAWA,CAAA,EAAG;IACV,IAAI,CAACZ,UAAU,CAACa,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACd,UAAU,CAACe,QAAQ,CAAC,CAAC;EAC9B;EACAJ,WAAWA,CAAC;IAAEK,UAAU;IAAEC,UAAU;IAAEC;EAAS,CAAC,EAAE;IAC9C,IAAIC,MAAM;IACV,IAAIH,UAAU,IAAIC,UAAU,EAAE;MAC1BA,UAAU,GAAG,IAAI,CAACG,gBAAgB,CAACH,UAAU,CAAC;MAC9CE,MAAM,GAAG,GAAGH,UAAU,IAAIC,UAAU,EAAE;IAC1C,CAAC,MACI;MACDE,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACvC,qBAAqB,CAAC;IACzD;IACA,IAAIwC,GAAG,GAAG,EAAE;IACZ,IAAI,IAAI,CAACvB,YAAY,IAAImB,UAAU,IAAIC,QAAQ,EAAE;MAC7C,MAAMI,IAAI,GAAG/C,MAAM,CAACgD,EAAE,CAACD,IAAI,CAACJ,QAAQ,CAAC;MACrC,MAAMM,MAAM,GAAGjD,MAAM,CAACgD,EAAE,CAAC,IAAI,CAACpC,KAAK,EAAE+B,QAAQ,CAAC,CAACO,SAAS,CAAC,CAAC;MAC1DJ,GAAG,GAAGC,IAAI,GAAG,GAAG,GAAGA,IAAI,CAACI,IAAI,CAACF,MAAM,CAAC,GAAG,EAAE;IAC7C;IACA,IAAI,IAAI,CAACrC,KAAK,EAAE;MACZ,MAAMwC,IAAI,GAAGT,QAAQ,GAAG3C,MAAM,CAACgD,EAAE,CAAChD,MAAM,CAACqD,GAAG,CAAC,IAAI,CAACzC,KAAK,CAAC,EAAE+B,QAAQ,CAAC,GAAG3C,MAAM,CAAC,IAAI,CAACY,KAAK,CAAC;MACxF,OAAO,GAAGwC,IAAI,CAACR,MAAM,CAACA,MAAM,CAAC,GAAGE,GAAG,EAAE;IACzC;IACA,OAAO,EAAE;EACb;EACAD,gBAAgBA,CAACD,MAAM,EAAE;IACrB,IAAI,IAAI,CAACX,gBAAgB,KAAK,SAAS,EAAE;MACrCW,MAAM,GAAGA,MAAM,CAACU,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;IAC9C;IACA,OAAOV,MAAM;EACjB;EACAT,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACpB,QAAQ,CAACwC,YAAY,CACrBC,IAAI,CAACtD,MAAM,CAACa,QAAQ,IAAI,CAAC,CAACA,QAAQ,CAAC,EAAEZ,SAAS,CAAC,IAAI,CAACsB,UAAU,CAAC,CAAC,CAChEgC,SAAS,CAAC,CAAC;MAAEhB,UAAU;MAAEC,UAAU;MAAEgB;IAAa,CAAC,KAAK;MACzD,IAAI,CAACrC,SAAS,GAAG,IAAI,CAACe,WAAW,CAAC;QAC9BK,UAAU;QACVC,UAAU;QACVC,QAAQ,EAAEe;MACd,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AAKJ,CAAC,EAJYlD,+BAAA,CAAKmD,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAErB,SAAS;EAAEsB,UAAU,EAAE,CAAC;IAAED,IAAI,EAAE9D,MAAM;IAAEgE,IAAI,EAAE,CAACzD,uBAAuB;EAAG,CAAC;AAAE,CAAC,EACrF;EAAEuD,IAAI,EAAE3D,eAAe;EAAE4D,UAAU,EAAE,CAAC;IAAED,IAAI,EAAE7D;EAAS,CAAC;AAAE,CAAC,CAC9D,EAAAS,+BAAA,CACJ;AACDD,8BAA8B,GAAGb,UAAU,CAAC,CACxCG,SAAS,CAAC;EACNkE,QAAQ,EAAE,8BAA8B;EACxCC,QAAQ,EAAErE,oBAAoB;EAC9BsE,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACtE,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEW,8BAA8B,CAAC;AAClC,SAASA,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}