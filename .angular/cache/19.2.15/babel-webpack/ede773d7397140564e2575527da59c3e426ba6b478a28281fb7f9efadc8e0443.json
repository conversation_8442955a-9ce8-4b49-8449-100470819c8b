{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { SwuiNotificationsService } from './swui-notifications.service';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Overlay } from '@angular/cdk/overlay';\ndescribe('SwuiNotificationsService', () => {\n  let service;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      providers: [MatSnackBar, Overlay, SwuiNotificationsService]\n    });\n    service = TestBed.inject(SwuiNotificationsService);\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  it('showSnackbar should be called after call success method', () => {\n    spyOn(service, 'showSnackbar');\n    service.success('success');\n    expect(service.showSnackbar).toHaveBeenCalled();\n  });\n  it('showSnackbar should be called after call error method', () => {\n    spyOn(service, 'showSnackbar');\n    service.error('error');\n    expect(service.showSnackbar).toHaveBeenCalled();\n  });\n  it('showSnackbar should be called after call warning method', () => {\n    spyOn(service, 'showSnackbar');\n    service.warning('warning');\n    expect(service.showSnackbar).toHaveBeenCalled();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "SwuiNotificationsService", "MatSnackBar", "Overlay", "describe", "service", "beforeEach", "configureTestingModule", "providers", "inject", "it", "expect", "toBeTruthy", "spyOn", "success", "showSnackbar", "toHaveBeenCalled", "error", "warning"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-notifications/swui-notifications.service.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\n\nimport { SwuiNotificationsService } from './swui-notifications.service';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Overlay } from '@angular/cdk/overlay';\n\ndescribe('SwuiNotificationsService', () => {\n\n  let service: SwuiNotificationsService;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      providers: [\n        MatSnackBar,\n        Overlay,\n        SwuiNotificationsService,\n      ],\n    });\n\n    service = TestBed.inject(SwuiNotificationsService);\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  it('showSnackbar should be called after call success method', () => {\n    spyOn(service, 'showSnackbar');\n    service.success('success');\n    expect(service.showSnackbar).toHaveBeenCalled();\n  });\n\n  it('showSnackbar should be called after call error method', () => {\n    spyOn(service, 'showSnackbar');\n    service.error('error');\n    expect(service.showSnackbar).toHaveBeenCalled();\n  });\n\n  it('showSnackbar should be called after call warning method', () => {\n    spyOn(service, 'showSnackbar');\n    service.warning('warning');\n    expect(service.showSnackbar).toHaveBeenCalled();\n  });\n});\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,OAAO,QAAQ,sBAAsB;AAE9CC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;EAExC,IAAIC,OAAiC;EAErCC,UAAU,CAAC,MAAK;IACdN,OAAO,CAACO,sBAAsB,CAAC;MAC7BC,SAAS,EAAE,CACTN,WAAW,EACXC,OAAO,EACPF,wBAAwB;KAE3B,CAAC;IAEFI,OAAO,GAAGL,OAAO,CAACS,MAAM,CAACR,wBAAwB,CAAC;EACpD,CAAC,CAAC;EAEFS,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACN,OAAO,CAAC,CAACO,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFF,EAAE,CAAC,yDAAyD,EAAE,MAAK;IACjEG,KAAK,CAACR,OAAO,EAAE,cAAc,CAAC;IAC9BA,OAAO,CAACS,OAAO,CAAC,SAAS,CAAC;IAC1BH,MAAM,CAACN,OAAO,CAACU,YAAY,CAAC,CAACC,gBAAgB,EAAE;EACjD,CAAC,CAAC;EAEFN,EAAE,CAAC,uDAAuD,EAAE,MAAK;IAC/DG,KAAK,CAACR,OAAO,EAAE,cAAc,CAAC;IAC9BA,OAAO,CAACY,KAAK,CAAC,OAAO,CAAC;IACtBN,MAAM,CAACN,OAAO,CAACU,YAAY,CAAC,CAACC,gBAAgB,EAAE;EACjD,CAAC,CAAC;EAEFN,EAAE,CAAC,yDAAyD,EAAE,MAAK;IACjEG,KAAK,CAACR,OAAO,EAAE,cAAc,CAAC;IAC9BA,OAAO,CAACa,OAAO,CAAC,SAAS,CAAC;IAC1BP,MAAM,CAACN,OAAO,CAACU,YAAY,CAAC,CAACC,gBAAgB,EAAE;EACjD,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}