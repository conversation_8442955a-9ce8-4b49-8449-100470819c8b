{"ast": null, "code": "var _SwuiTopMenuComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-top-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-top-menu.component.scss?ngResource\";\nimport { Component, EventEmitter, Inject, Input, Output } from '@angular/core';\nimport { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubInitService } from '../services/sw-hub-init/sw-hub-init.service';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../services/sw-hub-init/sw-hub-init.token';\nlet SwuiTopMenuComponent = (_SwuiTopMenuComponent = class SwuiTopMenuComponent {\n  constructor({\n    logo,\n    logoSymbols\n  }, auth, hubService, configService) {\n    var _configService$logo$m, _configService$logo, _configService$logo$w, _configService$logo2;\n    this.auth = auth;\n    this.hubService = hubService;\n    this.configService = configService;\n    this.sidebarChanges = new EventEmitter();\n    this.settingsClick = new EventEmitter();\n    this.showSearch = false;\n    this.logo = (_configService$logo$m = (_configService$logo = configService.logo) === null || _configService$logo === void 0 ? void 0 : _configService$logo.main) !== null && _configService$logo$m !== void 0 ? _configService$logo$m : logo;\n    this.logoSymbols = (_configService$logo$w = (_configService$logo2 = configService.logo) === null || _configService$logo2 === void 0 ? void 0 : _configService$logo2.white) !== null && _configService$logo$w !== void 0 ? _configService$logo$w : logoSymbols;\n  }\n  get isLogged() {\n    return this.auth.isLogged();\n  }\n  get username() {\n    return this.auth.username;\n  }\n  get entityKey() {\n    return this.auth.entityKey;\n  }\n  get isSuperAdminOnly() {\n    return this.auth.isSuperAdmin;\n  }\n  get hasTwoFactor() {\n    return this.auth.isTwoFactor;\n  }\n  get config() {\n    return this.configService;\n  }\n  get envName() {\n    return this.config.envName ? this.config.envName.toLocaleUpperCase() : '-';\n  }\n  get locationName() {\n    return this.config.locationName ? this.config.locationName.toLocaleUpperCase() : '-';\n  }\n  selectLang(lang) {\n    this.hubService.sendLocale(lang);\n  }\n  setSettings(value) {\n    this.hubService.sendAppSettings(value);\n  }\n  onLogout() {\n    this.hubService.sendLogout();\n  }\n}, _SwuiTopMenuComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_HUB_MESSAGE_CONFIG]\n  }]\n}, {\n  type: SwHubAuthService\n}, {\n  type: SwHubInitService\n}, {\n  type: SwHubConfigService\n}], _SwuiTopMenuComponent.propDecorators = {\n  sidebarChanges: [{\n    type: Output\n  }],\n  settingsClick: [{\n    type: Output\n  }],\n  showSearch: [{\n    type: Input\n  }]\n}, _SwuiTopMenuComponent);\nSwuiTopMenuComponent = __decorate([Component({\n  selector: 'lib-swui-top-menu',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTopMenuComponent);\nexport { SwuiTopMenuComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Inject", "Input", "Output", "SwHubAuthService", "SwHubConfigService", "SwHubInitService", "SWUI_HUB_MESSAGE_CONFIG", "SwuiTopMenuComponent", "_SwuiTopMenuComponent", "constructor", "logo", "logoSymbols", "auth", "hubService", "configService", "_configService$logo$m", "_configService$logo", "_configService$logo$w", "_configService$logo2", "sidebarChanges", "settingsClick", "showSearch", "main", "white", "isLogged", "username", "entityKey", "isSuperAdminOnly", "isSuperAdmin", "hasTwoFactor", "isTwoFactor", "config", "envName", "toLocaleUpperCase", "locationName", "selectLang", "lang", "sendLocale", "setSettings", "value", "sendAppSettings", "onLogout", "sendLogout", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/swui-top-menu.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Inject, Input, Output } from '@angular/core';\nimport { AppSettings } from '../services/settings/app-settings';\nimport { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';\nimport { HubConfig, SwHubConfigService } from '../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubMessageModuleConfig } from '../services/sw-hub-init/sw-hub-init.model';\nimport { SwHubInitService } from '../services/sw-hub-init/sw-hub-init.service';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../services/sw-hub-init/sw-hub-init.token';\n\n@Component({\n    selector: 'lib-swui-top-menu',\n    templateUrl: './swui-top-menu.component.html',\n    styleUrls: ['./swui-top-menu.component.scss'],\n    standalone: false\n})\nexport class SwuiTopMenuComponent {\n  @Output() sidebarChanges = new EventEmitter<void>();\n  @Output() settingsClick = new EventEmitter<string>();\n  @Input() showSearch = false;\n\n  readonly logo: string | undefined;\n  readonly logoSymbols: string | undefined;\n\n  constructor( @Inject(SWUI_HUB_MESSAGE_CONFIG) { logo, logoSymbols }: SwHubMessageModuleConfig,\n               private readonly auth: SwHubAuthService,\n               private readonly hubService: SwHubInitService,\n               private readonly configService: SwHubConfigService ) {\n    this.logo = configService.logo?.main ?? logo;\n    this.logoSymbols = configService.logo?.white ?? logoSymbols;\n  }\n\n  get isLogged(): boolean {\n    return this.auth.isLogged();\n  }\n\n  get username(): string | undefined {\n    return this.auth.username;\n  }\n\n  get entityKey(): string | undefined {\n    return this.auth.entityKey;\n  }\n\n  get isSuperAdminOnly(): boolean {\n    return this.auth.isSuperAdmin;\n  }\n\n  get hasTwoFactor(): boolean {\n    return this.auth.isTwoFactor;\n  }\n\n  get config(): HubConfig {\n    return this.configService;\n  }\n\n  get envName(): string {\n    return this.config.envName ? this.config.envName.toLocaleUpperCase() : '-';\n  }\n\n  get locationName(): string {\n    return this.config.locationName ? this.config.locationName.toLocaleUpperCase() : '-';\n  }\n\n  selectLang( lang: string ) {\n    this.hubService.sendLocale(lang);\n  }\n\n  setSettings( value: AppSettings ) {\n    this.hubService.sendAppSettings(value);\n  }\n\n  onLogout() {\n    this.hubService.sendLogout();\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAE9E,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAAoBC,kBAAkB,QAAQ,iDAAiD;AAE/F,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,uBAAuB,QAAQ,2CAA2C;AAQ5E,IAAMC,oBAAoB,IAAAC,qBAAA,GAA1B,MAAMD,oBAAoB;EAQ/BE,YAA8C;IAAEC,IAAI;IAAEC;EAAW,CAA4B,EAC/DC,IAAsB,EACtBC,UAA4B,EAC5BC,aAAiC;IAAA,IAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,oBAAA;IAFjC,KAAAN,IAAI,GAAJA,IAAI;IACJ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IAVjC,KAAAK,cAAc,GAAG,IAAIpB,YAAY,EAAQ;IACzC,KAAAqB,aAAa,GAAG,IAAIrB,YAAY,EAAU;IAC3C,KAAAsB,UAAU,GAAG,KAAK;IASzB,IAAI,CAACX,IAAI,IAAAK,qBAAA,IAAAC,mBAAA,GAAGF,aAAa,CAACJ,IAAI,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBM,IAAI,cAAAP,qBAAA,cAAAA,qBAAA,GAAIL,IAAI;IAC5C,IAAI,CAACC,WAAW,IAAAM,qBAAA,IAAAC,oBAAA,GAAGJ,aAAa,CAACJ,IAAI,cAAAQ,oBAAA,uBAAlBA,oBAAA,CAAoBK,KAAK,cAAAN,qBAAA,cAAAA,qBAAA,GAAIN,WAAW;EAC7D;EAEA,IAAIa,QAAQA,CAAA;IACV,OAAO,IAAI,CAACZ,IAAI,CAACY,QAAQ,EAAE;EAC7B;EAEA,IAAIC,QAAQA,CAAA;IACV,OAAO,IAAI,CAACb,IAAI,CAACa,QAAQ;EAC3B;EAEA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACd,IAAI,CAACc,SAAS;EAC5B;EAEA,IAAIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACf,IAAI,CAACgB,YAAY;EAC/B;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACjB,IAAI,CAACkB,WAAW;EAC9B;EAEA,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACjB,aAAa;EAC3B;EAEA,IAAIkB,OAAOA,CAAA;IACT,OAAO,IAAI,CAACD,MAAM,CAACC,OAAO,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO,CAACC,iBAAiB,EAAE,GAAG,GAAG;EAC5E;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACH,MAAM,CAACG,YAAY,GAAG,IAAI,CAACH,MAAM,CAACG,YAAY,CAACD,iBAAiB,EAAE,GAAG,GAAG;EACtF;EAEAE,UAAUA,CAAEC,IAAY;IACtB,IAAI,CAACvB,UAAU,CAACwB,UAAU,CAACD,IAAI,CAAC;EAClC;EAEAE,WAAWA,CAAEC,KAAkB;IAC7B,IAAI,CAAC1B,UAAU,CAAC2B,eAAe,CAACD,KAAK,CAAC;EACxC;EAEAE,QAAQA,CAAA;IACN,IAAI,CAAC5B,UAAU,CAAC6B,UAAU,EAAE;EAC9B;;;;UAlDc1C,MAAM;IAAA2C,IAAA,GAACrC,uBAAuB;EAAA;AAAA,G;;;;;;;;UAP3CJ;EAAM;;UACNA;EAAM;;UACND;EAAK;;AAHKM,oBAAoB,GAAAqC,UAAA,EANhC9C,SAAS,CAAC;EACP+C,QAAQ,EAAE,mBAAmB;EAC7BC,QAAA,EAAAC,oBAA6C;EAE7CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWzC,oBAAoB,CA2DhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}