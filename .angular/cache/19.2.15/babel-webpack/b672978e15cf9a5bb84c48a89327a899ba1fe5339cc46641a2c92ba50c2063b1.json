{"ast": null, "code": "var _TestWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet TestWidgetComponent = (_TestWidgetComponent = class TestWidgetComponent {\n  constructor({\n    value\n  }) {\n    this.value = value;\n  }\n}, _TestWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _TestWidgetComponent);\nTestWidgetComponent = __decorate([Component({\n  template: '<b style=\"font-weight: lighter; text-transform: uppercase;\">{{ value }}</b>',\n  standalone: false\n})], TestWidgetComponent);\nexport { TestWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "TestWidgetComponent", "_TestWidgetComponent", "constructor", "value", "ctorParameters", "type", "undefined", "decorators", "args", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/_stories/test-widget/test-widget.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet TestWidgetComponent = class TestWidgetComponent {\n    constructor({ value }) {\n        this.value = value;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nTestWidgetComponent = __decorate([\n    Component({\n        template: '<b style=\"font-weight: lighter; text-transform: uppercase;\">{{ value }}</b>',\n        standalone: false\n    })\n], TestWidgetComponent);\nexport { TestWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,mBAAmB,IAAAC,oBAAA,GAAG,MAAMD,mBAAmB,CAAC;EAChDE,WAAWA,CAAC;IAAEC;EAAM,CAAC,EAAE;IACnB,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;AAIJ,CAAC,EAHYF,oBAAA,CAAKG,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEP,MAAM;IAAEU,IAAI,EAAE,CAACT,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,oBAAA,CACJ;AACDD,mBAAmB,GAAGJ,UAAU,CAAC,CAC7BC,SAAS,CAAC;EACNY,QAAQ,EAAE,6EAA6E;EACvFC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEV,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}