{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n  return new Observable(function (subscriber) {\n    var resource = resourceFactory();\n    var result = observableFactory(resource);\n    var source = result ? innerFrom(result) : EMPTY;\n    source.subscribe(subscriber);\n    return function () {\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n}\n//# sourceMappingURL=using.js.map", "map": {"version": 3, "names": ["Observable", "innerFrom", "EMPTY", "using", "resourceFactory", "observableFactory", "subscriber", "resource", "result", "source", "subscribe", "unsubscribe"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/using.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n    return new Observable(function (subscriber) {\n        var resource = resourceFactory();\n        var result = observableFactory(resource);\n        var source = result ? innerFrom(result) : EMPTY;\n        source.subscribe(subscriber);\n        return function () {\n            if (resource) {\n                resource.unsubscribe();\n            }\n        };\n    });\n}\n//# sourceMappingURL=using.js.map"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,KAAKA,CAACC,eAAe,EAAEC,iBAAiB,EAAE;EACtD,OAAO,IAAIL,UAAU,CAAC,UAAUM,UAAU,EAAE;IACxC,IAAIC,QAAQ,GAAGH,eAAe,CAAC,CAAC;IAChC,IAAII,MAAM,GAAGH,iBAAiB,CAACE,QAAQ,CAAC;IACxC,IAAIE,MAAM,GAAGD,MAAM,GAAGP,SAAS,CAACO,MAAM,CAAC,GAAGN,KAAK;IAC/CO,MAAM,CAACC,SAAS,CAACJ,UAAU,CAAC;IAC5B,OAAO,YAAY;MACf,IAAIC,QAAQ,EAAE;QACVA,QAAQ,CAACI,WAAW,CAAC,CAAC;MAC1B;IACJ,CAAC;EACL,CAAC,CAAC;AACN;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}