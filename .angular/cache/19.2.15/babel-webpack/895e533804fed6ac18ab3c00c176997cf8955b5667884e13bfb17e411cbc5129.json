{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatRippleModule } from '@angular/material/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CustomPeriodComponent } from './custom-period.component';\nexport const MODULES = [MatMenuModule, MatButtonModule, MatRippleModule];\nlet CustomPeriodModule = class CustomPeriodModule {};\nCustomPeriodModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule.forChild(), ...MODULES],\n  declarations: [CustomPeriodComponent],\n  exports: [CustomPeriodComponent]\n})], CustomPeriodModule);\nexport { CustomPeriodModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "MatButtonModule", "MatMenuModule", "MatRippleModule", "TranslateModule", "CustomPeriodComponent", "MODULES", "CustomPeriodModule", "__decorate", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-range/custom-period/custom-period.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatRippleModule } from '@angular/material/core';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { CustomPeriodComponent } from './custom-period.component';\n\n\nexport const MODULES = [\n  MatMenuModule,\n  MatButtonModule,\n  MatRippleModule,\n];\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    ...MODULES,\n  ],\n  declarations: [\n    CustomPeriodComponent\n  ],\n  exports: [\n    CustomPeriodComponent\n  ]\n})\nexport class CustomPeriodModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,qBAAqB,QAAQ,2BAA2B;AAGjE,OAAO,MAAMC,OAAO,GAAG,CACrBJ,aAAa,EACbD,eAAe,EACfE,eAAe,CAChB;AAeM,IAAMI,kBAAkB,GAAxB,MAAMA,kBAAkB,GAC9B;AADYA,kBAAkB,GAAAC,UAAA,EAb9BT,QAAQ,CAAC;EACRU,OAAO,EAAE,CACPT,YAAY,EACZI,eAAe,CAACM,QAAQ,EAAE,EAC1B,GAAGJ,OAAO,CACX;EACDK,YAAY,EAAE,CACZN,qBAAqB,CACtB;EACDO,OAAO,EAAE,CACPP,qBAAqB;CAExB,CAAC,C,EACWE,kBAAkB,CAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}