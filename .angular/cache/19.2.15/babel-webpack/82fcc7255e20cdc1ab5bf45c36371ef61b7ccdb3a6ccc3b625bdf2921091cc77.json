{"ast": null, "code": "var _SwuiProgressContainerComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-progress-container/swui-progress-container.component.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=Omhvc3Qge2Rpc3BsYXk6IGlubGluZS1ibG9jazt9!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-progress-container/swui-progress-container.component.ts\";\nimport { Component } from '@angular/core';\nlet SwuiProgressContainerComponent = (_SwuiProgressContainerComponent = class SwuiProgressContainerComponent {\n  constructor() {}\n  ngOnInit() {}\n}, _SwuiProgressContainerComponent.ctorParameters = () => [], _SwuiProgressContainerComponent);\nSwuiProgressContainerComponent = __decorate([Component({\n  selector: 'lib-swui-progress-container',\n  template: '<mat-spinner diameter=\"40\"></mat-spinner>',\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__0]\n})], SwuiProgressContainerComponent);\nexport { SwuiProgressContainerComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "SwuiProgressContainerComponent", "_SwuiProgressContainerComponent", "constructor", "ngOnInit", "ctorParameters", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-progress-container/swui-progress-container.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-progress-container/swui-progress-container.component.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=Omhvc3Qge2Rpc3BsYXk6IGlubGluZS1ibG9jazt9!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-progress-container/swui-progress-container.component.ts\";\nimport { Component } from '@angular/core';\nlet SwuiProgressContainerComponent = class SwuiProgressContainerComponent {\n    constructor() {\n    }\n    ngOnInit() {\n    }\n    static { this.ctorParameters = () => []; }\n};\nSwuiProgressContainerComponent = __decorate([\n    Component({\n        selector: 'lib-swui-progress-container',\n        template: '<mat-spinner diameter=\"40\"></mat-spinner>',\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__0]\n    })\n], SwuiProgressContainerComponent);\nexport { SwuiProgressContainerComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,seAAse;AACvgB,SAASC,SAAS,QAAQ,eAAe;AACzC,IAAIC,8BAA8B,IAAAC,+BAAA,GAAG,MAAMD,8BAA8B,CAAC;EACtEE,WAAWA,CAAA,EAAG,CACd;EACAC,QAAQA,CAAA,EAAG,CACX;AAEJ,CAAC,EADYF,+BAAA,CAAKG,cAAc,GAAG,MAAM,EAAE,EAAAH,+BAAA,CAC1C;AACDD,8BAA8B,GAAGH,UAAU,CAAC,CACxCE,SAAS,CAAC;EACNM,QAAQ,EAAE,6BAA6B;EACvCC,QAAQ,EAAE,2CAA2C;EACrDC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACV,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEE,8BAA8B,CAAC;AAClC,SAASA,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}