{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiTopMenuComponent } from './swui-top-menu.component';\nimport { HubSelectorModule } from './hub-selector/hub-selector.module';\nimport { UserMenuModule } from './user-menu/user-menu.module';\nimport { LanguageSelectorModule } from './language-selector/language-selector.module';\nimport { EntityPickerModule } from './entity-picker/entity-picker.module';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatMenuModule } from '@angular/material/menu';\nlet SwuiTopMenuModule = class SwuiTopMenuModule {};\nSwuiTopMenuModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, MatToolbarModule, HubSelectorModule, UserMenuModule, LanguageSelectorModule, EntityPickerModule, MatMenuModule],\n  declarations: [SwuiTopMenuComponent],\n  exports: [SwuiTopMenuComponent]\n})], SwuiTopMenuModule);\nexport { SwuiTopMenuModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "TranslateModule", "SwuiTopMenuComponent", "HubSelectorModule", "UserMenuModule", "LanguageSelectorModule", "EntityPickerModule", "MatToolbarModule", "MatMenuModule", "SwuiTopMenuModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/swui-top-menu.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiTopMenuComponent } from './swui-top-menu.component';\nimport { HubSelectorModule } from './hub-selector/hub-selector.module';\nimport { UserMenuModule } from './user-menu/user-menu.module';\nimport { LanguageSelectorModule } from './language-selector/language-selector.module';\nimport { EntityPickerModule } from './entity-picker/entity-picker.module';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatMenuModule } from '@angular/material/menu';\nlet SwuiTopMenuModule = class SwuiTopMenuModule {\n};\nSwuiTopMenuModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule,\n            MatToolbarModule,\n            HubSelectorModule,\n            UserMenuModule,\n            LanguageSelectorModule,\n            EntityPickerModule,\n            MatMenuModule,\n        ],\n        declarations: [\n            SwuiTopMenuComponent,\n        ],\n        exports: [\n            SwuiTopMenuComponent,\n        ]\n    })\n], SwuiTopMenuModule);\nexport { SwuiTopMenuModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,sBAAsB,QAAQ,8CAA8C;AACrF,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,IAAIC,iBAAiB,GAAG,MAAMA,iBAAiB,CAAC,EAC/C;AACDA,iBAAiB,GAAGX,UAAU,CAAC,CAC3BC,QAAQ,CAAC;EACLW,OAAO,EAAE,CACLV,YAAY,EACZC,eAAe,EACfM,gBAAgB,EAChBJ,iBAAiB,EACjBC,cAAc,EACdC,sBAAsB,EACtBC,kBAAkB,EAClBE,aAAa,CAChB;EACDG,YAAY,EAAE,CACVT,oBAAoB,CACvB;EACDU,OAAO,EAAE,CACLV,oBAAoB;AAE5B,CAAC,CAAC,CACL,EAAEO,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}