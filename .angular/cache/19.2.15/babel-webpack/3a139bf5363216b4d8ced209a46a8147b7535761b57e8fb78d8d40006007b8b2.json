{"ast": null, "code": "import { identity } from './identity';\nexport function pipe() {\n  var fns = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    fns[_i] = arguments[_i];\n  }\n  return pipeFromArray(fns);\n}\nexport function pipeFromArray(fns) {\n  if (fns.length === 0) {\n    return identity;\n  }\n  if (fns.length === 1) {\n    return fns[0];\n  }\n  return function piped(input) {\n    return fns.reduce(function (prev, fn) {\n      return fn(prev);\n    }, input);\n  };\n}\n//# sourceMappingURL=pipe.js.map", "map": {"version": 3, "names": ["identity", "pipe", "fns", "_i", "arguments", "length", "pipeFromArray", "piped", "input", "reduce", "prev", "fn"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/pipe.js"], "sourcesContent": ["import { identity } from './identity';\nexport function pipe() {\n    var fns = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        fns[_i] = arguments[_i];\n    }\n    return pipeFromArray(fns);\n}\nexport function pipeFromArray(fns) {\n    if (fns.length === 0) {\n        return identity;\n    }\n    if (fns.length === 1) {\n        return fns[0];\n    }\n    return function piped(input) {\n        return fns.reduce(function (prev, fn) { return fn(prev); }, input);\n    };\n}\n//# sourceMappingURL=pipe.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AACrC,OAAO,SAASC,IAAIA,CAAA,EAAG;EACnB,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,GAAG,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC3B;EACA,OAAOG,aAAa,CAACJ,GAAG,CAAC;AAC7B;AACA,OAAO,SAASI,aAAaA,CAACJ,GAAG,EAAE;EAC/B,IAAIA,GAAG,CAACG,MAAM,KAAK,CAAC,EAAE;IAClB,OAAOL,QAAQ;EACnB;EACA,IAAIE,GAAG,CAACG,MAAM,KAAK,CAAC,EAAE;IAClB,OAAOH,GAAG,CAAC,CAAC,CAAC;EACjB;EACA,OAAO,SAASK,KAAKA,CAACC,KAAK,EAAE;IACzB,OAAON,GAAG,CAACO,MAAM,CAAC,UAAUC,IAAI,EAAEC,EAAE,EAAE;MAAE,OAAOA,EAAE,CAACD,IAAI,CAAC;IAAE,CAAC,EAAEF,KAAK,CAAC;EACtE,CAAC;AACL;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}