{"ast": null, "code": "var _SwuiGamesSelectManagerComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./games-select-manager.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./games-select-manager.scss?ngResource\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { gameSelectItemTypes } from './game-select-item/game-select-item-types';\nimport { GamesSelectManagerService } from './games-select-manager.service';\nexport const availableTabNames = {\n  GAMES: 'games',\n  LABELS: 'labels'\n};\nlet SwuiGamesSelectManagerComponent = (_SwuiGamesSelectManagerComponent = class SwuiGamesSelectManagerComponent {\n  set availableGames(games) {\n    if (!games) {\n      return;\n    }\n    this.service.setupAvailableGames(games);\n  }\n  set availableLabels(labels) {\n    if (!labels) {\n      return;\n    }\n    this.service.setupAvailableLabels(labels);\n  }\n  set categoryItems(items) {\n    if (!items) {\n      return;\n    }\n    this.service.setupCategoryItems(items);\n  }\n  set selectedGames(games) {\n    if (!games) {\n      return;\n    }\n    this.service.setupSelectedGames(games);\n  }\n  set saveSource(source$) {\n    if (!source$) {\n      return;\n    }\n    let sub = source$.subscribe(() => {\n      this.service.setupSelectedItems([]);\n      sub.unsubscribe();\n    });\n  }\n  constructor(service) {\n    this.service = service;\n    this.selectedGamesTitle = 'COMPONENTS.GAMES_SELECT_MANAGER.selectedGamesTitle';\n    this.emptyGamesText = 'COMPONENTS.GAMES_SELECT_MANAGER.emptySelectedGames';\n    this.embedded = false;\n    this.disabled = false;\n    this.hideLabelsTab = false;\n    this.availableGamesLoading = false;\n    this.firstShowTab = availableTabNames.GAMES;\n    this.height = '500px';\n    this.selectedItemsChanged = new EventEmitter();\n    this.dropListDropped = new EventEmitter();\n    this.gamesTotal = 0;\n    this.labelsTotal = 0;\n    this.selectedGamesTotal = 0;\n    this.availableTabs = [availableTabNames.GAMES, availableTabNames.LABELS];\n    this.tabs = availableTabNames;\n    this.subs = [];\n  }\n  ngOnInit() {\n    this.subs.push(this.service.gamesTotal$.subscribe(total => {\n      this.gamesTotal = total;\n    }), this.service.labelsTotal$.subscribe(total => {\n      this.labelsTotal = total;\n    }), this.service.selectedTotal$.subscribe(total => {\n      this.selectedGamesTotal = total;\n    }), this.service.selectedItems$.subscribe(items => {\n      items = items.filter(item => item.type !== gameSelectItemTypes.CORRUPTION); // cleaning up from corrupted items\n      const games = items.filter(item => item.type === gameSelectItemTypes.GAME);\n      const labels = items.filter(item => item.isLabel);\n      this.selectedItemsChanged.emit({\n        games,\n        labels,\n        items\n      });\n    }), this.service.createdIntersection$.subscribe(item => {\n      this.intersection = item;\n    }), this.service.intersectionDestroyed$.subscribe(() => {\n      this.intersection = undefined;\n    }), this.service.checkedAvailableGamesCount$.subscribe(count => {\n      this.checkedAvailable = count;\n    }), this.service.checkedAvailableCount$.subscribe(count => {\n      this.checkedFullAvailable = count;\n    }), this.service.checkedSelectedCount$.subscribe(count => {\n      this.checkedFullSelected = count;\n    }), this.service.checkedSelectedGamesCount$.subscribe(count => {\n      this.checkedSelected = count;\n    }));\n  }\n  ngOnDestroy() {\n    this.subs.forEach(s => s.unsubscribe());\n    this.service.selectedItems$.next([]);\n  }\n  addToSelectedGames() {\n    if (this.disabled) {\n      return;\n    }\n    if (this.intersection) {\n      this.service.addIntersection(this.intersection);\n    } else {\n      this.service.addToSelectedItems();\n    }\n  }\n  removeFromSelectedGames() {\n    if (this.disabled) {\n      return;\n    }\n    this.service.removeFromSelectedItems();\n  }\n  getFirstTabIndex() {\n    let idx = this.availableTabs.indexOf(this.firstShowTab);\n    if (idx === -1) {\n      idx = 0;\n    }\n    return idx;\n  }\n  onDropListDropped(items) {\n    this.dropListDropped.emit(items);\n  }\n}, _SwuiGamesSelectManagerComponent.ctorParameters = () => [{\n  type: GamesSelectManagerService\n}], _SwuiGamesSelectManagerComponent.propDecorators = {\n  availableGames: [{\n    type: Input\n  }],\n  availableLabels: [{\n    type: Input\n  }],\n  categoryItems: [{\n    type: Input\n  }],\n  selectedGames: [{\n    type: Input\n  }],\n  selectedGamesTitle: [{\n    type: Input\n  }],\n  emptyGamesText: [{\n    type: Input\n  }],\n  selectedGamesExtraColumn: [{\n    type: Input\n  }],\n  embedded: [{\n    type: Input\n  }],\n  disabled: [{\n    type: Input\n  }],\n  hideLabelsTab: [{\n    type: Input\n  }],\n  availableGamesLoading: [{\n    type: Input\n  }],\n  firstShowTab: [{\n    type: Input\n  }],\n  height: [{\n    type: Input\n  }],\n  saveSource: [{\n    type: Input\n  }],\n  selectedItemsChanged: [{\n    type: Output\n  }],\n  dropListDropped: [{\n    type: Output\n  }]\n}, _SwuiGamesSelectManagerComponent);\nSwuiGamesSelectManagerComponent = __decorate([Component({\n  selector: 'lib-swui-games-select-manager',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiGamesSelectManagerComponent);\nexport { SwuiGamesSelectManagerComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "EventEmitter", "Input", "Output", "gameSelectItemTypes", "GamesSelectManagerService", "availableTabNames", "GAMES", "LABELS", "SwuiGamesSelectManagerComponent", "_SwuiGamesSelectManagerComponent", "availableGames", "games", "service", "setupAvailableGames", "availableLabels", "labels", "setupAvailableLabels", "categoryItems", "items", "setupCategoryItems", "selected<PERSON><PERSON><PERSON>", "setupSelectedGames", "saveSource", "source$", "sub", "subscribe", "setupSelectedItems", "unsubscribe", "constructor", "selectedGamesTitle", "emptyGamesText", "embedded", "disabled", "hideLabelsTab", "availableGamesLoading", "firstShowTab", "height", "selectedItemsChanged", "dropListDropped", "gamesTotal", "labelsTotal", "selectedGamesTotal", "availableTabs", "tabs", "subs", "ngOnInit", "push", "gamesTotal$", "total", "labelsTotal$", "selectedTotal$", "selectedItems$", "filter", "item", "type", "CORRUPTION", "GAME", "isLabel", "emit", "createdIntersection$", "intersection", "intersectionDestroyed$", "undefined", "checkedAvailableGamesCount$", "count", "checkedAvailable", "checkedAvailableCount$", "checkedFullAvailable", "checkedSelectedCount$", "checkedFullSelected", "checkedSelectedGamesCount$", "checkedSelected", "ngOnDestroy", "for<PERSON>ach", "s", "next", "addToSelectedGames", "addIntersection", "addToSelectedItems", "removeFromSelectedGames", "removeFromSelectedItems", "getFirstTabIndex", "idx", "indexOf", "onDropListDropped", "ctorParameters", "propDecorators", "selectedGamesExtraColumn", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/games-select-manager.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./games-select-manager.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./games-select-manager.scss?ngResource\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { gameSelectItemTypes } from './game-select-item/game-select-item-types';\nimport { GamesSelectManagerService } from './games-select-manager.service';\nexport const availableTabNames = {\n    GAMES: 'games',\n    LABELS: 'labels',\n};\nlet SwuiGamesSelectManagerComponent = class SwuiGamesSelectManagerComponent {\n    set availableGames(games) {\n        if (!games) {\n            return;\n        }\n        this.service.setupAvailableGames(games);\n    }\n    set availableLabels(labels) {\n        if (!labels) {\n            return;\n        }\n        this.service.setupAvailableLabels(labels);\n    }\n    set categoryItems(items) {\n        if (!items) {\n            return;\n        }\n        this.service.setupCategoryItems(items);\n    }\n    set selectedGames(games) {\n        if (!games) {\n            return;\n        }\n        this.service.setupSelectedGames(games);\n    }\n    set saveSource(source$) {\n        if (!source$) {\n            return;\n        }\n        let sub = source$.subscribe(() => {\n            this.service.setupSelectedItems([]);\n            sub.unsubscribe();\n        });\n    }\n    constructor(service) {\n        this.service = service;\n        this.selectedGamesTitle = 'COMPONENTS.GAMES_SELECT_MANAGER.selectedGamesTitle';\n        this.emptyGamesText = 'COMPONENTS.GAMES_SELECT_MANAGER.emptySelectedGames';\n        this.embedded = false;\n        this.disabled = false;\n        this.hideLabelsTab = false;\n        this.availableGamesLoading = false;\n        this.firstShowTab = availableTabNames.GAMES;\n        this.height = '500px';\n        this.selectedItemsChanged = new EventEmitter();\n        this.dropListDropped = new EventEmitter();\n        this.gamesTotal = 0;\n        this.labelsTotal = 0;\n        this.selectedGamesTotal = 0;\n        this.availableTabs = [availableTabNames.GAMES, availableTabNames.LABELS];\n        this.tabs = availableTabNames;\n        this.subs = [];\n    }\n    ngOnInit() {\n        this.subs.push(this.service.gamesTotal$.subscribe(total => {\n            this.gamesTotal = total;\n        }), this.service.labelsTotal$.subscribe(total => {\n            this.labelsTotal = total;\n        }), this.service.selectedTotal$.subscribe(total => {\n            this.selectedGamesTotal = total;\n        }), this.service.selectedItems$.subscribe((items) => {\n            items = items.filter(item => item.type !== gameSelectItemTypes.CORRUPTION); // cleaning up from corrupted items\n            const games = items.filter(item => item.type === gameSelectItemTypes.GAME);\n            const labels = items.filter(item => item.isLabel);\n            this.selectedItemsChanged.emit({ games, labels, items });\n        }), this.service.createdIntersection$.subscribe(item => {\n            this.intersection = item;\n        }), this.service.intersectionDestroyed$.subscribe(() => {\n            this.intersection = undefined;\n        }), this.service.checkedAvailableGamesCount$.subscribe(count => {\n            this.checkedAvailable = count;\n        }), this.service.checkedAvailableCount$.subscribe(count => {\n            this.checkedFullAvailable = count;\n        }), this.service.checkedSelectedCount$.subscribe(count => {\n            this.checkedFullSelected = count;\n        }), this.service.checkedSelectedGamesCount$.subscribe(count => {\n            this.checkedSelected = count;\n        }));\n    }\n    ngOnDestroy() {\n        this.subs.forEach(s => s.unsubscribe());\n        this.service.selectedItems$.next([]);\n    }\n    addToSelectedGames() {\n        if (this.disabled) {\n            return;\n        }\n        if (this.intersection) {\n            this.service.addIntersection(this.intersection);\n        }\n        else {\n            this.service.addToSelectedItems();\n        }\n    }\n    removeFromSelectedGames() {\n        if (this.disabled) {\n            return;\n        }\n        this.service.removeFromSelectedItems();\n    }\n    getFirstTabIndex() {\n        let idx = this.availableTabs.indexOf(this.firstShowTab);\n        if (idx === -1) {\n            idx = 0;\n        }\n        return idx;\n    }\n    onDropListDropped(items) {\n        this.dropListDropped.emit(items);\n    }\n    static { this.ctorParameters = () => [\n        { type: GamesSelectManagerService }\n    ]; }\n    static { this.propDecorators = {\n        availableGames: [{ type: Input }],\n        availableLabels: [{ type: Input }],\n        categoryItems: [{ type: Input }],\n        selectedGames: [{ type: Input }],\n        selectedGamesTitle: [{ type: Input }],\n        emptyGamesText: [{ type: Input }],\n        selectedGamesExtraColumn: [{ type: Input }],\n        embedded: [{ type: Input }],\n        disabled: [{ type: Input }],\n        hideLabelsTab: [{ type: Input }],\n        availableGamesLoading: [{ type: Input }],\n        firstShowTab: [{ type: Input }],\n        height: [{ type: Input }],\n        saveSource: [{ type: Input }],\n        selectedItemsChanged: [{ type: Output }],\n        dropListDropped: [{ type: Output }]\n    }; }\n};\nSwuiGamesSelectManagerComponent = __decorate([\n    Component({\n        selector: 'lib-swui-games-select-manager',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiGamesSelectManagerComponent);\nexport { SwuiGamesSelectManagerComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,kDAAkD;AACnF,OAAOC,oBAAoB,MAAM,wCAAwC;AACzE,SAASC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AACtE,SAASC,mBAAmB,QAAQ,2CAA2C;AAC/E,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,OAAO,MAAMC,iBAAiB,GAAG;EAC7BC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE;AACZ,CAAC;AACD,IAAIC,+BAA+B,IAAAC,gCAAA,GAAG,MAAMD,+BAA+B,CAAC;EACxE,IAAIE,cAAcA,CAACC,KAAK,EAAE;IACtB,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA,IAAI,CAACC,OAAO,CAACC,mBAAmB,CAACF,KAAK,CAAC;EAC3C;EACA,IAAIG,eAAeA,CAACC,MAAM,EAAE;IACxB,IAAI,CAACA,MAAM,EAAE;MACT;IACJ;IACA,IAAI,CAACH,OAAO,CAACI,oBAAoB,CAACD,MAAM,CAAC;EAC7C;EACA,IAAIE,aAAaA,CAACC,KAAK,EAAE;IACrB,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA,IAAI,CAACN,OAAO,CAACO,kBAAkB,CAACD,KAAK,CAAC;EAC1C;EACA,IAAIE,aAAaA,CAACT,KAAK,EAAE;IACrB,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA,IAAI,CAACC,OAAO,CAACS,kBAAkB,CAACV,KAAK,CAAC;EAC1C;EACA,IAAIW,UAAUA,CAACC,OAAO,EAAE;IACpB,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACA,IAAIC,GAAG,GAAGD,OAAO,CAACE,SAAS,CAAC,MAAM;MAC9B,IAAI,CAACb,OAAO,CAACc,kBAAkB,CAAC,EAAE,CAAC;MACnCF,GAAG,CAACG,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC;EACN;EACAC,WAAWA,CAAChB,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiB,kBAAkB,GAAG,oDAAoD;IAC9E,IAAI,CAACC,cAAc,GAAG,oDAAoD;IAC1E,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,YAAY,GAAG9B,iBAAiB,CAACC,KAAK;IAC3C,IAAI,CAAC8B,MAAM,GAAG,OAAO;IACrB,IAAI,CAACC,oBAAoB,GAAG,IAAIrC,YAAY,CAAC,CAAC;IAC9C,IAAI,CAACsC,eAAe,GAAG,IAAItC,YAAY,CAAC,CAAC;IACzC,IAAI,CAACuC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,aAAa,GAAG,CAACrC,iBAAiB,CAACC,KAAK,EAAED,iBAAiB,CAACE,MAAM,CAAC;IACxE,IAAI,CAACoC,IAAI,GAAGtC,iBAAiB;IAC7B,IAAI,CAACuC,IAAI,GAAG,EAAE;EAClB;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,IAAI,CAAClC,OAAO,CAACmC,WAAW,CAACtB,SAAS,CAACuB,KAAK,IAAI;MACvD,IAAI,CAACT,UAAU,GAAGS,KAAK;IAC3B,CAAC,CAAC,EAAE,IAAI,CAACpC,OAAO,CAACqC,YAAY,CAACxB,SAAS,CAACuB,KAAK,IAAI;MAC7C,IAAI,CAACR,WAAW,GAAGQ,KAAK;IAC5B,CAAC,CAAC,EAAE,IAAI,CAACpC,OAAO,CAACsC,cAAc,CAACzB,SAAS,CAACuB,KAAK,IAAI;MAC/C,IAAI,CAACP,kBAAkB,GAAGO,KAAK;IACnC,CAAC,CAAC,EAAE,IAAI,CAACpC,OAAO,CAACuC,cAAc,CAAC1B,SAAS,CAAEP,KAAK,IAAK;MACjDA,KAAK,GAAGA,KAAK,CAACkC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKnD,mBAAmB,CAACoD,UAAU,CAAC,CAAC,CAAC;MAC5E,MAAM5C,KAAK,GAAGO,KAAK,CAACkC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKnD,mBAAmB,CAACqD,IAAI,CAAC;MAC1E,MAAMzC,MAAM,GAAGG,KAAK,CAACkC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACI,OAAO,CAAC;MACjD,IAAI,CAACpB,oBAAoB,CAACqB,IAAI,CAAC;QAAE/C,KAAK;QAAEI,MAAM;QAAEG;MAAM,CAAC,CAAC;IAC5D,CAAC,CAAC,EAAE,IAAI,CAACN,OAAO,CAAC+C,oBAAoB,CAAClC,SAAS,CAAC4B,IAAI,IAAI;MACpD,IAAI,CAACO,YAAY,GAAGP,IAAI;IAC5B,CAAC,CAAC,EAAE,IAAI,CAACzC,OAAO,CAACiD,sBAAsB,CAACpC,SAAS,CAAC,MAAM;MACpD,IAAI,CAACmC,YAAY,GAAGE,SAAS;IACjC,CAAC,CAAC,EAAE,IAAI,CAAClD,OAAO,CAACmD,2BAA2B,CAACtC,SAAS,CAACuC,KAAK,IAAI;MAC5D,IAAI,CAACC,gBAAgB,GAAGD,KAAK;IACjC,CAAC,CAAC,EAAE,IAAI,CAACpD,OAAO,CAACsD,sBAAsB,CAACzC,SAAS,CAACuC,KAAK,IAAI;MACvD,IAAI,CAACG,oBAAoB,GAAGH,KAAK;IACrC,CAAC,CAAC,EAAE,IAAI,CAACpD,OAAO,CAACwD,qBAAqB,CAAC3C,SAAS,CAACuC,KAAK,IAAI;MACtD,IAAI,CAACK,mBAAmB,GAAGL,KAAK;IACpC,CAAC,CAAC,EAAE,IAAI,CAACpD,OAAO,CAAC0D,0BAA0B,CAAC7C,SAAS,CAACuC,KAAK,IAAI;MAC3D,IAAI,CAACO,eAAe,GAAGP,KAAK;IAChC,CAAC,CAAC,CAAC;EACP;EACAQ,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5B,IAAI,CAAC6B,OAAO,CAACC,CAAC,IAAIA,CAAC,CAAC/C,WAAW,CAAC,CAAC,CAAC;IACvC,IAAI,CAACf,OAAO,CAACuC,cAAc,CAACwB,IAAI,CAAC,EAAE,CAAC;EACxC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC5C,QAAQ,EAAE;MACf;IACJ;IACA,IAAI,IAAI,CAAC4B,YAAY,EAAE;MACnB,IAAI,CAAChD,OAAO,CAACiE,eAAe,CAAC,IAAI,CAACjB,YAAY,CAAC;IACnD,CAAC,MACI;MACD,IAAI,CAAChD,OAAO,CAACkE,kBAAkB,CAAC,CAAC;IACrC;EACJ;EACAC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC/C,QAAQ,EAAE;MACf;IACJ;IACA,IAAI,CAACpB,OAAO,CAACoE,uBAAuB,CAAC,CAAC;EAC1C;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAIC,GAAG,GAAG,IAAI,CAACxC,aAAa,CAACyC,OAAO,CAAC,IAAI,CAAChD,YAAY,CAAC;IACvD,IAAI+C,GAAG,KAAK,CAAC,CAAC,EAAE;MACZA,GAAG,GAAG,CAAC;IACX;IACA,OAAOA,GAAG;EACd;EACAE,iBAAiBA,CAAClE,KAAK,EAAE;IACrB,IAAI,CAACoB,eAAe,CAACoB,IAAI,CAACxC,KAAK,CAAC;EACpC;AAsBJ,CAAC,EArBYT,gCAAA,CAAK4E,cAAc,GAAG,MAAM,CACjC;EAAE/B,IAAI,EAAElD;AAA0B,CAAC,CACtC,EACQK,gCAAA,CAAK6E,cAAc,GAAG;EAC3B5E,cAAc,EAAE,CAAC;IAAE4C,IAAI,EAAErD;EAAM,CAAC,CAAC;EACjCa,eAAe,EAAE,CAAC;IAAEwC,IAAI,EAAErD;EAAM,CAAC,CAAC;EAClCgB,aAAa,EAAE,CAAC;IAAEqC,IAAI,EAAErD;EAAM,CAAC,CAAC;EAChCmB,aAAa,EAAE,CAAC;IAAEkC,IAAI,EAAErD;EAAM,CAAC,CAAC;EAChC4B,kBAAkB,EAAE,CAAC;IAAEyB,IAAI,EAAErD;EAAM,CAAC,CAAC;EACrC6B,cAAc,EAAE,CAAC;IAAEwB,IAAI,EAAErD;EAAM,CAAC,CAAC;EACjCsF,wBAAwB,EAAE,CAAC;IAAEjC,IAAI,EAAErD;EAAM,CAAC,CAAC;EAC3C8B,QAAQ,EAAE,CAAC;IAAEuB,IAAI,EAAErD;EAAM,CAAC,CAAC;EAC3B+B,QAAQ,EAAE,CAAC;IAAEsB,IAAI,EAAErD;EAAM,CAAC,CAAC;EAC3BgC,aAAa,EAAE,CAAC;IAAEqB,IAAI,EAAErD;EAAM,CAAC,CAAC;EAChCiC,qBAAqB,EAAE,CAAC;IAAEoB,IAAI,EAAErD;EAAM,CAAC,CAAC;EACxCkC,YAAY,EAAE,CAAC;IAAEmB,IAAI,EAAErD;EAAM,CAAC,CAAC;EAC/BmC,MAAM,EAAE,CAAC;IAAEkB,IAAI,EAAErD;EAAM,CAAC,CAAC;EACzBqB,UAAU,EAAE,CAAC;IAAEgC,IAAI,EAAErD;EAAM,CAAC,CAAC;EAC7BoC,oBAAoB,EAAE,CAAC;IAAEiB,IAAI,EAAEpD;EAAO,CAAC,CAAC;EACxCoC,eAAe,EAAE,CAAC;IAAEgB,IAAI,EAAEpD;EAAO,CAAC;AACtC,CAAC,EAAAO,gCAAA,CACJ;AACDD,+BAA+B,GAAGZ,UAAU,CAAC,CACzCG,SAAS,CAAC;EACNyF,QAAQ,EAAE,+BAA+B;EACzCC,QAAQ,EAAE5F,oBAAoB;EAC9B6F,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC7F,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEU,+BAA+B,CAAC;AACnC,SAASA,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}