{"ast": null, "code": "var _SwuiCalendarComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-calendar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-calendar.component.scss?ngResource\";\nimport { Component, forwardRef, HostBinding, HostListener, Input } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as moment from 'moment';\nfunction toMoment(value) {\n  if (typeof value === 'undefined' || value === null) {\n    return null;\n  }\n  if (moment.isMoment(value)) {\n    return value;\n  }\n  const date = moment.parseZone(value);\n  if (date.isValid()) {\n    return date;\n  }\n  return null;\n}\nlet SwuiCalendarComponent = (_SwuiCalendarComponent = class SwuiCalendarComponent {\n  set timeZone(val) {\n    if (!val) {\n      return;\n    }\n    this._timeZone = val;\n    const current = moment.tz(val);\n    if (current.isValid()) {\n      this.currentDate = current.clone();\n      this.setMonth(this.currentDate);\n    }\n  }\n  get timeZone() {\n    return this._timeZone;\n  }\n  constructor() {\n    this.weekDayNames = moment.weekdaysShort();\n    this.monthNames = moment.monthsShort();\n    this.currentDate = moment.utc();\n    this.currentMonth = [];\n    this.isCalendarDisabled = false;\n    this.tabindex = 0;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.setMonth(this.currentDate);\n  }\n  onblur() {\n    this.onTouched();\n  }\n  writeValue(value) {\n    const date = toMoment(value);\n    if (date && !this.isDayDisabled(date)) {\n      this.selectedDate = date.clone();\n      if (!this.selectedDate.isSame(this.currentDate, 'month')) {\n        this.setMonth(this.selectedDate.clone());\n      }\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.isCalendarDisabled = disabled;\n  }\n  nextMonth(event) {\n    event.preventDefault();\n    this.setMonth(this.currentDate.add(1, 'months'));\n  }\n  prevMonth(event) {\n    event.preventDefault();\n    this.setMonth(this.currentDate.add(-1, 'months'));\n  }\n  selectDay(day, event) {\n    if (event) {\n      event.preventDefault();\n    }\n    if (day && !this.isDayDisabled(day)) {\n      this.selectedDate = day.clone();\n      if (this.selectedDate && !this.selectedDate.isSame(this.currentDate, 'month')) {\n        this.setMonth(this.selectedDate.clone());\n      }\n      this.onChange(this.selectedDate);\n    }\n  }\n  isDayToday(day) {\n    return day ? day.isSame(this.timeZone ? moment.tz(this.timeZone) : moment.utc(), 'date') : false;\n  }\n  isDaySelected(day) {\n    return day && this.selectedDate ? day.isSame(this.selectedDate, 'date') : false;\n  }\n  isDayDisabled(day) {\n    const minDate = this.minDate ? toMoment(this.minDate) : undefined;\n    const maxDate = this.maxDate ? toMoment(this.maxDate) : undefined;\n    return day && minDate && day.isBefore(minDate, 'date') || day && maxDate && day.isAfter(maxDate, 'date') || false;\n  }\n  setMonth(date) {\n    const firstDay = date.clone().startOf('month');\n    const lastDay = date.clone().endOf('month');\n    const result = [];\n    while (firstDay.date() <= lastDay.date()) {\n      if (!result.length || !firstDay.day()) {\n        result.push([]);\n      }\n      result[result.length - 1][firstDay.day()] = firstDay.clone();\n      if (firstDay.date() === lastDay.date()) {\n        break;\n      } else {\n        firstDay.add(1, 'days');\n      }\n    }\n    this.currentDate = date.clone().startOf('day');\n    this.currentMonth = result;\n  }\n}, _SwuiCalendarComponent.ctorParameters = () => [], _SwuiCalendarComponent.propDecorators = {\n  minDate: [{\n    type: Input\n  }],\n  maxDate: [{\n    type: Input\n  }],\n  timeZone: [{\n    type: Input\n  }],\n  tabindex: [{\n    type: HostBinding,\n    args: ['attr.tabindex']\n  }],\n  onblur: [{\n    type: HostListener,\n    args: ['blur']\n  }]\n}, _SwuiCalendarComponent);\nSwuiCalendarComponent = __decorate([Component({\n  selector: 'lib-swui-calendar',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SwuiCalendarComponent),\n    multi: true\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiCalendarComponent);\nexport { SwuiCalendarComponent };", "map": {"version": 3, "names": ["Component", "forwardRef", "HostBinding", "HostListener", "Input", "NG_VALUE_ACCESSOR", "moment", "toMoment", "value", "isMoment", "date", "parseZone", "<PERSON><PERSON><PERSON><PERSON>", "SwuiCalendarComponent", "_SwuiCalendarComponent", "timeZone", "val", "_timeZone", "current", "tz", "currentDate", "clone", "setMonth", "constructor", "weekDayNames", "weekdaysShort", "monthNames", "monthsShort", "utc", "currentMonth", "isCalendarDisabled", "tabindex", "onChange", "onTouched", "onblur", "writeValue", "isDayDisabled", "selectedDate", "isSame", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "disabled", "nextMonth", "event", "preventDefault", "add", "prevMonth", "selectDay", "day", "isDayToday", "isDaySelected", "minDate", "undefined", "maxDate", "isBefore", "isAfter", "firstDay", "startOf", "lastDay", "endOf", "result", "length", "push", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "provide", "useExisting", "multi", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-calendar/swui-calendar.component.ts"], "sourcesContent": ["import { Component, forwardRef, HostBinding, HostListener, Input } from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as moment from 'moment';\n\nfunction toMoment( value: moment.Moment | string | undefined | null ): moment.Moment | null {\n  if (typeof value === 'undefined' || value === null) {\n    return null;\n  }\n  if (moment.isMoment(value)) {\n    return value;\n  }\n  const date = moment.parseZone(value);\n  if (date.isValid()) {\n    return date;\n  }\n  return null;\n}\n\n@Component({\n    selector: 'lib-swui-calendar',\n    templateUrl: './swui-calendar.component.html',\n    styleUrls: ['./swui-calendar.component.scss'],\n    providers: [\n        {\n            provide: NG_VALUE_ACCESSOR,\n            useExisting: forwardRef(() => SwuiCalendarComponent),\n            multi: true\n        },\n    ],\n    standalone: false\n})\n\nexport class SwuiCalendarComponent implements ControlValueAccessor {\n  @Input() minDate?: moment.Moment | string;\n  @Input() maxDate?: moment.Moment | string;\n\n  @Input()\n  set timeZone( val: string | undefined ) {\n    if (!val) {\n      return;\n    }\n    this._timeZone = val;\n    const current = moment.tz(val);\n    if (current.isValid()) {\n      this.currentDate = current.clone();\n      this.setMonth(this.currentDate);\n    }\n  }\n\n  get timeZone(): string | undefined {\n    return this._timeZone;\n  }\n\n  weekDayNames = moment.weekdaysShort();\n  monthNames = moment.monthsShort();\n  currentDate: moment.Moment = moment.utc();\n  currentMonth: moment.Moment[][] = [];\n  selectedDate: moment.Moment | undefined;\n  isCalendarDisabled = false;\n\n  @HostBinding('attr.tabindex')\n  public tabindex = 0;\n\n  onChange: ( _: any ) => void = (() => {\n  });\n\n  private _timeZone?: string;\n\n  constructor() {\n    this.setMonth(this.currentDate);\n  }\n\n  @HostListener('blur') onblur() {\n    this.onTouched();\n  }\n\n  onTouched: any = () => {\n  };\n\n  writeValue( value: moment.Moment | string | undefined | null ) {\n    const date = toMoment(value);\n    if (date && !this.isDayDisabled(date)) {\n      this.selectedDate = date.clone();\n      if (!this.selectedDate.isSame(this.currentDate, 'month')) {\n        this.setMonth(this.selectedDate.clone());\n      }\n    }\n  }\n\n  registerOnChange( fn: ( _: any ) => void ): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched( fn: any ): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState( disabled: boolean ) {\n    this.isCalendarDisabled = disabled;\n  }\n\n  nextMonth( event: MouseEvent ) {\n    event.preventDefault();\n    this.setMonth(this.currentDate.add(1, 'months'));\n  }\n\n  prevMonth( event: MouseEvent ) {\n    event.preventDefault();\n    this.setMonth(this.currentDate.add(-1, 'months'));\n  }\n\n  selectDay( day: moment.Moment | null, event?: any ): void {\n    if (event) {\n      event.preventDefault();\n    }\n    if (day && !this.isDayDisabled(day)) {\n      this.selectedDate = day.clone();\n      if (this.selectedDate && !this.selectedDate.isSame(this.currentDate, 'month')) {\n        this.setMonth(this.selectedDate.clone());\n      }\n      this.onChange(this.selectedDate);\n    }\n  }\n\n  isDayToday( day: moment.Moment ): boolean {\n    return day ? day.isSame(this.timeZone ? moment.tz(this.timeZone) : moment.utc(), 'date') : false;\n  }\n\n  isDaySelected( day: moment.Moment ): boolean {\n    return day && this.selectedDate ? day.isSame(this.selectedDate, 'date') : false;\n  }\n\n  isDayDisabled( day: moment.Moment ): boolean {\n    const minDate = this.minDate ? toMoment(this.minDate) : undefined;\n    const maxDate = this.maxDate ? toMoment(this.maxDate) : undefined;\n    return (day && minDate && day.isBefore(minDate, 'date')) ||\n      (day && maxDate && day.isAfter(maxDate, 'date')) ||\n      false;\n  }\n\n  private setMonth( date: moment.Moment ) {\n    const firstDay = date.clone().startOf('month');\n    const lastDay = date.clone().endOf('month');\n    const result: moment.Moment[][] = [];\n    while (firstDay.date() <= lastDay.date()) {\n      if (!result.length || !firstDay.day()) {\n        result.push([]);\n      }\n      result[result.length - 1][firstDay.day()] = firstDay.clone();\n      if (firstDay.date() === lastDay.date()) {\n        break;\n      } else {\n        firstDay.add(1, 'days');\n      }\n    }\n    this.currentDate = date.clone().startOf('day');\n    this.currentMonth = result;\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,KAAK,QAAQ,eAAe;AACvF,SAA+BC,iBAAiB,QAAQ,gBAAgB;AACxE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAEhC,SAASC,QAAQA,CAAEC,KAAgD;EACjE,IAAI,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,EAAE;IAClD,OAAO,IAAI;EACb;EACA,IAAIF,MAAM,CAACG,QAAQ,CAACD,KAAK,CAAC,EAAE;IAC1B,OAAOA,KAAK;EACd;EACA,MAAME,IAAI,GAAGJ,MAAM,CAACK,SAAS,CAACH,KAAK,CAAC;EACpC,IAAIE,IAAI,CAACE,OAAO,EAAE,EAAE;IAClB,OAAOF,IAAI;EACb;EACA,OAAO,IAAI;AACb;AAgBO,IAAMG,qBAAqB,IAAAC,sBAAA,GAA3B,MAAMD,qBAAqB;MAK5BE,QAAQA,CAAEC,GAAuB;IACnC,IAAI,CAACA,GAAG,EAAE;MACR;IACF;IACA,IAAI,CAACC,SAAS,GAAGD,GAAG;IACpB,MAAME,OAAO,GAAGZ,MAAM,CAACa,EAAE,CAACH,GAAG,CAAC;IAC9B,IAAIE,OAAO,CAACN,OAAO,EAAE,EAAE;MACrB,IAAI,CAACQ,WAAW,GAAGF,OAAO,CAACG,KAAK,EAAE;MAClC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC;IACjC;EACF;EAEA,IAAIL,QAAQA,CAAA;IACV,OAAO,IAAI,CAACE,SAAS;EACvB;EAiBAM,YAAA;IAfA,KAAAC,YAAY,GAAGlB,MAAM,CAACmB,aAAa,EAAE;IACrC,KAAAC,UAAU,GAAGpB,MAAM,CAACqB,WAAW,EAAE;IACjC,KAAAP,WAAW,GAAkBd,MAAM,CAACsB,GAAG,EAAE;IACzC,KAAAC,YAAY,GAAsB,EAAE;IAEpC,KAAAC,kBAAkB,GAAG,KAAK;IAGnB,KAAAC,QAAQ,GAAG,CAAC;IAEnB,KAAAC,QAAQ,GAAwB,MAAK,CACrC,CAAE;IAYF,KAAAC,SAAS,GAAQ,MAAK,CACtB,CAAC;IARC,IAAI,CAACX,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC;EACjC;EAEsBc,MAAMA,CAAA;IAC1B,IAAI,CAACD,SAAS,EAAE;EAClB;EAKAE,UAAUA,CAAE3B,KAAgD;IAC1D,MAAME,IAAI,GAAGH,QAAQ,CAACC,KAAK,CAAC;IAC5B,IAAIE,IAAI,IAAI,CAAC,IAAI,CAAC0B,aAAa,CAAC1B,IAAI,CAAC,EAAE;MACrC,IAAI,CAAC2B,YAAY,GAAG3B,IAAI,CAACW,KAAK,EAAE;MAChC,IAAI,CAAC,IAAI,CAACgB,YAAY,CAACC,MAAM,CAAC,IAAI,CAAClB,WAAW,EAAE,OAAO,CAAC,EAAE;QACxD,IAAI,CAACE,QAAQ,CAAC,IAAI,CAACe,YAAY,CAAChB,KAAK,EAAE,CAAC;MAC1C;IACF;EACF;EAEAkB,gBAAgBA,CAAEC,EAAsB;IACtC,IAAI,CAACR,QAAQ,GAAGQ,EAAE;EACpB;EAEAC,iBAAiBA,CAAED,EAAO;IACxB,IAAI,CAACP,SAAS,GAAGO,EAAE;EACrB;EAEAE,gBAAgBA,CAAEC,QAAiB;IACjC,IAAI,CAACb,kBAAkB,GAAGa,QAAQ;EACpC;EAEAC,SAASA,CAAEC,KAAiB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACxB,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC2B,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;EAClD;EAEAC,SAASA,CAAEH,KAAiB;IAC1BA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACxB,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC2B,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;EACnD;EAEAE,SAASA,CAAEC,GAAyB,EAAEL,KAAW;IAC/C,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACC,cAAc,EAAE;IACxB;IACA,IAAII,GAAG,IAAI,CAAC,IAAI,CAACd,aAAa,CAACc,GAAG,CAAC,EAAE;MACnC,IAAI,CAACb,YAAY,GAAGa,GAAG,CAAC7B,KAAK,EAAE;MAC/B,IAAI,IAAI,CAACgB,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACC,MAAM,CAAC,IAAI,CAAClB,WAAW,EAAE,OAAO,CAAC,EAAE;QAC7E,IAAI,CAACE,QAAQ,CAAC,IAAI,CAACe,YAAY,CAAChB,KAAK,EAAE,CAAC;MAC1C;MACA,IAAI,CAACW,QAAQ,CAAC,IAAI,CAACK,YAAY,CAAC;IAClC;EACF;EAEAc,UAAUA,CAAED,GAAkB;IAC5B,OAAOA,GAAG,GAAGA,GAAG,CAACZ,MAAM,CAAC,IAAI,CAACvB,QAAQ,GAAGT,MAAM,CAACa,EAAE,CAAC,IAAI,CAACJ,QAAQ,CAAC,GAAGT,MAAM,CAACsB,GAAG,EAAE,EAAE,MAAM,CAAC,GAAG,KAAK;EAClG;EAEAwB,aAAaA,CAAEF,GAAkB;IAC/B,OAAOA,GAAG,IAAI,IAAI,CAACb,YAAY,GAAGa,GAAG,CAACZ,MAAM,CAAC,IAAI,CAACD,YAAY,EAAE,MAAM,CAAC,GAAG,KAAK;EACjF;EAEAD,aAAaA,CAAEc,GAAkB;IAC/B,MAAMG,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG9C,QAAQ,CAAC,IAAI,CAAC8C,OAAO,CAAC,GAAGC,SAAS;IACjE,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO,GAAGhD,QAAQ,CAAC,IAAI,CAACgD,OAAO,CAAC,GAAGD,SAAS;IACjE,OAAQJ,GAAG,IAAIG,OAAO,IAAIH,GAAG,CAACM,QAAQ,CAACH,OAAO,EAAE,MAAM,CAAC,IACpDH,GAAG,IAAIK,OAAO,IAAIL,GAAG,CAACO,OAAO,CAACF,OAAO,EAAE,MAAM,CAAE,IAChD,KAAK;EACT;EAEQjC,QAAQA,CAAEZ,IAAmB;IACnC,MAAMgD,QAAQ,GAAGhD,IAAI,CAACW,KAAK,EAAE,CAACsC,OAAO,CAAC,OAAO,CAAC;IAC9C,MAAMC,OAAO,GAAGlD,IAAI,CAACW,KAAK,EAAE,CAACwC,KAAK,CAAC,OAAO,CAAC;IAC3C,MAAMC,MAAM,GAAsB,EAAE;IACpC,OAAOJ,QAAQ,CAAChD,IAAI,EAAE,IAAIkD,OAAO,CAAClD,IAAI,EAAE,EAAE;MACxC,IAAI,CAACoD,MAAM,CAACC,MAAM,IAAI,CAACL,QAAQ,CAACR,GAAG,EAAE,EAAE;QACrCY,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC;MACjB;MACAF,MAAM,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAACL,QAAQ,CAACR,GAAG,EAAE,CAAC,GAAGQ,QAAQ,CAACrC,KAAK,EAAE;MAC5D,IAAIqC,QAAQ,CAAChD,IAAI,EAAE,KAAKkD,OAAO,CAAClD,IAAI,EAAE,EAAE;QACtC;MACF,CAAC,MAAM;QACLgD,QAAQ,CAACX,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;MACzB;IACF;IACA,IAAI,CAAC3B,WAAW,GAAGV,IAAI,CAACW,KAAK,EAAE,CAACsC,OAAO,CAAC,KAAK,CAAC;IAC9C,IAAI,CAAC9B,YAAY,GAAGiC,MAAM;EAC5B;;;UA5HC1D;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UAwBLF,WAAW;IAAA+D,IAAA,GAAC,eAAe;EAAA;;UAY3B9D,YAAY;IAAA8D,IAAA,GAAC,MAAM;EAAA;;AAxCTpD,qBAAqB,GAAAqD,UAAA,EAdjClE,SAAS,CAAC;EACPmE,QAAQ,EAAE,mBAAmB;EAC7BC,QAAA,EAAAC,oBAA6C;EAE7CC,SAAS,EAAE,CACP;IACIC,OAAO,EAAElE,iBAAiB;IAC1BmE,WAAW,EAAEvE,UAAU,CAAC,MAAMY,qBAAqB,CAAC;IACpD4D,KAAK,EAAE;GACV,CACJ;EACDC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EAEW7D,qBAAqB,CA8HjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}