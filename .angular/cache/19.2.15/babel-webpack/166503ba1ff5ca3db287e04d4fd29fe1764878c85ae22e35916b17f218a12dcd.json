{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function isScheduler(value) {\n  return value && isFunction(value.schedule);\n}\n//# sourceMappingURL=isScheduler.js.map", "map": {"version": 3, "names": ["isFunction", "isScheduler", "value", "schedule"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/isScheduler.js"], "sourcesContent": ["import { isFunction } from './isFunction';\nexport function isScheduler(value) {\n    return value && isFunction(value.schedule);\n}\n//# sourceMappingURL=isScheduler.js.map"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC/B,OAAOA,KAAK,IAAIF,UAAU,CAACE,KAAK,CAACC,QAAQ,CAAC;AAC9C;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}