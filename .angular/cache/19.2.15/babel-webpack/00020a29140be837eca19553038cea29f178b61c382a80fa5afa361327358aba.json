{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiControlStoriesComponent } from './swui-control-stories.component';\nimport { SwuiControlMessagesModule } from '../../swui-control-messages.module';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nlet SwuiControlStoriesModule = class SwuiControlStoriesModule {};\nSwuiControlStoriesModule = __decorate([NgModule({\n  imports: [BrowserAnimationsModule, CommonModule, ReactiveFormsModule, MatFormFieldModule, SwuiControlMessagesModule, MatInputModule, TranslateModule.forRoot()],\n  declarations: [SwuiControlStoriesComponent]\n})], SwuiControlStoriesModule);\nexport { SwuiControlStoriesModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "ReactiveFormsModule", "SwuiControlStoriesComponent", "SwuiControlMessagesModule", "BrowserAnimationsModule", "TranslateModule", "MatFormFieldModule", "MatInputModule", "SwuiControlStoriesModule", "imports", "forRoot", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-control-messages/stories/swui-control-stories/swui-control-stories.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiControlStoriesComponent } from './swui-control-stories.component';\nimport { SwuiControlMessagesModule } from '../../swui-control-messages.module';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nlet SwuiControlStoriesModule = class SwuiControlStoriesModule {\n};\nSwuiControlStoriesModule = __decorate([\n    NgModule({\n        imports: [\n            BrowserAnimationsModule,\n            CommonModule,\n            ReactiveFormsModule,\n            MatFormFieldModule,\n            SwuiControlMessagesModule,\n            MatInputModule,\n            TranslateModule.forRoot(),\n        ],\n        declarations: [\n            SwuiControlStoriesComponent\n        ],\n    })\n], SwuiControlStoriesModule);\nexport { SwuiControlStoriesModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,yBAAyB,QAAQ,oCAAoC;AAC9E,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,IAAIC,wBAAwB,GAAG,MAAMA,wBAAwB,CAAC,EAC7D;AACDA,wBAAwB,GAAGV,UAAU,CAAC,CAClCC,QAAQ,CAAC;EACLU,OAAO,EAAE,CACLL,uBAAuB,EACvBJ,YAAY,EACZC,mBAAmB,EACnBK,kBAAkB,EAClBH,yBAAyB,EACzBI,cAAc,EACdF,eAAe,CAACK,OAAO,CAAC,CAAC,CAC5B;EACDC,YAAY,EAAE,CACVT,2BAA2B;AAEnC,CAAC,CAAC,CACL,EAAEM,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}