{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _PlatformModule;\nexport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nexport { n as normalizePassiveListenerOptions, s as supportsPassiveEventListeners } from './passive-listeners-esHZRgIN.mjs';\nexport { R as RtlScrollAxisType, g as getRtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nexport { _ as _getEventTarget, c as _getFocusedElementPierceShadowDom, a as _getShadowRoot, b as _supportsShadowDom } from './shadow-dom-B0oHn41l.mjs';\nexport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nexport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport '@angular/common';\nclass PlatformModule {}\n_PlatformModule = PlatformModule;\n_defineProperty(PlatformModule, \"\\u0275fac\", function _PlatformModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PlatformModule)();\n});\n_defineProperty(PlatformModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _PlatformModule\n}));\n_defineProperty(PlatformModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n// `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n// first changing it to something else:\n// The specified value \"\" does not conform to the required format.\n// The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n'color', 'button', 'checkbox', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week'];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n  return supportedInputTypes;\n}\nexport { PlatformModule, getSupportedInputTypes };\n//# sourceMappingURL=platform.mjs.map", "map": {"version": 3, "names": ["P", "Platform", "i0", "NgModule", "n", "normalizePassiveListenerOptions", "s", "supportsPassiveEventListeners", "R", "RtlScrollAxisType", "g", "getRtlScrollAxisType", "supportsScrollBehavior", "_", "_getEventTarget", "c", "_getFocusedElementPierceShadowDom", "a", "_getShadowRoot", "b", "_supportsShadowDom", "_isTestEnvironment", "_bindEventWithOptions", "PlatformModule", "_PlatformModule", "_defineProperty", "_PlatformModule_Factory", "__ngFactoryType__", "ɵɵdefineNgModule", "type", "ɵɵdefineInjector", "ngDevMode", "ɵsetClassMetadata", "args", "supportedInputTypes", "candidateInputTypes", "getSupportedInputTypes", "document", "Set", "featureTestInput", "createElement", "filter", "value", "setAttribute"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/platform.mjs"], "sourcesContent": ["export { P as Platform } from './platform-DmdVEw_C.mjs';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nexport { n as normalizePassiveListenerOptions, s as supportsPassiveEventListeners } from './passive-listeners-esHZRgIN.mjs';\nexport { R as RtlScrollAxisType, g as getRtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nexport { _ as _getEventTarget, c as _getFocusedElementPierceShadowDom, a as _getShadowRoot, b as _supportsShadowDom } from './shadow-dom-B0oHn41l.mjs';\nexport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nexport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport '@angular/common';\n\nclass PlatformModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PlatformModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: PlatformModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PlatformModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: PlatformModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n    // `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n    // first changing it to something else:\n    // The specified value \"\" does not conform to the required format.\n    // The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n    'color',\n    'button',\n    'checkbox',\n    'date',\n    'datetime-local',\n    'email',\n    'file',\n    'hidden',\n    'image',\n    'month',\n    'number',\n    'password',\n    'radio',\n    'range',\n    'reset',\n    'search',\n    'submit',\n    'tel',\n    'text',\n    'time',\n    'url',\n    'week',\n];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n    // Result is cached.\n    if (supportedInputTypes) {\n        return supportedInputTypes;\n    }\n    // We can't check if an input type is not supported until we're on the browser, so say that\n    // everything is supported when not on the browser. We don't use `Platform` here since it's\n    // just a helper function and can't inject it.\n    if (typeof document !== 'object' || !document) {\n        supportedInputTypes = new Set(candidateInputTypes);\n        return supportedInputTypes;\n    }\n    let featureTestInput = document.createElement('input');\n    supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n        featureTestInput.setAttribute('type', value);\n        return featureTestInput.type === value;\n    }));\n    return supportedInputTypes;\n}\n\nexport { PlatformModule, getSupportedInputTypes };\n//# sourceMappingURL=platform.mjs.map\n"], "mappings": ";;AAAA,SAASA,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,+BAA+B,EAAEC,CAAC,IAAIC,6BAA6B,QAAQ,kCAAkC;AAC3H,SAASC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEL,CAAC,IAAIM,sBAAsB,QAAQ,0BAA0B;AACzH,SAASC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,iCAAiC,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,kBAAkB,QAAQ,2BAA2B;AACtJ,SAASP,CAAC,IAAIQ,kBAAkB,QAAQ,iCAAiC;AACzE,SAASR,CAAC,IAAIS,qBAAqB,QAAQ,wCAAwC;AACnF,OAAO,iBAAiB;AAExB,MAAMC,cAAc,CAAC;AAIpBC,eAAA,GAJKD,cAAc;AAAAE,eAAA,CAAdF,cAAc,wBAAAG,wBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IACmFJ,eAAc;AAAA;AAAAE,eAAA,CAD/GF,cAAc,8BAK6DrB,EAAE,CAAA0B,gBAAA;EAAAC,IAAA,EAHqBN;AAAc;AAAAE,eAAA,CAFhHF,cAAc,8BAK6DrB,EAAE,CAAA4B,gBAAA;AAAnF;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF7B,EAAE,CAAA8B,iBAAA,CAAQT,cAAc,EAAc,CAAC;IAC5GM,IAAI,EAAE1B,QAAQ;IACd8B,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AAAA;;AAEV;AACA,IAAIC,mBAAmB;AACvB;AACA,MAAMC,mBAAmB,GAAG;AACxB;AACA;AACA;AACA;AACA,OAAO,EACP,QAAQ,EACR,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,QAAQ,EACR,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,CACT;AACD;AACA,SAASC,sBAAsBA,CAAA,EAAG;EAC9B;EACA,IAAIF,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB;EAC9B;EACA;EACA;EACA;EACA,IAAI,OAAOG,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,EAAE;IAC3CH,mBAAmB,GAAG,IAAII,GAAG,CAACH,mBAAmB,CAAC;IAClD,OAAOD,mBAAmB;EAC9B;EACA,IAAIK,gBAAgB,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EACtDN,mBAAmB,GAAG,IAAII,GAAG,CAACH,mBAAmB,CAACM,MAAM,CAACC,KAAK,IAAI;IAC9DH,gBAAgB,CAACI,YAAY,CAAC,MAAM,EAAED,KAAK,CAAC;IAC5C,OAAOH,gBAAgB,CAACV,IAAI,KAAKa,KAAK;EAC1C,CAAC,CAAC,CAAC;EACH,OAAOR,mBAAmB;AAC9B;AAEA,SAASX,cAAc,EAAEa,sBAAsB;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}