{"ast": null, "code": "var _SwuiTdCalcWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./calc.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdCalcWidgetComponent = (_SwuiTdCalcWidgetComponent = class SwuiTdCalcWidgetComponent {\n  constructor({\n    value,\n    schema,\n    row\n  }, sanitizer) {\n    var _schema$td, _schema$td2, _schema$td3;\n    this.value = value;\n    this.row = row;\n    this.truncate = schema.td && schema.td.truncate ? schema.td.truncate : undefined;\n    const titleFn = (_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.titleFn;\n    const classFn = (_schema$td2 = schema.td) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.classFn;\n    this.titleFn = titleFn && titleFn(this.row, schema) || this.value;\n    this.classObj = classFn && classFn(this.row, schema);\n    this.useTranslate = schema.td && 'useTranslate' in schema.td ? ((_schema$td3 = schema.td) === null || _schema$td3 === void 0 ? void 0 : _schema$td3.useTranslate) || false : true;\n    // TODO: unsafe operation\n    if (schema.td && schema.td.sanitizeValue) {\n      this.titleFn = sanitizer.bypassSecurityTrustHtml(this.titleFn);\n    }\n  }\n}, _SwuiTdCalcWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}, {\n  type: DomSanitizer\n}], _SwuiTdCalcWidgetComponent);\nSwuiTdCalcWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-calc-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdCalcWidgetComponent);\nexport { SwuiTdCalcWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "Dom<PERSON><PERSON><PERSON>zer", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdCalcWidgetComponent", "_SwuiTdCalcWidgetComponent", "constructor", "value", "schema", "row", "sanitizer", "_schema$td", "_schema$td2", "_schema$td3", "truncate", "td", "undefined", "titleFn", "classFn", "classObj", "useTranslate", "sanitizeValue", "bypassSecurityTrustHtml", "ctorParameters", "type", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/calc/calc.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./calc.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdCalcWidgetComponent = class SwuiTdCalcWidgetComponent {\n    constructor({ value, schema, row }, sanitizer) {\n        this.value = value;\n        this.row = row;\n        this.truncate = schema.td && schema.td.truncate ? schema.td.truncate : undefined;\n        const titleFn = schema.td?.titleFn;\n        const classFn = schema.td?.classFn;\n        this.titleFn = (titleFn && titleFn(this.row, schema)) || this.value;\n        this.classObj = classFn && classFn(this.row, schema);\n        this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;\n        // TODO: unsafe operation\n        if (schema.td && schema.td.sanitizeValue) {\n            this.titleFn = sanitizer.bypassSecurityTrustHtml(this.titleFn);\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] },\n        { type: DomSanitizer }\n    ]; }\n};\nSwuiTdCalcWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-calc-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdCalcWidgetComponent);\nexport { SwuiTdCalcWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,CAAC;EAC5DE,WAAWA,CAAC;IAAEC,KAAK;IAAEC,MAAM;IAAEC;EAAI,CAAC,EAAEC,SAAS,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA;IAC3C,IAAI,CAACN,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,GAAG,GAAGA,GAAG;IACd,IAAI,CAACK,QAAQ,GAAGN,MAAM,CAACO,EAAE,IAAIP,MAAM,CAACO,EAAE,CAACD,QAAQ,GAAGN,MAAM,CAACO,EAAE,CAACD,QAAQ,GAAGE,SAAS;IAChF,MAAMC,OAAO,IAAAN,UAAA,GAAGH,MAAM,CAACO,EAAE,cAAAJ,UAAA,uBAATA,UAAA,CAAWM,OAAO;IAClC,MAAMC,OAAO,IAAAN,WAAA,GAAGJ,MAAM,CAACO,EAAE,cAAAH,WAAA,uBAATA,WAAA,CAAWM,OAAO;IAClC,IAAI,CAACD,OAAO,GAAIA,OAAO,IAAIA,OAAO,CAAC,IAAI,CAACR,GAAG,EAAED,MAAM,CAAC,IAAK,IAAI,CAACD,KAAK;IACnE,IAAI,CAACY,QAAQ,GAAGD,OAAO,IAAIA,OAAO,CAAC,IAAI,CAACT,GAAG,EAAED,MAAM,CAAC;IACpD,IAAI,CAACY,YAAY,GAAGZ,MAAM,CAACO,EAAE,IAAI,cAAc,IAAIP,MAAM,CAACO,EAAE,GAAG,EAAAF,WAAA,GAAAL,MAAM,CAACO,EAAE,cAAAF,WAAA,uBAATA,WAAA,CAAWO,YAAY,KAAI,KAAK,GAAG,IAAI;IACtG;IACA,IAAIZ,MAAM,CAACO,EAAE,IAAIP,MAAM,CAACO,EAAE,CAACM,aAAa,EAAE;MACtC,IAAI,CAACJ,OAAO,GAAGP,SAAS,CAACY,uBAAuB,CAAC,IAAI,CAACL,OAAO,CAAC;IAClE;EACJ;AAKJ,CAAC,EAJYZ,0BAAA,CAAKkB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAER,SAAS;EAAES,UAAU,EAAE,CAAC;IAAED,IAAI,EAAEvB,MAAM;IAAEyB,IAAI,EAAE,CAACvB,uBAAuB;EAAG,CAAC;AAAE,CAAC,EACrF;EAAEqB,IAAI,EAAEtB;AAAa,CAAC,CACzB,EAAAG,0BAAA,CACJ;AACDD,yBAAyB,GAAGN,UAAU,CAAC,CACnCE,SAAS,CAAC;EACN2B,QAAQ,EAAE,yBAAyB;EACnCC,QAAQ,EAAE7B,oBAAoB;EAC9B8B,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEzB,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}