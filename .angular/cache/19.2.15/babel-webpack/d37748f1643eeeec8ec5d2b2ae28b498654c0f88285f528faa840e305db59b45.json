{"ast": null, "code": "var _InputTextComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-text.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-text.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nfunction parseAutosize(value) {\n  if (value) {\n    return typeof value === 'boolean' ? {} : value;\n  }\n  return undefined;\n}\nlet InputTextComponent = (_InputTextComponent = class InputTextComponent {\n  constructor() {\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n    this.type = 'text';\n  }\n  set componentOptions(value) {\n    var _value$validation;\n    this.type = (value === null || value === void 0 ? void 0 : value.type) || 'text';\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.clearable = value === null || value === void 0 ? void 0 : value.clearable;\n    this.autosize = parseAutosize(value === null || value === void 0 ? void 0 : value.autosize);\n    this.required = value === null || value === void 0 ? void 0 : value.required;\n    this.errorMessages = value === null || value === void 0 || (_value$validation = value.validation) === null || _value$validation === void 0 ? void 0 : _value$validation.messages;\n  }\n  clear(event) {\n    event.stopPropagation();\n    if (this.control) {\n      this.control.setValue('');\n    }\n  }\n}, _InputTextComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }]\n}, _InputTextComponent);\nInputTextComponent = __decorate([Component({\n  selector: 'lib-input-text',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputTextComponent);\nexport { InputTextComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "parseAutosize", "value", "undefined", "InputTextComponent", "_InputTextComponent", "constructor", "id", "readonly", "submitted", "type", "componentOptions", "_value$validation", "title", "clearable", "autosize", "required", "errorMessages", "validation", "messages", "clear", "event", "stopPropagation", "control", "setValue", "propDecorators", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-text/input-text.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-text.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-text.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nfunction parseAutosize(value) {\n    if (value) {\n        return typeof value === 'boolean' ? {} : value;\n    }\n    return undefined;\n}\nlet InputTextComponent = class InputTextComponent {\n    constructor() {\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n        this.type = 'text';\n    }\n    set componentOptions(value) {\n        this.type = value?.type || 'text';\n        this.title = value?.title;\n        this.clearable = value?.clearable;\n        this.autosize = parseAutosize(value?.autosize);\n        this.required = value?.required;\n        this.errorMessages = value?.validation?.messages;\n    }\n    clear(event) {\n        event.stopPropagation();\n        if (this.control) {\n            this.control.setValue('');\n        }\n    }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }]\n    }; }\n};\nInputTextComponent = __decorate([\n    Component({\n        selector: 'lib-input-text',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputTextComponent);\nexport { InputTextComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,wCAAwC;AACzE,OAAOC,oBAAoB,MAAM,wCAAwC;AACzE,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC1B,IAAIA,KAAK,EAAE;IACP,OAAO,OAAOA,KAAK,KAAK,SAAS,GAAG,CAAC,CAAC,GAAGA,KAAK;EAClD;EACA,OAAOC,SAAS;AACpB;AACA,IAAIC,kBAAkB,IAAAC,mBAAA,GAAG,MAAMD,kBAAkB,CAAC;EAC9CE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,IAAI,GAAG,MAAM;EACtB;EACA,IAAIC,gBAAgBA,CAACT,KAAK,EAAE;IAAA,IAAAU,iBAAA;IACxB,IAAI,CAACF,IAAI,GAAG,CAAAR,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,IAAI,KAAI,MAAM;IACjC,IAAI,CAACG,KAAK,GAAGX,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,KAAK;IACzB,IAAI,CAACC,SAAS,GAAGZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,SAAS;IACjC,IAAI,CAACC,QAAQ,GAAGd,aAAa,CAACC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEa,QAAQ,CAAC;IAC9C,IAAI,CAACC,QAAQ,GAAGd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,QAAQ;IAC/B,IAAI,CAACC,aAAa,GAAGf,KAAK,aAALA,KAAK,gBAAAU,iBAAA,GAALV,KAAK,CAAEgB,UAAU,cAAAN,iBAAA,uBAAjBA,iBAAA,CAAmBO,QAAQ;EACpD;EACAC,KAAKA,CAACC,KAAK,EAAE;IACTA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,QAAQ,CAAC,EAAE,CAAC;IAC7B;EACJ;AAQJ,CAAC,EAPYnB,mBAAA,CAAKoB,cAAc,GAAG;EAC3BF,OAAO,EAAE,CAAC;IAAEb,IAAI,EAAEV;EAAM,CAAC,CAAC;EAC1BO,EAAE,EAAE,CAAC;IAAEG,IAAI,EAAEV;EAAM,CAAC,CAAC;EACrBQ,QAAQ,EAAE,CAAC;IAAEE,IAAI,EAAEV;EAAM,CAAC,CAAC;EAC3BS,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAEV;EAAM,CAAC,CAAC;EAC5BW,gBAAgB,EAAE,CAAC;IAAED,IAAI,EAAEV;EAAM,CAAC;AACtC,CAAC,EAAAK,mBAAA,CACJ;AACDD,kBAAkB,GAAGR,UAAU,CAAC,CAC5BG,SAAS,CAAC;EACN2B,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE9B,oBAAoB;EAC9B+B,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC/B,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEM,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}