{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { createHttpFactory, HttpMethod } from '@ngneat/spectator';\nimport { SwHubConfigService } from './sw-hub-config.service';\nimport { PERMISSIONS_NAMES } from '../sw-hub-auth/permissions';\nconst apiConfig = '/api/config';\ndescribe('SwHubConfigService', () => {\n  const createHttp = createHttpFactory(SwHubConfigService);\n  const testResponse = {\n    bridge: 'test_bridge',\n    logo: {\n      main: 'test_main_logo',\n      symbols: 'test_symbols_logo'\n    }\n  };\n  let spectator;\n  let response;\n  afterEach(() => spectator.controller.verify());\n  beforeEach(() => spectator = createHttp());\n  it('should be created', () => {\n    expect(spectator.service).toBeTruthy();\n  });\n  it('fetch should return empty object when config is empty', /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(function* (done) {\n      response = {\n        bridge: undefined,\n        loginUrl: undefined,\n        logoutUrl: undefined\n      };\n      spectator.service.fetch().then(() => done());\n      const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n      request.flush({});\n      expect(spectator.service.bridge).toBeUndefined();\n      expect(spectator.service.loginUrl).toBeUndefined();\n      expect(spectator.service.logoutUrl).toBeUndefined();\n    });\n    return function (_x) {\n      return _ref.apply(this, arguments);\n    };\n  }());\n  it('fetch should return object when config is not empty', /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(function* (done) {\n      response = {\n        bridge: 'test_bridge',\n        loginUrl: 'test_loginUrl',\n        logoutUrl: 'test_logoutUrl'\n      };\n      spectator.service.fetch().then(() => done());\n      const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n      request.flush(response);\n      expect(spectator.service.bridge).toEqual('test_bridge');\n      expect(spectator.service.loginUrl).toEqual('test_loginUrl');\n      expect(spectator.service.logoutUrl).toEqual('test_logoutUrl');\n    });\n    return function (_x2) {\n      return _ref2.apply(this, arguments);\n    };\n  }());\n  it('fetch should return casino hub', /*#__PURE__*/function () {\n    var _ref3 = _asyncToGenerator(function* (done) {\n      const responseHubs = {\n        casino: {\n          url: 'test_url',\n          name: 'HUBS.casino',\n          cssClass: 'hub-casino',\n          permission: PERMISSIONS_NAMES.HUB_CASINO\n        }\n      };\n      response = {\n        bridge: 'test_bridge',\n        hubs: {\n          casino: 'test_url',\n          analytics: undefined,\n          engagement: undefined,\n          studio: undefined\n        },\n        loginUrl: 'test_loginUrl',\n        logoutUrl: 'test_logoutUrl'\n      };\n      spectator.service.fetch().then(() => done());\n      const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n      request.flush(response);\n      expect(spectator.service.hubs).toEqual(responseHubs);\n    });\n    return function (_x3) {\n      return _ref3.apply(this, arguments);\n    };\n  }());\n  it('fetch should return all hubs', /*#__PURE__*/function () {\n    var _ref4 = _asyncToGenerator(function* (done) {\n      const responseHubs = {\n        casino: {\n          url: 'test_casino_url',\n          name: 'HUBS.casino',\n          cssClass: 'hub-casino',\n          permission: PERMISSIONS_NAMES.HUB_CASINO\n        },\n        engagement: {\n          url: 'test_engagement_url',\n          name: 'HUBS.engagement',\n          cssClass: 'hub-engagement',\n          permission: PERMISSIONS_NAMES.HUB_ENGAGEMENT\n        },\n        analytics: {\n          url: 'test_analytics_url',\n          name: 'HUBS.analytics',\n          cssClass: 'hub-analytics',\n          permission: PERMISSIONS_NAMES.HUB_ANALYTICS\n        },\n        studio: {\n          url: 'test_studio_url',\n          name: 'HUBS.studio',\n          cssClass: 'hub-studio',\n          permission: PERMISSIONS_NAMES.HUB_STUDIO\n        }\n      };\n      response = {\n        bridge: 'test_bridge',\n        hubs: {\n          casino: 'test_casino_url',\n          analytics: 'test_analytics_url',\n          engagement: 'test_engagement_url',\n          studio: 'test_studio_url'\n        },\n        loginUrl: 'test_loginUrl',\n        logoutUrl: 'test_logoutUrl'\n      };\n      spectator.service.fetch().then(() => done());\n      const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n      request.flush(response);\n      expect(spectator.service.hubs).toEqual(responseHubs);\n    });\n    return function (_x4) {\n      return _ref4.apply(this, arguments);\n    };\n  }());\n  it('fetch should return undefined if catch error', /*#__PURE__*/function () {\n    var _ref5 = _asyncToGenerator(function* (done) {\n      const errResponse = {\n        status: 400,\n        statusText: 'Bad Request'\n      };\n      spectator.service.fetch().then(() => done());\n      const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n      request.flush(errResponse);\n      expect(spectator.service.bridge).toBeUndefined();\n      expect(spectator.service.loginUrl).toBeUndefined();\n      expect(spectator.service.logoutUrl).toBeUndefined();\n    });\n    return function (_x5) {\n      return _ref5.apply(this, arguments);\n    };\n  }());\n  it('fetch should return empty logo when config is empty', /*#__PURE__*/function () {\n    var _ref6 = _asyncToGenerator(function* (done) {\n      spectator.service.fetch().then(() => done());\n      const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n      request.flush({});\n      expect(spectator.service.logo).toBeUndefined();\n    });\n    return function (_x6) {\n      return _ref6.apply(this, arguments);\n    };\n  }());\n  it('fetch should return undefined for non-matching domain', /*#__PURE__*/function () {\n    var _ref7 = _asyncToGenerator(function* (done) {\n      spectator.service.fetch().then(() => done());\n      const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n      request.flush(testResponse);\n      expect(spectator.service.logo).toEqual(testResponse.logo);\n    });\n    return function (_x7) {\n      return _ref7.apply(this, arguments);\n    };\n  }());\n});", "map": {"version": 3, "names": ["createHttpFactory", "HttpMethod", "SwHubConfigService", "PERMISSIONS_NAMES", "apiConfig", "describe", "createHttp", "testResponse", "bridge", "logo", "main", "symbols", "spectator", "response", "after<PERSON>ach", "controller", "verify", "beforeEach", "it", "expect", "service", "toBeTruthy", "_ref", "_asyncToGenerator", "done", "undefined", "loginUrl", "logoutUrl", "fetch", "then", "request", "expectOne", "GET", "flush", "toBeUndefined", "_x", "apply", "arguments", "_ref2", "toEqual", "_x2", "_ref3", "responseHubs", "casino", "url", "name", "cssClass", "permission", "HUB_CASINO", "hubs", "analytics", "engagement", "studio", "_x3", "_ref4", "HUB_ENGAGEMENT", "HUB_ANALYTICS", "HUB_STUDIO", "_x4", "_ref5", "errResponse", "status", "statusText", "_x5", "_ref6", "_x6", "_ref7", "_x7"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-config/sw-hub-config.service.spec.ts"], "sourcesContent": ["import { createHttpFactory, HttpMethod, SpectatorHttp } from '@ngneat/spectator';\nimport { HubConfigHubs, HubUrls, SwHubConfigService } from './sw-hub-config.service';\nimport { PERMISSIONS_NAMES } from '../sw-hub-auth/permissions';\n\nconst apiConfig = '/api/config';\n\ndescribe('SwHubConfigService', () => {\n  const createHttp = createHttpFactory(SwHubConfigService);\n  const testResponse = {\n    bridge: 'test_bridge',\n    logo: {\n      main: 'test_main_logo',\n      symbols: 'test_symbols_logo'\n    }\n  };\n\n  let spectator: SpectatorHttp<SwHubConfigService>;\n  let response: HubUrls;\n\n  afterEach(() => spectator.controller.verify());\n  beforeEach(() => spectator = createHttp());\n\n  it('should be created', () => {\n    expect(spectator.service).toBeTruthy();\n  });\n\n  it('fetch should return empty object when config is empty', async ( done ) => {\n    response = {\n      bridge: undefined,\n      loginUrl: undefined,\n      logoutUrl: undefined\n    };\n    spectator.service.fetch().then(() => done());\n\n    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n    request.flush({});\n\n    expect(spectator.service.bridge).toBeUndefined();\n    expect(spectator.service.loginUrl).toBeUndefined();\n    expect(spectator.service.logoutUrl).toBeUndefined();\n  });\n\n  it('fetch should return object when config is not empty', async ( done ) => {\n    response = {\n      bridge: 'test_bridge',\n      loginUrl: 'test_loginUrl',\n      logoutUrl: 'test_logoutUrl'\n    };\n\n    spectator.service.fetch().then(() => done());\n\n    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n    request.flush(response);\n\n    expect(spectator.service.bridge).toEqual('test_bridge');\n    expect(spectator.service.loginUrl).toEqual('test_loginUrl');\n    expect(spectator.service.logoutUrl).toEqual('test_logoutUrl');\n  });\n\n  it('fetch should return casino hub', async ( done ) => {\n    const responseHubs: HubConfigHubs = {\n      casino: {\n        url: 'test_url',\n        name: 'HUBS.casino',\n        cssClass: 'hub-casino',\n        permission: PERMISSIONS_NAMES.HUB_CASINO\n      }\n    };\n    response = {\n      bridge: 'test_bridge',\n      hubs: {\n        casino: 'test_url',\n        analytics: undefined,\n        engagement: undefined,\n        studio: undefined\n      },\n      loginUrl: 'test_loginUrl',\n      logoutUrl: 'test_logoutUrl'\n    };\n\n    spectator.service.fetch().then(() => done());\n\n    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n    request.flush(response);\n\n    expect(spectator.service.hubs).toEqual(responseHubs);\n  });\n\n  it('fetch should return all hubs', async ( done ) => {\n    const responseHubs: HubConfigHubs = {\n      casino: {\n        url: 'test_casino_url',\n        name: 'HUBS.casino',\n        cssClass: 'hub-casino',\n        permission: PERMISSIONS_NAMES.HUB_CASINO\n      },\n      engagement: {\n        url: 'test_engagement_url',\n        name: 'HUBS.engagement',\n        cssClass: 'hub-engagement',\n        permission: PERMISSIONS_NAMES.HUB_ENGAGEMENT\n      },\n      analytics: {\n        url: 'test_analytics_url',\n        name: 'HUBS.analytics',\n        cssClass: 'hub-analytics',\n        permission: PERMISSIONS_NAMES.HUB_ANALYTICS\n      },\n      studio: {\n        url: 'test_studio_url',\n        name: 'HUBS.studio',\n        cssClass: 'hub-studio',\n        permission: PERMISSIONS_NAMES.HUB_STUDIO\n      },\n    };\n    response = {\n      bridge: 'test_bridge',\n      hubs: {\n        casino: 'test_casino_url',\n        analytics: 'test_analytics_url',\n        engagement: 'test_engagement_url',\n        studio: 'test_studio_url'\n      },\n      loginUrl: 'test_loginUrl',\n      logoutUrl: 'test_logoutUrl'\n    };\n\n    spectator.service.fetch().then(() => done());\n\n    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n    request.flush(response);\n\n    expect(spectator.service.hubs).toEqual(responseHubs);\n  });\n\n  it('fetch should return undefined if catch error', async ( done ) => {\n    const errResponse = { status: 400, statusText: 'Bad Request' };\n\n    spectator.service.fetch().then(() => done());\n\n    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n    request.flush(errResponse);\n\n    expect(spectator.service.bridge).toBeUndefined();\n    expect(spectator.service.loginUrl).toBeUndefined();\n    expect(spectator.service.logoutUrl).toBeUndefined();\n  });\n\n  it('fetch should return empty logo when config is empty', async ( done ) => {\n    spectator.service.fetch().then(() => done());\n\n    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n    request.flush({});\n\n    expect(spectator.service.logo).toBeUndefined();\n  });\n\n  it('fetch should return undefined for non-matching domain', async ( done ) => {\n    spectator.service.fetch().then(() => done());\n\n    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);\n    request.flush(testResponse);\n\n    expect(spectator.service.logo).toEqual(testResponse.logo);\n  });\n});\n"], "mappings": ";AAAA,SAASA,iBAAiB,EAAEC,UAAU,QAAuB,mBAAmB;AAChF,SAAiCC,kBAAkB,QAAQ,yBAAyB;AACpF,SAASC,iBAAiB,QAAQ,4BAA4B;AAE9D,MAAMC,SAAS,GAAG,aAAa;AAE/BC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClC,MAAMC,UAAU,GAAGN,iBAAiB,CAACE,kBAAkB,CAAC;EACxD,MAAMK,YAAY,GAAG;IACnBC,MAAM,EAAE,aAAa;IACrBC,IAAI,EAAE;MACJC,IAAI,EAAE,gBAAgB;MACtBC,OAAO,EAAE;;GAEZ;EAED,IAAIC,SAA4C;EAChD,IAAIC,QAAiB;EAErBC,SAAS,CAAC,MAAMF,SAAS,CAACG,UAAU,CAACC,MAAM,EAAE,CAAC;EAC9CC,UAAU,CAAC,MAAML,SAAS,GAAGN,UAAU,EAAE,CAAC;EAE1CY,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACP,SAAS,CAACQ,OAAO,CAAC,CAACC,UAAU,EAAE;EACxC,CAAC,CAAC;EAEFH,EAAE,CAAC,uDAAuD;IAAA,IAAAI,IAAA,GAAAC,iBAAA,CAAE,WAAQC,IAAI,EAAK;MAC3EX,QAAQ,GAAG;QACTL,MAAM,EAAEiB,SAAS;QACjBC,QAAQ,EAAED,SAAS;QACnBE,SAAS,EAAEF;OACZ;MACDb,SAAS,CAACQ,OAAO,CAACQ,KAAK,EAAE,CAACC,IAAI,CAAC,MAAML,IAAI,EAAE,CAAC;MAE5C,MAAMM,OAAO,GAAGlB,SAAS,CAACG,UAAU,CAACgB,SAAS,CAAC3B,SAAS,EAAEH,UAAU,CAAC+B,GAAG,CAAC;MACzEF,OAAO,CAACG,KAAK,CAAC,EAAE,CAAC;MAEjBd,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACZ,MAAM,CAAC,CAAC0B,aAAa,EAAE;MAChDf,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACM,QAAQ,CAAC,CAACQ,aAAa,EAAE;MAClDf,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACO,SAAS,CAAC,CAACO,aAAa,EAAE;IACrD,CAAC;IAAA,iBAAAC,EAAA;MAAA,OAAAb,IAAA,CAAAc,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC;EAEFnB,EAAE,CAAC,qDAAqD;IAAA,IAAAoB,KAAA,GAAAf,iBAAA,CAAE,WAAQC,IAAI,EAAK;MACzEX,QAAQ,GAAG;QACTL,MAAM,EAAE,aAAa;QACrBkB,QAAQ,EAAE,eAAe;QACzBC,SAAS,EAAE;OACZ;MAEDf,SAAS,CAACQ,OAAO,CAACQ,KAAK,EAAE,CAACC,IAAI,CAAC,MAAML,IAAI,EAAE,CAAC;MAE5C,MAAMM,OAAO,GAAGlB,SAAS,CAACG,UAAU,CAACgB,SAAS,CAAC3B,SAAS,EAAEH,UAAU,CAAC+B,GAAG,CAAC;MACzEF,OAAO,CAACG,KAAK,CAACpB,QAAQ,CAAC;MAEvBM,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACZ,MAAM,CAAC,CAAC+B,OAAO,CAAC,aAAa,CAAC;MACvDpB,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACM,QAAQ,CAAC,CAACa,OAAO,CAAC,eAAe,CAAC;MAC3DpB,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACO,SAAS,CAAC,CAACY,OAAO,CAAC,gBAAgB,CAAC;IAC/D,CAAC;IAAA,iBAAAC,GAAA;MAAA,OAAAF,KAAA,CAAAF,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC;EAEFnB,EAAE,CAAC,gCAAgC;IAAA,IAAAuB,KAAA,GAAAlB,iBAAA,CAAE,WAAQC,IAAI,EAAK;MACpD,MAAMkB,YAAY,GAAkB;QAClCC,MAAM,EAAE;UACNC,GAAG,EAAE,UAAU;UACfC,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,YAAY;UACtBC,UAAU,EAAE5C,iBAAiB,CAAC6C;;OAEjC;MACDnC,QAAQ,GAAG;QACTL,MAAM,EAAE,aAAa;QACrByC,IAAI,EAAE;UACJN,MAAM,EAAE,UAAU;UAClBO,SAAS,EAAEzB,SAAS;UACpB0B,UAAU,EAAE1B,SAAS;UACrB2B,MAAM,EAAE3B;SACT;QACDC,QAAQ,EAAE,eAAe;QACzBC,SAAS,EAAE;OACZ;MAEDf,SAAS,CAACQ,OAAO,CAACQ,KAAK,EAAE,CAACC,IAAI,CAAC,MAAML,IAAI,EAAE,CAAC;MAE5C,MAAMM,OAAO,GAAGlB,SAAS,CAACG,UAAU,CAACgB,SAAS,CAAC3B,SAAS,EAAEH,UAAU,CAAC+B,GAAG,CAAC;MACzEF,OAAO,CAACG,KAAK,CAACpB,QAAQ,CAAC;MAEvBM,MAAM,CAACP,SAAS,CAACQ,OAAO,CAAC6B,IAAI,CAAC,CAACV,OAAO,CAACG,YAAY,CAAC;IACtD,CAAC;IAAA,iBAAAW,GAAA;MAAA,OAAAZ,KAAA,CAAAL,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC;EAEFnB,EAAE,CAAC,8BAA8B;IAAA,IAAAoC,KAAA,GAAA/B,iBAAA,CAAE,WAAQC,IAAI,EAAK;MAClD,MAAMkB,YAAY,GAAkB;QAClCC,MAAM,EAAE;UACNC,GAAG,EAAE,iBAAiB;UACtBC,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,YAAY;UACtBC,UAAU,EAAE5C,iBAAiB,CAAC6C;SAC/B;QACDG,UAAU,EAAE;UACVP,GAAG,EAAE,qBAAqB;UAC1BC,IAAI,EAAE,iBAAiB;UACvBC,QAAQ,EAAE,gBAAgB;UAC1BC,UAAU,EAAE5C,iBAAiB,CAACoD;SAC/B;QACDL,SAAS,EAAE;UACTN,GAAG,EAAE,oBAAoB;UACzBC,IAAI,EAAE,gBAAgB;UACtBC,QAAQ,EAAE,eAAe;UACzBC,UAAU,EAAE5C,iBAAiB,CAACqD;SAC/B;QACDJ,MAAM,EAAE;UACNR,GAAG,EAAE,iBAAiB;UACtBC,IAAI,EAAE,aAAa;UACnBC,QAAQ,EAAE,YAAY;UACtBC,UAAU,EAAE5C,iBAAiB,CAACsD;;OAEjC;MACD5C,QAAQ,GAAG;QACTL,MAAM,EAAE,aAAa;QACrByC,IAAI,EAAE;UACJN,MAAM,EAAE,iBAAiB;UACzBO,SAAS,EAAE,oBAAoB;UAC/BC,UAAU,EAAE,qBAAqB;UACjCC,MAAM,EAAE;SACT;QACD1B,QAAQ,EAAE,eAAe;QACzBC,SAAS,EAAE;OACZ;MAEDf,SAAS,CAACQ,OAAO,CAACQ,KAAK,EAAE,CAACC,IAAI,CAAC,MAAML,IAAI,EAAE,CAAC;MAE5C,MAAMM,OAAO,GAAGlB,SAAS,CAACG,UAAU,CAACgB,SAAS,CAAC3B,SAAS,EAAEH,UAAU,CAAC+B,GAAG,CAAC;MACzEF,OAAO,CAACG,KAAK,CAACpB,QAAQ,CAAC;MAEvBM,MAAM,CAACP,SAAS,CAACQ,OAAO,CAAC6B,IAAI,CAAC,CAACV,OAAO,CAACG,YAAY,CAAC;IACtD,CAAC;IAAA,iBAAAgB,GAAA;MAAA,OAAAJ,KAAA,CAAAlB,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC;EAEFnB,EAAE,CAAC,8CAA8C;IAAA,IAAAyC,KAAA,GAAApC,iBAAA,CAAE,WAAQC,IAAI,EAAK;MAClE,MAAMoC,WAAW,GAAG;QAAEC,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAa,CAAE;MAE9DlD,SAAS,CAACQ,OAAO,CAACQ,KAAK,EAAE,CAACC,IAAI,CAAC,MAAML,IAAI,EAAE,CAAC;MAE5C,MAAMM,OAAO,GAAGlB,SAAS,CAACG,UAAU,CAACgB,SAAS,CAAC3B,SAAS,EAAEH,UAAU,CAAC+B,GAAG,CAAC;MACzEF,OAAO,CAACG,KAAK,CAAC2B,WAAW,CAAC;MAE1BzC,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACZ,MAAM,CAAC,CAAC0B,aAAa,EAAE;MAChDf,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACM,QAAQ,CAAC,CAACQ,aAAa,EAAE;MAClDf,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACO,SAAS,CAAC,CAACO,aAAa,EAAE;IACrD,CAAC;IAAA,iBAAA6B,GAAA;MAAA,OAAAJ,KAAA,CAAAvB,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC;EAEFnB,EAAE,CAAC,qDAAqD;IAAA,IAAA8C,KAAA,GAAAzC,iBAAA,CAAE,WAAQC,IAAI,EAAK;MACzEZ,SAAS,CAACQ,OAAO,CAACQ,KAAK,EAAE,CAACC,IAAI,CAAC,MAAML,IAAI,EAAE,CAAC;MAE5C,MAAMM,OAAO,GAAGlB,SAAS,CAACG,UAAU,CAACgB,SAAS,CAAC3B,SAAS,EAAEH,UAAU,CAAC+B,GAAG,CAAC;MACzEF,OAAO,CAACG,KAAK,CAAC,EAAE,CAAC;MAEjBd,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACX,IAAI,CAAC,CAACyB,aAAa,EAAE;IAChD,CAAC;IAAA,iBAAA+B,GAAA;MAAA,OAAAD,KAAA,CAAA5B,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC;EAEFnB,EAAE,CAAC,uDAAuD;IAAA,IAAAgD,KAAA,GAAA3C,iBAAA,CAAE,WAAQC,IAAI,EAAK;MAC3EZ,SAAS,CAACQ,OAAO,CAACQ,KAAK,EAAE,CAACC,IAAI,CAAC,MAAML,IAAI,EAAE,CAAC;MAE5C,MAAMM,OAAO,GAAGlB,SAAS,CAACG,UAAU,CAACgB,SAAS,CAAC3B,SAAS,EAAEH,UAAU,CAAC+B,GAAG,CAAC;MACzEF,OAAO,CAACG,KAAK,CAAC1B,YAAY,CAAC;MAE3BY,MAAM,CAACP,SAAS,CAACQ,OAAO,CAACX,IAAI,CAAC,CAAC8B,OAAO,CAAChC,YAAY,CAACE,IAAI,CAAC;IAC3D,CAAC;IAAA,iBAAA0D,GAAA;MAAA,OAAAD,KAAA,CAAA9B,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}