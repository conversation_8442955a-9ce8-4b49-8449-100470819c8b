{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _ShowOnDirtyErrorStateMatcher, _ErrorStateMatcher;\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.dirty || form && form.submitted));\n  }\n}\n_ShowOnDirtyErrorStateMatcher = ShowOnDirtyErrorStateMatcher;\n_defineProperty(ShowOnDirtyErrorStateMatcher, \"\\u0275fac\", function _ShowOnDirtyErrorStateMatcher_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ShowOnDirtyErrorStateMatcher)();\n});\n_defineProperty(ShowOnDirtyErrorStateMatcher, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _ShowOnDirtyErrorStateMatcher,\n  factory: _ShowOnDirtyErrorStateMatcher.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowOnDirtyErrorStateMatcher, [{\n    type: Injectable\n  }], null, null);\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.touched || form && form.submitted));\n  }\n}\n_ErrorStateMatcher = ErrorStateMatcher;\n_defineProperty(ErrorStateMatcher, \"\\u0275fac\", function _ErrorStateMatcher_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ErrorStateMatcher)();\n});\n_defineProperty(ErrorStateMatcher, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _ErrorStateMatcher,\n  factory: _ErrorStateMatcher.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ErrorStateMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { ErrorStateMatcher as E, ShowOnDirtyErrorStateMatcher as S };", "map": {"version": 3, "names": ["i0", "Injectable", "ShowOnDirtyErrorStateMatcher", "isErrorState", "control", "form", "invalid", "dirty", "submitted", "_ShowOnDirtyErrorStateMatcher", "_defineProperty", "_ShowOnDirtyErrorStateMatcher_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "ngDevMode", "ɵsetClassMetadata", "type", "ErrorStateMatcher", "touched", "_ErrorStateMatcher", "_ErrorStateMatcher_Factory", "providedIn", "args", "E", "S"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/error-options-Dm2JJUbF.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.dirty || (form && form.submitted)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, decorators: [{\n            type: Injectable\n        }] });\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.touched || (form && form.submitted)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ErrorStateMatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ErrorStateMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nexport { ErrorStateMatcher as E, ShowOnDirtyErrorStateMatcher as S };\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;;AAE1C;AACA,MAAMC,4BAA4B,CAAC;EAC/BC,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAE;IACxB,OAAO,CAAC,EAAED,OAAO,IAAIA,OAAO,CAACE,OAAO,KAAKF,OAAO,CAACG,KAAK,IAAKF,IAAI,IAAIA,IAAI,CAACG,SAAU,CAAC,CAAC;EACxF;AAGJ;AAACC,6BAAA,GANKP,4BAA4B;AAAAQ,eAAA,CAA5BR,4BAA4B,wBAAAS,sCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAIqEV,6BAA4B;AAAA;AAAAQ,eAAA,CAJ7HR,4BAA4B,+BAO+CF,EAAE,CAAAa,kBAAA;EAAAC,KAAA,EAFwBZ,6BAA4B;EAAAa,OAAA,EAA5Bb,6BAA4B,CAAAc;AAAA;AAEvI;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFjB,EAAE,CAAAkB,iBAAA,CAAQhB,4BAA4B,EAAc,CAAC;IAC1HiB,IAAI,EAAElB;EACV,CAAC,CAAC;AAAA;AACV;AACA,MAAMmB,iBAAiB,CAAC;EACpBjB,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAE;IACxB,OAAO,CAAC,EAAED,OAAO,IAAIA,OAAO,CAACE,OAAO,KAAKF,OAAO,CAACiB,OAAO,IAAKhB,IAAI,IAAIA,IAAI,CAACG,SAAU,CAAC,CAAC;EAC1F;AAGJ;AAACc,kBAAA,GANKF,iBAAiB;AAAAV,eAAA,CAAjBU,iBAAiB,wBAAAG,2BAAAX,iBAAA;EAAA,YAAAA,iBAAA,IAIgFQ,kBAAiB;AAAA;AAAAV,eAAA,CAJlHU,iBAAiB,+BAJ0DpB,EAAE,CAAAa,kBAAA;EAAAC,KAAA,EASwBM,kBAAiB;EAAAL,OAAA,EAAjBK,kBAAiB,CAAAJ,IAAA;EAAAQ,UAAA,EAAc;AAAM;AAEhJ;EAAA,QAAAP,SAAA,oBAAAA,SAAA,KAXiFjB,EAAE,CAAAkB,iBAAA,CAWQE,iBAAiB,EAAc,CAAC;IAC/GD,IAAI,EAAElB,UAAU;IAChBwB,IAAI,EAAE,CAAC;MAAED,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,SAASJ,iBAAiB,IAAIM,CAAC,EAAExB,4BAA4B,IAAIyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}