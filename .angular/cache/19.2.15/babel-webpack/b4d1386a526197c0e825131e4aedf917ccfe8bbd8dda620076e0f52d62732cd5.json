{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatPaginatorIntl, _MatPaginator, _MatPaginatorModule;\nfunction _MatPaginator_Conditional_2_Conditional_3_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pageSizeOption_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", pageSizeOption_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", pageSizeOption_r3, \" \");\n  }\n}\nfunction _MatPaginator_Conditional_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 14)(1, \"mat-select\", 16, 0);\n    i0.ɵɵlistener(\"selectionChange\", function _MatPaginator_Conditional_2_Conditional_3_Template_mat_select_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1._changePageSize($event.value));\n    });\n    i0.ɵɵrepeaterCreate(3, _MatPaginator_Conditional_2_Conditional_3_For_4_Template, 2, 2, \"mat-option\", 17, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function _MatPaginator_Conditional_2_Conditional_3_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const selectRef_r4 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(selectRef_r4.open());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r1._formFieldAppearance)(\"color\", ctx_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.pageSize)(\"disabled\", ctx_r1.disabled)(\"aria-labelledby\", ctx_r1._pageSizeLabelId)(\"panelClass\", ctx_r1.selectConfig.panelClass || \"\")(\"disableOptionCentering\", ctx_r1.selectConfig.disableOptionCentering);\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r1._displayedPageSizeOptions);\n  }\n}\nfunction _MatPaginator_Conditional_2_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.pageSize);\n  }\n}\nfunction _MatPaginator_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, _MatPaginator_Conditional_2_Conditional_3_Template, 6, 7, \"mat-form-field\", 14)(4, _MatPaginator_Conditional_2_Conditional_4_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"id\", ctx_r1._pageSizeLabelId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1._intl.itemsPerPageLabel, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1._displayedPageSizeOptions.length > 1 ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1._displayedPageSizeOptions.length <= 1 ? 4 : -1);\n  }\n}\nfunction _MatPaginator_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function _MatPaginator_Conditional_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._buttonClicked(0, ctx_r1._previousButtonsDisabled()));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.firstPageLabel)(\"matTooltipDisabled\", ctx_r1._previousButtonsDisabled())(\"disabled\", ctx_r1._previousButtonsDisabled())(\"tabindex\", ctx_r1._previousButtonsDisabled() ? -1 : null);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.firstPageLabel);\n  }\n}\nfunction _MatPaginator_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function _MatPaginator_Conditional_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._buttonClicked(ctx_r1.getNumberOfPages() - 1, ctx_r1._nextButtonsDisabled()));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.lastPageLabel)(\"matTooltipDisabled\", ctx_r1._nextButtonsDisabled())(\"disabled\", ctx_r1._nextButtonsDisabled())(\"tabindex\", ctx_r1._nextButtonsDisabled() ? -1 : null);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.lastPageLabel);\n  }\n}\nimport * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, inject, ChangeDetectorRef, numberAttribute, EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { j as MatFormField } from './form-field-DqPi4knt.mjs';\nimport { g as MatSelect, M as MatSelectModule } from './module-Cbt8Fcmv.mjs';\nimport { e as MatTooltip, h as MatTooltipModule } from './module-C9K6ZqpI.mjs';\nimport { M as MatOption } from './option-ChV6uQgD.mjs';\nimport { M as MatIconButton } from './icon-button-D1J0zeqv.mjs';\nimport { MatButtonModule } from './button.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/platform';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport '@angular/cdk/collections';\nimport '@angular/cdk/keycodes';\nimport '@angular/forms';\nimport './error-options-Dm2JJUbF.mjs';\nimport './error-state-Dtb1IHM-.mjs';\nimport './index-DOxJc1m4.mjs';\nimport './index-SYVYjXwK.mjs';\nimport './common-module-WayjW0Pb.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-module-CAX2sutq.mjs';\nimport './pseudo-checkbox-CJ7seqQH.mjs';\nimport './module-BXZhw7pQ.mjs';\nimport '@angular/cdk/observers';\nimport '@angular/cdk/portal';\nimport './structural-styles-BQUT6wsL.mjs';\nimport './ripple-loader-Ce3DAhPW.mjs';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nclass MatPaginatorIntl {\n  constructor() {\n    /**\n     * Stream to emit from when labels are changed. Use this to notify components when the labels have\n     * changed after initialization.\n     */\n    _defineProperty(this, \"changes\", new Subject());\n    /** A label for the page size selector. */\n    _defineProperty(this, \"itemsPerPageLabel\", 'Items per page:');\n    /** A label for the button that increments the current page. */\n    _defineProperty(this, \"nextPageLabel\", 'Next page');\n    /** A label for the button that decrements the current page. */\n    _defineProperty(this, \"previousPageLabel\", 'Previous page');\n    /** A label for the button that moves to the first page. */\n    _defineProperty(this, \"firstPageLabel\", 'First page');\n    /** A label for the button that moves to the last page. */\n    _defineProperty(this, \"lastPageLabel\", 'Last page');\n    /** A label for the range of items within the current page and the length of the whole list. */\n    _defineProperty(this, \"getRangeLabel\", (page, pageSize, length) => {\n      if (length == 0 || pageSize == 0) {\n        return `0 of ${length}`;\n      }\n      length = Math.max(length, 0);\n      const startIndex = page * pageSize;\n      // If the start index exceeds the list length, do not try and fix the end index to the end.\n      const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n      return `${startIndex + 1} – ${endIndex} of ${length}`;\n    });\n  }\n}\n_MatPaginatorIntl = MatPaginatorIntl;\n_defineProperty(MatPaginatorIntl, \"\\u0275fac\", function _MatPaginatorIntl_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatPaginatorIntl)();\n});\n_defineProperty(MatPaginatorIntl, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _MatPaginatorIntl,\n  factory: _MatPaginatorIntl.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {\n  constructor() {\n    /** The current page index. */\n    _defineProperty(this, \"pageIndex\", void 0);\n    /**\n     * Index of the page that was selected previously.\n     * @breaking-change 8.0.0 To be made into a required property.\n     */\n    _defineProperty(this, \"previousPageIndex\", void 0);\n    /** The current page size. */\n    _defineProperty(this, \"pageSize\", void 0);\n    /** The current total number of items being paged. */\n    _defineProperty(this, \"length\", void 0);\n  }\n}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator {\n  /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n  get pageIndex() {\n    return this._pageIndex;\n  }\n  set pageIndex(value) {\n    this._pageIndex = Math.max(value || 0, 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** The length of the total number of items that are being paginated. Defaulted to 0. */\n  get length() {\n    return this._length;\n  }\n  set length(value) {\n    this._length = value || 0;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Number of items to display on a page. By default set to 50. */\n  get pageSize() {\n    return this._pageSize;\n  }\n  set pageSize(value) {\n    this._pageSize = Math.max(value || 0, 0);\n    this._updateDisplayedPageSizeOptions();\n  }\n  /** The set of provided page size options to display to the user. */\n  get pageSizeOptions() {\n    return this._pageSizeOptions;\n  }\n  set pageSizeOptions(value) {\n    this._pageSizeOptions = (value || []).map(p => numberAttribute(p, 0));\n    this._updateDisplayedPageSizeOptions();\n  }\n  constructor() {\n    _defineProperty(this, \"_intl\", inject(MatPaginatorIntl));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    /** If set, styles the \"page size\" form field with the designated style. */\n    _defineProperty(this, \"_formFieldAppearance\", void 0);\n    /** ID for the DOM node containing the paginator's items per page label. */\n    _defineProperty(this, \"_pageSizeLabelId\", inject(_IdGenerator).getId('mat-paginator-page-size-label-'));\n    _defineProperty(this, \"_intlChanges\", void 0);\n    _defineProperty(this, \"_isInitialized\", false);\n    _defineProperty(this, \"_initializedStream\", new ReplaySubject(1));\n    /**\n     * Theme color of the underlying form controls. This API is supported in M2\n     * themes only,it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/paginator/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    _defineProperty(this, \"color\", void 0);\n    _defineProperty(this, \"_pageIndex\", 0);\n    _defineProperty(this, \"_length\", 0);\n    _defineProperty(this, \"_pageSize\", void 0);\n    _defineProperty(this, \"_pageSizeOptions\", []);\n    /** Whether to hide the page size selection UI from the user. */\n    _defineProperty(this, \"hidePageSize\", false);\n    /** Whether to show the first/last buttons UI to the user. */\n    _defineProperty(this, \"showFirstLastButtons\", false);\n    /** Used to configure the underlying `MatSelect` inside the paginator. */\n    _defineProperty(this, \"selectConfig\", {});\n    /** Whether the paginator is disabled. */\n    _defineProperty(this, \"disabled\", false);\n    /** Event emitted when the paginator changes the page size or page index. */\n    _defineProperty(this, \"page\", new EventEmitter());\n    /** Displayed set of page size options. Will be sorted and include current page size. */\n    _defineProperty(this, \"_displayedPageSizeOptions\", void 0);\n    /** Emits when the paginator is initialized. */\n    _defineProperty(this, \"initialized\", this._initializedStream);\n    const _intl = this._intl;\n    const defaults = inject(MAT_PAGINATOR_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n    if (defaults) {\n      const {\n        pageSize,\n        pageSizeOptions,\n        hidePageSize,\n        showFirstLastButtons\n      } = defaults;\n      if (pageSize != null) {\n        this._pageSize = pageSize;\n      }\n      if (pageSizeOptions != null) {\n        this._pageSizeOptions = pageSizeOptions;\n      }\n      if (hidePageSize != null) {\n        this.hidePageSize = hidePageSize;\n      }\n      if (showFirstLastButtons != null) {\n        this.showFirstLastButtons = showFirstLastButtons;\n      }\n    }\n    this._formFieldAppearance = (defaults === null || defaults === void 0 ? void 0 : defaults.formFieldAppearance) || 'outline';\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._updateDisplayedPageSizeOptions();\n    this._initializedStream.next();\n  }\n  ngOnDestroy() {\n    this._initializedStream.complete();\n    this._intlChanges.unsubscribe();\n  }\n  /** Advances to the next page if it exists. */\n  nextPage() {\n    if (this.hasNextPage()) {\n      this._navigate(this.pageIndex + 1);\n    }\n  }\n  /** Move back to the previous page if it exists. */\n  previousPage() {\n    if (this.hasPreviousPage()) {\n      this._navigate(this.pageIndex - 1);\n    }\n  }\n  /** Move to the first page if not already there. */\n  firstPage() {\n    // hasPreviousPage being false implies at the start\n    if (this.hasPreviousPage()) {\n      this._navigate(0);\n    }\n  }\n  /** Move to the last page if not already there. */\n  lastPage() {\n    // hasNextPage being false implies at the end\n    if (this.hasNextPage()) {\n      this._navigate(this.getNumberOfPages() - 1);\n    }\n  }\n  /** Whether there is a previous page. */\n  hasPreviousPage() {\n    return this.pageIndex >= 1 && this.pageSize != 0;\n  }\n  /** Whether there is a next page. */\n  hasNextPage() {\n    const maxPageIndex = this.getNumberOfPages() - 1;\n    return this.pageIndex < maxPageIndex && this.pageSize != 0;\n  }\n  /** Calculate the number of pages */\n  getNumberOfPages() {\n    if (!this.pageSize) {\n      return 0;\n    }\n    return Math.ceil(this.length / this.pageSize);\n  }\n  /**\n   * Changes the page size so that the first item displayed on the page will still be\n   * displayed using the new page size.\n   *\n   * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n   * switching so that the page size is 5 will set the third page as the current page so\n   * that the 10th item will still be displayed.\n   */\n  _changePageSize(pageSize) {\n    // Current page needs to be updated to reflect the new page size. Navigate to the page\n    // containing the previous page's first item.\n    const startIndex = this.pageIndex * this.pageSize;\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n    this.pageSize = pageSize;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Checks whether the buttons for going forwards should be disabled. */\n  _nextButtonsDisabled() {\n    return this.disabled || !this.hasNextPage();\n  }\n  /** Checks whether the buttons for going backwards should be disabled. */\n  _previousButtonsDisabled() {\n    return this.disabled || !this.hasPreviousPage();\n  }\n  /**\n   * Updates the list of page size options to display to the user. Includes making sure that\n   * the page size is an option and that the list is sorted.\n   */\n  _updateDisplayedPageSizeOptions() {\n    if (!this._isInitialized) {\n      return;\n    }\n    // If no page size is provided, use the first page size option or the default page size.\n    if (!this.pageSize) {\n      this._pageSize = this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n    }\n    this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n    if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n      this._displayedPageSizeOptions.push(this.pageSize);\n    }\n    // Sort the numbers using a number-specific sort function.\n    this._displayedPageSizeOptions.sort((a, b) => a - b);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n  _emitPageEvent(previousPageIndex) {\n    this.page.emit({\n      previousPageIndex,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize,\n      length: this.length\n    });\n  }\n  /** Navigates to a specific page index. */\n  _navigate(index) {\n    const previousIndex = this.pageIndex;\n    if (index !== previousIndex) {\n      this.pageIndex = index;\n      this._emitPageEvent(previousIndex);\n    }\n  }\n  /**\n   * Callback invoked when one of the navigation buttons is called.\n   * @param targetIndex Index to which the paginator should navigate.\n   * @param isDisabled Whether the button is disabled.\n   */\n  _buttonClicked(targetIndex, isDisabled) {\n    // Note that normally disabled buttons won't dispatch the click event, but the paginator ones\n    // do, because we're using `disabledInteractive` to allow them to be focusable. We need to\n    // check here to avoid the navigation.\n    if (!isDisabled) {\n      this._navigate(targetIndex);\n    }\n  }\n}\n_MatPaginator = MatPaginator;\n_defineProperty(MatPaginator, \"\\u0275fac\", function _MatPaginator_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatPaginator)();\n});\n_defineProperty(MatPaginator, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatPaginator,\n  selectors: [[\"mat-paginator\"]],\n  hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-paginator\"],\n  inputs: {\n    color: \"color\",\n    pageIndex: [2, \"pageIndex\", \"pageIndex\", numberAttribute],\n    length: [2, \"length\", \"length\", numberAttribute],\n    pageSize: [2, \"pageSize\", \"pageSize\", numberAttribute],\n    pageSizeOptions: \"pageSizeOptions\",\n    hidePageSize: [2, \"hidePageSize\", \"hidePageSize\", booleanAttribute],\n    showFirstLastButtons: [2, \"showFirstLastButtons\", \"showFirstLastButtons\", booleanAttribute],\n    selectConfig: \"selectConfig\",\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n  },\n  outputs: {\n    page: \"page\"\n  },\n  exportAs: [\"matPaginator\"],\n  decls: 14,\n  vars: 14,\n  consts: [[\"selectRef\", \"\"], [1, \"mat-mdc-paginator-outer-container\"], [1, \"mat-mdc-paginator-container\"], [1, \"mat-mdc-paginator-page-size\"], [1, \"mat-mdc-paginator-range-actions\"], [\"aria-live\", \"polite\", 1, \"mat-mdc-paginator-range-label\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-previous\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"viewBox\", \"0 0 24 24\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-paginator-icon\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-next\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [1, \"mat-mdc-paginator-page-size-label\"], [1, \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\"], [1, \"mat-mdc-paginator-page-size-value\"], [\"hideSingleSelectionIndicator\", \"\", 3, \"selectionChange\", \"value\", \"disabled\", \"aria-labelledby\", \"panelClass\", \"disableOptionCentering\"], [3, \"value\"], [1, \"mat-mdc-paginator-touch-target\", 3, \"click\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"d\", \"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"d\", \"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"]],\n  template: function _MatPaginator_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n      i0.ɵɵtemplate(2, _MatPaginator_Conditional_2_Template, 5, 4, \"div\", 3);\n      i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5);\n      i0.ɵɵtext(5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(6, _MatPaginator_Conditional_6_Template, 3, 5, \"button\", 6);\n      i0.ɵɵelementStart(7, \"button\", 7);\n      i0.ɵɵlistener(\"click\", function _MatPaginator_Template_button_click_7_listener() {\n        return ctx._buttonClicked(ctx.pageIndex - 1, ctx._previousButtonsDisabled());\n      });\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(8, \"svg\", 8);\n      i0.ɵɵelement(9, \"path\", 9);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(10, \"button\", 10);\n      i0.ɵɵlistener(\"click\", function _MatPaginator_Template_button_click_10_listener() {\n        return ctx._buttonClicked(ctx.pageIndex + 1, ctx._nextButtonsDisabled());\n      });\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(11, \"svg\", 8);\n      i0.ɵɵelement(12, \"path\", 11);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(13, _MatPaginator_Conditional_13_Template, 3, 5, \"button\", 12);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(!ctx.hidePageSize ? 2 : -1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate1(\" \", ctx._intl.getRangeLabel(ctx.pageIndex, ctx.pageSize, ctx.length), \" \");\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.showFirstLastButtons ? 6 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"matTooltip\", ctx._intl.previousPageLabel)(\"matTooltipDisabled\", ctx._previousButtonsDisabled())(\"disabled\", ctx._previousButtonsDisabled())(\"tabindex\", ctx._previousButtonsDisabled() ? -1 : null);\n      i0.ɵɵattribute(\"aria-label\", ctx._intl.previousPageLabel);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"matTooltip\", ctx._intl.nextPageLabel)(\"matTooltipDisabled\", ctx._nextButtonsDisabled())(\"disabled\", ctx._nextButtonsDisabled())(\"tabindex\", ctx._nextButtonsDisabled() ? -1 : null);\n      i0.ɵɵattribute(\"aria-label\", ctx._intl.nextPageLabel);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.showFirstLastButtons ? 13 : -1);\n    }\n  },\n  dependencies: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n  styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height:var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginator, [{\n    type: Component,\n    args: [{\n      selector: 'mat-paginator',\n      exportAs: 'matPaginator',\n      host: {\n        'class': 'mat-mdc-paginator',\n        'role': 'group'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n      template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    @if (!hidePageSize) {\\n      <div class=\\\"mat-mdc-paginator-page-size\\\">\\n        <div class=\\\"mat-mdc-paginator-page-size-label\\\" [attr.id]=\\\"_pageSizeLabelId\\\">\\n          {{_intl.itemsPerPageLabel}}\\n        </div>\\n\\n        @if (_displayedPageSizeOptions.length > 1) {\\n          <mat-form-field\\n            [appearance]=\\\"_formFieldAppearance!\\\"\\n            [color]=\\\"color\\\"\\n            class=\\\"mat-mdc-paginator-page-size-select\\\">\\n            <mat-select\\n              #selectRef\\n              [value]=\\\"pageSize\\\"\\n              [disabled]=\\\"disabled\\\"\\n              [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n              [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n              [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n              (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n              hideSingleSelectionIndicator>\\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\\n                <mat-option [value]=\\\"pageSizeOption\\\">\\n                  {{pageSizeOption}}\\n                </mat-option>\\n              }\\n            </mat-select>\\n          <div class=\\\"mat-mdc-paginator-touch-target\\\" (click)=\\\"selectRef.open()\\\"></div>\\n          </mat-form-field>\\n        }\\n\\n        @if (_displayedPageSizeOptions.length <= 1) {\\n          <div class=\\\"mat-mdc-paginator-page-size-value\\\">{{pageSize}}</div>\\n        }\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <!--\\n      The buttons use `disabledInteractive` so that they can retain focus if they become disabled,\\n      otherwise focus is moved to the document body. However, users should not be able to navigate\\n      into these buttons, so `tabindex` is set to -1 when disabled.\\n      -->\\n\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-first\\\"\\n                (click)=\\\"_buttonClicked(0, _previousButtonsDisabled())\\\"\\n                [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n                matTooltipPosition=\\\"above\\\"\\n                [disabled]=\\\"_previousButtonsDisabled()\\\"\\n                [tabindex]=\\\"_previousButtonsDisabled() ? -1 : null\\\"\\n                disabledInteractive>\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"_buttonClicked(pageIndex - 1, _previousButtonsDisabled())\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              matTooltipPosition=\\\"above\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              [tabindex]=\\\"_previousButtonsDisabled() ? -1 : null\\\"\\n              disabledInteractive>\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"_buttonClicked(pageIndex + 1, _nextButtonsDisabled())\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              matTooltipPosition=\\\"above\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              [tabindex]=\\\"_nextButtonsDisabled() ? -1 : null\\\"\\n              disabledInteractive>\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-last\\\"\\n                (click)=\\\"_buttonClicked(getNumberOfPages() - 1, _nextButtonsDisabled())\\\"\\n                [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n                matTooltipPosition=\\\"above\\\"\\n                [disabled]=\\\"_nextButtonsDisabled()\\\"\\n                [tabindex]=\\\"_nextButtonsDisabled() ? -1 : null\\\"\\n                disabledInteractive>\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height:var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    length: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    pageSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    hidePageSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showFirstLastButtons: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectConfig: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    page: [{\n      type: Output\n    }]\n  });\n})();\nclass MatPaginatorModule {}\n_MatPaginatorModule = MatPaginatorModule;\n_defineProperty(MatPaginatorModule, \"\\u0275fac\", function _MatPaginatorModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatPaginatorModule)();\n});\n_defineProperty(MatPaginatorModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatPaginatorModule,\n  imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n  exports: [MatPaginator]\n}));\n_defineProperty(MatPaginatorModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_PAGINATOR_INTL_PROVIDER],\n  imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n      exports: [MatPaginator],\n      providers: [MAT_PAGINATOR_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent };", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "rf", "pageSizeOption_r3", "ctx", "$implicit", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "_MatPaginator_Conditional_2_Conditional_3_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "_MatPaginator_Conditional_2_Conditional_3_Template_mat_select_selectionChange_1_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "_changePageSize", "value", "ɵɵrepeaterCreate", "_MatPaginator_Conditional_2_Conditional_3_For_4_Template", "ɵɵrepeaterTrackByIdentity", "_MatPaginator_Conditional_2_Conditional_3_Template_div_click_5_listener", "selectRef_r4", "ɵɵreference", "open", "_formFieldAppearance", "color", "pageSize", "disabled", "_pageSizeLabelId", "selectConfig", "panelClass", "disableOptionCentering", "ɵɵrepeater", "_displayedPageSizeOptions", "_MatPaginator_Conditional_2_Conditional_4_Template", "ɵɵtextInterpolate", "_MatPaginator_Conditional_2_Template", "ɵɵtemplate", "ɵɵattribute", "_intl", "itemsPerPageLabel", "ɵɵconditional", "length", "_MatPaginator_Conditional_6_Template", "_r5", "_MatPaginator_Conditional_6_Template_button_click_0_listener", "_buttonClicked", "_previousButtonsDisabled", "ɵɵnamespaceSVG", "ɵɵelement", "firstPageLabel", "_MatPaginator_Conditional_13_Template", "_r6", "_MatPaginator_Conditional_13_Template_button_click_0_listener", "getNumberOfPages", "_nextButtonsDisabled", "lastPageLabel", "Injectable", "Optional", "SkipSelf", "InjectionToken", "inject", "ChangeDetectorRef", "numberAttribute", "EventEmitter", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "NgModule", "Subject", "ReplaySubject", "_IdGenerator", "j", "MatFormField", "g", "MatSelect", "M", "MatSelectModule", "e", "MatTooltip", "h", "MatTooltipModule", "MatOption", "MatIconButton", "MatButtonModule", "MatPaginatorIntl", "constructor", "_defineProperty", "page", "Math", "max", "startIndex", "endIndex", "min", "_MatPaginatorIntl", "_MatPaginatorIntl_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "MAT_PAGINATOR_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_PAGINATOR_INTL_PROVIDER", "provide", "deps", "useFactory", "DEFAULT_PAGE_SIZE", "PageEvent", "MAT_PAGINATOR_DEFAULT_OPTIONS", "MatPaginator", "pageIndex", "_pageIndex", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_length", "_pageSize", "_updateDisplayedPageSizeOptions", "pageSizeOptions", "_pageSizeOptions", "map", "p", "getId", "_initializedStream", "defaults", "optional", "_intlChanges", "changes", "subscribe", "hidePageSize", "showFirstLastButtons", "formFieldAppearance", "ngOnInit", "_isInitialized", "next", "ngOnDestroy", "complete", "unsubscribe", "nextPage", "hasNextPage", "_navigate", "previousPage", "hasPreviousPage", "firstPage", "lastPage", "maxPageIndex", "ceil", "previousPageIndex", "floor", "_emitPageEvent", "slice", "indexOf", "push", "sort", "a", "b", "emit", "index", "previousIndex", "targetIndex", "isDisabled", "_MatPaginator", "_MatPaginator_Factory", "ɵɵdefineComponent", "selectors", "hostAttrs", "inputs", "outputs", "exportAs", "decls", "vars", "consts", "template", "_MatPaginator_Template", "_MatPaginator_Template_button_click_7_listener", "ɵɵnamespaceHTML", "_MatPaginator_Template_button_click_10_listener", "getRangeLabel", "previousPageLabel", "nextPageLabel", "dependencies", "styles", "encapsulation", "changeDetection", "selector", "host", "OnPush", "None", "imports", "transform", "MatPaginatorModule", "_MatPaginatorModule", "_MatPaginatorModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/paginator.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, inject, ChangeDetectorRef, numberAttribute, EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { j as MatFormField } from './form-field-DqPi4knt.mjs';\nimport { g as MatSelect, M as MatSelectModule } from './module-Cbt8Fcmv.mjs';\nimport { e as MatTooltip, h as MatTooltipModule } from './module-C9K6ZqpI.mjs';\nimport { M as MatOption } from './option-ChV6uQgD.mjs';\nimport { M as MatIconButton } from './icon-button-D1J0zeqv.mjs';\nimport { MatButtonModule } from './button.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/platform';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport '@angular/cdk/collections';\nimport '@angular/cdk/keycodes';\nimport '@angular/forms';\nimport './error-options-Dm2JJUbF.mjs';\nimport './error-state-Dtb1IHM-.mjs';\nimport './index-DOxJc1m4.mjs';\nimport './index-SYVYjXwK.mjs';\nimport './common-module-WayjW0Pb.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-module-CAX2sutq.mjs';\nimport './pseudo-checkbox-CJ7seqQH.mjs';\nimport './module-BXZhw7pQ.mjs';\nimport '@angular/cdk/observers';\nimport '@angular/cdk/portal';\nimport './structural-styles-BQUT6wsL.mjs';\nimport './ripple-loader-Ce3DAhPW.mjs';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nclass MatPaginatorIntl {\n    /**\n     * Stream to emit from when labels are changed. Use this to notify components when the labels have\n     * changed after initialization.\n     */\n    changes = new Subject();\n    /** A label for the page size selector. */\n    itemsPerPageLabel = 'Items per page:';\n    /** A label for the button that increments the current page. */\n    nextPageLabel = 'Next page';\n    /** A label for the button that decrements the current page. */\n    previousPageLabel = 'Previous page';\n    /** A label for the button that moves to the first page. */\n    firstPageLabel = 'First page';\n    /** A label for the button that moves to the last page. */\n    lastPageLabel = 'Last page';\n    /** A label for the range of items within the current page and the length of the whole list. */\n    getRangeLabel = (page, pageSize, length) => {\n        if (length == 0 || pageSize == 0) {\n            return `0 of ${length}`;\n        }\n        length = Math.max(length, 0);\n        const startIndex = page * pageSize;\n        // If the start index exceeds the list length, do not try and fix the end index to the end.\n        const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n        return `${startIndex + 1} – ${endIndex} of ${length}`;\n    };\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginatorIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginatorIntl, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginatorIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatPaginatorIntl();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n    // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n    provide: MatPaginatorIntl,\n    deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n    useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY,\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {\n    /** The current page index. */\n    pageIndex;\n    /**\n     * Index of the page that was selected previously.\n     * @breaking-change 8.0.0 To be made into a required property.\n     */\n    previousPageIndex;\n    /** The current page size. */\n    pageSize;\n    /** The current total number of items being paged. */\n    length;\n}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator {\n    _intl = inject(MatPaginatorIntl);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    /** If set, styles the \"page size\" form field with the designated style. */\n    _formFieldAppearance;\n    /** ID for the DOM node containing the paginator's items per page label. */\n    _pageSizeLabelId = inject(_IdGenerator).getId('mat-paginator-page-size-label-');\n    _intlChanges;\n    _isInitialized = false;\n    _initializedStream = new ReplaySubject(1);\n    /**\n     * Theme color of the underlying form controls. This API is supported in M2\n     * themes only,it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/paginator/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n    get pageIndex() {\n        return this._pageIndex;\n    }\n    set pageIndex(value) {\n        this._pageIndex = Math.max(value || 0, 0);\n        this._changeDetectorRef.markForCheck();\n    }\n    _pageIndex = 0;\n    /** The length of the total number of items that are being paginated. Defaulted to 0. */\n    get length() {\n        return this._length;\n    }\n    set length(value) {\n        this._length = value || 0;\n        this._changeDetectorRef.markForCheck();\n    }\n    _length = 0;\n    /** Number of items to display on a page. By default set to 50. */\n    get pageSize() {\n        return this._pageSize;\n    }\n    set pageSize(value) {\n        this._pageSize = Math.max(value || 0, 0);\n        this._updateDisplayedPageSizeOptions();\n    }\n    _pageSize;\n    /** The set of provided page size options to display to the user. */\n    get pageSizeOptions() {\n        return this._pageSizeOptions;\n    }\n    set pageSizeOptions(value) {\n        this._pageSizeOptions = (value || []).map(p => numberAttribute(p, 0));\n        this._updateDisplayedPageSizeOptions();\n    }\n    _pageSizeOptions = [];\n    /** Whether to hide the page size selection UI from the user. */\n    hidePageSize = false;\n    /** Whether to show the first/last buttons UI to the user. */\n    showFirstLastButtons = false;\n    /** Used to configure the underlying `MatSelect` inside the paginator. */\n    selectConfig = {};\n    /** Whether the paginator is disabled. */\n    disabled = false;\n    /** Event emitted when the paginator changes the page size or page index. */\n    page = new EventEmitter();\n    /** Displayed set of page size options. Will be sorted and include current page size. */\n    _displayedPageSizeOptions;\n    /** Emits when the paginator is initialized. */\n    initialized = this._initializedStream;\n    constructor() {\n        const _intl = this._intl;\n        const defaults = inject(MAT_PAGINATOR_DEFAULT_OPTIONS, {\n            optional: true,\n        });\n        this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n        if (defaults) {\n            const { pageSize, pageSizeOptions, hidePageSize, showFirstLastButtons } = defaults;\n            if (pageSize != null) {\n                this._pageSize = pageSize;\n            }\n            if (pageSizeOptions != null) {\n                this._pageSizeOptions = pageSizeOptions;\n            }\n            if (hidePageSize != null) {\n                this.hidePageSize = hidePageSize;\n            }\n            if (showFirstLastButtons != null) {\n                this.showFirstLastButtons = showFirstLastButtons;\n            }\n        }\n        this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n        this._updateDisplayedPageSizeOptions();\n        this._initializedStream.next();\n    }\n    ngOnDestroy() {\n        this._initializedStream.complete();\n        this._intlChanges.unsubscribe();\n    }\n    /** Advances to the next page if it exists. */\n    nextPage() {\n        if (this.hasNextPage()) {\n            this._navigate(this.pageIndex + 1);\n        }\n    }\n    /** Move back to the previous page if it exists. */\n    previousPage() {\n        if (this.hasPreviousPage()) {\n            this._navigate(this.pageIndex - 1);\n        }\n    }\n    /** Move to the first page if not already there. */\n    firstPage() {\n        // hasPreviousPage being false implies at the start\n        if (this.hasPreviousPage()) {\n            this._navigate(0);\n        }\n    }\n    /** Move to the last page if not already there. */\n    lastPage() {\n        // hasNextPage being false implies at the end\n        if (this.hasNextPage()) {\n            this._navigate(this.getNumberOfPages() - 1);\n        }\n    }\n    /** Whether there is a previous page. */\n    hasPreviousPage() {\n        return this.pageIndex >= 1 && this.pageSize != 0;\n    }\n    /** Whether there is a next page. */\n    hasNextPage() {\n        const maxPageIndex = this.getNumberOfPages() - 1;\n        return this.pageIndex < maxPageIndex && this.pageSize != 0;\n    }\n    /** Calculate the number of pages */\n    getNumberOfPages() {\n        if (!this.pageSize) {\n            return 0;\n        }\n        return Math.ceil(this.length / this.pageSize);\n    }\n    /**\n     * Changes the page size so that the first item displayed on the page will still be\n     * displayed using the new page size.\n     *\n     * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n     * switching so that the page size is 5 will set the third page as the current page so\n     * that the 10th item will still be displayed.\n     */\n    _changePageSize(pageSize) {\n        // Current page needs to be updated to reflect the new page size. Navigate to the page\n        // containing the previous page's first item.\n        const startIndex = this.pageIndex * this.pageSize;\n        const previousPageIndex = this.pageIndex;\n        this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n        this.pageSize = pageSize;\n        this._emitPageEvent(previousPageIndex);\n    }\n    /** Checks whether the buttons for going forwards should be disabled. */\n    _nextButtonsDisabled() {\n        return this.disabled || !this.hasNextPage();\n    }\n    /** Checks whether the buttons for going backwards should be disabled. */\n    _previousButtonsDisabled() {\n        return this.disabled || !this.hasPreviousPage();\n    }\n    /**\n     * Updates the list of page size options to display to the user. Includes making sure that\n     * the page size is an option and that the list is sorted.\n     */\n    _updateDisplayedPageSizeOptions() {\n        if (!this._isInitialized) {\n            return;\n        }\n        // If no page size is provided, use the first page size option or the default page size.\n        if (!this.pageSize) {\n            this._pageSize =\n                this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n        }\n        this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n        if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n            this._displayedPageSizeOptions.push(this.pageSize);\n        }\n        // Sort the numbers using a number-specific sort function.\n        this._displayedPageSizeOptions.sort((a, b) => a - b);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n    _emitPageEvent(previousPageIndex) {\n        this.page.emit({\n            previousPageIndex,\n            pageIndex: this.pageIndex,\n            pageSize: this.pageSize,\n            length: this.length,\n        });\n    }\n    /** Navigates to a specific page index. */\n    _navigate(index) {\n        const previousIndex = this.pageIndex;\n        if (index !== previousIndex) {\n            this.pageIndex = index;\n            this._emitPageEvent(previousIndex);\n        }\n    }\n    /**\n     * Callback invoked when one of the navigation buttons is called.\n     * @param targetIndex Index to which the paginator should navigate.\n     * @param isDisabled Whether the button is disabled.\n     */\n    _buttonClicked(targetIndex, isDisabled) {\n        // Note that normally disabled buttons won't dispatch the click event, but the paginator ones\n        // do, because we're using `disabledInteractive` to allow them to be focusable. We need to\n        // check here to avoid the navigation.\n        if (!isDisabled) {\n            this._navigate(targetIndex);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginator, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatPaginator, isStandalone: true, selector: \"mat-paginator\", inputs: { color: \"color\", pageIndex: [\"pageIndex\", \"pageIndex\", numberAttribute], length: [\"length\", \"length\", numberAttribute], pageSize: [\"pageSize\", \"pageSize\", numberAttribute], pageSizeOptions: \"pageSizeOptions\", hidePageSize: [\"hidePageSize\", \"hidePageSize\", booleanAttribute], showFirstLastButtons: [\"showFirstLastButtons\", \"showFirstLastButtons\", booleanAttribute], selectConfig: \"selectConfig\", disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { page: \"page\" }, host: { attributes: { \"role\": \"group\" }, classAttribute: \"mat-mdc-paginator\" }, exportAs: [\"matPaginator\"], ngImport: i0, template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    @if (!hidePageSize) {\\n      <div class=\\\"mat-mdc-paginator-page-size\\\">\\n        <div class=\\\"mat-mdc-paginator-page-size-label\\\" [attr.id]=\\\"_pageSizeLabelId\\\">\\n          {{_intl.itemsPerPageLabel}}\\n        </div>\\n\\n        @if (_displayedPageSizeOptions.length > 1) {\\n          <mat-form-field\\n            [appearance]=\\\"_formFieldAppearance!\\\"\\n            [color]=\\\"color\\\"\\n            class=\\\"mat-mdc-paginator-page-size-select\\\">\\n            <mat-select\\n              #selectRef\\n              [value]=\\\"pageSize\\\"\\n              [disabled]=\\\"disabled\\\"\\n              [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n              [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n              [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n              (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n              hideSingleSelectionIndicator>\\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\\n                <mat-option [value]=\\\"pageSizeOption\\\">\\n                  {{pageSizeOption}}\\n                </mat-option>\\n              }\\n            </mat-select>\\n          <div class=\\\"mat-mdc-paginator-touch-target\\\" (click)=\\\"selectRef.open()\\\"></div>\\n          </mat-form-field>\\n        }\\n\\n        @if (_displayedPageSizeOptions.length <= 1) {\\n          <div class=\\\"mat-mdc-paginator-page-size-value\\\">{{pageSize}}</div>\\n        }\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <!--\\n      The buttons use `disabledInteractive` so that they can retain focus if they become disabled,\\n      otherwise focus is moved to the document body. However, users should not be able to navigate\\n      into these buttons, so `tabindex` is set to -1 when disabled.\\n      -->\\n\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-first\\\"\\n                (click)=\\\"_buttonClicked(0, _previousButtonsDisabled())\\\"\\n                [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n                matTooltipPosition=\\\"above\\\"\\n                [disabled]=\\\"_previousButtonsDisabled()\\\"\\n                [tabindex]=\\\"_previousButtonsDisabled() ? -1 : null\\\"\\n                disabledInteractive>\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"_buttonClicked(pageIndex - 1, _previousButtonsDisabled())\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              matTooltipPosition=\\\"above\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              [tabindex]=\\\"_previousButtonsDisabled() ? -1 : null\\\"\\n              disabledInteractive>\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"_buttonClicked(pageIndex + 1, _nextButtonsDisabled())\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              matTooltipPosition=\\\"above\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              [tabindex]=\\\"_nextButtonsDisabled() ? -1 : null\\\"\\n              disabledInteractive>\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-last\\\"\\n                (click)=\\\"_buttonClicked(getNumberOfPages() - 1, _nextButtonsDisabled())\\\"\\n                [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n                matTooltipPosition=\\\"above\\\"\\n                [disabled]=\\\"_nextButtonsDisabled()\\\"\\n                [tabindex]=\\\"_nextButtonsDisabled() ? -1 : null\\\"\\n                disabledInteractive>\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height:var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}\\n\"], dependencies: [{ kind: \"component\", type: MatFormField, selector: \"mat-form-field\", inputs: [\"hideRequiredMarker\", \"color\", \"floatLabel\", \"appearance\", \"subscriptSizing\", \"hintLabel\"], exportAs: [\"matFormField\"] }, { kind: \"component\", type: MatSelect, selector: \"mat-select\", inputs: [\"aria-describedby\", \"panelClass\", \"disabled\", \"disableRipple\", \"tabIndex\", \"hideSingleSelectionIndicator\", \"placeholder\", \"required\", \"multiple\", \"disableOptionCentering\", \"compareWith\", \"value\", \"aria-label\", \"aria-labelledby\", \"errorStateMatcher\", \"typeaheadDebounceInterval\", \"sortComparator\", \"id\", \"panelWidth\", \"canSelectNullableOptions\"], outputs: [\"openedChange\", \"opened\", \"closed\", \"selectionChange\", \"valueChange\"], exportAs: [\"matSelect\"] }, { kind: \"component\", type: MatOption, selector: \"mat-option\", inputs: [\"value\", \"id\", \"disabled\"], outputs: [\"onSelectionChange\"], exportAs: [\"matOption\"] }, { kind: \"component\", type: MatIconButton, selector: \"button[mat-icon-button]\", exportAs: [\"matButton\"] }, { kind: \"directive\", type: MatTooltip, selector: \"[matTooltip]\", inputs: [\"matTooltipPosition\", \"matTooltipPositionAtOrigin\", \"matTooltipDisabled\", \"matTooltipShowDelay\", \"matTooltipHideDelay\", \"matTooltipTouchGestures\", \"matTooltip\", \"matTooltipClass\"], exportAs: [\"matTooltip\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginator, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-paginator', exportAs: 'matPaginator', host: {\n                        'class': 'mat-mdc-paginator',\n                        'role': 'group',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, imports: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip], template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    @if (!hidePageSize) {\\n      <div class=\\\"mat-mdc-paginator-page-size\\\">\\n        <div class=\\\"mat-mdc-paginator-page-size-label\\\" [attr.id]=\\\"_pageSizeLabelId\\\">\\n          {{_intl.itemsPerPageLabel}}\\n        </div>\\n\\n        @if (_displayedPageSizeOptions.length > 1) {\\n          <mat-form-field\\n            [appearance]=\\\"_formFieldAppearance!\\\"\\n            [color]=\\\"color\\\"\\n            class=\\\"mat-mdc-paginator-page-size-select\\\">\\n            <mat-select\\n              #selectRef\\n              [value]=\\\"pageSize\\\"\\n              [disabled]=\\\"disabled\\\"\\n              [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n              [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n              [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n              (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n              hideSingleSelectionIndicator>\\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\\n                <mat-option [value]=\\\"pageSizeOption\\\">\\n                  {{pageSizeOption}}\\n                </mat-option>\\n              }\\n            </mat-select>\\n          <div class=\\\"mat-mdc-paginator-touch-target\\\" (click)=\\\"selectRef.open()\\\"></div>\\n          </mat-form-field>\\n        }\\n\\n        @if (_displayedPageSizeOptions.length <= 1) {\\n          <div class=\\\"mat-mdc-paginator-page-size-value\\\">{{pageSize}}</div>\\n        }\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      <!--\\n      The buttons use `disabledInteractive` so that they can retain focus if they become disabled,\\n      otherwise focus is moved to the document body. However, users should not be able to navigate\\n      into these buttons, so `tabindex` is set to -1 when disabled.\\n      -->\\n\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-first\\\"\\n                (click)=\\\"_buttonClicked(0, _previousButtonsDisabled())\\\"\\n                [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n                matTooltipPosition=\\\"above\\\"\\n                [disabled]=\\\"_previousButtonsDisabled()\\\"\\n                [tabindex]=\\\"_previousButtonsDisabled() ? -1 : null\\\"\\n                disabledInteractive>\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"_buttonClicked(pageIndex - 1, _previousButtonsDisabled())\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              matTooltipPosition=\\\"above\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\"\\n              [tabindex]=\\\"_previousButtonsDisabled() ? -1 : null\\\"\\n              disabledInteractive>\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"_buttonClicked(pageIndex + 1, _nextButtonsDisabled())\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              matTooltipPosition=\\\"above\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\"\\n              [tabindex]=\\\"_nextButtonsDisabled() ? -1 : null\\\"\\n              disabledInteractive>\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-last\\\"\\n                (click)=\\\"_buttonClicked(getNumberOfPages() - 1, _nextButtonsDisabled())\\\"\\n                [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n                matTooltipPosition=\\\"above\\\"\\n                [disabled]=\\\"_nextButtonsDisabled()\\\"\\n                [tabindex]=\\\"_nextButtonsDisabled() ? -1 : null\\\"\\n                disabledInteractive>\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height:var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input\n            }], pageIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], length: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], pageSize: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], pageSizeOptions: [{\n                type: Input\n            }], hidePageSize: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], showFirstLastButtons: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectConfig: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], page: [{\n                type: Output\n            }] } });\n\nclass MatPaginatorModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginatorModule, imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator], exports: [MatPaginator] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginatorModule, providers: [MAT_PAGINATOR_INTL_PROVIDER], imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n                    exports: [MatPaginator],\n                    providers: [MAT_PAGINATOR_INTL_PROVIDER],\n                }]\n        }] });\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent };\n"], "mappings": ";;;;IAsEiFA,EAAE,CAAAC,cAAA,oBA6QqxD,CAAC;IA7QxxDD,EAAE,CAAAE,MAAA,EA6Q60D,CAAC;IA7Qh1DF,EAAE,CAAAG,YAAA,CA6Q01D,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAC,iBAAA,GAAAC,GAAA,CAAAC,SAAA;IA7Q71DP,EAAE,CAAAQ,UAAA,UAAAH,iBA6QoxD,CAAC;IA7QvxDL,EAAE,CAAAS,SAAA,CA6Q60D,CAAC;IA7Qh1DT,EAAE,CAAAU,kBAAA,MAAAL,iBAAA,KA6Q60D,CAAC;EAAA;AAAA;AAAA,SAAAM,mDAAAP,EAAA,EAAAE,GAAA;EAAA,IAAAF,EAAA;IAAA,MAAAQ,GAAA,GA7Qh1DZ,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAC,cAAA,wBA6QgtC,CAAC,uBAAib,CAAC;IA7QroDD,EAAE,CAAAc,UAAA,6BAAAC,yFAAAC,MAAA;MAAFhB,EAAE,CAAAiB,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFlB,EAAE,CAAAmB,aAAA;MAAA,OAAFnB,EAAE,CAAAoB,WAAA,CA6QujDF,MAAA,CAAAG,eAAA,CAAAL,MAAA,CAAAM,KAA4B,CAAC;IAAA,CAAC,CAAC;IA7QxlDtB,EAAE,CAAAuB,gBAAA,IAAAC,wDAAA,0BAAFxB,EAAE,CAAAyB,yBA6Q22D,CAAC;IA7Q92DzB,EAAE,CAAAG,YAAA,CA6Qs4D,CAAC;IA7Qz4DH,EAAE,CAAAC,cAAA,aA6Q69D,CAAC;IA7Qh+DD,EAAE,CAAAc,UAAA,mBAAAY,wEAAA;MAAF1B,EAAE,CAAAiB,aAAA,CAAAL,GAAA;MAAA,MAAAe,YAAA,GAAF3B,EAAE,CAAA4B,WAAA;MAAA,OAAF5B,EAAE,CAAAoB,WAAA,CA6Q28DO,YAAA,CAAAE,IAAA,CAAe,CAAC;IAAA,CAAC,CAAC;IA7Q/9D7B,EAAE,CAAAG,YAAA,CA6Qm+D,CAAC,CAA4B,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAc,MAAA,GA7QngElB,EAAE,CAAAmB,aAAA;IAAFnB,EAAE,CAAAQ,UAAA,eAAAU,MAAA,CAAAY,oBA6QsnC,CAAC,UAAAZ,MAAA,CAAAa,KAA8B,CAAC;IA7QxpC/B,EAAE,CAAAS,SAAA,CA6QuyC,CAAC;IA7Q1yCT,EAAE,CAAAQ,UAAA,UAAAU,MAAA,CAAAc,QA6QuyC,CAAC,aAAAd,MAAA,CAAAe,QAAsC,CAAC,oBAAAf,MAAA,CAAAgB,gBAAqD,CAAC,eAAAhB,MAAA,CAAAiB,YAAA,CAAAC,UAAA,MAA6D,CAAC,2BAAAlB,MAAA,CAAAiB,YAAA,CAAAE,sBAA+E,CAAC;IA7QrhDrC,EAAE,CAAAS,SAAA,EA6Q22D,CAAC;IA7Q92DT,EAAE,CAAAsC,UAAA,CAAApB,MAAA,CAAAqB,yBA6Q22D,CAAC;EAAA;AAAA;AAAA,SAAAC,mDAAApC,EAAA,EAAAE,GAAA;EAAA,IAAAF,EAAA;IA7Q92DJ,EAAE,CAAAC,cAAA,aA6QioE,CAAC;IA7QpoED,EAAE,CAAAE,MAAA,EA6Q6oE,CAAC;IA7QhpEF,EAAE,CAAAG,YAAA,CA6QmpE,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAc,MAAA,GA7QtpElB,EAAE,CAAAmB,aAAA;IAAFnB,EAAE,CAAAS,SAAA,CA6Q6oE,CAAC;IA7QhpET,EAAE,CAAAyC,iBAAA,CAAAvB,MAAA,CAAAc,QA6Q6oE,CAAC;EAAA;AAAA;AAAA,SAAAU,qCAAAtC,EAAA,EAAAE,GAAA;EAAA,IAAAF,EAAA;IA7QhpEJ,EAAE,CAAAC,cAAA,YA6Q81B,CAAC,aAAyF,CAAC;IA7Q37BD,EAAE,CAAAE,MAAA,EA6Qy+B,CAAC;IA7Q5+BF,EAAE,CAAAG,YAAA,CA6Q++B,CAAC;IA7Ql/BH,EAAE,CAAA2C,UAAA,IAAAhC,kDAAA,4BA6QuiC,CAAC,IAAA6B,kDAAA,iBAA4hC,CAAC;IA7QvkExC,EAAE,CAAAG,YAAA,CA6Q4qE,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAc,MAAA,GA7Q/qElB,EAAE,CAAAmB,aAAA;IAAFnB,EAAE,CAAAS,SAAA,CA6Qu7B,CAAC;IA7Q17BT,EAAE,CAAA4C,WAAA,OAAA1B,MAAA,CAAAgB,gBAAA;IAAFlC,EAAE,CAAAS,SAAA,CA6Qy+B,CAAC;IA7Q5+BT,EAAE,CAAAU,kBAAA,MAAAQ,MAAA,CAAA2B,KAAA,CAAAC,iBAAA,KA6Qy+B,CAAC;IA7Q5+B9C,EAAE,CAAAS,SAAA,CA6Q2gE,CAAC;IA7Q9gET,EAAE,CAAA+C,aAAA,CAAA7B,MAAA,CAAAqB,yBAAA,CAAAS,MAAA,aA6Q2gE,CAAC;IA7Q9gEhD,EAAE,CAAAS,SAAA,CA6Q8pE,CAAC;IA7QjqET,EAAE,CAAA+C,aAAA,CAAA7B,MAAA,CAAAqB,yBAAA,CAAAS,MAAA,cA6Q8pE,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAA7C,EAAA,EAAAE,GAAA;EAAA,IAAAF,EAAA;IAAA,MAAA8C,GAAA,GA7QjqElD,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAC,cAAA,gBA6QoxG,CAAC;IA7QvxGD,EAAE,CAAAc,UAAA,mBAAAqC,6DAAA;MAAFnD,EAAE,CAAAiB,aAAA,CAAAiC,GAAA;MAAA,MAAAhC,MAAA,GAAFlB,EAAE,CAAAmB,aAAA;MAAA,OAAFnB,EAAE,CAAAoB,WAAA,CA6Qw1FF,MAAA,CAAAkC,cAAA,CAAe,CAAC,EAAElC,MAAA,CAAAmC,wBAAA,CAAyB,CAAC,CAAC;IAAA,CAAC,CAAC;IA7Qz4FrD,EAAE,CAAAsD,cAAA;IAAFtD,EAAE,CAAAC,cAAA,YA6Qk7G,CAAC;IA7Qr7GD,EAAE,CAAAuD,SAAA,cA6QwgH,CAAC;IA7Q3gHvD,EAAE,CAAAG,YAAA,CA6Q0hH,CAAC,CAAkB,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAc,MAAA,GA7QhjHlB,EAAE,CAAAmB,aAAA;IAAFnB,EAAE,CAAAQ,UAAA,eAAAU,MAAA,CAAA2B,KAAA,CAAAW,cA6Qy/F,CAAC,uBAAAtC,MAAA,CAAAmC,wBAAA,EAAoE,CAAC,aAAAnC,MAAA,CAAAmC,wBAAA,EAAwG,CAAC,aAAAnC,MAAA,CAAAmC,wBAAA,cAAsE,CAAC;IA7QjvGrD,EAAE,CAAA4C,WAAA,eAAA1B,MAAA,CAAA2B,KAAA,CAAAW,cAAA;EAAA;AAAA;AAAA,SAAAC,sCAAArD,EAAA,EAAAE,GAAA;EAAA,IAAAF,EAAA;IAAA,MAAAsD,GAAA,GAAF1D,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAC,cAAA,gBA6Q6xL,CAAC;IA7QhyLD,EAAE,CAAAc,UAAA,mBAAA6C,8DAAA;MAAF3D,EAAE,CAAAiB,aAAA,CAAAyC,GAAA;MAAA,MAAAxC,MAAA,GAAFlB,EAAE,CAAAmB,aAAA;MAAA,OAAFnB,EAAE,CAAAoB,WAAA,CA6Q81KF,MAAA,CAAAkC,cAAA,CAAelC,MAAA,CAAA0C,gBAAA,CAAiB,CAAC,GAAG,CAAC,EAAE1C,MAAA,CAAA2C,oBAAA,CAAqB,CAAC,CAAC;IAAA,CAAC,CAAC;IA7Qh6K7D,EAAE,CAAAsD,cAAA;IAAFtD,EAAE,CAAAC,cAAA,YA6Q27L,CAAC;IA7Q97LD,EAAE,CAAAuD,SAAA,cA6QihM,CAAC;IA7QphMvD,EAAE,CAAAG,YAAA,CA6QmiM,CAAC,CAAkB,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAc,MAAA,GA7QzjMlB,EAAE,CAAAmB,aAAA;IAAFnB,EAAE,CAAAQ,UAAA,eAAAU,MAAA,CAAA2B,KAAA,CAAAiB,aA6Q8gL,CAAC,uBAAA5C,MAAA,CAAA2C,oBAAA,EAAgE,CAAC,aAAA3C,MAAA,CAAA2C,oBAAA,EAAoG,CAAC,aAAA3C,MAAA,CAAA2C,oBAAA,cAAkE,CAAC;IA7Q1vL7D,EAAE,CAAA4C,WAAA,eAAA1B,MAAA,CAAA2B,KAAA,CAAAiB,aAAA;EAAA;AAAA;AAtEnF,OAAO,KAAK9D,EAAE,MAAM,eAAe;AACnC,SAAS+D,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC1O,SAASC,OAAO,EAAEC,aAAa,QAAQ,MAAM;AAC7C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,CAAC,IAAIC,YAAY,QAAQ,2BAA2B;AAC7D,SAASC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,eAAe,QAAQ,uBAAuB;AAC5E,SAASC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,gBAAgB,QAAQ,uBAAuB;AAC9E,SAASL,CAAC,IAAIM,SAAS,QAAQ,uBAAuB;AACtD,SAASN,CAAC,IAAIO,aAAa,QAAQ,4BAA4B;AAC/D,SAASC,eAAe,QAAQ,cAAc;AAC9C,OAAO,mBAAmB;AAC1B,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,iBAAiB;AACxB,OAAO,gBAAgB;AACvB,OAAO,gCAAgC;AACvC,OAAO,sBAAsB;AAC7B,OAAO,wBAAwB;AAC/B,OAAO,0BAA0B;AACjC,OAAO,uBAAuB;AAC9B,OAAO,gBAAgB;AACvB,OAAO,8BAA8B;AACrC,OAAO,4BAA4B;AACnC,OAAO,sBAAsB;AAC7B,OAAO,sBAAsB;AAC7B,OAAO,8BAA8B;AACrC,OAAO,uBAAuB;AAC9B,OAAO,sBAAsB;AAC7B,OAAO,uCAAuC;AAC9C,OAAO,gCAAgC;AACvC,OAAO,uBAAuB;AAC9B,OAAO,wBAAwB;AAC/B,OAAO,qBAAqB;AAC5B,OAAO,kCAAkC;AACzC,OAAO,8BAA8B;;AAErC;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EAAAC,YAAA;IACnB;AACJ;AACA;AACA;IAHIC,eAAA,kBAIU,IAAIlB,OAAO,CAAC,CAAC;IACvB;IAAAkB,eAAA,4BACoB,iBAAiB;IACrC;IAAAA,eAAA,wBACgB,WAAW;IAC3B;IAAAA,eAAA,4BACoB,eAAe;IACnC;IAAAA,eAAA,yBACiB,YAAY;IAC7B;IAAAA,eAAA,wBACgB,WAAW;IAC3B;IAAAA,eAAA,wBACgB,CAACC,IAAI,EAAEjE,QAAQ,EAAEgB,MAAM,KAAK;MACxC,IAAIA,MAAM,IAAI,CAAC,IAAIhB,QAAQ,IAAI,CAAC,EAAE;QAC9B,OAAO,QAAQgB,MAAM,EAAE;MAC3B;MACAA,MAAM,GAAGkD,IAAI,CAACC,GAAG,CAACnD,MAAM,EAAE,CAAC,CAAC;MAC5B,MAAMoD,UAAU,GAAGH,IAAI,GAAGjE,QAAQ;MAClC;MACA,MAAMqE,QAAQ,GAAGD,UAAU,GAAGpD,MAAM,GAAGkD,IAAI,CAACI,GAAG,CAACF,UAAU,GAAGpE,QAAQ,EAAEgB,MAAM,CAAC,GAAGoD,UAAU,GAAGpE,QAAQ;MACtG,OAAO,GAAGoE,UAAU,GAAG,CAAC,MAAMC,QAAQ,OAAOrD,MAAM,EAAE;IACzD,CAAC;EAAA;AAGL;AAACuD,iBAAA,GA7BKT,gBAAgB;AAAAE,eAAA,CAAhBF,gBAAgB,wBAAAU,0BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA2BiFX,iBAAgB;AAAA;AAAAE,eAAA,CA3BjHF,gBAAgB,+BA8B2D9F,EAAE,CAAA0G,kBAAA;EAAAC,KAAA,EAFwBb,iBAAgB;EAAAc,OAAA,EAAhBd,iBAAgB,CAAAe,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE/I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF/G,EAAE,CAAAgH,iBAAA,CAAQlB,gBAAgB,EAAc,CAAC;IAC9GmB,IAAI,EAAElD,UAAU;IAChBmD,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,SAASK,mCAAmCA,CAACC,UAAU,EAAE;EACrD,OAAOA,UAAU,IAAI,IAAItB,gBAAgB,CAAC,CAAC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,2BAA2B,GAAG;EAChC;EACAC,OAAO,EAAExB,gBAAgB;EACzByB,IAAI,EAAE,CAAC,CAAC,IAAIvD,QAAQ,CAAC,CAAC,EAAE,IAAIC,QAAQ,CAAC,CAAC,EAAE6B,gBAAgB,CAAC,CAAC;EAC1D0B,UAAU,EAAEL;AAChB,CAAC;;AAED;AACA,MAAMM,iBAAiB,GAAG,EAAE;AAC5B;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EAAA3B,YAAA;IACZ;IAAAC,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA;EAAA;AAEJ;AACA;AACA,MAAM2B,6BAA6B,GAAG,IAAIzD,cAAc,CAAC,+BAA+B,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA,MAAM0D,YAAY,CAAC;EAkBf;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAACvG,KAAK,EAAE;IACjB,IAAI,CAACwG,UAAU,GAAG5B,IAAI,CAACC,GAAG,CAAC7E,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;IACzC,IAAI,CAACyG,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EAEA;EACA,IAAIhF,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACiF,OAAO;EACvB;EACA,IAAIjF,MAAMA,CAAC1B,KAAK,EAAE;IACd,IAAI,CAAC2G,OAAO,GAAG3G,KAAK,IAAI,CAAC;IACzB,IAAI,CAACyG,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EAEA;EACA,IAAIhG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACkG,SAAS;EACzB;EACA,IAAIlG,QAAQA,CAACV,KAAK,EAAE;IAChB,IAAI,CAAC4G,SAAS,GAAGhC,IAAI,CAACC,GAAG,CAAC7E,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;IACxC,IAAI,CAAC6G,+BAA+B,CAAC,CAAC;EAC1C;EAEA;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAAC9G,KAAK,EAAE;IACvB,IAAI,CAAC+G,gBAAgB,GAAG,CAAC/G,KAAK,IAAI,EAAE,EAAEgH,GAAG,CAACC,CAAC,IAAIlE,eAAe,CAACkE,CAAC,EAAE,CAAC,CAAC,CAAC;IACrE,IAAI,CAACJ,+BAA+B,CAAC,CAAC;EAC1C;EAgBApC,WAAWA,CAAA,EAAG;IAAAC,eAAA,gBAnEN7B,MAAM,CAAC2B,gBAAgB,CAAC;IAAAE,eAAA,6BACX7B,MAAM,CAACC,iBAAiB,CAAC;IAC9C;IAAA4B,eAAA;IAEA;IAAAA,eAAA,2BACmB7B,MAAM,CAACa,YAAY,CAAC,CAACwD,KAAK,CAAC,gCAAgC,CAAC;IAAAxC,eAAA;IAAAA,eAAA,yBAE9D,KAAK;IAAAA,eAAA,6BACD,IAAIjB,aAAa,CAAC,CAAC,CAAC;IACzC;AACJ;AACA;AACA;AACA;AACA;AACA;IANIiB,eAAA;IAAAA,eAAA,qBAgBa,CAAC;IAAAA,eAAA,kBASJ,CAAC;IAAAA,eAAA;IAAAA,eAAA,2BAkBQ,EAAE;IACrB;IAAAA,eAAA,uBACe,KAAK;IACpB;IAAAA,eAAA,+BACuB,KAAK;IAC5B;IAAAA,eAAA,uBACe,CAAC,CAAC;IACjB;IAAAA,eAAA,mBACW,KAAK;IAChB;IAAAA,eAAA,eACO,IAAI1B,YAAY,CAAC,CAAC;IACzB;IAAA0B,eAAA;IAEA;IAAAA,eAAA,sBACc,IAAI,CAACyC,kBAAkB;IAEjC,MAAM5F,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAM6F,QAAQ,GAAGvE,MAAM,CAACwD,6BAA6B,EAAE;MACnDgB,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAACC,YAAY,GAAG/F,KAAK,CAACgG,OAAO,CAACC,SAAS,CAAC,MAAM,IAAI,CAACf,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAAC;IACzF,IAAIU,QAAQ,EAAE;MACV,MAAM;QAAE1G,QAAQ;QAAEoG,eAAe;QAAEW,YAAY;QAAEC;MAAqB,CAAC,GAAGN,QAAQ;MAClF,IAAI1G,QAAQ,IAAI,IAAI,EAAE;QAClB,IAAI,CAACkG,SAAS,GAAGlG,QAAQ;MAC7B;MACA,IAAIoG,eAAe,IAAI,IAAI,EAAE;QACzB,IAAI,CAACC,gBAAgB,GAAGD,eAAe;MAC3C;MACA,IAAIW,YAAY,IAAI,IAAI,EAAE;QACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;MACpC;MACA,IAAIC,oBAAoB,IAAI,IAAI,EAAE;QAC9B,IAAI,CAACA,oBAAoB,GAAGA,oBAAoB;MACpD;IACJ;IACA,IAAI,CAAClH,oBAAoB,GAAG,CAAA4G,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,mBAAmB,KAAI,SAAS;EAC1E;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAChB,+BAA+B,CAAC,CAAC;IACtC,IAAI,CAACM,kBAAkB,CAACW,IAAI,CAAC,CAAC;EAClC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACZ,kBAAkB,CAACa,QAAQ,CAAC,CAAC;IAClC,IAAI,CAACV,YAAY,CAACW,WAAW,CAAC,CAAC;EACnC;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;MACpB,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC7B,SAAS,GAAG,CAAC,CAAC;IACtC;EACJ;EACA;EACA8B,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MACxB,IAAI,CAACF,SAAS,CAAC,IAAI,CAAC7B,SAAS,GAAG,CAAC,CAAC;IACtC;EACJ;EACA;EACAgC,SAASA,CAAA,EAAG;IACR;IACA,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,EAAE;MACxB,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC;IACrB;EACJ;EACA;EACAI,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,IAAI,CAACL,WAAW,CAAC,CAAC,EAAE;MACpB,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC9F,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/C;EACJ;EACA;EACAgG,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC/B,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC7F,QAAQ,IAAI,CAAC;EACpD;EACA;EACAyH,WAAWA,CAAA,EAAG;IACV,MAAMM,YAAY,GAAG,IAAI,CAACnG,gBAAgB,CAAC,CAAC,GAAG,CAAC;IAChD,OAAO,IAAI,CAACiE,SAAS,GAAGkC,YAAY,IAAI,IAAI,CAAC/H,QAAQ,IAAI,CAAC;EAC9D;EACA;EACA4B,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAC5B,QAAQ,EAAE;MAChB,OAAO,CAAC;IACZ;IACA,OAAOkE,IAAI,CAAC8D,IAAI,CAAC,IAAI,CAAChH,MAAM,GAAG,IAAI,CAAChB,QAAQ,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIX,eAAeA,CAACW,QAAQ,EAAE;IACtB;IACA;IACA,MAAMoE,UAAU,GAAG,IAAI,CAACyB,SAAS,GAAG,IAAI,CAAC7F,QAAQ;IACjD,MAAMiI,iBAAiB,GAAG,IAAI,CAACpC,SAAS;IACxC,IAAI,CAACA,SAAS,GAAG3B,IAAI,CAACgE,KAAK,CAAC9D,UAAU,GAAGpE,QAAQ,CAAC,IAAI,CAAC;IACvD,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACmI,cAAc,CAACF,iBAAiB,CAAC;EAC1C;EACA;EACApG,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC5B,QAAQ,IAAI,CAAC,IAAI,CAACwH,WAAW,CAAC,CAAC;EAC/C;EACA;EACApG,wBAAwBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACpB,QAAQ,IAAI,CAAC,IAAI,CAAC2H,eAAe,CAAC,CAAC;EACnD;EACA;AACJ;AACA;AACA;EACIzB,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAACgB,cAAc,EAAE;MACtB;IACJ;IACA;IACA,IAAI,CAAC,IAAI,CAACnH,QAAQ,EAAE;MAChB,IAAI,CAACkG,SAAS,GACV,IAAI,CAACE,eAAe,CAACpF,MAAM,IAAI,CAAC,GAAG,IAAI,CAACoF,eAAe,CAAC,CAAC,CAAC,GAAGX,iBAAiB;IACtF;IACA,IAAI,CAAClF,yBAAyB,GAAG,IAAI,CAAC6F,eAAe,CAACgC,KAAK,CAAC,CAAC;IAC7D,IAAI,IAAI,CAAC7H,yBAAyB,CAAC8H,OAAO,CAAC,IAAI,CAACrI,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9D,IAAI,CAACO,yBAAyB,CAAC+H,IAAI,CAAC,IAAI,CAACtI,QAAQ,CAAC;IACtD;IACA;IACA,IAAI,CAACO,yBAAyB,CAACgI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;IACpD,IAAI,CAAC1C,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACAmC,cAAcA,CAACF,iBAAiB,EAAE;IAC9B,IAAI,CAAChE,IAAI,CAACyE,IAAI,CAAC;MACXT,iBAAiB;MACjBpC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB7F,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBgB,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC;EACN;EACA;EACA0G,SAASA,CAACiB,KAAK,EAAE;IACb,MAAMC,aAAa,GAAG,IAAI,CAAC/C,SAAS;IACpC,IAAI8C,KAAK,KAAKC,aAAa,EAAE;MACzB,IAAI,CAAC/C,SAAS,GAAG8C,KAAK;MACtB,IAAI,CAACR,cAAc,CAACS,aAAa,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIxH,cAAcA,CAACyH,WAAW,EAAEC,UAAU,EAAE;IACpC;IACA;IACA;IACA,IAAI,CAACA,UAAU,EAAE;MACb,IAAI,CAACpB,SAAS,CAACmB,WAAW,CAAC;IAC/B;EACJ;AAGJ;AAACE,aAAA,GA5NKnD,YAAY;AAAA5B,eAAA,CAAZ4B,YAAY,wBAAAoD,sBAAAvE,iBAAA;EAAA,YAAAA,iBAAA,IA0NqFmB,aAAY;AAAA;AAAA5B,eAAA,CA1N7G4B,YAAY,8BAlD+D5H,EAAE,CAAAiL,iBAAA;EAAAhE,IAAA,EA6QQW,aAAY;EAAAsD,SAAA;EAAAC,SAAA,WAAsjB,OAAO;EAAAC,MAAA;IAAArJ,KAAA;IAAA8F,SAAA,gCAA5cxD,eAAe;IAAArB,MAAA,0BAAgCqB,eAAe;IAAArC,QAAA,8BAAsCqC,eAAe;IAAA+D,eAAA;IAAAW,YAAA,sCAAsFxE,gBAAgB;IAAAyE,oBAAA,sDAA0EzE,gBAAgB;IAAApC,YAAA;IAAAF,QAAA,8BAAoEsC,gBAAgB;EAAA;EAAA8G,OAAA;IAAApF,IAAA;EAAA;EAAAqF,QAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,uBAAAvL,EAAA,EAAAE,GAAA;IAAA,IAAAF,EAAA;MA7Q9gBJ,EAAE,CAAAC,cAAA,YA6QiuB,CAAC,YAA8C,CAAC;MA7QnxBD,EAAE,CAAA2C,UAAA,IAAAD,oCAAA,gBA6Q2yB,CAAC;MA7Q9yB1C,EAAE,CAAAC,cAAA,YA6Q0uE,CAAC,YAAyE,CAAC;MA7QvzED,EAAE,CAAAE,MAAA,EA6Q03E,CAAC;MA7Q73EF,EAAE,CAAAG,YAAA,CA6Qg4E,CAAC;MA7Qn4EH,EAAE,CAAA2C,UAAA,IAAAM,oCAAA,mBA6Q4sF,CAAC;MA7Q/sFjD,EAAE,CAAAC,cAAA,eA6Q+nI,CAAC;MA7QloID,EAAE,CAAAc,UAAA,mBAAA8K,+CAAA;QAAA,OA6Q+rHtL,GAAA,CAAA8C,cAAA,CAAA9C,GAAA,CAAAuH,SAAA,GAA2B,CAAC,EAAEvH,GAAA,CAAA+C,wBAAA,CAAyB,CAAC,CAAC;MAAA,CAAC,CAAC;MA7Q5vHrD,EAAE,CAAAsD,cAAA;MAAFtD,EAAE,CAAAC,cAAA,YA6QwxI,CAAC;MA7Q3xID,EAAE,CAAAuD,SAAA,aA6Q+1I,CAAC;MA7Ql2IvD,EAAE,CAAAG,YAAA,CA6Q+2I,CAAC,CAAgB,CAAC;MA7Qn4IH,EAAE,CAAA6L,eAAA;MAAF7L,EAAE,CAAAC,cAAA,iBA6Q66J,CAAC;MA7Qh7JD,EAAE,CAAAc,UAAA,mBAAAgL,gDAAA;QAAA,OA6QqgJxL,GAAA,CAAA8C,cAAA,CAAA9C,GAAA,CAAAuH,SAAA,GAA2B,CAAC,EAAEvH,GAAA,CAAAuD,oBAAA,CAAqB,CAAC,CAAC;MAAA,CAAC,CAAC;MA7Q9jJ7D,EAAE,CAAAsD,cAAA;MAAFtD,EAAE,CAAAC,cAAA,aA6QskK,CAAC;MA7QzkKD,EAAE,CAAAuD,SAAA,eA6Q8oK,CAAC;MA7QjpKvD,EAAE,CAAAG,YAAA,CA6Q8pK,CAAC,CAAgB,CAAC;MA7QlrKH,EAAE,CAAA2C,UAAA,KAAAc,qCAAA,oBA6QmtK,CAAC;MA7QttKzD,EAAE,CAAAG,YAAA,CA6Q2kM,CAAC,CAAS,CAAC,CAAO,CAAC;IAAA;IAAA,IAAAC,EAAA;MA7QhmMJ,EAAE,CAAAS,SAAA,EA6QmrE,CAAC;MA7QtrET,EAAE,CAAA+C,aAAA,EAAAzC,GAAA,CAAAyI,YAAA,SA6QmrE,CAAC;MA7QtrE/I,EAAE,CAAAS,SAAA,EA6Q03E,CAAC;MA7Q73ET,EAAE,CAAAU,kBAAA,MAAAJ,GAAA,CAAAuC,KAAA,CAAAkJ,aAAA,CAAAzL,GAAA,CAAAuH,SAAA,EAAAvH,GAAA,CAAA0B,QAAA,EAAA1B,GAAA,CAAA0C,MAAA,MA6Q03E,CAAC;MA7Q73EhD,EAAE,CAAAS,SAAA,CA6QsjH,CAAC;MA7QzjHT,EAAE,CAAA+C,aAAA,CAAAzC,GAAA,CAAA0I,oBAAA,SA6QsjH,CAAC;MA7QzjHhJ,EAAE,CAAAS,SAAA,CA6Q82H,CAAC;MA7Qj3HT,EAAE,CAAAQ,UAAA,eAAAF,GAAA,CAAAuC,KAAA,CAAAmJ,iBA6Q82H,CAAC,uBAAA1L,GAAA,CAAA+C,wBAAA,EAAkE,CAAC,aAAA/C,GAAA,CAAA+C,wBAAA,EAAoG,CAAC,aAAA/C,GAAA,CAAA+C,wBAAA,cAAoE,CAAC;MA7Q9lIrD,EAAE,CAAA4C,WAAA,eAAAtC,GAAA,CAAAuC,KAAA,CAAAmJ,iBAAA;MAAFhM,EAAE,CAAAS,SAAA,EA6QwqJ,CAAC;MA7Q3qJT,EAAE,CAAAQ,UAAA,eAAAF,GAAA,CAAAuC,KAAA,CAAAoJ,aA6QwqJ,CAAC,uBAAA3L,GAAA,CAAAuD,oBAAA,EAA8D,CAAC,aAAAvD,GAAA,CAAAuD,oBAAA,EAAgG,CAAC,aAAAvD,GAAA,CAAAuD,oBAAA,cAAgE,CAAC;MA7Q54J7D,EAAE,CAAA4C,WAAA,eAAAtC,GAAA,CAAAuC,KAAA,CAAAoJ,aAAA;MAAFjM,EAAE,CAAAS,SAAA,EA6Q+jM,CAAC;MA7QlkMT,EAAE,CAAA+C,aAAA,CAAAzC,GAAA,CAAA0I,oBAAA,UA6Q+jM,CAAC;IAAA;EAAA;EAAAkD,YAAA,GAAmrFhH,YAAY,EAA4LE,SAAS,EAAogBO,SAAS,EAAqJC,aAAa,EAA6FJ,UAAU;EAAA2G,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAE7yT;EAAA,QAAAtF,SAAA,oBAAAA,SAAA,KA/QiF/G,EAAE,CAAAgH,iBAAA,CA+QQY,YAAY,EAAc,CAAC;IAC1GX,IAAI,EAAEzC,SAAS;IACf0C,IAAI,EAAE,CAAC;MAAEoF,QAAQ,EAAE,eAAe;MAAEhB,QAAQ,EAAE,cAAc;MAAEiB,IAAI,EAAE;QACxD,OAAO,EAAE,mBAAmB;QAC5B,MAAM,EAAE;MACZ,CAAC;MAAEF,eAAe,EAAE5H,uBAAuB,CAAC+H,MAAM;MAAEJ,aAAa,EAAE1H,iBAAiB,CAAC+H,IAAI;MAAEC,OAAO,EAAE,CAACxH,YAAY,EAAEE,SAAS,EAAEO,SAAS,EAAEC,aAAa,EAAEJ,UAAU,CAAC;MAAEkG,QAAQ,EAAE,i7KAAi7K;MAAES,MAAM,EAAE,CAAC,0lFAA0lF;IAAE,CAAC;EACptQ,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEpK,KAAK,EAAE,CAAC;MAChDkF,IAAI,EAAEtC;IACV,CAAC,CAAC;IAAEkD,SAAS,EAAE,CAAC;MACZZ,IAAI,EAAEtC,KAAK;MACXuC,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAEtI;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAErB,MAAM,EAAE,CAAC;MACTiE,IAAI,EAAEtC,KAAK;MACXuC,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAEtI;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAErC,QAAQ,EAAE,CAAC;MACXiF,IAAI,EAAEtC,KAAK;MACXuC,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAEtI;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE+D,eAAe,EAAE,CAAC;MAClBnB,IAAI,EAAEtC;IACV,CAAC,CAAC;IAAEoE,YAAY,EAAE,CAAC;MACf9B,IAAI,EAAEtC,KAAK;MACXuC,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAEpI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyE,oBAAoB,EAAE,CAAC;MACvB/B,IAAI,EAAEtC,KAAK;MACXuC,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAEpI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEpC,YAAY,EAAE,CAAC;MACf8E,IAAI,EAAEtC;IACV,CAAC,CAAC;IAAE1C,QAAQ,EAAE,CAAC;MACXgF,IAAI,EAAEtC,KAAK;MACXuC,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAEpI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0B,IAAI,EAAE,CAAC;MACPgB,IAAI,EAAErC;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgI,kBAAkB,CAAC;AAIxBC,mBAAA,GAJKD,kBAAkB;AAAA5G,eAAA,CAAlB4G,kBAAkB,wBAAAE,4BAAArG,iBAAA;EAAA,YAAAA,iBAAA,IAC+EmG,mBAAkB;AAAA;AAAA5G,eAAA,CADnH4G,kBAAkB,8BAjTyD5M,EAAE,CAAA+M,gBAAA;EAAA9F,IAAA,EAmTqB2F,mBAAkB;EAAAF,OAAA,GAAY7G,eAAe,EAAEP,eAAe,EAAEI,gBAAgB,EAAEkC,YAAY;EAAAoF,OAAA,GAAapF,YAAY;AAAA;AAAA5B,eAAA,CAFzN4G,kBAAkB,8BAjTyD5M,EAAE,CAAAiN,gBAAA;EAAAC,SAAA,EAoToD,CAAC7F,2BAA2B,CAAC;EAAAqF,OAAA,GAAY7G,eAAe,EAAEP,eAAe,EAAEI,gBAAgB,EAAEkC,YAAY;AAAA;AAEhP;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KAtTiF/G,EAAE,CAAAgH,iBAAA,CAsTQ4F,kBAAkB,EAAc,CAAC;IAChH3F,IAAI,EAAEpC,QAAQ;IACdqC,IAAI,EAAE,CAAC;MACCwF,OAAO,EAAE,CAAC7G,eAAe,EAAEP,eAAe,EAAEI,gBAAgB,EAAEkC,YAAY,CAAC;MAC3EoF,OAAO,EAAE,CAACpF,YAAY,CAAC;MACvBsF,SAAS,EAAE,CAAC7F,2BAA2B;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASM,6BAA6B,EAAEN,2BAA2B,EAAEF,mCAAmC,EAAES,YAAY,EAAE9B,gBAAgB,EAAE8G,kBAAkB,EAAElF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}