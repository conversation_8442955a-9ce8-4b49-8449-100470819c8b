{"ast": null, "code": "var _ControlItemsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./control-items.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./control-items.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, Input } from '@angular/core';\nlet ControlItemsComponent = (_ControlItemsComponent = class ControlItemsComponent {\n  constructor() {\n    this.items = [];\n    this.prefixId = '';\n    this.readonly = false;\n    this.submitted = false;\n  }\n  actionAdd(action) {\n    return action || {\n      label: 'LOBBY.THEMES.btnAdd',\n      class: 'pull-right ml-20'\n    };\n  }\n  actionRemove(action) {\n    return action || {\n      fontIcon: {\n        fontSet: 'icomoon',\n        name: 'icon-trash'\n      }\n    };\n  }\n}, _ControlItemsComponent.propDecorators = {\n  items: [{\n    type: Input\n  }],\n  prefixId: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }]\n}, _ControlItemsComponent);\nControlItemsComponent = __decorate([Component({\n  selector: 'lib-control-items',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ControlItemsComponent);\nexport { ControlItemsComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectionStrategy", "Component", "Input", "ControlItemsComponent", "_ControlItemsComponent", "constructor", "items", "prefixId", "readonly", "submitted", "actionAdd", "action", "label", "class", "actionRemove", "fontIcon", "fontSet", "name", "propDecorators", "type", "selector", "template", "changeDetection", "OnPush", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/control-items/control-items.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./control-items.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./control-items.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, Input } from '@angular/core';\nlet ControlItemsComponent = class ControlItemsComponent {\n    constructor() {\n        this.items = [];\n        this.prefixId = '';\n        this.readonly = false;\n        this.submitted = false;\n    }\n    actionAdd(action) {\n        return action || {\n            label: 'LOBBY.THEMES.btnAdd',\n            class: 'pull-right ml-20'\n        };\n    }\n    actionRemove(action) {\n        return action || {\n            fontIcon: {\n                fontSet: 'icomoon',\n                name: 'icon-trash'\n            }\n        };\n    }\n    static { this.propDecorators = {\n        items: [{ type: Input }],\n        prefixId: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }]\n    }; }\n};\nControlItemsComponent = __decorate([\n    Component({\n        selector: 'lib-control-items',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], ControlItemsComponent);\nexport { ControlItemsComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,SAASC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACzE,IAAIC,qBAAqB,IAAAC,sBAAA,GAAG,MAAMD,qBAAqB,CAAC;EACpDE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;EAC1B;EACAC,SAASA,CAACC,MAAM,EAAE;IACd,OAAOA,MAAM,IAAI;MACbC,KAAK,EAAE,qBAAqB;MAC5BC,KAAK,EAAE;IACX,CAAC;EACL;EACAC,YAAYA,CAACH,MAAM,EAAE;IACjB,OAAOA,MAAM,IAAI;MACbI,QAAQ,EAAE;QACNC,OAAO,EAAE,SAAS;QAClBC,IAAI,EAAE;MACV;IACJ,CAAC;EACL;AAOJ,CAAC,EANYb,sBAAA,CAAKc,cAAc,GAAG;EAC3BZ,KAAK,EAAE,CAAC;IAAEa,IAAI,EAAEjB;EAAM,CAAC,CAAC;EACxBK,QAAQ,EAAE,CAAC;IAAEY,IAAI,EAAEjB;EAAM,CAAC,CAAC;EAC3BM,QAAQ,EAAE,CAAC;IAAEW,IAAI,EAAEjB;EAAM,CAAC,CAAC;EAC3BO,SAAS,EAAE,CAAC;IAAEU,IAAI,EAAEjB;EAAM,CAAC;AAC/B,CAAC,EAAAE,sBAAA,CACJ;AACDD,qBAAqB,GAAGN,UAAU,CAAC,CAC/BI,SAAS,CAAC;EACNmB,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAEvB,oBAAoB;EAC9BwB,eAAe,EAAEtB,uBAAuB,CAACuB,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC1B,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEI,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}