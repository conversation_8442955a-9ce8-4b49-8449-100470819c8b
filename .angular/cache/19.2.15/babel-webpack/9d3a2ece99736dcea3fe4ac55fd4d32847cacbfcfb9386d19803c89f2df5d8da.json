{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { HttpHeaders, HttpResponse } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { of } from 'rxjs';\nimport { delay } from 'rxjs/operators';\nimport { users } from './users-example.data';\nfunction filterBy(name, params, list) {\n  const criteria = params.get(name);\n  if (criteria) {\n    return list.filter(row => row[name].indexOf(criteria) !== -1);\n  }\n  return list;\n}\nfunction filter(params, list) {\n  ['username', 'email'].forEach(name => {\n    list = filterBy(name, params, list);\n  });\n  return list;\n}\nfunction sort(params, list) {\n  if (list.length > 0) {\n    const sortBy = params.get('sortBy');\n    const sortOrder = params.get('sortOrder');\n    if (sortBy && sortOrder && sortOrder !== '' && sortBy in list[0]) {\n      const mod = sortOrder.toLowerCase() === 'asc' ? 1 : -1;\n      return list.sort((a, b) => a[sortBy] > b[sortBy] ? mod : -1 * mod);\n    }\n  }\n  return list;\n}\nlet GridDemoService = class GridDemoService {\n  getGridData(params) {\n    const offset = params.get('offset');\n    const limit = params.get('limit');\n    const source = sort(params, filter(params, users));\n    let body = [...source];\n    if (offset && Number(offset) > 0) {\n      body = body.slice(Number(offset));\n    }\n    if (limit && Number(limit) < body.length) {\n      body = body.slice(0, Number(limit));\n    }\n    const headers = new HttpHeaders({\n      'x-paging-limit': limit ? limit.toString() : '20',\n      'x-paging-offset': offset ? offset.toString() : '0',\n      'x-paging-total': source.length.toString()\n    });\n    return of(new HttpResponse({\n      body,\n      headers\n    })).pipe(delay(1500));\n  }\n};\nGridDemoService = __decorate([Injectable()], GridDemoService);\nexport { GridDemoService };", "map": {"version": 3, "names": ["__decorate", "HttpHeaders", "HttpResponse", "Injectable", "of", "delay", "users", "filterBy", "name", "params", "list", "criteria", "get", "filter", "row", "indexOf", "for<PERSON>ach", "sort", "length", "sortBy", "sortOrder", "mod", "toLowerCase", "a", "b", "GridDemoService", "getGridData", "offset", "limit", "source", "body", "Number", "slice", "headers", "toString", "pipe"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/_stories/grid-demo.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { HttpHeaders, HttpResponse } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { of } from 'rxjs';\nimport { delay } from 'rxjs/operators';\nimport { users } from './users-example.data';\nfunction filterBy(name, params, list) {\n    const criteria = params.get(name);\n    if (criteria) {\n        return list.filter(row => row[name].indexOf(criteria) !== -1);\n    }\n    return list;\n}\nfunction filter(params, list) {\n    ['username', 'email'].forEach(name => {\n        list = filterBy(name, params, list);\n    });\n    return list;\n}\nfunction sort(params, list) {\n    if (list.length > 0) {\n        const sortBy = params.get('sortBy');\n        const sortOrder = params.get('sortOrder');\n        if (sortBy && sortOrder && sortOrder !== '' && sortBy in list[0]) {\n            const mod = sortOrder.toLowerCase() === 'asc' ? 1 : -1;\n            return list.sort((a, b) => a[sortBy] > b[sortBy] ? mod : -1 * mod);\n        }\n    }\n    return list;\n}\nlet GridDemoService = class GridDemoService {\n    getGridData(params) {\n        const offset = params.get('offset');\n        const limit = params.get('limit');\n        const source = sort(params, filter(params, users));\n        let body = [...source];\n        if (offset && Number(offset) > 0) {\n            body = body.slice(Number(offset));\n        }\n        if (limit && Number(limit) < body.length) {\n            body = body.slice(0, Number(limit));\n        }\n        const headers = new HttpHeaders({\n            'x-paging-limit': limit ? limit.toString() : '20',\n            'x-paging-offset': offset ? offset.toString() : '0',\n            'x-paging-total': source.length.toString()\n        });\n        return of(new HttpResponse({ body, headers })).pipe(delay(1500));\n    }\n};\nGridDemoService = __decorate([\n    Injectable()\n], GridDemoService);\nexport { GridDemoService };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,WAAW,EAAEC,YAAY,QAAQ,sBAAsB;AAChE,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAE;EAClC,MAAMC,QAAQ,GAAGF,MAAM,CAACG,GAAG,CAACJ,IAAI,CAAC;EACjC,IAAIG,QAAQ,EAAE;IACV,OAAOD,IAAI,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACN,IAAI,CAAC,CAACO,OAAO,CAACJ,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACjE;EACA,OAAOD,IAAI;AACf;AACA,SAASG,MAAMA,CAACJ,MAAM,EAAEC,IAAI,EAAE;EAC1B,CAAC,UAAU,EAAE,OAAO,CAAC,CAACM,OAAO,CAACR,IAAI,IAAI;IAClCE,IAAI,GAAGH,QAAQ,CAACC,IAAI,EAAEC,MAAM,EAAEC,IAAI,CAAC;EACvC,CAAC,CAAC;EACF,OAAOA,IAAI;AACf;AACA,SAASO,IAAIA,CAACR,MAAM,EAAEC,IAAI,EAAE;EACxB,IAAIA,IAAI,CAACQ,MAAM,GAAG,CAAC,EAAE;IACjB,MAAMC,MAAM,GAAGV,MAAM,CAACG,GAAG,CAAC,QAAQ,CAAC;IACnC,MAAMQ,SAAS,GAAGX,MAAM,CAACG,GAAG,CAAC,WAAW,CAAC;IACzC,IAAIO,MAAM,IAAIC,SAAS,IAAIA,SAAS,KAAK,EAAE,IAAID,MAAM,IAAIT,IAAI,CAAC,CAAC,CAAC,EAAE;MAC9D,MAAMW,GAAG,GAAGD,SAAS,CAACE,WAAW,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MACtD,OAAOZ,IAAI,CAACO,IAAI,CAAC,CAACM,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACJ,MAAM,CAAC,GAAGK,CAAC,CAACL,MAAM,CAAC,GAAGE,GAAG,GAAG,CAAC,CAAC,GAAGA,GAAG,CAAC;IACtE;EACJ;EACA,OAAOX,IAAI;AACf;AACA,IAAIe,eAAe,GAAG,MAAMA,eAAe,CAAC;EACxCC,WAAWA,CAACjB,MAAM,EAAE;IAChB,MAAMkB,MAAM,GAAGlB,MAAM,CAACG,GAAG,CAAC,QAAQ,CAAC;IACnC,MAAMgB,KAAK,GAAGnB,MAAM,CAACG,GAAG,CAAC,OAAO,CAAC;IACjC,MAAMiB,MAAM,GAAGZ,IAAI,CAACR,MAAM,EAAEI,MAAM,CAACJ,MAAM,EAAEH,KAAK,CAAC,CAAC;IAClD,IAAIwB,IAAI,GAAG,CAAC,GAAGD,MAAM,CAAC;IACtB,IAAIF,MAAM,IAAII,MAAM,CAACJ,MAAM,CAAC,GAAG,CAAC,EAAE;MAC9BG,IAAI,GAAGA,IAAI,CAACE,KAAK,CAACD,MAAM,CAACJ,MAAM,CAAC,CAAC;IACrC;IACA,IAAIC,KAAK,IAAIG,MAAM,CAACH,KAAK,CAAC,GAAGE,IAAI,CAACZ,MAAM,EAAE;MACtCY,IAAI,GAAGA,IAAI,CAACE,KAAK,CAAC,CAAC,EAAED,MAAM,CAACH,KAAK,CAAC,CAAC;IACvC;IACA,MAAMK,OAAO,GAAG,IAAIhC,WAAW,CAAC;MAC5B,gBAAgB,EAAE2B,KAAK,GAAGA,KAAK,CAACM,QAAQ,CAAC,CAAC,GAAG,IAAI;MACjD,iBAAiB,EAAEP,MAAM,GAAGA,MAAM,CAACO,QAAQ,CAAC,CAAC,GAAG,GAAG;MACnD,gBAAgB,EAAEL,MAAM,CAACX,MAAM,CAACgB,QAAQ,CAAC;IAC7C,CAAC,CAAC;IACF,OAAO9B,EAAE,CAAC,IAAIF,YAAY,CAAC;MAAE4B,IAAI;MAAEG;IAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC9B,KAAK,CAAC,IAAI,CAAC,CAAC;EACpE;AACJ,CAAC;AACDoB,eAAe,GAAGzB,UAAU,CAAC,CACzBG,UAAU,CAAC,CAAC,CACf,EAAEsB,eAAe,CAAC;AACnB,SAASA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}