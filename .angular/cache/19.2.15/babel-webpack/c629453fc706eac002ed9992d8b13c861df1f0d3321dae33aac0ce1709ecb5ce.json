{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiTranslationsManagerComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-translations-manager.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-translations-manager.component.scss?ngResource\";\nimport { Component, EventEmitter, forwardRef, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { UntypedFormArray, UntypedFormControl, UntypedFormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { TranslateService } from '@ngx-translate/core';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { SwuiTranslationsManagerService } from './swui-translations-manager.service';\nlet SwuiTranslationsManagerComponent = (_SwuiTranslationsManagerComponent = class SwuiTranslationsManagerComponent {\n  set languages(val) {\n    this._languages = Array.isArray(val) ? val : [];\n    this.availableLanguages = this._languages;\n  }\n  get languages() {\n    return this._languages.sort((a, b) => {\n      return this.translateService.instant(a.text).localeCompare(this.translateService.instant(b.text));\n    });\n  }\n  set childComponent(value) {\n    this._childFormComponent = value;\n  }\n  get childComponent() {\n    return this._childFormComponent;\n  }\n  set submitted(val) {\n    this._submitted = val !== null && val !== void 0 ? val : false;\n    if (this._submitted) {\n      this.switchToFirstInvalidTab();\n    }\n  }\n  get submitted() {\n    return this._submitted;\n  }\n  set isDisabled(val) {\n    this._isDisabled = val !== null && val !== void 0 ? val : false;\n    this.isRemoveLanguageDisabled = this._isDisabled;\n  }\n  get isDisabled() {\n    return this._isDisabled;\n  }\n  constructor(translateService, translationManagerService) {\n    this.translateService = translateService;\n    this.translationManagerService = translationManagerService;\n    this.withoutDefault = false;\n    this.isRemoveLanguageDisabled = false;\n    this.isOrderingSupported = true;\n    this.addLang = new EventEmitter();\n    this.selectedTabIndex = -1;\n    this.availableLanguages = [];\n    this._submitted = false;\n    this._isDisabled = false;\n    this._languages = [];\n    this._onChange = () => {};\n    this.destroyed$ = new Subject();\n    this.onTouched = () => {};\n    this.form = new UntypedFormGroup({\n      info: new UntypedFormArray([])\n    });\n  }\n  ngOnInit() {\n    this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(value => {\n      if (this._onChange) {\n        this._onChange(this.transformForm(value));\n      }\n      this.form.markAllAsTouched();\n      this.onTouched();\n    });\n    this.listenLanguages();\n    if (!this.withoutDefault) {\n      var _this$childComponent;\n      this.infoArray.push(this.initLangGroup({\n        id: 'en'\n      }), {\n        emitEvent: false\n      });\n      this.availableLanguages = this.availableLanguages.filter(({\n        id\n      }) => id !== 'en');\n      this.selectedTabIndex = this.controls.length - 1;\n      (_this$childComponent = this.childComponent) === null || _this$childComponent === void 0 || _this$childComponent.setValue({\n        id: 'en'\n      });\n    }\n    const child = this.childComponent;\n    if (child) {\n      child.form.valueChanges.pipe(filter(val => !!val), takeUntil(this.destroyed$)).subscribe(val => {\n        const childControl = this.controls.find(control => {\n          var _control$get;\n          return ((_control$get = control.get('id')) === null || _control$get === void 0 ? void 0 : _control$get.value) === val.id;\n        });\n        if (childControl) {\n          childControl.patchValue(val);\n        }\n      });\n      child.form.statusChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n        if (this.selectedTabIndex !== -1 && this.controls.length && this.controls[this.selectedTabIndex]) {\n          const errors = child.form.invalid ? {\n            invalid: true\n          } : null;\n          this.controls[this.selectedTabIndex].setErrors(errors);\n        }\n        if (child.form.touched) {\n          this.onTouched();\n          if (this.selectedTabIndex !== -1 && this.controls.length && this.controls[this.selectedTabIndex]) {\n            this.controls[this.selectedTabIndex].markAsTouched();\n          }\n        }\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  ngAfterViewChecked() {\n    if (this.tabsRef) {\n      this.tabsRef.realignInkBar();\n    }\n  }\n  get controls() {\n    return this.infoArray.controls;\n  }\n  writeValue(value) {\n    if (typeof value === 'undefined' || value === null || value && !Object.keys(value).length) {\n      return;\n    }\n    const processedValue = Object.entries(value).map(([id, item]) => _objectSpread(_objectSpread({}, item), {}, {\n      id\n    }));\n    if (this.isOrderingSupported) {\n      processedValue.sort((a, b) => a.order - b.order);\n    }\n    this.availableLanguages = this._languages;\n    this.infoArray.clear({\n      emitEvent: false\n    });\n    processedValue.forEach(val => {\n      this.availableLanguages = this.availableLanguages.filter(({\n        id\n      }) => id !== val.id);\n      this.infoArray.push(this.initLangGroup(val), {\n        emitEvent: false\n      });\n    });\n    this.form.patchValue({\n      info: processedValue\n    }, {\n      emitEvent: false\n    });\n    this.onSelectedIndexChange(0);\n    this.form.markAsPristine();\n    this.form.markAsUntouched();\n  }\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    isDisabled ? this.form.disable({\n      emitEvent: false\n    }) : this.form.enable({\n      emitEvent: false\n    });\n  }\n  validate() {\n    return this.form.valid && this.controls.length ? null : {\n      invalidForm: {\n        valid: false\n      }\n    };\n  }\n  addTab(lang) {\n    this.infoArray.push(this.initLangGroup({\n      id: lang\n    }));\n    this.availableLanguages = this.availableLanguages.filter(({\n      id\n    }) => id !== lang);\n    this.selectedTabIndex = this.controls.length - 1;\n    if (this.translationManagerService) {\n      this.translationManagerService.addLanguages([lang]);\n    }\n    this.addLang.emit(this.controls[this.selectedTabIndex]);\n  }\n  removeTab(index) {\n    if (!this.isDisabled) {\n      const indexToRemove = index !== null && index !== void 0 ? index : this.selectedTabIndex;\n      const control = this.controls[indexToRemove].get('id');\n      const removedLang = control === null || control === void 0 ? void 0 : control.value;\n      const langSet = new Set(this.availableLanguages.map(({\n        id\n      }) => id));\n      if (removedLang) {\n        langSet.add(removedLang);\n      }\n      this.availableLanguages = this.languages.reduce((result, option) => {\n        if (langSet.has(option.id)) {\n          result.push(option);\n        }\n        return result;\n      }, []);\n      if (this.selectedTabIndex === indexToRemove) {\n        if (this.tabsRef && (this.selectedTabIndex === this.tabsRef._tabs.length - 2 || this.availableLanguages.length === 1)) {\n          this.selectedTabIndex = indexToRemove - 1;\n        } else {\n          var _this$childComponent2;\n          const val = this.infoArray.at(indexToRemove + 1) ? this.infoArray.at(indexToRemove + 1).value : null;\n          (_this$childComponent2 = this.childComponent) === null || _this$childComponent2 === void 0 || _this$childComponent2.setValue(val);\n        }\n      }\n      this.infoArray.removeAt(indexToRemove);\n      if (index === undefined && this.translationManagerService) {\n        this.translationManagerService.removeLanguage(removedLang);\n      }\n    }\n  }\n  prevent(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  onSelectedIndexChange(index) {\n    this.selectedTabIndex = index;\n    if (index !== -1 && this.infoArray.at(index).value && this.childComponent) {\n      var _this$infoArray$at;\n      this.childComponent.setValue((_this$infoArray$at = this.infoArray.at(index)) === null || _this$infoArray$at === void 0 ? void 0 : _this$infoArray$at.value);\n    }\n  }\n  trackByFn(_, control) {\n    return control.value.id;\n  }\n  get infoArray() {\n    return this.form.get('info');\n  }\n  transformForm(form) {\n    var _form$info;\n    return ((_form$info = form.info) !== null && _form$info !== void 0 ? _form$info : {}).reduce((result, item, order) => {\n      const processedItem = _objectSpread(_objectSpread({}, item), this.isOrderingSupported ? {\n        order\n      } : {});\n      delete processedItem.id;\n      return _objectSpread(_objectSpread({}, result), {}, {\n        [item.id]: processedItem\n      });\n    }, {});\n  }\n  initLangGroup(val) {\n    return this.childComponent ? this.childComponent.initForm(val) : new UntypedFormGroup({\n      id: new UntypedFormControl(val.id)\n    });\n  }\n  switchToFirstInvalidTab() {\n    const firstInvalid = this.controls.findIndex(({\n      status\n    }) => status === 'INVALID');\n    if (this.tabsRef && firstInvalid !== -1) {\n      this.tabsRef.selectedIndex = firstInvalid;\n    }\n  }\n  listenLanguages() {\n    if (this.translationManagerService) {\n      this.translationManagerService.languages$.pipe(takeUntil(this.destroyed$)).subscribe(languages => {\n        const indexes = this.controls.reduce((res, {\n          value\n        }, index) => {\n          if (!languages.includes(value.id)) {\n            res.unshift(index);\n          }\n          return res;\n        }, []);\n        indexes.forEach(index => {\n          this.removeTab(index);\n        });\n        languages.forEach(language => {\n          const availableLanguages = this.availableLanguages.map(({\n            id\n          }) => id);\n          if (availableLanguages.includes(language)) {\n            this.addTab(language);\n          }\n        });\n      });\n    }\n  }\n}, _SwuiTranslationsManagerComponent.ctorParameters = () => [{\n  type: TranslateService\n}, {\n  type: SwuiTranslationsManagerService,\n  decorators: [{\n    type: Optional\n  }]\n}], _SwuiTranslationsManagerComponent.propDecorators = {\n  languages: [{\n    type: Input\n  }],\n  childComponent: [{\n    type: Input,\n    args: ['component']\n  }],\n  submitted: [{\n    type: Input\n  }],\n  isDisabled: [{\n    type: Input\n  }],\n  withoutDefault: [{\n    type: Input\n  }],\n  isRemoveLanguageDisabled: [{\n    type: Input\n  }],\n  isOrderingSupported: [{\n    type: Input\n  }],\n  addLang: [{\n    type: Output\n  }],\n  tabsRef: [{\n    type: ViewChild,\n    args: ['tabSet', {\n      static: true\n    }]\n  }]\n}, _SwuiTranslationsManagerComponent);\nSwuiTranslationsManagerComponent = __decorate([Component({\n  selector: 'lib-swui-translations-manager',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SwuiTranslationsManagerComponent),\n    multi: true\n  }, {\n    provide: NG_VALIDATORS,\n    useExisting: forwardRef(() => SwuiTranslationsManagerComponent),\n    multi: true\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTranslationsManagerComponent);\nexport { SwuiTranslationsManagerComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "EventEmitter", "forwardRef", "Input", "Optional", "Output", "ViewChild", "UntypedFormArray", "UntypedFormControl", "UntypedFormGroup", "NG_VALIDATORS", "NG_VALUE_ACCESSOR", "TranslateService", "Subject", "filter", "takeUntil", "SwuiTranslationsManagerService", "SwuiTranslationsManagerComponent", "_SwuiTranslationsManagerComponent", "languages", "val", "_languages", "Array", "isArray", "availableLanguages", "sort", "a", "b", "translateService", "instant", "text", "localeCompare", "childComponent", "value", "_childFormComponent", "submitted", "_submitted", "switchToFirstInvalidTab", "isDisabled", "_isDisabled", "isRemoveLanguageDisabled", "constructor", "translationManagerService", "<PERSON><PERSON><PERSON><PERSON>", "isOrderingSupported", "addLang", "selectedTabIndex", "_onChange", "destroyed$", "onTouched", "form", "info", "ngOnInit", "valueChanges", "pipe", "subscribe", "transformForm", "mark<PERSON>llAsTouched", "listenLanguages", "_this$childComponent", "infoArray", "push", "initLangGroup", "id", "emitEvent", "controls", "length", "setValue", "child", "childControl", "find", "control", "_control$get", "get", "patchValue", "statusChanges", "errors", "invalid", "setErrors", "touched", "<PERSON><PERSON><PERSON><PERSON>ched", "ngOnDestroy", "next", "undefined", "complete", "ngAfterViewChecked", "tabsRef", "realignInkBar", "writeValue", "Object", "keys", "processedValue", "entries", "map", "item", "_objectSpread", "order", "clear", "for<PERSON>ach", "onSelectedIndexChange", "mark<PERSON><PERSON>ristine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "disable", "enable", "validate", "valid", "invalidForm", "addTab", "lang", "addLanguages", "emit", "removeTab", "index", "indexToRemove", "removedLang", "langSet", "Set", "add", "reduce", "result", "option", "has", "_tabs", "_this$childComponent2", "at", "removeAt", "removeLanguage", "prevent", "event", "preventDefault", "stopPropagation", "_this$infoArray$at", "trackByFn", "_", "_form$info", "processedItem", "initForm", "firstInvalid", "findIndex", "status", "selectedIndex", "languages$", "indexes", "res", "includes", "unshift", "language", "ctorParameters", "type", "decorators", "propDecorators", "args", "static", "selector", "template", "providers", "provide", "useExisting", "multi", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-translations-manager/swui-translations-manager.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-translations-manager.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-translations-manager.component.scss?ngResource\";\nimport { Component, EventEmitter, forwardRef, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { UntypedFormArray, UntypedFormControl, UntypedFormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { TranslateService } from '@ngx-translate/core';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { SwuiTranslationsManagerService } from './swui-translations-manager.service';\nlet SwuiTranslationsManagerComponent = class SwuiTranslationsManagerComponent {\n    set languages(val) {\n        this._languages = Array.isArray(val) ? val : [];\n        this.availableLanguages = this._languages;\n    }\n    get languages() {\n        return this._languages.sort((a, b) => {\n            return this.translateService.instant(a.text).localeCompare(this.translateService.instant(b.text));\n        });\n    }\n    set childComponent(value) {\n        this._childFormComponent = value;\n    }\n    get childComponent() {\n        return this._childFormComponent;\n    }\n    set submitted(val) {\n        this._submitted = val ?? false;\n        if (this._submitted) {\n            this.switchToFirstInvalidTab();\n        }\n    }\n    get submitted() {\n        return this._submitted;\n    }\n    set isDisabled(val) {\n        this._isDisabled = val ?? false;\n        this.isRemoveLanguageDisabled = this._isDisabled;\n    }\n    get isDisabled() {\n        return this._isDisabled;\n    }\n    constructor(translateService, translationManagerService) {\n        this.translateService = translateService;\n        this.translationManagerService = translationManagerService;\n        this.withoutDefault = false;\n        this.isRemoveLanguageDisabled = false;\n        this.isOrderingSupported = true;\n        this.addLang = new EventEmitter();\n        this.selectedTabIndex = -1;\n        this.availableLanguages = [];\n        this._submitted = false;\n        this._isDisabled = false;\n        this._languages = [];\n        this._onChange = (() => {\n        });\n        this.destroyed$ = new Subject();\n        this.onTouched = () => {\n        };\n        this.form = new UntypedFormGroup({\n            info: new UntypedFormArray([])\n        });\n    }\n    ngOnInit() {\n        this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(value => {\n            if (this._onChange) {\n                this._onChange(this.transformForm(value));\n            }\n            this.form.markAllAsTouched();\n            this.onTouched();\n        });\n        this.listenLanguages();\n        if (!this.withoutDefault) {\n            this.infoArray.push(this.initLangGroup({ id: 'en' }), { emitEvent: false });\n            this.availableLanguages = this.availableLanguages.filter(({ id }) => id !== 'en');\n            this.selectedTabIndex = this.controls.length - 1;\n            this.childComponent?.setValue({ id: 'en' });\n        }\n        const child = this.childComponent;\n        if (child) {\n            child.form.valueChanges.pipe(filter(val => !!val), takeUntil(this.destroyed$)).subscribe((val) => {\n                const childControl = this.controls.find((control) => control.get('id')?.value === val.id);\n                if (childControl) {\n                    childControl.patchValue(val);\n                }\n            });\n            child.form.statusChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n                if (this.selectedTabIndex !== -1 && this.controls.length && this.controls[this.selectedTabIndex]) {\n                    const errors = child.form.invalid ? { invalid: true } : null;\n                    this.controls[this.selectedTabIndex].setErrors(errors);\n                }\n                if (child.form.touched) {\n                    this.onTouched();\n                    if (this.selectedTabIndex !== -1 && this.controls.length && this.controls[this.selectedTabIndex]) {\n                        this.controls[this.selectedTabIndex].markAsTouched();\n                    }\n                }\n            });\n        }\n    }\n    ngOnDestroy() {\n        this.destroyed$.next(undefined);\n        this.destroyed$.complete();\n    }\n    ngAfterViewChecked() {\n        if (this.tabsRef) {\n            this.tabsRef.realignInkBar();\n        }\n    }\n    get controls() {\n        return this.infoArray.controls;\n    }\n    writeValue(value) {\n        if (typeof value === 'undefined' || value === null || (value && !Object.keys(value).length)) {\n            return;\n        }\n        const processedValue = Object.entries(value).map(([id, item]) => ({ ...item, id }));\n        if (this.isOrderingSupported) {\n            processedValue.sort((a, b) => a.order - b.order);\n        }\n        this.availableLanguages = this._languages;\n        this.infoArray.clear({ emitEvent: false });\n        processedValue.forEach(val => {\n            this.availableLanguages = this.availableLanguages.filter(({ id }) => id !== val.id);\n            this.infoArray.push(this.initLangGroup(val), { emitEvent: false });\n        });\n        this.form.patchValue({ info: processedValue }, { emitEvent: false });\n        this.onSelectedIndexChange(0);\n        this.form.markAsPristine();\n        this.form.markAsUntouched();\n    }\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(isDisabled) {\n        isDisabled ? this.form.disable({ emitEvent: false }) : this.form.enable({ emitEvent: false });\n    }\n    validate() {\n        return this.form.valid && this.controls.length ? null : { invalidForm: { valid: false } };\n    }\n    addTab(lang) {\n        this.infoArray.push(this.initLangGroup({ id: lang }));\n        this.availableLanguages = this.availableLanguages.filter(({ id }) => id !== lang);\n        this.selectedTabIndex = this.controls.length - 1;\n        if (this.translationManagerService) {\n            this.translationManagerService.addLanguages([lang]);\n        }\n        this.addLang.emit(this.controls[this.selectedTabIndex]);\n    }\n    removeTab(index) {\n        if (!this.isDisabled) {\n            const indexToRemove = index ?? this.selectedTabIndex;\n            const control = this.controls[indexToRemove].get('id');\n            const removedLang = control?.value;\n            const langSet = new Set(this.availableLanguages.map(({ id }) => id));\n            if (removedLang) {\n                langSet.add(removedLang);\n            }\n            this.availableLanguages = this.languages.reduce((result, option) => {\n                if (langSet.has(option.id)) {\n                    result.push(option);\n                }\n                return result;\n            }, []);\n            if (this.selectedTabIndex === indexToRemove) {\n                if (this.tabsRef && (this.selectedTabIndex === this.tabsRef._tabs.length - 2 || this.availableLanguages.length === 1)) {\n                    this.selectedTabIndex = indexToRemove - 1;\n                }\n                else {\n                    const val = this.infoArray.at(indexToRemove + 1) ? this.infoArray.at(indexToRemove + 1).value : null;\n                    this.childComponent?.setValue(val);\n                }\n            }\n            this.infoArray.removeAt(indexToRemove);\n            if (index === undefined && this.translationManagerService) {\n                this.translationManagerService.removeLanguage(removedLang);\n            }\n        }\n    }\n    prevent(event) {\n        event.preventDefault();\n        event.stopPropagation();\n    }\n    onSelectedIndexChange(index) {\n        this.selectedTabIndex = index;\n        if (index !== -1 && this.infoArray.at(index).value && this.childComponent) {\n            this.childComponent.setValue(this.infoArray.at(index)?.value);\n        }\n    }\n    trackByFn(_, control) {\n        return control.value.id;\n    }\n    get infoArray() {\n        return this.form.get('info');\n    }\n    transformForm(form) {\n        return (form.info ?? {}).reduce((result, item, order) => {\n            const processedItem = {\n                ...item,\n                ...(this.isOrderingSupported ? { order } : {})\n            };\n            delete processedItem.id;\n            return {\n                ...result,\n                [item.id]: processedItem\n            };\n        }, {});\n    }\n    initLangGroup(val) {\n        return this.childComponent ? this.childComponent.initForm(val) : new UntypedFormGroup({ id: new UntypedFormControl(val.id) });\n    }\n    switchToFirstInvalidTab() {\n        const firstInvalid = this.controls.findIndex(({ status }) => status === 'INVALID');\n        if (this.tabsRef && firstInvalid !== -1) {\n            this.tabsRef.selectedIndex = firstInvalid;\n        }\n    }\n    listenLanguages() {\n        if (this.translationManagerService) {\n            this.translationManagerService.languages$.pipe(takeUntil(this.destroyed$)).subscribe(languages => {\n                const indexes = this.controls.reduce((res, { value }, index) => {\n                    if (!languages.includes(value.id)) {\n                        res.unshift(index);\n                    }\n                    return res;\n                }, []);\n                indexes.forEach(index => {\n                    this.removeTab(index);\n                });\n                languages.forEach(language => {\n                    const availableLanguages = this.availableLanguages.map(({ id }) => id);\n                    if (availableLanguages.includes(language)) {\n                        this.addTab(language);\n                    }\n                });\n            });\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: TranslateService },\n        { type: SwuiTranslationsManagerService, decorators: [{ type: Optional }] }\n    ]; }\n    static { this.propDecorators = {\n        languages: [{ type: Input }],\n        childComponent: [{ type: Input, args: ['component',] }],\n        submitted: [{ type: Input }],\n        isDisabled: [{ type: Input }],\n        withoutDefault: [{ type: Input }],\n        isRemoveLanguageDisabled: [{ type: Input }],\n        isOrderingSupported: [{ type: Input }],\n        addLang: [{ type: Output }],\n        tabsRef: [{ type: ViewChild, args: ['tabSet', { static: true },] }]\n    }; }\n};\nSwuiTranslationsManagerComponent = __decorate([\n    Component({\n        selector: 'lib-swui-translations-manager',\n        template: __NG_CLI_RESOURCE__0,\n        providers: [\n            {\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => SwuiTranslationsManagerComponent),\n                multi: true\n            },\n            {\n                provide: NG_VALIDATORS,\n                useExisting: forwardRef(() => SwuiTranslationsManagerComponent),\n                multi: true\n            }\n        ],\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTranslationsManagerComponent);\nexport { SwuiTranslationsManagerComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,uDAAuD;AACxF,OAAOC,oBAAoB,MAAM,uDAAuD;AACxF,SAASC,SAAS,EAAEC,YAAY,EAAEC,UAAU,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACvG,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,iBAAiB,QAAQ,gBAAgB;AACzH,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAClD,SAASC,8BAA8B,QAAQ,qCAAqC;AACpF,IAAIC,gCAAgC,IAAAC,iCAAA,GAAG,MAAMD,gCAAgC,CAAC;EAC1E,IAAIE,SAASA,CAACC,GAAG,EAAE;IACf,IAAI,CAACC,UAAU,GAAGC,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;IAC/C,IAAI,CAACI,kBAAkB,GAAG,IAAI,CAACH,UAAU;EAC7C;EACA,IAAIF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACE,UAAU,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAClC,OAAO,IAAI,CAACC,gBAAgB,CAACC,OAAO,CAACH,CAAC,CAACI,IAAI,CAAC,CAACC,aAAa,CAAC,IAAI,CAACH,gBAAgB,CAACC,OAAO,CAACF,CAAC,CAACG,IAAI,CAAC,CAAC;IACrG,CAAC,CAAC;EACN;EACA,IAAIE,cAAcA,CAACC,KAAK,EAAE;IACtB,IAAI,CAACC,mBAAmB,GAAGD,KAAK;EACpC;EACA,IAAID,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACE,mBAAmB;EACnC;EACA,IAAIC,SAASA,CAACf,GAAG,EAAE;IACf,IAAI,CAACgB,UAAU,GAAGhB,GAAG,aAAHA,GAAG,cAAHA,GAAG,GAAI,KAAK;IAC9B,IAAI,IAAI,CAACgB,UAAU,EAAE;MACjB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAClC;EACJ;EACA,IAAIF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAIE,UAAUA,CAAClB,GAAG,EAAE;IAChB,IAAI,CAACmB,WAAW,GAAGnB,GAAG,aAAHA,GAAG,cAAHA,GAAG,GAAI,KAAK;IAC/B,IAAI,CAACoB,wBAAwB,GAAG,IAAI,CAACD,WAAW;EACpD;EACA,IAAID,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACAE,WAAWA,CAACb,gBAAgB,EAAEc,yBAAyB,EAAE;IACrD,IAAI,CAACd,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACc,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACH,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACI,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI5C,YAAY,CAAC,CAAC;IACjC,IAAI,CAAC6C,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACtB,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACY,UAAU,GAAG,KAAK;IACvB,IAAI,CAACG,WAAW,GAAG,KAAK;IACxB,IAAI,CAAClB,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC0B,SAAS,GAAI,MAAM,CACxB,CAAE;IACF,IAAI,CAACC,UAAU,GAAG,IAAInC,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACoC,SAAS,GAAG,MAAM,CACvB,CAAC;IACD,IAAI,CAACC,IAAI,GAAG,IAAIzC,gBAAgB,CAAC;MAC7B0C,IAAI,EAAE,IAAI5C,gBAAgB,CAAC,EAAE;IACjC,CAAC,CAAC;EACN;EACA6C,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,IAAI,CAACG,YAAY,CAACC,IAAI,CAACvC,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAAC,CAACO,SAAS,CAACtB,KAAK,IAAI;MACvE,IAAI,IAAI,CAACc,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAAC,IAAI,CAACS,aAAa,CAACvB,KAAK,CAAC,CAAC;MAC7C;MACA,IAAI,CAACiB,IAAI,CAACO,gBAAgB,CAAC,CAAC;MAC5B,IAAI,CAACR,SAAS,CAAC,CAAC;IACpB,CAAC,CAAC;IACF,IAAI,CAACS,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;MAAA,IAAAgB,oBAAA;MACtB,IAAI,CAACC,SAAS,CAACC,IAAI,CAAC,IAAI,CAACC,aAAa,CAAC;QAAEC,EAAE,EAAE;MAAK,CAAC,CAAC,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAC3E,IAAI,CAACxC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACV,MAAM,CAAC,CAAC;QAAEiD;MAAG,CAAC,KAAKA,EAAE,KAAK,IAAI,CAAC;MACjF,IAAI,CAACjB,gBAAgB,GAAG,IAAI,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC;MAChD,CAAAP,oBAAA,OAAI,CAAC3B,cAAc,cAAA2B,oBAAA,eAAnBA,oBAAA,CAAqBQ,QAAQ,CAAC;QAAEJ,EAAE,EAAE;MAAK,CAAC,CAAC;IAC/C;IACA,MAAMK,KAAK,GAAG,IAAI,CAACpC,cAAc;IACjC,IAAIoC,KAAK,EAAE;MACPA,KAAK,CAAClB,IAAI,CAACG,YAAY,CAACC,IAAI,CAACxC,MAAM,CAACM,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC,EAAEL,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAAC,CAACO,SAAS,CAAEnC,GAAG,IAAK;QAC9F,MAAMiD,YAAY,GAAG,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAEC,OAAO;UAAA,IAAAC,YAAA;UAAA,OAAK,EAAAA,YAAA,GAAAD,OAAO,CAACE,GAAG,CAAC,IAAI,CAAC,cAAAD,YAAA,uBAAjBA,YAAA,CAAmBvC,KAAK,MAAKb,GAAG,CAAC2C,EAAE;QAAA,EAAC;QACzF,IAAIM,YAAY,EAAE;UACdA,YAAY,CAACK,UAAU,CAACtD,GAAG,CAAC;QAChC;MACJ,CAAC,CAAC;MACFgD,KAAK,CAAClB,IAAI,CAACyB,aAAa,CAACrB,IAAI,CAACvC,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAAC,CAACO,SAAS,CAAC,MAAM;QACtE,IAAI,IAAI,CAACT,gBAAgB,KAAK,CAAC,CAAC,IAAI,IAAI,CAACmB,QAAQ,CAACC,MAAM,IAAI,IAAI,CAACD,QAAQ,CAAC,IAAI,CAACnB,gBAAgB,CAAC,EAAE;UAC9F,MAAM8B,MAAM,GAAGR,KAAK,CAAClB,IAAI,CAAC2B,OAAO,GAAG;YAAEA,OAAO,EAAE;UAAK,CAAC,GAAG,IAAI;UAC5D,IAAI,CAACZ,QAAQ,CAAC,IAAI,CAACnB,gBAAgB,CAAC,CAACgC,SAAS,CAACF,MAAM,CAAC;QAC1D;QACA,IAAIR,KAAK,CAAClB,IAAI,CAAC6B,OAAO,EAAE;UACpB,IAAI,CAAC9B,SAAS,CAAC,CAAC;UAChB,IAAI,IAAI,CAACH,gBAAgB,KAAK,CAAC,CAAC,IAAI,IAAI,CAACmB,QAAQ,CAACC,MAAM,IAAI,IAAI,CAACD,QAAQ,CAAC,IAAI,CAACnB,gBAAgB,CAAC,EAAE;YAC9F,IAAI,CAACmB,QAAQ,CAAC,IAAI,CAACnB,gBAAgB,CAAC,CAACkC,aAAa,CAAC,CAAC;UACxD;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjC,UAAU,CAACkC,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACnC,UAAU,CAACoC,QAAQ,CAAC,CAAC;EAC9B;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,aAAa,CAAC,CAAC;IAChC;EACJ;EACA,IAAItB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACL,SAAS,CAACK,QAAQ;EAClC;EACAuB,UAAUA,CAACvD,KAAK,EAAE;IACd,IAAI,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,IAAKA,KAAK,IAAI,CAACwD,MAAM,CAACC,IAAI,CAACzD,KAAK,CAAC,CAACiC,MAAO,EAAE;MACzF;IACJ;IACA,MAAMyB,cAAc,GAAGF,MAAM,CAACG,OAAO,CAAC3D,KAAK,CAAC,CAAC4D,GAAG,CAAC,CAAC,CAAC9B,EAAE,EAAE+B,IAAI,CAAC,KAAAC,aAAA,CAAAA,aAAA,KAAWD,IAAI;MAAE/B;IAAE,EAAG,CAAC;IACnF,IAAI,IAAI,CAACnB,mBAAmB,EAAE;MAC1B+C,cAAc,CAAClE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACsE,KAAK,GAAGrE,CAAC,CAACqE,KAAK,CAAC;IACpD;IACA,IAAI,CAACxE,kBAAkB,GAAG,IAAI,CAACH,UAAU;IACzC,IAAI,CAACuC,SAAS,CAACqC,KAAK,CAAC;MAAEjC,SAAS,EAAE;IAAM,CAAC,CAAC;IAC1C2B,cAAc,CAACO,OAAO,CAAC9E,GAAG,IAAI;MAC1B,IAAI,CAACI,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACV,MAAM,CAAC,CAAC;QAAEiD;MAAG,CAAC,KAAKA,EAAE,KAAK3C,GAAG,CAAC2C,EAAE,CAAC;MACnF,IAAI,CAACH,SAAS,CAACC,IAAI,CAAC,IAAI,CAACC,aAAa,CAAC1C,GAAG,CAAC,EAAE;QAAE4C,SAAS,EAAE;MAAM,CAAC,CAAC;IACtE,CAAC,CAAC;IACF,IAAI,CAACd,IAAI,CAACwB,UAAU,CAAC;MAAEvB,IAAI,EAAEwC;IAAe,CAAC,EAAE;MAAE3B,SAAS,EAAE;IAAM,CAAC,CAAC;IACpE,IAAI,CAACmC,qBAAqB,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACjD,IAAI,CAACkD,cAAc,CAAC,CAAC;IAC1B,IAAI,CAAClD,IAAI,CAACmD,eAAe,CAAC,CAAC;EAC/B;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACxD,SAAS,GAAGwD,EAAE;EACvB;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACtD,SAAS,GAAGsD,EAAE;EACvB;EACAE,gBAAgBA,CAACnE,UAAU,EAAE;IACzBA,UAAU,GAAG,IAAI,CAACY,IAAI,CAACwD,OAAO,CAAC;MAAE1C,SAAS,EAAE;IAAM,CAAC,CAAC,GAAG,IAAI,CAACd,IAAI,CAACyD,MAAM,CAAC;MAAE3C,SAAS,EAAE;IAAM,CAAC,CAAC;EACjG;EACA4C,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC1D,IAAI,CAAC2D,KAAK,IAAI,IAAI,CAAC5C,QAAQ,CAACC,MAAM,GAAG,IAAI,GAAG;MAAE4C,WAAW,EAAE;QAAED,KAAK,EAAE;MAAM;IAAE,CAAC;EAC7F;EACAE,MAAMA,CAACC,IAAI,EAAE;IACT,IAAI,CAACpD,SAAS,CAACC,IAAI,CAAC,IAAI,CAACC,aAAa,CAAC;MAAEC,EAAE,EAAEiD;IAAK,CAAC,CAAC,CAAC;IACrD,IAAI,CAACxF,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACV,MAAM,CAAC,CAAC;MAAEiD;IAAG,CAAC,KAAKA,EAAE,KAAKiD,IAAI,CAAC;IACjF,IAAI,CAAClE,gBAAgB,GAAG,IAAI,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC;IAChD,IAAI,IAAI,CAACxB,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACuE,YAAY,CAAC,CAACD,IAAI,CAAC,CAAC;IACvD;IACA,IAAI,CAACnE,OAAO,CAACqE,IAAI,CAAC,IAAI,CAACjD,QAAQ,CAAC,IAAI,CAACnB,gBAAgB,CAAC,CAAC;EAC3D;EACAqE,SAASA,CAACC,KAAK,EAAE;IACb,IAAI,CAAC,IAAI,CAAC9E,UAAU,EAAE;MAClB,MAAM+E,aAAa,GAAGD,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,IAAI,CAACtE,gBAAgB;MACpD,MAAMyB,OAAO,GAAG,IAAI,CAACN,QAAQ,CAACoD,aAAa,CAAC,CAAC5C,GAAG,CAAC,IAAI,CAAC;MACtD,MAAM6C,WAAW,GAAG/C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEtC,KAAK;MAClC,MAAMsF,OAAO,GAAG,IAAIC,GAAG,CAAC,IAAI,CAAChG,kBAAkB,CAACqE,GAAG,CAAC,CAAC;QAAE9B;MAAG,CAAC,KAAKA,EAAE,CAAC,CAAC;MACpE,IAAIuD,WAAW,EAAE;QACbC,OAAO,CAACE,GAAG,CAACH,WAAW,CAAC;MAC5B;MACA,IAAI,CAAC9F,kBAAkB,GAAG,IAAI,CAACL,SAAS,CAACuG,MAAM,CAAC,CAACC,MAAM,EAAEC,MAAM,KAAK;QAChE,IAAIL,OAAO,CAACM,GAAG,CAACD,MAAM,CAAC7D,EAAE,CAAC,EAAE;UACxB4D,MAAM,CAAC9D,IAAI,CAAC+D,MAAM,CAAC;QACvB;QACA,OAAOD,MAAM;MACjB,CAAC,EAAE,EAAE,CAAC;MACN,IAAI,IAAI,CAAC7E,gBAAgB,KAAKuE,aAAa,EAAE;QACzC,IAAI,IAAI,CAAC/B,OAAO,KAAK,IAAI,CAACxC,gBAAgB,KAAK,IAAI,CAACwC,OAAO,CAACwC,KAAK,CAAC5D,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC1C,kBAAkB,CAAC0C,MAAM,KAAK,CAAC,CAAC,EAAE;UACnH,IAAI,CAACpB,gBAAgB,GAAGuE,aAAa,GAAG,CAAC;QAC7C,CAAC,MACI;UAAA,IAAAU,qBAAA;UACD,MAAM3G,GAAG,GAAG,IAAI,CAACwC,SAAS,CAACoE,EAAE,CAACX,aAAa,GAAG,CAAC,CAAC,GAAG,IAAI,CAACzD,SAAS,CAACoE,EAAE,CAACX,aAAa,GAAG,CAAC,CAAC,CAACpF,KAAK,GAAG,IAAI;UACpG,CAAA8F,qBAAA,OAAI,CAAC/F,cAAc,cAAA+F,qBAAA,eAAnBA,qBAAA,CAAqB5D,QAAQ,CAAC/C,GAAG,CAAC;QACtC;MACJ;MACA,IAAI,CAACwC,SAAS,CAACqE,QAAQ,CAACZ,aAAa,CAAC;MACtC,IAAID,KAAK,KAAKjC,SAAS,IAAI,IAAI,CAACzC,yBAAyB,EAAE;QACvD,IAAI,CAACA,yBAAyB,CAACwF,cAAc,CAACZ,WAAW,CAAC;MAC9D;IACJ;EACJ;EACAa,OAAOA,CAACC,KAAK,EAAE;IACXA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACAnC,qBAAqBA,CAACiB,KAAK,EAAE;IACzB,IAAI,CAACtE,gBAAgB,GAAGsE,KAAK;IAC7B,IAAIA,KAAK,KAAK,CAAC,CAAC,IAAI,IAAI,CAACxD,SAAS,CAACoE,EAAE,CAACZ,KAAK,CAAC,CAACnF,KAAK,IAAI,IAAI,CAACD,cAAc,EAAE;MAAA,IAAAuG,kBAAA;MACvE,IAAI,CAACvG,cAAc,CAACmC,QAAQ,EAAAoE,kBAAA,GAAC,IAAI,CAAC3E,SAAS,CAACoE,EAAE,CAACZ,KAAK,CAAC,cAAAmB,kBAAA,uBAAxBA,kBAAA,CAA0BtG,KAAK,CAAC;IACjE;EACJ;EACAuG,SAASA,CAACC,CAAC,EAAElE,OAAO,EAAE;IAClB,OAAOA,OAAO,CAACtC,KAAK,CAAC8B,EAAE;EAC3B;EACA,IAAIH,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACV,IAAI,CAACuB,GAAG,CAAC,MAAM,CAAC;EAChC;EACAjB,aAAaA,CAACN,IAAI,EAAE;IAAA,IAAAwF,UAAA;IAChB,OAAO,EAAAA,UAAA,GAACxF,IAAI,CAACC,IAAI,cAAAuF,UAAA,cAAAA,UAAA,GAAI,CAAC,CAAC,EAAEhB,MAAM,CAAC,CAACC,MAAM,EAAE7B,IAAI,EAAEE,KAAK,KAAK;MACrD,MAAM2C,aAAa,GAAA5C,aAAA,CAAAA,aAAA,KACZD,IAAI,GACH,IAAI,CAAClD,mBAAmB,GAAG;QAAEoD;MAAM,CAAC,GAAG,CAAC,CAAC,CAChD;MACD,OAAO2C,aAAa,CAAC5E,EAAE;MACvB,OAAAgC,aAAA,CAAAA,aAAA,KACO4B,MAAM;QACT,CAAC7B,IAAI,CAAC/B,EAAE,GAAG4E;MAAa;IAEhC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;EACA7E,aAAaA,CAAC1C,GAAG,EAAE;IACf,OAAO,IAAI,CAACY,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC4G,QAAQ,CAACxH,GAAG,CAAC,GAAG,IAAIX,gBAAgB,CAAC;MAAEsD,EAAE,EAAE,IAAIvD,kBAAkB,CAACY,GAAG,CAAC2C,EAAE;IAAE,CAAC,CAAC;EACjI;EACA1B,uBAAuBA,CAAA,EAAG;IACtB,MAAMwG,YAAY,GAAG,IAAI,CAAC5E,QAAQ,CAAC6E,SAAS,CAAC,CAAC;MAAEC;IAAO,CAAC,KAAKA,MAAM,KAAK,SAAS,CAAC;IAClF,IAAI,IAAI,CAACzD,OAAO,IAAIuD,YAAY,KAAK,CAAC,CAAC,EAAE;MACrC,IAAI,CAACvD,OAAO,CAAC0D,aAAa,GAAGH,YAAY;IAC7C;EACJ;EACAnF,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAChB,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACuG,UAAU,CAAC3F,IAAI,CAACvC,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAAC,CAACO,SAAS,CAACpC,SAAS,IAAI;QAC9F,MAAM+H,OAAO,GAAG,IAAI,CAACjF,QAAQ,CAACyD,MAAM,CAAC,CAACyB,GAAG,EAAE;UAAElH;QAAM,CAAC,EAAEmF,KAAK,KAAK;UAC5D,IAAI,CAACjG,SAAS,CAACiI,QAAQ,CAACnH,KAAK,CAAC8B,EAAE,CAAC,EAAE;YAC/BoF,GAAG,CAACE,OAAO,CAACjC,KAAK,CAAC;UACtB;UACA,OAAO+B,GAAG;QACd,CAAC,EAAE,EAAE,CAAC;QACND,OAAO,CAAChD,OAAO,CAACkB,KAAK,IAAI;UACrB,IAAI,CAACD,SAAS,CAACC,KAAK,CAAC;QACzB,CAAC,CAAC;QACFjG,SAAS,CAAC+E,OAAO,CAACoD,QAAQ,IAAI;UAC1B,MAAM9H,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACqE,GAAG,CAAC,CAAC;YAAE9B;UAAG,CAAC,KAAKA,EAAE,CAAC;UACtE,IAAIvC,kBAAkB,CAAC4H,QAAQ,CAACE,QAAQ,CAAC,EAAE;YACvC,IAAI,CAACvC,MAAM,CAACuC,QAAQ,CAAC;UACzB;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;AAgBJ,CAAC,EAfYpI,iCAAA,CAAKqI,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE5I;AAAiB,CAAC,EAC1B;EAAE4I,IAAI,EAAExI,8BAA8B;EAAEyI,UAAU,EAAE,CAAC;IAAED,IAAI,EAAEpJ;EAAS,CAAC;AAAE,CAAC,CAC7E,EACQc,iCAAA,CAAKwI,cAAc,GAAG;EAC3BvI,SAAS,EAAE,CAAC;IAAEqI,IAAI,EAAErJ;EAAM,CAAC,CAAC;EAC5B6B,cAAc,EAAE,CAAC;IAAEwH,IAAI,EAAErJ,KAAK;IAAEwJ,IAAI,EAAE,CAAC,WAAW;EAAG,CAAC,CAAC;EACvDxH,SAAS,EAAE,CAAC;IAAEqH,IAAI,EAAErJ;EAAM,CAAC,CAAC;EAC5BmC,UAAU,EAAE,CAAC;IAAEkH,IAAI,EAAErJ;EAAM,CAAC,CAAC;EAC7BwC,cAAc,EAAE,CAAC;IAAE6G,IAAI,EAAErJ;EAAM,CAAC,CAAC;EACjCqC,wBAAwB,EAAE,CAAC;IAAEgH,IAAI,EAAErJ;EAAM,CAAC,CAAC;EAC3CyC,mBAAmB,EAAE,CAAC;IAAE4G,IAAI,EAAErJ;EAAM,CAAC,CAAC;EACtC0C,OAAO,EAAE,CAAC;IAAE2G,IAAI,EAAEnJ;EAAO,CAAC,CAAC;EAC3BiF,OAAO,EAAE,CAAC;IAAEkE,IAAI,EAAElJ,SAAS;IAAEqJ,IAAI,EAAE,CAAC,QAAQ,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC;AACtE,CAAC,EAAA1I,iCAAA,CACJ;AACDD,gCAAgC,GAAGpB,UAAU,CAAC,CAC1CG,SAAS,CAAC;EACN6J,QAAQ,EAAE,+BAA+B;EACzCC,QAAQ,EAAEhK,oBAAoB;EAC9BiK,SAAS,EAAE,CACP;IACIC,OAAO,EAAErJ,iBAAiB;IAC1BsJ,WAAW,EAAE/J,UAAU,CAAC,MAAMe,gCAAgC,CAAC;IAC/DiJ,KAAK,EAAE;EACX,CAAC,EACD;IACIF,OAAO,EAAEtJ,aAAa;IACtBuJ,WAAW,EAAE/J,UAAU,CAAC,MAAMe,gCAAgC,CAAC;IAC/DiJ,KAAK,EAAE;EACX,CAAC,CACJ;EACDC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACrK,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEkB,gCAAgC,CAAC;AACpC,SAASA,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}