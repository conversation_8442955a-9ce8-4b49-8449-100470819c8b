{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nexport function fromEventPattern(add<PERSON><PERSON><PERSON>, remove<PERSON><PERSON><PERSON>, resultSelector) {\n  if (resultSelector) {\n    return fromEventPattern(addHand<PERSON>, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n  }\n  return new Observable(function (subscriber) {\n    var handler = function () {\n      var e = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        e[_i] = arguments[_i];\n      }\n      return subscriber.next(e.length === 1 ? e[0] : e);\n    };\n    var retValue = addHandler(handler);\n    return isFunction(removeHandler) ? function () {\n      return removeHandler(handler, retValue);\n    } : undefined;\n  });\n}\n//# sourceMappingURL=fromEventPattern.js.map", "map": {"version": 3, "names": ["Observable", "isFunction", "mapOneOrManyArgs", "fromEventPattern", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "resultSelector", "pipe", "subscriber", "handler", "e", "_i", "arguments", "length", "next", "retValue", "undefined"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/fromEventPattern.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nexport function fromEventPattern(add<PERSON><PERSON><PERSON>, remove<PERSON><PERSON><PERSON>, resultSelector) {\n    if (resultSelector) {\n        return fromEventPattern(addHand<PERSON>, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n    }\n    return new Observable(function (subscriber) {\n        var handler = function () {\n            var e = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                e[_i] = arguments[_i];\n            }\n            return subscriber.next(e.length === 1 ? e[0] : e);\n        };\n        var retValue = addHandler(handler);\n        return isFunction(removeHandler) ? function () { return removeHandler(handler, retValue); } : undefined;\n    });\n}\n//# sourceMappingURL=fromEventPattern.js.map"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,OAAO,SAASC,gBAAgBA,CAACC,UAAU,EAAEC,aAAa,EAAEC,cAAc,EAAE;EACxE,IAAIA,cAAc,EAAE;IAChB,OAAOH,gBAAgB,CAACC,UAAU,EAAEC,aAAa,CAAC,CAACE,IAAI,CAACL,gBAAgB,CAACI,cAAc,CAAC,CAAC;EAC7F;EACA,OAAO,IAAIN,UAAU,CAAC,UAAUQ,UAAU,EAAE;IACxC,IAAIC,OAAO,GAAG,SAAAA,CAAA,EAAY;MACtB,IAAIC,CAAC,GAAG,EAAE;MACV,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;QAC1CD,CAAC,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;MACzB;MACA,OAAOH,UAAU,CAACM,IAAI,CAACJ,CAAC,CAACG,MAAM,KAAK,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC;IACrD,CAAC;IACD,IAAIK,QAAQ,GAAGX,UAAU,CAACK,OAAO,CAAC;IAClC,OAAOR,UAAU,CAACI,aAAa,CAAC,GAAG,YAAY;MAAE,OAAOA,aAAa,CAACI,OAAO,EAAEM,QAAQ,CAAC;IAAE,CAAC,GAAGC,SAAS;EAC3G,CAAC,CAAC;AACN;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}