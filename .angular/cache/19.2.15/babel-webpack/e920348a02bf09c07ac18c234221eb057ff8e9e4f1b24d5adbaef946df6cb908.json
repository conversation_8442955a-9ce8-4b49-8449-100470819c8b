{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var ObjectUnsubscribedError = createErrorClass(function (_super) {\n  return function ObjectUnsubscribedErrorImpl() {\n    _super(this);\n    this.name = 'ObjectUnsubscribedError';\n    this.message = 'object unsubscribed';\n  };\n});\n//# sourceMappingURL=ObjectUnsubscribedError.js.map", "map": {"version": 3, "names": ["createErrorClass", "ObjectUnsubscribedError", "_super", "ObjectUnsubscribedErrorImpl", "name", "message"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/ObjectUnsubscribedError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport var ObjectUnsubscribedError = createErrorClass(function (_super) {\n    return function ObjectUnsubscribedErrorImpl() {\n        _super(this);\n        this.name = 'ObjectUnsubscribedError';\n        this.message = 'object unsubscribed';\n    };\n});\n//# sourceMappingURL=ObjectUnsubscribedError.js.map"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,IAAIC,uBAAuB,GAAGD,gBAAgB,CAAC,UAAUE,MAAM,EAAE;EACpE,OAAO,SAASC,2BAA2BA,CAAA,EAAG;IAC1CD,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACE,IAAI,GAAG,yBAAyB;IACrC,IAAI,CAACC,OAAO,GAAG,qBAAqB;EACxC,CAAC;AACL,CAAC,CAAC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}