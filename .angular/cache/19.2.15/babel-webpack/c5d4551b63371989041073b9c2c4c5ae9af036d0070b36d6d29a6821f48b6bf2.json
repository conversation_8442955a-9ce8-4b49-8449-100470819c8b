{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiDatetimepickerComponent } from './swui-datetimepicker.component';\nimport { SwuiCalendarModule } from '../swui-calendar/swui-calendar.module';\nimport { SwuiTimepickerModule } from '../swui-timepicker/swui-timepicker.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nexport const DATETIMEPICKER_MODULES = [MatDividerModule, MatButtonModule, MatInputModule, MatMenuModule, ReactiveFormsModule, SwuiCalendarModule, SwuiTimepickerModule];\n/**\n * @deprecated use lib-swui-date-picker\n */\nlet SwuiDatetimepickerModule = class SwuiDatetimepickerModule {};\nSwuiDatetimepickerModule = __decorate([NgModule({\n  declarations: [SwuiDatetimepickerComponent],\n  exports: [SwuiDatetimepickerComponent],\n  imports: [CommonModule, ...DATETIMEPICKER_MODULES]\n})], SwuiDatetimepickerModule);\nexport { SwuiDatetimepickerModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "ReactiveFormsModule", "SwuiDatetimepickerComponent", "SwuiCalendarModule", "SwuiTimepickerModule", "MatMenuModule", "MatDividerModule", "MatInputModule", "MatButtonModule", "DATETIMEPICKER_MODULES", "SwuiDatetimepickerModule", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-datetimepicker/swui-datetimepicker.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiDatetimepickerComponent } from './swui-datetimepicker.component';\nimport { SwuiCalendarModule } from '../swui-calendar/swui-calendar.module';\nimport { SwuiTimepickerModule } from '../swui-timepicker/swui-timepicker.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nexport const DATETIMEPICKER_MODULES = [\n    MatDividerModule,\n    MatButtonModule,\n    MatInputModule,\n    MatMenuModule,\n    ReactiveFormsModule,\n    SwuiCalendarModule,\n    SwuiTimepickerModule,\n];\n/**\n * @deprecated use lib-swui-date-picker\n */\nlet SwuiDatetimepickerModule = class SwuiDatetimepickerModule {\n};\nSwuiDatetimepickerModule = __decorate([\n    NgModule({\n        declarations: [\n            SwuiDatetimepickerComponent\n        ],\n        exports: [\n            SwuiDatetimepickerComponent\n        ],\n        imports: [\n            CommonModule,\n            ...DATETIMEPICKER_MODULES,\n        ]\n    })\n], SwuiDatetimepickerModule);\nexport { SwuiDatetimepickerModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,sBAAsB,GAAG,CAClCH,gBAAgB,EAChBE,eAAe,EACfD,cAAc,EACdF,aAAa,EACbJ,mBAAmB,EACnBE,kBAAkB,EAClBC,oBAAoB,CACvB;AACD;AACA;AACA;AACA,IAAIM,wBAAwB,GAAG,MAAMA,wBAAwB,CAAC,EAC7D;AACDA,wBAAwB,GAAGZ,UAAU,CAAC,CAClCC,QAAQ,CAAC;EACLY,YAAY,EAAE,CACVT,2BAA2B,CAC9B;EACDU,OAAO,EAAE,CACLV,2BAA2B,CAC9B;EACDW,OAAO,EAAE,CACLb,YAAY,EACZ,GAAGS,sBAAsB;AAEjC,CAAC,CAAC,CACL,EAAEC,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}