{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { SwuiGridWidgetChooserComponent } from './widget-chooser.component';\ndescribe('SwuiGridWidgetChooserComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SwuiGridWidgetChooserComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiGridWidgetChooserComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "SwuiGridWidgetChooserComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/widget-chooser/widget-chooser.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\n\nimport { SwuiGridWidgetChooserComponent } from './widget-chooser.component';\n\ndescribe('SwuiGridWidgetChooserComponent', () => {\n  let component: SwuiGridWidgetChooserComponent;\n  let fixture: ComponentFixture<SwuiGridWidgetChooserComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SwuiGridWidgetChooserComponent]\n    }).compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiGridWidgetChooserComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,8BAA8B,QAAQ,4BAA4B;AAE3EC,QAAQ,CAAC,gCAAgC,EAAE,MAAK;EAC9C,IAAIC,SAAyC;EAC7C,IAAIC,OAAyD;EAE7DC,UAAU,CAACL,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACO,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,8BAA8B;KAC9C,CAAC,CAACO,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGL,OAAO,CAACU,eAAe,CAACR,8BAA8B,CAAC;IACjEE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}