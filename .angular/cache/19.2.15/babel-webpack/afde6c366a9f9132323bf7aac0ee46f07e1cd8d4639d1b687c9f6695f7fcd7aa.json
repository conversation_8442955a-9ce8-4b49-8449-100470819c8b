{"ast": null, "code": "import { __values } from \"tslib\";\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function windowCount(windowSize, startWindowEvery) {\n  if (startWindowEvery === void 0) {\n    startWindowEvery = 0;\n  }\n  var startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n  return operate(function (source, subscriber) {\n    var windows = [new Subject()];\n    var starts = [];\n    var count = 0;\n    subscriber.next(windows[0].asObservable());\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n      try {\n        for (var windows_1 = __values(windows), windows_1_1 = windows_1.next(); !windows_1_1.done; windows_1_1 = windows_1.next()) {\n          var window_1 = windows_1_1.value;\n          window_1.next(value);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (windows_1_1 && !windows_1_1.done && (_a = windows_1.return)) _a.call(windows_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      var c = count - windowSize + 1;\n      if (c >= 0 && c % startEvery === 0) {\n        windows.shift().complete();\n      }\n      if (++count % startEvery === 0) {\n        var window_2 = new Subject();\n        windows.push(window_2);\n        subscriber.next(window_2.asObservable());\n      }\n    }, function () {\n      while (windows.length > 0) {\n        windows.shift().complete();\n      }\n      subscriber.complete();\n    }, function (err) {\n      while (windows.length > 0) {\n        windows.shift().error(err);\n      }\n      subscriber.error(err);\n    }, function () {\n      starts = null;\n      windows = null;\n    }));\n  });\n}\n//# sourceMappingURL=windowCount.js.map", "map": {"version": 3, "names": ["__values", "Subject", "operate", "createOperatorSubscriber", "windowCount", "windowSize", "startWindowEvery", "startEvery", "source", "subscriber", "windows", "starts", "count", "next", "asObservable", "subscribe", "value", "e_1", "_a", "windows_1", "windows_1_1", "done", "window_1", "e_1_1", "error", "return", "call", "c", "shift", "complete", "window_2", "push", "length", "err"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/operators/windowCount.js"], "sourcesContent": ["import { __values } from \"tslib\";\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function windowCount(windowSize, startWindowEvery) {\n    if (startWindowEvery === void 0) { startWindowEvery = 0; }\n    var startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n    return operate(function (source, subscriber) {\n        var windows = [new Subject()];\n        var starts = [];\n        var count = 0;\n        subscriber.next(windows[0].asObservable());\n        source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n            var e_1, _a;\n            try {\n                for (var windows_1 = __values(windows), windows_1_1 = windows_1.next(); !windows_1_1.done; windows_1_1 = windows_1.next()) {\n                    var window_1 = windows_1_1.value;\n                    window_1.next(value);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (windows_1_1 && !windows_1_1.done && (_a = windows_1.return)) _a.call(windows_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            var c = count - windowSize + 1;\n            if (c >= 0 && c % startEvery === 0) {\n                windows.shift().complete();\n            }\n            if (++count % startEvery === 0) {\n                var window_2 = new Subject();\n                windows.push(window_2);\n                subscriber.next(window_2.asObservable());\n            }\n        }, function () {\n            while (windows.length > 0) {\n                windows.shift().complete();\n            }\n            subscriber.complete();\n        }, function (err) {\n            while (windows.length > 0) {\n                windows.shift().error(err);\n            }\n            subscriber.error(err);\n        }, function () {\n            starts = null;\n            windows = null;\n        }));\n    });\n}\n//# sourceMappingURL=windowCount.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,WAAWA,CAACC,UAAU,EAAEC,gBAAgB,EAAE;EACtD,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;IAAEA,gBAAgB,GAAG,CAAC;EAAE;EACzD,IAAIC,UAAU,GAAGD,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,GAAGD,UAAU;EACrE,OAAOH,OAAO,CAAC,UAAUM,MAAM,EAAEC,UAAU,EAAE;IACzC,IAAIC,OAAO,GAAG,CAAC,IAAIT,OAAO,CAAC,CAAC,CAAC;IAC7B,IAAIU,MAAM,GAAG,EAAE;IACf,IAAIC,KAAK,GAAG,CAAC;IACbH,UAAU,CAACI,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAACI,YAAY,CAAC,CAAC,CAAC;IAC1CN,MAAM,CAACO,SAAS,CAACZ,wBAAwB,CAACM,UAAU,EAAE,UAAUO,KAAK,EAAE;MACnE,IAAIC,GAAG,EAAEC,EAAE;MACX,IAAI;QACA,KAAK,IAAIC,SAAS,GAAGnB,QAAQ,CAACU,OAAO,CAAC,EAAEU,WAAW,GAAGD,SAAS,CAACN,IAAI,CAAC,CAAC,EAAE,CAACO,WAAW,CAACC,IAAI,EAAED,WAAW,GAAGD,SAAS,CAACN,IAAI,CAAC,CAAC,EAAE;UACvH,IAAIS,QAAQ,GAAGF,WAAW,CAACJ,KAAK;UAChCM,QAAQ,CAACT,IAAI,CAACG,KAAK,CAAC;QACxB;MACJ,CAAC,CACD,OAAOO,KAAK,EAAE;QAAEN,GAAG,GAAG;UAAEO,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIH,WAAW,IAAI,CAACA,WAAW,CAACC,IAAI,KAAKH,EAAE,GAAGC,SAAS,CAACM,MAAM,CAAC,EAAEP,EAAE,CAACQ,IAAI,CAACP,SAAS,CAAC;QACvF,CAAC,SACO;UAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACO,KAAK;QAAE;MACxC;MACA,IAAIG,CAAC,GAAGf,KAAK,GAAGP,UAAU,GAAG,CAAC;MAC9B,IAAIsB,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGpB,UAAU,KAAK,CAAC,EAAE;QAChCG,OAAO,CAACkB,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MAC9B;MACA,IAAI,EAAEjB,KAAK,GAAGL,UAAU,KAAK,CAAC,EAAE;QAC5B,IAAIuB,QAAQ,GAAG,IAAI7B,OAAO,CAAC,CAAC;QAC5BS,OAAO,CAACqB,IAAI,CAACD,QAAQ,CAAC;QACtBrB,UAAU,CAACI,IAAI,CAACiB,QAAQ,CAAChB,YAAY,CAAC,CAAC,CAAC;MAC5C;IACJ,CAAC,EAAE,YAAY;MACX,OAAOJ,OAAO,CAACsB,MAAM,GAAG,CAAC,EAAE;QACvBtB,OAAO,CAACkB,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MAC9B;MACApB,UAAU,CAACoB,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAE,UAAUI,GAAG,EAAE;MACd,OAAOvB,OAAO,CAACsB,MAAM,GAAG,CAAC,EAAE;QACvBtB,OAAO,CAACkB,KAAK,CAAC,CAAC,CAACJ,KAAK,CAACS,GAAG,CAAC;MAC9B;MACAxB,UAAU,CAACe,KAAK,CAACS,GAAG,CAAC;IACzB,CAAC,EAAE,YAAY;MACXtB,MAAM,GAAG,IAAI;MACbD,OAAO,GAAG,IAAI;IAClB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}