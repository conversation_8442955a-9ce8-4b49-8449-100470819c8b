{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { SwuiSidebarService } from './swui-sidebar.service';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\ndescribe('SwuiSidebarService', () => {\n  beforeEach(() => TestBed.configureTestingModule({\n    providers: [SwuiSidebarService],\n    schemas: [NO_ERRORS_SCHEMA]\n  }));\n  it('should be created', () => {\n    const service = TestBed.inject(SwuiSidebarService);\n    expect(service).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "SwuiSidebarService", "NO_ERRORS_SCHEMA", "describe", "beforeEach", "configureTestingModule", "providers", "schemas", "it", "service", "inject", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-sidebar/swui-sidebar.service.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\n\nimport { SwuiSidebarService } from './swui-sidebar.service';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\n\ndescribe('SwuiSidebarService', () => {\n  beforeEach(() => TestBed.configureTestingModule({\n    providers: [SwuiSidebarService],\n    schemas: [NO_ERRORS_SCHEMA],\n  }));\n\n  it('should be created', () => {\n    const service: SwuiSidebarService = TestBed.inject(SwuiSidebarService);\n    expect(service).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,eAAe;AAEhDC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClCC,UAAU,CAAC,MAAMJ,OAAO,CAACK,sBAAsB,CAAC;IAC9CC,SAAS,EAAE,CAACL,kBAAkB,CAAC;IAC/BM,OAAO,EAAE,CAACL,gBAAgB;GAC3B,CAAC,CAAC;EAEHM,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3B,MAAMC,OAAO,GAAuBT,OAAO,CAACU,MAAM,CAACT,kBAAkB,CAAC;IACtEU,MAAM,CAACF,OAAO,CAAC,CAACG,UAAU,EAAE;EAC9B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}