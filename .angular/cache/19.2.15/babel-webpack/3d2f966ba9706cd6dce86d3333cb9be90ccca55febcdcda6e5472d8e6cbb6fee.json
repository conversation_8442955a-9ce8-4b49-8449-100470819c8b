{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatSnackBarLabel, _MatSnackBarActions, _MatSnackBarAction, _SimpleSnackBar, _MatSnackBarContainer, _MatSnackBar, _MatSnackBarModule;\nfunction _SimpleSnackBar_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function _SimpleSnackBar_Conditional_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.data.action, \" \");\n  }\n}\nconst _c0 = [\"label\"];\nfunction _MatSnackBarContainer_ng_template_4_Template(rf, ctx) {}\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, NgZone, ElementRef, ChangeDetectorRef, ANIMATION_MODULE_TYPE, afterRender, ViewChild, Injector, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, of } from 'rxjs';\nimport { MatButton, MatButtonModule } from './button.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { _IdGenerator, LiveAnnouncer } from '@angular/cdk/a11y';\nimport { Platform } from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport './icon-button-D1J0zeqv.mjs';\nimport '@angular/cdk/private';\nimport './ripple-loader-Ce3DAhPW.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/coercion';\nimport './structural-styles-BQUT6wsL.mjs';\nimport './index-SYVYjXwK.mjs';\nimport '@angular/cdk/bidi';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n  constructor(containerInstance, _overlayRef) {\n    _defineProperty(this, \"_overlayRef\", void 0);\n    /** The instance of the component making up the content of the snack bar. */\n    _defineProperty(this, \"instance\", void 0);\n    /**\n     * The instance of the component making up the content of the snack bar.\n     * @docs-private\n     */\n    _defineProperty(this, \"containerInstance\", void 0);\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n    _defineProperty(this, \"_afterDismissed\", new Subject());\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n    _defineProperty(this, \"_afterOpened\", new Subject());\n    /** Subject for notifying the user that the snack bar action was called. */\n    _defineProperty(this, \"_onAction\", new Subject());\n    /**\n     * Timeout ID for the duration setTimeout call. Used to clear the timeout if the snackbar is\n     * dismissed before the duration passes.\n     */\n    _defineProperty(this, \"_durationTimeoutId\", void 0);\n    /** Whether the snack bar was dismissed using the action button. */\n    _defineProperty(this, \"_dismissedByAction\", false);\n    this._overlayRef = _overlayRef;\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n  _finishDismiss() {\n    this._overlayRef.dispose();\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction() {\n    return this._onAction;\n  }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n  constructor() {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    _defineProperty(this, \"politeness\", 'assertive');\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n    _defineProperty(this, \"announcementMessage\", '');\n    /**\n     * The view container that serves as the parent for the snackbar for the purposes of dependency\n     * injection. Note: this does not affect where the snackbar is inserted in the DOM.\n     */\n    _defineProperty(this, \"viewContainerRef\", void 0);\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n    _defineProperty(this, \"duration\", 0);\n    /** Extra CSS classes to be added to the snack bar container. */\n    _defineProperty(this, \"panelClass\", void 0);\n    /** Text layout direction for the snack bar. */\n    _defineProperty(this, \"direction\", void 0);\n    /** Data being injected into the child component. */\n    _defineProperty(this, \"data\", null);\n    /** The horizontal position to place the snack bar. */\n    _defineProperty(this, \"horizontalPosition\", 'center');\n    /** The vertical position to place the snack bar. */\n    _defineProperty(this, \"verticalPosition\", 'bottom');\n  }\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {}\n_MatSnackBarLabel = MatSnackBarLabel;\n_defineProperty(MatSnackBarLabel, \"\\u0275fac\", function _MatSnackBarLabel_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSnackBarLabel)();\n});\n_defineProperty(MatSnackBarLabel, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatSnackBarLabel,\n  selectors: [[\"\", \"matSnackBarLabel\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-snack-bar-label\", \"mdc-snackbar__label\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarLabel, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarLabel]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {}\n_MatSnackBarActions = MatSnackBarActions;\n_defineProperty(MatSnackBarActions, \"\\u0275fac\", function _MatSnackBarActions_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSnackBarActions)();\n});\n_defineProperty(MatSnackBarActions, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatSnackBarActions,\n  selectors: [[\"\", \"matSnackBarActions\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-snack-bar-actions\", \"mdc-snackbar__actions\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarActions, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarActions]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {}\n_MatSnackBarAction = MatSnackBarAction;\n_defineProperty(MatSnackBarAction, \"\\u0275fac\", function _MatSnackBarAction_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSnackBarAction)();\n});\n_defineProperty(MatSnackBarAction, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatSnackBarAction,\n  selectors: [[\"\", \"matSnackBarAction\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-snack-bar-action\", \"mdc-snackbar__action\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarAction, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarAction]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action'\n      }\n    }]\n  }], null, null);\n})();\nclass SimpleSnackBar {\n  constructor() {\n    _defineProperty(this, \"snackBarRef\", inject(MatSnackBarRef));\n    _defineProperty(this, \"data\", inject(MAT_SNACK_BAR_DATA));\n  }\n  /** Performs the action on the snack bar. */\n  action() {\n    this.snackBarRef.dismissWithAction();\n  }\n  /** If the action button should be shown. */\n  get hasAction() {\n    return !!this.data.action;\n  }\n}\n_SimpleSnackBar = SimpleSnackBar;\n_defineProperty(SimpleSnackBar, \"\\u0275fac\", function _SimpleSnackBar_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SimpleSnackBar)();\n});\n_defineProperty(SimpleSnackBar, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _SimpleSnackBar,\n  selectors: [[\"simple-snack-bar\"]],\n  hostAttrs: [1, \"mat-mdc-simple-snack-bar\"],\n  exportAs: [\"matSnackBar\"],\n  decls: 3,\n  vars: 2,\n  consts: [[\"matSnackBarLabel\", \"\"], [\"matSnackBarActions\", \"\"], [\"mat-button\", \"\", \"matSnackBarAction\", \"\", 3, \"click\"]],\n  template: function _SimpleSnackBar_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtext(1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(2, _SimpleSnackBar_Conditional_2_Template, 3, 1, \"div\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \"\\n\");\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasAction ? 2 : -1);\n    }\n  },\n  dependencies: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n  styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SimpleSnackBar, [{\n    type: Component,\n    args: [{\n      selector: 'simple-snack-bar',\n      exportAs: 'matSnackBar',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      host: {\n        'class': 'mat-mdc-simple-snack-bar'\n      },\n      template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\",\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"]\n    }]\n  }], () => [], null);\n})();\nconst ENTER_ANIMATION = '_mat-snack-bar-enter';\nconst EXIT_ANIMATION = '_mat-snack-bar-exit';\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n  constructor() {\n    super();\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_rendersRef\", void 0);\n    _defineProperty(this, \"_animationsDisabled\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }) === 'NoopAnimations');\n    _defineProperty(this, \"snackBarConfig\", inject(MatSnackBarConfig));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_trackedModals\", new Set());\n    _defineProperty(this, \"_enterFallback\", void 0);\n    _defineProperty(this, \"_exitFallback\", void 0);\n    _defineProperty(this, \"_renders\", new Subject());\n    /** The number of milliseconds to wait before announcing the snack bar's content. */\n    _defineProperty(this, \"_announceDelay\", 150);\n    /** The timeout for announcing the snack bar's content. */\n    _defineProperty(this, \"_announceTimeoutId\", void 0);\n    /** Whether the component has been destroyed. */\n    _defineProperty(this, \"_destroyed\", false);\n    /** The portal outlet inside of this container into which the snack bar content will be loaded. */\n    _defineProperty(this, \"_portalOutlet\", void 0);\n    /** Subject for notifying that the snack bar has announced to screen readers. */\n    _defineProperty(this, \"_onAnnounce\", new Subject());\n    /** Subject for notifying that the snack bar has exited from view. */\n    _defineProperty(this, \"_onExit\", new Subject());\n    /** Subject for notifying that the snack bar has finished entering the view. */\n    _defineProperty(this, \"_onEnter\", new Subject());\n    /** The state of the snack bar animations. */\n    _defineProperty(this, \"_animationState\", 'void');\n    /** aria-live value for the live region. */\n    _defineProperty(this, \"_live\", void 0);\n    /**\n     * Element that will have the `mdc-snackbar__label` class applied if the attached component\n     * or template does not have it. This ensures that the appropriate structure, typography, and\n     * color is applied to the attached view.\n     */\n    _defineProperty(this, \"_label\", void 0);\n    /**\n     * Role of the live region. This is only for Firefox as there is a known issue where Firefox +\n     * JAWS does not read out aria-live message.\n     */\n    _defineProperty(this, \"_role\", void 0);\n    /** Unique ID of the aria-live element. */\n    _defineProperty(this, \"_liveElementId\", inject(_IdGenerator).getId('mat-snack-bar-container-live-'));\n    /**\n     * Attaches a DOM portal to the snack bar container.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    _defineProperty(this, \"attachDomPortal\", portal => {\n      this._assertNotAttached();\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._afterPortalAttached();\n      return result;\n    });\n    const config = this.snackBarConfig;\n    // Use aria-live rather than a live role like 'alert' or 'status'\n    // because NVDA and JAWS have show inconsistent behavior with live roles.\n    if (config.politeness === 'assertive' && !config.announcementMessage) {\n      this._live = 'assertive';\n    } else if (config.politeness === 'off') {\n      this._live = 'off';\n    } else {\n      this._live = 'polite';\n    }\n    // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n    // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n    if (this._platform.FIREFOX) {\n      if (this._live === 'polite') {\n        this._role = 'status';\n      }\n      if (this._live === 'assertive') {\n        this._role = 'alert';\n      }\n    }\n    // Note: ideally we'd just do an `afterNextRender` in the places where we need to delay\n    // something, however in some cases (TestBed teardown) the injector can be destroyed at an\n    // unexpected time, causing the `afterRender` to fail.\n    this._rendersRef = afterRender(() => this._renders.next(), {\n      manualCleanup: true\n    });\n  }\n  /** Attach a component portal as content to this snack bar container. */\n  attachComponentPortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Attach a template portal as content to this snack bar container. */\n  attachTemplatePortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Handle end of animations, updating the state of the snackbar. */\n  onAnimationEnd(animationName) {\n    if (animationName === EXIT_ANIMATION) {\n      this._completeExit();\n    } else if (animationName === ENTER_ANIMATION) {\n      clearTimeout(this._enterFallback);\n      this._ngZone.run(() => {\n        this._onEnter.next();\n        this._onEnter.complete();\n      });\n    }\n  }\n  /** Begin animation of snack bar entrance into view. */\n  enter() {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n      // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n      this._changeDetectorRef.markForCheck();\n      this._changeDetectorRef.detectChanges();\n      this._screenReaderAnnounce();\n      if (this._animationsDisabled) {\n        this._renders.pipe(take(1)).subscribe(() => {\n          this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(ENTER_ANIMATION)));\n        });\n      } else {\n        clearTimeout(this._enterFallback);\n        this._enterFallback = setTimeout(() => {\n          // The snack bar will stay invisible if it fails to animate. Add a fallback class so it\n          // becomes visible. This can happen in some apps that do `* {animation: none !important}`.\n          this._elementRef.nativeElement.classList.add('mat-snack-bar-fallback-visible');\n          this.onAnimationEnd(ENTER_ANIMATION);\n        }, 200);\n      }\n    }\n  }\n  /** Begin animation of the snack bar exiting from view. */\n  exit() {\n    if (this._destroyed) {\n      return of(undefined);\n    }\n    // It's common for snack bars to be opened by random outside calls like HTTP requests or\n    // errors. Run inside the NgZone to ensure that it functions correctly.\n    this._ngZone.run(() => {\n      // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n      // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n      // `MatSnackBar.open`).\n      this._animationState = 'hidden';\n      this._changeDetectorRef.markForCheck();\n      // Mark this element with an 'exit' attribute to indicate that the snackbar has\n      // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n      // test harness.\n      this._elementRef.nativeElement.setAttribute('mat-exit', '');\n      // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n      // long enough to visually read it either, so clear the timeout for announcing.\n      clearTimeout(this._announceTimeoutId);\n      if (this._animationsDisabled) {\n        this._renders.pipe(take(1)).subscribe(() => {\n          this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(EXIT_ANIMATION)));\n        });\n      } else {\n        clearTimeout(this._exitFallback);\n        this._exitFallback = setTimeout(() => this.onAnimationEnd(EXIT_ANIMATION), 200);\n      }\n    });\n    return this._onExit;\n  }\n  /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n  ngOnDestroy() {\n    this._destroyed = true;\n    this._clearFromModals();\n    this._completeExit();\n    this._renders.complete();\n    this._rendersRef.destroy();\n  }\n  _completeExit() {\n    clearTimeout(this._exitFallback);\n    queueMicrotask(() => {\n      this._onExit.next();\n      this._onExit.complete();\n    });\n  }\n  /**\n   * Called after the portal contents have been attached. Can be\n   * used to modify the DOM once it's guaranteed to be in place.\n   */\n  _afterPortalAttached() {\n    const element = this._elementRef.nativeElement;\n    const panelClasses = this.snackBarConfig.panelClass;\n    if (panelClasses) {\n      if (Array.isArray(panelClasses)) {\n        // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n        panelClasses.forEach(cssClass => element.classList.add(cssClass));\n      } else {\n        element.classList.add(panelClasses);\n      }\n    }\n    this._exposeToModals();\n    // Check to see if the attached component or template uses the MDC template structure,\n    // specifically the MDC label. If not, the container should apply the MDC label class to this\n    // component's label container, which will apply MDC's label styles to the attached view.\n    const label = this._label.nativeElement;\n    const labelClass = 'mdc-snackbar__label';\n    label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live element if there is an\n   * `aria-modal` and the live element is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live element.\n   */\n  _exposeToModals() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n    // `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const id = this._liveElementId;\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      this._trackedModals.add(modal);\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  /** Clears the references to the live element from any modals it was added to. */\n  _clearFromModals() {\n    this._trackedModals.forEach(modal => {\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (ariaOwns) {\n        const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n        if (newValue.length > 0) {\n          modal.setAttribute('aria-owns', newValue);\n        } else {\n          modal.removeAttribute('aria-owns');\n        }\n      }\n    });\n    this._trackedModals.clear();\n  }\n  /** Asserts that no content is already attached to the container. */\n  _assertNotAttached() {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempting to attach snack bar content after content is already attached');\n    }\n  }\n  /**\n   * Starts a timeout to move the snack bar content to the live region so screen readers will\n   * announce it.\n   */\n  _screenReaderAnnounce() {\n    if (this._announceTimeoutId) {\n      return;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      this._announceTimeoutId = setTimeout(() => {\n        if (this._destroyed) {\n          return;\n        }\n        const element = this._elementRef.nativeElement;\n        const inertElement = element.querySelector('[aria-hidden]');\n        const liveElement = element.querySelector('[aria-live]');\n        if (inertElement && liveElement) {\n          var _focusedElement;\n          // If an element in the snack bar content is focused before being moved\n          // track it and restore focus after moving to the live region.\n          let focusedElement = null;\n          if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n            focusedElement = document.activeElement;\n          }\n          inertElement.removeAttribute('aria-hidden');\n          liveElement.appendChild(inertElement);\n          (_focusedElement = focusedElement) === null || _focusedElement === void 0 || _focusedElement.focus();\n          this._onAnnounce.next();\n          this._onAnnounce.complete();\n        }\n      }, this._announceDelay);\n    });\n  }\n}\n_MatSnackBarContainer = MatSnackBarContainer;\n_defineProperty(MatSnackBarContainer, \"\\u0275fac\", function _MatSnackBarContainer_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSnackBarContainer)();\n});\n_defineProperty(MatSnackBarContainer, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatSnackBarContainer,\n  selectors: [[\"mat-snack-bar-container\"]],\n  viewQuery: function _MatSnackBarContainer_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mdc-snackbar\", \"mat-mdc-snack-bar-container\"],\n  hostVars: 6,\n  hostBindings: function _MatSnackBarContainer_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"animationend\", function _MatSnackBarContainer_animationend_HostBindingHandler($event) {\n        return ctx.onAnimationEnd($event.animationName);\n      })(\"animationcancel\", function _MatSnackBarContainer_animationcancel_HostBindingHandler($event) {\n        return ctx.onAnimationEnd($event.animationName);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-snack-bar-container-enter\", ctx._animationState === \"visible\")(\"mat-snack-bar-container-exit\", ctx._animationState === \"hidden\")(\"mat-snack-bar-container-animations-enabled\", !ctx._animationsDisabled);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 6,\n  vars: 3,\n  consts: [[\"label\", \"\"], [1, \"mdc-snackbar__surface\", \"mat-mdc-snackbar-surface\"], [1, \"mat-mdc-snack-bar-label\"], [\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n  template: function _MatSnackBarContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2, 0)(3, \"div\", 3);\n      i0.ɵɵtemplate(4, _MatSnackBarContainer_ng_template_4_Template, 0, 0, \"ng-template\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(5, \"div\");\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(5);\n      i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role)(\"id\", ctx._liveElementId);\n    }\n  },\n  dependencies: [CdkPortalOutlet],\n  styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-snack-bar-container',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'mdc-snackbar mat-mdc-snack-bar-container',\n        '[class.mat-snack-bar-container-enter]': '_animationState === \"visible\"',\n        '[class.mat-snack-bar-container-exit]': '_animationState === \"hidden\"',\n        '[class.mat-snack-bar-container-animations-enabled]': '!_animationsDisabled',\n        '(animationend)': 'onAnimationEnd($event.animationName)',\n        '(animationcancel)': 'onAnimationEnd($event.animationName)'\n      },\n      template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\",\n      styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"]\n    }]\n  }], () => [], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }],\n    _label: [{\n      type: ViewChild,\n      args: ['label', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n  /** Reference to the currently opened snackbar at *any* level. */\n  get _openedSnackBarRef() {\n    const parent = this._parentSnackBar;\n    return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n  }\n  set _openedSnackBarRef(value) {\n    if (this._parentSnackBar) {\n      this._parentSnackBar._openedSnackBarRef = value;\n    } else {\n      this._snackBarRefAtThisLevel = value;\n    }\n  }\n  constructor() {\n    _defineProperty(this, \"_overlay\", inject(Overlay));\n    _defineProperty(this, \"_live\", inject(LiveAnnouncer));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_breakpointObserver\", inject(BreakpointObserver));\n    _defineProperty(this, \"_parentSnackBar\", inject(MatSnackBar, {\n      optional: true,\n      skipSelf: true\n    }));\n    _defineProperty(this, \"_defaultConfig\", inject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n    /**\n     * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n     * If there is a parent snack-bar service, all operations should delegate to that parent\n     * via `_openedSnackBarRef`.\n     */\n    _defineProperty(this, \"_snackBarRefAtThisLevel\", null);\n    /** The component that should be rendered as the snack bar's simple component. */\n    _defineProperty(this, \"simpleSnackBarComponent\", SimpleSnackBar);\n    /** The container component that attaches the provided template or component. */\n    _defineProperty(this, \"snackBarContainerComponent\", MatSnackBarContainer);\n    /** The CSS class to apply for handset mode. */\n    _defineProperty(this, \"handsetCssClass\", 'mat-mdc-snack-bar-handset');\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom component for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param component Component to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromComponent(component, config) {\n    return this._attach(component, config);\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom template for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param template Template to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromTemplate(template, config) {\n    return this._attach(template, config);\n  }\n  /**\n   * Opens a snackbar with a message and an optional action.\n   * @param message The message to show in the snackbar.\n   * @param action The label for the snackbar action.\n   * @param config Additional configuration options for the snackbar.\n   */\n  open(message, action = '', config) {\n    const _config = _objectSpread(_objectSpread({}, this._defaultConfig), config);\n    // Since the user doesn't have access to the component, we can\n    // override the data to pass in our own message and action.\n    _config.data = {\n      message,\n      action\n    };\n    // Since the snack bar has `role=\"alert\"`, we don't\n    // want to announce the same message twice.\n    if (_config.announcementMessage === message) {\n      _config.announcementMessage = undefined;\n    }\n    return this.openFromComponent(this.simpleSnackBarComponent, _config);\n  }\n  /**\n   * Dismisses the currently-visible snack bar.\n   */\n  dismiss() {\n    if (this._openedSnackBarRef) {\n      this._openedSnackBarRef.dismiss();\n    }\n  }\n  ngOnDestroy() {\n    // Only dismiss the snack bar at the current level on destroy.\n    if (this._snackBarRefAtThisLevel) {\n      this._snackBarRefAtThisLevel.dismiss();\n    }\n  }\n  /**\n   * Attaches the snack bar container component to the overlay.\n   */\n  _attachSnackBarContainer(overlayRef, config) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    const injector = Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarConfig,\n        useValue: config\n      }]\n    });\n    const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n    const containerRef = overlayRef.attach(containerPortal);\n    containerRef.instance.snackBarConfig = config;\n    return containerRef.instance;\n  }\n  /**\n   * Places a new component or a template as the content of the snack bar container.\n   */\n  _attach(content, userConfig) {\n    const config = _objectSpread(_objectSpread(_objectSpread({}, new MatSnackBarConfig()), this._defaultConfig), userConfig);\n    const overlayRef = this._createOverlay(config);\n    const container = this._attachSnackBarContainer(overlayRef, config);\n    const snackBarRef = new MatSnackBarRef(container, overlayRef);\n    if (content instanceof TemplateRef) {\n      const portal = new TemplatePortal(content, null, {\n        $implicit: config.data,\n        snackBarRef\n      });\n      snackBarRef.instance = container.attachTemplatePortal(portal);\n    } else {\n      const injector = this._createInjector(config, snackBarRef);\n      const portal = new ComponentPortal(content, undefined, injector);\n      const contentRef = container.attachComponentPortal(portal);\n      // We can't pass this via the injector, because the injector is created earlier.\n      snackBarRef.instance = contentRef.instance;\n    }\n    // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n    // appropriate. This class is applied to the overlay element because the overlay must expand to\n    // fill the width of the screen for full width snackbars.\n    this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n      overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n    });\n    if (config.announcementMessage) {\n      // Wait until the snack bar contents have been announced then deliver this message.\n      container._onAnnounce.subscribe(() => {\n        this._live.announce(config.announcementMessage, config.politeness);\n      });\n    }\n    this._animateSnackBar(snackBarRef, config);\n    this._openedSnackBarRef = snackBarRef;\n    return this._openedSnackBarRef;\n  }\n  /** Animates the old snack bar out and the new one in. */\n  _animateSnackBar(snackBarRef, config) {\n    // When the snackbar is dismissed, clear the reference to it.\n    snackBarRef.afterDismissed().subscribe(() => {\n      // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n      if (this._openedSnackBarRef == snackBarRef) {\n        this._openedSnackBarRef = null;\n      }\n      if (config.announcementMessage) {\n        this._live.clear();\n      }\n    });\n    // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n    if (config.duration && config.duration > 0) {\n      snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n    }\n    if (this._openedSnackBarRef) {\n      // If a snack bar is already in view, dismiss it and enter the\n      // new snack bar after exit animation is complete.\n      this._openedSnackBarRef.afterDismissed().subscribe(() => {\n        snackBarRef.containerInstance.enter();\n      });\n      this._openedSnackBarRef.dismiss();\n    } else {\n      // If no snack bar is in view, enter the new snack bar.\n      snackBarRef.containerInstance.enter();\n    }\n  }\n  /**\n   * Creates a new overlay and places it in the correct location.\n   * @param config The user-specified snack bar config.\n   */\n  _createOverlay(config) {\n    const overlayConfig = new OverlayConfig();\n    overlayConfig.direction = config.direction;\n    let positionStrategy = this._overlay.position().global();\n    // Set horizontal position.\n    const isRtl = config.direction === 'rtl';\n    const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n    const isRight = !isLeft && config.horizontalPosition !== 'center';\n    if (isLeft) {\n      positionStrategy.left('0');\n    } else if (isRight) {\n      positionStrategy.right('0');\n    } else {\n      positionStrategy.centerHorizontally();\n    }\n    // Set horizontal position.\n    if (config.verticalPosition === 'top') {\n      positionStrategy.top('0');\n    } else {\n      positionStrategy.bottom('0');\n    }\n    overlayConfig.positionStrategy = positionStrategy;\n    return this._overlay.create(overlayConfig);\n  }\n  /**\n   * Creates an injector to be used inside of a snack bar component.\n   * @param config Config that was used to create the snack bar.\n   * @param snackBarRef Reference to the snack bar.\n   */\n  _createInjector(config, snackBarRef) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    return Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarRef,\n        useValue: snackBarRef\n      }, {\n        provide: MAT_SNACK_BAR_DATA,\n        useValue: config.data\n      }]\n    });\n  }\n}\n_MatSnackBar = MatSnackBar;\n_defineProperty(MatSnackBar, \"\\u0275fac\", function _MatSnackBar_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSnackBar)();\n});\n_defineProperty(MatSnackBar, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _MatSnackBar,\n  factory: _MatSnackBar.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBar, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {}\n_MatSnackBarModule = MatSnackBarModule;\n_defineProperty(MatSnackBarModule, \"\\u0275fac\", function _MatSnackBarModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSnackBarModule)();\n});\n_defineProperty(MatSnackBarModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatSnackBarModule,\n  imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n  exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction]\n}));\n_defineProperty(MatSnackBarModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MatSnackBar],\n  imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, ...DIRECTIVES],\n      exports: [MatCommonModule, ...DIRECTIVES],\n      providers: [MatSnackBar]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSnackBarAnimations = {\n  // Represents\n  // trigger('state', [\n  //   state(\n  //     'void, hidden',\n  //     style({\n  //       transform: 'scale(0.8)',\n  //       opacity: 0,\n  //     }),\n  //   ),\n  //   state(\n  //     'visible',\n  //     style({\n  //       transform: 'scale(1)',\n  //       opacity: 1,\n  //     }),\n  //   ),\n  //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n  //   transition(\n  //     '* => void, * => hidden',\n  //     animate(\n  //       '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n  //       style({\n  //         opacity: 0,\n  //       }),\n  //     ),\n  //   ),\n  // ])\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: {\n    type: 7,\n    name: 'state',\n    'definitions': [{\n      type: 0,\n      name: 'void, hidden',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(0.8)',\n          opacity: 0\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'visible',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(1)',\n          opacity: 1\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => visible',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '150ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void, * => hidden',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '75ms cubic-bezier(0.4, 0.0, 1, 1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };\n//# sourceMappingURL=snack-bar.mjs.map", "map": {"version": 3, "names": ["i0", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "_SimpleSnackBar_Conditional_2_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "action", "ɵɵtext", "ɵɵelementEnd", "rf", "ɵɵadvance", "ɵɵtextInterpolate1", "data", "_c0", "_MatSnackBarContainer_ng_template_4_Template", "ctx", "InjectionToken", "Directive", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "NgZone", "ElementRef", "ChangeDetectorRef", "ANIMATION_MODULE_TYPE", "afterRender", "ViewChild", "Injector", "TemplateRef", "Injectable", "NgModule", "Subject", "of", "MatButton", "MatButtonModule", "DOCUMENT", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "_IdGenerator", "LiveAnnouncer", "Platform", "take", "takeUntil", "BreakpointObserver", "Breakpoints", "Overlay", "OverlayConfig", "OverlayModule", "M", "MatCommonModule", "MAX_TIMEOUT", "Math", "pow", "MatSnackBarRef", "constructor", "containerInstance", "_overlayRef", "_defineProperty", "_onExit", "subscribe", "_finishDismiss", "dismiss", "_afterDismissed", "closed", "exit", "clearTimeout", "_durationTimeoutId", "dismissWithAction", "_onAction", "_dismissedByAction", "next", "complete", "closeWithAction", "_dismissAfter", "duration", "setTimeout", "min", "_open", "_afterOpened", "dispose", "dismissedByAction", "afterDismissed", "afterOpened", "_onEnter", "onAction", "MAT_SNACK_BAR_DATA", "MatSnackBarConfig", "MatSnackBarLabel", "_MatSnackBarLabel", "_MatSnackBarLabel_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "MatSnackBarActions", "_MatSnackBarActions", "_MatSnackBarActions_Factory", "MatSnackBarAction", "_MatSnackBarAction", "_MatSnackBarAction_Factory", "SimpleSnackBar", "snackBarRef", "hasAction", "_SimpleSnackBar", "_SimpleSnackBar_Factory", "ɵɵdefineComponent", "exportAs", "decls", "vars", "consts", "template", "_SimpleSnackBar_Template", "ɵɵtemplate", "_SimpleSnackBar_Conditional_2_Template", "message", "ɵɵconditional", "dependencies", "styles", "encapsulation", "changeDetection", "None", "OnPush", "imports", "ENTER_ANIMATION", "EXIT_ANIMATION", "MatSnackBarContainer", "optional", "Set", "getId", "portal", "_assertNotAttached", "result", "_portalOutlet", "attachDomPortal", "_afterPortalAttached", "config", "snackBarConfig", "politeness", "announcementMessage", "_live", "_platform", "FIREFOX", "_role", "_rendersRef", "_renders", "manualCleanup", "attachComponentPortal", "attachTemplatePortal", "onAnimationEnd", "animationName", "_completeExit", "_enterFallback", "_ngZone", "run", "enter", "_destroyed", "_animationState", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "_screenReaderAnnounce", "_animationsDisabled", "pipe", "queueMicrotask", "_elementRef", "nativeElement", "classList", "add", "undefined", "setAttribute", "_announceTimeoutId", "_exitFallback", "ngOnDestroy", "_clearFromModals", "destroy", "element", "panelClasses", "panelClass", "Array", "isArray", "for<PERSON>ach", "cssClass", "_exposeToModals", "label", "_label", "labelClass", "toggle", "querySelector", "id", "_liveElementId", "modals", "_document", "querySelectorAll", "i", "length", "modal", "ariaOwns", "getAttribute", "_trackedModals", "indexOf", "newValue", "replace", "trim", "removeAttribute", "clear", "has<PERSON>tta<PERSON>", "Error", "runOutsideAngular", "inertElement", "liveElement", "_focusedElement", "focusedElement", "<PERSON><PERSON><PERSON><PERSON>", "document", "activeElement", "HTMLElement", "contains", "append<PERSON><PERSON><PERSON>", "focus", "_onAnnounce", "_announce<PERSON><PERSON>y", "_MatSnackBarContainer", "_MatSnackBarContainer_Factory", "viewQuery", "_MatSnackBarContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "hostBindings", "_MatSnackBarContainer_HostBindings", "_MatSnackBarContainer_animationend_HostBindingHandler", "$event", "_MatSnackBarContainer_animationcancel_HostBindingHandler", "ɵɵclassProp", "features", "ɵɵInheritDefinitionFeature", "_MatSnackBarContainer_Template", "ɵɵelement", "ɵɵattribute", "<PERSON><PERSON><PERSON>", "static", "MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY", "MAT_SNACK_BAR_DEFAULT_OPTIONS", "providedIn", "factory", "MatSnackBar", "_openedSnackBarRef", "parent", "_parentSnackBar", "_snackBarRefAtThisLevel", "value", "skipSelf", "openFromComponent", "component", "_attach", "openFromTemplate", "open", "_config", "_objectSpread", "_defaultConfig", "simpleSnackBarComponent", "_attachSnackBarContainer", "overlayRef", "userInjector", "viewContainerRef", "injector", "create", "_injector", "providers", "provide", "useValue", "containerPortal", "snackBarContainerComponent", "containerRef", "attach", "instance", "content", "userConfig", "_createOverlay", "container", "$implicit", "_createInjector", "contentRef", "_breakpointObserver", "observe", "HandsetPortrait", "detachments", "state", "overlayElement", "handsetCssClass", "matches", "announce", "_animateSnackBar", "overlayConfig", "direction", "positionStrategy", "_overlay", "position", "global", "isRtl", "isLeft", "horizontalPosition", "isRight", "left", "right", "centerHorizontally", "verticalPosition", "top", "bottom", "_MatSnackBar", "_MatSnackBar_Factory", "ɵɵdefineInjectable", "token", "ɵfac", "DIRECTIVES", "MatSnackBarModule", "_MatSnackBarModule", "_MatSnackBarModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "matSnackBarAnimations", "snackBarState", "name", "transform", "opacity", "offset", "expr", "animation", "timings", "options"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/snack-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, NgZone, ElementRef, ChangeDetectorRef, ANIMATION_MODULE_TYPE, afterRender, ViewChild, Injector, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, of } from 'rxjs';\nimport { MatButton, MatButtonModule } from './button.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { _IdGenerator, LiveAnnouncer } from '@angular/cdk/a11y';\nimport { Platform } from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport './icon-button-D1J0zeqv.mjs';\nimport '@angular/cdk/private';\nimport './ripple-loader-Ce3DAhPW.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/coercion';\nimport './structural-styles-BQUT6wsL.mjs';\nimport './index-SYVYjXwK.mjs';\nimport '@angular/cdk/bidi';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n    _overlayRef;\n    /** The instance of the component making up the content of the snack bar. */\n    instance;\n    /**\n     * The instance of the component making up the content of the snack bar.\n     * @docs-private\n     */\n    containerInstance;\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n    _afterDismissed = new Subject();\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n    _afterOpened = new Subject();\n    /** Subject for notifying the user that the snack bar action was called. */\n    _onAction = new Subject();\n    /**\n     * Timeout ID for the duration setTimeout call. Used to clear the timeout if the snackbar is\n     * dismissed before the duration passes.\n     */\n    _durationTimeoutId;\n    /** Whether the snack bar was dismissed using the action button. */\n    _dismissedByAction = false;\n    constructor(containerInstance, _overlayRef) {\n        this._overlayRef = _overlayRef;\n        this.containerInstance = containerInstance;\n        containerInstance._onExit.subscribe(() => this._finishDismiss());\n    }\n    /** Dismisses the snack bar. */\n    dismiss() {\n        if (!this._afterDismissed.closed) {\n            this.containerInstance.exit();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /** Marks the snackbar action clicked. */\n    dismissWithAction() {\n        if (!this._onAction.closed) {\n            this._dismissedByAction = true;\n            this._onAction.next();\n            this._onAction.complete();\n            this.dismiss();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /**\n     * Marks the snackbar action clicked.\n     * @deprecated Use `dismissWithAction` instead.\n     * @breaking-change 8.0.0\n     */\n    closeWithAction() {\n        this.dismissWithAction();\n    }\n    /** Dismisses the snack bar after some duration */\n    _dismissAfter(duration) {\n        // Note that we need to cap the duration to the maximum value for setTimeout, because\n        // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n        this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n    }\n    /** Marks the snackbar as opened */\n    _open() {\n        if (!this._afterOpened.closed) {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        }\n    }\n    /** Cleans up the DOM after closing. */\n    _finishDismiss() {\n        this._overlayRef.dispose();\n        if (!this._onAction.closed) {\n            this._onAction.complete();\n        }\n        this._afterDismissed.next({ dismissedByAction: this._dismissedByAction });\n        this._afterDismissed.complete();\n        this._dismissedByAction = false;\n    }\n    /** Gets an observable that is notified when the snack bar is finished closing. */\n    afterDismissed() {\n        return this._afterDismissed;\n    }\n    /** Gets an observable that is notified when the snack bar has opened and appeared. */\n    afterOpened() {\n        return this.containerInstance._onEnter;\n    }\n    /** Gets an observable that is notified when the snack bar action is called. */\n    onAction() {\n        return this._onAction;\n    }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    politeness = 'assertive';\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n    announcementMessage = '';\n    /**\n     * The view container that serves as the parent for the snackbar for the purposes of dependency\n     * injection. Note: this does not affect where the snackbar is inserted in the DOM.\n     */\n    viewContainerRef;\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n    duration = 0;\n    /** Extra CSS classes to be added to the snack bar container. */\n    panelClass;\n    /** Text layout direction for the snack bar. */\n    direction;\n    /** Data being injected into the child component. */\n    data = null;\n    /** The horizontal position to place the snack bar. */\n    horizontalPosition = 'center';\n    /** The vertical position to place the snack bar. */\n    verticalPosition = 'bottom';\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatSnackBarLabel, isStandalone: true, selector: \"[matSnackBarLabel]\", host: { classAttribute: \"mat-mdc-snack-bar-label mdc-snackbar__label\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarLabel]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarActions, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatSnackBarActions, isStandalone: true, selector: \"[matSnackBarActions]\", host: { classAttribute: \"mat-mdc-snack-bar-actions mdc-snackbar__actions\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarActions]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarAction, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatSnackBarAction, isStandalone: true, selector: \"[matSnackBarAction]\", host: { classAttribute: \"mat-mdc-snack-bar-action mdc-snackbar__action\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarAction, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarAction]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action',\n                    },\n                }]\n        }] });\n\nclass SimpleSnackBar {\n    snackBarRef = inject(MatSnackBarRef);\n    data = inject(MAT_SNACK_BAR_DATA);\n    constructor() { }\n    /** Performs the action on the snack bar. */\n    action() {\n        this.snackBarRef.dismissWithAction();\n    }\n    /** If the action button should be shown. */\n    get hasAction() {\n        return !!this.data.action;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: SimpleSnackBar, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: SimpleSnackBar, isStandalone: true, selector: \"simple-snack-bar\", host: { classAttribute: \"mat-mdc-simple-snack-bar\" }, exportAs: [\"matSnackBar\"], ngImport: i0, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"], dependencies: [{ kind: \"component\", type: MatButton, selector: \"    button[mat-button], button[mat-raised-button], button[mat-flat-button],    button[mat-stroked-button]  \", exportAs: [\"matButton\"] }, { kind: \"directive\", type: MatSnackBarLabel, selector: \"[matSnackBarLabel]\" }, { kind: \"directive\", type: MatSnackBarActions, selector: \"[matSnackBarActions]\" }, { kind: \"directive\", type: MatSnackBarAction, selector: \"[matSnackBarAction]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: SimpleSnackBar, decorators: [{\n            type: Component,\n            args: [{ selector: 'simple-snack-bar', exportAs: 'matSnackBar', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction], host: {\n                        'class': 'mat-mdc-simple-snack-bar',\n                    }, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"] }]\n        }], ctorParameters: () => [] });\n\nconst ENTER_ANIMATION = '_mat-snack-bar-enter';\nconst EXIT_ANIMATION = '_mat-snack-bar-exit';\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _platform = inject(Platform);\n    _rendersRef;\n    _animationsDisabled = inject(ANIMATION_MODULE_TYPE, { optional: true }) === 'NoopAnimations';\n    snackBarConfig = inject(MatSnackBarConfig);\n    _document = inject(DOCUMENT);\n    _trackedModals = new Set();\n    _enterFallback;\n    _exitFallback;\n    _renders = new Subject();\n    /** The number of milliseconds to wait before announcing the snack bar's content. */\n    _announceDelay = 150;\n    /** The timeout for announcing the snack bar's content. */\n    _announceTimeoutId;\n    /** Whether the component has been destroyed. */\n    _destroyed = false;\n    /** The portal outlet inside of this container into which the snack bar content will be loaded. */\n    _portalOutlet;\n    /** Subject for notifying that the snack bar has announced to screen readers. */\n    _onAnnounce = new Subject();\n    /** Subject for notifying that the snack bar has exited from view. */\n    _onExit = new Subject();\n    /** Subject for notifying that the snack bar has finished entering the view. */\n    _onEnter = new Subject();\n    /** The state of the snack bar animations. */\n    _animationState = 'void';\n    /** aria-live value for the live region. */\n    _live;\n    /**\n     * Element that will have the `mdc-snackbar__label` class applied if the attached component\n     * or template does not have it. This ensures that the appropriate structure, typography, and\n     * color is applied to the attached view.\n     */\n    _label;\n    /**\n     * Role of the live region. This is only for Firefox as there is a known issue where Firefox +\n     * JAWS does not read out aria-live message.\n     */\n    _role;\n    /** Unique ID of the aria-live element. */\n    _liveElementId = inject(_IdGenerator).getId('mat-snack-bar-container-live-');\n    constructor() {\n        super();\n        const config = this.snackBarConfig;\n        // Use aria-live rather than a live role like 'alert' or 'status'\n        // because NVDA and JAWS have show inconsistent behavior with live roles.\n        if (config.politeness === 'assertive' && !config.announcementMessage) {\n            this._live = 'assertive';\n        }\n        else if (config.politeness === 'off') {\n            this._live = 'off';\n        }\n        else {\n            this._live = 'polite';\n        }\n        // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n        // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n        if (this._platform.FIREFOX) {\n            if (this._live === 'polite') {\n                this._role = 'status';\n            }\n            if (this._live === 'assertive') {\n                this._role = 'alert';\n            }\n        }\n        // Note: ideally we'd just do an `afterNextRender` in the places where we need to delay\n        // something, however in some cases (TestBed teardown) the injector can be destroyed at an\n        // unexpected time, causing the `afterRender` to fail.\n        this._rendersRef = afterRender(() => this._renders.next(), { manualCleanup: true });\n    }\n    /** Attach a component portal as content to this snack bar container. */\n    attachComponentPortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /** Attach a template portal as content to this snack bar container. */\n    attachTemplatePortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /**\n     * Attaches a DOM portal to the snack bar container.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachDomPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    };\n    /** Handle end of animations, updating the state of the snackbar. */\n    onAnimationEnd(animationName) {\n        if (animationName === EXIT_ANIMATION) {\n            this._completeExit();\n        }\n        else if (animationName === ENTER_ANIMATION) {\n            clearTimeout(this._enterFallback);\n            this._ngZone.run(() => {\n                this._onEnter.next();\n                this._onEnter.complete();\n            });\n        }\n    }\n    /** Begin animation of snack bar entrance into view. */\n    enter() {\n        if (!this._destroyed) {\n            this._animationState = 'visible';\n            // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n            // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n            this._changeDetectorRef.markForCheck();\n            this._changeDetectorRef.detectChanges();\n            this._screenReaderAnnounce();\n            if (this._animationsDisabled) {\n                this._renders.pipe(take(1)).subscribe(() => {\n                    this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(ENTER_ANIMATION)));\n                });\n            }\n            else {\n                clearTimeout(this._enterFallback);\n                this._enterFallback = setTimeout(() => {\n                    // The snack bar will stay invisible if it fails to animate. Add a fallback class so it\n                    // becomes visible. This can happen in some apps that do `* {animation: none !important}`.\n                    this._elementRef.nativeElement.classList.add('mat-snack-bar-fallback-visible');\n                    this.onAnimationEnd(ENTER_ANIMATION);\n                }, 200);\n            }\n        }\n    }\n    /** Begin animation of the snack bar exiting from view. */\n    exit() {\n        if (this._destroyed) {\n            return of(undefined);\n        }\n        // It's common for snack bars to be opened by random outside calls like HTTP requests or\n        // errors. Run inside the NgZone to ensure that it functions correctly.\n        this._ngZone.run(() => {\n            // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n            // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n            // `MatSnackBar.open`).\n            this._animationState = 'hidden';\n            this._changeDetectorRef.markForCheck();\n            // Mark this element with an 'exit' attribute to indicate that the snackbar has\n            // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n            // test harness.\n            this._elementRef.nativeElement.setAttribute('mat-exit', '');\n            // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n            // long enough to visually read it either, so clear the timeout for announcing.\n            clearTimeout(this._announceTimeoutId);\n            if (this._animationsDisabled) {\n                this._renders.pipe(take(1)).subscribe(() => {\n                    this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(EXIT_ANIMATION)));\n                });\n            }\n            else {\n                clearTimeout(this._exitFallback);\n                this._exitFallback = setTimeout(() => this.onAnimationEnd(EXIT_ANIMATION), 200);\n            }\n        });\n        return this._onExit;\n    }\n    /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n    ngOnDestroy() {\n        this._destroyed = true;\n        this._clearFromModals();\n        this._completeExit();\n        this._renders.complete();\n        this._rendersRef.destroy();\n    }\n    _completeExit() {\n        clearTimeout(this._exitFallback);\n        queueMicrotask(() => {\n            this._onExit.next();\n            this._onExit.complete();\n        });\n    }\n    /**\n     * Called after the portal contents have been attached. Can be\n     * used to modify the DOM once it's guaranteed to be in place.\n     */\n    _afterPortalAttached() {\n        const element = this._elementRef.nativeElement;\n        const panelClasses = this.snackBarConfig.panelClass;\n        if (panelClasses) {\n            if (Array.isArray(panelClasses)) {\n                // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n                panelClasses.forEach(cssClass => element.classList.add(cssClass));\n            }\n            else {\n                element.classList.add(panelClasses);\n            }\n        }\n        this._exposeToModals();\n        // Check to see if the attached component or template uses the MDC template structure,\n        // specifically the MDC label. If not, the container should apply the MDC label class to this\n        // component's label container, which will apply MDC's label styles to the attached view.\n        const label = this._label.nativeElement;\n        const labelClass = 'mdc-snackbar__label';\n        label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live element if there is an\n     * `aria-modal` and the live element is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live element.\n     */\n    _exposeToModals() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n        // `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const id = this._liveElementId;\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            this._trackedModals.add(modal);\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    /** Clears the references to the live element from any modals it was added to. */\n    _clearFromModals() {\n        this._trackedModals.forEach(modal => {\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (ariaOwns) {\n                const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n                if (newValue.length > 0) {\n                    modal.setAttribute('aria-owns', newValue);\n                }\n                else {\n                    modal.removeAttribute('aria-owns');\n                }\n            }\n        });\n        this._trackedModals.clear();\n    }\n    /** Asserts that no content is already attached to the container. */\n    _assertNotAttached() {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Attempting to attach snack bar content after content is already attached');\n        }\n    }\n    /**\n     * Starts a timeout to move the snack bar content to the live region so screen readers will\n     * announce it.\n     */\n    _screenReaderAnnounce() {\n        if (this._announceTimeoutId) {\n            return;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            this._announceTimeoutId = setTimeout(() => {\n                if (this._destroyed) {\n                    return;\n                }\n                const element = this._elementRef.nativeElement;\n                const inertElement = element.querySelector('[aria-hidden]');\n                const liveElement = element.querySelector('[aria-live]');\n                if (inertElement && liveElement) {\n                    // If an element in the snack bar content is focused before being moved\n                    // track it and restore focus after moving to the live region.\n                    let focusedElement = null;\n                    if (this._platform.isBrowser &&\n                        document.activeElement instanceof HTMLElement &&\n                        inertElement.contains(document.activeElement)) {\n                        focusedElement = document.activeElement;\n                    }\n                    inertElement.removeAttribute('aria-hidden');\n                    liveElement.appendChild(inertElement);\n                    focusedElement?.focus();\n                    this._onAnnounce.next();\n                    this._onAnnounce.complete();\n                }\n            }, this._announceDelay);\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarContainer, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatSnackBarContainer, isStandalone: true, selector: \"mat-snack-bar-container\", host: { listeners: { \"animationend\": \"onAnimationEnd($event.animationName)\", \"animationcancel\": \"onAnimationEnd($event.animationName)\" }, properties: { \"class.mat-snack-bar-container-enter\": \"_animationState === \\\"visible\\\"\", \"class.mat-snack-bar-container-exit\": \"_animationState === \\\"hidden\\\"\", \"class.mat-snack-bar-container-animations-enabled\": \"!_animationsDisabled\" }, classAttribute: \"mdc-snackbar mat-mdc-snack-bar-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }, { propertyName: \"_label\", first: true, predicate: [\"label\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-snack-bar-container', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, imports: [CdkPortalOutlet], host: {\n                        'class': 'mdc-snackbar mat-mdc-snack-bar-container',\n                        '[class.mat-snack-bar-container-enter]': '_animationState === \"visible\"',\n                        '[class.mat-snack-bar-container-exit]': '_animationState === \"hidden\"',\n                        '[class.mat-snack-bar-container-animations-enabled]': '!_animationsDisabled',\n                        '(animationend)': 'onAnimationEnd($event.animationName)',\n                        '(animationcancel)': 'onAnimationEnd($event.animationName)',\n                    }, template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }], _label: [{\n                type: ViewChild,\n                args: ['label', { static: true }]\n            }] } });\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n    return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n    providedIn: 'root',\n    factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n    _overlay = inject(Overlay);\n    _live = inject(LiveAnnouncer);\n    _injector = inject(Injector);\n    _breakpointObserver = inject(BreakpointObserver);\n    _parentSnackBar = inject(MatSnackBar, { optional: true, skipSelf: true });\n    _defaultConfig = inject(MAT_SNACK_BAR_DEFAULT_OPTIONS);\n    /**\n     * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n     * If there is a parent snack-bar service, all operations should delegate to that parent\n     * via `_openedSnackBarRef`.\n     */\n    _snackBarRefAtThisLevel = null;\n    /** The component that should be rendered as the snack bar's simple component. */\n    simpleSnackBarComponent = SimpleSnackBar;\n    /** The container component that attaches the provided template or component. */\n    snackBarContainerComponent = MatSnackBarContainer;\n    /** The CSS class to apply for handset mode. */\n    handsetCssClass = 'mat-mdc-snack-bar-handset';\n    /** Reference to the currently opened snackbar at *any* level. */\n    get _openedSnackBarRef() {\n        const parent = this._parentSnackBar;\n        return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n    }\n    set _openedSnackBarRef(value) {\n        if (this._parentSnackBar) {\n            this._parentSnackBar._openedSnackBarRef = value;\n        }\n        else {\n            this._snackBarRefAtThisLevel = value;\n        }\n    }\n    constructor() { }\n    /**\n     * Creates and dispatches a snack bar with a custom component for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param component Component to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromComponent(component, config) {\n        return this._attach(component, config);\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom template for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param template Template to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromTemplate(template, config) {\n        return this._attach(template, config);\n    }\n    /**\n     * Opens a snackbar with a message and an optional action.\n     * @param message The message to show in the snackbar.\n     * @param action The label for the snackbar action.\n     * @param config Additional configuration options for the snackbar.\n     */\n    open(message, action = '', config) {\n        const _config = { ...this._defaultConfig, ...config };\n        // Since the user doesn't have access to the component, we can\n        // override the data to pass in our own message and action.\n        _config.data = { message, action };\n        // Since the snack bar has `role=\"alert\"`, we don't\n        // want to announce the same message twice.\n        if (_config.announcementMessage === message) {\n            _config.announcementMessage = undefined;\n        }\n        return this.openFromComponent(this.simpleSnackBarComponent, _config);\n    }\n    /**\n     * Dismisses the currently-visible snack bar.\n     */\n    dismiss() {\n        if (this._openedSnackBarRef) {\n            this._openedSnackBarRef.dismiss();\n        }\n    }\n    ngOnDestroy() {\n        // Only dismiss the snack bar at the current level on destroy.\n        if (this._snackBarRefAtThisLevel) {\n            this._snackBarRefAtThisLevel.dismiss();\n        }\n    }\n    /**\n     * Attaches the snack bar container component to the overlay.\n     */\n    _attachSnackBarContainer(overlayRef, config) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        const injector = Injector.create({\n            parent: userInjector || this._injector,\n            providers: [{ provide: MatSnackBarConfig, useValue: config }],\n        });\n        const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n        const containerRef = overlayRef.attach(containerPortal);\n        containerRef.instance.snackBarConfig = config;\n        return containerRef.instance;\n    }\n    /**\n     * Places a new component or a template as the content of the snack bar container.\n     */\n    _attach(content, userConfig) {\n        const config = { ...new MatSnackBarConfig(), ...this._defaultConfig, ...userConfig };\n        const overlayRef = this._createOverlay(config);\n        const container = this._attachSnackBarContainer(overlayRef, config);\n        const snackBarRef = new MatSnackBarRef(container, overlayRef);\n        if (content instanceof TemplateRef) {\n            const portal = new TemplatePortal(content, null, {\n                $implicit: config.data,\n                snackBarRef,\n            });\n            snackBarRef.instance = container.attachTemplatePortal(portal);\n        }\n        else {\n            const injector = this._createInjector(config, snackBarRef);\n            const portal = new ComponentPortal(content, undefined, injector);\n            const contentRef = container.attachComponentPortal(portal);\n            // We can't pass this via the injector, because the injector is created earlier.\n            snackBarRef.instance = contentRef.instance;\n        }\n        // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n        // appropriate. This class is applied to the overlay element because the overlay must expand to\n        // fill the width of the screen for full width snackbars.\n        this._breakpointObserver\n            .observe(Breakpoints.HandsetPortrait)\n            .pipe(takeUntil(overlayRef.detachments()))\n            .subscribe(state => {\n            overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n        });\n        if (config.announcementMessage) {\n            // Wait until the snack bar contents have been announced then deliver this message.\n            container._onAnnounce.subscribe(() => {\n                this._live.announce(config.announcementMessage, config.politeness);\n            });\n        }\n        this._animateSnackBar(snackBarRef, config);\n        this._openedSnackBarRef = snackBarRef;\n        return this._openedSnackBarRef;\n    }\n    /** Animates the old snack bar out and the new one in. */\n    _animateSnackBar(snackBarRef, config) {\n        // When the snackbar is dismissed, clear the reference to it.\n        snackBarRef.afterDismissed().subscribe(() => {\n            // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n            if (this._openedSnackBarRef == snackBarRef) {\n                this._openedSnackBarRef = null;\n            }\n            if (config.announcementMessage) {\n                this._live.clear();\n            }\n        });\n        // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n        if (config.duration && config.duration > 0) {\n            snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n        }\n        if (this._openedSnackBarRef) {\n            // If a snack bar is already in view, dismiss it and enter the\n            // new snack bar after exit animation is complete.\n            this._openedSnackBarRef.afterDismissed().subscribe(() => {\n                snackBarRef.containerInstance.enter();\n            });\n            this._openedSnackBarRef.dismiss();\n        }\n        else {\n            // If no snack bar is in view, enter the new snack bar.\n            snackBarRef.containerInstance.enter();\n        }\n    }\n    /**\n     * Creates a new overlay and places it in the correct location.\n     * @param config The user-specified snack bar config.\n     */\n    _createOverlay(config) {\n        const overlayConfig = new OverlayConfig();\n        overlayConfig.direction = config.direction;\n        let positionStrategy = this._overlay.position().global();\n        // Set horizontal position.\n        const isRtl = config.direction === 'rtl';\n        const isLeft = config.horizontalPosition === 'left' ||\n            (config.horizontalPosition === 'start' && !isRtl) ||\n            (config.horizontalPosition === 'end' && isRtl);\n        const isRight = !isLeft && config.horizontalPosition !== 'center';\n        if (isLeft) {\n            positionStrategy.left('0');\n        }\n        else if (isRight) {\n            positionStrategy.right('0');\n        }\n        else {\n            positionStrategy.centerHorizontally();\n        }\n        // Set horizontal position.\n        if (config.verticalPosition === 'top') {\n            positionStrategy.top('0');\n        }\n        else {\n            positionStrategy.bottom('0');\n        }\n        overlayConfig.positionStrategy = positionStrategy;\n        return this._overlay.create(overlayConfig);\n    }\n    /**\n     * Creates an injector to be used inside of a snack bar component.\n     * @param config Config that was used to create the snack bar.\n     * @param snackBarRef Reference to the snack bar.\n     */\n    _createInjector(config, snackBarRef) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        return Injector.create({\n            parent: userInjector || this._injector,\n            providers: [\n                { provide: MatSnackBarRef, useValue: snackBarRef },\n                { provide: MAT_SNACK_BAR_DATA, useValue: config.data },\n            ],\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBar, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBar, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBar, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarModule, imports: [OverlayModule,\n            PortalModule,\n            MatButtonModule,\n            MatCommonModule,\n            SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction], exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarModule, providers: [MatSnackBar], imports: [OverlayModule,\n            PortalModule,\n            MatButtonModule,\n            MatCommonModule,\n            SimpleSnackBar, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        OverlayModule,\n                        PortalModule,\n                        MatButtonModule,\n                        MatCommonModule,\n                        SimpleSnackBar,\n                        ...DIRECTIVES,\n                    ],\n                    exports: [MatCommonModule, ...DIRECTIVES],\n                    providers: [MatSnackBar],\n                }]\n        }] });\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSnackBarAnimations = {\n    // Represents\n    // trigger('state', [\n    //   state(\n    //     'void, hidden',\n    //     style({\n    //       transform: 'scale(0.8)',\n    //       opacity: 0,\n    //     }),\n    //   ),\n    //   state(\n    //     'visible',\n    //     style({\n    //       transform: 'scale(1)',\n    //       opacity: 1,\n    //     }),\n    //   ),\n    //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n    //   transition(\n    //     '* => void, * => hidden',\n    //     animate(\n    //       '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n    //       style({\n    //         opacity: 0,\n    //       }),\n    //     ),\n    //   ),\n    // ])\n    /** Animation that shows and hides a snack bar. */\n    snackBarState: {\n        type: 7,\n        name: 'state',\n        'definitions': [\n            {\n                type: 0,\n                name: 'void, hidden',\n                styles: { type: 6, styles: { transform: 'scale(0.8)', opacity: 0 }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'visible',\n                styles: { type: 6, styles: { transform: 'scale(1)', opacity: 1 }, offset: null },\n            },\n            {\n                type: 1,\n                expr: '* => visible',\n                animation: { type: 4, styles: null, timings: '150ms cubic-bezier(0, 0, 0.2, 1)' },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => void, * => hidden',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                    timings: '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };\n//# sourceMappingURL=snack-bar.mjs.map\n"], "mappings": ";;;;;gBAwJiFA,EAAE,CAAAC,gBAAA;IAAFD,EAAE,CAAAE,cAAA,YAmDsR,CAAC,eAA+D,CAAC;IAnDzVF,EAAE,CAAAG,UAAA,mBAAAC,+DAAA;MAAFJ,EAAE,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAFP,EAAE,CAAAQ,aAAA;MAAA,OAAFR,EAAE,CAAAS,WAAA,CAmD4UF,MAAA,CAAAG,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IAnDxVV,EAAE,CAAAW,MAAA,EAmDmX,CAAC;IAnDtXX,EAAE,CAAAY,YAAA,CAmD4X,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAN,MAAA,GAnDzYP,EAAE,CAAAQ,aAAA;IAAFR,EAAE,CAAAc,SAAA,EAmDmX,CAAC;IAnDtXd,EAAE,CAAAe,kBAAA,MAAAR,MAAA,CAAAS,IAAA,CAAAN,MAAA,KAmDmX,CAAC;EAAA;AAAA;AAAA,MAAAO,GAAA;AAAA,SAAAC,6CAAAL,EAAA,EAAAM,GAAA;AA3Mvc,OAAO,KAAKnB,EAAE,MAAM,eAAe;AACnC,SAASoB,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC3P,SAASC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,eAAe,QAAQ,cAAc;AACzD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACtH,SAASC,YAAY,EAAEC,aAAa,QAAQ,mBAAmB;AAC/D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAChD,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,qBAAqB;AACrE,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AAC5E,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,4BAA4B;AACnC,OAAO,sBAAsB;AAC7B,OAAO,8BAA8B;AACrC,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,kCAAkC;AACzC,OAAO,sBAAsB;AAC7B,OAAO,mBAAmB;;AAE1B;AACA,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AACvC;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EAsBjBC,WAAWA,CAACC,iBAAiB,EAAEC,WAAW,EAAE;IAAAC,eAAA;IApB5C;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA,0BACkB,IAAI7B,OAAO,CAAC,CAAC;IAC/B;IAAA6B,eAAA,uBACe,IAAI7B,OAAO,CAAC,CAAC;IAC5B;IAAA6B,eAAA,oBACY,IAAI7B,OAAO,CAAC,CAAC;IACzB;AACJ;AACA;AACA;IAHI6B,eAAA;IAKA;IAAAA,eAAA,6BACqB,KAAK;IAEtB,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;IAC1CA,iBAAiB,CAACG,OAAO,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;EACpE;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACC,eAAe,CAACC,MAAM,EAAE;MAC9B,IAAI,CAACR,iBAAiB,CAACS,IAAI,CAAC,CAAC;IACjC;IACAC,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;EACzC;EACA;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACC,SAAS,CAACL,MAAM,EAAE;MACxB,IAAI,CAACM,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACD,SAAS,CAACE,IAAI,CAAC,CAAC;MACrB,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAAC;MACzB,IAAI,CAACV,OAAO,CAAC,CAAC;IAClB;IACAI,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;EACIM,eAAeA,CAAA,EAAG;IACd,IAAI,CAACL,iBAAiB,CAAC,CAAC;EAC5B;EACA;EACAM,aAAaA,CAACC,QAAQ,EAAE;IACpB;IACA;IACA,IAAI,CAACR,kBAAkB,GAAGS,UAAU,CAAC,MAAM,IAAI,CAACd,OAAO,CAAC,CAAC,EAAEV,IAAI,CAACyB,GAAG,CAACF,QAAQ,EAAExB,WAAW,CAAC,CAAC;EAC/F;EACA;EACA2B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACC,YAAY,CAACf,MAAM,EAAE;MAC3B,IAAI,CAACe,YAAY,CAACR,IAAI,CAAC,CAAC;MACxB,IAAI,CAACQ,YAAY,CAACP,QAAQ,CAAC,CAAC;IAChC;EACJ;EACA;EACAX,cAAcA,CAAA,EAAG;IACb,IAAI,CAACJ,WAAW,CAACuB,OAAO,CAAC,CAAC;IAC1B,IAAI,CAAC,IAAI,CAACX,SAAS,CAACL,MAAM,EAAE;MACxB,IAAI,CAACK,SAAS,CAACG,QAAQ,CAAC,CAAC;IAC7B;IACA,IAAI,CAACT,eAAe,CAACQ,IAAI,CAAC;MAAEU,iBAAiB,EAAE,IAAI,CAACX;IAAmB,CAAC,CAAC;IACzE,IAAI,CAACP,eAAe,CAACS,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACF,kBAAkB,GAAG,KAAK;EACnC;EACA;EACAY,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACnB,eAAe;EAC/B;EACA;EACAoB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC3B,iBAAiB,CAAC4B,QAAQ;EAC1C;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAChB,SAAS;EACzB;AACJ;;AAEA;AACA,MAAMiB,kBAAkB,GAAG,IAAIzE,cAAc,CAAC,iBAAiB,CAAC;AAChE;AACA;AACA;AACA,MAAM0E,iBAAiB,CAAC;EAAAhC,YAAA;IACpB;IAAAG,eAAA,qBACa,WAAW;IACxB;AACJ;AACA;AACA;IAHIA,eAAA,8BAIsB,EAAE;IACxB;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA,mBACW,CAAC;IACZ;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,eACO,IAAI;IACX;IAAAA,eAAA,6BACqB,QAAQ;IAC7B;IAAAA,eAAA,2BACmB,QAAQ;EAAA;AAC/B;;AAEA;AACA,MAAM8B,gBAAgB,CAAC;AAGtBC,iBAAA,GAHKD,gBAAgB;AAAA9B,eAAA,CAAhB8B,gBAAgB,wBAAAE,0BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IACiFH,iBAAgB;AAAA;AAAA9B,eAAA,CADjH8B,gBAAgB,8BAI2D/F,EAAE,CAAAmG,iBAAA;EAAAC,IAAA,EAFQL,iBAAgB;EAAAM,SAAA;EAAAC,SAAA;AAAA;AAE3G;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFvG,EAAE,CAAAwG,iBAAA,CAAQT,gBAAgB,EAAc,CAAC;IAC9GK,IAAI,EAAE/E,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMC,kBAAkB,CAAC;AAGxBC,mBAAA,GAHKD,kBAAkB;AAAA3C,eAAA,CAAlB2C,kBAAkB,wBAAAE,4BAAAZ,iBAAA;EAAA,YAAAA,iBAAA,IAC+EU,mBAAkB;AAAA;AAAA3C,eAAA,CADnH2C,kBAAkB,8BAVyD5G,EAAE,CAAAmG,iBAAA;EAAAC,IAAA,EAYQQ,mBAAkB;EAAAP,SAAA;EAAAC,SAAA;AAAA;AAE7G;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAdiFvG,EAAE,CAAAwG,iBAAA,CAcQI,kBAAkB,EAAc,CAAC;IAChHR,IAAI,EAAE/E,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMI,iBAAiB,CAAC;AAGvBC,kBAAA,GAHKD,iBAAiB;AAAA9C,eAAA,CAAjB8C,iBAAiB,wBAAAE,2BAAAf,iBAAA;EAAA,YAAAA,iBAAA,IACgFa,kBAAiB;AAAA;AAAA9C,eAAA,CADlH8C,iBAAiB,8BAxB0D/G,EAAE,CAAAmG,iBAAA;EAAAC,IAAA,EA0BQW,kBAAiB;EAAAV,SAAA;EAAAC,SAAA;AAAA;AAE5G;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5BiFvG,EAAE,CAAAwG,iBAAA,CA4BQO,iBAAiB,EAAc,CAAC;IAC/GX,IAAI,EAAE/E,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMO,cAAc,CAAC;EAGjBpD,WAAWA,CAAA,EAAG;IAAAG,eAAA,sBAFA3C,MAAM,CAACuC,cAAc,CAAC;IAAAI,eAAA,eAC7B3C,MAAM,CAACuE,kBAAkB,CAAC;EACjB;EAChB;EACAnF,MAAMA,CAAA,EAAG;IACL,IAAI,CAACyG,WAAW,CAACxC,iBAAiB,CAAC,CAAC;EACxC;EACA;EACA,IAAIyC,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAACpG,IAAI,CAACN,MAAM;EAC7B;AAGJ;AAAC2G,eAAA,GAdKH,cAAc;AAAAjD,eAAA,CAAdiD,cAAc,wBAAAI,wBAAApB,iBAAA;EAAA,YAAAA,iBAAA,IAYmFgB,eAAc;AAAA;AAAAjD,eAAA,CAZ/GiD,cAAc,8BAtC6DlH,EAAE,CAAAuH,iBAAA;EAAAnB,IAAA,EAmDQc,eAAc;EAAAb,SAAA;EAAAC,SAAA;EAAAkB,QAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yBAAAhH,EAAA,EAAAM,GAAA;IAAA,IAAAN,EAAA;MAnDxBb,EAAE,CAAAE,cAAA,YAmDyM,CAAC;MAnD5MF,EAAE,CAAAW,MAAA,EAmD+N,CAAC;MAnDlOX,EAAE,CAAAY,YAAA,CAmDqO,CAAC;MAnDxOZ,EAAE,CAAA8H,UAAA,IAAAC,sCAAA,gBAmD0P,CAAC;IAAA;IAAA,IAAAlH,EAAA;MAnD7Pb,EAAE,CAAAc,SAAA,CAmD+N,CAAC;MAnDlOd,EAAE,CAAAe,kBAAA,MAAAI,GAAA,CAAAH,IAAA,CAAAgH,OAAA,MAmD+N,CAAC;MAnDlOhI,EAAE,CAAAc,SAAA,CAmDyY,CAAC;MAnD5Yd,EAAE,CAAAiI,aAAA,CAAA9G,GAAA,CAAAiG,SAAA,SAmDyY,CAAC;IAAA;EAAA;EAAAc,YAAA,GAAsG5F,SAAS,EAAiLyD,gBAAgB,EAA+Da,kBAAkB,EAAiEG,iBAAiB;EAAAoB,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAEh7B;EAAA,QAAA9B,SAAA,oBAAAA,SAAA,KArDiFvG,EAAE,CAAAwG,iBAAA,CAqDQU,cAAc,EAAc,CAAC;IAC5Gd,IAAI,EAAE7E,SAAS;IACfkF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEc,QAAQ,EAAE,aAAa;MAAEY,aAAa,EAAE5G,iBAAiB,CAAC8G,IAAI;MAAED,eAAe,EAAE5G,uBAAuB,CAAC8G,MAAM;MAAEC,OAAO,EAAE,CAAClG,SAAS,EAAEyD,gBAAgB,EAAEa,kBAAkB,EAAEG,iBAAiB,CAAC;MAAEJ,IAAI,EAAE;QACjO,OAAO,EAAE;MACb,CAAC;MAAEiB,QAAQ,EAAE,0NAA0N;MAAEO,MAAM,EAAE,CAAC,2CAA2C;IAAE,CAAC;EAC5S,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMM,eAAe,GAAG,sBAAsB;AAC9C,MAAMC,cAAc,GAAG,qBAAqB;AAC5C;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,SAASlG,gBAAgB,CAAC;EA4ChDqB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IAACG,eAAA,kBA5CF3C,MAAM,CAACI,MAAM,CAAC;IAAAuC,eAAA,sBACV3C,MAAM,CAACK,UAAU,CAAC;IAAAsC,eAAA,6BACX3C,MAAM,CAACM,iBAAiB,CAAC;IAAAqC,eAAA,oBAClC3C,MAAM,CAAC0B,QAAQ,CAAC;IAAAiB,eAAA;IAAAA,eAAA,8BAEN3C,MAAM,CAACO,qBAAqB,EAAE;MAAE+G,QAAQ,EAAE;IAAK,CAAC,CAAC,KAAK,gBAAgB;IAAA3E,eAAA,yBAC3E3C,MAAM,CAACwE,iBAAiB,CAAC;IAAA7B,eAAA,oBAC9B3C,MAAM,CAACkB,QAAQ,CAAC;IAAAyB,eAAA,yBACX,IAAI4E,GAAG,CAAC,CAAC;IAAA5E,eAAA;IAAAA,eAAA;IAAAA,eAAA,mBAGf,IAAI7B,OAAO,CAAC,CAAC;IACxB;IAAA6B,eAAA,yBACiB,GAAG;IACpB;IAAAA,eAAA;IAEA;IAAAA,eAAA,qBACa,KAAK;IAClB;IAAAA,eAAA;IAEA;IAAAA,eAAA,sBACc,IAAI7B,OAAO,CAAC,CAAC;IAC3B;IAAA6B,eAAA,kBACU,IAAI7B,OAAO,CAAC,CAAC;IACvB;IAAA6B,eAAA,mBACW,IAAI7B,OAAO,CAAC,CAAC;IACxB;IAAA6B,eAAA,0BACkB,MAAM;IACxB;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA,yBACiB3C,MAAM,CAACwB,YAAY,CAAC,CAACgG,KAAK,CAAC,+BAA+B,CAAC;IA4C5E;AACJ;AACA;AACA;AACA;IAJI7E,eAAA,0BAKmB8E,MAAM,IAAK;MAC1B,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACC,eAAe,CAACJ,MAAM,CAAC;MACzD,IAAI,CAACK,oBAAoB,CAAC,CAAC;MAC3B,OAAOH,MAAM;IACjB,CAAC;IAnDG,MAAMI,MAAM,GAAG,IAAI,CAACC,cAAc;IAClC;IACA;IACA,IAAID,MAAM,CAACE,UAAU,KAAK,WAAW,IAAI,CAACF,MAAM,CAACG,mBAAmB,EAAE;MAClE,IAAI,CAACC,KAAK,GAAG,WAAW;IAC5B,CAAC,MACI,IAAIJ,MAAM,CAACE,UAAU,KAAK,KAAK,EAAE;MAClC,IAAI,CAACE,KAAK,GAAG,KAAK;IACtB,CAAC,MACI;MACD,IAAI,CAACA,KAAK,GAAG,QAAQ;IACzB;IACA;IACA;IACA,IAAI,IAAI,CAACC,SAAS,CAACC,OAAO,EAAE;MACxB,IAAI,IAAI,CAACF,KAAK,KAAK,QAAQ,EAAE;QACzB,IAAI,CAACG,KAAK,GAAG,QAAQ;MACzB;MACA,IAAI,IAAI,CAACH,KAAK,KAAK,WAAW,EAAE;QAC5B,IAAI,CAACG,KAAK,GAAG,OAAO;MACxB;IACJ;IACA;IACA;IACA;IACA,IAAI,CAACC,WAAW,GAAG/H,WAAW,CAAC,MAAM,IAAI,CAACgI,QAAQ,CAAChF,IAAI,CAAC,CAAC,EAAE;MAAEiF,aAAa,EAAE;IAAK,CAAC,CAAC;EACvF;EACA;EACAC,qBAAqBA,CAACjB,MAAM,EAAE;IAC1B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACc,qBAAqB,CAACjB,MAAM,CAAC;IAC/D,IAAI,CAACK,oBAAoB,CAAC,CAAC;IAC3B,OAAOH,MAAM;EACjB;EACA;EACAgB,oBAAoBA,CAAClB,MAAM,EAAE;IACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACe,oBAAoB,CAAClB,MAAM,CAAC;IAC9D,IAAI,CAACK,oBAAoB,CAAC,CAAC;IAC3B,OAAOH,MAAM;EACjB;EAYA;EACAiB,cAAcA,CAACC,aAAa,EAAE;IAC1B,IAAIA,aAAa,KAAKzB,cAAc,EAAE;MAClC,IAAI,CAAC0B,aAAa,CAAC,CAAC;IACxB,CAAC,MACI,IAAID,aAAa,KAAK1B,eAAe,EAAE;MACxChE,YAAY,CAAC,IAAI,CAAC4F,cAAc,CAAC;MACjC,IAAI,CAACC,OAAO,CAACC,GAAG,CAAC,MAAM;QACnB,IAAI,CAAC5E,QAAQ,CAACb,IAAI,CAAC,CAAC;QACpB,IAAI,CAACa,QAAQ,CAACZ,QAAQ,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACA;EACAyF,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MAClB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC;MACA;MACA,IAAI,CAACC,kBAAkB,CAACC,YAAY,CAAC,CAAC;MACtC,IAAI,CAACD,kBAAkB,CAACE,aAAa,CAAC,CAAC;MACvC,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAAC/H,IAAI,CAAC,CAAC,CAAC,CAAC,CAACkB,SAAS,CAAC,MAAM;UACxC,IAAI,CAACmG,OAAO,CAACC,GAAG,CAAC,MAAMU,cAAc,CAAC,MAAM,IAAI,CAACf,cAAc,CAACzB,eAAe,CAAC,CAAC,CAAC;QACtF,CAAC,CAAC;MACN,CAAC,MACI;QACDhE,YAAY,CAAC,IAAI,CAAC4F,cAAc,CAAC;QACjC,IAAI,CAACA,cAAc,GAAGlF,UAAU,CAAC,MAAM;UACnC;UACA;UACA,IAAI,CAAC+F,WAAW,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC9E,IAAI,CAACnB,cAAc,CAACzB,eAAe,CAAC;QACxC,CAAC,EAAE,GAAG,CAAC;MACX;IACJ;EACJ;EACA;EACAjE,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACiG,UAAU,EAAE;MACjB,OAAOpI,EAAE,CAACiJ,SAAS,CAAC;IACxB;IACA;IACA;IACA,IAAI,CAAChB,OAAO,CAACC,GAAG,CAAC,MAAM;MACnB;MACA;MACA;MACA,IAAI,CAACG,eAAe,GAAG,QAAQ;MAC/B,IAAI,CAACC,kBAAkB,CAACC,YAAY,CAAC,CAAC;MACtC;MACA;MACA;MACA,IAAI,CAACM,WAAW,CAACC,aAAa,CAACI,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;MAC3D;MACA;MACA9G,YAAY,CAAC,IAAI,CAAC+G,kBAAkB,CAAC;MACrC,IAAI,IAAI,CAACT,mBAAmB,EAAE;QAC1B,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAAC/H,IAAI,CAAC,CAAC,CAAC,CAAC,CAACkB,SAAS,CAAC,MAAM;UACxC,IAAI,CAACmG,OAAO,CAACC,GAAG,CAAC,MAAMU,cAAc,CAAC,MAAM,IAAI,CAACf,cAAc,CAACxB,cAAc,CAAC,CAAC,CAAC;QACrF,CAAC,CAAC;MACN,CAAC,MACI;QACDjE,YAAY,CAAC,IAAI,CAACgH,aAAa,CAAC;QAChC,IAAI,CAACA,aAAa,GAAGtG,UAAU,CAAC,MAAM,IAAI,CAAC+E,cAAc,CAACxB,cAAc,CAAC,EAAE,GAAG,CAAC;MACnF;IACJ,CAAC,CAAC;IACF,OAAO,IAAI,CAACxE,OAAO;EACvB;EACA;EACAwH,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACkB,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACvB,aAAa,CAAC,CAAC;IACpB,IAAI,CAACN,QAAQ,CAAC/E,QAAQ,CAAC,CAAC;IACxB,IAAI,CAAC8E,WAAW,CAAC+B,OAAO,CAAC,CAAC;EAC9B;EACAxB,aAAaA,CAAA,EAAG;IACZ3F,YAAY,CAAC,IAAI,CAACgH,aAAa,CAAC;IAChCR,cAAc,CAAC,MAAM;MACjB,IAAI,CAAC/G,OAAO,CAACY,IAAI,CAAC,CAAC;MACnB,IAAI,CAACZ,OAAO,CAACa,QAAQ,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIqE,oBAAoBA,CAAA,EAAG;IACnB,MAAMyC,OAAO,GAAG,IAAI,CAACX,WAAW,CAACC,aAAa;IAC9C,MAAMW,YAAY,GAAG,IAAI,CAACxC,cAAc,CAACyC,UAAU;IACnD,IAAID,YAAY,EAAE;MACd,IAAIE,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,EAAE;QAC7B;QACAA,YAAY,CAACI,OAAO,CAACC,QAAQ,IAAIN,OAAO,CAACT,SAAS,CAACC,GAAG,CAACc,QAAQ,CAAC,CAAC;MACrE,CAAC,MACI;QACDN,OAAO,CAACT,SAAS,CAACC,GAAG,CAACS,YAAY,CAAC;MACvC;IACJ;IACA,IAAI,CAACM,eAAe,CAAC,CAAC;IACtB;IACA;IACA;IACA,MAAMC,KAAK,GAAG,IAAI,CAACC,MAAM,CAACnB,aAAa;IACvC,MAAMoB,UAAU,GAAG,qBAAqB;IACxCF,KAAK,CAACjB,SAAS,CAACoB,MAAM,CAACD,UAAU,EAAE,CAACF,KAAK,CAACI,aAAa,CAAC,IAAIF,UAAU,EAAE,CAAC,CAAC;EAC9E;EACA;AACJ;AACA;AACA;AACA;EACIH,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA,MAAMM,EAAE,GAAG,IAAI,CAACC,cAAc;IAC9B,MAAMC,MAAM,GAAG,IAAI,CAACC,SAAS,CAACC,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAME,KAAK,GAAGL,MAAM,CAACG,CAAC,CAAC;MACvB,MAAMG,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAACC,cAAc,CAAC/B,GAAG,CAAC4B,KAAK,CAAC;MAC9B,IAAI,CAACC,QAAQ,EAAE;QACXD,KAAK,CAAC1B,YAAY,CAAC,WAAW,EAAEmB,EAAE,CAAC;MACvC,CAAC,MACI,IAAIQ,QAAQ,CAACG,OAAO,CAACX,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClCO,KAAK,CAAC1B,YAAY,CAAC,WAAW,EAAE2B,QAAQ,GAAG,GAAG,GAAGR,EAAE,CAAC;MACxD;IACJ;EACJ;EACA;EACAf,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACyB,cAAc,CAAClB,OAAO,CAACe,KAAK,IAAI;MACjC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAID,QAAQ,EAAE;QACV,MAAMI,QAAQ,GAAGJ,QAAQ,CAACK,OAAO,CAAC,IAAI,CAACZ,cAAc,EAAE,EAAE,CAAC,CAACa,IAAI,CAAC,CAAC;QACjE,IAAIF,QAAQ,CAACN,MAAM,GAAG,CAAC,EAAE;UACrBC,KAAK,CAAC1B,YAAY,CAAC,WAAW,EAAE+B,QAAQ,CAAC;QAC7C,CAAC,MACI;UACDL,KAAK,CAACQ,eAAe,CAAC,WAAW,CAAC;QACtC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACL,cAAc,CAACM,KAAK,CAAC,CAAC;EAC/B;EACA;EACA1E,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACE,aAAa,CAACyE,WAAW,CAAC,CAAC,KAAK,OAAOpH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrF,MAAMqH,KAAK,CAAC,0EAA0E,CAAC;IAC3F;EACJ;EACA;AACJ;AACA;AACA;EACI9C,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACU,kBAAkB,EAAE;MACzB;IACJ;IACA,IAAI,CAAClB,OAAO,CAACuD,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACrC,kBAAkB,GAAGrG,UAAU,CAAC,MAAM;QACvC,IAAI,IAAI,CAACsF,UAAU,EAAE;UACjB;QACJ;QACA,MAAMoB,OAAO,GAAG,IAAI,CAACX,WAAW,CAACC,aAAa;QAC9C,MAAM2C,YAAY,GAAGjC,OAAO,CAACY,aAAa,CAAC,eAAe,CAAC;QAC3D,MAAMsB,WAAW,GAAGlC,OAAO,CAACY,aAAa,CAAC,aAAa,CAAC;QACxD,IAAIqB,YAAY,IAAIC,WAAW,EAAE;UAAA,IAAAC,eAAA;UAC7B;UACA;UACA,IAAIC,cAAc,GAAG,IAAI;UACzB,IAAI,IAAI,CAACvE,SAAS,CAACwE,SAAS,IACxBC,QAAQ,CAACC,aAAa,YAAYC,WAAW,IAC7CP,YAAY,CAACQ,QAAQ,CAACH,QAAQ,CAACC,aAAa,CAAC,EAAE;YAC/CH,cAAc,GAAGE,QAAQ,CAACC,aAAa;UAC3C;UACAN,YAAY,CAACL,eAAe,CAAC,aAAa,CAAC;UAC3CM,WAAW,CAACQ,WAAW,CAACT,YAAY,CAAC;UACrC,CAAAE,eAAA,GAAAC,cAAc,cAAAD,eAAA,eAAdA,eAAA,CAAgBQ,KAAK,CAAC,CAAC;UACvB,IAAI,CAACC,WAAW,CAAC3J,IAAI,CAAC,CAAC;UACvB,IAAI,CAAC2J,WAAW,CAAC1J,QAAQ,CAAC,CAAC;QAC/B;MACJ,CAAC,EAAE,IAAI,CAAC2J,cAAc,CAAC;IAC3B,CAAC,CAAC;EACN;AAGJ;AAACC,qBAAA,GAnSKhG,oBAAoB;AAAA1E,eAAA,CAApB0E,oBAAoB,wBAAAiG,8BAAA1I,iBAAA;EAAA,YAAAA,iBAAA,IAiS6EyC,qBAAoB;AAAA;AAAA1E,eAAA,CAjSrH0E,oBAAoB,8BAlEuD3I,EAAE,CAAAuH,iBAAA;EAAAnB,IAAA,EAoWQuC,qBAAoB;EAAAtC,SAAA;EAAAwI,SAAA,WAAAC,4BAAAjO,EAAA,EAAAM,GAAA;IAAA,IAAAN,EAAA;MApW9Bb,EAAE,CAAA+O,WAAA,CAoWolBrM,eAAe;MApWrmB1C,EAAE,CAAA+O,WAAA,CAAA9N,GAAA;IAAA;IAAA,IAAAJ,EAAA;MAAA,IAAAmO,EAAA;MAAFhP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAA/N,GAAA,CAAA+H,aAAA,GAAA8F,EAAA,CAAAG,KAAA;MAAFnP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAA/N,GAAA,CAAAmL,MAAA,GAAA0C,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAA7I,SAAA;EAAA8I,QAAA;EAAAC,YAAA,WAAAC,mCAAAzO,EAAA,EAAAM,GAAA;IAAA,IAAAN,EAAA;MAAFb,EAAE,CAAAG,UAAA,0BAAAoP,sDAAAC,MAAA;QAAA,OAoWQrO,GAAA,CAAA+I,cAAA,CAAAsF,MAAA,CAAArF,aAAmC,CAAC;MAAA,CAAjB,CAAC,6BAAAsF,yDAAAD,MAAA;QAAA,OAApBrO,GAAA,CAAA+I,cAAA,CAAAsF,MAAA,CAAArF,aAAmC,CAAC;MAAA,CAAjB,CAAC;IAAA;IAAA,IAAAtJ,EAAA;MApW9Bb,EAAE,CAAA0P,WAAA,kCAAAvO,GAAA,CAAAuJ,eAAA,KAoW4B,SAAD,CAAC,iCAAAvJ,GAAA,CAAAuJ,eAAA,aAAD,CAAC,gDAAAvJ,GAAA,CAAA4J,mBAAD,CAAC;IAAA;EAAA;EAAA4E,QAAA,GApW9B3P,EAAE,CAAA4P,0BAAA;EAAAnI,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAiI,+BAAAhP,EAAA,EAAAM,GAAA;IAAA,IAAAN,EAAA;MAAFb,EAAE,CAAAE,cAAA,YAoWs1B,CAAC,eAA6M,CAAC,YAAqI,CAAC;MApW7qCF,EAAE,CAAA8H,UAAA,IAAA5G,4CAAA,wBAoWitC,CAAC;MApWptClB,EAAE,CAAAY,YAAA,CAoW6tC,CAAC;MApWhuCZ,EAAE,CAAA8P,SAAA,SAoWm7C,CAAC;MApWt7C9P,EAAE,CAAAY,YAAA,CAoW67C,CAAC,CAAO,CAAC;IAAA;IAAA,IAAAC,EAAA;MApWx8Cb,EAAE,CAAAc,SAAA,EAoWy3C,CAAC;MApW53Cd,EAAE,CAAA+P,WAAA,cAAA5O,GAAA,CAAAsI,KAAA,UAAAtI,GAAA,CAAAyI,KAAA,QAAAzI,GAAA,CAAAwL,cAAA;IAAA;EAAA;EAAAzE,YAAA,GAoWs3IxF,eAAe;EAAAyF,MAAA;EAAAC,aAAA;AAAA;AAEx9I;EAAA,QAAA7B,SAAA,oBAAAA,SAAA,KAtWiFvG,EAAE,CAAAwG,iBAAA,CAsWQmC,oBAAoB,EAAc,CAAC;IAClHvC,IAAI,EAAE7E,SAAS;IACfkF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,yBAAyB;MAAE2B,eAAe,EAAE5G,uBAAuB,CAACuO,OAAO;MAAE5H,aAAa,EAAE5G,iBAAiB,CAAC8G,IAAI;MAAEE,OAAO,EAAE,CAAC9F,eAAe,CAAC;MAAEiE,IAAI,EAAE;QAC7J,OAAO,EAAE,0CAA0C;QACnD,uCAAuC,EAAE,+BAA+B;QACxE,sCAAsC,EAAE,8BAA8B;QACtE,oDAAoD,EAAE,sBAAsB;QAC5E,gBAAgB,EAAE,sCAAsC;QACxD,mBAAmB,EAAE;MACzB,CAAC;MAAEiB,QAAQ,EAAE,irBAAirB;MAAEO,MAAM,EAAE,CAAC,q3FAAq3F;IAAE,CAAC;EAC7kH,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEe,aAAa,EAAE,CAAC;MACxD9C,IAAI,EAAErE,SAAS;MACf0E,IAAI,EAAE,CAAC/D,eAAe,EAAE;QAAEuN,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAE3D,MAAM,EAAE,CAAC;MACTlG,IAAI,EAAErE,SAAS;MACf0E,IAAI,EAAE,CAAC,OAAO,EAAE;QAAEwJ,MAAM,EAAE;MAAK,CAAC;IACpC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,SAASC,qCAAqCA,CAAA,EAAG;EAC7C,OAAO,IAAIpK,iBAAiB,CAAC,CAAC;AAClC;AACA;AACA,MAAMqK,6BAA6B,GAAG,IAAI/O,cAAc,CAAC,+BAA+B,EAAE;EACtFgP,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMI,WAAW,CAAC;EAmBd;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe;IACnC,OAAOD,MAAM,GAAGA,MAAM,CAACD,kBAAkB,GAAG,IAAI,CAACG,uBAAuB;EAC5E;EACA,IAAIH,kBAAkBA,CAACI,KAAK,EAAE;IAC1B,IAAI,IAAI,CAACF,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACF,kBAAkB,GAAGI,KAAK;IACnD,CAAC,MACI;MACD,IAAI,CAACD,uBAAuB,GAAGC,KAAK;IACxC;EACJ;EACA7M,WAAWA,CAAA,EAAG;IAAAG,eAAA,mBA/BH3C,MAAM,CAAC+B,OAAO,CAAC;IAAAY,eAAA,gBAClB3C,MAAM,CAACyB,aAAa,CAAC;IAAAkB,eAAA,oBACjB3C,MAAM,CAACU,QAAQ,CAAC;IAAAiC,eAAA,8BACN3C,MAAM,CAAC6B,kBAAkB,CAAC;IAAAc,eAAA,0BAC9B3C,MAAM,CAACgP,WAAW,EAAE;MAAE1H,QAAQ,EAAE,IAAI;MAAEgI,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA3M,eAAA,yBACxD3C,MAAM,CAAC6O,6BAA6B,CAAC;IACtD;AACJ;AACA;AACA;AACA;IAJIlM,eAAA,kCAK0B,IAAI;IAC9B;IAAAA,eAAA,kCAC0BiD,cAAc;IACxC;IAAAjD,eAAA,qCAC6B0E,oBAAoB;IACjD;IAAA1E,eAAA,0BACkB,2BAA2B;EAc7B;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI4M,iBAAiBA,CAACC,SAAS,EAAEzH,MAAM,EAAE;IACjC,OAAO,IAAI,CAAC0H,OAAO,CAACD,SAAS,EAAEzH,MAAM,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2H,gBAAgBA,CAACpJ,QAAQ,EAAEyB,MAAM,EAAE;IAC/B,OAAO,IAAI,CAAC0H,OAAO,CAACnJ,QAAQ,EAAEyB,MAAM,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4H,IAAIA,CAACjJ,OAAO,EAAEtH,MAAM,GAAG,EAAE,EAAE2I,MAAM,EAAE;IAC/B,MAAM6H,OAAO,GAAAC,aAAA,CAAAA,aAAA,KAAQ,IAAI,CAACC,cAAc,GAAK/H,MAAM,CAAE;IACrD;IACA;IACA6H,OAAO,CAAClQ,IAAI,GAAG;MAAEgH,OAAO;MAAEtH;IAAO,CAAC;IAClC;IACA;IACA,IAAIwQ,OAAO,CAAC1H,mBAAmB,KAAKxB,OAAO,EAAE;MACzCkJ,OAAO,CAAC1H,mBAAmB,GAAG8B,SAAS;IAC3C;IACA,OAAO,IAAI,CAACuF,iBAAiB,CAAC,IAAI,CAACQ,uBAAuB,EAAEH,OAAO,CAAC;EACxE;EACA;AACJ;AACA;EACI7M,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACkM,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAAClM,OAAO,CAAC,CAAC;IACrC;EACJ;EACAqH,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,IAAI,CAACgF,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAACrM,OAAO,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;EACIiN,wBAAwBA,CAACC,UAAU,EAAElI,MAAM,EAAE;IACzC,MAAMmI,YAAY,GAAGnI,MAAM,IAAIA,MAAM,CAACoI,gBAAgB,IAAIpI,MAAM,CAACoI,gBAAgB,CAACC,QAAQ;IAC1F,MAAMA,QAAQ,GAAG1P,QAAQ,CAAC2P,MAAM,CAAC;MAC7BnB,MAAM,EAAEgB,YAAY,IAAI,IAAI,CAACI,SAAS;MACtCC,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEhM,iBAAiB;QAAEiM,QAAQ,EAAE1I;MAAO,CAAC;IAChE,CAAC,CAAC;IACF,MAAM2I,eAAe,GAAG,IAAIrP,eAAe,CAAC,IAAI,CAACsP,0BAA0B,EAAE5I,MAAM,CAACoI,gBAAgB,EAAEC,QAAQ,CAAC;IAC/G,MAAMQ,YAAY,GAAGX,UAAU,CAACY,MAAM,CAACH,eAAe,CAAC;IACvDE,YAAY,CAACE,QAAQ,CAAC9I,cAAc,GAAGD,MAAM;IAC7C,OAAO6I,YAAY,CAACE,QAAQ;EAChC;EACA;AACJ;AACA;EACIrB,OAAOA,CAACsB,OAAO,EAAEC,UAAU,EAAE;IACzB,MAAMjJ,MAAM,GAAA8H,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAQ,IAAIrL,iBAAiB,CAAC,CAAC,GAAK,IAAI,CAACsL,cAAc,GAAKkB,UAAU,CAAE;IACpF,MAAMf,UAAU,GAAG,IAAI,CAACgB,cAAc,CAAClJ,MAAM,CAAC;IAC9C,MAAMmJ,SAAS,GAAG,IAAI,CAAClB,wBAAwB,CAACC,UAAU,EAAElI,MAAM,CAAC;IACnE,MAAMlC,WAAW,GAAG,IAAItD,cAAc,CAAC2O,SAAS,EAAEjB,UAAU,CAAC;IAC7D,IAAIc,OAAO,YAAYpQ,WAAW,EAAE;MAChC,MAAM8G,MAAM,GAAG,IAAInG,cAAc,CAACyP,OAAO,EAAE,IAAI,EAAE;QAC7CI,SAAS,EAAEpJ,MAAM,CAACrI,IAAI;QACtBmG;MACJ,CAAC,CAAC;MACFA,WAAW,CAACiL,QAAQ,GAAGI,SAAS,CAACvI,oBAAoB,CAAClB,MAAM,CAAC;IACjE,CAAC,MACI;MACD,MAAM2I,QAAQ,GAAG,IAAI,CAACgB,eAAe,CAACrJ,MAAM,EAAElC,WAAW,CAAC;MAC1D,MAAM4B,MAAM,GAAG,IAAIpG,eAAe,CAAC0P,OAAO,EAAE/G,SAAS,EAAEoG,QAAQ,CAAC;MAChE,MAAMiB,UAAU,GAAGH,SAAS,CAACxI,qBAAqB,CAACjB,MAAM,CAAC;MAC1D;MACA5B,WAAW,CAACiL,QAAQ,GAAGO,UAAU,CAACP,QAAQ;IAC9C;IACA;IACA;IACA;IACA,IAAI,CAACQ,mBAAmB,CACnBC,OAAO,CAACzP,WAAW,CAAC0P,eAAe,CAAC,CACpC9H,IAAI,CAAC9H,SAAS,CAACqO,UAAU,CAACwB,WAAW,CAAC,CAAC,CAAC,CAAC,CACzC5O,SAAS,CAAC6O,KAAK,IAAI;MACpBzB,UAAU,CAAC0B,cAAc,CAAC7H,SAAS,CAACoB,MAAM,CAAC,IAAI,CAAC0G,eAAe,EAAEF,KAAK,CAACG,OAAO,CAAC;IACnF,CAAC,CAAC;IACF,IAAI9J,MAAM,CAACG,mBAAmB,EAAE;MAC5B;MACAgJ,SAAS,CAAC/D,WAAW,CAACtK,SAAS,CAAC,MAAM;QAClC,IAAI,CAACsF,KAAK,CAAC2J,QAAQ,CAAC/J,MAAM,CAACG,mBAAmB,EAAEH,MAAM,CAACE,UAAU,CAAC;MACtE,CAAC,CAAC;IACN;IACA,IAAI,CAAC8J,gBAAgB,CAAClM,WAAW,EAAEkC,MAAM,CAAC;IAC1C,IAAI,CAACkH,kBAAkB,GAAGpJ,WAAW;IACrC,OAAO,IAAI,CAACoJ,kBAAkB;EAClC;EACA;EACA8C,gBAAgBA,CAAClM,WAAW,EAAEkC,MAAM,EAAE;IAClC;IACAlC,WAAW,CAAC1B,cAAc,CAAC,CAAC,CAACtB,SAAS,CAAC,MAAM;MACzC;MACA,IAAI,IAAI,CAACoM,kBAAkB,IAAIpJ,WAAW,EAAE;QACxC,IAAI,CAACoJ,kBAAkB,GAAG,IAAI;MAClC;MACA,IAAIlH,MAAM,CAACG,mBAAmB,EAAE;QAC5B,IAAI,CAACC,KAAK,CAACiE,KAAK,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC;IACF;IACA,IAAIrE,MAAM,CAACnE,QAAQ,IAAImE,MAAM,CAACnE,QAAQ,GAAG,CAAC,EAAE;MACxCiC,WAAW,CAACzB,WAAW,CAAC,CAAC,CAACvB,SAAS,CAAC,MAAMgD,WAAW,CAAClC,aAAa,CAACoE,MAAM,CAACnE,QAAQ,CAAC,CAAC;IACzF;IACA,IAAI,IAAI,CAACqL,kBAAkB,EAAE;MACzB;MACA;MACA,IAAI,CAACA,kBAAkB,CAAC9K,cAAc,CAAC,CAAC,CAACtB,SAAS,CAAC,MAAM;QACrDgD,WAAW,CAACpD,iBAAiB,CAACyG,KAAK,CAAC,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAAC+F,kBAAkB,CAAClM,OAAO,CAAC,CAAC;IACrC,CAAC,MACI;MACD;MACA8C,WAAW,CAACpD,iBAAiB,CAACyG,KAAK,CAAC,CAAC;IACzC;EACJ;EACA;AACJ;AACA;AACA;EACI+H,cAAcA,CAAClJ,MAAM,EAAE;IACnB,MAAMiK,aAAa,GAAG,IAAIhQ,aAAa,CAAC,CAAC;IACzCgQ,aAAa,CAACC,SAAS,GAAGlK,MAAM,CAACkK,SAAS;IAC1C,IAAIC,gBAAgB,GAAG,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACxD;IACA,MAAMC,KAAK,GAAGvK,MAAM,CAACkK,SAAS,KAAK,KAAK;IACxC,MAAMM,MAAM,GAAGxK,MAAM,CAACyK,kBAAkB,KAAK,MAAM,IAC9CzK,MAAM,CAACyK,kBAAkB,KAAK,OAAO,IAAI,CAACF,KAAM,IAChDvK,MAAM,CAACyK,kBAAkB,KAAK,KAAK,IAAIF,KAAM;IAClD,MAAMG,OAAO,GAAG,CAACF,MAAM,IAAIxK,MAAM,CAACyK,kBAAkB,KAAK,QAAQ;IACjE,IAAID,MAAM,EAAE;MACRL,gBAAgB,CAACQ,IAAI,CAAC,GAAG,CAAC;IAC9B,CAAC,MACI,IAAID,OAAO,EAAE;MACdP,gBAAgB,CAACS,KAAK,CAAC,GAAG,CAAC;IAC/B,CAAC,MACI;MACDT,gBAAgB,CAACU,kBAAkB,CAAC,CAAC;IACzC;IACA;IACA,IAAI7K,MAAM,CAAC8K,gBAAgB,KAAK,KAAK,EAAE;MACnCX,gBAAgB,CAACY,GAAG,CAAC,GAAG,CAAC;IAC7B,CAAC,MACI;MACDZ,gBAAgB,CAACa,MAAM,CAAC,GAAG,CAAC;IAChC;IACAf,aAAa,CAACE,gBAAgB,GAAGA,gBAAgB;IACjD,OAAO,IAAI,CAACC,QAAQ,CAAC9B,MAAM,CAAC2B,aAAa,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIZ,eAAeA,CAACrJ,MAAM,EAAElC,WAAW,EAAE;IACjC,MAAMqK,YAAY,GAAGnI,MAAM,IAAIA,MAAM,CAACoI,gBAAgB,IAAIpI,MAAM,CAACoI,gBAAgB,CAACC,QAAQ;IAC1F,OAAO1P,QAAQ,CAAC2P,MAAM,CAAC;MACnBnB,MAAM,EAAEgB,YAAY,IAAI,IAAI,CAACI,SAAS;MACtCC,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEjO,cAAc;QAAEkO,QAAQ,EAAE5K;MAAY,CAAC,EAClD;QAAE2K,OAAO,EAAEjM,kBAAkB;QAAEkM,QAAQ,EAAE1I,MAAM,CAACrI;MAAK,CAAC;IAE9D,CAAC,CAAC;EACN;AAGJ;AAACsT,YAAA,GA3NKhE,WAAW;AAAArM,eAAA,CAAXqM,WAAW,wBAAAiE,qBAAArO,iBAAA;EAAA,YAAAA,iBAAA,IAyNsFoK,YAAW;AAAA;AAAArM,eAAA,CAzN5GqM,WAAW,+BAxYgEtQ,EAAE,CAAAwU,kBAAA;EAAAC,KAAA,EAkmBwBnE,YAAW;EAAAD,OAAA,EAAXC,YAAW,CAAAoE,IAAA;EAAAtE,UAAA,EAAc;AAAM;AAE1I;EAAA,QAAA7J,SAAA,oBAAAA,SAAA,KApmBiFvG,EAAE,CAAAwG,iBAAA,CAomBQ8J,WAAW,EAAc,CAAC;IACzGlK,IAAI,EAAElE,UAAU;IAChBuE,IAAI,EAAE,CAAC;MAAE2J,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMuE,UAAU,GAAG,CAAChM,oBAAoB,EAAE5C,gBAAgB,EAAEa,kBAAkB,EAAEG,iBAAiB,CAAC;AAClG,MAAM6N,iBAAiB,CAAC;AAYvBC,kBAAA,GAZKD,iBAAiB;AAAA3Q,eAAA,CAAjB2Q,iBAAiB,wBAAAE,2BAAA5O,iBAAA;EAAA,YAAAA,iBAAA,IACgF0O,kBAAiB;AAAA;AAAA3Q,eAAA,CADlH2Q,iBAAiB,8BA1mB0D5U,EAAE,CAAA+U,gBAAA;EAAA3O,IAAA,EA4mBqBwO,kBAAiB;EAAApM,OAAA,GAAYjF,aAAa,EACtIV,YAAY,EACZN,eAAe,EACfkB,eAAe,EACfyD,cAAc,EAAEyB,oBAAoB,EAAE5C,gBAAgB,EAAEa,kBAAkB,EAAEG,iBAAiB;EAAAiO,OAAA,GAAavR,eAAe,EAAEkF,oBAAoB,EAAE5C,gBAAgB,EAAEa,kBAAkB,EAAEG,iBAAiB;AAAA;AAAA9C,eAAA,CAN9M2Q,iBAAiB,8BA1mB0D5U,EAAE,CAAAiV,gBAAA;EAAApD,SAAA,EAinBmD,CAACvB,WAAW,CAAC;EAAA9H,OAAA,GAAYjF,aAAa,EAChKV,YAAY,EACZN,eAAe,EACfkB,eAAe,EACfyD,cAAc,EAAEzD,eAAe;AAAA;AAE3C;EAAA,QAAA8C,SAAA,oBAAAA,SAAA,KAvnBiFvG,EAAE,CAAAwG,iBAAA,CAunBQoO,iBAAiB,EAAc,CAAC;IAC/GxO,IAAI,EAAEjE,QAAQ;IACdsE,IAAI,EAAE,CAAC;MACC+B,OAAO,EAAE,CACLjF,aAAa,EACbV,YAAY,EACZN,eAAe,EACfkB,eAAe,EACfyD,cAAc,EACd,GAAGyN,UAAU,CAChB;MACDK,OAAO,EAAE,CAACvR,eAAe,EAAE,GAAGkR,UAAU,CAAC;MACzC9C,SAAS,EAAE,CAACvB,WAAW;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4E,qBAAqB,GAAG;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,aAAa,EAAE;IACX/O,IAAI,EAAE,CAAC;IACPgP,IAAI,EAAE,OAAO;IACb,aAAa,EAAE,CACX;MACIhP,IAAI,EAAE,CAAC;MACPgP,IAAI,EAAE,cAAc;MACpBjN,MAAM,EAAE;QAAE/B,IAAI,EAAE,CAAC;QAAE+B,MAAM,EAAE;UAAEkN,SAAS,EAAE,YAAY;UAAEC,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK;IACrF,CAAC,EACD;MACInP,IAAI,EAAE,CAAC;MACPgP,IAAI,EAAE,SAAS;MACfjN,MAAM,EAAE;QAAE/B,IAAI,EAAE,CAAC;QAAE+B,MAAM,EAAE;UAAEkN,SAAS,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK;IACnF,CAAC,EACD;MACInP,IAAI,EAAE,CAAC;MACPoP,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAE;QAAErP,IAAI,EAAE,CAAC;QAAE+B,MAAM,EAAE,IAAI;QAAEuN,OAAO,EAAE;MAAmC,CAAC;MACjFC,OAAO,EAAE;IACb,CAAC,EACD;MACIvP,IAAI,EAAE,CAAC;MACPoP,IAAI,EAAE,wBAAwB;MAC9BC,SAAS,EAAE;QACPrP,IAAI,EAAE,CAAC;QACP+B,MAAM,EAAE;UAAE/B,IAAI,EAAE,CAAC;UAAE+B,MAAM,EAAE;YAAEmN,OAAO,EAAE;UAAE,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAC;QACzDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAAS9P,kBAAkB,EAAEsK,6BAA6B,EAAED,qCAAqC,EAAEI,WAAW,EAAEvJ,iBAAiB,EAAEH,kBAAkB,EAAEd,iBAAiB,EAAE6C,oBAAoB,EAAE5C,gBAAgB,EAAE6O,iBAAiB,EAAE/Q,cAAc,EAAEqD,cAAc,EAAEgO,qBAAqB;AAC1R", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}