{"ast": null, "code": "export const users = [{\n  'username': 'TruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncat',\n  'email': '<EMAIL>',\n  'status': 'test',\n  'calcTest': 'Test very long data for testing calc truncate',\n  'active': true,\n  'activity': 'disabled',\n  'createdAt': '2019-01-30T11:25:58.179Z',\n  'updatedAt': '2019-02-20T12:25:58.179Z',\n  'operators': [{\n    'id': 'lqP17pRZ',\n    'brandPath': 'test_qwerty:',\n    'funding': 20\n  }, {\n    'id': '5R2lL63k',\n    'brandPath': 'as_main_mrch:as_main_mrch_brand1:',\n    'funding': 33\n  }, {\n    'id': 'DR4z5831',\n    'brandPath': 'test_brand2:'\n  }, {\n    'id': 'nqyrA6RQ',\n    'brandPath': 'as_main_mrch:as_main_mrch_brand:',\n    'funding': 20\n  }, {\n    'id': 'DR4l7mR1',\n    'brandPath': 'as_main_mrch:'\n  }],\n  'image': 'https://storage.googleapis.com/lobby.stg1.m27613.com/swgpro/games/posters/sw/en/sw_88sf.jpg'\n}, {\n  'username': '1234',\n  'calcTest': 'Test very long data for testing calc truncate',\n  'status': 'suspended',\n  'active': false,\n  'activity': 'enabled',\n  'email': '<EMAIL>',\n  'createdAt': '2019-01-14T07:04:16.712Z',\n  'updatedAt': '2019-02-20T12:25:58.179Z',\n  'image': 'https://storage.googleapis.com/lobby.stg1.m27613.com/swgpro/games/posters/sw/en/sw_88sf.jpg',\n  'operators': ['lqP17pRZ', '5R2lL63k', 'DR4z5831']\n}, {\n  'username': '123456fghfg',\n  'status': 'normal',\n  'active': true,\n  'email': 'kjhkj!<EMAIL>',\n  'image': 'https://storage.googleapis.com/lobby.stg1.m27613.com/swgpro/games/posters/sw/en/sw_dhcf.png',\n  'operators': []\n}, {\n  'username': '2123123',\n  'status': 'normal',\n  'active': true,\n  'email': '<EMAIL>',\n  'createdAt': '2019-01-14T09:20:06.246Z',\n  'updatedAt': '2019-02-20T12:25:58.179Z',\n  'operators': {\n    'owner': {\n      'id': 'JqNpXLxB',\n      'title': 'Child_reseller_5'\n    },\n    'brands': [{\n      'title': 'TEST',\n      'id': 'lqPzQ04B'\n    }]\n  }\n}, {\n  'username': '2222',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-02-01T08:36:48.205Z',\n  'operators': {\n    'owner': {\n      'id': 'JqNpXLxB',\n      'title': 'Child_reseller_5'\n    },\n    'brands': [{\n      'title': null,\n      'id': null\n    }]\n  }\n}, {\n  'username': '234234',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-04-15T08:39:10.125Z'\n}, {\n  'username': '235232341',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-01-18T15:00:41.851Z'\n}, {\n  'username': '23523523',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-01-30T09:34:13.976Z',\n  'updatedAt': '2019-02-20T12:25:58.179Z'\n}, {\n  'username': '23523523',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-01-25T13:17:01.313Z'\n}, {\n  'username': '23523523523',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-01-25T14:25:15.017Z'\n}, {\n  'username': '235343252',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-12-04T08:46:47.250Z'\n}, {\n  'username': '23534623',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-12-03T14:50:13.410Z'\n}, {\n  'username': '242635735735',\n  'status': 'suspended',\n  'email': '<EMAIL>',\n  'createdAt': '2019-08-13T14:11:10.118Z'\n}, {\n  'username': '2462352424',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-01-21T06:30:08.471Z'\n}, {\n  'username': '2e2e2e2',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-04-02T09:52:25.701Z'\n}, {\n  'username': '2e2e2e2',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-07-13T12:26:43.708Z'\n}, {\n  'username': '2FAUSER',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-01-16T14:23:37.379Z'\n}, {\n  'username': '2FAUser123',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-01-18T10:23:37.403Z'\n}, {\n  'username': '2www',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-02-01T11:36:43.929Z'\n}, {\n  'username': '324235232',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-01-18T13:53:44.625Z'\n}, {\n  'username': '235343252',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-12-04T08:46:47.250Z'\n}, {\n  'username': '23534623',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-12-03T14:50:13.410Z'\n}, {\n  'username': '242635735735',\n  'status': 'suspended',\n  'email': '<EMAIL>',\n  'createdAt': '2019-08-13T14:11:10.118Z'\n}, {\n  'username': '2462352424',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-01-21T06:30:08.471Z'\n}, {\n  'username': '2e2e2e2',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-04-02T09:52:25.701Z'\n}, {\n  'username': '2e2e2e2',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-07-13T12:26:43.708Z'\n}, {\n  'username': '2FAUSER',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-01-16T14:23:37.379Z'\n}, {\n  'username': '2FAUser123',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2018-01-18T10:23:37.403Z'\n}, {\n  'username': '2www',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-02-01T11:36:43.929Z'\n}, {\n  'username': '324235232',\n  'status': 'normal',\n  'email': '<EMAIL>',\n  'createdAt': '2019-01-18T13:53:44.625Z'\n}];", "map": {"version": 3, "names": ["users"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/_stories/users-example.data.ts"], "sourcesContent": ["export const users = [\n    {\n        'username': 'TruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncateTruncat',\n        'email': '<EMAIL>',\n        'status': 'test',\n        'calcTest': 'Test very long data for testing calc truncate',\n        'active': true,\n        'activity': 'disabled',\n        'createdAt': '2019-01-30T11:25:58.179Z',\n        'updatedAt': '2019-02-20T12:25:58.179Z',\n        'operators': [\n            {\n                'id': 'lqP17pRZ',\n                'brandPath': 'test_qwerty:',\n                'funding': 20\n            },\n            {\n                'id': '5R2lL63k',\n                'brandPath': 'as_main_mrch:as_main_mrch_brand1:',\n                'funding': 33\n            },\n            {\n                'id': 'DR4z5831',\n                'brandPath': 'test_brand2:'\n            },\n            {\n                'id': 'nqyrA6RQ',\n                'brandPath': 'as_main_mrch:as_main_mrch_brand:',\n                'funding': 20\n            },\n            {\n                'id': 'DR4l7mR1',\n                'brandPath': 'as_main_mrch:'\n            }\n        ],\n        'image': 'https://storage.googleapis.com/lobby.stg1.m27613.com/swgpro/games/posters/sw/en/sw_88sf.jpg'\n    },\n    {\n        'username': '1234',\n        'calcTest': 'Test very long data for testing calc truncate',\n        'status': 'suspended',\n        'active': false,\n        'activity': 'enabled',\n        'email': '<EMAIL>',\n        'createdAt': '2019-01-14T07:04:16.712Z',\n        'updatedAt': '2019-02-20T12:25:58.179Z',\n        'image': 'https://storage.googleapis.com/lobby.stg1.m27613.com/swgpro/games/posters/sw/en/sw_88sf.jpg',\n        'operators': [\n            'lqP17pRZ', '5R2lL63k', 'DR4z5831'\n        ],\n    },\n    {\n        'username': '123456fghfg',\n        'status': 'normal',\n        'active': true,\n        'email': 'kjhkj!<EMAIL>',\n        'image': 'https://storage.googleapis.com/lobby.stg1.m27613.com/swgpro/games/posters/sw/en/sw_dhcf.png',\n        'operators': []\n    },\n    {\n        'username': '2123123',\n        'status': 'normal',\n        'active': true,\n        'email': '<EMAIL>',\n        'createdAt': '2019-01-14T09:20:06.246Z',\n        'updatedAt': '2019-02-20T12:25:58.179Z',\n        'operators': {\n            'owner': { 'id': 'JqNpXLxB', 'title': 'Child_reseller_5' },\n            'brands': [{ 'title': 'TEST', 'id': 'lqPzQ04B' }]\n        },\n    }, {\n        'username': '2222',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-02-01T08:36:48.205Z',\n        'operators': {\n            'owner': { 'id': 'JqNpXLxB', 'title': 'Child_reseller_5' },\n            'brands': [{ 'title': null, 'id': null }]\n        },\n    }, {\n        'username': '234234',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-04-15T08:39:10.125Z',\n    }, {\n        'username': '235232341',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-01-18T15:00:41.851Z',\n    }, {\n        'username': '23523523',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-01-30T09:34:13.976Z',\n        'updatedAt': '2019-02-20T12:25:58.179Z',\n    }, {\n        'username': '23523523',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-01-25T13:17:01.313Z',\n    }, {\n        'username': '23523523523',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-01-25T14:25:15.017Z',\n    }, {\n        'username': '235343252',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-12-04T08:46:47.250Z',\n    }, {\n        'username': '23534623',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-12-03T14:50:13.410Z',\n    }, {\n        'username': '242635735735',\n        'status': 'suspended',\n        'email': '<EMAIL>',\n        'createdAt': '2019-08-13T14:11:10.118Z',\n    }, {\n        'username': '2462352424',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-01-21T06:30:08.471Z',\n    }, {\n        'username': '2e2e2e2',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-04-02T09:52:25.701Z',\n    }, {\n        'username': '2e2e2e2',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-07-13T12:26:43.708Z',\n    }, {\n        'username': '2FAUSER',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-01-16T14:23:37.379Z',\n    }, {\n        'username': '2FAUser123',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-01-18T10:23:37.403Z',\n    }, {\n        'username': '2www',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-02-01T11:36:43.929Z',\n    }, {\n        'username': '324235232',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-01-18T13:53:44.625Z',\n    }, {\n        'username': '235343252',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-12-04T08:46:47.250Z',\n    }, {\n        'username': '23534623',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-12-03T14:50:13.410Z',\n    }, {\n        'username': '242635735735',\n        'status': 'suspended',\n        'email': '<EMAIL>',\n        'createdAt': '2019-08-13T14:11:10.118Z',\n    }, {\n        'username': '2462352424',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-01-21T06:30:08.471Z',\n    }, {\n        'username': '2e2e2e2',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-04-02T09:52:25.701Z',\n    }, {\n        'username': '2e2e2e2',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-07-13T12:26:43.708Z',\n    }, {\n        'username': '2FAUSER',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-01-16T14:23:37.379Z',\n    }, {\n        'username': '2FAUser123',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2018-01-18T10:23:37.403Z',\n    }, {\n        'username': '2www',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-02-01T11:36:43.929Z',\n    }, {\n        'username': '324235232',\n        'status': 'normal',\n        'email': '<EMAIL>',\n        'createdAt': '2019-01-18T13:53:44.625Z',\n    }\n];\n"], "mappings": "AAAA,OAAO,MAAMA,KAAK,GAAG,CACjB;EACI,UAAU,EAAE,yHAAyH;EACrI,OAAO,EAAE,YAAY;EACrB,QAAQ,EAAE,MAAM;EAChB,UAAU,EAAE,+CAA+C;EAC3D,QAAQ,EAAE,IAAI;EACd,UAAU,EAAE,UAAU;EACtB,WAAW,EAAE,0BAA0B;EACvC,WAAW,EAAE,0BAA0B;EACvC,WAAW,EAAE,CACT;IACI,IAAI,EAAE,UAAU;IAChB,WAAW,EAAE,cAAc;IAC3B,SAAS,EAAE;EACf,CAAC,EACD;IACI,IAAI,EAAE,UAAU;IAChB,WAAW,EAAE,mCAAmC;IAChD,SAAS,EAAE;EACf,CAAC,EACD;IACI,IAAI,EAAE,UAAU;IAChB,WAAW,EAAE;EACjB,CAAC,EACD;IACI,IAAI,EAAE,UAAU;IAChB,WAAW,EAAE,kCAAkC;IAC/C,SAAS,EAAE;EACf,CAAC,EACD;IACI,IAAI,EAAE,UAAU;IAChB,WAAW,EAAE;EACjB,CAAC,CACJ;EACD,OAAO,EAAE;AACb,CAAC,EACD;EACI,UAAU,EAAE,MAAM;EAClB,UAAU,EAAE,+CAA+C;EAC3D,QAAQ,EAAE,WAAW;EACrB,QAAQ,EAAE,KAAK;EACf,UAAU,EAAE,SAAS;EACrB,OAAO,EAAE,uBAAuB;EAChC,WAAW,EAAE,0BAA0B;EACvC,WAAW,EAAE,0BAA0B;EACvC,OAAO,EAAE,6FAA6F;EACtG,WAAW,EAAE,CACT,UAAU,EAAE,UAAU,EAAE,UAAU;AAE1C,CAAC,EACD;EACI,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,IAAI;EACd,OAAO,EAAE,iBAAiB;EAC1B,OAAO,EAAE,6FAA6F;EACtG,WAAW,EAAE;AACjB,CAAC,EACD;EACI,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,QAAQ;EAClB,QAAQ,EAAE,IAAI;EACd,OAAO,EAAE,uBAAuB;EAChC,WAAW,EAAE,0BAA0B;EACvC,WAAW,EAAE,0BAA0B;EACvC,WAAW,EAAE;IACT,OAAO,EAAE;MAAE,IAAI,EAAE,UAAU;MAAE,OAAO,EAAE;IAAmB,CAAC;IAC1D,QAAQ,EAAE,CAAC;MAAE,OAAO,EAAE,MAAM;MAAE,IAAI,EAAE;IAAW,CAAC;EACpD;AACJ,CAAC,EAAE;EACC,UAAU,EAAE,MAAM;EAClB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,YAAY;EACrB,WAAW,EAAE,0BAA0B;EACvC,WAAW,EAAE;IACT,OAAO,EAAE;MAAE,IAAI,EAAE,UAAU;MAAE,OAAO,EAAE;IAAmB,CAAC;IAC1D,QAAQ,EAAE,CAAC;MAAE,OAAO,EAAE,IAAI;MAAE,IAAI,EAAE;IAAK,CAAC;EAC5C;AACJ,CAAC,EAAE;EACC,UAAU,EAAE,QAAQ;EACpB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,kBAAkB;EAC3B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,WAAW;EACvB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,qBAAqB;EAC9B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,UAAU;EACtB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,mBAAmB;EAC5B,WAAW,EAAE,0BAA0B;EACvC,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,UAAU;EACtB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,kBAAkB;EAC3B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,aAAa;EACzB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,qBAAqB;EAC9B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,WAAW;EACvB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,qBAAqB;EAC9B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,UAAU;EACtB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,oBAAoB;EAC7B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,cAAc;EAC1B,QAAQ,EAAE,WAAW;EACrB,OAAO,EAAE,sBAAsB;EAC/B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,YAAY;EACxB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,sBAAsB;EAC/B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,cAAc;EACvB,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,iBAAiB;EAC1B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,mBAAmB;EAC5B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,YAAY;EACxB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,4BAA4B;EACrC,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,MAAM;EAClB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,kBAAkB;EAC3B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,WAAW;EACvB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,kBAAkB;EAC3B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,WAAW;EACvB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,qBAAqB;EAC9B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,UAAU;EACtB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,oBAAoB;EAC7B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,cAAc;EAC1B,QAAQ,EAAE,WAAW;EACrB,OAAO,EAAE,sBAAsB;EAC/B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,YAAY;EACxB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,sBAAsB;EAC/B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,cAAc;EACvB,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,iBAAiB;EAC1B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,SAAS;EACrB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,mBAAmB;EAC5B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,YAAY;EACxB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,4BAA4B;EACrC,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,MAAM;EAClB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,kBAAkB;EAC3B,WAAW,EAAE;AACjB,CAAC,EAAE;EACC,UAAU,EAAE,WAAW;EACvB,QAAQ,EAAE,QAAQ;EAClB,OAAO,EAAE,kBAAkB;EAC3B,WAAW,EAAE;AACjB,CAAC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}