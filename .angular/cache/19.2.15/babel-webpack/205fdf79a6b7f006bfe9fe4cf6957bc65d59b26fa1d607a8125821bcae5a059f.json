{"ast": null, "code": "var _SwuiMenuSelectComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-menu-select.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-menu-select.component.scss?ngResource\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { BehaviorSubject, combineLatest, Subject } from 'rxjs';\nimport { UntypedFormControl } from '@angular/forms';\nimport { filter, map, takeUntil } from 'rxjs/operators';\nlet SwuiMenuSelectComponent = (_SwuiMenuSelectComponent = class SwuiMenuSelectComponent {\n  set data(val) {\n    const value = val ? val : [];\n    this._data$.next(value);\n    this._filtered = value.map(item => item.id);\n  }\n  set selected(val) {\n    const processed = Array.isArray(val) ? val : [];\n    this._sourceSelected = processed;\n    this._selected$.next(processed);\n  }\n  constructor() {\n    this.searchPlaceholder = 'COMPONENTS.MENU_SELECT.search';\n    this.title = '';\n    this.showSearch = false;\n    this.selectAll = false;\n    this.applyData = new EventEmitter();\n    this.cancelClick = new EventEmitter();\n    this.searchControl = new UntypedFormControl();\n    this.processedData = [];\n    this._sourceSelected = [];\n    this._data$ = new BehaviorSubject([]);\n    this._selected$ = new BehaviorSubject([]);\n    this._filtered = [];\n    this._destroyed$ = new Subject();\n    combineLatest([this._data$, this._selected$]).pipe(takeUntil(this._destroyed$)).subscribe(([data, selected]) => {\n      const selectedSet = new Set(selected);\n      this.processedData = data.reduce((result, option) => {\n        option.data = {\n          checked: selectedSet.has(option.id)\n        };\n        return [...result, ...[option]];\n      }, []);\n    });\n  }\n  ngOnInit() {\n    this.initFilter();\n  }\n  clear(event) {\n    event.preventDefault();\n    this._selected$.next([]);\n  }\n  onSelectAll(event) {\n    event.preventDefault();\n    this._selected$.next(this.processedData.filter(({\n      disabled\n    }) => !disabled).map(({\n      id\n    }) => id));\n  }\n  cancel(event) {\n    event.preventDefault();\n    this._selected$.next(this._sourceSelected);\n    this.cancelClick.emit();\n  }\n  apply(event) {\n    event.preventDefault();\n    const resultData = this.processedData.filter(el => el.data.checked);\n    const output = resultData ? resultData.map(el => el.id) : [];\n    this.applyData.emit(output);\n  }\n  isFiltered(id) {\n    return this._filtered.indexOf(id) > -1;\n  }\n  get selectedLength() {\n    return this.processedData.filter(el => el.data.checked).length;\n  }\n  prevent(event) {\n    event.stopPropagation();\n  }\n  initFilter() {\n    this.searchControl.valueChanges.pipe(filter(() => this.showSearch), map(searchString => searchString.toLowerCase()), takeUntil(this._destroyed$)).subscribe(searchString => {\n      const processed = this.processedData.filter(option => {\n        return option.text && option.text.toLowerCase().indexOf(searchString) > -1;\n      });\n      this._filtered = processed ? processed.map(item => item.id) : [];\n    });\n  }\n}, _SwuiMenuSelectComponent.ctorParameters = () => [], _SwuiMenuSelectComponent.propDecorators = {\n  searchPlaceholder: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  showSearch: [{\n    type: Input\n  }],\n  selectAll: [{\n    type: Input\n  }],\n  data: [{\n    type: Input\n  }],\n  selected: [{\n    type: Input\n  }],\n  applyData: [{\n    type: Output\n  }],\n  cancelClick: [{\n    type: Output\n  }]\n}, _SwuiMenuSelectComponent);\nSwuiMenuSelectComponent = __decorate([Component({\n  selector: 'lib-swui-menu-select',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiMenuSelectComponent);\nexport { SwuiMenuSelectComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "BehaviorSubject", "combineLatest", "Subject", "UntypedFormControl", "filter", "map", "takeUntil", "SwuiMenuSelectComponent", "_SwuiMenuSelectComponent", "data", "val", "value", "_data$", "next", "_filtered", "item", "id", "selected", "processed", "Array", "isArray", "_sourceSelected", "_selected$", "constructor", "searchPlaceholder", "title", "showSearch", "selectAll", "applyData", "cancelClick", "searchControl", "processedData", "_destroyed$", "pipe", "subscribe", "selectedSet", "Set", "reduce", "result", "option", "checked", "has", "ngOnInit", "initFilter", "clear", "event", "preventDefault", "onSelectAll", "disabled", "cancel", "emit", "apply", "resultData", "el", "output", "isFiltered", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "length", "prevent", "stopPropagation", "valueChanges", "searchString", "toLowerCase", "text", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-menu-select/swui-menu-select.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\nimport { BehaviorSubject, combineLatest, Subject } from 'rxjs';\nimport { UntypedFormControl } from '@angular/forms';\nimport { filter, map, takeUntil } from 'rxjs/operators';\n\nimport { SwuiSelectOption } from '../swui-select/swui-select.interface';\n\n\n@Component({\n    selector: 'lib-swui-menu-select',\n    templateUrl: './swui-menu-select.component.html',\n    styleUrls: ['./swui-menu-select.component.scss'],\n    standalone: false\n})\nexport class SwuiMenuSelectComponent implements OnInit {\n  @Input() searchPlaceholder = 'COMPONENTS.MENU_SELECT.search';\n  @Input() title = '';\n  @Input() showSearch = false;\n  @Input() selectAll = false;\n\n  @Input()\n  set data( val: SwuiSelectOption[] ) {\n    const value = val ? val : [];\n    this._data$.next(value);\n    this._filtered = value.map(( item: SwuiSelectOption ) => item.id);\n  }\n\n  @Input()\n  set selected( val: string[] ) {\n    const processed = Array.isArray(val) ? val : [];\n    this._sourceSelected = processed;\n    this._selected$.next(processed);\n  }\n\n  @Output() applyData = new EventEmitter<string[]>();\n  @Output() cancelClick = new EventEmitter();\n\n  searchControl = new UntypedFormControl();\n  processedData: SwuiSelectOption[] = [];\n\n  private _sourceSelected: string[] = [];\n  private _data$ = new BehaviorSubject<SwuiSelectOption[]>([]);\n  private _selected$ = new BehaviorSubject<string[]>([]);\n  private _filtered: string[] = [];\n  private readonly _destroyed$ = new Subject<void>();\n\n  constructor() {\n    combineLatest([this._data$, this._selected$])\n      .pipe(\n        takeUntil(this._destroyed$)\n      )\n      .subscribe(( [data, selected] ) => {\n        const selectedSet = new Set<string>(selected);\n        this.processedData = data.reduce<SwuiSelectOption[]>(( result, option ) => {\n          option.data = {\n            checked: selectedSet.has(option.id)\n          };\n          return [...result, ...[option]];\n        }, []);\n      });\n  }\n\n  ngOnInit() {\n    this.initFilter();\n  }\n\n  clear( event: Event ) {\n    event.preventDefault();\n    this._selected$.next([]);\n  }\n\n  onSelectAll( event: Event ) {\n    event.preventDefault();\n    this._selected$.next(this.processedData.filter(( { disabled } ) => !disabled).map(( { id } ) => id));\n  }\n\n  cancel( event: Event ) {\n    event.preventDefault();\n    this._selected$.next(this._sourceSelected);\n    this.cancelClick.emit();\n  }\n\n  apply( event: Event ) {\n    event.preventDefault();\n    const resultData = this.processedData.filter(( el: SwuiSelectOption ) => el.data.checked);\n    const output = resultData ? resultData.map(( el: SwuiSelectOption ) => el.id) : [];\n\n    this.applyData.emit(output);\n  }\n\n  isFiltered( id: string ): boolean {\n    return this._filtered.indexOf(id) > -1;\n  }\n\n  get selectedLength(): number {\n    return this.processedData.filter(( el: SwuiSelectOption ) => el.data.checked).length;\n  }\n\n  prevent( event: MouseEvent ) {\n    event.stopPropagation();\n  }\n\n  private initFilter() {\n    this.searchControl.valueChanges\n      .pipe(\n        filter(() => this.showSearch),\n        map(( searchString: string ) => searchString.toLowerCase()),\n        takeUntil(this._destroyed$)\n      )\n      .subscribe(( searchString: string ) => {\n        const processed = this.processedData.filter(( option: SwuiSelectOption ) => {\n          return option.text && option.text.toLowerCase().indexOf(searchString) > -1;\n        });\n        this._filtered = processed ? processed.map(( item: SwuiSelectOption ) => item.id) : [];\n      });\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAUC,MAAM,QAAQ,eAAe;AAC9E,SAASC,eAAe,EAAEC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC9D,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAWhD,IAAMC,uBAAuB,IAAAC,wBAAA,GAA7B,MAAMD,uBAAuB;MAO9BE,IAAIA,CAAEC,GAAuB;IAC/B,MAAMC,KAAK,GAAGD,GAAG,GAAGA,GAAG,GAAG,EAAE;IAC5B,IAAI,CAACE,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC;IACvB,IAAI,CAACG,SAAS,GAAGH,KAAK,CAACN,GAAG,CAAGU,IAAsB,IAAMA,IAAI,CAACC,EAAE,CAAC;EACnE;MAGIC,QAAQA,CAAEP,GAAa;IACzB,MAAMQ,SAAS,GAAGC,KAAK,CAACC,OAAO,CAACV,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;IAC/C,IAAI,CAACW,eAAe,GAAGH,SAAS;IAChC,IAAI,CAACI,UAAU,CAACT,IAAI,CAACK,SAAS,CAAC;EACjC;EAcAK,YAAA;IA/BS,KAAAC,iBAAiB,GAAG,+BAA+B;IACnD,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,KAAK;IAgBhB,KAAAC,SAAS,GAAG,IAAI/B,YAAY,EAAY;IACxC,KAAAgC,WAAW,GAAG,IAAIhC,YAAY,EAAE;IAE1C,KAAAiC,aAAa,GAAG,IAAI3B,kBAAkB,EAAE;IACxC,KAAA4B,aAAa,GAAuB,EAAE;IAE9B,KAAAV,eAAe,GAAa,EAAE;IAC9B,KAAAT,MAAM,GAAG,IAAIZ,eAAe,CAAqB,EAAE,CAAC;IACpD,KAAAsB,UAAU,GAAG,IAAItB,eAAe,CAAW,EAAE,CAAC;IAC9C,KAAAc,SAAS,GAAa,EAAE;IACf,KAAAkB,WAAW,GAAG,IAAI9B,OAAO,EAAQ;IAGhDD,aAAa,CAAC,CAAC,IAAI,CAACW,MAAM,EAAE,IAAI,CAACU,UAAU,CAAC,CAAC,CAC1CW,IAAI,CACH3B,SAAS,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC5B,CACAE,SAAS,CAAC,CAAE,CAACzB,IAAI,EAAEQ,QAAQ,CAAC,KAAK;MAChC,MAAMkB,WAAW,GAAG,IAAIC,GAAG,CAASnB,QAAQ,CAAC;MAC7C,IAAI,CAACc,aAAa,GAAGtB,IAAI,CAAC4B,MAAM,CAAqB,CAAEC,MAAM,EAAEC,MAAM,KAAK;QACxEA,MAAM,CAAC9B,IAAI,GAAG;UACZ+B,OAAO,EAAEL,WAAW,CAACM,GAAG,CAACF,MAAM,CAACvB,EAAE;SACnC;QACD,OAAO,CAAC,GAAGsB,MAAM,EAAE,GAAG,CAACC,MAAM,CAAC,CAAC;MACjC,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,CAAC;EACN;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,KAAKA,CAAEC,KAAY;IACjBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACxB,UAAU,CAACT,IAAI,CAAC,EAAE,CAAC;EAC1B;EAEAkC,WAAWA,CAAEF,KAAY;IACvBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACxB,UAAU,CAACT,IAAI,CAAC,IAAI,CAACkB,aAAa,CAAC3B,MAAM,CAAC,CAAE;MAAE4C;IAAQ,CAAE,KAAM,CAACA,QAAQ,CAAC,CAAC3C,GAAG,CAAC,CAAE;MAAEW;IAAE,CAAE,KAAMA,EAAE,CAAC,CAAC;EACtG;EAEAiC,MAAMA,CAAEJ,KAAY;IAClBA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACxB,UAAU,CAACT,IAAI,CAAC,IAAI,CAACQ,eAAe,CAAC;IAC1C,IAAI,CAACQ,WAAW,CAACqB,IAAI,EAAE;EACzB;EAEAC,KAAKA,CAAEN,KAAY;IACjBA,KAAK,CAACC,cAAc,EAAE;IACtB,MAAMM,UAAU,GAAG,IAAI,CAACrB,aAAa,CAAC3B,MAAM,CAAGiD,EAAoB,IAAMA,EAAE,CAAC5C,IAAI,CAAC+B,OAAO,CAAC;IACzF,MAAMc,MAAM,GAAGF,UAAU,GAAGA,UAAU,CAAC/C,GAAG,CAAGgD,EAAoB,IAAMA,EAAE,CAACrC,EAAE,CAAC,GAAG,EAAE;IAElF,IAAI,CAACY,SAAS,CAACsB,IAAI,CAACI,MAAM,CAAC;EAC7B;EAEAC,UAAUA,CAAEvC,EAAU;IACpB,OAAO,IAAI,CAACF,SAAS,CAAC0C,OAAO,CAACxC,EAAE,CAAC,GAAG,CAAC,CAAC;EACxC;EAEA,IAAIyC,cAAcA,CAAA;IAChB,OAAO,IAAI,CAAC1B,aAAa,CAAC3B,MAAM,CAAGiD,EAAoB,IAAMA,EAAE,CAAC5C,IAAI,CAAC+B,OAAO,CAAC,CAACkB,MAAM;EACtF;EAEAC,OAAOA,CAAEd,KAAiB;IACxBA,KAAK,CAACe,eAAe,EAAE;EACzB;EAEQjB,UAAUA,CAAA;IAChB,IAAI,CAACb,aAAa,CAAC+B,YAAY,CAC5B5B,IAAI,CACH7B,MAAM,CAAC,MAAM,IAAI,CAACsB,UAAU,CAAC,EAC7BrB,GAAG,CAAGyD,YAAoB,IAAMA,YAAY,CAACC,WAAW,EAAE,CAAC,EAC3DzD,SAAS,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC5B,CACAE,SAAS,CAAG4B,YAAoB,IAAK;MACpC,MAAM5C,SAAS,GAAG,IAAI,CAACa,aAAa,CAAC3B,MAAM,CAAGmC,MAAwB,IAAK;QACzE,OAAOA,MAAM,CAACyB,IAAI,IAAIzB,MAAM,CAACyB,IAAI,CAACD,WAAW,EAAE,CAACP,OAAO,CAACM,YAAY,CAAC,GAAG,CAAC,CAAC;MAC5E,CAAC,CAAC;MACF,IAAI,CAAChD,SAAS,GAAGI,SAAS,GAAGA,SAAS,CAACb,GAAG,CAAGU,IAAsB,IAAMA,IAAI,CAACC,EAAE,CAAC,GAAG,EAAE;IACxF,CAAC,CAAC;EACN;;;UApGClB;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UAOLA;EAAK;;UAOLC;EAAM;;UACNA;EAAM;;AArBIQ,uBAAuB,GAAA0D,UAAA,EANnCrE,SAAS,CAAC;EACPsE,QAAQ,EAAE,sBAAsB;EAChCC,QAAA,EAAAC,oBAAgD;EAEhDC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACW9D,uBAAuB,CAsGnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}