{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var SequenceError = createErrorClass(function (_super) {\n  return function SequenceErrorImpl(message) {\n    _super(this);\n    this.name = 'SequenceError';\n    this.message = message;\n  };\n});\n//# sourceMappingURL=SequenceError.js.map", "map": {"version": 3, "names": ["createErrorClass", "SequenceError", "_super", "SequenceErrorImpl", "message", "name"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/SequenceError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport var SequenceError = createErrorClass(function (_super) {\n    return function SequenceErrorImpl(message) {\n        _super(this);\n        this.name = 'SequenceError';\n        this.message = message;\n    };\n});\n//# sourceMappingURL=SequenceError.js.map"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,IAAIC,aAAa,GAAGD,gBAAgB,CAAC,UAAUE,MAAM,EAAE;EAC1D,OAAO,SAASC,iBAAiBA,CAACC,OAAO,EAAE;IACvCF,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACG,IAAI,GAAG,eAAe;IAC3B,IAAI,CAACD,OAAO,GAAGA,OAAO;EAC1B,CAAC;AACL,CAAC,CAAC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}