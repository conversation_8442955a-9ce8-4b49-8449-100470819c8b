{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Polish [pl]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/evoL\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var monthsNominative = 'styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień'.split('_'),\n    monthsSubjective = 'stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia'.split('_'),\n    monthsParse = [/^sty/i, /^lut/i, /^mar/i, /^kwi/i, /^maj/i, /^cze/i, /^lip/i, /^sie/i, /^wrz/i, /^paź/i, /^lis/i, /^gru/i];\n  function plural(n) {\n    return n % 10 < 5 && n % 10 > 1 && ~~(n / 10) % 10 !== 1;\n  }\n  function translate(number, withoutSuffix, key) {\n    var result = number + ' ';\n    switch (key) {\n      case 'ss':\n        return result + (plural(number) ? 'sekundy' : 'sekund');\n      case 'm':\n        return withoutSuffix ? 'minuta' : 'minutę';\n      case 'mm':\n        return result + (plural(number) ? 'minuty' : 'minut');\n      case 'h':\n        return withoutSuffix ? 'godzina' : 'godzinę';\n      case 'hh':\n        return result + (plural(number) ? 'godziny' : 'godzin');\n      case 'ww':\n        return result + (plural(number) ? 'tygodnie' : 'tygodni');\n      case 'MM':\n        return result + (plural(number) ? 'miesiące' : 'miesięcy');\n      case 'yy':\n        return result + (plural(number) ? 'lata' : 'lat');\n    }\n  }\n  var pl = moment.defineLocale('pl', {\n    months: function (momentToFormat, format) {\n      if (!momentToFormat) {\n        return monthsNominative;\n      } else if (/D MMMM/.test(format)) {\n        return monthsSubjective[momentToFormat.month()];\n      } else {\n        return monthsNominative[momentToFormat.month()];\n      }\n    },\n    monthsShort: 'sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru'.split('_'),\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    weekdays: 'niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota'.split('_'),\n    weekdaysShort: 'ndz_pon_wt_śr_czw_pt_sob'.split('_'),\n    weekdaysMin: 'Nd_Pn_Wt_Śr_Cz_Pt_So'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Dziś o] LT',\n      nextDay: '[Jutro o] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[W niedzielę o] LT';\n          case 2:\n            return '[We wtorek o] LT';\n          case 3:\n            return '[W środę o] LT';\n          case 6:\n            return '[W sobotę o] LT';\n          default:\n            return '[W] dddd [o] LT';\n        }\n      },\n      lastDay: '[Wczoraj o] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[W zeszłą niedzielę o] LT';\n          case 3:\n            return '[W zeszłą środę o] LT';\n          case 6:\n            return '[W zeszłą sobotę o] LT';\n          default:\n            return '[W zeszły] dddd [o] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'za %s',\n      past: '%s temu',\n      s: 'kilka sekund',\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: '1 dzień',\n      dd: '%d dni',\n      w: 'tydzień',\n      ww: translate,\n      M: 'miesiąc',\n      MM: translate,\n      y: 'rok',\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return pl;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "monthsNominative", "split", "monthsSubjective", "<PERSON><PERSON><PERSON>e", "plural", "n", "translate", "number", "withoutSuffix", "key", "result", "pl", "defineLocale", "months", "momentToFormat", "format", "test", "month", "monthsShort", "longMonthsParse", "shortMonthsParse", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "day", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/moment/locale/pl.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Polish [pl]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/evoL\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var monthsNominative = 'styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień'.split(\n            '_'\n        ),\n        monthsSubjective = 'stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia'.split(\n            '_'\n        ),\n        monthsParse = [\n            /^sty/i,\n            /^lut/i,\n            /^mar/i,\n            /^kwi/i,\n            /^maj/i,\n            /^cze/i,\n            /^lip/i,\n            /^sie/i,\n            /^wrz/i,\n            /^paź/i,\n            /^lis/i,\n            /^gru/i,\n        ];\n    function plural(n) {\n        return n % 10 < 5 && n % 10 > 1 && ~~(n / 10) % 10 !== 1;\n    }\n    function translate(number, withoutSuffix, key) {\n        var result = number + ' ';\n        switch (key) {\n            case 'ss':\n                return result + (plural(number) ? 'sekundy' : 'sekund');\n            case 'm':\n                return withoutSuffix ? 'minuta' : 'minutę';\n            case 'mm':\n                return result + (plural(number) ? 'minuty' : 'minut');\n            case 'h':\n                return withoutSuffix ? 'godzina' : 'godzinę';\n            case 'hh':\n                return result + (plural(number) ? 'godziny' : 'godzin');\n            case 'ww':\n                return result + (plural(number) ? 'tygodnie' : 'tygodni');\n            case 'MM':\n                return result + (plural(number) ? 'miesiące' : 'miesięcy');\n            case 'yy':\n                return result + (plural(number) ? 'lata' : 'lat');\n        }\n    }\n\n    var pl = moment.defineLocale('pl', {\n        months: function (momentToFormat, format) {\n            if (!momentToFormat) {\n                return monthsNominative;\n            } else if (/D MMMM/.test(format)) {\n                return monthsSubjective[momentToFormat.month()];\n            } else {\n                return monthsNominative[momentToFormat.month()];\n            }\n        },\n        monthsShort: 'sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru'.split('_'),\n        monthsParse: monthsParse,\n        longMonthsParse: monthsParse,\n        shortMonthsParse: monthsParse,\n        weekdays: 'niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota'.split(\n            '_'\n        ),\n        weekdaysShort: 'ndz_pon_wt_śr_czw_pt_sob'.split('_'),\n        weekdaysMin: 'Nd_Pn_Wt_Śr_Cz_Pt_So'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Dziś o] LT',\n            nextDay: '[Jutro o] LT',\n            nextWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                        return '[W niedzielę o] LT';\n\n                    case 2:\n                        return '[We wtorek o] LT';\n\n                    case 3:\n                        return '[W środę o] LT';\n\n                    case 6:\n                        return '[W sobotę o] LT';\n\n                    default:\n                        return '[W] dddd [o] LT';\n                }\n            },\n            lastDay: '[Wczoraj o] LT',\n            lastWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                        return '[W zeszłą niedzielę o] LT';\n                    case 3:\n                        return '[W zeszłą środę o] LT';\n                    case 6:\n                        return '[W zeszłą sobotę o] LT';\n                    default:\n                        return '[W zeszły] dddd [o] LT';\n                }\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'za %s',\n            past: '%s temu',\n            s: 'kilka sekund',\n            ss: translate,\n            m: translate,\n            mm: translate,\n            h: translate,\n            hh: translate,\n            d: '1 dzień',\n            dd: '%d dni',\n            w: 'tydzień',\n            ww: translate,\n            M: 'miesiąc',\n            MM: translate,\n            y: 'rok',\n            yy: translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return pl;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,gBAAgB,GAAG,kGAAkG,CAACC,KAAK,CACvH,GACJ,CAAC;IACDC,gBAAgB,GAAG,oGAAoG,CAACD,KAAK,CACzH,GACJ,CAAC;IACDE,WAAW,GAAG,CACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACV;EACL,SAASC,MAAMA,CAACC,CAAC,EAAE;IACf,OAAOA,CAAC,GAAG,EAAE,GAAG,CAAC,IAAIA,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;EAC5D;EACA,SAASC,SAASA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAE;IAC3C,IAAIC,MAAM,GAAGH,MAAM,GAAG,GAAG;IACzB,QAAQE,GAAG;MACP,KAAK,IAAI;QACL,OAAOC,MAAM,IAAIN,MAAM,CAACG,MAAM,CAAC,GAAG,SAAS,GAAG,QAAQ,CAAC;MAC3D,KAAK,GAAG;QACJ,OAAOC,aAAa,GAAG,QAAQ,GAAG,QAAQ;MAC9C,KAAK,IAAI;QACL,OAAOE,MAAM,IAAIN,MAAM,CAACG,MAAM,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;MACzD,KAAK,GAAG;QACJ,OAAOC,aAAa,GAAG,SAAS,GAAG,SAAS;MAChD,KAAK,IAAI;QACL,OAAOE,MAAM,IAAIN,MAAM,CAACG,MAAM,CAAC,GAAG,SAAS,GAAG,QAAQ,CAAC;MAC3D,KAAK,IAAI;QACL,OAAOG,MAAM,IAAIN,MAAM,CAACG,MAAM,CAAC,GAAG,UAAU,GAAG,SAAS,CAAC;MAC7D,KAAK,IAAI;QACL,OAAOG,MAAM,IAAIN,MAAM,CAACG,MAAM,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC;MAC9D,KAAK,IAAI;QACL,OAAOG,MAAM,IAAIN,MAAM,CAACG,MAAM,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;IACzD;EACJ;EAEA,IAAII,EAAE,GAAGZ,MAAM,CAACa,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,SAAAA,CAAUC,cAAc,EAAEC,MAAM,EAAE;MACtC,IAAI,CAACD,cAAc,EAAE;QACjB,OAAOd,gBAAgB;MAC3B,CAAC,MAAM,IAAI,QAAQ,CAACgB,IAAI,CAACD,MAAM,CAAC,EAAE;QAC9B,OAAOb,gBAAgB,CAACY,cAAc,CAACG,KAAK,CAAC,CAAC,CAAC;MACnD,CAAC,MAAM;QACH,OAAOjB,gBAAgB,CAACc,cAAc,CAACG,KAAK,CAAC,CAAC,CAAC;MACnD;IACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACjB,KAAK,CAAC,GAAG,CAAC;IACzEE,WAAW,EAAEA,WAAW;IACxBgB,eAAe,EAAEhB,WAAW;IAC5BiB,gBAAgB,EAAEjB,WAAW;IAC7BkB,QAAQ,EAAE,4DAA4D,CAACpB,KAAK,CACxE,GACJ,CAAC;IACDqB,aAAa,EAAE,0BAA0B,CAACrB,KAAK,CAAC,GAAG,CAAC;IACpDsB,WAAW,EAAE,sBAAsB,CAACtB,KAAK,CAAC,GAAG,CAAC;IAC9CuB,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;YACF,OAAO,oBAAoB;UAE/B,KAAK,CAAC;YACF,OAAO,kBAAkB;UAE7B,KAAK,CAAC;YACF,OAAO,gBAAgB;UAE3B,KAAK,CAAC;YACF,OAAO,iBAAiB;UAE5B;YACI,OAAO,iBAAiB;QAChC;MACJ,CAAC;MACDC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACF,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;YACF,OAAO,2BAA2B;UACtC,KAAK,CAAC;YACF,OAAO,uBAAuB;UAClC,KAAK,CAAC;YACF,OAAO,wBAAwB;UACnC;YACI,OAAO,wBAAwB;QACvC;MACJ,CAAC;MACDG,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAErC,SAAS;MACbsC,CAAC,EAAEtC,SAAS;MACZuC,EAAE,EAAEvC,SAAS;MACbwC,CAAC,EAAExC,SAAS;MACZyC,EAAE,EAAEzC,SAAS;MACb0C,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE7C,SAAS;MACb8C,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE/C,SAAS;MACbgD,CAAC,EAAE,KAAK;MACRC,EAAE,EAAEjD;IACR,CAAC;IACDkD,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOjD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}