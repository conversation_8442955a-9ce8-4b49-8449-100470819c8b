{"ast": null, "code": "var _SwuiGridWidgetChooserComponent;\nimport { __decorate } from \"tslib\";\nimport { Component, ComponentFactoryResolver, Injector, Input, ViewChild, ViewContainerRef } from '@angular/core';\nlet SwuiGridWidgetChooserComponent = (_SwuiGridWidgetChooserComponent = class SwuiGridWidgetChooserComponent {\n  constructor(resolver) {\n    this.resolver = resolver;\n  }\n  ngOnInit() {\n    if (this.viewRef && this.registry && this.schema && this.type) {\n      const typeSchema = this.schema[this.type];\n      const config = Object.assign({}, {\n        field: this.field || this.schema.field,\n        title: this.schema.title,\n        value: this.schema.value\n      }, typeSchema ? {\n        title: typeSchema.title,\n        value: typeSchema.value\n      } : {}, {\n        row: this.row,\n        schema: this.schema,\n        action: this.action\n      });\n      if (config.value === undefined && this.row) {\n        config.value = this.row[config.field];\n      }\n      if (typeof this.dataSource !== 'undefined' && this.type === 'footer') {\n        config.dataSource = this.dataSource;\n      }\n      const {\n        component,\n        fn\n      } = this.registry.getWidgetRegistryConfig(this.type, this.schema);\n      this.viewRef.createComponent(this.resolver.resolveComponentFactory(component), 0, Injector.create({\n        providers: fn(config)\n      }));\n    }\n  }\n}, _SwuiGridWidgetChooserComponent.ctorParameters = () => [{\n  type: ComponentFactoryResolver\n}], _SwuiGridWidgetChooserComponent.propDecorators = {\n  registry: [{\n    type: Input\n  }],\n  type: [{\n    type: Input\n  }],\n  schema: [{\n    type: Input\n  }],\n  row: [{\n    type: Input\n  }],\n  field: [{\n    type: Input\n  }],\n  action: [{\n    type: Input\n  }],\n  dataSource: [{\n    type: Input\n  }],\n  viewRef: [{\n    type: ViewChild,\n    args: ['target', {\n      read: ViewContainerRef,\n      static: true\n    }]\n  }]\n}, _SwuiGridWidgetChooserComponent);\nSwuiGridWidgetChooserComponent = __decorate([Component({\n  // tslint:disable-next-line:component-selector\n  selector: '[grid-widget-chooser]',\n  template: '<div #target></div>',\n  standalone: false\n})], SwuiGridWidgetChooserComponent);\nexport { SwuiGridWidgetChooserComponent };", "map": {"version": 3, "names": ["Component", "ComponentFactoryResolver", "Injector", "Input", "ViewChild", "ViewContainerRef", "SwuiGridWidgetChooserComponent", "_SwuiGridWidgetChooserComponent", "constructor", "resolver", "ngOnInit", "viewRef", "registry", "schema", "type", "typeSchema", "config", "Object", "assign", "field", "title", "value", "row", "action", "undefined", "dataSource", "component", "fn", "getWidgetRegistryConfig", "createComponent", "resolveComponentFactory", "create", "providers", "args", "read", "static", "__decorate", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/widget-chooser/widget-chooser.component.ts"], "sourcesContent": ["import { Component, ComponentFactoryResolver, Injector, Input, OnInit, ViewChild, ViewContainerRef } from '@angular/core';\nimport { SwuiGridWidgetConfig, SwuiGridWidgetRegistry } from '../registry/registry';\nimport { SwuiGridDataSource } from '../swui-grid.datasource';\nimport { SwuiGridSchemaField } from '../swui-grid.model';\n\n@Component({\n    // tslint:disable-next-line:component-selector\n    selector: '[grid-widget-chooser]',\n    template: '<div #target></div>',\n    standalone: false\n})\nexport class SwuiGridWidgetChooserComponent implements OnInit {\n  @Input() registry?: SwuiGridWidgetRegistry;\n  @Input() type?: 'td' | 'footer';\n  @Input() schema?: SwuiGridSchemaField;\n  @Input() row?: any;\n  @Input() field?: any;\n  @Input() action?: any;\n  @Input() dataSource?: SwuiGridDataSource<any>;\n\n  @ViewChild('target', { read: ViewContainerRef, static: true })\n  private viewRef?: ViewContainerRef;\n\n  constructor( private readonly resolver: ComponentFactoryResolver ) {\n  }\n\n  ngOnInit() {\n    if (this.viewRef && this.registry && this.schema && this.type) {\n      const typeSchema = this.schema[this.type];\n      const config: SwuiGridWidgetConfig<any> = Object.assign({},\n        {\n          field: this.field || this.schema.field,\n          title: this.schema.title,\n          value: this.schema.value,\n        },\n        typeSchema ? {\n          title: typeSchema.title,\n          value: typeSchema.value\n        } : {},\n        {\n          row: this.row,\n          schema: this.schema,\n          action: this.action\n        },\n      );\n\n      if (config.value === undefined && this.row) {\n        config.value = this.row[config.field];\n      }\n\n      if (typeof this.dataSource !== 'undefined' && this.type === 'footer') {\n        config.dataSource = this.dataSource;\n      }\n\n      const { component, fn } = this.registry.getWidgetRegistryConfig(this.type, this.schema);\n      this.viewRef.createComponent(this.resolver.resolveComponentFactory(component), 0, Injector.create({\n        providers: fn(config)\n      }));\n    }\n  }\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,wBAAwB,EAAEC,QAAQ,EAAEC,KAAK,EAAUC,SAAS,EAAEC,gBAAgB,QAAQ,eAAe;AAWlH,IAAMC,8BAA8B,IAAAC,+BAAA,GAApC,MAAMD,8BAA8B;EAYzCE,YAA8BC,QAAkC;IAAlC,KAAAA,QAAQ,GAARA,QAAQ;EACtC;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,CAACC,IAAI,EAAE;MAC7D,MAAMC,UAAU,GAAG,IAAI,CAACF,MAAM,CAAC,IAAI,CAACC,IAAI,CAAC;MACzC,MAAME,MAAM,GAA8BC,MAAM,CAACC,MAAM,CAAC,EAAE,EACxD;QACEC,KAAK,EAAE,IAAI,CAACA,KAAK,IAAI,IAAI,CAACN,MAAM,CAACM,KAAK;QACtCC,KAAK,EAAE,IAAI,CAACP,MAAM,CAACO,KAAK;QACxBC,KAAK,EAAE,IAAI,CAACR,MAAM,CAACQ;OACpB,EACDN,UAAU,GAAG;QACXK,KAAK,EAAEL,UAAU,CAACK,KAAK;QACvBC,KAAK,EAAEN,UAAU,CAACM;OACnB,GAAG,EAAE,EACN;QACEC,GAAG,EAAE,IAAI,CAACA,GAAG;QACbT,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBU,MAAM,EAAE,IAAI,CAACA;OACd,CACF;MAED,IAAIP,MAAM,CAACK,KAAK,KAAKG,SAAS,IAAI,IAAI,CAACF,GAAG,EAAE;QAC1CN,MAAM,CAACK,KAAK,GAAG,IAAI,CAACC,GAAG,CAACN,MAAM,CAACG,KAAK,CAAC;MACvC;MAEA,IAAI,OAAO,IAAI,CAACM,UAAU,KAAK,WAAW,IAAI,IAAI,CAACX,IAAI,KAAK,QAAQ,EAAE;QACpEE,MAAM,CAACS,UAAU,GAAG,IAAI,CAACA,UAAU;MACrC;MAEA,MAAM;QAAEC,SAAS;QAAEC;MAAE,CAAE,GAAG,IAAI,CAACf,QAAQ,CAACgB,uBAAuB,CAAC,IAAI,CAACd,IAAI,EAAE,IAAI,CAACD,MAAM,CAAC;MACvF,IAAI,CAACF,OAAO,CAACkB,eAAe,CAAC,IAAI,CAACpB,QAAQ,CAACqB,uBAAuB,CAACJ,SAAS,CAAC,EAAE,CAAC,EAAExB,QAAQ,CAAC6B,MAAM,CAAC;QAChGC,SAAS,EAAEL,EAAE,CAACX,MAAM;OACrB,CAAC,CAAC;IACL;EACF;;;;;UA/CCb;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELC,SAAS;IAAA6B,IAAA,GAAC,QAAQ,EAAE;MAAEC,IAAI,EAAE7B,gBAAgB;MAAE8B,MAAM,EAAE;IAAI,CAAE;EAAA;;AATlD7B,8BAA8B,GAAA8B,UAAA,EAN1CpC,SAAS,CAAC;EACP;EACAqC,QAAQ,EAAE,uBAAuB;EACjCC,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE;CACf,CAAC,C,EACWjC,8BAA8B,CAiD1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}