{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { RouterModule } from '@angular/router';\nimport { SwuiTdBooleanWidgetComponent } from './boolean/boolean.widget';\nimport { SwuiTdClickWidgetComponent } from './click/click.widget';\nimport { SwuiTdColorfulLabelsWidgetComponent } from './colorful-labels/colorful-labels.widget';\nimport { SwuiTdGameLabelsWidgetComponent } from './game-labels/game-labels.widget';\nimport { SwuiTdGamesLabelsWidgetComponent } from './games-labels/games-labels.widget';\nimport { SwuiTdJackpotWidgetComponent } from './jackpot/jackpot.widget';\nimport { SwuiTdNumberWidgetComponent } from './number/number.widget';\nimport { SwuiTdStringWidgetComponent } from './string/string.widget';\nimport { SwuiTdUserWidgetComponent } from './user/user.widget';\nimport { SwuiTdPercentWidgetComponent } from './percent/percent.widget';\nimport { SwuiTdTimestampWidgetComponent } from './timestamp/timestamp.widget';\nimport { SwuiTdCalcWidgetComponent } from './calc/calc.widget';\nimport { SwuiTdCalcAsyncWidgetComponent } from './calc-async/calc-async.widget';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatIconModule } from '@angular/material/icon';\nimport { SwuiTdIconPopoverWidgetComponent } from './icon-popover/icon-popover.widget';\nimport { SwuiTdCurrencyWidgetComponent } from './currency/currency.widget';\nimport { SwuiTdLinkWidgetComponent } from './link/link.widget';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { SwuiTdIconWidgetComponent } from './icon/icon.widget';\nimport { GridPipesModule } from '../pipes/grid-pipes.module';\nimport { SwuiTdPercentEditableWidgetComponent } from './percent-editable/percent-editable.widget';\nimport { SwuiTdStatusWidgetComponent } from './status/status.widget';\nimport { SwuiTdInactivityWidgetComponent } from './inactivity/inactivity.widget';\nimport { SwuiTdListWidgetComponent } from './list/list.widget';\nimport { SwuiTdImageWidgetComponent } from './image/image.widget';\nexport const matModules = [MatTooltipModule, MatIconModule, MatMenuModule, MatButtonModule, MatProgressBarModule];\nlet SwuiTdWidgetsModule = class SwuiTdWidgetsModule {};\nSwuiTdWidgetsModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule.forChild(), RouterModule, GridPipesModule, ...matModules],\n  declarations: [SwuiTdBooleanWidgetComponent, SwuiTdColorfulLabelsWidgetComponent, SwuiTdGameLabelsWidgetComponent, SwuiTdGamesLabelsWidgetComponent, SwuiTdJackpotWidgetComponent, SwuiTdNumberWidgetComponent, SwuiTdStringWidgetComponent, SwuiTdUserWidgetComponent, SwuiTdPercentWidgetComponent, SwuiTdPercentEditableWidgetComponent, SwuiTdTimestampWidgetComponent, SwuiTdCalcWidgetComponent, SwuiTdCalcAsyncWidgetComponent, SwuiTdIconPopoverWidgetComponent, SwuiTdCurrencyWidgetComponent, SwuiTdStatusWidgetComponent, SwuiTdLinkWidgetComponent, SwuiTdIconWidgetComponent, SwuiTdInactivityWidgetComponent, SwuiTdListWidgetComponent, SwuiTdImageWidgetComponent, SwuiTdClickWidgetComponent]\n})], SwuiTdWidgetsModule);\nexport { SwuiTdWidgetsModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "TranslateModule", "RouterModule", "SwuiTdBooleanWidgetComponent", "SwuiTdClickWidgetComponent", "SwuiTdColorfulLabelsWidgetComponent", "SwuiTdGameLabelsWidgetComponent", "SwuiTdGamesLabelsWidgetComponent", "SwuiTdJackpotWidgetComponent", "SwuiTdNumberWidgetComponent", "SwuiTdStringWidgetComponent", "SwuiTdUserWidgetComponent", "SwuiTdPercentWidgetComponent", "SwuiTdTimestampWidgetComponent", "SwuiTdCalcWidgetComponent", "SwuiTdCalcAsyncWidgetComponent", "MatTooltipModule", "MatIconModule", "SwuiTdIconPopoverWidgetComponent", "SwuiTdCurrencyWidgetComponent", "SwuiTdLinkWidgetComponent", "MatMenuModule", "MatButtonModule", "MatProgressBarModule", "SwuiTdIconWidgetComponent", "GridPipesModule", "SwuiTdPercentEditableWidgetComponent", "SwuiTdStatusWidgetComponent", "SwuiTdInactivityWidgetComponent", "SwuiTdListWidgetComponent", "SwuiTdImageWidgetComponent", "matModules", "SwuiTdWidgetsModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/td-widgets.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { RouterModule } from '@angular/router';\nimport { SwuiTdBooleanWidgetComponent } from './boolean/boolean.widget';\nimport { SwuiTdClickWidgetComponent } from './click/click.widget';\nimport { SwuiTdColorfulLabelsWidgetComponent } from './colorful-labels/colorful-labels.widget';\nimport { SwuiTdGameLabelsWidgetComponent } from './game-labels/game-labels.widget';\nimport { SwuiTdGamesLabelsWidgetComponent } from './games-labels/games-labels.widget';\nimport { SwuiTdJackpotWidgetComponent } from './jackpot/jackpot.widget';\nimport { SwuiTdNumberWidgetComponent } from './number/number.widget';\nimport { SwuiTdStringWidgetComponent } from './string/string.widget';\nimport { SwuiTdUserWidgetComponent } from './user/user.widget';\nimport { SwuiTdPercentWidgetComponent } from './percent/percent.widget';\nimport { SwuiTdTimestampWidgetComponent } from './timestamp/timestamp.widget';\nimport { SwuiTdCalcWidgetComponent } from './calc/calc.widget';\nimport { SwuiTdCalcAsyncWidgetComponent } from './calc-async/calc-async.widget';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatIconModule } from '@angular/material/icon';\nimport { SwuiTdIconPopoverWidgetComponent } from './icon-popover/icon-popover.widget';\nimport { SwuiTdCurrencyWidgetComponent } from './currency/currency.widget';\nimport { SwuiTdLinkWidgetComponent } from './link/link.widget';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { SwuiTdIconWidgetComponent } from './icon/icon.widget';\nimport { GridPipesModule } from '../pipes/grid-pipes.module';\nimport { SwuiTdPercentEditableWidgetComponent } from './percent-editable/percent-editable.widget';\nimport { SwuiTdStatusWidgetComponent } from './status/status.widget';\nimport { SwuiTdInactivityWidgetComponent } from './inactivity/inactivity.widget';\nimport { SwuiTdListWidgetComponent } from './list/list.widget';\nimport { SwuiTdImageWidgetComponent } from './image/image.widget';\nexport const matModules = [\n    MatTooltipModule,\n    MatIconModule,\n    MatMenuModule,\n    MatButtonModule,\n    MatProgressBarModule,\n];\nlet SwuiTdWidgetsModule = class SwuiTdWidgetsModule {\n};\nSwuiTdWidgetsModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule.forChild(),\n            RouterModule,\n            GridPipesModule,\n            ...matModules\n        ],\n        declarations: [\n            SwuiTdBooleanWidgetComponent,\n            SwuiTdColorfulLabelsWidgetComponent,\n            SwuiTdGameLabelsWidgetComponent,\n            SwuiTdGamesLabelsWidgetComponent,\n            SwuiTdJackpotWidgetComponent,\n            SwuiTdNumberWidgetComponent,\n            SwuiTdStringWidgetComponent,\n            SwuiTdUserWidgetComponent,\n            SwuiTdPercentWidgetComponent,\n            SwuiTdPercentEditableWidgetComponent,\n            SwuiTdTimestampWidgetComponent,\n            SwuiTdCalcWidgetComponent,\n            SwuiTdCalcAsyncWidgetComponent,\n            SwuiTdIconPopoverWidgetComponent,\n            SwuiTdCurrencyWidgetComponent,\n            SwuiTdStatusWidgetComponent,\n            SwuiTdLinkWidgetComponent,\n            SwuiTdIconWidgetComponent,\n            SwuiTdInactivityWidgetComponent,\n            SwuiTdListWidgetComponent,\n            SwuiTdImageWidgetComponent,\n            SwuiTdClickWidgetComponent\n        ],\n    })\n], SwuiTdWidgetsModule);\nexport { SwuiTdWidgetsModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,SAASC,mCAAmC,QAAQ,0CAA0C;AAC9F,SAASC,+BAA+B,QAAQ,kCAAkC;AAClF,SAASC,gCAAgC,QAAQ,oCAAoC;AACrF,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,yBAAyB,QAAQ,oBAAoB;AAC9D,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,SAASC,8BAA8B,QAAQ,8BAA8B;AAC7E,SAASC,yBAAyB,QAAQ,oBAAoB;AAC9D,SAASC,8BAA8B,QAAQ,gCAAgC;AAC/E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gCAAgC,QAAQ,oCAAoC;AACrF,SAASC,6BAA6B,QAAQ,4BAA4B;AAC1E,SAASC,yBAAyB,QAAQ,oBAAoB;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,yBAAyB,QAAQ,oBAAoB;AAC9D,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,oCAAoC,QAAQ,4CAA4C;AACjG,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,+BAA+B,QAAQ,gCAAgC;AAChF,SAASC,yBAAyB,QAAQ,oBAAoB;AAC9D,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,OAAO,MAAMC,UAAU,GAAG,CACtBf,gBAAgB,EAChBC,aAAa,EACbI,aAAa,EACbC,eAAe,EACfC,oBAAoB,CACvB;AACD,IAAIS,mBAAmB,GAAG,MAAMA,mBAAmB,CAAC,EACnD;AACDA,mBAAmB,GAAGlC,UAAU,CAAC,CAC7BC,QAAQ,CAAC;EACLkC,OAAO,EAAE,CACLjC,YAAY,EACZC,eAAe,CAACiC,QAAQ,CAAC,CAAC,EAC1BhC,YAAY,EACZuB,eAAe,EACf,GAAGM,UAAU,CAChB;EACDI,YAAY,EAAE,CACVhC,4BAA4B,EAC5BE,mCAAmC,EACnCC,+BAA+B,EAC/BC,gCAAgC,EAChCC,4BAA4B,EAC5BC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,yBAAyB,EACzBC,4BAA4B,EAC5Bc,oCAAoC,EACpCb,8BAA8B,EAC9BC,yBAAyB,EACzBC,8BAA8B,EAC9BG,gCAAgC,EAChCC,6BAA6B,EAC7BQ,2BAA2B,EAC3BP,yBAAyB,EACzBI,yBAAyB,EACzBI,+BAA+B,EAC/BC,yBAAyB,EACzBC,0BAA0B,EAC1B1B,0BAA0B;AAElC,CAAC,CAAC,CACL,EAAE4B,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}