{"ast": null, "code": "import { InjectionToken } from '@angular/core';\nexport const SWUI_CONTROL_MESSAGES = new InjectionToken('SWUI_CONTROL_MESSAGES');", "map": {"version": 3, "names": ["InjectionToken", "SWUI_CONTROL_MESSAGES"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-control-messages/swui-control-messages.token.ts"], "sourcesContent": ["import { InjectionToken } from '@angular/core';\nexport const SWUI_CONTROL_MESSAGES = new InjectionToken('SWUI_CONTROL_MESSAGES');\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,eAAe;AAC9C,OAAO,MAAMC,qBAAqB,GAAG,IAAID,cAAc,CAAC,uBAAuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}