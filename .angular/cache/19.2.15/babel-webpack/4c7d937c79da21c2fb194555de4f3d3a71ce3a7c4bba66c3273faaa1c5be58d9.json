{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatExpansionPanelContent, _MatExpansionPanel, _MatExpansionPanelActionRow, _MatExpansionPanelHeader, _MatExpansionPanelDescription, _MatExpansionPanelTitle, _MatAccordion, _MatExpansionModule;\nconst _c0 = [\"body\"];\nconst _c1 = [\"bodyWrapper\"];\nconst _c2 = [[[\"mat-expansion-panel-header\"]], \"*\", [[\"mat-action-row\"]]];\nconst _c3 = [\"mat-expansion-panel-header\", \"*\", \"mat-action-row\"];\nfunction _MatExpansionPanel_ng_template_7_Template(rf, ctx) {}\nconst _c4 = [[[\"mat-panel-title\"]], [[\"mat-panel-description\"]], \"*\"];\nconst _c5 = [\"mat-panel-title\", \"mat-panel-description\", \"*\"];\nfunction _MatExpansionPanelHeader_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 2);\n    i0.ɵɵelement(2, \"path\", 3);\n    i0.ɵɵelementEnd()();\n  }\n}\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, ANIMATION_MODULE_TYPE, NgZone, ElementRef, Renderer2, EventEmitter, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ContentChild, ViewChild, ChangeDetectorRef, HostAttributeToken, numberAttribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport { TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { _IdGenerator, FocusMonitor, FocusKeyManager } from '@angular/cdk/a11y';\nimport { startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { UniqueSelectionDispatcher } from '@angular/cdk/collections';\nimport { DOCUMENT } from '@angular/common';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/bidi';\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n  constructor() {\n    _defineProperty(this, \"_template\", inject(TemplateRef));\n    _defineProperty(this, \"_expansionPanel\", inject(MAT_EXPANSION_PANEL, {\n      optional: true\n    }));\n  }\n}\n_MatExpansionPanelContent = MatExpansionPanelContent;\n_defineProperty(MatExpansionPanelContent, \"\\u0275fac\", function _MatExpansionPanelContent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatExpansionPanelContent)();\n});\n_defineProperty(MatExpansionPanelContent, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatExpansionPanelContent,\n  selectors: [[\"ng-template\", \"matExpansionPanelContent\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matExpansionPanelContent]'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n  /** Whether the toggle indicator should be hidden. */\n  get hideToggle() {\n    return this._hideToggle || this.accordion && this.accordion.hideToggle;\n  }\n  set hideToggle(value) {\n    this._hideToggle = value;\n  }\n  /** The position of the expansion indicator. */\n  get togglePosition() {\n    return this._togglePosition || this.accordion && this.accordion.togglePosition;\n  }\n  set togglePosition(value) {\n    this._togglePosition = value;\n  }\n  constructor() {\n    super();\n    _defineProperty(this, \"_viewContainerRef\", inject(ViewContainerRef));\n    _defineProperty(this, \"_animationsDisabled\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }) === 'NoopAnimations');\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_renderer\", inject(Renderer2));\n    _defineProperty(this, \"_cleanupTransitionEnd\", void 0);\n    _defineProperty(this, \"_hideToggle\", false);\n    _defineProperty(this, \"_togglePosition\", void 0);\n    /** An event emitted after the body's expansion animation happens. */\n    _defineProperty(this, \"afterExpand\", new EventEmitter());\n    /** An event emitted after the body's collapse animation happens. */\n    _defineProperty(this, \"afterCollapse\", new EventEmitter());\n    /** Stream that emits for changes in `@Input` properties. */\n    _defineProperty(this, \"_inputChanges\", new Subject());\n    /** Optionally defined accordion the expansion panel belongs to. */\n    _defineProperty(this, \"accordion\", inject(MAT_ACCORDION, {\n      optional: true,\n      skipSelf: true\n    }));\n    /** Content that will be rendered lazily. */\n    _defineProperty(this, \"_lazyContent\", void 0);\n    /** Element containing the panel's user-provided content. */\n    _defineProperty(this, \"_body\", void 0);\n    /** Element wrapping the panel body. */\n    _defineProperty(this, \"_bodyWrapper\", void 0);\n    /** Portal holding the user's content. */\n    _defineProperty(this, \"_portal\", void 0);\n    /** ID for the associated header element. Used for a11y labelling. */\n    _defineProperty(this, \"_headerId\", inject(_IdGenerator).getId('mat-expansion-panel-header-'));\n    _defineProperty(this, \"_transitionEndListener\", ({\n      target,\n      propertyName\n    }) => {\n      var _this$_bodyWrapper;\n      if (target === ((_this$_bodyWrapper = this._bodyWrapper) === null || _this$_bodyWrapper === void 0 ? void 0 : _this$_bodyWrapper.nativeElement) && propertyName === 'grid-template-rows') {\n        this._ngZone.run(() => {\n          if (this.expanded) {\n            this.afterExpand.emit();\n          } else {\n            this.afterCollapse.emit();\n          }\n        });\n      }\n    });\n    const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this._expansionDispatcher = inject(UniqueSelectionDispatcher);\n    if (defaultOptions) {\n      this.hideToggle = defaultOptions.hideToggle;\n    }\n  }\n  /** Determines whether the expansion panel should have spacing between it and its siblings. */\n  _hasSpacing() {\n    if (this.accordion) {\n      return this.expanded && this.accordion.displayMode === 'default';\n    }\n    return false;\n  }\n  /** Gets the expanded state string. */\n  _getExpandedState() {\n    return this.expanded ? 'expanded' : 'collapsed';\n  }\n  /** Toggles the expanded state of the expansion panel. */\n  toggle() {\n    this.expanded = !this.expanded;\n  }\n  /** Sets the expanded state of the expansion panel to false. */\n  close() {\n    this.expanded = false;\n  }\n  /** Sets the expanded state of the expansion panel to true. */\n  open() {\n    this.expanded = true;\n  }\n  ngAfterContentInit() {\n    if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n      // Render the content as soon as the panel becomes open.\n      this.opened.pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1)).subscribe(() => {\n        this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n      });\n    }\n    this._setupAnimationEvents();\n  }\n  ngOnChanges(changes) {\n    this._inputChanges.next(changes);\n  }\n  ngOnDestroy() {\n    var _this$_cleanupTransit;\n    super.ngOnDestroy();\n    (_this$_cleanupTransit = this._cleanupTransitionEnd) === null || _this$_cleanupTransit === void 0 || _this$_cleanupTransit.call(this);\n    this._inputChanges.complete();\n  }\n  /** Checks whether the expansion panel's content contains the currently-focused element. */\n  _containsFocus() {\n    if (this._body) {\n      const focusedElement = this._document.activeElement;\n      const bodyElement = this._body.nativeElement;\n      return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n    }\n    return false;\n  }\n  _setupAnimationEvents() {\n    // This method is defined separately, because we need to\n    // disable this logic in some internal components.\n    this._ngZone.runOutsideAngular(() => {\n      if (this._animationsDisabled) {\n        this.opened.subscribe(() => this._ngZone.run(() => this.afterExpand.emit()));\n        this.closed.subscribe(() => this._ngZone.run(() => this.afterCollapse.emit()));\n      } else {\n        setTimeout(() => {\n          const element = this._elementRef.nativeElement;\n          this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this._transitionEndListener);\n          element.classList.add('mat-expansion-panel-animations-enabled');\n        }, 200);\n      }\n    });\n  }\n}\n_MatExpansionPanel = MatExpansionPanel;\n_defineProperty(MatExpansionPanel, \"\\u0275fac\", function _MatExpansionPanel_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatExpansionPanel)();\n});\n_defineProperty(MatExpansionPanel, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatExpansionPanel,\n  selectors: [[\"mat-expansion-panel\"]],\n  contentQueries: function _MatExpansionPanel_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelContent, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n    }\n  },\n  viewQuery: function _MatExpansionPanel_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._body = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._bodyWrapper = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-expansion-panel\"],\n  hostVars: 4,\n  hostBindings: function _MatExpansionPanel_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-expanded\", ctx.expanded)(\"mat-expansion-panel-spacing\", ctx._hasSpacing());\n    }\n  },\n  inputs: {\n    hideToggle: [2, \"hideToggle\", \"hideToggle\", booleanAttribute],\n    togglePosition: \"togglePosition\"\n  },\n  outputs: {\n    afterExpand: \"afterExpand\",\n    afterCollapse: \"afterCollapse\"\n  },\n  exportAs: [\"matExpansionPanel\"],\n  features: [i0.ɵɵProvidersFeature([\n  // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n  // to the same accordion.\n  {\n    provide: MAT_ACCORDION,\n    useValue: undefined\n  }, {\n    provide: MAT_EXPANSION_PANEL,\n    useExisting: _MatExpansionPanel\n  }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c3,\n  decls: 9,\n  vars: 4,\n  consts: [[\"bodyWrapper\", \"\"], [\"body\", \"\"], [1, \"mat-expansion-panel-content-wrapper\"], [\"role\", \"region\", 1, \"mat-expansion-panel-content\", 3, \"id\"], [1, \"mat-expansion-panel-body\"], [3, \"cdkPortalOutlet\"]],\n  template: function _MatExpansionPanel_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c2);\n      i0.ɵɵprojection(0);\n      i0.ɵɵelementStart(1, \"div\", 2, 0)(3, \"div\", 3, 1)(5, \"div\", 4);\n      i0.ɵɵprojection(6, 1);\n      i0.ɵɵtemplate(7, _MatExpansionPanel_ng_template_7_Template, 0, 0, \"ng-template\", 5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(8, 2);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵattribute(\"inert\", ctx.expanded ? null : \"\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"id\", ctx.id);\n      i0.ɵɵattribute(\"aria-labelledby\", ctx._headerId);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._portal);\n    }\n  },\n  dependencies: [CdkPortalOutlet],\n  styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel',\n      exportAs: 'matExpansionPanel',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [\n      // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n      // to the same accordion.\n      {\n        provide: MAT_ACCORDION,\n        useValue: undefined\n      }, {\n        provide: MAT_EXPANSION_PANEL,\n        useExisting: MatExpansionPanel\n      }],\n      host: {\n        'class': 'mat-expansion-panel',\n        '[class.mat-expanded]': 'expanded',\n        '[class.mat-expansion-panel-spacing]': '_hasSpacing()'\n      },\n      imports: [CdkPortalOutlet],\n      template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content-wrapper\\\" [attr.inert]=\\\"expanded ? null : ''\\\" #bodyWrapper>\\n  <div class=\\\"mat-expansion-panel-content\\\"\\n       role=\\\"region\\\"\\n       [attr.aria-labelledby]=\\\"_headerId\\\"\\n       [id]=\\\"id\\\"\\n       #body>\\n    <div class=\\\"mat-expansion-panel-body\\\">\\n      <ng-content></ng-content>\\n      <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n    </div>\\n    <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"]\n    }]\n  }], () => [], {\n    hideToggle: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    togglePosition: [{\n      type: Input\n    }],\n    afterExpand: [{\n      type: Output\n    }],\n    afterCollapse: [{\n      type: Output\n    }],\n    _lazyContent: [{\n      type: ContentChild,\n      args: [MatExpansionPanelContent]\n    }],\n    _body: [{\n      type: ViewChild,\n      args: ['body']\n    }],\n    _bodyWrapper: [{\n      type: ViewChild,\n      args: ['bodyWrapper']\n    }]\n  });\n})();\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {}\n_MatExpansionPanelActionRow = MatExpansionPanelActionRow;\n_defineProperty(MatExpansionPanelActionRow, \"\\u0275fac\", function _MatExpansionPanelActionRow_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatExpansionPanelActionRow)();\n});\n_defineProperty(MatExpansionPanelActionRow, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatExpansionPanelActionRow,\n  selectors: [[\"mat-action-row\"]],\n  hostAttrs: [1, \"mat-action-row\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelActionRow, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-action-row',\n      host: {\n        class: 'mat-action-row'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader {\n  constructor() {\n    _defineProperty(this, \"panel\", inject(MatExpansionPanel, {\n      host: true\n    }));\n    _defineProperty(this, \"_element\", inject(ElementRef));\n    _defineProperty(this, \"_focusMonitor\", inject(FocusMonitor));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_parentChangeSubscription\", Subscription.EMPTY);\n    /** Height of the header while the panel is expanded. */\n    _defineProperty(this, \"expandedHeight\", void 0);\n    /** Height of the header while the panel is collapsed. */\n    _defineProperty(this, \"collapsedHeight\", void 0);\n    /** Tab index of the header. */\n    _defineProperty(this, \"tabIndex\", 0);\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const panel = this.panel;\n    const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    const accordionHideToggleChange = panel.accordion ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition']))) : EMPTY;\n    this.tabIndex = parseInt(tabIndex || '') || 0;\n    // Since the toggle state depends on an @Input on the panel, we\n    // need to subscribe and trigger change detection manually.\n    this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n      return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n    }))).subscribe(() => this._changeDetectorRef.markForCheck());\n    // Avoids focus being lost if the panel contained the focused element and was closed.\n    panel.closed.pipe(filter(() => panel._containsFocus())).subscribe(() => this._focusMonitor.focusVia(this._element, 'program'));\n    if (defaultOptions) {\n      this.expandedHeight = defaultOptions.expandedHeight;\n      this.collapsedHeight = defaultOptions.collapsedHeight;\n    }\n  }\n  /**\n   * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n   * @docs-private\n   */\n  get disabled() {\n    return this.panel.disabled;\n  }\n  /** Toggles the expanded state of the panel. */\n  _toggle() {\n    if (!this.disabled) {\n      this.panel.toggle();\n    }\n  }\n  /** Gets whether the panel is expanded. */\n  _isExpanded() {\n    return this.panel.expanded;\n  }\n  /** Gets the expanded state string of the panel. */\n  _getExpandedState() {\n    return this.panel._getExpandedState();\n  }\n  /** Gets the panel id. */\n  _getPanelId() {\n    return this.panel.id;\n  }\n  /** Gets the toggle position for the header. */\n  _getTogglePosition() {\n    return this.panel.togglePosition;\n  }\n  /** Gets whether the expand indicator should be shown. */\n  _showToggle() {\n    return !this.panel.hideToggle && !this.panel.disabled;\n  }\n  /**\n   * Gets the current height of the header. Null if no custom height has been\n   * specified, and if the default height from the stylesheet should be used.\n   */\n  _getHeaderHeight() {\n    const isExpanded = this._isExpanded();\n    if (isExpanded && this.expandedHeight) {\n      return this.expandedHeight;\n    } else if (!isExpanded && this.collapsedHeight) {\n      return this.collapsedHeight;\n    }\n    return null;\n  }\n  /** Handle keydown event calling to toggle() if appropriate. */\n  _keydown(event) {\n    switch (event.keyCode) {\n      // Toggle for space and enter keys.\n      case SPACE:\n      case ENTER:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this._toggle();\n        }\n        break;\n      default:\n        if (this.panel.accordion) {\n          this.panel.accordion._handleHeaderKeydown(event);\n        }\n        return;\n    }\n  }\n  /**\n   * Focuses the panel header. Implemented as a part of `FocusableOption`.\n   * @param origin Origin of the action that triggered the focus.\n   * @docs-private\n   */\n  focus(origin, options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._element).subscribe(origin => {\n      if (origin && this.panel.accordion) {\n        this.panel.accordion._handleHeaderFocus(this);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._parentChangeSubscription.unsubscribe();\n    this._focusMonitor.stopMonitoring(this._element);\n  }\n}\n_MatExpansionPanelHeader = MatExpansionPanelHeader;\n_defineProperty(MatExpansionPanelHeader, \"\\u0275fac\", function _MatExpansionPanelHeader_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatExpansionPanelHeader)();\n});\n_defineProperty(MatExpansionPanelHeader, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatExpansionPanelHeader,\n  selectors: [[\"mat-expansion-panel-header\"]],\n  hostAttrs: [\"role\", \"button\", 1, \"mat-expansion-panel-header\", \"mat-focus-indicator\"],\n  hostVars: 13,\n  hostBindings: function _MatExpansionPanelHeader_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _MatExpansionPanelHeader_click_HostBindingHandler() {\n        return ctx._toggle();\n      })(\"keydown\", function _MatExpansionPanelHeader_keydown_HostBindingHandler($event) {\n        return ctx._keydown($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.panel._headerId)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx._getPanelId())(\"aria-expanded\", ctx._isExpanded())(\"aria-disabled\", ctx.panel.disabled);\n      i0.ɵɵstyleProp(\"height\", ctx._getHeaderHeight());\n      i0.ɵɵclassProp(\"mat-expanded\", ctx._isExpanded())(\"mat-expansion-toggle-indicator-after\", ctx._getTogglePosition() === \"after\")(\"mat-expansion-toggle-indicator-before\", ctx._getTogglePosition() === \"before\");\n    }\n  },\n  inputs: {\n    expandedHeight: \"expandedHeight\",\n    collapsedHeight: \"collapsedHeight\",\n    tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n  },\n  ngContentSelectors: _c5,\n  decls: 5,\n  vars: 3,\n  consts: [[1, \"mat-content\"], [1, \"mat-expansion-indicator\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 -960 960 960\", \"aria-hidden\", \"true\", \"focusable\", \"false\"], [\"d\", \"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\"]],\n  template: function _MatExpansionPanelHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c4);\n      i0.ɵɵelementStart(0, \"span\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵprojection(3, 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, _MatExpansionPanelHeader_Conditional_4_Template, 3, 0, \"span\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-content-hide-toggle\", !ctx._showToggle());\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx._showToggle() ? 4 : -1);\n    }\n  },\n  styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-expansion-panel-header mat-focus-indicator',\n        'role': 'button',\n        '[attr.id]': 'panel._headerId',\n        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n        '[attr.aria-controls]': '_getPanelId()',\n        '[attr.aria-expanded]': '_isExpanded()',\n        '[attr.aria-disabled]': 'panel.disabled',\n        '[class.mat-expanded]': '_isExpanded()',\n        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n        '[style.height]': '_getHeaderHeight()',\n        '(click)': '_toggle()',\n        '(keydown)': '_keydown($event)'\n      },\n      template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n\\n@if (_showToggle()) {\\n  <span class=\\\"mat-expansion-indicator\\\">\\n    <svg\\n      xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      aria-hidden=\\\"true\\\"\\n      focusable=\\\"false\\\">\\n      <path d=\\\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\\\"/>\\n    </svg>\\n  </span>\\n}\\n\",\n      styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}\\n\"]\n    }]\n  }], () => [], {\n    expandedHeight: [{\n      type: Input\n    }],\n    collapsedHeight: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }]\n  });\n})();\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {}\n_MatExpansionPanelDescription = MatExpansionPanelDescription;\n_defineProperty(MatExpansionPanelDescription, \"\\u0275fac\", function _MatExpansionPanelDescription_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatExpansionPanelDescription)();\n});\n_defineProperty(MatExpansionPanelDescription, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatExpansionPanelDescription,\n  selectors: [[\"mat-panel-description\"]],\n  hostAttrs: [1, \"mat-expansion-panel-header-description\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelDescription, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-description',\n      host: {\n        class: 'mat-expansion-panel-header-description'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {}\n_MatExpansionPanelTitle = MatExpansionPanelTitle;\n_defineProperty(MatExpansionPanelTitle, \"\\u0275fac\", function _MatExpansionPanelTitle_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatExpansionPanelTitle)();\n});\n_defineProperty(MatExpansionPanelTitle, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatExpansionPanelTitle,\n  selectors: [[\"mat-panel-title\"]],\n  hostAttrs: [1, \"mat-expansion-panel-header-title\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelTitle, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-title',\n      host: {\n        class: 'mat-expansion-panel-header-title'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"_keyManager\", void 0);\n    /** Headers belonging to this accordion. */\n    _defineProperty(this, \"_ownHeaders\", new QueryList());\n    /** All headers inside the accordion. Includes headers inside nested accordions. */\n    _defineProperty(this, \"_headers\", void 0);\n    /** Whether the expansion indicator should be hidden. */\n    _defineProperty(this, \"hideToggle\", false);\n    /**\n     * Display mode used for all expansion panels in the accordion. Currently two display\n     * modes exist:\n     *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n     *     panel at a different elevation from the rest of the accordion.\n     *  flat - no spacing is placed around expanded panels, showing all panels at the same\n     *     elevation.\n     */\n    _defineProperty(this, \"displayMode\", 'default');\n    /** The position of the expansion indicator. */\n    _defineProperty(this, \"togglePosition\", 'after');\n  }\n  ngAfterContentInit() {\n    this._headers.changes.pipe(startWith(this._headers)).subscribe(headers => {\n      this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n      this._ownHeaders.notifyOnChanges();\n    });\n    this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n  }\n  /** Handles keyboard events coming in from the panel headers. */\n  _handleHeaderKeydown(event) {\n    this._keyManager.onKeydown(event);\n  }\n  _handleHeaderFocus(header) {\n    this._keyManager.updateActiveItem(header);\n  }\n  ngOnDestroy() {\n    var _this$_keyManager;\n    super.ngOnDestroy();\n    (_this$_keyManager = this._keyManager) === null || _this$_keyManager === void 0 || _this$_keyManager.destroy();\n    this._ownHeaders.destroy();\n  }\n}\n_MatAccordion = MatAccordion;\n_defineProperty(MatAccordion, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatAccordion_BaseFactory;\n  return function _MatAccordion_Factory(__ngFactoryType__) {\n    return (ɵ_MatAccordion_BaseFactory || (ɵ_MatAccordion_BaseFactory = i0.ɵɵgetInheritedFactory(_MatAccordion)))(__ngFactoryType__ || _MatAccordion);\n  };\n})());\n_defineProperty(MatAccordion, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatAccordion,\n  selectors: [[\"mat-accordion\"]],\n  contentQueries: function _MatAccordion_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelHeader, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headers = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-accordion\"],\n  hostVars: 2,\n  hostBindings: function _MatAccordion_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-accordion-multi\", ctx.multi);\n    }\n  },\n  inputs: {\n    hideToggle: [2, \"hideToggle\", \"hideToggle\", booleanAttribute],\n    displayMode: \"displayMode\",\n    togglePosition: \"togglePosition\"\n  },\n  exportAs: [\"matAccordion\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_ACCORDION,\n    useExisting: _MatAccordion\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-accordion',\n      exportAs: 'matAccordion',\n      providers: [{\n        provide: MAT_ACCORDION,\n        useExisting: MatAccordion\n      }],\n      host: {\n        class: 'mat-accordion',\n        // Class binding which is only used by the test harness as there is no other\n        // way for the harness to detect if multiple panel support is enabled.\n        '[class.mat-accordion-multi]': 'this.multi'\n      }\n    }]\n  }], null, {\n    _headers: [{\n      type: ContentChildren,\n      args: [MatExpansionPanelHeader, {\n        descendants: true\n      }]\n    }],\n    hideToggle: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    displayMode: [{\n      type: Input\n    }],\n    togglePosition: [{\n      type: Input\n    }]\n  });\n})();\nclass MatExpansionModule {}\n_MatExpansionModule = MatExpansionModule;\n_defineProperty(MatExpansionModule, \"\\u0275fac\", function _MatExpansionModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatExpansionModule)();\n});\n_defineProperty(MatExpansionModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatExpansionModule,\n  imports: [MatCommonModule, CdkAccordionModule, PortalModule, MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent],\n  exports: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent]\n}));\n_defineProperty(MatExpansionModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, CdkAccordionModule, PortalModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CdkAccordionModule, PortalModule, MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent],\n      exports: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Time and timing curve for expansion panel animations.\n * @deprecated No longer used. Will be removed.\n * @breaking-change 21.0.0\n */\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM. This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matExpansionAnimations = {\n  // Represents:\n  // trigger('indicatorRotate', [\n  //   state('collapsed, void', style({transform: 'rotate(0deg)'})),\n  //   state('expanded', style({transform: 'rotate(180deg)'})),\n  //   transition(\n  //     'expanded <=> collapsed, void => collapsed',\n  //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n  //   ),\n  // ])\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: {\n    type: 7,\n    name: 'indicatorRotate',\n    definitions: [{\n      type: 0,\n      name: 'collapsed, void',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(0deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'expanded',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(180deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'expanded <=> collapsed, void => collapsed',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('bodyExpansion', [\n  //   state('collapsed, void', style({height: '0px', visibility: 'hidden'})),\n  //   // Clear the `visibility` while open, otherwise the content will be visible when placed in\n  //   // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n  //   // that have a `visibility` of their own (see #27436).\n  //   state('expanded', style({height: '*', visibility: ''})),\n  //   transition(\n  //     'expanded <=> collapsed, void => collapsed',\n  //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n  //   ),\n  // ])\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: {\n    type: 7,\n    name: 'bodyExpansion',\n    definitions: [{\n      type: 0,\n      name: 'collapsed, void',\n      styles: {\n        type: 6,\n        styles: {\n          'height': '0px',\n          'visibility': 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'expanded',\n      styles: {\n        type: 6,\n        styles: {\n          'height': '*',\n          'visibility': ''\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'expanded <=> collapsed, void => collapsed',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };\n//# sourceMappingURL=expansion.mjs.map", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "InjectionToken", "inject", "TemplateRef", "Directive", "ViewContainerRef", "ANIMATION_MODULE_TYPE", "NgZone", "ElementRef", "Renderer2", "EventEmitter", "booleanAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Output", "ContentChild", "ViewChild", "ChangeDetectorRef", "HostAttributeToken", "numberAttribute", "QueryList", "ContentChildren", "NgModule", "CdkAccordionItem", "CdkAccordion", "CdkAccordionModule", "TemplatePortal", "CdkPortalOutlet", "PortalModule", "_IdGenerator", "FocusMonitor", "FocusKeyManager", "startWith", "filter", "take", "ENTER", "hasModifierKey", "SPACE", "Subject", "Subscription", "EMPTY", "merge", "UniqueSelectionDispatcher", "DOCUMENT", "_CdkPrivateStyleLoader", "_", "_StructuralStylesLoader", "M", "MatCommonModule", "MAT_ACCORDION", "MAT_EXPANSION_PANEL", "MatExpansionPanelContent", "constructor", "_defineProperty", "optional", "_MatExpansionPanelContent", "_MatExpansionPanelContent_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "MAT_EXPANSION_PANEL_DEFAULT_OPTIONS", "MatExpansionPanel", "hideToggle", "_hideToggle", "accordion", "value", "togglePosition", "_togglePosition", "skipSelf", "getId", "target", "propertyName", "_this$_bodyWrapper", "_bodyWrapper", "nativeElement", "_ngZone", "run", "expanded", "afterExpand", "emit", "afterCollapse", "defaultOptions", "_expansionDispatcher", "_hasSpacing", "displayMode", "_getExpandedState", "toggle", "close", "open", "ngAfterContentInit", "_lazyContent", "_expansionPanel", "opened", "pipe", "_portal", "subscribe", "_template", "_viewContainerRef", "_setupAnimationEvents", "ngOnChanges", "changes", "_inputChanges", "next", "ngOnDestroy", "_this$_cleanupTransit", "_cleanupTransitionEnd", "call", "complete", "_containsFocus", "_body", "focusedElement", "_document", "activeElement", "bodyElement", "contains", "runOutsideAngular", "_animationsDisabled", "closed", "setTimeout", "element", "_elementRef", "_renderer", "listen", "_transitionEndListener", "classList", "add", "_MatExpansionPanel", "_MatExpansionPanel_Factory", "ɵɵdefineComponent", "contentQueries", "_MatExpansionPanel_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "_MatExpansionPanel_Query", "ɵɵviewQuery", "_c0", "_c1", "hostAttrs", "hostVars", "hostBindings", "_MatExpansionPanel_HostBindings", "ɵɵclassProp", "inputs", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useValue", "undefined", "useExisting", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "ngContentSelectors", "_c3", "decls", "vars", "consts", "template", "_MatExpansionPanel_Template", "ɵɵprojectionDef", "_c2", "ɵɵprojection", "ɵɵtemplate", "_MatExpansionPanel_ng_template_7_Template", "ɵɵadvance", "ɵɵattribute", "ɵɵproperty", "id", "_headerId", "dependencies", "styles", "encapsulation", "changeDetection", "None", "OnPush", "providers", "host", "imports", "transform", "MatExpansionPanelActionRow", "_MatExpansionPanelActionRow", "_MatExpansionPanelActionRow_Factory", "class", "MatExpansionPanelHeader", "load", "panel", "tabIndex", "accordionHideToggleChange", "_stateChanges", "parseInt", "_parentChangeSubscription", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_focusMonitor", "focusVia", "_element", "expandedHeight", "collapsedHeight", "disabled", "_toggle", "_isExpanded", "_getPanelId", "_getTogglePosition", "_showToggle", "_getHeaderHeight", "isExpanded", "_keydown", "event", "keyCode", "preventDefault", "_handleHeaderKeydown", "focus", "origin", "options", "ngAfterViewInit", "monitor", "_handleHeaderFocus", "unsubscribe", "stopMonitoring", "_MatExpansionPanelHeader", "_MatExpansionPanelHeader_Factory", "_MatExpansionPanelHeader_HostBindings", "ɵɵlistener", "_MatExpansionPanelHeader_click_HostBindingHandler", "_MatExpansionPanelHeader_keydown_HostBindingHandler", "$event", "ɵɵstyleProp", "_c5", "_MatExpansionPanelHeader_Template", "_c4", "_MatExpansionPanelHeader_Conditional_4_Template", "ɵɵconditional", "MatExpansionPanelDescription", "_MatExpansionPanelDescription", "_MatExpansionPanelDescription_Factory", "MatExpansionPanelTitle", "_MatExpansionPanelTitle", "_MatExpansionPanelTitle_Factory", "Mat<PERSON><PERSON>rdi<PERSON>", "_headers", "headers", "_ownHeaders", "reset", "header", "notifyOn<PERSON><PERSON>es", "_keyManager", "withWrap", "withHomeAndEnd", "onKeydown", "updateActiveItem", "_this$_keyManager", "destroy", "_MatAccordion", "ɵ_MatAccordion_BaseFactory", "_MatAccordion_Factory", "ɵɵgetInheritedFactory", "_MatAccordion_ContentQueries", "_MatAccordion_HostBindings", "multi", "descendants", "MatExpansionModule", "_MatExpansionModule", "_MatExpansionModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "EXPANSION_PANEL_ANIMATION_TIMING", "matExpansionAnimations", "indicatorRotate", "name", "definitions", "offset", "expr", "animation", "timings", "bodyExpansion"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/expansion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, ANIMATION_MODULE_TYPE, NgZone, ElementRef, Renderer2, EventEmitter, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ContentChild, ViewChild, ChangeDetectorRef, HostAttributeToken, numberAttribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport { TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { _IdGenerator, FocusMonitor, FocusKeyManager } from '@angular/cdk/a11y';\nimport { startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { UniqueSelectionDispatcher } from '@angular/cdk/collections';\nimport { DOCUMENT } from '@angular/common';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/bidi';\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n    _template = inject(TemplateRef);\n    _expansionPanel = inject(MAT_EXPANSION_PANEL, { optional: true });\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanelContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatExpansionPanelContent, isStandalone: true, selector: \"ng-template[matExpansionPanelContent]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanelContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matExpansionPanelContent]',\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n    _viewContainerRef = inject(ViewContainerRef);\n    _animationsDisabled = inject(ANIMATION_MODULE_TYPE, { optional: true }) === 'NoopAnimations';\n    _document = inject(DOCUMENT);\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _renderer = inject(Renderer2);\n    _cleanupTransitionEnd;\n    /** Whether the toggle indicator should be hidden. */\n    get hideToggle() {\n        return this._hideToggle || (this.accordion && this.accordion.hideToggle);\n    }\n    set hideToggle(value) {\n        this._hideToggle = value;\n    }\n    _hideToggle = false;\n    /** The position of the expansion indicator. */\n    get togglePosition() {\n        return this._togglePosition || (this.accordion && this.accordion.togglePosition);\n    }\n    set togglePosition(value) {\n        this._togglePosition = value;\n    }\n    _togglePosition;\n    /** An event emitted after the body's expansion animation happens. */\n    afterExpand = new EventEmitter();\n    /** An event emitted after the body's collapse animation happens. */\n    afterCollapse = new EventEmitter();\n    /** Stream that emits for changes in `@Input` properties. */\n    _inputChanges = new Subject();\n    /** Optionally defined accordion the expansion panel belongs to. */\n    accordion = inject(MAT_ACCORDION, { optional: true, skipSelf: true });\n    /** Content that will be rendered lazily. */\n    _lazyContent;\n    /** Element containing the panel's user-provided content. */\n    _body;\n    /** Element wrapping the panel body. */\n    _bodyWrapper;\n    /** Portal holding the user's content. */\n    _portal;\n    /** ID for the associated header element. Used for a11y labelling. */\n    _headerId = inject(_IdGenerator).getId('mat-expansion-panel-header-');\n    constructor() {\n        super();\n        const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, { optional: true });\n        this._expansionDispatcher = inject(UniqueSelectionDispatcher);\n        if (defaultOptions) {\n            this.hideToggle = defaultOptions.hideToggle;\n        }\n    }\n    /** Determines whether the expansion panel should have spacing between it and its siblings. */\n    _hasSpacing() {\n        if (this.accordion) {\n            return this.expanded && this.accordion.displayMode === 'default';\n        }\n        return false;\n    }\n    /** Gets the expanded state string. */\n    _getExpandedState() {\n        return this.expanded ? 'expanded' : 'collapsed';\n    }\n    /** Toggles the expanded state of the expansion panel. */\n    toggle() {\n        this.expanded = !this.expanded;\n    }\n    /** Sets the expanded state of the expansion panel to false. */\n    close() {\n        this.expanded = false;\n    }\n    /** Sets the expanded state of the expansion panel to true. */\n    open() {\n        this.expanded = true;\n    }\n    ngAfterContentInit() {\n        if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n            // Render the content as soon as the panel becomes open.\n            this.opened\n                .pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1))\n                .subscribe(() => {\n                this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n            });\n        }\n        this._setupAnimationEvents();\n    }\n    ngOnChanges(changes) {\n        this._inputChanges.next(changes);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._cleanupTransitionEnd?.();\n        this._inputChanges.complete();\n    }\n    /** Checks whether the expansion panel's content contains the currently-focused element. */\n    _containsFocus() {\n        if (this._body) {\n            const focusedElement = this._document.activeElement;\n            const bodyElement = this._body.nativeElement;\n            return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n        }\n        return false;\n    }\n    _transitionEndListener = ({ target, propertyName }) => {\n        if (target === this._bodyWrapper?.nativeElement && propertyName === 'grid-template-rows') {\n            this._ngZone.run(() => {\n                if (this.expanded) {\n                    this.afterExpand.emit();\n                }\n                else {\n                    this.afterCollapse.emit();\n                }\n            });\n        }\n    };\n    _setupAnimationEvents() {\n        // This method is defined separately, because we need to\n        // disable this logic in some internal components.\n        this._ngZone.runOutsideAngular(() => {\n            if (this._animationsDisabled) {\n                this.opened.subscribe(() => this._ngZone.run(() => this.afterExpand.emit()));\n                this.closed.subscribe(() => this._ngZone.run(() => this.afterCollapse.emit()));\n            }\n            else {\n                setTimeout(() => {\n                    const element = this._elementRef.nativeElement;\n                    this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this._transitionEndListener);\n                    element.classList.add('mat-expansion-panel-animations-enabled');\n                }, 200);\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanel, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatExpansionPanel, isStandalone: true, selector: \"mat-expansion-panel\", inputs: { hideToggle: [\"hideToggle\", \"hideToggle\", booleanAttribute], togglePosition: \"togglePosition\" }, outputs: { afterExpand: \"afterExpand\", afterCollapse: \"afterCollapse\" }, host: { properties: { \"class.mat-expanded\": \"expanded\", \"class.mat-expansion-panel-spacing\": \"_hasSpacing()\" }, classAttribute: \"mat-expansion-panel\" }, providers: [\n            // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n            // to the same accordion.\n            { provide: MAT_ACCORDION, useValue: undefined },\n            { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n        ], queries: [{ propertyName: \"_lazyContent\", first: true, predicate: MatExpansionPanelContent, descendants: true }], viewQueries: [{ propertyName: \"_body\", first: true, predicate: [\"body\"], descendants: true }, { propertyName: \"_bodyWrapper\", first: true, predicate: [\"bodyWrapper\"], descendants: true }], exportAs: [\"matExpansionPanel\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content-wrapper\\\" [attr.inert]=\\\"expanded ? null : ''\\\" #bodyWrapper>\\n  <div class=\\\"mat-expansion-panel-content\\\"\\n       role=\\\"region\\\"\\n       [attr.aria-labelledby]=\\\"_headerId\\\"\\n       [id]=\\\"id\\\"\\n       #body>\\n    <div class=\\\"mat-expansion-panel-body\\\">\\n      <ng-content></ng-content>\\n      <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n    </div>\\n    <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n  </div>\\n</div>\\n\", styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanel, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel', exportAs: 'matExpansionPanel', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n                        // to the same accordion.\n                        { provide: MAT_ACCORDION, useValue: undefined },\n                        { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n                    ], host: {\n                        'class': 'mat-expansion-panel',\n                        '[class.mat-expanded]': 'expanded',\n                        '[class.mat-expansion-panel-spacing]': '_hasSpacing()',\n                    }, imports: [CdkPortalOutlet], template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content-wrapper\\\" [attr.inert]=\\\"expanded ? null : ''\\\" #bodyWrapper>\\n  <div class=\\\"mat-expansion-panel-content\\\"\\n       role=\\\"region\\\"\\n       [attr.aria-labelledby]=\\\"_headerId\\\"\\n       [id]=\\\"id\\\"\\n       #body>\\n    <div class=\\\"mat-expansion-panel-body\\\">\\n      <ng-content></ng-content>\\n      <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n    </div>\\n    <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n  </div>\\n</div>\\n\", styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { hideToggle: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], togglePosition: [{\n                type: Input\n            }], afterExpand: [{\n                type: Output\n            }], afterCollapse: [{\n                type: Output\n            }], _lazyContent: [{\n                type: ContentChild,\n                args: [MatExpansionPanelContent]\n            }], _body: [{\n                type: ViewChild,\n                args: ['body']\n            }], _bodyWrapper: [{\n                type: ViewChild,\n                args: ['bodyWrapper']\n            }] } });\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanelActionRow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatExpansionPanelActionRow, isStandalone: true, selector: \"mat-action-row\", host: { classAttribute: \"mat-action-row\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanelActionRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-action-row',\n                    host: {\n                        class: 'mat-action-row',\n                    },\n                }]\n        }] });\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader {\n    panel = inject(MatExpansionPanel, { host: true });\n    _element = inject(ElementRef);\n    _focusMonitor = inject(FocusMonitor);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _parentChangeSubscription = Subscription.EMPTY;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const panel = this.panel;\n        const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, { optional: true });\n        const tabIndex = inject(new HostAttributeToken('tabindex'), { optional: true });\n        const accordionHideToggleChange = panel.accordion\n            ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition'])))\n            : EMPTY;\n        this.tabIndex = parseInt(tabIndex || '') || 0;\n        // Since the toggle state depends on an @Input on the panel, we\n        // need to subscribe and trigger change detection manually.\n        this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n            return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n        }))).subscribe(() => this._changeDetectorRef.markForCheck());\n        // Avoids focus being lost if the panel contained the focused element and was closed.\n        panel.closed\n            .pipe(filter(() => panel._containsFocus()))\n            .subscribe(() => this._focusMonitor.focusVia(this._element, 'program'));\n        if (defaultOptions) {\n            this.expandedHeight = defaultOptions.expandedHeight;\n            this.collapsedHeight = defaultOptions.collapsedHeight;\n        }\n    }\n    /** Height of the header while the panel is expanded. */\n    expandedHeight;\n    /** Height of the header while the panel is collapsed. */\n    collapsedHeight;\n    /** Tab index of the header. */\n    tabIndex = 0;\n    /**\n     * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n     * @docs-private\n     */\n    get disabled() {\n        return this.panel.disabled;\n    }\n    /** Toggles the expanded state of the panel. */\n    _toggle() {\n        if (!this.disabled) {\n            this.panel.toggle();\n        }\n    }\n    /** Gets whether the panel is expanded. */\n    _isExpanded() {\n        return this.panel.expanded;\n    }\n    /** Gets the expanded state string of the panel. */\n    _getExpandedState() {\n        return this.panel._getExpandedState();\n    }\n    /** Gets the panel id. */\n    _getPanelId() {\n        return this.panel.id;\n    }\n    /** Gets the toggle position for the header. */\n    _getTogglePosition() {\n        return this.panel.togglePosition;\n    }\n    /** Gets whether the expand indicator should be shown. */\n    _showToggle() {\n        return !this.panel.hideToggle && !this.panel.disabled;\n    }\n    /**\n     * Gets the current height of the header. Null if no custom height has been\n     * specified, and if the default height from the stylesheet should be used.\n     */\n    _getHeaderHeight() {\n        const isExpanded = this._isExpanded();\n        if (isExpanded && this.expandedHeight) {\n            return this.expandedHeight;\n        }\n        else if (!isExpanded && this.collapsedHeight) {\n            return this.collapsedHeight;\n        }\n        return null;\n    }\n    /** Handle keydown event calling to toggle() if appropriate. */\n    _keydown(event) {\n        switch (event.keyCode) {\n            // Toggle for space and enter keys.\n            case SPACE:\n            case ENTER:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this._toggle();\n                }\n                break;\n            default:\n                if (this.panel.accordion) {\n                    this.panel.accordion._handleHeaderKeydown(event);\n                }\n                return;\n        }\n    }\n    /**\n     * Focuses the panel header. Implemented as a part of `FocusableOption`.\n     * @param origin Origin of the action that triggered the focus.\n     * @docs-private\n     */\n    focus(origin, options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._element).subscribe(origin => {\n            if (origin && this.panel.accordion) {\n                this.panel.accordion._handleHeaderFocus(this);\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._parentChangeSubscription.unsubscribe();\n        this._focusMonitor.stopMonitoring(this._element);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanelHeader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatExpansionPanelHeader, isStandalone: true, selector: \"mat-expansion-panel-header\", inputs: { expandedHeight: \"expandedHeight\", collapsedHeight: \"collapsedHeight\", tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))] }, host: { attributes: { \"role\": \"button\" }, listeners: { \"click\": \"_toggle()\", \"keydown\": \"_keydown($event)\" }, properties: { \"attr.id\": \"panel._headerId\", \"attr.tabindex\": \"disabled ? -1 : tabIndex\", \"attr.aria-controls\": \"_getPanelId()\", \"attr.aria-expanded\": \"_isExpanded()\", \"attr.aria-disabled\": \"panel.disabled\", \"class.mat-expanded\": \"_isExpanded()\", \"class.mat-expansion-toggle-indicator-after\": \"_getTogglePosition() === 'after'\", \"class.mat-expansion-toggle-indicator-before\": \"_getTogglePosition() === 'before'\", \"style.height\": \"_getHeaderHeight()\" }, classAttribute: \"mat-expansion-panel-header mat-focus-indicator\" }, ngImport: i0, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n\\n@if (_showToggle()) {\\n  <span class=\\\"mat-expansion-indicator\\\">\\n    <svg\\n      xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      aria-hidden=\\\"true\\\"\\n      focusable=\\\"false\\\">\\n      <path d=\\\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\\\"/>\\n    </svg>\\n  </span>\\n}\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanelHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mat-expansion-panel-header mat-focus-indicator',\n                        'role': 'button',\n                        '[attr.id]': 'panel._headerId',\n                        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n                        '[attr.aria-controls]': '_getPanelId()',\n                        '[attr.aria-expanded]': '_isExpanded()',\n                        '[attr.aria-disabled]': 'panel.disabled',\n                        '[class.mat-expanded]': '_isExpanded()',\n                        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n                        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n                        '[style.height]': '_getHeaderHeight()',\n                        '(click)': '_toggle()',\n                        '(keydown)': '_keydown($event)',\n                    }, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n\\n@if (_showToggle()) {\\n  <span class=\\\"mat-expansion-indicator\\\">\\n    <svg\\n      xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      aria-hidden=\\\"true\\\"\\n      focusable=\\\"false\\\">\\n      <path d=\\\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\\\"/>\\n    </svg>\\n  </span>\\n}\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { expandedHeight: [{\n                type: Input\n            }], collapsedHeight: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }] } });\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanelDescription, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatExpansionPanelDescription, isStandalone: true, selector: \"mat-panel-description\", host: { classAttribute: \"mat-expansion-panel-header-description\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanelDescription, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-description',\n                    host: {\n                        class: 'mat-expansion-panel-header-description',\n                    },\n                }]\n        }] });\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanelTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatExpansionPanelTitle, isStandalone: true, selector: \"mat-panel-title\", host: { classAttribute: \"mat-expansion-panel-header-title\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionPanelTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-title',\n                    host: {\n                        class: 'mat-expansion-panel-header-title',\n                    },\n                }]\n        }] });\n\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n    _keyManager;\n    /** Headers belonging to this accordion. */\n    _ownHeaders = new QueryList();\n    /** All headers inside the accordion. Includes headers inside nested accordions. */\n    _headers;\n    /** Whether the expansion indicator should be hidden. */\n    hideToggle = false;\n    /**\n     * Display mode used for all expansion panels in the accordion. Currently two display\n     * modes exist:\n     *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n     *     panel at a different elevation from the rest of the accordion.\n     *  flat - no spacing is placed around expanded panels, showing all panels at the same\n     *     elevation.\n     */\n    displayMode = 'default';\n    /** The position of the expansion indicator. */\n    togglePosition = 'after';\n    ngAfterContentInit() {\n        this._headers.changes\n            .pipe(startWith(this._headers))\n            .subscribe((headers) => {\n            this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n            this._ownHeaders.notifyOnChanges();\n        });\n        this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n    }\n    /** Handles keyboard events coming in from the panel headers. */\n    _handleHeaderKeydown(event) {\n        this._keyManager.onKeydown(event);\n    }\n    _handleHeaderFocus(header) {\n        this._keyManager.updateActiveItem(header);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._keyManager?.destroy();\n        this._ownHeaders.destroy();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAccordion, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatAccordion, isStandalone: true, selector: \"mat-accordion\", inputs: { hideToggle: [\"hideToggle\", \"hideToggle\", booleanAttribute], displayMode: \"displayMode\", togglePosition: \"togglePosition\" }, host: { properties: { \"class.mat-accordion-multi\": \"this.multi\" }, classAttribute: \"mat-accordion\" }, providers: [\n            {\n                provide: MAT_ACCORDION,\n                useExisting: MatAccordion,\n            },\n        ], queries: [{ propertyName: \"_headers\", predicate: MatExpansionPanelHeader, descendants: true }], exportAs: [\"matAccordion\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAccordion, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-accordion',\n                    exportAs: 'matAccordion',\n                    providers: [\n                        {\n                            provide: MAT_ACCORDION,\n                            useExisting: MatAccordion,\n                        },\n                    ],\n                    host: {\n                        class: 'mat-accordion',\n                        // Class binding which is only used by the test harness as there is no other\n                        // way for the harness to detect if multiple panel support is enabled.\n                        '[class.mat-accordion-multi]': 'this.multi',\n                    },\n                }]\n        }], propDecorators: { _headers: [{\n                type: ContentChildren,\n                args: [MatExpansionPanelHeader, { descendants: true }]\n            }], hideToggle: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], displayMode: [{\n                type: Input\n            }], togglePosition: [{\n                type: Input\n            }] } });\n\nclass MatExpansionModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionModule, imports: [MatCommonModule,\n            CdkAccordionModule,\n            PortalModule,\n            MatAccordion,\n            MatExpansionPanel,\n            MatExpansionPanelActionRow,\n            MatExpansionPanelHeader,\n            MatExpansionPanelTitle,\n            MatExpansionPanelDescription,\n            MatExpansionPanelContent], exports: [MatAccordion,\n            MatExpansionPanel,\n            MatExpansionPanelActionRow,\n            MatExpansionPanelHeader,\n            MatExpansionPanelTitle,\n            MatExpansionPanelDescription,\n            MatExpansionPanelContent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionModule, imports: [MatCommonModule,\n            CdkAccordionModule,\n            PortalModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatExpansionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        CdkAccordionModule,\n                        PortalModule,\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                    exports: [\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                }]\n        }] });\n\n/**\n * Time and timing curve for expansion panel animations.\n * @deprecated No longer used. Will be removed.\n * @breaking-change 21.0.0\n */\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM. This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matExpansionAnimations = {\n    // Represents:\n    // trigger('indicatorRotate', [\n    //   state('collapsed, void', style({transform: 'rotate(0deg)'})),\n    //   state('expanded', style({transform: 'rotate(180deg)'})),\n    //   transition(\n    //     'expanded <=> collapsed, void => collapsed',\n    //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n    //   ),\n    // ])\n    /** Animation that rotates the indicator arrow. */\n    indicatorRotate: {\n        type: 7,\n        name: 'indicatorRotate',\n        definitions: [\n            {\n                type: 0,\n                name: 'collapsed, void',\n                styles: { type: 6, styles: { transform: 'rotate(0deg)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'expanded',\n                styles: { type: 6, styles: { transform: 'rotate(180deg)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'expanded <=> collapsed, void => collapsed',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('bodyExpansion', [\n    //   state('collapsed, void', style({height: '0px', visibility: 'hidden'})),\n    //   // Clear the `visibility` while open, otherwise the content will be visible when placed in\n    //   // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n    //   // that have a `visibility` of their own (see #27436).\n    //   state('expanded', style({height: '*', visibility: ''})),\n    //   transition(\n    //     'expanded <=> collapsed, void => collapsed',\n    //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n    //   ),\n    // ])\n    /** Animation that expands and collapses the panel content. */\n    bodyExpansion: {\n        type: 7,\n        name: 'bodyExpansion',\n        definitions: [\n            {\n                type: 0,\n                name: 'collapsed, void',\n                styles: { type: 6, styles: { 'height': '0px', 'visibility': 'hidden' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'expanded',\n                styles: { type: 6, styles: { 'height': '*', 'visibility': '' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'expanded <=> collapsed, void => collapsed',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };\n//# sourceMappingURL=expansion.mjs.map\n"], "mappings": ";;;;;;;;;;;IAsCiFA,EAAE,CAAAC,cAAA,aA0U0sC,CAAC;IA1U7sCD,EAAE,CAAAE,cAAA;IAAFF,EAAE,CAAAC,cAAA,YA0U01C,CAAC;IA1U71CD,EAAE,CAAAG,SAAA,aA0Uq6C,CAAC;IA1Ux6CH,EAAE,CAAAI,YAAA,CA0Ui7C,CAAC,CAAU,CAAC;EAAA;AAAA;AAhXhhD,OAAO,KAAKJ,EAAE,MAAM,eAAe;AACnC,SAASK,cAAc,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACnX,SAASC,gBAAgB,EAAEC,YAAY,EAAEC,kBAAkB,QAAQ,wBAAwB;AAC3F,SAASC,cAAc,EAAEC,eAAe,EAAEC,YAAY,QAAQ,qBAAqB;AACnF,SAASC,YAAY,EAAEC,YAAY,EAAEC,eAAe,QAAQ,mBAAmB;AAC/E,SAASC,SAAS,EAAEC,MAAM,EAAEC,IAAI,QAAQ,gBAAgB;AACxD,SAASC,KAAK,EAAEC,cAAc,EAAEC,KAAK,QAAQ,uBAAuB;AACpE,SAASC,OAAO,EAAEC,YAAY,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC1D,SAASC,yBAAyB,QAAQ,0BAA0B;AACpE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;;AAE1B;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAIlD,cAAc,CAAC,eAAe,CAAC;;AAEzD;AACA;AACA;AACA;AACA,MAAMmD,mBAAmB,GAAG,IAAInD,cAAc,CAAC,qBAAqB,CAAC;;AAErE;AACA;AACA;AACA;AACA,MAAMoD,wBAAwB,CAAC;EAG3BC,WAAWA,CAAA,EAAG;IAAAC,eAAA,oBAFFrD,MAAM,CAACC,WAAW,CAAC;IAAAoD,eAAA,0BACbrD,MAAM,CAACkD,mBAAmB,EAAE;MAAEI,QAAQ,EAAE;IAAK,CAAC,CAAC;EACjD;AAGpB;AAACC,yBAAA,GANKJ,wBAAwB;AAAAE,eAAA,CAAxBF,wBAAwB,wBAAAK,kCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAIyEN,yBAAwB;AAAA;AAAAE,eAAA,CAJzHF,wBAAwB,8BAOmDzD,EAAE,CAAAgE,iBAAA;EAAAC,IAAA,EAFQR,yBAAwB;EAAAS,SAAA;AAAA;AAEnH;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFnE,EAAE,CAAAoE,iBAAA,CAAQX,wBAAwB,EAAc,CAAC;IACtHQ,IAAI,EAAEzD,SAAS;IACf6D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,GAAG,IAAIlE,cAAc,CAAC,qCAAqC,CAAC;AACrG;AACA;AACA;AACA;AACA,MAAMmE,iBAAiB,SAAS3C,gBAAgB,CAAC;EAQ7C;EACA,IAAI4C,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW,IAAK,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACF,UAAW;EAC5E;EACA,IAAIA,UAAUA,CAACG,KAAK,EAAE;IAClB,IAAI,CAACF,WAAW,GAAGE,KAAK;EAC5B;EAEA;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACC,eAAe,IAAK,IAAI,CAACH,SAAS,IAAI,IAAI,CAACA,SAAS,CAACE,cAAe;EACpF;EACA,IAAIA,cAAcA,CAACD,KAAK,EAAE;IACtB,IAAI,CAACE,eAAe,GAAGF,KAAK;EAChC;EAoBAlB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IAACC,eAAA,4BA1CQrD,MAAM,CAACG,gBAAgB,CAAC;IAAAkD,eAAA,8BACtBrD,MAAM,CAACI,qBAAqB,EAAE;MAAEkD,QAAQ,EAAE;IAAK,CAAC,CAAC,KAAK,gBAAgB;IAAAD,eAAA,oBAChFrD,MAAM,CAAC2C,QAAQ,CAAC;IAAAU,eAAA,kBAClBrD,MAAM,CAACK,MAAM,CAAC;IAAAgD,eAAA,sBACVrD,MAAM,CAACM,UAAU,CAAC;IAAA+C,eAAA,oBACpBrD,MAAM,CAACO,SAAS,CAAC;IAAA8C,eAAA;IAAAA,eAAA,sBASf,KAAK;IAAAA,eAAA;IASnB;IAAAA,eAAA,sBACc,IAAI7C,YAAY,CAAC,CAAC;IAChC;IAAA6C,eAAA,wBACgB,IAAI7C,YAAY,CAAC,CAAC;IAClC;IAAA6C,eAAA,wBACgB,IAAIf,OAAO,CAAC,CAAC;IAC7B;IAAAe,eAAA,oBACYrD,MAAM,CAACiD,aAAa,EAAE;MAAEK,QAAQ,EAAE,IAAI;MAAEmB,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrE;IAAApB,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,oBACYrD,MAAM,CAAC6B,YAAY,CAAC,CAAC6C,KAAK,CAAC,6BAA6B,CAAC;IAAArB,eAAA,iCA4D5C,CAAC;MAAEsB,MAAM;MAAEC;IAAa,CAAC,KAAK;MAAA,IAAAC,kBAAA;MACnD,IAAIF,MAAM,OAAAE,kBAAA,GAAK,IAAI,CAACC,YAAY,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBE,aAAa,KAAIH,YAAY,KAAK,oBAAoB,EAAE;QACtF,IAAI,CAACI,OAAO,CAACC,GAAG,CAAC,MAAM;UACnB,IAAI,IAAI,CAACC,QAAQ,EAAE;YACf,IAAI,CAACC,WAAW,CAACC,IAAI,CAAC,CAAC;UAC3B,CAAC,MACI;YACD,IAAI,CAACC,aAAa,CAACD,IAAI,CAAC,CAAC;UAC7B;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;IApEG,MAAME,cAAc,GAAGtF,MAAM,CAACiE,mCAAmC,EAAE;MAAEX,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtF,IAAI,CAACiC,oBAAoB,GAAGvF,MAAM,CAAC0C,yBAAyB,CAAC;IAC7D,IAAI4C,cAAc,EAAE;MAChB,IAAI,CAACnB,UAAU,GAAGmB,cAAc,CAACnB,UAAU;IAC/C;EACJ;EACA;EACAqB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACnB,SAAS,EAAE;MAChB,OAAO,IAAI,CAACa,QAAQ,IAAI,IAAI,CAACb,SAAS,CAACoB,WAAW,KAAK,SAAS;IACpE;IACA,OAAO,KAAK;EAChB;EACA;EACAC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACR,QAAQ,GAAG,UAAU,GAAG,WAAW;EACnD;EACA;EACAS,MAAMA,CAAA,EAAG;IACL,IAAI,CAACT,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;EAClC;EACA;EACAU,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACV,QAAQ,GAAG,KAAK;EACzB;EACA;EACAW,IAAIA,CAAA,EAAG;IACH,IAAI,CAACX,QAAQ,GAAG,IAAI;EACxB;EACAY,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,CAACA,YAAY,CAACC,eAAe,KAAK,IAAI,EAAE;MACjE;MACA,IAAI,CAACC,MAAM,CACNC,IAAI,CAAClE,SAAS,CAAC,IAAI,CAAC,EAAEC,MAAM,CAAC,MAAM,IAAI,CAACiD,QAAQ,IAAI,CAAC,IAAI,CAACiB,OAAO,CAAC,EAAEjE,IAAI,CAAC,CAAC,CAAC,CAAC,CAC5EkE,SAAS,CAAC,MAAM;QACjB,IAAI,CAACD,OAAO,GAAG,IAAIzE,cAAc,CAAC,IAAI,CAACqE,YAAY,CAACM,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAAC;MAC1F,CAAC,CAAC;IACN;IACA,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACC,aAAa,CAACC,IAAI,CAACF,OAAO,CAAC;EACpC;EACAG,WAAWA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACV,KAAK,CAACD,WAAW,CAAC,CAAC;IACnB,CAAAC,qBAAA,OAAI,CAACC,qBAAqB,cAAAD,qBAAA,eAA1BA,qBAAA,CAAAE,IAAA,KAA6B,CAAC;IAC9B,IAAI,CAACL,aAAa,CAACM,QAAQ,CAAC,CAAC;EACjC;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,MAAMC,cAAc,GAAG,IAAI,CAACC,SAAS,CAACC,aAAa;MACnD,MAAMC,WAAW,GAAG,IAAI,CAACJ,KAAK,CAACnC,aAAa;MAC5C,OAAOoC,cAAc,KAAKG,WAAW,IAAIA,WAAW,CAACC,QAAQ,CAACJ,cAAc,CAAC;IACjF;IACA,OAAO,KAAK;EAChB;EAaAZ,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,IAAI,CAACvB,OAAO,CAACwC,iBAAiB,CAAC,MAAM;MACjC,IAAI,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAACxB,MAAM,CAACG,SAAS,CAAC,MAAM,IAAI,CAACpB,OAAO,CAACC,GAAG,CAAC,MAAM,IAAI,CAACE,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5E,IAAI,CAACsC,MAAM,CAACtB,SAAS,CAAC,MAAM,IAAI,CAACpB,OAAO,CAACC,GAAG,CAAC,MAAM,IAAI,CAACI,aAAa,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;MAClF,CAAC,MACI;QACDuC,UAAU,CAAC,MAAM;UACb,MAAMC,OAAO,GAAG,IAAI,CAACC,WAAW,CAAC9C,aAAa;UAC9C,IAAI,CAAC+B,qBAAqB,GAAG,IAAI,CAACgB,SAAS,CAACC,MAAM,CAACH,OAAO,EAAE,eAAe,EAAE,IAAI,CAACI,sBAAsB,CAAC;UACzGJ,OAAO,CAACK,SAAS,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACnE,CAAC,EAAE,GAAG,CAAC;MACX;IACJ,CAAC,CAAC;EACN;AAQJ;AAACC,kBAAA,GAzIKjE,iBAAiB;AAAAb,eAAA,CAAjBa,iBAAiB,wBAAAkE,2BAAA3E,iBAAA;EAAA,YAAAA,iBAAA,IAkIgFS,kBAAiB;AAAA;AAAAb,eAAA,CAlIlHa,iBAAiB,8BAhB0DxE,EAAE,CAAA2I,iBAAA;EAAA1E,IAAA,EAmJQO,kBAAiB;EAAAN,SAAA;EAAA0E,cAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;IAAA,IAAAF,EAAA;MAnJ3B9I,EAAE,CAAAiJ,cAAA,CAAAD,QAAA,EAwJNvF,wBAAwB;IAAA;IAAA,IAAAqF,EAAA;MAAA,IAAAI,EAAA;MAxJpBlJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAL,GAAA,CAAA1C,YAAA,GAAA6C,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,SAAA,WAAAC,yBAAAT,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF9I,EAAE,CAAAwJ,WAAA,CAAAC,GAAA;MAAFzJ,EAAE,CAAAwJ,WAAA,CAAAE,GAAA;IAAA;IAAA,IAAAZ,EAAA;MAAA,IAAAI,EAAA;MAAFlJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAL,GAAA,CAAAvB,KAAA,GAAA0B,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAL,GAAA,CAAA3D,YAAA,GAAA8D,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAM,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,gCAAAhB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF9I,EAAE,CAAA+J,WAAA,iBAAAhB,GAAA,CAAAvD,QAmJwB,CAAC,gCAAjBuD,GAAA,CAAAjD,WAAA,CAAY,CAAI,CAAC;IAAA;EAAA;EAAAkE,MAAA;IAAAvF,UAAA,kCAA0G1D,gBAAgB;IAAA8D,cAAA;EAAA;EAAAoF,OAAA;IAAAxE,WAAA;IAAAE,aAAA;EAAA;EAAAuE,QAAA;EAAAC,QAAA,GAnJrJnK,EAAE,CAAAoK,kBAAA,CAmJua;EAC9e;EACA;EACA;IAAEC,OAAO,EAAE9G,aAAa;IAAE+G,QAAQ,EAAEC;EAAU,CAAC,EAC/C;IAAEF,OAAO,EAAE7G,mBAAmB;IAAEgH,WAAW,EAAEhG;EAAkB,CAAC,CACnE,GAxJwExE,EAAE,CAAAyK,0BAAA,EAAFzK,EAAE,CAAA0K,oBAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,4BAAAnC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF9I,EAAE,CAAAkL,eAAA,CAAAC,GAAA;MAAFnL,EAAE,CAAAoL,YAAA,EAwJ2Y,CAAC;MAxJ9YpL,EAAE,CAAAC,cAAA,eAwJmf,CAAC,eAAqJ,CAAC,YAA6C,CAAC;MAxJ1rBD,EAAE,CAAAoL,YAAA,KAwJwtB,CAAC;MAxJ3tBpL,EAAE,CAAAqL,UAAA,IAAAC,yCAAA,wBAwJ2wB,CAAC;MAxJ9wBtL,EAAE,CAAAI,YAAA,CAwJqyB,CAAC;MAxJxyBJ,EAAE,CAAAoL,YAAA,KAwJ81B,CAAC;MAxJj2BpL,EAAE,CAAAI,YAAA,CAwJw2B,CAAC,CAAO,CAAC;IAAA;IAAA,IAAA0I,EAAA;MAxJn3B9I,EAAE,CAAAuL,SAAA,CAwJqe,CAAC;MAxJxevL,EAAE,CAAAwL,WAAA,UAAAzC,GAAA,CAAAvD,QAAA;MAAFxF,EAAE,CAAAuL,SAAA,EAwJ0nB,CAAC;MAxJ7nBvL,EAAE,CAAAyL,UAAA,OAAA1C,GAAA,CAAA2C,EAwJ0nB,CAAC;MAxJ7nB1L,EAAE,CAAAwL,WAAA,oBAAAzC,GAAA,CAAA4C,SAAA;MAAF3L,EAAE,CAAAuL,SAAA,EAwJ0wB,CAAC;MAxJ7wBvL,EAAE,CAAAyL,UAAA,oBAAA1C,GAAA,CAAAtC,OAwJ0wB,CAAC;IAAA;EAAA;EAAAmF,YAAA,GAA87G3J,eAAe;EAAA4J,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAE3yI;EAAA,QAAA5H,SAAA,oBAAAA,SAAA,KA1JiFnE,EAAE,CAAAoE,iBAAA,CA0JQI,iBAAiB,EAAc,CAAC;IAC/GP,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,qBAAqB;MAAE4F,QAAQ,EAAE,mBAAmB;MAAE4B,aAAa,EAAE7K,iBAAiB,CAAC+K,IAAI;MAAED,eAAe,EAAE7K,uBAAuB,CAAC+K,MAAM;MAAEC,SAAS,EAAE;MAChK;MACA;MACA;QAAE7B,OAAO,EAAE9G,aAAa;QAAE+G,QAAQ,EAAEC;MAAU,CAAC,EAC/C;QAAEF,OAAO,EAAE7G,mBAAmB;QAAEgH,WAAW,EAAEhG;MAAkB,CAAC,CACnE;MAAE2H,IAAI,EAAE;QACL,OAAO,EAAE,qBAAqB;QAC9B,sBAAsB,EAAE,UAAU;QAClC,qCAAqC,EAAE;MAC3C,CAAC;MAAEC,OAAO,EAAE,CAACnK,eAAe,CAAC;MAAE+I,QAAQ,EAAE,wiBAAwiB;MAAEa,MAAM,EAAE,CAAC,6xGAA6xG;IAAE,CAAC;EACx4H,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEpH,UAAU,EAAE,CAAC;MACrDR,IAAI,EAAE9C,KAAK;MACXkD,IAAI,EAAE,CAAC;QAAEgI,SAAS,EAAEtL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8D,cAAc,EAAE,CAAC;MACjBZ,IAAI,EAAE9C;IACV,CAAC,CAAC;IAAEsE,WAAW,EAAE,CAAC;MACdxB,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEuE,aAAa,EAAE,CAAC;MAChB1B,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEiF,YAAY,EAAE,CAAC;MACfpC,IAAI,EAAE5C,YAAY;MAClBgD,IAAI,EAAE,CAACZ,wBAAwB;IACnC,CAAC,CAAC;IAAE+D,KAAK,EAAE,CAAC;MACRvD,IAAI,EAAE3C,SAAS;MACf+C,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,CAAC;IAAEe,YAAY,EAAE,CAAC;MACfnB,IAAI,EAAE3C,SAAS;MACf+C,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMiI,0BAA0B,CAAC;AAGhCC,2BAAA,GAHKD,0BAA0B;AAAA3I,eAAA,CAA1B2I,0BAA0B,wBAAAE,oCAAAzI,iBAAA;EAAA,YAAAA,iBAAA,IACuEuI,2BAA0B;AAAA;AAAA3I,eAAA,CAD3H2I,0BAA0B,8BA5LiDtM,EAAE,CAAAgE,iBAAA;EAAAC,IAAA,EA8LQqI,2BAA0B;EAAApI,SAAA;EAAAyF,SAAA;AAAA;AAErH;EAAA,QAAAxF,SAAA,oBAAAA,SAAA,KAhMiFnE,EAAE,CAAAoE,iBAAA,CAgMQkI,0BAA0B,EAAc,CAAC;IACxHrI,IAAI,EAAEzD,SAAS;IACf6D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1B6H,IAAI,EAAE;QACFM,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAM1BhJ,WAAWA,CAAA,EAAG;IAAAC,eAAA,gBALNrD,MAAM,CAACkE,iBAAiB,EAAE;MAAE2H,IAAI,EAAE;IAAK,CAAC,CAAC;IAAAxI,eAAA,mBACtCrD,MAAM,CAACM,UAAU,CAAC;IAAA+C,eAAA,wBACbrD,MAAM,CAAC8B,YAAY,CAAC;IAAAuB,eAAA,6BACfrD,MAAM,CAACiB,iBAAiB,CAAC;IAAAoC,eAAA,oCAClBd,YAAY,CAACC,KAAK;IAwB9C;IAAAa,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,mBACW,CAAC;IA3BRrD,MAAM,CAAC4C,sBAAsB,CAAC,CAACyJ,IAAI,CAACvJ,uBAAuB,CAAC;IAC5D,MAAMwJ,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMhH,cAAc,GAAGtF,MAAM,CAACiE,mCAAmC,EAAE;MAAEX,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtF,MAAMiJ,QAAQ,GAAGvM,MAAM,CAAC,IAAIkB,kBAAkB,CAAC,UAAU,CAAC,EAAE;MAAEoC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC/E,MAAMkJ,yBAAyB,GAAGF,KAAK,CAACjI,SAAS,GAC3CiI,KAAK,CAACjI,SAAS,CAACoI,aAAa,CAACvG,IAAI,CAACjE,MAAM,CAACwE,OAAO,IAAI,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAC7GjE,KAAK;IACX,IAAI,CAAC+J,QAAQ,GAAGG,QAAQ,CAACH,QAAQ,IAAI,EAAE,CAAC,IAAI,CAAC;IAC7C;IACA;IACA,IAAI,CAACI,yBAAyB,GAAGlK,KAAK,CAAC6J,KAAK,CAACrG,MAAM,EAAEqG,KAAK,CAAC5E,MAAM,EAAE8E,yBAAyB,EAAEF,KAAK,CAAC5F,aAAa,CAACR,IAAI,CAACjE,MAAM,CAACwE,OAAO,IAAI;MACrI,OAAO,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC,CAAC,CAACL,SAAS,CAAC,MAAM,IAAI,CAACwG,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAAC;IAC5D;IACAP,KAAK,CAAC5E,MAAM,CACPxB,IAAI,CAACjE,MAAM,CAAC,MAAMqK,KAAK,CAACrF,cAAc,CAAC,CAAC,CAAC,CAAC,CAC1Cb,SAAS,CAAC,MAAM,IAAI,CAAC0G,aAAa,CAACC,QAAQ,CAAC,IAAI,CAACC,QAAQ,EAAE,SAAS,CAAC,CAAC;IAC3E,IAAI1H,cAAc,EAAE;MAChB,IAAI,CAAC2H,cAAc,GAAG3H,cAAc,CAAC2H,cAAc;MACnD,IAAI,CAACC,eAAe,GAAG5H,cAAc,CAAC4H,eAAe;IACzD;EACJ;EAOA;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACb,KAAK,CAACa,QAAQ;EAC9B;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;MAChB,IAAI,CAACb,KAAK,CAAC3G,MAAM,CAAC,CAAC;IACvB;EACJ;EACA;EACA0H,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACf,KAAK,CAACpH,QAAQ;EAC9B;EACA;EACAQ,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC4G,KAAK,CAAC5G,iBAAiB,CAAC,CAAC;EACzC;EACA;EACA4H,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChB,KAAK,CAAClB,EAAE;EACxB;EACA;EACAmC,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACjB,KAAK,CAAC/H,cAAc;EACpC;EACA;EACAiJ,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,IAAI,CAAClB,KAAK,CAACnI,UAAU,IAAI,CAAC,IAAI,CAACmI,KAAK,CAACa,QAAQ;EACzD;EACA;AACJ;AACA;AACA;EACIM,gBAAgBA,CAAA,EAAG;IACf,MAAMC,UAAU,GAAG,IAAI,CAACL,WAAW,CAAC,CAAC;IACrC,IAAIK,UAAU,IAAI,IAAI,CAACT,cAAc,EAAE;MACnC,OAAO,IAAI,CAACA,cAAc;IAC9B,CAAC,MACI,IAAI,CAACS,UAAU,IAAI,IAAI,CAACR,eAAe,EAAE;MAC1C,OAAO,IAAI,CAACA,eAAe;IAC/B;IACA,OAAO,IAAI;EACf;EACA;EACAS,QAAQA,CAACC,KAAK,EAAE;IACZ,QAAQA,KAAK,CAACC,OAAO;MACjB;MACA,KAAKxL,KAAK;MACV,KAAKF,KAAK;QACN,IAAI,CAACC,cAAc,CAACwL,KAAK,CAAC,EAAE;UACxBA,KAAK,CAACE,cAAc,CAAC,CAAC;UACtB,IAAI,CAACV,OAAO,CAAC,CAAC;QAClB;QACA;MACJ;QACI,IAAI,IAAI,CAACd,KAAK,CAACjI,SAAS,EAAE;UACtB,IAAI,CAACiI,KAAK,CAACjI,SAAS,CAAC0J,oBAAoB,CAACH,KAAK,CAAC;QACpD;QACA;IACR;EACJ;EACA;AACJ;AACA;AACA;AACA;EACII,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAID,MAAM,EAAE;MACR,IAAI,CAACnB,aAAa,CAACC,QAAQ,CAAC,IAAI,CAACC,QAAQ,EAAEiB,MAAM,EAAEC,OAAO,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAAClB,QAAQ,CAACjI,aAAa,CAACiJ,KAAK,CAACE,OAAO,CAAC;IAC9C;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACrB,aAAa,CAACsB,OAAO,CAAC,IAAI,CAACpB,QAAQ,CAAC,CAAC5G,SAAS,CAAC6H,MAAM,IAAI;MAC1D,IAAIA,MAAM,IAAI,IAAI,CAAC3B,KAAK,CAACjI,SAAS,EAAE;QAChC,IAAI,CAACiI,KAAK,CAACjI,SAAS,CAACgK,kBAAkB,CAAC,IAAI,CAAC;MACjD;IACJ,CAAC,CAAC;EACN;EACAzH,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+F,yBAAyB,CAAC2B,WAAW,CAAC,CAAC;IAC5C,IAAI,CAACxB,aAAa,CAACyB,cAAc,CAAC,IAAI,CAACvB,QAAQ,CAAC;EACpD;AAGJ;AAACwB,wBAAA,GA9HKpC,uBAAuB;AAAA/I,eAAA,CAAvB+I,uBAAuB,wBAAAqC,iCAAAhL,iBAAA;EAAA,YAAAA,iBAAA,IA4H0E2I,wBAAuB;AAAA;AAAA/I,eAAA,CA5HxH+I,uBAAuB,8BA7MoD1M,EAAE,CAAA2I,iBAAA;EAAA1E,IAAA,EA0UQyI,wBAAuB;EAAAxI,SAAA;EAAAyF,SAAA,WAA2Q,QAAQ;EAAAC,QAAA;EAAAC,YAAA,WAAAmF,sCAAAlG,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA1UpT9I,EAAE,CAAAiP,UAAA,mBAAAC,kDAAA;QAAA,OA0UQnG,GAAA,CAAA2E,OAAA,CAAQ,CAAC;MAAA,CAAa,CAAC,qBAAAyB,oDAAAC,MAAA;QAAA,OAAvBrG,GAAA,CAAAkF,QAAA,CAAAmB,MAAe,CAAC;MAAA,CAAM,CAAC;IAAA;IAAA,IAAAtG,EAAA;MA1UjC9I,EAAE,CAAAwL,WAAA,OAAAzC,GAAA,CAAA6D,KAAA,CAAAjB,SAAA,cAAA5C,GAAA,CAAA0E,QAAA,IA0UoB,CAAC,GAAA1E,GAAA,CAAA8D,QAAA,mBAAb9D,GAAA,CAAA6E,WAAA,CAAY,CAAC,mBAAb7E,GAAA,CAAA4E,WAAA,CAAY,CAAC,mBAAA5E,GAAA,CAAA6D,KAAA,CAAAa,QAAA;MA1UvBzN,EAAE,CAAAqP,WAAA,WA0UQtG,GAAA,CAAAgF,gBAAA,CAAiB,CAAK,CAAC;MA1UjC/N,EAAE,CAAA+J,WAAA,iBA0UQhB,GAAA,CAAA4E,WAAA,CAAY,CAAU,CAAC,yCAAvB5E,GAAA,CAAA8E,kBAAA,CAAmB,CAAC,KAAK,OAAH,CAAC,0CAAvB9E,GAAA,CAAA8E,kBAAA,CAAmB,CAAC,KAAK,QAAH,CAAC;IAAA;EAAA;EAAA7D,MAAA;IAAAuD,cAAA;IAAAC,eAAA;IAAAX,QAAA,8BAAkLjI,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGnD,eAAe,CAACmD,KAAK,CAAE;EAAA;EAAA+F,kBAAA,EAAA2E,GAAA;EAAAzE,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAuE,kCAAAzG,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA1UzQ9I,EAAE,CAAAkL,eAAA,CAAAsE,GAAA;MAAFxP,EAAE,CAAAC,cAAA,aA0Uy+B,CAAC;MA1U5+BD,EAAE,CAAAoL,YAAA,EA0UiiC,CAAC;MA1UpiCpL,EAAE,CAAAoL,YAAA,KA0U+lC,CAAC;MA1UlmCpL,EAAE,CAAAoL,YAAA,KA0U4nC,CAAC;MA1U/nCpL,EAAE,CAAAI,YAAA,CA0UqoC,CAAC;MA1UxoCJ,EAAE,CAAAqL,UAAA,IAAAoE,+CAAA,iBA0U8pC,CAAC;IAAA;IAAA,IAAA3G,EAAA;MA1UjqC9I,EAAE,CAAA+J,WAAA,6BAAAhB,GAAA,CAAA+E,WAAA,EA0Uw+B,CAAC;MA1U3+B9N,EAAE,CAAAuL,SAAA,EA0U+7C,CAAC;MA1Ul8CvL,EAAE,CAAA0P,aAAA,CAAA3G,GAAA,CAAA+E,WAAA,WA0U+7C,CAAC;IAAA;EAAA;EAAAjC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAEnhD;EAAA,QAAA5H,SAAA,oBAAAA,SAAA,KA5UiFnE,EAAE,CAAAoE,iBAAA,CA4UQsI,uBAAuB,EAAc,CAAC;IACrHzI,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEwH,aAAa,EAAE7K,iBAAiB,CAAC+K,IAAI;MAAED,eAAe,EAAE7K,uBAAuB,CAAC+K,MAAM;MAAEE,IAAI,EAAE;QACnI,OAAO,EAAE,gDAAgD;QACzD,MAAM,EAAE,QAAQ;QAChB,WAAW,EAAE,iBAAiB;QAC9B,iBAAiB,EAAE,0BAA0B;QAC7C,sBAAsB,EAAE,eAAe;QACvC,sBAAsB,EAAE,eAAe;QACvC,sBAAsB,EAAE,gBAAgB;QACxC,sBAAsB,EAAE,eAAe;QACvC,8CAA8C,EAAE,kCAAkC;QAClF,+CAA+C,EAAE,mCAAmC;QACpF,gBAAgB,EAAE,oBAAoB;QACtC,SAAS,EAAE,WAAW;QACtB,WAAW,EAAE;MACjB,CAAC;MAAEnB,QAAQ,EAAE,yiBAAyiB;MAAEa,MAAM,EAAE,CAAC,6gJAA6gJ;IAAE,CAAC;EAC7lK,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE0B,cAAc,EAAE,CAAC;MACzDtJ,IAAI,EAAE9C;IACV,CAAC,CAAC;IAAEqM,eAAe,EAAE,CAAC;MAClBvJ,IAAI,EAAE9C;IACV,CAAC,CAAC;IAAE0L,QAAQ,EAAE,CAAC;MACX5I,IAAI,EAAE9C,KAAK;MACXkD,IAAI,EAAE,CAAC;QACCgI,SAAS,EAAGzH,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGnD,eAAe,CAACmD,KAAK;MACpE,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAM+K,4BAA4B,CAAC;AAGlCC,6BAAA,GAHKD,4BAA4B;AAAAhM,eAAA,CAA5BgM,4BAA4B,wBAAAE,sCAAA9L,iBAAA;EAAA,YAAAA,iBAAA,IACqE4L,6BAA4B;AAAA;AAAAhM,eAAA,CAD7HgM,4BAA4B,8BA1W+C3P,EAAE,CAAAgE,iBAAA;EAAAC,IAAA,EA4WQ0L,6BAA4B;EAAAzL,SAAA;EAAAyF,SAAA;AAAA;AAEvH;EAAA,QAAAxF,SAAA,oBAAAA,SAAA,KA9WiFnE,EAAE,CAAAoE,iBAAA,CA8WQuL,4BAA4B,EAAc,CAAC;IAC1H1L,IAAI,EAAEzD,SAAS;IACf6D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjC6H,IAAI,EAAE;QACFM,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAMqD,sBAAsB,CAAC;AAG5BC,uBAAA,GAHKD,sBAAsB;AAAAnM,eAAA,CAAtBmM,sBAAsB,wBAAAE,gCAAAjM,iBAAA;EAAA,YAAAA,iBAAA,IAC2E+L,uBAAsB;AAAA;AAAAnM,eAAA,CADvHmM,sBAAsB,8BA1XqD9P,EAAE,CAAAgE,iBAAA;EAAAC,IAAA,EA4XQ6L,uBAAsB;EAAA5L,SAAA;EAAAyF,SAAA;AAAA;AAEjH;EAAA,QAAAxF,SAAA,oBAAAA,SAAA,KA9XiFnE,EAAE,CAAAoE,iBAAA,CA8XQ0L,sBAAsB,EAAc,CAAC;IACpH7L,IAAI,EAAEzD,SAAS;IACf6D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3B6H,IAAI,EAAE;QACFM,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMwD,YAAY,SAASnO,YAAY,CAAC;EAAA4B,YAAA,GAAAW,IAAA;IAAA,SAAAA,IAAA;IAAAV,eAAA;IAEpC;IAAAA,eAAA,sBACc,IAAIjC,SAAS,CAAC,CAAC;IAC7B;IAAAiC,eAAA;IAEA;IAAAA,eAAA,qBACa,KAAK;IAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IAPIA,eAAA,sBAQc,SAAS;IACvB;IAAAA,eAAA,yBACiB,OAAO;EAAA;EACxByC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC8J,QAAQ,CAACnJ,OAAO,CAChBP,IAAI,CAAClE,SAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9BxJ,SAAS,CAAEyJ,OAAO,IAAK;MACxB,IAAI,CAACC,WAAW,CAACC,KAAK,CAACF,OAAO,CAAC5N,MAAM,CAAC+N,MAAM,IAAIA,MAAM,CAAC1D,KAAK,CAACjI,SAAS,KAAK,IAAI,CAAC,CAAC;MACjF,IAAI,CAACyL,WAAW,CAACG,eAAe,CAAC,CAAC;IACtC,CAAC,CAAC;IACF,IAAI,CAACC,WAAW,GAAG,IAAInO,eAAe,CAAC,IAAI,CAAC+N,WAAW,CAAC,CAACK,QAAQ,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;EACxF;EACA;EACArC,oBAAoBA,CAACH,KAAK,EAAE;IACxB,IAAI,CAACsC,WAAW,CAACG,SAAS,CAACzC,KAAK,CAAC;EACrC;EACAS,kBAAkBA,CAAC2B,MAAM,EAAE;IACvB,IAAI,CAACE,WAAW,CAACI,gBAAgB,CAACN,MAAM,CAAC;EAC7C;EACApJ,WAAWA,CAAA,EAAG;IAAA,IAAA2J,iBAAA;IACV,KAAK,CAAC3J,WAAW,CAAC,CAAC;IACnB,CAAA2J,iBAAA,OAAI,CAACL,WAAW,cAAAK,iBAAA,eAAhBA,iBAAA,CAAkBC,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACV,WAAW,CAACU,OAAO,CAAC,CAAC;EAC9B;AAQJ;AAACC,aAAA,GA/CKd,YAAY;AAAAtM,eAAA,CAAZsM,YAAY;EAAA,IAAAe,0BAAA;EAAA,gBAAAC,sBAAAlN,iBAAA;IAAA,QAAAiN,0BAAA,KAAAA,0BAAA,GA3Y+DhR,EAAE,CAAAkR,qBAAA,CAmboBjB,aAAY,IAAAlM,iBAAA,IAAZkM,aAAY;EAAA;AAAA;AAAAtM,eAAA,CAxC7GsM,YAAY,8BA3Y+DjQ,EAAE,CAAAgE,iBAAA;EAAAC,IAAA,EAobQgM,aAAY;EAAA/L,SAAA;EAAA0E,cAAA,WAAAuI,6BAAArI,EAAA,EAAAC,GAAA,EAAAC,QAAA;IAAA,IAAAF,EAAA;MApbtB9I,EAAE,CAAAiJ,cAAA,CAAAD,QAAA,EAybvB0D,uBAAuB;IAAA;IAAA,IAAA5D,EAAA;MAAA,IAAAI,EAAA;MAzbFlJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAL,GAAA,CAAAmH,QAAA,GAAAhH,EAAA;IAAA;EAAA;EAAAS,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAuH,2BAAAtI,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF9I,EAAE,CAAA+J,WAAA,wBAAAhB,GAAA,CAAAsI,KAobmB,CAAC;IAAA;EAAA;EAAArH,MAAA;IAAAvF,UAAA,kCAAoG1D,gBAAgB;IAAAgF,WAAA;IAAAlB,cAAA;EAAA;EAAAqF,QAAA;EAAAC,QAAA,GApb1InK,EAAE,CAAAoK,kBAAA,CAob4T,CACnY;IACIC,OAAO,EAAE9G,aAAa;IACtBiH,WAAW,EAAEyF;EACjB,CAAC,CACJ,GAzbwEjQ,EAAE,CAAAyK,0BAAA;AAAA;AA2bnF;EAAA,QAAAtG,SAAA,oBAAAA,SAAA,KA3biFnE,EAAE,CAAAoE,iBAAA,CA2bQ6L,YAAY,EAAc,CAAC;IAC1GhM,IAAI,EAAEzD,SAAS;IACf6D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzB4F,QAAQ,EAAE,cAAc;MACxBgC,SAAS,EAAE,CACP;QACI7B,OAAO,EAAE9G,aAAa;QACtBiH,WAAW,EAAEyF;MACjB,CAAC,CACJ;MACD9D,IAAI,EAAE;QACFM,KAAK,EAAE,eAAe;QACtB;QACA;QACA,6BAA6B,EAAE;MACnC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEyD,QAAQ,EAAE,CAAC;MACzBjM,IAAI,EAAEtC,eAAe;MACrB0C,IAAI,EAAE,CAACqI,uBAAuB,EAAE;QAAE4E,WAAW,EAAE;MAAK,CAAC;IACzD,CAAC,CAAC;IAAE7M,UAAU,EAAE,CAAC;MACbR,IAAI,EAAE9C,KAAK;MACXkD,IAAI,EAAE,CAAC;QAAEgI,SAAS,EAAEtL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgF,WAAW,EAAE,CAAC;MACd9B,IAAI,EAAE9C;IACV,CAAC,CAAC;IAAE0D,cAAc,EAAE,CAAC;MACjBZ,IAAI,EAAE9C;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoQ,kBAAkB,CAAC;AAqBxBC,mBAAA,GArBKD,kBAAkB;AAAA5N,eAAA,CAAlB4N,kBAAkB,wBAAAE,4BAAA1N,iBAAA;EAAA,YAAAA,iBAAA,IAC+EwN,mBAAkB;AAAA;AAAA5N,eAAA,CADnH4N,kBAAkB,8BAzdyDvR,EAAE,CAAA0R,gBAAA;EAAAzN,IAAA,EA2dqBsN,mBAAkB;EAAAnF,OAAA,GAAY9I,eAAe,EACzIvB,kBAAkB,EAClBG,YAAY,EACZ+N,YAAY,EACZzL,iBAAiB,EACjB8H,0BAA0B,EAC1BI,uBAAuB,EACvBoD,sBAAsB,EACtBH,4BAA4B,EAC5BlM,wBAAwB;EAAAkO,OAAA,GAAa1B,YAAY,EACjDzL,iBAAiB,EACjB8H,0BAA0B,EAC1BI,uBAAuB,EACvBoD,sBAAsB,EACtBH,4BAA4B,EAC5BlM,wBAAwB;AAAA;AAAAE,eAAA,CAjB9B4N,kBAAkB,8BAzdyDvR,EAAE,CAAA4R,gBAAA;EAAAxF,OAAA,GA2emD9I,eAAe,EACzIvB,kBAAkB,EAClBG,YAAY;AAAA;AAExB;EAAA,QAAAiC,SAAA,oBAAAA,SAAA,KA/eiFnE,EAAE,CAAAoE,iBAAA,CA+eQmN,kBAAkB,EAAc,CAAC;IAChHtN,IAAI,EAAErC,QAAQ;IACdyC,IAAI,EAAE,CAAC;MACC+H,OAAO,EAAE,CACL9I,eAAe,EACfvB,kBAAkB,EAClBG,YAAY,EACZ+N,YAAY,EACZzL,iBAAiB,EACjB8H,0BAA0B,EAC1BI,uBAAuB,EACvBoD,sBAAsB,EACtBH,4BAA4B,EAC5BlM,wBAAwB,CAC3B;MACDkO,OAAO,EAAE,CACL1B,YAAY,EACZzL,iBAAiB,EACjB8H,0BAA0B,EAC1BI,uBAAuB,EACvBoD,sBAAsB,EACtBH,4BAA4B,EAC5BlM,wBAAwB;IAEhC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMoO,gCAAgC,GAAG,mCAAmC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,eAAe,EAAE;IACb9N,IAAI,EAAE,CAAC;IACP+N,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,CACT;MACIhO,IAAI,EAAE,CAAC;MACP+N,IAAI,EAAE,iBAAiB;MACvBnG,MAAM,EAAE;QAAE5H,IAAI,EAAE,CAAC;QAAE4H,MAAM,EAAE;UAAEQ,SAAS,EAAE;QAAe,CAAC;QAAE6F,MAAM,EAAE;MAAK;IAC3E,CAAC,EACD;MACIjO,IAAI,EAAE,CAAC;MACP+N,IAAI,EAAE,UAAU;MAChBnG,MAAM,EAAE;QAAE5H,IAAI,EAAE,CAAC;QAAE4H,MAAM,EAAE;UAAEQ,SAAS,EAAE;QAAiB,CAAC;QAAE6F,MAAM,EAAE;MAAK;IAC7E,CAAC,EACD;MACIjO,IAAI,EAAE,CAAC;MACPkO,IAAI,EAAE,2CAA2C;MACjDC,SAAS,EAAE;QAAEnO,IAAI,EAAE,CAAC;QAAE4H,MAAM,EAAE,IAAI;QAAEwG,OAAO,EAAE;MAAoC,CAAC;MAClF7D,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA8D,aAAa,EAAE;IACXrO,IAAI,EAAE,CAAC;IACP+N,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE,CACT;MACIhO,IAAI,EAAE,CAAC;MACP+N,IAAI,EAAE,iBAAiB;MACvBnG,MAAM,EAAE;QAAE5H,IAAI,EAAE,CAAC;QAAE4H,MAAM,EAAE;UAAE,QAAQ,EAAE,KAAK;UAAE,YAAY,EAAE;QAAS,CAAC;QAAEqG,MAAM,EAAE;MAAK;IACzF,CAAC,EACD;MACIjO,IAAI,EAAE,CAAC;MACP+N,IAAI,EAAE,UAAU;MAChBnG,MAAM,EAAE;QAAE5H,IAAI,EAAE,CAAC;QAAE4H,MAAM,EAAE;UAAE,QAAQ,EAAE,GAAG;UAAE,YAAY,EAAE;QAAG,CAAC;QAAEqG,MAAM,EAAE;MAAK;IACjF,CAAC,EACD;MACIjO,IAAI,EAAE,CAAC;MACPkO,IAAI,EAAE,2CAA2C;MACjDC,SAAS,EAAE;QAAEnO,IAAI,EAAE,CAAC;QAAE4H,MAAM,EAAE,IAAI;QAAEwG,OAAO,EAAE;MAAoC,CAAC;MAClF7D,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAASqD,gCAAgC,EAAEtO,aAAa,EAAEC,mBAAmB,EAAEe,mCAAmC,EAAE0L,YAAY,EAAEsB,kBAAkB,EAAE/M,iBAAiB,EAAE8H,0BAA0B,EAAE7I,wBAAwB,EAAEkM,4BAA4B,EAAEjD,uBAAuB,EAAEoD,sBAAsB,EAAEgC,sBAAsB;AACpU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}