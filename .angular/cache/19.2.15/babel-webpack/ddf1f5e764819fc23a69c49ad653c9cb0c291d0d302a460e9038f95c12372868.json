{"ast": null, "code": "var _SwuiTimeDurationComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-time-duration.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-time-duration.component.scss?ngResource\";\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormBuilder, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as moment from 'moment';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { takeUntil } from 'rxjs/operators';\nconst CONTROL_NAME = 'lib-swui-time-duration';\nlet nextUniqueId = 0;\nlet SwuiTimeDurationComponent = (_SwuiTimeDurationComponent = class SwuiTimeDurationComponent extends SwuiMatFormFieldControl {\n  get value() {\n    return this._value;\n  }\n  set value(val) {\n    this._value = coerceNumberProperty(val, 0);\n    this.patchForm(this._value);\n    this.stateChanges.next(undefined);\n  }\n  get daysDisabled() {\n    return this._daysDisabled;\n  }\n  set daysDisabled(value) {\n    this._daysDisabled = coerceBooleanProperty(value);\n    this._daysDisabled ? this.daysControl.disable() : this.daysControl.enable();\n  }\n  get secondsDisabled() {\n    return this._secondsDisabled;\n  }\n  set secondsDisabled(value) {\n    this._secondsDisabled = coerceBooleanProperty(value);\n    const sec = this.form.get('seconds');\n    sec.disable();\n  }\n  get empty() {\n    return this.value === null;\n  }\n  get shouldLabelFloat() {\n    return true;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, fb) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.controlType = CONTROL_NAME;\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._daysDisabled = false;\n    this._secondsDisabled = false;\n    this._value = null;\n    this.form = fb.group({\n      days: [''],\n      hours: [''],\n      minutes: [''],\n      seconds: ['']\n    }, {\n      updateOn: 'blur'\n    });\n  }\n  ngOnInit() {\n    this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n      this.processInputValue();\n    });\n  }\n  get daysControl() {\n    return this.form.get('days');\n  }\n  onContainerClick(event) {\n    if (this.daysInput && event.target.tagName.toLowerCase() !== 'input') {\n      this.daysInput.nativeElement.focus();\n    }\n  }\n  writeValue(value) {\n    if (!value) {\n      return;\n    }\n    this.patchForm(value);\n  }\n  processInputValue() {\n    let {\n      days,\n      hours,\n      minutes,\n      seconds\n    } = this.form.value;\n    let checkedDays = !days || isNaN(days) ? 0 : days;\n    let checkedHours = !hours || isNaN(hours) ? 0 : hours;\n    let checkedMinutes = !minutes || isNaN(minutes) ? 0 : minutes;\n    let checkedSeconds = !seconds || isNaN(seconds) ? 0 : seconds;\n    const daysAsMilliseconds = moment.duration(parseInt(checkedDays, 10), 'days').asMilliseconds();\n    const hoursAsMilliseconds = moment.duration(parseInt(checkedHours, 10), 'hours').asMilliseconds();\n    const minutesAsMilliseconds = moment.duration(parseInt(checkedMinutes, 10), 'minute').asMilliseconds();\n    const secondsAsMilliseconds = moment.duration(parseInt(checkedSeconds, 10), 'seconds').asMilliseconds();\n    const result = daysAsMilliseconds + hoursAsMilliseconds + minutesAsMilliseconds + secondsAsMilliseconds;\n    let duration = this.secondsToHms(result);\n    this.form.patchValue(duration, {\n      emitEvent: false\n    });\n    this._value = result;\n    this.onChange(result);\n  }\n  onDisabledState(disabled) {\n    disabled ? this.form.disable() : this.form.enable();\n  }\n  isErrorState() {\n    const daysState = this.daysInput ? this.daysInput.nativeElement.errorState : false;\n    const hoursState = this.hoursInput ? this.hoursInput.nativeElement.errorState : false;\n    const minutesState = this.minutesInput ? this.minutesInput.nativeElement.errorState : false;\n    const secondsState = this.secondsInput ? this.secondsInput.nativeElement.errorState : false;\n    return daysState || hoursState || minutesState || secondsState;\n  }\n  patchForm(value) {\n    const time = this.secondsToHms(value);\n    this.form.patchValue(time);\n  }\n  secondsToHms(v) {\n    let total_seconds = Math.floor(v / 1000);\n    let total_minutes = Math.floor(total_seconds / 60);\n    let total_hours = Math.floor(total_minutes / 60);\n    let total_days = Math.floor(total_hours / 24);\n    let seconds_result = total_seconds % 60;\n    let minutes_result = total_minutes % 60;\n    let hours_result = total_hours % 24;\n    const processValue = value => value < 10 ? '0' + value.toString() : value.toString();\n    return {\n      days: processValue(total_days),\n      hours: processValue(hours_result),\n      minutes: processValue(minutes_result),\n      seconds: processValue(seconds_result)\n    };\n  }\n}, _SwuiTimeDurationComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}, {\n  type: UntypedFormBuilder\n}], _SwuiTimeDurationComponent.propDecorators = {\n  value: [{\n    type: Input\n  }],\n  daysDisabled: [{\n    type: Input\n  }],\n  secondsDisabled: [{\n    type: Input\n  }],\n  daysInput: [{\n    type: ViewChild,\n    args: ['days']\n  }],\n  hoursInput: [{\n    type: ViewChild,\n    args: ['hours']\n  }],\n  minutesInput: [{\n    type: ViewChild,\n    args: ['minutes']\n  }],\n  secondsInput: [{\n    type: ViewChild,\n    args: ['seconds']\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiTimeDurationComponent);\nSwuiTimeDurationComponent = __decorate([Component({\n  selector: 'lib-swui-time-duration',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiTimeDurationComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTimeDurationComponent);\nexport { SwuiTimeDurationComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormBuilder", "FormGroupDirective", "NgControl", "MatFormFieldControl", "FocusMonitor", "coerceBooleanProperty", "coerceNumberProperty", "moment", "SwuiMatFormFieldControl", "ErrorStateMatcher", "takeUntil", "CONTROL_NAME", "nextUniqueId", "SwuiTimeDurationComponent", "_SwuiTimeDurationComponent", "value", "_value", "val", "patchForm", "stateChanges", "next", "undefined", "daysDisabled", "_daysDisabled", "daysControl", "disable", "enable", "secondsDisabled", "_secondsDisabled", "sec", "form", "get", "empty", "shouldLabelFloat", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "fb", "controlType", "id", "group", "days", "hours", "minutes", "seconds", "updateOn", "ngOnInit", "valueChanges", "pipe", "destroyed$", "subscribe", "processInputValue", "onContainerClick", "event", "daysInput", "target", "tagName", "toLowerCase", "nativeElement", "focus", "writeValue", "checkedDays", "isNaN", "checkedHours", "checkedMinutes", "checkedSeconds", "daysAsMilliseconds", "duration", "parseInt", "asMilliseconds", "hoursAsMilliseconds", "minutesAsMilliseconds", "secondsAsMilliseconds", "result", "secondsToHms", "patchValue", "emitEvent", "onChange", "onDisabledState", "disabled", "isErrorState", "daysState", "errorState", "hoursState", "hoursInput", "minutesState", "minutesInput", "secondsState", "secondsInput", "time", "v", "total_seconds", "Math", "floor", "total_minutes", "total_hours", "total_days", "seconds_result", "minutes_result", "hours_result", "processValue", "toString", "ctorParameters", "type", "decorators", "propDecorators", "args", "selector", "template", "providers", "provide", "useExisting", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-time-duration/swui-time-duration.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-time-duration.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-time-duration.component.scss?ngResource\";\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormBuilder, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as moment from 'moment';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { takeUntil } from 'rxjs/operators';\nconst CONTROL_NAME = 'lib-swui-time-duration';\nlet nextUniqueId = 0;\nlet SwuiTimeDurationComponent = class SwuiTimeDurationComponent extends SwuiMatFormFieldControl {\n    get value() {\n        return this._value;\n    }\n    set value(val) {\n        this._value = coerceNumberProperty(val, 0);\n        this.patchForm(this._value);\n        this.stateChanges.next(undefined);\n    }\n    get daysDisabled() {\n        return this._daysDisabled;\n    }\n    set daysDisabled(value) {\n        this._daysDisabled = coerceBooleanProperty(value);\n        this._daysDisabled ? this.daysControl.disable() : this.daysControl.enable();\n    }\n    get secondsDisabled() {\n        return this._secondsDisabled;\n    }\n    set secondsDisabled(value) {\n        this._secondsDisabled = coerceBooleanProperty(value);\n        const sec = this.form.get('seconds');\n        sec.disable();\n    }\n    get empty() {\n        return this.value === null;\n    }\n    get shouldLabelFloat() {\n        return true;\n    }\n    constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, fb) {\n        super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n        this.controlType = CONTROL_NAME;\n        this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n        this._daysDisabled = false;\n        this._secondsDisabled = false;\n        this._value = null;\n        this.form = fb.group({\n            days: [''],\n            hours: [''],\n            minutes: [''],\n            seconds: [''],\n        }, { updateOn: 'blur' });\n    }\n    ngOnInit() {\n        this.form.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n            this.processInputValue();\n        });\n    }\n    get daysControl() {\n        return this.form.get('days');\n    }\n    onContainerClick(event) {\n        if (this.daysInput && event.target.tagName.toLowerCase() !== 'input') {\n            this.daysInput.nativeElement.focus();\n        }\n    }\n    writeValue(value) {\n        if (!value) {\n            return;\n        }\n        this.patchForm(value);\n    }\n    processInputValue() {\n        let { days, hours, minutes, seconds } = this.form.value;\n        let checkedDays = !days || isNaN(days) ? 0 : days;\n        let checkedHours = !hours || isNaN(hours) ? 0 : hours;\n        let checkedMinutes = !minutes || isNaN(minutes) ? 0 : minutes;\n        let checkedSeconds = !seconds || isNaN(seconds) ? 0 : seconds;\n        const daysAsMilliseconds = moment.duration(parseInt(checkedDays, 10), 'days').asMilliseconds();\n        const hoursAsMilliseconds = moment.duration(parseInt(checkedHours, 10), 'hours').asMilliseconds();\n        const minutesAsMilliseconds = moment.duration(parseInt(checkedMinutes, 10), 'minute').asMilliseconds();\n        const secondsAsMilliseconds = moment.duration(parseInt(checkedSeconds, 10), 'seconds').asMilliseconds();\n        const result = daysAsMilliseconds + hoursAsMilliseconds + minutesAsMilliseconds + secondsAsMilliseconds;\n        let duration = this.secondsToHms(result);\n        this.form.patchValue(duration, { emitEvent: false });\n        this._value = result;\n        this.onChange(result);\n    }\n    onDisabledState(disabled) {\n        disabled ? this.form.disable() : this.form.enable();\n    }\n    isErrorState() {\n        const daysState = this.daysInput ? this.daysInput.nativeElement.errorState : false;\n        const hoursState = this.hoursInput ? this.hoursInput.nativeElement.errorState : false;\n        const minutesState = this.minutesInput ? this.minutesInput.nativeElement.errorState : false;\n        const secondsState = this.secondsInput ? this.secondsInput.nativeElement.errorState : false;\n        return daysState || hoursState || minutesState || secondsState;\n    }\n    patchForm(value) {\n        const time = this.secondsToHms(value);\n        this.form.patchValue(time);\n    }\n    secondsToHms(v) {\n        let total_seconds = Math.floor(v / 1000);\n        let total_minutes = Math.floor(total_seconds / 60);\n        let total_hours = Math.floor(total_minutes / 60);\n        let total_days = Math.floor(total_hours / 24);\n        let seconds_result = total_seconds % 60;\n        let minutes_result = total_minutes % 60;\n        let hours_result = total_hours % 24;\n        const processValue = (value) => value < 10 ? '0' + value.toString() : value.toString();\n        return {\n            days: processValue(total_days),\n            hours: processValue(hours_result),\n            minutes: processValue(minutes_result),\n            seconds: processValue(seconds_result)\n        };\n    }\n    static { this.ctorParameters = () => [\n        { type: FocusMonitor },\n        { type: ElementRef },\n        { type: NgControl, decorators: [{ type: Optional }, { type: Self }] },\n        { type: FormGroupDirective, decorators: [{ type: Optional }] },\n        { type: ErrorStateMatcher },\n        { type: UntypedFormBuilder }\n    ]; }\n    static { this.propDecorators = {\n        value: [{ type: Input }],\n        daysDisabled: [{ type: Input }],\n        secondsDisabled: [{ type: Input }],\n        daysInput: [{ type: ViewChild, args: ['days',] }],\n        hoursInput: [{ type: ViewChild, args: ['hours',] }],\n        minutesInput: [{ type: ViewChild, args: ['minutes',] }],\n        secondsInput: [{ type: ViewChild, args: ['seconds',] }],\n        id: [{ type: HostBinding }],\n        shouldLabelFloat: [{ type: HostBinding, args: ['class.floating',] }]\n    }; }\n};\nSwuiTimeDurationComponent = __decorate([\n    Component({\n        selector: 'lib-swui-time-duration',\n        template: __NG_CLI_RESOURCE__0,\n        providers: [{ provide: MatFormFieldControl, useExisting: SwuiTimeDurationComponent }],\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTimeDurationComponent);\nexport { SwuiTimeDurationComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,SAASC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AACpG,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAClF,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,MAAMC,YAAY,GAAG,wBAAwB;AAC7C,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,SAASL,uBAAuB,CAAC;EAC5F,IAAIO,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACE,GAAG,EAAE;IACX,IAAI,CAACD,MAAM,GAAGV,oBAAoB,CAACW,GAAG,EAAE,CAAC,CAAC;IAC1C,IAAI,CAACC,SAAS,CAAC,IAAI,CAACF,MAAM,CAAC;IAC3B,IAAI,CAACG,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;EACrC;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAACP,KAAK,EAAE;IACpB,IAAI,CAACQ,aAAa,GAAGlB,qBAAqB,CAACU,KAAK,CAAC;IACjD,IAAI,CAACQ,aAAa,GAAG,IAAI,CAACC,WAAW,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAACD,WAAW,CAACE,MAAM,CAAC,CAAC;EAC/E;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACZ,KAAK,EAAE;IACvB,IAAI,CAACa,gBAAgB,GAAGvB,qBAAqB,CAACU,KAAK,CAAC;IACpD,MAAMc,GAAG,GAAG,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,SAAS,CAAC;IACpCF,GAAG,CAACJ,OAAO,CAAC,CAAC;EACjB;EACA,IAAIO,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjB,KAAK,KAAK,IAAI;EAC9B;EACA,IAAIkB,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI;EACf;EACAC,WAAWA,CAACC,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,EAAE,EAAE;IACtE,KAAK,CAACL,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAC/D,IAAI,CAACE,WAAW,GAAG9B,YAAY;IAC/B,IAAI,CAAC+B,EAAE,GAAG,GAAG/B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAC7C,IAAI,CAACW,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACK,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACZ,MAAM,GAAG,IAAI;IAClB,IAAI,CAACc,IAAI,GAAGU,EAAE,CAACG,KAAK,CAAC;MACjBC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,OAAO,EAAE,CAAC,EAAE;IAChB,CAAC,EAAE;MAAEC,QAAQ,EAAE;IAAO,CAAC,CAAC;EAC5B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACnB,IAAI,CAACoB,YAAY,CAACC,IAAI,CAACzC,SAAS,CAAC,IAAI,CAAC0C,UAAU,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;MACpE,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA,IAAI9B,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACM,IAAI,CAACC,GAAG,CAAC,MAAM,CAAC;EAChC;EACAwB,gBAAgBA,CAACC,KAAK,EAAE;IACpB,IAAI,IAAI,CAACC,SAAS,IAAID,KAAK,CAACE,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,OAAO,EAAE;MAClE,IAAI,CAACH,SAAS,CAACI,aAAa,CAACC,KAAK,CAAC,CAAC;IACxC;EACJ;EACAC,UAAUA,CAAChD,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA,IAAI,CAACG,SAAS,CAACH,KAAK,CAAC;EACzB;EACAuC,iBAAiBA,CAAA,EAAG;IAChB,IAAI;MAAEV,IAAI;MAAEC,KAAK;MAAEC,OAAO;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACjB,IAAI,CAACf,KAAK;IACvD,IAAIiD,WAAW,GAAG,CAACpB,IAAI,IAAIqB,KAAK,CAACrB,IAAI,CAAC,GAAG,CAAC,GAAGA,IAAI;IACjD,IAAIsB,YAAY,GAAG,CAACrB,KAAK,IAAIoB,KAAK,CAACpB,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACrD,IAAIsB,cAAc,GAAG,CAACrB,OAAO,IAAImB,KAAK,CAACnB,OAAO,CAAC,GAAG,CAAC,GAAGA,OAAO;IAC7D,IAAIsB,cAAc,GAAG,CAACrB,OAAO,IAAIkB,KAAK,CAAClB,OAAO,CAAC,GAAG,CAAC,GAAGA,OAAO;IAC7D,MAAMsB,kBAAkB,GAAG9D,MAAM,CAAC+D,QAAQ,CAACC,QAAQ,CAACP,WAAW,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,CAACQ,cAAc,CAAC,CAAC;IAC9F,MAAMC,mBAAmB,GAAGlE,MAAM,CAAC+D,QAAQ,CAACC,QAAQ,CAACL,YAAY,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,CAACM,cAAc,CAAC,CAAC;IACjG,MAAME,qBAAqB,GAAGnE,MAAM,CAAC+D,QAAQ,CAACC,QAAQ,CAACJ,cAAc,EAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,CAACK,cAAc,CAAC,CAAC;IACtG,MAAMG,qBAAqB,GAAGpE,MAAM,CAAC+D,QAAQ,CAACC,QAAQ,CAACH,cAAc,EAAE,EAAE,CAAC,EAAE,SAAS,CAAC,CAACI,cAAc,CAAC,CAAC;IACvG,MAAMI,MAAM,GAAGP,kBAAkB,GAAGI,mBAAmB,GAAGC,qBAAqB,GAAGC,qBAAqB;IACvG,IAAIL,QAAQ,GAAG,IAAI,CAACO,YAAY,CAACD,MAAM,CAAC;IACxC,IAAI,CAAC9C,IAAI,CAACgD,UAAU,CAACR,QAAQ,EAAE;MAAES,SAAS,EAAE;IAAM,CAAC,CAAC;IACpD,IAAI,CAAC/D,MAAM,GAAG4D,MAAM;IACpB,IAAI,CAACI,QAAQ,CAACJ,MAAM,CAAC;EACzB;EACAK,eAAeA,CAACC,QAAQ,EAAE;IACtBA,QAAQ,GAAG,IAAI,CAACpD,IAAI,CAACL,OAAO,CAAC,CAAC,GAAG,IAAI,CAACK,IAAI,CAACJ,MAAM,CAAC,CAAC;EACvD;EACAyD,YAAYA,CAAA,EAAG;IACX,MAAMC,SAAS,GAAG,IAAI,CAAC3B,SAAS,GAAG,IAAI,CAACA,SAAS,CAACI,aAAa,CAACwB,UAAU,GAAG,KAAK;IAClF,MAAMC,UAAU,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC1B,aAAa,CAACwB,UAAU,GAAG,KAAK;IACrF,MAAMG,YAAY,GAAG,IAAI,CAACC,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC5B,aAAa,CAACwB,UAAU,GAAG,KAAK;IAC3F,MAAMK,YAAY,GAAG,IAAI,CAACC,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC9B,aAAa,CAACwB,UAAU,GAAG,KAAK;IAC3F,OAAOD,SAAS,IAAIE,UAAU,IAAIE,YAAY,IAAIE,YAAY;EAClE;EACAxE,SAASA,CAACH,KAAK,EAAE;IACb,MAAM6E,IAAI,GAAG,IAAI,CAACf,YAAY,CAAC9D,KAAK,CAAC;IACrC,IAAI,CAACe,IAAI,CAACgD,UAAU,CAACc,IAAI,CAAC;EAC9B;EACAf,YAAYA,CAACgB,CAAC,EAAE;IACZ,IAAIC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,GAAG,IAAI,CAAC;IACxC,IAAII,aAAa,GAAGF,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAII,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACC,aAAa,GAAG,EAAE,CAAC;IAChD,IAAIE,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC7C,IAAIE,cAAc,GAAGN,aAAa,GAAG,EAAE;IACvC,IAAIO,cAAc,GAAGJ,aAAa,GAAG,EAAE;IACvC,IAAIK,YAAY,GAAGJ,WAAW,GAAG,EAAE;IACnC,MAAMK,YAAY,GAAIxF,KAAK,IAAKA,KAAK,GAAG,EAAE,GAAG,GAAG,GAAGA,KAAK,CAACyF,QAAQ,CAAC,CAAC,GAAGzF,KAAK,CAACyF,QAAQ,CAAC,CAAC;IACtF,OAAO;MACH5D,IAAI,EAAE2D,YAAY,CAACJ,UAAU,CAAC;MAC9BtD,KAAK,EAAE0D,YAAY,CAACD,YAAY,CAAC;MACjCxD,OAAO,EAAEyD,YAAY,CAACF,cAAc,CAAC;MACrCtD,OAAO,EAAEwD,YAAY,CAACH,cAAc;IACxC,CAAC;EACL;AAoBJ,CAAC,EAnBYtF,0BAAA,CAAK2F,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEtG;AAAa,CAAC,EACtB;EAAEsG,IAAI,EAAEhH;AAAW,CAAC,EACpB;EAAEgH,IAAI,EAAExG,SAAS;EAAEyG,UAAU,EAAE,CAAC;IAAED,IAAI,EAAE7G;EAAS,CAAC,EAAE;IAAE6G,IAAI,EAAE5G;EAAK,CAAC;AAAE,CAAC,EACrE;EAAE4G,IAAI,EAAEzG,kBAAkB;EAAE0G,UAAU,EAAE,CAAC;IAAED,IAAI,EAAE7G;EAAS,CAAC;AAAE,CAAC,EAC9D;EAAE6G,IAAI,EAAEjG;AAAkB,CAAC,EAC3B;EAAEiG,IAAI,EAAE1G;AAAmB,CAAC,CAC/B,EACQc,0BAAA,CAAK8F,cAAc,GAAG;EAC3B7F,KAAK,EAAE,CAAC;IAAE2F,IAAI,EAAE9G;EAAM,CAAC,CAAC;EACxB0B,YAAY,EAAE,CAAC;IAAEoF,IAAI,EAAE9G;EAAM,CAAC,CAAC;EAC/B+B,eAAe,EAAE,CAAC;IAAE+E,IAAI,EAAE9G;EAAM,CAAC,CAAC;EAClC6D,SAAS,EAAE,CAAC;IAAEiD,IAAI,EAAE3G,SAAS;IAAE8G,IAAI,EAAE,CAAC,MAAM;EAAG,CAAC,CAAC;EACjDtB,UAAU,EAAE,CAAC;IAAEmB,IAAI,EAAE3G,SAAS;IAAE8G,IAAI,EAAE,CAAC,OAAO;EAAG,CAAC,CAAC;EACnDpB,YAAY,EAAE,CAAC;IAAEiB,IAAI,EAAE3G,SAAS;IAAE8G,IAAI,EAAE,CAAC,SAAS;EAAG,CAAC,CAAC;EACvDlB,YAAY,EAAE,CAAC;IAAEe,IAAI,EAAE3G,SAAS;IAAE8G,IAAI,EAAE,CAAC,SAAS;EAAG,CAAC,CAAC;EACvDnE,EAAE,EAAE,CAAC;IAAEgE,IAAI,EAAE/G;EAAY,CAAC,CAAC;EAC3BsC,gBAAgB,EAAE,CAAC;IAAEyE,IAAI,EAAE/G,WAAW;IAAEkH,IAAI,EAAE,CAAC,gBAAgB;EAAG,CAAC;AACvE,CAAC,EAAA/F,0BAAA,CACJ;AACDD,yBAAyB,GAAGvB,UAAU,CAAC,CACnCG,SAAS,CAAC;EACNqH,QAAQ,EAAE,wBAAwB;EAClCC,QAAQ,EAAExH,oBAAoB;EAC9ByH,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAE9G,mBAAmB;IAAE+G,WAAW,EAAErG;EAA0B,CAAC,CAAC;EACrFsG,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC5H,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEqB,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}