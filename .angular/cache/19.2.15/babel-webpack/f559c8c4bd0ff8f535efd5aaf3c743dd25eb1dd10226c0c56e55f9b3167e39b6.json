{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { L as ListKeyManager } from './list-key-manager-CyOIXo8P.mjs';\nclass FocusKeyManager extends ListKeyManager {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"_origin\", 'program');\n  }\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  setActiveItem(item) {\n    super.setActiveItem(item);\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\nexport { FocusKeyManager as F };\n//# sourceMappingURL=focus-key-manager-C1rAQJ5z.mjs.map", "map": {"version": 3, "names": ["L", "ListKeyManager", "FocusKeyManager", "constructor", "args", "_defineProperty", "setFocusOrigin", "origin", "_origin", "setActiveItem", "item", "activeItem", "focus", "F"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/focus-key-manager-C1rAQJ5z.mjs"], "sourcesContent": ["import { L as ListKeyManager } from './list-key-manager-CyOIXo8P.mjs';\n\nclass FocusKeyManager extends ListKeyManager {\n    _origin = 'program';\n    /**\n     * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n     * @param origin Focus origin to be used when focusing items.\n     */\n    setFocusOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    setActiveItem(item) {\n        super.setActiveItem(item);\n        if (this.activeItem) {\n            this.activeItem.focus(this._origin);\n        }\n    }\n}\n\nexport { FocusKeyManager as F };\n//# sourceMappingURL=focus-key-manager-C1rAQJ5z.mjs.map\n"], "mappings": ";AAAA,SAASA,CAAC,IAAIC,cAAc,QAAQ,iCAAiC;AAErE,MAAMC,eAAe,SAASD,cAAc,CAAC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAC,eAAA,kBAC/B,SAAS;EAAA;EACnB;AACJ;AACA;AACA;EACIC,cAAcA,CAACC,MAAM,EAAE;IACnB,IAAI,CAACC,OAAO,GAAGD,MAAM;IACrB,OAAO,IAAI;EACf;EACAE,aAAaA,CAACC,IAAI,EAAE;IAChB,KAAK,CAACD,aAAa,CAACC,IAAI,CAAC;IACzB,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACC,KAAK,CAAC,IAAI,CAACJ,OAAO,CAAC;IACvC;EACJ;AACJ;AAEA,SAASN,eAAe,IAAIW,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}