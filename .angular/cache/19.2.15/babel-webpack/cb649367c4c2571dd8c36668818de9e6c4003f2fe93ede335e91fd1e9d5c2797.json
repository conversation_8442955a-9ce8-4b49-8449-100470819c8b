{"ast": null, "code": "var _ExtraColumnChooserComponent;\nimport { __decorate } from \"tslib\";\nimport { Component, ComponentFactoryResolver, EventEmitter, Input, Output, ViewChild, ViewContainerRef } from '@angular/core';\nimport { extraColumnTypes } from '../games-select-manager.service';\nimport { CoinValueColumnComponent } from './coin-value-column.component';\nimport { GameCoeffColumnComponent } from './game-coeff-column.component';\nlet ExtraColumnChooserComponent = (_ExtraColumnChooserComponent = class ExtraColumnChooserComponent {\n  set disabled(value) {\n    const valueChanged = this._disabled !== value;\n    this._disabled = value;\n    if (valueChanged && this.componentRef && 'instance' in this.componentRef) {\n      this.componentRef.instance.disabled = true;\n    }\n  }\n  get disabled() {\n    return this._disabled;\n  }\n  constructor(resolver) {\n    this.resolver = resolver;\n    this.valueChange = new EventEmitter();\n    this.entryComponentsMap = {\n      [extraColumnTypes.coinValue]: CoinValueColumnComponent,\n      [extraColumnTypes.gameCoeff]: GameCoeffColumnComponent\n    };\n    this._disabled = false;\n  }\n  ngOnInit() {\n    this.createComponent();\n  }\n  ngOnDestroy() {\n    if (this.componentRef) {\n      this.componentRef.destroy();\n    }\n  }\n  get componentClass() {\n    if (this.extraColumn) {\n      return this.entryComponentsMap[this.extraColumn.type];\n    }\n  }\n  createComponent() {\n    if (this.container) {\n      this.container.clear();\n      const factory = this.resolver.resolveComponentFactory(this.componentClass);\n      this.componentRef = this.container.createComponent(factory);\n      const data = {\n        game: this.game,\n        values: this.extraColumn && this.extraColumn.valuesAccessor(this.game),\n        params: this.extraColumn && this.extraColumn.params,\n        valueChange: this.valueChange,\n        disabled: this.disabled\n      };\n      this.componentRef.instance.initWithData(data);\n    }\n  }\n}, _ExtraColumnChooserComponent.ctorParameters = () => [{\n  type: ComponentFactoryResolver\n}], _ExtraColumnChooserComponent.propDecorators = {\n  extraColumn: [{\n    type: Input\n  }],\n  game: [{\n    type: Input\n  }],\n  disabled: [{\n    type: Input\n  }],\n  valueChange: [{\n    type: Output\n  }],\n  container: [{\n    type: ViewChild,\n    args: ['target', {\n      read: ViewContainerRef,\n      static: true\n    }]\n  }]\n}, _ExtraColumnChooserComponent);\nExtraColumnChooserComponent = __decorate([Component({\n  // tslint:disable-next-line:component-selector\n  selector: '[extra-column-chooser]',\n  template: '<div class=\"extra-column-chooser\" #target></div>',\n  standalone: false\n})], ExtraColumnChooserComponent);\nexport { ExtraColumnChooserComponent };", "map": {"version": 3, "names": ["__decorate", "Component", "ComponentFactoryResolver", "EventEmitter", "Input", "Output", "ViewChild", "ViewContainerRef", "extraColumnTypes", "CoinValueColumnComponent", "GameCoeffColumnComponent", "ExtraColumnChooserComponent", "_ExtraColumnChooserComponent", "disabled", "value", "valueChanged", "_disabled", "componentRef", "instance", "constructor", "resolver", "valueChange", "entryComponentsMap", "coinValue", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "createComponent", "ngOnDestroy", "destroy", "componentClass", "extraColumn", "type", "container", "clear", "factory", "resolveComponentFactory", "data", "game", "values", "valuesAccessor", "params", "initWithData", "ctorParameters", "propDecorators", "args", "read", "static", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/extra-column-chooser.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Component, ComponentFactoryResolver, EventEmitter, Input, Output, ViewChild, ViewContainerRef } from '@angular/core';\nimport { extraColumnTypes } from '../games-select-manager.service';\nimport { CoinValueColumnComponent } from './coin-value-column.component';\nimport { GameCoeffColumnComponent } from './game-coeff-column.component';\nlet ExtraColumnChooserComponent = class ExtraColumnChooserComponent {\n    set disabled(value) {\n        const valueChanged = this._disabled !== value;\n        this._disabled = value;\n        if (valueChanged && this.componentRef && 'instance' in this.componentRef) {\n            this.componentRef.instance.disabled = true;\n        }\n    }\n    get disabled() {\n        return this._disabled;\n    }\n    constructor(resolver) {\n        this.resolver = resolver;\n        this.valueChange = new EventEmitter();\n        this.entryComponentsMap = {\n            [extraColumnTypes.coinValue]: CoinValueColumnComponent,\n            [extraColumnTypes.gameCoeff]: GameCoeffColumnComponent\n        };\n        this._disabled = false;\n    }\n    ngOnInit() {\n        this.createComponent();\n    }\n    ngOnDestroy() {\n        if (this.componentRef) {\n            this.componentRef.destroy();\n        }\n    }\n    get componentClass() {\n        if (this.extraColumn) {\n            return this.entryComponentsMap[this.extraColumn.type];\n        }\n    }\n    createComponent() {\n        if (this.container) {\n            this.container.clear();\n            const factory = this.resolver.resolveComponentFactory(this.componentClass);\n            this.componentRef = this.container.createComponent(factory);\n            const data = {\n                game: this.game,\n                values: this.extraColumn && this.extraColumn.valuesAccessor(this.game),\n                params: this.extraColumn && this.extraColumn.params,\n                valueChange: this.valueChange,\n                disabled: this.disabled,\n            };\n            this.componentRef.instance.initWithData(data);\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: ComponentFactoryResolver }\n    ]; }\n    static { this.propDecorators = {\n        extraColumn: [{ type: Input }],\n        game: [{ type: Input }],\n        disabled: [{ type: Input }],\n        valueChange: [{ type: Output }],\n        container: [{ type: ViewChild, args: ['target', { read: ViewContainerRef, static: true },] }]\n    }; }\n};\nExtraColumnChooserComponent = __decorate([\n    Component({\n        // tslint:disable-next-line:component-selector\n        selector: '[extra-column-chooser]',\n        template: '<div class=\"extra-column-chooser\" #target></div>',\n        standalone: false\n    })\n], ExtraColumnChooserComponent);\nexport { ExtraColumnChooserComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,wBAAwB,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,QAAQ,eAAe;AAC7H,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,IAAIC,2BAA2B,IAAAC,4BAAA,GAAG,MAAMD,2BAA2B,CAAC;EAChE,IAAIE,QAAQA,CAACC,KAAK,EAAE;IAChB,MAAMC,YAAY,GAAG,IAAI,CAACC,SAAS,KAAKF,KAAK;IAC7C,IAAI,CAACE,SAAS,GAAGF,KAAK;IACtB,IAAIC,YAAY,IAAI,IAAI,CAACE,YAAY,IAAI,UAAU,IAAI,IAAI,CAACA,YAAY,EAAE;MACtE,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACL,QAAQ,GAAG,IAAI;IAC9C;EACJ;EACA,IAAIA,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACG,SAAS;EACzB;EACAG,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAG,IAAIlB,YAAY,CAAC,CAAC;IACrC,IAAI,CAACmB,kBAAkB,GAAG;MACtB,CAACd,gBAAgB,CAACe,SAAS,GAAGd,wBAAwB;MACtD,CAACD,gBAAgB,CAACgB,SAAS,GAAGd;IAClC,CAAC;IACD,IAAI,CAACM,SAAS,GAAG,KAAK;EAC1B;EACAS,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACV,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACW,OAAO,CAAC,CAAC;IAC/B;EACJ;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACC,WAAW,EAAE;MAClB,OAAO,IAAI,CAACR,kBAAkB,CAAC,IAAI,CAACQ,WAAW,CAACC,IAAI,CAAC;IACzD;EACJ;EACAL,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACM,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACC,KAAK,CAAC,CAAC;MACtB,MAAMC,OAAO,GAAG,IAAI,CAACd,QAAQ,CAACe,uBAAuB,CAAC,IAAI,CAACN,cAAc,CAAC;MAC1E,IAAI,CAACZ,YAAY,GAAG,IAAI,CAACe,SAAS,CAACN,eAAe,CAACQ,OAAO,CAAC;MAC3D,MAAME,IAAI,GAAG;QACTC,IAAI,EAAE,IAAI,CAACA,IAAI;QACfC,MAAM,EAAE,IAAI,CAACR,WAAW,IAAI,IAAI,CAACA,WAAW,CAACS,cAAc,CAAC,IAAI,CAACF,IAAI,CAAC;QACtEG,MAAM,EAAE,IAAI,CAACV,WAAW,IAAI,IAAI,CAACA,WAAW,CAACU,MAAM;QACnDnB,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BR,QAAQ,EAAE,IAAI,CAACA;MACnB,CAAC;MACD,IAAI,CAACI,YAAY,CAACC,QAAQ,CAACuB,YAAY,CAACL,IAAI,CAAC;IACjD;EACJ;AAWJ,CAAC,EAVYxB,4BAAA,CAAK8B,cAAc,GAAG,MAAM,CACjC;EAAEX,IAAI,EAAE7B;AAAyB,CAAC,CACrC,EACQU,4BAAA,CAAK+B,cAAc,GAAG;EAC3Bb,WAAW,EAAE,CAAC;IAAEC,IAAI,EAAE3B;EAAM,CAAC,CAAC;EAC9BiC,IAAI,EAAE,CAAC;IAAEN,IAAI,EAAE3B;EAAM,CAAC,CAAC;EACvBS,QAAQ,EAAE,CAAC;IAAEkB,IAAI,EAAE3B;EAAM,CAAC,CAAC;EAC3BiB,WAAW,EAAE,CAAC;IAAEU,IAAI,EAAE1B;EAAO,CAAC,CAAC;EAC/B2B,SAAS,EAAE,CAAC;IAAED,IAAI,EAAEzB,SAAS;IAAEsC,IAAI,EAAE,CAAC,QAAQ,EAAE;MAAEC,IAAI,EAAEtC,gBAAgB;MAAEuC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC;AAChG,CAAC,EAAAlC,4BAAA,CACJ;AACDD,2BAA2B,GAAGX,UAAU,CAAC,CACrCC,SAAS,CAAC;EACN;EACA8C,QAAQ,EAAE,wBAAwB;EAClCC,QAAQ,EAAE,kDAAkD;EAC5DC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEtC,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}