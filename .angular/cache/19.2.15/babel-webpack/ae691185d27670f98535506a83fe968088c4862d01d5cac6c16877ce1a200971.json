{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuSelectComponent } from './swui-menu-select.component';\nimport { MENU_SELECT_MODULES } from './swui-menu-select.module';\ndescribe('SwuiMenuSelectComponent', () => {\n  let component;\n  let fixture;\n  let testOptions = [];\n  let testValue;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [CommonModule, BrowserAnimationsModule, TranslateModule.forRoot(), ...MENU_SELECT_MODULES],\n      declarations: [SwuiMenuSelectComponent]\n    });\n  });\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiMenuSelectComponent);\n    component = fixture.componentInstance;\n    testOptions = [{\n      id: '1',\n      text: 'Solo Option1'\n    }, {\n      id: '2',\n      text: 'Test Option2'\n    }, {\n      id: '3',\n      text: 'Option3',\n      disabled: true\n    }];\n    testValue = ['1', '2'];\n    component.data = testOptions;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should set searchPlaceholder', () => {\n    component.searchPlaceholder = 'test';\n    expect(component.searchPlaceholder).toBe('test');\n  });\n  it('should set title', () => {\n    component.title = 'test';\n    expect(component.title).toBe('test');\n  });\n  it('should set showSearch', () => {\n    component.showSearch = true;\n    expect(component.showSearch).toBe(true);\n  });\n  it('should emit cancel click', () => {\n    spyOn(component.cancelClick, 'emit');\n    component.cancel(createFakeEvent('click'));\n    fixture.detectChanges();\n    expect(component.cancelClick.emit).toHaveBeenCalled();\n  });\n  it('should emit applied data', () => {\n    spyOn(component.applyData, 'emit');\n    component.data = testOptions;\n    component.selected = testValue;\n    component.apply(createFakeEvent('click'));\n    fixture.detectChanges();\n    expect(component.applyData.emit).toHaveBeenCalled();\n  });\n  it('should set processedData', () => {\n    component.data = testOptions;\n    component.selected = ['1'];\n    const testResult = [{\n      id: '1',\n      text: 'Solo Option1',\n      data: {\n        checked: true\n      }\n    }, {\n      id: '2',\n      text: 'Test Option2',\n      data: {\n        checked: false\n      }\n    }, {\n      id: '3',\n      text: 'Option3',\n      disabled: true,\n      data: {\n        checked: false\n      }\n    }];\n    expect(component.processedData).toEqual(testResult);\n  });\n  it('should clear', () => {\n    component.data = testOptions;\n    component.selected = ['1'];\n    component.clear(createFakeEvent('click'));\n    const testResult = [{\n      id: '1',\n      text: 'Solo Option1',\n      data: {\n        checked: false\n      }\n    }, {\n      id: '2',\n      text: 'Test Option2',\n      data: {\n        checked: false\n      }\n    }, {\n      id: '3',\n      text: 'Option3',\n      disabled: true,\n      data: {\n        checked: false\n      }\n    }];\n    expect(component.processedData).toEqual(testResult);\n  });\n  it('should filter', () => {\n    component.showSearch = true;\n    component.ngOnInit();\n    component.searchControl.setValue('1');\n    expect(component.isFiltered('1')).toBe(true);\n  });\n  it('should get selected length', () => {\n    component.data = testOptions;\n    component.selected = ['1'];\n    expect(component.selectedLength).toBe(1);\n  });\n});\nfunction createFakeEvent(type) {\n  const event = document.createEvent('Event');\n  event.initEvent(type, true, true);\n  return event;\n}", "map": {"version": 3, "names": ["TestBed", "BrowserAnimationsModule", "CommonModule", "TranslateModule", "SwuiMenuSelectComponent", "MENU_SELECT_MODULES", "describe", "component", "fixture", "testOptions", "testValue", "beforeEach", "configureTestingModule", "imports", "forRoot", "declarations", "createComponent", "componentInstance", "id", "text", "disabled", "data", "detectChanges", "it", "expect", "toBeTruthy", "searchPlaceholder", "toBe", "title", "showSearch", "spyOn", "cancelClick", "cancel", "createFakeEvent", "emit", "toHaveBeenCalled", "applyData", "selected", "apply", "testResult", "checked", "processedData", "toEqual", "clear", "ngOnInit", "searchControl", "setValue", "isFiltered", "<PERSON><PERSON><PERSON><PERSON>", "type", "event", "document", "createEvent", "initEvent"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-menu-select/swui-menu-select.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { SwuiMenuSelectComponent } from './swui-menu-select.component';\nimport { SwuiSelectOption } from '../swui-select/swui-select.interface';\nimport { MENU_SELECT_MODULES } from './swui-menu-select.module';\n\n\ndescribe('SwuiMenuSelectComponent', () => {\n  let component: SwuiMenuSelectComponent;\n  let fixture: ComponentFixture<SwuiMenuSelectComponent>;\n  let testOptions: SwuiSelectOption[] = [];\n  let testValue: string[];\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [\n        CommonModule,\n        BrowserAnimationsModule,\n        TranslateModule.forRoot(),\n        ...MENU_SELECT_MODULES\n      ],\n      declarations: [SwuiMenuSelectComponent]\n    });\n  });\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiMenuSelectComponent);\n    component = fixture.componentInstance;\n    testOptions = [\n      {id: '1', text: 'Solo Option1'},\n      {id: '2', text: 'Test Option2'},\n      {id: '3', text: 'Option3', disabled: true}\n    ];\n    testValue = ['1', '2'];\n    component.data = testOptions;\n\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should set searchPlaceholder', () => {\n    component.searchPlaceholder = 'test';\n    expect(component.searchPlaceholder).toBe('test');\n  });\n\n  it('should set title', () => {\n    component.title = 'test';\n    expect(component.title).toBe('test');\n  });\n\n  it('should set showSearch', () => {\n    component.showSearch = true;\n    expect(component.showSearch).toBe(true);\n  });\n\n  it('should emit cancel click', () => {\n    spyOn(component.cancelClick, 'emit');\n    component.cancel(createFakeEvent('click'));\n\n    fixture.detectChanges();\n    expect(component.cancelClick.emit).toHaveBeenCalled();\n  });\n\n  it('should emit applied data', () => {\n    spyOn(component.applyData, 'emit');\n    component.data = testOptions;\n    component.selected = testValue;\n    component.apply(createFakeEvent('click'));\n\n    fixture.detectChanges();\n    expect(component.applyData.emit).toHaveBeenCalled();\n  });\n\n  it('should set processedData', () => {\n    component.data = testOptions;\n    component.selected = ['1'];\n    const testResult = [\n      {id: '1', text: 'Solo Option1', data: { checked: true }},\n      {id: '2', text: 'Test Option2', data: { checked: false }},\n      {id: '3', text: 'Option3', disabled: true, data: { checked: false }}\n    ];\n    expect(component.processedData).toEqual(testResult);\n  });\n\n  it('should clear', () => {\n    component.data = testOptions;\n    component.selected = ['1'];\n    component.clear(createFakeEvent('click'));\n\n    const testResult = [\n      {id: '1', text: 'Solo Option1', data: { checked: false }},\n      {id: '2', text: 'Test Option2', data: { checked: false }},\n      {id: '3', text: 'Option3', disabled: true, data: { checked: false }}\n    ];\n\n    expect(component.processedData).toEqual(testResult);\n  });\n\n  it('should filter', () => {\n    component.showSearch = true;\n    component.ngOnInit();\n    component.searchControl.setValue('1');\n    expect(component.isFiltered('1')).toBe(true);\n  });\n\n  it('should get selected length', () => {\n    component.data = testOptions;\n    component.selected = ['1'];\n\n    expect(component.selectedLength).toBe(1);\n  });\n\n});\n\n\nfunction createFakeEvent(type: string) {\n  const event = document.createEvent('Event');\n  event.initEvent(type, true, true);\n  return event;\n}\n"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,uBAAuB,QAAQ,8BAA8B;AAEtE,SAASC,mBAAmB,QAAQ,2BAA2B;AAG/DC,QAAQ,CAAC,yBAAyB,EAAE,MAAK;EACvC,IAAIC,SAAkC;EACtC,IAAIC,OAAkD;EACtD,IAAIC,WAAW,GAAuB,EAAE;EACxC,IAAIC,SAAmB;EAEvBC,UAAU,CAAC,MAAK;IACdX,OAAO,CAACY,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPX,YAAY,EACZD,uBAAuB,EACvBE,eAAe,CAACW,OAAO,EAAE,EACzB,GAAGT,mBAAmB,CACvB;MACDU,YAAY,EAAE,CAACX,uBAAuB;KACvC,CAAC;EACJ,CAAC,CAAC;EAEFO,UAAU,CAAC,MAAK;IACdH,OAAO,GAAGR,OAAO,CAACgB,eAAe,CAACZ,uBAAuB,CAAC;IAC1DG,SAAS,GAAGC,OAAO,CAACS,iBAAiB;IACrCR,WAAW,GAAG,CACZ;MAACS,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAc,CAAC,EAC/B;MAACD,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAc,CAAC,EAC/B;MAACD,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAI,CAAC,CAC3C;IACDV,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IACtBH,SAAS,CAACc,IAAI,GAAGZ,WAAW;IAE5BD,OAAO,CAACc,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACjB,SAAS,CAAC,CAACkB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtChB,SAAS,CAACmB,iBAAiB,GAAG,MAAM;IACpCF,MAAM,CAACjB,SAAS,CAACmB,iBAAiB,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;EAClD,CAAC,CAAC;EAEFJ,EAAE,CAAC,kBAAkB,EAAE,MAAK;IAC1BhB,SAAS,CAACqB,KAAK,GAAG,MAAM;IACxBJ,MAAM,CAACjB,SAAS,CAACqB,KAAK,CAAC,CAACD,IAAI,CAAC,MAAM,CAAC;EACtC,CAAC,CAAC;EAEFJ,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/BhB,SAAS,CAACsB,UAAU,GAAG,IAAI;IAC3BL,MAAM,CAACjB,SAAS,CAACsB,UAAU,CAAC,CAACF,IAAI,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;EAEFJ,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClCO,KAAK,CAACvB,SAAS,CAACwB,WAAW,EAAE,MAAM,CAAC;IACpCxB,SAAS,CAACyB,MAAM,CAACC,eAAe,CAAC,OAAO,CAAC,CAAC;IAE1CzB,OAAO,CAACc,aAAa,EAAE;IACvBE,MAAM,CAACjB,SAAS,CAACwB,WAAW,CAACG,IAAI,CAAC,CAACC,gBAAgB,EAAE;EACvD,CAAC,CAAC;EAEFZ,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClCO,KAAK,CAACvB,SAAS,CAAC6B,SAAS,EAAE,MAAM,CAAC;IAClC7B,SAAS,CAACc,IAAI,GAAGZ,WAAW;IAC5BF,SAAS,CAAC8B,QAAQ,GAAG3B,SAAS;IAC9BH,SAAS,CAAC+B,KAAK,CAACL,eAAe,CAAC,OAAO,CAAC,CAAC;IAEzCzB,OAAO,CAACc,aAAa,EAAE;IACvBE,MAAM,CAACjB,SAAS,CAAC6B,SAAS,CAACF,IAAI,CAAC,CAACC,gBAAgB,EAAE;EACrD,CAAC,CAAC;EAEFZ,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClChB,SAAS,CAACc,IAAI,GAAGZ,WAAW;IAC5BF,SAAS,CAAC8B,QAAQ,GAAG,CAAC,GAAG,CAAC;IAC1B,MAAME,UAAU,GAAG,CACjB;MAACrB,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,cAAc;MAAEE,IAAI,EAAE;QAAEmB,OAAO,EAAE;MAAI;IAAE,CAAC,EACxD;MAACtB,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,cAAc;MAAEE,IAAI,EAAE;QAAEmB,OAAO,EAAE;MAAK;IAAE,CAAC,EACzD;MAACtB,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,SAAS;MAAEC,QAAQ,EAAE,IAAI;MAAEC,IAAI,EAAE;QAAEmB,OAAO,EAAE;MAAK;IAAE,CAAC,CACrE;IACDhB,MAAM,CAACjB,SAAS,CAACkC,aAAa,CAAC,CAACC,OAAO,CAACH,UAAU,CAAC;EACrD,CAAC,CAAC;EAEFhB,EAAE,CAAC,cAAc,EAAE,MAAK;IACtBhB,SAAS,CAACc,IAAI,GAAGZ,WAAW;IAC5BF,SAAS,CAAC8B,QAAQ,GAAG,CAAC,GAAG,CAAC;IAC1B9B,SAAS,CAACoC,KAAK,CAACV,eAAe,CAAC,OAAO,CAAC,CAAC;IAEzC,MAAMM,UAAU,GAAG,CACjB;MAACrB,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,cAAc;MAAEE,IAAI,EAAE;QAAEmB,OAAO,EAAE;MAAK;IAAE,CAAC,EACzD;MAACtB,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,cAAc;MAAEE,IAAI,EAAE;QAAEmB,OAAO,EAAE;MAAK;IAAE,CAAC,EACzD;MAACtB,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,SAAS;MAAEC,QAAQ,EAAE,IAAI;MAAEC,IAAI,EAAE;QAAEmB,OAAO,EAAE;MAAK;IAAE,CAAC,CACrE;IAEDhB,MAAM,CAACjB,SAAS,CAACkC,aAAa,CAAC,CAACC,OAAO,CAACH,UAAU,CAAC;EACrD,CAAC,CAAC;EAEFhB,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBhB,SAAS,CAACsB,UAAU,GAAG,IAAI;IAC3BtB,SAAS,CAACqC,QAAQ,EAAE;IACpBrC,SAAS,CAACsC,aAAa,CAACC,QAAQ,CAAC,GAAG,CAAC;IACrCtB,MAAM,CAACjB,SAAS,CAACwC,UAAU,CAAC,GAAG,CAAC,CAAC,CAACpB,IAAI,CAAC,IAAI,CAAC;EAC9C,CAAC,CAAC;EAEFJ,EAAE,CAAC,4BAA4B,EAAE,MAAK;IACpChB,SAAS,CAACc,IAAI,GAAGZ,WAAW;IAC5BF,SAAS,CAAC8B,QAAQ,GAAG,CAAC,GAAG,CAAC;IAE1Bb,MAAM,CAACjB,SAAS,CAACyC,cAAc,CAAC,CAACrB,IAAI,CAAC,CAAC,CAAC;EAC1C,CAAC,CAAC;AAEJ,CAAC,CAAC;AAGF,SAASM,eAAeA,CAACgB,IAAY;EACnC,MAAMC,KAAK,GAAGC,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC;EAC3CF,KAAK,CAACG,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjC,OAAOC,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}