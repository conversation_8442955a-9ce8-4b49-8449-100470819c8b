{"ast": null, "code": "var _HubSelectorComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./hub-selector.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./hub-selector.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, Inject } from '@angular/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nlet HubSelectorComponent = (_HubSelectorComponent = class HubSelectorComponent {\n  constructor(settings, config, auth) {\n    this.config = config;\n    this.auth = auth;\n    this.hubs = Object.entries(this.config.hubs || {}).filter(([, {\n      permission\n    }]) => {\n      const permissions = Array.isArray(permission) ? permission : permission ? [permission] : [];\n      return permission ? this.auth.allowedTo(permissions) : true;\n    }).reduce((result, [id, {\n      cssClass,\n      name,\n      url\n    }]) => {\n      if (name && url) {\n        return [...result, {\n          id,\n          name,\n          url,\n          cssClass\n        }];\n      }\n      return result;\n    }, []);\n    this.activeHub = this.hubs.find(({\n      id\n    }) => id === settings.name);\n  }\n}, _HubSelectorComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_HUB_MESSAGE_CONFIG]\n  }]\n}, {\n  type: SwHubConfigService\n}, {\n  type: SwHubAuthService\n}], _HubSelectorComponent);\nHubSelectorComponent = __decorate([Component({\n  selector: 'lib-swui-hub-selector',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], HubSelectorComponent);\nexport { HubSelectorComponent };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "Component", "Inject", "SWUI_HUB_MESSAGE_CONFIG", "SwHubConfigService", "SwHubAuthService", "HubSelectorComponent", "_HubSelectorComponent", "constructor", "settings", "config", "auth", "hubs", "Object", "entries", "filter", "permission", "permissions", "Array", "isArray", "allowedTo", "reduce", "result", "id", "cssClass", "name", "url", "activeHub", "find", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "changeDetection", "OnPush", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/hub-selector/hub-selector.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { HubMenuItem } from '../swui-top-menu.model';\nimport { SwHubMessageModuleConfig } from '../../services/sw-hub-init/sw-hub-init.model';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\n\n@Component({\n    selector: 'lib-swui-hub-selector',\n    templateUrl: './hub-selector.component.html',\n    styleUrls: ['./hub-selector.component.scss'],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    standalone: false\n})\nexport class HubSelectorComponent {\n  readonly hubs: HubMenuItem[];\n  readonly activeHub: HubMenuItem | undefined;\n\n  constructor( @Inject(SWUI_HUB_MESSAGE_CONFIG) settings: SwHubMessageModuleConfig,\n               private readonly config: SwHubConfigService,\n               private readonly auth: SwHubAuthService ) {\n    this.hubs = Object.entries(this.config.hubs || {})\n      .filter(( [, { permission }] ) => {\n        const permissions = Array.isArray(permission) ? permission : permission ? [permission] : [];\n        return permission ? this.auth.allowedTo(permissions) : true;\n      })\n      .reduce<HubMenuItem[]>(( result, [id, { cssClass, name, url }] ) => {\n        if (name && url) {\n          return [\n            ...result,\n            { id, name, url, cssClass }\n          ];\n        }\n        return result;\n      }, []);\n    this.activeHub = this.hubs.find(( { id } ) => id === settings.name);\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,uBAAuB,EAAEC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AAC1E,SAASC,uBAAuB,QAAQ,8CAA8C;AAGtF,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,gBAAgB,QAAQ,gDAAgD;AAS1E,IAAMC,oBAAoB,IAAAC,qBAAA,GAA1B,MAAMD,oBAAoB;EAI/BE,YAA8CC,QAAkC,EAClDC,MAA0B,EAC1BC,IAAsB;IADtB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IAChC,IAAI,CAACC,IAAI,GAAGC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACJ,MAAM,CAACE,IAAI,IAAI,EAAE,CAAC,CAC/CG,MAAM,CAAC,CAAE,GAAG;MAAEC;IAAU,CAAE,CAAC,KAAK;MAC/B,MAAMC,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,GAAGA,UAAU,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC,GAAG,EAAE;MAC3F,OAAOA,UAAU,GAAG,IAAI,CAACL,IAAI,CAACS,SAAS,CAACH,WAAW,CAAC,GAAG,IAAI;IAC7D,CAAC,CAAC,CACDI,MAAM,CAAgB,CAAEC,MAAM,EAAE,CAACC,EAAE,EAAE;MAAEC,QAAQ;MAAEC,IAAI;MAAEC;IAAG,CAAE,CAAC,KAAK;MACjE,IAAID,IAAI,IAAIC,GAAG,EAAE;QACf,OAAO,CACL,GAAGJ,MAAM,EACT;UAAEC,EAAE;UAAEE,IAAI;UAAEC,GAAG;UAAEF;QAAQ,CAAE,CAC5B;MACH;MACA,OAAOF,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;IACR,IAAI,CAACK,SAAS,GAAG,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAC,CAAE;MAAEL;IAAE,CAAE,KAAMA,EAAE,KAAKd,QAAQ,CAACgB,IAAI,CAAC;EACrE;;;;UAlBcvB,MAAM;IAAA2B,IAAA,GAAC1B,uBAAuB;EAAA;AAAA,G;;;;;AAJjCG,oBAAoB,GAAAwB,UAAA,EAPhC7B,SAAS,CAAC;EACP8B,QAAQ,EAAE,uBAAuB;EACjCC,QAAA,EAAAC,oBAA4C;EAE5CC,eAAe,EAAElC,uBAAuB,CAACmC,MAAM;EAC/CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACW9B,oBAAoB,CAuBhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}