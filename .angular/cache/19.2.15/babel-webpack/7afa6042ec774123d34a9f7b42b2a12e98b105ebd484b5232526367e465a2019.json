{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatRippleLoader;\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ANIMATION_MODULE_TYPE, NgZone, Injector, RendererFactory2, Injectable } from '@angular/core';\nimport { Platform, _bindEventWithOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS, R as RippleRenderer, d as defaultRippleAnimationConfig } from './ripple-BT3tzh6F.mjs';\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {\n  capture: true\n};\n/**\n * The events that should trigger the initialization of the ripple.\n * Note that we use `mousedown`, rather than `click`, for mouse devices because\n * we can't rely on `mouseenter` in the shadow DOM and `click` happens too late.\n */\nconst rippleInteractionEvents = ['focus', 'mousedown', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n  constructor() {\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_animationMode\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }));\n    _defineProperty(this, \"_globalRippleOptions\", inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    }));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_eventCleanups\", void 0);\n    _defineProperty(this, \"_hosts\", new Map());\n    /**\n     * Handles creating and attaching component internals\n     * when a component is initially interacted with.\n     */\n    _defineProperty(this, \"_onInteraction\", event => {\n      const eventTarget = _getEventTarget(event);\n      if (eventTarget instanceof HTMLElement) {\n        var _this$_globalRippleOp, _this$_globalRippleOp2;\n        // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n        const element = eventTarget.closest(`[${matRippleUninitialized}=\"${(_this$_globalRippleOp = (_this$_globalRippleOp2 = this._globalRippleOptions) === null || _this$_globalRippleOp2 === void 0 ? void 0 : _this$_globalRippleOp2.namespace) !== null && _this$_globalRippleOp !== void 0 ? _this$_globalRippleOp : ''}\"]`);\n        if (element) {\n          this._createRipple(element);\n        }\n      }\n    });\n    const renderer = inject(RendererFactory2).createRenderer(null, null);\n    this._eventCleanups = this._ngZone.runOutsideAngular(() => {\n      return rippleInteractionEvents.map(name => _bindEventWithOptions(renderer, this._document, name, this._onInteraction, eventListenerOptions));\n    });\n  }\n  ngOnDestroy() {\n    const hosts = this._hosts.keys();\n    for (const host of hosts) {\n      this.destroyRipple(host);\n    }\n    this._eventCleanups.forEach(cleanup => cleanup());\n  }\n  /**\n   * Configures the ripple that will be rendered by the ripple loader.\n   *\n   * Stores the given information about how the ripple should be configured on the host\n   * element so that it can later be retrived & used when the ripple is actually created.\n   */\n  configureRipple(host, config) {\n    var _this$_globalRippleOp3, _this$_globalRippleOp4;\n    // Indicates that the ripple has not yet been rendered for this component.\n    host.setAttribute(matRippleUninitialized, (_this$_globalRippleOp3 = (_this$_globalRippleOp4 = this._globalRippleOptions) === null || _this$_globalRippleOp4 === void 0 ? void 0 : _this$_globalRippleOp4.namespace) !== null && _this$_globalRippleOp3 !== void 0 ? _this$_globalRippleOp3 : '');\n    // Store the additional class name(s) that should be added to the ripple element.\n    if (config.className || !host.hasAttribute(matRippleClassName)) {\n      host.setAttribute(matRippleClassName, config.className || '');\n    }\n    // Store whether the ripple should be centered.\n    if (config.centered) {\n      host.setAttribute(matRippleCentered, '');\n    }\n    if (config.disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    }\n  }\n  /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n  setDisabled(host, disabled) {\n    const ripple = this._hosts.get(host);\n    // If the ripple has already been instantiated, just disable it.\n    if (ripple) {\n      ripple.target.rippleDisabled = disabled;\n      if (!disabled && !ripple.hasSetUpEvents) {\n        ripple.hasSetUpEvents = true;\n        ripple.renderer.setupTriggerEvents(host);\n      }\n    } else if (disabled) {\n      // Otherwise, set an attribute so we know what the\n      // disabled state should be when the ripple is initialized.\n      host.setAttribute(matRippleDisabled, '');\n    } else {\n      host.removeAttribute(matRippleDisabled);\n    }\n  }\n  /** Creates a MatRipple and appends it to the given element. */\n  _createRipple(host) {\n    var _host$querySelector, _globalOptions$animat, _globalOptions$animat2, _globalOptions$animat3, _globalOptions$animat4;\n    if (!this._document || this._hosts.has(host)) {\n      return;\n    }\n    // Create the ripple element.\n    (_host$querySelector = host.querySelector('.mat-ripple')) === null || _host$querySelector === void 0 || _host$querySelector.remove();\n    const rippleEl = this._document.createElement('span');\n    rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n    host.append(rippleEl);\n    const isNoopAnimations = this._animationMode === 'NoopAnimations';\n    const globalOptions = this._globalRippleOptions;\n    const enterDuration = isNoopAnimations ? 0 : (_globalOptions$animat = globalOptions === null || globalOptions === void 0 || (_globalOptions$animat2 = globalOptions.animation) === null || _globalOptions$animat2 === void 0 ? void 0 : _globalOptions$animat2.enterDuration) !== null && _globalOptions$animat !== void 0 ? _globalOptions$animat : defaultRippleAnimationConfig.enterDuration;\n    const exitDuration = isNoopAnimations ? 0 : (_globalOptions$animat3 = globalOptions === null || globalOptions === void 0 || (_globalOptions$animat4 = globalOptions.animation) === null || _globalOptions$animat4 === void 0 ? void 0 : _globalOptions$animat4.exitDuration) !== null && _globalOptions$animat3 !== void 0 ? _globalOptions$animat3 : defaultRippleAnimationConfig.exitDuration;\n    const target = {\n      rippleDisabled: isNoopAnimations || (globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.disabled) || host.hasAttribute(matRippleDisabled),\n      rippleConfig: {\n        centered: host.hasAttribute(matRippleCentered),\n        terminateOnPointerUp: globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.terminateOnPointerUp,\n        animation: {\n          enterDuration,\n          exitDuration\n        }\n      }\n    };\n    const renderer = new RippleRenderer(target, this._ngZone, rippleEl, this._platform, this._injector);\n    const hasSetUpEvents = !target.rippleDisabled;\n    if (hasSetUpEvents) {\n      renderer.setupTriggerEvents(host);\n    }\n    this._hosts.set(host, {\n      target,\n      renderer,\n      hasSetUpEvents\n    });\n    host.removeAttribute(matRippleUninitialized);\n  }\n  destroyRipple(host) {\n    const ripple = this._hosts.get(host);\n    if (ripple) {\n      ripple.renderer._removeTriggerEvents();\n      this._hosts.delete(host);\n    }\n  }\n}\n_MatRippleLoader = MatRippleLoader;\n_defineProperty(MatRippleLoader, \"\\u0275fac\", function _MatRippleLoader_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatRippleLoader)();\n});\n_defineProperty(MatRippleLoader, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _MatRippleLoader,\n  factory: _MatRippleLoader.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { MatRippleLoader as M };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "ANIMATION_MODULE_TYPE", "NgZone", "Injector", "RendererFactory2", "Injectable", "Platform", "_bindEventWithOptions", "_getEventTarget", "a", "MAT_RIPPLE_GLOBAL_OPTIONS", "R", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "d", "defaultRippleAnimationConfig", "eventListenerOptions", "capture", "rippleInteractionEvents", "matRippleUninitialized", "matRippleClassName", "mat<PERSON><PERSON>pleCentered", "matRippleDisabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_defineProperty", "optional", "Map", "event", "eventTarget", "HTMLElement", "_this$_globalRippleOp", "_this$_globalRippleOp2", "element", "closest", "_globalRippleOptions", "namespace", "_createRipple", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "_eventCleanups", "_ngZone", "runOutsideAngular", "map", "name", "_document", "_onInteraction", "ngOnDestroy", "hosts", "_hosts", "keys", "host", "destroyRipple", "for<PERSON>ach", "cleanup", "configureRipple", "config", "_this$_globalRippleOp3", "_this$_globalRippleOp4", "setAttribute", "className", "hasAttribute", "centered", "disabled", "setDisabled", "ripple", "get", "target", "rippleDisabled", "hasSetUpEvents", "setupTriggerEvents", "removeAttribute", "_host$querySelector", "_globalOptions$animat", "_globalOptions$animat2", "_globalOptions$animat3", "_globalOptions$animat4", "has", "querySelector", "remove", "rippleEl", "createElement", "classList", "add", "getAttribute", "append", "isNoopAnimations", "_animationMode", "globalOptions", "enterDuration", "animation", "exitDuration", "rippleConfig", "terminateOnPointerUp", "_platform", "_injector", "set", "_removeTriggerEvents", "delete", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_MatRippleLoader_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "M"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/ripple-loader-Ce3DAhPW.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ANIMATION_MODULE_TYPE, NgZone, Injector, RendererFactory2, Injectable } from '@angular/core';\nimport { Platform, _bindEventWithOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS, R as RippleRenderer, d as defaultRippleAnimationConfig } from './ripple-BT3tzh6F.mjs';\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = { capture: true };\n/**\n * The events that should trigger the initialization of the ripple.\n * Note that we use `mousedown`, rather than `click`, for mouse devices because\n * we can't rely on `mouseenter` in the shadow DOM and `click` happens too late.\n */\nconst rippleInteractionEvents = ['focus', 'mousedown', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n    _document = inject(DOCUMENT);\n    _animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n    _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, { optional: true });\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _injector = inject(Injector);\n    _eventCleanups;\n    _hosts = new Map();\n    constructor() {\n        const renderer = inject(RendererFactory2).createRenderer(null, null);\n        this._eventCleanups = this._ngZone.runOutsideAngular(() => {\n            return rippleInteractionEvents.map(name => _bindEventWithOptions(renderer, this._document, name, this._onInteraction, eventListenerOptions));\n        });\n    }\n    ngOnDestroy() {\n        const hosts = this._hosts.keys();\n        for (const host of hosts) {\n            this.destroyRipple(host);\n        }\n        this._eventCleanups.forEach(cleanup => cleanup());\n    }\n    /**\n     * Configures the ripple that will be rendered by the ripple loader.\n     *\n     * Stores the given information about how the ripple should be configured on the host\n     * element so that it can later be retrived & used when the ripple is actually created.\n     */\n    configureRipple(host, config) {\n        // Indicates that the ripple has not yet been rendered for this component.\n        host.setAttribute(matRippleUninitialized, this._globalRippleOptions?.namespace ?? '');\n        // Store the additional class name(s) that should be added to the ripple element.\n        if (config.className || !host.hasAttribute(matRippleClassName)) {\n            host.setAttribute(matRippleClassName, config.className || '');\n        }\n        // Store whether the ripple should be centered.\n        if (config.centered) {\n            host.setAttribute(matRippleCentered, '');\n        }\n        if (config.disabled) {\n            host.setAttribute(matRippleDisabled, '');\n        }\n    }\n    /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n    setDisabled(host, disabled) {\n        const ripple = this._hosts.get(host);\n        // If the ripple has already been instantiated, just disable it.\n        if (ripple) {\n            ripple.target.rippleDisabled = disabled;\n            if (!disabled && !ripple.hasSetUpEvents) {\n                ripple.hasSetUpEvents = true;\n                ripple.renderer.setupTriggerEvents(host);\n            }\n        }\n        else if (disabled) {\n            // Otherwise, set an attribute so we know what the\n            // disabled state should be when the ripple is initialized.\n            host.setAttribute(matRippleDisabled, '');\n        }\n        else {\n            host.removeAttribute(matRippleDisabled);\n        }\n    }\n    /**\n     * Handles creating and attaching component internals\n     * when a component is initially interacted with.\n     */\n    _onInteraction = (event) => {\n        const eventTarget = _getEventTarget(event);\n        if (eventTarget instanceof HTMLElement) {\n            // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n            const element = eventTarget.closest(`[${matRippleUninitialized}=\"${this._globalRippleOptions?.namespace ?? ''}\"]`);\n            if (element) {\n                this._createRipple(element);\n            }\n        }\n    };\n    /** Creates a MatRipple and appends it to the given element. */\n    _createRipple(host) {\n        if (!this._document || this._hosts.has(host)) {\n            return;\n        }\n        // Create the ripple element.\n        host.querySelector('.mat-ripple')?.remove();\n        const rippleEl = this._document.createElement('span');\n        rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n        host.append(rippleEl);\n        const isNoopAnimations = this._animationMode === 'NoopAnimations';\n        const globalOptions = this._globalRippleOptions;\n        const enterDuration = isNoopAnimations\n            ? 0\n            : globalOptions?.animation?.enterDuration ?? defaultRippleAnimationConfig.enterDuration;\n        const exitDuration = isNoopAnimations\n            ? 0\n            : globalOptions?.animation?.exitDuration ?? defaultRippleAnimationConfig.exitDuration;\n        const target = {\n            rippleDisabled: isNoopAnimations || globalOptions?.disabled || host.hasAttribute(matRippleDisabled),\n            rippleConfig: {\n                centered: host.hasAttribute(matRippleCentered),\n                terminateOnPointerUp: globalOptions?.terminateOnPointerUp,\n                animation: {\n                    enterDuration,\n                    exitDuration,\n                },\n            },\n        };\n        const renderer = new RippleRenderer(target, this._ngZone, rippleEl, this._platform, this._injector);\n        const hasSetUpEvents = !target.rippleDisabled;\n        if (hasSetUpEvents) {\n            renderer.setupTriggerEvents(host);\n        }\n        this._hosts.set(host, {\n            target,\n            renderer,\n            hasSetUpEvents,\n        });\n        host.removeAttribute(matRippleUninitialized);\n    }\n    destroyRipple(host) {\n        const ripple = this._hosts.get(host);\n        if (ripple) {\n            ripple.renderer._removeTriggerEvents();\n            this._hosts.delete(host);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRippleLoader, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRippleLoader, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRippleLoader, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nexport { MatRippleLoader as M };\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AAC7G,SAASC,QAAQ,EAAEC,qBAAqB,EAAEC,eAAe,QAAQ,uBAAuB;AACxF,SAASC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,4BAA4B,QAAQ,uBAAuB;;AAE9H;AACA,MAAMC,oBAAoB,GAAG;EAAEC,OAAO,EAAE;AAAK,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC;AAClF;AACA,MAAMC,sBAAsB,GAAG,iCAAiC;AAChE;AACA,MAAMC,kBAAkB,GAAG,8BAA8B;AACzD;AACA,MAAMC,iBAAiB,GAAG,4BAA4B;AACtD;AACA,MAAMC,iBAAiB,GAAG,4BAA4B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EASlBC,WAAWA,CAAA,EAAG;IAAAC,eAAA,oBARFxB,MAAM,CAACF,QAAQ,CAAC;IAAA0B,eAAA,yBACXxB,MAAM,CAACC,qBAAqB,EAAE;MAAEwB,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAD,eAAA,+BAC3CxB,MAAM,CAACU,yBAAyB,EAAE;MAAEe,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAD,eAAA,oBAChExB,MAAM,CAACM,QAAQ,CAAC;IAAAkB,eAAA,kBAClBxB,MAAM,CAACE,MAAM,CAAC;IAAAsB,eAAA,oBACZxB,MAAM,CAACG,QAAQ,CAAC;IAAAqB,eAAA;IAAAA,eAAA,iBAEnB,IAAIE,GAAG,CAAC,CAAC;IAuDlB;AACJ;AACA;AACA;IAHIF,eAAA,yBAIkBG,KAAK,IAAK;MACxB,MAAMC,WAAW,GAAGpB,eAAe,CAACmB,KAAK,CAAC;MAC1C,IAAIC,WAAW,YAAYC,WAAW,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QACpC;QACA,MAAMC,OAAO,GAAGJ,WAAW,CAACK,OAAO,CAAC,IAAIf,sBAAsB,MAAAY,qBAAA,IAAAC,sBAAA,GAAK,IAAI,CAACG,oBAAoB,cAAAH,sBAAA,uBAAzBA,sBAAA,CAA2BI,SAAS,cAAAL,qBAAA,cAAAA,qBAAA,GAAI,EAAE,IAAI,CAAC;QAClH,IAAIE,OAAO,EAAE;UACT,IAAI,CAACI,aAAa,CAACJ,OAAO,CAAC;QAC/B;MACJ;IACJ,CAAC;IAlEG,MAAMK,QAAQ,GAAGrC,MAAM,CAACI,gBAAgB,CAAC,CAACkC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IACpE,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,MAAM;MACvD,OAAOxB,uBAAuB,CAACyB,GAAG,CAACC,IAAI,IAAIpC,qBAAqB,CAAC8B,QAAQ,EAAE,IAAI,CAACO,SAAS,EAAED,IAAI,EAAE,IAAI,CAACE,cAAc,EAAE9B,oBAAoB,CAAC,CAAC;IAChJ,CAAC,CAAC;EACN;EACA+B,WAAWA,CAAA,EAAG;IACV,MAAMC,KAAK,GAAG,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC;IAChC,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;MACtB,IAAI,CAACI,aAAa,CAACD,IAAI,CAAC;IAC5B;IACA,IAAI,CAACX,cAAc,CAACa,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACJ,IAAI,EAAEK,MAAM,EAAE;IAAA,IAAAC,sBAAA,EAAAC,sBAAA;IAC1B;IACAP,IAAI,CAACQ,YAAY,CAACxC,sBAAsB,GAAAsC,sBAAA,IAAAC,sBAAA,GAAE,IAAI,CAACvB,oBAAoB,cAAAuB,sBAAA,uBAAzBA,sBAAA,CAA2BtB,SAAS,cAAAqB,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;IACrF;IACA,IAAID,MAAM,CAACI,SAAS,IAAI,CAACT,IAAI,CAACU,YAAY,CAACzC,kBAAkB,CAAC,EAAE;MAC5D+B,IAAI,CAACQ,YAAY,CAACvC,kBAAkB,EAAEoC,MAAM,CAACI,SAAS,IAAI,EAAE,CAAC;IACjE;IACA;IACA,IAAIJ,MAAM,CAACM,QAAQ,EAAE;MACjBX,IAAI,CAACQ,YAAY,CAACtC,iBAAiB,EAAE,EAAE,CAAC;IAC5C;IACA,IAAImC,MAAM,CAACO,QAAQ,EAAE;MACjBZ,IAAI,CAACQ,YAAY,CAACrC,iBAAiB,EAAE,EAAE,CAAC;IAC5C;EACJ;EACA;EACA0C,WAAWA,CAACb,IAAI,EAAEY,QAAQ,EAAE;IACxB,MAAME,MAAM,GAAG,IAAI,CAAChB,MAAM,CAACiB,GAAG,CAACf,IAAI,CAAC;IACpC;IACA,IAAIc,MAAM,EAAE;MACRA,MAAM,CAACE,MAAM,CAACC,cAAc,GAAGL,QAAQ;MACvC,IAAI,CAACA,QAAQ,IAAI,CAACE,MAAM,CAACI,cAAc,EAAE;QACrCJ,MAAM,CAACI,cAAc,GAAG,IAAI;QAC5BJ,MAAM,CAAC3B,QAAQ,CAACgC,kBAAkB,CAACnB,IAAI,CAAC;MAC5C;IACJ,CAAC,MACI,IAAIY,QAAQ,EAAE;MACf;MACA;MACAZ,IAAI,CAACQ,YAAY,CAACrC,iBAAiB,EAAE,EAAE,CAAC;IAC5C,CAAC,MACI;MACD6B,IAAI,CAACoB,eAAe,CAACjD,iBAAiB,CAAC;IAC3C;EACJ;EAeA;EACAe,aAAaA,CAACc,IAAI,EAAE;IAAA,IAAAqB,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAChB,IAAI,CAAC,IAAI,CAAC/B,SAAS,IAAI,IAAI,CAACI,MAAM,CAAC4B,GAAG,CAAC1B,IAAI,CAAC,EAAE;MAC1C;IACJ;IACA;IACA,CAAAqB,mBAAA,GAAArB,IAAI,CAAC2B,aAAa,CAAC,aAAa,CAAC,cAAAN,mBAAA,eAAjCA,mBAAA,CAAmCO,MAAM,CAAC,CAAC;IAC3C,MAAMC,QAAQ,GAAG,IAAI,CAACnC,SAAS,CAACoC,aAAa,CAAC,MAAM,CAAC;IACrDD,QAAQ,CAACE,SAAS,CAACC,GAAG,CAAC,YAAY,EAAEhC,IAAI,CAACiC,YAAY,CAAChE,kBAAkB,CAAC,CAAC;IAC3E+B,IAAI,CAACkC,MAAM,CAACL,QAAQ,CAAC;IACrB,MAAMM,gBAAgB,GAAG,IAAI,CAACC,cAAc,KAAK,gBAAgB;IACjE,MAAMC,aAAa,GAAG,IAAI,CAACrD,oBAAoB;IAC/C,MAAMsD,aAAa,GAAGH,gBAAgB,GAChC,CAAC,IAAAb,qBAAA,GACDe,aAAa,aAAbA,aAAa,gBAAAd,sBAAA,GAAbc,aAAa,CAAEE,SAAS,cAAAhB,sBAAA,uBAAxBA,sBAAA,CAA0Be,aAAa,cAAAhB,qBAAA,cAAAA,qBAAA,GAAI1D,4BAA4B,CAAC0E,aAAa;IAC3F,MAAME,YAAY,GAAGL,gBAAgB,GAC/B,CAAC,IAAAX,sBAAA,GACDa,aAAa,aAAbA,aAAa,gBAAAZ,sBAAA,GAAbY,aAAa,CAAEE,SAAS,cAAAd,sBAAA,uBAAxBA,sBAAA,CAA0Be,YAAY,cAAAhB,sBAAA,cAAAA,sBAAA,GAAI5D,4BAA4B,CAAC4E,YAAY;IACzF,MAAMxB,MAAM,GAAG;MACXC,cAAc,EAAEkB,gBAAgB,KAAIE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEzB,QAAQ,KAAIZ,IAAI,CAACU,YAAY,CAACvC,iBAAiB,CAAC;MACnGsE,YAAY,EAAE;QACV9B,QAAQ,EAAEX,IAAI,CAACU,YAAY,CAACxC,iBAAiB,CAAC;QAC9CwE,oBAAoB,EAAEL,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEK,oBAAoB;QACzDH,SAAS,EAAE;UACPD,aAAa;UACbE;QACJ;MACJ;IACJ,CAAC;IACD,MAAMrD,QAAQ,GAAG,IAAIzB,cAAc,CAACsD,MAAM,EAAE,IAAI,CAAC1B,OAAO,EAAEuC,QAAQ,EAAE,IAAI,CAACc,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC;IACnG,MAAM1B,cAAc,GAAG,CAACF,MAAM,CAACC,cAAc;IAC7C,IAAIC,cAAc,EAAE;MAChB/B,QAAQ,CAACgC,kBAAkB,CAACnB,IAAI,CAAC;IACrC;IACA,IAAI,CAACF,MAAM,CAAC+C,GAAG,CAAC7C,IAAI,EAAE;MAClBgB,MAAM;MACN7B,QAAQ;MACR+B;IACJ,CAAC,CAAC;IACFlB,IAAI,CAACoB,eAAe,CAACpD,sBAAsB,CAAC;EAChD;EACAiC,aAAaA,CAACD,IAAI,EAAE;IAChB,MAAMc,MAAM,GAAG,IAAI,CAAChB,MAAM,CAACiB,GAAG,CAACf,IAAI,CAAC;IACpC,IAAIc,MAAM,EAAE;MACRA,MAAM,CAAC3B,QAAQ,CAAC2D,oBAAoB,CAAC,CAAC;MACtC,IAAI,CAAChD,MAAM,CAACiD,MAAM,CAAC/C,IAAI,CAAC;IAC5B;EACJ;AAGJ;AAACgD,gBAAA,GA/HK5E,eAAe;AAAAE,eAAA,CAAfF,eAAe,wBAAA6E,yBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA6HkF9E,gBAAe;AAAA;AAAAE,eAAA,CA7HhHF,eAAe,+BAgI4DvB,EAAE,CAAAsG,kBAAA;EAAAC,KAAA,EAFwBhF,gBAAe;EAAAiF,OAAA,EAAfjF,gBAAe,CAAAkF,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE9I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF3G,EAAE,CAAA4G,iBAAA,CAAQrF,eAAe,EAAc,CAAC;IAC7GsF,IAAI,EAAEvG,UAAU;IAChBwG,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASnF,eAAe,IAAIwF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}