{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { isObservable, of } from 'rxjs';\nimport { D as DataSource } from './data-source-D34wiQZj.mjs';\nimport { InjectionToken } from '@angular/core';\n\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n  constructor(_data) {\n    super();\n    _defineProperty(this, \"_data\", void 0);\n    this._data = _data;\n  }\n  connect() {\n    return isObservable(this._data) ? this._data : of(this._data);\n  }\n  disconnect() {}\n}\n\n/** Indicates how a view was changed by a {@link _ViewRepeater}. */\nvar _ViewRepeaterOperation;\n(function (_ViewRepeaterOperation) {\n  /** The content of an existing view was replaced with another item. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"REPLACED\"] = 0] = \"REPLACED\";\n  /** A new view was created with `createEmbeddedView`. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"INSERTED\"] = 1] = \"INSERTED\";\n  /** The position of a view changed, but the content remains the same. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"MOVED\"] = 2] = \"MOVED\";\n  /** A view was detached from the view container. */\n  _ViewRepeaterOperation[_ViewRepeaterOperation[\"REMOVED\"] = 3] = \"REMOVED\";\n})(_ViewRepeaterOperation || (_ViewRepeaterOperation = {}));\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n  constructor() {\n    /**\n     * The size of the cache used to store unused views.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n     */\n    _defineProperty(this, \"viewCacheSize\", 20);\n    /**\n     * View cache that stores embedded view instances that have been previously stamped out,\n     * but don't are not currently rendered. The view repeater will reuse these views rather than\n     * creating brand new ones.\n     *\n     * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n     */\n    _defineProperty(this, \"_viewCache\", []);\n  }\n  /** Apply changes to the DOM. */\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    // Rearrange the views to put them in the right location.\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n      if (record.previousIndex == null) {\n        // Item added.\n        const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n        view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = view ? _ViewRepeaterOperation.INSERTED : _ViewRepeaterOperation.REPLACED;\n      } else if (currentIndex == null) {\n        // Item removed.\n        this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n        operation = _ViewRepeaterOperation.REMOVED;\n      } else {\n        // Item moved.\n        view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = _ViewRepeaterOperation.MOVED;\n      }\n      if (itemViewChanged) {\n        var _view;\n        itemViewChanged({\n          context: (_view = view) === null || _view === void 0 ? void 0 : _view.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n  detach() {\n    for (const view of this._viewCache) {\n      view.destroy();\n    }\n    this._viewCache = [];\n  }\n  /**\n   * Inserts a view for a new item, either from the cache or by creating a new\n   * one. Returns `undefined` if the item was inserted into a cached view.\n   */\n  _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n    const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n    if (cachedView) {\n      cachedView.context.$implicit = value;\n      return undefined;\n    }\n    const viewArgs = viewArgsFactory();\n    return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n  }\n  /** Detaches the view at the given index and inserts into the view cache. */\n  _detachAndCacheView(index, viewContainerRef) {\n    const detachedView = viewContainerRef.detach(index);\n    this._maybeCacheView(detachedView, viewContainerRef);\n  }\n  /** Moves view at the previous index to the current index. */\n  _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n    const view = viewContainerRef.get(adjustedPreviousIndex);\n    viewContainerRef.move(view, currentIndex);\n    view.context.$implicit = value;\n    return view;\n  }\n  /**\n   * Cache the given detached view. If the cache is full, the view will be\n   * destroyed.\n   */\n  _maybeCacheView(view, viewContainerRef) {\n    if (this._viewCache.length < this.viewCacheSize) {\n      this._viewCache.push(view);\n    } else {\n      const index = viewContainerRef.indexOf(view);\n      // The host component could remove views from the container outside of\n      // the view repeater. It's unlikely this will occur, but just in case,\n      // destroy the view on its own, otherwise destroy it through the\n      // container to ensure that all the references are removed.\n      if (index === -1) {\n        view.destroy();\n      } else {\n        viewContainerRef.remove(index);\n      }\n    }\n  }\n  /** Inserts a recycled view from the cache at the given index. */\n  _insertViewFromCache(index, viewContainerRef) {\n    const cachedView = this._viewCache.pop();\n    if (cachedView) {\n      viewContainerRef.insert(cachedView, index);\n    }\n    return cachedView || null;\n  }\n}\nexport { ArrayDataSource as A, _RecycleViewRepeaterStrategy as _, _ViewRepeaterOperation as a, _VIEW_REPEATER_STRATEGY as b };\n//# sourceMappingURL=recycle-view-repeater-strategy-DoWdPqVw.mjs.map", "map": {"version": 3, "names": ["isObservable", "of", "D", "DataSource", "InjectionToken", "ArrayDataSource", "constructor", "_data", "_defineProperty", "connect", "disconnect", "_ViewRepeaterOperation", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "applyChanges", "changes", "viewContainerRef", "itemContextFactory", "itemValueResolver", "itemViewChanged", "forEachOperation", "record", "adjustedPreviousIndex", "currentIndex", "view", "operation", "previousIndex", "viewArgsFactory", "_insertView", "INSERTED", "REPLACED", "_detachAndCacheView", "REMOVED", "_moveView", "MOVED", "_view", "context", "detach", "_viewCache", "destroy", "value", "cachedView", "_insertViewFromCache", "$implicit", "undefined", "viewArgs", "createEmbeddedView", "templateRef", "index", "detached<PERSON>iew", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get", "move", "length", "viewCacheSize", "push", "indexOf", "remove", "pop", "insert", "A", "_", "a", "b"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/recycle-view-repeater-strategy-DoWdPqVw.mjs"], "sourcesContent": ["import { isObservable, of } from 'rxjs';\nimport { D as DataSource } from './data-source-D34wiQZj.mjs';\nimport { InjectionToken } from '@angular/core';\n\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n    _data;\n    constructor(_data) {\n        super();\n        this._data = _data;\n    }\n    connect() {\n        return isObservable(this._data) ? this._data : of(this._data);\n    }\n    disconnect() { }\n}\n\n/** Indicates how a view was changed by a {@link _ViewRepeater}. */\nvar _ViewRepeaterOperation;\n(function (_ViewRepeaterOperation) {\n    /** The content of an existing view was replaced with another item. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"REPLACED\"] = 0] = \"REPLACED\";\n    /** A new view was created with `createEmbeddedView`. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"INSERTED\"] = 1] = \"INSERTED\";\n    /** The position of a view changed, but the content remains the same. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"MOVED\"] = 2] = \"MOVED\";\n    /** A view was detached from the view container. */\n    _ViewRepeaterOperation[_ViewRepeaterOperation[\"REMOVED\"] = 3] = \"REMOVED\";\n})(_ViewRepeaterOperation || (_ViewRepeaterOperation = {}));\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n    /**\n     * The size of the cache used to store unused views.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n     */\n    viewCacheSize = 20;\n    /**\n     * View cache that stores embedded view instances that have been previously stamped out,\n     * but don't are not currently rendered. The view repeater will reuse these views rather than\n     * creating brand new ones.\n     *\n     * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n     */\n    _viewCache = [];\n    /** Apply changes to the DOM. */\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        // Rearrange the views to put them in the right location.\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                // Item added.\n                const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = view ? _ViewRepeaterOperation.INSERTED : _ViewRepeaterOperation.REPLACED;\n            }\n            else if (currentIndex == null) {\n                // Item removed.\n                this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n                operation = _ViewRepeaterOperation.REMOVED;\n            }\n            else {\n                // Item moved.\n                view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = _ViewRepeaterOperation.MOVED;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view?.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() {\n        for (const view of this._viewCache) {\n            view.destroy();\n        }\n        this._viewCache = [];\n    }\n    /**\n     * Inserts a view for a new item, either from the cache or by creating a new\n     * one. Returns `undefined` if the item was inserted into a cached view.\n     */\n    _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n        const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n        if (cachedView) {\n            cachedView.context.$implicit = value;\n            return undefined;\n        }\n        const viewArgs = viewArgsFactory();\n        return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n    }\n    /** Detaches the view at the given index and inserts into the view cache. */\n    _detachAndCacheView(index, viewContainerRef) {\n        const detachedView = viewContainerRef.detach(index);\n        this._maybeCacheView(detachedView, viewContainerRef);\n    }\n    /** Moves view at the previous index to the current index. */\n    _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n        const view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        view.context.$implicit = value;\n        return view;\n    }\n    /**\n     * Cache the given detached view. If the cache is full, the view will be\n     * destroyed.\n     */\n    _maybeCacheView(view, viewContainerRef) {\n        if (this._viewCache.length < this.viewCacheSize) {\n            this._viewCache.push(view);\n        }\n        else {\n            const index = viewContainerRef.indexOf(view);\n            // The host component could remove views from the container outside of\n            // the view repeater. It's unlikely this will occur, but just in case,\n            // destroy the view on its own, otherwise destroy it through the\n            // container to ensure that all the references are removed.\n            if (index === -1) {\n                view.destroy();\n            }\n            else {\n                viewContainerRef.remove(index);\n            }\n        }\n    }\n    /** Inserts a recycled view from the cache at the given index. */\n    _insertViewFromCache(index, viewContainerRef) {\n        const cachedView = this._viewCache.pop();\n        if (cachedView) {\n            viewContainerRef.insert(cachedView, index);\n        }\n        return cachedView || null;\n    }\n}\n\nexport { ArrayDataSource as A, _RecycleViewRepeaterStrategy as _, _ViewRepeaterOperation as a, _VIEW_REPEATER_STRATEGY as b };\n//# sourceMappingURL=recycle-view-repeater-strategy-DoWdPqVw.mjs.map\n"], "mappings": ";AAAA,SAASA,YAAY,EAAEC,EAAE,QAAQ,MAAM;AACvC,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC5D,SAASC,cAAc,QAAQ,eAAe;;AAE9C;AACA,MAAMC,eAAe,SAASF,UAAU,CAAC;EAErCG,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IAACC,eAAA;IACR,IAAI,CAACD,KAAK,GAAGA,KAAK;EACtB;EACAE,OAAOA,CAAA,EAAG;IACN,OAAOT,YAAY,CAAC,IAAI,CAACO,KAAK,CAAC,GAAG,IAAI,CAACA,KAAK,GAAGN,EAAE,CAAC,IAAI,CAACM,KAAK,CAAC;EACjE;EACAG,UAAUA,CAAA,EAAG,CAAE;AACnB;;AAEA;AACA,IAAIC,sBAAsB;AAC1B,CAAC,UAAUA,sBAAsB,EAAE;EAC/B;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC3E;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC3E;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACrE;EACAA,sBAAsB,CAACA,sBAAsB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;AAC7E,CAAC,EAAEA,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,IAAIR,cAAc,CAAC,eAAe,CAAC;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,4BAA4B,CAAC;EAAAP,YAAA;IAC/B;AACJ;AACA;AACA;IAHIE,eAAA,wBAIgB,EAAE;IAClB;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA,qBAOa,EAAE;EAAA;EACf;EACAM,YAAYA,CAACC,OAAO,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,eAAe,EAAE;IAC5F;IACAJ,OAAO,CAACK,gBAAgB,CAAC,CAACC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,KAAK;MACtE,IAAIC,IAAI;MACR,IAAIC,SAAS;MACb,IAAIJ,MAAM,CAACK,aAAa,IAAI,IAAI,EAAE;QAC9B;QACA,MAAMC,eAAe,GAAGA,CAAA,KAAMV,kBAAkB,CAACI,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,CAAC;QAC7FC,IAAI,GAAG,IAAI,CAACI,WAAW,CAACD,eAAe,EAAEJ,YAAY,EAAEP,gBAAgB,EAAEE,iBAAiB,CAACG,MAAM,CAAC,CAAC;QACnGI,SAAS,GAAGD,IAAI,GAAGb,sBAAsB,CAACkB,QAAQ,GAAGlB,sBAAsB,CAACmB,QAAQ;MACxF,CAAC,MACI,IAAIP,YAAY,IAAI,IAAI,EAAE;QAC3B;QACA,IAAI,CAACQ,mBAAmB,CAACT,qBAAqB,EAAEN,gBAAgB,CAAC;QACjES,SAAS,GAAGd,sBAAsB,CAACqB,OAAO;MAC9C,CAAC,MACI;QACD;QACAR,IAAI,GAAG,IAAI,CAACS,SAAS,CAACX,qBAAqB,EAAEC,YAAY,EAAEP,gBAAgB,EAAEE,iBAAiB,CAACG,MAAM,CAAC,CAAC;QACvGI,SAAS,GAAGd,sBAAsB,CAACuB,KAAK;MAC5C;MACA,IAAIf,eAAe,EAAE;QAAA,IAAAgB,KAAA;QACjBhB,eAAe,CAAC;UACZiB,OAAO,GAAAD,KAAA,GAAEX,IAAI,cAAAW,KAAA,uBAAJA,KAAA,CAAMC,OAAO;UACtBX,SAAS;UACTJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAgB,MAAMA,CAAA,EAAG;IACL,KAAK,MAAMb,IAAI,IAAI,IAAI,CAACc,UAAU,EAAE;MAChCd,IAAI,CAACe,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,CAACD,UAAU,GAAG,EAAE;EACxB;EACA;AACJ;AACA;AACA;EACIV,WAAWA,CAACD,eAAe,EAAEJ,YAAY,EAAEP,gBAAgB,EAAEwB,KAAK,EAAE;IAChE,MAAMC,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAACnB,YAAY,EAAEP,gBAAgB,CAAC;IAC5E,IAAIyB,UAAU,EAAE;MACZA,UAAU,CAACL,OAAO,CAACO,SAAS,GAAGH,KAAK;MACpC,OAAOI,SAAS;IACpB;IACA,MAAMC,QAAQ,GAAGlB,eAAe,CAAC,CAAC;IAClC,OAAOX,gBAAgB,CAAC8B,kBAAkB,CAACD,QAAQ,CAACE,WAAW,EAAEF,QAAQ,CAACT,OAAO,EAAES,QAAQ,CAACG,KAAK,CAAC;EACtG;EACA;EACAjB,mBAAmBA,CAACiB,KAAK,EAAEhC,gBAAgB,EAAE;IACzC,MAAMiC,YAAY,GAAGjC,gBAAgB,CAACqB,MAAM,CAACW,KAAK,CAAC;IACnD,IAAI,CAACE,eAAe,CAACD,YAAY,EAAEjC,gBAAgB,CAAC;EACxD;EACA;EACAiB,SAASA,CAACX,qBAAqB,EAAEC,YAAY,EAAEP,gBAAgB,EAAEwB,KAAK,EAAE;IACpE,MAAMhB,IAAI,GAAGR,gBAAgB,CAACmC,GAAG,CAAC7B,qBAAqB,CAAC;IACxDN,gBAAgB,CAACoC,IAAI,CAAC5B,IAAI,EAAED,YAAY,CAAC;IACzCC,IAAI,CAACY,OAAO,CAACO,SAAS,GAAGH,KAAK;IAC9B,OAAOhB,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI0B,eAAeA,CAAC1B,IAAI,EAAER,gBAAgB,EAAE;IACpC,IAAI,IAAI,CAACsB,UAAU,CAACe,MAAM,GAAG,IAAI,CAACC,aAAa,EAAE;MAC7C,IAAI,CAAChB,UAAU,CAACiB,IAAI,CAAC/B,IAAI,CAAC;IAC9B,CAAC,MACI;MACD,MAAMwB,KAAK,GAAGhC,gBAAgB,CAACwC,OAAO,CAAChC,IAAI,CAAC;MAC5C;MACA;MACA;MACA;MACA,IAAIwB,KAAK,KAAK,CAAC,CAAC,EAAE;QACdxB,IAAI,CAACe,OAAO,CAAC,CAAC;MAClB,CAAC,MACI;QACDvB,gBAAgB,CAACyC,MAAM,CAACT,KAAK,CAAC;MAClC;IACJ;EACJ;EACA;EACAN,oBAAoBA,CAACM,KAAK,EAAEhC,gBAAgB,EAAE;IAC1C,MAAMyB,UAAU,GAAG,IAAI,CAACH,UAAU,CAACoB,GAAG,CAAC,CAAC;IACxC,IAAIjB,UAAU,EAAE;MACZzB,gBAAgB,CAAC2C,MAAM,CAAClB,UAAU,EAAEO,KAAK,CAAC;IAC9C;IACA,OAAOP,UAAU,IAAI,IAAI;EAC7B;AACJ;AAEA,SAASpC,eAAe,IAAIuD,CAAC,EAAE/C,4BAA4B,IAAIgD,CAAC,EAAElD,sBAAsB,IAAImD,CAAC,EAAElD,uBAAuB,IAAImD,CAAC;AAC3H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}