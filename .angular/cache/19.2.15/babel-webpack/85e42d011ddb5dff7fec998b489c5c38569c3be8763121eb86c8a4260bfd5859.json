{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nexport class PermissionService {\n  constructor(permissions) {\n    this.permissions = permissions;\n    this.permissionTree = this.permissions.reduce((tree, item) => _objectSpread(_objectSpread({}, tree), this.insertNode(tree, item.split(':'))), {});\n  }\n  allowedTo(permissions) {\n    if (permissions.length === 0) {\n      return false;\n    }\n    return permissions.some(permission => this.checkPermission(this.permissionTree, permission.split(':')));\n  }\n  areGranted(permissions) {\n    if (permissions.indexOf('granted:all') > -1) {\n      return true;\n    }\n    if (permissions.indexOf('denied:all') > -1) {\n      return false;\n    }\n    const shortPermissions = [];\n    permissions.forEach(permission => {\n      const match = permission.match(/(?:entity:|keyentity:)*(\\w|-)+/);\n      if (Array.isArray(match) && match.length) {\n        shortPermissions.push(match[0]);\n      }\n    });\n    if (shortPermissions.some(permission => this.permissions.indexOf(permission) > -1)) {\n      return true;\n    }\n    return permissions.some(permission => this.permissions.indexOf(permission) > -1);\n  }\n  checkPermission(tree, parts) {\n    const part = parts.shift();\n    const node = part ? tree[part] || {} : {};\n    if (!('__self' in node) && parts.length) {\n      return this.checkPermission(node, parts);\n    }\n    return '__self' in node;\n  }\n  insertNode(tree, parts) {\n    const part = parts.shift();\n    if (!part) {\n      return {};\n    }\n    if (parts.length) {\n      return _objectSpread(_objectSpread({}, tree), {}, {\n        [part]: this.insertNode(part in tree ? tree[part] : {}, parts)\n      });\n    }\n    return _objectSpread(_objectSpread({}, tree), {}, {\n      [part]: _objectSpread(_objectSpread({}, tree[part]), {}, {\n        __self: true\n      })\n    });\n  }\n}", "map": {"version": 3, "names": ["PermissionService", "constructor", "permissions", "permissionTree", "reduce", "tree", "item", "_objectSpread", "insertNode", "split", "allowedTo", "length", "some", "permission", "checkPermission", "areGranted", "indexOf", "shortPermissions", "for<PERSON>ach", "match", "Array", "isArray", "push", "parts", "part", "shift", "node", "__self"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-auth/permission.service.ts"], "sourcesContent": ["export class PermissionService {\n  private readonly permissionTree: any;\n\n  constructor( readonly permissions: string[] ) {\n    this.permissionTree = this.permissions.reduce(( tree, item ) => ({\n      ...tree,\n      ...this.insertNode(tree, item.split(':'))\n    }), {});\n  }\n\n  allowedTo( permissions: string[] ): boolean {\n    if (permissions.length === 0) {\n      return false;\n    }\n    return permissions.some(permission => this.checkPermission(this.permissionTree, permission.split(':')));\n  }\n\n  areGranted( permissions: string[] ): boolean {\n    if (permissions.indexOf('granted:all') > -1) {\n      return true;\n    }\n    if (permissions.indexOf('denied:all') > -1) {\n      return false;\n    }\n    const shortPermissions: string[] = [];\n    permissions.forEach(permission => {\n      const match = permission.match(/(?:entity:|keyentity:)*(\\w|-)+/);\n      if (Array.isArray(match) && match.length) {\n        shortPermissions.push(match[0]);\n      }\n    });\n    if (shortPermissions.some(permission => this.permissions.indexOf(permission) > -1)) {\n      return true;\n    }\n    return permissions.some(permission => this.permissions.indexOf(permission) > -1);\n  }\n\n  private checkPermission( tree: any, parts: string[] ): boolean {\n    const part = parts.shift();\n    const node = part ? tree[part] || {} : {};\n    if (!('__self' in node) && parts.length) {\n      return this.checkPermission(node, parts);\n    }\n    return '__self' in node;\n  }\n\n  private insertNode( tree: any, parts: string[] ): any {\n    const part = parts.shift();\n    if (!part) {\n      return {};\n    }\n    if (parts.length) {\n      return { ...tree, [part]: this.insertNode(part in tree ? tree[part] : {}, parts) };\n    }\n    return { ...tree, [part]: { ...tree[part], __self: true } };\n  }\n}\n"], "mappings": ";AAAA,OAAM,MAAOA,iBAAiB;EAG5BC,YAAsBC,WAAqB;IAArB,KAAAA,WAAW,GAAXA,WAAW;IAC/B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACD,WAAW,CAACE,MAAM,CAAC,CAAEC,IAAI,EAAEC,IAAI,KAAAC,aAAA,CAAAA,aAAA,KACrDF,IAAI,GACJ,IAAI,CAACG,UAAU,CAACH,IAAI,EAAEC,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CACzC,EAAE,EAAE,CAAC;EACT;EAEAC,SAASA,CAAER,WAAqB;IAC9B,IAAIA,WAAW,CAACS,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO,KAAK;IACd;IACA,OAAOT,WAAW,CAACU,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,eAAe,CAAC,IAAI,CAACX,cAAc,EAAEU,UAAU,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACzG;EAEAM,UAAUA,CAAEb,WAAqB;IAC/B,IAAIA,WAAW,CAACc,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3C,OAAO,IAAI;IACb;IACA,IAAId,WAAW,CAACc,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;MAC1C,OAAO,KAAK;IACd;IACA,MAAMC,gBAAgB,GAAa,EAAE;IACrCf,WAAW,CAACgB,OAAO,CAACL,UAAU,IAAG;MAC/B,MAAMM,KAAK,GAAGN,UAAU,CAACM,KAAK,CAAC,gCAAgC,CAAC;MAChE,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACR,MAAM,EAAE;QACxCM,gBAAgB,CAACK,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IACF,IAAIF,gBAAgB,CAACL,IAAI,CAACC,UAAU,IAAI,IAAI,CAACX,WAAW,CAACc,OAAO,CAACH,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAClF,OAAO,IAAI;IACb;IACA,OAAOX,WAAW,CAACU,IAAI,CAACC,UAAU,IAAI,IAAI,CAACX,WAAW,CAACc,OAAO,CAACH,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;EAClF;EAEQC,eAAeA,CAAET,IAAS,EAAEkB,KAAe;IACjD,MAAMC,IAAI,GAAGD,KAAK,CAACE,KAAK,EAAE;IAC1B,MAAMC,IAAI,GAAGF,IAAI,GAAGnB,IAAI,CAACmB,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;IACzC,IAAI,EAAE,QAAQ,IAAIE,IAAI,CAAC,IAAIH,KAAK,CAACZ,MAAM,EAAE;MACvC,OAAO,IAAI,CAACG,eAAe,CAACY,IAAI,EAAEH,KAAK,CAAC;IAC1C;IACA,OAAO,QAAQ,IAAIG,IAAI;EACzB;EAEQlB,UAAUA,CAAEH,IAAS,EAAEkB,KAAe;IAC5C,MAAMC,IAAI,GAAGD,KAAK,CAACE,KAAK,EAAE;IAC1B,IAAI,CAACD,IAAI,EAAE;MACT,OAAO,EAAE;IACX;IACA,IAAID,KAAK,CAACZ,MAAM,EAAE;MAChB,OAAAJ,aAAA,CAAAA,aAAA,KAAYF,IAAI;QAAE,CAACmB,IAAI,GAAG,IAAI,CAAChB,UAAU,CAACgB,IAAI,IAAInB,IAAI,GAAGA,IAAI,CAACmB,IAAI,CAAC,GAAG,EAAE,EAAED,KAAK;MAAC;IAClF;IACA,OAAAhB,aAAA,CAAAA,aAAA,KAAYF,IAAI;MAAE,CAACmB,IAAI,GAAAjB,aAAA,CAAAA,aAAA,KAAQF,IAAI,CAACmB,IAAI,CAAC;QAAEG,MAAM,EAAE;MAAI;IAAE;EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}