{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiSidebarComponent } from './swui-sidebar.component';\nimport { SwuiMenuModule } from '../swui-menu/swui-menu.module';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\nlet SwuiSidebarModule = class SwuiSidebarModule {};\nSwuiSidebarModule = __decorate([NgModule({\n  declarations: [SwuiSidebarComponent],\n  imports: [CommonModule, MatIconModule, MatRippleModule, SwuiMenuModule],\n  exports: [SwuiSidebarComponent]\n})], SwuiSidebarModule);\nexport { SwuiSidebarModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "SwuiSidebarComponent", "SwuiMenuModule", "MatIconModule", "MatRippleModule", "SwuiSidebarModule", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-sidebar/swui-sidebar.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiSidebarComponent } from './swui-sidebar.component';\nimport { SwuiMenuModule } from '../swui-menu/swui-menu.module';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\nlet SwuiSidebarModule = class SwuiSidebarModule {\n};\nSwuiSidebarModule = __decorate([\n    NgModule({\n        declarations: [SwuiSidebarComponent],\n        imports: [\n            CommonModule,\n            MatIconModule,\n            MatRippleModule,\n            SwuiMenuModule,\n        ],\n        exports: [SwuiSidebarComponent],\n    })\n], SwuiSidebarModule);\nexport { SwuiSidebarModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,IAAIC,iBAAiB,GAAG,MAAMA,iBAAiB,CAAC,EAC/C;AACDA,iBAAiB,GAAGP,UAAU,CAAC,CAC3BC,QAAQ,CAAC;EACLO,YAAY,EAAE,CAACL,oBAAoB,CAAC;EACpCM,OAAO,EAAE,CACLP,YAAY,EACZG,aAAa,EACbC,eAAe,EACfF,cAAc,CACjB;EACDM,OAAO,EAAE,CAACP,oBAAoB;AAClC,CAAC,CAAC,CACL,EAAEI,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}