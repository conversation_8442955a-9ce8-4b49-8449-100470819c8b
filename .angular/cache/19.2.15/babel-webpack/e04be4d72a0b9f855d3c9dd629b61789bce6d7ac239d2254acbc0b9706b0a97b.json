{"ast": null, "code": "var _LanguageSelectorComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./language-selector.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./language-selector.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, Inject, Input, Output } from '@angular/core';\nimport { TranslateService } from '@ngx-translate/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nlet LanguageSelectorComponent = (_LanguageSelectorComponent = class LanguageSelectorComponent {\n  constructor(config, translate, elementRef, cdr) {\n    this.elementRef = elementRef;\n    this.cdr = cdr;\n    this.inMenu = false;\n    this.valueChanges = new EventEmitter();\n    this.isOpen = false;\n    this.items = config.langs || [];\n    this.active = this.items.find(({\n      id\n    }) => id === translate.currentLang);\n    translate.onLangChange.subscribe(({\n      lang\n    }) => {\n      this.active = this.items.find(({\n        id\n      }) => id === lang);\n      this.cdr.detectChanges();\n    });\n  }\n  onClick(target) {\n    const inside = this.elementRef.nativeElement.contains(target);\n    if (!inside && this.isOpen) {\n      this.isOpen = false;\n    }\n  }\n  select(item) {\n    if (item !== undefined) {\n      this.active = item;\n      this.valueChanges.emit(item.id);\n    }\n  }\n  prevent(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  triggerDropdown(event) {\n    event.preventDefault();\n    this.isOpen = !this.isOpen;\n  }\n}, _LanguageSelectorComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_HUB_MESSAGE_CONFIG]\n  }]\n}, {\n  type: TranslateService\n}, {\n  type: ElementRef\n}, {\n  type: ChangeDetectorRef\n}], _LanguageSelectorComponent.propDecorators = {\n  inMenu: [{\n    type: Input\n  }],\n  valueChanges: [{\n    type: Output\n  }],\n  onClick: [{\n    type: HostListener,\n    args: ['document:click', ['$event.target']]\n  }]\n}, _LanguageSelectorComponent);\nLanguageSelectorComponent = __decorate([Component({\n  selector: 'lib-swui-language-selector',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], LanguageSelectorComponent);\nexport { LanguageSelectorComponent };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "ChangeDetectorRef", "Component", "ElementRef", "EventEmitter", "HostListener", "Inject", "Input", "Output", "TranslateService", "SWUI_HUB_MESSAGE_CONFIG", "LanguageSelectorComponent", "_LanguageSelectorComponent", "constructor", "config", "translate", "elementRef", "cdr", "inMenu", "valueChanges", "isOpen", "items", "langs", "active", "find", "id", "currentLang", "onLangChange", "subscribe", "lang", "detectChanges", "onClick", "target", "inside", "nativeElement", "contains", "select", "item", "undefined", "emit", "prevent", "event", "preventDefault", "stopPropagation", "triggerDropdown", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "changeDetection", "OnPush", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/language-selector/language-selector.component.ts"], "sourcesContent": ["import {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  HostListener,\n  Inject,\n  Input,\n  Output\n} from '@angular/core';\nimport { LangChangeEvent, TranslateService } from '@ngx-translate/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { SwHubMessageModuleConfig } from '../../services/sw-hub-init/sw-hub-init.model';\n\nexport interface LangItem {\n  id: string;\n  dialect?: string | string[];\n  title?: string;\n  image?: string;\n}\n\n@Component({\n    selector: 'lib-swui-language-selector',\n    templateUrl: './language-selector.component.html',\n    styleUrls: ['./language-selector.component.scss'],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    standalone: false\n})\nexport class LanguageSelectorComponent {\n  @Input() inMenu = false;\n  @Output() valueChanges = new EventEmitter<string>();\n\n  readonly items: LangItem[];\n\n  active?: LangItem;\n  isOpen = false;\n\n  constructor( @Inject(SWUI_HUB_MESSAGE_CONFIG) config: SwHubMessageModuleConfig,\n               translate: TranslateService,\n               private readonly elementRef: ElementRef,\n               private readonly cdr: ChangeDetectorRef ) {\n    this.items = config.langs || [];\n    this.active = this.items.find(( { id } ) => id === translate.currentLang);\n    translate.onLangChange.subscribe(( { lang }: LangChangeEvent ) => {\n      this.active = this.items.find(( { id } ) => id === lang);\n      this.cdr.detectChanges();\n    });\n  }\n\n  @HostListener('document:click', ['$event.target'])\n  onClick( target: HTMLElement ) {\n    const inside = this.elementRef.nativeElement.contains(target);\n    if (!inside && this.isOpen) {\n      this.isOpen = false;\n    }\n  }\n\n  select( item: LangItem | undefined ): void {\n    if (item !== undefined) {\n      this.active = item;\n      this.valueChanges.emit(item.id);\n    }\n  }\n\n  prevent( event: Event ) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n\n  triggerDropdown( event: Event ) {\n    event.preventDefault();\n    this.isOpen = !this.isOpen;\n  }\n}\n"], "mappings": ";;;;AAAA,SACEA,uBAAuB,EACvBC,iBAAiB,EACjBC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,KAAK,EACLC,MAAM,QACD,eAAe;AACtB,SAA0BC,gBAAgB,QAAQ,qBAAqB;AACvE,SAASC,uBAAuB,QAAQ,8CAA8C;AAiB/E,IAAMC,yBAAyB,IAAAC,0BAAA,GAA/B,MAAMD,yBAAyB;EASpCE,YAA8CC,MAAgC,EACjEC,SAA2B,EACVC,UAAsB,EACtBC,GAAsB;IADtB,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,GAAG,GAAHA,GAAG;IAXxB,KAAAC,MAAM,GAAG,KAAK;IACb,KAAAC,YAAY,GAAG,IAAIf,YAAY,EAAU;IAKnD,KAAAgB,MAAM,GAAG,KAAK;IAMZ,IAAI,CAACC,KAAK,GAAGP,MAAM,CAACQ,KAAK,IAAI,EAAE;IAC/B,IAAI,CAACC,MAAM,GAAG,IAAI,CAACF,KAAK,CAACG,IAAI,CAAC,CAAE;MAAEC;IAAE,CAAE,KAAMA,EAAE,KAAKV,SAAS,CAACW,WAAW,CAAC;IACzEX,SAAS,CAACY,YAAY,CAACC,SAAS,CAAC,CAAE;MAAEC;IAAI,CAAmB,KAAK;MAC/D,IAAI,CAACN,MAAM,GAAG,IAAI,CAACF,KAAK,CAACG,IAAI,CAAC,CAAE;QAAEC;MAAE,CAAE,KAAMA,EAAE,KAAKI,IAAI,CAAC;MACxD,IAAI,CAACZ,GAAG,CAACa,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAGAC,OAAOA,CAAEC,MAAmB;IAC1B,MAAMC,MAAM,GAAG,IAAI,CAACjB,UAAU,CAACkB,aAAa,CAACC,QAAQ,CAACH,MAAM,CAAC;IAC7D,IAAI,CAACC,MAAM,IAAI,IAAI,CAACb,MAAM,EAAE;MAC1B,IAAI,CAACA,MAAM,GAAG,KAAK;IACrB;EACF;EAEAgB,MAAMA,CAAEC,IAA0B;IAChC,IAAIA,IAAI,KAAKC,SAAS,EAAE;MACtB,IAAI,CAACf,MAAM,GAAGc,IAAI;MAClB,IAAI,CAAClB,YAAY,CAACoB,IAAI,CAACF,IAAI,CAACZ,EAAE,CAAC;IACjC;EACF;EAEAe,OAAOA,CAAEC,KAAY;IACnBA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACE,eAAe,EAAE;EACzB;EAEAC,eAAeA,CAAEH,KAAY;IAC3BA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACtB,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;EAC5B;;;;UAnCcd,MAAM;IAAAuC,IAAA,GAACnC,uBAAuB;EAAA;AAAA,G;;;;;;;;UAR3CH;EAAK;;UACLC;EAAM;;UAmBNH,YAAY;IAAAwC,IAAA,GAAC,gBAAgB,EAAE,CAAC,eAAe,CAAC;EAAA;;AArBtClC,yBAAyB,GAAAmC,UAAA,EAPrC5C,SAAS,CAAC;EACP6C,QAAQ,EAAE,4BAA4B;EACtCC,QAAA,EAAAC,oBAAiD;EAEjDC,eAAe,EAAElD,uBAAuB,CAACmD,MAAM;EAC/CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWzC,yBAAyB,CA6CrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}