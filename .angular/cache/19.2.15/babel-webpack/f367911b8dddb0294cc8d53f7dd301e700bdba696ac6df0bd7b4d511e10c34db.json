{"ast": null, "code": "var _SwuiSelectComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-select.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-select.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostBinding, Input, isDevMode, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { TranslateService } from '@ngx-translate/core';\nimport { ReplaySubject } from 'rxjs';\nimport { filter, map, take, takeUntil } from 'rxjs/operators';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nconst CONTROL_NAME = 'lib-swui-select';\nlet nextUniqueId = 0;\nlet SwuiSelectComponent = (_SwuiSelectComponent = class SwuiSelectComponent extends SwuiMatFormFieldControl {\n  set multiple(value) {\n    const newMultiple = coerceBooleanProperty(value);\n    if (newMultiple !== this._multiple) {\n      if (isDevMode() && this._contentInitialized) {\n        throw new Error('Cannot change `multiple` mode of `lib-swui-select` after initialization.');\n      }\n      this._multiple = newMultiple;\n      this.itemHeight = newMultiple ? 48 : 42;\n    }\n  }\n  get multiple() {\n    return this._multiple;\n  }\n  set data(val) {\n    var _this$options;\n    this._data = val || [];\n    this.options = this._data;\n    if (this.multiple && !this.disableAllOption) {\n      var _this$value;\n      this.allChecked = ((_this$value = this.value) === null || _this$value === void 0 ? void 0 : _this$value.length) === this.enabledData.length;\n    }\n    (_this$options = this.options) === null || _this$options === void 0 || _this$options.forEach(option => {\n      var _this$value2;\n      return option.state = {\n        checked: (_this$value2 = this.value) === null || _this$value2 === void 0 ? void 0 : _this$value2.includes(option.id)\n      };\n    });\n    if (Array.isArray(this.options) && this.options.length) {\n      this.onDataReceived.next(this.options);\n    }\n  }\n  get data() {\n    return this._data;\n  }\n  set value(val) {\n    this.patchSelectControl(val);\n  }\n  get value() {\n    var _this$selectControl$v, _this$selectControl$v2;\n    return this.multiple ? (_this$selectControl$v = this.selectControl.value) === null || _this$selectControl$v === void 0 ? void 0 : _this$selectControl$v.map(v => v.id) : this.selectControl.value && ((_this$selectControl$v2 = this.selectControl.value[0]) === null || _this$selectControl$v2 === void 0 ? void 0 : _this$selectControl$v2.id);\n  }\n  get empty() {\n    var _this$selectControl$v3;\n    return !((_this$selectControl$v3 = this.selectControl.value) !== null && _this$selectControl$v3 !== void 0 && _this$selectControl$v3.length);\n  }\n  get viewportHeight() {\n    let length = this.data.length;\n    if (!this.multiple && !this.disableEmptyOption) {\n      length += 1;\n    }\n    length = Math.floor(length / 5) && 5 || length % 5;\n    if (length === 5) {\n      if (this.showSearch) {\n        length -= 1;\n      }\n      if (this.multiple) {\n        length -= 1;\n      }\n    }\n    return length * this.itemHeight;\n  }\n  get shouldLabelFloat() {\n    return this.triggerInputControl.value;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, cd, translate) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.cd = cd;\n    this.translate = translate;\n    this.searchPlaceholder = 'Search';\n    this.showSearch = false;\n    this.startSearchLength = 0;\n    this.emptyOptionPlaceholder = 'None';\n    this.disableEmptyOption = false;\n    this.disableAllOption = false;\n    this.allChecked = false;\n    this.options = [];\n    this.itemHeight = 48;\n    this.onDataReceived = new ReplaySubject(1);\n    this.controlType = CONTROL_NAME;\n    this.triggerInputControl = new UntypedFormControl();\n    this.searchControl = new UntypedFormControl();\n    this.selectControl = new UntypedFormControl();\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._contentInitialized = false;\n    this._multiple = false;\n    this._data = [];\n    this.previousSelected = null;\n  }\n  ngOnInit() {\n    this.searchControl.valueChanges.pipe(filter(data => this.showSearch && (data.length >= (this.startSearchLength || 0) || data.length === 0)), map(searchString => searchString.toLowerCase()), takeUntil(this.destroyed$)).subscribe(search => {\n      this.previousSelected = this.multiple && search ? this.selectControl.value : null;\n      this.options = this.data.filter(option => {\n        return option.text && option.text.toLowerCase().indexOf(search) > -1;\n      });\n      this.cd.markForCheck();\n    });\n    this.selectControl.valueChanges.pipe(map(val => {\n      if (this.multiple && Array.isArray(this.previousSelected) && Array.isArray(val)) {\n        const previousSelected = this.previousSelected.filter(id => !this.options.some(option => option.id === id));\n        const values = this.enabledData.filter(({\n          id\n        }) => previousSelected.some(item => item === id));\n        return [...values, ...val];\n      }\n      return val;\n    }), map(val => val.map(item => item ? item.id : '')), takeUntil(this.destroyed$)).subscribe(val => {\n      this.setVisibleValue(val);\n      this.onChange(this.multiple ? val : val[0] || null);\n      if (this.multiple && !this.disableAllOption) {\n        this.allChecked = this.enabledData.every(({\n          id\n        }) => {\n          var _this$value3;\n          return (_this$value3 = this.value) === null || _this$value3 === void 0 ? void 0 : _this$value3.includes(id);\n        });\n      }\n      this.cd.detectChanges();\n    });\n  }\n  ngAfterContentInit() {\n    this._contentInitialized = true;\n  }\n  setVisibleValue(val, options) {\n    if (options) {\n      this.patchTriggerInputControl(val, options);\n    } else {\n      this.onDataReceived.pipe(take(1)).subscribe(data => {\n        this.patchTriggerInputControl(val, data);\n      });\n    }\n  }\n  patchTriggerInputControl(val, options) {\n    let option = options === null || options === void 0 ? void 0 : options.find(opt => opt.id === val[0]);\n    let text = option ? this.translate.instant(option.text) : val[0];\n    let visibleValue = (val === null || val === void 0 ? void 0 : val.length) > 1 ? `${text} (+${val.length - 1} ${this.translate.instant('ALL.more')})` : text;\n    this.triggerInputControl.patchValue(visibleValue);\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.elRef && this.trigger && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n      this.trigger.openMenu();\n    }\n  }\n  writeValue(val) {\n    this.patchSelectControl(val);\n  }\n  patchSelectControl(val) {\n    this.onDataReceived.pipe(take(1)).subscribe(options => {\n      if (this.multiple && !this.disableAllOption) {\n        this.allChecked = Array.isArray(val) && val.length === this.enabledData.length;\n      }\n      const values = coerceArray(val || []);\n      options === null || options === void 0 || options.forEach(option => option.state = {\n        checked: values === null || values === void 0 ? void 0 : values.includes(option.id)\n      });\n      const value = options.filter(opt => values.includes(opt.id));\n      this.selectControl.patchValue(value, {\n        emitEvent: false\n      });\n      this.setVisibleValue(values, options);\n      this.cd.detectChanges();\n    });\n  }\n  toggleAll(event) {\n    event === null || event === void 0 || event.preventDefault();\n    event === null || event === void 0 || event.stopPropagation();\n    this.allChecked = !this.allChecked;\n    this.options.forEach(option => {\n      if (!option.disabled) {\n        option.state.checked = this.allChecked;\n      }\n    });\n    let checkedOptions = this.options.filter(option => option.state.checked);\n    this.selectControl.setValue(checkedOptions);\n  }\n  onOpened() {\n    if (this.searchRef) {\n      const input = this.searchRef.nativeElement;\n      input.focus();\n    }\n    setTimeout(() => {\n      if (this.virtualScroll) {\n        if (this.selectControl.value && this.selectControl.value.length) {\n          this.virtualScroll.scrollToIndex(this.options.map(option => option.id).indexOf(this.selectControl.value[0].id));\n        } else {\n          this.virtualScroll.scrollToOffset(1);\n        }\n      }\n    }, 100);\n  }\n  onClosed() {\n    this.searchControl.reset('', {\n      emitEvent: false\n    });\n    this.options = this.data;\n    this.previousSelected = null;\n  }\n  onSelectMultiple(event, option) {\n    var _this$data;\n    event.preventDefault();\n    event.stopPropagation();\n    if (option.state) {\n      option.state.checked = !option.state.checked;\n    }\n    this.selectControl.patchValue((_this$data = this.data) === null || _this$data === void 0 ? void 0 : _this$data.filter(item => {\n      var _item$state;\n      return (_item$state = item.state) === null || _item$state === void 0 ? void 0 : _item$state.checked;\n    }));\n  }\n  onSelect(option) {\n    this.options.forEach(opt => opt.state.checked = opt.id === (option === null || option === void 0 ? void 0 : option.id));\n    this.selectControl.patchValue(coerceArray(option || []));\n  }\n  stopPropagation(event) {\n    event.stopPropagation();\n  }\n  onDisabledState(disabled) {\n    disabled ? this.triggerInputControl.disable({\n      emitEvent: false\n    }) : this.triggerInputControl.enable({\n      emitEvent: false\n    });\n  }\n  get enabledData() {\n    return this.data.filter(({\n      disabled\n    }) => !disabled);\n  }\n}, _SwuiSelectComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: TranslateService\n}], _SwuiSelectComponent.propDecorators = {\n  searchPlaceholder: [{\n    type: Input\n  }],\n  showSearch: [{\n    type: Input\n  }],\n  startSearchLength: [{\n    type: Input\n  }],\n  emptyOptionPlaceholder: [{\n    type: Input\n  }],\n  disableEmptyOption: [{\n    type: Input\n  }],\n  disableAllOption: [{\n    type: Input\n  }],\n  multiple: [{\n    type: Input\n  }],\n  data: [{\n    type: Input\n  }],\n  value: [{\n    type: Input\n  }],\n  trigger: [{\n    type: ViewChild,\n    args: ['trigger']\n  }],\n  searchRef: [{\n    type: ViewChild,\n    args: ['search']\n  }],\n  virtualScroll: [{\n    type: ViewChild,\n    args: [CdkVirtualScrollViewport, {\n      static: false\n    }]\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiSelectComponent);\nSwuiSelectComponent = __decorate([Component({\n  selector: 'lib-swui-select',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiSelectComponent\n  }],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSelectComponent);\nexport { SwuiSelectComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "FocusMonitor", "coerce<PERSON><PERSON><PERSON>", "coerceBooleanProperty", "CdkVirtualScrollViewport", "ChangeDetectionStrategy", "ChangeDetectorRef", "Component", "ElementRef", "HostBinding", "Input", "isDevMode", "Optional", "Self", "ViewChild", "UntypedFormControl", "FormGroupDirective", "NgControl", "ErrorStateMatcher", "MatFormFieldControl", "TranslateService", "ReplaySubject", "filter", "map", "take", "takeUntil", "SwuiMatFormFieldControl", "CONTROL_NAME", "nextUniqueId", "SwuiSelectComponent", "_SwuiSelectComponent", "multiple", "value", "newMultiple", "_multiple", "_contentInitialized", "Error", "itemHeight", "data", "val", "_this$options", "_data", "options", "disableAllOption", "_this$value", "allChecked", "length", "enabledData", "for<PERSON>ach", "option", "_this$value2", "state", "checked", "includes", "id", "Array", "isArray", "onDataReceived", "next", "patchSelectControl", "_this$selectControl$v", "_this$selectControl$v2", "selectControl", "v", "empty", "_this$selectControl$v3", "viewportHeight", "disableEmptyOption", "Math", "floor", "showSearch", "shouldLabelFloat", "triggerInputControl", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "cd", "translate", "searchPlaceholder", "startSearchLength", "emptyOptionPlaceholder", "controlType", "searchControl", "previousSelected", "ngOnInit", "valueChanges", "pipe", "searchString", "toLowerCase", "destroyed$", "subscribe", "search", "text", "indexOf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "some", "values", "item", "setVisibleValue", "onChange", "every", "_this$value3", "detectChanges", "ngAfterContentInit", "patchTriggerInputControl", "find", "opt", "instant", "visibleValue", "patchValue", "onContainerClick", "event", "stopPropagation", "trigger", "target", "tagName", "disabled", "nativeElement", "focus", "openMenu", "writeValue", "emitEvent", "toggleAll", "preventDefault", "checkedOptions", "setValue", "onOpened", "searchRef", "input", "setTimeout", "virtualScroll", "scrollToIndex", "scrollToOffset", "onClosed", "reset", "onSelectMultiple", "_this$data", "_item$state", "onSelect", "onDisabledState", "disable", "enable", "ctorParameters", "type", "decorators", "propDecorators", "args", "static", "selector", "template", "providers", "provide", "useExisting", "changeDetection", "OnPush", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-select/swui-select.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-select.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-select.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostBinding, Input, isDevMode, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { TranslateService } from '@ngx-translate/core';\nimport { ReplaySubject } from 'rxjs';\nimport { filter, map, take, takeUntil } from 'rxjs/operators';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nconst CONTROL_NAME = 'lib-swui-select';\nlet nextUniqueId = 0;\nlet SwuiSelectComponent = class SwuiSelectComponent extends SwuiMatFormFieldControl {\n    set multiple(value) {\n        const newMultiple = coerceBooleanProperty(value);\n        if (newMultiple !== this._multiple) {\n            if (isDevMode() && this._contentInitialized) {\n                throw new Error('Cannot change `multiple` mode of `lib-swui-select` after initialization.');\n            }\n            this._multiple = newMultiple;\n            this.itemHeight = newMultiple ? 48 : 42;\n        }\n    }\n    get multiple() {\n        return this._multiple;\n    }\n    set data(val) {\n        this._data = val || [];\n        this.options = this._data;\n        if (this.multiple && !this.disableAllOption) {\n            this.allChecked = this.value?.length === this.enabledData.length;\n        }\n        this.options?.forEach(option => option.state = {\n            checked: this.value?.includes(option.id),\n        });\n        if (Array.isArray(this.options) && this.options.length) {\n            this.onDataReceived.next(this.options);\n        }\n    }\n    get data() {\n        return this._data;\n    }\n    set value(val) {\n        this.patchSelectControl(val);\n    }\n    get value() {\n        return this.multiple ?\n            this.selectControl.value?.map((v) => v.id) :\n            this.selectControl.value && this.selectControl.value[0]?.id;\n    }\n    get empty() {\n        return !this.selectControl.value?.length;\n    }\n    get viewportHeight() {\n        let length = this.data.length;\n        if (!this.multiple && !this.disableEmptyOption) {\n            length += 1;\n        }\n        length = Math.floor(length / 5) && 5 || length % 5;\n        if (length === 5) {\n            if (this.showSearch) {\n                length -= 1;\n            }\n            if (this.multiple) {\n                length -= 1;\n            }\n        }\n        return length * this.itemHeight;\n    }\n    get shouldLabelFloat() {\n        return this.triggerInputControl.value;\n    }\n    constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, cd, translate) {\n        super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n        this.cd = cd;\n        this.translate = translate;\n        this.searchPlaceholder = 'Search';\n        this.showSearch = false;\n        this.startSearchLength = 0;\n        this.emptyOptionPlaceholder = 'None';\n        this.disableEmptyOption = false;\n        this.disableAllOption = false;\n        this.allChecked = false;\n        this.options = [];\n        this.itemHeight = 48;\n        this.onDataReceived = new ReplaySubject(1);\n        this.controlType = CONTROL_NAME;\n        this.triggerInputControl = new UntypedFormControl();\n        this.searchControl = new UntypedFormControl();\n        this.selectControl = new UntypedFormControl();\n        this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n        this._contentInitialized = false;\n        this._multiple = false;\n        this._data = [];\n        this.previousSelected = null;\n    }\n    ngOnInit() {\n        this.searchControl.valueChanges.pipe(filter(data => this.showSearch && (data.length >= (this.startSearchLength || 0) || data.length === 0)), map(searchString => searchString.toLowerCase()), takeUntil(this.destroyed$)).subscribe(search => {\n            this.previousSelected = this.multiple && search ? this.selectControl.value : null;\n            this.options = this.data.filter(option => {\n                return option.text && option.text.toLowerCase().indexOf(search) > -1;\n            });\n            this.cd.markForCheck();\n        });\n        this.selectControl.valueChanges.pipe(map(val => {\n            if (this.multiple && Array.isArray(this.previousSelected) && Array.isArray(val)) {\n                const previousSelected = this.previousSelected.filter(id => !this.options.some(option => option.id === id));\n                const values = this.enabledData.filter(({ id }) => previousSelected.some(item => item === id));\n                return [...values, ...val];\n            }\n            return val;\n        }), map(val => val.map(item => item ? item.id : '')), takeUntil(this.destroyed$)).subscribe(val => {\n            this.setVisibleValue(val);\n            this.onChange(this.multiple ? val : val[0] || null);\n            if (this.multiple && !this.disableAllOption) {\n                this.allChecked = this.enabledData.every(({ id }) => this.value?.includes(id));\n            }\n            this.cd.detectChanges();\n        });\n    }\n    ngAfterContentInit() {\n        this._contentInitialized = true;\n    }\n    setVisibleValue(val, options) {\n        if (options) {\n            this.patchTriggerInputControl(val, options);\n        }\n        else {\n            this.onDataReceived\n                .pipe(take(1))\n                .subscribe(data => {\n                this.patchTriggerInputControl(val, data);\n            });\n        }\n    }\n    patchTriggerInputControl(val, options) {\n        let option = options?.find(opt => opt.id === val[0]);\n        let text = option ?\n            this.translate.instant(option.text) :\n            val[0];\n        let visibleValue = val?.length > 1 ?\n            `${text} (+${val.length - 1} ${this.translate.instant('ALL.more')})` :\n            text;\n        this.triggerInputControl.patchValue(visibleValue);\n    }\n    onContainerClick(event) {\n        event.stopPropagation();\n        if (this.elRef && this.trigger && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n            this.elRef.nativeElement.focus();\n            this.trigger.openMenu();\n        }\n    }\n    writeValue(val) {\n        this.patchSelectControl(val);\n    }\n    patchSelectControl(val) {\n        this.onDataReceived\n            .pipe(take(1))\n            .subscribe(options => {\n            if (this.multiple && !this.disableAllOption) {\n                this.allChecked = Array.isArray(val) && val.length === this.enabledData.length;\n            }\n            const values = coerceArray(val || []);\n            options?.forEach(option => option.state = {\n                checked: values?.includes(option.id),\n            });\n            const value = options.filter(opt => values.includes(opt.id));\n            this.selectControl.patchValue(value, { emitEvent: false });\n            this.setVisibleValue(values, options);\n            this.cd.detectChanges();\n        });\n    }\n    toggleAll(event) {\n        event?.preventDefault();\n        event?.stopPropagation();\n        this.allChecked = !this.allChecked;\n        this.options.forEach(option => {\n            if (!option.disabled) {\n                option.state.checked = this.allChecked;\n            }\n        });\n        let checkedOptions = this.options.filter(option => option.state.checked);\n        this.selectControl.setValue(checkedOptions);\n    }\n    onOpened() {\n        if (this.searchRef) {\n            const input = this.searchRef.nativeElement;\n            input.focus();\n        }\n        setTimeout(() => {\n            if (this.virtualScroll) {\n                if (this.selectControl.value && this.selectControl.value.length) {\n                    this.virtualScroll.scrollToIndex(this.options.map(option => option.id).indexOf(this.selectControl.value[0].id));\n                }\n                else {\n                    this.virtualScroll.scrollToOffset(1);\n                }\n            }\n        }, 100);\n    }\n    onClosed() {\n        this.searchControl.reset('', { emitEvent: false });\n        this.options = this.data;\n        this.previousSelected = null;\n    }\n    onSelectMultiple(event, option) {\n        event.preventDefault();\n        event.stopPropagation();\n        if (option.state) {\n            option.state.checked = !option.state.checked;\n        }\n        this.selectControl.patchValue(this.data?.filter(item => item.state?.checked));\n    }\n    onSelect(option) {\n        this.options.forEach(opt => opt.state.checked = opt.id === option?.id);\n        this.selectControl.patchValue(coerceArray(option || []));\n    }\n    stopPropagation(event) {\n        event.stopPropagation();\n    }\n    onDisabledState(disabled) {\n        disabled ?\n            this.triggerInputControl.disable({ emitEvent: false }) :\n            this.triggerInputControl.enable({ emitEvent: false });\n    }\n    get enabledData() {\n        return this.data.filter(({ disabled }) => !disabled);\n    }\n    static { this.ctorParameters = () => [\n        { type: FocusMonitor },\n        { type: ElementRef },\n        { type: NgControl, decorators: [{ type: Optional }, { type: Self }] },\n        { type: FormGroupDirective, decorators: [{ type: Optional }] },\n        { type: ErrorStateMatcher },\n        { type: ChangeDetectorRef },\n        { type: TranslateService }\n    ]; }\n    static { this.propDecorators = {\n        searchPlaceholder: [{ type: Input }],\n        showSearch: [{ type: Input }],\n        startSearchLength: [{ type: Input }],\n        emptyOptionPlaceholder: [{ type: Input }],\n        disableEmptyOption: [{ type: Input }],\n        disableAllOption: [{ type: Input }],\n        multiple: [{ type: Input }],\n        data: [{ type: Input }],\n        value: [{ type: Input }],\n        trigger: [{ type: ViewChild, args: ['trigger',] }],\n        searchRef: [{ type: ViewChild, args: ['search',] }],\n        virtualScroll: [{ type: ViewChild, args: [CdkVirtualScrollViewport, { static: false },] }],\n        id: [{ type: HostBinding }],\n        shouldLabelFloat: [{ type: HostBinding, args: ['class.floating',] }]\n    }; }\n};\nSwuiSelectComponent = __decorate([\n    Component({\n        selector: 'lib-swui-select',\n        template: __NG_CLI_RESOURCE__0,\n        providers: [{ provide: MatFormFieldControl, useExisting: SwuiSelectComponent }],\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiSelectComponent);\nexport { SwuiSelectComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,WAAW,EAAEC,qBAAqB,QAAQ,uBAAuB;AAC1E,SAASC,wBAAwB,QAAQ,wBAAwB;AACjE,SAASC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AAC3J,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAClF,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,aAAa,QAAQ,MAAM;AACpC,SAASC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAC7D,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,MAAMC,YAAY,GAAG,iBAAiB;AACtC,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,mBAAmB,IAAAC,oBAAA,GAAG,MAAMD,mBAAmB,SAASH,uBAAuB,CAAC;EAChF,IAAIK,QAAQA,CAACC,KAAK,EAAE;IAChB,MAAMC,WAAW,GAAG9B,qBAAqB,CAAC6B,KAAK,CAAC;IAChD,IAAIC,WAAW,KAAK,IAAI,CAACC,SAAS,EAAE;MAChC,IAAIvB,SAAS,CAAC,CAAC,IAAI,IAAI,CAACwB,mBAAmB,EAAE;QACzC,MAAM,IAAIC,KAAK,CAAC,0EAA0E,CAAC;MAC/F;MACA,IAAI,CAACF,SAAS,GAAGD,WAAW;MAC5B,IAAI,CAACI,UAAU,GAAGJ,WAAW,GAAG,EAAE,GAAG,EAAE;IAC3C;EACJ;EACA,IAAIF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACG,SAAS;EACzB;EACA,IAAII,IAAIA,CAACC,GAAG,EAAE;IAAA,IAAAC,aAAA;IACV,IAAI,CAACC,KAAK,GAAGF,GAAG,IAAI,EAAE;IACtB,IAAI,CAACG,OAAO,GAAG,IAAI,CAACD,KAAK;IACzB,IAAI,IAAI,CAACV,QAAQ,IAAI,CAAC,IAAI,CAACY,gBAAgB,EAAE;MAAA,IAAAC,WAAA;MACzC,IAAI,CAACC,UAAU,GAAG,EAAAD,WAAA,OAAI,CAACZ,KAAK,cAAAY,WAAA,uBAAVA,WAAA,CAAYE,MAAM,MAAK,IAAI,CAACC,WAAW,CAACD,MAAM;IACpE;IACA,CAAAN,aAAA,OAAI,CAACE,OAAO,cAAAF,aAAA,eAAZA,aAAA,CAAcQ,OAAO,CAACC,MAAM;MAAA,IAAAC,YAAA;MAAA,OAAID,MAAM,CAACE,KAAK,GAAG;QAC3CC,OAAO,GAAAF,YAAA,GAAE,IAAI,CAAClB,KAAK,cAAAkB,YAAA,uBAAVA,YAAA,CAAYG,QAAQ,CAACJ,MAAM,CAACK,EAAE;MAC3C,CAAC;IAAA,EAAC;IACF,IAAIC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACd,OAAO,CAAC,IAAI,IAAI,CAACA,OAAO,CAACI,MAAM,EAAE;MACpD,IAAI,CAACW,cAAc,CAACC,IAAI,CAAC,IAAI,CAAChB,OAAO,CAAC;IAC1C;EACJ;EACA,IAAIJ,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACG,KAAK;EACrB;EACA,IAAIT,KAAKA,CAACO,GAAG,EAAE;IACX,IAAI,CAACoB,kBAAkB,CAACpB,GAAG,CAAC;EAChC;EACA,IAAIP,KAAKA,CAAA,EAAG;IAAA,IAAA4B,qBAAA,EAAAC,sBAAA;IACR,OAAO,IAAI,CAAC9B,QAAQ,IAAA6B,qBAAA,GAChB,IAAI,CAACE,aAAa,CAAC9B,KAAK,cAAA4B,qBAAA,uBAAxBA,qBAAA,CAA0BrC,GAAG,CAAEwC,CAAC,IAAKA,CAAC,CAACT,EAAE,CAAC,GAC1C,IAAI,CAACQ,aAAa,CAAC9B,KAAK,MAAA6B,sBAAA,GAAI,IAAI,CAACC,aAAa,CAAC9B,KAAK,CAAC,CAAC,CAAC,cAAA6B,sBAAA,uBAA3BA,sBAAA,CAA6BP,EAAE;EACnE;EACA,IAAIU,KAAKA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACR,OAAO,GAAAA,sBAAA,GAAC,IAAI,CAACH,aAAa,CAAC9B,KAAK,cAAAiC,sBAAA,eAAxBA,sBAAA,CAA0BnB,MAAM;EAC5C;EACA,IAAIoB,cAAcA,CAAA,EAAG;IACjB,IAAIpB,MAAM,GAAG,IAAI,CAACR,IAAI,CAACQ,MAAM;IAC7B,IAAI,CAAC,IAAI,CAACf,QAAQ,IAAI,CAAC,IAAI,CAACoC,kBAAkB,EAAE;MAC5CrB,MAAM,IAAI,CAAC;IACf;IACAA,MAAM,GAAGsB,IAAI,CAACC,KAAK,CAACvB,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,IAAIA,MAAM,GAAG,CAAC;IAClD,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd,IAAI,IAAI,CAACwB,UAAU,EAAE;QACjBxB,MAAM,IAAI,CAAC;MACf;MACA,IAAI,IAAI,CAACf,QAAQ,EAAE;QACfe,MAAM,IAAI,CAAC;MACf;IACJ;IACA,OAAOA,MAAM,GAAG,IAAI,CAACT,UAAU;EACnC;EACA,IAAIkC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,mBAAmB,CAACxC,KAAK;EACzC;EACAyC,WAAWA,CAACC,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,EAAE,EAAEC,SAAS,EAAE;IACjF,KAAK,CAACN,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAC/D,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,iBAAiB,GAAG,QAAQ;IACjC,IAAI,CAACX,UAAU,GAAG,KAAK;IACvB,IAAI,CAACY,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,sBAAsB,GAAG,MAAM;IACpC,IAAI,CAAChB,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACxB,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACH,OAAO,GAAG,EAAE;IACjB,IAAI,CAACL,UAAU,GAAG,EAAE;IACpB,IAAI,CAACoB,cAAc,GAAG,IAAIpC,aAAa,CAAC,CAAC,CAAC;IAC1C,IAAI,CAAC+D,WAAW,GAAGzD,YAAY;IAC/B,IAAI,CAAC6C,mBAAmB,GAAG,IAAIzD,kBAAkB,CAAC,CAAC;IACnD,IAAI,CAACsE,aAAa,GAAG,IAAItE,kBAAkB,CAAC,CAAC;IAC7C,IAAI,CAAC+C,aAAa,GAAG,IAAI/C,kBAAkB,CAAC,CAAC;IAC7C,IAAI,CAACuC,EAAE,GAAG,GAAG3B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAC7C,IAAI,CAACO,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACO,KAAK,GAAG,EAAE;IACf,IAAI,CAAC6C,gBAAgB,GAAG,IAAI;EAChC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,aAAa,CAACG,YAAY,CAACC,IAAI,CAACnE,MAAM,CAACgB,IAAI,IAAI,IAAI,CAACgC,UAAU,KAAKhC,IAAI,CAACQ,MAAM,KAAK,IAAI,CAACoC,iBAAiB,IAAI,CAAC,CAAC,IAAI5C,IAAI,CAACQ,MAAM,KAAK,CAAC,CAAC,CAAC,EAAEvB,GAAG,CAACmE,YAAY,IAAIA,YAAY,CAACC,WAAW,CAAC,CAAC,CAAC,EAAElE,SAAS,CAAC,IAAI,CAACmE,UAAU,CAAC,CAAC,CAACC,SAAS,CAACC,MAAM,IAAI;MAC1O,IAAI,CAACR,gBAAgB,GAAG,IAAI,CAACvD,QAAQ,IAAI+D,MAAM,GAAG,IAAI,CAAChC,aAAa,CAAC9B,KAAK,GAAG,IAAI;MACjF,IAAI,CAACU,OAAO,GAAG,IAAI,CAACJ,IAAI,CAAChB,MAAM,CAAC2B,MAAM,IAAI;QACtC,OAAOA,MAAM,CAAC8C,IAAI,IAAI9C,MAAM,CAAC8C,IAAI,CAACJ,WAAW,CAAC,CAAC,CAACK,OAAO,CAACF,MAAM,CAAC,GAAG,CAAC,CAAC;MACxE,CAAC,CAAC;MACF,IAAI,CAACf,EAAE,CAACkB,YAAY,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF,IAAI,CAACnC,aAAa,CAAC0B,YAAY,CAACC,IAAI,CAAClE,GAAG,CAACgB,GAAG,IAAI;MAC5C,IAAI,IAAI,CAACR,QAAQ,IAAIwB,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC8B,gBAAgB,CAAC,IAAI/B,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,EAAE;QAC7E,MAAM+C,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAChE,MAAM,CAACgC,EAAE,IAAI,CAAC,IAAI,CAACZ,OAAO,CAACwD,IAAI,CAACjD,MAAM,IAAIA,MAAM,CAACK,EAAE,KAAKA,EAAE,CAAC,CAAC;QAC3G,MAAM6C,MAAM,GAAG,IAAI,CAACpD,WAAW,CAACzB,MAAM,CAAC,CAAC;UAAEgC;QAAG,CAAC,KAAKgC,gBAAgB,CAACY,IAAI,CAACE,IAAI,IAAIA,IAAI,KAAK9C,EAAE,CAAC,CAAC;QAC9F,OAAO,CAAC,GAAG6C,MAAM,EAAE,GAAG5D,GAAG,CAAC;MAC9B;MACA,OAAOA,GAAG;IACd,CAAC,CAAC,EAAEhB,GAAG,CAACgB,GAAG,IAAIA,GAAG,CAAChB,GAAG,CAAC6E,IAAI,IAAIA,IAAI,GAAGA,IAAI,CAAC9C,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE7B,SAAS,CAAC,IAAI,CAACmE,UAAU,CAAC,CAAC,CAACC,SAAS,CAACtD,GAAG,IAAI;MAC/F,IAAI,CAAC8D,eAAe,CAAC9D,GAAG,CAAC;MACzB,IAAI,CAAC+D,QAAQ,CAAC,IAAI,CAACvE,QAAQ,GAAGQ,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;MACnD,IAAI,IAAI,CAACR,QAAQ,IAAI,CAAC,IAAI,CAACY,gBAAgB,EAAE;QACzC,IAAI,CAACE,UAAU,GAAG,IAAI,CAACE,WAAW,CAACwD,KAAK,CAAC,CAAC;UAAEjD;QAAG,CAAC;UAAA,IAAAkD,YAAA;UAAA,QAAAA,YAAA,GAAK,IAAI,CAACxE,KAAK,cAAAwE,YAAA,uBAAVA,YAAA,CAAYnD,QAAQ,CAACC,EAAE,CAAC;QAAA,EAAC;MAClF;MACA,IAAI,CAACyB,EAAE,CAAC0B,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACvE,mBAAmB,GAAG,IAAI;EACnC;EACAkE,eAAeA,CAAC9D,GAAG,EAAEG,OAAO,EAAE;IAC1B,IAAIA,OAAO,EAAE;MACT,IAAI,CAACiE,wBAAwB,CAACpE,GAAG,EAAEG,OAAO,CAAC;IAC/C,CAAC,MACI;MACD,IAAI,CAACe,cAAc,CACdgC,IAAI,CAACjE,IAAI,CAAC,CAAC,CAAC,CAAC,CACbqE,SAAS,CAACvD,IAAI,IAAI;QACnB,IAAI,CAACqE,wBAAwB,CAACpE,GAAG,EAAED,IAAI,CAAC;MAC5C,CAAC,CAAC;IACN;EACJ;EACAqE,wBAAwBA,CAACpE,GAAG,EAAEG,OAAO,EAAE;IACnC,IAAIO,MAAM,GAAGP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACvD,EAAE,KAAKf,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,IAAIwD,IAAI,GAAG9C,MAAM,GACb,IAAI,CAAC+B,SAAS,CAAC8B,OAAO,CAAC7D,MAAM,CAAC8C,IAAI,CAAC,GACnCxD,GAAG,CAAC,CAAC,CAAC;IACV,IAAIwE,YAAY,GAAG,CAAAxE,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEO,MAAM,IAAG,CAAC,GAC9B,GAAGiD,IAAI,MAAMxD,GAAG,CAACO,MAAM,GAAG,CAAC,IAAI,IAAI,CAACkC,SAAS,CAAC8B,OAAO,CAAC,UAAU,CAAC,GAAG,GACpEf,IAAI;IACR,IAAI,CAACvB,mBAAmB,CAACwC,UAAU,CAACD,YAAY,CAAC;EACrD;EACAE,gBAAgBA,CAACC,KAAK,EAAE;IACpBA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACxC,KAAK,IAAI,IAAI,CAACyC,OAAO,IAAIF,KAAK,CAACG,MAAM,CAACC,OAAO,CAAC3B,WAAW,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC4B,QAAQ,EAAE;MAChG,IAAI,CAAC5C,KAAK,CAAC6C,aAAa,CAACC,KAAK,CAAC,CAAC;MAChC,IAAI,CAACL,OAAO,CAACM,QAAQ,CAAC,CAAC;IAC3B;EACJ;EACAC,UAAUA,CAACpF,GAAG,EAAE;IACZ,IAAI,CAACoB,kBAAkB,CAACpB,GAAG,CAAC;EAChC;EACAoB,kBAAkBA,CAACpB,GAAG,EAAE;IACpB,IAAI,CAACkB,cAAc,CACdgC,IAAI,CAACjE,IAAI,CAAC,CAAC,CAAC,CAAC,CACbqE,SAAS,CAACnD,OAAO,IAAI;MACtB,IAAI,IAAI,CAACX,QAAQ,IAAI,CAAC,IAAI,CAACY,gBAAgB,EAAE;QACzC,IAAI,CAACE,UAAU,GAAGU,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,IAAIA,GAAG,CAACO,MAAM,KAAK,IAAI,CAACC,WAAW,CAACD,MAAM;MAClF;MACA,MAAMqD,MAAM,GAAGjG,WAAW,CAACqC,GAAG,IAAI,EAAE,CAAC;MACrCG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEM,OAAO,CAACC,MAAM,IAAIA,MAAM,CAACE,KAAK,GAAG;QACtCC,OAAO,EAAE+C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE9C,QAAQ,CAACJ,MAAM,CAACK,EAAE;MACvC,CAAC,CAAC;MACF,MAAMtB,KAAK,GAAGU,OAAO,CAACpB,MAAM,CAACuF,GAAG,IAAIV,MAAM,CAAC9C,QAAQ,CAACwD,GAAG,CAACvD,EAAE,CAAC,CAAC;MAC5D,IAAI,CAACQ,aAAa,CAACkD,UAAU,CAAChF,KAAK,EAAE;QAAE4F,SAAS,EAAE;MAAM,CAAC,CAAC;MAC1D,IAAI,CAACvB,eAAe,CAACF,MAAM,EAAEzD,OAAO,CAAC;MACrC,IAAI,CAACqC,EAAE,CAAC0B,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAoB,SAASA,CAACX,KAAK,EAAE;IACbA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEY,cAAc,CAAC,CAAC;IACvBZ,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,eAAe,CAAC,CAAC;IACxB,IAAI,CAACtE,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,OAAO,CAACM,OAAO,CAACC,MAAM,IAAI;MAC3B,IAAI,CAACA,MAAM,CAACsE,QAAQ,EAAE;QAClBtE,MAAM,CAACE,KAAK,CAACC,OAAO,GAAG,IAAI,CAACP,UAAU;MAC1C;IACJ,CAAC,CAAC;IACF,IAAIkF,cAAc,GAAG,IAAI,CAACrF,OAAO,CAACpB,MAAM,CAAC2B,MAAM,IAAIA,MAAM,CAACE,KAAK,CAACC,OAAO,CAAC;IACxE,IAAI,CAACU,aAAa,CAACkE,QAAQ,CAACD,cAAc,CAAC;EAC/C;EACAE,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,SAAS,EAAE;MAChB,MAAMC,KAAK,GAAG,IAAI,CAACD,SAAS,CAACV,aAAa;MAC1CW,KAAK,CAACV,KAAK,CAAC,CAAC;IACjB;IACAW,UAAU,CAAC,MAAM;MACb,IAAI,IAAI,CAACC,aAAa,EAAE;QACpB,IAAI,IAAI,CAACvE,aAAa,CAAC9B,KAAK,IAAI,IAAI,CAAC8B,aAAa,CAAC9B,KAAK,CAACc,MAAM,EAAE;UAC7D,IAAI,CAACuF,aAAa,CAACC,aAAa,CAAC,IAAI,CAAC5F,OAAO,CAACnB,GAAG,CAAC0B,MAAM,IAAIA,MAAM,CAACK,EAAE,CAAC,CAAC0C,OAAO,CAAC,IAAI,CAAClC,aAAa,CAAC9B,KAAK,CAAC,CAAC,CAAC,CAACsB,EAAE,CAAC,CAAC;QACnH,CAAC,MACI;UACD,IAAI,CAAC+E,aAAa,CAACE,cAAc,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ,CAAC,EAAE,GAAG,CAAC;EACX;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACnD,aAAa,CAACoD,KAAK,CAAC,EAAE,EAAE;MAAEb,SAAS,EAAE;IAAM,CAAC,CAAC;IAClD,IAAI,CAAClF,OAAO,GAAG,IAAI,CAACJ,IAAI;IACxB,IAAI,CAACgD,gBAAgB,GAAG,IAAI;EAChC;EACAoD,gBAAgBA,CAACxB,KAAK,EAAEjE,MAAM,EAAE;IAAA,IAAA0F,UAAA;IAC5BzB,KAAK,CAACY,cAAc,CAAC,CAAC;IACtBZ,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAIlE,MAAM,CAACE,KAAK,EAAE;MACdF,MAAM,CAACE,KAAK,CAACC,OAAO,GAAG,CAACH,MAAM,CAACE,KAAK,CAACC,OAAO;IAChD;IACA,IAAI,CAACU,aAAa,CAACkD,UAAU,EAAA2B,UAAA,GAAC,IAAI,CAACrG,IAAI,cAAAqG,UAAA,uBAATA,UAAA,CAAWrH,MAAM,CAAC8E,IAAI;MAAA,IAAAwC,WAAA;MAAA,QAAAA,WAAA,GAAIxC,IAAI,CAACjD,KAAK,cAAAyF,WAAA,uBAAVA,WAAA,CAAYxF,OAAO;IAAA,EAAC,CAAC;EACjF;EACAyF,QAAQA,CAAC5F,MAAM,EAAE;IACb,IAAI,CAACP,OAAO,CAACM,OAAO,CAAC6D,GAAG,IAAIA,GAAG,CAAC1D,KAAK,CAACC,OAAO,GAAGyD,GAAG,CAACvD,EAAE,MAAKL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,EAAE,EAAC;IACtE,IAAI,CAACQ,aAAa,CAACkD,UAAU,CAAC9G,WAAW,CAAC+C,MAAM,IAAI,EAAE,CAAC,CAAC;EAC5D;EACAkE,eAAeA,CAACD,KAAK,EAAE;IACnBA,KAAK,CAACC,eAAe,CAAC,CAAC;EAC3B;EACA2B,eAAeA,CAACvB,QAAQ,EAAE;IACtBA,QAAQ,GACJ,IAAI,CAAC/C,mBAAmB,CAACuE,OAAO,CAAC;MAAEnB,SAAS,EAAE;IAAM,CAAC,CAAC,GACtD,IAAI,CAACpD,mBAAmB,CAACwE,MAAM,CAAC;MAAEpB,SAAS,EAAE;IAAM,CAAC,CAAC;EAC7D;EACA,IAAI7E,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACT,IAAI,CAAChB,MAAM,CAAC,CAAC;MAAEiG;IAAS,CAAC,KAAK,CAACA,QAAQ,CAAC;EACxD;AA0BJ,CAAC,EAzBYzF,oBAAA,CAAKmH,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEjJ;AAAa,CAAC,EACtB;EAAEiJ,IAAI,EAAE1I;AAAW,CAAC,EACpB;EAAE0I,IAAI,EAAEjI,SAAS;EAAEkI,UAAU,EAAE,CAAC;IAAED,IAAI,EAAEtI;EAAS,CAAC,EAAE;IAAEsI,IAAI,EAAErI;EAAK,CAAC;AAAE,CAAC,EACrE;EAAEqI,IAAI,EAAElI,kBAAkB;EAAEmI,UAAU,EAAE,CAAC;IAAED,IAAI,EAAEtI;EAAS,CAAC;AAAE,CAAC,EAC9D;EAAEsI,IAAI,EAAEhI;AAAkB,CAAC,EAC3B;EAAEgI,IAAI,EAAE5I;AAAkB,CAAC,EAC3B;EAAE4I,IAAI,EAAE9H;AAAiB,CAAC,CAC7B,EACQU,oBAAA,CAAKsH,cAAc,GAAG;EAC3BnE,iBAAiB,EAAE,CAAC;IAAEiE,IAAI,EAAExI;EAAM,CAAC,CAAC;EACpC4D,UAAU,EAAE,CAAC;IAAE4E,IAAI,EAAExI;EAAM,CAAC,CAAC;EAC7BwE,iBAAiB,EAAE,CAAC;IAAEgE,IAAI,EAAExI;EAAM,CAAC,CAAC;EACpCyE,sBAAsB,EAAE,CAAC;IAAE+D,IAAI,EAAExI;EAAM,CAAC,CAAC;EACzCyD,kBAAkB,EAAE,CAAC;IAAE+E,IAAI,EAAExI;EAAM,CAAC,CAAC;EACrCiC,gBAAgB,EAAE,CAAC;IAAEuG,IAAI,EAAExI;EAAM,CAAC,CAAC;EACnCqB,QAAQ,EAAE,CAAC;IAAEmH,IAAI,EAAExI;EAAM,CAAC,CAAC;EAC3B4B,IAAI,EAAE,CAAC;IAAE4G,IAAI,EAAExI;EAAM,CAAC,CAAC;EACvBsB,KAAK,EAAE,CAAC;IAAEkH,IAAI,EAAExI;EAAM,CAAC,CAAC;EACxB0G,OAAO,EAAE,CAAC;IAAE8B,IAAI,EAAEpI,SAAS;IAAEuI,IAAI,EAAE,CAAC,SAAS;EAAG,CAAC,CAAC;EAClDnB,SAAS,EAAE,CAAC;IAAEgB,IAAI,EAAEpI,SAAS;IAAEuI,IAAI,EAAE,CAAC,QAAQ;EAAG,CAAC,CAAC;EACnDhB,aAAa,EAAE,CAAC;IAAEa,IAAI,EAAEpI,SAAS;IAAEuI,IAAI,EAAE,CAACjJ,wBAAwB,EAAE;MAAEkJ,MAAM,EAAE;IAAM,CAAC;EAAG,CAAC,CAAC;EAC1FhG,EAAE,EAAE,CAAC;IAAE4F,IAAI,EAAEzI;EAAY,CAAC,CAAC;EAC3B8D,gBAAgB,EAAE,CAAC;IAAE2E,IAAI,EAAEzI,WAAW;IAAE4I,IAAI,EAAE,CAAC,gBAAgB;EAAG,CAAC;AACvE,CAAC,EAAAvH,oBAAA,CACJ;AACDD,mBAAmB,GAAG/B,UAAU,CAAC,CAC7BS,SAAS,CAAC;EACNgJ,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAEzJ,oBAAoB;EAC9B0J,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAEvI,mBAAmB;IAAEwI,WAAW,EAAE9H;EAAoB,CAAC,CAAC;EAC/E+H,eAAe,EAAEvJ,uBAAuB,CAACwJ,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC/J,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAE6B,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}