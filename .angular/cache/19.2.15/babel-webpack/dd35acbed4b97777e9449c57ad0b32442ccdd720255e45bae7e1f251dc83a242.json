{"ast": null, "code": "var _SwuiTdClickWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./click.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./click.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdClickWidgetComponent = (_SwuiTdClickWidgetComponent = class SwuiTdClickWidgetComponent {\n  constructor(config) {\n    var _schema$td, _schema$td2, _schema$td3;\n    this.config = config;\n    const {\n      row,\n      schema,\n      value,\n      action\n    } = config;\n    const titleFn = (_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.titleFn;\n    const isDisabled = (_schema$td2 = schema.td) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.isDisabled;\n    if (schema.td && 'truncate' in schema.td) {\n      this.truncate = schema.td.truncate;\n    }\n    this.useTranslate = schema.td && 'useTranslate' in schema.td ? ((_schema$td3 = schema.td) === null || _schema$td3 === void 0 ? void 0 : _schema$td3.useTranslate) || false : true;\n    this.title = titleFn && titleFn(row, schema) || value;\n    this.isDisabled = isDisabled && isDisabled(row);\n    this.action = action;\n  }\n  onClick(event) {\n    event.preventDefault();\n    if (this.action) {\n      const {\n        field,\n        row\n      } = this.config;\n      this.action.emit({\n        row,\n        field\n      });\n    }\n  }\n}, _SwuiTdClickWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdClickWidgetComponent);\nSwuiTdClickWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-click-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdClickWidgetComponent);\nexport { SwuiTdClickWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdClickWidgetComponent", "_SwuiTdClickWidgetComponent", "constructor", "config", "_schema$td", "_schema$td2", "_schema$td3", "row", "schema", "value", "action", "titleFn", "td", "isDisabled", "truncate", "useTranslate", "title", "onClick", "event", "preventDefault", "field", "emit", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/click/click.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./click.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./click.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdClickWidgetComponent = class SwuiTdClickWidgetComponent {\n    constructor(config) {\n        this.config = config;\n        const { row, schema, value, action } = config;\n        const titleFn = schema.td?.titleFn;\n        const isDisabled = schema.td?.isDisabled;\n        if (schema.td && 'truncate' in schema.td) {\n            this.truncate = schema.td.truncate;\n        }\n        this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;\n        this.title = (titleFn && titleFn(row, schema)) || value;\n        this.isDisabled = isDisabled && isDisabled(row);\n        this.action = action;\n    }\n    onClick(event) {\n        event.preventDefault();\n        if (this.action) {\n            const { field, row } = this.config;\n            this.action.emit({ row, field });\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdClickWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-click-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdClickWidgetComponent);\nexport { SwuiTdClickWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,0BAA0B,IAAAC,2BAAA,GAAG,MAAMD,0BAA0B,CAAC;EAC9DE,WAAWA,CAACC,MAAM,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA;IAChB,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,MAAM;MAAEI,GAAG;MAAEC,MAAM;MAAEC,KAAK;MAAEC;IAAO,CAAC,GAAGP,MAAM;IAC7C,MAAMQ,OAAO,IAAAP,UAAA,GAAGI,MAAM,CAACI,EAAE,cAAAR,UAAA,uBAATA,UAAA,CAAWO,OAAO;IAClC,MAAME,UAAU,IAAAR,WAAA,GAAGG,MAAM,CAACI,EAAE,cAAAP,WAAA,uBAATA,WAAA,CAAWQ,UAAU;IACxC,IAAIL,MAAM,CAACI,EAAE,IAAI,UAAU,IAAIJ,MAAM,CAACI,EAAE,EAAE;MACtC,IAAI,CAACE,QAAQ,GAAGN,MAAM,CAACI,EAAE,CAACE,QAAQ;IACtC;IACA,IAAI,CAACC,YAAY,GAAGP,MAAM,CAACI,EAAE,IAAI,cAAc,IAAIJ,MAAM,CAACI,EAAE,GAAG,EAAAN,WAAA,GAAAE,MAAM,CAACI,EAAE,cAAAN,WAAA,uBAATA,WAAA,CAAWS,YAAY,KAAI,KAAK,GAAG,IAAI;IACtG,IAAI,CAACC,KAAK,GAAIL,OAAO,IAAIA,OAAO,CAACJ,GAAG,EAAEC,MAAM,CAAC,IAAKC,KAAK;IACvD,IAAI,CAACI,UAAU,GAAGA,UAAU,IAAIA,UAAU,CAACN,GAAG,CAAC;IAC/C,IAAI,CAACG,MAAM,GAAGA,MAAM;EACxB;EACAO,OAAOA,CAACC,KAAK,EAAE;IACXA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,IAAI,CAACT,MAAM,EAAE;MACb,MAAM;QAAEU,KAAK;QAAEb;MAAI,CAAC,GAAG,IAAI,CAACJ,MAAM;MAClC,IAAI,CAACO,MAAM,CAACW,IAAI,CAAC;QAAEd,GAAG;QAAEa;MAAM,CAAC,CAAC;IACpC;EACJ;AAIJ,CAAC,EAHYnB,2BAAA,CAAKqB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEzB,MAAM;IAAE4B,IAAI,EAAE,CAAC3B,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,2BAAA,CACJ;AACDD,0BAA0B,GAAGN,UAAU,CAAC,CACpCG,SAAS,CAAC;EACN8B,QAAQ,EAAE,0BAA0B;EACpCC,QAAQ,EAAEjC,oBAAoB;EAC9BkC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAClC,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEI,0BAA0B,CAAC;AAC9B,SAASA,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}