{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageSelectorComponent } from './language-selector.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\nlet LanguageSelectorModule = class LanguageSelectorModule {};\nLanguageSelectorModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, MatRippleModule, MatIconModule, MatMenuModule],\n  declarations: [LanguageSelectorComponent],\n  exports: [LanguageSelectorComponent]\n})], LanguageSelectorModule);\nexport { LanguageSelectorModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "TranslateModule", "LanguageSelectorComponent", "MatMenuModule", "MatIconModule", "MatRippleModule", "LanguageSelectorModule", "__decorate", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/language-selector/language-selector.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageSelectorComponent } from './language-selector.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    MatRippleModule,\n    MatIconModule,\n    MatMenuModule,\n  ],\n  declarations: [\n    LanguageSelectorComponent,\n  ],\n  exports: [\n    LanguageSelectorComponent,\n  ],\n})\nexport class LanguageSelectorModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AAkBjD,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB,GAClC;AADYA,sBAAsB,GAAAC,UAAA,EAflCR,QAAQ,CAAC;EACRS,OAAO,EAAE,CACPR,YAAY,EACZC,eAAe,EACfI,eAAe,EACfD,aAAa,EACbD,aAAa,CACd;EACDM,YAAY,EAAE,CACZP,yBAAyB,CAC1B;EACDQ,OAAO,EAAE,CACPR,yBAAyB;CAE5B,CAAC,C,EACWI,sBAAsB,CAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}