{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatInputModule } from '@angular/material/input';\nimport { SwuiStartTimeComponent } from './swui-start-time.component';\nexport const START_TIME_MODULES = [ReactiveFormsModule, MatInputModule];\nlet SwuiStartTimeModule = class SwuiStartTimeModule {};\nSwuiStartTimeModule = __decorate([NgModule({\n  declarations: [SwuiStartTimeComponent],\n  exports: [SwuiStartTimeComponent],\n  imports: [CommonModule, ...START_TIME_MODULES]\n})], SwuiStartTimeModule);\nexport { SwuiStartTimeModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ReactiveFormsModule", "MatInputModule", "SwuiStartTimeComponent", "START_TIME_MODULES", "SwuiStartTimeModule", "__decorate", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-start-time/swui-start-time.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatInputModule } from '@angular/material/input';\n\nimport { SwuiStartTimeComponent } from './swui-start-time.component';\n\nexport const START_TIME_MODULES = [\n  ReactiveFormsModule,\n  MatInputModule,\n];\n\n@NgModule({\n  declarations: [SwuiStartTimeComponent],\n  exports: [SwuiStartTimeComponent],\n  imports: [\n    CommonModule,\n    ...START_TIME_MODULES,\n  ]\n})\nexport class SwuiStartTimeModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,cAAc,QAAQ,yBAAyB;AAExD,SAASC,sBAAsB,QAAQ,6BAA6B;AAEpE,OAAO,MAAMC,kBAAkB,GAAG,CAChCH,mBAAmB,EACnBC,cAAc,CACf;AAUM,IAAMG,mBAAmB,GAAzB,MAAMA,mBAAmB,GAAI;AAAvBA,mBAAmB,GAAAC,UAAA,EAR/BP,QAAQ,CAAC;EACRQ,YAAY,EAAE,CAACJ,sBAAsB,CAAC;EACtCK,OAAO,EAAE,CAACL,sBAAsB,CAAC;EACjCM,OAAO,EAAE,CACPT,YAAY,EACZ,GAAGI,kBAAkB;CAExB,CAAC,C,EACWC,mBAAmB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}