{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatRippleStylesLoader2, _MatRipple;\nimport { normalizePassiveListenerOptions, _getEventTarget, Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, InjectionToken, inject, ElementRef, ANIMATION_MODULE_TYPE, NgZone, Injector, Directive, Input } from '@angular/core';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\n\n/** Possible states for a ripple element. */\nvar RippleState;\n(function (RippleState) {\n  RippleState[RippleState[\"FADING_IN\"] = 0] = \"FADING_IN\";\n  RippleState[RippleState[\"VISIBLE\"] = 1] = \"VISIBLE\";\n  RippleState[RippleState[\"FADING_OUT\"] = 2] = \"FADING_OUT\";\n  RippleState[RippleState[\"HIDDEN\"] = 3] = \"HIDDEN\";\n})(RippleState || (RippleState = {}));\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n  constructor(_renderer, /** Reference to the ripple HTML element. */\n  element, /** Ripple configuration used for the ripple. */\n  config, /* Whether animations are forcibly disabled for ripples through CSS. */\n  _animationForciblyDisabledThroughCss = false) {\n    _defineProperty(this, \"_renderer\", void 0);\n    _defineProperty(this, \"element\", void 0);\n    _defineProperty(this, \"config\", void 0);\n    _defineProperty(this, \"_animationForciblyDisabledThroughCss\", void 0);\n    /** Current state of the ripple. */\n    _defineProperty(this, \"state\", RippleState.HIDDEN);\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n  }\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n  constructor() {\n    _defineProperty(this, \"_events\", new Map());\n    /** Event handler that is bound and which dispatches the events to the different targets. */\n    _defineProperty(this, \"_delegateEventHandler\", event => {\n      const target = _getEventTarget(event);\n      if (target) {\n        var _this$_events$get;\n        (_this$_events$get = this._events.get(event.type)) === null || _this$_events$get === void 0 || _this$_events$get.forEach((handlers, element) => {\n          if (element === target || element.contains(target)) {\n            handlers.forEach(handler => handler.handleEvent(event));\n          }\n        });\n      }\n    });\n  }\n  /** Adds an event handler. */\n  addHandler(ngZone, name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n      });\n    }\n  }\n  /** Removes an event handler. */\n  removeHandler(name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (!handlersForEvent) {\n      return;\n    }\n    const handlersForElement = handlersForEvent.get(element);\n    if (!handlersForElement) {\n      return;\n    }\n    handlersForElement.delete(handler);\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n    }\n  }\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\nclass _MatRippleStylesLoader {}\n_MatRippleStylesLoader2 = _MatRippleStylesLoader;\n_defineProperty(_MatRippleStylesLoader, \"\\u0275fac\", function _MatRippleStylesLoader2_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatRippleStylesLoader2)();\n});\n_defineProperty(_MatRippleStylesLoader, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatRippleStylesLoader2,\n  selectors: [[\"ng-component\"]],\n  hostAttrs: [\"mat-ripple-style-loader\", \"\"],\n  decls: 0,\n  vars: 0,\n  template: function _MatRippleStylesLoader2_Template(rf, ctx) {},\n  styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatRippleStylesLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'mat-ripple-style-loader': ''\n      },\n      styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"]\n    }]\n  }], null, null);\n})();\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n  constructor(_target, _ngZone, elementOrElementRef, _platform, injector) {\n    _defineProperty(this, \"_target\", void 0);\n    _defineProperty(this, \"_ngZone\", void 0);\n    _defineProperty(this, \"_platform\", void 0);\n    /** Element where the ripples are being added to. */\n    _defineProperty(this, \"_containerElement\", void 0);\n    /** Element which triggers the ripple elements on mouse events. */\n    _defineProperty(this, \"_triggerElement\", void 0);\n    /** Whether the pointer is currently down or not. */\n    _defineProperty(this, \"_isPointerDown\", false);\n    /**\n     * Map of currently active ripple references.\n     * The ripple reference is mapped to its element event listeners.\n     * The reason why `| null` is used is that event listeners are added only\n     * when the condition is truthy (see the `_startFadeOutTransition` method).\n     */\n    _defineProperty(this, \"_activeRipples\", new Map());\n    /** Latest non-persistent ripple that was triggered. */\n    _defineProperty(this, \"_mostRecentTransientRipple\", void 0);\n    /** Time in milliseconds when the last touchstart event happened. */\n    _defineProperty(this, \"_lastTouchStartEvent\", void 0);\n    /** Whether pointer-up event listeners have been registered. */\n    _defineProperty(this, \"_pointerUpEventsRegistered\", false);\n    /**\n     * Cached dimensions of the ripple container. Set when the first\n     * ripple is shown and cleared once no more ripples are visible.\n     */\n    _defineProperty(this, \"_containerRect\", void 0);\n    this._target = _target;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n    if (injector) {\n      injector.get(_CdkPrivateStyleLoader).load(_MatRippleStylesLoader);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n    const animationConfig = _objectSpread(_objectSpread({}, defaultRippleAnimationConfig), config.animation);\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n    this._containerElement.appendChild(ripple);\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n    // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n    // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n    userTransitionDuration === '0s' || userTransitionDuration === '0s, 0s' ||\n    // If the container is 0x0, it's likely `display: none`.\n    containerRect.width === 0 && containerRect.height === 0;\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n    rippleRef.state = RippleState.FADING_IN;\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n    let eventListeners = null;\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => {\n          // Clear the fallback timer since the transition fired correctly.\n          if (eventListeners) {\n            eventListeners.fallbackTimer = null;\n          }\n          clearTimeout(fallbackTimer);\n          this._finishRippleTransition(rippleRef);\n        };\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n        // In some cases where there's a higher load on the browser, it can choose not to dispatch\n        // neither `transitionend` nor `transitioncancel` (see b/227356674). This timer serves as a\n        // fallback for such cases so that the ripple doesn't become stuck. We add a 100ms buffer\n        // because timers aren't precise. Note that another approach can be to transition the ripple\n        // to the `VISIBLE` state immediately above and to `FADING_IN` afterwards inside\n        // `transitionstart`. We go with the timer because it's one less event listener and\n        // it's less likely to break existing tests.\n        const fallbackTimer = setTimeout(onTransitionCancel, enterDuration + 100);\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {\n          onTransitionEnd,\n          onTransitionCancel,\n          fallbackTimer\n        };\n      });\n    }\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n      return;\n    }\n    const rippleEl = rippleRef.element;\n    const animationConfig = _objectSpread(_objectSpread({}, defaultRippleAnimationConfig), rippleRef.config.animation);\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = RippleState.FADING_OUT;\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    }\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  _finishRippleTransition(rippleRef) {\n    if (rippleRef.state === RippleState.FADING_IN) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === RippleState.FADING_OUT) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  _startFadeOutTransition(rippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {\n      persistent\n    } = rippleRef.config;\n    rippleRef.state = RippleState.VISIBLE;\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  _destroyRipple(rippleRef) {\n    var _this$_activeRipples$;\n    const eventListeners = (_this$_activeRipples$ = this._activeRipples.get(rippleRef)) !== null && _this$_activeRipples$ !== void 0 ? _this$_activeRipples$ : null;\n    this._activeRipples.delete(rippleRef);\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n    rippleRef.state = RippleState.HIDDEN;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n      if (eventListeners.fallbackTimer !== null) {\n        clearTimeout(eventListeners.fallbackTimer);\n      }\n    }\n    rippleRef.element.remove();\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches;\n      // According to the typings the touches should always be defined, but in some cases\n      // the browser appears to not assign them in tests which leads to flakes.\n      if (touches) {\n        for (let i = 0; i < touches.length; i++) {\n          this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n        }\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n    this._isPointerDown = false;\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === RippleState.VISIBLE || ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN;\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  _getActiveRipples() {\n    return Array.from(this._activeRipples.keys());\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n    if (trigger) {\n      pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n        this._pointerUpEventsRegistered = false;\n      }\n    }\n  }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\n_defineProperty(RippleRenderer, \"_eventManager\", new RippleEventManager());\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n    this._disabled = value;\n    this._setupTriggerEventsIfEnabled();\n  }\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n  set trigger(trigger) {\n    this._trigger = trigger;\n    this._setupTriggerEventsIfEnabled();\n  }\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_animationMode\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }));\n    /** Custom color for all ripples. */\n    _defineProperty(this, \"color\", void 0);\n    /** Whether the ripples should be visible outside the component's bounds. */\n    _defineProperty(this, \"unbounded\", void 0);\n    /**\n     * Whether the ripple always originates from the center of the host element's bounds, rather\n     * than originating from the location of the click event.\n     */\n    _defineProperty(this, \"centered\", void 0);\n    /**\n     * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n     * will be the distance from the center of the ripple to the furthest corner of the host element's\n     * bounding rectangle.\n     */\n    _defineProperty(this, \"radius\", 0);\n    /**\n     * Configuration for the ripple animation. Allows modifying the enter and exit animation\n     * duration of the ripples. The animation durations will be overwritten if the\n     * `NoopAnimationsModule` is being used.\n     */\n    _defineProperty(this, \"animation\", void 0);\n    _defineProperty(this, \"_disabled\", false);\n    _defineProperty(this, \"_trigger\", void 0);\n    /** Renderer for the ripple DOM manipulations. */\n    _defineProperty(this, \"_rippleRenderer\", void 0);\n    /** Options that are set globally for all ripples. */\n    _defineProperty(this, \"_globalOptions\", void 0);\n    /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n    _defineProperty(this, \"_isInitialized\", false);\n    const ngZone = inject(NgZone);\n    const platform = inject(Platform);\n    const globalOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    const injector = inject(Injector);\n    // Note: cannot use `inject()` here, because this class\n    // gets instantiated manually in the ripple loader.\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, this._elementRef, platform, injector);\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._setupTriggerEventsIfEnabled();\n  }\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n  /** Fades out all currently showing ripple elements. */\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n  /** Fades out all currently showing non-persistent ripple elements. */\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleConfig() {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: _objectSpread(_objectSpread(_objectSpread({}, this._globalOptions.animation), this._animationMode === 'NoopAnimations' ? {\n        enterDuration: 0,\n        exitDuration: 0\n      } : {}), this.animation),\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n    };\n  }\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleDisabled() {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n  /** Sets up the trigger event listeners if ripples are enabled. */\n  _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n  launch(configOrX, y = 0, config) {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, _objectSpread(_objectSpread({}, this.rippleConfig), config));\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, _objectSpread(_objectSpread({}, this.rippleConfig), configOrX));\n    }\n  }\n}\n_MatRipple = MatRipple;\n_defineProperty(MatRipple, \"\\u0275fac\", function _MatRipple_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatRipple)();\n});\n_defineProperty(MatRipple, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatRipple,\n  selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n  hostAttrs: [1, \"mat-ripple\"],\n  hostVars: 2,\n  hostBindings: function _MatRipple_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n    }\n  },\n  inputs: {\n    color: [0, \"matRippleColor\", \"color\"],\n    unbounded: [0, \"matRippleUnbounded\", \"unbounded\"],\n    centered: [0, \"matRippleCentered\", \"centered\"],\n    radius: [0, \"matRippleRadius\", \"radius\"],\n    animation: [0, \"matRippleAnimation\", \"animation\"],\n    disabled: [0, \"matRippleDisabled\", \"disabled\"],\n    trigger: [0, \"matRippleTrigger\", \"trigger\"]\n  },\n  exportAs: [\"matRipple\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRipple, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-ripple], [matRipple]',\n      exportAs: 'matRipple',\n      host: {\n        'class': 'mat-ripple',\n        '[class.mat-ripple-unbounded]': 'unbounded'\n      }\n    }]\n  }], () => [], {\n    color: [{\n      type: Input,\n      args: ['matRippleColor']\n    }],\n    unbounded: [{\n      type: Input,\n      args: ['matRippleUnbounded']\n    }],\n    centered: [{\n      type: Input,\n      args: ['matRippleCentered']\n    }],\n    radius: [{\n      type: Input,\n      args: ['matRippleRadius']\n    }],\n    animation: [{\n      type: Input,\n      args: ['matRippleAnimation']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matRippleDisabled']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['matRippleTrigger']\n    }]\n  });\n})();\nexport { MatRipple as M, RippleRenderer as R, MAT_RIPPLE_GLOBAL_OPTIONS as a, RippleState as b, RippleRef as c, defaultRippleAnimationConfig as d };", "map": {"version": 3, "names": ["normalizePassiveListenerOptions", "_getEventTarget", "Platform", "i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "InjectionToken", "inject", "ElementRef", "ANIMATION_MODULE_TYPE", "NgZone", "Injector", "Directive", "Input", "isFakeMousedownFromScreenReader", "isFakeTouchstartFromScreenReader", "coerceElement", "_CdkPrivateStyleLoader", "RippleState", "RippleRef", "constructor", "_renderer", "element", "config", "_animationForciblyDisabledThroughCss", "_defineProperty", "HIDDEN", "fadeOut", "fadeOutRipple", "passiveCapturingEventOptions$1", "passive", "capture", "RippleEventManager", "Map", "event", "target", "_this$_events$get", "_events", "get", "type", "for<PERSON>ach", "handlers", "contains", "handler", "handleEvent", "add<PERSON><PERSON><PERSON>", "ngZone", "name", "handlersForEvent", "handlersForElement", "add", "set", "Set", "runOutsideAngular", "document", "addEventListener", "_delegate<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "delete", "size", "removeEventListener", "defaultRippleAnimationConfig", "enterDuration", "exitDuration", "ignoreMouseEventsTimeout", "passiveCapturingEventOptions", "pointerDownEvents", "pointerUpEvents", "_MatRippleStylesLoader", "_MatRippleStylesLoader2", "_MatRippleStylesLoader2_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "selectors", "hostAttrs", "decls", "vars", "template", "_MatRippleStylesLoader2_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "OnPush", "None", "host", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_target", "_ngZone", "elementOrElementRef", "_platform", "injector", "<PERSON><PERSON><PERSON><PERSON>", "_containerElement", "load", "fadeInRipple", "x", "y", "containerRect", "_containerRect", "getBoundingClientRect", "animationConfig", "_objectSpread", "animation", "centered", "left", "width", "top", "height", "radius", "distanceToFurthestCorner", "offsetX", "offsetY", "ripple", "createElement", "classList", "style", "color", "backgroundColor", "transitionDuration", "append<PERSON><PERSON><PERSON>", "computedStyles", "window", "getComputedStyle", "userTransitionProperty", "transitionProperty", "userTransitionDuration", "animationForciblyDisabledThroughCss", "rippleRef", "transform", "state", "FADING_IN", "persistent", "_mostRecentTransientRipple", "eventListeners", "onTransitionEnd", "fallbackTimer", "clearTimeout", "_finishRippleTransition", "onTransitionCancel", "_destroyRipple", "setTimeout", "_activeRipples", "FADING_OUT", "rippleEl", "opacity", "fadeOutAll", "_getActiveRipples", "fadeOutAllNonPersistent", "setupTriggerEvents", "_triggerElement", "_removeTriggerEvents", "_eventManager", "_onMousedown", "_onTouchStart", "_onPointerUp", "_pointerUpEventsRegistered", "_startFadeOutTransition", "isMostRecentTransientRipple", "VISIBLE", "_isPointerDown", "_this$_activeRipples$", "remove", "isFakeMousedown", "isSyntheticEvent", "_lastTouchStartEvent", "Date", "now", "rippleDisabled", "clientX", "clientY", "rippleConfig", "touches", "changedTouches", "i", "length", "isVisible", "terminateOnPointerUp", "Array", "from", "keys", "trigger", "rect", "distX", "Math", "max", "abs", "right", "distY", "bottom", "sqrt", "MAT_RIPPLE_GLOBAL_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "disabled", "_disabled", "value", "_setupTriggerEventsIfEnabled", "_trigger", "_elementRef", "nativeElement", "optional", "platform", "globalOptions", "_globalOptions", "_ripple<PERSON><PERSON>er", "ngOnInit", "_isInitialized", "ngOnDestroy", "_animationMode", "launch", "configOrX", "_<PERSON><PERSON><PERSON><PERSON>", "_MatRipple_Factory", "ɵɵdefineDirective", "hostVars", "hostBindings", "_MatRipple_HostBindings", "ɵɵclassProp", "unbounded", "inputs", "exportAs", "selector", "M", "R", "a", "b", "c", "d"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/ripple-BT3tzh6F.mjs"], "sourcesContent": ["import { normalizePassiveListenerOptions, _getEventTarget, Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, InjectionToken, inject, ElementRef, ANIMATION_MODULE_TYPE, NgZone, Injector, Directive, Input } from '@angular/core';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\n\n/** Possible states for a ripple element. */\nvar RippleState;\n(function (RippleState) {\n    RippleState[RippleState[\"FADING_IN\"] = 0] = \"FADING_IN\";\n    RippleState[RippleState[\"VISIBLE\"] = 1] = \"VISIBLE\";\n    RippleState[RippleState[\"FADING_OUT\"] = 2] = \"FADING_OUT\";\n    RippleState[RippleState[\"HIDDEN\"] = 3] = \"HIDDEN\";\n})(RippleState || (RippleState = {}));\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n    _renderer;\n    element;\n    config;\n    _animationForciblyDisabledThroughCss;\n    /** Current state of the ripple. */\n    state = RippleState.HIDDEN;\n    constructor(_renderer, \n    /** Reference to the ripple HTML element. */\n    element, \n    /** Ripple configuration used for the ripple. */\n    config, \n    /* Whether animations are forcibly disabled for ripples through CSS. */\n    _animationForciblyDisabledThroughCss = false) {\n        this._renderer = _renderer;\n        this.element = element;\n        this.config = config;\n        this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n    }\n    /** Fades out the ripple element. */\n    fadeOut() {\n        this._renderer.fadeOutRipple(this);\n    }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n    _events = new Map();\n    /** Adds an event handler. */\n    addHandler(ngZone, name, element, handler) {\n        const handlersForEvent = this._events.get(name);\n        if (handlersForEvent) {\n            const handlersForElement = handlersForEvent.get(element);\n            if (handlersForElement) {\n                handlersForElement.add(handler);\n            }\n            else {\n                handlersForEvent.set(element, new Set([handler]));\n            }\n        }\n        else {\n            this._events.set(name, new Map([[element, new Set([handler])]]));\n            ngZone.runOutsideAngular(() => {\n                document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n            });\n        }\n    }\n    /** Removes an event handler. */\n    removeHandler(name, element, handler) {\n        const handlersForEvent = this._events.get(name);\n        if (!handlersForEvent) {\n            return;\n        }\n        const handlersForElement = handlersForEvent.get(element);\n        if (!handlersForElement) {\n            return;\n        }\n        handlersForElement.delete(handler);\n        if (handlersForElement.size === 0) {\n            handlersForEvent.delete(element);\n        }\n        if (handlersForEvent.size === 0) {\n            this._events.delete(name);\n            document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n        }\n    }\n    /** Event handler that is bound and which dispatches the events to the different targets. */\n    _delegateEventHandler = (event) => {\n        const target = _getEventTarget(event);\n        if (target) {\n            this._events.get(event.type)?.forEach((handlers, element) => {\n                if (element === target || element.contains(target)) {\n                    handlers.forEach(handler => handler.handleEvent(event));\n                }\n            });\n        }\n    };\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n    enterDuration: 225,\n    exitDuration: 150,\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\nclass _MatRippleStylesLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _MatRippleStylesLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: _MatRippleStylesLoader, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"mat-ripple-style-loader\": \"\" } }, ngImport: i0, template: '', isInline: true, styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _MatRippleStylesLoader, decorators: [{\n            type: Component,\n            args: [{ template: '', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: { 'mat-ripple-style-loader': '' }, styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"] }]\n        }] });\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n    _target;\n    _ngZone;\n    _platform;\n    /** Element where the ripples are being added to. */\n    _containerElement;\n    /** Element which triggers the ripple elements on mouse events. */\n    _triggerElement;\n    /** Whether the pointer is currently down or not. */\n    _isPointerDown = false;\n    /**\n     * Map of currently active ripple references.\n     * The ripple reference is mapped to its element event listeners.\n     * The reason why `| null` is used is that event listeners are added only\n     * when the condition is truthy (see the `_startFadeOutTransition` method).\n     */\n    _activeRipples = new Map();\n    /** Latest non-persistent ripple that was triggered. */\n    _mostRecentTransientRipple;\n    /** Time in milliseconds when the last touchstart event happened. */\n    _lastTouchStartEvent;\n    /** Whether pointer-up event listeners have been registered. */\n    _pointerUpEventsRegistered = false;\n    /**\n     * Cached dimensions of the ripple container. Set when the first\n     * ripple is shown and cleared once no more ripples are visible.\n     */\n    _containerRect;\n    static _eventManager = new RippleEventManager();\n    constructor(_target, _ngZone, elementOrElementRef, _platform, injector) {\n        this._target = _target;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        // Only do anything if we're on the browser.\n        if (_platform.isBrowser) {\n            this._containerElement = coerceElement(elementOrElementRef);\n        }\n        if (injector) {\n            injector.get(_CdkPrivateStyleLoader).load(_MatRippleStylesLoader);\n        }\n    }\n    /**\n     * Fades in a ripple at the given coordinates.\n     * @param x Coordinate within the element, along the X axis at which to start the ripple.\n     * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n     * @param config Extra ripple options.\n     */\n    fadeInRipple(x, y, config = {}) {\n        const containerRect = (this._containerRect =\n            this._containerRect || this._containerElement.getBoundingClientRect());\n        const animationConfig = { ...defaultRippleAnimationConfig, ...config.animation };\n        if (config.centered) {\n            x = containerRect.left + containerRect.width / 2;\n            y = containerRect.top + containerRect.height / 2;\n        }\n        const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n        const offsetX = x - containerRect.left;\n        const offsetY = y - containerRect.top;\n        const enterDuration = animationConfig.enterDuration;\n        const ripple = document.createElement('div');\n        ripple.classList.add('mat-ripple-element');\n        ripple.style.left = `${offsetX - radius}px`;\n        ripple.style.top = `${offsetY - radius}px`;\n        ripple.style.height = `${radius * 2}px`;\n        ripple.style.width = `${radius * 2}px`;\n        // If a custom color has been specified, set it as inline style. If no color is\n        // set, the default color will be applied through the ripple theme styles.\n        if (config.color != null) {\n            ripple.style.backgroundColor = config.color;\n        }\n        ripple.style.transitionDuration = `${enterDuration}ms`;\n        this._containerElement.appendChild(ripple);\n        // By default the browser does not recalculate the styles of dynamically created\n        // ripple elements. This is critical to ensure that the `scale` animates properly.\n        // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n        // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n        const computedStyles = window.getComputedStyle(ripple);\n        const userTransitionProperty = computedStyles.transitionProperty;\n        const userTransitionDuration = computedStyles.transitionDuration;\n        // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n        // `transition: none` or `display: none`). This is technically unexpected since animations are\n        // controlled through the animation config, but this exists for backwards compatibility. This\n        // logic does not need to be super accurate since it covers some edge cases which can be easily\n        // avoided by users.\n        const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n            // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n            // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n            userTransitionDuration === '0s' ||\n            userTransitionDuration === '0s, 0s' ||\n            // If the container is 0x0, it's likely `display: none`.\n            (containerRect.width === 0 && containerRect.height === 0);\n        // Exposed reference to the ripple that will be returned.\n        const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n        // Start the enter animation by setting the transform/scale to 100%. The animation will\n        // execute as part of this statement because we forced a style recalculation before.\n        // Note: We use a 3d transform here in order to avoid an issue in Safari where\n        // the ripples aren't clipped when inside the shadow DOM (see #24028).\n        ripple.style.transform = 'scale3d(1, 1, 1)';\n        rippleRef.state = RippleState.FADING_IN;\n        if (!config.persistent) {\n            this._mostRecentTransientRipple = rippleRef;\n        }\n        let eventListeners = null;\n        // Do not register the `transition` event listener if fade-in and fade-out duration\n        // are set to zero. The events won't fire anyway and we can save resources here.\n        if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n            this._ngZone.runOutsideAngular(() => {\n                const onTransitionEnd = () => {\n                    // Clear the fallback timer since the transition fired correctly.\n                    if (eventListeners) {\n                        eventListeners.fallbackTimer = null;\n                    }\n                    clearTimeout(fallbackTimer);\n                    this._finishRippleTransition(rippleRef);\n                };\n                const onTransitionCancel = () => this._destroyRipple(rippleRef);\n                // In some cases where there's a higher load on the browser, it can choose not to dispatch\n                // neither `transitionend` nor `transitioncancel` (see b/227356674). This timer serves as a\n                // fallback for such cases so that the ripple doesn't become stuck. We add a 100ms buffer\n                // because timers aren't precise. Note that another approach can be to transition the ripple\n                // to the `VISIBLE` state immediately above and to `FADING_IN` afterwards inside\n                // `transitionstart`. We go with the timer because it's one less event listener and\n                // it's less likely to break existing tests.\n                const fallbackTimer = setTimeout(onTransitionCancel, enterDuration + 100);\n                ripple.addEventListener('transitionend', onTransitionEnd);\n                // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n                // directly as otherwise we would keep it part of the ripple container forever.\n                // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n                ripple.addEventListener('transitioncancel', onTransitionCancel);\n                eventListeners = { onTransitionEnd, onTransitionCancel, fallbackTimer };\n            });\n        }\n        // Add the ripple reference to the list of all active ripples.\n        this._activeRipples.set(rippleRef, eventListeners);\n        // In case there is no fade-in transition duration, we need to manually call the transition\n        // end listener because `transitionend` doesn't fire if there is no transition.\n        if (animationForciblyDisabledThroughCss || !enterDuration) {\n            this._finishRippleTransition(rippleRef);\n        }\n        return rippleRef;\n    }\n    /** Fades out a ripple reference. */\n    fadeOutRipple(rippleRef) {\n        // For ripples already fading out or hidden, this should be a noop.\n        if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n            return;\n        }\n        const rippleEl = rippleRef.element;\n        const animationConfig = { ...defaultRippleAnimationConfig, ...rippleRef.config.animation };\n        // This starts the fade-out transition and will fire the transition end listener that\n        // removes the ripple element from the DOM.\n        rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n        rippleEl.style.opacity = '0';\n        rippleRef.state = RippleState.FADING_OUT;\n        // In case there is no fade-out transition duration, we need to manually call the\n        // transition end listener because `transitionend` doesn't fire if there is no transition.\n        if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n            this._finishRippleTransition(rippleRef);\n        }\n    }\n    /** Fades out all currently active ripples. */\n    fadeOutAll() {\n        this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n    }\n    /** Fades out all currently active non-persistent ripples. */\n    fadeOutAllNonPersistent() {\n        this._getActiveRipples().forEach(ripple => {\n            if (!ripple.config.persistent) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    /** Sets up the trigger event listeners */\n    setupTriggerEvents(elementOrElementRef) {\n        const element = coerceElement(elementOrElementRef);\n        if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n            return;\n        }\n        // Remove all previously registered event listeners from the trigger element.\n        this._removeTriggerEvents();\n        this._triggerElement = element;\n        // Use event delegation for the trigger events since they're\n        // set up during creation and are performance-sensitive.\n        pointerDownEvents.forEach(type => {\n            RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n        });\n    }\n    /**\n     * Handles all registered events.\n     * @docs-private\n     */\n    handleEvent(event) {\n        if (event.type === 'mousedown') {\n            this._onMousedown(event);\n        }\n        else if (event.type === 'touchstart') {\n            this._onTouchStart(event);\n        }\n        else {\n            this._onPointerUp();\n        }\n        // If pointer-up events haven't been registered yet, do so now.\n        // We do this on-demand in order to reduce the total number of event listeners\n        // registered by the ripples, which speeds up the rendering time for large UIs.\n        if (!this._pointerUpEventsRegistered) {\n            // The events for hiding the ripple are bound directly on the trigger, because:\n            // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n            // delegation will be diminished by having to look through all the data structures often.\n            // 2. They aren't as performance-sensitive, because they're bound only after the user\n            // has interacted with an element.\n            this._ngZone.runOutsideAngular(() => {\n                pointerUpEvents.forEach(type => {\n                    this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n                });\n            });\n            this._pointerUpEventsRegistered = true;\n        }\n    }\n    /** Method that will be called if the fade-in or fade-in transition completed. */\n    _finishRippleTransition(rippleRef) {\n        if (rippleRef.state === RippleState.FADING_IN) {\n            this._startFadeOutTransition(rippleRef);\n        }\n        else if (rippleRef.state === RippleState.FADING_OUT) {\n            this._destroyRipple(rippleRef);\n        }\n    }\n    /**\n     * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n     * is not held down anymore.\n     */\n    _startFadeOutTransition(rippleRef) {\n        const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n        const { persistent } = rippleRef.config;\n        rippleRef.state = RippleState.VISIBLE;\n        // When the timer runs out while the user has kept their pointer down, we want to\n        // keep only the persistent ripples and the latest transient ripple. We do this,\n        // because we don't want stacked transient ripples to appear after their enter\n        // animation has finished.\n        if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n            rippleRef.fadeOut();\n        }\n    }\n    /** Destroys the given ripple by removing it from the DOM and updating its state. */\n    _destroyRipple(rippleRef) {\n        const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n        this._activeRipples.delete(rippleRef);\n        // Clear out the cached bounding rect if we have no more ripples.\n        if (!this._activeRipples.size) {\n            this._containerRect = null;\n        }\n        // If the current ref is the most recent transient ripple, unset it\n        // avoid memory leaks.\n        if (rippleRef === this._mostRecentTransientRipple) {\n            this._mostRecentTransientRipple = null;\n        }\n        rippleRef.state = RippleState.HIDDEN;\n        if (eventListeners !== null) {\n            rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n            rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n            if (eventListeners.fallbackTimer !== null) {\n                clearTimeout(eventListeners.fallbackTimer);\n            }\n        }\n        rippleRef.element.remove();\n    }\n    /** Function being called whenever the trigger is being pressed using mouse. */\n    _onMousedown(event) {\n        // Screen readers will fire fake mouse events for space/enter. Skip launching a\n        // ripple in this case for consistency with the non-screen-reader experience.\n        const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n        const isSyntheticEvent = this._lastTouchStartEvent &&\n            Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n        if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n            this._isPointerDown = true;\n            this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n        }\n    }\n    /** Function being called whenever the trigger is being pressed using touch. */\n    _onTouchStart(event) {\n        if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n            // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n            // events will launch a second ripple if we don't ignore mouse events for a specific\n            // time after a touchstart event.\n            this._lastTouchStartEvent = Date.now();\n            this._isPointerDown = true;\n            // Use `changedTouches` so we skip any touches where the user put\n            // their finger down, but used another finger to tap the element again.\n            const touches = event.changedTouches;\n            // According to the typings the touches should always be defined, but in some cases\n            // the browser appears to not assign them in tests which leads to flakes.\n            if (touches) {\n                for (let i = 0; i < touches.length; i++) {\n                    this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n                }\n            }\n        }\n    }\n    /** Function being called whenever the trigger is being released. */\n    _onPointerUp() {\n        if (!this._isPointerDown) {\n            return;\n        }\n        this._isPointerDown = false;\n        // Fade-out all ripples that are visible and not persistent.\n        this._getActiveRipples().forEach(ripple => {\n            // By default, only ripples that are completely visible will fade out on pointer release.\n            // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n            const isVisible = ripple.state === RippleState.VISIBLE ||\n                (ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN);\n            if (!ripple.config.persistent && isVisible) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    _getActiveRipples() {\n        return Array.from(this._activeRipples.keys());\n    }\n    /** Removes previously registered event listeners from the trigger element. */\n    _removeTriggerEvents() {\n        const trigger = this._triggerElement;\n        if (trigger) {\n            pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n            if (this._pointerUpEventsRegistered) {\n                pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n                this._pointerUpEventsRegistered = false;\n            }\n        }\n    }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n    const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n    const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n    return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n    _elementRef = inject(ElementRef);\n    _animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n    /** Custom color for all ripples. */\n    color;\n    /** Whether the ripples should be visible outside the component's bounds. */\n    unbounded;\n    /**\n     * Whether the ripple always originates from the center of the host element's bounds, rather\n     * than originating from the location of the click event.\n     */\n    centered;\n    /**\n     * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n     * will be the distance from the center of the ripple to the furthest corner of the host element's\n     * bounding rectangle.\n     */\n    radius = 0;\n    /**\n     * Configuration for the ripple animation. Allows modifying the enter and exit animation\n     * duration of the ripples. The animation durations will be overwritten if the\n     * `NoopAnimationsModule` is being used.\n     */\n    animation;\n    /**\n     * Whether click events will not trigger the ripple. Ripples can be still launched manually\n     * by using the `launch()` method.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        if (value) {\n            this.fadeOutAllNonPersistent();\n        }\n        this._disabled = value;\n        this._setupTriggerEventsIfEnabled();\n    }\n    _disabled = false;\n    /**\n     * The element that triggers the ripple when click events are received.\n     * Defaults to the directive's host element.\n     */\n    get trigger() {\n        return this._trigger || this._elementRef.nativeElement;\n    }\n    set trigger(trigger) {\n        this._trigger = trigger;\n        this._setupTriggerEventsIfEnabled();\n    }\n    _trigger;\n    /** Renderer for the ripple DOM manipulations. */\n    _rippleRenderer;\n    /** Options that are set globally for all ripples. */\n    _globalOptions;\n    /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n    _isInitialized = false;\n    constructor() {\n        const ngZone = inject(NgZone);\n        const platform = inject(Platform);\n        const globalOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, { optional: true });\n        const injector = inject(Injector);\n        // Note: cannot use `inject()` here, because this class\n        // gets instantiated manually in the ripple loader.\n        this._globalOptions = globalOptions || {};\n        this._rippleRenderer = new RippleRenderer(this, ngZone, this._elementRef, platform, injector);\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n        this._setupTriggerEventsIfEnabled();\n    }\n    ngOnDestroy() {\n        this._rippleRenderer._removeTriggerEvents();\n    }\n    /** Fades out all currently showing ripple elements. */\n    fadeOutAll() {\n        this._rippleRenderer.fadeOutAll();\n    }\n    /** Fades out all currently showing non-persistent ripple elements. */\n    fadeOutAllNonPersistent() {\n        this._rippleRenderer.fadeOutAllNonPersistent();\n    }\n    /**\n     * Ripple configuration from the directive's input values.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleConfig() {\n        return {\n            centered: this.centered,\n            radius: this.radius,\n            color: this.color,\n            animation: {\n                ...this._globalOptions.animation,\n                ...(this._animationMode === 'NoopAnimations' ? { enterDuration: 0, exitDuration: 0 } : {}),\n                ...this.animation,\n            },\n            terminateOnPointerUp: this._globalOptions.terminateOnPointerUp,\n        };\n    }\n    /**\n     * Whether ripples on pointer-down are disabled or not.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleDisabled() {\n        return this.disabled || !!this._globalOptions.disabled;\n    }\n    /** Sets up the trigger event listeners if ripples are enabled. */\n    _setupTriggerEventsIfEnabled() {\n        if (!this.disabled && this._isInitialized) {\n            this._rippleRenderer.setupTriggerEvents(this.trigger);\n        }\n    }\n    /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n    launch(configOrX, y = 0, config) {\n        if (typeof configOrX === 'number') {\n            return this._rippleRenderer.fadeInRipple(configOrX, y, { ...this.rippleConfig, ...config });\n        }\n        else {\n            return this._rippleRenderer.fadeInRipple(0, 0, { ...this.rippleConfig, ...configOrX });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRipple, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatRipple, isStandalone: true, selector: \"[mat-ripple], [matRipple]\", inputs: { color: [\"matRippleColor\", \"color\"], unbounded: [\"matRippleUnbounded\", \"unbounded\"], centered: [\"matRippleCentered\", \"centered\"], radius: [\"matRippleRadius\", \"radius\"], animation: [\"matRippleAnimation\", \"animation\"], disabled: [\"matRippleDisabled\", \"disabled\"], trigger: [\"matRippleTrigger\", \"trigger\"] }, host: { properties: { \"class.mat-ripple-unbounded\": \"unbounded\" }, classAttribute: \"mat-ripple\" }, exportAs: [\"matRipple\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRipple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-ripple], [matRipple]',\n                    exportAs: 'matRipple',\n                    host: {\n                        'class': 'mat-ripple',\n                        '[class.mat-ripple-unbounded]': 'unbounded',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input,\n                args: ['matRippleColor']\n            }], unbounded: [{\n                type: Input,\n                args: ['matRippleUnbounded']\n            }], centered: [{\n                type: Input,\n                args: ['matRippleCentered']\n            }], radius: [{\n                type: Input,\n                args: ['matRippleRadius']\n            }], animation: [{\n                type: Input,\n                args: ['matRippleAnimation']\n            }], disabled: [{\n                type: Input,\n                args: ['matRippleDisabled']\n            }], trigger: [{\n                type: Input,\n                args: ['matRippleTrigger']\n            }] } });\n\nexport { MatRipple as M, RippleRenderer as R, MAT_RIPPLE_GLOBAL_OPTIONS as a, RippleState as b, RippleRef as c, defaultRippleAnimationConfig as d };\n"], "mappings": ";;;AAAA,SAASA,+BAA+B,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,uBAAuB;AAClG,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACpL,SAASC,+BAA+B,EAAEC,gCAAgC,QAAQ,mBAAmB;AACrG,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,sBAAsB,QAAQ,sBAAsB;;AAE7D;AACA,IAAIC,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpBA,WAAW,CAACA,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACvDA,WAAW,CAACA,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACnDA,WAAW,CAACA,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACzDA,WAAW,CAACA,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AACrD,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EAOZC,WAAWA,CAACC,SAAS,EACrB;EACAC,OAAO,EACP;EACAC,MAAM,EACN;EACAC,oCAAoC,GAAG,KAAK,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAR9C;IAAAA,eAAA,gBACQP,WAAW,CAACQ,MAAM;IAQtB,IAAI,CAACL,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,oCAAoC,GAAGA,oCAAoC;EACpF;EACA;EACAG,OAAOA,CAAA,EAAG;IACN,IAAI,CAACN,SAAS,CAACO,aAAa,CAAC,IAAI,CAAC;EACtC;AACJ;;AAEA;AACA,MAAMC,8BAA8B,GAAG9B,+BAA+B,CAAC;EACnE+B,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMC,kBAAkB,CAAC;EAAAZ,YAAA;IAAAK,eAAA,kBACX,IAAIQ,GAAG,CAAC,CAAC;IAuCnB;IAAAR,eAAA,gCACyBS,KAAK,IAAK;MAC/B,MAAMC,MAAM,GAAGnC,eAAe,CAACkC,KAAK,CAAC;MACrC,IAAIC,MAAM,EAAE;QAAA,IAAAC,iBAAA;QACR,CAAAA,iBAAA,OAAI,CAACC,OAAO,CAACC,GAAG,CAACJ,KAAK,CAACK,IAAI,CAAC,cAAAH,iBAAA,eAA5BA,iBAAA,CAA8BI,OAAO,CAAC,CAACC,QAAQ,EAAEnB,OAAO,KAAK;UACzD,IAAIA,OAAO,KAAKa,MAAM,IAAIb,OAAO,CAACoB,QAAQ,CAACP,MAAM,CAAC,EAAE;YAChDM,QAAQ,CAACD,OAAO,CAACG,OAAO,IAAIA,OAAO,CAACC,WAAW,CAACV,KAAK,CAAC,CAAC;UAC3D;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;EAAA;EAhDD;EACAW,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAEzB,OAAO,EAAEqB,OAAO,EAAE;IACvC,MAAMK,gBAAgB,GAAG,IAAI,CAACX,OAAO,CAACC,GAAG,CAACS,IAAI,CAAC;IAC/C,IAAIC,gBAAgB,EAAE;MAClB,MAAMC,kBAAkB,GAAGD,gBAAgB,CAACV,GAAG,CAAChB,OAAO,CAAC;MACxD,IAAI2B,kBAAkB,EAAE;QACpBA,kBAAkB,CAACC,GAAG,CAACP,OAAO,CAAC;MACnC,CAAC,MACI;QACDK,gBAAgB,CAACG,GAAG,CAAC7B,OAAO,EAAE,IAAI8B,GAAG,CAAC,CAACT,OAAO,CAAC,CAAC,CAAC;MACrD;IACJ,CAAC,MACI;MACD,IAAI,CAACN,OAAO,CAACc,GAAG,CAACJ,IAAI,EAAE,IAAId,GAAG,CAAC,CAAC,CAACX,OAAO,EAAE,IAAI8B,GAAG,CAAC,CAACT,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChEG,MAAM,CAACO,iBAAiB,CAAC,MAAM;QAC3BC,QAAQ,CAACC,gBAAgB,CAACR,IAAI,EAAE,IAAI,CAACS,qBAAqB,EAAE3B,8BAA8B,CAAC;MAC/F,CAAC,CAAC;IACN;EACJ;EACA;EACA4B,aAAaA,CAACV,IAAI,EAAEzB,OAAO,EAAEqB,OAAO,EAAE;IAClC,MAAMK,gBAAgB,GAAG,IAAI,CAACX,OAAO,CAACC,GAAG,CAACS,IAAI,CAAC;IAC/C,IAAI,CAACC,gBAAgB,EAAE;MACnB;IACJ;IACA,MAAMC,kBAAkB,GAAGD,gBAAgB,CAACV,GAAG,CAAChB,OAAO,CAAC;IACxD,IAAI,CAAC2B,kBAAkB,EAAE;MACrB;IACJ;IACAA,kBAAkB,CAACS,MAAM,CAACf,OAAO,CAAC;IAClC,IAAIM,kBAAkB,CAACU,IAAI,KAAK,CAAC,EAAE;MAC/BX,gBAAgB,CAACU,MAAM,CAACpC,OAAO,CAAC;IACpC;IACA,IAAI0B,gBAAgB,CAACW,IAAI,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACtB,OAAO,CAACqB,MAAM,CAACX,IAAI,CAAC;MACzBO,QAAQ,CAACM,mBAAmB,CAACb,IAAI,EAAE,IAAI,CAACS,qBAAqB,EAAE3B,8BAA8B,CAAC;IAClG;EACJ;AAYJ;;AAEA;AACA;AACA;AACA;AACA,MAAMgC,4BAA4B,GAAG;EACjCC,aAAa,EAAE,GAAG;EAClBC,YAAY,EAAE;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,GAAG;AACpC;AACA,MAAMC,4BAA4B,GAAGlE,+BAA+B,CAAC;EACjE+B,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMmC,iBAAiB,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC;AACrD;AACA,MAAMC,eAAe,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC;AAC5E,MAAMC,sBAAsB,CAAC;AAG5BC,uBAAA,GAHKD,sBAAsB;AAAA3C,eAAA,CAAtB2C,sBAAsB,wBAAAE,gCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAC2EH,uBAAsB;AAAA;AAAA3C,eAAA,CADvH2C,sBAAsB,8BAIqDlE,EAAE,CAAAsE,iBAAA;EAAAjC,IAAA,EAFQ6B,uBAAsB;EAAAK,SAAA;EAAAC,SAAA,8BAAiG,EAAE;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;EAAAC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAEpN;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFlF,EAAE,CAAAmF,iBAAA,CAAQjB,sBAAsB,EAAc,CAAC;IACpH7B,IAAI,EAAEpC,SAAS;IACfmF,IAAI,EAAE,CAAC;MAAET,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAE/E,uBAAuB,CAACmF,MAAM;MAAEL,aAAa,EAAE7E,iBAAiB,CAACmF,IAAI;MAAEC,IAAI,EAAE;QAAE,yBAAyB,EAAE;MAAG,CAAC;MAAER,MAAM,EAAE,CAAC,6jBAA6jB;IAAE,CAAC;EACruB,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,cAAc,CAAC;EA6BjBtE,WAAWA,CAACuE,OAAO,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,QAAQ,EAAE;IAAAtE,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAzBxE;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,yBACiB,KAAK;IACtB;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,yBAMiB,IAAIQ,GAAG,CAAC,CAAC;IAC1B;IAAAR,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,qCAC6B,KAAK;IAClC;AACJ;AACA;AACA;IAHIA,eAAA;IAOI,IAAI,CAACkE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAIA,SAAS,CAACE,SAAS,EAAE;MACrB,IAAI,CAACC,iBAAiB,GAAGjF,aAAa,CAAC6E,mBAAmB,CAAC;IAC/D;IACA,IAAIE,QAAQ,EAAE;MACVA,QAAQ,CAACzD,GAAG,CAACrB,sBAAsB,CAAC,CAACiF,IAAI,CAAC9B,sBAAsB,CAAC;IACrE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+B,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE9E,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5B,MAAM+E,aAAa,GAAI,IAAI,CAACC,cAAc,GACtC,IAAI,CAACA,cAAc,IAAI,IAAI,CAACN,iBAAiB,CAACO,qBAAqB,CAAC,CAAE;IAC1E,MAAMC,eAAe,GAAAC,aAAA,CAAAA,aAAA,KAAQ7C,4BAA4B,GAAKtC,MAAM,CAACoF,SAAS,CAAE;IAChF,IAAIpF,MAAM,CAACqF,QAAQ,EAAE;MACjBR,CAAC,GAAGE,aAAa,CAACO,IAAI,GAAGP,aAAa,CAACQ,KAAK,GAAG,CAAC;MAChDT,CAAC,GAAGC,aAAa,CAACS,GAAG,GAAGT,aAAa,CAACU,MAAM,GAAG,CAAC;IACpD;IACA,MAAMC,MAAM,GAAG1F,MAAM,CAAC0F,MAAM,IAAIC,wBAAwB,CAACd,CAAC,EAAEC,CAAC,EAAEC,aAAa,CAAC;IAC7E,MAAMa,OAAO,GAAGf,CAAC,GAAGE,aAAa,CAACO,IAAI;IACtC,MAAMO,OAAO,GAAGf,CAAC,GAAGC,aAAa,CAACS,GAAG;IACrC,MAAMjD,aAAa,GAAG2C,eAAe,CAAC3C,aAAa;IACnD,MAAMuD,MAAM,GAAG/D,QAAQ,CAACgE,aAAa,CAAC,KAAK,CAAC;IAC5CD,MAAM,CAACE,SAAS,CAACrE,GAAG,CAAC,oBAAoB,CAAC;IAC1CmE,MAAM,CAACG,KAAK,CAACX,IAAI,GAAG,GAAGM,OAAO,GAAGF,MAAM,IAAI;IAC3CI,MAAM,CAACG,KAAK,CAACT,GAAG,GAAG,GAAGK,OAAO,GAAGH,MAAM,IAAI;IAC1CI,MAAM,CAACG,KAAK,CAACR,MAAM,GAAG,GAAGC,MAAM,GAAG,CAAC,IAAI;IACvCI,MAAM,CAACG,KAAK,CAACV,KAAK,GAAG,GAAGG,MAAM,GAAG,CAAC,IAAI;IACtC;IACA;IACA,IAAI1F,MAAM,CAACkG,KAAK,IAAI,IAAI,EAAE;MACtBJ,MAAM,CAACG,KAAK,CAACE,eAAe,GAAGnG,MAAM,CAACkG,KAAK;IAC/C;IACAJ,MAAM,CAACG,KAAK,CAACG,kBAAkB,GAAG,GAAG7D,aAAa,IAAI;IACtD,IAAI,CAACmC,iBAAiB,CAAC2B,WAAW,CAACP,MAAM,CAAC;IAC1C;IACA;IACA;IACA;IACA,MAAMQ,cAAc,GAAGC,MAAM,CAACC,gBAAgB,CAACV,MAAM,CAAC;IACtD,MAAMW,sBAAsB,GAAGH,cAAc,CAACI,kBAAkB;IAChE,MAAMC,sBAAsB,GAAGL,cAAc,CAACF,kBAAkB;IAChE;IACA;IACA;IACA;IACA;IACA,MAAMQ,mCAAmC,GAAGH,sBAAsB,KAAK,MAAM;IACzE;IACA;IACAE,sBAAsB,KAAK,IAAI,IAC/BA,sBAAsB,KAAK,QAAQ;IACnC;IACC5B,aAAa,CAACQ,KAAK,KAAK,CAAC,IAAIR,aAAa,CAACU,MAAM,KAAK,CAAE;IAC7D;IACA,MAAMoB,SAAS,GAAG,IAAIjH,SAAS,CAAC,IAAI,EAAEkG,MAAM,EAAE9F,MAAM,EAAE4G,mCAAmC,CAAC;IAC1F;IACA;IACA;IACA;IACAd,MAAM,CAACG,KAAK,CAACa,SAAS,GAAG,kBAAkB;IAC3CD,SAAS,CAACE,KAAK,GAAGpH,WAAW,CAACqH,SAAS;IACvC,IAAI,CAAChH,MAAM,CAACiH,UAAU,EAAE;MACpB,IAAI,CAACC,0BAA0B,GAAGL,SAAS;IAC/C;IACA,IAAIM,cAAc,GAAG,IAAI;IACzB;IACA;IACA,IAAI,CAACP,mCAAmC,KAAKrE,aAAa,IAAI2C,eAAe,CAAC1C,YAAY,CAAC,EAAE;MACzF,IAAI,CAAC6B,OAAO,CAACvC,iBAAiB,CAAC,MAAM;QACjC,MAAMsF,eAAe,GAAGA,CAAA,KAAM;UAC1B;UACA,IAAID,cAAc,EAAE;YAChBA,cAAc,CAACE,aAAa,GAAG,IAAI;UACvC;UACAC,YAAY,CAACD,aAAa,CAAC;UAC3B,IAAI,CAACE,uBAAuB,CAACV,SAAS,CAAC;QAC3C,CAAC;QACD,MAAMW,kBAAkB,GAAGA,CAAA,KAAM,IAAI,CAACC,cAAc,CAACZ,SAAS,CAAC;QAC/D;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMQ,aAAa,GAAGK,UAAU,CAACF,kBAAkB,EAAEjF,aAAa,GAAG,GAAG,CAAC;QACzEuD,MAAM,CAAC9D,gBAAgB,CAAC,eAAe,EAAEoF,eAAe,CAAC;QACzD;QACA;QACA;QACAtB,MAAM,CAAC9D,gBAAgB,CAAC,kBAAkB,EAAEwF,kBAAkB,CAAC;QAC/DL,cAAc,GAAG;UAAEC,eAAe;UAAEI,kBAAkB;UAAEH;QAAc,CAAC;MAC3E,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAACM,cAAc,CAAC/F,GAAG,CAACiF,SAAS,EAAEM,cAAc,CAAC;IAClD;IACA;IACA,IAAIP,mCAAmC,IAAI,CAACrE,aAAa,EAAE;MACvD,IAAI,CAACgF,uBAAuB,CAACV,SAAS,CAAC;IAC3C;IACA,OAAOA,SAAS;EACpB;EACA;EACAxG,aAAaA,CAACwG,SAAS,EAAE;IACrB;IACA,IAAIA,SAAS,CAACE,KAAK,KAAKpH,WAAW,CAACiI,UAAU,IAAIf,SAAS,CAACE,KAAK,KAAKpH,WAAW,CAACQ,MAAM,EAAE;MACtF;IACJ;IACA,MAAM0H,QAAQ,GAAGhB,SAAS,CAAC9G,OAAO;IAClC,MAAMmF,eAAe,GAAAC,aAAA,CAAAA,aAAA,KAAQ7C,4BAA4B,GAAKuE,SAAS,CAAC7G,MAAM,CAACoF,SAAS,CAAE;IAC1F;IACA;IACAyC,QAAQ,CAAC5B,KAAK,CAACG,kBAAkB,GAAG,GAAGlB,eAAe,CAAC1C,YAAY,IAAI;IACvEqF,QAAQ,CAAC5B,KAAK,CAAC6B,OAAO,GAAG,GAAG;IAC5BjB,SAAS,CAACE,KAAK,GAAGpH,WAAW,CAACiI,UAAU;IACxC;IACA;IACA,IAAIf,SAAS,CAAC5G,oCAAoC,IAAI,CAACiF,eAAe,CAAC1C,YAAY,EAAE;MACjF,IAAI,CAAC+E,uBAAuB,CAACV,SAAS,CAAC;IAC3C;EACJ;EACA;EACAkB,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC/G,OAAO,CAAC6E,MAAM,IAAIA,MAAM,CAAC1F,OAAO,CAAC,CAAC,CAAC;EAChE;EACA;EACA6H,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACD,iBAAiB,CAAC,CAAC,CAAC/G,OAAO,CAAC6E,MAAM,IAAI;MACvC,IAAI,CAACA,MAAM,CAAC9F,MAAM,CAACiH,UAAU,EAAE;QAC3BnB,MAAM,CAAC1F,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;EACN;EACA;EACA8H,kBAAkBA,CAAC5D,mBAAmB,EAAE;IACpC,MAAMvE,OAAO,GAAGN,aAAa,CAAC6E,mBAAmB,CAAC;IAClD,IAAI,CAAC,IAAI,CAACC,SAAS,CAACE,SAAS,IAAI,CAAC1E,OAAO,IAAIA,OAAO,KAAK,IAAI,CAACoI,eAAe,EAAE;MAC3E;IACJ;IACA;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACD,eAAe,GAAGpI,OAAO;IAC9B;IACA;IACA4C,iBAAiB,CAAC1B,OAAO,CAACD,IAAI,IAAI;MAC9BmD,cAAc,CAACkE,aAAa,CAAC/G,UAAU,CAAC,IAAI,CAAC+C,OAAO,EAAErD,IAAI,EAAEjB,OAAO,EAAE,IAAI,CAAC;IAC9E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIsB,WAAWA,CAACV,KAAK,EAAE;IACf,IAAIA,KAAK,CAACK,IAAI,KAAK,WAAW,EAAE;MAC5B,IAAI,CAACsH,YAAY,CAAC3H,KAAK,CAAC;IAC5B,CAAC,MACI,IAAIA,KAAK,CAACK,IAAI,KAAK,YAAY,EAAE;MAClC,IAAI,CAACuH,aAAa,CAAC5H,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAAC6H,YAAY,CAAC,CAAC;IACvB;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACC,0BAA0B,EAAE;MAClC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACpE,OAAO,CAACvC,iBAAiB,CAAC,MAAM;QACjCc,eAAe,CAAC3B,OAAO,CAACD,IAAI,IAAI;UAC5B,IAAI,CAACmH,eAAe,CAACnG,gBAAgB,CAAChB,IAAI,EAAE,IAAI,EAAE0B,4BAA4B,CAAC;QACnF,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAAC+F,0BAA0B,GAAG,IAAI;IAC1C;EACJ;EACA;EACAlB,uBAAuBA,CAACV,SAAS,EAAE;IAC/B,IAAIA,SAAS,CAACE,KAAK,KAAKpH,WAAW,CAACqH,SAAS,EAAE;MAC3C,IAAI,CAAC0B,uBAAuB,CAAC7B,SAAS,CAAC;IAC3C,CAAC,MACI,IAAIA,SAAS,CAACE,KAAK,KAAKpH,WAAW,CAACiI,UAAU,EAAE;MACjD,IAAI,CAACH,cAAc,CAACZ,SAAS,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;EACI6B,uBAAuBA,CAAC7B,SAAS,EAAE;IAC/B,MAAM8B,2BAA2B,GAAG9B,SAAS,KAAK,IAAI,CAACK,0BAA0B;IACjF,MAAM;MAAED;IAAW,CAAC,GAAGJ,SAAS,CAAC7G,MAAM;IACvC6G,SAAS,CAACE,KAAK,GAAGpH,WAAW,CAACiJ,OAAO;IACrC;IACA;IACA;IACA;IACA,IAAI,CAAC3B,UAAU,KAAK,CAAC0B,2BAA2B,IAAI,CAAC,IAAI,CAACE,cAAc,CAAC,EAAE;MACvEhC,SAAS,CAACzG,OAAO,CAAC,CAAC;IACvB;EACJ;EACA;EACAqH,cAAcA,CAACZ,SAAS,EAAE;IAAA,IAAAiC,qBAAA;IACtB,MAAM3B,cAAc,IAAA2B,qBAAA,GAAG,IAAI,CAACnB,cAAc,CAAC5G,GAAG,CAAC8F,SAAS,CAAC,cAAAiC,qBAAA,cAAAA,qBAAA,GAAI,IAAI;IACjE,IAAI,CAACnB,cAAc,CAACxF,MAAM,CAAC0E,SAAS,CAAC;IACrC;IACA,IAAI,CAAC,IAAI,CAACc,cAAc,CAACvF,IAAI,EAAE;MAC3B,IAAI,CAAC4C,cAAc,GAAG,IAAI;IAC9B;IACA;IACA;IACA,IAAI6B,SAAS,KAAK,IAAI,CAACK,0BAA0B,EAAE;MAC/C,IAAI,CAACA,0BAA0B,GAAG,IAAI;IAC1C;IACAL,SAAS,CAACE,KAAK,GAAGpH,WAAW,CAACQ,MAAM;IACpC,IAAIgH,cAAc,KAAK,IAAI,EAAE;MACzBN,SAAS,CAAC9G,OAAO,CAACsC,mBAAmB,CAAC,eAAe,EAAE8E,cAAc,CAACC,eAAe,CAAC;MACtFP,SAAS,CAAC9G,OAAO,CAACsC,mBAAmB,CAAC,kBAAkB,EAAE8E,cAAc,CAACK,kBAAkB,CAAC;MAC5F,IAAIL,cAAc,CAACE,aAAa,KAAK,IAAI,EAAE;QACvCC,YAAY,CAACH,cAAc,CAACE,aAAa,CAAC;MAC9C;IACJ;IACAR,SAAS,CAAC9G,OAAO,CAACgJ,MAAM,CAAC,CAAC;EAC9B;EACA;EACAT,YAAYA,CAAC3H,KAAK,EAAE;IAChB;IACA;IACA,MAAMqI,eAAe,GAAGzJ,+BAA+B,CAACoB,KAAK,CAAC;IAC9D,MAAMsI,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,IAC9CC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACF,oBAAoB,GAAGzG,wBAAwB;IACrE,IAAI,CAAC,IAAI,CAAC2B,OAAO,CAACiF,cAAc,IAAI,CAACL,eAAe,IAAI,CAACC,gBAAgB,EAAE;MACvE,IAAI,CAACJ,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACjE,YAAY,CAACjE,KAAK,CAAC2I,OAAO,EAAE3I,KAAK,CAAC4I,OAAO,EAAE,IAAI,CAACnF,OAAO,CAACoF,YAAY,CAAC;IAC9E;EACJ;EACA;EACAjB,aAAaA,CAAC5H,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAACyD,OAAO,CAACiF,cAAc,IAAI,CAAC7J,gCAAgC,CAACmB,KAAK,CAAC,EAAE;MAC1E;MACA;MACA;MACA,IAAI,CAACuI,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MACtC,IAAI,CAACP,cAAc,GAAG,IAAI;MAC1B;MACA;MACA,MAAMY,OAAO,GAAG9I,KAAK,CAAC+I,cAAc;MACpC;MACA;MACA,IAAID,OAAO,EAAE;QACT,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACrC,IAAI,CAAC/E,YAAY,CAAC6E,OAAO,CAACE,CAAC,CAAC,CAACL,OAAO,EAAEG,OAAO,CAACE,CAAC,CAAC,CAACJ,OAAO,EAAE,IAAI,CAACnF,OAAO,CAACoF,YAAY,CAAC;QACxF;MACJ;IACJ;EACJ;EACA;EACAhB,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACK,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,CAACA,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACb,iBAAiB,CAAC,CAAC,CAAC/G,OAAO,CAAC6E,MAAM,IAAI;MACvC;MACA;MACA,MAAM+D,SAAS,GAAG/D,MAAM,CAACiB,KAAK,KAAKpH,WAAW,CAACiJ,OAAO,IACjD9C,MAAM,CAAC9F,MAAM,CAAC8J,oBAAoB,IAAIhE,MAAM,CAACiB,KAAK,KAAKpH,WAAW,CAACqH,SAAU;MAClF,IAAI,CAAClB,MAAM,CAAC9F,MAAM,CAACiH,UAAU,IAAI4C,SAAS,EAAE;QACxC/D,MAAM,CAAC1F,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;EACN;EACA4H,iBAAiBA,CAAA,EAAG;IAChB,OAAO+B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACrC,cAAc,CAACsC,IAAI,CAAC,CAAC,CAAC;EACjD;EACA;EACA7B,oBAAoBA,CAAA,EAAG;IACnB,MAAM8B,OAAO,GAAG,IAAI,CAAC/B,eAAe;IACpC,IAAI+B,OAAO,EAAE;MACTvH,iBAAiB,CAAC1B,OAAO,CAACD,IAAI,IAAImD,cAAc,CAACkE,aAAa,CAACnG,aAAa,CAAClB,IAAI,EAAEkJ,OAAO,EAAE,IAAI,CAAC,CAAC;MAClG,IAAI,IAAI,CAACzB,0BAA0B,EAAE;QACjC7F,eAAe,CAAC3B,OAAO,CAACD,IAAI,IAAIkJ,OAAO,CAAC7H,mBAAmB,CAACrB,IAAI,EAAE,IAAI,EAAE0B,4BAA4B,CAAC,CAAC;QACtG,IAAI,CAAC+F,0BAA0B,GAAG,KAAK;MAC3C;IACJ;EACJ;AACJ;AACA;AACA;AACA;AAFAvI,eAAA,CA1UMiE,cAAc,mBA4BO,IAAI1D,kBAAkB,CAAC,CAAC;AAiTnD,SAASkF,wBAAwBA,CAACd,CAAC,EAAEC,CAAC,EAAEqF,IAAI,EAAE;EAC1C,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC1F,CAAC,GAAGsF,IAAI,CAAC7E,IAAI,CAAC,EAAE+E,IAAI,CAACE,GAAG,CAAC1F,CAAC,GAAGsF,IAAI,CAACK,KAAK,CAAC,CAAC;EACzE,MAAMC,KAAK,GAAGJ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACzF,CAAC,GAAGqF,IAAI,CAAC3E,GAAG,CAAC,EAAE6E,IAAI,CAACE,GAAG,CAACzF,CAAC,GAAGqF,IAAI,CAACO,MAAM,CAAC,CAAC;EACzE,OAAOL,IAAI,CAACM,IAAI,CAACP,KAAK,GAAGA,KAAK,GAAGK,KAAK,GAAGA,KAAK,CAAC;AACnD;;AAEA;AACA,MAAMG,yBAAyB,GAAG,IAAI7L,cAAc,CAAC,2BAA2B,CAAC;AACjF,MAAM8L,SAAS,CAAC;EAwBZ;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC/C,uBAAuB,CAAC,CAAC;IAClC;IACA,IAAI,CAAC8C,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACvC;EAEA;AACJ;AACA;AACA;EACI,IAAIf,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACgB,QAAQ,IAAI,IAAI,CAACC,WAAW,CAACC,aAAa;EAC1D;EACA,IAAIlB,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACgB,QAAQ,GAAGhB,OAAO;IACvB,IAAI,CAACe,4BAA4B,CAAC,CAAC;EACvC;EAQApL,WAAWA,CAAA,EAAG;IAAAK,eAAA,sBAxDAlB,MAAM,CAACC,UAAU,CAAC;IAAAiB,eAAA,yBACflB,MAAM,CAACE,qBAAqB,EAAE;MAAEmM,QAAQ,EAAE;IAAK,CAAC,CAAC;IAClE;IAAAnL,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;AACA;IAJIA,eAAA,iBAKS,CAAC;IACV;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAAAA,eAAA,oBAoBY,KAAK;IAAAA,eAAA;IAajB;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,yBACiB,KAAK;IAElB,MAAMqB,MAAM,GAAGvC,MAAM,CAACG,MAAM,CAAC;IAC7B,MAAMmM,QAAQ,GAAGtM,MAAM,CAACN,QAAQ,CAAC;IACjC,MAAM6M,aAAa,GAAGvM,MAAM,CAAC4L,yBAAyB,EAAE;MAAES,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3E,MAAM7G,QAAQ,GAAGxF,MAAM,CAACI,QAAQ,CAAC;IACjC;IACA;IACA,IAAI,CAACoM,cAAc,GAAGD,aAAa,IAAI,CAAC,CAAC;IACzC,IAAI,CAACE,eAAe,GAAG,IAAItH,cAAc,CAAC,IAAI,EAAE5C,MAAM,EAAE,IAAI,CAAC4J,WAAW,EAAEG,QAAQ,EAAE9G,QAAQ,CAAC;EACjG;EACAkH,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACV,4BAA4B,CAAC,CAAC;EACvC;EACAW,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,eAAe,CAACrD,oBAAoB,CAAC,CAAC;EAC/C;EACA;EACAL,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC0D,eAAe,CAAC1D,UAAU,CAAC,CAAC;EACrC;EACA;EACAE,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACwD,eAAe,CAACxD,uBAAuB,CAAC,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACI,IAAIuB,YAAYA,CAAA,EAAG;IACf,OAAO;MACHnE,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBK,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBQ,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBd,SAAS,EAAAD,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACF,IAAI,CAACqG,cAAc,CAACpG,SAAS,GAC5B,IAAI,CAACyG,cAAc,KAAK,gBAAgB,GAAG;QAAEtJ,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC,GAAG,CAAC,CAAC,GACtF,IAAI,CAAC4C,SAAS,CACpB;MACD0E,oBAAoB,EAAE,IAAI,CAAC0B,cAAc,CAAC1B;IAC9C,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI,IAAIT,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACyB,QAAQ,IAAI,CAAC,CAAC,IAAI,CAACU,cAAc,CAACV,QAAQ;EAC1D;EACA;EACAG,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,CAAC,IAAI,CAACH,QAAQ,IAAI,IAAI,CAACa,cAAc,EAAE;MACvC,IAAI,CAACF,eAAe,CAACvD,kBAAkB,CAAC,IAAI,CAACgC,OAAO,CAAC;IACzD;EACJ;EACA;EACA4B,MAAMA,CAACC,SAAS,EAAEjH,CAAC,GAAG,CAAC,EAAE9E,MAAM,EAAE;IAC7B,IAAI,OAAO+L,SAAS,KAAK,QAAQ,EAAE;MAC/B,OAAO,IAAI,CAACN,eAAe,CAAC7G,YAAY,CAACmH,SAAS,EAAEjH,CAAC,EAAAK,aAAA,CAAAA,aAAA,KAAO,IAAI,CAACqE,YAAY,GAAKxJ,MAAM,CAAE,CAAC;IAC/F,CAAC,MACI;MACD,OAAO,IAAI,CAACyL,eAAe,CAAC7G,YAAY,CAAC,CAAC,EAAE,CAAC,EAAAO,aAAA,CAAAA,aAAA,KAAO,IAAI,CAACqE,YAAY,GAAKuC,SAAS,CAAE,CAAC;IAC1F;EACJ;AAGJ;AAACC,UAAA,GA3HKnB,SAAS;AAAA3K,eAAA,CAAT2K,SAAS,wBAAAoB,mBAAAjJ,iBAAA;EAAA,YAAAA,iBAAA,IAyHwF6H,UAAS;AAAA;AAAA3K,eAAA,CAzH1G2K,SAAS,8BAhWkElM,EAAE,CAAAuN,iBAAA;EAAAlL,IAAA,EA0dQ6J,UAAS;EAAA3H,SAAA;EAAAC,SAAA;EAAAgJ,QAAA;EAAAC,YAAA,WAAAC,wBAAA7I,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA1dnB7E,EAAE,CAAA2N,WAAA,yBAAA7I,GAAA,CAAA8I,SA0dgB,CAAC;IAAA;EAAA;EAAAC,MAAA;IAAAtG,KAAA;IAAAqG,SAAA;IAAAlH,QAAA;IAAAK,MAAA;IAAAN,SAAA;IAAA0F,QAAA;IAAAZ,OAAA;EAAA;EAAAuC,QAAA;AAAA;AAEpG;EAAA,QAAA5I,SAAA,oBAAAA,SAAA,KA5diFlF,EAAE,CAAAmF,iBAAA,CA4dQ+G,SAAS,EAAc,CAAC;IACvG7J,IAAI,EAAE3B,SAAS;IACf0E,IAAI,EAAE,CAAC;MACC2I,QAAQ,EAAE,2BAA2B;MACrCD,QAAQ,EAAE,WAAW;MACrBvI,IAAI,EAAE;QACF,OAAO,EAAE,YAAY;QACrB,8BAA8B,EAAE;MACpC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEgC,KAAK,EAAE,CAAC;MAChDlF,IAAI,EAAE1B,KAAK;MACXyE,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEwI,SAAS,EAAE,CAAC;MACZvL,IAAI,EAAE1B,KAAK;MACXyE,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEsB,QAAQ,EAAE,CAAC;MACXrE,IAAI,EAAE1B,KAAK;MACXyE,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE2B,MAAM,EAAE,CAAC;MACT1E,IAAI,EAAE1B,KAAK;MACXyE,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEqB,SAAS,EAAE,CAAC;MACZpE,IAAI,EAAE1B,KAAK;MACXyE,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE+G,QAAQ,EAAE,CAAC;MACX9J,IAAI,EAAE1B,KAAK;MACXyE,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEmG,OAAO,EAAE,CAAC;MACVlJ,IAAI,EAAE1B,KAAK;MACXyE,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC;EAAE,CAAC;AAAA;AAEhB,SAAS8G,SAAS,IAAI8B,CAAC,EAAExI,cAAc,IAAIyI,CAAC,EAAEhC,yBAAyB,IAAIiC,CAAC,EAAElN,WAAW,IAAImN,CAAC,EAAElN,SAAS,IAAImN,CAAC,EAAEzK,4BAA4B,IAAI0K,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}