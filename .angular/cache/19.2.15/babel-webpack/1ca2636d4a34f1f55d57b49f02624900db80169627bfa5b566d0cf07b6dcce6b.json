{"ast": null, "code": "var _TestGridComponent;\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet TestGridComponent = (_TestGridComponent = class TestGridComponent {\n  constructor() {\n    this.testSchema = [{\n      field: 'foo',\n      title: 'Test column',\n      type: 'string',\n      isList: true\n    }];\n    this.testData = [{\n      foo: 'Initial'\n    }];\n  }\n  ngOnInit() {}\n  doReplaceData() {\n    this.testData = [{\n      foo: 'CHANGED'\n    }];\n  }\n  doEmptyData() {\n    this.testData = [];\n  }\n}, _TestGridComponent.ctorParameters = () => [], _TestGridComponent);\nTestGridComponent = __decorate([Component({\n  // tslint:disable-next-line:component-selector\n  selector: 'test-grid',\n  template: `<mat-card class=\"mat-elevation-z0\" style=\"margin: 32px\">\n    <lib-swui-grid [schema]=\"testSchema\" [data]=\"testData\" [columnsManagement]=\"false\" [pagination]=\"false\">\n      <div style=\"width:100%\">\n        <button mat-stroked-button (click)=\"doReplaceData()\" color=\"primary\">Replace Data</button>\n        <button style=\"margin-left:5px\" mat-stroked-button (click)=\"doEmptyData()\" color=\"warn\">Empty Data</button>\n      </div>\n    </lib-swui-grid>\n  </mat-card>\n  `,\n  standalone: false\n})], TestGridComponent);\nexport { TestGridComponent };", "map": {"version": 3, "names": ["__decorate", "Component", "TestGridComponent", "_TestGridComponent", "constructor", "testSchema", "field", "title", "type", "isList", "testData", "foo", "ngOnInit", "doReplaceData", "doEmptyData", "ctorParameters", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/_stories/test-grid/test-grid.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet TestGridComponent = class TestGridComponent {\n    constructor() {\n        this.testSchema = [\n            {\n                field: 'foo',\n                title: 'Test column',\n                type: 'string',\n                isList: true,\n            }\n        ];\n        this.testData = [\n            {\n                foo: 'Initial'\n            }\n        ];\n    }\n    ngOnInit() {\n    }\n    doReplaceData() {\n        this.testData = [{\n                foo: 'CHANGED'\n            }];\n    }\n    doEmptyData() {\n        this.testData = [];\n    }\n    static { this.ctorParameters = () => []; }\n};\nTestGridComponent = __decorate([\n    Component({\n        // tslint:disable-next-line:component-selector\n        selector: 'test-grid',\n        template: `<mat-card class=\"mat-elevation-z0\" style=\"margin: 32px\">\n    <lib-swui-grid [schema]=\"testSchema\" [data]=\"testData\" [columnsManagement]=\"false\" [pagination]=\"false\">\n      <div style=\"width:100%\">\n        <button mat-stroked-button (click)=\"doReplaceData()\" color=\"primary\">Replace Data</button>\n        <button style=\"margin-left:5px\" mat-stroked-button (click)=\"doEmptyData()\" color=\"warn\">Empty Data</button>\n      </div>\n    </lib-swui-grid>\n  </mat-card>\n  `,\n        standalone: false\n    })\n], TestGridComponent);\nexport { TestGridComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,QAAQ,eAAe;AACzC,IAAIC,iBAAiB,IAAAC,kBAAA,GAAG,MAAMD,iBAAiB,CAAC;EAC5CE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,CACd;MACIC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,aAAa;MACpBC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE;IACZ,CAAC,CACJ;IACD,IAAI,CAACC,QAAQ,GAAG,CACZ;MACIC,GAAG,EAAE;IACT,CAAC,CACJ;EACL;EACAC,QAAQA,CAAA,EAAG,CACX;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACH,QAAQ,GAAG,CAAC;MACTC,GAAG,EAAE;IACT,CAAC,CAAC;EACV;EACAG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACJ,QAAQ,GAAG,EAAE;EACtB;AAEJ,CAAC,EADYP,kBAAA,CAAKY,cAAc,GAAG,MAAM,EAAE,EAAAZ,kBAAA,CAC1C;AACDD,iBAAiB,GAAGF,UAAU,CAAC,CAC3BC,SAAS,CAAC;EACN;EACAe,QAAQ,EAAE,WAAW;EACrBC,QAAQ,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EACKC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEhB,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}