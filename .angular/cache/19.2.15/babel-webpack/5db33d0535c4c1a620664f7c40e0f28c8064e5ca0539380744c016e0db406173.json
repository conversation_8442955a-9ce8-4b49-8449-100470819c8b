{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Subscriber } from '../Subscriber';\nexport function createOperatorSubscriber(destination, onNext, onComplete, onError, onFinalize) {\n  return new OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize);\n}\nvar OperatorSubscriber = function (_super) {\n  __extends(OperatorSubscriber, _super);\n  function OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize, shouldUnsubscribe) {\n    var _this = _super.call(this, destination) || this;\n    _this.onFinalize = onFinalize;\n    _this.shouldUnsubscribe = shouldUnsubscribe;\n    _this._next = onNext ? function (value) {\n      try {\n        onNext(value);\n      } catch (err) {\n        destination.error(err);\n      }\n    } : _super.prototype._next;\n    _this._error = onError ? function (err) {\n      try {\n        onError(err);\n      } catch (err) {\n        destination.error(err);\n      } finally {\n        this.unsubscribe();\n      }\n    } : _super.prototype._error;\n    _this._complete = onComplete ? function () {\n      try {\n        onComplete();\n      } catch (err) {\n        destination.error(err);\n      } finally {\n        this.unsubscribe();\n      }\n    } : _super.prototype._complete;\n    return _this;\n  }\n  OperatorSubscriber.prototype.unsubscribe = function () {\n    var _a;\n    if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {\n      var closed_1 = this.closed;\n      _super.prototype.unsubscribe.call(this);\n      !closed_1 && ((_a = this.onFinalize) === null || _a === void 0 ? void 0 : _a.call(this));\n    }\n  };\n  return OperatorSubscriber;\n}(Subscriber);\nexport { OperatorSubscriber };\n//# sourceMappingURL=OperatorSubscriber.js.map", "map": {"version": 3, "names": ["__extends", "Subscriber", "createOperatorSubscriber", "destination", "onNext", "onComplete", "onError", "onFinalize", "OperatorSubscriber", "_super", "shouldUnsubscribe", "_this", "call", "_next", "value", "err", "error", "prototype", "_error", "unsubscribe", "_complete", "_a", "closed_1", "closed"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/operators/OperatorSubscriber.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { Subscriber } from '../Subscriber';\nexport function createOperatorSubscriber(destination, onNext, onComplete, onError, onFinalize) {\n    return new OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize);\n}\nvar OperatorSubscriber = (function (_super) {\n    __extends(OperatorSubscriber, _super);\n    function OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize, shouldUnsubscribe) {\n        var _this = _super.call(this, destination) || this;\n        _this.onFinalize = onFinalize;\n        _this.shouldUnsubscribe = shouldUnsubscribe;\n        _this._next = onNext\n            ? function (value) {\n                try {\n                    onNext(value);\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n            }\n            : _super.prototype._next;\n        _this._error = onError\n            ? function (err) {\n                try {\n                    onError(err);\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n                finally {\n                    this.unsubscribe();\n                }\n            }\n            : _super.prototype._error;\n        _this._complete = onComplete\n            ? function () {\n                try {\n                    onComplete();\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n                finally {\n                    this.unsubscribe();\n                }\n            }\n            : _super.prototype._complete;\n        return _this;\n    }\n    OperatorSubscriber.prototype.unsubscribe = function () {\n        var _a;\n        if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {\n            var closed_1 = this.closed;\n            _super.prototype.unsubscribe.call(this);\n            !closed_1 && ((_a = this.onFinalize) === null || _a === void 0 ? void 0 : _a.call(this));\n        }\n    };\n    return OperatorSubscriber;\n}(Subscriber));\nexport { OperatorSubscriber };\n//# sourceMappingURL=OperatorSubscriber.js.map"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,wBAAwBA,CAACC,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAE;EAC3F,OAAO,IAAIC,kBAAkB,CAACL,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,CAAC;AACvF;AACA,IAAIC,kBAAkB,GAAI,UAAUC,MAAM,EAAE;EACxCT,SAAS,CAACQ,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAACL,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEG,iBAAiB,EAAE;IACjG,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAET,WAAW,CAAC,IAAI,IAAI;IAClDQ,KAAK,CAACJ,UAAU,GAAGA,UAAU;IAC7BI,KAAK,CAACD,iBAAiB,GAAGA,iBAAiB;IAC3CC,KAAK,CAACE,KAAK,GAAGT,MAAM,GACd,UAAUU,KAAK,EAAE;MACf,IAAI;QACAV,MAAM,CAACU,KAAK,CAAC;MACjB,CAAC,CACD,OAAOC,GAAG,EAAE;QACRZ,WAAW,CAACa,KAAK,CAACD,GAAG,CAAC;MAC1B;IACJ,CAAC,GACCN,MAAM,CAACQ,SAAS,CAACJ,KAAK;IAC5BF,KAAK,CAACO,MAAM,GAAGZ,OAAO,GAChB,UAAUS,GAAG,EAAE;MACb,IAAI;QACAT,OAAO,CAACS,GAAG,CAAC;MAChB,CAAC,CACD,OAAOA,GAAG,EAAE;QACRZ,WAAW,CAACa,KAAK,CAACD,GAAG,CAAC;MAC1B,CAAC,SACO;QACJ,IAAI,CAACI,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,GACCV,MAAM,CAACQ,SAAS,CAACC,MAAM;IAC7BP,KAAK,CAACS,SAAS,GAAGf,UAAU,GACtB,YAAY;MACV,IAAI;QACAA,UAAU,CAAC,CAAC;MAChB,CAAC,CACD,OAAOU,GAAG,EAAE;QACRZ,WAAW,CAACa,KAAK,CAACD,GAAG,CAAC;MAC1B,CAAC,SACO;QACJ,IAAI,CAACI,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,GACCV,MAAM,CAACQ,SAAS,CAACG,SAAS;IAChC,OAAOT,KAAK;EAChB;EACAH,kBAAkB,CAACS,SAAS,CAACE,WAAW,GAAG,YAAY;IACnD,IAAIE,EAAE;IACN,IAAI,CAAC,IAAI,CAACX,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC,CAAC,EAAE;MACrD,IAAIY,QAAQ,GAAG,IAAI,CAACC,MAAM;MAC1Bd,MAAM,CAACQ,SAAS,CAACE,WAAW,CAACP,IAAI,CAAC,IAAI,CAAC;MACvC,CAACU,QAAQ,KAAK,CAACD,EAAE,GAAG,IAAI,CAACd,UAAU,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACT,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5F;EACJ,CAAC;EACD,OAAOJ,kBAAkB;AAC7B,CAAC,CAACP,UAAU,CAAE;AACd,SAASO,kBAAkB;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}