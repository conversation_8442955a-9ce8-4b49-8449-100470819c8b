{"ast": null, "code": "import { SchemaFilterMatchEnum } from '../../swui-schema-top-filter/swui-schema-top-filter.model';\nconst USER_STATUS_CLASS_MAP = {\n  normal: 'sw-chip sw-chip-green',\n  suspended: 'sw-chip sw-chip-blue'\n};\nexport const USER_STATUS_LIST = [{\n  id: 'normal',\n  code: 'normal',\n  displayName: 'ENTITY_SETUP.USERS.statusActive',\n  hidden: false\n}, {\n  id: 'suspended',\n  code: 'suspended',\n  displayName: 'ENTITY_SETUP.USERS.statusInactive',\n  hidden: false\n}, {\n  id: 'test',\n  code: 'test',\n  displayName: 'test',\n  hidden: true\n}];\nconst SCHEMA = [{\n  field: 'username',\n  title: 'Username',\n  type: 'string',\n  isList: true,\n  isViewable: true,\n  isSortable: true,\n  isFilterable: true,\n  isListVisible: true,\n  class: 'test',\n  td: {\n    type: 'string',\n    nowrap: true,\n    truncate: {\n      maxLength: 20,\n      isEllipsis: true\n    }\n  },\n  alignment: {\n    th: 'left',\n    td: 'left'\n  }\n}, {\n  field: 'calcTest',\n  title: 'calcTest',\n  type: 'string',\n  isList: true,\n  isViewable: true,\n  isSortable: true,\n  isFilterable: true,\n  isListVisible: true,\n  td: {\n    type: 'calc',\n    titleFn: row => row.calcTest || '-',\n    truncate: {\n      maxLength: 20,\n      isEllipsis: true\n    }\n  },\n  alignment: {\n    th: 'left',\n    td: 'left'\n  }\n}, {\n  field: 'email',\n  title: 'E-mail',\n  type: 'string',\n  placeholder: '<EMAIL>',\n  dataSource: '',\n  isList: true,\n  isViewable: true,\n  isSortable: true,\n  isFilterable: true,\n  form: {\n    pattern: '^\\\\S+@\\\\S+$'\n  },\n  alignment: {\n    th: 'right',\n    td: 'right'\n  }\n}, {\n  field: 'status',\n  title: 'COMPONENTS.GRID.STATUS',\n  type: 'select',\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: true,\n  data: USER_STATUS_LIST,\n  td: {\n    type: 'status',\n    statusList: USER_STATUS_LIST,\n    classMap: USER_STATUS_CLASS_MAP,\n    readonlyFn: row => {\n      return row.status === 'test';\n    }\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'active',\n  title: 'Active',\n  type: 'select',\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: true,\n  td: {\n    type: 'inactivity'\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'image',\n  title: 'Image',\n  type: 'base64image',\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: true,\n  td: {\n    type: 'image'\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n}, {\n  field: 'activity',\n  title: 'Calc inactivity',\n  type: 'select',\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: true,\n  td: {\n    type: 'inactivity',\n    valueFn: (row, schema) => row[schema.field] === 'enabled'\n  },\n  alignment: {\n    th: 'center',\n    td: 'center'\n  }\n},\n// Dates\n{\n  field: 'createdAt',\n  title: 'Created At',\n  type: 'datetimerange',\n  scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',\n  config: {\n    timePicker: true\n  },\n  td: {\n    type: 'timestamp',\n    showTimeZone: true\n  },\n  alignment: {\n    th: 'right',\n    td: 'right'\n  },\n  filterMatch: {\n    from: SchemaFilterMatchEnum.GreaterThanEquals,\n    to: SchemaFilterMatchEnum.LessThan\n  },\n  isList: true,\n  isViewable: true,\n  isSortable: true,\n  isFilterable: true\n}, {\n  field: 'updatedAt',\n  title: 'Updated At',\n  type: 'date',\n  scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',\n  config: {\n    timePicker: true\n  },\n  td: {\n    type: 'timestamp',\n    showTimeZone: true\n  },\n  alignment: {\n    th: 'right',\n    td: 'right'\n  },\n  isList: true,\n  isViewable: true,\n  isSortable: true,\n  isFilterable: true\n}];\nexport const SCHEMA_LIST = SCHEMA.filter(({\n  isList\n}) => isList);\nexport const percentFieldExample = {\n  field: 'profileFilled',\n  title: 'Percent',\n  td: {\n    type: 'percent'\n  },\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: false\n};\nexport const operatorsFieldExample = {\n  field: 'operators',\n  title: 'Operators',\n  td: {\n    type: 'list'\n  },\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: false\n};\nexport const currencyFieldExample = {\n  field: 'bnsRedeemed',\n  title: 'Currency',\n  type: 'string',\n  td: {\n    type: 'currency'\n  },\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: false\n};\nexport const iconFieldExample = {\n  field: 'iconField',\n  title: 'Icon',\n  td: {\n    type: 'icon',\n    icon: 'search',\n    titleFn: () => 'Test title',\n    classFn: () => 'testClass',\n    linkFn: () => []\n  },\n  alignment: {\n    td: 'center',\n    th: 'center'\n  },\n  isList: true,\n  isViewable: true,\n  isSortable: false,\n  isFilterable: false\n};", "map": {"version": 3, "names": ["SchemaFilterMatchEnum", "USER_STATUS_CLASS_MAP", "normal", "suspended", "USER_STATUS_LIST", "id", "code", "displayName", "hidden", "SCHEMA", "field", "title", "type", "isList", "isViewable", "isSortable", "isFilterable", "isListVisible", "class", "td", "nowrap", "truncate", "max<PERSON><PERSON><PERSON>", "isEllipsis", "alignment", "th", "titleFn", "row", "calcTest", "placeholder", "dataSource", "form", "pattern", "data", "statusList", "classMap", "readonlyFn", "status", "valueFn", "schema", "scope", "config", "timePicker", "showTimeZone", "filterMatch", "from", "GreaterThanEquals", "to", "<PERSON><PERSON><PERSON>", "SCHEMA_LIST", "filter", "percentFieldExample", "operatorsFieldExample", "currencyFieldExample", "icon<PERSON>ieldExample", "icon", "classFn", "linkFn"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/_stories/users-example.schema.ts"], "sourcesContent": ["import { SchemaFilterMatchEnum } from '../../swui-schema-top-filter/swui-schema-top-filter.model';\nconst USER_STATUS_CLASS_MAP = {\n    normal: 'sw-chip sw-chip-green',\n    suspended: 'sw-chip sw-chip-blue'\n};\nexport const USER_STATUS_LIST = [\n    { id: 'normal', code: 'normal', displayName: 'ENTITY_SETUP.USERS.statusActive', hidden: false },\n    { id: 'suspended', code: 'suspended', displayName: 'ENTITY_SETUP.USERS.statusInactive', hidden: false },\n    { id: 'test', code: 'test', displayName: 'test', hidden: true }\n];\nconst SCHEMA = [\n    {\n        field: 'username',\n        title: 'Username',\n        type: 'string',\n        isList: true,\n        isViewable: true,\n        isSortable: true,\n        isFilterable: true,\n        isListVisible: true,\n        class: 'test',\n        td: {\n            type: 'string',\n            nowrap: true,\n            truncate: {\n                maxLength: 20,\n                isEllipsis: true,\n            }\n        },\n        alignment: {\n            th: 'left',\n            td: 'left',\n        },\n    },\n    {\n        field: 'calcTest',\n        title: 'calcTest',\n        type: 'string',\n        isList: true,\n        isViewable: true,\n        isSortable: true,\n        isFilterable: true,\n        isListVisible: true,\n        td: {\n            type: 'calc',\n            titleFn: (row) => row.calcTest || '-',\n            truncate: {\n                maxLength: 20,\n                isEllipsis: true,\n            }\n        },\n        alignment: {\n            th: 'left',\n            td: 'left',\n        },\n    },\n    {\n        field: 'email',\n        title: 'E-mail',\n        type: 'string',\n        placeholder: '<EMAIL>',\n        dataSource: '',\n        isList: true,\n        isViewable: true,\n        isSortable: true,\n        isFilterable: true,\n        form: {\n            pattern: '^\\\\S+@\\\\S+$',\n        },\n        alignment: {\n            th: 'right',\n            td: 'right',\n        },\n    },\n    {\n        field: 'status',\n        title: 'COMPONENTS.GRID.STATUS',\n        type: 'select',\n        isList: true,\n        isViewable: true,\n        isSortable: false,\n        isFilterable: true,\n        data: USER_STATUS_LIST,\n        td: {\n            type: 'status',\n            statusList: USER_STATUS_LIST,\n            classMap: USER_STATUS_CLASS_MAP,\n            readonlyFn: (row) => {\n                return row.status === 'test';\n            }\n        },\n        alignment: {\n            th: 'center',\n            td: 'center',\n        },\n    },\n    {\n        field: 'active',\n        title: 'Active',\n        type: 'select',\n        isList: true,\n        isViewable: true,\n        isSortable: false,\n        isFilterable: true,\n        td: {\n            type: 'inactivity',\n        },\n        alignment: {\n            th: 'center',\n            td: 'center',\n        },\n    },\n    {\n        field: 'image',\n        title: 'Image',\n        type: 'base64image',\n        isList: true,\n        isViewable: true,\n        isSortable: false,\n        isFilterable: true,\n        td: {\n            type: 'image',\n        },\n        alignment: {\n            th: 'center',\n            td: 'center',\n        },\n    },\n    {\n        field: 'activity',\n        title: 'Calc inactivity',\n        type: 'select',\n        isList: true,\n        isViewable: true,\n        isSortable: false,\n        isFilterable: true,\n        td: {\n            type: 'inactivity',\n            valueFn: (row, schema) => row[schema.field] === 'enabled',\n        },\n        alignment: {\n            th: 'center',\n            td: 'center',\n        },\n    },\n    // Dates\n    {\n        field: 'createdAt',\n        title: 'Created At',\n        type: 'datetimerange',\n        scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',\n        config: {\n            timePicker: true,\n        },\n        td: {\n            type: 'timestamp',\n            showTimeZone: true\n        },\n        alignment: {\n            th: 'right',\n            td: 'right',\n        },\n        filterMatch: {\n            from: SchemaFilterMatchEnum.GreaterThanEquals,\n            to: SchemaFilterMatchEnum.LessThan,\n        },\n        isList: true,\n        isViewable: true,\n        isSortable: true,\n        isFilterable: true,\n    },\n    {\n        field: 'updatedAt',\n        title: 'Updated At',\n        type: 'date',\n        scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',\n        config: {\n            timePicker: true,\n        },\n        td: {\n            type: 'timestamp',\n            showTimeZone: true\n        },\n        alignment: {\n            th: 'right',\n            td: 'right',\n        },\n        isList: true,\n        isViewable: true,\n        isSortable: true,\n        isFilterable: true,\n    },\n];\nexport const SCHEMA_LIST = SCHEMA.filter(({ isList }) => isList);\nexport const percentFieldExample = {\n    field: 'profileFilled',\n    title: 'Percent',\n    td: {\n        type: 'percent',\n    },\n    isList: true,\n    isViewable: true,\n    isSortable: false,\n    isFilterable: false,\n};\nexport const operatorsFieldExample = {\n    field: 'operators',\n    title: 'Operators',\n    td: {\n        type: 'list',\n    },\n    isList: true,\n    isViewable: true,\n    isSortable: false,\n    isFilterable: false,\n};\nexport const currencyFieldExample = {\n    field: 'bnsRedeemed',\n    title: 'Currency',\n    type: 'string',\n    td: {\n        type: 'currency',\n    },\n    isList: true,\n    isViewable: true,\n    isSortable: false,\n    isFilterable: false,\n};\nexport const iconFieldExample = {\n    field: 'iconField',\n    title: 'Icon',\n    td: {\n        type: 'icon',\n        icon: 'search',\n        titleFn: () => 'Test title',\n        classFn: () => 'testClass',\n        linkFn: () => []\n    },\n    alignment: {\n        td: 'center',\n        th: 'center'\n    },\n    isList: true,\n    isViewable: true,\n    isSortable: false,\n    isFilterable: false,\n};\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,2DAA2D;AACjG,MAAMC,qBAAqB,GAAG;EAC1BC,MAAM,EAAE,uBAAuB;EAC/BC,SAAS,EAAE;AACf,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAG,CAC5B;EAAEC,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAE,QAAQ;EAAEC,WAAW,EAAE,iCAAiC;EAAEC,MAAM,EAAE;AAAM,CAAC,EAC/F;EAAEH,EAAE,EAAE,WAAW;EAAEC,IAAI,EAAE,WAAW;EAAEC,WAAW,EAAE,mCAAmC;EAAEC,MAAM,EAAE;AAAM,CAAC,EACvG;EAAEH,EAAE,EAAE,MAAM;EAAEC,IAAI,EAAE,MAAM;EAAEC,WAAW,EAAE,MAAM;EAAEC,MAAM,EAAE;AAAK,CAAC,CAClE;AACD,MAAMC,MAAM,GAAG,CACX;EACIC,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,IAAI;EACnBC,KAAK,EAAE,MAAM;EACbC,EAAE,EAAE;IACAP,IAAI,EAAE,QAAQ;IACdQ,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE;MACNC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE;IAChB;EACJ,CAAC;EACDC,SAAS,EAAE;IACPC,EAAE,EAAE,MAAM;IACVN,EAAE,EAAE;EACR;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,IAAI;EACnBE,EAAE,EAAE;IACAP,IAAI,EAAE,MAAM;IACZc,OAAO,EAAGC,GAAG,IAAKA,GAAG,CAACC,QAAQ,IAAI,GAAG;IACrCP,QAAQ,EAAE;MACNC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE;IAChB;EACJ,CAAC;EACDC,SAAS,EAAE;IACPC,EAAE,EAAE,MAAM;IACVN,EAAE,EAAE;EACR;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAE,QAAQ;EACdiB,WAAW,EAAE,kBAAkB;EAC/BC,UAAU,EAAE,EAAE;EACdjB,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBe,IAAI,EAAE;IACFC,OAAO,EAAE;EACb,CAAC;EACDR,SAAS,EAAE;IACPC,EAAE,EAAE,OAAO;IACXN,EAAE,EAAE;EACR;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,wBAAwB;EAC/BC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBiB,IAAI,EAAE7B,gBAAgB;EACtBe,EAAE,EAAE;IACAP,IAAI,EAAE,QAAQ;IACdsB,UAAU,EAAE9B,gBAAgB;IAC5B+B,QAAQ,EAAElC,qBAAqB;IAC/BmC,UAAU,EAAGT,GAAG,IAAK;MACjB,OAAOA,GAAG,CAACU,MAAM,KAAK,MAAM;IAChC;EACJ,CAAC;EACDb,SAAS,EAAE;IACPC,EAAE,EAAE,QAAQ;IACZN,EAAE,EAAE;EACR;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBG,EAAE,EAAE;IACAP,IAAI,EAAE;EACV,CAAC;EACDY,SAAS,EAAE;IACPC,EAAE,EAAE,QAAQ;IACZN,EAAE,EAAE;EACR;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBG,EAAE,EAAE;IACAP,IAAI,EAAE;EACV,CAAC;EACDY,SAAS,EAAE;IACPC,EAAE,EAAE,QAAQ;IACZN,EAAE,EAAE;EACR;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,UAAU;EACjBC,KAAK,EAAE,iBAAiB;EACxBC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,IAAI;EAClBG,EAAE,EAAE;IACAP,IAAI,EAAE,YAAY;IAClB0B,OAAO,EAAEA,CAACX,GAAG,EAAEY,MAAM,KAAKZ,GAAG,CAACY,MAAM,CAAC7B,KAAK,CAAC,KAAK;EACpD,CAAC;EACDc,SAAS,EAAE;IACPC,EAAE,EAAE,QAAQ;IACZN,EAAE,EAAE;EACR;AACJ,CAAC;AACD;AACA;EACIT,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,eAAe;EACrB4B,KAAK,EAAE,sCAAsC;EAC7CC,MAAM,EAAE;IACJC,UAAU,EAAE;EAChB,CAAC;EACDvB,EAAE,EAAE;IACAP,IAAI,EAAE,WAAW;IACjB+B,YAAY,EAAE;EAClB,CAAC;EACDnB,SAAS,EAAE;IACPC,EAAE,EAAE,OAAO;IACXN,EAAE,EAAE;EACR,CAAC;EACDyB,WAAW,EAAE;IACTC,IAAI,EAAE7C,qBAAqB,CAAC8C,iBAAiB;IAC7CC,EAAE,EAAE/C,qBAAqB,CAACgD;EAC9B,CAAC;EACDnC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE;AAClB,CAAC,EACD;EACIN,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,MAAM;EACZ4B,KAAK,EAAE,sCAAsC;EAC7CC,MAAM,EAAE;IACJC,UAAU,EAAE;EAChB,CAAC;EACDvB,EAAE,EAAE;IACAP,IAAI,EAAE,WAAW;IACjB+B,YAAY,EAAE;EAClB,CAAC;EACDnB,SAAS,EAAE;IACPC,EAAE,EAAE,OAAO;IACXN,EAAE,EAAE;EACR,CAAC;EACDN,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE;AAClB,CAAC,CACJ;AACD,OAAO,MAAMiC,WAAW,GAAGxC,MAAM,CAACyC,MAAM,CAAC,CAAC;EAAErC;AAAO,CAAC,KAAKA,MAAM,CAAC;AAChE,OAAO,MAAMsC,mBAAmB,GAAG;EAC/BzC,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,SAAS;EAChBQ,EAAE,EAAE;IACAP,IAAI,EAAE;EACV,CAAC;EACDC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE;AAClB,CAAC;AACD,OAAO,MAAMoC,qBAAqB,GAAG;EACjC1C,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,WAAW;EAClBQ,EAAE,EAAE;IACAP,IAAI,EAAE;EACV,CAAC;EACDC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE;AAClB,CAAC;AACD,OAAO,MAAMqC,oBAAoB,GAAG;EAChC3C,KAAK,EAAE,aAAa;EACpBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAE,QAAQ;EACdO,EAAE,EAAE;IACAP,IAAI,EAAE;EACV,CAAC;EACDC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE;AAClB,CAAC;AACD,OAAO,MAAMsC,gBAAgB,GAAG;EAC5B5C,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,MAAM;EACbQ,EAAE,EAAE;IACAP,IAAI,EAAE,MAAM;IACZ2C,IAAI,EAAE,QAAQ;IACd7B,OAAO,EAAEA,CAAA,KAAM,YAAY;IAC3B8B,OAAO,EAAEA,CAAA,KAAM,WAAW;IAC1BC,MAAM,EAAEA,CAAA,KAAM;EAClB,CAAC;EACDjC,SAAS,EAAE;IACPL,EAAE,EAAE,QAAQ;IACZM,EAAE,EAAE;EACR,CAAC;EACDZ,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}