{"ast": null, "code": "var _ControlInputComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./control-input.component.html?ngResource\";\nimport { ChangeDetectionStrategy, Component, Input } from '@angular/core';\nlet ControlInputComponent = (_ControlInputComponent = class ControlInputComponent {\n  constructor() {\n    this.prefixId = '';\n    this.readonly = false;\n    this.submitted = false;\n  }\n  get type() {\n    var _this$option;\n    const type = (_this$option = this.option) === null || _this$option === void 0 ? void 0 : _this$option.type;\n    if (type) {\n      if (['text', 'url', 'textarea', 'string'].includes(type)) {\n        return 'text';\n      }\n      if (['select', 'multiselect'].includes(type)) {\n        return 'select';\n      }\n      if (['daterange', 'datetimerange'].includes(type)) {\n        return 'daterange';\n      }\n    }\n    return type;\n  }\n  get formControl() {\n    var _this$item;\n    return (_this$item = this.item) === null || _this$item === void 0 ? void 0 : _this$item.control;\n  }\n  get option() {\n    var _this$item2;\n    return (_this$item2 = this.item) === null || _this$item2 === void 0 ? void 0 : _this$item2.option;\n  }\n  get id() {\n    var _this$option2;\n    return this.prefixId + (((_this$option2 = this.option) === null || _this$option2 === void 0 ? void 0 : _this$option2.key) || '');\n  }\n}, _ControlInputComponent.propDecorators = {\n  item: [{\n    type: Input\n  }],\n  prefixId: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }]\n}, _ControlInputComponent);\nControlInputComponent = __decorate([Component({\n  selector: 'lib-control-input',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false\n})], ControlInputComponent);\nexport { ControlInputComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "ChangeDetectionStrategy", "Component", "Input", "ControlInputComponent", "_ControlInputComponent", "constructor", "prefixId", "readonly", "submitted", "type", "_this$option", "option", "includes", "formControl", "_this$item", "item", "control", "_this$item2", "id", "_this$option2", "key", "propDecorators", "selector", "template", "changeDetection", "OnPush", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/control-input/control-input.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./control-input.component.html?ngResource\";\nimport { ChangeDetectionStrategy, Component, Input } from '@angular/core';\nlet ControlInputComponent = class ControlInputComponent {\n    constructor() {\n        this.prefixId = '';\n        this.readonly = false;\n        this.submitted = false;\n    }\n    get type() {\n        const type = this.option?.type;\n        if (type) {\n            if (['text', 'url', 'textarea', 'string'].includes(type)) {\n                return 'text';\n            }\n            if (['select', 'multiselect'].includes(type)) {\n                return 'select';\n            }\n            if (['daterange', 'datetimerange'].includes(type)) {\n                return 'daterange';\n            }\n        }\n        return type;\n    }\n    get formControl() {\n        return this.item?.control;\n    }\n    get option() {\n        return this.item?.option;\n    }\n    get id() {\n        return this.prefixId + (this.option?.key || '');\n    }\n    static { this.propDecorators = {\n        item: [{ type: Input }],\n        prefixId: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }]\n    }; }\n};\nControlInputComponent = __decorate([\n    Component({\n        selector: 'lib-control-input',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false\n    })\n], ControlInputComponent);\nexport { ControlInputComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,SAASC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACzE,IAAIC,qBAAqB,IAAAC,sBAAA,GAAG,MAAMD,qBAAqB,CAAC;EACpDE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;EAC1B;EACA,IAAIC,IAAIA,CAAA,EAAG;IAAA,IAAAC,YAAA;IACP,MAAMD,IAAI,IAAAC,YAAA,GAAG,IAAI,CAACC,MAAM,cAAAD,YAAA,uBAAXA,YAAA,CAAaD,IAAI;IAC9B,IAAIA,IAAI,EAAE;MACN,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC,CAACG,QAAQ,CAACH,IAAI,CAAC,EAAE;QACtD,OAAO,MAAM;MACjB;MACA,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAACG,QAAQ,CAACH,IAAI,CAAC,EAAE;QAC1C,OAAO,QAAQ;MACnB;MACA,IAAI,CAAC,WAAW,EAAE,eAAe,CAAC,CAACG,QAAQ,CAACH,IAAI,CAAC,EAAE;QAC/C,OAAO,WAAW;MACtB;IACJ;IACA,OAAOA,IAAI;EACf;EACA,IAAII,WAAWA,CAAA,EAAG;IAAA,IAAAC,UAAA;IACd,QAAAA,UAAA,GAAO,IAAI,CAACC,IAAI,cAAAD,UAAA,uBAATA,UAAA,CAAWE,OAAO;EAC7B;EACA,IAAIL,MAAMA,CAAA,EAAG;IAAA,IAAAM,WAAA;IACT,QAAAA,WAAA,GAAO,IAAI,CAACF,IAAI,cAAAE,WAAA,uBAATA,WAAA,CAAWN,MAAM;EAC5B;EACA,IAAIO,EAAEA,CAAA,EAAG;IAAA,IAAAC,aAAA;IACL,OAAO,IAAI,CAACb,QAAQ,IAAI,EAAAa,aAAA,OAAI,CAACR,MAAM,cAAAQ,aAAA,uBAAXA,aAAA,CAAaC,GAAG,KAAI,EAAE,CAAC;EACnD;AAOJ,CAAC,EANYhB,sBAAA,CAAKiB,cAAc,GAAG;EAC3BN,IAAI,EAAE,CAAC;IAAEN,IAAI,EAAEP;EAAM,CAAC,CAAC;EACvBI,QAAQ,EAAE,CAAC;IAAEG,IAAI,EAAEP;EAAM,CAAC,CAAC;EAC3BK,QAAQ,EAAE,CAAC;IAAEE,IAAI,EAAEP;EAAM,CAAC,CAAC;EAC3BM,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAEP;EAAM,CAAC;AAC/B,CAAC,EAAAE,sBAAA,CACJ;AACDD,qBAAqB,GAAGL,UAAU,CAAC,CAC/BG,SAAS,CAAC;EACNqB,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAExB,oBAAoB;EAC9ByB,eAAe,EAAExB,uBAAuB,CAACyB,MAAM;EAC/CC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEvB,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}