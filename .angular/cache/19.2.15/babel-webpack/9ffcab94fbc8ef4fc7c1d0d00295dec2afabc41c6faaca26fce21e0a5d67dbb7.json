{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { DocsComponent } from './docs.component';\nimport { DocsTypographyComponent } from './docs-typography/docs-typography.component';\nimport { DocsAdditionalColorsComponent } from './docs-additional-colors/docs-additional-colors.component';\nimport { DocsChipComponent } from './docs-chip/docs-chip.component';\nimport { DocsIconsComponent } from './docs-icons/docs-icons.component';\nimport { DocsButtonsComponent } from './docs-buttons/docs-buttons.component';\nimport { MatButtonModule } from '@angular/material/button';\nlet DocsModule = class DocsModule {};\nDocsModule = __decorate([NgModule({\n  declarations: [DocsComponent, DocsTypographyComponent, DocsAdditionalColorsComponent, DocsChipComponent, DocsIconsComponent, DocsButtonsComponent],\n  exports: [DocsComponent, DocsTypographyComponent, DocsAdditionalColorsComponent, DocsChipComponent, DocsIconsComponent, DocsButtonsComponent],\n  imports: [CommonModule, MatListModule, MatIconModule, MatCardModule, MatButtonModule, MatIconModule]\n})], DocsModule);\nexport { DocsModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "MatCardModule", "MatIconModule", "MatListModule", "DocsComponent", "DocsTypographyComponent", "DocsAdditionalColorsComponent", "DocsChipComponent", "DocsIconsComponent", "DocsButtonsComponent", "MatButtonModule", "DocsModule", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/styles/docs/docs.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { DocsComponent } from './docs.component';\nimport { DocsTypographyComponent } from './docs-typography/docs-typography.component';\nimport { DocsAdditionalColorsComponent } from './docs-additional-colors/docs-additional-colors.component';\nimport { DocsChipComponent } from './docs-chip/docs-chip.component';\nimport { DocsIconsComponent } from './docs-icons/docs-icons.component';\nimport { DocsButtonsComponent } from './docs-buttons/docs-buttons.component';\nimport { MatButtonModule } from '@angular/material/button';\nlet DocsModule = class DocsModule {\n};\nDocsModule = __decorate([\n    NgModule({\n        declarations: [\n            DocsComponent,\n            DocsTypographyComponent,\n            DocsAdditionalColorsComponent,\n            DocsChipComponent,\n            DocsIconsComponent,\n            DocsButtonsComponent,\n        ],\n        exports: [\n            DocsComponent,\n            DocsTypographyComponent,\n            DocsAdditionalColorsComponent,\n            DocsChipComponent,\n            DocsIconsComponent,\n            DocsButtonsComponent,\n        ],\n        imports: [\n            CommonModule,\n            MatListModule,\n            MatIconModule,\n            MatCardModule,\n            MatButtonModule,\n            MatIconModule,\n        ]\n    })\n], DocsModule);\nexport { DocsModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,IAAIC,UAAU,GAAG,MAAMA,UAAU,CAAC,EACjC;AACDA,UAAU,GAAGb,UAAU,CAAC,CACpBC,QAAQ,CAAC;EACLa,YAAY,EAAE,CACVR,aAAa,EACbC,uBAAuB,EACvBC,6BAA6B,EAC7BC,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,CACvB;EACDI,OAAO,EAAE,CACLT,aAAa,EACbC,uBAAuB,EACvBC,6BAA6B,EAC7BC,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,CACvB;EACDK,OAAO,EAAE,CACLd,YAAY,EACZG,aAAa,EACbD,aAAa,EACbD,aAAa,EACbS,eAAe,EACfR,aAAa;AAErB,CAAC,CAAC,CACL,EAAES,UAAU,CAAC;AACd,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}