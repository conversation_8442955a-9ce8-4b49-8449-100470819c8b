{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar AnimationFrameScheduler = function (_super) {\n  __extends(AnimationFrameScheduler, _super);\n  function AnimationFrameScheduler() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  AnimationFrameScheduler.prototype.flush = function (action) {\n    this._active = true;\n    var flushId;\n    if (action) {\n      flushId = action.id;\n    } else {\n      flushId = this._scheduled;\n      this._scheduled = undefined;\n    }\n    var actions = this.actions;\n    var error;\n    action = action || actions.shift();\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n    this._active = false;\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  return AnimationFrameScheduler;\n}(AsyncScheduler);\nexport { AnimationFrameScheduler };\n//# sourceMappingURL=AnimationFrameScheduler.js.map", "map": {"version": 3, "names": ["__extends", "AsyncScheduler", "AnimationFrameScheduler", "_super", "apply", "arguments", "prototype", "flush", "action", "_active", "flushId", "id", "_scheduled", "undefined", "actions", "error", "shift", "execute", "state", "delay", "unsubscribe"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/AnimationFrameScheduler.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar AnimationFrameScheduler = (function (_super) {\n    __extends(AnimationFrameScheduler, _super);\n    function AnimationFrameScheduler() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AnimationFrameScheduler.prototype.flush = function (action) {\n        this._active = true;\n        var flushId;\n        if (action) {\n            flushId = action.id;\n        }\n        else {\n            flushId = this._scheduled;\n            this._scheduled = undefined;\n        }\n        var actions = this.actions;\n        var error;\n        action = action || actions.shift();\n        do {\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        } while ((action = actions[0]) && action.id === flushId && actions.shift());\n        this._active = false;\n        if (error) {\n            while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    };\n    return AnimationFrameScheduler;\n}(AsyncScheduler));\nexport { AnimationFrameScheduler };\n//# sourceMappingURL=AnimationFrameScheduler.js.map"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,IAAIC,uBAAuB,GAAI,UAAUC,MAAM,EAAE;EAC7CH,SAAS,CAACE,uBAAuB,EAAEC,MAAM,CAAC;EAC1C,SAASD,uBAAuBA,CAAA,EAAG;IAC/B,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACnE;EACAH,uBAAuB,CAACI,SAAS,CAACC,KAAK,GAAG,UAAUC,MAAM,EAAE;IACxD,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAIC,OAAO;IACX,IAAIF,MAAM,EAAE;MACRE,OAAO,GAAGF,MAAM,CAACG,EAAE;IACvB,CAAC,MACI;MACDD,OAAO,GAAG,IAAI,CAACE,UAAU;MACzB,IAAI,CAACA,UAAU,GAAGC,SAAS;IAC/B;IACA,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIC,KAAK;IACTP,MAAM,GAAGA,MAAM,IAAIM,OAAO,CAACE,KAAK,CAAC,CAAC;IAClC,GAAG;MACC,IAAKD,KAAK,GAAGP,MAAM,CAACS,OAAO,CAACT,MAAM,CAACU,KAAK,EAAEV,MAAM,CAACW,KAAK,CAAC,EAAG;QACtD;MACJ;IACJ,CAAC,QAAQ,CAACX,MAAM,GAAGM,OAAO,CAAC,CAAC,CAAC,KAAKN,MAAM,CAACG,EAAE,KAAKD,OAAO,IAAII,OAAO,CAACE,KAAK,CAAC,CAAC;IAC1E,IAAI,CAACP,OAAO,GAAG,KAAK;IACpB,IAAIM,KAAK,EAAE;MACP,OAAO,CAACP,MAAM,GAAGM,OAAO,CAAC,CAAC,CAAC,KAAKN,MAAM,CAACG,EAAE,KAAKD,OAAO,IAAII,OAAO,CAACE,KAAK,CAAC,CAAC,EAAE;QACtER,MAAM,CAACY,WAAW,CAAC,CAAC;MACxB;MACA,MAAML,KAAK;IACf;EACJ,CAAC;EACD,OAAOb,uBAAuB;AAClC,CAAC,CAACD,cAAc,CAAE;AAClB,SAASC,uBAAuB;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}