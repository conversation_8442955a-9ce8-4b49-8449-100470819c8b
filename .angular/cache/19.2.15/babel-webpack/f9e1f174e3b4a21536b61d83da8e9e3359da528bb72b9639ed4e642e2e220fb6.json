{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var EmptyError = createErrorClass(function (_super) {\n  return function EmptyErrorImpl() {\n    _super(this);\n    this.name = 'EmptyError';\n    this.message = 'no elements in sequence';\n  };\n});\n//# sourceMappingURL=EmptyError.js.map", "map": {"version": 3, "names": ["createErrorClass", "EmptyError", "_super", "EmptyErrorImpl", "name", "message"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/EmptyError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport var EmptyError = createErrorClass(function (_super) {\n    return function EmptyErrorImpl() {\n        _super(this);\n        this.name = 'EmptyError';\n        this.message = 'no elements in sequence';\n    };\n});\n//# sourceMappingURL=EmptyError.js.map"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,IAAIC,UAAU,GAAGD,gBAAgB,CAAC,UAAUE,MAAM,EAAE;EACvD,OAAO,SAASC,cAAcA,CAAA,EAAG;IAC7BD,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACE,IAAI,GAAG,YAAY;IACxB,IAAI,CAACC,OAAO,GAAG,yBAAyB;EAC5C,CAAC;AACL,CAAC,CAAC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}