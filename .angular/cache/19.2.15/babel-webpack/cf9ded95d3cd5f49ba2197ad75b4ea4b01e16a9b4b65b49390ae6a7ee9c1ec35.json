{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _CdkPrivateStyleLoader2;\nimport * as i0 from '@angular/core';\nimport { inject, Injector, EnvironmentInjector, ApplicationRef, createComponent, Injectable } from '@angular/core';\n\n/** Apps in which we've loaded styles. */\nconst appsWithLoaders = new WeakMap();\n/**\n * Service that loads structural styles dynamically\n * and ensures that they're only loaded once per app.\n */\nclass _CdkPrivateStyleLoader {\n  constructor() {\n    _defineProperty(this, \"_appRef\", void 0);\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_environmentInjector\", inject(EnvironmentInjector));\n  }\n  /**\n   * Loads a set of styles.\n   * @param loader Component which will be instantiated to load the styles.\n   */\n  load(loader) {\n    // Resolve the app ref lazily to avoid circular dependency errors if this is called too early.\n    const appRef = this._appRef = this._appRef || this._injector.get(ApplicationRef);\n    let data = appsWithLoaders.get(appRef);\n    // If we haven't loaded for this app before, we have to initialize it.\n    if (!data) {\n      data = {\n        loaders: new Set(),\n        refs: []\n      };\n      appsWithLoaders.set(appRef, data);\n      // When the app is destroyed, we need to clean up all the related loaders.\n      appRef.onDestroy(() => {\n        var _appsWithLoaders$get;\n        (_appsWithLoaders$get = appsWithLoaders.get(appRef)) === null || _appsWithLoaders$get === void 0 || _appsWithLoaders$get.refs.forEach(ref => ref.destroy());\n        appsWithLoaders.delete(appRef);\n      });\n    }\n    // If the loader hasn't been loaded before, we need to instatiate it.\n    if (!data.loaders.has(loader)) {\n      data.loaders.add(loader);\n      data.refs.push(createComponent(loader, {\n        environmentInjector: this._environmentInjector\n      }));\n    }\n  }\n}\n_CdkPrivateStyleLoader2 = _CdkPrivateStyleLoader;\n_defineProperty(_CdkPrivateStyleLoader, \"\\u0275fac\", function _CdkPrivateStyleLoader2_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkPrivateStyleLoader2)();\n});\n_defineProperty(_CdkPrivateStyleLoader, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _CdkPrivateStyleLoader2,\n  factory: _CdkPrivateStyleLoader2.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkPrivateStyleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _CdkPrivateStyleLoader as _ };", "map": {"version": 3, "names": ["i0", "inject", "Injector", "EnvironmentInjector", "ApplicationRef", "createComponent", "Injectable", "appsWithLoaders", "WeakMap", "_CdkPrivateStyleLoader", "constructor", "_defineProperty", "load", "loader", "appRef", "_appRef", "_injector", "get", "data", "loaders", "Set", "refs", "set", "onDestroy", "_appsWithLoaders$get", "for<PERSON>ach", "ref", "destroy", "delete", "has", "add", "push", "environmentInjector", "_environmentInjector", "_CdkPrivateStyleLoader2", "_CdkPrivateStyleLoader2_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "_"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/style-loader-Cu9AvjH9.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injector, EnvironmentInjector, ApplicationRef, createComponent, Injectable } from '@angular/core';\n\n/** Apps in which we've loaded styles. */\nconst appsWithLoaders = new WeakMap();\n/**\n * Service that loads structural styles dynamically\n * and ensures that they're only loaded once per app.\n */\nclass _CdkPrivateStyleLoader {\n    _appRef;\n    _injector = inject(Injector);\n    _environmentInjector = inject(EnvironmentInjector);\n    /**\n     * Loads a set of styles.\n     * @param loader Component which will be instantiated to load the styles.\n     */\n    load(loader) {\n        // Resolve the app ref lazily to avoid circular dependency errors if this is called too early.\n        const appRef = (this._appRef = this._appRef || this._injector.get(ApplicationRef));\n        let data = appsWithLoaders.get(appRef);\n        // If we haven't loaded for this app before, we have to initialize it.\n        if (!data) {\n            data = { loaders: new Set(), refs: [] };\n            appsWithLoaders.set(appRef, data);\n            // When the app is destroyed, we need to clean up all the related loaders.\n            appRef.onDestroy(() => {\n                appsWithLoaders.get(appRef)?.refs.forEach(ref => ref.destroy());\n                appsWithLoaders.delete(appRef);\n            });\n        }\n        // If the loader hasn't been loaded before, we need to instatiate it.\n        if (!data.loaders.has(loader)) {\n            data.loaders.add(loader);\n            data.refs.push(createComponent(loader, { environmentInjector: this._environmentInjector }));\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkPrivateStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkPrivateStyleLoader, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkPrivateStyleLoader, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nexport { _CdkPrivateStyleLoader as _ };\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,UAAU,QAAQ,eAAe;;AAElH;AACA,MAAMC,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EAAAC,YAAA;IAAAC,eAAA;IAAAA,eAAA,oBAEbV,MAAM,CAACC,QAAQ,CAAC;IAAAS,eAAA,+BACLV,MAAM,CAACE,mBAAmB,CAAC;EAAA;EAClD;AACJ;AACA;AACA;EACIS,IAAIA,CAACC,MAAM,EAAE;IACT;IACA,MAAMC,MAAM,GAAI,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,IAAI,CAACC,SAAS,CAACC,GAAG,CAACb,cAAc,CAAE;IAClF,IAAIc,IAAI,GAAGX,eAAe,CAACU,GAAG,CAACH,MAAM,CAAC;IACtC;IACA,IAAI,CAACI,IAAI,EAAE;MACPA,IAAI,GAAG;QAAEC,OAAO,EAAE,IAAIC,GAAG,CAAC,CAAC;QAAEC,IAAI,EAAE;MAAG,CAAC;MACvCd,eAAe,CAACe,GAAG,CAACR,MAAM,EAAEI,IAAI,CAAC;MACjC;MACAJ,MAAM,CAACS,SAAS,CAAC,MAAM;QAAA,IAAAC,oBAAA;QACnB,CAAAA,oBAAA,GAAAjB,eAAe,CAACU,GAAG,CAACH,MAAM,CAAC,cAAAU,oBAAA,eAA3BA,oBAAA,CAA6BH,IAAI,CAACI,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;QAC/DpB,eAAe,CAACqB,MAAM,CAACd,MAAM,CAAC;MAClC,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAACI,IAAI,CAACC,OAAO,CAACU,GAAG,CAAChB,MAAM,CAAC,EAAE;MAC3BK,IAAI,CAACC,OAAO,CAACW,GAAG,CAACjB,MAAM,CAAC;MACxBK,IAAI,CAACG,IAAI,CAACU,IAAI,CAAC1B,eAAe,CAACQ,MAAM,EAAE;QAAEmB,mBAAmB,EAAE,IAAI,CAACC;MAAqB,CAAC,CAAC,CAAC;IAC/F;EACJ;AAGJ;AAACC,uBAAA,GA9BKzB,sBAAsB;AAAAE,eAAA,CAAtBF,sBAAsB,wBAAA0B,gCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA4B2E3B,uBAAsB;AAAA;AAAAE,eAAA,CA5BvHF,sBAAsB,+BA+BqDT,EAAE,CAAAqC,kBAAA;EAAAC,KAAA,EAFwB7B,uBAAsB;EAAA8B,OAAA,EAAtB9B,uBAAsB,CAAA+B,IAAA;EAAAC,UAAA,EAAc;AAAM;AAErJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF1C,EAAE,CAAA2C,iBAAA,CAAQlC,sBAAsB,EAAc,CAAC;IACpHmC,IAAI,EAAEtC,UAAU;IAChBuC,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,SAAShC,sBAAsB,IAAIqC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}