{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageSelectorModule } from '../language-selector/language-selector.module';\nimport { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';\nimport { UserMenuComponent } from './user-menu.component';\nlet UserMenuModule = class UserMenuModule {};\nUserMenuModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, MatRippleModule, MatIconModule, MatMenuModule, MatDividerModule, LanguageSelectorModule, SwuiSettingsDialogModule, MatTooltipModule],\n  declarations: [UserMenuComponent],\n  exports: [UserMenuComponent]\n})], UserMenuModule);\nexport { UserMenuModule };", "map": {"version": 3, "names": ["__decorate", "CommonModule", "NgModule", "MatRippleModule", "MatDividerModule", "MatIconModule", "MatMenuModule", "MatTooltipModule", "TranslateModule", "LanguageSelectorModule", "SwuiSettingsDialogModule", "UserMenuComponent", "UserMenuModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/user-menu/user-menu.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageSelectorModule } from '../language-selector/language-selector.module';\nimport { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';\nimport { UserMenuComponent } from './user-menu.component';\nlet UserMenuModule = class UserMenuModule {\n};\nUserMenuModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule,\n            MatRippleModule,\n            MatIconModule,\n            MatMenuModule,\n            MatDividerModule,\n            LanguageSelectorModule,\n            SwuiSettingsDialogModule,\n            MatTooltipModule\n        ],\n        declarations: [UserMenuComponent],\n        exports: [UserMenuComponent],\n    })\n], UserMenuModule);\nexport { UserMenuModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,sBAAsB,QAAQ,+CAA+C;AACtF,SAASC,wBAAwB,QAAQ,gDAAgD;AACzF,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,IAAIC,cAAc,GAAG,MAAMA,cAAc,CAAC,EACzC;AACDA,cAAc,GAAGZ,UAAU,CAAC,CACxBE,QAAQ,CAAC;EACLW,OAAO,EAAE,CACLZ,YAAY,EACZO,eAAe,EACfL,eAAe,EACfE,aAAa,EACbC,aAAa,EACbF,gBAAgB,EAChBK,sBAAsB,EACtBC,wBAAwB,EACxBH,gBAAgB,CACnB;EACDO,YAAY,EAAE,CAACH,iBAAiB,CAAC;EACjCI,OAAO,EAAE,CAACJ,iBAAiB;AAC/B,CAAC,CAAC,CACL,EAAEC,cAAc,CAAC;AAClB,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}