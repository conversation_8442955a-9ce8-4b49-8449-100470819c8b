{"ast": null, "code": "var _SwuiSidebarComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-sidebar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-sidebar.component.scss?ngResource\";\nimport { Component, HostBinding, HostListener, Input, ViewChild } from '@angular/core';\nimport { SwuiSidebarService } from './swui-sidebar.service';\nimport { DomSanitizer } from '@angular/platform-browser';\nlet SwuiSidebarComponent = (_SwuiSidebarComponent = class SwuiSidebarComponent {\n  constructor(sidebarStateService, sanitizer) {\n    this.sidebarStateService = sidebarStateService;\n    this.sanitizer = sanitizer;\n    this.topPadding = '0px';\n    this.isCollapsed = false;\n    this.isHovered = false;\n    this.collapseOnDimension();\n    this._sidebarStateSubscription = this.sidebarStateService.isCollapsed.subscribe(val => {\n      this.isCollapsed = val;\n    });\n  }\n  get valueAsStyle() {\n    return this.sanitizer.bypassSecurityTrustStyle(`--top-padding: ${this.topPadding}`);\n  }\n  onResize() {\n    this.collapseOnDimension();\n  }\n  onClick(targetElement) {\n    if (this.sidebarRef) {\n      const clickedInside = this.sidebarRef.nativeElement.contains(targetElement);\n      if (!clickedInside && window.innerWidth <= 1024) {\n        this.sidebarStateService.isCollapsed.next(true);\n      }\n    }\n  }\n  ngAfterViewInit() {\n    this.bindMouseenterEvent();\n    this.bindMouseLeaveEvent();\n  }\n  ngOnDestroy() {\n    if (this._sidebarStateSubscription) {\n      this._sidebarStateSubscription.unsubscribe();\n    }\n  }\n  toggleSidebar(event) {\n    event.preventDefault();\n    this.sidebarStateService.isCollapsed.next(!this.isCollapsed);\n  }\n  bindMouseenterEvent() {\n    if (this.sidebarRef) {\n      this.sidebarRef.nativeElement.addEventListener('mouseenter', () => {\n        if (this.isCollapsed) {\n          this.isHovered = true;\n        }\n      });\n    }\n  }\n  bindMouseLeaveEvent() {\n    if (this.sidebarRef) {\n      this.sidebarRef.nativeElement.addEventListener('mouseleave', () => {\n        this.isHovered = false;\n      });\n    }\n  }\n  collapseOnDimension() {\n    if (window.innerWidth <= 1024) {\n      this.sidebarStateService.isCollapsed.next(true);\n    }\n  }\n}, _SwuiSidebarComponent.ctorParameters = () => [{\n  type: SwuiSidebarService\n}, {\n  type: DomSanitizer\n}], _SwuiSidebarComponent.propDecorators = {\n  sidebarRef: [{\n    type: ViewChild,\n    args: ['sidebar']\n  }],\n  menuItems: [{\n    type: Input\n  }],\n  activeColor: [{\n    type: Input\n  }],\n  topPadding: [{\n    type: Input\n  }],\n  valueAsStyle: [{\n    type: HostBinding,\n    args: ['attr.style']\n  }],\n  onResize: [{\n    type: HostListener,\n    args: ['window:resize']\n  }],\n  onClick: [{\n    type: HostListener,\n    args: ['document:click', ['$event.target']]\n  }]\n}, _SwuiSidebarComponent);\nSwuiSidebarComponent = __decorate([Component({\n  selector: 'lib-swui-sidebar',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSidebarComponent);\nexport { SwuiSidebarComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "HostBinding", "HostListener", "Input", "ViewChild", "SwuiSidebarService", "Dom<PERSON><PERSON><PERSON>zer", "SwuiSidebarComponent", "_SwuiSidebarComponent", "constructor", "sidebarStateService", "sanitizer", "topPadding", "isCollapsed", "isHovered", "collapseOnDimension", "_sidebarStateSubscription", "subscribe", "val", "valueAsStyle", "bypassSecurityTrustStyle", "onResize", "onClick", "targetElement", "sidebarRef", "clickedInside", "nativeElement", "contains", "window", "innerWidth", "next", "ngAfterViewInit", "bindMouseenterEvent", "bindMouseLeaveEvent", "ngOnDestroy", "unsubscribe", "toggleSidebar", "event", "preventDefault", "addEventListener", "ctorParameters", "type", "propDecorators", "args", "menuItems", "activeColor", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-sidebar/swui-sidebar.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-sidebar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-sidebar.component.scss?ngResource\";\nimport { Component, HostBinding, HostListener, Input, ViewChild } from '@angular/core';\nimport { SwuiSidebarService } from './swui-sidebar.service';\nimport { DomSanitizer } from '@angular/platform-browser';\nlet SwuiSidebarComponent = class SwuiSidebarComponent {\n    constructor(sidebarStateService, sanitizer) {\n        this.sidebarStateService = sidebarStateService;\n        this.sanitizer = sanitizer;\n        this.topPadding = '0px';\n        this.isCollapsed = false;\n        this.isHovered = false;\n        this.collapseOnDimension();\n        this._sidebarStateSubscription = this.sidebarStateService.isCollapsed.subscribe((val) => {\n            this.isCollapsed = val;\n        });\n    }\n    get valueAsStyle() {\n        return this.sanitizer.bypassSecurityTrustStyle(`--top-padding: ${this.topPadding}`);\n    }\n    onResize() {\n        this.collapseOnDimension();\n    }\n    onClick(targetElement) {\n        if (this.sidebarRef) {\n            const clickedInside = this.sidebarRef.nativeElement.contains(targetElement);\n            if (!clickedInside && window.innerWidth <= 1024) {\n                this.sidebarStateService.isCollapsed.next(true);\n            }\n        }\n    }\n    ngAfterViewInit() {\n        this.bindMouseenterEvent();\n        this.bindMouseLeaveEvent();\n    }\n    ngOnDestroy() {\n        if (this._sidebarStateSubscription) {\n            this._sidebarStateSubscription.unsubscribe();\n        }\n    }\n    toggleSidebar(event) {\n        event.preventDefault();\n        this.sidebarStateService.isCollapsed.next(!this.isCollapsed);\n    }\n    bindMouseenterEvent() {\n        if (this.sidebarRef) {\n            this.sidebarRef.nativeElement.addEventListener('mouseenter', () => {\n                if (this.isCollapsed) {\n                    this.isHovered = true;\n                }\n            });\n        }\n    }\n    bindMouseLeaveEvent() {\n        if (this.sidebarRef) {\n            this.sidebarRef.nativeElement.addEventListener('mouseleave', () => {\n                this.isHovered = false;\n            });\n        }\n    }\n    collapseOnDimension() {\n        if (window.innerWidth <= 1024) {\n            this.sidebarStateService.isCollapsed.next(true);\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: SwuiSidebarService },\n        { type: DomSanitizer }\n    ]; }\n    static { this.propDecorators = {\n        sidebarRef: [{ type: ViewChild, args: ['sidebar',] }],\n        menuItems: [{ type: Input }],\n        activeColor: [{ type: Input }],\n        topPadding: [{ type: Input }],\n        valueAsStyle: [{ type: HostBinding, args: ['attr.style',] }],\n        onResize: [{ type: HostListener, args: ['window:resize',] }],\n        onClick: [{ type: HostListener, args: ['document:click', ['$event.target'],] }]\n    }; }\n};\nSwuiSidebarComponent = __decorate([\n    Component({\n        selector: 'lib-swui-sidebar',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiSidebarComponent);\nexport { SwuiSidebarComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,SAASC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AACtF,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,IAAIC,oBAAoB,IAAAC,qBAAA,GAAG,MAAMD,oBAAoB,CAAC;EAClDE,WAAWA,CAACC,mBAAmB,EAAEC,SAAS,EAAE;IACxC,IAAI,CAACD,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACN,mBAAmB,CAACG,WAAW,CAACI,SAAS,CAAEC,GAAG,IAAK;MACrF,IAAI,CAACL,WAAW,GAAGK,GAAG;IAC1B,CAAC,CAAC;EACN;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACR,SAAS,CAACS,wBAAwB,CAAC,kBAAkB,IAAI,CAACR,UAAU,EAAE,CAAC;EACvF;EACAS,QAAQA,CAAA,EAAG;IACP,IAAI,CAACN,mBAAmB,CAAC,CAAC;EAC9B;EACAO,OAAOA,CAACC,aAAa,EAAE;IACnB,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,MAAMC,aAAa,GAAG,IAAI,CAACD,UAAU,CAACE,aAAa,CAACC,QAAQ,CAACJ,aAAa,CAAC;MAC3E,IAAI,CAACE,aAAa,IAAIG,MAAM,CAACC,UAAU,IAAI,IAAI,EAAE;QAC7C,IAAI,CAACnB,mBAAmB,CAACG,WAAW,CAACiB,IAAI,CAAC,IAAI,CAAC;MACnD;IACJ;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAClB,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACmB,WAAW,CAAC,CAAC;IAChD;EACJ;EACAC,aAAaA,CAACC,KAAK,EAAE;IACjBA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAAC5B,mBAAmB,CAACG,WAAW,CAACiB,IAAI,CAAC,CAAC,IAAI,CAACjB,WAAW,CAAC;EAChE;EACAmB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACR,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACE,aAAa,CAACa,gBAAgB,CAAC,YAAY,EAAE,MAAM;QAC/D,IAAI,IAAI,CAAC1B,WAAW,EAAE;UAClB,IAAI,CAACC,SAAS,GAAG,IAAI;QACzB;MACJ,CAAC,CAAC;IACN;EACJ;EACAmB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACT,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACE,aAAa,CAACa,gBAAgB,CAAC,YAAY,EAAE,MAAM;QAC/D,IAAI,CAACzB,SAAS,GAAG,KAAK;MAC1B,CAAC,CAAC;IACN;EACJ;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAIa,MAAM,CAACC,UAAU,IAAI,IAAI,EAAE;MAC3B,IAAI,CAACnB,mBAAmB,CAACG,WAAW,CAACiB,IAAI,CAAC,IAAI,CAAC;IACnD;EACJ;AAcJ,CAAC,EAbYtB,qBAAA,CAAKgC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEpC;AAAmB,CAAC,EAC5B;EAAEoC,IAAI,EAAEnC;AAAa,CAAC,CACzB,EACQE,qBAAA,CAAKkC,cAAc,GAAG;EAC3BlB,UAAU,EAAE,CAAC;IAAEiB,IAAI,EAAErC,SAAS;IAAEuC,IAAI,EAAE,CAAC,SAAS;EAAG,CAAC,CAAC;EACrDC,SAAS,EAAE,CAAC;IAAEH,IAAI,EAAEtC;EAAM,CAAC,CAAC;EAC5B0C,WAAW,EAAE,CAAC;IAAEJ,IAAI,EAAEtC;EAAM,CAAC,CAAC;EAC9BS,UAAU,EAAE,CAAC;IAAE6B,IAAI,EAAEtC;EAAM,CAAC,CAAC;EAC7BgB,YAAY,EAAE,CAAC;IAAEsB,IAAI,EAAExC,WAAW;IAAE0C,IAAI,EAAE,CAAC,YAAY;EAAG,CAAC,CAAC;EAC5DtB,QAAQ,EAAE,CAAC;IAAEoB,IAAI,EAAEvC,YAAY;IAAEyC,IAAI,EAAE,CAAC,eAAe;EAAG,CAAC,CAAC;EAC5DrB,OAAO,EAAE,CAAC;IAAEmB,IAAI,EAAEvC,YAAY;IAAEyC,IAAI,EAAE,CAAC,gBAAgB,EAAE,CAAC,eAAe,CAAC;EAAG,CAAC;AAClF,CAAC,EAAAnC,qBAAA,CACJ;AACDD,oBAAoB,GAAGV,UAAU,CAAC,CAC9BG,SAAS,CAAC;EACN8C,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAEjD,oBAAoB;EAC9BkD,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAClD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEQ,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}