{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatCard, _MatCardTitle, _MatCardTitleGroup, _MatCardContent, _MatCardSubtitle, _MatCardActions, _Mat<PERSON>ardHeader, _Mat<PERSON>ardFooter, _MatCardImage, _MatCardSmImage, _MatCardMdImage, _MatCardLgImage, _MatCardXlImage, _MatCardAvatar, _MatCardModule;\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"], [\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"], [\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"], [\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"], [\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]], \"*\"];\nconst _c2 = [\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\", \"*\"];\nconst _c3 = [[[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]], [[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], \"*\"];\nconst _c4 = [\"[mat-card-avatar], [matCardAvatar]\", \"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"*\"];\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Directive, NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token that can be used to provide the default options the card module. */\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n  constructor() {\n    _defineProperty(this, \"appearance\", void 0);\n    const config = inject(MAT_CARD_CONFIG, {\n      optional: true\n    });\n    this.appearance = (config === null || config === void 0 ? void 0 : config.appearance) || 'raised';\n  }\n}\n_MatCard = MatCard;\n_defineProperty(MatCard, \"\\u0275fac\", function _MatCard_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCard)();\n});\n_defineProperty(MatCard, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatCard,\n  selectors: [[\"mat-card\"]],\n  hostAttrs: [1, \"mat-mdc-card\", \"mdc-card\"],\n  hostVars: 4,\n  hostBindings: function _MatCard_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-card-outlined\", ctx.appearance === \"outlined\")(\"mdc-card--outlined\", ctx.appearance === \"outlined\");\n    }\n  },\n  inputs: {\n    appearance: \"appearance\"\n  },\n  exportAs: [\"matCard\"],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function _MatCard_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  styles: [\".mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mdc-elevated-card-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:\\\"\\\";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mdc-outlined-card-container-color, var(--mat-sys-surface));border-radius:var(--mdc-outlined-card-container-shape, var(--mat-sys-corner-medium));border-width:var(--mdc-outlined-card-outline-width, 1px);border-color:var(--mdc-outlined-card-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mdc-outlined-card-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCard, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card',\n      host: {\n        'class': 'mat-mdc-card mdc-card',\n        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n        '[class.mdc-card--outlined]': 'appearance === \"outlined\"'\n      },\n      exportAs: 'matCard',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-content></ng-content>\\n\",\n      styles: [\".mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mdc-elevated-card-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:\\\"\\\";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mdc-outlined-card-container-color, var(--mat-sys-surface));border-radius:var(--mdc-outlined-card-container-shape, var(--mat-sys-corner-medium));border-width:var(--mdc-outlined-card-outline-width, 1px);border-color:var(--mdc-outlined-card-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mdc-outlined-card-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\\n\"]\n    }]\n  }], () => [], {\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {}\n_MatCardTitle = MatCardTitle;\n_defineProperty(MatCardTitle, \"\\u0275fac\", function _MatCardTitle_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardTitle)();\n});\n_defineProperty(MatCardTitle, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardTitle,\n  selectors: [[\"mat-card-title\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"matCardTitle\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-title\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n      host: {\n        'class': 'mat-mdc-card-title'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {}\n_MatCardTitleGroup = MatCardTitleGroup;\n_defineProperty(MatCardTitleGroup, \"\\u0275fac\", function _MatCardTitleGroup_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardTitleGroup)();\n});\n_defineProperty(MatCardTitleGroup, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatCardTitleGroup,\n  selectors: [[\"mat-card-title-group\"]],\n  hostAttrs: [1, \"mat-mdc-card-title-group\"],\n  ngContentSelectors: _c2,\n  decls: 4,\n  vars: 0,\n  template: function _MatCardTitleGroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c1);\n      i0.ɵɵelementStart(0, \"div\");\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵprojection(3, 2);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitleGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-title-group',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-title-group'\n      },\n      template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {}\n_MatCardContent = MatCardContent;\n_defineProperty(MatCardContent, \"\\u0275fac\", function _MatCardContent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardContent)();\n});\n_defineProperty(MatCardContent, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardContent,\n  selectors: [[\"mat-card-content\"]],\n  hostAttrs: [1, \"mat-mdc-card-content\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardContent, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-content',\n      host: {\n        'class': 'mat-mdc-card-content'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {}\n_MatCardSubtitle = MatCardSubtitle;\n_defineProperty(MatCardSubtitle, \"\\u0275fac\", function _MatCardSubtitle_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardSubtitle)();\n});\n_defineProperty(MatCardSubtitle, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardSubtitle,\n  selectors: [[\"mat-card-subtitle\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-subtitle\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSubtitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n      host: {\n        'class': 'mat-mdc-card-subtitle'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n  constructor() {\n    // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n    // as to not conflict with the native `align` attribute.\n    /** Position of the actions inside the card. */\n    _defineProperty(this, \"align\", 'start');\n  }\n}\n_MatCardActions = MatCardActions;\n_defineProperty(MatCardActions, \"\\u0275fac\", function _MatCardActions_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardActions)();\n});\n_defineProperty(MatCardActions, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardActions,\n  selectors: [[\"mat-card-actions\"]],\n  hostAttrs: [1, \"mat-mdc-card-actions\", \"mdc-card__actions\"],\n  hostVars: 2,\n  hostBindings: function _MatCardActions_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-card-actions-align-end\", ctx.align === \"end\");\n    }\n  },\n  inputs: {\n    align: \"align\"\n  },\n  exportAs: [\"matCardActions\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardActions, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-actions',\n      exportAs: 'matCardActions',\n      host: {\n        'class': 'mat-mdc-card-actions mdc-card__actions',\n        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {}\n_MatCardHeader = MatCardHeader;\n_defineProperty(MatCardHeader, \"\\u0275fac\", function _MatCardHeader_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardHeader)();\n});\n_defineProperty(MatCardHeader, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatCardHeader,\n  selectors: [[\"mat-card-header\"]],\n  hostAttrs: [1, \"mat-mdc-card-header\"],\n  ngContentSelectors: _c4,\n  decls: 4,\n  vars: 0,\n  consts: [[1, \"mat-mdc-card-header-text\"]],\n  template: function _MatCardHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c3);\n      i0.ɵɵprojection(0);\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(3, 2);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-header'\n      },\n      template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {}\n_MatCardFooter = MatCardFooter;\n_defineProperty(MatCardFooter, \"\\u0275fac\", function _MatCardFooter_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardFooter)();\n});\n_defineProperty(MatCardFooter, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardFooter,\n  selectors: [[\"mat-card-footer\"]],\n  hostAttrs: [1, \"mat-mdc-card-footer\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardFooter, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-footer',\n      host: {\n        'class': 'mat-mdc-card-footer'\n      }\n    }]\n  }], null, null);\n})();\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {}\n_MatCardImage = MatCardImage;\n_defineProperty(MatCardImage, \"\\u0275fac\", function _MatCardImage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardImage)();\n});\n_defineProperty(MatCardImage, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardImage,\n  selectors: [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-image\", \"mdc-card__media\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-image], [matCardImage]',\n      host: {\n        'class': 'mat-mdc-card-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {}\n_MatCardSmImage = MatCardSmImage;\n_defineProperty(MatCardSmImage, \"\\u0275fac\", function _MatCardSmImage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardSmImage)();\n});\n_defineProperty(MatCardSmImage, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardSmImage,\n  selectors: [[\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-sm-image\", \"mdc-card__media\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSmImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-sm-image], [matCardImageSmall]',\n      host: {\n        'class': 'mat-mdc-card-sm-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {}\n_MatCardMdImage = MatCardMdImage;\n_defineProperty(MatCardMdImage, \"\\u0275fac\", function _MatCardMdImage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardMdImage)();\n});\n_defineProperty(MatCardMdImage, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardMdImage,\n  selectors: [[\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-md-image\", \"mdc-card__media\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardMdImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-md-image], [matCardImageMedium]',\n      host: {\n        'class': 'mat-mdc-card-md-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {}\n_MatCardLgImage = MatCardLgImage;\n_defineProperty(MatCardLgImage, \"\\u0275fac\", function _MatCardLgImage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardLgImage)();\n});\n_defineProperty(MatCardLgImage, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardLgImage,\n  selectors: [[\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-lg-image\", \"mdc-card__media\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardLgImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-lg-image], [matCardImageLarge]',\n      host: {\n        'class': 'mat-mdc-card-lg-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {}\n_MatCardXlImage = MatCardXlImage;\n_defineProperty(MatCardXlImage, \"\\u0275fac\", function _MatCardXlImage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardXlImage)();\n});\n_defineProperty(MatCardXlImage, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardXlImage,\n  selectors: [[\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-xl-image\", \"mdc-card__media\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardXlImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-xl-image], [matCardImageXLarge]',\n      host: {\n        'class': 'mat-mdc-card-xl-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {}\n_MatCardAvatar = MatCardAvatar;\n_defineProperty(MatCardAvatar, \"\\u0275fac\", function _MatCardAvatar_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardAvatar)();\n});\n_defineProperty(MatCardAvatar, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCardAvatar,\n  selectors: [[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-card-avatar\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardAvatar, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-avatar], [matCardAvatar]',\n      host: {\n        'class': 'mat-mdc-card-avatar'\n      }\n    }]\n  }], null, null);\n})();\nconst CARD_DIRECTIVES = [MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage];\nclass MatCardModule {}\n_MatCardModule = MatCardModule;\n_defineProperty(MatCardModule, \"\\u0275fac\", function _MatCardModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCardModule)();\n});\n_defineProperty(MatCardModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatCardModule,\n  imports: [MatCommonModule, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage],\n  exports: [MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage, MatCommonModule]\n}));\n_defineProperty(MatCardModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, ...CARD_DIRECTIVES],\n      exports: [CARD_DIRECTIVES, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Directive", "NgModule", "M", "MatCommonModule", "MAT_CARD_CONFIG", "MatCard", "constructor", "_defineProperty", "config", "optional", "appearance", "_MatCard", "_MatCard_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "_MatCard_HostBindings", "rf", "ctx", "ɵɵclassProp", "inputs", "exportAs", "ngContentSelectors", "_c0", "decls", "vars", "template", "_Mat<PERSON><PERSON>_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "None", "OnPush", "MatCardTitle", "_MatCardTitle", "_MatCardTitle_Factory", "ɵɵdefineDirective", "MatCardTitleGroup", "_MatCardTitleGroup", "_MatCardTitleGroup_Factory", "_c2", "_MatCardTitleGroup_Template", "_c1", "ɵɵelementStart", "ɵɵelementEnd", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_MatCardContent_Factory", "MatCardSubtitle", "_MatCardSubtitle", "_MatCardSubtitle_Factory", "MatCardActions", "_MatCardActions", "_MatCardActions_Factory", "_MatCardActions_HostBindings", "align", "MatCardHeader", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_MatCardHeader_Factory", "_c4", "consts", "_Mat<PERSON>ardHeader_Template", "_c3", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON><PERSON>er", "_MatCardFooter_Factory", "MatCardImage", "_MatCardImage", "_MatCardImage_Factory", "MatCardSmImage", "_MatCardSmImage", "_MatCardSmImage_Factory", "MatCardMdImage", "_MatCardMdImage", "_MatCardMdImage_Factory", "MatCardLgImage", "_MatCardLgImage", "_MatCardLgImage_Factory", "MatCardXlImage", "_MatCardXlImage", "_MatCardXlImage_Factory", "MatCardAvatar", "_MatCardAvatar", "_MatCardAvatar_Factory", "CARD_DIRECTIVES", "MatCardModule", "_MatCardModule", "_MatCardModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/card.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Directive, NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token that can be used to provide the default options the card module. */\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n    appearance;\n    constructor() {\n        const config = inject(MAT_CARD_CONFIG, { optional: true });\n        this.appearance = config?.appearance || 'raised';\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCard, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCard, isStandalone: true, selector: \"mat-card\", inputs: { appearance: \"appearance\" }, host: { properties: { \"class.mat-mdc-card-outlined\": \"appearance === \\\"outlined\\\"\", \"class.mdc-card--outlined\": \"appearance === \\\"outlined\\\"\" }, classAttribute: \"mat-mdc-card mdc-card\" }, exportAs: [\"matCard\"], ngImport: i0, template: \"<ng-content></ng-content>\\n\", styles: [\".mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mdc-elevated-card-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:\\\"\\\";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mdc-outlined-card-container-color, var(--mat-sys-surface));border-radius:var(--mdc-outlined-card-container-shape, var(--mat-sys-corner-medium));border-width:var(--mdc-outlined-card-outline-width, 1px);border-color:var(--mdc-outlined-card-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mdc-outlined-card-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCard, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card', host: {\n                        'class': 'mat-mdc-card mdc-card',\n                        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n                        '[class.mdc-card--outlined]': 'appearance === \"outlined\"',\n                    }, exportAs: 'matCard', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-content></ng-content>\\n\", styles: [\".mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mdc-elevated-card-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:\\\"\\\";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mdc-outlined-card-container-color, var(--mat-sys-surface));border-radius:var(--mdc-outlined-card-container-shape, var(--mat-sys-corner-medium));border-width:var(--mdc-outlined-card-outline-width, 1px);border-color:var(--mdc-outlined-card-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mdc-outlined-card-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { appearance: [{\n                type: Input\n            }] } });\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardTitle, isStandalone: true, selector: \"mat-card-title, [mat-card-title], [matCardTitle]\", host: { classAttribute: \"mat-mdc-card-title\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n                    host: { 'class': 'mat-mdc-card-title' },\n                }]\n        }] });\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardTitleGroup, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardTitleGroup, isStandalone: true, selector: \"mat-card-title-group\", host: { classAttribute: \"mat-mdc-card-title-group\" }, ngImport: i0, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardTitleGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-title-group', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-mdc-card-title-group' }, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardContent, isStandalone: true, selector: \"mat-card-content\", host: { classAttribute: \"mat-mdc-card-content\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-content',\n                    host: { 'class': 'mat-mdc-card-content' },\n                }]\n        }] });\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardSubtitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardSubtitle, isStandalone: true, selector: \"mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]\", host: { classAttribute: \"mat-mdc-card-subtitle\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardSubtitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n                    host: { 'class': 'mat-mdc-card-subtitle' },\n                }]\n        }] });\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n    // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n    // as to not conflict with the native `align` attribute.\n    /** Position of the actions inside the card. */\n    align = 'start';\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardActions, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardActions, isStandalone: true, selector: \"mat-card-actions\", inputs: { align: \"align\" }, host: { properties: { \"class.mat-mdc-card-actions-align-end\": \"align === \\\"end\\\"\" }, classAttribute: \"mat-mdc-card-actions mdc-card__actions\" }, exportAs: [\"matCardActions\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-actions',\n                    exportAs: 'matCardActions',\n                    host: {\n                        'class': 'mat-mdc-card-actions mdc-card__actions',\n                        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }] } });\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardHeader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardHeader, isStandalone: true, selector: \"mat-card-header\", host: { classAttribute: \"mat-mdc-card-header\" }, ngImport: i0, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-mdc-card-header' }, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardFooter, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardFooter, isStandalone: true, selector: \"mat-card-footer\", host: { classAttribute: \"mat-mdc-card-footer\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardFooter, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-footer',\n                    host: { 'class': 'mat-mdc-card-footer' },\n                }]\n        }] });\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardImage, isStandalone: true, selector: \"[mat-card-image], [matCardImage]\", host: { classAttribute: \"mat-mdc-card-image mdc-card__media\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-image], [matCardImage]',\n                    host: { 'class': 'mat-mdc-card-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardSmImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardSmImage, isStandalone: true, selector: \"[mat-card-sm-image], [matCardImageSmall]\", host: { classAttribute: \"mat-mdc-card-sm-image mdc-card__media\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardSmImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-sm-image], [matCardImageSmall]',\n                    host: { 'class': 'mat-mdc-card-sm-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardMdImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardMdImage, isStandalone: true, selector: \"[mat-card-md-image], [matCardImageMedium]\", host: { classAttribute: \"mat-mdc-card-md-image mdc-card__media\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardMdImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-md-image], [matCardImageMedium]',\n                    host: { 'class': 'mat-mdc-card-md-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardLgImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardLgImage, isStandalone: true, selector: \"[mat-card-lg-image], [matCardImageLarge]\", host: { classAttribute: \"mat-mdc-card-lg-image mdc-card__media\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardLgImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-lg-image], [matCardImageLarge]',\n                    host: { 'class': 'mat-mdc-card-lg-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardXlImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardXlImage, isStandalone: true, selector: \"[mat-card-xl-image], [matCardImageXLarge]\", host: { classAttribute: \"mat-mdc-card-xl-image mdc-card__media\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardXlImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-xl-image], [matCardImageXLarge]',\n                    host: { 'class': 'mat-mdc-card-xl-image mdc-card__media' },\n                }]\n        }] });\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardAvatar, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCardAvatar, isStandalone: true, selector: \"[mat-card-avatar], [matCardAvatar]\", host: { classAttribute: \"mat-mdc-card-avatar\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-avatar], [matCardAvatar]',\n                    host: { 'class': 'mat-mdc-card-avatar' },\n                }]\n        }] });\n\nconst CARD_DIRECTIVES = [\n    MatCard,\n    MatCardActions,\n    MatCardAvatar,\n    MatCardContent,\n    MatCardFooter,\n    MatCardHeader,\n    MatCardImage,\n    MatCardLgImage,\n    MatCardMdImage,\n    MatCardSmImage,\n    MatCardSubtitle,\n    MatCardTitle,\n    MatCardTitleGroup,\n    MatCardXlImage,\n];\nclass MatCardModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardModule, imports: [MatCommonModule, MatCard,\n            MatCardActions,\n            MatCardAvatar,\n            MatCardContent,\n            MatCardFooter,\n            MatCardHeader,\n            MatCardImage,\n            MatCardLgImage,\n            MatCardMdImage,\n            MatCardSmImage,\n            MatCardSubtitle,\n            MatCardTitle,\n            MatCardTitleGroup,\n            MatCardXlImage], exports: [MatCard,\n            MatCardActions,\n            MatCardAvatar,\n            MatCardContent,\n            MatCardFooter,\n            MatCardHeader,\n            MatCardImage,\n            MatCardLgImage,\n            MatCardMdImage,\n            MatCardSmImage,\n            MatCardSubtitle,\n            MatCardTitle,\n            MatCardTitleGroup,\n            MatCardXlImage, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCardModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, ...CARD_DIRECTIVES],\n                    exports: [CARD_DIRECTIVES, MatCommonModule],\n                }]\n        }] });\n\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };\n"], "mappings": ";;;;;;;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACzI,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;;AAE1B;AACA,MAAMC,eAAe,GAAG,IAAIV,cAAc,CAAC,iBAAiB,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,OAAO,CAAC;EAEVC,WAAWA,CAAA,EAAG;IAAAC,eAAA;IACV,MAAMC,MAAM,GAAGb,MAAM,CAACS,eAAe,EAAE;MAAEK,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC1D,IAAI,CAACC,UAAU,GAAG,CAAAF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,UAAU,KAAI,QAAQ;EACpD;AAGJ;AAACC,QAAA,GARKN,OAAO;AAAAE,eAAA,CAAPF,OAAO,wBAAAO,iBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAM0FR,QAAO;AAAA;AAAAE,eAAA,CANxGF,OAAO,8BASoEZ,EAAE,CAAAqB,iBAAA;EAAAC,IAAA,EAFQV,QAAO;EAAAW,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAEjB5B,EAAE,CAAA8B,WAAA,0BAAAD,GAAA,CAAAZ,UAAA,KAFuB,UAAT,CAAC,uBAAAY,GAAA,CAAAZ,UAAA,KAAQ,UAAT,CAAC;IAAA;EAAA;EAAAc,MAAA;IAAAd,UAAA;EAAA;EAAAe,QAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,kBAAAV,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAEjB5B,EAAE,CAAAuC,eAAA;MAAFvC,EAAE,CAAAwC,YAAA,EAFqW,CAAC;IAAA;EAAA;EAAAC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAEzb;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF5C,EAAE,CAAA6C,iBAAA,CAAQjC,OAAO,EAAc,CAAC;IACrGU,IAAI,EAAEnB,SAAS;IACf2C,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEC,IAAI,EAAE;QACzB,OAAO,EAAE,uBAAuB;QAChC,+BAA+B,EAAE,2BAA2B;QAC5D,4BAA4B,EAAE;MAClC,CAAC;MAAEhB,QAAQ,EAAE,SAAS;MAAEU,aAAa,EAAEtC,iBAAiB,CAAC6C,IAAI;MAAEN,eAAe,EAAEtC,uBAAuB,CAAC6C,MAAM;MAAEb,QAAQ,EAAE,6BAA6B;MAAEI,MAAM,EAAE,CAAC,g/HAAg/H;IAAE,CAAC;EACjqI,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAExB,UAAU,EAAE,CAAC;MACrDK,IAAI,EAAEhB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6C,YAAY,CAAC;AAGlBC,aAAA,GAHKD,YAAY;AAAArC,eAAA,CAAZqC,YAAY,wBAAAE,sBAAAjC,iBAAA;EAAA,YAAAA,iBAAA,IACqF+B,aAAY;AAAA;AAAArC,eAAA,CAD7GqC,YAAY,8BAlB+DnD,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EAoBQ6B,aAAY;EAAA5B,SAAA;EAAAC,SAAA;AAAA;AAEvG;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KAtBiF5C,EAAE,CAAA6C,iBAAA,CAsBQM,YAAY,EAAc,CAAC;IAC1G7B,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kDAAkD;MAC5DC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAqB;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMO,iBAAiB,CAAC;AAGvBC,kBAAA,GAHKD,iBAAiB;AAAAzC,eAAA,CAAjByC,iBAAiB,wBAAAE,2BAAArC,iBAAA;EAAA,YAAAA,iBAAA,IACgFmC,kBAAiB;AAAA;AAAAzC,eAAA,CADlHyC,iBAAiB,8BAlC0DvD,EAAE,CAAAqB,iBAAA;EAAAC,IAAA,EAoCQiC,kBAAiB;EAAAhC,SAAA;EAAAC,SAAA;EAAAS,kBAAA,EAAAyB,GAAA;EAAAvB,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAsB,4BAAA/B,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MApC3B5B,EAAE,CAAAuC,eAAA,CAAAqB,GAAA;MAAF5D,EAAE,CAAA6D,cAAA,SAoCoK,CAAC;MApCvK7D,EAAE,CAAAwC,YAAA,EAoC6U,CAAC;MApChVxC,EAAE,CAAA8D,YAAA,CAoCqV,CAAC;MApCxV9D,EAAE,CAAAwC,YAAA,KAoC0pB,CAAC;MApC7pBxC,EAAE,CAAAwC,YAAA,KAoCqrB,CAAC;IAAA;EAAA;EAAAE,aAAA;EAAAC,eAAA;AAAA;AAEzwB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtCiF5C,EAAE,CAAA6C,iBAAA,CAsCQU,iBAAiB,EAAc,CAAC;IAC/GjC,IAAI,EAAEnB,SAAS;IACf2C,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAsB;MAAEL,aAAa,EAAEtC,iBAAiB,CAAC6C,IAAI;MAAEN,eAAe,EAAEtC,uBAAuB,CAAC6C,MAAM;MAAEF,IAAI,EAAE;QAAE,OAAO,EAAE;MAA2B,CAAC;MAAEX,QAAQ,EAAE;IAA2hB,CAAC;EAC5tB,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0B,cAAc,CAAC;AAGpBC,eAAA,GAHKD,cAAc;AAAAjD,eAAA,CAAdiD,cAAc,wBAAAE,wBAAA7C,iBAAA;EAAA,YAAAA,iBAAA,IACmF2C,eAAc;AAAA;AAAAjD,eAAA,CAD/GiD,cAAc,8BAjD6D/D,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EAmDQyC,eAAc;EAAAxC,SAAA;EAAAC,SAAA;AAAA;AAEzG;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KArDiF5C,EAAE,CAAA6C,iBAAA,CAqDQkB,cAAc,EAAc,CAAC;IAC5GzC,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAuB;IAC5C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkB,eAAe,CAAC;AAGrBC,gBAAA,GAHKD,eAAe;AAAApD,eAAA,CAAfoD,eAAe,wBAAAE,yBAAAhD,iBAAA;EAAA,YAAAA,iBAAA,IACkF8C,gBAAe;AAAA;AAAApD,eAAA,CADhHoD,eAAe,8BAnE4DlE,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EAqEQ4C,gBAAe;EAAA3C,SAAA;EAAAC,SAAA;AAAA;AAE1G;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KAvEiF5C,EAAE,CAAA6C,iBAAA,CAuEQqB,eAAe,EAAc,CAAC;IAC7G5C,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2DAA2D;MACrEC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwB;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,cAAc,CAAC;EAAAxD,YAAA;IACjB;IACA;IACA;IAAAC,eAAA,gBACQ,OAAO;EAAA;AAGnB;AAACwD,eAAA,GAPKD,cAAc;AAAAvD,eAAA,CAAduD,cAAc,wBAAAE,wBAAAnD,iBAAA;EAAA,YAAAA,iBAAA,IAKmFiD,eAAc;AAAA;AAAAvD,eAAA,CAL/GuD,cAAc,8BArF6DrE,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EA2FQ+C,eAAc;EAAA9C,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAA8C,6BAAA5C,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA3FxB5B,EAAE,CAAA8B,WAAA,mCAAAD,GAAA,CAAA4C,KAAA,KA2FkB,KAAG,CAAC;IAAA;EAAA;EAAA1C,MAAA;IAAA0C,KAAA;EAAA;EAAAzC,QAAA;AAAA;AAEzG;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KA7FiF5C,EAAE,CAAA6C,iBAAA,CA6FQwB,cAAc,EAAc,CAAC;IAC5G/C,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5Bf,QAAQ,EAAE,gBAAgB;MAC1BgB,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,wCAAwC,EAAE;MAC9C;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEyB,KAAK,EAAE,CAAC;MACtBnD,IAAI,EAAEhB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoE,aAAa,CAAC;AAGnBC,cAAA,GAHKD,aAAa;AAAA5D,eAAA,CAAb4D,aAAa,wBAAAE,uBAAAxD,iBAAA;EAAA,YAAAA,iBAAA,IACoFsD,cAAa;AAAA;AAAA5D,eAAA,CAD9G4D,aAAa,8BAlH8D1E,EAAE,CAAAqB,iBAAA;EAAAC,IAAA,EAoHQoD,cAAa;EAAAnD,SAAA;EAAAC,SAAA;EAAAS,kBAAA,EAAA4C,GAAA;EAAA1C,KAAA;EAAAC,IAAA;EAAA0C,MAAA;EAAAzC,QAAA,WAAA0C,wBAAAnD,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MApHvB5B,EAAE,CAAAuC,eAAA,CAAAyC,GAAA;MAAFhF,EAAE,CAAAwC,YAAA,EAoHwN,CAAC;MApH3NxC,EAAE,CAAA6D,cAAA,YAoHkQ,CAAC;MApHrQ7D,EAAE,CAAAwC,YAAA,KAoH2a,CAAC;MApH9axC,EAAE,CAAA8D,YAAA,CAoHmb,CAAC;MApHtb9D,EAAE,CAAAwC,YAAA,KAoH8c,CAAC;IAAA;EAAA;EAAAE,aAAA;EAAAC,eAAA;AAAA;AAEliB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtHiF5C,EAAE,CAAA6C,iBAAA,CAsHQ6B,aAAa,EAAc,CAAC;IAC3GpD,IAAI,EAAEnB,SAAS;IACf2C,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAEL,aAAa,EAAEtC,iBAAiB,CAAC6C,IAAI;MAAEN,eAAe,EAAEtC,uBAAuB,CAAC6C,MAAM;MAAEF,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB,CAAC;MAAEX,QAAQ,EAAE;IAAkU,CAAC;EACzf,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4C,aAAa,CAAC;AAGnBC,cAAA,GAHKD,aAAa;AAAAnE,eAAA,CAAbmE,aAAa,wBAAAE,uBAAA/D,iBAAA;EAAA,YAAAA,iBAAA,IACoF6D,cAAa;AAAA;AAAAnE,eAAA,CAD9GmE,aAAa,8BAjI8DjF,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EAmIQ2D,cAAa;EAAA1D,SAAA;EAAAC,SAAA;AAAA;AAExG;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KArIiF5C,EAAE,CAAA6C,iBAAA,CAqIQoC,aAAa,EAAc,CAAC;IAC3G3D,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,YAAY,CAAC;AAGlBC,aAAA,GAHKD,YAAY;AAAAtE,eAAA,CAAZsE,YAAY,wBAAAE,sBAAAlE,iBAAA;EAAA,YAAAA,iBAAA,IACqFgE,aAAY;AAAA;AAAAtE,eAAA,CAD7GsE,YAAY,8BAvJ+DpF,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EAyJQ8D,aAAY;EAAA7D,SAAA;EAAAC,SAAA;AAAA;AAEvG;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KA3JiF5C,EAAE,CAAA6C,iBAAA,CA2JQuC,YAAY,EAAc,CAAC;IAC1G9D,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAqC;IAC1D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMuC,cAAc,CAAC;AAGpBC,eAAA,GAHKD,cAAc;AAAAzE,eAAA,CAAdyE,cAAc,wBAAAE,wBAAArE,iBAAA;EAAA,YAAAA,iBAAA,IACmFmE,eAAc;AAAA;AAAAzE,eAAA,CAD/GyE,cAAc,8BAnK6DvF,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EAqKQiE,eAAc;EAAAhE,SAAA;EAAAC,SAAA;AAAA;AAEzG;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KAvKiF5C,EAAE,CAAA6C,iBAAA,CAuKQ0C,cAAc,EAAc,CAAC;IAC5GjE,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM0C,cAAc,CAAC;AAGpBC,eAAA,GAHKD,cAAc;AAAA5E,eAAA,CAAd4E,cAAc,wBAAAE,wBAAAxE,iBAAA;EAAA,YAAAA,iBAAA,IACmFsE,eAAc;AAAA;AAAA5E,eAAA,CAD/G4E,cAAc,8BA/K6D1F,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EAiLQoE,eAAc;EAAAnE,SAAA;EAAAC,SAAA;AAAA;AAEzG;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KAnLiF5C,EAAE,CAAA6C,iBAAA,CAmLQ6C,cAAc,EAAc,CAAC;IAC5GpE,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM6C,cAAc,CAAC;AAGpBC,eAAA,GAHKD,cAAc;AAAA/E,eAAA,CAAd+E,cAAc,wBAAAE,wBAAA3E,iBAAA;EAAA,YAAAA,iBAAA,IACmFyE,eAAc;AAAA;AAAA/E,eAAA,CAD/G+E,cAAc,8BA3L6D7F,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EA6LQuE,eAAc;EAAAtE,SAAA;EAAAC,SAAA;AAAA;AAEzG;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KA/LiF5C,EAAE,CAAA6C,iBAAA,CA+LQgD,cAAc,EAAc,CAAC;IAC5GvE,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMgD,cAAc,CAAC;AAGpBC,eAAA,GAHKD,cAAc;AAAAlF,eAAA,CAAdkF,cAAc,wBAAAE,wBAAA9E,iBAAA;EAAA,YAAAA,iBAAA,IACmF4E,eAAc;AAAA;AAAAlF,eAAA,CAD/GkF,cAAc,8BAvM6DhG,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EAyMQ0E,eAAc;EAAAzE,SAAA;EAAAC,SAAA;AAAA;AAEzG;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KA3MiF5C,EAAE,CAAA6C,iBAAA,CA2MQmD,cAAc,EAAc,CAAC;IAC5G1E,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmD,aAAa,CAAC;AAGnBC,cAAA,GAHKD,aAAa;AAAArF,eAAA,CAAbqF,aAAa,wBAAAE,uBAAAjF,iBAAA;EAAA,YAAAA,iBAAA,IACoF+E,cAAa;AAAA;AAAArF,eAAA,CAD9GqF,aAAa,8BA3N8DnG,EAAE,CAAAsD,iBAAA;EAAAhC,IAAA,EA6NQ6E,cAAa;EAAA5E,SAAA;EAAAC,SAAA;AAAA;AAExG;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KA/NiF5C,EAAE,CAAA6C,iBAAA,CA+NQsD,aAAa,EAAc,CAAC;IAC3G7E,IAAI,EAAEf,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMsD,eAAe,GAAG,CACpB1F,OAAO,EACPyD,cAAc,EACd8B,aAAa,EACbpC,cAAc,EACdkB,aAAa,EACbP,aAAa,EACbU,YAAY,EACZS,cAAc,EACdH,cAAc,EACdH,cAAc,EACdrB,eAAe,EACff,YAAY,EACZI,iBAAiB,EACjByC,cAAc,CACjB;AACD,MAAMO,aAAa,CAAC;AA8BnBC,cAAA,GA9BKD,aAAa;AAAAzF,eAAA,CAAbyF,aAAa,wBAAAE,uBAAArF,iBAAA;EAAA,YAAAA,iBAAA,IACoFmF,cAAa;AAAA;AAAAzF,eAAA,CAD9GyF,aAAa,8BAvP8DvG,EAAE,CAAA0G,gBAAA;EAAApF,IAAA,EAyPqBiF,cAAa;EAAAI,OAAA,GAAYjG,eAAe,EAAEE,OAAO,EAC7IyD,cAAc,EACd8B,aAAa,EACbpC,cAAc,EACdkB,aAAa,EACbP,aAAa,EACbU,YAAY,EACZS,cAAc,EACdH,cAAc,EACdH,cAAc,EACdrB,eAAe,EACff,YAAY,EACZI,iBAAiB,EACjByC,cAAc;EAAAY,OAAA,GAAahG,OAAO,EAClCyD,cAAc,EACd8B,aAAa,EACbpC,cAAc,EACdkB,aAAa,EACbP,aAAa,EACbU,YAAY,EACZS,cAAc,EACdH,cAAc,EACdH,cAAc,EACdrB,eAAe,EACff,YAAY,EACZI,iBAAiB,EACjByC,cAAc,EAAEtF,eAAe;AAAA;AAAAI,eAAA,CA5BrCyF,aAAa,8BAvP8DvG,EAAE,CAAA6G,gBAAA;EAAAF,OAAA,GAoR8CjG,eAAe,EAAEA,eAAe;AAAA;AAEjK;EAAA,QAAAkC,SAAA,oBAAAA,SAAA,KAtRiF5C,EAAE,CAAA6C,iBAAA,CAsRQ0D,aAAa,EAAc,CAAC;IAC3GjF,IAAI,EAAEd,QAAQ;IACdsC,IAAI,EAAE,CAAC;MACC6D,OAAO,EAAE,CAACjG,eAAe,EAAE,GAAG4F,eAAe,CAAC;MAC9CM,OAAO,EAAE,CAACN,eAAe,EAAE5F,eAAe;IAC9C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASC,eAAe,EAAEC,OAAO,EAAEyD,cAAc,EAAE8B,aAAa,EAAEpC,cAAc,EAAEkB,aAAa,EAAEP,aAAa,EAAEU,YAAY,EAAES,cAAc,EAAEH,cAAc,EAAEa,aAAa,EAAEhB,cAAc,EAAErB,eAAe,EAAEf,YAAY,EAAEI,iBAAiB,EAAEyC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}