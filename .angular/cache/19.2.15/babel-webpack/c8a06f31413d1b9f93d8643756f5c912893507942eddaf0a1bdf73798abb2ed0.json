{"ast": null, "code": "import { InjectionToken } from '@angular/core';\nimport { getDefaultWidgetsList } from './registry/default-list';\nexport const WidgetAddTypes = {\n  EXPAND: 'EXPAND',\n  REPLACE: 'REPLACE'\n};\nexport const GRID_CONFIG = new InjectionToken('SWUI_GRID_CONFIG');\nexport function getValidGridConfig(config) {\n  let configWidgets;\n  let widgetAddType = WidgetAddTypes.EXPAND;\n  let widgets = getDefaultWidgetsList();\n  if (config) {\n    if (config.widgets) {\n      configWidgets = config.widgets;\n    }\n    if (config.widgetAddType) {\n      widgetAddType = config.widgetAddType;\n    }\n  }\n  if (configWidgets) {\n    if (widgetAddType === WidgetAddTypes.REPLACE) {\n      widgets = configWidgets;\n    }\n    if (widgetAddType === WidgetAddTypes.EXPAND) {\n      // needs to perform override if it's required\n      const customTypes = configWidgets.map(item => item.type);\n      widgets = widgets.filter(item => customTypes.indexOf(item.type) === -1);\n      widgets = [...widgets, ...configWidgets];\n    }\n  }\n  return {\n    widgets,\n    widgetAddType\n  };\n}", "map": {"version": 3, "names": ["InjectionToken", "getDefaultWidgetsList", "WidgetAddTypes", "EXPAND", "REPLACE", "GRID_CONFIG", "getValidGridConfig", "config", "configWidgets", "widgetAddType", "widgets", "customTypes", "map", "item", "type", "filter", "indexOf"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid.config.ts"], "sourcesContent": ["import { InjectionToken } from '@angular/core';\nimport { getDefaultWidgetsList } from './registry/default-list';\nexport const WidgetAddTypes = {\n    EXPAND: 'EXPAND',\n    REPLACE: 'REPLACE'\n};\nexport const GRID_CONFIG = new InjectionToken('SWUI_GRID_CONFIG');\nexport function getValidGridConfig(config) {\n    let configWidgets;\n    let widgetAddType = WidgetAddTypes.EXPAND;\n    let widgets = getDefaultWidgetsList();\n    if (config) {\n        if (config.widgets) {\n            configWidgets = config.widgets;\n        }\n        if (config.widgetAddType) {\n            widgetAddType = config.widgetAddType;\n        }\n    }\n    if (configWidgets) {\n        if (widgetAddType === WidgetAddTypes.REPLACE) {\n            widgets = configWidgets;\n        }\n        if (widgetAddType === WidgetAddTypes.EXPAND) {\n            // needs to perform override if it's required\n            const customTypes = configWidgets.map((item) => item.type);\n            widgets = widgets.filter((item) => customTypes.indexOf(item.type) === -1);\n            widgets = [...widgets, ...configWidgets];\n        }\n    }\n    return { widgets, widgetAddType };\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,eAAe;AAC9C,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,OAAO,MAAMC,cAAc,GAAG;EAC1BC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE;AACb,CAAC;AACD,OAAO,MAAMC,WAAW,GAAG,IAAIL,cAAc,CAAC,kBAAkB,CAAC;AACjE,OAAO,SAASM,kBAAkBA,CAACC,MAAM,EAAE;EACvC,IAAIC,aAAa;EACjB,IAAIC,aAAa,GAAGP,cAAc,CAACC,MAAM;EACzC,IAAIO,OAAO,GAAGT,qBAAqB,CAAC,CAAC;EACrC,IAAIM,MAAM,EAAE;IACR,IAAIA,MAAM,CAACG,OAAO,EAAE;MAChBF,aAAa,GAAGD,MAAM,CAACG,OAAO;IAClC;IACA,IAAIH,MAAM,CAACE,aAAa,EAAE;MACtBA,aAAa,GAAGF,MAAM,CAACE,aAAa;IACxC;EACJ;EACA,IAAID,aAAa,EAAE;IACf,IAAIC,aAAa,KAAKP,cAAc,CAACE,OAAO,EAAE;MAC1CM,OAAO,GAAGF,aAAa;IAC3B;IACA,IAAIC,aAAa,KAAKP,cAAc,CAACC,MAAM,EAAE;MACzC;MACA,MAAMQ,WAAW,GAAGH,aAAa,CAACI,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,CAAC;MAC1DJ,OAAO,GAAGA,OAAO,CAACK,MAAM,CAAEF,IAAI,IAAKF,WAAW,CAACK,OAAO,CAACH,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;MACzEJ,OAAO,GAAG,CAAC,GAAGA,OAAO,EAAE,GAAGF,aAAa,CAAC;IAC5C;EACJ;EACA,OAAO;IAAEE,OAAO;IAAED;EAAc,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}