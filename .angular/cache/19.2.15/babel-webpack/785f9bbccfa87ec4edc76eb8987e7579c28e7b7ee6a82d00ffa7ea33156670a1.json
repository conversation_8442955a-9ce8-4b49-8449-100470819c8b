{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _InputModalityDetector, _FocusMonitor, _CdkMonitorFocus;\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, NgZone, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output } from '@angular/core';\nimport { BehaviorSubject, Subject, of } from 'rxjs';\nimport { skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport { d as ALT, C as CONTROL, M as MAC_META, e as META, f as SHIFT } from './keycodes-CpHkExLC.mjs';\nimport { _ as _getEventTarget, a as _getShadowRoot } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { n as normalizePassiveListenerOptions } from './passive-listeners-esHZRgIN.mjs';\nimport { a as coerceElement } from './element-x4z00URv.mjs';\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n  passive: true,\n  capture: true\n};\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n  /** The most recently detected input modality. */\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n  /**\n   * The most recently detected input modality event target. Is null if no input modality has been\n   * detected or if the associated event target is null for some unknown reason.\n   */\n\n  constructor() {\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_listenerCleanups\", void 0);\n    /** Emits whenever an input modality is detected. */\n    _defineProperty(this, \"modalityDetected\", void 0);\n    /** Emits when the input modality changes. */\n    _defineProperty(this, \"modalityChanged\", void 0);\n    _defineProperty(this, \"_mostRecentTarget\", null);\n    /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n    _defineProperty(this, \"_modality\", new BehaviorSubject(null));\n    /** Options for this InputModalityDetector. */\n    _defineProperty(this, \"_options\", void 0);\n    /**\n     * The timestamp of the last touch input modality. Used to determine whether mousedown events\n     * should be attributed to mouse or touch.\n     */\n    _defineProperty(this, \"_lastTouchMs\", 0);\n    /**\n     * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n     * bound.\n     */\n    _defineProperty(this, \"_onKeydown\", event => {\n      var _this$_options;\n      // If this is one of the keys we should ignore, then ignore it and don't update the input\n      // modality to keyboard.\n      if ((_this$_options = this._options) !== null && _this$_options !== void 0 && (_this$_options = _this$_options.ignoreKeys) !== null && _this$_options !== void 0 && _this$_options.some(keyCode => keyCode === event.keyCode)) {\n        return;\n      }\n      this._modality.next('keyboard');\n      this._mostRecentTarget = _getEventTarget(event);\n    });\n    /**\n     * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    _defineProperty(this, \"_onMousedown\", event => {\n      // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n      // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n      // after the previous touch event.\n      if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n        return;\n      }\n      // Fake mousedown events are fired by some screen readers when controls are activated by the\n      // screen reader. Attribute them to keyboard input modality.\n      this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n      this._mostRecentTarget = _getEventTarget(event);\n    });\n    /**\n     * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    _defineProperty(this, \"_onTouchstart\", event => {\n      // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n      // events are fired. Again, attribute to keyboard input modality.\n      if (isFakeTouchstartFromScreenReader(event)) {\n        this._modality.next('keyboard');\n        return;\n      }\n      // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n      // triggered via mouse vs touch.\n      this._lastTouchMs = Date.now();\n      this._modality.next('touch');\n      this._mostRecentTarget = _getEventTarget(event);\n    });\n    const ngZone = inject(NgZone);\n    const document = inject(DOCUMENT);\n    const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, {\n      optional: true\n    });\n    this._options = _objectSpread(_objectSpread({}, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS), options);\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (this._platform.isBrowser) {\n      const renderer = inject(RendererFactory2).createRenderer(null, null);\n      this._listenerCleanups = ngZone.runOutsideAngular(() => {\n        return [_bindEventWithOptions(renderer, document, 'keydown', this._onKeydown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'mousedown', this._onMousedown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'touchstart', this._onTouchstart, modalityEventListenerOptions)];\n      });\n    }\n  }\n  ngOnDestroy() {\n    var _this$_listenerCleanu;\n    this._modality.complete();\n    (_this$_listenerCleanu = this._listenerCleanups) === null || _this$_listenerCleanu === void 0 || _this$_listenerCleanu.forEach(cleanup => cleanup());\n  }\n}\n_InputModalityDetector = InputModalityDetector;\n_defineProperty(InputModalityDetector, \"\\u0275fac\", function _InputModalityDetector_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _InputModalityDetector)();\n});\n_defineProperty(InputModalityDetector, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _InputModalityDetector,\n  factory: _InputModalityDetector.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n  constructor() {\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_inputModalityDetector\", inject(InputModalityDetector));\n    /** The focus origin that the next focus event is a result of. */\n    _defineProperty(this, \"_origin\", null);\n    /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n    _defineProperty(this, \"_lastFocusOrigin\", void 0);\n    /** Whether the window has just been focused. */\n    _defineProperty(this, \"_windowFocused\", false);\n    /** The timeout id of the window focus timeout. */\n    _defineProperty(this, \"_windowFocusTimeoutId\", void 0);\n    /** The timeout id of the origin clearing timeout. */\n    _defineProperty(this, \"_originTimeoutId\", void 0);\n    /**\n     * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n     * focus events to touch interactions requires special logic.\n     */\n    _defineProperty(this, \"_originFromTouchInteraction\", false);\n    /** Map of elements being monitored to their info. */\n    _defineProperty(this, \"_elementInfo\", new Map());\n    /** The number of elements currently being monitored. */\n    _defineProperty(this, \"_monitoredElementCount\", 0);\n    /**\n     * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n     * as well as the number of monitored elements that they contain. We have to treat focus/blur\n     * handlers differently from the rest of the events, because the browser won't emit events\n     * to the document when focus moves inside of a shadow root.\n     */\n    _defineProperty(this, \"_rootNodeFocusListenerCount\", new Map());\n    /**\n     * The specified detection mode, used for attributing the origin of a focus\n     * event.\n     */\n    _defineProperty(this, \"_detectionMode\", void 0);\n    /**\n     * Event listener for `focus` events on the window.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    _defineProperty(this, \"_windowFocusListener\", () => {\n      // Make a note of when the window regains focus, so we can\n      // restore the origin info for the focused element.\n      this._windowFocused = true;\n      this._windowFocusTimeoutId = setTimeout(() => this._windowFocused = false);\n    });\n    /** Used to reference correct document/window */\n    _defineProperty(this, \"_document\", inject(DOCUMENT, {\n      optional: true\n    }));\n    /** Subject for stopping our InputModalityDetector subscription. */\n    _defineProperty(this, \"_stopInputModalityDetector\", new Subject());\n    /**\n     * Event listener for `focus` and 'blur' events on the document.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    _defineProperty(this, \"_rootNodeFocusAndBlurListener\", event => {\n      const target = _getEventTarget(event);\n      // We need to walk up the ancestor chain in order to support `checkChildren`.\n      for (let element = target; element; element = element.parentElement) {\n        if (event.type === 'focus') {\n          this._onFocus(event, element);\n        } else {\n          this._onBlur(event, element);\n        }\n      }\n    });\n    const options = inject(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this._detectionMode = (options === null || options === void 0 ? void 0 : options.detectionMode) || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element);\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return of();\n    }\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n      return cachedInfo.subject;\n    }\n    // Create monitored element info.\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n    return info.subject;\n  }\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n    if (elementInfo) {\n      elementInfo.subject.complete();\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin);\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n    return 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === FocusMonitorDetectionMode.EVENTUAL || !!(focusEventTarget !== null && focusEventTarget !== void 0 && focusEventTarget.contains(this._inputModalityDetector._mostRecentTarget));\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget(event);\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n  _emitOrigin(info, origin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true /* isFromInteraction */);\n      });\n    }\n  }\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n    // Unregister global listeners when last element is unmonitored.\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  _getClosestElementsInfo(element) {\n    const results = [];\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n    return results;\n  }\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  _isLastInteractionFromInputLabel(focusEventTarget) {\n    const {\n      _mostRecentTarget: mostRecentTarget,\n      mostRecentModality\n    } = this._inputModalityDetector;\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n      return false;\n    }\n    const labels = focusEventTarget.labels;\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n}\n_FocusMonitor = FocusMonitor;\n_defineProperty(FocusMonitor, \"\\u0275fac\", function _FocusMonitor_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FocusMonitor)();\n});\n_defineProperty(FocusMonitor, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _FocusMonitor,\n  factory: _FocusMonitor.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_focusMonitor\", inject(FocusMonitor));\n    _defineProperty(this, \"_monitorSubscription\", void 0);\n    _defineProperty(this, \"_focusOrigin\", null);\n    _defineProperty(this, \"cdkFocusChange\", new EventEmitter());\n  }\n  get focusOrigin() {\n    return this._focusOrigin;\n  }\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n      this._focusOrigin = origin;\n      this.cdkFocusChange.emit(origin);\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n}\n_CdkMonitorFocus = CdkMonitorFocus;\n_defineProperty(CdkMonitorFocus, \"\\u0275fac\", function _CdkMonitorFocus_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkMonitorFocus)();\n});\n_defineProperty(CdkMonitorFocus, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkMonitorFocus,\n  selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n  outputs: {\n    cdkFocusChange: \"cdkFocusChange\"\n  },\n  exportAs: [\"cdkMonitorFocus\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n      exportAs: 'cdkMonitorFocus'\n    }]\n  }], () => [], {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\nexport { CdkMonitorFocus as C, FocusMonitor as F, InputModalityDetector as I, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS as a, INPUT_MODALITY_DETECTOR_OPTIONS as b, FocusMonitorDetectionMode as c, FOCUS_MONITOR_DEFAULT_OPTIONS as d };\n//# sourceMappingURL=focus-monitor-e2l_RpN3.mjs.map", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "NgZone", "RendererFactory2", "Injectable", "ElementRef", "EventEmitter", "Directive", "Output", "BehaviorSubject", "Subject", "of", "skip", "distinctUntilChanged", "takeUntil", "DOCUMENT", "i", "isFakeMousedownFromScreenReader", "a", "isFakeTouchstartFromScreenReader", "d", "ALT", "C", "CONTROL", "M", "MAC_META", "e", "META", "f", "SHIFT", "_", "_getEventTarget", "_getShadowRoot", "_bindEventWithOptions", "P", "Platform", "n", "normalizePassiveListenerOptions", "coerceElement", "INPUT_MODALITY_DETECTOR_OPTIONS", "INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "TOUCH_BUFFER_MS", "modalityEventListenerOptions", "passive", "capture", "InputModalityDetector", "mostRecentModality", "_modality", "value", "constructor", "_defineProperty", "event", "_this$_options", "_options", "some", "keyCode", "next", "_mostRecentTarget", "Date", "now", "_lastTouchMs", "ngZone", "document", "options", "optional", "_objectSpread", "modalityDetected", "pipe", "modalityChanged", "_platform", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "_listenerCleanups", "runOutsideAngular", "_onKeydown", "_onMousedown", "_onTouchstart", "ngOnDestroy", "_this$_listenerCleanu", "complete", "for<PERSON>ach", "cleanup", "_InputModalityDetector", "_InputModalityDetector_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "FocusMonitorDetectionMode", "FOCUS_MONITOR_DEFAULT_OPTIONS", "captureEventListenerOptions", "FocusMonitor", "Map", "_windowFocused", "_windowFocusTimeoutId", "setTimeout", "target", "element", "parentElement", "_onFocus", "_onBlur", "_detectionMode", "detectionMode", "IMMEDIATE", "monitor", "check<PERSON><PERSON><PERSON><PERSON>", "nativeElement", "nodeType", "rootNode", "_getDocument", "cachedInfo", "_elementInfo", "get", "subject", "info", "set", "_registerGlobalListeners", "stopMonitoring", "elementInfo", "_setClasses", "delete", "_removeGlobalListeners", "focusVia", "origin", "focusedElement", "activeElement", "_getClosestElementsInfo", "currentElement", "_originChanged", "_set<PERSON><PERSON><PERSON>", "focus", "_info", "_document", "_getWindow", "doc", "defaultView", "window", "_getFocus<PERSON><PERSON>in", "focusEventTarget", "_origin", "_originFromTouchInteraction", "_shouldBeAttributedToTouch", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_isLastInteractionFromInputLabel", "EVENTUAL", "contains", "_inputModalityDetector", "classList", "toggle", "isFromInteraction", "_ngZone", "clearTimeout", "_originTimeoutId", "ms", "relatedTarget", "Node", "_emit<PERSON><PERSON>in", "observers", "length", "run", "rootNodeFocusListeners", "_rootNodeFocusListenerCount", "addEventListener", "_rootNodeFocusAndBlurListener", "_monitoredElementCount", "_windowFocusListener", "_stopInputModalityDetector", "subscribe", "modality", "has", "removeEventListener", "results", "push", "mostRecentTarget", "nodeName", "disabled", "labels", "_FocusMonitor", "_FocusMonitor_Factory", "CdkMonitorFocus", "<PERSON><PERSON><PERSON><PERSON>", "_focus<PERSON><PERSON>in", "ngAfterViewInit", "_elementRef", "_monitorSubscription", "_focusMonitor", "hasAttribute", "cdkFocusChange", "emit", "unsubscribe", "_CdkMonitorFocus", "_CdkMonitorFocus_Factory", "ɵɵdefineDirective", "selectors", "outputs", "exportAs", "selector", "F", "I", "b", "c"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/focus-monitor-e2l_RpN3.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Ng<PERSON><PERSON>, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output } from '@angular/core';\nimport { BehaviorSubject, Subject, of } from 'rxjs';\nimport { skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport { d as ALT, C as CONTROL, M as MAC_META, e as META, f as SHIFT } from './keycodes-CpHkExLC.mjs';\nimport { _ as _getEventTarget, a as _getShadowRoot } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { n as normalizePassiveListenerOptions } from './passive-listeners-esHZRgIN.mjs';\nimport { a as coerceElement } from './element-x4z00URv.mjs';\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n    ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT],\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n    passive: true,\n    capture: true,\n};\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n    _platform = inject(Platform);\n    _listenerCleanups;\n    /** Emits whenever an input modality is detected. */\n    modalityDetected;\n    /** Emits when the input modality changes. */\n    modalityChanged;\n    /** The most recently detected input modality. */\n    get mostRecentModality() {\n        return this._modality.value;\n    }\n    /**\n     * The most recently detected input modality event target. Is null if no input modality has been\n     * detected or if the associated event target is null for some unknown reason.\n     */\n    _mostRecentTarget = null;\n    /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n    _modality = new BehaviorSubject(null);\n    /** Options for this InputModalityDetector. */\n    _options;\n    /**\n     * The timestamp of the last touch input modality. Used to determine whether mousedown events\n     * should be attributed to mouse or touch.\n     */\n    _lastTouchMs = 0;\n    /**\n     * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n     * bound.\n     */\n    _onKeydown = (event) => {\n        // If this is one of the keys we should ignore, then ignore it and don't update the input\n        // modality to keyboard.\n        if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n            return;\n        }\n        this._modality.next('keyboard');\n        this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    _onMousedown = (event) => {\n        // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n        // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n        // after the previous touch event.\n        if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n            return;\n        }\n        // Fake mousedown events are fired by some screen readers when controls are activated by the\n        // screen reader. Attribute them to keyboard input modality.\n        this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n        this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    _onTouchstart = (event) => {\n        // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n        // events are fired. Again, attribute to keyboard input modality.\n        if (isFakeTouchstartFromScreenReader(event)) {\n            this._modality.next('keyboard');\n            return;\n        }\n        // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n        // triggered via mouse vs touch.\n        this._lastTouchMs = Date.now();\n        this._modality.next('touch');\n        this._mostRecentTarget = _getEventTarget(event);\n    };\n    constructor() {\n        const ngZone = inject(NgZone);\n        const document = inject(DOCUMENT);\n        const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, { optional: true });\n        this._options = {\n            ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n            ...options,\n        };\n        // Skip the first emission as it's null.\n        this.modalityDetected = this._modality.pipe(skip(1));\n        this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n        // If we're not in a browser, this service should do nothing, as there's no relevant input\n        // modality to detect.\n        if (this._platform.isBrowser) {\n            const renderer = inject(RendererFactory2).createRenderer(null, null);\n            this._listenerCleanups = ngZone.runOutsideAngular(() => {\n                return [\n                    _bindEventWithOptions(renderer, document, 'keydown', this._onKeydown, modalityEventListenerOptions),\n                    _bindEventWithOptions(renderer, document, 'mousedown', this._onMousedown, modalityEventListenerOptions),\n                    _bindEventWithOptions(renderer, document, 'touchstart', this._onTouchstart, modalityEventListenerOptions),\n                ];\n            });\n        }\n    }\n    ngOnDestroy() {\n        this._modality.complete();\n        this._listenerCleanups?.forEach(cleanup => cleanup());\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InputModalityDetector, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InputModalityDetector, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InputModalityDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n    /**\n     * Any mousedown, keydown, or touchstart event that happened in the previous\n     * tick or the current tick will be used to assign a focus event's origin (to\n     * either mouse, keyboard, or touch). This is the default option.\n     */\n    FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n    /**\n     * A focus event's origin is always attributed to the last corresponding\n     * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n     */\n    FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _inputModalityDetector = inject(InputModalityDetector);\n    /** The focus origin that the next focus event is a result of. */\n    _origin = null;\n    /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n    _lastFocusOrigin;\n    /** Whether the window has just been focused. */\n    _windowFocused = false;\n    /** The timeout id of the window focus timeout. */\n    _windowFocusTimeoutId;\n    /** The timeout id of the origin clearing timeout. */\n    _originTimeoutId;\n    /**\n     * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n     * focus events to touch interactions requires special logic.\n     */\n    _originFromTouchInteraction = false;\n    /** Map of elements being monitored to their info. */\n    _elementInfo = new Map();\n    /** The number of elements currently being monitored. */\n    _monitoredElementCount = 0;\n    /**\n     * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n     * as well as the number of monitored elements that they contain. We have to treat focus/blur\n     * handlers differently from the rest of the events, because the browser won't emit events\n     * to the document when focus moves inside of a shadow root.\n     */\n    _rootNodeFocusListenerCount = new Map();\n    /**\n     * The specified detection mode, used for attributing the origin of a focus\n     * event.\n     */\n    _detectionMode;\n    /**\n     * Event listener for `focus` events on the window.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    _windowFocusListener = () => {\n        // Make a note of when the window regains focus, so we can\n        // restore the origin info for the focused element.\n        this._windowFocused = true;\n        this._windowFocusTimeoutId = setTimeout(() => (this._windowFocused = false));\n    };\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, { optional: true });\n    /** Subject for stopping our InputModalityDetector subscription. */\n    _stopInputModalityDetector = new Subject();\n    constructor() {\n        const options = inject(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n            optional: true,\n        });\n        this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n    }\n    /**\n     * Event listener for `focus` and 'blur' events on the document.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    _rootNodeFocusAndBlurListener = (event) => {\n        const target = _getEventTarget(event);\n        // We need to walk up the ancestor chain in order to support `checkChildren`.\n        for (let element = target; element; element = element.parentElement) {\n            if (event.type === 'focus') {\n                this._onFocus(event, element);\n            }\n            else {\n                this._onBlur(event, element);\n            }\n        }\n    };\n    monitor(element, checkChildren = false) {\n        const nativeElement = coerceElement(element);\n        // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n        if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n            // Note: we don't want the observable to emit at all so we don't pass any parameters.\n            return of();\n        }\n        // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n        // the shadow root, rather than the `document`, because the browser won't emit focus events\n        // to the `document`, if focus is moving within the same shadow root.\n        const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n        const cachedInfo = this._elementInfo.get(nativeElement);\n        // Check if we're already monitoring this element.\n        if (cachedInfo) {\n            if (checkChildren) {\n                // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n                // observers into ones that behave as if `checkChildren` was turned on. We need a more\n                // robust solution.\n                cachedInfo.checkChildren = true;\n            }\n            return cachedInfo.subject;\n        }\n        // Create monitored element info.\n        const info = {\n            checkChildren: checkChildren,\n            subject: new Subject(),\n            rootNode,\n        };\n        this._elementInfo.set(nativeElement, info);\n        this._registerGlobalListeners(info);\n        return info.subject;\n    }\n    stopMonitoring(element) {\n        const nativeElement = coerceElement(element);\n        const elementInfo = this._elementInfo.get(nativeElement);\n        if (elementInfo) {\n            elementInfo.subject.complete();\n            this._setClasses(nativeElement);\n            this._elementInfo.delete(nativeElement);\n            this._removeGlobalListeners(elementInfo);\n        }\n    }\n    focusVia(element, origin, options) {\n        const nativeElement = coerceElement(element);\n        const focusedElement = this._getDocument().activeElement;\n        // If the element is focused already, calling `focus` again won't trigger the event listener\n        // which means that the focus classes won't be updated. If that's the case, update the classes\n        // directly without waiting for an event.\n        if (nativeElement === focusedElement) {\n            this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n        }\n        else {\n            this._setOrigin(origin);\n            // `focus` isn't available on the server\n            if (typeof nativeElement.focus === 'function') {\n                nativeElement.focus(options);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n        return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        const doc = this._getDocument();\n        return doc.defaultView || window;\n    }\n    _getFocusOrigin(focusEventTarget) {\n        if (this._origin) {\n            // If the origin was realized via a touch interaction, we need to perform additional checks\n            // to determine whether the focus origin should be attributed to touch or program.\n            if (this._originFromTouchInteraction) {\n                return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n            }\n            else {\n                return this._origin;\n            }\n        }\n        // If the window has just regained focus, we can restore the most recent origin from before the\n        // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n        // focus. This typically means one of two things happened:\n        //\n        // 1) The element was programmatically focused, or\n        // 2) The element was focused via screen reader navigation (which generally doesn't fire\n        //    events).\n        //\n        // Because we can't distinguish between these two cases, we default to setting `program`.\n        if (this._windowFocused && this._lastFocusOrigin) {\n            return this._lastFocusOrigin;\n        }\n        // If the interaction is coming from an input label, we consider it a mouse interactions.\n        // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n        // our detection, because all our assumptions are for `mousedown`. We need to handle this\n        // special case, because it's very common for checkboxes and radio buttons.\n        if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n            return 'mouse';\n        }\n        return 'program';\n    }\n    /**\n     * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n     * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n     * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n     * event was directly caused by the touch interaction or (2) the focus event was caused by a\n     * subsequent programmatic focus call triggered by the touch interaction.\n     * @param focusEventTarget The target of the focus event under examination.\n     */\n    _shouldBeAttributedToTouch(focusEventTarget) {\n        // Please note that this check is not perfect. Consider the following edge case:\n        //\n        // <div #parent tabindex=\"0\">\n        //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n        // </div>\n        //\n        // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n        // #child, #parent is programmatically focused. This code will attribute the focus to touch\n        // instead of program. This is a relatively minor edge-case that can be worked around by using\n        // focusVia(parent, 'program') to focus #parent.\n        return (this._detectionMode === FocusMonitorDetectionMode.EVENTUAL ||\n            !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget));\n    }\n    /**\n     * Sets the focus classes on the element based on the given focus origin.\n     * @param element The element to update the classes on.\n     * @param origin The focus origin.\n     */\n    _setClasses(element, origin) {\n        element.classList.toggle('cdk-focused', !!origin);\n        element.classList.toggle('cdk-touch-focused', origin === 'touch');\n        element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n        element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n        element.classList.toggle('cdk-program-focused', origin === 'program');\n    }\n    /**\n     * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n     * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n     * the origin being set.\n     * @param origin The origin to set.\n     * @param isFromInteraction Whether we are setting the origin from an interaction event.\n     */\n    _setOrigin(origin, isFromInteraction = false) {\n        this._ngZone.runOutsideAngular(() => {\n            this._origin = origin;\n            this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n            // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n            // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n            // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n            // a touch event because when a touch event is fired, the associated focus event isn't yet in\n            // the event queue. Before doing so, clear any pending timeouts.\n            if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n                clearTimeout(this._originTimeoutId);\n                const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n                this._originTimeoutId = setTimeout(() => (this._origin = null), ms);\n            }\n        });\n    }\n    /**\n     * Handles focus events on a registered element.\n     * @param event The focus event.\n     * @param element The monitored element.\n     */\n    _onFocus(event, element) {\n        // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n        // focus event affecting the monitored element. If we want to use the origin of the first event\n        // instead we should check for the cdk-focused class here and return if the element already has\n        // it. (This only matters for elements that have includesChildren = true).\n        // If we are not counting child-element-focus as focused, make sure that the event target is the\n        // monitored element itself.\n        const elementInfo = this._elementInfo.get(element);\n        const focusEventTarget = _getEventTarget(event);\n        if (!elementInfo || (!elementInfo.checkChildren && element !== focusEventTarget)) {\n            return;\n        }\n        this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n    }\n    /**\n     * Handles blur events on a registered element.\n     * @param event The blur event.\n     * @param element The monitored element.\n     */\n    _onBlur(event, element) {\n        // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n        // order to focus another child of the monitored element.\n        const elementInfo = this._elementInfo.get(element);\n        if (!elementInfo ||\n            (elementInfo.checkChildren &&\n                event.relatedTarget instanceof Node &&\n                element.contains(event.relatedTarget))) {\n            return;\n        }\n        this._setClasses(element);\n        this._emitOrigin(elementInfo, null);\n    }\n    _emitOrigin(info, origin) {\n        if (info.subject.observers.length) {\n            this._ngZone.run(() => info.subject.next(origin));\n        }\n    }\n    _registerGlobalListeners(elementInfo) {\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        const rootNode = elementInfo.rootNode;\n        const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n        if (!rootNodeFocusListeners) {\n            this._ngZone.runOutsideAngular(() => {\n                rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n            });\n        }\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n        // Register global listeners when first element is monitored.\n        if (++this._monitoredElementCount === 1) {\n            // Note: we listen to events in the capture phase so we\n            // can detect them even if the user stops propagation.\n            this._ngZone.runOutsideAngular(() => {\n                const window = this._getWindow();\n                window.addEventListener('focus', this._windowFocusListener);\n            });\n            // The InputModalityDetector is also just a collection of global listeners.\n            this._inputModalityDetector.modalityDetected\n                .pipe(takeUntil(this._stopInputModalityDetector))\n                .subscribe(modality => {\n                this._setOrigin(modality, true /* isFromInteraction */);\n            });\n        }\n    }\n    _removeGlobalListeners(elementInfo) {\n        const rootNode = elementInfo.rootNode;\n        if (this._rootNodeFocusListenerCount.has(rootNode)) {\n            const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n            if (rootNodeFocusListeners > 1) {\n                this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n            }\n            else {\n                rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                this._rootNodeFocusListenerCount.delete(rootNode);\n            }\n        }\n        // Unregister global listeners when last element is unmonitored.\n        if (!--this._monitoredElementCount) {\n            const window = this._getWindow();\n            window.removeEventListener('focus', this._windowFocusListener);\n            // Equivalently, stop our InputModalityDetector subscription.\n            this._stopInputModalityDetector.next();\n            // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n            clearTimeout(this._windowFocusTimeoutId);\n            clearTimeout(this._originTimeoutId);\n        }\n    }\n    /** Updates all the state on an element once its focus origin has changed. */\n    _originChanged(element, origin, elementInfo) {\n        this._setClasses(element, origin);\n        this._emitOrigin(elementInfo, origin);\n        this._lastFocusOrigin = origin;\n    }\n    /**\n     * Collects the `MonitoredElementInfo` of a particular element and\n     * all of its ancestors that have enabled `checkChildren`.\n     * @param element Element from which to start the search.\n     */\n    _getClosestElementsInfo(element) {\n        const results = [];\n        this._elementInfo.forEach((info, currentElement) => {\n            if (currentElement === element || (info.checkChildren && currentElement.contains(element))) {\n                results.push([currentElement, info]);\n            }\n        });\n        return results;\n    }\n    /**\n     * Returns whether an interaction is likely to have come from the user clicking the `label` of\n     * an `input` or `textarea` in order to focus it.\n     * @param focusEventTarget Target currently receiving focus.\n     */\n    _isLastInteractionFromInputLabel(focusEventTarget) {\n        const { _mostRecentTarget: mostRecentTarget, mostRecentModality } = this._inputModalityDetector;\n        // If the last interaction used the mouse on an element contained by one of the labels\n        // of an `input`/`textarea` that is currently focused, it is very likely that the\n        // user redirected focus using the label.\n        if (mostRecentModality !== 'mouse' ||\n            !mostRecentTarget ||\n            mostRecentTarget === focusEventTarget ||\n            (focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA') ||\n            focusEventTarget.disabled) {\n            return false;\n        }\n        const labels = focusEventTarget.labels;\n        if (labels) {\n            for (let i = 0; i < labels.length; i++) {\n                if (labels[i].contains(mostRecentTarget)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusMonitor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusMonitor, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n    _elementRef = inject(ElementRef);\n    _focusMonitor = inject(FocusMonitor);\n    _monitorSubscription;\n    _focusOrigin = null;\n    cdkFocusChange = new EventEmitter();\n    constructor() { }\n    get focusOrigin() {\n        return this._focusOrigin;\n    }\n    ngAfterViewInit() {\n        const element = this._elementRef.nativeElement;\n        this._monitorSubscription = this._focusMonitor\n            .monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus'))\n            .subscribe(origin => {\n            this._focusOrigin = origin;\n            this.cdkFocusChange.emit(origin);\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        if (this._monitorSubscription) {\n            this._monitorSubscription.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkMonitorFocus, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkMonitorFocus, isStandalone: true, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: { cdkFocusChange: \"cdkFocusChange\" }, exportAs: [\"cdkMonitorFocus\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkMonitorFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n                    exportAs: 'cdkMonitorFocus',\n                }]\n        }], ctorParameters: () => [], propDecorators: { cdkFocusChange: [{\n                type: Output\n            }] } });\n\nexport { CdkMonitorFocus as C, FocusMonitor as F, InputModalityDetector as I, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS as a, INPUT_MODALITY_DETECTOR_OPTIONS as b, FocusMonitorDetectionMode as c, FOCUS_MONITOR_DEFAULT_OPTIONS as d };\n//# sourceMappingURL=focus-monitor-e2l_RpN3.mjs.map\n"], "mappings": ";;;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACzI,SAASC,eAAe,EAAEC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AACnD,SAASC,IAAI,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AACtE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,CAAC,IAAIC,+BAA+B,EAAEC,CAAC,IAAIC,gCAAgC,QAAQ,qCAAqC;AACjI,SAASC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,KAAK,QAAQ,yBAAyB;AACtG,SAASC,CAAC,IAAIC,eAAe,EAAEb,CAAC,IAAIc,cAAc,QAAQ,2BAA2B;AACrF,SAASF,CAAC,IAAIG,qBAAqB,QAAQ,wCAAwC;AACnF,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,+BAA+B,QAAQ,kCAAkC;AACvF,SAASnB,CAAC,IAAIoB,aAAa,QAAQ,wBAAwB;;AAE3D;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG,IAAIvC,cAAc,CAAC,qCAAqC,CAAC;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwC,uCAAuC,GAAG;EAC5CC,UAAU,EAAE,CAACpB,GAAG,EAAEE,OAAO,EAAEE,QAAQ,EAAEE,IAAI,EAAEE,KAAK;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,eAAe,GAAG,GAAG;AAC3B;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,GAAG;EACjCC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EAOxB;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACC,SAAS,CAACC,KAAK;EAC/B;EACA;AACJ;AACA;AACA;;EAyDIC,WAAWA,CAAA,EAAG;IAAAC,eAAA,oBAtEFlD,MAAM,CAACkC,QAAQ,CAAC;IAAAgB,eAAA;IAE5B;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAAAA,eAAA,4BAUoB,IAAI;IACxB;IAAAA,eAAA,oBACY,IAAI1C,eAAe,CAAC,IAAI,CAAC;IACrC;IAAA0C,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA,uBAIe,CAAC;IAChB;AACJ;AACA;AACA;IAHIA,eAAA,qBAIcC,KAAK,IAAK;MAAA,IAAAC,cAAA;MACpB;MACA;MACA,KAAAA,cAAA,GAAI,IAAI,CAACC,QAAQ,cAAAD,cAAA,gBAAAA,cAAA,GAAbA,cAAA,CAAeZ,UAAU,cAAAY,cAAA,eAAzBA,cAAA,CAA2BE,IAAI,CAACC,OAAO,IAAIA,OAAO,KAAKJ,KAAK,CAACI,OAAO,CAAC,EAAE;QACvE;MACJ;MACA,IAAI,CAACR,SAAS,CAACS,IAAI,CAAC,UAAU,CAAC;MAC/B,IAAI,CAACC,iBAAiB,GAAG3B,eAAe,CAACqB,KAAK,CAAC;IACnD,CAAC;IACD;AACJ;AACA;AACA;IAHID,eAAA,uBAIgBC,KAAK,IAAK;MACtB;MACA;MACA;MACA,IAAIO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACC,YAAY,GAAGnB,eAAe,EAAE;QAClD;MACJ;MACA;MACA;MACA,IAAI,CAACM,SAAS,CAACS,IAAI,CAACxC,+BAA+B,CAACmC,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,CAAC;MAClF,IAAI,CAACM,iBAAiB,GAAG3B,eAAe,CAACqB,KAAK,CAAC;IACnD,CAAC;IACD;AACJ;AACA;AACA;IAHID,eAAA,wBAIiBC,KAAK,IAAK;MACvB;MACA;MACA,IAAIjC,gCAAgC,CAACiC,KAAK,CAAC,EAAE;QACzC,IAAI,CAACJ,SAAS,CAACS,IAAI,CAAC,UAAU,CAAC;QAC/B;MACJ;MACA;MACA;MACA,IAAI,CAACI,YAAY,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACZ,SAAS,CAACS,IAAI,CAAC,OAAO,CAAC;MAC5B,IAAI,CAACC,iBAAiB,GAAG3B,eAAe,CAACqB,KAAK,CAAC;IACnD,CAAC;IAEG,MAAMU,MAAM,GAAG7D,MAAM,CAACC,MAAM,CAAC;IAC7B,MAAM6D,QAAQ,GAAG9D,MAAM,CAACc,QAAQ,CAAC;IACjC,MAAMiD,OAAO,GAAG/D,MAAM,CAACsC,+BAA+B,EAAE;MAAE0B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3E,IAAI,CAACX,QAAQ,GAAAY,aAAA,CAAAA,aAAA,KACN1B,uCAAuC,GACvCwB,OAAO,CACb;IACD;IACA,IAAI,CAACG,gBAAgB,GAAG,IAAI,CAACnB,SAAS,CAACoB,IAAI,CAACxD,IAAI,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,CAACyD,eAAe,GAAG,IAAI,CAACF,gBAAgB,CAACC,IAAI,CAACvD,oBAAoB,CAAC,CAAC,CAAC;IACzE;IACA;IACA,IAAI,IAAI,CAACyD,SAAS,CAACC,SAAS,EAAE;MAC1B,MAAMC,QAAQ,GAAGvE,MAAM,CAACE,gBAAgB,CAAC,CAACsE,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;MACpE,IAAI,CAACC,iBAAiB,GAAGZ,MAAM,CAACa,iBAAiB,CAAC,MAAM;QACpD,OAAO,CACH1C,qBAAqB,CAACuC,QAAQ,EAAET,QAAQ,EAAE,SAAS,EAAE,IAAI,CAACa,UAAU,EAAEjC,4BAA4B,CAAC,EACnGV,qBAAqB,CAACuC,QAAQ,EAAET,QAAQ,EAAE,WAAW,EAAE,IAAI,CAACc,YAAY,EAAElC,4BAA4B,CAAC,EACvGV,qBAAqB,CAACuC,QAAQ,EAAET,QAAQ,EAAE,YAAY,EAAE,IAAI,CAACe,aAAa,EAAEnC,4BAA4B,CAAC,CAC5G;MACL,CAAC,CAAC;IACN;EACJ;EACAoC,WAAWA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACV,IAAI,CAAChC,SAAS,CAACiC,QAAQ,CAAC,CAAC;IACzB,CAAAD,qBAAA,OAAI,CAACN,iBAAiB,cAAAM,qBAAA,eAAtBA,qBAAA,CAAwBE,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;EACzD;AAGJ;AAACC,sBAAA,GArGKtC,qBAAqB;AAAAK,eAAA,CAArBL,qBAAqB,wBAAAuC,+BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAmG4ExC,sBAAqB;AAAA;AAAAK,eAAA,CAnGtHL,qBAAqB,+BAsGsD/C,EAAE,CAAAwF,kBAAA;EAAAC,KAAA,EAFwB1C,sBAAqB;EAAA2C,OAAA,EAArB3C,sBAAqB,CAAA4C,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEpJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF7F,EAAE,CAAA8F,iBAAA,CAAQ/C,qBAAqB,EAAc,CAAC;IACnHgD,IAAI,EAAE1F,UAAU;IAChB2F,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,IAAIK,yBAAyB;AAC7B,CAAC,UAAUA,yBAAyB,EAAE;EAClC;AACJ;AACA;AACA;AACA;EACIA,yBAAyB,CAACA,yBAAyB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACnF;AACJ;AACA;AACA;EACIA,yBAAyB,CAACA,yBAAyB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AACrF,CAAC,EAAEA,yBAAyB,KAAKA,yBAAyB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjE;AACA,MAAMC,6BAA6B,GAAG,IAAIjG,cAAc,CAAC,mCAAmC,CAAC;AAC7F;AACA;AACA;AACA;AACA,MAAMkG,2BAA2B,GAAG7D,+BAA+B,CAAC;EAChEO,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMsD,YAAY,CAAC;EAiDfjD,WAAWA,CAAA,EAAG;IAAAC,eAAA,kBAhDJlD,MAAM,CAACC,MAAM,CAAC;IAAAiD,eAAA,oBACZlD,MAAM,CAACkC,QAAQ,CAAC;IAAAgB,eAAA,iCACHlD,MAAM,CAAC6C,qBAAqB,CAAC;IACtD;IAAAK,eAAA,kBACU,IAAI;IACd;IAAAA,eAAA;IAEA;IAAAA,eAAA,yBACiB,KAAK;IACtB;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA,sCAI8B,KAAK;IACnC;IAAAA,eAAA,uBACe,IAAIiD,GAAG,CAAC,CAAC;IACxB;IAAAjD,eAAA,iCACyB,CAAC;IAC1B;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,sCAM8B,IAAIiD,GAAG,CAAC,CAAC;IACvC;AACJ;AACA;AACA;IAHIjD,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA,+BAIuB,MAAM;MACzB;MACA;MACA,IAAI,CAACkD,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACC,qBAAqB,GAAGC,UAAU,CAAC,MAAO,IAAI,CAACF,cAAc,GAAG,KAAM,CAAC;IAChF,CAAC;IACD;IAAAlD,eAAA,oBACYlD,MAAM,CAACc,QAAQ,EAAE;MAAEkD,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChD;IAAAd,eAAA,qCAC6B,IAAIzC,OAAO,CAAC,CAAC;IAO1C;AACJ;AACA;AACA;IAHIyC,eAAA,wCAIiCC,KAAK,IAAK;MACvC,MAAMoD,MAAM,GAAGzE,eAAe,CAACqB,KAAK,CAAC;MACrC;MACA,KAAK,IAAIqD,OAAO,GAAGD,MAAM,EAAEC,OAAO,EAAEA,OAAO,GAAGA,OAAO,CAACC,aAAa,EAAE;QACjE,IAAItD,KAAK,CAAC0C,IAAI,KAAK,OAAO,EAAE;UACxB,IAAI,CAACa,QAAQ,CAACvD,KAAK,EAAEqD,OAAO,CAAC;QACjC,CAAC,MACI;UACD,IAAI,CAACG,OAAO,CAACxD,KAAK,EAAEqD,OAAO,CAAC;QAChC;MACJ;IACJ,CAAC;IApBG,MAAMzC,OAAO,GAAG/D,MAAM,CAACgG,6BAA6B,EAAE;MAClDhC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAAC4C,cAAc,GAAG,CAAA7C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8C,aAAa,KAAId,yBAAyB,CAACe,SAAS;EACvF;EAiBAC,OAAOA,CAACP,OAAO,EAAEQ,aAAa,GAAG,KAAK,EAAE;IACpC,MAAMC,aAAa,GAAG5E,aAAa,CAACmE,OAAO,CAAC;IAC5C;IACA,IAAI,CAAC,IAAI,CAACnC,SAAS,CAACC,SAAS,IAAI2C,aAAa,CAACC,QAAQ,KAAK,CAAC,EAAE;MAC3D;MACA,OAAOxG,EAAE,CAAC,CAAC;IACf;IACA;IACA;IACA;IACA,MAAMyG,QAAQ,GAAGpF,cAAc,CAACkF,aAAa,CAAC,IAAI,IAAI,CAACG,YAAY,CAAC,CAAC;IACrE,MAAMC,UAAU,GAAG,IAAI,CAACC,YAAY,CAACC,GAAG,CAACN,aAAa,CAAC;IACvD;IACA,IAAII,UAAU,EAAE;MACZ,IAAIL,aAAa,EAAE;QACf;QACA;QACA;QACAK,UAAU,CAACL,aAAa,GAAG,IAAI;MACnC;MACA,OAAOK,UAAU,CAACG,OAAO;IAC7B;IACA;IACA,MAAMC,IAAI,GAAG;MACTT,aAAa,EAAEA,aAAa;MAC5BQ,OAAO,EAAE,IAAI/G,OAAO,CAAC,CAAC;MACtB0G;IACJ,CAAC;IACD,IAAI,CAACG,YAAY,CAACI,GAAG,CAACT,aAAa,EAAEQ,IAAI,CAAC;IAC1C,IAAI,CAACE,wBAAwB,CAACF,IAAI,CAAC;IACnC,OAAOA,IAAI,CAACD,OAAO;EACvB;EACAI,cAAcA,CAACpB,OAAO,EAAE;IACpB,MAAMS,aAAa,GAAG5E,aAAa,CAACmE,OAAO,CAAC;IAC5C,MAAMqB,WAAW,GAAG,IAAI,CAACP,YAAY,CAACC,GAAG,CAACN,aAAa,CAAC;IACxD,IAAIY,WAAW,EAAE;MACbA,WAAW,CAACL,OAAO,CAACxC,QAAQ,CAAC,CAAC;MAC9B,IAAI,CAAC8C,WAAW,CAACb,aAAa,CAAC;MAC/B,IAAI,CAACK,YAAY,CAACS,MAAM,CAACd,aAAa,CAAC;MACvC,IAAI,CAACe,sBAAsB,CAACH,WAAW,CAAC;IAC5C;EACJ;EACAI,QAAQA,CAACzB,OAAO,EAAE0B,MAAM,EAAEnE,OAAO,EAAE;IAC/B,MAAMkD,aAAa,GAAG5E,aAAa,CAACmE,OAAO,CAAC;IAC5C,MAAM2B,cAAc,GAAG,IAAI,CAACf,YAAY,CAAC,CAAC,CAACgB,aAAa;IACxD;IACA;IACA;IACA,IAAInB,aAAa,KAAKkB,cAAc,EAAE;MAClC,IAAI,CAACE,uBAAuB,CAACpB,aAAa,CAAC,CAAChC,OAAO,CAAC,CAAC,CAACqD,cAAc,EAAEb,IAAI,CAAC,KAAK,IAAI,CAACc,cAAc,CAACD,cAAc,EAAEJ,MAAM,EAAET,IAAI,CAAC,CAAC;IACtI,CAAC,MACI;MACD,IAAI,CAACe,UAAU,CAACN,MAAM,CAAC;MACvB;MACA,IAAI,OAAOjB,aAAa,CAACwB,KAAK,KAAK,UAAU,EAAE;QAC3CxB,aAAa,CAACwB,KAAK,CAAC1E,OAAO,CAAC;MAChC;IACJ;EACJ;EACAe,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwC,YAAY,CAACrC,OAAO,CAAC,CAACyD,KAAK,EAAElC,OAAO,KAAK,IAAI,CAACoB,cAAc,CAACpB,OAAO,CAAC,CAAC;EAC/E;EACA;EACAY,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuB,SAAS,IAAI7E,QAAQ;EACrC;EACA;EACA8E,UAAUA,CAAA,EAAG;IACT,MAAMC,GAAG,GAAG,IAAI,CAACzB,YAAY,CAAC,CAAC;IAC/B,OAAOyB,GAAG,CAACC,WAAW,IAAIC,MAAM;EACpC;EACAC,eAAeA,CAACC,gBAAgB,EAAE;IAC9B,IAAI,IAAI,CAACC,OAAO,EAAE;MACd;MACA;MACA,IAAI,IAAI,CAACC,2BAA2B,EAAE;QAClC,OAAO,IAAI,CAACC,0BAA0B,CAACH,gBAAgB,CAAC,GAAG,OAAO,GAAG,SAAS;MAClF,CAAC,MACI;QACD,OAAO,IAAI,CAACC,OAAO;MACvB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC9C,cAAc,IAAI,IAAI,CAACiD,gBAAgB,EAAE;MAC9C,OAAO,IAAI,CAACA,gBAAgB;IAChC;IACA;IACA;IACA;IACA;IACA,IAAIJ,gBAAgB,IAAI,IAAI,CAACK,gCAAgC,CAACL,gBAAgB,CAAC,EAAE;MAC7E,OAAO,OAAO;IAClB;IACA,OAAO,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,0BAA0BA,CAACH,gBAAgB,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAQ,IAAI,CAACrC,cAAc,KAAKb,yBAAyB,CAACwD,QAAQ,IAC9D,CAAC,EAACN,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEO,QAAQ,CAAC,IAAI,CAACC,sBAAsB,CAAChG,iBAAiB,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACIqE,WAAWA,CAACtB,OAAO,EAAE0B,MAAM,EAAE;IACzB1B,OAAO,CAACkD,SAAS,CAACC,MAAM,CAAC,aAAa,EAAE,CAAC,CAACzB,MAAM,CAAC;IACjD1B,OAAO,CAACkD,SAAS,CAACC,MAAM,CAAC,mBAAmB,EAAEzB,MAAM,KAAK,OAAO,CAAC;IACjE1B,OAAO,CAACkD,SAAS,CAACC,MAAM,CAAC,sBAAsB,EAAEzB,MAAM,KAAK,UAAU,CAAC;IACvE1B,OAAO,CAACkD,SAAS,CAACC,MAAM,CAAC,mBAAmB,EAAEzB,MAAM,KAAK,OAAO,CAAC;IACjE1B,OAAO,CAACkD,SAAS,CAACC,MAAM,CAAC,qBAAqB,EAAEzB,MAAM,KAAK,SAAS,CAAC;EACzE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,UAAUA,CAACN,MAAM,EAAE0B,iBAAiB,GAAG,KAAK,EAAE;IAC1C,IAAI,CAACC,OAAO,CAACnF,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACwE,OAAO,GAAGhB,MAAM;MACrB,IAAI,CAACiB,2BAA2B,GAAGjB,MAAM,KAAK,OAAO,IAAI0B,iBAAiB;MAC1E;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAChD,cAAc,KAAKb,yBAAyB,CAACe,SAAS,EAAE;QAC7DgD,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;QACnC,MAAMC,EAAE,GAAG,IAAI,CAACb,2BAA2B,GAAG1G,eAAe,GAAG,CAAC;QACjE,IAAI,CAACsH,gBAAgB,GAAGzD,UAAU,CAAC,MAAO,IAAI,CAAC4C,OAAO,GAAG,IAAK,EAAEc,EAAE,CAAC;MACvE;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACItD,QAAQA,CAACvD,KAAK,EAAEqD,OAAO,EAAE;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMqB,WAAW,GAAG,IAAI,CAACP,YAAY,CAACC,GAAG,CAACf,OAAO,CAAC;IAClD,MAAMyC,gBAAgB,GAAGnH,eAAe,CAACqB,KAAK,CAAC;IAC/C,IAAI,CAAC0E,WAAW,IAAK,CAACA,WAAW,CAACb,aAAa,IAAIR,OAAO,KAAKyC,gBAAiB,EAAE;MAC9E;IACJ;IACA,IAAI,CAACV,cAAc,CAAC/B,OAAO,EAAE,IAAI,CAACwC,eAAe,CAACC,gBAAgB,CAAC,EAAEpB,WAAW,CAAC;EACrF;EACA;AACJ;AACA;AACA;AACA;EACIlB,OAAOA,CAACxD,KAAK,EAAEqD,OAAO,EAAE;IACpB;IACA;IACA,MAAMqB,WAAW,GAAG,IAAI,CAACP,YAAY,CAACC,GAAG,CAACf,OAAO,CAAC;IAClD,IAAI,CAACqB,WAAW,IACXA,WAAW,CAACb,aAAa,IACtB7D,KAAK,CAAC8G,aAAa,YAAYC,IAAI,IACnC1D,OAAO,CAACgD,QAAQ,CAACrG,KAAK,CAAC8G,aAAa,CAAE,EAAE;MAC5C;IACJ;IACA,IAAI,CAACnC,WAAW,CAACtB,OAAO,CAAC;IACzB,IAAI,CAAC2D,WAAW,CAACtC,WAAW,EAAE,IAAI,CAAC;EACvC;EACAsC,WAAWA,CAAC1C,IAAI,EAAES,MAAM,EAAE;IACtB,IAAIT,IAAI,CAACD,OAAO,CAAC4C,SAAS,CAACC,MAAM,EAAE;MAC/B,IAAI,CAACR,OAAO,CAACS,GAAG,CAAC,MAAM7C,IAAI,CAACD,OAAO,CAAChE,IAAI,CAAC0E,MAAM,CAAC,CAAC;IACrD;EACJ;EACAP,wBAAwBA,CAACE,WAAW,EAAE;IAClC,IAAI,CAAC,IAAI,CAACxD,SAAS,CAACC,SAAS,EAAE;MAC3B;IACJ;IACA,MAAM6C,QAAQ,GAAGU,WAAW,CAACV,QAAQ;IACrC,MAAMoD,sBAAsB,GAAG,IAAI,CAACC,2BAA2B,CAACjD,GAAG,CAACJ,QAAQ,CAAC,IAAI,CAAC;IAClF,IAAI,CAACoD,sBAAsB,EAAE;MACzB,IAAI,CAACV,OAAO,CAACnF,iBAAiB,CAAC,MAAM;QACjCyC,QAAQ,CAACsD,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,6BAA6B,EAAEzE,2BAA2B,CAAC;QACnGkB,QAAQ,CAACsD,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACC,6BAA6B,EAAEzE,2BAA2B,CAAC;MACtG,CAAC,CAAC;IACN;IACA,IAAI,CAACuE,2BAA2B,CAAC9C,GAAG,CAACP,QAAQ,EAAEoD,sBAAsB,GAAG,CAAC,CAAC;IAC1E;IACA,IAAI,EAAE,IAAI,CAACI,sBAAsB,KAAK,CAAC,EAAE;MACrC;MACA;MACA,IAAI,CAACd,OAAO,CAACnF,iBAAiB,CAAC,MAAM;QACjC,MAAMqE,MAAM,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;QAChCG,MAAM,CAAC0B,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACG,oBAAoB,CAAC;MAC/D,CAAC,CAAC;MACF;MACA,IAAI,CAACnB,sBAAsB,CAACvF,gBAAgB,CACvCC,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACgK,0BAA0B,CAAC,CAAC,CAChDC,SAAS,CAACC,QAAQ,IAAI;QACvB,IAAI,CAACvC,UAAU,CAACuC,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC;MAC3D,CAAC,CAAC;IACN;EACJ;EACA/C,sBAAsBA,CAACH,WAAW,EAAE;IAChC,MAAMV,QAAQ,GAAGU,WAAW,CAACV,QAAQ;IACrC,IAAI,IAAI,CAACqD,2BAA2B,CAACQ,GAAG,CAAC7D,QAAQ,CAAC,EAAE;MAChD,MAAMoD,sBAAsB,GAAG,IAAI,CAACC,2BAA2B,CAACjD,GAAG,CAACJ,QAAQ,CAAC;MAC7E,IAAIoD,sBAAsB,GAAG,CAAC,EAAE;QAC5B,IAAI,CAACC,2BAA2B,CAAC9C,GAAG,CAACP,QAAQ,EAAEoD,sBAAsB,GAAG,CAAC,CAAC;MAC9E,CAAC,MACI;QACDpD,QAAQ,CAAC8D,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACP,6BAA6B,EAAEzE,2BAA2B,CAAC;QACtGkB,QAAQ,CAAC8D,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACP,6BAA6B,EAAEzE,2BAA2B,CAAC;QACrG,IAAI,CAACuE,2BAA2B,CAACzC,MAAM,CAACZ,QAAQ,CAAC;MACrD;IACJ;IACA;IACA,IAAI,CAAC,GAAE,IAAI,CAACwD,sBAAsB,EAAE;MAChC,MAAM5B,MAAM,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;MAChCG,MAAM,CAACkC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACL,oBAAoB,CAAC;MAC9D;MACA,IAAI,CAACC,0BAA0B,CAACrH,IAAI,CAAC,CAAC;MACtC;MACAsG,YAAY,CAAC,IAAI,CAACzD,qBAAqB,CAAC;MACxCyD,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;IACvC;EACJ;EACA;EACAxB,cAAcA,CAAC/B,OAAO,EAAE0B,MAAM,EAAEL,WAAW,EAAE;IACzC,IAAI,CAACC,WAAW,CAACtB,OAAO,EAAE0B,MAAM,CAAC;IACjC,IAAI,CAACiC,WAAW,CAACtC,WAAW,EAAEK,MAAM,CAAC;IACrC,IAAI,CAACmB,gBAAgB,GAAGnB,MAAM;EAClC;EACA;AACJ;AACA;AACA;AACA;EACIG,uBAAuBA,CAAC7B,OAAO,EAAE;IAC7B,MAAM0E,OAAO,GAAG,EAAE;IAClB,IAAI,CAAC5D,YAAY,CAACrC,OAAO,CAAC,CAACwC,IAAI,EAAEa,cAAc,KAAK;MAChD,IAAIA,cAAc,KAAK9B,OAAO,IAAKiB,IAAI,CAACT,aAAa,IAAIsB,cAAc,CAACkB,QAAQ,CAAChD,OAAO,CAAE,EAAE;QACxF0E,OAAO,CAACC,IAAI,CAAC,CAAC7C,cAAc,EAAEb,IAAI,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,OAAOyD,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACI5B,gCAAgCA,CAACL,gBAAgB,EAAE;IAC/C,MAAM;MAAExF,iBAAiB,EAAE2H,gBAAgB;MAAEtI;IAAmB,CAAC,GAAG,IAAI,CAAC2G,sBAAsB;IAC/F;IACA;IACA;IACA,IAAI3G,kBAAkB,KAAK,OAAO,IAC9B,CAACsI,gBAAgB,IACjBA,gBAAgB,KAAKnC,gBAAgB,IACpCA,gBAAgB,CAACoC,QAAQ,KAAK,OAAO,IAAIpC,gBAAgB,CAACoC,QAAQ,KAAK,UAAW,IACnFpC,gBAAgB,CAACqC,QAAQ,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAMC,MAAM,GAAGtC,gBAAgB,CAACsC,MAAM;IACtC,IAAIA,MAAM,EAAE;MACR,KAAK,IAAIxK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwK,MAAM,CAAClB,MAAM,EAAEtJ,CAAC,EAAE,EAAE;QACpC,IAAIwK,MAAM,CAACxK,CAAC,CAAC,CAACyI,QAAQ,CAAC4B,gBAAgB,CAAC,EAAE;UACtC,OAAO,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK;EAChB;AAGJ;AAACI,aAAA,GAvXKtF,YAAY;AAAAhD,eAAA,CAAZgD,YAAY,wBAAAuF,sBAAApG,iBAAA;EAAA,YAAAA,iBAAA,IAqXqFa,aAAY;AAAA;AAAAhD,eAAA,CArX7GgD,YAAY,+BA/B+DpG,EAAE,CAAAwF,kBAAA;EAAAC,KAAA,EAqZwBW,aAAY;EAAAV,OAAA,EAAZU,aAAY,CAAAT,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE3I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvZiF7F,EAAE,CAAA8F,iBAAA,CAuZQM,YAAY,EAAc,CAAC;IAC1GL,IAAI,EAAE1F,UAAU;IAChB2F,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgG,eAAe,CAAC;EAMlBzI,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBALAlD,MAAM,CAACI,UAAU,CAAC;IAAA8C,eAAA,wBAChBlD,MAAM,CAACkG,YAAY,CAAC;IAAAhD,eAAA;IAAAA,eAAA,uBAErB,IAAI;IAAAA,eAAA,yBACF,IAAI7C,YAAY,CAAC,CAAC;EACnB;EAChB,IAAIsL,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACAC,eAAeA,CAAA,EAAG;IACd,MAAMrF,OAAO,GAAG,IAAI,CAACsF,WAAW,CAAC7E,aAAa;IAC9C,IAAI,CAAC8E,oBAAoB,GAAG,IAAI,CAACC,aAAa,CACzCjF,OAAO,CAACP,OAAO,EAAEA,OAAO,CAACU,QAAQ,KAAK,CAAC,IAAIV,OAAO,CAACyF,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAC1FnB,SAAS,CAAC5C,MAAM,IAAI;MACrB,IAAI,CAAC0D,YAAY,GAAG1D,MAAM;MAC1B,IAAI,CAACgE,cAAc,CAACC,IAAI,CAACjE,MAAM,CAAC;IACpC,CAAC,CAAC;EACN;EACApD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkH,aAAa,CAACpE,cAAc,CAAC,IAAI,CAACkE,WAAW,CAAC;IACnD,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAACK,WAAW,CAAC,CAAC;IAC3C;EACJ;AAGJ;AAACC,gBAAA,GA3BKX,eAAe;AAAAxI,eAAA,CAAfwI,eAAe,wBAAAY,yBAAAjH,iBAAA;EAAA,YAAAA,iBAAA,IAyBkFqG,gBAAe;AAAA;AAAAxI,eAAA,CAzBhHwI,eAAe,8BApa4D5L,EAAE,CAAAyM,iBAAA;EAAA1G,IAAA,EA8bQ6F,gBAAe;EAAAc,SAAA;EAAAC,OAAA;IAAAP,cAAA;EAAA;EAAAQ,QAAA;AAAA;AAE1G;EAAA,QAAA/G,SAAA,oBAAAA,SAAA,KAhciF7F,EAAE,CAAA8F,iBAAA,CAgcQ8F,eAAe,EAAc,CAAC;IAC7G7F,IAAI,EAAEvF,SAAS;IACfwF,IAAI,EAAE,CAAC;MACC6G,QAAQ,EAAE,oDAAoD;MAC9DD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAER,cAAc,EAAE,CAAC;MACzDrG,IAAI,EAAEtF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,SAASmL,eAAe,IAAIrK,CAAC,EAAE6E,YAAY,IAAI0G,CAAC,EAAE/J,qBAAqB,IAAIgK,CAAC,EAAEtK,uCAAuC,IAAItB,CAAC,EAAEqB,+BAA+B,IAAIwK,CAAC,EAAE/G,yBAAyB,IAAIgH,CAAC,EAAE/G,6BAA6B,IAAI7E,CAAC;AACpO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}