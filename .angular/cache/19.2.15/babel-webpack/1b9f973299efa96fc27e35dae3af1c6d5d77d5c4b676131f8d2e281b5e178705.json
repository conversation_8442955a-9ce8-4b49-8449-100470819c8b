{"ast": null, "code": "export function isFunction(value) {\n  return typeof value === 'function';\n}\n//# sourceMappingURL=isFunction.js.map", "map": {"version": 3, "names": ["isFunction", "value"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/isFunction.js"], "sourcesContent": ["export function isFunction(value) {\n    return typeof value === 'function';\n}\n//# sourceMappingURL=isFunction.js.map"], "mappings": "AAAA,OAAO,SAASA,UAAUA,CAACC,KAAK,EAAE;EAC9B,OAAO,OAAOA,KAAK,KAAK,UAAU;AACtC;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}