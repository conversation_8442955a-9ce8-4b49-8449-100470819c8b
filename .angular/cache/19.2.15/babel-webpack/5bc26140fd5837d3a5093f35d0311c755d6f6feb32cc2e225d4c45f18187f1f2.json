{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _asyncToGenerator from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _HttpClient, _FetchBackend, _HttpInterceptorHandler, _JsonpClientBackend, _JsonpInterceptor, _HttpXhrBackend, _HttpXsrfCookieExtractor, _HttpXsrfInterceptor, _HttpClientXsrfModule, _HttpClientModule, _HttpClientJsonpModule;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, Injectable, inject, NgZone, DestroyRef, InjectionToken, ɵPendingTasksInternal as _PendingTasksInternal, PLATFORM_ID, ɵConsole as _Console, ɵformatRuntimeError as _formatRuntimeError, runInInjectionContext, Inject, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { concatMap, filter, map, finalize, switchMap } from 'rxjs/operators';\nimport { of, Observable, from } from 'rxjs';\nimport { isPlatformServer, XhrFactory, parseCookieValue } from './xhr-BfNfxNDv.mjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n  /**  Constructs a new HTTP header object with the given values.*/\n  constructor(headers) {\n    /**\n     * Internal map of lowercase header names to values.\n     */\n    _defineProperty(this, \"headers\", void 0);\n    /**\n     * Internal map of lowercased header names to the normalized\n     * form of the name (the form seen first).\n     */\n    _defineProperty(this, \"normalizedNames\", new Map());\n    /**\n     * Complete the lazy initialization of this object (needed before reading).\n     */\n    _defineProperty(this, \"lazyInit\", void 0);\n    /**\n     * Queued updates to be materialized the next initialization.\n     */\n    _defineProperty(this, \"lazyUpdate\", null);\n    if (!headers) {\n      this.headers = new Map();\n    } else if (typeof headers === 'string') {\n      this.lazyInit = () => {\n        this.headers = new Map();\n        headers.split('\\n').forEach(line => {\n          const index = line.indexOf(':');\n          if (index > 0) {\n            const name = line.slice(0, index);\n            const value = line.slice(index + 1).trim();\n            this.addHeaderEntry(name, value);\n          }\n        });\n      };\n    } else if (typeof Headers !== 'undefined' && headers instanceof Headers) {\n      this.headers = new Map();\n      headers.forEach((value, name) => {\n        this.addHeaderEntry(name, value);\n      });\n    } else {\n      this.lazyInit = () => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          assertValidHeaders(headers);\n        }\n        this.headers = new Map();\n        Object.entries(headers).forEach(([name, values]) => {\n          this.setHeaderEntries(name, values);\n        });\n      };\n    }\n  }\n  /**\n   * Checks for existence of a given header.\n   *\n   * @param name The header name to check for existence.\n   *\n   * @returns True if the header exists, false otherwise.\n   */\n  has(name) {\n    this.init();\n    return this.headers.has(name.toLowerCase());\n  }\n  /**\n   * Retrieves the first value of a given header.\n   *\n   * @param name The header name.\n   *\n   * @returns The value string if the header exists, null otherwise\n   */\n  get(name) {\n    this.init();\n    const values = this.headers.get(name.toLowerCase());\n    return values && values.length > 0 ? values[0] : null;\n  }\n  /**\n   * Retrieves the names of the headers.\n   *\n   * @returns A list of header names.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.normalizedNames.values());\n  }\n  /**\n   * Retrieves a list of values for a given header.\n   *\n   * @param name The header name from which to retrieve values.\n   *\n   * @returns A string of values if the header exists, null otherwise.\n   */\n  getAll(name) {\n    this.init();\n    return this.headers.get(name.toLowerCase()) || null;\n  }\n  /**\n   * Appends a new value to the existing set of values for a header\n   * and returns them in a clone of the original instance.\n   *\n   * @param name The header name for which to append the values.\n   * @param value The value to append.\n   *\n   * @returns A clone of the HTTP headers object with the value appended to the given header.\n   */\n  append(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Sets or modifies a value for a given header in a clone of the original instance.\n   * If the header already exists, its value is replaced with the given value\n   * in the returned object.\n   *\n   * @param name The header name.\n   * @param value The value or values to set or override for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the newly set header value.\n   */\n  set(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Deletes values for a given header in a clone of the original instance.\n   *\n   * @param name The header name.\n   * @param value The value or values to delete for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the given value deleted.\n   */\n  delete(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'd'\n    });\n  }\n  maybeSetNormalizedName(name, lcName) {\n    if (!this.normalizedNames.has(lcName)) {\n      this.normalizedNames.set(lcName, name);\n    }\n  }\n  init() {\n    if (!!this.lazyInit) {\n      if (this.lazyInit instanceof HttpHeaders) {\n        this.copyFrom(this.lazyInit);\n      } else {\n        this.lazyInit();\n      }\n      this.lazyInit = null;\n      if (!!this.lazyUpdate) {\n        this.lazyUpdate.forEach(update => this.applyUpdate(update));\n        this.lazyUpdate = null;\n      }\n    }\n  }\n  copyFrom(other) {\n    other.init();\n    Array.from(other.headers.keys()).forEach(key => {\n      this.headers.set(key, other.headers.get(key));\n      this.normalizedNames.set(key, other.normalizedNames.get(key));\n    });\n  }\n  clone(update) {\n    const clone = new HttpHeaders();\n    clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n    clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n    return clone;\n  }\n  applyUpdate(update) {\n    const key = update.name.toLowerCase();\n    switch (update.op) {\n      case 'a':\n      case 's':\n        let value = update.value;\n        if (typeof value === 'string') {\n          value = [value];\n        }\n        if (value.length === 0) {\n          return;\n        }\n        this.maybeSetNormalizedName(update.name, key);\n        const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n        base.push(...value);\n        this.headers.set(key, base);\n        break;\n      case 'd':\n        const toDelete = update.value;\n        if (!toDelete) {\n          this.headers.delete(key);\n          this.normalizedNames.delete(key);\n        } else {\n          let existing = this.headers.get(key);\n          if (!existing) {\n            return;\n          }\n          existing = existing.filter(value => toDelete.indexOf(value) === -1);\n          if (existing.length === 0) {\n            this.headers.delete(key);\n            this.normalizedNames.delete(key);\n          } else {\n            this.headers.set(key, existing);\n          }\n        }\n        break;\n    }\n  }\n  addHeaderEntry(name, value) {\n    const key = name.toLowerCase();\n    this.maybeSetNormalizedName(name, key);\n    if (this.headers.has(key)) {\n      this.headers.get(key).push(value);\n    } else {\n      this.headers.set(key, [value]);\n    }\n  }\n  setHeaderEntries(name, values) {\n    const headerValues = (Array.isArray(values) ? values : [values]).map(value => value.toString());\n    const key = name.toLowerCase();\n    this.headers.set(key, headerValues);\n    this.maybeSetNormalizedName(name, key);\n  }\n  /**\n   * @internal\n   */\n  forEach(fn) {\n    this.init();\n    Array.from(this.normalizedNames.keys()).forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n  }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n  for (const [key, value] of Object.entries(headers)) {\n    if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n      throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` + `Expecting either a string, a number or an array, but got: \\`${value}\\`.`);\n    }\n  }\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n  /**\n   * Encodes a key name for a URL parameter or query-string.\n   * @param key The key name.\n   * @returns The encoded key name.\n   */\n  encodeKey(key) {\n    return standardEncoding(key);\n  }\n  /**\n   * Encodes the value of a URL parameter or query-string.\n   * @param value The value.\n   * @returns The encoded value.\n   */\n  encodeValue(value) {\n    return standardEncoding(value);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string key.\n   * @param key The encoded key name.\n   * @returns The decoded key name.\n   */\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string value.\n   * @param value The encoded value.\n   * @returns The decoded value.\n   */\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n}\nfunction paramParser(rawParams, codec) {\n  const map = new Map();\n  if (rawParams.length > 0) {\n    // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n    // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n    // may start with the `?` char, so we strip it if it's present.\n    const params = rawParams.replace(/^\\?/, '').split('&');\n    params.forEach(param => {\n      const eqIdx = param.indexOf('=');\n      const [key, val] = eqIdx == -1 ? [codec.decodeKey(param), ''] : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n      const list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n  return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n  '40': '@',\n  '3A': ':',\n  '24': '$',\n  '2C': ',',\n  '3B': ';',\n  '3D': '=',\n  '3F': '?',\n  '2F': '/'\n};\nfunction standardEncoding(v) {\n  return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => {\n    var _STANDARD_ENCODING_RE;\n    return (_STANDARD_ENCODING_RE = STANDARD_ENCODING_REPLACEMENTS[t]) !== null && _STANDARD_ENCODING_RE !== void 0 ? _STANDARD_ENCODING_RE : s;\n  });\n}\nfunction valueToString(value) {\n  return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n  constructor(options = {}) {\n    _defineProperty(this, \"map\", void 0);\n    _defineProperty(this, \"encoder\", void 0);\n    _defineProperty(this, \"updates\", null);\n    _defineProperty(this, \"cloneFrom\", null);\n    this.encoder = options.encoder || new HttpUrlEncodingCodec();\n    if (options.fromString) {\n      if (options.fromObject) {\n        throw new _RuntimeError(2805 /* RuntimeErrorCode.CANNOT_SPECIFY_BOTH_FROM_STRING_AND_FROM_OBJECT */, ngDevMode && 'Cannot specify both fromString and fromObject.');\n      }\n      this.map = paramParser(options.fromString, this.encoder);\n    } else if (!!options.fromObject) {\n      this.map = new Map();\n      Object.keys(options.fromObject).forEach(key => {\n        const value = options.fromObject[key];\n        // convert the values to strings\n        const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n        this.map.set(key, values);\n      });\n    } else {\n      this.map = null;\n    }\n  }\n  /**\n   * Reports whether the body includes one or more values for a given parameter.\n   * @param param The parameter name.\n   * @returns True if the parameter has one or more values,\n   * false if it has no value or is not present.\n   */\n  has(param) {\n    this.init();\n    return this.map.has(param);\n  }\n  /**\n   * Retrieves the first value for a parameter.\n   * @param param The parameter name.\n   * @returns The first value of the given parameter,\n   * or `null` if the parameter is not present.\n   */\n  get(param) {\n    this.init();\n    const res = this.map.get(param);\n    return !!res ? res[0] : null;\n  }\n  /**\n   * Retrieves all values for a  parameter.\n   * @param param The parameter name.\n   * @returns All values in a string array,\n   * or `null` if the parameter not present.\n   */\n  getAll(param) {\n    this.init();\n    return this.map.get(param) || null;\n  }\n  /**\n   * Retrieves all the parameters for this body.\n   * @returns The parameter names in a string array.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.map.keys());\n  }\n  /**\n   * Appends a new value to existing values for a parameter.\n   * @param param The parameter name.\n   * @param value The new value to add.\n   * @return A new body with the appended value.\n   */\n  append(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Constructs a new body with appended values for the given parameter name.\n   * @param params parameters and values\n   * @return A new body with the new value.\n   */\n  appendAll(params) {\n    const updates = [];\n    Object.keys(params).forEach(param => {\n      const value = params[param];\n      if (Array.isArray(value)) {\n        value.forEach(_value => {\n          updates.push({\n            param,\n            value: _value,\n            op: 'a'\n          });\n        });\n      } else {\n        updates.push({\n          param,\n          value: value,\n          op: 'a'\n        });\n      }\n    });\n    return this.clone(updates);\n  }\n  /**\n   * Replaces the value for a parameter.\n   * @param param The parameter name.\n   * @param value The new value.\n   * @return A new body with the new value.\n   */\n  set(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Removes a given value or all values from a parameter.\n   * @param param The parameter name.\n   * @param value The value to remove, if provided.\n   * @return A new body with the given value removed, or with all values\n   * removed if no value is specified.\n   */\n  delete(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'd'\n    });\n  }\n  /**\n   * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n   * separated by `&`s.\n   */\n  toString() {\n    this.init();\n    return this.keys().map(key => {\n      const eKey = this.encoder.encodeKey(key);\n      // `a: ['1']` produces `'a=1'`\n      // `b: []` produces `''`\n      // `c: ['1', '2']` produces `'c=1&c=2'`\n      return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value)).join('&');\n    })\n    // filter out empty values because `b: []` produces `''`\n    // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n    .filter(param => param !== '').join('&');\n  }\n  clone(update) {\n    const clone = new HttpParams({\n      encoder: this.encoder\n    });\n    clone.cloneFrom = this.cloneFrom || this;\n    clone.updates = (this.updates || []).concat(update);\n    return clone;\n  }\n  init() {\n    if (this.map === null) {\n      this.map = new Map();\n    }\n    if (this.cloneFrom !== null) {\n      this.cloneFrom.init();\n      this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n      this.updates.forEach(update => {\n        switch (update.op) {\n          case 'a':\n          case 's':\n            const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n            base.push(valueToString(update.value));\n            this.map.set(update.param, base);\n            break;\n          case 'd':\n            if (update.value !== undefined) {\n              let base = this.map.get(update.param) || [];\n              const idx = base.indexOf(valueToString(update.value));\n              if (idx !== -1) {\n                base.splice(idx, 1);\n              }\n              if (base.length > 0) {\n                this.map.set(update.param, base);\n              } else {\n                this.map.delete(update.param);\n              }\n            } else {\n              this.map.delete(update.param);\n              break;\n            }\n        }\n      });\n      this.cloneFrom = this.updates = null;\n    }\n  }\n}\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n  constructor(defaultValue) {\n    _defineProperty(this, \"defaultValue\", void 0);\n    this.defaultValue = defaultValue;\n  }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```ts\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n  constructor() {\n    _defineProperty(this, \"map\", new Map());\n  }\n  /**\n   * Store a value in the context. If a value is already present it will be overwritten.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   * @param value The value to store.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  set(token, value) {\n    this.map.set(token, value);\n    return this;\n  }\n  /**\n   * Retrieve the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns The stored value or default if one is defined.\n   */\n  get(token) {\n    if (!this.map.has(token)) {\n      this.map.set(token, token.defaultValue());\n    }\n    return this.map.get(token);\n  }\n  /**\n   * Delete the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  delete(token) {\n    this.map.delete(token);\n    return this;\n  }\n  /**\n   * Checks for existence of a given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns True if the token exists, false otherwise.\n   */\n  has(token) {\n    return this.map.has(token);\n  }\n  /**\n   * @returns a list of tokens currently stored in the context.\n   */\n  keys() {\n    return this.map.keys();\n  }\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n  switch (method) {\n    case 'DELETE':\n    case 'GET':\n    case 'HEAD':\n    case 'OPTIONS':\n    case 'JSONP':\n      return false;\n    default:\n      return true;\n  }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n  return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n  return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n  return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n  return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * `Content-Type` is an HTTP header used to indicate the media type\n * (also known as MIME type) of the resource being sent to the client\n * or received from the server.\n */\nconst CONTENT_TYPE_HEADER = 'Content-Type';\n/**\n * The `Accept` header is an HTTP request header that indicates the media types\n * (or content types) the client is willing to receive from the server.\n */\nconst ACCEPT_HEADER = 'Accept';\n/**\n * `X-Request-URL` is a custom HTTP header used in older browser versions,\n * including Firefox (< 32), Chrome (< 37), Safari (< 8), and Internet Explorer,\n * to include the full URL of the request in cross-origin requests.\n */\nconst X_REQUEST_URL_HEADER = 'X-Request-URL';\n/**\n * `text/plain` is a content type used to indicate that the content being\n * sent is plain text with no special formatting or structured data\n * like HTML, XML, or JSON.\n */\nconst TEXT_CONTENT_TYPE = 'text/plain';\n/**\n * `application/json` is a content type used to indicate that the content\n * being sent is in the JSON format.\n */\nconst JSON_CONTENT_TYPE = 'application/json';\n/**\n * `application/json, text/plain, *\\/*` is a content negotiation string often seen in the\n * Accept header of HTTP requests. It indicates the types of content the client is willing\n * to accept from the server, with a preference for `application/json` and `text/plain`,\n * but also accepting any other type (*\\/*).\n */\nconst ACCEPT_HEADER_VALUE = `${JSON_CONTENT_TYPE}, ${TEXT_CONTENT_TYPE}, */*`;\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n  constructor(method, url, third, fourth) {\n    var _this$headers, _this$context;\n    _defineProperty(this, \"url\", void 0);\n    /**\n     * The request body, or `null` if one isn't set.\n     *\n     * Bodies are not enforced to be immutable, as they can include a reference to any\n     * user-defined data type. However, interceptors should take care to preserve\n     * idempotence by treating them as such.\n     */\n    _defineProperty(this, \"body\", null);\n    /**\n     * Outgoing headers for this request.\n     */\n    _defineProperty(this, \"headers\", void 0);\n    /**\n     * Shared and mutable context that can be used by interceptors\n     */\n    _defineProperty(this, \"context\", void 0);\n    /**\n     * Whether this request should be made in a way that exposes progress events.\n     *\n     * Progress events are expensive (change detection runs on each event) and so\n     * they should only be requested if the consumer intends to monitor them.\n     *\n     * Note: The `FetchBackend` doesn't support progress report on uploads.\n     */\n    _defineProperty(this, \"reportProgress\", false);\n    /**\n     * Whether this request should be sent with outgoing credentials (cookies).\n     */\n    _defineProperty(this, \"withCredentials\", false);\n    /**\n     * The expected response type of the server.\n     *\n     * This is used to parse the response appropriately before returning it to\n     * the requestee.\n     */\n    _defineProperty(this, \"responseType\", 'json');\n    /**\n     * The outgoing HTTP request method.\n     */\n    _defineProperty(this, \"method\", void 0);\n    /**\n     * Outgoing URL parameters.\n     *\n     * To pass a string representation of HTTP parameters in the URL-query-string format,\n     * the `HttpParamsOptions`' `fromString` may be used. For example:\n     *\n     * ```ts\n     * new HttpParams({fromString: 'angular=awesome'})\n     * ```\n     */\n    _defineProperty(this, \"params\", void 0);\n    /**\n     * The outgoing URL with all URL parameters set.\n     */\n    _defineProperty(this, \"urlWithParams\", void 0);\n    /**\n     * The HttpTransferCache option for the request\n     */\n    _defineProperty(this, \"transferCache\", void 0);\n    this.url = url;\n    this.method = method.toUpperCase();\n    // Next, need to figure out which argument holds the HttpRequestInit\n    // options, if any.\n    let options;\n    // Check whether a body argument is expected. The only valid way to omit\n    // the body argument is to use a known no-body method like GET.\n    if (mightHaveBody(this.method) || !!fourth) {\n      // Body is the third argument, options are the fourth.\n      this.body = third !== undefined ? third : null;\n      options = fourth;\n    } else {\n      // No body required, options are the third argument. The body stays null.\n      options = third;\n    }\n    // If options have been passed, interpret them.\n    if (options) {\n      // Normalize reportProgress and withCredentials.\n      this.reportProgress = !!options.reportProgress;\n      this.withCredentials = !!options.withCredentials;\n      // Override default response type of 'json' if one is provided.\n      if (!!options.responseType) {\n        this.responseType = options.responseType;\n      }\n      // Override headers if they're provided.\n      if (!!options.headers) {\n        this.headers = options.headers;\n      }\n      if (!!options.context) {\n        this.context = options.context;\n      }\n      if (!!options.params) {\n        this.params = options.params;\n      }\n      // We do want to assign transferCache even if it's falsy (false is valid value)\n      this.transferCache = options.transferCache;\n    }\n    // If no headers have been passed in, construct a new HttpHeaders instance.\n    (_this$headers = this.headers) !== null && _this$headers !== void 0 ? _this$headers : this.headers = new HttpHeaders();\n    // If no context have been passed in, construct a new HttpContext instance.\n    (_this$context = this.context) !== null && _this$context !== void 0 ? _this$context : this.context = new HttpContext();\n    // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n    if (!this.params) {\n      this.params = new HttpParams();\n      this.urlWithParams = url;\n    } else {\n      // Encode the parameters to a string in preparation for inclusion in the URL.\n      const params = this.params.toString();\n      if (params.length === 0) {\n        // No parameters, the visible URL is just the URL given at creation time.\n        this.urlWithParams = url;\n      } else {\n        // Does the URL already have query parameters? Look for '?'.\n        const qIdx = url.indexOf('?');\n        // There are 3 cases to handle:\n        // 1) No existing parameters -> append '?' followed by params.\n        // 2) '?' exists and is followed by existing query string ->\n        //    append '&' followed by params.\n        // 3) '?' exists at the end of the url -> append params directly.\n        // This basically amounts to determining the character, if any, with\n        // which to join the URL and parameters.\n        const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n        this.urlWithParams = url + sep + params;\n      }\n    }\n  }\n  /**\n   * Transform the free-form body into a serialized format suitable for\n   * transmission to the server.\n   */\n  serializeBody() {\n    // If no body is present, no need to serialize it.\n    if (this.body === null) {\n      return null;\n    }\n    // Check whether the body is already in a serialized form. If so,\n    // it can just be returned directly.\n    if (typeof this.body === 'string' || isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) || isUrlSearchParams(this.body)) {\n      return this.body;\n    }\n    // Check whether the body is an instance of HttpUrlEncodedParams.\n    if (this.body instanceof HttpParams) {\n      return this.body.toString();\n    }\n    // Check whether the body is an object or array, and serialize with JSON if so.\n    if (typeof this.body === 'object' || typeof this.body === 'boolean' || Array.isArray(this.body)) {\n      return JSON.stringify(this.body);\n    }\n    // Fall back on toString() for everything else.\n    return this.body.toString();\n  }\n  /**\n   * Examine the body and attempt to infer an appropriate MIME type\n   * for it.\n   *\n   * If no such type can be inferred, this method will return `null`.\n   */\n  detectContentTypeHeader() {\n    // An empty body has no content type.\n    if (this.body === null) {\n      return null;\n    }\n    // FormData bodies rely on the browser's content type assignment.\n    if (isFormData(this.body)) {\n      return null;\n    }\n    // Blobs usually have their own content type. If it doesn't, then\n    // no type can be inferred.\n    if (isBlob(this.body)) {\n      return this.body.type || null;\n    }\n    // Array buffers have unknown contents and thus no type can be inferred.\n    if (isArrayBuffer(this.body)) {\n      return null;\n    }\n    // Technically, strings could be a form of JSON data, but it's safe enough\n    // to assume they're plain strings.\n    if (typeof this.body === 'string') {\n      return TEXT_CONTENT_TYPE;\n    }\n    // `HttpUrlEncodedParams` has its own content-type.\n    if (this.body instanceof HttpParams) {\n      return 'application/x-www-form-urlencoded;charset=UTF-8';\n    }\n    // Arrays, objects, boolean and numbers will be encoded as JSON.\n    if (typeof this.body === 'object' || typeof this.body === 'number' || typeof this.body === 'boolean') {\n      return JSON_CONTENT_TYPE;\n    }\n    // No type could be inferred.\n    return null;\n  }\n  clone(update = {}) {\n    var _update$transferCache, _update$withCredentia, _update$reportProgres, _update$context;\n    // For method, url, and responseType, take the current value unless\n    // it is overridden in the update hash.\n    const method = update.method || this.method;\n    const url = update.url || this.url;\n    const responseType = update.responseType || this.responseType;\n    // Carefully handle the transferCache to differentiate between\n    // `false` and `undefined` in the update args.\n    const transferCache = (_update$transferCache = update.transferCache) !== null && _update$transferCache !== void 0 ? _update$transferCache : this.transferCache;\n    // The body is somewhat special - a `null` value in update.body means\n    // whatever current body is present is being overridden with an empty\n    // body, whereas an `undefined` value in update.body implies no\n    // override.\n    const body = update.body !== undefined ? update.body : this.body;\n    // Carefully handle the boolean options to differentiate between\n    // `false` and `undefined` in the update args.\n    const withCredentials = (_update$withCredentia = update.withCredentials) !== null && _update$withCredentia !== void 0 ? _update$withCredentia : this.withCredentials;\n    const reportProgress = (_update$reportProgres = update.reportProgress) !== null && _update$reportProgres !== void 0 ? _update$reportProgres : this.reportProgress;\n    // Headers and params may be appended to if `setHeaders` or\n    // `setParams` are used.\n    let headers = update.headers || this.headers;\n    let params = update.params || this.params;\n    // Pass on context if needed\n    const context = (_update$context = update.context) !== null && _update$context !== void 0 ? _update$context : this.context;\n    // Check whether the caller has asked to add headers.\n    if (update.setHeaders !== undefined) {\n      // Set every requested header.\n      headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n    }\n    // Check whether the caller has asked to set params.\n    if (update.setParams) {\n      // Set every requested param.\n      params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n    }\n    // Finally, construct the new HttpRequest using the pieces from above.\n    return new HttpRequest(method, url, body, {\n      params,\n      headers,\n      context,\n      reportProgress,\n      responseType,\n      withCredentials,\n      transferCache\n    });\n  }\n}\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n  /**\n   * The request was sent out over the wire.\n   */\n  HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n  /**\n   * An upload progress event was received.\n   *\n   * Note: The `FetchBackend` doesn't support progress report on uploads.\n   */\n  HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n  /**\n   * The response status code and headers were received.\n   */\n  HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n  /**\n   * A download progress event was received.\n   */\n  HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n  /**\n   * The full response including the body was received.\n   */\n  HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n  /**\n   * A custom event from an interceptor or a backend.\n   */\n  HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n  /**\n   * Super-constructor for all responses.\n   *\n   * The single parameter accepted is an initialization hash. Any properties\n   * of the response passed there will override the default values.\n   */\n  constructor(init, defaultStatus = 200, defaultStatusText = 'OK') {\n    /**\n     * All response headers.\n     */\n    _defineProperty(this, \"headers\", void 0);\n    /**\n     * Response status code.\n     */\n    _defineProperty(this, \"status\", void 0);\n    /**\n     * Textual description of response status code, defaults to OK.\n     *\n     * Do not depend on this.\n     */\n    _defineProperty(this, \"statusText\", void 0);\n    /**\n     * URL of the resource retrieved, or null if not available.\n     */\n    _defineProperty(this, \"url\", void 0);\n    /**\n     * Whether the status code falls in the 2xx range.\n     */\n    _defineProperty(this, \"ok\", void 0);\n    /**\n     * Type of the response, narrowed to either the full response or the header.\n     */\n    _defineProperty(this, \"type\", void 0);\n    // If the hash has values passed, use them to initialize the response.\n    // Otherwise use the default values.\n    this.headers = init.headers || new HttpHeaders();\n    this.status = init.status !== undefined ? init.status : defaultStatus;\n    this.statusText = init.statusText || defaultStatusText;\n    this.url = init.url || null;\n    // Cache the ok value to avoid defining a getter.\n    this.ok = this.status >= 200 && this.status < 300;\n  }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n  /**\n   * Create a new `HttpHeaderResponse` with the given parameters.\n   */\n  constructor(init = {}) {\n    super(init);\n    _defineProperty(this, \"type\", HttpEventType.ResponseHeader);\n  }\n  /**\n   * Copy this `HttpHeaderResponse`, overriding its contents with the\n   * given parameter hash.\n   */\n  clone(update = {}) {\n    // Perform a straightforward initialization of the new HttpHeaderResponse,\n    // overriding the current parameters with new ones if given.\n    return new HttpHeaderResponse({\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n  /**\n   * Construct a new `HttpResponse`.\n   */\n  constructor(init = {}) {\n    super(init);\n    /**\n     * The response body, or `null` if one was not returned.\n     */\n    _defineProperty(this, \"body\", void 0);\n    _defineProperty(this, \"type\", HttpEventType.Response);\n    this.body = init.body !== undefined ? init.body : null;\n  }\n  clone(update = {}) {\n    return new HttpResponse({\n      body: update.body !== undefined ? update.body : this.body,\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n  constructor(init) {\n    // Initialize with a default status of 0 / Unknown Error.\n    super(init, 0, 'Unknown Error');\n    // If the response was successful, then this was a parse error. Otherwise, it was\n    // a protocol-level failure of some sort. Either the request failed in transit\n    // or the server returned an unsuccessful status code.\n    _defineProperty(this, \"name\", 'HttpErrorResponse');\n    _defineProperty(this, \"message\", void 0);\n    _defineProperty(this, \"error\", void 0);\n    /**\n     * Errors are never okay, even when the status code is in the 2xx success range.\n     */\n    _defineProperty(this, \"ok\", false);\n    if (this.status >= 200 && this.status < 300) {\n      this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n    } else {\n      this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n    }\n    this.error = init.error || null;\n  }\n}\n/**\n * We use these constant to prevent pulling the whole HttpStatusCode enum\n * Those are the only ones referenced directly by the framework\n */\nconst HTTP_STATUS_CODE_OK = 200;\nconst HTTP_STATUS_CODE_NO_CONTENT = 204;\n/**\n * Http status codes.\n * As per https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml\n * @publicApi\n */\nvar HttpStatusCode;\n(function (HttpStatusCode) {\n  HttpStatusCode[HttpStatusCode[\"Continue\"] = 100] = \"Continue\";\n  HttpStatusCode[HttpStatusCode[\"SwitchingProtocols\"] = 101] = \"SwitchingProtocols\";\n  HttpStatusCode[HttpStatusCode[\"Processing\"] = 102] = \"Processing\";\n  HttpStatusCode[HttpStatusCode[\"EarlyHints\"] = 103] = \"EarlyHints\";\n  HttpStatusCode[HttpStatusCode[\"Ok\"] = 200] = \"Ok\";\n  HttpStatusCode[HttpStatusCode[\"Created\"] = 201] = \"Created\";\n  HttpStatusCode[HttpStatusCode[\"Accepted\"] = 202] = \"Accepted\";\n  HttpStatusCode[HttpStatusCode[\"NonAuthoritativeInformation\"] = 203] = \"NonAuthoritativeInformation\";\n  HttpStatusCode[HttpStatusCode[\"NoContent\"] = 204] = \"NoContent\";\n  HttpStatusCode[HttpStatusCode[\"ResetContent\"] = 205] = \"ResetContent\";\n  HttpStatusCode[HttpStatusCode[\"PartialContent\"] = 206] = \"PartialContent\";\n  HttpStatusCode[HttpStatusCode[\"MultiStatus\"] = 207] = \"MultiStatus\";\n  HttpStatusCode[HttpStatusCode[\"AlreadyReported\"] = 208] = \"AlreadyReported\";\n  HttpStatusCode[HttpStatusCode[\"ImUsed\"] = 226] = \"ImUsed\";\n  HttpStatusCode[HttpStatusCode[\"MultipleChoices\"] = 300] = \"MultipleChoices\";\n  HttpStatusCode[HttpStatusCode[\"MovedPermanently\"] = 301] = \"MovedPermanently\";\n  HttpStatusCode[HttpStatusCode[\"Found\"] = 302] = \"Found\";\n  HttpStatusCode[HttpStatusCode[\"SeeOther\"] = 303] = \"SeeOther\";\n  HttpStatusCode[HttpStatusCode[\"NotModified\"] = 304] = \"NotModified\";\n  HttpStatusCode[HttpStatusCode[\"UseProxy\"] = 305] = \"UseProxy\";\n  HttpStatusCode[HttpStatusCode[\"Unused\"] = 306] = \"Unused\";\n  HttpStatusCode[HttpStatusCode[\"TemporaryRedirect\"] = 307] = \"TemporaryRedirect\";\n  HttpStatusCode[HttpStatusCode[\"PermanentRedirect\"] = 308] = \"PermanentRedirect\";\n  HttpStatusCode[HttpStatusCode[\"BadRequest\"] = 400] = \"BadRequest\";\n  HttpStatusCode[HttpStatusCode[\"Unauthorized\"] = 401] = \"Unauthorized\";\n  HttpStatusCode[HttpStatusCode[\"PaymentRequired\"] = 402] = \"PaymentRequired\";\n  HttpStatusCode[HttpStatusCode[\"Forbidden\"] = 403] = \"Forbidden\";\n  HttpStatusCode[HttpStatusCode[\"NotFound\"] = 404] = \"NotFound\";\n  HttpStatusCode[HttpStatusCode[\"MethodNotAllowed\"] = 405] = \"MethodNotAllowed\";\n  HttpStatusCode[HttpStatusCode[\"NotAcceptable\"] = 406] = \"NotAcceptable\";\n  HttpStatusCode[HttpStatusCode[\"ProxyAuthenticationRequired\"] = 407] = \"ProxyAuthenticationRequired\";\n  HttpStatusCode[HttpStatusCode[\"RequestTimeout\"] = 408] = \"RequestTimeout\";\n  HttpStatusCode[HttpStatusCode[\"Conflict\"] = 409] = \"Conflict\";\n  HttpStatusCode[HttpStatusCode[\"Gone\"] = 410] = \"Gone\";\n  HttpStatusCode[HttpStatusCode[\"LengthRequired\"] = 411] = \"LengthRequired\";\n  HttpStatusCode[HttpStatusCode[\"PreconditionFailed\"] = 412] = \"PreconditionFailed\";\n  HttpStatusCode[HttpStatusCode[\"PayloadTooLarge\"] = 413] = \"PayloadTooLarge\";\n  HttpStatusCode[HttpStatusCode[\"UriTooLong\"] = 414] = \"UriTooLong\";\n  HttpStatusCode[HttpStatusCode[\"UnsupportedMediaType\"] = 415] = \"UnsupportedMediaType\";\n  HttpStatusCode[HttpStatusCode[\"RangeNotSatisfiable\"] = 416] = \"RangeNotSatisfiable\";\n  HttpStatusCode[HttpStatusCode[\"ExpectationFailed\"] = 417] = \"ExpectationFailed\";\n  HttpStatusCode[HttpStatusCode[\"ImATeapot\"] = 418] = \"ImATeapot\";\n  HttpStatusCode[HttpStatusCode[\"MisdirectedRequest\"] = 421] = \"MisdirectedRequest\";\n  HttpStatusCode[HttpStatusCode[\"UnprocessableEntity\"] = 422] = \"UnprocessableEntity\";\n  HttpStatusCode[HttpStatusCode[\"Locked\"] = 423] = \"Locked\";\n  HttpStatusCode[HttpStatusCode[\"FailedDependency\"] = 424] = \"FailedDependency\";\n  HttpStatusCode[HttpStatusCode[\"TooEarly\"] = 425] = \"TooEarly\";\n  HttpStatusCode[HttpStatusCode[\"UpgradeRequired\"] = 426] = \"UpgradeRequired\";\n  HttpStatusCode[HttpStatusCode[\"PreconditionRequired\"] = 428] = \"PreconditionRequired\";\n  HttpStatusCode[HttpStatusCode[\"TooManyRequests\"] = 429] = \"TooManyRequests\";\n  HttpStatusCode[HttpStatusCode[\"RequestHeaderFieldsTooLarge\"] = 431] = \"RequestHeaderFieldsTooLarge\";\n  HttpStatusCode[HttpStatusCode[\"UnavailableForLegalReasons\"] = 451] = \"UnavailableForLegalReasons\";\n  HttpStatusCode[HttpStatusCode[\"InternalServerError\"] = 500] = \"InternalServerError\";\n  HttpStatusCode[HttpStatusCode[\"NotImplemented\"] = 501] = \"NotImplemented\";\n  HttpStatusCode[HttpStatusCode[\"BadGateway\"] = 502] = \"BadGateway\";\n  HttpStatusCode[HttpStatusCode[\"ServiceUnavailable\"] = 503] = \"ServiceUnavailable\";\n  HttpStatusCode[HttpStatusCode[\"GatewayTimeout\"] = 504] = \"GatewayTimeout\";\n  HttpStatusCode[HttpStatusCode[\"HttpVersionNotSupported\"] = 505] = \"HttpVersionNotSupported\";\n  HttpStatusCode[HttpStatusCode[\"VariantAlsoNegotiates\"] = 506] = \"VariantAlsoNegotiates\";\n  HttpStatusCode[HttpStatusCode[\"InsufficientStorage\"] = 507] = \"InsufficientStorage\";\n  HttpStatusCode[HttpStatusCode[\"LoopDetected\"] = 508] = \"LoopDetected\";\n  HttpStatusCode[HttpStatusCode[\"NotExtended\"] = 510] = \"NotExtended\";\n  HttpStatusCode[HttpStatusCode[\"NetworkAuthenticationRequired\"] = 511] = \"NetworkAuthenticationRequired\";\n})(HttpStatusCode || (HttpStatusCode = {}));\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n  return {\n    body,\n    headers: options.headers,\n    context: options.context,\n    observe: options.observe,\n    params: options.params,\n    reportProgress: options.reportProgress,\n    responseType: options.responseType,\n    withCredentials: options.withCredentials,\n    transferCache: options.transferCache\n  };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n * @usageNotes\n *\n * ### HTTP Request Example\n *\n * ```ts\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```ts\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```ts\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```ts\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n  constructor(handler) {\n    _defineProperty(this, \"handler\", void 0);\n    this.handler = handler;\n  }\n  /**\n   * Constructs an observable for a generic HTTP request that, when subscribed,\n   * fires the request through the chain of registered interceptors and on to the\n   * server.\n   *\n   * You can pass an `HttpRequest` directly as the only parameter. In this case,\n   * the call returns an observable of the raw `HttpEvent` stream.\n   *\n   * Alternatively you can pass an HTTP method as the first parameter,\n   * a URL string as the second, and an options hash containing the request body as the third.\n   * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n   * type of returned observable.\n   *   * The `responseType` value determines how a successful response body is parsed.\n   *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n   * object as a type parameter to the call.\n   *\n   * The `observe` value determines the return type, according to what you are interested in\n   * observing.\n   *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n   * progress events by default.\n   *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n   * where the `T` parameter depends on the `responseType` and any optionally provided type\n   * parameter.\n   *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n   *\n   */\n  request(first, url, options = {}) {\n    let req;\n    // First, check whether the primary argument is an instance of `HttpRequest`.\n    if (first instanceof HttpRequest) {\n      // It is. The other arguments must be undefined (per the signatures) and can be\n      // ignored.\n      req = first;\n    } else {\n      // It's a string, so it represents a URL. Construct a request based on it,\n      // and incorporate the remaining arguments (assuming `GET` unless a method is\n      // provided.\n      // Figure out the headers.\n      let headers = undefined;\n      if (options.headers instanceof HttpHeaders) {\n        headers = options.headers;\n      } else {\n        headers = new HttpHeaders(options.headers);\n      }\n      // Sort out parameters.\n      let params = undefined;\n      if (!!options.params) {\n        if (options.params instanceof HttpParams) {\n          params = options.params;\n        } else {\n          params = new HttpParams({\n            fromObject: options.params\n          });\n        }\n      }\n      // Construct the request.\n      req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n        headers,\n        context: options.context,\n        params,\n        reportProgress: options.reportProgress,\n        // By default, JSON is assumed to be returned for all calls.\n        responseType: options.responseType || 'json',\n        withCredentials: options.withCredentials,\n        transferCache: options.transferCache\n      });\n    }\n    // Start with an Observable.of() the initial request, and run the handler (which\n    // includes all interceptors) inside a concatMap(). This way, the handler runs\n    // inside an Observable chain, which causes interceptors to be re-run on every\n    // subscription (this also makes retries re-run the handler, including interceptors).\n    const events$ = of(req).pipe(concatMap(req => this.handler.handle(req)));\n    // If coming via the API signature which accepts a previously constructed HttpRequest,\n    // the only option is to get the event stream. Otherwise, return the event stream if\n    // that is what was requested.\n    if (first instanceof HttpRequest || options.observe === 'events') {\n      return events$;\n    }\n    // The requested stream contains either the full response or the body. In either\n    // case, the first step is to filter the event stream to extract a stream of\n    // responses(s).\n    const res$ = events$.pipe(filter(event => event instanceof HttpResponse));\n    // Decide which stream to return.\n    switch (options.observe || 'body') {\n      case 'body':\n        // The requested stream is the body. Map the response stream to the response\n        // body. This could be done more simply, but a misbehaving interceptor might\n        // transform the response body into a different format and ignore the requested\n        // responseType. Guard against this by validating that the response is of the\n        // requested type.\n        switch (req.responseType) {\n          case 'arraybuffer':\n            return res$.pipe(map(res => {\n              // Validate that the body is an ArrayBuffer.\n              if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                throw new _RuntimeError(2806 /* RuntimeErrorCode.RESPONSE_IS_NOT_AN_ARRAY_BUFFER */, ngDevMode && 'Response is not an ArrayBuffer.');\n              }\n              return res.body;\n            }));\n          case 'blob':\n            return res$.pipe(map(res => {\n              // Validate that the body is a Blob.\n              if (res.body !== null && !(res.body instanceof Blob)) {\n                throw new _RuntimeError(2807 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_BLOB */, ngDevMode && 'Response is not a Blob.');\n              }\n              return res.body;\n            }));\n          case 'text':\n            return res$.pipe(map(res => {\n              // Validate that the body is a string.\n              if (res.body !== null && typeof res.body !== 'string') {\n                throw new _RuntimeError(2808 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_STRING */, ngDevMode && 'Response is not a string.');\n              }\n              return res.body;\n            }));\n          case 'json':\n          default:\n            // No validation needed for JSON responses, as they can be of any type.\n            return res$.pipe(map(res => res.body));\n        }\n      case 'response':\n        // The response stream was requested directly, so return it.\n        return res$;\n      default:\n        // Guard against new future observe types being added.\n        throw new _RuntimeError(2809 /* RuntimeErrorCode.UNHANDLED_OBSERVE_TYPE */, ngDevMode && `Unreachable: unhandled observe type ${options.observe}}`);\n    }\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `DELETE` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   */\n  delete(url, options = {}) {\n    return this.request('DELETE', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `GET` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  get(url, options = {}) {\n    return this.request('GET', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `HEAD` request to execute on the server. The `HEAD` method returns\n   * meta information about the resource without transferring the\n   * resource itself. See the individual overloads for\n   * details on the return type.\n   */\n  head(url, options = {}) {\n    return this.request('HEAD', url, options);\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes a request with the special method\n   * `JSONP` to be dispatched via the interceptor pipeline.\n   * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n   * API endpoints that don't support newer,\n   * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n   * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n   * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n   * application making the request.\n   * The endpoint API must support JSONP callback for JSONP requests to work.\n   * The resource API returns the JSON response wrapped in a callback function.\n   * You can pass the callback function name as one of the query parameters.\n   * Note that JSONP requests can only be used with `GET` requests.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   */\n  jsonp(url, callbackParam) {\n    return this.request('JSONP', url, {\n      params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n      observe: 'body',\n      responseType: 'json'\n    });\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes the configured\n   * `OPTIONS` request to execute on the server. This method allows the client\n   * to determine the supported HTTP methods and other capabilities of an endpoint,\n   * without implying a resource action. See the individual overloads for\n   * details on the return type.\n   */\n  options(url, options = {}) {\n    return this.request('OPTIONS', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PATCH` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  patch(url, body, options = {}) {\n    return this.request('PATCH', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `POST` request to execute on the server. The server responds with the location of\n   * the replaced resource. See the individual overloads for\n   * details on the return type.\n   */\n  post(url, body, options = {}) {\n    return this.request('POST', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n   * with a new set of values.\n   * See the individual overloads for details on the return type.\n   */\n  put(url, body, options = {}) {\n    return this.request('PUT', url, addBody(options, body));\n  }\n}\n_HttpClient = HttpClient;\n_defineProperty(HttpClient, \"\\u0275fac\", function _HttpClient_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HttpClient)(i0.ɵɵinject(HttpHandler));\n});\n_defineProperty(HttpClient, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _HttpClient,\n  factory: _HttpClient.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClient, [{\n    type: Injectable\n  }], () => [{\n    type: HttpHandler\n  }], null);\n})();\nconst XSSI_PREFIX$1 = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * response url or the X-Request-URL header.\n */\nfunction getResponseUrl$1(response) {\n  if (response.url) {\n    return response.url;\n  }\n  // stored as lowercase in the map\n  const xRequestUrl = X_REQUEST_URL_HEADER.toLocaleLowerCase();\n  return response.headers.get(xRequestUrl);\n}\n/**\n * An internal injection token to reference `FetchBackend` implementation\n * in a tree-shakable way.\n */\nconst FETCH_BACKEND = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'FETCH_BACKEND' : '');\n/**\n * Uses `fetch` to send requests to a backend server.\n *\n * This `FetchBackend` requires the support of the\n * [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) which is available on all\n * supported browsers and on Node.js v18 or later.\n *\n * @see {@link HttpHandler}\n *\n * @publicApi\n */\nclass FetchBackend {\n  constructor() {\n    var _inject$fetch, _inject;\n    // We use an arrow function to always reference the current global implementation of `fetch`.\n    // This is helpful for cases when the global `fetch` implementation is modified by external code,\n    // see https://github.com/angular/angular/issues/57527.\n    _defineProperty(this, \"fetchImpl\", (_inject$fetch = (_inject = inject(FetchFactory, {\n      optional: true\n    })) === null || _inject === void 0 ? void 0 : _inject.fetch) !== null && _inject$fetch !== void 0 ? _inject$fetch : (...args) => globalThis.fetch(...args));\n    _defineProperty(this, \"ngZone\", inject(NgZone));\n    _defineProperty(this, \"destroyRef\", inject(DestroyRef));\n    _defineProperty(this, \"destroyed\", false);\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  handle(request) {\n    return new Observable(observer => {\n      const aborter = new AbortController();\n      this.doRequest(request, aborter.signal, observer).then(noop, error => observer.error(new HttpErrorResponse({\n        error\n      })));\n      return () => aborter.abort();\n    });\n  }\n  doRequest(request, signal, observer) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      var _getResponseUrl$;\n      const init = _this.createRequestInit(request);\n      let response;\n      try {\n        // Run fetch outside of Angular zone.\n        // This is due to Node.js fetch implementation (Undici) which uses a number of setTimeouts to check if\n        // the response should eventually timeout which causes extra CD cycles every 500ms\n        const fetchPromise = _this.ngZone.runOutsideAngular(() => _this.fetchImpl(request.urlWithParams, _objectSpread({\n          signal\n        }, init)));\n        // Make sure Zone.js doesn't trigger false-positive unhandled promise\n        // error in case the Promise is rejected synchronously. See function\n        // description for additional information.\n        silenceSuperfluousUnhandledPromiseRejection(fetchPromise);\n        // Send the `Sent` event before awaiting the response.\n        observer.next({\n          type: HttpEventType.Sent\n        });\n        response = yield fetchPromise;\n      } catch (error) {\n        var _error$status;\n        observer.error(new HttpErrorResponse({\n          error,\n          status: (_error$status = error.status) !== null && _error$status !== void 0 ? _error$status : 0,\n          statusText: error.statusText,\n          url: request.urlWithParams,\n          headers: error.headers\n        }));\n        return;\n      }\n      const headers = new HttpHeaders(response.headers);\n      const statusText = response.statusText;\n      const url = (_getResponseUrl$ = getResponseUrl$1(response)) !== null && _getResponseUrl$ !== void 0 ? _getResponseUrl$ : request.urlWithParams;\n      let status = response.status;\n      let body = null;\n      if (request.reportProgress) {\n        observer.next(new HttpHeaderResponse({\n          headers,\n          status,\n          statusText,\n          url\n        }));\n      }\n      if (response.body) {\n        // Read Progress\n        const contentLength = response.headers.get('content-length');\n        const chunks = [];\n        const reader = response.body.getReader();\n        let receivedLength = 0;\n        let decoder;\n        let partialText;\n        // We have to check whether the Zone is defined in the global scope because this may be called\n        // when the zone is nooped.\n        const reqZone = typeof Zone !== 'undefined' && Zone.current;\n        let canceled = false;\n        // Perform response processing outside of Angular zone to\n        // ensure no excessive change detection runs are executed\n        // Here calling the async ReadableStreamDefaultReader.read() is responsible for triggering CD\n        yield _this.ngZone.runOutsideAngular(/*#__PURE__*/_asyncToGenerator(function* () {\n          while (true) {\n            // Prevent reading chunks if the app is destroyed. Otherwise, we risk doing\n            // unnecessary work or triggering side effects after teardown.\n            // This may happen if the app was explicitly destroyed before\n            // the response returned entirely.\n            if (_this.destroyed) {\n              // Streams left in a pending state (due to `break` without cancel) may\n              // continue consuming or holding onto data behind the scenes.\n              // Calling `reader.cancel()` allows the browser or the underlying\n              // system to release any network or memory resources associated with the stream.\n              yield reader.cancel();\n              canceled = true;\n              break;\n            }\n            const {\n              done,\n              value\n            } = yield reader.read();\n            if (done) {\n              break;\n            }\n            chunks.push(value);\n            receivedLength += value.length;\n            if (request.reportProgress) {\n              partialText = request.responseType === 'text' ? (partialText !== null && partialText !== void 0 ? partialText : '') + (decoder !== null && decoder !== void 0 ? decoder : decoder = new TextDecoder()).decode(value, {\n                stream: true\n              }) : undefined;\n              const reportProgress = () => observer.next({\n                type: HttpEventType.DownloadProgress,\n                total: contentLength ? +contentLength : undefined,\n                loaded: receivedLength,\n                partialText\n              });\n              reqZone ? reqZone.run(reportProgress) : reportProgress();\n            }\n          }\n        }));\n        // We need to manage the canceled state — because the Streams API does not\n        // expose a direct `.state` property on the reader.\n        // We need to `return` because `parseBody` may not be able to parse chunks\n        // that were only partially read (due to cancellation caused by app destruction).\n        if (canceled) {\n          observer.complete();\n          return;\n        }\n        // Combine all chunks.\n        const chunksAll = _this.concatChunks(chunks, receivedLength);\n        try {\n          var _response$headers$get;\n          const contentType = (_response$headers$get = response.headers.get(CONTENT_TYPE_HEADER)) !== null && _response$headers$get !== void 0 ? _response$headers$get : '';\n          body = _this.parseBody(request, chunksAll, contentType);\n        } catch (error) {\n          var _getResponseUrl$2;\n          // Body loading or parsing failed\n          observer.error(new HttpErrorResponse({\n            error,\n            headers: new HttpHeaders(response.headers),\n            status: response.status,\n            statusText: response.statusText,\n            url: (_getResponseUrl$2 = getResponseUrl$1(response)) !== null && _getResponseUrl$2 !== void 0 ? _getResponseUrl$2 : request.urlWithParams\n          }));\n          return;\n        }\n      }\n      // Same behavior as the XhrBackend\n      if (status === 0) {\n        status = body ? HTTP_STATUS_CODE_OK : 0;\n      }\n      // ok determines whether the response will be transmitted on the event or\n      // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n      // but a successful status code can still result in an error if the user\n      // asked for JSON data and the body cannot be parsed as such.\n      const ok = status >= 200 && status < 300;\n      if (ok) {\n        observer.next(new HttpResponse({\n          body,\n          headers,\n          status,\n          statusText,\n          url\n        }));\n        // The full body has been received and delivered, no further events\n        // are possible. This request is complete.\n        observer.complete();\n      } else {\n        observer.error(new HttpErrorResponse({\n          error: body,\n          headers,\n          status,\n          statusText,\n          url\n        }));\n      }\n    })();\n  }\n  parseBody(request, binContent, contentType) {\n    switch (request.responseType) {\n      case 'json':\n        // stripping the XSSI when present\n        const text = new TextDecoder().decode(binContent).replace(XSSI_PREFIX$1, '');\n        return text === '' ? null : JSON.parse(text);\n      case 'text':\n        return new TextDecoder().decode(binContent);\n      case 'blob':\n        return new Blob([binContent], {\n          type: contentType\n        });\n      case 'arraybuffer':\n        return binContent.buffer;\n    }\n  }\n  createRequestInit(req) {\n    // We could share some of this logic with the XhrBackend\n    const headers = {};\n    const credentials = req.withCredentials ? 'include' : undefined;\n    // Setting all the requested headers.\n    req.headers.forEach((name, values) => headers[name] = values.join(','));\n    // Add an Accept header if one isn't present already.\n    if (!req.headers.has(ACCEPT_HEADER)) {\n      headers[ACCEPT_HEADER] = ACCEPT_HEADER_VALUE;\n    }\n    // Auto-detect the Content-Type header if one isn't present already.\n    if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n      const detectedType = req.detectContentTypeHeader();\n      // Sometimes Content-Type detection fails.\n      if (detectedType !== null) {\n        headers[CONTENT_TYPE_HEADER] = detectedType;\n      }\n    }\n    return {\n      body: req.serializeBody(),\n      method: req.method,\n      headers,\n      credentials\n    };\n  }\n  concatChunks(chunks, totalLength) {\n    const chunksAll = new Uint8Array(totalLength);\n    let position = 0;\n    for (const chunk of chunks) {\n      chunksAll.set(chunk, position);\n      position += chunk.length;\n    }\n    return chunksAll;\n  }\n}\n_FetchBackend = FetchBackend;\n_defineProperty(FetchBackend, \"\\u0275fac\", function _FetchBackend_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FetchBackend)();\n});\n_defineProperty(FetchBackend, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _FetchBackend,\n  factory: _FetchBackend.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FetchBackend, [{\n    type: Injectable\n  }], () => [], null);\n})();\n/**\n * Abstract class to provide a mocked implementation of `fetch()`\n */\nclass FetchFactory {}\nfunction noop() {}\n/**\n * Zone.js treats a rejected promise that has not yet been awaited\n * as an unhandled error. This function adds a noop `.then` to make\n * sure that Zone.js doesn't throw an error if the Promise is rejected\n * synchronously.\n */\nfunction silenceSuperfluousUnhandledPromiseRejection(promise) {\n  promise.then(noop, noop);\n}\nfunction interceptorChainEndFn(req, finalHandlerFn) {\n  return finalHandlerFn(req);\n}\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(chainTailFn, interceptor) {\n  return (initialRequest, finalHandlerFn) => interceptor.intercept(initialRequest, {\n    handle: downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)\n  });\n}\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(chainTailFn, interceptorFn, injector) {\n  return (initialRequest, finalHandlerFn) => runInInjectionContext(injector, () => interceptorFn(initialRequest, downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)));\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTORS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nconst HTTP_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTOR_FNS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s that are only set in root.\n */\nconst HTTP_ROOT_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_ROOT_INTERCEPTOR_FNS' : '');\n// TODO(atscott): We need a larger discussion about stability and what should contribute to stability.\n// Should the whole interceptor chain contribute to stability or just the backend request #55075?\n// Should HttpClient contribute to stability automatically at all?\nconst REQUESTS_CONTRIBUTE_TO_STABILITY = new InjectionToken(ngDevMode ? 'REQUESTS_CONTRIBUTE_TO_STABILITY' : '', {\n  providedIn: 'root',\n  factory: () => true\n});\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nfunction legacyInterceptorFnFactory() {\n  let chain = null;\n  return (req, handler) => {\n    if (chain === null) {\n      var _inject2;\n      const interceptors = (_inject2 = inject(HTTP_INTERCEPTORS, {\n        optional: true\n      })) !== null && _inject2 !== void 0 ? _inject2 : [];\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      chain = interceptors.reduceRight(adaptLegacyInterceptorToChain, interceptorChainEndFn);\n    }\n    const pendingTasks = inject(_PendingTasksInternal);\n    const contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n    if (contributeToStability) {\n      const taskId = pendingTasks.add();\n      return chain(req, handler).pipe(finalize(() => pendingTasks.remove(taskId)));\n    } else {\n      return chain(req, handler);\n    }\n  };\n}\nlet fetchBackendWarningDisplayed = false;\nclass HttpInterceptorHandler extends HttpHandler {\n  constructor(backend, injector) {\n    super();\n    _defineProperty(this, \"backend\", void 0);\n    _defineProperty(this, \"injector\", void 0);\n    _defineProperty(this, \"chain\", null);\n    _defineProperty(this, \"pendingTasks\", inject(_PendingTasksInternal));\n    _defineProperty(this, \"contributeToStability\", inject(REQUESTS_CONTRIBUTE_TO_STABILITY));\n    this.backend = backend;\n    this.injector = injector;\n    // We strongly recommend using fetch backend for HTTP calls when SSR is used\n    // for an application. The logic below checks if that's the case and produces\n    // a warning otherwise.\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !fetchBackendWarningDisplayed) {\n      const isServer = isPlatformServer(injector.get(PLATFORM_ID));\n      // This flag is necessary because provideHttpClientTesting() overrides the backend\n      // even if `withFetch()` is used within the test. When the testing HTTP backend is provided,\n      // no HTTP calls are actually performed during the test, so producing a warning would be\n      // misleading.\n      const isTestingBackend = this.backend.isTestingBackend;\n      if (isServer && !(this.backend instanceof FetchBackend) && !isTestingBackend) {\n        fetchBackendWarningDisplayed = true;\n        injector.get(_Console).warn(_formatRuntimeError(2801 /* RuntimeErrorCode.NOT_USING_FETCH_BACKEND_IN_SSR */, 'Angular detected that `HttpClient` is not configured ' + \"to use `fetch` APIs. It's strongly recommended to \" + 'enable `fetch` for applications that use Server-Side Rendering ' + 'for better performance and compatibility. ' + 'To enable `fetch`, add the `withFetch()` to the `provideHttpClient()` ' + 'call at the root of the application.'));\n      }\n    }\n  }\n  handle(initialRequest) {\n    if (this.chain === null) {\n      const dedupedInterceptorFns = Array.from(new Set([...this.injector.get(HTTP_INTERCEPTOR_FNS), ...this.injector.get(HTTP_ROOT_INTERCEPTOR_FNS, [])]));\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      this.chain = dedupedInterceptorFns.reduceRight((nextSequencedFn, interceptorFn) => chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector), interceptorChainEndFn);\n    }\n    if (this.contributeToStability) {\n      const taskId = this.pendingTasks.add();\n      return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest)).pipe(finalize(() => this.pendingTasks.remove(taskId)));\n    } else {\n      return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest));\n    }\n  }\n}\n_HttpInterceptorHandler = HttpInterceptorHandler;\n_defineProperty(HttpInterceptorHandler, \"\\u0275fac\", function _HttpInterceptorHandler_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HttpInterceptorHandler)(i0.ɵɵinject(HttpBackend), i0.ɵɵinject(i0.EnvironmentInjector));\n});\n_defineProperty(HttpInterceptorHandler, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _HttpInterceptorHandler,\n  factory: _HttpInterceptorHandler.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpInterceptorHandler, [{\n    type: Injectable\n  }], () => [{\n    type: HttpBackend\n  }, {\n    type: i0.EnvironmentInjector\n  }], null);\n})();\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n  if (typeof window === 'object') {\n    return window;\n  }\n  return {};\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n  constructor(callbackMap, document) {\n    _defineProperty(this, \"callbackMap\", void 0);\n    _defineProperty(this, \"document\", void 0);\n    /**\n     * A resolved promise that can be used to schedule microtasks in the event handlers.\n     */\n    _defineProperty(this, \"resolvedPromise\", Promise.resolve());\n    this.callbackMap = callbackMap;\n    this.document = document;\n  }\n  /**\n   * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n   */\n  nextCallback() {\n    return `ng_jsonp_callback_${nextRequestId++}`;\n  }\n  /**\n   * Processes a JSONP request and returns an event stream of the results.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   *\n   */\n  handle(req) {\n    // Firstly, check both the method and response type. If either doesn't match\n    // then the request was improperly routed here and cannot be handled.\n    if (req.method !== 'JSONP') {\n      throw new Error(JSONP_ERR_WRONG_METHOD);\n    } else if (req.responseType !== 'json') {\n      throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n    }\n    // Check the request headers. JSONP doesn't support headers and\n    // cannot set any that were supplied.\n    if (req.headers.keys().length > 0) {\n      throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n    }\n    // Everything else happens inside the Observable boundary.\n    return new Observable(observer => {\n      // The first step to make a request is to generate the callback name, and replace the\n      // callback placeholder in the URL with the name. Care has to be taken here to ensure\n      // a trailing &, if matched, gets inserted back into the URL in the correct place.\n      const callback = this.nextCallback();\n      const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n      // Construct the <script> tag and point it at the URL.\n      const node = this.document.createElement('script');\n      node.src = url;\n      // A JSONP request requires waiting for multiple callbacks. These variables\n      // are closed over and track state across those callbacks.\n      // The response object, if one has been received, or null otherwise.\n      let body = null;\n      // Whether the response callback has been called.\n      let finished = false;\n      // Set the response callback in this.callbackMap (which will be the window\n      // object in the browser. The script being loaded via the <script> tag will\n      // eventually call this callback.\n      this.callbackMap[callback] = data => {\n        // Data has been received from the JSONP script. Firstly, delete this callback.\n        delete this.callbackMap[callback];\n        // Set state to indicate data was received.\n        body = data;\n        finished = true;\n      };\n      // cleanup() is a utility closure that removes the <script> from the page and\n      // the response callback from the window. This logic is used in both the\n      // success, error, and cancellation paths, so it's extracted out for convenience.\n      const cleanup = () => {\n        node.removeEventListener('load', onLoad);\n        node.removeEventListener('error', onError);\n        // Remove the <script> tag if it's still on the page.\n        node.remove();\n        // Remove the response callback from the callbackMap (window object in the\n        // browser).\n        delete this.callbackMap[callback];\n      };\n      // onLoad() is the success callback which runs after the response callback\n      // if the JSONP script loads successfully. The event itself is unimportant.\n      // If something went wrong, onLoad() may run without the response callback\n      // having been invoked.\n      const onLoad = event => {\n        // We wrap it in an extra Promise, to ensure the microtask\n        // is scheduled after the loaded endpoint has executed any potential microtask itself,\n        // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n        this.resolvedPromise.then(() => {\n          // Cleanup the page.\n          cleanup();\n          // Check whether the response callback has run.\n          if (!finished) {\n            // It hasn't, something went wrong with the request. Return an error via\n            // the Observable error path. All JSONP errors have status 0.\n            observer.error(new HttpErrorResponse({\n              url,\n              status: 0,\n              statusText: 'JSONP Error',\n              error: new Error(JSONP_ERR_NO_CALLBACK)\n            }));\n            return;\n          }\n          // Success. body either contains the response body or null if none was\n          // returned.\n          observer.next(new HttpResponse({\n            body,\n            status: HTTP_STATUS_CODE_OK,\n            statusText: 'OK',\n            url\n          }));\n          // Complete the stream, the response is over.\n          observer.complete();\n        });\n      };\n      // onError() is the error callback, which runs if the script returned generates\n      // a Javascript error. It emits the error via the Observable error channel as\n      // a HttpErrorResponse.\n      const onError = error => {\n        cleanup();\n        // Wrap the error in a HttpErrorResponse.\n        observer.error(new HttpErrorResponse({\n          error,\n          status: 0,\n          statusText: 'JSONP Error',\n          url\n        }));\n      };\n      // Subscribe to both the success (load) and error events on the <script> tag,\n      // and add it to the page.\n      node.addEventListener('load', onLoad);\n      node.addEventListener('error', onError);\n      this.document.body.appendChild(node);\n      // The request has now been successfully sent.\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      // Cancellation handler.\n      return () => {\n        if (!finished) {\n          this.removeListeners(node);\n        }\n        // And finally, clean up the page.\n        cleanup();\n      };\n    });\n  }\n  removeListeners(script) {\n    // Issue #34818\n    // Changing <script>'s ownerDocument will prevent it from execution.\n    // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n    foreignDocument !== null && foreignDocument !== void 0 ? foreignDocument : foreignDocument = this.document.implementation.createHTMLDocument();\n    foreignDocument.adoptNode(script);\n  }\n}\n_JsonpClientBackend = JsonpClientBackend;\n_defineProperty(JsonpClientBackend, \"\\u0275fac\", function _JsonpClientBackend_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _JsonpClientBackend)(i0.ɵɵinject(JsonpCallbackContext), i0.ɵɵinject(DOCUMENT));\n});\n_defineProperty(JsonpClientBackend, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _JsonpClientBackend,\n  factory: _JsonpClientBackend.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpClientBackend, [{\n    type: Injectable\n  }], () => [{\n    type: JsonpCallbackContext\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nfunction jsonpInterceptorFn(req, next) {\n  if (req.method === 'JSONP') {\n    return inject(JsonpClientBackend).handle(req);\n  }\n  // Fall through for normal HTTP requests.\n  return next(req);\n}\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n  constructor(injector) {\n    _defineProperty(this, \"injector\", void 0);\n    this.injector = injector;\n  }\n  /**\n   * Identifies and handles a given JSONP request.\n   * @param initialRequest The outgoing request object to handle.\n   * @param next The next interceptor in the chain, or the backend\n   * if no interceptors remain in the chain.\n   * @returns An observable of the event stream.\n   */\n  intercept(initialRequest, next) {\n    return runInInjectionContext(this.injector, () => jsonpInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n}\n_JsonpInterceptor = JsonpInterceptor;\n_defineProperty(JsonpInterceptor, \"\\u0275fac\", function _JsonpInterceptor_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _JsonpInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n});\n_defineProperty(JsonpInterceptor, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _JsonpInterceptor,\n  factory: _JsonpInterceptor.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\nconst X_REQUEST_URL_REGEXP = RegExp(`^${X_REQUEST_URL_HEADER}:`, 'm');\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n  if ('responseURL' in xhr && xhr.responseURL) {\n    return xhr.responseURL;\n  }\n  if (X_REQUEST_URL_REGEXP.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader(X_REQUEST_URL_HEADER);\n  }\n  return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n  constructor(xhrFactory) {\n    _defineProperty(this, \"xhrFactory\", void 0);\n    this.xhrFactory = xhrFactory;\n  }\n  /**\n   * Processes a request and returns a stream of response events.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   */\n  handle(req) {\n    // Quick check to give a better error message when a user attempts to use\n    // HttpClient.jsonp() without installing the HttpClientJsonpModule\n    if (req.method === 'JSONP') {\n      throw new _RuntimeError(-2800 /* RuntimeErrorCode.MISSING_JSONP_MODULE */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Cannot make a JSONP request without JSONP support. To fix the problem, either add the \\`withJsonpSupport()\\` call (if \\`provideHttpClient()\\` is used) or import the \\`HttpClientJsonpModule\\` in the root NgModule.`);\n    }\n    // Check whether this factory has a special function to load an XHR implementation\n    // for various non-browser environments. We currently limit it to only `ServerXhr`\n    // class, which needs to load an XHR implementation.\n    const xhrFactory = this.xhrFactory;\n    const source = xhrFactory.ɵloadImpl ? from(xhrFactory.ɵloadImpl()) : of(null);\n    return source.pipe(switchMap(() => {\n      // Everything happens on Observable subscription.\n      return new Observable(observer => {\n        // Start by setting up the XHR object with request method, URL, and withCredentials\n        // flag.\n        const xhr = xhrFactory.build();\n        xhr.open(req.method, req.urlWithParams);\n        if (req.withCredentials) {\n          xhr.withCredentials = true;\n        }\n        // Add all the requested headers.\n        req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n        // Add an Accept header if one isn't present already.\n        if (!req.headers.has(ACCEPT_HEADER)) {\n          xhr.setRequestHeader(ACCEPT_HEADER, ACCEPT_HEADER_VALUE);\n        }\n        // Auto-detect the Content-Type header if one isn't present already.\n        if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n          const detectedType = req.detectContentTypeHeader();\n          // Sometimes Content-Type detection fails.\n          if (detectedType !== null) {\n            xhr.setRequestHeader(CONTENT_TYPE_HEADER, detectedType);\n          }\n        }\n        // Set the responseType if one was requested.\n        if (req.responseType) {\n          const responseType = req.responseType.toLowerCase();\n          // JSON responses need to be processed as text. This is because if the server\n          // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n          // xhr.response will be null, and xhr.responseText cannot be accessed to\n          // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n          // is parsed by first requesting text and then applying JSON.parse.\n          xhr.responseType = responseType !== 'json' ? responseType : 'text';\n        }\n        // Serialize the request body if one is present. If not, this will be set to null.\n        const reqBody = req.serializeBody();\n        // If progress events are enabled, response headers will be delivered\n        // in two events - the HttpHeaderResponse event and the full HttpResponse\n        // event. However, since response headers don't change in between these\n        // two events, it doesn't make sense to parse them twice. So headerResponse\n        // caches the data extracted from the response whenever it's first parsed,\n        // to ensure parsing isn't duplicated.\n        let headerResponse = null;\n        // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n        // state, and memoizes it into headerResponse.\n        const partialFromXhr = () => {\n          if (headerResponse !== null) {\n            return headerResponse;\n          }\n          const statusText = xhr.statusText || 'OK';\n          // Parse headers from XMLHttpRequest - this step is lazy.\n          const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n          // Read the response URL from the XMLHttpResponse instance and fall back on the\n          // request URL.\n          const url = getResponseUrl(xhr) || req.url;\n          // Construct the HttpHeaderResponse and memoize it.\n          headerResponse = new HttpHeaderResponse({\n            headers,\n            status: xhr.status,\n            statusText,\n            url\n          });\n          return headerResponse;\n        };\n        // Next, a few closures are defined for the various events which XMLHttpRequest can\n        // emit. This allows them to be unregistered as event listeners later.\n        // First up is the load event, which represents a response being fully available.\n        const onLoad = () => {\n          // Read response state from the memoized partial data.\n          let {\n            headers,\n            status,\n            statusText,\n            url\n          } = partialFromXhr();\n          // The body will be read out if present.\n          let body = null;\n          if (status !== HTTP_STATUS_CODE_NO_CONTENT) {\n            // Use XMLHttpRequest.response if set, responseText otherwise.\n            body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n          }\n          // Normalize another potential bug (this one comes from CORS).\n          if (status === 0) {\n            status = !!body ? HTTP_STATUS_CODE_OK : 0;\n          }\n          // ok determines whether the response will be transmitted on the event or\n          // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n          // but a successful status code can still result in an error if the user\n          // asked for JSON data and the body cannot be parsed as such.\n          let ok = status >= 200 && status < 300;\n          // Check whether the body needs to be parsed as JSON (in many cases the browser\n          // will have done that already).\n          if (req.responseType === 'json' && typeof body === 'string') {\n            // Save the original body, before attempting XSSI prefix stripping.\n            const originalBody = body;\n            body = body.replace(XSSI_PREFIX, '');\n            try {\n              // Attempt the parse. If it fails, a parse error should be delivered to the\n              // user.\n              body = body !== '' ? JSON.parse(body) : null;\n            } catch (error) {\n              // Since the JSON.parse failed, it's reasonable to assume this might not have\n              // been a JSON response. Restore the original body (including any XSSI prefix)\n              // to deliver a better error response.\n              body = originalBody;\n              // If this was an error request to begin with, leave it as a string, it\n              // probably just isn't JSON. Otherwise, deliver the parsing error to the user.\n              if (ok) {\n                // Even though the response status was 2xx, this is still an error.\n                ok = false;\n                // The parse error contains the text of the body that failed to parse.\n                body = {\n                  error,\n                  text: body\n                };\n              }\n            }\n          }\n          if (ok) {\n            // A successful response is delivered on the event stream.\n            observer.next(new HttpResponse({\n              body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n            // The full body has been received and delivered, no further events\n            // are possible. This request is complete.\n            observer.complete();\n          } else {\n            // An unsuccessful request is delivered on the error channel.\n            observer.error(new HttpErrorResponse({\n              // The error in this case is the response body (error from the server).\n              error: body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n          }\n        };\n        // The onError callback is called when something goes wrong at the network level.\n        // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n        // transmitted on the error channel.\n        const onError = error => {\n          const {\n            url\n          } = partialFromXhr();\n          const res = new HttpErrorResponse({\n            error,\n            status: xhr.status || 0,\n            statusText: xhr.statusText || 'Unknown Error',\n            url: url || undefined\n          });\n          observer.error(res);\n        };\n        // The sentHeaders flag tracks whether the HttpResponseHeaders event\n        // has been sent on the stream. This is necessary to track if progress\n        // is enabled since the event will be sent on only the first download\n        // progress event.\n        let sentHeaders = false;\n        // The download progress event handler, which is only registered if\n        // progress events are enabled.\n        const onDownProgress = event => {\n          // Send the HttpResponseHeaders event if it hasn't been sent already.\n          if (!sentHeaders) {\n            observer.next(partialFromXhr());\n            sentHeaders = true;\n          }\n          // Start building the download progress event to deliver on the response\n          // event stream.\n          let progressEvent = {\n            type: HttpEventType.DownloadProgress,\n            loaded: event.loaded\n          };\n          // Set the total number of bytes in the event if it's available.\n          if (event.lengthComputable) {\n            progressEvent.total = event.total;\n          }\n          // If the request was for text content and a partial response is\n          // available on XMLHttpRequest, include it in the progress event\n          // to allow for streaming reads.\n          if (req.responseType === 'text' && !!xhr.responseText) {\n            progressEvent.partialText = xhr.responseText;\n          }\n          // Finally, fire the event.\n          observer.next(progressEvent);\n        };\n        // The upload progress event handler, which is only registered if\n        // progress events are enabled.\n        const onUpProgress = event => {\n          // Upload progress events are simpler. Begin building the progress\n          // event.\n          let progress = {\n            type: HttpEventType.UploadProgress,\n            loaded: event.loaded\n          };\n          // If the total number of bytes being uploaded is available, include\n          // it.\n          if (event.lengthComputable) {\n            progress.total = event.total;\n          }\n          // Send the event.\n          observer.next(progress);\n        };\n        // By default, register for load and error events.\n        xhr.addEventListener('load', onLoad);\n        xhr.addEventListener('error', onError);\n        xhr.addEventListener('timeout', onError);\n        xhr.addEventListener('abort', onError);\n        // Progress events are only enabled if requested.\n        if (req.reportProgress) {\n          // Download progress is always enabled if requested.\n          xhr.addEventListener('progress', onDownProgress);\n          // Upload progress depends on whether there is a body to upload.\n          if (reqBody !== null && xhr.upload) {\n            xhr.upload.addEventListener('progress', onUpProgress);\n          }\n        }\n        // Fire the request, and notify the event stream that it was fired.\n        xhr.send(reqBody);\n        observer.next({\n          type: HttpEventType.Sent\n        });\n        // This is the return from the Observable function, which is the\n        // request cancellation handler.\n        return () => {\n          // On a cancellation, remove all registered event listeners.\n          xhr.removeEventListener('error', onError);\n          xhr.removeEventListener('abort', onError);\n          xhr.removeEventListener('load', onLoad);\n          xhr.removeEventListener('timeout', onError);\n          if (req.reportProgress) {\n            xhr.removeEventListener('progress', onDownProgress);\n            if (reqBody !== null && xhr.upload) {\n              xhr.upload.removeEventListener('progress', onUpProgress);\n            }\n          }\n          // Finally, abort the in-flight request.\n          if (xhr.readyState !== xhr.DONE) {\n            xhr.abort();\n          }\n        };\n      });\n    }));\n  }\n}\n_HttpXhrBackend = HttpXhrBackend;\n_defineProperty(HttpXhrBackend, \"\\u0275fac\", function _HttpXhrBackend_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HttpXhrBackend)(i0.ɵɵinject(XhrFactory));\n});\n_defineProperty(HttpXhrBackend, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _HttpXhrBackend,\n  factory: _HttpXhrBackend.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXhrBackend, [{\n    type: Injectable\n  }], () => [{\n    type: XhrFactory\n  }], null);\n})();\nconst XSRF_ENABLED = new InjectionToken(ngDevMode ? 'XSRF_ENABLED' : '');\nconst XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nconst XSRF_COOKIE_NAME = new InjectionToken(ngDevMode ? 'XSRF_COOKIE_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_COOKIE_NAME\n});\nconst XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nconst XSRF_HEADER_NAME = new InjectionToken(ngDevMode ? 'XSRF_HEADER_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_HEADER_NAME\n});\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n  constructor(doc, cookieName) {\n    _defineProperty(this, \"doc\", void 0);\n    _defineProperty(this, \"cookieName\", void 0);\n    _defineProperty(this, \"lastCookieString\", '');\n    _defineProperty(this, \"lastToken\", null);\n    /**\n     * @internal for testing\n     */\n    _defineProperty(this, \"parseCount\", 0);\n    this.doc = doc;\n    this.cookieName = cookieName;\n  }\n  getToken() {\n    if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n      return null;\n    }\n    const cookieString = this.doc.cookie || '';\n    if (cookieString !== this.lastCookieString) {\n      this.parseCount++;\n      this.lastToken = parseCookieValue(cookieString, this.cookieName);\n      this.lastCookieString = cookieString;\n    }\n    return this.lastToken;\n  }\n}\n_HttpXsrfCookieExtractor = HttpXsrfCookieExtractor;\n_defineProperty(HttpXsrfCookieExtractor, \"\\u0275fac\", function _HttpXsrfCookieExtractor_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HttpXsrfCookieExtractor)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(XSRF_COOKIE_NAME));\n});\n_defineProperty(HttpXsrfCookieExtractor, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _HttpXsrfCookieExtractor,\n  factory: _HttpXsrfCookieExtractor.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfCookieExtractor, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [XSRF_COOKIE_NAME]\n    }]\n  }], null);\n})();\nfunction xsrfInterceptorFn(req, next) {\n  const lcUrl = req.url.toLowerCase();\n  // Skip both non-mutating requests and absolute URLs.\n  // Non-mutating requests don't require a token, and absolute URLs require special handling\n  // anyway as the cookie set\n  // on our origin is not the same as the token expected by another origin.\n  if (!inject(XSRF_ENABLED) || req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') || lcUrl.startsWith('https://')) {\n    return next(req);\n  }\n  const token = inject(HttpXsrfTokenExtractor).getToken();\n  const headerName = inject(XSRF_HEADER_NAME);\n  // Be careful not to overwrite an existing header of the same name.\n  if (token != null && !req.headers.has(headerName)) {\n    req = req.clone({\n      headers: req.headers.set(headerName, token)\n    });\n  }\n  return next(req);\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n  constructor(injector) {\n    _defineProperty(this, \"injector\", void 0);\n    this.injector = injector;\n  }\n  intercept(initialRequest, next) {\n    return runInInjectionContext(this.injector, () => xsrfInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n}\n_HttpXsrfInterceptor = HttpXsrfInterceptor;\n_defineProperty(HttpXsrfInterceptor, \"\\u0275fac\", function _HttpXsrfInterceptor_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HttpXsrfInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n});\n_defineProperty(HttpXsrfInterceptor, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _HttpXsrfInterceptor,\n  factory: _HttpXsrfInterceptor.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nvar HttpFeatureKind;\n(function (HttpFeatureKind) {\n  HttpFeatureKind[HttpFeatureKind[\"Interceptors\"] = 0] = \"Interceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"LegacyInterceptors\"] = 1] = \"LegacyInterceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"CustomXsrfConfiguration\"] = 2] = \"CustomXsrfConfiguration\";\n  HttpFeatureKind[HttpFeatureKind[\"NoXsrfProtection\"] = 3] = \"NoXsrfProtection\";\n  HttpFeatureKind[HttpFeatureKind[\"JsonpSupport\"] = 4] = \"JsonpSupport\";\n  HttpFeatureKind[HttpFeatureKind[\"RequestsMadeViaParent\"] = 5] = \"RequestsMadeViaParent\";\n  HttpFeatureKind[HttpFeatureKind[\"Fetch\"] = 6] = \"Fetch\";\n})(HttpFeatureKind || (HttpFeatureKind = {}));\nfunction makeHttpFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * It's strongly recommended to enable\n * [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) for applications that use\n * Server-Side Rendering for better performance and compatibility. To enable `fetch`, add\n * `withFetch()` feature to the `provideHttpClient()` call at the root of the application:\n *\n * ```ts\n * provideHttpClient(withFetch());\n * ```\n *\n * </div>\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n * @see {@link withFetch}\n */\nfunction provideHttpClient(...features) {\n  if (ngDevMode) {\n    const featureKinds = new Set(features.map(f => f.ɵkind));\n    if (featureKinds.has(HttpFeatureKind.NoXsrfProtection) && featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)) {\n      throw new Error(ngDevMode ? `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.` : '');\n    }\n  }\n  const providers = [HttpClient, HttpXhrBackend, HttpInterceptorHandler, {\n    provide: HttpHandler,\n    useExisting: HttpInterceptorHandler\n  }, {\n    provide: HttpBackend,\n    useFactory: () => {\n      var _inject3;\n      return (_inject3 = inject(FETCH_BACKEND, {\n        optional: true\n      })) !== null && _inject3 !== void 0 ? _inject3 : inject(HttpXhrBackend);\n    }\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: xsrfInterceptorFn,\n    multi: true\n  }, {\n    provide: XSRF_ENABLED,\n    useValue: true\n  }, {\n    provide: HttpXsrfTokenExtractor,\n    useClass: HttpXsrfCookieExtractor\n  }];\n  for (const feature of features) {\n    providers.push(...feature.ɵproviders);\n  }\n  return makeEnvironmentProviders(providers);\n}\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withInterceptors(interceptorFns) {\n  return makeHttpFeature(HttpFeatureKind.Interceptors, interceptorFns.map(interceptorFn => {\n    return {\n      provide: HTTP_INTERCEPTOR_FNS,\n      useValue: interceptorFn,\n      multi: true\n    };\n  }));\n}\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken(ngDevMode ? 'LEGACY_INTERCEPTOR_FN' : '');\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nfunction withInterceptorsFromDi() {\n  // Note: the legacy interceptor function is provided here via an intermediate token\n  // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n  // included multiple times, all of the multi-provider entries will have the same instance of the\n  // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n  // interceptors will not run multiple times.\n  return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [{\n    provide: LEGACY_INTERCEPTOR_FN,\n    useFactory: legacyInterceptorFnFactory\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useExisting: LEGACY_INTERCEPTOR_FN,\n    multi: true\n  }]);\n}\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withXsrfConfiguration({\n  cookieName,\n  headerName\n}) {\n  const providers = [];\n  if (cookieName !== undefined) {\n    providers.push({\n      provide: XSRF_COOKIE_NAME,\n      useValue: cookieName\n    });\n  }\n  if (headerName !== undefined) {\n    providers.push({\n      provide: XSRF_HEADER_NAME,\n      useValue: headerName\n    });\n  }\n  return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withNoXsrfProtection() {\n  return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [{\n    provide: XSRF_ENABLED,\n    useValue: false\n  }]);\n}\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nfunction withJsonpSupport() {\n  return makeHttpFeature(HttpFeatureKind.JsonpSupport, [JsonpClientBackend, {\n    provide: JsonpCallbackContext,\n    useFactory: jsonpCallbackContext\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: jsonpInterceptorFn,\n    multi: true\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withRequestsMadeViaParent() {\n  return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [{\n    provide: HttpBackend,\n    useFactory: () => {\n      const handlerFromParent = inject(HttpHandler, {\n        skipSelf: true,\n        optional: true\n      });\n      if (ngDevMode && handlerFromParent === null) {\n        throw new Error('withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient');\n      }\n      return handlerFromParent;\n    }\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests using the fetch API.\n *\n * Note: The Fetch API doesn't support progress report on uploads.\n *\n * @publicApi\n */\nfunction withFetch() {\n  return makeHttpFeature(HttpFeatureKind.Fetch, [FetchBackend, {\n    provide: FETCH_BACKEND,\n    useExisting: FetchBackend\n  }, {\n    provide: HttpBackend,\n    useExisting: FetchBackend\n  }]);\n}\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n * @deprecated Use withXsrfConfiguration({cookieName: 'XSRF-TOKEN', headerName: 'X-XSRF-TOKEN'}) as\n *     providers instead or `withNoXsrfProtection` if you want to disabled XSRF protection.\n */\nclass HttpClientXsrfModule {\n  /**\n   * Disable the default XSRF protection.\n   */\n  static disable() {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [withNoXsrfProtection().ɵproviders]\n    };\n  }\n  /**\n   * Configure XSRF protection.\n   * @param options An object that can specify either or both\n   * cookie name or header name.\n   * - Cookie name default is `XSRF-TOKEN`.\n   * - Header name default is `X-XSRF-TOKEN`.\n   *\n   */\n  static withOptions(options = {}) {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: withXsrfConfiguration(options).ɵproviders\n    };\n  }\n}\n_HttpClientXsrfModule = HttpClientXsrfModule;\n_defineProperty(HttpClientXsrfModule, \"\\u0275fac\", function _HttpClientXsrfModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HttpClientXsrfModule)();\n});\n_defineProperty(HttpClientXsrfModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _HttpClientXsrfModule\n}));\n_defineProperty(HttpClientXsrfModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [HttpXsrfInterceptor, {\n    provide: HTTP_INTERCEPTORS,\n    useExisting: HttpXsrfInterceptor,\n    multi: true\n  }, {\n    provide: HttpXsrfTokenExtractor,\n    useClass: HttpXsrfCookieExtractor\n  }, withXsrfConfiguration({\n    cookieName: XSRF_DEFAULT_COOKIE_NAME,\n    headerName: XSRF_DEFAULT_HEADER_NAME\n  }).ɵproviders, {\n    provide: XSRF_ENABLED,\n    useValue: true\n  }]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientXsrfModule, [{\n    type: NgModule,\n    args: [{\n      providers: [HttpXsrfInterceptor, {\n        provide: HTTP_INTERCEPTORS,\n        useExisting: HttpXsrfInterceptor,\n        multi: true\n      }, {\n        provide: HttpXsrfTokenExtractor,\n        useClass: HttpXsrfCookieExtractor\n      }, withXsrfConfiguration({\n        cookieName: XSRF_DEFAULT_COOKIE_NAME,\n        headerName: XSRF_DEFAULT_HEADER_NAME\n      }).ɵproviders, {\n        provide: XSRF_ENABLED,\n        useValue: true\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in DI token `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n * @deprecated use `provideHttpClient(withInterceptorsFromDi())` as providers instead\n */\nclass HttpClientModule {}\n_HttpClientModule = HttpClientModule;\n_defineProperty(HttpClientModule, \"\\u0275fac\", function _HttpClientModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HttpClientModule)();\n});\n_defineProperty(HttpClientModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _HttpClientModule\n}));\n_defineProperty(HttpClientModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [provideHttpClient(withInterceptorsFromDi())]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientModule, [{\n    type: NgModule,\n    args: [{\n      /**\n       * Configures the dependency injector where it is imported\n       * with supporting services for HTTP communications.\n       */\n      providers: [provideHttpClient(withInterceptorsFromDi())]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n * @deprecated `withJsonpSupport()` as providers instead\n */\nclass HttpClientJsonpModule {}\n_HttpClientJsonpModule = HttpClientJsonpModule;\n_defineProperty(HttpClientJsonpModule, \"\\u0275fac\", function _HttpClientJsonpModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HttpClientJsonpModule)();\n});\n_defineProperty(HttpClientJsonpModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _HttpClientJsonpModule\n}));\n_defineProperty(HttpClientJsonpModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [withJsonpSupport().ɵproviders]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientJsonpModule, [{\n    type: NgModule,\n    args: [{\n      providers: [withJsonpSupport().ɵproviders]\n    }]\n  }], null, null);\n})();\nexport { FetchBackend, HTTP_INTERCEPTORS, HTTP_ROOT_INTERCEPTOR_FNS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpInterceptorHandler, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, REQUESTS_CONTRIBUTE_TO_STABILITY, provideHttpClient, withFetch, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration };", "map": {"version": 3, "names": ["i0", "ɵRuntimeError", "_RuntimeError", "Injectable", "inject", "NgZone", "DestroyRef", "InjectionToken", "ɵPendingTasksInternal", "_PendingTasksInternal", "PLATFORM_ID", "ɵConsole", "_Console", "ɵformatRuntimeError", "_formatRuntimeError", "runInInjectionContext", "Inject", "makeEnvironmentProviders", "NgModule", "concatMap", "filter", "map", "finalize", "switchMap", "of", "Observable", "from", "isPlatformServer", "XhrFactory", "parseCookieValue", "DOCUMENT", "HttpHandler", "HttpBackend", "HttpHeaders", "constructor", "headers", "_defineProperty", "Map", "lazyInit", "split", "for<PERSON>ach", "line", "index", "indexOf", "name", "slice", "value", "trim", "addHeaderEntry", "Headers", "ngDevMode", "assertValidHeaders", "Object", "entries", "values", "setHeaderEntries", "has", "init", "toLowerCase", "get", "length", "keys", "Array", "normalizedNames", "getAll", "append", "clone", "op", "set", "delete", "maybeSetNormalizedName", "lcName", "copyFrom", "lazyUpdate", "update", "applyUpdate", "other", "key", "concat", "base", "undefined", "push", "toDelete", "existing", "headerValues", "isArray", "toString", "fn", "Error", "HttpUrlEncodingCodec", "encodeKey", "standardEncoding", "encodeValue", "decodeKey", "decodeURIComponent", "decodeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rawParams", "codec", "params", "replace", "param", "eqIdx", "val", "list", "STANDARD_ENCODING_REGEX", "STANDARD_ENCODING_REPLACEMENTS", "v", "encodeURIComponent", "s", "t", "_STANDARD_ENCODING_RE", "valueToString", "HttpParams", "options", "encoder", "fromString", "fromObject", "res", "appendAll", "updates", "_value", "<PERSON><PERSON><PERSON>", "join", "cloneFrom", "idx", "splice", "HttpContextToken", "defaultValue", "HttpContext", "token", "mightHaveBody", "method", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isBlob", "Blob", "isFormData", "FormData", "isUrlSearchParams", "URLSearchParams", "CONTENT_TYPE_HEADER", "ACCEPT_HEADER", "X_REQUEST_URL_HEADER", "TEXT_CONTENT_TYPE", "JSON_CONTENT_TYPE", "ACCEPT_HEADER_VALUE", "HttpRequest", "url", "third", "fourth", "_this$headers", "_this$context", "toUpperCase", "body", "reportProgress", "withCredentials", "responseType", "context", "transferCache", "urlWithParams", "qIdx", "sep", "serializeBody", "JSON", "stringify", "detectContentTypeHeader", "type", "_update$transferCache", "_update$withCredentia", "_update$reportProgres", "_update$context", "setHeaders", "reduce", "setParams", "HttpEventType", "HttpResponseBase", "defaultStatus", "defaultStatusText", "status", "statusText", "ok", "HttpHeaderResponse", "ResponseHeader", "HttpResponse", "Response", "HttpErrorResponse", "message", "error", "HTTP_STATUS_CODE_OK", "HTTP_STATUS_CODE_NO_CONTENT", "HttpStatusCode", "addBody", "observe", "HttpClient", "handler", "request", "first", "req", "events$", "pipe", "handle", "res$", "event", "head", "jsonp", "callback<PERSON><PERSON><PERSON>", "patch", "post", "put", "_HttpClient", "_HttpClient_Factory", "__ngFactoryType__", "ɵɵinject", "ɵɵdefineInjectable", "factory", "ɵfac", "ɵsetClassMetadata", "XSSI_PREFIX$1", "getResponseUrl$1", "response", "xRequestUrl", "toLocaleLowerCase", "FETCH_BACKEND", "FetchBackend", "_inject$fetch", "_inject", "FetchFactory", "optional", "fetch", "args", "globalThis", "destroyRef", "onDestroy", "destroyed", "observer", "aborter", "AbortController", "doRequest", "signal", "then", "noop", "abort", "_this", "_asyncToGenerator", "_getResponseUrl$", "createRequestInit", "fetchPromise", "ngZone", "runOutsideAngular", "fetchImpl", "_objectSpread", "silenceSuperfluousUnhandledPromiseRejection", "next", "<PERSON><PERSON>", "_error$status", "contentLength", "chunks", "reader", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "partialText", "reqZone", "Zone", "current", "canceled", "cancel", "done", "read", "TextDecoder", "decode", "stream", "DownloadProgress", "total", "loaded", "run", "complete", "chunksAll", "concatChunks", "_response$headers$get", "contentType", "parseBody", "_getResponseUrl$2", "binContent", "text", "parse", "buffer", "credentials", "detectedType", "totalLength", "Uint8Array", "position", "chunk", "_FetchBackend", "_FetchBackend_Factory", "promise", "interceptorChainEndFn", "finalHandlerFn", "adaptLegacyInterceptorToChain", "chainTailFn", "interceptor", "initialRequest", "intercept", "downstreamRequest", "chainedInterceptorFn", "interceptorFn", "injector", "HTTP_INTERCEPTORS", "HTTP_INTERCEPTOR_FNS", "HTTP_ROOT_INTERCEPTOR_FNS", "REQUESTS_CONTRIBUTE_TO_STABILITY", "providedIn", "legacyInterceptorFnFactory", "chain", "_inject2", "interceptors", "reduceRight", "pendingTasks", "contributeToStability", "taskId", "add", "remove", "fetchBackendWarningDisplayed", "HttpInterceptorHandler", "backend", "isServer", "isTestingBackend", "warn", "dedupedInterceptorFns", "Set", "nextSequencedFn", "_HttpInterceptorHandler", "_HttpInterceptorHandler_Factory", "EnvironmentInjector", "nextRequestId", "foreignDocument", "JSONP_ERR_NO_CALLBACK", "JSONP_ERR_WRONG_METHOD", "JSONP_ERR_WRONG_RESPONSE_TYPE", "JSONP_ERR_HEADERS_NOT_SUPPORTED", "JsonpCallbackContext", "jsonpCallbackContext", "window", "JsonpClientBackend", "callbackMap", "document", "Promise", "resolve", "nextCallback", "callback", "node", "createElement", "src", "finished", "data", "cleanup", "removeEventListener", "onLoad", "onError", "resolvedPromise", "addEventListener", "append<PERSON><PERSON><PERSON>", "removeListeners", "script", "implementation", "createHTMLDocument", "adoptNode", "_JsonpClientBackend", "_JsonpClientBackend_Factory", "decorators", "jsonpInterceptorFn", "JsonpInterceptor", "_JsonpInterceptor", "_JsonpInterceptor_Factory", "XSSI_PREFIX", "X_REQUEST_URL_REGEXP", "RegExp", "getResponseUrl", "xhr", "responseURL", "test", "getAllResponseHeaders", "getResponseHeader", "HttpXhrBackend", "xhrFactory", "source", "ɵloadImpl", "build", "open", "setRequestHeader", "reqBody", "headerResponse", "partialFromXhr", "responseText", "originalBody", "sentHeaders", "onDownProgress", "progressEvent", "lengthComputable", "onUpProgress", "progress", "UploadProgress", "upload", "send", "readyState", "DONE", "_HttpXhrBackend", "_HttpXhrBackend_Factory", "XSRF_ENABLED", "XSRF_DEFAULT_COOKIE_NAME", "XSRF_COOKIE_NAME", "XSRF_DEFAULT_HEADER_NAME", "XSRF_HEADER_NAME", "HttpXsrfTokenExtractor", "HttpXsrfCookieExtractor", "doc", "cookieName", "getToken", "ngServerMode", "cookieString", "cookie", "lastCookieString", "parseCount", "lastToken", "_HttpXsrfCookieExtractor", "_HttpXsrfCookieExtractor_Factory", "xsrfInterceptorFn", "lcUrl", "startsWith", "headerName", "HttpXsrfInterceptor", "_HttpXsrfInterceptor", "_HttpXsrfInterceptor_Factory", "HttpFeatureKind", "makeHttpFeature", "kind", "providers", "ɵkind", "ɵproviders", "provideHttpClient", "features", "featureKinds", "f", "NoXsrfProtection", "CustomXsrfConfiguration", "provide", "useExisting", "useFactory", "_inject3", "useValue", "multi", "useClass", "feature", "withInterceptors", "interceptorFns", "Interceptors", "LEGACY_INTERCEPTOR_FN", "withInterceptorsFromDi", "LegacyInterceptors", "withXsrfConfiguration", "withNoXsrfProtection", "withJsonpSupport", "JsonpSupport", "withRequestsMadeViaParent", "RequestsMadeViaParent", "handlerFromParent", "skipSelf", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>tch", "HttpClientXsrfModule", "disable", "ngModule", "withOptions", "_HttpClientXsrfModule", "_HttpClientXsrfModule_Factory", "ɵɵdefineNgModule", "ɵɵdefineInjector", "HttpClientModule", "_HttpClientModule", "_HttpClientModule_Factory", "HttpClientJsonpModule", "_HttpClientJsonpModule", "_HttpClientJsonpModule_Factory"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/common/fesm2022/module-z3bvLlVg.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, Injectable, inject, NgZone, DestroyRef, InjectionToken, ɵPendingTasksInternal as _PendingTasksInternal, PLATFORM_ID, ɵConsole as _Console, ɵformatRuntimeError as _formatRuntimeError, runInInjectionContext, Inject, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { concatMap, filter, map, finalize, switchMap } from 'rxjs/operators';\nimport { of, Observable, from } from 'rxjs';\nimport { isPlatformServer, XhrFactory, parseCookieValue } from './xhr-BfNfxNDv.mjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {\n}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {\n}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n    /**\n     * Internal map of lowercase header names to values.\n     */\n    headers;\n    /**\n     * Internal map of lowercased header names to the normalized\n     * form of the name (the form seen first).\n     */\n    normalizedNames = new Map();\n    /**\n     * Complete the lazy initialization of this object (needed before reading).\n     */\n    lazyInit;\n    /**\n     * Queued updates to be materialized the next initialization.\n     */\n    lazyUpdate = null;\n    /**  Constructs a new HTTP header object with the given values.*/\n    constructor(headers) {\n        if (!headers) {\n            this.headers = new Map();\n        }\n        else if (typeof headers === 'string') {\n            this.lazyInit = () => {\n                this.headers = new Map();\n                headers.split('\\n').forEach((line) => {\n                    const index = line.indexOf(':');\n                    if (index > 0) {\n                        const name = line.slice(0, index);\n                        const value = line.slice(index + 1).trim();\n                        this.addHeaderEntry(name, value);\n                    }\n                });\n            };\n        }\n        else if (typeof Headers !== 'undefined' && headers instanceof Headers) {\n            this.headers = new Map();\n            headers.forEach((value, name) => {\n                this.addHeaderEntry(name, value);\n            });\n        }\n        else {\n            this.lazyInit = () => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    assertValidHeaders(headers);\n                }\n                this.headers = new Map();\n                Object.entries(headers).forEach(([name, values]) => {\n                    this.setHeaderEntries(name, values);\n                });\n            };\n        }\n    }\n    /**\n     * Checks for existence of a given header.\n     *\n     * @param name The header name to check for existence.\n     *\n     * @returns True if the header exists, false otherwise.\n     */\n    has(name) {\n        this.init();\n        return this.headers.has(name.toLowerCase());\n    }\n    /**\n     * Retrieves the first value of a given header.\n     *\n     * @param name The header name.\n     *\n     * @returns The value string if the header exists, null otherwise\n     */\n    get(name) {\n        this.init();\n        const values = this.headers.get(name.toLowerCase());\n        return values && values.length > 0 ? values[0] : null;\n    }\n    /**\n     * Retrieves the names of the headers.\n     *\n     * @returns A list of header names.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.normalizedNames.values());\n    }\n    /**\n     * Retrieves a list of values for a given header.\n     *\n     * @param name The header name from which to retrieve values.\n     *\n     * @returns A string of values if the header exists, null otherwise.\n     */\n    getAll(name) {\n        this.init();\n        return this.headers.get(name.toLowerCase()) || null;\n    }\n    /**\n     * Appends a new value to the existing set of values for a header\n     * and returns them in a clone of the original instance.\n     *\n     * @param name The header name for which to append the values.\n     * @param value The value to append.\n     *\n     * @returns A clone of the HTTP headers object with the value appended to the given header.\n     */\n    append(name, value) {\n        return this.clone({ name, value, op: 'a' });\n    }\n    /**\n     * Sets or modifies a value for a given header in a clone of the original instance.\n     * If the header already exists, its value is replaced with the given value\n     * in the returned object.\n     *\n     * @param name The header name.\n     * @param value The value or values to set or override for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the newly set header value.\n     */\n    set(name, value) {\n        return this.clone({ name, value, op: 's' });\n    }\n    /**\n     * Deletes values for a given header in a clone of the original instance.\n     *\n     * @param name The header name.\n     * @param value The value or values to delete for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the given value deleted.\n     */\n    delete(name, value) {\n        return this.clone({ name, value, op: 'd' });\n    }\n    maybeSetNormalizedName(name, lcName) {\n        if (!this.normalizedNames.has(lcName)) {\n            this.normalizedNames.set(lcName, name);\n        }\n    }\n    init() {\n        if (!!this.lazyInit) {\n            if (this.lazyInit instanceof HttpHeaders) {\n                this.copyFrom(this.lazyInit);\n            }\n            else {\n                this.lazyInit();\n            }\n            this.lazyInit = null;\n            if (!!this.lazyUpdate) {\n                this.lazyUpdate.forEach((update) => this.applyUpdate(update));\n                this.lazyUpdate = null;\n            }\n        }\n    }\n    copyFrom(other) {\n        other.init();\n        Array.from(other.headers.keys()).forEach((key) => {\n            this.headers.set(key, other.headers.get(key));\n            this.normalizedNames.set(key, other.normalizedNames.get(key));\n        });\n    }\n    clone(update) {\n        const clone = new HttpHeaders();\n        clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n        clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n        return clone;\n    }\n    applyUpdate(update) {\n        const key = update.name.toLowerCase();\n        switch (update.op) {\n            case 'a':\n            case 's':\n                let value = update.value;\n                if (typeof value === 'string') {\n                    value = [value];\n                }\n                if (value.length === 0) {\n                    return;\n                }\n                this.maybeSetNormalizedName(update.name, key);\n                const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n                base.push(...value);\n                this.headers.set(key, base);\n                break;\n            case 'd':\n                const toDelete = update.value;\n                if (!toDelete) {\n                    this.headers.delete(key);\n                    this.normalizedNames.delete(key);\n                }\n                else {\n                    let existing = this.headers.get(key);\n                    if (!existing) {\n                        return;\n                    }\n                    existing = existing.filter((value) => toDelete.indexOf(value) === -1);\n                    if (existing.length === 0) {\n                        this.headers.delete(key);\n                        this.normalizedNames.delete(key);\n                    }\n                    else {\n                        this.headers.set(key, existing);\n                    }\n                }\n                break;\n        }\n    }\n    addHeaderEntry(name, value) {\n        const key = name.toLowerCase();\n        this.maybeSetNormalizedName(name, key);\n        if (this.headers.has(key)) {\n            this.headers.get(key).push(value);\n        }\n        else {\n            this.headers.set(key, [value]);\n        }\n    }\n    setHeaderEntries(name, values) {\n        const headerValues = (Array.isArray(values) ? values : [values]).map((value) => value.toString());\n        const key = name.toLowerCase();\n        this.headers.set(key, headerValues);\n        this.maybeSetNormalizedName(name, key);\n    }\n    /**\n     * @internal\n     */\n    forEach(fn) {\n        this.init();\n        Array.from(this.normalizedNames.keys()).forEach((key) => fn(this.normalizedNames.get(key), this.headers.get(key)));\n    }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n    for (const [key, value] of Object.entries(headers)) {\n        if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n            throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` +\n                `Expecting either a string, a number or an array, but got: \\`${value}\\`.`);\n        }\n    }\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n    /**\n     * Encodes a key name for a URL parameter or query-string.\n     * @param key The key name.\n     * @returns The encoded key name.\n     */\n    encodeKey(key) {\n        return standardEncoding(key);\n    }\n    /**\n     * Encodes the value of a URL parameter or query-string.\n     * @param value The value.\n     * @returns The encoded value.\n     */\n    encodeValue(value) {\n        return standardEncoding(value);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string key.\n     * @param key The encoded key name.\n     * @returns The decoded key name.\n     */\n    decodeKey(key) {\n        return decodeURIComponent(key);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string value.\n     * @param value The encoded value.\n     * @returns The decoded value.\n     */\n    decodeValue(value) {\n        return decodeURIComponent(value);\n    }\n}\nfunction paramParser(rawParams, codec) {\n    const map = new Map();\n    if (rawParams.length > 0) {\n        // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n        // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n        // may start with the `?` char, so we strip it if it's present.\n        const params = rawParams.replace(/^\\?/, '').split('&');\n        params.forEach((param) => {\n            const eqIdx = param.indexOf('=');\n            const [key, val] = eqIdx == -1\n                ? [codec.decodeKey(param), '']\n                : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n            const list = map.get(key) || [];\n            list.push(val);\n            map.set(key, list);\n        });\n    }\n    return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n    '40': '@',\n    '3A': ':',\n    '24': '$',\n    '2C': ',',\n    '3B': ';',\n    '3D': '=',\n    '3F': '?',\n    '2F': '/',\n};\nfunction standardEncoding(v) {\n    return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n    return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n    map;\n    encoder;\n    updates = null;\n    cloneFrom = null;\n    constructor(options = {}) {\n        this.encoder = options.encoder || new HttpUrlEncodingCodec();\n        if (options.fromString) {\n            if (options.fromObject) {\n                throw new _RuntimeError(2805 /* RuntimeErrorCode.CANNOT_SPECIFY_BOTH_FROM_STRING_AND_FROM_OBJECT */, ngDevMode && 'Cannot specify both fromString and fromObject.');\n            }\n            this.map = paramParser(options.fromString, this.encoder);\n        }\n        else if (!!options.fromObject) {\n            this.map = new Map();\n            Object.keys(options.fromObject).forEach((key) => {\n                const value = options.fromObject[key];\n                // convert the values to strings\n                const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n                this.map.set(key, values);\n            });\n        }\n        else {\n            this.map = null;\n        }\n    }\n    /**\n     * Reports whether the body includes one or more values for a given parameter.\n     * @param param The parameter name.\n     * @returns True if the parameter has one or more values,\n     * false if it has no value or is not present.\n     */\n    has(param) {\n        this.init();\n        return this.map.has(param);\n    }\n    /**\n     * Retrieves the first value for a parameter.\n     * @param param The parameter name.\n     * @returns The first value of the given parameter,\n     * or `null` if the parameter is not present.\n     */\n    get(param) {\n        this.init();\n        const res = this.map.get(param);\n        return !!res ? res[0] : null;\n    }\n    /**\n     * Retrieves all values for a  parameter.\n     * @param param The parameter name.\n     * @returns All values in a string array,\n     * or `null` if the parameter not present.\n     */\n    getAll(param) {\n        this.init();\n        return this.map.get(param) || null;\n    }\n    /**\n     * Retrieves all the parameters for this body.\n     * @returns The parameter names in a string array.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.map.keys());\n    }\n    /**\n     * Appends a new value to existing values for a parameter.\n     * @param param The parameter name.\n     * @param value The new value to add.\n     * @return A new body with the appended value.\n     */\n    append(param, value) {\n        return this.clone({ param, value, op: 'a' });\n    }\n    /**\n     * Constructs a new body with appended values for the given parameter name.\n     * @param params parameters and values\n     * @return A new body with the new value.\n     */\n    appendAll(params) {\n        const updates = [];\n        Object.keys(params).forEach((param) => {\n            const value = params[param];\n            if (Array.isArray(value)) {\n                value.forEach((_value) => {\n                    updates.push({ param, value: _value, op: 'a' });\n                });\n            }\n            else {\n                updates.push({ param, value: value, op: 'a' });\n            }\n        });\n        return this.clone(updates);\n    }\n    /**\n     * Replaces the value for a parameter.\n     * @param param The parameter name.\n     * @param value The new value.\n     * @return A new body with the new value.\n     */\n    set(param, value) {\n        return this.clone({ param, value, op: 's' });\n    }\n    /**\n     * Removes a given value or all values from a parameter.\n     * @param param The parameter name.\n     * @param value The value to remove, if provided.\n     * @return A new body with the given value removed, or with all values\n     * removed if no value is specified.\n     */\n    delete(param, value) {\n        return this.clone({ param, value, op: 'd' });\n    }\n    /**\n     * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n     * separated by `&`s.\n     */\n    toString() {\n        this.init();\n        return (this.keys()\n            .map((key) => {\n            const eKey = this.encoder.encodeKey(key);\n            // `a: ['1']` produces `'a=1'`\n            // `b: []` produces `''`\n            // `c: ['1', '2']` produces `'c=1&c=2'`\n            return this.map.get(key)\n                .map((value) => eKey + '=' + this.encoder.encodeValue(value))\n                .join('&');\n        })\n            // filter out empty values because `b: []` produces `''`\n            // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n            .filter((param) => param !== '')\n            .join('&'));\n    }\n    clone(update) {\n        const clone = new HttpParams({ encoder: this.encoder });\n        clone.cloneFrom = this.cloneFrom || this;\n        clone.updates = (this.updates || []).concat(update);\n        return clone;\n    }\n    init() {\n        if (this.map === null) {\n            this.map = new Map();\n        }\n        if (this.cloneFrom !== null) {\n            this.cloneFrom.init();\n            this.cloneFrom.keys().forEach((key) => this.map.set(key, this.cloneFrom.map.get(key)));\n            this.updates.forEach((update) => {\n                switch (update.op) {\n                    case 'a':\n                    case 's':\n                        const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n                        base.push(valueToString(update.value));\n                        this.map.set(update.param, base);\n                        break;\n                    case 'd':\n                        if (update.value !== undefined) {\n                            let base = this.map.get(update.param) || [];\n                            const idx = base.indexOf(valueToString(update.value));\n                            if (idx !== -1) {\n                                base.splice(idx, 1);\n                            }\n                            if (base.length > 0) {\n                                this.map.set(update.param, base);\n                            }\n                            else {\n                                this.map.delete(update.param);\n                            }\n                        }\n                        else {\n                            this.map.delete(update.param);\n                            break;\n                        }\n                }\n            });\n            this.cloneFrom = this.updates = null;\n        }\n    }\n}\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n    defaultValue;\n    constructor(defaultValue) {\n        this.defaultValue = defaultValue;\n    }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```ts\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n    map = new Map();\n    /**\n     * Store a value in the context. If a value is already present it will be overwritten.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     * @param value The value to store.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    set(token, value) {\n        this.map.set(token, value);\n        return this;\n    }\n    /**\n     * Retrieve the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns The stored value or default if one is defined.\n     */\n    get(token) {\n        if (!this.map.has(token)) {\n            this.map.set(token, token.defaultValue());\n        }\n        return this.map.get(token);\n    }\n    /**\n     * Delete the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    delete(token) {\n        this.map.delete(token);\n        return this;\n    }\n    /**\n     * Checks for existence of a given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns True if the token exists, false otherwise.\n     */\n    has(token) {\n        return this.map.has(token);\n    }\n    /**\n     * @returns a list of tokens currently stored in the context.\n     */\n    keys() {\n        return this.map.keys();\n    }\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n    switch (method) {\n        case 'DELETE':\n        case 'GET':\n        case 'HEAD':\n        case 'OPTIONS':\n        case 'JSONP':\n            return false;\n        default:\n            return true;\n    }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n    return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n    return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n    return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n    return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * `Content-Type` is an HTTP header used to indicate the media type\n * (also known as MIME type) of the resource being sent to the client\n * or received from the server.\n */\nconst CONTENT_TYPE_HEADER = 'Content-Type';\n/**\n * The `Accept` header is an HTTP request header that indicates the media types\n * (or content types) the client is willing to receive from the server.\n */\nconst ACCEPT_HEADER = 'Accept';\n/**\n * `X-Request-URL` is a custom HTTP header used in older browser versions,\n * including Firefox (< 32), Chrome (< 37), Safari (< 8), and Internet Explorer,\n * to include the full URL of the request in cross-origin requests.\n */\nconst X_REQUEST_URL_HEADER = 'X-Request-URL';\n/**\n * `text/plain` is a content type used to indicate that the content being\n * sent is plain text with no special formatting or structured data\n * like HTML, XML, or JSON.\n */\nconst TEXT_CONTENT_TYPE = 'text/plain';\n/**\n * `application/json` is a content type used to indicate that the content\n * being sent is in the JSON format.\n */\nconst JSON_CONTENT_TYPE = 'application/json';\n/**\n * `application/json, text/plain, *\\/*` is a content negotiation string often seen in the\n * Accept header of HTTP requests. It indicates the types of content the client is willing\n * to accept from the server, with a preference for `application/json` and `text/plain`,\n * but also accepting any other type (*\\/*).\n */\nconst ACCEPT_HEADER_VALUE = `${JSON_CONTENT_TYPE}, ${TEXT_CONTENT_TYPE}, */*`;\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n    url;\n    /**\n     * The request body, or `null` if one isn't set.\n     *\n     * Bodies are not enforced to be immutable, as they can include a reference to any\n     * user-defined data type. However, interceptors should take care to preserve\n     * idempotence by treating them as such.\n     */\n    body = null;\n    /**\n     * Outgoing headers for this request.\n     */\n    headers;\n    /**\n     * Shared and mutable context that can be used by interceptors\n     */\n    context;\n    /**\n     * Whether this request should be made in a way that exposes progress events.\n     *\n     * Progress events are expensive (change detection runs on each event) and so\n     * they should only be requested if the consumer intends to monitor them.\n     *\n     * Note: The `FetchBackend` doesn't support progress report on uploads.\n     */\n    reportProgress = false;\n    /**\n     * Whether this request should be sent with outgoing credentials (cookies).\n     */\n    withCredentials = false;\n    /**\n     * The expected response type of the server.\n     *\n     * This is used to parse the response appropriately before returning it to\n     * the requestee.\n     */\n    responseType = 'json';\n    /**\n     * The outgoing HTTP request method.\n     */\n    method;\n    /**\n     * Outgoing URL parameters.\n     *\n     * To pass a string representation of HTTP parameters in the URL-query-string format,\n     * the `HttpParamsOptions`' `fromString` may be used. For example:\n     *\n     * ```ts\n     * new HttpParams({fromString: 'angular=awesome'})\n     * ```\n     */\n    params;\n    /**\n     * The outgoing URL with all URL parameters set.\n     */\n    urlWithParams;\n    /**\n     * The HttpTransferCache option for the request\n     */\n    transferCache;\n    constructor(method, url, third, fourth) {\n        this.url = url;\n        this.method = method.toUpperCase();\n        // Next, need to figure out which argument holds the HttpRequestInit\n        // options, if any.\n        let options;\n        // Check whether a body argument is expected. The only valid way to omit\n        // the body argument is to use a known no-body method like GET.\n        if (mightHaveBody(this.method) || !!fourth) {\n            // Body is the third argument, options are the fourth.\n            this.body = third !== undefined ? third : null;\n            options = fourth;\n        }\n        else {\n            // No body required, options are the third argument. The body stays null.\n            options = third;\n        }\n        // If options have been passed, interpret them.\n        if (options) {\n            // Normalize reportProgress and withCredentials.\n            this.reportProgress = !!options.reportProgress;\n            this.withCredentials = !!options.withCredentials;\n            // Override default response type of 'json' if one is provided.\n            if (!!options.responseType) {\n                this.responseType = options.responseType;\n            }\n            // Override headers if they're provided.\n            if (!!options.headers) {\n                this.headers = options.headers;\n            }\n            if (!!options.context) {\n                this.context = options.context;\n            }\n            if (!!options.params) {\n                this.params = options.params;\n            }\n            // We do want to assign transferCache even if it's falsy (false is valid value)\n            this.transferCache = options.transferCache;\n        }\n        // If no headers have been passed in, construct a new HttpHeaders instance.\n        this.headers ??= new HttpHeaders();\n        // If no context have been passed in, construct a new HttpContext instance.\n        this.context ??= new HttpContext();\n        // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n        if (!this.params) {\n            this.params = new HttpParams();\n            this.urlWithParams = url;\n        }\n        else {\n            // Encode the parameters to a string in preparation for inclusion in the URL.\n            const params = this.params.toString();\n            if (params.length === 0) {\n                // No parameters, the visible URL is just the URL given at creation time.\n                this.urlWithParams = url;\n            }\n            else {\n                // Does the URL already have query parameters? Look for '?'.\n                const qIdx = url.indexOf('?');\n                // There are 3 cases to handle:\n                // 1) No existing parameters -> append '?' followed by params.\n                // 2) '?' exists and is followed by existing query string ->\n                //    append '&' followed by params.\n                // 3) '?' exists at the end of the url -> append params directly.\n                // This basically amounts to determining the character, if any, with\n                // which to join the URL and parameters.\n                const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n                this.urlWithParams = url + sep + params;\n            }\n        }\n    }\n    /**\n     * Transform the free-form body into a serialized format suitable for\n     * transmission to the server.\n     */\n    serializeBody() {\n        // If no body is present, no need to serialize it.\n        if (this.body === null) {\n            return null;\n        }\n        // Check whether the body is already in a serialized form. If so,\n        // it can just be returned directly.\n        if (typeof this.body === 'string' ||\n            isArrayBuffer(this.body) ||\n            isBlob(this.body) ||\n            isFormData(this.body) ||\n            isUrlSearchParams(this.body)) {\n            return this.body;\n        }\n        // Check whether the body is an instance of HttpUrlEncodedParams.\n        if (this.body instanceof HttpParams) {\n            return this.body.toString();\n        }\n        // Check whether the body is an object or array, and serialize with JSON if so.\n        if (typeof this.body === 'object' ||\n            typeof this.body === 'boolean' ||\n            Array.isArray(this.body)) {\n            return JSON.stringify(this.body);\n        }\n        // Fall back on toString() for everything else.\n        return this.body.toString();\n    }\n    /**\n     * Examine the body and attempt to infer an appropriate MIME type\n     * for it.\n     *\n     * If no such type can be inferred, this method will return `null`.\n     */\n    detectContentTypeHeader() {\n        // An empty body has no content type.\n        if (this.body === null) {\n            return null;\n        }\n        // FormData bodies rely on the browser's content type assignment.\n        if (isFormData(this.body)) {\n            return null;\n        }\n        // Blobs usually have their own content type. If it doesn't, then\n        // no type can be inferred.\n        if (isBlob(this.body)) {\n            return this.body.type || null;\n        }\n        // Array buffers have unknown contents and thus no type can be inferred.\n        if (isArrayBuffer(this.body)) {\n            return null;\n        }\n        // Technically, strings could be a form of JSON data, but it's safe enough\n        // to assume they're plain strings.\n        if (typeof this.body === 'string') {\n            return TEXT_CONTENT_TYPE;\n        }\n        // `HttpUrlEncodedParams` has its own content-type.\n        if (this.body instanceof HttpParams) {\n            return 'application/x-www-form-urlencoded;charset=UTF-8';\n        }\n        // Arrays, objects, boolean and numbers will be encoded as JSON.\n        if (typeof this.body === 'object' ||\n            typeof this.body === 'number' ||\n            typeof this.body === 'boolean') {\n            return JSON_CONTENT_TYPE;\n        }\n        // No type could be inferred.\n        return null;\n    }\n    clone(update = {}) {\n        // For method, url, and responseType, take the current value unless\n        // it is overridden in the update hash.\n        const method = update.method || this.method;\n        const url = update.url || this.url;\n        const responseType = update.responseType || this.responseType;\n        // Carefully handle the transferCache to differentiate between\n        // `false` and `undefined` in the update args.\n        const transferCache = update.transferCache ?? this.transferCache;\n        // The body is somewhat special - a `null` value in update.body means\n        // whatever current body is present is being overridden with an empty\n        // body, whereas an `undefined` value in update.body implies no\n        // override.\n        const body = update.body !== undefined ? update.body : this.body;\n        // Carefully handle the boolean options to differentiate between\n        // `false` and `undefined` in the update args.\n        const withCredentials = update.withCredentials ?? this.withCredentials;\n        const reportProgress = update.reportProgress ?? this.reportProgress;\n        // Headers and params may be appended to if `setHeaders` or\n        // `setParams` are used.\n        let headers = update.headers || this.headers;\n        let params = update.params || this.params;\n        // Pass on context if needed\n        const context = update.context ?? this.context;\n        // Check whether the caller has asked to add headers.\n        if (update.setHeaders !== undefined) {\n            // Set every requested header.\n            headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n        }\n        // Check whether the caller has asked to set params.\n        if (update.setParams) {\n            // Set every requested param.\n            params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n        }\n        // Finally, construct the new HttpRequest using the pieces from above.\n        return new HttpRequest(method, url, body, {\n            params,\n            headers,\n            context,\n            reportProgress,\n            responseType,\n            withCredentials,\n            transferCache,\n        });\n    }\n}\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n    /**\n     * The request was sent out over the wire.\n     */\n    HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n    /**\n     * An upload progress event was received.\n     *\n     * Note: The `FetchBackend` doesn't support progress report on uploads.\n     */\n    HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n    /**\n     * The response status code and headers were received.\n     */\n    HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n    /**\n     * A download progress event was received.\n     */\n    HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n    /**\n     * The full response including the body was received.\n     */\n    HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n    /**\n     * A custom event from an interceptor or a backend.\n     */\n    HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n    /**\n     * All response headers.\n     */\n    headers;\n    /**\n     * Response status code.\n     */\n    status;\n    /**\n     * Textual description of response status code, defaults to OK.\n     *\n     * Do not depend on this.\n     */\n    statusText;\n    /**\n     * URL of the resource retrieved, or null if not available.\n     */\n    url;\n    /**\n     * Whether the status code falls in the 2xx range.\n     */\n    ok;\n    /**\n     * Type of the response, narrowed to either the full response or the header.\n     */\n    type;\n    /**\n     * Super-constructor for all responses.\n     *\n     * The single parameter accepted is an initialization hash. Any properties\n     * of the response passed there will override the default values.\n     */\n    constructor(init, defaultStatus = 200, defaultStatusText = 'OK') {\n        // If the hash has values passed, use them to initialize the response.\n        // Otherwise use the default values.\n        this.headers = init.headers || new HttpHeaders();\n        this.status = init.status !== undefined ? init.status : defaultStatus;\n        this.statusText = init.statusText || defaultStatusText;\n        this.url = init.url || null;\n        // Cache the ok value to avoid defining a getter.\n        this.ok = this.status >= 200 && this.status < 300;\n    }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n    /**\n     * Create a new `HttpHeaderResponse` with the given parameters.\n     */\n    constructor(init = {}) {\n        super(init);\n    }\n    type = HttpEventType.ResponseHeader;\n    /**\n     * Copy this `HttpHeaderResponse`, overriding its contents with the\n     * given parameter hash.\n     */\n    clone(update = {}) {\n        // Perform a straightforward initialization of the new HttpHeaderResponse,\n        // overriding the current parameters with new ones if given.\n        return new HttpHeaderResponse({\n            headers: update.headers || this.headers,\n            status: update.status !== undefined ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n    /**\n     * The response body, or `null` if one was not returned.\n     */\n    body;\n    /**\n     * Construct a new `HttpResponse`.\n     */\n    constructor(init = {}) {\n        super(init);\n        this.body = init.body !== undefined ? init.body : null;\n    }\n    type = HttpEventType.Response;\n    clone(update = {}) {\n        return new HttpResponse({\n            body: update.body !== undefined ? update.body : this.body,\n            headers: update.headers || this.headers,\n            status: update.status !== undefined ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n    name = 'HttpErrorResponse';\n    message;\n    error;\n    /**\n     * Errors are never okay, even when the status code is in the 2xx success range.\n     */\n    ok = false;\n    constructor(init) {\n        // Initialize with a default status of 0 / Unknown Error.\n        super(init, 0, 'Unknown Error');\n        // If the response was successful, then this was a parse error. Otherwise, it was\n        // a protocol-level failure of some sort. Either the request failed in transit\n        // or the server returned an unsuccessful status code.\n        if (this.status >= 200 && this.status < 300) {\n            this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n        }\n        else {\n            this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n        }\n        this.error = init.error || null;\n    }\n}\n/**\n * We use these constant to prevent pulling the whole HttpStatusCode enum\n * Those are the only ones referenced directly by the framework\n */\nconst HTTP_STATUS_CODE_OK = 200;\nconst HTTP_STATUS_CODE_NO_CONTENT = 204;\n/**\n * Http status codes.\n * As per https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml\n * @publicApi\n */\nvar HttpStatusCode;\n(function (HttpStatusCode) {\n    HttpStatusCode[HttpStatusCode[\"Continue\"] = 100] = \"Continue\";\n    HttpStatusCode[HttpStatusCode[\"SwitchingProtocols\"] = 101] = \"SwitchingProtocols\";\n    HttpStatusCode[HttpStatusCode[\"Processing\"] = 102] = \"Processing\";\n    HttpStatusCode[HttpStatusCode[\"EarlyHints\"] = 103] = \"EarlyHints\";\n    HttpStatusCode[HttpStatusCode[\"Ok\"] = 200] = \"Ok\";\n    HttpStatusCode[HttpStatusCode[\"Created\"] = 201] = \"Created\";\n    HttpStatusCode[HttpStatusCode[\"Accepted\"] = 202] = \"Accepted\";\n    HttpStatusCode[HttpStatusCode[\"NonAuthoritativeInformation\"] = 203] = \"NonAuthoritativeInformation\";\n    HttpStatusCode[HttpStatusCode[\"NoContent\"] = 204] = \"NoContent\";\n    HttpStatusCode[HttpStatusCode[\"ResetContent\"] = 205] = \"ResetContent\";\n    HttpStatusCode[HttpStatusCode[\"PartialContent\"] = 206] = \"PartialContent\";\n    HttpStatusCode[HttpStatusCode[\"MultiStatus\"] = 207] = \"MultiStatus\";\n    HttpStatusCode[HttpStatusCode[\"AlreadyReported\"] = 208] = \"AlreadyReported\";\n    HttpStatusCode[HttpStatusCode[\"ImUsed\"] = 226] = \"ImUsed\";\n    HttpStatusCode[HttpStatusCode[\"MultipleChoices\"] = 300] = \"MultipleChoices\";\n    HttpStatusCode[HttpStatusCode[\"MovedPermanently\"] = 301] = \"MovedPermanently\";\n    HttpStatusCode[HttpStatusCode[\"Found\"] = 302] = \"Found\";\n    HttpStatusCode[HttpStatusCode[\"SeeOther\"] = 303] = \"SeeOther\";\n    HttpStatusCode[HttpStatusCode[\"NotModified\"] = 304] = \"NotModified\";\n    HttpStatusCode[HttpStatusCode[\"UseProxy\"] = 305] = \"UseProxy\";\n    HttpStatusCode[HttpStatusCode[\"Unused\"] = 306] = \"Unused\";\n    HttpStatusCode[HttpStatusCode[\"TemporaryRedirect\"] = 307] = \"TemporaryRedirect\";\n    HttpStatusCode[HttpStatusCode[\"PermanentRedirect\"] = 308] = \"PermanentRedirect\";\n    HttpStatusCode[HttpStatusCode[\"BadRequest\"] = 400] = \"BadRequest\";\n    HttpStatusCode[HttpStatusCode[\"Unauthorized\"] = 401] = \"Unauthorized\";\n    HttpStatusCode[HttpStatusCode[\"PaymentRequired\"] = 402] = \"PaymentRequired\";\n    HttpStatusCode[HttpStatusCode[\"Forbidden\"] = 403] = \"Forbidden\";\n    HttpStatusCode[HttpStatusCode[\"NotFound\"] = 404] = \"NotFound\";\n    HttpStatusCode[HttpStatusCode[\"MethodNotAllowed\"] = 405] = \"MethodNotAllowed\";\n    HttpStatusCode[HttpStatusCode[\"NotAcceptable\"] = 406] = \"NotAcceptable\";\n    HttpStatusCode[HttpStatusCode[\"ProxyAuthenticationRequired\"] = 407] = \"ProxyAuthenticationRequired\";\n    HttpStatusCode[HttpStatusCode[\"RequestTimeout\"] = 408] = \"RequestTimeout\";\n    HttpStatusCode[HttpStatusCode[\"Conflict\"] = 409] = \"Conflict\";\n    HttpStatusCode[HttpStatusCode[\"Gone\"] = 410] = \"Gone\";\n    HttpStatusCode[HttpStatusCode[\"LengthRequired\"] = 411] = \"LengthRequired\";\n    HttpStatusCode[HttpStatusCode[\"PreconditionFailed\"] = 412] = \"PreconditionFailed\";\n    HttpStatusCode[HttpStatusCode[\"PayloadTooLarge\"] = 413] = \"PayloadTooLarge\";\n    HttpStatusCode[HttpStatusCode[\"UriTooLong\"] = 414] = \"UriTooLong\";\n    HttpStatusCode[HttpStatusCode[\"UnsupportedMediaType\"] = 415] = \"UnsupportedMediaType\";\n    HttpStatusCode[HttpStatusCode[\"RangeNotSatisfiable\"] = 416] = \"RangeNotSatisfiable\";\n    HttpStatusCode[HttpStatusCode[\"ExpectationFailed\"] = 417] = \"ExpectationFailed\";\n    HttpStatusCode[HttpStatusCode[\"ImATeapot\"] = 418] = \"ImATeapot\";\n    HttpStatusCode[HttpStatusCode[\"MisdirectedRequest\"] = 421] = \"MisdirectedRequest\";\n    HttpStatusCode[HttpStatusCode[\"UnprocessableEntity\"] = 422] = \"UnprocessableEntity\";\n    HttpStatusCode[HttpStatusCode[\"Locked\"] = 423] = \"Locked\";\n    HttpStatusCode[HttpStatusCode[\"FailedDependency\"] = 424] = \"FailedDependency\";\n    HttpStatusCode[HttpStatusCode[\"TooEarly\"] = 425] = \"TooEarly\";\n    HttpStatusCode[HttpStatusCode[\"UpgradeRequired\"] = 426] = \"UpgradeRequired\";\n    HttpStatusCode[HttpStatusCode[\"PreconditionRequired\"] = 428] = \"PreconditionRequired\";\n    HttpStatusCode[HttpStatusCode[\"TooManyRequests\"] = 429] = \"TooManyRequests\";\n    HttpStatusCode[HttpStatusCode[\"RequestHeaderFieldsTooLarge\"] = 431] = \"RequestHeaderFieldsTooLarge\";\n    HttpStatusCode[HttpStatusCode[\"UnavailableForLegalReasons\"] = 451] = \"UnavailableForLegalReasons\";\n    HttpStatusCode[HttpStatusCode[\"InternalServerError\"] = 500] = \"InternalServerError\";\n    HttpStatusCode[HttpStatusCode[\"NotImplemented\"] = 501] = \"NotImplemented\";\n    HttpStatusCode[HttpStatusCode[\"BadGateway\"] = 502] = \"BadGateway\";\n    HttpStatusCode[HttpStatusCode[\"ServiceUnavailable\"] = 503] = \"ServiceUnavailable\";\n    HttpStatusCode[HttpStatusCode[\"GatewayTimeout\"] = 504] = \"GatewayTimeout\";\n    HttpStatusCode[HttpStatusCode[\"HttpVersionNotSupported\"] = 505] = \"HttpVersionNotSupported\";\n    HttpStatusCode[HttpStatusCode[\"VariantAlsoNegotiates\"] = 506] = \"VariantAlsoNegotiates\";\n    HttpStatusCode[HttpStatusCode[\"InsufficientStorage\"] = 507] = \"InsufficientStorage\";\n    HttpStatusCode[HttpStatusCode[\"LoopDetected\"] = 508] = \"LoopDetected\";\n    HttpStatusCode[HttpStatusCode[\"NotExtended\"] = 510] = \"NotExtended\";\n    HttpStatusCode[HttpStatusCode[\"NetworkAuthenticationRequired\"] = 511] = \"NetworkAuthenticationRequired\";\n})(HttpStatusCode || (HttpStatusCode = {}));\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n    return {\n        body,\n        headers: options.headers,\n        context: options.context,\n        observe: options.observe,\n        params: options.params,\n        reportProgress: options.reportProgress,\n        responseType: options.responseType,\n        withCredentials: options.withCredentials,\n        transferCache: options.transferCache,\n    };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n * @usageNotes\n *\n * ### HTTP Request Example\n *\n * ```ts\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```ts\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```ts\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```ts\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n    handler;\n    constructor(handler) {\n        this.handler = handler;\n    }\n    /**\n     * Constructs an observable for a generic HTTP request that, when subscribed,\n     * fires the request through the chain of registered interceptors and on to the\n     * server.\n     *\n     * You can pass an `HttpRequest` directly as the only parameter. In this case,\n     * the call returns an observable of the raw `HttpEvent` stream.\n     *\n     * Alternatively you can pass an HTTP method as the first parameter,\n     * a URL string as the second, and an options hash containing the request body as the third.\n     * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n     * type of returned observable.\n     *   * The `responseType` value determines how a successful response body is parsed.\n     *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n     * object as a type parameter to the call.\n     *\n     * The `observe` value determines the return type, according to what you are interested in\n     * observing.\n     *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n     * progress events by default.\n     *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n     * where the `T` parameter depends on the `responseType` and any optionally provided type\n     * parameter.\n     *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n     *\n     */\n    request(first, url, options = {}) {\n        let req;\n        // First, check whether the primary argument is an instance of `HttpRequest`.\n        if (first instanceof HttpRequest) {\n            // It is. The other arguments must be undefined (per the signatures) and can be\n            // ignored.\n            req = first;\n        }\n        else {\n            // It's a string, so it represents a URL. Construct a request based on it,\n            // and incorporate the remaining arguments (assuming `GET` unless a method is\n            // provided.\n            // Figure out the headers.\n            let headers = undefined;\n            if (options.headers instanceof HttpHeaders) {\n                headers = options.headers;\n            }\n            else {\n                headers = new HttpHeaders(options.headers);\n            }\n            // Sort out parameters.\n            let params = undefined;\n            if (!!options.params) {\n                if (options.params instanceof HttpParams) {\n                    params = options.params;\n                }\n                else {\n                    params = new HttpParams({ fromObject: options.params });\n                }\n            }\n            // Construct the request.\n            req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n                headers,\n                context: options.context,\n                params,\n                reportProgress: options.reportProgress,\n                // By default, JSON is assumed to be returned for all calls.\n                responseType: options.responseType || 'json',\n                withCredentials: options.withCredentials,\n                transferCache: options.transferCache,\n            });\n        }\n        // Start with an Observable.of() the initial request, and run the handler (which\n        // includes all interceptors) inside a concatMap(). This way, the handler runs\n        // inside an Observable chain, which causes interceptors to be re-run on every\n        // subscription (this also makes retries re-run the handler, including interceptors).\n        const events$ = of(req).pipe(concatMap((req) => this.handler.handle(req)));\n        // If coming via the API signature which accepts a previously constructed HttpRequest,\n        // the only option is to get the event stream. Otherwise, return the event stream if\n        // that is what was requested.\n        if (first instanceof HttpRequest || options.observe === 'events') {\n            return events$;\n        }\n        // The requested stream contains either the full response or the body. In either\n        // case, the first step is to filter the event stream to extract a stream of\n        // responses(s).\n        const res$ = (events$.pipe(filter((event) => event instanceof HttpResponse)));\n        // Decide which stream to return.\n        switch (options.observe || 'body') {\n            case 'body':\n                // The requested stream is the body. Map the response stream to the response\n                // body. This could be done more simply, but a misbehaving interceptor might\n                // transform the response body into a different format and ignore the requested\n                // responseType. Guard against this by validating that the response is of the\n                // requested type.\n                switch (req.responseType) {\n                    case 'arraybuffer':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is an ArrayBuffer.\n                            if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                                throw new _RuntimeError(2806 /* RuntimeErrorCode.RESPONSE_IS_NOT_AN_ARRAY_BUFFER */, ngDevMode && 'Response is not an ArrayBuffer.');\n                            }\n                            return res.body;\n                        }));\n                    case 'blob':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a Blob.\n                            if (res.body !== null && !(res.body instanceof Blob)) {\n                                throw new _RuntimeError(2807 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_BLOB */, ngDevMode && 'Response is not a Blob.');\n                            }\n                            return res.body;\n                        }));\n                    case 'text':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a string.\n                            if (res.body !== null && typeof res.body !== 'string') {\n                                throw new _RuntimeError(2808 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_STRING */, ngDevMode && 'Response is not a string.');\n                            }\n                            return res.body;\n                        }));\n                    case 'json':\n                    default:\n                        // No validation needed for JSON responses, as they can be of any type.\n                        return res$.pipe(map((res) => res.body));\n                }\n            case 'response':\n                // The response stream was requested directly, so return it.\n                return res$;\n            default:\n                // Guard against new future observe types being added.\n                throw new _RuntimeError(2809 /* RuntimeErrorCode.UNHANDLED_OBSERVE_TYPE */, ngDevMode && `Unreachable: unhandled observe type ${options.observe}}`);\n        }\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `DELETE` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     *\n     * @param url     The endpoint URL.\n     * @param options The HTTP options to send with the request.\n     *\n     */\n    delete(url, options = {}) {\n        return this.request('DELETE', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `GET` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    get(url, options = {}) {\n        return this.request('GET', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `HEAD` request to execute on the server. The `HEAD` method returns\n     * meta information about the resource without transferring the\n     * resource itself. See the individual overloads for\n     * details on the return type.\n     */\n    head(url, options = {}) {\n        return this.request('HEAD', url, options);\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes a request with the special method\n     * `JSONP` to be dispatched via the interceptor pipeline.\n     * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n     * API endpoints that don't support newer,\n     * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n     * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n     * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n     * application making the request.\n     * The endpoint API must support JSONP callback for JSONP requests to work.\n     * The resource API returns the JSON response wrapped in a callback function.\n     * You can pass the callback function name as one of the query parameters.\n     * Note that JSONP requests can only be used with `GET` requests.\n     *\n     * @param url The resource URL.\n     * @param callbackParam The callback function name.\n     *\n     */\n    jsonp(url, callbackParam) {\n        return this.request('JSONP', url, {\n            params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n            observe: 'body',\n            responseType: 'json',\n        });\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes the configured\n     * `OPTIONS` request to execute on the server. This method allows the client\n     * to determine the supported HTTP methods and other capabilities of an endpoint,\n     * without implying a resource action. See the individual overloads for\n     * details on the return type.\n     */\n    options(url, options = {}) {\n        return this.request('OPTIONS', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PATCH` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    patch(url, body, options = {}) {\n        return this.request('PATCH', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `POST` request to execute on the server. The server responds with the location of\n     * the replaced resource. See the individual overloads for\n     * details on the return type.\n     */\n    post(url, body, options = {}) {\n        return this.request('POST', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n     * with a new set of values.\n     * See the individual overloads for details on the return type.\n     */\n    put(url, body, options = {}) {\n        return this.request('PUT', url, addBody(options, body));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClient, deps: [{ token: HttpHandler }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClient });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClient, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: HttpHandler }] });\n\nconst XSSI_PREFIX$1 = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * response url or the X-Request-URL header.\n */\nfunction getResponseUrl$1(response) {\n    if (response.url) {\n        return response.url;\n    }\n    // stored as lowercase in the map\n    const xRequestUrl = X_REQUEST_URL_HEADER.toLocaleLowerCase();\n    return response.headers.get(xRequestUrl);\n}\n/**\n * An internal injection token to reference `FetchBackend` implementation\n * in a tree-shakable way.\n */\nconst FETCH_BACKEND = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'FETCH_BACKEND' : '');\n/**\n * Uses `fetch` to send requests to a backend server.\n *\n * This `FetchBackend` requires the support of the\n * [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) which is available on all\n * supported browsers and on Node.js v18 or later.\n *\n * @see {@link HttpHandler}\n *\n * @publicApi\n */\nclass FetchBackend {\n    // We use an arrow function to always reference the current global implementation of `fetch`.\n    // This is helpful for cases when the global `fetch` implementation is modified by external code,\n    // see https://github.com/angular/angular/issues/57527.\n    fetchImpl = inject(FetchFactory, { optional: true })?.fetch ?? ((...args) => globalThis.fetch(...args));\n    ngZone = inject(NgZone);\n    destroyRef = inject(DestroyRef);\n    destroyed = false;\n    constructor() {\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n        });\n    }\n    handle(request) {\n        return new Observable((observer) => {\n            const aborter = new AbortController();\n            this.doRequest(request, aborter.signal, observer).then(noop, (error) => observer.error(new HttpErrorResponse({ error })));\n            return () => aborter.abort();\n        });\n    }\n    async doRequest(request, signal, observer) {\n        const init = this.createRequestInit(request);\n        let response;\n        try {\n            // Run fetch outside of Angular zone.\n            // This is due to Node.js fetch implementation (Undici) which uses a number of setTimeouts to check if\n            // the response should eventually timeout which causes extra CD cycles every 500ms\n            const fetchPromise = this.ngZone.runOutsideAngular(() => this.fetchImpl(request.urlWithParams, { signal, ...init }));\n            // Make sure Zone.js doesn't trigger false-positive unhandled promise\n            // error in case the Promise is rejected synchronously. See function\n            // description for additional information.\n            silenceSuperfluousUnhandledPromiseRejection(fetchPromise);\n            // Send the `Sent` event before awaiting the response.\n            observer.next({ type: HttpEventType.Sent });\n            response = await fetchPromise;\n        }\n        catch (error) {\n            observer.error(new HttpErrorResponse({\n                error,\n                status: error.status ?? 0,\n                statusText: error.statusText,\n                url: request.urlWithParams,\n                headers: error.headers,\n            }));\n            return;\n        }\n        const headers = new HttpHeaders(response.headers);\n        const statusText = response.statusText;\n        const url = getResponseUrl$1(response) ?? request.urlWithParams;\n        let status = response.status;\n        let body = null;\n        if (request.reportProgress) {\n            observer.next(new HttpHeaderResponse({ headers, status, statusText, url }));\n        }\n        if (response.body) {\n            // Read Progress\n            const contentLength = response.headers.get('content-length');\n            const chunks = [];\n            const reader = response.body.getReader();\n            let receivedLength = 0;\n            let decoder;\n            let partialText;\n            // We have to check whether the Zone is defined in the global scope because this may be called\n            // when the zone is nooped.\n            const reqZone = typeof Zone !== 'undefined' && Zone.current;\n            let canceled = false;\n            // Perform response processing outside of Angular zone to\n            // ensure no excessive change detection runs are executed\n            // Here calling the async ReadableStreamDefaultReader.read() is responsible for triggering CD\n            await this.ngZone.runOutsideAngular(async () => {\n                while (true) {\n                    // Prevent reading chunks if the app is destroyed. Otherwise, we risk doing\n                    // unnecessary work or triggering side effects after teardown.\n                    // This may happen if the app was explicitly destroyed before\n                    // the response returned entirely.\n                    if (this.destroyed) {\n                        // Streams left in a pending state (due to `break` without cancel) may\n                        // continue consuming or holding onto data behind the scenes.\n                        // Calling `reader.cancel()` allows the browser or the underlying\n                        // system to release any network or memory resources associated with the stream.\n                        await reader.cancel();\n                        canceled = true;\n                        break;\n                    }\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    chunks.push(value);\n                    receivedLength += value.length;\n                    if (request.reportProgress) {\n                        partialText =\n                            request.responseType === 'text'\n                                ? (partialText ?? '') +\n                                    (decoder ??= new TextDecoder()).decode(value, { stream: true })\n                                : undefined;\n                        const reportProgress = () => observer.next({\n                            type: HttpEventType.DownloadProgress,\n                            total: contentLength ? +contentLength : undefined,\n                            loaded: receivedLength,\n                            partialText,\n                        });\n                        reqZone ? reqZone.run(reportProgress) : reportProgress();\n                    }\n                }\n            });\n            // We need to manage the canceled state — because the Streams API does not\n            // expose a direct `.state` property on the reader.\n            // We need to `return` because `parseBody` may not be able to parse chunks\n            // that were only partially read (due to cancellation caused by app destruction).\n            if (canceled) {\n                observer.complete();\n                return;\n            }\n            // Combine all chunks.\n            const chunksAll = this.concatChunks(chunks, receivedLength);\n            try {\n                const contentType = response.headers.get(CONTENT_TYPE_HEADER) ?? '';\n                body = this.parseBody(request, chunksAll, contentType);\n            }\n            catch (error) {\n                // Body loading or parsing failed\n                observer.error(new HttpErrorResponse({\n                    error,\n                    headers: new HttpHeaders(response.headers),\n                    status: response.status,\n                    statusText: response.statusText,\n                    url: getResponseUrl$1(response) ?? request.urlWithParams,\n                }));\n                return;\n            }\n        }\n        // Same behavior as the XhrBackend\n        if (status === 0) {\n            status = body ? HTTP_STATUS_CODE_OK : 0;\n        }\n        // ok determines whether the response will be transmitted on the event or\n        // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n        // but a successful status code can still result in an error if the user\n        // asked for JSON data and the body cannot be parsed as such.\n        const ok = status >= 200 && status < 300;\n        if (ok) {\n            observer.next(new HttpResponse({\n                body,\n                headers,\n                status,\n                statusText,\n                url,\n            }));\n            // The full body has been received and delivered, no further events\n            // are possible. This request is complete.\n            observer.complete();\n        }\n        else {\n            observer.error(new HttpErrorResponse({\n                error: body,\n                headers,\n                status,\n                statusText,\n                url,\n            }));\n        }\n    }\n    parseBody(request, binContent, contentType) {\n        switch (request.responseType) {\n            case 'json':\n                // stripping the XSSI when present\n                const text = new TextDecoder().decode(binContent).replace(XSSI_PREFIX$1, '');\n                return text === '' ? null : JSON.parse(text);\n            case 'text':\n                return new TextDecoder().decode(binContent);\n            case 'blob':\n                return new Blob([binContent], { type: contentType });\n            case 'arraybuffer':\n                return binContent.buffer;\n        }\n    }\n    createRequestInit(req) {\n        // We could share some of this logic with the XhrBackend\n        const headers = {};\n        const credentials = req.withCredentials ? 'include' : undefined;\n        // Setting all the requested headers.\n        req.headers.forEach((name, values) => (headers[name] = values.join(',')));\n        // Add an Accept header if one isn't present already.\n        if (!req.headers.has(ACCEPT_HEADER)) {\n            headers[ACCEPT_HEADER] = ACCEPT_HEADER_VALUE;\n        }\n        // Auto-detect the Content-Type header if one isn't present already.\n        if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n            const detectedType = req.detectContentTypeHeader();\n            // Sometimes Content-Type detection fails.\n            if (detectedType !== null) {\n                headers[CONTENT_TYPE_HEADER] = detectedType;\n            }\n        }\n        return {\n            body: req.serializeBody(),\n            method: req.method,\n            headers,\n            credentials,\n        };\n    }\n    concatChunks(chunks, totalLength) {\n        const chunksAll = new Uint8Array(totalLength);\n        let position = 0;\n        for (const chunk of chunks) {\n            chunksAll.set(chunk, position);\n            position += chunk.length;\n        }\n        return chunksAll;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: FetchBackend, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: FetchBackend });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: FetchBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\n/**\n * Abstract class to provide a mocked implementation of `fetch()`\n */\nclass FetchFactory {\n}\nfunction noop() { }\n/**\n * Zone.js treats a rejected promise that has not yet been awaited\n * as an unhandled error. This function adds a noop `.then` to make\n * sure that Zone.js doesn't throw an error if the Promise is rejected\n * synchronously.\n */\nfunction silenceSuperfluousUnhandledPromiseRejection(promise) {\n    promise.then(noop, noop);\n}\n\nfunction interceptorChainEndFn(req, finalHandlerFn) {\n    return finalHandlerFn(req);\n}\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(chainTailFn, interceptor) {\n    return (initialRequest, finalHandlerFn) => interceptor.intercept(initialRequest, {\n        handle: (downstreamRequest) => chainTailFn(downstreamRequest, finalHandlerFn),\n    });\n}\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(chainTailFn, interceptorFn, injector) {\n    return (initialRequest, finalHandlerFn) => runInInjectionContext(injector, () => interceptorFn(initialRequest, (downstreamRequest) => chainTailFn(downstreamRequest, finalHandlerFn)));\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTORS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nconst HTTP_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTOR_FNS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s that are only set in root.\n */\nconst HTTP_ROOT_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_ROOT_INTERCEPTOR_FNS' : '');\n// TODO(atscott): We need a larger discussion about stability and what should contribute to stability.\n// Should the whole interceptor chain contribute to stability or just the backend request #55075?\n// Should HttpClient contribute to stability automatically at all?\nconst REQUESTS_CONTRIBUTE_TO_STABILITY = new InjectionToken(ngDevMode ? 'REQUESTS_CONTRIBUTE_TO_STABILITY' : '', { providedIn: 'root', factory: () => true });\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nfunction legacyInterceptorFnFactory() {\n    let chain = null;\n    return (req, handler) => {\n        if (chain === null) {\n            const interceptors = inject(HTTP_INTERCEPTORS, { optional: true }) ?? [];\n            // Note: interceptors are wrapped right-to-left so that final execution order is\n            // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n            // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n            // out.\n            chain = interceptors.reduceRight(adaptLegacyInterceptorToChain, interceptorChainEndFn);\n        }\n        const pendingTasks = inject(_PendingTasksInternal);\n        const contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n        if (contributeToStability) {\n            const taskId = pendingTasks.add();\n            return chain(req, handler).pipe(finalize(() => pendingTasks.remove(taskId)));\n        }\n        else {\n            return chain(req, handler);\n        }\n    };\n}\nlet fetchBackendWarningDisplayed = false;\nclass HttpInterceptorHandler extends HttpHandler {\n    backend;\n    injector;\n    chain = null;\n    pendingTasks = inject(_PendingTasksInternal);\n    contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n    constructor(backend, injector) {\n        super();\n        this.backend = backend;\n        this.injector = injector;\n        // We strongly recommend using fetch backend for HTTP calls when SSR is used\n        // for an application. The logic below checks if that's the case and produces\n        // a warning otherwise.\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && !fetchBackendWarningDisplayed) {\n            const isServer = isPlatformServer(injector.get(PLATFORM_ID));\n            // This flag is necessary because provideHttpClientTesting() overrides the backend\n            // even if `withFetch()` is used within the test. When the testing HTTP backend is provided,\n            // no HTTP calls are actually performed during the test, so producing a warning would be\n            // misleading.\n            const isTestingBackend = this.backend.isTestingBackend;\n            if (isServer && !(this.backend instanceof FetchBackend) && !isTestingBackend) {\n                fetchBackendWarningDisplayed = true;\n                injector\n                    .get(_Console)\n                    .warn(_formatRuntimeError(2801 /* RuntimeErrorCode.NOT_USING_FETCH_BACKEND_IN_SSR */, 'Angular detected that `HttpClient` is not configured ' +\n                    \"to use `fetch` APIs. It's strongly recommended to \" +\n                    'enable `fetch` for applications that use Server-Side Rendering ' +\n                    'for better performance and compatibility. ' +\n                    'To enable `fetch`, add the `withFetch()` to the `provideHttpClient()` ' +\n                    'call at the root of the application.'));\n            }\n        }\n    }\n    handle(initialRequest) {\n        if (this.chain === null) {\n            const dedupedInterceptorFns = Array.from(new Set([\n                ...this.injector.get(HTTP_INTERCEPTOR_FNS),\n                ...this.injector.get(HTTP_ROOT_INTERCEPTOR_FNS, []),\n            ]));\n            // Note: interceptors are wrapped right-to-left so that final execution order is\n            // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n            // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n            // out.\n            this.chain = dedupedInterceptorFns.reduceRight((nextSequencedFn, interceptorFn) => chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector), interceptorChainEndFn);\n        }\n        if (this.contributeToStability) {\n            const taskId = this.pendingTasks.add();\n            return this.chain(initialRequest, (downstreamRequest) => this.backend.handle(downstreamRequest)).pipe(finalize(() => this.pendingTasks.remove(taskId)));\n        }\n        else {\n            return this.chain(initialRequest, (downstreamRequest) => this.backend.handle(downstreamRequest));\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpInterceptorHandler, deps: [{ token: HttpBackend }, { token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpInterceptorHandler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpInterceptorHandler, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: HttpBackend }, { type: i0.EnvironmentInjector }] });\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {\n}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n    if (typeof window === 'object') {\n        return window;\n    }\n    return {};\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n    callbackMap;\n    document;\n    /**\n     * A resolved promise that can be used to schedule microtasks in the event handlers.\n     */\n    resolvedPromise = Promise.resolve();\n    constructor(callbackMap, document) {\n        this.callbackMap = callbackMap;\n        this.document = document;\n    }\n    /**\n     * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n     */\n    nextCallback() {\n        return `ng_jsonp_callback_${nextRequestId++}`;\n    }\n    /**\n     * Processes a JSONP request and returns an event stream of the results.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     *\n     */\n    handle(req) {\n        // Firstly, check both the method and response type. If either doesn't match\n        // then the request was improperly routed here and cannot be handled.\n        if (req.method !== 'JSONP') {\n            throw new Error(JSONP_ERR_WRONG_METHOD);\n        }\n        else if (req.responseType !== 'json') {\n            throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n        }\n        // Check the request headers. JSONP doesn't support headers and\n        // cannot set any that were supplied.\n        if (req.headers.keys().length > 0) {\n            throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n        }\n        // Everything else happens inside the Observable boundary.\n        return new Observable((observer) => {\n            // The first step to make a request is to generate the callback name, and replace the\n            // callback placeholder in the URL with the name. Care has to be taken here to ensure\n            // a trailing &, if matched, gets inserted back into the URL in the correct place.\n            const callback = this.nextCallback();\n            const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n            // Construct the <script> tag and point it at the URL.\n            const node = this.document.createElement('script');\n            node.src = url;\n            // A JSONP request requires waiting for multiple callbacks. These variables\n            // are closed over and track state across those callbacks.\n            // The response object, if one has been received, or null otherwise.\n            let body = null;\n            // Whether the response callback has been called.\n            let finished = false;\n            // Set the response callback in this.callbackMap (which will be the window\n            // object in the browser. The script being loaded via the <script> tag will\n            // eventually call this callback.\n            this.callbackMap[callback] = (data) => {\n                // Data has been received from the JSONP script. Firstly, delete this callback.\n                delete this.callbackMap[callback];\n                // Set state to indicate data was received.\n                body = data;\n                finished = true;\n            };\n            // cleanup() is a utility closure that removes the <script> from the page and\n            // the response callback from the window. This logic is used in both the\n            // success, error, and cancellation paths, so it's extracted out for convenience.\n            const cleanup = () => {\n                node.removeEventListener('load', onLoad);\n                node.removeEventListener('error', onError);\n                // Remove the <script> tag if it's still on the page.\n                node.remove();\n                // Remove the response callback from the callbackMap (window object in the\n                // browser).\n                delete this.callbackMap[callback];\n            };\n            // onLoad() is the success callback which runs after the response callback\n            // if the JSONP script loads successfully. The event itself is unimportant.\n            // If something went wrong, onLoad() may run without the response callback\n            // having been invoked.\n            const onLoad = (event) => {\n                // We wrap it in an extra Promise, to ensure the microtask\n                // is scheduled after the loaded endpoint has executed any potential microtask itself,\n                // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n                this.resolvedPromise.then(() => {\n                    // Cleanup the page.\n                    cleanup();\n                    // Check whether the response callback has run.\n                    if (!finished) {\n                        // It hasn't, something went wrong with the request. Return an error via\n                        // the Observable error path. All JSONP errors have status 0.\n                        observer.error(new HttpErrorResponse({\n                            url,\n                            status: 0,\n                            statusText: 'JSONP Error',\n                            error: new Error(JSONP_ERR_NO_CALLBACK),\n                        }));\n                        return;\n                    }\n                    // Success. body either contains the response body or null if none was\n                    // returned.\n                    observer.next(new HttpResponse({\n                        body,\n                        status: HTTP_STATUS_CODE_OK,\n                        statusText: 'OK',\n                        url,\n                    }));\n                    // Complete the stream, the response is over.\n                    observer.complete();\n                });\n            };\n            // onError() is the error callback, which runs if the script returned generates\n            // a Javascript error. It emits the error via the Observable error channel as\n            // a HttpErrorResponse.\n            const onError = (error) => {\n                cleanup();\n                // Wrap the error in a HttpErrorResponse.\n                observer.error(new HttpErrorResponse({\n                    error,\n                    status: 0,\n                    statusText: 'JSONP Error',\n                    url,\n                }));\n            };\n            // Subscribe to both the success (load) and error events on the <script> tag,\n            // and add it to the page.\n            node.addEventListener('load', onLoad);\n            node.addEventListener('error', onError);\n            this.document.body.appendChild(node);\n            // The request has now been successfully sent.\n            observer.next({ type: HttpEventType.Sent });\n            // Cancellation handler.\n            return () => {\n                if (!finished) {\n                    this.removeListeners(node);\n                }\n                // And finally, clean up the page.\n                cleanup();\n            };\n        });\n    }\n    removeListeners(script) {\n        // Issue #34818\n        // Changing <script>'s ownerDocument will prevent it from execution.\n        // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n        foreignDocument ??= this.document.implementation.createHTMLDocument();\n        foreignDocument.adoptNode(script);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: JsonpClientBackend, deps: [{ token: JsonpCallbackContext }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: JsonpClientBackend });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: JsonpClientBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: JsonpCallbackContext }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nfunction jsonpInterceptorFn(req, next) {\n    if (req.method === 'JSONP') {\n        return inject(JsonpClientBackend).handle(req);\n    }\n    // Fall through for normal HTTP requests.\n    return next(req);\n}\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n    injector;\n    constructor(injector) {\n        this.injector = injector;\n    }\n    /**\n     * Identifies and handles a given JSONP request.\n     * @param initialRequest The outgoing request object to handle.\n     * @param next The next interceptor in the chain, or the backend\n     * if no interceptors remain in the chain.\n     * @returns An observable of the event stream.\n     */\n    intercept(initialRequest, next) {\n        return runInInjectionContext(this.injector, () => jsonpInterceptorFn(initialRequest, (downstreamRequest) => next.handle(downstreamRequest)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: JsonpInterceptor, deps: [{ token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: JsonpInterceptor });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: JsonpInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.EnvironmentInjector }] });\n\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\nconst X_REQUEST_URL_REGEXP = RegExp(`^${X_REQUEST_URL_HEADER}:`, 'm');\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n    if ('responseURL' in xhr && xhr.responseURL) {\n        return xhr.responseURL;\n    }\n    if (X_REQUEST_URL_REGEXP.test(xhr.getAllResponseHeaders())) {\n        return xhr.getResponseHeader(X_REQUEST_URL_HEADER);\n    }\n    return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n    xhrFactory;\n    constructor(xhrFactory) {\n        this.xhrFactory = xhrFactory;\n    }\n    /**\n     * Processes a request and returns a stream of response events.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     */\n    handle(req) {\n        // Quick check to give a better error message when a user attempts to use\n        // HttpClient.jsonp() without installing the HttpClientJsonpModule\n        if (req.method === 'JSONP') {\n            throw new _RuntimeError(-2800 /* RuntimeErrorCode.MISSING_JSONP_MODULE */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `Cannot make a JSONP request without JSONP support. To fix the problem, either add the \\`withJsonpSupport()\\` call (if \\`provideHttpClient()\\` is used) or import the \\`HttpClientJsonpModule\\` in the root NgModule.`);\n        }\n        // Check whether this factory has a special function to load an XHR implementation\n        // for various non-browser environments. We currently limit it to only `ServerXhr`\n        // class, which needs to load an XHR implementation.\n        const xhrFactory = this.xhrFactory;\n        const source = xhrFactory.ɵloadImpl\n            ? from(xhrFactory.ɵloadImpl())\n            : of(null);\n        return source.pipe(switchMap(() => {\n            // Everything happens on Observable subscription.\n            return new Observable((observer) => {\n                // Start by setting up the XHR object with request method, URL, and withCredentials\n                // flag.\n                const xhr = xhrFactory.build();\n                xhr.open(req.method, req.urlWithParams);\n                if (req.withCredentials) {\n                    xhr.withCredentials = true;\n                }\n                // Add all the requested headers.\n                req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n                // Add an Accept header if one isn't present already.\n                if (!req.headers.has(ACCEPT_HEADER)) {\n                    xhr.setRequestHeader(ACCEPT_HEADER, ACCEPT_HEADER_VALUE);\n                }\n                // Auto-detect the Content-Type header if one isn't present already.\n                if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n                    const detectedType = req.detectContentTypeHeader();\n                    // Sometimes Content-Type detection fails.\n                    if (detectedType !== null) {\n                        xhr.setRequestHeader(CONTENT_TYPE_HEADER, detectedType);\n                    }\n                }\n                // Set the responseType if one was requested.\n                if (req.responseType) {\n                    const responseType = req.responseType.toLowerCase();\n                    // JSON responses need to be processed as text. This is because if the server\n                    // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n                    // xhr.response will be null, and xhr.responseText cannot be accessed to\n                    // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n                    // is parsed by first requesting text and then applying JSON.parse.\n                    xhr.responseType = (responseType !== 'json' ? responseType : 'text');\n                }\n                // Serialize the request body if one is present. If not, this will be set to null.\n                const reqBody = req.serializeBody();\n                // If progress events are enabled, response headers will be delivered\n                // in two events - the HttpHeaderResponse event and the full HttpResponse\n                // event. However, since response headers don't change in between these\n                // two events, it doesn't make sense to parse them twice. So headerResponse\n                // caches the data extracted from the response whenever it's first parsed,\n                // to ensure parsing isn't duplicated.\n                let headerResponse = null;\n                // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n                // state, and memoizes it into headerResponse.\n                const partialFromXhr = () => {\n                    if (headerResponse !== null) {\n                        return headerResponse;\n                    }\n                    const statusText = xhr.statusText || 'OK';\n                    // Parse headers from XMLHttpRequest - this step is lazy.\n                    const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n                    // Read the response URL from the XMLHttpResponse instance and fall back on the\n                    // request URL.\n                    const url = getResponseUrl(xhr) || req.url;\n                    // Construct the HttpHeaderResponse and memoize it.\n                    headerResponse = new HttpHeaderResponse({ headers, status: xhr.status, statusText, url });\n                    return headerResponse;\n                };\n                // Next, a few closures are defined for the various events which XMLHttpRequest can\n                // emit. This allows them to be unregistered as event listeners later.\n                // First up is the load event, which represents a response being fully available.\n                const onLoad = () => {\n                    // Read response state from the memoized partial data.\n                    let { headers, status, statusText, url } = partialFromXhr();\n                    // The body will be read out if present.\n                    let body = null;\n                    if (status !== HTTP_STATUS_CODE_NO_CONTENT) {\n                        // Use XMLHttpRequest.response if set, responseText otherwise.\n                        body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n                    }\n                    // Normalize another potential bug (this one comes from CORS).\n                    if (status === 0) {\n                        status = !!body ? HTTP_STATUS_CODE_OK : 0;\n                    }\n                    // ok determines whether the response will be transmitted on the event or\n                    // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n                    // but a successful status code can still result in an error if the user\n                    // asked for JSON data and the body cannot be parsed as such.\n                    let ok = status >= 200 && status < 300;\n                    // Check whether the body needs to be parsed as JSON (in many cases the browser\n                    // will have done that already).\n                    if (req.responseType === 'json' && typeof body === 'string') {\n                        // Save the original body, before attempting XSSI prefix stripping.\n                        const originalBody = body;\n                        body = body.replace(XSSI_PREFIX, '');\n                        try {\n                            // Attempt the parse. If it fails, a parse error should be delivered to the\n                            // user.\n                            body = body !== '' ? JSON.parse(body) : null;\n                        }\n                        catch (error) {\n                            // Since the JSON.parse failed, it's reasonable to assume this might not have\n                            // been a JSON response. Restore the original body (including any XSSI prefix)\n                            // to deliver a better error response.\n                            body = originalBody;\n                            // If this was an error request to begin with, leave it as a string, it\n                            // probably just isn't JSON. Otherwise, deliver the parsing error to the user.\n                            if (ok) {\n                                // Even though the response status was 2xx, this is still an error.\n                                ok = false;\n                                // The parse error contains the text of the body that failed to parse.\n                                body = { error, text: body };\n                            }\n                        }\n                    }\n                    if (ok) {\n                        // A successful response is delivered on the event stream.\n                        observer.next(new HttpResponse({\n                            body,\n                            headers,\n                            status,\n                            statusText,\n                            url: url || undefined,\n                        }));\n                        // The full body has been received and delivered, no further events\n                        // are possible. This request is complete.\n                        observer.complete();\n                    }\n                    else {\n                        // An unsuccessful request is delivered on the error channel.\n                        observer.error(new HttpErrorResponse({\n                            // The error in this case is the response body (error from the server).\n                            error: body,\n                            headers,\n                            status,\n                            statusText,\n                            url: url || undefined,\n                        }));\n                    }\n                };\n                // The onError callback is called when something goes wrong at the network level.\n                // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n                // transmitted on the error channel.\n                const onError = (error) => {\n                    const { url } = partialFromXhr();\n                    const res = new HttpErrorResponse({\n                        error,\n                        status: xhr.status || 0,\n                        statusText: xhr.statusText || 'Unknown Error',\n                        url: url || undefined,\n                    });\n                    observer.error(res);\n                };\n                // The sentHeaders flag tracks whether the HttpResponseHeaders event\n                // has been sent on the stream. This is necessary to track if progress\n                // is enabled since the event will be sent on only the first download\n                // progress event.\n                let sentHeaders = false;\n                // The download progress event handler, which is only registered if\n                // progress events are enabled.\n                const onDownProgress = (event) => {\n                    // Send the HttpResponseHeaders event if it hasn't been sent already.\n                    if (!sentHeaders) {\n                        observer.next(partialFromXhr());\n                        sentHeaders = true;\n                    }\n                    // Start building the download progress event to deliver on the response\n                    // event stream.\n                    let progressEvent = {\n                        type: HttpEventType.DownloadProgress,\n                        loaded: event.loaded,\n                    };\n                    // Set the total number of bytes in the event if it's available.\n                    if (event.lengthComputable) {\n                        progressEvent.total = event.total;\n                    }\n                    // If the request was for text content and a partial response is\n                    // available on XMLHttpRequest, include it in the progress event\n                    // to allow for streaming reads.\n                    if (req.responseType === 'text' && !!xhr.responseText) {\n                        progressEvent.partialText = xhr.responseText;\n                    }\n                    // Finally, fire the event.\n                    observer.next(progressEvent);\n                };\n                // The upload progress event handler, which is only registered if\n                // progress events are enabled.\n                const onUpProgress = (event) => {\n                    // Upload progress events are simpler. Begin building the progress\n                    // event.\n                    let progress = {\n                        type: HttpEventType.UploadProgress,\n                        loaded: event.loaded,\n                    };\n                    // If the total number of bytes being uploaded is available, include\n                    // it.\n                    if (event.lengthComputable) {\n                        progress.total = event.total;\n                    }\n                    // Send the event.\n                    observer.next(progress);\n                };\n                // By default, register for load and error events.\n                xhr.addEventListener('load', onLoad);\n                xhr.addEventListener('error', onError);\n                xhr.addEventListener('timeout', onError);\n                xhr.addEventListener('abort', onError);\n                // Progress events are only enabled if requested.\n                if (req.reportProgress) {\n                    // Download progress is always enabled if requested.\n                    xhr.addEventListener('progress', onDownProgress);\n                    // Upload progress depends on whether there is a body to upload.\n                    if (reqBody !== null && xhr.upload) {\n                        xhr.upload.addEventListener('progress', onUpProgress);\n                    }\n                }\n                // Fire the request, and notify the event stream that it was fired.\n                xhr.send(reqBody);\n                observer.next({ type: HttpEventType.Sent });\n                // This is the return from the Observable function, which is the\n                // request cancellation handler.\n                return () => {\n                    // On a cancellation, remove all registered event listeners.\n                    xhr.removeEventListener('error', onError);\n                    xhr.removeEventListener('abort', onError);\n                    xhr.removeEventListener('load', onLoad);\n                    xhr.removeEventListener('timeout', onError);\n                    if (req.reportProgress) {\n                        xhr.removeEventListener('progress', onDownProgress);\n                        if (reqBody !== null && xhr.upload) {\n                            xhr.upload.removeEventListener('progress', onUpProgress);\n                        }\n                    }\n                    // Finally, abort the in-flight request.\n                    if (xhr.readyState !== xhr.DONE) {\n                        xhr.abort();\n                    }\n                };\n            });\n        }));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpXhrBackend, deps: [{ token: XhrFactory }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpXhrBackend });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpXhrBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: XhrFactory }] });\n\nconst XSRF_ENABLED = new InjectionToken(ngDevMode ? 'XSRF_ENABLED' : '');\nconst XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nconst XSRF_COOKIE_NAME = new InjectionToken(ngDevMode ? 'XSRF_COOKIE_NAME' : '', {\n    providedIn: 'root',\n    factory: () => XSRF_DEFAULT_COOKIE_NAME,\n});\nconst XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nconst XSRF_HEADER_NAME = new InjectionToken(ngDevMode ? 'XSRF_HEADER_NAME' : '', {\n    providedIn: 'root',\n    factory: () => XSRF_DEFAULT_HEADER_NAME,\n});\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {\n}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n    doc;\n    cookieName;\n    lastCookieString = '';\n    lastToken = null;\n    /**\n     * @internal for testing\n     */\n    parseCount = 0;\n    constructor(doc, cookieName) {\n        this.doc = doc;\n        this.cookieName = cookieName;\n    }\n    getToken() {\n        if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n            return null;\n        }\n        const cookieString = this.doc.cookie || '';\n        if (cookieString !== this.lastCookieString) {\n            this.parseCount++;\n            this.lastToken = parseCookieValue(cookieString, this.cookieName);\n            this.lastCookieString = cookieString;\n        }\n        return this.lastToken;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpXsrfCookieExtractor, deps: [{ token: DOCUMENT }, { token: XSRF_COOKIE_NAME }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpXsrfCookieExtractor });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpXsrfCookieExtractor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [XSRF_COOKIE_NAME]\n                }] }] });\nfunction xsrfInterceptorFn(req, next) {\n    const lcUrl = req.url.toLowerCase();\n    // Skip both non-mutating requests and absolute URLs.\n    // Non-mutating requests don't require a token, and absolute URLs require special handling\n    // anyway as the cookie set\n    // on our origin is not the same as the token expected by another origin.\n    if (!inject(XSRF_ENABLED) ||\n        req.method === 'GET' ||\n        req.method === 'HEAD' ||\n        lcUrl.startsWith('http://') ||\n        lcUrl.startsWith('https://')) {\n        return next(req);\n    }\n    const token = inject(HttpXsrfTokenExtractor).getToken();\n    const headerName = inject(XSRF_HEADER_NAME);\n    // Be careful not to overwrite an existing header of the same name.\n    if (token != null && !req.headers.has(headerName)) {\n        req = req.clone({ headers: req.headers.set(headerName, token) });\n    }\n    return next(req);\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n    injector;\n    constructor(injector) {\n        this.injector = injector;\n    }\n    intercept(initialRequest, next) {\n        return runInInjectionContext(this.injector, () => xsrfInterceptorFn(initialRequest, (downstreamRequest) => next.handle(downstreamRequest)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpXsrfInterceptor, deps: [{ token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpXsrfInterceptor });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpXsrfInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.EnvironmentInjector }] });\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nvar HttpFeatureKind;\n(function (HttpFeatureKind) {\n    HttpFeatureKind[HttpFeatureKind[\"Interceptors\"] = 0] = \"Interceptors\";\n    HttpFeatureKind[HttpFeatureKind[\"LegacyInterceptors\"] = 1] = \"LegacyInterceptors\";\n    HttpFeatureKind[HttpFeatureKind[\"CustomXsrfConfiguration\"] = 2] = \"CustomXsrfConfiguration\";\n    HttpFeatureKind[HttpFeatureKind[\"NoXsrfProtection\"] = 3] = \"NoXsrfProtection\";\n    HttpFeatureKind[HttpFeatureKind[\"JsonpSupport\"] = 4] = \"JsonpSupport\";\n    HttpFeatureKind[HttpFeatureKind[\"RequestsMadeViaParent\"] = 5] = \"RequestsMadeViaParent\";\n    HttpFeatureKind[HttpFeatureKind[\"Fetch\"] = 6] = \"Fetch\";\n})(HttpFeatureKind || (HttpFeatureKind = {}));\nfunction makeHttpFeature(kind, providers) {\n    return {\n        ɵkind: kind,\n        ɵproviders: providers,\n    };\n}\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * It's strongly recommended to enable\n * [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) for applications that use\n * Server-Side Rendering for better performance and compatibility. To enable `fetch`, add\n * `withFetch()` feature to the `provideHttpClient()` call at the root of the application:\n *\n * ```ts\n * provideHttpClient(withFetch());\n * ```\n *\n * </div>\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n * @see {@link withFetch}\n */\nfunction provideHttpClient(...features) {\n    if (ngDevMode) {\n        const featureKinds = new Set(features.map((f) => f.ɵkind));\n        if (featureKinds.has(HttpFeatureKind.NoXsrfProtection) &&\n            featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)) {\n            throw new Error(ngDevMode\n                ? `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.`\n                : '');\n        }\n    }\n    const providers = [\n        HttpClient,\n        HttpXhrBackend,\n        HttpInterceptorHandler,\n        { provide: HttpHandler, useExisting: HttpInterceptorHandler },\n        {\n            provide: HttpBackend,\n            useFactory: () => {\n                return inject(FETCH_BACKEND, { optional: true }) ?? inject(HttpXhrBackend);\n            },\n        },\n        {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useValue: xsrfInterceptorFn,\n            multi: true,\n        },\n        { provide: XSRF_ENABLED, useValue: true },\n        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n    ];\n    for (const feature of features) {\n        providers.push(...feature.ɵproviders);\n    }\n    return makeEnvironmentProviders(providers);\n}\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withInterceptors(interceptorFns) {\n    return makeHttpFeature(HttpFeatureKind.Interceptors, interceptorFns.map((interceptorFn) => {\n        return {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useValue: interceptorFn,\n            multi: true,\n        };\n    }));\n}\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken(ngDevMode ? 'LEGACY_INTERCEPTOR_FN' : '');\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nfunction withInterceptorsFromDi() {\n    // Note: the legacy interceptor function is provided here via an intermediate token\n    // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n    // included multiple times, all of the multi-provider entries will have the same instance of the\n    // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n    // interceptors will not run multiple times.\n    return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [\n        {\n            provide: LEGACY_INTERCEPTOR_FN,\n            useFactory: legacyInterceptorFnFactory,\n        },\n        {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useExisting: LEGACY_INTERCEPTOR_FN,\n            multi: true,\n        },\n    ]);\n}\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withXsrfConfiguration({ cookieName, headerName, }) {\n    const providers = [];\n    if (cookieName !== undefined) {\n        providers.push({ provide: XSRF_COOKIE_NAME, useValue: cookieName });\n    }\n    if (headerName !== undefined) {\n        providers.push({ provide: XSRF_HEADER_NAME, useValue: headerName });\n    }\n    return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withNoXsrfProtection() {\n    return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [\n        {\n            provide: XSRF_ENABLED,\n            useValue: false,\n        },\n    ]);\n}\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nfunction withJsonpSupport() {\n    return makeHttpFeature(HttpFeatureKind.JsonpSupport, [\n        JsonpClientBackend,\n        { provide: JsonpCallbackContext, useFactory: jsonpCallbackContext },\n        { provide: HTTP_INTERCEPTOR_FNS, useValue: jsonpInterceptorFn, multi: true },\n    ]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withRequestsMadeViaParent() {\n    return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [\n        {\n            provide: HttpBackend,\n            useFactory: () => {\n                const handlerFromParent = inject(HttpHandler, { skipSelf: true, optional: true });\n                if (ngDevMode && handlerFromParent === null) {\n                    throw new Error('withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient');\n                }\n                return handlerFromParent;\n            },\n        },\n    ]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests using the fetch API.\n *\n * Note: The Fetch API doesn't support progress report on uploads.\n *\n * @publicApi\n */\nfunction withFetch() {\n    return makeHttpFeature(HttpFeatureKind.Fetch, [\n        FetchBackend,\n        { provide: FETCH_BACKEND, useExisting: FetchBackend },\n        { provide: HttpBackend, useExisting: FetchBackend },\n    ]);\n}\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n * @deprecated Use withXsrfConfiguration({cookieName: 'XSRF-TOKEN', headerName: 'X-XSRF-TOKEN'}) as\n *     providers instead or `withNoXsrfProtection` if you want to disabled XSRF protection.\n */\nclass HttpClientXsrfModule {\n    /**\n     * Disable the default XSRF protection.\n     */\n    static disable() {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: [withNoXsrfProtection().ɵproviders],\n        };\n    }\n    /**\n     * Configure XSRF protection.\n     * @param options An object that can specify either or both\n     * cookie name or header name.\n     * - Cookie name default is `XSRF-TOKEN`.\n     * - Header name default is `X-XSRF-TOKEN`.\n     *\n     */\n    static withOptions(options = {}) {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: withXsrfConfiguration(options).ɵproviders,\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientXsrfModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientXsrfModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientXsrfModule, providers: [\n            HttpXsrfInterceptor,\n            { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n            { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n            withXsrfConfiguration({\n                cookieName: XSRF_DEFAULT_COOKIE_NAME,\n                headerName: XSRF_DEFAULT_HEADER_NAME,\n            }).ɵproviders,\n            { provide: XSRF_ENABLED, useValue: true },\n        ] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientXsrfModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        HttpXsrfInterceptor,\n                        { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n                        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n                        withXsrfConfiguration({\n                            cookieName: XSRF_DEFAULT_COOKIE_NAME,\n                            headerName: XSRF_DEFAULT_HEADER_NAME,\n                        }).ɵproviders,\n                        { provide: XSRF_ENABLED, useValue: true },\n                    ],\n                }]\n        }] });\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in DI token `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n * @deprecated use `provideHttpClient(withInterceptorsFromDi())` as providers instead\n */\nclass HttpClientModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientModule, providers: [provideHttpClient(withInterceptorsFromDi())] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    /**\n                     * Configures the dependency injector where it is imported\n                     * with supporting services for HTTP communications.\n                     */\n                    providers: [provideHttpClient(withInterceptorsFromDi())],\n                }]\n        }] });\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n * @deprecated `withJsonpSupport()` as providers instead\n */\nclass HttpClientJsonpModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientJsonpModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientJsonpModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientJsonpModule, providers: [withJsonpSupport().ɵproviders] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: HttpClientJsonpModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [withJsonpSupport().ɵproviders],\n                }]\n        }] });\n\nexport { FetchBackend, HTTP_INTERCEPTORS, HTTP_ROOT_INTERCEPTOR_FNS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpInterceptorHandler, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, REQUESTS_CONTRIBUTE_TO_STABILITY, provideHttpClient, withFetch, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration };\n"], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,aAAa,IAAIC,aAAa,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,qBAAqB,IAAIC,qBAAqB,EAAEC,WAAW,EAAEC,QAAQ,IAAIC,QAAQ,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,wBAAwB,EAAEC,QAAQ,QAAQ,eAAe;AACxT,SAASC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AAC5E,SAASC,EAAE,EAAEC,UAAU,EAAEC,IAAI,QAAQ,MAAM;AAC3C,SAASC,gBAAgB,EAAEC,UAAU,EAAEC,gBAAgB,QAAQ,oBAAoB;AACnF,SAASC,QAAQ,QAAQ,2BAA2B;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;;AAGlB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EAkBd;EACAC,WAAWA,CAACC,OAAO,EAAE;IAlBrB;AACJ;AACA;IAFIC,eAAA;IAIA;AACJ;AACA;AACA;IAHIA,eAAA,0BAIkB,IAAIC,GAAG,CAAC,CAAC;IAC3B;AACJ;AACA;IAFID,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA,qBAGa,IAAI;IAGb,IAAI,CAACD,OAAO,EAAE;MACV,IAAI,CAACA,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC5B,CAAC,MACI,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;MAClC,IAAI,CAACG,QAAQ,GAAG,MAAM;QAClB,IAAI,CAACH,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;QACxBF,OAAO,CAACI,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAAEC,IAAI,IAAK;UAClC,MAAMC,KAAK,GAAGD,IAAI,CAACE,OAAO,CAAC,GAAG,CAAC;UAC/B,IAAID,KAAK,GAAG,CAAC,EAAE;YACX,MAAME,IAAI,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;YACjC,MAAMI,KAAK,GAAGL,IAAI,CAACI,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC;YAC1C,IAAI,CAACC,cAAc,CAACJ,IAAI,EAAEE,KAAK,CAAC;UACpC;QACJ,CAAC,CAAC;MACN,CAAC;IACL,CAAC,MACI,IAAI,OAAOG,OAAO,KAAK,WAAW,IAAId,OAAO,YAAYc,OAAO,EAAE;MACnE,IAAI,CAACd,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;MACxBF,OAAO,CAACK,OAAO,CAAC,CAACM,KAAK,EAAEF,IAAI,KAAK;QAC7B,IAAI,CAACI,cAAc,CAACJ,IAAI,EAAEE,KAAK,CAAC;MACpC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACR,QAAQ,GAAG,MAAM;QAClB,IAAI,OAAOY,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/CC,kBAAkB,CAAChB,OAAO,CAAC;QAC/B;QACA,IAAI,CAACA,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;QACxBe,MAAM,CAACC,OAAO,CAAClB,OAAO,CAAC,CAACK,OAAO,CAAC,CAAC,CAACI,IAAI,EAAEU,MAAM,CAAC,KAAK;UAChD,IAAI,CAACC,gBAAgB,CAACX,IAAI,EAAEU,MAAM,CAAC;QACvC,CAAC,CAAC;MACN,CAAC;IACL;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,GAAGA,CAACZ,IAAI,EAAE;IACN,IAAI,CAACa,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACtB,OAAO,CAACqB,GAAG,CAACZ,IAAI,CAACc,WAAW,CAAC,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,GAAGA,CAACf,IAAI,EAAE;IACN,IAAI,CAACa,IAAI,CAAC,CAAC;IACX,MAAMH,MAAM,GAAG,IAAI,CAACnB,OAAO,CAACwB,GAAG,CAACf,IAAI,CAACc,WAAW,CAAC,CAAC,CAAC;IACnD,OAAOJ,MAAM,IAAIA,MAAM,CAACM,MAAM,GAAG,CAAC,GAAGN,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;EACzD;EACA;AACJ;AACA;AACA;AACA;EACIO,IAAIA,CAAA,EAAG;IACH,IAAI,CAACJ,IAAI,CAAC,CAAC;IACX,OAAOK,KAAK,CAACpC,IAAI,CAAC,IAAI,CAACqC,eAAe,CAACT,MAAM,CAAC,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIU,MAAMA,CAACpB,IAAI,EAAE;IACT,IAAI,CAACa,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACtB,OAAO,CAACwB,GAAG,CAACf,IAAI,CAACc,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,MAAMA,CAACrB,IAAI,EAAEE,KAAK,EAAE;IAChB,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAEtB,IAAI;MAAEE,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,GAAGA,CAACxB,IAAI,EAAEE,KAAK,EAAE;IACb,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAEtB,IAAI;MAAEE,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,MAAMA,CAACzB,IAAI,EAAEE,KAAK,EAAE;IAChB,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAEtB,IAAI;MAAEE,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACAG,sBAAsBA,CAAC1B,IAAI,EAAE2B,MAAM,EAAE;IACjC,IAAI,CAAC,IAAI,CAACR,eAAe,CAACP,GAAG,CAACe,MAAM,CAAC,EAAE;MACnC,IAAI,CAACR,eAAe,CAACK,GAAG,CAACG,MAAM,EAAE3B,IAAI,CAAC;IAC1C;EACJ;EACAa,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,CAAC,IAAI,CAACnB,QAAQ,EAAE;MACjB,IAAI,IAAI,CAACA,QAAQ,YAAYL,WAAW,EAAE;QACtC,IAAI,CAACuC,QAAQ,CAAC,IAAI,CAAClC,QAAQ,CAAC;MAChC,CAAC,MACI;QACD,IAAI,CAACA,QAAQ,CAAC,CAAC;MACnB;MACA,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAAC,CAAC,IAAI,CAACmC,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACjC,OAAO,CAAEkC,MAAM,IAAK,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC,CAAC;QAC7D,IAAI,CAACD,UAAU,GAAG,IAAI;MAC1B;IACJ;EACJ;EACAD,QAAQA,CAACI,KAAK,EAAE;IACZA,KAAK,CAACnB,IAAI,CAAC,CAAC;IACZK,KAAK,CAACpC,IAAI,CAACkD,KAAK,CAACzC,OAAO,CAAC0B,IAAI,CAAC,CAAC,CAAC,CAACrB,OAAO,CAAEqC,GAAG,IAAK;MAC9C,IAAI,CAAC1C,OAAO,CAACiC,GAAG,CAACS,GAAG,EAAED,KAAK,CAACzC,OAAO,CAACwB,GAAG,CAACkB,GAAG,CAAC,CAAC;MAC7C,IAAI,CAACd,eAAe,CAACK,GAAG,CAACS,GAAG,EAAED,KAAK,CAACb,eAAe,CAACJ,GAAG,CAACkB,GAAG,CAAC,CAAC;IACjE,CAAC,CAAC;EACN;EACAX,KAAKA,CAACQ,MAAM,EAAE;IACV,MAAMR,KAAK,GAAG,IAAIjC,WAAW,CAAC,CAAC;IAC/BiC,KAAK,CAAC5B,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACA,QAAQ,YAAYL,WAAW,GAAG,IAAI,CAACK,QAAQ,GAAG,IAAI;IAC/F4B,KAAK,CAACO,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU,IAAI,EAAE,EAAEK,MAAM,CAAC,CAACJ,MAAM,CAAC,CAAC;IAC3D,OAAOR,KAAK;EAChB;EACAS,WAAWA,CAACD,MAAM,EAAE;IAChB,MAAMG,GAAG,GAAGH,MAAM,CAAC9B,IAAI,CAACc,WAAW,CAAC,CAAC;IACrC,QAAQgB,MAAM,CAACP,EAAE;MACb,KAAK,GAAG;MACR,KAAK,GAAG;QACJ,IAAIrB,KAAK,GAAG4B,MAAM,CAAC5B,KAAK;QACxB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC3BA,KAAK,GAAG,CAACA,KAAK,CAAC;QACnB;QACA,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;UACpB;QACJ;QACA,IAAI,CAACU,sBAAsB,CAACI,MAAM,CAAC9B,IAAI,EAAEiC,GAAG,CAAC;QAC7C,MAAME,IAAI,GAAG,CAACL,MAAM,CAACP,EAAE,KAAK,GAAG,GAAG,IAAI,CAAChC,OAAO,CAACwB,GAAG,CAACkB,GAAG,CAAC,GAAGG,SAAS,KAAK,EAAE;QAC1ED,IAAI,CAACE,IAAI,CAAC,GAAGnC,KAAK,CAAC;QACnB,IAAI,CAACX,OAAO,CAACiC,GAAG,CAACS,GAAG,EAAEE,IAAI,CAAC;QAC3B;MACJ,KAAK,GAAG;QACJ,MAAMG,QAAQ,GAAGR,MAAM,CAAC5B,KAAK;QAC7B,IAAI,CAACoC,QAAQ,EAAE;UACX,IAAI,CAAC/C,OAAO,CAACkC,MAAM,CAACQ,GAAG,CAAC;UACxB,IAAI,CAACd,eAAe,CAACM,MAAM,CAACQ,GAAG,CAAC;QACpC,CAAC,MACI;UACD,IAAIM,QAAQ,GAAG,IAAI,CAAChD,OAAO,CAACwB,GAAG,CAACkB,GAAG,CAAC;UACpC,IAAI,CAACM,QAAQ,EAAE;YACX;UACJ;UACAA,QAAQ,GAAGA,QAAQ,CAAC/D,MAAM,CAAE0B,KAAK,IAAKoC,QAAQ,CAACvC,OAAO,CAACG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;UACrE,IAAIqC,QAAQ,CAACvB,MAAM,KAAK,CAAC,EAAE;YACvB,IAAI,CAACzB,OAAO,CAACkC,MAAM,CAACQ,GAAG,CAAC;YACxB,IAAI,CAACd,eAAe,CAACM,MAAM,CAACQ,GAAG,CAAC;UACpC,CAAC,MACI;YACD,IAAI,CAAC1C,OAAO,CAACiC,GAAG,CAACS,GAAG,EAAEM,QAAQ,CAAC;UACnC;QACJ;QACA;IACR;EACJ;EACAnC,cAAcA,CAACJ,IAAI,EAAEE,KAAK,EAAE;IACxB,MAAM+B,GAAG,GAAGjC,IAAI,CAACc,WAAW,CAAC,CAAC;IAC9B,IAAI,CAACY,sBAAsB,CAAC1B,IAAI,EAAEiC,GAAG,CAAC;IACtC,IAAI,IAAI,CAAC1C,OAAO,CAACqB,GAAG,CAACqB,GAAG,CAAC,EAAE;MACvB,IAAI,CAAC1C,OAAO,CAACwB,GAAG,CAACkB,GAAG,CAAC,CAACI,IAAI,CAACnC,KAAK,CAAC;IACrC,CAAC,MACI;MACD,IAAI,CAACX,OAAO,CAACiC,GAAG,CAACS,GAAG,EAAE,CAAC/B,KAAK,CAAC,CAAC;IAClC;EACJ;EACAS,gBAAgBA,CAACX,IAAI,EAAEU,MAAM,EAAE;IAC3B,MAAM8B,YAAY,GAAG,CAACtB,KAAK,CAACuB,OAAO,CAAC/B,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC,EAAEjC,GAAG,CAAEyB,KAAK,IAAKA,KAAK,CAACwC,QAAQ,CAAC,CAAC,CAAC;IACjG,MAAMT,GAAG,GAAGjC,IAAI,CAACc,WAAW,CAAC,CAAC;IAC9B,IAAI,CAACvB,OAAO,CAACiC,GAAG,CAACS,GAAG,EAAEO,YAAY,CAAC;IACnC,IAAI,CAACd,sBAAsB,CAAC1B,IAAI,EAAEiC,GAAG,CAAC;EAC1C;EACA;AACJ;AACA;EACIrC,OAAOA,CAAC+C,EAAE,EAAE;IACR,IAAI,CAAC9B,IAAI,CAAC,CAAC;IACXK,KAAK,CAACpC,IAAI,CAAC,IAAI,CAACqC,eAAe,CAACF,IAAI,CAAC,CAAC,CAAC,CAACrB,OAAO,CAAEqC,GAAG,IAAKU,EAAE,CAAC,IAAI,CAACxB,eAAe,CAACJ,GAAG,CAACkB,GAAG,CAAC,EAAE,IAAI,CAAC1C,OAAO,CAACwB,GAAG,CAACkB,GAAG,CAAC,CAAC,CAAC;EACtH;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1B,kBAAkBA,CAAChB,OAAO,EAAE;EACjC,KAAK,MAAM,CAAC0C,GAAG,EAAE/B,KAAK,CAAC,IAAIM,MAAM,CAACC,OAAO,CAAClB,OAAO,CAAC,EAAE;IAChD,IAAI,EAAE,OAAOW,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAC,IAAI,CAACgB,KAAK,CAACuB,OAAO,CAACvC,KAAK,CAAC,EAAE;MACpF,MAAM,IAAI0C,KAAK,CAAC,6BAA6BX,GAAG,sBAAsB,GAClE,+DAA+D/B,KAAK,KAAK,CAAC;IAClF;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2C,oBAAoB,CAAC;EACvB;AACJ;AACA;AACA;AACA;EACIC,SAASA,CAACb,GAAG,EAAE;IACX,OAAOc,gBAAgB,CAACd,GAAG,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACIe,WAAWA,CAAC9C,KAAK,EAAE;IACf,OAAO6C,gBAAgB,CAAC7C,KAAK,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACI+C,SAASA,CAAChB,GAAG,EAAE;IACX,OAAOiB,kBAAkB,CAACjB,GAAG,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACIkB,WAAWA,CAACjD,KAAK,EAAE;IACf,OAAOgD,kBAAkB,CAAChD,KAAK,CAAC;EACpC;AACJ;AACA,SAASkD,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAE;EACnC,MAAM7E,GAAG,GAAG,IAAIgB,GAAG,CAAC,CAAC;EACrB,IAAI4D,SAAS,CAACrC,MAAM,GAAG,CAAC,EAAE;IACtB;IACA;IACA;IACA,MAAMuC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC7D,KAAK,CAAC,GAAG,CAAC;IACtD4D,MAAM,CAAC3D,OAAO,CAAE6D,KAAK,IAAK;MACtB,MAAMC,KAAK,GAAGD,KAAK,CAAC1D,OAAO,CAAC,GAAG,CAAC;MAChC,MAAM,CAACkC,GAAG,EAAE0B,GAAG,CAAC,GAAGD,KAAK,IAAI,CAAC,CAAC,GACxB,CAACJ,KAAK,CAACL,SAAS,CAACQ,KAAK,CAAC,EAAE,EAAE,CAAC,GAC5B,CAACH,KAAK,CAACL,SAAS,CAACQ,KAAK,CAACxD,KAAK,CAAC,CAAC,EAAEyD,KAAK,CAAC,CAAC,EAAEJ,KAAK,CAACH,WAAW,CAACM,KAAK,CAACxD,KAAK,CAACyD,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;MACzF,MAAME,IAAI,GAAGnF,GAAG,CAACsC,GAAG,CAACkB,GAAG,CAAC,IAAI,EAAE;MAC/B2B,IAAI,CAACvB,IAAI,CAACsB,GAAG,CAAC;MACdlF,GAAG,CAAC+C,GAAG,CAACS,GAAG,EAAE2B,IAAI,CAAC;IACtB,CAAC,CAAC;EACN;EACA,OAAOnF,GAAG;AACd;AACA;AACA;AACA;AACA,MAAMoF,uBAAuB,GAAG,iBAAiB;AACjD,MAAMC,8BAA8B,GAAG;EACnC,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE;AACV,CAAC;AACD,SAASf,gBAAgBA,CAACgB,CAAC,EAAE;EACzB,OAAOC,kBAAkB,CAACD,CAAC,CAAC,CAACP,OAAO,CAACK,uBAAuB,EAAE,CAACI,CAAC,EAAEC,CAAC;IAAA,IAAAC,qBAAA;IAAA,QAAAA,qBAAA,GAAKL,8BAA8B,CAACI,CAAC,CAAC,cAAAC,qBAAA,cAAAA,qBAAA,GAAIF,CAAC;EAAA,EAAC;AACnH;AACA,SAASG,aAAaA,CAAClE,KAAK,EAAE;EAC1B,OAAO,GAAGA,KAAK,EAAE;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmE,UAAU,CAAC;EAKb/E,WAAWA,CAACgF,OAAO,GAAG,CAAC,CAAC,EAAE;IAAA9E,eAAA;IAAAA,eAAA;IAAAA,eAAA,kBAFhB,IAAI;IAAAA,eAAA,oBACF,IAAI;IAEZ,IAAI,CAAC+E,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,IAAI1B,oBAAoB,CAAC,CAAC;IAC5D,IAAIyB,OAAO,CAACE,UAAU,EAAE;MACpB,IAAIF,OAAO,CAACG,UAAU,EAAE;QACpB,MAAM,IAAInH,aAAa,CAAC,IAAI,CAAC,wEAAwEgD,SAAS,IAAI,gDAAgD,CAAC;MACvK;MACA,IAAI,CAAC7B,GAAG,GAAG2E,WAAW,CAACkB,OAAO,CAACE,UAAU,EAAE,IAAI,CAACD,OAAO,CAAC;IAC5D,CAAC,MACI,IAAI,CAAC,CAACD,OAAO,CAACG,UAAU,EAAE;MAC3B,IAAI,CAAChG,GAAG,GAAG,IAAIgB,GAAG,CAAC,CAAC;MACpBe,MAAM,CAACS,IAAI,CAACqD,OAAO,CAACG,UAAU,CAAC,CAAC7E,OAAO,CAAEqC,GAAG,IAAK;QAC7C,MAAM/B,KAAK,GAAGoE,OAAO,CAACG,UAAU,CAACxC,GAAG,CAAC;QACrC;QACA,MAAMvB,MAAM,GAAGQ,KAAK,CAACuB,OAAO,CAACvC,KAAK,CAAC,GAAGA,KAAK,CAACzB,GAAG,CAAC2F,aAAa,CAAC,GAAG,CAACA,aAAa,CAAClE,KAAK,CAAC,CAAC;QACvF,IAAI,CAACzB,GAAG,CAAC+C,GAAG,CAACS,GAAG,EAAEvB,MAAM,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACjC,GAAG,GAAG,IAAI;IACnB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACImC,GAAGA,CAAC6C,KAAK,EAAE;IACP,IAAI,CAAC5C,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACpC,GAAG,CAACmC,GAAG,CAAC6C,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI1C,GAAGA,CAAC0C,KAAK,EAAE;IACP,IAAI,CAAC5C,IAAI,CAAC,CAAC;IACX,MAAM6D,GAAG,GAAG,IAAI,CAACjG,GAAG,CAACsC,GAAG,CAAC0C,KAAK,CAAC;IAC/B,OAAO,CAAC,CAACiB,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;EACItD,MAAMA,CAACqC,KAAK,EAAE;IACV,IAAI,CAAC5C,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACpC,GAAG,CAACsC,GAAG,CAAC0C,KAAK,CAAC,IAAI,IAAI;EACtC;EACA;AACJ;AACA;AACA;EACIxC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACJ,IAAI,CAAC,CAAC;IACX,OAAOK,KAAK,CAACpC,IAAI,CAAC,IAAI,CAACL,GAAG,CAACwC,IAAI,CAAC,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,MAAMA,CAACoC,KAAK,EAAEvD,KAAK,EAAE;IACjB,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAEmC,KAAK;MAAEvD,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;EACIoD,SAASA,CAACpB,MAAM,EAAE;IACd,MAAMqB,OAAO,GAAG,EAAE;IAClBpE,MAAM,CAACS,IAAI,CAACsC,MAAM,CAAC,CAAC3D,OAAO,CAAE6D,KAAK,IAAK;MACnC,MAAMvD,KAAK,GAAGqD,MAAM,CAACE,KAAK,CAAC;MAC3B,IAAIvC,KAAK,CAACuB,OAAO,CAACvC,KAAK,CAAC,EAAE;QACtBA,KAAK,CAACN,OAAO,CAAEiF,MAAM,IAAK;UACtBD,OAAO,CAACvC,IAAI,CAAC;YAAEoB,KAAK;YAAEvD,KAAK,EAAE2E,MAAM;YAAEtD,EAAE,EAAE;UAAI,CAAC,CAAC;QACnD,CAAC,CAAC;MACN,CAAC,MACI;QACDqD,OAAO,CAACvC,IAAI,CAAC;UAAEoB,KAAK;UAAEvD,KAAK,EAAEA,KAAK;UAAEqB,EAAE,EAAE;QAAI,CAAC,CAAC;MAClD;IACJ,CAAC,CAAC;IACF,OAAO,IAAI,CAACD,KAAK,CAACsD,OAAO,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIpD,GAAGA,CAACiC,KAAK,EAAEvD,KAAK,EAAE;IACd,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAEmC,KAAK;MAAEvD,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,MAAMA,CAACgC,KAAK,EAAEvD,KAAK,EAAE;IACjB,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAEmC,KAAK;MAAEvD,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACImB,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC7B,IAAI,CAAC,CAAC;IACX,OAAQ,IAAI,CAACI,IAAI,CAAC,CAAC,CACdxC,GAAG,CAAEwD,GAAG,IAAK;MACd,MAAM6C,IAAI,GAAG,IAAI,CAACP,OAAO,CAACzB,SAAS,CAACb,GAAG,CAAC;MACxC;MACA;MACA;MACA,OAAO,IAAI,CAACxD,GAAG,CAACsC,GAAG,CAACkB,GAAG,CAAC,CACnBxD,GAAG,CAAEyB,KAAK,IAAK4E,IAAI,GAAG,GAAG,GAAG,IAAI,CAACP,OAAO,CAACvB,WAAW,CAAC9C,KAAK,CAAC,CAAC,CAC5D6E,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IACG;IACA;IAAA,CACCvG,MAAM,CAAEiF,KAAK,IAAKA,KAAK,KAAK,EAAE,CAAC,CAC/BsB,IAAI,CAAC,GAAG,CAAC;EAClB;EACAzD,KAAKA,CAACQ,MAAM,EAAE;IACV,MAAMR,KAAK,GAAG,IAAI+C,UAAU,CAAC;MAAEE,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;IACvDjD,KAAK,CAAC0D,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAI;IACxC1D,KAAK,CAACsD,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO,IAAI,EAAE,EAAE1C,MAAM,CAACJ,MAAM,CAAC;IACnD,OAAOR,KAAK;EAChB;EACAT,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACpC,GAAG,KAAK,IAAI,EAAE;MACnB,IAAI,CAACA,GAAG,GAAG,IAAIgB,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,IAAI,CAACuF,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAACA,SAAS,CAACnE,IAAI,CAAC,CAAC;MACrB,IAAI,CAACmE,SAAS,CAAC/D,IAAI,CAAC,CAAC,CAACrB,OAAO,CAAEqC,GAAG,IAAK,IAAI,CAACxD,GAAG,CAAC+C,GAAG,CAACS,GAAG,EAAE,IAAI,CAAC+C,SAAS,CAACvG,GAAG,CAACsC,GAAG,CAACkB,GAAG,CAAC,CAAC,CAAC;MACtF,IAAI,CAAC2C,OAAO,CAAChF,OAAO,CAAEkC,MAAM,IAAK;QAC7B,QAAQA,MAAM,CAACP,EAAE;UACb,KAAK,GAAG;UACR,KAAK,GAAG;YACJ,MAAMY,IAAI,GAAG,CAACL,MAAM,CAACP,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC9C,GAAG,CAACsC,GAAG,CAACe,MAAM,CAAC2B,KAAK,CAAC,GAAGrB,SAAS,KAAK,EAAE;YAC/ED,IAAI,CAACE,IAAI,CAAC+B,aAAa,CAACtC,MAAM,CAAC5B,KAAK,CAAC,CAAC;YACtC,IAAI,CAACzB,GAAG,CAAC+C,GAAG,CAACM,MAAM,CAAC2B,KAAK,EAAEtB,IAAI,CAAC;YAChC;UACJ,KAAK,GAAG;YACJ,IAAIL,MAAM,CAAC5B,KAAK,KAAKkC,SAAS,EAAE;cAC5B,IAAID,IAAI,GAAG,IAAI,CAAC1D,GAAG,CAACsC,GAAG,CAACe,MAAM,CAAC2B,KAAK,CAAC,IAAI,EAAE;cAC3C,MAAMwB,GAAG,GAAG9C,IAAI,CAACpC,OAAO,CAACqE,aAAa,CAACtC,MAAM,CAAC5B,KAAK,CAAC,CAAC;cACrD,IAAI+E,GAAG,KAAK,CAAC,CAAC,EAAE;gBACZ9C,IAAI,CAAC+C,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;cACvB;cACA,IAAI9C,IAAI,CAACnB,MAAM,GAAG,CAAC,EAAE;gBACjB,IAAI,CAACvC,GAAG,CAAC+C,GAAG,CAACM,MAAM,CAAC2B,KAAK,EAAEtB,IAAI,CAAC;cACpC,CAAC,MACI;gBACD,IAAI,CAAC1D,GAAG,CAACgD,MAAM,CAACK,MAAM,CAAC2B,KAAK,CAAC;cACjC;YACJ,CAAC,MACI;cACD,IAAI,CAAChF,GAAG,CAACgD,MAAM,CAACK,MAAM,CAAC2B,KAAK,CAAC;cAC7B;YACJ;QACR;MACJ,CAAC,CAAC;MACF,IAAI,CAACuB,SAAS,GAAG,IAAI,CAACJ,OAAO,GAAG,IAAI;IACxC;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMO,gBAAgB,CAAC;EAEnB7F,WAAWA,CAAC8F,YAAY,EAAE;IAAA5F,eAAA;IACtB,IAAI,CAAC4F,YAAY,GAAGA,YAAY;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EAAA/F,YAAA;IAAAE,eAAA,cACR,IAAIC,GAAG,CAAC,CAAC;EAAA;EACf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI+B,GAAGA,CAAC8D,KAAK,EAAEpF,KAAK,EAAE;IACd,IAAI,CAACzB,GAAG,CAAC+C,GAAG,CAAC8D,KAAK,EAAEpF,KAAK,CAAC;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIa,GAAGA,CAACuE,KAAK,EAAE;IACP,IAAI,CAAC,IAAI,CAAC7G,GAAG,CAACmC,GAAG,CAAC0E,KAAK,CAAC,EAAE;MACtB,IAAI,CAAC7G,GAAG,CAAC+C,GAAG,CAAC8D,KAAK,EAAEA,KAAK,CAACF,YAAY,CAAC,CAAC,CAAC;IAC7C;IACA,OAAO,IAAI,CAAC3G,GAAG,CAACsC,GAAG,CAACuE,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI7D,MAAMA,CAAC6D,KAAK,EAAE;IACV,IAAI,CAAC7G,GAAG,CAACgD,MAAM,CAAC6D,KAAK,CAAC;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1E,GAAGA,CAAC0E,KAAK,EAAE;IACP,OAAO,IAAI,CAAC7G,GAAG,CAACmC,GAAG,CAAC0E,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;EACIrE,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACxC,GAAG,CAACwC,IAAI,CAAC,CAAC;EAC1B;AACJ;;AAEA;AACA;AACA;AACA,SAASsE,aAAaA,CAACC,MAAM,EAAE;EAC3B,QAAQA,MAAM;IACV,KAAK,QAAQ;IACb,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,SAAS;IACd,KAAK,OAAO;MACR,OAAO,KAAK;IAChB;MACI,OAAO,IAAI;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACvF,KAAK,EAAE;EAC1B,OAAO,OAAOwF,WAAW,KAAK,WAAW,IAAIxF,KAAK,YAAYwF,WAAW;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACzF,KAAK,EAAE;EACnB,OAAO,OAAO0F,IAAI,KAAK,WAAW,IAAI1F,KAAK,YAAY0F,IAAI;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAC3F,KAAK,EAAE;EACvB,OAAO,OAAO4F,QAAQ,KAAK,WAAW,IAAI5F,KAAK,YAAY4F,QAAQ;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAAC7F,KAAK,EAAE;EAC9B,OAAO,OAAO8F,eAAe,KAAK,WAAW,IAAI9F,KAAK,YAAY8F,eAAe;AACrF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,cAAc;AAC1C;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,QAAQ;AAC9B;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,eAAe;AAC5C;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,YAAY;AACtC;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,kBAAkB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAGD,iBAAiB,KAAKD,iBAAiB,OAAO;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,WAAW,CAAC;EA6DdjH,WAAWA,CAACkG,MAAM,EAAEgB,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAAA,IAAAC,aAAA,EAAAC,aAAA;IAAApH,eAAA;IA3DxC;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA,eAOO,IAAI;IACX;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IAPIA,eAAA,yBAQiB,KAAK;IACtB;AACJ;AACA;IAFIA,eAAA,0BAGkB,KAAK;IACvB;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,uBAMe,MAAM;IACrB;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IATIA,eAAA;IAWA;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA;IAKI,IAAI,CAACgH,GAAG,GAAGA,GAAG;IACd,IAAI,CAAChB,MAAM,GAAGA,MAAM,CAACqB,WAAW,CAAC,CAAC;IAClC;IACA;IACA,IAAIvC,OAAO;IACX;IACA;IACA,IAAIiB,aAAa,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC,CAACkB,MAAM,EAAE;MACxC;MACA,IAAI,CAACI,IAAI,GAAGL,KAAK,KAAKrE,SAAS,GAAGqE,KAAK,GAAG,IAAI;MAC9CnC,OAAO,GAAGoC,MAAM;IACpB,CAAC,MACI;MACD;MACApC,OAAO,GAAGmC,KAAK;IACnB;IACA;IACA,IAAInC,OAAO,EAAE;MACT;MACA,IAAI,CAACyC,cAAc,GAAG,CAAC,CAACzC,OAAO,CAACyC,cAAc;MAC9C,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC1C,OAAO,CAAC0C,eAAe;MAChD;MACA,IAAI,CAAC,CAAC1C,OAAO,CAAC2C,YAAY,EAAE;QACxB,IAAI,CAACA,YAAY,GAAG3C,OAAO,CAAC2C,YAAY;MAC5C;MACA;MACA,IAAI,CAAC,CAAC3C,OAAO,CAAC/E,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAG+E,OAAO,CAAC/E,OAAO;MAClC;MACA,IAAI,CAAC,CAAC+E,OAAO,CAAC4C,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAG5C,OAAO,CAAC4C,OAAO;MAClC;MACA,IAAI,CAAC,CAAC5C,OAAO,CAACf,MAAM,EAAE;QAClB,IAAI,CAACA,MAAM,GAAGe,OAAO,CAACf,MAAM;MAChC;MACA;MACA,IAAI,CAAC4D,aAAa,GAAG7C,OAAO,CAAC6C,aAAa;IAC9C;IACA;IACA,CAAAR,aAAA,OAAI,CAACpH,OAAO,cAAAoH,aAAA,cAAAA,aAAA,GAAZ,IAAI,CAACpH,OAAO,GAAK,IAAIF,WAAW,CAAC,CAAC;IAClC;IACA,CAAAuH,aAAA,OAAI,CAACM,OAAO,cAAAN,aAAA,cAAAA,aAAA,GAAZ,IAAI,CAACM,OAAO,GAAK,IAAI7B,WAAW,CAAC,CAAC;IAClC;IACA,IAAI,CAAC,IAAI,CAAC9B,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,IAAIc,UAAU,CAAC,CAAC;MAC9B,IAAI,CAAC+C,aAAa,GAAGZ,GAAG;IAC5B,CAAC,MACI;MACD;MACA,MAAMjD,MAAM,GAAG,IAAI,CAACA,MAAM,CAACb,QAAQ,CAAC,CAAC;MACrC,IAAIa,MAAM,CAACvC,MAAM,KAAK,CAAC,EAAE;QACrB;QACA,IAAI,CAACoG,aAAa,GAAGZ,GAAG;MAC5B,CAAC,MACI;QACD;QACA,MAAMa,IAAI,GAAGb,GAAG,CAACzG,OAAO,CAAC,GAAG,CAAC;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMuH,GAAG,GAAGD,IAAI,KAAK,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,GAAGb,GAAG,CAACxF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;QAChE,IAAI,CAACoG,aAAa,GAAGZ,GAAG,GAAGc,GAAG,GAAG/D,MAAM;MAC3C;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACIgE,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAACT,IAAI,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAI,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,IAC7BrB,aAAa,CAAC,IAAI,CAACqB,IAAI,CAAC,IACxBnB,MAAM,CAAC,IAAI,CAACmB,IAAI,CAAC,IACjBjB,UAAU,CAAC,IAAI,CAACiB,IAAI,CAAC,IACrBf,iBAAiB,CAAC,IAAI,CAACe,IAAI,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACA,IAAI;IACpB;IACA;IACA,IAAI,IAAI,CAACA,IAAI,YAAYzC,UAAU,EAAE;MACjC,OAAO,IAAI,CAACyC,IAAI,CAACpE,QAAQ,CAAC,CAAC;IAC/B;IACA;IACA,IAAI,OAAO,IAAI,CAACoE,IAAI,KAAK,QAAQ,IAC7B,OAAO,IAAI,CAACA,IAAI,KAAK,SAAS,IAC9B5F,KAAK,CAACuB,OAAO,CAAC,IAAI,CAACqE,IAAI,CAAC,EAAE;MAC1B,OAAOU,IAAI,CAACC,SAAS,CAAC,IAAI,CAACX,IAAI,CAAC;IACpC;IACA;IACA,OAAO,IAAI,CAACA,IAAI,CAACpE,QAAQ,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgF,uBAAuBA,CAAA,EAAG;IACtB;IACA,IAAI,IAAI,CAACZ,IAAI,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;IACf;IACA;IACA,IAAIjB,UAAU,CAAC,IAAI,CAACiB,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAInB,MAAM,CAAC,IAAI,CAACmB,IAAI,CAAC,EAAE;MACnB,OAAO,IAAI,CAACA,IAAI,CAACa,IAAI,IAAI,IAAI;IACjC;IACA;IACA,IAAIlC,aAAa,CAAC,IAAI,CAACqB,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAI,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,EAAE;MAC/B,OAAOV,iBAAiB;IAC5B;IACA;IACA,IAAI,IAAI,CAACU,IAAI,YAAYzC,UAAU,EAAE;MACjC,OAAO,iDAAiD;IAC5D;IACA;IACA,IAAI,OAAO,IAAI,CAACyC,IAAI,KAAK,QAAQ,IAC7B,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,IAC7B,OAAO,IAAI,CAACA,IAAI,KAAK,SAAS,EAAE;MAChC,OAAOT,iBAAiB;IAC5B;IACA;IACA,OAAO,IAAI;EACf;EACA/E,KAAKA,CAACQ,MAAM,GAAG,CAAC,CAAC,EAAE;IAAA,IAAA8F,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,eAAA;IACf;IACA;IACA,MAAMvC,MAAM,GAAG1D,MAAM,CAAC0D,MAAM,IAAI,IAAI,CAACA,MAAM;IAC3C,MAAMgB,GAAG,GAAG1E,MAAM,CAAC0E,GAAG,IAAI,IAAI,CAACA,GAAG;IAClC,MAAMS,YAAY,GAAGnF,MAAM,CAACmF,YAAY,IAAI,IAAI,CAACA,YAAY;IAC7D;IACA;IACA,MAAME,aAAa,IAAAS,qBAAA,GAAG9F,MAAM,CAACqF,aAAa,cAAAS,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAACT,aAAa;IAChE;IACA;IACA;IACA;IACA,MAAML,IAAI,GAAGhF,MAAM,CAACgF,IAAI,KAAK1E,SAAS,GAAGN,MAAM,CAACgF,IAAI,GAAG,IAAI,CAACA,IAAI;IAChE;IACA;IACA,MAAME,eAAe,IAAAa,qBAAA,GAAG/F,MAAM,CAACkF,eAAe,cAAAa,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAACb,eAAe;IACtE,MAAMD,cAAc,IAAAe,qBAAA,GAAGhG,MAAM,CAACiF,cAAc,cAAAe,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAACf,cAAc;IACnE;IACA;IACA,IAAIxH,OAAO,GAAGuC,MAAM,CAACvC,OAAO,IAAI,IAAI,CAACA,OAAO;IAC5C,IAAIgE,MAAM,GAAGzB,MAAM,CAACyB,MAAM,IAAI,IAAI,CAACA,MAAM;IACzC;IACA,MAAM2D,OAAO,IAAAa,eAAA,GAAGjG,MAAM,CAACoF,OAAO,cAAAa,eAAA,cAAAA,eAAA,GAAI,IAAI,CAACb,OAAO;IAC9C;IACA,IAAIpF,MAAM,CAACkG,UAAU,KAAK5F,SAAS,EAAE;MACjC;MACA7C,OAAO,GAAGiB,MAAM,CAACS,IAAI,CAACa,MAAM,CAACkG,UAAU,CAAC,CAACC,MAAM,CAAC,CAAC1I,OAAO,EAAES,IAAI,KAAKT,OAAO,CAACiC,GAAG,CAACxB,IAAI,EAAE8B,MAAM,CAACkG,UAAU,CAAChI,IAAI,CAAC,CAAC,EAAET,OAAO,CAAC;IAC3H;IACA;IACA,IAAIuC,MAAM,CAACoG,SAAS,EAAE;MAClB;MACA3E,MAAM,GAAG/C,MAAM,CAACS,IAAI,CAACa,MAAM,CAACoG,SAAS,CAAC,CAACD,MAAM,CAAC,CAAC1E,MAAM,EAAEE,KAAK,KAAKF,MAAM,CAAC/B,GAAG,CAACiC,KAAK,EAAE3B,MAAM,CAACoG,SAAS,CAACzE,KAAK,CAAC,CAAC,EAAEF,MAAM,CAAC;IACxH;IACA;IACA,OAAO,IAAIgD,WAAW,CAACf,MAAM,EAAEgB,GAAG,EAAEM,IAAI,EAAE;MACtCvD,MAAM;MACNhE,OAAO;MACP2H,OAAO;MACPH,cAAc;MACdE,YAAY;MACZD,eAAe;MACfG;IACJ,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIgB,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjD;AACJ;AACA;AACA;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACrE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACrE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EACzE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzD;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACrD,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EA2BnB;AACJ;AACA;AACA;AACA;AACA;EACI9I,WAAWA,CAACuB,IAAI,EAAEwH,aAAa,GAAG,GAAG,EAAEC,iBAAiB,GAAG,IAAI,EAAE;IAhCjE;AACJ;AACA;IAFI9I,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA;IAWI;IACA;IACA,IAAI,CAACD,OAAO,GAAGsB,IAAI,CAACtB,OAAO,IAAI,IAAIF,WAAW,CAAC,CAAC;IAChD,IAAI,CAACkJ,MAAM,GAAG1H,IAAI,CAAC0H,MAAM,KAAKnG,SAAS,GAAGvB,IAAI,CAAC0H,MAAM,GAAGF,aAAa;IACrE,IAAI,CAACG,UAAU,GAAG3H,IAAI,CAAC2H,UAAU,IAAIF,iBAAiB;IACtD,IAAI,CAAC9B,GAAG,GAAG3F,IAAI,CAAC2F,GAAG,IAAI,IAAI;IAC3B;IACA,IAAI,CAACiC,EAAE,GAAG,IAAI,CAACF,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG;EACrD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,kBAAkB,SAASN,gBAAgB,CAAC;EAC9C;AACJ;AACA;EACI9I,WAAWA,CAACuB,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,KAAK,CAACA,IAAI,CAAC;IAACrB,eAAA,eAET2I,aAAa,CAACQ,cAAc;EADnC;EAEA;AACJ;AACA;AACA;EACIrH,KAAKA,CAACQ,MAAM,GAAG,CAAC,CAAC,EAAE;IACf;IACA;IACA,OAAO,IAAI4G,kBAAkB,CAAC;MAC1BnJ,OAAO,EAAEuC,MAAM,CAACvC,OAAO,IAAI,IAAI,CAACA,OAAO;MACvCgJ,MAAM,EAAEzG,MAAM,CAACyG,MAAM,KAAKnG,SAAS,GAAGN,MAAM,CAACyG,MAAM,GAAG,IAAI,CAACA,MAAM;MACjEC,UAAU,EAAE1G,MAAM,CAAC0G,UAAU,IAAI,IAAI,CAACA,UAAU;MAChDhC,GAAG,EAAE1E,MAAM,CAAC0E,GAAG,IAAI,IAAI,CAACA,GAAG,IAAIpE;IACnC,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwG,YAAY,SAASR,gBAAgB,CAAC;EAKxC;AACJ;AACA;EACI9I,WAAWA,CAACuB,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,KAAK,CAACA,IAAI,CAAC;IARf;AACJ;AACA;IAFIrB,eAAA;IAAAA,eAAA,eAWO2I,aAAa,CAACU,QAAQ;IAFzB,IAAI,CAAC/B,IAAI,GAAGjG,IAAI,CAACiG,IAAI,KAAK1E,SAAS,GAAGvB,IAAI,CAACiG,IAAI,GAAG,IAAI;EAC1D;EAEAxF,KAAKA,CAACQ,MAAM,GAAG,CAAC,CAAC,EAAE;IACf,OAAO,IAAI8G,YAAY,CAAC;MACpB9B,IAAI,EAAEhF,MAAM,CAACgF,IAAI,KAAK1E,SAAS,GAAGN,MAAM,CAACgF,IAAI,GAAG,IAAI,CAACA,IAAI;MACzDvH,OAAO,EAAEuC,MAAM,CAACvC,OAAO,IAAI,IAAI,CAACA,OAAO;MACvCgJ,MAAM,EAAEzG,MAAM,CAACyG,MAAM,KAAKnG,SAAS,GAAGN,MAAM,CAACyG,MAAM,GAAG,IAAI,CAACA,MAAM;MACjEC,UAAU,EAAE1G,MAAM,CAAC0G,UAAU,IAAI,IAAI,CAACA,UAAU;MAChDhC,GAAG,EAAE1E,MAAM,CAAC0E,GAAG,IAAI,IAAI,CAACA,GAAG,IAAIpE;IACnC,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0G,iBAAiB,SAASV,gBAAgB,CAAC;EAQ7C9I,WAAWA,CAACuB,IAAI,EAAE;IACd;IACA,KAAK,CAACA,IAAI,EAAE,CAAC,EAAE,eAAe,CAAC;IAC/B;IACA;IACA;IAAArB,eAAA,eAZG,mBAAmB;IAAAA,eAAA;IAAAA,eAAA;IAG1B;AACJ;AACA;IAFIA,eAAA,aAGK,KAAK;IAON,IAAI,IAAI,CAAC+I,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG,EAAE;MACzC,IAAI,CAACQ,OAAO,GAAG,mCAAmClI,IAAI,CAAC2F,GAAG,IAAI,eAAe,EAAE;IACnF,CAAC,MACI;MACD,IAAI,CAACuC,OAAO,GAAG,6BAA6BlI,IAAI,CAAC2F,GAAG,IAAI,eAAe,KAAK3F,IAAI,CAAC0H,MAAM,IAAI1H,IAAI,CAAC2H,UAAU,EAAE;IAChH;IACA,IAAI,CAACQ,KAAK,GAAGnI,IAAI,CAACmI,KAAK,IAAI,IAAI;EACnC;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAG;AAC/B,MAAMC,2BAA2B,GAAG,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;EACjDA,cAAc,CAACA,cAAc,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS;EAC3DA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,6BAA6B;EACnGA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW;EAC/DA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,cAAc;EACrEA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACnEA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;EACzDA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EAC7EA,cAAc,CAACA,cAAc,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO;EACvDA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACnEA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;EACzDA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,mBAAmB;EAC/EA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,mBAAmB;EAC/EA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,cAAc;EACrEA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW;EAC/DA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EAC7EA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC,GAAG,eAAe;EACvEA,cAAc,CAACA,cAAc,CAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,6BAA6B;EACnGA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM;EACrDA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,sBAAsB,CAAC,GAAG,GAAG,CAAC,GAAG,sBAAsB;EACrFA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,mBAAmB;EAC/EA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW;EAC/DA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;EACzDA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EAC7EA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,sBAAsB,CAAC,GAAG,GAAG,CAAC,GAAG,sBAAsB;EACrFA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,6BAA6B;EACnGA,cAAc,CAACA,cAAc,CAAC,4BAA4B,CAAC,GAAG,GAAG,CAAC,GAAG,4BAA4B;EACjGA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC,GAAG,yBAAyB;EAC3FA,cAAc,CAACA,cAAc,CAAC,uBAAuB,CAAC,GAAG,GAAG,CAAC,GAAG,uBAAuB;EACvFA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,cAAc;EACrEA,cAAc,CAACA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACnEA,cAAc,CAACA,cAAc,CAAC,+BAA+B,CAAC,GAAG,GAAG,CAAC,GAAG,+BAA+B;AAC3G,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAAC9E,OAAO,EAAEwC,IAAI,EAAE;EAC5B,OAAO;IACHA,IAAI;IACJvH,OAAO,EAAE+E,OAAO,CAAC/E,OAAO;IACxB2H,OAAO,EAAE5C,OAAO,CAAC4C,OAAO;IACxBmC,OAAO,EAAE/E,OAAO,CAAC+E,OAAO;IACxB9F,MAAM,EAAEe,OAAO,CAACf,MAAM;IACtBwD,cAAc,EAAEzC,OAAO,CAACyC,cAAc;IACtCE,YAAY,EAAE3C,OAAO,CAAC2C,YAAY;IAClCD,eAAe,EAAE1C,OAAO,CAAC0C,eAAe;IACxCG,aAAa,EAAE7C,OAAO,CAAC6C;EAC3B,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmC,UAAU,CAAC;EAEbhK,WAAWA,CAACiK,OAAO,EAAE;IAAA/J,eAAA;IACjB,IAAI,CAAC+J,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,KAAK,EAAEjD,GAAG,EAAElC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAIoF,GAAG;IACP;IACA,IAAID,KAAK,YAAYlD,WAAW,EAAE;MAC9B;MACA;MACAmD,GAAG,GAAGD,KAAK;IACf,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,IAAIlK,OAAO,GAAG6C,SAAS;MACvB,IAAIkC,OAAO,CAAC/E,OAAO,YAAYF,WAAW,EAAE;QACxCE,OAAO,GAAG+E,OAAO,CAAC/E,OAAO;MAC7B,CAAC,MACI;QACDA,OAAO,GAAG,IAAIF,WAAW,CAACiF,OAAO,CAAC/E,OAAO,CAAC;MAC9C;MACA;MACA,IAAIgE,MAAM,GAAGnB,SAAS;MACtB,IAAI,CAAC,CAACkC,OAAO,CAACf,MAAM,EAAE;QAClB,IAAIe,OAAO,CAACf,MAAM,YAAYc,UAAU,EAAE;UACtCd,MAAM,GAAGe,OAAO,CAACf,MAAM;QAC3B,CAAC,MACI;UACDA,MAAM,GAAG,IAAIc,UAAU,CAAC;YAAEI,UAAU,EAAEH,OAAO,CAACf;UAAO,CAAC,CAAC;QAC3D;MACJ;MACA;MACAmG,GAAG,GAAG,IAAInD,WAAW,CAACkD,KAAK,EAAEjD,GAAG,EAAElC,OAAO,CAACwC,IAAI,KAAK1E,SAAS,GAAGkC,OAAO,CAACwC,IAAI,GAAG,IAAI,EAAE;QAChFvH,OAAO;QACP2H,OAAO,EAAE5C,OAAO,CAAC4C,OAAO;QACxB3D,MAAM;QACNwD,cAAc,EAAEzC,OAAO,CAACyC,cAAc;QACtC;QACAE,YAAY,EAAE3C,OAAO,CAAC2C,YAAY,IAAI,MAAM;QAC5CD,eAAe,EAAE1C,OAAO,CAAC0C,eAAe;QACxCG,aAAa,EAAE7C,OAAO,CAAC6C;MAC3B,CAAC,CAAC;IACN;IACA;IACA;IACA;IACA;IACA,MAAMwC,OAAO,GAAG/K,EAAE,CAAC8K,GAAG,CAAC,CAACE,IAAI,CAACrL,SAAS,CAAEmL,GAAG,IAAK,IAAI,CAACH,OAAO,CAACM,MAAM,CAACH,GAAG,CAAC,CAAC,CAAC;IAC1E;IACA;IACA;IACA,IAAID,KAAK,YAAYlD,WAAW,IAAIjC,OAAO,CAAC+E,OAAO,KAAK,QAAQ,EAAE;MAC9D,OAAOM,OAAO;IAClB;IACA;IACA;IACA;IACA,MAAMG,IAAI,GAAIH,OAAO,CAACC,IAAI,CAACpL,MAAM,CAAEuL,KAAK,IAAKA,KAAK,YAAYnB,YAAY,CAAC,CAAE;IAC7E;IACA,QAAQtE,OAAO,CAAC+E,OAAO,IAAI,MAAM;MAC7B,KAAK,MAAM;QACP;QACA;QACA;QACA;QACA;QACA,QAAQK,GAAG,CAACzC,YAAY;UACpB,KAAK,aAAa;YACd,OAAO6C,IAAI,CAACF,IAAI,CAACnL,GAAG,CAAEiG,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAACoC,IAAI,KAAK,IAAI,IAAI,EAAEpC,GAAG,CAACoC,IAAI,YAAYpB,WAAW,CAAC,EAAE;gBACzD,MAAM,IAAIpI,aAAa,CAAC,IAAI,CAAC,wDAAwDgD,SAAS,IAAI,iCAAiC,CAAC;cACxI;cACA,OAAOoE,GAAG,CAACoC,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;YACP,OAAOgD,IAAI,CAACF,IAAI,CAACnL,GAAG,CAAEiG,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAACoC,IAAI,KAAK,IAAI,IAAI,EAAEpC,GAAG,CAACoC,IAAI,YAAYlB,IAAI,CAAC,EAAE;gBAClD,MAAM,IAAItI,aAAa,CAAC,IAAI,CAAC,+CAA+CgD,SAAS,IAAI,yBAAyB,CAAC;cACvH;cACA,OAAOoE,GAAG,CAACoC,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;YACP,OAAOgD,IAAI,CAACF,IAAI,CAACnL,GAAG,CAAEiG,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAACoC,IAAI,KAAK,IAAI,IAAI,OAAOpC,GAAG,CAACoC,IAAI,KAAK,QAAQ,EAAE;gBACnD,MAAM,IAAIxJ,aAAa,CAAC,IAAI,CAAC,iDAAiDgD,SAAS,IAAI,2BAA2B,CAAC;cAC3H;cACA,OAAOoE,GAAG,CAACoC,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;UACX;YACI;YACA,OAAOgD,IAAI,CAACF,IAAI,CAACnL,GAAG,CAAEiG,GAAG,IAAKA,GAAG,CAACoC,IAAI,CAAC,CAAC;QAChD;MACJ,KAAK,UAAU;QACX;QACA,OAAOgD,IAAI;MACf;QACI;QACA,MAAM,IAAIxM,aAAa,CAAC,IAAI,CAAC,+CAA+CgD,SAAS,IAAI,uCAAuCgE,OAAO,CAAC+E,OAAO,GAAG,CAAC;IAC3J;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5H,MAAMA,CAAC+E,GAAG,EAAElC,OAAO,GAAG,CAAC,CAAC,EAAE;IACtB,OAAO,IAAI,CAACkF,OAAO,CAAC,QAAQ,EAAEhD,GAAG,EAAElC,OAAO,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;EACIvD,GAAGA,CAACyF,GAAG,EAAElC,OAAO,GAAG,CAAC,CAAC,EAAE;IACnB,OAAO,IAAI,CAACkF,OAAO,CAAC,KAAK,EAAEhD,GAAG,EAAElC,OAAO,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0F,IAAIA,CAACxD,GAAG,EAAElC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpB,OAAO,IAAI,CAACkF,OAAO,CAAC,MAAM,EAAEhD,GAAG,EAAElC,OAAO,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2F,KAAKA,CAACzD,GAAG,EAAE0D,aAAa,EAAE;IACtB,OAAO,IAAI,CAACV,OAAO,CAAC,OAAO,EAAEhD,GAAG,EAAE;MAC9BjD,MAAM,EAAE,IAAIc,UAAU,CAAC,CAAC,CAAChD,MAAM,CAAC6I,aAAa,EAAE,gBAAgB,CAAC;MAChEb,OAAO,EAAE,MAAM;MACfpC,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI3C,OAAOA,CAACkC,GAAG,EAAElC,OAAO,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACkF,OAAO,CAAC,SAAS,EAAEhD,GAAG,EAAElC,OAAO,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;EACI6F,KAAKA,CAAC3D,GAAG,EAAEM,IAAI,EAAExC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3B,OAAO,IAAI,CAACkF,OAAO,CAAC,OAAO,EAAEhD,GAAG,EAAE4C,OAAO,CAAC9E,OAAO,EAAEwC,IAAI,CAAC,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsD,IAAIA,CAAC5D,GAAG,EAAEM,IAAI,EAAExC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1B,OAAO,IAAI,CAACkF,OAAO,CAAC,MAAM,EAAEhD,GAAG,EAAE4C,OAAO,CAAC9E,OAAO,EAAEwC,IAAI,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuD,GAAGA,CAAC7D,GAAG,EAAEM,IAAI,EAAExC,OAAO,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO,IAAI,CAACkF,OAAO,CAAC,KAAK,EAAEhD,GAAG,EAAE4C,OAAO,CAAC9E,OAAO,EAAEwC,IAAI,CAAC,CAAC;EAC3D;AAGJ;AAACwD,WAAA,GAnOKhB,UAAU;AAAA9J,eAAA,CAAV8J,UAAU,wBAAAiB,oBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAiOwFlB,WAAU,EAGhClM,EAAE,CAAAqN,QAAA,CAHgDtL,WAAW;AAAA;AAAAK,eAAA,CAjOzI8J,UAAU,+BAoOkElM,EAAE,CAAAsN,kBAAA;EAAApF,KAAA,EAFwBgE,WAAU;EAAAqB,OAAA,EAAVrB,WAAU,CAAAsB;AAAA;AAEtH;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KAAkFlD,EAAE,CAAAyN,iBAAA,CAAQvB,UAAU,EAAc,CAAC;IACzG3B,IAAI,EAAEpK;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoK,IAAI,EAAExI;EAAY,CAAC,CAAC;AAAA;AAEzD,MAAM2L,aAAa,GAAG,cAAc;AACpC;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EAChC,IAAIA,QAAQ,CAACxE,GAAG,EAAE;IACd,OAAOwE,QAAQ,CAACxE,GAAG;EACvB;EACA;EACA,MAAMyE,WAAW,GAAG9E,oBAAoB,CAAC+E,iBAAiB,CAAC,CAAC;EAC5D,OAAOF,QAAQ,CAACzL,OAAO,CAACwB,GAAG,CAACkK,WAAW,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA,MAAME,aAAa,GAAG,IAAIxN,cAAc,CAAC,OAAO2C,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,eAAe,GAAG,EAAE,CAAC;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8K,YAAY,CAAC;EAQf9L,WAAWA,CAAA,EAAG;IAAA,IAAA+L,aAAA,EAAAC,OAAA;IAPd;IACA;IACA;IAAA9L,eAAA,qBAAA6L,aAAA,IAAAC,OAAA,GACY9N,MAAM,CAAC+N,YAAY,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,cAAAF,OAAA,uBAAxCA,OAAA,CAA0CG,KAAK,cAAAJ,aAAA,cAAAA,aAAA,GAAK,CAAC,GAAGK,IAAI,KAAKC,UAAU,CAACF,KAAK,CAAC,GAAGC,IAAI,CAAC;IAAAlM,eAAA,iBAC7FhC,MAAM,CAACC,MAAM,CAAC;IAAA+B,eAAA,qBACVhC,MAAM,CAACE,UAAU,CAAC;IAAA8B,eAAA,oBACnB,KAAK;IAEb,IAAI,CAACoM,UAAU,CAACC,SAAS,CAAC,MAAM;MAC5B,IAAI,CAACC,SAAS,GAAG,IAAI;IACzB,CAAC,CAAC;EACN;EACAjC,MAAMA,CAACL,OAAO,EAAE;IACZ,OAAO,IAAI3K,UAAU,CAAEkN,QAAQ,IAAK;MAChC,MAAMC,OAAO,GAAG,IAAIC,eAAe,CAAC,CAAC;MACrC,IAAI,CAACC,SAAS,CAAC1C,OAAO,EAAEwC,OAAO,CAACG,MAAM,EAAEJ,QAAQ,CAAC,CAACK,IAAI,CAACC,IAAI,EAAGrD,KAAK,IAAK+C,QAAQ,CAAC/C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;QAAEE;MAAM,CAAC,CAAC,CAAC,CAAC;MACzH,OAAO,MAAMgD,OAAO,CAACM,KAAK,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACMJ,SAASA,CAAC1C,OAAO,EAAE2C,MAAM,EAAEJ,QAAQ,EAAE;IAAA,IAAAQ,KAAA;IAAA,OAAAC,iBAAA;MAAA,IAAAC,gBAAA;MACvC,MAAM5L,IAAI,GAAG0L,KAAI,CAACG,iBAAiB,CAAClD,OAAO,CAAC;MAC5C,IAAIwB,QAAQ;MACZ,IAAI;QACA;QACA;QACA;QACA,MAAM2B,YAAY,GAAGJ,KAAI,CAACK,MAAM,CAACC,iBAAiB,CAAC,MAAMN,KAAI,CAACO,SAAS,CAACtD,OAAO,CAACpC,aAAa,EAAA2F,aAAA;UAAIZ;QAAM,GAAKtL,IAAI,CAAE,CAAC,CAAC;QACpH;QACA;QACA;QACAmM,2CAA2C,CAACL,YAAY,CAAC;QACzD;QACAZ,QAAQ,CAACkB,IAAI,CAAC;UAAEtF,IAAI,EAAEQ,aAAa,CAAC+E;QAAK,CAAC,CAAC;QAC3ClC,QAAQ,SAAS2B,YAAY;MACjC,CAAC,CACD,OAAO3D,KAAK,EAAE;QAAA,IAAAmE,aAAA;QACVpB,QAAQ,CAAC/C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;UACjCE,KAAK;UACLT,MAAM,GAAA4E,aAAA,GAAEnE,KAAK,CAACT,MAAM,cAAA4E,aAAA,cAAAA,aAAA,GAAI,CAAC;UACzB3E,UAAU,EAAEQ,KAAK,CAACR,UAAU;UAC5BhC,GAAG,EAAEgD,OAAO,CAACpC,aAAa;UAC1B7H,OAAO,EAAEyJ,KAAK,CAACzJ;QACnB,CAAC,CAAC,CAAC;QACH;MACJ;MACA,MAAMA,OAAO,GAAG,IAAIF,WAAW,CAAC2L,QAAQ,CAACzL,OAAO,CAAC;MACjD,MAAMiJ,UAAU,GAAGwC,QAAQ,CAACxC,UAAU;MACtC,MAAMhC,GAAG,IAAAiG,gBAAA,GAAG1B,gBAAgB,CAACC,QAAQ,CAAC,cAAAyB,gBAAA,cAAAA,gBAAA,GAAIjD,OAAO,CAACpC,aAAa;MAC/D,IAAImB,MAAM,GAAGyC,QAAQ,CAACzC,MAAM;MAC5B,IAAIzB,IAAI,GAAG,IAAI;MACf,IAAI0C,OAAO,CAACzC,cAAc,EAAE;QACxBgF,QAAQ,CAACkB,IAAI,CAAC,IAAIvE,kBAAkB,CAAC;UAAEnJ,OAAO;UAAEgJ,MAAM;UAAEC,UAAU;UAAEhC;QAAI,CAAC,CAAC,CAAC;MAC/E;MACA,IAAIwE,QAAQ,CAAClE,IAAI,EAAE;QACf;QACA,MAAMsG,aAAa,GAAGpC,QAAQ,CAACzL,OAAO,CAACwB,GAAG,CAAC,gBAAgB,CAAC;QAC5D,MAAMsM,MAAM,GAAG,EAAE;QACjB,MAAMC,MAAM,GAAGtC,QAAQ,CAAClE,IAAI,CAACyG,SAAS,CAAC,CAAC;QACxC,IAAIC,cAAc,GAAG,CAAC;QACtB,IAAIC,OAAO;QACX,IAAIC,WAAW;QACf;QACA;QACA,MAAMC,OAAO,GAAG,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACC,OAAO;QAC3D,IAAIC,QAAQ,GAAG,KAAK;QACpB;QACA;QACA;QACA,MAAMvB,KAAI,CAACK,MAAM,CAACC,iBAAiB,cAAAL,iBAAA,CAAC,aAAY;UAC5C,OAAO,IAAI,EAAE;YACT;YACA;YACA;YACA;YACA,IAAID,KAAI,CAACT,SAAS,EAAE;cAChB;cACA;cACA;cACA;cACA,MAAMwB,MAAM,CAACS,MAAM,CAAC,CAAC;cACrBD,QAAQ,GAAG,IAAI;cACf;YACJ;YACA,MAAM;cAAEE,IAAI;cAAE9N;YAAM,CAAC,SAASoN,MAAM,CAACW,IAAI,CAAC,CAAC;YAC3C,IAAID,IAAI,EAAE;cACN;YACJ;YACAX,MAAM,CAAChL,IAAI,CAACnC,KAAK,CAAC;YAClBsN,cAAc,IAAItN,KAAK,CAACc,MAAM;YAC9B,IAAIwI,OAAO,CAACzC,cAAc,EAAE;cACxB2G,WAAW,GACPlE,OAAO,CAACvC,YAAY,KAAK,MAAM,GACzB,CAACyG,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,EAAE,IAChB,CAACD,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAPA,OAAO,GAAK,IAAIS,WAAW,CAAC,CAAC,EAAEC,MAAM,CAACjO,KAAK,EAAE;gBAAEkO,MAAM,EAAE;cAAK,CAAC,CAAC,GACjEhM,SAAS;cACnB,MAAM2E,cAAc,GAAGA,CAAA,KAAMgF,QAAQ,CAACkB,IAAI,CAAC;gBACvCtF,IAAI,EAAEQ,aAAa,CAACkG,gBAAgB;gBACpCC,KAAK,EAAElB,aAAa,GAAG,CAACA,aAAa,GAAGhL,SAAS;gBACjDmM,MAAM,EAAEf,cAAc;gBACtBE;cACJ,CAAC,CAAC;cACFC,OAAO,GAAGA,OAAO,CAACa,GAAG,CAACzH,cAAc,CAAC,GAAGA,cAAc,CAAC,CAAC;YAC5D;UACJ;QACJ,CAAC,EAAC;QACF;QACA;QACA;QACA;QACA,IAAI+G,QAAQ,EAAE;UACV/B,QAAQ,CAAC0C,QAAQ,CAAC,CAAC;UACnB;QACJ;QACA;QACA,MAAMC,SAAS,GAAGnC,KAAI,CAACoC,YAAY,CAACtB,MAAM,EAAEG,cAAc,CAAC;QAC3D,IAAI;UAAA,IAAAoB,qBAAA;UACA,MAAMC,WAAW,IAAAD,qBAAA,GAAG5D,QAAQ,CAACzL,OAAO,CAACwB,GAAG,CAACkF,mBAAmB,CAAC,cAAA2I,qBAAA,cAAAA,qBAAA,GAAI,EAAE;UACnE9H,IAAI,GAAGyF,KAAI,CAACuC,SAAS,CAACtF,OAAO,EAAEkF,SAAS,EAAEG,WAAW,CAAC;QAC1D,CAAC,CACD,OAAO7F,KAAK,EAAE;UAAA,IAAA+F,iBAAA;UACV;UACAhD,QAAQ,CAAC/C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;YACjCE,KAAK;YACLzJ,OAAO,EAAE,IAAIF,WAAW,CAAC2L,QAAQ,CAACzL,OAAO,CAAC;YAC1CgJ,MAAM,EAAEyC,QAAQ,CAACzC,MAAM;YACvBC,UAAU,EAAEwC,QAAQ,CAACxC,UAAU;YAC/BhC,GAAG,GAAAuI,iBAAA,GAAEhE,gBAAgB,CAACC,QAAQ,CAAC,cAAA+D,iBAAA,cAAAA,iBAAA,GAAIvF,OAAO,CAACpC;UAC/C,CAAC,CAAC,CAAC;UACH;QACJ;MACJ;MACA;MACA,IAAImB,MAAM,KAAK,CAAC,EAAE;QACdA,MAAM,GAAGzB,IAAI,GAAGmC,mBAAmB,GAAG,CAAC;MAC3C;MACA;MACA;MACA;MACA;MACA,MAAMR,EAAE,GAAGF,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;MACxC,IAAIE,EAAE,EAAE;QACJsD,QAAQ,CAACkB,IAAI,CAAC,IAAIrE,YAAY,CAAC;UAC3B9B,IAAI;UACJvH,OAAO;UACPgJ,MAAM;UACNC,UAAU;UACVhC;QACJ,CAAC,CAAC,CAAC;QACH;QACA;QACAuF,QAAQ,CAAC0C,QAAQ,CAAC,CAAC;MACvB,CAAC,MACI;QACD1C,QAAQ,CAAC/C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;UACjCE,KAAK,EAAElC,IAAI;UACXvH,OAAO;UACPgJ,MAAM;UACNC,UAAU;UACVhC;QACJ,CAAC,CAAC,CAAC;MACP;IAAC;EACL;EACAsI,SAASA,CAACtF,OAAO,EAAEwF,UAAU,EAAEH,WAAW,EAAE;IACxC,QAAQrF,OAAO,CAACvC,YAAY;MACxB,KAAK,MAAM;QACP;QACA,MAAMgI,IAAI,GAAG,IAAIf,WAAW,CAAC,CAAC,CAACC,MAAM,CAACa,UAAU,CAAC,CAACxL,OAAO,CAACsH,aAAa,EAAE,EAAE,CAAC;QAC5E,OAAOmE,IAAI,KAAK,EAAE,GAAG,IAAI,GAAGzH,IAAI,CAAC0H,KAAK,CAACD,IAAI,CAAC;MAChD,KAAK,MAAM;QACP,OAAO,IAAIf,WAAW,CAAC,CAAC,CAACC,MAAM,CAACa,UAAU,CAAC;MAC/C,KAAK,MAAM;QACP,OAAO,IAAIpJ,IAAI,CAAC,CAACoJ,UAAU,CAAC,EAAE;UAAErH,IAAI,EAAEkH;QAAY,CAAC,CAAC;MACxD,KAAK,aAAa;QACd,OAAOG,UAAU,CAACG,MAAM;IAChC;EACJ;EACAzC,iBAAiBA,CAAChD,GAAG,EAAE;IACnB;IACA,MAAMnK,OAAO,GAAG,CAAC,CAAC;IAClB,MAAM6P,WAAW,GAAG1F,GAAG,CAAC1C,eAAe,GAAG,SAAS,GAAG5E,SAAS;IAC/D;IACAsH,GAAG,CAACnK,OAAO,CAACK,OAAO,CAAC,CAACI,IAAI,EAAEU,MAAM,KAAMnB,OAAO,CAACS,IAAI,CAAC,GAAGU,MAAM,CAACqE,IAAI,CAAC,GAAG,CAAE,CAAC;IACzE;IACA,IAAI,CAAC2E,GAAG,CAACnK,OAAO,CAACqB,GAAG,CAACsF,aAAa,CAAC,EAAE;MACjC3G,OAAO,CAAC2G,aAAa,CAAC,GAAGI,mBAAmB;IAChD;IACA;IACA,IAAI,CAACoD,GAAG,CAACnK,OAAO,CAACqB,GAAG,CAACqF,mBAAmB,CAAC,EAAE;MACvC,MAAMoJ,YAAY,GAAG3F,GAAG,CAAChC,uBAAuB,CAAC,CAAC;MAClD;MACA,IAAI2H,YAAY,KAAK,IAAI,EAAE;QACvB9P,OAAO,CAAC0G,mBAAmB,CAAC,GAAGoJ,YAAY;MAC/C;IACJ;IACA,OAAO;MACHvI,IAAI,EAAE4C,GAAG,CAACnC,aAAa,CAAC,CAAC;MACzB/B,MAAM,EAAEkE,GAAG,CAAClE,MAAM;MAClBjG,OAAO;MACP6P;IACJ,CAAC;EACL;EACAT,YAAYA,CAACtB,MAAM,EAAEiC,WAAW,EAAE;IAC9B,MAAMZ,SAAS,GAAG,IAAIa,UAAU,CAACD,WAAW,CAAC;IAC7C,IAAIE,QAAQ,GAAG,CAAC;IAChB,KAAK,MAAMC,KAAK,IAAIpC,MAAM,EAAE;MACxBqB,SAAS,CAAClN,GAAG,CAACiO,KAAK,EAAED,QAAQ,CAAC;MAC9BA,QAAQ,IAAIC,KAAK,CAACzO,MAAM;IAC5B;IACA,OAAO0N,SAAS;EACpB;AAGJ;AAACgB,aAAA,GArNKtE,YAAY;AAAA5L,eAAA,CAAZ4L,YAAY,wBAAAuE,sBAAAnF,iBAAA;EAAA,YAAAA,iBAAA,IAmNsFY,aAAY;AAAA;AAAA5L,eAAA,CAnN9G4L,YAAY,+BAjCgEhO,EAAE,CAAAsN,kBAAA;EAAApF,KAAA,EAqPwB8F,aAAY;EAAAT,OAAA,EAAZS,aAAY,CAAAR;AAAA;AAExH;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KAvPkFlD,EAAE,CAAAyN,iBAAA,CAuPQO,YAAY,EAAc,CAAC;IAC3GzD,IAAI,EAAEpK;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA,MAAMgO,YAAY,CAAC;AAEnB,SAASc,IAAIA,CAAA,EAAG,CAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,2CAA2CA,CAAC4C,OAAO,EAAE;EAC1DA,OAAO,CAACxD,IAAI,CAACC,IAAI,EAAEA,IAAI,CAAC;AAC5B;AAEA,SAASwD,qBAAqBA,CAACnG,GAAG,EAAEoG,cAAc,EAAE;EAChD,OAAOA,cAAc,CAACpG,GAAG,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA,SAASqG,6BAA6BA,CAACC,WAAW,EAAEC,WAAW,EAAE;EAC7D,OAAO,CAACC,cAAc,EAAEJ,cAAc,KAAKG,WAAW,CAACE,SAAS,CAACD,cAAc,EAAE;IAC7ErG,MAAM,EAAGuG,iBAAiB,IAAKJ,WAAW,CAACI,iBAAiB,EAAEN,cAAc;EAChF,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA,SAASO,oBAAoBA,CAACL,WAAW,EAAEM,aAAa,EAAEC,QAAQ,EAAE;EAChE,OAAO,CAACL,cAAc,EAAEJ,cAAc,KAAK3R,qBAAqB,CAACoS,QAAQ,EAAE,MAAMD,aAAa,CAACJ,cAAc,EAAGE,iBAAiB,IAAKJ,WAAW,CAACI,iBAAiB,EAAEN,cAAc,CAAC,CAAC,CAAC;AAC1L;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,iBAAiB,GAAG,IAAI7S,cAAc,CAAC2C,SAAS,GAAG,mBAAmB,GAAG,EAAE,CAAC;AAClF;AACA;AACA;AACA,MAAMmQ,oBAAoB,GAAG,IAAI9S,cAAc,CAAC2C,SAAS,GAAG,sBAAsB,GAAG,EAAE,CAAC;AACxF;AACA;AACA;AACA,MAAMoQ,yBAAyB,GAAG,IAAI/S,cAAc,CAAC2C,SAAS,GAAG,2BAA2B,GAAG,EAAE,CAAC;AAClG;AACA;AACA;AACA,MAAMqQ,gCAAgC,GAAG,IAAIhT,cAAc,CAAC2C,SAAS,GAAG,kCAAkC,GAAG,EAAE,EAAE;EAAEsQ,UAAU,EAAE,MAAM;EAAEjG,OAAO,EAAEA,CAAA,KAAM;AAAK,CAAC,CAAC;AAC7J;AACA;AACA;AACA;AACA,SAASkG,0BAA0BA,CAAA,EAAG;EAClC,IAAIC,KAAK,GAAG,IAAI;EAChB,OAAO,CAACpH,GAAG,EAAEH,OAAO,KAAK;IACrB,IAAIuH,KAAK,KAAK,IAAI,EAAE;MAAA,IAAAC,QAAA;MAChB,MAAMC,YAAY,IAAAD,QAAA,GAAGvT,MAAM,CAACgT,iBAAiB,EAAE;QAAEhF,QAAQ,EAAE;MAAK,CAAC,CAAC,cAAAuF,QAAA,cAAAA,QAAA,GAAI,EAAE;MACxE;MACA;MACA;MACA;MACAD,KAAK,GAAGE,YAAY,CAACC,WAAW,CAAClB,6BAA6B,EAAEF,qBAAqB,CAAC;IAC1F;IACA,MAAMqB,YAAY,GAAG1T,MAAM,CAACK,qBAAqB,CAAC;IAClD,MAAMsT,qBAAqB,GAAG3T,MAAM,CAACmT,gCAAgC,CAAC;IACtE,IAAIQ,qBAAqB,EAAE;MACvB,MAAMC,MAAM,GAAGF,YAAY,CAACG,GAAG,CAAC,CAAC;MACjC,OAAOP,KAAK,CAACpH,GAAG,EAAEH,OAAO,CAAC,CAACK,IAAI,CAAClL,QAAQ,CAAC,MAAMwS,YAAY,CAACI,MAAM,CAACF,MAAM,CAAC,CAAC,CAAC;IAChF,CAAC,MACI;MACD,OAAON,KAAK,CAACpH,GAAG,EAAEH,OAAO,CAAC;IAC9B;EACJ,CAAC;AACL;AACA,IAAIgI,4BAA4B,GAAG,KAAK;AACxC,MAAMC,sBAAsB,SAASrS,WAAW,CAAC;EAM7CG,WAAWA,CAACmS,OAAO,EAAElB,QAAQ,EAAE;IAC3B,KAAK,CAAC,CAAC;IAAC/Q,eAAA;IAAAA,eAAA;IAAAA,eAAA,gBAJJ,IAAI;IAAAA,eAAA,uBACGhC,MAAM,CAACK,qBAAqB,CAAC;IAAA2B,eAAA,gCACpBhC,MAAM,CAACmT,gCAAgC,CAAC;IAG5D,IAAI,CAACc,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAClB,QAAQ,GAAGA,QAAQ;IACxB;IACA;IACA;IACA,IAAI,CAAC,OAAOjQ,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,CAACiR,4BAA4B,EAAE;MAClF,MAAMG,QAAQ,GAAG3S,gBAAgB,CAACwR,QAAQ,CAACxP,GAAG,CAACjD,WAAW,CAAC,CAAC;MAC5D;MACA;MACA;MACA;MACA,MAAM6T,gBAAgB,GAAG,IAAI,CAACF,OAAO,CAACE,gBAAgB;MACtD,IAAID,QAAQ,IAAI,EAAE,IAAI,CAACD,OAAO,YAAYrG,YAAY,CAAC,IAAI,CAACuG,gBAAgB,EAAE;QAC1EJ,4BAA4B,GAAG,IAAI;QACnChB,QAAQ,CACHxP,GAAG,CAAC/C,QAAQ,CAAC,CACb4T,IAAI,CAAC1T,mBAAmB,CAAC,IAAI,CAAC,uDAAuD,uDAAuD,GAC7I,oDAAoD,GACpD,iEAAiE,GACjE,4CAA4C,GAC5C,wEAAwE,GACxE,sCAAsC,CAAC,CAAC;MAChD;IACJ;EACJ;EACA2L,MAAMA,CAACqG,cAAc,EAAE;IACnB,IAAI,IAAI,CAACY,KAAK,KAAK,IAAI,EAAE;MACrB,MAAMe,qBAAqB,GAAG3Q,KAAK,CAACpC,IAAI,CAAC,IAAIgT,GAAG,CAAC,CAC7C,GAAG,IAAI,CAACvB,QAAQ,CAACxP,GAAG,CAAC0P,oBAAoB,CAAC,EAC1C,GAAG,IAAI,CAACF,QAAQ,CAACxP,GAAG,CAAC2P,yBAAyB,EAAE,EAAE,CAAC,CACtD,CAAC,CAAC;MACH;MACA;MACA;MACA;MACA,IAAI,CAACI,KAAK,GAAGe,qBAAqB,CAACZ,WAAW,CAAC,CAACc,eAAe,EAAEzB,aAAa,KAAKD,oBAAoB,CAAC0B,eAAe,EAAEzB,aAAa,EAAE,IAAI,CAACC,QAAQ,CAAC,EAAEV,qBAAqB,CAAC;IAClL;IACA,IAAI,IAAI,CAACsB,qBAAqB,EAAE;MAC5B,MAAMC,MAAM,GAAG,IAAI,CAACF,YAAY,CAACG,GAAG,CAAC,CAAC;MACtC,OAAO,IAAI,CAACP,KAAK,CAACZ,cAAc,EAAGE,iBAAiB,IAAK,IAAI,CAACqB,OAAO,CAAC5H,MAAM,CAACuG,iBAAiB,CAAC,CAAC,CAACxG,IAAI,CAAClL,QAAQ,CAAC,MAAM,IAAI,CAACwS,YAAY,CAACI,MAAM,CAACF,MAAM,CAAC,CAAC,CAAC;IAC3J,CAAC,MACI;MACD,OAAO,IAAI,CAACN,KAAK,CAACZ,cAAc,EAAGE,iBAAiB,IAAK,IAAI,CAACqB,OAAO,CAAC5H,MAAM,CAACuG,iBAAiB,CAAC,CAAC;IACpG;EACJ;AAGJ;AAAC4B,uBAAA,GAvDKR,sBAAsB;AAAAhS,eAAA,CAAtBgS,sBAAsB,wBAAAS,gCAAAzH,iBAAA;EAAA,YAAAA,iBAAA,IAqD4EgH,uBAAsB,EAhY5CpU,EAAE,CAAAqN,QAAA,CAgY4DrL,WAAW,GAhYzEhC,EAAE,CAAAqN,QAAA,CAgYoFrN,EAAE,CAAC8U,mBAAmB;AAAA;AAAA1S,eAAA,CArDxLgS,sBAAsB,+BA3UsDpU,EAAE,CAAAsN,kBAAA;EAAApF,KAAA,EAiYwBkM,uBAAsB;EAAA7G,OAAA,EAAtB6G,uBAAsB,CAAA5G;AAAA;AAElI;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KAnYkFlD,EAAE,CAAAyN,iBAAA,CAmYQ2G,sBAAsB,EAAc,CAAC;IACrH7J,IAAI,EAAEpK;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoK,IAAI,EAAEvI;EAAY,CAAC,EAAE;IAAEuI,IAAI,EAAEvK,EAAE,CAAC8U;EAAoB,CAAC,CAAC;AAAA;;AAE3F;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,CAAC;AACrB;AACA;AACA;AACA;AACA,IAAIC,eAAe;AACnB;AACA;AACA,MAAMC,qBAAqB,GAAG,gDAAgD;AAC9E;AACA;AACA,MAAMC,sBAAsB,GAAG,+CAA+C;AAC9E,MAAMC,6BAA6B,GAAG,6CAA6C;AACnF;AACA;AACA,MAAMC,+BAA+B,GAAG,wCAAwC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAOA,MAAM;EACjB;EACA,OAAO,CAAC,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EAOrBtT,WAAWA,CAACuT,WAAW,EAAEC,QAAQ,EAAE;IAAAtT,eAAA;IAAAA,eAAA;IAJnC;AACJ;AACA;IAFIA,eAAA,0BAGkBuT,OAAO,CAACC,OAAO,CAAC,CAAC;IAE/B,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;EACIG,YAAYA,CAAA,EAAG;IACX,OAAO,qBAAqBd,aAAa,EAAE,EAAE;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;EACItI,MAAMA,CAACH,GAAG,EAAE;IACR;IACA;IACA,IAAIA,GAAG,CAAClE,MAAM,KAAK,OAAO,EAAE;MACxB,MAAM,IAAI5C,KAAK,CAAC0P,sBAAsB,CAAC;IAC3C,CAAC,MACI,IAAI5I,GAAG,CAACzC,YAAY,KAAK,MAAM,EAAE;MAClC,MAAM,IAAIrE,KAAK,CAAC2P,6BAA6B,CAAC;IAClD;IACA;IACA;IACA,IAAI7I,GAAG,CAACnK,OAAO,CAAC0B,IAAI,CAAC,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAM,IAAI4B,KAAK,CAAC4P,+BAA+B,CAAC;IACpD;IACA;IACA,OAAO,IAAI3T,UAAU,CAAEkN,QAAQ,IAAK;MAChC;MACA;MACA;MACA,MAAMmH,QAAQ,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACpC,MAAMzM,GAAG,GAAGkD,GAAG,CAACtC,aAAa,CAAC5D,OAAO,CAAC,sBAAsB,EAAE,IAAI0P,QAAQ,IAAI,CAAC;MAC/E;MACA,MAAMC,IAAI,GAAG,IAAI,CAACL,QAAQ,CAACM,aAAa,CAAC,QAAQ,CAAC;MAClDD,IAAI,CAACE,GAAG,GAAG7M,GAAG;MACd;MACA;MACA;MACA,IAAIM,IAAI,GAAG,IAAI;MACf;MACA,IAAIwM,QAAQ,GAAG,KAAK;MACpB;MACA;MACA;MACA,IAAI,CAACT,WAAW,CAACK,QAAQ,CAAC,GAAIK,IAAI,IAAK;QACnC;QACA,OAAO,IAAI,CAACV,WAAW,CAACK,QAAQ,CAAC;QACjC;QACApM,IAAI,GAAGyM,IAAI;QACXD,QAAQ,GAAG,IAAI;MACnB,CAAC;MACD;MACA;MACA;MACA,MAAME,OAAO,GAAGA,CAAA,KAAM;QAClBL,IAAI,CAACM,mBAAmB,CAAC,MAAM,EAAEC,MAAM,CAAC;QACxCP,IAAI,CAACM,mBAAmB,CAAC,OAAO,EAAEE,OAAO,CAAC;QAC1C;QACAR,IAAI,CAAC7B,MAAM,CAAC,CAAC;QACb;QACA;QACA,OAAO,IAAI,CAACuB,WAAW,CAACK,QAAQ,CAAC;MACrC,CAAC;MACD;MACA;MACA;MACA;MACA,MAAMQ,MAAM,GAAI3J,KAAK,IAAK;QACtB;QACA;QACA;QACA,IAAI,CAAC6J,eAAe,CAACxH,IAAI,CAAC,MAAM;UAC5B;UACAoH,OAAO,CAAC,CAAC;UACT;UACA,IAAI,CAACF,QAAQ,EAAE;YACX;YACA;YACAvH,QAAQ,CAAC/C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;cACjCtC,GAAG;cACH+B,MAAM,EAAE,CAAC;cACTC,UAAU,EAAE,aAAa;cACzBQ,KAAK,EAAE,IAAIpG,KAAK,CAACyP,qBAAqB;YAC1C,CAAC,CAAC,CAAC;YACH;UACJ;UACA;UACA;UACAtG,QAAQ,CAACkB,IAAI,CAAC,IAAIrE,YAAY,CAAC;YAC3B9B,IAAI;YACJyB,MAAM,EAAEU,mBAAmB;YAC3BT,UAAU,EAAE,IAAI;YAChBhC;UACJ,CAAC,CAAC,CAAC;UACH;UACAuF,QAAQ,CAAC0C,QAAQ,CAAC,CAAC;QACvB,CAAC,CAAC;MACN,CAAC;MACD;MACA;MACA;MACA,MAAMkF,OAAO,GAAI3K,KAAK,IAAK;QACvBwK,OAAO,CAAC,CAAC;QACT;QACAzH,QAAQ,CAAC/C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;UACjCE,KAAK;UACLT,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE,aAAa;UACzBhC;QACJ,CAAC,CAAC,CAAC;MACP,CAAC;MACD;MACA;MACA2M,IAAI,CAACU,gBAAgB,CAAC,MAAM,EAAEH,MAAM,CAAC;MACrCP,IAAI,CAACU,gBAAgB,CAAC,OAAO,EAAEF,OAAO,CAAC;MACvC,IAAI,CAACb,QAAQ,CAAChM,IAAI,CAACgN,WAAW,CAACX,IAAI,CAAC;MACpC;MACApH,QAAQ,CAACkB,IAAI,CAAC;QAAEtF,IAAI,EAAEQ,aAAa,CAAC+E;MAAK,CAAC,CAAC;MAC3C;MACA,OAAO,MAAM;QACT,IAAI,CAACoG,QAAQ,EAAE;UACX,IAAI,CAACS,eAAe,CAACZ,IAAI,CAAC;QAC9B;QACA;QACAK,OAAO,CAAC,CAAC;MACb,CAAC;IACL,CAAC,CAAC;EACN;EACAO,eAAeA,CAACC,MAAM,EAAE;IACpB;IACA;IACA;IACA5B,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAfA,eAAe,GAAK,IAAI,CAACU,QAAQ,CAACmB,cAAc,CAACC,kBAAkB,CAAC,CAAC;IACrE9B,eAAe,CAAC+B,SAAS,CAACH,MAAM,CAAC;EACrC;AAGJ;AAACI,mBAAA,GArJKxB,kBAAkB;AAAApT,eAAA,CAAlBoT,kBAAkB,wBAAAyB,4BAAA7J,iBAAA;EAAA,YAAAA,iBAAA,IAmJgFoI,mBAAkB,EA7kBxCxV,EAAE,CAAAqN,QAAA,CA6kBwDgI,oBAAoB,GA7kB9ErV,EAAE,CAAAqN,QAAA,CA6kByFvL,QAAQ;AAAA;AAAAM,eAAA,CAnJ/KoT,kBAAkB,+BA1b0DxV,EAAE,CAAAsN,kBAAA;EAAApF,KAAA,EA8kBwBsN,mBAAkB;EAAAjI,OAAA,EAAlBiI,mBAAkB,CAAAhI;AAAA;AAE9H;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KAhlBkFlD,EAAE,CAAAyN,iBAAA,CAglBQ+H,kBAAkB,EAAc,CAAC;IACjHjL,IAAI,EAAEpK;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoK,IAAI,EAAE8K;EAAqB,CAAC,EAAE;IAAE9K,IAAI,EAAEvF,SAAS;IAAEkS,UAAU,EAAE,CAAC;MAC/E3M,IAAI,EAAEvJ,MAAM;MACZsN,IAAI,EAAE,CAACxM,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA,SAASqV,kBAAkBA,CAAC7K,GAAG,EAAEuD,IAAI,EAAE;EACnC,IAAIvD,GAAG,CAAClE,MAAM,KAAK,OAAO,EAAE;IACxB,OAAOhI,MAAM,CAACoV,kBAAkB,CAAC,CAAC/I,MAAM,CAACH,GAAG,CAAC;EACjD;EACA;EACA,OAAOuD,IAAI,CAACvD,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8K,gBAAgB,CAAC;EAEnBlV,WAAWA,CAACiR,QAAQ,EAAE;IAAA/Q,eAAA;IAClB,IAAI,CAAC+Q,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIJ,SAASA,CAACD,cAAc,EAAEjD,IAAI,EAAE;IAC5B,OAAO9O,qBAAqB,CAAC,IAAI,CAACoS,QAAQ,EAAE,MAAMgE,kBAAkB,CAACrE,cAAc,EAAGE,iBAAiB,IAAKnD,IAAI,CAACpD,MAAM,CAACuG,iBAAiB,CAAC,CAAC,CAAC;EAChJ;AAGJ;AAACqE,iBAAA,GAjBKD,gBAAgB;AAAAhV,eAAA,CAAhBgV,gBAAgB,wBAAAE,0BAAAlK,iBAAA;EAAA,YAAAA,iBAAA,IAekFgK,iBAAgB,EAvnBtCpX,EAAE,CAAAqN,QAAA,CAunBsDrN,EAAE,CAAC8U,mBAAmB;AAAA;AAAA1S,eAAA,CAf1JgV,gBAAgB,+BAxmB4DpX,EAAE,CAAAsN,kBAAA;EAAApF,KAAA,EAwnBwBkP,iBAAgB;EAAA7J,OAAA,EAAhB6J,iBAAgB,CAAA5J;AAAA;AAE5H;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KA1nBkFlD,EAAE,CAAAyN,iBAAA,CA0nBQ2J,gBAAgB,EAAc,CAAC;IAC/G7M,IAAI,EAAEpK;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoK,IAAI,EAAEvK,EAAE,CAAC8U;EAAoB,CAAC,CAAC;AAAA;AAEpE,MAAMyC,WAAW,GAAG,cAAc;AAClC,MAAMC,oBAAoB,GAAGC,MAAM,CAAC,IAAI1O,oBAAoB,GAAG,EAAE,GAAG,CAAC;AACrE;AACA;AACA;AACA;AACA,SAAS2O,cAAcA,CAACC,GAAG,EAAE;EACzB,IAAI,aAAa,IAAIA,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE;IACzC,OAAOD,GAAG,CAACC,WAAW;EAC1B;EACA,IAAIJ,oBAAoB,CAACK,IAAI,CAACF,GAAG,CAACG,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACxD,OAAOH,GAAG,CAACI,iBAAiB,CAAChP,oBAAoB,CAAC;EACtD;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiP,cAAc,CAAC;EAEjB9V,WAAWA,CAAC+V,UAAU,EAAE;IAAA7V,eAAA;IACpB,IAAI,CAAC6V,UAAU,GAAGA,UAAU;EAChC;EACA;AACJ;AACA;AACA;AACA;EACIxL,MAAMA,CAACH,GAAG,EAAE;IACR;IACA;IACA,IAAIA,GAAG,CAAClE,MAAM,KAAK,OAAO,EAAE;MACxB,MAAM,IAAIlI,aAAa,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,OAAOgD,SAAS,KAAK,WAAW,IAAIA,SAAS,KACrH,sNAAsN,CAAC;IAC/N;IACA;IACA;IACA;IACA,MAAM+U,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMC,MAAM,GAAGD,UAAU,CAACE,SAAS,GAC7BzW,IAAI,CAACuW,UAAU,CAACE,SAAS,CAAC,CAAC,CAAC,GAC5B3W,EAAE,CAAC,IAAI,CAAC;IACd,OAAO0W,MAAM,CAAC1L,IAAI,CAACjL,SAAS,CAAC,MAAM;MAC/B;MACA,OAAO,IAAIE,UAAU,CAAEkN,QAAQ,IAAK;QAChC;QACA;QACA,MAAMgJ,GAAG,GAAGM,UAAU,CAACG,KAAK,CAAC,CAAC;QAC9BT,GAAG,CAACU,IAAI,CAAC/L,GAAG,CAAClE,MAAM,EAAEkE,GAAG,CAACtC,aAAa,CAAC;QACvC,IAAIsC,GAAG,CAAC1C,eAAe,EAAE;UACrB+N,GAAG,CAAC/N,eAAe,GAAG,IAAI;QAC9B;QACA;QACA0C,GAAG,CAACnK,OAAO,CAACK,OAAO,CAAC,CAACI,IAAI,EAAEU,MAAM,KAAKqU,GAAG,CAACW,gBAAgB,CAAC1V,IAAI,EAAEU,MAAM,CAACqE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACnF;QACA,IAAI,CAAC2E,GAAG,CAACnK,OAAO,CAACqB,GAAG,CAACsF,aAAa,CAAC,EAAE;UACjC6O,GAAG,CAACW,gBAAgB,CAACxP,aAAa,EAAEI,mBAAmB,CAAC;QAC5D;QACA;QACA,IAAI,CAACoD,GAAG,CAACnK,OAAO,CAACqB,GAAG,CAACqF,mBAAmB,CAAC,EAAE;UACvC,MAAMoJ,YAAY,GAAG3F,GAAG,CAAChC,uBAAuB,CAAC,CAAC;UAClD;UACA,IAAI2H,YAAY,KAAK,IAAI,EAAE;YACvB0F,GAAG,CAACW,gBAAgB,CAACzP,mBAAmB,EAAEoJ,YAAY,CAAC;UAC3D;QACJ;QACA;QACA,IAAI3F,GAAG,CAACzC,YAAY,EAAE;UAClB,MAAMA,YAAY,GAAGyC,GAAG,CAACzC,YAAY,CAACnG,WAAW,CAAC,CAAC;UACnD;UACA;UACA;UACA;UACA;UACAiU,GAAG,CAAC9N,YAAY,GAAIA,YAAY,KAAK,MAAM,GAAGA,YAAY,GAAG,MAAO;QACxE;QACA;QACA,MAAM0O,OAAO,GAAGjM,GAAG,CAACnC,aAAa,CAAC,CAAC;QACnC;QACA;QACA;QACA;QACA;QACA;QACA,IAAIqO,cAAc,GAAG,IAAI;QACzB;QACA;QACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;UACzB,IAAID,cAAc,KAAK,IAAI,EAAE;YACzB,OAAOA,cAAc;UACzB;UACA,MAAMpN,UAAU,GAAGuM,GAAG,CAACvM,UAAU,IAAI,IAAI;UACzC;UACA,MAAMjJ,OAAO,GAAG,IAAIF,WAAW,CAAC0V,GAAG,CAACG,qBAAqB,CAAC,CAAC,CAAC;UAC5D;UACA;UACA,MAAM1O,GAAG,GAAGsO,cAAc,CAACC,GAAG,CAAC,IAAIrL,GAAG,CAAClD,GAAG;UAC1C;UACAoP,cAAc,GAAG,IAAIlN,kBAAkB,CAAC;YAAEnJ,OAAO;YAAEgJ,MAAM,EAAEwM,GAAG,CAACxM,MAAM;YAAEC,UAAU;YAAEhC;UAAI,CAAC,CAAC;UACzF,OAAOoP,cAAc;QACzB,CAAC;QACD;QACA;QACA;QACA,MAAMlC,MAAM,GAAGA,CAAA,KAAM;UACjB;UACA,IAAI;YAAEnU,OAAO;YAAEgJ,MAAM;YAAEC,UAAU;YAAEhC;UAAI,CAAC,GAAGqP,cAAc,CAAC,CAAC;UAC3D;UACA,IAAI/O,IAAI,GAAG,IAAI;UACf,IAAIyB,MAAM,KAAKW,2BAA2B,EAAE;YACxC;YACApC,IAAI,GAAG,OAAOiO,GAAG,CAAC/J,QAAQ,KAAK,WAAW,GAAG+J,GAAG,CAACe,YAAY,GAAGf,GAAG,CAAC/J,QAAQ;UAChF;UACA;UACA,IAAIzC,MAAM,KAAK,CAAC,EAAE;YACdA,MAAM,GAAG,CAAC,CAACzB,IAAI,GAAGmC,mBAAmB,GAAG,CAAC;UAC7C;UACA;UACA;UACA;UACA;UACA,IAAIR,EAAE,GAAGF,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;UACtC;UACA;UACA,IAAImB,GAAG,CAACzC,YAAY,KAAK,MAAM,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;YACzD;YACA,MAAMiP,YAAY,GAAGjP,IAAI;YACzBA,IAAI,GAAGA,IAAI,CAACtD,OAAO,CAACmR,WAAW,EAAE,EAAE,CAAC;YACpC,IAAI;cACA;cACA;cACA7N,IAAI,GAAGA,IAAI,KAAK,EAAE,GAAGU,IAAI,CAAC0H,KAAK,CAACpI,IAAI,CAAC,GAAG,IAAI;YAChD,CAAC,CACD,OAAOkC,KAAK,EAAE;cACV;cACA;cACA;cACAlC,IAAI,GAAGiP,YAAY;cACnB;cACA;cACA,IAAItN,EAAE,EAAE;gBACJ;gBACAA,EAAE,GAAG,KAAK;gBACV;gBACA3B,IAAI,GAAG;kBAAEkC,KAAK;kBAAEiG,IAAI,EAAEnI;gBAAK,CAAC;cAChC;YACJ;UACJ;UACA,IAAI2B,EAAE,EAAE;YACJ;YACAsD,QAAQ,CAACkB,IAAI,CAAC,IAAIrE,YAAY,CAAC;cAC3B9B,IAAI;cACJvH,OAAO;cACPgJ,MAAM;cACNC,UAAU;cACVhC,GAAG,EAAEA,GAAG,IAAIpE;YAChB,CAAC,CAAC,CAAC;YACH;YACA;YACA2J,QAAQ,CAAC0C,QAAQ,CAAC,CAAC;UACvB,CAAC,MACI;YACD;YACA1C,QAAQ,CAAC/C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;cACjC;cACAE,KAAK,EAAElC,IAAI;cACXvH,OAAO;cACPgJ,MAAM;cACNC,UAAU;cACVhC,GAAG,EAAEA,GAAG,IAAIpE;YAChB,CAAC,CAAC,CAAC;UACP;QACJ,CAAC;QACD;QACA;QACA;QACA,MAAMuR,OAAO,GAAI3K,KAAK,IAAK;UACvB,MAAM;YAAExC;UAAI,CAAC,GAAGqP,cAAc,CAAC,CAAC;UAChC,MAAMnR,GAAG,GAAG,IAAIoE,iBAAiB,CAAC;YAC9BE,KAAK;YACLT,MAAM,EAAEwM,GAAG,CAACxM,MAAM,IAAI,CAAC;YACvBC,UAAU,EAAEuM,GAAG,CAACvM,UAAU,IAAI,eAAe;YAC7ChC,GAAG,EAAEA,GAAG,IAAIpE;UAChB,CAAC,CAAC;UACF2J,QAAQ,CAAC/C,KAAK,CAACtE,GAAG,CAAC;QACvB,CAAC;QACD;QACA;QACA;QACA;QACA,IAAIsR,WAAW,GAAG,KAAK;QACvB;QACA;QACA,MAAMC,cAAc,GAAIlM,KAAK,IAAK;UAC9B;UACA,IAAI,CAACiM,WAAW,EAAE;YACdjK,QAAQ,CAACkB,IAAI,CAAC4I,cAAc,CAAC,CAAC,CAAC;YAC/BG,WAAW,GAAG,IAAI;UACtB;UACA;UACA;UACA,IAAIE,aAAa,GAAG;YAChBvO,IAAI,EAAEQ,aAAa,CAACkG,gBAAgB;YACpCE,MAAM,EAAExE,KAAK,CAACwE;UAClB,CAAC;UACD;UACA,IAAIxE,KAAK,CAACoM,gBAAgB,EAAE;YACxBD,aAAa,CAAC5H,KAAK,GAAGvE,KAAK,CAACuE,KAAK;UACrC;UACA;UACA;UACA;UACA,IAAI5E,GAAG,CAACzC,YAAY,KAAK,MAAM,IAAI,CAAC,CAAC8N,GAAG,CAACe,YAAY,EAAE;YACnDI,aAAa,CAACxI,WAAW,GAAGqH,GAAG,CAACe,YAAY;UAChD;UACA;UACA/J,QAAQ,CAACkB,IAAI,CAACiJ,aAAa,CAAC;QAChC,CAAC;QACD;QACA;QACA,MAAME,YAAY,GAAIrM,KAAK,IAAK;UAC5B;UACA;UACA,IAAIsM,QAAQ,GAAG;YACX1O,IAAI,EAAEQ,aAAa,CAACmO,cAAc;YAClC/H,MAAM,EAAExE,KAAK,CAACwE;UAClB,CAAC;UACD;UACA;UACA,IAAIxE,KAAK,CAACoM,gBAAgB,EAAE;YACxBE,QAAQ,CAAC/H,KAAK,GAAGvE,KAAK,CAACuE,KAAK;UAChC;UACA;UACAvC,QAAQ,CAACkB,IAAI,CAACoJ,QAAQ,CAAC;QAC3B,CAAC;QACD;QACAtB,GAAG,CAAClB,gBAAgB,CAAC,MAAM,EAAEH,MAAM,CAAC;QACpCqB,GAAG,CAAClB,gBAAgB,CAAC,OAAO,EAAEF,OAAO,CAAC;QACtCoB,GAAG,CAAClB,gBAAgB,CAAC,SAAS,EAAEF,OAAO,CAAC;QACxCoB,GAAG,CAAClB,gBAAgB,CAAC,OAAO,EAAEF,OAAO,CAAC;QACtC;QACA,IAAIjK,GAAG,CAAC3C,cAAc,EAAE;UACpB;UACAgO,GAAG,CAAClB,gBAAgB,CAAC,UAAU,EAAEoC,cAAc,CAAC;UAChD;UACA,IAAIN,OAAO,KAAK,IAAI,IAAIZ,GAAG,CAACwB,MAAM,EAAE;YAChCxB,GAAG,CAACwB,MAAM,CAAC1C,gBAAgB,CAAC,UAAU,EAAEuC,YAAY,CAAC;UACzD;QACJ;QACA;QACArB,GAAG,CAACyB,IAAI,CAACb,OAAO,CAAC;QACjB5J,QAAQ,CAACkB,IAAI,CAAC;UAAEtF,IAAI,EAAEQ,aAAa,CAAC+E;QAAK,CAAC,CAAC;QAC3C;QACA;QACA,OAAO,MAAM;UACT;UACA6H,GAAG,CAACtB,mBAAmB,CAAC,OAAO,EAAEE,OAAO,CAAC;UACzCoB,GAAG,CAACtB,mBAAmB,CAAC,OAAO,EAAEE,OAAO,CAAC;UACzCoB,GAAG,CAACtB,mBAAmB,CAAC,MAAM,EAAEC,MAAM,CAAC;UACvCqB,GAAG,CAACtB,mBAAmB,CAAC,SAAS,EAAEE,OAAO,CAAC;UAC3C,IAAIjK,GAAG,CAAC3C,cAAc,EAAE;YACpBgO,GAAG,CAACtB,mBAAmB,CAAC,UAAU,EAAEwC,cAAc,CAAC;YACnD,IAAIN,OAAO,KAAK,IAAI,IAAIZ,GAAG,CAACwB,MAAM,EAAE;cAChCxB,GAAG,CAACwB,MAAM,CAAC9C,mBAAmB,CAAC,UAAU,EAAE2C,YAAY,CAAC;YAC5D;UACJ;UACA;UACA,IAAIrB,GAAG,CAAC0B,UAAU,KAAK1B,GAAG,CAAC2B,IAAI,EAAE;YAC7B3B,GAAG,CAACzI,KAAK,CAAC,CAAC;UACf;QACJ,CAAC;MACL,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;EACP;AAGJ;AAACqK,eAAA,GAlQKvB,cAAc;AAAA5V,eAAA,CAAd4V,cAAc,wBAAAwB,wBAAApM,iBAAA;EAAA,YAAAA,iBAAA,IAgQoF4K,eAAc,EAp5BpChY,EAAE,CAAAqN,QAAA,CAo5BoDzL,UAAU;AAAA;AAAAQ,eAAA,CAhQ5I4V,cAAc,+BAppB8DhY,EAAE,CAAAsN,kBAAA;EAAApF,KAAA,EAq5BwB8P,eAAc;EAAAzK,OAAA,EAAdyK,eAAc,CAAAxK;AAAA;AAE1H;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KAv5BkFlD,EAAE,CAAAyN,iBAAA,CAu5BQuK,cAAc,EAAc,CAAC;IAC7GzN,IAAI,EAAEpK;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoK,IAAI,EAAE3I;EAAW,CAAC,CAAC;AAAA;AAExD,MAAM6X,YAAY,GAAG,IAAIlZ,cAAc,CAAC2C,SAAS,GAAG,cAAc,GAAG,EAAE,CAAC;AACxE,MAAMwW,wBAAwB,GAAG,YAAY;AAC7C,MAAMC,gBAAgB,GAAG,IAAIpZ,cAAc,CAAC2C,SAAS,GAAG,kBAAkB,GAAG,EAAE,EAAE;EAC7EsQ,UAAU,EAAE,MAAM;EAClBjG,OAAO,EAAEA,CAAA,KAAMmM;AACnB,CAAC,CAAC;AACF,MAAME,wBAAwB,GAAG,cAAc;AAC/C,MAAMC,gBAAgB,GAAG,IAAItZ,cAAc,CAAC2C,SAAS,GAAG,kBAAkB,GAAG,EAAE,EAAE;EAC7EsQ,UAAU,EAAE,MAAM;EAClBjG,OAAO,EAAEA,CAAA,KAAMqM;AACnB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAME,sBAAsB,CAAC;AAE7B;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAS1B7X,WAAWA,CAAC8X,GAAG,EAAEC,UAAU,EAAE;IAAA7X,eAAA;IAAAA,eAAA;IAAAA,eAAA,2BANV,EAAE;IAAAA,eAAA,oBACT,IAAI;IAChB;AACJ;AACA;IAFIA,eAAA,qBAGa,CAAC;IAEV,IAAI,CAAC4X,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,OAAOC,YAAY,KAAK,WAAW,IAAIA,YAAY,EAAE;MACrD,OAAO,IAAI;IACf;IACA,MAAMC,YAAY,GAAG,IAAI,CAACJ,GAAG,CAACK,MAAM,IAAI,EAAE;IAC1C,IAAID,YAAY,KAAK,IAAI,CAACE,gBAAgB,EAAE;MACxC,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACC,SAAS,GAAG3Y,gBAAgB,CAACuY,YAAY,EAAE,IAAI,CAACH,UAAU,CAAC;MAChE,IAAI,CAACK,gBAAgB,GAAGF,YAAY;IACxC;IACA,OAAO,IAAI,CAACI,SAAS;EACzB;AAGJ;AAACC,wBAAA,GA3BKV,uBAAuB;AAAA3X,eAAA,CAAvB2X,uBAAuB,wBAAAW,iCAAAtN,iBAAA;EAAA,YAAAA,iBAAA,IAyB2E2M,wBAAuB,EAz8B7C/Z,EAAE,CAAAqN,QAAA,CAy8B6DvL,QAAQ,GAz8BvE9B,EAAE,CAAAqN,QAAA,CAy8BkFsM,gBAAgB;AAAA;AAAAvX,eAAA,CAzBhL2X,uBAAuB,+BAh7BqD/Z,EAAE,CAAAsN,kBAAA;EAAApF,KAAA,EA08BwB6R,wBAAuB;EAAAxM,OAAA,EAAvBwM,wBAAuB,CAAAvM;AAAA;AAEnI;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KA58BkFlD,EAAE,CAAAyN,iBAAA,CA48BQsM,uBAAuB,EAAc,CAAC;IACtHxP,IAAI,EAAEpK;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoK,IAAI,EAAEvF,SAAS;IAAEkS,UAAU,EAAE,CAAC;MAC/C3M,IAAI,EAAEvJ,MAAM;MACZsN,IAAI,EAAE,CAACxM,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEyI,IAAI,EAAEvF,SAAS;IAAEkS,UAAU,EAAE,CAAC;MAClC3M,IAAI,EAAEvJ,MAAM;MACZsN,IAAI,EAAE,CAACqL,gBAAgB;IAC3B,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,SAASgB,iBAAiBA,CAACrO,GAAG,EAAEuD,IAAI,EAAE;EAClC,MAAM+K,KAAK,GAAGtO,GAAG,CAAClD,GAAG,CAAC1F,WAAW,CAAC,CAAC;EACnC;EACA;EACA;EACA;EACA,IAAI,CAACtD,MAAM,CAACqZ,YAAY,CAAC,IACrBnN,GAAG,CAAClE,MAAM,KAAK,KAAK,IACpBkE,GAAG,CAAClE,MAAM,KAAK,MAAM,IACrBwS,KAAK,CAACC,UAAU,CAAC,SAAS,CAAC,IAC3BD,KAAK,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IAC9B,OAAOhL,IAAI,CAACvD,GAAG,CAAC;EACpB;EACA,MAAMpE,KAAK,GAAG9H,MAAM,CAAC0Z,sBAAsB,CAAC,CAACI,QAAQ,CAAC,CAAC;EACvD,MAAMY,UAAU,GAAG1a,MAAM,CAACyZ,gBAAgB,CAAC;EAC3C;EACA,IAAI3R,KAAK,IAAI,IAAI,IAAI,CAACoE,GAAG,CAACnK,OAAO,CAACqB,GAAG,CAACsX,UAAU,CAAC,EAAE;IAC/CxO,GAAG,GAAGA,GAAG,CAACpI,KAAK,CAAC;MAAE/B,OAAO,EAAEmK,GAAG,CAACnK,OAAO,CAACiC,GAAG,CAAC0W,UAAU,EAAE5S,KAAK;IAAE,CAAC,CAAC;EACpE;EACA,OAAO2H,IAAI,CAACvD,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA,MAAMyO,mBAAmB,CAAC;EAEtB7Y,WAAWA,CAACiR,QAAQ,EAAE;IAAA/Q,eAAA;IAClB,IAAI,CAAC+Q,QAAQ,GAAGA,QAAQ;EAC5B;EACAJ,SAASA,CAACD,cAAc,EAAEjD,IAAI,EAAE;IAC5B,OAAO9O,qBAAqB,CAAC,IAAI,CAACoS,QAAQ,EAAE,MAAMwH,iBAAiB,CAAC7H,cAAc,EAAGE,iBAAiB,IAAKnD,IAAI,CAACpD,MAAM,CAACuG,iBAAiB,CAAC,CAAC,CAAC;EAC/I;AAGJ;AAACgI,oBAAA,GAVKD,mBAAmB;AAAA3Y,eAAA,CAAnB2Y,mBAAmB,wBAAAE,6BAAA7N,iBAAA;EAAA,YAAAA,iBAAA,IAQ+E2N,oBAAmB,EAr/BzC/a,EAAE,CAAAqN,QAAA,CAq/ByDrN,EAAE,CAAC8U,mBAAmB;AAAA;AAAA1S,eAAA,CAR7J2Y,mBAAmB,+BA7+ByD/a,EAAE,CAAAsN,kBAAA;EAAApF,KAAA,EAs/BwB6S,oBAAmB;EAAAxN,OAAA,EAAnBwN,oBAAmB,CAAAvN;AAAA;AAE/H;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KAx/BkFlD,EAAE,CAAAyN,iBAAA,CAw/BQsN,mBAAmB,EAAc,CAAC;IAClHxQ,IAAI,EAAEpK;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoK,IAAI,EAAEvK,EAAE,CAAC8U;EAAoB,CAAC,CAAC;AAAA;;AAEpE;AACA;AACA;AACA;AACA;AACA,IAAIoG,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAACA,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrEA,eAAe,CAACA,eAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB;EACjFA,eAAe,CAACA,eAAe,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,GAAG,yBAAyB;EAC3FA,eAAe,CAACA,eAAe,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EAC7EA,eAAe,CAACA,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrEA,eAAe,CAACA,eAAe,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,GAAG,uBAAuB;EACvFA,eAAe,CAACA,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;AAC3D,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,SAASC,eAAeA,CAACC,IAAI,EAAEC,SAAS,EAAE;EACtC,OAAO;IACHC,KAAK,EAAEF,IAAI;IACXG,UAAU,EAAEF;EAChB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,iBAAiBA,CAAC,GAAGC,QAAQ,EAAE;EACpC,IAAIvY,SAAS,EAAE;IACX,MAAMwY,YAAY,GAAG,IAAIhH,GAAG,CAAC+G,QAAQ,CAACpa,GAAG,CAAEsa,CAAC,IAAKA,CAAC,CAACL,KAAK,CAAC,CAAC;IAC1D,IAAII,YAAY,CAAClY,GAAG,CAAC0X,eAAe,CAACU,gBAAgB,CAAC,IAClDF,YAAY,CAAClY,GAAG,CAAC0X,eAAe,CAACW,uBAAuB,CAAC,EAAE;MAC3D,MAAM,IAAIrW,KAAK,CAACtC,SAAS,GACnB,uJAAuJ,GACvJ,EAAE,CAAC;IACb;EACJ;EACA,MAAMmY,SAAS,GAAG,CACdnP,UAAU,EACV8L,cAAc,EACd5D,sBAAsB,EACtB;IAAE0H,OAAO,EAAE/Z,WAAW;IAAEga,WAAW,EAAE3H;EAAuB,CAAC,EAC7D;IACI0H,OAAO,EAAE9Z,WAAW;IACpBga,UAAU,EAAEA,CAAA,KAAM;MAAA,IAAAC,QAAA;MACd,QAAAA,QAAA,GAAO7b,MAAM,CAAC2N,aAAa,EAAE;QAAEK,QAAQ,EAAE;MAAK,CAAC,CAAC,cAAA6N,QAAA,cAAAA,QAAA,GAAI7b,MAAM,CAAC4X,cAAc,CAAC;IAC9E;EACJ,CAAC,EACD;IACI8D,OAAO,EAAEzI,oBAAoB;IAC7B6I,QAAQ,EAAEvB,iBAAiB;IAC3BwB,KAAK,EAAE;EACX,CAAC,EACD;IAAEL,OAAO,EAAErC,YAAY;IAAEyC,QAAQ,EAAE;EAAK,CAAC,EACzC;IAAEJ,OAAO,EAAEhC,sBAAsB;IAAEsC,QAAQ,EAAErC;EAAwB,CAAC,CACzE;EACD,KAAK,MAAMsC,OAAO,IAAIZ,QAAQ,EAAE;IAC5BJ,SAAS,CAACpW,IAAI,CAAC,GAAGoX,OAAO,CAACd,UAAU,CAAC;EACzC;EACA,OAAOta,wBAAwB,CAACoa,SAAS,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiB,gBAAgBA,CAACC,cAAc,EAAE;EACtC,OAAOpB,eAAe,CAACD,eAAe,CAACsB,YAAY,EAAED,cAAc,CAAClb,GAAG,CAAE6R,aAAa,IAAK;IACvF,OAAO;MACH4I,OAAO,EAAEzI,oBAAoB;MAC7B6I,QAAQ,EAAEhJ,aAAa;MACvBiJ,KAAK,EAAE;IACX,CAAC;EACL,CAAC,CAAC,CAAC;AACP;AACA,MAAMM,qBAAqB,GAAG,IAAIlc,cAAc,CAAC2C,SAAS,GAAG,uBAAuB,GAAG,EAAE,CAAC;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwZ,sBAAsBA,CAAA,EAAG;EAC9B;EACA;EACA;EACA;EACA;EACA,OAAOvB,eAAe,CAACD,eAAe,CAACyB,kBAAkB,EAAE,CACvD;IACIb,OAAO,EAAEW,qBAAqB;IAC9BT,UAAU,EAAEvI;EAChB,CAAC,EACD;IACIqI,OAAO,EAAEzI,oBAAoB;IAC7B0I,WAAW,EAAEU,qBAAqB;IAClCN,KAAK,EAAE;EACX,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,qBAAqBA,CAAC;EAAE3C,UAAU;EAAEa;AAAY,CAAC,EAAE;EACxD,MAAMO,SAAS,GAAG,EAAE;EACpB,IAAIpB,UAAU,KAAKjV,SAAS,EAAE;IAC1BqW,SAAS,CAACpW,IAAI,CAAC;MAAE6W,OAAO,EAAEnC,gBAAgB;MAAEuC,QAAQ,EAAEjC;IAAW,CAAC,CAAC;EACvE;EACA,IAAIa,UAAU,KAAK9V,SAAS,EAAE;IAC1BqW,SAAS,CAACpW,IAAI,CAAC;MAAE6W,OAAO,EAAEjC,gBAAgB;MAAEqC,QAAQ,EAAEpB;IAAW,CAAC,CAAC;EACvE;EACA,OAAOK,eAAe,CAACD,eAAe,CAACW,uBAAuB,EAAER,SAAS,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwB,oBAAoBA,CAAA,EAAG;EAC5B,OAAO1B,eAAe,CAACD,eAAe,CAACU,gBAAgB,EAAE,CACrD;IACIE,OAAO,EAAErC,YAAY;IACrByC,QAAQ,EAAE;EACd,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,gBAAgBA,CAAA,EAAG;EACxB,OAAO3B,eAAe,CAACD,eAAe,CAAC6B,YAAY,EAAE,CACjDvH,kBAAkB,EAClB;IAAEsG,OAAO,EAAEzG,oBAAoB;IAAE2G,UAAU,EAAE1G;EAAqB,CAAC,EACnE;IAAEwG,OAAO,EAAEzI,oBAAoB;IAAE6I,QAAQ,EAAE/E,kBAAkB;IAAEgF,KAAK,EAAE;EAAK,CAAC,CAC/E,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,yBAAyBA,CAAA,EAAG;EACjC,OAAO7B,eAAe,CAACD,eAAe,CAAC+B,qBAAqB,EAAE,CAC1D;IACInB,OAAO,EAAE9Z,WAAW;IACpBga,UAAU,EAAEA,CAAA,KAAM;MACd,MAAMkB,iBAAiB,GAAG9c,MAAM,CAAC2B,WAAW,EAAE;QAAEob,QAAQ,EAAE,IAAI;QAAE/O,QAAQ,EAAE;MAAK,CAAC,CAAC;MACjF,IAAIlL,SAAS,IAAIga,iBAAiB,KAAK,IAAI,EAAE;QACzC,MAAM,IAAI1X,KAAK,CAAC,kGAAkG,CAAC;MACvH;MACA,OAAO0X,iBAAiB;IAC5B;EACJ,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAAA,EAAG;EACjB,OAAOjC,eAAe,CAACD,eAAe,CAACmC,KAAK,EAAE,CAC1CrP,YAAY,EACZ;IAAE8N,OAAO,EAAE/N,aAAa;IAAEgO,WAAW,EAAE/N;EAAa,CAAC,EACrD;IAAE8N,OAAO,EAAE9Z,WAAW;IAAE+Z,WAAW,EAAE/N;EAAa,CAAC,CACtD,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsP,oBAAoB,CAAC;EACvB;AACJ;AACA;EACI,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,oBAAoB;MAC9BjC,SAAS,EAAE,CAACwB,oBAAoB,CAAC,CAAC,CAACtB,UAAU;IACjD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOkC,WAAWA,CAACvW,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7B,OAAO;MACHsW,QAAQ,EAAEF,oBAAoB;MAC9BjC,SAAS,EAAEuB,qBAAqB,CAAC1V,OAAO,CAAC,CAACqU;IAC9C,CAAC;EACL;AAaJ;AAACmC,qBAAA,GApCKJ,oBAAoB;AAAAlb,eAAA,CAApBkb,oBAAoB,wBAAAK,8BAAAvQ,iBAAA;EAAA,YAAAA,iBAAA,IAwB8EkQ,qBAAoB;AAAA;AAAAlb,eAAA,CAxBtHkb,oBAAoB,8BA1uCwDtd,EAAE,CAAA4d,gBAAA;EAAArT,IAAA,EAmwCqB+S;AAAoB;AAAAlb,eAAA,CAzBvHkb,oBAAoB,8BA1uCwDtd,EAAE,CAAA6d,gBAAA;EAAAxC,SAAA,EAowCsD,CAC9HN,mBAAmB,EACnB;IAAEe,OAAO,EAAE1I,iBAAiB;IAAE2I,WAAW,EAAEhB,mBAAmB;IAAEoB,KAAK,EAAE;EAAK,CAAC,EAC7E;IAAEL,OAAO,EAAEhC,sBAAsB;IAAEsC,QAAQ,EAAErC;EAAwB,CAAC,EACtE6C,qBAAqB,CAAC;IAClB3C,UAAU,EAAEP,wBAAwB;IACpCoB,UAAU,EAAElB;EAChB,CAAC,CAAC,CAAC2B,UAAU,EACb;IAAEO,OAAO,EAAErC,YAAY;IAAEyC,QAAQ,EAAE;EAAK,CAAC;AAC5C;AAET;EAAA,QAAAhZ,SAAA,oBAAAA,SAAA,KA/wCkFlD,EAAE,CAAAyN,iBAAA,CA+wCQ6P,oBAAoB,EAAc,CAAC;IACnH/S,IAAI,EAAErJ,QAAQ;IACdoN,IAAI,EAAE,CAAC;MACC+M,SAAS,EAAE,CACPN,mBAAmB,EACnB;QAAEe,OAAO,EAAE1I,iBAAiB;QAAE2I,WAAW,EAAEhB,mBAAmB;QAAEoB,KAAK,EAAE;MAAK,CAAC,EAC7E;QAAEL,OAAO,EAAEhC,sBAAsB;QAAEsC,QAAQ,EAAErC;MAAwB,CAAC,EACtE6C,qBAAqB,CAAC;QAClB3C,UAAU,EAAEP,wBAAwB;QACpCoB,UAAU,EAAElB;MAChB,CAAC,CAAC,CAAC2B,UAAU,EACb;QAAEO,OAAO,EAAErC,YAAY;QAAEyC,QAAQ,EAAE;MAAK,CAAC;IAEjD,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4B,gBAAgB,CAAC;AAItBC,iBAAA,GAJKD,gBAAgB;AAAA1b,eAAA,CAAhB0b,gBAAgB,wBAAAE,0BAAA5Q,iBAAA;EAAA,YAAAA,iBAAA,IACkF0Q,iBAAgB;AAAA;AAAA1b,eAAA,CADlH0b,gBAAgB,8BAxyC4D9d,EAAE,CAAA4d,gBAAA;EAAArT,IAAA,EA0yCqBuT;AAAgB;AAAA1b,eAAA,CAFnH0b,gBAAgB,8BAxyC4D9d,EAAE,CAAA6d,gBAAA;EAAAxC,SAAA,EA2yCkD,CAACG,iBAAiB,CAACkB,sBAAsB,CAAC,CAAC,CAAC;AAAC;AAEnL;EAAA,QAAAxZ,SAAA,oBAAAA,SAAA,KA7yCkFlD,EAAE,CAAAyN,iBAAA,CA6yCQqQ,gBAAgB,EAAc,CAAC;IAC/GvT,IAAI,EAAErJ,QAAQ;IACdoN,IAAI,EAAE,CAAC;MACC;AACpB;AACA;AACA;MACoB+M,SAAS,EAAE,CAACG,iBAAiB,CAACkB,sBAAsB,CAAC,CAAC,CAAC;IAC3D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,qBAAqB,CAAC;AAI3BC,sBAAA,GAJKD,qBAAqB;AAAA7b,eAAA,CAArB6b,qBAAqB,wBAAAE,+BAAA/Q,iBAAA;EAAA,YAAAA,iBAAA,IAC6E6Q,sBAAqB;AAAA;AAAA7b,eAAA,CADvH6b,qBAAqB,8BAh0CuDje,EAAE,CAAA4d,gBAAA;EAAArT,IAAA,EAk0CqB0T;AAAqB;AAAA7b,eAAA,CAFxH6b,qBAAqB,8BAh0CuDje,EAAE,CAAA6d,gBAAA;EAAAxC,SAAA,EAm0CuD,CAACyB,gBAAgB,CAAC,CAAC,CAACvB,UAAU;AAAC;AAE1K;EAAA,QAAArY,SAAA,oBAAAA,SAAA,KAr0CkFlD,EAAE,CAAAyN,iBAAA,CAq0CQwQ,qBAAqB,EAAc,CAAC;IACpH1T,IAAI,EAAErJ,QAAQ;IACdoN,IAAI,EAAE,CAAC;MACC+M,SAAS,EAAE,CAACyB,gBAAgB,CAAC,CAAC,CAACvB,UAAU;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASvN,YAAY,EAAEoF,iBAAiB,EAAEE,yBAAyB,EAAEtR,WAAW,EAAEkK,UAAU,EAAE+R,qBAAqB,EAAEH,gBAAgB,EAAER,oBAAoB,EAAErV,WAAW,EAAEF,gBAAgB,EAAE2D,iBAAiB,EAAEX,aAAa,EAAEmQ,eAAe,EAAEnZ,WAAW,EAAEuJ,kBAAkB,EAAErJ,WAAW,EAAEmS,sBAAsB,EAAEnN,UAAU,EAAEkC,WAAW,EAAEqC,YAAY,EAAER,gBAAgB,EAAEe,cAAc,EAAEtG,oBAAoB,EAAEuS,cAAc,EAAE8B,sBAAsB,EAAEtE,kBAAkB,EAAE4B,gBAAgB,EAAE7D,gCAAgC,EAAEiI,iBAAiB,EAAE4B,SAAS,EAAEd,gBAAgB,EAAEI,sBAAsB,EAAEI,gBAAgB,EAAED,oBAAoB,EAAEG,yBAAyB,EAAEJ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}