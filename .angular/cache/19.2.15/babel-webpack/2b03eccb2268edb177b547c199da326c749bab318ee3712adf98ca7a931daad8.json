{"ast": null, "code": "import { of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nexport class ColumnsManagementDataProvider {}\nexport class LocalStorageColumnsDataProvider extends ColumnsManagementDataProvider {\n  getColumns(gridId) {\n    return of(localStorage.getItem(gridId)).pipe(map(data => data !== null ? JSON.parse(data) : {}));\n  }\n  saveColumns(gridId, data) {\n    localStorage.setItem(gridId, JSON.stringify(data));\n    return of(data);\n  }\n}", "map": {"version": 3, "names": ["of", "map", "ColumnsManagementDataProvider", "LocalStorageColumnsDataProvider", "getColumns", "gridId", "localStorage", "getItem", "pipe", "data", "JSON", "parse", "saveColumns", "setItem", "stringify"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/columns-management/columns-management.model.ts"], "sourcesContent": ["import { of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nexport class ColumnsManagementDataProvider {\n}\nexport class LocalStorageColumnsDataProvider extends ColumnsManagementDataProvider {\n    getColumns(gridId) {\n        return of(localStorage.getItem(gridId)).pipe(map(data => data !== null ? JSON.parse(data) : {}));\n    }\n    saveColumns(gridId, data) {\n        localStorage.setItem(gridId, JSON.stringify(data));\n        return of(data);\n    }\n}\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,MAAM;AACzB,SAASC,GAAG,QAAQ,gBAAgB;AACpC,OAAO,MAAMC,6BAA6B,CAAC;AAE3C,OAAO,MAAMC,+BAA+B,SAASD,6BAA6B,CAAC;EAC/EE,UAAUA,CAACC,MAAM,EAAE;IACf,OAAOL,EAAE,CAACM,YAAY,CAACC,OAAO,CAACF,MAAM,CAAC,CAAC,CAACG,IAAI,CAACP,GAAG,CAACQ,IAAI,IAAIA,IAAI,KAAK,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpG;EACAG,WAAWA,CAACP,MAAM,EAAEI,IAAI,EAAE;IACtBH,YAAY,CAACO,OAAO,CAACR,MAAM,EAAEK,IAAI,CAACI,SAAS,CAACL,IAAI,CAAC,CAAC;IAClD,OAAOT,EAAE,CAACS,IAAI,CAAC;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}