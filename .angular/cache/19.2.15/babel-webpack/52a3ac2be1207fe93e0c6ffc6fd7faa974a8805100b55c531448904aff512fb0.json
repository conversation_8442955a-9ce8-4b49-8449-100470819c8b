{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { NgModule } from '@angular/core';\nimport { SwuiGamesSelectManagerComponent } from './games-select-manager.component';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { AvailableGamesComponent } from './available-games/available-games.component';\nimport { AvailableLabelsComponent } from './available-labels/available-labels.component';\nimport { GamesSelectManagerService } from './games-select-manager.service';\n// import { DragulaModule } from 'ng2-dragula';\nimport { SelectedItemsComponent } from './selected-items/selected-items.component';\nimport { CoinValueColumnComponent } from './game-select-columns/coin-value-column.component';\nimport { GameCoeffColumnComponent } from './game-select-columns/game-coeff-column.component';\nimport { ExtraColumnChooserComponent } from './game-select-columns/extra-column-chooser.component';\nimport { FormsModule } from '@angular/forms';\nimport { FilteredPipe } from './filtered.pipe';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { MatListModule } from '@angular/material/list';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatChipsModule } from '@angular/material/chips';\nlet SwuiGamesSelectManagerModule = class SwuiGamesSelectManagerModule {};\nSwuiGamesSelectManagerModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule,\n  // DragulaModule.forRoot(),\n  FormsModule, MatTabsModule, MatTooltipModule, MatButtonModule, MatInputModule, MatCheckboxModule, MatIconModule, MatListModule, DragDropModule, MatExpansionModule, MatChipsModule, ScrollingModule],\n  exports: [SwuiGamesSelectManagerComponent],\n  declarations: [SwuiGamesSelectManagerComponent, AvailableGamesComponent, AvailableLabelsComponent, SelectedItemsComponent, ExtraColumnChooserComponent, CoinValueColumnComponent, GameCoeffColumnComponent, FilteredPipe],\n  providers: [GamesSelectManagerService]\n})], SwuiGamesSelectManagerModule);\nexport { SwuiGamesSelectManagerModule };", "map": {"version": 3, "names": ["__decorate", "ScrollingModule", "NgModule", "SwuiGamesSelectManagerComponent", "CommonModule", "TranslateModule", "AvailableGamesComponent", "AvailableLabelsComponent", "GamesSelectManagerService", "SelectedItemsComponent", "CoinValueColumnComponent", "GameCoeffColumnComponent", "ExtraColumnChooserComponent", "FormsModule", "FilteredPipe", "MatTabsModule", "MatTooltipModule", "MatButtonModule", "MatIconModule", "MatInputModule", "MatCheckboxModule", "DragDropModule", "MatListModule", "MatExpansionModule", "MatChipsModule", "SwuiGamesSelectManagerModule", "imports", "exports", "declarations", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/games-select-manager.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { NgModule } from '@angular/core';\nimport { SwuiGamesSelectManagerComponent } from './games-select-manager.component';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { AvailableGamesComponent } from './available-games/available-games.component';\nimport { AvailableLabelsComponent } from './available-labels/available-labels.component';\nimport { GamesSelectManagerService } from './games-select-manager.service';\n// import { DragulaModule } from 'ng2-dragula';\nimport { SelectedItemsComponent } from './selected-items/selected-items.component';\nimport { CoinValueColumnComponent } from './game-select-columns/coin-value-column.component';\nimport { GameCoeffColumnComponent } from './game-select-columns/game-coeff-column.component';\nimport { ExtraColumnChooserComponent } from './game-select-columns/extra-column-chooser.component';\nimport { FormsModule } from '@angular/forms';\nimport { FilteredPipe } from './filtered.pipe';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { MatListModule } from '@angular/material/list';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatChipsModule } from '@angular/material/chips';\nlet SwuiGamesSelectManagerModule = class SwuiGamesSelectManagerModule {\n};\nSwuiGamesSelectManagerModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule,\n            // DragulaModule.forRoot(),\n            FormsModule,\n            MatTabsModule,\n            MatTooltipModule,\n            MatButtonModule,\n            MatInputModule,\n            MatCheckboxModule,\n            MatIconModule,\n            MatListModule,\n            DragDropModule,\n            MatExpansionModule,\n            MatChipsModule,\n            ScrollingModule,\n        ],\n        exports: [\n            SwuiGamesSelectManagerComponent,\n        ],\n        declarations: [\n            SwuiGamesSelectManagerComponent,\n            AvailableGamesComponent,\n            AvailableLabelsComponent,\n            SelectedItemsComponent,\n            ExtraColumnChooserComponent,\n            CoinValueColumnComponent,\n            GameCoeffColumnComponent,\n            FilteredPipe,\n        ],\n        providers: [\n            GamesSelectManagerService\n        ],\n    })\n], SwuiGamesSelectManagerModule);\nexport { SwuiGamesSelectManagerModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,+BAA+B,QAAQ,kCAAkC;AAClF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E;AACA,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,wBAAwB,QAAQ,mDAAmD;AAC5F,SAASC,wBAAwB,QAAQ,mDAAmD;AAC5F,SAASC,2BAA2B,QAAQ,sDAAsD;AAClG,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,IAAIC,4BAA4B,GAAG,MAAMA,4BAA4B,CAAC,EACrE;AACDA,4BAA4B,GAAGzB,UAAU,CAAC,CACtCE,QAAQ,CAAC;EACLwB,OAAO,EAAE,CACLtB,YAAY,EACZC,eAAe;EACf;EACAQ,WAAW,EACXE,aAAa,EACbC,gBAAgB,EAChBC,eAAe,EACfE,cAAc,EACdC,iBAAiB,EACjBF,aAAa,EACbI,aAAa,EACbD,cAAc,EACdE,kBAAkB,EAClBC,cAAc,EACdvB,eAAe,CAClB;EACD0B,OAAO,EAAE,CACLxB,+BAA+B,CAClC;EACDyB,YAAY,EAAE,CACVzB,+BAA+B,EAC/BG,uBAAuB,EACvBC,wBAAwB,EACxBE,sBAAsB,EACtBG,2BAA2B,EAC3BF,wBAAwB,EACxBC,wBAAwB,EACxBG,YAAY,CACf;EACDe,SAAS,EAAE,CACPrB,yBAAyB;AAEjC,CAAC,CAAC,CACL,EAAEiB,4BAA4B,CAAC;AAChC,SAASA,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}