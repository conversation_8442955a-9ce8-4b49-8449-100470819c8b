{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatButtonBase, _MatAnchorBase, _MatIconButton, _MatIconAnchor;\nconst _c0 = [\"mat-icon-button\", \"\"];\nconst _c1 = [\"*\"];\nconst _c2 = \".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\";\nconst _c3 = \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\";\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, ANIMATION_MODULE_TYPE, booleanAttribute, Directive, Input, Renderer2, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { M as MatRippleLoader } from './ripple-loader-Ce3DAhPW.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\n\n/** Injection token that can be used to provide the default options the button component. */\nconst MAT_BUTTON_CONFIG = new InjectionToken('MAT_BUTTON_CONFIG');\n/** Shared host configuration for all buttons */\nconst MAT_BUTTON_HOST = {\n  '[attr.disabled]': '_getDisabledAttribute()',\n  '[attr.aria-disabled]': '_getAriaDisabled()',\n  '[class.mat-mdc-button-disabled]': 'disabled',\n  '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n  '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  // MDC automatically applies the primary theme color to the button, but we want to support\n  // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n  // select and style this \"theme\".\n  '[class.mat-unthemed]': '!color',\n  // Add a class that applies to all buttons. This makes it easier to target if somebody\n  // wants to target all Material buttons.\n  '[class.mat-mdc-button-base]': 'true',\n  '[class]': 'color ? \"mat-\" + color : \"\"'\n};\n/** List of classes to add to buttons instances based on host attribute selector. */\nconst HOST_SELECTOR_MDC_CLASS_PAIR = [{\n  attribute: 'mat-button',\n  mdcClasses: ['mdc-button', 'mat-mdc-button']\n}, {\n  attribute: 'mat-flat-button',\n  mdcClasses: ['mdc-button', 'mdc-button--unelevated', 'mat-mdc-unelevated-button']\n}, {\n  attribute: 'mat-raised-button',\n  mdcClasses: ['mdc-button', 'mdc-button--raised', 'mat-mdc-raised-button']\n}, {\n  attribute: 'mat-stroked-button',\n  mdcClasses: ['mdc-button', 'mdc-button--outlined', 'mat-mdc-outlined-button']\n}, {\n  attribute: 'mat-fab',\n  mdcClasses: ['mdc-fab', 'mat-mdc-fab-base', 'mat-mdc-fab']\n}, {\n  attribute: 'mat-mini-fab',\n  mdcClasses: ['mdc-fab', 'mat-mdc-fab-base', 'mdc-fab--mini', 'mat-mdc-mini-fab']\n}, {\n  attribute: 'mat-icon-button',\n  mdcClasses: ['mdc-icon-button', 'mat-mdc-icon-button']\n}];\n/** Base class for all buttons.  */\nclass MatButtonBase {\n  /** Whether the ripple effect is disabled or not. */\n  get disableRipple() {\n    return this._disableRipple;\n  }\n  set disableRipple(value) {\n    this._disableRipple = value;\n    this._updateRippleDisabled();\n  }\n  /** Whether the button is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._updateRippleDisabled();\n  }\n  constructor() {\n    var _config$disabledInter, _config$color, _this$_rippleLoader;\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_animationMode\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }));\n    _defineProperty(this, \"_focusMonitor\", inject(FocusMonitor));\n    /**\n     * Handles the lazy creation of the MatButton ripple.\n     * Used to improve initial load time of large applications.\n     */\n    _defineProperty(this, \"_rippleLoader\", inject(MatRippleLoader));\n    /** Whether this button is a FAB. Used to apply the correct class on the ripple. */\n    _defineProperty(this, \"_isFab\", false);\n    /**\n     * Theme color of the button. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/button/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    _defineProperty(this, \"color\", void 0);\n    _defineProperty(this, \"_disableRipple\", false);\n    _defineProperty(this, \"_disabled\", false);\n    /** `aria-disabled` value of the button. */\n    _defineProperty(this, \"ariaDisabled\", void 0);\n    /**\n     * Natively disabled buttons prevent focus and any pointer events from reaching the button.\n     * In some scenarios this might not be desirable, because it can prevent users from finding out\n     * why the button is disabled (e.g. via tooltip). This is also useful for buttons that may\n     * become disabled when activated, which would cause focus to be transferred to the document\n     * body instead of remaining on the button.\n     *\n     * Enabling this input will change the button so that it is styled to be disabled and will be\n     * marked as `aria-disabled`, but it will allow the button to receive events and focus.\n     *\n     * Note that by enabling this, you need to set the `tabindex` yourself if the button isn't\n     * meant to be tabbable and you have to prevent the button action (e.g. form submissions).\n     */\n    _defineProperty(this, \"disabledInteractive\", void 0);\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const config = inject(MAT_BUTTON_CONFIG, {\n      optional: true\n    });\n    const element = this._elementRef.nativeElement;\n    const classList = element.classList;\n    this.disabledInteractive = (_config$disabledInter = config === null || config === void 0 ? void 0 : config.disabledInteractive) !== null && _config$disabledInter !== void 0 ? _config$disabledInter : false;\n    this.color = (_config$color = config === null || config === void 0 ? void 0 : config.color) !== null && _config$color !== void 0 ? _config$color : null;\n    (_this$_rippleLoader = this._rippleLoader) === null || _this$_rippleLoader === void 0 || _this$_rippleLoader.configureRipple(element, {\n      className: 'mat-mdc-button-ripple'\n    });\n    // For each of the variant selectors that is present in the button's host\n    // attributes, add the correct corresponding MDC classes.\n    for (const {\n      attribute,\n      mdcClasses\n    } of HOST_SELECTOR_MDC_CLASS_PAIR) {\n      if (element.hasAttribute(attribute)) {\n        classList.add(...mdcClasses);\n      }\n    }\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n  ngOnDestroy() {\n    var _this$_rippleLoader2;\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    (_this$_rippleLoader2 = this._rippleLoader) === null || _this$_rippleLoader2 === void 0 || _this$_rippleLoader2.destroyRipple(this._elementRef.nativeElement);\n  }\n  /** Focuses the button. */\n  focus(origin = 'program', options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._elementRef.nativeElement, origin, options);\n    } else {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n  _getAriaDisabled() {\n    if (this.ariaDisabled != null) {\n      return this.ariaDisabled;\n    }\n    return this.disabled && this.disabledInteractive ? true : null;\n  }\n  _getDisabledAttribute() {\n    return this.disabledInteractive || !this.disabled ? null : true;\n  }\n  _updateRippleDisabled() {\n    var _this$_rippleLoader3;\n    (_this$_rippleLoader3 = this._rippleLoader) === null || _this$_rippleLoader3 === void 0 || _this$_rippleLoader3.setDisabled(this._elementRef.nativeElement, this.disableRipple || this.disabled);\n  }\n}\n_MatButtonBase = MatButtonBase;\n_defineProperty(MatButtonBase, \"\\u0275fac\", function _MatButtonBase_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatButtonBase)();\n});\n_defineProperty(MatButtonBase, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatButtonBase,\n  inputs: {\n    color: \"color\",\n    disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n    ariaDisabled: [2, \"aria-disabled\", \"ariaDisabled\", booleanAttribute],\n    disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n  }\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonBase, [{\n    type: Directive\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    ariaDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute,\n        alias: 'aria-disabled'\n      }]\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/** Shared host configuration for buttons using the `<a>` tag. */\nconst MAT_ANCHOR_HOST = {\n  // Note that this is basically a noop on anchors,\n  // but it appears that some internal apps depend on it.\n  '[attr.disabled]': '_getDisabledAttribute()',\n  '[class.mat-mdc-button-disabled]': 'disabled',\n  '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n  '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n  // Note that we ignore the user-specified tabindex when it's disabled for\n  // consistency with the `mat-button` applied on native buttons where even\n  // though they have an index, they're not tabbable.\n  '[attr.tabindex]': 'disabled && !disabledInteractive ? -1 : tabIndex',\n  '[attr.aria-disabled]': '_getAriaDisabled()',\n  // MDC automatically applies the primary theme color to the button, but we want to support\n  // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n  // select and style this \"theme\".\n  '[class.mat-unthemed]': '!color',\n  // Add a class that applies to all buttons. This makes it easier to target if somebody\n  // wants to target all Material buttons.\n  '[class.mat-mdc-button-base]': 'true',\n  '[class]': 'color ? \"mat-\" + color : \"\"'\n};\n/**\n * Anchor button base.\n */\nclass MatAnchorBase extends MatButtonBase {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"_renderer\", inject(Renderer2));\n    _defineProperty(this, \"_cleanupClick\", void 0);\n    _defineProperty(this, \"tabIndex\", void 0);\n    _defineProperty(this, \"_haltDisabledEvents\", event => {\n      // A disabled button shouldn't apply any actions\n      if (this.disabled) {\n        event.preventDefault();\n        event.stopImmediatePropagation();\n      }\n    });\n  }\n  ngOnInit() {\n    this._ngZone.runOutsideAngular(() => {\n      this._cleanupClick = this._renderer.listen(this._elementRef.nativeElement, 'click', this._haltDisabledEvents);\n    });\n  }\n  ngOnDestroy() {\n    var _this$_cleanupClick;\n    super.ngOnDestroy();\n    (_this$_cleanupClick = this._cleanupClick) === null || _this$_cleanupClick === void 0 || _this$_cleanupClick.call(this);\n  }\n  _getAriaDisabled() {\n    if (this.ariaDisabled != null) {\n      return this.ariaDisabled;\n    }\n    return this.disabled || null;\n  }\n}\n_MatAnchorBase = MatAnchorBase;\n_defineProperty(MatAnchorBase, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatAnchorBase_BaseFactory;\n  return function _MatAnchorBase_Factory(__ngFactoryType__) {\n    return (ɵ_MatAnchorBase_BaseFactory || (ɵ_MatAnchorBase_BaseFactory = i0.ɵɵgetInheritedFactory(_MatAnchorBase)))(__ngFactoryType__ || _MatAnchorBase);\n  };\n})());\n_defineProperty(MatAnchorBase, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatAnchorBase,\n  inputs: {\n    tabIndex: [2, \"tabIndex\", \"tabIndex\", value => {\n      return value == null ? undefined : numberAttribute(value);\n    }]\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAnchorBase, [{\n    type: Directive\n  }], null, {\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => {\n          return value == null ? undefined : numberAttribute(value);\n        }\n      }]\n    }]\n  });\n})();\n\n/**\n * Material Design icon button component. This type of button displays a single interactive icon for\n * users to perform an action.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nclass MatIconButton extends MatButtonBase {\n  constructor() {\n    super();\n    this._rippleLoader.configureRipple(this._elementRef.nativeElement, {\n      centered: true\n    });\n  }\n}\n_MatIconButton = MatIconButton;\n_defineProperty(MatIconButton, \"\\u0275fac\", function _MatIconButton_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatIconButton)();\n});\n_defineProperty(MatIconButton, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatIconButton,\n  selectors: [[\"button\", \"mat-icon-button\", \"\"]],\n  hostVars: 14,\n  hostBindings: function _MatIconButton_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx._getAriaDisabled());\n      i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n      i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n    }\n  },\n  exportAs: [\"matButton\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c0,\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 0,\n  consts: [[1, \"mat-mdc-button-persistent-ripple\", \"mdc-icon-button__ripple\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n  template: function _MatIconButton_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelement(0, \"span\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n    }\n  },\n  styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconButton, [{\n    type: Component,\n    args: [{\n      selector: `button[mat-icon-button]`,\n      host: MAT_BUTTON_HOST,\n      exportAs: 'matButton',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"]\n    }]\n  }], () => [], null);\n})();\n/**\n * Material Design icon button component for anchor elements. This button displays a single\n * interaction icon that allows users to navigate across different routes or pages.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nclass MatIconAnchor extends MatAnchorBase {}\n_MatIconAnchor = MatIconAnchor;\n_defineProperty(MatIconAnchor, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatIconAnchor_BaseFactory;\n  return function _MatIconAnchor_Factory(__ngFactoryType__) {\n    return (ɵ_MatIconAnchor_BaseFactory || (ɵ_MatIconAnchor_BaseFactory = i0.ɵɵgetInheritedFactory(_MatIconAnchor)))(__ngFactoryType__ || _MatIconAnchor);\n  };\n})());\n_defineProperty(MatIconAnchor, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatIconAnchor,\n  selectors: [[\"a\", \"mat-icon-button\", \"\"]],\n  hostVars: 15,\n  hostBindings: function _MatIconAnchor_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"tabindex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx._getAriaDisabled());\n      i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n      i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-unthemed\", !ctx.color)(\"mat-mdc-button-base\", true);\n    }\n  },\n  exportAs: [\"matButton\", \"matAnchor\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c0,\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 0,\n  consts: [[1, \"mat-mdc-button-persistent-ripple\", \"mdc-icon-button__ripple\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n  template: function _MatIconAnchor_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelement(0, \"span\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n    }\n  },\n  styles: [_c2, _c3],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconAnchor, [{\n    type: Component,\n    args: [{\n      selector: `a[mat-icon-button]`,\n      host: MAT_ANCHOR_HOST,\n      exportAs: 'matButton, matAnchor',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\",\n      styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { MatIconButton as M, MatButtonBase as a, MAT_BUTTON_HOST as b, MatAnchorBase as c, MAT_ANCHOR_HOST as d, MatIconAnchor as e, MAT_BUTTON_CONFIG as f };\n//# sourceMappingURL=icon-button-D1J0zeqv.mjs.map", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "ElementRef", "NgZone", "ANIMATION_MODULE_TYPE", "booleanAttribute", "Directive", "Input", "Renderer2", "numberAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "FocusMonitor", "_CdkPrivateStyleLoader", "M", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_", "_StructuralStylesLoader", "MAT_BUTTON_CONFIG", "MAT_BUTTON_HOST", "HOST_SELECTOR_MDC_CLASS_PAIR", "attribute", "mdcClasses", "MatButtonBase", "disable<PERSON><PERSON><PERSON>", "_disableRipple", "value", "_updateRippleDisabled", "disabled", "_disabled", "constructor", "_config$disabledInter", "_config$color", "_this$_rippleLoader", "_defineProperty", "optional", "load", "config", "element", "_elementRef", "nativeElement", "classList", "disabledInteractive", "color", "_ripple<PERSON><PERSON>der", "configureRipple", "className", "hasAttribute", "add", "ngAfterViewInit", "_focusMonitor", "monitor", "ngOnDestroy", "_this$_rippleLoader2", "stopMonitoring", "destroyRipple", "focus", "origin", "options", "focusVia", "_getAriaDisabled", "ariaDisabled", "_getDisabledAttribute", "_this$_rippleLoader3", "setDisabled", "_MatButtonBase", "_MatButtonBase_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "inputs", "ngDevMode", "ɵsetClassMetadata", "args", "transform", "alias", "MAT_ANCHOR_HOST", "MatAnchorBase", "event", "preventDefault", "stopImmediatePropagation", "ngOnInit", "_ngZone", "runOutsideAngular", "_cleanupClick", "_renderer", "listen", "_haltDisabledEvents", "_this$_cleanupClick", "call", "_MatAnchorBase", "ɵ_MatAnchorBase_BaseFactory", "_MatAnchorBase_Factory", "ɵɵgetInheritedFactory", "tabIndex", "undefined", "features", "ɵɵInheritDefinitionFeature", "MatIconButton", "centered", "_MatIconButton", "_MatIconButton_Factory", "ɵɵdefineComponent", "selectors", "hostVars", "hostBindings", "_MatIconButton_HostBindings", "rf", "ctx", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "_animationMode", "exportAs", "attrs", "_c0", "ngContentSelectors", "_c1", "decls", "vars", "consts", "template", "_MatIconButton_Template", "ɵɵprojectionDef", "ɵɵelement", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "selector", "host", "None", "OnPush", "MatIconAnchor", "_MatIconAnchor", "ɵ_MatIconAnchor_BaseFactory", "_MatIconAnchor_Factory", "_MatIconAnchor_HostBindings", "_MatIconAnchor_Template", "_c2", "_c3", "a", "b", "c", "d", "e", "f"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/icon-button-D1J0zeqv.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, <PERSON><PERSON><PERSON>, ANIMATION_MODULE_TYPE, booleanAttribute, Directive, Input, Renderer2, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { M as MatRippleLoader } from './ripple-loader-Ce3DAhPW.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\n\n/** Injection token that can be used to provide the default options the button component. */\nconst MAT_BUTTON_CONFIG = new InjectionToken('MAT_BUTTON_CONFIG');\n/** Shared host configuration for all buttons */\nconst MAT_BUTTON_HOST = {\n    '[attr.disabled]': '_getDisabledAttribute()',\n    '[attr.aria-disabled]': '_getAriaDisabled()',\n    '[class.mat-mdc-button-disabled]': 'disabled',\n    '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n    '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n    // MDC automatically applies the primary theme color to the button, but we want to support\n    // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n    // select and style this \"theme\".\n    '[class.mat-unthemed]': '!color',\n    // Add a class that applies to all buttons. This makes it easier to target if somebody\n    // wants to target all Material buttons.\n    '[class.mat-mdc-button-base]': 'true',\n    '[class]': 'color ? \"mat-\" + color : \"\"',\n};\n/** List of classes to add to buttons instances based on host attribute selector. */\nconst HOST_SELECTOR_MDC_CLASS_PAIR = [\n    {\n        attribute: 'mat-button',\n        mdcClasses: ['mdc-button', 'mat-mdc-button'],\n    },\n    {\n        attribute: 'mat-flat-button',\n        mdcClasses: ['mdc-button', 'mdc-button--unelevated', 'mat-mdc-unelevated-button'],\n    },\n    {\n        attribute: 'mat-raised-button',\n        mdcClasses: ['mdc-button', 'mdc-button--raised', 'mat-mdc-raised-button'],\n    },\n    {\n        attribute: 'mat-stroked-button',\n        mdcClasses: ['mdc-button', 'mdc-button--outlined', 'mat-mdc-outlined-button'],\n    },\n    {\n        attribute: 'mat-fab',\n        mdcClasses: ['mdc-fab', 'mat-mdc-fab-base', 'mat-mdc-fab'],\n    },\n    {\n        attribute: 'mat-mini-fab',\n        mdcClasses: ['mdc-fab', 'mat-mdc-fab-base', 'mdc-fab--mini', 'mat-mdc-mini-fab'],\n    },\n    {\n        attribute: 'mat-icon-button',\n        mdcClasses: ['mdc-icon-button', 'mat-mdc-icon-button'],\n    },\n];\n/** Base class for all buttons.  */\nclass MatButtonBase {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n    _focusMonitor = inject(FocusMonitor);\n    /**\n     * Handles the lazy creation of the MatButton ripple.\n     * Used to improve initial load time of large applications.\n     */\n    _rippleLoader = inject(MatRippleLoader);\n    /** Whether this button is a FAB. Used to apply the correct class on the ripple. */\n    _isFab = false;\n    /**\n     * Theme color of the button. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/button/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n        return this._disableRipple;\n    }\n    set disableRipple(value) {\n        this._disableRipple = value;\n        this._updateRippleDisabled();\n    }\n    _disableRipple = false;\n    /** Whether the button is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._updateRippleDisabled();\n    }\n    _disabled = false;\n    /** `aria-disabled` value of the button. */\n    ariaDisabled;\n    /**\n     * Natively disabled buttons prevent focus and any pointer events from reaching the button.\n     * In some scenarios this might not be desirable, because it can prevent users from finding out\n     * why the button is disabled (e.g. via tooltip). This is also useful for buttons that may\n     * become disabled when activated, which would cause focus to be transferred to the document\n     * body instead of remaining on the button.\n     *\n     * Enabling this input will change the button so that it is styled to be disabled and will be\n     * marked as `aria-disabled`, but it will allow the button to receive events and focus.\n     *\n     * Note that by enabling this, you need to set the `tabindex` yourself if the button isn't\n     * meant to be tabbable and you have to prevent the button action (e.g. form submissions).\n     */\n    disabledInteractive;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const config = inject(MAT_BUTTON_CONFIG, { optional: true });\n        const element = this._elementRef.nativeElement;\n        const classList = element.classList;\n        this.disabledInteractive = config?.disabledInteractive ?? false;\n        this.color = config?.color ?? null;\n        this._rippleLoader?.configureRipple(element, { className: 'mat-mdc-button-ripple' });\n        // For each of the variant selectors that is present in the button's host\n        // attributes, add the correct corresponding MDC classes.\n        for (const { attribute, mdcClasses } of HOST_SELECTOR_MDC_CLASS_PAIR) {\n            if (element.hasAttribute(attribute)) {\n                classList.add(...mdcClasses);\n            }\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._elementRef, true);\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n    }\n    /** Focuses the button. */\n    focus(origin = 'program', options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._elementRef.nativeElement, origin, options);\n        }\n        else {\n            this._elementRef.nativeElement.focus(options);\n        }\n    }\n    _getAriaDisabled() {\n        if (this.ariaDisabled != null) {\n            return this.ariaDisabled;\n        }\n        return this.disabled && this.disabledInteractive ? true : null;\n    }\n    _getDisabledAttribute() {\n        return this.disabledInteractive || !this.disabled ? null : true;\n    }\n    _updateRippleDisabled() {\n        this._rippleLoader?.setDisabled(this._elementRef.nativeElement, this.disableRipple || this.disabled);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatButtonBase, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatButtonBase, isStandalone: true, inputs: { color: \"color\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], ariaDisabled: [\"aria-disabled\", \"ariaDisabled\", booleanAttribute], disabledInteractive: [\"disabledInteractive\", \"disabledInteractive\", booleanAttribute] }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatButtonBase, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], ariaDisabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute, alias: 'aria-disabled' }]\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n/** Shared host configuration for buttons using the `<a>` tag. */\nconst MAT_ANCHOR_HOST = {\n    // Note that this is basically a noop on anchors,\n    // but it appears that some internal apps depend on it.\n    '[attr.disabled]': '_getDisabledAttribute()',\n    '[class.mat-mdc-button-disabled]': 'disabled',\n    '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n    '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n    // Note that we ignore the user-specified tabindex when it's disabled for\n    // consistency with the `mat-button` applied on native buttons where even\n    // though they have an index, they're not tabbable.\n    '[attr.tabindex]': 'disabled && !disabledInteractive ? -1 : tabIndex',\n    '[attr.aria-disabled]': '_getAriaDisabled()',\n    // MDC automatically applies the primary theme color to the button, but we want to support\n    // an unthemed version. If color is undefined, apply a CSS class that makes it easy to\n    // select and style this \"theme\".\n    '[class.mat-unthemed]': '!color',\n    // Add a class that applies to all buttons. This makes it easier to target if somebody\n    // wants to target all Material buttons.\n    '[class.mat-mdc-button-base]': 'true',\n    '[class]': 'color ? \"mat-\" + color : \"\"',\n};\n/**\n * Anchor button base.\n */\nclass MatAnchorBase extends MatButtonBase {\n    _renderer = inject(Renderer2);\n    _cleanupClick;\n    tabIndex;\n    ngOnInit() {\n        this._ngZone.runOutsideAngular(() => {\n            this._cleanupClick = this._renderer.listen(this._elementRef.nativeElement, 'click', this._haltDisabledEvents);\n        });\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._cleanupClick?.();\n    }\n    _haltDisabledEvents = (event) => {\n        // A disabled button shouldn't apply any actions\n        if (this.disabled) {\n            event.preventDefault();\n            event.stopImmediatePropagation();\n        }\n    };\n    _getAriaDisabled() {\n        if (this.ariaDisabled != null) {\n            return this.ariaDisabled;\n        }\n        return this.disabled || null;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAnchorBase, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatAnchorBase, isStandalone: true, inputs: { tabIndex: [\"tabIndex\", \"tabIndex\", (value) => {\n                    return value == null ? undefined : numberAttribute(value);\n                }] }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAnchorBase, decorators: [{\n            type: Directive\n        }], propDecorators: { tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => {\n                            return value == null ? undefined : numberAttribute(value);\n                        },\n                    }]\n            }] } });\n\n/**\n * Material Design icon button component. This type of button displays a single interactive icon for\n * users to perform an action.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nclass MatIconButton extends MatButtonBase {\n    constructor() {\n        super();\n        this._rippleLoader.configureRipple(this._elementRef.nativeElement, { centered: true });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatIconButton, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatIconButton, isStandalone: true, selector: \"button[mat-icon-button]\", host: { properties: { \"attr.disabled\": \"_getDisabledAttribute()\", \"attr.aria-disabled\": \"_getAriaDisabled()\", \"class.mat-mdc-button-disabled\": \"disabled\", \"class.mat-mdc-button-disabled-interactive\": \"disabledInteractive\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"class.mat-unthemed\": \"!color\", \"class.mat-mdc-button-base\": \"true\", \"class\": \"color ? \\\"mat-\\\" + color : \\\"\\\"\" } }, exportAs: [\"matButton\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\", styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatIconButton, decorators: [{\n            type: Component,\n            args: [{ selector: `button[mat-icon-button]`, host: MAT_BUTTON_HOST, exportAs: 'matButton', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\", styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"] }]\n        }], ctorParameters: () => [] });\n/**\n * Material Design icon button component for anchor elements. This button displays a single\n * interaction icon that allows users to navigate across different routes or pages.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nclass MatIconAnchor extends MatAnchorBase {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatIconAnchor, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatIconAnchor, isStandalone: true, selector: \"a[mat-icon-button]\", host: { properties: { \"attr.disabled\": \"_getDisabledAttribute()\", \"class.mat-mdc-button-disabled\": \"disabled\", \"class.mat-mdc-button-disabled-interactive\": \"disabledInteractive\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"attr.tabindex\": \"disabled && !disabledInteractive ? -1 : tabIndex\", \"attr.aria-disabled\": \"_getAriaDisabled()\", \"class.mat-unthemed\": \"!color\", \"class.mat-mdc-button-base\": \"true\", \"class\": \"color ? \\\"mat-\\\" + color : \\\"\\\"\" } }, exportAs: [\"matButton\", \"matAnchor\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\", styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatIconAnchor, decorators: [{\n            type: Component,\n            args: [{ selector: `a[mat-icon-button]`, host: MAT_ANCHOR_HOST, exportAs: 'matButton, matAnchor', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<span class=\\\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\\\"></span>\\n\\n<ng-content></ng-content>\\n\\n<!--\\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\\n-->\\n<span class=\\\"mat-focus-indicator\\\"></span>\\n\\n<span class=\\\"mat-mdc-button-touch-target\\\"></span>\\n\", styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"] }]\n        }] });\n\nexport { MatIconButton as M, MatButtonBase as a, MAT_BUTTON_HOST as b, MatAnchorBase as c, MAT_ANCHOR_HOST as d, MatIconAnchor as e, MAT_BUTTON_CONFIG as f };\n//# sourceMappingURL=icon-button-D1J0zeqv.mjs.map\n"], "mappings": ";;;;;;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,QAAQ,eAAe;AACxN,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;;AAE/E;AACA,MAAMC,iBAAiB,GAAG,IAAInB,cAAc,CAAC,mBAAmB,CAAC;AACjE;AACA,MAAMoB,eAAe,GAAG;EACpB,iBAAiB,EAAE,yBAAyB;EAC5C,sBAAsB,EAAE,oBAAoB;EAC5C,iCAAiC,EAAE,UAAU;EAC7C,6CAA6C,EAAE,qBAAqB;EACpE,iCAAiC,EAAE,qCAAqC;EACxE;EACA;EACA;EACA,sBAAsB,EAAE,QAAQ;EAChC;EACA;EACA,6BAA6B,EAAE,MAAM;EACrC,SAAS,EAAE;AACf,CAAC;AACD;AACA,MAAMC,4BAA4B,GAAG,CACjC;EACIC,SAAS,EAAE,YAAY;EACvBC,UAAU,EAAE,CAAC,YAAY,EAAE,gBAAgB;AAC/C,CAAC,EACD;EACID,SAAS,EAAE,iBAAiB;EAC5BC,UAAU,EAAE,CAAC,YAAY,EAAE,wBAAwB,EAAE,2BAA2B;AACpF,CAAC,EACD;EACID,SAAS,EAAE,mBAAmB;EAC9BC,UAAU,EAAE,CAAC,YAAY,EAAE,oBAAoB,EAAE,uBAAuB;AAC5E,CAAC,EACD;EACID,SAAS,EAAE,oBAAoB;EAC/BC,UAAU,EAAE,CAAC,YAAY,EAAE,sBAAsB,EAAE,yBAAyB;AAChF,CAAC,EACD;EACID,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,CAAC,SAAS,EAAE,kBAAkB,EAAE,aAAa;AAC7D,CAAC,EACD;EACID,SAAS,EAAE,cAAc;EACzBC,UAAU,EAAE,CAAC,SAAS,EAAE,kBAAkB,EAAE,eAAe,EAAE,kBAAkB;AACnF,CAAC,EACD;EACID,SAAS,EAAE,iBAAiB;EAC5BC,UAAU,EAAE,CAAC,iBAAiB,EAAE,qBAAqB;AACzD,CAAC,CACJ;AACD;AACA,MAAMC,aAAa,CAAC;EAoBhB;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAaA,CAACE,KAAK,EAAE;IACrB,IAAI,CAACD,cAAc,GAAGC,KAAK;IAC3B,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EAEA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACF,KAAK,EAAE;IAChB,IAAI,CAACG,SAAS,GAAGH,KAAK;IACtB,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EAkBAG,WAAWA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,aAAA,EAAAC,mBAAA;IAAAC,eAAA,sBArDAlC,MAAM,CAACC,UAAU,CAAC;IAAAiC,eAAA,kBACtBlC,MAAM,CAACE,MAAM,CAAC;IAAAgC,eAAA,yBACPlC,MAAM,CAACG,qBAAqB,EAAE;MAAEgC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAD,eAAA,wBAClDlC,MAAM,CAACY,YAAY,CAAC;IACpC;AACJ;AACA;AACA;IAHIsB,eAAA,wBAIgBlC,MAAM,CAACe,eAAe,CAAC;IACvC;IAAAmB,eAAA,iBACS,KAAK;IACd;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA;IAAAA,eAAA,yBAgBiB,KAAK;IAAAA,eAAA,oBASV,KAAK;IACjB;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAZIA,eAAA;IAeIlC,MAAM,CAACa,sBAAsB,CAAC,CAACuB,IAAI,CAACnB,uBAAuB,CAAC;IAC5D,MAAMoB,MAAM,GAAGrC,MAAM,CAACkB,iBAAiB,EAAE;MAAEiB,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC5D,MAAMG,OAAO,GAAG,IAAI,CAACC,WAAW,CAACC,aAAa;IAC9C,MAAMC,SAAS,GAAGH,OAAO,CAACG,SAAS;IACnC,IAAI,CAACC,mBAAmB,IAAAX,qBAAA,GAAGM,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,mBAAmB,cAAAX,qBAAA,cAAAA,qBAAA,GAAI,KAAK;IAC/D,IAAI,CAACY,KAAK,IAAAX,aAAA,GAAGK,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEM,KAAK,cAAAX,aAAA,cAAAA,aAAA,GAAI,IAAI;IAClC,CAAAC,mBAAA,OAAI,CAACW,aAAa,cAAAX,mBAAA,eAAlBA,mBAAA,CAAoBY,eAAe,CAACP,OAAO,EAAE;MAAEQ,SAAS,EAAE;IAAwB,CAAC,CAAC;IACpF;IACA;IACA,KAAK,MAAM;MAAEzB,SAAS;MAAEC;IAAW,CAAC,IAAIF,4BAA4B,EAAE;MAClE,IAAIkB,OAAO,CAACS,YAAY,CAAC1B,SAAS,CAAC,EAAE;QACjCoB,SAAS,CAACO,GAAG,CAAC,GAAG1B,UAAU,CAAC;MAChC;IACJ;EACJ;EACA2B,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,aAAa,CAACC,OAAO,CAAC,IAAI,CAACZ,WAAW,EAAE,IAAI,CAAC;EACtD;EACAa,WAAWA,CAAA,EAAG;IAAA,IAAAC,oBAAA;IACV,IAAI,CAACH,aAAa,CAACI,cAAc,CAAC,IAAI,CAACf,WAAW,CAAC;IACnD,CAAAc,oBAAA,OAAI,CAACT,aAAa,cAAAS,oBAAA,eAAlBA,oBAAA,CAAoBE,aAAa,CAAC,IAAI,CAAChB,WAAW,CAACC,aAAa,CAAC;EACrE;EACA;EACAgB,KAAKA,CAACC,MAAM,GAAG,SAAS,EAAEC,OAAO,EAAE;IAC/B,IAAID,MAAM,EAAE;MACR,IAAI,CAACP,aAAa,CAACS,QAAQ,CAAC,IAAI,CAACpB,WAAW,CAACC,aAAa,EAAEiB,MAAM,EAAEC,OAAO,CAAC;IAChF,CAAC,MACI;MACD,IAAI,CAACnB,WAAW,CAACC,aAAa,CAACgB,KAAK,CAACE,OAAO,CAAC;IACjD;EACJ;EACAE,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,EAAE;MAC3B,OAAO,IAAI,CAACA,YAAY;IAC5B;IACA,OAAO,IAAI,CAACjC,QAAQ,IAAI,IAAI,CAACc,mBAAmB,GAAG,IAAI,GAAG,IAAI;EAClE;EACAoB,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACpB,mBAAmB,IAAI,CAAC,IAAI,CAACd,QAAQ,GAAG,IAAI,GAAG,IAAI;EACnE;EACAD,qBAAqBA,CAAA,EAAG;IAAA,IAAAoC,oBAAA;IACpB,CAAAA,oBAAA,OAAI,CAACnB,aAAa,cAAAmB,oBAAA,eAAlBA,oBAAA,CAAoBC,WAAW,CAAC,IAAI,CAACzB,WAAW,CAACC,aAAa,EAAE,IAAI,CAAChB,aAAa,IAAI,IAAI,CAACI,QAAQ,CAAC;EACxG;AAGJ;AAACqC,cAAA,GApGK1C,aAAa;AAAAW,eAAA,CAAbX,aAAa,wBAAA2C,uBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAkGoF5C,cAAa;AAAA;AAAAW,eAAA,CAlG9GX,aAAa,8BAqG8DzB,EAAE,CAAAsE,iBAAA;EAAAC,IAAA,EAFQ9C,cAAa;EAAA+C,MAAA;IAAA3B,KAAA;IAAAnB,aAAA,wCAAkGpB,gBAAgB;IAAAwB,QAAA,8BAAsCxB,gBAAgB;IAAAyD,YAAA,uCAAmDzD,gBAAgB;IAAAsC,mBAAA,oDAAuEtC,gBAAgB;EAAA;AAAA;AAE1a;EAAA,QAAAmE,SAAA,oBAAAA,SAAA,KAAiFzE,EAAE,CAAA0E,iBAAA,CAAQjD,aAAa,EAAc,CAAC;IAC3G8C,IAAI,EAAEhE;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEsC,KAAK,EAAE,CAAC;MAChD0B,IAAI,EAAE/D;IACV,CAAC,CAAC;IAAEkB,aAAa,EAAE,CAAC;MAChB6C,IAAI,EAAE/D,KAAK;MACXmE,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAEtE;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwB,QAAQ,EAAE,CAAC;MACXyC,IAAI,EAAE/D,KAAK;MACXmE,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAEtE;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyD,YAAY,EAAE,CAAC;MACfQ,IAAI,EAAE/D,KAAK;MACXmE,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAEtE,gBAAgB;QAAEuE,KAAK,EAAE;MAAgB,CAAC;IAClE,CAAC,CAAC;IAAEjC,mBAAmB,EAAE,CAAC;MACtB2B,IAAI,EAAE/D,KAAK;MACXmE,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAEtE;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMwE,eAAe,GAAG;EACpB;EACA;EACA,iBAAiB,EAAE,yBAAyB;EAC5C,iCAAiC,EAAE,UAAU;EAC7C,6CAA6C,EAAE,qBAAqB;EACpE,iCAAiC,EAAE,qCAAqC;EACxE;EACA;EACA;EACA,iBAAiB,EAAE,kDAAkD;EACrE,sBAAsB,EAAE,oBAAoB;EAC5C;EACA;EACA;EACA,sBAAsB,EAAE,QAAQ;EAChC;EACA;EACA,6BAA6B,EAAE,MAAM;EACrC,SAAS,EAAE;AACf,CAAC;AACD;AACA;AACA;AACA,MAAMC,aAAa,SAAStD,aAAa,CAAC;EAAAO,YAAA,GAAA2C,IAAA;IAAA,SAAAA,IAAA;IAAAvC,eAAA,oBAC1BlC,MAAM,CAACO,SAAS,CAAC;IAAA2B,eAAA;IAAAA,eAAA;IAAAA,eAAA,8BAYN4C,KAAK,IAAK;MAC7B;MACA,IAAI,IAAI,CAAClD,QAAQ,EAAE;QACfkD,KAAK,CAACC,cAAc,CAAC,CAAC;QACtBD,KAAK,CAACE,wBAAwB,CAAC,CAAC;MACpC;IACJ,CAAC;EAAA;EAfDC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,IAAI,CAAC/C,WAAW,CAACC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC+C,mBAAmB,CAAC;IACjH,CAAC,CAAC;EACN;EACAnC,WAAWA,CAAA,EAAG;IAAA,IAAAoC,mBAAA;IACV,KAAK,CAACpC,WAAW,CAAC,CAAC;IACnB,CAAAoC,mBAAA,OAAI,CAACJ,aAAa,cAAAI,mBAAA,eAAlBA,mBAAA,CAAAC,IAAA,KAAqB,CAAC;EAC1B;EAQA7B,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,EAAE;MAC3B,OAAO,IAAI,CAACA,YAAY;IAC5B;IACA,OAAO,IAAI,CAACjC,QAAQ,IAAI,IAAI;EAChC;AAKJ;AAAC8D,cAAA,GA9BKb,aAAa;AAAA3C,eAAA,CAAb2C,aAAa;EAAA,IAAAc,2BAAA;EAAA,gBAAAC,uBAAAzB,iBAAA;IAAA,QAAAwB,2BAAA,KAAAA,2BAAA,GA1C8D7F,EAAE,CAAA+F,qBAAA,CAoEoBhB,cAAa,IAAAV,iBAAA,IAAbU,cAAa;EAAA;AAAA;AAAA3C,eAAA,CA1B9G2C,aAAa,8BA1C8D/E,EAAE,CAAAsE,iBAAA;EAAAC,IAAA,EAqEQQ,cAAa;EAAAP,MAAA;IAAAwB,QAAA,8BAAoEpE,KAAK,IAAK;MAClK,OAAOA,KAAK,IAAI,IAAI,GAAGqE,SAAS,GAAGvF,eAAe,CAACkB,KAAK,CAAC;IAC7D,CAAC;EAAA;EAAAsE,QAAA,GAvEgElG,EAAE,CAAAmG,0BAAA;AAAA;AAyEnF;EAAA,QAAA1B,SAAA,oBAAAA,SAAA,KAzEiFzE,EAAE,CAAA0E,iBAAA,CAyEQK,aAAa,EAAc,CAAC;IAC3GR,IAAI,EAAEhE;EACV,CAAC,CAAC,QAAkB;IAAEyF,QAAQ,EAAE,CAAC;MACzBzB,IAAI,EAAE/D,KAAK;MACXmE,IAAI,EAAE,CAAC;QACCC,SAAS,EAAGhD,KAAK,IAAK;UAClB,OAAOA,KAAK,IAAI,IAAI,GAAGqE,SAAS,GAAGvF,eAAe,CAACkB,KAAK,CAAC;QAC7D;MACJ,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMwE,aAAa,SAAS3E,aAAa,CAAC;EACtCO,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACc,aAAa,CAACC,eAAe,CAAC,IAAI,CAACN,WAAW,CAACC,aAAa,EAAE;MAAE2D,QAAQ,EAAE;IAAK,CAAC,CAAC;EAC1F;AAGJ;AAACC,cAAA,GAPKF,aAAa;AAAAhE,eAAA,CAAbgE,aAAa,wBAAAG,uBAAAlC,iBAAA;EAAA,YAAAA,iBAAA,IAKoF+B,cAAa;AAAA;AAAAhE,eAAA,CAL9GgE,aAAa,8BAzF8DpG,EAAE,CAAAwG,iBAAA;EAAAjC,IAAA,EA+FQ6B,cAAa;EAAAK,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA/FvB7G,EAAE,CAAA+G,WAAA,aA+FQD,GAAA,CAAA9C,qBAAA,CAAsB,CAAC,mBAAvB8C,GAAA,CAAAhD,gBAAA,CAAiB,CAAC;MA/F5B9D,EAAE,CAAAgH,UAAA,CAAAF,GAAA,CAAAjE,KAAA,GA+FgB,MAAM,GAAAiE,GAAA,CAAAjE,KAAA,GAAW,EAAb,CAAC;MA/FvB7C,EAAE,CAAAiH,WAAA,4BAAAH,GAAA,CAAAhF,QA+FoB,CAAC,wCAAAgF,GAAA,CAAAlE,mBAAD,CAAC,4BAAAkE,GAAA,CAAAI,cAAA,KAAM,gBAAP,CAAC,kBAAAJ,GAAA,CAAAjE,KAAD,CAAC,wBAAb,IAAY,CAAC;IAAA;EAAA;EAAAsE,QAAA;EAAAjB,QAAA,GA/FvBlG,EAAE,CAAAmG,0BAAA;EAAAiB,KAAA,EAAAC,GAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,wBAAAf,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF7G,EAAE,CAAA6H,eAAA;MAAF7H,EAAE,CAAA8H,SAAA,aA+FsoB,CAAC;MA/FzoB9H,EAAE,CAAA+H,YAAA,EA+FmqB,CAAC;MA/FtqB/H,EAAE,CAAA8H,SAAA,aA+F65B,CAAC,aAAsD,CAAC;IAAA;EAAA;EAAAE,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAExiC;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KAjGiFzE,EAAE,CAAA0E,iBAAA,CAiGQ0B,aAAa,EAAc,CAAC;IAC3G7B,IAAI,EAAE5D,SAAS;IACfgE,IAAI,EAAE,CAAC;MAAEwD,QAAQ,EAAE,yBAAyB;MAAEC,IAAI,EAAE/G,eAAe;MAAE8F,QAAQ,EAAE,WAAW;MAAEc,aAAa,EAAErH,iBAAiB,CAACyH,IAAI;MAAEH,eAAe,EAAErH,uBAAuB,CAACyH,MAAM;MAAEX,QAAQ,EAAE,kaAAka;MAAEK,MAAM,EAAE,CAAC,gzHAAgzH,EAAE,kVAAkV;IAAE,CAAC;EACtvJ,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA,MAAMO,aAAa,SAASxD,aAAa,CAAC;AAGzCyD,cAAA,GAHKD,aAAa;AAAAnG,eAAA,CAAbmG,aAAa;EAAA,IAAAE,2BAAA;EAAA,gBAAAC,uBAAArE,iBAAA;IAAA,QAAAoE,2BAAA,KAAAA,2BAAA,GA1G8DzI,EAAE,CAAA+F,qBAAA,CA2GoBwC,cAAa,IAAAlE,iBAAA,IAAbkE,cAAa;EAAA;AAAA;AAAAnG,eAAA,CAD9GmG,aAAa,8BA1G8DvI,EAAE,CAAAwG,iBAAA;EAAAjC,IAAA,EA4GQgE,cAAa;EAAA9B,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAgC,4BAAA9B,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA5GvB7G,EAAE,CAAA+G,WAAA,aA4GQD,GAAA,CAAA9C,qBAAA,CAAsB,CAAC,cAAA8C,GAAA,CAAAhF,QAAA,KAAAgF,GAAA,CAAAlE,mBAAA,IAAa,CAAC,GAAAkE,GAAA,CAAAd,QAAA,mBAArCc,GAAA,CAAAhD,gBAAA,CAAiB,CAAC;MA5G5B9D,EAAE,CAAAgH,UAAA,CAAAF,GAAA,CAAAjE,KAAA,GA4GgB,MAAM,GAAAiE,GAAA,CAAAjE,KAAA,GAAW,EAAb,CAAC;MA5GvB7C,EAAE,CAAAiH,WAAA,4BAAAH,GAAA,CAAAhF,QA4GoB,CAAC,wCAAAgF,GAAA,CAAAlE,mBAAD,CAAC,4BAAAkE,GAAA,CAAAI,cAAA,KAAM,gBAAP,CAAC,kBAAAJ,GAAA,CAAAjE,KAAD,CAAC,wBAAb,IAAY,CAAC;IAAA;EAAA;EAAAsE,QAAA;EAAAjB,QAAA,GA5GvBlG,EAAE,CAAAmG,0BAAA;EAAAiB,KAAA,EAAAC,GAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAiB,wBAAA/B,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF7G,EAAE,CAAA6H,eAAA;MAAF7H,EAAE,CAAA8H,SAAA,aA4GmtB,CAAC;MA5GttB9H,EAAE,CAAA+H,YAAA,EA4GgvB,CAAC;MA5GnvB/H,EAAE,CAAA8H,SAAA,aA4G0+B,CAAC,aAAsD,CAAC;IAAA;EAAA;EAAAE,MAAA,GAAAa,GAAA,EAAAC,GAAA;EAAAb,aAAA;EAAAC,eAAA;AAAA;AAErnC;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KA9GiFzE,EAAE,CAAA0E,iBAAA,CA8GQ6D,aAAa,EAAc,CAAC;IAC3GhE,IAAI,EAAE5D,SAAS;IACfgE,IAAI,EAAE,CAAC;MAAEwD,QAAQ,EAAE,oBAAoB;MAAEC,IAAI,EAAEtD,eAAe;MAAEqC,QAAQ,EAAE,sBAAsB;MAAEc,aAAa,EAAErH,iBAAiB,CAACyH,IAAI;MAAEH,eAAe,EAAErH,uBAAuB,CAACyH,MAAM;MAAEX,QAAQ,EAAE,kaAAka;MAAEK,MAAM,EAAE,CAAC,gzHAAgzH,EAAE,kVAAkV;IAAE,CAAC;EAC5vJ,CAAC,CAAC;AAAA;AAEV,SAAS5B,aAAa,IAAIpF,CAAC,EAAES,aAAa,IAAIsH,CAAC,EAAE1H,eAAe,IAAI2H,CAAC,EAAEjE,aAAa,IAAIkE,CAAC,EAAEnE,eAAe,IAAIoE,CAAC,EAAEX,aAAa,IAAIY,CAAC,EAAE/H,iBAAiB,IAAIgI,CAAC;AAC3J", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}