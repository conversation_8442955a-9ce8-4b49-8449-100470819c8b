{"ast": null, "code": "var _SwuiTdImageWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./image.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./image.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdImageWidgetComponent = (_SwuiTdImageWidgetComponent = class SwuiTdImageWidgetComponent {\n  constructor({\n    schema,\n    row,\n    value\n  }) {\n    var _schema$td, _schema$td2, _schema$td3;\n    const classFn = (_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.classFn;\n    const imageFn = (_schema$td2 = schema.td) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.imageFn;\n    this.classObj = classFn && classFn(row, schema);\n    this.image = imageFn && imageFn(row, schema) || value;\n    this.alt = ((_schema$td3 = schema.td) === null || _schema$td3 === void 0 ? void 0 : _schema$td3.alt) || '';\n  }\n}, _SwuiTdImageWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdImageWidgetComponent);\nSwuiTdImageWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-image-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdImageWidgetComponent);\nexport { SwuiTdImageWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdImageWidgetComponent", "_SwuiTdImageWidgetComponent", "constructor", "schema", "row", "value", "_schema$td", "_schema$td2", "_schema$td3", "classFn", "td", "imageFn", "classObj", "image", "alt", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/image/image.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./image.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./image.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdImageWidgetComponent = class SwuiTdImageWidgetComponent {\n    constructor({ schema, row, value }) {\n        const classFn = schema.td?.classFn;\n        const imageFn = schema.td?.imageFn;\n        this.classObj = classFn && classFn(row, schema);\n        this.image = (imageFn && imageFn(row, schema)) || value;\n        this.alt = schema.td?.alt || '';\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdImageWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-image-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdImageWidgetComponent);\nexport { SwuiTdImageWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,0BAA0B,IAAAC,2BAAA,GAAG,MAAMD,0BAA0B,CAAC;EAC9DE,WAAWA,CAAC;IAAEC,MAAM;IAAEC,GAAG;IAAEC;EAAM,CAAC,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA;IAChC,MAAMC,OAAO,IAAAH,UAAA,GAAGH,MAAM,CAACO,EAAE,cAAAJ,UAAA,uBAATA,UAAA,CAAWG,OAAO;IAClC,MAAME,OAAO,IAAAJ,WAAA,GAAGJ,MAAM,CAACO,EAAE,cAAAH,WAAA,uBAATA,WAAA,CAAWI,OAAO;IAClC,IAAI,CAACC,QAAQ,GAAGH,OAAO,IAAIA,OAAO,CAACL,GAAG,EAAED,MAAM,CAAC;IAC/C,IAAI,CAACU,KAAK,GAAIF,OAAO,IAAIA,OAAO,CAACP,GAAG,EAAED,MAAM,CAAC,IAAKE,KAAK;IACvD,IAAI,CAACS,GAAG,GAAG,EAAAN,WAAA,GAAAL,MAAM,CAACO,EAAE,cAAAF,WAAA,uBAATA,WAAA,CAAWM,GAAG,KAAI,EAAE;EACnC;AAIJ,CAAC,EAHYb,2BAAA,CAAKc,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAElB,MAAM;IAAEqB,IAAI,EAAE,CAACpB,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,2BAAA,CACJ;AACDD,0BAA0B,GAAGN,UAAU,CAAC,CACpCG,SAAS,CAAC;EACNuB,QAAQ,EAAE,0BAA0B;EACpCC,QAAQ,EAAE1B,oBAAoB;EAC9B2B,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC3B,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEI,0BAA0B,CAAC;AAC9B,SAASA,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}