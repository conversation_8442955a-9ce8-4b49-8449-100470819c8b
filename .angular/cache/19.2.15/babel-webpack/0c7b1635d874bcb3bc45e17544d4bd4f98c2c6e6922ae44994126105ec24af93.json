{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatLabel, _MatError, _MatHint, _MatPrefix, _MatSuffix, _MatFormFieldFloatingLabel, _MatFormFieldLineRipple, _MatFormFieldNotchedOutline, _MatFormFieldControl, _MatFormField;\nconst _c0 = [\"notch\"];\nconst _c1 = [\"matFormFieldNotchedOutline\", \"\"];\nconst _c2 = [\"*\"];\nconst _c3 = [\"textField\"];\nconst _c4 = [\"iconPrefixContainer\"];\nconst _c5 = [\"textPrefixContainer\"];\nconst _c6 = [\"iconSuffixContainer\"];\nconst _c7 = [\"textSuffixContainer\"];\nconst _c8 = [\"*\", [[\"mat-label\"]], [[\"\", \"matPrefix\", \"\"], [\"\", \"matIconPrefix\", \"\"]], [[\"\", \"matTextPrefix\", \"\"]], [[\"\", \"matTextSuffix\", \"\"]], [[\"\", \"matSuffix\", \"\"], [\"\", \"matIconSuffix\", \"\"]], [[\"mat-error\"], [\"\", \"matError\", \"\"]], [[\"mat-hint\", 3, \"align\", \"end\"]], [[\"mat-hint\", \"align\", \"end\"]]];\nconst _c9 = [\"*\", \"mat-label\", \"[matPrefix], [matIconPrefix]\", \"[matTextPrefix]\", \"[matTextSuffix]\", \"[matSuffix], [matIconSuffix]\", \"mat-error, [matError]\", \"mat-hint:not([align='end'])\", \"mat-hint[align='end']\"];\nfunction _MatFormField_ng_template_0_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n}\nfunction _MatFormField_ng_template_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 19);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵtemplate(2, _MatFormField_ng_template_0_Conditional_0_Conditional_2_Template, 1, 0, \"span\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"floating\", ctx_r1._shouldLabelFloat())(\"monitorResize\", ctx_r1._hasOutline())(\"id\", ctx_r1._labelId);\n    i0.ɵɵattribute(\"for\", ctx_r1._control.disableAutomaticLabeling ? null : ctx_r1._control.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(!ctx_r1.hideRequiredMarker && ctx_r1._control.required ? 2 : -1);\n  }\n}\nfunction _MatFormField_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, _MatFormField_ng_template_0_Conditional_0_Template, 3, 5, \"label\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1._hasFloatingLabel() ? 0 : -1);\n  }\n}\nfunction _MatFormField_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n}\nfunction _MatFormField_Conditional_6_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction _MatFormField_Conditional_6_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, _MatFormField_Conditional_6_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const labelTemplate_r3 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", labelTemplate_r3);\n  }\n}\nfunction _MatFormField_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, _MatFormField_Conditional_6_Conditional_1_Template, 1, 1, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matFormFieldNotchedOutlineOpen\", ctx_r1._shouldLabelFloat());\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r1._forceDisplayInfixLabel() ? 1 : -1);\n  }\n}\nfunction _MatFormField_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10, 2);\n    i0.ɵɵprojection(2, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _MatFormField_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11, 3);\n    i0.ɵɵprojection(2, 3);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _MatFormField_Conditional_10_ng_template_0_Template(rf, ctx) {}\nfunction _MatFormField_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, _MatFormField_Conditional_10_ng_template_0_Template, 0, 0, \"ng-template\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const labelTemplate_r3 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", labelTemplate_r3);\n  }\n}\nfunction _MatFormField_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14, 4);\n    i0.ɵɵprojection(2, 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _MatFormField_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15, 5);\n    i0.ɵɵprojection(2, 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _MatFormField_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 16);\n  }\n}\nfunction _MatFormField_Case_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 6);\n  }\n}\nfunction _MatFormField_Case_19_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"id\", ctx_r1._hintLabelId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.hintLabel);\n  }\n}\nfunction _MatFormField_Case_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, _MatFormField_Case_19_Conditional_0_Template, 2, 2, \"mat-hint\", 21);\n    i0.ɵɵprojection(1, 7);\n    i0.ɵɵelement(2, \"div\", 22);\n    i0.ɵɵprojection(3, 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1.hintLabel ? 0 : -1);\n  }\n}\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform } from '@angular/cdk/platform';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, inject, Input, ElementRef, NgZone, Renderer2, Component, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, ChangeDetectorRef, Injector, contentChild, ANIMATION_MODULE_TYPE, computed, afterRender, ContentChild, ContentChildren } from '@angular/core';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { Subscription, Subject, merge } from 'rxjs';\nimport { startWith, map, pairwise, filter, takeUntil } from 'rxjs/operators';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\n\n/** The floating label for a `mat-form-field`. */\nclass MatLabel {}\n_MatLabel = MatLabel;\n_defineProperty(MatLabel, \"\\u0275fac\", function _MatLabel_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatLabel)();\n});\n_defineProperty(MatLabel, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatLabel,\n  selectors: [[\"mat-label\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLabel, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-label'\n    }]\n  }], null, null);\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_ERROR = new InjectionToken('MatError');\n/** Single error message to be shown underneath the form-field. */\nclass MatError {\n  constructor() {\n    _defineProperty(this, \"id\", inject(_IdGenerator).getId('mat-mdc-error-'));\n  }\n}\n_MatError = MatError;\n_defineProperty(MatError, \"\\u0275fac\", function _MatError_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatError)();\n});\n_defineProperty(MatError, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatError,\n  selectors: [[\"mat-error\"], [\"\", \"matError\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-form-field-error\", \"mat-mdc-form-field-bottom-align\"],\n  hostVars: 1,\n  hostBindings: function _MatError_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id);\n    }\n  },\n  inputs: {\n    id: \"id\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_ERROR,\n    useExisting: _MatError\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatError, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-error, [matError]',\n      host: {\n        'class': 'mat-mdc-form-field-error mat-mdc-form-field-bottom-align',\n        '[id]': 'id'\n      },\n      providers: [{\n        provide: MAT_ERROR,\n        useExisting: MatError\n      }]\n    }]\n  }], () => [], {\n    id: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Hint text to be shown underneath the form field control. */\nclass MatHint {\n  constructor() {\n    /** Whether to align the hint label at the start or end of the line. */\n    _defineProperty(this, \"align\", 'start');\n    /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n    _defineProperty(this, \"id\", inject(_IdGenerator).getId('mat-mdc-hint-'));\n  }\n}\n_MatHint = MatHint;\n_defineProperty(MatHint, \"\\u0275fac\", function _MatHint_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatHint)();\n});\n_defineProperty(MatHint, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatHint,\n  selectors: [[\"mat-hint\"]],\n  hostAttrs: [1, \"mat-mdc-form-field-hint\", \"mat-mdc-form-field-bottom-align\"],\n  hostVars: 4,\n  hostBindings: function _MatHint_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id);\n      i0.ɵɵattribute(\"align\", null);\n      i0.ɵɵclassProp(\"mat-mdc-form-field-hint-end\", ctx.align === \"end\");\n    }\n  },\n  inputs: {\n    align: \"align\",\n    id: \"id\"\n  }\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHint, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-hint',\n      host: {\n        'class': 'mat-mdc-form-field-hint mat-mdc-form-field-bottom-align',\n        '[class.mat-mdc-form-field-hint-end]': 'align === \"end\"',\n        '[id]': 'id',\n        // Remove align attribute to prevent it from interfering with layout.\n        '[attr.align]': 'null'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_PREFIX = new InjectionToken('MatPrefix');\n/** Prefix to be placed in front of the form field. */\nclass MatPrefix {\n  constructor() {\n    _defineProperty(this, \"_isText\", false);\n  }\n  set _isTextSelector(value) {\n    this._isText = true;\n  }\n}\n_MatPrefix = MatPrefix;\n_defineProperty(MatPrefix, \"\\u0275fac\", function _MatPrefix_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatPrefix)();\n});\n_defineProperty(MatPrefix, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatPrefix,\n  selectors: [[\"\", \"matPrefix\", \"\"], [\"\", \"matIconPrefix\", \"\"], [\"\", \"matTextPrefix\", \"\"]],\n  inputs: {\n    _isTextSelector: [0, \"matTextPrefix\", \"_isTextSelector\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_PREFIX,\n    useExisting: _MatPrefix\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPrefix, [{\n    type: Directive,\n    args: [{\n      selector: '[matPrefix], [matIconPrefix], [matTextPrefix]',\n      providers: [{\n        provide: MAT_PREFIX,\n        useExisting: MatPrefix\n      }]\n    }]\n  }], null, {\n    _isTextSelector: [{\n      type: Input,\n      args: ['matTextPrefix']\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SUFFIX = new InjectionToken('MatSuffix');\n/** Suffix to be placed at the end of the form field. */\nclass MatSuffix {\n  constructor() {\n    _defineProperty(this, \"_isText\", false);\n  }\n  set _isTextSelector(value) {\n    this._isText = true;\n  }\n}\n_MatSuffix = MatSuffix;\n_defineProperty(MatSuffix, \"\\u0275fac\", function _MatSuffix_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSuffix)();\n});\n_defineProperty(MatSuffix, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatSuffix,\n  selectors: [[\"\", \"matSuffix\", \"\"], [\"\", \"matIconSuffix\", \"\"], [\"\", \"matTextSuffix\", \"\"]],\n  inputs: {\n    _isTextSelector: [0, \"matTextSuffix\", \"_isTextSelector\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_SUFFIX,\n    useExisting: _MatSuffix\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSuffix, [{\n    type: Directive,\n    args: [{\n      selector: '[matSuffix], [matIconSuffix], [matTextSuffix]',\n      providers: [{\n        provide: MAT_SUFFIX,\n        useExisting: MatSuffix\n      }]\n    }]\n  }], null, {\n    _isTextSelector: [{\n      type: Input,\n      args: ['matTextSuffix']\n    }]\n  });\n})();\n\n/** An injion token for the parent form-field. */\nconst FLOATING_LABEL_PARENT = new InjectionToken('FloatingLabelParent');\n/**\n * Internal directive that maintains a MDC floating label. This directive does not\n * use the `MDCFloatingLabelFoundation` class, as it is not worth the size cost of\n * including it just to measure the label width and toggle some classes.\n *\n * The use of a directive allows us to conditionally render a floating label in the\n * template without having to manually manage instantiation and destruction of the\n * floating label component based on.\n *\n * The component is responsible for setting up the floating label styles, measuring label\n * width for the outline notch, and providing inputs that can be used to toggle the\n * label's floating or required state.\n */\nclass MatFormFieldFloatingLabel {\n  /** Whether the label is floating. */\n  get floating() {\n    return this._floating;\n  }\n  set floating(value) {\n    this._floating = value;\n    if (this.monitorResize) {\n      this._handleResize();\n    }\n  }\n  /** Whether to monitor for resize events on the floating label. */\n  get monitorResize() {\n    return this._monitorResize;\n  }\n  set monitorResize(value) {\n    this._monitorResize = value;\n    if (this._monitorResize) {\n      this._subscribeToResize();\n    } else {\n      this._resizeSubscription.unsubscribe();\n    }\n  }\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_floating\", false);\n    _defineProperty(this, \"_monitorResize\", false);\n    /** The shared ResizeObserver. */\n    _defineProperty(this, \"_resizeObserver\", inject(SharedResizeObserver));\n    /** The Angular zone. */\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    /** The parent form-field. */\n    _defineProperty(this, \"_parent\", inject(FLOATING_LABEL_PARENT));\n    /** The current resize event subscription. */\n    _defineProperty(this, \"_resizeSubscription\", new Subscription());\n  }\n  ngOnDestroy() {\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Gets the width of the label. Used for the outline notch. */\n  getWidth() {\n    return estimateScrollWidth(this._elementRef.nativeElement);\n  }\n  /** Gets the HTML element for the floating label. */\n  get element() {\n    return this._elementRef.nativeElement;\n  }\n  /** Handles resize events from the ResizeObserver. */\n  _handleResize() {\n    // In the case where the label grows in size, the following sequence of events occurs:\n    // 1. The label grows by 1px triggering the ResizeObserver\n    // 2. The notch is expanded to accommodate the entire label\n    // 3. The label expands to its full width, triggering the ResizeObserver again\n    //\n    // This is expected, but If we allow this to all happen within the same macro task it causes an\n    // error: `ResizeObserver loop limit exceeded`. Therefore we push the notch resize out until\n    // the next macro task.\n    setTimeout(() => this._parent._handleLabelResized());\n  }\n  /** Subscribes to resize events. */\n  _subscribeToResize() {\n    this._resizeSubscription.unsubscribe();\n    this._ngZone.runOutsideAngular(() => {\n      this._resizeSubscription = this._resizeObserver.observe(this._elementRef.nativeElement, {\n        box: 'border-box'\n      }).subscribe(() => this._handleResize());\n    });\n  }\n}\n_MatFormFieldFloatingLabel = MatFormFieldFloatingLabel;\n_defineProperty(MatFormFieldFloatingLabel, \"\\u0275fac\", function _MatFormFieldFloatingLabel_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatFormFieldFloatingLabel)();\n});\n_defineProperty(MatFormFieldFloatingLabel, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatFormFieldFloatingLabel,\n  selectors: [[\"label\", \"matFormFieldFloatingLabel\", \"\"]],\n  hostAttrs: [1, \"mdc-floating-label\", \"mat-mdc-floating-label\"],\n  hostVars: 2,\n  hostBindings: function _MatFormFieldFloatingLabel_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mdc-floating-label--float-above\", ctx.floating);\n    }\n  },\n  inputs: {\n    floating: \"floating\",\n    monitorResize: \"monitorResize\"\n  }\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldFloatingLabel, [{\n    type: Directive,\n    args: [{\n      selector: 'label[matFormFieldFloatingLabel]',\n      host: {\n        'class': 'mdc-floating-label mat-mdc-floating-label',\n        '[class.mdc-floating-label--float-above]': 'floating'\n      }\n    }]\n  }], () => [], {\n    floating: [{\n      type: Input\n    }],\n    monitorResize: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Estimates the scroll width of an element.\n * via https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-dom/ponyfill.ts\n */\nfunction estimateScrollWidth(element) {\n  // Check the offsetParent. If the element inherits display: none from any\n  // parent, the offsetParent property will be null (see\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n  // This check ensures we only clone the node when necessary.\n  const htmlEl = element;\n  if (htmlEl.offsetParent !== null) {\n    return htmlEl.scrollWidth;\n  }\n  const clone = htmlEl.cloneNode(true);\n  clone.style.setProperty('position', 'absolute');\n  clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n  document.documentElement.appendChild(clone);\n  const scrollWidth = clone.scrollWidth;\n  clone.remove();\n  return scrollWidth;\n}\n\n/** Class added when the line ripple is active. */\nconst ACTIVATE_CLASS = 'mdc-line-ripple--active';\n/** Class added when the line ripple is being deactivated. */\nconst DEACTIVATING_CLASS = 'mdc-line-ripple--deactivating';\n/**\n * Internal directive that creates an instance of the MDC line-ripple component. Using a\n * directive allows us to conditionally render a line-ripple in the template without having\n * to manually create and destroy the `MDCLineRipple` component whenever the condition changes.\n *\n * The directive sets up the styles for the line-ripple and provides an API for activating\n * and deactivating the line-ripple.\n */\nclass MatFormFieldLineRipple {\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_cleanupTransitionEnd\", void 0);\n    _defineProperty(this, \"_handleTransitionEnd\", event => {\n      const classList = this._elementRef.nativeElement.classList;\n      const isDeactivating = classList.contains(DEACTIVATING_CLASS);\n      if (event.propertyName === 'opacity' && isDeactivating) {\n        classList.remove(ACTIVATE_CLASS, DEACTIVATING_CLASS);\n      }\n    });\n    const ngZone = inject(NgZone);\n    const renderer = inject(Renderer2);\n    ngZone.runOutsideAngular(() => {\n      this._cleanupTransitionEnd = renderer.listen(this._elementRef.nativeElement, 'transitionend', this._handleTransitionEnd);\n    });\n  }\n  activate() {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove(DEACTIVATING_CLASS);\n    classList.add(ACTIVATE_CLASS);\n  }\n  deactivate() {\n    this._elementRef.nativeElement.classList.add(DEACTIVATING_CLASS);\n  }\n  ngOnDestroy() {\n    this._cleanupTransitionEnd();\n  }\n}\n_MatFormFieldLineRipple = MatFormFieldLineRipple;\n_defineProperty(MatFormFieldLineRipple, \"\\u0275fac\", function _MatFormFieldLineRipple_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatFormFieldLineRipple)();\n});\n_defineProperty(MatFormFieldLineRipple, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatFormFieldLineRipple,\n  selectors: [[\"div\", \"matFormFieldLineRipple\", \"\"]],\n  hostAttrs: [1, \"mdc-line-ripple\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldLineRipple, [{\n    type: Directive,\n    args: [{\n      selector: 'div[matFormFieldLineRipple]',\n      host: {\n        'class': 'mdc-line-ripple'\n      }\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Internal component that creates an instance of the MDC notched-outline component.\n *\n * The component sets up the HTML structure and styles for the notched-outline. It provides\n * inputs to toggle the notch state and width.\n */\nclass MatFormFieldNotchedOutline {\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    /** Whether the notch should be opened. */\n    _defineProperty(this, \"open\", false);\n    _defineProperty(this, \"_notch\", void 0);\n  }\n  ngAfterViewInit() {\n    const label = this._elementRef.nativeElement.querySelector('.mdc-floating-label');\n    if (label) {\n      this._elementRef.nativeElement.classList.add('mdc-notched-outline--upgraded');\n      if (typeof requestAnimationFrame === 'function') {\n        label.style.transitionDuration = '0s';\n        this._ngZone.runOutsideAngular(() => {\n          requestAnimationFrame(() => label.style.transitionDuration = '');\n        });\n      }\n    } else {\n      this._elementRef.nativeElement.classList.add('mdc-notched-outline--no-label');\n    }\n  }\n  _setNotchWidth(labelWidth) {\n    if (!this.open || !labelWidth) {\n      this._notch.nativeElement.style.width = '';\n    } else {\n      const NOTCH_ELEMENT_PADDING = 8;\n      const NOTCH_ELEMENT_BORDER = 1;\n      this._notch.nativeElement.style.width = `calc(${labelWidth}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + ${NOTCH_ELEMENT_PADDING + NOTCH_ELEMENT_BORDER}px)`;\n    }\n  }\n}\n_MatFormFieldNotchedOutline = MatFormFieldNotchedOutline;\n_defineProperty(MatFormFieldNotchedOutline, \"\\u0275fac\", function _MatFormFieldNotchedOutline_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatFormFieldNotchedOutline)();\n});\n_defineProperty(MatFormFieldNotchedOutline, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatFormFieldNotchedOutline,\n  selectors: [[\"div\", \"matFormFieldNotchedOutline\", \"\"]],\n  viewQuery: function _MatFormFieldNotchedOutline_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._notch = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mdc-notched-outline\"],\n  hostVars: 2,\n  hostBindings: function _MatFormFieldNotchedOutline_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mdc-notched-outline--notched\", ctx.open);\n    }\n  },\n  inputs: {\n    open: [0, \"matFormFieldNotchedOutlineOpen\", \"open\"]\n  },\n  attrs: _c1,\n  ngContentSelectors: _c2,\n  decls: 5,\n  vars: 0,\n  consts: [[\"notch\", \"\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__leading\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__notch\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__trailing\"]],\n  template: function _MatFormFieldNotchedOutline_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelement(0, \"div\", 1);\n      i0.ɵɵelementStart(1, \"div\", 2, 0);\n      i0.ɵɵprojection(3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(4, \"div\", 3);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldNotchedOutline, [{\n    type: Component,\n    args: [{\n      selector: 'div[matFormFieldNotchedOutline]',\n      host: {\n        'class': 'mdc-notched-outline',\n        // Besides updating the notch state through the MDC component, we toggle this class through\n        // a host binding in order to ensure that the notched-outline renders correctly on the server.\n        '[class.mdc-notched-outline--notched]': 'open'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__leading\\\"></div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__notch\\\" #notch>\\n  <ng-content></ng-content>\\n</div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__trailing\\\"></div>\\n\"\n    }]\n  }], () => [], {\n    open: [{\n      type: Input,\n      args: ['matFormFieldNotchedOutlineOpen']\n    }],\n    _notch: [{\n      type: ViewChild,\n      args: ['notch']\n    }]\n  });\n})();\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\nclass MatFormFieldControl {\n  constructor() {\n    /** The value of the control. */\n    _defineProperty(this, \"value\", void 0);\n    /**\n     * Stream that emits whenever the state of the control changes such that the parent `MatFormField`\n     * needs to run change detection.\n     */\n    _defineProperty(this, \"stateChanges\", void 0);\n    /** The element ID for this control. */\n    _defineProperty(this, \"id\", void 0);\n    /** The placeholder for this control. */\n    _defineProperty(this, \"placeholder\", void 0);\n    /** Gets the AbstractControlDirective for this control. */\n    _defineProperty(this, \"ngControl\", void 0);\n    /** Whether the control is focused. */\n    _defineProperty(this, \"focused\", void 0);\n    /** Whether the control is empty. */\n    _defineProperty(this, \"empty\", void 0);\n    /** Whether the `MatFormField` label should try to float. */\n    _defineProperty(this, \"shouldLabelFloat\", void 0);\n    /** Whether the control is required. */\n    _defineProperty(this, \"required\", void 0);\n    /** Whether the control is disabled. */\n    _defineProperty(this, \"disabled\", void 0);\n    /** Whether the control is in an error state. */\n    _defineProperty(this, \"errorState\", void 0);\n    /**\n     * An optional name for the control type that can be used to distinguish `mat-form-field` elements\n     * based on their control type. The form field will add a class,\n     * `mat-form-field-type-{{controlType}}` to its root element.\n     */\n    _defineProperty(this, \"controlType\", void 0);\n    /**\n     * Whether the input is currently in an autofilled state. If property is not present on the\n     * control it is assumed to be false.\n     */\n    _defineProperty(this, \"autofilled\", void 0);\n    /**\n     * Value of `aria-describedby` that should be merged with the described-by ids\n     * which are set by the form-field.\n     */\n    _defineProperty(this, \"userAriaDescribedBy\", void 0);\n    /**\n     * Whether to automatically assign the ID of the form field as the `for` attribute\n     * on the `<label>` inside the form field. Set this to true to prevent the form\n     * field from associating the label with non-native elements.\n     */\n    _defineProperty(this, \"disableAutomaticLabeling\", void 0);\n  }\n}\n_MatFormFieldControl = MatFormFieldControl;\n_defineProperty(MatFormFieldControl, \"\\u0275fac\", function _MatFormFieldControl_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatFormFieldControl)();\n});\n_defineProperty(MatFormFieldControl, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatFormFieldControl\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldControl, [{\n    type: Directive\n  }], null, null);\n})();\n\n/** @docs-private */\nfunction getMatFormFieldPlaceholderConflictError() {\n  return Error('Placeholder attribute and child element were both specified.');\n}\n/** @docs-private */\nfunction getMatFormFieldDuplicatedHintError(align) {\n  return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n/** @docs-private */\nfunction getMatFormFieldMissingControlError() {\n  return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nconst MAT_FORM_FIELD = new InjectionToken('MatFormField');\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nconst MAT_FORM_FIELD_DEFAULT_OPTIONS = new InjectionToken('MAT_FORM_FIELD_DEFAULT_OPTIONS');\n/** Default appearance used by the form field. */\nconst DEFAULT_APPEARANCE = 'fill';\n/**\n * Whether the label for form fields should by default float `always`,\n * `never`, or `auto`.\n */\nconst DEFAULT_FLOAT_LABEL = 'auto';\n/** Default way that the subscript element height is set. */\nconst DEFAULT_SUBSCRIPT_SIZING = 'fixed';\n/**\n * Default transform for docked floating labels in a MDC text-field. This value has been\n * extracted from the MDC text-field styles because we programmatically modify the docked\n * label transform, but do not want to accidentally discard the default label transform.\n */\nconst FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM = `translateY(-50%)`;\n/** Container for form controls that applies Material Design styling and behavior. */\nclass MatFormField {\n  /** Whether the required marker should be hidden. */\n  get hideRequiredMarker() {\n    return this._hideRequiredMarker;\n  }\n  set hideRequiredMarker(value) {\n    this._hideRequiredMarker = coerceBooleanProperty(value);\n  }\n  /** Whether the label should always float or float as the user types. */\n  get floatLabel() {\n    var _this$_defaults;\n    return this._floatLabel || ((_this$_defaults = this._defaults) === null || _this$_defaults === void 0 ? void 0 : _this$_defaults.floatLabel) || DEFAULT_FLOAT_LABEL;\n  }\n  set floatLabel(value) {\n    if (value !== this._floatLabel) {\n      this._floatLabel = value;\n      // For backwards compatibility. Custom form field controls or directives might set\n      // the \"floatLabel\" input and expect the form field view to be updated automatically.\n      // e.g. autocomplete trigger. Ideally we'd get rid of this and the consumers would just\n      // emit the \"stateChanges\" observable. TODO(devversion): consider removing.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** The form field appearance style. */\n  get appearance() {\n    return this._appearance;\n  }\n  set appearance(value) {\n    var _this$_defaults2;\n    const oldValue = this._appearance;\n    const newAppearance = value || ((_this$_defaults2 = this._defaults) === null || _this$_defaults2 === void 0 ? void 0 : _this$_defaults2.appearance) || DEFAULT_APPEARANCE;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (newAppearance !== 'fill' && newAppearance !== 'outline') {\n        throw new Error(`MatFormField: Invalid appearance \"${newAppearance}\", valid values are \"fill\" or \"outline\".`);\n      }\n    }\n    this._appearance = newAppearance;\n    if (this._appearance === 'outline' && this._appearance !== oldValue) {\n      // If the appearance has been switched to `outline`, the label offset needs to be updated.\n      // The update can happen once the view has been re-checked, but not immediately because\n      // the view has not been updated and the notched-outline floating label is not present.\n      this._needsOutlineLabelOffsetUpdate = true;\n    }\n  }\n  /**\n   * Whether the form field should reserve space for one line of hint/error text (default)\n   * or to have the spacing grow from 0px as needed based on the size of the hint/error content.\n   * Note that when using dynamic sizing, layout shifts will occur when hint/error text changes.\n   */\n  get subscriptSizing() {\n    var _this$_defaults3;\n    return this._subscriptSizing || ((_this$_defaults3 = this._defaults) === null || _this$_defaults3 === void 0 ? void 0 : _this$_defaults3.subscriptSizing) || DEFAULT_SUBSCRIPT_SIZING;\n  }\n  set subscriptSizing(value) {\n    var _this$_defaults4;\n    this._subscriptSizing = value || ((_this$_defaults4 = this._defaults) === null || _this$_defaults4 === void 0 ? void 0 : _this$_defaults4.subscriptSizing) || DEFAULT_SUBSCRIPT_SIZING;\n  }\n  /** Text for the form field hint. */\n  get hintLabel() {\n    return this._hintLabel;\n  }\n  set hintLabel(value) {\n    this._hintLabel = value;\n    this._processHints();\n  }\n  /** Gets the current form field control */\n  get _control() {\n    return this._explicitFormFieldControl || this._formFieldControl;\n  }\n  set _control(value) {\n    this._explicitFormFieldControl = value;\n  }\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_dir\", inject(Directionality));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_idGenerator\", inject(_IdGenerator));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_defaults\", inject(MAT_FORM_FIELD_DEFAULT_OPTIONS, {\n      optional: true\n    }));\n    _defineProperty(this, \"_textField\", void 0);\n    _defineProperty(this, \"_iconPrefixContainer\", void 0);\n    _defineProperty(this, \"_textPrefixContainer\", void 0);\n    _defineProperty(this, \"_iconSuffixContainer\", void 0);\n    _defineProperty(this, \"_textSuffixContainer\", void 0);\n    _defineProperty(this, \"_floatingLabel\", void 0);\n    _defineProperty(this, \"_notchedOutline\", void 0);\n    _defineProperty(this, \"_lineRipple\", void 0);\n    _defineProperty(this, \"_formFieldControl\", void 0);\n    _defineProperty(this, \"_prefixChildren\", void 0);\n    _defineProperty(this, \"_suffixChildren\", void 0);\n    _defineProperty(this, \"_errorChildren\", void 0);\n    _defineProperty(this, \"_hintChildren\", void 0);\n    _defineProperty(this, \"_labelChild\", contentChild(MatLabel));\n    _defineProperty(this, \"_hideRequiredMarker\", false);\n    /**\n     * Theme color of the form field. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/form-field/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    _defineProperty(this, \"color\", 'primary');\n    _defineProperty(this, \"_floatLabel\", void 0);\n    _defineProperty(this, \"_appearance\", DEFAULT_APPEARANCE);\n    _defineProperty(this, \"_subscriptSizing\", null);\n    _defineProperty(this, \"_hintLabel\", '');\n    _defineProperty(this, \"_hasIconPrefix\", false);\n    _defineProperty(this, \"_hasTextPrefix\", false);\n    _defineProperty(this, \"_hasIconSuffix\", false);\n    _defineProperty(this, \"_hasTextSuffix\", false);\n    // Unique id for the internal form field label.\n    _defineProperty(this, \"_labelId\", this._idGenerator.getId('mat-mdc-form-field-label-'));\n    // Unique id for the hint label.\n    _defineProperty(this, \"_hintLabelId\", this._idGenerator.getId('mat-mdc-hint-'));\n    _defineProperty(this, \"_destroyed\", new Subject());\n    _defineProperty(this, \"_isFocused\", null);\n    _defineProperty(this, \"_explicitFormFieldControl\", void 0);\n    _defineProperty(this, \"_needsOutlineLabelOffsetUpdate\", false);\n    _defineProperty(this, \"_previousControl\", null);\n    _defineProperty(this, \"_previousControlValidatorFn\", null);\n    _defineProperty(this, \"_stateChanges\", void 0);\n    _defineProperty(this, \"_valueChanges\", void 0);\n    _defineProperty(this, \"_describedByChanges\", void 0);\n    _defineProperty(this, \"_animationsDisabled\", void 0);\n    /**\n     * Gets the id of the label element. If no label is present, returns `null`.\n     */\n    _defineProperty(this, \"getLabelId\", computed(() => this._hasFloatingLabel() ? this._labelId : null));\n    _defineProperty(this, \"_hasFloatingLabel\", computed(() => !!this._labelChild()));\n    const defaults = this._defaults;\n    if (defaults) {\n      if (defaults.appearance) {\n        this.appearance = defaults.appearance;\n      }\n      this._hideRequiredMarker = Boolean(defaults === null || defaults === void 0 ? void 0 : defaults.hideRequiredMarker);\n      if (defaults.color) {\n        this.color = defaults.color;\n      }\n    }\n    this._animationsDisabled = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }) === 'NoopAnimations';\n  }\n  ngAfterViewInit() {\n    // Initial focus state sync. This happens rarely, but we want to account for\n    // it in case the form field control has \"focused\" set to true on init.\n    this._updateFocusState();\n    if (!this._animationsDisabled) {\n      this._ngZone.runOutsideAngular(() => {\n        // Enable animations after a certain amount of time so that they don't run on init.\n        setTimeout(() => {\n          this._elementRef.nativeElement.classList.add('mat-form-field-animations-enabled');\n        }, 300);\n      });\n    }\n    // Because the above changes a value used in the template after it was checked, we need\n    // to trigger CD or the change might not be reflected if there is no other CD scheduled.\n    this._changeDetectorRef.detectChanges();\n  }\n  ngAfterContentInit() {\n    this._assertFormFieldControl();\n    this._initializeSubscript();\n    this._initializePrefixAndSuffix();\n    this._initializeOutlineLabelOffsetSubscriptions();\n  }\n  ngAfterContentChecked() {\n    this._assertFormFieldControl();\n    // if form field was being used with an input in first place and then replaced by other\n    // component such as select.\n    if (this._control !== this._previousControl) {\n      this._initializeControl(this._previousControl);\n      // keep a reference for last validator we had.\n      if (this._control.ngControl && this._control.ngControl.control) {\n        this._previousControlValidatorFn = this._control.ngControl.control.validator;\n      }\n      this._previousControl = this._control;\n    }\n    // make sure the the control has been initialized.\n    if (this._control.ngControl && this._control.ngControl.control) {\n      // get the validators for current control.\n      const validatorFn = this._control.ngControl.control.validator;\n      // if our current validatorFn isn't equal to it might be we are CD behind, marking the\n      // component will allow us to catch up.\n      if (validatorFn !== this._previousControlValidatorFn) {\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  ngOnDestroy() {\n    var _this$_stateChanges, _this$_valueChanges, _this$_describedByCha;\n    (_this$_stateChanges = this._stateChanges) === null || _this$_stateChanges === void 0 || _this$_stateChanges.unsubscribe();\n    (_this$_valueChanges = this._valueChanges) === null || _this$_valueChanges === void 0 || _this$_valueChanges.unsubscribe();\n    (_this$_describedByCha = this._describedByChanges) === null || _this$_describedByCha === void 0 || _this$_describedByCha.unsubscribe();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Gets an ElementRef for the element that a overlay attached to the form field\n   * should be positioned relative to.\n   */\n  getConnectedOverlayOrigin() {\n    return this._textField || this._elementRef;\n  }\n  /** Animates the placeholder up and locks it in position. */\n  _animateAndLockLabel() {\n    // This is for backwards compatibility only. Consumers of the form field might use\n    // this method. e.g. the autocomplete trigger. This method has been added to the non-MDC\n    // form field because setting \"floatLabel\" to \"always\" caused the label to float without\n    // animation. This is different in MDC where the label always animates, so this method\n    // is no longer necessary. There doesn't seem any benefit in adding logic to allow changing\n    // the floating label state without animations. The non-MDC implementation was inconsistent\n    // because it always animates if \"floatLabel\" is set away from \"always\".\n    // TODO(devversion): consider removing this method when releasing the MDC form field.\n    if (this._hasFloatingLabel()) {\n      this.floatLabel = 'always';\n    }\n  }\n  /** Initializes the registered form field control. */\n  _initializeControl(previousControl) {\n    var _this$_stateChanges2, _this$_describedByCha2, _this$_valueChanges2;\n    const control = this._control;\n    const classPrefix = 'mat-mdc-form-field-type-';\n    if (previousControl) {\n      this._elementRef.nativeElement.classList.remove(classPrefix + previousControl.controlType);\n    }\n    if (control.controlType) {\n      this._elementRef.nativeElement.classList.add(classPrefix + control.controlType);\n    }\n    // Subscribe to changes in the child control state in order to update the form field UI.\n    (_this$_stateChanges2 = this._stateChanges) === null || _this$_stateChanges2 === void 0 || _this$_stateChanges2.unsubscribe();\n    this._stateChanges = control.stateChanges.subscribe(() => {\n      this._updateFocusState();\n      this._changeDetectorRef.markForCheck();\n    });\n    // Updating the `aria-describedby` touches the DOM. Only do it if it actually needs to change.\n    (_this$_describedByCha2 = this._describedByChanges) === null || _this$_describedByCha2 === void 0 || _this$_describedByCha2.unsubscribe();\n    this._describedByChanges = control.stateChanges.pipe(startWith([undefined, undefined]), map(() => [control.errorState, control.userAriaDescribedBy]), pairwise(), filter(([[prevErrorState, prevDescribedBy], [currentErrorState, currentDescribedBy]]) => {\n      return prevErrorState !== currentErrorState || prevDescribedBy !== currentDescribedBy;\n    })).subscribe(() => this._syncDescribedByIds());\n    (_this$_valueChanges2 = this._valueChanges) === null || _this$_valueChanges2 === void 0 || _this$_valueChanges2.unsubscribe();\n    // Run change detection if the value changes.\n    if (control.ngControl && control.ngControl.valueChanges) {\n      this._valueChanges = control.ngControl.valueChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n  }\n  _checkPrefixAndSuffixTypes() {\n    this._hasIconPrefix = !!this._prefixChildren.find(p => !p._isText);\n    this._hasTextPrefix = !!this._prefixChildren.find(p => p._isText);\n    this._hasIconSuffix = !!this._suffixChildren.find(s => !s._isText);\n    this._hasTextSuffix = !!this._suffixChildren.find(s => s._isText);\n  }\n  /** Initializes the prefix and suffix containers. */\n  _initializePrefixAndSuffix() {\n    this._checkPrefixAndSuffixTypes();\n    // Mark the form field as dirty whenever the prefix or suffix children change. This\n    // is necessary because we conditionally display the prefix/suffix containers based\n    // on whether there is projected content.\n    merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n      this._checkPrefixAndSuffixTypes();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  /**\n   * Initializes the subscript by validating hints and synchronizing \"aria-describedby\" ids\n   * with the custom form field control. Also subscribes to hint and error changes in order\n   * to be able to validate and synchronize ids on change.\n   */\n  _initializeSubscript() {\n    // Re-validate when the number of hints changes.\n    this._hintChildren.changes.subscribe(() => {\n      this._processHints();\n      this._changeDetectorRef.markForCheck();\n    });\n    // Update the aria-described by when the number of errors changes.\n    this._errorChildren.changes.subscribe(() => {\n      this._syncDescribedByIds();\n      this._changeDetectorRef.markForCheck();\n    });\n    // Initial mat-hint validation and subscript describedByIds sync.\n    this._validateHints();\n    this._syncDescribedByIds();\n  }\n  /** Throws an error if the form field's control is missing. */\n  _assertFormFieldControl() {\n    if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatFormFieldMissingControlError();\n    }\n  }\n  _updateFocusState() {\n    var _this$_textField;\n    // Usually the MDC foundation would call \"activateFocus\" and \"deactivateFocus\" whenever\n    // certain DOM events are emitted. This is not possible in our implementation of the\n    // form field because we support abstract form field controls which are not necessarily\n    // of type input, nor do we have a reference to a native form field control element. Instead\n    // we handle the focus by checking if the abstract form field control focused state changes.\n    if (this._control.focused && !this._isFocused) {\n      var _this$_lineRipple;\n      this._isFocused = true;\n      (_this$_lineRipple = this._lineRipple) === null || _this$_lineRipple === void 0 || _this$_lineRipple.activate();\n    } else if (!this._control.focused && (this._isFocused || this._isFocused === null)) {\n      var _this$_lineRipple2;\n      this._isFocused = false;\n      (_this$_lineRipple2 = this._lineRipple) === null || _this$_lineRipple2 === void 0 || _this$_lineRipple2.deactivate();\n    }\n    (_this$_textField = this._textField) === null || _this$_textField === void 0 || _this$_textField.nativeElement.classList.toggle('mdc-text-field--focused', this._control.focused);\n  }\n  /**\n   * The floating label in the docked state needs to account for prefixes. The horizontal offset\n   * is calculated whenever the appearance changes to `outline`, the prefixes change, or when the\n   * form field is added to the DOM. This method sets up all subscriptions which are needed to\n   * trigger the label offset update.\n   */\n  _initializeOutlineLabelOffsetSubscriptions() {\n    // Whenever the prefix changes, schedule an update of the label offset.\n    // TODO(mmalerba): Use ResizeObserver to better support dynamically changing prefix content.\n    this._prefixChildren.changes.subscribe(() => this._needsOutlineLabelOffsetUpdate = true);\n    // TODO(mmalerba): Split this into separate `afterRender` calls using the `EarlyRead` and\n    //  `Write` phases.\n    afterRender(() => {\n      if (this._needsOutlineLabelOffsetUpdate) {\n        this._needsOutlineLabelOffsetUpdate = false;\n        this._updateOutlineLabelOffset();\n      }\n    }, {\n      injector: this._injector\n    });\n    this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._needsOutlineLabelOffsetUpdate = true);\n  }\n  /** Whether the floating label should always float or not. */\n  _shouldAlwaysFloat() {\n    return this.floatLabel === 'always';\n  }\n  _hasOutline() {\n    return this.appearance === 'outline';\n  }\n  /**\n   * Whether the label should display in the infix. Labels in the outline appearance are\n   * displayed as part of the notched-outline and are horizontally offset to account for\n   * form field prefix content. This won't work in server side rendering since we cannot\n   * measure the width of the prefix container. To make the docked label appear as if the\n   * right offset has been calculated, we forcibly render the label inside the infix. Since\n   * the label is part of the infix, the label cannot overflow the prefix content.\n   */\n  _forceDisplayInfixLabel() {\n    return !this._platform.isBrowser && this._prefixChildren.length && !this._shouldLabelFloat();\n  }\n  _shouldLabelFloat() {\n    if (!this._hasFloatingLabel()) {\n      return false;\n    }\n    return this._control.shouldLabelFloat || this._shouldAlwaysFloat();\n  }\n  /**\n   * Determines whether a class from the AbstractControlDirective\n   * should be forwarded to the host element.\n   */\n  _shouldForward(prop) {\n    const control = this._control ? this._control.ngControl : null;\n    return control && control[prop];\n  }\n  /** Gets the type of subscript message to render (error or hint). */\n  _getSubscriptMessageType() {\n    return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState ? 'error' : 'hint';\n  }\n  /** Handle label resize events. */\n  _handleLabelResized() {\n    this._refreshOutlineNotchWidth();\n  }\n  /** Refreshes the width of the outline-notch, if present. */\n  _refreshOutlineNotchWidth() {\n    if (!this._hasOutline() || !this._floatingLabel || !this._shouldLabelFloat()) {\n      var _this$_notchedOutline;\n      (_this$_notchedOutline = this._notchedOutline) === null || _this$_notchedOutline === void 0 || _this$_notchedOutline._setNotchWidth(0);\n    } else {\n      var _this$_notchedOutline2;\n      (_this$_notchedOutline2 = this._notchedOutline) === null || _this$_notchedOutline2 === void 0 || _this$_notchedOutline2._setNotchWidth(this._floatingLabel.getWidth());\n    }\n  }\n  /** Does any extra processing that is required when handling the hints. */\n  _processHints() {\n    this._validateHints();\n    this._syncDescribedByIds();\n  }\n  /**\n   * Ensure that there is a maximum of one of each \"mat-hint\" alignment specified. The hint\n   * label specified set through the input is being considered as \"start\" aligned.\n   *\n   * This method is a noop if Angular runs in production mode.\n   */\n  _validateHints() {\n    if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      let startHint;\n      let endHint;\n      this._hintChildren.forEach(hint => {\n        if (hint.align === 'start') {\n          if (startHint || this.hintLabel) {\n            throw getMatFormFieldDuplicatedHintError('start');\n          }\n          startHint = hint;\n        } else if (hint.align === 'end') {\n          if (endHint) {\n            throw getMatFormFieldDuplicatedHintError('end');\n          }\n          endHint = hint;\n        }\n      });\n    }\n  }\n  /**\n   * Sets the list of element IDs that describe the child control. This allows the control to update\n   * its `aria-describedby` attribute accordingly.\n   */\n  _syncDescribedByIds() {\n    if (this._control) {\n      let ids = [];\n      // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n      if (this._control.userAriaDescribedBy && typeof this._control.userAriaDescribedBy === 'string') {\n        ids.push(...this._control.userAriaDescribedBy.split(' '));\n      }\n      if (this._getSubscriptMessageType() === 'hint') {\n        const startHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'start') : null;\n        const endHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'end') : null;\n        if (startHint) {\n          ids.push(startHint.id);\n        } else if (this._hintLabel) {\n          ids.push(this._hintLabelId);\n        }\n        if (endHint) {\n          ids.push(endHint.id);\n        }\n      } else if (this._errorChildren) {\n        ids.push(...this._errorChildren.map(error => error.id));\n      }\n      this._control.setDescribedByIds(ids);\n    }\n  }\n  /**\n   * Updates the horizontal offset of the label in the outline appearance. In the outline\n   * appearance, the notched-outline and label are not relative to the infix container because\n   * the outline intends to surround prefixes, suffixes and the infix. This means that the\n   * floating label by default overlaps prefixes in the docked state. To avoid this, we need to\n   * horizontally offset the label by the width of the prefix container. The MDC text-field does\n   * not need to do this because they use a fixed width for prefixes. Hence, they can simply\n   * incorporate the horizontal offset into their default text-field styles.\n   */\n  _updateOutlineLabelOffset() {\n    var _this$_iconPrefixCont, _this$_textPrefixCont, _this$_iconSuffixCont, _this$_textSuffixCont, _iconPrefixContainer$, _textPrefixContainer$, _iconSuffixContainer$, _textSuffixContainer$;\n    if (!this._hasOutline() || !this._floatingLabel) {\n      return;\n    }\n    const floatingLabel = this._floatingLabel.element;\n    // If no prefix is displayed, reset the outline label offset from potential\n    // previous label offset updates.\n    if (!(this._iconPrefixContainer || this._textPrefixContainer)) {\n      floatingLabel.style.transform = '';\n      return;\n    }\n    // If the form field is not attached to the DOM yet (e.g. in a tab), we defer\n    // the label offset update until the zone stabilizes.\n    if (!this._isAttachedToDom()) {\n      this._needsOutlineLabelOffsetUpdate = true;\n      return;\n    }\n    const iconPrefixContainer = (_this$_iconPrefixCont = this._iconPrefixContainer) === null || _this$_iconPrefixCont === void 0 ? void 0 : _this$_iconPrefixCont.nativeElement;\n    const textPrefixContainer = (_this$_textPrefixCont = this._textPrefixContainer) === null || _this$_textPrefixCont === void 0 ? void 0 : _this$_textPrefixCont.nativeElement;\n    const iconSuffixContainer = (_this$_iconSuffixCont = this._iconSuffixContainer) === null || _this$_iconSuffixCont === void 0 ? void 0 : _this$_iconSuffixCont.nativeElement;\n    const textSuffixContainer = (_this$_textSuffixCont = this._textSuffixContainer) === null || _this$_textSuffixCont === void 0 ? void 0 : _this$_textSuffixCont.nativeElement;\n    const iconPrefixContainerWidth = (_iconPrefixContainer$ = iconPrefixContainer === null || iconPrefixContainer === void 0 ? void 0 : iconPrefixContainer.getBoundingClientRect().width) !== null && _iconPrefixContainer$ !== void 0 ? _iconPrefixContainer$ : 0;\n    const textPrefixContainerWidth = (_textPrefixContainer$ = textPrefixContainer === null || textPrefixContainer === void 0 ? void 0 : textPrefixContainer.getBoundingClientRect().width) !== null && _textPrefixContainer$ !== void 0 ? _textPrefixContainer$ : 0;\n    const iconSuffixContainerWidth = (_iconSuffixContainer$ = iconSuffixContainer === null || iconSuffixContainer === void 0 ? void 0 : iconSuffixContainer.getBoundingClientRect().width) !== null && _iconSuffixContainer$ !== void 0 ? _iconSuffixContainer$ : 0;\n    const textSuffixContainerWidth = (_textSuffixContainer$ = textSuffixContainer === null || textSuffixContainer === void 0 ? void 0 : textSuffixContainer.getBoundingClientRect().width) !== null && _textSuffixContainer$ !== void 0 ? _textSuffixContainer$ : 0;\n    // If the directionality is RTL, the x-axis transform needs to be inverted. This\n    // is because `transformX` does not change based on the page directionality.\n    const negate = this._dir.value === 'rtl' ? '-1' : '1';\n    const prefixWidth = `${iconPrefixContainerWidth + textPrefixContainerWidth}px`;\n    const labelOffset = `var(--mat-mdc-form-field-label-offset-x, 0px)`;\n    const labelHorizontalOffset = `calc(${negate} * (${prefixWidth} + ${labelOffset}))`;\n    // Update the translateX of the floating label to account for the prefix container,\n    // but allow the CSS to override this setting via a CSS variable when the label is\n    // floating.\n    floatingLabel.style.transform = `var(\n        --mat-mdc-form-field-label-transform,\n        ${FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM} translateX(${labelHorizontalOffset})\n    )`;\n    // Prevent the label from overlapping the suffix when in resting position.\n    const prefixAndSuffixWidth = iconPrefixContainerWidth + textPrefixContainerWidth + iconSuffixContainerWidth + textSuffixContainerWidth;\n    this._elementRef.nativeElement.style.setProperty('--mat-form-field-notch-max-width', `calc(100% - ${prefixAndSuffixWidth}px)`);\n  }\n  /** Checks whether the form field is attached to the DOM. */\n  _isAttachedToDom() {\n    const element = this._elementRef.nativeElement;\n    if (element.getRootNode) {\n      const rootNode = element.getRootNode();\n      // If the element is inside the DOM the root node will be either the document\n      // or the closest shadow root, otherwise it'll be the element itself.\n      return rootNode && rootNode !== element;\n    }\n    // Otherwise fall back to checking if it's in the document. This doesn't account for\n    // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n    return document.documentElement.contains(element);\n  }\n}\n_MatFormField = MatFormField;\n_defineProperty(MatFormField, \"\\u0275fac\", function _MatFormField_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatFormField)();\n});\n_defineProperty(MatFormField, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatFormField,\n  selectors: [[\"mat-form-field\"]],\n  contentQueries: function _MatFormField_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuerySignal(dirIndex, ctx._labelChild, MatLabel, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatFormFieldControl, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_PREFIX, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_SUFFIX, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_ERROR, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatHint, 5);\n    }\n    if (rf & 2) {\n      i0.ɵɵqueryAdvance();\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._formFieldControl = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._prefixChildren = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._suffixChildren = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._errorChildren = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._hintChildren = _t);\n    }\n  },\n  viewQuery: function _MatFormField_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c3, 5);\n      i0.ɵɵviewQuery(_c4, 5);\n      i0.ɵɵviewQuery(_c5, 5);\n      i0.ɵɵviewQuery(_c6, 5);\n      i0.ɵɵviewQuery(_c7, 5);\n      i0.ɵɵviewQuery(MatFormFieldFloatingLabel, 5);\n      i0.ɵɵviewQuery(MatFormFieldNotchedOutline, 5);\n      i0.ɵɵviewQuery(MatFormFieldLineRipple, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textField = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._iconPrefixContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textPrefixContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._iconSuffixContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textSuffixContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._floatingLabel = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._notchedOutline = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lineRipple = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-form-field\"],\n  hostVars: 40,\n  hostBindings: function _MatFormField_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-form-field-label-always-float\", ctx._shouldAlwaysFloat())(\"mat-mdc-form-field-has-icon-prefix\", ctx._hasIconPrefix)(\"mat-mdc-form-field-has-icon-suffix\", ctx._hasIconSuffix)(\"mat-form-field-invalid\", ctx._control.errorState)(\"mat-form-field-disabled\", ctx._control.disabled)(\"mat-form-field-autofilled\", ctx._control.autofilled)(\"mat-form-field-appearance-fill\", ctx.appearance == \"fill\")(\"mat-form-field-appearance-outline\", ctx.appearance == \"outline\")(\"mat-form-field-hide-placeholder\", ctx._hasFloatingLabel() && !ctx._shouldLabelFloat())(\"mat-focused\", ctx._control.focused)(\"mat-primary\", ctx.color !== \"accent\" && ctx.color !== \"warn\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"ng-untouched\", ctx._shouldForward(\"untouched\"))(\"ng-touched\", ctx._shouldForward(\"touched\"))(\"ng-pristine\", ctx._shouldForward(\"pristine\"))(\"ng-dirty\", ctx._shouldForward(\"dirty\"))(\"ng-valid\", ctx._shouldForward(\"valid\"))(\"ng-invalid\", ctx._shouldForward(\"invalid\"))(\"ng-pending\", ctx._shouldForward(\"pending\"));\n    }\n  },\n  inputs: {\n    hideRequiredMarker: \"hideRequiredMarker\",\n    color: \"color\",\n    floatLabel: \"floatLabel\",\n    appearance: \"appearance\",\n    subscriptSizing: \"subscriptSizing\",\n    hintLabel: \"hintLabel\"\n  },\n  exportAs: [\"matFormField\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_FORM_FIELD,\n    useExisting: _MatFormField\n  }, {\n    provide: FLOATING_LABEL_PARENT,\n    useExisting: _MatFormField\n  }])],\n  ngContentSelectors: _c9,\n  decls: 20,\n  vars: 25,\n  consts: [[\"labelTemplate\", \"\"], [\"textField\", \"\"], [\"iconPrefixContainer\", \"\"], [\"textPrefixContainer\", \"\"], [\"textSuffixContainer\", \"\"], [\"iconSuffixContainer\", \"\"], [1, \"mat-mdc-text-field-wrapper\", \"mdc-text-field\", 3, \"click\"], [1, \"mat-mdc-form-field-focus-overlay\"], [1, \"mat-mdc-form-field-flex\"], [\"matFormFieldNotchedOutline\", \"\", 3, \"matFormFieldNotchedOutlineOpen\"], [1, \"mat-mdc-form-field-icon-prefix\"], [1, \"mat-mdc-form-field-text-prefix\"], [1, \"mat-mdc-form-field-infix\"], [3, \"ngTemplateOutlet\"], [1, \"mat-mdc-form-field-text-suffix\"], [1, \"mat-mdc-form-field-icon-suffix\"], [\"matFormFieldLineRipple\", \"\"], [1, \"mat-mdc-form-field-subscript-wrapper\", \"mat-mdc-form-field-bottom-align\"], [\"aria-atomic\", \"true\", \"aria-live\", \"polite\"], [\"matFormFieldFloatingLabel\", \"\", 3, \"floating\", \"monitorResize\", \"id\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-form-field-required-marker\", \"mdc-floating-label--required\"], [3, \"id\"], [1, \"mat-mdc-form-field-hint-spacer\"]],\n  template: function _MatFormField_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵprojectionDef(_c8);\n      i0.ɵɵtemplate(0, _MatFormField_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(2, \"div\", 6, 1);\n      i0.ɵɵlistener(\"click\", function _MatFormField_Template_div_click_2_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._control.onContainerClick($event));\n      });\n      i0.ɵɵtemplate(4, _MatFormField_Conditional_4_Template, 1, 0, \"div\", 7);\n      i0.ɵɵelementStart(5, \"div\", 8);\n      i0.ɵɵtemplate(6, _MatFormField_Conditional_6_Template, 2, 2, \"div\", 9)(7, _MatFormField_Conditional_7_Template, 3, 0, \"div\", 10)(8, _MatFormField_Conditional_8_Template, 3, 0, \"div\", 11);\n      i0.ɵɵelementStart(9, \"div\", 12);\n      i0.ɵɵtemplate(10, _MatFormField_Conditional_10_Template, 1, 1, null, 13);\n      i0.ɵɵprojection(11);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(12, _MatFormField_Conditional_12_Template, 3, 0, \"div\", 14)(13, _MatFormField_Conditional_13_Template, 3, 0, \"div\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(14, _MatFormField_Conditional_14_Template, 1, 0, \"div\", 16);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"div\", 17);\n      i0.ɵɵdeclareLet(16);\n      i0.ɵɵelementStart(17, \"div\", 18);\n      i0.ɵɵtemplate(18, _MatFormField_Case_18_Template, 1, 0)(19, _MatFormField_Case_19_Template, 4, 1);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      let tmp_19_0;\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"mdc-text-field--filled\", !ctx._hasOutline())(\"mdc-text-field--outlined\", ctx._hasOutline())(\"mdc-text-field--no-label\", !ctx._hasFloatingLabel())(\"mdc-text-field--disabled\", ctx._control.disabled)(\"mdc-text-field--invalid\", ctx._control.errorState);\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(!ctx._hasOutline() && !ctx._control.disabled ? 4 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(ctx._hasOutline() ? 6 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx._hasIconPrefix ? 7 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx._hasTextPrefix ? 8 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(!ctx._hasOutline() || ctx._forceDisplayInfixLabel() ? 10 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(ctx._hasTextSuffix ? 12 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx._hasIconSuffix ? 13 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(!ctx._hasOutline() ? 14 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵclassProp(\"mat-mdc-form-field-subscript-dynamic-size\", ctx.subscriptSizing === \"dynamic\");\n      const subscriptMessageType_r4 = ctx._getSubscriptMessageType();\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"mat-mdc-form-field-error-wrapper\", subscriptMessageType_r4 === \"error\")(\"mat-mdc-form-field-hint-wrapper\", subscriptMessageType_r4 === \"hint\");\n      i0.ɵɵadvance();\n      i0.ɵɵconditional((tmp_19_0 = subscriptMessageType_r4) === \"error\" ? 18 : tmp_19_0 === \"hint\" ? 19 : -1);\n    }\n  },\n  dependencies: [MatFormFieldFloatingLabel, MatFormFieldNotchedOutline, NgTemplateOutlet, MatFormFieldLineRipple, MatHint],\n  styles: [\".mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-filled-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-outlined-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-filled-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-filled-text-field-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-filled-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-filled-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-filled-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-filled-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-filled-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-filled-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-outlined-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-outlined-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-outlined-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-outlined-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-outlined-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-outlined-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-outline-color, var(--mat-sys-outline));border-width:var(--mdc-outlined-text-field-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mdc-outlined-text-field-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),100% - max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))*2)}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none;--mat-form-field-notch-max-width: 100%}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mdc-filled-text-field-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormField, [{\n    type: Component,\n    args: [{\n      selector: 'mat-form-field',\n      exportAs: 'matFormField',\n      host: {\n        'class': 'mat-mdc-form-field',\n        '[class.mat-mdc-form-field-label-always-float]': '_shouldAlwaysFloat()',\n        '[class.mat-mdc-form-field-has-icon-prefix]': '_hasIconPrefix',\n        '[class.mat-mdc-form-field-has-icon-suffix]': '_hasIconSuffix',\n        // Note that these classes reuse the same names as the non-MDC version, because they can be\n        // considered a public API since custom form controls may use them to style themselves.\n        // See https://github.com/angular/components/pull/20502#discussion_r486124901.\n        '[class.mat-form-field-invalid]': '_control.errorState',\n        '[class.mat-form-field-disabled]': '_control.disabled',\n        '[class.mat-form-field-autofilled]': '_control.autofilled',\n        '[class.mat-form-field-appearance-fill]': 'appearance == \"fill\"',\n        '[class.mat-form-field-appearance-outline]': 'appearance == \"outline\"',\n        '[class.mat-form-field-hide-placeholder]': '_hasFloatingLabel() && !_shouldLabelFloat()',\n        '[class.mat-focused]': '_control.focused',\n        '[class.mat-primary]': 'color !== \"accent\" && color !== \"warn\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class.ng-untouched]': '_shouldForward(\"untouched\")',\n        '[class.ng-touched]': '_shouldForward(\"touched\")',\n        '[class.ng-pristine]': '_shouldForward(\"pristine\")',\n        '[class.ng-dirty]': '_shouldForward(\"dirty\")',\n        '[class.ng-valid]': '_shouldForward(\"valid\")',\n        '[class.ng-invalid]': '_shouldForward(\"invalid\")',\n        '[class.ng-pending]': '_shouldForward(\"pending\")'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MAT_FORM_FIELD,\n        useExisting: MatFormField\n      }, {\n        provide: FLOATING_LABEL_PARENT,\n        useExisting: MatFormField\n      }],\n      imports: [MatFormFieldFloatingLabel, MatFormFieldNotchedOutline, NgTemplateOutlet, MatFormFieldLineRipple, MatHint],\n      template: \"<ng-template #labelTemplate>\\n  <!--\\n    MDC recommends that the text-field is a `<label>` element. This rather complicates the\\n    setup because it would require every form-field control to explicitly set `aria-labelledby`.\\n    This is because the `<label>` itself contains more than the actual label (e.g. prefix, suffix\\n    or other projected content), and screen readers could potentially read out undesired content.\\n    Excluding elements from being printed out requires them to be marked with `aria-hidden`, or\\n    the form control is set to a scoped element for the label (using `aria-labelledby`). Both of\\n    these options seem to complicate the setup because we know exactly what content is rendered\\n    as part of the label, and we don't want to spend resources on walking through projected content\\n    to set `aria-hidden`. Nor do we want to set `aria-labelledby` on every form control if we could\\n    simply link the label to the control using the label `for` attribute.\\n  -->\\n  @if (_hasFloatingLabel()) {\\n    <label\\n      matFormFieldFloatingLabel\\n      [floating]=\\\"_shouldLabelFloat()\\\"\\n      [monitorResize]=\\\"_hasOutline()\\\"\\n      [id]=\\\"_labelId\\\"\\n      [attr.for]=\\\"_control.disableAutomaticLabeling ? null : _control.id\\\"\\n    >\\n      <ng-content select=\\\"mat-label\\\"></ng-content>\\n      <!--\\n        We set the required marker as a separate element, in order to make it easier to target if\\n        apps want to override it and to be able to set `aria-hidden` so that screen readers don't\\n        pick it up.\\n       -->\\n      @if (!hideRequiredMarker && _control.required) {\\n        <span\\n          aria-hidden=\\\"true\\\"\\n          class=\\\"mat-mdc-form-field-required-marker mdc-floating-label--required\\\"\\n        ></span>\\n      }\\n    </label>\\n  }\\n</ng-template>\\n\\n<div\\n  class=\\\"mat-mdc-text-field-wrapper mdc-text-field\\\"\\n  #textField\\n  [class.mdc-text-field--filled]=\\\"!_hasOutline()\\\"\\n  [class.mdc-text-field--outlined]=\\\"_hasOutline()\\\"\\n  [class.mdc-text-field--no-label]=\\\"!_hasFloatingLabel()\\\"\\n  [class.mdc-text-field--disabled]=\\\"_control.disabled\\\"\\n  [class.mdc-text-field--invalid]=\\\"_control.errorState\\\"\\n  (click)=\\\"_control.onContainerClick($event)\\\"\\n>\\n  @if (!_hasOutline() && !_control.disabled) {\\n    <div class=\\\"mat-mdc-form-field-focus-overlay\\\"></div>\\n  }\\n  <div class=\\\"mat-mdc-form-field-flex\\\">\\n    @if (_hasOutline()) {\\n      <div matFormFieldNotchedOutline [matFormFieldNotchedOutlineOpen]=\\\"_shouldLabelFloat()\\\">\\n        @if (!_forceDisplayInfixLabel()) {\\n          <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n        }\\n      </div>\\n    }\\n\\n    @if (_hasIconPrefix) {\\n      <div class=\\\"mat-mdc-form-field-icon-prefix\\\" #iconPrefixContainer>\\n        <ng-content select=\\\"[matPrefix], [matIconPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasTextPrefix) {\\n      <div class=\\\"mat-mdc-form-field-text-prefix\\\" #textPrefixContainer>\\n        <ng-content select=\\\"[matTextPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-form-field-infix\\\">\\n      @if (!_hasOutline() || _forceDisplayInfixLabel()) {\\n        <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n      }\\n\\n      <ng-content></ng-content>\\n    </div>\\n\\n    @if (_hasTextSuffix) {\\n      <div class=\\\"mat-mdc-form-field-text-suffix\\\" #textSuffixContainer>\\n        <ng-content select=\\\"[matTextSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasIconSuffix) {\\n      <div class=\\\"mat-mdc-form-field-icon-suffix\\\" #iconSuffixContainer>\\n        <ng-content select=\\\"[matSuffix], [matIconSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n  </div>\\n\\n  @if (!_hasOutline()) {\\n    <div matFormFieldLineRipple></div>\\n  }\\n</div>\\n\\n<div\\n    class=\\\"mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align\\\"\\n    [class.mat-mdc-form-field-subscript-dynamic-size]=\\\"subscriptSizing === 'dynamic'\\\"\\n>\\n  @let subscriptMessageType = _getSubscriptMessageType();\\n\\n  <!-- \\n    Use a single permanent wrapper for both hints and errors so aria-live works correctly,\\n    as having it appear post render will not consistently work. We also do not want to add\\n    additional divs as it causes styling regressions.\\n    -->\\n  <div aria-atomic=\\\"true\\\" aria-live=\\\"polite\\\" \\n      [class.mat-mdc-form-field-error-wrapper]=\\\"subscriptMessageType === 'error'\\\"\\n      [class.mat-mdc-form-field-hint-wrapper]=\\\"subscriptMessageType === 'hint'\\\"\\n    >\\n    @switch (subscriptMessageType) {\\n      @case ('error') {\\n        <ng-content select=\\\"mat-error, [matError]\\\"></ng-content>\\n      }\\n\\n      @case ('hint') {\\n        @if (hintLabel) {\\n          <mat-hint [id]=\\\"_hintLabelId\\\">{{hintLabel}}</mat-hint>\\n        }\\n        <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n        <div class=\\\"mat-mdc-form-field-hint-spacer\\\"></div>\\n        <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n      }\\n    }\\n  </div>\\n</div>\\n\",\n      styles: [\".mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-filled-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-outlined-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-filled-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-filled-text-field-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-filled-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-filled-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-filled-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-filled-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-filled-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-filled-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-outlined-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-outlined-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-outlined-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-outlined-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-outlined-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-outlined-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-outline-color, var(--mat-sys-outline));border-width:var(--mdc-outlined-text-field-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mdc-outlined-text-field-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),100% - max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))*2)}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none;--mat-form-field-notch-max-width: 100%}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mdc-filled-text-field-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\\n\"]\n    }]\n  }], () => [], {\n    _textField: [{\n      type: ViewChild,\n      args: ['textField']\n    }],\n    _iconPrefixContainer: [{\n      type: ViewChild,\n      args: ['iconPrefixContainer']\n    }],\n    _textPrefixContainer: [{\n      type: ViewChild,\n      args: ['textPrefixContainer']\n    }],\n    _iconSuffixContainer: [{\n      type: ViewChild,\n      args: ['iconSuffixContainer']\n    }],\n    _textSuffixContainer: [{\n      type: ViewChild,\n      args: ['textSuffixContainer']\n    }],\n    _floatingLabel: [{\n      type: ViewChild,\n      args: [MatFormFieldFloatingLabel]\n    }],\n    _notchedOutline: [{\n      type: ViewChild,\n      args: [MatFormFieldNotchedOutline]\n    }],\n    _lineRipple: [{\n      type: ViewChild,\n      args: [MatFormFieldLineRipple]\n    }],\n    _formFieldControl: [{\n      type: ContentChild,\n      args: [MatFormFieldControl]\n    }],\n    _prefixChildren: [{\n      type: ContentChildren,\n      args: [MAT_PREFIX, {\n        descendants: true\n      }]\n    }],\n    _suffixChildren: [{\n      type: ContentChildren,\n      args: [MAT_SUFFIX, {\n        descendants: true\n      }]\n    }],\n    _errorChildren: [{\n      type: ContentChildren,\n      args: [MAT_ERROR, {\n        descendants: true\n      }]\n    }],\n    _hintChildren: [{\n      type: ContentChildren,\n      args: [MatHint, {\n        descendants: true\n      }]\n    }],\n    hideRequiredMarker: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    floatLabel: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }],\n    subscriptSizing: [{\n      type: Input\n    }],\n    hintLabel: [{\n      type: Input\n    }]\n  });\n})();\nexport { MatLabel as M, MAT_ERROR as a, MatError as b, MatHint as c, MAT_PREFIX as d, MatPrefix as e, MAT_SUFFIX as f, MatSuffix as g, MAT_FORM_FIELD as h, MAT_FORM_FIELD_DEFAULT_OPTIONS as i, MatFormField as j, MatFormFieldControl as k, getMatFormFieldPlaceholderConflictError as l, getMatFormFieldDuplicatedHintError as m, getMatFormFieldMissingControlError as n };\n//# sourceMappingURL=form-field-DqPi4knt.mjs.map", "map": {"version": 3, "names": ["i0", "ɵɵelement", "_MatFormField_ng_template_0_Conditional_0_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵprojection", "ɵɵtemplate", "_MatFormField_ng_template_0_Conditional_0_Conditional_2_Template", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "_shouldLabelFloat", "_hasOutline", "_labelId", "ɵɵattribute", "_control", "disableAutomaticLabeling", "id", "ɵɵadvance", "ɵɵconditional", "hideRequiredMarker", "required", "_MatFormField_ng_template_0_Template", "_hasFloatingLabel", "_MatFormField_Conditional_4_Template", "_MatFormField_Conditional_6_Conditional_1_ng_template_0_Template", "_MatFormField_Conditional_6_Conditional_1_Template", "labelTemplate_r3", "ɵɵreference", "_MatFormField_Conditional_6_Template", "_forceDisplayInfixLabel", "_MatFormField_Conditional_7_Template", "_MatFormField_Conditional_8_Template", "_MatFormField_Conditional_10_ng_template_0_Template", "_MatFormField_Conditional_10_Template", "_MatFormField_Conditional_12_Template", "_MatFormField_Conditional_13_Template", "_MatFormField_Conditional_14_Template", "_Mat<PERSON><PERSON><PERSON><PERSON>_Case_18_Template", "_MatFormField_Case_19_Conditional_0_Template", "ɵɵtext", "_hintLabelId", "ɵɵtextInterpolate", "hintLabel", "_Mat<PERSON><PERSON><PERSON><PERSON>_Case_19_Template", "Directionality", "coerceBooleanProperty", "Platform", "NgTemplateOutlet", "Directive", "InjectionToken", "inject", "Input", "ElementRef", "NgZone", "Renderer2", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "ViewChild", "ChangeDetectorRef", "Injector", "contentChild", "ANIMATION_MODULE_TYPE", "computed", "afterRender", "ContentChild", "ContentChildren", "_IdGenerator", "Subscription", "Subject", "merge", "startWith", "map", "pairwise", "filter", "takeUntil", "SharedResizeObserver", "<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "_MatLabel_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "MAT_ERROR", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "getId", "_MatE<PERSON>r", "_MatError_Factory", "hostAttrs", "hostVars", "hostBindings", "_MatError_HostBindings", "ɵɵhostProperty", "inputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "host", "providers", "MatHint", "_MatHint", "_MatHint_Factory", "_MatHint_HostBindings", "ɵɵclassProp", "align", "MAT_PREFIX", "MatPrefix", "_isTextSelector", "value", "_isText", "_MatPrefix", "_MatPrefix_Factory", "MAT_SUFFIX", "MatSuffix", "_MatSuffix", "_MatSuffix_Factory", "FLOATING_LABEL_PARENT", "MatFormFieldFloatingLabel", "floating", "_floating", "monitorResize", "_handleResize", "_monitorResize", "_subscribeToResize", "_resizeSubscription", "unsubscribe", "ngOnDestroy", "getWidth", "estimateScrollWidth", "_elementRef", "nativeElement", "element", "setTimeout", "_parent", "_handleLabelResized", "_ngZone", "runOutsideAngular", "_resizeObserver", "observe", "box", "subscribe", "_MatFormFieldFloatingLabel", "_MatFormFieldFloatingLabel_Factory", "_MatFormFieldFloatingLabel_HostBindings", "htmlEl", "offsetParent", "scrollWidth", "clone", "cloneNode", "style", "setProperty", "document", "documentElement", "append<PERSON><PERSON><PERSON>", "remove", "ACTIVATE_CLASS", "DEACTIVATING_CLASS", "MatFormFieldLineRipple", "event", "classList", "isDeactivating", "contains", "propertyName", "ngZone", "renderer", "_cleanupTransitionEnd", "listen", "_handleTransitionEnd", "activate", "add", "deactivate", "_MatFormFieldLineRipple", "_MatFormFieldLineRipple_Factory", "MatFormFieldNotchedOutline", "ngAfterViewInit", "label", "querySelector", "requestAnimationFrame", "transitionDuration", "_setNotchWidth", "labelWidth", "open", "_notch", "width", "NOTCH_ELEMENT_PADDING", "NOTCH_ELEMENT_BORDER", "_MatFormFieldNotchedOutline", "_MatFormFieldNotchedOutline_Factory", "ɵɵdefineComponent", "viewQuery", "_MatFormFieldNotchedOutline_Query", "ɵɵviewQuery", "_c0", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "_MatFormFieldNotchedOutline_HostBindings", "attrs", "_c1", "ngContentSelectors", "_c2", "decls", "vars", "consts", "template", "_MatFormFieldNotchedOutline_Template", "ɵɵprojectionDef", "encapsulation", "changeDetection", "OnPush", "None", "MatFormFieldControl", "_MatFormFieldControl", "_MatFormFieldControl_Factory", "getMatFormFieldPlaceholderConflictError", "Error", "getMatFormFieldDuplicatedHintError", "getMatFormFieldMissingControlError", "MAT_FORM_FIELD", "MAT_FORM_FIELD_DEFAULT_OPTIONS", "DEFAULT_APPEARANCE", "DEFAULT_FLOAT_LABEL", "DEFAULT_SUBSCRIPT_SIZING", "FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM", "MatFormField", "_hideRequiredMarker", "floatLabel", "_this$_defaults", "_floatLabel", "_defaults", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appearance", "_appearance", "_this$_defaults2", "oldValue", "newAppearance", "_needsOutlineLabelOffsetUpdate", "subscriptSizing", "_this$_defaults3", "_subscriptSizing", "_this$_defaults4", "_<PERSON><PERSON><PERSON>l", "_processHints", "_explicitFormFieldControl", "_formFieldControl", "optional", "_idGenerator", "_labelChild", "defaults", "Boolean", "color", "_animationsDisabled", "_updateFocusState", "detectChanges", "ngAfterContentInit", "_assertFormFieldControl", "_initializeSubscript", "_initializePrefixAndSuffix", "_initializeOutlineLabelOffsetSubscriptions", "ngAfterContentChecked", "_previousControl", "_initializeControl", "ngControl", "control", "_previousControlValidatorFn", "validator", "validatorFn", "_this$_stateChanges", "_this$_valueChanges", "_this$_describedByCha", "_stateChanges", "_valueChanges", "_describedByChanges", "_destroyed", "next", "complete", "getConnectedOverlayOrigin", "_textField", "_animateAndLockLabel", "previousControl", "_this$_stateChanges2", "_this$_describedByCha2", "_this$_valueChanges2", "classPrefix", "controlType", "stateChanges", "pipe", "undefined", "errorState", "userAriaDescribedBy", "prevErrorState", "prevDescribedBy", "currentErrorState", "currentDescribedBy", "_syncDescribedByIds", "valueChanges", "_checkPrefixAndSuffixTypes", "_hasIconPrefix", "_prefixChildren", "find", "p", "_hasTextPrefix", "_hasIconSuffix", "_suffixC<PERSON><PERSON>n", "s", "_hasTextSuffix", "changes", "_hint<PERSON><PERSON><PERSON>n", "_errorC<PERSON><PERSON>n", "_validateHints", "_this$_textField", "focused", "_isFocused", "_this$_lineRipple", "_lineRipple", "_this$_lineRipple2", "toggle", "_updateOutlineLabelOffset", "injector", "_injector", "_dir", "change", "_shouldAlwaysFloat", "_platform", "<PERSON><PERSON><PERSON><PERSON>", "length", "shouldLabelFloat", "_shouldForward", "prop", "_getSubscriptMessageType", "_refreshOutlineNotchWidth", "_floating<PERSON>abel", "_this$_notchedOutline", "_notchedOutline", "_this$_notchedOutline2", "startHint", "endHint", "for<PERSON>ach", "hint", "ids", "push", "split", "error", "setDescribedByIds", "_this$_iconPrefixCont", "_this$_textPrefixCont", "_this$_iconSuffixCont", "_this$_textSuffixCont", "_iconPrefixContainer$", "_textPrefixContainer$", "_iconSuffixContainer$", "_textSuffixContainer$", "floatingLabel", "_iconPrefixContainer", "_textPrefixContainer", "transform", "_isAttachedToDom", "iconPrefixContainer", "textPrefixContainer", "iconSuffixContainer", "_iconSuffixContainer", "textSuffixContainer", "_textSuffixContainer", "iconPrefixContainer<PERSON>idth", "getBoundingClientRect", "textPrefixContainer<PERSON><PERSON><PERSON>", "iconSuffixContainerWidth", "textSuffixContainer<PERSON>idth", "negate", "prefixWidth", "labelOffset", "labelHorizontalOffset", "prefixAndSuffixWidth", "getRootNode", "rootNode", "_MatFormField", "_MatFormField_Factory", "contentQueries", "_MatFormField_ContentQueries", "dirIndex", "ɵɵcontentQuerySignal", "ɵɵcontentQuery", "ɵɵqueryAdvance", "_MatFormField_Query", "_c3", "_c4", "_c5", "_c6", "_c7", "_MatFormField_HostB<PERSON>ings", "disabled", "autofilled", "exportAs", "_c9", "_MatForm<PERSON>ield_Template", "_r1", "ɵɵgetCurrentView", "_c8", "ɵɵtemplateRefExtractor", "ɵɵlistener", "_MatF<PERSON>Field_Template_div_click_2_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "onContainerClick", "ɵɵdeclareLet", "tmp_19_0", "subscriptMessageType_r4", "dependencies", "styles", "imports", "descendants", "M", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/form-field-DqPi4knt.mjs"], "sourcesContent": ["import { Directionality } from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform } from '@angular/cdk/platform';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, inject, Input, ElementRef, NgZone, Renderer2, Component, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, ChangeDetectorRef, Injector, contentChild, ANIMATION_MODULE_TYPE, computed, afterRender, ContentChild, ContentChildren } from '@angular/core';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { Subscription, Subject, merge } from 'rxjs';\nimport { startWith, map, pairwise, filter, takeUntil } from 'rxjs/operators';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\n\n/** The floating label for a `mat-form-field`. */\nclass MatLabel {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatLabel, isStandalone: true, selector: \"mat-label\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-label',\n                }]\n        }] });\n\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_ERROR = new InjectionToken('MatError');\n/** Single error message to be shown underneath the form-field. */\nclass MatError {\n    id = inject(_IdGenerator).getId('mat-mdc-error-');\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatError, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatError, isStandalone: true, selector: \"mat-error, [matError]\", inputs: { id: \"id\" }, host: { properties: { \"id\": \"id\" }, classAttribute: \"mat-mdc-form-field-error mat-mdc-form-field-bottom-align\" }, providers: [{ provide: MAT_ERROR, useExisting: MatError }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatError, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-error, [matError]',\n                    host: {\n                        'class': 'mat-mdc-form-field-error mat-mdc-form-field-bottom-align',\n                        '[id]': 'id',\n                    },\n                    providers: [{ provide: MAT_ERROR, useExisting: MatError }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { id: [{\n                type: Input\n            }] } });\n\n/** Hint text to be shown underneath the form field control. */\nclass MatHint {\n    /** Whether to align the hint label at the start or end of the line. */\n    align = 'start';\n    /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n    id = inject(_IdGenerator).getId('mat-mdc-hint-');\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatHint, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatHint, isStandalone: true, selector: \"mat-hint\", inputs: { align: \"align\", id: \"id\" }, host: { properties: { \"class.mat-mdc-form-field-hint-end\": \"align === \\\"end\\\"\", \"id\": \"id\", \"attr.align\": \"null\" }, classAttribute: \"mat-mdc-form-field-hint mat-mdc-form-field-bottom-align\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatHint, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-hint',\n                    host: {\n                        'class': 'mat-mdc-form-field-hint mat-mdc-form-field-bottom-align',\n                        '[class.mat-mdc-form-field-hint-end]': 'align === \"end\"',\n                        '[id]': 'id',\n                        // Remove align attribute to prevent it from interfering with layout.\n                        '[attr.align]': 'null',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_PREFIX = new InjectionToken('MatPrefix');\n/** Prefix to be placed in front of the form field. */\nclass MatPrefix {\n    set _isTextSelector(value) {\n        this._isText = true;\n    }\n    _isText = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPrefix, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatPrefix, isStandalone: true, selector: \"[matPrefix], [matIconPrefix], [matTextPrefix]\", inputs: { _isTextSelector: [\"matTextPrefix\", \"_isTextSelector\"] }, providers: [{ provide: MAT_PREFIX, useExisting: MatPrefix }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPrefix, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matPrefix], [matIconPrefix], [matTextPrefix]',\n                    providers: [{ provide: MAT_PREFIX, useExisting: MatPrefix }],\n                }]\n        }], propDecorators: { _isTextSelector: [{\n                type: Input,\n                args: ['matTextPrefix']\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SUFFIX = new InjectionToken('MatSuffix');\n/** Suffix to be placed at the end of the form field. */\nclass MatSuffix {\n    set _isTextSelector(value) {\n        this._isText = true;\n    }\n    _isText = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSuffix, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatSuffix, isStandalone: true, selector: \"[matSuffix], [matIconSuffix], [matTextSuffix]\", inputs: { _isTextSelector: [\"matTextSuffix\", \"_isTextSelector\"] }, providers: [{ provide: MAT_SUFFIX, useExisting: MatSuffix }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSuffix, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matSuffix], [matIconSuffix], [matTextSuffix]',\n                    providers: [{ provide: MAT_SUFFIX, useExisting: MatSuffix }],\n                }]\n        }], propDecorators: { _isTextSelector: [{\n                type: Input,\n                args: ['matTextSuffix']\n            }] } });\n\n/** An injion token for the parent form-field. */\nconst FLOATING_LABEL_PARENT = new InjectionToken('FloatingLabelParent');\n/**\n * Internal directive that maintains a MDC floating label. This directive does not\n * use the `MDCFloatingLabelFoundation` class, as it is not worth the size cost of\n * including it just to measure the label width and toggle some classes.\n *\n * The use of a directive allows us to conditionally render a floating label in the\n * template without having to manually manage instantiation and destruction of the\n * floating label component based on.\n *\n * The component is responsible for setting up the floating label styles, measuring label\n * width for the outline notch, and providing inputs that can be used to toggle the\n * label's floating or required state.\n */\nclass MatFormFieldFloatingLabel {\n    _elementRef = inject(ElementRef);\n    /** Whether the label is floating. */\n    get floating() {\n        return this._floating;\n    }\n    set floating(value) {\n        this._floating = value;\n        if (this.monitorResize) {\n            this._handleResize();\n        }\n    }\n    _floating = false;\n    /** Whether to monitor for resize events on the floating label. */\n    get monitorResize() {\n        return this._monitorResize;\n    }\n    set monitorResize(value) {\n        this._monitorResize = value;\n        if (this._monitorResize) {\n            this._subscribeToResize();\n        }\n        else {\n            this._resizeSubscription.unsubscribe();\n        }\n    }\n    _monitorResize = false;\n    /** The shared ResizeObserver. */\n    _resizeObserver = inject(SharedResizeObserver);\n    /** The Angular zone. */\n    _ngZone = inject(NgZone);\n    /** The parent form-field. */\n    _parent = inject(FLOATING_LABEL_PARENT);\n    /** The current resize event subscription. */\n    _resizeSubscription = new Subscription();\n    constructor() { }\n    ngOnDestroy() {\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Gets the width of the label. Used for the outline notch. */\n    getWidth() {\n        return estimateScrollWidth(this._elementRef.nativeElement);\n    }\n    /** Gets the HTML element for the floating label. */\n    get element() {\n        return this._elementRef.nativeElement;\n    }\n    /** Handles resize events from the ResizeObserver. */\n    _handleResize() {\n        // In the case where the label grows in size, the following sequence of events occurs:\n        // 1. The label grows by 1px triggering the ResizeObserver\n        // 2. The notch is expanded to accommodate the entire label\n        // 3. The label expands to its full width, triggering the ResizeObserver again\n        //\n        // This is expected, but If we allow this to all happen within the same macro task it causes an\n        // error: `ResizeObserver loop limit exceeded`. Therefore we push the notch resize out until\n        // the next macro task.\n        setTimeout(() => this._parent._handleLabelResized());\n    }\n    /** Subscribes to resize events. */\n    _subscribeToResize() {\n        this._resizeSubscription.unsubscribe();\n        this._ngZone.runOutsideAngular(() => {\n            this._resizeSubscription = this._resizeObserver\n                .observe(this._elementRef.nativeElement, { box: 'border-box' })\n                .subscribe(() => this._handleResize());\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldFloatingLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatFormFieldFloatingLabel, isStandalone: true, selector: \"label[matFormFieldFloatingLabel]\", inputs: { floating: \"floating\", monitorResize: \"monitorResize\" }, host: { properties: { \"class.mdc-floating-label--float-above\": \"floating\" }, classAttribute: \"mdc-floating-label mat-mdc-floating-label\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldFloatingLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'label[matFormFieldFloatingLabel]',\n                    host: {\n                        'class': 'mdc-floating-label mat-mdc-floating-label',\n                        '[class.mdc-floating-label--float-above]': 'floating',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { floating: [{\n                type: Input\n            }], monitorResize: [{\n                type: Input\n            }] } });\n/**\n * Estimates the scroll width of an element.\n * via https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-dom/ponyfill.ts\n */\nfunction estimateScrollWidth(element) {\n    // Check the offsetParent. If the element inherits display: none from any\n    // parent, the offsetParent property will be null (see\n    // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n    // This check ensures we only clone the node when necessary.\n    const htmlEl = element;\n    if (htmlEl.offsetParent !== null) {\n        return htmlEl.scrollWidth;\n    }\n    const clone = htmlEl.cloneNode(true);\n    clone.style.setProperty('position', 'absolute');\n    clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n    document.documentElement.appendChild(clone);\n    const scrollWidth = clone.scrollWidth;\n    clone.remove();\n    return scrollWidth;\n}\n\n/** Class added when the line ripple is active. */\nconst ACTIVATE_CLASS = 'mdc-line-ripple--active';\n/** Class added when the line ripple is being deactivated. */\nconst DEACTIVATING_CLASS = 'mdc-line-ripple--deactivating';\n/**\n * Internal directive that creates an instance of the MDC line-ripple component. Using a\n * directive allows us to conditionally render a line-ripple in the template without having\n * to manually create and destroy the `MDCLineRipple` component whenever the condition changes.\n *\n * The directive sets up the styles for the line-ripple and provides an API for activating\n * and deactivating the line-ripple.\n */\nclass MatFormFieldLineRipple {\n    _elementRef = inject(ElementRef);\n    _cleanupTransitionEnd;\n    constructor() {\n        const ngZone = inject(NgZone);\n        const renderer = inject(Renderer2);\n        ngZone.runOutsideAngular(() => {\n            this._cleanupTransitionEnd = renderer.listen(this._elementRef.nativeElement, 'transitionend', this._handleTransitionEnd);\n        });\n    }\n    activate() {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove(DEACTIVATING_CLASS);\n        classList.add(ACTIVATE_CLASS);\n    }\n    deactivate() {\n        this._elementRef.nativeElement.classList.add(DEACTIVATING_CLASS);\n    }\n    _handleTransitionEnd = (event) => {\n        const classList = this._elementRef.nativeElement.classList;\n        const isDeactivating = classList.contains(DEACTIVATING_CLASS);\n        if (event.propertyName === 'opacity' && isDeactivating) {\n            classList.remove(ACTIVATE_CLASS, DEACTIVATING_CLASS);\n        }\n    };\n    ngOnDestroy() {\n        this._cleanupTransitionEnd();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldLineRipple, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatFormFieldLineRipple, isStandalone: true, selector: \"div[matFormFieldLineRipple]\", host: { classAttribute: \"mdc-line-ripple\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldLineRipple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'div[matFormFieldLineRipple]',\n                    host: {\n                        'class': 'mdc-line-ripple',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Internal component that creates an instance of the MDC notched-outline component.\n *\n * The component sets up the HTML structure and styles for the notched-outline. It provides\n * inputs to toggle the notch state and width.\n */\nclass MatFormFieldNotchedOutline {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    /** Whether the notch should be opened. */\n    open = false;\n    _notch;\n    constructor() { }\n    ngAfterViewInit() {\n        const label = this._elementRef.nativeElement.querySelector('.mdc-floating-label');\n        if (label) {\n            this._elementRef.nativeElement.classList.add('mdc-notched-outline--upgraded');\n            if (typeof requestAnimationFrame === 'function') {\n                label.style.transitionDuration = '0s';\n                this._ngZone.runOutsideAngular(() => {\n                    requestAnimationFrame(() => (label.style.transitionDuration = ''));\n                });\n            }\n        }\n        else {\n            this._elementRef.nativeElement.classList.add('mdc-notched-outline--no-label');\n        }\n    }\n    _setNotchWidth(labelWidth) {\n        if (!this.open || !labelWidth) {\n            this._notch.nativeElement.style.width = '';\n        }\n        else {\n            const NOTCH_ELEMENT_PADDING = 8;\n            const NOTCH_ELEMENT_BORDER = 1;\n            this._notch.nativeElement.style.width = `calc(${labelWidth}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + ${NOTCH_ELEMENT_PADDING + NOTCH_ELEMENT_BORDER}px)`;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldNotchedOutline, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatFormFieldNotchedOutline, isStandalone: true, selector: \"div[matFormFieldNotchedOutline]\", inputs: { open: [\"matFormFieldNotchedOutlineOpen\", \"open\"] }, host: { properties: { \"class.mdc-notched-outline--notched\": \"open\" }, classAttribute: \"mdc-notched-outline\" }, viewQueries: [{ propertyName: \"_notch\", first: true, predicate: [\"notch\"], descendants: true }], ngImport: i0, template: \"<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__leading\\\"></div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__notch\\\" #notch>\\n  <ng-content></ng-content>\\n</div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__trailing\\\"></div>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldNotchedOutline, decorators: [{\n            type: Component,\n            args: [{ selector: 'div[matFormFieldNotchedOutline]', host: {\n                        'class': 'mdc-notched-outline',\n                        // Besides updating the notch state through the MDC component, we toggle this class through\n                        // a host binding in order to ensure that the notched-outline renders correctly on the server.\n                        '[class.mdc-notched-outline--notched]': 'open',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__leading\\\"></div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__notch\\\" #notch>\\n  <ng-content></ng-content>\\n</div>\\n<div class=\\\"mat-mdc-notch-piece mdc-notched-outline__trailing\\\"></div>\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { open: [{\n                type: Input,\n                args: ['matFormFieldNotchedOutlineOpen']\n            }], _notch: [{\n                type: ViewChild,\n                args: ['notch']\n            }] } });\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\nclass MatFormFieldControl {\n    /** The value of the control. */\n    value;\n    /**\n     * Stream that emits whenever the state of the control changes such that the parent `MatFormField`\n     * needs to run change detection.\n     */\n    stateChanges;\n    /** The element ID for this control. */\n    id;\n    /** The placeholder for this control. */\n    placeholder;\n    /** Gets the AbstractControlDirective for this control. */\n    ngControl;\n    /** Whether the control is focused. */\n    focused;\n    /** Whether the control is empty. */\n    empty;\n    /** Whether the `MatFormField` label should try to float. */\n    shouldLabelFloat;\n    /** Whether the control is required. */\n    required;\n    /** Whether the control is disabled. */\n    disabled;\n    /** Whether the control is in an error state. */\n    errorState;\n    /**\n     * An optional name for the control type that can be used to distinguish `mat-form-field` elements\n     * based on their control type. The form field will add a class,\n     * `mat-form-field-type-{{controlType}}` to its root element.\n     */\n    controlType;\n    /**\n     * Whether the input is currently in an autofilled state. If property is not present on the\n     * control it is assumed to be false.\n     */\n    autofilled;\n    /**\n     * Value of `aria-describedby` that should be merged with the described-by ids\n     * which are set by the form-field.\n     */\n    userAriaDescribedBy;\n    /**\n     * Whether to automatically assign the ID of the form field as the `for` attribute\n     * on the `<label>` inside the form field. Set this to true to prevent the form\n     * field from associating the label with non-native elements.\n     */\n    disableAutomaticLabeling;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldControl, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatFormFieldControl, isStandalone: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldControl, decorators: [{\n            type: Directive\n        }] });\n\n/** @docs-private */\nfunction getMatFormFieldPlaceholderConflictError() {\n    return Error('Placeholder attribute and child element were both specified.');\n}\n/** @docs-private */\nfunction getMatFormFieldDuplicatedHintError(align) {\n    return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n/** @docs-private */\nfunction getMatFormFieldMissingControlError() {\n    return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nconst MAT_FORM_FIELD = new InjectionToken('MatFormField');\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nconst MAT_FORM_FIELD_DEFAULT_OPTIONS = new InjectionToken('MAT_FORM_FIELD_DEFAULT_OPTIONS');\n/** Default appearance used by the form field. */\nconst DEFAULT_APPEARANCE = 'fill';\n/**\n * Whether the label for form fields should by default float `always`,\n * `never`, or `auto`.\n */\nconst DEFAULT_FLOAT_LABEL = 'auto';\n/** Default way that the subscript element height is set. */\nconst DEFAULT_SUBSCRIPT_SIZING = 'fixed';\n/**\n * Default transform for docked floating labels in a MDC text-field. This value has been\n * extracted from the MDC text-field styles because we programmatically modify the docked\n * label transform, but do not want to accidentally discard the default label transform.\n */\nconst FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM = `translateY(-50%)`;\n/** Container for form controls that applies Material Design styling and behavior. */\nclass MatFormField {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _dir = inject(Directionality);\n    _platform = inject(Platform);\n    _idGenerator = inject(_IdGenerator);\n    _ngZone = inject(NgZone);\n    _injector = inject(Injector);\n    _defaults = inject(MAT_FORM_FIELD_DEFAULT_OPTIONS, {\n        optional: true,\n    });\n    _textField;\n    _iconPrefixContainer;\n    _textPrefixContainer;\n    _iconSuffixContainer;\n    _textSuffixContainer;\n    _floatingLabel;\n    _notchedOutline;\n    _lineRipple;\n    _formFieldControl;\n    _prefixChildren;\n    _suffixChildren;\n    _errorChildren;\n    _hintChildren;\n    _labelChild = contentChild(MatLabel);\n    /** Whether the required marker should be hidden. */\n    get hideRequiredMarker() {\n        return this._hideRequiredMarker;\n    }\n    set hideRequiredMarker(value) {\n        this._hideRequiredMarker = coerceBooleanProperty(value);\n    }\n    _hideRequiredMarker = false;\n    /**\n     * Theme color of the form field. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/form-field/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color = 'primary';\n    /** Whether the label should always float or float as the user types. */\n    get floatLabel() {\n        return this._floatLabel || this._defaults?.floatLabel || DEFAULT_FLOAT_LABEL;\n    }\n    set floatLabel(value) {\n        if (value !== this._floatLabel) {\n            this._floatLabel = value;\n            // For backwards compatibility. Custom form field controls or directives might set\n            // the \"floatLabel\" input and expect the form field view to be updated automatically.\n            // e.g. autocomplete trigger. Ideally we'd get rid of this and the consumers would just\n            // emit the \"stateChanges\" observable. TODO(devversion): consider removing.\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _floatLabel;\n    /** The form field appearance style. */\n    get appearance() {\n        return this._appearance;\n    }\n    set appearance(value) {\n        const oldValue = this._appearance;\n        const newAppearance = value || this._defaults?.appearance || DEFAULT_APPEARANCE;\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (newAppearance !== 'fill' && newAppearance !== 'outline') {\n                throw new Error(`MatFormField: Invalid appearance \"${newAppearance}\", valid values are \"fill\" or \"outline\".`);\n            }\n        }\n        this._appearance = newAppearance;\n        if (this._appearance === 'outline' && this._appearance !== oldValue) {\n            // If the appearance has been switched to `outline`, the label offset needs to be updated.\n            // The update can happen once the view has been re-checked, but not immediately because\n            // the view has not been updated and the notched-outline floating label is not present.\n            this._needsOutlineLabelOffsetUpdate = true;\n        }\n    }\n    _appearance = DEFAULT_APPEARANCE;\n    /**\n     * Whether the form field should reserve space for one line of hint/error text (default)\n     * or to have the spacing grow from 0px as needed based on the size of the hint/error content.\n     * Note that when using dynamic sizing, layout shifts will occur when hint/error text changes.\n     */\n    get subscriptSizing() {\n        return this._subscriptSizing || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    set subscriptSizing(value) {\n        this._subscriptSizing = value || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    _subscriptSizing = null;\n    /** Text for the form field hint. */\n    get hintLabel() {\n        return this._hintLabel;\n    }\n    set hintLabel(value) {\n        this._hintLabel = value;\n        this._processHints();\n    }\n    _hintLabel = '';\n    _hasIconPrefix = false;\n    _hasTextPrefix = false;\n    _hasIconSuffix = false;\n    _hasTextSuffix = false;\n    // Unique id for the internal form field label.\n    _labelId = this._idGenerator.getId('mat-mdc-form-field-label-');\n    // Unique id for the hint label.\n    _hintLabelId = this._idGenerator.getId('mat-mdc-hint-');\n    /** Gets the current form field control */\n    get _control() {\n        return this._explicitFormFieldControl || this._formFieldControl;\n    }\n    set _control(value) {\n        this._explicitFormFieldControl = value;\n    }\n    _destroyed = new Subject();\n    _isFocused = null;\n    _explicitFormFieldControl;\n    _needsOutlineLabelOffsetUpdate = false;\n    _previousControl = null;\n    _previousControlValidatorFn = null;\n    _stateChanges;\n    _valueChanges;\n    _describedByChanges;\n    _animationsDisabled;\n    constructor() {\n        const defaults = this._defaults;\n        if (defaults) {\n            if (defaults.appearance) {\n                this.appearance = defaults.appearance;\n            }\n            this._hideRequiredMarker = Boolean(defaults?.hideRequiredMarker);\n            if (defaults.color) {\n                this.color = defaults.color;\n            }\n        }\n        this._animationsDisabled = inject(ANIMATION_MODULE_TYPE, { optional: true }) === 'NoopAnimations';\n    }\n    ngAfterViewInit() {\n        // Initial focus state sync. This happens rarely, but we want to account for\n        // it in case the form field control has \"focused\" set to true on init.\n        this._updateFocusState();\n        if (!this._animationsDisabled) {\n            this._ngZone.runOutsideAngular(() => {\n                // Enable animations after a certain amount of time so that they don't run on init.\n                setTimeout(() => {\n                    this._elementRef.nativeElement.classList.add('mat-form-field-animations-enabled');\n                }, 300);\n            });\n        }\n        // Because the above changes a value used in the template after it was checked, we need\n        // to trigger CD or the change might not be reflected if there is no other CD scheduled.\n        this._changeDetectorRef.detectChanges();\n    }\n    ngAfterContentInit() {\n        this._assertFormFieldControl();\n        this._initializeSubscript();\n        this._initializePrefixAndSuffix();\n        this._initializeOutlineLabelOffsetSubscriptions();\n    }\n    ngAfterContentChecked() {\n        this._assertFormFieldControl();\n        // if form field was being used with an input in first place and then replaced by other\n        // component such as select.\n        if (this._control !== this._previousControl) {\n            this._initializeControl(this._previousControl);\n            // keep a reference for last validator we had.\n            if (this._control.ngControl && this._control.ngControl.control) {\n                this._previousControlValidatorFn = this._control.ngControl.control.validator;\n            }\n            this._previousControl = this._control;\n        }\n        // make sure the the control has been initialized.\n        if (this._control.ngControl && this._control.ngControl.control) {\n            // get the validators for current control.\n            const validatorFn = this._control.ngControl.control.validator;\n            // if our current validatorFn isn't equal to it might be we are CD behind, marking the\n            // component will allow us to catch up.\n            if (validatorFn !== this._previousControlValidatorFn) {\n                this._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges?.unsubscribe();\n        this._valueChanges?.unsubscribe();\n        this._describedByChanges?.unsubscribe();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Gets the id of the label element. If no label is present, returns `null`.\n     */\n    getLabelId = computed(() => (this._hasFloatingLabel() ? this._labelId : null));\n    /**\n     * Gets an ElementRef for the element that a overlay attached to the form field\n     * should be positioned relative to.\n     */\n    getConnectedOverlayOrigin() {\n        return this._textField || this._elementRef;\n    }\n    /** Animates the placeholder up and locks it in position. */\n    _animateAndLockLabel() {\n        // This is for backwards compatibility only. Consumers of the form field might use\n        // this method. e.g. the autocomplete trigger. This method has been added to the non-MDC\n        // form field because setting \"floatLabel\" to \"always\" caused the label to float without\n        // animation. This is different in MDC where the label always animates, so this method\n        // is no longer necessary. There doesn't seem any benefit in adding logic to allow changing\n        // the floating label state without animations. The non-MDC implementation was inconsistent\n        // because it always animates if \"floatLabel\" is set away from \"always\".\n        // TODO(devversion): consider removing this method when releasing the MDC form field.\n        if (this._hasFloatingLabel()) {\n            this.floatLabel = 'always';\n        }\n    }\n    /** Initializes the registered form field control. */\n    _initializeControl(previousControl) {\n        const control = this._control;\n        const classPrefix = 'mat-mdc-form-field-type-';\n        if (previousControl) {\n            this._elementRef.nativeElement.classList.remove(classPrefix + previousControl.controlType);\n        }\n        if (control.controlType) {\n            this._elementRef.nativeElement.classList.add(classPrefix + control.controlType);\n        }\n        // Subscribe to changes in the child control state in order to update the form field UI.\n        this._stateChanges?.unsubscribe();\n        this._stateChanges = control.stateChanges.subscribe(() => {\n            this._updateFocusState();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Updating the `aria-describedby` touches the DOM. Only do it if it actually needs to change.\n        this._describedByChanges?.unsubscribe();\n        this._describedByChanges = control.stateChanges\n            .pipe(startWith([undefined, undefined]), map(() => [control.errorState, control.userAriaDescribedBy]), pairwise(), filter(([[prevErrorState, prevDescribedBy], [currentErrorState, currentDescribedBy]]) => {\n            return prevErrorState !== currentErrorState || prevDescribedBy !== currentDescribedBy;\n        }))\n            .subscribe(() => this._syncDescribedByIds());\n        this._valueChanges?.unsubscribe();\n        // Run change detection if the value changes.\n        if (control.ngControl && control.ngControl.valueChanges) {\n            this._valueChanges = control.ngControl.valueChanges\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(() => this._changeDetectorRef.markForCheck());\n        }\n    }\n    _checkPrefixAndSuffixTypes() {\n        this._hasIconPrefix = !!this._prefixChildren.find(p => !p._isText);\n        this._hasTextPrefix = !!this._prefixChildren.find(p => p._isText);\n        this._hasIconSuffix = !!this._suffixChildren.find(s => !s._isText);\n        this._hasTextSuffix = !!this._suffixChildren.find(s => s._isText);\n    }\n    /** Initializes the prefix and suffix containers. */\n    _initializePrefixAndSuffix() {\n        this._checkPrefixAndSuffixTypes();\n        // Mark the form field as dirty whenever the prefix or suffix children change. This\n        // is necessary because we conditionally display the prefix/suffix containers based\n        // on whether there is projected content.\n        merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n            this._checkPrefixAndSuffixTypes();\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /**\n     * Initializes the subscript by validating hints and synchronizing \"aria-describedby\" ids\n     * with the custom form field control. Also subscribes to hint and error changes in order\n     * to be able to validate and synchronize ids on change.\n     */\n    _initializeSubscript() {\n        // Re-validate when the number of hints changes.\n        this._hintChildren.changes.subscribe(() => {\n            this._processHints();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Update the aria-described by when the number of errors changes.\n        this._errorChildren.changes.subscribe(() => {\n            this._syncDescribedByIds();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Initial mat-hint validation and subscript describedByIds sync.\n        this._validateHints();\n        this._syncDescribedByIds();\n    }\n    /** Throws an error if the form field's control is missing. */\n    _assertFormFieldControl() {\n        if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatFormFieldMissingControlError();\n        }\n    }\n    _updateFocusState() {\n        // Usually the MDC foundation would call \"activateFocus\" and \"deactivateFocus\" whenever\n        // certain DOM events are emitted. This is not possible in our implementation of the\n        // form field because we support abstract form field controls which are not necessarily\n        // of type input, nor do we have a reference to a native form field control element. Instead\n        // we handle the focus by checking if the abstract form field control focused state changes.\n        if (this._control.focused && !this._isFocused) {\n            this._isFocused = true;\n            this._lineRipple?.activate();\n        }\n        else if (!this._control.focused && (this._isFocused || this._isFocused === null)) {\n            this._isFocused = false;\n            this._lineRipple?.deactivate();\n        }\n        this._textField?.nativeElement.classList.toggle('mdc-text-field--focused', this._control.focused);\n    }\n    /**\n     * The floating label in the docked state needs to account for prefixes. The horizontal offset\n     * is calculated whenever the appearance changes to `outline`, the prefixes change, or when the\n     * form field is added to the DOM. This method sets up all subscriptions which are needed to\n     * trigger the label offset update.\n     */\n    _initializeOutlineLabelOffsetSubscriptions() {\n        // Whenever the prefix changes, schedule an update of the label offset.\n        // TODO(mmalerba): Use ResizeObserver to better support dynamically changing prefix content.\n        this._prefixChildren.changes.subscribe(() => (this._needsOutlineLabelOffsetUpdate = true));\n        // TODO(mmalerba): Split this into separate `afterRender` calls using the `EarlyRead` and\n        //  `Write` phases.\n        afterRender(() => {\n            if (this._needsOutlineLabelOffsetUpdate) {\n                this._needsOutlineLabelOffsetUpdate = false;\n                this._updateOutlineLabelOffset();\n            }\n        }, {\n            injector: this._injector,\n        });\n        this._dir.change\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => (this._needsOutlineLabelOffsetUpdate = true));\n    }\n    /** Whether the floating label should always float or not. */\n    _shouldAlwaysFloat() {\n        return this.floatLabel === 'always';\n    }\n    _hasOutline() {\n        return this.appearance === 'outline';\n    }\n    /**\n     * Whether the label should display in the infix. Labels in the outline appearance are\n     * displayed as part of the notched-outline and are horizontally offset to account for\n     * form field prefix content. This won't work in server side rendering since we cannot\n     * measure the width of the prefix container. To make the docked label appear as if the\n     * right offset has been calculated, we forcibly render the label inside the infix. Since\n     * the label is part of the infix, the label cannot overflow the prefix content.\n     */\n    _forceDisplayInfixLabel() {\n        return !this._platform.isBrowser && this._prefixChildren.length && !this._shouldLabelFloat();\n    }\n    _hasFloatingLabel = computed(() => !!this._labelChild());\n    _shouldLabelFloat() {\n        if (!this._hasFloatingLabel()) {\n            return false;\n        }\n        return this._control.shouldLabelFloat || this._shouldAlwaysFloat();\n    }\n    /**\n     * Determines whether a class from the AbstractControlDirective\n     * should be forwarded to the host element.\n     */\n    _shouldForward(prop) {\n        const control = this._control ? this._control.ngControl : null;\n        return control && control[prop];\n    }\n    /** Gets the type of subscript message to render (error or hint). */\n    _getSubscriptMessageType() {\n        return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState\n            ? 'error'\n            : 'hint';\n    }\n    /** Handle label resize events. */\n    _handleLabelResized() {\n        this._refreshOutlineNotchWidth();\n    }\n    /** Refreshes the width of the outline-notch, if present. */\n    _refreshOutlineNotchWidth() {\n        if (!this._hasOutline() || !this._floatingLabel || !this._shouldLabelFloat()) {\n            this._notchedOutline?._setNotchWidth(0);\n        }\n        else {\n            this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth());\n        }\n    }\n    /** Does any extra processing that is required when handling the hints. */\n    _processHints() {\n        this._validateHints();\n        this._syncDescribedByIds();\n    }\n    /**\n     * Ensure that there is a maximum of one of each \"mat-hint\" alignment specified. The hint\n     * label specified set through the input is being considered as \"start\" aligned.\n     *\n     * This method is a noop if Angular runs in production mode.\n     */\n    _validateHints() {\n        if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            let startHint;\n            let endHint;\n            this._hintChildren.forEach((hint) => {\n                if (hint.align === 'start') {\n                    if (startHint || this.hintLabel) {\n                        throw getMatFormFieldDuplicatedHintError('start');\n                    }\n                    startHint = hint;\n                }\n                else if (hint.align === 'end') {\n                    if (endHint) {\n                        throw getMatFormFieldDuplicatedHintError('end');\n                    }\n                    endHint = hint;\n                }\n            });\n        }\n    }\n    /**\n     * Sets the list of element IDs that describe the child control. This allows the control to update\n     * its `aria-describedby` attribute accordingly.\n     */\n    _syncDescribedByIds() {\n        if (this._control) {\n            let ids = [];\n            // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n            if (this._control.userAriaDescribedBy &&\n                typeof this._control.userAriaDescribedBy === 'string') {\n                ids.push(...this._control.userAriaDescribedBy.split(' '));\n            }\n            if (this._getSubscriptMessageType() === 'hint') {\n                const startHint = this._hintChildren\n                    ? this._hintChildren.find(hint => hint.align === 'start')\n                    : null;\n                const endHint = this._hintChildren\n                    ? this._hintChildren.find(hint => hint.align === 'end')\n                    : null;\n                if (startHint) {\n                    ids.push(startHint.id);\n                }\n                else if (this._hintLabel) {\n                    ids.push(this._hintLabelId);\n                }\n                if (endHint) {\n                    ids.push(endHint.id);\n                }\n            }\n            else if (this._errorChildren) {\n                ids.push(...this._errorChildren.map(error => error.id));\n            }\n            this._control.setDescribedByIds(ids);\n        }\n    }\n    /**\n     * Updates the horizontal offset of the label in the outline appearance. In the outline\n     * appearance, the notched-outline and label are not relative to the infix container because\n     * the outline intends to surround prefixes, suffixes and the infix. This means that the\n     * floating label by default overlaps prefixes in the docked state. To avoid this, we need to\n     * horizontally offset the label by the width of the prefix container. The MDC text-field does\n     * not need to do this because they use a fixed width for prefixes. Hence, they can simply\n     * incorporate the horizontal offset into their default text-field styles.\n     */\n    _updateOutlineLabelOffset() {\n        if (!this._hasOutline() || !this._floatingLabel) {\n            return;\n        }\n        const floatingLabel = this._floatingLabel.element;\n        // If no prefix is displayed, reset the outline label offset from potential\n        // previous label offset updates.\n        if (!(this._iconPrefixContainer || this._textPrefixContainer)) {\n            floatingLabel.style.transform = '';\n            return;\n        }\n        // If the form field is not attached to the DOM yet (e.g. in a tab), we defer\n        // the label offset update until the zone stabilizes.\n        if (!this._isAttachedToDom()) {\n            this._needsOutlineLabelOffsetUpdate = true;\n            return;\n        }\n        const iconPrefixContainer = this._iconPrefixContainer?.nativeElement;\n        const textPrefixContainer = this._textPrefixContainer?.nativeElement;\n        const iconSuffixContainer = this._iconSuffixContainer?.nativeElement;\n        const textSuffixContainer = this._textSuffixContainer?.nativeElement;\n        const iconPrefixContainerWidth = iconPrefixContainer?.getBoundingClientRect().width ?? 0;\n        const textPrefixContainerWidth = textPrefixContainer?.getBoundingClientRect().width ?? 0;\n        const iconSuffixContainerWidth = iconSuffixContainer?.getBoundingClientRect().width ?? 0;\n        const textSuffixContainerWidth = textSuffixContainer?.getBoundingClientRect().width ?? 0;\n        // If the directionality is RTL, the x-axis transform needs to be inverted. This\n        // is because `transformX` does not change based on the page directionality.\n        const negate = this._dir.value === 'rtl' ? '-1' : '1';\n        const prefixWidth = `${iconPrefixContainerWidth + textPrefixContainerWidth}px`;\n        const labelOffset = `var(--mat-mdc-form-field-label-offset-x, 0px)`;\n        const labelHorizontalOffset = `calc(${negate} * (${prefixWidth} + ${labelOffset}))`;\n        // Update the translateX of the floating label to account for the prefix container,\n        // but allow the CSS to override this setting via a CSS variable when the label is\n        // floating.\n        floatingLabel.style.transform = `var(\n        --mat-mdc-form-field-label-transform,\n        ${FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM} translateX(${labelHorizontalOffset})\n    )`;\n        // Prevent the label from overlapping the suffix when in resting position.\n        const prefixAndSuffixWidth = iconPrefixContainerWidth +\n            textPrefixContainerWidth +\n            iconSuffixContainerWidth +\n            textSuffixContainerWidth;\n        this._elementRef.nativeElement.style.setProperty('--mat-form-field-notch-max-width', `calc(100% - ${prefixAndSuffixWidth}px)`);\n    }\n    /** Checks whether the form field is attached to the DOM. */\n    _isAttachedToDom() {\n        const element = this._elementRef.nativeElement;\n        if (element.getRootNode) {\n            const rootNode = element.getRootNode();\n            // If the element is inside the DOM the root node will be either the document\n            // or the closest shadow root, otherwise it'll be the element itself.\n            return rootNode && rootNode !== element;\n        }\n        // Otherwise fall back to checking if it's in the document. This doesn't account for\n        // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n        return document.documentElement.contains(element);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormField, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatFormField, isStandalone: true, selector: \"mat-form-field\", inputs: { hideRequiredMarker: \"hideRequiredMarker\", color: \"color\", floatLabel: \"floatLabel\", appearance: \"appearance\", subscriptSizing: \"subscriptSizing\", hintLabel: \"hintLabel\" }, host: { properties: { \"class.mat-mdc-form-field-label-always-float\": \"_shouldAlwaysFloat()\", \"class.mat-mdc-form-field-has-icon-prefix\": \"_hasIconPrefix\", \"class.mat-mdc-form-field-has-icon-suffix\": \"_hasIconSuffix\", \"class.mat-form-field-invalid\": \"_control.errorState\", \"class.mat-form-field-disabled\": \"_control.disabled\", \"class.mat-form-field-autofilled\": \"_control.autofilled\", \"class.mat-form-field-appearance-fill\": \"appearance == \\\"fill\\\"\", \"class.mat-form-field-appearance-outline\": \"appearance == \\\"outline\\\"\", \"class.mat-form-field-hide-placeholder\": \"_hasFloatingLabel() && !_shouldLabelFloat()\", \"class.mat-focused\": \"_control.focused\", \"class.mat-primary\": \"color !== \\\"accent\\\" && color !== \\\"warn\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class.ng-untouched\": \"_shouldForward(\\\"untouched\\\")\", \"class.ng-touched\": \"_shouldForward(\\\"touched\\\")\", \"class.ng-pristine\": \"_shouldForward(\\\"pristine\\\")\", \"class.ng-dirty\": \"_shouldForward(\\\"dirty\\\")\", \"class.ng-valid\": \"_shouldForward(\\\"valid\\\")\", \"class.ng-invalid\": \"_shouldForward(\\\"invalid\\\")\", \"class.ng-pending\": \"_shouldForward(\\\"pending\\\")\" }, classAttribute: \"mat-mdc-form-field\" }, providers: [\n            { provide: MAT_FORM_FIELD, useExisting: MatFormField },\n            { provide: FLOATING_LABEL_PARENT, useExisting: MatFormField },\n        ], queries: [{ propertyName: \"_labelChild\", first: true, predicate: MatLabel, descendants: true, isSignal: true }, { propertyName: \"_formFieldControl\", first: true, predicate: MatFormFieldControl, descendants: true }, { propertyName: \"_prefixChildren\", predicate: MAT_PREFIX, descendants: true }, { propertyName: \"_suffixChildren\", predicate: MAT_SUFFIX, descendants: true }, { propertyName: \"_errorChildren\", predicate: MAT_ERROR, descendants: true }, { propertyName: \"_hintChildren\", predicate: MatHint, descendants: true }], viewQueries: [{ propertyName: \"_textField\", first: true, predicate: [\"textField\"], descendants: true }, { propertyName: \"_iconPrefixContainer\", first: true, predicate: [\"iconPrefixContainer\"], descendants: true }, { propertyName: \"_textPrefixContainer\", first: true, predicate: [\"textPrefixContainer\"], descendants: true }, { propertyName: \"_iconSuffixContainer\", first: true, predicate: [\"iconSuffixContainer\"], descendants: true }, { propertyName: \"_textSuffixContainer\", first: true, predicate: [\"textSuffixContainer\"], descendants: true }, { propertyName: \"_floatingLabel\", first: true, predicate: MatFormFieldFloatingLabel, descendants: true }, { propertyName: \"_notchedOutline\", first: true, predicate: MatFormFieldNotchedOutline, descendants: true }, { propertyName: \"_lineRipple\", first: true, predicate: MatFormFieldLineRipple, descendants: true }], exportAs: [\"matFormField\"], ngImport: i0, template: \"<ng-template #labelTemplate>\\n  <!--\\n    MDC recommends that the text-field is a `<label>` element. This rather complicates the\\n    setup because it would require every form-field control to explicitly set `aria-labelledby`.\\n    This is because the `<label>` itself contains more than the actual label (e.g. prefix, suffix\\n    or other projected content), and screen readers could potentially read out undesired content.\\n    Excluding elements from being printed out requires them to be marked with `aria-hidden`, or\\n    the form control is set to a scoped element for the label (using `aria-labelledby`). Both of\\n    these options seem to complicate the setup because we know exactly what content is rendered\\n    as part of the label, and we don't want to spend resources on walking through projected content\\n    to set `aria-hidden`. Nor do we want to set `aria-labelledby` on every form control if we could\\n    simply link the label to the control using the label `for` attribute.\\n  -->\\n  @if (_hasFloatingLabel()) {\\n    <label\\n      matFormFieldFloatingLabel\\n      [floating]=\\\"_shouldLabelFloat()\\\"\\n      [monitorResize]=\\\"_hasOutline()\\\"\\n      [id]=\\\"_labelId\\\"\\n      [attr.for]=\\\"_control.disableAutomaticLabeling ? null : _control.id\\\"\\n    >\\n      <ng-content select=\\\"mat-label\\\"></ng-content>\\n      <!--\\n        We set the required marker as a separate element, in order to make it easier to target if\\n        apps want to override it and to be able to set `aria-hidden` so that screen readers don't\\n        pick it up.\\n       -->\\n      @if (!hideRequiredMarker && _control.required) {\\n        <span\\n          aria-hidden=\\\"true\\\"\\n          class=\\\"mat-mdc-form-field-required-marker mdc-floating-label--required\\\"\\n        ></span>\\n      }\\n    </label>\\n  }\\n</ng-template>\\n\\n<div\\n  class=\\\"mat-mdc-text-field-wrapper mdc-text-field\\\"\\n  #textField\\n  [class.mdc-text-field--filled]=\\\"!_hasOutline()\\\"\\n  [class.mdc-text-field--outlined]=\\\"_hasOutline()\\\"\\n  [class.mdc-text-field--no-label]=\\\"!_hasFloatingLabel()\\\"\\n  [class.mdc-text-field--disabled]=\\\"_control.disabled\\\"\\n  [class.mdc-text-field--invalid]=\\\"_control.errorState\\\"\\n  (click)=\\\"_control.onContainerClick($event)\\\"\\n>\\n  @if (!_hasOutline() && !_control.disabled) {\\n    <div class=\\\"mat-mdc-form-field-focus-overlay\\\"></div>\\n  }\\n  <div class=\\\"mat-mdc-form-field-flex\\\">\\n    @if (_hasOutline()) {\\n      <div matFormFieldNotchedOutline [matFormFieldNotchedOutlineOpen]=\\\"_shouldLabelFloat()\\\">\\n        @if (!_forceDisplayInfixLabel()) {\\n          <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n        }\\n      </div>\\n    }\\n\\n    @if (_hasIconPrefix) {\\n      <div class=\\\"mat-mdc-form-field-icon-prefix\\\" #iconPrefixContainer>\\n        <ng-content select=\\\"[matPrefix], [matIconPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasTextPrefix) {\\n      <div class=\\\"mat-mdc-form-field-text-prefix\\\" #textPrefixContainer>\\n        <ng-content select=\\\"[matTextPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-form-field-infix\\\">\\n      @if (!_hasOutline() || _forceDisplayInfixLabel()) {\\n        <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n      }\\n\\n      <ng-content></ng-content>\\n    </div>\\n\\n    @if (_hasTextSuffix) {\\n      <div class=\\\"mat-mdc-form-field-text-suffix\\\" #textSuffixContainer>\\n        <ng-content select=\\\"[matTextSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasIconSuffix) {\\n      <div class=\\\"mat-mdc-form-field-icon-suffix\\\" #iconSuffixContainer>\\n        <ng-content select=\\\"[matSuffix], [matIconSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n  </div>\\n\\n  @if (!_hasOutline()) {\\n    <div matFormFieldLineRipple></div>\\n  }\\n</div>\\n\\n<div\\n    class=\\\"mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align\\\"\\n    [class.mat-mdc-form-field-subscript-dynamic-size]=\\\"subscriptSizing === 'dynamic'\\\"\\n>\\n  @let subscriptMessageType = _getSubscriptMessageType();\\n\\n  <!-- \\n    Use a single permanent wrapper for both hints and errors so aria-live works correctly,\\n    as having it appear post render will not consistently work. We also do not want to add\\n    additional divs as it causes styling regressions.\\n    -->\\n  <div aria-atomic=\\\"true\\\" aria-live=\\\"polite\\\" \\n      [class.mat-mdc-form-field-error-wrapper]=\\\"subscriptMessageType === 'error'\\\"\\n      [class.mat-mdc-form-field-hint-wrapper]=\\\"subscriptMessageType === 'hint'\\\"\\n    >\\n    @switch (subscriptMessageType) {\\n      @case ('error') {\\n        <ng-content select=\\\"mat-error, [matError]\\\"></ng-content>\\n      }\\n\\n      @case ('hint') {\\n        @if (hintLabel) {\\n          <mat-hint [id]=\\\"_hintLabelId\\\">{{hintLabel}}</mat-hint>\\n        }\\n        <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n        <div class=\\\"mat-mdc-form-field-hint-spacer\\\"></div>\\n        <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n      }\\n    }\\n  </div>\\n</div>\\n\", styles: [\".mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-filled-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-outlined-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-filled-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-filled-text-field-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-filled-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-filled-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-filled-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-filled-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-filled-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-filled-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-outlined-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-outlined-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-outlined-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-outlined-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-outlined-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-outlined-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-outline-color, var(--mat-sys-outline));border-width:var(--mdc-outlined-text-field-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mdc-outlined-text-field-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),100% - max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))*2)}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none;--mat-form-field-notch-max-width: 100%}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mdc-filled-text-field-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\\n\"], dependencies: [{ kind: \"directive\", type: MatFormFieldFloatingLabel, selector: \"label[matFormFieldFloatingLabel]\", inputs: [\"floating\", \"monitorResize\"] }, { kind: \"component\", type: MatFormFieldNotchedOutline, selector: \"div[matFormFieldNotchedOutline]\", inputs: [\"matFormFieldNotchedOutlineOpen\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: MatFormFieldLineRipple, selector: \"div[matFormFieldLineRipple]\" }, { kind: \"directive\", type: MatHint, selector: \"mat-hint\", inputs: [\"align\", \"id\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormField, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-form-field', exportAs: 'matFormField', host: {\n                        'class': 'mat-mdc-form-field',\n                        '[class.mat-mdc-form-field-label-always-float]': '_shouldAlwaysFloat()',\n                        '[class.mat-mdc-form-field-has-icon-prefix]': '_hasIconPrefix',\n                        '[class.mat-mdc-form-field-has-icon-suffix]': '_hasIconSuffix',\n                        // Note that these classes reuse the same names as the non-MDC version, because they can be\n                        // considered a public API since custom form controls may use them to style themselves.\n                        // See https://github.com/angular/components/pull/20502#discussion_r486124901.\n                        '[class.mat-form-field-invalid]': '_control.errorState',\n                        '[class.mat-form-field-disabled]': '_control.disabled',\n                        '[class.mat-form-field-autofilled]': '_control.autofilled',\n                        '[class.mat-form-field-appearance-fill]': 'appearance == \"fill\"',\n                        '[class.mat-form-field-appearance-outline]': 'appearance == \"outline\"',\n                        '[class.mat-form-field-hide-placeholder]': '_hasFloatingLabel() && !_shouldLabelFloat()',\n                        '[class.mat-focused]': '_control.focused',\n                        '[class.mat-primary]': 'color !== \"accent\" && color !== \"warn\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class.ng-untouched]': '_shouldForward(\"untouched\")',\n                        '[class.ng-touched]': '_shouldForward(\"touched\")',\n                        '[class.ng-pristine]': '_shouldForward(\"pristine\")',\n                        '[class.ng-dirty]': '_shouldForward(\"dirty\")',\n                        '[class.ng-valid]': '_shouldForward(\"valid\")',\n                        '[class.ng-invalid]': '_shouldForward(\"invalid\")',\n                        '[class.ng-pending]': '_shouldForward(\"pending\")',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        { provide: MAT_FORM_FIELD, useExisting: MatFormField },\n                        { provide: FLOATING_LABEL_PARENT, useExisting: MatFormField },\n                    ], imports: [\n                        MatFormFieldFloatingLabel,\n                        MatFormFieldNotchedOutline,\n                        NgTemplateOutlet,\n                        MatFormFieldLineRipple,\n                        MatHint,\n                    ], template: \"<ng-template #labelTemplate>\\n  <!--\\n    MDC recommends that the text-field is a `<label>` element. This rather complicates the\\n    setup because it would require every form-field control to explicitly set `aria-labelledby`.\\n    This is because the `<label>` itself contains more than the actual label (e.g. prefix, suffix\\n    or other projected content), and screen readers could potentially read out undesired content.\\n    Excluding elements from being printed out requires them to be marked with `aria-hidden`, or\\n    the form control is set to a scoped element for the label (using `aria-labelledby`). Both of\\n    these options seem to complicate the setup because we know exactly what content is rendered\\n    as part of the label, and we don't want to spend resources on walking through projected content\\n    to set `aria-hidden`. Nor do we want to set `aria-labelledby` on every form control if we could\\n    simply link the label to the control using the label `for` attribute.\\n  -->\\n  @if (_hasFloatingLabel()) {\\n    <label\\n      matFormFieldFloatingLabel\\n      [floating]=\\\"_shouldLabelFloat()\\\"\\n      [monitorResize]=\\\"_hasOutline()\\\"\\n      [id]=\\\"_labelId\\\"\\n      [attr.for]=\\\"_control.disableAutomaticLabeling ? null : _control.id\\\"\\n    >\\n      <ng-content select=\\\"mat-label\\\"></ng-content>\\n      <!--\\n        We set the required marker as a separate element, in order to make it easier to target if\\n        apps want to override it and to be able to set `aria-hidden` so that screen readers don't\\n        pick it up.\\n       -->\\n      @if (!hideRequiredMarker && _control.required) {\\n        <span\\n          aria-hidden=\\\"true\\\"\\n          class=\\\"mat-mdc-form-field-required-marker mdc-floating-label--required\\\"\\n        ></span>\\n      }\\n    </label>\\n  }\\n</ng-template>\\n\\n<div\\n  class=\\\"mat-mdc-text-field-wrapper mdc-text-field\\\"\\n  #textField\\n  [class.mdc-text-field--filled]=\\\"!_hasOutline()\\\"\\n  [class.mdc-text-field--outlined]=\\\"_hasOutline()\\\"\\n  [class.mdc-text-field--no-label]=\\\"!_hasFloatingLabel()\\\"\\n  [class.mdc-text-field--disabled]=\\\"_control.disabled\\\"\\n  [class.mdc-text-field--invalid]=\\\"_control.errorState\\\"\\n  (click)=\\\"_control.onContainerClick($event)\\\"\\n>\\n  @if (!_hasOutline() && !_control.disabled) {\\n    <div class=\\\"mat-mdc-form-field-focus-overlay\\\"></div>\\n  }\\n  <div class=\\\"mat-mdc-form-field-flex\\\">\\n    @if (_hasOutline()) {\\n      <div matFormFieldNotchedOutline [matFormFieldNotchedOutlineOpen]=\\\"_shouldLabelFloat()\\\">\\n        @if (!_forceDisplayInfixLabel()) {\\n          <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n        }\\n      </div>\\n    }\\n\\n    @if (_hasIconPrefix) {\\n      <div class=\\\"mat-mdc-form-field-icon-prefix\\\" #iconPrefixContainer>\\n        <ng-content select=\\\"[matPrefix], [matIconPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasTextPrefix) {\\n      <div class=\\\"mat-mdc-form-field-text-prefix\\\" #textPrefixContainer>\\n        <ng-content select=\\\"[matTextPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-form-field-infix\\\">\\n      @if (!_hasOutline() || _forceDisplayInfixLabel()) {\\n        <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n      }\\n\\n      <ng-content></ng-content>\\n    </div>\\n\\n    @if (_hasTextSuffix) {\\n      <div class=\\\"mat-mdc-form-field-text-suffix\\\" #textSuffixContainer>\\n        <ng-content select=\\\"[matTextSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasIconSuffix) {\\n      <div class=\\\"mat-mdc-form-field-icon-suffix\\\" #iconSuffixContainer>\\n        <ng-content select=\\\"[matSuffix], [matIconSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n  </div>\\n\\n  @if (!_hasOutline()) {\\n    <div matFormFieldLineRipple></div>\\n  }\\n</div>\\n\\n<div\\n    class=\\\"mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align\\\"\\n    [class.mat-mdc-form-field-subscript-dynamic-size]=\\\"subscriptSizing === 'dynamic'\\\"\\n>\\n  @let subscriptMessageType = _getSubscriptMessageType();\\n\\n  <!-- \\n    Use a single permanent wrapper for both hints and errors so aria-live works correctly,\\n    as having it appear post render will not consistently work. We also do not want to add\\n    additional divs as it causes styling regressions.\\n    -->\\n  <div aria-atomic=\\\"true\\\" aria-live=\\\"polite\\\" \\n      [class.mat-mdc-form-field-error-wrapper]=\\\"subscriptMessageType === 'error'\\\"\\n      [class.mat-mdc-form-field-hint-wrapper]=\\\"subscriptMessageType === 'hint'\\\"\\n    >\\n    @switch (subscriptMessageType) {\\n      @case ('error') {\\n        <ng-content select=\\\"mat-error, [matError]\\\"></ng-content>\\n      }\\n\\n      @case ('hint') {\\n        @if (hintLabel) {\\n          <mat-hint [id]=\\\"_hintLabelId\\\">{{hintLabel}}</mat-hint>\\n        }\\n        <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n        <div class=\\\"mat-mdc-form-field-hint-spacer\\\"></div>\\n        <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n      }\\n    }\\n  </div>\\n</div>\\n\", styles: [\".mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-filled-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-outlined-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-filled-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-filled-text-field-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-filled-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-filled-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-filled-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-filled-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-filled-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-filled-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-outlined-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-outlined-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-outlined-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-outlined-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-outlined-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-outlined-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-outline-color, var(--mat-sys-outline));border-width:var(--mdc-outlined-text-field-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mdc-outlined-text-field-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),100% - max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))*2)}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none;--mat-form-field-notch-max-width: 100%}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mdc-filled-text-field-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _textField: [{\n                type: ViewChild,\n                args: ['textField']\n            }], _iconPrefixContainer: [{\n                type: ViewChild,\n                args: ['iconPrefixContainer']\n            }], _textPrefixContainer: [{\n                type: ViewChild,\n                args: ['textPrefixContainer']\n            }], _iconSuffixContainer: [{\n                type: ViewChild,\n                args: ['iconSuffixContainer']\n            }], _textSuffixContainer: [{\n                type: ViewChild,\n                args: ['textSuffixContainer']\n            }], _floatingLabel: [{\n                type: ViewChild,\n                args: [MatFormFieldFloatingLabel]\n            }], _notchedOutline: [{\n                type: ViewChild,\n                args: [MatFormFieldNotchedOutline]\n            }], _lineRipple: [{\n                type: ViewChild,\n                args: [MatFormFieldLineRipple]\n            }], _formFieldControl: [{\n                type: ContentChild,\n                args: [MatFormFieldControl]\n            }], _prefixChildren: [{\n                type: ContentChildren,\n                args: [MAT_PREFIX, { descendants: true }]\n            }], _suffixChildren: [{\n                type: ContentChildren,\n                args: [MAT_SUFFIX, { descendants: true }]\n            }], _errorChildren: [{\n                type: ContentChildren,\n                args: [MAT_ERROR, { descendants: true }]\n            }], _hintChildren: [{\n                type: ContentChildren,\n                args: [MatHint, { descendants: true }]\n            }], hideRequiredMarker: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], floatLabel: [{\n                type: Input\n            }], appearance: [{\n                type: Input\n            }], subscriptSizing: [{\n                type: Input\n            }], hintLabel: [{\n                type: Input\n            }] } });\n\nexport { MatLabel as M, MAT_ERROR as a, MatError as b, MatHint as c, MAT_PREFIX as d, MatPrefix as e, MAT_SUFFIX as f, MatSuffix as g, MAT_FORM_FIELD as h, MAT_FORM_FIELD_DEFAULT_OPTIONS as i, MatFormField as j, MatFormFieldControl as k, getMatFormFieldPlaceholderConflictError as l, getMatFormFieldDuplicatedHintError as m, getMatFormFieldMissingControlError as n };\n//# sourceMappingURL=form-field-DqPi4knt.mjs.map\n"], "mappings": ";;;;;;;;;;;;;;IAgBiFA,EAAE,CAAAC,SAAA,cA67B+jG,CAAC;EAAA;AAAA;AAAA,SAAAC,mDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77BlkGH,EAAE,CAAAK,cAAA,eA67BwkF,CAAC;IA77B3kFL,EAAE,CAAAM,YAAA,KA67B8nF,CAAC;IA77BjoFN,EAAE,CAAAO,UAAA,IAAAC,gEAAA,kBA67By6F,CAAC;IA77B56FR,EAAE,CAAAS,YAAA,CA67BslG,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GA77BzlGV,EAAE,CAAAW,aAAA;IAAFX,EAAE,CAAAY,UAAA,aAAAF,MAAA,CAAAG,iBAAA,EA67Bk7E,CAAC,kBAAAH,MAAA,CAAAI,WAAA,EAAwC,CAAC,OAAAJ,MAAA,CAAAK,QAAwB,CAAC;IA77Bv/Ef,EAAE,CAAAgB,WAAA,QAAAN,MAAA,CAAAO,QAAA,CAAAC,wBAAA,UAAAR,MAAA,CAAAO,QAAA,CAAAE,EAAA;IAAFnB,EAAE,CAAAoB,SAAA,EA67BwkG,CAAC;IA77B3kGpB,EAAE,CAAAqB,aAAA,EAAAX,MAAA,CAAAY,kBAAA,IAAAZ,MAAA,CAAAO,QAAA,CAAAM,QAAA,SA67BwkG,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77B3kGH,EAAE,CAAAO,UAAA,IAAAL,kDAAA,mBA67B21E,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAO,MAAA,GA77B91EV,EAAE,CAAAW,aAAA;IAAFX,EAAE,CAAAqB,aAAA,CAAAX,MAAA,CAAAe,iBAAA,WA67B2lG,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77B9lGH,EAAE,CAAAC,SAAA,YA67BqnH,CAAC;EAAA;AAAA;AAAA,SAAA0B,iEAAAxB,EAAA,EAAAC,GAAA;AAAA,SAAAwB,mDAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77BxnHH,EAAE,CAAAO,UAAA,IAAAoB,gEAAA,yBA67B24H,CAAC;EAAA;EAAA,IAAAxB,EAAA;IA77B94HH,EAAE,CAAAW,aAAA;IAAA,MAAAkB,gBAAA,GAAF7B,EAAE,CAAA8B,WAAA;IAAF9B,EAAE,CAAAY,UAAA,qBAAAiB,gBA67B04H,CAAC;EAAA;AAAA;AAAA,SAAAE,qCAAA5B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77B74HH,EAAE,CAAAK,cAAA,YA67BiyH,CAAC;IA77BpyHL,EAAE,CAAAO,UAAA,IAAAqB,kDAAA,gBA67B60H,CAAC;IA77Bh1H5B,EAAE,CAAAS,YAAA,CA67Bk7H,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GA77Br7HV,EAAE,CAAAW,aAAA;IAAFX,EAAE,CAAAY,UAAA,mCAAAF,MAAA,CAAAG,iBAAA,EA67BgyH,CAAC;IA77BnyHb,EAAE,CAAAoB,SAAA,CA67Bo6H,CAAC;IA77Bv6HpB,EAAE,CAAAqB,aAAA,EAAAX,MAAA,CAAAsB,uBAAA,WA67Bo6H,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77Bv6HH,EAAE,CAAAK,cAAA,gBA67BkiI,CAAC;IA77BriIL,EAAE,CAAAM,YAAA,KA67B6mI,CAAC;IA77BhnIN,EAAE,CAAAS,YAAA,CA67B2nI,CAAC;EAAA;AAAA;AAAA,SAAAyB,qCAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77B9nIH,EAAE,CAAAK,cAAA,gBA67B2uI,CAAC;IA77B9uIL,EAAE,CAAAM,YAAA,KA67ByyI,CAAC;IA77B5yIN,EAAE,CAAAS,YAAA,CA67BuzI,CAAC;EAAA;AAAA;AAAA,SAAA0B,oDAAAhC,EAAA,EAAAC,GAAA;AAAA,SAAAgC,sCAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77B1zIH,EAAE,CAAAO,UAAA,IAAA4B,mDAAA,yBA67Bq+I,CAAC;EAAA;EAAA,IAAAhC,EAAA;IA77Bx+IH,EAAE,CAAAW,aAAA;IAAA,MAAAkB,gBAAA,GAAF7B,EAAE,CAAA8B,WAAA;IAAF9B,EAAE,CAAAY,UAAA,qBAAAiB,gBA67Bo+I,CAAC;EAAA;AAAA;AAAA,SAAAQ,sCAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77Bv+IH,EAAE,CAAAK,cAAA,gBA67BopJ,CAAC;IA77BvpJL,EAAE,CAAAM,YAAA,KA67BktJ,CAAC;IA77BrtJN,EAAE,CAAAS,YAAA,CA67BguJ,CAAC;EAAA;AAAA;AAAA,SAAA6B,sCAAAnC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77BnuJH,EAAE,CAAAK,cAAA,gBA67Bg1J,CAAC;IA77Bn1JL,EAAE,CAAAM,YAAA,KA67B25J,CAAC;IA77B95JN,EAAE,CAAAS,YAAA,CA67By6J,CAAC;EAAA;AAAA;AAAA,SAAA8B,sCAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77B56JH,EAAE,CAAAC,SAAA,aA67B8/J,CAAC;EAAA;AAAA;AAAA,SAAAuC,+BAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77BjgKH,EAAE,CAAAM,YAAA,KA67Bs2L,CAAC;EAAA;AAAA;AAAA,SAAAmC,6CAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77Bz2LH,EAAE,CAAAK,cAAA,kBA67Bg9L,CAAC;IA77Bn9LL,EAAE,CAAA0C,MAAA,EA67B69L,CAAC;IA77Bh+L1C,EAAE,CAAAS,YAAA,CA67Bw+L,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GA77B3+LV,EAAE,CAAAW,aAAA;IAAFX,EAAE,CAAAY,UAAA,OAAAF,MAAA,CAAAiC,YA67B+8L,CAAC;IA77Bl9L3C,EAAE,CAAAoB,SAAA,CA67B69L,CAAC;IA77Bh+LpB,EAAE,CAAA4C,iBAAA,CAAAlC,MAAA,CAAAmC,SA67B69L,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA77Bh+LH,EAAE,CAAAO,UAAA,IAAAkC,4CAAA,sBA67Bo6L,CAAC;IA77Bv6LzC,EAAE,CAAAM,YAAA,KA67B6jM,CAAC;IA77BhkMN,EAAE,CAAAC,SAAA,aA67B2nM,CAAC;IA77B9nMD,EAAE,CAAAM,YAAA,KA67B+rM,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAAO,MAAA,GA77BlsMV,EAAE,CAAAW,aAAA;IAAFX,EAAE,CAAAqB,aAAA,CAAAX,MAAA,CAAAmC,SAAA,SA67Bm/L,CAAC;EAAA;AAAA;AA78BvkM,SAASE,cAAc,QAAQ,mBAAmB;AAClD,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAO,KAAKlD,EAAE,MAAM,eAAe;AACnC,SAASmD,SAAS,EAAEC,cAAc,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,QAAQ,eAAe;AACjS,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,YAAY,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACnD,SAASC,SAAS,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAC5E,SAASC,oBAAoB,QAAQ,gCAAgC;;AAErE;AACA,MAAMC,QAAQ,CAAC;AAGdC,SAAA,GAHKD,QAAQ;AAAAE,eAAA,CAARF,QAAQ,wBAAAG,kBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IACyFJ,SAAQ;AAAA;AAAAE,eAAA,CADzGF,QAAQ,8BAImEhF,EAAE,CAAAqF,iBAAA;EAAAC,IAAA,EAFQN,SAAQ;EAAAO,SAAA;AAAA;AAEnG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFxF,EAAE,CAAAyF,iBAAA,CAAQT,QAAQ,EAAc,CAAC;IACtGM,IAAI,EAAEnC,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,IAAIxC,cAAc,CAAC,UAAU,CAAC;AAChD;AACA,MAAMyC,QAAQ,CAAC;EAEXC,WAAWA,CAAA,EAAG;IAAAZ,eAAA,aADT7B,MAAM,CAACiB,YAAY,CAAC,CAACyB,KAAK,CAAC,gBAAgB,CAAC;EACjC;AAGpB;AAACC,SAAA,GALKH,QAAQ;AAAAX,eAAA,CAARW,QAAQ,wBAAAI,kBAAAb,iBAAA;EAAA,YAAAA,iBAAA,IAGyFS,SAAQ;AAAA;AAAAX,eAAA,CAHzGW,QAAQ,8BAdmE7F,EAAE,CAAAqF,iBAAA;EAAAC,IAAA,EAkBQO,SAAQ;EAAAN,SAAA;EAAAW,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,uBAAAlG,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAlBlBH,EAAE,CAAAsG,cAAA,OAAAlG,GAAA,CAAAe,EAkBe,CAAC;IAAA;EAAA;EAAAoF,MAAA;IAAApF,EAAA;EAAA;EAAAqF,QAAA,GAlBlBxG,EAAE,CAAAyG,kBAAA,CAkB4N,CAAC;IAAEC,OAAO,EAAEd,SAAS;IAAEe,WAAW,EAAEd;EAAS,CAAC,CAAC;AAAA;AAE9V;EAAA,QAAAL,SAAA,oBAAAA,SAAA,KApBiFxF,EAAE,CAAAyF,iBAAA,CAoBQI,QAAQ,EAAc,CAAC;IACtGP,IAAI,EAAEnC,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjCiB,IAAI,EAAE;QACF,OAAO,EAAE,0DAA0D;QACnE,MAAM,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEd,SAAS;QAAEe,WAAW,EAAEd;MAAS,CAAC;IAC7D,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE1E,EAAE,EAAE,CAAC;MAC7CmE,IAAI,EAAEhC;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMwD,OAAO,CAAC;EAAAhB,YAAA;IACV;IAAAZ,eAAA,gBACQ,OAAO;IACf;IAAAA,eAAA,aACK7B,MAAM,CAACiB,YAAY,CAAC,CAACyB,KAAK,CAAC,eAAe,CAAC;EAAA;AAGpD;AAACgB,QAAA,GAPKD,OAAO;AAAA5B,eAAA,CAAP4B,OAAO,wBAAAE,iBAAA5B,iBAAA;EAAA,YAAAA,iBAAA,IAK0F0B,QAAO;AAAA;AAAA5B,eAAA,CALxG4B,OAAO,8BAnCoE9G,EAAE,CAAAqF,iBAAA;EAAAC,IAAA,EAyCQwB,QAAO;EAAAvB,SAAA;EAAAW,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAa,sBAAA9G,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAzCjBH,EAAE,CAAAsG,cAAA,OAAAlG,GAAA,CAAAe,EAyCc,CAAC;MAzCjBnB,EAAE,CAAAgB,WAAA,UAyCQ,IAAI;MAzCdhB,EAAE,CAAAkH,WAAA,gCAAA9G,GAAA,CAAA+G,KAAA,KAyCkB,KAAJ,CAAC;IAAA;EAAA;EAAAZ,MAAA;IAAAY,KAAA;IAAAhG,EAAA;EAAA;AAAA;AAElG;EAAA,QAAAqE,SAAA,oBAAAA,SAAA,KA3CiFxF,EAAE,CAAAyF,iBAAA,CA2CQqB,OAAO,EAAc,CAAC;IACrGxB,IAAI,EAAEnC,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBiB,IAAI,EAAE;QACF,OAAO,EAAE,yDAAyD;QAClE,qCAAqC,EAAE,iBAAiB;QACxD,MAAM,EAAE,IAAI;QACZ;QACA,cAAc,EAAE;MACpB;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEO,KAAK,EAAE,CAAC;MACtB7B,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEnC,EAAE,EAAE,CAAC;MACLmE,IAAI,EAAEhC;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAM8D,UAAU,GAAG,IAAIhE,cAAc,CAAC,WAAW,CAAC;AAClD;AACA,MAAMiE,SAAS,CAAC;EAAAvB,YAAA;IAAAZ,eAAA,kBAIF,KAAK;EAAA;EAHf,IAAIoC,eAAeA,CAACC,KAAK,EAAE;IACvB,IAAI,CAACC,OAAO,GAAG,IAAI;EACvB;AAIJ;AAACC,UAAA,GAPKJ,SAAS;AAAAnC,eAAA,CAATmC,SAAS,wBAAAK,mBAAAtC,iBAAA;EAAA,YAAAA,iBAAA,IAKwFiC,UAAS;AAAA;AAAAnC,eAAA,CAL1GmC,SAAS,8BApEkErH,EAAE,CAAAqF,iBAAA;EAAAC,IAAA,EA0EQ+B,UAAS;EAAA9B,SAAA;EAAAgB,MAAA;IAAAe,eAAA;EAAA;EAAAd,QAAA,GA1EnBxG,EAAE,CAAAyG,kBAAA,CA0EgL,CAAC;IAAEC,OAAO,EAAEU,UAAU;IAAET,WAAW,EAAEU;EAAU,CAAC,CAAC;AAAA;AAEpT;EAAA,QAAA7B,SAAA,oBAAAA,SAAA,KA5EiFxF,EAAE,CAAAyF,iBAAA,CA4EQ4B,SAAS,EAAc,CAAC;IACvG/B,IAAI,EAAEnC,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+CAA+C;MACzDkB,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEU,UAAU;QAAET,WAAW,EAAEU;MAAU,CAAC;IAC/D,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEC,eAAe,EAAE,CAAC;MAChChC,IAAI,EAAEhC,KAAK;MACXoC,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMiC,UAAU,GAAG,IAAIvE,cAAc,CAAC,WAAW,CAAC;AAClD;AACA,MAAMwE,SAAS,CAAC;EAAA9B,YAAA;IAAAZ,eAAA,kBAIF,KAAK;EAAA;EAHf,IAAIoC,eAAeA,CAACC,KAAK,EAAE;IACvB,IAAI,CAACC,OAAO,GAAG,IAAI;EACvB;AAIJ;AAACK,UAAA,GAPKD,SAAS;AAAA1C,eAAA,CAAT0C,SAAS,wBAAAE,mBAAA1C,iBAAA;EAAA,YAAAA,iBAAA,IAKwFwC,UAAS;AAAA;AAAA1C,eAAA,CAL1G0C,SAAS,8BA9FkE5H,EAAE,CAAAqF,iBAAA;EAAAC,IAAA,EAoGQsC,UAAS;EAAArC,SAAA;EAAAgB,MAAA;IAAAe,eAAA;EAAA;EAAAd,QAAA,GApGnBxG,EAAE,CAAAyG,kBAAA,CAoGgL,CAAC;IAAEC,OAAO,EAAEiB,UAAU;IAAEhB,WAAW,EAAEiB;EAAU,CAAC,CAAC;AAAA;AAEpT;EAAA,QAAApC,SAAA,oBAAAA,SAAA,KAtGiFxF,EAAE,CAAAyF,iBAAA,CAsGQmC,SAAS,EAAc,CAAC;IACvGtC,IAAI,EAAEnC,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+CAA+C;MACzDkB,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEiB,UAAU;QAAEhB,WAAW,EAAEiB;MAAU,CAAC;IAC/D,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEN,eAAe,EAAE,CAAC;MAChChC,IAAI,EAAEhC,KAAK;MACXoC,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMqC,qBAAqB,GAAG,IAAI3E,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4E,yBAAyB,CAAC;EAE5B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACV,KAAK,EAAE;IAChB,IAAI,CAACW,SAAS,GAAGX,KAAK;IACtB,IAAI,IAAI,CAACY,aAAa,EAAE;MACpB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EAEA;EACA,IAAID,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACE,cAAc;EAC9B;EACA,IAAIF,aAAaA,CAACZ,KAAK,EAAE;IACrB,IAAI,CAACc,cAAc,GAAGd,KAAK;IAC3B,IAAI,IAAI,CAACc,cAAc,EAAE;MACrB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACC,mBAAmB,CAACC,WAAW,CAAC,CAAC;IAC1C;EACJ;EAUA1C,WAAWA,CAAA,EAAG;IAAAZ,eAAA,sBAlCA7B,MAAM,CAACE,UAAU,CAAC;IAAA2B,eAAA,oBAWpB,KAAK;IAAAA,eAAA,yBAcA,KAAK;IACtB;IAAAA,eAAA,0BACkB7B,MAAM,CAAC0B,oBAAoB,CAAC;IAC9C;IAAAG,eAAA,kBACU7B,MAAM,CAACG,MAAM,CAAC;IACxB;IAAA0B,eAAA,kBACU7B,MAAM,CAAC0E,qBAAqB,CAAC;IACvC;IAAA7C,eAAA,8BACsB,IAAIX,YAAY,CAAC,CAAC;EACxB;EAChBkE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,mBAAmB,CAACC,WAAW,CAAC,CAAC;EAC1C;EACA;EACAE,QAAQA,CAAA,EAAG;IACP,OAAOC,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAACC,aAAa,CAAC;EAC9D;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,WAAW,CAACC,aAAa;EACzC;EACA;EACAT,aAAaA,CAAA,EAAG;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAW,UAAU,CAAC,MAAM,IAAI,CAACC,OAAO,CAACC,mBAAmB,CAAC,CAAC,CAAC;EACxD;EACA;EACAX,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,mBAAmB,CAACC,WAAW,CAAC,CAAC;IACtC,IAAI,CAACU,OAAO,CAACC,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACZ,mBAAmB,GAAG,IAAI,CAACa,eAAe,CAC1CC,OAAO,CAAC,IAAI,CAACT,WAAW,CAACC,aAAa,EAAE;QAAES,GAAG,EAAE;MAAa,CAAC,CAAC,CAC9DC,SAAS,CAAC,MAAM,IAAI,CAACnB,aAAa,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;EACN;AAGJ;AAACoB,0BAAA,GAtEKxB,yBAAyB;AAAA9C,eAAA,CAAzB8C,yBAAyB,wBAAAyB,mCAAArE,iBAAA;EAAA,YAAAA,iBAAA,IAoEwE4C,0BAAyB;AAAA;AAAA9C,eAAA,CApE1H8C,yBAAyB,8BAhIkDhI,EAAE,CAAAqF,iBAAA;EAAAC,IAAA,EAqMQ0C,0BAAyB;EAAAzC,SAAA;EAAAW,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAsD,wCAAAvJ,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MArMnCH,EAAE,CAAAkH,WAAA,oCAAA9G,GAAA,CAAA6H,QAqMgC,CAAC;IAAA;EAAA;EAAA1B,MAAA;IAAA0B,QAAA;IAAAE,aAAA;EAAA;AAAA;AAEpH;EAAA,QAAA3C,SAAA,oBAAAA,SAAA,KAvMiFxF,EAAE,CAAAyF,iBAAA,CAuMQuC,yBAAyB,EAAc,CAAC;IACvH1C,IAAI,EAAEnC,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CiB,IAAI,EAAE;QACF,OAAO,EAAE,2CAA2C;QACpD,yCAAyC,EAAE;MAC/C;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEqB,QAAQ,EAAE,CAAC;MACnD3C,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAE6E,aAAa,EAAE,CAAC;MAChB7C,IAAI,EAAEhC;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,SAASqF,mBAAmBA,CAACG,OAAO,EAAE;EAClC;EACA;EACA;EACA;EACA,MAAMa,MAAM,GAAGb,OAAO;EACtB,IAAIa,MAAM,CAACC,YAAY,KAAK,IAAI,EAAE;IAC9B,OAAOD,MAAM,CAACE,WAAW;EAC7B;EACA,MAAMC,KAAK,GAAGH,MAAM,CAACI,SAAS,CAAC,IAAI,CAAC;EACpCD,KAAK,CAACE,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC;EAC/CH,KAAK,CAACE,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,6BAA6B,CAAC;EACnEC,QAAQ,CAACC,eAAe,CAACC,WAAW,CAACN,KAAK,CAAC;EAC3C,MAAMD,WAAW,GAAGC,KAAK,CAACD,WAAW;EACrCC,KAAK,CAACO,MAAM,CAAC,CAAC;EACd,OAAOR,WAAW;AACtB;;AAEA;AACA,MAAMS,cAAc,GAAG,yBAAyB;AAChD;AACA,MAAMC,kBAAkB,GAAG,+BAA+B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EAGzB1E,WAAWA,CAAA,EAAG;IAAAZ,eAAA,sBAFA7B,MAAM,CAACE,UAAU,CAAC;IAAA2B,eAAA;IAAAA,eAAA,+BAiBRuF,KAAK,IAAK;MAC9B,MAAMC,SAAS,GAAG,IAAI,CAAC9B,WAAW,CAACC,aAAa,CAAC6B,SAAS;MAC1D,MAAMC,cAAc,GAAGD,SAAS,CAACE,QAAQ,CAACL,kBAAkB,CAAC;MAC7D,IAAIE,KAAK,CAACI,YAAY,KAAK,SAAS,IAAIF,cAAc,EAAE;QACpDD,SAAS,CAACL,MAAM,CAACC,cAAc,EAAEC,kBAAkB,CAAC;MACxD;IACJ,CAAC;IApBG,MAAMO,MAAM,GAAGzH,MAAM,CAACG,MAAM,CAAC;IAC7B,MAAMuH,QAAQ,GAAG1H,MAAM,CAACI,SAAS,CAAC;IAClCqH,MAAM,CAAC3B,iBAAiB,CAAC,MAAM;MAC3B,IAAI,CAAC6B,qBAAqB,GAAGD,QAAQ,CAACE,MAAM,CAAC,IAAI,CAACrC,WAAW,CAACC,aAAa,EAAE,eAAe,EAAE,IAAI,CAACqC,oBAAoB,CAAC;IAC5H,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG;IACP,MAAMT,SAAS,GAAG,IAAI,CAAC9B,WAAW,CAACC,aAAa,CAAC6B,SAAS;IAC1DA,SAAS,CAACL,MAAM,CAACE,kBAAkB,CAAC;IACpCG,SAAS,CAACU,GAAG,CAACd,cAAc,CAAC;EACjC;EACAe,UAAUA,CAAA,EAAG;IACT,IAAI,CAACzC,WAAW,CAACC,aAAa,CAAC6B,SAAS,CAACU,GAAG,CAACb,kBAAkB,CAAC;EACpE;EAQA9B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuC,qBAAqB,CAAC,CAAC;EAChC;AAGJ;AAACM,uBAAA,GA9BKd,sBAAsB;AAAAtF,eAAA,CAAtBsF,sBAAsB,wBAAAe,gCAAAnG,iBAAA;EAAA,YAAAA,iBAAA,IA4B2EoF,uBAAsB;AAAA;AAAAtF,eAAA,CA5BvHsF,sBAAsB,8BAvPqDxK,EAAE,CAAAqF,iBAAA;EAAAC,IAAA,EAoRQkF,uBAAsB;EAAAjF,SAAA;EAAAW,SAAA;AAAA;AAEjH;EAAA,QAAAV,SAAA,oBAAAA,SAAA,KAtRiFxF,EAAE,CAAAyF,iBAAA,CAsRQ+E,sBAAsB,EAAc,CAAC;IACpHlF,IAAI,EAAEnC,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6BAA6B;MACvCiB,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4E,0BAA0B,CAAC;EAM7B1F,WAAWA,CAAA,EAAG;IAAAZ,eAAA,sBALA7B,MAAM,CAACE,UAAU,CAAC;IAAA2B,eAAA,kBACtB7B,MAAM,CAACG,MAAM,CAAC;IACxB;IAAA0B,eAAA,eACO,KAAK;IAAAA,eAAA;EAEI;EAChBuG,eAAeA,CAAA,EAAG;IACd,MAAMC,KAAK,GAAG,IAAI,CAAC9C,WAAW,CAACC,aAAa,CAAC8C,aAAa,CAAC,qBAAqB,CAAC;IACjF,IAAID,KAAK,EAAE;MACP,IAAI,CAAC9C,WAAW,CAACC,aAAa,CAAC6B,SAAS,CAACU,GAAG,CAAC,+BAA+B,CAAC;MAC7E,IAAI,OAAOQ,qBAAqB,KAAK,UAAU,EAAE;QAC7CF,KAAK,CAAC1B,KAAK,CAAC6B,kBAAkB,GAAG,IAAI;QACrC,IAAI,CAAC3C,OAAO,CAACC,iBAAiB,CAAC,MAAM;UACjCyC,qBAAqB,CAAC,MAAOF,KAAK,CAAC1B,KAAK,CAAC6B,kBAAkB,GAAG,EAAG,CAAC;QACtE,CAAC,CAAC;MACN;IACJ,CAAC,MACI;MACD,IAAI,CAACjD,WAAW,CAACC,aAAa,CAAC6B,SAAS,CAACU,GAAG,CAAC,+BAA+B,CAAC;IACjF;EACJ;EACAU,cAAcA,CAACC,UAAU,EAAE;IACvB,IAAI,CAAC,IAAI,CAACC,IAAI,IAAI,CAACD,UAAU,EAAE;MAC3B,IAAI,CAACE,MAAM,CAACpD,aAAa,CAACmB,KAAK,CAACkC,KAAK,GAAG,EAAE;IAC9C,CAAC,MACI;MACD,MAAMC,qBAAqB,GAAG,CAAC;MAC/B,MAAMC,oBAAoB,GAAG,CAAC;MAC9B,IAAI,CAACH,MAAM,CAACpD,aAAa,CAACmB,KAAK,CAACkC,KAAK,GAAG,QAAQH,UAAU,+DAA+DI,qBAAqB,GAAGC,oBAAoB,KAAK;IAC9K;EACJ;AAGJ;AAACC,2BAAA,GAlCKb,0BAA0B;AAAAtG,eAAA,CAA1BsG,0BAA0B,wBAAAc,oCAAAlH,iBAAA;EAAA,YAAAA,iBAAA,IAgCuEoG,2BAA0B;AAAA;AAAAtG,eAAA,CAhC3HsG,0BAA0B,8BAtSiDxL,EAAE,CAAAuM,iBAAA;EAAAjH,IAAA,EAuUQkG,2BAA0B;EAAAjG,SAAA;EAAAiH,SAAA,WAAAC,kCAAAtM,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAvUpCH,EAAE,CAAA0M,WAAA,CAAAC,GAAA;IAAA;IAAA,IAAAxM,EAAA;MAAA,IAAAyM,EAAA;MAAF5M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAA6L,MAAA,GAAAW,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAA7G,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAA4G,yCAAA7M,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFH,EAAE,CAAAkH,WAAA,iCAAA9G,GAAA,CAAA4L,IAuUiC,CAAC;IAAA;EAAA;EAAAzF,MAAA;IAAAyF,IAAA;EAAA;EAAAiB,KAAA,EAAAC,GAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,qCAAAtN,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAvUpCH,EAAE,CAAA0N,eAAA;MAAF1N,EAAE,CAAAC,SAAA,YAuUid,CAAC;MAvUpdD,EAAE,CAAAK,cAAA,eAuUwhB,CAAC;MAvU3hBL,EAAE,CAAAM,YAAA,EAuUqjB,CAAC;MAvUxjBN,EAAE,CAAAS,YAAA,CAuU6jB,CAAC;MAvUhkBT,EAAE,CAAAC,SAAA,YAuUsoB,CAAC;IAAA;EAAA;EAAA0N,aAAA;EAAAC,eAAA;AAAA;AAE1tB;EAAA,QAAApI,SAAA,oBAAAA,SAAA,KAzUiFxF,EAAE,CAAAyF,iBAAA,CAyUQ+F,0BAA0B,EAAc,CAAC;IACxHlG,IAAI,EAAE5B,SAAS;IACfgC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iCAAiC;MAAEiB,IAAI,EAAE;QAChD,OAAO,EAAE,qBAAqB;QAC9B;QACA;QACA,sCAAsC,EAAE;MAC5C,CAAC;MAAEgH,eAAe,EAAEjK,uBAAuB,CAACkK,MAAM;MAAEF,aAAa,EAAE/J,iBAAiB,CAACkK,IAAI;MAAEN,QAAQ,EAAE;IAAgQ,CAAC;EAClX,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAExB,IAAI,EAAE,CAAC;MAC/C1G,IAAI,EAAEhC,KAAK;MACXoC,IAAI,EAAE,CAAC,gCAAgC;IAC3C,CAAC,CAAC;IAAEuG,MAAM,EAAE,CAAC;MACT3G,IAAI,EAAEzB,SAAS;MACf6B,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMqI,mBAAmB,CAAC;EAAAjI,YAAA;IACtB;IAAAZ,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;EAAA;AAQJ;AAAC8I,oBAAA,GAlDKD,mBAAmB;AAAA7I,eAAA,CAAnB6I,mBAAmB,wBAAAE,6BAAA7I,iBAAA;EAAA,YAAAA,iBAAA,IAgD8E2I,oBAAmB;AAAA;AAAA7I,eAAA,CAhDpH6I,mBAAmB,8BA1VwD/N,EAAE,CAAAqF,iBAAA;EAAAC,IAAA,EA2YQyI;AAAmB;AAE9G;EAAA,QAAAvI,SAAA,oBAAAA,SAAA,KA7YiFxF,EAAE,CAAAyF,iBAAA,CA6YQsI,mBAAmB,EAAc,CAAC;IACjHzI,IAAI,EAAEnC;EACV,CAAC,CAAC;AAAA;;AAEV;AACA,SAAS+K,uCAAuCA,CAAA,EAAG;EAC/C,OAAOC,KAAK,CAAC,8DAA8D,CAAC;AAChF;AACA;AACA,SAASC,kCAAkCA,CAACjH,KAAK,EAAE;EAC/C,OAAOgH,KAAK,CAAC,2CAA2ChH,KAAK,KAAK,CAAC;AACvE;AACA;AACA,SAASkH,kCAAkCA,CAAA,EAAG;EAC1C,OAAOF,KAAK,CAAC,oDAAoD,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,cAAc,GAAG,IAAIlL,cAAc,CAAC,cAAc,CAAC;AACzD;AACA;AACA;AACA;AACA,MAAMmL,8BAA8B,GAAG,IAAInL,cAAc,CAAC,gCAAgC,CAAC;AAC3F;AACA,MAAMoL,kBAAkB,GAAG,MAAM;AACjC;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,MAAM;AAClC;AACA,MAAMC,wBAAwB,GAAG,OAAO;AACxC;AACA;AACA;AACA;AACA;AACA,MAAMC,uCAAuC,GAAG,kBAAkB;AAClE;AACA,MAAMC,YAAY,CAAC;EAyBf;EACA,IAAItN,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACuN,mBAAmB;EACnC;EACA,IAAIvN,kBAAkBA,CAACiG,KAAK,EAAE;IAC1B,IAAI,CAACsH,mBAAmB,GAAG7L,qBAAqB,CAACuE,KAAK,CAAC;EAC3D;EAUA;EACA,IAAIuH,UAAUA,CAAA,EAAG;IAAA,IAAAC,eAAA;IACb,OAAO,IAAI,CAACC,WAAW,MAAAD,eAAA,GAAI,IAAI,CAACE,SAAS,cAAAF,eAAA,uBAAdA,eAAA,CAAgBD,UAAU,KAAIL,mBAAmB;EAChF;EACA,IAAIK,UAAUA,CAACvH,KAAK,EAAE;IAClB,IAAIA,KAAK,KAAK,IAAI,CAACyH,WAAW,EAAE;MAC5B,IAAI,CAACA,WAAW,GAAGzH,KAAK;MACxB;MACA;MACA;MACA;MACA,IAAI,CAAC2H,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EAEA;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAAC7H,KAAK,EAAE;IAAA,IAAA+H,gBAAA;IAClB,MAAMC,QAAQ,GAAG,IAAI,CAACF,WAAW;IACjC,MAAMG,aAAa,GAAGjI,KAAK,MAAA+H,gBAAA,GAAI,IAAI,CAACL,SAAS,cAAAK,gBAAA,uBAAdA,gBAAA,CAAgBF,UAAU,KAAIZ,kBAAkB;IAC/E,IAAI,OAAOhJ,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAIgK,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,SAAS,EAAE;QACzD,MAAM,IAAIrB,KAAK,CAAC,qCAAqCqB,aAAa,0CAA0C,CAAC;MACjH;IACJ;IACA,IAAI,CAACH,WAAW,GAAGG,aAAa;IAChC,IAAI,IAAI,CAACH,WAAW,KAAK,SAAS,IAAI,IAAI,CAACA,WAAW,KAAKE,QAAQ,EAAE;MACjE;MACA;MACA;MACA,IAAI,CAACE,8BAA8B,GAAG,IAAI;IAC9C;EACJ;EAEA;AACJ;AACA;AACA;AACA;EACI,IAAIC,eAAeA,CAAA,EAAG;IAAA,IAAAC,gBAAA;IAClB,OAAO,IAAI,CAACC,gBAAgB,MAAAD,gBAAA,GAAI,IAAI,CAACV,SAAS,cAAAU,gBAAA,uBAAdA,gBAAA,CAAgBD,eAAe,KAAIhB,wBAAwB;EAC/F;EACA,IAAIgB,eAAeA,CAACnI,KAAK,EAAE;IAAA,IAAAsI,gBAAA;IACvB,IAAI,CAACD,gBAAgB,GAAGrI,KAAK,MAAAsI,gBAAA,GAAI,IAAI,CAACZ,SAAS,cAAAY,gBAAA,uBAAdA,gBAAA,CAAgBH,eAAe,KAAIhB,wBAAwB;EAChG;EAEA;EACA,IAAI7L,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACiN,UAAU;EAC1B;EACA,IAAIjN,SAASA,CAAC0E,KAAK,EAAE;IACjB,IAAI,CAACuI,UAAU,GAAGvI,KAAK;IACvB,IAAI,CAACwI,aAAa,CAAC,CAAC;EACxB;EAUA;EACA,IAAI9O,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC+O,yBAAyB,IAAI,IAAI,CAACC,iBAAiB;EACnE;EACA,IAAIhP,QAAQA,CAACsG,KAAK,EAAE;IAChB,IAAI,CAACyI,yBAAyB,GAAGzI,KAAK;EAC1C;EAWAzB,WAAWA,CAAA,EAAG;IAAAZ,eAAA,sBA1HA7B,MAAM,CAACE,UAAU,CAAC;IAAA2B,eAAA,6BACX7B,MAAM,CAACS,iBAAiB,CAAC;IAAAoB,eAAA,eACvC7B,MAAM,CAACN,cAAc,CAAC;IAAAmC,eAAA,oBACjB7B,MAAM,CAACJ,QAAQ,CAAC;IAAAiC,eAAA,uBACb7B,MAAM,CAACiB,YAAY,CAAC;IAAAY,eAAA,kBACzB7B,MAAM,CAACG,MAAM,CAAC;IAAA0B,eAAA,oBACZ7B,MAAM,CAACU,QAAQ,CAAC;IAAAmB,eAAA,oBAChB7B,MAAM,CAACkL,8BAA8B,EAAE;MAC/C2B,QAAQ,EAAE;IACd,CAAC,CAAC;IAAAhL,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,sBAcYlB,YAAY,CAACgB,QAAQ,CAAC;IAAAE,eAAA,8BAQd,KAAK;IAC3B;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA,gBAOQ,SAAS;IAAAA,eAAA;IAAAA,eAAA,sBAoCHsJ,kBAAkB;IAAAtJ,eAAA,2BAYb,IAAI;IAAAA,eAAA,qBASV,EAAE;IAAAA,eAAA,yBACE,KAAK;IAAAA,eAAA,yBACL,KAAK;IAAAA,eAAA,yBACL,KAAK;IAAAA,eAAA,yBACL,KAAK;IACtB;IAAAA,eAAA,mBACW,IAAI,CAACiL,YAAY,CAACpK,KAAK,CAAC,2BAA2B,CAAC;IAC/D;IAAAb,eAAA,uBACe,IAAI,CAACiL,YAAY,CAACpK,KAAK,CAAC,eAAe,CAAC;IAAAb,eAAA,qBAQ1C,IAAIV,OAAO,CAAC,CAAC;IAAAU,eAAA,qBACb,IAAI;IAAAA,eAAA;IAAAA,eAAA,yCAEgB,KAAK;IAAAA,eAAA,2BACnB,IAAI;IAAAA,eAAA,sCACO,IAAI;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAsElC;AACJ;AACA;IAFIA,eAAA,qBAGahB,QAAQ,CAAC,MAAO,IAAI,CAACzC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACV,QAAQ,GAAG,IAAK,CAAC;IAAAmE,eAAA,4BA0J1DhB,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAACkM,WAAW,CAAC,CAAC,CAAC;IA7NpD,MAAMC,QAAQ,GAAG,IAAI,CAACpB,SAAS;IAC/B,IAAIoB,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACjB,UAAU,EAAE;QACrB,IAAI,CAACA,UAAU,GAAGiB,QAAQ,CAACjB,UAAU;MACzC;MACA,IAAI,CAACP,mBAAmB,GAAGyB,OAAO,CAACD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE/O,kBAAkB,CAAC;MAChE,IAAI+O,QAAQ,CAACE,KAAK,EAAE;QAChB,IAAI,CAACA,KAAK,GAAGF,QAAQ,CAACE,KAAK;MAC/B;IACJ;IACA,IAAI,CAACC,mBAAmB,GAAGnN,MAAM,CAACY,qBAAqB,EAAE;MAAEiM,QAAQ,EAAE;IAAK,CAAC,CAAC,KAAK,gBAAgB;EACrG;EACAzE,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAACgF,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,CAACD,mBAAmB,EAAE;MAC3B,IAAI,CAACtH,OAAO,CAACC,iBAAiB,CAAC,MAAM;QACjC;QACAJ,UAAU,CAAC,MAAM;UACb,IAAI,CAACH,WAAW,CAACC,aAAa,CAAC6B,SAAS,CAACU,GAAG,CAAC,mCAAmC,CAAC;QACrF,CAAC,EAAE,GAAG,CAAC;MACX,CAAC,CAAC;IACN;IACA;IACA;IACA,IAAI,CAAC8D,kBAAkB,CAACwB,aAAa,CAAC,CAAC;EAC3C;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,0CAA0C,CAAC,CAAC;EACrD;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACJ,uBAAuB,CAAC,CAAC;IAC9B;IACA;IACA,IAAI,IAAI,CAAC3P,QAAQ,KAAK,IAAI,CAACgQ,gBAAgB,EAAE;MACzC,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACD,gBAAgB,CAAC;MAC9C;MACA,IAAI,IAAI,CAAChQ,QAAQ,CAACkQ,SAAS,IAAI,IAAI,CAAClQ,QAAQ,CAACkQ,SAAS,CAACC,OAAO,EAAE;QAC5D,IAAI,CAACC,2BAA2B,GAAG,IAAI,CAACpQ,QAAQ,CAACkQ,SAAS,CAACC,OAAO,CAACE,SAAS;MAChF;MACA,IAAI,CAACL,gBAAgB,GAAG,IAAI,CAAChQ,QAAQ;IACzC;IACA;IACA,IAAI,IAAI,CAACA,QAAQ,CAACkQ,SAAS,IAAI,IAAI,CAAClQ,QAAQ,CAACkQ,SAAS,CAACC,OAAO,EAAE;MAC5D;MACA,MAAMG,WAAW,GAAG,IAAI,CAACtQ,QAAQ,CAACkQ,SAAS,CAACC,OAAO,CAACE,SAAS;MAC7D;MACA;MACA,IAAIC,WAAW,KAAK,IAAI,CAACF,2BAA2B,EAAE;QAClD,IAAI,CAACnC,kBAAkB,CAACC,YAAY,CAAC,CAAC;MAC1C;IACJ;EACJ;EACA1G,WAAWA,CAAA,EAAG;IAAA,IAAA+I,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IACV,CAAAF,mBAAA,OAAI,CAACG,aAAa,cAAAH,mBAAA,eAAlBA,mBAAA,CAAoBhJ,WAAW,CAAC,CAAC;IACjC,CAAAiJ,mBAAA,OAAI,CAACG,aAAa,cAAAH,mBAAA,eAAlBA,mBAAA,CAAoBjJ,WAAW,CAAC,CAAC;IACjC,CAAAkJ,qBAAA,OAAI,CAACG,mBAAmB,cAAAH,qBAAA,eAAxBA,qBAAA,CAA0BlJ,WAAW,CAAC,CAAC;IACvC,IAAI,CAACsJ,UAAU,CAACC,IAAI,CAAC,CAAC;IACtB,IAAI,CAACD,UAAU,CAACE,QAAQ,CAAC,CAAC;EAC9B;EAKA;AACJ;AACA;AACA;EACIC,yBAAyBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,UAAU,IAAI,IAAI,CAACtJ,WAAW;EAC9C;EACA;EACAuJ,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC1Q,iBAAiB,CAAC,CAAC,EAAE;MAC1B,IAAI,CAACqN,UAAU,GAAG,QAAQ;IAC9B;EACJ;EACA;EACAoC,kBAAkBA,CAACkB,eAAe,EAAE;IAAA,IAAAC,oBAAA,EAAAC,sBAAA,EAAAC,oBAAA;IAChC,MAAMnB,OAAO,GAAG,IAAI,CAACnQ,QAAQ;IAC7B,MAAMuR,WAAW,GAAG,0BAA0B;IAC9C,IAAIJ,eAAe,EAAE;MACjB,IAAI,CAACxJ,WAAW,CAACC,aAAa,CAAC6B,SAAS,CAACL,MAAM,CAACmI,WAAW,GAAGJ,eAAe,CAACK,WAAW,CAAC;IAC9F;IACA,IAAIrB,OAAO,CAACqB,WAAW,EAAE;MACrB,IAAI,CAAC7J,WAAW,CAACC,aAAa,CAAC6B,SAAS,CAACU,GAAG,CAACoH,WAAW,GAAGpB,OAAO,CAACqB,WAAW,CAAC;IACnF;IACA;IACA,CAAAJ,oBAAA,OAAI,CAACV,aAAa,cAAAU,oBAAA,eAAlBA,oBAAA,CAAoB7J,WAAW,CAAC,CAAC;IACjC,IAAI,CAACmJ,aAAa,GAAGP,OAAO,CAACsB,YAAY,CAACnJ,SAAS,CAAC,MAAM;MACtD,IAAI,CAACkH,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACvB,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF;IACA,CAAAmD,sBAAA,OAAI,CAACT,mBAAmB,cAAAS,sBAAA,eAAxBA,sBAAA,CAA0B9J,WAAW,CAAC,CAAC;IACvC,IAAI,CAACqJ,mBAAmB,GAAGT,OAAO,CAACsB,YAAY,CAC1CC,IAAI,CAACjO,SAAS,CAAC,CAACkO,SAAS,EAAEA,SAAS,CAAC,CAAC,EAAEjO,GAAG,CAAC,MAAM,CAACyM,OAAO,CAACyB,UAAU,EAAEzB,OAAO,CAAC0B,mBAAmB,CAAC,CAAC,EAAElO,QAAQ,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC,CAAC,CAACkO,cAAc,EAAEC,eAAe,CAAC,EAAE,CAACC,iBAAiB,EAAEC,kBAAkB,CAAC,CAAC,KAAK;MAC5M,OAAOH,cAAc,KAAKE,iBAAiB,IAAID,eAAe,KAAKE,kBAAkB;IACzF,CAAC,CAAC,CAAC,CACE3J,SAAS,CAAC,MAAM,IAAI,CAAC4J,mBAAmB,CAAC,CAAC,CAAC;IAChD,CAAAZ,oBAAA,OAAI,CAACX,aAAa,cAAAW,oBAAA,eAAlBA,oBAAA,CAAoB/J,WAAW,CAAC,CAAC;IACjC;IACA,IAAI4I,OAAO,CAACD,SAAS,IAAIC,OAAO,CAACD,SAAS,CAACiC,YAAY,EAAE;MACrD,IAAI,CAACxB,aAAa,GAAGR,OAAO,CAACD,SAAS,CAACiC,YAAY,CAC9CT,IAAI,CAAC7N,SAAS,CAAC,IAAI,CAACgN,UAAU,CAAC,CAAC,CAChCvI,SAAS,CAAC,MAAM,IAAI,CAAC2F,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAAC;IAChE;EACJ;EACAkE,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC,IAAI,CAACC,eAAe,CAACC,IAAI,CAACC,CAAC,IAAI,CAACA,CAAC,CAACjM,OAAO,CAAC;IAClE,IAAI,CAACkM,cAAc,GAAG,CAAC,CAAC,IAAI,CAACH,eAAe,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjM,OAAO,CAAC;IACjE,IAAI,CAACmM,cAAc,GAAG,CAAC,CAAC,IAAI,CAACC,eAAe,CAACJ,IAAI,CAACK,CAAC,IAAI,CAACA,CAAC,CAACrM,OAAO,CAAC;IAClE,IAAI,CAACsM,cAAc,GAAG,CAAC,CAAC,IAAI,CAACF,eAAe,CAACJ,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACrM,OAAO,CAAC;EACrE;EACA;EACAsJ,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAACuC,0BAA0B,CAAC,CAAC;IACjC;IACA;IACA;IACA5O,KAAK,CAAC,IAAI,CAAC8O,eAAe,CAACQ,OAAO,EAAE,IAAI,CAACH,eAAe,CAACG,OAAO,CAAC,CAACxK,SAAS,CAAC,MAAM;MAC9E,IAAI,CAAC8J,0BAA0B,CAAC,CAAC;MACjC,IAAI,CAACnE,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI0B,oBAAoBA,CAAA,EAAG;IACnB;IACA,IAAI,CAACmD,aAAa,CAACD,OAAO,CAACxK,SAAS,CAAC,MAAM;MACvC,IAAI,CAACwG,aAAa,CAAC,CAAC;MACpB,IAAI,CAACb,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF;IACA,IAAI,CAAC8E,cAAc,CAACF,OAAO,CAACxK,SAAS,CAAC,MAAM;MACxC,IAAI,CAAC4J,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACjE,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF;IACA,IAAI,CAAC+E,cAAc,CAAC,CAAC;IACrB,IAAI,CAACf,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACAvC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAAC3P,QAAQ,KAAK,OAAOuE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAM6I,kCAAkC,CAAC,CAAC;IAC9C;EACJ;EACAoC,iBAAiBA,CAAA,EAAG;IAAA,IAAA0D,gBAAA;IAChB;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAClT,QAAQ,CAACmT,OAAO,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MAAA,IAAAC,iBAAA;MAC3C,IAAI,CAACD,UAAU,GAAG,IAAI;MACtB,CAAAC,iBAAA,OAAI,CAACC,WAAW,cAAAD,iBAAA,eAAhBA,iBAAA,CAAkBnJ,QAAQ,CAAC,CAAC;IAChC,CAAC,MACI,IAAI,CAAC,IAAI,CAAClK,QAAQ,CAACmT,OAAO,KAAK,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,KAAK,IAAI,CAAC,EAAE;MAAA,IAAAG,kBAAA;MAC9E,IAAI,CAACH,UAAU,GAAG,KAAK;MACvB,CAAAG,kBAAA,OAAI,CAACD,WAAW,cAAAC,kBAAA,eAAhBA,kBAAA,CAAkBnJ,UAAU,CAAC,CAAC;IAClC;IACA,CAAA8I,gBAAA,OAAI,CAACjC,UAAU,cAAAiC,gBAAA,eAAfA,gBAAA,CAAiBtL,aAAa,CAAC6B,SAAS,CAAC+J,MAAM,CAAC,yBAAyB,EAAE,IAAI,CAACxT,QAAQ,CAACmT,OAAO,CAAC;EACrG;EACA;AACJ;AACA;AACA;AACA;AACA;EACIrD,0CAA0CA,CAAA,EAAG;IACzC;IACA;IACA,IAAI,CAACwC,eAAe,CAACQ,OAAO,CAACxK,SAAS,CAAC,MAAO,IAAI,CAACkG,8BAA8B,GAAG,IAAK,CAAC;IAC1F;IACA;IACAtL,WAAW,CAAC,MAAM;MACd,IAAI,IAAI,CAACsL,8BAA8B,EAAE;QACrC,IAAI,CAACA,8BAA8B,GAAG,KAAK;QAC3C,IAAI,CAACiF,yBAAyB,CAAC,CAAC;MACpC;IACJ,CAAC,EAAE;MACCC,QAAQ,EAAE,IAAI,CAACC;IACnB,CAAC,CAAC;IACF,IAAI,CAACC,IAAI,CAACC,MAAM,CACXnC,IAAI,CAAC7N,SAAS,CAAC,IAAI,CAACgN,UAAU,CAAC,CAAC,CAChCvI,SAAS,CAAC,MAAO,IAAI,CAACkG,8BAA8B,GAAG,IAAK,CAAC;EACtE;EACA;EACAsF,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACjG,UAAU,KAAK,QAAQ;EACvC;EACAhO,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACsO,UAAU,KAAK,SAAS;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIpN,uBAAuBA,CAAA,EAAG;IACtB,OAAO,CAAC,IAAI,CAACgT,SAAS,CAACC,SAAS,IAAI,IAAI,CAAC1B,eAAe,CAAC2B,MAAM,IAAI,CAAC,IAAI,CAACrU,iBAAiB,CAAC,CAAC;EAChG;EAEAA,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACY,iBAAiB,CAAC,CAAC,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACR,QAAQ,CAACkU,gBAAgB,IAAI,IAAI,CAACJ,kBAAkB,CAAC,CAAC;EACtE;EACA;AACJ;AACA;AACA;EACIK,cAAcA,CAACC,IAAI,EAAE;IACjB,MAAMjE,OAAO,GAAG,IAAI,CAACnQ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACkQ,SAAS,GAAG,IAAI;IAC9D,OAAOC,OAAO,IAAIA,OAAO,CAACiE,IAAI,CAAC;EACnC;EACA;EACAC,wBAAwBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACrB,cAAc,IAAI,IAAI,CAACA,cAAc,CAACiB,MAAM,GAAG,CAAC,IAAI,IAAI,CAACjU,QAAQ,CAAC4R,UAAU,GAClF,OAAO,GACP,MAAM;EAChB;EACA;EACA5J,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACsM,yBAAyB,CAAC,CAAC;EACpC;EACA;EACAA,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAAC,IAAI,CAACzU,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC0U,cAAc,IAAI,CAAC,IAAI,CAAC3U,iBAAiB,CAAC,CAAC,EAAE;MAAA,IAAA4U,qBAAA;MAC1E,CAAAA,qBAAA,OAAI,CAACC,eAAe,cAAAD,qBAAA,eAApBA,qBAAA,CAAsB3J,cAAc,CAAC,CAAC,CAAC;IAC3C,CAAC,MACI;MAAA,IAAA6J,sBAAA;MACD,CAAAA,sBAAA,OAAI,CAACD,eAAe,cAAAC,sBAAA,eAApBA,sBAAA,CAAsB7J,cAAc,CAAC,IAAI,CAAC0J,cAAc,CAAC9M,QAAQ,CAAC,CAAC,CAAC;IACxE;EACJ;EACA;EACAqH,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACmE,cAAc,CAAC,CAAC;IACrB,IAAI,CAACf,mBAAmB,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIe,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACF,aAAa,KAAK,OAAOxO,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACvE,IAAIoQ,SAAS;MACb,IAAIC,OAAO;MACX,IAAI,CAAC7B,aAAa,CAAC8B,OAAO,CAAEC,IAAI,IAAK;QACjC,IAAIA,IAAI,CAAC5O,KAAK,KAAK,OAAO,EAAE;UACxB,IAAIyO,SAAS,IAAI,IAAI,CAAC/S,SAAS,EAAE;YAC7B,MAAMuL,kCAAkC,CAAC,OAAO,CAAC;UACrD;UACAwH,SAAS,GAAGG,IAAI;QACpB,CAAC,MACI,IAAIA,IAAI,CAAC5O,KAAK,KAAK,KAAK,EAAE;UAC3B,IAAI0O,OAAO,EAAE;YACT,MAAMzH,kCAAkC,CAAC,KAAK,CAAC;UACnD;UACAyH,OAAO,GAAGE,IAAI;QAClB;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;EACI5C,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAClS,QAAQ,EAAE;MACf,IAAI+U,GAAG,GAAG,EAAE;MACZ;MACA,IAAI,IAAI,CAAC/U,QAAQ,CAAC6R,mBAAmB,IACjC,OAAO,IAAI,CAAC7R,QAAQ,CAAC6R,mBAAmB,KAAK,QAAQ,EAAE;QACvDkD,GAAG,CAACC,IAAI,CAAC,GAAG,IAAI,CAAChV,QAAQ,CAAC6R,mBAAmB,CAACoD,KAAK,CAAC,GAAG,CAAC,CAAC;MAC7D;MACA,IAAI,IAAI,CAACZ,wBAAwB,CAAC,CAAC,KAAK,MAAM,EAAE;QAC5C,MAAMM,SAAS,GAAG,IAAI,CAAC5B,aAAa,GAC9B,IAAI,CAACA,aAAa,CAACR,IAAI,CAACuC,IAAI,IAAIA,IAAI,CAAC5O,KAAK,KAAK,OAAO,CAAC,GACvD,IAAI;QACV,MAAM0O,OAAO,GAAG,IAAI,CAAC7B,aAAa,GAC5B,IAAI,CAACA,aAAa,CAACR,IAAI,CAACuC,IAAI,IAAIA,IAAI,CAAC5O,KAAK,KAAK,KAAK,CAAC,GACrD,IAAI;QACV,IAAIyO,SAAS,EAAE;UACXI,GAAG,CAACC,IAAI,CAACL,SAAS,CAACzU,EAAE,CAAC;QAC1B,CAAC,MACI,IAAI,IAAI,CAAC2O,UAAU,EAAE;UACtBkG,GAAG,CAACC,IAAI,CAAC,IAAI,CAACtT,YAAY,CAAC;QAC/B;QACA,IAAIkT,OAAO,EAAE;UACTG,GAAG,CAACC,IAAI,CAACJ,OAAO,CAAC1U,EAAE,CAAC;QACxB;MACJ,CAAC,MACI,IAAI,IAAI,CAAC8S,cAAc,EAAE;QAC1B+B,GAAG,CAACC,IAAI,CAAC,GAAG,IAAI,CAAChC,cAAc,CAACtP,GAAG,CAACwR,KAAK,IAAIA,KAAK,CAAChV,EAAE,CAAC,CAAC;MAC3D;MACA,IAAI,CAACF,QAAQ,CAACmV,iBAAiB,CAACJ,GAAG,CAAC;IACxC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItB,yBAAyBA,CAAA,EAAG;IAAA,IAAA2B,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACxB,IAAI,CAAC,IAAI,CAAC9V,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC0U,cAAc,EAAE;MAC7C;IACJ;IACA,MAAMqB,aAAa,GAAG,IAAI,CAACrB,cAAc,CAAC1M,OAAO;IACjD;IACA;IACA,IAAI,EAAE,IAAI,CAACgO,oBAAoB,IAAI,IAAI,CAACC,oBAAoB,CAAC,EAAE;MAC3DF,aAAa,CAAC7M,KAAK,CAACgN,SAAS,GAAG,EAAE;MAClC;IACJ;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAE;MAC1B,IAAI,CAACxH,8BAA8B,GAAG,IAAI;MAC1C;IACJ;IACA,MAAMyH,mBAAmB,IAAAb,qBAAA,GAAG,IAAI,CAACS,oBAAoB,cAAAT,qBAAA,uBAAzBA,qBAAA,CAA2BxN,aAAa;IACpE,MAAMsO,mBAAmB,IAAAb,qBAAA,GAAG,IAAI,CAACS,oBAAoB,cAAAT,qBAAA,uBAAzBA,qBAAA,CAA2BzN,aAAa;IACpE,MAAMuO,mBAAmB,IAAAb,qBAAA,GAAG,IAAI,CAACc,oBAAoB,cAAAd,qBAAA,uBAAzBA,qBAAA,CAA2B1N,aAAa;IACpE,MAAMyO,mBAAmB,IAAAd,qBAAA,GAAG,IAAI,CAACe,oBAAoB,cAAAf,qBAAA,uBAAzBA,qBAAA,CAA2B3N,aAAa;IACpE,MAAM2O,wBAAwB,IAAAf,qBAAA,GAAGS,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEO,qBAAqB,CAAC,CAAC,CAACvL,KAAK,cAAAuK,qBAAA,cAAAA,qBAAA,GAAI,CAAC;IACxF,MAAMiB,wBAAwB,IAAAhB,qBAAA,GAAGS,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEM,qBAAqB,CAAC,CAAC,CAACvL,KAAK,cAAAwK,qBAAA,cAAAA,qBAAA,GAAI,CAAC;IACxF,MAAMiB,wBAAwB,IAAAhB,qBAAA,GAAGS,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEK,qBAAqB,CAAC,CAAC,CAACvL,KAAK,cAAAyK,qBAAA,cAAAA,qBAAA,GAAI,CAAC;IACxF,MAAMiB,wBAAwB,IAAAhB,qBAAA,GAAGU,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEG,qBAAqB,CAAC,CAAC,CAACvL,KAAK,cAAA0K,qBAAA,cAAAA,qBAAA,GAAI,CAAC;IACxF;IACA;IACA,MAAMiB,MAAM,GAAG,IAAI,CAAChD,IAAI,CAACtN,KAAK,KAAK,KAAK,GAAG,IAAI,GAAG,GAAG;IACrD,MAAMuQ,WAAW,GAAG,GAAGN,wBAAwB,GAAGE,wBAAwB,IAAI;IAC9E,MAAMK,WAAW,GAAG,+CAA+C;IACnE,MAAMC,qBAAqB,GAAG,QAAQH,MAAM,OAAOC,WAAW,MAAMC,WAAW,IAAI;IACnF;IACA;IACA;IACAlB,aAAa,CAAC7M,KAAK,CAACgN,SAAS,GAAG;AACxC;AACA,UAAUrI,uCAAuC,eAAeqJ,qBAAqB;AACrF,MAAM;IACE;IACA,MAAMC,oBAAoB,GAAGT,wBAAwB,GACjDE,wBAAwB,GACxBC,wBAAwB,GACxBC,wBAAwB;IAC5B,IAAI,CAAChP,WAAW,CAACC,aAAa,CAACmB,KAAK,CAACC,WAAW,CAAC,kCAAkC,EAAE,eAAegO,oBAAoB,KAAK,CAAC;EAClI;EACA;EACAhB,gBAAgBA,CAAA,EAAG;IACf,MAAMnO,OAAO,GAAG,IAAI,CAACF,WAAW,CAACC,aAAa;IAC9C,IAAIC,OAAO,CAACoP,WAAW,EAAE;MACrB,MAAMC,QAAQ,GAAGrP,OAAO,CAACoP,WAAW,CAAC,CAAC;MACtC;MACA;MACA,OAAOC,QAAQ,IAAIA,QAAQ,KAAKrP,OAAO;IAC3C;IACA;IACA;IACA,OAAOoB,QAAQ,CAACC,eAAe,CAACS,QAAQ,CAAC9B,OAAO,CAAC;EACrD;AAMJ;AAACsP,aAAA,GArgBKxJ,YAAY;AAAA1J,eAAA,CAAZ0J,YAAY,wBAAAyJ,sBAAAjT,iBAAA;EAAA,YAAAA,iBAAA,IAggBqFwJ,aAAY;AAAA;AAAA1J,eAAA,CAhgB7G0J,YAAY,8BAzb+D5O,EAAE,CAAAuM,iBAAA;EAAAjH,IAAA,EA07BQsJ,aAAY;EAAArJ,SAAA;EAAA+S,cAAA,WAAAC,6BAAApY,EAAA,EAAAC,GAAA,EAAAoY,QAAA;IAAA,IAAArY,EAAA;MA17BtBH,EAAE,CAAAyY,oBAAA,CAAAD,QAAA,EAAApY,GAAA,CAAAgQ,WAAA,EA67BPpL,QAAQ;MA77BHhF,EAAE,CAAA0Y,cAAA,CAAAF,QAAA,EA67BqGzK,mBAAmB;MA77B1H/N,EAAE,CAAA0Y,cAAA,CAAAF,QAAA,EA67B6LpR,UAAU;MA77BzMpH,EAAE,CAAA0Y,cAAA,CAAAF,QAAA,EA67B4Q7Q,UAAU;MA77BxR3H,EAAE,CAAA0Y,cAAA,CAAAF,QAAA,EA67B0V5S,SAAS;MA77BrW5F,EAAE,CAAA0Y,cAAA,CAAAF,QAAA,EA67Bsa1R,OAAO;IAAA;IAAA,IAAA3G,EAAA;MA77B/aH,EAAE,CAAA2Y,cAAA;MAAA,IAAA/L,EAAA;MAAF5M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAA6P,iBAAA,GAAArD,EAAA,CAAAG,KAAA;MAAF/M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAAmT,eAAA,GAAA3G,EAAA;MAAF5M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAAwT,eAAA,GAAAhH,EAAA;MAAF5M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAA6T,cAAA,GAAArH,EAAA;MAAF5M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAA4T,aAAA,GAAApH,EAAA;IAAA;EAAA;EAAAJ,SAAA,WAAAoM,oBAAAzY,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFH,EAAE,CAAA0M,WAAA,CAAAmM,GAAA;MAAF7Y,EAAE,CAAA0M,WAAA,CAAAoM,GAAA;MAAF9Y,EAAE,CAAA0M,WAAA,CAAAqM,GAAA;MAAF/Y,EAAE,CAAA0M,WAAA,CAAAsM,GAAA;MAAFhZ,EAAE,CAAA0M,WAAA,CAAAuM,GAAA;MAAFjZ,EAAE,CAAA0M,WAAA,CA67B+hC1E,yBAAyB;MA77B1jChI,EAAE,CAAA0M,WAAA,CA67B0oClB,0BAA0B;MA77BtqCxL,EAAE,CAAA0M,WAAA,CA67BkvClC,sBAAsB;IAAA;IAAA,IAAArK,EAAA;MAAA,IAAAyM,EAAA;MA77B1wC5M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAA8R,UAAA,GAAAtF,EAAA,CAAAG,KAAA;MAAF/M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAA0W,oBAAA,GAAAlK,EAAA,CAAAG,KAAA;MAAF/M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAA2W,oBAAA,GAAAnK,EAAA,CAAAG,KAAA;MAAF/M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAAiX,oBAAA,GAAAzK,EAAA,CAAAG,KAAA;MAAF/M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAAmX,oBAAA,GAAA3K,EAAA,CAAAG,KAAA;MAAF/M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAAoV,cAAA,GAAA5I,EAAA,CAAAG,KAAA;MAAF/M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAAsV,eAAA,GAAA9I,EAAA,CAAAG,KAAA;MAAF/M,EAAE,CAAA6M,cAAA,CAAAD,EAAA,GAAF5M,EAAE,CAAA8M,WAAA,QAAA1M,GAAA,CAAAmU,WAAA,GAAA3H,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAA7G,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAA8S,2BAAA/Y,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFH,EAAE,CAAAkH,WAAA,0CA07BQ9G,GAAA,CAAA2U,kBAAA,CAAmB,CAAR,CAAC,uCAAA3U,GAAA,CAAAkT,cAAD,CAAC,uCAAAlT,GAAA,CAAAuT,cAAD,CAAC,2BAAAvT,GAAA,CAAAa,QAAA,CAAA4R,UAAD,CAAC,4BAAAzS,GAAA,CAAAa,QAAA,CAAAkY,QAAD,CAAC,8BAAA/Y,GAAA,CAAAa,QAAA,CAAAmY,UAAD,CAAC,mCAAAhZ,GAAA,CAAAgP,UAAA,IAAE,MAAH,CAAC,sCAAAhP,GAAA,CAAAgP,UAAA,IAAE,SAAH,CAAC,oCAAZhP,GAAA,CAAAqB,iBAAA,CAAkB,CAAC,KAAKrB,GAAA,CAAAS,iBAAA,CAAkB,CAA/B,CAAC,gBAAAT,GAAA,CAAAa,QAAA,CAAAmT,OAAD,CAAC,gBAAAhU,GAAA,CAAAmQ,KAAA,KAAF,QAAQ,IAAAnQ,GAAA,CAAAmQ,KAAA,KAAc,MAArB,CAAC,eAAAnQ,GAAA,CAAAmQ,KAAA,KAAF,QAAC,CAAC,aAAAnQ,GAAA,CAAAmQ,KAAA,KAAF,MAAC,CAAC,iBAAZnQ,GAAA,CAAAgV,cAAA,CAAe,WAAW,CAAf,CAAC,eAAZhV,GAAA,CAAAgV,cAAA,CAAe,SAAS,CAAb,CAAC,gBAAZhV,GAAA,CAAAgV,cAAA,CAAe,UAAU,CAAd,CAAC,aAAZhV,GAAA,CAAAgV,cAAA,CAAe,OAAO,CAAX,CAAC,aAAZhV,GAAA,CAAAgV,cAAA,CAAe,OAAO,CAAX,CAAC,eAAZhV,GAAA,CAAAgV,cAAA,CAAe,SAAS,CAAb,CAAC,eAAZhV,GAAA,CAAAgV,cAAA,CAAe,SAAS,CAAb,CAAC;IAAA;EAAA;EAAA7O,MAAA;IAAAjF,kBAAA;IAAAiP,KAAA;IAAAzB,UAAA;IAAAM,UAAA;IAAAM,eAAA;IAAA7M,SAAA;EAAA;EAAAwW,QAAA;EAAA7S,QAAA,GA17BtBxG,EAAE,CAAAyG,kBAAA,CA07Bs7C,CAC7/C;IAAEC,OAAO,EAAE4H,cAAc;IAAE3H,WAAW,EAAEiI;EAAa,CAAC,EACtD;IAAElI,OAAO,EAAEqB,qBAAqB;IAAEpB,WAAW,EAAEiI;EAAa,CAAC,CAChE;EAAAzB,kBAAA,EAAAmM,GAAA;EAAAjM,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA+L,uBAAApZ,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAA,MAAAqZ,GAAA,GA77BwExZ,EAAE,CAAAyZ,gBAAA;MAAFzZ,EAAE,CAAA0N,eAAA,CAAAgM,GAAA;MAAF1Z,EAAE,CAAAO,UAAA,IAAAiB,oCAAA,gCAAFxB,EAAE,CAAA2Z,sBA67Bg3C,CAAC;MA77Bn3C3Z,EAAE,CAAAK,cAAA,eA67BygH,CAAC;MA77B5gHL,EAAE,CAAA4Z,UAAA,mBAAAC,4CAAAC,MAAA;QAAF9Z,EAAE,CAAA+Z,aAAA,CAAAP,GAAA;QAAA,OAAFxZ,EAAE,CAAAga,WAAA,CA67Bo+G5Z,GAAA,CAAAa,QAAA,CAAAgZ,gBAAA,CAAAH,MAAgC,CAAC;MAAA,CAAC,CAAC;MA77BzgH9Z,EAAE,CAAAO,UAAA,IAAAmB,oCAAA,gBA67ByjH,CAAC;MA77B5jH1B,EAAE,CAAAK,cAAA,YA67BqqH,CAAC;MA77BxqHL,EAAE,CAAAO,UAAA,IAAAwB,oCAAA,gBA67BgsH,CAAC,IAAAE,oCAAA,iBAAsR,CAAC,IAAAC,oCAAA,iBAAwM,CAAC;MA77BnqIlC,EAAE,CAAAK,cAAA,aA67B82I,CAAC;MA77Bj3IL,EAAE,CAAAO,UAAA,KAAA6B,qCAAA,gBA67By6I,CAAC;MA77B56IpC,EAAE,CAAAM,YAAA,GA67B+hJ,CAAC;MA77BliJN,EAAE,CAAAS,YAAA,CA67B2iJ,CAAC;MA77B9iJT,EAAE,CAAAO,UAAA,KAAA8B,qCAAA,iBA67BykJ,CAAC,KAAAC,qCAAA,iBAA2L,CAAC;MA77BxwJtC,EAAE,CAAAS,YAAA,CA67B07J,CAAC;MA77B77JT,EAAE,CAAAO,UAAA,KAAAgC,qCAAA,iBA67Bs9J,CAAC;MA77Bz9JvC,EAAE,CAAAS,YAAA,CA67B2gK,CAAC;MA77B9gKT,EAAE,CAAAK,cAAA,cA67BmsK,CAAC;MA77BtsKL,EAAE,CAAAka,YAAA,GA67B6vK,CAAC;MA77BhwKla,EAAE,CAAAK,cAAA,cA67BmuL,CAAC;MA77BtuLL,EAAE,CAAAO,UAAA,KAAAiC,8BAAA,MA67BkyL,CAAC,KAAAM,8BAAA,MAAsG,CAAC;MA77B54L9C,EAAE,CAAAS,YAAA,CA67BytM,CAAC,CAAO,CAAC;IAAA;IAAA,IAAAN,EAAA;MAAA,IAAAga,QAAA;MA77BpuMna,EAAE,CAAAoB,SAAA,EA67B6uG,CAAC;MA77BhvGpB,EAAE,CAAAkH,WAAA,4BAAA9G,GAAA,CAAAU,WAAA,EA67B6uG,CAAC,6BAAAV,GAAA,CAAAU,WAAA,EAAqD,CAAC,8BAAAV,GAAA,CAAAqB,iBAAA,EAA4D,CAAC,6BAAArB,GAAA,CAAAa,QAAA,CAAAkY,QAAyD,CAAC,4BAAA/Y,GAAA,CAAAa,QAAA,CAAA4R,UAA0D,CAAC;MA77Bx9G7S,EAAE,CAAAoB,SAAA,EA67B0nH,CAAC;MA77B7nHpB,EAAE,CAAAqB,aAAA,EAAAjB,GAAA,CAAAU,WAAA,OAAAV,GAAA,CAAAa,QAAA,CAAAkY,QAAA,SA67B0nH,CAAC;MA77B7nHnZ,EAAE,CAAAoB,SAAA,EA67By7H,CAAC;MA77B57HpB,EAAE,CAAAqB,aAAA,CAAAjB,GAAA,CAAAU,WAAA,WA67By7H,CAAC;MA77B57Hd,EAAE,CAAAoB,SAAA,CA67BkoI,CAAC;MA77BroIpB,EAAE,CAAAqB,aAAA,CAAAjB,GAAA,CAAAkT,cAAA,SA67BkoI,CAAC;MA77BroItT,EAAE,CAAAoB,SAAA,CA67B8zI,CAAC;MA77Bj0IpB,EAAE,CAAAqB,aAAA,CAAAjB,GAAA,CAAAsT,cAAA,SA67B8zI,CAAC;MA77Bj0I1T,EAAE,CAAAoB,SAAA,EA67B4/I,CAAC;MA77B//IpB,EAAE,CAAAqB,aAAA,EAAAjB,GAAA,CAAAU,WAAA,MAAAV,GAAA,CAAA4B,uBAAA,YA67B4/I,CAAC;MA77B//IhC,EAAE,CAAAoB,SAAA,EA67BuuJ,CAAC;MA77B1uJpB,EAAE,CAAAqB,aAAA,CAAAjB,GAAA,CAAA0T,cAAA,UA67BuuJ,CAAC;MA77B1uJ9T,EAAE,CAAAoB,SAAA,CA67Bg7J,CAAC;MA77Bn7JpB,EAAE,CAAAqB,aAAA,CAAAjB,GAAA,CAAAuT,cAAA,UA67Bg7J,CAAC;MA77Bn7J3T,EAAE,CAAAoB,SAAA,CA67BmgK,CAAC;MA77BtgKpB,EAAE,CAAAqB,aAAA,EAAAjB,GAAA,CAAAU,WAAA,YA67BmgK,CAAC;MA77BtgKd,EAAE,CAAAoB,SAAA,CA67BgsK,CAAC;MA77BnsKpB,EAAE,CAAAkH,WAAA,8CAAA9G,GAAA,CAAAsP,eAAA,cA67BgsK,CAAC;MAAA,MAAA0K,uBAAA,GAAmCha,GAAA,CAAAkV,wBAAA,CAAyB,CAAC;MA77BhwKtV,EAAE,CAAAoB,SAAA,EA67ByoL,CAAC;MA77B5oLpB,EAAE,CAAAkH,WAAA,qCAAAkT,uBAAA,YA67ByoL,CAAC,oCAAAA,uBAAA,WAAkF,CAAC;MA77B/tLpa,EAAE,CAAAoB,SAAA,CA67B+sM,CAAC;MA77BltMpB,EAAE,CAAAqB,aAAA,EAAA8Y,QAAA,GAAAC,uBAAA,MA67B0uL,OAAO,QAAAD,QAAA,KAAP,MAAM,UAA+d,CAAC;IAAA;EAAA;EAAAE,YAAA,GAAwjiCrS,yBAAyB,EAAoHwD,0BAA0B,EAAwHtI,gBAAgB,EAAoJsH,sBAAsB,EAAwE1D,OAAO;EAAAwT,MAAA;EAAA3M,aAAA;EAAAC,eAAA;AAAA;AAEn4vC;EAAA,QAAApI,SAAA,oBAAAA,SAAA,KA/7BiFxF,EAAE,CAAAyF,iBAAA,CA+7BQmJ,YAAY,EAAc,CAAC;IAC1GtJ,IAAI,EAAE5B,SAAS;IACfgC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAgB;MAAE0T,QAAQ,EAAE,cAAc;MAAEzS,IAAI,EAAE;QACzD,OAAO,EAAE,oBAAoB;QAC7B,+CAA+C,EAAE,sBAAsB;QACvE,4CAA4C,EAAE,gBAAgB;QAC9D,4CAA4C,EAAE,gBAAgB;QAC9D;QACA;QACA;QACA,gCAAgC,EAAE,qBAAqB;QACvD,iCAAiC,EAAE,mBAAmB;QACtD,mCAAmC,EAAE,qBAAqB;QAC1D,wCAAwC,EAAE,sBAAsB;QAChE,2CAA2C,EAAE,yBAAyB;QACtE,yCAAyC,EAAE,6CAA6C;QACxF,qBAAqB,EAAE,kBAAkB;QACzC,qBAAqB,EAAE,wCAAwC;QAC/D,oBAAoB,EAAE,oBAAoB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,sBAAsB,EAAE,6BAA6B;QACrD,oBAAoB,EAAE,2BAA2B;QACjD,qBAAqB,EAAE,4BAA4B;QACnD,kBAAkB,EAAE,yBAAyB;QAC7C,kBAAkB,EAAE,yBAAyB;QAC7C,oBAAoB,EAAE,2BAA2B;QACjD,oBAAoB,EAAE;MAC1B,CAAC;MAAE+G,aAAa,EAAE/J,iBAAiB,CAACkK,IAAI;MAAEF,eAAe,EAAEjK,uBAAuB,CAACkK,MAAM;MAAEhH,SAAS,EAAE,CAClG;QAAEH,OAAO,EAAE4H,cAAc;QAAE3H,WAAW,EAAEiI;MAAa,CAAC,EACtD;QAAElI,OAAO,EAAEqB,qBAAqB;QAAEpB,WAAW,EAAEiI;MAAa,CAAC,CAChE;MAAE2L,OAAO,EAAE,CACRvS,yBAAyB,EACzBwD,0BAA0B,EAC1BtI,gBAAgB,EAChBsH,sBAAsB,EACtB1D,OAAO,CACV;MAAE0G,QAAQ,EAAE,i5JAAi5J;MAAE8M,MAAM,EAAE,CAAC,2+hCAA2+hC;IAAE,CAAC;EACn6rC,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEpI,UAAU,EAAE,CAAC;MACrD5M,IAAI,EAAEzB,SAAS;MACf6B,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEoR,oBAAoB,EAAE,CAAC;MACvBxR,IAAI,EAAEzB,SAAS;MACf6B,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEqR,oBAAoB,EAAE,CAAC;MACvBzR,IAAI,EAAEzB,SAAS;MACf6B,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE2R,oBAAoB,EAAE,CAAC;MACvB/R,IAAI,EAAEzB,SAAS;MACf6B,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE6R,oBAAoB,EAAE,CAAC;MACvBjS,IAAI,EAAEzB,SAAS;MACf6B,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE8P,cAAc,EAAE,CAAC;MACjBlQ,IAAI,EAAEzB,SAAS;MACf6B,IAAI,EAAE,CAACsC,yBAAyB;IACpC,CAAC,CAAC;IAAE0N,eAAe,EAAE,CAAC;MAClBpQ,IAAI,EAAEzB,SAAS;MACf6B,IAAI,EAAE,CAAC8F,0BAA0B;IACrC,CAAC,CAAC;IAAE+I,WAAW,EAAE,CAAC;MACdjP,IAAI,EAAEzB,SAAS;MACf6B,IAAI,EAAE,CAAC8E,sBAAsB;IACjC,CAAC,CAAC;IAAEyF,iBAAiB,EAAE,CAAC;MACpB3K,IAAI,EAAElB,YAAY;MAClBsB,IAAI,EAAE,CAACqI,mBAAmB;IAC9B,CAAC,CAAC;IAAEwF,eAAe,EAAE,CAAC;MAClBjO,IAAI,EAAEjB,eAAe;MACrBqB,IAAI,EAAE,CAAC0B,UAAU,EAAE;QAAEoT,WAAW,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAE5G,eAAe,EAAE,CAAC;MAClBtO,IAAI,EAAEjB,eAAe;MACrBqB,IAAI,EAAE,CAACiC,UAAU,EAAE;QAAE6S,WAAW,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAEvG,cAAc,EAAE,CAAC;MACjB3O,IAAI,EAAEjB,eAAe;MACrBqB,IAAI,EAAE,CAACE,SAAS,EAAE;QAAE4U,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAExG,aAAa,EAAE,CAAC;MAChB1O,IAAI,EAAEjB,eAAe;MACrBqB,IAAI,EAAE,CAACoB,OAAO,EAAE;QAAE0T,WAAW,EAAE;MAAK,CAAC;IACzC,CAAC,CAAC;IAAElZ,kBAAkB,EAAE,CAAC;MACrBgE,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEiN,KAAK,EAAE,CAAC;MACRjL,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEwL,UAAU,EAAE,CAAC;MACbxJ,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAE8L,UAAU,EAAE,CAAC;MACb9J,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEoM,eAAe,EAAE,CAAC;MAClBpK,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAET,SAAS,EAAE,CAAC;MACZyC,IAAI,EAAEhC;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,SAAS0B,QAAQ,IAAIyV,CAAC,EAAE7U,SAAS,IAAI8U,CAAC,EAAE7U,QAAQ,IAAI8U,CAAC,EAAE7T,OAAO,IAAI8T,CAAC,EAAExT,UAAU,IAAIyT,CAAC,EAAExT,SAAS,IAAIyT,CAAC,EAAEnT,UAAU,IAAIoT,CAAC,EAAEnT,SAAS,IAAIoT,CAAC,EAAE1M,cAAc,IAAI2M,CAAC,EAAE1M,8BAA8B,IAAI2M,CAAC,EAAEtM,YAAY,IAAIuM,CAAC,EAAEpN,mBAAmB,IAAIqN,CAAC,EAAElN,uCAAuC,IAAImN,CAAC,EAAEjN,kCAAkC,IAAIkN,CAAC,EAAEjN,kCAAkC,IAAIkN,CAAC;AAC5W", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}