{"ast": null, "code": "import { Observable } from '../Observable';\nexport var EMPTY = new Observable(function (subscriber) {\n  return subscriber.complete();\n});\nexport function empty(scheduler) {\n  return scheduler ? emptyScheduled(scheduler) : EMPTY;\n}\nfunction emptyScheduled(scheduler) {\n  return new Observable(function (subscriber) {\n    return scheduler.schedule(function () {\n      return subscriber.complete();\n    });\n  });\n}\n//# sourceMappingURL=empty.js.map", "map": {"version": 3, "names": ["Observable", "EMPTY", "subscriber", "complete", "empty", "scheduler", "emptyScheduled", "schedule"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/empty.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport var EMPTY = new Observable(function (subscriber) { return subscriber.complete(); });\nexport function empty(scheduler) {\n    return scheduler ? emptyScheduled(scheduler) : EMPTY;\n}\nfunction emptyScheduled(scheduler) {\n    return new Observable(function (subscriber) { return scheduler.schedule(function () { return subscriber.complete(); }); });\n}\n//# sourceMappingURL=empty.js.map"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,IAAIC,KAAK,GAAG,IAAID,UAAU,CAAC,UAAUE,UAAU,EAAE;EAAE,OAAOA,UAAU,CAACC,QAAQ,CAAC,CAAC;AAAE,CAAC,CAAC;AAC1F,OAAO,SAASC,KAAKA,CAACC,SAAS,EAAE;EAC7B,OAAOA,SAAS,GAAGC,cAAc,CAACD,SAAS,CAAC,GAAGJ,KAAK;AACxD;AACA,SAASK,cAAcA,CAACD,SAAS,EAAE;EAC/B,OAAO,IAAIL,UAAU,CAAC,UAAUE,UAAU,EAAE;IAAE,OAAOG,SAAS,CAACE,QAAQ,CAAC,YAAY;MAAE,OAAOL,UAAU,CAACC,QAAQ,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,CAAC;AAC9H;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}