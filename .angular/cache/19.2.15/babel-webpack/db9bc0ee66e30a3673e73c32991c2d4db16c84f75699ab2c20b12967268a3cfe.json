{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { ReplaySubject } from 'rxjs';\nimport { JwtHelperService } from '@auth0/angular-jwt';\nimport { PermissionService } from './permission.service';\nlet SwHubAuthService = class SwHubAuthService {\n  constructor() {\n    this.logged = new ReplaySubject();\n    this.isSuperAdmin = false;\n    this.isTwoFactor = false;\n    this.jwt = new JwtHelperService();\n  }\n  isLogged() {\n    if (this.expirationDate) {\n      return this.expirationDate > new Date().getTime();\n    }\n    return this.accessToken !== undefined;\n  }\n  tokenGetter() {\n    return () => this.accessToken;\n  }\n  setToken(accessToken, grantedPermissions, twoFactor) {\n    this.logout();\n    if (accessToken) {\n      const token = this.decode(accessToken);\n      const permissions = grantedPermissions ? this.decode(grantedPermissions).permissions : null;\n      if (token) {\n        this.isTwoFactor = !!twoFactor;\n        this.accessToken = accessToken;\n        this.entityKey = `${token.entityId}`;\n        this.username = token.username;\n        this.isSuperAdmin = token.isSuperAdmin || false;\n        this.permissionService = new PermissionService(permissions || token.grantedPermissions || []);\n        if (token.hasOwnProperty('exp')) {\n          const date = new Date(0);\n          date.setUTCSeconds(token.exp);\n          this.expirationDate = date.getTime();\n          console.log(`Token expired at ${date}`);\n        } else {\n          this.expirationDate = undefined;\n        }\n      }\n    }\n    this.logged.next(undefined);\n  }\n  logout() {\n    this.accessToken = undefined;\n    this.entityKey = undefined;\n    this.username = undefined;\n    this.isSuperAdmin = false;\n    this.permissionService = undefined;\n    this.expirationDate = undefined;\n  }\n  getGrantedPermissions() {\n    return this.permissionService ? this.permissionService.permissions : [];\n  }\n  allowedTo(permissions) {\n    return this.permissionService ? this.permissionService.allowedTo(permissions) : false;\n  }\n  areGranted(permissions) {\n    return this.permissionService ? this.permissionService.areGranted(permissions) : false;\n  }\n  decode(token) {\n    try {\n      return this.jwt.decodeToken(token);\n    } catch (e) {\n      console.error(e);\n    }\n    return null;\n  }\n};\nSwHubAuthService = __decorate([Injectable()], SwHubAuthService);\nexport { SwHubAuthService };", "map": {"version": 3, "names": ["Injectable", "ReplaySubject", "JwtHelperService", "PermissionService", "SwHubAuthService", "constructor", "logged", "isSuperAdmin", "isTwoFactor", "jwt", "isLogged", "expirationDate", "Date", "getTime", "accessToken", "undefined", "tokenGetter", "setToken", "grantedPermissions", "twoFactor", "logout", "token", "decode", "permissions", "entityKey", "entityId", "username", "permissionService", "hasOwnProperty", "date", "setUTCSeconds", "exp", "console", "log", "next", "getGrantedPermissions", "allowedTo", "areGranted", "decodeToken", "e", "error", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-auth/sw-hub-auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { ReplaySubject } from 'rxjs';\nimport { JwtHelperService } from '@auth0/angular-jwt';\nimport { PermissionService } from './permission.service';\n\n@Injectable()\nexport class SwHubAuthService {\n  logged = new ReplaySubject<void>();\n  accessToken: string | undefined;\n  entityKey: string | undefined;\n  username: string | undefined;\n  isSuperAdmin = false;\n  isTwoFactor = false;\n\n  private expirationDate: number | undefined;\n  private permissionService: PermissionService | undefined;\n  private readonly jwt = new JwtHelperService();\n\n  isLogged(): boolean {\n    if (this.expirationDate) {\n      return this.expirationDate > new Date().getTime();\n    }\n    return this.accessToken !== undefined;\n  }\n\n  tokenGetter() {\n    return () => this.accessToken;\n  }\n\n  setToken( accessToken?: string | null, grantedPermissions?: string | null, twoFactor?: boolean): void {\n    this.logout();\n    if (accessToken) {\n      const token = this.decode(accessToken);\n      const permissions = grantedPermissions ? this.decode(grantedPermissions).permissions : null;\n      if (token) {\n        this.isTwoFactor = !!twoFactor;\n        this.accessToken = accessToken;\n        this.entityKey = `${token.entityId}`;\n        this.username = token.username;\n        this.isSuperAdmin = token.isSuperAdmin || false;\n        this.permissionService = new PermissionService(permissions || token.grantedPermissions || []);\n\n        if (token.hasOwnProperty('exp')) {\n          const date = new Date(0);\n          date.setUTCSeconds(token.exp);\n          this.expirationDate = date.getTime();\n          console.log(`Token expired at ${date}`);\n        } else {\n          this.expirationDate = undefined;\n        }\n      }\n    }\n    this.logged.next(undefined);\n  }\n\n  logout(): void {\n    this.accessToken = undefined;\n    this.entityKey = undefined;\n    this.username = undefined;\n    this.isSuperAdmin = false;\n    this.permissionService = undefined;\n    this.expirationDate = undefined;\n  }\n\n  getGrantedPermissions(): string[] {\n    return this.permissionService ? this.permissionService.permissions : [];\n  }\n\n  allowedTo( permissions: string[] ): boolean {\n    return this.permissionService ? this.permissionService.allowedTo(permissions) : false;\n  }\n\n  areGranted( permissions: string[] ): boolean {\n    return this.permissionService ? this.permissionService.areGranted(permissions) : false;\n  }\n\n  private decode( token: string ): any | null {\n    try {\n      return this.jwt.decodeToken(token);\n    } catch (e) {\n      console.error(e);\n    }\n    return null;\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,aAAa,QAAQ,MAAM;AACpC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,iBAAiB,QAAQ,sBAAsB;AAGjD,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAAtBC,YAAA;IACL,KAAAC,MAAM,GAAG,IAAIL,aAAa,EAAQ;IAIlC,KAAAM,YAAY,GAAG,KAAK;IACpB,KAAAC,WAAW,GAAG,KAAK;IAIF,KAAAC,GAAG,GAAG,IAAIP,gBAAgB,EAAE;EAoE/C;EAlEEQ,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,cAAc,EAAE;MACvB,OAAO,IAAI,CAACA,cAAc,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;IACnD;IACA,OAAO,IAAI,CAACC,WAAW,KAAKC,SAAS;EACvC;EAEAC,WAAWA,CAAA;IACT,OAAO,MAAM,IAAI,CAACF,WAAW;EAC/B;EAEAG,QAAQA,CAAEH,WAA2B,EAAEI,kBAAkC,EAAEC,SAAmB;IAC5F,IAAI,CAACC,MAAM,EAAE;IACb,IAAIN,WAAW,EAAE;MACf,MAAMO,KAAK,GAAG,IAAI,CAACC,MAAM,CAACR,WAAW,CAAC;MACtC,MAAMS,WAAW,GAAGL,kBAAkB,GAAG,IAAI,CAACI,MAAM,CAACJ,kBAAkB,CAAC,CAACK,WAAW,GAAG,IAAI;MAC3F,IAAIF,KAAK,EAAE;QACT,IAAI,CAACb,WAAW,GAAG,CAAC,CAACW,SAAS;QAC9B,IAAI,CAACL,WAAW,GAAGA,WAAW;QAC9B,IAAI,CAACU,SAAS,GAAG,GAAGH,KAAK,CAACI,QAAQ,EAAE;QACpC,IAAI,CAACC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;QAC9B,IAAI,CAACnB,YAAY,GAAGc,KAAK,CAACd,YAAY,IAAI,KAAK;QAC/C,IAAI,CAACoB,iBAAiB,GAAG,IAAIxB,iBAAiB,CAACoB,WAAW,IAAIF,KAAK,CAACH,kBAAkB,IAAI,EAAE,CAAC;QAE7F,IAAIG,KAAK,CAACO,cAAc,CAAC,KAAK,CAAC,EAAE;UAC/B,MAAMC,IAAI,GAAG,IAAIjB,IAAI,CAAC,CAAC,CAAC;UACxBiB,IAAI,CAACC,aAAa,CAACT,KAAK,CAACU,GAAG,CAAC;UAC7B,IAAI,CAACpB,cAAc,GAAGkB,IAAI,CAAChB,OAAO,EAAE;UACpCmB,OAAO,CAACC,GAAG,CAAC,oBAAoBJ,IAAI,EAAE,CAAC;QACzC,CAAC,MAAM;UACL,IAAI,CAAClB,cAAc,GAAGI,SAAS;QACjC;MACF;IACF;IACA,IAAI,CAACT,MAAM,CAAC4B,IAAI,CAACnB,SAAS,CAAC;EAC7B;EAEAK,MAAMA,CAAA;IACJ,IAAI,CAACN,WAAW,GAAGC,SAAS;IAC5B,IAAI,CAACS,SAAS,GAAGT,SAAS;IAC1B,IAAI,CAACW,QAAQ,GAAGX,SAAS;IACzB,IAAI,CAACR,YAAY,GAAG,KAAK;IACzB,IAAI,CAACoB,iBAAiB,GAAGZ,SAAS;IAClC,IAAI,CAACJ,cAAc,GAAGI,SAAS;EACjC;EAEAoB,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACR,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACJ,WAAW,GAAG,EAAE;EACzE;EAEAa,SAASA,CAAEb,WAAqB;IAC9B,OAAO,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACS,SAAS,CAACb,WAAW,CAAC,GAAG,KAAK;EACvF;EAEAc,UAAUA,CAAEd,WAAqB;IAC/B,OAAO,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACU,UAAU,CAACd,WAAW,CAAC,GAAG,KAAK;EACxF;EAEQD,MAAMA,CAAED,KAAa;IAC3B,IAAI;MACF,OAAO,IAAI,CAACZ,GAAG,CAAC6B,WAAW,CAACjB,KAAK,CAAC;IACpC,CAAC,CAAC,OAAOkB,CAAC,EAAE;MACVP,OAAO,CAACQ,KAAK,CAACD,CAAC,CAAC;IAClB;IACA,OAAO,IAAI;EACb;CACD;AA9EYnC,gBAAgB,GAAAqC,UAAA,EAD5BzC,UAAU,EAAE,C,EACAI,gBAAgB,CA8E5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}