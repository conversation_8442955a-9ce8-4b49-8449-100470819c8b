{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./coin-value-column.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/coin-value-column.component.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICBzZWxlY3QgewogICAgICBoZWlnaHQ6IDI0cHg7CiAgICB9CiAg!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/coin-value-column.component.ts\";\nimport { Component } from '@angular/core';\nimport { DefaultColumnComponent } from './default-column.component';\nlet CoinValueColumnComponent = class CoinValueColumnComponent extends DefaultColumnComponent {\n  get coinLimit() {\n    if (this.curcode && this.game && 'coins' in this.game && this.game.coins && this.game.coins.length) {\n      return this.game.coins[0][this.curcode].coin;\n    }\n  }\n  set coinLimit(coin) {\n    if (this.game) {\n      this.game.coins = []; // supports only one currency for now\n      if (this.curcode && coin) {\n        this.game.coins.push({\n          [this.curcode]: {\n            coin\n          }\n        });\n      }\n    }\n    if (this.valueChange) {\n      this.valueChange.emit(coin);\n    }\n  }\n  ngOnInit() {\n    this.curcode = this.params['curcode'];\n    this.checkAndSetDefaultCoinLimit();\n  }\n  setLimit(event) {\n    let value = event.target.value;\n    this.coinLimit = parseFloat(value);\n  }\n  checkAndSetDefaultCoinLimit() {\n    if (this.values && this.values.length > 0) {\n      if (this.game && !('coins' in this.game)) {\n        this.coinLimit = this.values[0];\n      }\n    }\n  }\n};\nCoinValueColumnComponent = __decorate([Component({\n  // tslint:disable-next-line:component-selector\n  selector: 'sw-coin-value-column',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CoinValueColumnComponent);\nexport { CoinValueColumnComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "DefaultColumnComponent", "CoinValueColumnComponent", "coinLimit", "curcode", "game", "coins", "length", "coin", "push", "valueChange", "emit", "ngOnInit", "params", "checkAndSetDefaultCoinLimit", "setLimit", "event", "value", "target", "parseFloat", "values", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/coin-value-column.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./coin-value-column.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/coin-value-column.component.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICBzZWxlY3QgewogICAgICBoZWlnaHQ6IDI0cHg7CiAgICB9CiAg!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/coin-value-column.component.ts\";\nimport { Component } from '@angular/core';\nimport { DefaultColumnComponent } from './default-column.component';\nlet CoinValueColumnComponent = class CoinValueColumnComponent extends DefaultColumnComponent {\n    get coinLimit() {\n        if (this.curcode && this.game && 'coins' in this.game && this.game.coins && this.game.coins.length) {\n            return this.game.coins[0][this.curcode].coin;\n        }\n    }\n    set coinLimit(coin) {\n        if (this.game) {\n            this.game.coins = []; // supports only one currency for now\n            if (this.curcode && coin) {\n                this.game.coins.push({\n                    [this.curcode]: { coin }\n                });\n            }\n        }\n        if (this.valueChange) {\n            this.valueChange.emit(coin);\n        }\n    }\n    ngOnInit() {\n        this.curcode = this.params['curcode'];\n        this.checkAndSetDefaultCoinLimit();\n    }\n    setLimit(event) {\n        let value = event.target.value;\n        this.coinLimit = parseFloat(value);\n    }\n    checkAndSetDefaultCoinLimit() {\n        if (this.values && this.values.length > 0) {\n            if (this.game && !('coins' in this.game)) {\n                this.coinLimit = this.values[0];\n            }\n        }\n    }\n};\nCoinValueColumnComponent = __decorate([\n    Component({\n        // tslint:disable-next-line:component-selector\n        selector: 'sw-coin-value-column',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], CoinValueColumnComponent);\nexport { CoinValueColumnComponent };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,OAAOC,oBAAoB,MAAM,shBAAshB;AACvjB,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,IAAIC,wBAAwB,GAAG,MAAMA,wBAAwB,SAASD,sBAAsB,CAAC;EACzF,IAAIE,SAASA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,IAAI,IAAI,OAAO,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,KAAK,IAAI,IAAI,CAACD,IAAI,CAACC,KAAK,CAACC,MAAM,EAAE;MAChG,OAAO,IAAI,CAACF,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAACF,OAAO,CAAC,CAACI,IAAI;IAChD;EACJ;EACA,IAAIL,SAASA,CAACK,IAAI,EAAE;IAChB,IAAI,IAAI,CAACH,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACC,KAAK,GAAG,EAAE,CAAC,CAAC;MACtB,IAAI,IAAI,CAACF,OAAO,IAAII,IAAI,EAAE;QACtB,IAAI,CAACH,IAAI,CAACC,KAAK,CAACG,IAAI,CAAC;UACjB,CAAC,IAAI,CAACL,OAAO,GAAG;YAAEI;UAAK;QAC3B,CAAC,CAAC;MACN;IACJ;IACA,IAAI,IAAI,CAACE,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACC,IAAI,CAACH,IAAI,CAAC;IAC/B;EACJ;EACAI,QAAQA,CAAA,EAAG;IACP,IAAI,CAACR,OAAO,GAAG,IAAI,CAACS,MAAM,CAAC,SAAS,CAAC;IACrC,IAAI,CAACC,2BAA2B,CAAC,CAAC;EACtC;EACAC,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAIC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAC9B,IAAI,CAACd,SAAS,GAAGgB,UAAU,CAACF,KAAK,CAAC;EACtC;EACAH,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,IAAI,CAACM,MAAM,IAAI,IAAI,CAACA,MAAM,CAACb,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,IAAI,CAACF,IAAI,IAAI,EAAE,OAAO,IAAI,IAAI,CAACA,IAAI,CAAC,EAAE;QACtC,IAAI,CAACF,SAAS,GAAG,IAAI,CAACiB,MAAM,CAAC,CAAC,CAAC;MACnC;IACJ;EACJ;AACJ,CAAC;AACDlB,wBAAwB,GAAGL,UAAU,CAAC,CAClCG,SAAS,CAAC;EACN;EACAqB,QAAQ,EAAE,sBAAsB;EAChCC,QAAQ,EAAExB,oBAAoB;EAC9ByB,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACzB,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEG,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}