{"ast": null, "code": "var _SwuiFooterStringWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./string.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiFooterStringWidgetComponent = (_SwuiFooterStringWidgetComponent = class SwuiFooterStringWidgetComponent {\n  constructor({\n    value\n  }) {\n    this.value = value;\n  }\n}, _SwuiFooterStringWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiFooterStringWidgetComponent);\nSwuiFooterStringWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-footer-string-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiFooterStringWidgetComponent);\nexport { SwuiFooterStringWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiFooterStringWidgetComponent", "_SwuiFooterStringWidgetComponent", "constructor", "value", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/footer-widget/string/string.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./string.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiFooterStringWidgetComponent = class SwuiFooterStringWidgetComponent {\n    constructor({ value }) {\n        this.value = value;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiFooterStringWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-footer-string-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiFooterStringWidgetComponent);\nexport { SwuiFooterStringWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,+BAA+B,IAAAC,gCAAA,GAAG,MAAMD,+BAA+B,CAAC;EACxEE,WAAWA,CAAC;IAAEC;EAAM,CAAC,EAAE;IACnB,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;AAIJ,CAAC,EAHYF,gCAAA,CAAKG,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEP,MAAM;IAAEU,IAAI,EAAE,CAACT,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,gCAAA,CACJ;AACDD,+BAA+B,GAAGL,UAAU,CAAC,CACzCE,SAAS,CAAC;EACNY,QAAQ,EAAE,+BAA+B;EACzCC,QAAQ,EAAEd,oBAAoB;EAC9Be,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEX,+BAA+B,CAAC;AACnC,SAASA,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}