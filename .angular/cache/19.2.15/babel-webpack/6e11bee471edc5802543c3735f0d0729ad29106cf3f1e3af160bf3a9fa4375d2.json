{"ast": null, "code": "var _SwuiSnackbarComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-snackbar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-snackbar.component.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';\nlet SwuiSnackbarComponent = (_SwuiSnackbarComponent = class SwuiSnackbarComponent {\n  constructor(snackbar, data) {\n    this.snackbar = snackbar;\n    this.data = data;\n  }\n  ngOnDestroy() {\n    this.close();\n  }\n  close() {\n    if (this.snackbar) {\n      this.snackbar.dismiss();\n    }\n  }\n}, _SwuiSnackbarComponent.ctorParameters = () => [{\n  type: MatSnackBarRef\n}, {\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [MAT_SNACK_BAR_DATA]\n  }]\n}], _SwuiSnackbarComponent);\nSwuiSnackbarComponent = __decorate([Component({\n  selector: 'lib-swui-snackbar',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSnackbarComponent);\nexport { SwuiSnackbarComponent };", "map": {"version": 3, "names": ["Component", "Inject", "MAT_SNACK_BAR_DATA", "MatSnackBarRef", "SwuiSnackbarComponent", "_SwuiSnackbarComponent", "constructor", "snackbar", "data", "ngOnDestroy", "close", "dismiss", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-notifications/swui-snackbar/swui-snackbar.component.ts"], "sourcesContent": ["import { Component, Inject, OnDestroy } from '@angular/core';\nimport { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';\nimport { NotificationsMessage } from '../swui-notifications.service';\n\n@Component({\n    selector: 'lib-swui-snackbar',\n    templateUrl: './swui-snackbar.component.html',\n    styleUrls: ['./swui-snackbar.component.scss'],\n    standalone: false\n})\nexport class SwuiSnackbarComponent implements OnDestroy {\n\n  constructor(\n    public snackbar: MatSnackBarRef<SwuiSnackbarComponent>,\n    @Inject(MAT_SNACK_BAR_DATA) public data: NotificationsMessage,\n  ) {\n  }\n\n  ngOnDestroy() {\n    this.close();\n  }\n\n  close() {\n    if (this.snackbar) {\n      this.snackbar.dismiss();\n    }\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAmB,eAAe;AAC5D,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,6BAA6B;AASzE,IAAMC,qBAAqB,IAAAC,sBAAA,GAA3B,MAAMD,qBAAqB;EAEhCE,YACSC,QAA+C,EACnBC,IAA0B;IADtD,KAAAD,QAAQ,GAARA,QAAQ;IACoB,KAAAC,IAAI,GAAJA,IAAI;EAEzC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACC,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,IAAI,CAACH,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACI,OAAO,EAAE;IACzB;EACF;;;;;;UAZGV,MAAM;IAAAW,IAAA,GAACV,kBAAkB;EAAA;AAAA,E;AAJjBE,qBAAqB,GAAAS,UAAA,EANjCb,SAAS,CAAC;EACPc,QAAQ,EAAE,mBAAmB;EAC7BC,QAAA,EAAAC,oBAA6C;EAE7CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWb,qBAAqB,CAiBjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}