{"ast": null, "code": "var _SwuiCheckboxComponent;\nimport { __decorate } from \"tslib\";\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, Input } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet SwuiCheckboxComponent = (_SwuiCheckboxComponent = class SwuiCheckboxComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.isChecked = false;\n    this.isDisabled = false;\n    this.onChange = _ => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    if (value) {\n      this.isChecked = value;\n      this.cdr.detectChanges();\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.isDisabled = isDisabled;\n  }\n  handleChange() {\n    this.isChecked = !this.isChecked;\n    this.cdr.detectChanges();\n    this.onChange(this.isChecked);\n  }\n}, _SwuiCheckboxComponent.ctorParameters = () => [{\n  type: ChangeDetectorRef\n}], _SwuiCheckboxComponent.propDecorators = {\n  cssClass: [{\n    type: Input\n  }]\n}, _SwuiCheckboxComponent);\nSwuiCheckboxComponent = __decorate([Component({\n  selector: 'lib-swui-checkbox',\n  template: `\n    <label class=\"checkbox-inline\" [ngClass]=\"cssClass\" [class.disabled]=\"isDisabled\">\n      <div class=\"checker\">\n        <span [class.checked]=\"isChecked\">\n          <input\n            class=\"bo-checkbox__input\"\n            type=\"checkbox\"\n            [disabled]=\"isDisabled\"\n            [checked]=\"isChecked\"\n            (change)=\"handleChange()\">\n        </span>\n      </div>\n      <ng-content></ng-content>\n    </label>\n  `,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SwuiCheckboxComponent),\n    multi: true\n  }],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false\n})], SwuiCheckboxComponent);\nexport { SwuiCheckboxComponent };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "ChangeDetectorRef", "Component", "forwardRef", "Input", "NG_VALUE_ACCESSOR", "SwuiCheckboxComponent", "_SwuiCheckboxComponent", "constructor", "cdr", "isChecked", "isDisabled", "onChange", "_", "onTouched", "writeValue", "value", "detectChanges", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "handleChange", "__decorate", "selector", "template", "providers", "provide", "useExisting", "multi", "changeDetection", "OnPush", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-checkbox/swui-checkbox.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, Input } from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\n\n@Component({\n    selector: 'lib-swui-checkbox',\n    template: `\n    <label class=\"checkbox-inline\" [ngClass]=\"cssClass\" [class.disabled]=\"isDisabled\">\n      <div class=\"checker\">\n        <span [class.checked]=\"isChecked\">\n          <input\n            class=\"bo-checkbox__input\"\n            type=\"checkbox\"\n            [disabled]=\"isDisabled\"\n            [checked]=\"isChecked\"\n            (change)=\"handleChange()\">\n        </span>\n      </div>\n      <ng-content></ng-content>\n    </label>\n  `,\n    styles: [],\n    providers: [\n        { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => SwuiCheckboxComponent), multi: true },\n    ],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    standalone: false\n})\nexport class SwuiCheckboxComponent implements ControlValueAccessor {\n\n  @Input() cssClass: string | undefined;\n\n  public isChecked = false;\n  public isDisabled = false;\n\n  constructor(private cdr: ChangeDetectorRef) {\n  }\n\n  public onChange = (_: boolean) => {\n  };\n  public onTouched = () => {\n  };\n\n  writeValue(value: boolean) {\n    if (value) {\n      this.isChecked = value;\n      this.cdr.detectChanges();\n    }\n  }\n\n  registerOnChange(fn: (_: any) => void) {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void) {\n    this.onTouched = fn;\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    this.isDisabled = isDisabled;\n  }\n\n  public handleChange() {\n    this.isChecked = !this.isChecked;\n    this.cdr.detectChanges();\n    this.onChange(this.isChecked);\n  }\n}\n"], "mappings": ";;AAAA,SAASA,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,KAAK,QAAQ,eAAe;AACxG,SAA+BC,iBAAiB,QAAQ,gBAAgB;AA0BjE,IAAMC,qBAAqB,IAAAC,sBAAA,GAA3B,MAAMD,qBAAqB;EAOhCE,YAAoBC,GAAsB;IAAtB,KAAAA,GAAG,GAAHA,GAAG;IAHhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAG,KAAK;IAKlB,KAAAC,QAAQ,GAAIC,CAAU,IAAI,CACjC,CAAC;IACM,KAAAC,SAAS,GAAG,MAAK,CACxB,CAAC;EALD;EAOAC,UAAUA,CAACC,KAAc;IACvB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACN,SAAS,GAAGM,KAAK;MACtB,IAAI,CAACP,GAAG,CAACQ,aAAa,EAAE;IAC1B;EACF;EAEAC,gBAAgBA,CAACC,EAAoB;IACnC,IAAI,CAACP,QAAQ,GAAGO,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACL,SAAS,GAAGK,EAAE;EACrB;EAEAE,gBAAgBA,CAACV,UAAmB;IAClC,IAAI,CAACA,UAAU,GAAGA,UAAU;EAC9B;EAEOW,YAAYA,CAAA;IACjB,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAChC,IAAI,CAACD,GAAG,CAACQ,aAAa,EAAE;IACxB,IAAI,CAACL,QAAQ,CAAC,IAAI,CAACF,SAAS,CAAC;EAC/B;;;;;UApCCN;EAAK;;AAFKE,qBAAqB,GAAAiB,UAAA,EAxBjCrB,SAAS,CAAC;EACPsB,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAE;;;;;;;;;;;;;;GAcX;EAECC,SAAS,EAAE,CACP;IAAEC,OAAO,EAAEtB,iBAAiB;IAAEuB,WAAW,EAAEzB,UAAU,CAAC,MAAMG,qBAAqB,CAAC;IAAEuB,KAAK,EAAE;EAAI,CAAE,CACpG;EACDC,eAAe,EAAE9B,uBAAuB,CAAC+B,MAAM;EAC/CC,UAAU,EAAE;CACf,CAAC,C,EACW1B,qBAAqB,CAuCjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}