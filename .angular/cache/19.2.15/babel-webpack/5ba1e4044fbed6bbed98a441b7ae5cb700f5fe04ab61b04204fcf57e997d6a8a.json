{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MediaMatcher, _BreakpointObserver;\nimport * as i0 from '@angular/core';\nimport { inject, CSP_NONCE, Injectable, NgZone } from '@angular/core';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n  constructor() {\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_nonce\", inject(CSP_NONCE, {\n      optional: true\n    }));\n    /** The internal matchMedia method to return back a MediaQueryList like object. */\n    _defineProperty(this, \"_matchMedia\", void 0);\n    this._matchMedia = this._platform.isBrowser && window.matchMedia ?\n    // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n    // call it from a different scope.\n    window.matchMedia.bind(window) : noopMatchMedia;\n  }\n  /**\n   * Evaluates the given media query and returns the native MediaQueryList from which results\n   * can be retrieved.\n   * Confirms the layout engine will trigger for the selector query provided and returns the\n   * MediaQueryList for the query provided.\n   */\n  matchMedia(query) {\n    if (this._platform.WEBKIT || this._platform.BLINK) {\n      createEmptyStyleRule(query, this._nonce);\n    }\n    return this._matchMedia(query);\n  }\n}\n_MediaMatcher = MediaMatcher;\n_defineProperty(MediaMatcher, \"\\u0275fac\", function _MediaMatcher_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MediaMatcher)();\n});\n_defineProperty(MediaMatcher, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _MediaMatcher,\n  factory: _MediaMatcher.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MediaMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n      if (nonce) {\n        mediaQueryStyleNode.setAttribute('nonce', nonce);\n      }\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head.appendChild(mediaQueryStyleNode);\n    }\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n\n/** Utility for checking the matching state of `@media` queries. */\nclass BreakpointObserver {\n  constructor() {\n    _defineProperty(this, \"_mediaMatcher\", inject(MediaMatcher));\n    _defineProperty(this, \"_zone\", inject(NgZone));\n    /**  A map of all media queries currently being listened for. */\n    _defineProperty(this, \"_queries\", new Map());\n    /** A subject for all other observables to takeUntil based on. */\n    _defineProperty(this, \"_destroySubject\", new Subject());\n  }\n  /** Completes the active subject, signalling to all other observables to complete. */\n  ngOnDestroy() {\n    this._destroySubject.next();\n    this._destroySubject.complete();\n  }\n  /**\n   * Whether one or more media queries match the current viewport size.\n   * @param value One or more media queries to check.\n   * @returns Whether any of the media queries match.\n   */\n  isMatched(value) {\n    const queries = splitQueries(coerceArray(value));\n    return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n  }\n  /**\n   * Gets an observable of results for the given queries that will emit new results for any changes\n   * in matching of the given queries.\n   * @param value One or more media queries to check.\n   * @returns A stream of matches for the given queries.\n   */\n  observe(value) {\n    const queries = splitQueries(coerceArray(value));\n    const observables = queries.map(query => this._registerQuery(query).observable);\n    let stateObservable = combineLatest(observables);\n    // Emit the first state immediately, and then debounce the subsequent emissions.\n    stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n    return stateObservable.pipe(map(breakpointStates => {\n      const response = {\n        matches: false,\n        breakpoints: {}\n      };\n      breakpointStates.forEach(({\n        matches,\n        query\n      }) => {\n        response.matches = response.matches || matches;\n        response.breakpoints[query] = matches;\n      });\n      return response;\n    }));\n  }\n  /** Registers a specific query to be listened for. */\n  _registerQuery(query) {\n    // Only set up a new MediaQueryList if it is not already being listened for.\n    if (this._queries.has(query)) {\n      return this._queries.get(query);\n    }\n    const mql = this._mediaMatcher.matchMedia(query);\n    // Create callback for match changes and add it is as a listener.\n    const queryObservable = new Observable(observer => {\n      // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n      // back into the zone because matchMedia is only included in Zone.js by loading the\n      // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n      // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n      // patches it.\n      const handler = e => this._zone.run(() => observer.next(e));\n      mql.addListener(handler);\n      return () => {\n        mql.removeListener(handler);\n      };\n    }).pipe(startWith(mql), map(({\n      matches\n    }) => ({\n      query,\n      matches\n    })), takeUntil(this._destroySubject));\n    // Add the MediaQueryList to the set of queries.\n    const output = {\n      observable: queryObservable,\n      mql\n    };\n    this._queries.set(query, output);\n    return output;\n  }\n}\n_BreakpointObserver = BreakpointObserver;\n_defineProperty(BreakpointObserver, \"\\u0275fac\", function _BreakpointObserver_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BreakpointObserver)();\n});\n_defineProperty(BreakpointObserver, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _BreakpointObserver,\n  factory: _BreakpointObserver.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreakpointObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\nexport { BreakpointObserver as B, MediaMatcher as M };\n//# sourceMappingURL=breakpoints-observer-CljOfYGy.mjs.map", "map": {"version": 3, "names": ["i0", "inject", "CSP_NONCE", "Injectable", "NgZone", "Subject", "combineLatest", "concat", "Observable", "take", "skip", "debounceTime", "map", "startWith", "takeUntil", "P", "Platform", "c", "coerce<PERSON><PERSON><PERSON>", "mediaQueriesForWebkitCompatibility", "Set", "mediaQueryStyleNode", "MediaMatcher", "constructor", "_defineProperty", "optional", "_matchMedia", "_platform", "<PERSON><PERSON><PERSON><PERSON>", "window", "matchMedia", "bind", "noopMatchMedia", "query", "WEBKIT", "BLINK", "createEmptyStyleRule", "_nonce", "_MediaMatcher", "_MediaMatcher_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "nonce", "has", "document", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "add", "e", "console", "error", "matches", "media", "addListener", "removeListener", "BreakpointObserver", "Map", "ngOnDestroy", "_destroySubject", "next", "complete", "isMatched", "value", "queries", "splitQueries", "some", "mediaQuery", "_registerQuery", "mql", "observe", "observables", "observable", "stateObservable", "pipe", "breakpointStates", "response", "breakpoints", "for<PERSON>ach", "_queries", "get", "_mediaMatcher", "queryObservable", "observer", "handler", "_zone", "run", "output", "set", "_BreakpointObserver", "_BreakpointObserver_Factory", "split", "reduce", "a1", "a2", "trim", "B", "M"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/breakpoints-observer-CljOfYGy.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, CSP_NONCE, Injectable, NgZone } from '@angular/core';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n    _platform = inject(Platform);\n    _nonce = inject(CSP_NONCE, { optional: true });\n    /** The internal matchMedia method to return back a MediaQueryList like object. */\n    _matchMedia;\n    constructor() {\n        this._matchMedia =\n            this._platform.isBrowser && window.matchMedia\n                ? // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n                    // call it from a different scope.\n                    window.matchMedia.bind(window)\n                : noopMatchMedia;\n    }\n    /**\n     * Evaluates the given media query and returns the native MediaQueryList from which results\n     * can be retrieved.\n     * Confirms the layout engine will trigger for the selector query provided and returns the\n     * MediaQueryList for the query provided.\n     */\n    matchMedia(query) {\n        if (this._platform.WEBKIT || this._platform.BLINK) {\n            createEmptyStyleRule(query, this._nonce);\n        }\n        return this._matchMedia(query);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MediaMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MediaMatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MediaMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n    if (mediaQueriesForWebkitCompatibility.has(query)) {\n        return;\n    }\n    try {\n        if (!mediaQueryStyleNode) {\n            mediaQueryStyleNode = document.createElement('style');\n            if (nonce) {\n                mediaQueryStyleNode.setAttribute('nonce', nonce);\n            }\n            mediaQueryStyleNode.setAttribute('type', 'text/css');\n            document.head.appendChild(mediaQueryStyleNode);\n        }\n        if (mediaQueryStyleNode.sheet) {\n            mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n            mediaQueriesForWebkitCompatibility.add(query);\n        }\n    }\n    catch (e) {\n        console.error(e);\n    }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n    // Use `as any` here to avoid adding additional necessary properties for\n    // the noop matcher.\n    return {\n        matches: query === 'all' || query === '',\n        media: query,\n        addListener: () => { },\n        removeListener: () => { },\n    };\n}\n\n/** Utility for checking the matching state of `@media` queries. */\nclass BreakpointObserver {\n    _mediaMatcher = inject(MediaMatcher);\n    _zone = inject(NgZone);\n    /**  A map of all media queries currently being listened for. */\n    _queries = new Map();\n    /** A subject for all other observables to takeUntil based on. */\n    _destroySubject = new Subject();\n    constructor() { }\n    /** Completes the active subject, signalling to all other observables to complete. */\n    ngOnDestroy() {\n        this._destroySubject.next();\n        this._destroySubject.complete();\n    }\n    /**\n     * Whether one or more media queries match the current viewport size.\n     * @param value One or more media queries to check.\n     * @returns Whether any of the media queries match.\n     */\n    isMatched(value) {\n        const queries = splitQueries(coerceArray(value));\n        return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n    }\n    /**\n     * Gets an observable of results for the given queries that will emit new results for any changes\n     * in matching of the given queries.\n     * @param value One or more media queries to check.\n     * @returns A stream of matches for the given queries.\n     */\n    observe(value) {\n        const queries = splitQueries(coerceArray(value));\n        const observables = queries.map(query => this._registerQuery(query).observable);\n        let stateObservable = combineLatest(observables);\n        // Emit the first state immediately, and then debounce the subsequent emissions.\n        stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n        return stateObservable.pipe(map(breakpointStates => {\n            const response = {\n                matches: false,\n                breakpoints: {},\n            };\n            breakpointStates.forEach(({ matches, query }) => {\n                response.matches = response.matches || matches;\n                response.breakpoints[query] = matches;\n            });\n            return response;\n        }));\n    }\n    /** Registers a specific query to be listened for. */\n    _registerQuery(query) {\n        // Only set up a new MediaQueryList if it is not already being listened for.\n        if (this._queries.has(query)) {\n            return this._queries.get(query);\n        }\n        const mql = this._mediaMatcher.matchMedia(query);\n        // Create callback for match changes and add it is as a listener.\n        const queryObservable = new Observable((observer) => {\n            // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n            // back into the zone because matchMedia is only included in Zone.js by loading the\n            // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n            // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n            // patches it.\n            const handler = (e) => this._zone.run(() => observer.next(e));\n            mql.addListener(handler);\n            return () => {\n                mql.removeListener(handler);\n            };\n        }).pipe(startWith(mql), map(({ matches }) => ({ query, matches })), takeUntil(this._destroySubject));\n        // Add the MediaQueryList to the set of queries.\n        const output = { observable: queryObservable, mql };\n        this._queries.set(query, output);\n        return output;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BreakpointObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BreakpointObserver, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BreakpointObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n    return queries\n        .map(query => query.split(','))\n        .reduce((a1, a2) => a1.concat(a2))\n        .map(query => query.trim());\n}\n\nexport { BreakpointObserver as B, MediaMatcher as M };\n//# sourceMappingURL=breakpoints-observer-CljOfYGy.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AACrE,SAASC,OAAO,EAAEC,aAAa,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AACjE,SAASC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACpF,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,WAAW,QAAQ,sBAAsB;;AAEvD;AACA,MAAMC,kCAAkC,GAAG,IAAIC,GAAG,CAAC,CAAC;AACpD;AACA,IAAIC,mBAAmB;AACvB;AACA,MAAMC,YAAY,CAAC;EAKfC,WAAWA,CAAA,EAAG;IAAAC,eAAA,oBAJFvB,MAAM,CAACe,QAAQ,CAAC;IAAAQ,eAAA,iBACnBvB,MAAM,CAACC,SAAS,EAAE;MAAEuB,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC9C;IAAAD,eAAA;IAGI,IAAI,CAACE,WAAW,GACZ,IAAI,CAACC,SAAS,CAACC,SAAS,IAAIC,MAAM,CAACC,UAAU;IACvC;IACE;IACAD,MAAM,CAACC,UAAU,CAACC,IAAI,CAACF,MAAM,CAAC,GAChCG,cAAc;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,UAAUA,CAACG,KAAK,EAAE;IACd,IAAI,IAAI,CAACN,SAAS,CAACO,MAAM,IAAI,IAAI,CAACP,SAAS,CAACQ,KAAK,EAAE;MAC/CC,oBAAoB,CAACH,KAAK,EAAE,IAAI,CAACI,MAAM,CAAC;IAC5C;IACA,OAAO,IAAI,CAACX,WAAW,CAACO,KAAK,CAAC;EAClC;AAGJ;AAACK,aAAA,GA3BKhB,YAAY;AAAAE,eAAA,CAAZF,YAAY,wBAAAiB,sBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAyBqFlB,aAAY;AAAA;AAAAE,eAAA,CAzB7GF,YAAY,+BA4B+DtB,EAAE,CAAAyC,kBAAA;EAAAC,KAAA,EAFwBpB,aAAY;EAAAqB,OAAA,EAAZrB,aAAY,CAAAsB,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE3I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF9C,EAAE,CAAA+C,iBAAA,CAAQzB,YAAY,EAAc,CAAC;IAC1G0B,IAAI,EAAE7C,UAAU;IAChB8C,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAST,oBAAoBA,CAACH,KAAK,EAAEiB,KAAK,EAAE;EACxC,IAAI/B,kCAAkC,CAACgC,GAAG,CAAClB,KAAK,CAAC,EAAE;IAC/C;EACJ;EACA,IAAI;IACA,IAAI,CAACZ,mBAAmB,EAAE;MACtBA,mBAAmB,GAAG+B,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MACrD,IAAIH,KAAK,EAAE;QACP7B,mBAAmB,CAACiC,YAAY,CAAC,OAAO,EAAEJ,KAAK,CAAC;MACpD;MACA7B,mBAAmB,CAACiC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;MACpDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACnC,mBAAmB,CAAC;IAClD;IACA,IAAIA,mBAAmB,CAACoC,KAAK,EAAE;MAC3BpC,mBAAmB,CAACoC,KAAK,CAACC,UAAU,CAAC,UAAUzB,KAAK,YAAY,EAAE,CAAC,CAAC;MACpEd,kCAAkC,CAACwC,GAAG,CAAC1B,KAAK,CAAC;IACjD;EACJ,CAAC,CACD,OAAO2B,CAAC,EAAE;IACNC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;EACpB;AACJ;AACA;AACA,SAAS5B,cAAcA,CAACC,KAAK,EAAE;EAC3B;EACA;EACA,OAAO;IACH8B,OAAO,EAAE9B,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,EAAE;IACxC+B,KAAK,EAAE/B,KAAK;IACZgC,WAAW,EAAEA,CAAA,KAAM,CAAE,CAAC;IACtBC,cAAc,EAAEA,CAAA,KAAM,CAAE;EAC5B,CAAC;AACL;;AAEA;AACA,MAAMC,kBAAkB,CAAC;EAOrB5C,WAAWA,CAAA,EAAG;IAAAC,eAAA,wBANEvB,MAAM,CAACqB,YAAY,CAAC;IAAAE,eAAA,gBAC5BvB,MAAM,CAACG,MAAM,CAAC;IACtB;IAAAoB,eAAA,mBACW,IAAI4C,GAAG,CAAC,CAAC;IACpB;IAAA5C,eAAA,0BACkB,IAAInB,OAAO,CAAC,CAAC;EACf;EAChB;EACAgE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,eAAe,CAACC,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACD,eAAe,CAACE,QAAQ,CAAC,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;EACIC,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGC,YAAY,CAAC1D,WAAW,CAACwD,KAAK,CAAC,CAAC;IAChD,OAAOC,OAAO,CAACE,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,cAAc,CAACD,UAAU,CAAC,CAACE,GAAG,CAACjB,OAAO,CAAC;EAClF;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkB,OAAOA,CAACP,KAAK,EAAE;IACX,MAAMC,OAAO,GAAGC,YAAY,CAAC1D,WAAW,CAACwD,KAAK,CAAC,CAAC;IAChD,MAAMQ,WAAW,GAAGP,OAAO,CAAC/D,GAAG,CAACqB,KAAK,IAAI,IAAI,CAAC8C,cAAc,CAAC9C,KAAK,CAAC,CAACkD,UAAU,CAAC;IAC/E,IAAIC,eAAe,GAAG9E,aAAa,CAAC4E,WAAW,CAAC;IAChD;IACAE,eAAe,GAAG7E,MAAM,CAAC6E,eAAe,CAACC,IAAI,CAAC5E,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE2E,eAAe,CAACC,IAAI,CAAC3E,IAAI,CAAC,CAAC,CAAC,EAAEC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACvG,OAAOyE,eAAe,CAACC,IAAI,CAACzE,GAAG,CAAC0E,gBAAgB,IAAI;MAChD,MAAMC,QAAQ,GAAG;QACbxB,OAAO,EAAE,KAAK;QACdyB,WAAW,EAAE,CAAC;MAClB,CAAC;MACDF,gBAAgB,CAACG,OAAO,CAAC,CAAC;QAAE1B,OAAO;QAAE9B;MAAM,CAAC,KAAK;QAC7CsD,QAAQ,CAACxB,OAAO,GAAGwB,QAAQ,CAACxB,OAAO,IAAIA,OAAO;QAC9CwB,QAAQ,CAACC,WAAW,CAACvD,KAAK,CAAC,GAAG8B,OAAO;MACzC,CAAC,CAAC;MACF,OAAOwB,QAAQ;IACnB,CAAC,CAAC,CAAC;EACP;EACA;EACAR,cAAcA,CAAC9C,KAAK,EAAE;IAClB;IACA,IAAI,IAAI,CAACyD,QAAQ,CAACvC,GAAG,CAAClB,KAAK,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACyD,QAAQ,CAACC,GAAG,CAAC1D,KAAK,CAAC;IACnC;IACA,MAAM+C,GAAG,GAAG,IAAI,CAACY,aAAa,CAAC9D,UAAU,CAACG,KAAK,CAAC;IAChD;IACA,MAAM4D,eAAe,GAAG,IAAIrF,UAAU,CAAEsF,QAAQ,IAAK;MACjD;MACA;MACA;MACA;MACA;MACA,MAAMC,OAAO,GAAInC,CAAC,IAAK,IAAI,CAACoC,KAAK,CAACC,GAAG,CAAC,MAAMH,QAAQ,CAACvB,IAAI,CAACX,CAAC,CAAC,CAAC;MAC7DoB,GAAG,CAACf,WAAW,CAAC8B,OAAO,CAAC;MACxB,OAAO,MAAM;QACTf,GAAG,CAACd,cAAc,CAAC6B,OAAO,CAAC;MAC/B,CAAC;IACL,CAAC,CAAC,CAACV,IAAI,CAACxE,SAAS,CAACmE,GAAG,CAAC,EAAEpE,GAAG,CAAC,CAAC;MAAEmD;IAAQ,CAAC,MAAM;MAAE9B,KAAK;MAAE8B;IAAQ,CAAC,CAAC,CAAC,EAAEjD,SAAS,CAAC,IAAI,CAACwD,eAAe,CAAC,CAAC;IACpG;IACA,MAAM4B,MAAM,GAAG;MAAEf,UAAU,EAAEU,eAAe;MAAEb;IAAI,CAAC;IACnD,IAAI,CAACU,QAAQ,CAACS,GAAG,CAAClE,KAAK,EAAEiE,MAAM,CAAC;IAChC,OAAOA,MAAM;EACjB;AAGJ;AAACE,mBAAA,GAzEKjC,kBAAkB;AAAA3C,eAAA,CAAlB2C,kBAAkB,wBAAAkC,4BAAA7D,iBAAA;EAAA,YAAAA,iBAAA,IAuE+E2B,mBAAkB;AAAA;AAAA3C,eAAA,CAvEnH2C,kBAAkB,+BAhDyDnE,EAAE,CAAAyC,kBAAA;EAAAC,KAAA,EAwHwByB,mBAAkB;EAAAxB,OAAA,EAAlBwB,mBAAkB,CAAAvB,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEjJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1HiF9C,EAAE,CAAA+C,iBAAA,CA0HQoB,kBAAkB,EAAc,CAAC;IAChHnB,IAAI,EAAE7C,UAAU;IAChB8C,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,SAAS+B,YAAYA,CAACD,OAAO,EAAE;EAC3B,OAAOA,OAAO,CACT/D,GAAG,CAACqB,KAAK,IAAIA,KAAK,CAACqE,KAAK,CAAC,GAAG,CAAC,CAAC,CAC9BC,MAAM,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,CAACjG,MAAM,CAACkG,EAAE,CAAC,CAAC,CACjC7F,GAAG,CAACqB,KAAK,IAAIA,KAAK,CAACyE,IAAI,CAAC,CAAC,CAAC;AACnC;AAEA,SAASvC,kBAAkB,IAAIwC,CAAC,EAAErF,YAAY,IAAIsF,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}