{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { ReplaySubject } from 'rxjs';\nimport { JwtHelperService } from '@auth0/angular-jwt';\nimport { PermissionService } from './permission.service';\nlet SwHubAuthService = class SwHubAuthService {\n  constructor() {\n    this.logged = new ReplaySubject();\n    this.isSuperAdmin = false;\n    this.isTwoFactor = false;\n    this.jwt = new JwtHelperService();\n  }\n  isLogged() {\n    if (this.expirationDate) {\n      return this.expirationDate > new Date().getTime();\n    }\n    return this.accessToken !== undefined;\n  }\n  tokenGetter() {\n    return () => this.accessToken;\n  }\n  setToken(accessToken, grantedPermissions, twoFactor) {\n    this.logout();\n    if (accessToken) {\n      const token = this.decode(accessToken);\n      const permissions = grantedPermissions ? this.decode(grantedPermissions).permissions : null;\n      if (token) {\n        this.isTwoFactor = !!twoFactor;\n        this.accessToken = accessToken;\n        this.entityKey = `${token.entityId}`;\n        this.username = token.username;\n        this.isSuperAdmin = token.isSuperAdmin || false;\n        this.permissionService = new PermissionService(permissions || token.grantedPermissions || []);\n        if (token.hasOwnProperty('exp')) {\n          const date = new Date(0);\n          date.setUTCSeconds(token.exp);\n          this.expirationDate = date.getTime();\n          console.log(`Token expired at ${date}`);\n        } else {\n          this.expirationDate = undefined;\n        }\n      }\n    }\n    this.logged.next(undefined);\n  }\n  logout() {\n    this.accessToken = undefined;\n    this.entityKey = undefined;\n    this.username = undefined;\n    this.isSuperAdmin = false;\n    this.permissionService = undefined;\n    this.expirationDate = undefined;\n  }\n  getGrantedPermissions() {\n    return this.permissionService ? this.permissionService.permissions : [];\n  }\n  allowedTo(permissions) {\n    return this.permissionService ? this.permissionService.allowedTo(permissions) : false;\n  }\n  areGranted(permissions) {\n    return this.permissionService ? this.permissionService.areGranted(permissions) : false;\n  }\n  decode(token) {\n    try {\n      return this.jwt.decodeToken(token);\n    } catch (e) {\n      console.error(e);\n    }\n    return null;\n  }\n};\nSwHubAuthService = __decorate([Injectable()], SwHubAuthService);\nexport { SwHubAuthService };", "map": {"version": 3, "names": ["__decorate", "Injectable", "ReplaySubject", "JwtHelperService", "PermissionService", "SwHubAuthService", "constructor", "logged", "isSuperAdmin", "isTwoFactor", "jwt", "isLogged", "expirationDate", "Date", "getTime", "accessToken", "undefined", "tokenGetter", "setToken", "grantedPermissions", "twoFactor", "logout", "token", "decode", "permissions", "entityKey", "entityId", "username", "permissionService", "hasOwnProperty", "date", "setUTCSeconds", "exp", "console", "log", "next", "getGrantedPermissions", "allowedTo", "areGranted", "decodeToken", "e", "error"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-auth/sw-hub-auth.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { ReplaySubject } from 'rxjs';\nimport { JwtHelperService } from '@auth0/angular-jwt';\nimport { PermissionService } from './permission.service';\nlet SwHubAuthService = class SwHubAuthService {\n    constructor() {\n        this.logged = new ReplaySubject();\n        this.isSuperAdmin = false;\n        this.isTwoFactor = false;\n        this.jwt = new JwtHelperService();\n    }\n    isLogged() {\n        if (this.expirationDate) {\n            return this.expirationDate > new Date().getTime();\n        }\n        return this.accessToken !== undefined;\n    }\n    tokenGetter() {\n        return () => this.accessToken;\n    }\n    setToken(accessToken, grantedPermissions, twoFactor) {\n        this.logout();\n        if (accessToken) {\n            const token = this.decode(accessToken);\n            const permissions = grantedPermissions ? this.decode(grantedPermissions).permissions : null;\n            if (token) {\n                this.isTwoFactor = !!twoFactor;\n                this.accessToken = accessToken;\n                this.entityKey = `${token.entityId}`;\n                this.username = token.username;\n                this.isSuperAdmin = token.isSuperAdmin || false;\n                this.permissionService = new PermissionService(permissions || token.grantedPermissions || []);\n                if (token.hasOwnProperty('exp')) {\n                    const date = new Date(0);\n                    date.setUTCSeconds(token.exp);\n                    this.expirationDate = date.getTime();\n                    console.log(`Token expired at ${date}`);\n                }\n                else {\n                    this.expirationDate = undefined;\n                }\n            }\n        }\n        this.logged.next(undefined);\n    }\n    logout() {\n        this.accessToken = undefined;\n        this.entityKey = undefined;\n        this.username = undefined;\n        this.isSuperAdmin = false;\n        this.permissionService = undefined;\n        this.expirationDate = undefined;\n    }\n    getGrantedPermissions() {\n        return this.permissionService ? this.permissionService.permissions : [];\n    }\n    allowedTo(permissions) {\n        return this.permissionService ? this.permissionService.allowedTo(permissions) : false;\n    }\n    areGranted(permissions) {\n        return this.permissionService ? this.permissionService.areGranted(permissions) : false;\n    }\n    decode(token) {\n        try {\n            return this.jwt.decodeToken(token);\n        }\n        catch (e) {\n            console.error(e);\n        }\n        return null;\n    }\n};\nSwHubAuthService = __decorate([\n    Injectable()\n], SwHubAuthService);\nexport { SwHubAuthService };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,aAAa,QAAQ,MAAM;AACpC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,IAAIC,gBAAgB,GAAG,MAAMA,gBAAgB,CAAC;EAC1CC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,IAAIL,aAAa,CAAC,CAAC;IACjC,IAAI,CAACM,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,GAAG,GAAG,IAAIP,gBAAgB,CAAC,CAAC;EACrC;EACAQ,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,cAAc,EAAE;MACrB,OAAO,IAAI,CAACA,cAAc,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACrD;IACA,OAAO,IAAI,CAACC,WAAW,KAAKC,SAAS;EACzC;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,MAAM,IAAI,CAACF,WAAW;EACjC;EACAG,QAAQA,CAACH,WAAW,EAAEI,kBAAkB,EAAEC,SAAS,EAAE;IACjD,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,IAAIN,WAAW,EAAE;MACb,MAAMO,KAAK,GAAG,IAAI,CAACC,MAAM,CAACR,WAAW,CAAC;MACtC,MAAMS,WAAW,GAAGL,kBAAkB,GAAG,IAAI,CAACI,MAAM,CAACJ,kBAAkB,CAAC,CAACK,WAAW,GAAG,IAAI;MAC3F,IAAIF,KAAK,EAAE;QACP,IAAI,CAACb,WAAW,GAAG,CAAC,CAACW,SAAS;QAC9B,IAAI,CAACL,WAAW,GAAGA,WAAW;QAC9B,IAAI,CAACU,SAAS,GAAG,GAAGH,KAAK,CAACI,QAAQ,EAAE;QACpC,IAAI,CAACC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;QAC9B,IAAI,CAACnB,YAAY,GAAGc,KAAK,CAACd,YAAY,IAAI,KAAK;QAC/C,IAAI,CAACoB,iBAAiB,GAAG,IAAIxB,iBAAiB,CAACoB,WAAW,IAAIF,KAAK,CAACH,kBAAkB,IAAI,EAAE,CAAC;QAC7F,IAAIG,KAAK,CAACO,cAAc,CAAC,KAAK,CAAC,EAAE;UAC7B,MAAMC,IAAI,GAAG,IAAIjB,IAAI,CAAC,CAAC,CAAC;UACxBiB,IAAI,CAACC,aAAa,CAACT,KAAK,CAACU,GAAG,CAAC;UAC7B,IAAI,CAACpB,cAAc,GAAGkB,IAAI,CAAChB,OAAO,CAAC,CAAC;UACpCmB,OAAO,CAACC,GAAG,CAAC,oBAAoBJ,IAAI,EAAE,CAAC;QAC3C,CAAC,MACI;UACD,IAAI,CAAClB,cAAc,GAAGI,SAAS;QACnC;MACJ;IACJ;IACA,IAAI,CAACT,MAAM,CAAC4B,IAAI,CAACnB,SAAS,CAAC;EAC/B;EACAK,MAAMA,CAAA,EAAG;IACL,IAAI,CAACN,WAAW,GAAGC,SAAS;IAC5B,IAAI,CAACS,SAAS,GAAGT,SAAS;IAC1B,IAAI,CAACW,QAAQ,GAAGX,SAAS;IACzB,IAAI,CAACR,YAAY,GAAG,KAAK;IACzB,IAAI,CAACoB,iBAAiB,GAAGZ,SAAS;IAClC,IAAI,CAACJ,cAAc,GAAGI,SAAS;EACnC;EACAoB,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACR,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACJ,WAAW,GAAG,EAAE;EAC3E;EACAa,SAASA,CAACb,WAAW,EAAE;IACnB,OAAO,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACS,SAAS,CAACb,WAAW,CAAC,GAAG,KAAK;EACzF;EACAc,UAAUA,CAACd,WAAW,EAAE;IACpB,OAAO,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACU,UAAU,CAACd,WAAW,CAAC,GAAG,KAAK;EAC1F;EACAD,MAAMA,CAACD,KAAK,EAAE;IACV,IAAI;MACA,OAAO,IAAI,CAACZ,GAAG,CAAC6B,WAAW,CAACjB,KAAK,CAAC;IACtC,CAAC,CACD,OAAOkB,CAAC,EAAE;MACNP,OAAO,CAACQ,KAAK,CAACD,CAAC,CAAC;IACpB;IACA,OAAO,IAAI;EACf;AACJ,CAAC;AACDnC,gBAAgB,GAAGL,UAAU,CAAC,CAC1BC,UAAU,CAAC,CAAC,CACf,EAAEI,gBAAgB,CAAC;AACpB,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}