{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _CdkTextFieldStyleLoader2, _AutofillMonitor, _CdkAutofill, _CdkTextareaAutosize, _TextFieldModule;\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, inject, NgZone, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output, Renderer2, booleanAttribute, Input, NgModule } from '@angular/core';\nimport { EMPTY, Subject } from 'rxjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { auditTime } from 'rxjs/operators';\n\n/** Component used to load the structural styles of the text field. */\nclass _CdkTextFieldStyleLoader {}\n_CdkTextFieldStyleLoader2 = _CdkTextFieldStyleLoader;\n_defineProperty(_CdkTextFieldStyleLoader, \"\\u0275fac\", function _CdkTextFieldStyleLoader2_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTextFieldStyleLoader2)();\n});\n_defineProperty(_CdkTextFieldStyleLoader, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _CdkTextFieldStyleLoader2,\n  selectors: [[\"ng-component\"]],\n  hostAttrs: [\"cdk-text-field-style-loader\", \"\"],\n  decls: 0,\n  vars: 0,\n  template: function _CdkTextFieldStyleLoader2_Template(rf, ctx) {},\n  styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkTextFieldStyleLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'cdk-text-field-style-loader': ''\n      },\n      styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = {\n  passive: true\n};\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n  constructor() {\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_renderer\", inject(RendererFactory2).createRenderer(null, null));\n    _defineProperty(this, \"_styleLoader\", inject(_CdkPrivateStyleLoader));\n    _defineProperty(this, \"_monitoredElements\", new Map());\n  }\n  monitor(elementOrRef) {\n    if (!this._platform.isBrowser) {\n      return EMPTY;\n    }\n    this._styleLoader.load(_CdkTextFieldStyleLoader);\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      return info.subject;\n    }\n    const subject = new Subject();\n    const cssClass = 'cdk-text-field-autofilled';\n    const listener = event => {\n      // Animation events fire on initial element render, we check for the presence of the autofill\n      // CSS class to make sure this is a real change in state, not just the initial render before\n      // we fire off events.\n      if (event.animationName === 'cdk-text-field-autofill-start' && !element.classList.contains(cssClass)) {\n        element.classList.add(cssClass);\n        this._ngZone.run(() => subject.next({\n          target: event.target,\n          isAutofilled: true\n        }));\n      } else if (event.animationName === 'cdk-text-field-autofill-end' && element.classList.contains(cssClass)) {\n        element.classList.remove(cssClass);\n        this._ngZone.run(() => subject.next({\n          target: event.target,\n          isAutofilled: false\n        }));\n      }\n    };\n    const unlisten = this._ngZone.runOutsideAngular(() => {\n      element.classList.add('cdk-text-field-autofill-monitored');\n      return _bindEventWithOptions(this._renderer, element, 'animationstart', listener, listenerOptions);\n    });\n    this._monitoredElements.set(element, {\n      subject,\n      unlisten\n    });\n    return subject;\n  }\n  stopMonitoring(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      info.unlisten();\n      info.subject.complete();\n      element.classList.remove('cdk-text-field-autofill-monitored');\n      element.classList.remove('cdk-text-field-autofilled');\n      this._monitoredElements.delete(element);\n    }\n  }\n  ngOnDestroy() {\n    this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n  }\n}\n_AutofillMonitor = AutofillMonitor;\n_defineProperty(AutofillMonitor, \"\\u0275fac\", function _AutofillMonitor_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AutofillMonitor)();\n});\n_defineProperty(AutofillMonitor, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _AutofillMonitor,\n  factory: _AutofillMonitor.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutofillMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_autofillMonitor\", inject(AutofillMonitor));\n    /** Emits when the autofill state of the element changes. */\n    _defineProperty(this, \"cdkAutofill\", new EventEmitter());\n  }\n  ngOnInit() {\n    this._autofillMonitor.monitor(this._elementRef).subscribe(event => this.cdkAutofill.emit(event));\n  }\n  ngOnDestroy() {\n    this._autofillMonitor.stopMonitoring(this._elementRef);\n  }\n}\n_CdkAutofill = CdkAutofill;\n_defineProperty(CdkAutofill, \"\\u0275fac\", function _CdkAutofill_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkAutofill)();\n});\n_defineProperty(CdkAutofill, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkAutofill,\n  selectors: [[\"\", \"cdkAutofill\", \"\"]],\n  outputs: {\n    cdkAutofill: \"cdkAutofill\"\n  }\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAutofill, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAutofill]'\n    }]\n  }], () => [], {\n    cdkAutofill: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n  /** Minimum amount of rows in the textarea. */\n  get minRows() {\n    return this._minRows;\n  }\n  set minRows(value) {\n    this._minRows = coerceNumberProperty(value);\n    this._setMinHeight();\n  }\n  /** Maximum amount of rows in the textarea. */\n  get maxRows() {\n    return this._maxRows;\n  }\n  set maxRows(value) {\n    this._maxRows = coerceNumberProperty(value);\n    this._setMaxHeight();\n  }\n  /** Whether autosizing is enabled or not */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    // Only act if the actual value changed. This specifically helps to not run\n    // resizeToFitContent too early (i.e. before ngAfterViewInit)\n    if (this._enabled !== value) {\n      (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n    }\n  }\n  get placeholder() {\n    return this._textareaElement.placeholder;\n  }\n  set placeholder(value) {\n    this._cachedPlaceholderHeight = undefined;\n    if (value) {\n      this._textareaElement.setAttribute('placeholder', value);\n    } else {\n      this._textareaElement.removeAttribute('placeholder');\n    }\n    this._cacheTextareaPlaceholderHeight();\n  }\n  /** Cached height of a textarea with a single row. */\n\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_renderer\", inject(Renderer2));\n    _defineProperty(this, \"_resizeEvents\", new Subject());\n    /** Keep track of the previous textarea value to avoid resizing when the value hasn't changed. */\n    _defineProperty(this, \"_previousValue\", void 0);\n    _defineProperty(this, \"_initialHeight\", void 0);\n    _defineProperty(this, \"_destroyed\", new Subject());\n    _defineProperty(this, \"_listenerCleanups\", void 0);\n    _defineProperty(this, \"_minRows\", void 0);\n    _defineProperty(this, \"_maxRows\", void 0);\n    _defineProperty(this, \"_enabled\", true);\n    /**\n     * Value of minRows as of last resize. If the minRows has decreased, the\n     * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n     * does not have the same problem because it does not affect the textarea's scrollHeight.\n     */\n    _defineProperty(this, \"_previousMinRows\", -1);\n    _defineProperty(this, \"_textareaElement\", void 0);\n    _defineProperty(this, \"_cachedLineHeight\", void 0);\n    /** Cached height of a textarea with only the placeholder. */\n    _defineProperty(this, \"_cachedPlaceholderHeight\", void 0);\n    /** Cached scroll top of a textarea */\n    _defineProperty(this, \"_cachedScrollTop\", void 0);\n    /** Used to reference correct document/window */\n    _defineProperty(this, \"_document\", inject(DOCUMENT, {\n      optional: true\n    }));\n    _defineProperty(this, \"_hasFocus\", void 0);\n    _defineProperty(this, \"_isViewInited\", false);\n    /** Handles `focus` and `blur` events. */\n    _defineProperty(this, \"_handleFocusEvent\", event => {\n      this._hasFocus = event.type === 'focus';\n    });\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_CdkTextFieldStyleLoader);\n    this._textareaElement = this._elementRef.nativeElement;\n  }\n  /** Sets the minimum height of the textarea as determined by minRows. */\n  _setMinHeight() {\n    const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n    if (minHeight) {\n      this._textareaElement.style.minHeight = minHeight;\n    }\n  }\n  /** Sets the maximum height of the textarea as determined by maxRows. */\n  _setMaxHeight() {\n    const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n    if (maxHeight) {\n      this._textareaElement.style.maxHeight = maxHeight;\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      // Remember the height which we started with in case autosizing is disabled\n      this._initialHeight = this._textareaElement.style.height;\n      this.resizeToFitContent();\n      this._ngZone.runOutsideAngular(() => {\n        this._listenerCleanups = [this._renderer.listen('window', 'resize', () => this._resizeEvents.next()), this._renderer.listen(this._textareaElement, 'focus', this._handleFocusEvent), this._renderer.listen(this._textareaElement, 'blur', this._handleFocusEvent)];\n        this._resizeEvents.pipe(auditTime(16)).subscribe(() => {\n          // Clear the cached heights since the styles can change\n          // when the window is resized (e.g. by media queries).\n          this._cachedLineHeight = this._cachedPlaceholderHeight = undefined;\n          this.resizeToFitContent(true);\n        });\n      });\n      this._isViewInited = true;\n      this.resizeToFitContent(true);\n    }\n  }\n  ngOnDestroy() {\n    var _this$_listenerCleanu;\n    (_this$_listenerCleanu = this._listenerCleanups) === null || _this$_listenerCleanu === void 0 || _this$_listenerCleanu.forEach(cleanup => cleanup());\n    this._resizeEvents.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Cache the height of a single-row textarea if it has not already been cached.\n   *\n   * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n   * maxRows. For the initial version, we will assume that the height of a single line in the\n   * textarea does not ever change.\n   */\n  _cacheTextareaLineHeight() {\n    if (this._cachedLineHeight) {\n      return;\n    }\n    // Use a clone element because we have to override some styles.\n    const textareaClone = this._textareaElement.cloneNode(false);\n    const cloneStyles = textareaClone.style;\n    textareaClone.rows = 1;\n    // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n    cloneStyles.position = 'absolute';\n    cloneStyles.visibility = 'hidden';\n    cloneStyles.border = 'none';\n    cloneStyles.padding = '0';\n    cloneStyles.height = '';\n    cloneStyles.minHeight = '';\n    cloneStyles.maxHeight = '';\n    // App styles might be messing with the height through the positioning properties.\n    cloneStyles.top = cloneStyles.bottom = cloneStyles.left = cloneStyles.right = 'auto';\n    // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n    cloneStyles.overflow = 'hidden';\n    this._textareaElement.parentNode.appendChild(textareaClone);\n    this._cachedLineHeight = textareaClone.clientHeight;\n    textareaClone.remove();\n    // Min and max heights have to be re-calculated if the cached line height changes\n    this._setMinHeight();\n    this._setMaxHeight();\n  }\n  _measureScrollHeight() {\n    const element = this._textareaElement;\n    const previousMargin = element.style.marginBottom || '';\n    const isFirefox = this._platform.FIREFOX;\n    const needsMarginFiller = isFirefox && this._hasFocus;\n    const measuringClass = isFirefox ? 'cdk-textarea-autosize-measuring-firefox' : 'cdk-textarea-autosize-measuring';\n    // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n    // work around it by assigning a temporary margin with the same height as the `textarea` so that\n    // it occupies the same amount of space. See #23233.\n    if (needsMarginFiller) {\n      element.style.marginBottom = `${element.clientHeight}px`;\n    }\n    // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n    element.classList.add(measuringClass);\n    // The measuring class includes a 2px padding to workaround an issue with Chrome,\n    // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n    const scrollHeight = element.scrollHeight - 4;\n    element.classList.remove(measuringClass);\n    if (needsMarginFiller) {\n      element.style.marginBottom = previousMargin;\n    }\n    return scrollHeight;\n  }\n  _cacheTextareaPlaceholderHeight() {\n    if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n      return;\n    }\n    if (!this.placeholder) {\n      this._cachedPlaceholderHeight = 0;\n      return;\n    }\n    const value = this._textareaElement.value;\n    this._textareaElement.value = this._textareaElement.placeholder;\n    this._cachedPlaceholderHeight = this._measureScrollHeight();\n    this._textareaElement.value = value;\n  }\n  ngDoCheck() {\n    if (this._platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n  /**\n   * Resize the textarea to fit its content.\n   * @param force Whether to force a height recalculation. By default the height will be\n   *    recalculated only if the value changed since the last call.\n   */\n  resizeToFitContent(force = false) {\n    // If autosizing is disabled, just skip everything else\n    if (!this._enabled) {\n      return;\n    }\n    this._cacheTextareaLineHeight();\n    this._cacheTextareaPlaceholderHeight();\n    this._cachedScrollTop = this._textareaElement.scrollTop;\n    // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n    if (!this._cachedLineHeight) {\n      return;\n    }\n    const textarea = this._elementRef.nativeElement;\n    const value = textarea.value;\n    // Only resize if the value or minRows have changed since these calculations can be expensive.\n    if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n      return;\n    }\n    const scrollHeight = this._measureScrollHeight();\n    const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n    // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n    textarea.style.height = `${height}px`;\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame !== 'undefined') {\n        requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n      } else {\n        setTimeout(() => this._scrollToCaretPosition(textarea));\n      }\n    });\n    this._previousValue = value;\n    this._previousMinRows = this._minRows;\n  }\n  /**\n   * Resets the textarea to its original size\n   */\n  reset() {\n    // Do not try to change the textarea, if the initialHeight has not been determined yet\n    // This might potentially remove styles when reset() is called before ngAfterViewInit\n    if (this._initialHeight !== undefined) {\n      this._textareaElement.style.height = this._initialHeight;\n    }\n  }\n  _noopInputHandler() {\n    // no-op handler that ensures we're running change detection on input events.\n  }\n  /**\n   * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n   * prevent it from scrolling to the caret position. We need to re-set the selection\n   * in order for it to scroll to the proper position.\n   */\n  _scrollToCaretPosition(textarea) {\n    const {\n      selectionStart,\n      selectionEnd\n    } = textarea;\n    // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n    // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n    // between the time we requested the animation frame and when it was executed.\n    // Also note that we have to assert that the textarea is focused before we set the\n    // selection range. Setting the selection range on a non-focused textarea will cause\n    // it to receive focus on IE and Edge.\n    if (!this._destroyed.isStopped && this._hasFocus) {\n      textarea.setSelectionRange(selectionStart, selectionEnd);\n      textarea.scrollTop = this._cachedScrollTop;\n    }\n  }\n}\n_CdkTextareaAutosize = CdkTextareaAutosize;\n_defineProperty(CdkTextareaAutosize, \"\\u0275fac\", function _CdkTextareaAutosize_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTextareaAutosize)();\n});\n_defineProperty(CdkTextareaAutosize, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkTextareaAutosize,\n  selectors: [[\"textarea\", \"cdkTextareaAutosize\", \"\"]],\n  hostAttrs: [\"rows\", \"1\", 1, \"cdk-textarea-autosize\"],\n  hostBindings: function _CdkTextareaAutosize_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"input\", function _CdkTextareaAutosize_input_HostBindingHandler() {\n        return ctx._noopInputHandler();\n      });\n    }\n  },\n  inputs: {\n    minRows: [0, \"cdkAutosizeMinRows\", \"minRows\"],\n    maxRows: [0, \"cdkAutosizeMaxRows\", \"maxRows\"],\n    enabled: [2, \"cdkTextareaAutosize\", \"enabled\", booleanAttribute],\n    placeholder: \"placeholder\"\n  },\n  exportAs: [\"cdkTextareaAutosize\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextareaAutosize, [{\n    type: Directive,\n    args: [{\n      selector: 'textarea[cdkTextareaAutosize]',\n      exportAs: 'cdkTextareaAutosize',\n      host: {\n        'class': 'cdk-textarea-autosize',\n        // Textarea elements that have the directive applied should have a single row by default.\n        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n        'rows': '1',\n        '(input)': '_noopInputHandler()'\n      }\n    }]\n  }], () => [], {\n    minRows: [{\n      type: Input,\n      args: ['cdkAutosizeMinRows']\n    }],\n    maxRows: [{\n      type: Input,\n      args: ['cdkAutosizeMaxRows']\n    }],\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTextareaAutosize',\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }]\n  });\n})();\nclass TextFieldModule {}\n_TextFieldModule = TextFieldModule;\n_defineProperty(TextFieldModule, \"\\u0275fac\", function _TextFieldModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TextFieldModule)();\n});\n_defineProperty(TextFieldModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _TextFieldModule,\n  imports: [CdkAutofill, CdkTextareaAutosize],\n  exports: [CdkAutofill, CdkTextareaAutosize]\n}));\n_defineProperty(TextFieldModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextFieldModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkAutofill, CdkTextareaAutosize],\n      exports: [CdkAutofill, CdkTextareaAutosize]\n    }]\n  }], null, null);\n})();\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n//# sourceMappingURL=text-field.mjs.map", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "inject", "NgZone", "RendererFactory2", "Injectable", "ElementRef", "EventEmitter", "Directive", "Output", "Renderer2", "booleanAttribute", "Input", "NgModule", "EMPTY", "Subject", "P", "Platform", "_", "_CdkPrivateStyleLoader", "_bindEventWithOptions", "a", "coerceElement", "c", "coerceNumberProperty", "DOCUMENT", "auditTime", "_CdkTextFieldStyleLoader", "_CdkTextFieldStyleLoader2", "_defineProperty", "_CdkTextFieldStyleLoader2_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "decls", "vars", "template", "_CdkTextFieldStyleLoader2_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "OnPush", "None", "host", "listenerOptions", "passive", "AutofillMonitor", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "Map", "monitor", "elementOrRef", "_platform", "<PERSON><PERSON><PERSON><PERSON>", "_styleLoader", "load", "element", "info", "_monitoredElements", "get", "subject", "cssClass", "listener", "event", "animationName", "classList", "contains", "add", "_ngZone", "run", "next", "target", "isAutofilled", "remove", "unlisten", "runOutsideAngular", "_renderer", "set", "stopMonitoring", "complete", "delete", "ngOnDestroy", "for<PERSON>ach", "_info", "_AutofillMonitor", "_AutofillMonitor_Factory", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "CdkAutofill", "ngOnInit", "_autofillMonitor", "_elementRef", "subscribe", "cdkAutofill", "emit", "_CdkAutofill", "_CdkAutofill_Factory", "ɵɵdefineDirective", "outputs", "selector", "CdkTextareaAutosize", "minRows", "_minRows", "value", "_setMinHeight", "maxRows", "_maxRows", "_setMaxHeight", "enabled", "_enabled", "resizeToFitContent", "reset", "placeholder", "_textareaElement", "_cachedPlaceholderHeight", "undefined", "setAttribute", "removeAttribute", "_cacheTextareaPlaceholderHeight", "optional", "_hasFocus", "<PERSON><PERSON><PERSON><PERSON>", "nativeElement", "minHeight", "_cachedLineHeight", "style", "maxHeight", "ngAfterViewInit", "_initialHeight", "height", "_listenerCleanups", "listen", "_resizeEvents", "_handleFocusEvent", "pipe", "_isViewInited", "_this$_listenerCleanu", "cleanup", "_destroyed", "_cacheTextareaLineHeight", "textareaClone", "cloneNode", "cloneStyles", "rows", "position", "visibility", "border", "padding", "top", "bottom", "left", "right", "overflow", "parentNode", "append<PERSON><PERSON><PERSON>", "clientHeight", "_measureScrollHeight", "<PERSON><PERSON><PERSON><PERSON>", "marginBottom", "isFirefox", "FIREFOX", "needsMarginFiller", "measuringClass", "scrollHeight", "ngDoCheck", "force", "_cachedScrollTop", "scrollTop", "textarea", "_previousMinRows", "_previousValue", "Math", "max", "requestAnimationFrame", "_scrollToCaretPosition", "setTimeout", "_noopInputHandler", "selectionStart", "selectionEnd", "isStopped", "setSelectionRange", "_CdkTextareaAutosize", "_CdkTextareaAutosize_Factory", "hostBindings", "_CdkTextareaAutosize_HostBindings", "ɵɵlistener", "_CdkTextareaAutosize_input_HostBindingHandler", "inputs", "exportAs", "alias", "transform", "TextFieldModule", "_TextFieldModule", "_TextFieldModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/text-field.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, inject, NgZone, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output, Renderer2, booleanAttribute, Input, NgModule } from '@angular/core';\nimport { EMPTY, Subject } from 'rxjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { auditTime } from 'rxjs/operators';\n\n/** Component used to load the structural styles of the text field. */\nclass _CdkTextFieldStyleLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkTextFieldStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: _CdkTextFieldStyleLoader, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"cdk-text-field-style-loader\": \"\" } }, ngImport: i0, template: '', isInline: true, styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkTextFieldStyleLoader, decorators: [{\n            type: Component,\n            args: [{ template: '', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: { 'cdk-text-field-style-loader': '' }, styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\\n\"] }]\n        }] });\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = { passive: true };\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _styleLoader = inject(_CdkPrivateStyleLoader);\n    _monitoredElements = new Map();\n    constructor() { }\n    monitor(elementOrRef) {\n        if (!this._platform.isBrowser) {\n            return EMPTY;\n        }\n        this._styleLoader.load(_CdkTextFieldStyleLoader);\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            return info.subject;\n        }\n        const subject = new Subject();\n        const cssClass = 'cdk-text-field-autofilled';\n        const listener = (event) => {\n            // Animation events fire on initial element render, we check for the presence of the autofill\n            // CSS class to make sure this is a real change in state, not just the initial render before\n            // we fire off events.\n            if (event.animationName === 'cdk-text-field-autofill-start' &&\n                !element.classList.contains(cssClass)) {\n                element.classList.add(cssClass);\n                this._ngZone.run(() => subject.next({ target: event.target, isAutofilled: true }));\n            }\n            else if (event.animationName === 'cdk-text-field-autofill-end' &&\n                element.classList.contains(cssClass)) {\n                element.classList.remove(cssClass);\n                this._ngZone.run(() => subject.next({ target: event.target, isAutofilled: false }));\n            }\n        };\n        const unlisten = this._ngZone.runOutsideAngular(() => {\n            element.classList.add('cdk-text-field-autofill-monitored');\n            return _bindEventWithOptions(this._renderer, element, 'animationstart', listener, listenerOptions);\n        });\n        this._monitoredElements.set(element, { subject, unlisten });\n        return subject;\n    }\n    stopMonitoring(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            info.unlisten();\n            info.subject.complete();\n            element.classList.remove('cdk-text-field-autofill-monitored');\n            element.classList.remove('cdk-text-field-autofilled');\n            this._monitoredElements.delete(element);\n        }\n    }\n    ngOnDestroy() {\n        this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: AutofillMonitor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: AutofillMonitor, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: AutofillMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n    _elementRef = inject(ElementRef);\n    _autofillMonitor = inject(AutofillMonitor);\n    /** Emits when the autofill state of the element changes. */\n    cdkAutofill = new EventEmitter();\n    constructor() { }\n    ngOnInit() {\n        this._autofillMonitor\n            .monitor(this._elementRef)\n            .subscribe(event => this.cdkAutofill.emit(event));\n    }\n    ngOnDestroy() {\n        this._autofillMonitor.stopMonitoring(this._elementRef);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAutofill, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkAutofill, isStandalone: true, selector: \"[cdkAutofill]\", outputs: { cdkAutofill: \"cdkAutofill\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAutofill, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAutofill]',\n                }]\n        }], ctorParameters: () => [], propDecorators: { cdkAutofill: [{\n                type: Output\n            }] } });\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n    _elementRef = inject(ElementRef);\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(Renderer2);\n    _resizeEvents = new Subject();\n    /** Keep track of the previous textarea value to avoid resizing when the value hasn't changed. */\n    _previousValue;\n    _initialHeight;\n    _destroyed = new Subject();\n    _listenerCleanups;\n    _minRows;\n    _maxRows;\n    _enabled = true;\n    /**\n     * Value of minRows as of last resize. If the minRows has decreased, the\n     * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n     * does not have the same problem because it does not affect the textarea's scrollHeight.\n     */\n    _previousMinRows = -1;\n    _textareaElement;\n    /** Minimum amount of rows in the textarea. */\n    get minRows() {\n        return this._minRows;\n    }\n    set minRows(value) {\n        this._minRows = coerceNumberProperty(value);\n        this._setMinHeight();\n    }\n    /** Maximum amount of rows in the textarea. */\n    get maxRows() {\n        return this._maxRows;\n    }\n    set maxRows(value) {\n        this._maxRows = coerceNumberProperty(value);\n        this._setMaxHeight();\n    }\n    /** Whether autosizing is enabled or not */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        // Only act if the actual value changed. This specifically helps to not run\n        // resizeToFitContent too early (i.e. before ngAfterViewInit)\n        if (this._enabled !== value) {\n            (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n        }\n    }\n    get placeholder() {\n        return this._textareaElement.placeholder;\n    }\n    set placeholder(value) {\n        this._cachedPlaceholderHeight = undefined;\n        if (value) {\n            this._textareaElement.setAttribute('placeholder', value);\n        }\n        else {\n            this._textareaElement.removeAttribute('placeholder');\n        }\n        this._cacheTextareaPlaceholderHeight();\n    }\n    /** Cached height of a textarea with a single row. */\n    _cachedLineHeight;\n    /** Cached height of a textarea with only the placeholder. */\n    _cachedPlaceholderHeight;\n    /** Cached scroll top of a textarea */\n    _cachedScrollTop;\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, { optional: true });\n    _hasFocus;\n    _isViewInited = false;\n    constructor() {\n        const styleLoader = inject(_CdkPrivateStyleLoader);\n        styleLoader.load(_CdkTextFieldStyleLoader);\n        this._textareaElement = this._elementRef.nativeElement;\n    }\n    /** Sets the minimum height of the textarea as determined by minRows. */\n    _setMinHeight() {\n        const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n        if (minHeight) {\n            this._textareaElement.style.minHeight = minHeight;\n        }\n    }\n    /** Sets the maximum height of the textarea as determined by maxRows. */\n    _setMaxHeight() {\n        const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n        if (maxHeight) {\n            this._textareaElement.style.maxHeight = maxHeight;\n        }\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            // Remember the height which we started with in case autosizing is disabled\n            this._initialHeight = this._textareaElement.style.height;\n            this.resizeToFitContent();\n            this._ngZone.runOutsideAngular(() => {\n                this._listenerCleanups = [\n                    this._renderer.listen('window', 'resize', () => this._resizeEvents.next()),\n                    this._renderer.listen(this._textareaElement, 'focus', this._handleFocusEvent),\n                    this._renderer.listen(this._textareaElement, 'blur', this._handleFocusEvent),\n                ];\n                this._resizeEvents.pipe(auditTime(16)).subscribe(() => {\n                    // Clear the cached heights since the styles can change\n                    // when the window is resized (e.g. by media queries).\n                    this._cachedLineHeight = this._cachedPlaceholderHeight = undefined;\n                    this.resizeToFitContent(true);\n                });\n            });\n            this._isViewInited = true;\n            this.resizeToFitContent(true);\n        }\n    }\n    ngOnDestroy() {\n        this._listenerCleanups?.forEach(cleanup => cleanup());\n        this._resizeEvents.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Cache the height of a single-row textarea if it has not already been cached.\n     *\n     * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n     * maxRows. For the initial version, we will assume that the height of a single line in the\n     * textarea does not ever change.\n     */\n    _cacheTextareaLineHeight() {\n        if (this._cachedLineHeight) {\n            return;\n        }\n        // Use a clone element because we have to override some styles.\n        const textareaClone = this._textareaElement.cloneNode(false);\n        const cloneStyles = textareaClone.style;\n        textareaClone.rows = 1;\n        // Use `position: absolute` so that this doesn't cause a browser layout and use\n        // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n        // would affect the height.\n        cloneStyles.position = 'absolute';\n        cloneStyles.visibility = 'hidden';\n        cloneStyles.border = 'none';\n        cloneStyles.padding = '0';\n        cloneStyles.height = '';\n        cloneStyles.minHeight = '';\n        cloneStyles.maxHeight = '';\n        // App styles might be messing with the height through the positioning properties.\n        cloneStyles.top = cloneStyles.bottom = cloneStyles.left = cloneStyles.right = 'auto';\n        // In Firefox it happens that textarea elements are always bigger than the specified amount\n        // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n        // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n        // to hidden. This ensures that there is no invalid calculation of the line height.\n        // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n        cloneStyles.overflow = 'hidden';\n        this._textareaElement.parentNode.appendChild(textareaClone);\n        this._cachedLineHeight = textareaClone.clientHeight;\n        textareaClone.remove();\n        // Min and max heights have to be re-calculated if the cached line height changes\n        this._setMinHeight();\n        this._setMaxHeight();\n    }\n    _measureScrollHeight() {\n        const element = this._textareaElement;\n        const previousMargin = element.style.marginBottom || '';\n        const isFirefox = this._platform.FIREFOX;\n        const needsMarginFiller = isFirefox && this._hasFocus;\n        const measuringClass = isFirefox\n            ? 'cdk-textarea-autosize-measuring-firefox'\n            : 'cdk-textarea-autosize-measuring';\n        // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n        // work around it by assigning a temporary margin with the same height as the `textarea` so that\n        // it occupies the same amount of space. See #23233.\n        if (needsMarginFiller) {\n            element.style.marginBottom = `${element.clientHeight}px`;\n        }\n        // Reset the textarea height to auto in order to shrink back to its default size.\n        // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n        element.classList.add(measuringClass);\n        // The measuring class includes a 2px padding to workaround an issue with Chrome,\n        // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n        const scrollHeight = element.scrollHeight - 4;\n        element.classList.remove(measuringClass);\n        if (needsMarginFiller) {\n            element.style.marginBottom = previousMargin;\n        }\n        return scrollHeight;\n    }\n    _cacheTextareaPlaceholderHeight() {\n        if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n            return;\n        }\n        if (!this.placeholder) {\n            this._cachedPlaceholderHeight = 0;\n            return;\n        }\n        const value = this._textareaElement.value;\n        this._textareaElement.value = this._textareaElement.placeholder;\n        this._cachedPlaceholderHeight = this._measureScrollHeight();\n        this._textareaElement.value = value;\n    }\n    /** Handles `focus` and `blur` events. */\n    _handleFocusEvent = (event) => {\n        this._hasFocus = event.type === 'focus';\n    };\n    ngDoCheck() {\n        if (this._platform.isBrowser) {\n            this.resizeToFitContent();\n        }\n    }\n    /**\n     * Resize the textarea to fit its content.\n     * @param force Whether to force a height recalculation. By default the height will be\n     *    recalculated only if the value changed since the last call.\n     */\n    resizeToFitContent(force = false) {\n        // If autosizing is disabled, just skip everything else\n        if (!this._enabled) {\n            return;\n        }\n        this._cacheTextareaLineHeight();\n        this._cacheTextareaPlaceholderHeight();\n        this._cachedScrollTop = this._textareaElement.scrollTop;\n        // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n        // in checking the height of the textarea.\n        if (!this._cachedLineHeight) {\n            return;\n        }\n        const textarea = this._elementRef.nativeElement;\n        const value = textarea.value;\n        // Only resize if the value or minRows have changed since these calculations can be expensive.\n        if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n            return;\n        }\n        const scrollHeight = this._measureScrollHeight();\n        const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n        // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n        textarea.style.height = `${height}px`;\n        this._ngZone.runOutsideAngular(() => {\n            if (typeof requestAnimationFrame !== 'undefined') {\n                requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n            }\n            else {\n                setTimeout(() => this._scrollToCaretPosition(textarea));\n            }\n        });\n        this._previousValue = value;\n        this._previousMinRows = this._minRows;\n    }\n    /**\n     * Resets the textarea to its original size\n     */\n    reset() {\n        // Do not try to change the textarea, if the initialHeight has not been determined yet\n        // This might potentially remove styles when reset() is called before ngAfterViewInit\n        if (this._initialHeight !== undefined) {\n            this._textareaElement.style.height = this._initialHeight;\n        }\n    }\n    _noopInputHandler() {\n        // no-op handler that ensures we're running change detection on input events.\n    }\n    /**\n     * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n     * prevent it from scrolling to the caret position. We need to re-set the selection\n     * in order for it to scroll to the proper position.\n     */\n    _scrollToCaretPosition(textarea) {\n        const { selectionStart, selectionEnd } = textarea;\n        // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n        // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n        // between the time we requested the animation frame and when it was executed.\n        // Also note that we have to assert that the textarea is focused before we set the\n        // selection range. Setting the selection range on a non-focused textarea will cause\n        // it to receive focus on IE and Edge.\n        if (!this._destroyed.isStopped && this._hasFocus) {\n            textarea.setSelectionRange(selectionStart, selectionEnd);\n            textarea.scrollTop = this._cachedScrollTop;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTextareaAutosize, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkTextareaAutosize, isStandalone: true, selector: \"textarea[cdkTextareaAutosize]\", inputs: { minRows: [\"cdkAutosizeMinRows\", \"minRows\"], maxRows: [\"cdkAutosizeMaxRows\", \"maxRows\"], enabled: [\"cdkTextareaAutosize\", \"enabled\", booleanAttribute], placeholder: \"placeholder\" }, host: { attributes: { \"rows\": \"1\" }, listeners: { \"input\": \"_noopInputHandler()\" }, classAttribute: \"cdk-textarea-autosize\" }, exportAs: [\"cdkTextareaAutosize\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTextareaAutosize, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'textarea[cdkTextareaAutosize]',\n                    exportAs: 'cdkTextareaAutosize',\n                    host: {\n                        'class': 'cdk-textarea-autosize',\n                        // Textarea elements that have the directive applied should have a single row by default.\n                        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n                        'rows': '1',\n                        '(input)': '_noopInputHandler()',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { minRows: [{\n                type: Input,\n                args: ['cdkAutosizeMinRows']\n            }], maxRows: [{\n                type: Input,\n                args: ['cdkAutosizeMaxRows']\n            }], enabled: [{\n                type: Input,\n                args: [{ alias: 'cdkTextareaAutosize', transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input\n            }] } });\n\nclass TextFieldModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TextFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: TextFieldModule, imports: [CdkAutofill, CdkTextareaAutosize], exports: [CdkAutofill, CdkTextareaAutosize] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TextFieldModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TextFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkAutofill, CdkTextareaAutosize],\n                    exports: [CdkAutofill, CdkTextareaAutosize],\n                }]\n        }] });\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n//# sourceMappingURL=text-field.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC9N,SAASC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AACrC,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,sBAAsB,QAAQ,6BAA6B;AACzE,SAASD,CAAC,IAAIE,qBAAqB,QAAQ,wCAAwC;AACnF,SAASC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,oBAAoB,QAAQ,wBAAwB;AACtF,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA,MAAMC,wBAAwB,CAAC;AAG9BC,yBAAA,GAHKD,wBAAwB;AAAAE,eAAA,CAAxBF,wBAAwB,wBAAAG,kCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IACyEJ,yBAAwB;AAAA;AAAAE,eAAA,CADzHF,wBAAwB,8BAImD7B,EAAE,CAAAkC,iBAAA;EAAAC,IAAA,EAFQN,yBAAwB;EAAAO,SAAA;EAAAC,SAAA,kCAAqG,EAAE;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;EAAAC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAE1N;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF/C,EAAE,CAAAgD,iBAAA,CAAQnB,wBAAwB,EAAc,CAAC;IACtHM,IAAI,EAAElC,SAAS;IACfgD,IAAI,EAAE,CAAC;MAAET,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAE5C,uBAAuB,CAACgD,MAAM;MAAEL,aAAa,EAAE1C,iBAAiB,CAACgD,IAAI;MAAEC,IAAI,EAAE;QAAE,6BAA6B,EAAE;MAAG,CAAC;MAAER,MAAM,EAAE,CAAC,2mBAA2mB;IAAE,CAAC;EACvxB,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMS,eAAe,GAAG;EAAEC,OAAO,EAAE;AAAK,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAMlBC,WAAWA,CAAA,EAAG;IAAAzB,eAAA,oBALF3B,MAAM,CAACe,QAAQ,CAAC;IAAAY,eAAA,kBAClB3B,MAAM,CAACC,MAAM,CAAC;IAAA0B,eAAA,oBACZ3B,MAAM,CAACE,gBAAgB,CAAC,CAACmD,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IAAA1B,eAAA,uBAChD3B,MAAM,CAACiB,sBAAsB,CAAC;IAAAU,eAAA,6BACxB,IAAI2B,GAAG,CAAC,CAAC;EACd;EAChBC,OAAOA,CAACC,YAAY,EAAE;IAClB,IAAI,CAAC,IAAI,CAACC,SAAS,CAACC,SAAS,EAAE;MAC3B,OAAO9C,KAAK;IAChB;IACA,IAAI,CAAC+C,YAAY,CAACC,IAAI,CAACnC,wBAAwB,CAAC;IAChD,MAAMoC,OAAO,GAAGzC,aAAa,CAACoC,YAAY,CAAC;IAC3C,MAAMM,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAACC,GAAG,CAACH,OAAO,CAAC;IACjD,IAAIC,IAAI,EAAE;MACN,OAAOA,IAAI,CAACG,OAAO;IACvB;IACA,MAAMA,OAAO,GAAG,IAAIpD,OAAO,CAAC,CAAC;IAC7B,MAAMqD,QAAQ,GAAG,2BAA2B;IAC5C,MAAMC,QAAQ,GAAIC,KAAK,IAAK;MACxB;MACA;MACA;MACA,IAAIA,KAAK,CAACC,aAAa,KAAK,+BAA+B,IACvD,CAACR,OAAO,CAACS,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;QACvCL,OAAO,CAACS,SAAS,CAACE,GAAG,CAACN,QAAQ,CAAC;QAC/B,IAAI,CAACO,OAAO,CAACC,GAAG,CAAC,MAAMT,OAAO,CAACU,IAAI,CAAC;UAAEC,MAAM,EAAER,KAAK,CAACQ,MAAM;UAAEC,YAAY,EAAE;QAAK,CAAC,CAAC,CAAC;MACtF,CAAC,MACI,IAAIT,KAAK,CAACC,aAAa,KAAK,6BAA6B,IAC1DR,OAAO,CAACS,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;QACtCL,OAAO,CAACS,SAAS,CAACQ,MAAM,CAACZ,QAAQ,CAAC;QAClC,IAAI,CAACO,OAAO,CAACC,GAAG,CAAC,MAAMT,OAAO,CAACU,IAAI,CAAC;UAAEC,MAAM,EAAER,KAAK,CAACQ,MAAM;UAAEC,YAAY,EAAE;QAAM,CAAC,CAAC,CAAC;MACvF;IACJ,CAAC;IACD,MAAME,QAAQ,GAAG,IAAI,CAACN,OAAO,CAACO,iBAAiB,CAAC,MAAM;MAClDnB,OAAO,CAACS,SAAS,CAACE,GAAG,CAAC,mCAAmC,CAAC;MAC1D,OAAOtD,qBAAqB,CAAC,IAAI,CAAC+D,SAAS,EAAEpB,OAAO,EAAE,gBAAgB,EAAEM,QAAQ,EAAElB,eAAe,CAAC;IACtG,CAAC,CAAC;IACF,IAAI,CAACc,kBAAkB,CAACmB,GAAG,CAACrB,OAAO,EAAE;MAAEI,OAAO;MAAEc;IAAS,CAAC,CAAC;IAC3D,OAAOd,OAAO;EAClB;EACAkB,cAAcA,CAAC3B,YAAY,EAAE;IACzB,MAAMK,OAAO,GAAGzC,aAAa,CAACoC,YAAY,CAAC;IAC3C,MAAMM,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAACC,GAAG,CAACH,OAAO,CAAC;IACjD,IAAIC,IAAI,EAAE;MACNA,IAAI,CAACiB,QAAQ,CAAC,CAAC;MACfjB,IAAI,CAACG,OAAO,CAACmB,QAAQ,CAAC,CAAC;MACvBvB,OAAO,CAACS,SAAS,CAACQ,MAAM,CAAC,mCAAmC,CAAC;MAC7DjB,OAAO,CAACS,SAAS,CAACQ,MAAM,CAAC,2BAA2B,CAAC;MACrD,IAAI,CAACf,kBAAkB,CAACsB,MAAM,CAACxB,OAAO,CAAC;IAC3C;EACJ;EACAyB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACvB,kBAAkB,CAACwB,OAAO,CAAC,CAACC,KAAK,EAAE3B,OAAO,KAAK,IAAI,CAACsB,cAAc,CAACtB,OAAO,CAAC,CAAC;EACrF;AAGJ;AAAC4B,gBAAA,GAzDKtC,eAAe;AAAAxB,eAAA,CAAfwB,eAAe,wBAAAuC,yBAAA7D,iBAAA;EAAA,YAAAA,iBAAA,IAuDkFsB,gBAAe;AAAA;AAAAxB,eAAA,CAvDhHwB,eAAe,+BAZ4DvD,EAAE,CAAA+F,kBAAA;EAAAC,KAAA,EAoEwBzC,gBAAe;EAAA0C,OAAA,EAAf1C,gBAAe,CAAA2C,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE9I;EAAA,QAAApD,SAAA,oBAAAA,SAAA,KAtEiF/C,EAAE,CAAAgD,iBAAA,CAsEQO,eAAe,EAAc,CAAC;IAC7GpB,IAAI,EAAE5B,UAAU;IAChB0C,IAAI,EAAE,CAAC;MAAEkD,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,MAAMC,WAAW,CAAC;EAKd5C,WAAWA,CAAA,EAAG;IAAAzB,eAAA,sBAJA3B,MAAM,CAACI,UAAU,CAAC;IAAAuB,eAAA,2BACb3B,MAAM,CAACmD,eAAe,CAAC;IAC1C;IAAAxB,eAAA,sBACc,IAAItB,YAAY,CAAC,CAAC;EAChB;EAChB4F,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,gBAAgB,CAChB3C,OAAO,CAAC,IAAI,CAAC4C,WAAW,CAAC,CACzBC,SAAS,CAAChC,KAAK,IAAI,IAAI,CAACiC,WAAW,CAACC,IAAI,CAAClC,KAAK,CAAC,CAAC;EACzD;EACAkB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACY,gBAAgB,CAACf,cAAc,CAAC,IAAI,CAACgB,WAAW,CAAC;EAC1D;AAGJ;AAACI,YAAA,GAhBKP,WAAW;AAAArE,eAAA,CAAXqE,WAAW,wBAAAQ,qBAAA3E,iBAAA;EAAA,YAAAA,iBAAA,IAcsFmE,YAAW;AAAA;AAAArE,eAAA,CAd5GqE,WAAW,8BA3EgEpG,EAAE,CAAA6G,iBAAA;EAAA1E,IAAA,EA0FQiE,YAAW;EAAAhE,SAAA;EAAA0E,OAAA;IAAAL,WAAA;EAAA;AAAA;AAEtG;EAAA,QAAA1D,SAAA,oBAAAA,SAAA,KA5FiF/C,EAAE,CAAAgD,iBAAA,CA4FQoD,WAAW,EAAc,CAAC;IACzGjE,IAAI,EAAEzB,SAAS;IACfuC,IAAI,EAAE,CAAC;MACC8D,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEN,WAAW,EAAE,CAAC;MACtDtE,IAAI,EAAExB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMqG,mBAAmB,CAAC;EAqBtB;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGxF,oBAAoB,CAACyF,KAAK,CAAC;IAC3C,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACF,KAAK,EAAE;IACf,IAAI,CAACG,QAAQ,GAAG5F,oBAAoB,CAACyF,KAAK,CAAC;IAC3C,IAAI,CAACI,aAAa,CAAC,CAAC;EACxB;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACL,KAAK,EAAE;IACf;IACA;IACA,IAAI,IAAI,CAACM,QAAQ,KAAKN,KAAK,EAAE;MACzB,CAAC,IAAI,CAACM,QAAQ,GAAGN,KAAK,IAAI,IAAI,CAACO,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;IAC1E;EACJ;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,gBAAgB,CAACD,WAAW;EAC5C;EACA,IAAIA,WAAWA,CAACT,KAAK,EAAE;IACnB,IAAI,CAACW,wBAAwB,GAAGC,SAAS;IACzC,IAAIZ,KAAK,EAAE;MACP,IAAI,CAACU,gBAAgB,CAACG,YAAY,CAAC,aAAa,EAAEb,KAAK,CAAC;IAC5D,CAAC,MACI;MACD,IAAI,CAACU,gBAAgB,CAACI,eAAe,CAAC,aAAa,CAAC;IACxD;IACA,IAAI,CAACC,+BAA+B,CAAC,CAAC;EAC1C;EACA;;EAUA1E,WAAWA,CAAA,EAAG;IAAAzB,eAAA,sBAtEA3B,MAAM,CAACI,UAAU,CAAC;IAAAuB,eAAA,oBACpB3B,MAAM,CAACe,QAAQ,CAAC;IAAAY,eAAA,kBAClB3B,MAAM,CAACC,MAAM,CAAC;IAAA0B,eAAA,oBACZ3B,MAAM,CAACQ,SAAS,CAAC;IAAAmB,eAAA,wBACb,IAAId,OAAO,CAAC,CAAC;IAC7B;IAAAc,eAAA;IAAAA,eAAA;IAAAA,eAAA,qBAGa,IAAId,OAAO,CAAC,CAAC;IAAAc,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,mBAIf,IAAI;IACf;AACJ;AACA;AACA;AACA;IAJIA,eAAA,2BAKmB,CAAC,CAAC;IAAAA,eAAA;IAAAA,eAAA;IA4CrB;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,oBACY3B,MAAM,CAACuB,QAAQ,EAAE;MAAEwG,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAApG,eAAA;IAAAA,eAAA,wBAEhC,KAAK;IA+HrB;IAAAA,eAAA,4BACqByC,KAAK,IAAK;MAC3B,IAAI,CAAC4D,SAAS,GAAG5D,KAAK,CAACrC,IAAI,KAAK,OAAO;IAC3C,CAAC;IAhIG,MAAMkG,WAAW,GAAGjI,MAAM,CAACiB,sBAAsB,CAAC;IAClDgH,WAAW,CAACrE,IAAI,CAACnC,wBAAwB,CAAC;IAC1C,IAAI,CAACgG,gBAAgB,GAAG,IAAI,CAACtB,WAAW,CAAC+B,aAAa;EAC1D;EACA;EACAlB,aAAaA,CAAA,EAAG;IACZ,MAAMmB,SAAS,GAAG,IAAI,CAACtB,OAAO,IAAI,IAAI,CAACuB,iBAAiB,GAAG,GAAG,IAAI,CAACvB,OAAO,GAAG,IAAI,CAACuB,iBAAiB,IAAI,GAAG,IAAI;IAC9G,IAAID,SAAS,EAAE;MACX,IAAI,CAACV,gBAAgB,CAACY,KAAK,CAACF,SAAS,GAAGA,SAAS;IACrD;EACJ;EACA;EACAhB,aAAaA,CAAA,EAAG;IACZ,MAAMmB,SAAS,GAAG,IAAI,CAACrB,OAAO,IAAI,IAAI,CAACmB,iBAAiB,GAAG,GAAG,IAAI,CAACnB,OAAO,GAAG,IAAI,CAACmB,iBAAiB,IAAI,GAAG,IAAI;IAC9G,IAAIE,SAAS,EAAE;MACX,IAAI,CAACb,gBAAgB,CAACY,KAAK,CAACC,SAAS,GAAGA,SAAS;IACrD;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC9E,SAAS,CAACC,SAAS,EAAE;MAC1B;MACA,IAAI,CAAC8E,cAAc,GAAG,IAAI,CAACf,gBAAgB,CAACY,KAAK,CAACI,MAAM;MACxD,IAAI,CAACnB,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAAC7C,OAAO,CAACO,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAAC0D,iBAAiB,GAAG,CACrB,IAAI,CAACzD,SAAS,CAAC0D,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,IAAI,CAACC,aAAa,CAACjE,IAAI,CAAC,CAAC,CAAC,EAC1E,IAAI,CAACM,SAAS,CAAC0D,MAAM,CAAC,IAAI,CAAClB,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAACoB,iBAAiB,CAAC,EAC7E,IAAI,CAAC5D,SAAS,CAAC0D,MAAM,CAAC,IAAI,CAAClB,gBAAgB,EAAE,MAAM,EAAE,IAAI,CAACoB,iBAAiB,CAAC,CAC/E;QACD,IAAI,CAACD,aAAa,CAACE,IAAI,CAACtH,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC4E,SAAS,CAAC,MAAM;UACnD;UACA;UACA,IAAI,CAACgC,iBAAiB,GAAG,IAAI,CAACV,wBAAwB,GAAGC,SAAS;UAClE,IAAI,CAACL,kBAAkB,CAAC,IAAI,CAAC;QACjC,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACyB,aAAa,GAAG,IAAI;MACzB,IAAI,CAACzB,kBAAkB,CAAC,IAAI,CAAC;IACjC;EACJ;EACAhC,WAAWA,CAAA,EAAG;IAAA,IAAA0D,qBAAA;IACV,CAAAA,qBAAA,OAAI,CAACN,iBAAiB,cAAAM,qBAAA,eAAtBA,qBAAA,CAAwBzD,OAAO,CAAC0D,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IACrD,IAAI,CAACL,aAAa,CAACxD,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC8D,UAAU,CAACvE,IAAI,CAAC,CAAC;IACtB,IAAI,CAACuE,UAAU,CAAC9D,QAAQ,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+D,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACf,iBAAiB,EAAE;MACxB;IACJ;IACA;IACA,MAAMgB,aAAa,GAAG,IAAI,CAAC3B,gBAAgB,CAAC4B,SAAS,CAAC,KAAK,CAAC;IAC5D,MAAMC,WAAW,GAAGF,aAAa,CAACf,KAAK;IACvCe,aAAa,CAACG,IAAI,GAAG,CAAC;IACtB;IACA;IACA;IACAD,WAAW,CAACE,QAAQ,GAAG,UAAU;IACjCF,WAAW,CAACG,UAAU,GAAG,QAAQ;IACjCH,WAAW,CAACI,MAAM,GAAG,MAAM;IAC3BJ,WAAW,CAACK,OAAO,GAAG,GAAG;IACzBL,WAAW,CAACb,MAAM,GAAG,EAAE;IACvBa,WAAW,CAACnB,SAAS,GAAG,EAAE;IAC1BmB,WAAW,CAAChB,SAAS,GAAG,EAAE;IAC1B;IACAgB,WAAW,CAACM,GAAG,GAAGN,WAAW,CAACO,MAAM,GAAGP,WAAW,CAACQ,IAAI,GAAGR,WAAW,CAACS,KAAK,GAAG,MAAM;IACpF;IACA;IACA;IACA;IACA;IACAT,WAAW,CAACU,QAAQ,GAAG,QAAQ;IAC/B,IAAI,CAACvC,gBAAgB,CAACwC,UAAU,CAACC,WAAW,CAACd,aAAa,CAAC;IAC3D,IAAI,CAAChB,iBAAiB,GAAGgB,aAAa,CAACe,YAAY;IACnDf,aAAa,CAACtE,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAACkC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACG,aAAa,CAAC,CAAC;EACxB;EACAiD,oBAAoBA,CAAA,EAAG;IACnB,MAAMvG,OAAO,GAAG,IAAI,CAAC4D,gBAAgB;IACrC,MAAM4C,cAAc,GAAGxG,OAAO,CAACwE,KAAK,CAACiC,YAAY,IAAI,EAAE;IACvD,MAAMC,SAAS,GAAG,IAAI,CAAC9G,SAAS,CAAC+G,OAAO;IACxC,MAAMC,iBAAiB,GAAGF,SAAS,IAAI,IAAI,CAACvC,SAAS;IACrD,MAAM0C,cAAc,GAAGH,SAAS,GAC1B,yCAAyC,GACzC,iCAAiC;IACvC;IACA;IACA;IACA,IAAIE,iBAAiB,EAAE;MACnB5G,OAAO,CAACwE,KAAK,CAACiC,YAAY,GAAG,GAAGzG,OAAO,CAACsG,YAAY,IAAI;IAC5D;IACA;IACA;IACAtG,OAAO,CAACS,SAAS,CAACE,GAAG,CAACkG,cAAc,CAAC;IACrC;IACA;IACA,MAAMC,YAAY,GAAG9G,OAAO,CAAC8G,YAAY,GAAG,CAAC;IAC7C9G,OAAO,CAACS,SAAS,CAACQ,MAAM,CAAC4F,cAAc,CAAC;IACxC,IAAID,iBAAiB,EAAE;MACnB5G,OAAO,CAACwE,KAAK,CAACiC,YAAY,GAAGD,cAAc;IAC/C;IACA,OAAOM,YAAY;EACvB;EACA7C,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAACiB,aAAa,IAAI,IAAI,CAACrB,wBAAwB,IAAIC,SAAS,EAAE;MACnE;IACJ;IACA,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;MACnB,IAAI,CAACE,wBAAwB,GAAG,CAAC;MACjC;IACJ;IACA,MAAMX,KAAK,GAAG,IAAI,CAACU,gBAAgB,CAACV,KAAK;IACzC,IAAI,CAACU,gBAAgB,CAACV,KAAK,GAAG,IAAI,CAACU,gBAAgB,CAACD,WAAW;IAC/D,IAAI,CAACE,wBAAwB,GAAG,IAAI,CAAC0C,oBAAoB,CAAC,CAAC;IAC3D,IAAI,CAAC3C,gBAAgB,CAACV,KAAK,GAAGA,KAAK;EACvC;EAKA6D,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACnH,SAAS,CAACC,SAAS,EAAE;MAC1B,IAAI,CAAC4D,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIA,kBAAkBA,CAACuD,KAAK,GAAG,KAAK,EAAE;IAC9B;IACA,IAAI,CAAC,IAAI,CAACxD,QAAQ,EAAE;MAChB;IACJ;IACA,IAAI,CAAC8B,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACrB,+BAA+B,CAAC,CAAC;IACtC,IAAI,CAACgD,gBAAgB,GAAG,IAAI,CAACrD,gBAAgB,CAACsD,SAAS;IACvD;IACA;IACA,IAAI,CAAC,IAAI,CAAC3C,iBAAiB,EAAE;MACzB;IACJ;IACA,MAAM4C,QAAQ,GAAG,IAAI,CAAC7E,WAAW,CAAC+B,aAAa;IAC/C,MAAMnB,KAAK,GAAGiE,QAAQ,CAACjE,KAAK;IAC5B;IACA,IAAI,CAAC8D,KAAK,IAAI,IAAI,CAAC/D,QAAQ,KAAK,IAAI,CAACmE,gBAAgB,IAAIlE,KAAK,KAAK,IAAI,CAACmE,cAAc,EAAE;MACpF;IACJ;IACA,MAAMP,YAAY,GAAG,IAAI,CAACP,oBAAoB,CAAC,CAAC;IAChD,MAAM3B,MAAM,GAAG0C,IAAI,CAACC,GAAG,CAACT,YAAY,EAAE,IAAI,CAACjD,wBAAwB,IAAI,CAAC,CAAC;IACzE;IACAsD,QAAQ,CAAC3C,KAAK,CAACI,MAAM,GAAG,GAAGA,MAAM,IAAI;IACrC,IAAI,CAAChE,OAAO,CAACO,iBAAiB,CAAC,MAAM;MACjC,IAAI,OAAOqG,qBAAqB,KAAK,WAAW,EAAE;QAC9CA,qBAAqB,CAAC,MAAM,IAAI,CAACC,sBAAsB,CAACN,QAAQ,CAAC,CAAC;MACtE,CAAC,MACI;QACDO,UAAU,CAAC,MAAM,IAAI,CAACD,sBAAsB,CAACN,QAAQ,CAAC,CAAC;MAC3D;IACJ,CAAC,CAAC;IACF,IAAI,CAACE,cAAc,GAAGnE,KAAK;IAC3B,IAAI,CAACkE,gBAAgB,GAAG,IAAI,CAACnE,QAAQ;EACzC;EACA;AACJ;AACA;EACIS,KAAKA,CAAA,EAAG;IACJ;IACA;IACA,IAAI,IAAI,CAACiB,cAAc,KAAKb,SAAS,EAAE;MACnC,IAAI,CAACF,gBAAgB,CAACY,KAAK,CAACI,MAAM,GAAG,IAAI,CAACD,cAAc;IAC5D;EACJ;EACAgD,iBAAiBA,CAAA,EAAG;IAChB;EAAA;EAEJ;AACJ;AACA;AACA;AACA;EACIF,sBAAsBA,CAACN,QAAQ,EAAE;IAC7B,MAAM;MAAES,cAAc;MAAEC;IAAa,CAAC,GAAGV,QAAQ;IACjD;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC9B,UAAU,CAACyC,SAAS,IAAI,IAAI,CAAC3D,SAAS,EAAE;MAC9CgD,QAAQ,CAACY,iBAAiB,CAACH,cAAc,EAAEC,YAAY,CAAC;MACxDV,QAAQ,CAACD,SAAS,GAAG,IAAI,CAACD,gBAAgB;IAC9C;EACJ;AAGJ;AAACe,oBAAA,GAtRKjF,mBAAmB;AAAAjF,eAAA,CAAnBiF,mBAAmB,wBAAAkF,6BAAAjK,iBAAA;EAAA,YAAAA,iBAAA,IAoR8E+E,oBAAmB;AAAA;AAAAjF,eAAA,CApRpHiF,mBAAmB,8BAtGwDhH,EAAE,CAAA6G,iBAAA;EAAA1E,IAAA,EA2XQ6E,oBAAmB;EAAA5E,SAAA;EAAAC,SAAA,WAA8R,GAAG;EAAA8J,YAAA,WAAAC,kCAAA1J,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA3X9T1C,EAAE,CAAAqM,UAAA,mBAAAC,8CAAA;QAAA,OA2XQ3J,GAAA,CAAAiJ,iBAAA,CAAkB,CAAC;MAAA,CAAD,CAAC;IAAA;EAAA;EAAAW,MAAA;IAAAtF,OAAA;IAAAI,OAAA;IAAAG,OAAA,wCAA+M3G,gBAAgB;IAAA+G,WAAA;EAAA;EAAA4E,QAAA;AAAA;AAE7U;EAAA,QAAAzJ,SAAA,oBAAAA,SAAA,KA7XiF/C,EAAE,CAAAgD,iBAAA,CA6XQgE,mBAAmB,EAAc,CAAC;IACjH7E,IAAI,EAAEzB,SAAS;IACfuC,IAAI,EAAE,CAAC;MACC8D,QAAQ,EAAE,+BAA+B;MACzCyF,QAAQ,EAAE,qBAAqB;MAC/BpJ,IAAI,EAAE;QACF,OAAO,EAAE,uBAAuB;QAChC;QACA;QACA,MAAM,EAAE,GAAG;QACX,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE6D,OAAO,EAAE,CAAC;MAClD9E,IAAI,EAAErB,KAAK;MACXmC,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEoE,OAAO,EAAE,CAAC;MACVlF,IAAI,EAAErB,KAAK;MACXmC,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEuE,OAAO,EAAE,CAAC;MACVrF,IAAI,EAAErB,KAAK;MACXmC,IAAI,EAAE,CAAC;QAAEwJ,KAAK,EAAE,qBAAqB;QAAEC,SAAS,EAAE7L;MAAiB,CAAC;IACxE,CAAC,CAAC;IAAE+G,WAAW,EAAE,CAAC;MACdzF,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6L,eAAe,CAAC;AAIrBC,gBAAA,GAJKD,eAAe;AAAA5K,eAAA,CAAf4K,eAAe,wBAAAE,yBAAA5K,iBAAA;EAAA,YAAAA,iBAAA,IACkF0K,gBAAe;AAAA;AAAA5K,eAAA,CADhH4K,eAAe,8BAvZ4D3M,EAAE,CAAA8M,gBAAA;EAAA3K,IAAA,EAyZqBwK,gBAAe;EAAAI,OAAA,GAAY3G,WAAW,EAAEY,mBAAmB;EAAAgG,OAAA,GAAa5G,WAAW,EAAEY,mBAAmB;AAAA;AAAAjF,eAAA,CAF1M4K,eAAe,8BAvZ4D3M,EAAE,CAAAiN,gBAAA;AA4ZnF;EAAA,QAAAlK,SAAA,oBAAAA,SAAA,KA5ZiF/C,EAAE,CAAAgD,iBAAA,CA4ZQ2J,eAAe,EAAc,CAAC;IAC7GxK,IAAI,EAAEpB,QAAQ;IACdkC,IAAI,EAAE,CAAC;MACC8J,OAAO,EAAE,CAAC3G,WAAW,EAAEY,mBAAmB,CAAC;MAC3CgG,OAAO,EAAE,CAAC5G,WAAW,EAAEY,mBAAmB;IAC9C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASzD,eAAe,EAAE6C,WAAW,EAAEY,mBAAmB,EAAE2F,eAAe;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}