{"ast": null, "code": "export class SwHubEntityDataSource {}", "map": {"version": 3, "names": ["SwHubEntityDataSource"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-entity/sw-hub-entity-data-source.ts"], "sourcesContent": ["export class SwHubEntityDataSource {\n}\n"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}