{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _InputDateRangeComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-date-range.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-date-range.component.scss?ngResource\";\nimport { ChangeDetectorRef, Component, Input, Optional } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { MatDynamicFormService } from '../mat-dynamic-form.service';\nlet InputDateRangeComponent = (_InputDateRangeComponent = class InputDateRangeComponent {\n  set componentOptions(value) {\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.clearable = value === null || value === void 0 ? void 0 : value.clearable;\n    if (value !== null && value !== void 0 && value.config) {\n      this.config = _objectSpread(_objectSpread({}, this.config), value.config);\n    }\n  }\n  constructor(service, cdr) {\n    this.cdr = cdr;\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n    this.config = {};\n    this.destroy$ = new Subject();\n    if (service) {\n      service.timezone.pipe(takeUntil(this.destroy$)).subscribe(timeZone => {\n        this.config = _objectSpread(_objectSpread({}, this.config), {}, {\n          timeZone\n        });\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  clear(event) {\n    event.stopPropagation();\n    if (this.control) {\n      this.control.setValue({});\n    }\n  }\n  isEmpty() {\n    var _this$control;\n    const value = (_this$control = this.control) === null || _this$control === void 0 ? void 0 : _this$control.value;\n    return !value || Object.values(value).every(item => !item);\n  }\n  ngOnDestroy() {\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n}, _InputDateRangeComponent.ctorParameters = () => [{\n  type: MatDynamicFormService,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ChangeDetectorRef\n}], _InputDateRangeComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }]\n}, _InputDateRangeComponent);\nInputDateRangeComponent = __decorate([Component({\n  selector: 'lib-input-date-range',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputDateRangeComponent);\nexport { InputDateRangeComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectorRef", "Component", "Input", "Optional", "Subject", "takeUntil", "MatDynamicFormService", "InputDateRangeComponent", "_InputDateRangeComponent", "componentOptions", "value", "title", "clearable", "config", "_objectSpread", "constructor", "service", "cdr", "id", "readonly", "submitted", "destroy$", "timezone", "pipe", "subscribe", "timeZone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "event", "stopPropagation", "control", "setValue", "isEmpty", "_this$control", "Object", "values", "every", "item", "ngOnDestroy", "next", "undefined", "complete", "ctorParameters", "type", "decorators", "propDecorators", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-date-range/input-date-range.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-date-range.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-date-range.component.scss?ngResource\";\nimport { ChangeDetectorRef, Component, Input, Optional } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { MatDynamicFormService } from '../mat-dynamic-form.service';\nlet InputDateRangeComponent = class InputDateRangeComponent {\n    set componentOptions(value) {\n        this.title = value?.title;\n        this.clearable = value?.clearable;\n        if (value?.config) {\n            this.config = { ...this.config, ...value.config };\n        }\n    }\n    constructor(service, cdr) {\n        this.cdr = cdr;\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n        this.config = {};\n        this.destroy$ = new Subject();\n        if (service) {\n            service.timezone\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(timeZone => {\n                this.config = { ...this.config, timeZone };\n                this.cdr.markForCheck();\n            });\n        }\n    }\n    clear(event) {\n        event.stopPropagation();\n        if (this.control) {\n            this.control.setValue({});\n        }\n    }\n    isEmpty() {\n        const value = this.control?.value;\n        return !value || Object.values(value).every(item => !item);\n    }\n    ngOnDestroy() {\n        this.destroy$.next(undefined);\n        this.destroy$.complete();\n    }\n    static { this.ctorParameters = () => [\n        { type: MatDynamicFormService, decorators: [{ type: Optional }] },\n        { type: ChangeDetectorRef }\n    ]; }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }]\n    }; }\n};\nInputDateRangeComponent = __decorate([\n    Component({\n        selector: 'lib-input-date-range',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputDateRangeComponent);\nexport { InputDateRangeComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,SAASC,iBAAiB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC7E,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,IAAIC,uBAAuB,IAAAC,wBAAA,GAAG,MAAMD,uBAAuB,CAAC;EACxD,IAAIE,gBAAgBA,CAACC,KAAK,EAAE;IACxB,IAAI,CAACC,KAAK,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,KAAK;IACzB,IAAI,CAACC,SAAS,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,SAAS;IACjC,IAAIF,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEG,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,GAAAC,aAAA,CAAAA,aAAA,KAAQ,IAAI,CAACD,MAAM,GAAKH,KAAK,CAACG,MAAM,CAAE;IACrD;EACJ;EACAE,WAAWA,CAACC,OAAO,EAAEC,GAAG,EAAE;IACtB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACQ,QAAQ,GAAG,IAAIjB,OAAO,CAAC,CAAC;IAC7B,IAAIY,OAAO,EAAE;MACTA,OAAO,CAACM,QAAQ,CACXC,IAAI,CAAClB,SAAS,CAAC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAACC,QAAQ,IAAI;QACvB,IAAI,CAACZ,MAAM,GAAAC,aAAA,CAAAA,aAAA,KAAQ,IAAI,CAACD,MAAM;UAAEY;QAAQ,EAAE;QAC1C,IAAI,CAACR,GAAG,CAACS,YAAY,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN;EACJ;EACAC,KAAKA,CAACC,KAAK,EAAE;IACTA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7B;EACJ;EACAC,OAAOA,CAAA,EAAG;IAAA,IAAAC,aAAA;IACN,MAAMvB,KAAK,IAAAuB,aAAA,GAAG,IAAI,CAACH,OAAO,cAAAG,aAAA,uBAAZA,aAAA,CAAcvB,KAAK;IACjC,OAAO,CAACA,KAAK,IAAIwB,MAAM,CAACC,MAAM,CAACzB,KAAK,CAAC,CAAC0B,KAAK,CAACC,IAAI,IAAI,CAACA,IAAI,CAAC;EAC9D;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAACC,SAAS,CAAC;IAC7B,IAAI,CAACnB,QAAQ,CAACoB,QAAQ,CAAC,CAAC;EAC5B;AAYJ,CAAC,EAXYjC,wBAAA,CAAKkC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAErC,qBAAqB;EAAEsC,UAAU,EAAE,CAAC;IAAED,IAAI,EAAExC;EAAS,CAAC;AAAE,CAAC,EACjE;EAAEwC,IAAI,EAAE3C;AAAkB,CAAC,CAC9B,EACQQ,wBAAA,CAAKqC,cAAc,GAAG;EAC3Bf,OAAO,EAAE,CAAC;IAAEa,IAAI,EAAEzC;EAAM,CAAC,CAAC;EAC1BgB,EAAE,EAAE,CAAC;IAAEyB,IAAI,EAAEzC;EAAM,CAAC,CAAC;EACrBiB,QAAQ,EAAE,CAAC;IAAEwB,IAAI,EAAEzC;EAAM,CAAC,CAAC;EAC3BkB,SAAS,EAAE,CAAC;IAAEuB,IAAI,EAAEzC;EAAM,CAAC,CAAC;EAC5BO,gBAAgB,EAAE,CAAC;IAAEkC,IAAI,EAAEzC;EAAM,CAAC;AACtC,CAAC,EAAAM,wBAAA,CACJ;AACDD,uBAAuB,GAAGV,UAAU,CAAC,CACjCI,SAAS,CAAC;EACN6C,QAAQ,EAAE,sBAAsB;EAChCC,QAAQ,EAAEjD,oBAAoB;EAC9BkD,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAClD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEQ,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}