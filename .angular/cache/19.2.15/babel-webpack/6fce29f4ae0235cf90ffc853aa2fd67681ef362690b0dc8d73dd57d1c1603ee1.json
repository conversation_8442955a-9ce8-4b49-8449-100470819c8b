{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TestWidgetComponent } from './test-widget.component';\nlet TestWidgetModule = class TestWidgetModule {};\nTestWidgetModule = __decorate([NgModule({\n  imports: [CommonModule],\n  declarations: [TestWidgetComponent]\n})], TestWidgetModule);\nexport { TestWidgetModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "TestWidgetComponent", "TestWidgetModule", "imports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/_stories/test-widget/test-widget.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TestWidgetComponent } from './test-widget.component';\nlet TestWidgetModule = class TestWidgetModule {\n};\nTestWidgetModule = __decorate([\n    NgModule({\n        imports: [CommonModule],\n        declarations: [TestWidgetComponent]\n    })\n], TestWidgetModule);\nexport { TestWidgetModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,IAAIC,gBAAgB,GAAG,MAAMA,gBAAgB,CAAC,EAC7C;AACDA,gBAAgB,GAAGJ,UAAU,CAAC,CAC1BC,QAAQ,CAAC;EACLI,OAAO,EAAE,CAACH,YAAY,CAAC;EACvBI,YAAY,EAAE,CAACH,mBAAmB;AACtC,CAAC,CAAC,CACL,EAAEC,gBAAgB,CAAC;AACpB,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}