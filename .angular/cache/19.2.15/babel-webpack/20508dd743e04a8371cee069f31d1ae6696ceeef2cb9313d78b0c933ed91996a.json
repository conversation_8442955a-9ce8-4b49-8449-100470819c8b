{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatRippleModule;\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nclass MatRippleModule {}\n_MatRippleModule = MatRippleModule;\n_defineProperty(MatRippleModule, \"\\u0275fac\", function _MatRippleModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatRippleModule)();\n});\n_defineProperty(MatRippleModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatRippleModule,\n  imports: [MatCommonModule, MatRipple],\n  exports: [MatR<PERSON>ple, MatCommonModule]\n}));\n_defineProperty(MatRippleModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRipple],\n      exports: [MatRipple, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatRippleModule as M };\n//# sourceMappingURL=index-SYVYjXwK.mjs.map", "map": {"version": 3, "names": ["i0", "NgModule", "M", "MatCommonModule", "<PERSON><PERSON><PERSON><PERSON>", "MatRippleModule", "_MatRippleModule", "_defineProperty", "_MatRippleModule_Factory", "__ngFactoryType__", "ɵɵdefineNgModule", "type", "imports", "exports", "ɵɵdefineInjector", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/index-SYVYjXwK.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\n\nclass MatRippleModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRippleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRippleModule, imports: [MatCommonModule, MatRipple], exports: [MatRipple, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRippleModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRippleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatRipple],\n                    exports: [MatRipple, MatCommonModule],\n                }]\n        }] });\n\nexport { MatRippleModule as M };\n//# sourceMappingURL=index-SYVYjXwK.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASD,CAAC,IAAIE,SAAS,QAAQ,uBAAuB;AAEtD,MAAMC,eAAe,CAAC;AAIrBC,gBAAA,GAJKD,eAAe;AAAAE,eAAA,CAAfF,eAAe,wBAAAG,yBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IACkFJ,gBAAe;AAAA;AAAAE,eAAA,CADhHF,eAAe,8BAK4DL,EAAE,CAAAU,gBAAA;EAAAC,IAAA,EAHqBN,gBAAe;EAAAO,OAAA,GAAYT,eAAe,EAAEC,SAAS;EAAAS,OAAA,GAAaT,SAAS,EAAED,eAAe;AAAA;AAAAI,eAAA,CAF9LF,eAAe,8BAK4DL,EAAE,CAAAc,gBAAA;EAAAF,OAAA,GAFgDT,eAAe,EAAEA,eAAe;AAAA;AAEnK;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAAiFf,EAAE,CAAAgB,iBAAA,CAAQX,eAAe,EAAc,CAAC;IAC7GM,IAAI,EAAEV,QAAQ;IACdgB,IAAI,EAAE,CAAC;MACCL,OAAO,EAAE,CAACT,eAAe,EAAEC,SAAS,CAAC;MACrCS,OAAO,EAAE,CAACT,SAAS,EAAED,eAAe;IACxC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASE,eAAe,IAAIH,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}