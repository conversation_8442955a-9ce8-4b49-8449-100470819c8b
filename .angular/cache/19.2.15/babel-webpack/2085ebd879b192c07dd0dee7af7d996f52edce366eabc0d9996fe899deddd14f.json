{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuSelectComponent } from './swui-menu-select.component';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const MENU_SELECT_MODULES = [FormsModule, ReactiveFormsModule, MatInputModule, MatIconModule, MatCheckboxModule, MatSelectModule, MatButtonModule, MatRippleModule];\nlet SwuiMenuSelectModule = class SwuiMenuSelectModule {};\nSwuiMenuSelectModule = __decorate([NgModule({\n  declarations: [SwuiMenuSelectComponent],\n  imports: [CommonModule, TranslateModule.forChild(), ...MENU_SELECT_MODULES],\n  exports: [SwuiMenuSelectComponent]\n})], SwuiMenuSelectModule);\nexport { SwuiMenuSelectModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "FormsModule", "ReactiveFormsModule", "TranslateModule", "SwuiMenuSelectComponent", "MatCheckboxModule", "MatSelectModule", "MatIconModule", "MatInputModule", "MatButtonModule", "MatRippleModule", "MENU_SELECT_MODULES", "SwuiMenuSelectModule", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-menu-select/swui-menu-select.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuSelectComponent } from './swui-menu-select.component';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const MENU_SELECT_MODULES = [\n    FormsModule,\n    ReactiveFormsModule,\n    MatInputModule,\n    MatIconModule,\n    MatCheckboxModule,\n    MatSelectModule,\n    MatButtonModule,\n    MatRippleModule,\n];\nlet SwuiMenuSelectModule = class SwuiMenuSelectModule {\n};\nSwuiMenuSelectModule = __decorate([\n    NgModule({\n        declarations: [SwuiMenuSelectComponent],\n        imports: [\n            CommonModule,\n            TranslateModule.forChild(),\n            ...MENU_SELECT_MODULES\n        ],\n        exports: [SwuiMenuSelectComponent],\n    })\n], SwuiMenuSelectModule);\nexport { SwuiMenuSelectModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,MAAMC,mBAAmB,GAAG,CAC/BV,WAAW,EACXC,mBAAmB,EACnBM,cAAc,EACdD,aAAa,EACbF,iBAAiB,EACjBC,eAAe,EACfG,eAAe,EACfC,eAAe,CAClB;AACD,IAAIE,oBAAoB,GAAG,MAAMA,oBAAoB,CAAC,EACrD;AACDA,oBAAoB,GAAGd,UAAU,CAAC,CAC9BC,QAAQ,CAAC;EACLc,YAAY,EAAE,CAACT,uBAAuB,CAAC;EACvCU,OAAO,EAAE,CACLd,YAAY,EACZG,eAAe,CAACY,QAAQ,CAAC,CAAC,EAC1B,GAAGJ,mBAAmB,CACzB;EACDK,OAAO,EAAE,CAACZ,uBAAuB;AACrC,CAAC,CAAC,CACL,EAAEQ,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}