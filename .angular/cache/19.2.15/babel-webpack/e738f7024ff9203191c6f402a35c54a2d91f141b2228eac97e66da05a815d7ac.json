{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HubSelectorComponent } from './hub-selector.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\nlet HubSelectorModule = class HubSelectorModule {};\nHubSelectorModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, MatRippleModule, MatMenuModule, MatIconModule],\n  declarations: [HubSelectorComponent],\n  exports: [HubSelectorComponent]\n})], HubSelectorModule);\nexport { HubSelectorModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "HubSelectorComponent", "TranslateModule", "MatMenuModule", "MatIconModule", "MatRippleModule", "HubSelectorModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/hub-selector/hub-selector.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HubSelectorComponent } from './hub-selector.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\nlet HubSelectorModule = class HubSelectorModule {\n};\nHubSelectorModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule,\n            MatRippleModule,\n            MatMenuModule,\n            MatIconModule,\n        ],\n        declarations: [HubSelectorComponent],\n        exports: [HubSelectorComponent],\n    })\n], HubSelectorModule);\nexport { HubSelectorModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,IAAIC,iBAAiB,GAAG,MAAMA,iBAAiB,CAAC,EAC/C;AACDA,iBAAiB,GAAGR,UAAU,CAAC,CAC3BC,QAAQ,CAAC;EACLQ,OAAO,EAAE,CACLP,YAAY,EACZE,eAAe,EACfG,eAAe,EACfF,aAAa,EACbC,aAAa,CAChB;EACDI,YAAY,EAAE,CAACP,oBAAoB,CAAC;EACpCQ,OAAO,EAAE,CAACR,oBAAoB;AAClC,CAAC,CAAC,CACL,EAAEK,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}