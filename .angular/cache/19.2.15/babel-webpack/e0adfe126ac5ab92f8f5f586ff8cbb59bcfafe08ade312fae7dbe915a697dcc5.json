{"ast": null, "code": "import { config } from '../config';\nimport { timeoutProvider } from '../scheduler/timeoutProvider';\nexport function reportUnhandledError(err) {\n  timeoutProvider.setTimeout(function () {\n    var onUnhandledError = config.onUnhandledError;\n    if (onUnhandledError) {\n      onUnhandledError(err);\n    } else {\n      throw err;\n    }\n  });\n}\n//# sourceMappingURL=reportUnhandledError.js.map", "map": {"version": 3, "names": ["config", "timeout<PERSON>rovider", "reportUnhandledError", "err", "setTimeout", "onUnhandledError"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/reportUnhandledError.js"], "sourcesContent": ["import { config } from '../config';\nimport { timeoutProvider } from '../scheduler/timeoutProvider';\nexport function reportUnhandledError(err) {\n    timeoutProvider.setTimeout(function () {\n        var onUnhandledError = config.onUnhandledError;\n        if (onUnhandledError) {\n            onUnhandledError(err);\n        }\n        else {\n            throw err;\n        }\n    });\n}\n//# sourceMappingURL=reportUnhandledError.js.map"], "mappings": "AAAA,SAASA,MAAM,QAAQ,WAAW;AAClC,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,OAAO,SAASC,oBAAoBA,CAACC,GAAG,EAAE;EACtCF,eAAe,CAACG,UAAU,CAAC,YAAY;IACnC,IAAIC,gBAAgB,GAAGL,MAAM,CAACK,gBAAgB;IAC9C,IAAIA,gBAAgB,EAAE;MAClBA,gBAAgB,CAACF,GAAG,CAAC;IACzB,CAAC,MACI;MACD,MAAMA,GAAG;IACb;EACJ,CAAC,CAAC;AACN;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}