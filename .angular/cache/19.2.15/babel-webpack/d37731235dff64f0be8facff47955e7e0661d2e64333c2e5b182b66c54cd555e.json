{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { EntityPickerComponent } from './entity-picker.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatRippleModule } from '@angular/material/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nlet EntityPickerModule = class EntityPickerModule {};\nEntityPickerModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, MatRippleModule, MatMenuModule, MatIconModule, MatInputModule, ReactiveFormsModule, MatTooltipModule],\n  declarations: [EntityPickerComponent],\n  exports: [EntityPickerComponent]\n})], EntityPickerModule);\nexport { EntityPickerModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "MatTooltipModule", "TranslateModule", "EntityPickerComponent", "MatIconModule", "MatInputModule", "MatMenuModule", "MatRippleModule", "ReactiveFormsModule", "EntityPickerModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/entity-picker/entity-picker.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { EntityPickerComponent } from './entity-picker.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatRippleModule } from '@angular/material/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nlet EntityPickerModule = class EntityPickerModule {\n};\nEntityPickerModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule,\n            MatRippleModule,\n            MatMenuModule,\n            MatIconModule,\n            MatInputModule,\n            ReactiveFormsModule,\n            MatTooltipModule,\n        ],\n        declarations: [EntityPickerComponent],\n        exports: [EntityPickerComponent],\n    })\n], EntityPickerModule);\nexport { EntityPickerModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,IAAIC,kBAAkB,GAAG,MAAMA,kBAAkB,CAAC,EACjD;AACDA,kBAAkB,GAAGX,UAAU,CAAC,CAC5BC,QAAQ,CAAC;EACLW,OAAO,EAAE,CACLV,YAAY,EACZE,eAAe,EACfK,eAAe,EACfD,aAAa,EACbF,aAAa,EACbC,cAAc,EACdG,mBAAmB,EACnBP,gBAAgB,CACnB;EACDU,YAAY,EAAE,CAACR,qBAAqB,CAAC;EACrCS,OAAO,EAAE,CAACT,qBAAqB;AACnC,CAAC,CAAC,CACL,EAAEM,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}