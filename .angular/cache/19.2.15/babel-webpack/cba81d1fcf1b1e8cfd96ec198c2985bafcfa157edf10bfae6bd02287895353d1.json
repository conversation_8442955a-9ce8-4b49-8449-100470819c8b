{"ast": null, "code": "var _SwuiTdPercentWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./percent.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdPercentWidgetComponent = (_SwuiTdPercentWidgetComponent = class SwuiTdPercentWidgetComponent {\n  constructor({\n    value,\n    schema: {\n      delimiter,\n      fractionCount,\n      td\n    }\n  }) {\n    this.delimiter = delimiter;\n    this.fractionCount = fractionCount || 0;\n    this.value = value || 0;\n    const formatted = td && 'formatted' in td ? td.formatted : true;\n    if (!formatted) {\n      this.value *= 100;\n    }\n  }\n}, _SwuiTdPercentWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdPercentWidgetComponent);\nSwuiTdPercentWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-percent-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdPercentWidgetComponent);\nexport { SwuiTdPercentWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdPercentWidgetComponent", "_SwuiTdPercentWidgetComponent", "constructor", "value", "schema", "delimiter", "fractionCount", "td", "formatted", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/percent/percent.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./percent.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdPercentWidgetComponent = class SwuiTdPercentWidgetComponent {\n    constructor({ value, schema: { delimiter, fractionCount, td } }) {\n        this.delimiter = delimiter;\n        this.fractionCount = fractionCount || 0;\n        this.value = value || 0;\n        const formatted = td && 'formatted' in td ? td.formatted : true;\n        if (!formatted) {\n            this.value *= 100;\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdPercentWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-percent-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdPercentWidgetComponent);\nexport { SwuiTdPercentWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,4BAA4B,IAAAC,6BAAA,GAAG,MAAMD,4BAA4B,CAAC;EAClEE,WAAWA,CAAC;IAAEC,KAAK;IAAEC,MAAM,EAAE;MAAEC,SAAS;MAAEC,aAAa;MAAEC;IAAG;EAAE,CAAC,EAAE;IAC7D,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAGA,aAAa,IAAI,CAAC;IACvC,IAAI,CAACH,KAAK,GAAGA,KAAK,IAAI,CAAC;IACvB,MAAMK,SAAS,GAAGD,EAAE,IAAI,WAAW,IAAIA,EAAE,GAAGA,EAAE,CAACC,SAAS,GAAG,IAAI;IAC/D,IAAI,CAACA,SAAS,EAAE;MACZ,IAAI,CAACL,KAAK,IAAI,GAAG;IACrB;EACJ;AAIJ,CAAC,EAHYF,6BAAA,CAAKQ,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEZ,MAAM;IAAEe,IAAI,EAAE,CAACd,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,6BAAA,CACJ;AACDD,4BAA4B,GAAGL,UAAU,CAAC,CACtCE,SAAS,CAAC;EACNiB,QAAQ,EAAE,4BAA4B;EACtCC,QAAQ,EAAEnB,oBAAoB;EAC9BoB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEhB,4BAA4B,CAAC;AAChC,SAASA,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}