{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { CommonModule } from '@angular/common';\nimport { provideHttpClientTesting } from '@angular/common/http/testing';\nimport { ActivatedRoute } from '@angular/router';\nimport { SwHubEntityService } from './sw-hub-entity.service';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\ndescribe('SwHubEntityService', () => {\n  let service;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [CommonModule],\n      providers: [SwHubEntityService, {\n        provide: ActivatedRoute,\n        useValue: {\n          snapshot: {\n            queryParams: {}\n          }\n        }\n      }, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()]\n    });\n    service = TestBed.inject(SwHubEntityService);\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "CommonModule", "provideHttpClientTesting", "ActivatedRoute", "SwHubEntityService", "provideHttpClient", "withInterceptorsFromDi", "describe", "service", "beforeEach", "configureTestingModule", "imports", "providers", "provide", "useValue", "snapshot", "queryParams", "inject", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-entity/sw-hub-entity.service.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\nimport { CommonModule } from '@angular/common';\nimport { provideHttpClientTesting } from '@angular/common/http/testing';\nimport { ActivatedRoute } from '@angular/router';\nimport { SwHubEntityService } from './sw-hub-entity.service';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\n\n\ndescribe('SwHubEntityService', () => {\n  let service: SwHubEntityService;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n    imports: [CommonModule],\n    providers: [\n        SwHubEntityService,\n        {\n            provide: ActivatedRoute,\n            useValue: {\n                snapshot: {\n                    queryParams: {}\n                }\n            }\n        },\n        provideHttpClient(withInterceptorsFromDi()),\n        provideHttpClientTesting()\n    ]\n});\n    service = TestBed.inject(SwHubEntityService);\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,sBAAsB;AAGhFC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClC,IAAIC,OAA2B;EAC/BC,UAAU,CAAC,MAAK;IACdT,OAAO,CAACU,sBAAsB,CAAC;MAC/BC,OAAO,EAAE,CAACV,YAAY,CAAC;MACvBW,SAAS,EAAE,CACPR,kBAAkB,EAClB;QACIS,OAAO,EAAEV,cAAc;QACvBW,QAAQ,EAAE;UACNC,QAAQ,EAAE;YACNC,WAAW,EAAE;;;OAGxB,EACDX,iBAAiB,CAACC,sBAAsB,EAAE,CAAC,EAC3CJ,wBAAwB,EAAE;KAEjC,CAAC;IACEM,OAAO,GAAGR,OAAO,CAACiB,MAAM,CAACb,kBAAkB,CAAC;EAC9C,CAAC,CAAC;EAEFc,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACX,OAAO,CAAC,CAACY,UAAU,EAAE;EAC9B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}