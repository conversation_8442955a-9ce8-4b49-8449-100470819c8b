{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiSelectModule } from '../../swui-select/swui-select.module';\nimport { SwuiSettingsDialogComponent } from './swui-settings-dialog.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatButtonModule } from '@angular/material/button';\nlet SwuiSettingsDialogModule = class SwuiSettingsDialogModule {};\nSwuiSettingsDialogModule = __decorate([NgModule({\n  imports: [CommonModule, ReactiveFormsModule, TranslateModule, MatButtonModule, MatSelectModule, MatDialogModule, SwuiSelectModule],\n  declarations: [SwuiSettingsDialogComponent]\n})], SwuiSettingsDialogModule);\nexport { SwuiSettingsDialogModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "SwuiSelectModule", "SwuiSettingsDialogComponent", "ReactiveFormsModule", "TranslateModule", "MatDialogModule", "MatSelectModule", "MatButtonModule", "SwuiSettingsDialogModule", "__decorate", "imports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/settings-dialog/swui-settings-dialog.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiSelectModule } from '../../swui-select/swui-select.module';\nimport { SwuiSettingsDialogComponent } from './swui-settings-dialog.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatDialogModule } from '@angular/material/dialog';\n\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatButtonModule } from '@angular/material/button';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    TranslateModule,\n    MatButtonModule,\n    MatSelectModule,\n    MatDialogModule,\n\n    SwuiSelectModule,\n  ],\n  declarations: [\n    SwuiSettingsDialogComponent\n  ],\n})\nexport class SwuiSettingsDialogModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAiBnD,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB,GACpC;AADYA,wBAAwB,GAAAC,UAAA,EAfpCV,QAAQ,CAAC;EACRW,OAAO,EAAE,CACPV,YAAY,EACZG,mBAAmB,EACnBC,eAAe,EACfG,eAAe,EACfD,eAAe,EACfD,eAAe,EAEfJ,gBAAgB,CACjB;EACDU,YAAY,EAAE,CACZT,2BAA2B;CAE9B,CAAC,C,EACWM,wBAAwB,CACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}