{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwDexieService;\nimport { __decorate } from \"tslib\";\nimport { Inject, Injectable, InjectionToken } from '@angular/core';\nimport <PERSON><PERSON> from 'dexie';\nimport { ReplaySubject } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';\nimport { settingsNames } from './dexie-types';\nexport const DEXI_CONFIG = new InjectionToken('SwDexieServiceConfig');\nlet SwDexieService = (_SwDexieService = class SwDexieService extends Dexie {\n  get ready() {\n    return !this.authService ? Promise.resolve({}) : new Promise(resolve => {\n      this._ready.pipe(take(1)).subscribe(() => {\n        resolve({});\n      });\n    });\n  }\n  constructor(authService, hubName) {\n    super(hubName);\n    this.authService = authService;\n    this._ready = new ReplaySubject(1);\n    try {\n      this.version(1).stores({\n        filters: '++id,name,scope,data',\n        settings: '++,name,params'\n      });\n      this.version(2).stores({\n        users: '++id, &[username+entityKey]',\n        columns: '++id, userId, schemaType, params',\n        appSettings: '++id, userId, params',\n        filters: '++id, [userId+scope], name, data',\n        loginKeys: '++id, active, key, label'\n      });\n      this.version(3).stores({\n        collapsedState: '++id, userId, componentId, data, scope, &[userId+componentId+scope]'\n      });\n      this.version(4).stores({\n        columns: '++id, [userId+schemaType], params'\n      });\n      this.version(5).stores({\n        usersNotificatedAboutNewMenu: '++id, &[username+entityKey]'\n      });\n      this.version(6).stores({\n        filterStates: '++id, &[username+entityKey+componentName], state'\n      });\n      this.version(7).stores({\n        lastVisitedPages: '++id, &[username+entityKey], url'\n      });\n      this.version(8).stores({\n        runSettings: '++id, &[username+entityKey+path], params'\n      });\n    } catch (e) {\n      console.error('SwDexieService constructor error ', e);\n    }\n    this._subscribeUser();\n  }\n  getSetting(opts) {\n    let result;\n    switch (opts.name) {\n      case 'filter':\n        result = this.getFilterState(opts === null || opts === void 0 ? void 0 : opts.component);\n        break;\n      default:\n        result = Promise.resolve();\n    }\n    return result;\n  }\n  putSetting(opts, value) {\n    let result;\n    switch (opts.name) {\n      case settingsNames.filter:\n        if (opts.component) {\n          result = this.putFilterState(opts.component, value);\n        }\n        break;\n      default:\n        result = Promise.resolve();\n    }\n    return result;\n  }\n  getColumn(schemaType) {\n    return this.ready.then(() => {\n      var _this$columns, _this$user;\n      return (_this$columns = this.columns) === null || _this$columns === void 0 ? void 0 : _this$columns.get({\n        userId: (_this$user = this.user) === null || _this$user === void 0 ? void 0 : _this$user.id,\n        schemaType\n      });\n    });\n  }\n  saveColumn(schemaType, params) {\n    return this.ready.then(() => this.getColumn(schemaType)).then(column => {\n      if (column && column.id) {\n        var _this$columns2, _this$user2;\n        return (_this$columns2 = this.columns) === null || _this$columns2 === void 0 ? void 0 : _this$columns2.update(column.id, {\n          userId: (_this$user2 = this.user) === null || _this$user2 === void 0 ? void 0 : _this$user2.id,\n          schemaType,\n          params\n        });\n      } else {\n        var _this$columns3, _this$user3;\n        return (_this$columns3 = this.columns) === null || _this$columns3 === void 0 ? void 0 : _this$columns3.add({\n          userId: ((_this$user3 = this.user) === null || _this$user3 === void 0 ? void 0 : _this$user3.id) || 0,\n          schemaType,\n          params\n        });\n      }\n    });\n  }\n  getAppSettings() {\n    return this.ready.then(() => {\n      var _this$appSettings, _this$user4;\n      return (_this$appSettings = this.appSettings) === null || _this$appSettings === void 0 ? void 0 : _this$appSettings.get({\n        userId: (_this$user4 = this.user) === null || _this$user4 === void 0 ? void 0 : _this$user4.id\n      });\n    });\n  }\n  saveAppSettings(params) {\n    return this.ready.then(() => {\n      return this.getAppSettings().then(appSettings => {\n        if (appSettings && appSettings.id) {\n          var _this$appSettings2, _this$user5;\n          return (_this$appSettings2 = this.appSettings) === null || _this$appSettings2 === void 0 ? void 0 : _this$appSettings2.update(appSettings === null || appSettings === void 0 ? void 0 : appSettings.id, {\n            userId: (_this$user5 = this.user) === null || _this$user5 === void 0 ? void 0 : _this$user5.id,\n            params\n          });\n        } else {\n          var _this$appSettings3, _this$user6;\n          return (_this$appSettings3 = this.appSettings) === null || _this$appSettings3 === void 0 ? void 0 : _this$appSettings3.add({\n            userId: ((_this$user6 = this.user) === null || _this$user6 === void 0 ? void 0 : _this$user6.id) || 0,\n            params\n          });\n        }\n      });\n    });\n  }\n  getComponentState(componentId, scope) {\n    return this.ready.then(() => {\n      var _this$collapsedState, _this$user7;\n      return (_this$collapsedState = this.collapsedState) === null || _this$collapsedState === void 0 ? void 0 : _this$collapsedState.get({\n        userId: (_this$user7 = this.user) === null || _this$user7 === void 0 ? void 0 : _this$user7.id,\n        componentId,\n        scope\n      });\n    });\n  }\n  updateComponentState(componentId, scope, data) {\n    var _this$user8, _this$collapsedState2;\n    let search = {\n      userId: ((_this$user8 = this.user) === null || _this$user8 === void 0 ? void 0 : _this$user8.id) || 0,\n      componentId,\n      scope\n    };\n    (_this$collapsedState2 = this.collapsedState) === null || _this$collapsedState2 === void 0 || _this$collapsedState2.get(search, res => {\n      var _this$collapsedState3;\n      res = Object.assign(res || search, {\n        data\n      });\n      (_this$collapsedState3 = this.collapsedState) === null || _this$collapsedState3 === void 0 || _this$collapsedState3.put(res);\n    });\n  }\n  setUserToNotificatedList() {\n    return this.ready.then(() => {\n      var _this$usersNotificate;\n      const {\n        username,\n        entityKey\n      } = this.authService;\n      const newUser = {\n        username: username,\n        entityKey: entityKey\n      };\n      return (_this$usersNotificate = this.usersNotificatedAboutNewMenu) === null || _this$usersNotificate === void 0 ? void 0 : _this$usersNotificate.add(newUser);\n    });\n  }\n  isNotUserInNotificatedList() {\n    return this.ready.then(() => {\n      var _this$usersNotificate2;\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey\n      };\n      return (_this$usersNotificate2 = this.usersNotificatedAboutNewMenu) === null || _this$usersNotificate2 === void 0 ? void 0 : _this$usersNotificate2.get(findOptions).then(user => !user);\n    });\n  }\n  getFilterState(componentName) {\n    return this.ready.then(() => {\n      var _this$filterStates;\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        componentName\n      };\n      return (_this$filterStates = this.filterStates) === null || _this$filterStates === void 0 ? void 0 : _this$filterStates.get(findOptions).then(filterState => {\n        return filterState ? JSON.parse(filterState.state) : {};\n      });\n    });\n  }\n  putFilterState(componentName, state) {\n    return this.ready.then(() => {\n      var _this$filterStates2;\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        componentName\n      };\n      const newState = JSON.stringify(state);\n      const createOptions = _objectSpread(_objectSpread({}, findOptions), {}, {\n        state: newState\n      });\n      return (_this$filterStates2 = this.filterStates) === null || _this$filterStates2 === void 0 ? void 0 : _this$filterStates2.get(findOptions).then(row => {\n        if (row && row.id) {\n          var _this$filterStates3;\n          return (_this$filterStates3 = this.filterStates) === null || _this$filterStates3 === void 0 ? void 0 : _this$filterStates3.update(row.id, {\n            state: newState\n          });\n        }\n        if (!!createOptions) {\n          var _this$filterStates4;\n          return (_this$filterStates4 = this.filterStates) === null || _this$filterStates4 === void 0 ? void 0 : _this$filterStates4.add(createOptions);\n        }\n      });\n    });\n  }\n  getRunSettings(path) {\n    return this.ready.then(() => {\n      var _this$runSettings;\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        path\n      };\n      return (_this$runSettings = this.runSettings) === null || _this$runSettings === void 0 ? void 0 : _this$runSettings.get(findOptions).then(settings => {\n        return settings ? JSON.parse(settings.params) : {};\n      });\n    });\n  }\n  putRunSettings(path, params) {\n    return this.ready.then(() => {\n      var _this$runSettings2;\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        path\n      };\n      const newParams = JSON.stringify(params);\n      const createOptions = _objectSpread(_objectSpread({}, findOptions), {}, {\n        params: newParams\n      });\n      return (_this$runSettings2 = this.runSettings) === null || _this$runSettings2 === void 0 ? void 0 : _this$runSettings2.get(findOptions).then(row => {\n        if (row && row.id) {\n          var _this$runSettings3;\n          return (_this$runSettings3 = this.runSettings) === null || _this$runSettings3 === void 0 ? void 0 : _this$runSettings3.update(row.id, {\n            params: newParams\n          });\n        }\n        if (!!createOptions) {\n          var _this$runSettings4;\n          return (_this$runSettings4 = this.runSettings) === null || _this$runSettings4 === void 0 ? void 0 : _this$runSettings4.add(createOptions);\n        }\n      });\n    });\n  }\n  _subscribeUser() {\n    this.authService.logged.asObservable().subscribe(() => {\n      if (this.authService.isLogged()) {\n        let activeKey = this.authService.entityKey;\n        if (activeKey && this.authService.username) {\n          this._getOrCreateUser(this.authService.username, activeKey);\n        }\n      }\n    });\n  }\n  _getOrCreateUser(username, entityKey) {\n    var _this$users;\n    let userObj = {\n      username,\n      entityKey\n    };\n    // @ts-ignore\n    return (_this$users = this.users) === null || _this$users === void 0 ? void 0 : _this$users.get(userObj).then(this._saveUser.bind(this, userObj)).catch(console.error.bind(console));\n  }\n  _saveUser(userObj, user) {\n    if (user) {\n      this.user = user;\n      this._ready.next(undefined);\n    } else {\n      var _this$users2;\n      (_this$users2 = this.users) === null || _this$users2 === void 0 || _this$users2.add(Object.assign({}, userObj)).then(() => {\n        var _this$users3;\n        (_this$users3 = this.users) === null || _this$users3 === void 0 || _this$users3.get(userObj).then(this._saveUser.bind(this, userObj)).catch(console.error.bind(console));\n      }).catch(console.error.bind(console));\n    }\n  }\n}, _SwDexieService.ctorParameters = () => [{\n  type: SwHubAuthService\n}, {\n  type: String,\n  decorators: [{\n    type: Inject,\n    args: [DEXI_CONFIG]\n  }]\n}], _SwDexieService);\nSwDexieService = __decorate([Injectable()], SwDexieService);\nexport { SwDexieService };", "map": {"version": 3, "names": ["Inject", "Injectable", "InjectionToken", "<PERSON><PERSON>", "ReplaySubject", "take", "SwHubAuthService", "settingsNames", "DEXI_CONFIG", "SwDexieService", "_SwDexieService", "ready", "authService", "Promise", "resolve", "_ready", "pipe", "subscribe", "constructor", "hubName", "version", "stores", "filters", "settings", "users", "columns", "appSettings", "loginKeys", "collapsedState", "usersNotificatedAboutNewMenu", "filterStates", "lastVisitedPages", "runSettings", "e", "console", "error", "_subscribeUser", "getSetting", "opts", "result", "name", "getFilterState", "component", "putSetting", "value", "filter", "putFilterState", "getColumn", "schemaType", "then", "_this$columns", "_this$user", "get", "userId", "user", "id", "saveColumn", "params", "column", "_this$columns2", "_this$user2", "update", "_this$columns3", "_this$user3", "add", "getAppSettings", "_this$appSettings", "_this$user4", "saveAppSettings", "_this$appSettings2", "_this$user5", "_this$appSettings3", "_this$user6", "getComponentState", "componentId", "scope", "_this$collapsedState", "_this$user7", "updateComponentState", "data", "_this$user8", "_this$collapsedState2", "search", "res", "_this$collapsedState3", "Object", "assign", "put", "setUserToNotificatedList", "_this$usersNotificate", "username", "entityKey", "newUser", "isNotUserInNotificatedList", "_this$usersNotificate2", "findOptions", "componentName", "_this$filterStates", "filterState", "JSON", "parse", "state", "_this$filterStates2", "newState", "stringify", "createOptions", "_objectSpread", "row", "_this$filterStates3", "_this$filterStates4", "getRunSettings", "path", "_this$runSettings", "putRunSettings", "_this$runSettings2", "newParams", "_this$runSettings3", "_this$runSettings4", "logged", "asObservable", "isLogged", "active<PERSON><PERSON>", "_getOrCreateUser", "_this$users", "userObj", "_saveUser", "bind", "catch", "next", "undefined", "_this$users2", "_this$users3", "args", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-dexie/sw-dexie.service.ts"], "sourcesContent": ["import { Inject, Injectable, InjectionToken } from '@angular/core';\nimport <PERSON><PERSON> from 'dexie';\nimport { ReplaySubject } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';\nimport {\n  IDexieAppSettings, IDexieColumn, IDexieComponentConfig, IDexieFilter, IDexieFilterState, IDexieRunSettings,\n  IDexieUser, settingsNames,\n  UserSettingOptions\n} from './dexie-types';\n\nexport const DEXI_CONFIG = new InjectionToken<string>('SwDexieServiceConfig');\n\n@Injectable()\nexport class SwDexieService extends Dexie {\n  users: Dexie.Table<IDexieUser, number> | undefined;\n  columns: Dexie.Table<IDexieColumn, number> | undefined;\n  appSettings: Dexie.Table<IDexieAppSettings, number> | undefined;\n  filters: Dexie.Table<IDexieFilter, number> | undefined;\n  collapsedState: Dexie.Table<IDexieComponentConfig, number> | undefined;\n  usersNotificatedAboutNewMenu: Dexie.Table<IDexieUser, number> | undefined;\n  filterStates: Dexie.Table<IDexieFilterState, number> | undefined;\n  runSettings: Dexie.Table<IDexieRunSettings, number> | undefined;\n  user: IDexieUser | undefined;\n\n  _ready = new ReplaySubject<any>(1);\n\n  get ready(): Promise<any> {\n    return !this.authService ? Promise.resolve({}) : new Promise(( resolve ) => {\n      this._ready.pipe(\n        take(1)\n      ).subscribe(() => {\n        resolve({});\n      });\n    });\n  }\n\n  constructor( private authService: SwHubAuthService,\n               @Inject(DEXI_CONFIG) hubName: string\n  ) {\n    super(hubName);\n    try {\n      this.version(1).stores({\n        filters: '++id,name,scope,data',\n        settings: '++,name,params',\n      });\n\n      this.version(2).stores({\n        users: '++id, &[username+entityKey]',\n        columns: '++id, userId, schemaType, params',\n        appSettings: '++id, userId, params',\n        filters: '++id, [userId+scope], name, data',\n        loginKeys: '++id, active, key, label',\n      });\n\n      this.version(3).stores({\n        collapsedState: '++id, userId, componentId, data, scope, &[userId+componentId+scope]',\n      });\n\n      this.version(4).stores({\n        columns: '++id, [userId+schemaType], params',\n      });\n\n      this.version(5).stores({\n        usersNotificatedAboutNewMenu: '++id, &[username+entityKey]',\n      });\n\n      this.version(6).stores({\n        filterStates: '++id, &[username+entityKey+componentName], state',\n      });\n\n      this.version(7).stores({\n        lastVisitedPages: '++id, &[username+entityKey], url',\n      });\n\n      this.version(8).stores({\n        runSettings: '++id, &[username+entityKey+path], params',\n      });\n    } catch (e) {\n      console.error('SwDexieService constructor error ', e);\n    }\n    this._subscribeUser();\n  }\n\n  getSetting( opts: UserSettingOptions ): Promise<any> {\n    let result;\n    switch (opts.name) {\n      case 'filter':\n        result = this.getFilterState(opts?.component);\n        break;\n      default:\n        result = Promise.resolve();\n    }\n    return result;\n  }\n\n  putSetting( opts: UserSettingOptions, value: any ) {\n    let result;\n    switch (opts.name) {\n      case settingsNames.filter:\n        if (opts.component) {\n          result = this.putFilterState(opts.component, value);\n        }\n        break;\n      default:\n        result = Promise.resolve();\n    }\n    return result;\n  }\n\n  getColumn( schemaType: string ): Promise<IDexieColumn | undefined> {\n    return this.ready.then(() => {\n      return this.columns?.get({ userId: this.user?.id, schemaType });\n    });\n  }\n\n  saveColumn( schemaType: string, params: any ): Promise<number | undefined> {\n    return this.ready\n      .then(() => this.getColumn(schemaType))\n      .then(( column ) => {\n        if (column && column.id) {\n          return this.columns?.update(column.id, { userId: this.user?.id, schemaType, params });\n        } else {\n          return this.columns?.add({ userId: this.user?.id || 0, schemaType, params });\n        }\n      });\n  }\n\n  getAppSettings(): Promise<IDexieAppSettings | undefined> {\n    return this.ready.then(() => {\n      return this.appSettings?.get({ userId: this.user?.id });\n    });\n  }\n\n  saveAppSettings( params: any ): Promise<IDexieAppSettings | number | undefined> {\n    return this.ready.then(() => {\n      return this.getAppSettings().then(( appSettings ) => {\n        if (appSettings && appSettings.id) {\n          return this.appSettings?.update(appSettings?.id, { userId: this.user?.id, params });\n        } else {\n          return this.appSettings?.add({ userId: this.user?.id || 0, params });\n        }\n      });\n    });\n  }\n\n  getComponentState( componentId: string, scope: string ): any {\n    return this.ready.then(() => {\n      return this.collapsedState?.get({ userId: this.user?.id, componentId, scope });\n    });\n  }\n\n  updateComponentState( componentId: string, scope: string, data: any ): void {\n    let search = { userId: this.user?.id || 0, componentId, scope };\n    this.collapsedState?.get(search, res => {\n      res = Object.assign(res || search, { data });\n      this.collapsedState?.put(res);\n    });\n  }\n\n  setUserToNotificatedList(): Promise<number | undefined> {\n    return this.ready.then(() => {\n      const { username, entityKey } = this.authService;\n      const newUser: IDexieUser = {\n        username: username,\n        entityKey: entityKey,\n      };\n\n      return this.usersNotificatedAboutNewMenu?.add(newUser);\n    });\n  }\n\n  isNotUserInNotificatedList(): Promise<boolean | undefined> {\n    return this.ready.then(() => {\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n      };\n\n      return this.usersNotificatedAboutNewMenu?.get(findOptions)\n        .then(user => !user);\n    });\n  }\n\n  getFilterState( componentName: string | undefined ): Promise<any> {\n    return this.ready.then(() => {\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        componentName,\n      };\n      return this.filterStates?.get(findOptions)\n        .then(filterState => {\n          return filterState ? JSON.parse(filterState.state) : {};\n        });\n    });\n  }\n\n  putFilterState( componentName: string, state: Record<string, any> ): Promise<number | undefined> {\n    return this.ready.then(() => {\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        componentName,\n      };\n\n      const newState = JSON.stringify(state);\n      const createOptions: IDexieFilterState = { ...findOptions, state: newState };\n\n      return this.filterStates?.get(findOptions)\n        .then(row => {\n          if (row && row.id) {\n            return this.filterStates?.update(row.id, { state: newState });\n          }\n          if (!!createOptions) {\n            return this.filterStates?.add(createOptions);\n          }\n        });\n    });\n  }\n\n  getRunSettings( path: string | undefined ): Promise<any> {\n    return this.ready.then(() => {\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        path,\n      };\n      return this.runSettings?.get(findOptions)\n        .then(settings => {\n          return settings ? JSON.parse(settings.params) : {};\n        });\n    });\n  }\n\n  putRunSettings( path: string, params: Record<string, any> ): Promise<number | undefined> {\n    return this.ready.then(() => {\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        path,\n      };\n\n      const newParams = JSON.stringify(params);\n      const createOptions: IDexieRunSettings = { ...findOptions, params: newParams };\n\n      return this.runSettings?.get(findOptions)\n        .then(row => {\n          if (row && row.id) {\n            return this.runSettings?.update(row.id, { params: newParams });\n          }\n          if (!!createOptions) {\n            return this.runSettings?.add(createOptions);\n          }\n        });\n    });\n  }\n\n  private _subscribeUser(): void {\n    this.authService.logged.asObservable().subscribe(() => {\n      if (this.authService.isLogged()) {\n        let activeKey = this.authService.entityKey;\n        if (activeKey && this.authService.username) {\n          this._getOrCreateUser(this.authService.username, activeKey);\n        }\n      }\n    });\n  }\n\n  private _getOrCreateUser( username: string, entityKey: string ): Promise<IDexieUser | undefined> {\n    let userObj = { username, entityKey };\n\n    // @ts-ignore\n    return this.users?.get(userObj)\n      .then(this._saveUser.bind(this, userObj))\n      .catch(console.error.bind(console));\n  }\n\n  private _saveUser( userObj: any, user: any ): void {\n    if (user) {\n      this.user = user;\n      this._ready.next(undefined);\n    } else {\n      this.users?.add(Object.assign({}, userObj))\n        .then(() => {\n          this.users?.get(userObj).then(this._saveUser.bind(this, userObj)).catch(console.error.bind(console));\n        })\n        .catch(console.error.bind(console));\n    }\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,MAAM,EAAEC,UAAU,EAAEC,cAAc,QAAQ,eAAe;AAClE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,MAAM;AACpC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAEcC,aAAa,QAEpB,eAAe;AAEtB,OAAO,MAAMC,WAAW,GAAG,IAAIN,cAAc,CAAS,sBAAsB,CAAC;AAGtE,IAAMO,cAAc,IAAAC,eAAA,GAApB,MAAMD,cAAe,SAAQN,KAAK;EAavC,IAAIQ,KAAKA,CAAA;IACP,OAAO,CAAC,IAAI,CAACC,WAAW,GAAGC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAID,OAAO,CAAGC,OAAO,IAAK;MACzE,IAAI,CAACC,MAAM,CAACC,IAAI,CACdX,IAAI,CAAC,CAAC,CAAC,CACR,CAACY,SAAS,CAAC,MAAK;QACfH,OAAO,CAAC,EAAE,CAAC;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAI,YAAqBN,WAA6B,EAChBO,OAAe;IAE/C,KAAK,CAACA,OAAO,CAAC;IAHK,KAAAP,WAAW,GAAXA,WAAW;IAZhC,KAAAG,MAAM,GAAG,IAAIX,aAAa,CAAM,CAAC,CAAC;IAgBhC,IAAI;MACF,IAAI,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACrBC,OAAO,EAAE,sBAAsB;QAC/BC,QAAQ,EAAE;OACX,CAAC;MAEF,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACrBG,KAAK,EAAE,6BAA6B;QACpCC,OAAO,EAAE,kCAAkC;QAC3CC,WAAW,EAAE,sBAAsB;QACnCJ,OAAO,EAAE,kCAAkC;QAC3CK,SAAS,EAAE;OACZ,CAAC;MAEF,IAAI,CAACP,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACrBO,cAAc,EAAE;OACjB,CAAC;MAEF,IAAI,CAACR,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACrBI,OAAO,EAAE;OACV,CAAC;MAEF,IAAI,CAACL,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACrBQ,4BAA4B,EAAE;OAC/B,CAAC;MAEF,IAAI,CAACT,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACrBS,YAAY,EAAE;OACf,CAAC;MAEF,IAAI,CAACV,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACrBU,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAACX,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACrBW,WAAW,EAAE;OACd,CAAC;IACJ,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEF,CAAC,CAAC;IACvD;IACA,IAAI,CAACG,cAAc,EAAE;EACvB;EAEAC,UAAUA,CAAEC,IAAwB;IAClC,IAAIC,MAAM;IACV,QAAQD,IAAI,CAACE,IAAI;MACf,KAAK,QAAQ;QACXD,MAAM,GAAG,IAAI,CAACE,cAAc,CAACH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,SAAS,CAAC;QAC7C;MACF;QACEH,MAAM,GAAG1B,OAAO,CAACC,OAAO,EAAE;IAC9B;IACA,OAAOyB,MAAM;EACf;EAEAI,UAAUA,CAAEL,IAAwB,EAAEM,KAAU;IAC9C,IAAIL,MAAM;IACV,QAAQD,IAAI,CAACE,IAAI;MACf,KAAKjC,aAAa,CAACsC,MAAM;QACvB,IAAIP,IAAI,CAACI,SAAS,EAAE;UAClBH,MAAM,GAAG,IAAI,CAACO,cAAc,CAACR,IAAI,CAACI,SAAS,EAAEE,KAAK,CAAC;QACrD;QACA;MACF;QACEL,MAAM,GAAG1B,OAAO,CAACC,OAAO,EAAE;IAC9B;IACA,OAAOyB,MAAM;EACf;EAEAQ,SAASA,CAAEC,UAAkB;IAC3B,OAAO,IAAI,CAACrC,KAAK,CAACsC,IAAI,CAAC,MAAK;MAAA,IAAAC,aAAA,EAAAC,UAAA;MAC1B,QAAAD,aAAA,GAAO,IAAI,CAACzB,OAAO,cAAAyB,aAAA,uBAAZA,aAAA,CAAcE,GAAG,CAAC;QAAEC,MAAM,GAAAF,UAAA,GAAE,IAAI,CAACG,IAAI,cAAAH,UAAA,uBAATA,UAAA,CAAWI,EAAE;QAAEP;MAAU,CAAE,CAAC;IACjE,CAAC,CAAC;EACJ;EAEAQ,UAAUA,CAAER,UAAkB,EAAES,MAAW;IACzC,OAAO,IAAI,CAAC9C,KAAK,CACdsC,IAAI,CAAC,MAAM,IAAI,CAACF,SAAS,CAACC,UAAU,CAAC,CAAC,CACtCC,IAAI,CAAGS,MAAM,IAAK;MACjB,IAAIA,MAAM,IAAIA,MAAM,CAACH,EAAE,EAAE;QAAA,IAAAI,cAAA,EAAAC,WAAA;QACvB,QAAAD,cAAA,GAAO,IAAI,CAAClC,OAAO,cAAAkC,cAAA,uBAAZA,cAAA,CAAcE,MAAM,CAACH,MAAM,CAACH,EAAE,EAAE;UAAEF,MAAM,GAAAO,WAAA,GAAE,IAAI,CAACN,IAAI,cAAAM,WAAA,uBAATA,WAAA,CAAWL,EAAE;UAAEP,UAAU;UAAES;QAAM,CAAE,CAAC;MACvF,CAAC,MAAM;QAAA,IAAAK,cAAA,EAAAC,WAAA;QACL,QAAAD,cAAA,GAAO,IAAI,CAACrC,OAAO,cAAAqC,cAAA,uBAAZA,cAAA,CAAcE,GAAG,CAAC;UAAEX,MAAM,EAAE,EAAAU,WAAA,OAAI,CAACT,IAAI,cAAAS,WAAA,uBAATA,WAAA,CAAWR,EAAE,KAAI,CAAC;UAAEP,UAAU;UAAES;QAAM,CAAE,CAAC;MAC9E;IACF,CAAC,CAAC;EACN;EAEAQ,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACtD,KAAK,CAACsC,IAAI,CAAC,MAAK;MAAA,IAAAiB,iBAAA,EAAAC,WAAA;MAC1B,QAAAD,iBAAA,GAAO,IAAI,CAACxC,WAAW,cAAAwC,iBAAA,uBAAhBA,iBAAA,CAAkBd,GAAG,CAAC;QAAEC,MAAM,GAAAc,WAAA,GAAE,IAAI,CAACb,IAAI,cAAAa,WAAA,uBAATA,WAAA,CAAWZ;MAAE,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ;EAEAa,eAAeA,CAAEX,MAAW;IAC1B,OAAO,IAAI,CAAC9C,KAAK,CAACsC,IAAI,CAAC,MAAK;MAC1B,OAAO,IAAI,CAACgB,cAAc,EAAE,CAAChB,IAAI,CAAGvB,WAAW,IAAK;QAClD,IAAIA,WAAW,IAAIA,WAAW,CAAC6B,EAAE,EAAE;UAAA,IAAAc,kBAAA,EAAAC,WAAA;UACjC,QAAAD,kBAAA,GAAO,IAAI,CAAC3C,WAAW,cAAA2C,kBAAA,uBAAhBA,kBAAA,CAAkBR,MAAM,CAACnC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6B,EAAE,EAAE;YAAEF,MAAM,GAAAiB,WAAA,GAAE,IAAI,CAAChB,IAAI,cAAAgB,WAAA,uBAATA,WAAA,CAAWf,EAAE;YAAEE;UAAM,CAAE,CAAC;QACrF,CAAC,MAAM;UAAA,IAAAc,kBAAA,EAAAC,WAAA;UACL,QAAAD,kBAAA,GAAO,IAAI,CAAC7C,WAAW,cAAA6C,kBAAA,uBAAhBA,kBAAA,CAAkBP,GAAG,CAAC;YAAEX,MAAM,EAAE,EAAAmB,WAAA,OAAI,CAAClB,IAAI,cAAAkB,WAAA,uBAATA,WAAA,CAAWjB,EAAE,KAAI,CAAC;YAAEE;UAAM,CAAE,CAAC;QACtE;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAgB,iBAAiBA,CAAEC,WAAmB,EAAEC,KAAa;IACnD,OAAO,IAAI,CAAChE,KAAK,CAACsC,IAAI,CAAC,MAAK;MAAA,IAAA2B,oBAAA,EAAAC,WAAA;MAC1B,QAAAD,oBAAA,GAAO,IAAI,CAAChD,cAAc,cAAAgD,oBAAA,uBAAnBA,oBAAA,CAAqBxB,GAAG,CAAC;QAAEC,MAAM,GAAAwB,WAAA,GAAE,IAAI,CAACvB,IAAI,cAAAuB,WAAA,uBAATA,WAAA,CAAWtB,EAAE;QAAEmB,WAAW;QAAEC;MAAK,CAAE,CAAC;IAChF,CAAC,CAAC;EACJ;EAEAG,oBAAoBA,CAAEJ,WAAmB,EAAEC,KAAa,EAAEI,IAAS;IAAA,IAAAC,WAAA,EAAAC,qBAAA;IACjE,IAAIC,MAAM,GAAG;MAAE7B,MAAM,EAAE,EAAA2B,WAAA,OAAI,CAAC1B,IAAI,cAAA0B,WAAA,uBAATA,WAAA,CAAWzB,EAAE,KAAI,CAAC;MAAEmB,WAAW;MAAEC;IAAK,CAAE;IAC/D,CAAAM,qBAAA,OAAI,CAACrD,cAAc,cAAAqD,qBAAA,eAAnBA,qBAAA,CAAqB7B,GAAG,CAAC8B,MAAM,EAAEC,GAAG,IAAG;MAAA,IAAAC,qBAAA;MACrCD,GAAG,GAAGE,MAAM,CAACC,MAAM,CAACH,GAAG,IAAID,MAAM,EAAE;QAAEH;MAAI,CAAE,CAAC;MAC5C,CAAAK,qBAAA,OAAI,CAACxD,cAAc,cAAAwD,qBAAA,eAAnBA,qBAAA,CAAqBG,GAAG,CAACJ,GAAG,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEAK,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAAC7E,KAAK,CAACsC,IAAI,CAAC,MAAK;MAAA,IAAAwC,qBAAA;MAC1B,MAAM;QAAEC,QAAQ;QAAEC;MAAS,CAAE,GAAG,IAAI,CAAC/E,WAAW;MAChD,MAAMgF,OAAO,GAAe;QAC1BF,QAAQ,EAAEA,QAAQ;QAClBC,SAAS,EAAEA;OACZ;MAED,QAAAF,qBAAA,GAAO,IAAI,CAAC5D,4BAA4B,cAAA4D,qBAAA,uBAAjCA,qBAAA,CAAmCzB,GAAG,CAAC4B,OAAO,CAAC;IACxD,CAAC,CAAC;EACJ;EAEAC,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAAClF,KAAK,CAACsC,IAAI,CAAC,MAAK;MAAA,IAAA6C,sBAAA;MAC1B,MAAMC,WAAW,GAAG;QAClBL,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAC8E,QAAQ;QACnCC,SAAS,EAAE,IAAI,CAAC/E,WAAW,CAAC+E;OAC7B;MAED,QAAAG,sBAAA,GAAO,IAAI,CAACjE,4BAA4B,cAAAiE,sBAAA,uBAAjCA,sBAAA,CAAmC1C,GAAG,CAAC2C,WAAW,CAAC,CACvD9C,IAAI,CAACK,IAAI,IAAI,CAACA,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ;EAEAb,cAAcA,CAAEuD,aAAiC;IAC/C,OAAO,IAAI,CAACrF,KAAK,CAACsC,IAAI,CAAC,MAAK;MAAA,IAAAgD,kBAAA;MAC1B,MAAMF,WAAW,GAAG;QAClBL,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAC8E,QAAQ;QACnCC,SAAS,EAAE,IAAI,CAAC/E,WAAW,CAAC+E,SAAS;QACrCK;OACD;MACD,QAAAC,kBAAA,GAAO,IAAI,CAACnE,YAAY,cAAAmE,kBAAA,uBAAjBA,kBAAA,CAAmB7C,GAAG,CAAC2C,WAAW,CAAC,CACvC9C,IAAI,CAACiD,WAAW,IAAG;QAClB,OAAOA,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACF,WAAW,CAACG,KAAK,CAAC,GAAG,EAAE;MACzD,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAvD,cAAcA,CAAEkD,aAAqB,EAAEK,KAA0B;IAC/D,OAAO,IAAI,CAAC1F,KAAK,CAACsC,IAAI,CAAC,MAAK;MAAA,IAAAqD,mBAAA;MAC1B,MAAMP,WAAW,GAAG;QAClBL,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAC8E,QAAQ;QACnCC,SAAS,EAAE,IAAI,CAAC/E,WAAW,CAAC+E,SAAS;QACrCK;OACD;MAED,MAAMO,QAAQ,GAAGJ,IAAI,CAACK,SAAS,CAACH,KAAK,CAAC;MACtC,MAAMI,aAAa,GAAAC,aAAA,CAAAA,aAAA,KAA2BX,WAAW;QAAEM,KAAK,EAAEE;MAAQ,EAAE;MAE5E,QAAAD,mBAAA,GAAO,IAAI,CAACxE,YAAY,cAAAwE,mBAAA,uBAAjBA,mBAAA,CAAmBlD,GAAG,CAAC2C,WAAW,CAAC,CACvC9C,IAAI,CAAC0D,GAAG,IAAG;QACV,IAAIA,GAAG,IAAIA,GAAG,CAACpD,EAAE,EAAE;UAAA,IAAAqD,mBAAA;UACjB,QAAAA,mBAAA,GAAO,IAAI,CAAC9E,YAAY,cAAA8E,mBAAA,uBAAjBA,mBAAA,CAAmB/C,MAAM,CAAC8C,GAAG,CAACpD,EAAE,EAAE;YAAE8C,KAAK,EAAEE;UAAQ,CAAE,CAAC;QAC/D;QACA,IAAI,CAAC,CAACE,aAAa,EAAE;UAAA,IAAAI,mBAAA;UACnB,QAAAA,mBAAA,GAAO,IAAI,CAAC/E,YAAY,cAAA+E,mBAAA,uBAAjBA,mBAAA,CAAmB7C,GAAG,CAACyC,aAAa,CAAC;QAC9C;MACF,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAK,cAAcA,CAAEC,IAAwB;IACtC,OAAO,IAAI,CAACpG,KAAK,CAACsC,IAAI,CAAC,MAAK;MAAA,IAAA+D,iBAAA;MAC1B,MAAMjB,WAAW,GAAG;QAClBL,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAC8E,QAAQ;QACnCC,SAAS,EAAE,IAAI,CAAC/E,WAAW,CAAC+E,SAAS;QACrCoB;OACD;MACD,QAAAC,iBAAA,GAAO,IAAI,CAAChF,WAAW,cAAAgF,iBAAA,uBAAhBA,iBAAA,CAAkB5D,GAAG,CAAC2C,WAAW,CAAC,CACtC9C,IAAI,CAAC1B,QAAQ,IAAG;QACf,OAAOA,QAAQ,GAAG4E,IAAI,CAACC,KAAK,CAAC7E,QAAQ,CAACkC,MAAM,CAAC,GAAG,EAAE;MACpD,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAwD,cAAcA,CAAEF,IAAY,EAAEtD,MAA2B;IACvD,OAAO,IAAI,CAAC9C,KAAK,CAACsC,IAAI,CAAC,MAAK;MAAA,IAAAiE,kBAAA;MAC1B,MAAMnB,WAAW,GAAG;QAClBL,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAC8E,QAAQ;QACnCC,SAAS,EAAE,IAAI,CAAC/E,WAAW,CAAC+E,SAAS;QACrCoB;OACD;MAED,MAAMI,SAAS,GAAGhB,IAAI,CAACK,SAAS,CAAC/C,MAAM,CAAC;MACxC,MAAMgD,aAAa,GAAAC,aAAA,CAAAA,aAAA,KAA2BX,WAAW;QAAEtC,MAAM,EAAE0D;MAAS,EAAE;MAE9E,QAAAD,kBAAA,GAAO,IAAI,CAAClF,WAAW,cAAAkF,kBAAA,uBAAhBA,kBAAA,CAAkB9D,GAAG,CAAC2C,WAAW,CAAC,CACtC9C,IAAI,CAAC0D,GAAG,IAAG;QACV,IAAIA,GAAG,IAAIA,GAAG,CAACpD,EAAE,EAAE;UAAA,IAAA6D,kBAAA;UACjB,QAAAA,kBAAA,GAAO,IAAI,CAACpF,WAAW,cAAAoF,kBAAA,uBAAhBA,kBAAA,CAAkBvD,MAAM,CAAC8C,GAAG,CAACpD,EAAE,EAAE;YAAEE,MAAM,EAAE0D;UAAS,CAAE,CAAC;QAChE;QACA,IAAI,CAAC,CAACV,aAAa,EAAE;UAAA,IAAAY,kBAAA;UACnB,QAAAA,kBAAA,GAAO,IAAI,CAACrF,WAAW,cAAAqF,kBAAA,uBAAhBA,kBAAA,CAAkBrD,GAAG,CAACyC,aAAa,CAAC;QAC7C;MACF,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEQrE,cAAcA,CAAA;IACpB,IAAI,CAACxB,WAAW,CAAC0G,MAAM,CAACC,YAAY,EAAE,CAACtG,SAAS,CAAC,MAAK;MACpD,IAAI,IAAI,CAACL,WAAW,CAAC4G,QAAQ,EAAE,EAAE;QAC/B,IAAIC,SAAS,GAAG,IAAI,CAAC7G,WAAW,CAAC+E,SAAS;QAC1C,IAAI8B,SAAS,IAAI,IAAI,CAAC7G,WAAW,CAAC8E,QAAQ,EAAE;UAC1C,IAAI,CAACgC,gBAAgB,CAAC,IAAI,CAAC9G,WAAW,CAAC8E,QAAQ,EAAE+B,SAAS,CAAC;QAC7D;MACF;IACF,CAAC,CAAC;EACJ;EAEQC,gBAAgBA,CAAEhC,QAAgB,EAAEC,SAAiB;IAAA,IAAAgC,WAAA;IAC3D,IAAIC,OAAO,GAAG;MAAElC,QAAQ;MAAEC;IAAS,CAAE;IAErC;IACA,QAAAgC,WAAA,GAAO,IAAI,CAACnG,KAAK,cAAAmG,WAAA,uBAAVA,WAAA,CAAYvE,GAAG,CAACwE,OAAO,CAAC,CAC5B3E,IAAI,CAAC,IAAI,CAAC4E,SAAS,CAACC,IAAI,CAAC,IAAI,EAAEF,OAAO,CAAC,CAAC,CACxCG,KAAK,CAAC7F,OAAO,CAACC,KAAK,CAAC2F,IAAI,CAAC5F,OAAO,CAAC,CAAC;EACvC;EAEQ2F,SAASA,CAAED,OAAY,EAAEtE,IAAS;IACxC,IAAIA,IAAI,EAAE;MACR,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACvC,MAAM,CAACiH,IAAI,CAACC,SAAS,CAAC;IAC7B,CAAC,MAAM;MAAA,IAAAC,YAAA;MACL,CAAAA,YAAA,OAAI,CAAC1G,KAAK,cAAA0G,YAAA,eAAVA,YAAA,CAAYlE,GAAG,CAACqB,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEsC,OAAO,CAAC,CAAC,CACxC3E,IAAI,CAAC,MAAK;QAAA,IAAAkF,YAAA;QACT,CAAAA,YAAA,OAAI,CAAC3G,KAAK,cAAA2G,YAAA,eAAVA,YAAA,CAAY/E,GAAG,CAACwE,OAAO,CAAC,CAAC3E,IAAI,CAAC,IAAI,CAAC4E,SAAS,CAACC,IAAI,CAAC,IAAI,EAAEF,OAAO,CAAC,CAAC,CAACG,KAAK,CAAC7F,OAAO,CAACC,KAAK,CAAC2F,IAAI,CAAC5F,OAAO,CAAC,CAAC;MACtG,CAAC,CAAC,CACD6F,KAAK,CAAC7F,OAAO,CAACC,KAAK,CAAC2F,IAAI,CAAC5F,OAAO,CAAC,CAAC;IACvC;EACF;;;;;;UA3PclC,MAAM;IAAAoI,IAAA,GAAC5H,WAAW;EAAA;AAAA,E;AAxBrBC,cAAc,GAAA4H,UAAA,EAD1BpI,UAAU,EAAE,C,EACAQ,cAAc,CAoR1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}