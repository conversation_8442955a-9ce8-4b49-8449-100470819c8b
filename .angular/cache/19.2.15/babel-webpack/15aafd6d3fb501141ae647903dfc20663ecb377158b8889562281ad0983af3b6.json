{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _TextDirective, _SliderDirective, _ColorPickerService, _ColorPickerComponent, _ColorPickerDirective, _ColorPickerModule;\nconst _c0 = [\"dialogPopup\"];\nconst _c1 = [\"hueSlider\"];\nconst _c2 = [\"alphaSlider\"];\nfunction _ColorPickerComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"arrow arrow-\", ctx_r1.cpUsePosition, \"\");\n    i0.ɵɵstyleProp(\"left\", ctx_r1.cpArrowPosition)(\"top\", ctx_r1.arrowTop, \"px\");\n  }\n}\nfunction _ColorPickerComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"newValue\", function _ColorPickerComponent_div_3_Template_div_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onColorChange($event));\n    })(\"dragStart\", function _ColorPickerComponent_div_3_Template_div_dragStart_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDragStart(\"saturation-lightness\"));\n    })(\"dragEnd\", function _ColorPickerComponent_div_3_Template_div_dragEnd_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDragEnd(\"saturation-lightness\"));\n    });\n    i0.ɵɵelement(1, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.hueSliderColor);\n    i0.ɵɵproperty(\"rgX\", 1)(\"rgY\", 1);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"top\", ctx_r1.slider == null ? null : ctx_r1.slider.v, \"px\")(\"left\", ctx_r1.slider == null ? null : ctx_r1.slider.s, \"px\");\n  }\n}\nfunction _ColorPickerComponent__svg_svg_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 29);\n    i0.ɵɵelement(1, \"path\", 30)(2, \"path\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _ColorPickerComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function _ColorPickerComponent_button_9_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAddPresetColor($event, ctx_r1.selectedColor));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.cpAddColorButtonClass);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.cpPresetColors && ctx_r1.cpPresetColors.length >= ctx_r1.cpMaxPresetColorsLength);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.cpAddColorButtonText, \" \");\n  }\n}\nfunction _ColorPickerComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 33);\n  }\n}\nfunction _ColorPickerComponent_div_21_input_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_21_input_6_Template_input_keyup_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_21_input_6_Template_input_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAlphaInput($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rg\", 1)(\"value\", ctx_r1.cmykText == null ? null : ctx_r1.cmykText.a);\n  }\n}\nfunction _ColorPickerComponent_div_21_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _ColorPickerComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_21_Template_input_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_21_Template_input_newValue_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCyanInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_21_Template_input_keyup_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_21_Template_input_newValue_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMagentaInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_21_Template_input_keyup_enter_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_21_Template_input_newValue_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onYellowInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_21_Template_input_keyup_enter_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_21_Template_input_newValue_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBlackInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, _ColorPickerComponent_div_21_input_6_Template, 1, 2, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 35)(8, \"div\");\n    i0.ɵɵtext(9, \"C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\");\n    i0.ɵɵtext(11, \"M\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13, \"Y\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\");\n    i0.ɵɵtext(15, \"K\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, _ColorPickerComponent_div_21_div_16_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.format !== 3 ? \"none\" : \"block\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.cmykText == null ? null : ctx_r1.cmykText.c);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.cmykText == null ? null : ctx_r1.cmykText.m);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.cmykText == null ? null : ctx_r1.cmykText.y);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.cmykText == null ? null : ctx_r1.cmykText.k);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n  }\n}\nfunction _ColorPickerComponent_div_22_input_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_22_input_5_Template_input_keyup_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_22_input_5_Template_input_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAlphaInput($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rg\", 1)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.a);\n  }\n}\nfunction _ColorPickerComponent_div_22_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _ColorPickerComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 35)(2, \"input\", 41);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_22_Template_input_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_22_Template_input_newValue_2_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHueInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_22_Template_input_keyup_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_22_Template_input_newValue_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSaturationInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_22_Template_input_keyup_enter_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_22_Template_input_newValue_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onLightnessInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, _ColorPickerComponent_div_22_input_5_Template, 1, 2, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 35)(7, \"div\");\n    i0.ɵɵtext(8, \"H\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\");\n    i0.ɵɵtext(10, \"S\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\");\n    i0.ɵɵtext(12, \"L\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, _ColorPickerComponent_div_22_div_13_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.format !== 2 ? \"none\" : \"block\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rg\", 360)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.h);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.s);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.l);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n  }\n}\nfunction _ColorPickerComponent_div_23_input_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_23_input_5_Template_input_keyup_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_23_input_5_Template_input_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAlphaInput($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rg\", 1)(\"value\", ctx_r1.rgbaText == null ? null : ctx_r1.rgbaText.a);\n  }\n}\nfunction _ColorPickerComponent_div_23_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _ColorPickerComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 35)(2, \"input\", 43);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_23_Template_input_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_23_Template_input_newValue_2_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRedInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 43);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_23_Template_input_keyup_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_23_Template_input_newValue_3_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onGreenInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 43);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_23_Template_input_keyup_enter_4_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_23_Template_input_newValue_4_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBlueInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, _ColorPickerComponent_div_23_input_5_Template, 1, 2, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 35)(7, \"div\");\n    i0.ɵɵtext(8, \"R\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\");\n    i0.ɵɵtext(10, \"G\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\");\n    i0.ɵɵtext(12, \"B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, _ColorPickerComponent_div_23_div_13_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.format !== 1 ? \"none\" : \"block\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rg\", 255)(\"value\", ctx_r1.rgbaText == null ? null : ctx_r1.rgbaText.r);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 255)(\"value\", ctx_r1.rgbaText == null ? null : ctx_r1.rgbaText.g);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rg\", 255)(\"value\", ctx_r1.rgbaText == null ? null : ctx_r1.rgbaText.b);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n  }\n}\nfunction _ColorPickerComponent_div_24_input_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_24_input_3_Template_input_keyup_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_24_input_3_Template_input_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAlphaInput($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rg\", 1)(\"value\", ctx_r1.hexAlpha);\n  }\n}\nfunction _ColorPickerComponent_div_24_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _ColorPickerComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 35)(2, \"input\", 45);\n    i0.ɵɵlistener(\"blur\", function _ColorPickerComponent_div_24_Template_input_blur_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHexInput(null));\n    })(\"keyup.enter\", function _ColorPickerComponent_div_24_Template_input_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_24_Template_input_newValue_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHexInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, _ColorPickerComponent_div_24_input_3_Template, 1, 2, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"div\");\n    i0.ɵɵtext(6, \"Hex\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, _ColorPickerComponent_div_24_div_7_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.format !== 0 ? \"none\" : \"block\");\n    i0.ɵɵclassProp(\"hex-alpha\", ctx_r1.cpAlphaChannel === \"forced\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.hexText);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel === \"forced\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel === \"forced\");\n  }\n}\nfunction _ColorPickerComponent_div_25_input_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_25_input_3_Template_input_keyup_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_25_input_3_Template_input_newValue_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAlphaInput($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"rg\", 1)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.a);\n  }\n}\nfunction _ColorPickerComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 35)(2, \"input\", 36);\n    i0.ɵɵlistener(\"keyup.enter\", function _ColorPickerComponent_div_25_Template_input_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    })(\"newValue\", function _ColorPickerComponent_div_25_Template_input_newValue_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onValueInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, _ColorPickerComponent_div_25_input_3_Template, 1, 2, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"div\");\n    i0.ɵɵtext(6, \"V\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \"A\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rg\", 100)(\"value\", ctx_r1.hslaText == null ? null : ctx_r1.hslaText.l);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAlphaChannel !== \"disabled\");\n  }\n}\nfunction _ColorPickerComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 48);\n    i0.ɵɵlistener(\"click\", function _ColorPickerComponent_div_26_Template_span_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFormatToggle(-1));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 48);\n    i0.ɵɵlistener(\"click\", function _ColorPickerComponent_div_26_Template_span_click_2_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFormatToggle(1));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction _ColorPickerComponent_div_27_div_4_div_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵlistener(\"click\", function _ColorPickerComponent_div_27_div_4_div_1_span_1_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const color_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onRemovePresetColor($event, color_r17));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(ctx_r1.cpRemoveColorButtonClass);\n  }\n}\nfunction _ColorPickerComponent_div_27_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵlistener(\"click\", function _ColorPickerComponent_div_27_div_4_div_1_Template_div_click_0_listener() {\n      const color_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setColorFromString(color_r17));\n    });\n    i0.ɵɵtemplate(1, _ColorPickerComponent_div_27_div_4_div_1_span_1_Template, 1, 3, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const color_r17 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", color_r17);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpAddColorButton);\n  }\n}\nfunction _ColorPickerComponent_div_27_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, _ColorPickerComponent_div_27_div_4_div_1_Template, 2, 3, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cpPresetColorsClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.cpPresetColors);\n  }\n}\nfunction _ColorPickerComponent_div_27_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cpPresetEmptyMessageClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cpPresetEmptyMessage);\n  }\n}\nfunction _ColorPickerComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"hr\");\n    i0.ɵɵelementStart(2, \"div\", 50);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, _ColorPickerComponent_div_27_div_4_Template, 2, 4, \"div\", 51)(5, _ColorPickerComponent_div_27_div_5_Template, 2, 4, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.cpPresetLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpPresetColors == null ? null : ctx_r1.cpPresetColors.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.cpPresetColors == null ? null : ctx_r1.cpPresetColors.length) && ctx_r1.cpAddColorButton);\n  }\n}\nfunction _ColorPickerComponent_div_28_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function _ColorPickerComponent_div_28_button_1_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCancelColor($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cpCancelButtonClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cpCancelButtonText);\n  }\n}\nfunction _ColorPickerComponent_div_28_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function _ColorPickerComponent_div_28_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAcceptColor($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cpOKButtonClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.cpOKButtonText);\n  }\n}\nfunction _ColorPickerComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtemplate(1, _ColorPickerComponent_div_28_button_1_Template, 2, 4, \"button\", 57)(2, _ColorPickerComponent_div_28_button_2_Template, 2, 4, \"button\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpCancelButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cpOKButton);\n  }\n}\nfunction _ColorPickerComponent_div_29_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction _ColorPickerComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, _ColorPickerComponent_div_29_ng_container_1_Template, 1, 0, \"ng-container\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.cpExtraTemplate);\n  }\n}\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, HostListener, Injectable, PLATFORM_ID, Component, ViewEncapsulation, Inject, ViewChild, Injector, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nvar ColorFormats;\n(function (ColorFormats) {\n  ColorFormats[ColorFormats[\"HEX\"] = 0] = \"HEX\";\n  ColorFormats[ColorFormats[\"RGBA\"] = 1] = \"RGBA\";\n  ColorFormats[ColorFormats[\"HSLA\"] = 2] = \"HSLA\";\n  ColorFormats[ColorFormats[\"CMYK\"] = 3] = \"CMYK\";\n})(ColorFormats || (ColorFormats = {}));\nclass Rgba {\n  constructor(r, g, b, a) {\n    _defineProperty(this, \"r\", void 0);\n    _defineProperty(this, \"g\", void 0);\n    _defineProperty(this, \"b\", void 0);\n    _defineProperty(this, \"a\", void 0);\n    this.r = r;\n    this.g = g;\n    this.b = b;\n    this.a = a;\n  }\n}\nclass Hsva {\n  constructor(h, s, v, a) {\n    _defineProperty(this, \"h\", void 0);\n    _defineProperty(this, \"s\", void 0);\n    _defineProperty(this, \"v\", void 0);\n    _defineProperty(this, \"a\", void 0);\n    this.h = h;\n    this.s = s;\n    this.v = v;\n    this.a = a;\n  }\n}\nclass Hsla {\n  constructor(h, s, l, a) {\n    _defineProperty(this, \"h\", void 0);\n    _defineProperty(this, \"s\", void 0);\n    _defineProperty(this, \"l\", void 0);\n    _defineProperty(this, \"a\", void 0);\n    this.h = h;\n    this.s = s;\n    this.l = l;\n    this.a = a;\n  }\n}\nclass Cmyk {\n  constructor(c, m, y, k, a = 1) {\n    _defineProperty(this, \"c\", void 0);\n    _defineProperty(this, \"m\", void 0);\n    _defineProperty(this, \"y\", void 0);\n    _defineProperty(this, \"k\", void 0);\n    _defineProperty(this, \"a\", void 0);\n    this.c = c;\n    this.m = m;\n    this.y = y;\n    this.k = k;\n    this.a = a;\n  }\n}\nfunction calculateAutoPositioning(elBounds, triggerElBounds) {\n  // Defaults\n  let usePositionX = 'right';\n  let usePositionY = 'bottom';\n  // Calculate collisions\n  const {\n    height,\n    width\n  } = elBounds;\n  const {\n    top,\n    left\n  } = triggerElBounds;\n  const bottom = top + triggerElBounds.height;\n  const right = left + triggerElBounds.width;\n  const collisionTop = top - height < 0;\n  const collisionBottom = bottom + height > (window.innerHeight || document.documentElement.clientHeight);\n  const collisionLeft = left - width < 0;\n  const collisionRight = right + width > (window.innerWidth || document.documentElement.clientWidth);\n  const collisionAll = collisionTop && collisionBottom && collisionLeft && collisionRight;\n  // Generate X & Y position values\n  if (collisionBottom) {\n    usePositionY = 'top';\n  }\n  if (collisionTop) {\n    usePositionY = 'bottom';\n  }\n  if (collisionLeft) {\n    usePositionX = 'right';\n  }\n  if (collisionRight) {\n    usePositionX = 'left';\n  }\n  // Choose the largest gap available\n  if (collisionAll) {\n    const postions = ['left', 'right', 'top', 'bottom'];\n    return postions.reduce((prev, next) => elBounds[prev] > elBounds[next] ? prev : next);\n  }\n  if (collisionLeft && collisionRight) {\n    if (collisionTop) {\n      return 'bottom';\n    }\n    if (collisionBottom) {\n      return 'top';\n    }\n    return top > bottom ? 'top' : 'bottom';\n  }\n  if (collisionTop && collisionBottom) {\n    if (collisionLeft) {\n      return 'right';\n    }\n    if (collisionRight) {\n      return 'left';\n    }\n    return left > right ? 'left' : 'right';\n  }\n  return `${usePositionY}-${usePositionX}`;\n}\nfunction detectIE() {\n  let ua = '';\n  if (typeof navigator !== 'undefined') {\n    ua = navigator.userAgent.toLowerCase();\n  }\n  const msie = ua.indexOf('msie ');\n  if (msie > 0) {\n    // IE 10 or older => return version number\n    return parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);\n  }\n  // Other browser\n  return false;\n}\nclass TextDirective {\n  constructor() {\n    _defineProperty(this, \"rg\", void 0);\n    _defineProperty(this, \"text\", void 0);\n    _defineProperty(this, \"newValue\", new EventEmitter());\n  }\n  inputChange(event) {\n    const value = event.target.value;\n    if (this.rg === undefined) {\n      this.newValue.emit(value);\n    } else {\n      const numeric = parseFloat(value);\n      this.newValue.emit({\n        v: numeric,\n        rg: this.rg\n      });\n    }\n  }\n}\n_TextDirective = TextDirective;\n_defineProperty(TextDirective, \"\\u0275fac\", function _TextDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TextDirective)();\n});\n_defineProperty(TextDirective, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _TextDirective,\n  selectors: [[\"\", \"text\", \"\"]],\n  hostBindings: function _TextDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"input\", function _TextDirective_input_HostBindingHandler($event) {\n        return ctx.inputChange($event);\n      });\n    }\n  },\n  inputs: {\n    rg: \"rg\",\n    text: \"text\"\n  },\n  outputs: {\n    newValue: \"newValue\"\n  },\n  standalone: false\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[text]'\n    }]\n  }], null, {\n    rg: [{\n      type: Input\n    }],\n    text: [{\n      type: Input\n    }],\n    newValue: [{\n      type: Output\n    }],\n    inputChange: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }]\n  });\n})();\nclass SliderDirective {\n  mouseDown(event) {\n    this.start(event);\n  }\n  touchStart(event) {\n    this.start(event);\n  }\n  constructor(elRef) {\n    _defineProperty(this, \"elRef\", void 0);\n    _defineProperty(this, \"listenerMove\", void 0);\n    _defineProperty(this, \"listenerStop\", void 0);\n    _defineProperty(this, \"rgX\", void 0);\n    _defineProperty(this, \"rgY\", void 0);\n    _defineProperty(this, \"slider\", void 0);\n    _defineProperty(this, \"dragEnd\", new EventEmitter());\n    _defineProperty(this, \"dragStart\", new EventEmitter());\n    _defineProperty(this, \"newValue\", new EventEmitter());\n    this.elRef = elRef;\n    this.listenerMove = event => this.move(event);\n    this.listenerStop = () => this.stop();\n  }\n  move(event) {\n    event.preventDefault();\n    this.setCursor(event);\n  }\n  start(event) {\n    this.setCursor(event);\n    event.stopPropagation();\n    document.addEventListener('mouseup', this.listenerStop);\n    document.addEventListener('touchend', this.listenerStop);\n    document.addEventListener('mousemove', this.listenerMove);\n    document.addEventListener('touchmove', this.listenerMove);\n    this.dragStart.emit();\n  }\n  stop() {\n    document.removeEventListener('mouseup', this.listenerStop);\n    document.removeEventListener('touchend', this.listenerStop);\n    document.removeEventListener('mousemove', this.listenerMove);\n    document.removeEventListener('touchmove', this.listenerMove);\n    this.dragEnd.emit();\n  }\n  getX(event) {\n    const position = this.elRef.nativeElement.getBoundingClientRect();\n    const pageX = event.pageX !== undefined ? event.pageX : event.touches[0].pageX;\n    return pageX - position.left - window.pageXOffset;\n  }\n  getY(event) {\n    const position = this.elRef.nativeElement.getBoundingClientRect();\n    const pageY = event.pageY !== undefined ? event.pageY : event.touches[0].pageY;\n    return pageY - position.top - window.pageYOffset;\n  }\n  setCursor(event) {\n    const width = this.elRef.nativeElement.offsetWidth;\n    const height = this.elRef.nativeElement.offsetHeight;\n    const x = Math.max(0, Math.min(this.getX(event), width));\n    const y = Math.max(0, Math.min(this.getY(event), height));\n    if (this.rgX !== undefined && this.rgY !== undefined) {\n      this.newValue.emit({\n        s: x / width,\n        v: 1 - y / height,\n        rgX: this.rgX,\n        rgY: this.rgY\n      });\n    } else if (this.rgX === undefined && this.rgY !== undefined) {\n      this.newValue.emit({\n        v: y / height,\n        rgY: this.rgY\n      });\n    } else if (this.rgX !== undefined && this.rgY === undefined) {\n      this.newValue.emit({\n        v: x / width,\n        rgX: this.rgX\n      });\n    }\n  }\n}\n_SliderDirective = SliderDirective;\n_defineProperty(SliderDirective, \"\\u0275fac\", function _SliderDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SliderDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n});\n_defineProperty(SliderDirective, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _SliderDirective,\n  selectors: [[\"\", \"slider\", \"\"]],\n  hostBindings: function _SliderDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mousedown\", function _SliderDirective_mousedown_HostBindingHandler($event) {\n        return ctx.mouseDown($event);\n      })(\"touchstart\", function _SliderDirective_touchstart_HostBindingHandler($event) {\n        return ctx.touchStart($event);\n      });\n    }\n  },\n  inputs: {\n    rgX: \"rgX\",\n    rgY: \"rgY\",\n    slider: \"slider\"\n  },\n  outputs: {\n    dragEnd: \"dragEnd\",\n    dragStart: \"dragStart\",\n    newValue: \"newValue\"\n  },\n  standalone: false\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SliderDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[slider]'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    rgX: [{\n      type: Input\n    }],\n    rgY: [{\n      type: Input\n    }],\n    slider: [{\n      type: Input\n    }],\n    dragEnd: [{\n      type: Output\n    }],\n    dragStart: [{\n      type: Output\n    }],\n    newValue: [{\n      type: Output\n    }],\n    mouseDown: [{\n      type: HostListener,\n      args: ['mousedown', ['$event']]\n    }],\n    touchStart: [{\n      type: HostListener,\n      args: ['touchstart', ['$event']]\n    }]\n  });\n})();\nclass SliderPosition {\n  constructor(h, s, v, a) {\n    _defineProperty(this, \"h\", void 0);\n    _defineProperty(this, \"s\", void 0);\n    _defineProperty(this, \"v\", void 0);\n    _defineProperty(this, \"a\", void 0);\n    this.h = h;\n    this.s = s;\n    this.v = v;\n    this.a = a;\n  }\n}\nclass SliderDimension {\n  constructor(h, s, v, a) {\n    _defineProperty(this, \"h\", void 0);\n    _defineProperty(this, \"s\", void 0);\n    _defineProperty(this, \"v\", void 0);\n    _defineProperty(this, \"a\", void 0);\n    this.h = h;\n    this.s = s;\n    this.v = v;\n    this.a = a;\n  }\n}\nclass ColorPickerService {\n  constructor() {\n    _defineProperty(this, \"active\", null);\n  }\n  setActive(active) {\n    if (this.active && this.active !== active && this.active.cpDialogDisplay !== 'inline') {\n      this.active.closeDialog();\n    }\n    this.active = active;\n  }\n  hsva2hsla(hsva) {\n    const h = hsva.h,\n      s = hsva.s,\n      v = hsva.v,\n      a = hsva.a;\n    if (v === 0) {\n      return new Hsla(h, 0, 0, a);\n    } else if (s === 0 && v === 1) {\n      return new Hsla(h, 1, 1, a);\n    } else {\n      const l = v * (2 - s) / 2;\n      return new Hsla(h, v * s / (1 - Math.abs(2 * l - 1)), l, a);\n    }\n  }\n  hsla2hsva(hsla) {\n    const h = Math.min(hsla.h, 1),\n      s = Math.min(hsla.s, 1);\n    const l = Math.min(hsla.l, 1),\n      a = Math.min(hsla.a, 1);\n    if (l === 0) {\n      return new Hsva(h, 0, 0, a);\n    } else {\n      const v = l + s * (1 - Math.abs(2 * l - 1)) / 2;\n      return new Hsva(h, 2 * (v - l) / v, v, a);\n    }\n  }\n  hsvaToRgba(hsva) {\n    let r, g, b;\n    const h = hsva.h,\n      s = hsva.s,\n      v = hsva.v,\n      a = hsva.a;\n    const i = Math.floor(h * 6);\n    const f = h * 6 - i;\n    const p = v * (1 - s);\n    const q = v * (1 - f * s);\n    const t = v * (1 - (1 - f) * s);\n    switch (i % 6) {\n      case 0:\n        r = v, g = t, b = p;\n        break;\n      case 1:\n        r = q, g = v, b = p;\n        break;\n      case 2:\n        r = p, g = v, b = t;\n        break;\n      case 3:\n        r = p, g = q, b = v;\n        break;\n      case 4:\n        r = t, g = p, b = v;\n        break;\n      case 5:\n        r = v, g = p, b = q;\n        break;\n      default:\n        r = 0, g = 0, b = 0;\n    }\n    return new Rgba(r, g, b, a);\n  }\n  cmykToRgb(cmyk) {\n    const r = (1 - cmyk.c) * (1 - cmyk.k);\n    const g = (1 - cmyk.m) * (1 - cmyk.k);\n    const b = (1 - cmyk.y) * (1 - cmyk.k);\n    return new Rgba(r, g, b, cmyk.a);\n  }\n  rgbaToCmyk(rgba) {\n    const k = 1 - Math.max(rgba.r, rgba.g, rgba.b);\n    if (k === 1) {\n      return new Cmyk(0, 0, 0, 1, rgba.a);\n    } else {\n      const c = (1 - rgba.r - k) / (1 - k);\n      const m = (1 - rgba.g - k) / (1 - k);\n      const y = (1 - rgba.b - k) / (1 - k);\n      return new Cmyk(c, m, y, k, rgba.a);\n    }\n  }\n  rgbaToHsva(rgba) {\n    let h, s;\n    const r = Math.min(rgba.r, 1),\n      g = Math.min(rgba.g, 1);\n    const b = Math.min(rgba.b, 1),\n      a = Math.min(rgba.a, 1);\n    const max = Math.max(r, g, b),\n      min = Math.min(r, g, b);\n    const v = max,\n      d = max - min;\n    s = max === 0 ? 0 : d / max;\n    if (max === min) {\n      h = 0;\n    } else {\n      switch (max) {\n        case r:\n          h = (g - b) / d + (g < b ? 6 : 0);\n          break;\n        case g:\n          h = (b - r) / d + 2;\n          break;\n        case b:\n          h = (r - g) / d + 4;\n          break;\n        default:\n          h = 0;\n      }\n      h /= 6;\n    }\n    return new Hsva(h, s, v, a);\n  }\n  rgbaToHex(rgba, allowHex8) {\n    /* eslint-disable no-bitwise */\n    let hex = '#' + (1 << 24 | rgba.r << 16 | rgba.g << 8 | rgba.b).toString(16).substr(1);\n    if (allowHex8) {\n      hex += (1 << 8 | Math.round(rgba.a * 255)).toString(16).substr(1);\n    }\n    /* eslint-enable no-bitwise */\n    return hex;\n  }\n  normalizeCMYK(cmyk) {\n    return new Cmyk(cmyk.c / 100, cmyk.m / 100, cmyk.y / 100, cmyk.k / 100, cmyk.a);\n  }\n  denormalizeCMYK(cmyk) {\n    return new Cmyk(Math.floor(cmyk.c * 100), Math.floor(cmyk.m * 100), Math.floor(cmyk.y * 100), Math.floor(cmyk.k * 100), cmyk.a);\n  }\n  denormalizeRGBA(rgba) {\n    return new Rgba(Math.round(rgba.r * 255), Math.round(rgba.g * 255), Math.round(rgba.b * 255), rgba.a);\n  }\n  stringToHsva(colorString = '', allowHex8 = false) {\n    let hsva = null;\n    colorString = (colorString || '').toLowerCase();\n    const stringParsers = [{\n      re: /(rgb)a?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*%?,\\s*(\\d{1,3})\\s*%?(?:,\\s*(\\d+(?:\\.\\d+)?)\\s*)?\\)/,\n      parse: function (execResult) {\n        return new Rgba(parseInt(execResult[2], 10) / 255, parseInt(execResult[3], 10) / 255, parseInt(execResult[4], 10) / 255, isNaN(parseFloat(execResult[5])) ? 1 : parseFloat(execResult[5]));\n      }\n    }, {\n      re: /(hsl)a?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})%\\s*,\\s*(\\d{1,3})%\\s*(?:,\\s*(\\d+(?:\\.\\d+)?)\\s*)?\\)/,\n      parse: function (execResult) {\n        return new Hsla(parseInt(execResult[2], 10) / 360, parseInt(execResult[3], 10) / 100, parseInt(execResult[4], 10) / 100, isNaN(parseFloat(execResult[5])) ? 1 : parseFloat(execResult[5]));\n      }\n    }];\n    if (allowHex8) {\n      stringParsers.push({\n        re: /#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})?$/,\n        parse: function (execResult) {\n          return new Rgba(parseInt(execResult[1], 16) / 255, parseInt(execResult[2], 16) / 255, parseInt(execResult[3], 16) / 255, parseInt(execResult[4] || 'FF', 16) / 255);\n        }\n      });\n    } else {\n      stringParsers.push({\n        re: /#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})$/,\n        parse: function (execResult) {\n          return new Rgba(parseInt(execResult[1], 16) / 255, parseInt(execResult[2], 16) / 255, parseInt(execResult[3], 16) / 255, 1);\n        }\n      });\n    }\n    stringParsers.push({\n      re: /#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])$/,\n      parse: function (execResult) {\n        return new Rgba(parseInt(execResult[1] + execResult[1], 16) / 255, parseInt(execResult[2] + execResult[2], 16) / 255, parseInt(execResult[3] + execResult[3], 16) / 255, 1);\n      }\n    });\n    for (const key in stringParsers) {\n      if (stringParsers.hasOwnProperty(key)) {\n        const parser = stringParsers[key];\n        const match = parser.re.exec(colorString),\n          color = match && parser.parse(match);\n        if (color) {\n          if (color instanceof Rgba) {\n            hsva = this.rgbaToHsva(color);\n          } else if (color instanceof Hsla) {\n            hsva = this.hsla2hsva(color);\n          }\n          return hsva;\n        }\n      }\n    }\n    return hsva;\n  }\n  outputFormat(hsva, outputFormat, alphaChannel) {\n    if (outputFormat === 'auto') {\n      outputFormat = hsva.a < 1 ? 'rgba' : 'hex';\n    }\n    switch (outputFormat) {\n      case 'hsla':\n        const hsla = this.hsva2hsla(hsva);\n        const hslaText = new Hsla(Math.round(hsla.h * 360), Math.round(hsla.s * 100), Math.round(hsla.l * 100), Math.round(hsla.a * 100) / 100);\n        if (hsva.a < 1 || alphaChannel === 'always') {\n          return 'hsla(' + hslaText.h + ',' + hslaText.s + '%,' + hslaText.l + '%,' + hslaText.a + ')';\n        } else {\n          return 'hsl(' + hslaText.h + ',' + hslaText.s + '%,' + hslaText.l + '%)';\n        }\n      case 'rgba':\n        const rgba = this.denormalizeRGBA(this.hsvaToRgba(hsva));\n        if (hsva.a < 1 || alphaChannel === 'always') {\n          return 'rgba(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ',' + Math.round(rgba.a * 100) / 100 + ')';\n        } else {\n          return 'rgb(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ')';\n        }\n      default:\n        const allowHex8 = alphaChannel === 'always' || alphaChannel === 'forced';\n        return this.rgbaToHex(this.denormalizeRGBA(this.hsvaToRgba(hsva)), allowHex8);\n    }\n  }\n}\n_ColorPickerService = ColorPickerService;\n_defineProperty(ColorPickerService, \"\\u0275fac\", function _ColorPickerService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ColorPickerService)();\n});\n_defineProperty(ColorPickerService, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _ColorPickerService,\n  factory: _ColorPickerService.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColorPickerService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n// Do not store that on the class instance since the condition will be run\n// every time the class is created.\nconst SUPPORTS_TOUCH = typeof window !== 'undefined' && 'ontouchstart' in window;\nclass ColorPickerComponent {\n  handleEsc(event) {\n    if (this.show && this.cpDialogDisplay === 'popup') {\n      this.onCancelColor(event);\n    }\n  }\n  handleEnter(event) {\n    if (this.show && this.cpDialogDisplay === 'popup') {\n      this.onAcceptColor(event);\n    }\n  }\n  constructor(ngZone, elRef, cdRef, document, platformId, service) {\n    _defineProperty(this, \"ngZone\", void 0);\n    _defineProperty(this, \"elRef\", void 0);\n    _defineProperty(this, \"cdRef\", void 0);\n    _defineProperty(this, \"document\", void 0);\n    _defineProperty(this, \"platformId\", void 0);\n    _defineProperty(this, \"service\", void 0);\n    _defineProperty(this, \"isIE10\", false);\n    _defineProperty(this, \"cmyk\", void 0);\n    _defineProperty(this, \"hsva\", void 0);\n    _defineProperty(this, \"width\", void 0);\n    _defineProperty(this, \"height\", void 0);\n    _defineProperty(this, \"cmykColor\", void 0);\n    _defineProperty(this, \"outputColor\", void 0);\n    _defineProperty(this, \"initialColor\", void 0);\n    _defineProperty(this, \"fallbackColor\", void 0);\n    _defineProperty(this, \"listenerResize\", void 0);\n    _defineProperty(this, \"listenerMouseDown\", void 0);\n    _defineProperty(this, \"directiveInstance\", void 0);\n    _defineProperty(this, \"sliderH\", void 0);\n    _defineProperty(this, \"sliderDimMax\", void 0);\n    _defineProperty(this, \"directiveElementRef\", void 0);\n    _defineProperty(this, \"dialogArrowSize\", 10);\n    _defineProperty(this, \"dialogArrowOffset\", 15);\n    _defineProperty(this, \"dialogInputFields\", [ColorFormats.HEX, ColorFormats.RGBA, ColorFormats.HSLA, ColorFormats.CMYK]);\n    _defineProperty(this, \"useRootViewContainer\", false);\n    _defineProperty(this, \"show\", void 0);\n    _defineProperty(this, \"hidden\", void 0);\n    _defineProperty(this, \"top\", void 0);\n    _defineProperty(this, \"left\", void 0);\n    _defineProperty(this, \"position\", void 0);\n    _defineProperty(this, \"format\", void 0);\n    _defineProperty(this, \"slider\", void 0);\n    _defineProperty(this, \"hexText\", void 0);\n    _defineProperty(this, \"hexAlpha\", void 0);\n    _defineProperty(this, \"cmykText\", void 0);\n    _defineProperty(this, \"hslaText\", void 0);\n    _defineProperty(this, \"rgbaText\", void 0);\n    _defineProperty(this, \"arrowTop\", void 0);\n    _defineProperty(this, \"selectedColor\", void 0);\n    _defineProperty(this, \"hueSliderColor\", void 0);\n    _defineProperty(this, \"alphaSliderColor\", void 0);\n    _defineProperty(this, \"cpWidth\", void 0);\n    _defineProperty(this, \"cpHeight\", void 0);\n    _defineProperty(this, \"cpColorMode\", void 0);\n    _defineProperty(this, \"cpCmykEnabled\", void 0);\n    _defineProperty(this, \"cpAlphaChannel\", void 0);\n    _defineProperty(this, \"cpOutputFormat\", void 0);\n    _defineProperty(this, \"cpDisableInput\", void 0);\n    _defineProperty(this, \"cpDialogDisplay\", void 0);\n    _defineProperty(this, \"cpIgnoredElements\", void 0);\n    _defineProperty(this, \"cpSaveClickOutside\", void 0);\n    _defineProperty(this, \"cpCloseClickOutside\", void 0);\n    _defineProperty(this, \"cpPosition\", void 0);\n    _defineProperty(this, \"cpUsePosition\", void 0);\n    _defineProperty(this, \"cpPositionOffset\", void 0);\n    _defineProperty(this, \"cpOKButton\", void 0);\n    _defineProperty(this, \"cpOKButtonText\", void 0);\n    _defineProperty(this, \"cpOKButtonClass\", void 0);\n    _defineProperty(this, \"cpCancelButton\", void 0);\n    _defineProperty(this, \"cpCancelButtonText\", void 0);\n    _defineProperty(this, \"cpCancelButtonClass\", void 0);\n    _defineProperty(this, \"cpEyeDropper\", void 0);\n    _defineProperty(this, \"eyeDropperSupported\", void 0);\n    _defineProperty(this, \"cpPresetLabel\", void 0);\n    _defineProperty(this, \"cpPresetColors\", void 0);\n    _defineProperty(this, \"cpPresetColorsClass\", void 0);\n    _defineProperty(this, \"cpMaxPresetColorsLength\", void 0);\n    _defineProperty(this, \"cpPresetEmptyMessage\", void 0);\n    _defineProperty(this, \"cpPresetEmptyMessageClass\", void 0);\n    _defineProperty(this, \"cpAddColorButton\", void 0);\n    _defineProperty(this, \"cpAddColorButtonText\", void 0);\n    _defineProperty(this, \"cpAddColorButtonClass\", void 0);\n    _defineProperty(this, \"cpRemoveColorButtonClass\", void 0);\n    _defineProperty(this, \"cpArrowPosition\", void 0);\n    _defineProperty(this, \"cpTriggerElement\", void 0);\n    _defineProperty(this, \"cpExtraTemplate\", void 0);\n    _defineProperty(this, \"dialogElement\", void 0);\n    _defineProperty(this, \"hueSlider\", void 0);\n    _defineProperty(this, \"alphaSlider\", void 0);\n    this.ngZone = ngZone;\n    this.elRef = elRef;\n    this.cdRef = cdRef;\n    this.document = document;\n    this.platformId = platformId;\n    this.service = service;\n    this.eyeDropperSupported = isPlatformBrowser(this.platformId) && 'EyeDropper' in this.document.defaultView;\n  }\n  ngOnInit() {\n    this.slider = new SliderPosition(0, 0, 0, 0);\n    const hueWidth = this.hueSlider.nativeElement.offsetWidth || 140;\n    const alphaWidth = this.alphaSlider.nativeElement.offsetWidth || 140;\n    this.sliderDimMax = new SliderDimension(hueWidth, this.cpWidth, 130, alphaWidth);\n    if (this.cpCmykEnabled) {\n      this.format = ColorFormats.CMYK;\n    } else if (this.cpOutputFormat === 'rgba') {\n      this.format = ColorFormats.RGBA;\n    } else if (this.cpOutputFormat === 'hsla') {\n      this.format = ColorFormats.HSLA;\n    } else {\n      this.format = ColorFormats.HEX;\n    }\n    this.listenerMouseDown = event => {\n      this.onMouseDown(event);\n    };\n    this.listenerResize = () => {\n      this.onResize();\n    };\n    this.openDialog(this.initialColor, false);\n  }\n  ngOnDestroy() {\n    this.closeDialog();\n  }\n  ngAfterViewInit() {\n    if (this.cpWidth !== 230 || this.cpDialogDisplay === 'inline') {\n      const hueWidth = this.hueSlider.nativeElement.offsetWidth || 140;\n      const alphaWidth = this.alphaSlider.nativeElement.offsetWidth || 140;\n      this.sliderDimMax = new SliderDimension(hueWidth, this.cpWidth, 130, alphaWidth);\n      this.updateColorPicker(false);\n      this.cdRef.detectChanges();\n    }\n  }\n  openDialog(color, emit = true) {\n    this.service.setActive(this);\n    if (!this.width) {\n      this.cpWidth = this.directiveElementRef.nativeElement.offsetWidth;\n    }\n    if (!this.height) {\n      this.height = 320;\n    }\n    this.setInitialColor(color);\n    this.setColorFromString(color, emit);\n    this.openColorPicker();\n  }\n  closeDialog() {\n    this.closeColorPicker();\n  }\n  setupDialog(instance, elementRef, color, cpWidth, cpHeight, cpDialogDisplay, cpFallbackColor, cpColorMode, cpCmykEnabled, cpAlphaChannel, cpOutputFormat, cpDisableInput, cpIgnoredElements, cpSaveClickOutside, cpCloseClickOutside, cpUseRootViewContainer, cpPosition, cpPositionOffset, cpPositionRelativeToArrow, cpPresetLabel, cpPresetColors, cpPresetColorsClass, cpMaxPresetColorsLength, cpPresetEmptyMessage, cpPresetEmptyMessageClass, cpOKButton, cpOKButtonClass, cpOKButtonText, cpCancelButton, cpCancelButtonClass, cpCancelButtonText, cpAddColorButton, cpAddColorButtonClass, cpAddColorButtonText, cpRemoveColorButtonClass, cpEyeDropper, cpTriggerElement, cpExtraTemplate) {\n    this.setInitialColor(color);\n    this.setColorMode(cpColorMode);\n    this.isIE10 = detectIE() === 10;\n    this.directiveInstance = instance;\n    this.directiveElementRef = elementRef;\n    this.cpDisableInput = cpDisableInput;\n    this.cpCmykEnabled = cpCmykEnabled;\n    this.cpAlphaChannel = cpAlphaChannel;\n    this.cpOutputFormat = cpOutputFormat;\n    this.cpDialogDisplay = cpDialogDisplay;\n    this.cpIgnoredElements = cpIgnoredElements;\n    this.cpSaveClickOutside = cpSaveClickOutside;\n    this.cpCloseClickOutside = cpCloseClickOutside;\n    this.useRootViewContainer = cpUseRootViewContainer;\n    this.width = this.cpWidth = parseInt(cpWidth, 10);\n    this.height = this.cpHeight = parseInt(cpHeight, 10);\n    this.cpPosition = cpPosition;\n    this.cpPositionOffset = parseInt(cpPositionOffset, 10);\n    this.cpOKButton = cpOKButton;\n    this.cpOKButtonText = cpOKButtonText;\n    this.cpOKButtonClass = cpOKButtonClass;\n    this.cpCancelButton = cpCancelButton;\n    this.cpCancelButtonText = cpCancelButtonText;\n    this.cpCancelButtonClass = cpCancelButtonClass;\n    this.cpEyeDropper = cpEyeDropper;\n    this.fallbackColor = cpFallbackColor || '#fff';\n    this.setPresetConfig(cpPresetLabel, cpPresetColors);\n    this.cpPresetColorsClass = cpPresetColorsClass;\n    this.cpMaxPresetColorsLength = cpMaxPresetColorsLength;\n    this.cpPresetEmptyMessage = cpPresetEmptyMessage;\n    this.cpPresetEmptyMessageClass = cpPresetEmptyMessageClass;\n    this.cpAddColorButton = cpAddColorButton;\n    this.cpAddColorButtonText = cpAddColorButtonText;\n    this.cpAddColorButtonClass = cpAddColorButtonClass;\n    this.cpRemoveColorButtonClass = cpRemoveColorButtonClass;\n    this.cpTriggerElement = cpTriggerElement;\n    this.cpExtraTemplate = cpExtraTemplate;\n    if (!cpPositionRelativeToArrow) {\n      this.dialogArrowOffset = 0;\n    }\n    if (cpDialogDisplay === 'inline') {\n      this.dialogArrowSize = 0;\n      this.dialogArrowOffset = 0;\n    }\n    if (cpOutputFormat === 'hex' && cpAlphaChannel !== 'always' && cpAlphaChannel !== 'forced') {\n      this.cpAlphaChannel = 'disabled';\n    }\n  }\n  setColorMode(mode) {\n    switch (mode.toString().toUpperCase()) {\n      case '1':\n      case 'C':\n      case 'COLOR':\n        this.cpColorMode = 1;\n        break;\n      case '2':\n      case 'G':\n      case 'GRAYSCALE':\n        this.cpColorMode = 2;\n        break;\n      case '3':\n      case 'P':\n      case 'PRESETS':\n        this.cpColorMode = 3;\n        break;\n      default:\n        this.cpColorMode = 1;\n    }\n  }\n  setInitialColor(color) {\n    this.initialColor = color;\n  }\n  setPresetConfig(cpPresetLabel, cpPresetColors) {\n    this.cpPresetLabel = cpPresetLabel;\n    this.cpPresetColors = cpPresetColors;\n  }\n  setColorFromString(value, emit = true, update = true) {\n    let hsva;\n    if (this.cpAlphaChannel === 'always' || this.cpAlphaChannel === 'forced') {\n      hsva = this.service.stringToHsva(value, true);\n      if (!hsva && !this.hsva) {\n        hsva = this.service.stringToHsva(value, false);\n      }\n    } else {\n      hsva = this.service.stringToHsva(value, false);\n    }\n    if (!hsva && !this.hsva) {\n      hsva = this.service.stringToHsva(this.fallbackColor, false);\n    }\n    if (hsva) {\n      this.hsva = hsva;\n      this.sliderH = this.hsva.h;\n      if (this.cpOutputFormat === 'hex' && this.cpAlphaChannel === 'disabled') {\n        this.hsva.a = 1;\n      }\n      this.updateColorPicker(emit, update);\n    }\n  }\n  onResize() {\n    if (this.position === 'fixed') {\n      this.setDialogPosition();\n    } else if (this.cpDialogDisplay !== 'inline') {\n      this.closeColorPicker();\n    }\n  }\n  onDragEnd(slider) {\n    this.directiveInstance.sliderDragEnd({\n      slider: slider,\n      color: this.outputColor\n    });\n  }\n  onDragStart(slider) {\n    this.directiveInstance.sliderDragStart({\n      slider: slider,\n      color: this.outputColor\n    });\n  }\n  onMouseDown(event) {\n    if (this.show && !this.isIE10 && this.cpDialogDisplay === 'popup' && event.target !== this.directiveElementRef.nativeElement && !this.isDescendant(this.elRef.nativeElement, event.target) && !this.isDescendant(this.directiveElementRef.nativeElement, event.target) && this.cpIgnoredElements.filter(item => item === event.target).length === 0) {\n      this.ngZone.run(() => {\n        if (this.cpSaveClickOutside) {\n          this.directiveInstance.colorSelected(this.outputColor);\n        } else {\n          this.hsva = null;\n          this.setColorFromString(this.initialColor, false);\n          if (this.cpCmykEnabled) {\n            this.directiveInstance.cmykChanged(this.cmykColor);\n          }\n          this.directiveInstance.colorChanged(this.initialColor);\n          this.directiveInstance.colorCanceled();\n        }\n        if (this.cpCloseClickOutside) {\n          this.closeColorPicker();\n        }\n      });\n    }\n  }\n  onAcceptColor(event) {\n    event.stopPropagation();\n    if (this.outputColor) {\n      this.directiveInstance.colorSelected(this.outputColor);\n    }\n    if (this.cpDialogDisplay === 'popup') {\n      this.closeColorPicker();\n    }\n  }\n  onCancelColor(event) {\n    this.hsva = null;\n    event.stopPropagation();\n    this.directiveInstance.colorCanceled();\n    this.setColorFromString(this.initialColor, true);\n    if (this.cpDialogDisplay === 'popup') {\n      if (this.cpCmykEnabled) {\n        this.directiveInstance.cmykChanged(this.cmykColor);\n      }\n      this.directiveInstance.colorChanged(this.initialColor, true);\n      this.closeColorPicker();\n    }\n  }\n  onEyeDropper() {\n    if (!this.eyeDropperSupported) return;\n    const eyeDropper = new window.EyeDropper();\n    eyeDropper.open().then(eyeDropperResult => {\n      this.setColorFromString(eyeDropperResult.sRGBHex, true);\n    });\n  }\n  onFormatToggle(change) {\n    const availableFormats = this.dialogInputFields.length - (this.cpCmykEnabled ? 0 : 1);\n    const nextFormat = ((this.dialogInputFields.indexOf(this.format) + change) % availableFormats + availableFormats) % availableFormats;\n    this.format = this.dialogInputFields[nextFormat];\n  }\n  onColorChange(value) {\n    this.hsva.s = value.s / value.rgX;\n    this.hsva.v = value.v / value.rgY;\n    this.updateColorPicker();\n    this.directiveInstance.sliderChanged({\n      slider: 'lightness',\n      value: this.hsva.v,\n      color: this.outputColor\n    });\n    this.directiveInstance.sliderChanged({\n      slider: 'saturation',\n      value: this.hsva.s,\n      color: this.outputColor\n    });\n  }\n  onHueChange(value) {\n    this.hsva.h = value.v / value.rgX;\n    this.sliderH = this.hsva.h;\n    this.updateColorPicker();\n    this.directiveInstance.sliderChanged({\n      slider: 'hue',\n      value: this.hsva.h,\n      color: this.outputColor\n    });\n  }\n  onValueChange(value) {\n    this.hsva.v = value.v / value.rgX;\n    this.updateColorPicker();\n    this.directiveInstance.sliderChanged({\n      slider: 'value',\n      value: this.hsva.v,\n      color: this.outputColor\n    });\n  }\n  onAlphaChange(value) {\n    this.hsva.a = value.v / value.rgX;\n    this.updateColorPicker();\n    this.directiveInstance.sliderChanged({\n      slider: 'alpha',\n      value: this.hsva.a,\n      color: this.outputColor\n    });\n  }\n  onHexInput(value) {\n    if (value === null) {\n      this.updateColorPicker();\n    } else {\n      if (value && value[0] !== '#') {\n        value = '#' + value;\n      }\n      let validHex = /^#([a-f0-9]{3}|[a-f0-9]{6})$/gi;\n      if (this.cpAlphaChannel === 'always') {\n        validHex = /^#([a-f0-9]{3}|[a-f0-9]{6}|[a-f0-9]{8})$/gi;\n      }\n      const valid = validHex.test(value);\n      if (valid) {\n        if (value.length < 5) {\n          value = '#' + value.substring(1).split('').map(c => c + c).join('');\n        }\n        if (this.cpAlphaChannel === 'forced') {\n          value += Math.round(this.hsva.a * 255).toString(16);\n        }\n        this.setColorFromString(value, true, false);\n      }\n      this.directiveInstance.inputChanged({\n        input: 'hex',\n        valid: valid,\n        value: value,\n        color: this.outputColor\n      });\n    }\n  }\n  onRedInput(value) {\n    const rgba = this.service.hsvaToRgba(this.hsva);\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      rgba.r = value.v / value.rg;\n      this.hsva = this.service.rgbaToHsva(rgba);\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'red',\n      valid: valid,\n      value: rgba.r,\n      color: this.outputColor\n    });\n  }\n  onBlueInput(value) {\n    const rgba = this.service.hsvaToRgba(this.hsva);\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      rgba.b = value.v / value.rg;\n      this.hsva = this.service.rgbaToHsva(rgba);\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'blue',\n      valid: valid,\n      value: rgba.b,\n      color: this.outputColor\n    });\n  }\n  onGreenInput(value) {\n    const rgba = this.service.hsvaToRgba(this.hsva);\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      rgba.g = value.v / value.rg;\n      this.hsva = this.service.rgbaToHsva(rgba);\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'green',\n      valid: valid,\n      value: rgba.g,\n      color: this.outputColor\n    });\n  }\n  onHueInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.hsva.h = value.v / value.rg;\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'hue',\n      valid: valid,\n      value: this.hsva.h,\n      color: this.outputColor\n    });\n  }\n  onValueInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.hsva.v = value.v / value.rg;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'value',\n      valid: valid,\n      value: this.hsva.v,\n      color: this.outputColor\n    });\n  }\n  onAlphaInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.hsva.a = value.v / value.rg;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'alpha',\n      valid: valid,\n      value: this.hsva.a,\n      color: this.outputColor\n    });\n  }\n  onLightnessInput(value) {\n    const hsla = this.service.hsva2hsla(this.hsva);\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      hsla.l = value.v / value.rg;\n      this.hsva = this.service.hsla2hsva(hsla);\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'lightness',\n      valid: valid,\n      value: hsla.l,\n      color: this.outputColor\n    });\n  }\n  onSaturationInput(value) {\n    const hsla = this.service.hsva2hsla(this.hsva);\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      hsla.s = value.v / value.rg;\n      this.hsva = this.service.hsla2hsva(hsla);\n      this.sliderH = this.hsva.h;\n      this.updateColorPicker();\n    }\n    this.directiveInstance.inputChanged({\n      input: 'saturation',\n      valid: valid,\n      value: hsla.s,\n      color: this.outputColor\n    });\n  }\n  onCyanInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.cmyk.c = value.v;\n      this.updateColorPicker(false, true, true);\n    }\n    this.directiveInstance.inputChanged({\n      input: 'cyan',\n      valid: true,\n      value: this.cmyk.c,\n      color: this.outputColor\n    });\n  }\n  onMagentaInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.cmyk.m = value.v;\n      this.updateColorPicker(false, true, true);\n    }\n    this.directiveInstance.inputChanged({\n      input: 'magenta',\n      valid: true,\n      value: this.cmyk.m,\n      color: this.outputColor\n    });\n  }\n  onYellowInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.cmyk.y = value.v;\n      this.updateColorPicker(false, true, true);\n    }\n    this.directiveInstance.inputChanged({\n      input: 'yellow',\n      valid: true,\n      value: this.cmyk.y,\n      color: this.outputColor\n    });\n  }\n  onBlackInput(value) {\n    const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n    if (valid) {\n      this.cmyk.k = value.v;\n      this.updateColorPicker(false, true, true);\n    }\n    this.directiveInstance.inputChanged({\n      input: 'black',\n      valid: true,\n      value: this.cmyk.k,\n      color: this.outputColor\n    });\n  }\n  onAddPresetColor(event, value) {\n    event.stopPropagation();\n    if (!this.cpPresetColors.filter(color => color === value).length) {\n      this.cpPresetColors = this.cpPresetColors.concat(value);\n      this.directiveInstance.presetColorsChanged(this.cpPresetColors);\n    }\n  }\n  onRemovePresetColor(event, value) {\n    event.stopPropagation();\n    this.cpPresetColors = this.cpPresetColors.filter(color => color !== value);\n    this.directiveInstance.presetColorsChanged(this.cpPresetColors);\n  }\n  // Private helper functions for the color picker dialog status\n  openColorPicker() {\n    if (!this.show) {\n      this.show = true;\n      this.hidden = true;\n      setTimeout(() => {\n        this.hidden = false;\n        this.setDialogPosition();\n        this.cdRef.detectChanges();\n      }, 0);\n      this.directiveInstance.stateChanged(true);\n      if (!this.isIE10) {\n        // The change detection should be run on `mousedown` event only when the condition\n        // is met within the `onMouseDown` method.\n        this.ngZone.runOutsideAngular(() => {\n          // There's no sense to add both event listeners on touch devices since the `touchstart`\n          // event is handled earlier than `mousedown`, so we'll get 2 change detections and the\n          // second one will be unnecessary.\n          if (SUPPORTS_TOUCH) {\n            document.addEventListener('touchstart', this.listenerMouseDown);\n          } else {\n            document.addEventListener('mousedown', this.listenerMouseDown);\n          }\n        });\n      }\n      window.addEventListener('resize', this.listenerResize);\n    }\n  }\n  closeColorPicker() {\n    if (this.show) {\n      this.show = false;\n      this.directiveInstance.stateChanged(false);\n      if (!this.isIE10) {\n        if (SUPPORTS_TOUCH) {\n          document.removeEventListener('touchstart', this.listenerMouseDown);\n        } else {\n          document.removeEventListener('mousedown', this.listenerMouseDown);\n        }\n      }\n      window.removeEventListener('resize', this.listenerResize);\n      if (!this.cdRef['destroyed']) {\n        this.cdRef.detectChanges();\n      }\n    }\n  }\n  updateColorPicker(emit = true, update = true, cmykInput = false) {\n    if (this.sliderDimMax) {\n      if (this.cpColorMode === 2) {\n        this.hsva.s = 0;\n      }\n      let hue, hsla, rgba;\n      const lastOutput = this.outputColor;\n      hsla = this.service.hsva2hsla(this.hsva);\n      if (!this.cpCmykEnabled) {\n        rgba = this.service.denormalizeRGBA(this.service.hsvaToRgba(this.hsva));\n      } else {\n        if (!cmykInput) {\n          rgba = this.service.hsvaToRgba(this.hsva);\n          this.cmyk = this.service.denormalizeCMYK(this.service.rgbaToCmyk(rgba));\n        } else {\n          rgba = this.service.cmykToRgb(this.service.normalizeCMYK(this.cmyk));\n          this.hsva = this.service.rgbaToHsva(rgba);\n        }\n        rgba = this.service.denormalizeRGBA(rgba);\n        this.sliderH = this.hsva.h;\n      }\n      hue = this.service.denormalizeRGBA(this.service.hsvaToRgba(new Hsva(this.sliderH || this.hsva.h, 1, 1, 1)));\n      if (update) {\n        this.hslaText = new Hsla(Math.round(hsla.h * 360), Math.round(hsla.s * 100), Math.round(hsla.l * 100), Math.round(hsla.a * 100) / 100);\n        this.rgbaText = new Rgba(rgba.r, rgba.g, rgba.b, Math.round(rgba.a * 100) / 100);\n        if (this.cpCmykEnabled) {\n          this.cmykText = new Cmyk(this.cmyk.c, this.cmyk.m, this.cmyk.y, this.cmyk.k, Math.round(this.cmyk.a * 100) / 100);\n        }\n        const allowHex8 = this.cpAlphaChannel === 'always';\n        this.hexText = this.service.rgbaToHex(rgba, allowHex8);\n        this.hexAlpha = this.rgbaText.a;\n      }\n      if (this.cpOutputFormat === 'auto') {\n        if (this.format !== ColorFormats.RGBA && this.format !== ColorFormats.CMYK && this.format !== ColorFormats.HSLA) {\n          if (this.hsva.a < 1) {\n            this.format = this.hsva.a < 1 ? ColorFormats.RGBA : ColorFormats.HEX;\n          }\n        }\n      }\n      this.hueSliderColor = 'rgb(' + hue.r + ',' + hue.g + ',' + hue.b + ')';\n      this.alphaSliderColor = 'rgb(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ')';\n      this.outputColor = this.service.outputFormat(this.hsva, this.cpOutputFormat, this.cpAlphaChannel);\n      this.selectedColor = this.service.outputFormat(this.hsva, 'rgba', null);\n      if (this.format !== ColorFormats.CMYK) {\n        this.cmykColor = '';\n      } else {\n        if (this.cpAlphaChannel === 'always' || this.cpAlphaChannel === 'enabled' || this.cpAlphaChannel === 'forced') {\n          const alpha = Math.round(this.cmyk.a * 100) / 100;\n          this.cmykColor = `cmyka(${this.cmyk.c},${this.cmyk.m},${this.cmyk.y},${this.cmyk.k},${alpha})`;\n        } else {\n          this.cmykColor = `cmyk(${this.cmyk.c},${this.cmyk.m},${this.cmyk.y},${this.cmyk.k})`;\n        }\n      }\n      this.slider = new SliderPosition((this.sliderH || this.hsva.h) * this.sliderDimMax.h - 8, this.hsva.s * this.sliderDimMax.s - 8, (1 - this.hsva.v) * this.sliderDimMax.v - 8, this.hsva.a * this.sliderDimMax.a - 8);\n      if (emit && lastOutput !== this.outputColor) {\n        if (this.cpCmykEnabled) {\n          this.directiveInstance.cmykChanged(this.cmykColor);\n        }\n        this.directiveInstance.colorChanged(this.outputColor);\n      }\n    }\n  }\n  // Private helper functions for the color picker dialog positioning\n  setDialogPosition() {\n    if (this.cpDialogDisplay === 'inline') {\n      this.position = 'relative';\n    } else {\n      let position = 'static',\n        transform = '',\n        style;\n      let parentNode = null,\n        transformNode = null;\n      let node = this.directiveElementRef.nativeElement.parentNode;\n      const dialogHeight = this.dialogElement.nativeElement.offsetHeight;\n      while (node !== null && node.tagName !== 'HTML') {\n        style = window.getComputedStyle(node);\n        position = style.getPropertyValue('position');\n        transform = style.getPropertyValue('transform');\n        if (position !== 'static' && parentNode === null) {\n          parentNode = node;\n        }\n        if (transform && transform !== 'none' && transformNode === null) {\n          transformNode = node;\n        }\n        if (position === 'fixed') {\n          parentNode = transformNode;\n          break;\n        }\n        node = node.parentNode;\n      }\n      const boxDirective = this.createDialogBox(this.directiveElementRef.nativeElement, position !== 'fixed');\n      if (this.useRootViewContainer || position === 'fixed' && (!parentNode || parentNode instanceof HTMLUnknownElement)) {\n        this.top = boxDirective.top;\n        this.left = boxDirective.left;\n      } else {\n        if (parentNode === null) {\n          parentNode = node;\n        }\n        const boxParent = this.createDialogBox(parentNode, position !== 'fixed');\n        this.top = boxDirective.top - boxParent.top;\n        this.left = boxDirective.left - boxParent.left;\n      }\n      if (position === 'fixed') {\n        this.position = 'fixed';\n      }\n      let usePosition = this.cpPosition;\n      const dialogBounds = this.dialogElement.nativeElement.getBoundingClientRect();\n      if (this.cpPosition === 'auto') {\n        const triggerBounds = this.cpTriggerElement.nativeElement.getBoundingClientRect();\n        usePosition = calculateAutoPositioning(dialogBounds, triggerBounds);\n      }\n      this.arrowTop = usePosition === 'top' ? dialogHeight - 1 : undefined;\n      this.cpArrowPosition = undefined;\n      switch (usePosition) {\n        case 'top':\n          this.top -= dialogHeight + this.dialogArrowSize;\n          this.left += this.cpPositionOffset / 100 * boxDirective.width - this.dialogArrowOffset;\n          break;\n        case 'bottom':\n          this.top += boxDirective.height + this.dialogArrowSize;\n          this.left += this.cpPositionOffset / 100 * boxDirective.width - this.dialogArrowOffset;\n          break;\n        case 'top-left':\n        case 'left-top':\n          this.top -= dialogHeight - boxDirective.height + boxDirective.height * this.cpPositionOffset / 100;\n          this.left -= this.cpWidth + this.dialogArrowSize - 2 - this.dialogArrowOffset;\n          break;\n        case 'top-right':\n        case 'right-top':\n          this.top -= dialogHeight - boxDirective.height + boxDirective.height * this.cpPositionOffset / 100;\n          this.left += boxDirective.width + this.dialogArrowSize - 2 - this.dialogArrowOffset;\n          break;\n        case 'left':\n        case 'bottom-left':\n        case 'left-bottom':\n          this.top += boxDirective.height * this.cpPositionOffset / 100 - this.dialogArrowOffset;\n          this.left -= this.cpWidth + this.dialogArrowSize - 2;\n          break;\n        case 'right':\n        case 'bottom-right':\n        case 'right-bottom':\n        default:\n          this.top += boxDirective.height * this.cpPositionOffset / 100 - this.dialogArrowOffset;\n          this.left += boxDirective.width + this.dialogArrowSize - 2;\n          break;\n      }\n      const windowInnerHeight = window.innerHeight;\n      const windowInnerWidth = window.innerWidth;\n      const elRefClientRect = this.elRef.nativeElement.getBoundingClientRect();\n      const bottom = this.top + dialogBounds.height;\n      if (bottom > windowInnerHeight) {\n        this.top = windowInnerHeight - dialogBounds.height;\n        this.cpArrowPosition = elRefClientRect.x / 2 - 20;\n      }\n      const right = this.left + dialogBounds.width;\n      if (right > windowInnerWidth) {\n        this.left = windowInnerWidth - dialogBounds.width;\n        this.cpArrowPosition = elRefClientRect.x / 2 - 20;\n      }\n      this.cpUsePosition = usePosition;\n    }\n  }\n  // Private helper functions for the color picker dialog positioning and opening\n  isDescendant(parent, child) {\n    let node = child.parentNode;\n    while (node !== null) {\n      if (node === parent) {\n        return true;\n      }\n      node = node.parentNode;\n    }\n    return false;\n  }\n  createDialogBox(element, offset) {\n    const {\n      top,\n      left\n    } = element.getBoundingClientRect();\n    return {\n      top: top + (offset ? window.pageYOffset : 0),\n      left: left + (offset ? window.pageXOffset : 0),\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n  }\n}\n_ColorPickerComponent = ColorPickerComponent;\n_defineProperty(ColorPickerComponent, \"\\u0275fac\", function _ColorPickerComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ColorPickerComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(ColorPickerService));\n});\n_defineProperty(ColorPickerComponent, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _ColorPickerComponent,\n  selectors: [[\"color-picker\"]],\n  viewQuery: function _ColorPickerComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n      i0.ɵɵviewQuery(_c1, 7);\n      i0.ɵɵviewQuery(_c2, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialogElement = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.hueSlider = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.alphaSlider = _t.first);\n    }\n  },\n  hostBindings: function _ColorPickerComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keyup.esc\", function _ColorPickerComponent_keyup_esc_HostBindingHandler($event) {\n        return ctx.handleEsc($event);\n      }, false, i0.ɵɵresolveDocument)(\"keyup.enter\", function _ColorPickerComponent_keyup_enter_HostBindingHandler($event) {\n        return ctx.handleEnter($event);\n      }, false, i0.ɵɵresolveDocument);\n    }\n  },\n  standalone: false,\n  decls: 30,\n  vars: 51,\n  consts: [[\"dialogPopup\", \"\"], [\"hueSlider\", \"\"], [\"valueSlider\", \"\"], [\"alphaSlider\", \"\"], [1, \"color-picker\", 3, \"click\"], [3, \"left\", \"class\", \"top\", 4, \"ngIf\"], [\"class\", \"saturation-lightness\", 3, \"slider\", \"rgX\", \"rgY\", \"background-color\", \"newValue\", \"dragStart\", \"dragEnd\", 4, \"ngIf\"], [1, \"hue-alpha\", \"box\"], [1, \"left\"], [1, \"selected-color-background\"], [1, \"selected-color\", 3, \"click\"], [\"class\", \"eyedropper-icon\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"height\", \"24px\", \"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"fill\", \"#000000\", 4, \"ngIf\"], [\"type\", \"button\", 3, \"class\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"right\"], [\"style\", \"height: 16px;\", 4, \"ngIf\"], [1, \"hue\", 3, \"newValue\", \"dragStart\", \"dragEnd\", \"slider\", \"rgX\"], [1, \"cursor\"], [1, \"value\", 3, \"newValue\", \"dragStart\", \"dragEnd\", \"slider\", \"rgX\"], [1, \"alpha\", 3, \"newValue\", \"dragStart\", \"dragEnd\", \"slider\", \"rgX\"], [\"class\", \"cmyk-text\", 3, \"display\", 4, \"ngIf\"], [\"class\", \"hsla-text\", 3, \"display\", 4, \"ngIf\"], [\"class\", \"rgba-text\", 3, \"display\", 4, \"ngIf\"], [\"class\", \"hex-text\", 3, \"hex-alpha\", \"display\", 4, \"ngIf\"], [\"class\", \"value-text\", 4, \"ngIf\"], [\"class\", \"type-policy\", 4, \"ngIf\"], [\"class\", \"preset-area\", 4, \"ngIf\"], [\"class\", \"button-area\", 4, \"ngIf\"], [\"class\", \"extra-template\", 4, \"ngIf\"], [1, \"saturation-lightness\", 3, \"newValue\", \"dragStart\", \"dragEnd\", \"slider\", \"rgX\", \"rgY\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"height\", \"24px\", \"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"fill\", \"#000000\", 1, \"eyedropper-icon\"], [\"d\", \"M0 0h24v24H0V0z\", \"fill\", \"none\"], [\"d\", \"M17.66 5.41l.92.92-2.69 2.69-.92-.92 2.69-2.69M17.67 3c-.26 0-.51.1-.71.29l-3.12 3.12-1.93-1.91-1.41 1.41 1.42 1.42L3 16.25V21h4.75l8.92-8.92 1.42 1.42 1.41-1.41-1.92-1.92 3.12-3.12c.4-.4.4-1.03.01-1.42l-2.34-2.34c-.2-.19-.45-.29-.7-.29zM6.92 19L5 17.08l8.06-8.06 1.92 1.92L6.92 19z\"], [\"type\", \"button\", 3, \"click\", \"disabled\"], [2, \"height\", \"16px\"], [1, \"cmyk-text\"], [1, \"box\"], [\"type\", \"number\", \"pattern\", \"[0-9]*\", \"min\", \"0\", \"max\", \"100\", 3, \"keyup.enter\", \"newValue\", \"text\", \"rg\", \"value\"], [\"type\", \"number\", \"pattern\", \"[0-9]+([\\\\.,][0-9]{1,2})?\", \"min\", \"0\", \"max\", \"1\", \"step\", \"0.1\", 3, \"text\", \"rg\", \"value\", \"keyup.enter\", \"newValue\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"number\", \"pattern\", \"[0-9]+([\\\\.,][0-9]{1,2})?\", \"min\", \"0\", \"max\", \"1\", \"step\", \"0.1\", 3, \"keyup.enter\", \"newValue\", \"text\", \"rg\", \"value\"], [1, \"hsla-text\"], [\"type\", \"number\", \"pattern\", \"[0-9]*\", \"min\", \"0\", \"max\", \"360\", 3, \"keyup.enter\", \"newValue\", \"text\", \"rg\", \"value\"], [1, \"rgba-text\"], [\"type\", \"number\", \"pattern\", \"[0-9]*\", \"min\", \"0\", \"max\", \"255\", 3, \"keyup.enter\", \"newValue\", \"text\", \"rg\", \"value\"], [1, \"hex-text\"], [3, \"blur\", \"keyup.enter\", \"newValue\", \"text\", \"value\"], [1, \"value-text\"], [1, \"type-policy\"], [1, \"type-policy-arrow\", 3, \"click\"], [1, \"preset-area\"], [1, \"preset-label\"], [3, \"class\", 4, \"ngIf\"], [\"class\", \"preset-color\", 3, \"backgroundColor\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"preset-color\", 3, \"click\"], [3, \"class\", \"click\", 4, \"ngIf\"], [3, \"click\"], [1, \"button-area\"], [\"type\", \"button\", 3, \"class\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", 3, \"click\"], [1, \"extra-template\"], [4, \"ngTemplateOutlet\"]],\n  template: function _ColorPickerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵelementStart(0, \"div\", 4, 0);\n      i0.ɵɵlistener(\"click\", function _ColorPickerComponent_Template_div_click_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView($event.stopPropagation());\n      });\n      i0.ɵɵtemplate(2, _ColorPickerComponent_div_2_Template, 1, 7, \"div\", 5)(3, _ColorPickerComponent_div_3_Template, 2, 8, \"div\", 6);\n      i0.ɵɵelementStart(4, \"div\", 7)(5, \"div\", 8);\n      i0.ɵɵelement(6, \"div\", 9);\n      i0.ɵɵelementStart(7, \"div\", 10);\n      i0.ɵɵlistener(\"click\", function _ColorPickerComponent_Template_div_click_7_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.eyeDropperSupported && ctx.cpEyeDropper && ctx.onEyeDropper());\n      });\n      i0.ɵɵtemplate(8, _ColorPickerComponent__svg_svg_8_Template, 3, 0, \"svg\", 11);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(9, _ColorPickerComponent_button_9_Template, 2, 5, \"button\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(10, \"div\", 13);\n      i0.ɵɵtemplate(11, _ColorPickerComponent_div_11_Template, 1, 0, \"div\", 14);\n      i0.ɵɵelementStart(12, \"div\", 15, 1);\n      i0.ɵɵlistener(\"newValue\", function _ColorPickerComponent_Template_div_newValue_12_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.onHueChange($event));\n      })(\"dragStart\", function _ColorPickerComponent_Template_div_dragStart_12_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.onDragStart(\"hue\"));\n      })(\"dragEnd\", function _ColorPickerComponent_Template_div_dragEnd_12_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.onDragEnd(\"hue\"));\n      });\n      i0.ɵɵelement(14, \"div\", 16);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"div\", 17, 2);\n      i0.ɵɵlistener(\"newValue\", function _ColorPickerComponent_Template_div_newValue_15_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.onValueChange($event));\n      })(\"dragStart\", function _ColorPickerComponent_Template_div_dragStart_15_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.onDragStart(\"value\"));\n      })(\"dragEnd\", function _ColorPickerComponent_Template_div_dragEnd_15_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.onDragEnd(\"value\"));\n      });\n      i0.ɵɵelement(17, \"div\", 16);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"div\", 18, 3);\n      i0.ɵɵlistener(\"newValue\", function _ColorPickerComponent_Template_div_newValue_18_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.onAlphaChange($event));\n      })(\"dragStart\", function _ColorPickerComponent_Template_div_dragStart_18_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.onDragStart(\"alpha\"));\n      })(\"dragEnd\", function _ColorPickerComponent_Template_div_dragEnd_18_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.onDragEnd(\"alpha\"));\n      });\n      i0.ɵɵelement(20, \"div\", 16);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(21, _ColorPickerComponent_div_21_Template, 17, 12, \"div\", 19)(22, _ColorPickerComponent_div_22_Template, 14, 10, \"div\", 20)(23, _ColorPickerComponent_div_23_Template, 14, 10, \"div\", 21)(24, _ColorPickerComponent_div_24_Template, 8, 7, \"div\", 22)(25, _ColorPickerComponent_div_25_Template, 9, 3, \"div\", 23)(26, _ColorPickerComponent_div_26_Template, 3, 0, \"div\", 24)(27, _ColorPickerComponent_div_27_Template, 6, 3, \"div\", 25)(28, _ColorPickerComponent_div_28_Template, 3, 2, \"div\", 26)(29, _ColorPickerComponent_div_29_Template, 2, 1, \"div\", 27);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"display\", !ctx.show ? \"none\" : \"block\")(\"visibility\", ctx.hidden ? \"hidden\" : \"visible\")(\"top\", ctx.top, \"px\")(\"left\", ctx.left, \"px\")(\"position\", ctx.position)(\"height\", ctx.cpHeight, \"px\")(\"width\", ctx.cpWidth, \"px\");\n      i0.ɵɵclassProp(\"open\", ctx.show);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.cpDialogDisplay === \"popup\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", (ctx.cpColorMode || 1) === 1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵstyleProp(\"background-color\", ctx.selectedColor)(\"cursor\", ctx.eyeDropperSupported && ctx.cpEyeDropper ? \"pointer\" : null);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.eyeDropperSupported && ctx.cpEyeDropper);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.cpAddColorButton);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.cpAlphaChannel === \"disabled\");\n      i0.ɵɵadvance();\n      i0.ɵɵstyleProp(\"display\", (ctx.cpColorMode || 1) === 1 ? \"block\" : \"none\");\n      i0.ɵɵproperty(\"rgX\", 1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵstyleProp(\"left\", ctx.slider == null ? null : ctx.slider.h, \"px\");\n      i0.ɵɵadvance();\n      i0.ɵɵstyleProp(\"display\", (ctx.cpColorMode || 1) === 2 ? \"block\" : \"none\");\n      i0.ɵɵproperty(\"rgX\", 1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵstyleProp(\"right\", ctx.slider == null ? null : ctx.slider.v, \"px\");\n      i0.ɵɵadvance();\n      i0.ɵɵstyleProp(\"display\", ctx.cpAlphaChannel === \"disabled\" ? \"none\" : \"block\")(\"background-color\", ctx.alphaSliderColor);\n      i0.ɵɵproperty(\"rgX\", 1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵstyleProp(\"left\", ctx.slider == null ? null : ctx.slider.a, \"px\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 1);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 1);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 1);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 1);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 2);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.cpDisableInput && (ctx.cpColorMode || 1) === 1);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", (ctx.cpPresetColors == null ? null : ctx.cpPresetColors.length) || ctx.cpAddColorButton);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.cpOKButton || ctx.cpCancelButton);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.cpExtraTemplate);\n    }\n  },\n  dependencies: [i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, TextDirective, SliderDirective],\n  styles: [\".color-picker{position:absolute;z-index:1000;width:230px;height:auto;border:#777 solid 1px;cursor:default;-webkit-user-select:none;user-select:none;background-color:#fff}.color-picker *{box-sizing:border-box;margin:0;font-size:11px}.color-picker input{width:0;height:26px;min-width:0;font-size:13px;text-align:center;color:#000}.color-picker input:invalid,.color-picker input:-moz-ui-invalid,.color-picker input:-moz-submit-invalid{box-shadow:none}.color-picker input::-webkit-inner-spin-button,.color-picker input::-webkit-outer-spin-button{margin:0;-webkit-appearance:none}.color-picker .arrow{position:absolute;z-index:999999;width:0;height:0;border-style:solid}.color-picker .arrow.arrow-top{left:8px;border-width:10px 5px;border-color:#777 rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-bottom{top:-20px;left:8px;border-width:10px 5px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) #777 rgba(0,0,0,0)}.color-picker .arrow.arrow-top-left,.color-picker .arrow.arrow-left-top{right:-21px;bottom:8px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-top-right,.color-picker .arrow.arrow-right-top{bottom:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-left,.color-picker .arrow.arrow-left-bottom,.color-picker .arrow.arrow-bottom-left{top:8px;right:-21px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-right,.color-picker .arrow.arrow-right-bottom,.color-picker .arrow.arrow-bottom-right{top:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .cursor{position:relative;width:16px;height:16px;border:#222 solid 2px;border-radius:50%;cursor:default}.color-picker .box{display:flex;padding:4px 8px}.color-picker .left{position:relative;padding:16px 8px}.color-picker .right{flex:1 1 auto;padding:12px 8px}.color-picker .button-area{padding:0 16px 16px;text-align:right}.color-picker .button-area button{margin-left:8px}.color-picker .preset-area{padding:4px 15px}.color-picker .preset-area .preset-label{overflow:hidden;width:100%;padding:4px;font-size:11px;white-space:nowrap;text-align:left;text-overflow:ellipsis;color:#555}.color-picker .preset-area .preset-color{position:relative;display:inline-block;width:18px;height:18px;margin:4px 6px 8px;border:#a9a9a9 solid 1px;border-radius:25%;cursor:pointer}.color-picker .preset-area .preset-empty-message{min-height:18px;margin-top:4px;margin-bottom:8px;font-style:italic;text-align:center}.color-picker .hex-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .hex-text .box{padding:0 24px 8px 8px}.color-picker .hex-text .box div{float:left;flex:1 1 auto;text-align:center;color:#555;clear:left}.color-picker .hex-text .box input{flex:1 1 auto;padding:1px;border:#a9a9a9 solid 1px}.color-picker .hex-alpha .box div:first-child,.color-picker .hex-alpha .box input:first-child{flex-grow:3;margin-right:8px}.color-picker .cmyk-text,.color-picker .hsla-text,.color-picker .rgba-text,.color-picker .value-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .cmyk-text .box,.color-picker .hsla-text .box,.color-picker .rgba-text .box{padding:0 24px 8px 8px}.color-picker .value-text .box{padding:0 8px 8px}.color-picker .cmyk-text .box div,.color-picker .hsla-text .box div,.color-picker .rgba-text .box div,.color-picker .value-text .box div{flex:1 1 auto;margin-right:8px;text-align:center;color:#555}.color-picker .cmyk-text .box div:last-child,.color-picker .hsla-text .box div:last-child,.color-picker .rgba-text .box div:last-child,.color-picker .value-text .box div:last-child{margin-right:0}.color-picker .cmyk-text .box input,.color-picker .hsla-text .box input,.color-picker .rgba-text .box input,.color-picker .value-text .box input{float:left;flex:1;padding:1px;margin:0 8px 0 0;border:#a9a9a9 solid 1px}.color-picker .cmyk-text .box input:last-child,.color-picker .hsla-text .box input:last-child,.color-picker .rgba-text .box input:last-child,.color-picker .value-text .box input:last-child{margin-right:0}.color-picker .hue-alpha{align-items:center;margin-bottom:3px}.color-picker .hue{direction:ltr;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwkUFWbCCAAAAFxJREFUaN7t0kEKg0AQAME2x83/n2qu5qCgD1iDhCoYdpnbQC9bbY1qVO/jvc6k3ad91s7/7F1/csgPrujuQ17BDYSFsBAWwgJhISyEBcJCWAgLhIWwEBYIi2f7Ar/1TCgFH2X9AAAAAElFTkSuQmCC)}.color-picker .value{direction:rtl;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAACTklEQVR42u3SYUcrABhA4U2SkmRJMmWSJklKJiWZZpKUJJskKUmaTFImKZOUzMySpGRmliRNJilJSpKSJEtmSpIpmWmSdO736/6D+x7OP3gUCoWCv1cqlSQlJZGcnExKSgqpqamkpaWRnp5ORkYGmZmZqFQqsrKyyM7OJicnh9zcXNRqNXl5eeTn56PRaCgoKKCwsJCioiK0Wi3FxcWUlJRQWlpKWVkZ5eXlVFRUUFlZiU6no6qqiurqampqaqitraWurg69Xk99fT0GgwGj0UhDQwONjY00NTXR3NxMS0sLra2ttLW10d7ejslkwmw209HRQWdnJ11dXXR3d9PT00Nvby99fX309/czMDDA4OAgFouFoaEhrFYrw8PDjIyMMDo6ytjYGDabjfHxcSYmJpicnGRqagq73c709DQzMzPMzs4yNzfH/Pw8DocDp9OJy+XC7XazsLDA4uIiS0tLLC8vs7KywurqKmtra3g8HrxeLz6fD7/fz/r6OhsbG2xubrK1tcX29jaBQICdnR2CwSC7u7vs7e2xv7/PwcEBh4eHHB0dcXx8zMnJCaenp5ydnXF+fs7FxQWXl5dcXV1xfX3Nzc0Nt7e33N3dEQqFuL+/5+HhgXA4TCQS4fHxkaenJ56fn3l5eeH19ZVoNMrb2xvv7+98fHwQi8WIx+N8fn6SSCT4+vri+/ubn58ffn9/+VcKgSWwBJbAElgCS2AJLIElsASWwBJYAktgCSyBJbAElsASWAJLYAksgSWwBJbAElgCS2AJLIElsP4/WH8AmJ5Z6jHS4h8AAAAASUVORK5CYII=)}.color-picker .alpha{direction:ltr;width:100%;height:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwYQlZMa3gAAAWVJREFUaN7tmEGO6jAQRCsOArHgBpyAJYGjcGocxAm4A2IHpmoWE0eBH+ezmFlNvU06shJ3W6VEelWMUQAIIF9f6qZpimsA1LYtS2uF51/u27YVAFZVRUkEoGHdPV/sIcbIEIIkUdI/9Xa7neyv61+SWFUVAVCSct00TWn2fv6u3+Ecfd3tXzy/0+nEUu+SPjo/kqzrmiQpScN6v98XewfA8/lMkiLJ2WxGSUopcT6fM6U0NX9/frfbjev1WtfrlZfLhYfDQQHG/AIOlnGwjINlHCxjHCzjYJm/TJWdCwquJXseFFzGwDNNeiKMOJTO8xQdDQaeB29+K9efeLaBo9J7vdvtJj1RjFFjfiv7qv95tjx/7leSQgh93e1ffMeIp6O+YQjho/N791t1XVOSSI7N//K+4/GoxWLBx+PB5/Op5XLJ+/3OlJJWqxU3m83ovv5iGf8KjYNlHCxjHCzjYBkHy5gf5gusvQU7U37jTAAAAABJRU5ErkJggg==)}.color-picker .type-policy{position:absolute;top:218px;right:12px;width:16px;height:24px;background-size:8px 16px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAACewAAAnsB01CO3AAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIASURBVEiJ7ZY9axRRFIafsxMStrLQJpAgpBFhi+C9w1YSo00I6RZ/g9vZpBf/QOr4GyRgkSKNSrAadsZqQGwCkuAWyRZJsySwvhZ7N/vhzrgbLH3Ld8597jlzz50zJokyxXH8DqDVar0qi6v8BbItqSGpEcfxdlmsFWXkvX8AfAVWg3UKPEnT9GKujMzsAFgZsVaCN1VTQd77XUnrgE1kv+6935268WRpzrnHZvYRWC7YvC3pRZZl3wozqtVqiyH9IgjAspkd1Gq1xUJQtVrdB9ZKIAOthdg/Qc65LUk7wNIMoCVJO865rYFhkqjX6/d7vV4GPJwBMqofURS5JEk6FYBer/eeYb/Mo9WwFnPOvQbeAvfuAAK4BN4sAJtAG/gJIElmNuiJyba3EGNmZiPeZuEVmVell/Y/6N+CzDn3AXhEOOo7Hv/3BeAz8IzQkMPnJbuPx1wC+yYJ7/0nYIP5S/0FHKdp+rwCEEXRS/rf5Hl1Gtb2M0iSpCOpCZzPATmX1EySpHMLAsiy7MjMDoHrGSDXZnaYZdnRwBh7J91utwmczAA6CbG3GgPleX4jqUH/a1CktqRGnuc3hSCAMB32gKspkCtgb3KCQMmkjeP4WNJThrNNZval1WptTIsv7JtQ4tmIdRa8qSoEpWl6YWZNoAN0zKxZNPehpLSBZv2t+Q0CJ9lLnARQLAAAAABJRU5ErkJggg==);background-repeat:no-repeat;background-position:center}.color-picker .type-policy .type-policy-arrow{display:block;width:100%;height:50%}.color-picker .selected-color{position:absolute;top:16px;left:8px;width:40px;height:40px;border:1px solid #a9a9a9;border-radius:50%}.color-picker .selected-color-background{width:40px;height:40px;border-radius:50%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAh0lEQVRYR+2W0QlAMQgD60zdfwOdqa8TmI/wQMr5K0I5bZLIzLOa2nt37VVVbd+dDx5obgCC3KBLwJ2ff4PnVidkf+ucIhw80HQaCLo3DMH3CRK3iFsmAWVl6hPNDwt8EvNE5q+YuEXcMgkonVM6SdyCoEvAnZ8v1Hjx817MilmxSUB5rdLJDycZgUAZUch/AAAAAElFTkSuQmCC)}.color-picker .saturation-lightness{direction:ltr;width:100%;height:130px;border:none;cursor:pointer;touch-action:manipulation;background-size:100% 100%;background-image:url(data:image/png;base64,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)}.color-picker .cp-add-color-button-class{position:absolute;display:inline;padding:0;margin:3px -3px;border:0;cursor:pointer;background:transparent}.color-picker .cp-add-color-button-class:hover{text-decoration:underline}.color-picker .cp-add-color-button-class:disabled{cursor:not-allowed;color:#999}.color-picker .cp-add-color-button-class:disabled:hover{text-decoration:none}.color-picker .cp-remove-color-button-class{position:absolute;top:-5px;right:-5px;display:block;width:10px;height:10px;border-radius:50%;cursor:pointer;text-align:center;background:#fff;box-shadow:1px 1px 5px #333}.color-picker .cp-remove-color-button-class:before{content:\\\"x\\\";position:relative;bottom:3.5px;display:inline-block;font-size:10px}.color-picker .eyedropper-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);fill:#fff;mix-blend-mode:exclusion}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColorPickerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'color-picker',\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div #dialogPopup class=\\\"color-picker\\\" [class.open]=\\\"show\\\" [style.display]=\\\"!show ? 'none' : 'block'\\\" [style.visibility]=\\\"hidden ? 'hidden' : 'visible'\\\" [style.top.px]=\\\"top\\\" [style.left.px]=\\\"left\\\" [style.position]=\\\"position\\\" [style.height.px]=\\\"cpHeight\\\" [style.width.px]=\\\"cpWidth\\\" (click)=\\\"$event.stopPropagation()\\\">\\n  <div *ngIf=\\\"cpDialogDisplay === 'popup'\\\" [style.left]=\\\"cpArrowPosition\\\" class=\\\"arrow arrow-{{cpUsePosition}}\\\" [style.top.px]=\\\"arrowTop\\\"></div>\\n\\n  <div *ngIf=\\\"(cpColorMode ||\\u00A01) === 1\\\" class=\\\"saturation-lightness\\\" [slider] [rgX]=\\\"1\\\" [rgY]=\\\"1\\\" [style.background-color]=\\\"hueSliderColor\\\" (newValue)=\\\"onColorChange($event)\\\" (dragStart)=\\\"onDragStart('saturation-lightness')\\\" (dragEnd)=\\\"onDragEnd('saturation-lightness')\\\">\\n    <div class=\\\"cursor\\\" [style.top.px]=\\\"slider?.v\\\" [style.left.px]=\\\"slider?.s\\\"></div>\\n  </div>\\n\\n  <div class=\\\"hue-alpha box\\\">\\n    <div class=\\\"left\\\">\\n      <div class=\\\"selected-color-background\\\"></div>\\n\\n      <div class=\\\"selected-color\\\" [style.background-color]=\\\"selectedColor\\\" [style.cursor]=\\\"eyeDropperSupported && cpEyeDropper ? 'pointer' : null\\\" (click)=\\\"eyeDropperSupported && cpEyeDropper && onEyeDropper()\\\">\\n        <svg *ngIf=\\\"eyeDropperSupported && cpEyeDropper\\\" class=\\\"eyedropper-icon\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" height=\\\"24px\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" fill=\\\"#000000\\\"><path d=\\\"M0 0h24v24H0V0z\\\" fill=\\\"none\\\"/><path d=\\\"M17.66 5.41l.92.92-2.69 2.69-.92-.92 2.69-2.69M17.67 3c-.26 0-.51.1-.71.29l-3.12 3.12-1.93-1.91-1.41 1.41 1.42 1.42L3 16.25V21h4.75l8.92-8.92 1.42 1.42 1.41-1.41-1.92-1.92 3.12-3.12c.4-.4.4-1.03.01-1.42l-2.34-2.34c-.2-.19-.45-.29-.7-.29zM6.92 19L5 17.08l8.06-8.06 1.92 1.92L6.92 19z\\\"/></svg>\\n      </div>\\n\\n      <button *ngIf=\\\"cpAddColorButton\\\" type=\\\"button\\\" class=\\\"{{cpAddColorButtonClass}}\\\" [disabled]=\\\"cpPresetColors && cpPresetColors.length >= cpMaxPresetColorsLength\\\" (click)=\\\"onAddPresetColor($event, selectedColor)\\\">\\n        {{cpAddColorButtonText}}\\n      </button>\\n    </div>\\n\\n    <div class=\\\"right\\\">\\n      <div *ngIf=\\\"cpAlphaChannel==='disabled'\\\" style=\\\"height: 16px;\\\"></div>\\n\\n      <div #hueSlider class=\\\"hue\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 1 ? 'block' : 'none'\\\" (newValue)=\\\"onHueChange($event)\\\" (dragStart)=\\\"onDragStart('hue')\\\" (dragEnd)=\\\"onDragEnd('hue')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.h\\\"></div>\\n      </div>\\n\\n      <div #valueSlider class=\\\"value\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 2 ? 'block': 'none'\\\" (newValue)=\\\"onValueChange($event)\\\" (dragStart)=\\\"onDragStart('value')\\\" (dragEnd)=\\\"onDragEnd('value')\\\">\\n        <div class=\\\"cursor\\\" [style.right.px]=\\\"slider?.v\\\"></div>\\n      </div>\\n\\n      <div #alphaSlider class=\\\"alpha\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"cpAlphaChannel === 'disabled' ? 'none' : 'block'\\\" [style.background-color]=\\\"alphaSliderColor\\\" (newValue)=\\\"onAlphaChange($event)\\\" (dragStart)=\\\"onDragStart('alpha')\\\" (dragEnd)=\\\"onDragEnd('alpha')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.a\\\"></div>\\n      </div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"cmyk-text\\\" [style.display]=\\\"format !== 3 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.c\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onCyanInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.m\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onMagentaInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.y\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onYellowInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.k\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlackInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"cmykText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n     <div class=\\\"box\\\">\\n      <div>C</div><div>M</div><div>Y</div><div>K</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" class=\\\"hsla-text\\\" [style.display]=\\\"format !== 2 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"360\\\" [text] [rg]=\\\"360\\\" [value]=\\\"hslaText?.h\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHueInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.s\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onSaturationInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onLightnessInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>H</div><div>S</div><div>L</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" [style.display]=\\\"format !== 1 ? 'none' : 'block'\\\" class=\\\"rgba-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.r\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onRedInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.g\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onGreenInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.b\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"rgbaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>R</div><div>G</div><div>B</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"hex-text\\\" [class.hex-alpha]=\\\"cpAlphaChannel==='forced'\\\"\\n    [style.display]=\\\"format !== 0 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input [text] [value]=\\\"hexText\\\" (blur)=\\\"onHexInput(null)\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHexInput($event)\\\"/>\\n      <input *ngIf=\\\"cpAlphaChannel==='forced'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hexAlpha\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\"/>\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>Hex</div>\\n      <div *ngIf=\\\"cpAlphaChannel==='forced'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 2\\\" class=\\\"value-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onValueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\"  [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>V</div><div>A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"type-policy\\\">\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(-1)\\\"></span>\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(1)\\\"></span>\\n  </div>\\n\\n  <div *ngIf=\\\"cpPresetColors?.length || cpAddColorButton\\\" class=\\\"preset-area\\\">\\n    <hr>\\n\\n    <div class=\\\"preset-label\\\">{{cpPresetLabel}}</div>\\n\\n    <div *ngIf=\\\"cpPresetColors?.length\\\" class=\\\"{{cpPresetColorsClass}}\\\">\\n      <div *ngFor=\\\"let color of cpPresetColors\\\" class=\\\"preset-color\\\" [style.backgroundColor]=\\\"color\\\" (click)=\\\"setColorFromString(color)\\\">\\n        <span *ngIf=\\\"cpAddColorButton\\\" class=\\\"{{cpRemoveColorButtonClass}}\\\" (click)=\\\"onRemovePresetColor($event, color)\\\"></span>\\n      </div>\\n    </div>\\n\\n    <div *ngIf=\\\"!cpPresetColors?.length && cpAddColorButton\\\" class=\\\"{{cpPresetEmptyMessageClass}}\\\">{{cpPresetEmptyMessage}}</div>\\n  </div>\\n\\n  <div *ngIf=\\\"cpOKButton || cpCancelButton\\\" class=\\\"button-area\\\">\\n    <button *ngIf=\\\"cpCancelButton\\\" type=\\\"button\\\" class=\\\"{{cpCancelButtonClass}}\\\" (click)=\\\"onCancelColor($event)\\\">{{cpCancelButtonText}}</button>\\n\\n    <button *ngIf=\\\"cpOKButton\\\" type=\\\"button\\\" class=\\\"{{cpOKButtonClass}}\\\" (click)=\\\"onAcceptColor($event)\\\">{{cpOKButtonText}}</button>\\n  </div>\\n\\n  <div class=\\\"extra-template\\\" *ngIf=\\\"cpExtraTemplate\\\">\\n    <ng-container *ngTemplateOutlet=\\\"cpExtraTemplate\\\"></ng-container>\\n  </div>\\n</div>\\n\",\n      styles: [\".color-picker{position:absolute;z-index:1000;width:230px;height:auto;border:#777 solid 1px;cursor:default;-webkit-user-select:none;user-select:none;background-color:#fff}.color-picker *{box-sizing:border-box;margin:0;font-size:11px}.color-picker input{width:0;height:26px;min-width:0;font-size:13px;text-align:center;color:#000}.color-picker input:invalid,.color-picker input:-moz-ui-invalid,.color-picker input:-moz-submit-invalid{box-shadow:none}.color-picker input::-webkit-inner-spin-button,.color-picker input::-webkit-outer-spin-button{margin:0;-webkit-appearance:none}.color-picker .arrow{position:absolute;z-index:999999;width:0;height:0;border-style:solid}.color-picker .arrow.arrow-top{left:8px;border-width:10px 5px;border-color:#777 rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-bottom{top:-20px;left:8px;border-width:10px 5px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) #777 rgba(0,0,0,0)}.color-picker .arrow.arrow-top-left,.color-picker .arrow.arrow-left-top{right:-21px;bottom:8px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-top-right,.color-picker .arrow.arrow-right-top{bottom:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-left,.color-picker .arrow.arrow-left-bottom,.color-picker .arrow.arrow-bottom-left{top:8px;right:-21px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-right,.color-picker .arrow.arrow-right-bottom,.color-picker .arrow.arrow-bottom-right{top:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .cursor{position:relative;width:16px;height:16px;border:#222 solid 2px;border-radius:50%;cursor:default}.color-picker .box{display:flex;padding:4px 8px}.color-picker .left{position:relative;padding:16px 8px}.color-picker .right{flex:1 1 auto;padding:12px 8px}.color-picker .button-area{padding:0 16px 16px;text-align:right}.color-picker .button-area button{margin-left:8px}.color-picker .preset-area{padding:4px 15px}.color-picker .preset-area .preset-label{overflow:hidden;width:100%;padding:4px;font-size:11px;white-space:nowrap;text-align:left;text-overflow:ellipsis;color:#555}.color-picker .preset-area .preset-color{position:relative;display:inline-block;width:18px;height:18px;margin:4px 6px 8px;border:#a9a9a9 solid 1px;border-radius:25%;cursor:pointer}.color-picker .preset-area .preset-empty-message{min-height:18px;margin-top:4px;margin-bottom:8px;font-style:italic;text-align:center}.color-picker .hex-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .hex-text .box{padding:0 24px 8px 8px}.color-picker .hex-text .box div{float:left;flex:1 1 auto;text-align:center;color:#555;clear:left}.color-picker .hex-text .box input{flex:1 1 auto;padding:1px;border:#a9a9a9 solid 1px}.color-picker .hex-alpha .box div:first-child,.color-picker .hex-alpha .box input:first-child{flex-grow:3;margin-right:8px}.color-picker .cmyk-text,.color-picker .hsla-text,.color-picker .rgba-text,.color-picker .value-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .cmyk-text .box,.color-picker .hsla-text .box,.color-picker .rgba-text .box{padding:0 24px 8px 8px}.color-picker .value-text .box{padding:0 8px 8px}.color-picker .cmyk-text .box div,.color-picker .hsla-text .box div,.color-picker .rgba-text .box div,.color-picker .value-text .box div{flex:1 1 auto;margin-right:8px;text-align:center;color:#555}.color-picker .cmyk-text .box div:last-child,.color-picker .hsla-text .box div:last-child,.color-picker .rgba-text .box div:last-child,.color-picker .value-text .box div:last-child{margin-right:0}.color-picker .cmyk-text .box input,.color-picker .hsla-text .box input,.color-picker .rgba-text .box input,.color-picker .value-text .box input{float:left;flex:1;padding:1px;margin:0 8px 0 0;border:#a9a9a9 solid 1px}.color-picker .cmyk-text .box input:last-child,.color-picker .hsla-text .box input:last-child,.color-picker .rgba-text .box input:last-child,.color-picker .value-text .box input:last-child{margin-right:0}.color-picker .hue-alpha{align-items:center;margin-bottom:3px}.color-picker .hue{direction:ltr;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwkUFWbCCAAAAFxJREFUaN7t0kEKg0AQAME2x83/n2qu5qCgD1iDhCoYdpnbQC9bbY1qVO/jvc6k3ad91s7/7F1/csgPrujuQ17BDYSFsBAWwgJhISyEBcJCWAgLhIWwEBYIi2f7Ar/1TCgFH2X9AAAAAElFTkSuQmCC)}.color-picker .value{direction:rtl;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAACTklEQVR42u3SYUcrABhA4U2SkmRJMmWSJklKJiWZZpKUJJskKUmaTFImKZOUzMySpGRmliRNJilJSpKSJEtmSpIpmWmSdO736/6D+x7OP3gUCoWCv1cqlSQlJZGcnExKSgqpqamkpaWRnp5ORkYGmZmZqFQqsrKyyM7OJicnh9zcXNRqNXl5eeTn56PRaCgoKKCwsJCioiK0Wi3FxcWUlJRQWlpKWVkZ5eXlVFRUUFlZiU6no6qqiurqampqaqitraWurg69Xk99fT0GgwGj0UhDQwONjY00NTXR3NxMS0sLra2ttLW10d7ejslkwmw209HRQWdnJ11dXXR3d9PT00Nvby99fX309/czMDDA4OAgFouFoaEhrFYrw8PDjIyMMDo6ytjYGDabjfHxcSYmJpicnGRqagq73c709DQzMzPMzs4yNzfH/Pw8DocDp9OJy+XC7XazsLDA4uIiS0tLLC8vs7KywurqKmtra3g8HrxeLz6fD7/fz/r6OhsbG2xubrK1tcX29jaBQICdnR2CwSC7u7vs7e2xv7/PwcEBh4eHHB0dcXx8zMnJCaenp5ydnXF+fs7FxQWXl5dcXV1xfX3Nzc0Nt7e33N3dEQqFuL+/5+HhgXA4TCQS4fHxkaenJ56fn3l5eeH19ZVoNMrb2xvv7+98fHwQi8WIx+N8fn6SSCT4+vri+/ubn58ffn9/+VcKgSWwBJbAElgCS2AJLIElsASWwBJYAktgCSyBJbAElsASWAJLYAksgSWwBJbAElgCS2AJLIElsP4/WH8AmJ5Z6jHS4h8AAAAASUVORK5CYII=)}.color-picker .alpha{direction:ltr;width:100%;height:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwYQlZMa3gAAAWVJREFUaN7tmEGO6jAQRCsOArHgBpyAJYGjcGocxAm4A2IHpmoWE0eBH+ezmFlNvU06shJ3W6VEelWMUQAIIF9f6qZpimsA1LYtS2uF51/u27YVAFZVRUkEoGHdPV/sIcbIEIIkUdI/9Xa7neyv61+SWFUVAVCSct00TWn2fv6u3+Ecfd3tXzy/0+nEUu+SPjo/kqzrmiQpScN6v98XewfA8/lMkiLJ2WxGSUopcT6fM6U0NX9/frfbjev1WtfrlZfLhYfDQQHG/AIOlnGwjINlHCxjHCzjYJm/TJWdCwquJXseFFzGwDNNeiKMOJTO8xQdDQaeB29+K9efeLaBo9J7vdvtJj1RjFFjfiv7qv95tjx/7leSQgh93e1ffMeIp6O+YQjho/N791t1XVOSSI7N//K+4/GoxWLBx+PB5/Op5XLJ+/3OlJJWqxU3m83ovv5iGf8KjYNlHCxjHCzjYBkHy5gf5gusvQU7U37jTAAAAABJRU5ErkJggg==)}.color-picker .type-policy{position:absolute;top:218px;right:12px;width:16px;height:24px;background-size:8px 16px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAACewAAAnsB01CO3AAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIASURBVEiJ7ZY9axRRFIafsxMStrLQJpAgpBFhi+C9w1YSo00I6RZ/g9vZpBf/QOr4GyRgkSKNSrAadsZqQGwCkuAWyRZJsySwvhZ7N/vhzrgbLH3Ld8597jlzz50zJokyxXH8DqDVar0qi6v8BbItqSGpEcfxdlmsFWXkvX8AfAVWg3UKPEnT9GKujMzsAFgZsVaCN1VTQd77XUnrgE1kv+6935268WRpzrnHZvYRWC7YvC3pRZZl3wozqtVqiyH9IgjAspkd1Gq1xUJQtVrdB9ZKIAOthdg/Qc65LUk7wNIMoCVJO865rYFhkqjX6/d7vV4GPJwBMqofURS5JEk6FYBer/eeYb/Mo9WwFnPOvQbeAvfuAAK4BN4sAJtAG/gJIElmNuiJyba3EGNmZiPeZuEVmVell/Y/6N+CzDn3AXhEOOo7Hv/3BeAz8IzQkMPnJbuPx1wC+yYJ7/0nYIP5S/0FHKdp+rwCEEXRS/rf5Hl1Gtb2M0iSpCOpCZzPATmX1EySpHMLAsiy7MjMDoHrGSDXZnaYZdnRwBh7J91utwmczAA6CbG3GgPleX4jqUH/a1CktqRGnuc3hSCAMB32gKspkCtgb3KCQMmkjeP4WNJThrNNZval1WptTIsv7JtQ4tmIdRa8qSoEpWl6YWZNoAN0zKxZNPehpLSBZv2t+Q0CJ9lLnARQLAAAAABJRU5ErkJggg==);background-repeat:no-repeat;background-position:center}.color-picker .type-policy .type-policy-arrow{display:block;width:100%;height:50%}.color-picker .selected-color{position:absolute;top:16px;left:8px;width:40px;height:40px;border:1px solid #a9a9a9;border-radius:50%}.color-picker .selected-color-background{width:40px;height:40px;border-radius:50%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAh0lEQVRYR+2W0QlAMQgD60zdfwOdqa8TmI/wQMr5K0I5bZLIzLOa2nt37VVVbd+dDx5obgCC3KBLwJ2ff4PnVidkf+ucIhw80HQaCLo3DMH3CRK3iFsmAWVl6hPNDwt8EvNE5q+YuEXcMgkonVM6SdyCoEvAnZ8v1Hjx817MilmxSUB5rdLJDycZgUAZUch/AAAAAElFTkSuQmCC)}.color-picker .saturation-lightness{direction:ltr;width:100%;height:130px;border:none;cursor:pointer;touch-action:manipulation;background-size:100% 100%;background-image:url(data:image/png;base64,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)}.color-picker .cp-add-color-button-class{position:absolute;display:inline;padding:0;margin:3px -3px;border:0;cursor:pointer;background:transparent}.color-picker .cp-add-color-button-class:hover{text-decoration:underline}.color-picker .cp-add-color-button-class:disabled{cursor:not-allowed;color:#999}.color-picker .cp-add-color-button-class:disabled:hover{text-decoration:none}.color-picker .cp-remove-color-button-class{position:absolute;top:-5px;right:-5px;display:block;width:10px;height:10px;border-radius:50%;cursor:pointer;text-align:center;background:#fff;box-shadow:1px 1px 5px #333}.color-picker .cp-remove-color-button-class:before{content:\\\"x\\\";position:relative;bottom:3.5px;display:inline-block;font-size:10px}.color-picker .eyedropper-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);fill:#fff;mix-blend-mode:exclusion}\\n\"]\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: ColorPickerService\n  }], {\n    dialogElement: [{\n      type: ViewChild,\n      args: ['dialogPopup', {\n        static: true\n      }]\n    }],\n    hueSlider: [{\n      type: ViewChild,\n      args: ['hueSlider', {\n        static: true\n      }]\n    }],\n    alphaSlider: [{\n      type: ViewChild,\n      args: ['alphaSlider', {\n        static: true\n      }]\n    }],\n    handleEsc: [{\n      type: HostListener,\n      args: ['document:keyup.esc', ['$event']]\n    }],\n    handleEnter: [{\n      type: HostListener,\n      args: ['document:keyup.enter', ['$event']]\n    }]\n  });\n})();\n\n// Caretaker note: we have still left the `typeof` condition in order to avoid\n// creating a breaking change for projects that still use the View Engine.\n// The `ngDevMode` is always available when Ivy is enabled.\n// This will be evaluated during compilation into `const NG_DEV_MODE = false`,\n// thus Terser will be able to tree-shake `console.warn` calls.\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\nclass ColorPickerDirective {\n  handleClick() {\n    this.inputFocus();\n  }\n  handleFocus() {\n    this.inputFocus();\n  }\n  handleInput(event) {\n    this.inputChange(event);\n  }\n  constructor(injector, cfr, appRef, vcRef, elRef, _service) {\n    _defineProperty(this, \"injector\", void 0);\n    _defineProperty(this, \"cfr\", void 0);\n    _defineProperty(this, \"appRef\", void 0);\n    _defineProperty(this, \"vcRef\", void 0);\n    _defineProperty(this, \"elRef\", void 0);\n    _defineProperty(this, \"_service\", void 0);\n    _defineProperty(this, \"dialog\", void 0);\n    _defineProperty(this, \"dialogCreated\", false);\n    _defineProperty(this, \"ignoreChanges\", false);\n    _defineProperty(this, \"cmpRef\", void 0);\n    _defineProperty(this, \"viewAttachedToAppRef\", false);\n    _defineProperty(this, \"colorPicker\", void 0);\n    _defineProperty(this, \"cpWidth\", '230px');\n    _defineProperty(this, \"cpHeight\", 'auto');\n    _defineProperty(this, \"cpToggle\", false);\n    _defineProperty(this, \"cpDisabled\", false);\n    _defineProperty(this, \"cpIgnoredElements\", []);\n    _defineProperty(this, \"cpFallbackColor\", '');\n    _defineProperty(this, \"cpColorMode\", 'color');\n    _defineProperty(this, \"cpCmykEnabled\", false);\n    _defineProperty(this, \"cpOutputFormat\", 'auto');\n    _defineProperty(this, \"cpAlphaChannel\", 'enabled');\n    _defineProperty(this, \"cpDisableInput\", false);\n    _defineProperty(this, \"cpDialogDisplay\", 'popup');\n    _defineProperty(this, \"cpSaveClickOutside\", true);\n    _defineProperty(this, \"cpCloseClickOutside\", true);\n    _defineProperty(this, \"cpUseRootViewContainer\", false);\n    _defineProperty(this, \"cpPosition\", 'auto');\n    _defineProperty(this, \"cpPositionOffset\", '0%');\n    _defineProperty(this, \"cpPositionRelativeToArrow\", false);\n    _defineProperty(this, \"cpOKButton\", false);\n    _defineProperty(this, \"cpOKButtonText\", 'OK');\n    _defineProperty(this, \"cpOKButtonClass\", 'cp-ok-button-class');\n    _defineProperty(this, \"cpCancelButton\", false);\n    _defineProperty(this, \"cpCancelButtonText\", 'Cancel');\n    _defineProperty(this, \"cpCancelButtonClass\", 'cp-cancel-button-class');\n    _defineProperty(this, \"cpEyeDropper\", false);\n    _defineProperty(this, \"cpPresetLabel\", 'Preset colors');\n    _defineProperty(this, \"cpPresetColors\", void 0);\n    _defineProperty(this, \"cpPresetColorsClass\", 'cp-preset-colors-class');\n    _defineProperty(this, \"cpMaxPresetColorsLength\", 6);\n    _defineProperty(this, \"cpPresetEmptyMessage\", 'No colors added');\n    _defineProperty(this, \"cpPresetEmptyMessageClass\", 'preset-empty-message');\n    _defineProperty(this, \"cpAddColorButton\", false);\n    _defineProperty(this, \"cpAddColorButtonText\", 'Add color');\n    _defineProperty(this, \"cpAddColorButtonClass\", 'cp-add-color-button-class');\n    _defineProperty(this, \"cpRemoveColorButtonClass\", 'cp-remove-color-button-class');\n    _defineProperty(this, \"cpArrowPosition\", 0);\n    _defineProperty(this, \"cpExtraTemplate\", void 0);\n    _defineProperty(this, \"cpInputChange\", new EventEmitter(true));\n    _defineProperty(this, \"cpToggleChange\", new EventEmitter(true));\n    _defineProperty(this, \"cpSliderChange\", new EventEmitter(true));\n    _defineProperty(this, \"cpSliderDragEnd\", new EventEmitter(true));\n    _defineProperty(this, \"cpSliderDragStart\", new EventEmitter(true));\n    _defineProperty(this, \"colorPickerOpen\", new EventEmitter(true));\n    _defineProperty(this, \"colorPickerClose\", new EventEmitter(true));\n    _defineProperty(this, \"colorPickerCancel\", new EventEmitter(true));\n    _defineProperty(this, \"colorPickerSelect\", new EventEmitter(true));\n    _defineProperty(this, \"colorPickerChange\", new EventEmitter(false));\n    _defineProperty(this, \"cpCmykColorChange\", new EventEmitter(true));\n    _defineProperty(this, \"cpPresetColorsChange\", new EventEmitter(true));\n    this.injector = injector;\n    this.cfr = cfr;\n    this.appRef = appRef;\n    this.vcRef = vcRef;\n    this.elRef = elRef;\n    this._service = _service;\n  }\n  ngOnDestroy() {\n    if (this.cmpRef != null) {\n      if (this.viewAttachedToAppRef) {\n        this.appRef.detachView(this.cmpRef.hostView);\n      }\n      this.cmpRef.destroy();\n      this.cmpRef = null;\n      this.dialog = null;\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes.cpToggle && !this.cpDisabled) {\n      if (changes.cpToggle.currentValue) {\n        this.openDialog();\n      } else if (!changes.cpToggle.currentValue) {\n        this.closeDialog();\n      }\n    }\n    if (changes.colorPicker) {\n      if (this.dialog && !this.ignoreChanges) {\n        if (this.cpDialogDisplay === 'inline') {\n          this.dialog.setInitialColor(changes.colorPicker.currentValue);\n        }\n        this.dialog.setColorFromString(changes.colorPicker.currentValue, false);\n        if (this.cpUseRootViewContainer && this.cpDialogDisplay !== 'inline') {\n          this.cmpRef.changeDetectorRef.detectChanges();\n        }\n      }\n      this.ignoreChanges = false;\n    }\n    if (changes.cpPresetLabel || changes.cpPresetColors) {\n      if (this.dialog) {\n        this.dialog.setPresetConfig(this.cpPresetLabel, this.cpPresetColors);\n      }\n    }\n  }\n  openDialog() {\n    if (!this.dialogCreated) {\n      let vcRef = this.vcRef;\n      this.dialogCreated = true;\n      this.viewAttachedToAppRef = false;\n      if (this.cpUseRootViewContainer && this.cpDialogDisplay !== 'inline') {\n        const classOfRootComponent = this.appRef.componentTypes[0];\n        const appInstance = this.injector.get(classOfRootComponent, Injector.NULL);\n        if (appInstance !== Injector.NULL) {\n          vcRef = appInstance.vcRef || appInstance.viewContainerRef || this.vcRef;\n          if (NG_DEV_MODE && vcRef === this.vcRef) {\n            console.warn('You are using cpUseRootViewContainer, ' + 'but the root component is not exposing viewContainerRef!' + 'Please expose it by adding \\'public vcRef: ViewContainerRef\\' to the constructor.');\n          }\n        } else {\n          this.viewAttachedToAppRef = true;\n        }\n      }\n      const compFactory = this.cfr.resolveComponentFactory(ColorPickerComponent);\n      if (this.viewAttachedToAppRef) {\n        this.cmpRef = compFactory.create(this.injector);\n        this.appRef.attachView(this.cmpRef.hostView);\n        document.body.appendChild(this.cmpRef.hostView.rootNodes[0]);\n      } else {\n        const injector = Injector.create({\n          providers: [],\n          // We shouldn't use `vcRef.parentInjector` since it's been deprecated long time ago and might be removed\n          // in newer Angular versions: https://github.com/angular/angular/pull/25174.\n          parent: vcRef.injector\n        });\n        this.cmpRef = vcRef.createComponent(compFactory, 0, injector, []);\n      }\n      this.cmpRef.instance.setupDialog(this, this.elRef, this.colorPicker, this.cpWidth, this.cpHeight, this.cpDialogDisplay, this.cpFallbackColor, this.cpColorMode, this.cpCmykEnabled, this.cpAlphaChannel, this.cpOutputFormat, this.cpDisableInput, this.cpIgnoredElements, this.cpSaveClickOutside, this.cpCloseClickOutside, this.cpUseRootViewContainer, this.cpPosition, this.cpPositionOffset, this.cpPositionRelativeToArrow, this.cpPresetLabel, this.cpPresetColors, this.cpPresetColorsClass, this.cpMaxPresetColorsLength, this.cpPresetEmptyMessage, this.cpPresetEmptyMessageClass, this.cpOKButton, this.cpOKButtonClass, this.cpOKButtonText, this.cpCancelButton, this.cpCancelButtonClass, this.cpCancelButtonText, this.cpAddColorButton, this.cpAddColorButtonClass, this.cpAddColorButtonText, this.cpRemoveColorButtonClass, this.cpEyeDropper, this.elRef, this.cpExtraTemplate);\n      this.dialog = this.cmpRef.instance;\n      if (this.vcRef !== vcRef) {\n        this.cmpRef.changeDetectorRef.detectChanges();\n      }\n    } else if (this.dialog) {\n      this.dialog.openDialog(this.colorPicker);\n    }\n  }\n  closeDialog() {\n    if (this.dialog && this.cpDialogDisplay === 'popup') {\n      this.dialog.closeDialog();\n    }\n  }\n  cmykChanged(value) {\n    this.cpCmykColorChange.emit(value);\n  }\n  stateChanged(state) {\n    this.cpToggleChange.emit(state);\n    if (state) {\n      this.colorPickerOpen.emit(this.colorPicker);\n    } else {\n      this.colorPickerClose.emit(this.colorPicker);\n    }\n  }\n  colorChanged(value, ignore = true) {\n    this.ignoreChanges = ignore;\n    this.colorPickerChange.emit(value);\n  }\n  colorSelected(value) {\n    this.colorPickerSelect.emit(value);\n  }\n  colorCanceled() {\n    this.colorPickerCancel.emit();\n  }\n  inputFocus() {\n    const element = this.elRef.nativeElement;\n    const ignored = this.cpIgnoredElements.filter(item => item === element);\n    if (!this.cpDisabled && !ignored.length) {\n      if (typeof document !== 'undefined' && element === document.activeElement) {\n        this.openDialog();\n      } else if (!this.dialog || !this.dialog.show) {\n        this.openDialog();\n      } else {\n        this.closeDialog();\n      }\n    }\n  }\n  inputChange(event) {\n    if (this.dialog) {\n      this.dialog.setColorFromString(event.target.value, true);\n    } else {\n      this.colorPicker = event.target.value;\n      this.colorPickerChange.emit(this.colorPicker);\n    }\n  }\n  inputChanged(event) {\n    this.cpInputChange.emit(event);\n  }\n  sliderChanged(event) {\n    this.cpSliderChange.emit(event);\n  }\n  sliderDragEnd(event) {\n    this.cpSliderDragEnd.emit(event);\n  }\n  sliderDragStart(event) {\n    this.cpSliderDragStart.emit(event);\n  }\n  presetColorsChanged(value) {\n    this.cpPresetColorsChange.emit(value);\n  }\n}\n_ColorPickerDirective = ColorPickerDirective;\n_defineProperty(ColorPickerDirective, \"\\u0275fac\", function _ColorPickerDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ColorPickerDirective)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ApplicationRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ColorPickerService));\n});\n_defineProperty(ColorPickerDirective, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _ColorPickerDirective,\n  selectors: [[\"\", \"colorPicker\", \"\"]],\n  hostBindings: function _ColorPickerDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _ColorPickerDirective_click_HostBindingHandler() {\n        return ctx.handleClick();\n      })(\"focus\", function _ColorPickerDirective_focus_HostBindingHandler() {\n        return ctx.handleFocus();\n      })(\"input\", function _ColorPickerDirective_input_HostBindingHandler($event) {\n        return ctx.handleInput($event);\n      });\n    }\n  },\n  inputs: {\n    colorPicker: \"colorPicker\",\n    cpWidth: \"cpWidth\",\n    cpHeight: \"cpHeight\",\n    cpToggle: \"cpToggle\",\n    cpDisabled: \"cpDisabled\",\n    cpIgnoredElements: \"cpIgnoredElements\",\n    cpFallbackColor: \"cpFallbackColor\",\n    cpColorMode: \"cpColorMode\",\n    cpCmykEnabled: \"cpCmykEnabled\",\n    cpOutputFormat: \"cpOutputFormat\",\n    cpAlphaChannel: \"cpAlphaChannel\",\n    cpDisableInput: \"cpDisableInput\",\n    cpDialogDisplay: \"cpDialogDisplay\",\n    cpSaveClickOutside: \"cpSaveClickOutside\",\n    cpCloseClickOutside: \"cpCloseClickOutside\",\n    cpUseRootViewContainer: \"cpUseRootViewContainer\",\n    cpPosition: \"cpPosition\",\n    cpPositionOffset: \"cpPositionOffset\",\n    cpPositionRelativeToArrow: \"cpPositionRelativeToArrow\",\n    cpOKButton: \"cpOKButton\",\n    cpOKButtonText: \"cpOKButtonText\",\n    cpOKButtonClass: \"cpOKButtonClass\",\n    cpCancelButton: \"cpCancelButton\",\n    cpCancelButtonText: \"cpCancelButtonText\",\n    cpCancelButtonClass: \"cpCancelButtonClass\",\n    cpEyeDropper: \"cpEyeDropper\",\n    cpPresetLabel: \"cpPresetLabel\",\n    cpPresetColors: \"cpPresetColors\",\n    cpPresetColorsClass: \"cpPresetColorsClass\",\n    cpMaxPresetColorsLength: \"cpMaxPresetColorsLength\",\n    cpPresetEmptyMessage: \"cpPresetEmptyMessage\",\n    cpPresetEmptyMessageClass: \"cpPresetEmptyMessageClass\",\n    cpAddColorButton: \"cpAddColorButton\",\n    cpAddColorButtonText: \"cpAddColorButtonText\",\n    cpAddColorButtonClass: \"cpAddColorButtonClass\",\n    cpRemoveColorButtonClass: \"cpRemoveColorButtonClass\",\n    cpArrowPosition: \"cpArrowPosition\",\n    cpExtraTemplate: \"cpExtraTemplate\"\n  },\n  outputs: {\n    cpInputChange: \"cpInputChange\",\n    cpToggleChange: \"cpToggleChange\",\n    cpSliderChange: \"cpSliderChange\",\n    cpSliderDragEnd: \"cpSliderDragEnd\",\n    cpSliderDragStart: \"cpSliderDragStart\",\n    colorPickerOpen: \"colorPickerOpen\",\n    colorPickerClose: \"colorPickerClose\",\n    colorPickerCancel: \"colorPickerCancel\",\n    colorPickerSelect: \"colorPickerSelect\",\n    colorPickerChange: \"colorPickerChange\",\n    cpCmykColorChange: \"cpCmykColorChange\",\n    cpPresetColorsChange: \"cpPresetColorsChange\"\n  },\n  exportAs: [\"ngxColorPicker\"],\n  standalone: false,\n  features: [i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColorPickerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[colorPicker]',\n      exportAs: 'ngxColorPicker'\n    }]\n  }], () => [{\n    type: i0.Injector\n  }, {\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: i0.ApplicationRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: ColorPickerService\n  }], {\n    colorPicker: [{\n      type: Input\n    }],\n    cpWidth: [{\n      type: Input\n    }],\n    cpHeight: [{\n      type: Input\n    }],\n    cpToggle: [{\n      type: Input\n    }],\n    cpDisabled: [{\n      type: Input\n    }],\n    cpIgnoredElements: [{\n      type: Input\n    }],\n    cpFallbackColor: [{\n      type: Input\n    }],\n    cpColorMode: [{\n      type: Input\n    }],\n    cpCmykEnabled: [{\n      type: Input\n    }],\n    cpOutputFormat: [{\n      type: Input\n    }],\n    cpAlphaChannel: [{\n      type: Input\n    }],\n    cpDisableInput: [{\n      type: Input\n    }],\n    cpDialogDisplay: [{\n      type: Input\n    }],\n    cpSaveClickOutside: [{\n      type: Input\n    }],\n    cpCloseClickOutside: [{\n      type: Input\n    }],\n    cpUseRootViewContainer: [{\n      type: Input\n    }],\n    cpPosition: [{\n      type: Input\n    }],\n    cpPositionOffset: [{\n      type: Input\n    }],\n    cpPositionRelativeToArrow: [{\n      type: Input\n    }],\n    cpOKButton: [{\n      type: Input\n    }],\n    cpOKButtonText: [{\n      type: Input\n    }],\n    cpOKButtonClass: [{\n      type: Input\n    }],\n    cpCancelButton: [{\n      type: Input\n    }],\n    cpCancelButtonText: [{\n      type: Input\n    }],\n    cpCancelButtonClass: [{\n      type: Input\n    }],\n    cpEyeDropper: [{\n      type: Input\n    }],\n    cpPresetLabel: [{\n      type: Input\n    }],\n    cpPresetColors: [{\n      type: Input\n    }],\n    cpPresetColorsClass: [{\n      type: Input\n    }],\n    cpMaxPresetColorsLength: [{\n      type: Input\n    }],\n    cpPresetEmptyMessage: [{\n      type: Input\n    }],\n    cpPresetEmptyMessageClass: [{\n      type: Input\n    }],\n    cpAddColorButton: [{\n      type: Input\n    }],\n    cpAddColorButtonText: [{\n      type: Input\n    }],\n    cpAddColorButtonClass: [{\n      type: Input\n    }],\n    cpRemoveColorButtonClass: [{\n      type: Input\n    }],\n    cpArrowPosition: [{\n      type: Input\n    }],\n    cpExtraTemplate: [{\n      type: Input\n    }],\n    cpInputChange: [{\n      type: Output\n    }],\n    cpToggleChange: [{\n      type: Output\n    }],\n    cpSliderChange: [{\n      type: Output\n    }],\n    cpSliderDragEnd: [{\n      type: Output\n    }],\n    cpSliderDragStart: [{\n      type: Output\n    }],\n    colorPickerOpen: [{\n      type: Output\n    }],\n    colorPickerClose: [{\n      type: Output\n    }],\n    colorPickerCancel: [{\n      type: Output\n    }],\n    colorPickerSelect: [{\n      type: Output\n    }],\n    colorPickerChange: [{\n      type: Output\n    }],\n    cpCmykColorChange: [{\n      type: Output\n    }],\n    cpPresetColorsChange: [{\n      type: Output\n    }],\n    handleClick: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    handleFocus: [{\n      type: HostListener,\n      args: ['focus']\n    }],\n    handleInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }]\n  });\n})();\nclass ColorPickerModule {}\n_ColorPickerModule = ColorPickerModule;\n_defineProperty(ColorPickerModule, \"\\u0275fac\", function _ColorPickerModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ColorPickerModule)();\n});\n_defineProperty(ColorPickerModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _ColorPickerModule,\n  declarations: [ColorPickerComponent, ColorPickerDirective, TextDirective, SliderDirective],\n  imports: [CommonModule],\n  exports: [ColorPickerDirective]\n}));\n_defineProperty(ColorPickerModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [ColorPickerService],\n  imports: [CommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColorPickerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [ColorPickerDirective],\n      providers: [ColorPickerService],\n      declarations: [ColorPickerComponent, ColorPickerDirective, TextDirective, SliderDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Cmyk, ColorPickerComponent, ColorPickerDirective, ColorPickerModule, ColorPickerService, Hsla, Hsva, Rgba, SliderDirective, TextDirective };\n//# sourceMappingURL=ngx-color-picker.mjs.map", "map": {"version": 3, "names": ["i0", "ɵɵelement", "rf", "ctx_r1", "ɵɵnextContext", "ɵɵclassMapInterpolate1", "cpUsePosition", "ɵɵstyleProp", "cpArrowPosition", "arrowTop", "_ColorPickerComponent_div_3_Template", "ctx", "_r3", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "_ColorPickerComponent_div_3_Template_div_newValue_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "onColorChange", "_ColorPickerComponent_div_3_Template_div_dragStart_0_listener", "onDragStart", "_ColorPickerComponent_div_3_Template_div_dragEnd_0_listener", "onDragEnd", "ɵɵelementEnd", "hueSliderColor", "ɵɵproperty", "ɵɵadvance", "slider", "v", "s", "_ColorPickerComponent__svg_svg_8_Template", "ɵɵnamespaceSVG", "_ColorPickerComponent_button_9_Template", "_r4", "_ColorPickerComponent_button_9_Template_button_click_0_listener", "onAddPresetColor", "selectedColor", "ɵɵtext", "ɵɵclassMap", "cpAddColorButtonClass", "cpPresetColors", "length", "cpMaxPresetColorsLength", "ɵɵtextInterpolate1", "cpAddColorButtonText", "_ColorPickerComponent_div_11_Template", "_ColorPickerComponent_div_21_input_6_Template", "_r6", "_ColorPickerComponent_div_21_input_6_Template_input_keyup_enter_0_listener", "onAcceptColor", "_ColorPickerComponent_div_21_input_6_Template_input_newValue_0_listener", "onAlphaInput", "cmykText", "a", "_ColorPickerComponent_div_21_div_16_Template", "_ColorPickerComponent_div_21_Template", "_r5", "_ColorPickerComponent_div_21_Template_input_keyup_enter_2_listener", "_ColorPickerComponent_div_21_Template_input_newValue_2_listener", "onCyanInput", "_ColorPickerComponent_div_21_Template_input_keyup_enter_3_listener", "_ColorPickerComponent_div_21_Template_input_newValue_3_listener", "onMagentaInput", "_ColorPickerComponent_div_21_Template_input_keyup_enter_4_listener", "_ColorPickerComponent_div_21_Template_input_newValue_4_listener", "onYellowInput", "_ColorPickerComponent_div_21_Template_input_keyup_enter_5_listener", "_ColorPickerComponent_div_21_Template_input_newValue_5_listener", "onBlackInput", "ɵɵtemplate", "format", "c", "m", "y", "k", "cpAlphaChannel", "_ColorPickerComponent_div_22_input_5_Template", "_r8", "_ColorPickerComponent_div_22_input_5_Template_input_keyup_enter_0_listener", "_ColorPickerComponent_div_22_input_5_Template_input_newValue_0_listener", "hslaText", "_ColorPickerComponent_div_22_div_13_Template", "_ColorPickerComponent_div_22_Template", "_r7", "_ColorPickerComponent_div_22_Template_input_keyup_enter_2_listener", "_ColorPickerComponent_div_22_Template_input_newValue_2_listener", "onHueInput", "_ColorPickerComponent_div_22_Template_input_keyup_enter_3_listener", "_ColorPickerComponent_div_22_Template_input_newValue_3_listener", "onSaturationInput", "_ColorPickerComponent_div_22_Template_input_keyup_enter_4_listener", "_ColorPickerComponent_div_22_Template_input_newValue_4_listener", "onLightnessInput", "h", "l", "_ColorPickerComponent_div_23_input_5_Template", "_r10", "_ColorPickerComponent_div_23_input_5_Template_input_keyup_enter_0_listener", "_ColorPickerComponent_div_23_input_5_Template_input_newValue_0_listener", "rgbaText", "_ColorPickerComponent_div_23_div_13_Template", "_ColorPickerComponent_div_23_Template", "_r9", "_ColorPickerComponent_div_23_Template_input_keyup_enter_2_listener", "_ColorPickerComponent_div_23_Template_input_newValue_2_listener", "onRedInput", "_ColorPickerComponent_div_23_Template_input_keyup_enter_3_listener", "_ColorPickerComponent_div_23_Template_input_newValue_3_listener", "onGreenInput", "_ColorPickerComponent_div_23_Template_input_keyup_enter_4_listener", "_ColorPickerComponent_div_23_Template_input_newValue_4_listener", "onBlueInput", "r", "g", "b", "_ColorPickerComponent_div_24_input_3_Template", "_r12", "_ColorPickerComponent_div_24_input_3_Template_input_keyup_enter_0_listener", "_ColorPickerComponent_div_24_input_3_Template_input_newValue_0_listener", "hexAlpha", "_ColorPickerComponent_div_24_div_7_Template", "_ColorPickerComponent_div_24_Template", "_r11", "_ColorPickerComponent_div_24_Template_input_blur_2_listener", "onHexInput", "_ColorPickerComponent_div_24_Template_input_keyup_enter_2_listener", "_ColorPickerComponent_div_24_Template_input_newValue_2_listener", "ɵɵclassProp", "hexText", "_ColorPickerComponent_div_25_input_3_Template", "_r14", "_ColorPickerComponent_div_25_input_3_Template_input_keyup_enter_0_listener", "_ColorPickerComponent_div_25_input_3_Template_input_newValue_0_listener", "_ColorPickerComponent_div_25_Template", "_r13", "_ColorPickerComponent_div_25_Template_input_keyup_enter_2_listener", "_ColorPickerComponent_div_25_Template_input_newValue_2_listener", "onValueInput", "_ColorPickerComponent_div_26_Template", "_r15", "_ColorPickerComponent_div_26_Template_span_click_1_listener", "onFormatToggle", "_ColorPickerComponent_div_26_Template_span_click_2_listener", "_ColorPickerComponent_div_27_div_4_div_1_span_1_Template", "_r18", "_ColorPickerComponent_div_27_div_4_div_1_span_1_Template_span_click_0_listener", "color_r17", "$implicit", "onRemovePresetColor", "cpRemoveColorButtonClass", "_ColorPickerComponent_div_27_div_4_div_1_Template", "_r16", "_ColorPickerComponent_div_27_div_4_div_1_Template_div_click_0_listener", "setColorFromString", "cpAddColorButton", "_ColorPickerComponent_div_27_div_4_Template", "cpPresetColorsClass", "_ColorPickerComponent_div_27_div_5_Template", "cpPresetEmptyMessageClass", "ɵɵtextInterpolate", "cpPresetEmptyMessage", "_ColorPickerComponent_div_27_Template", "cpPresetLabel", "_ColorPickerComponent_div_28_button_1_Template", "_r19", "_ColorPickerComponent_div_28_button_1_Template_button_click_0_listener", "onCancelColor", "cpCancelButtonClass", "cpCancelButtonText", "_ColorPickerComponent_div_28_button_2_Template", "_r20", "_ColorPickerComponent_div_28_button_2_Template_button_click_0_listener", "cpOKButtonClass", "cpOKButtonText", "_ColorPickerComponent_div_28_Template", "cpCancelButton", "cpOK<PERSON><PERSON>on", "_ColorPickerComponent_div_29_ng_container_1_Template", "ɵɵelementContainer", "_ColorPickerComponent_div_29_Template", "cpExtraTemplate", "EventEmitter", "Directive", "Input", "Output", "HostListener", "Injectable", "PLATFORM_ID", "Component", "ViewEncapsulation", "Inject", "ViewChild", "Injector", "NgModule", "i2", "isPlatformBrowser", "DOCUMENT", "CommonModule", "ColorFormats", "Rgba", "constructor", "_defineProperty", "Hsva", "Hsla", "Cmyk", "calculateAutoPositioning", "elBounds", "triggerElBounds", "usePositionX", "usePositionY", "height", "width", "top", "left", "bottom", "right", "collisionTop", "collisionBottom", "window", "innerHeight", "document", "documentElement", "clientHeight", "collisionLeft", "collisionRight", "innerWidth", "clientWidth", "collisionAll", "postions", "reduce", "prev", "next", "detectIE", "ua", "navigator", "userAgent", "toLowerCase", "msie", "indexOf", "parseInt", "substring", "TextDirective", "inputChange", "event", "value", "target", "rg", "undefined", "newValue", "emit", "numeric", "parseFloat", "_TextDirective", "_TextDirective_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "hostBindings", "_TextDirective_HostBindings", "_TextDirective_input_HostBindingHandler", "inputs", "text", "outputs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "SliderDirective", "mouseDown", "start", "touchStart", "elRef", "listenerMove", "move", "listenerStop", "stop", "preventDefault", "setCursor", "stopPropagation", "addEventListener", "dragStart", "removeEventListener", "dragEnd", "getX", "position", "nativeElement", "getBoundingClientRect", "pageX", "touches", "pageXOffset", "getY", "pageY", "pageYOffset", "offsetWidth", "offsetHeight", "x", "Math", "max", "min", "rgX", "rgY", "_SliderDirective", "_SliderDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "_SliderDirective_HostBindings", "_SliderDirective_mousedown_HostBindingHandler", "_SliderDirective_touchstart_HostBindingHandler", "SliderPosition", "SliderDimension", "ColorPickerService", "setActive", "active", "cpDialogDisplay", "closeDialog", "hsva2hsla", "hsva", "abs", "hsla2hsva", "hsla", "hsvaToRgba", "i", "floor", "f", "p", "q", "t", "cmykToRgb", "cmyk", "rgbaToCmyk", "rgba", "rgbaToHsva", "d", "rgbaToHex", "allowHex8", "hex", "toString", "substr", "round", "normalizeCMYK", "denormalizeCMYK", "denormalizeRGBA", "stringToHsva", "colorString", "stringParsers", "re", "parse", "execResult", "isNaN", "push", "key", "hasOwnProperty", "parser", "match", "exec", "color", "outputFormat", "alphaChannel", "_ColorPickerService", "_ColorPickerService_Factory", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "SUPPORTS_TOUCH", "ColorPickerComponent", "handleEsc", "show", "handleEnter", "ngZone", "cdRef", "platformId", "service", "HEX", "RGBA", "HSLA", "CMYK", "eyeDropperSupported", "defaultView", "ngOnInit", "hue<PERSON><PERSON><PERSON>", "hue<PERSON><PERSON><PERSON>", "alphaWidth", "alphaSlider", "sliderDimMax", "cp<PERSON>idth", "cpCmykEnabled", "cpOutputFormat", "listenerMouseDown", "onMouseDown", "listenerResize", "onResize", "openDialog", "initialColor", "ngOnDestroy", "ngAfterViewInit", "updateColorPicker", "detectChanges", "directiveElementRef", "setInitialColor", "openColorPicker", "closeColorPicker", "setupDialog", "instance", "elementRef", "cpHeight", "cpFallbackColor", "cpColorMode", "cpDisableInput", "cpIgnoredElements", "cpSaveClickOutside", "cpCloseClickOutside", "cpUseRootViewContainer", "cpPosition", "cpPositionOffset", "cpPositionRelativeToArrow", "cpEyeDropper", "cpTriggerElement", "setColorMode", "isIE10", "directiveInstance", "useRootViewContainer", "fallbackColor", "setPresetConfig", "dialogArrowOffset", "dialogArrowSize", "mode", "toUpperCase", "update", "sliderH", "setDialogPosition", "sliderDragEnd", "outputColor", "sliderDragStart", "isDescendant", "filter", "item", "run", "colorSelected", "cmykChanged", "cmykColor", "colorChanged", "colorCanceled", "onEyeDropper", "eyeDropper", "EyeDropper", "open", "then", "eyeDropperResult", "sRGBHex", "change", "availableFormats", "dialogInputFields", "nextFormat", "slider<PERSON><PERSON>ed", "onHueChange", "onValueChange", "onAlphaChange", "validHex", "valid", "test", "split", "map", "join", "inputChanged", "input", "concat", "presetColorsChanged", "hidden", "setTimeout", "stateChanged", "runOutsideAngular", "cmykInput", "hue", "lastOutput", "alphaSliderColor", "alpha", "transform", "style", "parentNode", "transformNode", "node", "dialogHeight", "dialogElement", "tagName", "getComputedStyle", "getPropertyValue", "boxDirective", "createDialogBox", "HTMLUnknownElement", "boxParent", "usePosition", "dialogBounds", "triggerBounds", "windowInnerHeight", "windowInnerWidth", "elRefClientRect", "parent", "child", "element", "offset", "_ColorPickerComponent", "_ColorPickerComponent_Factory", "NgZone", "ChangeDetectorRef", "ɵɵdefineComponent", "viewQuery", "_ColorPickerComponent_Query", "ɵɵviewQuery", "_c0", "_c1", "_c2", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "_ColorPickerComponent_HostBindings", "_ColorPickerComponent_keyup_esc_HostBindingHandler", "ɵɵresolveDocument", "_ColorPickerComponent_keyup_enter_HostBindingHandler", "decls", "vars", "consts", "template", "_ColorPickerComponent_Template", "_r1", "_ColorPickerComponent_Template_div_click_0_listener", "_ColorPickerComponent_div_2_Template", "_ColorPickerComponent_Template_div_click_7_listener", "_ColorPickerComponent_Template_div_newValue_12_listener", "_ColorPickerComponent_Template_div_dragStart_12_listener", "_ColorPickerComponent_Template_div_dragEnd_12_listener", "_ColorPickerComponent_Template_div_newValue_15_listener", "_ColorPickerComponent_Template_div_dragStart_15_listener", "_ColorPickerComponent_Template_div_dragEnd_15_listener", "_ColorPickerComponent_Template_div_newValue_18_listener", "_ColorPickerComponent_Template_div_dragStart_18_listener", "_ColorPickerComponent_Template_div_dragEnd_18_listener", "dependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "styles", "encapsulation", "None", "Document", "decorators", "static", "NG_DEV_MODE", "ColorPickerDirective", "handleClick", "inputFocus", "handleFocus", "handleInput", "injector", "cfr", "appRef", "vcRef", "_service", "cmpRef", "viewAttachedToAppRef", "detach<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "dialog", "ngOnChanges", "changes", "cpToggle", "cpDisabled", "currentValue", "colorPicker", "ignore<PERSON><PERSON>es", "changeDetectorRef", "dialogCreated", "classOfRootComponent", "componentTypes", "appInstance", "get", "NULL", "viewContainerRef", "console", "warn", "compFactory", "resolveComponentFactory", "create", "attachView", "body", "append<PERSON><PERSON><PERSON>", "rootNodes", "providers", "createComponent", "cpCmykColorChange", "state", "cpToggleChange", "colorPickerOpen", "colorPickerClose", "ignore", "colorPickerChange", "colorPickerSelect", "colorPickerCancel", "ignored", "activeElement", "cpInputChange", "cpSliderChange", "cpSliderDragEnd", "cpSliderDragStart", "cpPresetColorsChange", "_ColorPickerDirective", "_ColorPickerDirective_Factory", "ComponentFactoryResolver", "ApplicationRef", "ViewContainerRef", "_ColorPickerDirective_HostBindings", "_ColorPickerDirective_click_HostBindingHandler", "_ColorPickerDirective_focus_HostBindingHandler", "_ColorPickerDirective_input_HostBindingHandler", "exportAs", "features", "ɵɵNgOnChangesFeature", "ColorPickerModule", "_ColorPickerModule", "_ColorPickerModule_Factory", "ɵɵdefineNgModule", "declarations", "imports", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/ngx-color-picker/fesm2022/ngx-color-picker.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, HostListener, Injectable, PLATFORM_ID, Component, ViewEncapsulation, Inject, ViewChild, Injector, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\n\nvar ColorFormats;\n(function (ColorFormats) {\n    ColorFormats[ColorFormats[\"HEX\"] = 0] = \"HEX\";\n    ColorFormats[ColorFormats[\"RGBA\"] = 1] = \"RGBA\";\n    ColorFormats[ColorFormats[\"HSLA\"] = 2] = \"HSLA\";\n    ColorFormats[ColorFormats[\"CMYK\"] = 3] = \"CMYK\";\n})(ColorFormats || (ColorFormats = {}));\nclass Rgba {\n    r;\n    g;\n    b;\n    a;\n    constructor(r, g, b, a) {\n        this.r = r;\n        this.g = g;\n        this.b = b;\n        this.a = a;\n    }\n}\nclass Hsva {\n    h;\n    s;\n    v;\n    a;\n    constructor(h, s, v, a) {\n        this.h = h;\n        this.s = s;\n        this.v = v;\n        this.a = a;\n    }\n}\nclass Hsla {\n    h;\n    s;\n    l;\n    a;\n    constructor(h, s, l, a) {\n        this.h = h;\n        this.s = s;\n        this.l = l;\n        this.a = a;\n    }\n}\nclass Cmyk {\n    c;\n    m;\n    y;\n    k;\n    a;\n    constructor(c, m, y, k, a = 1) {\n        this.c = c;\n        this.m = m;\n        this.y = y;\n        this.k = k;\n        this.a = a;\n    }\n}\n\nfunction calculateAutoPositioning(elBounds, triggerElBounds) {\n    // Defaults\n    let usePositionX = 'right';\n    let usePositionY = 'bottom';\n    // Calculate collisions\n    const { height, width } = elBounds;\n    const { top, left } = triggerElBounds;\n    const bottom = top + triggerElBounds.height;\n    const right = left + triggerElBounds.width;\n    const collisionTop = top - height < 0;\n    const collisionBottom = bottom + height > (window.innerHeight || document.documentElement.clientHeight);\n    const collisionLeft = left - width < 0;\n    const collisionRight = right + width > (window.innerWidth || document.documentElement.clientWidth);\n    const collisionAll = collisionTop && collisionBottom && collisionLeft && collisionRight;\n    // Generate X & Y position values\n    if (collisionBottom) {\n        usePositionY = 'top';\n    }\n    if (collisionTop) {\n        usePositionY = 'bottom';\n    }\n    if (collisionLeft) {\n        usePositionX = 'right';\n    }\n    if (collisionRight) {\n        usePositionX = 'left';\n    }\n    // Choose the largest gap available\n    if (collisionAll) {\n        const postions = ['left', 'right', 'top', 'bottom'];\n        return postions.reduce((prev, next) => elBounds[prev] > elBounds[next] ? prev : next);\n    }\n    if ((collisionLeft && collisionRight)) {\n        if (collisionTop) {\n            return 'bottom';\n        }\n        if (collisionBottom) {\n            return 'top';\n        }\n        return top > bottom ? 'top' : 'bottom';\n    }\n    if ((collisionTop && collisionBottom)) {\n        if (collisionLeft) {\n            return 'right';\n        }\n        if (collisionRight) {\n            return 'left';\n        }\n        return left > right ? 'left' : 'right';\n    }\n    return `${usePositionY}-${usePositionX}`;\n}\nfunction detectIE() {\n    let ua = '';\n    if (typeof navigator !== 'undefined') {\n        ua = navigator.userAgent.toLowerCase();\n    }\n    const msie = ua.indexOf('msie ');\n    if (msie > 0) {\n        // IE 10 or older => return version number\n        return parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);\n    }\n    // Other browser\n    return false;\n}\nclass TextDirective {\n    rg;\n    text;\n    newValue = new EventEmitter();\n    inputChange(event) {\n        const value = event.target.value;\n        if (this.rg === undefined) {\n            this.newValue.emit(value);\n        }\n        else {\n            const numeric = parseFloat(value);\n            this.newValue.emit({ v: numeric, rg: this.rg });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: TextDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.2\", type: TextDirective, selector: \"[text]\", inputs: { rg: \"rg\", text: \"text\" }, outputs: { newValue: \"newValue\" }, host: { listeners: { \"input\": \"inputChange($event)\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: TextDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[text]'\n                }]\n        }], propDecorators: { rg: [{\n                type: Input\n            }], text: [{\n                type: Input\n            }], newValue: [{\n                type: Output\n            }], inputChange: [{\n                type: HostListener,\n                args: ['input', ['$event']]\n            }] } });\nclass SliderDirective {\n    elRef;\n    listenerMove;\n    listenerStop;\n    rgX;\n    rgY;\n    slider;\n    dragEnd = new EventEmitter();\n    dragStart = new EventEmitter();\n    newValue = new EventEmitter();\n    mouseDown(event) {\n        this.start(event);\n    }\n    touchStart(event) {\n        this.start(event);\n    }\n    constructor(elRef) {\n        this.elRef = elRef;\n        this.listenerMove = (event) => this.move(event);\n        this.listenerStop = () => this.stop();\n    }\n    move(event) {\n        event.preventDefault();\n        this.setCursor(event);\n    }\n    start(event) {\n        this.setCursor(event);\n        event.stopPropagation();\n        document.addEventListener('mouseup', this.listenerStop);\n        document.addEventListener('touchend', this.listenerStop);\n        document.addEventListener('mousemove', this.listenerMove);\n        document.addEventListener('touchmove', this.listenerMove);\n        this.dragStart.emit();\n    }\n    stop() {\n        document.removeEventListener('mouseup', this.listenerStop);\n        document.removeEventListener('touchend', this.listenerStop);\n        document.removeEventListener('mousemove', this.listenerMove);\n        document.removeEventListener('touchmove', this.listenerMove);\n        this.dragEnd.emit();\n    }\n    getX(event) {\n        const position = this.elRef.nativeElement.getBoundingClientRect();\n        const pageX = (event.pageX !== undefined) ? event.pageX : event.touches[0].pageX;\n        return pageX - position.left - window.pageXOffset;\n    }\n    getY(event) {\n        const position = this.elRef.nativeElement.getBoundingClientRect();\n        const pageY = (event.pageY !== undefined) ? event.pageY : event.touches[0].pageY;\n        return pageY - position.top - window.pageYOffset;\n    }\n    setCursor(event) {\n        const width = this.elRef.nativeElement.offsetWidth;\n        const height = this.elRef.nativeElement.offsetHeight;\n        const x = Math.max(0, Math.min(this.getX(event), width));\n        const y = Math.max(0, Math.min(this.getY(event), height));\n        if (this.rgX !== undefined && this.rgY !== undefined) {\n            this.newValue.emit({ s: x / width, v: (1 - y / height), rgX: this.rgX, rgY: this.rgY });\n        }\n        else if (this.rgX === undefined && this.rgY !== undefined) {\n            this.newValue.emit({ v: y / height, rgY: this.rgY });\n        }\n        else if (this.rgX !== undefined && this.rgY === undefined) {\n            this.newValue.emit({ v: x / width, rgX: this.rgX });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: SliderDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.2\", type: SliderDirective, selector: \"[slider]\", inputs: { rgX: \"rgX\", rgY: \"rgY\", slider: \"slider\" }, outputs: { dragEnd: \"dragEnd\", dragStart: \"dragStart\", newValue: \"newValue\" }, host: { listeners: { \"mousedown\": \"mouseDown($event)\", \"touchstart\": \"touchStart($event)\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: SliderDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[slider]'\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { rgX: [{\n                type: Input\n            }], rgY: [{\n                type: Input\n            }], slider: [{\n                type: Input\n            }], dragEnd: [{\n                type: Output\n            }], dragStart: [{\n                type: Output\n            }], newValue: [{\n                type: Output\n            }], mouseDown: [{\n                type: HostListener,\n                args: ['mousedown', ['$event']]\n            }], touchStart: [{\n                type: HostListener,\n                args: ['touchstart', ['$event']]\n            }] } });\nclass SliderPosition {\n    h;\n    s;\n    v;\n    a;\n    constructor(h, s, v, a) {\n        this.h = h;\n        this.s = s;\n        this.v = v;\n        this.a = a;\n    }\n}\nclass SliderDimension {\n    h;\n    s;\n    v;\n    a;\n    constructor(h, s, v, a) {\n        this.h = h;\n        this.s = s;\n        this.v = v;\n        this.a = a;\n    }\n}\n\nclass ColorPickerService {\n    active = null;\n    setActive(active) {\n        if (this.active && this.active !== active && this.active.cpDialogDisplay !== 'inline') {\n            this.active.closeDialog();\n        }\n        this.active = active;\n    }\n    hsva2hsla(hsva) {\n        const h = hsva.h, s = hsva.s, v = hsva.v, a = hsva.a;\n        if (v === 0) {\n            return new Hsla(h, 0, 0, a);\n        }\n        else if (s === 0 && v === 1) {\n            return new Hsla(h, 1, 1, a);\n        }\n        else {\n            const l = v * (2 - s) / 2;\n            return new Hsla(h, v * s / (1 - Math.abs(2 * l - 1)), l, a);\n        }\n    }\n    hsla2hsva(hsla) {\n        const h = Math.min(hsla.h, 1), s = Math.min(hsla.s, 1);\n        const l = Math.min(hsla.l, 1), a = Math.min(hsla.a, 1);\n        if (l === 0) {\n            return new Hsva(h, 0, 0, a);\n        }\n        else {\n            const v = l + s * (1 - Math.abs(2 * l - 1)) / 2;\n            return new Hsva(h, 2 * (v - l) / v, v, a);\n        }\n    }\n    hsvaToRgba(hsva) {\n        let r, g, b;\n        const h = hsva.h, s = hsva.s, v = hsva.v, a = hsva.a;\n        const i = Math.floor(h * 6);\n        const f = h * 6 - i;\n        const p = v * (1 - s);\n        const q = v * (1 - f * s);\n        const t = v * (1 - (1 - f) * s);\n        switch (i % 6) {\n            case 0:\n                r = v, g = t, b = p;\n                break;\n            case 1:\n                r = q, g = v, b = p;\n                break;\n            case 2:\n                r = p, g = v, b = t;\n                break;\n            case 3:\n                r = p, g = q, b = v;\n                break;\n            case 4:\n                r = t, g = p, b = v;\n                break;\n            case 5:\n                r = v, g = p, b = q;\n                break;\n            default:\n                r = 0, g = 0, b = 0;\n        }\n        return new Rgba(r, g, b, a);\n    }\n    cmykToRgb(cmyk) {\n        const r = (1 - cmyk.c) * (1 - cmyk.k);\n        const g = (1 - cmyk.m) * (1 - cmyk.k);\n        const b = (1 - cmyk.y) * (1 - cmyk.k);\n        return new Rgba(r, g, b, cmyk.a);\n    }\n    rgbaToCmyk(rgba) {\n        const k = 1 - Math.max(rgba.r, rgba.g, rgba.b);\n        if (k === 1) {\n            return new Cmyk(0, 0, 0, 1, rgba.a);\n        }\n        else {\n            const c = (1 - rgba.r - k) / (1 - k);\n            const m = (1 - rgba.g - k) / (1 - k);\n            const y = (1 - rgba.b - k) / (1 - k);\n            return new Cmyk(c, m, y, k, rgba.a);\n        }\n    }\n    rgbaToHsva(rgba) {\n        let h, s;\n        const r = Math.min(rgba.r, 1), g = Math.min(rgba.g, 1);\n        const b = Math.min(rgba.b, 1), a = Math.min(rgba.a, 1);\n        const max = Math.max(r, g, b), min = Math.min(r, g, b);\n        const v = max, d = max - min;\n        s = (max === 0) ? 0 : d / max;\n        if (max === min) {\n            h = 0;\n        }\n        else {\n            switch (max) {\n                case r:\n                    h = (g - b) / d + (g < b ? 6 : 0);\n                    break;\n                case g:\n                    h = (b - r) / d + 2;\n                    break;\n                case b:\n                    h = (r - g) / d + 4;\n                    break;\n                default:\n                    h = 0;\n            }\n            h /= 6;\n        }\n        return new Hsva(h, s, v, a);\n    }\n    rgbaToHex(rgba, allowHex8) {\n        /* eslint-disable no-bitwise */\n        let hex = '#' + ((1 << 24) | (rgba.r << 16) | (rgba.g << 8) | rgba.b).toString(16).substr(1);\n        if (allowHex8) {\n            hex += ((1 << 8) | Math.round(rgba.a * 255)).toString(16).substr(1);\n        }\n        /* eslint-enable no-bitwise */\n        return hex;\n    }\n    normalizeCMYK(cmyk) {\n        return new Cmyk(cmyk.c / 100, cmyk.m / 100, cmyk.y / 100, cmyk.k / 100, cmyk.a);\n    }\n    denormalizeCMYK(cmyk) {\n        return new Cmyk(Math.floor(cmyk.c * 100), Math.floor(cmyk.m * 100), Math.floor(cmyk.y * 100), Math.floor(cmyk.k * 100), cmyk.a);\n    }\n    denormalizeRGBA(rgba) {\n        return new Rgba(Math.round(rgba.r * 255), Math.round(rgba.g * 255), Math.round(rgba.b * 255), rgba.a);\n    }\n    stringToHsva(colorString = '', allowHex8 = false) {\n        let hsva = null;\n        colorString = (colorString || '').toLowerCase();\n        const stringParsers = [\n            {\n                re: /(rgb)a?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*%?,\\s*(\\d{1,3})\\s*%?(?:,\\s*(\\d+(?:\\.\\d+)?)\\s*)?\\)/,\n                parse: function (execResult) {\n                    return new Rgba(parseInt(execResult[2], 10) / 255, parseInt(execResult[3], 10) / 255, parseInt(execResult[4], 10) / 255, isNaN(parseFloat(execResult[5])) ? 1 : parseFloat(execResult[5]));\n                }\n            }, {\n                re: /(hsl)a?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})%\\s*,\\s*(\\d{1,3})%\\s*(?:,\\s*(\\d+(?:\\.\\d+)?)\\s*)?\\)/,\n                parse: function (execResult) {\n                    return new Hsla(parseInt(execResult[2], 10) / 360, parseInt(execResult[3], 10) / 100, parseInt(execResult[4], 10) / 100, isNaN(parseFloat(execResult[5])) ? 1 : parseFloat(execResult[5]));\n                }\n            }\n        ];\n        if (allowHex8) {\n            stringParsers.push({\n                re: /#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})?$/,\n                parse: function (execResult) {\n                    return new Rgba(parseInt(execResult[1], 16) / 255, parseInt(execResult[2], 16) / 255, parseInt(execResult[3], 16) / 255, parseInt(execResult[4] || 'FF', 16) / 255);\n                }\n            });\n        }\n        else {\n            stringParsers.push({\n                re: /#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})$/,\n                parse: function (execResult) {\n                    return new Rgba(parseInt(execResult[1], 16) / 255, parseInt(execResult[2], 16) / 255, parseInt(execResult[3], 16) / 255, 1);\n                }\n            });\n        }\n        stringParsers.push({\n            re: /#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])$/,\n            parse: function (execResult) {\n                return new Rgba(parseInt(execResult[1] + execResult[1], 16) / 255, parseInt(execResult[2] + execResult[2], 16) / 255, parseInt(execResult[3] + execResult[3], 16) / 255, 1);\n            }\n        });\n        for (const key in stringParsers) {\n            if (stringParsers.hasOwnProperty(key)) {\n                const parser = stringParsers[key];\n                const match = parser.re.exec(colorString), color = match && parser.parse(match);\n                if (color) {\n                    if (color instanceof Rgba) {\n                        hsva = this.rgbaToHsva(color);\n                    }\n                    else if (color instanceof Hsla) {\n                        hsva = this.hsla2hsva(color);\n                    }\n                    return hsva;\n                }\n            }\n        }\n        return hsva;\n    }\n    outputFormat(hsva, outputFormat, alphaChannel) {\n        if (outputFormat === 'auto') {\n            outputFormat = hsva.a < 1 ? 'rgba' : 'hex';\n        }\n        switch (outputFormat) {\n            case 'hsla':\n                const hsla = this.hsva2hsla(hsva);\n                const hslaText = new Hsla(Math.round((hsla.h) * 360), Math.round(hsla.s * 100), Math.round(hsla.l * 100), Math.round(hsla.a * 100) / 100);\n                if (hsva.a < 1 || alphaChannel === 'always') {\n                    return 'hsla(' + hslaText.h + ',' + hslaText.s + '%,' + hslaText.l + '%,' +\n                        hslaText.a + ')';\n                }\n                else {\n                    return 'hsl(' + hslaText.h + ',' + hslaText.s + '%,' + hslaText.l + '%)';\n                }\n            case 'rgba':\n                const rgba = this.denormalizeRGBA(this.hsvaToRgba(hsva));\n                if (hsva.a < 1 || alphaChannel === 'always') {\n                    return 'rgba(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ',' +\n                        Math.round(rgba.a * 100) / 100 + ')';\n                }\n                else {\n                    return 'rgb(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ')';\n                }\n            default:\n                const allowHex8 = (alphaChannel === 'always' || alphaChannel === 'forced');\n                return this.rgbaToHex(this.denormalizeRGBA(this.hsvaToRgba(hsva)), allowHex8);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerService, decorators: [{\n            type: Injectable\n        }] });\n\n// Do not store that on the class instance since the condition will be run\n// every time the class is created.\nconst SUPPORTS_TOUCH = typeof window !== 'undefined' && 'ontouchstart' in window;\nclass ColorPickerComponent {\n    ngZone;\n    elRef;\n    cdRef;\n    document;\n    platformId;\n    service;\n    isIE10 = false;\n    cmyk;\n    hsva;\n    width;\n    height;\n    cmykColor;\n    outputColor;\n    initialColor;\n    fallbackColor;\n    listenerResize;\n    listenerMouseDown;\n    directiveInstance;\n    sliderH;\n    sliderDimMax;\n    directiveElementRef;\n    dialogArrowSize = 10;\n    dialogArrowOffset = 15;\n    dialogInputFields = [\n        ColorFormats.HEX,\n        ColorFormats.RGBA,\n        ColorFormats.HSLA,\n        ColorFormats.CMYK\n    ];\n    useRootViewContainer = false;\n    show;\n    hidden;\n    top;\n    left;\n    position;\n    format;\n    slider;\n    hexText;\n    hexAlpha;\n    cmykText;\n    hslaText;\n    rgbaText;\n    arrowTop;\n    selectedColor;\n    hueSliderColor;\n    alphaSliderColor;\n    cpWidth;\n    cpHeight;\n    cpColorMode;\n    cpCmykEnabled;\n    cpAlphaChannel;\n    cpOutputFormat;\n    cpDisableInput;\n    cpDialogDisplay;\n    cpIgnoredElements;\n    cpSaveClickOutside;\n    cpCloseClickOutside;\n    cpPosition;\n    cpUsePosition;\n    cpPositionOffset;\n    cpOKButton;\n    cpOKButtonText;\n    cpOKButtonClass;\n    cpCancelButton;\n    cpCancelButtonText;\n    cpCancelButtonClass;\n    cpEyeDropper;\n    eyeDropperSupported;\n    cpPresetLabel;\n    cpPresetColors;\n    cpPresetColorsClass;\n    cpMaxPresetColorsLength;\n    cpPresetEmptyMessage;\n    cpPresetEmptyMessageClass;\n    cpAddColorButton;\n    cpAddColorButtonText;\n    cpAddColorButtonClass;\n    cpRemoveColorButtonClass;\n    cpArrowPosition;\n    cpTriggerElement;\n    cpExtraTemplate;\n    dialogElement;\n    hueSlider;\n    alphaSlider;\n    handleEsc(event) {\n        if (this.show && this.cpDialogDisplay === 'popup') {\n            this.onCancelColor(event);\n        }\n    }\n    handleEnter(event) {\n        if (this.show && this.cpDialogDisplay === 'popup') {\n            this.onAcceptColor(event);\n        }\n    }\n    constructor(ngZone, elRef, cdRef, document, platformId, service) {\n        this.ngZone = ngZone;\n        this.elRef = elRef;\n        this.cdRef = cdRef;\n        this.document = document;\n        this.platformId = platformId;\n        this.service = service;\n        this.eyeDropperSupported = isPlatformBrowser(this.platformId) && 'EyeDropper' in this.document.defaultView;\n    }\n    ngOnInit() {\n        this.slider = new SliderPosition(0, 0, 0, 0);\n        const hueWidth = this.hueSlider.nativeElement.offsetWidth || 140;\n        const alphaWidth = this.alphaSlider.nativeElement.offsetWidth || 140;\n        this.sliderDimMax = new SliderDimension(hueWidth, this.cpWidth, 130, alphaWidth);\n        if (this.cpCmykEnabled) {\n            this.format = ColorFormats.CMYK;\n        }\n        else if (this.cpOutputFormat === 'rgba') {\n            this.format = ColorFormats.RGBA;\n        }\n        else if (this.cpOutputFormat === 'hsla') {\n            this.format = ColorFormats.HSLA;\n        }\n        else {\n            this.format = ColorFormats.HEX;\n        }\n        this.listenerMouseDown = (event) => { this.onMouseDown(event); };\n        this.listenerResize = () => { this.onResize(); };\n        this.openDialog(this.initialColor, false);\n    }\n    ngOnDestroy() {\n        this.closeDialog();\n    }\n    ngAfterViewInit() {\n        if (this.cpWidth !== 230 || this.cpDialogDisplay === 'inline') {\n            const hueWidth = this.hueSlider.nativeElement.offsetWidth || 140;\n            const alphaWidth = this.alphaSlider.nativeElement.offsetWidth || 140;\n            this.sliderDimMax = new SliderDimension(hueWidth, this.cpWidth, 130, alphaWidth);\n            this.updateColorPicker(false);\n            this.cdRef.detectChanges();\n        }\n    }\n    openDialog(color, emit = true) {\n        this.service.setActive(this);\n        if (!this.width) {\n            this.cpWidth = this.directiveElementRef.nativeElement.offsetWidth;\n        }\n        if (!this.height) {\n            this.height = 320;\n        }\n        this.setInitialColor(color);\n        this.setColorFromString(color, emit);\n        this.openColorPicker();\n    }\n    closeDialog() {\n        this.closeColorPicker();\n    }\n    setupDialog(instance, elementRef, color, cpWidth, cpHeight, cpDialogDisplay, cpFallbackColor, cpColorMode, cpCmykEnabled, cpAlphaChannel, cpOutputFormat, cpDisableInput, cpIgnoredElements, cpSaveClickOutside, cpCloseClickOutside, cpUseRootViewContainer, cpPosition, cpPositionOffset, cpPositionRelativeToArrow, cpPresetLabel, cpPresetColors, cpPresetColorsClass, cpMaxPresetColorsLength, cpPresetEmptyMessage, cpPresetEmptyMessageClass, cpOKButton, cpOKButtonClass, cpOKButtonText, cpCancelButton, cpCancelButtonClass, cpCancelButtonText, cpAddColorButton, cpAddColorButtonClass, cpAddColorButtonText, cpRemoveColorButtonClass, cpEyeDropper, cpTriggerElement, cpExtraTemplate) {\n        this.setInitialColor(color);\n        this.setColorMode(cpColorMode);\n        this.isIE10 = (detectIE() === 10);\n        this.directiveInstance = instance;\n        this.directiveElementRef = elementRef;\n        this.cpDisableInput = cpDisableInput;\n        this.cpCmykEnabled = cpCmykEnabled;\n        this.cpAlphaChannel = cpAlphaChannel;\n        this.cpOutputFormat = cpOutputFormat;\n        this.cpDialogDisplay = cpDialogDisplay;\n        this.cpIgnoredElements = cpIgnoredElements;\n        this.cpSaveClickOutside = cpSaveClickOutside;\n        this.cpCloseClickOutside = cpCloseClickOutside;\n        this.useRootViewContainer = cpUseRootViewContainer;\n        this.width = this.cpWidth = parseInt(cpWidth, 10);\n        this.height = this.cpHeight = parseInt(cpHeight, 10);\n        this.cpPosition = cpPosition;\n        this.cpPositionOffset = parseInt(cpPositionOffset, 10);\n        this.cpOKButton = cpOKButton;\n        this.cpOKButtonText = cpOKButtonText;\n        this.cpOKButtonClass = cpOKButtonClass;\n        this.cpCancelButton = cpCancelButton;\n        this.cpCancelButtonText = cpCancelButtonText;\n        this.cpCancelButtonClass = cpCancelButtonClass;\n        this.cpEyeDropper = cpEyeDropper;\n        this.fallbackColor = cpFallbackColor || '#fff';\n        this.setPresetConfig(cpPresetLabel, cpPresetColors);\n        this.cpPresetColorsClass = cpPresetColorsClass;\n        this.cpMaxPresetColorsLength = cpMaxPresetColorsLength;\n        this.cpPresetEmptyMessage = cpPresetEmptyMessage;\n        this.cpPresetEmptyMessageClass = cpPresetEmptyMessageClass;\n        this.cpAddColorButton = cpAddColorButton;\n        this.cpAddColorButtonText = cpAddColorButtonText;\n        this.cpAddColorButtonClass = cpAddColorButtonClass;\n        this.cpRemoveColorButtonClass = cpRemoveColorButtonClass;\n        this.cpTriggerElement = cpTriggerElement;\n        this.cpExtraTemplate = cpExtraTemplate;\n        if (!cpPositionRelativeToArrow) {\n            this.dialogArrowOffset = 0;\n        }\n        if (cpDialogDisplay === 'inline') {\n            this.dialogArrowSize = 0;\n            this.dialogArrowOffset = 0;\n        }\n        if (cpOutputFormat === 'hex' &&\n            cpAlphaChannel !== 'always' && cpAlphaChannel !== 'forced') {\n            this.cpAlphaChannel = 'disabled';\n        }\n    }\n    setColorMode(mode) {\n        switch (mode.toString().toUpperCase()) {\n            case '1':\n            case 'C':\n            case 'COLOR':\n                this.cpColorMode = 1;\n                break;\n            case '2':\n            case 'G':\n            case 'GRAYSCALE':\n                this.cpColorMode = 2;\n                break;\n            case '3':\n            case 'P':\n            case 'PRESETS':\n                this.cpColorMode = 3;\n                break;\n            default:\n                this.cpColorMode = 1;\n        }\n    }\n    setInitialColor(color) {\n        this.initialColor = color;\n    }\n    setPresetConfig(cpPresetLabel, cpPresetColors) {\n        this.cpPresetLabel = cpPresetLabel;\n        this.cpPresetColors = cpPresetColors;\n    }\n    setColorFromString(value, emit = true, update = true) {\n        let hsva;\n        if (this.cpAlphaChannel === 'always' || this.cpAlphaChannel === 'forced') {\n            hsva = this.service.stringToHsva(value, true);\n            if (!hsva && !this.hsva) {\n                hsva = this.service.stringToHsva(value, false);\n            }\n        }\n        else {\n            hsva = this.service.stringToHsva(value, false);\n        }\n        if (!hsva && !this.hsva) {\n            hsva = this.service.stringToHsva(this.fallbackColor, false);\n        }\n        if (hsva) {\n            this.hsva = hsva;\n            this.sliderH = this.hsva.h;\n            if (this.cpOutputFormat === 'hex' && this.cpAlphaChannel === 'disabled') {\n                this.hsva.a = 1;\n            }\n            this.updateColorPicker(emit, update);\n        }\n    }\n    onResize() {\n        if (this.position === 'fixed') {\n            this.setDialogPosition();\n        }\n        else if (this.cpDialogDisplay !== 'inline') {\n            this.closeColorPicker();\n        }\n    }\n    onDragEnd(slider) {\n        this.directiveInstance.sliderDragEnd({ slider: slider, color: this.outputColor });\n    }\n    onDragStart(slider) {\n        this.directiveInstance.sliderDragStart({ slider: slider, color: this.outputColor });\n    }\n    onMouseDown(event) {\n        if (this.show &&\n            !this.isIE10 &&\n            this.cpDialogDisplay === 'popup' &&\n            event.target !== this.directiveElementRef.nativeElement &&\n            !this.isDescendant(this.elRef.nativeElement, event.target) &&\n            !this.isDescendant(this.directiveElementRef.nativeElement, event.target) &&\n            this.cpIgnoredElements.filter((item) => item === event.target).length === 0) {\n            this.ngZone.run(() => {\n                if (this.cpSaveClickOutside) {\n                    this.directiveInstance.colorSelected(this.outputColor);\n                }\n                else {\n                    this.hsva = null;\n                    this.setColorFromString(this.initialColor, false);\n                    if (this.cpCmykEnabled) {\n                        this.directiveInstance.cmykChanged(this.cmykColor);\n                    }\n                    this.directiveInstance.colorChanged(this.initialColor);\n                    this.directiveInstance.colorCanceled();\n                }\n                if (this.cpCloseClickOutside) {\n                    this.closeColorPicker();\n                }\n            });\n        }\n    }\n    onAcceptColor(event) {\n        event.stopPropagation();\n        if (this.outputColor) {\n            this.directiveInstance.colorSelected(this.outputColor);\n        }\n        if (this.cpDialogDisplay === 'popup') {\n            this.closeColorPicker();\n        }\n    }\n    onCancelColor(event) {\n        this.hsva = null;\n        event.stopPropagation();\n        this.directiveInstance.colorCanceled();\n        this.setColorFromString(this.initialColor, true);\n        if (this.cpDialogDisplay === 'popup') {\n            if (this.cpCmykEnabled) {\n                this.directiveInstance.cmykChanged(this.cmykColor);\n            }\n            this.directiveInstance.colorChanged(this.initialColor, true);\n            this.closeColorPicker();\n        }\n    }\n    onEyeDropper() {\n        if (!this.eyeDropperSupported)\n            return;\n        const eyeDropper = new window.EyeDropper();\n        eyeDropper.open().then((eyeDropperResult) => {\n            this.setColorFromString(eyeDropperResult.sRGBHex, true);\n        });\n    }\n    onFormatToggle(change) {\n        const availableFormats = this.dialogInputFields.length -\n            (this.cpCmykEnabled ? 0 : 1);\n        const nextFormat = (((this.dialogInputFields.indexOf(this.format) + change) %\n            availableFormats) + availableFormats) % availableFormats;\n        this.format = this.dialogInputFields[nextFormat];\n    }\n    onColorChange(value) {\n        this.hsva.s = value.s / value.rgX;\n        this.hsva.v = value.v / value.rgY;\n        this.updateColorPicker();\n        this.directiveInstance.sliderChanged({\n            slider: 'lightness',\n            value: this.hsva.v,\n            color: this.outputColor\n        });\n        this.directiveInstance.sliderChanged({\n            slider: 'saturation',\n            value: this.hsva.s,\n            color: this.outputColor\n        });\n    }\n    onHueChange(value) {\n        this.hsva.h = value.v / value.rgX;\n        this.sliderH = this.hsva.h;\n        this.updateColorPicker();\n        this.directiveInstance.sliderChanged({\n            slider: 'hue',\n            value: this.hsva.h,\n            color: this.outputColor\n        });\n    }\n    onValueChange(value) {\n        this.hsva.v = value.v / value.rgX;\n        this.updateColorPicker();\n        this.directiveInstance.sliderChanged({\n            slider: 'value',\n            value: this.hsva.v,\n            color: this.outputColor\n        });\n    }\n    onAlphaChange(value) {\n        this.hsva.a = value.v / value.rgX;\n        this.updateColorPicker();\n        this.directiveInstance.sliderChanged({\n            slider: 'alpha',\n            value: this.hsva.a,\n            color: this.outputColor\n        });\n    }\n    onHexInput(value) {\n        if (value === null) {\n            this.updateColorPicker();\n        }\n        else {\n            if (value && value[0] !== '#') {\n                value = '#' + value;\n            }\n            let validHex = /^#([a-f0-9]{3}|[a-f0-9]{6})$/gi;\n            if (this.cpAlphaChannel === 'always') {\n                validHex = /^#([a-f0-9]{3}|[a-f0-9]{6}|[a-f0-9]{8})$/gi;\n            }\n            const valid = validHex.test(value);\n            if (valid) {\n                if (value.length < 5) {\n                    value = '#' + value.substring(1)\n                        .split('')\n                        .map(c => c + c)\n                        .join('');\n                }\n                if (this.cpAlphaChannel === 'forced') {\n                    value += Math.round(this.hsva.a * 255).toString(16);\n                }\n                this.setColorFromString(value, true, false);\n            }\n            this.directiveInstance.inputChanged({\n                input: 'hex',\n                valid: valid,\n                value: value,\n                color: this.outputColor\n            });\n        }\n    }\n    onRedInput(value) {\n        const rgba = this.service.hsvaToRgba(this.hsva);\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            rgba.r = value.v / value.rg;\n            this.hsva = this.service.rgbaToHsva(rgba);\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'red',\n            valid: valid,\n            value: rgba.r,\n            color: this.outputColor\n        });\n    }\n    onBlueInput(value) {\n        const rgba = this.service.hsvaToRgba(this.hsva);\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            rgba.b = value.v / value.rg;\n            this.hsva = this.service.rgbaToHsva(rgba);\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'blue',\n            valid: valid,\n            value: rgba.b,\n            color: this.outputColor\n        });\n    }\n    onGreenInput(value) {\n        const rgba = this.service.hsvaToRgba(this.hsva);\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            rgba.g = value.v / value.rg;\n            this.hsva = this.service.rgbaToHsva(rgba);\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'green',\n            valid: valid,\n            value: rgba.g,\n            color: this.outputColor\n        });\n    }\n    onHueInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.hsva.h = value.v / value.rg;\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'hue',\n            valid: valid,\n            value: this.hsva.h,\n            color: this.outputColor\n        });\n    }\n    onValueInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.hsva.v = value.v / value.rg;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'value',\n            valid: valid,\n            value: this.hsva.v,\n            color: this.outputColor\n        });\n    }\n    onAlphaInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.hsva.a = value.v / value.rg;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'alpha',\n            valid: valid,\n            value: this.hsva.a,\n            color: this.outputColor\n        });\n    }\n    onLightnessInput(value) {\n        const hsla = this.service.hsva2hsla(this.hsva);\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            hsla.l = value.v / value.rg;\n            this.hsva = this.service.hsla2hsva(hsla);\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'lightness',\n            valid: valid,\n            value: hsla.l,\n            color: this.outputColor\n        });\n    }\n    onSaturationInput(value) {\n        const hsla = this.service.hsva2hsla(this.hsva);\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            hsla.s = value.v / value.rg;\n            this.hsva = this.service.hsla2hsva(hsla);\n            this.sliderH = this.hsva.h;\n            this.updateColorPicker();\n        }\n        this.directiveInstance.inputChanged({\n            input: 'saturation',\n            valid: valid,\n            value: hsla.s,\n            color: this.outputColor\n        });\n    }\n    onCyanInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.cmyk.c = value.v;\n            this.updateColorPicker(false, true, true);\n        }\n        this.directiveInstance.inputChanged({\n            input: 'cyan',\n            valid: true,\n            value: this.cmyk.c,\n            color: this.outputColor\n        });\n    }\n    onMagentaInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.cmyk.m = value.v;\n            this.updateColorPicker(false, true, true);\n        }\n        this.directiveInstance.inputChanged({\n            input: 'magenta',\n            valid: true,\n            value: this.cmyk.m,\n            color: this.outputColor\n        });\n    }\n    onYellowInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.cmyk.y = value.v;\n            this.updateColorPicker(false, true, true);\n        }\n        this.directiveInstance.inputChanged({\n            input: 'yellow',\n            valid: true,\n            value: this.cmyk.y,\n            color: this.outputColor\n        });\n    }\n    onBlackInput(value) {\n        const valid = !isNaN(value.v) && value.v >= 0 && value.v <= value.rg;\n        if (valid) {\n            this.cmyk.k = value.v;\n            this.updateColorPicker(false, true, true);\n        }\n        this.directiveInstance.inputChanged({\n            input: 'black',\n            valid: true,\n            value: this.cmyk.k,\n            color: this.outputColor\n        });\n    }\n    onAddPresetColor(event, value) {\n        event.stopPropagation();\n        if (!this.cpPresetColors.filter((color) => (color === value)).length) {\n            this.cpPresetColors = this.cpPresetColors.concat(value);\n            this.directiveInstance.presetColorsChanged(this.cpPresetColors);\n        }\n    }\n    onRemovePresetColor(event, value) {\n        event.stopPropagation();\n        this.cpPresetColors = this.cpPresetColors.filter((color) => (color !== value));\n        this.directiveInstance.presetColorsChanged(this.cpPresetColors);\n    }\n    // Private helper functions for the color picker dialog status\n    openColorPicker() {\n        if (!this.show) {\n            this.show = true;\n            this.hidden = true;\n            setTimeout(() => {\n                this.hidden = false;\n                this.setDialogPosition();\n                this.cdRef.detectChanges();\n            }, 0);\n            this.directiveInstance.stateChanged(true);\n            if (!this.isIE10) {\n                // The change detection should be run on `mousedown` event only when the condition\n                // is met within the `onMouseDown` method.\n                this.ngZone.runOutsideAngular(() => {\n                    // There's no sense to add both event listeners on touch devices since the `touchstart`\n                    // event is handled earlier than `mousedown`, so we'll get 2 change detections and the\n                    // second one will be unnecessary.\n                    if (SUPPORTS_TOUCH) {\n                        document.addEventListener('touchstart', this.listenerMouseDown);\n                    }\n                    else {\n                        document.addEventListener('mousedown', this.listenerMouseDown);\n                    }\n                });\n            }\n            window.addEventListener('resize', this.listenerResize);\n        }\n    }\n    closeColorPicker() {\n        if (this.show) {\n            this.show = false;\n            this.directiveInstance.stateChanged(false);\n            if (!this.isIE10) {\n                if (SUPPORTS_TOUCH) {\n                    document.removeEventListener('touchstart', this.listenerMouseDown);\n                }\n                else {\n                    document.removeEventListener('mousedown', this.listenerMouseDown);\n                }\n            }\n            window.removeEventListener('resize', this.listenerResize);\n            if (!this.cdRef['destroyed']) {\n                this.cdRef.detectChanges();\n            }\n        }\n    }\n    updateColorPicker(emit = true, update = true, cmykInput = false) {\n        if (this.sliderDimMax) {\n            if (this.cpColorMode === 2) {\n                this.hsva.s = 0;\n            }\n            let hue, hsla, rgba;\n            const lastOutput = this.outputColor;\n            hsla = this.service.hsva2hsla(this.hsva);\n            if (!this.cpCmykEnabled) {\n                rgba = this.service.denormalizeRGBA(this.service.hsvaToRgba(this.hsva));\n            }\n            else {\n                if (!cmykInput) {\n                    rgba = this.service.hsvaToRgba(this.hsva);\n                    this.cmyk = this.service.denormalizeCMYK(this.service.rgbaToCmyk(rgba));\n                }\n                else {\n                    rgba = this.service.cmykToRgb(this.service.normalizeCMYK(this.cmyk));\n                    this.hsva = this.service.rgbaToHsva(rgba);\n                }\n                rgba = this.service.denormalizeRGBA(rgba);\n                this.sliderH = this.hsva.h;\n            }\n            hue = this.service.denormalizeRGBA(this.service.hsvaToRgba(new Hsva(this.sliderH || this.hsva.h, 1, 1, 1)));\n            if (update) {\n                this.hslaText = new Hsla(Math.round((hsla.h) * 360), Math.round(hsla.s * 100), Math.round(hsla.l * 100), Math.round(hsla.a * 100) / 100);\n                this.rgbaText = new Rgba(rgba.r, rgba.g, rgba.b, Math.round(rgba.a * 100) / 100);\n                if (this.cpCmykEnabled) {\n                    this.cmykText = new Cmyk(this.cmyk.c, this.cmyk.m, this.cmyk.y, this.cmyk.k, Math.round(this.cmyk.a * 100) / 100);\n                }\n                const allowHex8 = this.cpAlphaChannel === 'always';\n                this.hexText = this.service.rgbaToHex(rgba, allowHex8);\n                this.hexAlpha = this.rgbaText.a;\n            }\n            if (this.cpOutputFormat === 'auto') {\n                if (this.format !== ColorFormats.RGBA && this.format !== ColorFormats.CMYK && this.format !== ColorFormats.HSLA) {\n                    if (this.hsva.a < 1) {\n                        this.format = this.hsva.a < 1 ? ColorFormats.RGBA : ColorFormats.HEX;\n                    }\n                }\n            }\n            this.hueSliderColor = 'rgb(' + hue.r + ',' + hue.g + ',' + hue.b + ')';\n            this.alphaSliderColor = 'rgb(' + rgba.r + ',' + rgba.g + ',' + rgba.b + ')';\n            this.outputColor = this.service.outputFormat(this.hsva, this.cpOutputFormat, this.cpAlphaChannel);\n            this.selectedColor = this.service.outputFormat(this.hsva, 'rgba', null);\n            if (this.format !== ColorFormats.CMYK) {\n                this.cmykColor = '';\n            }\n            else {\n                if (this.cpAlphaChannel === 'always' || this.cpAlphaChannel === 'enabled' ||\n                    this.cpAlphaChannel === 'forced') {\n                    const alpha = Math.round(this.cmyk.a * 100) / 100;\n                    this.cmykColor = `cmyka(${this.cmyk.c},${this.cmyk.m},${this.cmyk.y},${this.cmyk.k},${alpha})`;\n                }\n                else {\n                    this.cmykColor = `cmyk(${this.cmyk.c},${this.cmyk.m},${this.cmyk.y},${this.cmyk.k})`;\n                }\n            }\n            this.slider = new SliderPosition((this.sliderH || this.hsva.h) * this.sliderDimMax.h - 8, this.hsva.s * this.sliderDimMax.s - 8, (1 - this.hsva.v) * this.sliderDimMax.v - 8, this.hsva.a * this.sliderDimMax.a - 8);\n            if (emit && lastOutput !== this.outputColor) {\n                if (this.cpCmykEnabled) {\n                    this.directiveInstance.cmykChanged(this.cmykColor);\n                }\n                this.directiveInstance.colorChanged(this.outputColor);\n            }\n        }\n    }\n    // Private helper functions for the color picker dialog positioning\n    setDialogPosition() {\n        if (this.cpDialogDisplay === 'inline') {\n            this.position = 'relative';\n        }\n        else {\n            let position = 'static', transform = '', style;\n            let parentNode = null, transformNode = null;\n            let node = this.directiveElementRef.nativeElement.parentNode;\n            const dialogHeight = this.dialogElement.nativeElement.offsetHeight;\n            while (node !== null && node.tagName !== 'HTML') {\n                style = window.getComputedStyle(node);\n                position = style.getPropertyValue('position');\n                transform = style.getPropertyValue('transform');\n                if (position !== 'static' && parentNode === null) {\n                    parentNode = node;\n                }\n                if (transform && transform !== 'none' && transformNode === null) {\n                    transformNode = node;\n                }\n                if (position === 'fixed') {\n                    parentNode = transformNode;\n                    break;\n                }\n                node = node.parentNode;\n            }\n            const boxDirective = this.createDialogBox(this.directiveElementRef.nativeElement, (position !== 'fixed'));\n            if (this.useRootViewContainer || (position === 'fixed' &&\n                (!parentNode || parentNode instanceof HTMLUnknownElement))) {\n                this.top = boxDirective.top;\n                this.left = boxDirective.left;\n            }\n            else {\n                if (parentNode === null) {\n                    parentNode = node;\n                }\n                const boxParent = this.createDialogBox(parentNode, (position !== 'fixed'));\n                this.top = boxDirective.top - boxParent.top;\n                this.left = boxDirective.left - boxParent.left;\n            }\n            if (position === 'fixed') {\n                this.position = 'fixed';\n            }\n            let usePosition = this.cpPosition;\n            const dialogBounds = this.dialogElement.nativeElement.getBoundingClientRect();\n            if (this.cpPosition === 'auto') {\n                const triggerBounds = this.cpTriggerElement.nativeElement.getBoundingClientRect();\n                usePosition = calculateAutoPositioning(dialogBounds, triggerBounds);\n            }\n            this.arrowTop = usePosition === 'top'\n                ? dialogHeight - 1\n                : undefined;\n            this.cpArrowPosition = undefined;\n            switch (usePosition) {\n                case 'top':\n                    this.top -= dialogHeight + this.dialogArrowSize;\n                    this.left += this.cpPositionOffset / 100 * boxDirective.width - this.dialogArrowOffset;\n                    break;\n                case 'bottom':\n                    this.top += boxDirective.height + this.dialogArrowSize;\n                    this.left += this.cpPositionOffset / 100 * boxDirective.width - this.dialogArrowOffset;\n                    break;\n                case 'top-left':\n                case 'left-top':\n                    this.top -= dialogHeight - boxDirective.height + boxDirective.height * this.cpPositionOffset / 100;\n                    this.left -= this.cpWidth + this.dialogArrowSize - 2 - this.dialogArrowOffset;\n                    break;\n                case 'top-right':\n                case 'right-top':\n                    this.top -= dialogHeight - boxDirective.height + boxDirective.height * this.cpPositionOffset / 100;\n                    this.left += boxDirective.width + this.dialogArrowSize - 2 - this.dialogArrowOffset;\n                    break;\n                case 'left':\n                case 'bottom-left':\n                case 'left-bottom':\n                    this.top += boxDirective.height * this.cpPositionOffset / 100 - this.dialogArrowOffset;\n                    this.left -= this.cpWidth + this.dialogArrowSize - 2;\n                    break;\n                case 'right':\n                case 'bottom-right':\n                case 'right-bottom':\n                default:\n                    this.top += boxDirective.height * this.cpPositionOffset / 100 - this.dialogArrowOffset;\n                    this.left += boxDirective.width + this.dialogArrowSize - 2;\n                    break;\n            }\n            const windowInnerHeight = window.innerHeight;\n            const windowInnerWidth = window.innerWidth;\n            const elRefClientRect = this.elRef.nativeElement.getBoundingClientRect();\n            const bottom = this.top + dialogBounds.height;\n            if (bottom > windowInnerHeight) {\n                this.top = windowInnerHeight - dialogBounds.height;\n                this.cpArrowPosition = elRefClientRect.x / 2 - 20;\n            }\n            const right = this.left + dialogBounds.width;\n            if (right > windowInnerWidth) {\n                this.left = windowInnerWidth - dialogBounds.width;\n                this.cpArrowPosition = elRefClientRect.x / 2 - 20;\n            }\n            this.cpUsePosition = usePosition;\n        }\n    }\n    // Private helper functions for the color picker dialog positioning and opening\n    isDescendant(parent, child) {\n        let node = child.parentNode;\n        while (node !== null) {\n            if (node === parent) {\n                return true;\n            }\n            node = node.parentNode;\n        }\n        return false;\n    }\n    createDialogBox(element, offset) {\n        const { top, left } = element.getBoundingClientRect();\n        return {\n            top: top + (offset ? window.pageYOffset : 0),\n            left: left + (offset ? window.pageXOffset : 0),\n            width: element.offsetWidth,\n            height: element.offsetHeight\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerComponent, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: ColorPickerService }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.2\", type: ColorPickerComponent, selector: \"color-picker\", host: { listeners: { \"document:keyup.esc\": \"handleEsc($event)\", \"document:keyup.enter\": \"handleEnter($event)\" } }, viewQueries: [{ propertyName: \"dialogElement\", first: true, predicate: [\"dialogPopup\"], descendants: true, static: true }, { propertyName: \"hueSlider\", first: true, predicate: [\"hueSlider\"], descendants: true, static: true }, { propertyName: \"alphaSlider\", first: true, predicate: [\"alphaSlider\"], descendants: true, static: true }], ngImport: i0, template: \"<div #dialogPopup class=\\\"color-picker\\\" [class.open]=\\\"show\\\" [style.display]=\\\"!show ? 'none' : 'block'\\\" [style.visibility]=\\\"hidden ? 'hidden' : 'visible'\\\" [style.top.px]=\\\"top\\\" [style.left.px]=\\\"left\\\" [style.position]=\\\"position\\\" [style.height.px]=\\\"cpHeight\\\" [style.width.px]=\\\"cpWidth\\\" (click)=\\\"$event.stopPropagation()\\\">\\n  <div *ngIf=\\\"cpDialogDisplay === 'popup'\\\" [style.left]=\\\"cpArrowPosition\\\" class=\\\"arrow arrow-{{cpUsePosition}}\\\" [style.top.px]=\\\"arrowTop\\\"></div>\\n\\n  <div *ngIf=\\\"(cpColorMode ||\\u00A01) === 1\\\" class=\\\"saturation-lightness\\\" [slider] [rgX]=\\\"1\\\" [rgY]=\\\"1\\\" [style.background-color]=\\\"hueSliderColor\\\" (newValue)=\\\"onColorChange($event)\\\" (dragStart)=\\\"onDragStart('saturation-lightness')\\\" (dragEnd)=\\\"onDragEnd('saturation-lightness')\\\">\\n    <div class=\\\"cursor\\\" [style.top.px]=\\\"slider?.v\\\" [style.left.px]=\\\"slider?.s\\\"></div>\\n  </div>\\n\\n  <div class=\\\"hue-alpha box\\\">\\n    <div class=\\\"left\\\">\\n      <div class=\\\"selected-color-background\\\"></div>\\n\\n      <div class=\\\"selected-color\\\" [style.background-color]=\\\"selectedColor\\\" [style.cursor]=\\\"eyeDropperSupported && cpEyeDropper ? 'pointer' : null\\\" (click)=\\\"eyeDropperSupported && cpEyeDropper && onEyeDropper()\\\">\\n        <svg *ngIf=\\\"eyeDropperSupported && cpEyeDropper\\\" class=\\\"eyedropper-icon\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" height=\\\"24px\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" fill=\\\"#000000\\\"><path d=\\\"M0 0h24v24H0V0z\\\" fill=\\\"none\\\"/><path d=\\\"M17.66 5.41l.92.92-2.69 2.69-.92-.92 2.69-2.69M17.67 3c-.26 0-.51.1-.71.29l-3.12 3.12-1.93-1.91-1.41 1.41 1.42 1.42L3 16.25V21h4.75l8.92-8.92 1.42 1.42 1.41-1.41-1.92-1.92 3.12-3.12c.4-.4.4-1.03.01-1.42l-2.34-2.34c-.2-.19-.45-.29-.7-.29zM6.92 19L5 17.08l8.06-8.06 1.92 1.92L6.92 19z\\\"/></svg>\\n      </div>\\n\\n      <button *ngIf=\\\"cpAddColorButton\\\" type=\\\"button\\\" class=\\\"{{cpAddColorButtonClass}}\\\" [disabled]=\\\"cpPresetColors && cpPresetColors.length >= cpMaxPresetColorsLength\\\" (click)=\\\"onAddPresetColor($event, selectedColor)\\\">\\n        {{cpAddColorButtonText}}\\n      </button>\\n    </div>\\n\\n    <div class=\\\"right\\\">\\n      <div *ngIf=\\\"cpAlphaChannel==='disabled'\\\" style=\\\"height: 16px;\\\"></div>\\n\\n      <div #hueSlider class=\\\"hue\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 1 ? 'block' : 'none'\\\" (newValue)=\\\"onHueChange($event)\\\" (dragStart)=\\\"onDragStart('hue')\\\" (dragEnd)=\\\"onDragEnd('hue')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.h\\\"></div>\\n      </div>\\n\\n      <div #valueSlider class=\\\"value\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 2 ? 'block': 'none'\\\" (newValue)=\\\"onValueChange($event)\\\" (dragStart)=\\\"onDragStart('value')\\\" (dragEnd)=\\\"onDragEnd('value')\\\">\\n        <div class=\\\"cursor\\\" [style.right.px]=\\\"slider?.v\\\"></div>\\n      </div>\\n\\n      <div #alphaSlider class=\\\"alpha\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"cpAlphaChannel === 'disabled' ? 'none' : 'block'\\\" [style.background-color]=\\\"alphaSliderColor\\\" (newValue)=\\\"onAlphaChange($event)\\\" (dragStart)=\\\"onDragStart('alpha')\\\" (dragEnd)=\\\"onDragEnd('alpha')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.a\\\"></div>\\n      </div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"cmyk-text\\\" [style.display]=\\\"format !== 3 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.c\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onCyanInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.m\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onMagentaInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.y\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onYellowInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.k\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlackInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"cmykText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n     <div class=\\\"box\\\">\\n      <div>C</div><div>M</div><div>Y</div><div>K</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" class=\\\"hsla-text\\\" [style.display]=\\\"format !== 2 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"360\\\" [text] [rg]=\\\"360\\\" [value]=\\\"hslaText?.h\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHueInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.s\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onSaturationInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onLightnessInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>H</div><div>S</div><div>L</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" [style.display]=\\\"format !== 1 ? 'none' : 'block'\\\" class=\\\"rgba-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.r\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onRedInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.g\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onGreenInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.b\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"rgbaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>R</div><div>G</div><div>B</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"hex-text\\\" [class.hex-alpha]=\\\"cpAlphaChannel==='forced'\\\"\\n    [style.display]=\\\"format !== 0 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input [text] [value]=\\\"hexText\\\" (blur)=\\\"onHexInput(null)\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHexInput($event)\\\"/>\\n      <input *ngIf=\\\"cpAlphaChannel==='forced'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hexAlpha\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\"/>\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>Hex</div>\\n      <div *ngIf=\\\"cpAlphaChannel==='forced'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 2\\\" class=\\\"value-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onValueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\"  [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>V</div><div>A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"type-policy\\\">\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(-1)\\\"></span>\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(1)\\\"></span>\\n  </div>\\n\\n  <div *ngIf=\\\"cpPresetColors?.length || cpAddColorButton\\\" class=\\\"preset-area\\\">\\n    <hr>\\n\\n    <div class=\\\"preset-label\\\">{{cpPresetLabel}}</div>\\n\\n    <div *ngIf=\\\"cpPresetColors?.length\\\" class=\\\"{{cpPresetColorsClass}}\\\">\\n      <div *ngFor=\\\"let color of cpPresetColors\\\" class=\\\"preset-color\\\" [style.backgroundColor]=\\\"color\\\" (click)=\\\"setColorFromString(color)\\\">\\n        <span *ngIf=\\\"cpAddColorButton\\\" class=\\\"{{cpRemoveColorButtonClass}}\\\" (click)=\\\"onRemovePresetColor($event, color)\\\"></span>\\n      </div>\\n    </div>\\n\\n    <div *ngIf=\\\"!cpPresetColors?.length && cpAddColorButton\\\" class=\\\"{{cpPresetEmptyMessageClass}}\\\">{{cpPresetEmptyMessage}}</div>\\n  </div>\\n\\n  <div *ngIf=\\\"cpOKButton || cpCancelButton\\\" class=\\\"button-area\\\">\\n    <button *ngIf=\\\"cpCancelButton\\\" type=\\\"button\\\" class=\\\"{{cpCancelButtonClass}}\\\" (click)=\\\"onCancelColor($event)\\\">{{cpCancelButtonText}}</button>\\n\\n    <button *ngIf=\\\"cpOKButton\\\" type=\\\"button\\\" class=\\\"{{cpOKButtonClass}}\\\" (click)=\\\"onAcceptColor($event)\\\">{{cpOKButtonText}}</button>\\n  </div>\\n\\n  <div class=\\\"extra-template\\\" *ngIf=\\\"cpExtraTemplate\\\">\\n    <ng-container *ngTemplateOutlet=\\\"cpExtraTemplate\\\"></ng-container>\\n  </div>\\n</div>\\n\", styles: [\".color-picker{position:absolute;z-index:1000;width:230px;height:auto;border:#777 solid 1px;cursor:default;-webkit-user-select:none;user-select:none;background-color:#fff}.color-picker *{box-sizing:border-box;margin:0;font-size:11px}.color-picker input{width:0;height:26px;min-width:0;font-size:13px;text-align:center;color:#000}.color-picker input:invalid,.color-picker input:-moz-ui-invalid,.color-picker input:-moz-submit-invalid{box-shadow:none}.color-picker input::-webkit-inner-spin-button,.color-picker input::-webkit-outer-spin-button{margin:0;-webkit-appearance:none}.color-picker .arrow{position:absolute;z-index:999999;width:0;height:0;border-style:solid}.color-picker .arrow.arrow-top{left:8px;border-width:10px 5px;border-color:#777 rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-bottom{top:-20px;left:8px;border-width:10px 5px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) #777 rgba(0,0,0,0)}.color-picker .arrow.arrow-top-left,.color-picker .arrow.arrow-left-top{right:-21px;bottom:8px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-top-right,.color-picker .arrow.arrow-right-top{bottom:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-left,.color-picker .arrow.arrow-left-bottom,.color-picker .arrow.arrow-bottom-left{top:8px;right:-21px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-right,.color-picker .arrow.arrow-right-bottom,.color-picker .arrow.arrow-bottom-right{top:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .cursor{position:relative;width:16px;height:16px;border:#222 solid 2px;border-radius:50%;cursor:default}.color-picker .box{display:flex;padding:4px 8px}.color-picker .left{position:relative;padding:16px 8px}.color-picker .right{flex:1 1 auto;padding:12px 8px}.color-picker .button-area{padding:0 16px 16px;text-align:right}.color-picker .button-area button{margin-left:8px}.color-picker .preset-area{padding:4px 15px}.color-picker .preset-area .preset-label{overflow:hidden;width:100%;padding:4px;font-size:11px;white-space:nowrap;text-align:left;text-overflow:ellipsis;color:#555}.color-picker .preset-area .preset-color{position:relative;display:inline-block;width:18px;height:18px;margin:4px 6px 8px;border:#a9a9a9 solid 1px;border-radius:25%;cursor:pointer}.color-picker .preset-area .preset-empty-message{min-height:18px;margin-top:4px;margin-bottom:8px;font-style:italic;text-align:center}.color-picker .hex-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .hex-text .box{padding:0 24px 8px 8px}.color-picker .hex-text .box div{float:left;flex:1 1 auto;text-align:center;color:#555;clear:left}.color-picker .hex-text .box input{flex:1 1 auto;padding:1px;border:#a9a9a9 solid 1px}.color-picker .hex-alpha .box div:first-child,.color-picker .hex-alpha .box input:first-child{flex-grow:3;margin-right:8px}.color-picker .cmyk-text,.color-picker .hsla-text,.color-picker .rgba-text,.color-picker .value-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .cmyk-text .box,.color-picker .hsla-text .box,.color-picker .rgba-text .box{padding:0 24px 8px 8px}.color-picker .value-text .box{padding:0 8px 8px}.color-picker .cmyk-text .box div,.color-picker .hsla-text .box div,.color-picker .rgba-text .box div,.color-picker .value-text .box div{flex:1 1 auto;margin-right:8px;text-align:center;color:#555}.color-picker .cmyk-text .box div:last-child,.color-picker .hsla-text .box div:last-child,.color-picker .rgba-text .box div:last-child,.color-picker .value-text .box div:last-child{margin-right:0}.color-picker .cmyk-text .box input,.color-picker .hsla-text .box input,.color-picker .rgba-text .box input,.color-picker .value-text .box input{float:left;flex:1;padding:1px;margin:0 8px 0 0;border:#a9a9a9 solid 1px}.color-picker .cmyk-text .box input:last-child,.color-picker .hsla-text .box input:last-child,.color-picker .rgba-text .box input:last-child,.color-picker .value-text .box input:last-child{margin-right:0}.color-picker .hue-alpha{align-items:center;margin-bottom:3px}.color-picker .hue{direction:ltr;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwkUFWbCCAAAAFxJREFUaN7t0kEKg0AQAME2x83/n2qu5qCgD1iDhCoYdpnbQC9bbY1qVO/jvc6k3ad91s7/7F1/csgPrujuQ17BDYSFsBAWwgJhISyEBcJCWAgLhIWwEBYIi2f7Ar/1TCgFH2X9AAAAAElFTkSuQmCC)}.color-picker .value{direction:rtl;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAACTklEQVR42u3SYUcrABhA4U2SkmRJMmWSJklKJiWZZpKUJJskKUmaTFImKZOUzMySpGRmliRNJilJSpKSJEtmSpIpmWmSdO736/6D+x7OP3gUCoWCv1cqlSQlJZGcnExKSgqpqamkpaWRnp5ORkYGmZmZqFQqsrKyyM7OJicnh9zcXNRqNXl5eeTn56PRaCgoKKCwsJCioiK0Wi3FxcWUlJRQWlpKWVkZ5eXlVFRUUFlZiU6no6qqiurqampqaqitraWurg69Xk99fT0GgwGj0UhDQwONjY00NTXR3NxMS0sLra2ttLW10d7ejslkwmw209HRQWdnJ11dXXR3d9PT00Nvby99fX309/czMDDA4OAgFouFoaEhrFYrw8PDjIyMMDo6ytjYGDabjfHxcSYmJpicnGRqagq73c709DQzMzPMzs4yNzfH/Pw8DocDp9OJy+XC7XazsLDA4uIiS0tLLC8vs7KywurqKmtra3g8HrxeLz6fD7/fz/r6OhsbG2xubrK1tcX29jaBQICdnR2CwSC7u7vs7e2xv7/PwcEBh4eHHB0dcXx8zMnJCaenp5ydnXF+fs7FxQWXl5dcXV1xfX3Nzc0Nt7e33N3dEQqFuL+/5+HhgXA4TCQS4fHxkaenJ56fn3l5eeH19ZVoNMrb2xvv7+98fHwQi8WIx+N8fn6SSCT4+vri+/ubn58ffn9/+VcKgSWwBJbAElgCS2AJLIElsASWwBJYAktgCSyBJbAElsASWAJLYAksgSWwBJbAElgCS2AJLIElsP4/WH8AmJ5Z6jHS4h8AAAAASUVORK5CYII=)}.color-picker .alpha{direction:ltr;width:100%;height:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwYQlZMa3gAAAWVJREFUaN7tmEGO6jAQRCsOArHgBpyAJYGjcGocxAm4A2IHpmoWE0eBH+ezmFlNvU06shJ3W6VEelWMUQAIIF9f6qZpimsA1LYtS2uF51/u27YVAFZVRUkEoGHdPV/sIcbIEIIkUdI/9Xa7neyv61+SWFUVAVCSct00TWn2fv6u3+Ecfd3tXzy/0+nEUu+SPjo/kqzrmiQpScN6v98XewfA8/lMkiLJ2WxGSUopcT6fM6U0NX9/frfbjev1WtfrlZfLhYfDQQHG/AIOlnGwjINlHCxjHCzjYJm/TJWdCwquJXseFFzGwDNNeiKMOJTO8xQdDQaeB29+K9efeLaBo9J7vdvtJj1RjFFjfiv7qv95tjx/7leSQgh93e1ffMeIp6O+YQjho/N791t1XVOSSI7N//K+4/GoxWLBx+PB5/Op5XLJ+/3OlJJWqxU3m83ovv5iGf8KjYNlHCxjHCzjYBkHy5gf5gusvQU7U37jTAAAAABJRU5ErkJggg==)}.color-picker .type-policy{position:absolute;top:218px;right:12px;width:16px;height:24px;background-size:8px 16px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAACewAAAnsB01CO3AAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIASURBVEiJ7ZY9axRRFIafsxMStrLQJpAgpBFhi+C9w1YSo00I6RZ/g9vZpBf/QOr4GyRgkSKNSrAadsZqQGwCkuAWyRZJsySwvhZ7N/vhzrgbLH3Ld8597jlzz50zJokyxXH8DqDVar0qi6v8BbItqSGpEcfxdlmsFWXkvX8AfAVWg3UKPEnT9GKujMzsAFgZsVaCN1VTQd77XUnrgE1kv+6935268WRpzrnHZvYRWC7YvC3pRZZl3wozqtVqiyH9IgjAspkd1Gq1xUJQtVrdB9ZKIAOthdg/Qc65LUk7wNIMoCVJO865rYFhkqjX6/d7vV4GPJwBMqofURS5JEk6FYBer/eeYb/Mo9WwFnPOvQbeAvfuAAK4BN4sAJtAG/gJIElmNuiJyba3EGNmZiPeZuEVmVell/Y/6N+CzDn3AXhEOOo7Hv/3BeAz8IzQkMPnJbuPx1wC+yYJ7/0nYIP5S/0FHKdp+rwCEEXRS/rf5Hl1Gtb2M0iSpCOpCZzPATmX1EySpHMLAsiy7MjMDoHrGSDXZnaYZdnRwBh7J91utwmczAA6CbG3GgPleX4jqUH/a1CktqRGnuc3hSCAMB32gKspkCtgb3KCQMmkjeP4WNJThrNNZval1WptTIsv7JtQ4tmIdRa8qSoEpWl6YWZNoAN0zKxZNPehpLSBZv2t+Q0CJ9lLnARQLAAAAABJRU5ErkJggg==);background-repeat:no-repeat;background-position:center}.color-picker .type-policy .type-policy-arrow{display:block;width:100%;height:50%}.color-picker .selected-color{position:absolute;top:16px;left:8px;width:40px;height:40px;border:1px solid #a9a9a9;border-radius:50%}.color-picker .selected-color-background{width:40px;height:40px;border-radius:50%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAh0lEQVRYR+2W0QlAMQgD60zdfwOdqa8TmI/wQMr5K0I5bZLIzLOa2nt37VVVbd+dDx5obgCC3KBLwJ2ff4PnVidkf+ucIhw80HQaCLo3DMH3CRK3iFsmAWVl6hPNDwt8EvNE5q+YuEXcMgkonVM6SdyCoEvAnZ8v1Hjx817MilmxSUB5rdLJDycZgUAZUch/AAAAAElFTkSuQmCC)}.color-picker .saturation-lightness{direction:ltr;width:100%;height:130px;border:none;cursor:pointer;touch-action:manipulation;background-size:100% 100%;background-image:url(data:image/png;base64,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)}.color-picker .cp-add-color-button-class{position:absolute;display:inline;padding:0;margin:3px -3px;border:0;cursor:pointer;background:transparent}.color-picker .cp-add-color-button-class:hover{text-decoration:underline}.color-picker .cp-add-color-button-class:disabled{cursor:not-allowed;color:#999}.color-picker .cp-add-color-button-class:disabled:hover{text-decoration:none}.color-picker .cp-remove-color-button-class{position:absolute;top:-5px;right:-5px;display:block;width:10px;height:10px;border-radius:50%;cursor:pointer;text-align:center;background:#fff;box-shadow:1px 1px 5px #333}.color-picker .cp-remove-color-button-class:before{content:\\\"x\\\";position:relative;bottom:3.5px;display:inline-block;font-size:10px}.color-picker .eyedropper-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);fill:#fff;mix-blend-mode:exclusion}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: TextDirective, selector: \"[text]\", inputs: [\"rg\", \"text\"], outputs: [\"newValue\"] }, { kind: \"directive\", type: SliderDirective, selector: \"[slider]\", inputs: [\"rgX\", \"rgY\", \"slider\"], outputs: [\"dragEnd\", \"dragStart\", \"newValue\"] }], encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'color-picker', encapsulation: ViewEncapsulation.None, template: \"<div #dialogPopup class=\\\"color-picker\\\" [class.open]=\\\"show\\\" [style.display]=\\\"!show ? 'none' : 'block'\\\" [style.visibility]=\\\"hidden ? 'hidden' : 'visible'\\\" [style.top.px]=\\\"top\\\" [style.left.px]=\\\"left\\\" [style.position]=\\\"position\\\" [style.height.px]=\\\"cpHeight\\\" [style.width.px]=\\\"cpWidth\\\" (click)=\\\"$event.stopPropagation()\\\">\\n  <div *ngIf=\\\"cpDialogDisplay === 'popup'\\\" [style.left]=\\\"cpArrowPosition\\\" class=\\\"arrow arrow-{{cpUsePosition}}\\\" [style.top.px]=\\\"arrowTop\\\"></div>\\n\\n  <div *ngIf=\\\"(cpColorMode ||\\u00A01) === 1\\\" class=\\\"saturation-lightness\\\" [slider] [rgX]=\\\"1\\\" [rgY]=\\\"1\\\" [style.background-color]=\\\"hueSliderColor\\\" (newValue)=\\\"onColorChange($event)\\\" (dragStart)=\\\"onDragStart('saturation-lightness')\\\" (dragEnd)=\\\"onDragEnd('saturation-lightness')\\\">\\n    <div class=\\\"cursor\\\" [style.top.px]=\\\"slider?.v\\\" [style.left.px]=\\\"slider?.s\\\"></div>\\n  </div>\\n\\n  <div class=\\\"hue-alpha box\\\">\\n    <div class=\\\"left\\\">\\n      <div class=\\\"selected-color-background\\\"></div>\\n\\n      <div class=\\\"selected-color\\\" [style.background-color]=\\\"selectedColor\\\" [style.cursor]=\\\"eyeDropperSupported && cpEyeDropper ? 'pointer' : null\\\" (click)=\\\"eyeDropperSupported && cpEyeDropper && onEyeDropper()\\\">\\n        <svg *ngIf=\\\"eyeDropperSupported && cpEyeDropper\\\" class=\\\"eyedropper-icon\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" height=\\\"24px\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" fill=\\\"#000000\\\"><path d=\\\"M0 0h24v24H0V0z\\\" fill=\\\"none\\\"/><path d=\\\"M17.66 5.41l.92.92-2.69 2.69-.92-.92 2.69-2.69M17.67 3c-.26 0-.51.1-.71.29l-3.12 3.12-1.93-1.91-1.41 1.41 1.42 1.42L3 16.25V21h4.75l8.92-8.92 1.42 1.42 1.41-1.41-1.92-1.92 3.12-3.12c.4-.4.4-1.03.01-1.42l-2.34-2.34c-.2-.19-.45-.29-.7-.29zM6.92 19L5 17.08l8.06-8.06 1.92 1.92L6.92 19z\\\"/></svg>\\n      </div>\\n\\n      <button *ngIf=\\\"cpAddColorButton\\\" type=\\\"button\\\" class=\\\"{{cpAddColorButtonClass}}\\\" [disabled]=\\\"cpPresetColors && cpPresetColors.length >= cpMaxPresetColorsLength\\\" (click)=\\\"onAddPresetColor($event, selectedColor)\\\">\\n        {{cpAddColorButtonText}}\\n      </button>\\n    </div>\\n\\n    <div class=\\\"right\\\">\\n      <div *ngIf=\\\"cpAlphaChannel==='disabled'\\\" style=\\\"height: 16px;\\\"></div>\\n\\n      <div #hueSlider class=\\\"hue\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 1 ? 'block' : 'none'\\\" (newValue)=\\\"onHueChange($event)\\\" (dragStart)=\\\"onDragStart('hue')\\\" (dragEnd)=\\\"onDragEnd('hue')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.h\\\"></div>\\n      </div>\\n\\n      <div #valueSlider class=\\\"value\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"(cpColorMode ||\\u00A01) === 2 ? 'block': 'none'\\\" (newValue)=\\\"onValueChange($event)\\\" (dragStart)=\\\"onDragStart('value')\\\" (dragEnd)=\\\"onDragEnd('value')\\\">\\n        <div class=\\\"cursor\\\" [style.right.px]=\\\"slider?.v\\\"></div>\\n      </div>\\n\\n      <div #alphaSlider class=\\\"alpha\\\" [slider] [rgX]=\\\"1\\\" [style.display]=\\\"cpAlphaChannel === 'disabled' ? 'none' : 'block'\\\" [style.background-color]=\\\"alphaSliderColor\\\" (newValue)=\\\"onAlphaChange($event)\\\" (dragStart)=\\\"onDragStart('alpha')\\\" (dragEnd)=\\\"onDragEnd('alpha')\\\">\\n        <div class=\\\"cursor\\\" [style.left.px]=\\\"slider?.a\\\"></div>\\n      </div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"cmyk-text\\\" [style.display]=\\\"format !== 3 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.c\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onCyanInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.m\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onMagentaInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.y\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onYellowInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"cmykText?.k\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlackInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"cmykText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n     <div class=\\\"box\\\">\\n      <div>C</div><div>M</div><div>Y</div><div>K</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" class=\\\"hsla-text\\\" [style.display]=\\\"format !== 2 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"360\\\" [text] [rg]=\\\"360\\\" [value]=\\\"hslaText?.h\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHueInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.s\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onSaturationInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onLightnessInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>H</div><div>S</div><div>L</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1 \\\" [style.display]=\\\"format !== 1 ? 'none' : 'block'\\\" class=\\\"rgba-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.r\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onRedInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.g\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onGreenInput($event)\\\" />\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"255\\\" [text] [rg]=\\\"255\\\" [value]=\\\"rgbaText?.b\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onBlueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"rgbaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>R</div><div>G</div><div>B</div><div *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" >A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"hex-text\\\" [class.hex-alpha]=\\\"cpAlphaChannel==='forced'\\\"\\n    [style.display]=\\\"format !== 0 ? 'none' : 'block'\\\">\\n    <div class=\\\"box\\\">\\n      <input [text] [value]=\\\"hexText\\\" (blur)=\\\"onHexInput(null)\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onHexInput($event)\\\"/>\\n      <input *ngIf=\\\"cpAlphaChannel==='forced'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\" [text] [rg]=\\\"1\\\" [value]=\\\"hexAlpha\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\"/>\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>Hex</div>\\n      <div *ngIf=\\\"cpAlphaChannel==='forced'\\\">A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 2\\\" class=\\\"value-text\\\">\\n    <div class=\\\"box\\\">\\n      <input type=\\\"number\\\" pattern=\\\"[0-9]*\\\" min=\\\"0\\\" max=\\\"100\\\" [text] [rg]=\\\"100\\\" [value]=\\\"hslaText?.l\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onValueInput($event)\\\" />\\n      <input *ngIf=\\\"cpAlphaChannel!=='disabled'\\\" type=\\\"number\\\" pattern=\\\"[0-9]+([\\\\.,][0-9]{1,2})?\\\" min=\\\"0\\\" max=\\\"1\\\" step=\\\"0.1\\\"  [text] [rg]=\\\"1\\\" [value]=\\\"hslaText?.a\\\" (keyup.enter)=\\\"onAcceptColor($event)\\\" (newValue)=\\\"onAlphaInput($event)\\\" />\\n    </div>\\n\\n    <div class=\\\"box\\\">\\n      <div>V</div><div>A</div>\\n    </div>\\n  </div>\\n\\n  <div *ngIf=\\\"!cpDisableInput && (cpColorMode ||\\u00A01) === 1\\\" class=\\\"type-policy\\\">\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(-1)\\\"></span>\\n    <span class=\\\"type-policy-arrow\\\" (click)=\\\"onFormatToggle(1)\\\"></span>\\n  </div>\\n\\n  <div *ngIf=\\\"cpPresetColors?.length || cpAddColorButton\\\" class=\\\"preset-area\\\">\\n    <hr>\\n\\n    <div class=\\\"preset-label\\\">{{cpPresetLabel}}</div>\\n\\n    <div *ngIf=\\\"cpPresetColors?.length\\\" class=\\\"{{cpPresetColorsClass}}\\\">\\n      <div *ngFor=\\\"let color of cpPresetColors\\\" class=\\\"preset-color\\\" [style.backgroundColor]=\\\"color\\\" (click)=\\\"setColorFromString(color)\\\">\\n        <span *ngIf=\\\"cpAddColorButton\\\" class=\\\"{{cpRemoveColorButtonClass}}\\\" (click)=\\\"onRemovePresetColor($event, color)\\\"></span>\\n      </div>\\n    </div>\\n\\n    <div *ngIf=\\\"!cpPresetColors?.length && cpAddColorButton\\\" class=\\\"{{cpPresetEmptyMessageClass}}\\\">{{cpPresetEmptyMessage}}</div>\\n  </div>\\n\\n  <div *ngIf=\\\"cpOKButton || cpCancelButton\\\" class=\\\"button-area\\\">\\n    <button *ngIf=\\\"cpCancelButton\\\" type=\\\"button\\\" class=\\\"{{cpCancelButtonClass}}\\\" (click)=\\\"onCancelColor($event)\\\">{{cpCancelButtonText}}</button>\\n\\n    <button *ngIf=\\\"cpOKButton\\\" type=\\\"button\\\" class=\\\"{{cpOKButtonClass}}\\\" (click)=\\\"onAcceptColor($event)\\\">{{cpOKButtonText}}</button>\\n  </div>\\n\\n  <div class=\\\"extra-template\\\" *ngIf=\\\"cpExtraTemplate\\\">\\n    <ng-container *ngTemplateOutlet=\\\"cpExtraTemplate\\\"></ng-container>\\n  </div>\\n</div>\\n\", styles: [\".color-picker{position:absolute;z-index:1000;width:230px;height:auto;border:#777 solid 1px;cursor:default;-webkit-user-select:none;user-select:none;background-color:#fff}.color-picker *{box-sizing:border-box;margin:0;font-size:11px}.color-picker input{width:0;height:26px;min-width:0;font-size:13px;text-align:center;color:#000}.color-picker input:invalid,.color-picker input:-moz-ui-invalid,.color-picker input:-moz-submit-invalid{box-shadow:none}.color-picker input::-webkit-inner-spin-button,.color-picker input::-webkit-outer-spin-button{margin:0;-webkit-appearance:none}.color-picker .arrow{position:absolute;z-index:999999;width:0;height:0;border-style:solid}.color-picker .arrow.arrow-top{left:8px;border-width:10px 5px;border-color:#777 rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-bottom{top:-20px;left:8px;border-width:10px 5px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) #777 rgba(0,0,0,0)}.color-picker .arrow.arrow-top-left,.color-picker .arrow.arrow-left-top{right:-21px;bottom:8px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-top-right,.color-picker .arrow.arrow-right-top{bottom:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .arrow.arrow-left,.color-picker .arrow.arrow-left-bottom,.color-picker .arrow.arrow-bottom-left{top:8px;right:-21px;border-width:5px 10px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgba(0,0,0,0) #777}.color-picker .arrow.arrow-right,.color-picker .arrow.arrow-right-bottom,.color-picker .arrow.arrow-bottom-right{top:8px;left:-20px;border-width:5px 10px;border-color:rgba(0,0,0,0) #777 rgba(0,0,0,0) rgba(0,0,0,0)}.color-picker .cursor{position:relative;width:16px;height:16px;border:#222 solid 2px;border-radius:50%;cursor:default}.color-picker .box{display:flex;padding:4px 8px}.color-picker .left{position:relative;padding:16px 8px}.color-picker .right{flex:1 1 auto;padding:12px 8px}.color-picker .button-area{padding:0 16px 16px;text-align:right}.color-picker .button-area button{margin-left:8px}.color-picker .preset-area{padding:4px 15px}.color-picker .preset-area .preset-label{overflow:hidden;width:100%;padding:4px;font-size:11px;white-space:nowrap;text-align:left;text-overflow:ellipsis;color:#555}.color-picker .preset-area .preset-color{position:relative;display:inline-block;width:18px;height:18px;margin:4px 6px 8px;border:#a9a9a9 solid 1px;border-radius:25%;cursor:pointer}.color-picker .preset-area .preset-empty-message{min-height:18px;margin-top:4px;margin-bottom:8px;font-style:italic;text-align:center}.color-picker .hex-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .hex-text .box{padding:0 24px 8px 8px}.color-picker .hex-text .box div{float:left;flex:1 1 auto;text-align:center;color:#555;clear:left}.color-picker .hex-text .box input{flex:1 1 auto;padding:1px;border:#a9a9a9 solid 1px}.color-picker .hex-alpha .box div:first-child,.color-picker .hex-alpha .box input:first-child{flex-grow:3;margin-right:8px}.color-picker .cmyk-text,.color-picker .hsla-text,.color-picker .rgba-text,.color-picker .value-text{width:100%;padding:4px 8px;font-size:11px}.color-picker .cmyk-text .box,.color-picker .hsla-text .box,.color-picker .rgba-text .box{padding:0 24px 8px 8px}.color-picker .value-text .box{padding:0 8px 8px}.color-picker .cmyk-text .box div,.color-picker .hsla-text .box div,.color-picker .rgba-text .box div,.color-picker .value-text .box div{flex:1 1 auto;margin-right:8px;text-align:center;color:#555}.color-picker .cmyk-text .box div:last-child,.color-picker .hsla-text .box div:last-child,.color-picker .rgba-text .box div:last-child,.color-picker .value-text .box div:last-child{margin-right:0}.color-picker .cmyk-text .box input,.color-picker .hsla-text .box input,.color-picker .rgba-text .box input,.color-picker .value-text .box input{float:left;flex:1;padding:1px;margin:0 8px 0 0;border:#a9a9a9 solid 1px}.color-picker .cmyk-text .box input:last-child,.color-picker .hsla-text .box input:last-child,.color-picker .rgba-text .box input:last-child,.color-picker .value-text .box input:last-child{margin-right:0}.color-picker .hue-alpha{align-items:center;margin-bottom:3px}.color-picker .hue{direction:ltr;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwkUFWbCCAAAAFxJREFUaN7t0kEKg0AQAME2x83/n2qu5qCgD1iDhCoYdpnbQC9bbY1qVO/jvc6k3ad91s7/7F1/csgPrujuQ17BDYSFsBAWwgJhISyEBcJCWAgLhIWwEBYIi2f7Ar/1TCgFH2X9AAAAAElFTkSuQmCC)}.color-picker .value{direction:rtl;width:100%;height:16px;margin-bottom:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAACTklEQVR42u3SYUcrABhA4U2SkmRJMmWSJklKJiWZZpKUJJskKUmaTFImKZOUzMySpGRmliRNJilJSpKSJEtmSpIpmWmSdO736/6D+x7OP3gUCoWCv1cqlSQlJZGcnExKSgqpqamkpaWRnp5ORkYGmZmZqFQqsrKyyM7OJicnh9zcXNRqNXl5eeTn56PRaCgoKKCwsJCioiK0Wi3FxcWUlJRQWlpKWVkZ5eXlVFRUUFlZiU6no6qqiurqampqaqitraWurg69Xk99fT0GgwGj0UhDQwONjY00NTXR3NxMS0sLra2ttLW10d7ejslkwmw209HRQWdnJ11dXXR3d9PT00Nvby99fX309/czMDDA4OAgFouFoaEhrFYrw8PDjIyMMDo6ytjYGDabjfHxcSYmJpicnGRqagq73c709DQzMzPMzs4yNzfH/Pw8DocDp9OJy+XC7XazsLDA4uIiS0tLLC8vs7KywurqKmtra3g8HrxeLz6fD7/fz/r6OhsbG2xubrK1tcX29jaBQICdnR2CwSC7u7vs7e2xv7/PwcEBh4eHHB0dcXx8zMnJCaenp5ydnXF+fs7FxQWXl5dcXV1xfX3Nzc0Nt7e33N3dEQqFuL+/5+HhgXA4TCQS4fHxkaenJ56fn3l5eeH19ZVoNMrb2xvv7+98fHwQi8WIx+N8fn6SSCT4+vri+/ubn58ffn9/+VcKgSWwBJbAElgCS2AJLIElsASWwBJYAktgCSyBJbAElsASWAJLYAksgSWwBJbAElgCS2AJLIElsP4/WH8AmJ5Z6jHS4h8AAAAASUVORK5CYII=)}.color-picker .alpha{direction:ltr;width:100%;height:16px;border:none;cursor:pointer;background-size:100% 100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAAQCAYAAAD06IYnAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AIWDwYQlZMa3gAAAWVJREFUaN7tmEGO6jAQRCsOArHgBpyAJYGjcGocxAm4A2IHpmoWE0eBH+ezmFlNvU06shJ3W6VEelWMUQAIIF9f6qZpimsA1LYtS2uF51/u27YVAFZVRUkEoGHdPV/sIcbIEIIkUdI/9Xa7neyv61+SWFUVAVCSct00TWn2fv6u3+Ecfd3tXzy/0+nEUu+SPjo/kqzrmiQpScN6v98XewfA8/lMkiLJ2WxGSUopcT6fM6U0NX9/frfbjev1WtfrlZfLhYfDQQHG/AIOlnGwjINlHCxjHCzjYJm/TJWdCwquJXseFFzGwDNNeiKMOJTO8xQdDQaeB29+K9efeLaBo9J7vdvtJj1RjFFjfiv7qv95tjx/7leSQgh93e1ffMeIp6O+YQjho/N791t1XVOSSI7N//K+4/GoxWLBx+PB5/Op5XLJ+/3OlJJWqxU3m83ovv5iGf8KjYNlHCxjHCzjYBkHy5gf5gusvQU7U37jTAAAAABJRU5ErkJggg==)}.color-picker .type-policy{position:absolute;top:218px;right:12px;width:16px;height:24px;background-size:8px 16px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAgCAYAAAAffCjxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAACewAAAnsB01CO3AAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIASURBVEiJ7ZY9axRRFIafsxMStrLQJpAgpBFhi+C9w1YSo00I6RZ/g9vZpBf/QOr4GyRgkSKNSrAadsZqQGwCkuAWyRZJsySwvhZ7N/vhzrgbLH3Ld8597jlzz50zJokyxXH8DqDVar0qi6v8BbItqSGpEcfxdlmsFWXkvX8AfAVWg3UKPEnT9GKujMzsAFgZsVaCN1VTQd77XUnrgE1kv+6935268WRpzrnHZvYRWC7YvC3pRZZl3wozqtVqiyH9IgjAspkd1Gq1xUJQtVrdB9ZKIAOthdg/Qc65LUk7wNIMoCVJO865rYFhkqjX6/d7vV4GPJwBMqofURS5JEk6FYBer/eeYb/Mo9WwFnPOvQbeAvfuAAK4BN4sAJtAG/gJIElmNuiJyba3EGNmZiPeZuEVmVell/Y/6N+CzDn3AXhEOOo7Hv/3BeAz8IzQkMPnJbuPx1wC+yYJ7/0nYIP5S/0FHKdp+rwCEEXRS/rf5Hl1Gtb2M0iSpCOpCZzPATmX1EySpHMLAsiy7MjMDoHrGSDXZnaYZdnRwBh7J91utwmczAA6CbG3GgPleX4jqUH/a1CktqRGnuc3hSCAMB32gKspkCtgb3KCQMmkjeP4WNJThrNNZval1WptTIsv7JtQ4tmIdRa8qSoEpWl6YWZNoAN0zKxZNPehpLSBZv2t+Q0CJ9lLnARQLAAAAABJRU5ErkJggg==);background-repeat:no-repeat;background-position:center}.color-picker .type-policy .type-policy-arrow{display:block;width:100%;height:50%}.color-picker .selected-color{position:absolute;top:16px;left:8px;width:40px;height:40px;border:1px solid #a9a9a9;border-radius:50%}.color-picker .selected-color-background{width:40px;height:40px;border-radius:50%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAh0lEQVRYR+2W0QlAMQgD60zdfwOdqa8TmI/wQMr5K0I5bZLIzLOa2nt37VVVbd+dDx5obgCC3KBLwJ2ff4PnVidkf+ucIhw80HQaCLo3DMH3CRK3iFsmAWVl6hPNDwt8EvNE5q+YuEXcMgkonVM6SdyCoEvAnZ8v1Hjx817MilmxSUB5rdLJDycZgUAZUch/AAAAAElFTkSuQmCC)}.color-picker .saturation-lightness{direction:ltr;width:100%;height:130px;border:none;cursor:pointer;touch-action:manipulation;background-size:100% 100%;background-image:url(data:image/png;base64,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)}.color-picker .cp-add-color-button-class{position:absolute;display:inline;padding:0;margin:3px -3px;border:0;cursor:pointer;background:transparent}.color-picker .cp-add-color-button-class:hover{text-decoration:underline}.color-picker .cp-add-color-button-class:disabled{cursor:not-allowed;color:#999}.color-picker .cp-add-color-button-class:disabled:hover{text-decoration:none}.color-picker .cp-remove-color-button-class{position:absolute;top:-5px;right:-5px;display:block;width:10px;height:10px;border-radius:50%;cursor:pointer;text-align:center;background:#fff;box-shadow:1px 1px 5px #333}.color-picker .cp-remove-color-button-class:before{content:\\\"x\\\";position:relative;bottom:3.5px;display:inline-block;font-size:10px}.color-picker .eyedropper-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);fill:#fff;mix-blend-mode:exclusion}\\n\"] }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: ColorPickerService }], propDecorators: { dialogElement: [{\n                type: ViewChild,\n                args: ['dialogPopup', { static: true }]\n            }], hueSlider: [{\n                type: ViewChild,\n                args: ['hueSlider', { static: true }]\n            }], alphaSlider: [{\n                type: ViewChild,\n                args: ['alphaSlider', { static: true }]\n            }], handleEsc: [{\n                type: HostListener,\n                args: ['document:keyup.esc', ['$event']]\n            }], handleEnter: [{\n                type: HostListener,\n                args: ['document:keyup.enter', ['$event']]\n            }] } });\n\n// Caretaker note: we have still left the `typeof` condition in order to avoid\n// creating a breaking change for projects that still use the View Engine.\n// The `ngDevMode` is always available when Ivy is enabled.\n// This will be evaluated during compilation into `const NG_DEV_MODE = false`,\n// thus Terser will be able to tree-shake `console.warn` calls.\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\nclass ColorPickerDirective {\n    injector;\n    cfr;\n    appRef;\n    vcRef;\n    elRef;\n    _service;\n    dialog;\n    dialogCreated = false;\n    ignoreChanges = false;\n    cmpRef;\n    viewAttachedToAppRef = false;\n    colorPicker;\n    cpWidth = '230px';\n    cpHeight = 'auto';\n    cpToggle = false;\n    cpDisabled = false;\n    cpIgnoredElements = [];\n    cpFallbackColor = '';\n    cpColorMode = 'color';\n    cpCmykEnabled = false;\n    cpOutputFormat = 'auto';\n    cpAlphaChannel = 'enabled';\n    cpDisableInput = false;\n    cpDialogDisplay = 'popup';\n    cpSaveClickOutside = true;\n    cpCloseClickOutside = true;\n    cpUseRootViewContainer = false;\n    cpPosition = 'auto';\n    cpPositionOffset = '0%';\n    cpPositionRelativeToArrow = false;\n    cpOKButton = false;\n    cpOKButtonText = 'OK';\n    cpOKButtonClass = 'cp-ok-button-class';\n    cpCancelButton = false;\n    cpCancelButtonText = 'Cancel';\n    cpCancelButtonClass = 'cp-cancel-button-class';\n    cpEyeDropper = false;\n    cpPresetLabel = 'Preset colors';\n    cpPresetColors;\n    cpPresetColorsClass = 'cp-preset-colors-class';\n    cpMaxPresetColorsLength = 6;\n    cpPresetEmptyMessage = 'No colors added';\n    cpPresetEmptyMessageClass = 'preset-empty-message';\n    cpAddColorButton = false;\n    cpAddColorButtonText = 'Add color';\n    cpAddColorButtonClass = 'cp-add-color-button-class';\n    cpRemoveColorButtonClass = 'cp-remove-color-button-class';\n    cpArrowPosition = 0;\n    cpExtraTemplate;\n    cpInputChange = new EventEmitter(true);\n    cpToggleChange = new EventEmitter(true);\n    cpSliderChange = new EventEmitter(true);\n    cpSliderDragEnd = new EventEmitter(true);\n    cpSliderDragStart = new EventEmitter(true);\n    colorPickerOpen = new EventEmitter(true);\n    colorPickerClose = new EventEmitter(true);\n    colorPickerCancel = new EventEmitter(true);\n    colorPickerSelect = new EventEmitter(true);\n    colorPickerChange = new EventEmitter(false);\n    cpCmykColorChange = new EventEmitter(true);\n    cpPresetColorsChange = new EventEmitter(true);\n    handleClick() {\n        this.inputFocus();\n    }\n    handleFocus() {\n        this.inputFocus();\n    }\n    handleInput(event) {\n        this.inputChange(event);\n    }\n    constructor(injector, cfr, appRef, vcRef, elRef, _service) {\n        this.injector = injector;\n        this.cfr = cfr;\n        this.appRef = appRef;\n        this.vcRef = vcRef;\n        this.elRef = elRef;\n        this._service = _service;\n    }\n    ngOnDestroy() {\n        if (this.cmpRef != null) {\n            if (this.viewAttachedToAppRef) {\n                this.appRef.detachView(this.cmpRef.hostView);\n            }\n            this.cmpRef.destroy();\n            this.cmpRef = null;\n            this.dialog = null;\n        }\n    }\n    ngOnChanges(changes) {\n        if (changes.cpToggle && !this.cpDisabled) {\n            if (changes.cpToggle.currentValue) {\n                this.openDialog();\n            }\n            else if (!changes.cpToggle.currentValue) {\n                this.closeDialog();\n            }\n        }\n        if (changes.colorPicker) {\n            if (this.dialog && !this.ignoreChanges) {\n                if (this.cpDialogDisplay === 'inline') {\n                    this.dialog.setInitialColor(changes.colorPicker.currentValue);\n                }\n                this.dialog.setColorFromString(changes.colorPicker.currentValue, false);\n                if (this.cpUseRootViewContainer && this.cpDialogDisplay !== 'inline') {\n                    this.cmpRef.changeDetectorRef.detectChanges();\n                }\n            }\n            this.ignoreChanges = false;\n        }\n        if (changes.cpPresetLabel || changes.cpPresetColors) {\n            if (this.dialog) {\n                this.dialog.setPresetConfig(this.cpPresetLabel, this.cpPresetColors);\n            }\n        }\n    }\n    openDialog() {\n        if (!this.dialogCreated) {\n            let vcRef = this.vcRef;\n            this.dialogCreated = true;\n            this.viewAttachedToAppRef = false;\n            if (this.cpUseRootViewContainer && this.cpDialogDisplay !== 'inline') {\n                const classOfRootComponent = this.appRef.componentTypes[0];\n                const appInstance = this.injector.get(classOfRootComponent, Injector.NULL);\n                if (appInstance !== Injector.NULL) {\n                    vcRef = appInstance.vcRef || appInstance.viewContainerRef || this.vcRef;\n                    if (NG_DEV_MODE && vcRef === this.vcRef) {\n                        console.warn('You are using cpUseRootViewContainer, ' +\n                            'but the root component is not exposing viewContainerRef!' +\n                            'Please expose it by adding \\'public vcRef: ViewContainerRef\\' to the constructor.');\n                    }\n                }\n                else {\n                    this.viewAttachedToAppRef = true;\n                }\n            }\n            const compFactory = this.cfr.resolveComponentFactory(ColorPickerComponent);\n            if (this.viewAttachedToAppRef) {\n                this.cmpRef = compFactory.create(this.injector);\n                this.appRef.attachView(this.cmpRef.hostView);\n                document.body.appendChild(this.cmpRef.hostView.rootNodes[0]);\n            }\n            else {\n                const injector = Injector.create({\n                    providers: [],\n                    // We shouldn't use `vcRef.parentInjector` since it's been deprecated long time ago and might be removed\n                    // in newer Angular versions: https://github.com/angular/angular/pull/25174.\n                    parent: vcRef.injector,\n                });\n                this.cmpRef = vcRef.createComponent(compFactory, 0, injector, []);\n            }\n            this.cmpRef.instance.setupDialog(this, this.elRef, this.colorPicker, this.cpWidth, this.cpHeight, this.cpDialogDisplay, this.cpFallbackColor, this.cpColorMode, this.cpCmykEnabled, this.cpAlphaChannel, this.cpOutputFormat, this.cpDisableInput, this.cpIgnoredElements, this.cpSaveClickOutside, this.cpCloseClickOutside, this.cpUseRootViewContainer, this.cpPosition, this.cpPositionOffset, this.cpPositionRelativeToArrow, this.cpPresetLabel, this.cpPresetColors, this.cpPresetColorsClass, this.cpMaxPresetColorsLength, this.cpPresetEmptyMessage, this.cpPresetEmptyMessageClass, this.cpOKButton, this.cpOKButtonClass, this.cpOKButtonText, this.cpCancelButton, this.cpCancelButtonClass, this.cpCancelButtonText, this.cpAddColorButton, this.cpAddColorButtonClass, this.cpAddColorButtonText, this.cpRemoveColorButtonClass, this.cpEyeDropper, this.elRef, this.cpExtraTemplate);\n            this.dialog = this.cmpRef.instance;\n            if (this.vcRef !== vcRef) {\n                this.cmpRef.changeDetectorRef.detectChanges();\n            }\n        }\n        else if (this.dialog) {\n            this.dialog.openDialog(this.colorPicker);\n        }\n    }\n    closeDialog() {\n        if (this.dialog && this.cpDialogDisplay === 'popup') {\n            this.dialog.closeDialog();\n        }\n    }\n    cmykChanged(value) {\n        this.cpCmykColorChange.emit(value);\n    }\n    stateChanged(state) {\n        this.cpToggleChange.emit(state);\n        if (state) {\n            this.colorPickerOpen.emit(this.colorPicker);\n        }\n        else {\n            this.colorPickerClose.emit(this.colorPicker);\n        }\n    }\n    colorChanged(value, ignore = true) {\n        this.ignoreChanges = ignore;\n        this.colorPickerChange.emit(value);\n    }\n    colorSelected(value) {\n        this.colorPickerSelect.emit(value);\n    }\n    colorCanceled() {\n        this.colorPickerCancel.emit();\n    }\n    inputFocus() {\n        const element = this.elRef.nativeElement;\n        const ignored = this.cpIgnoredElements.filter((item) => item === element);\n        if (!this.cpDisabled && !ignored.length) {\n            if (typeof document !== 'undefined' && element === document.activeElement) {\n                this.openDialog();\n            }\n            else if (!this.dialog || !this.dialog.show) {\n                this.openDialog();\n            }\n            else {\n                this.closeDialog();\n            }\n        }\n    }\n    inputChange(event) {\n        if (this.dialog) {\n            this.dialog.setColorFromString(event.target.value, true);\n        }\n        else {\n            this.colorPicker = event.target.value;\n            this.colorPickerChange.emit(this.colorPicker);\n        }\n    }\n    inputChanged(event) {\n        this.cpInputChange.emit(event);\n    }\n    sliderChanged(event) {\n        this.cpSliderChange.emit(event);\n    }\n    sliderDragEnd(event) {\n        this.cpSliderDragEnd.emit(event);\n    }\n    sliderDragStart(event) {\n        this.cpSliderDragStart.emit(event);\n    }\n    presetColorsChanged(value) {\n        this.cpPresetColorsChange.emit(value);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerDirective, deps: [{ token: i0.Injector }, { token: i0.ComponentFactoryResolver }, { token: i0.ApplicationRef }, { token: i0.ViewContainerRef }, { token: i0.ElementRef }, { token: ColorPickerService }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.2\", type: ColorPickerDirective, selector: \"[colorPicker]\", inputs: { colorPicker: \"colorPicker\", cpWidth: \"cpWidth\", cpHeight: \"cpHeight\", cpToggle: \"cpToggle\", cpDisabled: \"cpDisabled\", cpIgnoredElements: \"cpIgnoredElements\", cpFallbackColor: \"cpFallbackColor\", cpColorMode: \"cpColorMode\", cpCmykEnabled: \"cpCmykEnabled\", cpOutputFormat: \"cpOutputFormat\", cpAlphaChannel: \"cpAlphaChannel\", cpDisableInput: \"cpDisableInput\", cpDialogDisplay: \"cpDialogDisplay\", cpSaveClickOutside: \"cpSaveClickOutside\", cpCloseClickOutside: \"cpCloseClickOutside\", cpUseRootViewContainer: \"cpUseRootViewContainer\", cpPosition: \"cpPosition\", cpPositionOffset: \"cpPositionOffset\", cpPositionRelativeToArrow: \"cpPositionRelativeToArrow\", cpOKButton: \"cpOKButton\", cpOKButtonText: \"cpOKButtonText\", cpOKButtonClass: \"cpOKButtonClass\", cpCancelButton: \"cpCancelButton\", cpCancelButtonText: \"cpCancelButtonText\", cpCancelButtonClass: \"cpCancelButtonClass\", cpEyeDropper: \"cpEyeDropper\", cpPresetLabel: \"cpPresetLabel\", cpPresetColors: \"cpPresetColors\", cpPresetColorsClass: \"cpPresetColorsClass\", cpMaxPresetColorsLength: \"cpMaxPresetColorsLength\", cpPresetEmptyMessage: \"cpPresetEmptyMessage\", cpPresetEmptyMessageClass: \"cpPresetEmptyMessageClass\", cpAddColorButton: \"cpAddColorButton\", cpAddColorButtonText: \"cpAddColorButtonText\", cpAddColorButtonClass: \"cpAddColorButtonClass\", cpRemoveColorButtonClass: \"cpRemoveColorButtonClass\", cpArrowPosition: \"cpArrowPosition\", cpExtraTemplate: \"cpExtraTemplate\" }, outputs: { cpInputChange: \"cpInputChange\", cpToggleChange: \"cpToggleChange\", cpSliderChange: \"cpSliderChange\", cpSliderDragEnd: \"cpSliderDragEnd\", cpSliderDragStart: \"cpSliderDragStart\", colorPickerOpen: \"colorPickerOpen\", colorPickerClose: \"colorPickerClose\", colorPickerCancel: \"colorPickerCancel\", colorPickerSelect: \"colorPickerSelect\", colorPickerChange: \"colorPickerChange\", cpCmykColorChange: \"cpCmykColorChange\", cpPresetColorsChange: \"cpPresetColorsChange\" }, host: { listeners: { \"click\": \"handleClick()\", \"focus\": \"handleFocus()\", \"input\": \"handleInput($event)\" } }, exportAs: [\"ngxColorPicker\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[colorPicker]',\n                    exportAs: 'ngxColorPicker'\n                }]\n        }], ctorParameters: () => [{ type: i0.Injector }, { type: i0.ComponentFactoryResolver }, { type: i0.ApplicationRef }, { type: i0.ViewContainerRef }, { type: i0.ElementRef }, { type: ColorPickerService }], propDecorators: { colorPicker: [{\n                type: Input\n            }], cpWidth: [{\n                type: Input\n            }], cpHeight: [{\n                type: Input\n            }], cpToggle: [{\n                type: Input\n            }], cpDisabled: [{\n                type: Input\n            }], cpIgnoredElements: [{\n                type: Input\n            }], cpFallbackColor: [{\n                type: Input\n            }], cpColorMode: [{\n                type: Input\n            }], cpCmykEnabled: [{\n                type: Input\n            }], cpOutputFormat: [{\n                type: Input\n            }], cpAlphaChannel: [{\n                type: Input\n            }], cpDisableInput: [{\n                type: Input\n            }], cpDialogDisplay: [{\n                type: Input\n            }], cpSaveClickOutside: [{\n                type: Input\n            }], cpCloseClickOutside: [{\n                type: Input\n            }], cpUseRootViewContainer: [{\n                type: Input\n            }], cpPosition: [{\n                type: Input\n            }], cpPositionOffset: [{\n                type: Input\n            }], cpPositionRelativeToArrow: [{\n                type: Input\n            }], cpOKButton: [{\n                type: Input\n            }], cpOKButtonText: [{\n                type: Input\n            }], cpOKButtonClass: [{\n                type: Input\n            }], cpCancelButton: [{\n                type: Input\n            }], cpCancelButtonText: [{\n                type: Input\n            }], cpCancelButtonClass: [{\n                type: Input\n            }], cpEyeDropper: [{\n                type: Input\n            }], cpPresetLabel: [{\n                type: Input\n            }], cpPresetColors: [{\n                type: Input\n            }], cpPresetColorsClass: [{\n                type: Input\n            }], cpMaxPresetColorsLength: [{\n                type: Input\n            }], cpPresetEmptyMessage: [{\n                type: Input\n            }], cpPresetEmptyMessageClass: [{\n                type: Input\n            }], cpAddColorButton: [{\n                type: Input\n            }], cpAddColorButtonText: [{\n                type: Input\n            }], cpAddColorButtonClass: [{\n                type: Input\n            }], cpRemoveColorButtonClass: [{\n                type: Input\n            }], cpArrowPosition: [{\n                type: Input\n            }], cpExtraTemplate: [{\n                type: Input\n            }], cpInputChange: [{\n                type: Output\n            }], cpToggleChange: [{\n                type: Output\n            }], cpSliderChange: [{\n                type: Output\n            }], cpSliderDragEnd: [{\n                type: Output\n            }], cpSliderDragStart: [{\n                type: Output\n            }], colorPickerOpen: [{\n                type: Output\n            }], colorPickerClose: [{\n                type: Output\n            }], colorPickerCancel: [{\n                type: Output\n            }], colorPickerSelect: [{\n                type: Output\n            }], colorPickerChange: [{\n                type: Output\n            }], cpCmykColorChange: [{\n                type: Output\n            }], cpPresetColorsChange: [{\n                type: Output\n            }], handleClick: [{\n                type: HostListener,\n                args: ['click']\n            }], handleFocus: [{\n                type: HostListener,\n                args: ['focus']\n            }], handleInput: [{\n                type: HostListener,\n                args: ['input', ['$event']]\n            }] } });\n\nclass ColorPickerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerModule, declarations: [ColorPickerComponent, ColorPickerDirective, TextDirective, SliderDirective], imports: [CommonModule], exports: [ColorPickerDirective] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerModule, providers: [ColorPickerService], imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.2\", ngImport: i0, type: ColorPickerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [ColorPickerDirective],\n                    providers: [ColorPickerService],\n                    declarations: [ColorPickerComponent, ColorPickerDirective, TextDirective, SliderDirective]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Cmyk, ColorPickerComponent, ColorPickerDirective, ColorPickerModule, ColorPickerService, Hsla, Hsva, Rgba, SliderDirective, TextDirective };\n//# sourceMappingURL=ngx-color-picker.mjs.map\n"], "mappings": ";;;;;;;IAiJiFA,EAAE,CAAAC,SAAA,SA+pC2/B,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAC,MAAA,GA/pC9/BH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAK,sBAAA,iBAAAF,MAAA,CAAAG,aAAA,IA+pCw9B,CAAC;IA/pC39BN,EAAE,CAAAO,WAAA,SAAAJ,MAAA,CAAAK,eA+pCg7B,CAAC,QAAAL,MAAA,CAAAM,QAAA,MAAmE,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAAR,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAU,GAAA,GA/pCv/BZ,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,aA+pCmyC,CAAC;IA/pCtyCd,EAAE,CAAAe,UAAA,sBAAAC,6DAAAC,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAN,GAAA;MAAA,MAAAT,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCwqChB,MAAA,CAAAiB,aAAA,CAAAH,MAAoB,CAAC;IAAA,CAAC,CAAC,uBAAAI,8DAAA;MA/pCjsCrB,EAAE,CAAAkB,aAAA,CAAAN,GAAA;MAAA,MAAAT,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC8sChB,MAAA,CAAAmB,WAAA,CAAY,sBAAsB,CAAC;IAAA,CAAC,CAAC,qBAAAC,4DAAA;MA/pCrvCvB,EAAE,CAAAkB,aAAA,CAAAN,GAAA;MAAA,MAAAT,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCgwChB,MAAA,CAAAqB,SAAA,CAAU,sBAAsB,CAAC;IAAA,CAAC,CAAC;IA/pCryCxB,EAAE,CAAAC,SAAA,aA+pCg4C,CAAC;IA/pCn4CD,EAAE,CAAAyB,YAAA,CA+pC04C,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pC74CH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAO,WAAA,qBAAAJ,MAAA,CAAAuB,cA+pCypC,CAAC;IA/pC5pC1B,EAAE,CAAA2B,UAAA,SA+pCimC,CAAC,SAAW,CAAC;IA/pChnC3B,EAAE,CAAA4B,SAAA,CA+pC21C,CAAC;IA/pC91C5B,EAAE,CAAAO,WAAA,QAAAJ,MAAA,CAAA0B,MAAA,kBAAA1B,MAAA,CAAA0B,MAAA,CAAAC,CAAA,MA+pC21C,CAAC,SAAA3B,MAAA,CAAA0B,MAAA,kBAAA1B,MAAA,CAAA0B,MAAA,CAAAE,CAAA,MAA6B,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAA9B,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pC53CF,EAAE,CAAAiC,cAAA;IAAFjC,EAAE,CAAAc,cAAA,aA+pC+5D,CAAC;IA/pCl6Dd,EAAE,CAAAC,SAAA,cA+pC08D,CAAC,cAAuS,CAAC;IA/pCrvED,EAAE,CAAAyB,YAAA,CA+pCwvE,CAAC;EAAA;AAAA;AAAA,SAAAS,wCAAAhC,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAiC,GAAA,GA/pC3vEnC,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,gBA+pC6+E,CAAC;IA/pCh/Ed,EAAE,CAAAe,UAAA,mBAAAqB,gEAAAnB,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAiB,GAAA;MAAA,MAAAhC,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCo8EhB,MAAA,CAAAkC,gBAAA,CAAApB,MAAA,EAAAd,MAAA,CAAAmC,aAAsC,CAAC;IAAA,CAAC,CAAC;IA/pC/+EtC,EAAE,CAAAuC,MAAA,EA+pCuhF,CAAC;IA/pC1hFvC,EAAE,CAAAyB,YAAA,CA+pCgiF,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pCniFH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAwC,UAAA,CAAArC,MAAA,CAAAsC,qBA+pCs2E,CAAC;IA/pCz2EzC,EAAE,CAAA2B,UAAA,aAAAxB,MAAA,CAAAuC,cAAA,IAAAvC,MAAA,CAAAuC,cAAA,CAAAC,MAAA,IAAAxC,MAAA,CAAAyC,uBA+pCw7E,CAAC;IA/pC37E5C,EAAE,CAAA4B,SAAA,CA+pCuhF,CAAC;IA/pC1hF5B,EAAE,CAAA6C,kBAAA,MAAA1C,MAAA,CAAA2C,oBAAA,KA+pCuhF,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAA7C,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pC1hFF,EAAE,CAAAC,SAAA,aA+pC0pF,CAAC;EAAA;AAAA;AAAA,SAAA+C,8CAAA9C,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAA+C,GAAA,GA/pC7pFjD,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,eA+pCk1J,CAAC;IA/pCr1Jd,EAAE,CAAAe,UAAA,yBAAAmC,2EAAAjC,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAA+B,GAAA;MAAA,MAAA9C,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCqxJhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAmC,wEAAAnC,MAAA;MA/pC9yJjB,EAAE,CAAAkB,aAAA,CAAA+B,GAAA;MAAA,MAAA9C,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC0zJhB,MAAA,CAAAkD,YAAA,CAAApC,MAAmB,CAAC;IAAA,CAAC,CAAC;IA/pCl1JjB,EAAE,CAAAyB,YAAA,CA+pCk1J,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pCr1JH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAA2B,UAAA,QA+pC2uJ,CAAC,UAAAxB,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAC,CAAuB,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAAtD,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pCtwJF,EAAE,CAAAc,cAAA,SA+pC89J,CAAC;IA/pCj+Jd,EAAE,CAAAuC,MAAA,OA+pC+9J,CAAC;IA/pCl+JvC,EAAE,CAAAyB,YAAA,CA+pCq+J,CAAC;EAAA;AAAA;AAAA,SAAAgC,sCAAAvD,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAwD,GAAA,GA/pCx+J1D,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,aA+pC2yH,CAAC,aAAwB,CAAC,eAAgM,CAAC;IA/pCxgId,EAAE,CAAAe,UAAA,yBAAA4C,mEAAA1C,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAwC,GAAA;MAAA,MAAAvD,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCy8HhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA2C,gEAAA3C,MAAA;MA/pCl+HjB,EAAE,CAAAkB,aAAA,CAAAwC,GAAA;MAAA,MAAAvD,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC8+HhB,MAAA,CAAA0D,WAAA,CAAA5C,MAAkB,CAAC;IAAA,CAAC,CAAC;IA/pCrgIjB,EAAE,CAAAyB,YAAA,CA+pCqgI,CAAC;IA/pCxgIzB,EAAE,CAAAc,cAAA,eA+pCysI,CAAC;IA/pC5sId,EAAE,CAAAe,UAAA,yBAAA+C,mEAAA7C,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAwC,GAAA;MAAA,MAAAvD,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC0oIhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA8C,gEAAA9C,MAAA;MA/pCnqIjB,EAAE,CAAAkB,aAAA,CAAAwC,GAAA;MAAA,MAAAvD,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC+qIhB,MAAA,CAAA6D,cAAA,CAAA/C,MAAqB,CAAC;IAAA,CAAC,CAAC;IA/pCzsIjB,EAAE,CAAAyB,YAAA,CA+pCysI,CAAC;IA/pC5sIzB,EAAE,CAAAc,cAAA,eA+pC44I,CAAC;IA/pC/4Id,EAAE,CAAAe,UAAA,yBAAAkD,mEAAAhD,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAwC,GAAA;MAAA,MAAAvD,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC80IhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAiD,gEAAAjD,MAAA;MA/pCv2IjB,EAAE,CAAAkB,aAAA,CAAAwC,GAAA;MAAA,MAAAvD,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCm3IhB,MAAA,CAAAgE,aAAA,CAAAlD,MAAoB,CAAC;IAAA,CAAC,CAAC;IA/pC54IjB,EAAE,CAAAyB,YAAA,CA+pC44I,CAAC;IA/pC/4IzB,EAAE,CAAAc,cAAA,eA+pC8kJ,CAAC;IA/pCjlJd,EAAE,CAAAe,UAAA,yBAAAqD,mEAAAnD,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAwC,GAAA;MAAA,MAAAvD,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCihJhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAoD,gEAAApD,MAAA;MA/pC1iJjB,EAAE,CAAAkB,aAAA,CAAAwC,GAAA;MAAA,MAAAvD,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCsjJhB,MAAA,CAAAmE,YAAA,CAAArD,MAAmB,CAAC;IAAA,CAAC,CAAC;IA/pC9kJjB,EAAE,CAAAyB,YAAA,CA+pC8kJ,CAAC;IA/pCjlJzB,EAAE,CAAAuE,UAAA,IAAAvB,6CAAA,mBA+pCk1J,CAAC;IA/pCr1JhD,EAAE,CAAAyB,YAAA,CA+pC81J,CAAC;IA/pCj2JzB,EAAE,CAAAc,cAAA,aA+pC03J,CAAC,SAAY,CAAC;IA/pC14Jd,EAAE,CAAAuC,MAAA,OA+pCw4J,CAAC;IA/pC34JvC,EAAE,CAAAyB,YAAA,CA+pC84J,CAAC;IA/pCj5JzB,EAAE,CAAAc,cAAA,UA+pCm5J,CAAC;IA/pCt5Jd,EAAE,CAAAuC,MAAA,QA+pCo5J,CAAC;IA/pCv5JvC,EAAE,CAAAyB,YAAA,CA+pC05J,CAAC;IA/pC75JzB,EAAE,CAAAc,cAAA,UA+pC+5J,CAAC;IA/pCl6Jd,EAAE,CAAAuC,MAAA,QA+pCg6J,CAAC;IA/pCn6JvC,EAAE,CAAAyB,YAAA,CA+pCs6J,CAAC;IA/pCz6JzB,EAAE,CAAAc,cAAA,UA+pC26J,CAAC;IA/pC96Jd,EAAE,CAAAuC,MAAA,QA+pC46J,CAAC;IA/pC/6JvC,EAAE,CAAAyB,YAAA,CA+pCk7J,CAAC;IA/pCr7JzB,EAAE,CAAAuE,UAAA,KAAAf,4CAAA,iBA+pC89J,CAAC;IA/pCj+JxD,EAAE,CAAAyB,YAAA,CA+pCi/J,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pC9/JH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAO,WAAA,YAAAJ,MAAA,CAAAqE,MAAA,yBA+pC0yH,CAAC;IA/pC7yHxE,EAAE,CAAA4B,SAAA,EA+pC+5H,CAAC;IA/pCl6H5B,EAAE,CAAA2B,UAAA,UA+pC+5H,CAAC,UAAAxB,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAmB,CAAuB,CAAC;IA/pC17HzE,EAAE,CAAA4B,SAAA,CA+pCgmI,CAAC;IA/pCnmI5B,EAAE,CAAA2B,UAAA,UA+pCgmI,CAAC,UAAAxB,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAoB,CAAuB,CAAC;IA/pC3nI1E,EAAE,CAAA4B,SAAA,CA+pCoyI,CAAC;IA/pCvyI5B,EAAE,CAAA2B,UAAA,UA+pCoyI,CAAC,UAAAxB,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAqB,CAAuB,CAAC;IA/pC/zI3E,EAAE,CAAA4B,SAAA,CA+pCu+I,CAAC;IA/pC1+I5B,EAAE,CAAA2B,UAAA,UA+pCu+I,CAAC,UAAAxB,MAAA,CAAAmD,QAAA,kBAAAnD,MAAA,CAAAmD,QAAA,CAAAsB,CAAuB,CAAC;IA/pClgJ5E,EAAE,CAAA4B,SAAA,CA+pCgoJ,CAAC;IA/pCnoJ5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA0E,cAAA,eA+pCgoJ,CAAC;IA/pCnoJ7E,EAAE,CAAA4B,SAAA,GA+pC09J,CAAC;IA/pC79J5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA0E,cAAA,eA+pC09J,CAAC;EAAA;AAAA;AAAA,SAAAC,8CAAA5E,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAA6E,GAAA,GA/pC79J/E,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,eA+pCo/L,CAAC;IA/pCv/Ld,EAAE,CAAAe,UAAA,yBAAAiE,2EAAA/D,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAA6D,GAAA;MAAA,MAAA5E,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCu7LhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAgE,wEAAAhE,MAAA;MA/pCh9LjB,EAAE,CAAAkB,aAAA,CAAA6D,GAAA;MAAA,MAAA5E,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC49LhB,MAAA,CAAAkD,YAAA,CAAApC,MAAmB,CAAC;IAAA,CAAC,CAAC;IA/pCp/LjB,EAAE,CAAAyB,YAAA,CA+pCo/L,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pCv/LH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAA2B,UAAA,QA+pC64L,CAAC,UAAAxB,MAAA,CAAA+E,QAAA,kBAAA/E,MAAA,CAAA+E,QAAA,CAAA3B,CAAuB,CAAC;EAAA;AAAA;AAAA,SAAA4B,6CAAAjF,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pCx6LF,EAAE,CAAAc,cAAA,SA+pCknM,CAAC;IA/pCrnMd,EAAE,CAAAuC,MAAA,OA+pCmnM,CAAC;IA/pCtnMvC,EAAE,CAAAyB,YAAA,CA+pCynM,CAAC;EAAA;AAAA;AAAA,SAAA2D,sCAAAlF,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAmF,GAAA,GA/pC5nMrF,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,aA+pC0oK,CAAC,aAAwB,CAAC,eAA+L,CAAC;IA/pCt2Kd,EAAE,CAAAe,UAAA,yBAAAuE,mEAAArE,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAmE,GAAA;MAAA,MAAAlF,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCwyKhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAsE,gEAAAtE,MAAA;MA/pCj0KjB,EAAE,CAAAkB,aAAA,CAAAmE,GAAA;MAAA,MAAAlF,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC60KhB,MAAA,CAAAqF,UAAA,CAAAvE,MAAiB,CAAC;IAAA,CAAC,CAAC;IA/pCn2KjB,EAAE,CAAAyB,YAAA,CA+pCm2K,CAAC;IA/pCt2KzB,EAAE,CAAAc,cAAA,eA+pC0iL,CAAC;IA/pC7iLd,EAAE,CAAAe,UAAA,yBAAA0E,mEAAAxE,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAmE,GAAA;MAAA,MAAAlF,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCw+KhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAyE,gEAAAzE,MAAA;MA/pCjgLjB,EAAE,CAAAkB,aAAA,CAAAmE,GAAA;MAAA,MAAAlF,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC6gLhB,MAAA,CAAAwF,iBAAA,CAAA1E,MAAwB,CAAC;IAAA,CAAC,CAAC;IA/pC1iLjB,EAAE,CAAAyB,YAAA,CA+pC0iL,CAAC;IA/pC7iLzB,EAAE,CAAAc,cAAA,eA+pCgvL,CAAC;IA/pCnvLd,EAAE,CAAAe,UAAA,yBAAA6E,mEAAA3E,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAmE,GAAA;MAAA,MAAAlF,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC+qLhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA4E,gEAAA5E,MAAA;MA/pCxsLjB,EAAE,CAAAkB,aAAA,CAAAmE,GAAA;MAAA,MAAAlF,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCotLhB,MAAA,CAAA2F,gBAAA,CAAA7E,MAAuB,CAAC;IAAA,CAAC,CAAC;IA/pChvLjB,EAAE,CAAAyB,YAAA,CA+pCgvL,CAAC;IA/pCnvLzB,EAAE,CAAAuE,UAAA,IAAAO,6CAAA,mBA+pCo/L,CAAC;IA/pCv/L9E,EAAE,CAAAyB,YAAA,CA+pCggM,CAAC;IA/pCngMzB,EAAE,CAAAc,cAAA,aA+pC2hM,CAAC,SAAY,CAAC;IA/pC3iMd,EAAE,CAAAuC,MAAA,OA+pCyiM,CAAC;IA/pC5iMvC,EAAE,CAAAyB,YAAA,CA+pC+iM,CAAC;IA/pCljMzB,EAAE,CAAAc,cAAA,SA+pCojM,CAAC;IA/pCvjMd,EAAE,CAAAuC,MAAA,QA+pCqjM,CAAC;IA/pCxjMvC,EAAE,CAAAyB,YAAA,CA+pC2jM,CAAC;IA/pC9jMzB,EAAE,CAAAc,cAAA,UA+pCgkM,CAAC;IA/pCnkMd,EAAE,CAAAuC,MAAA,QA+pCikM,CAAC;IA/pCpkMvC,EAAE,CAAAyB,YAAA,CA+pCukM,CAAC;IA/pC1kMzB,EAAE,CAAAuE,UAAA,KAAAY,4CAAA,iBA+pCknM,CAAC;IA/pCrnMnF,EAAE,CAAAyB,YAAA,CA+pCqoM,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pClpMH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAO,WAAA,YAAAJ,MAAA,CAAAqE,MAAA,yBA+pCyoK,CAAC;IA/pC5oKxE,EAAE,CAAA4B,SAAA,EA+pC8vK,CAAC;IA/pCjwK5B,EAAE,CAAA2B,UAAA,UA+pC8vK,CAAC,UAAAxB,MAAA,CAAA+E,QAAA,kBAAA/E,MAAA,CAAA+E,QAAA,CAAAa,CAAuB,CAAC;IA/pCzxK/F,EAAE,CAAA4B,SAAA,CA+pC87K,CAAC;IA/pCj8K5B,EAAE,CAAA2B,UAAA,UA+pC87K,CAAC,UAAAxB,MAAA,CAAA+E,QAAA,kBAAA/E,MAAA,CAAA+E,QAAA,CAAAnD,CAAuB,CAAC;IA/pCz9K/B,EAAE,CAAA4B,SAAA,CA+pCqoL,CAAC;IA/pCxoL5B,EAAE,CAAA2B,UAAA,UA+pCqoL,CAAC,UAAAxB,MAAA,CAAA+E,QAAA,kBAAA/E,MAAA,CAAA+E,QAAA,CAAAc,CAAuB,CAAC;IA/pChqLhG,EAAE,CAAA4B,SAAA,CA+pCkyL,CAAC;IA/pCryL5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA0E,cAAA,eA+pCkyL,CAAC;IA/pCryL7E,EAAE,CAAA4B,SAAA,EA+pC+mM,CAAC;IA/pClnM5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA0E,cAAA,eA+pC+mM,CAAC;EAAA;AAAA;AAAA,SAAAoB,8CAAA/F,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAgG,IAAA,GA/pClnMlG,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,eA+pC8nO,CAAC;IA/pCjoOd,EAAE,CAAAe,UAAA,yBAAAoF,2EAAAlF,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAgF,IAAA;MAAA,MAAA/F,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCikOhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAmF,wEAAAnF,MAAA;MA/pC1lOjB,EAAE,CAAAkB,aAAA,CAAAgF,IAAA;MAAA,MAAA/F,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCsmOhB,MAAA,CAAAkD,YAAA,CAAApC,MAAmB,CAAC;IAAA,CAAC,CAAC;IA/pC9nOjB,EAAE,CAAAyB,YAAA,CA+pC8nO,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pCjoOH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAA2B,UAAA,QA+pCuhO,CAAC,UAAAxB,MAAA,CAAAkG,QAAA,kBAAAlG,MAAA,CAAAkG,QAAA,CAAA9C,CAAuB,CAAC;EAAA;AAAA;AAAA,SAAA+C,6CAAApG,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pCljOF,EAAE,CAAAc,cAAA,SA+pC6vO,CAAC;IA/pChwOd,EAAE,CAAAuC,MAAA,OA+pC8vO,CAAC;IA/pCjwOvC,EAAE,CAAAyB,YAAA,CA+pCowO,CAAC;EAAA;AAAA;AAAA,SAAA8E,sCAAArG,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAsG,GAAA,GA/pCvwOxG,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,aA+pC8xM,CAAC,aAAwB,CAAC,eAA+L,CAAC;IA/pC1/Md,EAAE,CAAAe,UAAA,yBAAA0F,mEAAAxF,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAsF,GAAA;MAAA,MAAArG,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC47MhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAyF,gEAAAzF,MAAA;MA/pCr9MjB,EAAE,CAAAkB,aAAA,CAAAsF,GAAA;MAAA,MAAArG,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCi+MhB,MAAA,CAAAwG,UAAA,CAAA1F,MAAiB,CAAC;IAAA,CAAC,CAAC;IA/pCv/MjB,EAAE,CAAAyB,YAAA,CA+pCu/M,CAAC;IA/pC1/MzB,EAAE,CAAAc,cAAA,eA+pCyrN,CAAC;IA/pC5rNd,EAAE,CAAAe,UAAA,yBAAA6F,mEAAA3F,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAsF,GAAA;MAAA,MAAArG,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC4nNhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA4F,gEAAA5F,MAAA;MA/pCrpNjB,EAAE,CAAAkB,aAAA,CAAAsF,GAAA;MAAA,MAAArG,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCiqNhB,MAAA,CAAA2G,YAAA,CAAA7F,MAAmB,CAAC;IAAA,CAAC,CAAC;IA/pCzrNjB,EAAE,CAAAyB,YAAA,CA+pCyrN,CAAC;IA/pC5rNzB,EAAE,CAAAc,cAAA,eA+pC03N,CAAC;IA/pC73Nd,EAAE,CAAAe,UAAA,yBAAAgG,mEAAA9F,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAsF,GAAA;MAAA,MAAArG,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC8zNhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA+F,gEAAA/F,MAAA;MA/pCv1NjB,EAAE,CAAAkB,aAAA,CAAAsF,GAAA;MAAA,MAAArG,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCm2NhB,MAAA,CAAA8G,WAAA,CAAAhG,MAAkB,CAAC;IAAA,CAAC,CAAC;IA/pC13NjB,EAAE,CAAAyB,YAAA,CA+pC03N,CAAC;IA/pC73NzB,EAAE,CAAAuE,UAAA,IAAA0B,6CAAA,mBA+pC8nO,CAAC;IA/pCjoOjG,EAAE,CAAAyB,YAAA,CA+pC0oO,CAAC;IA/pC7oOzB,EAAE,CAAAc,cAAA,aA+pCqqO,CAAC,SAAY,CAAC;IA/pCrrOd,EAAE,CAAAuC,MAAA,OA+pCmrO,CAAC;IA/pCtrOvC,EAAE,CAAAyB,YAAA,CA+pCyrO,CAAC;IA/pC5rOzB,EAAE,CAAAc,cAAA,SA+pC8rO,CAAC;IA/pCjsOd,EAAE,CAAAuC,MAAA,QA+pC+rO,CAAC;IA/pClsOvC,EAAE,CAAAyB,YAAA,CA+pCqsO,CAAC;IA/pCxsOzB,EAAE,CAAAc,cAAA,UA+pC0sO,CAAC;IA/pC7sOd,EAAE,CAAAuC,MAAA,QA+pC2sO,CAAC;IA/pC9sOvC,EAAE,CAAAyB,YAAA,CA+pCitO,CAAC;IA/pCptOzB,EAAE,CAAAuE,UAAA,KAAA+B,4CAAA,iBA+pC6vO,CAAC;IA/pChwOtG,EAAE,CAAAyB,YAAA,CA+pCgxO,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pC7xOH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAO,WAAA,YAAAJ,MAAA,CAAAqE,MAAA,yBA+pCywM,CAAC;IA/pC5wMxE,EAAE,CAAA4B,SAAA,EA+pCk5M,CAAC;IA/pCr5M5B,EAAE,CAAA2B,UAAA,UA+pCk5M,CAAC,UAAAxB,MAAA,CAAAkG,QAAA,kBAAAlG,MAAA,CAAAkG,QAAA,CAAAa,CAAuB,CAAC;IA/pC76MlH,EAAE,CAAA4B,SAAA,CA+pCklN,CAAC;IA/pCrlN5B,EAAE,CAAA2B,UAAA,UA+pCklN,CAAC,UAAAxB,MAAA,CAAAkG,QAAA,kBAAAlG,MAAA,CAAAkG,QAAA,CAAAc,CAAuB,CAAC;IA/pC7mNnH,EAAE,CAAA4B,SAAA,CA+pCoxN,CAAC;IA/pCvxN5B,EAAE,CAAA2B,UAAA,UA+pCoxN,CAAC,UAAAxB,MAAA,CAAAkG,QAAA,kBAAAlG,MAAA,CAAAkG,QAAA,CAAAe,CAAuB,CAAC;IA/pC/yNpH,EAAE,CAAA4B,SAAA,CA+pC46N,CAAC;IA/pC/6N5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA0E,cAAA,eA+pC46N,CAAC;IA/pC/6N7E,EAAE,CAAA4B,SAAA,EA+pCyvO,CAAC;IA/pC5vO5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA0E,cAAA,eA+pCyvO,CAAC;EAAA;AAAA;AAAA,SAAAwC,8CAAAnH,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAoH,IAAA,GA/pC5vOtH,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,eA+pCo4P,CAAC;IA/pCv4Pd,EAAE,CAAAe,UAAA,yBAAAwG,2EAAAtG,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAoG,IAAA;MAAA,MAAAnH,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCw0PhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAuG,wEAAAvG,MAAA;MA/pCj2PjB,EAAE,CAAAkB,aAAA,CAAAoG,IAAA;MAAA,MAAAnH,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC62PhB,MAAA,CAAAkD,YAAA,CAAApC,MAAmB,CAAC;IAAA,CAAC,CAAC;IA/pCr4PjB,EAAE,CAAAyB,YAAA,CA+pCo4P,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pCv4PH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAA2B,UAAA,QA+pCiyP,CAAC,UAAAxB,MAAA,CAAAsH,QAAoB,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAAxH,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pCzzPF,EAAE,CAAAc,cAAA,SA+pCk/P,CAAC;IA/pCr/Pd,EAAE,CAAAuC,MAAA,OA+pCm/P,CAAC;IA/pCt/PvC,EAAE,CAAAyB,YAAA,CA+pCy/P,CAAC;EAAA;AAAA;AAAA,SAAAkG,sCAAAzH,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAA0H,IAAA,GA/pC5/P5H,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,aA+pC49O,CAAC,aAAwB,CAAC,eAAgJ,CAAC;IA/pCzoPd,EAAE,CAAAe,UAAA,kBAAA8G,4DAAA;MAAF7H,EAAE,CAAAkB,aAAA,CAAA0G,IAAA;MAAA,MAAAzH,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCyiPhB,MAAA,CAAA2H,UAAA,CAAW,IAAI,CAAC;IAAA,CAAC,CAAC,yBAAAC,mEAAA9G,MAAA;MA/pC7jPjB,EAAE,CAAAkB,aAAA,CAAA0G,IAAA;MAAA,MAAAzH,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC4kPhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAA+G,gEAAA/G,MAAA;MA/pCrmPjB,EAAE,CAAAkB,aAAA,CAAA0G,IAAA;MAAA,MAAAzH,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCinPhB,MAAA,CAAA2H,UAAA,CAAA7G,MAAiB,CAAC;IAAA,CAAC,CAAC;IA/pCvoPjB,EAAE,CAAAyB,YAAA,CA+pCsoP,CAAC;IA/pCzoPzB,EAAE,CAAAuE,UAAA,IAAA8C,6CAAA,mBA+pCo4P,CAAC;IA/pCv4PrH,EAAE,CAAAyB,YAAA,CA+pCg5P,CAAC;IA/pCn5PzB,EAAE,CAAAc,cAAA,aA+pC26P,CAAC,SAAY,CAAC;IA/pC37Pd,EAAE,CAAAuC,MAAA,SA+pC27P,CAAC;IA/pC97PvC,EAAE,CAAAyB,YAAA,CA+pCi8P,CAAC;IA/pCp8PzB,EAAE,CAAAuE,UAAA,IAAAmD,2CAAA,iBA+pCk/P,CAAC;IA/pCr/P1H,EAAE,CAAAyB,YAAA,CA+pCqgQ,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pClhQH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAO,WAAA,YAAAJ,MAAA,CAAAqE,MAAA,yBA+pC29O,CAAC;IA/pC99OxE,EAAE,CAAAiI,WAAA,cAAA9H,MAAA,CAAA0E,cAAA,aA+pCk6O,CAAC;IA/pCr6O7E,EAAE,CAAA4B,SAAA,EA+pC8hP,CAAC;IA/pCjiP5B,EAAE,CAAA2B,UAAA,UAAAxB,MAAA,CAAA+H,OA+pC8hP,CAAC;IA/pCjiPlI,EAAE,CAAA4B,SAAA,CA+pCsrP,CAAC;IA/pCzrP5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA0E,cAAA,aA+pCsrP,CAAC;IA/pCzrP7E,EAAE,CAAA4B,SAAA,EA+pC++P,CAAC;IA/pCl/P5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA0E,cAAA,aA+pC++P,CAAC;EAAA;AAAA;AAAA,SAAAsD,8CAAAjI,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAkI,IAAA,GA/pCl/PpI,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,eA+pC0kR,CAAC;IA/pC7kRd,EAAE,CAAAe,UAAA,yBAAAsH,2EAAApH,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAkH,IAAA;MAAA,MAAAjI,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC6gRhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAqH,wEAAArH,MAAA;MA/pCtiRjB,EAAE,CAAAkB,aAAA,CAAAkH,IAAA;MAAA,MAAAjI,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCkjRhB,MAAA,CAAAkD,YAAA,CAAApC,MAAmB,CAAC;IAAA,CAAC,CAAC;IA/pC1kRjB,EAAE,CAAAyB,YAAA,CA+pC0kR,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pC7kRH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAA2B,UAAA,QA+pCm+Q,CAAC,UAAAxB,MAAA,CAAA+E,QAAA,kBAAA/E,MAAA,CAAA+E,QAAA,CAAA3B,CAAuB,CAAC;EAAA;AAAA;AAAA,SAAAgF,sCAAArI,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAsI,IAAA,GA/pC9/QxI,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,aA+pC0mQ,CAAC,aAAwB,CAAC,eAAiM,CAAC;IA/pCx0Qd,EAAE,CAAAe,UAAA,yBAAA0H,mEAAAxH,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAsH,IAAA;MAAA,MAAArI,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCwwQhB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC,sBAAAyH,gEAAAzH,MAAA;MA/pCjyQjB,EAAE,CAAAkB,aAAA,CAAAsH,IAAA;MAAA,MAAArI,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC6yQhB,MAAA,CAAAwI,YAAA,CAAA1H,MAAmB,CAAC;IAAA,CAAC,CAAC;IA/pCr0QjB,EAAE,CAAAyB,YAAA,CA+pCq0Q,CAAC;IA/pCx0QzB,EAAE,CAAAuE,UAAA,IAAA4D,6CAAA,mBA+pC0kR,CAAC;IA/pC7kRnI,EAAE,CAAAyB,YAAA,CA+pCslR,CAAC;IA/pCzlRzB,EAAE,CAAAc,cAAA,aA+pCinR,CAAC,SAAY,CAAC;IA/pCjoRd,EAAE,CAAAuC,MAAA,OA+pC+nR,CAAC;IA/pCloRvC,EAAE,CAAAyB,YAAA,CA+pCqoR,CAAC;IA/pCxoRzB,EAAE,CAAAc,cAAA,SA+pC0oR,CAAC;IA/pC7oRd,EAAE,CAAAuC,MAAA,OA+pC2oR,CAAC;IA/pC9oRvC,EAAE,CAAAyB,YAAA,CA+pCipR,CAAC,CAAW,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pC1qRH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAA4B,SAAA,EA+pC8tQ,CAAC;IA/pCjuQ5B,EAAE,CAAA2B,UAAA,UA+pC8tQ,CAAC,UAAAxB,MAAA,CAAA+E,QAAA,kBAAA/E,MAAA,CAAA+E,QAAA,CAAAc,CAAuB,CAAC;IA/pCzvQhG,EAAE,CAAA4B,SAAA,CA+pCu3Q,CAAC;IA/pC13Q5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA0E,cAAA,eA+pCu3Q,CAAC;EAAA;AAAA;AAAA,SAAA+D,sCAAA1I,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAA2I,IAAA,GA/pC13Q7I,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,aA+pCmwR,CAAC,cAAsE,CAAC;IA/pC70Rd,EAAE,CAAAe,UAAA,mBAAA+H,4DAAA;MAAF9I,EAAE,CAAAkB,aAAA,CAAA2H,IAAA;MAAA,MAAA1I,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCszRhB,MAAA,CAAA4I,cAAA,EAAgB,CAAC,CAAC;IAAA,CAAC,CAAC;IA/pC50R/I,EAAE,CAAAyB,YAAA,CA+pCi1R,CAAC;IA/pCp1RzB,EAAE,CAAAc,cAAA,cA+pCu5R,CAAC;IA/pC15Rd,EAAE,CAAAe,UAAA,mBAAAiI,4DAAA;MAAFhJ,EAAE,CAAAkB,aAAA,CAAA2H,IAAA;MAAA,MAAA1I,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCo4RhB,MAAA,CAAA4I,cAAA,CAAe,CAAC,CAAC;IAAA,CAAC,CAAC;IA/pCz5R/I,EAAE,CAAAyB,YAAA,CA+pC85R,CAAC,CAAS,CAAC;EAAA;AAAA;AAAA,SAAAwH,yDAAA/I,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAgJ,IAAA,GA/pC36RlJ,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,cA+pCu6S,CAAC;IA/pC16Sd,EAAE,CAAAe,UAAA,mBAAAoI,+EAAAlI,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAgI,IAAA;MAAA,MAAAE,SAAA,GAAFpJ,EAAE,CAAAI,aAAA,GAAAiJ,SAAA;MAAA,MAAAlJ,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCm4ShB,MAAA,CAAAmJ,mBAAA,CAAArI,MAAA,EAAAmI,SAAiC,CAAC;IAAA,CAAC,CAAC;IA/pCz6SpJ,EAAE,CAAAyB,YAAA,CA+pC86S,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pCj7SH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAwC,UAAA,CAAArC,MAAA,CAAAoJ,wBA+pCu3S,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAtJ,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAuJ,IAAA,GA/pC13SzJ,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,aA+pCsyS,CAAC;IA/pCzySd,EAAE,CAAAe,UAAA,mBAAA2I,uEAAA;MAAA,MAAAN,SAAA,GAAFpJ,EAAE,CAAAkB,aAAA,CAAAuI,IAAA,EAAAJ,SAAA;MAAA,MAAAlJ,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC2wShB,MAAA,CAAAwJ,kBAAA,CAAAP,SAAwB,CAAC;IAAA,CAAC,CAAC;IA/pCxySpJ,EAAE,CAAAuE,UAAA,IAAA0E,wDAAA,kBA+pCu6S,CAAC;IA/pC16SjJ,EAAE,CAAAyB,YAAA,CA+pC47S,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAkJ,SAAA,GAAAzI,GAAA,CAAA0I,SAAA;IAAA,MAAAlJ,MAAA,GA/pC/7SH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAO,WAAA,qBAAA6I,SA+pC+vS,CAAC;IA/pClwSpJ,EAAE,CAAA4B,SAAA,CA+pC80S,CAAC;IA/pCj1S5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAAyJ,gBA+pC80S,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAA3J,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pCj1SF,EAAE,CAAAc,cAAA,SA+pCmpS,CAAC;IA/pCtpSd,EAAE,CAAAuE,UAAA,IAAAiF,iDAAA,iBA+pCsyS,CAAC;IA/pCzySxJ,EAAE,CAAAyB,YAAA,CA+pCw8S,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pC38SH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAwC,UAAA,CAAArC,MAAA,CAAA2J,mBA+pCkpS,CAAC;IA/pCrpS9J,EAAE,CAAA4B,SAAA,CA+pCosS,CAAC;IA/pCvsS5B,EAAE,CAAA2B,UAAA,YAAAxB,MAAA,CAAAuC,cA+pCosS,CAAC;EAAA;AAAA;AAAA,SAAAqH,4CAAA7J,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pCvsSF,EAAE,CAAAc,cAAA,SA+pCmjT,CAAC;IA/pCtjTd,EAAE,CAAAuC,MAAA,EA+pC2kT,CAAC;IA/pC9kTvC,EAAE,CAAAyB,YAAA,CA+pCilT,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pCplTH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAwC,UAAA,CAAArC,MAAA,CAAA6J,yBA+pCkjT,CAAC;IA/pCrjThK,EAAE,CAAA4B,SAAA,CA+pC2kT,CAAC;IA/pC9kT5B,EAAE,CAAAiK,iBAAA,CAAA9J,MAAA,CAAA+J,oBA+pC2kT,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAAjK,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pC9kTF,EAAE,CAAAc,cAAA,aA+pC8/R,CAAC;IA/pCjgSd,EAAE,CAAAC,SAAA,QA+pCwgS,CAAC;IA/pC3gSD,EAAE,CAAAc,cAAA,aA+pC4iS,CAAC;IA/pC/iSd,EAAE,CAAAuC,MAAA,EA+pC6jS,CAAC;IA/pChkSvC,EAAE,CAAAyB,YAAA,CA+pCmkS,CAAC;IA/pCtkSzB,EAAE,CAAAuE,UAAA,IAAAsF,2CAAA,iBA+pCmpS,CAAC,IAAAE,2CAAA,iBAA+Z,CAAC;IA/pCtjT/J,EAAE,CAAAyB,YAAA,CA+pC2lT,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pC9lTH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAA4B,SAAA,EA+pC6jS,CAAC;IA/pChkS5B,EAAE,CAAAiK,iBAAA,CAAA9J,MAAA,CAAAiK,aA+pC6jS,CAAC;IA/pChkSpK,EAAE,CAAA4B,SAAA,CA+pC8mS,CAAC;IA/pCjnS5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAAuC,cAAA,kBAAAvC,MAAA,CAAAuC,cAAA,CAAAC,MA+pC8mS,CAAC;IA/pCjnS3C,EAAE,CAAA4B,SAAA,CA+pCwgT,CAAC;IA/pC3gT5B,EAAE,CAAA2B,UAAA,WAAAxB,MAAA,CAAAuC,cAAA,kBAAAvC,MAAA,CAAAuC,cAAA,CAAAC,MAAA,KAAAxC,MAAA,CAAAyJ,gBA+pCwgT,CAAC;EAAA;AAAA;AAAA,SAAAS,+CAAAnK,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAAoK,IAAA,GA/pC3gTtK,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,gBA+pC8xT,CAAC;IA/pCjyTd,EAAE,CAAAe,UAAA,mBAAAwJ,uEAAAtJ,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAAoJ,IAAA;MAAA,MAAAnK,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pCuwThB,MAAA,CAAAqK,aAAA,CAAAvJ,MAAoB,CAAC;IAAA,CAAC,CAAC;IA/pChyTjB,EAAE,CAAAuC,MAAA,EA+pCozT,CAAC;IA/pCvzTvC,EAAE,CAAAyB,YAAA,CA+pC6zT,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pCh0TH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAwC,UAAA,CAAArC,MAAA,CAAAsK,mBA+pC2vT,CAAC;IA/pC9vTzK,EAAE,CAAA4B,SAAA,CA+pCozT,CAAC;IA/pCvzT5B,EAAE,CAAAiK,iBAAA,CAAA9J,MAAA,CAAAuK,kBA+pCozT,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAAzK,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IAAA,MAAA0K,IAAA,GA/pCvzT5K,EAAE,CAAAa,gBAAA;IAAFb,EAAE,CAAAc,cAAA,gBA+pCk7T,CAAC;IA/pCr7Td,EAAE,CAAAe,UAAA,mBAAA8J,uEAAA5J,MAAA;MAAFjB,EAAE,CAAAkB,aAAA,CAAA0J,IAAA;MAAA,MAAAzK,MAAA,GAAFH,EAAE,CAAAI,aAAA;MAAA,OAAFJ,EAAE,CAAAmB,WAAA,CA+pC25ThB,MAAA,CAAAgD,aAAA,CAAAlC,MAAoB,CAAC;IAAA,CAAC,CAAC;IA/pCp7TjB,EAAE,CAAAuC,MAAA,EA+pCo8T,CAAC;IA/pCv8TvC,EAAE,CAAAyB,YAAA,CA+pC68T,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pCh9TH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAwC,UAAA,CAAArC,MAAA,CAAA2K,eA+pC+4T,CAAC;IA/pCl5T9K,EAAE,CAAA4B,SAAA,CA+pCo8T,CAAC;IA/pCv8T5B,EAAE,CAAAiK,iBAAA,CAAA9J,MAAA,CAAA4K,cA+pCo8T,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAA9K,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pCv8TF,EAAE,CAAAc,cAAA,aA+pCmqT,CAAC;IA/pCtqTd,EAAE,CAAAuE,UAAA,IAAA8F,8CAAA,oBA+pC8xT,CAAC,IAAAM,8CAAA,oBAAmJ,CAAC;IA/pCr7T3K,EAAE,CAAAyB,YAAA,CA+pCu9T,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pC19TH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAA4B,SAAA,CA+pCusT,CAAC;IA/pC1sT5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA8K,cA+pCusT,CAAC;IA/pC1sTjL,EAAE,CAAA4B,SAAA,CA+pC+1T,CAAC;IA/pCl2T5B,EAAE,CAAA2B,UAAA,SAAAxB,MAAA,CAAA+K,UA+pC+1T,CAAC;EAAA;AAAA;AAAA,SAAAC,qDAAAjL,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pCl2TF,EAAE,CAAAoL,kBAAA,EA+pC8lU,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAAnL,EAAA,EAAAS,GAAA;EAAA,IAAAT,EAAA;IA/pCjmUF,EAAE,CAAAc,cAAA,aA+pCqhU,CAAC;IA/pCxhUd,EAAE,CAAAuE,UAAA,IAAA4G,oDAAA,0BA+pC+kU,CAAC;IA/pCllUnL,EAAE,CAAAyB,YAAA,CA+pCwmU,CAAC;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAC,MAAA,GA/pC3mUH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAA4B,SAAA,CA+pC4kU,CAAC;IA/pC/kU5B,EAAE,CAAA2B,UAAA,qBAAAxB,MAAA,CAAAmL,eA+pC4kU,CAAC;EAAA;AAAA;AAhzChqU,OAAO,KAAKtL,EAAE,MAAM,eAAe;AACnC,SAASuL,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAClL,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAE3E,IAAIC,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrBA,YAAY,CAACA,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EAC7CA,YAAY,CAACA,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC/CA,YAAY,CAACA,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC/CA,YAAY,CAACA,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACnD,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,MAAMC,IAAI,CAAC;EAKPC,WAAWA,CAACxF,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE7D,CAAC,EAAE;IAAAoJ,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACpB,IAAI,CAACzF,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAAC7D,CAAC,GAAGA,CAAC;EACd;AACJ;AACA,MAAMqJ,IAAI,CAAC;EAKPF,WAAWA,CAAC3G,CAAC,EAAEhE,CAAC,EAAED,CAAC,EAAEyB,CAAC,EAAE;IAAAoJ,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACpB,IAAI,CAAC5G,CAAC,GAAGA,CAAC;IACV,IAAI,CAAChE,CAAC,GAAGA,CAAC;IACV,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACyB,CAAC,GAAGA,CAAC;EACd;AACJ;AACA,MAAMsJ,IAAI,CAAC;EAKPH,WAAWA,CAAC3G,CAAC,EAAEhE,CAAC,EAAEiE,CAAC,EAAEzC,CAAC,EAAE;IAAAoJ,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACpB,IAAI,CAAC5G,CAAC,GAAGA,CAAC;IACV,IAAI,CAAChE,CAAC,GAAGA,CAAC;IACV,IAAI,CAACiE,CAAC,GAAGA,CAAC;IACV,IAAI,CAACzC,CAAC,GAAGA,CAAC;EACd;AACJ;AACA,MAAMuJ,IAAI,CAAC;EAMPJ,WAAWA,CAACjI,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAErB,CAAC,GAAG,CAAC,EAAE;IAAAoJ,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAC3B,IAAI,CAAClI,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACrB,CAAC,GAAGA,CAAC;EACd;AACJ;AAEA,SAASwJ,wBAAwBA,CAACC,QAAQ,EAAEC,eAAe,EAAE;EACzD;EACA,IAAIC,YAAY,GAAG,OAAO;EAC1B,IAAIC,YAAY,GAAG,QAAQ;EAC3B;EACA,MAAM;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAGL,QAAQ;EAClC,MAAM;IAAEM,GAAG;IAAEC;EAAK,CAAC,GAAGN,eAAe;EACrC,MAAMO,MAAM,GAAGF,GAAG,GAAGL,eAAe,CAACG,MAAM;EAC3C,MAAMK,KAAK,GAAGF,IAAI,GAAGN,eAAe,CAACI,KAAK;EAC1C,MAAMK,YAAY,GAAGJ,GAAG,GAAGF,MAAM,GAAG,CAAC;EACrC,MAAMO,eAAe,GAAGH,MAAM,GAAGJ,MAAM,IAAIQ,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAC;EACvG,MAAMC,aAAa,GAAGV,IAAI,GAAGF,KAAK,GAAG,CAAC;EACtC,MAAMa,cAAc,GAAGT,KAAK,GAAGJ,KAAK,IAAIO,MAAM,CAACO,UAAU,IAAIL,QAAQ,CAACC,eAAe,CAACK,WAAW,CAAC;EAClG,MAAMC,YAAY,GAAGX,YAAY,IAAIC,eAAe,IAAIM,aAAa,IAAIC,cAAc;EACvF;EACA,IAAIP,eAAe,EAAE;IACjBR,YAAY,GAAG,KAAK;EACxB;EACA,IAAIO,YAAY,EAAE;IACdP,YAAY,GAAG,QAAQ;EAC3B;EACA,IAAIc,aAAa,EAAE;IACff,YAAY,GAAG,OAAO;EAC1B;EACA,IAAIgB,cAAc,EAAE;IAChBhB,YAAY,GAAG,MAAM;EACzB;EACA;EACA,IAAImB,YAAY,EAAE;IACd,MAAMC,QAAQ,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;IACnD,OAAOA,QAAQ,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAKzB,QAAQ,CAACwB,IAAI,CAAC,GAAGxB,QAAQ,CAACyB,IAAI,CAAC,GAAGD,IAAI,GAAGC,IAAI,CAAC;EACzF;EACA,IAAKR,aAAa,IAAIC,cAAc,EAAG;IACnC,IAAIR,YAAY,EAAE;MACd,OAAO,QAAQ;IACnB;IACA,IAAIC,eAAe,EAAE;MACjB,OAAO,KAAK;IAChB;IACA,OAAOL,GAAG,GAAGE,MAAM,GAAG,KAAK,GAAG,QAAQ;EAC1C;EACA,IAAKE,YAAY,IAAIC,eAAe,EAAG;IACnC,IAAIM,aAAa,EAAE;MACf,OAAO,OAAO;IAClB;IACA,IAAIC,cAAc,EAAE;MAChB,OAAO,MAAM;IACjB;IACA,OAAOX,IAAI,GAAGE,KAAK,GAAG,MAAM,GAAG,OAAO;EAC1C;EACA,OAAO,GAAGN,YAAY,IAAID,YAAY,EAAE;AAC5C;AACA,SAASwB,QAAQA,CAAA,EAAG;EAChB,IAAIC,EAAE,GAAG,EAAE;EACX,IAAI,OAAOC,SAAS,KAAK,WAAW,EAAE;IAClCD,EAAE,GAAGC,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC;EAC1C;EACA,MAAMC,IAAI,GAAGJ,EAAE,CAACK,OAAO,CAAC,OAAO,CAAC;EAChC,IAAID,IAAI,GAAG,CAAC,EAAE;IACV;IACA,OAAOE,QAAQ,CAACN,EAAE,CAACO,SAAS,CAACH,IAAI,GAAG,CAAC,EAAEJ,EAAE,CAACK,OAAO,CAAC,GAAG,EAAED,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EACtE;EACA;EACA,OAAO,KAAK;AAChB;AACA,MAAMI,aAAa,CAAC;EAAAzC,YAAA;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA,mBAGL,IAAIpB,YAAY,CAAC,CAAC;EAAA;EAC7B6D,WAAWA,CAACC,KAAK,EAAE;IACf,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAI,IAAI,CAACE,EAAE,KAAKC,SAAS,EAAE;MACvB,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,MAAMM,OAAO,GAAGC,UAAU,CAACP,KAAK,CAAC;MACjC,IAAI,CAACI,QAAQ,CAACC,IAAI,CAAC;QAAE7N,CAAC,EAAE8N,OAAO;QAAEJ,EAAE,EAAE,IAAI,CAACA;MAAG,CAAC,CAAC;IACnD;EACJ;AAGJ;AAACM,cAAA,GAhBKX,aAAa;AAAAxC,eAAA,CAAbwC,aAAa,wBAAAY,uBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAcoFb,cAAa;AAAA;AAAAxC,eAAA,CAd9GwC,aAAa,8BAiB8DnP,EAAE,CAAAiQ,iBAAA;EAAAC,IAAA,EAFQf,cAAa;EAAAgB,SAAA;EAAAC,YAAA,WAAAC,4BAAAnQ,EAAA,EAAAS,GAAA;IAAA,IAAAT,EAAA;MAEvBF,EAAE,CAAAe,UAAA,mBAAAuP,wCAAArP,MAAA;QAAA,OAFQN,GAAA,CAAAyO,WAAA,CAAAnO,MAAkB,CAAC;MAAA,CAAP,CAAC;IAAA;EAAA;EAAAsP,MAAA;IAAAf,EAAA;IAAAgB,IAAA;EAAA;EAAAC,OAAA;IAAAf,QAAA;EAAA;EAAAgB,UAAA;AAAA;AAExG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF3Q,EAAE,CAAA4Q,iBAAA,CAAQzB,aAAa,EAAc,CAAC;IAC3Ge,IAAI,EAAE1E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEtB,EAAE,EAAE,CAAC;MACnBU,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE+E,IAAI,EAAE,CAAC;MACPN,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEiE,QAAQ,EAAE,CAAC;MACXQ,IAAI,EAAExE;IACV,CAAC,CAAC;IAAE0D,WAAW,EAAE,CAAC;MACdc,IAAI,EAAEvE,YAAY;MAClBkF,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB,MAAME,eAAe,CAAC;EAUlBC,SAASA,CAAC3B,KAAK,EAAE;IACb,IAAI,CAAC4B,KAAK,CAAC5B,KAAK,CAAC;EACrB;EACA6B,UAAUA,CAAC7B,KAAK,EAAE;IACd,IAAI,CAAC4B,KAAK,CAAC5B,KAAK,CAAC;EACrB;EACA3C,WAAWA,CAACyE,KAAK,EAAE;IAAAxE,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,kBATT,IAAIpB,YAAY,CAAC,CAAC;IAAAoB,eAAA,oBAChB,IAAIpB,YAAY,CAAC,CAAC;IAAAoB,eAAA,mBACnB,IAAIpB,YAAY,CAAC,CAAC;IAQzB,IAAI,CAAC4F,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,YAAY,GAAI/B,KAAK,IAAK,IAAI,CAACgC,IAAI,CAAChC,KAAK,CAAC;IAC/C,IAAI,CAACiC,YAAY,GAAG,MAAM,IAAI,CAACC,IAAI,CAAC,CAAC;EACzC;EACAF,IAAIA,CAAChC,KAAK,EAAE;IACRA,KAAK,CAACmC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACC,SAAS,CAACpC,KAAK,CAAC;EACzB;EACA4B,KAAKA,CAAC5B,KAAK,EAAE;IACT,IAAI,CAACoC,SAAS,CAACpC,KAAK,CAAC;IACrBA,KAAK,CAACqC,eAAe,CAAC,CAAC;IACvB5D,QAAQ,CAAC6D,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACL,YAAY,CAAC;IACvDxD,QAAQ,CAAC6D,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACL,YAAY,CAAC;IACxDxD,QAAQ,CAAC6D,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACP,YAAY,CAAC;IACzDtD,QAAQ,CAAC6D,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACP,YAAY,CAAC;IACzD,IAAI,CAACQ,SAAS,CAACjC,IAAI,CAAC,CAAC;EACzB;EACA4B,IAAIA,CAAA,EAAG;IACHzD,QAAQ,CAAC+D,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACP,YAAY,CAAC;IAC1DxD,QAAQ,CAAC+D,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACP,YAAY,CAAC;IAC3DxD,QAAQ,CAAC+D,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACT,YAAY,CAAC;IAC5DtD,QAAQ,CAAC+D,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACT,YAAY,CAAC;IAC5D,IAAI,CAACU,OAAO,CAACnC,IAAI,CAAC,CAAC;EACvB;EACAoC,IAAIA,CAAC1C,KAAK,EAAE;IACR,MAAM2C,QAAQ,GAAG,IAAI,CAACb,KAAK,CAACc,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACjE,MAAMC,KAAK,GAAI9C,KAAK,CAAC8C,KAAK,KAAK1C,SAAS,GAAIJ,KAAK,CAAC8C,KAAK,GAAG9C,KAAK,CAAC+C,OAAO,CAAC,CAAC,CAAC,CAACD,KAAK;IAChF,OAAOA,KAAK,GAAGH,QAAQ,CAACzE,IAAI,GAAGK,MAAM,CAACyE,WAAW;EACrD;EACAC,IAAIA,CAACjD,KAAK,EAAE;IACR,MAAM2C,QAAQ,GAAG,IAAI,CAACb,KAAK,CAACc,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACjE,MAAMK,KAAK,GAAIlD,KAAK,CAACkD,KAAK,KAAK9C,SAAS,GAAIJ,KAAK,CAACkD,KAAK,GAAGlD,KAAK,CAAC+C,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK;IAChF,OAAOA,KAAK,GAAGP,QAAQ,CAAC1E,GAAG,GAAGM,MAAM,CAAC4E,WAAW;EACpD;EACAf,SAASA,CAACpC,KAAK,EAAE;IACb,MAAMhC,KAAK,GAAG,IAAI,CAAC8D,KAAK,CAACc,aAAa,CAACQ,WAAW;IAClD,MAAMrF,MAAM,GAAG,IAAI,CAAC+D,KAAK,CAACc,aAAa,CAACS,YAAY;IACpD,MAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACf,IAAI,CAAC1C,KAAK,CAAC,EAAEhC,KAAK,CAAC,CAAC;IACxD,MAAM1I,CAAC,GAAGiO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,IAAI,CAACR,IAAI,CAACjD,KAAK,CAAC,EAAEjC,MAAM,CAAC,CAAC;IACzD,IAAI,IAAI,CAAC2F,GAAG,KAAKtD,SAAS,IAAI,IAAI,CAACuD,GAAG,KAAKvD,SAAS,EAAE;MAClD,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;QAAE5N,CAAC,EAAE4Q,CAAC,GAAGtF,KAAK;QAAEvL,CAAC,EAAG,CAAC,GAAG6C,CAAC,GAAGyI,MAAO;QAAE2F,GAAG,EAAE,IAAI,CAACA,GAAG;QAAEC,GAAG,EAAE,IAAI,CAACA;MAAI,CAAC,CAAC;IAC3F,CAAC,MACI,IAAI,IAAI,CAACD,GAAG,KAAKtD,SAAS,IAAI,IAAI,CAACuD,GAAG,KAAKvD,SAAS,EAAE;MACvD,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;QAAE7N,CAAC,EAAE6C,CAAC,GAAGyI,MAAM;QAAE4F,GAAG,EAAE,IAAI,CAACA;MAAI,CAAC,CAAC;IACxD,CAAC,MACI,IAAI,IAAI,CAACD,GAAG,KAAKtD,SAAS,IAAI,IAAI,CAACuD,GAAG,KAAKvD,SAAS,EAAE;MACvD,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;QAAE7N,CAAC,EAAE6Q,CAAC,GAAGtF,KAAK;QAAE0F,GAAG,EAAE,IAAI,CAACA;MAAI,CAAC,CAAC;IACvD;EACJ;AAGJ;AAACE,gBAAA,GApEKlC,eAAe;AAAApE,eAAA,CAAfoE,eAAe,wBAAAmC,yBAAAlD,iBAAA;EAAA,YAAAA,iBAAA,IAkEkFe,gBAAe,EAjFrC/Q,EAAE,CAAAmT,iBAAA,CAiFqDnT,EAAE,CAACoT,UAAU;AAAA;AAAAzG,eAAA,CAlE/IoE,eAAe,8BAf4D/Q,EAAE,CAAAiQ,iBAAA;EAAAC,IAAA,EAkFQa,gBAAe;EAAAZ,SAAA;EAAAC,YAAA,WAAAiD,8BAAAnT,EAAA,EAAAS,GAAA;IAAA,IAAAT,EAAA;MAlFzBF,EAAE,CAAAe,UAAA,uBAAAuS,8CAAArS,MAAA;QAAA,OAkFQN,GAAA,CAAAqQ,SAAA,CAAA/P,MAAgB,CAAC;MAAA,CAAH,CAAC,wBAAAsS,+CAAAtS,MAAA;QAAA,OAAfN,GAAA,CAAAuQ,UAAA,CAAAjQ,MAAiB,CAAC;MAAA,CAAJ,CAAC;IAAA;EAAA;EAAAsP,MAAA;IAAAwC,GAAA;IAAAC,GAAA;IAAAnR,MAAA;EAAA;EAAA4O,OAAA;IAAAqB,OAAA;IAAAF,SAAA;IAAAlC,QAAA;EAAA;EAAAgB,UAAA;AAAA;AAE1G;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApFiF3Q,EAAE,CAAA4Q,iBAAA,CAoFQG,eAAe,EAAc,CAAC;IAC7Gb,IAAI,EAAE1E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEZ,IAAI,EAAElQ,EAAE,CAACoT;EAAW,CAAC,CAAC,EAAkB;IAAEL,GAAG,EAAE,CAAC;MACrE7C,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEuH,GAAG,EAAE,CAAC;MACN9C,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE5J,MAAM,EAAE,CAAC;MACTqO,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEqG,OAAO,EAAE,CAAC;MACV5B,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEkG,SAAS,EAAE,CAAC;MACZ1B,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEgE,QAAQ,EAAE,CAAC;MACXQ,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEsF,SAAS,EAAE,CAAC;MACZd,IAAI,EAAEvE,YAAY;MAClBkF,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;IAClC,CAAC,CAAC;IAAEK,UAAU,EAAE,CAAC;MACbhB,IAAI,EAAEvE,YAAY;MAClBkF,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC;IACnC,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM2C,cAAc,CAAC;EAKjB9G,WAAWA,CAAC3G,CAAC,EAAEhE,CAAC,EAAED,CAAC,EAAEyB,CAAC,EAAE;IAAAoJ,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACpB,IAAI,CAAC5G,CAAC,GAAGA,CAAC;IACV,IAAI,CAAChE,CAAC,GAAGA,CAAC;IACV,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACyB,CAAC,GAAGA,CAAC;EACd;AACJ;AACA,MAAMkQ,eAAe,CAAC;EAKlB/G,WAAWA,CAAC3G,CAAC,EAAEhE,CAAC,EAAED,CAAC,EAAEyB,CAAC,EAAE;IAAAoJ,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACpB,IAAI,CAAC5G,CAAC,GAAGA,CAAC;IACV,IAAI,CAAChE,CAAC,GAAGA,CAAC;IACV,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACyB,CAAC,GAAGA,CAAC;EACd;AACJ;AAEA,MAAMmQ,kBAAkB,CAAC;EAAAhH,YAAA;IAAAC,eAAA,iBACZ,IAAI;EAAA;EACbgH,SAASA,CAACC,MAAM,EAAE;IACd,IAAI,IAAI,CAACA,MAAM,IAAI,IAAI,CAACA,MAAM,KAAKA,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,eAAe,KAAK,QAAQ,EAAE;MACnF,IAAI,CAACD,MAAM,CAACE,WAAW,CAAC,CAAC;IAC7B;IACA,IAAI,CAACF,MAAM,GAAGA,MAAM;EACxB;EACAG,SAASA,CAACC,IAAI,EAAE;IACZ,MAAMjO,CAAC,GAAGiO,IAAI,CAACjO,CAAC;MAAEhE,CAAC,GAAGiS,IAAI,CAACjS,CAAC;MAAED,CAAC,GAAGkS,IAAI,CAAClS,CAAC;MAAEyB,CAAC,GAAGyQ,IAAI,CAACzQ,CAAC;IACpD,IAAIzB,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,IAAI+K,IAAI,CAAC9G,CAAC,EAAE,CAAC,EAAE,CAAC,EAAExC,CAAC,CAAC;IAC/B,CAAC,MACI,IAAIxB,CAAC,KAAK,CAAC,IAAID,CAAC,KAAK,CAAC,EAAE;MACzB,OAAO,IAAI+K,IAAI,CAAC9G,CAAC,EAAE,CAAC,EAAE,CAAC,EAAExC,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,MAAMyC,CAAC,GAAGlE,CAAC,IAAI,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC;MACzB,OAAO,IAAI8K,IAAI,CAAC9G,CAAC,EAAEjE,CAAC,GAAGC,CAAC,IAAI,CAAC,GAAG6Q,IAAI,CAACqB,GAAG,CAAC,CAAC,GAAGjO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEA,CAAC,EAAEzC,CAAC,CAAC;IAC/D;EACJ;EACA2Q,SAASA,CAACC,IAAI,EAAE;IACZ,MAAMpO,CAAC,GAAG6M,IAAI,CAACE,GAAG,CAACqB,IAAI,CAACpO,CAAC,EAAE,CAAC,CAAC;MAAEhE,CAAC,GAAG6Q,IAAI,CAACE,GAAG,CAACqB,IAAI,CAACpS,CAAC,EAAE,CAAC,CAAC;IACtD,MAAMiE,CAAC,GAAG4M,IAAI,CAACE,GAAG,CAACqB,IAAI,CAACnO,CAAC,EAAE,CAAC,CAAC;MAAEzC,CAAC,GAAGqP,IAAI,CAACE,GAAG,CAACqB,IAAI,CAAC5Q,CAAC,EAAE,CAAC,CAAC;IACtD,IAAIyC,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,IAAI4G,IAAI,CAAC7G,CAAC,EAAE,CAAC,EAAE,CAAC,EAAExC,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,MAAMzB,CAAC,GAAGkE,CAAC,GAAGjE,CAAC,IAAI,CAAC,GAAG6Q,IAAI,CAACqB,GAAG,CAAC,CAAC,GAAGjO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MAC/C,OAAO,IAAI4G,IAAI,CAAC7G,CAAC,EAAE,CAAC,IAAIjE,CAAC,GAAGkE,CAAC,CAAC,GAAGlE,CAAC,EAAEA,CAAC,EAAEyB,CAAC,CAAC;IAC7C;EACJ;EACA6Q,UAAUA,CAACJ,IAAI,EAAE;IACb,IAAI9M,CAAC,EAAEC,CAAC,EAAEC,CAAC;IACX,MAAMrB,CAAC,GAAGiO,IAAI,CAACjO,CAAC;MAAEhE,CAAC,GAAGiS,IAAI,CAACjS,CAAC;MAAED,CAAC,GAAGkS,IAAI,CAAClS,CAAC;MAAEyB,CAAC,GAAGyQ,IAAI,CAACzQ,CAAC;IACpD,MAAM8Q,CAAC,GAAGzB,IAAI,CAAC0B,KAAK,CAACvO,CAAC,GAAG,CAAC,CAAC;IAC3B,MAAMwO,CAAC,GAAGxO,CAAC,GAAG,CAAC,GAAGsO,CAAC;IACnB,MAAMG,CAAC,GAAG1S,CAAC,IAAI,CAAC,GAAGC,CAAC,CAAC;IACrB,MAAM0S,CAAC,GAAG3S,CAAC,IAAI,CAAC,GAAGyS,CAAC,GAAGxS,CAAC,CAAC;IACzB,MAAM2S,CAAC,GAAG5S,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGyS,CAAC,IAAIxS,CAAC,CAAC;IAC/B,QAAQsS,CAAC,GAAG,CAAC;MACT,KAAK,CAAC;QACFnN,CAAC,GAAGpF,CAAC,EAAEqF,CAAC,GAAGuN,CAAC,EAAEtN,CAAC,GAAGoN,CAAC;QACnB;MACJ,KAAK,CAAC;QACFtN,CAAC,GAAGuN,CAAC,EAAEtN,CAAC,GAAGrF,CAAC,EAAEsF,CAAC,GAAGoN,CAAC;QACnB;MACJ,KAAK,CAAC;QACFtN,CAAC,GAAGsN,CAAC,EAAErN,CAAC,GAAGrF,CAAC,EAAEsF,CAAC,GAAGsN,CAAC;QACnB;MACJ,KAAK,CAAC;QACFxN,CAAC,GAAGsN,CAAC,EAAErN,CAAC,GAAGsN,CAAC,EAAErN,CAAC,GAAGtF,CAAC;QACnB;MACJ,KAAK,CAAC;QACFoF,CAAC,GAAGwN,CAAC,EAAEvN,CAAC,GAAGqN,CAAC,EAAEpN,CAAC,GAAGtF,CAAC;QACnB;MACJ,KAAK,CAAC;QACFoF,CAAC,GAAGpF,CAAC,EAAEqF,CAAC,GAAGqN,CAAC,EAAEpN,CAAC,GAAGqN,CAAC;QACnB;MACJ;QACIvN,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC;IAC3B;IACA,OAAO,IAAIqF,IAAI,CAACvF,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE7D,CAAC,CAAC;EAC/B;EACAoR,SAASA,CAACC,IAAI,EAAE;IACZ,MAAM1N,CAAC,GAAG,CAAC,CAAC,GAAG0N,IAAI,CAACnQ,CAAC,KAAK,CAAC,GAAGmQ,IAAI,CAAChQ,CAAC,CAAC;IACrC,MAAMuC,CAAC,GAAG,CAAC,CAAC,GAAGyN,IAAI,CAAClQ,CAAC,KAAK,CAAC,GAAGkQ,IAAI,CAAChQ,CAAC,CAAC;IACrC,MAAMwC,CAAC,GAAG,CAAC,CAAC,GAAGwN,IAAI,CAACjQ,CAAC,KAAK,CAAC,GAAGiQ,IAAI,CAAChQ,CAAC,CAAC;IACrC,OAAO,IAAI6H,IAAI,CAACvF,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEwN,IAAI,CAACrR,CAAC,CAAC;EACpC;EACAsR,UAAUA,CAACC,IAAI,EAAE;IACb,MAAMlQ,CAAC,GAAG,CAAC,GAAGgO,IAAI,CAACC,GAAG,CAACiC,IAAI,CAAC5N,CAAC,EAAE4N,IAAI,CAAC3N,CAAC,EAAE2N,IAAI,CAAC1N,CAAC,CAAC;IAC9C,IAAIxC,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,IAAIkI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEgI,IAAI,CAACvR,CAAC,CAAC;IACvC,CAAC,MACI;MACD,MAAMkB,CAAC,GAAG,CAAC,CAAC,GAAGqQ,IAAI,CAAC5N,CAAC,GAAGtC,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC;MACpC,MAAMF,CAAC,GAAG,CAAC,CAAC,GAAGoQ,IAAI,CAAC3N,CAAC,GAAGvC,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC;MACpC,MAAMD,CAAC,GAAG,CAAC,CAAC,GAAGmQ,IAAI,CAAC1N,CAAC,GAAGxC,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC;MACpC,OAAO,IAAIkI,IAAI,CAACrI,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEkQ,IAAI,CAACvR,CAAC,CAAC;IACvC;EACJ;EACAwR,UAAUA,CAACD,IAAI,EAAE;IACb,IAAI/O,CAAC,EAAEhE,CAAC;IACR,MAAMmF,CAAC,GAAG0L,IAAI,CAACE,GAAG,CAACgC,IAAI,CAAC5N,CAAC,EAAE,CAAC,CAAC;MAAEC,CAAC,GAAGyL,IAAI,CAACE,GAAG,CAACgC,IAAI,CAAC3N,CAAC,EAAE,CAAC,CAAC;IACtD,MAAMC,CAAC,GAAGwL,IAAI,CAACE,GAAG,CAACgC,IAAI,CAAC1N,CAAC,EAAE,CAAC,CAAC;MAAE7D,CAAC,GAAGqP,IAAI,CAACE,GAAG,CAACgC,IAAI,CAACvR,CAAC,EAAE,CAAC,CAAC;IACtD,MAAMsP,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC3L,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MAAE0L,GAAG,GAAGF,IAAI,CAACE,GAAG,CAAC5L,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtD,MAAMtF,CAAC,GAAG+Q,GAAG;MAAEmC,CAAC,GAAGnC,GAAG,GAAGC,GAAG;IAC5B/Q,CAAC,GAAI8Q,GAAG,KAAK,CAAC,GAAI,CAAC,GAAGmC,CAAC,GAAGnC,GAAG;IAC7B,IAAIA,GAAG,KAAKC,GAAG,EAAE;MACb/M,CAAC,GAAG,CAAC;IACT,CAAC,MACI;MACD,QAAQ8M,GAAG;QACP,KAAK3L,CAAC;UACFnB,CAAC,GAAG,CAACoB,CAAC,GAAGC,CAAC,IAAI4N,CAAC,IAAI7N,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UACjC;QACJ,KAAKD,CAAC;UACFpB,CAAC,GAAG,CAACqB,CAAC,GAAGF,CAAC,IAAI8N,CAAC,GAAG,CAAC;UACnB;QACJ,KAAK5N,CAAC;UACFrB,CAAC,GAAG,CAACmB,CAAC,GAAGC,CAAC,IAAI6N,CAAC,GAAG,CAAC;UACnB;QACJ;UACIjP,CAAC,GAAG,CAAC;MACb;MACAA,CAAC,IAAI,CAAC;IACV;IACA,OAAO,IAAI6G,IAAI,CAAC7G,CAAC,EAAEhE,CAAC,EAAED,CAAC,EAAEyB,CAAC,CAAC;EAC/B;EACA0R,SAASA,CAACH,IAAI,EAAEI,SAAS,EAAE;IACvB;IACA,IAAIC,GAAG,GAAG,GAAG,GAAG,CAAE,CAAC,IAAI,EAAE,GAAKL,IAAI,CAAC5N,CAAC,IAAI,EAAG,GAAI4N,IAAI,CAAC3N,CAAC,IAAI,CAAE,GAAG2N,IAAI,CAAC1N,CAAC,EAAEgO,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;IAC5F,IAAIH,SAAS,EAAE;MACXC,GAAG,IAAI,CAAE,CAAC,IAAI,CAAC,GAAIvC,IAAI,CAAC0C,KAAK,CAACR,IAAI,CAACvR,CAAC,GAAG,GAAG,CAAC,EAAE6R,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;IACvE;IACA;IACA,OAAOF,GAAG;EACd;EACAI,aAAaA,CAACX,IAAI,EAAE;IAChB,OAAO,IAAI9H,IAAI,CAAC8H,IAAI,CAACnQ,CAAC,GAAG,GAAG,EAAEmQ,IAAI,CAAClQ,CAAC,GAAG,GAAG,EAAEkQ,IAAI,CAACjQ,CAAC,GAAG,GAAG,EAAEiQ,IAAI,CAAChQ,CAAC,GAAG,GAAG,EAAEgQ,IAAI,CAACrR,CAAC,CAAC;EACnF;EACAiS,eAAeA,CAACZ,IAAI,EAAE;IAClB,OAAO,IAAI9H,IAAI,CAAC8F,IAAI,CAAC0B,KAAK,CAACM,IAAI,CAACnQ,CAAC,GAAG,GAAG,CAAC,EAAEmO,IAAI,CAAC0B,KAAK,CAACM,IAAI,CAAClQ,CAAC,GAAG,GAAG,CAAC,EAAEkO,IAAI,CAAC0B,KAAK,CAACM,IAAI,CAACjQ,CAAC,GAAG,GAAG,CAAC,EAAEiO,IAAI,CAAC0B,KAAK,CAACM,IAAI,CAAChQ,CAAC,GAAG,GAAG,CAAC,EAAEgQ,IAAI,CAACrR,CAAC,CAAC;EACnI;EACAkS,eAAeA,CAACX,IAAI,EAAE;IAClB,OAAO,IAAIrI,IAAI,CAACmG,IAAI,CAAC0C,KAAK,CAACR,IAAI,CAAC5N,CAAC,GAAG,GAAG,CAAC,EAAE0L,IAAI,CAAC0C,KAAK,CAACR,IAAI,CAAC3N,CAAC,GAAG,GAAG,CAAC,EAAEyL,IAAI,CAAC0C,KAAK,CAACR,IAAI,CAAC1N,CAAC,GAAG,GAAG,CAAC,EAAE0N,IAAI,CAACvR,CAAC,CAAC;EACzG;EACAmS,YAAYA,CAACC,WAAW,GAAG,EAAE,EAAET,SAAS,GAAG,KAAK,EAAE;IAC9C,IAAIlB,IAAI,GAAG,IAAI;IACf2B,WAAW,GAAG,CAACA,WAAW,IAAI,EAAE,EAAE7G,WAAW,CAAC,CAAC;IAC/C,MAAM8G,aAAa,GAAG,CAClB;MACIC,EAAE,EAAE,2FAA2F;MAC/FC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;QACzB,OAAO,IAAItJ,IAAI,CAACwC,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAEC,KAAK,CAACnG,UAAU,CAACkG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGlG,UAAU,CAACkG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9L;IACJ,CAAC,EAAE;MACCF,EAAE,EAAE,yFAAyF;MAC7FC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;QACzB,OAAO,IAAIlJ,IAAI,CAACoC,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAEC,KAAK,CAACnG,UAAU,CAACkG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGlG,UAAU,CAACkG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9L;IACJ,CAAC,CACJ;IACD,IAAIb,SAAS,EAAE;MACXU,aAAa,CAACK,IAAI,CAAC;QACfJ,EAAE,EAAE,qEAAqE;QACzEC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;UACzB,OAAO,IAAItJ,IAAI,CAACwC,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;QACvK;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACDH,aAAa,CAACK,IAAI,CAAC;QACfJ,EAAE,EAAE,oDAAoD;QACxDC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;UACzB,OAAO,IAAItJ,IAAI,CAACwC,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QAC/H;MACJ,CAAC,CAAC;IACN;IACAH,aAAa,CAACK,IAAI,CAAC;MACfJ,EAAE,EAAE,2CAA2C;MAC/CC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;QACzB,OAAO,IAAItJ,IAAI,CAACwC,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE9G,QAAQ,CAAC8G,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;MAC/K;IACJ,CAAC,CAAC;IACF,KAAK,MAAMG,GAAG,IAAIN,aAAa,EAAE;MAC7B,IAAIA,aAAa,CAACO,cAAc,CAACD,GAAG,CAAC,EAAE;QACnC,MAAME,MAAM,GAAGR,aAAa,CAACM,GAAG,CAAC;QACjC,MAAMG,KAAK,GAAGD,MAAM,CAACP,EAAE,CAACS,IAAI,CAACX,WAAW,CAAC;UAAEY,KAAK,GAAGF,KAAK,IAAID,MAAM,CAACN,KAAK,CAACO,KAAK,CAAC;QAC/E,IAAIE,KAAK,EAAE;UACP,IAAIA,KAAK,YAAY9J,IAAI,EAAE;YACvBuH,IAAI,GAAG,IAAI,CAACe,UAAU,CAACwB,KAAK,CAAC;UACjC,CAAC,MACI,IAAIA,KAAK,YAAY1J,IAAI,EAAE;YAC5BmH,IAAI,GAAG,IAAI,CAACE,SAAS,CAACqC,KAAK,CAAC;UAChC;UACA,OAAOvC,IAAI;QACf;MACJ;IACJ;IACA,OAAOA,IAAI;EACf;EACAwC,YAAYA,CAACxC,IAAI,EAAEwC,YAAY,EAAEC,YAAY,EAAE;IAC3C,IAAID,YAAY,KAAK,MAAM,EAAE;MACzBA,YAAY,GAAGxC,IAAI,CAACzQ,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;IAC9C;IACA,QAAQiT,YAAY;MAChB,KAAK,MAAM;QACP,MAAMrC,IAAI,GAAG,IAAI,CAACJ,SAAS,CAACC,IAAI,CAAC;QACjC,MAAM9O,QAAQ,GAAG,IAAI2H,IAAI,CAAC+F,IAAI,CAAC0C,KAAK,CAAEnB,IAAI,CAACpO,CAAC,GAAI,GAAG,CAAC,EAAE6M,IAAI,CAAC0C,KAAK,CAACnB,IAAI,CAACpS,CAAC,GAAG,GAAG,CAAC,EAAE6Q,IAAI,CAAC0C,KAAK,CAACnB,IAAI,CAACnO,CAAC,GAAG,GAAG,CAAC,EAAE4M,IAAI,CAAC0C,KAAK,CAACnB,IAAI,CAAC5Q,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACzI,IAAIyQ,IAAI,CAACzQ,CAAC,GAAG,CAAC,IAAIkT,YAAY,KAAK,QAAQ,EAAE;UACzC,OAAO,OAAO,GAAGvR,QAAQ,CAACa,CAAC,GAAG,GAAG,GAAGb,QAAQ,CAACnD,CAAC,GAAG,IAAI,GAAGmD,QAAQ,CAACc,CAAC,GAAG,IAAI,GACrEd,QAAQ,CAAC3B,CAAC,GAAG,GAAG;QACxB,CAAC,MACI;UACD,OAAO,MAAM,GAAG2B,QAAQ,CAACa,CAAC,GAAG,GAAG,GAAGb,QAAQ,CAACnD,CAAC,GAAG,IAAI,GAAGmD,QAAQ,CAACc,CAAC,GAAG,IAAI;QAC5E;MACJ,KAAK,MAAM;QACP,MAAM8O,IAAI,GAAG,IAAI,CAACW,eAAe,CAAC,IAAI,CAACrB,UAAU,CAACJ,IAAI,CAAC,CAAC;QACxD,IAAIA,IAAI,CAACzQ,CAAC,GAAG,CAAC,IAAIkT,YAAY,KAAK,QAAQ,EAAE;UACzC,OAAO,OAAO,GAAG3B,IAAI,CAAC5N,CAAC,GAAG,GAAG,GAAG4N,IAAI,CAAC3N,CAAC,GAAG,GAAG,GAAG2N,IAAI,CAAC1N,CAAC,GAAG,GAAG,GACvDwL,IAAI,CAAC0C,KAAK,CAACR,IAAI,CAACvR,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;QAC5C,CAAC,MACI;UACD,OAAO,MAAM,GAAGuR,IAAI,CAAC5N,CAAC,GAAG,GAAG,GAAG4N,IAAI,CAAC3N,CAAC,GAAG,GAAG,GAAG2N,IAAI,CAAC1N,CAAC,GAAG,GAAG;QAC9D;MACJ;QACI,MAAM8N,SAAS,GAAIuB,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,QAAS;QAC1E,OAAO,IAAI,CAACxB,SAAS,CAAC,IAAI,CAACQ,eAAe,CAAC,IAAI,CAACrB,UAAU,CAACJ,IAAI,CAAC,CAAC,EAAEkB,SAAS,CAAC;IACrF;EACJ;AAGJ;AAACwB,mBAAA,GAtNKhD,kBAAkB;AAAA/G,eAAA,CAAlB+G,kBAAkB,wBAAAiD,4BAAA3G,iBAAA;EAAA,YAAAA,iBAAA,IAoN+E0D,mBAAkB;AAAA;AAAA/G,eAAA,CApNnH+G,kBAAkB,+BArIyD1T,EAAE,CAAA4W,kBAAA;EAAAC,KAAA,EA0VwBnD,mBAAkB;EAAAoD,OAAA,EAAlBpD,mBAAkB,CAAAqD;AAAA;AAE7H;EAAA,QAAApG,SAAA,oBAAAA,SAAA,KA5ViF3Q,EAAE,CAAA4Q,iBAAA,CA4VQ8C,kBAAkB,EAAc,CAAC;IAChHxD,IAAI,EAAEtE;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA,MAAMoL,cAAc,GAAG,OAAOpJ,MAAM,KAAK,WAAW,IAAI,cAAc,IAAIA,MAAM;AAChF,MAAMqJ,oBAAoB,CAAC;EAqFvBC,SAASA,CAAC7H,KAAK,EAAE;IACb,IAAI,IAAI,CAAC8H,IAAI,IAAI,IAAI,CAACtD,eAAe,KAAK,OAAO,EAAE;MAC/C,IAAI,CAACrJ,aAAa,CAAC6E,KAAK,CAAC;IAC7B;EACJ;EACA+H,WAAWA,CAAC/H,KAAK,EAAE;IACf,IAAI,IAAI,CAAC8H,IAAI,IAAI,IAAI,CAACtD,eAAe,KAAK,OAAO,EAAE;MAC/C,IAAI,CAAC1Q,aAAa,CAACkM,KAAK,CAAC;IAC7B;EACJ;EACA3C,WAAWA,CAAC2K,MAAM,EAAElG,KAAK,EAAEmG,KAAK,EAAExJ,QAAQ,EAAEyJ,UAAU,EAAEC,OAAO,EAAE;IAAA7K,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,iBAxFxD,KAAK;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,0BAeI,EAAE;IAAAA,eAAA,4BACA,EAAE;IAAAA,eAAA,4BACF,CAChBH,YAAY,CAACiL,GAAG,EAChBjL,YAAY,CAACkL,IAAI,EACjBlL,YAAY,CAACmL,IAAI,EACjBnL,YAAY,CAACoL,IAAI,CACpB;IAAAjL,eAAA,+BACsB,KAAK;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAkExB,IAAI,CAAC0K,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAClG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACmG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACxJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACyJ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACK,mBAAmB,GAAGxL,iBAAiB,CAAC,IAAI,CAACkL,UAAU,CAAC,IAAI,YAAY,IAAI,IAAI,CAACzJ,QAAQ,CAACgK,WAAW;EAC9G;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAClW,MAAM,GAAG,IAAI2R,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5C,MAAMwE,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAChG,aAAa,CAACQ,WAAW,IAAI,GAAG;IAChE,MAAMyF,UAAU,GAAG,IAAI,CAACC,WAAW,CAAClG,aAAa,CAACQ,WAAW,IAAI,GAAG;IACpE,IAAI,CAAC2F,YAAY,GAAG,IAAI3E,eAAe,CAACuE,QAAQ,EAAE,IAAI,CAACK,OAAO,EAAE,GAAG,EAAEH,UAAU,CAAC;IAChF,IAAI,IAAI,CAACI,aAAa,EAAE;MACpB,IAAI,CAAC9T,MAAM,GAAGgI,YAAY,CAACoL,IAAI;IACnC,CAAC,MACI,IAAI,IAAI,CAACW,cAAc,KAAK,MAAM,EAAE;MACrC,IAAI,CAAC/T,MAAM,GAAGgI,YAAY,CAACkL,IAAI;IACnC,CAAC,MACI,IAAI,IAAI,CAACa,cAAc,KAAK,MAAM,EAAE;MACrC,IAAI,CAAC/T,MAAM,GAAGgI,YAAY,CAACmL,IAAI;IACnC,CAAC,MACI;MACD,IAAI,CAACnT,MAAM,GAAGgI,YAAY,CAACiL,GAAG;IAClC;IACA,IAAI,CAACe,iBAAiB,GAAInJ,KAAK,IAAK;MAAE,IAAI,CAACoJ,WAAW,CAACpJ,KAAK,CAAC;IAAE,CAAC;IAChE,IAAI,CAACqJ,cAAc,GAAG,MAAM;MAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;IAAE,CAAC;IAChD,IAAI,CAACC,UAAU,CAAC,IAAI,CAACC,YAAY,EAAE,KAAK,CAAC;EAC7C;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChF,WAAW,CAAC,CAAC;EACtB;EACAiF,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACV,OAAO,KAAK,GAAG,IAAI,IAAI,CAACxE,eAAe,KAAK,QAAQ,EAAE;MAC3D,MAAMmE,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAChG,aAAa,CAACQ,WAAW,IAAI,GAAG;MAChE,MAAMyF,UAAU,GAAG,IAAI,CAACC,WAAW,CAAClG,aAAa,CAACQ,WAAW,IAAI,GAAG;MACpE,IAAI,CAAC2F,YAAY,GAAG,IAAI3E,eAAe,CAACuE,QAAQ,EAAE,IAAI,CAACK,OAAO,EAAE,GAAG,EAAEH,UAAU,CAAC;MAChF,IAAI,CAACc,iBAAiB,CAAC,KAAK,CAAC;MAC7B,IAAI,CAAC1B,KAAK,CAAC2B,aAAa,CAAC,CAAC;IAC9B;EACJ;EACAL,UAAUA,CAACrC,KAAK,EAAE5G,IAAI,GAAG,IAAI,EAAE;IAC3B,IAAI,CAAC6H,OAAO,CAAC7D,SAAS,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAC,IAAI,CAACtG,KAAK,EAAE;MACb,IAAI,CAACgL,OAAO,GAAG,IAAI,CAACa,mBAAmB,CAACjH,aAAa,CAACQ,WAAW;IACrE;IACA,IAAI,CAAC,IAAI,CAACrF,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,GAAG;IACrB;IACA,IAAI,CAAC+L,eAAe,CAAC5C,KAAK,CAAC;IAC3B,IAAI,CAAC5M,kBAAkB,CAAC4M,KAAK,EAAE5G,IAAI,CAAC;IACpC,IAAI,CAACyJ,eAAe,CAAC,CAAC;EAC1B;EACAtF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuF,gBAAgB,CAAC,CAAC;EAC3B;EACAC,WAAWA,CAACC,QAAQ,EAAEC,UAAU,EAAEjD,KAAK,EAAE8B,OAAO,EAAEoB,QAAQ,EAAE5F,eAAe,EAAE6F,eAAe,EAAEC,WAAW,EAAErB,aAAa,EAAEzT,cAAc,EAAE0T,cAAc,EAAEqB,cAAc,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,sBAAsB,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,yBAAyB,EAAE/P,aAAa,EAAE1H,cAAc,EAAEoH,mBAAmB,EAAElH,uBAAuB,EAAEsH,oBAAoB,EAAEF,yBAAyB,EAAEkB,UAAU,EAAEJ,eAAe,EAAEC,cAAc,EAAEE,cAAc,EAAER,mBAAmB,EAAEC,kBAAkB,EAAEd,gBAAgB,EAAEnH,qBAAqB,EAAEK,oBAAoB,EAAEyG,wBAAwB,EAAE6Q,YAAY,EAAEC,gBAAgB,EAAE/O,eAAe,EAAE;IACjqB,IAAI,CAAC6N,eAAe,CAAC5C,KAAK,CAAC;IAC3B,IAAI,CAAC+D,YAAY,CAACX,WAAW,CAAC;IAC9B,IAAI,CAACY,MAAM,GAAI7L,QAAQ,CAAC,CAAC,KAAK,EAAG;IACjC,IAAI,CAAC8L,iBAAiB,GAAGjB,QAAQ;IACjC,IAAI,CAACL,mBAAmB,GAAGM,UAAU;IACrC,IAAI,CAACI,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACtB,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACzT,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC0T,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC1E,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACgG,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACU,oBAAoB,GAAGT,sBAAsB;IAClD,IAAI,CAAC3M,KAAK,GAAG,IAAI,CAACgL,OAAO,GAAGpJ,QAAQ,CAACoJ,OAAO,EAAE,EAAE,CAAC;IACjD,IAAI,CAACjL,MAAM,GAAG,IAAI,CAACqM,QAAQ,GAAGxK,QAAQ,CAACwK,QAAQ,EAAE,EAAE,CAAC;IACpD,IAAI,CAACQ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAGjL,QAAQ,CAACiL,gBAAgB,EAAE,EAAE,CAAC;IACtD,IAAI,CAAChP,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACP,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACD,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAC2P,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACM,aAAa,GAAGhB,eAAe,IAAI,MAAM;IAC9C,IAAI,CAACiB,eAAe,CAACvQ,aAAa,EAAE1H,cAAc,CAAC;IACnD,IAAI,CAACoH,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAClH,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACsH,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACF,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACJ,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC9G,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACL,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAAC8G,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAAC8Q,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC/O,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC6O,yBAAyB,EAAE;MAC5B,IAAI,CAACS,iBAAiB,GAAG,CAAC;IAC9B;IACA,IAAI/G,eAAe,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAACgH,eAAe,GAAG,CAAC;MACxB,IAAI,CAACD,iBAAiB,GAAG,CAAC;IAC9B;IACA,IAAIrC,cAAc,KAAK,KAAK,IACxB1T,cAAc,KAAK,QAAQ,IAAIA,cAAc,KAAK,QAAQ,EAAE;MAC5D,IAAI,CAACA,cAAc,GAAG,UAAU;IACpC;EACJ;EACAyV,YAAYA,CAACQ,IAAI,EAAE;IACf,QAAQA,IAAI,CAAC1F,QAAQ,CAAC,CAAC,CAAC2F,WAAW,CAAC,CAAC;MACjC,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,OAAO;QACR,IAAI,CAACpB,WAAW,GAAG,CAAC;QACpB;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,WAAW;QACZ,IAAI,CAACA,WAAW,GAAG,CAAC;QACpB;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,SAAS;QACV,IAAI,CAACA,WAAW,GAAG,CAAC;QACpB;MACJ;QACI,IAAI,CAACA,WAAW,GAAG,CAAC;IAC5B;EACJ;EACAR,eAAeA,CAAC5C,KAAK,EAAE;IACnB,IAAI,CAACsC,YAAY,GAAGtC,KAAK;EAC7B;EACAoE,eAAeA,CAACvQ,aAAa,EAAE1H,cAAc,EAAE;IAC3C,IAAI,CAAC0H,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC1H,cAAc,GAAGA,cAAc;EACxC;EACAiH,kBAAkBA,CAAC2F,KAAK,EAAEK,IAAI,GAAG,IAAI,EAAEqL,MAAM,GAAG,IAAI,EAAE;IAClD,IAAIhH,IAAI;IACR,IAAI,IAAI,CAACnP,cAAc,KAAK,QAAQ,IAAI,IAAI,CAACA,cAAc,KAAK,QAAQ,EAAE;MACtEmP,IAAI,GAAG,IAAI,CAACwD,OAAO,CAAC9B,YAAY,CAACpG,KAAK,EAAE,IAAI,CAAC;MAC7C,IAAI,CAAC0E,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,EAAE;QACrBA,IAAI,GAAG,IAAI,CAACwD,OAAO,CAAC9B,YAAY,CAACpG,KAAK,EAAE,KAAK,CAAC;MAClD;IACJ,CAAC,MACI;MACD0E,IAAI,GAAG,IAAI,CAACwD,OAAO,CAAC9B,YAAY,CAACpG,KAAK,EAAE,KAAK,CAAC;IAClD;IACA,IAAI,CAAC0E,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,EAAE;MACrBA,IAAI,GAAG,IAAI,CAACwD,OAAO,CAAC9B,YAAY,CAAC,IAAI,CAACgF,aAAa,EAAE,KAAK,CAAC;IAC/D;IACA,IAAI1G,IAAI,EAAE;MACN,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACiH,OAAO,GAAG,IAAI,CAACjH,IAAI,CAACjO,CAAC;MAC1B,IAAI,IAAI,CAACwS,cAAc,KAAK,KAAK,IAAI,IAAI,CAAC1T,cAAc,KAAK,UAAU,EAAE;QACrE,IAAI,CAACmP,IAAI,CAACzQ,CAAC,GAAG,CAAC;MACnB;MACA,IAAI,CAACyV,iBAAiB,CAACrJ,IAAI,EAAEqL,MAAM,CAAC;IACxC;EACJ;EACArC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC3G,QAAQ,KAAK,OAAO,EAAE;MAC3B,IAAI,CAACkJ,iBAAiB,CAAC,CAAC;IAC5B,CAAC,MACI,IAAI,IAAI,CAACrH,eAAe,KAAK,QAAQ,EAAE;MACxC,IAAI,CAACwF,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACA7X,SAASA,CAACK,MAAM,EAAE;IACd,IAAI,CAAC2Y,iBAAiB,CAACW,aAAa,CAAC;MAAEtZ,MAAM,EAAEA,MAAM;MAAE0U,KAAK,EAAE,IAAI,CAAC6E;IAAY,CAAC,CAAC;EACrF;EACA9Z,WAAWA,CAACO,MAAM,EAAE;IAChB,IAAI,CAAC2Y,iBAAiB,CAACa,eAAe,CAAC;MAAExZ,MAAM,EAAEA,MAAM;MAAE0U,KAAK,EAAE,IAAI,CAAC6E;IAAY,CAAC,CAAC;EACvF;EACA3C,WAAWA,CAACpJ,KAAK,EAAE;IACf,IAAI,IAAI,CAAC8H,IAAI,IACT,CAAC,IAAI,CAACoD,MAAM,IACZ,IAAI,CAAC1G,eAAe,KAAK,OAAO,IAChCxE,KAAK,CAACE,MAAM,KAAK,IAAI,CAAC2J,mBAAmB,CAACjH,aAAa,IACvD,CAAC,IAAI,CAACqJ,YAAY,CAAC,IAAI,CAACnK,KAAK,CAACc,aAAa,EAAE5C,KAAK,CAACE,MAAM,CAAC,IAC1D,CAAC,IAAI,CAAC+L,YAAY,CAAC,IAAI,CAACpC,mBAAmB,CAACjH,aAAa,EAAE5C,KAAK,CAACE,MAAM,CAAC,IACxE,IAAI,CAACsK,iBAAiB,CAAC0B,MAAM,CAAEC,IAAI,IAAKA,IAAI,KAAKnM,KAAK,CAACE,MAAM,CAAC,CAAC5M,MAAM,KAAK,CAAC,EAAE;MAC7E,IAAI,CAAC0U,MAAM,CAACoE,GAAG,CAAC,MAAM;QAClB,IAAI,IAAI,CAAC3B,kBAAkB,EAAE;UACzB,IAAI,CAACU,iBAAiB,CAACkB,aAAa,CAAC,IAAI,CAACN,WAAW,CAAC;QAC1D,CAAC,MACI;UACD,IAAI,CAACpH,IAAI,GAAG,IAAI;UAChB,IAAI,CAACrK,kBAAkB,CAAC,IAAI,CAACkP,YAAY,EAAE,KAAK,CAAC;UACjD,IAAI,IAAI,CAACP,aAAa,EAAE;YACpB,IAAI,CAACkC,iBAAiB,CAACmB,WAAW,CAAC,IAAI,CAACC,SAAS,CAAC;UACtD;UACA,IAAI,CAACpB,iBAAiB,CAACqB,YAAY,CAAC,IAAI,CAAChD,YAAY,CAAC;UACtD,IAAI,CAAC2B,iBAAiB,CAACsB,aAAa,CAAC,CAAC;QAC1C;QACA,IAAI,IAAI,CAAC/B,mBAAmB,EAAE;UAC1B,IAAI,CAACV,gBAAgB,CAAC,CAAC;QAC3B;MACJ,CAAC,CAAC;IACN;EACJ;EACAlW,aAAaA,CAACkM,KAAK,EAAE;IACjBA,KAAK,CAACqC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAAC0J,WAAW,EAAE;MAClB,IAAI,CAACZ,iBAAiB,CAACkB,aAAa,CAAC,IAAI,CAACN,WAAW,CAAC;IAC1D;IACA,IAAI,IAAI,CAACvH,eAAe,KAAK,OAAO,EAAE;MAClC,IAAI,CAACwF,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACA7O,aAAaA,CAAC6E,KAAK,EAAE;IACjB,IAAI,CAAC2E,IAAI,GAAG,IAAI;IAChB3E,KAAK,CAACqC,eAAe,CAAC,CAAC;IACvB,IAAI,CAAC8I,iBAAiB,CAACsB,aAAa,CAAC,CAAC;IACtC,IAAI,CAACnS,kBAAkB,CAAC,IAAI,CAACkP,YAAY,EAAE,IAAI,CAAC;IAChD,IAAI,IAAI,CAAChF,eAAe,KAAK,OAAO,EAAE;MAClC,IAAI,IAAI,CAACyE,aAAa,EAAE;QACpB,IAAI,CAACkC,iBAAiB,CAACmB,WAAW,CAAC,IAAI,CAACC,SAAS,CAAC;MACtD;MACA,IAAI,CAACpB,iBAAiB,CAACqB,YAAY,CAAC,IAAI,CAAChD,YAAY,EAAE,IAAI,CAAC;MAC5D,IAAI,CAACQ,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACA0C,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAClE,mBAAmB,EACzB;IACJ,MAAMmE,UAAU,GAAG,IAAIpO,MAAM,CAACqO,UAAU,CAAC,CAAC;IAC1CD,UAAU,CAACE,IAAI,CAAC,CAAC,CAACC,IAAI,CAAEC,gBAAgB,IAAK;MACzC,IAAI,CAACzS,kBAAkB,CAACyS,gBAAgB,CAACC,OAAO,EAAE,IAAI,CAAC;IAC3D,CAAC,CAAC;EACN;EACAtT,cAAcA,CAACuT,MAAM,EAAE;IACnB,MAAMC,gBAAgB,GAAG,IAAI,CAACC,iBAAiB,CAAC7Z,MAAM,IACjD,IAAI,CAAC2V,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,MAAMmE,UAAU,GAAG,CAAE,CAAC,IAAI,CAACD,iBAAiB,CAACxN,OAAO,CAAC,IAAI,CAACxK,MAAM,CAAC,GAAG8X,MAAM,IACtEC,gBAAgB,GAAIA,gBAAgB,IAAIA,gBAAgB;IAC5D,IAAI,CAAC/X,MAAM,GAAG,IAAI,CAACgY,iBAAiB,CAACC,UAAU,CAAC;EACpD;EACArb,aAAaA,CAACkO,KAAK,EAAE;IACjB,IAAI,CAAC0E,IAAI,CAACjS,CAAC,GAAGuN,KAAK,CAACvN,CAAC,GAAGuN,KAAK,CAACyD,GAAG;IACjC,IAAI,CAACiB,IAAI,CAAClS,CAAC,GAAGwN,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAAC0D,GAAG;IACjC,IAAI,CAACgG,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACwB,iBAAiB,CAACkC,aAAa,CAAC;MACjC7a,MAAM,EAAE,WAAW;MACnByN,KAAK,EAAE,IAAI,CAAC0E,IAAI,CAAClS,CAAC;MAClByU,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;IACF,IAAI,CAACZ,iBAAiB,CAACkC,aAAa,CAAC;MACjC7a,MAAM,EAAE,YAAY;MACpByN,KAAK,EAAE,IAAI,CAAC0E,IAAI,CAACjS,CAAC;MAClBwU,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAuB,WAAWA,CAACrN,KAAK,EAAE;IACf,IAAI,CAAC0E,IAAI,CAACjO,CAAC,GAAGuJ,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACyD,GAAG;IACjC,IAAI,CAACkI,OAAO,GAAG,IAAI,CAACjH,IAAI,CAACjO,CAAC;IAC1B,IAAI,CAACiT,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACwB,iBAAiB,CAACkC,aAAa,CAAC;MACjC7a,MAAM,EAAE,KAAK;MACbyN,KAAK,EAAE,IAAI,CAAC0E,IAAI,CAACjO,CAAC;MAClBwQ,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAwB,aAAaA,CAACtN,KAAK,EAAE;IACjB,IAAI,CAAC0E,IAAI,CAAClS,CAAC,GAAGwN,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACyD,GAAG;IACjC,IAAI,CAACiG,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACwB,iBAAiB,CAACkC,aAAa,CAAC;MACjC7a,MAAM,EAAE,OAAO;MACfyN,KAAK,EAAE,IAAI,CAAC0E,IAAI,CAAClS,CAAC;MAClByU,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAyB,aAAaA,CAACvN,KAAK,EAAE;IACjB,IAAI,CAAC0E,IAAI,CAACzQ,CAAC,GAAG+L,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACyD,GAAG;IACjC,IAAI,CAACiG,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACwB,iBAAiB,CAACkC,aAAa,CAAC;MACjC7a,MAAM,EAAE,OAAO;MACfyN,KAAK,EAAE,IAAI,CAAC0E,IAAI,CAACzQ,CAAC;MAClBgT,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAtT,UAAUA,CAACwH,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,EAAE;MAChB,IAAI,CAAC0J,iBAAiB,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,IAAI1J,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC3BA,KAAK,GAAG,GAAG,GAAGA,KAAK;MACvB;MACA,IAAIwN,QAAQ,GAAG,gCAAgC;MAC/C,IAAI,IAAI,CAACjY,cAAc,KAAK,QAAQ,EAAE;QAClCiY,QAAQ,GAAG,4CAA4C;MAC3D;MACA,MAAMC,KAAK,GAAGD,QAAQ,CAACE,IAAI,CAAC1N,KAAK,CAAC;MAClC,IAAIyN,KAAK,EAAE;QACP,IAAIzN,KAAK,CAAC3M,MAAM,GAAG,CAAC,EAAE;UAClB2M,KAAK,GAAG,GAAG,GAAGA,KAAK,CAACJ,SAAS,CAAC,CAAC,CAAC,CAC3B+N,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAACzY,CAAC,IAAIA,CAAC,GAAGA,CAAC,CAAC,CACf0Y,IAAI,CAAC,EAAE,CAAC;QACjB;QACA,IAAI,IAAI,CAACtY,cAAc,KAAK,QAAQ,EAAE;UAClCyK,KAAK,IAAIsD,IAAI,CAAC0C,KAAK,CAAC,IAAI,CAACtB,IAAI,CAACzQ,CAAC,GAAG,GAAG,CAAC,CAAC6R,QAAQ,CAAC,EAAE,CAAC;QACvD;QACA,IAAI,CAACzL,kBAAkB,CAAC2F,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;MAC/C;MACA,IAAI,CAACkL,iBAAiB,CAAC4C,YAAY,CAAC;QAChCC,KAAK,EAAE,KAAK;QACZN,KAAK,EAAEA,KAAK;QACZzN,KAAK,EAAEA,KAAK;QACZiH,KAAK,EAAE,IAAI,CAAC6E;MAChB,CAAC,CAAC;IACN;EACJ;EACAzU,UAAUA,CAAC2I,KAAK,EAAE;IACd,MAAMwF,IAAI,GAAG,IAAI,CAAC0C,OAAO,CAACpD,UAAU,CAAC,IAAI,CAACJ,IAAI,CAAC;IAC/C,MAAM+I,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACPjI,IAAI,CAAC5N,CAAC,GAAGoI,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACE,EAAE;MAC3B,IAAI,CAACwE,IAAI,GAAG,IAAI,CAACwD,OAAO,CAACzC,UAAU,CAACD,IAAI,CAAC;MACzC,IAAI,CAACmG,OAAO,GAAG,IAAI,CAACjH,IAAI,CAACjO,CAAC;MAC1B,IAAI,CAACiT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,KAAK;MACZN,KAAK,EAAEA,KAAK;MACZzN,KAAK,EAAEwF,IAAI,CAAC5N,CAAC;MACbqP,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAnU,WAAWA,CAACqI,KAAK,EAAE;IACf,MAAMwF,IAAI,GAAG,IAAI,CAAC0C,OAAO,CAACpD,UAAU,CAAC,IAAI,CAACJ,IAAI,CAAC;IAC/C,MAAM+I,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACPjI,IAAI,CAAC1N,CAAC,GAAGkI,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACE,EAAE;MAC3B,IAAI,CAACwE,IAAI,GAAG,IAAI,CAACwD,OAAO,CAACzC,UAAU,CAACD,IAAI,CAAC;MACzC,IAAI,CAACmG,OAAO,GAAG,IAAI,CAACjH,IAAI,CAACjO,CAAC;MAC1B,IAAI,CAACiT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,MAAM;MACbN,KAAK,EAAEA,KAAK;MACZzN,KAAK,EAAEwF,IAAI,CAAC1N,CAAC;MACbmP,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAtU,YAAYA,CAACwI,KAAK,EAAE;IAChB,MAAMwF,IAAI,GAAG,IAAI,CAAC0C,OAAO,CAACpD,UAAU,CAAC,IAAI,CAACJ,IAAI,CAAC;IAC/C,MAAM+I,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACPjI,IAAI,CAAC3N,CAAC,GAAGmI,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACE,EAAE;MAC3B,IAAI,CAACwE,IAAI,GAAG,IAAI,CAACwD,OAAO,CAACzC,UAAU,CAACD,IAAI,CAAC;MACzC,IAAI,CAACmG,OAAO,GAAG,IAAI,CAACjH,IAAI,CAACjO,CAAC;MAC1B,IAAI,CAACiT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,OAAO;MACdN,KAAK,EAAEA,KAAK;MACZzN,KAAK,EAAEwF,IAAI,CAAC3N,CAAC;MACboP,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACA5V,UAAUA,CAAC8J,KAAK,EAAE;IACd,MAAMyN,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACP,IAAI,CAAC/I,IAAI,CAACjO,CAAC,GAAGuJ,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACE,EAAE;MAChC,IAAI,CAACyL,OAAO,GAAG,IAAI,CAACjH,IAAI,CAACjO,CAAC;MAC1B,IAAI,CAACiT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,KAAK;MACZN,KAAK,EAAEA,KAAK;MACZzN,KAAK,EAAE,IAAI,CAAC0E,IAAI,CAACjO,CAAC;MAClBwQ,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAzS,YAAYA,CAAC2G,KAAK,EAAE;IAChB,MAAMyN,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACP,IAAI,CAAC/I,IAAI,CAAClS,CAAC,GAAGwN,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACE,EAAE;MAChC,IAAI,CAACwJ,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,OAAO;MACdN,KAAK,EAAEA,KAAK;MACZzN,KAAK,EAAE,IAAI,CAAC0E,IAAI,CAAClS,CAAC;MAClByU,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACA/X,YAAYA,CAACiM,KAAK,EAAE;IAChB,MAAMyN,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACP,IAAI,CAAC/I,IAAI,CAACzQ,CAAC,GAAG+L,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACE,EAAE;MAChC,IAAI,CAACwJ,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,OAAO;MACdN,KAAK,EAAEA,KAAK;MACZzN,KAAK,EAAE,IAAI,CAAC0E,IAAI,CAACzQ,CAAC;MAClBgT,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAtV,gBAAgBA,CAACwJ,KAAK,EAAE;IACpB,MAAM6E,IAAI,GAAG,IAAI,CAACqD,OAAO,CAACzD,SAAS,CAAC,IAAI,CAACC,IAAI,CAAC;IAC9C,MAAM+I,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACP5I,IAAI,CAACnO,CAAC,GAAGsJ,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACE,EAAE;MAC3B,IAAI,CAACwE,IAAI,GAAG,IAAI,CAACwD,OAAO,CAACtD,SAAS,CAACC,IAAI,CAAC;MACxC,IAAI,CAAC8G,OAAO,GAAG,IAAI,CAACjH,IAAI,CAACjO,CAAC;MAC1B,IAAI,CAACiT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,WAAW;MAClBN,KAAK,EAAEA,KAAK;MACZzN,KAAK,EAAE6E,IAAI,CAACnO,CAAC;MACbuQ,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAzV,iBAAiBA,CAAC2J,KAAK,EAAE;IACrB,MAAM6E,IAAI,GAAG,IAAI,CAACqD,OAAO,CAACzD,SAAS,CAAC,IAAI,CAACC,IAAI,CAAC;IAC9C,MAAM+I,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACP5I,IAAI,CAACpS,CAAC,GAAGuN,KAAK,CAACxN,CAAC,GAAGwN,KAAK,CAACE,EAAE;MAC3B,IAAI,CAACwE,IAAI,GAAG,IAAI,CAACwD,OAAO,CAACtD,SAAS,CAACC,IAAI,CAAC;MACxC,IAAI,CAAC8G,OAAO,GAAG,IAAI,CAACjH,IAAI,CAACjO,CAAC;MAC1B,IAAI,CAACiT,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,YAAY;MACnBN,KAAK,EAAEA,KAAK;MACZzN,KAAK,EAAE6E,IAAI,CAACpS,CAAC;MACbwU,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAvX,WAAWA,CAACyL,KAAK,EAAE;IACf,MAAMyN,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACP,IAAI,CAACnI,IAAI,CAACnQ,CAAC,GAAG6K,KAAK,CAACxN,CAAC;MACrB,IAAI,CAACkX,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7C;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,MAAM;MACbN,KAAK,EAAE,IAAI;MACXzN,KAAK,EAAE,IAAI,CAACsF,IAAI,CAACnQ,CAAC;MAClB8R,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACApX,cAAcA,CAACsL,KAAK,EAAE;IAClB,MAAMyN,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACP,IAAI,CAACnI,IAAI,CAAClQ,CAAC,GAAG4K,KAAK,CAACxN,CAAC;MACrB,IAAI,CAACkX,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7C;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,SAAS;MAChBN,KAAK,EAAE,IAAI;MACXzN,KAAK,EAAE,IAAI,CAACsF,IAAI,CAAClQ,CAAC;MAClB6R,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACAjX,aAAaA,CAACmL,KAAK,EAAE;IACjB,MAAMyN,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACP,IAAI,CAACnI,IAAI,CAACjQ,CAAC,GAAG2K,KAAK,CAACxN,CAAC;MACrB,IAAI,CAACkX,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7C;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,QAAQ;MACfN,KAAK,EAAE,IAAI;MACXzN,KAAK,EAAE,IAAI,CAACsF,IAAI,CAACjQ,CAAC;MAClB4R,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACA9W,YAAYA,CAACgL,KAAK,EAAE;IAChB,MAAMyN,KAAK,GAAG,CAAC/G,KAAK,CAAC1G,KAAK,CAACxN,CAAC,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAI,CAAC,IAAIwN,KAAK,CAACxN,CAAC,IAAIwN,KAAK,CAACE,EAAE;IACpE,IAAIuN,KAAK,EAAE;MACP,IAAI,CAACnI,IAAI,CAAChQ,CAAC,GAAG0K,KAAK,CAACxN,CAAC;MACrB,IAAI,CAACkX,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7C;IACA,IAAI,CAACwB,iBAAiB,CAAC4C,YAAY,CAAC;MAChCC,KAAK,EAAE,OAAO;MACdN,KAAK,EAAE,IAAI;MACXzN,KAAK,EAAE,IAAI,CAACsF,IAAI,CAAChQ,CAAC;MAClB2R,KAAK,EAAE,IAAI,CAAC6E;IAChB,CAAC,CAAC;EACN;EACA/Y,gBAAgBA,CAACgN,KAAK,EAAEC,KAAK,EAAE;IAC3BD,KAAK,CAACqC,eAAe,CAAC,CAAC;IACvB,IAAI,CAAC,IAAI,CAAChP,cAAc,CAAC6Y,MAAM,CAAEhF,KAAK,IAAMA,KAAK,KAAKjH,KAAM,CAAC,CAAC3M,MAAM,EAAE;MAClE,IAAI,CAACD,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC4a,MAAM,CAAChO,KAAK,CAAC;MACvD,IAAI,CAACkL,iBAAiB,CAAC+C,mBAAmB,CAAC,IAAI,CAAC7a,cAAc,CAAC;IACnE;EACJ;EACA4G,mBAAmBA,CAAC+F,KAAK,EAAEC,KAAK,EAAE;IAC9BD,KAAK,CAACqC,eAAe,CAAC,CAAC;IACvB,IAAI,CAAChP,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC6Y,MAAM,CAAEhF,KAAK,IAAMA,KAAK,KAAKjH,KAAM,CAAC;IAC9E,IAAI,CAACkL,iBAAiB,CAAC+C,mBAAmB,CAAC,IAAI,CAAC7a,cAAc,CAAC;EACnE;EACA;EACA0W,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACjC,IAAI,EAAE;MACZ,IAAI,CAACA,IAAI,GAAG,IAAI;MAChB,IAAI,CAACqG,MAAM,GAAG,IAAI;MAClBC,UAAU,CAAC,MAAM;QACb,IAAI,CAACD,MAAM,GAAG,KAAK;QACnB,IAAI,CAACtC,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAAC5D,KAAK,CAAC2B,aAAa,CAAC,CAAC;MAC9B,CAAC,EAAE,CAAC,CAAC;MACL,IAAI,CAACuB,iBAAiB,CAACkD,YAAY,CAAC,IAAI,CAAC;MACzC,IAAI,CAAC,IAAI,CAACnD,MAAM,EAAE;QACd;QACA;QACA,IAAI,CAAClD,MAAM,CAACsG,iBAAiB,CAAC,MAAM;UAChC;UACA;UACA;UACA,IAAI3G,cAAc,EAAE;YAChBlJ,QAAQ,CAAC6D,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC6G,iBAAiB,CAAC;UACnE,CAAC,MACI;YACD1K,QAAQ,CAAC6D,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC6G,iBAAiB,CAAC;UAClE;QACJ,CAAC,CAAC;MACN;MACA5K,MAAM,CAAC+D,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC+G,cAAc,CAAC;IAC1D;EACJ;EACAW,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAAClC,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,GAAG,KAAK;MACjB,IAAI,CAACqD,iBAAiB,CAACkD,YAAY,CAAC,KAAK,CAAC;MAC1C,IAAI,CAAC,IAAI,CAACnD,MAAM,EAAE;QACd,IAAIvD,cAAc,EAAE;UAChBlJ,QAAQ,CAAC+D,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC2G,iBAAiB,CAAC;QACtE,CAAC,MACI;UACD1K,QAAQ,CAAC+D,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC2G,iBAAiB,CAAC;QACrE;MACJ;MACA5K,MAAM,CAACiE,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC6G,cAAc,CAAC;MACzD,IAAI,CAAC,IAAI,CAACpB,KAAK,CAAC,WAAW,CAAC,EAAE;QAC1B,IAAI,CAACA,KAAK,CAAC2B,aAAa,CAAC,CAAC;MAC9B;IACJ;EACJ;EACAD,iBAAiBA,CAACrJ,IAAI,GAAG,IAAI,EAAEqL,MAAM,GAAG,IAAI,EAAE4C,SAAS,GAAG,KAAK,EAAE;IAC7D,IAAI,IAAI,CAACxF,YAAY,EAAE;MACnB,IAAI,IAAI,CAACuB,WAAW,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC3F,IAAI,CAACjS,CAAC,GAAG,CAAC;MACnB;MACA,IAAI8b,GAAG,EAAE1J,IAAI,EAAEW,IAAI;MACnB,MAAMgJ,UAAU,GAAG,IAAI,CAAC1C,WAAW;MACnCjH,IAAI,GAAG,IAAI,CAACqD,OAAO,CAACzD,SAAS,CAAC,IAAI,CAACC,IAAI,CAAC;MACxC,IAAI,CAAC,IAAI,CAACsE,aAAa,EAAE;QACrBxD,IAAI,GAAG,IAAI,CAAC0C,OAAO,CAAC/B,eAAe,CAAC,IAAI,CAAC+B,OAAO,CAACpD,UAAU,CAAC,IAAI,CAACJ,IAAI,CAAC,CAAC;MAC3E,CAAC,MACI;QACD,IAAI,CAAC4J,SAAS,EAAE;UACZ9I,IAAI,GAAG,IAAI,CAAC0C,OAAO,CAACpD,UAAU,CAAC,IAAI,CAACJ,IAAI,CAAC;UACzC,IAAI,CAACY,IAAI,GAAG,IAAI,CAAC4C,OAAO,CAAChC,eAAe,CAAC,IAAI,CAACgC,OAAO,CAAC3C,UAAU,CAACC,IAAI,CAAC,CAAC;QAC3E,CAAC,MACI;UACDA,IAAI,GAAG,IAAI,CAAC0C,OAAO,CAAC7C,SAAS,CAAC,IAAI,CAAC6C,OAAO,CAACjC,aAAa,CAAC,IAAI,CAACX,IAAI,CAAC,CAAC;UACpE,IAAI,CAACZ,IAAI,GAAG,IAAI,CAACwD,OAAO,CAACzC,UAAU,CAACD,IAAI,CAAC;QAC7C;QACAA,IAAI,GAAG,IAAI,CAAC0C,OAAO,CAAC/B,eAAe,CAACX,IAAI,CAAC;QACzC,IAAI,CAACmG,OAAO,GAAG,IAAI,CAACjH,IAAI,CAACjO,CAAC;MAC9B;MACA8X,GAAG,GAAG,IAAI,CAACrG,OAAO,CAAC/B,eAAe,CAAC,IAAI,CAAC+B,OAAO,CAACpD,UAAU,CAAC,IAAIxH,IAAI,CAAC,IAAI,CAACqO,OAAO,IAAI,IAAI,CAACjH,IAAI,CAACjO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3G,IAAIiV,MAAM,EAAE;QACR,IAAI,CAAC9V,QAAQ,GAAG,IAAI2H,IAAI,CAAC+F,IAAI,CAAC0C,KAAK,CAAEnB,IAAI,CAACpO,CAAC,GAAI,GAAG,CAAC,EAAE6M,IAAI,CAAC0C,KAAK,CAACnB,IAAI,CAACpS,CAAC,GAAG,GAAG,CAAC,EAAE6Q,IAAI,CAAC0C,KAAK,CAACnB,IAAI,CAACnO,CAAC,GAAG,GAAG,CAAC,EAAE4M,IAAI,CAAC0C,KAAK,CAACnB,IAAI,CAAC5Q,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACxI,IAAI,CAAC8C,QAAQ,GAAG,IAAIoG,IAAI,CAACqI,IAAI,CAAC5N,CAAC,EAAE4N,IAAI,CAAC3N,CAAC,EAAE2N,IAAI,CAAC1N,CAAC,EAAEwL,IAAI,CAAC0C,KAAK,CAACR,IAAI,CAACvR,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAChF,IAAI,IAAI,CAAC+U,aAAa,EAAE;UACpB,IAAI,CAAChV,QAAQ,GAAG,IAAIwJ,IAAI,CAAC,IAAI,CAAC8H,IAAI,CAACnQ,CAAC,EAAE,IAAI,CAACmQ,IAAI,CAAClQ,CAAC,EAAE,IAAI,CAACkQ,IAAI,CAACjQ,CAAC,EAAE,IAAI,CAACiQ,IAAI,CAAChQ,CAAC,EAAEgO,IAAI,CAAC0C,KAAK,CAAC,IAAI,CAACV,IAAI,CAACrR,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACrH;QACA,MAAM2R,SAAS,GAAG,IAAI,CAACrQ,cAAc,KAAK,QAAQ;QAClD,IAAI,CAACqD,OAAO,GAAG,IAAI,CAACsP,OAAO,CAACvC,SAAS,CAACH,IAAI,EAAEI,SAAS,CAAC;QACtD,IAAI,CAACzN,QAAQ,GAAG,IAAI,CAACpB,QAAQ,CAAC9C,CAAC;MACnC;MACA,IAAI,IAAI,CAACgV,cAAc,KAAK,MAAM,EAAE;QAChC,IAAI,IAAI,CAAC/T,MAAM,KAAKgI,YAAY,CAACkL,IAAI,IAAI,IAAI,CAAClT,MAAM,KAAKgI,YAAY,CAACoL,IAAI,IAAI,IAAI,CAACpT,MAAM,KAAKgI,YAAY,CAACmL,IAAI,EAAE;UAC7G,IAAI,IAAI,CAAC3D,IAAI,CAACzQ,CAAC,GAAG,CAAC,EAAE;YACjB,IAAI,CAACiB,MAAM,GAAG,IAAI,CAACwP,IAAI,CAACzQ,CAAC,GAAG,CAAC,GAAGiJ,YAAY,CAACkL,IAAI,GAAGlL,YAAY,CAACiL,GAAG;UACxE;QACJ;MACJ;MACA,IAAI,CAAC/V,cAAc,GAAG,MAAM,GAAGmc,GAAG,CAAC3W,CAAC,GAAG,GAAG,GAAG2W,GAAG,CAAC1W,CAAC,GAAG,GAAG,GAAG0W,GAAG,CAACzW,CAAC,GAAG,GAAG;MACtE,IAAI,CAAC2W,gBAAgB,GAAG,MAAM,GAAGjJ,IAAI,CAAC5N,CAAC,GAAG,GAAG,GAAG4N,IAAI,CAAC3N,CAAC,GAAG,GAAG,GAAG2N,IAAI,CAAC1N,CAAC,GAAG,GAAG;MAC3E,IAAI,CAACgU,WAAW,GAAG,IAAI,CAAC5D,OAAO,CAAChB,YAAY,CAAC,IAAI,CAACxC,IAAI,EAAE,IAAI,CAACuE,cAAc,EAAE,IAAI,CAAC1T,cAAc,CAAC;MACjG,IAAI,CAACvC,aAAa,GAAG,IAAI,CAACkV,OAAO,CAAChB,YAAY,CAAC,IAAI,CAACxC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;MACvE,IAAI,IAAI,CAACxP,MAAM,KAAKgI,YAAY,CAACoL,IAAI,EAAE;QACnC,IAAI,CAACgE,SAAS,GAAG,EAAE;MACvB,CAAC,MACI;QACD,IAAI,IAAI,CAAC/W,cAAc,KAAK,QAAQ,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,IACrE,IAAI,CAACA,cAAc,KAAK,QAAQ,EAAE;UAClC,MAAMmZ,KAAK,GAAGpL,IAAI,CAAC0C,KAAK,CAAC,IAAI,CAACV,IAAI,CAACrR,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;UACjD,IAAI,CAACqY,SAAS,GAAG,SAAS,IAAI,CAAChH,IAAI,CAACnQ,CAAC,IAAI,IAAI,CAACmQ,IAAI,CAAClQ,CAAC,IAAI,IAAI,CAACkQ,IAAI,CAACjQ,CAAC,IAAI,IAAI,CAACiQ,IAAI,CAAChQ,CAAC,IAAIoZ,KAAK,GAAG;QAClG,CAAC,MACI;UACD,IAAI,CAACpC,SAAS,GAAG,QAAQ,IAAI,CAAChH,IAAI,CAACnQ,CAAC,IAAI,IAAI,CAACmQ,IAAI,CAAClQ,CAAC,IAAI,IAAI,CAACkQ,IAAI,CAACjQ,CAAC,IAAI,IAAI,CAACiQ,IAAI,CAAChQ,CAAC,GAAG;QACxF;MACJ;MACA,IAAI,CAAC/C,MAAM,GAAG,IAAI2R,cAAc,CAAC,CAAC,IAAI,CAACyH,OAAO,IAAI,IAAI,CAACjH,IAAI,CAACjO,CAAC,IAAI,IAAI,CAACqS,YAAY,CAACrS,CAAC,GAAG,CAAC,EAAE,IAAI,CAACiO,IAAI,CAACjS,CAAC,GAAG,IAAI,CAACqW,YAAY,CAACrW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAACiS,IAAI,CAAClS,CAAC,IAAI,IAAI,CAACsW,YAAY,CAACtW,CAAC,GAAG,CAAC,EAAE,IAAI,CAACkS,IAAI,CAACzQ,CAAC,GAAG,IAAI,CAAC6U,YAAY,CAAC7U,CAAC,GAAG,CAAC,CAAC;MACpN,IAAIoM,IAAI,IAAImO,UAAU,KAAK,IAAI,CAAC1C,WAAW,EAAE;QACzC,IAAI,IAAI,CAAC9C,aAAa,EAAE;UACpB,IAAI,CAACkC,iBAAiB,CAACmB,WAAW,CAAC,IAAI,CAACC,SAAS,CAAC;QACtD;QACA,IAAI,CAACpB,iBAAiB,CAACqB,YAAY,CAAC,IAAI,CAACT,WAAW,CAAC;MACzD;IACJ;EACJ;EACA;EACAF,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACrH,eAAe,KAAK,QAAQ,EAAE;MACnC,IAAI,CAAC7B,QAAQ,GAAG,UAAU;IAC9B,CAAC,MACI;MACD,IAAIA,QAAQ,GAAG,QAAQ;QAAEiM,SAAS,GAAG,EAAE;QAAEC,KAAK;MAC9C,IAAIC,UAAU,GAAG,IAAI;QAAEC,aAAa,GAAG,IAAI;MAC3C,IAAIC,IAAI,GAAG,IAAI,CAACnF,mBAAmB,CAACjH,aAAa,CAACkM,UAAU;MAC5D,MAAMG,YAAY,GAAG,IAAI,CAACC,aAAa,CAACtM,aAAa,CAACS,YAAY;MAClE,OAAO2L,IAAI,KAAK,IAAI,IAAIA,IAAI,CAACG,OAAO,KAAK,MAAM,EAAE;QAC7CN,KAAK,GAAGtQ,MAAM,CAAC6Q,gBAAgB,CAACJ,IAAI,CAAC;QACrCrM,QAAQ,GAAGkM,KAAK,CAACQ,gBAAgB,CAAC,UAAU,CAAC;QAC7CT,SAAS,GAAGC,KAAK,CAACQ,gBAAgB,CAAC,WAAW,CAAC;QAC/C,IAAI1M,QAAQ,KAAK,QAAQ,IAAImM,UAAU,KAAK,IAAI,EAAE;UAC9CA,UAAU,GAAGE,IAAI;QACrB;QACA,IAAIJ,SAAS,IAAIA,SAAS,KAAK,MAAM,IAAIG,aAAa,KAAK,IAAI,EAAE;UAC7DA,aAAa,GAAGC,IAAI;QACxB;QACA,IAAIrM,QAAQ,KAAK,OAAO,EAAE;UACtBmM,UAAU,GAAGC,aAAa;UAC1B;QACJ;QACAC,IAAI,GAAGA,IAAI,CAACF,UAAU;MAC1B;MACA,MAAMQ,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC1F,mBAAmB,CAACjH,aAAa,EAAGD,QAAQ,KAAK,OAAQ,CAAC;MACzG,IAAI,IAAI,CAACyI,oBAAoB,IAAKzI,QAAQ,KAAK,OAAO,KACjD,CAACmM,UAAU,IAAIA,UAAU,YAAYU,kBAAkB,CAAE,EAAE;QAC5D,IAAI,CAACvR,GAAG,GAAGqR,YAAY,CAACrR,GAAG;QAC3B,IAAI,CAACC,IAAI,GAAGoR,YAAY,CAACpR,IAAI;MACjC,CAAC,MACI;QACD,IAAI4Q,UAAU,KAAK,IAAI,EAAE;UACrBA,UAAU,GAAGE,IAAI;QACrB;QACA,MAAMS,SAAS,GAAG,IAAI,CAACF,eAAe,CAACT,UAAU,EAAGnM,QAAQ,KAAK,OAAQ,CAAC;QAC1E,IAAI,CAAC1E,GAAG,GAAGqR,YAAY,CAACrR,GAAG,GAAGwR,SAAS,CAACxR,GAAG;QAC3C,IAAI,CAACC,IAAI,GAAGoR,YAAY,CAACpR,IAAI,GAAGuR,SAAS,CAACvR,IAAI;MAClD;MACA,IAAIyE,QAAQ,KAAK,OAAO,EAAE;QACtB,IAAI,CAACA,QAAQ,GAAG,OAAO;MAC3B;MACA,IAAI+M,WAAW,GAAG,IAAI,CAAC9E,UAAU;MACjC,MAAM+E,YAAY,GAAG,IAAI,CAACT,aAAa,CAACtM,aAAa,CAACC,qBAAqB,CAAC,CAAC;MAC7E,IAAI,IAAI,CAAC+H,UAAU,KAAK,MAAM,EAAE;QAC5B,MAAMgF,aAAa,GAAG,IAAI,CAAC5E,gBAAgB,CAACpI,aAAa,CAACC,qBAAqB,CAAC,CAAC;QACjF6M,WAAW,GAAGhS,wBAAwB,CAACiS,YAAY,EAAEC,aAAa,CAAC;MACvE;MACA,IAAI,CAACxe,QAAQ,GAAGse,WAAW,KAAK,KAAK,GAC/BT,YAAY,GAAG,CAAC,GAChB7O,SAAS;MACf,IAAI,CAACjP,eAAe,GAAGiP,SAAS;MAChC,QAAQsP,WAAW;QACf,KAAK,KAAK;UACN,IAAI,CAACzR,GAAG,IAAIgR,YAAY,GAAG,IAAI,CAACzD,eAAe;UAC/C,IAAI,CAACtN,IAAI,IAAI,IAAI,CAAC2M,gBAAgB,GAAG,GAAG,GAAGyE,YAAY,CAACtR,KAAK,GAAG,IAAI,CAACuN,iBAAiB;UACtF;QACJ,KAAK,QAAQ;UACT,IAAI,CAACtN,GAAG,IAAIqR,YAAY,CAACvR,MAAM,GAAG,IAAI,CAACyN,eAAe;UACtD,IAAI,CAACtN,IAAI,IAAI,IAAI,CAAC2M,gBAAgB,GAAG,GAAG,GAAGyE,YAAY,CAACtR,KAAK,GAAG,IAAI,CAACuN,iBAAiB;UACtF;QACJ,KAAK,UAAU;QACf,KAAK,UAAU;UACX,IAAI,CAACtN,GAAG,IAAIgR,YAAY,GAAGK,YAAY,CAACvR,MAAM,GAAGuR,YAAY,CAACvR,MAAM,GAAG,IAAI,CAAC8M,gBAAgB,GAAG,GAAG;UAClG,IAAI,CAAC3M,IAAI,IAAI,IAAI,CAAC8K,OAAO,GAAG,IAAI,CAACwC,eAAe,GAAG,CAAC,GAAG,IAAI,CAACD,iBAAiB;UAC7E;QACJ,KAAK,WAAW;QAChB,KAAK,WAAW;UACZ,IAAI,CAACtN,GAAG,IAAIgR,YAAY,GAAGK,YAAY,CAACvR,MAAM,GAAGuR,YAAY,CAACvR,MAAM,GAAG,IAAI,CAAC8M,gBAAgB,GAAG,GAAG;UAClG,IAAI,CAAC3M,IAAI,IAAIoR,YAAY,CAACtR,KAAK,GAAG,IAAI,CAACwN,eAAe,GAAG,CAAC,GAAG,IAAI,CAACD,iBAAiB;UACnF;QACJ,KAAK,MAAM;QACX,KAAK,aAAa;QAClB,KAAK,aAAa;UACd,IAAI,CAACtN,GAAG,IAAIqR,YAAY,CAACvR,MAAM,GAAG,IAAI,CAAC8M,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAACU,iBAAiB;UACtF,IAAI,CAACrN,IAAI,IAAI,IAAI,CAAC8K,OAAO,GAAG,IAAI,CAACwC,eAAe,GAAG,CAAC;UACpD;QACJ,KAAK,OAAO;QACZ,KAAK,cAAc;QACnB,KAAK,cAAc;QACnB;UACI,IAAI,CAACvN,GAAG,IAAIqR,YAAY,CAACvR,MAAM,GAAG,IAAI,CAAC8M,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAACU,iBAAiB;UACtF,IAAI,CAACrN,IAAI,IAAIoR,YAAY,CAACtR,KAAK,GAAG,IAAI,CAACwN,eAAe,GAAG,CAAC;UAC1D;MACR;MACA,MAAMqE,iBAAiB,GAAGtR,MAAM,CAACC,WAAW;MAC5C,MAAMsR,gBAAgB,GAAGvR,MAAM,CAACO,UAAU;MAC1C,MAAMiR,eAAe,GAAG,IAAI,CAACjO,KAAK,CAACc,aAAa,CAACC,qBAAqB,CAAC,CAAC;MACxE,MAAM1E,MAAM,GAAG,IAAI,CAACF,GAAG,GAAG0R,YAAY,CAAC5R,MAAM;MAC7C,IAAII,MAAM,GAAG0R,iBAAiB,EAAE;QAC5B,IAAI,CAAC5R,GAAG,GAAG4R,iBAAiB,GAAGF,YAAY,CAAC5R,MAAM;QAClD,IAAI,CAAC5M,eAAe,GAAG4e,eAAe,CAACzM,CAAC,GAAG,CAAC,GAAG,EAAE;MACrD;MACA,MAAMlF,KAAK,GAAG,IAAI,CAACF,IAAI,GAAGyR,YAAY,CAAC3R,KAAK;MAC5C,IAAII,KAAK,GAAG0R,gBAAgB,EAAE;QAC1B,IAAI,CAAC5R,IAAI,GAAG4R,gBAAgB,GAAGH,YAAY,CAAC3R,KAAK;QACjD,IAAI,CAAC7M,eAAe,GAAG4e,eAAe,CAACzM,CAAC,GAAG,CAAC,GAAG,EAAE;MACrD;MACA,IAAI,CAACrS,aAAa,GAAGye,WAAW;IACpC;EACJ;EACA;EACAzD,YAAYA,CAAC+D,MAAM,EAAEC,KAAK,EAAE;IACxB,IAAIjB,IAAI,GAAGiB,KAAK,CAACnB,UAAU;IAC3B,OAAOE,IAAI,KAAK,IAAI,EAAE;MAClB,IAAIA,IAAI,KAAKgB,MAAM,EAAE;QACjB,OAAO,IAAI;MACf;MACAhB,IAAI,GAAGA,IAAI,CAACF,UAAU;IAC1B;IACA,OAAO,KAAK;EAChB;EACAS,eAAeA,CAACW,OAAO,EAAEC,MAAM,EAAE;IAC7B,MAAM;MAAElS,GAAG;MAAEC;IAAK,CAAC,GAAGgS,OAAO,CAACrN,qBAAqB,CAAC,CAAC;IACrD,OAAO;MACH5E,GAAG,EAAEA,GAAG,IAAIkS,MAAM,GAAG5R,MAAM,CAAC4E,WAAW,GAAG,CAAC,CAAC;MAC5CjF,IAAI,EAAEA,IAAI,IAAIiS,MAAM,GAAG5R,MAAM,CAACyE,WAAW,GAAG,CAAC,CAAC;MAC9ChF,KAAK,EAAEkS,OAAO,CAAC9M,WAAW;MAC1BrF,MAAM,EAAEmS,OAAO,CAAC7M;IACpB,CAAC;EACL;AAGJ;AAAC+M,qBAAA,GA7zBKxI,oBAAoB;AAAAtK,eAAA,CAApBsK,oBAAoB,wBAAAyI,8BAAA1P,iBAAA;EAAA,YAAAA,iBAAA,IA2zB6EiH,qBAAoB,EA9pC1CjX,EAAE,CAAAmT,iBAAA,CA8pC0DnT,EAAE,CAAC2f,MAAM,GA9pCrE3f,EAAE,CAAAmT,iBAAA,CA8pCgFnT,EAAE,CAACoT,UAAU,GA9pC/FpT,EAAE,CAAAmT,iBAAA,CA8pC0GnT,EAAE,CAAC4f,iBAAiB,GA9pChI5f,EAAE,CAAAmT,iBAAA,CA8pC2I7G,QAAQ,GA9pCrJtM,EAAE,CAAAmT,iBAAA,CA8pCgKtH,WAAW,GA9pC7K7L,EAAE,CAAAmT,iBAAA,CA8pCwLO,kBAAkB;AAAA;AAAA/G,eAAA,CA3zBvRsK,oBAAoB,8BAnWuDjX,EAAE,CAAA6f,iBAAA;EAAA3P,IAAA,EA+pCQ+G,qBAAoB;EAAA9G,SAAA;EAAA2P,SAAA,WAAAC,4BAAA7f,EAAA,EAAAS,GAAA;IAAA,IAAAT,EAAA;MA/pC9BF,EAAE,CAAAggB,WAAA,CAAAC,GAAA;MAAFjgB,EAAE,CAAAggB,WAAA,CAAAE,GAAA;MAAFlgB,EAAE,CAAAggB,WAAA,CAAAG,GAAA;IAAA;IAAA,IAAAjgB,EAAA;MAAA,IAAAkgB,EAAA;MAAFpgB,EAAE,CAAAqgB,cAAA,CAAAD,EAAA,GAAFpgB,EAAE,CAAAsgB,WAAA,QAAA3f,GAAA,CAAA4d,aAAA,GAAA6B,EAAA,CAAAG,KAAA;MAAFvgB,EAAE,CAAAqgB,cAAA,CAAAD,EAAA,GAAFpgB,EAAE,CAAAsgB,WAAA,QAAA3f,GAAA,CAAAsX,SAAA,GAAAmI,EAAA,CAAAG,KAAA;MAAFvgB,EAAE,CAAAqgB,cAAA,CAAAD,EAAA,GAAFpgB,EAAE,CAAAsgB,WAAA,QAAA3f,GAAA,CAAAwX,WAAA,GAAAiI,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAnQ,YAAA,WAAAoQ,mCAAAtgB,EAAA,EAAAS,GAAA;IAAA,IAAAT,EAAA;MAAFF,EAAE,CAAAe,UAAA,uBAAA0f,mDAAAxf,MAAA;QAAA,OA+pCQN,GAAA,CAAAuW,SAAA,CAAAjW,MAAgB,CAAC;MAAA,UA/pC3BjB,EAAE,CAAA0gB,iBA+pC2B,CAAC,yBAAAC,qDAAA1f,MAAA;QAAA,OAApBN,GAAA,CAAAyW,WAAA,CAAAnW,MAAkB,CAAC;MAAA,UA/pC7BjB,EAAE,CAAA0gB,iBA+pC2B,CAAC;IAAA;EAAA;EAAAhQ,UAAA;EAAAkQ,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,+BAAA9gB,EAAA,EAAAS,GAAA;IAAA,IAAAT,EAAA;MAAA,MAAA+gB,GAAA,GA/pC9BjhB,EAAE,CAAAa,gBAAA;MAAFb,EAAE,CAAAc,cAAA,eA+pCi2B,CAAC;MA/pCp2Bd,EAAE,CAAAe,UAAA,mBAAAmgB,oDAAAjgB,MAAA;QAAFjB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CA+pCu0BF,MAAA,CAAAyQ,eAAA,CAAuB,CAAC;MAAA,CAAC,CAAC;MA/pCn2B1R,EAAE,CAAAuE,UAAA,IAAA4c,oCAAA,gBA+pCq/B,CAAC,IAAAzgB,oCAAA,gBAA6S,CAAC;MA/pCtyCV,EAAE,CAAAc,cAAA,YA+pC66C,CAAC,YAAyB,CAAC;MA/pC18Cd,EAAE,CAAAC,SAAA,YA+pC8/C,CAAC;MA/pCjgDD,EAAE,CAAAc,cAAA,aA+pC6tD,CAAC;MA/pChuDd,EAAE,CAAAe,UAAA,mBAAAqgB,oDAAA;QAAFphB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CAAAR,GAAA,CAAAkX,mBAAA,IAAAlX,GAAA,CAAAyZ,YAAA,IA+pC6sDzZ,GAAA,CAAAob,YAAA,CAAa,CAAC;MAAA,CAAC,CAAC;MA/pC/tD/b,EAAE,CAAAuE,UAAA,IAAAvC,yCAAA,iBA+pC+5D,CAAC;MA/pCl6DhC,EAAE,CAAAyB,YAAA,CA+pCswE,CAAC;MA/pCzwEzB,EAAE,CAAAuE,UAAA,IAAArC,uCAAA,oBA+pC6+E,CAAC;MA/pCh/ElC,EAAE,CAAAyB,YAAA,CA+pC4iF,CAAC;MA/pC/iFzB,EAAE,CAAAc,cAAA,cA+pCykF,CAAC;MA/pC5kFd,EAAE,CAAAuE,UAAA,KAAAxB,qCAAA,iBA+pCopF,CAAC;MA/pCvpF/C,EAAE,CAAAc,cAAA,iBA+pCi4F,CAAC;MA/pCp4Fd,EAAE,CAAAe,UAAA,sBAAAsgB,wDAAApgB,MAAA;QAAFjB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CA+pC0yFR,GAAA,CAAAgc,WAAA,CAAA1b,MAAkB,CAAC;MAAA,CAAC,CAAC,uBAAAqgB,yDAAA;QA/pCj0FthB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CA+pC80FR,GAAA,CAAAW,WAAA,CAAY,KAAK,CAAC;MAAA,CAAC,CAAC,qBAAAigB,uDAAA;QA/pCp2FvhB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CA+pC+2FR,GAAA,CAAAa,SAAA,CAAU,KAAK,CAAC;MAAA,CAAC,CAAC;MA/pCn4FxB,EAAE,CAAAC,SAAA,cA+pCq8F,CAAC;MA/pCx8FD,EAAE,CAAAyB,YAAA,CA+pCm9F,CAAC;MA/pCt9FzB,EAAE,CAAAc,cAAA,iBA+pCmsG,CAAC;MA/pCtsGd,EAAE,CAAAe,UAAA,sBAAAygB,wDAAAvgB,MAAA;QAAFjB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CA+pCsmGR,GAAA,CAAAic,aAAA,CAAA3b,MAAoB,CAAC;MAAA,CAAC,CAAC,uBAAAwgB,yDAAA;QA/pC/nGzhB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CA+pC4oGR,GAAA,CAAAW,WAAA,CAAY,OAAO,CAAC;MAAA,CAAC,CAAC,qBAAAogB,uDAAA;QA/pCpqG1hB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CA+pC+qGR,GAAA,CAAAa,SAAA,CAAU,OAAO,CAAC;MAAA,CAAC,CAAC;MA/pCrsGxB,EAAE,CAAAC,SAAA,cA+pCwwG,CAAC;MA/pC3wGD,EAAE,CAAAyB,YAAA,CA+pCsxG,CAAC;MA/pCzxGzB,EAAE,CAAAc,cAAA,iBA+pCqjH,CAAC;MA/pCxjHd,EAAE,CAAAe,UAAA,sBAAA4gB,wDAAA1gB,MAAA;QAAFjB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CA+pCw9GR,GAAA,CAAAkc,aAAA,CAAA5b,MAAoB,CAAC;MAAA,CAAC,CAAC,uBAAA2gB,yDAAA;QA/pCj/G5hB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CA+pC8/GR,GAAA,CAAAW,WAAA,CAAY,OAAO,CAAC;MAAA,CAAC,CAAC,qBAAAugB,uDAAA;QA/pCthH7hB,EAAE,CAAAkB,aAAA,CAAA+f,GAAA;QAAA,OAAFjhB,EAAE,CAAAmB,WAAA,CA+pCiiHR,GAAA,CAAAa,SAAA,CAAU,OAAO,CAAC;MAAA,CAAC,CAAC;MA/pCvjHxB,EAAE,CAAAC,SAAA,cA+pCynH,CAAC;MA/pC5nHD,EAAE,CAAAyB,YAAA,CA+pCuoH,CAAC,CAAW,CAAC,CAAS,CAAC;MA/pChqHzB,EAAE,CAAAuE,UAAA,KAAAd,qCAAA,mBA+pC2yH,CAAC,KAAA2B,qCAAA,mBAA81C,CAAC,KAAAmB,qCAAA,mBAAmpC,CAAC,KAAAoB,qCAAA,iBAA6rC,CAAC,KAAAY,qCAAA,iBAA6oB,CAAC,KAAAK,qCAAA,iBAAwpB,CAAC,KAAAuB,qCAAA,iBAA0P,CAAC,KAAAa,qCAAA,iBAAoqB,CAAC,KAAAK,qCAAA,iBAAiX,CAAC;MA/pCxhUrL,EAAE,CAAAyB,YAAA,CA+pCgnU,CAAC;IAAA;IAAA,IAAAvB,EAAA;MA/pCnnUF,EAAE,CAAAO,WAAA,aAAAI,GAAA,CAAAwW,IAAA,mBA+pC4nB,CAAC,eAAAxW,GAAA,CAAA6c,MAAA,uBAAoD,CAAC,QAAA7c,GAAA,CAAA2M,GAAA,MAAsB,CAAC,SAAA3M,GAAA,CAAA4M,IAAA,MAAwB,CAAC,aAAA5M,GAAA,CAAAqR,QAA6B,CAAC,WAAArR,GAAA,CAAA8Y,QAAA,MAA8B,CAAC,UAAA9Y,GAAA,CAAA0X,OAAA,MAA4B,CAAC;MA/pC9zBrY,EAAE,CAAAiI,WAAA,SAAAtH,GAAA,CAAAwW,IA+pC+kB,CAAC;MA/pCllBnX,EAAE,CAAA4B,SAAA,EA+pC64B,CAAC;MA/pCh5B5B,EAAE,CAAA2B,UAAA,SAAAhB,GAAA,CAAAkT,eAAA,YA+pC64B,CAAC;MA/pCh5B7T,EAAE,CAAA4B,SAAA,CA+pCsiC,CAAC;MA/pCziC5B,EAAE,CAAA2B,UAAA,UAAAhB,GAAA,CAAAgZ,WAAA,YA+pCsiC,CAAC;MA/pCziC3Z,EAAE,CAAA4B,SAAA,EA+pCglD,CAAC;MA/pCnlD5B,EAAE,CAAAO,WAAA,qBAAAI,GAAA,CAAA2B,aA+pCglD,CAAC,WAAA3B,GAAA,CAAAkX,mBAAA,IAAAlX,GAAA,CAAAyZ,YAAA,mBAAyE,CAAC;MA/pC7pDpa,EAAE,CAAA4B,SAAA,CA+pCuxD,CAAC;MA/pC1xD5B,EAAE,CAAA2B,UAAA,SAAAhB,GAAA,CAAAkX,mBAAA,IAAAlX,GAAA,CAAAyZ,YA+pCuxD,CAAC;MA/pC1xDpa,EAAE,CAAA4B,SAAA,CA+pCgzE,CAAC;MA/pCnzE5B,EAAE,CAAA2B,UAAA,SAAAhB,GAAA,CAAAiJ,gBA+pCgzE,CAAC;MA/pCnzE5J,EAAE,CAAA4B,SAAA,EA+pCynF,CAAC;MA/pC5nF5B,EAAE,CAAA2B,UAAA,SAAAhB,GAAA,CAAAkE,cAAA,eA+pCynF,CAAC;MA/pC5nF7E,EAAE,CAAA4B,SAAA,CA+pC2xF,CAAC;MA/pC9xF5B,EAAE,CAAAO,WAAA,aAAAI,GAAA,CAAAgZ,WAAA,+BA+pC2xF,CAAC;MA/pC9xF3Z,EAAE,CAAA2B,UAAA,SA+pCstF,CAAC;MA/pCztF3B,EAAE,CAAA4B,SAAA,EA+pC87F,CAAC;MA/pCj8F5B,EAAE,CAAAO,WAAA,SAAAI,GAAA,CAAAkB,MAAA,kBAAAlB,GAAA,CAAAkB,MAAA,CAAAkE,CAAA,MA+pC87F,CAAC;MA/pCj8F/F,EAAE,CAAA4B,SAAA,CA+pCulG,CAAC;MA/pC1lG5B,EAAE,CAAAO,WAAA,aAAAI,GAAA,CAAAgZ,WAAA,+BA+pCulG,CAAC;MA/pC1lG3Z,EAAE,CAAA2B,UAAA,SA+pCmhG,CAAC;MA/pCthG3B,EAAE,CAAA4B,SAAA,EA+pCiwG,CAAC;MA/pCpwG5B,EAAE,CAAAO,WAAA,UAAAI,GAAA,CAAAkB,MAAA,kBAAAlB,GAAA,CAAAkB,MAAA,CAAAC,CAAA,MA+pCiwG,CAAC;MA/pCpwG9B,EAAE,CAAA4B,SAAA,CA+pC25G,CAAC;MA/pC95G5B,EAAE,CAAAO,WAAA,YAAAI,GAAA,CAAAkE,cAAA,kCA+pC25G,CAAC,qBAAAlE,GAAA,CAAAod,gBAA6C,CAAC;MA/pC58G/d,EAAE,CAAA2B,UAAA,SA+pCs1G,CAAC;MA/pCz1G3B,EAAE,CAAA4B,SAAA,EA+pCknH,CAAC;MA/pCrnH5B,EAAE,CAAAO,WAAA,SAAAI,GAAA,CAAAkB,MAAA,kBAAAlB,GAAA,CAAAkB,MAAA,CAAA0B,CAAA,MA+pCknH,CAAC;MA/pCrnHvD,EAAE,CAAA4B,SAAA,CA+pC2tH,CAAC;MA/pC9tH5B,EAAE,CAAA2B,UAAA,UAAAhB,GAAA,CAAAiZ,cAAA,KAAAjZ,GAAA,CAAAgZ,WAAA,YA+pC2tH,CAAC;MA/pC9tH3Z,EAAE,CAAA4B,SAAA,CA+pCyjK,CAAC;MA/pC5jK5B,EAAE,CAAA2B,UAAA,UAAAhB,GAAA,CAAAiZ,cAAA,KAAAjZ,GAAA,CAAAgZ,WAAA,YA+pCyjK,CAAC;MA/pC5jK3Z,EAAE,CAAA4B,SAAA,CA+pC6sM,CAAC;MA/pChtM5B,EAAE,CAAA2B,UAAA,UAAAhB,GAAA,CAAAiZ,cAAA,KAAAjZ,GAAA,CAAAgZ,WAAA,YA+pC6sM,CAAC;MA/pChtM3Z,EAAE,CAAA4B,SAAA,CA+pCw1O,CAAC;MA/pC31O5B,EAAE,CAAA2B,UAAA,UAAAhB,GAAA,CAAAiZ,cAAA,KAAAjZ,GAAA,CAAAgZ,WAAA,YA+pCw1O,CAAC;MA/pC31O3Z,EAAE,CAAA4B,SAAA,CA+pC6kQ,CAAC;MA/pChlQ5B,EAAE,CAAA2B,UAAA,UAAAhB,GAAA,CAAAiZ,cAAA,KAAAjZ,GAAA,CAAAgZ,WAAA,YA+pC6kQ,CAAC;MA/pChlQ3Z,EAAE,CAAA4B,SAAA,CA+pCquR,CAAC;MA/pCxuR5B,EAAE,CAAA2B,UAAA,UAAAhB,GAAA,CAAAiZ,cAAA,KAAAjZ,GAAA,CAAAgZ,WAAA,YA+pCquR,CAAC;MA/pCxuR3Z,EAAE,CAAA4B,SAAA,CA+pCq+R,CAAC;MA/pCx+R5B,EAAE,CAAA2B,UAAA,UAAAhB,GAAA,CAAA+B,cAAA,kBAAA/B,GAAA,CAAA+B,cAAA,CAAAC,MAAA,KAAAhC,GAAA,CAAAiJ,gBA+pCq+R,CAAC;MA/pCx+R5J,EAAE,CAAA4B,SAAA,CA+pC0oT,CAAC;MA/pC7oT5B,EAAE,CAAA2B,UAAA,SAAAhB,GAAA,CAAAuK,UAAA,IAAAvK,GAAA,CAAAsK,cA+pC0oT,CAAC;MA/pC7oTjL,EAAE,CAAA4B,SAAA,CA+pCkhU,CAAC;MA/pCrhU5B,EAAE,CAAA2B,UAAA,SAAAhB,GAAA,CAAA2K,eA+pCkhU,CAAC;IAAA;EAAA;EAAAwW,YAAA,GAAmnyB1V,EAAE,CAAC2V,OAAO,EAAmH3V,EAAE,CAAC4V,IAAI,EAA6F5V,EAAE,CAAC6V,gBAAgB,EAAoJ9S,aAAa,EAAkG4B,eAAe;EAAAmR,MAAA;EAAAC,aAAA;AAAA;AAE/tnC;EAAA,QAAAxR,SAAA,oBAAAA,SAAA,KAjqCiF3Q,EAAE,CAAA4Q,iBAAA,CAiqCQqG,oBAAoB,EAAc,CAAC;IAClH/G,IAAI,EAAEpE,SAAS;IACf+E,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEqR,aAAa,EAAEpW,iBAAiB,CAACqW,IAAI;MAAErB,QAAQ,EAAE,mmTAAmmT;MAAEmB,MAAM,EAAE,CAAC,09xBAA09xB;IAAE,CAAC;EACnqlC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhS,IAAI,EAAElQ,EAAE,CAAC2f;EAAO,CAAC,EAAE;IAAEzP,IAAI,EAAElQ,EAAE,CAACoT;EAAW,CAAC,EAAE;IAAElD,IAAI,EAAElQ,EAAE,CAAC4f;EAAkB,CAAC,EAAE;IAAE1P,IAAI,EAAEmS,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC5HpS,IAAI,EAAElE,MAAM;MACZ6E,IAAI,EAAE,CAACvE,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE4D,IAAI,EAAET,SAAS;IAAE6S,UAAU,EAAE,CAAC;MAClCpS,IAAI,EAAElE,MAAM;MACZ6E,IAAI,EAAE,CAAChF,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEqE,IAAI,EAAEwD;EAAmB,CAAC,CAAC,EAAkB;IAAE6K,aAAa,EAAE,CAAC;MACvErO,IAAI,EAAEjE,SAAS;MACf4E,IAAI,EAAE,CAAC,aAAa,EAAE;QAAE0R,MAAM,EAAE;MAAK,CAAC;IAC1C,CAAC,CAAC;IAAEtK,SAAS,EAAE,CAAC;MACZ/H,IAAI,EAAEjE,SAAS;MACf4E,IAAI,EAAE,CAAC,WAAW,EAAE;QAAE0R,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEpK,WAAW,EAAE,CAAC;MACdjI,IAAI,EAAEjE,SAAS;MACf4E,IAAI,EAAE,CAAC,aAAa,EAAE;QAAE0R,MAAM,EAAE;MAAK,CAAC;IAC1C,CAAC,CAAC;IAAErL,SAAS,EAAE,CAAC;MACZhH,IAAI,EAAEvE,YAAY;MAClBkF,IAAI,EAAE,CAAC,oBAAoB,EAAE,CAAC,QAAQ,CAAC;IAC3C,CAAC,CAAC;IAAEuG,WAAW,EAAE,CAAC;MACdlH,IAAI,EAAEvE,YAAY;MAClBkF,IAAI,EAAE,CAAC,sBAAsB,EAAE,CAAC,QAAQ,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAM2R,WAAW,GAAG,OAAO7R,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;AACnE,MAAM8R,oBAAoB,CAAC;EA8DvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,UAAU,CAAC,CAAC;EACrB;EACAE,WAAWA,CAACxT,KAAK,EAAE;IACf,IAAI,CAACD,WAAW,CAACC,KAAK,CAAC;EAC3B;EACA3C,WAAWA,CAACoW,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAE9R,KAAK,EAAE+R,QAAQ,EAAE;IAAAvW,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,wBA/D3C,KAAK;IAAAA,eAAA,wBACL,KAAK;IAAAA,eAAA;IAAAA,eAAA,+BAEE,KAAK;IAAAA,eAAA;IAAAA,eAAA,kBAElB,OAAO;IAAAA,eAAA,mBACN,MAAM;IAAAA,eAAA,mBACN,KAAK;IAAAA,eAAA,qBACH,KAAK;IAAAA,eAAA,4BACE,EAAE;IAAAA,eAAA,0BACJ,EAAE;IAAAA,eAAA,sBACN,OAAO;IAAAA,eAAA,wBACL,KAAK;IAAAA,eAAA,yBACJ,MAAM;IAAAA,eAAA,yBACN,SAAS;IAAAA,eAAA,yBACT,KAAK;IAAAA,eAAA,0BACJ,OAAO;IAAAA,eAAA,6BACJ,IAAI;IAAAA,eAAA,8BACH,IAAI;IAAAA,eAAA,iCACD,KAAK;IAAAA,eAAA,qBACjB,MAAM;IAAAA,eAAA,2BACA,IAAI;IAAAA,eAAA,oCACK,KAAK;IAAAA,eAAA,qBACpB,KAAK;IAAAA,eAAA,yBACD,IAAI;IAAAA,eAAA,0BACH,oBAAoB;IAAAA,eAAA,yBACrB,KAAK;IAAAA,eAAA,6BACD,QAAQ;IAAAA,eAAA,8BACP,wBAAwB;IAAAA,eAAA,uBAC/B,KAAK;IAAAA,eAAA,wBACJ,eAAe;IAAAA,eAAA;IAAAA,eAAA,8BAET,wBAAwB;IAAAA,eAAA,kCACpB,CAAC;IAAAA,eAAA,+BACJ,iBAAiB;IAAAA,eAAA,oCACZ,sBAAsB;IAAAA,eAAA,2BAC/B,KAAK;IAAAA,eAAA,+BACD,WAAW;IAAAA,eAAA,gCACV,2BAA2B;IAAAA,eAAA,mCACxB,8BAA8B;IAAAA,eAAA,0BACvC,CAAC;IAAAA,eAAA;IAAAA,eAAA,wBAEH,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAAAoB,eAAA,yBACrB,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAAAoB,eAAA,yBACtB,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAAAoB,eAAA,0BACrB,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAAAoB,eAAA,4BACpB,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAAAoB,eAAA,0BACxB,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAAAoB,eAAA,2BACrB,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAAAoB,eAAA,4BACrB,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAAAoB,eAAA,4BACtB,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAAAoB,eAAA,4BACtB,IAAIpB,YAAY,CAAC,KAAK,CAAC;IAAAoB,eAAA,4BACvB,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAAAoB,eAAA,+BACnB,IAAIpB,YAAY,CAAC,IAAI,CAAC;IAWzC,IAAI,CAACuX,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC9R,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC+R,QAAQ,GAAGA,QAAQ;EAC5B;EACApK,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACqK,MAAM,IAAI,IAAI,EAAE;MACrB,IAAI,IAAI,CAACC,oBAAoB,EAAE;QAC3B,IAAI,CAACJ,MAAM,CAACK,UAAU,CAAC,IAAI,CAACF,MAAM,CAACG,QAAQ,CAAC;MAChD;MACA,IAAI,CAACH,MAAM,CAACI,OAAO,CAAC,CAAC;MACrB,IAAI,CAACJ,MAAM,GAAG,IAAI;MAClB,IAAI,CAACK,MAAM,GAAG,IAAI;IACtB;EACJ;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACtC,IAAIF,OAAO,CAACC,QAAQ,CAACE,YAAY,EAAE;QAC/B,IAAI,CAACjL,UAAU,CAAC,CAAC;MACrB,CAAC,MACI,IAAI,CAAC8K,OAAO,CAACC,QAAQ,CAACE,YAAY,EAAE;QACrC,IAAI,CAAC/P,WAAW,CAAC,CAAC;MACtB;IACJ;IACA,IAAI4P,OAAO,CAACI,WAAW,EAAE;MACrB,IAAI,IAAI,CAACN,MAAM,IAAI,CAAC,IAAI,CAACO,aAAa,EAAE;QACpC,IAAI,IAAI,CAAClQ,eAAe,KAAK,QAAQ,EAAE;UACnC,IAAI,CAAC2P,MAAM,CAACrK,eAAe,CAACuK,OAAO,CAACI,WAAW,CAACD,YAAY,CAAC;QACjE;QACA,IAAI,CAACL,MAAM,CAAC7Z,kBAAkB,CAAC+Z,OAAO,CAACI,WAAW,CAACD,YAAY,EAAE,KAAK,CAAC;QACvE,IAAI,IAAI,CAAC7J,sBAAsB,IAAI,IAAI,CAACnG,eAAe,KAAK,QAAQ,EAAE;UAClE,IAAI,CAACsP,MAAM,CAACa,iBAAiB,CAAC/K,aAAa,CAAC,CAAC;QACjD;MACJ;MACA,IAAI,CAAC8K,aAAa,GAAG,KAAK;IAC9B;IACA,IAAIL,OAAO,CAACtZ,aAAa,IAAIsZ,OAAO,CAAChhB,cAAc,EAAE;MACjD,IAAI,IAAI,CAAC8gB,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAAC7I,eAAe,CAAC,IAAI,CAACvQ,aAAa,EAAE,IAAI,CAAC1H,cAAc,CAAC;MACxE;IACJ;EACJ;EACAkW,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACqL,aAAa,EAAE;MACrB,IAAIhB,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI,CAACgB,aAAa,GAAG,IAAI;MACzB,IAAI,CAACb,oBAAoB,GAAG,KAAK;MACjC,IAAI,IAAI,CAACpJ,sBAAsB,IAAI,IAAI,CAACnG,eAAe,KAAK,QAAQ,EAAE;QAClE,MAAMqQ,oBAAoB,GAAG,IAAI,CAAClB,MAAM,CAACmB,cAAc,CAAC,CAAC,CAAC;QAC1D,MAAMC,WAAW,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAACH,oBAAoB,EAAEhY,QAAQ,CAACoY,IAAI,CAAC;QAC1E,IAAIF,WAAW,KAAKlY,QAAQ,CAACoY,IAAI,EAAE;UAC/BrB,KAAK,GAAGmB,WAAW,CAACnB,KAAK,IAAImB,WAAW,CAACG,gBAAgB,IAAI,IAAI,CAACtB,KAAK;UACvE,IAAIT,WAAW,IAAIS,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;YACrCuB,OAAO,CAACC,IAAI,CAAC,wCAAwC,GACjD,0DAA0D,GAC1D,mFAAmF,CAAC;UAC5F;QACJ,CAAC,MACI;UACD,IAAI,CAACrB,oBAAoB,GAAG,IAAI;QACpC;MACJ;MACA,MAAMsB,WAAW,GAAG,IAAI,CAAC3B,GAAG,CAAC4B,uBAAuB,CAAC1N,oBAAoB,CAAC;MAC1E,IAAI,IAAI,CAACmM,oBAAoB,EAAE;QAC3B,IAAI,CAACD,MAAM,GAAGuB,WAAW,CAACE,MAAM,CAAC,IAAI,CAAC9B,QAAQ,CAAC;QAC/C,IAAI,CAACE,MAAM,CAAC6B,UAAU,CAAC,IAAI,CAAC1B,MAAM,CAACG,QAAQ,CAAC;QAC5CxV,QAAQ,CAACgX,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC5B,MAAM,CAACG,QAAQ,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC,MACI;QACD,MAAMlC,QAAQ,GAAG5W,QAAQ,CAAC0Y,MAAM,CAAC;UAC7BK,SAAS,EAAE,EAAE;UACb;UACA;UACA5F,MAAM,EAAE4D,KAAK,CAACH;QAClB,CAAC,CAAC;QACF,IAAI,CAACK,MAAM,GAAGF,KAAK,CAACiC,eAAe,CAACR,WAAW,EAAE,CAAC,EAAE5B,QAAQ,EAAE,EAAE,CAAC;MACrE;MACA,IAAI,CAACK,MAAM,CAAC5J,QAAQ,CAACD,WAAW,CAAC,IAAI,EAAE,IAAI,CAACnI,KAAK,EAAE,IAAI,CAAC2S,WAAW,EAAE,IAAI,CAACzL,OAAO,EAAE,IAAI,CAACoB,QAAQ,EAAE,IAAI,CAAC5F,eAAe,EAAE,IAAI,CAAC6F,eAAe,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACrB,aAAa,EAAE,IAAI,CAACzT,cAAc,EAAE,IAAI,CAAC0T,cAAc,EAAE,IAAI,CAACqB,cAAc,EAAE,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAACC,mBAAmB,EAAE,IAAI,CAACC,sBAAsB,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,yBAAyB,EAAE,IAAI,CAAC/P,aAAa,EAAE,IAAI,CAAC1H,cAAc,EAAE,IAAI,CAACoH,mBAAmB,EAAE,IAAI,CAAClH,uBAAuB,EAAE,IAAI,CAACsH,oBAAoB,EAAE,IAAI,CAACF,yBAAyB,EAAE,IAAI,CAACkB,UAAU,EAAE,IAAI,CAACJ,eAAe,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,CAACE,cAAc,EAAE,IAAI,CAACR,mBAAmB,EAAE,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAACd,gBAAgB,EAAE,IAAI,CAACnH,qBAAqB,EAAE,IAAI,CAACK,oBAAoB,EAAE,IAAI,CAACyG,wBAAwB,EAAE,IAAI,CAAC6Q,YAAY,EAAE,IAAI,CAACjJ,KAAK,EAAE,IAAI,CAAC7F,eAAe,CAAC;MACp2B,IAAI,CAACkY,MAAM,GAAG,IAAI,CAACL,MAAM,CAAC5J,QAAQ;MAClC,IAAI,IAAI,CAAC0J,KAAK,KAAKA,KAAK,EAAE;QACtB,IAAI,CAACE,MAAM,CAACa,iBAAiB,CAAC/K,aAAa,CAAC,CAAC;MACjD;IACJ,CAAC,MACI,IAAI,IAAI,CAACuK,MAAM,EAAE;MAClB,IAAI,CAACA,MAAM,CAAC5K,UAAU,CAAC,IAAI,CAACkL,WAAW,CAAC;IAC5C;EACJ;EACAhQ,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC0P,MAAM,IAAI,IAAI,CAAC3P,eAAe,KAAK,OAAO,EAAE;MACjD,IAAI,CAAC2P,MAAM,CAAC1P,WAAW,CAAC,CAAC;IAC7B;EACJ;EACA6H,WAAWA,CAACrM,KAAK,EAAE;IACf,IAAI,CAAC6V,iBAAiB,CAACxV,IAAI,CAACL,KAAK,CAAC;EACtC;EACAoO,YAAYA,CAAC0H,KAAK,EAAE;IAChB,IAAI,CAACC,cAAc,CAAC1V,IAAI,CAACyV,KAAK,CAAC;IAC/B,IAAIA,KAAK,EAAE;MACP,IAAI,CAACE,eAAe,CAAC3V,IAAI,CAAC,IAAI,CAACmU,WAAW,CAAC;IAC/C,CAAC,MACI;MACD,IAAI,CAACyB,gBAAgB,CAAC5V,IAAI,CAAC,IAAI,CAACmU,WAAW,CAAC;IAChD;EACJ;EACAjI,YAAYA,CAACvM,KAAK,EAAEkW,MAAM,GAAG,IAAI,EAAE;IAC/B,IAAI,CAACzB,aAAa,GAAGyB,MAAM;IAC3B,IAAI,CAACC,iBAAiB,CAAC9V,IAAI,CAACL,KAAK,CAAC;EACtC;EACAoM,aAAaA,CAACpM,KAAK,EAAE;IACjB,IAAI,CAACoW,iBAAiB,CAAC/V,IAAI,CAACL,KAAK,CAAC;EACtC;EACAwM,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC6J,iBAAiB,CAAChW,IAAI,CAAC,CAAC;EACjC;EACAgT,UAAUA,CAAA,EAAG;IACT,MAAMpD,OAAO,GAAG,IAAI,CAACpO,KAAK,CAACc,aAAa;IACxC,MAAM2T,OAAO,GAAG,IAAI,CAAC/L,iBAAiB,CAAC0B,MAAM,CAAEC,IAAI,IAAKA,IAAI,KAAK+D,OAAO,CAAC;IACzE,IAAI,CAAC,IAAI,CAACqE,UAAU,IAAI,CAACgC,OAAO,CAACjjB,MAAM,EAAE;MACrC,IAAI,OAAOmL,QAAQ,KAAK,WAAW,IAAIyR,OAAO,KAAKzR,QAAQ,CAAC+X,aAAa,EAAE;QACvE,IAAI,CAACjN,UAAU,CAAC,CAAC;MACrB,CAAC,MACI,IAAI,CAAC,IAAI,CAAC4K,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAACrM,IAAI,EAAE;QACxC,IAAI,CAACyB,UAAU,CAAC,CAAC;MACrB,CAAC,MACI;QACD,IAAI,CAAC9E,WAAW,CAAC,CAAC;MACtB;IACJ;EACJ;EACA1E,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,IAAI,CAACmU,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAAC7Z,kBAAkB,CAAC0F,KAAK,CAACE,MAAM,CAACD,KAAK,EAAE,IAAI,CAAC;IAC5D,CAAC,MACI;MACD,IAAI,CAACwU,WAAW,GAAGzU,KAAK,CAACE,MAAM,CAACD,KAAK;MACrC,IAAI,CAACmW,iBAAiB,CAAC9V,IAAI,CAAC,IAAI,CAACmU,WAAW,CAAC;IACjD;EACJ;EACA1G,YAAYA,CAAC/N,KAAK,EAAE;IAChB,IAAI,CAACyW,aAAa,CAACnW,IAAI,CAACN,KAAK,CAAC;EAClC;EACAqN,aAAaA,CAACrN,KAAK,EAAE;IACjB,IAAI,CAAC0W,cAAc,CAACpW,IAAI,CAACN,KAAK,CAAC;EACnC;EACA8L,aAAaA,CAAC9L,KAAK,EAAE;IACjB,IAAI,CAAC2W,eAAe,CAACrW,IAAI,CAACN,KAAK,CAAC;EACpC;EACAgM,eAAeA,CAAChM,KAAK,EAAE;IACnB,IAAI,CAAC4W,iBAAiB,CAACtW,IAAI,CAACN,KAAK,CAAC;EACtC;EACAkO,mBAAmBA,CAACjO,KAAK,EAAE;IACvB,IAAI,CAAC4W,oBAAoB,CAACvW,IAAI,CAACL,KAAK,CAAC;EACzC;AAGJ;AAAC6W,qBAAA,GArOK1D,oBAAoB;AAAA9V,eAAA,CAApB8V,oBAAoB,wBAAA2D,8BAAApW,iBAAA;EAAA,YAAAA,iBAAA,IAmO6EyS,qBAAoB,EAp6C1CziB,EAAE,CAAAmT,iBAAA,CAo6C0DnT,EAAE,CAACkM,QAAQ,GAp6CvElM,EAAE,CAAAmT,iBAAA,CAo6CkFnT,EAAE,CAACqmB,wBAAwB,GAp6C/GrmB,EAAE,CAAAmT,iBAAA,CAo6C0HnT,EAAE,CAACsmB,cAAc,GAp6C7ItmB,EAAE,CAAAmT,iBAAA,CAo6CwJnT,EAAE,CAACumB,gBAAgB,GAp6C7KvmB,EAAE,CAAAmT,iBAAA,CAo6CwLnT,EAAE,CAACoT,UAAU,GAp6CvMpT,EAAE,CAAAmT,iBAAA,CAo6CkNO,kBAAkB;AAAA;AAAA/G,eAAA,CAnOjT8V,oBAAoB,8BAjsCuDziB,EAAE,CAAAiQ,iBAAA;EAAAC,IAAA,EAq6CQuS,qBAAoB;EAAAtS,SAAA;EAAAC,YAAA,WAAAoW,mCAAAtmB,EAAA,EAAAS,GAAA;IAAA,IAAAT,EAAA;MAr6C9BF,EAAE,CAAAe,UAAA,mBAAA0lB,+CAAA;QAAA,OAq6CQ9lB,GAAA,CAAA+hB,WAAA,CAAY,CAAC;MAAA,CAAM,CAAC,mBAAAgE,+CAAA;QAAA,OAApB/lB,GAAA,CAAAiiB,WAAA,CAAY,CAAC;MAAA,CAAM,CAAC,mBAAA+D,+CAAA1lB,MAAA;QAAA,OAApBN,GAAA,CAAAkiB,WAAA,CAAA5hB,MAAkB,CAAC;MAAA,EAAC;IAAA;EAAA;EAAAsP,MAAA;IAAAuT,WAAA;IAAAzL,OAAA;IAAAoB,QAAA;IAAAkK,QAAA;IAAAC,UAAA;IAAA/J,iBAAA;IAAAH,eAAA;IAAAC,WAAA;IAAArB,aAAA;IAAAC,cAAA;IAAA1T,cAAA;IAAA+U,cAAA;IAAA/F,eAAA;IAAAiG,kBAAA;IAAAC,mBAAA;IAAAC,sBAAA;IAAAC,UAAA;IAAAC,gBAAA;IAAAC,yBAAA;IAAAjP,UAAA;IAAAH,cAAA;IAAAD,eAAA;IAAAG,cAAA;IAAAP,kBAAA;IAAAD,mBAAA;IAAA2P,YAAA;IAAAhQ,aAAA;IAAA1H,cAAA;IAAAoH,mBAAA;IAAAlH,uBAAA;IAAAsH,oBAAA;IAAAF,yBAAA;IAAAJ,gBAAA;IAAA9G,oBAAA;IAAAL,qBAAA;IAAA8G,wBAAA;IAAA/I,eAAA;IAAA8K,eAAA;EAAA;EAAAmF,OAAA;IAAAqV,aAAA;IAAAT,cAAA;IAAAU,cAAA;IAAAC,eAAA;IAAAC,iBAAA;IAAAX,eAAA;IAAAC,gBAAA;IAAAI,iBAAA;IAAAD,iBAAA;IAAAD,iBAAA;IAAAN,iBAAA;IAAAe,oBAAA;EAAA;EAAAU,QAAA;EAAAlW,UAAA;EAAAmW,QAAA,GAr6C9B7mB,EAAE,CAAA8mB,oBAAA;AAAA;AAu6CnF;EAAA,QAAAnW,SAAA,oBAAAA,SAAA,KAv6CiF3Q,EAAE,CAAA4Q,iBAAA,CAu6CQ6R,oBAAoB,EAAc,CAAC;IAClHvS,IAAI,EAAE1E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzB8V,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1W,IAAI,EAAElQ,EAAE,CAACkM;EAAS,CAAC,EAAE;IAAEgE,IAAI,EAAElQ,EAAE,CAACqmB;EAAyB,CAAC,EAAE;IAAEnW,IAAI,EAAElQ,EAAE,CAACsmB;EAAe,CAAC,EAAE;IAAEpW,IAAI,EAAElQ,EAAE,CAACumB;EAAiB,CAAC,EAAE;IAAErW,IAAI,EAAElQ,EAAE,CAACoT;EAAW,CAAC,EAAE;IAAElD,IAAI,EAAEwD;EAAmB,CAAC,CAAC,EAAkB;IAAEoQ,WAAW,EAAE,CAAC;MACrO5T,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE4M,OAAO,EAAE,CAAC;MACVnI,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEgO,QAAQ,EAAE,CAAC;MACXvJ,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEkY,QAAQ,EAAE,CAAC;MACXzT,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEmY,UAAU,EAAE,CAAC;MACb1T,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEoO,iBAAiB,EAAE,CAAC;MACpB3J,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEiO,eAAe,EAAE,CAAC;MAClBxJ,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEkO,WAAW,EAAE,CAAC;MACdzJ,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE6M,aAAa,EAAE,CAAC;MAChBpI,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE8M,cAAc,EAAE,CAAC;MACjBrI,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE5G,cAAc,EAAE,CAAC;MACjBqL,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEmO,cAAc,EAAE,CAAC;MACjB1J,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEoI,eAAe,EAAE,CAAC;MAClB3D,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEqO,kBAAkB,EAAE,CAAC;MACrB5J,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEsO,mBAAmB,EAAE,CAAC;MACtB7J,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEuO,sBAAsB,EAAE,CAAC;MACzB9J,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEwO,UAAU,EAAE,CAAC;MACb/J,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEyO,gBAAgB,EAAE,CAAC;MACnBhK,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE0O,yBAAyB,EAAE,CAAC;MAC5BjK,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEP,UAAU,EAAE,CAAC;MACbgF,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEV,cAAc,EAAE,CAAC;MACjBmF,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEX,eAAe,EAAE,CAAC;MAClBoF,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAER,cAAc,EAAE,CAAC;MACjBiF,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEf,kBAAkB,EAAE,CAAC;MACrBwF,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEhB,mBAAmB,EAAE,CAAC;MACtByF,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE2O,YAAY,EAAE,CAAC;MACflK,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAErB,aAAa,EAAE,CAAC;MAChB8F,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE/I,cAAc,EAAE,CAAC;MACjBwN,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE3B,mBAAmB,EAAE,CAAC;MACtBoG,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE7I,uBAAuB,EAAE,CAAC;MAC1BsN,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEvB,oBAAoB,EAAE,CAAC;MACvBgG,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEzB,yBAAyB,EAAE,CAAC;MAC5BkG,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE7B,gBAAgB,EAAE,CAAC;MACnBsG,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE3I,oBAAoB,EAAE,CAAC;MACvBoN,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEhJ,qBAAqB,EAAE,CAAC;MACxByN,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAElC,wBAAwB,EAAE,CAAC;MAC3B2G,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEjL,eAAe,EAAE,CAAC;MAClB0P,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEH,eAAe,EAAE,CAAC;MAClB4E,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEqa,aAAa,EAAE,CAAC;MAChB5V,IAAI,EAAExE;IACV,CAAC,CAAC;IAAE2Z,cAAc,EAAE,CAAC;MACjBnV,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEqa,cAAc,EAAE,CAAC;MACjB7V,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEsa,eAAe,EAAE,CAAC;MAClB9V,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEua,iBAAiB,EAAE,CAAC;MACpB/V,IAAI,EAAExE;IACV,CAAC,CAAC;IAAE4Z,eAAe,EAAE,CAAC;MAClBpV,IAAI,EAAExE;IACV,CAAC,CAAC;IAAE6Z,gBAAgB,EAAE,CAAC;MACnBrV,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEia,iBAAiB,EAAE,CAAC;MACpBzV,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEga,iBAAiB,EAAE,CAAC;MACpBxV,IAAI,EAAExE;IACV,CAAC,CAAC;IAAE+Z,iBAAiB,EAAE,CAAC;MACpBvV,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEyZ,iBAAiB,EAAE,CAAC;MACpBjV,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEwa,oBAAoB,EAAE,CAAC;MACvBhW,IAAI,EAAExE;IACV,CAAC,CAAC;IAAEgX,WAAW,EAAE,CAAC;MACdxS,IAAI,EAAEvE,YAAY;MAClBkF,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE+R,WAAW,EAAE,CAAC;MACd1S,IAAI,EAAEvE,YAAY;MAClBkF,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEgS,WAAW,EAAE,CAAC;MACd3S,IAAI,EAAEvE,YAAY;MAClBkF,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkW,iBAAiB,CAAC;AAIvBC,kBAAA,GAJKD,iBAAiB;AAAApa,eAAA,CAAjBoa,iBAAiB,wBAAAE,2BAAAjX,iBAAA;EAAA,YAAAA,iBAAA,IACgF+W,kBAAiB;AAAA;AAAApa,eAAA,CADlHoa,iBAAiB,8BA5hD0D/mB,EAAE,CAAAknB,gBAAA;EAAAhX,IAAA,EA8hDqB6W,kBAAiB;EAAAI,YAAA,GAAiBlQ,oBAAoB,EAAEwL,oBAAoB,EAAEtT,aAAa,EAAE4B,eAAe;EAAAqW,OAAA,GAAa7a,YAAY;EAAA8a,OAAA,GAAa5E,oBAAoB;AAAA;AAAA9V,eAAA,CAFxQoa,iBAAiB,8BA5hD0D/mB,EAAE,CAAAsnB,gBAAA;EAAArC,SAAA,EA+hDmD,CAACvR,kBAAkB,CAAC;EAAA0T,OAAA,GAAY7a,YAAY;AAAA;AAElL;EAAA,QAAAoE,SAAA,oBAAAA,SAAA,KAjiDiF3Q,EAAE,CAAA4Q,iBAAA,CAiiDQmW,iBAAiB,EAAc,CAAC;IAC/G7W,IAAI,EAAE/D,QAAQ;IACd0E,IAAI,EAAE,CAAC;MACCuW,OAAO,EAAE,CAAC7a,YAAY,CAAC;MACvB8a,OAAO,EAAE,CAAC5E,oBAAoB,CAAC;MAC/BwC,SAAS,EAAE,CAACvR,kBAAkB,CAAC;MAC/ByT,YAAY,EAAE,CAAClQ,oBAAoB,EAAEwL,oBAAoB,EAAEtT,aAAa,EAAE4B,eAAe;IAC7F,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASjE,IAAI,EAAEmK,oBAAoB,EAAEwL,oBAAoB,EAAEsE,iBAAiB,EAAErT,kBAAkB,EAAE7G,IAAI,EAAED,IAAI,EAAEH,IAAI,EAAEsE,eAAe,EAAE5B,aAAa;AAClJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}