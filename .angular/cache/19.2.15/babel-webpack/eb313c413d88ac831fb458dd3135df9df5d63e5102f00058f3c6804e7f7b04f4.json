{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuComponent } from './swui-menu.component';\nimport { MAT_MODULES } from './swui-menu.module';\ndescribe('MenuComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [...MAT_MODULES, TranslateModule.forRoot(), RouterModule.forRoot([])],\n      declarations: [SwuiMenuComponent],\n      schemas: [NO_ERRORS_SCHEMA]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiMenuComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "NO_ERRORS_SCHEMA", "RouterModule", "TranslateModule", "SwuiMenuComponent", "MAT_MODULES", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "forRoot", "declarations", "schemas", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-menu/swui-menu.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\n\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { SwuiMenuComponent } from './swui-menu.component';\nimport { MAT_MODULES } from './swui-menu.module';\n\n\ndescribe('MenuComponent', () => {\n  let component: SwuiMenuComponent;\n  let fixture: ComponentFixture<SwuiMenuComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [\n        ...MAT_MODULES,\n        TranslateModule.forRoot(),\n        RouterModule.forRoot([])\n      ],\n      declarations: [SwuiMenuComponent],\n      schemas: [NO_ERRORS_SCHEMA],\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiMenuComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,WAAW,QAAQ,oBAAoB;AAGhDC,QAAQ,CAAC,eAAe,EAAE,MAAK;EAC7B,IAAIC,SAA4B;EAChC,IAAIC,OAA4C;EAEhDC,UAAU,CAACT,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACP,GAAGN,WAAW,EACdF,eAAe,CAACS,OAAO,EAAE,EACzBV,YAAY,CAACU,OAAO,CAAC,EAAE,CAAC,CACzB;MACDC,YAAY,EAAE,CAACT,iBAAiB,CAAC;MACjCU,OAAO,EAAE,CAACb,gBAAgB;KAC3B,CAAC,CACCc,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHN,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGT,OAAO,CAACiB,eAAe,CAACZ,iBAAiB,CAAC;IACpDG,SAAS,GAAGC,OAAO,CAACS,iBAAiB;IACrCT,OAAO,CAACU,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACb,SAAS,CAAC,CAACc,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}