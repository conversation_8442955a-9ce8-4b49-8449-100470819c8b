{"ast": null, "code": "export const gameSelectItemTypes = {\n  GAME: 'game',\n  PROVIDER: 'provider',\n  LABEL: 'label',\n  INTERSECTION: 'intersection',\n  CORRUPTION: 'corruption'\n};", "map": {"version": 3, "names": ["gameSelectItemTypes", "GAME", "PROVIDER", "LABEL", "INTERSECTION", "CORRUPTION"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-item/game-select-item-types.ts"], "sourcesContent": ["export const gameSelectItemTypes = {\n    GAME: 'game',\n    PROVIDER: 'provider',\n    LABEL: 'label',\n    INTERSECTION: 'intersection',\n    CORRUPTION: 'corruption'\n};\n"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,GAAG;EAC/BC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,YAAY,EAAE,cAAc;EAC5BC,UAAU,EAAE;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}