{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { SwuiFooterTotalWidgetComponent } from './total/total.widget';\nimport { CommonModule } from '@angular/common';\nimport { SwuiFooterStringWidgetComponent } from './string/string.widget';\nimport { GridPipesModule } from '../pipes/grid-pipes.module';\nlet SwuiFooterWidgetsModule = class SwuiFooterWidgetsModule {};\nSwuiFooterWidgetsModule = __decorate([NgModule({\n  imports: [CommonModule, GridPipesModule],\n  exports: [],\n  declarations: [SwuiFooterStringWidgetComponent, SwuiFooterTotalWidgetComponent],\n  providers: []\n})], SwuiFooterWidgetsModule);\nexport { SwuiFooterWidgetsModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "SwuiFooterTotalWidgetComponent", "CommonModule", "SwuiFooterStringWidgetComponent", "GridPipesModule", "SwuiFooterWidgetsModule", "imports", "exports", "declarations", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/footer-widget/footer-widgets.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { SwuiFooterTotalWidgetComponent } from './total/total.widget';\nimport { CommonModule } from '@angular/common';\nimport { SwuiFooterStringWidgetComponent } from './string/string.widget';\nimport { GridPipesModule } from '../pipes/grid-pipes.module';\nlet SwuiFooterWidgetsModule = class SwuiFooterWidgetsModule {\n};\nSwuiFooterWidgetsModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            GridPipesModule,\n        ],\n        exports: [],\n        declarations: [\n            SwuiFooterStringWidgetComponent,\n            SwuiFooterTotalWidgetComponent,\n        ],\n        providers: [],\n    })\n], SwuiFooterWidgetsModule);\nexport { SwuiFooterWidgetsModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,8BAA8B,QAAQ,sBAAsB;AACrE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,+BAA+B,QAAQ,wBAAwB;AACxE,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,IAAIC,uBAAuB,GAAG,MAAMA,uBAAuB,CAAC,EAC3D;AACDA,uBAAuB,GAAGN,UAAU,CAAC,CACjCC,QAAQ,CAAC;EACLM,OAAO,EAAE,CACLJ,YAAY,EACZE,eAAe,CAClB;EACDG,OAAO,EAAE,EAAE;EACXC,YAAY,EAAE,CACVL,+BAA+B,EAC/BF,8BAA8B,CACjC;EACDQ,SAAS,EAAE;AACf,CAAC,CAAC,CACL,EAAEJ,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}