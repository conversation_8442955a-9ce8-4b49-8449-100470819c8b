{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { isEqual, cloneDeep } from 'lodash';\nimport { Observable, ReplaySubject, Subject } from 'rxjs';\nlet SwuiTopFilterDataService = class SwuiTopFilterDataService {\n  constructor() {\n    this.onReset = new Subject();\n    this._displayedFilterState = new Subject();\n    this._appliedFilterState = new ReplaySubject(1);\n    this._filterFormState = new ReplaySubject(1);\n    this.appliedFilterValue = {};\n    this.patched = false;\n  }\n  get displayedFilter() {\n    return this._displayedFilterState.asObservable();\n  }\n  get appliedFilter() {\n    return this._appliedFilterState.asObservable();\n  }\n  get filterFormState() {\n    return this._filterFormState.asObservable();\n  }\n  setFormState(value) {\n    this._filterFormState.next(value);\n  }\n  setDisplayFilter(value) {\n    this._displayedFilterState.next(value);\n  }\n  patchFilter(value) {\n    const needUpdate = Object.keys(value).every(key => this.appliedFilterValue.hasOwnProperty(key));\n    if (!needUpdate) {\n      return;\n    }\n    const newFilter = _objectSpread(_objectSpread({}, this.appliedFilterValue), value);\n    if (!isEqual(newFilter, this.appliedFilterValue)) {\n      this.submitFilter(newFilter);\n      this.patched = true;\n    }\n  }\n  /**\n   * Updates appliedFilter to remove values that don't exist in the provided schema\n   */\n  updateFilter(schema) {\n    const updatedFilter = cloneDeep(this.appliedFilterValue);\n    for (const [field, value] of Object.entries(updatedFilter)) {\n      const fieldSchema = schema.find(item => item.field === field);\n      let data;\n      if ((fieldSchema === null || fieldSchema === void 0 ? void 0 : fieldSchema.type) === 'select-table') {\n        data = fieldSchema.data;\n      } else if ((fieldSchema === null || fieldSchema === void 0 ? void 0 : fieldSchema.type) === 'multiselect') {\n        data = fieldSchema.data;\n      } else if ((fieldSchema === null || fieldSchema === void 0 ? void 0 : fieldSchema.type) === 'select') {\n        data = fieldSchema.data;\n      }\n      if (!data) {\n        continue;\n      }\n      if (data instanceof Observable) {\n        updatedFilter[field] = [];\n      } else {\n        const options = data;\n        if (Array.isArray(value)) {\n          // \"multiselect\"\n          updatedFilter[field] = value.filter(item => options.some(option => item.id === option.id));\n        } else if (typeof value === 'object' && value !== null) {\n          // \"select-table\"\n          if (!options.some(option => value.id === option.id)) {\n            delete updatedFilter[field];\n          }\n        } else {\n          // \"select\"\n          if (!options.some(option => value === option.id)) {\n            delete updatedFilter[field];\n          }\n        }\n      }\n    }\n    if (!isEqual(updatedFilter, this.appliedFilterValue)) {\n      this.submitFilter(updatedFilter);\n    }\n  }\n  submitFilter(value, checkPatched) {\n    if (!this.patched || !checkPatched) {\n      this.appliedFilterValue = value;\n      this.setDisplayFilter(value);\n      this._appliedFilterState.next(value);\n    }\n    this.patched = false;\n  }\n  resetFilter() {\n    this.onReset.next(undefined);\n    this.submitFilter({});\n  }\n};\nSwuiTopFilterDataService = __decorate([Injectable()], SwuiTopFilterDataService);\nexport { SwuiTopFilterDataService };", "map": {"version": 3, "names": ["Injectable", "isEqual", "cloneDeep", "Observable", "ReplaySubject", "Subject", "SwuiTopFilterDataService", "constructor", "onReset", "_displayedFilterState", "_appliedFilterState", "_filterFormState", "appliedFilterValue", "patched", "displayedFilter", "asObservable", "appliedFilter", "filterFormState", "setFormState", "value", "next", "setDisplayFilter", "patchFilter", "needUpdate", "Object", "keys", "every", "key", "hasOwnProperty", "newFilter", "_objectSpread", "submitFilter", "updateFilter", "schema", "updatedFilter", "field", "entries", "fieldSchema", "find", "item", "data", "type", "options", "Array", "isArray", "filter", "some", "option", "id", "checkPatched", "resetFilter", "undefined", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-top-filter/top-filter-data.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { isEqual, cloneDeep } from 'lodash';\nimport { Observable, ReplaySubject, Subject } from 'rxjs';\nimport { SchemaTopFilterField } from './swui-schema-top-filter.model';\nimport { MultiselectInputOptionData, SelectInputOptionData, SelectOptionItem, SelectTableInputOptionData } from '../swui-dynamic-form/dynamic-form.model';\nimport { SwuiSelectTableOption } from '../swui-select-table/swui-select-table.interface';\n\n@Injectable()\nexport class SwuiTopFilterDataService {\n  public onReset = new Subject<void>();\n  private _displayedFilterState = new Subject<any>();\n  private _appliedFilterState = new ReplaySubject<any>(1);\n  private _filterFormState = new ReplaySubject(1);\n  private appliedFilterValue: Object = {};\n  private patched = false;\n\n  get displayedFilter(): Observable<any> {\n    return this._displayedFilterState.asObservable();\n  }\n\n  get appliedFilter(): Observable<any> {\n    return this._appliedFilterState.asObservable();\n  }\n\n  get filterFormState(): Observable<any> {\n    return this._filterFormState.asObservable();\n  }\n\n  setFormState( value: any ) {\n    this._filterFormState.next(value);\n  }\n\n  setDisplayFilter( value: any ): void {\n    this._displayedFilterState.next(value);\n  }\n\n  patchFilter(value: Object) {\n    const needUpdate = Object.keys(value).every(key => this.appliedFilterValue.hasOwnProperty(key));\n\n    if (!needUpdate) {\n      return;\n    }\n\n    const newFilter = { ...this.appliedFilterValue, ...value };\n\n    if (!isEqual(newFilter, this.appliedFilterValue)) {\n      this.submitFilter(newFilter);\n      this.patched = true;\n    }\n  }\n\n  /**\n   * Updates appliedFilter to remove values that don't exist in the provided schema\n   */\n  updateFilter(schema: SchemaTopFilterField[]): void {\n    const updatedFilter: Record<string, any> = cloneDeep(this.appliedFilterValue);\n    for (const [field, value] of Object.entries(updatedFilter)) {\n      const fieldSchema = schema.find(item => item.field === field);\n      let data: SelectTableInputOptionData['data'] | SelectInputOptionData['data'] | MultiselectInputOptionData['data'];\n      if (fieldSchema?.type === 'select-table') {\n        data = (fieldSchema as SelectTableInputOptionData).data;\n      } else if (fieldSchema?.type === 'multiselect') {\n        data = (fieldSchema as MultiselectInputOptionData).data;\n      } else if (fieldSchema?.type === 'select') {\n        data = (fieldSchema as SelectInputOptionData).data;\n      }\n      if (!data) {\n        continue;\n      }\n      if (data instanceof Observable) {\n        updatedFilter[field] = [];\n      } else {\n        const options = data as (SelectOptionItem[] | SwuiSelectTableOption[]);\n        if (Array.isArray(value)) {\n          // \"multiselect\"\n          updatedFilter[field] = value.filter(item => options.some(option => item.id === option.id));\n        } else if (typeof value === 'object' && value !== null) {\n          // \"select-table\"\n          if (!options.some(option => value.id === option.id)) {\n            delete updatedFilter[field];\n          }\n        } else {\n          // \"select\"\n          if (!options.some(option => value === option.id)) {\n            delete updatedFilter[field];\n          }\n        }\n      }\n    }\n    if (!isEqual(updatedFilter, this.appliedFilterValue)) {\n      this.submitFilter(updatedFilter);\n    }\n  }\n\n  submitFilter(value: any, checkPatched?: boolean): void {\n    if (!this.patched || !checkPatched) {\n      this.appliedFilterValue = value;\n      this.setDisplayFilter(value);\n      this._appliedFilterState.next(value);\n    }\n\n    this.patched = false;\n  }\n\n  resetFilter(): void {\n    this.onReset.next(undefined);\n    this.submitFilter({});\n  }\n}\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,OAAO,EAAEC,SAAS,QAAQ,QAAQ;AAC3C,SAASC,UAAU,EAAEC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAMlD,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAA9BC,YAAA;IACE,KAAAC,OAAO,GAAG,IAAIH,OAAO,EAAQ;IAC5B,KAAAI,qBAAqB,GAAG,IAAIJ,OAAO,EAAO;IAC1C,KAAAK,mBAAmB,GAAG,IAAIN,aAAa,CAAM,CAAC,CAAC;IAC/C,KAAAO,gBAAgB,GAAG,IAAIP,aAAa,CAAC,CAAC,CAAC;IACvC,KAAAQ,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,OAAO,GAAG,KAAK;EA8FzB;EA5FE,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACL,qBAAqB,CAACM,YAAY,EAAE;EAClD;EAEA,IAAIC,aAAaA,CAAA;IACf,OAAO,IAAI,CAACN,mBAAmB,CAACK,YAAY,EAAE;EAChD;EAEA,IAAIE,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACN,gBAAgB,CAACI,YAAY,EAAE;EAC7C;EAEAG,YAAYA,CAAEC,KAAU;IACtB,IAAI,CAACR,gBAAgB,CAACS,IAAI,CAACD,KAAK,CAAC;EACnC;EAEAE,gBAAgBA,CAAEF,KAAU;IAC1B,IAAI,CAACV,qBAAqB,CAACW,IAAI,CAACD,KAAK,CAAC;EACxC;EAEAG,WAAWA,CAACH,KAAa;IACvB,MAAMI,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACN,KAAK,CAAC,CAACO,KAAK,CAACC,GAAG,IAAI,IAAI,CAACf,kBAAkB,CAACgB,cAAc,CAACD,GAAG,CAAC,CAAC;IAE/F,IAAI,CAACJ,UAAU,EAAE;MACf;IACF;IAEA,MAAMM,SAAS,GAAAC,aAAA,CAAAA,aAAA,KAAQ,IAAI,CAAClB,kBAAkB,GAAKO,KAAK,CAAE;IAE1D,IAAI,CAAClB,OAAO,CAAC4B,SAAS,EAAE,IAAI,CAACjB,kBAAkB,CAAC,EAAE;MAChD,IAAI,CAACmB,YAAY,CAACF,SAAS,CAAC;MAC5B,IAAI,CAAChB,OAAO,GAAG,IAAI;IACrB;EACF;EAEA;;;EAGAmB,YAAYA,CAACC,MAA8B;IACzC,MAAMC,aAAa,GAAwBhC,SAAS,CAAC,IAAI,CAACU,kBAAkB,CAAC;IAC7E,KAAK,MAAM,CAACuB,KAAK,EAAEhB,KAAK,CAAC,IAAIK,MAAM,CAACY,OAAO,CAACF,aAAa,CAAC,EAAE;MAC1D,MAAMG,WAAW,GAAGJ,MAAM,CAACK,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACJ,KAAK,KAAKA,KAAK,CAAC;MAC7D,IAAIK,IAA6G;MACjH,IAAI,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,MAAK,cAAc,EAAE;QACxCD,IAAI,GAAIH,WAA0C,CAACG,IAAI;MACzD,CAAC,MAAM,IAAI,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,MAAK,aAAa,EAAE;QAC9CD,IAAI,GAAIH,WAA0C,CAACG,IAAI;MACzD,CAAC,MAAM,IAAI,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,MAAK,QAAQ,EAAE;QACzCD,IAAI,GAAIH,WAAqC,CAACG,IAAI;MACpD;MACA,IAAI,CAACA,IAAI,EAAE;QACT;MACF;MACA,IAAIA,IAAI,YAAYrC,UAAU,EAAE;QAC9B+B,aAAa,CAACC,KAAK,CAAC,GAAG,EAAE;MAC3B,CAAC,MAAM;QACL,MAAMO,OAAO,GAAGF,IAAsD;QACtE,IAAIG,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,EAAE;UACxB;UACAe,aAAa,CAACC,KAAK,CAAC,GAAGhB,KAAK,CAAC0B,MAAM,CAACN,IAAI,IAAIG,OAAO,CAACI,IAAI,CAACC,MAAM,IAAIR,IAAI,CAACS,EAAE,KAAKD,MAAM,CAACC,EAAE,CAAC,CAAC;QAC5F,CAAC,MAAM,IAAI,OAAO7B,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;UACtD;UACA,IAAI,CAACuB,OAAO,CAACI,IAAI,CAACC,MAAM,IAAI5B,KAAK,CAAC6B,EAAE,KAAKD,MAAM,CAACC,EAAE,CAAC,EAAE;YACnD,OAAOd,aAAa,CAACC,KAAK,CAAC;UAC7B;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAACO,OAAO,CAACI,IAAI,CAACC,MAAM,IAAI5B,KAAK,KAAK4B,MAAM,CAACC,EAAE,CAAC,EAAE;YAChD,OAAOd,aAAa,CAACC,KAAK,CAAC;UAC7B;QACF;MACF;IACF;IACA,IAAI,CAAClC,OAAO,CAACiC,aAAa,EAAE,IAAI,CAACtB,kBAAkB,CAAC,EAAE;MACpD,IAAI,CAACmB,YAAY,CAACG,aAAa,CAAC;IAClC;EACF;EAEAH,YAAYA,CAACZ,KAAU,EAAE8B,YAAsB;IAC7C,IAAI,CAAC,IAAI,CAACpC,OAAO,IAAI,CAACoC,YAAY,EAAE;MAClC,IAAI,CAACrC,kBAAkB,GAAGO,KAAK;MAC/B,IAAI,CAACE,gBAAgB,CAACF,KAAK,CAAC;MAC5B,IAAI,CAACT,mBAAmB,CAACU,IAAI,CAACD,KAAK,CAAC;IACtC;IAEA,IAAI,CAACN,OAAO,GAAG,KAAK;EACtB;EAEAqC,WAAWA,CAAA;IACT,IAAI,CAAC1C,OAAO,CAACY,IAAI,CAAC+B,SAAS,CAAC;IAC5B,IAAI,CAACpB,YAAY,CAAC,EAAE,CAAC;EACvB;CACD;AApGYzB,wBAAwB,GAAA8C,UAAA,EADpCpD,UAAU,EAAE,C,EACAM,wBAAwB,CAoGpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}