{"ast": null, "code": "var _SwuiGridUrlHandlerService;\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nlet SwuiGridUrlHandlerService = (_SwuiGridUrlHandlerService = class SwuiGridUrlHandlerService {\n  constructor(router, activatedRoute) {\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.allowQueryParamsUpdate = true;\n    this.pageSortQueryParams = {\n      limit: 'limit',\n      offset: 'offset',\n      sortBy: 'sortBy',\n      sortOrder: 'sortOrder'\n    };\n  }\n  setAllowQueryParamsUpdate(allow) {\n    this.allowQueryParamsUpdate = allow;\n  }\n  setParams(queryParams, queryParamsHandling = '') {\n    if (this.allowQueryParamsUpdate) {\n      this.router.navigate([], {\n        relativeTo: this.activatedRoute,\n        queryParams,\n        queryParamsHandling,\n        preserveFragment: true\n      });\n    }\n  }\n  getParams() {\n    return this.activatedRoute.snapshot.queryParams;\n  }\n  fetchPageSize(initial = 0) {\n    const params = this.getParams();\n    let fetched = initial;\n    if (params && 'limit' in params) {\n      fetched = parseInt(params['limit'], 10);\n    }\n    return fetched;\n  }\n  setParamsToPaginator(paginator) {\n    const params = this.getParams();\n    if (Object.keys(params).length) {\n      if ('offset' in params) {\n        paginator.pageIndex = parseInt(params[this.pageSortQueryParams.offset], 10) / paginator.pageSize;\n      }\n    }\n  }\n  setParamsToSort(sort) {\n    const params = this.getParams();\n    if (Object.keys(params).length) {\n      if ('sortOrder' in params) {\n        sort.direction = params[this.pageSortQueryParams.sortOrder].toLowerCase();\n      }\n      if ('sortBy' in params) {\n        sort.active = params[this.pageSortQueryParams.sortBy];\n      }\n    }\n  }\n  getFilterQueryParams() {\n    const params = this.getParams();\n    const ignoredParams = Object.keys(this.pageSortQueryParams);\n    return Object.keys(params).filter(key => ignoredParams.indexOf(key) === -1).reduce((data, key) => {\n      data[key] = params[key];\n      return data;\n    }, {});\n  }\n}, _SwuiGridUrlHandlerService.ctorParameters = () => [{\n  type: Router\n}, {\n  type: ActivatedRoute\n}], _SwuiGridUrlHandlerService);\nSwuiGridUrlHandlerService = __decorate([Injectable()], SwuiGridUrlHandlerService);\nexport { SwuiGridUrlHandlerService };\nlet MockUrlHandler = class MockUrlHandler {\n  // @ts-ignore\n  setAllowQueryParamsUpdate(allow) {}\n  // @ts-ignore\n  setParams(params) {}\n  // @ts-ignore\n  fetchPageSize(initial, override) {\n    return initial;\n  }\n  // @ts-ignore\n  setParamsToPaginator(paginator) {}\n  // @ts-ignore\n  setParamsToFilter(filter) {}\n  // @ts-ignore\n  setParamsToSort(sort) {}\n};\nMockUrlHandler = __decorate([Injectable()], MockUrlHandler);\nexport { MockUrlHandler };", "map": {"version": 3, "names": ["__decorate", "Injectable", "ActivatedRoute", "Router", "SwuiGridUrlHandlerService", "_SwuiGridUrlHandlerService", "constructor", "router", "activatedRoute", "allowQueryParamsUpdate", "pageSortQueryParams", "limit", "offset", "sortBy", "sortOrder", "setAllowQueryParamsUpdate", "allow", "setParams", "queryParams", "queryParamsHandling", "navigate", "relativeTo", "preserveFragment", "getParams", "snapshot", "fetchPageSize", "initial", "params", "fetched", "parseInt", "setParamsToPaginator", "paginator", "Object", "keys", "length", "pageIndex", "pageSize", "setParamsToSort", "sort", "direction", "toLowerCase", "active", "getFilterQueryParams", "ignoredParams", "filter", "key", "indexOf", "reduce", "data", "ctorParameters", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "override", "setParamsToFilter"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid-url-handler.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nlet SwuiGridUrlHandlerService = class SwuiGridUrlHandlerService {\n    constructor(router, activatedRoute) {\n        this.router = router;\n        this.activatedRoute = activatedRoute;\n        this.allowQueryParamsUpdate = true;\n        this.pageSortQueryParams = {\n            limit: 'limit',\n            offset: 'offset',\n            sortBy: 'sortBy',\n            sortOrder: 'sortOrder',\n        };\n    }\n    setAllowQueryParamsUpdate(allow) {\n        this.allowQueryParamsUpdate = allow;\n    }\n    setParams(queryParams, queryParamsHandling = '') {\n        if (this.allowQueryParamsUpdate) {\n            this.router.navigate([], {\n                relativeTo: this.activatedRoute,\n                queryParams,\n                queryParamsHandling,\n                preserveFragment: true,\n            });\n        }\n    }\n    getParams() {\n        return this.activatedRoute.snapshot.queryParams;\n    }\n    fetchPageSize(initial = 0) {\n        const params = this.getParams();\n        let fetched = initial;\n        if (params && 'limit' in params) {\n            fetched = parseInt(params['limit'], 10);\n        }\n        return fetched;\n    }\n    setParamsToPaginator(paginator) {\n        const params = this.getParams();\n        if (Object.keys(params).length) {\n            if ('offset' in params) {\n                paginator.pageIndex = parseInt(params[this.pageSortQueryParams.offset], 10) / paginator.pageSize;\n            }\n        }\n    }\n    setParamsToSort(sort) {\n        const params = this.getParams();\n        if (Object.keys(params).length) {\n            if ('sortOrder' in params) {\n                sort.direction = params[this.pageSortQueryParams.sortOrder].toLowerCase();\n            }\n            if ('sortBy' in params) {\n                sort.active = params[this.pageSortQueryParams.sortBy];\n            }\n        }\n    }\n    getFilterQueryParams() {\n        const params = this.getParams();\n        const ignoredParams = Object.keys(this.pageSortQueryParams);\n        return Object.keys(params)\n            .filter((key) => ignoredParams.indexOf(key) === -1)\n            .reduce((data, key) => {\n            data[key] = params[key];\n            return data;\n        }, {});\n    }\n    static { this.ctorParameters = () => [\n        { type: Router },\n        { type: ActivatedRoute }\n    ]; }\n};\nSwuiGridUrlHandlerService = __decorate([\n    Injectable()\n], SwuiGridUrlHandlerService);\nexport { SwuiGridUrlHandlerService };\nlet MockUrlHandler = class MockUrlHandler {\n    // @ts-ignore\n    setAllowQueryParamsUpdate(allow) {\n    }\n    // @ts-ignore\n    setParams(params) {\n    }\n    // @ts-ignore\n    fetchPageSize(initial, override) {\n        return initial;\n    }\n    // @ts-ignore\n    setParamsToPaginator(paginator) {\n    }\n    // @ts-ignore\n    setParamsToFilter(filter) {\n    }\n    // @ts-ignore\n    setParamsToSort(sort) {\n    }\n};\nMockUrlHandler = __decorate([\n    Injectable()\n], MockUrlHandler);\nexport { MockUrlHandler };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,IAAIC,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,CAAC;EAC5DE,WAAWA,CAACC,MAAM,EAAEC,cAAc,EAAE;IAChC,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACC,mBAAmB,GAAG;MACvBC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE;IACf,CAAC;EACL;EACAC,yBAAyBA,CAACC,KAAK,EAAE;IAC7B,IAAI,CAACP,sBAAsB,GAAGO,KAAK;EACvC;EACAC,SAASA,CAACC,WAAW,EAAEC,mBAAmB,GAAG,EAAE,EAAE;IAC7C,IAAI,IAAI,CAACV,sBAAsB,EAAE;MAC7B,IAAI,CAACF,MAAM,CAACa,QAAQ,CAAC,EAAE,EAAE;QACrBC,UAAU,EAAE,IAAI,CAACb,cAAc;QAC/BU,WAAW;QACXC,mBAAmB;QACnBG,gBAAgB,EAAE;MACtB,CAAC,CAAC;IACN;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACf,cAAc,CAACgB,QAAQ,CAACN,WAAW;EACnD;EACAO,aAAaA,CAACC,OAAO,GAAG,CAAC,EAAE;IACvB,MAAMC,MAAM,GAAG,IAAI,CAACJ,SAAS,CAAC,CAAC;IAC/B,IAAIK,OAAO,GAAGF,OAAO;IACrB,IAAIC,MAAM,IAAI,OAAO,IAAIA,MAAM,EAAE;MAC7BC,OAAO,GAAGC,QAAQ,CAACF,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;IAC3C;IACA,OAAOC,OAAO;EAClB;EACAE,oBAAoBA,CAACC,SAAS,EAAE;IAC5B,MAAMJ,MAAM,GAAG,IAAI,CAACJ,SAAS,CAAC,CAAC;IAC/B,IAAIS,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACO,MAAM,EAAE;MAC5B,IAAI,QAAQ,IAAIP,MAAM,EAAE;QACpBI,SAAS,CAACI,SAAS,GAAGN,QAAQ,CAACF,MAAM,CAAC,IAAI,CAACjB,mBAAmB,CAACE,MAAM,CAAC,EAAE,EAAE,CAAC,GAAGmB,SAAS,CAACK,QAAQ;MACpG;IACJ;EACJ;EACAC,eAAeA,CAACC,IAAI,EAAE;IAClB,MAAMX,MAAM,GAAG,IAAI,CAACJ,SAAS,CAAC,CAAC;IAC/B,IAAIS,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACO,MAAM,EAAE;MAC5B,IAAI,WAAW,IAAIP,MAAM,EAAE;QACvBW,IAAI,CAACC,SAAS,GAAGZ,MAAM,CAAC,IAAI,CAACjB,mBAAmB,CAACI,SAAS,CAAC,CAAC0B,WAAW,CAAC,CAAC;MAC7E;MACA,IAAI,QAAQ,IAAIb,MAAM,EAAE;QACpBW,IAAI,CAACG,MAAM,GAAGd,MAAM,CAAC,IAAI,CAACjB,mBAAmB,CAACG,MAAM,CAAC;MACzD;IACJ;EACJ;EACA6B,oBAAoBA,CAAA,EAAG;IACnB,MAAMf,MAAM,GAAG,IAAI,CAACJ,SAAS,CAAC,CAAC;IAC/B,MAAMoB,aAAa,GAAGX,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvB,mBAAmB,CAAC;IAC3D,OAAOsB,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CACrBiB,MAAM,CAAEC,GAAG,IAAKF,aAAa,CAACG,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAClDE,MAAM,CAAC,CAACC,IAAI,EAAEH,GAAG,KAAK;MACvBG,IAAI,CAACH,GAAG,CAAC,GAAGlB,MAAM,CAACkB,GAAG,CAAC;MACvB,OAAOG,IAAI;IACf,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;AAKJ,CAAC,EAJY3C,0BAAA,CAAK4C,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE/C;AAAO,CAAC,EAChB;EAAE+C,IAAI,EAAEhD;AAAe,CAAC,CAC3B,EAAAG,0BAAA,CACJ;AACDD,yBAAyB,GAAGJ,UAAU,CAAC,CACnCC,UAAU,CAAC,CAAC,CACf,EAAEG,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB;AAClC,IAAI+C,cAAc,GAAG,MAAMA,cAAc,CAAC;EACtC;EACApC,yBAAyBA,CAACC,KAAK,EAAE,CACjC;EACA;EACAC,SAASA,CAACU,MAAM,EAAE,CAClB;EACA;EACAF,aAAaA,CAACC,OAAO,EAAE0B,QAAQ,EAAE;IAC7B,OAAO1B,OAAO;EAClB;EACA;EACAI,oBAAoBA,CAACC,SAAS,EAAE,CAChC;EACA;EACAsB,iBAAiBA,CAACT,MAAM,EAAE,CAC1B;EACA;EACAP,eAAeA,CAACC,IAAI,EAAE,CACtB;AACJ,CAAC;AACDa,cAAc,GAAGnD,UAAU,CAAC,CACxBC,UAAU,CAAC,CAAC,CACf,EAAEkD,cAAc,CAAC;AAClB,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}