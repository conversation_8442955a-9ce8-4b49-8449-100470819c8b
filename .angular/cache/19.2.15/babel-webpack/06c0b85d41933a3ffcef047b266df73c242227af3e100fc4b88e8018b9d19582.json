{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { NumericRangeMenuComponent } from './swui-numeric-range-menu.component';\nlet SwuiNumericRangeMenuModule = class SwuiNumericRangeMenuModule {};\nSwuiNumericRangeMenuModule = __decorate([NgModule({\n  imports: [CommonModule, MatMenuModule, FormsModule, ReactiveFormsModule, MatInputModule, MatFormFieldModule, TranslateModule, MatIconModule, MatButtonModule],\n  exports: [NumericRangeMenuComponent],\n  declarations: [NumericRangeMenuComponent],\n  providers: []\n})], SwuiNumericRangeMenuModule);\nexport { SwuiNumericRangeMenuModule };", "map": {"version": 3, "names": ["__decorate", "CommonModule", "NgModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatMenuModule", "TranslateModule", "NumericRangeMenuComponent", "SwuiNumericRangeMenuModule", "imports", "exports", "declarations", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-numeric-range-menu/swui-numeric-range-menu.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { NumericRangeMenuComponent } from './swui-numeric-range-menu.component';\nlet SwuiNumericRangeMenuModule = class SwuiNumericRangeMenuModule {\n};\nSwuiNumericRangeMenuModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            MatMenuModule,\n            FormsModule,\n            ReactiveFormsModule,\n            MatInputModule,\n            MatFormFieldModule,\n            TranslateModule,\n            MatIconModule,\n            MatButtonModule\n        ],\n        exports: [NumericRangeMenuComponent],\n        declarations: [NumericRangeMenuComponent],\n        providers: [],\n    })\n], SwuiNumericRangeMenuModule);\nexport { SwuiNumericRangeMenuModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,yBAAyB,QAAQ,qCAAqC;AAC/E,IAAIC,0BAA0B,GAAG,MAAMA,0BAA0B,CAAC,EACjE;AACDA,0BAA0B,GAAGZ,UAAU,CAAC,CACpCE,QAAQ,CAAC;EACLW,OAAO,EAAE,CACLZ,YAAY,EACZQ,aAAa,EACbN,WAAW,EACXC,mBAAmB,EACnBI,cAAc,EACdF,kBAAkB,EAClBI,eAAe,EACfH,aAAa,EACbF,eAAe,CAClB;EACDS,OAAO,EAAE,CAACH,yBAAyB,CAAC;EACpCI,YAAY,EAAE,CAACJ,yBAAyB,CAAC;EACzCK,SAAS,EAAE;AACf,CAAC,CAAC,CACL,EAAEJ,0BAA0B,CAAC;AAC9B,SAASA,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}