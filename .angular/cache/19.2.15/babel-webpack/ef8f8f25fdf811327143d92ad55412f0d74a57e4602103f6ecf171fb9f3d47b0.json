{"ast": null, "code": "var _DefaultWidgetRegistry;\nimport { __decorate } from \"tslib\";\nimport { WidgetRegistry } from './registry';\nimport { Inject, Injectable } from '@angular/core';\nimport { getValidGridConfig, GRID_CONFIG } from '../swui-grid.config';\nlet DefaultWidgetRegistry = (_DefaultWidgetRegistry = class DefaultWidgetRegistry extends WidgetRegistry {\n  constructor(config) {\n    super('default');\n    this.config = config;\n    const {\n      widgets\n    } = getValidGridConfig(this.config);\n    this.registerWidgetsList(widgets);\n  }\n}, _DefaultWidgetRegistry.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [GRID_CONFIG]\n  }]\n}], _DefaultWidgetRegistry);\nDefaultWidgetRegistry = __decorate([Injectable()], DefaultWidgetRegistry);\nexport { DefaultWidgetRegistry };", "map": {"version": 3, "names": ["__decorate", "WidgetRegistry", "Inject", "Injectable", "getValidGridConfig", "GRID_CONFIG", "DefaultWidgetRegistry", "_DefaultWidgetRegistry", "constructor", "config", "widgets", "registerWidgetsList", "ctorParameters", "type", "undefined", "decorators", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/registry/default-registry.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { WidgetRegistry } from './registry';\nimport { Inject, Injectable } from '@angular/core';\nimport { getValidGridConfig, GRID_CONFIG } from '../swui-grid.config';\nlet DefaultWidgetRegistry = class DefaultWidgetRegistry extends WidgetRegistry {\n    constructor(config) {\n        super('default');\n        this.config = config;\n        const { widgets } = getValidGridConfig(this.config);\n        this.registerWidgetsList(widgets);\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [GRID_CONFIG,] }] }\n    ]; }\n};\nDefaultWidgetRegistry = __decorate([\n    Injectable()\n], DefaultWidgetRegistry);\nexport { DefaultWidgetRegistry };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,MAAM,EAAEC,UAAU,QAAQ,eAAe;AAClD,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,qBAAqB;AACrE,IAAIC,qBAAqB,IAAAC,sBAAA,GAAG,MAAMD,qBAAqB,SAASL,cAAc,CAAC;EAC3EO,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAAC,SAAS,CAAC;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,MAAM;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAAC,IAAI,CAACK,MAAM,CAAC;IACnD,IAAI,CAACE,mBAAmB,CAACD,OAAO,CAAC;EACrC;AAIJ,CAAC,EAHYH,sBAAA,CAAKK,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEX,MAAM;IAAEc,IAAI,EAAE,CAACX,WAAW;EAAG,CAAC;AAAE,CAAC,CAC5E,EAAAE,sBAAA,CACJ;AACDD,qBAAqB,GAAGN,UAAU,CAAC,CAC/BG,UAAU,CAAC,CAAC,CACf,EAAEG,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}