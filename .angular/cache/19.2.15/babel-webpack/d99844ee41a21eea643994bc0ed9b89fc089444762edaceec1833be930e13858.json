{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _GamesSelectManagerService;\nvar GamesSelectManagerService_1;\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { combineLatest, merge, of, ReplaySubject, Subject, zip } from 'rxjs';\nimport { createLabelIntersection, GameSelectItem } from './game-select-item/game-select-item.model';\nimport { map, mergeMap, startWith, tap } from 'rxjs/operators';\nimport { gameSelectItemTypes } from './game-select-item/game-select-item-types';\nimport { gameCategoryItemTypes } from './game-category-item.model';\nexport const extraColumnTypes = {\n  coinValue: 'coinValue',\n  gameCoeff: 'gameCoeff'\n};\nconst reduceObjectIdToObjectHash = (hash, obj) => _objectSpread(_objectSpread({}, hash), {}, {\n  [obj.id]: obj\n});\nlet GamesSelectManagerService = (_GamesSelectManagerService = class GamesSelectManagerService {\n  static calculateTotalGames(items) {\n    const reducer = (total, item) => {\n      let subTotal = 1;\n      if (item.isLabel) {\n        subTotal = item.items.length || 0;\n      } else if (item.type === gameSelectItemTypes.INTERSECTION) {\n        // subTotal = item.data['games'].length || 0;\n        subTotal = item.intersection.games.length || 0; // possible intersections bug\n      }\n      return total + subTotal;\n    };\n    return items.reduce(reducer, 0);\n  }\n  static clearPreviousCheckedState(games, labels) {\n    let clearChecked = item => {\n      item.checked = false;\n    };\n    games.map(clearChecked);\n    labels.map(clearChecked);\n  }\n  constructor() {\n    // subjects for components subscriptions\n    this.availableGames$ = new ReplaySubject(1);\n    this.availableLabels$ = new ReplaySubject(1);\n    this.selectedItems$ = new ReplaySubject(1);\n    this.gamesTotal$ = new ReplaySubject(1);\n    this.labelsTotal$ = new ReplaySubject(1);\n    this.selectedTotal$ = new ReplaySubject(1);\n    this.addedItems$ = new Subject();\n    this.removedItems$ = new Subject();\n    this.createdIntersection$ = new Subject();\n    this.intersectionDestroyed$ = new Subject();\n    this.checkedAvailableGamesCount$ = new Subject();\n    this.checkedAvailableCount$ = new Subject();\n    this.checkedSelectedGamesCount$ = new Subject();\n    this.checkedSelectedCount$ = new Subject();\n    // internal streams for data manipulation\n    this.categoryItemsStream = new Subject();\n    this.availableGameItemsStream = new Subject();\n    this.availableLabelItemsStream = new Subject();\n    this.selectedGameItemsStream = new Subject();\n    this.addStream = new Subject();\n    this.removeStream = new Subject();\n    this.refreshSelectedStream = new Subject();\n    this.addIntersectionStream = new Subject();\n    // internal cached data for proper service working\n    this.games = [];\n    this.labels = [];\n    this.selected = [];\n    this.checkedAvailableGamesCount = {\n      games: 0,\n      labelGames: 0,\n      intersectionGames: 0,\n      labels: 0\n    };\n    this.checkedSelectedGamesCount = {\n      games: 0,\n      labelGames: 0,\n      intersectionGames: 0,\n      labels: 0,\n      intersections: 0\n    };\n    this.initLabelGames();\n    this.initAvailableStreams();\n    this.initSelectedStreams();\n  }\n  setupCategoryItems(items) {\n    // this.items = items;\n    this.categoryItemsStream.next(items);\n  }\n  /**\n   * Creates GameSelectItem from games (using only in EMBEDDED mode)\n   */\n  setupSelectedGames(games) {\n    this.setupSelectedItems(games);\n  }\n  setupAvailableGames(games) {\n    this.games = games;\n    this.availableGameItemsStream.next(games);\n  }\n  setupAvailableLabels(labels) {\n    this.labels = labels;\n    this.availableLabelItemsStream.next(labels);\n  }\n  setupSelectedItems(items) {\n    this.selected = items;\n    this.selectedGameItemsStream.next(items);\n  }\n  refreshSelected() {\n    this.refreshSelectedStream.next(true);\n  }\n  addToSelectedItems() {\n    this.addStream.next(true);\n  }\n  removeFromSelectedItems() {\n    this.removeStream.next(true);\n  }\n  createIntersection(labels) {\n    this.createdIntersection$.next(createLabelIntersection(labels));\n  }\n  removeAvailableIntersection(uncheckLabels = false) {\n    this.intersectionDestroyed$.next({\n      remove: true,\n      uncheckLabels\n    });\n  }\n  addIntersection(intersection) {\n    this.addIntersectionStream.next(intersection);\n    this.removeAvailableIntersection(true);\n  }\n  calculateCheckedAvailableGames(games) {\n    this.checkedAvailableGamesCount.games = games.length;\n    this.notifyAboutCheckedAvailableGames();\n  }\n  calculateCheckedAvailableLabels(labels) {\n    this.checkedAvailableGamesCount.labelGames = labels.length === 1 ? labels[0].items.length : 0;\n    this.checkedAvailableGamesCount.labels = labels.length;\n    this.notifyAboutCheckedAvailableGames();\n  }\n  calculateCheckedAvailableIntersection(item) {\n    this.checkedAvailableGamesCount.intersectionGames = item !== null ? item.intersection.games.length : 0;\n    this.notifyAboutCheckedAvailableGames();\n  }\n  calculateCheckedSelectedGames(games) {\n    this.checkedSelectedGamesCount.games = games.length;\n    this.notifyAboutCheckedSelectedGames();\n  }\n  calculateCheckedSelectedLabels(labels) {\n    this.checkedSelectedGamesCount.labelGames = labels.reduce((total, label) => total + label.items.length, 0);\n    this.checkedSelectedGamesCount.labels = labels.length;\n    this.notifyAboutCheckedSelectedGames();\n  }\n  calculateCheckedSelectedIntersections(intersections) {\n    this.checkedSelectedGamesCount.intersectionGames = intersections.reduce((total, intersection) => total + intersection.intersection.games.length, 0);\n    this.checkedSelectedGamesCount.intersections = intersections.length;\n    this.notifyAboutCheckedSelectedGames();\n  }\n  filterGames(items, search) {\n    return items.filter(item => {\n      let {\n        title,\n        code,\n        labels\n      } = item.gameInfo;\n      let needle = search.toLowerCase();\n      return title.toLowerCase().indexOf(needle) > -1 || code.toLowerCase().indexOf(needle) > -1 || labels.map(label => label.title.toLowerCase()).filter(text => text.indexOf(needle) > -1).length > 0;\n    });\n  }\n  filterLabels(items, search) {\n    return items.filter(item => item.label.title.toLowerCase().indexOf(search.toLowerCase()) > -1);\n  }\n  initAvailableStreams() {\n    this.initAvailableGamesStreams();\n    this.initAvailableLabelsStreams();\n  }\n  initAvailableGamesStreams() {\n    const games$ = this.availableGameItemsStream.pipe(tap(games => this.games = games), map(games => ({\n      games,\n      selected: this.selected\n    })));\n    const selected$ = this.selectedGameItemsStream.pipe(tap(selected => this.selected = selected), map(selected => ({\n      selected,\n      games: this.games\n    })));\n    const filteredAvailableGames$ = merge(games$, selected$).pipe(startWith({\n      games: this.games,\n      selected: this.selected\n    }), mergeMap(({\n      games,\n      selected\n    }) => {\n      const filtered = games.filter(item => selected.map(game => game.id).indexOf(item.id) === -1);\n      this.gamesTotal$.next(filtered.length);\n      return of(filtered);\n    }));\n    filteredAvailableGames$.subscribe(games => {\n      this.availableGames$.next(games);\n    });\n  }\n  initAvailableLabelsStreams() {\n    const labels$ = this.availableLabelItemsStream.pipe(tap(labels => {\n      this.labels = labels;\n    }), map(labels => ({\n      labels,\n      selected: this.selected\n    })));\n    const selected$ = this.selectedGameItemsStream.pipe(tap(selected => {\n      this.selected = selected;\n    }), map(selected => ({\n      selected,\n      labels: this.labels\n    })));\n    const filteredAvailableLabels$ = merge(labels$, selected$).pipe(startWith({\n      labels: this.labels,\n      selected: this.selected\n    }), map(({\n      labels,\n      selected\n    }) => {\n      // hiding selected labels\n      return labels.filter(({\n        id\n      }) => selected.find(({\n        id: selectedId\n      }) => id === selectedId) !== null);\n    }), tap(labels => {\n      this.labelsTotal$.next(labels.length);\n    }));\n    // const filteredAvailableLabels$ = merge(labels$, selected$)\n    //   .pipe(\n    //     startWith({labels: this.labels, selected: this.selected}),\n    //     mergeMap(({labels, selected}) => {\n    //\n    //       let realAvailable$: Observable<GameSelectItem[]> = of(labels)\n    //         .pipe(\n    //           map(items => items.filter(({id}) => // hiding selected labels\n    //             selected.map(selectedItem => selectedItem.id).indexOf(id) === -1\n    //             )\n    //           ),\n    //           flatMap((data: GameSelectItem[]) => of(data))\n    //         );\n    //\n    //       realAvailable$.pipe(count()).subscribe(total => this.labelsTotal$.next(total));\n    //       return realAvailable$.pipe(\n    //         combineAll(),\n    //         defaultIfEmpty([])\n    //       );\n    //     })\n    //   );\n    filteredAvailableLabels$.subscribe(labels => {\n      this.availableLabels$.next(labels);\n    });\n  }\n  initSelectedStreams() {\n    this.populateCategoryGameItems();\n    const added$ = this.addStream.pipe(map(added => {\n      const available = [...this.games, ...this.labels];\n      let selected = this.selected;\n      if (added) {\n        let addedItems = [...available.filter(item => this.selected.indexOf(item) === -1).filter(item => {\n          let checked = item.checked;\n          item.checked = false;\n          return checked;\n        })];\n        const labelsWereAdded = addedItems.some(item => item.isLabel);\n        if (labelsWereAdded) {\n          this.removeAvailableIntersection(true);\n        }\n        this.addedItems$.next(addedItems);\n        selected = [...selected, ...addedItems];\n      }\n      return {\n        selected\n      };\n    }));\n    const removed$ = this.removeStream.pipe(map(removed => {\n      let selected = this.selected;\n      if (removed) {\n        let removedItems = selected.filter(item => item.checked);\n        selected = selected.filter(item => {\n          let itemStillSelected = !item.checked;\n          if (!itemStillSelected) {\n            item.toggleCheck();\n          }\n          return itemStillSelected;\n        });\n        this.removedItems$.next(removedItems);\n      }\n      return {\n        selected\n      };\n    }));\n    merge(added$, removed$).subscribe(({\n      selected\n    }) => this.setupSelectedItems(selected));\n    const refreshed$ = this.refreshSelectedStream.pipe(map(() => {\n      const selected = this.selected;\n      return {\n        selected\n      };\n    }));\n    refreshed$.subscribe(({\n      selected\n    }) => this.setupSelectedItems(selected));\n    const intersectionAdded$ = this.addIntersectionStream.pipe(map(item => {\n      const selected = this.selected;\n      selected.push(item);\n      return {\n        selected\n      };\n    }));\n    intersectionAdded$.subscribe(({\n      selected\n    }) => this.setupSelectedItems(selected));\n    this.selectedGameItemsStream.subscribe(items => {\n      this.selectedItems$.next(items);\n      this.selectedTotal$.next(GamesSelectManagerService_1.calculateTotalGames(items));\n    });\n  }\n  /**\n   * Creates items for GameSelectItem type=label (items contains array of GameSelectItem with type=game)\n   * same for GameSelectItem type=provider\n   */\n  initLabelGames() {\n    zip(this.availableGameItemsStream, this.availableLabelItemsStream).subscribe(([games, labels]) => {\n      let labelsHash = labels.map(label => {\n        label.items = [];\n        return label;\n      }).reduce(reduceObjectIdToObjectHash, {});\n      games.forEach(game => {\n        if (game) {\n          (game.getGameLabels() || []).forEach(label => {\n            if (label && label.id && label.id in labelsHash) {\n              labelsHash[label.id].addGameToLabel(game);\n            }\n          });\n        }\n      });\n    });\n  }\n  populateCategoryGameItems() {\n    combineLatest([this.categoryItemsStream, this.availableGameItemsStream, this.availableLabelItemsStream]).subscribe(([items, games, labels]) => {\n      let selected = this.sanitizeCategoryItems(games, labels, items);\n      GamesSelectManagerService_1.clearPreviousCheckedState(games, labels);\n      this.setupSelectedItems(selected);\n    });\n  }\n  /**\n   *  Method which replaces category items to gameSelectItems with data from available games and labels\n   *\n   */\n  sanitizeCategoryItems(games, labels, items) {\n    let gamesHash = games.reduce(reduceObjectIdToObjectHash, {});\n    let labelsHash = labels.reduce(reduceObjectIdToObjectHash, {});\n    const replaceCategoryItems = item => {\n      let result;\n      switch (item.type) {\n        case gameCategoryItemTypes.GAME:\n          if (item.id && item.id in gamesHash) {\n            result = gamesHash[item.id];\n          } else {\n            result = new GameSelectItem({\n              id: item.id,\n              type: gameSelectItemTypes.CORRUPTION,\n              data: item\n            });\n          }\n          break;\n        case gameCategoryItemTypes.LABEL:\n        case gameCategoryItemTypes.PROVIDER:\n          if (item.id && item.id in labelsHash) {\n            result = labelsHash[item.id];\n          } else {\n            result = new GameSelectItem({\n              id: item.id,\n              type: gameSelectItemTypes.CORRUPTION,\n              data: item\n            });\n          }\n          break;\n        case gameCategoryItemTypes.INTERSECTION:\n          let intersectionItems = (item.items || []).map(intersectionItem => replaceCategoryItems(intersectionItem));\n          result = createLabelIntersection(intersectionItems);\n          result.id = item.id;\n          break;\n        default:\n          throw new Error('Unknown gameCategoryItemType');\n      }\n      return result;\n    };\n    return items.map(replaceCategoryItems);\n  }\n  notifyAboutCheckedSelectedGames() {\n    const {\n      games,\n      labelGames,\n      intersectionGames,\n      labels,\n      intersections\n    } = this.checkedSelectedGamesCount;\n    const totalCount = games + labelGames + intersectionGames;\n    this.checkedSelectedGamesCount$.next(totalCount);\n    this.checkedSelectedCount$.next(totalCount + labels + intersections);\n  }\n  notifyAboutCheckedAvailableGames() {\n    const {\n      games,\n      labelGames,\n      intersectionGames,\n      labels\n    } = this.checkedAvailableGamesCount;\n    const totalCount = games + labelGames + intersectionGames;\n    this.checkedAvailableGamesCount$.next(totalCount);\n    this.checkedAvailableCount$.next(totalCount + labels);\n  }\n}, GamesSelectManagerService_1 = _GamesSelectManagerService, _GamesSelectManagerService.ctorParameters = () => [], _GamesSelectManagerService);\nGamesSelectManagerService = GamesSelectManagerService_1 = __decorate([Injectable()], GamesSelectManagerService);\nexport { GamesSelectManagerService };", "map": {"version": 3, "names": ["GamesSelectManagerService_1", "__decorate", "Injectable", "combineLatest", "merge", "of", "ReplaySubject", "Subject", "zip", "createLabelIntersection", "GameSelectItem", "map", "mergeMap", "startWith", "tap", "gameSelectItemTypes", "gameCategoryItemTypes", "extraColumnTypes", "coinValue", "<PERSON><PERSON><PERSON><PERSON>", "reduceObjectIdToObjectHash", "hash", "obj", "_objectSpread", "id", "GamesSelectManagerService", "_GamesSelectManagerService", "calculateTotalGames", "items", "reducer", "total", "item", "subTotal", "isLabel", "length", "type", "INTERSECTION", "intersection", "games", "reduce", "clearPreviousCheckedState", "labels", "clearChecked", "checked", "constructor", "availableGames$", "availableLabels$", "selectedItems$", "gamesTotal$", "labelsTotal$", "selectedTotal$", "addedItems$", "removedItems$", "createdIntersection$", "intersectionDestroyed$", "checkedAvailableGamesCount$", "checkedAvailableCount$", "checkedSelectedGamesCount$", "checkedSelectedCount$", "categoryItemsStream", "availableGameItemsStream", "availableLabelItemsStream", "selectedGameItemsStream", "addStream", "removeStream", "refreshSelectedStream", "addIntersectionStream", "selected", "checkedAvailableGamesCount", "labelGames", "intersectionGames", "checkedSelectedGamesCount", "intersections", "initLabelGames", "initAvailableStreams", "initSelectedStreams", "setupCategoryItems", "next", "setupSelectedGames", "setupSelectedItems", "setupAvailableGames", "setupAvailableLabels", "refreshSelected", "addToSelectedItems", "removeFromSelectedItems", "createIntersection", "removeAvailableIntersection", "uncheckLabels", "remove", "addIntersection", "calculateCheckedAvailableGames", "notifyAboutCheckedAvailableGames", "calculateCheckedAvailableLabels", "calculateCheckedAvailableIntersection", "calculateCheckedSelectedGames", "notifyAboutCheckedSelectedGames", "calculateCheckedSelectedLabels", "label", "calculateCheckedSelectedIntersections", "filterGames", "search", "filter", "title", "code", "gameInfo", "needle", "toLowerCase", "indexOf", "text", "filterLabels", "initAvailableGamesStreams", "initAvailableLabelsStreams", "games$", "pipe", "selected$", "filteredAvailableGames$", "filtered", "game", "subscribe", "labels$", "filteredAvailableLabels$", "find", "selectedId", "populateCategoryGameItems", "added$", "added", "available", "addedItems", "labelsWereAdded", "some", "removed$", "removed", "removedItems", "itemStillSelected", "to<PERSON><PERSON><PERSON><PERSON>", "refreshed$", "intersectionAdded$", "push", "labelsHash", "for<PERSON>ach", "getGameLabels", "addGameToLabel", "sanitizeCategoryItems", "gamesHash", "replaceCategoryItems", "result", "GAME", "CORRUPTION", "data", "LABEL", "PROVIDER", "intersectionItems", "intersectionItem", "Error", "totalCount", "ctorParameters"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/games-select-manager.service.ts"], "sourcesContent": ["var GamesSelectManagerService_1;\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { combineLatest, merge, of, ReplaySubject, Subject, zip } from 'rxjs';\nimport { createLabelIntersection, GameSelectItem } from './game-select-item/game-select-item.model';\nimport { map, mergeMap, startWith, tap, } from 'rxjs/operators';\nimport { gameSelectItemTypes } from './game-select-item/game-select-item-types';\nimport { gameCategoryItemTypes } from './game-category-item.model';\nexport const extraColumnTypes = {\n    coinValue: 'coinValue',\n    gameCoeff: 'gameCoeff',\n};\nconst reduceObjectIdToObjectHash = (hash, obj) => ({ ...hash, [obj.id]: obj });\nlet GamesSelectManagerService = class GamesSelectManagerService {\n    static { GamesSelectManagerService_1 = this; }\n    static calculateTotalGames(items) {\n        const reducer = (total, item) => {\n            let subTotal = 1;\n            if (item.isLabel) {\n                subTotal = item.items.length || 0;\n            }\n            else if (item.type === gameSelectItemTypes.INTERSECTION) {\n                // subTotal = item.data['games'].length || 0;\n                subTotal = item.intersection.games.length || 0; // possible intersections bug\n            }\n            return total + subTotal;\n        };\n        return items.reduce(reducer, 0);\n    }\n    static clearPreviousCheckedState(games, labels) {\n        let clearChecked = (item) => {\n            item.checked = false;\n        };\n        games.map(clearChecked);\n        labels.map(clearChecked);\n    }\n    constructor() {\n        // subjects for components subscriptions\n        this.availableGames$ = new ReplaySubject(1);\n        this.availableLabels$ = new ReplaySubject(1);\n        this.selectedItems$ = new ReplaySubject(1);\n        this.gamesTotal$ = new ReplaySubject(1);\n        this.labelsTotal$ = new ReplaySubject(1);\n        this.selectedTotal$ = new ReplaySubject(1);\n        this.addedItems$ = new Subject();\n        this.removedItems$ = new Subject();\n        this.createdIntersection$ = new Subject();\n        this.intersectionDestroyed$ = new Subject();\n        this.checkedAvailableGamesCount$ = new Subject();\n        this.checkedAvailableCount$ = new Subject();\n        this.checkedSelectedGamesCount$ = new Subject();\n        this.checkedSelectedCount$ = new Subject();\n        // internal streams for data manipulation\n        this.categoryItemsStream = new Subject();\n        this.availableGameItemsStream = new Subject();\n        this.availableLabelItemsStream = new Subject();\n        this.selectedGameItemsStream = new Subject();\n        this.addStream = new Subject();\n        this.removeStream = new Subject();\n        this.refreshSelectedStream = new Subject();\n        this.addIntersectionStream = new Subject();\n        // internal cached data for proper service working\n        this.games = [];\n        this.labels = [];\n        this.selected = [];\n        this.checkedAvailableGamesCount = {\n            games: 0, labelGames: 0, intersectionGames: 0, labels: 0\n        };\n        this.checkedSelectedGamesCount = {\n            games: 0, labelGames: 0, intersectionGames: 0, labels: 0, intersections: 0\n        };\n        this.initLabelGames();\n        this.initAvailableStreams();\n        this.initSelectedStreams();\n    }\n    setupCategoryItems(items) {\n        // this.items = items;\n        this.categoryItemsStream.next(items);\n    }\n    /**\n     * Creates GameSelectItem from games (using only in EMBEDDED mode)\n     */\n    setupSelectedGames(games) {\n        this.setupSelectedItems(games);\n    }\n    setupAvailableGames(games) {\n        this.games = games;\n        this.availableGameItemsStream.next(games);\n    }\n    setupAvailableLabels(labels) {\n        this.labels = labels;\n        this.availableLabelItemsStream.next(labels);\n    }\n    setupSelectedItems(items) {\n        this.selected = items;\n        this.selectedGameItemsStream.next(items);\n    }\n    refreshSelected() {\n        this.refreshSelectedStream.next(true);\n    }\n    addToSelectedItems() {\n        this.addStream.next(true);\n    }\n    removeFromSelectedItems() {\n        this.removeStream.next(true);\n    }\n    createIntersection(labels) {\n        this.createdIntersection$.next(createLabelIntersection(labels));\n    }\n    removeAvailableIntersection(uncheckLabels = false) {\n        this.intersectionDestroyed$.next({ remove: true, uncheckLabels });\n    }\n    addIntersection(intersection) {\n        this.addIntersectionStream.next(intersection);\n        this.removeAvailableIntersection(true);\n    }\n    calculateCheckedAvailableGames(games) {\n        this.checkedAvailableGamesCount.games = games.length;\n        this.notifyAboutCheckedAvailableGames();\n    }\n    calculateCheckedAvailableLabels(labels) {\n        this.checkedAvailableGamesCount.labelGames = labels.length === 1 ? labels[0].items.length : 0;\n        this.checkedAvailableGamesCount.labels = labels.length;\n        this.notifyAboutCheckedAvailableGames();\n    }\n    calculateCheckedAvailableIntersection(item) {\n        this.checkedAvailableGamesCount.intersectionGames = item !== null ? item.intersection.games.length : 0;\n        this.notifyAboutCheckedAvailableGames();\n    }\n    calculateCheckedSelectedGames(games) {\n        this.checkedSelectedGamesCount.games = games.length;\n        this.notifyAboutCheckedSelectedGames();\n    }\n    calculateCheckedSelectedLabels(labels) {\n        this.checkedSelectedGamesCount.labelGames = labels.reduce((total, label) => (total + label.items.length), 0);\n        this.checkedSelectedGamesCount.labels = labels.length;\n        this.notifyAboutCheckedSelectedGames();\n    }\n    calculateCheckedSelectedIntersections(intersections) {\n        this.checkedSelectedGamesCount.intersectionGames =\n            intersections.reduce((total, intersection) => (total + intersection.intersection.games.length), 0);\n        this.checkedSelectedGamesCount.intersections = intersections.length;\n        this.notifyAboutCheckedSelectedGames();\n    }\n    filterGames(items, search) {\n        return items.filter((item) => {\n            let { title, code, labels } = item.gameInfo;\n            let needle = search.toLowerCase();\n            return title.toLowerCase().indexOf(needle) > -1\n                || code.toLowerCase().indexOf(needle) > -1\n                || labels.map(label => label.title.toLowerCase())\n                    .filter(text => text.indexOf(needle) > -1).length > 0;\n        });\n    }\n    filterLabels(items, search) {\n        return items.filter((item) => item.label.title.toLowerCase().indexOf(search.toLowerCase()) > -1);\n    }\n    initAvailableStreams() {\n        this.initAvailableGamesStreams();\n        this.initAvailableLabelsStreams();\n    }\n    initAvailableGamesStreams() {\n        const games$ = this.availableGameItemsStream.pipe(tap(games => this.games = games), map((games) => ({ games, selected: this.selected })));\n        const selected$ = this.selectedGameItemsStream.pipe(tap(selected => this.selected = selected), map((selected) => ({ selected, games: this.games })));\n        const filteredAvailableGames$ = merge(games$, selected$)\n            .pipe(startWith({ games: this.games, selected: this.selected }), mergeMap(({ games, selected }) => {\n            const filtered = games.filter(item => selected.map(game => game.id).indexOf(item.id) === -1);\n            this.gamesTotal$.next(filtered.length);\n            return of(filtered);\n        }));\n        filteredAvailableGames$.subscribe(games => {\n            this.availableGames$.next(games);\n        });\n    }\n    initAvailableLabelsStreams() {\n        const labels$ = this.availableLabelItemsStream.pipe(tap(labels => {\n            this.labels = labels;\n        }), map(labels => ({ labels, selected: this.selected })));\n        const selected$ = this.selectedGameItemsStream.pipe(tap(selected => {\n            this.selected = selected;\n        }), map(selected => ({ selected, labels: this.labels })));\n        const filteredAvailableLabels$ = merge(labels$, selected$)\n            .pipe(startWith({ labels: this.labels, selected: this.selected }), map(({ labels, selected }) => {\n            // hiding selected labels\n            return labels.filter(({ id }) => selected.find(({ id: selectedId }) => id === selectedId) !== null);\n        }), tap(labels => {\n            this.labelsTotal$.next(labels.length);\n        }));\n        // const filteredAvailableLabels$ = merge(labels$, selected$)\n        //   .pipe(\n        //     startWith({labels: this.labels, selected: this.selected}),\n        //     mergeMap(({labels, selected}) => {\n        //\n        //       let realAvailable$: Observable<GameSelectItem[]> = of(labels)\n        //         .pipe(\n        //           map(items => items.filter(({id}) => // hiding selected labels\n        //             selected.map(selectedItem => selectedItem.id).indexOf(id) === -1\n        //             )\n        //           ),\n        //           flatMap((data: GameSelectItem[]) => of(data))\n        //         );\n        //\n        //       realAvailable$.pipe(count()).subscribe(total => this.labelsTotal$.next(total));\n        //       return realAvailable$.pipe(\n        //         combineAll(),\n        //         defaultIfEmpty([])\n        //       );\n        //     })\n        //   );\n        filteredAvailableLabels$.subscribe((labels) => {\n            this.availableLabels$.next(labels);\n        });\n    }\n    initSelectedStreams() {\n        this.populateCategoryGameItems();\n        const added$ = this.addStream.pipe(map((added) => {\n            const available = [...this.games, ...this.labels];\n            let selected = this.selected;\n            if (added) {\n                let addedItems = [\n                    ...available\n                        .filter(item => this.selected.indexOf(item) === -1)\n                        .filter(item => {\n                        let checked = item.checked;\n                        item.checked = false;\n                        return checked;\n                    })\n                ];\n                const labelsWereAdded = addedItems.some(item => item.isLabel);\n                if (labelsWereAdded) {\n                    this.removeAvailableIntersection(true);\n                }\n                this.addedItems$.next(addedItems);\n                selected = [...selected, ...addedItems];\n            }\n            return { selected };\n        }));\n        const removed$ = this.removeStream.pipe(map((removed) => {\n            let selected = this.selected;\n            if (removed) {\n                let removedItems = selected.filter(item => item.checked);\n                selected = selected.filter(item => {\n                    let itemStillSelected = !item.checked;\n                    if (!itemStillSelected) {\n                        item.toggleCheck();\n                    }\n                    return itemStillSelected;\n                });\n                this.removedItems$.next(removedItems);\n            }\n            return { selected };\n        }));\n        merge(added$, removed$).subscribe(({ selected }) => this.setupSelectedItems(selected));\n        const refreshed$ = this.refreshSelectedStream\n            .pipe(map(() => {\n            const selected = this.selected;\n            return { selected };\n        }));\n        refreshed$.subscribe(({ selected }) => this.setupSelectedItems(selected));\n        const intersectionAdded$ = this.addIntersectionStream.pipe(map((item) => {\n            const selected = this.selected;\n            selected.push(item);\n            return { selected };\n        }));\n        intersectionAdded$.subscribe(({ selected }) => this.setupSelectedItems(selected));\n        this.selectedGameItemsStream.subscribe((items) => {\n            this.selectedItems$.next(items);\n            this.selectedTotal$.next(GamesSelectManagerService_1.calculateTotalGames(items));\n        });\n    }\n    /**\n     * Creates items for GameSelectItem type=label (items contains array of GameSelectItem with type=game)\n     * same for GameSelectItem type=provider\n     */\n    initLabelGames() {\n        zip(this.availableGameItemsStream, this.availableLabelItemsStream).subscribe(([games, labels]) => {\n            let labelsHash = labels\n                .map(label => {\n                label.items = [];\n                return label;\n            })\n                .reduce(reduceObjectIdToObjectHash, {});\n            games.forEach((game) => {\n                if (game) {\n                    (game.getGameLabels() || []).forEach((label) => {\n                        if (label && label.id && label.id in labelsHash) {\n                            labelsHash[label.id].addGameToLabel(game);\n                        }\n                    });\n                }\n            });\n        });\n    }\n    populateCategoryGameItems() {\n        combineLatest([\n            this.categoryItemsStream,\n            this.availableGameItemsStream,\n            this.availableLabelItemsStream\n        ]).subscribe(([items, games, labels]) => {\n            let selected = this.sanitizeCategoryItems(games, labels, items);\n            GamesSelectManagerService_1.clearPreviousCheckedState(games, labels);\n            this.setupSelectedItems(selected);\n        });\n    }\n    /**\n     *  Method which replaces category items to gameSelectItems with data from available games and labels\n     *\n     */\n    sanitizeCategoryItems(games, labels, items) {\n        let gamesHash = games.reduce(reduceObjectIdToObjectHash, {});\n        let labelsHash = labels.reduce(reduceObjectIdToObjectHash, {});\n        const replaceCategoryItems = (item) => {\n            let result;\n            switch (item.type) {\n                case gameCategoryItemTypes.GAME:\n                    if (item.id && item.id in gamesHash) {\n                        result = gamesHash[item.id];\n                    }\n                    else {\n                        result = new GameSelectItem({\n                            id: item.id,\n                            type: gameSelectItemTypes.CORRUPTION,\n                            data: item,\n                        });\n                    }\n                    break;\n                case gameCategoryItemTypes.LABEL:\n                case gameCategoryItemTypes.PROVIDER:\n                    if (item.id && item.id in labelsHash) {\n                        result = labelsHash[item.id];\n                    }\n                    else {\n                        result = new GameSelectItem({\n                            id: item.id,\n                            type: gameSelectItemTypes.CORRUPTION,\n                            data: item,\n                        });\n                    }\n                    break;\n                case gameCategoryItemTypes.INTERSECTION:\n                    let intersectionItems = (item.items || []).map(intersectionItem => replaceCategoryItems(intersectionItem));\n                    result = createLabelIntersection(intersectionItems);\n                    result.id = item.id;\n                    break;\n                default:\n                    throw new Error('Unknown gameCategoryItemType');\n            }\n            return result;\n        };\n        return items.map(replaceCategoryItems);\n    }\n    notifyAboutCheckedSelectedGames() {\n        const { games, labelGames, intersectionGames, labels, intersections } = this.checkedSelectedGamesCount;\n        const totalCount = games + labelGames + intersectionGames;\n        this.checkedSelectedGamesCount$.next(totalCount);\n        this.checkedSelectedCount$.next(totalCount + labels + intersections);\n    }\n    notifyAboutCheckedAvailableGames() {\n        const { games, labelGames, intersectionGames, labels } = this.checkedAvailableGamesCount;\n        const totalCount = games + labelGames + intersectionGames;\n        this.checkedAvailableGamesCount$.next(totalCount);\n        this.checkedAvailableCount$.next(totalCount + labels);\n    }\n    static { this.ctorParameters = () => []; }\n};\nGamesSelectManagerService = GamesSelectManagerService_1 = __decorate([\n    Injectable()\n], GamesSelectManagerService);\nexport { GamesSelectManagerService };\n"], "mappings": ";;AAAA,IAAIA,2BAA2B;AAC/B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,aAAa,EAAEC,KAAK,EAAEC,EAAE,EAAEC,aAAa,EAAEC,OAAO,EAAEC,GAAG,QAAQ,MAAM;AAC5E,SAASC,uBAAuB,EAAEC,cAAc,QAAQ,2CAA2C;AACnG,SAASC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAS,gBAAgB;AAC/D,SAASC,mBAAmB,QAAQ,2CAA2C;AAC/E,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,OAAO,MAAMC,gBAAgB,GAAG;EAC5BC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE;AACf,CAAC;AACD,MAAMC,0BAA0B,GAAGA,CAACC,IAAI,EAAEC,GAAG,KAAAC,aAAA,CAAAA,aAAA,KAAWF,IAAI;EAAE,CAACC,GAAG,CAACE,EAAE,GAAGF;AAAG,EAAG;AAC9E,IAAIG,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,CAAC;EAE5D,OAAOE,mBAAmBA,CAACC,KAAK,EAAE;IAC9B,MAAMC,OAAO,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;MAC7B,IAAIC,QAAQ,GAAG,CAAC;MAChB,IAAID,IAAI,CAACE,OAAO,EAAE;QACdD,QAAQ,GAAGD,IAAI,CAACH,KAAK,CAACM,MAAM,IAAI,CAAC;MACrC,CAAC,MACI,IAAIH,IAAI,CAACI,IAAI,KAAKpB,mBAAmB,CAACqB,YAAY,EAAE;QACrD;QACAJ,QAAQ,GAAGD,IAAI,CAACM,YAAY,CAACC,KAAK,CAACJ,MAAM,IAAI,CAAC,CAAC,CAAC;MACpD;MACA,OAAOJ,KAAK,GAAGE,QAAQ;IAC3B,CAAC;IACD,OAAOJ,KAAK,CAACW,MAAM,CAACV,OAAO,EAAE,CAAC,CAAC;EACnC;EACA,OAAOW,yBAAyBA,CAACF,KAAK,EAAEG,MAAM,EAAE;IAC5C,IAAIC,YAAY,GAAIX,IAAI,IAAK;MACzBA,IAAI,CAACY,OAAO,GAAG,KAAK;IACxB,CAAC;IACDL,KAAK,CAAC3B,GAAG,CAAC+B,YAAY,CAAC;IACvBD,MAAM,CAAC9B,GAAG,CAAC+B,YAAY,CAAC;EAC5B;EACAE,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,eAAe,GAAG,IAAIvC,aAAa,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACwC,gBAAgB,GAAG,IAAIxC,aAAa,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACyC,cAAc,GAAG,IAAIzC,aAAa,CAAC,CAAC,CAAC;IAC1C,IAAI,CAAC0C,WAAW,GAAG,IAAI1C,aAAa,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC2C,YAAY,GAAG,IAAI3C,aAAa,CAAC,CAAC,CAAC;IACxC,IAAI,CAAC4C,cAAc,GAAG,IAAI5C,aAAa,CAAC,CAAC,CAAC;IAC1C,IAAI,CAAC6C,WAAW,GAAG,IAAI5C,OAAO,CAAC,CAAC;IAChC,IAAI,CAAC6C,aAAa,GAAG,IAAI7C,OAAO,CAAC,CAAC;IAClC,IAAI,CAAC8C,oBAAoB,GAAG,IAAI9C,OAAO,CAAC,CAAC;IACzC,IAAI,CAAC+C,sBAAsB,GAAG,IAAI/C,OAAO,CAAC,CAAC;IAC3C,IAAI,CAACgD,2BAA2B,GAAG,IAAIhD,OAAO,CAAC,CAAC;IAChD,IAAI,CAACiD,sBAAsB,GAAG,IAAIjD,OAAO,CAAC,CAAC;IAC3C,IAAI,CAACkD,0BAA0B,GAAG,IAAIlD,OAAO,CAAC,CAAC;IAC/C,IAAI,CAACmD,qBAAqB,GAAG,IAAInD,OAAO,CAAC,CAAC;IAC1C;IACA,IAAI,CAACoD,mBAAmB,GAAG,IAAIpD,OAAO,CAAC,CAAC;IACxC,IAAI,CAACqD,wBAAwB,GAAG,IAAIrD,OAAO,CAAC,CAAC;IAC7C,IAAI,CAACsD,yBAAyB,GAAG,IAAItD,OAAO,CAAC,CAAC;IAC9C,IAAI,CAACuD,uBAAuB,GAAG,IAAIvD,OAAO,CAAC,CAAC;IAC5C,IAAI,CAACwD,SAAS,GAAG,IAAIxD,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACyD,YAAY,GAAG,IAAIzD,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC0D,qBAAqB,GAAG,IAAI1D,OAAO,CAAC,CAAC;IAC1C,IAAI,CAAC2D,qBAAqB,GAAG,IAAI3D,OAAO,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC+B,KAAK,GAAG,EAAE;IACf,IAAI,CAACG,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC0B,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,0BAA0B,GAAG;MAC9B9B,KAAK,EAAE,CAAC;MAAE+B,UAAU,EAAE,CAAC;MAAEC,iBAAiB,EAAE,CAAC;MAAE7B,MAAM,EAAE;IAC3D,CAAC;IACD,IAAI,CAAC8B,yBAAyB,GAAG;MAC7BjC,KAAK,EAAE,CAAC;MAAE+B,UAAU,EAAE,CAAC;MAAEC,iBAAiB,EAAE,CAAC;MAAE7B,MAAM,EAAE,CAAC;MAAE+B,aAAa,EAAE;IAC7E,CAAC;IACD,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACAC,kBAAkBA,CAAChD,KAAK,EAAE;IACtB;IACA,IAAI,CAAC+B,mBAAmB,CAACkB,IAAI,CAACjD,KAAK,CAAC;EACxC;EACA;AACJ;AACA;EACIkD,kBAAkBA,CAACxC,KAAK,EAAE;IACtB,IAAI,CAACyC,kBAAkB,CAACzC,KAAK,CAAC;EAClC;EACA0C,mBAAmBA,CAAC1C,KAAK,EAAE;IACvB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACsB,wBAAwB,CAACiB,IAAI,CAACvC,KAAK,CAAC;EAC7C;EACA2C,oBAAoBA,CAACxC,MAAM,EAAE;IACzB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACoB,yBAAyB,CAACgB,IAAI,CAACpC,MAAM,CAAC;EAC/C;EACAsC,kBAAkBA,CAACnD,KAAK,EAAE;IACtB,IAAI,CAACuC,QAAQ,GAAGvC,KAAK;IACrB,IAAI,CAACkC,uBAAuB,CAACe,IAAI,CAACjD,KAAK,CAAC;EAC5C;EACAsD,eAAeA,CAAA,EAAG;IACd,IAAI,CAACjB,qBAAqB,CAACY,IAAI,CAAC,IAAI,CAAC;EACzC;EACAM,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACpB,SAAS,CAACc,IAAI,CAAC,IAAI,CAAC;EAC7B;EACAO,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACpB,YAAY,CAACa,IAAI,CAAC,IAAI,CAAC;EAChC;EACAQ,kBAAkBA,CAAC5C,MAAM,EAAE;IACvB,IAAI,CAACY,oBAAoB,CAACwB,IAAI,CAACpE,uBAAuB,CAACgC,MAAM,CAAC,CAAC;EACnE;EACA6C,2BAA2BA,CAACC,aAAa,GAAG,KAAK,EAAE;IAC/C,IAAI,CAACjC,sBAAsB,CAACuB,IAAI,CAAC;MAAEW,MAAM,EAAE,IAAI;MAAED;IAAc,CAAC,CAAC;EACrE;EACAE,eAAeA,CAACpD,YAAY,EAAE;IAC1B,IAAI,CAAC6B,qBAAqB,CAACW,IAAI,CAACxC,YAAY,CAAC;IAC7C,IAAI,CAACiD,2BAA2B,CAAC,IAAI,CAAC;EAC1C;EACAI,8BAA8BA,CAACpD,KAAK,EAAE;IAClC,IAAI,CAAC8B,0BAA0B,CAAC9B,KAAK,GAAGA,KAAK,CAACJ,MAAM;IACpD,IAAI,CAACyD,gCAAgC,CAAC,CAAC;EAC3C;EACAC,+BAA+BA,CAACnD,MAAM,EAAE;IACpC,IAAI,CAAC2B,0BAA0B,CAACC,UAAU,GAAG5B,MAAM,CAACP,MAAM,KAAK,CAAC,GAAGO,MAAM,CAAC,CAAC,CAAC,CAACb,KAAK,CAACM,MAAM,GAAG,CAAC;IAC7F,IAAI,CAACkC,0BAA0B,CAAC3B,MAAM,GAAGA,MAAM,CAACP,MAAM;IACtD,IAAI,CAACyD,gCAAgC,CAAC,CAAC;EAC3C;EACAE,qCAAqCA,CAAC9D,IAAI,EAAE;IACxC,IAAI,CAACqC,0BAA0B,CAACE,iBAAiB,GAAGvC,IAAI,KAAK,IAAI,GAAGA,IAAI,CAACM,YAAY,CAACC,KAAK,CAACJ,MAAM,GAAG,CAAC;IACtG,IAAI,CAACyD,gCAAgC,CAAC,CAAC;EAC3C;EACAG,6BAA6BA,CAACxD,KAAK,EAAE;IACjC,IAAI,CAACiC,yBAAyB,CAACjC,KAAK,GAAGA,KAAK,CAACJ,MAAM;IACnD,IAAI,CAAC6D,+BAA+B,CAAC,CAAC;EAC1C;EACAC,8BAA8BA,CAACvD,MAAM,EAAE;IACnC,IAAI,CAAC8B,yBAAyB,CAACF,UAAU,GAAG5B,MAAM,CAACF,MAAM,CAAC,CAACT,KAAK,EAAEmE,KAAK,KAAMnE,KAAK,GAAGmE,KAAK,CAACrE,KAAK,CAACM,MAAO,EAAE,CAAC,CAAC;IAC5G,IAAI,CAACqC,yBAAyB,CAAC9B,MAAM,GAAGA,MAAM,CAACP,MAAM;IACrD,IAAI,CAAC6D,+BAA+B,CAAC,CAAC;EAC1C;EACAG,qCAAqCA,CAAC1B,aAAa,EAAE;IACjD,IAAI,CAACD,yBAAyB,CAACD,iBAAiB,GAC5CE,aAAa,CAACjC,MAAM,CAAC,CAACT,KAAK,EAAEO,YAAY,KAAMP,KAAK,GAAGO,YAAY,CAACA,YAAY,CAACC,KAAK,CAACJ,MAAO,EAAE,CAAC,CAAC;IACtG,IAAI,CAACqC,yBAAyB,CAACC,aAAa,GAAGA,aAAa,CAACtC,MAAM;IACnE,IAAI,CAAC6D,+BAA+B,CAAC,CAAC;EAC1C;EACAI,WAAWA,CAACvE,KAAK,EAAEwE,MAAM,EAAE;IACvB,OAAOxE,KAAK,CAACyE,MAAM,CAAEtE,IAAI,IAAK;MAC1B,IAAI;QAAEuE,KAAK;QAAEC,IAAI;QAAE9D;MAAO,CAAC,GAAGV,IAAI,CAACyE,QAAQ;MAC3C,IAAIC,MAAM,GAAGL,MAAM,CAACM,WAAW,CAAC,CAAC;MACjC,OAAOJ,KAAK,CAACI,WAAW,CAAC,CAAC,CAACC,OAAO,CAACF,MAAM,CAAC,GAAG,CAAC,CAAC,IACxCF,IAAI,CAACG,WAAW,CAAC,CAAC,CAACC,OAAO,CAACF,MAAM,CAAC,GAAG,CAAC,CAAC,IACvChE,MAAM,CAAC9B,GAAG,CAACsF,KAAK,IAAIA,KAAK,CAACK,KAAK,CAACI,WAAW,CAAC,CAAC,CAAC,CAC5CL,MAAM,CAACO,IAAI,IAAIA,IAAI,CAACD,OAAO,CAACF,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAACvE,MAAM,GAAG,CAAC;IACjE,CAAC,CAAC;EACN;EACA2E,YAAYA,CAACjF,KAAK,EAAEwE,MAAM,EAAE;IACxB,OAAOxE,KAAK,CAACyE,MAAM,CAAEtE,IAAI,IAAKA,IAAI,CAACkE,KAAK,CAACK,KAAK,CAACI,WAAW,CAAC,CAAC,CAACC,OAAO,CAACP,MAAM,CAACM,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACpG;EACAhC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACoC,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACC,0BAA0B,CAAC,CAAC;EACrC;EACAD,yBAAyBA,CAAA,EAAG;IACxB,MAAME,MAAM,GAAG,IAAI,CAACpD,wBAAwB,CAACqD,IAAI,CAACnG,GAAG,CAACwB,KAAK,IAAI,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAC,EAAE3B,GAAG,CAAE2B,KAAK,KAAM;MAAEA,KAAK;MAAE6B,QAAQ,EAAE,IAAI,CAACA;IAAS,CAAC,CAAC,CAAC,CAAC;IACzI,MAAM+C,SAAS,GAAG,IAAI,CAACpD,uBAAuB,CAACmD,IAAI,CAACnG,GAAG,CAACqD,QAAQ,IAAI,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC,EAAExD,GAAG,CAAEwD,QAAQ,KAAM;MAAEA,QAAQ;MAAE7B,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC,CAAC,CAAC;IACpJ,MAAM6E,uBAAuB,GAAG/G,KAAK,CAAC4G,MAAM,EAAEE,SAAS,CAAC,CACnDD,IAAI,CAACpG,SAAS,CAAC;MAAEyB,KAAK,EAAE,IAAI,CAACA,KAAK;MAAE6B,QAAQ,EAAE,IAAI,CAACA;IAAS,CAAC,CAAC,EAAEvD,QAAQ,CAAC,CAAC;MAAE0B,KAAK;MAAE6B;IAAS,CAAC,KAAK;MACnG,MAAMiD,QAAQ,GAAG9E,KAAK,CAAC+D,MAAM,CAACtE,IAAI,IAAIoC,QAAQ,CAACxD,GAAG,CAAC0G,IAAI,IAAIA,IAAI,CAAC7F,EAAE,CAAC,CAACmF,OAAO,CAAC5E,IAAI,CAACP,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;MAC5F,IAAI,CAACwB,WAAW,CAAC6B,IAAI,CAACuC,QAAQ,CAAClF,MAAM,CAAC;MACtC,OAAO7B,EAAE,CAAC+G,QAAQ,CAAC;IACvB,CAAC,CAAC,CAAC;IACHD,uBAAuB,CAACG,SAAS,CAAChF,KAAK,IAAI;MACvC,IAAI,CAACO,eAAe,CAACgC,IAAI,CAACvC,KAAK,CAAC;IACpC,CAAC,CAAC;EACN;EACAyE,0BAA0BA,CAAA,EAAG;IACzB,MAAMQ,OAAO,GAAG,IAAI,CAAC1D,yBAAyB,CAACoD,IAAI,CAACnG,GAAG,CAAC2B,MAAM,IAAI;MAC9D,IAAI,CAACA,MAAM,GAAGA,MAAM;IACxB,CAAC,CAAC,EAAE9B,GAAG,CAAC8B,MAAM,KAAK;MAAEA,MAAM;MAAE0B,QAAQ,EAAE,IAAI,CAACA;IAAS,CAAC,CAAC,CAAC,CAAC;IACzD,MAAM+C,SAAS,GAAG,IAAI,CAACpD,uBAAuB,CAACmD,IAAI,CAACnG,GAAG,CAACqD,QAAQ,IAAI;MAChE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC5B,CAAC,CAAC,EAAExD,GAAG,CAACwD,QAAQ,KAAK;MAAEA,QAAQ;MAAE1B,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,CAAC,CAAC,CAAC;IACzD,MAAM+E,wBAAwB,GAAGpH,KAAK,CAACmH,OAAO,EAAEL,SAAS,CAAC,CACrDD,IAAI,CAACpG,SAAS,CAAC;MAAE4B,MAAM,EAAE,IAAI,CAACA,MAAM;MAAE0B,QAAQ,EAAE,IAAI,CAACA;IAAS,CAAC,CAAC,EAAExD,GAAG,CAAC,CAAC;MAAE8B,MAAM;MAAE0B;IAAS,CAAC,KAAK;MACjG;MACA,OAAO1B,MAAM,CAAC4D,MAAM,CAAC,CAAC;QAAE7E;MAAG,CAAC,KAAK2C,QAAQ,CAACsD,IAAI,CAAC,CAAC;QAAEjG,EAAE,EAAEkG;MAAW,CAAC,KAAKlG,EAAE,KAAKkG,UAAU,CAAC,KAAK,IAAI,CAAC;IACvG,CAAC,CAAC,EAAE5G,GAAG,CAAC2B,MAAM,IAAI;MACd,IAAI,CAACQ,YAAY,CAAC4B,IAAI,CAACpC,MAAM,CAACP,MAAM,CAAC;IACzC,CAAC,CAAC,CAAC;IACH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAsF,wBAAwB,CAACF,SAAS,CAAE7E,MAAM,IAAK;MAC3C,IAAI,CAACK,gBAAgB,CAAC+B,IAAI,CAACpC,MAAM,CAAC;IACtC,CAAC,CAAC;EACN;EACAkC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACgD,yBAAyB,CAAC,CAAC;IAChC,MAAMC,MAAM,GAAG,IAAI,CAAC7D,SAAS,CAACkD,IAAI,CAACtG,GAAG,CAAEkH,KAAK,IAAK;MAC9C,MAAMC,SAAS,GAAG,CAAC,GAAG,IAAI,CAACxF,KAAK,EAAE,GAAG,IAAI,CAACG,MAAM,CAAC;MACjD,IAAI0B,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5B,IAAI0D,KAAK,EAAE;QACP,IAAIE,UAAU,GAAG,CACb,GAAGD,SAAS,CACPzB,MAAM,CAACtE,IAAI,IAAI,IAAI,CAACoC,QAAQ,CAACwC,OAAO,CAAC5E,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAClDsE,MAAM,CAACtE,IAAI,IAAI;UAChB,IAAIY,OAAO,GAAGZ,IAAI,CAACY,OAAO;UAC1BZ,IAAI,CAACY,OAAO,GAAG,KAAK;UACpB,OAAOA,OAAO;QAClB,CAAC,CAAC,CACL;QACD,MAAMqF,eAAe,GAAGD,UAAU,CAACE,IAAI,CAAClG,IAAI,IAAIA,IAAI,CAACE,OAAO,CAAC;QAC7D,IAAI+F,eAAe,EAAE;UACjB,IAAI,CAAC1C,2BAA2B,CAAC,IAAI,CAAC;QAC1C;QACA,IAAI,CAACnC,WAAW,CAAC0B,IAAI,CAACkD,UAAU,CAAC;QACjC5D,QAAQ,GAAG,CAAC,GAAGA,QAAQ,EAAE,GAAG4D,UAAU,CAAC;MAC3C;MACA,OAAO;QAAE5D;MAAS,CAAC;IACvB,CAAC,CAAC,CAAC;IACH,MAAM+D,QAAQ,GAAG,IAAI,CAAClE,YAAY,CAACiD,IAAI,CAACtG,GAAG,CAAEwH,OAAO,IAAK;MACrD,IAAIhE,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5B,IAAIgE,OAAO,EAAE;QACT,IAAIC,YAAY,GAAGjE,QAAQ,CAACkC,MAAM,CAACtE,IAAI,IAAIA,IAAI,CAACY,OAAO,CAAC;QACxDwB,QAAQ,GAAGA,QAAQ,CAACkC,MAAM,CAACtE,IAAI,IAAI;UAC/B,IAAIsG,iBAAiB,GAAG,CAACtG,IAAI,CAACY,OAAO;UACrC,IAAI,CAAC0F,iBAAiB,EAAE;YACpBtG,IAAI,CAACuG,WAAW,CAAC,CAAC;UACtB;UACA,OAAOD,iBAAiB;QAC5B,CAAC,CAAC;QACF,IAAI,CAACjF,aAAa,CAACyB,IAAI,CAACuD,YAAY,CAAC;MACzC;MACA,OAAO;QAAEjE;MAAS,CAAC;IACvB,CAAC,CAAC,CAAC;IACH/D,KAAK,CAACwH,MAAM,EAAEM,QAAQ,CAAC,CAACZ,SAAS,CAAC,CAAC;MAAEnD;IAAS,CAAC,KAAK,IAAI,CAACY,kBAAkB,CAACZ,QAAQ,CAAC,CAAC;IACtF,MAAMoE,UAAU,GAAG,IAAI,CAACtE,qBAAqB,CACxCgD,IAAI,CAACtG,GAAG,CAAC,MAAM;MAChB,MAAMwD,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,OAAO;QAAEA;MAAS,CAAC;IACvB,CAAC,CAAC,CAAC;IACHoE,UAAU,CAACjB,SAAS,CAAC,CAAC;MAAEnD;IAAS,CAAC,KAAK,IAAI,CAACY,kBAAkB,CAACZ,QAAQ,CAAC,CAAC;IACzE,MAAMqE,kBAAkB,GAAG,IAAI,CAACtE,qBAAqB,CAAC+C,IAAI,CAACtG,GAAG,CAAEoB,IAAI,IAAK;MACrE,MAAMoC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9BA,QAAQ,CAACsE,IAAI,CAAC1G,IAAI,CAAC;MACnB,OAAO;QAAEoC;MAAS,CAAC;IACvB,CAAC,CAAC,CAAC;IACHqE,kBAAkB,CAAClB,SAAS,CAAC,CAAC;MAAEnD;IAAS,CAAC,KAAK,IAAI,CAACY,kBAAkB,CAACZ,QAAQ,CAAC,CAAC;IACjF,IAAI,CAACL,uBAAuB,CAACwD,SAAS,CAAE1F,KAAK,IAAK;MAC9C,IAAI,CAACmB,cAAc,CAAC8B,IAAI,CAACjD,KAAK,CAAC;MAC/B,IAAI,CAACsB,cAAc,CAAC2B,IAAI,CAAC7E,2BAA2B,CAAC2B,mBAAmB,CAACC,KAAK,CAAC,CAAC;IACpF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI6C,cAAcA,CAAA,EAAG;IACbjE,GAAG,CAAC,IAAI,CAACoD,wBAAwB,EAAE,IAAI,CAACC,yBAAyB,CAAC,CAACyD,SAAS,CAAC,CAAC,CAAChF,KAAK,EAAEG,MAAM,CAAC,KAAK;MAC9F,IAAIiG,UAAU,GAAGjG,MAAM,CAClB9B,GAAG,CAACsF,KAAK,IAAI;QACdA,KAAK,CAACrE,KAAK,GAAG,EAAE;QAChB,OAAOqE,KAAK;MAChB,CAAC,CAAC,CACG1D,MAAM,CAACnB,0BAA0B,EAAE,CAAC,CAAC,CAAC;MAC3CkB,KAAK,CAACqG,OAAO,CAAEtB,IAAI,IAAK;QACpB,IAAIA,IAAI,EAAE;UACN,CAACA,IAAI,CAACuB,aAAa,CAAC,CAAC,IAAI,EAAE,EAAED,OAAO,CAAE1C,KAAK,IAAK;YAC5C,IAAIA,KAAK,IAAIA,KAAK,CAACzE,EAAE,IAAIyE,KAAK,CAACzE,EAAE,IAAIkH,UAAU,EAAE;cAC7CA,UAAU,CAACzC,KAAK,CAACzE,EAAE,CAAC,CAACqH,cAAc,CAACxB,IAAI,CAAC;YAC7C;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAM,yBAAyBA,CAAA,EAAG;IACxBxH,aAAa,CAAC,CACV,IAAI,CAACwD,mBAAmB,EACxB,IAAI,CAACC,wBAAwB,EAC7B,IAAI,CAACC,yBAAyB,CACjC,CAAC,CAACyD,SAAS,CAAC,CAAC,CAAC1F,KAAK,EAAEU,KAAK,EAAEG,MAAM,CAAC,KAAK;MACrC,IAAI0B,QAAQ,GAAG,IAAI,CAAC2E,qBAAqB,CAACxG,KAAK,EAAEG,MAAM,EAAEb,KAAK,CAAC;MAC/D5B,2BAA2B,CAACwC,yBAAyB,CAACF,KAAK,EAAEG,MAAM,CAAC;MACpE,IAAI,CAACsC,kBAAkB,CAACZ,QAAQ,CAAC;IACrC,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI2E,qBAAqBA,CAACxG,KAAK,EAAEG,MAAM,EAAEb,KAAK,EAAE;IACxC,IAAImH,SAAS,GAAGzG,KAAK,CAACC,MAAM,CAACnB,0BAA0B,EAAE,CAAC,CAAC,CAAC;IAC5D,IAAIsH,UAAU,GAAGjG,MAAM,CAACF,MAAM,CAACnB,0BAA0B,EAAE,CAAC,CAAC,CAAC;IAC9D,MAAM4H,oBAAoB,GAAIjH,IAAI,IAAK;MACnC,IAAIkH,MAAM;MACV,QAAQlH,IAAI,CAACI,IAAI;QACb,KAAKnB,qBAAqB,CAACkI,IAAI;UAC3B,IAAInH,IAAI,CAACP,EAAE,IAAIO,IAAI,CAACP,EAAE,IAAIuH,SAAS,EAAE;YACjCE,MAAM,GAAGF,SAAS,CAAChH,IAAI,CAACP,EAAE,CAAC;UAC/B,CAAC,MACI;YACDyH,MAAM,GAAG,IAAIvI,cAAc,CAAC;cACxBc,EAAE,EAAEO,IAAI,CAACP,EAAE;cACXW,IAAI,EAAEpB,mBAAmB,CAACoI,UAAU;cACpCC,IAAI,EAAErH;YACV,CAAC,CAAC;UACN;UACA;QACJ,KAAKf,qBAAqB,CAACqI,KAAK;QAChC,KAAKrI,qBAAqB,CAACsI,QAAQ;UAC/B,IAAIvH,IAAI,CAACP,EAAE,IAAIO,IAAI,CAACP,EAAE,IAAIkH,UAAU,EAAE;YAClCO,MAAM,GAAGP,UAAU,CAAC3G,IAAI,CAACP,EAAE,CAAC;UAChC,CAAC,MACI;YACDyH,MAAM,GAAG,IAAIvI,cAAc,CAAC;cACxBc,EAAE,EAAEO,IAAI,CAACP,EAAE;cACXW,IAAI,EAAEpB,mBAAmB,CAACoI,UAAU;cACpCC,IAAI,EAAErH;YACV,CAAC,CAAC;UACN;UACA;QACJ,KAAKf,qBAAqB,CAACoB,YAAY;UACnC,IAAImH,iBAAiB,GAAG,CAACxH,IAAI,CAACH,KAAK,IAAI,EAAE,EAAEjB,GAAG,CAAC6I,gBAAgB,IAAIR,oBAAoB,CAACQ,gBAAgB,CAAC,CAAC;UAC1GP,MAAM,GAAGxI,uBAAuB,CAAC8I,iBAAiB,CAAC;UACnDN,MAAM,CAACzH,EAAE,GAAGO,IAAI,CAACP,EAAE;UACnB;QACJ;UACI,MAAM,IAAIiI,KAAK,CAAC,8BAA8B,CAAC;MACvD;MACA,OAAOR,MAAM;IACjB,CAAC;IACD,OAAOrH,KAAK,CAACjB,GAAG,CAACqI,oBAAoB,CAAC;EAC1C;EACAjD,+BAA+BA,CAAA,EAAG;IAC9B,MAAM;MAAEzD,KAAK;MAAE+B,UAAU;MAAEC,iBAAiB;MAAE7B,MAAM;MAAE+B;IAAc,CAAC,GAAG,IAAI,CAACD,yBAAyB;IACtG,MAAMmF,UAAU,GAAGpH,KAAK,GAAG+B,UAAU,GAAGC,iBAAiB;IACzD,IAAI,CAACb,0BAA0B,CAACoB,IAAI,CAAC6E,UAAU,CAAC;IAChD,IAAI,CAAChG,qBAAqB,CAACmB,IAAI,CAAC6E,UAAU,GAAGjH,MAAM,GAAG+B,aAAa,CAAC;EACxE;EACAmB,gCAAgCA,CAAA,EAAG;IAC/B,MAAM;MAAErD,KAAK;MAAE+B,UAAU;MAAEC,iBAAiB;MAAE7B;IAAO,CAAC,GAAG,IAAI,CAAC2B,0BAA0B;IACxF,MAAMsF,UAAU,GAAGpH,KAAK,GAAG+B,UAAU,GAAGC,iBAAiB;IACzD,IAAI,CAACf,2BAA2B,CAACsB,IAAI,CAAC6E,UAAU,CAAC;IACjD,IAAI,CAAClG,sBAAsB,CAACqB,IAAI,CAAC6E,UAAU,GAAGjH,MAAM,CAAC;EACzD;AAEJ,CAAC,EA9VYzC,2BAA2B,GAAA0B,0BAAO,EA6VlCA,0BAAA,CAAKiI,cAAc,GAAG,MAAM,EAAE,EAAAjI,0BAAA,CAC1C;AACDD,yBAAyB,GAAGzB,2BAA2B,GAAGC,UAAU,CAAC,CACjEC,UAAU,CAAC,CAAC,CACf,EAAEuB,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}