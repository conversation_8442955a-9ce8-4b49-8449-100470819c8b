{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { consumerMarkDirty, SIGNAL, consumerDestroy, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation, REACTIVE_NODE } from '../untracked-BKcld_ew.mjs';\nexport { SIGNAL_NODE, createComputed, createLinkedSignal, createSignal, defaultEquals, getActiveConsumer, isReactive, linkedSignalSetFn, linkedSignalUpdateFn, producerAccessed, producerIncrementEpoch, producerMarkClean, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, runPostSignalSetFn, setActiveConsumer, setAlternateWeakRefImpl, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalSetFn, signalUpdateFn, untracked } from '../untracked-BKcld_ew.mjs';\nfunction createWatch(fn, schedule, allowSignalWrites) {\n  const node = Object.create(WATCH_NODE);\n  if (allowSignalWrites) {\n    node.consumerAllowSignalWrites = true;\n  }\n  node.fn = fn;\n  node.schedule = schedule;\n  const registerOnCleanup = cleanupFn => {\n    node.cleanupFn = cleanupFn;\n  };\n  function isWatchNodeDestroyed(node) {\n    return node.fn === null && node.schedule === null;\n  }\n  function destroyWatchNode(node) {\n    if (!isWatchNodeDestroyed(node)) {\n      consumerDestroy(node); // disconnect watcher from the reactive graph\n      node.cleanupFn();\n      // nullify references to the integration functions to mark node as destroyed\n      node.fn = null;\n      node.schedule = null;\n      node.cleanupFn = NOOP_CLEANUP_FN;\n    }\n  }\n  const run = () => {\n    if (node.fn === null) {\n      // trying to run a destroyed watch is noop\n      return;\n    }\n    if (isInNotificationPhase()) {\n      throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n    }\n    node.dirty = false;\n    if (node.hasRun && !consumerPollProducersForChange(node)) {\n      return;\n    }\n    node.hasRun = true;\n    const prevConsumer = consumerBeforeComputation(node);\n    try {\n      node.cleanupFn();\n      node.cleanupFn = NOOP_CLEANUP_FN;\n      node.fn(registerOnCleanup);\n    } finally {\n      consumerAfterComputation(node, prevConsumer);\n    }\n  };\n  node.ref = {\n    notify: () => consumerMarkDirty(node),\n    run,\n    cleanup: () => node.cleanupFn(),\n    destroy: () => destroyWatchNode(node),\n    [SIGNAL]: node\n  };\n  return node.ref;\n}\nconst NOOP_CLEANUP_FN = () => {};\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst WATCH_NODE = /* @__PURE__ */(() => {\n  return _objectSpread(_objectSpread({}, REACTIVE_NODE), {}, {\n    consumerIsAlwaysLive: true,\n    consumerAllowSignalWrites: false,\n    consumerMarkedDirty: node => {\n      if (node.schedule !== null) {\n        node.schedule(node.ref);\n      }\n    },\n    hasRun: false,\n    cleanupFn: NOOP_CLEANUP_FN\n  });\n})();\nexport { REACTIVE_NODE, SIGNAL, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, createWatch, isInNotificationPhase };\n//# sourceMappingURL=signals.mjs.map", "map": {"version": 3, "names": ["consumerMarkDirty", "SIGNAL", "consumerDestroy", "isInNotificationPhase", "consumerPollProducersForChange", "consumerBeforeComputation", "consumerAfterComputation", "REACTIVE_NODE", "SIGNAL_NODE", "createComputed", "createLinkedSignal", "createSignal", "defaultEquals", "getActiveConsumer", "isReactive", "linkedSignalSetFn", "linkedSignalUpdateFn", "producerAccessed", "producerIncrementEpoch", "producerMark<PERSON><PERSON>", "producerNotifyConsumers", "producerUpdateValueVersion", "producer<PERSON><PERSON>datesAllowed", "runPostSignalSetFn", "setActiveConsumer", "setAlternateWeakRefImpl", "setPostSignalSetFn", "setThrowInvalidWriteToSignalError", "signalSetFn", "signalUpdateFn", "untracked", "createWatch", "fn", "schedule", "allowSignalWrites", "node", "Object", "create", "WATCH_NODE", "consumerAllowSignalWrites", "registerOnCleanup", "cleanupFn", "isWatchNodeDestroyed", "destroyWatchNode", "NOOP_CLEANUP_FN", "run", "Error", "dirty", "<PERSON><PERSON>un", "prevConsumer", "ref", "notify", "cleanup", "destroy", "_objectSpread", "consumerIsAlwaysLive", "consumerMarkedDirty"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/core/fesm2022/primitives/signals.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { consumerMarkDirty, SIGNAL, consumerDestroy, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation, REACTIVE_NODE } from '../untracked-BKcld_ew.mjs';\nexport { SIGNAL_NODE, createComputed, createLinkedSignal, createSignal, defaultEquals, getActiveConsumer, isReactive, linkedSignalSetFn, linkedSignalUpdateFn, producerAccessed, producerIncrementEpoch, producerMarkClean, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, runPostSignalSetFn, setActiveConsumer, setAlternateWeakRefImpl, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalSetFn, signalUpdateFn, untracked } from '../untracked-BKcld_ew.mjs';\n\nfunction createWatch(fn, schedule, allowSignalWrites) {\n    const node = Object.create(WATCH_NODE);\n    if (allowSignalWrites) {\n        node.consumerAllowSignalWrites = true;\n    }\n    node.fn = fn;\n    node.schedule = schedule;\n    const registerOnCleanup = (cleanupFn) => {\n        node.cleanupFn = cleanupFn;\n    };\n    function isWatchNodeDestroyed(node) {\n        return node.fn === null && node.schedule === null;\n    }\n    function destroyWatchNode(node) {\n        if (!isWatchNodeDestroyed(node)) {\n            consumerDestroy(node); // disconnect watcher from the reactive graph\n            node.cleanupFn();\n            // nullify references to the integration functions to mark node as destroyed\n            node.fn = null;\n            node.schedule = null;\n            node.cleanupFn = NOOP_CLEANUP_FN;\n        }\n    }\n    const run = () => {\n        if (node.fn === null) {\n            // trying to run a destroyed watch is noop\n            return;\n        }\n        if (isInNotificationPhase()) {\n            throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n        }\n        node.dirty = false;\n        if (node.hasRun && !consumerPollProducersForChange(node)) {\n            return;\n        }\n        node.hasRun = true;\n        const prevConsumer = consumerBeforeComputation(node);\n        try {\n            node.cleanupFn();\n            node.cleanupFn = NOOP_CLEANUP_FN;\n            node.fn(registerOnCleanup);\n        }\n        finally {\n            consumerAfterComputation(node, prevConsumer);\n        }\n    };\n    node.ref = {\n        notify: () => consumerMarkDirty(node),\n        run,\n        cleanup: () => node.cleanupFn(),\n        destroy: () => destroyWatchNode(node),\n        [SIGNAL]: node,\n    };\n    return node.ref;\n}\nconst NOOP_CLEANUP_FN = () => { };\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst WATCH_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        consumerIsAlwaysLive: true,\n        consumerAllowSignalWrites: false,\n        consumerMarkedDirty: (node) => {\n            if (node.schedule !== null) {\n                node.schedule(node.ref);\n            }\n        },\n        hasRun: false,\n        cleanupFn: NOOP_CLEANUP_FN,\n    };\n})();\n\nexport { REACTIVE_NODE, SIGNAL, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, createWatch, isInNotificationPhase };\n//# sourceMappingURL=signals.mjs.map\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,iBAAiB,EAAEC,MAAM,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,8BAA8B,EAAEC,yBAAyB,EAAEC,wBAAwB,EAAEC,aAAa,QAAQ,2BAA2B;AACjN,SAASC,WAAW,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,0BAA0B,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,iCAAiC,EAAEC,WAAW,EAAEC,cAAc,EAAEC,SAAS,QAAQ,2BAA2B;AAEze,SAASC,WAAWA,CAACC,EAAE,EAAEC,QAAQ,EAAEC,iBAAiB,EAAE;EAClD,MAAMC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAACC,UAAU,CAAC;EACtC,IAAIJ,iBAAiB,EAAE;IACnBC,IAAI,CAACI,yBAAyB,GAAG,IAAI;EACzC;EACAJ,IAAI,CAACH,EAAE,GAAGA,EAAE;EACZG,IAAI,CAACF,QAAQ,GAAGA,QAAQ;EACxB,MAAMO,iBAAiB,GAAIC,SAAS,IAAK;IACrCN,IAAI,CAACM,SAAS,GAAGA,SAAS;EAC9B,CAAC;EACD,SAASC,oBAAoBA,CAACP,IAAI,EAAE;IAChC,OAAOA,IAAI,CAACH,EAAE,KAAK,IAAI,IAAIG,IAAI,CAACF,QAAQ,KAAK,IAAI;EACrD;EACA,SAASU,gBAAgBA,CAACR,IAAI,EAAE;IAC5B,IAAI,CAACO,oBAAoB,CAACP,IAAI,CAAC,EAAE;MAC7BjC,eAAe,CAACiC,IAAI,CAAC,CAAC,CAAC;MACvBA,IAAI,CAACM,SAAS,CAAC,CAAC;MAChB;MACAN,IAAI,CAACH,EAAE,GAAG,IAAI;MACdG,IAAI,CAACF,QAAQ,GAAG,IAAI;MACpBE,IAAI,CAACM,SAAS,GAAGG,eAAe;IACpC;EACJ;EACA,MAAMC,GAAG,GAAGA,CAAA,KAAM;IACd,IAAIV,IAAI,CAACH,EAAE,KAAK,IAAI,EAAE;MAClB;MACA;IACJ;IACA,IAAI7B,qBAAqB,CAAC,CAAC,EAAE;MACzB,MAAM,IAAI2C,KAAK,CAAC,mEAAmE,CAAC;IACxF;IACAX,IAAI,CAACY,KAAK,GAAG,KAAK;IAClB,IAAIZ,IAAI,CAACa,MAAM,IAAI,CAAC5C,8BAA8B,CAAC+B,IAAI,CAAC,EAAE;MACtD;IACJ;IACAA,IAAI,CAACa,MAAM,GAAG,IAAI;IAClB,MAAMC,YAAY,GAAG5C,yBAAyB,CAAC8B,IAAI,CAAC;IACpD,IAAI;MACAA,IAAI,CAACM,SAAS,CAAC,CAAC;MAChBN,IAAI,CAACM,SAAS,GAAGG,eAAe;MAChCT,IAAI,CAACH,EAAE,CAACQ,iBAAiB,CAAC;IAC9B,CAAC,SACO;MACJlC,wBAAwB,CAAC6B,IAAI,EAAEc,YAAY,CAAC;IAChD;EACJ,CAAC;EACDd,IAAI,CAACe,GAAG,GAAG;IACPC,MAAM,EAAEA,CAAA,KAAMnD,iBAAiB,CAACmC,IAAI,CAAC;IACrCU,GAAG;IACHO,OAAO,EAAEA,CAAA,KAAMjB,IAAI,CAACM,SAAS,CAAC,CAAC;IAC/BY,OAAO,EAAEA,CAAA,KAAMV,gBAAgB,CAACR,IAAI,CAAC;IACrC,CAAClC,MAAM,GAAGkC;EACd,CAAC;EACD,OAAOA,IAAI,CAACe,GAAG;AACnB;AACA,MAAMN,eAAe,GAAGA,CAAA,KAAM,CAAE,CAAC;AACjC;AACA;AACA;AACA,MAAMN,UAAU,GAAG,eAAgB,CAAC,MAAM;EACtC,OAAAgB,aAAA,CAAAA,aAAA,KACO/C,aAAa;IAChBgD,oBAAoB,EAAE,IAAI;IAC1BhB,yBAAyB,EAAE,KAAK;IAChCiB,mBAAmB,EAAGrB,IAAI,IAAK;MAC3B,IAAIA,IAAI,CAACF,QAAQ,KAAK,IAAI,EAAE;QACxBE,IAAI,CAACF,QAAQ,CAACE,IAAI,CAACe,GAAG,CAAC;MAC3B;IACJ,CAAC;IACDF,MAAM,EAAE,KAAK;IACbP,SAAS,EAAEG;EAAe;AAElC,CAAC,EAAE,CAAC;AAEJ,SAASrC,aAAa,EAAEN,MAAM,EAAEK,wBAAwB,EAAED,yBAAyB,EAAEH,eAAe,EAAEF,iBAAiB,EAAEI,8BAA8B,EAAE2B,WAAW,EAAE5B,qBAAqB;AAC3L", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}