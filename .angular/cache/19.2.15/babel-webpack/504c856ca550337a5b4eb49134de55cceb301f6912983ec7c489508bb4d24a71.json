{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatAutocomplete, _MatAutocompleteOrigin, _MatAutocompleteTrigger, _MatAutocompleteModule;\nconst _c0 = [\"panel\"];\nconst _c1 = [\"*\"];\nfunction _MatAutocomplete_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1, 0);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formFieldId_r1 = ctx.id;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1._classList);\n    i0.ɵɵclassProp(\"mat-mdc-autocomplete-visible\", ctx_r1.showPanel)(\"mat-mdc-autocomplete-hidden\", !ctx_r1.showPanel)(\"mat-autocomplete-panel-animations-enabled\", !ctx_r1._animationsDisabled)(\"mat-primary\", ctx_r1._color === \"primary\")(\"mat-accent\", ctx_r1._color === \"accent\")(\"mat-warn\", ctx_r1._color === \"warn\");\n    i0.ɵɵproperty(\"id\", ctx_r1.id);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1._getPanelAriaLabelledby(formFieldId_r1));\n  }\n}\nimport { c as MAT_OPTION_PARENT_COMPONENT, M as MatOption, d as MAT_OPTGROUP, e as MatOptionSelectionChange, _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition } from './option-ChV6uQgD.mjs';\nexport { a as MatOptgroup } from './option-ChV6uQgD.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, ANIMATION_MODULE_TYPE, EventEmitter, booleanAttribute, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, ContentChildren, Input, Output, Directive, forwardRef, EnvironmentInjector, ViewContainerRef, NgZone, Renderer2, afterNextRender, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { _IdGenerator, ActiveDescendantKeyManager, removeAriaReferencedId, addAriaReferencedId } from '@angular/cdk/a11y';\nimport { Platform, _getFocusedElementPierceShadowDom, _getEventTarget } from '@angular/cdk/platform';\nimport { Subscription, Subject, merge, of, defer, Observable } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { hasModifierKey, ESCAPE, ENTER, TAB, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { filter, map, startWith, switchMap, tap, delay, take } from 'rxjs/operators';\nimport { h as MAT_FORM_FIELD } from './form-field-DqPi4knt.mjs';\nimport { M as MatOptionModule } from './index-DOxJc1m4.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-CJ7seqQH.mjs';\nimport './structural-styles-BQUT6wsL.mjs';\nimport '@angular/common';\nimport '@angular/cdk/observers/private';\nimport './index-SYVYjXwK.mjs';\nimport './pseudo-checkbox-module-CAX2sutq.mjs';\n\n/** Event object that is emitted when an autocomplete option is selected. */\nclass MatAutocompleteSelectedEvent {\n  constructor(/** Reference to the autocomplete panel that emitted the event. */\n  source, /** Option that was selected. */\n  option) {\n    _defineProperty(this, \"source\", void 0);\n    _defineProperty(this, \"option\", void 0);\n    this.source = source;\n    this.option = option;\n  }\n}\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken('mat-autocomplete-default-options', {\n  providedIn: 'root',\n  factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    autoActiveFirstOption: false,\n    autoSelectActiveOption: false,\n    hideSingleSelectionIndicator: false,\n    requireSelection: false,\n    hasBackdrop: false\n  };\n}\n/** Autocomplete component. */\nclass MatAutocomplete {\n  /** Whether the autocomplete panel is open. */\n  get isOpen() {\n    return this._isOpen && this.showPanel;\n  }\n  /** @docs-private Sets the theme color of the panel. */\n  _setColor(value) {\n    this._color = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** @docs-private theme color of the panel */\n\n  /**\n   * Takes classes set on the host mat-autocomplete element and applies them to the panel\n   * inside the overlay container to allow for easy styling.\n   */\n  set classList(value) {\n    this._classList = value;\n    this._elementRef.nativeElement.className = '';\n  }\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncParentProperties();\n  }\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties() {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n\n  constructor() {\n    var _this$_defaults$hideS;\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_defaults\", inject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS));\n    _defineProperty(this, \"_animationsDisabled\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }) === 'NoopAnimations');\n    _defineProperty(this, \"_activeOptionChanges\", Subscription.EMPTY);\n    /** Manages active item in option list based on key events. */\n    _defineProperty(this, \"_keyManager\", void 0);\n    /** Whether the autocomplete panel should be visible, depending on option length. */\n    _defineProperty(this, \"showPanel\", false);\n    _defineProperty(this, \"_isOpen\", false);\n    /** Latest trigger that opened the autocomplete. */\n    _defineProperty(this, \"_latestOpeningTrigger\", void 0);\n    _defineProperty(this, \"_color\", void 0);\n    // The @ViewChild query for TemplateRef here needs to be static because some code paths\n    // lead to the overlay being created before change detection has finished for this component.\n    // Notably, another component may trigger `focus` on the autocomplete-trigger.\n    /** @docs-private */\n    _defineProperty(this, \"template\", void 0);\n    /** Element for the panel containing the autocomplete options. */\n    _defineProperty(this, \"panel\", void 0);\n    /** Reference to all options within the autocomplete. */\n    _defineProperty(this, \"options\", void 0);\n    /** Reference to all option groups within the autocomplete. */\n    _defineProperty(this, \"optionGroups\", void 0);\n    /** Aria label of the autocomplete. */\n    _defineProperty(this, \"ariaLabel\", void 0);\n    /** Input that can be used to specify the `aria-labelledby` attribute. */\n    _defineProperty(this, \"ariaLabelledby\", void 0);\n    /** Function that maps an option's control value to its display value in the trigger. */\n    _defineProperty(this, \"displayWith\", null);\n    /**\n     * Whether the first option should be highlighted when the autocomplete panel is opened.\n     * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n     */\n    _defineProperty(this, \"autoActiveFirstOption\", void 0);\n    /** Whether the active option should be selected as the user is navigating. */\n    _defineProperty(this, \"autoSelectActiveOption\", void 0);\n    /**\n     * Whether the user is required to make a selection when they're interacting with the\n     * autocomplete. If the user moves away from the autocomplete without selecting an option from\n     * the list, the value will be reset. If the user opens the panel and closes it without\n     * interacting or selecting a value, the initial value will be kept.\n     */\n    _defineProperty(this, \"requireSelection\", void 0);\n    /**\n     * Specify the width of the autocomplete panel.  Can be any CSS sizing value, otherwise it will\n     * match the width of its host.\n     */\n    _defineProperty(this, \"panelWidth\", void 0);\n    /** Whether ripples are disabled within the autocomplete panel. */\n    _defineProperty(this, \"disableRipple\", void 0);\n    /** Event that is emitted whenever an option from the list is selected. */\n    _defineProperty(this, \"optionSelected\", new EventEmitter());\n    /** Event that is emitted when the autocomplete panel is opened. */\n    _defineProperty(this, \"opened\", new EventEmitter());\n    /** Event that is emitted when the autocomplete panel is closed. */\n    _defineProperty(this, \"closed\", new EventEmitter());\n    /** Emits whenever an option is activated. */\n    _defineProperty(this, \"optionActivated\", new EventEmitter());\n    _defineProperty(this, \"_classList\", void 0);\n    _defineProperty(this, \"_hideSingleSelectionIndicator\", void 0);\n    _defineProperty(this, \"id\", inject(_IdGenerator).getId('mat-autocomplete-'));\n    /**\n     * Tells any descendant `mat-optgroup` to use the inert a11y pattern.\n     * @docs-private\n     */\n    _defineProperty(this, \"inertGroups\", void 0);\n    const platform = inject(Platform);\n    // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n    // Safari using VoiceOver. We should occasionally check back to see whether the bug\n    // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n    // option altogether.\n    this.inertGroups = (platform === null || platform === void 0 ? void 0 : platform.SAFARI) || false;\n    this.autoActiveFirstOption = !!this._defaults.autoActiveFirstOption;\n    this.autoSelectActiveOption = !!this._defaults.autoSelectActiveOption;\n    this.requireSelection = !!this._defaults.requireSelection;\n    this._hideSingleSelectionIndicator = (_this$_defaults$hideS = this._defaults.hideSingleSelectionIndicator) !== null && _this$_defaults$hideS !== void 0 ? _this$_defaults$hideS : false;\n  }\n  ngAfterContentInit() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withWrap().skipPredicate(this._skipPredicate);\n    this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n      if (this.isOpen) {\n        this.optionActivated.emit({\n          source: this,\n          option: this.options.toArray()[index] || null\n        });\n      }\n    });\n    // Set the initial visibility state.\n    this._setVisibility();\n  }\n  ngOnDestroy() {\n    var _this$_keyManager;\n    (_this$_keyManager = this._keyManager) === null || _this$_keyManager === void 0 || _this$_keyManager.destroy();\n    this._activeOptionChanges.unsubscribe();\n  }\n  /**\n   * Sets the panel scrollTop. This allows us to manually scroll to display options\n   * above or below the fold, as they are not actually being focused when active.\n   */\n  _setScrollTop(scrollTop) {\n    if (this.panel) {\n      this.panel.nativeElement.scrollTop = scrollTop;\n    }\n  }\n  /** Returns the panel's scrollTop. */\n  _getScrollTop() {\n    return this.panel ? this.panel.nativeElement.scrollTop : 0;\n  }\n  /** Panel should hide itself when the option list is empty. */\n  _setVisibility() {\n    var _this$options;\n    this.showPanel = !!((_this$options = this.options) !== null && _this$options !== void 0 && _this$options.length);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits the `select` event. */\n  _emitSelectEvent(option) {\n    const event = new MatAutocompleteSelectedEvent(this, option);\n    this.optionSelected.emit(event);\n  }\n  /** Gets the aria-labelledby for the autocomplete panel. */\n  _getPanelAriaLabelledby(labelId) {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n  // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n  // recommendation.\n  //\n  // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n  // makes a few exceptions for compound widgets.\n  //\n  // From [Developing a Keyboard Interface](\n  // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n  //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n  //   Listbox...\"\n  //\n  // The user can focus disabled options using the keyboard, but the user cannot click disabled\n  // options.\n  _skipPredicate() {\n    return false;\n  }\n}\n_MatAutocomplete = MatAutocomplete;\n_defineProperty(MatAutocomplete, \"\\u0275fac\", function _MatAutocomplete_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatAutocomplete)();\n});\n_defineProperty(MatAutocomplete, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatAutocomplete,\n  selectors: [[\"mat-autocomplete\"]],\n  contentQueries: function _MatAutocomplete_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n    }\n  },\n  viewQuery: function _MatAutocomplete_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 7);\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-autocomplete\"],\n  inputs: {\n    ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n    displayWith: \"displayWith\",\n    autoActiveFirstOption: [2, \"autoActiveFirstOption\", \"autoActiveFirstOption\", booleanAttribute],\n    autoSelectActiveOption: [2, \"autoSelectActiveOption\", \"autoSelectActiveOption\", booleanAttribute],\n    requireSelection: [2, \"requireSelection\", \"requireSelection\", booleanAttribute],\n    panelWidth: \"panelWidth\",\n    disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n    classList: [0, \"class\", \"classList\"],\n    hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute]\n  },\n  outputs: {\n    optionSelected: \"optionSelected\",\n    opened: \"opened\",\n    closed: \"closed\",\n    optionActivated: \"optionActivated\"\n  },\n  exportAs: [\"matAutocomplete\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_OPTION_PARENT_COMPONENT,\n    useExisting: _MatAutocomplete\n  }])],\n  ngContentSelectors: _c1,\n  decls: 1,\n  vars: 0,\n  consts: [[\"panel\", \"\"], [\"role\", \"listbox\", 1, \"mat-mdc-autocomplete-panel\", \"mdc-menu-surface\", \"mdc-menu-surface--open\", 3, \"id\"]],\n  template: function _MatAutocomplete_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, _MatAutocomplete_ng_template_0_Template, 3, 17, \"ng-template\");\n    }\n  },\n  styles: [\"div.mat-mdc-autocomplete-panel{width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;position:relative;border-radius:var(--mat-autocomplete-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-autocomplete-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-autocomplete-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-autocomplete-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden;pointer-events:none}@keyframes _mat-autocomplete-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}.mat-autocomplete-panel-animations-enabled{animation:_mat-autocomplete-enter 120ms cubic-bezier(0, 0, 0.2, 1)}mat-autocomplete{display:none}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocomplete, [{\n    type: Component,\n    args: [{\n      selector: 'mat-autocomplete',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      exportAs: 'matAutocomplete',\n      host: {\n        'class': 'mat-mdc-autocomplete'\n      },\n      providers: [{\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatAutocomplete\n      }],\n      template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div\\n    class=\\\"mat-mdc-autocomplete-panel mdc-menu-surface mdc-menu-surface--open\\\"\\n    role=\\\"listbox\\\"\\n    [id]=\\\"id\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-mdc-autocomplete-visible]=\\\"showPanel\\\"\\n    [class.mat-mdc-autocomplete-hidden]=\\\"!showPanel\\\"\\n    [class.mat-autocomplete-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [class.mat-primary]=\\\"_color === 'primary'\\\"\\n    [class.mat-accent]=\\\"_color === 'accent'\\\"\\n    [class.mat-warn]=\\\"_color === 'warn'\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n    #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"div.mat-mdc-autocomplete-panel{width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;position:relative;border-radius:var(--mat-autocomplete-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-autocomplete-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-autocomplete-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-autocomplete-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden;pointer-events:none}@keyframes _mat-autocomplete-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}.mat-autocomplete-panel-animations-enabled{animation:_mat-autocomplete-enter 120ms cubic-bezier(0, 0, 0.2, 1)}mat-autocomplete{display:none}\\n\"]\n    }]\n  }], () => [], {\n    template: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    displayWith: [{\n      type: Input\n    }],\n    autoActiveFirstOption: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoSelectActiveOption: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    requireSelection: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    optionSelected: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    closed: [{\n      type: Output\n    }],\n    optionActivated: [{\n      type: Output\n    }],\n    classList: [{\n      type: Input,\n      args: ['class']\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\nclass MatAutocompleteOrigin {\n  constructor() {\n    _defineProperty(this, \"elementRef\", inject(ElementRef));\n  }\n}\n_MatAutocompleteOrigin = MatAutocompleteOrigin;\n_defineProperty(MatAutocompleteOrigin, \"\\u0275fac\", function _MatAutocompleteOrigin_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatAutocompleteOrigin)();\n});\n_defineProperty(MatAutocompleteOrigin, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatAutocompleteOrigin,\n  selectors: [[\"\", \"matAutocompleteOrigin\", \"\"]],\n  exportAs: [\"matAutocompleteOrigin\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[matAutocompleteOrigin]',\n      exportAs: 'matAutocompleteOrigin'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatAutocompleteTrigger),\n  multi: true\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nfunction getMatAutocompleteMissingPanelError() {\n  return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' + 'Make sure that the id passed to the `matAutocomplete` is correct and that ' + \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken('mat-autocomplete-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY\n};\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\nclass MatAutocompleteTrigger {\n  constructor() {\n    _defineProperty(this, \"_environmentInjector\", inject(EnvironmentInjector));\n    _defineProperty(this, \"_element\", inject(ElementRef));\n    _defineProperty(this, \"_overlay\", inject(Overlay));\n    _defineProperty(this, \"_viewContainerRef\", inject(ViewContainerRef));\n    _defineProperty(this, \"_zone\", inject(NgZone));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_dir\", inject(Directionality, {\n      optional: true\n    }));\n    _defineProperty(this, \"_formField\", inject(MAT_FORM_FIELD, {\n      optional: true,\n      host: true\n    }));\n    _defineProperty(this, \"_viewportRuler\", inject(ViewportRuler));\n    _defineProperty(this, \"_scrollStrategy\", inject(MAT_AUTOCOMPLETE_SCROLL_STRATEGY));\n    _defineProperty(this, \"_renderer\", inject(Renderer2));\n    _defineProperty(this, \"_defaults\", inject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, {\n      optional: true\n    }));\n    _defineProperty(this, \"_overlayRef\", void 0);\n    _defineProperty(this, \"_portal\", void 0);\n    _defineProperty(this, \"_componentDestroyed\", false);\n    _defineProperty(this, \"_initialized\", new Subject());\n    _defineProperty(this, \"_keydownSubscription\", void 0);\n    _defineProperty(this, \"_outsideClickSubscription\", void 0);\n    _defineProperty(this, \"_cleanupWindowBlur\", void 0);\n    /** Old value of the native input. Used to work around issues with the `input` event on IE. */\n    _defineProperty(this, \"_previousValue\", void 0);\n    /** Value of the input element when the panel was attached (even if there are no options). */\n    _defineProperty(this, \"_valueOnAttach\", void 0);\n    /** Value on the previous keydown event. */\n    _defineProperty(this, \"_valueOnLastKeydown\", void 0);\n    /** Strategy that is used to position the panel. */\n    _defineProperty(this, \"_positionStrategy\", void 0);\n    /** Whether or not the label state is being overridden. */\n    _defineProperty(this, \"_manuallyFloatingLabel\", false);\n    /** The subscription for closing actions (some are bound to document). */\n    _defineProperty(this, \"_closingActionsSubscription\", void 0);\n    /** Subscription to viewport size changes. */\n    _defineProperty(this, \"_viewportSubscription\", Subscription.EMPTY);\n    /** Implements BreakpointObserver to be used to detect handset landscape */\n    _defineProperty(this, \"_breakpointObserver\", inject(BreakpointObserver));\n    _defineProperty(this, \"_handsetLandscapeSubscription\", Subscription.EMPTY);\n    /**\n     * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n     * closed autocomplete from being reopened if the user switches to another browser tab and then\n     * comes back.\n     */\n    _defineProperty(this, \"_canOpenOnNextFocus\", true);\n    /** Value inside the input before we auto-selected an option. */\n    _defineProperty(this, \"_valueBeforeAutoSelection\", void 0);\n    /**\n     * Current option that we have auto-selected as the user is navigating,\n     * but which hasn't been propagated to the model value yet.\n     */\n    _defineProperty(this, \"_pendingAutoselectedOption\", void 0);\n    /** Stream of keyboard events that can close the panel. */\n    _defineProperty(this, \"_closeKeyEventStream\", new Subject());\n    /**\n     * Event handler for when the window is blurred. Needs to be an\n     * arrow function in order to preserve the context.\n     */\n    _defineProperty(this, \"_windowBlurHandler\", () => {\n      // If the user blurred the window while the autocomplete is focused, it means that it'll be\n      // refocused when they come back. In this case we want to skip the first focus event, if the\n      // pane was closed, in order to avoid reopening it unintentionally.\n      this._canOpenOnNextFocus = this.panelOpen || !this._hasFocus();\n    });\n    /** `View -> model callback called when value changes` */\n    _defineProperty(this, \"_onChange\", () => {});\n    /** `View -> model callback called when autocomplete has been touched` */\n    _defineProperty(this, \"_onTouched\", () => {});\n    /** The autocomplete panel to be attached to this trigger. */\n    _defineProperty(this, \"autocomplete\", void 0);\n    /**\n     * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n     * will render the panel underneath the trigger if there is enough space for it to fit in\n     * the viewport, otherwise the panel will be shown above it. If the position is set to\n     * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n     * whether it fits completely in the viewport.\n     */\n    _defineProperty(this, \"position\", 'auto');\n    /**\n     * Reference relative to which to position the autocomplete panel.\n     * Defaults to the autocomplete trigger element.\n     */\n    _defineProperty(this, \"connectedTo\", void 0);\n    /**\n     * `autocomplete` attribute to be set on the input element.\n     * @docs-private\n     */\n    _defineProperty(this, \"autocompleteAttribute\", 'off');\n    /**\n     * Whether the autocomplete is disabled. When disabled, the element will\n     * act as a regular input and the user won't be able to open the panel.\n     */\n    _defineProperty(this, \"autocompleteDisabled\", void 0);\n    /** Class to apply to the panel when it's above the input. */\n    _defineProperty(this, \"_aboveClass\", 'mat-mdc-autocomplete-panel-above');\n    _defineProperty(this, \"_overlayAttached\", false);\n    /** Stream of changes to the selection state of the autocomplete options. */\n    _defineProperty(this, \"optionSelections\", defer(() => {\n      const options = this.autocomplete ? this.autocomplete.options : null;\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      }\n      // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n      // Return a stream that we'll replace with the real one once everything is in place.\n      return this._initialized.pipe(switchMap(() => this.optionSelections));\n    }));\n    /** Handles keyboard events coming from the overlay panel. */\n    _defineProperty(this, \"_handlePanelKeydown\", event => {\n      // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n      // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n      if (event.keyCode === ESCAPE && !hasModifierKey(event) || event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey')) {\n        // If the user had typed something in before we autoselected an option, and they decided\n        // to cancel the selection, restore the input value to the one they had typed in.\n        if (this._pendingAutoselectedOption) {\n          var _this$_valueBeforeAut;\n          this._updateNativeInputValue((_this$_valueBeforeAut = this._valueBeforeAutoSelection) !== null && _this$_valueBeforeAut !== void 0 ? _this$_valueBeforeAut : '');\n          this._pendingAutoselectedOption = null;\n        }\n        this._closeKeyEventStream.next();\n        this._resetActiveItem();\n        // We need to stop propagation, otherwise the event will eventually\n        // reach the input itself and cause the overlay to be reopened.\n        event.stopPropagation();\n        event.preventDefault();\n      }\n    });\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    _defineProperty(this, \"_trackedModal\", null);\n  }\n  ngAfterViewInit() {\n    this._initialized.next();\n    this._initialized.complete();\n    this._cleanupWindowBlur = this._renderer.listen('window', 'blur', this._windowBlurHandler);\n  }\n  ngOnChanges(changes) {\n    if (changes['position'] && this._positionStrategy) {\n      this._setStrategyPositions(this._positionStrategy);\n      if (this.panelOpen) {\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  ngOnDestroy() {\n    var _this$_cleanupWindowB;\n    (_this$_cleanupWindowB = this._cleanupWindowBlur) === null || _this$_cleanupWindowB === void 0 || _this$_cleanupWindowB.call(this);\n    this._handsetLandscapeSubscription.unsubscribe();\n    this._viewportSubscription.unsubscribe();\n    this._componentDestroyed = true;\n    this._destroyPanel();\n    this._closeKeyEventStream.complete();\n    this._clearFromModal();\n  }\n  /** Whether or not the autocomplete panel is open. */\n  get panelOpen() {\n    return this._overlayAttached && this.autocomplete.showPanel;\n  }\n  /** Opens the autocomplete suggestion panel. */\n  openPanel() {\n    this._openPanelInternal();\n  }\n  /** Closes the autocomplete suggestion panel. */\n  closePanel() {\n    this._resetLabel();\n    if (!this._overlayAttached) {\n      return;\n    }\n    if (this.panelOpen) {\n      // Only emit if the panel was visible.\n      // `afterNextRender` always runs outside of the Angular zone, so all the subscriptions from\n      // `_subscribeToClosingActions()` are also outside of the Angular zone.\n      // We should manually run in Angular zone to update UI after panel closing.\n      this._zone.run(() => {\n        this.autocomplete.closed.emit();\n      });\n    }\n    // Only reset if this trigger is the latest one that opened the\n    // autocomplete since another may have taken it over.\n    if (this.autocomplete._latestOpeningTrigger === this) {\n      this.autocomplete._isOpen = false;\n      this.autocomplete._latestOpeningTrigger = null;\n    }\n    this._overlayAttached = false;\n    this._pendingAutoselectedOption = null;\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n      this._closingActionsSubscription.unsubscribe();\n    }\n    this._updatePanelState();\n    // Note that in some cases this can end up being called after the component is destroyed.\n    // Add a check to ensure that we don't try to run change detection on a destroyed view.\n    if (!this._componentDestroyed) {\n      // We need to trigger change detection manually, because\n      // `fromEvent` doesn't seem to do it at the proper time.\n      // This ensures that the label is reset when the\n      // user clicks outside.\n      this._changeDetectorRef.detectChanges();\n    }\n    // Remove aria-owns attribute when the autocomplete is no longer visible.\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', this.autocomplete.id);\n    }\n  }\n  /**\n   * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n   * within the viewport.\n   */\n  updatePosition() {\n    if (this._overlayAttached) {\n      this._overlayRef.updatePosition();\n    }\n  }\n  /**\n   * A stream of actions that should close the autocomplete panel, including\n   * when an option is selected, on blur, and when TAB is pressed.\n   */\n  get panelClosingActions() {\n    return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached)) : of()).pipe(\n    // Normalize the output so we return a consistent type.\n    map(event => event instanceof MatOptionSelectionChange ? event : null));\n  }\n  /** The currently active option, coerced to MatOption type. */\n  get activeOption() {\n    if (this.autocomplete && this.autocomplete._keyManager) {\n      return this.autocomplete._keyManager.activeItem;\n    }\n    return null;\n  }\n  /** Stream of clicks outside of the autocomplete panel. */\n  _getOutsideClickStream() {\n    return new Observable(observer => {\n      const listener = event => {\n        // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n        // fall back to check the first element in the path of the click event.\n        const clickTarget = _getEventTarget(event);\n        const formField = this._formField ? this._formField.getConnectedOverlayOrigin().nativeElement : null;\n        const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n        if (this._overlayAttached && clickTarget !== this._element.nativeElement &&\n        // Normally focus moves inside `mousedown` so this condition will almost always be\n        // true. Its main purpose is to handle the case where the input is focused from an\n        // outside click which propagates up to the `body` listener within the same sequence\n        // and causes the panel to close immediately (see #3106).\n        !this._hasFocus() && (!formField || !formField.contains(clickTarget)) && (!customOrigin || !customOrigin.contains(clickTarget)) && !!this._overlayRef && !this._overlayRef.overlayElement.contains(clickTarget)) {\n          observer.next(event);\n        }\n      };\n      const cleanups = [this._renderer.listen('document', 'click', listener), this._renderer.listen('document', 'auxclick', listener), this._renderer.listen('document', 'touchend', listener)];\n      return () => {\n        cleanups.forEach(current => current());\n      };\n    });\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    Promise.resolve(null).then(() => this._assignOptionValue(value));\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this._element.nativeElement.disabled = isDisabled;\n  }\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const hasModifier = hasModifierKey(event);\n    // Prevent the default action on all escape key presses. This is here primarily to bring IE\n    // in line with other browsers. By default, pressing escape on IE will cause it to revert\n    // the input value to the one that it had on focus, however it won't dispatch any events\n    // which means that the model value will be out of sync with the view.\n    if (keyCode === ESCAPE && !hasModifier) {\n      event.preventDefault();\n    }\n    this._valueOnLastKeydown = this._element.nativeElement.value;\n    if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n      this.activeOption._selectViaInteraction();\n      this._resetActiveItem();\n      event.preventDefault();\n    } else if (this.autocomplete) {\n      const prevActiveItem = this.autocomplete._keyManager.activeItem;\n      const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n      if (keyCode === TAB || isArrowKey && !hasModifier && this.panelOpen) {\n        this.autocomplete._keyManager.onKeydown(event);\n      } else if (isArrowKey && this._canOpen()) {\n        this._openPanelInternal(this._valueOnLastKeydown);\n      }\n      if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n        this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n        if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n          if (!this._pendingAutoselectedOption) {\n            this._valueBeforeAutoSelection = this._valueOnLastKeydown;\n          }\n          this._pendingAutoselectedOption = this.activeOption;\n          this._assignOptionValue(this.activeOption.value);\n        }\n      }\n    }\n  }\n  _handleInput(event) {\n    let target = event.target;\n    let value = target.value;\n    // Based on `NumberValueAccessor` from forms.\n    if (target.type === 'number') {\n      value = value == '' ? null : parseFloat(value);\n    }\n    // If the input has a placeholder, IE will fire the `input` event on page load,\n    // focus and blur, in addition to when the user actually changed the value. To\n    // filter out all of the extra events, we save the value on focus and between\n    // `input` events, and we check whether it changed.\n    // See: https://connect.microsoft.com/IE/feedback/details/885747/\n    if (this._previousValue !== value) {\n      this._previousValue = value;\n      this._pendingAutoselectedOption = null;\n      // If selection is required we don't write to the CVA while the user is typing.\n      // At the end of the selection either the user will have picked something\n      // or we'll reset the value back to null.\n      if (!this.autocomplete || !this.autocomplete.requireSelection) {\n        this._onChange(value);\n      }\n      if (!value) {\n        this._clearPreviousSelectedOption(null, false);\n      } else if (this.panelOpen && !this.autocomplete.requireSelection) {\n        var _this$autocomplete$op;\n        // Note that we don't reset this when `requireSelection` is enabled,\n        // because the option will be reset when the panel is closed.\n        const selectedOption = (_this$autocomplete$op = this.autocomplete.options) === null || _this$autocomplete$op === void 0 ? void 0 : _this$autocomplete$op.find(option => option.selected);\n        if (selectedOption) {\n          const display = this._getDisplayValue(selectedOption.value);\n          if (value !== display) {\n            selectedOption.deselect(false);\n          }\n        }\n      }\n      if (this._canOpen() && this._hasFocus()) {\n        var _this$_valueOnLastKey;\n        // When the `input` event fires, the input's value will have already changed. This means\n        // that if we take the `this._element.nativeElement.value` directly, it'll be one keystroke\n        // behind. This can be a problem when the user selects a value, changes a character while\n        // the input still has focus and then clicks away (see #28432). To work around it, we\n        // capture the value in `keydown` so we can use it here.\n        const valueOnAttach = (_this$_valueOnLastKey = this._valueOnLastKeydown) !== null && _this$_valueOnLastKey !== void 0 ? _this$_valueOnLastKey : this._element.nativeElement.value;\n        this._valueOnLastKeydown = null;\n        this._openPanelInternal(valueOnAttach);\n      }\n    }\n  }\n  _handleFocus() {\n    if (!this._canOpenOnNextFocus) {\n      this._canOpenOnNextFocus = true;\n    } else if (this._canOpen()) {\n      this._previousValue = this._element.nativeElement.value;\n      this._attachOverlay(this._previousValue);\n      this._floatLabel(true);\n    }\n  }\n  _handleClick() {\n    if (this._canOpen() && !this.panelOpen) {\n      this._openPanelInternal();\n    }\n  }\n  /** Whether the input currently has focus. */\n  _hasFocus() {\n    return _getFocusedElementPierceShadowDom() === this._element.nativeElement;\n  }\n  /**\n   * In \"auto\" mode, the label will animate down as soon as focus is lost.\n   * This causes the value to jump when selecting an option with the mouse.\n   * This method manually floats the label until the panel can be closed.\n   * @param shouldAnimate Whether the label should be animated when it is floated.\n   */\n  _floatLabel(shouldAnimate = false) {\n    if (this._formField && this._formField.floatLabel === 'auto') {\n      if (shouldAnimate) {\n        this._formField._animateAndLockLabel();\n      } else {\n        this._formField.floatLabel = 'always';\n      }\n      this._manuallyFloatingLabel = true;\n    }\n  }\n  /** If the label has been manually elevated, return it to its normal state. */\n  _resetLabel() {\n    if (this._manuallyFloatingLabel) {\n      if (this._formField) {\n        this._formField.floatLabel = 'auto';\n      }\n      this._manuallyFloatingLabel = false;\n    }\n  }\n  /**\n   * This method listens to a stream of panel closing actions and resets the\n   * stream every time the option list changes.\n   */\n  _subscribeToClosingActions() {\n    var _this$autocomplete$op2, _this$autocomplete$op3;\n    const initialRender = new Observable(subscriber => {\n      afterNextRender(() => {\n        subscriber.next();\n      }, {\n        injector: this._environmentInjector\n      });\n    });\n    const optionChanges = (_this$autocomplete$op2 = (_this$autocomplete$op3 = this.autocomplete.options) === null || _this$autocomplete$op3 === void 0 ? void 0 : _this$autocomplete$op3.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()),\n    // Defer emitting to the stream until the next tick, because changing\n    // bindings in here will cause \"changed after checked\" errors.\n    delay(0))) !== null && _this$autocomplete$op2 !== void 0 ? _this$autocomplete$op2 : of();\n    // When the options are initially rendered, and when the option list changes...\n    return merge(initialRender, optionChanges).pipe(\n    // create a new stream of panelClosingActions, replacing any previous streams\n    // that were created, and flatten it so our stream only emits closing events...\n    switchMap(() => this._zone.run(() => {\n      // `afterNextRender` always runs outside of the Angular zone, thus we have to re-enter\n      // the Angular zone. This will lead to change detection being called outside of the Angular\n      // zone and the `autocomplete.opened` will also emit outside of the Angular.\n      const wasOpen = this.panelOpen;\n      this._resetActiveItem();\n      this._updatePanelState();\n      this._changeDetectorRef.detectChanges();\n      if (this.panelOpen) {\n        this._overlayRef.updatePosition();\n      }\n      if (wasOpen !== this.panelOpen) {\n        // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n        // `closed` event, because we may not have emitted it. This can happen\n        // - if the users opens the panel and there are no options, but the\n        //   options come in slightly later or as a result of the value changing,\n        // - if the panel is closed after the user entered a string that did not match any\n        //   of the available options,\n        // - if a valid string is entered after an invalid one.\n        if (this.panelOpen) {\n          this._emitOpened();\n        } else {\n          this.autocomplete.closed.emit();\n        }\n      }\n      return this.panelClosingActions;\n    })),\n    // when the first closing event occurs...\n    take(1))\n    // set the value, close the panel, and complete.\n    .subscribe(event => this._setValueAndClose(event));\n  }\n  /**\n   * Emits the opened event once it's known that the panel will be shown and stores\n   * the state of the trigger right before the opening sequence was finished.\n   */\n  _emitOpened() {\n    this.autocomplete.opened.emit();\n  }\n  /** Destroys the autocomplete suggestion panel. */\n  _destroyPanel() {\n    if (this._overlayRef) {\n      this.closePanel();\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n  }\n  /** Given a value, returns the string that should be shown within the input. */\n  _getDisplayValue(value) {\n    const autocomplete = this.autocomplete;\n    return autocomplete && autocomplete.displayWith ? autocomplete.displayWith(value) : value;\n  }\n  _assignOptionValue(value) {\n    const toDisplay = this._getDisplayValue(value);\n    if (value == null) {\n      this._clearPreviousSelectedOption(null, false);\n    }\n    // Simply falling back to an empty string if the display value is falsy does not work properly.\n    // The display value can also be the number zero and shouldn't fall back to an empty string.\n    this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n  }\n  _updateNativeInputValue(value) {\n    // If it's used within a `MatFormField`, we should set it through the property so it can go\n    // through change detection.\n    if (this._formField) {\n      this._formField._control.value = value;\n    } else {\n      this._element.nativeElement.value = value;\n    }\n    this._previousValue = value;\n  }\n  /**\n   * This method closes the panel, and if a value is specified, also sets the associated\n   * control to that value. It will also mark the control as dirty if this interaction\n   * stemmed from the user.\n   */\n  _setValueAndClose(event) {\n    const panel = this.autocomplete;\n    const toSelect = event ? event.source : this._pendingAutoselectedOption;\n    if (toSelect) {\n      this._clearPreviousSelectedOption(toSelect);\n      this._assignOptionValue(toSelect.value);\n      // TODO(crisbeto): this should wait until the animation is done, otherwise the value\n      // gets reset while the panel is still animating which looks glitchy. It'll likely break\n      // some tests to change it at this point.\n      this._onChange(toSelect.value);\n      panel._emitSelectEvent(toSelect);\n      this._element.nativeElement.focus();\n    } else if (panel.requireSelection && this._element.nativeElement.value !== this._valueOnAttach) {\n      this._clearPreviousSelectedOption(null);\n      this._assignOptionValue(null);\n      this._onChange(null);\n    }\n    this.closePanel();\n  }\n  /**\n   * Clear any previous selected option and emit a selection change event for this option\n   */\n  _clearPreviousSelectedOption(skip, emitEvent) {\n    var _this$autocomplete;\n    // Null checks are necessary here, because the autocomplete\n    // or its options may not have been assigned yet.\n    (_this$autocomplete = this.autocomplete) === null || _this$autocomplete === void 0 || (_this$autocomplete = _this$autocomplete.options) === null || _this$autocomplete === void 0 || _this$autocomplete.forEach(option => {\n      if (option !== skip && option.selected) {\n        option.deselect(emitEvent);\n      }\n    });\n  }\n  _openPanelInternal(valueOnAttach = this._element.nativeElement.value) {\n    this._attachOverlay(valueOnAttach);\n    this._floatLabel();\n    // Add aria-owns attribute when the autocomplete becomes visible.\n    if (this._trackedModal) {\n      const panelId = this.autocomplete.id;\n      addAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n  }\n  _attachOverlay(valueOnAttach) {\n    var _this$_formField2;\n    if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatAutocompleteMissingPanelError();\n    }\n    let overlayRef = this._overlayRef;\n    if (!overlayRef) {\n      var _this$_formField;\n      this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n        id: (_this$_formField = this._formField) === null || _this$_formField === void 0 ? void 0 : _this$_formField.getLabelId()\n      });\n      overlayRef = this._overlay.create(this._getOverlayConfig());\n      this._overlayRef = overlayRef;\n      this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n        if (this.panelOpen && overlayRef) {\n          overlayRef.updateSize({\n            width: this._getPanelWidth()\n          });\n        }\n      });\n      // Subscribe to the breakpoint events stream to detect when screen is in\n      // handsetLandscape.\n      this._handsetLandscapeSubscription = this._breakpointObserver.observe(Breakpoints.HandsetLandscape).subscribe(result => {\n        const isHandsetLandscape = result.matches;\n        // Check if result.matches Breakpoints.HandsetLandscape. Apply HandsetLandscape\n        // settings to prevent overlay cutoff in that breakpoint. Fixes b/284148377\n        if (isHandsetLandscape) {\n          this._positionStrategy.withFlexibleDimensions(true).withGrowAfterOpen(true).withViewportMargin(8);\n        } else {\n          this._positionStrategy.withFlexibleDimensions(false).withGrowAfterOpen(false).withViewportMargin(0);\n        }\n      });\n    } else {\n      // Update the trigger, panel width and direction, in case anything has changed.\n      this._positionStrategy.setOrigin(this._getConnectedElement());\n      overlayRef.updateSize({\n        width: this._getPanelWidth()\n      });\n    }\n    if (overlayRef && !overlayRef.hasAttached()) {\n      overlayRef.attach(this._portal);\n      this._valueOnAttach = valueOnAttach;\n      this._valueOnLastKeydown = null;\n      this._closingActionsSubscription = this._subscribeToClosingActions();\n    }\n    const wasOpen = this.panelOpen;\n    this.autocomplete._isOpen = this._overlayAttached = true;\n    this.autocomplete._latestOpeningTrigger = this;\n    this.autocomplete._setColor((_this$_formField2 = this._formField) === null || _this$_formField2 === void 0 ? void 0 : _this$_formField2.color);\n    this._updatePanelState();\n    this._applyModalPanelOwnership();\n    // We need to do an extra `panelOpen` check in here, because the\n    // autocomplete won't be shown if there are no options.\n    if (this.panelOpen && wasOpen !== this.panelOpen) {\n      this._emitOpened();\n    }\n  }\n  /** Updates the panel's visibility state and any trigger state tied to id. */\n  _updatePanelState() {\n    this.autocomplete._setVisibility();\n    // Note that here we subscribe and unsubscribe based on the panel's visiblity state,\n    // because the act of subscribing will prevent events from reaching other overlays and\n    // we don't want to block the events if there are no options.\n    if (this.panelOpen) {\n      const overlayRef = this._overlayRef;\n      if (!this._keydownSubscription) {\n        // Use the `keydownEvents` in order to take advantage of\n        // the overlay event targeting provided by the CDK overlay.\n        this._keydownSubscription = overlayRef.keydownEvents().subscribe(this._handlePanelKeydown);\n      }\n      if (!this._outsideClickSubscription) {\n        // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n        // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n        // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n        this._outsideClickSubscription = overlayRef.outsidePointerEvents().subscribe();\n      }\n    } else {\n      var _this$_keydownSubscri, _this$_outsideClickSu;\n      (_this$_keydownSubscri = this._keydownSubscription) === null || _this$_keydownSubscri === void 0 || _this$_keydownSubscri.unsubscribe();\n      (_this$_outsideClickSu = this._outsideClickSubscription) === null || _this$_outsideClickSu === void 0 || _this$_outsideClickSu.unsubscribe();\n      this._keydownSubscription = this._outsideClickSubscription = null;\n    }\n  }\n  _getOverlayConfig() {\n    var _this$_dir, _this$_defaults, _this$_defaults2, _this$_defaults3;\n    return new OverlayConfig({\n      positionStrategy: this._getOverlayPosition(),\n      scrollStrategy: this._scrollStrategy(),\n      width: this._getPanelWidth(),\n      direction: (_this$_dir = this._dir) !== null && _this$_dir !== void 0 ? _this$_dir : undefined,\n      hasBackdrop: (_this$_defaults = this._defaults) === null || _this$_defaults === void 0 ? void 0 : _this$_defaults.hasBackdrop,\n      backdropClass: (_this$_defaults2 = this._defaults) === null || _this$_defaults2 === void 0 ? void 0 : _this$_defaults2.backdropClass,\n      panelClass: (_this$_defaults3 = this._defaults) === null || _this$_defaults3 === void 0 ? void 0 : _this$_defaults3.overlayPanelClass\n    });\n  }\n  _getOverlayPosition() {\n    // Set default Overlay Position\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getConnectedElement()).withFlexibleDimensions(false).withPush(false);\n    this._setStrategyPositions(strategy);\n    this._positionStrategy = strategy;\n    return strategy;\n  }\n  /** Sets the positions on a position strategy based on the directive's input state. */\n  _setStrategyPositions(positionStrategy) {\n    // Note that we provide horizontal fallback positions, even though by default the dropdown\n    // width matches the input, because consumers can override the width. See #18854.\n    const belowPositions = [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    }];\n    // The overlay edge connected to the trigger should have squared corners, while\n    // the opposite end has rounded corners. We apply a CSS class to swap the\n    // border-radius based on the overlay position.\n    const panelClass = this._aboveClass;\n    const abovePositions = [{\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass\n    }, {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass\n    }];\n    let positions;\n    if (this.position === 'above') {\n      positions = abovePositions;\n    } else if (this.position === 'below') {\n      positions = belowPositions;\n    } else {\n      positions = [...belowPositions, ...abovePositions];\n    }\n    positionStrategy.withPositions(positions);\n  }\n  _getConnectedElement() {\n    if (this.connectedTo) {\n      return this.connectedTo.elementRef;\n    }\n    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n  }\n  _getPanelWidth() {\n    return this.autocomplete.panelWidth || this._getHostWidth();\n  }\n  /** Returns the width of the input element, so the panel width can match it. */\n  _getHostWidth() {\n    return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n  }\n  /**\n   * Reset the active item to -1. This is so that pressing arrow keys will activate the correct\n   * option.\n   *\n   * If the consumer opted-in to automatically activatating the first option, activate the first\n   * *enabled* option.\n   */\n  _resetActiveItem() {\n    const autocomplete = this.autocomplete;\n    if (autocomplete.autoActiveFirstOption) {\n      // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n      // because it activates the first option that passes the skip predicate, rather than the\n      // first *enabled* option.\n      let firstEnabledOptionIndex = -1;\n      for (let index = 0; index < autocomplete.options.length; index++) {\n        const option = autocomplete.options.get(index);\n        if (!option.disabled) {\n          firstEnabledOptionIndex = index;\n          break;\n        }\n      }\n      autocomplete._keyManager.setActiveItem(firstEnabledOptionIndex);\n    } else {\n      autocomplete._keyManager.setActiveItem(-1);\n    }\n  }\n  /** Determines whether the panel can be opened. */\n  _canOpen() {\n    const element = this._element.nativeElement;\n    return !element.readOnly && !element.disabled && !this.autocompleteDisabled;\n  }\n  /** Scrolls to a particular option in the list. */\n  _scrollToOption(index) {\n    // Given that we are not actually focusing active options, we must manually adjust scroll\n    // to reveal options below the fold. First, we find the offset of the option from the top\n    // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n    // the panel height + the option height, so the active option will be just visible at the\n    // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n    // will become the offset. If that offset is visible within the panel already, the scrollTop is\n    // not adjusted.\n    const autocomplete = this.autocomplete;\n    const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n    if (index === 0 && labelCount === 1) {\n      // If we've got one group label before the option and we're at the top option,\n      // scroll the list to the top. This is better UX than scrolling the list to the\n      // top of the option, because it allows the user to read the top group's label.\n      autocomplete._setScrollTop(0);\n    } else if (autocomplete.panel) {\n      const option = autocomplete.options.toArray()[index];\n      if (option) {\n        const element = option._getHostElement();\n        const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n        autocomplete._setScrollTop(newScrollPosition);\n      }\n    }\n  }\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._element.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n    const panelId = this.autocomplete.id;\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n  /** Clears the references to the listbox overlay element from the modal it was added to. */\n  _clearFromModal() {\n    if (this._trackedModal) {\n      const panelId = this.autocomplete.id;\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      this._trackedModal = null;\n    }\n  }\n}\n_MatAutocompleteTrigger = MatAutocompleteTrigger;\n_defineProperty(MatAutocompleteTrigger, \"\\u0275fac\", function _MatAutocompleteTrigger_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatAutocompleteTrigger)();\n});\n_defineProperty(MatAutocompleteTrigger, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatAutocompleteTrigger,\n  selectors: [[\"input\", \"matAutocomplete\", \"\"], [\"textarea\", \"matAutocomplete\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-autocomplete-trigger\"],\n  hostVars: 7,\n  hostBindings: function _MatAutocompleteTrigger_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focusin\", function _MatAutocompleteTrigger_focusin_HostBindingHandler() {\n        return ctx._handleFocus();\n      })(\"blur\", function _MatAutocompleteTrigger_blur_HostBindingHandler() {\n        return ctx._onTouched();\n      })(\"input\", function _MatAutocompleteTrigger_input_HostBindingHandler($event) {\n        return ctx._handleInput($event);\n      })(\"keydown\", function _MatAutocompleteTrigger_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      })(\"click\", function _MatAutocompleteTrigger_click_HostBindingHandler() {\n        return ctx._handleClick();\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"autocomplete\", ctx.autocompleteAttribute)(\"role\", ctx.autocompleteDisabled ? null : \"combobox\")(\"aria-autocomplete\", ctx.autocompleteDisabled ? null : \"list\")(\"aria-activedescendant\", ctx.panelOpen && ctx.activeOption ? ctx.activeOption.id : null)(\"aria-expanded\", ctx.autocompleteDisabled ? null : ctx.panelOpen.toString())(\"aria-controls\", ctx.autocompleteDisabled || !ctx.panelOpen ? null : ctx.autocomplete == null ? null : ctx.autocomplete.id)(\"aria-haspopup\", ctx.autocompleteDisabled ? null : \"listbox\");\n    }\n  },\n  inputs: {\n    autocomplete: [0, \"matAutocomplete\", \"autocomplete\"],\n    position: [0, \"matAutocompletePosition\", \"position\"],\n    connectedTo: [0, \"matAutocompleteConnectedTo\", \"connectedTo\"],\n    autocompleteAttribute: [0, \"autocomplete\", \"autocompleteAttribute\"],\n    autocompleteDisabled: [2, \"matAutocompleteDisabled\", \"autocompleteDisabled\", booleanAttribute]\n  },\n  exportAs: [\"matAutocompleteTrigger\"],\n  features: [i0.ɵɵProvidersFeature([MAT_AUTOCOMPLETE_VALUE_ACCESSOR]), i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n      host: {\n        'class': 'mat-mdc-autocomplete-trigger',\n        '[attr.autocomplete]': 'autocompleteAttribute',\n        '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n        '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n        '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n        '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n        '[attr.aria-controls]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n        '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n        // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n        // a little earlier. This avoids issues where IE delays the focusing of the input.\n        '(focusin)': '_handleFocus()',\n        '(blur)': '_onTouched()',\n        '(input)': '_handleInput($event)',\n        '(keydown)': '_handleKeydown($event)',\n        '(click)': '_handleClick()'\n      },\n      exportAs: 'matAutocompleteTrigger',\n      providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR]\n    }]\n  }], () => [], {\n    autocomplete: [{\n      type: Input,\n      args: ['matAutocomplete']\n    }],\n    position: [{\n      type: Input,\n      args: ['matAutocompletePosition']\n    }],\n    connectedTo: [{\n      type: Input,\n      args: ['matAutocompleteConnectedTo']\n    }],\n    autocompleteAttribute: [{\n      type: Input,\n      args: ['autocomplete']\n    }],\n    autocompleteDisabled: [{\n      type: Input,\n      args: [{\n        alias: 'matAutocompleteDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatAutocompleteModule {}\n_MatAutocompleteModule = MatAutocompleteModule;\n_defineProperty(MatAutocompleteModule, \"\\u0275fac\", function _MatAutocompleteModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatAutocompleteModule)();\n});\n_defineProperty(MatAutocompleteModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatAutocompleteModule,\n  imports: [OverlayModule, MatOptionModule, MatCommonModule, MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n  exports: [CdkScrollableModule, MatAutocomplete, MatOptionModule, MatCommonModule, MatAutocompleteTrigger, MatAutocompleteOrigin]\n}));\n_defineProperty(MatAutocompleteModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n  imports: [OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatOptionModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n      exports: [CdkScrollableModule, MatAutocomplete, MatOptionModule, MatCommonModule, MatAutocompleteTrigger, MatAutocompleteOrigin],\n      providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, MatOption, getMatAutocompleteMissingPanelError };", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "rf", "formFieldId_r1", "ctx", "id", "ctx_r1", "ɵɵnextContext", "ɵɵclassMap", "_classList", "ɵɵclassProp", "showPanel", "_animationsDisabled", "_color", "ɵɵproperty", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "_getPanelAriaLabe<PERSON>by", "c", "MAT_OPTION_PARENT_COMPONENT", "M", "MatOption", "d", "MAT_OPTGROUP", "e", "MatOptionSelectionChange", "_", "_countGroupLabelsBeforeOption", "b", "_getOptionScrollPosition", "a", "MatOptgroup", "InjectionToken", "inject", "ChangeDetectorRef", "ElementRef", "ANIMATION_MODULE_TYPE", "EventEmitter", "booleanAttribute", "TemplateRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "ContentChildren", "Input", "Output", "Directive", "forwardRef", "EnvironmentInjector", "ViewContainerRef", "NgZone", "Renderer2", "afterNextRender", "NgModule", "ViewportRuler", "CdkScrollableModule", "Overlay", "OverlayConfig", "OverlayModule", "_IdGenerator", "ActiveDescendantKeyManager", "removeAriaReferencedId", "addAriaReferencedId", "Platform", "_getFocusedElementPierceShadowDom", "_getEventTarget", "Subscription", "Subject", "merge", "of", "defer", "Observable", "Directionality", "hasModifierKey", "ESCAPE", "ENTER", "TAB", "UP_ARROW", "DOWN_ARROW", "BreakpointObserver", "Breakpoints", "TemplatePortal", "NG_VALUE_ACCESSOR", "filter", "map", "startWith", "switchMap", "tap", "delay", "take", "h", "MAT_FORM_FIELD", "MatOptionModule", "MatCommonModule", "MatAutocompleteSelectedEvent", "constructor", "source", "option", "_defineProperty", "MAT_AUTOCOMPLETE_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY", "autoActiveFirstOption", "autoSelectActiveOption", "hideSingleSelectionIndicator", "requireSelection", "hasBackdrop", "MatAutocomplete", "isOpen", "_isOpen", "_setColor", "value", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classList", "_elementRef", "nativeElement", "className", "_hideSingleSelectionIndicator", "_syncParentProperties", "options", "_this$_defaults$hideS", "optional", "EMPTY", "getId", "platform", "inertGroups", "SAFARI", "_defaults", "ngAfterContentInit", "_keyManager", "withWrap", "skipPredicate", "_skipPredicate", "_activeOptionChanges", "change", "subscribe", "index", "optionActivated", "emit", "toArray", "_setVisibility", "ngOnDestroy", "_this$_keyManager", "destroy", "unsubscribe", "_setScrollTop", "scrollTop", "panel", "_getScrollTop", "_this$options", "length", "_emitSelectEvent", "event", "optionSelected", "labelId", "labelExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_MatAutocomplete", "_MatAutocomplete_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "_MatAutocomplete_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "optionGroups", "viewQuery", "_MatAutocomplete_Query", "ɵɵviewQuery", "_c0", "template", "first", "hostAttrs", "inputs", "displayWith", "panelWidth", "disable<PERSON><PERSON><PERSON>", "outputs", "opened", "closed", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ngContentSelectors", "_c1", "decls", "vars", "consts", "_MatAutocomplete_Template", "ɵɵprojectionDef", "ɵɵtemplate", "_MatAutocomplete_ng_template_0_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "host", "providers", "static", "descendants", "transform", "MatAutocompleteOrigin", "_MatAutocompleteOrigin", "_MatAutocompleteOrigin_Factory", "ɵɵdefineDirective", "MAT_AUTOCOMPLETE_VALUE_ACCESSOR", "MatAutocompleteTrigger", "multi", "getMatAutocompleteMissingPanelError", "Error", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY", "overlay", "scrollStrategies", "reposition", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "_canOpenOnNextFocus", "panelOpen", "_hasFocus", "autocomplete", "changes", "pipe", "onSelectionChange", "_initialized", "optionSelections", "keyCode", "_pendingAutoselectedOption", "_this$_valueBeforeAut", "_updateNativeInputValue", "_valueBeforeAutoSelection", "_closeKeyEventStream", "next", "_resetActiveItem", "stopPropagation", "preventDefault", "ngAfterViewInit", "complete", "_cleanupWindowBlur", "_renderer", "listen", "_windowBlurHandler", "ngOnChanges", "_positionStrategy", "_setStrategyPositions", "_overlayRef", "updatePosition", "_this$_cleanupWindowB", "call", "_handsetLandscapeSubscription", "_viewportSubscription", "_componentDestroyed", "_destroyPanel", "_clearFromModal", "_overlayAttached", "openPanel", "_openPanelInternal", "closePanel", "_resetLabel", "_zone", "run", "_latestOpeningTrigger", "has<PERSON>tta<PERSON>", "detach", "_closingActionsSubscription", "_updatePanelState", "detectChanges", "_trackedModal", "panelClosingActions", "tabOut", "_getOutsideClickStream", "detachments", "activeOption", "activeItem", "observer", "listener", "clickTarget", "formField", "_formField", "getConnectedOverlayOrigin", "customOrigin", "connectedTo", "elementRef", "_element", "contains", "overlayElement", "cleanups", "for<PERSON>ach", "current", "writeValue", "Promise", "resolve", "then", "_assignOptionValue", "registerOnChange", "fn", "_onChange", "registerOnTouched", "_onTouched", "setDisabledState", "isDisabled", "disabled", "_handleKeydown", "hasModifier", "_valueOnLastKeydown", "_selectViaInteraction", "prevActiveItem", "isArrowKey", "onKeydown", "_canOpen", "_scrollToOption", "activeItemIndex", "_handleInput", "target", "parseFloat", "_previousValue", "_clearPreviousSelectedOption", "_this$autocomplete$op", "selectedOption", "find", "selected", "display", "_getDisplayValue", "deselect", "_this$_valueOnLastKey", "valueOnAttach", "_handleFocus", "_attachOverlay", "_floatLabel", "_handleClick", "shouldAnimate", "floatLabel", "_animateAndLockLabel", "_manuallyFloatingLabel", "_subscribeToClosingActions", "_this$autocomplete$op2", "_this$autocomplete$op3", "initialRender", "subscriber", "injector", "_environmentInjector", "optionChanges", "reapplyLastPosition", "was<PERSON><PERSON>", "_emitOpened", "_setValueAndClose", "dispose", "toDisplay", "_control", "toSelect", "focus", "_valueOnAttach", "skip", "emitEvent", "_this$autocomplete", "panelId", "_this$_formField2", "overlayRef", "_this$_formField", "_portal", "_viewContainerRef", "getLabelId", "_overlay", "create", "_getOverlayConfig", "_viewportRuler", "updateSize", "width", "_get<PERSON><PERSON><PERSON><PERSON>idth", "_breakpointObserver", "observe", "HandsetLandscape", "result", "isHandsetLandscape", "matches", "withFlexibleDimensions", "withGrowAfterOpen", "withViewportMargin", "<PERSON><PERSON><PERSON><PERSON>", "_getConnectedElement", "attach", "color", "_applyModalPanelOwnership", "_keydownSubscription", "keydownEvents", "_handlePanelKeydown", "_outsideClickSubscription", "outsidePointerEvents", "_this$_keydownSubscri", "_this$_outsideClickSu", "_this$_dir", "_this$_defaults", "_this$_defaults2", "_this$_defaults3", "positionStrategy", "_getOverlayPosition", "scrollStrategy", "_scrollStrategy", "direction", "_dir", "undefined", "backdropClass", "panelClass", "overlayPanelClass", "strategy", "position", "flexibleConnectedTo", "with<PERSON><PERSON>", "belowPositions", "originX", "originY", "overlayX", "overlayY", "_aboveClass", "abovePositions", "positions", "withPositions", "_getHostWidth", "getBoundingClientRect", "firstEnabledOptionIndex", "get", "setActiveItem", "element", "readOnly", "autocompleteDisabled", "labelCount", "_getHostElement", "newScrollPosition", "offsetTop", "offsetHeight", "modal", "closest", "_MatAutocompleteTrigger", "_MatAutocompleteTrigger_Factory", "hostVars", "hostBindings", "_MatAutocompleteTrigger_HostBindings", "ɵɵlistener", "_MatAutocompleteTrigger_focusin_HostBindingHandler", "_MatAutocompleteTrigger_blur_HostBindingHandler", "_MatAutocompleteTrigger_input_HostBindingHandler", "$event", "_MatAutocompleteTrigger_keydown_HostBindingHandler", "_MatAutocompleteTrigger_click_HostBindingHandler", "autocompleteAttribute", "toString", "ɵɵNgOnChangesFeature", "alias", "MatAutocompleteModule", "_MatAutocompleteModule", "_MatAutocompleteModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/autocomplete.mjs"], "sourcesContent": ["import { c as MAT_OPTION_PARENT_COMPONENT, M as MatOption, d as MAT_OPTGROUP, e as MatOptionSelectionChange, _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition } from './option-ChV6uQgD.mjs';\nexport { a as MatOptgroup } from './option-ChV6uQgD.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, ANIMATION_MODULE_TYPE, EventEmitter, booleanAttribute, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, ContentChildren, Input, Output, Directive, forwardRef, EnvironmentInjector, ViewContainerRef, NgZone, Renderer2, afterNextRender, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { _IdGenerator, ActiveDescendantKeyManager, removeAriaReferencedId, addAriaReferencedId } from '@angular/cdk/a11y';\nimport { Platform, _getFocusedElementPierceShadowDom, _getEventTarget } from '@angular/cdk/platform';\nimport { Subscription, Subject, merge, of, defer, Observable } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { hasModifierKey, ESCAPE, ENTER, TAB, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { filter, map, startWith, switchMap, tap, delay, take } from 'rxjs/operators';\nimport { h as MAT_FORM_FIELD } from './form-field-DqPi4knt.mjs';\nimport { M as MatOptionModule } from './index-DOxJc1m4.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-CJ7seqQH.mjs';\nimport './structural-styles-BQUT6wsL.mjs';\nimport '@angular/common';\nimport '@angular/cdk/observers/private';\nimport './index-SYVYjXwK.mjs';\nimport './pseudo-checkbox-module-CAX2sutq.mjs';\n\n/** Event object that is emitted when an autocomplete option is selected. */\nclass MatAutocompleteSelectedEvent {\n    source;\n    option;\n    constructor(\n    /** Reference to the autocomplete panel that emitted the event. */\n    source, \n    /** Option that was selected. */\n    option) {\n        this.source = source;\n        this.option = option;\n    }\n}\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken('mat-autocomplete-default-options', {\n    providedIn: 'root',\n    factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        autoActiveFirstOption: false,\n        autoSelectActiveOption: false,\n        hideSingleSelectionIndicator: false,\n        requireSelection: false,\n        hasBackdrop: false,\n    };\n}\n/** Autocomplete component. */\nclass MatAutocomplete {\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _defaults = inject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS);\n    _animationsDisabled = inject(ANIMATION_MODULE_TYPE, { optional: true }) === 'NoopAnimations';\n    _activeOptionChanges = Subscription.EMPTY;\n    /** Manages active item in option list based on key events. */\n    _keyManager;\n    /** Whether the autocomplete panel should be visible, depending on option length. */\n    showPanel = false;\n    /** Whether the autocomplete panel is open. */\n    get isOpen() {\n        return this._isOpen && this.showPanel;\n    }\n    _isOpen = false;\n    /** Latest trigger that opened the autocomplete. */\n    _latestOpeningTrigger;\n    /** @docs-private Sets the theme color of the panel. */\n    _setColor(value) {\n        this._color = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** @docs-private theme color of the panel */\n    _color;\n    // The @ViewChild query for TemplateRef here needs to be static because some code paths\n    // lead to the overlay being created before change detection has finished for this component.\n    // Notably, another component may trigger `focus` on the autocomplete-trigger.\n    /** @docs-private */\n    template;\n    /** Element for the panel containing the autocomplete options. */\n    panel;\n    /** Reference to all options within the autocomplete. */\n    options;\n    /** Reference to all option groups within the autocomplete. */\n    optionGroups;\n    /** Aria label of the autocomplete. */\n    ariaLabel;\n    /** Input that can be used to specify the `aria-labelledby` attribute. */\n    ariaLabelledby;\n    /** Function that maps an option's control value to its display value in the trigger. */\n    displayWith = null;\n    /**\n     * Whether the first option should be highlighted when the autocomplete panel is opened.\n     * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n     */\n    autoActiveFirstOption;\n    /** Whether the active option should be selected as the user is navigating. */\n    autoSelectActiveOption;\n    /**\n     * Whether the user is required to make a selection when they're interacting with the\n     * autocomplete. If the user moves away from the autocomplete without selecting an option from\n     * the list, the value will be reset. If the user opens the panel and closes it without\n     * interacting or selecting a value, the initial value will be kept.\n     */\n    requireSelection;\n    /**\n     * Specify the width of the autocomplete panel.  Can be any CSS sizing value, otherwise it will\n     * match the width of its host.\n     */\n    panelWidth;\n    /** Whether ripples are disabled within the autocomplete panel. */\n    disableRipple;\n    /** Event that is emitted whenever an option from the list is selected. */\n    optionSelected = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is opened. */\n    opened = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is closed. */\n    closed = new EventEmitter();\n    /** Emits whenever an option is activated. */\n    optionActivated = new EventEmitter();\n    /**\n     * Takes classes set on the host mat-autocomplete element and applies them to the panel\n     * inside the overlay container to allow for easy styling.\n     */\n    set classList(value) {\n        this._classList = value;\n        this._elementRef.nativeElement.className = '';\n    }\n    _classList;\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = value;\n        this._syncParentProperties();\n    }\n    _hideSingleSelectionIndicator;\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n        if (this.options) {\n            for (const option of this.options) {\n                option._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n    id = inject(_IdGenerator).getId('mat-autocomplete-');\n    /**\n     * Tells any descendant `mat-optgroup` to use the inert a11y pattern.\n     * @docs-private\n     */\n    inertGroups;\n    constructor() {\n        const platform = inject(Platform);\n        // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n        // Safari using VoiceOver. We should occasionally check back to see whether the bug\n        // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n        // option altogether.\n        this.inertGroups = platform?.SAFARI || false;\n        this.autoActiveFirstOption = !!this._defaults.autoActiveFirstOption;\n        this.autoSelectActiveOption = !!this._defaults.autoSelectActiveOption;\n        this.requireSelection = !!this._defaults.requireSelection;\n        this._hideSingleSelectionIndicator = this._defaults.hideSingleSelectionIndicator ?? false;\n    }\n    ngAfterContentInit() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options)\n            .withWrap()\n            .skipPredicate(this._skipPredicate);\n        this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n            if (this.isOpen) {\n                this.optionActivated.emit({ source: this, option: this.options.toArray()[index] || null });\n            }\n        });\n        // Set the initial visibility state.\n        this._setVisibility();\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._activeOptionChanges.unsubscribe();\n    }\n    /**\n     * Sets the panel scrollTop. This allows us to manually scroll to display options\n     * above or below the fold, as they are not actually being focused when active.\n     */\n    _setScrollTop(scrollTop) {\n        if (this.panel) {\n            this.panel.nativeElement.scrollTop = scrollTop;\n        }\n    }\n    /** Returns the panel's scrollTop. */\n    _getScrollTop() {\n        return this.panel ? this.panel.nativeElement.scrollTop : 0;\n    }\n    /** Panel should hide itself when the option list is empty. */\n    _setVisibility() {\n        this.showPanel = !!this.options?.length;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits the `select` event. */\n    _emitSelectEvent(option) {\n        const event = new MatAutocompleteSelectedEvent(this, option);\n        this.optionSelected.emit(event);\n    }\n    /** Gets the aria-labelledby for the autocomplete panel. */\n    _getPanelAriaLabelledby(labelId) {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    _skipPredicate() {\n        return false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAutocomplete, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatAutocomplete, isStandalone: true, selector: \"mat-autocomplete\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], displayWith: \"displayWith\", autoActiveFirstOption: [\"autoActiveFirstOption\", \"autoActiveFirstOption\", booleanAttribute], autoSelectActiveOption: [\"autoSelectActiveOption\", \"autoSelectActiveOption\", booleanAttribute], requireSelection: [\"requireSelection\", \"requireSelection\", booleanAttribute], panelWidth: \"panelWidth\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], classList: [\"class\", \"classList\"], hideSingleSelectionIndicator: [\"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute] }, outputs: { optionSelected: \"optionSelected\", opened: \"opened\", closed: \"closed\", optionActivated: \"optionActivated\" }, host: { classAttribute: \"mat-mdc-autocomplete\" }, providers: [{ provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete }], queries: [{ propertyName: \"options\", predicate: MatOption, descendants: true }, { propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }], viewQueries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true, static: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }], exportAs: [\"matAutocomplete\"], ngImport: i0, template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div\\n    class=\\\"mat-mdc-autocomplete-panel mdc-menu-surface mdc-menu-surface--open\\\"\\n    role=\\\"listbox\\\"\\n    [id]=\\\"id\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-mdc-autocomplete-visible]=\\\"showPanel\\\"\\n    [class.mat-mdc-autocomplete-hidden]=\\\"!showPanel\\\"\\n    [class.mat-autocomplete-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [class.mat-primary]=\\\"_color === 'primary'\\\"\\n    [class.mat-accent]=\\\"_color === 'accent'\\\"\\n    [class.mat-warn]=\\\"_color === 'warn'\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n    #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\"div.mat-mdc-autocomplete-panel{width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;position:relative;border-radius:var(--mat-autocomplete-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-autocomplete-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-autocomplete-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-autocomplete-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden;pointer-events:none}@keyframes _mat-autocomplete-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}.mat-autocomplete-panel-animations-enabled{animation:_mat-autocomplete-enter 120ms cubic-bezier(0, 0, 0.2, 1)}mat-autocomplete{display:none}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAutocomplete, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-autocomplete', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, exportAs: 'matAutocomplete', host: {\n                        'class': 'mat-mdc-autocomplete',\n                    }, providers: [{ provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete }], template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div\\n    class=\\\"mat-mdc-autocomplete-panel mdc-menu-surface mdc-menu-surface--open\\\"\\n    role=\\\"listbox\\\"\\n    [id]=\\\"id\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-mdc-autocomplete-visible]=\\\"showPanel\\\"\\n    [class.mat-mdc-autocomplete-hidden]=\\\"!showPanel\\\"\\n    [class.mat-autocomplete-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [class.mat-primary]=\\\"_color === 'primary'\\\"\\n    [class.mat-accent]=\\\"_color === 'accent'\\\"\\n    [class.mat-warn]=\\\"_color === 'warn'\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n    #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\"div.mat-mdc-autocomplete-panel{width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;position:relative;border-radius:var(--mat-autocomplete-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-autocomplete-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-autocomplete-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-autocomplete-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden;pointer-events:none}@keyframes _mat-autocomplete-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}.mat-autocomplete-panel-animations-enabled{animation:_mat-autocomplete-enter 120ms cubic-bezier(0, 0, 0.2, 1)}mat-autocomplete{display:none}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { template: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }], optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], displayWith: [{\n                type: Input\n            }], autoActiveFirstOption: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoSelectActiveOption: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], requireSelection: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], panelWidth: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], optionSelected: [{\n                type: Output\n            }], opened: [{\n                type: Output\n            }], closed: [{\n                type: Output\n            }], optionActivated: [{\n                type: Output\n            }], classList: [{\n                type: Input,\n                args: ['class']\n            }], hideSingleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\nclass MatAutocompleteOrigin {\n    elementRef = inject(ElementRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAutocompleteOrigin, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatAutocompleteOrigin, isStandalone: true, selector: \"[matAutocompleteOrigin]\", exportAs: [\"matAutocompleteOrigin\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAutocompleteOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matAutocompleteOrigin]',\n                    exportAs: 'matAutocompleteOrigin',\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatAutocompleteTrigger),\n    multi: true,\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nfunction getMatAutocompleteMissingPanelError() {\n    return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' +\n        'Make sure that the id passed to the `matAutocomplete` is correct and that ' +\n        \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken('mat-autocomplete-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition();\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY,\n};\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\nclass MatAutocompleteTrigger {\n    _environmentInjector = inject(EnvironmentInjector);\n    _element = inject(ElementRef);\n    _overlay = inject(Overlay);\n    _viewContainerRef = inject(ViewContainerRef);\n    _zone = inject(NgZone);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _dir = inject(Directionality, { optional: true });\n    _formField = inject(MAT_FORM_FIELD, { optional: true, host: true });\n    _viewportRuler = inject(ViewportRuler);\n    _scrollStrategy = inject(MAT_AUTOCOMPLETE_SCROLL_STRATEGY);\n    _renderer = inject(Renderer2);\n    _defaults = inject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, { optional: true });\n    _overlayRef;\n    _portal;\n    _componentDestroyed = false;\n    _initialized = new Subject();\n    _keydownSubscription;\n    _outsideClickSubscription;\n    _cleanupWindowBlur;\n    /** Old value of the native input. Used to work around issues with the `input` event on IE. */\n    _previousValue;\n    /** Value of the input element when the panel was attached (even if there are no options). */\n    _valueOnAttach;\n    /** Value on the previous keydown event. */\n    _valueOnLastKeydown;\n    /** Strategy that is used to position the panel. */\n    _positionStrategy;\n    /** Whether or not the label state is being overridden. */\n    _manuallyFloatingLabel = false;\n    /** The subscription for closing actions (some are bound to document). */\n    _closingActionsSubscription;\n    /** Subscription to viewport size changes. */\n    _viewportSubscription = Subscription.EMPTY;\n    /** Implements BreakpointObserver to be used to detect handset landscape */\n    _breakpointObserver = inject(BreakpointObserver);\n    _handsetLandscapeSubscription = Subscription.EMPTY;\n    /**\n     * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n     * closed autocomplete from being reopened if the user switches to another browser tab and then\n     * comes back.\n     */\n    _canOpenOnNextFocus = true;\n    /** Value inside the input before we auto-selected an option. */\n    _valueBeforeAutoSelection;\n    /**\n     * Current option that we have auto-selected as the user is navigating,\n     * but which hasn't been propagated to the model value yet.\n     */\n    _pendingAutoselectedOption;\n    /** Stream of keyboard events that can close the panel. */\n    _closeKeyEventStream = new Subject();\n    /**\n     * Event handler for when the window is blurred. Needs to be an\n     * arrow function in order to preserve the context.\n     */\n    _windowBlurHandler = () => {\n        // If the user blurred the window while the autocomplete is focused, it means that it'll be\n        // refocused when they come back. In this case we want to skip the first focus event, if the\n        // pane was closed, in order to avoid reopening it unintentionally.\n        this._canOpenOnNextFocus = this.panelOpen || !this._hasFocus();\n    };\n    /** `View -> model callback called when value changes` */\n    _onChange = () => { };\n    /** `View -> model callback called when autocomplete has been touched` */\n    _onTouched = () => { };\n    /** The autocomplete panel to be attached to this trigger. */\n    autocomplete;\n    /**\n     * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n     * will render the panel underneath the trigger if there is enough space for it to fit in\n     * the viewport, otherwise the panel will be shown above it. If the position is set to\n     * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n     * whether it fits completely in the viewport.\n     */\n    position = 'auto';\n    /**\n     * Reference relative to which to position the autocomplete panel.\n     * Defaults to the autocomplete trigger element.\n     */\n    connectedTo;\n    /**\n     * `autocomplete` attribute to be set on the input element.\n     * @docs-private\n     */\n    autocompleteAttribute = 'off';\n    /**\n     * Whether the autocomplete is disabled. When disabled, the element will\n     * act as a regular input and the user won't be able to open the panel.\n     */\n    autocompleteDisabled;\n    constructor() { }\n    /** Class to apply to the panel when it's above the input. */\n    _aboveClass = 'mat-mdc-autocomplete-panel-above';\n    ngAfterViewInit() {\n        this._initialized.next();\n        this._initialized.complete();\n        this._cleanupWindowBlur = this._renderer.listen('window', 'blur', this._windowBlurHandler);\n    }\n    ngOnChanges(changes) {\n        if (changes['position'] && this._positionStrategy) {\n            this._setStrategyPositions(this._positionStrategy);\n            if (this.panelOpen) {\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._cleanupWindowBlur?.();\n        this._handsetLandscapeSubscription.unsubscribe();\n        this._viewportSubscription.unsubscribe();\n        this._componentDestroyed = true;\n        this._destroyPanel();\n        this._closeKeyEventStream.complete();\n        this._clearFromModal();\n    }\n    /** Whether or not the autocomplete panel is open. */\n    get panelOpen() {\n        return this._overlayAttached && this.autocomplete.showPanel;\n    }\n    _overlayAttached = false;\n    /** Opens the autocomplete suggestion panel. */\n    openPanel() {\n        this._openPanelInternal();\n    }\n    /** Closes the autocomplete suggestion panel. */\n    closePanel() {\n        this._resetLabel();\n        if (!this._overlayAttached) {\n            return;\n        }\n        if (this.panelOpen) {\n            // Only emit if the panel was visible.\n            // `afterNextRender` always runs outside of the Angular zone, so all the subscriptions from\n            // `_subscribeToClosingActions()` are also outside of the Angular zone.\n            // We should manually run in Angular zone to update UI after panel closing.\n            this._zone.run(() => {\n                this.autocomplete.closed.emit();\n            });\n        }\n        // Only reset if this trigger is the latest one that opened the\n        // autocomplete since another may have taken it over.\n        if (this.autocomplete._latestOpeningTrigger === this) {\n            this.autocomplete._isOpen = false;\n            this.autocomplete._latestOpeningTrigger = null;\n        }\n        this._overlayAttached = false;\n        this._pendingAutoselectedOption = null;\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n            this._closingActionsSubscription.unsubscribe();\n        }\n        this._updatePanelState();\n        // Note that in some cases this can end up being called after the component is destroyed.\n        // Add a check to ensure that we don't try to run change detection on a destroyed view.\n        if (!this._componentDestroyed) {\n            // We need to trigger change detection manually, because\n            // `fromEvent` doesn't seem to do it at the proper time.\n            // This ensures that the label is reset when the\n            // user clicks outside.\n            this._changeDetectorRef.detectChanges();\n        }\n        // Remove aria-owns attribute when the autocomplete is no longer visible.\n        if (this._trackedModal) {\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', this.autocomplete.id);\n        }\n    }\n    /**\n     * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n     * within the viewport.\n     */\n    updatePosition() {\n        if (this._overlayAttached) {\n            this._overlayRef.updatePosition();\n        }\n    }\n    /**\n     * A stream of actions that should close the autocomplete panel, including\n     * when an option is selected, on blur, and when TAB is pressed.\n     */\n    get panelClosingActions() {\n        return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef\n            ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached))\n            : of()).pipe(\n        // Normalize the output so we return a consistent type.\n        map(event => (event instanceof MatOptionSelectionChange ? event : null)));\n    }\n    /** Stream of changes to the selection state of the autocomplete options. */\n    optionSelections = defer(() => {\n        const options = this.autocomplete ? this.autocomplete.options : null;\n        if (options) {\n            return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n        }\n        // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n        // Return a stream that we'll replace with the real one once everything is in place.\n        return this._initialized.pipe(switchMap(() => this.optionSelections));\n    });\n    /** The currently active option, coerced to MatOption type. */\n    get activeOption() {\n        if (this.autocomplete && this.autocomplete._keyManager) {\n            return this.autocomplete._keyManager.activeItem;\n        }\n        return null;\n    }\n    /** Stream of clicks outside of the autocomplete panel. */\n    _getOutsideClickStream() {\n        return new Observable(observer => {\n            const listener = (event) => {\n                // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n                // fall back to check the first element in the path of the click event.\n                const clickTarget = _getEventTarget(event);\n                const formField = this._formField\n                    ? this._formField.getConnectedOverlayOrigin().nativeElement\n                    : null;\n                const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n                if (this._overlayAttached &&\n                    clickTarget !== this._element.nativeElement &&\n                    // Normally focus moves inside `mousedown` so this condition will almost always be\n                    // true. Its main purpose is to handle the case where the input is focused from an\n                    // outside click which propagates up to the `body` listener within the same sequence\n                    // and causes the panel to close immediately (see #3106).\n                    !this._hasFocus() &&\n                    (!formField || !formField.contains(clickTarget)) &&\n                    (!customOrigin || !customOrigin.contains(clickTarget)) &&\n                    !!this._overlayRef &&\n                    !this._overlayRef.overlayElement.contains(clickTarget)) {\n                    observer.next(event);\n                }\n            };\n            const cleanups = [\n                this._renderer.listen('document', 'click', listener),\n                this._renderer.listen('document', 'auxclick', listener),\n                this._renderer.listen('document', 'touchend', listener),\n            ];\n            return () => {\n                cleanups.forEach(current => current());\n            };\n        });\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        Promise.resolve(null).then(() => this._assignOptionValue(value));\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this._element.nativeElement.disabled = isDisabled;\n    }\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const hasModifier = hasModifierKey(event);\n        // Prevent the default action on all escape key presses. This is here primarily to bring IE\n        // in line with other browsers. By default, pressing escape on IE will cause it to revert\n        // the input value to the one that it had on focus, however it won't dispatch any events\n        // which means that the model value will be out of sync with the view.\n        if (keyCode === ESCAPE && !hasModifier) {\n            event.preventDefault();\n        }\n        this._valueOnLastKeydown = this._element.nativeElement.value;\n        if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n            this.activeOption._selectViaInteraction();\n            this._resetActiveItem();\n            event.preventDefault();\n        }\n        else if (this.autocomplete) {\n            const prevActiveItem = this.autocomplete._keyManager.activeItem;\n            const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n            if (keyCode === TAB || (isArrowKey && !hasModifier && this.panelOpen)) {\n                this.autocomplete._keyManager.onKeydown(event);\n            }\n            else if (isArrowKey && this._canOpen()) {\n                this._openPanelInternal(this._valueOnLastKeydown);\n            }\n            if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n                this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n                if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n                    if (!this._pendingAutoselectedOption) {\n                        this._valueBeforeAutoSelection = this._valueOnLastKeydown;\n                    }\n                    this._pendingAutoselectedOption = this.activeOption;\n                    this._assignOptionValue(this.activeOption.value);\n                }\n            }\n        }\n    }\n    _handleInput(event) {\n        let target = event.target;\n        let value = target.value;\n        // Based on `NumberValueAccessor` from forms.\n        if (target.type === 'number') {\n            value = value == '' ? null : parseFloat(value);\n        }\n        // If the input has a placeholder, IE will fire the `input` event on page load,\n        // focus and blur, in addition to when the user actually changed the value. To\n        // filter out all of the extra events, we save the value on focus and between\n        // `input` events, and we check whether it changed.\n        // See: https://connect.microsoft.com/IE/feedback/details/885747/\n        if (this._previousValue !== value) {\n            this._previousValue = value;\n            this._pendingAutoselectedOption = null;\n            // If selection is required we don't write to the CVA while the user is typing.\n            // At the end of the selection either the user will have picked something\n            // or we'll reset the value back to null.\n            if (!this.autocomplete || !this.autocomplete.requireSelection) {\n                this._onChange(value);\n            }\n            if (!value) {\n                this._clearPreviousSelectedOption(null, false);\n            }\n            else if (this.panelOpen && !this.autocomplete.requireSelection) {\n                // Note that we don't reset this when `requireSelection` is enabled,\n                // because the option will be reset when the panel is closed.\n                const selectedOption = this.autocomplete.options?.find(option => option.selected);\n                if (selectedOption) {\n                    const display = this._getDisplayValue(selectedOption.value);\n                    if (value !== display) {\n                        selectedOption.deselect(false);\n                    }\n                }\n            }\n            if (this._canOpen() && this._hasFocus()) {\n                // When the `input` event fires, the input's value will have already changed. This means\n                // that if we take the `this._element.nativeElement.value` directly, it'll be one keystroke\n                // behind. This can be a problem when the user selects a value, changes a character while\n                // the input still has focus and then clicks away (see #28432). To work around it, we\n                // capture the value in `keydown` so we can use it here.\n                const valueOnAttach = this._valueOnLastKeydown ?? this._element.nativeElement.value;\n                this._valueOnLastKeydown = null;\n                this._openPanelInternal(valueOnAttach);\n            }\n        }\n    }\n    _handleFocus() {\n        if (!this._canOpenOnNextFocus) {\n            this._canOpenOnNextFocus = true;\n        }\n        else if (this._canOpen()) {\n            this._previousValue = this._element.nativeElement.value;\n            this._attachOverlay(this._previousValue);\n            this._floatLabel(true);\n        }\n    }\n    _handleClick() {\n        if (this._canOpen() && !this.panelOpen) {\n            this._openPanelInternal();\n        }\n    }\n    /** Whether the input currently has focus. */\n    _hasFocus() {\n        return _getFocusedElementPierceShadowDom() === this._element.nativeElement;\n    }\n    /**\n     * In \"auto\" mode, the label will animate down as soon as focus is lost.\n     * This causes the value to jump when selecting an option with the mouse.\n     * This method manually floats the label until the panel can be closed.\n     * @param shouldAnimate Whether the label should be animated when it is floated.\n     */\n    _floatLabel(shouldAnimate = false) {\n        if (this._formField && this._formField.floatLabel === 'auto') {\n            if (shouldAnimate) {\n                this._formField._animateAndLockLabel();\n            }\n            else {\n                this._formField.floatLabel = 'always';\n            }\n            this._manuallyFloatingLabel = true;\n        }\n    }\n    /** If the label has been manually elevated, return it to its normal state. */\n    _resetLabel() {\n        if (this._manuallyFloatingLabel) {\n            if (this._formField) {\n                this._formField.floatLabel = 'auto';\n            }\n            this._manuallyFloatingLabel = false;\n        }\n    }\n    /**\n     * This method listens to a stream of panel closing actions and resets the\n     * stream every time the option list changes.\n     */\n    _subscribeToClosingActions() {\n        const initialRender = new Observable(subscriber => {\n            afterNextRender(() => {\n                subscriber.next();\n            }, { injector: this._environmentInjector });\n        });\n        const optionChanges = this.autocomplete.options?.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()), \n        // Defer emitting to the stream until the next tick, because changing\n        // bindings in here will cause \"changed after checked\" errors.\n        delay(0)) ?? of();\n        // When the options are initially rendered, and when the option list changes...\n        return (merge(initialRender, optionChanges)\n            .pipe(\n        // create a new stream of panelClosingActions, replacing any previous streams\n        // that were created, and flatten it so our stream only emits closing events...\n        switchMap(() => this._zone.run(() => {\n            // `afterNextRender` always runs outside of the Angular zone, thus we have to re-enter\n            // the Angular zone. This will lead to change detection being called outside of the Angular\n            // zone and the `autocomplete.opened` will also emit outside of the Angular.\n            const wasOpen = this.panelOpen;\n            this._resetActiveItem();\n            this._updatePanelState();\n            this._changeDetectorRef.detectChanges();\n            if (this.panelOpen) {\n                this._overlayRef.updatePosition();\n            }\n            if (wasOpen !== this.panelOpen) {\n                // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n                // `closed` event, because we may not have emitted it. This can happen\n                // - if the users opens the panel and there are no options, but the\n                //   options come in slightly later or as a result of the value changing,\n                // - if the panel is closed after the user entered a string that did not match any\n                //   of the available options,\n                // - if a valid string is entered after an invalid one.\n                if (this.panelOpen) {\n                    this._emitOpened();\n                }\n                else {\n                    this.autocomplete.closed.emit();\n                }\n            }\n            return this.panelClosingActions;\n        })), \n        // when the first closing event occurs...\n        take(1))\n            // set the value, close the panel, and complete.\n            .subscribe(event => this._setValueAndClose(event)));\n    }\n    /**\n     * Emits the opened event once it's known that the panel will be shown and stores\n     * the state of the trigger right before the opening sequence was finished.\n     */\n    _emitOpened() {\n        this.autocomplete.opened.emit();\n    }\n    /** Destroys the autocomplete suggestion panel. */\n    _destroyPanel() {\n        if (this._overlayRef) {\n            this.closePanel();\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n    }\n    /** Given a value, returns the string that should be shown within the input. */\n    _getDisplayValue(value) {\n        const autocomplete = this.autocomplete;\n        return autocomplete && autocomplete.displayWith ? autocomplete.displayWith(value) : value;\n    }\n    _assignOptionValue(value) {\n        const toDisplay = this._getDisplayValue(value);\n        if (value == null) {\n            this._clearPreviousSelectedOption(null, false);\n        }\n        // Simply falling back to an empty string if the display value is falsy does not work properly.\n        // The display value can also be the number zero and shouldn't fall back to an empty string.\n        this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n    }\n    _updateNativeInputValue(value) {\n        // If it's used within a `MatFormField`, we should set it through the property so it can go\n        // through change detection.\n        if (this._formField) {\n            this._formField._control.value = value;\n        }\n        else {\n            this._element.nativeElement.value = value;\n        }\n        this._previousValue = value;\n    }\n    /**\n     * This method closes the panel, and if a value is specified, also sets the associated\n     * control to that value. It will also mark the control as dirty if this interaction\n     * stemmed from the user.\n     */\n    _setValueAndClose(event) {\n        const panel = this.autocomplete;\n        const toSelect = event ? event.source : this._pendingAutoselectedOption;\n        if (toSelect) {\n            this._clearPreviousSelectedOption(toSelect);\n            this._assignOptionValue(toSelect.value);\n            // TODO(crisbeto): this should wait until the animation is done, otherwise the value\n            // gets reset while the panel is still animating which looks glitchy. It'll likely break\n            // some tests to change it at this point.\n            this._onChange(toSelect.value);\n            panel._emitSelectEvent(toSelect);\n            this._element.nativeElement.focus();\n        }\n        else if (panel.requireSelection &&\n            this._element.nativeElement.value !== this._valueOnAttach) {\n            this._clearPreviousSelectedOption(null);\n            this._assignOptionValue(null);\n            this._onChange(null);\n        }\n        this.closePanel();\n    }\n    /**\n     * Clear any previous selected option and emit a selection change event for this option\n     */\n    _clearPreviousSelectedOption(skip, emitEvent) {\n        // Null checks are necessary here, because the autocomplete\n        // or its options may not have been assigned yet.\n        this.autocomplete?.options?.forEach(option => {\n            if (option !== skip && option.selected) {\n                option.deselect(emitEvent);\n            }\n        });\n    }\n    _openPanelInternal(valueOnAttach = this._element.nativeElement.value) {\n        this._attachOverlay(valueOnAttach);\n        this._floatLabel();\n        // Add aria-owns attribute when the autocomplete becomes visible.\n        if (this._trackedModal) {\n            const panelId = this.autocomplete.id;\n            addAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n    }\n    _attachOverlay(valueOnAttach) {\n        if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatAutocompleteMissingPanelError();\n        }\n        let overlayRef = this._overlayRef;\n        if (!overlayRef) {\n            this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n                id: this._formField?.getLabelId(),\n            });\n            overlayRef = this._overlay.create(this._getOverlayConfig());\n            this._overlayRef = overlayRef;\n            this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n                if (this.panelOpen && overlayRef) {\n                    overlayRef.updateSize({ width: this._getPanelWidth() });\n                }\n            });\n            // Subscribe to the breakpoint events stream to detect when screen is in\n            // handsetLandscape.\n            this._handsetLandscapeSubscription = this._breakpointObserver\n                .observe(Breakpoints.HandsetLandscape)\n                .subscribe(result => {\n                const isHandsetLandscape = result.matches;\n                // Check if result.matches Breakpoints.HandsetLandscape. Apply HandsetLandscape\n                // settings to prevent overlay cutoff in that breakpoint. Fixes b/284148377\n                if (isHandsetLandscape) {\n                    this._positionStrategy\n                        .withFlexibleDimensions(true)\n                        .withGrowAfterOpen(true)\n                        .withViewportMargin(8);\n                }\n                else {\n                    this._positionStrategy\n                        .withFlexibleDimensions(false)\n                        .withGrowAfterOpen(false)\n                        .withViewportMargin(0);\n                }\n            });\n        }\n        else {\n            // Update the trigger, panel width and direction, in case anything has changed.\n            this._positionStrategy.setOrigin(this._getConnectedElement());\n            overlayRef.updateSize({ width: this._getPanelWidth() });\n        }\n        if (overlayRef && !overlayRef.hasAttached()) {\n            overlayRef.attach(this._portal);\n            this._valueOnAttach = valueOnAttach;\n            this._valueOnLastKeydown = null;\n            this._closingActionsSubscription = this._subscribeToClosingActions();\n        }\n        const wasOpen = this.panelOpen;\n        this.autocomplete._isOpen = this._overlayAttached = true;\n        this.autocomplete._latestOpeningTrigger = this;\n        this.autocomplete._setColor(this._formField?.color);\n        this._updatePanelState();\n        this._applyModalPanelOwnership();\n        // We need to do an extra `panelOpen` check in here, because the\n        // autocomplete won't be shown if there are no options.\n        if (this.panelOpen && wasOpen !== this.panelOpen) {\n            this._emitOpened();\n        }\n    }\n    /** Handles keyboard events coming from the overlay panel. */\n    _handlePanelKeydown = (event) => {\n        // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n        // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n        if ((event.keyCode === ESCAPE && !hasModifierKey(event)) ||\n            (event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey'))) {\n            // If the user had typed something in before we autoselected an option, and they decided\n            // to cancel the selection, restore the input value to the one they had typed in.\n            if (this._pendingAutoselectedOption) {\n                this._updateNativeInputValue(this._valueBeforeAutoSelection ?? '');\n                this._pendingAutoselectedOption = null;\n            }\n            this._closeKeyEventStream.next();\n            this._resetActiveItem();\n            // We need to stop propagation, otherwise the event will eventually\n            // reach the input itself and cause the overlay to be reopened.\n            event.stopPropagation();\n            event.preventDefault();\n        }\n    };\n    /** Updates the panel's visibility state and any trigger state tied to id. */\n    _updatePanelState() {\n        this.autocomplete._setVisibility();\n        // Note that here we subscribe and unsubscribe based on the panel's visiblity state,\n        // because the act of subscribing will prevent events from reaching other overlays and\n        // we don't want to block the events if there are no options.\n        if (this.panelOpen) {\n            const overlayRef = this._overlayRef;\n            if (!this._keydownSubscription) {\n                // Use the `keydownEvents` in order to take advantage of\n                // the overlay event targeting provided by the CDK overlay.\n                this._keydownSubscription = overlayRef.keydownEvents().subscribe(this._handlePanelKeydown);\n            }\n            if (!this._outsideClickSubscription) {\n                // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n                // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n                // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n                this._outsideClickSubscription = overlayRef.outsidePointerEvents().subscribe();\n            }\n        }\n        else {\n            this._keydownSubscription?.unsubscribe();\n            this._outsideClickSubscription?.unsubscribe();\n            this._keydownSubscription = this._outsideClickSubscription = null;\n        }\n    }\n    _getOverlayConfig() {\n        return new OverlayConfig({\n            positionStrategy: this._getOverlayPosition(),\n            scrollStrategy: this._scrollStrategy(),\n            width: this._getPanelWidth(),\n            direction: this._dir ?? undefined,\n            hasBackdrop: this._defaults?.hasBackdrop,\n            backdropClass: this._defaults?.backdropClass,\n            panelClass: this._defaults?.overlayPanelClass,\n        });\n    }\n    _getOverlayPosition() {\n        // Set default Overlay Position\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this._getConnectedElement())\n            .withFlexibleDimensions(false)\n            .withPush(false);\n        this._setStrategyPositions(strategy);\n        this._positionStrategy = strategy;\n        return strategy;\n    }\n    /** Sets the positions on a position strategy based on the directive's input state. */\n    _setStrategyPositions(positionStrategy) {\n        // Note that we provide horizontal fallback positions, even though by default the dropdown\n        // width matches the input, because consumers can override the width. See #18854.\n        const belowPositions = [\n            { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n            { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n        ];\n        // The overlay edge connected to the trigger should have squared corners, while\n        // the opposite end has rounded corners. We apply a CSS class to swap the\n        // border-radius based on the overlay position.\n        const panelClass = this._aboveClass;\n        const abovePositions = [\n            { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom', panelClass },\n            { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom', panelClass },\n        ];\n        let positions;\n        if (this.position === 'above') {\n            positions = abovePositions;\n        }\n        else if (this.position === 'below') {\n            positions = belowPositions;\n        }\n        else {\n            positions = [...belowPositions, ...abovePositions];\n        }\n        positionStrategy.withPositions(positions);\n    }\n    _getConnectedElement() {\n        if (this.connectedTo) {\n            return this.connectedTo.elementRef;\n        }\n        return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n    }\n    _getPanelWidth() {\n        return this.autocomplete.panelWidth || this._getHostWidth();\n    }\n    /** Returns the width of the input element, so the panel width can match it. */\n    _getHostWidth() {\n        return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n    }\n    /**\n     * Reset the active item to -1. This is so that pressing arrow keys will activate the correct\n     * option.\n     *\n     * If the consumer opted-in to automatically activatating the first option, activate the first\n     * *enabled* option.\n     */\n    _resetActiveItem() {\n        const autocomplete = this.autocomplete;\n        if (autocomplete.autoActiveFirstOption) {\n            // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n            // because it activates the first option that passes the skip predicate, rather than the\n            // first *enabled* option.\n            let firstEnabledOptionIndex = -1;\n            for (let index = 0; index < autocomplete.options.length; index++) {\n                const option = autocomplete.options.get(index);\n                if (!option.disabled) {\n                    firstEnabledOptionIndex = index;\n                    break;\n                }\n            }\n            autocomplete._keyManager.setActiveItem(firstEnabledOptionIndex);\n        }\n        else {\n            autocomplete._keyManager.setActiveItem(-1);\n        }\n    }\n    /** Determines whether the panel can be opened. */\n    _canOpen() {\n        const element = this._element.nativeElement;\n        return !element.readOnly && !element.disabled && !this.autocompleteDisabled;\n    }\n    /** Scrolls to a particular option in the list. */\n    _scrollToOption(index) {\n        // Given that we are not actually focusing active options, we must manually adjust scroll\n        // to reveal options below the fold. First, we find the offset of the option from the top\n        // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n        // the panel height + the option height, so the active option will be just visible at the\n        // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n        // will become the offset. If that offset is visible within the panel already, the scrollTop is\n        // not adjusted.\n        const autocomplete = this.autocomplete;\n        const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n        if (index === 0 && labelCount === 1) {\n            // If we've got one group label before the option and we're at the top option,\n            // scroll the list to the top. This is better UX than scrolling the list to the\n            // top of the option, because it allows the user to read the top group's label.\n            autocomplete._setScrollTop(0);\n        }\n        else if (autocomplete.panel) {\n            const option = autocomplete.options.toArray()[index];\n            if (option) {\n                const element = option._getHostElement();\n                const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n                autocomplete._setScrollTop(newScrollPosition);\n            }\n        }\n    }\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    _trackedModal = null;\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modal = this._element.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        if (!modal) {\n            // Most commonly, the autocomplete trigger is not inside a modal.\n            return;\n        }\n        const panelId = this.autocomplete.id;\n        if (this._trackedModal) {\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n        addAriaReferencedId(modal, 'aria-owns', panelId);\n        this._trackedModal = modal;\n    }\n    /** Clears the references to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n        if (this._trackedModal) {\n            const panelId = this.autocomplete.id;\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n            this._trackedModal = null;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAutocompleteTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatAutocompleteTrigger, isStandalone: true, selector: \"input[matAutocomplete], textarea[matAutocomplete]\", inputs: { autocomplete: [\"matAutocomplete\", \"autocomplete\"], position: [\"matAutocompletePosition\", \"position\"], connectedTo: [\"matAutocompleteConnectedTo\", \"connectedTo\"], autocompleteAttribute: [\"autocomplete\", \"autocompleteAttribute\"], autocompleteDisabled: [\"matAutocompleteDisabled\", \"autocompleteDisabled\", booleanAttribute] }, host: { listeners: { \"focusin\": \"_handleFocus()\", \"blur\": \"_onTouched()\", \"input\": \"_handleInput($event)\", \"keydown\": \"_handleKeydown($event)\", \"click\": \"_handleClick()\" }, properties: { \"attr.autocomplete\": \"autocompleteAttribute\", \"attr.role\": \"autocompleteDisabled ? null : \\\"combobox\\\"\", \"attr.aria-autocomplete\": \"autocompleteDisabled ? null : \\\"list\\\"\", \"attr.aria-activedescendant\": \"(panelOpen && activeOption) ? activeOption.id : null\", \"attr.aria-expanded\": \"autocompleteDisabled ? null : panelOpen.toString()\", \"attr.aria-controls\": \"(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id\", \"attr.aria-haspopup\": \"autocompleteDisabled ? null : \\\"listbox\\\"\" }, classAttribute: \"mat-mdc-autocomplete-trigger\" }, providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR], exportAs: [\"matAutocompleteTrigger\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAutocompleteTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n                    host: {\n                        'class': 'mat-mdc-autocomplete-trigger',\n                        '[attr.autocomplete]': 'autocompleteAttribute',\n                        '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n                        '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n                        '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n                        '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n                        '[attr.aria-controls]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n                        '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n                        // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n                        // a little earlier. This avoids issues where IE delays the focusing of the input.\n                        '(focusin)': '_handleFocus()',\n                        '(blur)': '_onTouched()',\n                        '(input)': '_handleInput($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(click)': '_handleClick()',\n                    },\n                    exportAs: 'matAutocompleteTrigger',\n                    providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR],\n                }]\n        }], ctorParameters: () => [], propDecorators: { autocomplete: [{\n                type: Input,\n                args: ['matAutocomplete']\n            }], position: [{\n                type: Input,\n                args: ['matAutocompletePosition']\n            }], connectedTo: [{\n                type: Input,\n                args: ['matAutocompleteConnectedTo']\n            }], autocompleteAttribute: [{\n                type: Input,\n                args: ['autocomplete']\n            }], autocompleteDisabled: [{\n                type: Input,\n                args: [{ alias: 'matAutocompleteDisabled', transform: booleanAttribute }]\n            }] } });\n\nclass MatAutocompleteModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAutocompleteModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAutocompleteModule, imports: [OverlayModule,\n            MatOptionModule,\n            MatCommonModule,\n            MatAutocomplete,\n            MatAutocompleteTrigger,\n            MatAutocompleteOrigin], exports: [CdkScrollableModule,\n            MatAutocomplete,\n            MatOptionModule,\n            MatCommonModule,\n            MatAutocompleteTrigger,\n            MatAutocompleteOrigin] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAutocompleteModule, providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [OverlayModule,\n            MatOptionModule,\n            MatCommonModule, CdkScrollableModule,\n            MatOptionModule,\n            MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatAutocompleteModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        OverlayModule,\n                        MatOptionModule,\n                        MatCommonModule,\n                        MatAutocomplete,\n                        MatAutocompleteTrigger,\n                        MatAutocompleteOrigin,\n                    ],\n                    exports: [\n                        CdkScrollableModule,\n                        MatAutocomplete,\n                        MatOptionModule,\n                        MatCommonModule,\n                        MatAutocompleteTrigger,\n                        MatAutocompleteOrigin,\n                    ],\n                    providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, MatOption, getMatAutocompleteMissingPanelError };\n"], "mappings": ";;;;;;IAmPiFA,EAAE,CAAAC,cAAA,eAFu/D,CAAC;IAE1/DD,EAAE,CAAAE,YAAA,EAFshE,CAAC;IAEzhEF,EAAE,CAAAG,YAAA,CAFgiE,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAC,cAAA,GAAAC,GAAA,CAAAC,EAAA;IAAA,MAAAC,MAAA,GAEniER,EAAE,CAAAS,aAAA;IAAFT,EAAE,CAAAU,UAAA,CAAAF,MAAA,CAAAG,UAF0iD,CAAC;IAE7iDX,EAAE,CAAAY,WAAA,iCAAAJ,MAAA,CAAAK,SAFkmD,CAAC,iCAAAL,MAAA,CAAAK,SAAuD,CAAC,+CAAAL,MAAA,CAAAM,mBAA+E,CAAC,gBAAAN,MAAA,CAAAO,MAAA,cAAiD,CAAC,eAAAP,MAAA,CAAAO,MAAA,aAA+C,CAAC,aAAAP,MAAA,CAAAO,MAAA,WAA2C,CAAC;IAE33Df,EAAE,CAAAgB,UAAA,OAAAR,MAAA,CAAAD,EAF8gD,CAAC;IAEjhDP,EAAE,CAAAiB,WAAA,eAAAT,MAAA,CAAAU,SAAA,6BAAAV,MAAA,CAAAW,uBAAA,CAAAd,cAAA;EAAA;AAAA;AAnPnF,SAASe,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,6BAA6B,EAAEC,CAAC,IAAIC,wBAAwB,QAAQ,uBAAuB;AAC7M,SAASC,CAAC,IAAIC,WAAW,QAAQ,uBAAuB;AACxD,OAAO,KAAKjC,EAAE,MAAM,eAAe;AACnC,SAASkC,cAAc,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,eAAe,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACvW,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC3E,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AAC5E,SAASC,YAAY,EAAEC,0BAA0B,EAAEC,sBAAsB,EAAEC,mBAAmB,QAAQ,mBAAmB;AACzH,SAASC,QAAQ,EAAEC,iCAAiC,EAAEC,eAAe,QAAQ,uBAAuB;AACpG,SAASC,YAAY,EAAEC,OAAO,EAAEC,KAAK,EAAEC,EAAE,EAAEC,KAAK,EAAEC,UAAU,QAAQ,MAAM;AAC1E,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,cAAc,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,uBAAuB;AAChG,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,qBAAqB;AACrE,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAEC,IAAI,QAAQ,gBAAgB;AACpF,SAASC,CAAC,IAAIC,cAAc,QAAQ,2BAA2B;AAC/D,SAASxE,CAAC,IAAIyE,eAAe,QAAQ,sBAAsB;AAC3D,SAASzE,CAAC,IAAI0E,eAAe,QAAQ,8BAA8B;AACnE,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,sBAAsB;AAC7B,OAAO,gCAAgC;AACvC,OAAO,kCAAkC;AACzC,OAAO,iBAAiB;AACxB,OAAO,gCAAgC;AACvC,OAAO,sBAAsB;AAC7B,OAAO,uCAAuC;;AAE9C;AACA,MAAMC,4BAA4B,CAAC;EAG/BC,WAAWA,CACX;EACAC,MAAM,EACN;EACAC,MAAM,EAAE;IAAAC,eAAA;IAAAA,eAAA;IACJ,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA;AACA,MAAME,gCAAgC,GAAG,IAAIpE,cAAc,CAAC,kCAAkC,EAAE;EAC5FqE,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,wCAAwCA,CAAA,EAAG;EAChD,OAAO;IACHC,qBAAqB,EAAE,KAAK;IAC5BC,sBAAsB,EAAE,KAAK;IAC7BC,4BAA4B,EAAE,KAAK;IACnCC,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE;EACjB,CAAC;AACL;AACA;AACA,MAAMC,eAAe,CAAC;EAUlB;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO,IAAI,IAAI,CAACpG,SAAS;EACzC;EAIA;EACAqG,SAASA,CAACC,KAAK,EAAE;IACb,IAAI,CAACpG,MAAM,GAAGoG,KAAK;IACnB,IAAI,CAACC,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;;EAgDA;AACJ;AACA;AACA;EACI,IAAIC,SAASA,CAACH,KAAK,EAAE;IACjB,IAAI,CAACxG,UAAU,GAAGwG,KAAK;IACvB,IAAI,CAACI,WAAW,CAACC,aAAa,CAACC,SAAS,GAAG,EAAE;EACjD;EAEA;EACA,IAAIb,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACc,6BAA6B;EAC7C;EACA,IAAId,4BAA4BA,CAACO,KAAK,EAAE;IACpC,IAAI,CAACO,6BAA6B,GAAGP,KAAK;IAC1C,IAAI,CAACQ,qBAAqB,CAAC,CAAC;EAChC;EAEA;EACAA,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,KAAK,MAAMxB,MAAM,IAAI,IAAI,CAACwB,OAAO,EAAE;QAC/BxB,MAAM,CAACgB,kBAAkB,CAACC,YAAY,CAAC,CAAC;MAC5C;IACJ;EACJ;EACA;;EAOAnB,WAAWA,CAAA,EAAG;IAAA,IAAA2B,qBAAA;IAAAxB,eAAA,6BAtGOlE,MAAM,CAACC,iBAAiB,CAAC;IAAAiE,eAAA,sBAChClE,MAAM,CAACE,UAAU,CAAC;IAAAgE,eAAA,oBACpBlE,MAAM,CAACmE,gCAAgC,CAAC;IAAAD,eAAA,8BAC9BlE,MAAM,CAACG,qBAAqB,EAAE;MAAEwF,QAAQ,EAAE;IAAK,CAAC,CAAC,KAAK,gBAAgB;IAAAzB,eAAA,+BACrEhC,YAAY,CAAC0D,KAAK;IACzC;IAAA1B,eAAA;IAEA;IAAAA,eAAA,oBACY,KAAK;IAAAA,eAAA,kBAKP,KAAK;IACf;IAAAA,eAAA;IAAAA,eAAA;IASA;IACA;IACA;IACA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,sBACc,IAAI;IAClB;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAOA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA,yBACiB,IAAI9D,YAAY,CAAC,CAAC;IACnC;IAAA8D,eAAA,iBACS,IAAI9D,YAAY,CAAC,CAAC;IAC3B;IAAA8D,eAAA,iBACS,IAAI9D,YAAY,CAAC,CAAC;IAC3B;IAAA8D,eAAA,0BACkB,IAAI9D,YAAY,CAAC,CAAC;IAAA8D,eAAA;IAAAA,eAAA;IAAAA,eAAA,aA4B/BlE,MAAM,CAAC2B,YAAY,CAAC,CAACkE,KAAK,CAAC,mBAAmB,CAAC;IACpD;AACJ;AACA;AACA;IAHI3B,eAAA;IAMI,MAAM4B,QAAQ,GAAG9F,MAAM,CAAC+B,QAAQ,CAAC;IACjC;IACA;IACA;IACA;IACA,IAAI,CAACgE,WAAW,GAAG,CAAAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,KAAI,KAAK;IAC5C,IAAI,CAACzB,qBAAqB,GAAG,CAAC,CAAC,IAAI,CAAC0B,SAAS,CAAC1B,qBAAqB;IACnE,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAACyB,SAAS,CAACzB,sBAAsB;IACrE,IAAI,CAACE,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAACuB,SAAS,CAACvB,gBAAgB;IACzD,IAAI,CAACa,6BAA6B,IAAAG,qBAAA,GAAG,IAAI,CAACO,SAAS,CAACxB,4BAA4B,cAAAiB,qBAAA,cAAAA,qBAAA,GAAI,KAAK;EAC7F;EACAQ,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,WAAW,GAAG,IAAIvE,0BAA0B,CAAC,IAAI,CAAC6D,OAAO,CAAC,CAC1DW,QAAQ,CAAC,CAAC,CACVC,aAAa,CAAC,IAAI,CAACC,cAAc,CAAC;IACvC,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACJ,WAAW,CAACK,MAAM,CAACC,SAAS,CAACC,KAAK,IAAI;MACnE,IAAI,IAAI,CAAC7B,MAAM,EAAE;QACb,IAAI,CAAC8B,eAAe,CAACC,IAAI,CAAC;UAAE5C,MAAM,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI,CAACwB,OAAO,CAACoB,OAAO,CAAC,CAAC,CAACH,KAAK,CAAC,IAAI;QAAK,CAAC,CAAC;MAC9F;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACI,cAAc,CAAC,CAAC;EACzB;EACAC,WAAWA,CAAA,EAAG;IAAA,IAAAC,iBAAA;IACV,CAAAA,iBAAA,OAAI,CAACb,WAAW,cAAAa,iBAAA,eAAhBA,iBAAA,CAAkBC,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACV,oBAAoB,CAACW,WAAW,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAAChC,aAAa,CAAC+B,SAAS,GAAGA,SAAS;IAClD;EACJ;EACA;EACAE,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,KAAK,GAAG,IAAI,CAACA,KAAK,CAAChC,aAAa,CAAC+B,SAAS,GAAG,CAAC;EAC9D;EACA;EACAN,cAAcA,CAAA,EAAG;IAAA,IAAAS,aAAA;IACb,IAAI,CAAC7I,SAAS,GAAG,CAAC,GAAA6I,aAAA,GAAC,IAAI,CAAC9B,OAAO,cAAA8B,aAAA,eAAZA,aAAA,CAAcC,MAAM;IACvC,IAAI,CAACvC,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACAuC,gBAAgBA,CAACxD,MAAM,EAAE;IACrB,MAAMyD,KAAK,GAAG,IAAI5D,4BAA4B,CAAC,IAAI,EAAEG,MAAM,CAAC;IAC5D,IAAI,CAAC0D,cAAc,CAACf,IAAI,CAACc,KAAK,CAAC;EACnC;EACA;EACA1I,uBAAuBA,CAAC4I,OAAO,EAAE;IAC7B,IAAI,IAAI,CAAC7I,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAM8I,eAAe,GAAGD,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE;IACpD,OAAO,IAAI,CAACE,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACC,cAAc,GAAGF,OAAO;EAChF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAtB,cAAcA,CAAA,EAAG;IACb,OAAO,KAAK;EAChB;AAGJ;AAACyB,gBAAA,GArLKnD,eAAe;AAAAV,eAAA,CAAfU,eAAe,wBAAAoD,yBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAmLkFrD,gBAAe;AAAA;AAAAV,eAAA,CAnLhHU,eAAe,8BAsL4D/G,EAAE,CAAAqK,iBAAA;EAAAC,IAAA,EAFQvD,gBAAe;EAAAwD,SAAA;EAAAC,cAAA,WAAAC,gCAAArK,EAAA,EAAAE,GAAA,EAAAoK,QAAA;IAAA,IAAAtK,EAAA;MAEzBJ,EAAE,CAAA2K,cAAA,CAAAD,QAAA,EAF+/BnJ,SAAS;MAE1gCvB,EAAE,CAAA2K,cAAA,CAAAD,QAAA,EAF0kCjJ,YAAY;IAAA;IAAA,IAAArB,EAAA;MAAA,IAAAwK,EAAA;MAExlC5K,EAAE,CAAA6K,cAAA,CAAAD,EAAA,GAAF5K,EAAE,CAAA8K,WAAA,QAAAxK,GAAA,CAAAsH,OAAA,GAAAgD,EAAA;MAAF5K,EAAE,CAAA6K,cAAA,CAAAD,EAAA,GAAF5K,EAAE,CAAA8K,WAAA,QAAAxK,GAAA,CAAAyK,YAAA,GAAAH,EAAA;IAAA;EAAA;EAAAI,SAAA,WAAAC,uBAAA7K,EAAA,EAAAE,GAAA;IAAA,IAAAF,EAAA;MAAFJ,EAAE,CAAAkL,WAAA,CAFgrCzI,WAAW;MAE7rCzC,EAAE,CAAAkL,WAAA,CAAAC,GAAA;IAAA;IAAA,IAAA/K,EAAA;MAAA,IAAAwK,EAAA;MAAF5K,EAAE,CAAA6K,cAAA,CAAAD,EAAA,GAAF5K,EAAE,CAAA8K,WAAA,QAAAxK,GAAA,CAAA8K,QAAA,GAAAR,EAAA,CAAAS,KAAA;MAAFrL,EAAE,CAAA6K,cAAA,CAAAD,EAAA,GAAF5K,EAAE,CAAA8K,WAAA,QAAAxK,GAAA,CAAAkJ,KAAA,GAAAoB,EAAA,CAAAS,KAAA;IAAA;EAAA;EAAAC,SAAA;EAAAC,MAAA;IAAArK,SAAA;IAAA+I,cAAA;IAAAuB,WAAA;IAAA9E,qBAAA,wDAF0RlE,gBAAgB;IAAAmE,sBAAA,0DAAgFnE,gBAAgB;IAAAqE,gBAAA,8CAA8DrE,gBAAgB;IAAAiJ,UAAA;IAAAC,aAAA,wCAA+ElJ,gBAAgB;IAAA8E,SAAA;IAAAV,4BAAA,sEAAqIpE,gBAAgB;EAAA;EAAAmJ,OAAA;IAAA7B,cAAA;IAAA8B,MAAA;IAAAC,MAAA;IAAA/C,eAAA;EAAA;EAAAgD,QAAA;EAAAC,QAAA,GAE9sB/L,EAAE,CAAAgM,kBAAA,CAFq4B,CAAC;IAAEC,OAAO,EAAE5K,2BAA2B;IAAE6K,WAAW,EAAEnF;EAAgB,CAAC,CAAC;EAAAoF,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAnB,QAAA,WAAAoB,0BAAApM,EAAA,EAAAE,GAAA;IAAA,IAAAF,EAAA;MAE/8BJ,EAAE,CAAAyM,eAAA;MAAFzM,EAAE,CAAA0M,UAAA,IAAAC,uCAAA,sBAF64C,CAAC;IAAA;EAAA;EAAAC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAEj+C;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF/M,EAAE,CAAAgN,iBAAA,CAAQjG,eAAe,EAAc,CAAC;IAC7GuD,IAAI,EAAE5H,SAAS;IACfuK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEL,aAAa,EAAElK,iBAAiB,CAACwK,IAAI;MAAEL,eAAe,EAAElK,uBAAuB,CAACwK,MAAM;MAAEtB,QAAQ,EAAE,iBAAiB;MAAEuB,IAAI,EAAE;QACtJ,OAAO,EAAE;MACb,CAAC;MAAEC,SAAS,EAAE,CAAC;QAAErB,OAAO,EAAE5K,2BAA2B;QAAE6K,WAAW,EAAEnF;MAAgB,CAAC,CAAC;MAAEqE,QAAQ,EAAE,2sBAA2sB;MAAEwB,MAAM,EAAE,CAAC,wyCAAwyC;IAAE,CAAC;EAC/mE,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAExB,QAAQ,EAAE,CAAC;MACnDd,IAAI,EAAEzH,SAAS;MACfoK,IAAI,EAAE,CAACxK,WAAW,EAAE;QAAE8K,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAE/D,KAAK,EAAE,CAAC;MACRc,IAAI,EAAEzH,SAAS;MACfoK,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAErF,OAAO,EAAE,CAAC;MACV0C,IAAI,EAAExH,eAAe;MACrBmK,IAAI,EAAE,CAAC1L,SAAS,EAAE;QAAEiM,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEzC,YAAY,EAAE,CAAC;MACfT,IAAI,EAAExH,eAAe;MACrBmK,IAAI,EAAE,CAACxL,YAAY,EAAE;QAAE+L,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEtM,SAAS,EAAE,CAAC;MACZoJ,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEhD,cAAc,EAAE,CAAC;MACjBK,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEzB,WAAW,EAAE,CAAC;MACdlB,IAAI,EAAEvH;IACV,CAAC,CAAC;IAAE2D,qBAAqB,EAAE,CAAC;MACxB4D,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEjL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmE,sBAAsB,EAAE,CAAC;MACzB2D,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEjL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqE,gBAAgB,EAAE,CAAC;MACnByD,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEjL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiJ,UAAU,EAAE,CAAC;MACbnB,IAAI,EAAEvH;IACV,CAAC,CAAC;IAAE2I,aAAa,EAAE,CAAC;MAChBpB,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEjL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsH,cAAc,EAAE,CAAC;MACjBQ,IAAI,EAAEtH;IACV,CAAC,CAAC;IAAE4I,MAAM,EAAE,CAAC;MACTtB,IAAI,EAAEtH;IACV,CAAC,CAAC;IAAE6I,MAAM,EAAE,CAAC;MACTvB,IAAI,EAAEtH;IACV,CAAC,CAAC;IAAE8F,eAAe,EAAE,CAAC;MAClBwB,IAAI,EAAEtH;IACV,CAAC,CAAC;IAAEsE,SAAS,EAAE,CAAC;MACZgD,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAErG,4BAA4B,EAAE,CAAC;MAC/B0D,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEjL;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMkL,qBAAqB,CAAC;EAExBxH,WAAWA,CAAA,EAAG;IAAAG,eAAA,qBADDlE,MAAM,CAACE,UAAU,CAAC;EACf;AAGpB;AAACsL,sBAAA,GALKD,qBAAqB;AAAArH,eAAA,CAArBqH,qBAAqB,wBAAAE,+BAAAxD,iBAAA;EAAA,YAAAA,iBAAA,IAG4EsD,sBAAqB;AAAA;AAAArH,eAAA,CAHtHqH,qBAAqB,8BA3DsD1N,EAAE,CAAA6N,iBAAA;EAAAvD,IAAA,EA+DQoD,sBAAqB;EAAAnD,SAAA;EAAAuB,QAAA;AAAA;AAEhH;EAAA,QAAAiB,SAAA,oBAAAA,SAAA,KAjEiF/M,EAAE,CAAAgN,iBAAA,CAiEQU,qBAAqB,EAAc,CAAC;IACnHpD,IAAI,EAAErH,SAAS;IACfgK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,yBAAyB;MACnCpB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA,MAAMgC,+BAA+B,GAAG;EACpC7B,OAAO,EAAE5G,iBAAiB;EAC1B6G,WAAW,EAAEhJ,UAAU,CAAC,MAAM6K,sBAAsB,CAAC;EACrDC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,SAASC,mCAAmCA,CAAA,EAAG;EAC3C,OAAOC,KAAK,CAAC,kEAAkE,GAC3E,4EAA4E,GAC5E,iEAAiE,CAAC;AAC1E;AACA;AACA,MAAMC,gCAAgC,GAAG,IAAIjM,cAAc,CAAC,kCAAkC,EAAE;EAC5FqE,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAM4H,OAAO,GAAGjM,MAAM,CAACwB,OAAO,CAAC;IAC/B,OAAO,MAAMyK,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;EACtD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASC,wCAAwCA,CAACH,OAAO,EAAE;EACvD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,iDAAiD,GAAG;EACtDvC,OAAO,EAAEkC,gCAAgC;EACzCM,IAAI,EAAE,CAAC9K,OAAO,CAAC;EACf+K,UAAU,EAAEH;AAChB,CAAC;AACD;AACA,MAAMR,sBAAsB,CAAC;EA2FzB7H,WAAWA,CAAA,EAAG;IAAAG,eAAA,+BA1FSlE,MAAM,CAACgB,mBAAmB,CAAC;IAAAkD,eAAA,mBACvClE,MAAM,CAACE,UAAU,CAAC;IAAAgE,eAAA,mBAClBlE,MAAM,CAACwB,OAAO,CAAC;IAAA0C,eAAA,4BACNlE,MAAM,CAACiB,gBAAgB,CAAC;IAAAiD,eAAA,gBACpClE,MAAM,CAACkB,MAAM,CAAC;IAAAgD,eAAA,6BACDlE,MAAM,CAACC,iBAAiB,CAAC;IAAAiE,eAAA,eACvClE,MAAM,CAACwC,cAAc,EAAE;MAAEmD,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAzB,eAAA,qBACpClE,MAAM,CAAC2D,cAAc,EAAE;MAAEgC,QAAQ,EAAE,IAAI;MAAEuF,IAAI,EAAE;IAAK,CAAC,CAAC;IAAAhH,eAAA,yBAClDlE,MAAM,CAACsB,aAAa,CAAC;IAAA4C,eAAA,0BACpBlE,MAAM,CAACgM,gCAAgC,CAAC;IAAA9H,eAAA,oBAC9ClE,MAAM,CAACmB,SAAS,CAAC;IAAA+C,eAAA,oBACjBlE,MAAM,CAACmE,gCAAgC,EAAE;MAAEwB,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAzB,eAAA;IAAAA,eAAA;IAAAA,eAAA,8BAGlD,KAAK;IAAAA,eAAA,uBACZ,IAAI/B,OAAO,CAAC,CAAC;IAAA+B,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAI5B;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,iCACyB,KAAK;IAC9B;IAAAA,eAAA;IAEA;IAAAA,eAAA,gCACwBhC,YAAY,CAAC0D,KAAK;IAC1C;IAAA1B,eAAA,8BACsBlE,MAAM,CAAC+C,kBAAkB,CAAC;IAAAmB,eAAA,wCAChBhC,YAAY,CAAC0D,KAAK;IAClD;AACJ;AACA;AACA;AACA;IAJI1B,eAAA,8BAKsB,IAAI;IAC1B;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA,+BACuB,IAAI/B,OAAO,CAAC,CAAC;IACpC;AACJ;AACA;AACA;IAHI+B,eAAA,6BAIqB,MAAM;MACvB;MACA;MACA;MACA,IAAI,CAACsI,mBAAmB,GAAG,IAAI,CAACC,SAAS,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC;IAClE,CAAC;IACD;IAAAxI,eAAA,oBACY,MAAM,CAAE,CAAC;IACrB;IAAAA,eAAA,qBACa,MAAM,CAAE,CAAC;IACtB;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA,mBAOW,MAAM;IACjB;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA,gCAIwB,KAAK;IAC7B;AACJ;AACA;AACA;IAHIA,eAAA;IAMA;IAAAA,eAAA,sBACc,kCAAkC;IAAAA,eAAA,2BA2B7B,KAAK;IAmExB;IAAAA,eAAA,2BACmB5B,KAAK,CAAC,MAAM;MAC3B,MAAMmD,OAAO,GAAG,IAAI,CAACkH,YAAY,GAAG,IAAI,CAACA,YAAY,CAAClH,OAAO,GAAG,IAAI;MACpE,IAAIA,OAAO,EAAE;QACT,OAAOA,OAAO,CAACmH,OAAO,CAACC,IAAI,CAACxJ,SAAS,CAACoC,OAAO,CAAC,EAAEnC,SAAS,CAAC,MAAMlB,KAAK,CAAC,GAAGqD,OAAO,CAACrC,GAAG,CAACa,MAAM,IAAIA,MAAM,CAAC6I,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC/H;MACA;MACA;MACA,OAAO,IAAI,CAACC,YAAY,CAACF,IAAI,CAACvJ,SAAS,CAAC,MAAM,IAAI,CAAC0J,gBAAgB,CAAC,CAAC;IACzE,CAAC,CAAC;IAoYF;IAAA9I,eAAA,8BACuBwD,KAAK,IAAK;MAC7B;MACA;MACA,IAAKA,KAAK,CAACuF,OAAO,KAAKvK,MAAM,IAAI,CAACD,cAAc,CAACiF,KAAK,CAAC,IAClDA,KAAK,CAACuF,OAAO,KAAKpK,QAAQ,IAAIJ,cAAc,CAACiF,KAAK,EAAE,QAAQ,CAAE,EAAE;QACjE;QACA;QACA,IAAI,IAAI,CAACwF,0BAA0B,EAAE;UAAA,IAAAC,qBAAA;UACjC,IAAI,CAACC,uBAAuB,EAAAD,qBAAA,GAAC,IAAI,CAACE,yBAAyB,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UAClE,IAAI,CAACD,0BAA0B,GAAG,IAAI;QAC1C;QACA,IAAI,CAACI,oBAAoB,CAACC,IAAI,CAAC,CAAC;QAChC,IAAI,CAACC,gBAAgB,CAAC,CAAC;QACvB;QACA;QACA9F,KAAK,CAAC+F,eAAe,CAAC,CAAC;QACvB/F,KAAK,CAACgG,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IAoJD;AACJ;AACA;AACA;AACA;IAJIxJ,eAAA,wBAKgB,IAAI;EAzpBJ;EAGhByJ,eAAeA,CAAA,EAAG;IACd,IAAI,CAACZ,YAAY,CAACQ,IAAI,CAAC,CAAC;IACxB,IAAI,CAACR,YAAY,CAACa,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAACC,kBAAkB,CAAC;EAC9F;EACAC,WAAWA,CAACrB,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAACsB,iBAAiB,EAAE;MAC/C,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACD,iBAAiB,CAAC;MAClD,IAAI,IAAI,CAACzB,SAAS,EAAE;QAChB,IAAI,CAAC2B,WAAW,CAACC,cAAc,CAAC,CAAC;MACrC;IACJ;EACJ;EACAtH,WAAWA,CAAA,EAAG;IAAA,IAAAuH,qBAAA;IACV,CAAAA,qBAAA,OAAI,CAACT,kBAAkB,cAAAS,qBAAA,eAAvBA,qBAAA,CAAAC,IAAA,KAA0B,CAAC;IAC3B,IAAI,CAACC,6BAA6B,CAACtH,WAAW,CAAC,CAAC;IAChD,IAAI,CAACuH,qBAAqB,CAACvH,WAAW,CAAC,CAAC;IACxC,IAAI,CAACwH,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACrB,oBAAoB,CAACM,QAAQ,CAAC,CAAC;IACpC,IAAI,CAACgB,eAAe,CAAC,CAAC;EAC1B;EACA;EACA,IAAInC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACoC,gBAAgB,IAAI,IAAI,CAAClC,YAAY,CAACjO,SAAS;EAC/D;EAEA;EACAoQ,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC,IAAI,CAACJ,gBAAgB,EAAE;MACxB;IACJ;IACA,IAAI,IAAI,CAACpC,SAAS,EAAE;MAChB;MACA;MACA;MACA;MACA,IAAI,CAACyC,KAAK,CAACC,GAAG,CAAC,MAAM;QACjB,IAAI,CAACxC,YAAY,CAACjD,MAAM,CAAC9C,IAAI,CAAC,CAAC;MACnC,CAAC,CAAC;IACN;IACA;IACA;IACA,IAAI,IAAI,CAAC+F,YAAY,CAACyC,qBAAqB,KAAK,IAAI,EAAE;MAClD,IAAI,CAACzC,YAAY,CAAC7H,OAAO,GAAG,KAAK;MACjC,IAAI,CAAC6H,YAAY,CAACyC,qBAAqB,GAAG,IAAI;IAClD;IACA,IAAI,CAACP,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC3B,0BAA0B,GAAG,IAAI;IACtC,IAAI,IAAI,CAACkB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACiB,WAAW,CAAC,CAAC,EAAE;MACpD,IAAI,CAACjB,WAAW,CAACkB,MAAM,CAAC,CAAC;MACzB,IAAI,CAACC,2BAA2B,CAACrI,WAAW,CAAC,CAAC;IAClD;IACA,IAAI,CAACsI,iBAAiB,CAAC,CAAC;IACxB;IACA;IACA,IAAI,CAAC,IAAI,CAACd,mBAAmB,EAAE;MAC3B;MACA;MACA;MACA;MACA,IAAI,CAACzJ,kBAAkB,CAACwK,aAAa,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,IAAI,CAACC,aAAa,EAAE;MACpB7N,sBAAsB,CAAC,IAAI,CAAC6N,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC/C,YAAY,CAACvO,EAAE,CAAC;IACjF;EACJ;EACA;AACJ;AACA;AACA;EACIiQ,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACQ,gBAAgB,EAAE;MACvB,IAAI,CAACT,WAAW,CAACC,cAAc,CAAC,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIsB,mBAAmBA,CAAA,EAAG;IACtB,OAAOvN,KAAK,CAAC,IAAI,CAAC4K,gBAAgB,EAAE,IAAI,CAACL,YAAY,CAACxG,WAAW,CAACyJ,MAAM,CAAC/C,IAAI,CAAC1J,MAAM,CAAC,MAAM,IAAI,CAAC0L,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACvB,oBAAoB,EAAE,IAAI,CAACuC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAACzB,WAAW,GACxL,IAAI,CAACA,WAAW,CAAC0B,WAAW,CAAC,CAAC,CAACjD,IAAI,CAAC1J,MAAM,CAAC,MAAM,IAAI,CAAC0L,gBAAgB,CAAC,CAAC,GACxExM,EAAE,CAAC,CAAC,CAAC,CAACwK,IAAI;IAChB;IACAzJ,GAAG,CAACsE,KAAK,IAAKA,KAAK,YAAYlI,wBAAwB,GAAGkI,KAAK,GAAG,IAAK,CAAC,CAAC;EAC7E;EAWA;EACA,IAAIqI,YAAYA,CAAA,EAAG;IACf,IAAI,IAAI,CAACpD,YAAY,IAAI,IAAI,CAACA,YAAY,CAACxG,WAAW,EAAE;MACpD,OAAO,IAAI,CAACwG,YAAY,CAACxG,WAAW,CAAC6J,UAAU;IACnD;IACA,OAAO,IAAI;EACf;EACA;EACAH,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAItN,UAAU,CAAC0N,QAAQ,IAAI;MAC9B,MAAMC,QAAQ,GAAIxI,KAAK,IAAK;QACxB;QACA;QACA,MAAMyI,WAAW,GAAGlO,eAAe,CAACyF,KAAK,CAAC;QAC1C,MAAM0I,SAAS,GAAG,IAAI,CAACC,UAAU,GAC3B,IAAI,CAACA,UAAU,CAACC,yBAAyB,CAAC,CAAC,CAACjL,aAAa,GACzD,IAAI;QACV,MAAMkL,YAAY,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACC,UAAU,CAACpL,aAAa,GAAG,IAAI;QACxF,IAAI,IAAI,CAACwJ,gBAAgB,IACrBsB,WAAW,KAAK,IAAI,CAACO,QAAQ,CAACrL,aAAa;QAC3C;QACA;QACA;QACA;QACA,CAAC,IAAI,CAACqH,SAAS,CAAC,CAAC,KAChB,CAAC0D,SAAS,IAAI,CAACA,SAAS,CAACO,QAAQ,CAACR,WAAW,CAAC,CAAC,KAC/C,CAACI,YAAY,IAAI,CAACA,YAAY,CAACI,QAAQ,CAACR,WAAW,CAAC,CAAC,IACtD,CAAC,CAAC,IAAI,CAAC/B,WAAW,IAClB,CAAC,IAAI,CAACA,WAAW,CAACwC,cAAc,CAACD,QAAQ,CAACR,WAAW,CAAC,EAAE;UACxDF,QAAQ,CAAC1C,IAAI,CAAC7F,KAAK,CAAC;QACxB;MACJ,CAAC;MACD,MAAMmJ,QAAQ,GAAG,CACb,IAAI,CAAC/C,SAAS,CAACC,MAAM,CAAC,UAAU,EAAE,OAAO,EAAEmC,QAAQ,CAAC,EACpD,IAAI,CAACpC,SAAS,CAACC,MAAM,CAAC,UAAU,EAAE,UAAU,EAAEmC,QAAQ,CAAC,EACvD,IAAI,CAACpC,SAAS,CAACC,MAAM,CAAC,UAAU,EAAE,UAAU,EAAEmC,QAAQ,CAAC,CAC1D;MACD,OAAO,MAAM;QACTW,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;MAC1C,CAAC;IACL,CAAC,CAAC;EACN;EACA;EACAC,UAAUA,CAAChM,KAAK,EAAE;IACdiM,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACC,kBAAkB,CAACpM,KAAK,CAAC,CAAC;EACpE;EACA;EACAqM,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACC,SAAS,GAAGD,EAAE;EACvB;EACA;EACAE,iBAAiBA,CAACF,EAAE,EAAE;IAClB,IAAI,CAACG,UAAU,GAAGH,EAAE;EACxB;EACA;EACAI,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACjB,QAAQ,CAACrL,aAAa,CAACuM,QAAQ,GAAGD,UAAU;EACrD;EACAE,cAAcA,CAACnK,KAAK,EAAE;IAClB,MAAMuF,OAAO,GAAGvF,KAAK,CAACuF,OAAO;IAC7B,MAAM6E,WAAW,GAAGrP,cAAc,CAACiF,KAAK,CAAC;IACzC;IACA;IACA;IACA;IACA,IAAIuF,OAAO,KAAKvK,MAAM,IAAI,CAACoP,WAAW,EAAE;MACpCpK,KAAK,CAACgG,cAAc,CAAC,CAAC;IAC1B;IACA,IAAI,CAACqE,mBAAmB,GAAG,IAAI,CAACrB,QAAQ,CAACrL,aAAa,CAACL,KAAK;IAC5D,IAAI,IAAI,CAAC+K,YAAY,IAAI9C,OAAO,KAAKtK,KAAK,IAAI,IAAI,CAAC8J,SAAS,IAAI,CAACqF,WAAW,EAAE;MAC1E,IAAI,CAAC/B,YAAY,CAACiC,qBAAqB,CAAC,CAAC;MACzC,IAAI,CAACxE,gBAAgB,CAAC,CAAC;MACvB9F,KAAK,CAACgG,cAAc,CAAC,CAAC;IAC1B,CAAC,MACI,IAAI,IAAI,CAACf,YAAY,EAAE;MACxB,MAAMsF,cAAc,GAAG,IAAI,CAACtF,YAAY,CAACxG,WAAW,CAAC6J,UAAU;MAC/D,MAAMkC,UAAU,GAAGjF,OAAO,KAAKpK,QAAQ,IAAIoK,OAAO,KAAKnK,UAAU;MACjE,IAAImK,OAAO,KAAKrK,GAAG,IAAKsP,UAAU,IAAI,CAACJ,WAAW,IAAI,IAAI,CAACrF,SAAU,EAAE;QACnE,IAAI,CAACE,YAAY,CAACxG,WAAW,CAACgM,SAAS,CAACzK,KAAK,CAAC;MAClD,CAAC,MACI,IAAIwK,UAAU,IAAI,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE;QACpC,IAAI,CAACrD,kBAAkB,CAAC,IAAI,CAACgD,mBAAmB,CAAC;MACrD;MACA,IAAIG,UAAU,IAAI,IAAI,CAACvF,YAAY,CAACxG,WAAW,CAAC6J,UAAU,KAAKiC,cAAc,EAAE;QAC3E,IAAI,CAACI,eAAe,CAAC,IAAI,CAAC1F,YAAY,CAACxG,WAAW,CAACmM,eAAe,IAAI,CAAC,CAAC;QACxE,IAAI,IAAI,CAAC3F,YAAY,CAACnI,sBAAsB,IAAI,IAAI,CAACuL,YAAY,EAAE;UAC/D,IAAI,CAAC,IAAI,CAAC7C,0BAA0B,EAAE;YAClC,IAAI,CAACG,yBAAyB,GAAG,IAAI,CAAC0E,mBAAmB;UAC7D;UACA,IAAI,CAAC7E,0BAA0B,GAAG,IAAI,CAAC6C,YAAY;UACnD,IAAI,CAACqB,kBAAkB,CAAC,IAAI,CAACrB,YAAY,CAAC/K,KAAK,CAAC;QACpD;MACJ;IACJ;EACJ;EACAuN,YAAYA,CAAC7K,KAAK,EAAE;IAChB,IAAI8K,MAAM,GAAG9K,KAAK,CAAC8K,MAAM;IACzB,IAAIxN,KAAK,GAAGwN,MAAM,CAACxN,KAAK;IACxB;IACA,IAAIwN,MAAM,CAACrK,IAAI,KAAK,QAAQ,EAAE;MAC1BnD,KAAK,GAAGA,KAAK,IAAI,EAAE,GAAG,IAAI,GAAGyN,UAAU,CAACzN,KAAK,CAAC;IAClD;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC0N,cAAc,KAAK1N,KAAK,EAAE;MAC/B,IAAI,CAAC0N,cAAc,GAAG1N,KAAK;MAC3B,IAAI,CAACkI,0BAA0B,GAAG,IAAI;MACtC;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACP,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACjI,gBAAgB,EAAE;QAC3D,IAAI,CAAC6M,SAAS,CAACvM,KAAK,CAAC;MACzB;MACA,IAAI,CAACA,KAAK,EAAE;QACR,IAAI,CAAC2N,4BAA4B,CAAC,IAAI,EAAE,KAAK,CAAC;MAClD,CAAC,MACI,IAAI,IAAI,CAAClG,SAAS,IAAI,CAAC,IAAI,CAACE,YAAY,CAACjI,gBAAgB,EAAE;QAAA,IAAAkO,qBAAA;QAC5D;QACA;QACA,MAAMC,cAAc,IAAAD,qBAAA,GAAG,IAAI,CAACjG,YAAY,CAAClH,OAAO,cAAAmN,qBAAA,uBAAzBA,qBAAA,CAA2BE,IAAI,CAAC7O,MAAM,IAAIA,MAAM,CAAC8O,QAAQ,CAAC;QACjF,IAAIF,cAAc,EAAE;UAChB,MAAMG,OAAO,GAAG,IAAI,CAACC,gBAAgB,CAACJ,cAAc,CAAC7N,KAAK,CAAC;UAC3D,IAAIA,KAAK,KAAKgO,OAAO,EAAE;YACnBH,cAAc,CAACK,QAAQ,CAAC,KAAK,CAAC;UAClC;QACJ;MACJ;MACA,IAAI,IAAI,CAACd,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC1F,SAAS,CAAC,CAAC,EAAE;QAAA,IAAAyG,qBAAA;QACrC;QACA;QACA;QACA;QACA;QACA,MAAMC,aAAa,IAAAD,qBAAA,GAAG,IAAI,CAACpB,mBAAmB,cAAAoB,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAACzC,QAAQ,CAACrL,aAAa,CAACL,KAAK;QACnF,IAAI,CAAC+M,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAAChD,kBAAkB,CAACqE,aAAa,CAAC;MAC1C;IACJ;EACJ;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC7G,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI;IACnC,CAAC,MACI,IAAI,IAAI,CAAC4F,QAAQ,CAAC,CAAC,EAAE;MACtB,IAAI,CAACM,cAAc,GAAG,IAAI,CAAChC,QAAQ,CAACrL,aAAa,CAACL,KAAK;MACvD,IAAI,CAACsO,cAAc,CAAC,IAAI,CAACZ,cAAc,CAAC;MACxC,IAAI,CAACa,WAAW,CAAC,IAAI,CAAC;IAC1B;EACJ;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACpB,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC3F,SAAS,EAAE;MACpC,IAAI,CAACsC,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACA;EACArC,SAASA,CAAA,EAAG;IACR,OAAO1K,iCAAiC,CAAC,CAAC,KAAK,IAAI,CAAC0O,QAAQ,CAACrL,aAAa;EAC9E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkO,WAAWA,CAACE,aAAa,GAAG,KAAK,EAAE;IAC/B,IAAI,IAAI,CAACpD,UAAU,IAAI,IAAI,CAACA,UAAU,CAACqD,UAAU,KAAK,MAAM,EAAE;MAC1D,IAAID,aAAa,EAAE;QACf,IAAI,CAACpD,UAAU,CAACsD,oBAAoB,CAAC,CAAC;MAC1C,CAAC,MACI;QACD,IAAI,CAACtD,UAAU,CAACqD,UAAU,GAAG,QAAQ;MACzC;MACA,IAAI,CAACE,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACA;EACA3E,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC2E,sBAAsB,EAAE;MAC7B,IAAI,IAAI,CAACvD,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACqD,UAAU,GAAG,MAAM;MACvC;MACA,IAAI,CAACE,sBAAsB,GAAG,KAAK;IACvC;EACJ;EACA;AACJ;AACA;AACA;EACIC,0BAA0BA,CAAA,EAAG;IAAA,IAAAC,sBAAA,EAAAC,sBAAA;IACzB,MAAMC,aAAa,GAAG,IAAIzR,UAAU,CAAC0R,UAAU,IAAI;MAC/C7S,eAAe,CAAC,MAAM;QAClB6S,UAAU,CAAC1G,IAAI,CAAC,CAAC;MACrB,CAAC,EAAE;QAAE2G,QAAQ,EAAE,IAAI,CAACC;MAAqB,CAAC,CAAC;IAC/C,CAAC,CAAC;IACF,MAAMC,aAAa,IAAAN,sBAAA,IAAAC,sBAAA,GAAG,IAAI,CAACpH,YAAY,CAAClH,OAAO,cAAAsO,sBAAA,uBAAzBA,sBAAA,CAA2BnH,OAAO,CAACC,IAAI,CAACtJ,GAAG,CAAC,MAAM,IAAI,CAAC2K,iBAAiB,CAACmG,mBAAmB,CAAC,CAAC,CAAC;IACrH;IACA;IACA7Q,KAAK,CAAC,CAAC,CAAC,CAAC,cAAAsQ,sBAAA,cAAAA,sBAAA,GAAIzR,EAAE,CAAC,CAAC;IACjB;IACA,OAAQD,KAAK,CAAC4R,aAAa,EAAEI,aAAa,CAAC,CACtCvH,IAAI;IACT;IACA;IACAvJ,SAAS,CAAC,MAAM,IAAI,CAAC4L,KAAK,CAACC,GAAG,CAAC,MAAM;MACjC;MACA;MACA;MACA,MAAMmF,OAAO,GAAG,IAAI,CAAC7H,SAAS;MAC9B,IAAI,CAACe,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACgC,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACvK,kBAAkB,CAACwK,aAAa,CAAC,CAAC;MACvC,IAAI,IAAI,CAAChD,SAAS,EAAE;QAChB,IAAI,CAAC2B,WAAW,CAACC,cAAc,CAAC,CAAC;MACrC;MACA,IAAIiG,OAAO,KAAK,IAAI,CAAC7H,SAAS,EAAE;QAC5B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,IAAI,CAACA,SAAS,EAAE;UAChB,IAAI,CAAC8H,WAAW,CAAC,CAAC;QACtB,CAAC,MACI;UACD,IAAI,CAAC5H,YAAY,CAACjD,MAAM,CAAC9C,IAAI,CAAC,CAAC;QACnC;MACJ;MACA,OAAO,IAAI,CAAC+I,mBAAmB;IACnC,CAAC,CAAC,CAAC;IACH;IACAlM,IAAI,CAAC,CAAC,CAAC;IACH;IAAA,CACCgD,SAAS,CAACiB,KAAK,IAAI,IAAI,CAAC8M,iBAAiB,CAAC9M,KAAK,CAAC,CAAC;EAC1D;EACA;AACJ;AACA;AACA;EACI6M,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5H,YAAY,CAAClD,MAAM,CAAC7C,IAAI,CAAC,CAAC;EACnC;EACA;EACA+H,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACP,WAAW,EAAE;MAClB,IAAI,CAACY,UAAU,CAAC,CAAC;MACjB,IAAI,CAACZ,WAAW,CAACqG,OAAO,CAAC,CAAC;MAC1B,IAAI,CAACrG,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACA6E,gBAAgBA,CAACjO,KAAK,EAAE;IACpB,MAAM2H,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,OAAOA,YAAY,IAAIA,YAAY,CAACtD,WAAW,GAAGsD,YAAY,CAACtD,WAAW,CAACrE,KAAK,CAAC,GAAGA,KAAK;EAC7F;EACAoM,kBAAkBA,CAACpM,KAAK,EAAE;IACtB,MAAM0P,SAAS,GAAG,IAAI,CAACzB,gBAAgB,CAACjO,KAAK,CAAC;IAC9C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,CAAC2N,4BAA4B,CAAC,IAAI,EAAE,KAAK,CAAC;IAClD;IACA;IACA;IACA,IAAI,CAACvF,uBAAuB,CAACsH,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,EAAE,CAAC;EACpE;EACAtH,uBAAuBA,CAACpI,KAAK,EAAE;IAC3B;IACA;IACA,IAAI,IAAI,CAACqL,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACsE,QAAQ,CAAC3P,KAAK,GAAGA,KAAK;IAC1C,CAAC,MACI;MACD,IAAI,CAAC0L,QAAQ,CAACrL,aAAa,CAACL,KAAK,GAAGA,KAAK;IAC7C;IACA,IAAI,CAAC0N,cAAc,GAAG1N,KAAK;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACIwP,iBAAiBA,CAAC9M,KAAK,EAAE;IACrB,MAAML,KAAK,GAAG,IAAI,CAACsF,YAAY;IAC/B,MAAMiI,QAAQ,GAAGlN,KAAK,GAAGA,KAAK,CAAC1D,MAAM,GAAG,IAAI,CAACkJ,0BAA0B;IACvE,IAAI0H,QAAQ,EAAE;MACV,IAAI,CAACjC,4BAA4B,CAACiC,QAAQ,CAAC;MAC3C,IAAI,CAACxD,kBAAkB,CAACwD,QAAQ,CAAC5P,KAAK,CAAC;MACvC;MACA;MACA;MACA,IAAI,CAACuM,SAAS,CAACqD,QAAQ,CAAC5P,KAAK,CAAC;MAC9BqC,KAAK,CAACI,gBAAgB,CAACmN,QAAQ,CAAC;MAChC,IAAI,CAAClE,QAAQ,CAACrL,aAAa,CAACwP,KAAK,CAAC,CAAC;IACvC,CAAC,MACI,IAAIxN,KAAK,CAAC3C,gBAAgB,IAC3B,IAAI,CAACgM,QAAQ,CAACrL,aAAa,CAACL,KAAK,KAAK,IAAI,CAAC8P,cAAc,EAAE;MAC3D,IAAI,CAACnC,4BAA4B,CAAC,IAAI,CAAC;MACvC,IAAI,CAACvB,kBAAkB,CAAC,IAAI,CAAC;MAC7B,IAAI,CAACG,SAAS,CAAC,IAAI,CAAC;IACxB;IACA,IAAI,CAACvC,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;EACI2D,4BAA4BA,CAACoC,IAAI,EAAEC,SAAS,EAAE;IAAA,IAAAC,kBAAA;IAC1C;IACA;IACA,CAAAA,kBAAA,OAAI,CAACtI,YAAY,cAAAsI,kBAAA,gBAAAA,kBAAA,GAAjBA,kBAAA,CAAmBxP,OAAO,cAAAwP,kBAAA,eAA1BA,kBAAA,CAA4BnE,OAAO,CAAC7M,MAAM,IAAI;MAC1C,IAAIA,MAAM,KAAK8Q,IAAI,IAAI9Q,MAAM,CAAC8O,QAAQ,EAAE;QACpC9O,MAAM,CAACiP,QAAQ,CAAC8B,SAAS,CAAC;MAC9B;IACJ,CAAC,CAAC;EACN;EACAjG,kBAAkBA,CAACqE,aAAa,GAAG,IAAI,CAAC1C,QAAQ,CAACrL,aAAa,CAACL,KAAK,EAAE;IAClE,IAAI,CAACsO,cAAc,CAACF,aAAa,CAAC;IAClC,IAAI,CAACG,WAAW,CAAC,CAAC;IAClB;IACA,IAAI,IAAI,CAAC7D,aAAa,EAAE;MACpB,MAAMwF,OAAO,GAAG,IAAI,CAACvI,YAAY,CAACvO,EAAE;MACpC0D,mBAAmB,CAAC,IAAI,CAAC4N,aAAa,EAAE,WAAW,EAAEwF,OAAO,CAAC;IACjE;EACJ;EACA5B,cAAcA,CAACF,aAAa,EAAE;IAAA,IAAA+B,iBAAA;IAC1B,IAAI,CAAC,IAAI,CAACxI,YAAY,KAAK,OAAO/B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACvE,MAAMkB,mCAAmC,CAAC,CAAC;IAC/C;IACA,IAAIsJ,UAAU,GAAG,IAAI,CAAChH,WAAW;IACjC,IAAI,CAACgH,UAAU,EAAE;MAAA,IAAAC,gBAAA;MACb,IAAI,CAACC,OAAO,GAAG,IAAIrS,cAAc,CAAC,IAAI,CAAC0J,YAAY,CAAC1D,QAAQ,EAAE,IAAI,CAACsM,iBAAiB,EAAE;QAClFnX,EAAE,GAAAiX,gBAAA,GAAE,IAAI,CAAChF,UAAU,cAAAgF,gBAAA,uBAAfA,gBAAA,CAAiBG,UAAU,CAAC;MACpC,CAAC,CAAC;MACFJ,UAAU,GAAG,IAAI,CAACK,QAAQ,CAACC,MAAM,CAAC,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC;MAC3D,IAAI,CAACvH,WAAW,GAAGgH,UAAU;MAC7B,IAAI,CAAC3G,qBAAqB,GAAG,IAAI,CAACmH,cAAc,CAACpP,MAAM,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;QACtE,IAAI,IAAI,CAACgG,SAAS,IAAI2I,UAAU,EAAE;UAC9BA,UAAU,CAACS,UAAU,CAAC;YAAEC,KAAK,EAAE,IAAI,CAACC,cAAc,CAAC;UAAE,CAAC,CAAC;QAC3D;MACJ,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAACvH,6BAA6B,GAAG,IAAI,CAACwH,mBAAmB,CACxDC,OAAO,CAACjT,WAAW,CAACkT,gBAAgB,CAAC,CACrCzP,SAAS,CAAC0P,MAAM,IAAI;QACrB,MAAMC,kBAAkB,GAAGD,MAAM,CAACE,OAAO;QACzC;QACA;QACA,IAAID,kBAAkB,EAAE;UACpB,IAAI,CAAClI,iBAAiB,CACjBoI,sBAAsB,CAAC,IAAI,CAAC,CAC5BC,iBAAiB,CAAC,IAAI,CAAC,CACvBC,kBAAkB,CAAC,CAAC,CAAC;QAC9B,CAAC,MACI;UACD,IAAI,CAACtI,iBAAiB,CACjBoI,sBAAsB,CAAC,KAAK,CAAC,CAC7BC,iBAAiB,CAAC,KAAK,CAAC,CACxBC,kBAAkB,CAAC,CAAC,CAAC;QAC9B;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACA,IAAI,CAACtI,iBAAiB,CAACuI,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAAC;MAC7DtB,UAAU,CAACS,UAAU,CAAC;QAAEC,KAAK,EAAE,IAAI,CAACC,cAAc,CAAC;MAAE,CAAC,CAAC;IAC3D;IACA,IAAIX,UAAU,IAAI,CAACA,UAAU,CAAC/F,WAAW,CAAC,CAAC,EAAE;MACzC+F,UAAU,CAACuB,MAAM,CAAC,IAAI,CAACrB,OAAO,CAAC;MAC/B,IAAI,CAACR,cAAc,GAAG1B,aAAa;MACnC,IAAI,CAACrB,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACxC,2BAA2B,GAAG,IAAI,CAACsE,0BAA0B,CAAC,CAAC;IACxE;IACA,MAAMS,OAAO,GAAG,IAAI,CAAC7H,SAAS;IAC9B,IAAI,CAACE,YAAY,CAAC7H,OAAO,GAAG,IAAI,CAAC+J,gBAAgB,GAAG,IAAI;IACxD,IAAI,CAAClC,YAAY,CAACyC,qBAAqB,GAAG,IAAI;IAC9C,IAAI,CAACzC,YAAY,CAAC5H,SAAS,EAAAoQ,iBAAA,GAAC,IAAI,CAAC9E,UAAU,cAAA8E,iBAAA,uBAAfA,iBAAA,CAAiByB,KAAK,CAAC;IACnD,IAAI,CAACpH,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACqH,yBAAyB,CAAC,CAAC;IAChC;IACA;IACA,IAAI,IAAI,CAACpK,SAAS,IAAI6H,OAAO,KAAK,IAAI,CAAC7H,SAAS,EAAE;MAC9C,IAAI,CAAC8H,WAAW,CAAC,CAAC;IACtB;EACJ;EAqBA;EACA/E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC7C,YAAY,CAAC7F,cAAc,CAAC,CAAC;IAClC;IACA;IACA;IACA,IAAI,IAAI,CAAC2F,SAAS,EAAE;MAChB,MAAM2I,UAAU,GAAG,IAAI,CAAChH,WAAW;MACnC,IAAI,CAAC,IAAI,CAAC0I,oBAAoB,EAAE;QAC5B;QACA;QACA,IAAI,CAACA,oBAAoB,GAAG1B,UAAU,CAAC2B,aAAa,CAAC,CAAC,CAACtQ,SAAS,CAAC,IAAI,CAACuQ,mBAAmB,CAAC;MAC9F;MACA,IAAI,CAAC,IAAI,CAACC,yBAAyB,EAAE;QACjC;QACA;QACA;QACA,IAAI,CAACA,yBAAyB,GAAG7B,UAAU,CAAC8B,oBAAoB,CAAC,CAAC,CAACzQ,SAAS,CAAC,CAAC;MAClF;IACJ,CAAC,MACI;MAAA,IAAA0Q,qBAAA,EAAAC,qBAAA;MACD,CAAAD,qBAAA,OAAI,CAACL,oBAAoB,cAAAK,qBAAA,eAAzBA,qBAAA,CAA2BjQ,WAAW,CAAC,CAAC;MACxC,CAAAkQ,qBAAA,OAAI,CAACH,yBAAyB,cAAAG,qBAAA,eAA9BA,qBAAA,CAAgClQ,WAAW,CAAC,CAAC;MAC7C,IAAI,CAAC4P,oBAAoB,GAAG,IAAI,CAACG,yBAAyB,GAAG,IAAI;IACrE;EACJ;EACAtB,iBAAiBA,CAAA,EAAG;IAAA,IAAA0B,UAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;IAChB,OAAO,IAAI/V,aAAa,CAAC;MACrBgW,gBAAgB,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC5CC,cAAc,EAAE,IAAI,CAACC,eAAe,CAAC,CAAC;MACtC9B,KAAK,EAAE,IAAI,CAACC,cAAc,CAAC,CAAC;MAC5B8B,SAAS,GAAAR,UAAA,GAAE,IAAI,CAACS,IAAI,cAAAT,UAAA,cAAAA,UAAA,GAAIU,SAAS;MACjCpT,WAAW,GAAA2S,eAAA,GAAE,IAAI,CAACrR,SAAS,cAAAqR,eAAA,uBAAdA,eAAA,CAAgB3S,WAAW;MACxCqT,aAAa,GAAAT,gBAAA,GAAE,IAAI,CAACtR,SAAS,cAAAsR,gBAAA,uBAAdA,gBAAA,CAAgBS,aAAa;MAC5CC,UAAU,GAAAT,gBAAA,GAAE,IAAI,CAACvR,SAAS,cAAAuR,gBAAA,uBAAdA,gBAAA,CAAgBU;IAChC,CAAC,CAAC;EACN;EACAR,mBAAmBA,CAAA,EAAG;IAClB;IACA,MAAMS,QAAQ,GAAG,IAAI,CAAC1C,QAAQ,CACzB2C,QAAQ,CAAC,CAAC,CACVC,mBAAmB,CAAC,IAAI,CAAC3B,oBAAoB,CAAC,CAAC,CAAC,CAChDJ,sBAAsB,CAAC,KAAK,CAAC,CAC7BgC,QAAQ,CAAC,KAAK,CAAC;IACpB,IAAI,CAACnK,qBAAqB,CAACgK,QAAQ,CAAC;IACpC,IAAI,CAACjK,iBAAiB,GAAGiK,QAAQ;IACjC,OAAOA,QAAQ;EACnB;EACA;EACAhK,qBAAqBA,CAACsJ,gBAAgB,EAAE;IACpC;IACA;IACA,MAAMc,cAAc,GAAG,CACnB;MAAEC,OAAO,EAAE,OAAO;MAAEC,OAAO,EAAE,QAAQ;MAAEC,QAAQ,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAM,CAAC,EAC3E;MAAEH,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE,QAAQ;MAAEC,QAAQ,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,CAC1E;IACD;IACA;IACA;IACA,MAAMV,UAAU,GAAG,IAAI,CAACW,WAAW;IACnC,MAAMC,cAAc,GAAG,CACnB;MAAEL,OAAO,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAEC,QAAQ,EAAE,OAAO;MAAEC,QAAQ,EAAE,QAAQ;MAAEV;IAAW,CAAC,EACvF;MAAEO,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE,KAAK;MAAEC,QAAQ,EAAE,KAAK;MAAEC,QAAQ,EAAE,QAAQ;MAAEV;IAAW,CAAC,CACtF;IACD,IAAIa,SAAS;IACb,IAAI,IAAI,CAACV,QAAQ,KAAK,OAAO,EAAE;MAC3BU,SAAS,GAAGD,cAAc;IAC9B,CAAC,MACI,IAAI,IAAI,CAACT,QAAQ,KAAK,OAAO,EAAE;MAChCU,SAAS,GAAGP,cAAc;IAC9B,CAAC,MACI;MACDO,SAAS,GAAG,CAAC,GAAGP,cAAc,EAAE,GAAGM,cAAc,CAAC;IACtD;IACApB,gBAAgB,CAACsB,aAAa,CAACD,SAAS,CAAC;EAC7C;EACApC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAClG,WAAW,EAAE;MAClB,OAAO,IAAI,CAACA,WAAW,CAACC,UAAU;IACtC;IACA,OAAO,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACA,UAAU,CAACC,yBAAyB,CAAC,CAAC,GAAG,IAAI,CAACI,QAAQ;EACxF;EACAqF,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACpJ,YAAY,CAACrD,UAAU,IAAI,IAAI,CAAC0P,aAAa,CAAC,CAAC;EAC/D;EACA;EACAA,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACtC,oBAAoB,CAAC,CAAC,CAACrR,aAAa,CAAC4T,qBAAqB,CAAC,CAAC,CAACnD,KAAK;EAClF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACItI,gBAAgBA,CAAA,EAAG;IACf,MAAMb,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,IAAIA,YAAY,CAACpI,qBAAqB,EAAE;MACpC;MACA;MACA;MACA,IAAI2U,uBAAuB,GAAG,CAAC,CAAC;MAChC,KAAK,IAAIxS,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGiG,YAAY,CAAClH,OAAO,CAAC+B,MAAM,EAAEd,KAAK,EAAE,EAAE;QAC9D,MAAMzC,MAAM,GAAG0I,YAAY,CAAClH,OAAO,CAAC0T,GAAG,CAACzS,KAAK,CAAC;QAC9C,IAAI,CAACzC,MAAM,CAAC2N,QAAQ,EAAE;UAClBsH,uBAAuB,GAAGxS,KAAK;UAC/B;QACJ;MACJ;MACAiG,YAAY,CAACxG,WAAW,CAACiT,aAAa,CAACF,uBAAuB,CAAC;IACnE,CAAC,MACI;MACDvM,YAAY,CAACxG,WAAW,CAACiT,aAAa,CAAC,CAAC,CAAC,CAAC;IAC9C;EACJ;EACA;EACAhH,QAAQA,CAAA,EAAG;IACP,MAAMiH,OAAO,GAAG,IAAI,CAAC3I,QAAQ,CAACrL,aAAa;IAC3C,OAAO,CAACgU,OAAO,CAACC,QAAQ,IAAI,CAACD,OAAO,CAACzH,QAAQ,IAAI,CAAC,IAAI,CAAC2H,oBAAoB;EAC/E;EACA;EACAlH,eAAeA,CAAC3L,KAAK,EAAE;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMiG,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,MAAM6M,UAAU,GAAG9Z,6BAA6B,CAACgH,KAAK,EAAEiG,YAAY,CAAClH,OAAO,EAAEkH,YAAY,CAAC/D,YAAY,CAAC;IACxG,IAAIlC,KAAK,KAAK,CAAC,IAAI8S,UAAU,KAAK,CAAC,EAAE;MACjC;MACA;MACA;MACA7M,YAAY,CAACxF,aAAa,CAAC,CAAC,CAAC;IACjC,CAAC,MACI,IAAIwF,YAAY,CAACtF,KAAK,EAAE;MACzB,MAAMpD,MAAM,GAAG0I,YAAY,CAAClH,OAAO,CAACoB,OAAO,CAAC,CAAC,CAACH,KAAK,CAAC;MACpD,IAAIzC,MAAM,EAAE;QACR,MAAMoV,OAAO,GAAGpV,MAAM,CAACwV,eAAe,CAAC,CAAC;QACxC,MAAMC,iBAAiB,GAAG9Z,wBAAwB,CAACyZ,OAAO,CAACM,SAAS,EAAEN,OAAO,CAACO,YAAY,EAAEjN,YAAY,CAACrF,aAAa,CAAC,CAAC,EAAEqF,YAAY,CAACtF,KAAK,CAAChC,aAAa,CAACuU,YAAY,CAAC;QACxKjN,YAAY,CAACxF,aAAa,CAACuS,iBAAiB,CAAC;MACjD;IACJ;EACJ;EAOA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI7C,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMgD,KAAK,GAAG,IAAI,CAACnJ,QAAQ,CAACrL,aAAa,CAACyU,OAAO,CAAC,mDAAmD,CAAC;IACtG,IAAI,CAACD,KAAK,EAAE;MACR;MACA;IACJ;IACA,MAAM3E,OAAO,GAAG,IAAI,CAACvI,YAAY,CAACvO,EAAE;IACpC,IAAI,IAAI,CAACsR,aAAa,EAAE;MACpB7N,sBAAsB,CAAC,IAAI,CAAC6N,aAAa,EAAE,WAAW,EAAEwF,OAAO,CAAC;IACpE;IACApT,mBAAmB,CAAC+X,KAAK,EAAE,WAAW,EAAE3E,OAAO,CAAC;IAChD,IAAI,CAACxF,aAAa,GAAGmK,KAAK;EAC9B;EACA;EACAjL,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACc,aAAa,EAAE;MACpB,MAAMwF,OAAO,GAAG,IAAI,CAACvI,YAAY,CAACvO,EAAE;MACpCyD,sBAAsB,CAAC,IAAI,CAAC6N,aAAa,EAAE,WAAW,EAAEwF,OAAO,CAAC;MAChE,IAAI,CAACxF,aAAa,GAAG,IAAI;IAC7B;EACJ;AAGJ;AAACqK,uBAAA,GAryBKnO,sBAAsB;AAAA1H,eAAA,CAAtB0H,sBAAsB,wBAAAoO,gCAAA/R,iBAAA;EAAA,YAAAA,iBAAA,IAmyB2E2D,uBAAsB;AAAA;AAAA1H,eAAA,CAnyBvH0H,sBAAsB,8BAtHqD/N,EAAE,CAAA6N,iBAAA;EAAAvD,IAAA,EA05BQyD,uBAAsB;EAAAxD,SAAA;EAAAe,SAAA;EAAA8Q,QAAA;EAAAC,YAAA,WAAAC,qCAAAlc,EAAA,EAAAE,GAAA;IAAA,IAAAF,EAAA;MA15BhCJ,EAAE,CAAAuc,UAAA,qBAAAC,mDAAA;QAAA,OA05BQlc,GAAA,CAAAkV,YAAA,CAAa,CAAC;MAAA,CAAO,CAAC,kBAAAiH,gDAAA;QAAA,OAAtBnc,GAAA,CAAAsT,UAAA,CAAW,CAAC;MAAA,CAAS,CAAC,mBAAA8I,iDAAAC,MAAA;QAAA,OAAtBrc,GAAA,CAAAoU,YAAA,CAAAiI,MAAmB,CAAC;MAAA,CAAC,CAAC,qBAAAC,mDAAAD,MAAA;QAAA,OAAtBrc,GAAA,CAAA0T,cAAA,CAAA2I,MAAqB,CAAC;MAAA,CAAD,CAAC,mBAAAE,iDAAA;QAAA,OAAtBvc,GAAA,CAAAqV,YAAA,CAAa,CAAC;MAAA,CAAO,CAAC;IAAA;IAAA,IAAAvV,EAAA;MA15BhCJ,EAAE,CAAAiB,WAAA,iBAAAX,GAAA,CAAAwc,qBAAA,UAAAxc,GAAA,CAAAob,oBAAA,GA05B+B,IAAI,GAAG,UAAU,uBAAApb,GAAA,CAAAob,oBAAA,GAAjB,IAAI,GAAG,MAAM,2BAAApb,GAAA,CAAAsO,SAAA,IAAAtO,GAAA,CAAA4R,YAAA,GAAA5R,GAAA,CAAA4R,YAAA,CAAA3R,EAAA,GAAY,IAAI,mBAAAD,GAAA,CAAAob,oBAAA,GAA7B,IAAI,GAAGpb,GAAA,CAAAsO,SAAA,CAAAmO,QAAA,CAAmB,CAAC,mBAAAzc,GAAA,CAAAob,oBAAA,KAAApb,GAAA,CAAAsO,SAAA,GAAX,IAAI,GAAAtO,GAAA,CAAAwO,YAAA,kBAAAxO,GAAA,CAAAwO,YAAA,CAAAvO,EAAA,mBAAAD,GAAA,CAAAob,oBAAA,GAApB,IAAI,GAAG,SAAS;IAAA;EAAA;EAAAnQ,MAAA;IAAAuD,YAAA;IAAAyL,QAAA;IAAA5H,WAAA;IAAAmK,qBAAA;IAAApB,oBAAA,yDAA4XlZ,gBAAgB;EAAA;EAAAsJ,QAAA;EAAAC,QAAA,GA15B7b/L,EAAE,CAAAgM,kBAAA,CA05BoqC,CAAC8B,+BAA+B,CAAC,GA15BvsC9N,EAAE,CAAAgd,oBAAA;AAAA;AA45BnF;EAAA,QAAAjQ,SAAA,oBAAAA,SAAA,KA55BiF/M,EAAE,CAAAgN,iBAAA,CA45BQe,sBAAsB,EAAc,CAAC;IACpHzD,IAAI,EAAErH,SAAS;IACfgK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mDAAmD;MAC7DG,IAAI,EAAE;QACF,OAAO,EAAE,8BAA8B;QACvC,qBAAqB,EAAE,uBAAuB;QAC9C,aAAa,EAAE,0CAA0C;QACzD,0BAA0B,EAAE,sCAAsC;QAClE,8BAA8B,EAAE,sDAAsD;QACtF,sBAAsB,EAAE,oDAAoD;QAC5E,sBAAsB,EAAE,gEAAgE;QACxF,sBAAsB,EAAE,yCAAyC;QACjE;QACA;QACA,WAAW,EAAE,gBAAgB;QAC7B,QAAQ,EAAE,cAAc;QACxB,SAAS,EAAE,sBAAsB;QACjC,WAAW,EAAE,wBAAwB;QACrC,SAAS,EAAE;MACf,CAAC;MACDvB,QAAQ,EAAE,wBAAwB;MAClCwB,SAAS,EAAE,CAACQ,+BAA+B;IAC/C,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEgB,YAAY,EAAE,CAAC;MACvDxE,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEsN,QAAQ,EAAE,CAAC;MACXjQ,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAE0F,WAAW,EAAE,CAAC;MACdrI,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAE6P,qBAAqB,EAAE,CAAC;MACxBxS,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEyO,oBAAoB,EAAE,CAAC;MACvBpR,IAAI,EAAEvH,KAAK;MACXkK,IAAI,EAAE,CAAC;QAAEgQ,KAAK,EAAE,yBAAyB;QAAExP,SAAS,EAAEjL;MAAiB,CAAC;IAC5E,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0a,qBAAqB,CAAC;AAkB3BC,sBAAA,GAlBKD,qBAAqB;AAAA7W,eAAA,CAArB6W,qBAAqB,wBAAAE,+BAAAhT,iBAAA;EAAA,YAAAA,iBAAA,IAC4E8S,sBAAqB;AAAA;AAAA7W,eAAA,CADtH6W,qBAAqB,8BAr8BsDld,EAAE,CAAAqd,gBAAA;EAAA/S,IAAA,EAu8BqB4S,sBAAqB;EAAAI,OAAA,GAAYzZ,aAAa,EAC1IkC,eAAe,EACfC,eAAe,EACfe,eAAe,EACfgH,sBAAsB,EACtBL,qBAAqB;EAAA6P,OAAA,GAAa7Z,mBAAmB,EACrDqD,eAAe,EACfhB,eAAe,EACfC,eAAe,EACf+H,sBAAsB,EACtBL,qBAAqB;AAAA;AAAArH,eAAA,CAZ3B6W,qBAAqB,8BAr8BsDld,EAAE,CAAAwd,gBAAA;EAAAlQ,SAAA,EAk9BuD,CAACkB,iDAAiD,CAAC;EAAA8O,OAAA,GAAYzZ,aAAa,EAC1MkC,eAAe,EACfC,eAAe,EAAEtC,mBAAmB,EACpCqC,eAAe,EACfC,eAAe;AAAA;AAE3B;EAAA,QAAA+G,SAAA,oBAAAA,SAAA,KAx9BiF/M,EAAE,CAAAgN,iBAAA,CAw9BQkQ,qBAAqB,EAAc,CAAC;IACnH5S,IAAI,EAAE9G,QAAQ;IACdyJ,IAAI,EAAE,CAAC;MACCqQ,OAAO,EAAE,CACLzZ,aAAa,EACbkC,eAAe,EACfC,eAAe,EACfe,eAAe,EACfgH,sBAAsB,EACtBL,qBAAqB,CACxB;MACD6P,OAAO,EAAE,CACL7Z,mBAAmB,EACnBqD,eAAe,EACfhB,eAAe,EACfC,eAAe,EACf+H,sBAAsB,EACtBL,qBAAqB,CACxB;MACDJ,SAAS,EAAE,CAACkB,iDAAiD;IACjE,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASlI,gCAAgC,EAAEG,wCAAwC,EAAE0H,gCAAgC,EAAEI,wCAAwC,EAAEC,iDAAiD,EAAEV,+BAA+B,EAAE/G,eAAe,EAAEmW,qBAAqB,EAAExP,qBAAqB,EAAEzH,4BAA4B,EAAE8H,sBAAsB,EAAExM,SAAS,EAAE0M,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}