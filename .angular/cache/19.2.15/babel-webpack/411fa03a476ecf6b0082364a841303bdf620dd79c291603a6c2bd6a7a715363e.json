{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { InjectionToken, inject, LOCALE_ID } from '@angular/core';\nimport { Subject } from 'rxjs';\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_DATE_LOCALE_FACTORY() {\n  return inject(LOCALE_ID);\n}\nconst NOT_IMPLEMENTED = 'Method not implemented';\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n  constructor() {\n    /** The locale to use for all dates. */\n    _defineProperty(this, \"locale\", void 0);\n    _defineProperty(this, \"_localeChanges\", new Subject());\n    /** A stream that emits when the locale changes. */\n    _defineProperty(this, \"localeChanges\", this._localeChanges);\n  }\n  /**\n   * Sets the time of one date to the time of another.\n   * @param target Date whose time will be set.\n   * @param hours New hours to set on the date object.\n   * @param minutes New minutes to set on the date object.\n   * @param seconds New seconds to set on the date object.\n   */\n  setTime(target, hours, minutes, seconds) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Gets the hours component of the given date.\n   * @param date The date to extract the hours from.\n   */\n  getHours(date) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Gets the minutes component of the given date.\n   * @param date The date to extract the minutes from.\n   */\n  getMinutes(date) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Gets the seconds component of the given date.\n   * @param date The date to extract the seconds from.\n   */\n  getSeconds(date) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Parses a date with a specific time from a user-provided value.\n   * @param value The value to parse.\n   * @param parseFormat The expected format of the value being parsed\n   *     (type is implementation-dependent).\n   */\n  parseTime(value, parseFormat) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Adds an amount of seconds to the specified date.\n   * @param date Date to which to add the seconds.\n   * @param amount Amount of seconds to add to the date.\n   */\n  addSeconds(date, amount) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n  getValidDateOrNull(obj) {\n    return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n  }\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n  deserialize(value) {\n    if (value == null || this.isDateInstance(value) && this.isValid(value)) {\n      return value;\n    }\n    return this.invalid();\n  }\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n  setLocale(locale) {\n    this.locale = locale;\n    this._localeChanges.next();\n  }\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n  compareDate(first, second) {\n    return this.getYear(first) - this.getYear(second) || this.getMonth(first) - this.getMonth(second) || this.getDate(first) - this.getDate(second);\n  }\n  /**\n   * Compares the time values of two dates.\n   * @param first First date to compare.\n   * @param second Second date to compare.\n   * @returns 0 if the times are equal, a number less than 0 if the first time is earlier,\n   *     a number greater than 0 if the first time is later.\n   */\n  compareTime(first, second) {\n    return this.getHours(first) - this.getHours(second) || this.getMinutes(first) - this.getMinutes(second) || this.getSeconds(first) - this.getSeconds(second);\n  }\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameDate(first, second) {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n  /**\n   * Checks if the times of two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the times of the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameTime(first, second) {\n    if (first && second) {\n      const firstValid = this.isValid(first);\n      const secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareTime(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n  clampDate(date, min, max) {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n    return date;\n  }\n}\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\nexport { DateAdapter as D, MAT_DATE_LOCALE as M, MAT_DATE_FORMATS as a, MAT_DATE_LOCALE_FACTORY as b };\n//# sourceMappingURL=date-formats-K6TQue-Y.mjs.map", "map": {"version": 3, "names": ["InjectionToken", "inject", "LOCALE_ID", "Subject", "MAT_DATE_LOCALE", "providedIn", "factory", "MAT_DATE_LOCALE_FACTORY", "NOT_IMPLEMENTED", "DateAdapter", "constructor", "_defineProperty", "_localeChanges", "setTime", "target", "hours", "minutes", "seconds", "Error", "getHours", "date", "getMinutes", "getSeconds", "parseTime", "value", "parseFormat", "addSeconds", "amount", "getValidDateOrNull", "obj", "isDateInstance", "<PERSON><PERSON><PERSON><PERSON>", "deserialize", "invalid", "setLocale", "locale", "next", "compareDate", "first", "second", "getYear", "getMonth", "getDate", "compareTime", "sameDate", "firstValid", "second<PERSON><PERSON><PERSON>", "sameTime", "clampDate", "min", "max", "MAT_DATE_FORMATS", "D", "M", "a", "b"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/date-formats-K6TQue-Y.mjs"], "sourcesContent": ["import { InjectionToken, inject, LOCALE_ID } from '@angular/core';\nimport { Subject } from 'rxjs';\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n    providedIn: 'root',\n    factory: MAT_DATE_LOCALE_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_DATE_LOCALE_FACTORY() {\n    return inject(LOCALE_ID);\n}\nconst NOT_IMPLEMENTED = 'Method not implemented';\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n    /** The locale to use for all dates. */\n    locale;\n    _localeChanges = new Subject();\n    /** A stream that emits when the locale changes. */\n    localeChanges = this._localeChanges;\n    /**\n     * Sets the time of one date to the time of another.\n     * @param target Date whose time will be set.\n     * @param hours New hours to set on the date object.\n     * @param minutes New minutes to set on the date object.\n     * @param seconds New seconds to set on the date object.\n     */\n    setTime(target, hours, minutes, seconds) {\n        throw new Error(NOT_IMPLEMENTED);\n    }\n    /**\n     * Gets the hours component of the given date.\n     * @param date The date to extract the hours from.\n     */\n    getHours(date) {\n        throw new Error(NOT_IMPLEMENTED);\n    }\n    /**\n     * Gets the minutes component of the given date.\n     * @param date The date to extract the minutes from.\n     */\n    getMinutes(date) {\n        throw new Error(NOT_IMPLEMENTED);\n    }\n    /**\n     * Gets the seconds component of the given date.\n     * @param date The date to extract the seconds from.\n     */\n    getSeconds(date) {\n        throw new Error(NOT_IMPLEMENTED);\n    }\n    /**\n     * Parses a date with a specific time from a user-provided value.\n     * @param value The value to parse.\n     * @param parseFormat The expected format of the value being parsed\n     *     (type is implementation-dependent).\n     */\n    parseTime(value, parseFormat) {\n        throw new Error(NOT_IMPLEMENTED);\n    }\n    /**\n     * Adds an amount of seconds to the specified date.\n     * @param date Date to which to add the seconds.\n     * @param amount Amount of seconds to add to the date.\n     */\n    addSeconds(date, amount) {\n        throw new Error(NOT_IMPLEMENTED);\n    }\n    /**\n     * Given a potential date object, returns that same date object if it is\n     * a valid date, or `null` if it's not a valid date.\n     * @param obj The object to check.\n     * @returns A date or `null`.\n     */\n    getValidDateOrNull(obj) {\n        return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n    }\n    /**\n     * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n     * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n     * string). The default implementation does not allow any deserialization, it simply checks that\n     * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n     * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n     * support passing values from your backend directly to these properties by overriding this method\n     * to also deserialize the format used by your backend.\n     * @param value The value to be deserialized into a date object.\n     * @returns The deserialized date object, either a valid date, null if the value can be\n     *     deserialized into a null date (e.g. the empty string), or an invalid date.\n     */\n    deserialize(value) {\n        if (value == null || (this.isDateInstance(value) && this.isValid(value))) {\n            return value;\n        }\n        return this.invalid();\n    }\n    /**\n     * Sets the locale used for all dates.\n     * @param locale The new locale.\n     */\n    setLocale(locale) {\n        this.locale = locale;\n        this._localeChanges.next();\n    }\n    /**\n     * Compares two dates.\n     * @param first The first date to compare.\n     * @param second The second date to compare.\n     * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n     *     a number greater than 0 if the first date is later.\n     */\n    compareDate(first, second) {\n        return (this.getYear(first) - this.getYear(second) ||\n            this.getMonth(first) - this.getMonth(second) ||\n            this.getDate(first) - this.getDate(second));\n    }\n    /**\n     * Compares the time values of two dates.\n     * @param first First date to compare.\n     * @param second Second date to compare.\n     * @returns 0 if the times are equal, a number less than 0 if the first time is earlier,\n     *     a number greater than 0 if the first time is later.\n     */\n    compareTime(first, second) {\n        return (this.getHours(first) - this.getHours(second) ||\n            this.getMinutes(first) - this.getMinutes(second) ||\n            this.getSeconds(first) - this.getSeconds(second));\n    }\n    /**\n     * Checks if two dates are equal.\n     * @param first The first date to check.\n     * @param second The second date to check.\n     * @returns Whether the two dates are equal.\n     *     Null dates are considered equal to other null dates.\n     */\n    sameDate(first, second) {\n        if (first && second) {\n            let firstValid = this.isValid(first);\n            let secondValid = this.isValid(second);\n            if (firstValid && secondValid) {\n                return !this.compareDate(first, second);\n            }\n            return firstValid == secondValid;\n        }\n        return first == second;\n    }\n    /**\n     * Checks if the times of two dates are equal.\n     * @param first The first date to check.\n     * @param second The second date to check.\n     * @returns Whether the times of the two dates are equal.\n     *     Null dates are considered equal to other null dates.\n     */\n    sameTime(first, second) {\n        if (first && second) {\n            const firstValid = this.isValid(first);\n            const secondValid = this.isValid(second);\n            if (firstValid && secondValid) {\n                return !this.compareTime(first, second);\n            }\n            return firstValid == secondValid;\n        }\n        return first == second;\n    }\n    /**\n     * Clamp the given date between min and max dates.\n     * @param date The date to clamp.\n     * @param min The minimum value to allow. If null or omitted no min is enforced.\n     * @param max The maximum value to allow. If null or omitted no max is enforced.\n     * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n     *     otherwise `date`.\n     */\n    clampDate(date, min, max) {\n        if (min && this.compareDate(date, min) < 0) {\n            return min;\n        }\n        if (max && this.compareDate(date, max) > 0) {\n            return max;\n        }\n        return date;\n    }\n}\n\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n\nexport { DateAdapter as D, MAT_DATE_LOCALE as M, MAT_DATE_FORMATS as a, MAT_DATE_LOCALE_FACTORY as b };\n//# sourceMappingURL=date-formats-K6TQue-Y.mjs.map\n"], "mappings": ";AAAA,SAASA,cAAc,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACjE,SAASC,OAAO,QAAQ,MAAM;;AAE9B;AACA,MAAMC,eAAe,GAAG,IAAIJ,cAAc,CAAC,iBAAiB,EAAE;EAC1DK,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,uBAAuBA,CAAA,EAAG;EAC/B,OAAON,MAAM,CAACC,SAAS,CAAC;AAC5B;AACA,MAAMM,eAAe,GAAG,wBAAwB;AAChD;AACA,MAAMC,WAAW,CAAC;EAAAC,YAAA;IACd;IAAAC,eAAA;IAAAA,eAAA,yBAEiB,IAAIR,OAAO,CAAC,CAAC;IAC9B;IAAAQ,eAAA,wBACgB,IAAI,CAACC,cAAc;EAAA;EACnC;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;IACrC,MAAM,IAAIC,KAAK,CAACV,eAAe,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACIW,QAAQA,CAACC,IAAI,EAAE;IACX,MAAM,IAAIF,KAAK,CAACV,eAAe,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACIa,UAAUA,CAACD,IAAI,EAAE;IACb,MAAM,IAAIF,KAAK,CAACV,eAAe,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACIc,UAAUA,CAACF,IAAI,EAAE;IACb,MAAM,IAAIF,KAAK,CAACV,eAAe,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIe,SAASA,CAACC,KAAK,EAAEC,WAAW,EAAE;IAC1B,MAAM,IAAIP,KAAK,CAACV,eAAe,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACIkB,UAAUA,CAACN,IAAI,EAAEO,MAAM,EAAE;IACrB,MAAM,IAAIT,KAAK,CAACV,eAAe,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoB,kBAAkBA,CAACC,GAAG,EAAE;IACpB,OAAO,IAAI,CAACC,cAAc,CAACD,GAAG,CAAC,IAAI,IAAI,CAACE,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,IAAI;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,WAAWA,CAACR,KAAK,EAAE;IACf,IAAIA,KAAK,IAAI,IAAI,IAAK,IAAI,CAACM,cAAc,CAACN,KAAK,CAAC,IAAI,IAAI,CAACO,OAAO,CAACP,KAAK,CAAE,EAAE;MACtE,OAAOA,KAAK;IAChB;IACA,OAAO,IAAI,CAACS,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;EACIC,SAASA,CAACC,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACvB,cAAc,CAACwB,IAAI,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACvB,OAAQ,IAAI,CAACC,OAAO,CAACF,KAAK,CAAC,GAAG,IAAI,CAACE,OAAO,CAACD,MAAM,CAAC,IAC9C,IAAI,CAACE,QAAQ,CAACH,KAAK,CAAC,GAAG,IAAI,CAACG,QAAQ,CAACF,MAAM,CAAC,IAC5C,IAAI,CAACG,OAAO,CAACJ,KAAK,CAAC,GAAG,IAAI,CAACI,OAAO,CAACH,MAAM,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,WAAWA,CAACL,KAAK,EAAEC,MAAM,EAAE;IACvB,OAAQ,IAAI,CAACpB,QAAQ,CAACmB,KAAK,CAAC,GAAG,IAAI,CAACnB,QAAQ,CAACoB,MAAM,CAAC,IAChD,IAAI,CAAClB,UAAU,CAACiB,KAAK,CAAC,GAAG,IAAI,CAACjB,UAAU,CAACkB,MAAM,CAAC,IAChD,IAAI,CAACjB,UAAU,CAACgB,KAAK,CAAC,GAAG,IAAI,CAAChB,UAAU,CAACiB,MAAM,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,QAAQA,CAACN,KAAK,EAAEC,MAAM,EAAE;IACpB,IAAID,KAAK,IAAIC,MAAM,EAAE;MACjB,IAAIM,UAAU,GAAG,IAAI,CAACd,OAAO,CAACO,KAAK,CAAC;MACpC,IAAIQ,WAAW,GAAG,IAAI,CAACf,OAAO,CAACQ,MAAM,CAAC;MACtC,IAAIM,UAAU,IAAIC,WAAW,EAAE;QAC3B,OAAO,CAAC,IAAI,CAACT,WAAW,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC3C;MACA,OAAOM,UAAU,IAAIC,WAAW;IACpC;IACA,OAAOR,KAAK,IAAIC,MAAM;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIQ,QAAQA,CAACT,KAAK,EAAEC,MAAM,EAAE;IACpB,IAAID,KAAK,IAAIC,MAAM,EAAE;MACjB,MAAMM,UAAU,GAAG,IAAI,CAACd,OAAO,CAACO,KAAK,CAAC;MACtC,MAAMQ,WAAW,GAAG,IAAI,CAACf,OAAO,CAACQ,MAAM,CAAC;MACxC,IAAIM,UAAU,IAAIC,WAAW,EAAE;QAC3B,OAAO,CAAC,IAAI,CAACH,WAAW,CAACL,KAAK,EAAEC,MAAM,CAAC;MAC3C;MACA,OAAOM,UAAU,IAAIC,WAAW;IACpC;IACA,OAAOR,KAAK,IAAIC,MAAM;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIS,SAASA,CAAC5B,IAAI,EAAE6B,GAAG,EAAEC,GAAG,EAAE;IACtB,IAAID,GAAG,IAAI,IAAI,CAACZ,WAAW,CAACjB,IAAI,EAAE6B,GAAG,CAAC,GAAG,CAAC,EAAE;MACxC,OAAOA,GAAG;IACd;IACA,IAAIC,GAAG,IAAI,IAAI,CAACb,WAAW,CAACjB,IAAI,EAAE8B,GAAG,CAAC,GAAG,CAAC,EAAE;MACxC,OAAOA,GAAG;IACd;IACA,OAAO9B,IAAI;EACf;AACJ;AAEA,MAAM+B,gBAAgB,GAAG,IAAInD,cAAc,CAAC,kBAAkB,CAAC;AAE/D,SAASS,WAAW,IAAI2C,CAAC,EAAEhD,eAAe,IAAIiD,CAAC,EAAEF,gBAAgB,IAAIG,CAAC,EAAE/C,uBAAuB,IAAIgD,CAAC;AACpG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}