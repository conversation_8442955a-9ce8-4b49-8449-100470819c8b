{"ast": null, "code": "var _SwuiTdJackpotWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./jackpot.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SwuiTdColorfulLabelsWidgetComponent } from '../colorful-labels/colorful-labels.widget';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdJackpotWidgetComponent = (_SwuiTdJackpotWidgetComponent = class SwuiTdJackpotWidgetComponent extends SwuiTdColorfulLabelsWidgetComponent {\n  constructor(config) {\n    super(config);\n    this.jackpots = [];\n    this.palettes = ['pink', 'violet', 'purple', 'indigo'];\n    this.action = config.action;\n    this.field = config.field;\n    this.row = config.row;\n    if (this.row.settings && 'jackpotId' in this.row.settings) {\n      if (typeof this.row.settings.jackpotId === 'string') {\n        this.row.settings.jackpotId = [this.row.settings.jackpotId];\n      }\n      this.row.settings.jackpotId.forEach((jackpotId, idx) => {\n        let jackpotType = this.row.settings.jackpotType[idx];\n        this.jackpots.push({\n          jackpotId,\n          jackpotType\n        });\n      });\n    }\n  }\n  manageClicked(event) {\n    event.preventDefault();\n    this.action.emit({\n      field: this.field,\n      row: this.row,\n      payload: {\n        showManage: true\n      }\n    });\n  }\n}, _SwuiTdJackpotWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdJackpotWidgetComponent);\nSwuiTdJackpotWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-jackpot-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdJackpotWidgetComponent);\nexport { SwuiTdJackpotWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "SwuiTdColorfulLabelsWidgetComponent", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdJackpotWidgetComponent", "_SwuiTdJackpotWidgetComponent", "constructor", "config", "jackpots", "palettes", "action", "field", "row", "settings", "jackpotId", "for<PERSON>ach", "idx", "jackpotType", "push", "manageClicked", "event", "preventDefault", "emit", "payload", "showManage", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/jackpot/jackpot.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./jackpot.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SwuiTdColorfulLabelsWidgetComponent } from '../colorful-labels/colorful-labels.widget';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdJackpotWidgetComponent = class SwuiTdJackpotWidgetComponent extends SwuiTdColorfulLabelsWidgetComponent {\n    constructor(config) {\n        super(config);\n        this.jackpots = [];\n        this.palettes = ['pink', 'violet', 'purple', 'indigo'];\n        this.action = config.action;\n        this.field = config.field;\n        this.row = config.row;\n        if (this.row.settings && ('jackpotId' in this.row.settings)) {\n            if (typeof this.row.settings.jackpotId === 'string') {\n                this.row.settings.jackpotId = [this.row.settings.jackpotId];\n            }\n            this.row.settings.jackpotId.forEach((jackpotId, idx) => {\n                let jackpotType = this.row.settings.jackpotType[idx];\n                this.jackpots.push({ jackpotId, jackpotType });\n            });\n        }\n    }\n    manageClicked(event) {\n        event.preventDefault();\n        this.action.emit({\n            field: this.field,\n            row: this.row,\n            payload: { showManage: true }\n        });\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdJackpotWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-jackpot-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdJackpotWidgetComponent);\nexport { SwuiTdJackpotWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,mCAAmC,QAAQ,2CAA2C;AAC/F,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,4BAA4B,IAAAC,6BAAA,GAAG,MAAMD,4BAA4B,SAASF,mCAAmC,CAAC;EAC9GI,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAACA,MAAM,CAAC;IACb,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACtD,IAAI,CAACC,MAAM,GAAGH,MAAM,CAACG,MAAM;IAC3B,IAAI,CAACC,KAAK,GAAGJ,MAAM,CAACI,KAAK;IACzB,IAAI,CAACC,GAAG,GAAGL,MAAM,CAACK,GAAG;IACrB,IAAI,IAAI,CAACA,GAAG,CAACC,QAAQ,IAAK,WAAW,IAAI,IAAI,CAACD,GAAG,CAACC,QAAS,EAAE;MACzD,IAAI,OAAO,IAAI,CAACD,GAAG,CAACC,QAAQ,CAACC,SAAS,KAAK,QAAQ,EAAE;QACjD,IAAI,CAACF,GAAG,CAACC,QAAQ,CAACC,SAAS,GAAG,CAAC,IAAI,CAACF,GAAG,CAACC,QAAQ,CAACC,SAAS,CAAC;MAC/D;MACA,IAAI,CAACF,GAAG,CAACC,QAAQ,CAACC,SAAS,CAACC,OAAO,CAAC,CAACD,SAAS,EAAEE,GAAG,KAAK;QACpD,IAAIC,WAAW,GAAG,IAAI,CAACL,GAAG,CAACC,QAAQ,CAACI,WAAW,CAACD,GAAG,CAAC;QACpD,IAAI,CAACR,QAAQ,CAACU,IAAI,CAAC;UAAEJ,SAAS;UAAEG;QAAY,CAAC,CAAC;MAClD,CAAC,CAAC;IACN;EACJ;EACAE,aAAaA,CAACC,KAAK,EAAE;IACjBA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACX,MAAM,CAACY,IAAI,CAAC;MACbX,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,GAAG,EAAE,IAAI,CAACA,GAAG;MACbW,OAAO,EAAE;QAAEC,UAAU,EAAE;MAAK;IAChC,CAAC,CAAC;EACN;AAIJ,CAAC,EAHYnB,6BAAA,CAAKoB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEzB,MAAM;IAAE4B,IAAI,EAAE,CAAC1B,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,6BAAA,CACJ;AACDD,4BAA4B,GAAGN,UAAU,CAAC,CACtCE,SAAS,CAAC;EACN8B,QAAQ,EAAE,4BAA4B;EACtCC,QAAQ,EAAEhC,oBAAoB;EAC9BiC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAE5B,4BAA4B,CAAC;AAChC,SAASA,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}