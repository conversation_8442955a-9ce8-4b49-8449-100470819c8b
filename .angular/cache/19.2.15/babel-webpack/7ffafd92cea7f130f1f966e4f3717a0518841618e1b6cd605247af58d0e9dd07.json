{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { SwuiGridComponent } from './swui-grid.component';\nimport { CommonModule } from '@angular/common';\nimport { SwuiGridModule } from './swui-grid.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { MockUrlHandler, SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';\nimport { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwDexieModule } from '../services/sw-dexie/sw-dexie.module';\nimport { DEXI_CONFIG } from '../services/sw-dexie/sw-dexie.service';\nimport { ReplaySubject } from 'rxjs';\ndescribe('SwuiGridComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [CommonModule, TranslateModule.forRoot(), SwuiGridModule.forRoot(), RouterTestingModule, SwDexieModule.forRoot('test')],\n      providers: [{\n        provide: SwuiGridUrlHandlerService,\n        useClass: MockUrlHandler\n      }, {\n        provide: SwHubAuthService,\n        useValue: {\n          logged: new ReplaySubject(),\n          username: 'username-test',\n          entityKey: 'entityKey-test'\n        }\n      }, {\n        provide: DEXI_CONFIG,\n        useValue: 'hubName'\n      }]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiGridComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "SwuiGridComponent", "CommonModule", "SwuiGridModule", "TranslateModule", "RouterTestingModule", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SwuiGridUrlHandlerService", "SwHubAuthService", "SwDexieModule", "DEXI_CONFIG", "ReplaySubject", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "forRoot", "providers", "provide", "useClass", "useValue", "logged", "username", "entityKey", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\n\nimport { SwuiGridComponent } from './swui-grid.component';\nimport { CommonModule } from '@angular/common';\nimport { SwuiGridModule } from './swui-grid.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { MockUrlHandler, SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';\nimport { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwDexieModule } from '../services/sw-dexie/sw-dexie.module';\nimport { DEXI_CONFIG } from '../services/sw-dexie/sw-dexie.service';\nimport { ReplaySubject } from 'rxjs';\n\nexport interface TestTableDataItem {\n  [key: string]: any;\n}\n\ndescribe('SwuiGridComponent', () => {\n  let component: SwuiGridComponent<TestTableDataItem>;\n  let fixture: ComponentFixture<SwuiGridComponent<TestTableDataItem>>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [\n        CommonModule,\n        TranslateModule.forRoot(),\n        SwuiGridModule.forRoot(),\n        RouterTestingModule,\n        SwDexieModule.forRoot('test')\n      ],\n      providers: [\n        { provide: SwuiGridUrlHandlerService, useClass: MockUrlHandler },\n        { provide: SwHubAuthService, useValue: {\n            logged: new ReplaySubject<void>(),\n            username: 'username-test',\n            entityKey: 'entityKey-test'\n          } },\n        { provide: DEXI_CONFIG, useValue: 'hubName' },\n      ]\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent<SwuiGridComponent<TestTableDataItem>>(SwuiGridComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,cAAc,EAAEC,yBAAyB,QAAQ,iCAAiC;AAC3F,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,WAAW,QAAQ,uCAAuC;AACnE,SAASC,aAAa,QAAQ,MAAM;AAMpCC,QAAQ,CAAC,mBAAmB,EAAE,MAAK;EACjC,IAAIC,SAA+C;EACnD,IAAIC,OAA+D;EAEnEC,UAAU,CAACf,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACiB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPf,YAAY,EACZE,eAAe,CAACc,OAAO,EAAE,EACzBf,cAAc,CAACe,OAAO,EAAE,EACxBb,mBAAmB,EACnBI,aAAa,CAACS,OAAO,CAAC,MAAM,CAAC,CAC9B;MACDC,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEb,yBAAyB;QAAEc,QAAQ,EAAEf;MAAc,CAAE,EAChE;QAAEc,OAAO,EAAEZ,gBAAgB;QAAEc,QAAQ,EAAE;UACnCC,MAAM,EAAE,IAAIZ,aAAa,EAAQ;UACjCa,QAAQ,EAAE,eAAe;UACzBC,SAAS,EAAE;;MACZ,CAAE,EACL;QAAEL,OAAO,EAAEV,WAAW;QAAEY,QAAQ,EAAE;MAAS,CAAE;KAEhD,CAAC,CACCI,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHX,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGf,OAAO,CAAC4B,eAAe,CAAuC1B,iBAAiB,CAAC;IAC1FY,SAAS,GAAGC,OAAO,CAACc,iBAAiB;IACrCd,OAAO,CAACe,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAClB,SAAS,CAAC,CAACmB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}