{"ast": null, "code": "export var dateTimestampProvider = {\n  now: function () {\n    return (dateTimestampProvider.delegate || Date).now();\n  },\n  delegate: undefined\n};\n//# sourceMappingURL=dateTimestampProvider.js.map", "map": {"version": 3, "names": ["dateTimestampProvider", "now", "delegate", "Date", "undefined"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/dateTimestampProvider.js"], "sourcesContent": ["export var dateTimestampProvider = {\n    now: function () {\n        return (dateTimestampProvider.delegate || Date).now();\n    },\n    delegate: undefined,\n};\n//# sourceMappingURL=dateTimestampProvider.js.map"], "mappings": "AAAA,OAAO,IAAIA,qBAAqB,GAAG;EAC/BC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACb,OAAO,CAACD,qBAAqB,CAACE,QAAQ,IAAIC,IAAI,EAAEF,GAAG,CAAC,CAAC;EACzD,CAAC;EACDC,QAAQ,EAAEE;AACd,CAAC;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}