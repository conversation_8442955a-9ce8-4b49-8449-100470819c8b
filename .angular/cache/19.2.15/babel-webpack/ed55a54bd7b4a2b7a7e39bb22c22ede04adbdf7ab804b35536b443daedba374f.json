{"ast": null, "code": "var _SwHubInitService;\nimport { __decorate } from \"tslib\";\nimport { Inject, Injectable } from '@angular/core';\nimport { NavigationEnd, Router } from '@angular/router';\nimport { TranslateService } from '@ngx-translate/core';\nimport { BehaviorSubject, fromEvent, merge, Subject } from 'rxjs';\nimport { filter, map, share, take, takeUntil, tap, throttleTime } from 'rxjs/operators';\nimport { SwuiSidebarService } from '../../swui-sidebar/swui-sidebar.service';\nimport { SettingsService } from '../settings/settings.service';\nimport { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';\nimport { SwHubEntityService } from '../sw-hub-entity/sw-hub-entity.service';\nimport { isHubMessage, TYPES } from './sw-hub-init.model';\nimport { SWUI_HUB_MESSAGE_CONFIG } from './sw-hub-init.token';\nlet SwHubInitService = (_SwHubInitService = class SwHubInitService {\n  constructor(router, translate, authService, settingsService, entityService, configService, sidebarService, config) {\n    this.router = router;\n    this.translate = translate;\n    this.authService = authService;\n    this.settingsService = settingsService;\n    this.entityService = entityService;\n    this.configService = configService;\n    this.sidebarService = sidebarService;\n    this.config = config;\n    this.navigation$ = new BehaviorSubject(null);\n    this.destroyed$ = new Subject();\n    this.ready$ = new Subject();\n    this.ready = false;\n    const source = fromEvent(window, 'message').pipe(filter(event => isHubMessage(event)), map(({\n      data\n    }) => data), filter(({\n      target\n    }) => target === config.name || target === '*'), tap(data => console.log('[hub] getMessage', data)), share());\n    this.message$ = source.pipe(filter(({\n      type\n    }) => ![TYPES.BRIDGE_LOADED, TYPES.LOGIN, TYPES.LOGOUT].includes(type)));\n    source.pipe(takeUntil(this.destroyed$)).subscribe(({\n      type,\n      body\n    }) => {\n      switch (type) {\n        case TYPES.BRIDGE_LOADED:\n          const {\n            accessToken,\n            lang,\n            settings,\n            navigation,\n            entityId,\n            grantedPermissions,\n            twoFactor\n          } = body;\n          this.ready = true;\n          if (this.win) {\n            this.ready$.next(this.win);\n          }\n          this.authService.setToken(accessToken, grantedPermissions, twoFactor);\n          if (lang) {\n            this.translate.use(lang);\n          }\n          if (settings) {\n            this.settingsService.use(settings);\n          }\n          if (navigation) {\n            this.navigation$.next(navigation);\n          }\n          if (entityId) {\n            this.entityService.useByPath(entityId);\n          }\n          break;\n        case TYPES.TOKEN:\n        case TYPES.LOGIN:\n          this.authService.setToken(body.accessToken, body.grantedPermissions, body.twoFactor);\n          break;\n        case TYPES.LOGOUT:\n          this.authService.logout();\n          if (this.configService.loginUrl) {\n            location.href = this.configService.loginUrl;\n          }\n          break;\n        case TYPES.LOCALE_CHANGED:\n          this.translate.use(body);\n          break;\n        case TYPES.APP_SETTINGS_CHANGED:\n          this.settingsService.use(body);\n          break;\n        case TYPES.ENTITY_ID_CHANGED:\n          this.entityService.use(body);\n          break;\n        case TYPES.SIDEBAR_COLLAPSED:\n          this.sidebarService.isCollapsed.next(body === true);\n          break;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  init() {\n    this.translate.addLangs(this.config.langs.map(({\n      id\n    }) => id));\n    if (this.config.defaultLang) {\n      this.translate.setDefaultLang(this.config.defaultLang);\n    }\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), map(event => event), map(event => {\n      var _this$config;\n      const foundException = (_this$config = this.config) === null || _this$config === void 0 || (_this$config = _this$config.lastLocationExceptions) === null || _this$config === void 0 ? void 0 : _this$config.find(exception => event.url.startsWith(exception.url));\n      return foundException ? foundException.replaceTo : event.url;\n    }), takeUntil(this.destroyed$)).subscribe(url => {\n      const body = {\n        hub: this.config.name,\n        url: url\n      };\n      this.postMessage(TYPES.LOCATION_CHANGED, body);\n    });\n    if (this.configService.bridge) {\n      this.create(this.configService.bridge);\n    }\n    this.postMessage(TYPES.HUB_LOADED);\n  }\n  initInactivityWatcher() {\n    merge(fromEvent(window, 'mousemove'), fromEvent(window, 'keypress')).pipe(throttleTime(10000), takeUntil(this.destroyed$)).subscribe(() => {\n      this.postMessage(TYPES.USER_ACTIVITY, new Date());\n    });\n    this.postMessage(TYPES.USER_ACTIVITY, new Date());\n  }\n  sendLogin(accessToken, grantedPermissions, twoFactor) {\n    this.postMessage(TYPES.LOGIN, {\n      accessToken,\n      grantedPermissions,\n      twoFactor\n    });\n  }\n  sendLogout() {\n    this.postMessage(TYPES.LOGOUT);\n  }\n  sendLocale(lang) {\n    this.postMessage(TYPES.LOCALE_CHANGED, lang);\n  }\n  sendAppSettings(settings) {\n    this.postMessage(TYPES.APP_SETTINGS_CHANGED, settings);\n  }\n  sendEntityId(entityId) {\n    this.postMessage(TYPES.ENTITY_ID_CHANGED, entityId);\n  }\n  sendTokenExpired(body) {\n    this.postMessage(TYPES.TOKEN_EXPIRED, body);\n  }\n  sendUnknownUserLocation(url) {\n    this.postMessage(TYPES.UNKNOWN_LOCATION, url);\n  }\n  postMessage(type, body) {\n    const message = {\n      target: 'bridge',\n      initiator: this.config.name,\n      type,\n      body\n    };\n    if (this.win && this.ready) {\n      this.win.postMessage(message, '*');\n    } else {\n      console.log('[hub] queue message', type);\n      this.ready$.pipe(take(1)).subscribe(win => {\n        console.log('[hub] post queued message', type);\n        win.postMessage(message, '*');\n      });\n    }\n  }\n  create(url) {\n    const el = document.createElement('iframe');\n    el.src = url;\n    el.style.display = 'none';\n    const frame = document.body.appendChild(el);\n    console.log('[hub] frame created');\n    frame.onload = () => {\n      if (frame.contentWindow) {\n        console.log('[hub] frame loaded');\n        this.win = frame.contentWindow;\n        if (this.ready) {\n          this.ready$.next(this.win);\n        }\n      }\n    };\n  }\n}, _SwHubInitService.ctorParameters = () => [{\n  type: Router\n}, {\n  type: TranslateService\n}, {\n  type: SwHubAuthService\n}, {\n  type: SettingsService\n}, {\n  type: SwHubEntityService\n}, {\n  type: SwHubConfigService\n}, {\n  type: SwuiSidebarService\n}, {\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_HUB_MESSAGE_CONFIG]\n  }]\n}], _SwHubInitService);\nSwHubInitService = __decorate([Injectable()], SwHubInitService);\nexport { SwHubInitService };", "map": {"version": 3, "names": ["Inject", "Injectable", "NavigationEnd", "Router", "TranslateService", "BehaviorSubject", "fromEvent", "merge", "Subject", "filter", "map", "share", "take", "takeUntil", "tap", "throttleTime", "SwuiSidebarService", "SettingsService", "SwHubAuthService", "SwHubConfigService", "SwHubEntityService", "isHubMessage", "TYPES", "SWUI_HUB_MESSAGE_CONFIG", "SwHubInitService", "_SwHubInitService", "constructor", "router", "translate", "authService", "settingsService", "entityService", "configService", "sidebarService", "config", "navigation$", "destroyed$", "ready$", "ready", "source", "window", "pipe", "event", "data", "target", "name", "console", "log", "message$", "type", "BRIDGE_LOADED", "LOGIN", "LOGOUT", "includes", "subscribe", "body", "accessToken", "lang", "settings", "navigation", "entityId", "grantedPermissions", "twoFactor", "win", "next", "setToken", "use", "useByPath", "TOKEN", "logout", "loginUrl", "location", "href", "LOCALE_CHANGED", "APP_SETTINGS_CHANGED", "ENTITY_ID_CHANGED", "SIDEBAR_COLLAPSED", "isCollapsed", "ngOnDestroy", "undefined", "complete", "init", "addLangs", "langs", "id", "defaultLang", "setDefaultLang", "events", "_this$config", "foundException", "lastLocationExceptions", "find", "exception", "url", "startsWith", "replaceTo", "hub", "postMessage", "LOCATION_CHANGED", "bridge", "create", "HUB_LOADED", "initInactivityWatcher", "USER_ACTIVITY", "Date", "send<PERSON><PERSON><PERSON>", "sendLogout", "sendLocale", "sendAppSettings", "sendEntityId", "sendTokenExpired", "TOKEN_EXPIRED", "sendUnknownUserLocation", "UNKNOWN_LOCATION", "message", "initiator", "el", "document", "createElement", "src", "style", "display", "frame", "append<PERSON><PERSON><PERSON>", "onload", "contentWindow", "args", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-init/sw-hub-init.service.ts"], "sourcesContent": ["import { Inject, Injectable, OnDestroy } from '@angular/core';\nimport { NavigationEnd, Router } from '@angular/router';\nimport { TranslateService } from '@ngx-translate/core';\nimport { BehaviorSubject, fromEvent, merge, Observable, Subject } from 'rxjs';\nimport { filter, map, share, take, takeUntil, tap, throttleTime } from 'rxjs/operators';\nimport { SwuiSidebarService } from '../../swui-sidebar/swui-sidebar.service';\nimport { AppSettings } from '../settings/app-settings';\nimport { SettingsService } from '../settings/settings.service';\nimport { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';\nimport { SwHubEntityService } from '../sw-hub-entity/sw-hub-entity.service';\nimport {\n  BridgeLoadedMessageBody, HubMessage, isHubMessage, NavigationHistoryBody, SwHubMessageModuleConfig,\n  TokenExpiredMessageBody, TYPES\n} from './sw-hub-init.model';\nimport { SWUI_HUB_MESSAGE_CONFIG } from './sw-hub-init.token';\n\n@Injectable()\nexport class SwHubInitService implements OnDestroy {\n  readonly message$: Observable<HubMessage>;\n  readonly navigation$ = new BehaviorSubject<NavigationHistoryBody | null>(null);\n\n  private readonly destroyed$ = new Subject<void>();\n  private readonly ready$ = new Subject<Window>();\n  private ready = false;\n  private win?: Window;\n\n  constructor(\n    private readonly router: Router,\n    private readonly translate: TranslateService,\n    private readonly authService: SwHubAuthService,\n    private readonly settingsService: SettingsService,\n    private readonly entityService: SwHubEntityService,\n    private readonly configService: SwHubConfigService,\n    private readonly sidebarService: SwuiSidebarService,\n    @Inject(SWUI_HUB_MESSAGE_CONFIG) private readonly config: SwHubMessageModuleConfig\n  ) {\n    const source = fromEvent<MessageEvent>(window, 'message').pipe(\n      filter(event => isHubMessage(event)),\n      map<MessageEvent, HubMessage>(( { data } ) => data),\n      filter(( { target } ) => target === config.name || target === '*'),\n      tap(data => console.log('[hub] getMessage', data)),\n      share()\n    );\n    this.message$ = source.pipe(\n      filter(( { type } ) => ![TYPES.BRIDGE_LOADED, TYPES.LOGIN, TYPES.LOGOUT].includes(type))\n    );\n\n    source.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(( { type, body } ) => {\n      switch (type) {\n        case TYPES.BRIDGE_LOADED:\n          const { accessToken, lang, settings, navigation, entityId, grantedPermissions, twoFactor } = body as BridgeLoadedMessageBody;\n          this.ready = true;\n          if (this.win) {\n            this.ready$.next(this.win);\n          }\n          this.authService.setToken(accessToken, grantedPermissions, twoFactor);\n          if (lang) {\n            this.translate.use(lang);\n          }\n          if (settings) {\n            this.settingsService.use(settings);\n          }\n          if (navigation) {\n            this.navigation$.next(navigation);\n          }\n          if (entityId) {\n            this.entityService.useByPath(entityId);\n          }\n          break;\n        case TYPES.TOKEN:\n        case TYPES.LOGIN:\n          this.authService.setToken(body.accessToken, body.grantedPermissions, body.twoFactor);\n          break;\n        case TYPES.LOGOUT:\n          this.authService.logout();\n          if (this.configService.loginUrl) {\n            location.href = this.configService.loginUrl;\n          }\n          break;\n        case TYPES.LOCALE_CHANGED:\n          this.translate.use(body);\n          break;\n        case TYPES.APP_SETTINGS_CHANGED:\n          this.settingsService.use(body);\n          break;\n        case TYPES.ENTITY_ID_CHANGED:\n          this.entityService.use(body);\n          break;\n        case TYPES.SIDEBAR_COLLAPSED:\n          this.sidebarService.isCollapsed.next(body === true);\n          break;\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n\n  init() {\n    this.translate.addLangs(this.config.langs.map(( { id } ) => id));\n    if (this.config.defaultLang) {\n      this.translate.setDefaultLang(this.config.defaultLang);\n    }\n\n    this.router.events.pipe(\n      filter( event => event instanceof NavigationEnd ),\n      map( event => event as NavigationEnd ),\n      map( event => {\n        const foundException = this.config?.lastLocationExceptions?.find( exception => event.url.startsWith( exception.url ) );\n        return foundException ? foundException.replaceTo : event.url;\n      } ),\n      takeUntil( this.destroyed$ )\n    ).subscribe( url => {\n      const body: NavigationHistoryBody = { hub: this.config.name, url: url };\n      this.postMessage( TYPES.LOCATION_CHANGED, body );\n    } );\n\n    if (this.configService.bridge) {\n      this.create(this.configService.bridge);\n    }\n    this.postMessage(TYPES.HUB_LOADED);\n  }\n\n  initInactivityWatcher(): void {\n    merge(\n      fromEvent(window, 'mousemove'),\n      fromEvent(window, 'keypress'),\n    ).pipe(\n      throttleTime(10000),\n      takeUntil(this.destroyed$)\n    ).subscribe(() => {\n      this.postMessage(TYPES.USER_ACTIVITY, new Date());\n    });\n    this.postMessage(TYPES.USER_ACTIVITY, new Date());\n  }\n\n  sendLogin( accessToken: string, grantedPermissions: string, twoFactor: boolean ) {\n    this.postMessage(TYPES.LOGIN, {\n      accessToken,\n      grantedPermissions,\n      twoFactor\n    });\n  }\n\n  sendLogout() {\n    this.postMessage(TYPES.LOGOUT);\n  }\n\n  sendLocale( lang: string ) {\n    this.postMessage(TYPES.LOCALE_CHANGED, lang);\n  }\n\n  sendAppSettings( settings: AppSettings ) {\n    this.postMessage(TYPES.APP_SETTINGS_CHANGED, settings);\n  }\n\n  sendEntityId( entityId: string ) {\n    this.postMessage(TYPES.ENTITY_ID_CHANGED, entityId);\n  }\n\n  sendTokenExpired( body: TokenExpiredMessageBody ) {\n    this.postMessage(TYPES.TOKEN_EXPIRED, body);\n  }\n\n  sendUnknownUserLocation( url: string ) {\n    this.postMessage(TYPES.UNKNOWN_LOCATION, url);\n  }\n\n  private postMessage( type: string, body?: any ) {\n    const message: HubMessage = {\n      target: 'bridge',\n      initiator: this.config.name,\n      type,\n      body\n    };\n    if (this.win && this.ready) {\n      this.win.postMessage(message, '*');\n    } else {\n      console.log('[hub] queue message', type);\n      this.ready$.pipe(take(1)).subscribe(win => {\n        console.log('[hub] post queued message', type);\n        win.postMessage(message, '*');\n      });\n    }\n  }\n\n  private create( url: string ): void {\n    const el = document.createElement('iframe');\n    el.src = url;\n    el.style.display = 'none';\n    const frame = document.body.appendChild(el);\n    console.log('[hub] frame created');\n    frame.onload = () => {\n      if (frame.contentWindow) {\n        console.log('[hub] frame loaded');\n        this.win = frame.contentWindow;\n        if (this.ready) {\n          this.ready$.next(this.win);\n        }\n      }\n    };\n  }\n}\n"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,UAAU,QAAmB,eAAe;AAC7D,SAASC,aAAa,EAAEC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAcC,OAAO,QAAQ,MAAM;AAC7E,SAASC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAEC,YAAY,QAAQ,gBAAgB;AACvF,SAASC,kBAAkB,QAAQ,yCAAyC;AAE5E,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SACuCC,YAAY,EACxBC,KAAK,QACzB,qBAAqB;AAC5B,SAASC,uBAAuB,QAAQ,qBAAqB;AAGtD,IAAMC,gBAAgB,IAAAC,iBAAA,GAAtB,MAAMD,gBAAgB;EAS3BE,YACmBC,MAAc,EACdC,SAA2B,EAC3BC,WAA6B,EAC7BC,eAAgC,EAChCC,aAAiC,EACjCC,aAAiC,EACjCC,cAAkC,EACDC,MAAgC;IAPjE,KAAAP,MAAM,GAANA,MAAM;IACN,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACmB,KAAAC,MAAM,GAANA,MAAM;IAfjD,KAAAC,WAAW,GAAG,IAAI9B,eAAe,CAA+B,IAAI,CAAC;IAE7D,KAAA+B,UAAU,GAAG,IAAI5B,OAAO,EAAQ;IAChC,KAAA6B,MAAM,GAAG,IAAI7B,OAAO,EAAU;IACvC,KAAA8B,KAAK,GAAG,KAAK;IAanB,MAAMC,MAAM,GAAGjC,SAAS,CAAekC,MAAM,EAAE,SAAS,CAAC,CAACC,IAAI,CAC5DhC,MAAM,CAACiC,KAAK,IAAIrB,YAAY,CAACqB,KAAK,CAAC,CAAC,EACpChC,GAAG,CAA2B,CAAE;MAAEiC;IAAI,CAAE,KAAMA,IAAI,CAAC,EACnDlC,MAAM,CAAC,CAAE;MAAEmC;IAAM,CAAE,KAAMA,MAAM,KAAKV,MAAM,CAACW,IAAI,IAAID,MAAM,KAAK,GAAG,CAAC,EAClE9B,GAAG,CAAC6B,IAAI,IAAIG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEJ,IAAI,CAAC,CAAC,EAClDhC,KAAK,EAAE,CACR;IACD,IAAI,CAACqC,QAAQ,GAAGT,MAAM,CAACE,IAAI,CACzBhC,MAAM,CAAC,CAAE;MAAEwC;IAAI,CAAE,KAAM,CAAC,CAAC3B,KAAK,CAAC4B,aAAa,EAAE5B,KAAK,CAAC6B,KAAK,EAAE7B,KAAK,CAAC8B,MAAM,CAAC,CAACC,QAAQ,CAACJ,IAAI,CAAC,CAAC,CACzF;IAEDV,MAAM,CAACE,IAAI,CACT5B,SAAS,CAAC,IAAI,CAACuB,UAAU,CAAC,CAC3B,CAACkB,SAAS,CAAC,CAAE;MAAEL,IAAI;MAAEM;IAAI,CAAE,KAAK;MAC/B,QAAQN,IAAI;QACV,KAAK3B,KAAK,CAAC4B,aAAa;UACtB,MAAM;YAAEM,WAAW;YAAEC,IAAI;YAAEC,QAAQ;YAAEC,UAAU;YAAEC,QAAQ;YAAEC,kBAAkB;YAAEC;UAAS,CAAE,GAAGP,IAA+B;UAC5H,IAAI,CAACjB,KAAK,GAAG,IAAI;UACjB,IAAI,IAAI,CAACyB,GAAG,EAAE;YACZ,IAAI,CAAC1B,MAAM,CAAC2B,IAAI,CAAC,IAAI,CAACD,GAAG,CAAC;UAC5B;UACA,IAAI,CAAClC,WAAW,CAACoC,QAAQ,CAACT,WAAW,EAAEK,kBAAkB,EAAEC,SAAS,CAAC;UACrE,IAAIL,IAAI,EAAE;YACR,IAAI,CAAC7B,SAAS,CAACsC,GAAG,CAACT,IAAI,CAAC;UAC1B;UACA,IAAIC,QAAQ,EAAE;YACZ,IAAI,CAAC5B,eAAe,CAACoC,GAAG,CAACR,QAAQ,CAAC;UACpC;UACA,IAAIC,UAAU,EAAE;YACd,IAAI,CAACxB,WAAW,CAAC6B,IAAI,CAACL,UAAU,CAAC;UACnC;UACA,IAAIC,QAAQ,EAAE;YACZ,IAAI,CAAC7B,aAAa,CAACoC,SAAS,CAACP,QAAQ,CAAC;UACxC;UACA;QACF,KAAKtC,KAAK,CAAC8C,KAAK;QAChB,KAAK9C,KAAK,CAAC6B,KAAK;UACd,IAAI,CAACtB,WAAW,CAACoC,QAAQ,CAACV,IAAI,CAACC,WAAW,EAAED,IAAI,CAACM,kBAAkB,EAAEN,IAAI,CAACO,SAAS,CAAC;UACpF;QACF,KAAKxC,KAAK,CAAC8B,MAAM;UACf,IAAI,CAACvB,WAAW,CAACwC,MAAM,EAAE;UACzB,IAAI,IAAI,CAACrC,aAAa,CAACsC,QAAQ,EAAE;YAC/BC,QAAQ,CAACC,IAAI,GAAG,IAAI,CAACxC,aAAa,CAACsC,QAAQ;UAC7C;UACA;QACF,KAAKhD,KAAK,CAACmD,cAAc;UACvB,IAAI,CAAC7C,SAAS,CAACsC,GAAG,CAACX,IAAI,CAAC;UACxB;QACF,KAAKjC,KAAK,CAACoD,oBAAoB;UAC7B,IAAI,CAAC5C,eAAe,CAACoC,GAAG,CAACX,IAAI,CAAC;UAC9B;QACF,KAAKjC,KAAK,CAACqD,iBAAiB;UAC1B,IAAI,CAAC5C,aAAa,CAACmC,GAAG,CAACX,IAAI,CAAC;UAC5B;QACF,KAAKjC,KAAK,CAACsD,iBAAiB;UAC1B,IAAI,CAAC3C,cAAc,CAAC4C,WAAW,CAACb,IAAI,CAACT,IAAI,KAAK,IAAI,CAAC;UACnD;MACJ;IACF,CAAC,CAAC;EACJ;EAEAuB,WAAWA,CAAA;IACT,IAAI,CAAC1C,UAAU,CAAC4B,IAAI,CAACe,SAAS,CAAC;IAC/B,IAAI,CAAC3C,UAAU,CAAC4C,QAAQ,EAAE;EAC5B;EAEAC,IAAIA,CAAA;IACF,IAAI,CAACrD,SAAS,CAACsD,QAAQ,CAAC,IAAI,CAAChD,MAAM,CAACiD,KAAK,CAACzE,GAAG,CAAC,CAAE;MAAE0E;IAAE,CAAE,KAAMA,EAAE,CAAC,CAAC;IAChE,IAAI,IAAI,CAAClD,MAAM,CAACmD,WAAW,EAAE;MAC3B,IAAI,CAACzD,SAAS,CAAC0D,cAAc,CAAC,IAAI,CAACpD,MAAM,CAACmD,WAAW,CAAC;IACxD;IAEA,IAAI,CAAC1D,MAAM,CAAC4D,MAAM,CAAC9C,IAAI,CACrBhC,MAAM,CAAEiC,KAAK,IAAIA,KAAK,YAAYxC,aAAa,CAAE,EACjDQ,GAAG,CAAEgC,KAAK,IAAIA,KAAsB,CAAE,EACtChC,GAAG,CAAEgC,KAAK,IAAG;MAAA,IAAA8C,YAAA;MACX,MAAMC,cAAc,IAAAD,YAAA,GAAG,IAAI,CAACtD,MAAM,cAAAsD,YAAA,gBAAAA,YAAA,GAAXA,YAAA,CAAaE,sBAAsB,cAAAF,YAAA,uBAAnCA,YAAA,CAAqCG,IAAI,CAAEC,SAAS,IAAIlD,KAAK,CAACmD,GAAG,CAACC,UAAU,CAAEF,SAAS,CAACC,GAAG,CAAE,CAAE;MACtH,OAAOJ,cAAc,GAAGA,cAAc,CAACM,SAAS,GAAGrD,KAAK,CAACmD,GAAG;IAC9D,CAAC,CAAE,EACHhF,SAAS,CAAE,IAAI,CAACuB,UAAU,CAAE,CAC7B,CAACkB,SAAS,CAAEuC,GAAG,IAAG;MACjB,MAAMtC,IAAI,GAA0B;QAAEyC,GAAG,EAAE,IAAI,CAAC9D,MAAM,CAACW,IAAI;QAAEgD,GAAG,EAAEA;MAAG,CAAE;MACvE,IAAI,CAACI,WAAW,CAAE3E,KAAK,CAAC4E,gBAAgB,EAAE3C,IAAI,CAAE;IAClD,CAAC,CAAE;IAEH,IAAI,IAAI,CAACvB,aAAa,CAACmE,MAAM,EAAE;MAC7B,IAAI,CAACC,MAAM,CAAC,IAAI,CAACpE,aAAa,CAACmE,MAAM,CAAC;IACxC;IACA,IAAI,CAACF,WAAW,CAAC3E,KAAK,CAAC+E,UAAU,CAAC;EACpC;EAEAC,qBAAqBA,CAAA;IACnB/F,KAAK,CACHD,SAAS,CAACkC,MAAM,EAAE,WAAW,CAAC,EAC9BlC,SAAS,CAACkC,MAAM,EAAE,UAAU,CAAC,CAC9B,CAACC,IAAI,CACJ1B,YAAY,CAAC,KAAK,CAAC,EACnBF,SAAS,CAAC,IAAI,CAACuB,UAAU,CAAC,CAC3B,CAACkB,SAAS,CAAC,MAAK;MACf,IAAI,CAAC2C,WAAW,CAAC3E,KAAK,CAACiF,aAAa,EAAE,IAAIC,IAAI,EAAE,CAAC;IACnD,CAAC,CAAC;IACF,IAAI,CAACP,WAAW,CAAC3E,KAAK,CAACiF,aAAa,EAAE,IAAIC,IAAI,EAAE,CAAC;EACnD;EAEAC,SAASA,CAAEjD,WAAmB,EAAEK,kBAA0B,EAAEC,SAAkB;IAC5E,IAAI,CAACmC,WAAW,CAAC3E,KAAK,CAAC6B,KAAK,EAAE;MAC5BK,WAAW;MACXK,kBAAkB;MAClBC;KACD,CAAC;EACJ;EAEA4C,UAAUA,CAAA;IACR,IAAI,CAACT,WAAW,CAAC3E,KAAK,CAAC8B,MAAM,CAAC;EAChC;EAEAuD,UAAUA,CAAElD,IAAY;IACtB,IAAI,CAACwC,WAAW,CAAC3E,KAAK,CAACmD,cAAc,EAAEhB,IAAI,CAAC;EAC9C;EAEAmD,eAAeA,CAAElD,QAAqB;IACpC,IAAI,CAACuC,WAAW,CAAC3E,KAAK,CAACoD,oBAAoB,EAAEhB,QAAQ,CAAC;EACxD;EAEAmD,YAAYA,CAAEjD,QAAgB;IAC5B,IAAI,CAACqC,WAAW,CAAC3E,KAAK,CAACqD,iBAAiB,EAAEf,QAAQ,CAAC;EACrD;EAEAkD,gBAAgBA,CAAEvD,IAA6B;IAC7C,IAAI,CAAC0C,WAAW,CAAC3E,KAAK,CAACyF,aAAa,EAAExD,IAAI,CAAC;EAC7C;EAEAyD,uBAAuBA,CAAEnB,GAAW;IAClC,IAAI,CAACI,WAAW,CAAC3E,KAAK,CAAC2F,gBAAgB,EAAEpB,GAAG,CAAC;EAC/C;EAEQI,WAAWA,CAAEhD,IAAY,EAAEM,IAAU;IAC3C,MAAM2D,OAAO,GAAe;MAC1BtE,MAAM,EAAE,QAAQ;MAChBuE,SAAS,EAAE,IAAI,CAACjF,MAAM,CAACW,IAAI;MAC3BI,IAAI;MACJM;KACD;IACD,IAAI,IAAI,CAACQ,GAAG,IAAI,IAAI,CAACzB,KAAK,EAAE;MAC1B,IAAI,CAACyB,GAAG,CAACkC,WAAW,CAACiB,OAAO,EAAE,GAAG,CAAC;IACpC,CAAC,MAAM;MACLpE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEE,IAAI,CAAC;MACxC,IAAI,CAACZ,MAAM,CAACI,IAAI,CAAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0C,SAAS,CAACS,GAAG,IAAG;QACxCjB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEE,IAAI,CAAC;QAC9Cc,GAAG,CAACkC,WAAW,CAACiB,OAAO,EAAE,GAAG,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF;EAEQd,MAAMA,CAAEP,GAAW;IACzB,MAAMuB,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC3CF,EAAE,CAACG,GAAG,GAAG1B,GAAG;IACZuB,EAAE,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;IACzB,MAAMC,KAAK,GAAGL,QAAQ,CAAC9D,IAAI,CAACoE,WAAW,CAACP,EAAE,CAAC;IAC3CtE,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC2E,KAAK,CAACE,MAAM,GAAG,MAAK;MAClB,IAAIF,KAAK,CAACG,aAAa,EAAE;QACvB/E,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;QACjC,IAAI,CAACgB,GAAG,GAAG2D,KAAK,CAACG,aAAa;QAC9B,IAAI,IAAI,CAACvF,KAAK,EAAE;UACd,IAAI,CAACD,MAAM,CAAC2B,IAAI,CAAC,IAAI,CAACD,GAAG,CAAC;QAC5B;MACF;IACF,CAAC;EACH;;;;;;;;;;;;;;;;;;UA3KG/D,MAAM;IAAA8H,IAAA,GAACvG,uBAAuB;EAAA;AAAA,E;AAjBtBC,gBAAgB,GAAAuG,UAAA,EAD5B9H,UAAU,EAAE,C,EACAuB,gBAAgB,CA6L5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}