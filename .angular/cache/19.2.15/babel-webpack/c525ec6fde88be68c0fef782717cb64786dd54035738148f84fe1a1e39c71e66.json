{"ast": null, "code": "var _SwuiAutoselectComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-autoselect.component.html?ngResource\";\nimport { ChangeDetectionStrategy, Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { BehaviorSubject } from 'rxjs';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { takeUntil } from 'rxjs/operators';\nconst CONTROL_NAME = 'lib-swui-autoselect';\nlet nextUniqueId = 0;\nlet SwuiAutoselectComponent = (_SwuiAutoselectComponent = class SwuiAutoselectComponent extends SwuiMatFormFieldControl {\n  get value() {\n    return this._value;\n  }\n  set value(val) {\n    this._selectedId$.next(val || '');\n    this.stateChanges.next(undefined);\n  }\n  get options() {\n    return this._options;\n  }\n  set options(data) {\n    this._options = data;\n    this.filteredOptions = data;\n  }\n  get empty() {\n    return this._isEmpty;\n  }\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.isNotInOptionsValue = false;\n    this.controlType = CONTROL_NAME;\n    this.inputFormControl = new UntypedFormControl();\n    this.filteredOptions = [];\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._options = [];\n    this._selectedId$ = new BehaviorSubject('');\n    this._isEmpty = true;\n  }\n  ngOnInit() {\n    this.inputFormControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(text => {\n      this.filteredOptions = this._filter(text);\n      let item = this.options.find(option => option.text === text);\n      if (this.isNotInOptionsValue && text && !item) {\n        item = {\n          id: text,\n          text: text\n        };\n      }\n      this._value = item ? item.id : undefined;\n      this._isEmpty = !this._value;\n      this.onChange(this._value);\n    });\n    this._selectedId$.pipe(takeUntil(this.destroyed$)).subscribe(id => {\n      this.setInputValue(id);\n    });\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.input && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.input.focus();\n    }\n  }\n  writeValue(id) {\n    if (!id) {\n      return;\n    }\n    this._selectedId$.next(id);\n  }\n  onDisabledState(disabled) {\n    disabled ? this.inputFormControl.disable() : this.inputFormControl.enable();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n  _filter(value) {\n    const filterValue = value ? value.toLowerCase() : '';\n    return this.options.filter(option => option.text.toLowerCase().includes(filterValue));\n  }\n  setInputValue(id) {\n    let item = this.options.find(option => option.id === id && !option.disabled);\n    if (this.isNotInOptionsValue && id && !item) {\n      item = {\n        id: id,\n        text: id\n      };\n    }\n    this.inputFormControl.setValue(item ? item.text : undefined);\n  }\n}, _SwuiAutoselectComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}], _SwuiAutoselectComponent.propDecorators = {\n  value: [{\n    type: Input\n  }],\n  options: [{\n    type: Input\n  }],\n  isNotInOptionsValue: [{\n    type: Input\n  }],\n  input: [{\n    type: ViewChild,\n    args: [MatInput]\n  }],\n  matAutocomplete: [{\n    type: ViewChild,\n    args: ['auto']\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiAutoselectComponent);\nSwuiAutoselectComponent = __decorate([Component({\n  selector: 'lib-swui-autoselect',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiAutoselectComponent\n  }],\n  standalone: false\n})], SwuiAutoselectComponent);\nexport { SwuiAutoselectComponent };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormControl", "FormGroupDirective", "NgControl", "BehaviorSubject", "FocusMonitor", "MatFormFieldControl", "MatInput", "SwuiMatFormFieldControl", "ErrorStateMatcher", "takeUntil", "CONTROL_NAME", "nextUniqueId", "SwuiAutoselectComponent", "_SwuiAutoselectComponent", "value", "_value", "val", "_selectedId$", "next", "stateChanges", "undefined", "options", "_options", "data", "filteredOptions", "empty", "_isEmpty", "shouldLabelFloat", "focused", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "isNotInOptionsValue", "controlType", "inputFormControl", "id", "ngOnInit", "valueChanges", "pipe", "destroyed$", "subscribe", "text", "_filter", "item", "find", "option", "onChange", "setInputValue", "onContainerClick", "event", "stopPropagation", "input", "target", "tagName", "toLowerCase", "disabled", "focus", "writeValue", "onDisabledState", "disable", "enable", "isErrorState", "errorState", "filterValue", "filter", "includes", "setValue", "type", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "changeDetection", "OnPush", "providers", "provide", "useExisting", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-autoselect/swui-autoselect.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { BehaviorSubject } from 'rxjs';\nimport { FocusMonitor } from '@angular/cdk/a11y';\n\nimport { OptionModel } from './option.model';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatAutocomplete } from '@angular/material/autocomplete';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { takeUntil } from 'rxjs/operators';\n\n\nconst CONTROL_NAME = 'lib-swui-autoselect';\nlet nextUniqueId = 0;\n\n@Component({\n    selector: 'lib-swui-autoselect',\n    templateUrl: './swui-autoselect.component.html',\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    providers: [{ provide: MatFormFieldControl, useExisting: SwuiAutoselectComponent }],\n    standalone: false\n})\nexport class SwuiAutoselectComponent extends SwuiMatFormFieldControl<string | undefined> implements OnInit {\n  @Input()\n  get value(): string | undefined {\n    return this._value;\n  }\n\n  set value( val: string | undefined ) {\n    this._selectedId$.next(val || '');\n    this.stateChanges.next(undefined);\n  }\n\n  @Input()\n  get options(): OptionModel[] {\n    return this._options;\n  }\n\n  set options( data: OptionModel[] ) {\n    this._options = data;\n    this.filteredOptions = data;\n  }\n\n  @Input() isNotInOptionsValue = false;\n\n  get empty() {\n    return this._isEmpty;\n  }\n\n  readonly controlType = CONTROL_NAME;\n\n  readonly inputFormControl: UntypedFormControl = new UntypedFormControl();\n  filteredOptions: OptionModel[] = [];\n\n  @ViewChild(MatInput) input?: MatInput;\n  @ViewChild('auto') matAutocomplete: MatAutocomplete | undefined;\n\n  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;\n\n  @HostBinding('class.floating')\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n\n  private _value: string | undefined;\n  private _options: OptionModel[] = [];\n  private readonly _selectedId$: BehaviorSubject<string> = new BehaviorSubject('');\n  private _isEmpty = true;\n\n  constructor( fm: FocusMonitor,\n               elRef: ElementRef<HTMLElement>,\n               @Optional() @Self() ngControl: NgControl,\n               @Optional() parentFormGroup: FormGroupDirective,\n               errorStateMatcher: ErrorStateMatcher ) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n  }\n\n  ngOnInit() {\n    this.inputFormControl.valueChanges.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(( text: string ) => {\n      this.filteredOptions = this._filter(text);\n      let item = this.options.find(( option: OptionModel ) => option.text === text);\n      if (this.isNotInOptionsValue && text && !item) {\n        item = {\n          id: text,\n          text: text,\n        };\n      }\n      this._value = item ? item.id : undefined;\n      this._isEmpty = !this._value;\n      this.onChange(this._value);\n    });\n\n    this._selectedId$.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(( id: string | undefined ) => {\n      this.setInputValue(id);\n    });\n  }\n\n  onContainerClick( event: Event ) {\n    event.stopPropagation();\n    if (this.input && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.input.focus();\n    }\n  }\n\n  writeValue( id: string ) {\n    if (!id) {\n      return;\n    }\n    this._selectedId$.next(id);\n  }\n\n  protected onDisabledState( disabled: boolean ) {\n    disabled ? this.inputFormControl.disable() : this.inputFormControl.enable();\n  }\n\n  protected isErrorState(): boolean {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n\n  private _filter( value: string ): OptionModel[] {\n    const filterValue = value ? value.toLowerCase() : '';\n    return this.options.filter(( option: OptionModel ) => option.text.toLowerCase().includes(filterValue));\n  }\n\n  private setInputValue( id: string | undefined ) {\n    let item = this.options.find(( option: OptionModel ) => option.id === id && !option.disabled);\n    if (this.isNotInOptionsValue && id && !item) {\n      item = {\n        id: id,\n        text: id,\n      };\n    }\n    this.inputFormControl.setValue(item ? item.text : undefined);\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,uBAAuB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAUC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AACrI,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAClF,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,YAAY,QAAQ,mBAAmB;AAGhD,SAASC,mBAAmB,QAAQ,8BAA8B;AAElE,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,SAAS,QAAQ,gBAAgB;AAG1C,MAAMC,YAAY,GAAG,qBAAqB;AAC1C,IAAIC,YAAY,GAAG,CAAC;AASb,IAAMC,uBAAuB,IAAAC,wBAAA,GAA7B,MAAMD,uBAAwB,SAAQL,uBAA2C;MAElFO,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,MAAM;EACpB;EAEA,IAAID,KAAKA,CAAEE,GAAuB;IAChC,IAAI,CAACC,YAAY,CAACC,IAAI,CAACF,GAAG,IAAI,EAAE,CAAC;IACjC,IAAI,CAACG,YAAY,CAACD,IAAI,CAACE,SAAS,CAAC;EACnC;MAGIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACC,QAAQ;EACtB;EAEA,IAAID,OAAOA,CAAEE,IAAmB;IAC9B,IAAI,CAACD,QAAQ,GAAGC,IAAI;IACpB,IAAI,CAACC,eAAe,GAAGD,IAAI;EAC7B;EAIA,IAAIE,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,QAAQ;EACtB;MAaIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACH,KAAK;EACpC;EAOAI,YAAaC,EAAgB,EAChBC,KAA8B,EACVC,SAAoB,EAC5BC,eAAmC,EAC/CC,iBAAoC;IAC/C,KAAK,CAACJ,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IA/BxD,KAAAC,mBAAmB,GAAG,KAAK;IAM3B,KAAAC,WAAW,GAAG1B,YAAY;IAE1B,KAAA2B,gBAAgB,GAAuB,IAAIrC,kBAAkB,EAAE;IACxE,KAAAwB,eAAe,GAAkB,EAAE;IAKX,KAAAc,EAAE,GAAG,GAAG5B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAQxD,KAAAW,QAAQ,GAAkB,EAAE;IACnB,KAAAL,YAAY,GAA4B,IAAId,eAAe,CAAC,EAAE,CAAC;IACxE,KAAAuB,QAAQ,GAAG,IAAI;EAQvB;EAEAa,QAAQA,CAAA;IACN,IAAI,CAACF,gBAAgB,CAACG,YAAY,CAACC,IAAI,CACrChC,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAC3B,CAACC,SAAS,CAAGC,IAAY,IAAK;MAC7B,IAAI,CAACpB,eAAe,GAAG,IAAI,CAACqB,OAAO,CAACD,IAAI,CAAC;MACzC,IAAIE,IAAI,GAAG,IAAI,CAACzB,OAAO,CAAC0B,IAAI,CAAGC,MAAmB,IAAMA,MAAM,CAACJ,IAAI,KAAKA,IAAI,CAAC;MAC7E,IAAI,IAAI,CAACT,mBAAmB,IAAIS,IAAI,IAAI,CAACE,IAAI,EAAE;QAC7CA,IAAI,GAAG;UACLR,EAAE,EAAEM,IAAI;UACRA,IAAI,EAAEA;SACP;MACH;MACA,IAAI,CAAC7B,MAAM,GAAG+B,IAAI,GAAGA,IAAI,CAACR,EAAE,GAAGlB,SAAS;MACxC,IAAI,CAACM,QAAQ,GAAG,CAAC,IAAI,CAACX,MAAM;MAC5B,IAAI,CAACkC,QAAQ,CAAC,IAAI,CAAClC,MAAM,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI,CAACE,YAAY,CAACwB,IAAI,CACpBhC,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAC3B,CAACC,SAAS,CAAGL,EAAsB,IAAK;MACvC,IAAI,CAACY,aAAa,CAACZ,EAAE,CAAC;IACxB,CAAC,CAAC;EACJ;EAEAa,gBAAgBA,CAAEC,KAAY;IAC5BA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,IAAI,CAACC,KAAK,IAAKF,KAAK,CAACG,MAAkB,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAC/F,IAAI,CAACJ,KAAK,CAACK,KAAK,EAAE;IACpB;EACF;EAEAC,UAAUA,CAAEtB,EAAU;IACpB,IAAI,CAACA,EAAE,EAAE;MACP;IACF;IACA,IAAI,CAACrB,YAAY,CAACC,IAAI,CAACoB,EAAE,CAAC;EAC5B;EAEUuB,eAAeA,CAAEH,QAAiB;IAC1CA,QAAQ,GAAG,IAAI,CAACrB,gBAAgB,CAACyB,OAAO,EAAE,GAAG,IAAI,CAACzB,gBAAgB,CAAC0B,MAAM,EAAE;EAC7E;EAEUC,YAAYA,CAAA;IACpB,IAAI,IAAI,CAACV,KAAK,EAAE;MACd,OAAO,IAAI,CAACA,KAAK,CAACW,UAAU;IAC9B;IACA,OAAO,KAAK;EACd;EAEQpB,OAAOA,CAAE/B,KAAa;IAC5B,MAAMoD,WAAW,GAAGpD,KAAK,GAAGA,KAAK,CAAC2C,WAAW,EAAE,GAAG,EAAE;IACpD,OAAO,IAAI,CAACpC,OAAO,CAAC8C,MAAM,CAAGnB,MAAmB,IAAMA,MAAM,CAACJ,IAAI,CAACa,WAAW,EAAE,CAACW,QAAQ,CAACF,WAAW,CAAC,CAAC;EACxG;EAEQhB,aAAaA,CAAEZ,EAAsB;IAC3C,IAAIQ,IAAI,GAAG,IAAI,CAACzB,OAAO,CAAC0B,IAAI,CAAGC,MAAmB,IAAMA,MAAM,CAACV,EAAE,KAAKA,EAAE,IAAI,CAACU,MAAM,CAACU,QAAQ,CAAC;IAC7F,IAAI,IAAI,CAACvB,mBAAmB,IAAIG,EAAE,IAAI,CAACQ,IAAI,EAAE;MAC3CA,IAAI,GAAG;QACLR,EAAE,EAAEA,EAAE;QACNM,IAAI,EAAEN;OACP;IACH;IACA,IAAI,CAACD,gBAAgB,CAACgC,QAAQ,CAACvB,IAAI,GAAGA,IAAI,CAACF,IAAI,GAAGxB,SAAS,CAAC;EAC9D;;;;;;;;UArEcvB;EAAQ;IAAAyE,IAAA,EAAIxE;EAAI;AAAA,G;;;UAChBD;EAAQ;AAAA,G;;;;UAjDrBD;EAAK;;UAULA;EAAK;;UAULA;EAAK;;UAWLG,SAAS;IAAAwE,IAAA,GAACjE,QAAQ;EAAA;;UAClBP,SAAS;IAAAwE,IAAA,GAAC,MAAM;EAAA;;UAEhB5E;EAAW;;UAEXA,WAAW;IAAA4E,IAAA,GAAC,gBAAgB;EAAA;;AArClB3D,uBAAuB,GAAA4D,UAAA,EAPnC/E,SAAS,CAAC;EACPgF,QAAQ,EAAE,qBAAqB;EAC/BC,QAAA,EAAAC,oBAA+C;EAC/CC,eAAe,EAAEpF,uBAAuB,CAACqF,MAAM;EAC/CC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAE1E,mBAAmB;IAAE2E,WAAW,EAAEpE;EAAuB,CAAE,CAAC;EACnFqE,UAAU,EAAE;CACf,CAAC,C,EACWrE,uBAAuB,CAuHnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}