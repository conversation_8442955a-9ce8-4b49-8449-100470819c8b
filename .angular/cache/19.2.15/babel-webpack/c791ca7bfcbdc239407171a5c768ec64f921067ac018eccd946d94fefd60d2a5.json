{"ast": null, "code": "var MatDynamicFormModule_1;\nimport { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ColorPickerModule } from 'ngx-color-picker';\nimport { SelectOnClickModule } from '../directives/selectOnClick/select-on-click.module';\nimport { SwuiControlMessagesModule } from '../swui-control-messages/swui-control-messages.module';\nimport { SwuiDateRangeModule } from '../swui-date-range/swui-date-range.module';\nimport { SwuiNumericRangeMenuModule } from '../swui-numeric-range-menu/swui-numeric-range-menu.module';\nimport { SwuiSearchModule } from '../swui-search/swui-search.module';\nimport { SwuiSelectTableModule } from '../swui-select-table/swui-select-table.module';\nimport { SwuiSelectModule } from '../swui-select/swui-select.module';\nimport { SwitcheryModule } from '../swui-switchery/switchery.module';\nimport { InputColorComponent } from './input-color/input-color.component';\nimport { InputImageComponent } from './input-image/input-image.component';\nimport { ControlInputComponent } from './control-input/control-input.component';\nimport { InputPasswordComponent } from './input-password/input-password.component';\nimport { InputSelectTableComponent } from './input-select-table/input-select-table.component';\nimport { InputSelectComponent } from './input-select/input-select.component';\nimport { InputSwitchComponent } from './input-switch/input-switch.component';\nimport { InputTextComponent } from './input-text/input-text.component';\nimport { InputVideoUrlComponent } from './input-video-url/input-video-url.component';\nimport { MatDynamicFormComponent } from './mat-dynamic-form.component';\nimport { InputDateRangeComponent } from './input-date-range/input-date-range.component';\nimport { InputSearchComponent } from './input-search/input-search.component';\nimport { InputNumericRangeComponent } from './input-numeric-range/input-numeric-range.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { InputOutletComponent } from './input-outlet/input-outlet.component';\nimport { SWUI_DYNAMIC_FORM_CONFIG } from './dynamic-form.model';\nimport { ControlItemsComponent } from './control-items/control-items.component';\nimport { ButtonActionComponent } from './button-action/button-action.component';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { InputNumberComponent } from './input-number/input-number.component';\nimport { InputDateComponent } from './input-date/input-date.component';\nimport { SwuiDatePickerModule } from '../swui-date-picker/swui-date-picker.module';\nlet MatDynamicFormModule = MatDynamicFormModule_1 = class MatDynamicFormModule {\n  static forRoot(config) {\n    return {\n      ngModule: MatDynamicFormModule_1,\n      providers: [{\n        provide: SWUI_DYNAMIC_FORM_CONFIG,\n        useValue: config\n      }]\n    };\n  }\n};\nMatDynamicFormModule = MatDynamicFormModule_1 = __decorate([NgModule({\n  imports: [CommonModule, ReactiveFormsModule, SwuiControlMessagesModule, TranslateModule, SwitcheryModule, ColorPickerModule, SelectOnClickModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatSelectModule, MatSlideToggleModule, MatIconModule, MatTooltipModule, SwuiDateRangeModule, SwuiSelectModule, SwuiSearchModule, SwuiNumericRangeMenuModule, SwuiDatePickerModule, SwuiSelectTableModule],\n  exports: [MatDynamicFormComponent],\n  declarations: [MatDynamicFormComponent, ControlItemsComponent, ControlInputComponent, InputOutletComponent, InputTextComponent, InputSelectComponent, InputImageComponent, InputColorComponent, InputSwitchComponent, InputPasswordComponent, InputDateRangeComponent, InputSearchComponent, InputNumericRangeComponent, ButtonActionComponent, InputNumberComponent, InputDateComponent, InputVideoUrlComponent, InputSelectTableComponent],\n  providers: []\n})], MatDynamicFormModule);\nexport { MatDynamicFormModule };", "map": {"version": 3, "names": ["MatDynamicFormModule_1", "__decorate", "CommonModule", "NgModule", "ReactiveFormsModule", "TranslateModule", "ColorPickerModule", "SelectOnClickModule", "SwuiControlMessagesModule", "SwuiDateRangeModule", "SwuiNumericRangeMenuModule", "SwuiSearchModule", "SwuiSelectTableModule", "SwuiSelectModule", "SwitcheryModule", "InputColorComponent", "InputImageComponent", "ControlInputComponent", "InputPasswordComponent", "InputSelectTableComponent", "InputSelectComponent", "InputSwitchComponent", "InputTextComponent", "InputVideoUrlComponent", "MatDynamicFormComponent", "InputDateRangeComponent", "InputSearchComponent", "InputNumericRangeComponent", "MatFormFieldModule", "MatSelectModule", "MatSlideToggleModule", "MatIconModule", "MatInputModule", "MatButtonModule", "InputOutletComponent", "SWUI_DYNAMIC_FORM_CONFIG", "ControlItemsComponent", "ButtonActionComponent", "MatTooltipModule", "InputNumberComponent", "InputDateComponent", "SwuiDatePickerModule", "MatDynamicFormModule", "forRoot", "config", "ngModule", "providers", "provide", "useValue", "imports", "exports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/mat-dynamic-form.module.ts"], "sourcesContent": ["var MatDynamicFormModule_1;\nimport { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ColorPickerModule } from 'ngx-color-picker';\nimport { SelectOnClickModule } from '../directives/selectOnClick/select-on-click.module';\nimport { SwuiControlMessagesModule } from '../swui-control-messages/swui-control-messages.module';\nimport { SwuiDateRangeModule } from '../swui-date-range/swui-date-range.module';\nimport { SwuiNumericRangeMenuModule } from '../swui-numeric-range-menu/swui-numeric-range-menu.module';\nimport { SwuiSearchModule } from '../swui-search/swui-search.module';\nimport { SwuiSelectTableModule } from '../swui-select-table/swui-select-table.module';\nimport { SwuiSelectModule } from '../swui-select/swui-select.module';\nimport { SwitcheryModule } from '../swui-switchery/switchery.module';\nimport { InputColorComponent } from './input-color/input-color.component';\nimport { InputImageComponent } from './input-image/input-image.component';\nimport { ControlInputComponent } from './control-input/control-input.component';\nimport { InputPasswordComponent } from './input-password/input-password.component';\nimport { InputSelectTableComponent } from './input-select-table/input-select-table.component';\nimport { InputSelectComponent } from './input-select/input-select.component';\nimport { InputSwitchComponent } from './input-switch/input-switch.component';\nimport { InputTextComponent } from './input-text/input-text.component';\nimport { InputVideoUrlComponent } from './input-video-url/input-video-url.component';\nimport { MatDynamicFormComponent } from './mat-dynamic-form.component';\nimport { InputDateRangeComponent } from './input-date-range/input-date-range.component';\nimport { InputSearchComponent } from './input-search/input-search.component';\nimport { InputNumericRangeComponent } from './input-numeric-range/input-numeric-range.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { InputOutletComponent } from './input-outlet/input-outlet.component';\nimport { SWUI_DYNAMIC_FORM_CONFIG } from './dynamic-form.model';\nimport { ControlItemsComponent } from './control-items/control-items.component';\nimport { ButtonActionComponent } from './button-action/button-action.component';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { InputNumberComponent } from './input-number/input-number.component';\nimport { InputDateComponent } from './input-date/input-date.component';\nimport { SwuiDatePickerModule } from '../swui-date-picker/swui-date-picker.module';\nlet MatDynamicFormModule = MatDynamicFormModule_1 = class MatDynamicFormModule {\n    static forRoot(config) {\n        return {\n            ngModule: MatDynamicFormModule_1,\n            providers: [\n                {\n                    provide: SWUI_DYNAMIC_FORM_CONFIG,\n                    useValue: config,\n                }\n            ]\n        };\n    }\n};\nMatDynamicFormModule = MatDynamicFormModule_1 = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            ReactiveFormsModule,\n            SwuiControlMessagesModule,\n            TranslateModule,\n            SwitcheryModule,\n            ColorPickerModule,\n            SelectOnClickModule,\n            MatFormFieldModule,\n            MatInputModule,\n            MatButtonModule,\n            MatSelectModule,\n            MatSlideToggleModule,\n            MatIconModule,\n            MatTooltipModule,\n            SwuiDateRangeModule,\n            SwuiSelectModule,\n            SwuiSearchModule,\n            SwuiNumericRangeMenuModule,\n            SwuiDatePickerModule,\n            SwuiSelectTableModule\n        ],\n        exports: [MatDynamicFormComponent],\n        declarations: [\n            MatDynamicFormComponent,\n            ControlItemsComponent,\n            ControlInputComponent,\n            InputOutletComponent,\n            InputTextComponent,\n            InputSelectComponent,\n            InputImageComponent,\n            InputColorComponent,\n            InputSwitchComponent,\n            InputPasswordComponent,\n            InputDateRangeComponent,\n            InputSearchComponent,\n            InputNumericRangeComponent,\n            ButtonActionComponent,\n            InputNumberComponent,\n            InputDateComponent,\n            InputVideoUrlComponent,\n            InputSelectTableComponent\n        ],\n        providers: [],\n    })\n], MatDynamicFormModule);\nexport { MatDynamicFormModule };\n"], "mappings": "AAAA,IAAIA,sBAAsB;AAC1B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,mBAAmB,QAAQ,oDAAoD;AACxF,SAASC,yBAAyB,QAAQ,uDAAuD;AACjG,SAASC,mBAAmB,QAAQ,2CAA2C;AAC/E,SAASC,0BAA0B,QAAQ,2DAA2D;AACtG,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,sBAAsB,QAAQ,6CAA6C;AACpF,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,oBAAoB,QAAQ,6CAA6C;AAClF,IAAIC,oBAAoB,GAAG1C,sBAAsB,GAAG,MAAM0C,oBAAoB,CAAC;EAC3E,OAAOC,OAAOA,CAACC,MAAM,EAAE;IACnB,OAAO;MACHC,QAAQ,EAAE7C,sBAAsB;MAChC8C,SAAS,EAAE,CACP;QACIC,OAAO,EAAEZ,wBAAwB;QACjCa,QAAQ,EAAEJ;MACd,CAAC;IAET,CAAC;EACL;AACJ,CAAC;AACDF,oBAAoB,GAAG1C,sBAAsB,GAAGC,UAAU,CAAC,CACvDE,QAAQ,CAAC;EACL8C,OAAO,EAAE,CACL/C,YAAY,EACZE,mBAAmB,EACnBI,yBAAyB,EACzBH,eAAe,EACfS,eAAe,EACfR,iBAAiB,EACjBC,mBAAmB,EACnBqB,kBAAkB,EAClBI,cAAc,EACdC,eAAe,EACfJ,eAAe,EACfC,oBAAoB,EACpBC,aAAa,EACbO,gBAAgB,EAChB7B,mBAAmB,EACnBI,gBAAgB,EAChBF,gBAAgB,EAChBD,0BAA0B,EAC1B+B,oBAAoB,EACpB7B,qBAAqB,CACxB;EACDsC,OAAO,EAAE,CAAC1B,uBAAuB,CAAC;EAClC2B,YAAY,EAAE,CACV3B,uBAAuB,EACvBY,qBAAqB,EACrBnB,qBAAqB,EACrBiB,oBAAoB,EACpBZ,kBAAkB,EAClBF,oBAAoB,EACpBJ,mBAAmB,EACnBD,mBAAmB,EACnBM,oBAAoB,EACpBH,sBAAsB,EACtBO,uBAAuB,EACvBC,oBAAoB,EACpBC,0BAA0B,EAC1BU,qBAAqB,EACrBE,oBAAoB,EACpBC,kBAAkB,EAClBjB,sBAAsB,EACtBJ,yBAAyB,CAC5B;EACD2B,SAAS,EAAE;AACf,CAAC,CAAC,CACL,EAAEJ,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}