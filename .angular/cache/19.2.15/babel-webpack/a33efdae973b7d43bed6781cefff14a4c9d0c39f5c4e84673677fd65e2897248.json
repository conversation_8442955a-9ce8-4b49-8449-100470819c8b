{"ast": null, "code": "import { InjectionToken } from '@angular/core';\n\n/**\n * This token is used to inject the object whose value should be set into `MatInput`. If none is\n * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide\n * themselves for this token, in order to make `MatInput` delegate the getting and setting of the\n * value to them.\n */\nconst MAT_INPUT_VALUE_ACCESSOR = new InjectionToken('MAT_INPUT_VALUE_ACCESSOR');\nexport { MAT_INPUT_VALUE_ACCESSOR as M };", "map": {"version": 3, "names": ["InjectionToken", "MAT_INPUT_VALUE_ACCESSOR", "M"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/input-value-accessor-D1GvPuqO.mjs"], "sourcesContent": ["import { InjectionToken } from '@angular/core';\n\n/**\n * This token is used to inject the object whose value should be set into `MatInput`. If none is\n * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide\n * themselves for this token, in order to make `MatInput` delegate the getting and setting of the\n * value to them.\n */\nconst MAT_INPUT_VALUE_ACCESSOR = new InjectionToken('MAT_INPUT_VALUE_ACCESSOR');\n\nexport { MAT_INPUT_VALUE_ACCESSOR as M };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,eAAe;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,IAAID,cAAc,CAAC,0BAA0B,CAAC;AAE/E,SAASC,wBAAwB,IAAIC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}