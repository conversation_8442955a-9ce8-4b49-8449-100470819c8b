{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Konka<PERSON> Devanagari script [gom-deva]\n//! author : The Discoverer : https://github.com/WikiDiscoverer\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      s: ['थोडया सॅकंडांनी', 'थोडे सॅकंड'],\n      ss: [number + ' सॅकंडांनी', number + ' सॅकंड'],\n      m: ['एका मिणटान', 'एक मिनूट'],\n      mm: [number + ' मिणटांनी', number + ' मिणटां'],\n      h: ['एका वरान', 'एक वर'],\n      hh: [number + ' वरांनी', number + ' वरां'],\n      d: ['एका दिसान', 'एक दीस'],\n      dd: [number + ' दिसांनी', number + ' दीस'],\n      M: ['एका म्हयन्यान', 'एक म्हयनो'],\n      MM: [number + ' म्हयन्यानी', number + ' म्हयने'],\n      y: ['एका वर्सान', 'एक वर्स'],\n      yy: [number + ' वर्सांनी', number + ' वर्सां']\n    };\n    return isFuture ? format[key][0] : format[key][1];\n  }\n  var gomDeva = moment.defineLocale('gom-deva', {\n    months: {\n      standalone: 'जानेवारी_फेब्रुवारी_मार्च_एप्रील_मे_जून_जुलय_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर'.split('_'),\n      format: 'जानेवारीच्या_फेब्रुवारीच्या_मार्चाच्या_एप्रीलाच्या_मेयाच्या_जूनाच्या_जुलयाच्या_ऑगस्टाच्या_सप्टेंबराच्या_ऑक्टोबराच्या_नोव्हेंबराच्या_डिसेंबराच्या'.split('_'),\n      isFormat: /MMMM(\\s)+D[oD]?/\n    },\n    monthsShort: 'जाने._फेब्रु._मार्च_एप्री._मे_जून_जुल._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'आयतार_सोमार_मंगळार_बुधवार_बिरेस्तार_सुक्रार_शेनवार'.split('_'),\n    weekdaysShort: 'आयत._सोम._मंगळ._बुध._ब्रेस्त._सुक्र._शेन.'.split('_'),\n    weekdaysMin: 'आ_सो_मं_बु_ब्रे_सु_शे'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'A h:mm [वाजतां]',\n      LTS: 'A h:mm:ss [वाजतां]',\n      L: 'DD-MM-YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY A h:mm [वाजतां]',\n      LLLL: 'dddd, MMMM Do, YYYY, A h:mm [वाजतां]',\n      llll: 'ddd, D MMM YYYY, A h:mm [वाजतां]'\n    },\n    calendar: {\n      sameDay: '[आयज] LT',\n      nextDay: '[फाल्यां] LT',\n      nextWeek: '[फुडलो] dddd[,] LT',\n      lastDay: '[काल] LT',\n      lastWeek: '[फाटलो] dddd[,] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s',\n      past: '%s आदीं',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(वेर)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        // the ordinal 'वेर' only applies to day of the month\n        case 'D':\n          return number + 'वेर';\n        default:\n        case 'M':\n        case 'Q':\n        case 'DDD':\n        case 'd':\n        case 'w':\n        case 'W':\n          return number;\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week\n      doy: 3 // The week that contains Jan 4th is the first week of the year (7 + 0 - 4)\n    },\n    meridiemParse: /राती|सकाळीं|दनपारां|सांजे/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'राती') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'सकाळीं') {\n        return hour;\n      } else if (meridiem === 'दनपारां') {\n        return hour > 12 ? hour : hour + 12;\n      } else if (meridiem === 'सांजे') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'राती';\n      } else if (hour < 12) {\n        return 'सकाळीं';\n      } else if (hour < 16) {\n        return 'दनपारां';\n      } else if (hour < 20) {\n        return 'सांजे';\n      } else {\n        return 'राती';\n      }\n    }\n  });\n  return gomDeva;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "processRelativeTime", "number", "withoutSuffix", "key", "isFuture", "format", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "<PERSON><PERSON><PERSON><PERSON>", "defineLocale", "months", "standalone", "split", "isFormat", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "llll", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "dayOfMonthOrdinalParse", "ordinal", "period", "week", "dow", "doy", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/moment/locale/gom-deva.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Konka<PERSON> Devanagari script [gom-deva]\n//! author : The Discoverer : https://github.com/WikiDiscoverer\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            s: ['थोडया सॅकंडांनी', 'थोडे सॅकंड'],\n            ss: [number + ' सॅकंडांनी', number + ' सॅकंड'],\n            m: ['एका मिणटान', 'एक मिनूट'],\n            mm: [number + ' मिणटांनी', number + ' मिणटां'],\n            h: ['एका वरान', 'एक वर'],\n            hh: [number + ' वरांनी', number + ' वरां'],\n            d: ['एका दिसान', 'एक दीस'],\n            dd: [number + ' दिसांनी', number + ' दीस'],\n            M: ['एका म्हयन्यान', 'एक म्हयनो'],\n            MM: [number + ' म्हयन्यानी', number + ' म्हयने'],\n            y: ['एका वर्सान', 'एक वर्स'],\n            yy: [number + ' वर्सांनी', number + ' वर्सां'],\n        };\n        return isFuture ? format[key][0] : format[key][1];\n    }\n\n    var gomDeva = moment.defineLocale('gom-deva', {\n        months: {\n            standalone: 'जानेवारी_फेब्रुवारी_मार्च_एप्रील_मे_जून_जुलय_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर'.split(\n                '_'\n            ),\n            format: 'जानेवारीच्या_फेब्रुवारीच्या_मार्चाच्या_एप्रीलाच्या_मेयाच्या_जूनाच्या_जुलयाच्या_ऑगस्टाच्या_सप्टेंबराच्या_ऑक्टोबराच्या_नोव्हेंबराच्या_डिसेंबराच्या'.split(\n                '_'\n            ),\n            isFormat: /MMMM(\\s)+D[oD]?/,\n        },\n        monthsShort: 'जाने._फेब्रु._मार्च_एप्री._मे_जून_जुल._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.'.split(\n            '_'\n        ),\n        monthsParseExact: true,\n        weekdays: 'आयतार_सोमार_मंगळार_बुधवार_बिरेस्तार_सुक्रार_शेनवार'.split('_'),\n        weekdaysShort: 'आयत._सोम._मंगळ._बुध._ब्रेस्त._सुक्र._शेन.'.split('_'),\n        weekdaysMin: 'आ_सो_मं_बु_ब्रे_सु_शे'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'A h:mm [वाजतां]',\n            LTS: 'A h:mm:ss [वाजतां]',\n            L: 'DD-MM-YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY A h:mm [वाजतां]',\n            LLLL: 'dddd, MMMM Do, YYYY, A h:mm [वाजतां]',\n            llll: 'ddd, D MMM YYYY, A h:mm [वाजतां]',\n        },\n        calendar: {\n            sameDay: '[आयज] LT',\n            nextDay: '[फाल्यां] LT',\n            nextWeek: '[फुडलो] dddd[,] LT',\n            lastDay: '[काल] LT',\n            lastWeek: '[फाटलो] dddd[,] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s',\n            past: '%s आदीं',\n            s: processRelativeTime,\n            ss: processRelativeTime,\n            m: processRelativeTime,\n            mm: processRelativeTime,\n            h: processRelativeTime,\n            hh: processRelativeTime,\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(वेर)/,\n        ordinal: function (number, period) {\n            switch (period) {\n                // the ordinal 'वेर' only applies to day of the month\n                case 'D':\n                    return number + 'वेर';\n                default:\n                case 'M':\n                case 'Q':\n                case 'DDD':\n                case 'd':\n                case 'w':\n                case 'W':\n                    return number;\n            }\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week\n            doy: 3, // The week that contains Jan 4th is the first week of the year (7 + 0 - 4)\n        },\n        meridiemParse: /राती|सकाळीं|दनपारां|सांजे/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'राती') {\n                return hour < 4 ? hour : hour + 12;\n            } else if (meridiem === 'सकाळीं') {\n                return hour;\n            } else if (meridiem === 'दनपारां') {\n                return hour > 12 ? hour : hour + 12;\n            } else if (meridiem === 'सांजे') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'राती';\n            } else if (hour < 12) {\n                return 'सकाळीं';\n            } else if (hour < 16) {\n                return 'दनपारां';\n            } else if (hour < 20) {\n                return 'सांजे';\n            } else {\n                return 'राती';\n            }\n        },\n    });\n\n    return gomDeva;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC/D,IAAIC,MAAM,GAAG;MACTC,CAAC,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC;MACpCC,EAAE,EAAE,CAACN,MAAM,GAAG,YAAY,EAAEA,MAAM,GAAG,QAAQ,CAAC;MAC9CO,CAAC,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;MAC7BC,EAAE,EAAE,CAACR,MAAM,GAAG,WAAW,EAAEA,MAAM,GAAG,SAAS,CAAC;MAC9CS,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;MACxBC,EAAE,EAAE,CAACV,MAAM,GAAG,SAAS,EAAEA,MAAM,GAAG,OAAO,CAAC;MAC1CW,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;MAC1BC,EAAE,EAAE,CAACZ,MAAM,GAAG,UAAU,EAAEA,MAAM,GAAG,MAAM,CAAC;MAC1Ca,CAAC,EAAE,CAAC,eAAe,EAAE,WAAW,CAAC;MACjCC,EAAE,EAAE,CAACd,MAAM,GAAG,aAAa,EAAEA,MAAM,GAAG,SAAS,CAAC;MAChDe,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC;MAC5BC,EAAE,EAAE,CAAChB,MAAM,GAAG,WAAW,EAAEA,MAAM,GAAG,SAAS;IACjD,CAAC;IACD,OAAOG,QAAQ,GAAGC,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;EACrD;EAEA,IAAIe,OAAO,GAAGnB,MAAM,CAACoB,YAAY,CAAC,UAAU,EAAE;IAC1CC,MAAM,EAAE;MACJC,UAAU,EAAE,uFAAuF,CAACC,KAAK,CACrG,GACJ,CAAC;MACDjB,MAAM,EAAE,kJAAkJ,CAACiB,KAAK,CAC5J,GACJ,CAAC;MACDC,QAAQ,EAAE;IACd,CAAC;IACDC,WAAW,EAAE,2EAA2E,CAACF,KAAK,CAC1F,GACJ,CAAC;IACDG,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,oDAAoD,CAACJ,KAAK,CAAC,GAAG,CAAC;IACzEK,aAAa,EAAE,2CAA2C,CAACL,KAAK,CAAC,GAAG,CAAC;IACrEM,WAAW,EAAE,uBAAuB,CAACN,KAAK,CAAC,GAAG,CAAC;IAC/CO,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,iBAAiB;MACrBC,GAAG,EAAE,oBAAoB;MACzBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,6BAA6B;MAClCC,IAAI,EAAE,sCAAsC;MAC5CC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,UAAU;MACnBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,oBAAoB;MAC9BC,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,SAAS;MACfzC,CAAC,EAAEN,mBAAmB;MACtBO,EAAE,EAAEP,mBAAmB;MACvBQ,CAAC,EAAER,mBAAmB;MACtBS,EAAE,EAAET,mBAAmB;MACvBU,CAAC,EAAEV,mBAAmB;MACtBW,EAAE,EAAEX,mBAAmB;MACvBY,CAAC,EAAEZ,mBAAmB;MACtBa,EAAE,EAAEb,mBAAmB;MACvBc,CAAC,EAAEd,mBAAmB;MACtBe,EAAE,EAAEf,mBAAmB;MACvBgB,CAAC,EAAEhB,mBAAmB;MACtBiB,EAAE,EAAEjB;IACR,CAAC;IACDgD,sBAAsB,EAAE,cAAc;IACtCC,OAAO,EAAE,SAAAA,CAAUhD,MAAM,EAAEiD,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV;QACA,KAAK,GAAG;UACJ,OAAOjD,MAAM,GAAG,KAAK;QACzB;QACA,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,KAAK;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,GAAG;UACJ,OAAOA,MAAM;MACrB;IACJ,CAAC;IACDkD,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ,CAAC;IACDC,aAAa,EAAE,2BAA2B;IAC1CC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,QAAQ,KAAK,MAAM,EAAE;QACrB,OAAOD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACtC,CAAC,MAAM,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QAC9B,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,SAAS,EAAE;QAC/B,OAAOD,IAAI,GAAG,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACvC,CAAC,MAAM,IAAIC,QAAQ,KAAK,OAAO,EAAE;QAC7B,OAAOD,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIH,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,QAAQ;MACnB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,SAAS;MACpB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO;MAClB,CAAC,MAAM;QACH,OAAO,MAAM;MACjB;IACJ;EACJ,CAAC,CAAC;EAEF,OAAOtC,OAAO;AAElB,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}