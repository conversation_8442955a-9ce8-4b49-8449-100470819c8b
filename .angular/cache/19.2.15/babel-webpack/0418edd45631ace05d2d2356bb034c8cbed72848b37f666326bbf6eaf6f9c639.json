{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _Platform;\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, Injectable } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch (_unused) {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n  constructor() {\n    _defineProperty(this, \"_platformId\", inject(PLATFORM_ID));\n    // We want to use the Angular platform check because if the Document is shimmed\n    // without the navigator, the following checks will fail. This is preferred because\n    // sometimes the Document may be shimmed without the user's knowledge or intention\n    /** Whether the Angular application is being rendered in the browser. */\n    _defineProperty(this, \"isBrowser\", this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document);\n    /** Whether the current browser is Microsoft Edge. */\n    _defineProperty(this, \"EDGE\", this.isBrowser && /(edge)/i.test(navigator.userAgent));\n    /** Whether the current rendering engine is Microsoft Trident. */\n    _defineProperty(this, \"TRIDENT\", this.isBrowser && /(msie|trident)/i.test(navigator.userAgent));\n    // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n    /** Whether the current rendering engine is Blink. */\n    _defineProperty(this, \"BLINK\", this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT);\n    // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n    // ensure that Webkit runs standalone and is not used as another engine's base.\n    /** Whether the current rendering engine is WebKit. */\n    _defineProperty(this, \"WEBKIT\", this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT);\n    /** Whether the current platform is Apple iOS. */\n    _defineProperty(this, \"IOS\", this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window));\n    // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n    // them self as Gecko-like browsers and modify the userAgent's according to that.\n    // Since we only cover one explicit Firefox case, we can simply check for Firefox\n    // instead of having an unstable check for Gecko.\n    /** Whether the current browser is Firefox. */\n    _defineProperty(this, \"FIREFOX\", this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent));\n    /** Whether the current platform is Android. */\n    // Trident on mobile adds the android platform to the userAgent to trick detections.\n    _defineProperty(this, \"ANDROID\", this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT);\n    // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n    // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n    // Safari browser should also use Webkit as its layout engine.\n    /** Whether the current browser is Safari. */\n    _defineProperty(this, \"SAFARI\", this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT);\n  }\n}\n_Platform = Platform;\n_defineProperty(Platform, \"\\u0275fac\", function _Platform_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Platform)();\n});\n_defineProperty(Platform, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _Platform,\n  factory: _Platform.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { Platform as P };", "map": {"version": 3, "names": ["i0", "inject", "PLATFORM_ID", "Injectable", "isPlatformBrowser", "hasV8BreakIterator", "Intl", "v8BreakIterator", "_unused", "Platform", "constructor", "_defineProperty", "_platformId", "document", "<PERSON><PERSON><PERSON><PERSON>", "test", "navigator", "userAgent", "window", "chrome", "CSS", "EDGE", "TRIDENT", "BLINK", "WEBKIT", "_Platform", "_Platform_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "P"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/platform-DmdVEw_C.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, Injectable } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n    hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n}\ncatch {\n    hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n    _platformId = inject(PLATFORM_ID);\n    // We want to use the Angular platform check because if the Document is shimmed\n    // without the navigator, the following checks will fail. This is preferred because\n    // sometimes the Document may be shimmed without the user's knowledge or intention\n    /** Whether the Angular application is being rendered in the browser. */\n    isBrowser = this._platformId\n        ? isPlatformBrowser(this._platformId)\n        : typeof document === 'object' && !!document;\n    /** Whether the current browser is Microsoft Edge. */\n    EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n    /** Whether the current rendering engine is Microsoft Trident. */\n    TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n    // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n    /** Whether the current rendering engine is Blink. */\n    BLINK = this.isBrowser &&\n        !!(window.chrome || hasV8BreakIterator) &&\n        typeof CSS !== 'undefined' &&\n        !this.EDGE &&\n        !this.TRIDENT;\n    // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n    // ensure that Webkit runs standalone and is not used as another engine's base.\n    /** Whether the current rendering engine is WebKit. */\n    WEBKIT = this.isBrowser &&\n        /AppleWebKit/i.test(navigator.userAgent) &&\n        !this.BLINK &&\n        !this.EDGE &&\n        !this.TRIDENT;\n    /** Whether the current platform is Apple iOS. */\n    IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n    // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n    // them self as Gecko-like browsers and modify the userAgent's according to that.\n    // Since we only cover one explicit Firefox case, we can simply check for Firefox\n    // instead of having an unstable check for Gecko.\n    /** Whether the current browser is Firefox. */\n    FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n    /** Whether the current platform is Android. */\n    // Trident on mobile adds the android platform to the userAgent to trick detections.\n    ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n    // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n    // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n    // Safari browser should also use Webkit as its layout engine.\n    /** Whether the current browser is Safari. */\n    SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Platform, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Platform, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Platform, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nexport { Platform as P };\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,WAAW,EAAEC,UAAU,QAAQ,eAAe;AAC/D,SAASC,iBAAiB,QAAQ,iBAAiB;;AAEnD;AACA;AACA,IAAIC,kBAAkB;AACtB;AACA;AACA;AACA;AACA;AACA,IAAI;EACAA,kBAAkB,GAAG,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACC,eAAe;AAC5E,CAAC,CACD,OAAAC,OAAA,EAAM;EACFH,kBAAkB,GAAG,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA,MAAMI,QAAQ,CAAC;EA4CXC,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBA3CAV,MAAM,CAACC,WAAW,CAAC;IACjC;IACA;IACA;IACA;IAAAS,eAAA,oBACY,IAAI,CAACC,WAAW,GACtBR,iBAAiB,CAAC,IAAI,CAACQ,WAAW,CAAC,GACnC,OAAOC,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAACA,QAAQ;IAChD;IAAAF,eAAA,eACO,IAAI,CAACG,SAAS,IAAI,SAAS,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IAC5D;IAAAN,eAAA,kBACU,IAAI,CAACG,SAAS,IAAI,iBAAiB,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IACvE;IACA;IAAAN,eAAA,gBACQ,IAAI,CAACG,SAAS,IAClB,CAAC,EAAEI,MAAM,CAACC,MAAM,IAAId,kBAAkB,CAAC,IACvC,OAAOe,GAAG,KAAK,WAAW,IAC1B,CAAC,IAAI,CAACC,IAAI,IACV,CAAC,IAAI,CAACC,OAAO;IACjB;IACA;IACA;IAAAX,eAAA,iBACS,IAAI,CAACG,SAAS,IACnB,cAAc,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IACxC,CAAC,IAAI,CAACM,KAAK,IACX,CAAC,IAAI,CAACF,IAAI,IACV,CAAC,IAAI,CAACC,OAAO;IACjB;IAAAX,eAAA,cACM,IAAI,CAACG,SAAS,IAAI,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,EAAE,UAAU,IAAIC,MAAM,CAAC;IAC/F;IACA;IACA;IACA;IACA;IAAAP,eAAA,kBACU,IAAI,CAACG,SAAS,IAAI,sBAAsB,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IAC5E;IACA;IAAAN,eAAA,kBACU,IAAI,CAACG,SAAS,IAAI,UAAU,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC,IAAI,CAACK,OAAO;IACjF;IACA;IACA;IACA;IAAAX,eAAA,iBACS,IAAI,CAACG,SAAS,IAAI,SAAS,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,IAAI,CAACO,MAAM;EAC7D;AAGpB;AAACC,SAAA,GA/CKhB,QAAQ;AAAAE,eAAA,CAARF,QAAQ,wBAAAiB,kBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA6CyFlB,SAAQ;AAAA;AAAAE,eAAA,CA7CzGF,QAAQ,+BAgDmET,EAAE,CAAA4B,kBAAA;EAAAC,KAAA,EAFwBpB,SAAQ;EAAAqB,OAAA,EAARrB,SAAQ,CAAAsB,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEvI;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFjC,EAAE,CAAAkC,iBAAA,CAAQzB,QAAQ,EAAc,CAAC;IACtG0B,IAAI,EAAEhC,UAAU;IAChBiC,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASvB,QAAQ,IAAI4B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}