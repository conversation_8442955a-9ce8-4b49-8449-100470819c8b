{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _BrowserDynamicTestingModule;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { createPlatformFactory, NgModule } from '@angular/core';\nimport { platformBrowserDynamic } from './platform-browser-dynamic.mjs';\nimport { BrowserTestingModule } from '@angular/platform-browser/testing';\nimport '@angular/compiler';\nimport '@angular/platform-browser';\n\n/**\n * @publicApi\n */\nconst platformBrowserDynamicTesting = createPlatformFactory(platformBrowserDynamic, 'browserDynamicTesting');\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserDynamicTestingModule {}\n_BrowserDynamicTestingModule = BrowserDynamicTestingModule;\n_defineProperty(BrowserDynamicTestingModule, \"\\u0275fac\", function _BrowserDynamicTestingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BrowserDynamicTestingModule)();\n});\n_defineProperty(BrowserDynamicTestingModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _BrowserDynamicTestingModule,\n  exports: [BrowserTestingModule]\n}));\n_defineProperty(BrowserDynamicTestingModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [BrowserTestingModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserDynamicTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserTestingModule]\n    }]\n  }], null, null);\n})();\nexport { BrowserDynamicTestingModule, platformBrowserDynamicTesting };", "map": {"version": 3, "names": ["i0", "createPlatformFactory", "NgModule", "platformBrowserDynamic", "BrowserTestingModule", "platformBrowserDynamicTesting", "BrowserDynamicTestingModule", "_BrowserDynamicTestingModule", "_defineProperty", "_BrowserDynamicTestingModule_Factory", "__ngFactoryType__", "ɵɵdefineNgModule", "type", "exports", "ɵɵdefineInjector", "imports", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/platform-browser-dynamic/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { createPlatformFactory, NgModule } from '@angular/core';\nimport { platformBrowserDynamic } from './platform-browser-dynamic.mjs';\nimport { BrowserTestingModule } from '@angular/platform-browser/testing';\nimport '@angular/compiler';\nimport '@angular/platform-browser';\n\n/**\n * @publicApi\n */\nconst platformBrowserDynamicTesting = createPlatformFactory(platformBrowserDynamic, 'browserDynamicTesting');\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserDynamicTestingModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserDynamicTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserDynamicTestingModule, exports: [BrowserTestingModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserDynamicTestingModule, imports: [BrowserTestingModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserDynamicTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserTestingModule],\n                }]\n        }] });\n\nexport { BrowserDynamicTestingModule, platformBrowserDynamicTesting };\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,qBAAqB,EAAEC,QAAQ,QAAQ,eAAe;AAC/D,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,OAAO,mBAAmB;AAC1B,OAAO,2BAA2B;;AAElC;AACA;AACA;AACA,MAAMC,6BAA6B,GAAGJ,qBAAqB,CAACE,sBAAsB,EAAE,uBAAuB,CAAC;AAC5G;AACA;AACA;AACA;AACA;AACA,MAAMG,2BAA2B,CAAC;AAIjCC,4BAAA,GAJKD,2BAA2B;AAAAE,eAAA,CAA3BF,2BAA2B,wBAAAG,qCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IACuEJ,4BAA2B;AAAA;AAAAE,eAAA,CAD7HF,2BAA2B,8BAKiDN,EAAE,CAAAW,gBAAA;EAAAC,IAAA,EAHqBN,4BAA2B;EAAAO,OAAA,GAAYT,oBAAoB;AAAA;AAAAI,eAAA,CAF9JF,2BAA2B,8BAKiDN,EAAE,CAAAc,gBAAA;EAAAC,OAAA,GAF4DX,oBAAoB;AAAA;AAEpK;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAAkFhB,EAAE,CAAAiB,iBAAA,CAAQX,2BAA2B,EAAc,CAAC;IAC1HM,IAAI,EAAEV,QAAQ;IACdgB,IAAI,EAAE,CAAC;MACCL,OAAO,EAAE,CAACT,oBAAoB;IAClC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASE,2BAA2B,EAAED,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}