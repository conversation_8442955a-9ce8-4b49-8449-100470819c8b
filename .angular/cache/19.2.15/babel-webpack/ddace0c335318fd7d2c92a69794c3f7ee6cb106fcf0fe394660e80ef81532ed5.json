{"ast": null, "code": "var SwuiNotificationsModule_1;\nimport { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { SwuiNotificationsService } from './swui-notifications.service';\nimport { SwuiSnackbarComponent } from './swui-snackbar/swui-snackbar.component';\nimport { MAT_SNACK_BAR_DEFAULT_OPTIONS, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nexport const notificationsMatModules = [MatButtonModule, MatIconModule, MatSnackBarModule];\nlet SwuiNotificationsModule = SwuiNotificationsModule_1 = class SwuiNotificationsModule {\n  static forRoot() {\n    return {\n      ngModule: SwuiNotificationsModule_1,\n      providers: [SwuiNotificationsService, {\n        provide: MAT_SNACK_BAR_DEFAULT_OPTIONS,\n        useValue: {\n          duration: 2500\n        }\n      }]\n    };\n  }\n};\nSwuiNotificationsModule = SwuiNotificationsModule_1 = __decorate([NgModule({\n  imports: [CommonModule, ...notificationsMatModules],\n  declarations: [SwuiSnackbarComponent],\n  exports: [...notificationsMatModules]\n})], SwuiNotificationsModule);\nexport { SwuiNotificationsModule };", "map": {"version": 3, "names": ["CommonModule", "NgModule", "SwuiNotificationsService", "SwuiSnackbarComponent", "MAT_SNACK_BAR_DEFAULT_OPTIONS", "MatSnackBarModule", "MatIconModule", "MatButtonModule", "notificationsMatModules", "SwuiNotificationsModule", "SwuiNotificationsModule_1", "forRoot", "ngModule", "providers", "provide", "useValue", "duration", "__decorate", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-notifications/swui-notifications.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ModuleWithProviders, NgModule } from '@angular/core';\n\nimport { SwuiNotificationsService } from './swui-notifications.service';\nimport { SwuiSnackbarComponent } from './swui-snackbar/swui-snackbar.component';\nimport { MAT_SNACK_BAR_DEFAULT_OPTIONS, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\n\nexport const notificationsMatModules = [\n  MatButtonModule,\n  MatIconModule,\n  MatSnackBarModule,\n];\n\n@NgModule({\n  imports: [\n    CommonModule,\n    ...notificationsMatModules,\n  ],\n  declarations: [\n    SwuiSnackbarComponent,\n  ],\n  exports: [\n    ...notificationsMatModules,\n  ],\n})\nexport class SwuiNotificationsModule {\n\n  static forRoot(): ModuleWithProviders<SwuiNotificationsModule> {\n    return {\n      ngModule: SwuiNotificationsModule,\n      providers: [\n        SwuiNotificationsService,\n        { provide: MAT_SNACK_BAR_DEFAULT_OPTIONS, useValue: { duration: 2500 } }\n      ]\n    };\n  }\n}\n"], "mappings": ";;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAA8BC,QAAQ,QAAQ,eAAe;AAE7D,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,6BAA6B,EAAEC,iBAAiB,QAAQ,6BAA6B;AAC9F,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,OAAO,MAAMC,uBAAuB,GAAG,CACrCD,eAAe,EACfD,aAAa,EACbD,iBAAiB,CAClB;AAcM,IAAMI,uBAAuB,GAAAC,yBAAA,GAA7B,MAAMD,uBAAuB;EAElC,OAAOE,OAAOA,CAAA;IACZ,OAAO;MACLC,QAAQ,EAAEF,yBAAuB;MACjCG,SAAS,EAAE,CACTX,wBAAwB,EACxB;QAAEY,OAAO,EAAEV,6BAA6B;QAAEW,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAAE,CAAE;KAE3E;EACH;CACD;AAXYP,uBAAuB,GAAAC,yBAAA,GAAAO,UAAA,EAZnChB,QAAQ,CAAC;EACRiB,OAAO,EAAE,CACPlB,YAAY,EACZ,GAAGQ,uBAAuB,CAC3B;EACDW,YAAY,EAAE,CACZhB,qBAAqB,CACtB;EACDiB,OAAO,EAAE,CACP,GAAGZ,uBAAuB;CAE7B,CAAC,C,EACWC,uBAAuB,CAWnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}