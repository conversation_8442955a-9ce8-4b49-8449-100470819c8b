{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { Subject } from 'rxjs';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n  /** Selected values. */\n  get selected() {\n    if (!this._selected) {\n      this._selected = Array.from(this._selection.values());\n    }\n    return this._selected;\n  }\n  /** Event emitted when the value has changed. */\n\n  constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n    _defineProperty(this, \"_multiple\", void 0);\n    _defineProperty(this, \"_emitChanges\", void 0);\n    _defineProperty(this, \"compareWith\", void 0);\n    /** Currently-selected values. */\n    _defineProperty(this, \"_selection\", new Set());\n    /** Keeps track of the deselected options that haven't been emitted by the change event. */\n    _defineProperty(this, \"_deselectedToEmit\", []);\n    /** Keeps track of the selected options that haven't been emitted by the change event. */\n    _defineProperty(this, \"_selectedToEmit\", []);\n    /** Cache for the array value of the selected items. */\n    _defineProperty(this, \"_selected\", void 0);\n    _defineProperty(this, \"changed\", new Subject());\n    this._multiple = _multiple;\n    this._emitChanges = _emitChanges;\n    this.compareWith = compareWith;\n    if (initiallySelectedValues && initiallySelectedValues.length) {\n      if (_multiple) {\n        initiallySelectedValues.forEach(value => this._markSelected(value));\n      } else {\n        this._markSelected(initiallySelectedValues[0]);\n      }\n      // Clear the array in order to avoid firing the change event for preselected values.\n      this._selectedToEmit.length = 0;\n    }\n  }\n  /**\n   * Selects a value or an array of values.\n   * @param values The values to select\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  select(...values) {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._markSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Deselects a value or an array of values.\n   * @param values The values to deselect\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  deselect(...values) {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Sets the selected values\n   * @param values The new selected values\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  setSelection(...values) {\n    this._verifyValueAssignment(values);\n    const oldValues = this.selected;\n    const newSelectedSet = new Set(values.map(value => this._getConcreteValue(value)));\n    values.forEach(value => this._markSelected(value));\n    oldValues.filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet))).forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Toggles a value between selected and deselected.\n   * @param value The value to toggle\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  toggle(value) {\n    return this.isSelected(value) ? this.deselect(value) : this.select(value);\n  }\n  /**\n   * Clears all of the selected values.\n   * @param flushEvent Whether to flush the changes in an event.\n   *   If false, the changes to the selection will be flushed along with the next event.\n   * @return Whether the selection changed as a result of this call\n   * @breaking-change 16.0.0 make return type boolean\n   */\n  clear(flushEvent = true) {\n    this._unmarkAll();\n    const changed = this._hasQueuedChanges();\n    if (flushEvent) {\n      this._emitChangeEvent();\n    }\n    return changed;\n  }\n  /**\n   * Determines whether a value is selected.\n   */\n  isSelected(value) {\n    return this._selection.has(this._getConcreteValue(value));\n  }\n  /**\n   * Determines whether the model does not have a value.\n   */\n  isEmpty() {\n    return this._selection.size === 0;\n  }\n  /**\n   * Determines whether the model has a value.\n   */\n  hasValue() {\n    return !this.isEmpty();\n  }\n  /**\n   * Sorts the selected values based on a predicate function.\n   */\n  sort(predicate) {\n    if (this._multiple && this.selected) {\n      this._selected.sort(predicate);\n    }\n  }\n  /**\n   * Gets whether multiple values can be selected.\n   */\n  isMultipleSelection() {\n    return this._multiple;\n  }\n  /** Emits a change event and clears the records of selected and deselected values. */\n  _emitChangeEvent() {\n    // Clear the selected values so they can be re-cached.\n    this._selected = null;\n    if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n      this.changed.next({\n        source: this,\n        added: this._selectedToEmit,\n        removed: this._deselectedToEmit\n      });\n      this._deselectedToEmit = [];\n      this._selectedToEmit = [];\n    }\n  }\n  /** Selects a value. */\n  _markSelected(value) {\n    value = this._getConcreteValue(value);\n    if (!this.isSelected(value)) {\n      if (!this._multiple) {\n        this._unmarkAll();\n      }\n      if (!this.isSelected(value)) {\n        this._selection.add(value);\n      }\n      if (this._emitChanges) {\n        this._selectedToEmit.push(value);\n      }\n    }\n  }\n  /** Deselects a value. */\n  _unmarkSelected(value) {\n    value = this._getConcreteValue(value);\n    if (this.isSelected(value)) {\n      this._selection.delete(value);\n      if (this._emitChanges) {\n        this._deselectedToEmit.push(value);\n      }\n    }\n  }\n  /** Clears out the selected values. */\n  _unmarkAll() {\n    if (!this.isEmpty()) {\n      this._selection.forEach(value => this._unmarkSelected(value));\n    }\n  }\n  /**\n   * Verifies the value assignment and throws an error if the specified value array is\n   * including multiple values while the selection model is not supporting multiple values.\n   */\n  _verifyValueAssignment(values) {\n    if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMultipleValuesInSingleSelectionError();\n    }\n  }\n  /** Whether there are queued up change to be emitted. */\n  _hasQueuedChanges() {\n    return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n  }\n  /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n  _getConcreteValue(inputValue, selection) {\n    if (!this.compareWith) {\n      return inputValue;\n    } else {\n      selection = selection !== null && selection !== void 0 ? selection : this._selection;\n      for (let selectedValue of selection) {\n        if (this.compareWith(inputValue, selectedValue)) {\n          return selectedValue;\n        }\n      }\n      return inputValue;\n    }\n  }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n  return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\nexport { SelectionModel as S, getMultipleValuesInSingleSelectionError as g };\n//# sourceMappingURL=selection-model-CeeHVIcP.mjs.map", "map": {"version": 3, "names": ["Subject", "SelectionModel", "selected", "_selected", "Array", "from", "_selection", "values", "constructor", "_multiple", "initiallySelectedValues", "_emitChanges", "compareWith", "_defineProperty", "Set", "length", "for<PERSON>ach", "value", "_markSelected", "_selectedToEmit", "select", "_verifyValueAssignment", "changed", "_hasQueuedChanges", "_emitChangeEvent", "deselect", "_unmarkSelected", "setSelection", "oldValues", "newSelectedSet", "map", "_getConcreteValue", "filter", "has", "toggle", "isSelected", "clear", "flushEvent", "_unmarkAll", "isEmpty", "size", "hasValue", "sort", "predicate", "isMultipleSelection", "_deselectedToEmit", "next", "source", "added", "removed", "add", "push", "delete", "ngDevMode", "getMultipleValuesInSingleSelectionError", "inputValue", "selection", "selected<PERSON><PERSON><PERSON>", "Error", "S", "g"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/selection-model-CeeHVIcP.mjs"], "sourcesContent": ["import { Subject } from 'rxjs';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n    _multiple;\n    _emitChanges;\n    compareWith;\n    /** Currently-selected values. */\n    _selection = new Set();\n    /** Keeps track of the deselected options that haven't been emitted by the change event. */\n    _deselectedToEmit = [];\n    /** Keeps track of the selected options that haven't been emitted by the change event. */\n    _selectedToEmit = [];\n    /** Cache for the array value of the selected items. */\n    _selected;\n    /** Selected values. */\n    get selected() {\n        if (!this._selected) {\n            this._selected = Array.from(this._selection.values());\n        }\n        return this._selected;\n    }\n    /** Event emitted when the value has changed. */\n    changed = new Subject();\n    constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n        this._multiple = _multiple;\n        this._emitChanges = _emitChanges;\n        this.compareWith = compareWith;\n        if (initiallySelectedValues && initiallySelectedValues.length) {\n            if (_multiple) {\n                initiallySelectedValues.forEach(value => this._markSelected(value));\n            }\n            else {\n                this._markSelected(initiallySelectedValues[0]);\n            }\n            // Clear the array in order to avoid firing the change event for preselected values.\n            this._selectedToEmit.length = 0;\n        }\n    }\n    /**\n     * Selects a value or an array of values.\n     * @param values The values to select\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    select(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._markSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Deselects a value or an array of values.\n     * @param values The values to deselect\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    deselect(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Sets the selected values\n     * @param values The new selected values\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    setSelection(...values) {\n        this._verifyValueAssignment(values);\n        const oldValues = this.selected;\n        const newSelectedSet = new Set(values.map(value => this._getConcreteValue(value)));\n        values.forEach(value => this._markSelected(value));\n        oldValues\n            .filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet)))\n            .forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Toggles a value between selected and deselected.\n     * @param value The value to toggle\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    toggle(value) {\n        return this.isSelected(value) ? this.deselect(value) : this.select(value);\n    }\n    /**\n     * Clears all of the selected values.\n     * @param flushEvent Whether to flush the changes in an event.\n     *   If false, the changes to the selection will be flushed along with the next event.\n     * @return Whether the selection changed as a result of this call\n     * @breaking-change 16.0.0 make return type boolean\n     */\n    clear(flushEvent = true) {\n        this._unmarkAll();\n        const changed = this._hasQueuedChanges();\n        if (flushEvent) {\n            this._emitChangeEvent();\n        }\n        return changed;\n    }\n    /**\n     * Determines whether a value is selected.\n     */\n    isSelected(value) {\n        return this._selection.has(this._getConcreteValue(value));\n    }\n    /**\n     * Determines whether the model does not have a value.\n     */\n    isEmpty() {\n        return this._selection.size === 0;\n    }\n    /**\n     * Determines whether the model has a value.\n     */\n    hasValue() {\n        return !this.isEmpty();\n    }\n    /**\n     * Sorts the selected values based on a predicate function.\n     */\n    sort(predicate) {\n        if (this._multiple && this.selected) {\n            this._selected.sort(predicate);\n        }\n    }\n    /**\n     * Gets whether multiple values can be selected.\n     */\n    isMultipleSelection() {\n        return this._multiple;\n    }\n    /** Emits a change event and clears the records of selected and deselected values. */\n    _emitChangeEvent() {\n        // Clear the selected values so they can be re-cached.\n        this._selected = null;\n        if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n            this.changed.next({\n                source: this,\n                added: this._selectedToEmit,\n                removed: this._deselectedToEmit,\n            });\n            this._deselectedToEmit = [];\n            this._selectedToEmit = [];\n        }\n    }\n    /** Selects a value. */\n    _markSelected(value) {\n        value = this._getConcreteValue(value);\n        if (!this.isSelected(value)) {\n            if (!this._multiple) {\n                this._unmarkAll();\n            }\n            if (!this.isSelected(value)) {\n                this._selection.add(value);\n            }\n            if (this._emitChanges) {\n                this._selectedToEmit.push(value);\n            }\n        }\n    }\n    /** Deselects a value. */\n    _unmarkSelected(value) {\n        value = this._getConcreteValue(value);\n        if (this.isSelected(value)) {\n            this._selection.delete(value);\n            if (this._emitChanges) {\n                this._deselectedToEmit.push(value);\n            }\n        }\n    }\n    /** Clears out the selected values. */\n    _unmarkAll() {\n        if (!this.isEmpty()) {\n            this._selection.forEach(value => this._unmarkSelected(value));\n        }\n    }\n    /**\n     * Verifies the value assignment and throws an error if the specified value array is\n     * including multiple values while the selection model is not supporting multiple values.\n     */\n    _verifyValueAssignment(values) {\n        if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMultipleValuesInSingleSelectionError();\n        }\n    }\n    /** Whether there are queued up change to be emitted. */\n    _hasQueuedChanges() {\n        return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n    }\n    /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n    _getConcreteValue(inputValue, selection) {\n        if (!this.compareWith) {\n            return inputValue;\n        }\n        else {\n            selection = selection ?? this._selection;\n            for (let selectedValue of selection) {\n                if (this.compareWith(inputValue, selectedValue)) {\n                    return selectedValue;\n                }\n            }\n            return inputValue;\n        }\n    }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n    return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\nexport { SelectionModel as S, getMultipleValuesInSingleSelectionError as g };\n//# sourceMappingURL=selection-model-CeeHVIcP.mjs.map\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,MAAM;;AAE9B;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EAYjB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,UAAU,CAACC,MAAM,CAAC,CAAC,CAAC;IACzD;IACA,OAAO,IAAI,CAACJ,SAAS;EACzB;EACA;;EAEAK,WAAWA,CAACC,SAAS,GAAG,KAAK,EAAEC,uBAAuB,EAAEC,YAAY,GAAG,IAAI,EAAEC,WAAW,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAjB1F;IAAAA,eAAA,qBACa,IAAIC,GAAG,CAAC,CAAC;IACtB;IAAAD,eAAA,4BACoB,EAAE;IACtB;IAAAA,eAAA,0BACkB,EAAE;IACpB;IAAAA,eAAA;IAAAA,eAAA,kBAUU,IAAIb,OAAO,CAAC,CAAC;IAEnB,IAAI,CAACS,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAIF,uBAAuB,IAAIA,uBAAuB,CAACK,MAAM,EAAE;MAC3D,IAAIN,SAAS,EAAE;QACXC,uBAAuB,CAACM,OAAO,CAACC,KAAK,IAAI,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAACC,aAAa,CAACR,uBAAuB,CAAC,CAAC,CAAC,CAAC;MAClD;MACA;MACA,IAAI,CAACS,eAAe,CAACJ,MAAM,GAAG,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,MAAMA,CAAC,GAAGb,MAAM,EAAE;IACd,IAAI,CAACc,sBAAsB,CAACd,MAAM,CAAC;IACnCA,MAAM,CAACS,OAAO,CAACC,KAAK,IAAI,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC,CAAC;IAClD,MAAMK,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAOF,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,QAAQA,CAAC,GAAGlB,MAAM,EAAE;IAChB,IAAI,CAACc,sBAAsB,CAACd,MAAM,CAAC;IACnCA,MAAM,CAACS,OAAO,CAACC,KAAK,IAAI,IAAI,CAACS,eAAe,CAACT,KAAK,CAAC,CAAC;IACpD,MAAMK,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAOF,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,YAAYA,CAAC,GAAGpB,MAAM,EAAE;IACpB,IAAI,CAACc,sBAAsB,CAACd,MAAM,CAAC;IACnC,MAAMqB,SAAS,GAAG,IAAI,CAAC1B,QAAQ;IAC/B,MAAM2B,cAAc,GAAG,IAAIf,GAAG,CAACP,MAAM,CAACuB,GAAG,CAACb,KAAK,IAAI,IAAI,CAACc,iBAAiB,CAACd,KAAK,CAAC,CAAC,CAAC;IAClFV,MAAM,CAACS,OAAO,CAACC,KAAK,IAAI,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC,CAAC;IAClDW,SAAS,CACJI,MAAM,CAACf,KAAK,IAAI,CAACY,cAAc,CAACI,GAAG,CAAC,IAAI,CAACF,iBAAiB,CAACd,KAAK,EAAEY,cAAc,CAAC,CAAC,CAAC,CACnFb,OAAO,CAACC,KAAK,IAAI,IAAI,CAACS,eAAe,CAACT,KAAK,CAAC,CAAC;IAClD,MAAMK,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAOF,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIY,MAAMA,CAACjB,KAAK,EAAE;IACV,OAAO,IAAI,CAACkB,UAAU,CAAClB,KAAK,CAAC,GAAG,IAAI,CAACQ,QAAQ,CAACR,KAAK,CAAC,GAAG,IAAI,CAACG,MAAM,CAACH,KAAK,CAAC;EAC7E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACImB,KAAKA,CAACC,UAAU,GAAG,IAAI,EAAE;IACrB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,MAAMhB,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxC,IAAIc,UAAU,EAAE;MACZ,IAAI,CAACb,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAOF,OAAO;EAClB;EACA;AACJ;AACA;EACIa,UAAUA,CAAClB,KAAK,EAAE;IACd,OAAO,IAAI,CAACX,UAAU,CAAC2B,GAAG,CAAC,IAAI,CAACF,iBAAiB,CAACd,KAAK,CAAC,CAAC;EAC7D;EACA;AACJ;AACA;EACIsB,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACjC,UAAU,CAACkC,IAAI,KAAK,CAAC;EACrC;EACA;AACJ;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACF,OAAO,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;EACIG,IAAIA,CAACC,SAAS,EAAE;IACZ,IAAI,IAAI,CAAClC,SAAS,IAAI,IAAI,CAACP,QAAQ,EAAE;MACjC,IAAI,CAACC,SAAS,CAACuC,IAAI,CAACC,SAAS,CAAC;IAClC;EACJ;EACA;AACJ;AACA;EACIC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACnC,SAAS;EACzB;EACA;EACAe,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAI,CAACrB,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACgB,eAAe,CAACJ,MAAM,IAAI,IAAI,CAAC8B,iBAAiB,CAAC9B,MAAM,EAAE;MAC9D,IAAI,CAACO,OAAO,CAACwB,IAAI,CAAC;QACdC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,IAAI,CAAC7B,eAAe;QAC3B8B,OAAO,EAAE,IAAI,CAACJ;MAClB,CAAC,CAAC;MACF,IAAI,CAACA,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAAC1B,eAAe,GAAG,EAAE;IAC7B;EACJ;EACA;EACAD,aAAaA,CAACD,KAAK,EAAE;IACjBA,KAAK,GAAG,IAAI,CAACc,iBAAiB,CAACd,KAAK,CAAC;IACrC,IAAI,CAAC,IAAI,CAACkB,UAAU,CAAClB,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC,IAAI,CAACR,SAAS,EAAE;QACjB,IAAI,CAAC6B,UAAU,CAAC,CAAC;MACrB;MACA,IAAI,CAAC,IAAI,CAACH,UAAU,CAAClB,KAAK,CAAC,EAAE;QACzB,IAAI,CAACX,UAAU,CAAC4C,GAAG,CAACjC,KAAK,CAAC;MAC9B;MACA,IAAI,IAAI,CAACN,YAAY,EAAE;QACnB,IAAI,CAACQ,eAAe,CAACgC,IAAI,CAAClC,KAAK,CAAC;MACpC;IACJ;EACJ;EACA;EACAS,eAAeA,CAACT,KAAK,EAAE;IACnBA,KAAK,GAAG,IAAI,CAACc,iBAAiB,CAACd,KAAK,CAAC;IACrC,IAAI,IAAI,CAACkB,UAAU,CAAClB,KAAK,CAAC,EAAE;MACxB,IAAI,CAACX,UAAU,CAAC8C,MAAM,CAACnC,KAAK,CAAC;MAC7B,IAAI,IAAI,CAACN,YAAY,EAAE;QACnB,IAAI,CAACkC,iBAAiB,CAACM,IAAI,CAAClC,KAAK,CAAC;MACtC;IACJ;EACJ;EACA;EACAqB,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;MACjB,IAAI,CAACjC,UAAU,CAACU,OAAO,CAACC,KAAK,IAAI,IAAI,CAACS,eAAe,CAACT,KAAK,CAAC,CAAC;IACjE;EACJ;EACA;AACJ;AACA;AACA;EACII,sBAAsBA,CAACd,MAAM,EAAE;IAC3B,IAAIA,MAAM,CAACQ,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACN,SAAS,KAAK,OAAO4C,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzF,MAAMC,uCAAuC,CAAC,CAAC;IACnD;EACJ;EACA;EACA/B,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,EAAE,IAAI,CAACsB,iBAAiB,CAAC9B,MAAM,IAAI,IAAI,CAACI,eAAe,CAACJ,MAAM,CAAC;EAC3E;EACA;EACAgB,iBAAiBA,CAACwB,UAAU,EAAEC,SAAS,EAAE;IACrC,IAAI,CAAC,IAAI,CAAC5C,WAAW,EAAE;MACnB,OAAO2C,UAAU;IACrB,CAAC,MACI;MACDC,SAAS,GAAGA,SAAS,aAATA,SAAS,cAATA,SAAS,GAAI,IAAI,CAAClD,UAAU;MACxC,KAAK,IAAImD,aAAa,IAAID,SAAS,EAAE;QACjC,IAAI,IAAI,CAAC5C,WAAW,CAAC2C,UAAU,EAAEE,aAAa,CAAC,EAAE;UAC7C,OAAOA,aAAa;QACxB;MACJ;MACA,OAAOF,UAAU;IACrB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,uCAAuCA,CAAA,EAAG;EAC/C,OAAOI,KAAK,CAAC,yEAAyE,CAAC;AAC3F;AAEA,SAASzD,cAAc,IAAI0D,CAAC,EAAEL,uCAAuC,IAAIM,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}