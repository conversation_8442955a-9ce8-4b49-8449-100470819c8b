{"ast": null, "code": "var _SwuiNotificationsTemplateComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-notifications-template.component.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { SwuiNotificationsService } from '../swui-notifications.service';\nlet SwuiNotificationsTemplateComponent = (_SwuiNotificationsTemplateComponent = class SwuiNotificationsTemplateComponent {\n  constructor(notifications) {\n    this.notifications = notifications;\n  }\n  showNotification(type) {\n    switch (type) {\n      case 'success':\n        this.notifications.success('Success message', 'default');\n        break;\n      case 'error':\n        this.notifications.error('Error message', '(4 sec) top', 'top', undefined, 4000);\n        break;\n      case 'warning':\n        this.notifications.warning('Warning message', '(5 sec) top right', 'top', 'right', 5000);\n        break;\n    }\n  }\n}, _SwuiNotificationsTemplateComponent.ctorParameters = () => [{\n  type: SwuiNotificationsService\n}], _SwuiNotificationsTemplateComponent);\nSwuiNotificationsTemplateComponent = __decorate([Component({\n  selector: 'lib-swui-notifications-template',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiNotificationsTemplateComponent);\nexport { SwuiNotificationsTemplateComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "SwuiNotificationsService", "SwuiNotificationsTemplateComponent", "_SwuiNotificationsTemplateComponent", "constructor", "notifications", "showNotification", "type", "success", "error", "undefined", "warning", "ctorParameters", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-notifications/stories/swui-notifications-template.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-notifications-template.component.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { SwuiNotificationsService } from '../swui-notifications.service';\nlet SwuiNotificationsTemplateComponent = class SwuiNotificationsTemplateComponent {\n    constructor(notifications) {\n        this.notifications = notifications;\n    }\n    showNotification(type) {\n        switch (type) {\n            case 'success':\n                this.notifications.success('Success message', 'default');\n                break;\n            case 'error':\n                this.notifications.error('Error message', '(4 sec) top', 'top', undefined, 4000);\n                break;\n            case 'warning':\n                this.notifications.warning('Warning message', '(5 sec) top right', 'top', 'right', 5000);\n                break;\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: SwuiNotificationsService }\n    ]; }\n};\nSwuiNotificationsTemplateComponent = __decorate([\n    Component({\n        selector: 'lib-swui-notifications-template',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiNotificationsTemplateComponent);\nexport { SwuiNotificationsTemplateComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,yDAAyD;AAC1F,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,IAAIC,kCAAkC,IAAAC,mCAAA,GAAG,MAAMD,kCAAkC,CAAC;EAC9EE,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAI,CAACA,aAAa,GAAGA,aAAa;EACtC;EACAC,gBAAgBA,CAACC,IAAI,EAAE;IACnB,QAAQA,IAAI;MACR,KAAK,SAAS;QACV,IAAI,CAACF,aAAa,CAACG,OAAO,CAAC,iBAAiB,EAAE,SAAS,CAAC;QACxD;MACJ,KAAK,OAAO;QACR,IAAI,CAACH,aAAa,CAACI,KAAK,CAAC,eAAe,EAAE,aAAa,EAAE,KAAK,EAAEC,SAAS,EAAE,IAAI,CAAC;QAChF;MACJ,KAAK,SAAS;QACV,IAAI,CAACL,aAAa,CAACM,OAAO,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC;QACxF;IACR;EACJ;AAIJ,CAAC,EAHYR,mCAAA,CAAKS,cAAc,GAAG,MAAM,CACjC;EAAEL,IAAI,EAAEN;AAAyB,CAAC,CACrC,EAAAE,mCAAA,CACJ;AACDD,kCAAkC,GAAGJ,UAAU,CAAC,CAC5CE,SAAS,CAAC;EACNa,QAAQ,EAAE,iCAAiC;EAC3CC,QAAQ,EAAEf,oBAAoB;EAC9BgB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEb,kCAAkC,CAAC;AACtC,SAASA,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}