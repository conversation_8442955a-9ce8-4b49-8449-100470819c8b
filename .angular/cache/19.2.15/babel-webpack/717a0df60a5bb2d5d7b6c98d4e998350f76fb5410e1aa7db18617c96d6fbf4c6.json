{"ast": null, "code": "var _SwuiTdColorfulLabelsWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./colorful-labels.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\n// tslint:disable:no-bitwise\nfunction hashCode(str) {\n  if (!str) {\n    return;\n  }\n  let hash = 0;\n  if (str.length === 0) {\n    return hash;\n  }\n  let i;\n  let chr;\n  for (i = 0; i < str.length; i++) {\n    chr = str.charCodeAt(i);\n    hash = (hash << 5) - hash + chr;\n    hash |= 0; // Convert to 32bit integer\n  }\n  return hash;\n}\nfunction scaleHash(hash, min, max) {\n  const maxHash = 2147483647;\n  const minHash = 0;\n  if (hash < 0) {\n    hash *= -1;\n  }\n  let percent = (hash - minHash) / (maxHash - minHash);\n  return Math.floor(percent * (max - min) + min);\n}\nlet SwuiTdColorfulLabelsWidgetComponent = (_SwuiTdColorfulLabelsWidgetComponent = class SwuiTdColorfulLabelsWidgetComponent {\n  constructor({\n    field,\n    value,\n    row\n  }) {\n    this.tones = ['-300', '-400', '', '-600', '-700', '-800'];\n    this.palettes = ['pink', 'violet', 'purple', 'indigo', 'blue', 'teal', 'green', 'orange', 'brown', 'grey', 'slate'];\n    this.value = value;\n    if (row.hasOwnProperty(field)) {\n      const value1 = row[field];\n      if (value1) {\n        this.value = value1;\n      }\n    }\n    this.colorfulClass = this.getRandomClassByValue(this.value) || '';\n  }\n  getRandomClassByValue(value) {\n    const classes = this.getAvailableClasses();\n    const hash = hashCode(value);\n    if (hash) {\n      const index = scaleHash(hash, 0, classes.length);\n      return classes[index];\n    }\n  }\n  getAvailableClasses() {\n    const result = [];\n    this.palettes.forEach(palette => {\n      this.tones.forEach(tone => {\n        result.push('bg-' + palette + tone);\n      });\n    });\n    return result;\n  }\n}, _SwuiTdColorfulLabelsWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdColorfulLabelsWidgetComponent);\nSwuiTdColorfulLabelsWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-colorful-labels-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdColorfulLabelsWidgetComponent);\nexport { SwuiTdColorfulLabelsWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "hashCode", "str", "hash", "length", "i", "chr", "charCodeAt", "scaleHash", "min", "max", "maxHash", "minHash", "percent", "Math", "floor", "SwuiTdColorfulLabelsWidgetComponent", "_SwuiTdColorfulLabelsWidgetComponent", "constructor", "field", "value", "row", "tones", "palettes", "hasOwnProperty", "value1", "colorfulClass", "getRandomClassByValue", "classes", "getAvailableClasses", "index", "result", "for<PERSON>ach", "palette", "tone", "push", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/colorful-labels/colorful-labels.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./colorful-labels.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\n// tslint:disable:no-bitwise\nfunction hashCode(str) {\n    if (!str) {\n        return;\n    }\n    let hash = 0;\n    if (str.length === 0) {\n        return hash;\n    }\n    let i;\n    let chr;\n    for (i = 0; i < str.length; i++) {\n        chr = str.charCodeAt(i);\n        hash = ((hash << 5) - hash) + chr;\n        hash |= 0; // Convert to 32bit integer\n    }\n    return hash;\n}\nfunction scaleHash(hash, min, max) {\n    const maxHash = 2147483647;\n    const minHash = 0;\n    if (hash < 0) {\n        hash *= -1;\n    }\n    let percent = (hash - minHash) / (maxHash - minHash);\n    return Math.floor(percent * (max - min) + min);\n}\nlet SwuiTdColorfulLabelsWidgetComponent = class SwuiTdColorfulLabelsWidgetComponent {\n    constructor({ field, value, row }) {\n        this.tones = ['-300', '-400', '', '-600', '-700', '-800'];\n        this.palettes = ['pink', 'violet', 'purple', 'indigo', 'blue', 'teal', 'green', 'orange', 'brown', 'grey', 'slate'];\n        this.value = value;\n        if (row.hasOwnProperty(field)) {\n            const value1 = row[field];\n            if (value1) {\n                this.value = value1;\n            }\n        }\n        this.colorfulClass = this.getRandomClassByValue(this.value) || '';\n    }\n    getRandomClassByValue(value) {\n        const classes = this.getAvailableClasses();\n        const hash = hashCode(value);\n        if (hash) {\n            const index = scaleHash(hash, 0, classes.length);\n            return classes[index];\n        }\n    }\n    getAvailableClasses() {\n        const result = [];\n        this.palettes.forEach(palette => {\n            this.tones.forEach(tone => {\n                result.push('bg-' + palette + tone);\n            });\n        });\n        return result;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdColorfulLabelsWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-colorful-labels-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdColorfulLabelsWidgetComponent);\nexport { SwuiTdColorfulLabelsWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D;AACA,SAASC,QAAQA,CAACC,GAAG,EAAE;EACnB,IAAI,CAACA,GAAG,EAAE;IACN;EACJ;EACA,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAID,GAAG,CAACE,MAAM,KAAK,CAAC,EAAE;IAClB,OAAOD,IAAI;EACf;EACA,IAAIE,CAAC;EACL,IAAIC,GAAG;EACP,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC7BC,GAAG,GAAGJ,GAAG,CAACK,UAAU,CAACF,CAAC,CAAC;IACvBF,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAIG,GAAG;IACjCH,IAAI,IAAI,CAAC,CAAC,CAAC;EACf;EACA,OAAOA,IAAI;AACf;AACA,SAASK,SAASA,CAACL,IAAI,EAAEM,GAAG,EAAEC,GAAG,EAAE;EAC/B,MAAMC,OAAO,GAAG,UAAU;EAC1B,MAAMC,OAAO,GAAG,CAAC;EACjB,IAAIT,IAAI,GAAG,CAAC,EAAE;IACVA,IAAI,IAAI,CAAC,CAAC;EACd;EACA,IAAIU,OAAO,GAAG,CAACV,IAAI,GAAGS,OAAO,KAAKD,OAAO,GAAGC,OAAO,CAAC;EACpD,OAAOE,IAAI,CAACC,KAAK,CAACF,OAAO,IAAIH,GAAG,GAAGD,GAAG,CAAC,GAAGA,GAAG,CAAC;AAClD;AACA,IAAIO,mCAAmC,IAAAC,oCAAA,GAAG,MAAMD,mCAAmC,CAAC;EAChFE,WAAWA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAI,CAAC,EAAE;IAC/B,IAAI,CAACC,KAAK,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACzD,IAAI,CAACC,QAAQ,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;IACnH,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAIC,GAAG,CAACG,cAAc,CAACL,KAAK,CAAC,EAAE;MAC3B,MAAMM,MAAM,GAAGJ,GAAG,CAACF,KAAK,CAAC;MACzB,IAAIM,MAAM,EAAE;QACR,IAAI,CAACL,KAAK,GAAGK,MAAM;MACvB;IACJ;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACP,KAAK,CAAC,IAAI,EAAE;EACrE;EACAO,qBAAqBA,CAACP,KAAK,EAAE;IACzB,MAAMQ,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1C,MAAM1B,IAAI,GAAGF,QAAQ,CAACmB,KAAK,CAAC;IAC5B,IAAIjB,IAAI,EAAE;MACN,MAAM2B,KAAK,GAAGtB,SAAS,CAACL,IAAI,EAAE,CAAC,EAAEyB,OAAO,CAACxB,MAAM,CAAC;MAChD,OAAOwB,OAAO,CAACE,KAAK,CAAC;IACzB;EACJ;EACAD,mBAAmBA,CAAA,EAAG;IAClB,MAAME,MAAM,GAAG,EAAE;IACjB,IAAI,CAACR,QAAQ,CAACS,OAAO,CAACC,OAAO,IAAI;MAC7B,IAAI,CAACX,KAAK,CAACU,OAAO,CAACE,IAAI,IAAI;QACvBH,MAAM,CAACI,IAAI,CAAC,KAAK,GAAGF,OAAO,GAAGC,IAAI,CAAC;MACvC,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAOH,MAAM;EACjB;AAIJ,CAAC,EAHYd,oCAAA,CAAKmB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEtC,MAAM;IAAEyC,IAAI,EAAE,CAACxC,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAiB,oCAAA,CACJ;AACDD,mCAAmC,GAAGpB,UAAU,CAAC,CAC7CE,SAAS,CAAC;EACN2C,QAAQ,EAAE,oCAAoC;EAC9CC,QAAQ,EAAE7C,oBAAoB;EAC9B8C,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAE3B,mCAAmC,CAAC;AACvC,SAASA,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}