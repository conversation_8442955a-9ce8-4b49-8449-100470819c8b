{"ast": null, "code": "export function noop() {}\n//# sourceMappingURL=noop.js.map", "map": {"version": 3, "names": ["noop"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/noop.js"], "sourcesContent": ["export function noop() { }\n//# sourceMappingURL=noop.js.map"], "mappings": "AAAA,OAAO,SAASA,IAAIA,CAAA,EAAG,CAAE;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}