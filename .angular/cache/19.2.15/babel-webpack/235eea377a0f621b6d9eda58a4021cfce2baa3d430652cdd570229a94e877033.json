{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport var asyncScheduler = new AsyncScheduler(AsyncAction);\nexport var async = asyncScheduler;\n//# sourceMappingURL=async.js.map", "map": {"version": 3, "names": ["AsyncAction", "AsyncScheduler", "asyncScheduler", "async"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/async.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport var asyncScheduler = new AsyncScheduler(AsyncAction);\nexport var async = asyncScheduler;\n//# sourceMappingURL=async.js.map"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,IAAIC,cAAc,GAAG,IAAID,cAAc,CAACD,WAAW,CAAC;AAC3D,OAAO,IAAIG,KAAK,GAAGD,cAAc;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}