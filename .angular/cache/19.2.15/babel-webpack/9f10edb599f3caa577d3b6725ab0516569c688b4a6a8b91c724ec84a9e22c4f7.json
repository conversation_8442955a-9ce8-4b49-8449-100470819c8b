{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { HubSelectorComponent } from './hub-selector.component';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatRippleModule } from '@angular/material/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\ndescribe('HubSelectorComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      schemas: [NO_ERRORS_SCHEMA],\n      declarations: [HubSelectorComponent],\n      imports: [TranslateModule.forRoot(), MatRippleModule, MatMenuModule, MatIconModule],\n      providers: [{\n        provide: SWUI_HUB_MESSAGE_CONFIG,\n        useValue: {}\n      }, {\n        provide: SwHubConfigService,\n        useValue: {}\n      }, {\n        provide: SwHubAuthService,\n        useValue: {\n          isLogged() {\n            return true;\n          }\n        }\n      }]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(HubSelectorComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "HubSelectorComponent", "NO_ERRORS_SCHEMA", "TranslateModule", "MatIconModule", "MatMenuModule", "MatRippleModule", "SWUI_HUB_MESSAGE_CONFIG", "SwHubConfigService", "SwHubAuthService", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "schemas", "declarations", "imports", "forRoot", "providers", "provide", "useValue", "isLogged", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/hub-selector/hub-selector.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\n\nimport { HubSelectorComponent } from './hub-selector.component';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatRippleModule } from '@angular/material/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\n\n\ndescribe('HubSelectorComponent', () => {\n  let component: HubSelectorComponent;\n  let fixture: ComponentFixture<HubSelectorComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      schemas: [NO_ERRORS_SCHEMA],\n      declarations: [HubSelectorComponent],\n      imports: [\n        TranslateModule.forRoot(),\n        MatRippleModule,\n        MatMenuModule,\n        MatIconModule,\n      ],\n      providers: [\n        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: {} },\n        { provide: SwHubConfigService, useValue: {} },\n        {\n          provide: SwHubAuthService, useValue: {\n            isLogged() {\n              return true;\n            }\n          }\n        },\n      ]\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(HubSelectorComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,gBAAgB,QAAQ,gDAAgD;AAGjFC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;EACpC,IAAIC,SAA+B;EACnC,IAAIC,OAA+C;EAEnDC,UAAU,CAACb,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACe,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACb,gBAAgB,CAAC;MAC3Bc,YAAY,EAAE,CAACf,oBAAoB,CAAC;MACpCgB,OAAO,EAAE,CACPd,eAAe,CAACe,OAAO,EAAE,EACzBZ,eAAe,EACfD,aAAa,EACbD,aAAa,CACd;MACDe,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEb,uBAAuB;QAAEc,QAAQ,EAAE;MAAE,CAAE,EAClD;QAAED,OAAO,EAAEZ,kBAAkB;QAAEa,QAAQ,EAAE;MAAE,CAAE,EAC7C;QACED,OAAO,EAAEX,gBAAgB;QAAEY,QAAQ,EAAE;UACnCC,QAAQA,CAAA;YACN,OAAO,IAAI;UACb;;OAEH;KAEJ,CAAC,CACCC,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHV,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGb,OAAO,CAACyB,eAAe,CAACvB,oBAAoB,CAAC;IACvDU,SAAS,GAAGC,OAAO,CAACa,iBAAiB;IACrCb,OAAO,CAACc,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACjB,SAAS,CAAC,CAACkB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}