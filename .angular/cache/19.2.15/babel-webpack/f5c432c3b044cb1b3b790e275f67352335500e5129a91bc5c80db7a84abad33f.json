{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nlet MatDynamicFormService = class MatDynamicFormService {\n  constructor() {\n    this._timezone = new BehaviorSubject('');\n  }\n  setTimezone(timezone) {\n    this._timezone.next(timezone);\n  }\n  get timezone() {\n    return this._timezone.asObservable();\n  }\n};\nMatDynamicFormService = __decorate([Injectable()], MatDynamicFormService);\nexport { MatDynamicFormService };", "map": {"version": 3, "names": ["__decorate", "Injectable", "BehaviorSubject", "MatDynamicFormService", "constructor", "_timezone", "setTimezone", "timezone", "next", "asObservable"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/mat-dynamic-form.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nlet MatDynamicFormService = class MatDynamicFormService {\n    constructor() {\n        this._timezone = new BehaviorSubject('');\n    }\n    setTimezone(timezone) {\n        this._timezone.next(timezone);\n    }\n    get timezone() {\n        return this._timezone.asObservable();\n    }\n};\nMatDynamicFormService = __decorate([\n    Injectable()\n], MatDynamicFormService);\nexport { MatDynamicFormService };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAQ,MAAM;AACtC,IAAIC,qBAAqB,GAAG,MAAMA,qBAAqB,CAAC;EACpDC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,GAAG,IAAIH,eAAe,CAAC,EAAE,CAAC;EAC5C;EACAI,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACF,SAAS,CAACG,IAAI,CAACD,QAAQ,CAAC;EACjC;EACA,IAAIA,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,SAAS,CAACI,YAAY,CAAC,CAAC;EACxC;AACJ,CAAC;AACDN,qBAAqB,GAAGH,UAAU,CAAC,CAC/BC,UAAU,CAAC,CAAC,CACf,EAAEE,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}