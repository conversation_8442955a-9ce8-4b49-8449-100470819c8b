{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { SwuiDateTimeChooserComponent } from './swui-date-time-chooser.component';\nimport { DATE_TIME_CHOOSER_MODULES } from './swui-date-time-chooser.module';\nimport * as moment from 'moment';\ndescribe('SwuiDateTimeChooserComponent', () => {\n  let component;\n  let fixture;\n  let host;\n  let testIsoString;\n  let testTimeZone;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SwuiDateTimeChooserComponent],\n      imports: [BrowserAnimationsModule, ...DATE_TIME_CHOOSER_MODULES]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiDateTimeChooserComponent);\n    component = fixture.componentInstance;\n    host = fixture.debugElement;\n    testIsoString = '2020-06-30T01:01:01.000Z';\n    testTimeZone = 'Pacific/Tongatapu';\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should set tabindex', () => {\n    expect(host.nativeElement.getAttribute('tabindex')).toBe('0');\n  });\n  it('should disable calendar', () => {\n    expect(component.isDisabled).toBeFalsy();\n    expect(component.form.disabled).toBeFalsy();\n    component.setDisabledState(true);\n    expect(component.isDisabled).toBeTruthy();\n    expect(component.form.disabled).toBeTruthy();\n    component.setDisabledState(false);\n    expect(component.isDisabled).toBeFalsy();\n    expect(component.form.disabled).toBeFalsy();\n  });\n  it('should call onTouched on blur', () => {\n    spyOn(component, 'onTouched');\n    dispatchFakeEvent(host.nativeElement, 'blur');\n    expect(component.onTouched).toHaveBeenCalled();\n  });\n  it('should set onChange in registerOnChange', () => {\n    let test = false;\n    const fn = () => {\n      test = true;\n    };\n    component.registerOnChange(fn);\n    component.dateControl.patchValue(testIsoString);\n    expect(test).toBe(true);\n  });\n  it('should set onChange in registerOnTouched', () => {\n    let test = false;\n    const fn = () => {\n      test = true;\n    };\n    component.registerOnTouched(fn);\n    dispatchFakeEvent(host.nativeElement, 'blur');\n    expect(test).toBe(true);\n  });\n  it('should set value', () => {\n    component.value = testIsoString;\n    expect(component.value).toBe(testIsoString);\n    component.value = 'wrong_string';\n    expect(component.value).toBe('');\n  });\n  it('should writeValue', () => {\n    component.writeValue(testIsoString);\n    expect(component.value).toBe(testIsoString);\n    component.writeValue('wrong_string');\n    expect(component.value).toBe('');\n  });\n  it('should set timeZone', () => {\n    component.timeZone = testTimeZone;\n    expect(component.timeZone).toBe(testTimeZone);\n  });\n  it('should set minDate', () => {\n    const testDate = moment().clone().add(-1, 'day');\n    component.minDate = testDate.clone().toISOString();\n    expect(component.minDate).toEqual(testDate.toISOString());\n  });\n  it('should set maxDate', () => {\n    const testDate = moment().clone().add(-1, 'day');\n    component.maxDate = testDate.clone().toISOString();\n    expect(component.maxDate).toEqual(testDate.toISOString());\n  });\n  it('should set timepicker', () => {\n    component.timePicker = true;\n    expect(component.timeControl.disabled).toBe(false);\n    component.timePicker = false;\n    expect(component.timeControl.disabled).toBe(true);\n  });\n  it('should patch form', () => {\n    spyOn(component, 'onChange');\n    component.timeZone = testTimeZone; // +13h\n    component.value = testIsoString;\n    component.timePicker = true;\n    const expected = {\n      date: testIsoString,\n      time: {\n        hour: 14,\n        minute: 1,\n        second: 1\n      }\n    };\n    expect(component.form.value).toEqual(expected);\n  });\n});\nfunction createFakeEvent(type) {\n  const event = document.createEvent('Event');\n  event.initEvent(type, true, true);\n  return event;\n}\nfunction dispatchFakeEvent(node, type) {\n  node.dispatchEvent(createFakeEvent(type));\n}", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "BrowserAnimationsModule", "SwuiDateTimeChooserComponent", "DATE_TIME_CHOOSER_MODULES", "moment", "describe", "component", "fixture", "host", "testIsoString", "testTimeZone", "beforeEach", "configureTestingModule", "declarations", "imports", "compileComponents", "createComponent", "componentInstance", "debugElement", "detectChanges", "it", "expect", "toBeTruthy", "nativeElement", "getAttribute", "toBe", "isDisabled", "toBeFalsy", "form", "disabled", "setDisabledState", "spyOn", "dispatchFakeEvent", "onTouched", "toHaveBeenCalled", "test", "fn", "registerOnChange", "dateControl", "patchValue", "registerOnTouched", "value", "writeValue", "timeZone", "testDate", "clone", "add", "minDate", "toISOString", "toEqual", "maxDate", "timePicker", "timeControl", "expected", "date", "time", "hour", "minute", "second", "createFakeEvent", "type", "event", "document", "createEvent", "initEvent", "node", "dispatchEvent"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-chooser/swui-date-time-chooser.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { DebugElement } from '@angular/core';\n\nimport { SwuiDateTimeChooserComponent } from './swui-date-time-chooser.component';\nimport { DATE_TIME_CHOOSER_MODULES } from './swui-date-time-chooser.module';\nimport * as moment from 'moment';\n\n\ndescribe('SwuiDateTimeChooserComponent', () => {\n  let component: SwuiDateTimeChooserComponent;\n  let fixture: ComponentFixture<SwuiDateTimeChooserComponent>;\n  let host: DebugElement;\n  let testIsoString: string;\n  let testTimeZone: string;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SwuiDateTimeChooserComponent],\n      imports: [\n        BrowserAnimationsModule,\n        ...DATE_TIME_CHOOSER_MODULES\n      ]\n    }).compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiDateTimeChooserComponent);\n    component = fixture.componentInstance;\n    host = fixture.debugElement;\n    testIsoString = '2020-06-30T01:01:01.000Z';\n    testTimeZone = 'Pacific/Tongatapu';\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should set tabindex', () => {\n    expect(host.nativeElement.getAttribute('tabindex')).toBe('0');\n  });\n\n  it('should disable calendar', () => {\n    expect(component.isDisabled).toBeFalsy();\n    expect(component.form.disabled).toBeFalsy();\n\n    component.setDisabledState(true);\n    expect(component.isDisabled).toBeTruthy();\n    expect(component.form.disabled).toBeTruthy();\n\n    component.setDisabledState(false);\n    expect(component.isDisabled).toBeFalsy();\n    expect(component.form.disabled).toBeFalsy();\n  });\n\n  it('should call onTouched on blur', () => {\n    spyOn(component, 'onTouched');\n    dispatchFakeEvent(host.nativeElement, 'blur');\n    expect(component.onTouched).toHaveBeenCalled();\n  });\n\n  it('should set onChange in registerOnChange', () => {\n    let test = false;\n    const fn = () => {\n      test = true;\n    };\n    component.registerOnChange(fn);\n    component.dateControl.patchValue(testIsoString);\n    expect(test).toBe(true);\n  });\n\n  it('should set onChange in registerOnTouched', () => {\n    let test = false;\n    const fn = () => {\n      test = true;\n    };\n    component.registerOnTouched(fn);\n    dispatchFakeEvent(host.nativeElement, 'blur');\n    expect(test).toBe(true);\n  });\n\n  it('should set value', () => {\n    component.value = testIsoString;\n    expect(component.value).toBe(testIsoString);\n\n    component.value = 'wrong_string';\n    expect(component.value).toBe('');\n  });\n\n  it('should writeValue', () => {\n    component.writeValue(testIsoString);\n    expect(component.value).toBe(testIsoString);\n\n    component.writeValue('wrong_string');\n    expect(component.value).toBe('');\n  });\n\n  it('should set timeZone', () => {\n    component.timeZone = testTimeZone;\n    expect(component.timeZone).toBe(testTimeZone);\n  });\n\n  it('should set minDate', () => {\n    const testDate = moment().clone().add(-1, 'day');\n    component.minDate = testDate.clone().toISOString();\n\n    expect(component.minDate).toEqual(testDate.toISOString());\n  });\n\n  it('should set maxDate', () => {\n    const testDate = moment().clone().add(-1, 'day');\n    component.maxDate = testDate.clone().toISOString();\n\n    expect(component.maxDate).toEqual(testDate.toISOString());\n  });\n\n  it('should set timepicker', () => {\n    component.timePicker = true;\n    expect(component.timeControl.disabled).toBe(false);\n\n    component.timePicker = false;\n    expect(component.timeControl.disabled).toBe(true);\n  });\n\n  it('should patch form', () => {\n    spyOn(component, 'onChange');\n    component.timeZone = testTimeZone; // +13h\n    component.value = testIsoString;\n    component.timePicker = true;\n\n    const expected = {\n      date: testIsoString,\n      time: {\n        hour: 14,\n        minute: 1,\n        second: 1\n      }\n    };\n    expect(component.form.value).toEqual(expected);\n  });\n});\n\nfunction createFakeEvent( type: string ) {\n  const event = document.createEvent('Event');\n  event.initEvent(type, true, true);\n  return event;\n}\n\nfunction dispatchFakeEvent( node: Node | Window, type: string ) {\n  node.dispatchEvent(createFakeEvent(type));\n}\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAC/E,SAASC,uBAAuB,QAAQ,sCAAsC;AAG9E,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhCC,QAAQ,CAAC,8BAA8B,EAAE,MAAK;EAC5C,IAAIC,SAAuC;EAC3C,IAAIC,OAAuD;EAC3D,IAAIC,IAAkB;EACtB,IAAIC,aAAqB;EACzB,IAAIC,YAAoB;EAExBC,UAAU,CAACX,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACa,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACX,4BAA4B,CAAC;MAC5CY,OAAO,EAAE,CACPb,uBAAuB,EACvB,GAAGE,yBAAyB;KAE/B,CAAC,CAACY,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHJ,UAAU,CAAC,MAAK;IACdJ,OAAO,GAAGR,OAAO,CAACiB,eAAe,CAACd,4BAA4B,CAAC;IAC/DI,SAAS,GAAGC,OAAO,CAACU,iBAAiB;IACrCT,IAAI,GAAGD,OAAO,CAACW,YAAY;IAC3BT,aAAa,GAAG,0BAA0B;IAC1CC,YAAY,GAAG,mBAAmB;IAClCH,OAAO,CAACY,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACf,SAAS,CAAC,CAACgB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7BC,MAAM,CAACb,IAAI,CAACe,aAAa,CAACC,YAAY,CAAC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAC/D,CAAC,CAAC;EAEFL,EAAE,CAAC,yBAAyB,EAAE,MAAK;IACjCC,MAAM,CAACf,SAAS,CAACoB,UAAU,CAAC,CAACC,SAAS,EAAE;IACxCN,MAAM,CAACf,SAAS,CAACsB,IAAI,CAACC,QAAQ,CAAC,CAACF,SAAS,EAAE;IAE3CrB,SAAS,CAACwB,gBAAgB,CAAC,IAAI,CAAC;IAChCT,MAAM,CAACf,SAAS,CAACoB,UAAU,CAAC,CAACJ,UAAU,EAAE;IACzCD,MAAM,CAACf,SAAS,CAACsB,IAAI,CAACC,QAAQ,CAAC,CAACP,UAAU,EAAE;IAE5ChB,SAAS,CAACwB,gBAAgB,CAAC,KAAK,CAAC;IACjCT,MAAM,CAACf,SAAS,CAACoB,UAAU,CAAC,CAACC,SAAS,EAAE;IACxCN,MAAM,CAACf,SAAS,CAACsB,IAAI,CAACC,QAAQ,CAAC,CAACF,SAAS,EAAE;EAC7C,CAAC,CAAC;EAEFP,EAAE,CAAC,+BAA+B,EAAE,MAAK;IACvCW,KAAK,CAACzB,SAAS,EAAE,WAAW,CAAC;IAC7B0B,iBAAiB,CAACxB,IAAI,CAACe,aAAa,EAAE,MAAM,CAAC;IAC7CF,MAAM,CAACf,SAAS,CAAC2B,SAAS,CAAC,CAACC,gBAAgB,EAAE;EAChD,CAAC,CAAC;EAEFd,EAAE,CAAC,yCAAyC,EAAE,MAAK;IACjD,IAAIe,IAAI,GAAG,KAAK;IAChB,MAAMC,EAAE,GAAGA,CAAA,KAAK;MACdD,IAAI,GAAG,IAAI;IACb,CAAC;IACD7B,SAAS,CAAC+B,gBAAgB,CAACD,EAAE,CAAC;IAC9B9B,SAAS,CAACgC,WAAW,CAACC,UAAU,CAAC9B,aAAa,CAAC;IAC/CY,MAAM,CAACc,IAAI,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC;EACzB,CAAC,CAAC;EAEFL,EAAE,CAAC,0CAA0C,EAAE,MAAK;IAClD,IAAIe,IAAI,GAAG,KAAK;IAChB,MAAMC,EAAE,GAAGA,CAAA,KAAK;MACdD,IAAI,GAAG,IAAI;IACb,CAAC;IACD7B,SAAS,CAACkC,iBAAiB,CAACJ,EAAE,CAAC;IAC/BJ,iBAAiB,CAACxB,IAAI,CAACe,aAAa,EAAE,MAAM,CAAC;IAC7CF,MAAM,CAACc,IAAI,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC;EACzB,CAAC,CAAC;EAEFL,EAAE,CAAC,kBAAkB,EAAE,MAAK;IAC1Bd,SAAS,CAACmC,KAAK,GAAGhC,aAAa;IAC/BY,MAAM,CAACf,SAAS,CAACmC,KAAK,CAAC,CAAChB,IAAI,CAAChB,aAAa,CAAC;IAE3CH,SAAS,CAACmC,KAAK,GAAG,cAAc;IAChCpB,MAAM,CAACf,SAAS,CAACmC,KAAK,CAAC,CAAChB,IAAI,CAAC,EAAE,CAAC;EAClC,CAAC,CAAC;EAEFL,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3Bd,SAAS,CAACoC,UAAU,CAACjC,aAAa,CAAC;IACnCY,MAAM,CAACf,SAAS,CAACmC,KAAK,CAAC,CAAChB,IAAI,CAAChB,aAAa,CAAC;IAE3CH,SAAS,CAACoC,UAAU,CAAC,cAAc,CAAC;IACpCrB,MAAM,CAACf,SAAS,CAACmC,KAAK,CAAC,CAAChB,IAAI,CAAC,EAAE,CAAC;EAClC,CAAC,CAAC;EAEFL,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7Bd,SAAS,CAACqC,QAAQ,GAAGjC,YAAY;IACjCW,MAAM,CAACf,SAAS,CAACqC,QAAQ,CAAC,CAAClB,IAAI,CAACf,YAAY,CAAC;EAC/C,CAAC,CAAC;EAEFU,EAAE,CAAC,oBAAoB,EAAE,MAAK;IAC5B,MAAMwB,QAAQ,GAAGxC,MAAM,EAAE,CAACyC,KAAK,EAAE,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;IAChDxC,SAAS,CAACyC,OAAO,GAAGH,QAAQ,CAACC,KAAK,EAAE,CAACG,WAAW,EAAE;IAElD3B,MAAM,CAACf,SAAS,CAACyC,OAAO,CAAC,CAACE,OAAO,CAACL,QAAQ,CAACI,WAAW,EAAE,CAAC;EAC3D,CAAC,CAAC;EAEF5B,EAAE,CAAC,oBAAoB,EAAE,MAAK;IAC5B,MAAMwB,QAAQ,GAAGxC,MAAM,EAAE,CAACyC,KAAK,EAAE,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;IAChDxC,SAAS,CAAC4C,OAAO,GAAGN,QAAQ,CAACC,KAAK,EAAE,CAACG,WAAW,EAAE;IAElD3B,MAAM,CAACf,SAAS,CAAC4C,OAAO,CAAC,CAACD,OAAO,CAACL,QAAQ,CAACI,WAAW,EAAE,CAAC;EAC3D,CAAC,CAAC;EAEF5B,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/Bd,SAAS,CAAC6C,UAAU,GAAG,IAAI;IAC3B9B,MAAM,CAACf,SAAS,CAAC8C,WAAW,CAACvB,QAAQ,CAAC,CAACJ,IAAI,CAAC,KAAK,CAAC;IAElDnB,SAAS,CAAC6C,UAAU,GAAG,KAAK;IAC5B9B,MAAM,CAACf,SAAS,CAAC8C,WAAW,CAACvB,QAAQ,CAAC,CAACJ,IAAI,CAAC,IAAI,CAAC;EACnD,CAAC,CAAC;EAEFL,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BW,KAAK,CAACzB,SAAS,EAAE,UAAU,CAAC;IAC5BA,SAAS,CAACqC,QAAQ,GAAGjC,YAAY,CAAC,CAAC;IACnCJ,SAAS,CAACmC,KAAK,GAAGhC,aAAa;IAC/BH,SAAS,CAAC6C,UAAU,GAAG,IAAI;IAE3B,MAAME,QAAQ,GAAG;MACfC,IAAI,EAAE7C,aAAa;MACnB8C,IAAI,EAAE;QACJC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE;;KAEX;IACDrC,MAAM,CAACf,SAAS,CAACsB,IAAI,CAACa,KAAK,CAAC,CAACQ,OAAO,CAACI,QAAQ,CAAC;EAChD,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,SAASM,eAAeA,CAAEC,IAAY;EACpC,MAAMC,KAAK,GAAGC,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC;EAC3CF,KAAK,CAACG,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjC,OAAOC,KAAK;AACd;AAEA,SAAS7B,iBAAiBA,CAAEiC,IAAmB,EAAEL,IAAY;EAC3DK,IAAI,CAACC,aAAa,CAACP,eAAe,CAACC,IAAI,CAAC,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}