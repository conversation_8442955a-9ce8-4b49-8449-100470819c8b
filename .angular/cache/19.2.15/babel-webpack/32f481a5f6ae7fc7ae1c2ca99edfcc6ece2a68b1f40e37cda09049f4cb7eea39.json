{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuSelectModule } from '../../swui-menu-select/swui-menu-select.module';\nimport { SwuiColumnsManagementComponent } from './columns-management.component';\nlet SwuiColumnsManagementModule = class SwuiColumnsManagementModule {};\nSwuiColumnsManagementModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, MatButtonModule, MatTooltipModule, MatMenuModule, SwuiMenuSelectModule],\n  declarations: [SwuiColumnsManagementComponent],\n  exports: [SwuiColumnsManagementComponent]\n})], SwuiColumnsManagementModule);\nexport { SwuiColumnsManagementModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "MatButtonModule", "MatMenuModule", "MatTooltipModule", "TranslateModule", "SwuiMenuSelectModule", "SwuiColumnsManagementComponent", "SwuiColumnsManagementModule", "__decorate", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/columns-management/columns-management.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuSelectModule } from '../../swui-menu-select/swui-menu-select.module';\nimport { SwuiColumnsManagementComponent } from './columns-management.component';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    MatButtonModule,\n    MatTooltipModule,\n    MatMenuModule,\n    SwuiMenuSelectModule,\n  ],\n  declarations: [\n    SwuiColumnsManagementComponent,\n  ],\n  exports: [\n    SwuiColumnsManagementComponent\n  ],\n})\nexport class SwuiColumnsManagementModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,oBAAoB,QAAQ,gDAAgD;AACrF,SAASC,8BAA8B,QAAQ,gCAAgC;AAmBxE,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B,GACvC;AADYA,2BAA2B,GAAAC,UAAA,EAhBvCT,QAAQ,CAAC;EACRU,OAAO,EAAE,CACPT,YAAY,EACZI,eAAe,EACfH,eAAe,EACfE,gBAAgB,EAChBD,aAAa,EACbG,oBAAoB,CACrB;EACDK,YAAY,EAAE,CACZJ,8BAA8B,CAC/B;EACDK,OAAO,EAAE,CACPL,8BAA8B;CAEjC,CAAC,C,EACWC,2BAA2B,CACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}