{"ast": null, "code": "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {\n    exports: {}\n  }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from)) if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n      get: () => from[key],\n      enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n    });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n// If the importer is in node compatibility mode or this is not an ESM\n// file that has been converted to a CommonJS file using a Babel-\n// compatible transform (i.e. \"__esModule\" has not been set), then set\n// \"default\" to the CommonJS \"module.exports\" for node compatibility.\nisNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", {\n  value: mod,\n  enumerable: true\n}) : target, mod));\n\n// src/dom-event.ts\nvar eventProperties = [\"bubbles\", \"cancelBubble\", \"cancelable\", \"composed\", \"currentTarget\", \"defaultPrevented\", \"eventPhase\", \"isTrusted\", \"returnValue\", \"srcElement\", \"target\", \"timeStamp\", \"type\"];\nvar customEventSpecificProperties = [\"detail\"];\nfunction extractEventHiddenProperties(event) {\n  const rebuildEvent = eventProperties.filter(value => event[value] !== void 0).reduce((acc, value) => {\n    acc[value] = event[value];\n    return acc;\n  }, {});\n  if (event instanceof CustomEvent) {\n    for (const value of customEventSpecificProperties.filter(value2 => event[value2] !== void 0)) {\n      rebuildEvent[value] = event[value];\n    }\n  }\n  return rebuildEvent;\n}\nexport { __commonJS, __toESM, extractEventHiddenProperties };", "map": {"version": 3, "names": ["__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__commonJS", "cb", "mod", "__require", "exports", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__toESM", "isNodeMode", "target", "__esModule", "value", "eventProperties", "customEventSpecificProperties", "extractEventHiddenProperties", "event", "rebuildEvent", "filter", "reduce", "acc", "CustomEvent", "value2"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@storybook/angular/node_modules/telejson/dist/chunk-EAFQLD22.mjs"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n// src/dom-event.ts\nvar eventProperties = [\n  \"bubbles\",\n  \"cancelBubble\",\n  \"cancelable\",\n  \"composed\",\n  \"currentTarget\",\n  \"defaultPrevented\",\n  \"eventPhase\",\n  \"isTrusted\",\n  \"returnValue\",\n  \"srcElement\",\n  \"target\",\n  \"timeStamp\",\n  \"type\"\n];\nvar customEventSpecificProperties = [\"detail\"];\nfunction extractEventHiddenProperties(event) {\n  const rebuildEvent = eventProperties.filter((value) => event[value] !== void 0).reduce((acc, value) => {\n    acc[value] = event[value];\n    return acc;\n  }, {});\n  if (event instanceof CustomEvent) {\n    for (const value of customEventSpecificProperties.filter(\n      (value2) => event[value2] !== void 0\n    )) {\n      rebuildEvent[value] = event[value];\n    }\n  }\n  return rebuildEvent;\n}\n\nexport {\n  __commonJS,\n  __toESM,\n  extractEventHiddenProperties\n};\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM;AAC5B,IAAIC,SAAS,GAAGF,MAAM,CAACG,cAAc;AACrC,IAAIC,gBAAgB,GAAGJ,MAAM,CAACK,wBAAwB;AACtD,IAAIC,iBAAiB,GAAGN,MAAM,CAACO,mBAAmB;AAClD,IAAIC,YAAY,GAAGR,MAAM,CAACS,cAAc;AACxC,IAAIC,YAAY,GAAGV,MAAM,CAACW,SAAS,CAACC,cAAc;AAClD,IAAIC,UAAU,GAAGA,CAACC,EAAE,EAAEC,GAAG,KAAK,SAASC,SAASA,CAAA,EAAG;EACjD,OAAOD,GAAG,IAAI,CAAC,CAAC,EAAED,EAAE,CAACR,iBAAiB,CAACQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACC,GAAG,GAAG;IAAEE,OAAO,EAAE,CAAC;EAAE,CAAC,EAAEA,OAAO,EAAEF,GAAG,CAAC,EAAEA,GAAG,CAACE,OAAO;AACpG,CAAC;AACD,IAAIC,WAAW,GAAGA,CAACC,EAAE,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,KAAK;EAC5C,IAAIF,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAClE,KAAK,IAAIG,GAAG,IAAIjB,iBAAiB,CAACc,IAAI,CAAC,EACrC,IAAI,CAACV,YAAY,CAACc,IAAI,CAACL,EAAE,EAAEI,GAAG,CAAC,IAAIA,GAAG,KAAKF,MAAM,EAC/CnB,SAAS,CAACiB,EAAE,EAAEI,GAAG,EAAE;MAAEE,GAAG,EAAEA,CAAA,KAAML,IAAI,CAACG,GAAG,CAAC;MAAEG,UAAU,EAAE,EAAEJ,IAAI,GAAGlB,gBAAgB,CAACgB,IAAI,EAAEG,GAAG,CAAC,CAAC,IAAID,IAAI,CAACI;IAAW,CAAC,CAAC;EACxH;EACA,OAAOP,EAAE;AACX,CAAC;AACD,IAAIQ,OAAO,GAAGA,CAACZ,GAAG,EAAEa,UAAU,EAAEC,MAAM,MAAMA,MAAM,GAAGd,GAAG,IAAI,IAAI,GAAGhB,QAAQ,CAACS,YAAY,CAACO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEG,WAAW;AAC9G;AACA;AACA;AACA;AACAU,UAAU,IAAI,CAACb,GAAG,IAAI,CAACA,GAAG,CAACe,UAAU,GAAG5B,SAAS,CAAC2B,MAAM,EAAE,SAAS,EAAE;EAAEE,KAAK,EAAEhB,GAAG;EAAEW,UAAU,EAAE;AAAK,CAAC,CAAC,GAAGG,MAAM,EAC/Gd,GACF,CAAC,CAAC;;AAEF;AACA,IAAIiB,eAAe,GAAG,CACpB,SAAS,EACT,cAAc,EACd,YAAY,EACZ,UAAU,EACV,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,WAAW,EACX,aAAa,EACb,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,MAAM,CACP;AACD,IAAIC,6BAA6B,GAAG,CAAC,QAAQ,CAAC;AAC9C,SAASC,4BAA4BA,CAACC,KAAK,EAAE;EAC3C,MAAMC,YAAY,GAAGJ,eAAe,CAACK,MAAM,CAAEN,KAAK,IAAKI,KAAK,CAACJ,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,CAACO,MAAM,CAAC,CAACC,GAAG,EAAER,KAAK,KAAK;IACrGQ,GAAG,CAACR,KAAK,CAAC,GAAGI,KAAK,CAACJ,KAAK,CAAC;IACzB,OAAOQ,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,IAAIJ,KAAK,YAAYK,WAAW,EAAE;IAChC,KAAK,MAAMT,KAAK,IAAIE,6BAA6B,CAACI,MAAM,CACrDI,MAAM,IAAKN,KAAK,CAACM,MAAM,CAAC,KAAK,KAAK,CACrC,CAAC,EAAE;MACDL,YAAY,CAACL,KAAK,CAAC,GAAGI,KAAK,CAACJ,KAAK,CAAC;IACpC;EACF;EACA,OAAOK,YAAY;AACrB;AAEA,SACEvB,UAAU,EACVc,OAAO,EACPO,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}