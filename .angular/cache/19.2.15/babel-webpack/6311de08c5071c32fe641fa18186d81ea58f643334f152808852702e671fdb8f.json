{"ast": null, "code": "export function createErrorClass(createImpl) {\n  var _super = function (instance) {\n    Error.call(instance);\n    instance.stack = new Error().stack;\n  };\n  var ctorFunc = createImpl(_super);\n  ctorFunc.prototype = Object.create(Error.prototype);\n  ctorFunc.prototype.constructor = ctorFunc;\n  return ctorFunc;\n}\n//# sourceMappingURL=createErrorClass.js.map", "map": {"version": 3, "names": ["createErrorClass", "createImpl", "_super", "instance", "Error", "call", "stack", "ctorFunc", "prototype", "Object", "create", "constructor"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/createErrorClass.js"], "sourcesContent": ["export function createErrorClass(createImpl) {\n    var _super = function (instance) {\n        Error.call(instance);\n        instance.stack = new Error().stack;\n    };\n    var ctorFunc = createImpl(_super);\n    ctorFunc.prototype = Object.create(Error.prototype);\n    ctorFunc.prototype.constructor = ctorFunc;\n    return ctorFunc;\n}\n//# sourceMappingURL=createErrorClass.js.map"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,UAAU,EAAE;EACzC,IAAIC,MAAM,GAAG,SAAAA,CAAUC,QAAQ,EAAE;IAC7BC,KAAK,CAACC,IAAI,CAACF,QAAQ,CAAC;IACpBA,QAAQ,CAACG,KAAK,GAAG,IAAIF,KAAK,CAAC,CAAC,CAACE,KAAK;EACtC,CAAC;EACD,IAAIC,QAAQ,GAAGN,UAAU,CAACC,MAAM,CAAC;EACjCK,QAAQ,CAACC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACN,KAAK,CAACI,SAAS,CAAC;EACnDD,QAAQ,CAACC,SAAS,CAACG,WAAW,GAAGJ,QAAQ;EACzC,OAAOA,QAAQ;AACnB;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}