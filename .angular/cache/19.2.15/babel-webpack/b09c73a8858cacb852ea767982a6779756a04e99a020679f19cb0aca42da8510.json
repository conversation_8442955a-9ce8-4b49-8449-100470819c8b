{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMultiselectComponent } from './swui-multiselect.component';\nimport { SwuiMenuSelectModule } from '../swui-menu-select/swui-menu-select.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatInputModule } from '@angular/material/input';\nexport const MULTISELECT_MODULES = [ReactiveFormsModule, MatInputModule, MatMenuModule, SwuiMenuSelectModule];\nlet SwuiMultiselectModule = class SwuiMultiselectModule {};\nSwuiMultiselectModule = __decorate([NgModule({\n  declarations: [SwuiMultiselectComponent],\n  imports: [CommonModule, TranslateModule.forChild(), ...MULTISELECT_MODULES],\n  exports: [SwuiMultiselectComponent]\n})], SwuiMultiselectModule);\nexport { SwuiMultiselectModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "ReactiveFormsModule", "TranslateModule", "SwuiMultiselectComponent", "SwuiMenuSelectModule", "MatMenuModule", "MatInputModule", "MULTISELECT_MODULES", "SwuiMultiselectModule", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-multiselect/swui-multiselect.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMultiselectComponent } from './swui-multiselect.component';\nimport { SwuiMenuSelectModule } from '../swui-menu-select/swui-menu-select.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatInputModule } from '@angular/material/input';\nexport const MULTISELECT_MODULES = [\n    ReactiveFormsModule,\n    MatInputModule,\n    MatMenuModule,\n    SwuiMenuSelectModule,\n];\nlet SwuiMultiselectModule = class SwuiMultiselectModule {\n};\nSwuiMultiselectModule = __decorate([\n    NgModule({\n        declarations: [SwuiMultiselectComponent],\n        imports: [\n            CommonModule,\n            TranslateModule.forChild(),\n            ...MULTISELECT_MODULES\n        ],\n        exports: [SwuiMultiselectComponent],\n    })\n], SwuiMultiselectModule);\nexport { SwuiMultiselectModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,oBAAoB,QAAQ,6CAA6C;AAClF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAO,MAAMC,mBAAmB,GAAG,CAC/BN,mBAAmB,EACnBK,cAAc,EACdD,aAAa,EACbD,oBAAoB,CACvB;AACD,IAAII,qBAAqB,GAAG,MAAMA,qBAAqB,CAAC,EACvD;AACDA,qBAAqB,GAAGV,UAAU,CAAC,CAC/BC,QAAQ,CAAC;EACLU,YAAY,EAAE,CAACN,wBAAwB,CAAC;EACxCO,OAAO,EAAE,CACLV,YAAY,EACZE,eAAe,CAACS,QAAQ,CAAC,CAAC,EAC1B,GAAGJ,mBAAmB,CACzB;EACDK,OAAO,EAAE,CAACT,wBAAwB;AACtC,CAAC,CAAC,CACL,EAAEK,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}