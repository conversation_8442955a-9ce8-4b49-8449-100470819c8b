{"ast": null, "code": "import { defer } from './defer';\nexport function iif(condition, trueResult, falseResult) {\n  return defer(function () {\n    return condition() ? trueResult : falseResult;\n  });\n}\n//# sourceMappingURL=iif.js.map", "map": {"version": 3, "names": ["defer", "iif", "condition", "trueResult", "falseResult"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/iif.js"], "sourcesContent": ["import { defer } from './defer';\nexport function iif(condition, trueResult, falseResult) {\n    return defer(function () { return (condition() ? trueResult : falseResult); });\n}\n//# sourceMappingURL=iif.js.map"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,GAAGA,CAACC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAE;EACpD,OAAOJ,KAAK,CAAC,YAAY;IAAE,OAAQE,SAAS,CAAC,CAAC,GAAGC,UAAU,GAAGC,WAAW;EAAG,CAAC,CAAC;AAClF;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}