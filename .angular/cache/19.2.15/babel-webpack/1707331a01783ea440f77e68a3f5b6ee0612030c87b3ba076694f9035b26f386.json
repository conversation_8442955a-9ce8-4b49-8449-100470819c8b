{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { QueryList, InjectionToken } from '@angular/core';\nimport { Subscription, isObservable, Subject, of } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { T as Typeahead } from './typeahead-9ZW4Dtsf.mjs';\nimport { coerceObservable } from './coercion/private.mjs';\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nclass TreeKeyManager {\n  _initializeFocus() {\n    if (this._hasInitialFocused || this._items.length === 0) {\n      return;\n    }\n    let activeIndex = 0;\n    for (let i = 0; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n        activeIndex = i;\n        break;\n      }\n    }\n    const activeItem = this._items[activeIndex];\n    // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n    // capture the focus since the user isn't interacting with it. See #29628.\n    if (activeItem.makeFocusable) {\n      var _this$_activeItem, _this$_typeahead;\n      (_this$_activeItem = this._activeItem) === null || _this$_activeItem === void 0 || _this$_activeItem.unfocus();\n      this._activeItemIndex = activeIndex;\n      this._activeItem = activeItem;\n      (_this$_typeahead = this._typeahead) === null || _this$_typeahead === void 0 || _this$_typeahead.setCurrentSelectedItemIndex(activeIndex);\n      activeItem.makeFocusable();\n    } else {\n      // Backwards compatibility for items that don't implement `makeFocusable`.\n      this.focusItem(activeIndex);\n    }\n    this._hasInitialFocused = true;\n  }\n  /**\n   *\n   * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n   * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n   * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n   * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n   * default interval of 200ms.\n   */\n  constructor(items, config) {\n    /** The index of the currently active (focused) item. */\n    _defineProperty(this, \"_activeItemIndex\", -1);\n    /** The currently active (focused) item. */\n    _defineProperty(this, \"_activeItem\", null);\n    /** Whether or not we activate the item when it's focused. */\n    _defineProperty(this, \"_shouldActivationFollowFocus\", false);\n    /**\n     * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n     * Right arrow are switched.\n     */\n    _defineProperty(this, \"_horizontalOrientation\", 'ltr');\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager.\n     *\n     * The default value for this doesn't skip any elements in order to keep tree items focusable\n     * when disabled. This aligns with ARIA guidelines:\n     * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n     */\n    _defineProperty(this, \"_skipPredicateFn\", _item => false);\n    /** Function to determine equivalent items. */\n    _defineProperty(this, \"_trackByFn\", item => item);\n    /** Synchronous cache of the items to manage. */\n    _defineProperty(this, \"_items\", []);\n    _defineProperty(this, \"_typeahead\", void 0);\n    _defineProperty(this, \"_typeaheadSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_hasInitialFocused\", false);\n    /** Stream that emits any time the focused item changes. */\n    _defineProperty(this, \"change\", new Subject());\n    // We allow for the items to be an array or Observable because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (items instanceof QueryList) {\n      this._items = items.toArray();\n      items.changes.subscribe(newItems => {\n        var _this$_typeahead2;\n        this._items = newItems.toArray();\n        (_this$_typeahead2 = this._typeahead) === null || _this$_typeahead2 === void 0 || _this$_typeahead2.setItems(this._items);\n        this._updateActiveItemIndex(this._items);\n        this._initializeFocus();\n      });\n    } else if (isObservable(items)) {\n      items.subscribe(newItems => {\n        var _this$_typeahead3;\n        this._items = newItems;\n        (_this$_typeahead3 = this._typeahead) === null || _this$_typeahead3 === void 0 || _this$_typeahead3.setItems(newItems);\n        this._updateActiveItemIndex(newItems);\n        this._initializeFocus();\n      });\n    } else {\n      this._items = items;\n      this._initializeFocus();\n    }\n    if (typeof config.shouldActivationFollowFocus === 'boolean') {\n      this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n    }\n    if (config.horizontalOrientation) {\n      this._horizontalOrientation = config.horizontalOrientation;\n    }\n    if (config.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if (config.trackBy) {\n      this._trackByFn = config.trackBy;\n    }\n    if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n      this._setTypeAhead(config.typeAheadDebounceInterval);\n    }\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    var _this$_typeahead4;\n    this._typeaheadSubscription.unsubscribe();\n    (_this$_typeahead4 = this._typeahead) === null || _this$_typeahead4 === void 0 || _this$_typeahead4.destroy();\n    this.change.complete();\n  }\n  /**\n   * Handles a keyboard event on the tree.\n   * @param event Keyboard event that represents the user interaction with the tree.\n   */\n  onKeydown(event) {\n    var _this$_typeahead5, _this$_typeahead6;\n    const key = event.key;\n    switch (key) {\n      case 'Tab':\n        // Return early here, in order to allow Tab to actually tab out of the tree\n        return;\n      case 'ArrowDown':\n        this._focusNextItem();\n        break;\n      case 'ArrowUp':\n        this._focusPreviousItem();\n        break;\n      case 'ArrowRight':\n        this._horizontalOrientation === 'rtl' ? this._collapseCurrentItem() : this._expandCurrentItem();\n        break;\n      case 'ArrowLeft':\n        this._horizontalOrientation === 'rtl' ? this._expandCurrentItem() : this._collapseCurrentItem();\n        break;\n      case 'Home':\n        this._focusFirstItem();\n        break;\n      case 'End':\n        this._focusLastItem();\n        break;\n      case 'Enter':\n      case ' ':\n        this._activateCurrentItem();\n        break;\n      default:\n        if (event.key === '*') {\n          this._expandAllItemsAtCurrentItemLevel();\n          break;\n        }\n        (_this$_typeahead5 = this._typeahead) === null || _this$_typeahead5 === void 0 || _this$_typeahead5.handleKey(event);\n        // Return here, in order to avoid preventing the default action of non-navigational\n        // keys or resetting the buffer of pressed letters.\n        return;\n    }\n    // Reset the typeahead since the user has used a navigational key.\n    (_this$_typeahead6 = this._typeahead) === null || _this$_typeahead6 === void 0 || _this$_typeahead6.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  getActiveItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The currently active item. */\n  getActiveItem() {\n    return this._activeItem;\n  }\n  /** Focus the first available item. */\n  _focusFirstItem() {\n    this.focusItem(this._findNextAvailableItemIndex(-1));\n  }\n  /** Focus the last available item. */\n  _focusLastItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n  }\n  /** Focus the next available item. */\n  _focusNextItem() {\n    this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n  }\n  /** Focus the previous available item. */\n  _focusPreviousItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n  }\n  focusItem(itemOrIndex, options = {}) {\n    var _options$emitChangeEv, _this$_typeahead7, _this$_activeItem2;\n    // Set default options\n    (_options$emitChangeEv = options.emitChangeEvent) !== null && _options$emitChangeEv !== void 0 ? _options$emitChangeEv : options.emitChangeEvent = true;\n    let index = typeof itemOrIndex === 'number' ? itemOrIndex : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n    if (index < 0 || index >= this._items.length) {\n      return;\n    }\n    const activeItem = this._items[index];\n    // If we're just setting the same item, don't re-call activate or focus\n    if (this._activeItem !== null && this._trackByFn(activeItem) === this._trackByFn(this._activeItem)) {\n      return;\n    }\n    const previousActiveItem = this._activeItem;\n    this._activeItem = activeItem !== null && activeItem !== void 0 ? activeItem : null;\n    this._activeItemIndex = index;\n    (_this$_typeahead7 = this._typeahead) === null || _this$_typeahead7 === void 0 || _this$_typeahead7.setCurrentSelectedItemIndex(index);\n    (_this$_activeItem2 = this._activeItem) === null || _this$_activeItem2 === void 0 || _this$_activeItem2.focus();\n    previousActiveItem === null || previousActiveItem === void 0 || previousActiveItem.unfocus();\n    if (options.emitChangeEvent) {\n      this.change.next(this._activeItem);\n    }\n    if (this._shouldActivationFollowFocus) {\n      this._activateCurrentItem();\n    }\n  }\n  _updateActiveItemIndex(newItems) {\n    const activeItem = this._activeItem;\n    if (!activeItem) {\n      return;\n    }\n    const newIndex = newItems.findIndex(item => this._trackByFn(item) === this._trackByFn(activeItem));\n    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n      var _this$_typeahead8;\n      this._activeItemIndex = newIndex;\n      (_this$_typeahead8 = this._typeahead) === null || _this$_typeahead8 === void 0 || _this$_typeahead8.setCurrentSelectedItemIndex(newIndex);\n    }\n  }\n  _setTypeAhead(debounceInterval) {\n    this._typeahead = new Typeahead(this._items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.focusItem(item);\n    });\n  }\n  _findNextAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex + 1; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  _findPreviousAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex - 1; i >= 0; i--) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  /**\n   * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n   */\n  _collapseCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (this._isCurrentItemExpanded()) {\n      this._activeItem.collapse();\n    } else {\n      const parent = this._activeItem.getParent();\n      if (!parent || this._skipPredicateFn(parent)) {\n        return;\n      }\n      this.focusItem(parent);\n    }\n  }\n  /**\n   * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n   */\n  _expandCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (!this._isCurrentItemExpanded()) {\n      this._activeItem.expand();\n    } else {\n      coerceObservable(this._activeItem.getChildren()).pipe(take(1)).subscribe(children => {\n        const firstChild = children.find(child => !this._skipPredicateFn(child));\n        if (!firstChild) {\n          return;\n        }\n        this.focusItem(firstChild);\n      });\n    }\n  }\n  _isCurrentItemExpanded() {\n    if (!this._activeItem) {\n      return false;\n    }\n    return typeof this._activeItem.isExpanded === 'boolean' ? this._activeItem.isExpanded : this._activeItem.isExpanded();\n  }\n  _isItemDisabled(item) {\n    var _item$isDisabled;\n    return typeof item.isDisabled === 'boolean' ? item.isDisabled : (_item$isDisabled = item.isDisabled) === null || _item$isDisabled === void 0 ? void 0 : _item$isDisabled.call(item);\n  }\n  /** For all items that are the same level as the current item, we expand those items. */\n  _expandAllItemsAtCurrentItemLevel() {\n    if (!this._activeItem) {\n      return;\n    }\n    const parent = this._activeItem.getParent();\n    let itemsToExpand;\n    if (!parent) {\n      itemsToExpand = of(this._items.filter(item => item.getParent() === null));\n    } else {\n      itemsToExpand = coerceObservable(parent.getChildren());\n    }\n    itemsToExpand.pipe(take(1)).subscribe(items => {\n      for (const item of items) {\n        item.expand();\n      }\n    });\n  }\n  _activateCurrentItem() {\n    var _this$_activeItem3;\n    (_this$_activeItem3 = this._activeItem) === null || _this$_activeItem3 === void 0 || _this$_activeItem3.activate();\n  }\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction TREE_KEY_MANAGER_FACTORY() {\n  return (items, options) => new TreeKeyManager(items, options);\n}\n/** Injection token that determines the key manager to use. */\nconst TREE_KEY_MANAGER = new InjectionToken('tree-key-manager', {\n  providedIn: 'root',\n  factory: TREE_KEY_MANAGER_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: TREE_KEY_MANAGER_FACTORY\n};\nexport { TREE_KEY_MANAGER as T, TreeKeyManager as a, TREE_KEY_MANAGER_FACTORY as b, TREE_KEY_MANAGER_FACTORY_PROVIDER as c };\n//# sourceMappingURL=tree-key-manager-KnCoIkIC.mjs.map", "map": {"version": 3, "names": ["QueryList", "InjectionToken", "Subscription", "isObservable", "Subject", "of", "take", "T", "Typeahead", "coerceObservable", "TreeKeyManager", "_initializeFocus", "_hasInitialFocused", "_items", "length", "activeIndex", "i", "_skipPredicateFn", "_isItemDisabled", "activeItem", "makeFocusable", "_this$_activeItem", "_this$_typeahead", "_activeItem", "unfocus", "_activeItemIndex", "_typeahead", "setCurrentSelectedItemIndex", "focusItem", "constructor", "items", "config", "_defineProperty", "_item", "item", "EMPTY", "toArray", "changes", "subscribe", "newItems", "_this$_typeahead2", "setItems", "_updateActiveItemIndex", "_this$_typeahead3", "shouldActivationFollowFocus", "_shouldActivationFollowFocus", "horizontalOrientation", "_horizontalOrientation", "skipPredicate", "trackBy", "_trackByFn", "typeAheadDebounceInterval", "_setTypeAhead", "destroy", "_this$_typeahead4", "_typeaheadSubscription", "unsubscribe", "change", "complete", "onKeydown", "event", "_this$_typeahead5", "_this$_typeahead6", "key", "_focusNextItem", "_focusPreviousItem", "_collapseCurrentItem", "_expandCurrentItem", "_focusFirstItem", "_focusLastItem", "_activateCurrentItem", "_expandAllItemsAtCurrentItemLevel", "handle<PERSON>ey", "reset", "preventDefault", "getActiveItemIndex", "getActiveItem", "_findNextAvailableItemIndex", "_findPreviousAvailableItemIndex", "itemOrIndex", "options", "_options$emitChangeEv", "_this$_typeahead7", "_this$_activeItem2", "emitChangeEvent", "index", "findIndex", "previousActiveItem", "focus", "next", "newIndex", "_this$_typeahead8", "debounceInterval", "undefined", "selectedItem", "startingIndex", "_isCurrentItemExpanded", "collapse", "parent", "getParent", "expand", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pipe", "children", "<PERSON><PERSON><PERSON><PERSON>", "find", "child", "isExpanded", "_item$isDisabled", "isDisabled", "call", "itemsToExpand", "filter", "_this$_activeItem3", "activate", "TREE_KEY_MANAGER_FACTORY", "TREE_KEY_MANAGER", "providedIn", "factory", "TREE_KEY_MANAGER_FACTORY_PROVIDER", "provide", "useFactory", "a", "b", "c"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/tree-key-manager-KnCoIkIC.mjs"], "sourcesContent": ["import { QueryList, InjectionToken } from '@angular/core';\nimport { Subscription, isObservable, Subject, of } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { T as Typeahead } from './typeahead-9ZW4Dtsf.mjs';\nimport { coerceObservable } from './coercion/private.mjs';\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nclass TreeKeyManager {\n    /** The index of the currently active (focused) item. */\n    _activeItemIndex = -1;\n    /** The currently active (focused) item. */\n    _activeItem = null;\n    /** Whether or not we activate the item when it's focused. */\n    _shouldActivationFollowFocus = false;\n    /**\n     * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n     * Right arrow are switched.\n     */\n    _horizontalOrientation = 'ltr';\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager.\n     *\n     * The default value for this doesn't skip any elements in order to keep tree items focusable\n     * when disabled. This aligns with ARIA guidelines:\n     * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n     */\n    _skipPredicateFn = (_item) => false;\n    /** Function to determine equivalent items. */\n    _trackByFn = (item) => item;\n    /** Synchronous cache of the items to manage. */\n    _items = [];\n    _typeahead;\n    _typeaheadSubscription = Subscription.EMPTY;\n    _hasInitialFocused = false;\n    _initializeFocus() {\n        if (this._hasInitialFocused || this._items.length === 0) {\n            return;\n        }\n        let activeIndex = 0;\n        for (let i = 0; i < this._items.length; i++) {\n            if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n                activeIndex = i;\n                break;\n            }\n        }\n        const activeItem = this._items[activeIndex];\n        // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n        // capture the focus since the user isn't interacting with it. See #29628.\n        if (activeItem.makeFocusable) {\n            this._activeItem?.unfocus();\n            this._activeItemIndex = activeIndex;\n            this._activeItem = activeItem;\n            this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n            activeItem.makeFocusable();\n        }\n        else {\n            // Backwards compatibility for items that don't implement `makeFocusable`.\n            this.focusItem(activeIndex);\n        }\n        this._hasInitialFocused = true;\n    }\n    /**\n     *\n     * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n     * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n     * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n     * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n     * default interval of 200ms.\n     */\n    constructor(items, config) {\n        // We allow for the items to be an array or Observable because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (items instanceof QueryList) {\n            this._items = items.toArray();\n            items.changes.subscribe((newItems) => {\n                this._items = newItems.toArray();\n                this._typeahead?.setItems(this._items);\n                this._updateActiveItemIndex(this._items);\n                this._initializeFocus();\n            });\n        }\n        else if (isObservable(items)) {\n            items.subscribe(newItems => {\n                this._items = newItems;\n                this._typeahead?.setItems(newItems);\n                this._updateActiveItemIndex(newItems);\n                this._initializeFocus();\n            });\n        }\n        else {\n            this._items = items;\n            this._initializeFocus();\n        }\n        if (typeof config.shouldActivationFollowFocus === 'boolean') {\n            this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n        }\n        if (config.horizontalOrientation) {\n            this._horizontalOrientation = config.horizontalOrientation;\n        }\n        if (config.skipPredicate) {\n            this._skipPredicateFn = config.skipPredicate;\n        }\n        if (config.trackBy) {\n            this._trackByFn = config.trackBy;\n        }\n        if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n            this._setTypeAhead(config.typeAheadDebounceInterval);\n        }\n    }\n    /** Stream that emits any time the focused item changes. */\n    change = new Subject();\n    /** Cleans up the key manager. */\n    destroy() {\n        this._typeaheadSubscription.unsubscribe();\n        this._typeahead?.destroy();\n        this.change.complete();\n    }\n    /**\n     * Handles a keyboard event on the tree.\n     * @param event Keyboard event that represents the user interaction with the tree.\n     */\n    onKeydown(event) {\n        const key = event.key;\n        switch (key) {\n            case 'Tab':\n                // Return early here, in order to allow Tab to actually tab out of the tree\n                return;\n            case 'ArrowDown':\n                this._focusNextItem();\n                break;\n            case 'ArrowUp':\n                this._focusPreviousItem();\n                break;\n            case 'ArrowRight':\n                this._horizontalOrientation === 'rtl'\n                    ? this._collapseCurrentItem()\n                    : this._expandCurrentItem();\n                break;\n            case 'ArrowLeft':\n                this._horizontalOrientation === 'rtl'\n                    ? this._expandCurrentItem()\n                    : this._collapseCurrentItem();\n                break;\n            case 'Home':\n                this._focusFirstItem();\n                break;\n            case 'End':\n                this._focusLastItem();\n                break;\n            case 'Enter':\n            case ' ':\n                this._activateCurrentItem();\n                break;\n            default:\n                if (event.key === '*') {\n                    this._expandAllItemsAtCurrentItemLevel();\n                    break;\n                }\n                this._typeahead?.handleKey(event);\n                // Return here, in order to avoid preventing the default action of non-navigational\n                // keys or resetting the buffer of pressed letters.\n                return;\n        }\n        // Reset the typeahead since the user has used a navigational key.\n        this._typeahead?.reset();\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    getActiveItemIndex() {\n        return this._activeItemIndex;\n    }\n    /** The currently active item. */\n    getActiveItem() {\n        return this._activeItem;\n    }\n    /** Focus the first available item. */\n    _focusFirstItem() {\n        this.focusItem(this._findNextAvailableItemIndex(-1));\n    }\n    /** Focus the last available item. */\n    _focusLastItem() {\n        this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n    }\n    /** Focus the next available item. */\n    _focusNextItem() {\n        this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n    }\n    /** Focus the previous available item. */\n    _focusPreviousItem() {\n        this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n    }\n    focusItem(itemOrIndex, options = {}) {\n        // Set default options\n        options.emitChangeEvent ??= true;\n        let index = typeof itemOrIndex === 'number'\n            ? itemOrIndex\n            : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n        if (index < 0 || index >= this._items.length) {\n            return;\n        }\n        const activeItem = this._items[index];\n        // If we're just setting the same item, don't re-call activate or focus\n        if (this._activeItem !== null &&\n            this._trackByFn(activeItem) === this._trackByFn(this._activeItem)) {\n            return;\n        }\n        const previousActiveItem = this._activeItem;\n        this._activeItem = activeItem ?? null;\n        this._activeItemIndex = index;\n        this._typeahead?.setCurrentSelectedItemIndex(index);\n        this._activeItem?.focus();\n        previousActiveItem?.unfocus();\n        if (options.emitChangeEvent) {\n            this.change.next(this._activeItem);\n        }\n        if (this._shouldActivationFollowFocus) {\n            this._activateCurrentItem();\n        }\n    }\n    _updateActiveItemIndex(newItems) {\n        const activeItem = this._activeItem;\n        if (!activeItem) {\n            return;\n        }\n        const newIndex = newItems.findIndex(item => this._trackByFn(item) === this._trackByFn(activeItem));\n        if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n            this._activeItemIndex = newIndex;\n            this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n        }\n    }\n    _setTypeAhead(debounceInterval) {\n        this._typeahead = new Typeahead(this._items, {\n            debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n            skipPredicate: item => this._skipPredicateFn(item),\n        });\n        this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n            this.focusItem(item);\n        });\n    }\n    _findNextAvailableItemIndex(startingIndex) {\n        for (let i = startingIndex + 1; i < this._items.length; i++) {\n            if (!this._skipPredicateFn(this._items[i])) {\n                return i;\n            }\n        }\n        return startingIndex;\n    }\n    _findPreviousAvailableItemIndex(startingIndex) {\n        for (let i = startingIndex - 1; i >= 0; i--) {\n            if (!this._skipPredicateFn(this._items[i])) {\n                return i;\n            }\n        }\n        return startingIndex;\n    }\n    /**\n     * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n     */\n    _collapseCurrentItem() {\n        if (!this._activeItem) {\n            return;\n        }\n        if (this._isCurrentItemExpanded()) {\n            this._activeItem.collapse();\n        }\n        else {\n            const parent = this._activeItem.getParent();\n            if (!parent || this._skipPredicateFn(parent)) {\n                return;\n            }\n            this.focusItem(parent);\n        }\n    }\n    /**\n     * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n     */\n    _expandCurrentItem() {\n        if (!this._activeItem) {\n            return;\n        }\n        if (!this._isCurrentItemExpanded()) {\n            this._activeItem.expand();\n        }\n        else {\n            coerceObservable(this._activeItem.getChildren())\n                .pipe(take(1))\n                .subscribe(children => {\n                const firstChild = children.find(child => !this._skipPredicateFn(child));\n                if (!firstChild) {\n                    return;\n                }\n                this.focusItem(firstChild);\n            });\n        }\n    }\n    _isCurrentItemExpanded() {\n        if (!this._activeItem) {\n            return false;\n        }\n        return typeof this._activeItem.isExpanded === 'boolean'\n            ? this._activeItem.isExpanded\n            : this._activeItem.isExpanded();\n    }\n    _isItemDisabled(item) {\n        return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n    }\n    /** For all items that are the same level as the current item, we expand those items. */\n    _expandAllItemsAtCurrentItemLevel() {\n        if (!this._activeItem) {\n            return;\n        }\n        const parent = this._activeItem.getParent();\n        let itemsToExpand;\n        if (!parent) {\n            itemsToExpand = of(this._items.filter(item => item.getParent() === null));\n        }\n        else {\n            itemsToExpand = coerceObservable(parent.getChildren());\n        }\n        itemsToExpand.pipe(take(1)).subscribe(items => {\n            for (const item of items) {\n                item.expand();\n            }\n        });\n    }\n    _activateCurrentItem() {\n        this._activeItem?.activate();\n    }\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction TREE_KEY_MANAGER_FACTORY() {\n    return (items, options) => new TreeKeyManager(items, options);\n}\n/** Injection token that determines the key manager to use. */\nconst TREE_KEY_MANAGER = new InjectionToken('tree-key-manager', {\n    providedIn: 'root',\n    factory: TREE_KEY_MANAGER_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n    provide: TREE_KEY_MANAGER,\n    useFactory: TREE_KEY_MANAGER_FACTORY,\n};\n\nexport { TREE_KEY_MANAGER as T, TreeKeyManager as a, TREE_KEY_MANAGER_FACTORY as b, TREE_KEY_MANAGER_FACTORY_PROVIDER as c };\n//# sourceMappingURL=tree-key-manager-KnCoIkIC.mjs.map\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,cAAc,QAAQ,eAAe;AACzD,SAASC,YAAY,EAAEC,YAAY,EAAEC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AAC9D,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,CAAC,IAAIC,SAAS,QAAQ,0BAA0B;AACzD,SAASC,gBAAgB,QAAQ,wBAAwB;;AAEzD;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EA4BjBC,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACC,kBAAkB,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MACrD;IACJ;IACA,IAAIC,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,MAAM,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;MACzC,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACJ,MAAM,CAACG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACE,eAAe,CAAC,IAAI,CAACL,MAAM,CAACG,CAAC,CAAC,CAAC,EAAE;QACjFD,WAAW,GAAGC,CAAC;QACf;MACJ;IACJ;IACA,MAAMG,UAAU,GAAG,IAAI,CAACN,MAAM,CAACE,WAAW,CAAC;IAC3C;IACA;IACA,IAAII,UAAU,CAACC,aAAa,EAAE;MAAA,IAAAC,iBAAA,EAAAC,gBAAA;MAC1B,CAAAD,iBAAA,OAAI,CAACE,WAAW,cAAAF,iBAAA,eAAhBA,iBAAA,CAAkBG,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACC,gBAAgB,GAAGV,WAAW;MACnC,IAAI,CAACQ,WAAW,GAAGJ,UAAU;MAC7B,CAAAG,gBAAA,OAAI,CAACI,UAAU,cAAAJ,gBAAA,eAAfA,gBAAA,CAAiBK,2BAA2B,CAACZ,WAAW,CAAC;MACzDI,UAAU,CAACC,aAAa,CAAC,CAAC;IAC9B,CAAC,MACI;MACD;MACA,IAAI,CAACQ,SAAS,CAACb,WAAW,CAAC;IAC/B;IACA,IAAI,CAACH,kBAAkB,GAAG,IAAI;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIiB,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;IA9D3B;IAAAC,eAAA,2BACmB,CAAC,CAAC;IACrB;IAAAA,eAAA,sBACc,IAAI;IAClB;IAAAA,eAAA,uCAC+B,KAAK;IACpC;AACJ;AACA;AACA;IAHIA,eAAA,iCAIyB,KAAK;IAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IAPIA,eAAA,2BAQoBC,KAAK,IAAK,KAAK;IACnC;IAAAD,eAAA,qBACcE,IAAI,IAAKA,IAAI;IAC3B;IAAAF,eAAA,iBACS,EAAE;IAAAA,eAAA;IAAAA,eAAA,iCAEc9B,YAAY,CAACiC,KAAK;IAAAH,eAAA,6BACtB,KAAK;IA6E1B;IAAAA,eAAA,iBACS,IAAI5B,OAAO,CAAC,CAAC;IAzClB;IACA;IACA;IACA,IAAI0B,KAAK,YAAY9B,SAAS,EAAE;MAC5B,IAAI,CAACa,MAAM,GAAGiB,KAAK,CAACM,OAAO,CAAC,CAAC;MAC7BN,KAAK,CAACO,OAAO,CAACC,SAAS,CAAEC,QAAQ,IAAK;QAAA,IAAAC,iBAAA;QAClC,IAAI,CAAC3B,MAAM,GAAG0B,QAAQ,CAACH,OAAO,CAAC,CAAC;QAChC,CAAAI,iBAAA,OAAI,CAACd,UAAU,cAAAc,iBAAA,eAAfA,iBAAA,CAAiBC,QAAQ,CAAC,IAAI,CAAC5B,MAAM,CAAC;QACtC,IAAI,CAAC6B,sBAAsB,CAAC,IAAI,CAAC7B,MAAM,CAAC;QACxC,IAAI,CAACF,gBAAgB,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,MACI,IAAIR,YAAY,CAAC2B,KAAK,CAAC,EAAE;MAC1BA,KAAK,CAACQ,SAAS,CAACC,QAAQ,IAAI;QAAA,IAAAI,iBAAA;QACxB,IAAI,CAAC9B,MAAM,GAAG0B,QAAQ;QACtB,CAAAI,iBAAA,OAAI,CAACjB,UAAU,cAAAiB,iBAAA,eAAfA,iBAAA,CAAiBF,QAAQ,CAACF,QAAQ,CAAC;QACnC,IAAI,CAACG,sBAAsB,CAACH,QAAQ,CAAC;QACrC,IAAI,CAAC5B,gBAAgB,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACE,MAAM,GAAGiB,KAAK;MACnB,IAAI,CAACnB,gBAAgB,CAAC,CAAC;IAC3B;IACA,IAAI,OAAOoB,MAAM,CAACa,2BAA2B,KAAK,SAAS,EAAE;MACzD,IAAI,CAACC,4BAA4B,GAAGd,MAAM,CAACa,2BAA2B;IAC1E;IACA,IAAIb,MAAM,CAACe,qBAAqB,EAAE;MAC9B,IAAI,CAACC,sBAAsB,GAAGhB,MAAM,CAACe,qBAAqB;IAC9D;IACA,IAAIf,MAAM,CAACiB,aAAa,EAAE;MACtB,IAAI,CAAC/B,gBAAgB,GAAGc,MAAM,CAACiB,aAAa;IAChD;IACA,IAAIjB,MAAM,CAACkB,OAAO,EAAE;MAChB,IAAI,CAACC,UAAU,GAAGnB,MAAM,CAACkB,OAAO;IACpC;IACA,IAAI,OAAOlB,MAAM,CAACoB,yBAAyB,KAAK,WAAW,EAAE;MACzD,IAAI,CAACC,aAAa,CAACrB,MAAM,CAACoB,yBAAyB,CAAC;IACxD;EACJ;EAGA;EACAE,OAAOA,CAAA,EAAG;IAAA,IAAAC,iBAAA;IACN,IAAI,CAACC,sBAAsB,CAACC,WAAW,CAAC,CAAC;IACzC,CAAAF,iBAAA,OAAI,CAAC5B,UAAU,cAAA4B,iBAAA,eAAfA,iBAAA,CAAiBD,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACI,MAAM,CAACC,QAAQ,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIC,SAASA,CAACC,KAAK,EAAE;IAAA,IAAAC,iBAAA,EAAAC,iBAAA;IACb,MAAMC,GAAG,GAAGH,KAAK,CAACG,GAAG;IACrB,QAAQA,GAAG;MACP,KAAK,KAAK;QACN;QACA;MACJ,KAAK,WAAW;QACZ,IAAI,CAACC,cAAc,CAAC,CAAC;QACrB;MACJ,KAAK,SAAS;QACV,IAAI,CAACC,kBAAkB,CAAC,CAAC;QACzB;MACJ,KAAK,YAAY;QACb,IAAI,CAAClB,sBAAsB,KAAK,KAAK,GAC/B,IAAI,CAACmB,oBAAoB,CAAC,CAAC,GAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;QAC/B;MACJ,KAAK,WAAW;QACZ,IAAI,CAACpB,sBAAsB,KAAK,KAAK,GAC/B,IAAI,CAACoB,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACD,oBAAoB,CAAC,CAAC;QACjC;MACJ,KAAK,MAAM;QACP,IAAI,CAACE,eAAe,CAAC,CAAC;QACtB;MACJ,KAAK,KAAK;QACN,IAAI,CAACC,cAAc,CAAC,CAAC;QACrB;MACJ,KAAK,OAAO;MACZ,KAAK,GAAG;QACJ,IAAI,CAACC,oBAAoB,CAAC,CAAC;QAC3B;MACJ;QACI,IAAIV,KAAK,CAACG,GAAG,KAAK,GAAG,EAAE;UACnB,IAAI,CAACQ,iCAAiC,CAAC,CAAC;UACxC;QACJ;QACA,CAAAV,iBAAA,OAAI,CAACnC,UAAU,cAAAmC,iBAAA,eAAfA,iBAAA,CAAiBW,SAAS,CAACZ,KAAK,CAAC;QACjC;QACA;QACA;IACR;IACA;IACA,CAAAE,iBAAA,OAAI,CAACpC,UAAU,cAAAoC,iBAAA,eAAfA,iBAAA,CAAiBW,KAAK,CAAC,CAAC;IACxBb,KAAK,CAACc,cAAc,CAAC,CAAC;EAC1B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAClD,gBAAgB;EAChC;EACA;EACAmD,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrD,WAAW;EAC3B;EACA;EACA6C,eAAeA,CAAA,EAAG;IACd,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACiD,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD;EACA;EACAR,cAAcA,CAAA,EAAG;IACb,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACkD,+BAA+B,CAAC,IAAI,CAACjE,MAAM,CAACC,MAAM,CAAC,CAAC;EAC5E;EACA;EACAkD,cAAcA,CAAA,EAAG;IACb,IAAI,CAACpC,SAAS,CAAC,IAAI,CAACiD,2BAA2B,CAAC,IAAI,CAACpD,gBAAgB,CAAC,CAAC;EAC3E;EACA;EACAwC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACrC,SAAS,CAAC,IAAI,CAACkD,+BAA+B,CAAC,IAAI,CAACrD,gBAAgB,CAAC,CAAC;EAC/E;EACAG,SAASA,CAACmD,WAAW,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAAA,IAAAC,qBAAA,EAAAC,iBAAA,EAAAC,kBAAA;IACjC;IACA,CAAAF,qBAAA,GAAAD,OAAO,CAACI,eAAe,cAAAH,qBAAA,cAAAA,qBAAA,GAAvBD,OAAO,CAACI,eAAe,GAAK,IAAI;IAChC,IAAIC,KAAK,GAAG,OAAON,WAAW,KAAK,QAAQ,GACrCA,WAAW,GACX,IAAI,CAAClE,MAAM,CAACyE,SAAS,CAACpD,IAAI,IAAI,IAAI,CAACgB,UAAU,CAAChB,IAAI,CAAC,KAAK,IAAI,CAACgB,UAAU,CAAC6B,WAAW,CAAC,CAAC;IAC3F,IAAIM,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACxE,MAAM,CAACC,MAAM,EAAE;MAC1C;IACJ;IACA,MAAMK,UAAU,GAAG,IAAI,CAACN,MAAM,CAACwE,KAAK,CAAC;IACrC;IACA,IAAI,IAAI,CAAC9D,WAAW,KAAK,IAAI,IACzB,IAAI,CAAC2B,UAAU,CAAC/B,UAAU,CAAC,KAAK,IAAI,CAAC+B,UAAU,CAAC,IAAI,CAAC3B,WAAW,CAAC,EAAE;MACnE;IACJ;IACA,MAAMgE,kBAAkB,GAAG,IAAI,CAAChE,WAAW;IAC3C,IAAI,CAACA,WAAW,GAAGJ,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAI,IAAI;IACrC,IAAI,CAACM,gBAAgB,GAAG4D,KAAK;IAC7B,CAAAH,iBAAA,OAAI,CAACxD,UAAU,cAAAwD,iBAAA,eAAfA,iBAAA,CAAiBvD,2BAA2B,CAAC0D,KAAK,CAAC;IACnD,CAAAF,kBAAA,OAAI,CAAC5D,WAAW,cAAA4D,kBAAA,eAAhBA,kBAAA,CAAkBK,KAAK,CAAC,CAAC;IACzBD,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAE/D,OAAO,CAAC,CAAC;IAC7B,IAAIwD,OAAO,CAACI,eAAe,EAAE;MACzB,IAAI,CAAC3B,MAAM,CAACgC,IAAI,CAAC,IAAI,CAAClE,WAAW,CAAC;IACtC;IACA,IAAI,IAAI,CAACsB,4BAA4B,EAAE;MACnC,IAAI,CAACyB,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA5B,sBAAsBA,CAACH,QAAQ,EAAE;IAC7B,MAAMpB,UAAU,GAAG,IAAI,CAACI,WAAW;IACnC,IAAI,CAACJ,UAAU,EAAE;MACb;IACJ;IACA,MAAMuE,QAAQ,GAAGnD,QAAQ,CAAC+C,SAAS,CAACpD,IAAI,IAAI,IAAI,CAACgB,UAAU,CAAChB,IAAI,CAAC,KAAK,IAAI,CAACgB,UAAU,CAAC/B,UAAU,CAAC,CAAC;IAClG,IAAIuE,QAAQ,GAAG,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,CAACjE,gBAAgB,EAAE;MAAA,IAAAkE,iBAAA;MACrD,IAAI,CAAClE,gBAAgB,GAAGiE,QAAQ;MAChC,CAAAC,iBAAA,OAAI,CAACjE,UAAU,cAAAiE,iBAAA,eAAfA,iBAAA,CAAiBhE,2BAA2B,CAAC+D,QAAQ,CAAC;IAC1D;EACJ;EACAtC,aAAaA,CAACwC,gBAAgB,EAAE;IAC5B,IAAI,CAAClE,UAAU,GAAG,IAAIlB,SAAS,CAAC,IAAI,CAACK,MAAM,EAAE;MACzC+E,gBAAgB,EAAE,OAAOA,gBAAgB,KAAK,QAAQ,GAAGA,gBAAgB,GAAGC,SAAS;MACrF7C,aAAa,EAAEd,IAAI,IAAI,IAAI,CAACjB,gBAAgB,CAACiB,IAAI;IACrD,CAAC,CAAC;IACF,IAAI,CAACqB,sBAAsB,GAAG,IAAI,CAAC7B,UAAU,CAACoE,YAAY,CAACxD,SAAS,CAACJ,IAAI,IAAI;MACzE,IAAI,CAACN,SAAS,CAACM,IAAI,CAAC;IACxB,CAAC,CAAC;EACN;EACA2C,2BAA2BA,CAACkB,aAAa,EAAE;IACvC,KAAK,IAAI/E,CAAC,GAAG+E,aAAa,GAAG,CAAC,EAAE/E,CAAC,GAAG,IAAI,CAACH,MAAM,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;MACzD,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACJ,MAAM,CAACG,CAAC,CAAC,CAAC,EAAE;QACxC,OAAOA,CAAC;MACZ;IACJ;IACA,OAAO+E,aAAa;EACxB;EACAjB,+BAA+BA,CAACiB,aAAa,EAAE;IAC3C,KAAK,IAAI/E,CAAC,GAAG+E,aAAa,GAAG,CAAC,EAAE/E,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACJ,MAAM,CAACG,CAAC,CAAC,CAAC,EAAE;QACxC,OAAOA,CAAC;MACZ;IACJ;IACA,OAAO+E,aAAa;EACxB;EACA;AACJ;AACA;EACI7B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAAC3C,WAAW,EAAE;MACnB;IACJ;IACA,IAAI,IAAI,CAACyE,sBAAsB,CAAC,CAAC,EAAE;MAC/B,IAAI,CAACzE,WAAW,CAAC0E,QAAQ,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,MAAMC,MAAM,GAAG,IAAI,CAAC3E,WAAW,CAAC4E,SAAS,CAAC,CAAC;MAC3C,IAAI,CAACD,MAAM,IAAI,IAAI,CAACjF,gBAAgB,CAACiF,MAAM,CAAC,EAAE;QAC1C;MACJ;MACA,IAAI,CAACtE,SAAS,CAACsE,MAAM,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;EACI/B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC5C,WAAW,EAAE;MACnB;IACJ;IACA,IAAI,CAAC,IAAI,CAACyE,sBAAsB,CAAC,CAAC,EAAE;MAChC,IAAI,CAACzE,WAAW,CAAC6E,MAAM,CAAC,CAAC;IAC7B,CAAC,MACI;MACD3F,gBAAgB,CAAC,IAAI,CAACc,WAAW,CAAC8E,WAAW,CAAC,CAAC,CAAC,CAC3CC,IAAI,CAAChG,IAAI,CAAC,CAAC,CAAC,CAAC,CACbgC,SAAS,CAACiE,QAAQ,IAAI;QACvB,MAAMC,UAAU,GAAGD,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,CAAC,IAAI,CAACzF,gBAAgB,CAACyF,KAAK,CAAC,CAAC;QACxE,IAAI,CAACF,UAAU,EAAE;UACb;QACJ;QACA,IAAI,CAAC5E,SAAS,CAAC4E,UAAU,CAAC;MAC9B,CAAC,CAAC;IACN;EACJ;EACAR,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAACzE,WAAW,EAAE;MACnB,OAAO,KAAK;IAChB;IACA,OAAO,OAAO,IAAI,CAACA,WAAW,CAACoF,UAAU,KAAK,SAAS,GACjD,IAAI,CAACpF,WAAW,CAACoF,UAAU,GAC3B,IAAI,CAACpF,WAAW,CAACoF,UAAU,CAAC,CAAC;EACvC;EACAzF,eAAeA,CAACgB,IAAI,EAAE;IAAA,IAAA0E,gBAAA;IAClB,OAAO,OAAO1E,IAAI,CAAC2E,UAAU,KAAK,SAAS,GAAG3E,IAAI,CAAC2E,UAAU,IAAAD,gBAAA,GAAG1E,IAAI,CAAC2E,UAAU,cAAAD,gBAAA,uBAAfA,gBAAA,CAAAE,IAAA,CAAA5E,IAAkB,CAAC;EACvF;EACA;EACAqC,iCAAiCA,CAAA,EAAG;IAChC,IAAI,CAAC,IAAI,CAAChD,WAAW,EAAE;MACnB;IACJ;IACA,MAAM2E,MAAM,GAAG,IAAI,CAAC3E,WAAW,CAAC4E,SAAS,CAAC,CAAC;IAC3C,IAAIY,aAAa;IACjB,IAAI,CAACb,MAAM,EAAE;MACTa,aAAa,GAAG1G,EAAE,CAAC,IAAI,CAACQ,MAAM,CAACmG,MAAM,CAAC9E,IAAI,IAAIA,IAAI,CAACiE,SAAS,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;IAC7E,CAAC,MACI;MACDY,aAAa,GAAGtG,gBAAgB,CAACyF,MAAM,CAACG,WAAW,CAAC,CAAC,CAAC;IAC1D;IACAU,aAAa,CAACT,IAAI,CAAChG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgC,SAAS,CAACR,KAAK,IAAI;MAC3C,KAAK,MAAMI,IAAI,IAAIJ,KAAK,EAAE;QACtBI,IAAI,CAACkE,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA9B,oBAAoBA,CAAA,EAAG;IAAA,IAAA2C,kBAAA;IACnB,CAAAA,kBAAA,OAAI,CAAC1F,WAAW,cAAA0F,kBAAA,eAAhBA,kBAAA,CAAkBC,QAAQ,CAAC,CAAC;EAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAAA,EAAG;EAChC,OAAO,CAACrF,KAAK,EAAEkD,OAAO,KAAK,IAAItE,cAAc,CAACoB,KAAK,EAAEkD,OAAO,CAAC;AACjE;AACA;AACA,MAAMoC,gBAAgB,GAAG,IAAInH,cAAc,CAAC,kBAAkB,EAAE;EAC5DoH,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMI,iCAAiC,GAAG;EACtCC,OAAO,EAAEJ,gBAAgB;EACzBK,UAAU,EAAEN;AAChB,CAAC;AAED,SAASC,gBAAgB,IAAI7G,CAAC,EAAEG,cAAc,IAAIgH,CAAC,EAAEP,wBAAwB,IAAIQ,CAAC,EAAEJ,iCAAiC,IAAIK,CAAC;AAC1H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}