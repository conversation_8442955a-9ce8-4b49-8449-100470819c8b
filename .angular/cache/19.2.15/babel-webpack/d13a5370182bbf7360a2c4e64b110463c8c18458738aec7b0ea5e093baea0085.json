{"ast": null, "code": "var _SwuiTdGameLabelsWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./game-labels.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdGameLabelsWidgetComponent = (_SwuiTdGameLabelsWidgetComponent = class SwuiTdGameLabelsWidgetComponent {\n  constructor({\n    schema,\n    row\n  }) {\n    this.items = [];\n    const {\n      classFn\n    } = schema;\n    this.classObj = classFn && classFn(row, schema);\n    if (schema.td && 'group' in schema.td) {\n      this.items = row.labels.filter(({\n        group\n      }) => {\n        var _schema$td;\n        return group === ((_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.group);\n      });\n    }\n  }\n}, _SwuiTdGameLabelsWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdGameLabelsWidgetComponent);\nSwuiTdGameLabelsWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-game-labels-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdGameLabelsWidgetComponent);\nexport { SwuiTdGameLabelsWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdGameLabelsWidgetComponent", "_SwuiTdGameLabelsWidgetComponent", "constructor", "schema", "row", "items", "classFn", "classObj", "td", "labels", "filter", "group", "_schema$td", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/game-labels/game-labels.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./game-labels.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdGameLabelsWidgetComponent = class SwuiTdGameLabelsWidgetComponent {\n    constructor({ schema, row }) {\n        this.items = [];\n        const { classFn } = schema;\n        this.classObj = classFn && classFn(row, schema);\n        if (schema.td && 'group' in schema.td) {\n            this.items = row.labels.filter(({ group }) => group === schema.td?.group);\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdGameLabelsWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-game-labels-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdGameLabelsWidgetComponent);\nexport { SwuiTdGameLabelsWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,+BAA+B,IAAAC,gCAAA,GAAG,MAAMD,+BAA+B,CAAC;EACxEE,WAAWA,CAAC;IAAEC,MAAM;IAAEC;EAAI,CAAC,EAAE;IACzB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,MAAM;MAAEC;IAAQ,CAAC,GAAGH,MAAM;IAC1B,IAAI,CAACI,QAAQ,GAAGD,OAAO,IAAIA,OAAO,CAACF,GAAG,EAAED,MAAM,CAAC;IAC/C,IAAIA,MAAM,CAACK,EAAE,IAAI,OAAO,IAAIL,MAAM,CAACK,EAAE,EAAE;MACnC,IAAI,CAACH,KAAK,GAAGD,GAAG,CAACK,MAAM,CAACC,MAAM,CAAC,CAAC;QAAEC;MAAM,CAAC;QAAA,IAAAC,UAAA;QAAA,OAAKD,KAAK,OAAAC,UAAA,GAAKT,MAAM,CAACK,EAAE,cAAAI,UAAA,uBAATA,UAAA,CAAWD,KAAK;MAAA,EAAC;IAC7E;EACJ;AAIJ,CAAC,EAHYV,gCAAA,CAAKY,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEhB,MAAM;IAAEmB,IAAI,EAAE,CAAClB,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,gCAAA,CACJ;AACDD,+BAA+B,GAAGL,UAAU,CAAC,CACzCE,SAAS,CAAC;EACNqB,QAAQ,EAAE,gCAAgC;EAC1CC,QAAQ,EAAEvB,oBAAoB;EAC9BwB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEpB,+BAA+B,CAAC;AACnC,SAASA,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}