{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatPseudoCheckboxModule, MatRippleModule } from '@angular/material/core';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSelectModule } from '@angular/material/select';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiSelectComponent } from './swui-select.component';\nexport const SELECT_MODULES = [FormsModule, ReactiveFormsModule, MatSelectModule, MatInputModule, MatIconModule, MatCheckboxModule, MatRippleModule, ScrollingModule, MatMenuModule];\nlet SwuiSelectModule = class SwuiSelectModule {};\nSwuiSelectModule = __decorate([NgModule({\n  declarations: [SwuiSelectComponent],\n  imports: [CommonModule, TranslateModule.forChild(), ...SELECT_MODULES, MatMenuModule, MatPseudoCheckboxModule],\n  exports: [SwuiSelectComponent]\n})], SwuiSelectModule);\nexport { SwuiSelectModule };", "map": {"version": 3, "names": ["__decorate", "ScrollingModule", "CommonModule", "NgModule", "FormsModule", "ReactiveFormsModule", "MatCheckboxModule", "MatPseudoCheckboxModule", "MatRippleModule", "MatIconModule", "MatInputModule", "MatMenuModule", "MatSelectModule", "TranslateModule", "SwuiSelectComponent", "SELECT_MODULES", "SwuiSelectModule", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-select/swui-select.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatPseudoCheckboxModule, MatRippleModule } from '@angular/material/core';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSelectModule } from '@angular/material/select';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiSelectComponent } from './swui-select.component';\nexport const SELECT_MODULES = [\n    FormsModule,\n    ReactiveFormsModule,\n    MatSelectModule,\n    MatInputModule,\n    MatIconModule,\n    MatCheckboxModule,\n    MatRippleModule,\n    ScrollingModule,\n    MatMenuModule,\n];\nlet SwuiSelectModule = class SwuiSelectModule {\n};\nSwuiSelectModule = __decorate([\n    NgModule({\n        declarations: [SwuiSelectComponent],\n        imports: [\n            CommonModule,\n            TranslateModule.forChild(),\n            ...SELECT_MODULES,\n            MatMenuModule,\n            MatPseudoCheckboxModule\n        ],\n        exports: [SwuiSelectComponent],\n    })\n], SwuiSelectModule);\nexport { SwuiSelectModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,uBAAuB,EAAEC,eAAe,QAAQ,wBAAwB;AACjF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,OAAO,MAAMC,cAAc,GAAG,CAC1BX,WAAW,EACXC,mBAAmB,EACnBO,eAAe,EACfF,cAAc,EACdD,aAAa,EACbH,iBAAiB,EACjBE,eAAe,EACfP,eAAe,EACfU,aAAa,CAChB;AACD,IAAIK,gBAAgB,GAAG,MAAMA,gBAAgB,CAAC,EAC7C;AACDA,gBAAgB,GAAGhB,UAAU,CAAC,CAC1BG,QAAQ,CAAC;EACLc,YAAY,EAAE,CAACH,mBAAmB,CAAC;EACnCI,OAAO,EAAE,CACLhB,YAAY,EACZW,eAAe,CAACM,QAAQ,CAAC,CAAC,EAC1B,GAAGJ,cAAc,EACjBJ,aAAa,EACbJ,uBAAuB,CAC1B;EACDa,OAAO,EAAE,CAACN,mBAAmB;AACjC,CAAC,CAAC,CACL,EAAEE,gBAAgB,CAAC;AACpB,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}