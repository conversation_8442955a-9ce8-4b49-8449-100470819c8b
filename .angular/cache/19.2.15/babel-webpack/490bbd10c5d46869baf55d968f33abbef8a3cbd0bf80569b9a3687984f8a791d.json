{"ast": null, "code": "import { reduce } from './reduce';\nimport { operate } from '../util/lift';\nvar arrReducer = function (arr, value) {\n  return arr.push(value), arr;\n};\nexport function toArray() {\n  return operate(function (source, subscriber) {\n    reduce(arrReducer, [])(source).subscribe(subscriber);\n  });\n}\n//# sourceMappingURL=toArray.js.map", "map": {"version": 3, "names": ["reduce", "operate", "arrReducer", "arr", "value", "push", "toArray", "source", "subscriber", "subscribe"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/operators/toArray.js"], "sourcesContent": ["import { reduce } from './reduce';\nimport { operate } from '../util/lift';\nvar arrReducer = function (arr, value) { return (arr.push(value), arr); };\nexport function toArray() {\n    return operate(function (source, subscriber) {\n        reduce(arrReducer, [])(source).subscribe(subscriber);\n    });\n}\n//# sourceMappingURL=toArray.js.map"], "mappings": "AAAA,SAASA,MAAM,QAAQ,UAAU;AACjC,SAASC,OAAO,QAAQ,cAAc;AACtC,IAAIC,UAAU,GAAG,SAAAA,CAAUC,GAAG,EAAEC,KAAK,EAAE;EAAE,OAAQD,GAAG,CAACE,IAAI,CAACD,KAAK,CAAC,EAAED,GAAG;AAAG,CAAC;AACzE,OAAO,SAASG,OAAOA,CAAA,EAAG;EACtB,OAAOL,OAAO,CAAC,UAAUM,MAAM,EAAEC,UAAU,EAAE;IACzCR,MAAM,CAACE,UAAU,EAAE,EAAE,CAAC,CAACK,MAAM,CAAC,CAACE,SAAS,CAACD,UAAU,CAAC;EACxD,CAAC,CAAC;AACN;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}