{"ast": null, "code": "var _SwuiDatePickerComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-date-picker.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-date-picker.component.scss?ngResource\";\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { BehaviorSubject, combineLatest } from 'rxjs';\nimport { map, takeUntil } from 'rxjs/operators';\nimport * as moment from 'moment';\nimport { SwuiDatePickerConfigModel } from './swui-date-picker-config.model';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nconst CONTROL_NAME = 'lib-swui-date-picker';\nlet nextUniqueId = 0;\nexport function processInputString(val, config) {\n  const {\n    dateFormat,\n    timePicker,\n    timeFormat,\n    timeZone\n  } = config;\n  const format = timePicker ? dateFormat + ' ' + timeFormat : dateFormat;\n  let processed = '';\n  if (moment.utc(val).isValid()) {\n    if (timeZone) {\n      processed = moment.tz(val, timeZone).format(format);\n    } else {\n      processed = moment.utc(val).format(format);\n    }\n  }\n  return processed;\n}\nlet SwuiDatePickerComponent = (_SwuiDatePickerComponent = class SwuiDatePickerComponent extends SwuiMatFormFieldControl {\n  set config(val) {\n    this._config$.next(new SwuiDatePickerConfigModel(val));\n  }\n  get config() {\n    return this._config$.value;\n  }\n  set value(val) {\n    this._value$.next(val);\n  }\n  get value() {\n    return this._value$.value;\n  }\n  get empty() {\n    return !this.value;\n  }\n  get shouldLabelFloat() {\n    return this.inputControl.value;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.minDate = '';\n    this.maxDate = '';\n    this.title = '';\n    this.inputControl = new UntypedFormControl();\n    this.dateControl = new UntypedFormControl();\n    this.controlType = CONTROL_NAME;\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._value$ = new BehaviorSubject('');\n    this._config$ = new BehaviorSubject(new SwuiDatePickerConfigModel(undefined));\n    this._sourceValue = '';\n  }\n  ngOnInit() {\n    combineLatest([this._value$, this._config$]).pipe(map(([val, config]) => {\n      const processed = val && moment.utc(val).isValid() ? val : '';\n      return {\n        processed,\n        config\n      };\n    }), takeUntil(this.destroyed$)).subscribe(val => {\n      const {\n        processed,\n        config\n      } = val;\n      this._sourceValue = processed;\n      this.dateControl.setValue(processed, {\n        emitEvent: false\n      });\n      this.inputControl.setValue(processInputString(processed, config), {\n        emitEvent: false\n      });\n      this.onChange(processed);\n    });\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.elRef && this.menuTrigger && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n      this.menuTrigger.openMenu();\n    }\n  }\n  writeValue(val) {\n    this._value$.next(val);\n  }\n  cancel(event) {\n    this.prevent(event);\n    this._value$.next(this._sourceValue);\n    if (this.menuTrigger) {\n      this.menuTrigger.closeMenu();\n    }\n  }\n  apply(event) {\n    this.prevent(event);\n    this._value$.next(this.dateControl.value);\n    if (this.menuTrigger) {\n      this.menuTrigger.closeMenu();\n    }\n  }\n  clear(event) {\n    this.prevent(event);\n    this.dateControl.setValue('');\n  }\n  prevent(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  onOpen() {\n    this.dateControl.setValue(this.dateControl.value);\n  }\n  onDisabledState(disabled) {\n    disabled ? this.inputControl.disable() : this.inputControl.enable();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n}, _SwuiDatePickerComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}], _SwuiDatePickerComponent.propDecorators = {\n  minDate: [{\n    type: Input\n  }],\n  maxDate: [{\n    type: Input\n  }],\n  config: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  value: [{\n    type: Input\n  }],\n  input: [{\n    type: ViewChild,\n    args: [MatInput]\n  }],\n  menuTrigger: [{\n    type: ViewChild,\n    args: [MatMenuTrigger, {\n      static: true\n    }]\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiDatePickerComponent);\nSwuiDatePickerComponent = __decorate([Component({\n  selector: 'lib-swui-date-picker',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiDatePickerComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiDatePickerComponent);\nexport { SwuiDatePickerComponent };", "map": {"version": 3, "names": ["Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormControl", "FormGroupDirective", "NgControl", "FocusMonitor", "BehaviorSubject", "combineLatest", "map", "takeUntil", "moment", "SwuiDatePickerConfigModel", "MatMenuTrigger", "MatFormFieldControl", "MatInput", "SwuiMatFormFieldControl", "ErrorStateMatcher", "CONTROL_NAME", "nextUniqueId", "processInputString", "val", "config", "dateFormat", "timePicker", "timeFormat", "timeZone", "format", "processed", "utc", "<PERSON><PERSON><PERSON><PERSON>", "tz", "SwuiDatePickerComponent", "_SwuiDatePickerComponent", "_config$", "next", "value", "_value$", "empty", "shouldLabelFloat", "inputControl", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "minDate", "maxDate", "title", "dateControl", "controlType", "id", "undefined", "_sourceValue", "ngOnInit", "pipe", "destroyed$", "subscribe", "setValue", "emitEvent", "onChange", "onContainerClick", "event", "stopPropagation", "menuTrigger", "target", "tagName", "toLowerCase", "disabled", "nativeElement", "focus", "openMenu", "writeValue", "cancel", "prevent", "closeMenu", "apply", "clear", "preventDefault", "onOpen", "onDisabledState", "disable", "enable", "isErrorState", "input", "errorState", "type", "args", "static", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "provide", "useExisting", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-picker/swui-date-picker.component.ts"], "sourcesContent": ["import { Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { BehaviorSubject, combineLatest } from 'rxjs';\nimport { map, takeUntil } from 'rxjs/operators';\nimport * as moment from 'moment';\nimport { SwuiDatePickerConfig, SwuiDatePickerConfigModel } from './swui-date-picker-config.model';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\n\n\nconst CONTROL_NAME = 'lib-swui-date-picker';\nlet nextUniqueId = 0;\n\nexport function processInputString( val: string, config: SwuiDatePickerConfigModel ): string {\n  const { dateFormat, timePicker, timeFormat, timeZone } = config;\n  const format = timePicker ? dateFormat + ' ' + timeFormat : dateFormat;\n  let processed = '';\n  if (moment.utc(val).isValid()) {\n    if (timeZone) {\n      processed = moment.tz(val, timeZone).format(format);\n    } else {\n      processed = moment.utc(val).format(format);\n    }\n  }\n  return processed;\n}\n\n@Component({\n    selector: 'lib-swui-date-picker',\n    templateUrl: './swui-date-picker.component.html',\n    styleUrls: ['./swui-date-picker.component.scss'],\n    providers: [{ provide: MatFormFieldControl, useExisting: SwuiDatePickerComponent }],\n    standalone: false\n})\n\nexport class SwuiDatePickerComponent extends SwuiMatFormFieldControl<string> implements OnInit {\n  @Input() minDate = '';\n  @Input() maxDate = '';\n\n  @Input()\n  set config( val: SwuiDatePickerConfig ) {\n    this._config$.next(new SwuiDatePickerConfigModel(val));\n  }\n\n  get config(): SwuiDatePickerConfig {\n    return this._config$.value;\n  }\n\n  @Input() title = '';\n\n  @Input()\n  set value( val: string ) {\n    this._value$.next(val);\n  }\n\n  get value(): string {\n    return this._value$.value;\n  }\n\n  get empty() {\n    return !this.value;\n  }\n\n  readonly inputControl = new UntypedFormControl();\n  readonly dateControl = new UntypedFormControl();\n  readonly controlType = CONTROL_NAME;\n\n  @ViewChild(MatInput) input?: MatInput;\n  @ViewChild(MatMenuTrigger, { static: true }) menuTrigger?: MatMenuTrigger;\n\n  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;\n\n  @HostBinding('class.floating')\n  get shouldLabelFloat() {\n    return this.inputControl.value;\n  }\n\n  private readonly _value$ = new BehaviorSubject<string>('');\n  private readonly _config$ = new BehaviorSubject<SwuiDatePickerConfig>(new SwuiDatePickerConfigModel(undefined));\n  private _sourceValue = '';\n\n  constructor( fm: FocusMonitor,\n               elRef: ElementRef<HTMLElement>,\n               @Optional() @Self() ngControl: NgControl,\n               @Optional() parentFormGroup: FormGroupDirective,\n               errorStateMatcher: ErrorStateMatcher ) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n  }\n\n  ngOnInit(): void {\n    combineLatest([this._value$, this._config$])\n      .pipe(\n        map(( [val, config] ) => {\n          const processed = val && moment.utc(val).isValid() ? val : '';\n          return { processed, config };\n        }),\n        takeUntil(this.destroyed$)\n      )\n      .subscribe(val => {\n        const { processed, config } = val;\n        this._sourceValue = processed;\n        this.dateControl.setValue(processed, { emitEvent: false });\n        this.inputControl.setValue(processInputString(processed, config), { emitEvent: false });\n        this.onChange(processed);\n      });\n  }\n\n  onContainerClick( event: Event ): void {\n    event.stopPropagation();\n    if (this.elRef && this.menuTrigger && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n      this.menuTrigger.openMenu();\n    }\n  }\n\n  writeValue( val: string ): void {\n    this._value$.next(val);\n  }\n\n  cancel( event: Event ) {\n    this.prevent(event);\n    this._value$.next(this._sourceValue);\n    if (this.menuTrigger) {\n      this.menuTrigger.closeMenu();\n    }\n  }\n\n  apply( event: Event ) {\n    this.prevent(event);\n    this._value$.next(this.dateControl.value);\n    if (this.menuTrigger) {\n      this.menuTrigger.closeMenu();\n    }\n  }\n\n  clear( event: Event ) {\n    this.prevent(event);\n    this.dateControl.setValue('');\n  }\n\n  prevent( event: Event ) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n\n  onOpen() {\n    this.dateControl.setValue(this.dateControl.value);\n  }\n\n  protected onDisabledState( disabled: boolean ) {\n    disabled ? this.inputControl.disable() : this.inputControl.enable();\n  }\n\n  protected isErrorState(): boolean {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAUC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AAC5G,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAClF,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,eAAe,EAAEC,aAAa,QAAQ,MAAM;AACrD,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAC/C,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAA+BC,yBAAyB,QAAQ,iCAAiC;AACjG,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAG1D,MAAMC,YAAY,GAAG,sBAAsB;AAC3C,IAAIC,YAAY,GAAG,CAAC;AAEpB,OAAM,SAAUC,kBAAkBA,CAAEC,GAAW,EAAEC,MAAiC;EAChF,MAAM;IAAEC,UAAU;IAAEC,UAAU;IAAEC,UAAU;IAAEC;EAAQ,CAAE,GAAGJ,MAAM;EAC/D,MAAMK,MAAM,GAAGH,UAAU,GAAGD,UAAU,GAAG,GAAG,GAAGE,UAAU,GAAGF,UAAU;EACtE,IAAIK,SAAS,GAAG,EAAE;EAClB,IAAIjB,MAAM,CAACkB,GAAG,CAACR,GAAG,CAAC,CAACS,OAAO,EAAE,EAAE;IAC7B,IAAIJ,QAAQ,EAAE;MACZE,SAAS,GAAGjB,MAAM,CAACoB,EAAE,CAACV,GAAG,EAAEK,QAAQ,CAAC,CAACC,MAAM,CAACA,MAAM,CAAC;IACrD,CAAC,MAAM;MACLC,SAAS,GAAGjB,MAAM,CAACkB,GAAG,CAACR,GAAG,CAAC,CAACM,MAAM,CAACA,MAAM,CAAC;IAC5C;EACF;EACA,OAAOC,SAAS;AAClB;AAUO,IAAMI,uBAAuB,IAAAC,wBAAA,GAA7B,MAAMD,uBAAwB,SAAQhB,uBAA+B;MAKtEM,MAAMA,CAAED,GAAyB;IACnC,IAAI,CAACa,QAAQ,CAACC,IAAI,CAAC,IAAIvB,yBAAyB,CAACS,GAAG,CAAC,CAAC;EACxD;EAEA,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACY,QAAQ,CAACE,KAAK;EAC5B;MAKIA,KAAKA,CAAEf,GAAW;IACpB,IAAI,CAACgB,OAAO,CAACF,IAAI,CAACd,GAAG,CAAC;EACxB;EAEA,IAAIe,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,OAAO,CAACD,KAAK;EAC3B;EAEA,IAAIE,KAAKA,CAAA;IACP,OAAO,CAAC,IAAI,CAACF,KAAK;EACpB;MAYIG,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,YAAY,CAACJ,KAAK;EAChC;EAMAK,YAAaC,EAAgB,EAChBC,KAA8B,EACVC,SAAoB,EAC5BC,eAAmC,EAC/CC,iBAAoC;IAC/C,KAAK,CAACJ,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAlDxD,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,OAAO,GAAG,EAAE;IAWZ,KAAAC,KAAK,GAAG,EAAE;IAeV,KAAAT,YAAY,GAAG,IAAIrC,kBAAkB,EAAE;IACvC,KAAA+C,WAAW,GAAG,IAAI/C,kBAAkB,EAAE;IACtC,KAAAgD,WAAW,GAAGjC,YAAY;IAKX,KAAAkC,EAAE,GAAG,GAAGlC,YAAY,IAAIC,YAAY,EAAE,EAAE;IAO/C,KAAAkB,OAAO,GAAG,IAAI9B,eAAe,CAAS,EAAE,CAAC;IACzC,KAAA2B,QAAQ,GAAG,IAAI3B,eAAe,CAAuB,IAAIK,yBAAyB,CAACyC,SAAS,CAAC,CAAC;IACvG,KAAAC,YAAY,GAAG,EAAE;EAQzB;EAEAC,QAAQA,CAAA;IACN/C,aAAa,CAAC,CAAC,IAAI,CAAC6B,OAAO,EAAE,IAAI,CAACH,QAAQ,CAAC,CAAC,CACzCsB,IAAI,CACH/C,GAAG,CAAC,CAAE,CAACY,GAAG,EAAEC,MAAM,CAAC,KAAK;MACtB,MAAMM,SAAS,GAAGP,GAAG,IAAIV,MAAM,CAACkB,GAAG,CAACR,GAAG,CAAC,CAACS,OAAO,EAAE,GAAGT,GAAG,GAAG,EAAE;MAC7D,OAAO;QAAEO,SAAS;QAAEN;MAAM,CAAE;IAC9B,CAAC,CAAC,EACFZ,SAAS,CAAC,IAAI,CAAC+C,UAAU,CAAC,CAC3B,CACAC,SAAS,CAACrC,GAAG,IAAG;MACf,MAAM;QAAEO,SAAS;QAAEN;MAAM,CAAE,GAAGD,GAAG;MACjC,IAAI,CAACiC,YAAY,GAAG1B,SAAS;MAC7B,IAAI,CAACsB,WAAW,CAACS,QAAQ,CAAC/B,SAAS,EAAE;QAAEgC,SAAS,EAAE;MAAK,CAAE,CAAC;MAC1D,IAAI,CAACpB,YAAY,CAACmB,QAAQ,CAACvC,kBAAkB,CAACQ,SAAS,EAAEN,MAAM,CAAC,EAAE;QAAEsC,SAAS,EAAE;MAAK,CAAE,CAAC;MACvF,IAAI,CAACC,QAAQ,CAACjC,SAAS,CAAC;IAC1B,CAAC,CAAC;EACN;EAEAkC,gBAAgBA,CAAEC,KAAY;IAC5BA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,IAAI,CAACrB,KAAK,IAAI,IAAI,CAACsB,WAAW,IAAKF,KAAK,CAACG,MAAkB,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MACnH,IAAI,CAAC1B,KAAK,CAAC2B,aAAa,CAACC,KAAK,EAAE;MAChC,IAAI,CAACN,WAAW,CAACO,QAAQ,EAAE;IAC7B;EACF;EAEAC,UAAUA,CAAEpD,GAAW;IACrB,IAAI,CAACgB,OAAO,CAACF,IAAI,CAACd,GAAG,CAAC;EACxB;EAEAqD,MAAMA,CAAEX,KAAY;IAClB,IAAI,CAACY,OAAO,CAACZ,KAAK,CAAC;IACnB,IAAI,CAAC1B,OAAO,CAACF,IAAI,CAAC,IAAI,CAACmB,YAAY,CAAC;IACpC,IAAI,IAAI,CAACW,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACW,SAAS,EAAE;IAC9B;EACF;EAEAC,KAAKA,CAAEd,KAAY;IACjB,IAAI,CAACY,OAAO,CAACZ,KAAK,CAAC;IACnB,IAAI,CAAC1B,OAAO,CAACF,IAAI,CAAC,IAAI,CAACe,WAAW,CAACd,KAAK,CAAC;IACzC,IAAI,IAAI,CAAC6B,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACW,SAAS,EAAE;IAC9B;EACF;EAEAE,KAAKA,CAAEf,KAAY;IACjB,IAAI,CAACY,OAAO,CAACZ,KAAK,CAAC;IACnB,IAAI,CAACb,WAAW,CAACS,QAAQ,CAAC,EAAE,CAAC;EAC/B;EAEAgB,OAAOA,CAAEZ,KAAY;IACnBA,KAAK,CAACgB,cAAc,EAAE;IACtBhB,KAAK,CAACC,eAAe,EAAE;EACzB;EAEAgB,MAAMA,CAAA;IACJ,IAAI,CAAC9B,WAAW,CAACS,QAAQ,CAAC,IAAI,CAACT,WAAW,CAACd,KAAK,CAAC;EACnD;EAEU6C,eAAeA,CAAEZ,QAAiB;IAC1CA,QAAQ,GAAG,IAAI,CAAC7B,YAAY,CAAC0C,OAAO,EAAE,GAAG,IAAI,CAAC1C,YAAY,CAAC2C,MAAM,EAAE;EACrE;EAEUC,YAAYA,CAAA;IACpB,IAAI,IAAI,CAACC,KAAK,EAAE;MACd,OAAO,IAAI,CAACA,KAAK,CAACC,UAAU;IAC9B;IACA,OAAO,KAAK;EACd;;;;;;;;UA3EctF;EAAQ;IAAAuF,IAAA,EAAItF;EAAI;AAAA,G;;;UAChBD;EAAQ;AAAA,G;;;;UAhDrBD;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UASLA;EAAK;;UAELA;EAAK;;UAiBLG,SAAS;IAAAsF,IAAA,GAACzE,QAAQ;EAAA;;UAClBb,SAAS;IAAAsF,IAAA,GAAC3E,cAAc,EAAE;MAAE4E,MAAM,EAAE;IAAI,CAAE;EAAA;;UAE1C3F;EAAW;;UAEXA,WAAW;IAAA0F,IAAA,GAAC,gBAAgB;EAAA;;AArClBxD,uBAAuB,GAAA0D,UAAA,EARnC9F,SAAS,CAAC;EACP+F,QAAQ,EAAE,sBAAsB;EAChCC,QAAA,EAAAC,oBAAgD;EAEhDC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAEjF,mBAAmB;IAAEkF,WAAW,EAAEhE;EAAuB,CAAE,CAAC;EACnFiE,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EAEWjE,uBAAuB,CA4HnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}