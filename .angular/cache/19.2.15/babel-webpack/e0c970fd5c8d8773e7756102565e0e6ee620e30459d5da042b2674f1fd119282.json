{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { render_exports, isComponent, computesTemplateFromComponent, formatPropInTemplate } from './chunk-LXSTVAFF.mjs';\nimport { setDefaultProjectAnnotations, setProjectAnnotations as setProjectAnnotations$1 } from 'storybook/preview-api';\nfunction setProjectAnnotations(projectAnnotations) {\n  return setDefaultProjectAnnotations(render_exports), setProjectAnnotations$1(projectAnnotations);\n}\nvar moduleMetadata = metadata => storyFn => {\n  let story = storyFn(),\n    storyMetadata = story.moduleMetadata || {};\n  return metadata = metadata || {}, _objectSpread(_objectSpread({}, story), {}, {\n    moduleMetadata: {\n      declarations: [...(metadata.declarations || []), ...(storyMetadata.declarations || [])],\n      entryComponents: [...(metadata.entryComponents || []), ...(storyMetadata.entryComponents || [])],\n      imports: [...(metadata.imports || []), ...(storyMetadata.imports || [])],\n      schemas: [...(metadata.schemas || []), ...(storyMetadata.schemas || [])],\n      providers: [...(metadata.providers || []), ...(storyMetadata.providers || [])]\n    }\n  });\n};\nfunction applicationConfig(config) {\n  return storyFn => {\n    let story = storyFn(),\n      storyConfig = story.applicationConfig;\n    return _objectSpread(_objectSpread({}, story), {}, {\n      applicationConfig: storyConfig || config ? _objectSpread(_objectSpread(_objectSpread({}, config), storyConfig), {}, {\n        providers: [...((config === null || config === void 0 ? void 0 : config.providers) || []), ...((storyConfig === null || storyConfig === void 0 ? void 0 : storyConfig.providers) || [])]\n      }) : void 0\n    });\n  };\n}\nvar componentWrapperDecorator = (element, props) => (storyFn, storyContext) => {\n  let story = storyFn(),\n    currentProps = typeof props == \"function\" ? props(storyContext) : props,\n    template = isComponent(element) ? computesTemplateFromComponent(element, currentProps !== null && currentProps !== void 0 ? currentProps : {}, story.template) : element(story.template);\n  return _objectSpread(_objectSpread({}, story), {}, {\n    template\n  }, currentProps || story.props ? {\n    props: _objectSpread(_objectSpread({}, currentProps), story.props)\n  } : {});\n};\nfunction argsToTemplate(args, options = {}) {\n  let includeSet = options.include ? new Set(options.include) : null,\n    excludeSet = options.exclude ? new Set(options.exclude) : null;\n  return Object.entries(args).filter(([key]) => args[key] !== void 0).filter(([key]) => includeSet ? includeSet.has(key) : excludeSet ? !excludeSet.has(key) : !0).map(([key, value]) => typeof value == \"function\" ? `(${key})=\"${formatPropInTemplate(key)}($event)\"` : `[${key}]=\"${formatPropInTemplate(key)}\"`).join(\" \");\n}\nexport { applicationConfig, argsToTemplate, componentWrapperDecorator, moduleMetadata, setProjectAnnotations };", "map": {"version": 3, "names": ["render_exports", "isComponent", "computesTemplateFromComponent", "formatPropInTemplate", "setDefaultProjectAnnotations", "setProjectAnnotations", "setProjectAnnotations$1", "projectAnnotations", "moduleMetadata", "metadata", "storyFn", "story", "storyMetadata", "_objectSpread", "declarations", "entryComponents", "imports", "schemas", "providers", "applicationConfig", "config", "storyConfig", "componentWrapperDecorator", "element", "props", "storyContext", "currentProps", "template", "argsToTemplate", "args", "options", "includeSet", "include", "Set", "excludeSet", "exclude", "Object", "entries", "filter", "key", "has", "map", "value", "join"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@storybook/angular/dist/chunk-KMSSK3DZ.mjs"], "sourcesContent": ["import { render_exports, isComponent, computesTemplateFromComponent, formatPropInTemplate } from './chunk-LXSTVAFF.mjs';\nimport { setDefaultProjectAnnotations, setProjectAnnotations as setProjectAnnotations$1 } from 'storybook/preview-api';\n\nfunction setProjectAnnotations(projectAnnotations){return setDefaultProjectAnnotations(render_exports),setProjectAnnotations$1(projectAnnotations)}var moduleMetadata=metadata=>storyFn=>{let story=storyFn(),storyMetadata=story.moduleMetadata||{};return metadata=metadata||{},{...story,moduleMetadata:{declarations:[...metadata.declarations||[],...storyMetadata.declarations||[]],entryComponents:[...metadata.entryComponents||[],...storyMetadata.entryComponents||[]],imports:[...metadata.imports||[],...storyMetadata.imports||[]],schemas:[...metadata.schemas||[],...storyMetadata.schemas||[]],providers:[...metadata.providers||[],...storyMetadata.providers||[]]}}};function applicationConfig(config){return storyFn=>{let story=storyFn(),storyConfig=story.applicationConfig;return {...story,applicationConfig:storyConfig||config?{...config,...storyConfig,providers:[...config?.providers||[],...storyConfig?.providers||[]]}:void 0}}}var componentWrapperDecorator=(element,props)=>(storyFn,storyContext)=>{let story=storyFn(),currentProps=typeof props==\"function\"?props(storyContext):props,template=isComponent(element)?computesTemplateFromComponent(element,currentProps??{},story.template):element(story.template);return {...story,template,...currentProps||story.props?{props:{...currentProps,...story.props}}:{}}};function argsToTemplate(args,options={}){let includeSet=options.include?new Set(options.include):null,excludeSet=options.exclude?new Set(options.exclude):null;return Object.entries(args).filter(([key])=>args[key]!==void 0).filter(([key])=>includeSet?includeSet.has(key):excludeSet?!excludeSet.has(key):!0).map(([key,value])=>typeof value==\"function\"?`(${key})=\"${formatPropInTemplate(key)}($event)\"`:`[${key}]=\"${formatPropInTemplate(key)}\"`).join(\" \")}\n\nexport { applicationConfig, argsToTemplate, componentWrapperDecorator, moduleMetadata, setProjectAnnotations };\n"], "mappings": ";AAAA,SAASA,cAAc,EAAEC,WAAW,EAAEC,6BAA6B,EAAEC,oBAAoB,QAAQ,sBAAsB;AACvH,SAASC,4BAA4B,EAAEC,qBAAqB,IAAIC,uBAAuB,QAAQ,uBAAuB;AAEtH,SAASD,qBAAqBA,CAACE,kBAAkB,EAAC;EAAC,OAAOH,4BAA4B,CAACJ,cAAc,CAAC,EAACM,uBAAuB,CAACC,kBAAkB,CAAC;AAAA;AAAC,IAAIC,cAAc,GAACC,QAAQ,IAAEC,OAAO,IAAE;EAAC,IAAIC,KAAK,GAACD,OAAO,CAAC,CAAC;IAACE,aAAa,GAACD,KAAK,CAACH,cAAc,IAAE,CAAC,CAAC;EAAC,OAAOC,QAAQ,GAACA,QAAQ,IAAE,CAAC,CAAC,EAAAI,aAAA,CAAAA,aAAA,KAAKF,KAAK;IAACH,cAAc,EAAC;MAACM,YAAY,EAAC,CAAC,IAAGL,QAAQ,CAACK,YAAY,IAAE,EAAE,GAAC,IAAGF,aAAa,CAACE,YAAY,IAAE,EAAE,EAAC;MAACC,eAAe,EAAC,CAAC,IAAGN,QAAQ,CAACM,eAAe,IAAE,EAAE,GAAC,IAAGH,aAAa,CAACG,eAAe,IAAE,EAAE,EAAC;MAACC,OAAO,EAAC,CAAC,IAAGP,QAAQ,CAACO,OAAO,IAAE,EAAE,GAAC,IAAGJ,aAAa,CAACI,OAAO,IAAE,EAAE,EAAC;MAACC,OAAO,EAAC,CAAC,IAAGR,QAAQ,CAACQ,OAAO,IAAE,EAAE,GAAC,IAAGL,aAAa,CAACK,OAAO,IAAE,EAAE,EAAC;MAACC,SAAS,EAAC,CAAC,IAAGT,QAAQ,CAACS,SAAS,IAAE,EAAE,GAAC,IAAGN,aAAa,CAACM,SAAS,IAAE,EAAE;IAAC;EAAC,EAAC;AAAA,CAAC;AAAC,SAASC,iBAAiBA,CAACC,MAAM,EAAC;EAAC,OAAOV,OAAO,IAAE;IAAC,IAAIC,KAAK,GAACD,OAAO,CAAC,CAAC;MAACW,WAAW,GAACV,KAAK,CAACQ,iBAAiB;IAAC,OAAAN,aAAA,CAAAA,aAAA,KAAWF,KAAK;MAACQ,iBAAiB,EAACE,WAAW,IAAED,MAAM,GAAAP,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAKO,MAAM,GAAIC,WAAW;QAACH,SAAS,EAAC,CAAC,IAAG,CAAAE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEF,SAAS,KAAE,EAAE,GAAC,IAAG,CAAAG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEH,SAAS,KAAE,EAAE;MAAC,KAAE,KAAK;IAAC;EAAC,CAAC;AAAA;AAAC,IAAII,yBAAyB,GAACA,CAACC,OAAO,EAACC,KAAK,KAAG,CAACd,OAAO,EAACe,YAAY,KAAG;EAAC,IAAId,KAAK,GAACD,OAAO,CAAC,CAAC;IAACgB,YAAY,GAAC,OAAOF,KAAK,IAAE,UAAU,GAACA,KAAK,CAACC,YAAY,CAAC,GAACD,KAAK;IAACG,QAAQ,GAAC1B,WAAW,CAACsB,OAAO,CAAC,GAACrB,6BAA6B,CAACqB,OAAO,EAACG,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAE,CAAC,CAAC,EAACf,KAAK,CAACgB,QAAQ,CAAC,GAACJ,OAAO,CAACZ,KAAK,CAACgB,QAAQ,CAAC;EAAC,OAAAd,aAAA,CAAAA,aAAA,KAAWF,KAAK;IAACgB;EAAQ,GAAID,YAAY,IAAEf,KAAK,CAACa,KAAK,GAAC;IAACA,KAAK,EAAAX,aAAA,CAAAA,aAAA,KAAKa,YAAY,GAAIf,KAAK,CAACa,KAAK;EAAC,CAAC,GAAC,CAAC,CAAC;AAAC,CAAC;AAAC,SAASI,cAAcA,CAACC,IAAI,EAACC,OAAO,GAAC,CAAC,CAAC,EAAC;EAAC,IAAIC,UAAU,GAACD,OAAO,CAACE,OAAO,GAAC,IAAIC,GAAG,CAACH,OAAO,CAACE,OAAO,CAAC,GAAC,IAAI;IAACE,UAAU,GAACJ,OAAO,CAACK,OAAO,GAAC,IAAIF,GAAG,CAACH,OAAO,CAACK,OAAO,CAAC,GAAC,IAAI;EAAC,OAAOC,MAAM,CAACC,OAAO,CAACR,IAAI,CAAC,CAACS,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,KAAGV,IAAI,CAACU,GAAG,CAAC,KAAG,KAAK,CAAC,CAAC,CAACD,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,KAAGR,UAAU,GAACA,UAAU,CAACS,GAAG,CAACD,GAAG,CAAC,GAACL,UAAU,GAAC,CAACA,UAAU,CAACM,GAAG,CAACD,GAAG,CAAC,GAAC,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,CAAC,CAACF,GAAG,EAACG,KAAK,CAAC,KAAG,OAAOA,KAAK,IAAE,UAAU,GAAC,IAAIH,GAAG,MAAMpC,oBAAoB,CAACoC,GAAG,CAAC,WAAW,GAAC,IAAIA,GAAG,MAAMpC,oBAAoB,CAACoC,GAAG,CAAC,GAAG,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC;AAAA;AAEnuD,SAASxB,iBAAiB,EAAES,cAAc,EAAEN,yBAAyB,EAAEd,cAAc,EAAEH,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}