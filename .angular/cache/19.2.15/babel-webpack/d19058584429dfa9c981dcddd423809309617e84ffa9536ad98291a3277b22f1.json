{"ast": null, "code": "import { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { TestBed, waitForAsync } from '@angular/core/testing';\nimport { ActivatedRoute } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { EntityPickerComponent } from './entity-picker.component';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatRippleModule } from '@angular/material/core';\nimport { LanguageSelectorModule } from '../language-selector/language-selector.module';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';\nimport { SwHubEntityService } from '../../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwHubInitService } from '../../services/sw-hub-init/sw-hub-init.service';\ndescribe('EntityPickerComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      schemas: [NO_ERRORS_SCHEMA],\n      declarations: [EntityPickerComponent],\n      imports: [TranslateModule.forRoot(), MatRippleModule, MatIconModule, MatMenuModule, MatDividerModule, MatDialogModule, LanguageSelectorModule, SwuiSettingsDialogModule],\n      providers: [{\n        provide: MatDialog,\n        useValue: {\n          open: () => {}\n        }\n      }, {\n        provide: SWUI_HUB_MESSAGE_CONFIG,\n        useValue: {}\n      }, {\n        provide: SwHubInitService,\n        useValue: {}\n      }, SwHubEntityService, {\n        provide: ActivatedRoute,\n        useValue: {\n          snapshot: {\n            queryParams: {}\n          }\n        }\n      }, {\n        provide: SwHubAuthService,\n        useValue: {\n          isLogged() {\n            return true;\n          }\n        }\n      }]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(EntityPickerComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["NO_ERRORS_SCHEMA", "TestBed", "waitForAsync", "ActivatedRoute", "TranslateModule", "SwHubAuthService", "EntityPickerComponent", "MatDialog", "MatDialogModule", "MatDividerModule", "MatIconModule", "MatMenuModule", "MatRippleModule", "LanguageSelectorModule", "SWUI_HUB_MESSAGE_CONFIG", "SwuiSettingsDialogModule", "SwHubEntityService", "SwHubInitService", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "schemas", "declarations", "imports", "forRoot", "providers", "provide", "useValue", "open", "snapshot", "queryParams", "isLogged", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/entity-picker/entity-picker.component.spec.ts"], "sourcesContent": ["import { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\nimport { ActivatedRoute } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { EntityPickerComponent } from './entity-picker.component';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatRippleModule } from '@angular/material/core';\nimport { LanguageSelectorModule } from '../language-selector/language-selector.module';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';\nimport { SwHubEntityService } from '../../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwHubInitService } from '../../services/sw-hub-init/sw-hub-init.service';\n\n\ndescribe('EntityPickerComponent', () => {\n  let component: EntityPickerComponent;\n  let fixture: ComponentFixture<EntityPickerComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      schemas: [NO_ERRORS_SCHEMA],\n      declarations: [EntityPickerComponent],\n      imports: [\n        TranslateModule.forRoot(),\n        MatRippleModule,\n        MatIconModule,\n        MatMenuModule,\n        MatDividerModule,\n        MatDialogModule,\n        LanguageSelectorModule,\n        SwuiSettingsDialogModule\n      ],\n      providers: [\n        {\n          provide: MatDialog, useValue: {\n            open: () => {\n            }\n          }\n        },\n        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: {} },\n        { provide: SwHubInitService, useValue: {} },\n        SwHubEntityService,\n        {\n          provide: ActivatedRoute,\n          useValue: {\n            snapshot: {\n              queryParams: {}\n            }\n          }\n        },\n        {\n          provide: SwHubAuthService, useValue: {\n            isLogged() {\n              return true;\n            }\n          }\n        },\n      ]\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(EntityPickerComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,eAAe;AAChD,SAA2BC,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAC/E,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,SAAS,EAAEC,eAAe,QAAQ,0BAA0B;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,sBAAsB,QAAQ,+CAA+C;AACtF,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,wBAAwB,QAAQ,gDAAgD;AACzF,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,gBAAgB,QAAQ,gDAAgD;AAGjFC,QAAQ,CAAC,uBAAuB,EAAE,MAAK;EACrC,IAAIC,SAAgC;EACpC,IAAIC,OAAgD;EAEpDC,UAAU,CAACnB,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACqB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACvB,gBAAgB,CAAC;MAC3BwB,YAAY,EAAE,CAAClB,qBAAqB,CAAC;MACrCmB,OAAO,EAAE,CACPrB,eAAe,CAACsB,OAAO,EAAE,EACzBd,eAAe,EACfF,aAAa,EACbC,aAAa,EACbF,gBAAgB,EAChBD,eAAe,EACfK,sBAAsB,EACtBE,wBAAwB,CACzB;MACDY,SAAS,EAAE,CACT;QACEC,OAAO,EAAErB,SAAS;QAAEsB,QAAQ,EAAE;UAC5BC,IAAI,EAAEA,CAAA,KAAK,CACX;;OAEH,EACD;QAAEF,OAAO,EAAEd,uBAAuB;QAAEe,QAAQ,EAAE;MAAE,CAAE,EAClD;QAAED,OAAO,EAAEX,gBAAgB;QAAEY,QAAQ,EAAE;MAAE,CAAE,EAC3Cb,kBAAkB,EAClB;QACEY,OAAO,EAAEzB,cAAc;QACvB0B,QAAQ,EAAE;UACRE,QAAQ,EAAE;YACRC,WAAW,EAAE;;;OAGlB,EACD;QACEJ,OAAO,EAAEvB,gBAAgB;QAAEwB,QAAQ,EAAE;UACnCI,QAAQA,CAAA;YACN,OAAO,IAAI;UACb;;OAEH;KAEJ,CAAC,CACCC,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHb,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGnB,OAAO,CAACkC,eAAe,CAAC7B,qBAAqB,CAAC;IACxDa,SAAS,GAAGC,OAAO,CAACgB,iBAAiB;IACrChB,OAAO,CAACiB,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACpB,SAAS,CAAC,CAACqB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}