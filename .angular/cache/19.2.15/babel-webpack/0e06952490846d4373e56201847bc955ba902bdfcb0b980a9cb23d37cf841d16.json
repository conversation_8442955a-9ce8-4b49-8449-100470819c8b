{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { NgModule } from '@angular/core';\nimport { MatIconModule } from '@angular/material/icon';\nimport { SwuiSearchComponent } from './swui-search.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nlet SwuiSearchModule = class SwuiSearchModule {};\nSwuiSearchModule = __decorate([NgModule({\n  declarations: [SwuiSearchComponent],\n  exports: [SwuiSearchComponent],\n  imports: [CommonModule, FormsModule, MatFormFieldModule, MatInputModule, ReactiveFormsModule, MatIconModule]\n})], SwuiSearchModule);\nexport { SwuiSearchModule };", "map": {"version": 3, "names": ["__decorate", "CommonModule", "FormsModule", "ReactiveFormsModule", "NgModule", "MatIconModule", "SwuiSearchComponent", "MatFormFieldModule", "MatInputModule", "SwuiSearchModule", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-search/swui-search.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { NgModule } from '@angular/core';\nimport { MatIconModule } from '@angular/material/icon';\nimport { SwuiSearchComponent } from './swui-search.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nlet SwuiSearchModule = class SwuiSearchModule {\n};\nSwuiSearchModule = __decorate([\n    NgModule({\n        declarations: [\n            SwuiSearchComponent,\n        ],\n        exports: [\n            SwuiSearchComponent,\n        ],\n        imports: [\n            CommonModule,\n            FormsModule,\n            MatFormFieldModule,\n            MatInputModule,\n            ReactiveFormsModule,\n            MatIconModule\n        ]\n    })\n], SwuiSearchModule);\nexport { SwuiSearchModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,IAAIC,gBAAgB,GAAG,MAAMA,gBAAgB,CAAC,EAC7C;AACDA,gBAAgB,GAAGT,UAAU,CAAC,CAC1BI,QAAQ,CAAC;EACLM,YAAY,EAAE,CACVJ,mBAAmB,CACtB;EACDK,OAAO,EAAE,CACLL,mBAAmB,CACtB;EACDM,OAAO,EAAE,CACLX,YAAY,EACZC,WAAW,EACXK,kBAAkB,EAClBC,cAAc,EACdL,mBAAmB,EACnBE,aAAa;AAErB,CAAC,CAAC,CACL,EAAEI,gBAAgB,CAAC;AACpB,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}