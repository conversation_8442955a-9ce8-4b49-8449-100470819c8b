{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _CdkTreeNodeOutlet, _CdkTreeNodeDef, _CdkTree, _CdkTreeNode, _CdkNestedTreeNode, _CdkTreeNodePadding, _CdkTreeNodeToggle, _CdkTreeModule;\nimport { S as SelectionModel } from './selection-model-CeeHVIcP.mjs';\nimport { isObservable, Subject, BehaviorSubject, of, combineLatest, EMPTY, concat } from 'rxjs';\nimport { take, filter, takeUntil, startWith, tap, switchMap, map, reduce, concatMap, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ViewContainerRef, Directive, TemplateRef, IterableDiffers, ChangeDetectorRef, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ViewChild, ContentChildren, EventEmitter, booleanAttribute, Output, numberAttribute, NgModule } from '@angular/core';\nimport { T as TREE_KEY_MANAGER } from './tree-key-manager-KnCoIkIC.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { i as isDataSource } from './data-source-D34wiQZj.mjs';\nimport { coerceObservable } from './coercion/private.mjs';\nimport './typeahead-9ZW4Dtsf.mjs';\nimport './keycodes-CpHkExLC.mjs';\nimport '@angular/common';\n\n/**\n * Base tree control. It has basic toggle/expand/collapse operations on a single data node.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor. To be removed in a future version.\n * @breaking-change 21.0.0\n */\nclass BaseTreeControl {\n  constructor() {\n    /** Saved data node for `expandAll` action. */\n    _defineProperty(this, \"dataNodes\", void 0);\n    /** A selection model with multi-selection to track expansion status. */\n    _defineProperty(this, \"expansionModel\", new SelectionModel(true));\n    /**\n     * Returns the identifier by which a dataNode should be tracked, should its\n     * reference change.\n     *\n     * Similar to trackBy for *ngFor\n     */\n    _defineProperty(this, \"trackBy\", void 0);\n    /** Get depth of a given data node, return the level number. This is for flat tree node. */\n    _defineProperty(this, \"getLevel\", void 0);\n    /**\n     * Whether the data node is expandable. Returns true if expandable.\n     * This is for flat tree node.\n     */\n    _defineProperty(this, \"isExpandable\", void 0);\n    /** Gets a stream that emits whenever the given data node's children change. */\n    _defineProperty(this, \"getChildren\", void 0);\n  }\n  /** Toggles one single data node's expanded/collapsed state. */\n  toggle(dataNode) {\n    this.expansionModel.toggle(this._trackByValue(dataNode));\n  }\n  /** Expands one single data node. */\n  expand(dataNode) {\n    this.expansionModel.select(this._trackByValue(dataNode));\n  }\n  /** Collapses one single data node. */\n  collapse(dataNode) {\n    this.expansionModel.deselect(this._trackByValue(dataNode));\n  }\n  /** Whether a given data node is expanded or not. Returns true if the data node is expanded. */\n  isExpanded(dataNode) {\n    return this.expansionModel.isSelected(this._trackByValue(dataNode));\n  }\n  /** Toggles a subtree rooted at `node` recursively. */\n  toggleDescendants(dataNode) {\n    this.expansionModel.isSelected(this._trackByValue(dataNode)) ? this.collapseDescendants(dataNode) : this.expandDescendants(dataNode);\n  }\n  /** Collapse all dataNodes in the tree. */\n  collapseAll() {\n    this.expansionModel.clear();\n  }\n  /** Expands a subtree rooted at given data node recursively. */\n  expandDescendants(dataNode) {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.select(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n  /** Collapses a subtree rooted at given data node recursively. */\n  collapseDescendants(dataNode) {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.deselect(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n  _trackByValue(value) {\n    return this.trackBy ? this.trackBy(value) : value;\n  }\n}\n\n/**\n * Flat tree control. Able to expand/collapse a subtree recursively for flattened tree.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nclass FlatTreeControl extends BaseTreeControl {\n  /** Construct with flat tree data node functions getLevel and isExpandable. */\n  constructor(getLevel, isExpandable, options) {\n    super();\n    _defineProperty(this, \"getLevel\", void 0);\n    _defineProperty(this, \"isExpandable\", void 0);\n    _defineProperty(this, \"options\", void 0);\n    this.getLevel = getLevel;\n    this.isExpandable = isExpandable;\n    this.options = options;\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n  /**\n   * Gets a list of the data node's subtree of descendent data nodes.\n   *\n   * To make this working, the `dataNodes` of the TreeControl must be flattened tree nodes\n   * with correct levels.\n   */\n  getDescendants(dataNode) {\n    const startIndex = this.dataNodes.indexOf(dataNode);\n    const results = [];\n    // Goes through flattened tree nodes in the `dataNodes` array, and get all descendants.\n    // The level of descendants of a tree node must be greater than the level of the given\n    // tree node.\n    // If we reach a node whose level is equal to the level of the tree node, we hit a sibling.\n    // If we reach a node whose level is greater than the level of the tree node, we hit a\n    // sibling of an ancestor.\n    for (let i = startIndex + 1; i < this.dataNodes.length && this.getLevel(dataNode) < this.getLevel(this.dataNodes[i]); i++) {\n      results.push(this.dataNodes[i]);\n    }\n    return results;\n  }\n  /**\n   * Expands all data nodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all flattened\n   * data nodes of the tree.\n   */\n  expandAll() {\n    this.expansionModel.select(...this.dataNodes.map(node => this._trackByValue(node)));\n  }\n}\n\n/**\n * Nested tree control. Able to expand/collapse a subtree recursively for NestedNode type.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nclass NestedTreeControl extends BaseTreeControl {\n  /** Construct with nested tree function getChildren. */\n  constructor(getChildren, options) {\n    var _this$options;\n    super();\n    _defineProperty(this, \"getChildren\", void 0);\n    _defineProperty(this, \"options\", void 0);\n    this.getChildren = getChildren;\n    this.options = options;\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n    if ((_this$options = this.options) !== null && _this$options !== void 0 && _this$options.isExpandable) {\n      this.isExpandable = this.options.isExpandable;\n    }\n  }\n  /**\n   * Expands all dataNodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all root level\n   * data nodes of the tree.\n   */\n  expandAll() {\n    this.expansionModel.clear();\n    const allNodes = this.dataNodes.reduce((accumulator, dataNode) => [...accumulator, ...this.getDescendants(dataNode), dataNode], []);\n    this.expansionModel.select(...allNodes.map(node => this._trackByValue(node)));\n  }\n  /** Gets a list of descendant dataNodes of a subtree rooted at given data node recursively. */\n  getDescendants(dataNode) {\n    const descendants = [];\n    this._getDescendants(descendants, dataNode);\n    // Remove the node itself\n    return descendants.splice(1);\n  }\n  /** A helper function to get descendants recursively. */\n  _getDescendants(descendants, dataNode) {\n    descendants.push(dataNode);\n    const childrenNodes = this.getChildren(dataNode);\n    if (Array.isArray(childrenNodes)) {\n      childrenNodes.forEach(child => this._getDescendants(descendants, child));\n    } else if (isObservable(childrenNodes)) {\n      // TypeScript as of version 3.5 doesn't seem to treat `Boolean` like a function that\n      // returns a `boolean` specifically in the context of `filter`, so we manually clarify that.\n      childrenNodes.pipe(take(1), filter(Boolean)).subscribe(children => {\n        for (const child of children) {\n          this._getDescendants(descendants, child);\n        }\n      });\n    }\n  }\n}\n\n/**\n * Injection token used to provide a `CdkTreeNode` to its outlet.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst CDK_TREE_NODE_OUTLET_NODE = new InjectionToken('CDK_TREE_NODE_OUTLET_NODE');\n/**\n * Outlet for nested CdkNode. Put `[cdkTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nclass CdkTreeNodeOutlet {\n  constructor() {\n    _defineProperty(this, \"viewContainer\", inject(ViewContainerRef));\n    _defineProperty(this, \"_node\", inject(CDK_TREE_NODE_OUTLET_NODE, {\n      optional: true\n    }));\n  }\n}\n_CdkTreeNodeOutlet = CdkTreeNodeOutlet;\n_defineProperty(CdkTreeNodeOutlet, \"\\u0275fac\", function _CdkTreeNodeOutlet_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTreeNodeOutlet)();\n});\n_defineProperty(CdkTreeNodeOutlet, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkTreeNodeOutlet,\n  selectors: [[\"\", \"cdkTreeNodeOutlet\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeOutlet]'\n    }]\n  }], () => [], null);\n})();\n\n/** Context provided to the tree node component. */\nclass CdkTreeNodeOutletContext {\n  constructor(data) {\n    /** Data for the node. */\n    _defineProperty(this, \"$implicit\", void 0);\n    /** Depth of the node. */\n    _defineProperty(this, \"level\", void 0);\n    /** Index location of the node. */\n    _defineProperty(this, \"index\", void 0);\n    /** Length of the number of total dataNodes. */\n    _defineProperty(this, \"count\", void 0);\n    this.$implicit = data;\n  }\n}\n/**\n * Data node definition for the CdkTree.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nclass CdkTreeNodeDef {\n  constructor() {\n    /** @docs-private */\n    _defineProperty(this, \"template\", inject(TemplateRef));\n    /**\n     * Function that should return true if this node template should be used for the provided node\n     * data and index. If left undefined, this node will be considered the default node template to\n     * use when no other when functions return true for the data.\n     * For every node, there must be at least one when function that passes or an undefined to\n     * default.\n     */\n    _defineProperty(this, \"when\", void 0);\n  }\n}\n_CdkTreeNodeDef = CdkTreeNodeDef;\n_defineProperty(CdkTreeNodeDef, \"\\u0275fac\", function _CdkTreeNodeDef_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTreeNodeDef)();\n});\n_defineProperty(CdkTreeNodeDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkTreeNodeDef,\n  selectors: [[\"\", \"cdkTreeNodeDef\", \"\"]],\n  inputs: {\n    when: [0, \"cdkTreeNodeDefWhen\", \"when\"]\n  }\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeDef]',\n      inputs: [{\n        name: 'when',\n        alias: 'cdkTreeNodeDefWhen'\n      }]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Returns an error to be thrown when there is no usable data.\n * @docs-private\n */\nfunction getTreeNoValidDataSourceError() {\n  return Error(`A valid data source must be provided.`);\n}\n/**\n * Returns an error to be thrown when there are multiple nodes that are missing a when function.\n * @docs-private\n */\nfunction getTreeMultipleDefaultNodeDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching node defs for a particular set of data.\n * @docs-private\n */\nfunction getTreeMissingMatchingNodeDefError() {\n  return Error(`Could not find a matching node definition for the provided node data.`);\n}\n/**\n * Returns an error to be thrown when there is no tree control.\n * @docs-private\n */\nfunction getTreeControlMissingError() {\n  return Error(`Could not find a tree control, levelAccessor, or childrenAccessor for the tree.`);\n}\n/**\n * Returns an error to be thrown when there are multiple ways of specifying children or level\n * provided to the tree.\n * @docs-private\n */\nfunction getMultipleTreeControlsError() {\n  return Error(`More than one of tree control, levelAccessor, or childrenAccessor were provided.`);\n}\n\n/**\n * CDK tree component that connects with a data source to retrieve data of type `T` and renders\n * dataNodes with hierarchy. Updates the dataNodes when new data is provided by the data source.\n */\nclass CdkTree {\n  /**\n   * Provides a stream containing the latest data array to render. Influenced by the tree's\n   * stream of view window (what dataNodes are currently on screen).\n   * Data source can be an observable of data array, or a data array to render.\n   */\n  get dataSource() {\n    return this._dataSource;\n  }\n  set dataSource(dataSource) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  constructor() {\n    _defineProperty(this, \"_differs\", inject(IterableDiffers));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_dir\", inject(Directionality));\n    /** Subject that emits when the component has been destroyed. */\n    _defineProperty(this, \"_onDestroy\", new Subject());\n    /** Differ used to find the changes in the data provided by the data source. */\n    _defineProperty(this, \"_dataDiffer\", void 0);\n    /** Stores the node definition that does not have a when predicate. */\n    _defineProperty(this, \"_defaultNodeDef\", void 0);\n    /** Data subscription */\n    _defineProperty(this, \"_dataSubscription\", void 0);\n    /** Level of nodes */\n    _defineProperty(this, \"_levels\", new Map());\n    /** The immediate parents for a node. This is `null` if there is no parent. */\n    _defineProperty(this, \"_parents\", new Map());\n    /**\n     * Nodes grouped into each set, which is a list of nodes displayed together in the DOM.\n     *\n     * Lookup key is the parent of a set. Root nodes have key of null.\n     *\n     * Values is a 'set' of tree nodes. Each tree node maps to a treeitem element. Sets are in the\n     * order that it is rendered. Each set maps directly to aria-posinset and aria-setsize attributes.\n     */\n    _defineProperty(this, \"_ariaSets\", new Map());\n    _defineProperty(this, \"_dataSource\", void 0);\n    /**\n     * The tree controller\n     *\n     * @deprecated Use one of `levelAccessor` or `childrenAccessor` instead. To be removed in a\n     * future version.\n     * @breaking-change 21.0.0\n     */\n    _defineProperty(this, \"treeControl\", void 0);\n    /**\n     * Given a data node, determines what tree level the node is at.\n     *\n     * One of levelAccessor or childrenAccessor must be specified, not both.\n     * This is enforced at run-time.\n     */\n    _defineProperty(this, \"levelAccessor\", void 0);\n    /**\n     * Given a data node, determines what the children of that node are.\n     *\n     * One of levelAccessor or childrenAccessor must be specified, not both.\n     * This is enforced at run-time.\n     */\n    _defineProperty(this, \"childrenAccessor\", void 0);\n    /**\n     * Tracking function that will be used to check the differences in data changes. Used similarly\n     * to `ngFor` `trackBy` function. Optimize node operations by identifying a node based on its data\n     * relative to the function to know if a node should be added/removed/moved.\n     * Accepts a function that takes two parameters, `index` and `item`.\n     */\n    _defineProperty(this, \"trackBy\", void 0);\n    /**\n     * Given a data node, determines the key by which we determine whether or not this node is expanded.\n     */\n    _defineProperty(this, \"expansionKey\", void 0);\n    // Outlets within the tree's template where the dataNodes will be inserted.\n    _defineProperty(this, \"_nodeOutlet\", void 0);\n    /** The tree node template for the tree */\n    _defineProperty(this, \"_nodeDefs\", void 0);\n    // TODO(tinayuangao): Setup a listener for scrolling, emit the calculated view to viewChange.\n    //     Remove the MAX_VALUE in viewChange\n    /**\n     * Stream containing the latest information on what rows are being displayed on screen.\n     * Can be used by the data source to as a heuristic of what data should be provided.\n     */\n    _defineProperty(this, \"viewChange\", new BehaviorSubject({\n      start: 0,\n      end: Number.MAX_VALUE\n    }));\n    /** Keep track of which nodes are expanded. */\n    _defineProperty(this, \"_expansionModel\", void 0);\n    /**\n     * Maintain a synchronous cache of flattened data nodes. This will only be\n     * populated after initial render, and in certain cases, will be delayed due to\n     * relying on Observable `getChildren` calls.\n     */\n    _defineProperty(this, \"_flattenedNodes\", new BehaviorSubject([]));\n    /** The automatically determined node type for the tree. */\n    _defineProperty(this, \"_nodeType\", new BehaviorSubject(null));\n    /** The mapping between data and the node that is rendered. */\n    _defineProperty(this, \"_nodes\", new BehaviorSubject(new Map()));\n    /**\n     * Synchronous cache of nodes for the `TreeKeyManager`. This is separate\n     * from `_flattenedNodes` so they can be independently updated at different\n     * times.\n     */\n    _defineProperty(this, \"_keyManagerNodes\", new BehaviorSubject([]));\n    _defineProperty(this, \"_keyManagerFactory\", inject(TREE_KEY_MANAGER));\n    /** The key manager for this tree. Handles focus and activation based on user keyboard input. */\n    _defineProperty(this, \"_keyManager\", void 0);\n    _defineProperty(this, \"_viewInit\", false);\n  }\n  ngAfterContentInit() {\n    this._initializeKeyManager();\n  }\n  ngAfterContentChecked() {\n    this._updateDefaultNodeDefinition();\n    this._subscribeToDataChanges();\n  }\n  ngOnDestroy() {\n    var _this$_keyManager;\n    this._nodeOutlet.viewContainer.clear();\n    this.viewChange.complete();\n    this._onDestroy.next();\n    this._onDestroy.complete();\n    if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n      this.dataSource.disconnect(this);\n    }\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n    // In certain tests, the tree might be destroyed before this is initialized\n    // in `ngAfterContentInit`.\n    (_this$_keyManager = this._keyManager) === null || _this$_keyManager === void 0 || _this$_keyManager.destroy();\n  }\n  ngOnInit() {\n    this._checkTreeControlUsage();\n    this._initializeDataDiffer();\n  }\n  ngAfterViewInit() {\n    this._viewInit = true;\n  }\n  _updateDefaultNodeDefinition() {\n    const defaultNodeDefs = this._nodeDefs.filter(def => !def.when);\n    if (defaultNodeDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMultipleDefaultNodeDefsError();\n    }\n    this._defaultNodeDef = defaultNodeDefs[0];\n  }\n  /**\n   * Sets the node type for the tree, if it hasn't been set yet.\n   *\n   * This will be called by the first node that's rendered in order for the tree\n   * to determine what data transformations are required.\n   */\n  _setNodeTypeIfUnset(newType) {\n    const currentType = this._nodeType.value;\n    if (currentType === null) {\n      this._nodeType.next(newType);\n    } else if ((typeof ngDevMode === 'undefined' || ngDevMode) && currentType !== newType) {\n      console.warn(`Tree is using conflicting node types which can cause unexpected behavior. ` + `Please use tree nodes of the same type (e.g. only flat or only nested). ` + `Current node type: \"${currentType}\", new node type \"${newType}\".`);\n    }\n  }\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the node outlet. Otherwise start listening for new data.\n   */\n  _switchDataSource(dataSource) {\n    if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n      this.dataSource.disconnect(this);\n    }\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n    // Remove the all dataNodes if there is now no data source\n    if (!dataSource) {\n      this._nodeOutlet.viewContainer.clear();\n    }\n    this._dataSource = dataSource;\n    if (this._nodeDefs) {\n      this._subscribeToDataChanges();\n    }\n  }\n  _getExpansionModel() {\n    if (!this.treeControl) {\n      var _this$_expansionModel;\n      (_this$_expansionModel = this._expansionModel) !== null && _this$_expansionModel !== void 0 ? _this$_expansionModel : this._expansionModel = new SelectionModel(true);\n      return this._expansionModel;\n    }\n    return this.treeControl.expansionModel;\n  }\n  /** Set up a subscription for the data provided by the data source. */\n  _subscribeToDataChanges() {\n    if (this._dataSubscription) {\n      return;\n    }\n    let dataStream;\n    if (isDataSource(this._dataSource)) {\n      dataStream = this._dataSource.connect(this);\n    } else if (isObservable(this._dataSource)) {\n      dataStream = this._dataSource;\n    } else if (Array.isArray(this._dataSource)) {\n      dataStream = of(this._dataSource);\n    }\n    if (!dataStream) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        throw getTreeNoValidDataSourceError();\n      }\n      return;\n    }\n    this._dataSubscription = this._getRenderData(dataStream).pipe(takeUntil(this._onDestroy)).subscribe(renderingData => {\n      this._renderDataChanges(renderingData);\n    });\n  }\n  /** Given an Observable containing a stream of the raw data, returns an Observable containing the RenderingData */\n  _getRenderData(dataStream) {\n    const expansionModel = this._getExpansionModel();\n    return combineLatest([dataStream, this._nodeType,\n    // We don't use the expansion data directly, however we add it here to essentially\n    // trigger data rendering when expansion changes occur.\n    expansionModel.changed.pipe(startWith(null), tap(expansionChanges => {\n      this._emitExpansionChanges(expansionChanges);\n    }))]).pipe(switchMap(([data, nodeType]) => {\n      if (nodeType === null) {\n        return of({\n          renderNodes: data,\n          flattenedNodes: null,\n          nodeType\n        });\n      }\n      // If we're here, then we know what our node type is, and therefore can\n      // perform our usual rendering pipeline, which necessitates converting the data\n      return this._computeRenderingData(data, nodeType).pipe(map(convertedData => _objectSpread(_objectSpread({}, convertedData), {}, {\n        nodeType\n      })));\n    }));\n  }\n  _renderDataChanges(data) {\n    if (data.nodeType === null) {\n      this.renderNodeChanges(data.renderNodes);\n      return;\n    }\n    // If we're here, then we know what our node type is, and therefore can\n    // perform our usual rendering pipeline.\n    this._updateCachedData(data.flattenedNodes);\n    this.renderNodeChanges(data.renderNodes);\n    this._updateKeyManagerItems(data.flattenedNodes);\n  }\n  _emitExpansionChanges(expansionChanges) {\n    if (!expansionChanges) {\n      return;\n    }\n    const nodes = this._nodes.value;\n    for (const added of expansionChanges.added) {\n      const node = nodes.get(added);\n      node === null || node === void 0 || node._emitExpansionState(true);\n    }\n    for (const removed of expansionChanges.removed) {\n      const node = nodes.get(removed);\n      node === null || node === void 0 || node._emitExpansionState(false);\n    }\n  }\n  _initializeKeyManager() {\n    const items = combineLatest([this._keyManagerNodes, this._nodes]).pipe(map(([keyManagerNodes, renderNodes]) => keyManagerNodes.reduce((items, data) => {\n      const node = renderNodes.get(this._getExpansionKey(data));\n      if (node) {\n        items.push(node);\n      }\n      return items;\n    }, [])));\n    const keyManagerOptions = {\n      trackBy: node => this._getExpansionKey(node.data),\n      skipPredicate: node => !!node.isDisabled,\n      typeAheadDebounceInterval: true,\n      horizontalOrientation: this._dir.value\n    };\n    this._keyManager = this._keyManagerFactory(items, keyManagerOptions);\n  }\n  _initializeDataDiffer() {\n    var _this$trackBy;\n    // Provide a default trackBy based on `_getExpansionKey` if one isn't provided.\n    const trackBy = (_this$trackBy = this.trackBy) !== null && _this$trackBy !== void 0 ? _this$trackBy : (_index, item) => this._getExpansionKey(item);\n    this._dataDiffer = this._differs.find([]).create(trackBy);\n  }\n  _checkTreeControlUsage() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Verify that Tree follows API contract of using one of TreeControl, levelAccessor or\n      // childrenAccessor. Throw an appropriate error if contract is not met.\n      let numTreeControls = 0;\n      if (this.treeControl) {\n        numTreeControls++;\n      }\n      if (this.levelAccessor) {\n        numTreeControls++;\n      }\n      if (this.childrenAccessor) {\n        numTreeControls++;\n      }\n      if (!numTreeControls) {\n        throw getTreeControlMissingError();\n      } else if (numTreeControls > 1) {\n        throw getMultipleTreeControlsError();\n      }\n    }\n  }\n  /** Check for changes made in the data and render each change (node added/removed/moved). */\n  renderNodeChanges(data, dataDiffer = this._dataDiffer, viewContainer = this._nodeOutlet.viewContainer, parentData) {\n    const changes = dataDiffer.diff(data);\n    // Some tree consumers expect change detection to propagate to nodes\n    // even when the array itself hasn't changed; we explicitly detect changes\n    // anyways in order for nodes to update their data.\n    //\n    // However, if change detection is called while the component's view is\n    // still initing, then the order of child views initing will be incorrect;\n    // to prevent this, we only exit early if the view hasn't initialized yet.\n    if (!changes && !this._viewInit) {\n      return;\n    }\n    changes === null || changes === void 0 || changes.forEachOperation((item, adjustedPreviousIndex, currentIndex) => {\n      if (item.previousIndex == null) {\n        this.insertNode(data[currentIndex], currentIndex, viewContainer, parentData);\n      } else if (currentIndex == null) {\n        viewContainer.remove(adjustedPreviousIndex);\n      } else {\n        const view = viewContainer.get(adjustedPreviousIndex);\n        viewContainer.move(view, currentIndex);\n      }\n    });\n    // If the data itself changes, but keeps the same trackBy, we need to update the templates'\n    // context to reflect the new object.\n    changes === null || changes === void 0 || changes.forEachIdentityChange(record => {\n      const newData = record.item;\n      if (record.currentIndex != undefined) {\n        const view = viewContainer.get(record.currentIndex);\n        view.context.$implicit = newData;\n      }\n    });\n    // Note: we only `detectChanges` from a top-level call, otherwise we risk overflowing\n    // the call stack since this method is called recursively (see #29733.)\n    // TODO: change to `this._changeDetectorRef.markForCheck()`,\n    // or just switch this component to use signals.\n    if (parentData) {\n      this._changeDetectorRef.markForCheck();\n    } else {\n      this._changeDetectorRef.detectChanges();\n    }\n  }\n  /**\n   * Finds the matching node definition that should be used for this node data. If there is only\n   * one node definition, it is returned. Otherwise, find the node definition that has a when\n   * predicate that returns true with the data. If none return true, return the default node\n   * definition.\n   */\n  _getNodeDef(data, i) {\n    if (this._nodeDefs.length === 1) {\n      return this._nodeDefs.first;\n    }\n    const nodeDef = this._nodeDefs.find(def => def.when && def.when(i, data)) || this._defaultNodeDef;\n    if (!nodeDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMissingMatchingNodeDefError();\n    }\n    return nodeDef;\n  }\n  /**\n   * Create the embedded view for the data node template and place it in the correct index location\n   * within the data node view container.\n   */\n  insertNode(nodeData, index, viewContainer, parentData) {\n    var _this$_parents$get;\n    const levelAccessor = this._getLevelAccessor();\n    const node = this._getNodeDef(nodeData, index);\n    const key = this._getExpansionKey(nodeData);\n    // Node context that will be provided to created embedded view\n    const context = new CdkTreeNodeOutletContext(nodeData);\n    parentData !== null && parentData !== void 0 ? parentData : parentData = (_this$_parents$get = this._parents.get(key)) !== null && _this$_parents$get !== void 0 ? _this$_parents$get : undefined;\n    // If the tree is flat tree, then use the `getLevel` function in flat tree control\n    // Otherwise, use the level of parent node.\n    if (levelAccessor) {\n      context.level = levelAccessor(nodeData);\n    } else if (parentData !== undefined && this._levels.has(this._getExpansionKey(parentData))) {\n      context.level = this._levels.get(this._getExpansionKey(parentData)) + 1;\n    } else {\n      context.level = 0;\n    }\n    this._levels.set(key, context.level);\n    // Use default tree nodeOutlet, or nested node's nodeOutlet\n    const container = viewContainer ? viewContainer : this._nodeOutlet.viewContainer;\n    container.createEmbeddedView(node.template, context, index);\n    // Set the data to just created `CdkTreeNode`.\n    // The `CdkTreeNode` created from `createEmbeddedView` will be saved in static variable\n    //     `mostRecentTreeNode`. We get it from static variable and pass the node data to it.\n    if (CdkTreeNode.mostRecentTreeNode) {\n      CdkTreeNode.mostRecentTreeNode.data = nodeData;\n    }\n  }\n  /** Whether the data node is expanded or collapsed. Returns true if it's expanded. */\n  isExpanded(dataNode) {\n    var _this$treeControl, _this$_expansionModel2;\n    return !!((_this$treeControl = this.treeControl) !== null && _this$treeControl !== void 0 && _this$treeControl.isExpanded(dataNode) || (_this$_expansionModel2 = this._expansionModel) !== null && _this$_expansionModel2 !== void 0 && _this$_expansionModel2.isSelected(this._getExpansionKey(dataNode)));\n  }\n  /** If the data node is currently expanded, collapse it. Otherwise, expand it. */\n  toggle(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.toggle(dataNode);\n    } else if (this._expansionModel) {\n      this._expansionModel.toggle(this._getExpansionKey(dataNode));\n    }\n  }\n  /** Expand the data node. If it is already expanded, does nothing. */\n  expand(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.expand(dataNode);\n    } else if (this._expansionModel) {\n      this._expansionModel.select(this._getExpansionKey(dataNode));\n    }\n  }\n  /** Collapse the data node. If it is already collapsed, does nothing. */\n  collapse(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.collapse(dataNode);\n    } else if (this._expansionModel) {\n      this._expansionModel.deselect(this._getExpansionKey(dataNode));\n    }\n  }\n  /**\n   * If the data node is currently expanded, collapse it and all its descendants.\n   * Otherwise, expand it and all its descendants.\n   */\n  toggleDescendants(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.toggleDescendants(dataNode);\n    } else if (this._expansionModel) {\n      if (this.isExpanded(dataNode)) {\n        this.collapseDescendants(dataNode);\n      } else {\n        this.expandDescendants(dataNode);\n      }\n    }\n  }\n  /**\n   * Expand the data node and all its descendants. If they are already expanded, does nothing.\n   */\n  expandDescendants(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.expandDescendants(dataNode);\n    } else if (this._expansionModel) {\n      const expansionModel = this._expansionModel;\n      expansionModel.select(this._getExpansionKey(dataNode));\n      this._getDescendants(dataNode).pipe(take(1), takeUntil(this._onDestroy)).subscribe(children => {\n        expansionModel.select(...children.map(child => this._getExpansionKey(child)));\n      });\n    }\n  }\n  /** Collapse the data node and all its descendants. If it is already collapsed, does nothing. */\n  collapseDescendants(dataNode) {\n    if (this.treeControl) {\n      this.treeControl.collapseDescendants(dataNode);\n    } else if (this._expansionModel) {\n      const expansionModel = this._expansionModel;\n      expansionModel.deselect(this._getExpansionKey(dataNode));\n      this._getDescendants(dataNode).pipe(take(1), takeUntil(this._onDestroy)).subscribe(children => {\n        expansionModel.deselect(...children.map(child => this._getExpansionKey(child)));\n      });\n    }\n  }\n  /** Expands all data nodes in the tree. */\n  expandAll() {\n    if (this.treeControl) {\n      this.treeControl.expandAll();\n    } else if (this._expansionModel) {\n      this._forEachExpansionKey(keys => {\n        var _this$_expansionModel3;\n        return (_this$_expansionModel3 = this._expansionModel) === null || _this$_expansionModel3 === void 0 ? void 0 : _this$_expansionModel3.select(...keys);\n      });\n    }\n  }\n  /** Collapse all data nodes in the tree. */\n  collapseAll() {\n    if (this.treeControl) {\n      this.treeControl.collapseAll();\n    } else if (this._expansionModel) {\n      this._forEachExpansionKey(keys => {\n        var _this$_expansionModel4;\n        return (_this$_expansionModel4 = this._expansionModel) === null || _this$_expansionModel4 === void 0 ? void 0 : _this$_expansionModel4.deselect(...keys);\n      });\n    }\n  }\n  /** Level accessor, used for compatibility between the old Tree and new Tree */\n  _getLevelAccessor() {\n    var _this$treeControl$get, _this$treeControl2;\n    return (_this$treeControl$get = (_this$treeControl2 = this.treeControl) === null || _this$treeControl2 === void 0 || (_this$treeControl2 = _this$treeControl2.getLevel) === null || _this$treeControl2 === void 0 ? void 0 : _this$treeControl2.bind(this.treeControl)) !== null && _this$treeControl$get !== void 0 ? _this$treeControl$get : this.levelAccessor;\n  }\n  /** Children accessor, used for compatibility between the old Tree and new Tree */\n  _getChildrenAccessor() {\n    var _this$treeControl$get2, _this$treeControl3;\n    return (_this$treeControl$get2 = (_this$treeControl3 = this.treeControl) === null || _this$treeControl3 === void 0 || (_this$treeControl3 = _this$treeControl3.getChildren) === null || _this$treeControl3 === void 0 ? void 0 : _this$treeControl3.bind(this.treeControl)) !== null && _this$treeControl$get2 !== void 0 ? _this$treeControl$get2 : this.childrenAccessor;\n  }\n  /**\n   * Gets the direct children of a node; used for compatibility between the old tree and the\n   * new tree.\n   */\n  _getDirectChildren(dataNode) {\n    var _this$_expansionModel5, _this$treeControl4;\n    const levelAccessor = this._getLevelAccessor();\n    const expansionModel = (_this$_expansionModel5 = this._expansionModel) !== null && _this$_expansionModel5 !== void 0 ? _this$_expansionModel5 : (_this$treeControl4 = this.treeControl) === null || _this$treeControl4 === void 0 ? void 0 : _this$treeControl4.expansionModel;\n    if (!expansionModel) {\n      return of([]);\n    }\n    const key = this._getExpansionKey(dataNode);\n    const isExpanded = expansionModel.changed.pipe(switchMap(changes => {\n      if (changes.added.includes(key)) {\n        return of(true);\n      } else if (changes.removed.includes(key)) {\n        return of(false);\n      }\n      return EMPTY;\n    }), startWith(this.isExpanded(dataNode)));\n    if (levelAccessor) {\n      return combineLatest([isExpanded, this._flattenedNodes]).pipe(map(([expanded, flattenedNodes]) => {\n        if (!expanded) {\n          return [];\n        }\n        return this._findChildrenByLevel(levelAccessor, flattenedNodes, dataNode, 1);\n      }));\n    }\n    const childrenAccessor = this._getChildrenAccessor();\n    if (childrenAccessor) {\n      var _childrenAccessor;\n      return coerceObservable((_childrenAccessor = childrenAccessor(dataNode)) !== null && _childrenAccessor !== void 0 ? _childrenAccessor : []);\n    }\n    throw getTreeControlMissingError();\n  }\n  /**\n   * Given the list of flattened nodes, the level accessor, and the level range within\n   * which to consider children, finds the children for a given node.\n   *\n   * For example, for direct children, `levelDelta` would be 1. For all descendants,\n   * `levelDelta` would be Infinity.\n   */\n  _findChildrenByLevel(levelAccessor, flattenedNodes, dataNode, levelDelta) {\n    const key = this._getExpansionKey(dataNode);\n    const startIndex = flattenedNodes.findIndex(node => this._getExpansionKey(node) === key);\n    const dataNodeLevel = levelAccessor(dataNode);\n    const expectedLevel = dataNodeLevel + levelDelta;\n    const results = [];\n    // Goes through flattened tree nodes in the `flattenedNodes` array, and get all\n    // descendants within a certain level range.\n    //\n    // If we reach a node whose level is equal to or less than the level of the tree node,\n    // we hit a sibling or parent's sibling, and should stop.\n    for (let i = startIndex + 1; i < flattenedNodes.length; i++) {\n      const currentLevel = levelAccessor(flattenedNodes[i]);\n      if (currentLevel <= dataNodeLevel) {\n        break;\n      }\n      if (currentLevel <= expectedLevel) {\n        results.push(flattenedNodes[i]);\n      }\n    }\n    return results;\n  }\n  /**\n   * Adds the specified node component to the tree's internal registry.\n   *\n   * This primarily facilitates keyboard navigation.\n   */\n  _registerNode(node) {\n    this._nodes.value.set(this._getExpansionKey(node.data), node);\n    this._nodes.next(this._nodes.value);\n  }\n  /** Removes the specified node component from the tree's internal registry. */\n  _unregisterNode(node) {\n    this._nodes.value.delete(this._getExpansionKey(node.data));\n    this._nodes.next(this._nodes.value);\n  }\n  /**\n   * For the given node, determine the level where this node appears in the tree.\n   *\n   * This is intended to be used for `aria-level` but is 0-indexed.\n   */\n  _getLevel(node) {\n    return this._levels.get(this._getExpansionKey(node));\n  }\n  /**\n   * For the given node, determine the size of the parent's child set.\n   *\n   * This is intended to be used for `aria-setsize`.\n   */\n  _getSetSize(dataNode) {\n    const set = this._getAriaSet(dataNode);\n    return set.length;\n  }\n  /**\n   * For the given node, determine the index (starting from 1) of the node in its parent's child set.\n   *\n   * This is intended to be used for `aria-posinset`.\n   */\n  _getPositionInSet(dataNode) {\n    const set = this._getAriaSet(dataNode);\n    const key = this._getExpansionKey(dataNode);\n    return set.findIndex(node => this._getExpansionKey(node) === key) + 1;\n  }\n  /** Given a CdkTreeNode, gets the node that renders that node's parent's data. */\n  _getNodeParent(node) {\n    const parent = this._parents.get(this._getExpansionKey(node.data));\n    return parent && this._nodes.value.get(this._getExpansionKey(parent));\n  }\n  /** Given a CdkTreeNode, gets the nodes that renders that node's child data. */\n  _getNodeChildren(node) {\n    return this._getDirectChildren(node.data).pipe(map(children => children.reduce((nodes, child) => {\n      const value = this._nodes.value.get(this._getExpansionKey(child));\n      if (value) {\n        nodes.push(value);\n      }\n      return nodes;\n    }, [])));\n  }\n  /** `keydown` event handler; this just passes the event to the `TreeKeyManager`. */\n  _sendKeydownToKeyManager(event) {\n    // Only handle events directly on the tree or directly on one of the nodes, otherwise\n    // we risk interfering with events in the projected content (see #29828).\n    if (event.target === this._elementRef.nativeElement) {\n      this._keyManager.onKeydown(event);\n    } else {\n      const nodes = this._nodes.getValue();\n      for (const [, node] of nodes) {\n        if (event.target === node._elementRef.nativeElement) {\n          this._keyManager.onKeydown(event);\n          break;\n        }\n      }\n    }\n  }\n  /** Gets all nested descendants of a given node. */\n  _getDescendants(dataNode) {\n    if (this.treeControl) {\n      return of(this.treeControl.getDescendants(dataNode));\n    }\n    if (this.levelAccessor) {\n      const results = this._findChildrenByLevel(this.levelAccessor, this._flattenedNodes.value, dataNode, Infinity);\n      return of(results);\n    }\n    if (this.childrenAccessor) {\n      return this._getAllChildrenRecursively(dataNode).pipe(reduce((allChildren, nextChildren) => {\n        allChildren.push(...nextChildren);\n        return allChildren;\n      }, []));\n    }\n    throw getTreeControlMissingError();\n  }\n  /**\n   * Gets all children and sub-children of the provided node.\n   *\n   * This will emit multiple times, in the order that the children will appear\n   * in the tree, and can be combined with a `reduce` operator.\n   */\n  _getAllChildrenRecursively(dataNode) {\n    if (!this.childrenAccessor) {\n      return of([]);\n    }\n    return coerceObservable(this.childrenAccessor(dataNode)).pipe(take(1), switchMap(children => {\n      // Here, we cache the parents of a particular child so that we can compute the levels.\n      for (const child of children) {\n        this._parents.set(this._getExpansionKey(child), dataNode);\n      }\n      return of(...children).pipe(concatMap(child => concat(of([child]), this._getAllChildrenRecursively(child))));\n    }));\n  }\n  _getExpansionKey(dataNode) {\n    var _this$expansionKey, _this$expansionKey2;\n    // In the case that a key accessor function was not provided by the\n    // tree user, we'll default to using the node object itself as the key.\n    //\n    // This cast is safe since:\n    // - if an expansionKey is provided, TS will infer the type of K to be\n    //   the return type.\n    // - if it's not, then K will be defaulted to T.\n    return (_this$expansionKey = (_this$expansionKey2 = this.expansionKey) === null || _this$expansionKey2 === void 0 ? void 0 : _this$expansionKey2.call(this, dataNode)) !== null && _this$expansionKey !== void 0 ? _this$expansionKey : dataNode;\n  }\n  _getAriaSet(node) {\n    const key = this._getExpansionKey(node);\n    const parent = this._parents.get(key);\n    const parentKey = parent ? this._getExpansionKey(parent) : null;\n    const set = this._ariaSets.get(parentKey);\n    return set !== null && set !== void 0 ? set : [node];\n  }\n  /**\n   * Finds the parent for the given node. If this is a root node, this\n   * returns null. If we're unable to determine the parent, for example,\n   * if we don't have cached node data, this returns undefined.\n   */\n  _findParentForNode(node, index, cachedNodes) {\n    var _this$_levels$get;\n    // In all cases, we have a mapping from node to level; all we need to do here is backtrack in\n    // our flattened list of nodes to determine the first node that's of a level lower than the\n    // provided node.\n    if (!cachedNodes.length) {\n      return null;\n    }\n    const currentLevel = (_this$_levels$get = this._levels.get(this._getExpansionKey(node))) !== null && _this$_levels$get !== void 0 ? _this$_levels$get : 0;\n    for (let parentIndex = index - 1; parentIndex >= 0; parentIndex--) {\n      var _this$_levels$get2;\n      const parentNode = cachedNodes[parentIndex];\n      const parentLevel = (_this$_levels$get2 = this._levels.get(this._getExpansionKey(parentNode))) !== null && _this$_levels$get2 !== void 0 ? _this$_levels$get2 : 0;\n      if (parentLevel < currentLevel) {\n        return parentNode;\n      }\n    }\n    return null;\n  }\n  /**\n   * Given a set of root nodes and the current node level, flattens any nested\n   * nodes into a single array.\n   *\n   * If any nodes are not expanded, then their children will not be added into the array.\n   * This will still traverse all nested children in order to build up our internal data\n   * models, but will not include them in the returned array.\n   */\n  _flattenNestedNodesWithExpansion(nodes, level = 0) {\n    const childrenAccessor = this._getChildrenAccessor();\n    // If we're using a level accessor, we don't need to flatten anything.\n    if (!childrenAccessor) {\n      return of([...nodes]);\n    }\n    return of(...nodes).pipe(concatMap(node => {\n      const parentKey = this._getExpansionKey(node);\n      if (!this._parents.has(parentKey)) {\n        this._parents.set(parentKey, null);\n      }\n      this._levels.set(parentKey, level);\n      const children = coerceObservable(childrenAccessor(node));\n      return concat(of([node]), children.pipe(take(1), tap(childNodes => {\n        this._ariaSets.set(parentKey, [...(childNodes !== null && childNodes !== void 0 ? childNodes : [])]);\n        for (const child of childNodes !== null && childNodes !== void 0 ? childNodes : []) {\n          const childKey = this._getExpansionKey(child);\n          this._parents.set(childKey, node);\n          this._levels.set(childKey, level + 1);\n        }\n      }), switchMap(childNodes => {\n        if (!childNodes) {\n          return of([]);\n        }\n        return this._flattenNestedNodesWithExpansion(childNodes, level + 1).pipe(map(nestedNodes => this.isExpanded(node) ? nestedNodes : []));\n      })));\n    }), reduce((results, children) => {\n      results.push(...children);\n      return results;\n    }, []));\n  }\n  /**\n   * Converts children for certain tree configurations.\n   *\n   * This also computes parent, level, and group data.\n   */\n  _computeRenderingData(nodes, nodeType) {\n    // The only situations where we have to convert children types is when\n    // they're mismatched; i.e. if the tree is using a childrenAccessor and the\n    // nodes are flat, or if the tree is using a levelAccessor and the nodes are\n    // nested.\n    if (this.childrenAccessor && nodeType === 'flat') {\n      // clear previously generated data so we don't keep end up retaining data overtime causing\n      // memory leaks.\n      this._clearPreviousCache();\n      // This flattens children into a single array.\n      this._ariaSets.set(null, [...nodes]);\n      return this._flattenNestedNodesWithExpansion(nodes).pipe(map(flattenedNodes => ({\n        renderNodes: flattenedNodes,\n        flattenedNodes\n      })));\n    } else if (this.levelAccessor && nodeType === 'nested') {\n      // In the nested case, we only look for root nodes. The CdkNestedNode\n      // itself will handle rendering each individual node's children.\n      const levelAccessor = this.levelAccessor;\n      return of(nodes.filter(node => levelAccessor(node) === 0)).pipe(map(rootNodes => ({\n        renderNodes: rootNodes,\n        flattenedNodes: nodes\n      })), tap(({\n        flattenedNodes\n      }) => {\n        this._calculateParents(flattenedNodes);\n      }));\n    } else if (nodeType === 'flat') {\n      // In the case of a TreeControl, we know that the node type matches up\n      // with the TreeControl, and so no conversions are necessary. Otherwise,\n      // we've already confirmed that the data model matches up with the\n      // desired node type here.\n      return of({\n        renderNodes: nodes,\n        flattenedNodes: nodes\n      }).pipe(tap(({\n        flattenedNodes\n      }) => {\n        this._calculateParents(flattenedNodes);\n      }));\n    } else {\n      // clear previously generated data so we don't keep end up retaining data overtime causing\n      // memory leaks.\n      this._clearPreviousCache();\n      // For nested nodes, we still need to perform the node flattening in order\n      // to maintain our caches for various tree operations.\n      this._ariaSets.set(null, [...nodes]);\n      return this._flattenNestedNodesWithExpansion(nodes).pipe(map(flattenedNodes => ({\n        renderNodes: nodes,\n        flattenedNodes\n      })));\n    }\n  }\n  _updateCachedData(flattenedNodes) {\n    this._flattenedNodes.next(flattenedNodes);\n  }\n  _updateKeyManagerItems(flattenedNodes) {\n    this._keyManagerNodes.next(flattenedNodes);\n  }\n  /** Traverse the flattened node data and compute parents, levels, and group data. */\n  _calculateParents(flattenedNodes) {\n    const levelAccessor = this._getLevelAccessor();\n    if (!levelAccessor) {\n      return;\n    }\n    // clear previously generated data so we don't keep end up retaining data overtime causing\n    // memory leaks.\n    this._clearPreviousCache();\n    for (let index = 0; index < flattenedNodes.length; index++) {\n      var _this$_ariaSets$get;\n      const dataNode = flattenedNodes[index];\n      const key = this._getExpansionKey(dataNode);\n      this._levels.set(key, levelAccessor(dataNode));\n      const parent = this._findParentForNode(dataNode, index, flattenedNodes);\n      this._parents.set(key, parent);\n      const parentKey = parent ? this._getExpansionKey(parent) : null;\n      const group = (_this$_ariaSets$get = this._ariaSets.get(parentKey)) !== null && _this$_ariaSets$get !== void 0 ? _this$_ariaSets$get : [];\n      group.splice(index, 0, dataNode);\n      this._ariaSets.set(parentKey, group);\n    }\n  }\n  /** Invokes a callback with all node expansion keys. */\n  _forEachExpansionKey(callback) {\n    const toToggle = [];\n    const observables = [];\n    this._nodes.value.forEach(node => {\n      toToggle.push(this._getExpansionKey(node.data));\n      observables.push(this._getDescendants(node.data));\n    });\n    if (observables.length > 0) {\n      combineLatest(observables).pipe(take(1), takeUntil(this._onDestroy)).subscribe(results => {\n        results.forEach(inner => inner.forEach(r => toToggle.push(this._getExpansionKey(r))));\n        callback(toToggle);\n      });\n    } else {\n      callback(toToggle);\n    }\n  }\n  /** Clears the maps we use to store parents, level & aria-sets in. */\n  _clearPreviousCache() {\n    this._parents.clear();\n    this._levels.clear();\n    this._ariaSets.clear();\n  }\n}\n_CdkTree = CdkTree;\n_defineProperty(CdkTree, \"\\u0275fac\", function _CdkTree_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTree)();\n});\n_defineProperty(CdkTree, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _CdkTree,\n  selectors: [[\"cdk-tree\"]],\n  contentQueries: function _CdkTree_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, CdkTreeNodeDef, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeDefs = _t);\n    }\n  },\n  viewQuery: function _CdkTree_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(CdkTreeNodeOutlet, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeOutlet = _t.first);\n    }\n  },\n  hostAttrs: [\"role\", \"tree\", 1, \"cdk-tree\"],\n  hostBindings: function _CdkTree_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function _CdkTree_keydown_HostBindingHandler($event) {\n        return ctx._sendKeydownToKeyManager($event);\n      });\n    }\n  },\n  inputs: {\n    dataSource: \"dataSource\",\n    treeControl: \"treeControl\",\n    levelAccessor: \"levelAccessor\",\n    childrenAccessor: \"childrenAccessor\",\n    trackBy: \"trackBy\",\n    expansionKey: \"expansionKey\"\n  },\n  exportAs: [\"cdkTree\"],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkTreeNodeOutlet\", \"\"]],\n  template: function _CdkTree_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [CdkTreeNodeOutlet],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTree, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-tree',\n      exportAs: 'cdkTree',\n      template: `<ng-container cdkTreeNodeOutlet></ng-container>`,\n      host: {\n        'class': 'cdk-tree',\n        'role': 'tree',\n        '(keydown)': '_sendKeydownToKeyManager($event)'\n      },\n      encapsulation: ViewEncapsulation.None,\n      // The \"OnPush\" status for the `CdkTree` component is effectively a noop, so we are removing it.\n      // The view for `CdkTree` consists entirely of templates declared in other views. As they are\n      // declared elsewhere, they are checked when their declaration points are checked.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkTreeNodeOutlet]\n    }]\n  }], () => [], {\n    dataSource: [{\n      type: Input\n    }],\n    treeControl: [{\n      type: Input\n    }],\n    levelAccessor: [{\n      type: Input\n    }],\n    childrenAccessor: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    expansionKey: [{\n      type: Input\n    }],\n    _nodeOutlet: [{\n      type: ViewChild,\n      args: [CdkTreeNodeOutlet, {\n        static: true\n      }]\n    }],\n    _nodeDefs: [{\n      type: ContentChildren,\n      args: [CdkTreeNodeDef, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * Tree node for CdkTree. It contains the data in the tree node.\n */\nclass CdkTreeNode {\n  /**\n   * The role of the tree node.\n   *\n   * @deprecated This will be ignored; the tree will automatically determine the appropriate role\n   * for tree node. This input will be removed in a future version.\n   * @breaking-change 21.0.0\n   */\n  get role() {\n    return 'treeitem';\n  }\n  set role(_role) {\n    // ignore any role setting, we handle this internally.\n  }\n  /**\n   * Whether or not this node is expandable.\n   *\n   * If not using `FlatTreeControl`, or if `isExpandable` is not provided to\n   * `NestedTreeControl`, this should be provided for correct node a11y.\n   */\n  get isExpandable() {\n    return this._isExpandable();\n  }\n  set isExpandable(isExpandable) {\n    this._inputIsExpandable = isExpandable;\n    if (this.data && !this._isExpandable || !this._inputIsExpandable) {\n      return;\n    }\n    // If the node is being set to expandable, ensure that the status of the\n    // node is propagated\n    if (this._inputIsExpanded) {\n      this.expand();\n    } else if (this._inputIsExpanded === false) {\n      this.collapse();\n    }\n  }\n  get isExpanded() {\n    return this._tree.isExpanded(this._data);\n  }\n  set isExpanded(isExpanded) {\n    this._inputIsExpanded = isExpanded;\n    if (isExpanded) {\n      this.expand();\n    } else {\n      this.collapse();\n    }\n  }\n  /**\n   * Whether or not this node is disabled. If it's disabled, then the user won't be able to focus\n   * or activate this node.\n   */\n\n  getLabel() {\n    var _this$_elementRef$nat;\n    return this.typeaheadLabel || ((_this$_elementRef$nat = this._elementRef.nativeElement.textContent) === null || _this$_elementRef$nat === void 0 ? void 0 : _this$_elementRef$nat.trim()) || '';\n  }\n  /** This emits when the node has been programatically activated or activated by keyboard. */\n\n  /** The tree node's data. */\n  get data() {\n    return this._data;\n  }\n  set data(value) {\n    if (value !== this._data) {\n      this._data = value;\n      this._dataChanges.next();\n    }\n  }\n  /* If leaf node, return true to not assign aria-expanded attribute */\n  get isLeafNode() {\n    var _this$_tree$treeContr, _this$_tree$treeContr2, _this$_tree$treeContr3;\n    // If flat tree node data returns false for expandable property, it's a leaf node\n    if (((_this$_tree$treeContr = this._tree.treeControl) === null || _this$_tree$treeContr === void 0 ? void 0 : _this$_tree$treeContr.isExpandable) !== undefined && !this._tree.treeControl.isExpandable(this._data)) {\n      return true;\n      // If nested tree node data returns 0 descendants, it's a leaf node\n    } else if (((_this$_tree$treeContr2 = this._tree.treeControl) === null || _this$_tree$treeContr2 === void 0 ? void 0 : _this$_tree$treeContr2.isExpandable) === undefined && ((_this$_tree$treeContr3 = this._tree.treeControl) === null || _this$_tree$treeContr3 === void 0 ? void 0 : _this$_tree$treeContr3.getDescendants(this._data).length) === 0) {\n      return true;\n    }\n    return false;\n  }\n  get level() {\n    var _this$_tree$_getLevel;\n    // If the tree has a levelAccessor, use it to get the level. Otherwise read the\n    // aria-level off the parent node and use it as the level for this node (note aria-level is\n    // 1-indexed, while this property is 0-indexed, so we don't need to increment).\n    return (_this$_tree$_getLevel = this._tree._getLevel(this._data)) !== null && _this$_tree$_getLevel !== void 0 ? _this$_tree$_getLevel : this._parentNodeAriaLevel;\n  }\n  /** Determines if the tree node is expandable. */\n  _isExpandable() {\n    if (this._tree.treeControl) {\n      if (this.isLeafNode) {\n        return false;\n      }\n      // For compatibility with trees created using TreeControl before we added\n      // CdkTreeNode#isExpandable.\n      return true;\n    }\n    return this._inputIsExpandable;\n  }\n  /**\n   * Determines the value for `aria-expanded`.\n   *\n   * For non-expandable nodes, this is `null`.\n   */\n  _getAriaExpanded() {\n    if (!this._isExpandable()) {\n      return null;\n    }\n    return String(this.isExpanded);\n  }\n  /**\n   * Determines the size of this node's parent's child set.\n   *\n   * This is intended to be used for `aria-setsize`.\n   */\n  _getSetSize() {\n    return this._tree._getSetSize(this._data);\n  }\n  /**\n   * Determines the index (starting from 1) of this node in its parent's child set.\n   *\n   * This is intended to be used for `aria-posinset`.\n   */\n  _getPositionInSet() {\n    return this._tree._getPositionInSet(this._data);\n  }\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_tree\", inject(CdkTree));\n    _defineProperty(this, \"_tabindex\", -1);\n    _defineProperty(this, \"_type\", 'flat');\n    _defineProperty(this, \"isDisabled\", void 0);\n    /**\n     * The text used to locate this item during typeahead. If not specified, the `textContent` will\n     * will be used.\n     */\n    _defineProperty(this, \"typeaheadLabel\", void 0);\n    _defineProperty(this, \"activation\", new EventEmitter());\n    /** This emits when the node's expansion status has been changed. */\n    _defineProperty(this, \"expandedChange\", new EventEmitter());\n    /** Subject that emits when the component has been destroyed. */\n    _defineProperty(this, \"_destroyed\", new Subject());\n    /** Emits when the node's data has changed. */\n    _defineProperty(this, \"_dataChanges\", new Subject());\n    _defineProperty(this, \"_inputIsExpandable\", false);\n    _defineProperty(this, \"_inputIsExpanded\", undefined);\n    /**\n     * Flag used to determine whether or not we should be focusing the actual element based on\n     * some user interaction (click or focus). On click, we don't forcibly focus the element\n     * since the click could trigger some other component that wants to grab its own focus\n     * (e.g. menu, dialog).\n     */\n    _defineProperty(this, \"_shouldFocus\", true);\n    _defineProperty(this, \"_parentNodeAriaLevel\", void 0);\n    _defineProperty(this, \"_data\", void 0);\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    CdkTreeNode.mostRecentTreeNode = this;\n  }\n  ngOnInit() {\n    this._parentNodeAriaLevel = getParentNodeAriaLevel(this._elementRef.nativeElement);\n    this._tree._getExpansionModel().changed.pipe(map(() => this.isExpanded), distinctUntilChanged()).subscribe(() => this._changeDetectorRef.markForCheck());\n    this._tree._setNodeTypeIfUnset(this._type);\n    this._tree._registerNode(this);\n  }\n  ngOnDestroy() {\n    // If this is the last tree node being destroyed,\n    // clear out the reference to avoid leaking memory.\n    if (CdkTreeNode.mostRecentTreeNode === this) {\n      CdkTreeNode.mostRecentTreeNode = null;\n    }\n    this._dataChanges.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  getParent() {\n    var _this$_tree$_getNodeP;\n    return (_this$_tree$_getNodeP = this._tree._getNodeParent(this)) !== null && _this$_tree$_getNodeP !== void 0 ? _this$_tree$_getNodeP : null;\n  }\n  getChildren() {\n    return this._tree._getNodeChildren(this);\n  }\n  /** Focuses this data node. Implemented for TreeKeyManagerItem. */\n  focus() {\n    this._tabindex = 0;\n    if (this._shouldFocus) {\n      this._elementRef.nativeElement.focus();\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Defocus this data node. */\n  unfocus() {\n    this._tabindex = -1;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits an activation event. Implemented for TreeKeyManagerItem. */\n  activate() {\n    if (this.isDisabled) {\n      return;\n    }\n    this.activation.next(this._data);\n  }\n  /** Collapses this data node. Implemented for TreeKeyManagerItem. */\n  collapse() {\n    if (this.isExpandable) {\n      this._tree.collapse(this._data);\n    }\n  }\n  /** Expands this data node. Implemented for TreeKeyManagerItem. */\n  expand() {\n    if (this.isExpandable) {\n      this._tree.expand(this._data);\n    }\n  }\n  /** Makes the node focusable. Implemented for TreeKeyManagerItem. */\n  makeFocusable() {\n    this._tabindex = 0;\n    this._changeDetectorRef.markForCheck();\n  }\n  _focusItem() {\n    if (this.isDisabled) {\n      return;\n    }\n    this._tree._keyManager.focusItem(this);\n  }\n  _setActiveItem() {\n    if (this.isDisabled) {\n      return;\n    }\n    this._shouldFocus = false;\n    this._tree._keyManager.focusItem(this);\n    this._shouldFocus = true;\n  }\n  _emitExpansionState(expanded) {\n    this.expandedChange.emit(expanded);\n  }\n}\n_CdkTreeNode = CdkTreeNode;\n/**\n * The most recently created `CdkTreeNode`. We save it in static variable so we can retrieve it\n * in `CdkTree` and set the data to it.\n */\n_defineProperty(CdkTreeNode, \"mostRecentTreeNode\", null);\n_defineProperty(CdkTreeNode, \"\\u0275fac\", function _CdkTreeNode_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTreeNode)();\n});\n_defineProperty(CdkTreeNode, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkTreeNode,\n  selectors: [[\"cdk-tree-node\"]],\n  hostAttrs: [\"role\", \"treeitem\", 1, \"cdk-tree-node\"],\n  hostVars: 5,\n  hostBindings: function _CdkTreeNode_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _CdkTreeNode_click_HostBindingHandler() {\n        return ctx._setActiveItem();\n      })(\"focus\", function _CdkTreeNode_focus_HostBindingHandler() {\n        return ctx._focusItem();\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"tabindex\", ctx._tabindex);\n      i0.ɵɵattribute(\"aria-expanded\", ctx._getAriaExpanded())(\"aria-level\", ctx.level + 1)(\"aria-posinset\", ctx._getPositionInSet())(\"aria-setsize\", ctx._getSetSize());\n    }\n  },\n  inputs: {\n    role: \"role\",\n    isExpandable: [2, \"isExpandable\", \"isExpandable\", booleanAttribute],\n    isExpanded: \"isExpanded\",\n    isDisabled: [2, \"isDisabled\", \"isDisabled\", booleanAttribute],\n    typeaheadLabel: [0, \"cdkTreeNodeTypeaheadLabel\", \"typeaheadLabel\"]\n  },\n  outputs: {\n    activation: \"activation\",\n    expandedChange: \"expandedChange\"\n  },\n  exportAs: [\"cdkTreeNode\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-tree-node',\n      exportAs: 'cdkTreeNode',\n      host: {\n        'class': 'cdk-tree-node',\n        '[attr.aria-expanded]': '_getAriaExpanded()',\n        '[attr.aria-level]': 'level + 1',\n        '[attr.aria-posinset]': '_getPositionInSet()',\n        '[attr.aria-setsize]': '_getSetSize()',\n        '[tabindex]': '_tabindex',\n        'role': 'treeitem',\n        '(click)': '_setActiveItem()',\n        '(focus)': '_focusItem()'\n      }\n    }]\n  }], () => [], {\n    role: [{\n      type: Input\n    }],\n    isExpandable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    isExpanded: [{\n      type: Input\n    }],\n    isDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    typeaheadLabel: [{\n      type: Input,\n      args: ['cdkTreeNodeTypeaheadLabel']\n    }],\n    activation: [{\n      type: Output\n    }],\n    expandedChange: [{\n      type: Output\n    }]\n  });\n})();\nfunction getParentNodeAriaLevel(nodeElement) {\n  let parent = nodeElement.parentElement;\n  while (parent && !isNodeElement(parent)) {\n    parent = parent.parentElement;\n  }\n  if (!parent) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw Error('Incorrect tree structure containing detached node.');\n    } else {\n      return -1;\n    }\n  } else if (parent.classList.contains('cdk-nested-tree-node')) {\n    return numberAttribute(parent.getAttribute('aria-level'));\n  } else {\n    // The ancestor element is the cdk-tree itself\n    return 0;\n  }\n}\nfunction isNodeElement(element) {\n  const classList = element.classList;\n  return !!(classList !== null && classList !== void 0 && classList.contains('cdk-nested-tree-node') || classList !== null && classList !== void 0 && classList.contains('cdk-tree'));\n}\n\n/**\n * Nested node is a child of `<cdk-tree>`. It works with nested tree.\n * By using `cdk-nested-tree-node` component in tree node template, children of the parent node will\n * be added in the `cdkTreeNodeOutlet` in tree node template.\n * The children of node will be automatically added to `cdkTreeNodeOutlet`.\n */\nclass CdkNestedTreeNode extends CdkTreeNode {\n  constructor() {\n    super();\n    _defineProperty(this, \"_type\", 'nested');\n    _defineProperty(this, \"_differs\", inject(IterableDiffers));\n    /** Differ used to find the changes in the data provided by the data source. */\n    _defineProperty(this, \"_dataDiffer\", void 0);\n    /** The children data dataNodes of current node. They will be placed in `CdkTreeNodeOutlet`. */\n    _defineProperty(this, \"_children\", void 0);\n    /** The children node placeholder. */\n    _defineProperty(this, \"nodeOutlet\", void 0);\n  }\n  ngAfterContentInit() {\n    this._dataDiffer = this._differs.find([]).create(this._tree.trackBy);\n    this._tree._getDirectChildren(this.data).pipe(takeUntil(this._destroyed)).subscribe(result => this.updateChildrenNodes(result));\n    this.nodeOutlet.changes.pipe(takeUntil(this._destroyed)).subscribe(() => this.updateChildrenNodes());\n  }\n  ngOnDestroy() {\n    this._clear();\n    super.ngOnDestroy();\n  }\n  /** Add children dataNodes to the NodeOutlet */\n  updateChildrenNodes(children) {\n    const outlet = this._getNodeOutlet();\n    if (children) {\n      this._children = children;\n    }\n    if (outlet && this._children) {\n      const viewContainer = outlet.viewContainer;\n      this._tree.renderNodeChanges(this._children, this._dataDiffer, viewContainer, this._data);\n    } else {\n      // Reset the data differ if there's no children nodes displayed\n      this._dataDiffer.diff([]);\n    }\n  }\n  /** Clear the children dataNodes. */\n  _clear() {\n    const outlet = this._getNodeOutlet();\n    if (outlet) {\n      outlet.viewContainer.clear();\n      this._dataDiffer.diff([]);\n    }\n  }\n  /** Gets the outlet for the current node. */\n  _getNodeOutlet() {\n    const outlets = this.nodeOutlet;\n    // Note that since we use `descendants: true` on the query, we have to ensure\n    // that we don't pick up the outlet of a child node by accident.\n    return outlets && outlets.find(outlet => !outlet._node || outlet._node === this);\n  }\n}\n_CdkNestedTreeNode = CdkNestedTreeNode;\n_defineProperty(CdkNestedTreeNode, \"\\u0275fac\", function _CdkNestedTreeNode_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkNestedTreeNode)();\n});\n_defineProperty(CdkNestedTreeNode, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkNestedTreeNode,\n  selectors: [[\"cdk-nested-tree-node\"]],\n  contentQueries: function _CdkNestedTreeNode_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, CdkTreeNodeOutlet, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nodeOutlet = _t);\n    }\n  },\n  hostAttrs: [1, \"cdk-nested-tree-node\"],\n  exportAs: [\"cdkNestedTreeNode\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkTreeNode,\n    useExisting: _CdkNestedTreeNode\n  }, {\n    provide: CDK_TREE_NODE_OUTLET_NODE,\n    useExisting: _CdkNestedTreeNode\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkNestedTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-nested-tree-node',\n      exportAs: 'cdkNestedTreeNode',\n      providers: [{\n        provide: CdkTreeNode,\n        useExisting: CdkNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: CdkNestedTreeNode\n      }],\n      host: {\n        'class': 'cdk-nested-tree-node'\n      }\n    }]\n  }], () => [], {\n    nodeOutlet: [{\n      type: ContentChildren,\n      args: [CdkTreeNodeOutlet, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * Indent for the children tree dataNodes.\n * This directive will add left-padding to the node to show hierarchy.\n */\nclass CdkTreeNodePadding {\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  get level() {\n    return this._level;\n  }\n  set level(value) {\n    this._setLevelInput(value);\n  }\n  /**\n   * The indent for each level. Can be a number or a CSS string.\n   * Default number 40px from material design menu sub-menu spec.\n   */\n  get indent() {\n    return this._indent;\n  }\n  set indent(indent) {\n    this._setIndentInput(indent);\n  }\n  constructor() {\n    var _this$_dir;\n    _defineProperty(this, \"_treeNode\", inject(CdkTreeNode));\n    _defineProperty(this, \"_tree\", inject(CdkTree));\n    _defineProperty(this, \"_element\", inject(ElementRef));\n    _defineProperty(this, \"_dir\", inject(Directionality, {\n      optional: true\n    }));\n    /** Current padding value applied to the element. Used to avoid unnecessarily hitting the DOM. */\n    _defineProperty(this, \"_currentPadding\", void 0);\n    /** Subject that emits when the component has been destroyed. */\n    _defineProperty(this, \"_destroyed\", new Subject());\n    /** CSS units used for the indentation value. */\n    _defineProperty(this, \"indentUnits\", 'px');\n    _defineProperty(this, \"_level\", void 0);\n    _defineProperty(this, \"_indent\", 40);\n    this._setPadding();\n    (_this$_dir = this._dir) === null || _this$_dir === void 0 || _this$_dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._setPadding(true));\n    // In Ivy the indentation binding might be set before the tree node's data has been added,\n    // which means that we'll miss the first render. We have to subscribe to changes in the\n    // data to ensure that everything is up to date.\n    this._treeNode._dataChanges.subscribe(() => this._setPadding());\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** The padding indent value for the tree node. Returns a string with px numbers if not null. */\n  _paddingIndent() {\n    var _ref;\n    const nodeLevel = (_ref = this._treeNode.data && this._tree._getLevel(this._treeNode.data)) !== null && _ref !== void 0 ? _ref : null;\n    const level = this._level == null ? nodeLevel : this._level;\n    return typeof level === 'number' ? `${level * this._indent}${this.indentUnits}` : null;\n  }\n  _setPadding(forceChange = false) {\n    const padding = this._paddingIndent();\n    if (padding !== this._currentPadding || forceChange) {\n      const element = this._element.nativeElement;\n      const paddingProp = this._dir && this._dir.value === 'rtl' ? 'paddingRight' : 'paddingLeft';\n      const resetProp = paddingProp === 'paddingLeft' ? 'paddingRight' : 'paddingLeft';\n      element.style[paddingProp] = padding || '';\n      element.style[resetProp] = '';\n      this._currentPadding = padding;\n    }\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setLevelInput(value) {\n    // Set to null as the fallback value so that _setPadding can fall back to the node level if the\n    // consumer set the directive as `cdkTreeNodePadding=\"\"`. We still want to take this value if\n    // they set 0 explicitly.\n    this._level = isNaN(value) ? null : value;\n    this._setPadding();\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setIndentInput(indent) {\n    let value = indent;\n    let units = 'px';\n    if (typeof indent === 'string') {\n      const parts = indent.split(cssUnitPattern);\n      value = parts[0];\n      units = parts[1] || units;\n    }\n    this.indentUnits = units;\n    this._indent = numberAttribute(value);\n    this._setPadding();\n  }\n}\n_CdkTreeNodePadding = CdkTreeNodePadding;\n_defineProperty(CdkTreeNodePadding, \"\\u0275fac\", function _CdkTreeNodePadding_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTreeNodePadding)();\n});\n_defineProperty(CdkTreeNodePadding, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkTreeNodePadding,\n  selectors: [[\"\", \"cdkTreeNodePadding\", \"\"]],\n  inputs: {\n    level: [2, \"cdkTreeNodePadding\", \"level\", numberAttribute],\n    indent: [0, \"cdkTreeNodePaddingIndent\", \"indent\"]\n  }\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodePadding, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodePadding]'\n    }]\n  }], () => [], {\n    level: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTreeNodePadding',\n        transform: numberAttribute\n      }]\n    }],\n    indent: [{\n      type: Input,\n      args: ['cdkTreeNodePaddingIndent']\n    }]\n  });\n})();\n\n/**\n * Node toggle to expand and collapse the node.\n */\nclass CdkTreeNodeToggle {\n  constructor() {\n    _defineProperty(this, \"_tree\", inject(CdkTree));\n    _defineProperty(this, \"_treeNode\", inject(CdkTreeNode));\n    /** Whether expand/collapse the node recursively. */\n    _defineProperty(this, \"recursive\", false);\n  }\n  // Toggle the expanded or collapsed state of this node.\n  //\n  // Focus this node with expanding or collapsing it. This ensures that the active node will always\n  // be visible when expanding and collapsing.\n  _toggle() {\n    this.recursive ? this._tree.toggleDescendants(this._treeNode.data) : this._tree.toggle(this._treeNode.data);\n    this._tree._keyManager.focusItem(this._treeNode);\n  }\n}\n_CdkTreeNodeToggle = CdkTreeNodeToggle;\n_defineProperty(CdkTreeNodeToggle, \"\\u0275fac\", function _CdkTreeNodeToggle_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTreeNodeToggle)();\n});\n_defineProperty(CdkTreeNodeToggle, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkTreeNodeToggle,\n  selectors: [[\"\", \"cdkTreeNodeToggle\", \"\"]],\n  hostAttrs: [\"tabindex\", \"-1\"],\n  hostBindings: function _CdkTreeNodeToggle_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _CdkTreeNodeToggle_click_HostBindingHandler($event) {\n        ctx._toggle();\n        return $event.stopPropagation();\n      })(\"keydown.Enter\", function _CdkTreeNodeToggle_keydown_Enter_HostBindingHandler($event) {\n        ctx._toggle();\n        return $event.preventDefault();\n      })(\"keydown.Space\", function _CdkTreeNodeToggle_keydown_Space_HostBindingHandler($event) {\n        ctx._toggle();\n        return $event.preventDefault();\n      });\n    }\n  },\n  inputs: {\n    recursive: [2, \"cdkTreeNodeToggleRecursive\", \"recursive\", booleanAttribute]\n  }\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeNodeToggle, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTreeNodeToggle]',\n      host: {\n        '(click)': '_toggle(); $event.stopPropagation();',\n        '(keydown.Enter)': '_toggle(); $event.preventDefault();',\n        '(keydown.Space)': '_toggle(); $event.preventDefault();',\n        'tabindex': '-1'\n      }\n    }]\n  }], () => [], {\n    recursive: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTreeNodeToggleRecursive',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst EXPORTED_DECLARATIONS = [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet];\nclass CdkTreeModule {}\n_CdkTreeModule = CdkTreeModule;\n_defineProperty(CdkTreeModule, \"\\u0275fac\", function _CdkTreeModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTreeModule)();\n});\n_defineProperty(CdkTreeModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _CdkTreeModule,\n  imports: [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet],\n  exports: [CdkNestedTreeNode, CdkTreeNodeDef, CdkTreeNodePadding, CdkTreeNodeToggle, CdkTree, CdkTreeNode, CdkTreeNodeOutlet]\n}));\n_defineProperty(CdkTreeModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTreeModule, [{\n    type: NgModule,\n    args: [{\n      imports: EXPORTED_DECLARATIONS,\n      exports: EXPORTED_DECLARATIONS\n    }]\n  }], null, null);\n})();\nexport { BaseTreeControl, CDK_TREE_NODE_OUTLET_NODE, CdkNestedTreeNode, CdkTree, CdkTreeModule, CdkTreeNode, CdkTreeNodeDef, CdkTreeNodeOutlet, CdkTreeNodeOutletContext, CdkTreeNodePadding, CdkTreeNodeToggle, FlatTreeControl, NestedTreeControl, getMultipleTreeControlsError, getTreeControlMissingError, getTreeMissingMatchingNodeDefError, getTreeMultipleDefaultNodeDefsError, getTreeNoValidDataSourceError };\n//# sourceMappingURL=tree.mjs.map", "map": {"version": 3, "names": ["S", "SelectionModel", "isObservable", "Subject", "BehaviorSubject", "of", "combineLatest", "EMPTY", "concat", "take", "filter", "takeUntil", "startWith", "tap", "switchMap", "map", "reduce", "concatMap", "distinctUntilChanged", "i0", "InjectionToken", "inject", "ViewContainerRef", "Directive", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "ChangeDetectorRef", "ElementRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "ViewChild", "ContentChildren", "EventEmitter", "booleanAttribute", "Output", "numberAttribute", "NgModule", "T", "TREE_KEY_MANAGER", "D", "Directionality", "i", "isDataSource", "coerceObservable", "BaseTreeControl", "constructor", "_defineProperty", "toggle", "dataNode", "expansionModel", "_trackByValue", "expand", "select", "collapse", "deselect", "isExpanded", "isSelected", "toggleDescendants", "collapseDescendants", "expandDescendants", "collapseAll", "clear", "toBeProcessed", "push", "getDescendants", "value", "trackBy", "FlatTreeControl", "getLevel", "isExpandable", "options", "startIndex", "dataNodes", "indexOf", "results", "length", "expandAll", "node", "NestedTreeControl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this$options", "allNodes", "accumulator", "descendants", "_getDescendants", "splice", "childrenNodes", "Array", "isArray", "for<PERSON>ach", "child", "pipe", "Boolean", "subscribe", "children", "CDK_TREE_NODE_OUTLET_NODE", "CdkTreeNodeOutlet", "optional", "_CdkTreeNodeOutlet", "_CdkTreeNodeOutlet_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "CdkTreeNodeOutletContext", "data", "$implicit", "CdkTreeNodeDef", "_CdkTreeNodeDef", "_CdkTreeNodeDef_Factory", "inputs", "when", "name", "alias", "getTreeNoValidDataSourceError", "Error", "getTreeMultipleDefaultNodeDefsError", "getTreeMissingMatchingNodeDefError", "getTreeControlMissingError", "getMultipleTreeControlsError", "CdkTree", "dataSource", "_dataSource", "_switchDataSource", "Map", "start", "end", "Number", "MAX_VALUE", "ngAfterContentInit", "_initializeKeyManager", "ngAfterContentChecked", "_updateDefaultNodeDefinition", "_subscribeToDataChanges", "ngOnDestroy", "_this$_keyManager", "_nodeOutlet", "viewContainer", "viewChange", "complete", "_onD<PERSON>roy", "next", "disconnect", "_dataSubscription", "unsubscribe", "_keyManager", "destroy", "ngOnInit", "_checkTreeControlUsage", "_initialize<PERSON><PERSON><PERSON><PERSON><PERSON>", "ngAfterViewInit", "_viewInit", "defaultNodeDefs", "_nodeDefs", "def", "_defaultNodeDef", "_setNodeTypeIfUnset", "newType", "currentType", "_nodeType", "console", "warn", "_getExpansionModel", "treeControl", "_this$_expansionModel", "_expansionModel", "dataStream", "connect", "_getRenderData", "renderingData", "_renderDataChanges", "changed", "expansionChanges", "_emitExpansionChanges", "nodeType", "renderNodes", "flattenedNodes", "_computeRenderingData", "convertedData", "_objectSpread", "renderNodeChanges", "_updateCachedData", "_updateKeyManagerItems", "nodes", "_nodes", "added", "get", "_emitExpansionState", "removed", "items", "_keyManagerNodes", "keyManagerNodes", "_getExpansionKey", "keyManagerOptions", "skipPredicate", "isDisabled", "typeAheadDebounceInterval", "horizontalOrientation", "_dir", "_keyManagerFactory", "_this$trackBy", "_index", "item", "_data<PERSON><PERSON>er", "_differs", "find", "create", "numTreeControls", "levelAccessor", "children<PERSON><PERSON><PERSON>or", "<PERSON><PERSON><PERSON><PERSON>", "parentData", "changes", "diff", "forEachOperation", "adjustedPreviousIndex", "currentIndex", "previousIndex", "insertNode", "remove", "view", "move", "forEachIdentityChange", "record", "newData", "undefined", "context", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "_getNodeDef", "first", "nodeDef", "nodeData", "index", "_this$_parents$get", "_getLevelAccessor", "key", "_parents", "level", "_levels", "has", "set", "container", "createEmbeddedView", "template", "CdkTreeNode", "mostRecentTreeNode", "_this$treeControl", "_this$_expansionModel2", "_forEachExpansionKey", "keys", "_this$_expansionModel3", "_this$_expansionModel4", "_this$treeControl$get", "_this$treeControl2", "bind", "_get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>essor", "_this$treeControl$get2", "_this$treeControl3", "_getDirectChildren", "_this$_expansionModel5", "_this$treeControl4", "includes", "_flattenedNodes", "expanded", "_findChildrenByLevel", "_childrenAccessor", "levelDelta", "findIndex", "dataNodeLevel", "expectedLevel", "currentLevel", "_registerNode", "_unregisterNode", "delete", "_getLevel", "_getSetSize", "_getAriaSet", "_getPositionInSet", "_getNodeParent", "parent", "_getNodeC<PERSON><PERSON>n", "_sendKeydownToKeyManager", "event", "target", "_elementRef", "nativeElement", "onKeydown", "getValue", "Infinity", "_getAllChildrenRecursively", "allChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this$expansionKey", "_this$expansionKey2", "expansionKey", "call", "parent<PERSON><PERSON>", "_ariaSets", "_findParentForNode", "cachedNodes", "_this$_levels$get", "parentIndex", "_this$_levels$get2", "parentNode", "parentLevel", "_flattenNestedNodesWithExpansion", "childNodes", "<PERSON><PERSON><PERSON>", "nestedNodes", "_clearPreviousCache", "rootNodes", "_calculateParents", "_this$_ariaSets$get", "group", "callback", "toToggle", "observables", "inner", "r", "_CdkTree", "_CdkTree_Factory", "ɵɵdefineComponent", "contentQueries", "_CdkTree_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "_CdkTree_Query", "ɵɵviewQuery", "hostAttrs", "hostBindings", "_CdkTree_HostBindings", "ɵɵlistener", "_CdkTree_keydown_HostBindingHandler", "$event", "exportAs", "decls", "vars", "consts", "_CdkTree_Template", "ɵɵelementContainer", "dependencies", "encapsulation", "host", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "static", "role", "_role", "_isExpandable", "_inputIsExpandable", "_inputIsExpanded", "_tree", "_data", "get<PERSON><PERSON><PERSON>", "_this$_elementRef$nat", "typeaheadLabel", "textContent", "trim", "_dataChanges", "isLeafNode", "_this$_tree$treeContr", "_this$_tree$treeContr2", "_this$_tree$treeContr3", "_this$_tree$_getLevel", "_parentNodeAriaLevel", "_getAriaExpanded", "String", "getParentNodeAriaLevel", "_type", "_destroyed", "getParent", "_this$_tree$_getNodeP", "focus", "_tabindex", "_shouldFocus", "unfocus", "activate", "activation", "makeFocusable", "_focusItem", "focusItem", "_setActiveItem", "expandedChange", "emit", "_CdkTreeNode", "_CdkTreeNode_Factory", "hostVars", "_CdkTreeNode_HostBindings", "_CdkTreeNode_click_HostBindingHandler", "_CdkTreeNode_focus_HostBindingHandler", "ɵɵhostProperty", "ɵɵattribute", "outputs", "transform", "nodeElement", "parentElement", "isNodeElement", "classList", "contains", "getAttribute", "element", "CdkNestedTreeNode", "result", "updateChildrenNodes", "nodeOutlet", "_clear", "outlet", "_getNodeOutlet", "_children", "outlets", "_node", "_CdkNestedTreeNode", "_CdkNestedTreeNode_Factory", "_CdkNestedTreeNode_ContentQueries", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInheritDefinitionFeature", "providers", "cssUnitPattern", "CdkTreeNodePadding", "_level", "_setLevelInput", "indent", "_indent", "_setIndentInput", "_this$_dir", "_setPadding", "change", "_treeNode", "_paddingIndent", "_ref", "nodeLevel", "indentUnits", "forceChange", "padding", "_currentPadding", "_element", "paddingProp", "resetProp", "style", "isNaN", "units", "parts", "split", "_CdkTreeNodePadding", "_CdkTreeNodePadding_Factory", "CdkTreeNodeToggle", "_toggle", "recursive", "_CdkTreeNodeToggle", "_CdkTreeNodeToggle_Factory", "_CdkTreeNodeToggle_HostBindings", "_CdkTreeNodeToggle_click_HostBindingHandler", "stopPropagation", "_CdkTreeNodeToggle_keydown_Enter_HostBindingHandler", "preventDefault", "_CdkTreeNodeToggle_keydown_Space_HostBindingHandler", "EXPORTED_DECLARATIONS", "CdkTreeModule", "_CdkTreeModule", "_CdkTreeModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/tree.mjs"], "sourcesContent": ["import { S as SelectionModel } from './selection-model-CeeHVIcP.mjs';\nimport { isObservable, Subject, BehaviorSubject, of, combineLatest, EMPTY, concat } from 'rxjs';\nimport { take, filter, takeUntil, startWith, tap, switchMap, map, reduce, concatMap, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ViewContainerRef, Directive, TemplateRef, IterableDiffers, ChangeDetectorRef, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ViewChild, ContentChildren, EventEmitter, booleanAttribute, Output, numberAttribute, NgModule } from '@angular/core';\nimport { T as TREE_KEY_MANAGER } from './tree-key-manager-KnCoIkIC.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { i as isDataSource } from './data-source-D34wiQZj.mjs';\nimport { coerceObservable } from './coercion/private.mjs';\nimport './typeahead-9ZW4Dtsf.mjs';\nimport './keycodes-CpHkExLC.mjs';\nimport '@angular/common';\n\n/**\n * Base tree control. It has basic toggle/expand/collapse operations on a single data node.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor. To be removed in a future version.\n * @breaking-change 21.0.0\n */\nclass BaseTreeControl {\n    /** Saved data node for `expandAll` action. */\n    dataNodes;\n    /** A selection model with multi-selection to track expansion status. */\n    expansionModel = new SelectionModel(true);\n    /**\n     * Returns the identifier by which a dataNode should be tracked, should its\n     * reference change.\n     *\n     * Similar to trackBy for *ngFor\n     */\n    trackBy;\n    /** Get depth of a given data node, return the level number. This is for flat tree node. */\n    getLevel;\n    /**\n     * Whether the data node is expandable. Returns true if expandable.\n     * This is for flat tree node.\n     */\n    isExpandable;\n    /** Gets a stream that emits whenever the given data node's children change. */\n    getChildren;\n    /** Toggles one single data node's expanded/collapsed state. */\n    toggle(dataNode) {\n        this.expansionModel.toggle(this._trackByValue(dataNode));\n    }\n    /** Expands one single data node. */\n    expand(dataNode) {\n        this.expansionModel.select(this._trackByValue(dataNode));\n    }\n    /** Collapses one single data node. */\n    collapse(dataNode) {\n        this.expansionModel.deselect(this._trackByValue(dataNode));\n    }\n    /** Whether a given data node is expanded or not. Returns true if the data node is expanded. */\n    isExpanded(dataNode) {\n        return this.expansionModel.isSelected(this._trackByValue(dataNode));\n    }\n    /** Toggles a subtree rooted at `node` recursively. */\n    toggleDescendants(dataNode) {\n        this.expansionModel.isSelected(this._trackByValue(dataNode))\n            ? this.collapseDescendants(dataNode)\n            : this.expandDescendants(dataNode);\n    }\n    /** Collapse all dataNodes in the tree. */\n    collapseAll() {\n        this.expansionModel.clear();\n    }\n    /** Expands a subtree rooted at given data node recursively. */\n    expandDescendants(dataNode) {\n        let toBeProcessed = [dataNode];\n        toBeProcessed.push(...this.getDescendants(dataNode));\n        this.expansionModel.select(...toBeProcessed.map(value => this._trackByValue(value)));\n    }\n    /** Collapses a subtree rooted at given data node recursively. */\n    collapseDescendants(dataNode) {\n        let toBeProcessed = [dataNode];\n        toBeProcessed.push(...this.getDescendants(dataNode));\n        this.expansionModel.deselect(...toBeProcessed.map(value => this._trackByValue(value)));\n    }\n    _trackByValue(value) {\n        return this.trackBy ? this.trackBy(value) : value;\n    }\n}\n\n/**\n * Flat tree control. Able to expand/collapse a subtree recursively for flattened tree.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nclass FlatTreeControl extends BaseTreeControl {\n    getLevel;\n    isExpandable;\n    options;\n    /** Construct with flat tree data node functions getLevel and isExpandable. */\n    constructor(getLevel, isExpandable, options) {\n        super();\n        this.getLevel = getLevel;\n        this.isExpandable = isExpandable;\n        this.options = options;\n        if (this.options) {\n            this.trackBy = this.options.trackBy;\n        }\n    }\n    /**\n     * Gets a list of the data node's subtree of descendent data nodes.\n     *\n     * To make this working, the `dataNodes` of the TreeControl must be flattened tree nodes\n     * with correct levels.\n     */\n    getDescendants(dataNode) {\n        const startIndex = this.dataNodes.indexOf(dataNode);\n        const results = [];\n        // Goes through flattened tree nodes in the `dataNodes` array, and get all descendants.\n        // The level of descendants of a tree node must be greater than the level of the given\n        // tree node.\n        // If we reach a node whose level is equal to the level of the tree node, we hit a sibling.\n        // If we reach a node whose level is greater than the level of the tree node, we hit a\n        // sibling of an ancestor.\n        for (let i = startIndex + 1; i < this.dataNodes.length && this.getLevel(dataNode) < this.getLevel(this.dataNodes[i]); i++) {\n            results.push(this.dataNodes[i]);\n        }\n        return results;\n    }\n    /**\n     * Expands all data nodes in the tree.\n     *\n     * To make this working, the `dataNodes` variable of the TreeControl must be set to all flattened\n     * data nodes of the tree.\n     */\n    expandAll() {\n        this.expansionModel.select(...this.dataNodes.map(node => this._trackByValue(node)));\n    }\n}\n\n/**\n * Nested tree control. Able to expand/collapse a subtree recursively for NestedNode type.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nclass NestedTreeControl extends BaseTreeControl {\n    getChildren;\n    options;\n    /** Construct with nested tree function getChildren. */\n    constructor(getChildren, options) {\n        super();\n        this.getChildren = getChildren;\n        this.options = options;\n        if (this.options) {\n            this.trackBy = this.options.trackBy;\n        }\n        if (this.options?.isExpandable) {\n            this.isExpandable = this.options.isExpandable;\n        }\n    }\n    /**\n     * Expands all dataNodes in the tree.\n     *\n     * To make this working, the `dataNodes` variable of the TreeControl must be set to all root level\n     * data nodes of the tree.\n     */\n    expandAll() {\n        this.expansionModel.clear();\n        const allNodes = this.dataNodes.reduce((accumulator, dataNode) => [...accumulator, ...this.getDescendants(dataNode), dataNode], []);\n        this.expansionModel.select(...allNodes.map(node => this._trackByValue(node)));\n    }\n    /** Gets a list of descendant dataNodes of a subtree rooted at given data node recursively. */\n    getDescendants(dataNode) {\n        const descendants = [];\n        this._getDescendants(descendants, dataNode);\n        // Remove the node itself\n        return descendants.splice(1);\n    }\n    /** A helper function to get descendants recursively. */\n    _getDescendants(descendants, dataNode) {\n        descendants.push(dataNode);\n        const childrenNodes = this.getChildren(dataNode);\n        if (Array.isArray(childrenNodes)) {\n            childrenNodes.forEach((child) => this._getDescendants(descendants, child));\n        }\n        else if (isObservable(childrenNodes)) {\n            // TypeScript as of version 3.5 doesn't seem to treat `Boolean` like a function that\n            // returns a `boolean` specifically in the context of `filter`, so we manually clarify that.\n            childrenNodes.pipe(take(1), filter(Boolean)).subscribe(children => {\n                for (const child of children) {\n                    this._getDescendants(descendants, child);\n                }\n            });\n        }\n    }\n}\n\n/**\n * Injection token used to provide a `CdkTreeNode` to its outlet.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst CDK_TREE_NODE_OUTLET_NODE = new InjectionToken('CDK_TREE_NODE_OUTLET_NODE');\n/**\n * Outlet for nested CdkNode. Put `[cdkTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nclass CdkTreeNodeOutlet {\n    viewContainer = inject(ViewContainerRef);\n    _node = inject(CDK_TREE_NODE_OUTLET_NODE, { optional: true });\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeNodeOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkTreeNodeOutlet, isStandalone: true, selector: \"[cdkTreeNodeOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeNodeOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTreeNodeOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n\n/** Context provided to the tree node component. */\nclass CdkTreeNodeOutletContext {\n    /** Data for the node. */\n    $implicit;\n    /** Depth of the node. */\n    level;\n    /** Index location of the node. */\n    index;\n    /** Length of the number of total dataNodes. */\n    count;\n    constructor(data) {\n        this.$implicit = data;\n    }\n}\n/**\n * Data node definition for the CdkTree.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nclass CdkTreeNodeDef {\n    /** @docs-private */\n    template = inject(TemplateRef);\n    /**\n     * Function that should return true if this node template should be used for the provided node\n     * data and index. If left undefined, this node will be considered the default node template to\n     * use when no other when functions return true for the data.\n     * For every node, there must be at least one when function that passes or an undefined to\n     * default.\n     */\n    when;\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeNodeDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkTreeNodeDef, isStandalone: true, selector: \"[cdkTreeNodeDef]\", inputs: { when: [\"cdkTreeNodeDefWhen\", \"when\"] }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeNodeDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTreeNodeDef]',\n                    inputs: [{ name: 'when', alias: 'cdkTreeNodeDefWhen' }],\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Returns an error to be thrown when there is no usable data.\n * @docs-private\n */\nfunction getTreeNoValidDataSourceError() {\n    return Error(`A valid data source must be provided.`);\n}\n/**\n * Returns an error to be thrown when there are multiple nodes that are missing a when function.\n * @docs-private\n */\nfunction getTreeMultipleDefaultNodeDefsError() {\n    return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching node defs for a particular set of data.\n * @docs-private\n */\nfunction getTreeMissingMatchingNodeDefError() {\n    return Error(`Could not find a matching node definition for the provided node data.`);\n}\n/**\n * Returns an error to be thrown when there is no tree control.\n * @docs-private\n */\nfunction getTreeControlMissingError() {\n    return Error(`Could not find a tree control, levelAccessor, or childrenAccessor for the tree.`);\n}\n/**\n * Returns an error to be thrown when there are multiple ways of specifying children or level\n * provided to the tree.\n * @docs-private\n */\nfunction getMultipleTreeControlsError() {\n    return Error(`More than one of tree control, levelAccessor, or childrenAccessor were provided.`);\n}\n\n/**\n * CDK tree component that connects with a data source to retrieve data of type `T` and renders\n * dataNodes with hierarchy. Updates the dataNodes when new data is provided by the data source.\n */\nclass CdkTree {\n    _differs = inject(IterableDiffers);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _dir = inject(Directionality);\n    /** Subject that emits when the component has been destroyed. */\n    _onDestroy = new Subject();\n    /** Differ used to find the changes in the data provided by the data source. */\n    _dataDiffer;\n    /** Stores the node definition that does not have a when predicate. */\n    _defaultNodeDef;\n    /** Data subscription */\n    _dataSubscription;\n    /** Level of nodes */\n    _levels = new Map();\n    /** The immediate parents for a node. This is `null` if there is no parent. */\n    _parents = new Map();\n    /**\n     * Nodes grouped into each set, which is a list of nodes displayed together in the DOM.\n     *\n     * Lookup key is the parent of a set. Root nodes have key of null.\n     *\n     * Values is a 'set' of tree nodes. Each tree node maps to a treeitem element. Sets are in the\n     * order that it is rendered. Each set maps directly to aria-posinset and aria-setsize attributes.\n     */\n    _ariaSets = new Map();\n    /**\n     * Provides a stream containing the latest data array to render. Influenced by the tree's\n     * stream of view window (what dataNodes are currently on screen).\n     * Data source can be an observable of data array, or a data array to render.\n     */\n    get dataSource() {\n        return this._dataSource;\n    }\n    set dataSource(dataSource) {\n        if (this._dataSource !== dataSource) {\n            this._switchDataSource(dataSource);\n        }\n    }\n    _dataSource;\n    /**\n     * The tree controller\n     *\n     * @deprecated Use one of `levelAccessor` or `childrenAccessor` instead. To be removed in a\n     * future version.\n     * @breaking-change 21.0.0\n     */\n    treeControl;\n    /**\n     * Given a data node, determines what tree level the node is at.\n     *\n     * One of levelAccessor or childrenAccessor must be specified, not both.\n     * This is enforced at run-time.\n     */\n    levelAccessor;\n    /**\n     * Given a data node, determines what the children of that node are.\n     *\n     * One of levelAccessor or childrenAccessor must be specified, not both.\n     * This is enforced at run-time.\n     */\n    childrenAccessor;\n    /**\n     * Tracking function that will be used to check the differences in data changes. Used similarly\n     * to `ngFor` `trackBy` function. Optimize node operations by identifying a node based on its data\n     * relative to the function to know if a node should be added/removed/moved.\n     * Accepts a function that takes two parameters, `index` and `item`.\n     */\n    trackBy;\n    /**\n     * Given a data node, determines the key by which we determine whether or not this node is expanded.\n     */\n    expansionKey;\n    // Outlets within the tree's template where the dataNodes will be inserted.\n    _nodeOutlet;\n    /** The tree node template for the tree */\n    _nodeDefs;\n    // TODO(tinayuangao): Setup a listener for scrolling, emit the calculated view to viewChange.\n    //     Remove the MAX_VALUE in viewChange\n    /**\n     * Stream containing the latest information on what rows are being displayed on screen.\n     * Can be used by the data source to as a heuristic of what data should be provided.\n     */\n    viewChange = new BehaviorSubject({\n        start: 0,\n        end: Number.MAX_VALUE,\n    });\n    /** Keep track of which nodes are expanded. */\n    _expansionModel;\n    /**\n     * Maintain a synchronous cache of flattened data nodes. This will only be\n     * populated after initial render, and in certain cases, will be delayed due to\n     * relying on Observable `getChildren` calls.\n     */\n    _flattenedNodes = new BehaviorSubject([]);\n    /** The automatically determined node type for the tree. */\n    _nodeType = new BehaviorSubject(null);\n    /** The mapping between data and the node that is rendered. */\n    _nodes = new BehaviorSubject(new Map());\n    /**\n     * Synchronous cache of nodes for the `TreeKeyManager`. This is separate\n     * from `_flattenedNodes` so they can be independently updated at different\n     * times.\n     */\n    _keyManagerNodes = new BehaviorSubject([]);\n    _keyManagerFactory = inject(TREE_KEY_MANAGER);\n    /** The key manager for this tree. Handles focus and activation based on user keyboard input. */\n    _keyManager;\n    _viewInit = false;\n    constructor() { }\n    ngAfterContentInit() {\n        this._initializeKeyManager();\n    }\n    ngAfterContentChecked() {\n        this._updateDefaultNodeDefinition();\n        this._subscribeToDataChanges();\n    }\n    ngOnDestroy() {\n        this._nodeOutlet.viewContainer.clear();\n        this.viewChange.complete();\n        this._onDestroy.next();\n        this._onDestroy.complete();\n        if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n            this.dataSource.disconnect(this);\n        }\n        if (this._dataSubscription) {\n            this._dataSubscription.unsubscribe();\n            this._dataSubscription = null;\n        }\n        // In certain tests, the tree might be destroyed before this is initialized\n        // in `ngAfterContentInit`.\n        this._keyManager?.destroy();\n    }\n    ngOnInit() {\n        this._checkTreeControlUsage();\n        this._initializeDataDiffer();\n    }\n    ngAfterViewInit() {\n        this._viewInit = true;\n    }\n    _updateDefaultNodeDefinition() {\n        const defaultNodeDefs = this._nodeDefs.filter(def => !def.when);\n        if (defaultNodeDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTreeMultipleDefaultNodeDefsError();\n        }\n        this._defaultNodeDef = defaultNodeDefs[0];\n    }\n    /**\n     * Sets the node type for the tree, if it hasn't been set yet.\n     *\n     * This will be called by the first node that's rendered in order for the tree\n     * to determine what data transformations are required.\n     */\n    _setNodeTypeIfUnset(newType) {\n        const currentType = this._nodeType.value;\n        if (currentType === null) {\n            this._nodeType.next(newType);\n        }\n        else if ((typeof ngDevMode === 'undefined' || ngDevMode) && currentType !== newType) {\n            console.warn(`Tree is using conflicting node types which can cause unexpected behavior. ` +\n                `Please use tree nodes of the same type (e.g. only flat or only nested). ` +\n                `Current node type: \"${currentType}\", new node type \"${newType}\".`);\n        }\n    }\n    /**\n     * Switch to the provided data source by resetting the data and unsubscribing from the current\n     * render change subscription if one exists. If the data source is null, interpret this by\n     * clearing the node outlet. Otherwise start listening for new data.\n     */\n    _switchDataSource(dataSource) {\n        if (this._dataSource && typeof this._dataSource.disconnect === 'function') {\n            this.dataSource.disconnect(this);\n        }\n        if (this._dataSubscription) {\n            this._dataSubscription.unsubscribe();\n            this._dataSubscription = null;\n        }\n        // Remove the all dataNodes if there is now no data source\n        if (!dataSource) {\n            this._nodeOutlet.viewContainer.clear();\n        }\n        this._dataSource = dataSource;\n        if (this._nodeDefs) {\n            this._subscribeToDataChanges();\n        }\n    }\n    _getExpansionModel() {\n        if (!this.treeControl) {\n            this._expansionModel ??= new SelectionModel(true);\n            return this._expansionModel;\n        }\n        return this.treeControl.expansionModel;\n    }\n    /** Set up a subscription for the data provided by the data source. */\n    _subscribeToDataChanges() {\n        if (this._dataSubscription) {\n            return;\n        }\n        let dataStream;\n        if (isDataSource(this._dataSource)) {\n            dataStream = this._dataSource.connect(this);\n        }\n        else if (isObservable(this._dataSource)) {\n            dataStream = this._dataSource;\n        }\n        else if (Array.isArray(this._dataSource)) {\n            dataStream = of(this._dataSource);\n        }\n        if (!dataStream) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                throw getTreeNoValidDataSourceError();\n            }\n            return;\n        }\n        this._dataSubscription = this._getRenderData(dataStream)\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(renderingData => {\n            this._renderDataChanges(renderingData);\n        });\n    }\n    /** Given an Observable containing a stream of the raw data, returns an Observable containing the RenderingData */\n    _getRenderData(dataStream) {\n        const expansionModel = this._getExpansionModel();\n        return combineLatest([\n            dataStream,\n            this._nodeType,\n            // We don't use the expansion data directly, however we add it here to essentially\n            // trigger data rendering when expansion changes occur.\n            expansionModel.changed.pipe(startWith(null), tap(expansionChanges => {\n                this._emitExpansionChanges(expansionChanges);\n            })),\n        ]).pipe(switchMap(([data, nodeType]) => {\n            if (nodeType === null) {\n                return of({ renderNodes: data, flattenedNodes: null, nodeType });\n            }\n            // If we're here, then we know what our node type is, and therefore can\n            // perform our usual rendering pipeline, which necessitates converting the data\n            return this._computeRenderingData(data, nodeType).pipe(map(convertedData => ({ ...convertedData, nodeType })));\n        }));\n    }\n    _renderDataChanges(data) {\n        if (data.nodeType === null) {\n            this.renderNodeChanges(data.renderNodes);\n            return;\n        }\n        // If we're here, then we know what our node type is, and therefore can\n        // perform our usual rendering pipeline.\n        this._updateCachedData(data.flattenedNodes);\n        this.renderNodeChanges(data.renderNodes);\n        this._updateKeyManagerItems(data.flattenedNodes);\n    }\n    _emitExpansionChanges(expansionChanges) {\n        if (!expansionChanges) {\n            return;\n        }\n        const nodes = this._nodes.value;\n        for (const added of expansionChanges.added) {\n            const node = nodes.get(added);\n            node?._emitExpansionState(true);\n        }\n        for (const removed of expansionChanges.removed) {\n            const node = nodes.get(removed);\n            node?._emitExpansionState(false);\n        }\n    }\n    _initializeKeyManager() {\n        const items = combineLatest([this._keyManagerNodes, this._nodes]).pipe(map(([keyManagerNodes, renderNodes]) => keyManagerNodes.reduce((items, data) => {\n            const node = renderNodes.get(this._getExpansionKey(data));\n            if (node) {\n                items.push(node);\n            }\n            return items;\n        }, [])));\n        const keyManagerOptions = {\n            trackBy: node => this._getExpansionKey(node.data),\n            skipPredicate: node => !!node.isDisabled,\n            typeAheadDebounceInterval: true,\n            horizontalOrientation: this._dir.value,\n        };\n        this._keyManager = this._keyManagerFactory(items, keyManagerOptions);\n    }\n    _initializeDataDiffer() {\n        // Provide a default trackBy based on `_getExpansionKey` if one isn't provided.\n        const trackBy = this.trackBy ?? ((_index, item) => this._getExpansionKey(item));\n        this._dataDiffer = this._differs.find([]).create(trackBy);\n    }\n    _checkTreeControlUsage() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Verify that Tree follows API contract of using one of TreeControl, levelAccessor or\n            // childrenAccessor. Throw an appropriate error if contract is not met.\n            let numTreeControls = 0;\n            if (this.treeControl) {\n                numTreeControls++;\n            }\n            if (this.levelAccessor) {\n                numTreeControls++;\n            }\n            if (this.childrenAccessor) {\n                numTreeControls++;\n            }\n            if (!numTreeControls) {\n                throw getTreeControlMissingError();\n            }\n            else if (numTreeControls > 1) {\n                throw getMultipleTreeControlsError();\n            }\n        }\n    }\n    /** Check for changes made in the data and render each change (node added/removed/moved). */\n    renderNodeChanges(data, dataDiffer = this._dataDiffer, viewContainer = this._nodeOutlet.viewContainer, parentData) {\n        const changes = dataDiffer.diff(data);\n        // Some tree consumers expect change detection to propagate to nodes\n        // even when the array itself hasn't changed; we explicitly detect changes\n        // anyways in order for nodes to update their data.\n        //\n        // However, if change detection is called while the component's view is\n        // still initing, then the order of child views initing will be incorrect;\n        // to prevent this, we only exit early if the view hasn't initialized yet.\n        if (!changes && !this._viewInit) {\n            return;\n        }\n        changes?.forEachOperation((item, adjustedPreviousIndex, currentIndex) => {\n            if (item.previousIndex == null) {\n                this.insertNode(data[currentIndex], currentIndex, viewContainer, parentData);\n            }\n            else if (currentIndex == null) {\n                viewContainer.remove(adjustedPreviousIndex);\n            }\n            else {\n                const view = viewContainer.get(adjustedPreviousIndex);\n                viewContainer.move(view, currentIndex);\n            }\n        });\n        // If the data itself changes, but keeps the same trackBy, we need to update the templates'\n        // context to reflect the new object.\n        changes?.forEachIdentityChange((record) => {\n            const newData = record.item;\n            if (record.currentIndex != undefined) {\n                const view = viewContainer.get(record.currentIndex);\n                view.context.$implicit = newData;\n            }\n        });\n        // Note: we only `detectChanges` from a top-level call, otherwise we risk overflowing\n        // the call stack since this method is called recursively (see #29733.)\n        // TODO: change to `this._changeDetectorRef.markForCheck()`,\n        // or just switch this component to use signals.\n        if (parentData) {\n            this._changeDetectorRef.markForCheck();\n        }\n        else {\n            this._changeDetectorRef.detectChanges();\n        }\n    }\n    /**\n     * Finds the matching node definition that should be used for this node data. If there is only\n     * one node definition, it is returned. Otherwise, find the node definition that has a when\n     * predicate that returns true with the data. If none return true, return the default node\n     * definition.\n     */\n    _getNodeDef(data, i) {\n        if (this._nodeDefs.length === 1) {\n            return this._nodeDefs.first;\n        }\n        const nodeDef = this._nodeDefs.find(def => def.when && def.when(i, data)) || this._defaultNodeDef;\n        if (!nodeDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTreeMissingMatchingNodeDefError();\n        }\n        return nodeDef;\n    }\n    /**\n     * Create the embedded view for the data node template and place it in the correct index location\n     * within the data node view container.\n     */\n    insertNode(nodeData, index, viewContainer, parentData) {\n        const levelAccessor = this._getLevelAccessor();\n        const node = this._getNodeDef(nodeData, index);\n        const key = this._getExpansionKey(nodeData);\n        // Node context that will be provided to created embedded view\n        const context = new CdkTreeNodeOutletContext(nodeData);\n        parentData ??= this._parents.get(key) ?? undefined;\n        // If the tree is flat tree, then use the `getLevel` function in flat tree control\n        // Otherwise, use the level of parent node.\n        if (levelAccessor) {\n            context.level = levelAccessor(nodeData);\n        }\n        else if (parentData !== undefined && this._levels.has(this._getExpansionKey(parentData))) {\n            context.level = this._levels.get(this._getExpansionKey(parentData)) + 1;\n        }\n        else {\n            context.level = 0;\n        }\n        this._levels.set(key, context.level);\n        // Use default tree nodeOutlet, or nested node's nodeOutlet\n        const container = viewContainer ? viewContainer : this._nodeOutlet.viewContainer;\n        container.createEmbeddedView(node.template, context, index);\n        // Set the data to just created `CdkTreeNode`.\n        // The `CdkTreeNode` created from `createEmbeddedView` will be saved in static variable\n        //     `mostRecentTreeNode`. We get it from static variable and pass the node data to it.\n        if (CdkTreeNode.mostRecentTreeNode) {\n            CdkTreeNode.mostRecentTreeNode.data = nodeData;\n        }\n    }\n    /** Whether the data node is expanded or collapsed. Returns true if it's expanded. */\n    isExpanded(dataNode) {\n        return !!(this.treeControl?.isExpanded(dataNode) ||\n            this._expansionModel?.isSelected(this._getExpansionKey(dataNode)));\n    }\n    /** If the data node is currently expanded, collapse it. Otherwise, expand it. */\n    toggle(dataNode) {\n        if (this.treeControl) {\n            this.treeControl.toggle(dataNode);\n        }\n        else if (this._expansionModel) {\n            this._expansionModel.toggle(this._getExpansionKey(dataNode));\n        }\n    }\n    /** Expand the data node. If it is already expanded, does nothing. */\n    expand(dataNode) {\n        if (this.treeControl) {\n            this.treeControl.expand(dataNode);\n        }\n        else if (this._expansionModel) {\n            this._expansionModel.select(this._getExpansionKey(dataNode));\n        }\n    }\n    /** Collapse the data node. If it is already collapsed, does nothing. */\n    collapse(dataNode) {\n        if (this.treeControl) {\n            this.treeControl.collapse(dataNode);\n        }\n        else if (this._expansionModel) {\n            this._expansionModel.deselect(this._getExpansionKey(dataNode));\n        }\n    }\n    /**\n     * If the data node is currently expanded, collapse it and all its descendants.\n     * Otherwise, expand it and all its descendants.\n     */\n    toggleDescendants(dataNode) {\n        if (this.treeControl) {\n            this.treeControl.toggleDescendants(dataNode);\n        }\n        else if (this._expansionModel) {\n            if (this.isExpanded(dataNode)) {\n                this.collapseDescendants(dataNode);\n            }\n            else {\n                this.expandDescendants(dataNode);\n            }\n        }\n    }\n    /**\n     * Expand the data node and all its descendants. If they are already expanded, does nothing.\n     */\n    expandDescendants(dataNode) {\n        if (this.treeControl) {\n            this.treeControl.expandDescendants(dataNode);\n        }\n        else if (this._expansionModel) {\n            const expansionModel = this._expansionModel;\n            expansionModel.select(this._getExpansionKey(dataNode));\n            this._getDescendants(dataNode)\n                .pipe(take(1), takeUntil(this._onDestroy))\n                .subscribe(children => {\n                expansionModel.select(...children.map(child => this._getExpansionKey(child)));\n            });\n        }\n    }\n    /** Collapse the data node and all its descendants. If it is already collapsed, does nothing. */\n    collapseDescendants(dataNode) {\n        if (this.treeControl) {\n            this.treeControl.collapseDescendants(dataNode);\n        }\n        else if (this._expansionModel) {\n            const expansionModel = this._expansionModel;\n            expansionModel.deselect(this._getExpansionKey(dataNode));\n            this._getDescendants(dataNode)\n                .pipe(take(1), takeUntil(this._onDestroy))\n                .subscribe(children => {\n                expansionModel.deselect(...children.map(child => this._getExpansionKey(child)));\n            });\n        }\n    }\n    /** Expands all data nodes in the tree. */\n    expandAll() {\n        if (this.treeControl) {\n            this.treeControl.expandAll();\n        }\n        else if (this._expansionModel) {\n            this._forEachExpansionKey(keys => this._expansionModel?.select(...keys));\n        }\n    }\n    /** Collapse all data nodes in the tree. */\n    collapseAll() {\n        if (this.treeControl) {\n            this.treeControl.collapseAll();\n        }\n        else if (this._expansionModel) {\n            this._forEachExpansionKey(keys => this._expansionModel?.deselect(...keys));\n        }\n    }\n    /** Level accessor, used for compatibility between the old Tree and new Tree */\n    _getLevelAccessor() {\n        return this.treeControl?.getLevel?.bind(this.treeControl) ?? this.levelAccessor;\n    }\n    /** Children accessor, used for compatibility between the old Tree and new Tree */\n    _getChildrenAccessor() {\n        return this.treeControl?.getChildren?.bind(this.treeControl) ?? this.childrenAccessor;\n    }\n    /**\n     * Gets the direct children of a node; used for compatibility between the old tree and the\n     * new tree.\n     */\n    _getDirectChildren(dataNode) {\n        const levelAccessor = this._getLevelAccessor();\n        const expansionModel = this._expansionModel ?? this.treeControl?.expansionModel;\n        if (!expansionModel) {\n            return of([]);\n        }\n        const key = this._getExpansionKey(dataNode);\n        const isExpanded = expansionModel.changed.pipe(switchMap(changes => {\n            if (changes.added.includes(key)) {\n                return of(true);\n            }\n            else if (changes.removed.includes(key)) {\n                return of(false);\n            }\n            return EMPTY;\n        }), startWith(this.isExpanded(dataNode)));\n        if (levelAccessor) {\n            return combineLatest([isExpanded, this._flattenedNodes]).pipe(map(([expanded, flattenedNodes]) => {\n                if (!expanded) {\n                    return [];\n                }\n                return this._findChildrenByLevel(levelAccessor, flattenedNodes, dataNode, 1);\n            }));\n        }\n        const childrenAccessor = this._getChildrenAccessor();\n        if (childrenAccessor) {\n            return coerceObservable(childrenAccessor(dataNode) ?? []);\n        }\n        throw getTreeControlMissingError();\n    }\n    /**\n     * Given the list of flattened nodes, the level accessor, and the level range within\n     * which to consider children, finds the children for a given node.\n     *\n     * For example, for direct children, `levelDelta` would be 1. For all descendants,\n     * `levelDelta` would be Infinity.\n     */\n    _findChildrenByLevel(levelAccessor, flattenedNodes, dataNode, levelDelta) {\n        const key = this._getExpansionKey(dataNode);\n        const startIndex = flattenedNodes.findIndex(node => this._getExpansionKey(node) === key);\n        const dataNodeLevel = levelAccessor(dataNode);\n        const expectedLevel = dataNodeLevel + levelDelta;\n        const results = [];\n        // Goes through flattened tree nodes in the `flattenedNodes` array, and get all\n        // descendants within a certain level range.\n        //\n        // If we reach a node whose level is equal to or less than the level of the tree node,\n        // we hit a sibling or parent's sibling, and should stop.\n        for (let i = startIndex + 1; i < flattenedNodes.length; i++) {\n            const currentLevel = levelAccessor(flattenedNodes[i]);\n            if (currentLevel <= dataNodeLevel) {\n                break;\n            }\n            if (currentLevel <= expectedLevel) {\n                results.push(flattenedNodes[i]);\n            }\n        }\n        return results;\n    }\n    /**\n     * Adds the specified node component to the tree's internal registry.\n     *\n     * This primarily facilitates keyboard navigation.\n     */\n    _registerNode(node) {\n        this._nodes.value.set(this._getExpansionKey(node.data), node);\n        this._nodes.next(this._nodes.value);\n    }\n    /** Removes the specified node component from the tree's internal registry. */\n    _unregisterNode(node) {\n        this._nodes.value.delete(this._getExpansionKey(node.data));\n        this._nodes.next(this._nodes.value);\n    }\n    /**\n     * For the given node, determine the level where this node appears in the tree.\n     *\n     * This is intended to be used for `aria-level` but is 0-indexed.\n     */\n    _getLevel(node) {\n        return this._levels.get(this._getExpansionKey(node));\n    }\n    /**\n     * For the given node, determine the size of the parent's child set.\n     *\n     * This is intended to be used for `aria-setsize`.\n     */\n    _getSetSize(dataNode) {\n        const set = this._getAriaSet(dataNode);\n        return set.length;\n    }\n    /**\n     * For the given node, determine the index (starting from 1) of the node in its parent's child set.\n     *\n     * This is intended to be used for `aria-posinset`.\n     */\n    _getPositionInSet(dataNode) {\n        const set = this._getAriaSet(dataNode);\n        const key = this._getExpansionKey(dataNode);\n        return set.findIndex(node => this._getExpansionKey(node) === key) + 1;\n    }\n    /** Given a CdkTreeNode, gets the node that renders that node's parent's data. */\n    _getNodeParent(node) {\n        const parent = this._parents.get(this._getExpansionKey(node.data));\n        return parent && this._nodes.value.get(this._getExpansionKey(parent));\n    }\n    /** Given a CdkTreeNode, gets the nodes that renders that node's child data. */\n    _getNodeChildren(node) {\n        return this._getDirectChildren(node.data).pipe(map(children => children.reduce((nodes, child) => {\n            const value = this._nodes.value.get(this._getExpansionKey(child));\n            if (value) {\n                nodes.push(value);\n            }\n            return nodes;\n        }, [])));\n    }\n    /** `keydown` event handler; this just passes the event to the `TreeKeyManager`. */\n    _sendKeydownToKeyManager(event) {\n        // Only handle events directly on the tree or directly on one of the nodes, otherwise\n        // we risk interfering with events in the projected content (see #29828).\n        if (event.target === this._elementRef.nativeElement) {\n            this._keyManager.onKeydown(event);\n        }\n        else {\n            const nodes = this._nodes.getValue();\n            for (const [, node] of nodes) {\n                if (event.target === node._elementRef.nativeElement) {\n                    this._keyManager.onKeydown(event);\n                    break;\n                }\n            }\n        }\n    }\n    /** Gets all nested descendants of a given node. */\n    _getDescendants(dataNode) {\n        if (this.treeControl) {\n            return of(this.treeControl.getDescendants(dataNode));\n        }\n        if (this.levelAccessor) {\n            const results = this._findChildrenByLevel(this.levelAccessor, this._flattenedNodes.value, dataNode, Infinity);\n            return of(results);\n        }\n        if (this.childrenAccessor) {\n            return this._getAllChildrenRecursively(dataNode).pipe(reduce((allChildren, nextChildren) => {\n                allChildren.push(...nextChildren);\n                return allChildren;\n            }, []));\n        }\n        throw getTreeControlMissingError();\n    }\n    /**\n     * Gets all children and sub-children of the provided node.\n     *\n     * This will emit multiple times, in the order that the children will appear\n     * in the tree, and can be combined with a `reduce` operator.\n     */\n    _getAllChildrenRecursively(dataNode) {\n        if (!this.childrenAccessor) {\n            return of([]);\n        }\n        return coerceObservable(this.childrenAccessor(dataNode)).pipe(take(1), switchMap(children => {\n            // Here, we cache the parents of a particular child so that we can compute the levels.\n            for (const child of children) {\n                this._parents.set(this._getExpansionKey(child), dataNode);\n            }\n            return of(...children).pipe(concatMap(child => concat(of([child]), this._getAllChildrenRecursively(child))));\n        }));\n    }\n    _getExpansionKey(dataNode) {\n        // In the case that a key accessor function was not provided by the\n        // tree user, we'll default to using the node object itself as the key.\n        //\n        // This cast is safe since:\n        // - if an expansionKey is provided, TS will infer the type of K to be\n        //   the return type.\n        // - if it's not, then K will be defaulted to T.\n        return this.expansionKey?.(dataNode) ?? dataNode;\n    }\n    _getAriaSet(node) {\n        const key = this._getExpansionKey(node);\n        const parent = this._parents.get(key);\n        const parentKey = parent ? this._getExpansionKey(parent) : null;\n        const set = this._ariaSets.get(parentKey);\n        return set ?? [node];\n    }\n    /**\n     * Finds the parent for the given node. If this is a root node, this\n     * returns null. If we're unable to determine the parent, for example,\n     * if we don't have cached node data, this returns undefined.\n     */\n    _findParentForNode(node, index, cachedNodes) {\n        // In all cases, we have a mapping from node to level; all we need to do here is backtrack in\n        // our flattened list of nodes to determine the first node that's of a level lower than the\n        // provided node.\n        if (!cachedNodes.length) {\n            return null;\n        }\n        const currentLevel = this._levels.get(this._getExpansionKey(node)) ?? 0;\n        for (let parentIndex = index - 1; parentIndex >= 0; parentIndex--) {\n            const parentNode = cachedNodes[parentIndex];\n            const parentLevel = this._levels.get(this._getExpansionKey(parentNode)) ?? 0;\n            if (parentLevel < currentLevel) {\n                return parentNode;\n            }\n        }\n        return null;\n    }\n    /**\n     * Given a set of root nodes and the current node level, flattens any nested\n     * nodes into a single array.\n     *\n     * If any nodes are not expanded, then their children will not be added into the array.\n     * This will still traverse all nested children in order to build up our internal data\n     * models, but will not include them in the returned array.\n     */\n    _flattenNestedNodesWithExpansion(nodes, level = 0) {\n        const childrenAccessor = this._getChildrenAccessor();\n        // If we're using a level accessor, we don't need to flatten anything.\n        if (!childrenAccessor) {\n            return of([...nodes]);\n        }\n        return of(...nodes).pipe(concatMap(node => {\n            const parentKey = this._getExpansionKey(node);\n            if (!this._parents.has(parentKey)) {\n                this._parents.set(parentKey, null);\n            }\n            this._levels.set(parentKey, level);\n            const children = coerceObservable(childrenAccessor(node));\n            return concat(of([node]), children.pipe(take(1), tap(childNodes => {\n                this._ariaSets.set(parentKey, [...(childNodes ?? [])]);\n                for (const child of childNodes ?? []) {\n                    const childKey = this._getExpansionKey(child);\n                    this._parents.set(childKey, node);\n                    this._levels.set(childKey, level + 1);\n                }\n            }), switchMap(childNodes => {\n                if (!childNodes) {\n                    return of([]);\n                }\n                return this._flattenNestedNodesWithExpansion(childNodes, level + 1).pipe(map(nestedNodes => (this.isExpanded(node) ? nestedNodes : [])));\n            })));\n        }), reduce((results, children) => {\n            results.push(...children);\n            return results;\n        }, []));\n    }\n    /**\n     * Converts children for certain tree configurations.\n     *\n     * This also computes parent, level, and group data.\n     */\n    _computeRenderingData(nodes, nodeType) {\n        // The only situations where we have to convert children types is when\n        // they're mismatched; i.e. if the tree is using a childrenAccessor and the\n        // nodes are flat, or if the tree is using a levelAccessor and the nodes are\n        // nested.\n        if (this.childrenAccessor && nodeType === 'flat') {\n            // clear previously generated data so we don't keep end up retaining data overtime causing\n            // memory leaks.\n            this._clearPreviousCache();\n            // This flattens children into a single array.\n            this._ariaSets.set(null, [...nodes]);\n            return this._flattenNestedNodesWithExpansion(nodes).pipe(map(flattenedNodes => ({\n                renderNodes: flattenedNodes,\n                flattenedNodes,\n            })));\n        }\n        else if (this.levelAccessor && nodeType === 'nested') {\n            // In the nested case, we only look for root nodes. The CdkNestedNode\n            // itself will handle rendering each individual node's children.\n            const levelAccessor = this.levelAccessor;\n            return of(nodes.filter(node => levelAccessor(node) === 0)).pipe(map(rootNodes => ({\n                renderNodes: rootNodes,\n                flattenedNodes: nodes,\n            })), tap(({ flattenedNodes }) => {\n                this._calculateParents(flattenedNodes);\n            }));\n        }\n        else if (nodeType === 'flat') {\n            // In the case of a TreeControl, we know that the node type matches up\n            // with the TreeControl, and so no conversions are necessary. Otherwise,\n            // we've already confirmed that the data model matches up with the\n            // desired node type here.\n            return of({ renderNodes: nodes, flattenedNodes: nodes }).pipe(tap(({ flattenedNodes }) => {\n                this._calculateParents(flattenedNodes);\n            }));\n        }\n        else {\n            // clear previously generated data so we don't keep end up retaining data overtime causing\n            // memory leaks.\n            this._clearPreviousCache();\n            // For nested nodes, we still need to perform the node flattening in order\n            // to maintain our caches for various tree operations.\n            this._ariaSets.set(null, [...nodes]);\n            return this._flattenNestedNodesWithExpansion(nodes).pipe(map(flattenedNodes => ({\n                renderNodes: nodes,\n                flattenedNodes,\n            })));\n        }\n    }\n    _updateCachedData(flattenedNodes) {\n        this._flattenedNodes.next(flattenedNodes);\n    }\n    _updateKeyManagerItems(flattenedNodes) {\n        this._keyManagerNodes.next(flattenedNodes);\n    }\n    /** Traverse the flattened node data and compute parents, levels, and group data. */\n    _calculateParents(flattenedNodes) {\n        const levelAccessor = this._getLevelAccessor();\n        if (!levelAccessor) {\n            return;\n        }\n        // clear previously generated data so we don't keep end up retaining data overtime causing\n        // memory leaks.\n        this._clearPreviousCache();\n        for (let index = 0; index < flattenedNodes.length; index++) {\n            const dataNode = flattenedNodes[index];\n            const key = this._getExpansionKey(dataNode);\n            this._levels.set(key, levelAccessor(dataNode));\n            const parent = this._findParentForNode(dataNode, index, flattenedNodes);\n            this._parents.set(key, parent);\n            const parentKey = parent ? this._getExpansionKey(parent) : null;\n            const group = this._ariaSets.get(parentKey) ?? [];\n            group.splice(index, 0, dataNode);\n            this._ariaSets.set(parentKey, group);\n        }\n    }\n    /** Invokes a callback with all node expansion keys. */\n    _forEachExpansionKey(callback) {\n        const toToggle = [];\n        const observables = [];\n        this._nodes.value.forEach(node => {\n            toToggle.push(this._getExpansionKey(node.data));\n            observables.push(this._getDescendants(node.data));\n        });\n        if (observables.length > 0) {\n            combineLatest(observables)\n                .pipe(take(1), takeUntil(this._onDestroy))\n                .subscribe(results => {\n                results.forEach(inner => inner.forEach(r => toToggle.push(this._getExpansionKey(r))));\n                callback(toToggle);\n            });\n        }\n        else {\n            callback(toToggle);\n        }\n    }\n    /** Clears the maps we use to store parents, level & aria-sets in. */\n    _clearPreviousCache() {\n        this._parents.clear();\n        this._levels.clear();\n        this._ariaSets.clear();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTree, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkTree, isStandalone: true, selector: \"cdk-tree\", inputs: { dataSource: \"dataSource\", treeControl: \"treeControl\", levelAccessor: \"levelAccessor\", childrenAccessor: \"childrenAccessor\", trackBy: \"trackBy\", expansionKey: \"expansionKey\" }, host: { attributes: { \"role\": \"tree\" }, listeners: { \"keydown\": \"_sendKeydownToKeyManager($event)\" }, classAttribute: \"cdk-tree\" }, queries: [{ propertyName: \"_nodeDefs\", predicate: CdkTreeNodeDef, descendants: true }], viewQueries: [{ propertyName: \"_nodeOutlet\", first: true, predicate: CdkTreeNodeOutlet, descendants: true, static: true }], exportAs: [\"cdkTree\"], ngImport: i0, template: `<ng-container cdkTreeNodeOutlet></ng-container>`, isInline: true, dependencies: [{ kind: \"directive\", type: CdkTreeNodeOutlet, selector: \"[cdkTreeNodeOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTree, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-tree',\n                    exportAs: 'cdkTree',\n                    template: `<ng-container cdkTreeNodeOutlet></ng-container>`,\n                    host: {\n                        'class': 'cdk-tree',\n                        'role': 'tree',\n                        '(keydown)': '_sendKeydownToKeyManager($event)',\n                    },\n                    encapsulation: ViewEncapsulation.None,\n                    // The \"OnPush\" status for the `CdkTree` component is effectively a noop, so we are removing it.\n                    // The view for `CdkTree` consists entirely of templates declared in other views. As they are\n                    // declared elsewhere, they are checked when their declaration points are checked.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    imports: [CdkTreeNodeOutlet],\n                }]\n        }], ctorParameters: () => [], propDecorators: { dataSource: [{\n                type: Input\n            }], treeControl: [{\n                type: Input\n            }], levelAccessor: [{\n                type: Input\n            }], childrenAccessor: [{\n                type: Input\n            }], trackBy: [{\n                type: Input\n            }], expansionKey: [{\n                type: Input\n            }], _nodeOutlet: [{\n                type: ViewChild,\n                args: [CdkTreeNodeOutlet, { static: true }]\n            }], _nodeDefs: [{\n                type: ContentChildren,\n                args: [CdkTreeNodeDef, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n/**\n * Tree node for CdkTree. It contains the data in the tree node.\n */\nclass CdkTreeNode {\n    _elementRef = inject(ElementRef);\n    _tree = inject(CdkTree);\n    _tabindex = -1;\n    _type = 'flat';\n    /**\n     * The role of the tree node.\n     *\n     * @deprecated This will be ignored; the tree will automatically determine the appropriate role\n     * for tree node. This input will be removed in a future version.\n     * @breaking-change 21.0.0\n     */\n    get role() {\n        return 'treeitem';\n    }\n    set role(_role) {\n        // ignore any role setting, we handle this internally.\n    }\n    /**\n     * Whether or not this node is expandable.\n     *\n     * If not using `FlatTreeControl`, or if `isExpandable` is not provided to\n     * `NestedTreeControl`, this should be provided for correct node a11y.\n     */\n    get isExpandable() {\n        return this._isExpandable();\n    }\n    set isExpandable(isExpandable) {\n        this._inputIsExpandable = isExpandable;\n        if ((this.data && !this._isExpandable) || !this._inputIsExpandable) {\n            return;\n        }\n        // If the node is being set to expandable, ensure that the status of the\n        // node is propagated\n        if (this._inputIsExpanded) {\n            this.expand();\n        }\n        else if (this._inputIsExpanded === false) {\n            this.collapse();\n        }\n    }\n    get isExpanded() {\n        return this._tree.isExpanded(this._data);\n    }\n    set isExpanded(isExpanded) {\n        this._inputIsExpanded = isExpanded;\n        if (isExpanded) {\n            this.expand();\n        }\n        else {\n            this.collapse();\n        }\n    }\n    /**\n     * Whether or not this node is disabled. If it's disabled, then the user won't be able to focus\n     * or activate this node.\n     */\n    isDisabled;\n    /**\n     * The text used to locate this item during typeahead. If not specified, the `textContent` will\n     * will be used.\n     */\n    typeaheadLabel;\n    getLabel() {\n        return this.typeaheadLabel || this._elementRef.nativeElement.textContent?.trim() || '';\n    }\n    /** This emits when the node has been programatically activated or activated by keyboard. */\n    activation = new EventEmitter();\n    /** This emits when the node's expansion status has been changed. */\n    expandedChange = new EventEmitter();\n    /**\n     * The most recently created `CdkTreeNode`. We save it in static variable so we can retrieve it\n     * in `CdkTree` and set the data to it.\n     */\n    static mostRecentTreeNode = null;\n    /** Subject that emits when the component has been destroyed. */\n    _destroyed = new Subject();\n    /** Emits when the node's data has changed. */\n    _dataChanges = new Subject();\n    _inputIsExpandable = false;\n    _inputIsExpanded = undefined;\n    /**\n     * Flag used to determine whether or not we should be focusing the actual element based on\n     * some user interaction (click or focus). On click, we don't forcibly focus the element\n     * since the click could trigger some other component that wants to grab its own focus\n     * (e.g. menu, dialog).\n     */\n    _shouldFocus = true;\n    _parentNodeAriaLevel;\n    /** The tree node's data. */\n    get data() {\n        return this._data;\n    }\n    set data(value) {\n        if (value !== this._data) {\n            this._data = value;\n            this._dataChanges.next();\n        }\n    }\n    _data;\n    /* If leaf node, return true to not assign aria-expanded attribute */\n    get isLeafNode() {\n        // If flat tree node data returns false for expandable property, it's a leaf node\n        if (this._tree.treeControl?.isExpandable !== undefined &&\n            !this._tree.treeControl.isExpandable(this._data)) {\n            return true;\n            // If nested tree node data returns 0 descendants, it's a leaf node\n        }\n        else if (this._tree.treeControl?.isExpandable === undefined &&\n            this._tree.treeControl?.getDescendants(this._data).length === 0) {\n            return true;\n        }\n        return false;\n    }\n    get level() {\n        // If the tree has a levelAccessor, use it to get the level. Otherwise read the\n        // aria-level off the parent node and use it as the level for this node (note aria-level is\n        // 1-indexed, while this property is 0-indexed, so we don't need to increment).\n        return this._tree._getLevel(this._data) ?? this._parentNodeAriaLevel;\n    }\n    /** Determines if the tree node is expandable. */\n    _isExpandable() {\n        if (this._tree.treeControl) {\n            if (this.isLeafNode) {\n                return false;\n            }\n            // For compatibility with trees created using TreeControl before we added\n            // CdkTreeNode#isExpandable.\n            return true;\n        }\n        return this._inputIsExpandable;\n    }\n    /**\n     * Determines the value for `aria-expanded`.\n     *\n     * For non-expandable nodes, this is `null`.\n     */\n    _getAriaExpanded() {\n        if (!this._isExpandable()) {\n            return null;\n        }\n        return String(this.isExpanded);\n    }\n    /**\n     * Determines the size of this node's parent's child set.\n     *\n     * This is intended to be used for `aria-setsize`.\n     */\n    _getSetSize() {\n        return this._tree._getSetSize(this._data);\n    }\n    /**\n     * Determines the index (starting from 1) of this node in its parent's child set.\n     *\n     * This is intended to be used for `aria-posinset`.\n     */\n    _getPositionInSet() {\n        return this._tree._getPositionInSet(this._data);\n    }\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    constructor() {\n        CdkTreeNode.mostRecentTreeNode = this;\n    }\n    ngOnInit() {\n        this._parentNodeAriaLevel = getParentNodeAriaLevel(this._elementRef.nativeElement);\n        this._tree\n            ._getExpansionModel()\n            .changed.pipe(map(() => this.isExpanded), distinctUntilChanged())\n            .subscribe(() => this._changeDetectorRef.markForCheck());\n        this._tree._setNodeTypeIfUnset(this._type);\n        this._tree._registerNode(this);\n    }\n    ngOnDestroy() {\n        // If this is the last tree node being destroyed,\n        // clear out the reference to avoid leaking memory.\n        if (CdkTreeNode.mostRecentTreeNode === this) {\n            CdkTreeNode.mostRecentTreeNode = null;\n        }\n        this._dataChanges.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    getParent() {\n        return this._tree._getNodeParent(this) ?? null;\n    }\n    getChildren() {\n        return this._tree._getNodeChildren(this);\n    }\n    /** Focuses this data node. Implemented for TreeKeyManagerItem. */\n    focus() {\n        this._tabindex = 0;\n        if (this._shouldFocus) {\n            this._elementRef.nativeElement.focus();\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Defocus this data node. */\n    unfocus() {\n        this._tabindex = -1;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits an activation event. Implemented for TreeKeyManagerItem. */\n    activate() {\n        if (this.isDisabled) {\n            return;\n        }\n        this.activation.next(this._data);\n    }\n    /** Collapses this data node. Implemented for TreeKeyManagerItem. */\n    collapse() {\n        if (this.isExpandable) {\n            this._tree.collapse(this._data);\n        }\n    }\n    /** Expands this data node. Implemented for TreeKeyManagerItem. */\n    expand() {\n        if (this.isExpandable) {\n            this._tree.expand(this._data);\n        }\n    }\n    /** Makes the node focusable. Implemented for TreeKeyManagerItem. */\n    makeFocusable() {\n        this._tabindex = 0;\n        this._changeDetectorRef.markForCheck();\n    }\n    _focusItem() {\n        if (this.isDisabled) {\n            return;\n        }\n        this._tree._keyManager.focusItem(this);\n    }\n    _setActiveItem() {\n        if (this.isDisabled) {\n            return;\n        }\n        this._shouldFocus = false;\n        this._tree._keyManager.focusItem(this);\n        this._shouldFocus = true;\n    }\n    _emitExpansionState(expanded) {\n        this.expandedChange.emit(expanded);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeNode, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkTreeNode, isStandalone: true, selector: \"cdk-tree-node\", inputs: { role: \"role\", isExpandable: [\"isExpandable\", \"isExpandable\", booleanAttribute], isExpanded: \"isExpanded\", isDisabled: [\"isDisabled\", \"isDisabled\", booleanAttribute], typeaheadLabel: [\"cdkTreeNodeTypeaheadLabel\", \"typeaheadLabel\"] }, outputs: { activation: \"activation\", expandedChange: \"expandedChange\" }, host: { attributes: { \"role\": \"treeitem\" }, listeners: { \"click\": \"_setActiveItem()\", \"focus\": \"_focusItem()\" }, properties: { \"attr.aria-expanded\": \"_getAriaExpanded()\", \"attr.aria-level\": \"level + 1\", \"attr.aria-posinset\": \"_getPositionInSet()\", \"attr.aria-setsize\": \"_getSetSize()\", \"tabindex\": \"_tabindex\" }, classAttribute: \"cdk-tree-node\" }, exportAs: [\"cdkTreeNode\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeNode, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-tree-node',\n                    exportAs: 'cdkTreeNode',\n                    host: {\n                        'class': 'cdk-tree-node',\n                        '[attr.aria-expanded]': '_getAriaExpanded()',\n                        '[attr.aria-level]': 'level + 1',\n                        '[attr.aria-posinset]': '_getPositionInSet()',\n                        '[attr.aria-setsize]': '_getSetSize()',\n                        '[tabindex]': '_tabindex',\n                        'role': 'treeitem',\n                        '(click)': '_setActiveItem()',\n                        '(focus)': '_focusItem()',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { role: [{\n                type: Input\n            }], isExpandable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], isExpanded: [{\n                type: Input\n            }], isDisabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], typeaheadLabel: [{\n                type: Input,\n                args: ['cdkTreeNodeTypeaheadLabel']\n            }], activation: [{\n                type: Output\n            }], expandedChange: [{\n                type: Output\n            }] } });\nfunction getParentNodeAriaLevel(nodeElement) {\n    let parent = nodeElement.parentElement;\n    while (parent && !isNodeElement(parent)) {\n        parent = parent.parentElement;\n    }\n    if (!parent) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw Error('Incorrect tree structure containing detached node.');\n        }\n        else {\n            return -1;\n        }\n    }\n    else if (parent.classList.contains('cdk-nested-tree-node')) {\n        return numberAttribute(parent.getAttribute('aria-level'));\n    }\n    else {\n        // The ancestor element is the cdk-tree itself\n        return 0;\n    }\n}\nfunction isNodeElement(element) {\n    const classList = element.classList;\n    return !!(classList?.contains('cdk-nested-tree-node') || classList?.contains('cdk-tree'));\n}\n\n/**\n * Nested node is a child of `<cdk-tree>`. It works with nested tree.\n * By using `cdk-nested-tree-node` component in tree node template, children of the parent node will\n * be added in the `cdkTreeNodeOutlet` in tree node template.\n * The children of node will be automatically added to `cdkTreeNodeOutlet`.\n */\nclass CdkNestedTreeNode extends CdkTreeNode {\n    _type = 'nested';\n    _differs = inject(IterableDiffers);\n    /** Differ used to find the changes in the data provided by the data source. */\n    _dataDiffer;\n    /** The children data dataNodes of current node. They will be placed in `CdkTreeNodeOutlet`. */\n    _children;\n    /** The children node placeholder. */\n    nodeOutlet;\n    constructor() {\n        super();\n    }\n    ngAfterContentInit() {\n        this._dataDiffer = this._differs.find([]).create(this._tree.trackBy);\n        this._tree\n            ._getDirectChildren(this.data)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(result => this.updateChildrenNodes(result));\n        this.nodeOutlet.changes\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this.updateChildrenNodes());\n    }\n    ngOnDestroy() {\n        this._clear();\n        super.ngOnDestroy();\n    }\n    /** Add children dataNodes to the NodeOutlet */\n    updateChildrenNodes(children) {\n        const outlet = this._getNodeOutlet();\n        if (children) {\n            this._children = children;\n        }\n        if (outlet && this._children) {\n            const viewContainer = outlet.viewContainer;\n            this._tree.renderNodeChanges(this._children, this._dataDiffer, viewContainer, this._data);\n        }\n        else {\n            // Reset the data differ if there's no children nodes displayed\n            this._dataDiffer.diff([]);\n        }\n    }\n    /** Clear the children dataNodes. */\n    _clear() {\n        const outlet = this._getNodeOutlet();\n        if (outlet) {\n            outlet.viewContainer.clear();\n            this._dataDiffer.diff([]);\n        }\n    }\n    /** Gets the outlet for the current node. */\n    _getNodeOutlet() {\n        const outlets = this.nodeOutlet;\n        // Note that since we use `descendants: true` on the query, we have to ensure\n        // that we don't pick up the outlet of a child node by accident.\n        return outlets && outlets.find(outlet => !outlet._node || outlet._node === this);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkNestedTreeNode, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkNestedTreeNode, isStandalone: true, selector: \"cdk-nested-tree-node\", host: { classAttribute: \"cdk-nested-tree-node\" }, providers: [\n            { provide: CdkTreeNode, useExisting: CdkNestedTreeNode },\n            { provide: CDK_TREE_NODE_OUTLET_NODE, useExisting: CdkNestedTreeNode },\n        ], queries: [{ propertyName: \"nodeOutlet\", predicate: CdkTreeNodeOutlet, descendants: true }], exportAs: [\"cdkNestedTreeNode\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkNestedTreeNode, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-nested-tree-node',\n                    exportAs: 'cdkNestedTreeNode',\n                    providers: [\n                        { provide: CdkTreeNode, useExisting: CdkNestedTreeNode },\n                        { provide: CDK_TREE_NODE_OUTLET_NODE, useExisting: CdkNestedTreeNode },\n                    ],\n                    host: {\n                        'class': 'cdk-nested-tree-node',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { nodeOutlet: [{\n                type: ContentChildren,\n                args: [CdkTreeNodeOutlet, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * Indent for the children tree dataNodes.\n * This directive will add left-padding to the node to show hierarchy.\n */\nclass CdkTreeNodePadding {\n    _treeNode = inject(CdkTreeNode);\n    _tree = inject(CdkTree);\n    _element = inject(ElementRef);\n    _dir = inject(Directionality, { optional: true });\n    /** Current padding value applied to the element. Used to avoid unnecessarily hitting the DOM. */\n    _currentPadding;\n    /** Subject that emits when the component has been destroyed. */\n    _destroyed = new Subject();\n    /** CSS units used for the indentation value. */\n    indentUnits = 'px';\n    /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n    get level() {\n        return this._level;\n    }\n    set level(value) {\n        this._setLevelInput(value);\n    }\n    _level;\n    /**\n     * The indent for each level. Can be a number or a CSS string.\n     * Default number 40px from material design menu sub-menu spec.\n     */\n    get indent() {\n        return this._indent;\n    }\n    set indent(indent) {\n        this._setIndentInput(indent);\n    }\n    _indent = 40;\n    constructor() {\n        this._setPadding();\n        this._dir?.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._setPadding(true));\n        // In Ivy the indentation binding might be set before the tree node's data has been added,\n        // which means that we'll miss the first render. We have to subscribe to changes in the\n        // data to ensure that everything is up to date.\n        this._treeNode._dataChanges.subscribe(() => this._setPadding());\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** The padding indent value for the tree node. Returns a string with px numbers if not null. */\n    _paddingIndent() {\n        const nodeLevel = (this._treeNode.data && this._tree._getLevel(this._treeNode.data)) ?? null;\n        const level = this._level == null ? nodeLevel : this._level;\n        return typeof level === 'number' ? `${level * this._indent}${this.indentUnits}` : null;\n    }\n    _setPadding(forceChange = false) {\n        const padding = this._paddingIndent();\n        if (padding !== this._currentPadding || forceChange) {\n            const element = this._element.nativeElement;\n            const paddingProp = this._dir && this._dir.value === 'rtl' ? 'paddingRight' : 'paddingLeft';\n            const resetProp = paddingProp === 'paddingLeft' ? 'paddingRight' : 'paddingLeft';\n            element.style[paddingProp] = padding || '';\n            element.style[resetProp] = '';\n            this._currentPadding = padding;\n        }\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setLevelInput(value) {\n        // Set to null as the fallback value so that _setPadding can fall back to the node level if the\n        // consumer set the directive as `cdkTreeNodePadding=\"\"`. We still want to take this value if\n        // they set 0 explicitly.\n        this._level = isNaN(value) ? null : value;\n        this._setPadding();\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setIndentInput(indent) {\n        let value = indent;\n        let units = 'px';\n        if (typeof indent === 'string') {\n            const parts = indent.split(cssUnitPattern);\n            value = parts[0];\n            units = parts[1] || units;\n        }\n        this.indentUnits = units;\n        this._indent = numberAttribute(value);\n        this._setPadding();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeNodePadding, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkTreeNodePadding, isStandalone: true, selector: \"[cdkTreeNodePadding]\", inputs: { level: [\"cdkTreeNodePadding\", \"level\", numberAttribute], indent: [\"cdkTreeNodePaddingIndent\", \"indent\"] }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeNodePadding, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTreeNodePadding]',\n                }]\n        }], ctorParameters: () => [], propDecorators: { level: [{\n                type: Input,\n                args: [{ alias: 'cdkTreeNodePadding', transform: numberAttribute }]\n            }], indent: [{\n                type: Input,\n                args: ['cdkTreeNodePaddingIndent']\n            }] } });\n\n/**\n * Node toggle to expand and collapse the node.\n */\nclass CdkTreeNodeToggle {\n    _tree = inject(CdkTree);\n    _treeNode = inject(CdkTreeNode);\n    /** Whether expand/collapse the node recursively. */\n    recursive = false;\n    constructor() { }\n    // Toggle the expanded or collapsed state of this node.\n    //\n    // Focus this node with expanding or collapsing it. This ensures that the active node will always\n    // be visible when expanding and collapsing.\n    _toggle() {\n        this.recursive\n            ? this._tree.toggleDescendants(this._treeNode.data)\n            : this._tree.toggle(this._treeNode.data);\n        this._tree._keyManager.focusItem(this._treeNode);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeNodeToggle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkTreeNodeToggle, isStandalone: true, selector: \"[cdkTreeNodeToggle]\", inputs: { recursive: [\"cdkTreeNodeToggleRecursive\", \"recursive\", booleanAttribute] }, host: { attributes: { \"tabindex\": \"-1\" }, listeners: { \"click\": \"_toggle(); $event.stopPropagation();\", \"keydown.Enter\": \"_toggle(); $event.preventDefault();\", \"keydown.Space\": \"_toggle(); $event.preventDefault();\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeNodeToggle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTreeNodeToggle]',\n                    host: {\n                        '(click)': '_toggle(); $event.stopPropagation();',\n                        '(keydown.Enter)': '_toggle(); $event.preventDefault();',\n                        '(keydown.Space)': '_toggle(); $event.preventDefault();',\n                        'tabindex': '-1',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { recursive: [{\n                type: Input,\n                args: [{ alias: 'cdkTreeNodeToggleRecursive', transform: booleanAttribute }]\n            }] } });\n\nconst EXPORTED_DECLARATIONS = [\n    CdkNestedTreeNode,\n    CdkTreeNodeDef,\n    CdkTreeNodePadding,\n    CdkTreeNodeToggle,\n    CdkTree,\n    CdkTreeNode,\n    CdkTreeNodeOutlet,\n];\nclass CdkTreeModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeModule, imports: [CdkNestedTreeNode,\n            CdkTreeNodeDef,\n            CdkTreeNodePadding,\n            CdkTreeNodeToggle,\n            CdkTree,\n            CdkTreeNode,\n            CdkTreeNodeOutlet], exports: [CdkNestedTreeNode,\n            CdkTreeNodeDef,\n            CdkTreeNodePadding,\n            CdkTreeNodeToggle,\n            CdkTree,\n            CdkTreeNode,\n            CdkTreeNodeOutlet] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTreeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: EXPORTED_DECLARATIONS,\n                    exports: EXPORTED_DECLARATIONS,\n                }]\n        }] });\n\nexport { BaseTreeControl, CDK_TREE_NODE_OUTLET_NODE, CdkNestedTreeNode, CdkTree, CdkTreeModule, CdkTreeNode, CdkTreeNodeDef, CdkTreeNodeOutlet, CdkTreeNodeOutletContext, CdkTreeNodePadding, CdkTreeNodeToggle, FlatTreeControl, NestedTreeControl, getMultipleTreeControlsError, getTreeControlMissingError, getTreeMissingMatchingNodeDefError, getTreeMultipleDefaultNodeDefsError, getTreeNoValidDataSourceError };\n//# sourceMappingURL=tree.mjs.map\n"], "mappings": ";;;AAAA,SAASA,CAAC,IAAIC,cAAc,QAAQ,gCAAgC;AACpE,SAASC,YAAY,EAAEC,OAAO,EAAEC,eAAe,EAAEC,EAAE,EAAEC,aAAa,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC/F,SAASC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,EAAEC,SAAS,EAAEC,oBAAoB,QAAQ,gBAAgB;AACjI,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC7S,SAASC,CAAC,IAAIC,gBAAgB,QAAQ,iCAAiC;AACvE,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,SAASC,CAAC,IAAIC,YAAY,QAAQ,4BAA4B;AAC9D,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,OAAO,0BAA0B;AACjC,OAAO,yBAAyB;AAChC,OAAO,iBAAiB;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAAAC,YAAA;IAClB;IAAAC,eAAA;IAEA;IAAAA,eAAA,yBACiB,IAAI/C,cAAc,CAAC,IAAI,CAAC;IACzC;AACJ;AACA;AACA;AACA;AACA;IALI+C,eAAA;IAOA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;EAAA;EAEA;EACAC,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACC,cAAc,CAACF,MAAM,CAAC,IAAI,CAACG,aAAa,CAACF,QAAQ,CAAC,CAAC;EAC5D;EACA;EACAG,MAAMA,CAACH,QAAQ,EAAE;IACb,IAAI,CAACC,cAAc,CAACG,MAAM,CAAC,IAAI,CAACF,aAAa,CAACF,QAAQ,CAAC,CAAC;EAC5D;EACA;EACAK,QAAQA,CAACL,QAAQ,EAAE;IACf,IAAI,CAACC,cAAc,CAACK,QAAQ,CAAC,IAAI,CAACJ,aAAa,CAACF,QAAQ,CAAC,CAAC;EAC9D;EACA;EACAO,UAAUA,CAACP,QAAQ,EAAE;IACjB,OAAO,IAAI,CAACC,cAAc,CAACO,UAAU,CAAC,IAAI,CAACN,aAAa,CAACF,QAAQ,CAAC,CAAC;EACvE;EACA;EACAS,iBAAiBA,CAACT,QAAQ,EAAE;IACxB,IAAI,CAACC,cAAc,CAACO,UAAU,CAAC,IAAI,CAACN,aAAa,CAACF,QAAQ,CAAC,CAAC,GACtD,IAAI,CAACU,mBAAmB,CAACV,QAAQ,CAAC,GAClC,IAAI,CAACW,iBAAiB,CAACX,QAAQ,CAAC;EAC1C;EACA;EACAY,WAAWA,CAAA,EAAG;IACV,IAAI,CAACX,cAAc,CAACY,KAAK,CAAC,CAAC;EAC/B;EACA;EACAF,iBAAiBA,CAACX,QAAQ,EAAE;IACxB,IAAIc,aAAa,GAAG,CAACd,QAAQ,CAAC;IAC9Bc,aAAa,CAACC,IAAI,CAAC,GAAG,IAAI,CAACC,cAAc,CAAChB,QAAQ,CAAC,CAAC;IACpD,IAAI,CAACC,cAAc,CAACG,MAAM,CAAC,GAAGU,aAAa,CAACjD,GAAG,CAACoD,KAAK,IAAI,IAAI,CAACf,aAAa,CAACe,KAAK,CAAC,CAAC,CAAC;EACxF;EACA;EACAP,mBAAmBA,CAACV,QAAQ,EAAE;IAC1B,IAAIc,aAAa,GAAG,CAACd,QAAQ,CAAC;IAC9Bc,aAAa,CAACC,IAAI,CAAC,GAAG,IAAI,CAACC,cAAc,CAAChB,QAAQ,CAAC,CAAC;IACpD,IAAI,CAACC,cAAc,CAACK,QAAQ,CAAC,GAAGQ,aAAa,CAACjD,GAAG,CAACoD,KAAK,IAAI,IAAI,CAACf,aAAa,CAACe,KAAK,CAAC,CAAC,CAAC;EAC1F;EACAf,aAAaA,CAACe,KAAK,EAAE;IACjB,OAAO,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACD,KAAK,CAAC,GAAGA,KAAK;EACrD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,eAAe,SAASvB,eAAe,CAAC;EAI1C;EACAC,WAAWA,CAACuB,QAAQ,EAAEC,YAAY,EAAEC,OAAO,EAAE;IACzC,KAAK,CAAC,CAAC;IAACxB,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACR,IAAI,CAACsB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,EAAE;MACd,IAAI,CAACJ,OAAO,GAAG,IAAI,CAACI,OAAO,CAACJ,OAAO;IACvC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,cAAcA,CAAChB,QAAQ,EAAE;IACrB,MAAMuB,UAAU,GAAG,IAAI,CAACC,SAAS,CAACC,OAAO,CAACzB,QAAQ,CAAC;IACnD,MAAM0B,OAAO,GAAG,EAAE;IAClB;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,IAAIjC,CAAC,GAAG8B,UAAU,GAAG,CAAC,EAAE9B,CAAC,GAAG,IAAI,CAAC+B,SAAS,CAACG,MAAM,IAAI,IAAI,CAACP,QAAQ,CAACpB,QAAQ,CAAC,GAAG,IAAI,CAACoB,QAAQ,CAAC,IAAI,CAACI,SAAS,CAAC/B,CAAC,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvHiC,OAAO,CAACX,IAAI,CAAC,IAAI,CAACS,SAAS,CAAC/B,CAAC,CAAC,CAAC;IACnC;IACA,OAAOiC,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,SAASA,CAAA,EAAG;IACR,IAAI,CAAC3B,cAAc,CAACG,MAAM,CAAC,GAAG,IAAI,CAACoB,SAAS,CAAC3D,GAAG,CAACgE,IAAI,IAAI,IAAI,CAAC3B,aAAa,CAAC2B,IAAI,CAAC,CAAC,CAAC;EACvF;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,SAASlC,eAAe,CAAC;EAG5C;EACAC,WAAWA,CAACkC,WAAW,EAAET,OAAO,EAAE;IAAA,IAAAU,aAAA;IAC9B,KAAK,CAAC,CAAC;IAAClC,eAAA;IAAAA,eAAA;IACR,IAAI,CAACiC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACT,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,EAAE;MACd,IAAI,CAACJ,OAAO,GAAG,IAAI,CAACI,OAAO,CAACJ,OAAO;IACvC;IACA,KAAAc,aAAA,GAAI,IAAI,CAACV,OAAO,cAAAU,aAAA,eAAZA,aAAA,CAAcX,YAAY,EAAE;MAC5B,IAAI,CAACA,YAAY,GAAG,IAAI,CAACC,OAAO,CAACD,YAAY;IACjD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIO,SAASA,CAAA,EAAG;IACR,IAAI,CAAC3B,cAAc,CAACY,KAAK,CAAC,CAAC;IAC3B,MAAMoB,QAAQ,GAAG,IAAI,CAACT,SAAS,CAAC1D,MAAM,CAAC,CAACoE,WAAW,EAAElC,QAAQ,KAAK,CAAC,GAAGkC,WAAW,EAAE,GAAG,IAAI,CAAClB,cAAc,CAAChB,QAAQ,CAAC,EAAEA,QAAQ,CAAC,EAAE,EAAE,CAAC;IACnI,IAAI,CAACC,cAAc,CAACG,MAAM,CAAC,GAAG6B,QAAQ,CAACpE,GAAG,CAACgE,IAAI,IAAI,IAAI,CAAC3B,aAAa,CAAC2B,IAAI,CAAC,CAAC,CAAC;EACjF;EACA;EACAb,cAAcA,CAAChB,QAAQ,EAAE;IACrB,MAAMmC,WAAW,GAAG,EAAE;IACtB,IAAI,CAACC,eAAe,CAACD,WAAW,EAAEnC,QAAQ,CAAC;IAC3C;IACA,OAAOmC,WAAW,CAACE,MAAM,CAAC,CAAC,CAAC;EAChC;EACA;EACAD,eAAeA,CAACD,WAAW,EAAEnC,QAAQ,EAAE;IACnCmC,WAAW,CAACpB,IAAI,CAACf,QAAQ,CAAC;IAC1B,MAAMsC,aAAa,GAAG,IAAI,CAACP,WAAW,CAAC/B,QAAQ,CAAC;IAChD,IAAIuC,KAAK,CAACC,OAAO,CAACF,aAAa,CAAC,EAAE;MAC9BA,aAAa,CAACG,OAAO,CAAEC,KAAK,IAAK,IAAI,CAACN,eAAe,CAACD,WAAW,EAAEO,KAAK,CAAC,CAAC;IAC9E,CAAC,MACI,IAAI1F,YAAY,CAACsF,aAAa,CAAC,EAAE;MAClC;MACA;MACAA,aAAa,CAACK,IAAI,CAACpF,IAAI,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACoF,OAAO,CAAC,CAAC,CAACC,SAAS,CAACC,QAAQ,IAAI;QAC/D,KAAK,MAAMJ,KAAK,IAAII,QAAQ,EAAE;UAC1B,IAAI,CAACV,eAAe,CAACD,WAAW,EAAEO,KAAK,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMK,yBAAyB,GAAG,IAAI7E,cAAc,CAAC,2BAA2B,CAAC;AACjF;AACA;AACA;AACA;AACA,MAAM8E,iBAAiB,CAAC;EAGpBnD,WAAWA,CAAA,EAAG;IAAAC,eAAA,wBAFE3B,MAAM,CAACC,gBAAgB,CAAC;IAAA0B,eAAA,gBAChC3B,MAAM,CAAC4E,yBAAyB,EAAE;MAAEE,QAAQ,EAAE;IAAK,CAAC,CAAC;EAC7C;AAGpB;AAACC,kBAAA,GANKF,iBAAiB;AAAAlD,eAAA,CAAjBkD,iBAAiB,wBAAAG,2BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAIgFJ,kBAAiB;AAAA;AAAAlD,eAAA,CAJlHkD,iBAAiB,8BAO0D/E,EAAE,CAAAoF,iBAAA;EAAAC,IAAA,EAFQN,kBAAiB;EAAAO,SAAA;AAAA;AAE5G;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFvF,EAAE,CAAAwF,iBAAA,CAAQT,iBAAiB,EAAc,CAAC;IAC/GM,IAAI,EAAEjF,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAMC,wBAAwB,CAAC;EAS3B/D,WAAWA,CAACgE,IAAI,EAAE;IARlB;IAAA/D,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAGI,IAAI,CAACgE,SAAS,GAAGD,IAAI;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA,MAAME,cAAc,CAAC;EAWjBlE,WAAWA,CAAA,EAAG;IAVd;IAAAC,eAAA,mBACW3B,MAAM,CAACG,WAAW,CAAC;IAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;IANIwB,eAAA;EAQgB;AAGpB;AAACkE,eAAA,GAdKD,cAAc;AAAAjE,eAAA,CAAdiE,cAAc,wBAAAE,wBAAAb,iBAAA;EAAA,YAAAA,iBAAA,IAYmFW,eAAc;AAAA;AAAAjE,eAAA,CAZ/GiE,cAAc,8BAzB6D9F,EAAE,CAAAoF,iBAAA;EAAAC,IAAA,EAsCQS,eAAc;EAAAR,SAAA;EAAAW,MAAA;IAAAC,IAAA;EAAA;AAAA;AAEzG;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KAxCiFvF,EAAE,CAAAwF,iBAAA,CAwCQM,cAAc,EAAc,CAAC;IAC5GT,IAAI,EAAEjF,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BO,MAAM,EAAE,CAAC;QAAEE,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAqB,CAAC;IAC1D,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAAA,EAAG;EACrC,OAAOC,KAAK,CAAC,uCAAuC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,SAASC,mCAAmCA,CAAA,EAAG;EAC3C,OAAOD,KAAK,CAAC,sEAAsE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,SAASE,kCAAkCA,CAAA,EAAG;EAC1C,OAAOF,KAAK,CAAC,uEAAuE,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA,SAASG,0BAA0BA,CAAA,EAAG;EAClC,OAAOH,KAAK,CAAC,iFAAiF,CAAC;AACnG;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,4BAA4BA,CAAA,EAAG;EACpC,OAAOJ,KAAK,CAAC,kFAAkF,CAAC;AACpG;;AAEA;AACA;AACA;AACA;AACA,MAAMK,OAAO,CAAC;EA0BV;AACJ;AACA;AACA;AACA;EACI,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,IAAI,CAACC,WAAW,KAAKD,UAAU,EAAE;MACjC,IAAI,CAACE,iBAAiB,CAACF,UAAU,CAAC;IACtC;EACJ;EAuEAhF,WAAWA,CAAA,EAAG;IAAAC,eAAA,mBA5GH3B,MAAM,CAACI,eAAe,CAAC;IAAAuB,eAAA,6BACb3B,MAAM,CAACK,iBAAiB,CAAC;IAAAsB,eAAA,sBAChC3B,MAAM,CAACM,UAAU,CAAC;IAAAqB,eAAA,eACzB3B,MAAM,CAACqB,cAAc,CAAC;IAC7B;IAAAM,eAAA,qBACa,IAAI7C,OAAO,CAAC,CAAC;IAC1B;IAAA6C,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,kBACU,IAAIkF,GAAG,CAAC,CAAC;IACnB;IAAAlF,eAAA,mBACW,IAAIkF,GAAG,CAAC,CAAC;IACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IAPIlF,eAAA,oBAQY,IAAIkF,GAAG,CAAC,CAAC;IAAAlF,eAAA;IAerB;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA;IAQA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAOA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAOA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAOA;AACJ;AACA;IAFIA,eAAA;IAIA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IACA;IACA;AACJ;AACA;AACA;IAHIA,eAAA,qBAIa,IAAI5C,eAAe,CAAC;MAC7B+H,KAAK,EAAE,CAAC;MACRC,GAAG,EAAEC,MAAM,CAACC;IAChB,CAAC,CAAC;IACF;IAAAtF,eAAA;IAEA;AACJ;AACA;AACA;AACA;IAJIA,eAAA,0BAKkB,IAAI5C,eAAe,CAAC,EAAE,CAAC;IACzC;IAAA4C,eAAA,oBACY,IAAI5C,eAAe,CAAC,IAAI,CAAC;IACrC;IAAA4C,eAAA,iBACS,IAAI5C,eAAe,CAAC,IAAI8H,GAAG,CAAC,CAAC,CAAC;IACvC;AACJ;AACA;AACA;AACA;IAJIlF,eAAA,2BAKmB,IAAI5C,eAAe,CAAC,EAAE,CAAC;IAAA4C,eAAA,6BACrB3B,MAAM,CAACmB,gBAAgB,CAAC;IAC7C;IAAAQ,eAAA;IAAAA,eAAA,oBAEY,KAAK;EACD;EAChBuF,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACC,4BAA4B,CAAC,CAAC;IACnC,IAAI,CAACC,uBAAuB,CAAC,CAAC;EAClC;EACAC,WAAWA,CAAA,EAAG;IAAA,IAAAC,iBAAA;IACV,IAAI,CAACC,WAAW,CAACC,aAAa,CAAChF,KAAK,CAAC,CAAC;IACtC,IAAI,CAACiF,UAAU,CAACC,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,CAAC;IACtB,IAAI,CAACD,UAAU,CAACD,QAAQ,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACjB,WAAW,IAAI,OAAO,IAAI,CAACA,WAAW,CAACoB,UAAU,KAAK,UAAU,EAAE;MACvE,IAAI,CAACrB,UAAU,CAACqB,UAAU,CAAC,IAAI,CAAC;IACpC;IACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACC,WAAW,CAAC,CAAC;MACpC,IAAI,CAACD,iBAAiB,GAAG,IAAI;IACjC;IACA;IACA;IACA,CAAAR,iBAAA,OAAI,CAACU,WAAW,cAAAV,iBAAA,eAAhBA,iBAAA,CAAkBW,OAAO,CAAC,CAAC;EAC/B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,SAAS,GAAG,IAAI;EACzB;EACAnB,4BAA4BA,CAAA,EAAG;IAC3B,MAAMoB,eAAe,GAAG,IAAI,CAACC,SAAS,CAACrJ,MAAM,CAACsJ,GAAG,IAAI,CAACA,GAAG,CAAC3C,IAAI,CAAC;IAC/D,IAAIyC,eAAe,CAACjF,MAAM,GAAG,CAAC,KAAK,OAAO6B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC/E,MAAMgB,mCAAmC,CAAC,CAAC;IAC/C;IACA,IAAI,CAACuC,eAAe,GAAGH,eAAe,CAAC,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,mBAAmBA,CAACC,OAAO,EAAE;IACzB,MAAMC,WAAW,GAAG,IAAI,CAACC,SAAS,CAAClG,KAAK;IACxC,IAAIiG,WAAW,KAAK,IAAI,EAAE;MACtB,IAAI,CAACC,SAAS,CAAClB,IAAI,CAACgB,OAAO,CAAC;IAChC,CAAC,MACI,IAAI,CAAC,OAAOzD,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK0D,WAAW,KAAKD,OAAO,EAAE;MACjFG,OAAO,CAACC,IAAI,CAAC,4EAA4E,GACrF,0EAA0E,GAC1E,uBAAuBH,WAAW,qBAAqBD,OAAO,IAAI,CAAC;IAC3E;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIlC,iBAAiBA,CAACF,UAAU,EAAE;IAC1B,IAAI,IAAI,CAACC,WAAW,IAAI,OAAO,IAAI,CAACA,WAAW,CAACoB,UAAU,KAAK,UAAU,EAAE;MACvE,IAAI,CAACrB,UAAU,CAACqB,UAAU,CAAC,IAAI,CAAC;IACpC;IACA,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACC,WAAW,CAAC,CAAC;MACpC,IAAI,CAACD,iBAAiB,GAAG,IAAI;IACjC;IACA;IACA,IAAI,CAACtB,UAAU,EAAE;MACb,IAAI,CAACe,WAAW,CAACC,aAAa,CAAChF,KAAK,CAAC,CAAC;IAC1C;IACA,IAAI,CAACiE,WAAW,GAAGD,UAAU;IAC7B,IAAI,IAAI,CAACgC,SAAS,EAAE;MAChB,IAAI,CAACpB,uBAAuB,CAAC,CAAC;IAClC;EACJ;EACA6B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;MAAA,IAAAC,qBAAA;MACnB,CAAAA,qBAAA,OAAI,CAACC,eAAe,cAAAD,qBAAA,cAAAA,qBAAA,GAApB,IAAI,CAACC,eAAe,GAAK,IAAI1K,cAAc,CAAC,IAAI,CAAC;MACjD,OAAO,IAAI,CAAC0K,eAAe;IAC/B;IACA,OAAO,IAAI,CAACF,WAAW,CAACtH,cAAc;EAC1C;EACA;EACAwF,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACU,iBAAiB,EAAE;MACxB;IACJ;IACA,IAAIuB,UAAU;IACd,IAAIhI,YAAY,CAAC,IAAI,CAACoF,WAAW,CAAC,EAAE;MAChC4C,UAAU,GAAG,IAAI,CAAC5C,WAAW,CAAC6C,OAAO,CAAC,IAAI,CAAC;IAC/C,CAAC,MACI,IAAI3K,YAAY,CAAC,IAAI,CAAC8H,WAAW,CAAC,EAAE;MACrC4C,UAAU,GAAG,IAAI,CAAC5C,WAAW;IACjC,CAAC,MACI,IAAIvC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACsC,WAAW,CAAC,EAAE;MACtC4C,UAAU,GAAGvK,EAAE,CAAC,IAAI,CAAC2H,WAAW,CAAC;IACrC;IACA,IAAI,CAAC4C,UAAU,EAAE;MACb,IAAI,OAAOlE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,MAAMc,6BAA6B,CAAC,CAAC;MACzC;MACA;IACJ;IACA,IAAI,CAAC6B,iBAAiB,GAAG,IAAI,CAACyB,cAAc,CAACF,UAAU,CAAC,CACnD/E,IAAI,CAAClF,SAAS,CAAC,IAAI,CAACuI,UAAU,CAAC,CAAC,CAChCnD,SAAS,CAACgF,aAAa,IAAI;MAC5B,IAAI,CAACC,kBAAkB,CAACD,aAAa,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;EACAD,cAAcA,CAACF,UAAU,EAAE;IACvB,MAAMzH,cAAc,GAAG,IAAI,CAACqH,kBAAkB,CAAC,CAAC;IAChD,OAAOlK,aAAa,CAAC,CACjBsK,UAAU,EACV,IAAI,CAACP,SAAS;IACd;IACA;IACAlH,cAAc,CAAC8H,OAAO,CAACpF,IAAI,CAACjF,SAAS,CAAC,IAAI,CAAC,EAAEC,GAAG,CAACqK,gBAAgB,IAAI;MACjE,IAAI,CAACC,qBAAqB,CAACD,gBAAgB,CAAC;IAChD,CAAC,CAAC,CAAC,CACN,CAAC,CAACrF,IAAI,CAAC/E,SAAS,CAAC,CAAC,CAACiG,IAAI,EAAEqE,QAAQ,CAAC,KAAK;MACpC,IAAIA,QAAQ,KAAK,IAAI,EAAE;QACnB,OAAO/K,EAAE,CAAC;UAAEgL,WAAW,EAAEtE,IAAI;UAAEuE,cAAc,EAAE,IAAI;UAAEF;QAAS,CAAC,CAAC;MACpE;MACA;MACA;MACA,OAAO,IAAI,CAACG,qBAAqB,CAACxE,IAAI,EAAEqE,QAAQ,CAAC,CAACvF,IAAI,CAAC9E,GAAG,CAACyK,aAAa,IAAAC,aAAA,CAAAA,aAAA,KAAUD,aAAa;QAAEJ;MAAQ,EAAG,CAAC,CAAC;IAClH,CAAC,CAAC,CAAC;EACP;EACAJ,kBAAkBA,CAACjE,IAAI,EAAE;IACrB,IAAIA,IAAI,CAACqE,QAAQ,KAAK,IAAI,EAAE;MACxB,IAAI,CAACM,iBAAiB,CAAC3E,IAAI,CAACsE,WAAW,CAAC;MACxC;IACJ;IACA;IACA;IACA,IAAI,CAACM,iBAAiB,CAAC5E,IAAI,CAACuE,cAAc,CAAC;IAC3C,IAAI,CAACI,iBAAiB,CAAC3E,IAAI,CAACsE,WAAW,CAAC;IACxC,IAAI,CAACO,sBAAsB,CAAC7E,IAAI,CAACuE,cAAc,CAAC;EACpD;EACAH,qBAAqBA,CAACD,gBAAgB,EAAE;IACpC,IAAI,CAACA,gBAAgB,EAAE;MACnB;IACJ;IACA,MAAMW,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC3H,KAAK;IAC/B,KAAK,MAAM4H,KAAK,IAAIb,gBAAgB,CAACa,KAAK,EAAE;MACxC,MAAMhH,IAAI,GAAG8G,KAAK,CAACG,GAAG,CAACD,KAAK,CAAC;MAC7BhH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkH,mBAAmB,CAAC,IAAI,CAAC;IACnC;IACA,KAAK,MAAMC,OAAO,IAAIhB,gBAAgB,CAACgB,OAAO,EAAE;MAC5C,MAAMnH,IAAI,GAAG8G,KAAK,CAACG,GAAG,CAACE,OAAO,CAAC;MAC/BnH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkH,mBAAmB,CAAC,KAAK,CAAC;IACpC;EACJ;EACAzD,qBAAqBA,CAAA,EAAG;IACpB,MAAM2D,KAAK,GAAG7L,aAAa,CAAC,CAAC,IAAI,CAAC8L,gBAAgB,EAAE,IAAI,CAACN,MAAM,CAAC,CAAC,CAACjG,IAAI,CAAC9E,GAAG,CAAC,CAAC,CAACsL,eAAe,EAAEhB,WAAW,CAAC,KAAKgB,eAAe,CAACrL,MAAM,CAAC,CAACmL,KAAK,EAAEpF,IAAI,KAAK;MACnJ,MAAMhC,IAAI,GAAGsG,WAAW,CAACW,GAAG,CAAC,IAAI,CAACM,gBAAgB,CAACvF,IAAI,CAAC,CAAC;MACzD,IAAIhC,IAAI,EAAE;QACNoH,KAAK,CAAClI,IAAI,CAACc,IAAI,CAAC;MACpB;MACA,OAAOoH,KAAK;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACR,MAAMI,iBAAiB,GAAG;MACtBnI,OAAO,EAAEW,IAAI,IAAI,IAAI,CAACuH,gBAAgB,CAACvH,IAAI,CAACgC,IAAI,CAAC;MACjDyF,aAAa,EAAEzH,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC0H,UAAU;MACxCC,yBAAyB,EAAE,IAAI;MAC/BC,qBAAqB,EAAE,IAAI,CAACC,IAAI,CAACzI;IACrC,CAAC;IACD,IAAI,CAACoF,WAAW,GAAG,IAAI,CAACsD,kBAAkB,CAACV,KAAK,EAAEI,iBAAiB,CAAC;EACxE;EACA5C,qBAAqBA,CAAA,EAAG;IAAA,IAAAmD,aAAA;IACpB;IACA,MAAM1I,OAAO,IAAA0I,aAAA,GAAG,IAAI,CAAC1I,OAAO,cAAA0I,aAAA,cAAAA,aAAA,GAAK,CAACC,MAAM,EAAEC,IAAI,KAAK,IAAI,CAACV,gBAAgB,CAACU,IAAI,CAAE;IAC/E,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,MAAM,CAAChJ,OAAO,CAAC;EAC7D;EACAsF,sBAAsBA,CAAA,EAAG;IACrB,IAAI,OAAOhD,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C;MACA;MACA,IAAI2G,eAAe,GAAG,CAAC;MACvB,IAAI,IAAI,CAAC5C,WAAW,EAAE;QAClB4C,eAAe,EAAE;MACrB;MACA,IAAI,IAAI,CAACC,aAAa,EAAE;QACpBD,eAAe,EAAE;MACrB;MACA,IAAI,IAAI,CAACE,gBAAgB,EAAE;QACvBF,eAAe,EAAE;MACrB;MACA,IAAI,CAACA,eAAe,EAAE;QAClB,MAAMzF,0BAA0B,CAAC,CAAC;MACtC,CAAC,MACI,IAAIyF,eAAe,GAAG,CAAC,EAAE;QAC1B,MAAMxF,4BAA4B,CAAC,CAAC;MACxC;IACJ;EACJ;EACA;EACA6D,iBAAiBA,CAAC3E,IAAI,EAAEyG,UAAU,GAAG,IAAI,CAACP,WAAW,EAAElE,aAAa,GAAG,IAAI,CAACD,WAAW,CAACC,aAAa,EAAE0E,UAAU,EAAE;IAC/G,MAAMC,OAAO,GAAGF,UAAU,CAACG,IAAI,CAAC5G,IAAI,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC2G,OAAO,IAAI,CAAC,IAAI,CAAC7D,SAAS,EAAE;MAC7B;IACJ;IACA6D,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,gBAAgB,CAAC,CAACZ,IAAI,EAAEa,qBAAqB,EAAEC,YAAY,KAAK;MACrE,IAAId,IAAI,CAACe,aAAa,IAAI,IAAI,EAAE;QAC5B,IAAI,CAACC,UAAU,CAACjH,IAAI,CAAC+G,YAAY,CAAC,EAAEA,YAAY,EAAE/E,aAAa,EAAE0E,UAAU,CAAC;MAChF,CAAC,MACI,IAAIK,YAAY,IAAI,IAAI,EAAE;QAC3B/E,aAAa,CAACkF,MAAM,CAACJ,qBAAqB,CAAC;MAC/C,CAAC,MACI;QACD,MAAMK,IAAI,GAAGnF,aAAa,CAACiD,GAAG,CAAC6B,qBAAqB,CAAC;QACrD9E,aAAa,CAACoF,IAAI,CAACD,IAAI,EAAEJ,YAAY,CAAC;MAC1C;IACJ,CAAC,CAAC;IACF;IACA;IACAJ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEU,qBAAqB,CAAEC,MAAM,IAAK;MACvC,MAAMC,OAAO,GAAGD,MAAM,CAACrB,IAAI;MAC3B,IAAIqB,MAAM,CAACP,YAAY,IAAIS,SAAS,EAAE;QAClC,MAAML,IAAI,GAAGnF,aAAa,CAACiD,GAAG,CAACqC,MAAM,CAACP,YAAY,CAAC;QACnDI,IAAI,CAACM,OAAO,CAACxH,SAAS,GAAGsH,OAAO;MACpC;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA,IAAIb,UAAU,EAAE;MACZ,IAAI,CAACgB,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,MACI;MACD,IAAI,CAACD,kBAAkB,CAACE,aAAa,CAAC,CAAC;IAC3C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAAC7H,IAAI,EAAEpE,CAAC,EAAE;IACjB,IAAI,IAAI,CAACoH,SAAS,CAAClF,MAAM,KAAK,CAAC,EAAE;MAC7B,OAAO,IAAI,CAACkF,SAAS,CAAC8E,KAAK;IAC/B;IACA,MAAMC,OAAO,GAAG,IAAI,CAAC/E,SAAS,CAACoD,IAAI,CAACnD,GAAG,IAAIA,GAAG,CAAC3C,IAAI,IAAI2C,GAAG,CAAC3C,IAAI,CAAC1E,CAAC,EAAEoE,IAAI,CAAC,CAAC,IAAI,IAAI,CAACkD,eAAe;IACjG,IAAI,CAAC6E,OAAO,KAAK,OAAOpI,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7D,MAAMiB,kCAAkC,CAAC,CAAC;IAC9C;IACA,OAAOmH,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACId,UAAUA,CAACe,QAAQ,EAAEC,KAAK,EAAEjG,aAAa,EAAE0E,UAAU,EAAE;IAAA,IAAAwB,kBAAA;IACnD,MAAM3B,aAAa,GAAG,IAAI,CAAC4B,iBAAiB,CAAC,CAAC;IAC9C,MAAMnK,IAAI,GAAG,IAAI,CAAC6J,WAAW,CAACG,QAAQ,EAAEC,KAAK,CAAC;IAC9C,MAAMG,GAAG,GAAG,IAAI,CAAC7C,gBAAgB,CAACyC,QAAQ,CAAC;IAC3C;IACA,MAAMP,OAAO,GAAG,IAAI1H,wBAAwB,CAACiI,QAAQ,CAAC;IACtDtB,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAVA,UAAU,IAAAwB,kBAAA,GAAK,IAAI,CAACG,QAAQ,CAACpD,GAAG,CAACmD,GAAG,CAAC,cAAAF,kBAAA,cAAAA,kBAAA,GAAIV,SAAS;IAClD;IACA;IACA,IAAIjB,aAAa,EAAE;MACfkB,OAAO,CAACa,KAAK,GAAG/B,aAAa,CAACyB,QAAQ,CAAC;IAC3C,CAAC,MACI,IAAItB,UAAU,KAAKc,SAAS,IAAI,IAAI,CAACe,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjD,gBAAgB,CAACmB,UAAU,CAAC,CAAC,EAAE;MACtFe,OAAO,CAACa,KAAK,GAAG,IAAI,CAACC,OAAO,CAACtD,GAAG,CAAC,IAAI,CAACM,gBAAgB,CAACmB,UAAU,CAAC,CAAC,GAAG,CAAC;IAC3E,CAAC,MACI;MACDe,OAAO,CAACa,KAAK,GAAG,CAAC;IACrB;IACA,IAAI,CAACC,OAAO,CAACE,GAAG,CAACL,GAAG,EAAEX,OAAO,CAACa,KAAK,CAAC;IACpC;IACA,MAAMI,SAAS,GAAG1G,aAAa,GAAGA,aAAa,GAAG,IAAI,CAACD,WAAW,CAACC,aAAa;IAChF0G,SAAS,CAACC,kBAAkB,CAAC3K,IAAI,CAAC4K,QAAQ,EAAEnB,OAAO,EAAEQ,KAAK,CAAC;IAC3D;IACA;IACA;IACA,IAAIY,WAAW,CAACC,kBAAkB,EAAE;MAChCD,WAAW,CAACC,kBAAkB,CAAC9I,IAAI,GAAGgI,QAAQ;IAClD;EACJ;EACA;EACAtL,UAAUA,CAACP,QAAQ,EAAE;IAAA,IAAA4M,iBAAA,EAAAC,sBAAA;IACjB,OAAO,CAAC,EAAE,CAAAD,iBAAA,OAAI,CAACrF,WAAW,cAAAqF,iBAAA,eAAhBA,iBAAA,CAAkBrM,UAAU,CAACP,QAAQ,CAAC,KAAA6M,sBAAA,GAC5C,IAAI,CAACpF,eAAe,cAAAoF,sBAAA,eAApBA,sBAAA,CAAsBrM,UAAU,CAAC,IAAI,CAAC4I,gBAAgB,CAACpJ,QAAQ,CAAC,CAAC,CAAC;EAC1E;EACA;EACAD,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,IAAI,CAACuH,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACxH,MAAM,CAACC,QAAQ,CAAC;IACrC,CAAC,MACI,IAAI,IAAI,CAACyH,eAAe,EAAE;MAC3B,IAAI,CAACA,eAAe,CAAC1H,MAAM,CAAC,IAAI,CAACqJ,gBAAgB,CAACpJ,QAAQ,CAAC,CAAC;IAChE;EACJ;EACA;EACAG,MAAMA,CAACH,QAAQ,EAAE;IACb,IAAI,IAAI,CAACuH,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACpH,MAAM,CAACH,QAAQ,CAAC;IACrC,CAAC,MACI,IAAI,IAAI,CAACyH,eAAe,EAAE;MAC3B,IAAI,CAACA,eAAe,CAACrH,MAAM,CAAC,IAAI,CAACgJ,gBAAgB,CAACpJ,QAAQ,CAAC,CAAC;IAChE;EACJ;EACA;EACAK,QAAQA,CAACL,QAAQ,EAAE;IACf,IAAI,IAAI,CAACuH,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAClH,QAAQ,CAACL,QAAQ,CAAC;IACvC,CAAC,MACI,IAAI,IAAI,CAACyH,eAAe,EAAE;MAC3B,IAAI,CAACA,eAAe,CAACnH,QAAQ,CAAC,IAAI,CAAC8I,gBAAgB,CAACpJ,QAAQ,CAAC,CAAC;IAClE;EACJ;EACA;AACJ;AACA;AACA;EACIS,iBAAiBA,CAACT,QAAQ,EAAE;IACxB,IAAI,IAAI,CAACuH,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC9G,iBAAiB,CAACT,QAAQ,CAAC;IAChD,CAAC,MACI,IAAI,IAAI,CAACyH,eAAe,EAAE;MAC3B,IAAI,IAAI,CAAClH,UAAU,CAACP,QAAQ,CAAC,EAAE;QAC3B,IAAI,CAACU,mBAAmB,CAACV,QAAQ,CAAC;MACtC,CAAC,MACI;QACD,IAAI,CAACW,iBAAiB,CAACX,QAAQ,CAAC;MACpC;IACJ;EACJ;EACA;AACJ;AACA;EACIW,iBAAiBA,CAACX,QAAQ,EAAE;IACxB,IAAI,IAAI,CAACuH,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC5G,iBAAiB,CAACX,QAAQ,CAAC;IAChD,CAAC,MACI,IAAI,IAAI,CAACyH,eAAe,EAAE;MAC3B,MAAMxH,cAAc,GAAG,IAAI,CAACwH,eAAe;MAC3CxH,cAAc,CAACG,MAAM,CAAC,IAAI,CAACgJ,gBAAgB,CAACpJ,QAAQ,CAAC,CAAC;MACtD,IAAI,CAACoC,eAAe,CAACpC,QAAQ,CAAC,CACzB2C,IAAI,CAACpF,IAAI,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC,IAAI,CAACuI,UAAU,CAAC,CAAC,CACzCnD,SAAS,CAACC,QAAQ,IAAI;QACvB7C,cAAc,CAACG,MAAM,CAAC,GAAG0C,QAAQ,CAACjF,GAAG,CAAC6E,KAAK,IAAI,IAAI,CAAC0G,gBAAgB,CAAC1G,KAAK,CAAC,CAAC,CAAC;MACjF,CAAC,CAAC;IACN;EACJ;EACA;EACAhC,mBAAmBA,CAACV,QAAQ,EAAE;IAC1B,IAAI,IAAI,CAACuH,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC7G,mBAAmB,CAACV,QAAQ,CAAC;IAClD,CAAC,MACI,IAAI,IAAI,CAACyH,eAAe,EAAE;MAC3B,MAAMxH,cAAc,GAAG,IAAI,CAACwH,eAAe;MAC3CxH,cAAc,CAACK,QAAQ,CAAC,IAAI,CAAC8I,gBAAgB,CAACpJ,QAAQ,CAAC,CAAC;MACxD,IAAI,CAACoC,eAAe,CAACpC,QAAQ,CAAC,CACzB2C,IAAI,CAACpF,IAAI,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC,IAAI,CAACuI,UAAU,CAAC,CAAC,CACzCnD,SAAS,CAACC,QAAQ,IAAI;QACvB7C,cAAc,CAACK,QAAQ,CAAC,GAAGwC,QAAQ,CAACjF,GAAG,CAAC6E,KAAK,IAAI,IAAI,CAAC0G,gBAAgB,CAAC1G,KAAK,CAAC,CAAC,CAAC;MACnF,CAAC,CAAC;IACN;EACJ;EACA;EACAd,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC2F,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC3F,SAAS,CAAC,CAAC;IAChC,CAAC,MACI,IAAI,IAAI,CAAC6F,eAAe,EAAE;MAC3B,IAAI,CAACqF,oBAAoB,CAACC,IAAI;QAAA,IAAAC,sBAAA;QAAA,QAAAA,sBAAA,GAAI,IAAI,CAACvF,eAAe,cAAAuF,sBAAA,uBAApBA,sBAAA,CAAsB5M,MAAM,CAAC,GAAG2M,IAAI,CAAC;MAAA,EAAC;IAC5E;EACJ;EACA;EACAnM,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC2G,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC3G,WAAW,CAAC,CAAC;IAClC,CAAC,MACI,IAAI,IAAI,CAAC6G,eAAe,EAAE;MAC3B,IAAI,CAACqF,oBAAoB,CAACC,IAAI;QAAA,IAAAE,sBAAA;QAAA,QAAAA,sBAAA,GAAI,IAAI,CAACxF,eAAe,cAAAwF,sBAAA,uBAApBA,sBAAA,CAAsB3M,QAAQ,CAAC,GAAGyM,IAAI,CAAC;MAAA,EAAC;IAC9E;EACJ;EACA;EACAf,iBAAiBA,CAAA,EAAG;IAAA,IAAAkB,qBAAA,EAAAC,kBAAA;IAChB,QAAAD,qBAAA,IAAAC,kBAAA,GAAO,IAAI,CAAC5F,WAAW,cAAA4F,kBAAA,gBAAAA,kBAAA,GAAhBA,kBAAA,CAAkB/L,QAAQ,cAAA+L,kBAAA,uBAA1BA,kBAAA,CAA4BC,IAAI,CAAC,IAAI,CAAC7F,WAAW,CAAC,cAAA2F,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAAC9C,aAAa;EACnF;EACA;EACAiD,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,sBAAA,EAAAC,kBAAA;IACnB,QAAAD,sBAAA,IAAAC,kBAAA,GAAO,IAAI,CAAChG,WAAW,cAAAgG,kBAAA,gBAAAA,kBAAA,GAAhBA,kBAAA,CAAkBxL,WAAW,cAAAwL,kBAAA,uBAA7BA,kBAAA,CAA+BH,IAAI,CAAC,IAAI,CAAC7F,WAAW,CAAC,cAAA+F,sBAAA,cAAAA,sBAAA,GAAI,IAAI,CAACjD,gBAAgB;EACzF;EACA;AACJ;AACA;AACA;EACImD,kBAAkBA,CAACxN,QAAQ,EAAE;IAAA,IAAAyN,sBAAA,EAAAC,kBAAA;IACzB,MAAMtD,aAAa,GAAG,IAAI,CAAC4B,iBAAiB,CAAC,CAAC;IAC9C,MAAM/L,cAAc,IAAAwN,sBAAA,GAAG,IAAI,CAAChG,eAAe,cAAAgG,sBAAA,cAAAA,sBAAA,IAAAC,kBAAA,GAAI,IAAI,CAACnG,WAAW,cAAAmG,kBAAA,uBAAhBA,kBAAA,CAAkBzN,cAAc;IAC/E,IAAI,CAACA,cAAc,EAAE;MACjB,OAAO9C,EAAE,CAAC,EAAE,CAAC;IACjB;IACA,MAAM8O,GAAG,GAAG,IAAI,CAAC7C,gBAAgB,CAACpJ,QAAQ,CAAC;IAC3C,MAAMO,UAAU,GAAGN,cAAc,CAAC8H,OAAO,CAACpF,IAAI,CAAC/E,SAAS,CAAC4M,OAAO,IAAI;MAChE,IAAIA,OAAO,CAAC3B,KAAK,CAAC8E,QAAQ,CAAC1B,GAAG,CAAC,EAAE;QAC7B,OAAO9O,EAAE,CAAC,IAAI,CAAC;MACnB,CAAC,MACI,IAAIqN,OAAO,CAACxB,OAAO,CAAC2E,QAAQ,CAAC1B,GAAG,CAAC,EAAE;QACpC,OAAO9O,EAAE,CAAC,KAAK,CAAC;MACpB;MACA,OAAOE,KAAK;IAChB,CAAC,CAAC,EAAEK,SAAS,CAAC,IAAI,CAAC6C,UAAU,CAACP,QAAQ,CAAC,CAAC,CAAC;IACzC,IAAIoK,aAAa,EAAE;MACf,OAAOhN,aAAa,CAAC,CAACmD,UAAU,EAAE,IAAI,CAACqN,eAAe,CAAC,CAAC,CAACjL,IAAI,CAAC9E,GAAG,CAAC,CAAC,CAACgQ,QAAQ,EAAEzF,cAAc,CAAC,KAAK;QAC9F,IAAI,CAACyF,QAAQ,EAAE;UACX,OAAO,EAAE;QACb;QACA,OAAO,IAAI,CAACC,oBAAoB,CAAC1D,aAAa,EAAEhC,cAAc,EAAEpI,QAAQ,EAAE,CAAC,CAAC;MAChF,CAAC,CAAC,CAAC;IACP;IACA,MAAMqK,gBAAgB,GAAG,IAAI,CAACgD,oBAAoB,CAAC,CAAC;IACpD,IAAIhD,gBAAgB,EAAE;MAAA,IAAA0D,iBAAA;MAClB,OAAOpO,gBAAgB,EAAAoO,iBAAA,GAAC1D,gBAAgB,CAACrK,QAAQ,CAAC,cAAA+N,iBAAA,cAAAA,iBAAA,GAAI,EAAE,CAAC;IAC7D;IACA,MAAMrJ,0BAA0B,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIoJ,oBAAoBA,CAAC1D,aAAa,EAAEhC,cAAc,EAAEpI,QAAQ,EAAEgO,UAAU,EAAE;IACtE,MAAM/B,GAAG,GAAG,IAAI,CAAC7C,gBAAgB,CAACpJ,QAAQ,CAAC;IAC3C,MAAMuB,UAAU,GAAG6G,cAAc,CAAC6F,SAAS,CAACpM,IAAI,IAAI,IAAI,CAACuH,gBAAgB,CAACvH,IAAI,CAAC,KAAKoK,GAAG,CAAC;IACxF,MAAMiC,aAAa,GAAG9D,aAAa,CAACpK,QAAQ,CAAC;IAC7C,MAAMmO,aAAa,GAAGD,aAAa,GAAGF,UAAU;IAChD,MAAMtM,OAAO,GAAG,EAAE;IAClB;IACA;IACA;IACA;IACA;IACA,KAAK,IAAIjC,CAAC,GAAG8B,UAAU,GAAG,CAAC,EAAE9B,CAAC,GAAG2I,cAAc,CAACzG,MAAM,EAAElC,CAAC,EAAE,EAAE;MACzD,MAAM2O,YAAY,GAAGhE,aAAa,CAAChC,cAAc,CAAC3I,CAAC,CAAC,CAAC;MACrD,IAAI2O,YAAY,IAAIF,aAAa,EAAE;QAC/B;MACJ;MACA,IAAIE,YAAY,IAAID,aAAa,EAAE;QAC/BzM,OAAO,CAACX,IAAI,CAACqH,cAAc,CAAC3I,CAAC,CAAC,CAAC;MACnC;IACJ;IACA,OAAOiC,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACI2M,aAAaA,CAACxM,IAAI,EAAE;IAChB,IAAI,CAAC+G,MAAM,CAAC3H,KAAK,CAACqL,GAAG,CAAC,IAAI,CAAClD,gBAAgB,CAACvH,IAAI,CAACgC,IAAI,CAAC,EAAEhC,IAAI,CAAC;IAC7D,IAAI,CAAC+G,MAAM,CAAC3C,IAAI,CAAC,IAAI,CAAC2C,MAAM,CAAC3H,KAAK,CAAC;EACvC;EACA;EACAqN,eAAeA,CAACzM,IAAI,EAAE;IAClB,IAAI,CAAC+G,MAAM,CAAC3H,KAAK,CAACsN,MAAM,CAAC,IAAI,CAACnF,gBAAgB,CAACvH,IAAI,CAACgC,IAAI,CAAC,CAAC;IAC1D,IAAI,CAAC+E,MAAM,CAAC3C,IAAI,CAAC,IAAI,CAAC2C,MAAM,CAAC3H,KAAK,CAAC;EACvC;EACA;AACJ;AACA;AACA;AACA;EACIuN,SAASA,CAAC3M,IAAI,EAAE;IACZ,OAAO,IAAI,CAACuK,OAAO,CAACtD,GAAG,CAAC,IAAI,CAACM,gBAAgB,CAACvH,IAAI,CAAC,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;EACI4M,WAAWA,CAACzO,QAAQ,EAAE;IAClB,MAAMsM,GAAG,GAAG,IAAI,CAACoC,WAAW,CAAC1O,QAAQ,CAAC;IACtC,OAAOsM,GAAG,CAAC3K,MAAM;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIgN,iBAAiBA,CAAC3O,QAAQ,EAAE;IACxB,MAAMsM,GAAG,GAAG,IAAI,CAACoC,WAAW,CAAC1O,QAAQ,CAAC;IACtC,MAAMiM,GAAG,GAAG,IAAI,CAAC7C,gBAAgB,CAACpJ,QAAQ,CAAC;IAC3C,OAAOsM,GAAG,CAAC2B,SAAS,CAACpM,IAAI,IAAI,IAAI,CAACuH,gBAAgB,CAACvH,IAAI,CAAC,KAAKoK,GAAG,CAAC,GAAG,CAAC;EACzE;EACA;EACA2C,cAAcA,CAAC/M,IAAI,EAAE;IACjB,MAAMgN,MAAM,GAAG,IAAI,CAAC3C,QAAQ,CAACpD,GAAG,CAAC,IAAI,CAACM,gBAAgB,CAACvH,IAAI,CAACgC,IAAI,CAAC,CAAC;IAClE,OAAOgL,MAAM,IAAI,IAAI,CAACjG,MAAM,CAAC3H,KAAK,CAAC6H,GAAG,CAAC,IAAI,CAACM,gBAAgB,CAACyF,MAAM,CAAC,CAAC;EACzE;EACA;EACAC,gBAAgBA,CAACjN,IAAI,EAAE;IACnB,OAAO,IAAI,CAAC2L,kBAAkB,CAAC3L,IAAI,CAACgC,IAAI,CAAC,CAAClB,IAAI,CAAC9E,GAAG,CAACiF,QAAQ,IAAIA,QAAQ,CAAChF,MAAM,CAAC,CAAC6K,KAAK,EAAEjG,KAAK,KAAK;MAC7F,MAAMzB,KAAK,GAAG,IAAI,CAAC2H,MAAM,CAAC3H,KAAK,CAAC6H,GAAG,CAAC,IAAI,CAACM,gBAAgB,CAAC1G,KAAK,CAAC,CAAC;MACjE,IAAIzB,KAAK,EAAE;QACP0H,KAAK,CAAC5H,IAAI,CAACE,KAAK,CAAC;MACrB;MACA,OAAO0H,KAAK;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACZ;EACA;EACAoG,wBAAwBA,CAACC,KAAK,EAAE;IAC5B;IACA;IACA,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,CAACC,WAAW,CAACC,aAAa,EAAE;MACjD,IAAI,CAAC9I,WAAW,CAAC+I,SAAS,CAACJ,KAAK,CAAC;IACrC,CAAC,MACI;MACD,MAAMrG,KAAK,GAAG,IAAI,CAACC,MAAM,CAACyG,QAAQ,CAAC,CAAC;MACpC,KAAK,MAAM,GAAGxN,IAAI,CAAC,IAAI8G,KAAK,EAAE;QAC1B,IAAIqG,KAAK,CAACC,MAAM,KAAKpN,IAAI,CAACqN,WAAW,CAACC,aAAa,EAAE;UACjD,IAAI,CAAC9I,WAAW,CAAC+I,SAAS,CAACJ,KAAK,CAAC;UACjC;QACJ;MACJ;IACJ;EACJ;EACA;EACA5M,eAAeA,CAACpC,QAAQ,EAAE;IACtB,IAAI,IAAI,CAACuH,WAAW,EAAE;MAClB,OAAOpK,EAAE,CAAC,IAAI,CAACoK,WAAW,CAACvG,cAAc,CAAChB,QAAQ,CAAC,CAAC;IACxD;IACA,IAAI,IAAI,CAACoK,aAAa,EAAE;MACpB,MAAM1I,OAAO,GAAG,IAAI,CAACoM,oBAAoB,CAAC,IAAI,CAAC1D,aAAa,EAAE,IAAI,CAACwD,eAAe,CAAC3M,KAAK,EAAEjB,QAAQ,EAAEsP,QAAQ,CAAC;MAC7G,OAAOnS,EAAE,CAACuE,OAAO,CAAC;IACtB;IACA,IAAI,IAAI,CAAC2I,gBAAgB,EAAE;MACvB,OAAO,IAAI,CAACkF,0BAA0B,CAACvP,QAAQ,CAAC,CAAC2C,IAAI,CAAC7E,MAAM,CAAC,CAAC0R,WAAW,EAAEC,YAAY,KAAK;QACxFD,WAAW,CAACzO,IAAI,CAAC,GAAG0O,YAAY,CAAC;QACjC,OAAOD,WAAW;MACtB,CAAC,EAAE,EAAE,CAAC,CAAC;IACX;IACA,MAAM9K,0BAA0B,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6K,0BAA0BA,CAACvP,QAAQ,EAAE;IACjC,IAAI,CAAC,IAAI,CAACqK,gBAAgB,EAAE;MACxB,OAAOlN,EAAE,CAAC,EAAE,CAAC;IACjB;IACA,OAAOwC,gBAAgB,CAAC,IAAI,CAAC0K,gBAAgB,CAACrK,QAAQ,CAAC,CAAC,CAAC2C,IAAI,CAACpF,IAAI,CAAC,CAAC,CAAC,EAAEK,SAAS,CAACkF,QAAQ,IAAI;MACzF;MACA,KAAK,MAAMJ,KAAK,IAAII,QAAQ,EAAE;QAC1B,IAAI,CAACoJ,QAAQ,CAACI,GAAG,CAAC,IAAI,CAAClD,gBAAgB,CAAC1G,KAAK,CAAC,EAAE1C,QAAQ,CAAC;MAC7D;MACA,OAAO7C,EAAE,CAAC,GAAG2F,QAAQ,CAAC,CAACH,IAAI,CAAC5E,SAAS,CAAC2E,KAAK,IAAIpF,MAAM,CAACH,EAAE,CAAC,CAACuF,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC6M,0BAA0B,CAAC7M,KAAK,CAAC,CAAC,CAAC,CAAC;IAChH,CAAC,CAAC,CAAC;EACP;EACA0G,gBAAgBA,CAACpJ,QAAQ,EAAE;IAAA,IAAA0P,kBAAA,EAAAC,mBAAA;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAAD,kBAAA,IAAAC,mBAAA,GAAO,IAAI,CAACC,YAAY,cAAAD,mBAAA,uBAAjBA,mBAAA,CAAAE,IAAA,KAAI,EAAgB7P,QAAQ,CAAC,cAAA0P,kBAAA,cAAAA,kBAAA,GAAI1P,QAAQ;EACpD;EACA0O,WAAWA,CAAC7M,IAAI,EAAE;IACd,MAAMoK,GAAG,GAAG,IAAI,CAAC7C,gBAAgB,CAACvH,IAAI,CAAC;IACvC,MAAMgN,MAAM,GAAG,IAAI,CAAC3C,QAAQ,CAACpD,GAAG,CAACmD,GAAG,CAAC;IACrC,MAAM6D,SAAS,GAAGjB,MAAM,GAAG,IAAI,CAACzF,gBAAgB,CAACyF,MAAM,CAAC,GAAG,IAAI;IAC/D,MAAMvC,GAAG,GAAG,IAAI,CAACyD,SAAS,CAACjH,GAAG,CAACgH,SAAS,CAAC;IACzC,OAAOxD,GAAG,aAAHA,GAAG,cAAHA,GAAG,GAAI,CAACzK,IAAI,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACImO,kBAAkBA,CAACnO,IAAI,EAAEiK,KAAK,EAAEmE,WAAW,EAAE;IAAA,IAAAC,iBAAA;IACzC;IACA;IACA;IACA,IAAI,CAACD,WAAW,CAACtO,MAAM,EAAE;MACrB,OAAO,IAAI;IACf;IACA,MAAMyM,YAAY,IAAA8B,iBAAA,GAAG,IAAI,CAAC9D,OAAO,CAACtD,GAAG,CAAC,IAAI,CAACM,gBAAgB,CAACvH,IAAI,CAAC,CAAC,cAAAqO,iBAAA,cAAAA,iBAAA,GAAI,CAAC;IACvE,KAAK,IAAIC,WAAW,GAAGrE,KAAK,GAAG,CAAC,EAAEqE,WAAW,IAAI,CAAC,EAAEA,WAAW,EAAE,EAAE;MAAA,IAAAC,kBAAA;MAC/D,MAAMC,UAAU,GAAGJ,WAAW,CAACE,WAAW,CAAC;MAC3C,MAAMG,WAAW,IAAAF,kBAAA,GAAG,IAAI,CAAChE,OAAO,CAACtD,GAAG,CAAC,IAAI,CAACM,gBAAgB,CAACiH,UAAU,CAAC,CAAC,cAAAD,kBAAA,cAAAA,kBAAA,GAAI,CAAC;MAC5E,IAAIE,WAAW,GAAGlC,YAAY,EAAE;QAC5B,OAAOiC,UAAU;MACrB;IACJ;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,gCAAgCA,CAAC5H,KAAK,EAAEwD,KAAK,GAAG,CAAC,EAAE;IAC/C,MAAM9B,gBAAgB,GAAG,IAAI,CAACgD,oBAAoB,CAAC,CAAC;IACpD;IACA,IAAI,CAAChD,gBAAgB,EAAE;MACnB,OAAOlN,EAAE,CAAC,CAAC,GAAGwL,KAAK,CAAC,CAAC;IACzB;IACA,OAAOxL,EAAE,CAAC,GAAGwL,KAAK,CAAC,CAAChG,IAAI,CAAC5E,SAAS,CAAC8D,IAAI,IAAI;MACvC,MAAMiO,SAAS,GAAG,IAAI,CAAC1G,gBAAgB,CAACvH,IAAI,CAAC;MAC7C,IAAI,CAAC,IAAI,CAACqK,QAAQ,CAACG,GAAG,CAACyD,SAAS,CAAC,EAAE;QAC/B,IAAI,CAAC5D,QAAQ,CAACI,GAAG,CAACwD,SAAS,EAAE,IAAI,CAAC;MACtC;MACA,IAAI,CAAC1D,OAAO,CAACE,GAAG,CAACwD,SAAS,EAAE3D,KAAK,CAAC;MAClC,MAAMrJ,QAAQ,GAAGnD,gBAAgB,CAAC0K,gBAAgB,CAACxI,IAAI,CAAC,CAAC;MACzD,OAAOvE,MAAM,CAACH,EAAE,CAAC,CAAC0E,IAAI,CAAC,CAAC,EAAEiB,QAAQ,CAACH,IAAI,CAACpF,IAAI,CAAC,CAAC,CAAC,EAAEI,GAAG,CAAC6S,UAAU,IAAI;QAC/D,IAAI,CAACT,SAAS,CAACzD,GAAG,CAACwD,SAAS,EAAE,CAAC,IAAIU,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAI,EAAE,CAAC,CAAC,CAAC;QACtD,KAAK,MAAM9N,KAAK,IAAI8N,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAI,EAAE,EAAE;UAClC,MAAMC,QAAQ,GAAG,IAAI,CAACrH,gBAAgB,CAAC1G,KAAK,CAAC;UAC7C,IAAI,CAACwJ,QAAQ,CAACI,GAAG,CAACmE,QAAQ,EAAE5O,IAAI,CAAC;UACjC,IAAI,CAACuK,OAAO,CAACE,GAAG,CAACmE,QAAQ,EAAEtE,KAAK,GAAG,CAAC,CAAC;QACzC;MACJ,CAAC,CAAC,EAAEvO,SAAS,CAAC4S,UAAU,IAAI;QACxB,IAAI,CAACA,UAAU,EAAE;UACb,OAAOrT,EAAE,CAAC,EAAE,CAAC;QACjB;QACA,OAAO,IAAI,CAACoT,gCAAgC,CAACC,UAAU,EAAErE,KAAK,GAAG,CAAC,CAAC,CAACxJ,IAAI,CAAC9E,GAAG,CAAC6S,WAAW,IAAK,IAAI,CAACnQ,UAAU,CAACsB,IAAI,CAAC,GAAG6O,WAAW,GAAG,EAAG,CAAC,CAAC;MAC5I,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,EAAE5S,MAAM,CAAC,CAAC4D,OAAO,EAAEoB,QAAQ,KAAK;MAC9BpB,OAAO,CAACX,IAAI,CAAC,GAAG+B,QAAQ,CAAC;MACzB,OAAOpB,OAAO;IAClB,CAAC,EAAE,EAAE,CAAC,CAAC;EACX;EACA;AACJ;AACA;AACA;AACA;EACI2G,qBAAqBA,CAACM,KAAK,EAAET,QAAQ,EAAE;IACnC;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACmC,gBAAgB,IAAInC,QAAQ,KAAK,MAAM,EAAE;MAC9C;MACA;MACA,IAAI,CAACyI,mBAAmB,CAAC,CAAC;MAC1B;MACA,IAAI,CAACZ,SAAS,CAACzD,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG3D,KAAK,CAAC,CAAC;MACpC,OAAO,IAAI,CAAC4H,gCAAgC,CAAC5H,KAAK,CAAC,CAAChG,IAAI,CAAC9E,GAAG,CAACuK,cAAc,KAAK;QAC5ED,WAAW,EAAEC,cAAc;QAC3BA;MACJ,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,MACI,IAAI,IAAI,CAACgC,aAAa,IAAIlC,QAAQ,KAAK,QAAQ,EAAE;MAClD;MACA;MACA,MAAMkC,aAAa,GAAG,IAAI,CAACA,aAAa;MACxC,OAAOjN,EAAE,CAACwL,KAAK,CAACnL,MAAM,CAACqE,IAAI,IAAIuI,aAAa,CAACvI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAACc,IAAI,CAAC9E,GAAG,CAAC+S,SAAS,KAAK;QAC9EzI,WAAW,EAAEyI,SAAS;QACtBxI,cAAc,EAAEO;MACpB,CAAC,CAAC,CAAC,EAAEhL,GAAG,CAAC,CAAC;QAAEyK;MAAe,CAAC,KAAK;QAC7B,IAAI,CAACyI,iBAAiB,CAACzI,cAAc,CAAC;MAC1C,CAAC,CAAC,CAAC;IACP,CAAC,MACI,IAAIF,QAAQ,KAAK,MAAM,EAAE;MAC1B;MACA;MACA;MACA;MACA,OAAO/K,EAAE,CAAC;QAAEgL,WAAW,EAAEQ,KAAK;QAAEP,cAAc,EAAEO;MAAM,CAAC,CAAC,CAAChG,IAAI,CAAChF,GAAG,CAAC,CAAC;QAAEyK;MAAe,CAAC,KAAK;QACtF,IAAI,CAACyI,iBAAiB,CAACzI,cAAc,CAAC;MAC1C,CAAC,CAAC,CAAC;IACP,CAAC,MACI;MACD;MACA;MACA,IAAI,CAACuI,mBAAmB,CAAC,CAAC;MAC1B;MACA;MACA,IAAI,CAACZ,SAAS,CAACzD,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG3D,KAAK,CAAC,CAAC;MACpC,OAAO,IAAI,CAAC4H,gCAAgC,CAAC5H,KAAK,CAAC,CAAChG,IAAI,CAAC9E,GAAG,CAACuK,cAAc,KAAK;QAC5ED,WAAW,EAAEQ,KAAK;QAClBP;MACJ,CAAC,CAAC,CAAC,CAAC;IACR;EACJ;EACAK,iBAAiBA,CAACL,cAAc,EAAE;IAC9B,IAAI,CAACwF,eAAe,CAAC3H,IAAI,CAACmC,cAAc,CAAC;EAC7C;EACAM,sBAAsBA,CAACN,cAAc,EAAE;IACnC,IAAI,CAACc,gBAAgB,CAACjD,IAAI,CAACmC,cAAc,CAAC;EAC9C;EACA;EACAyI,iBAAiBA,CAACzI,cAAc,EAAE;IAC9B,MAAMgC,aAAa,GAAG,IAAI,CAAC4B,iBAAiB,CAAC,CAAC;IAC9C,IAAI,CAAC5B,aAAa,EAAE;MAChB;IACJ;IACA;IACA;IACA,IAAI,CAACuG,mBAAmB,CAAC,CAAC;IAC1B,KAAK,IAAI7E,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG1D,cAAc,CAACzG,MAAM,EAAEmK,KAAK,EAAE,EAAE;MAAA,IAAAgF,mBAAA;MACxD,MAAM9Q,QAAQ,GAAGoI,cAAc,CAAC0D,KAAK,CAAC;MACtC,MAAMG,GAAG,GAAG,IAAI,CAAC7C,gBAAgB,CAACpJ,QAAQ,CAAC;MAC3C,IAAI,CAACoM,OAAO,CAACE,GAAG,CAACL,GAAG,EAAE7B,aAAa,CAACpK,QAAQ,CAAC,CAAC;MAC9C,MAAM6O,MAAM,GAAG,IAAI,CAACmB,kBAAkB,CAAChQ,QAAQ,EAAE8L,KAAK,EAAE1D,cAAc,CAAC;MACvE,IAAI,CAAC8D,QAAQ,CAACI,GAAG,CAACL,GAAG,EAAE4C,MAAM,CAAC;MAC9B,MAAMiB,SAAS,GAAGjB,MAAM,GAAG,IAAI,CAACzF,gBAAgB,CAACyF,MAAM,CAAC,GAAG,IAAI;MAC/D,MAAMkC,KAAK,IAAAD,mBAAA,GAAG,IAAI,CAACf,SAAS,CAACjH,GAAG,CAACgH,SAAS,CAAC,cAAAgB,mBAAA,cAAAA,mBAAA,GAAI,EAAE;MACjDC,KAAK,CAAC1O,MAAM,CAACyJ,KAAK,EAAE,CAAC,EAAE9L,QAAQ,CAAC;MAChC,IAAI,CAAC+P,SAAS,CAACzD,GAAG,CAACwD,SAAS,EAAEiB,KAAK,CAAC;IACxC;EACJ;EACA;EACAjE,oBAAoBA,CAACkE,QAAQ,EAAE;IAC3B,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMC,WAAW,GAAG,EAAE;IACtB,IAAI,CAACtI,MAAM,CAAC3H,KAAK,CAACwB,OAAO,CAACZ,IAAI,IAAI;MAC9BoP,QAAQ,CAAClQ,IAAI,CAAC,IAAI,CAACqI,gBAAgB,CAACvH,IAAI,CAACgC,IAAI,CAAC,CAAC;MAC/CqN,WAAW,CAACnQ,IAAI,CAAC,IAAI,CAACqB,eAAe,CAACP,IAAI,CAACgC,IAAI,CAAC,CAAC;IACrD,CAAC,CAAC;IACF,IAAIqN,WAAW,CAACvP,MAAM,GAAG,CAAC,EAAE;MACxBvE,aAAa,CAAC8T,WAAW,CAAC,CACrBvO,IAAI,CAACpF,IAAI,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC,IAAI,CAACuI,UAAU,CAAC,CAAC,CACzCnD,SAAS,CAACnB,OAAO,IAAI;QACtBA,OAAO,CAACe,OAAO,CAAC0O,KAAK,IAAIA,KAAK,CAAC1O,OAAO,CAAC2O,CAAC,IAAIH,QAAQ,CAAClQ,IAAI,CAAC,IAAI,CAACqI,gBAAgB,CAACgI,CAAC,CAAC,CAAC,CAAC,CAAC;QACrFJ,QAAQ,CAACC,QAAQ,CAAC;MACtB,CAAC,CAAC;IACN,CAAC,MACI;MACDD,QAAQ,CAACC,QAAQ,CAAC;IACtB;EACJ;EACA;EACAN,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACzE,QAAQ,CAACrL,KAAK,CAAC,CAAC;IACrB,IAAI,CAACuL,OAAO,CAACvL,KAAK,CAAC,CAAC;IACpB,IAAI,CAACkP,SAAS,CAAClP,KAAK,CAAC,CAAC;EAC1B;AAGJ;AAACwQ,QAAA,GAn2BKzM,OAAO;AAAA9E,eAAA,CAAP8E,OAAO,wBAAA0M,iBAAAlO,iBAAA;EAAA,YAAAA,iBAAA,IAi2B0FwB,QAAO;AAAA;AAAA9E,eAAA,CAj2BxG8E,OAAO,8BAzFoE3G,EAAE,CAAAsT,iBAAA;EAAAjO,IAAA,EA27BQsB,QAAO;EAAArB,SAAA;EAAAiO,cAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;IAAA,IAAAF,EAAA;MA37BjBzT,EAAE,CAAA4T,cAAA,CAAAD,QAAA,EA27B2a7N,cAAc;IAAA;IAAA,IAAA2N,EAAA;MAAA,IAAAI,EAAA;MA37B3b7T,EAAE,CAAA8T,cAAA,CAAAD,EAAA,GAAF7T,EAAE,CAAA+T,WAAA,QAAAL,GAAA,CAAA9K,SAAA,GAAAiL,EAAA;IAAA;EAAA;EAAAG,SAAA,WAAAC,eAAAR,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFzT,EAAE,CAAAkU,WAAA,CA27BshBnP,iBAAiB;IAAA;IAAA,IAAA0O,EAAA;MAAA,IAAAI,EAAA;MA37BziB7T,EAAE,CAAA8T,cAAA,CAAAD,EAAA,GAAF7T,EAAE,CAAA+T,WAAA,QAAAL,GAAA,CAAA/L,WAAA,GAAAkM,EAAA,CAAAnG,KAAA;IAAA;EAAA;EAAAyG,SAAA,WA27BmR,MAAM;EAAAC,YAAA,WAAAC,sBAAAZ,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA37B3RzT,EAAE,CAAAsU,UAAA,qBAAAC,oCAAAC,MAAA;QAAA,OA27BQd,GAAA,CAAA5C,wBAAA,CAAA0D,MAA+B,CAAC;MAAA,CAA1B,CAAC;IAAA;EAAA;EAAAvO,MAAA;IAAAW,UAAA;IAAA0C,WAAA;IAAA6C,aAAA;IAAAC,gBAAA;IAAAnJ,OAAA;IAAA0O,YAAA;EAAA;EAAA8C,QAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAApG,QAAA,WAAAqG,kBAAApB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA37BjBzT,EAAE,CAAA8U,kBAAA,KA27B2qB,CAAC;IAAA;EAAA;EAAAC,YAAA,GAA6DhQ,iBAAiB;EAAAiQ,aAAA;AAAA;AAE70B;EAAA,QAAAzP,SAAA,oBAAAA,SAAA,KA77BiFvF,EAAE,CAAAwF,iBAAA,CA67BQmB,OAAO,EAAc,CAAC;IACrGtB,IAAI,EAAE5E,SAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpB+O,QAAQ,EAAE,SAAS;MACnBjG,QAAQ,EAAE,iDAAiD;MAC3DyG,IAAI,EAAE;QACF,OAAO,EAAE,UAAU;QACnB,MAAM,EAAE,MAAM;QACd,WAAW,EAAE;MACjB,CAAC;MACDD,aAAa,EAAEtU,iBAAiB,CAACwU,IAAI;MACrC;MACA;MACA;MACA;MACAC,eAAe,EAAExU,uBAAuB,CAACyU,OAAO;MAChDC,OAAO,EAAE,CAACtQ,iBAAiB;IAC/B,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE6B,UAAU,EAAE,CAAC;MACrDvB,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE0I,WAAW,EAAE,CAAC;MACdjE,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEuL,aAAa,EAAE,CAAC;MAChB9G,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEwL,gBAAgB,EAAE,CAAC;MACnB/G,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEqC,OAAO,EAAE,CAAC;MACVoC,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE+Q,YAAY,EAAE,CAAC;MACftM,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE+G,WAAW,EAAE,CAAC;MACdtC,IAAI,EAAExE,SAAS;MACf4E,IAAI,EAAE,CAACV,iBAAiB,EAAE;QAAEuQ,MAAM,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAE1M,SAAS,EAAE,CAAC;MACZvD,IAAI,EAAEvE,eAAe;MACrB2E,IAAI,EAAE,CAACK,cAAc,EAAE;QACf;QACA;QACA5B,WAAW,EAAE;MACjB,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMuK,WAAW,CAAC;EAKd;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI8G,IAAIA,CAAA,EAAG;IACP,OAAO,UAAU;EACrB;EACA,IAAIA,IAAIA,CAACC,KAAK,EAAE;IACZ;EAAA;EAEJ;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIpS,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACqS,aAAa,CAAC,CAAC;EAC/B;EACA,IAAIrS,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,CAACsS,kBAAkB,GAAGtS,YAAY;IACtC,IAAK,IAAI,CAACwC,IAAI,IAAI,CAAC,IAAI,CAAC6P,aAAa,IAAK,CAAC,IAAI,CAACC,kBAAkB,EAAE;MAChE;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACzT,MAAM,CAAC,CAAC;IACjB,CAAC,MACI,IAAI,IAAI,CAACyT,gBAAgB,KAAK,KAAK,EAAE;MACtC,IAAI,CAACvT,QAAQ,CAAC,CAAC;IACnB;EACJ;EACA,IAAIE,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACsT,KAAK,CAACtT,UAAU,CAAC,IAAI,CAACuT,KAAK,CAAC;EAC5C;EACA,IAAIvT,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,CAACqT,gBAAgB,GAAGrT,UAAU;IAClC,IAAIA,UAAU,EAAE;MACZ,IAAI,CAACJ,MAAM,CAAC,CAAC;IACjB,CAAC,MACI;MACD,IAAI,CAACE,QAAQ,CAAC,CAAC;IACnB;EACJ;EACA;AACJ;AACA;AACA;;EAOI0T,QAAQA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACP,OAAO,IAAI,CAACC,cAAc,MAAAD,qBAAA,GAAI,IAAI,CAAC9E,WAAW,CAACC,aAAa,CAAC+E,WAAW,cAAAF,qBAAA,uBAA1CA,qBAAA,CAA4CG,IAAI,CAAC,CAAC,KAAI,EAAE;EAC1F;EACA;;EAuBA;EACA,IAAItQ,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACiQ,KAAK;EACrB;EACA,IAAIjQ,IAAIA,CAAC5C,KAAK,EAAE;IACZ,IAAIA,KAAK,KAAK,IAAI,CAAC6S,KAAK,EAAE;MACtB,IAAI,CAACA,KAAK,GAAG7S,KAAK;MAClB,IAAI,CAACmT,YAAY,CAACnO,IAAI,CAAC,CAAC;IAC5B;EACJ;EAEA;EACA,IAAIoO,UAAUA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACb;IACA,IAAI,EAAAF,qBAAA,OAAI,CAACT,KAAK,CAACtM,WAAW,cAAA+M,qBAAA,uBAAtBA,qBAAA,CAAwBjT,YAAY,MAAKgK,SAAS,IAClD,CAAC,IAAI,CAACwI,KAAK,CAACtM,WAAW,CAAClG,YAAY,CAAC,IAAI,CAACyS,KAAK,CAAC,EAAE;MAClD,OAAO,IAAI;MACX;IACJ,CAAC,MACI,IAAI,EAAAS,sBAAA,OAAI,CAACV,KAAK,CAACtM,WAAW,cAAAgN,sBAAA,uBAAtBA,sBAAA,CAAwBlT,YAAY,MAAKgK,SAAS,IACvD,EAAAmJ,sBAAA,OAAI,CAACX,KAAK,CAACtM,WAAW,cAAAiN,sBAAA,uBAAtBA,sBAAA,CAAwBxT,cAAc,CAAC,IAAI,CAAC8S,KAAK,CAAC,CAACnS,MAAM,MAAK,CAAC,EAAE;MACjE,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA,IAAIwK,KAAKA,CAAA,EAAG;IAAA,IAAAsI,qBAAA;IACR;IACA;IACA;IACA,QAAAA,qBAAA,GAAO,IAAI,CAACZ,KAAK,CAACrF,SAAS,CAAC,IAAI,CAACsF,KAAK,CAAC,cAAAW,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAACC,oBAAoB;EACxE;EACA;EACAhB,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACG,KAAK,CAACtM,WAAW,EAAE;MACxB,IAAI,IAAI,CAAC8M,UAAU,EAAE;QACjB,OAAO,KAAK;MAChB;MACA;MACA;MACA,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACV,kBAAkB;EAClC;EACA;AACJ;AACA;AACA;AACA;EACIgB,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACjB,aAAa,CAAC,CAAC,EAAE;MACvB,OAAO,IAAI;IACf;IACA,OAAOkB,MAAM,CAAC,IAAI,CAACrU,UAAU,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACIkO,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoF,KAAK,CAACpF,WAAW,CAAC,IAAI,CAACqF,KAAK,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;EACInF,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACkF,KAAK,CAAClF,iBAAiB,CAAC,IAAI,CAACmF,KAAK,CAAC;EACnD;EAEAjU,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBA/JA3B,MAAM,CAACM,UAAU,CAAC;IAAAqB,eAAA,gBACxB3B,MAAM,CAACyG,OAAO,CAAC;IAAA9E,eAAA,oBACX,CAAC,CAAC;IAAAA,eAAA,gBACN,MAAM;IAAAA,eAAA;IAsDd;AACJ;AACA;AACA;IAHIA,eAAA;IAAAA,eAAA,qBASa,IAAId,YAAY,CAAC,CAAC;IAC/B;IAAAc,eAAA,yBACiB,IAAId,YAAY,CAAC,CAAC;IAMnC;IAAAc,eAAA,qBACa,IAAI7C,OAAO,CAAC,CAAC;IAC1B;IAAA6C,eAAA,uBACe,IAAI7C,OAAO,CAAC,CAAC;IAAA6C,eAAA,6BACP,KAAK;IAAAA,eAAA,2BACPuL,SAAS;IAC5B;AACJ;AACA;AACA;AACA;AACA;IALIvL,eAAA,uBAMe,IAAI;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,6BAwEE3B,MAAM,CAACK,iBAAiB,CAAC;IAE1CkO,WAAW,CAACC,kBAAkB,GAAG,IAAI;EACzC;EACApG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACmO,oBAAoB,GAAGG,sBAAsB,CAAC,IAAI,CAAC3F,WAAW,CAACC,aAAa,CAAC;IAClF,IAAI,CAAC0E,KAAK,CACLvM,kBAAkB,CAAC,CAAC,CACpBS,OAAO,CAACpF,IAAI,CAAC9E,GAAG,CAAC,MAAM,IAAI,CAAC0C,UAAU,CAAC,EAAEvC,oBAAoB,CAAC,CAAC,CAAC,CAChE6E,SAAS,CAAC,MAAM,IAAI,CAAC0I,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACqI,KAAK,CAAC7M,mBAAmB,CAAC,IAAI,CAAC8N,KAAK,CAAC;IAC1C,IAAI,CAACjB,KAAK,CAACxF,aAAa,CAAC,IAAI,CAAC;EAClC;EACA3I,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAIgH,WAAW,CAACC,kBAAkB,KAAK,IAAI,EAAE;MACzCD,WAAW,CAACC,kBAAkB,GAAG,IAAI;IACzC;IACA,IAAI,CAACyH,YAAY,CAACrO,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACgP,UAAU,CAAC9O,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC8O,UAAU,CAAChP,QAAQ,CAAC,CAAC;EAC9B;EACAiP,SAASA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACR,QAAAA,qBAAA,GAAO,IAAI,CAACpB,KAAK,CAACjF,cAAc,CAAC,IAAI,CAAC,cAAAqG,qBAAA,cAAAA,qBAAA,GAAI,IAAI;EAClD;EACAlT,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC8R,KAAK,CAAC/E,gBAAgB,CAAC,IAAI,CAAC;EAC5C;EACA;EACAoG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAAClG,WAAW,CAACC,aAAa,CAAC+F,KAAK,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC3J,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACA6J,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAAC5J,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACA8J,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC/L,UAAU,EAAE;MACjB;IACJ;IACA,IAAI,CAACgM,UAAU,CAACtP,IAAI,CAAC,IAAI,CAAC6N,KAAK,CAAC;EACpC;EACA;EACAzT,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACgB,YAAY,EAAE;MACnB,IAAI,CAACwS,KAAK,CAACxT,QAAQ,CAAC,IAAI,CAACyT,KAAK,CAAC;IACnC;EACJ;EACA;EACA3T,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACkB,YAAY,EAAE;MACnB,IAAI,CAACwS,KAAK,CAAC1T,MAAM,CAAC,IAAI,CAAC2T,KAAK,CAAC;IACjC;EACJ;EACA;EACA0B,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACL,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC5J,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACAiK,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAAClM,UAAU,EAAE;MACjB;IACJ;IACA,IAAI,CAACsK,KAAK,CAACxN,WAAW,CAACqP,SAAS,CAAC,IAAI,CAAC;EAC1C;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACpM,UAAU,EAAE;MACjB;IACJ;IACA,IAAI,CAAC6L,YAAY,GAAG,KAAK;IACzB,IAAI,CAACvB,KAAK,CAACxN,WAAW,CAACqP,SAAS,CAAC,IAAI,CAAC;IACtC,IAAI,CAACN,YAAY,GAAG,IAAI;EAC5B;EACArM,mBAAmBA,CAAC8E,QAAQ,EAAE;IAC1B,IAAI,CAAC+H,cAAc,CAACC,IAAI,CAAChI,QAAQ,CAAC;EACtC;AAGJ;AAACiI,YAAA,GApPKpJ,WAAW;AAsEb;AACJ;AACA;AACA;AAHI5M,eAAA,CAtEE4M,WAAW,wBA0Ee,IAAI;AAAA5M,eAAA,CA1E9B4M,WAAW,wBAAAqJ,qBAAA3S,iBAAA;EAAA,YAAAA,iBAAA,IAkPsFsJ,YAAW;AAAA;AAAA5M,eAAA,CAlP5G4M,WAAW,8BA1+BgEzO,EAAE,CAAAoF,iBAAA;EAAAC,IAAA,EA6tCQoJ,YAAW;EAAAnJ,SAAA;EAAA6O,SAAA,WAA2Y,UAAU;EAAA4D,QAAA;EAAA3D,YAAA,WAAA4D,0BAAAvE,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA7tC1azT,EAAE,CAAAsU,UAAA,mBAAA2D,sCAAA;QAAA,OA6tCQvE,GAAA,CAAAgE,cAAA,CAAe,CAAC;MAAA,CAAN,CAAC,mBAAAQ,sCAAA;QAAA,OAAXxE,GAAA,CAAA8D,UAAA,CAAW,CAAC;MAAA,CAAF,CAAC;IAAA;IAAA,IAAA/D,EAAA;MA7tCrBzT,EAAE,CAAAmY,cAAA,aAAAzE,GAAA,CAAAwD,SA6tCkB,CAAC;MA7tCrBlX,EAAE,CAAAoY,WAAA,kBA6tCQ1E,GAAA,CAAAgD,gBAAA,CAAiB,CAAC,gBAAAhD,GAAA,CAAAxF,KAAA,GAAV,CAAC,mBAATwF,GAAA,CAAAhD,iBAAA,CAAkB,CAAC,kBAAnBgD,GAAA,CAAAlD,WAAA,CAAY,CAAC;IAAA;EAAA;EAAAvK,MAAA;IAAAsP,IAAA;IAAAnS,YAAA,sCAAsHpC,gBAAgB;IAAAsB,UAAA;IAAAgJ,UAAA,kCAAsEtK,gBAAgB;IAAAgV,cAAA;EAAA;EAAAqC,OAAA;IAAAf,UAAA;IAAAK,cAAA;EAAA;EAAAlD,QAAA;AAAA;AAEpU;EAAA,QAAAlP,SAAA,oBAAAA,SAAA,KA/tCiFvF,EAAE,CAAAwF,iBAAA,CA+tCQiJ,WAAW,EAAc,CAAC;IACzGpJ,IAAI,EAAEjF,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzB+O,QAAQ,EAAE,aAAa;MACvBQ,IAAI,EAAE;QACF,OAAO,EAAE,eAAe;QACxB,sBAAsB,EAAE,oBAAoB;QAC5C,mBAAmB,EAAE,WAAW;QAChC,sBAAsB,EAAE,qBAAqB;QAC7C,qBAAqB,EAAE,eAAe;QACtC,YAAY,EAAE,WAAW;QACzB,MAAM,EAAE,UAAU;QAClB,SAAS,EAAE,kBAAkB;QAC7B,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEM,IAAI,EAAE,CAAC;MAC/ClQ,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAEwC,YAAY,EAAE,CAAC;MACfiC,IAAI,EAAEzE,KAAK;MACX6E,IAAI,EAAE,CAAC;QAAE6S,SAAS,EAAEtX;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsB,UAAU,EAAE,CAAC;MACb+C,IAAI,EAAEzE;IACV,CAAC,CAAC;IAAE0K,UAAU,EAAE,CAAC;MACbjG,IAAI,EAAEzE,KAAK;MACX6E,IAAI,EAAE,CAAC;QAAE6S,SAAS,EAAEtX;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgV,cAAc,EAAE,CAAC;MACjB3Q,IAAI,EAAEzE,KAAK;MACX6E,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAE6R,UAAU,EAAE,CAAC;MACbjS,IAAI,EAAEpE;IACV,CAAC,CAAC;IAAE0W,cAAc,EAAE,CAAC;MACjBtS,IAAI,EAAEpE;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,SAAS2V,sBAAsBA,CAAC2B,WAAW,EAAE;EACzC,IAAI3H,MAAM,GAAG2H,WAAW,CAACC,aAAa;EACtC,OAAO5H,MAAM,IAAI,CAAC6H,aAAa,CAAC7H,MAAM,CAAC,EAAE;IACrCA,MAAM,GAAGA,MAAM,CAAC4H,aAAa;EACjC;EACA,IAAI,CAAC5H,MAAM,EAAE;IACT,IAAI,OAAOrL,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMe,KAAK,CAAC,oDAAoD,CAAC;IACrE,CAAC,MACI;MACD,OAAO,CAAC,CAAC;IACb;EACJ,CAAC,MACI,IAAIsK,MAAM,CAAC8H,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAAE;IACxD,OAAOzX,eAAe,CAAC0P,MAAM,CAACgI,YAAY,CAAC,YAAY,CAAC,CAAC;EAC7D,CAAC,MACI;IACD;IACA,OAAO,CAAC;EACZ;AACJ;AACA,SAASH,aAAaA,CAACI,OAAO,EAAE;EAC5B,MAAMH,SAAS,GAAGG,OAAO,CAACH,SAAS;EACnC,OAAO,CAAC,EAAEA,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,QAAQ,CAAC,sBAAsB,CAAC,IAAID,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC7F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,iBAAiB,SAASrK,WAAW,CAAC;EASxC7M,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IAACC,eAAA,gBATJ,QAAQ;IAAAA,eAAA,mBACL3B,MAAM,CAACI,eAAe,CAAC;IAClC;IAAAuB,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;EAIA;EACAuF,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC0E,WAAW,GAAG,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC2J,KAAK,CAAC3S,OAAO,CAAC;IACpE,IAAI,CAAC2S,KAAK,CACLrG,kBAAkB,CAAC,IAAI,CAAC3J,IAAI,CAAC,CAC7BlB,IAAI,CAAClF,SAAS,CAAC,IAAI,CAACsX,UAAU,CAAC,CAAC,CAChClS,SAAS,CAACmU,MAAM,IAAI,IAAI,CAACC,mBAAmB,CAACD,MAAM,CAAC,CAAC;IAC1D,IAAI,CAACE,UAAU,CAAC1M,OAAO,CAClB7H,IAAI,CAAClF,SAAS,CAAC,IAAI,CAACsX,UAAU,CAAC,CAAC,CAChClS,SAAS,CAAC,MAAM,IAAI,CAACoU,mBAAmB,CAAC,CAAC,CAAC;EACpD;EACAvR,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyR,MAAM,CAAC,CAAC;IACb,KAAK,CAACzR,WAAW,CAAC,CAAC;EACvB;EACA;EACAuR,mBAAmBA,CAACnU,QAAQ,EAAE;IAC1B,MAAMsU,MAAM,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACpC,IAAIvU,QAAQ,EAAE;MACV,IAAI,CAACwU,SAAS,GAAGxU,QAAQ;IAC7B;IACA,IAAIsU,MAAM,IAAI,IAAI,CAACE,SAAS,EAAE;MAC1B,MAAMzR,aAAa,GAAGuR,MAAM,CAACvR,aAAa;MAC1C,IAAI,CAACgO,KAAK,CAACrL,iBAAiB,CAAC,IAAI,CAAC8O,SAAS,EAAE,IAAI,CAACvN,WAAW,EAAElE,aAAa,EAAE,IAAI,CAACiO,KAAK,CAAC;IAC7F,CAAC,MACI;MACD;MACA,IAAI,CAAC/J,WAAW,CAACU,IAAI,CAAC,EAAE,CAAC;IAC7B;EACJ;EACA;EACA0M,MAAMA,CAAA,EAAG;IACL,MAAMC,MAAM,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACpC,IAAID,MAAM,EAAE;MACRA,MAAM,CAACvR,aAAa,CAAChF,KAAK,CAAC,CAAC;MAC5B,IAAI,CAACkJ,WAAW,CAACU,IAAI,CAAC,EAAE,CAAC;IAC7B;EACJ;EACA;EACA4M,cAAcA,CAAA,EAAG;IACb,MAAME,OAAO,GAAG,IAAI,CAACL,UAAU;IAC/B;IACA;IACA,OAAOK,OAAO,IAAIA,OAAO,CAACtN,IAAI,CAACmN,MAAM,IAAI,CAACA,MAAM,CAACI,KAAK,IAAIJ,MAAM,CAACI,KAAK,KAAK,IAAI,CAAC;EACpF;AAMJ;AAACC,kBAAA,GA7DKV,iBAAiB;AAAAjX,eAAA,CAAjBiX,iBAAiB,wBAAAW,2BAAAtU,iBAAA;EAAA,YAAAA,iBAAA,IAwDgF2T,kBAAiB;AAAA;AAAAjX,eAAA,CAxDlHiX,iBAAiB,8BAlyC0D9Y,EAAE,CAAAoF,iBAAA;EAAAC,IAAA,EA21CQyT,kBAAiB;EAAAxT,SAAA;EAAAiO,cAAA,WAAAmG,kCAAAjG,EAAA,EAAAC,GAAA,EAAAC,QAAA;IAAA,IAAAF,EAAA;MA31C3BzT,EAAE,CAAA4T,cAAA,CAAAD,QAAA,EA81CrB5O,iBAAiB;IAAA;IAAA,IAAA0O,EAAA;MAAA,IAAAI,EAAA;MA91CE7T,EAAE,CAAA8T,cAAA,CAAAD,EAAA,GAAF7T,EAAE,CAAA+T,WAAA,QAAAL,GAAA,CAAAuF,UAAA,GAAApF,EAAA;IAAA;EAAA;EAAAM,SAAA;EAAAM,QAAA;EAAAkF,QAAA,GAAF3Z,EAAE,CAAA4Z,kBAAA,CA21C8I,CACrN;IAAEC,OAAO,EAAEpL,WAAW;IAAEqL,WAAW,EAAEhB;EAAkB,CAAC,EACxD;IAAEe,OAAO,EAAE/U,yBAAyB;IAAEgV,WAAW,EAAEhB;EAAkB,CAAC,CACzE,GA91CwE9Y,EAAE,CAAA+Z,0BAAA;AAAA;AAg2CnF;EAAA,QAAAxU,SAAA,oBAAAA,SAAA,KAh2CiFvF,EAAE,CAAAwF,iBAAA,CAg2CQsT,iBAAiB,EAAc,CAAC;IAC/GzT,IAAI,EAAEjF,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChC+O,QAAQ,EAAE,mBAAmB;MAC7BuF,SAAS,EAAE,CACP;QAAEH,OAAO,EAAEpL,WAAW;QAAEqL,WAAW,EAAEhB;MAAkB,CAAC,EACxD;QAAEe,OAAO,EAAE/U,yBAAyB;QAAEgV,WAAW,EAAEhB;MAAkB,CAAC,CACzE;MACD7D,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEgE,UAAU,EAAE,CAAC;MACrD5T,IAAI,EAAEvE,eAAe;MACrB2E,IAAI,EAAE,CAACV,iBAAiB,EAAE;QAClB;QACA;QACAb,WAAW,EAAE;MACjB,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM+V,cAAc,GAAG,eAAe;AACtC;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EAWrB;EACA,IAAIhM,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACiM,MAAM;EACtB;EACA,IAAIjM,KAAKA,CAAClL,KAAK,EAAE;IACb,IAAI,CAACoX,cAAc,CAACpX,KAAK,CAAC;EAC9B;EAEA;AACJ;AACA;AACA;EACI,IAAIqX,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACE,eAAe,CAACF,MAAM,CAAC;EAChC;EAEAzY,WAAWA,CAAA,EAAG;IAAA,IAAA4Y,UAAA;IAAA3Y,eAAA,oBA7BF3B,MAAM,CAACuO,WAAW,CAAC;IAAA5M,eAAA,gBACvB3B,MAAM,CAACyG,OAAO,CAAC;IAAA9E,eAAA,mBACZ3B,MAAM,CAACM,UAAU,CAAC;IAAAqB,eAAA,eACtB3B,MAAM,CAACqB,cAAc,EAAE;MAAEyD,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjD;IAAAnD,eAAA;IAEA;IAAAA,eAAA,qBACa,IAAI7C,OAAO,CAAC,CAAC;IAC1B;IAAA6C,eAAA,sBACc,IAAI;IAAAA,eAAA;IAAAA,eAAA,kBAmBR,EAAE;IAER,IAAI,CAAC4Y,WAAW,CAAC,CAAC;IAClB,CAAAD,UAAA,OAAI,CAAC/O,IAAI,cAAA+O,UAAA,eAATA,UAAA,CAAWE,MAAM,CAAChW,IAAI,CAAClF,SAAS,CAAC,IAAI,CAACsX,UAAU,CAAC,CAAC,CAAClS,SAAS,CAAC,MAAM,IAAI,CAAC6V,WAAW,CAAC,IAAI,CAAC,CAAC;IAC1F;IACA;IACA;IACA,IAAI,CAACE,SAAS,CAACxE,YAAY,CAACvR,SAAS,CAAC,MAAM,IAAI,CAAC6V,WAAW,CAAC,CAAC,CAAC;EACnE;EACAhT,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqP,UAAU,CAAC9O,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC8O,UAAU,CAAChP,QAAQ,CAAC,CAAC;EAC9B;EACA;EACA8S,cAAcA,CAAA,EAAG;IAAA,IAAAC,IAAA;IACb,MAAMC,SAAS,IAAAD,IAAA,GAAI,IAAI,CAACF,SAAS,CAAC/U,IAAI,IAAI,IAAI,CAACgQ,KAAK,CAACrF,SAAS,CAAC,IAAI,CAACoK,SAAS,CAAC/U,IAAI,CAAC,cAAAiV,IAAA,cAAAA,IAAA,GAAK,IAAI;IAC5F,MAAM3M,KAAK,GAAG,IAAI,CAACiM,MAAM,IAAI,IAAI,GAAGW,SAAS,GAAG,IAAI,CAACX,MAAM;IAC3D,OAAO,OAAOjM,KAAK,KAAK,QAAQ,GAAG,GAAGA,KAAK,GAAG,IAAI,CAACoM,OAAO,GAAG,IAAI,CAACS,WAAW,EAAE,GAAG,IAAI;EAC1F;EACAN,WAAWA,CAACO,WAAW,GAAG,KAAK,EAAE;IAC7B,MAAMC,OAAO,GAAG,IAAI,CAACL,cAAc,CAAC,CAAC;IACrC,IAAIK,OAAO,KAAK,IAAI,CAACC,eAAe,IAAIF,WAAW,EAAE;MACjD,MAAMnC,OAAO,GAAG,IAAI,CAACsC,QAAQ,CAACjK,aAAa;MAC3C,MAAMkK,WAAW,GAAG,IAAI,CAAC3P,IAAI,IAAI,IAAI,CAACA,IAAI,CAACzI,KAAK,KAAK,KAAK,GAAG,cAAc,GAAG,aAAa;MAC3F,MAAMqY,SAAS,GAAGD,WAAW,KAAK,aAAa,GAAG,cAAc,GAAG,aAAa;MAChFvC,OAAO,CAACyC,KAAK,CAACF,WAAW,CAAC,GAAGH,OAAO,IAAI,EAAE;MAC1CpC,OAAO,CAACyC,KAAK,CAACD,SAAS,CAAC,GAAG,EAAE;MAC7B,IAAI,CAACH,eAAe,GAAGD,OAAO;IAClC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIb,cAAcA,CAACpX,KAAK,EAAE;IAClB;IACA;IACA;IACA,IAAI,CAACmX,MAAM,GAAGoB,KAAK,CAACvY,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;IACzC,IAAI,CAACyX,WAAW,CAAC,CAAC;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,eAAeA,CAACF,MAAM,EAAE;IACpB,IAAIrX,KAAK,GAAGqX,MAAM;IAClB,IAAImB,KAAK,GAAG,IAAI;IAChB,IAAI,OAAOnB,MAAM,KAAK,QAAQ,EAAE;MAC5B,MAAMoB,KAAK,GAAGpB,MAAM,CAACqB,KAAK,CAACzB,cAAc,CAAC;MAC1CjX,KAAK,GAAGyY,KAAK,CAAC,CAAC,CAAC;MAChBD,KAAK,GAAGC,KAAK,CAAC,CAAC,CAAC,IAAID,KAAK;IAC7B;IACA,IAAI,CAACT,WAAW,GAAGS,KAAK;IACxB,IAAI,CAAClB,OAAO,GAAGpZ,eAAe,CAAC8B,KAAK,CAAC;IACrC,IAAI,CAACyX,WAAW,CAAC,CAAC;EACtB;AAGJ;AAACkB,mBAAA,GA5FKzB,kBAAkB;AAAArY,eAAA,CAAlBqY,kBAAkB,wBAAA0B,4BAAAzW,iBAAA;EAAA,YAAAA,iBAAA,IA0F+E+U,mBAAkB;AAAA;AAAArY,eAAA,CA1FnHqY,kBAAkB,8BA53CyDla,EAAE,CAAAoF,iBAAA;EAAAC,IAAA,EAu9CQ6U,mBAAkB;EAAA5U,SAAA;EAAAW,MAAA;IAAAiI,KAAA,qCAAyGhN,eAAe;IAAAmZ,MAAA;EAAA;AAAA;AAErO;EAAA,QAAA9U,SAAA,oBAAAA,SAAA,KAz9CiFvF,EAAE,CAAAwF,iBAAA,CAy9CQ0U,kBAAkB,EAAc,CAAC;IAChH7U,IAAI,EAAEjF,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEwI,KAAK,EAAE,CAAC;MAChD7I,IAAI,EAAEzE,KAAK;MACX6E,IAAI,EAAE,CAAC;QAAEW,KAAK,EAAE,oBAAoB;QAAEkS,SAAS,EAAEpX;MAAgB,CAAC;IACtE,CAAC,CAAC;IAAEmZ,MAAM,EAAE,CAAC;MACThV,IAAI,EAAEzE,KAAK;MACX6E,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMoW,iBAAiB,CAAC;EAKpBja,WAAWA,CAAA,EAAG;IAAAC,eAAA,gBAJN3B,MAAM,CAACyG,OAAO,CAAC;IAAA9E,eAAA,oBACX3B,MAAM,CAACuO,WAAW,CAAC;IAC/B;IAAA5M,eAAA,oBACY,KAAK;EACD;EAChB;EACA;EACA;EACA;EACAia,OAAOA,CAAA,EAAG;IACN,IAAI,CAACC,SAAS,GACR,IAAI,CAACnG,KAAK,CAACpT,iBAAiB,CAAC,IAAI,CAACmY,SAAS,CAAC/U,IAAI,CAAC,GACjD,IAAI,CAACgQ,KAAK,CAAC9T,MAAM,CAAC,IAAI,CAAC6Y,SAAS,CAAC/U,IAAI,CAAC;IAC5C,IAAI,CAACgQ,KAAK,CAACxN,WAAW,CAACqP,SAAS,CAAC,IAAI,CAACkD,SAAS,CAAC;EACpD;AAGJ;AAACqB,kBAAA,GAlBKH,iBAAiB;AAAAha,eAAA,CAAjBga,iBAAiB,wBAAAI,2BAAA9W,iBAAA;EAAA,YAAAA,iBAAA,IAgBgF0W,kBAAiB;AAAA;AAAAha,eAAA,CAhBlHga,iBAAiB,8BAz+C0D7b,EAAE,CAAAoF,iBAAA;EAAAC,IAAA,EA0/CQwW,kBAAiB;EAAAvW,SAAA;EAAA6O,SAAA,eAA+K,IAAI;EAAAC,YAAA,WAAA8H,gCAAAzI,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA1/C9MzT,EAAE,CAAAsU,UAAA,mBAAA6H,4CAAA3H,MAAA;QA0/CQd,GAAA,CAAAoI,OAAA,CAAQ,CAAC;QAAA,OAAEtH,MAAA,CAAA4H,eAAA,CAAuB,CAAC;MAAA,CAAnB,CAAC,2BAAAC,oDAAA7H,MAAA;QAAjBd,GAAA,CAAAoI,OAAA,CAAQ,CAAC;QAAA,OAAEtH,MAAA,CAAA8H,cAAA,CAAsB,CAAC;MAAA,CAAlB,CAAC,2BAAAC,oDAAA/H,MAAA;QAAjBd,GAAA,CAAAoI,OAAA,CAAQ,CAAC;QAAA,OAAEtH,MAAA,CAAA8H,cAAA,CAAsB,CAAC;MAAA,CAAlB,CAAC;IAAA;EAAA;EAAArW,MAAA;IAAA8V,SAAA,iDAAwH/a,gBAAgB;EAAA;AAAA;AAEpP;EAAA,QAAAuE,SAAA,oBAAAA,SAAA,KA5/CiFvF,EAAE,CAAAwF,iBAAA,CA4/CQqW,iBAAiB,EAAc,CAAC;IAC/GxW,IAAI,EAAEjF,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BuP,IAAI,EAAE;QACF,SAAS,EAAE,sCAAsC;QACjD,iBAAiB,EAAE,qCAAqC;QACxD,iBAAiB,EAAE,qCAAqC;QACxD,UAAU,EAAE;MAChB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE8G,SAAS,EAAE,CAAC;MACpD1W,IAAI,EAAEzE,KAAK;MACX6E,IAAI,EAAE,CAAC;QAAEW,KAAK,EAAE,4BAA4B;QAAEkS,SAAS,EAAEtX;MAAiB,CAAC;IAC/E,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwb,qBAAqB,GAAG,CAC1B1D,iBAAiB,EACjBhT,cAAc,EACdoU,kBAAkB,EAClB2B,iBAAiB,EACjBlV,OAAO,EACP8H,WAAW,EACX1J,iBAAiB,CACpB;AACD,MAAM0X,aAAa,CAAC;AAgBnBC,cAAA,GAhBKD,aAAa;AAAA5a,eAAA,CAAb4a,aAAa,wBAAAE,uBAAAxX,iBAAA;EAAA,YAAAA,iBAAA,IACoFsX,cAAa;AAAA;AAAA5a,eAAA,CAD9G4a,aAAa,8BArhD8Dzc,EAAE,CAAA4c,gBAAA;EAAAvX,IAAA,EAuhDqBoX,cAAa;EAAApH,OAAA,GAAYyD,iBAAiB,EACtIhT,cAAc,EACdoU,kBAAkB,EAClB2B,iBAAiB,EACjBlV,OAAO,EACP8H,WAAW,EACX1J,iBAAiB;EAAA8X,OAAA,GAAa/D,iBAAiB,EAC/ChT,cAAc,EACdoU,kBAAkB,EAClB2B,iBAAiB,EACjBlV,OAAO,EACP8H,WAAW,EACX1J,iBAAiB;AAAA;AAAAlD,eAAA,CAdvB4a,aAAa,8BArhD8Dzc,EAAE,CAAA8c,gBAAA;AAsiDnF;EAAA,QAAAvX,SAAA,oBAAAA,SAAA,KAtiDiFvF,EAAE,CAAAwF,iBAAA,CAsiDQiX,aAAa,EAAc,CAAC;IAC3GpX,IAAI,EAAElE,QAAQ;IACdsE,IAAI,EAAE,CAAC;MACC4P,OAAO,EAAEmH,qBAAqB;MAC9BK,OAAO,EAAEL;IACb,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAAS7a,eAAe,EAAEmD,yBAAyB,EAAEgU,iBAAiB,EAAEnS,OAAO,EAAE8V,aAAa,EAAEhO,WAAW,EAAE3I,cAAc,EAAEf,iBAAiB,EAAEY,wBAAwB,EAAEuU,kBAAkB,EAAE2B,iBAAiB,EAAE3Y,eAAe,EAAEW,iBAAiB,EAAE6C,4BAA4B,EAAED,0BAA0B,EAAED,kCAAkC,EAAED,mCAAmC,EAAEF,6BAA6B;AACrZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}