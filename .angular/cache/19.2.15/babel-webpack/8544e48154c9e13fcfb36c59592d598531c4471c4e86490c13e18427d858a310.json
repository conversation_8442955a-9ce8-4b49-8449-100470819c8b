{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _MatDynamicFormComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./mat-dynamic-form.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./mat-dynamic-form.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostBinding, Input } from '@angular/core';\nimport { UntypedFormGroup } from '@angular/forms';\nimport { createControlItem } from './dynamic-form.model';\nlet MatDynamicFormComponent = (_MatDynamicFormComponent = class MatDynamicFormComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.form = new UntypedFormGroup({});\n    this.readonly = false;\n    this.submitted = false;\n    this.direction = 'column';\n    this.items = [];\n  }\n  ngOnChanges(changes) {\n    if (changes.hasOwnProperty('options')) {\n      this.onOptions(changes.options.currentValue);\n    }\n  }\n  onOptions(value) {\n    const options = Array.isArray(value) ? value : Object.entries(value).map(([key, option]) => _objectSpread(_objectSpread({}, option), {}, {\n      key\n    }));\n    this.items = options.map(option => createControlItem(option));\n    if (this.controlName) {\n      const controls = this.items.reduce((result, item) => _objectSpread(_objectSpread({}, result), {}, {\n        [item.option.key]: item.control\n      }), {});\n      this.form.setControl(this.controlName, new UntypedFormGroup(controls));\n    } else {\n      this.items.forEach(item => {\n        this.form.setControl(item.option.key, item.control);\n      });\n      const keys = this.items.map(({\n        option: {\n          key\n        }\n      }) => key);\n      Object.keys(this.form.controls).forEach(control => {\n        if (!keys.includes(control)) {\n          this.form.removeControl(control);\n        }\n      });\n    }\n    this.cdr.detectChanges();\n  }\n}, _MatDynamicFormComponent.ctorParameters = () => [{\n  type: ChangeDetectorRef\n}], _MatDynamicFormComponent.propDecorators = {\n  options: [{\n    type: Input\n  }],\n  controlName: [{\n    type: Input\n  }],\n  form: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  direction: [{\n    type: Input\n  }, {\n    type: HostBinding,\n    args: ['style.flex-direction']\n  }]\n}, _MatDynamicFormComponent);\nMatDynamicFormComponent = __decorate([Component({\n  selector: 'lib-dynamic-form',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], MatDynamicFormComponent);\nexport { MatDynamicFormComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectionStrategy", "ChangeDetectorRef", "Component", "HostBinding", "Input", "UntypedFormGroup", "createControlItem", "MatDynamicFormComponent", "_MatDynamicFormComponent", "constructor", "cdr", "form", "readonly", "submitted", "direction", "items", "ngOnChanges", "changes", "hasOwnProperty", "onOptions", "options", "currentValue", "value", "Array", "isArray", "Object", "entries", "map", "key", "option", "_objectSpread", "controlName", "controls", "reduce", "result", "item", "control", "setControl", "for<PERSON>ach", "keys", "includes", "removeControl", "detectChanges", "ctorParameters", "type", "propDecorators", "args", "selector", "template", "changeDetection", "OnPush", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/mat-dynamic-form.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./mat-dynamic-form.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./mat-dynamic-form.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostBinding, Input } from '@angular/core';\nimport { UntypedFormGroup } from '@angular/forms';\nimport { createControlItem } from './dynamic-form.model';\nlet MatDynamicFormComponent = class MatDynamicFormComponent {\n    constructor(cdr) {\n        this.cdr = cdr;\n        this.form = new UntypedFormGroup({});\n        this.readonly = false;\n        this.submitted = false;\n        this.direction = 'column';\n        this.items = [];\n    }\n    ngOnChanges(changes) {\n        if (changes.hasOwnProperty('options')) {\n            this.onOptions(changes.options.currentValue);\n        }\n    }\n    onOptions(value) {\n        const options = Array.isArray(value) ? value : Object.entries(value).map(([key, option]) => ({\n            ...option,\n            key,\n        }));\n        this.items = options.map(option => createControlItem(option));\n        if (this.controlName) {\n            const controls = this.items.reduce((result, item) => ({\n                ...result,\n                [item.option.key]: item.control\n            }), {});\n            this.form.setControl(this.controlName, new UntypedFormGroup(controls));\n        }\n        else {\n            this.items.forEach(item => {\n                this.form.setControl(item.option.key, item.control);\n            });\n            const keys = this.items.map(({ option: { key } }) => key);\n            Object.keys(this.form.controls).forEach(control => {\n                if (!keys.includes(control)) {\n                    this.form.removeControl(control);\n                }\n            });\n        }\n        this.cdr.detectChanges();\n    }\n    static { this.ctorParameters = () => [\n        { type: ChangeDetectorRef }\n    ]; }\n    static { this.propDecorators = {\n        options: [{ type: Input }],\n        controlName: [{ type: Input }],\n        form: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        direction: [{ type: Input }, { type: HostBinding, args: ['style.flex-direction',] }]\n    }; }\n};\nMatDynamicFormComponent = __decorate([\n    Component({\n        selector: 'lib-dynamic-form',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], MatDynamicFormComponent);\nexport { MatDynamicFormComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,SAASC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAK,QAAQ,eAAe;AACzG,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,IAAIC,uBAAuB,IAAAC,wBAAA,GAAG,MAAMD,uBAAuB,CAAC;EACxDE,WAAWA,CAACC,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,IAAI,GAAG,IAAIN,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,CAACO,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,QAAQ;IACzB,IAAI,CAACC,KAAK,GAAG,EAAE;EACnB;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACC,cAAc,CAAC,SAAS,CAAC,EAAE;MACnC,IAAI,CAACC,SAAS,CAACF,OAAO,CAACG,OAAO,CAACC,YAAY,CAAC;IAChD;EACJ;EACAF,SAASA,CAACG,KAAK,EAAE;IACb,MAAMF,OAAO,GAAGG,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAGG,MAAM,CAACC,OAAO,CAACJ,KAAK,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,KAAAC,aAAA,CAAAA,aAAA,KAChFD,MAAM;MACTD;IAAG,EACL,CAAC;IACH,IAAI,CAACb,KAAK,GAAGK,OAAO,CAACO,GAAG,CAACE,MAAM,IAAIvB,iBAAiB,CAACuB,MAAM,CAAC,CAAC;IAC7D,IAAI,IAAI,CAACE,WAAW,EAAE;MAClB,MAAMC,QAAQ,GAAG,IAAI,CAACjB,KAAK,CAACkB,MAAM,CAAC,CAACC,MAAM,EAAEC,IAAI,KAAAL,aAAA,CAAAA,aAAA,KACzCI,MAAM;QACT,CAACC,IAAI,CAACN,MAAM,CAACD,GAAG,GAAGO,IAAI,CAACC;MAAO,EACjC,EAAE,CAAC,CAAC,CAAC;MACP,IAAI,CAACzB,IAAI,CAAC0B,UAAU,CAAC,IAAI,CAACN,WAAW,EAAE,IAAI1B,gBAAgB,CAAC2B,QAAQ,CAAC,CAAC;IAC1E,CAAC,MACI;MACD,IAAI,CAACjB,KAAK,CAACuB,OAAO,CAACH,IAAI,IAAI;QACvB,IAAI,CAACxB,IAAI,CAAC0B,UAAU,CAACF,IAAI,CAACN,MAAM,CAACD,GAAG,EAAEO,IAAI,CAACC,OAAO,CAAC;MACvD,CAAC,CAAC;MACF,MAAMG,IAAI,GAAG,IAAI,CAACxB,KAAK,CAACY,GAAG,CAAC,CAAC;QAAEE,MAAM,EAAE;UAAED;QAAI;MAAE,CAAC,KAAKA,GAAG,CAAC;MACzDH,MAAM,CAACc,IAAI,CAAC,IAAI,CAAC5B,IAAI,CAACqB,QAAQ,CAAC,CAACM,OAAO,CAACF,OAAO,IAAI;QAC/C,IAAI,CAACG,IAAI,CAACC,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACzB,IAAI,CAACzB,IAAI,CAAC8B,aAAa,CAACL,OAAO,CAAC;QACpC;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC1B,GAAG,CAACgC,aAAa,CAAC,CAAC;EAC5B;AAYJ,CAAC,EAXYlC,wBAAA,CAAKmC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE3C;AAAkB,CAAC,CAC9B,EACQO,wBAAA,CAAKqC,cAAc,GAAG;EAC3BzB,OAAO,EAAE,CAAC;IAAEwB,IAAI,EAAExC;EAAM,CAAC,CAAC;EAC1B2B,WAAW,EAAE,CAAC;IAAEa,IAAI,EAAExC;EAAM,CAAC,CAAC;EAC9BO,IAAI,EAAE,CAAC;IAAEiC,IAAI,EAAExC;EAAM,CAAC,CAAC;EACvBQ,QAAQ,EAAE,CAAC;IAAEgC,IAAI,EAAExC;EAAM,CAAC,CAAC;EAC3BS,SAAS,EAAE,CAAC;IAAE+B,IAAI,EAAExC;EAAM,CAAC,CAAC;EAC5BU,SAAS,EAAE,CAAC;IAAE8B,IAAI,EAAExC;EAAM,CAAC,EAAE;IAAEwC,IAAI,EAAEzC,WAAW;IAAE2C,IAAI,EAAE,CAAC,sBAAsB;EAAG,CAAC;AACvF,CAAC,EAAAtC,wBAAA,CACJ;AACDD,uBAAuB,GAAGV,UAAU,CAAC,CACjCK,SAAS,CAAC;EACN6C,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAElD,oBAAoB;EAC9BmD,eAAe,EAAEjD,uBAAuB,CAACkD,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACrD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEQ,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}