{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Action } from './Action';\nimport { intervalProvider } from './intervalProvider';\nimport { arrRemove } from '../util/arrRemove';\nvar AsyncAction = function (_super) {\n  __extends(AsyncAction, _super);\n  function AsyncAction(scheduler, work) {\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    _this.pending = false;\n    return _this;\n  }\n  AsyncAction.prototype.schedule = function (state, delay) {\n    var _a;\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (this.closed) {\n      return this;\n    }\n    this.state = state;\n    var id = this.id;\n    var scheduler = this.scheduler;\n    if (id != null) {\n      this.id = this.recycleAsyncId(scheduler, id, delay);\n    }\n    this.pending = true;\n    this.delay = delay;\n    this.id = (_a = this.id) !== null && _a !== void 0 ? _a : this.requestAsyncId(scheduler, this.id, delay);\n    return this;\n  };\n  AsyncAction.prototype.requestAsyncId = function (scheduler, _id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return intervalProvider.setInterval(scheduler.flush.bind(scheduler, this), delay);\n  };\n  AsyncAction.prototype.recycleAsyncId = function (_scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay != null && this.delay === delay && this.pending === false) {\n      return id;\n    }\n    if (id != null) {\n      intervalProvider.clearInterval(id);\n    }\n    return undefined;\n  };\n  AsyncAction.prototype.execute = function (state, delay) {\n    if (this.closed) {\n      return new Error('executing a cancelled action');\n    }\n    this.pending = false;\n    var error = this._execute(state, delay);\n    if (error) {\n      return error;\n    } else if (this.pending === false && this.id != null) {\n      this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n    }\n  };\n  AsyncAction.prototype._execute = function (state, _delay) {\n    var errored = false;\n    var errorValue;\n    try {\n      this.work(state);\n    } catch (e) {\n      errored = true;\n      errorValue = e ? e : new Error('Scheduled action threw falsy error');\n    }\n    if (errored) {\n      this.unsubscribe();\n      return errorValue;\n    }\n  };\n  AsyncAction.prototype.unsubscribe = function () {\n    if (!this.closed) {\n      var _a = this,\n        id = _a.id,\n        scheduler = _a.scheduler;\n      var actions = scheduler.actions;\n      this.work = this.state = this.scheduler = null;\n      this.pending = false;\n      arrRemove(actions, this);\n      if (id != null) {\n        this.id = this.recycleAsyncId(scheduler, id, null);\n      }\n      this.delay = null;\n      _super.prototype.unsubscribe.call(this);\n    }\n  };\n  return AsyncAction;\n}(Action);\nexport { AsyncAction };\n//# sourceMappingURL=AsyncAction.js.map", "map": {"version": 3, "names": ["__extends", "Action", "intervalProvider", "arr<PERSON><PERSON><PERSON>", "AsyncAction", "_super", "scheduler", "work", "_this", "call", "pending", "prototype", "schedule", "state", "delay", "_a", "closed", "id", "recycleAsyncId", "requestAsyncId", "_id", "setInterval", "flush", "bind", "_scheduler", "clearInterval", "undefined", "execute", "Error", "error", "_execute", "_delay", "errored", "errorValue", "e", "unsubscribe", "actions"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/AsyncAction.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { Action } from './Action';\nimport { intervalProvider } from './intervalProvider';\nimport { arrRemove } from '../util/arrRemove';\nvar AsyncAction = (function (_super) {\n    __extends(AsyncAction, _super);\n    function AsyncAction(scheduler, work) {\n        var _this = _super.call(this, scheduler, work) || this;\n        _this.scheduler = scheduler;\n        _this.work = work;\n        _this.pending = false;\n        return _this;\n    }\n    AsyncAction.prototype.schedule = function (state, delay) {\n        var _a;\n        if (delay === void 0) { delay = 0; }\n        if (this.closed) {\n            return this;\n        }\n        this.state = state;\n        var id = this.id;\n        var scheduler = this.scheduler;\n        if (id != null) {\n            this.id = this.recycleAsyncId(scheduler, id, delay);\n        }\n        this.pending = true;\n        this.delay = delay;\n        this.id = (_a = this.id) !== null && _a !== void 0 ? _a : this.requestAsyncId(scheduler, this.id, delay);\n        return this;\n    };\n    AsyncAction.prototype.requestAsyncId = function (scheduler, _id, delay) {\n        if (delay === void 0) { delay = 0; }\n        return intervalProvider.setInterval(scheduler.flush.bind(scheduler, this), delay);\n    };\n    AsyncAction.prototype.recycleAsyncId = function (_scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        if (delay != null && this.delay === delay && this.pending === false) {\n            return id;\n        }\n        if (id != null) {\n            intervalProvider.clearInterval(id);\n        }\n        return undefined;\n    };\n    AsyncAction.prototype.execute = function (state, delay) {\n        if (this.closed) {\n            return new Error('executing a cancelled action');\n        }\n        this.pending = false;\n        var error = this._execute(state, delay);\n        if (error) {\n            return error;\n        }\n        else if (this.pending === false && this.id != null) {\n            this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n        }\n    };\n    AsyncAction.prototype._execute = function (state, _delay) {\n        var errored = false;\n        var errorValue;\n        try {\n            this.work(state);\n        }\n        catch (e) {\n            errored = true;\n            errorValue = e ? e : new Error('Scheduled action threw falsy error');\n        }\n        if (errored) {\n            this.unsubscribe();\n            return errorValue;\n        }\n    };\n    AsyncAction.prototype.unsubscribe = function () {\n        if (!this.closed) {\n            var _a = this, id = _a.id, scheduler = _a.scheduler;\n            var actions = scheduler.actions;\n            this.work = this.state = this.scheduler = null;\n            this.pending = false;\n            arrRemove(actions, this);\n            if (id != null) {\n                this.id = this.recycleAsyncId(scheduler, id, null);\n            }\n            this.delay = null;\n            _super.prototype.unsubscribe.call(this);\n        }\n    };\n    return AsyncAction;\n}(Action));\nexport { AsyncAction };\n//# sourceMappingURL=AsyncAction.js.map"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,IAAIC,WAAW,GAAI,UAAUC,MAAM,EAAE;EACjCL,SAAS,CAACI,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAACE,SAAS,EAAEC,IAAI,EAAE;IAClC,IAAIC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAAC,IAAI,EAAEH,SAAS,EAAEC,IAAI,CAAC,IAAI,IAAI;IACtDC,KAAK,CAACF,SAAS,GAAGA,SAAS;IAC3BE,KAAK,CAACD,IAAI,GAAGA,IAAI;IACjBC,KAAK,CAACE,OAAO,GAAG,KAAK;IACrB,OAAOF,KAAK;EAChB;EACAJ,WAAW,CAACO,SAAS,CAACC,QAAQ,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACrD,IAAIC,EAAE;IACN,IAAID,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAI,IAAI,CAACE,MAAM,EAAE;MACb,OAAO,IAAI;IACf;IACA,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAII,EAAE,GAAG,IAAI,CAACA,EAAE;IAChB,IAAIX,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIW,EAAE,IAAI,IAAI,EAAE;MACZ,IAAI,CAACA,EAAE,GAAG,IAAI,CAACC,cAAc,CAACZ,SAAS,EAAEW,EAAE,EAAEH,KAAK,CAAC;IACvD;IACA,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACI,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,EAAE,GAAG,CAACF,EAAE,GAAG,IAAI,CAACE,EAAE,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACI,cAAc,CAACb,SAAS,EAAE,IAAI,CAACW,EAAE,EAAEH,KAAK,CAAC;IACxG,OAAO,IAAI;EACf,CAAC;EACDV,WAAW,CAACO,SAAS,CAACQ,cAAc,GAAG,UAAUb,SAAS,EAAEc,GAAG,EAAEN,KAAK,EAAE;IACpE,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,OAAOZ,gBAAgB,CAACmB,WAAW,CAACf,SAAS,CAACgB,KAAK,CAACC,IAAI,CAACjB,SAAS,EAAE,IAAI,CAAC,EAAEQ,KAAK,CAAC;EACrF,CAAC;EACDV,WAAW,CAACO,SAAS,CAACO,cAAc,GAAG,UAAUM,UAAU,EAAEP,EAAE,EAAEH,KAAK,EAAE;IACpE,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAIA,KAAK,IAAI,IAAI,IAAI,IAAI,CAACA,KAAK,KAAKA,KAAK,IAAI,IAAI,CAACJ,OAAO,KAAK,KAAK,EAAE;MACjE,OAAOO,EAAE;IACb;IACA,IAAIA,EAAE,IAAI,IAAI,EAAE;MACZf,gBAAgB,CAACuB,aAAa,CAACR,EAAE,CAAC;IACtC;IACA,OAAOS,SAAS;EACpB,CAAC;EACDtB,WAAW,CAACO,SAAS,CAACgB,OAAO,GAAG,UAAUd,KAAK,EAAEC,KAAK,EAAE;IACpD,IAAI,IAAI,CAACE,MAAM,EAAE;MACb,OAAO,IAAIY,KAAK,CAAC,8BAA8B,CAAC;IACpD;IACA,IAAI,CAAClB,OAAO,GAAG,KAAK;IACpB,IAAImB,KAAK,GAAG,IAAI,CAACC,QAAQ,CAACjB,KAAK,EAAEC,KAAK,CAAC;IACvC,IAAIe,KAAK,EAAE;MACP,OAAOA,KAAK;IAChB,CAAC,MACI,IAAI,IAAI,CAACnB,OAAO,KAAK,KAAK,IAAI,IAAI,CAACO,EAAE,IAAI,IAAI,EAAE;MAChD,IAAI,CAACA,EAAE,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACZ,SAAS,EAAE,IAAI,CAACW,EAAE,EAAE,IAAI,CAAC;IAChE;EACJ,CAAC;EACDb,WAAW,CAACO,SAAS,CAACmB,QAAQ,GAAG,UAAUjB,KAAK,EAAEkB,MAAM,EAAE;IACtD,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAIC,UAAU;IACd,IAAI;MACA,IAAI,CAAC1B,IAAI,CAACM,KAAK,CAAC;IACpB,CAAC,CACD,OAAOqB,CAAC,EAAE;MACNF,OAAO,GAAG,IAAI;MACdC,UAAU,GAAGC,CAAC,GAAGA,CAAC,GAAG,IAAIN,KAAK,CAAC,oCAAoC,CAAC;IACxE;IACA,IAAII,OAAO,EAAE;MACT,IAAI,CAACG,WAAW,CAAC,CAAC;MAClB,OAAOF,UAAU;IACrB;EACJ,CAAC;EACD7B,WAAW,CAACO,SAAS,CAACwB,WAAW,GAAG,YAAY;IAC5C,IAAI,CAAC,IAAI,CAACnB,MAAM,EAAE;MACd,IAAID,EAAE,GAAG,IAAI;QAAEE,EAAE,GAAGF,EAAE,CAACE,EAAE;QAAEX,SAAS,GAAGS,EAAE,CAACT,SAAS;MACnD,IAAI8B,OAAO,GAAG9B,SAAS,CAAC8B,OAAO;MAC/B,IAAI,CAAC7B,IAAI,GAAG,IAAI,CAACM,KAAK,GAAG,IAAI,CAACP,SAAS,GAAG,IAAI;MAC9C,IAAI,CAACI,OAAO,GAAG,KAAK;MACpBP,SAAS,CAACiC,OAAO,EAAE,IAAI,CAAC;MACxB,IAAInB,EAAE,IAAI,IAAI,EAAE;QACZ,IAAI,CAACA,EAAE,GAAG,IAAI,CAACC,cAAc,CAACZ,SAAS,EAAEW,EAAE,EAAE,IAAI,CAAC;MACtD;MACA,IAAI,CAACH,KAAK,GAAG,IAAI;MACjBT,MAAM,CAACM,SAAS,CAACwB,WAAW,CAAC1B,IAAI,CAAC,IAAI,CAAC;IAC3C;EACJ,CAAC;EACD,OAAOL,WAAW;AACtB,CAAC,CAACH,MAAM,CAAE;AACV,SAASG,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}