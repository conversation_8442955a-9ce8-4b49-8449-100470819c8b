{"ast": null, "code": "var _ActionConfirmDialogComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./action-confirm-dialog.component.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nlet ActionConfirmDialogComponent = (_ActionConfirmDialogComponent = class ActionConfirmDialogComponent {\n  constructor(data, dialogRef) {\n    this.data = data;\n    this.dialogRef = dialogRef;\n  }\n  ngOnInit() {}\n  doConfirm() {\n    this.dialogRef.close({\n      confirmed: true\n    });\n  }\n}, _ActionConfirmDialogComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [MAT_DIALOG_DATA]\n  }]\n}, {\n  type: MatDialogRef\n}], _ActionConfirmDialogComponent);\nActionConfirmDialogComponent = __decorate([Component({\n  selector: 'lib-swui-action-confirm-dialog',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], ActionConfirmDialogComponent);\nexport { ActionConfirmDialogComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "MAT_DIALOG_DATA", "MatDialogRef", "ActionConfirmDialogComponent", "_ActionConfirmDialogComponent", "constructor", "data", "dialogRef", "ngOnInit", "doConfirm", "close", "confirmed", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-page-panel/action-confirm-dialog/action-confirm-dialog.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./action-confirm-dialog.component.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nlet ActionConfirmDialogComponent = class ActionConfirmDialogComponent {\n    constructor(data, dialogRef) {\n        this.data = data;\n        this.dialogRef = dialogRef;\n    }\n    ngOnInit() {\n    }\n    doConfirm() {\n        this.dialogRef.close({ confirmed: true });\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [MAT_DIALOG_DATA,] }] },\n        { type: MatDialogRef }\n    ]; }\n};\nActionConfirmDialogComponent = __decorate([\n    Component({\n        selector: 'lib-swui-action-confirm-dialog',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], ActionConfirmDialogComponent);\nexport { ActionConfirmDialogComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,mDAAmD;AACpF,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,eAAe,EAAEC,YAAY,QAAQ,0BAA0B;AACxE,IAAIC,4BAA4B,IAAAC,6BAAA,GAAG,MAAMD,4BAA4B,CAAC;EAClEE,WAAWA,CAACC,IAAI,EAAEC,SAAS,EAAE;IACzB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAC,QAAQA,CAAA,EAAG,CACX;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACF,SAAS,CAACG,KAAK,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;EAC7C;AAKJ,CAAC,EAJYP,6BAAA,CAAKQ,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEb,MAAM;IAAEgB,IAAI,EAAE,CAACf,eAAe;EAAG,CAAC;AAAE,CAAC,EAC7E;EAAEY,IAAI,EAAEX;AAAa,CAAC,CACzB,EAAAE,6BAAA,CACJ;AACDD,4BAA4B,GAAGN,UAAU,CAAC,CACtCE,SAAS,CAAC;EACNkB,QAAQ,EAAE,gCAAgC;EAC1CC,QAAQ,EAAEpB,oBAAoB;EAC9BqB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEhB,4BAA4B,CAAC;AAChC,SAASA,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}