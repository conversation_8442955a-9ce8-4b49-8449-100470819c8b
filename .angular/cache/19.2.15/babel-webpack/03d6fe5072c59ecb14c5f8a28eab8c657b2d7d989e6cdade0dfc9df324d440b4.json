{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiGridWidgetChooserComponent } from './widget-chooser.component';\nlet SwuiGridWidgetChooserModule = class SwuiGridWidgetChooserModule {};\nSwuiGridWidgetChooserModule = __decorate([NgModule({\n  imports: [CommonModule],\n  declarations: [SwuiGridWidgetChooserComponent],\n  exports: [SwuiGridWidgetChooserComponent]\n})], SwuiGridWidgetChooserModule);\nexport { SwuiGridWidgetChooserModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "SwuiGridWidgetChooserComponent", "SwuiGridWidgetChooserModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/widget-chooser/widget-chooser.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiGridWidgetChooserComponent } from './widget-chooser.component';\nlet SwuiGridWidgetChooserModule = class SwuiGridWidgetChooserModule {\n};\nSwuiGridWidgetChooserModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule\n        ],\n        declarations: [\n            SwuiGridWidgetChooserComponent\n        ],\n        exports: [\n            SwuiGridWidgetChooserComponent\n        ]\n    })\n], SwuiGridWidgetChooserModule);\nexport { SwuiGridWidgetChooserModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,8BAA8B,QAAQ,4BAA4B;AAC3E,IAAIC,2BAA2B,GAAG,MAAMA,2BAA2B,CAAC,EACnE;AACDA,2BAA2B,GAAGJ,UAAU,CAAC,CACrCC,QAAQ,CAAC;EACLI,OAAO,EAAE,CACLH,YAAY,CACf;EACDI,YAAY,EAAE,CACVH,8BAA8B,CACjC;EACDI,OAAO,EAAE,CACLJ,8BAA8B;AAEtC,CAAC,CAAC,CACL,EAAEC,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}