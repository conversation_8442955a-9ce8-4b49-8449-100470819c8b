{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatFormFieldModule;\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { j as <PERSON><PERSON><PERSON><PERSON>ield, M as <PERSON><PERSON><PERSON><PERSON>, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, e as <PERSON><PERSON>refix, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-DqPi4knt.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nclass MatFormFieldModule {}\n_MatFormFieldModule = MatFormFieldModule;\n_defineProperty(MatFormFieldModule, \"\\u0275fac\", function _MatFormFieldModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatFormFieldModule)();\n});\n_defineProperty(MatFormFieldModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatFormFieldModule,\n  imports: [MatCommonModule, ObserversModule, MatFormField, MatLabel, MatError, MatHint, MatPrefix, MatSuffix],\n  exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule]\n}));\n_defineProperty(MatFormFieldModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, ObserversModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, ObserversModule, MatFormField, MatLabel, MatError, MatHint, MatPrefix, MatSuffix],\n      exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatFormFieldModule as M };\n//# sourceMappingURL=module-BXZhw7pQ.mjs.map", "map": {"version": 3, "names": ["ObserversModule", "i0", "NgModule", "j", "MatFormField", "M", "<PERSON><PERSON><PERSON><PERSON>", "b", "<PERSON><PERSON><PERSON><PERSON>", "c", "MatHint", "e", "MatPrefix", "g", "MatSuffix", "MatCommonModule", "MatFormFieldModule", "_MatFormFieldModule", "_defineProperty", "_MatFormFieldModule_Factory", "__ngFactoryType__", "ɵɵdefineNgModule", "type", "imports", "exports", "ɵɵdefineInjector", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/module-BXZhw7pQ.mjs"], "sourcesContent": ["import { ObserversModule } from '@angular/cdk/observers';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { j as <PERSON><PERSON><PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON><PERSON>, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-DqPi4knt.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\n\nclass MatFormFieldModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldModule, imports: [MatCommonModule,\n            ObserversModule,\n            MatFormField,\n            Mat<PERSON><PERSON><PERSON>,\n            <PERSON><PERSON><PERSON>r,\n            <PERSON><PERSON><PERSON>,\n            MatPrefix,\n            MatSuffix], exports: [<PERSON><PERSON>orm<PERSON>ield, MatLabel, <PERSON><PERSON>int, <PERSON><PERSON><PERSON><PERSON>, Mat<PERSON>refix, MatSuffix, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldModule, imports: [MatCommonModule,\n            ObserversModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFormFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        ObserversModule,\n                        MatFormField,\n                        MatLabel,\n                        MatError,\n                        MatHint,\n                        MatPrefix,\n                        MatSuffix,\n                    ],\n                    exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule],\n                }]\n        }] });\n\nexport { MatFormFieldModule as M };\n//# sourceMappingURL=module-BXZhw7pQ.mjs.map\n"], "mappings": ";;AAAA,SAASA,eAAe,QAAQ,wBAAwB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,SAAS,QAAQ,2BAA2B;AACzI,SAAST,CAAC,IAAIU,eAAe,QAAQ,8BAA8B;AAEnE,MAAMC,kBAAkB,CAAC;AAYxBC,mBAAA,GAZKD,kBAAkB;AAAAE,eAAA,CAAlBF,kBAAkB,wBAAAG,4BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAC+EJ,mBAAkB;AAAA;AAAAE,eAAA,CADnHF,kBAAkB,8BAayDf,EAAE,CAAAoB,gBAAA;EAAAC,IAAA,EAXqBN,mBAAkB;EAAAO,OAAA,GAAYR,eAAe,EACzIf,eAAe,EACfI,YAAY,EACZE,QAAQ,EACRE,QAAQ,EACRE,OAAO,EACPE,SAAS,EACTE,SAAS;EAAAU,OAAA,GAAapB,YAAY,EAAEE,QAAQ,EAAEI,OAAO,EAAEF,QAAQ,EAAEI,SAAS,EAAEE,SAAS,EAAEC,eAAe;AAAA;AAAAG,eAAA,CAT5GF,kBAAkB,8BAayDf,EAAE,CAAAwB,gBAAA;EAAAF,OAAA,GAHmDR,eAAe,EACzIf,eAAe,EAAEe,eAAe;AAAA;AAE5C;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KAAiFzB,EAAE,CAAA0B,iBAAA,CAAQX,kBAAkB,EAAc,CAAC;IAChHM,IAAI,EAAEpB,QAAQ;IACd0B,IAAI,EAAE,CAAC;MACCL,OAAO,EAAE,CACLR,eAAe,EACff,eAAe,EACfI,YAAY,EACZE,QAAQ,EACRE,QAAQ,EACRE,OAAO,EACPE,SAAS,EACTE,SAAS,CACZ;MACDU,OAAO,EAAE,CAACpB,YAAY,EAAEE,QAAQ,EAAEI,OAAO,EAAEF,QAAQ,EAAEI,SAAS,EAAEE,SAAS,EAAEC,eAAe;IAC9F,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASC,kBAAkB,IAAIX,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}