{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _Dir, _BidiModule;\nimport { _ as _resolveDirectionality, D as Directionality } from './directionality-CBXD4hga.mjs';\nexport { a as DIR_DOCUMENT } from './directionality-CBXD4hga.mjs';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport '@angular/common';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nclass Dir {\n  constructor() {\n    /** Normalized direction that accounts for invalid/unsupported values. */\n    _defineProperty(this, \"_dir\", 'ltr');\n    /** Whether the `value` has been set to its initial value. */\n    _defineProperty(this, \"_isInitialized\", false);\n    /** Direction as passed in by the consumer. */\n    _defineProperty(this, \"_rawDir\", void 0);\n    /** Event emitted when the direction changes. */\n    _defineProperty(this, \"change\", new EventEmitter());\n  }\n  /** @docs-private */\n  get dir() {\n    return this._dir;\n  }\n  set dir(value) {\n    const previousValue = this._dir;\n    // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n    // whereas the browser does it based on the content of the element. Since doing so based\n    // on the content can be expensive, for now we're doing the simpler matching.\n    this._dir = _resolveDirectionality(value);\n    this._rawDir = value;\n    if (previousValue !== this._dir && this._isInitialized) {\n      this.change.emit(this._dir);\n    }\n  }\n  /** Current layout direction of the element. */\n  get value() {\n    return this.dir;\n  }\n  /** Initialize once default value has been set. */\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    this.change.complete();\n  }\n}\n_Dir = Dir;\n_defineProperty(Dir, \"\\u0275fac\", function _Dir_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Dir)();\n});\n_defineProperty(Dir, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _Dir,\n  selectors: [[\"\", \"dir\", \"\"]],\n  hostVars: 1,\n  hostBindings: function _Dir_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"dir\", ctx._rawDir);\n    }\n  },\n  inputs: {\n    dir: \"dir\"\n  },\n  outputs: {\n    change: \"dirChange\"\n  },\n  exportAs: [\"dir\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: Directionality,\n    useExisting: _Dir\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dir, [{\n    type: Directive,\n    args: [{\n      selector: '[dir]',\n      providers: [{\n        provide: Directionality,\n        useExisting: Dir\n      }],\n      host: {\n        '[attr.dir]': '_rawDir'\n      },\n      exportAs: 'dir'\n    }]\n  }], null, {\n    change: [{\n      type: Output,\n      args: ['dirChange']\n    }],\n    dir: [{\n      type: Input\n    }]\n  });\n})();\nclass BidiModule {}\n_BidiModule = BidiModule;\n_defineProperty(BidiModule, \"\\u0275fac\", function _BidiModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BidiModule)();\n});\n_defineProperty(BidiModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _BidiModule,\n  imports: [Dir],\n  exports: [Dir]\n}));\n_defineProperty(BidiModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BidiModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Dir],\n      exports: [Dir]\n    }]\n  }], null, null);\n})();\nexport { BidiModule, Dir, Directionality };", "map": {"version": 3, "names": ["_", "_resolveDirectionality", "D", "Directionality", "a", "DIR_DOCUMENT", "i0", "EventEmitter", "Directive", "Output", "Input", "NgModule", "<PERSON><PERSON>", "constructor", "_defineProperty", "dir", "_dir", "value", "previousValue", "_rawDir", "_isInitialized", "change", "emit", "ngAfterContentInit", "ngOnDestroy", "complete", "_Dir", "_Dir_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "_Dir_HostB<PERSON>ings", "rf", "ctx", "ɵɵattribute", "inputs", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "host", "BidiModule", "_BidiModule", "_BidiModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/bidi.mjs"], "sourcesContent": ["import { _ as _resolveDirectionality, D as Directionality } from './directionality-CBXD4hga.mjs';\nexport { a as DIR_DOCUMENT } from './directionality-CBXD4hga.mjs';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport '@angular/common';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nclass Dir {\n    /** Normalized direction that accounts for invalid/unsupported values. */\n    _dir = 'ltr';\n    /** Whether the `value` has been set to its initial value. */\n    _isInitialized = false;\n    /** Direction as passed in by the consumer. */\n    _rawDir;\n    /** Event emitted when the direction changes. */\n    change = new EventEmitter();\n    /** @docs-private */\n    get dir() {\n        return this._dir;\n    }\n    set dir(value) {\n        const previousValue = this._dir;\n        // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n        // whereas the browser does it based on the content of the element. Since doing so based\n        // on the content can be expensive, for now we're doing the simpler matching.\n        this._dir = _resolveDirectionality(value);\n        this._rawDir = value;\n        if (previousValue !== this._dir && this._isInitialized) {\n            this.change.emit(this._dir);\n        }\n    }\n    /** Current layout direction of the element. */\n    get value() {\n        return this.dir;\n    }\n    /** Initialize once default value has been set. */\n    ngAfterContentInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        this.change.complete();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Dir, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: Dir, isStandalone: true, selector: \"[dir]\", inputs: { dir: \"dir\" }, outputs: { change: \"dirChange\" }, host: { properties: { \"attr.dir\": \"_rawDir\" } }, providers: [{ provide: Directionality, useExisting: Dir }], exportAs: [\"dir\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Dir, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[dir]',\n                    providers: [{ provide: Directionality, useExisting: Dir }],\n                    host: { '[attr.dir]': '_rawDir' },\n                    exportAs: 'dir',\n                }]\n        }], propDecorators: { change: [{\n                type: Output,\n                args: ['dirChange']\n            }], dir: [{\n                type: Input\n            }] } });\n\nclass BidiModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BidiModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: BidiModule, imports: [Dir], exports: [Dir] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BidiModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BidiModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [Dir],\n                    exports: [Dir],\n                }]\n        }] });\n\nexport { BidiModule, Dir, Directionality };\n"], "mappings": ";;AAAA,SAASA,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AAChG,SAASC,CAAC,IAAIC,YAAY,QAAQ,+BAA+B;AACjE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAChF,OAAO,iBAAiB;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,GAAG,CAAC;EAAAC,YAAA;IACN;IAAAC,eAAA,eACO,KAAK;IACZ;IAAAA,eAAA,yBACiB,KAAK;IACtB;IAAAA,eAAA;IAEA;IAAAA,eAAA,iBACS,IAAIP,YAAY,CAAC,CAAC;EAAA;EAC3B;EACA,IAAIQ,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACC,IAAI;EACpB;EACA,IAAID,GAAGA,CAACE,KAAK,EAAE;IACX,MAAMC,aAAa,GAAG,IAAI,CAACF,IAAI;IAC/B;IACA;IACA;IACA,IAAI,CAACA,IAAI,GAAGf,sBAAsB,CAACgB,KAAK,CAAC;IACzC,IAAI,CAACE,OAAO,GAAGF,KAAK;IACpB,IAAIC,aAAa,KAAK,IAAI,CAACF,IAAI,IAAI,IAAI,CAACI,cAAc,EAAE;MACpD,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACN,IAAI,CAAC;IAC/B;EACJ;EACA;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACF,GAAG;EACnB;EACA;EACAQ,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACH,cAAc,GAAG,IAAI;EAC9B;EACAI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,MAAM,CAACI,QAAQ,CAAC,CAAC;EAC1B;AAGJ;AAACC,IAAA,GArCKd,GAAG;AAAAE,eAAA,CAAHF,GAAG,wBAAAe,aAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAmC8FhB,IAAG;AAAA;AAAAE,eAAA,CAnCpGF,GAAG,8BAsCwEN,EAAE,CAAAuB,iBAAA;EAAAC,IAAA,EAFQlB,IAAG;EAAAmB,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAEb7B,EAAE,CAAA+B,WAAA,QAAAD,GAAA,CAAAjB,OAAA;IAAA;EAAA;EAAAmB,MAAA;IAAAvB,GAAA;EAAA;EAAAwB,OAAA;IAAAlB,MAAA;EAAA;EAAAmB,QAAA;EAAAC,QAAA,GAAFnC,EAAE,CAAAoC,kBAAA,CAF0K,CAAC;IAAEC,OAAO,EAAExC,cAAc;IAAEyC,WAAW,EAAEhC;EAAI,CAAC,CAAC;AAAA;AAE5S;EAAA,QAAAiC,SAAA,oBAAAA,SAAA,KAAiFvC,EAAE,CAAAwC,iBAAA,CAAQlC,GAAG,EAAc,CAAC;IACjGkB,IAAI,EAAEtB,SAAS;IACfuC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAExC,cAAc;QAAEyC,WAAW,EAAEhC;MAAI,CAAC,CAAC;MAC1DsC,IAAI,EAAE;QAAE,YAAY,EAAE;MAAU,CAAC;MACjCV,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEnB,MAAM,EAAE,CAAC;MACvBS,IAAI,EAAErB,MAAM;MACZsC,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEhC,GAAG,EAAE,CAAC;MACNe,IAAI,EAAEpB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyC,UAAU,CAAC;AAIhBC,WAAA,GAJKD,UAAU;AAAArC,eAAA,CAAVqC,UAAU,wBAAAE,oBAAAzB,iBAAA;EAAA,YAAAA,iBAAA,IACuFuB,WAAU;AAAA;AAAArC,eAAA,CAD3GqC,UAAU,8BAfiE7C,EAAE,CAAAgD,gBAAA;EAAAxB,IAAA,EAiBqBqB,WAAU;EAAAI,OAAA,GAAY3C,GAAG;EAAA4C,OAAA,GAAa5C,GAAG;AAAA;AAAAE,eAAA,CAF3IqC,UAAU,8BAfiE7C,EAAE,CAAAmD,gBAAA;AAoBnF;EAAA,QAAAZ,SAAA,oBAAAA,SAAA,KApBiFvC,EAAE,CAAAwC,iBAAA,CAoBQK,UAAU,EAAc,CAAC;IACxGrB,IAAI,EAAEnB,QAAQ;IACdoC,IAAI,EAAE,CAAC;MACCQ,OAAO,EAAE,CAAC3C,GAAG,CAAC;MACd4C,OAAO,EAAE,CAAC5C,GAAG;IACjB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASuC,UAAU,EAAEvC,GAAG,EAAET,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}