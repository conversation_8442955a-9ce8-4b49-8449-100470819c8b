{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nexport var intervalProvider = {\n  setInterval: function (handler, timeout) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n      args[_i - 2] = arguments[_i];\n    }\n    var delegate = intervalProvider.delegate;\n    if (delegate === null || delegate === void 0 ? void 0 : delegate.setInterval) {\n      return delegate.setInterval.apply(delegate, __spreadArray([handler, timeout], __read(args)));\n    }\n    return setInterval.apply(void 0, __spreadArray([handler, timeout], __read(args)));\n  },\n  clearInterval: function (handle) {\n    var delegate = intervalProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearInterval) || clearInterval)(handle);\n  },\n  delegate: undefined\n};\n//# sourceMappingURL=intervalProvider.js.map", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "intervalProvider", "setInterval", "handler", "timeout", "args", "_i", "arguments", "length", "delegate", "apply", "clearInterval", "handle", "undefined"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/intervalProvider.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nexport var intervalProvider = {\n    setInterval: function (handler, timeout) {\n        var args = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            args[_i - 2] = arguments[_i];\n        }\n        var delegate = intervalProvider.delegate;\n        if (delegate === null || delegate === void 0 ? void 0 : delegate.setInterval) {\n            return delegate.setInterval.apply(delegate, __spreadArray([handler, timeout], __read(args)));\n        }\n        return setInterval.apply(void 0, __spreadArray([handler, timeout], __read(args)));\n    },\n    clearInterval: function (handle) {\n        var delegate = intervalProvider.delegate;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearInterval) || clearInterval)(handle);\n    },\n    delegate: undefined,\n};\n//# sourceMappingURL=intervalProvider.js.map"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,OAAO,IAAIC,gBAAgB,GAAG;EAC1BC,WAAW,EAAE,SAAAA,CAAUC,OAAO,EAAEC,OAAO,EAAE;IACrC,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAChC;IACA,IAAIG,QAAQ,GAAGR,gBAAgB,CAACQ,QAAQ;IACxC,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACP,WAAW,EAAE;MAC1E,OAAOO,QAAQ,CAACP,WAAW,CAACQ,KAAK,CAACD,QAAQ,EAAET,aAAa,CAAC,CAACG,OAAO,EAAEC,OAAO,CAAC,EAAEL,MAAM,CAACM,IAAI,CAAC,CAAC,CAAC;IAChG;IACA,OAAOH,WAAW,CAACQ,KAAK,CAAC,KAAK,CAAC,EAAEV,aAAa,CAAC,CAACG,OAAO,EAAEC,OAAO,CAAC,EAAEL,MAAM,CAACM,IAAI,CAAC,CAAC,CAAC;EACrF,CAAC;EACDM,aAAa,EAAE,SAAAA,CAAUC,MAAM,EAAE;IAC7B,IAAIH,QAAQ,GAAGR,gBAAgB,CAACQ,QAAQ;IACxC,OAAO,CAAC,CAACA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,aAAa,KAAKA,aAAa,EAAEC,MAAM,CAAC;EAClH,CAAC;EACDH,QAAQ,EAAEI;AACd,CAAC;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}