{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { TestGridComponent } from './test-grid.component';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { SwuiGridModule } from '../../swui-grid.module';\nlet TestGridModule = class TestGridModule {};\nTestGridModule = __decorate([NgModule({\n  imports: [MatCardModule, SwuiGridModule.forRoot(), MatButtonModule],\n  exports: [TestGridComponent],\n  declarations: [TestGridComponent],\n  providers: []\n})], TestGridModule);\nexport { TestGridModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "TestGridComponent", "MatButtonModule", "MatCardModule", "SwuiGridModule", "TestGridModule", "imports", "forRoot", "exports", "declarations", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/_stories/test-grid/test-grid.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { TestGridComponent } from './test-grid.component';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { SwuiGridModule } from '../../swui-grid.module';\nlet TestGridModule = class TestGridModule {\n};\nTestGridModule = __decorate([\n    NgModule({\n        imports: [\n            MatCardModule,\n            SwuiGridModule.forRoot(),\n            MatButtonModule,\n        ],\n        exports: [TestGridComponent],\n        declarations: [TestGridComponent],\n        providers: [],\n    })\n], TestGridModule);\nexport { TestGridModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,IAAIC,cAAc,GAAG,MAAMA,cAAc,CAAC,EACzC;AACDA,cAAc,GAAGN,UAAU,CAAC,CACxBC,QAAQ,CAAC;EACLM,OAAO,EAAE,CACLH,aAAa,EACbC,cAAc,CAACG,OAAO,CAAC,CAAC,EACxBL,eAAe,CAClB;EACDM,OAAO,EAAE,CAACP,iBAAiB,CAAC;EAC5BQ,YAAY,EAAE,CAACR,iBAAiB,CAAC;EACjCS,SAAS,EAAE;AACf,CAAC,CAAC,CACL,EAAEL,cAAc,CAAC;AAClB,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}