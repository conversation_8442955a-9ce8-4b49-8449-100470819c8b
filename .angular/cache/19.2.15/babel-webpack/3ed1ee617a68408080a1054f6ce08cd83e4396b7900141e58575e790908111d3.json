{"ast": null, "code": "export const TYPES = {\n  BRIDGE_LOADED: 'bridge_loaded',\n  LOGIN: 'login',\n  LOGOUT: 'logout',\n  HUB_LOADED: 'hub_loaded',\n  OPEN_HUB: 'open_hub',\n  TOKEN: 'token',\n  TOKEN_EXPIRED: 'token_expired',\n  LOCALE_CHANGED: 'locale_changed',\n  APP_SETTINGS_CHANGED: 'app_settings_changed',\n  ENTITY_ID_CHANGED: 'entity_id_changed',\n  SIDEBAR_TOGGLE: 'sidebar_toggle',\n  SIDEBAR_COLLAPSED: 'sidebar_collapsed',\n  LOCATION_CHANGED: 'location_changed',\n  // from child to base\n  USER_ACTIVITY: 'user_activity',\n  PAGE_TITLE: 'page_title',\n  UNKNOWN_LOCATION: 'unknown_location'\n};\nexport function isHubMessage(event) {\n  return typeof event.data === 'object' && 'target' in event.data && 'initiator' in event.data && 'type' in event.data && 'body' in event.data;\n}", "map": {"version": 3, "names": ["TYPES", "BRIDGE_LOADED", "LOGIN", "LOGOUT", "HUB_LOADED", "OPEN_HUB", "TOKEN", "TOKEN_EXPIRED", "LOCALE_CHANGED", "APP_SETTINGS_CHANGED", "ENTITY_ID_CHANGED", "SIDEBAR_TOGGLE", "SIDEBAR_COLLAPSED", "LOCATION_CHANGED", "USER_ACTIVITY", "PAGE_TITLE", "UNKNOWN_LOCATION", "isHubMessage", "event", "data"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-init/sw-hub-init.model.ts"], "sourcesContent": ["export const TYPES = {\n    BRIDGE_LOADED: 'bridge_loaded',\n    LOGIN: 'login',\n    LOGOUT: 'logout',\n    HUB_LOADED: 'hub_loaded',\n    OPEN_HUB: 'open_hub',\n    TOKEN: 'token',\n    TOKEN_EXPIRED: 'token_expired',\n    LOCALE_CHANGED: 'locale_changed',\n    APP_SETTINGS_CHANGED: 'app_settings_changed',\n    ENTITY_ID_CHANGED: 'entity_id_changed',\n    SIDEBAR_TOGGLE: 'sidebar_toggle',\n    SIDEBAR_COLLAPSED: 'sidebar_collapsed',\n    LOCATION_CHANGED: 'location_changed', // from child to base\n    USER_ACTIVITY: 'user_activity',\n    PAGE_TITLE: 'page_title',\n    UNKNOWN_LOCATION: 'unknown_location'\n};\nexport function isHubMessage(event) {\n    return typeof event.data === 'object' &&\n        'target' in event.data &&\n        'initiator' in event.data &&\n        'type' in event.data &&\n        'body' in event.data;\n}\n"], "mappings": "AAAA,OAAO,MAAMA,KAAK,GAAG;EACjBC,aAAa,EAAE,eAAe;EAC9BC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,aAAa,EAAE,eAAe;EAC9BC,cAAc,EAAE,gBAAgB;EAChCC,oBAAoB,EAAE,sBAAsB;EAC5CC,iBAAiB,EAAE,mBAAmB;EACtCC,cAAc,EAAE,gBAAgB;EAChCC,iBAAiB,EAAE,mBAAmB;EACtCC,gBAAgB,EAAE,kBAAkB;EAAE;EACtCC,aAAa,EAAE,eAAe;EAC9BC,UAAU,EAAE,YAAY;EACxBC,gBAAgB,EAAE;AACtB,CAAC;AACD,OAAO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAChC,OAAO,OAAOA,KAAK,CAACC,IAAI,KAAK,QAAQ,IACjC,QAAQ,IAAID,KAAK,CAACC,IAAI,IACtB,WAAW,IAAID,KAAK,CAACC,IAAI,IACzB,MAAM,IAAID,KAAK,CAACC,IAAI,IACpB,MAAM,IAAID,KAAK,CAACC,IAAI;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}