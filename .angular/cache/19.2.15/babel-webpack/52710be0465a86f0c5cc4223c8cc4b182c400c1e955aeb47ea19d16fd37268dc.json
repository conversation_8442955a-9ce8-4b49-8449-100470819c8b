{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./user.widget.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { SwuiTdStringWidgetComponent } from '../string/string.widget';\nlet SwuiTdUserWidgetComponent = class SwuiTdUserWidgetComponent extends SwuiTdStringWidgetComponent {};\nSwuiTdUserWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-user-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdUserWidgetComponent);\nexport { SwuiTdUserWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "SwuiTdStringWidgetComponent", "SwuiTdUserWidgetComponent", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/user/user.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./user.widget.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { SwuiTdStringWidgetComponent } from '../string/string.widget';\nlet SwuiTdUserWidgetComponent = class SwuiTdUserWidgetComponent extends SwuiTdStringWidgetComponent {\n};\nSwuiTdUserWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-user-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdUserWidgetComponent);\nexport { SwuiTdUserWidgetComponent };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,2BAA2B,QAAQ,yBAAyB;AACrE,IAAIC,yBAAyB,GAAG,MAAMA,yBAAyB,SAASD,2BAA2B,CAAC,EACnG;AACDC,yBAAyB,GAAGJ,UAAU,CAAC,CACnCE,SAAS,CAAC;EACNG,QAAQ,EAAE,yBAAyB;EACnCC,QAAQ,EAAEL,oBAAoB;EAC9BM,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEH,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}