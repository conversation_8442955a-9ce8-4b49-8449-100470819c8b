{"ast": null, "code": "var _SwuiFooterTotalWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./total.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { map, takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiFooterTotalWidgetComponent = (_SwuiFooterTotalWidgetComponent = class SwuiFooterTotalWidgetComponent {\n  constructor({\n    field,\n    dataSource,\n    schema: {\n      footer\n    }\n  }) {\n    this.value = 0;\n    this.destroyed$ = new Subject();\n    this.format = footer === null || footer === void 0 ? void 0 : footer.format;\n    this.prefix = footer === null || footer === void 0 ? void 0 : footer.prefix;\n    this.delimiter = footer === null || footer === void 0 ? void 0 : footer.delimiter;\n    this.fractionCount = footer === null || footer === void 0 ? void 0 : footer.fractionCount;\n    if (dataSource) {\n      this.connectToDataSource(field, dataSource);\n    }\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  connectToDataSource(field, dataSource) {\n    dataSource.connect().pipe(map(data => {\n      return data.reduce((total, row) => {\n        if (field in row) {\n          total += row[field];\n        }\n        return total;\n      }, 0);\n    }), takeUntil(this.destroyed$)).subscribe(total => {\n      this.value = total;\n    });\n  }\n}, _SwuiFooterTotalWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiFooterTotalWidgetComponent);\nSwuiFooterTotalWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-footer-total-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiFooterTotalWidgetComponent);\nexport { SwuiFooterTotalWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "map", "takeUntil", "Subject", "SWUI_GRID_WIDGET_CONFIG", "SwuiFooterTotalWidgetComponent", "_SwuiFooterTotalWidgetComponent", "constructor", "field", "dataSource", "schema", "footer", "value", "destroyed$", "format", "prefix", "delimiter", "fractionCount", "connectToDataSource", "ngOnDestroy", "next", "undefined", "complete", "connect", "pipe", "data", "reduce", "total", "row", "subscribe", "ctorParameters", "type", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/footer-widget/total/total.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./total.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { map, takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiFooterTotalWidgetComponent = class SwuiFooterTotalWidgetComponent {\n    constructor({ field, dataSource, schema: { footer } }) {\n        this.value = 0;\n        this.destroyed$ = new Subject();\n        this.format = footer?.format;\n        this.prefix = footer?.prefix;\n        this.delimiter = footer?.delimiter;\n        this.fractionCount = footer?.fractionCount;\n        if (dataSource) {\n            this.connectToDataSource(field, dataSource);\n        }\n    }\n    ngOnDestroy() {\n        this.destroyed$.next(undefined);\n        this.destroyed$.complete();\n    }\n    connectToDataSource(field, dataSource) {\n        dataSource.connect().pipe(map((data) => {\n            return data.reduce((total, row) => {\n                if (field in row) {\n                    total += row[field];\n                }\n                return total;\n            }, 0);\n        }), takeUntil(this.destroyed$)).subscribe(total => {\n            this.value = total;\n        });\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiFooterTotalWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-footer-total-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiFooterTotalWidgetComponent);\nexport { SwuiFooterTotalWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAC/C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,8BAA8B,IAAAC,+BAAA,GAAG,MAAMD,8BAA8B,CAAC;EACtEE,WAAWA,CAAC;IAAEC,KAAK;IAAEC,UAAU;IAAEC,MAAM,EAAE;MAAEC;IAAO;EAAE,CAAC,EAAE;IACnD,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,UAAU,GAAG,IAAIV,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACW,MAAM,GAAGH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,MAAM;IAC5B,IAAI,CAACC,MAAM,GAAGJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,MAAM;IAC5B,IAAI,CAACC,SAAS,GAAGL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,SAAS;IAClC,IAAI,CAACC,aAAa,GAAGN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEM,aAAa;IAC1C,IAAIR,UAAU,EAAE;MACZ,IAAI,CAACS,mBAAmB,CAACV,KAAK,EAAEC,UAAU,CAAC;IAC/C;EACJ;EACAU,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,UAAU,CAACO,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACR,UAAU,CAACS,QAAQ,CAAC,CAAC;EAC9B;EACAJ,mBAAmBA,CAACV,KAAK,EAAEC,UAAU,EAAE;IACnCA,UAAU,CAACc,OAAO,CAAC,CAAC,CAACC,IAAI,CAACvB,GAAG,CAAEwB,IAAI,IAAK;MACpC,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC/B,IAAIpB,KAAK,IAAIoB,GAAG,EAAE;UACdD,KAAK,IAAIC,GAAG,CAACpB,KAAK,CAAC;QACvB;QACA,OAAOmB,KAAK;MAChB,CAAC,EAAE,CAAC,CAAC;IACT,CAAC,CAAC,EAAEzB,SAAS,CAAC,IAAI,CAACW,UAAU,CAAC,CAAC,CAACgB,SAAS,CAACF,KAAK,IAAI;MAC/C,IAAI,CAACf,KAAK,GAAGe,KAAK;IACtB,CAAC,CAAC;EACN;AAIJ,CAAC,EAHYrB,+BAAA,CAAKwB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEV,SAAS;EAAEW,UAAU,EAAE,CAAC;IAAED,IAAI,EAAE/B,MAAM;IAAEiC,IAAI,EAAE,CAAC7B,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,+BAAA,CACJ;AACDD,8BAA8B,GAAGR,UAAU,CAAC,CACxCE,SAAS,CAAC;EACNmC,QAAQ,EAAE,8BAA8B;EACxCC,QAAQ,EAAErC,oBAAoB;EAC9BsC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAE/B,8BAA8B,CAAC;AAClC,SAASA,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}