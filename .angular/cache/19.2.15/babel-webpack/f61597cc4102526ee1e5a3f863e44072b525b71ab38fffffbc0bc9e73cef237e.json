{"ast": null, "code": "var _SwuiSelectTableComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-select-table.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-select-table.component.scss?ngResource\";\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, HostBinding, Input, ViewChild } from '@angular/core';\nimport { UntypedFormControl, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { filter, map, take, takeUntil } from 'rxjs/operators';\nconst CONTROL_NAME = 'lib-swui-select-table';\nlet nextUniqueId = 0;\nlet SwuiSelectTableComponent = (_SwuiSelectTableComponent = class SwuiSelectTableComponent {\n  set data(val) {\n    var _this$options;\n    this._data = val || [];\n    this.options = this._data;\n    if (!this.disableAllOption) {\n      var _this$value;\n      this.allChecked = ((_this$value = this.value) === null || _this$value === void 0 ? void 0 : _this$value.length) === this.enabledData.length;\n    }\n    (_this$options = this.options) === null || _this$options === void 0 || _this$options.forEach(option => {\n      var _this$value2;\n      return option.state = {\n        checked: (_this$value2 = this.value) === null || _this$value2 === void 0 ? void 0 : _this$value2.includes(option.id)\n      };\n    });\n    if (Array.isArray(this.options) && this.options.length) {\n      this.onDataReceived.next(this.options);\n    }\n  }\n  get data() {\n    return this._data;\n  }\n  set disabled(disabled) {\n    this.setDisabledState(disabled);\n  }\n  get disabled() {\n    return this._disabled;\n  }\n  set value(val) {\n    this.patchSelectControl(val);\n  }\n  get value() {\n    var _this$selectControl$v;\n    return (_this$selectControl$v = this.selectControl.value) === null || _this$selectControl$v === void 0 ? void 0 : _this$selectControl$v.map(v => v.id);\n  }\n  get empty() {\n    var _this$selectControl$v2;\n    return !((_this$selectControl$v2 = this.selectControl.value) !== null && _this$selectControl$v2 !== void 0 && _this$selectControl$v2.length);\n  }\n  get viewportHeight() {\n    let length = this.data.length;\n    length = Math.floor(length / this.rowsNumber) && this.rowsNumber || length % this.rowsNumber;\n    return length * this.itemHeight;\n  }\n  constructor(cd) {\n    this.cd = cd;\n    this.searchPlaceholder = 'Search';\n    this.showSearch = false;\n    this.startSearchLength = 0;\n    this.disableEmptyOption = false;\n    this.disableAllOption = false;\n    this.columns = [];\n    this.rowsNumber = 3;\n    this.loading = false;\n    this.allChecked = false;\n    this.viewSelected = false;\n    this.options = [];\n    this.itemHeight = 48;\n    this.onDataReceived = new ReplaySubject(1);\n    this.controlType = CONTROL_NAME;\n    this.searchControl = new UntypedFormControl('');\n    this.selectControl = new UntypedFormControl();\n    this.destroyed$ = new Subject();\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._data = [];\n    this._disabled = false;\n    this.previousSelected = [];\n    this.onChange = _ => {};\n  }\n  ngOnInit() {\n    this.searchControl.valueChanges.pipe(filter(data => this.showSearch && ((data === null || data === void 0 ? void 0 : data.length) >= (this.startSearchLength || 0) || (data === null || data === void 0 ? void 0 : data.length) === 0)), map(searchString => searchString.toLowerCase()), takeUntil(this.destroyed$)).subscribe(search => {\n      this.previousSelected = search ? this.selectControl.value : [];\n      this.options = this.data.filter(option => {\n        for (const column of this.columns) {\n          if (option[column.field] && option[column.field].toLowerCase().indexOf(search) > -1) {\n            return true;\n          }\n        }\n        return false;\n      });\n      this.cd.markForCheck();\n    });\n    this.selectControl.valueChanges.pipe(map(val => {\n      const previousSelected = this.previousSelected.filter(id => !this.options.some(option => option.id === id));\n      const values = this.enabledData.filter(({\n        id\n      }) => previousSelected.some(item => item === id));\n      return [...values, ...val];\n    }), map(val => val.map(item => item ? item.id : '')), takeUntil(this.destroyed$)).subscribe(val => {\n      this.onChange(val || null);\n      this.cd.detectChanges();\n    });\n  }\n  writeValue(val) {\n    this.patchSelectControl(val);\n  }\n  patchSelectControl(val) {\n    this.onDataReceived.pipe(take(1)).subscribe(options => {\n      if (!this.disableAllOption) {\n        this.allChecked = Array.isArray(val) && val.length === this.enabledData.length;\n      }\n      const values = coerceArray(val || []);\n      options === null || options === void 0 || options.forEach(option => option.state = {\n        checked: values === null || values === void 0 ? void 0 : values.includes(option.id)\n      });\n      const value = options.filter(opt => values.includes(opt.id));\n      this.selectControl.patchValue(value, {\n        emitEvent: false\n      });\n      this.cd.detectChanges();\n    });\n  }\n  toggleAll(event) {\n    event === null || event === void 0 || event.preventDefault();\n    event === null || event === void 0 || event.stopPropagation();\n    this.allChecked = !this.allChecked;\n    this.options.forEach(option => {\n      if (!option.disabled) {\n        option.state.checked = this.allChecked;\n      }\n    });\n    let checkedOptions = this.options.filter(option => option.state.checked);\n    this.selectControl.setValue(checkedOptions);\n  }\n  showSelected() {\n    this.viewSelected = !this.viewSelected;\n    this.previousSelected = this.viewSelected ? this.selectControl.value : [];\n    this.options = this.viewSelected ? this.data.filter(option => {\n      return option.state.checked;\n    }) : this.data;\n    this.cd.markForCheck();\n  }\n  onSelectMultiple(event, option) {\n    var _this$data;\n    event.preventDefault();\n    event.stopPropagation();\n    if (option.state) {\n      option.state.checked = !option.state.checked;\n    }\n    this.selectControl.patchValue((_this$data = this.data) === null || _this$data === void 0 ? void 0 : _this$data.filter(item => {\n      var _item$state;\n      return (_item$state = item.state) === null || _item$state === void 0 ? void 0 : _item$state.checked;\n    }));\n  }\n  stopPropagation(event) {\n    event.stopPropagation();\n  }\n  get enabledData() {\n    return this.data.filter(({\n      disabled\n    }) => !disabled);\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched() {}\n  setDisabledState(isDisabled) {\n    this._disabled = isDisabled;\n    isDisabled ? this.searchControl.disable() : this.searchControl.enable();\n  }\n}, _SwuiSelectTableComponent.ctorParameters = () => [{\n  type: ChangeDetectorRef\n}], _SwuiSelectTableComponent.propDecorators = {\n  searchPlaceholder: [{\n    type: Input\n  }],\n  showSearch: [{\n    type: Input\n  }],\n  startSearchLength: [{\n    type: Input\n  }],\n  disableEmptyOption: [{\n    type: Input\n  }],\n  disableAllOption: [{\n    type: Input\n  }],\n  columns: [{\n    type: Input\n  }],\n  rowsNumber: [{\n    type: Input\n  }],\n  loading: [{\n    type: Input\n  }],\n  data: [{\n    type: Input\n  }],\n  disabled: [{\n    type: Input\n  }],\n  value: [{\n    type: Input\n  }],\n  trigger: [{\n    type: ViewChild,\n    args: ['trigger']\n  }],\n  searchRef: [{\n    type: ViewChild,\n    args: ['search']\n  }],\n  virtualScroll: [{\n    type: ViewChild,\n    args: [CdkVirtualScrollViewport, {\n      static: false\n    }]\n  }],\n  id: [{\n    type: HostBinding\n  }]\n}, _SwuiSelectTableComponent);\nSwuiSelectTableComponent = __decorate([Component({\n  selector: 'lib-swui-select-table',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SwuiSelectTableComponent),\n    multi: true\n  }],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSelectTableComponent);\nexport { SwuiSelectTableComponent };", "map": {"version": 3, "names": ["coerce<PERSON><PERSON><PERSON>", "CdkVirtualScrollViewport", "ChangeDetectionStrategy", "ChangeDetectorRef", "Component", "forwardRef", "HostBinding", "Input", "ViewChild", "UntypedFormControl", "NG_VALUE_ACCESSOR", "ReplaySubject", "Subject", "filter", "map", "take", "takeUntil", "CONTROL_NAME", "nextUniqueId", "SwuiSelectTableComponent", "_SwuiSelectTableComponent", "data", "val", "_this$options", "_data", "options", "disableAllOption", "_this$value", "allChecked", "value", "length", "enabledData", "for<PERSON>ach", "option", "_this$value2", "state", "checked", "includes", "id", "Array", "isArray", "onDataReceived", "next", "disabled", "setDisabledState", "_disabled", "patchSelectControl", "_this$selectControl$v", "selectControl", "v", "empty", "_this$selectControl$v2", "viewportHeight", "Math", "floor", "rowsNumber", "itemHeight", "constructor", "cd", "searchPlaceholder", "showSearch", "startSearchLength", "disableEmptyOption", "columns", "loading", "viewSelected", "controlType", "searchControl", "destroyed$", "previousSelected", "onChange", "_", "ngOnInit", "valueChanges", "pipe", "searchString", "toLowerCase", "subscribe", "search", "column", "field", "indexOf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "some", "values", "item", "detectChanges", "writeValue", "opt", "patchValue", "emitEvent", "toggleAll", "event", "preventDefault", "stopPropagation", "checkedOptions", "setValue", "showSelected", "onSelectMultiple", "_this$data", "_item$state", "registerOnChange", "fn", "registerOnTouched", "isDisabled", "disable", "enable", "args", "static", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "provide", "useExisting", "multi", "changeDetection", "OnPush", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-select-table/swui-select-table.component.ts"], "sourcesContent": ["import { coerceArray } from '@angular/cdk/coercion';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport {\n  ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, forwardRef, HostBinding, Input, OnInit, ViewChild\n} from '@angular/core';\nimport { ControlValueAccessor, UntypedFormControl, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { filter, map, take, takeUntil } from 'rxjs/operators';\nimport { SwuiSelectTableOption } from './swui-select-table.interface';\n\nconst CONTROL_NAME = 'lib-swui-select-table';\nlet nextUniqueId = 0;\n\n@Component({\n    selector: 'lib-swui-select-table',\n    templateUrl: './swui-select-table.component.html',\n    styleUrls: ['./swui-select-table.component.scss'],\n    providers: [\n        {\n            provide: NG_VALUE_ACCESSOR,\n            useExisting: forwardRef(() => SwuiSelectTableComponent),\n            multi: true\n        }\n    ],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    standalone: false\n})\nexport class SwuiSelectTableComponent implements ControlValueAccessor, OnInit {\n  @Input() searchPlaceholder = 'Search';\n  @Input() showSearch = false;\n  @Input() startSearchLength = 0;\n  @Input() disableEmptyOption = false;\n  @Input() disableAllOption = false;\n  @Input() columns: any[] = [];\n  @Input() rowsNumber = 3;\n  @Input() loading = false;\n\n  @Input()\n  set data( val: SwuiSelectTableOption[] ) {\n    this._data = val || [];\n    this.options = this._data;\n\n    if (!this.disableAllOption) {\n      this.allChecked = this.value?.length === this.enabledData.length;\n    }\n\n    this.options?.forEach(option => option.state = {\n      checked: this.value?.includes(option.id),\n    });\n\n    if (Array.isArray(this.options) && this.options.length) {\n      this.onDataReceived.next(this.options);\n    }\n  }\n\n  get data(): SwuiSelectTableOption[] {\n    return this._data;\n  }\n\n  @Input()\n  set disabled( disabled: boolean ) {\n    this.setDisabledState(disabled);\n  }\n\n  get disabled(): boolean {\n    return this._disabled;\n  }\n\n  @Input()\n  set value( val: string | string[] | undefined ) {\n    this.patchSelectControl(val);\n  }\n\n  get value(): string[] | undefined {\n    return this.selectControl.value?.map(( v: SwuiSelectTableOption ) => v.id);\n  }\n\n  get empty() {\n    return !this.selectControl.value?.length;\n  }\n\n  get viewportHeight(): number {\n    let length = this.data.length;\n    length = Math.floor(length / this.rowsNumber) && this.rowsNumber || length % this.rowsNumber;\n    return length * this.itemHeight;\n  }\n\n  allChecked = false;\n  viewSelected = false;\n  options: SwuiSelectTableOption[] = [];\n\n  itemHeight = 48;\n\n  onDataReceived = new ReplaySubject<SwuiSelectTableOption[]>(1);\n\n  readonly controlType = CONTROL_NAME;\n  readonly searchControl = new UntypedFormControl('');\n  readonly selectControl = new UntypedFormControl();\n  readonly destroyed$ = new Subject<void>();\n\n  @ViewChild('trigger') trigger?: MatMenuTrigger;\n  @ViewChild('search') searchRef?: ElementRef;\n  @ViewChild(CdkVirtualScrollViewport, { static: false }) virtualScroll?: CdkVirtualScrollViewport;\n\n  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;\n\n  private _data: SwuiSelectTableOption[] = [];\n  private _disabled = false;\n\n  private previousSelected: string[] = [];\n\n  constructor( private readonly cd: ChangeDetectorRef ) {\n  }\n\n  onChange = ( _: any ) => {\n  };\n\n  ngOnInit(): void {\n    this.searchControl.valueChanges.pipe(\n      filter<string>(data => this.showSearch && (data?.length >= (this.startSearchLength || 0) || data?.length === 0)),\n      map<string, string>(searchString => searchString.toLowerCase()),\n      takeUntil(this.destroyed$)\n    ).subscribe(search => {\n      this.previousSelected = search ? this.selectControl.value : [];\n      this.options = this.data.filter(option => {\n        for (const column of this.columns) {\n          if (option[column.field] && option[column.field].toLowerCase().indexOf(search) > -1) {\n            return true;\n          }\n        }\n        return false;\n      });\n      this.cd.markForCheck();\n    });\n\n    this.selectControl.valueChanges.pipe(\n      map<SwuiSelectTableOption[], SwuiSelectTableOption[]>(val => {\n        const previousSelected = this.previousSelected.filter(id => !this.options.some(option => option.id === id));\n        const values = this.enabledData.filter(( { id } ) => previousSelected.some(item => item === id));\n        return [...values, ...val];\n      }),\n      map<SwuiSelectTableOption[], string[]>(val => val.map(item => item ? item.id : '')),\n      takeUntil(this.destroyed$)\n    ).subscribe(val => {\n      this.onChange(val || null);\n      this.cd.detectChanges();\n    });\n  }\n\n  writeValue( val: string | string[] | undefined ): void {\n    this.patchSelectControl(val);\n  }\n\n  patchSelectControl( val: string | string[] | undefined ) {\n    this.onDataReceived\n      .pipe(\n        take(1),\n      )\n      .subscribe(\n        options => {\n          if (!this.disableAllOption) {\n            this.allChecked = Array.isArray(val) && val.length === this.enabledData.length;\n          }\n\n          const values = coerceArray(val || []);\n\n          options?.forEach(option => option.state = {\n            checked: values?.includes(option.id),\n          });\n\n          const value = options.filter(opt => values.includes(opt.id));\n\n          this.selectControl.patchValue(value, { emitEvent: false });\n\n          this.cd.detectChanges();\n        }\n      );\n  }\n\n  toggleAll( event?: MouseEvent ) {\n    event?.preventDefault();\n    event?.stopPropagation();\n\n    this.allChecked = !this.allChecked;\n    this.options.forEach(option => {\n      if (!option.disabled) {\n        option.state.checked = this.allChecked;\n      }\n    });\n\n    let checkedOptions = this.options.filter(option => option.state.checked);\n\n    this.selectControl.setValue(checkedOptions);\n  }\n\n  showSelected() {\n    this.viewSelected = !this.viewSelected;\n\n    this.previousSelected = this.viewSelected ? this.selectControl.value : [];\n    this.options = this.viewSelected ? this.data.filter(option => {\n      return option.state.checked;\n    }) : this.data;\n    this.cd.markForCheck();\n  }\n\n  onSelectMultiple( event: MouseEvent, option: SwuiSelectTableOption ) {\n    event.preventDefault();\n    event.stopPropagation();\n\n    if (option.state) {\n      option.state.checked = !option.state.checked;\n    }\n\n    this.selectControl.patchValue(this.data?.filter(item => item.state?.checked));\n  }\n\n  stopPropagation( event: Event ) {\n    event.stopPropagation();\n  }\n\n  private get enabledData(): SwuiSelectTableOption[] {\n    return this.data.filter(( { disabled } ) => !disabled);\n  }\n\n  registerOnChange( fn: any ): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(): void {\n  }\n\n  setDisabledState( isDisabled: boolean ): void {\n    this._disabled = isDisabled;\n    isDisabled ? this.searchControl.disable() : this.searchControl.enable();\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,WAAW,QAAQ,uBAAuB;AACnD,SAASC,wBAAwB,QAAQ,wBAAwB;AACjE,SACEC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAcC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAUC,SAAS,QAC/G,eAAe;AACtB,SAA+BC,kBAAkB,EAAEC,iBAAiB,QAAQ,gBAAgB;AAE5F,SAASC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC7C,SAASC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAG7D,MAAMC,YAAY,GAAG,uBAAuB;AAC5C,IAAIC,YAAY,GAAG,CAAC;AAgBb,IAAMC,wBAAwB,IAAAC,yBAAA,GAA9B,MAAMD,wBAAwB;MAW/BE,IAAIA,CAAEC,GAA4B;IAAA,IAAAC,aAAA;IACpC,IAAI,CAACC,KAAK,GAAGF,GAAG,IAAI,EAAE;IACtB,IAAI,CAACG,OAAO,GAAG,IAAI,CAACD,KAAK;IAEzB,IAAI,CAAC,IAAI,CAACE,gBAAgB,EAAE;MAAA,IAAAC,WAAA;MAC1B,IAAI,CAACC,UAAU,GAAG,EAAAD,WAAA,OAAI,CAACE,KAAK,cAAAF,WAAA,uBAAVA,WAAA,CAAYG,MAAM,MAAK,IAAI,CAACC,WAAW,CAACD,MAAM;IAClE;IAEA,CAAAP,aAAA,OAAI,CAACE,OAAO,cAAAF,aAAA,eAAZA,aAAA,CAAcS,OAAO,CAACC,MAAM;MAAA,IAAAC,YAAA;MAAA,OAAID,MAAM,CAACE,KAAK,GAAG;QAC7CC,OAAO,GAAAF,YAAA,GAAE,IAAI,CAACL,KAAK,cAAAK,YAAA,uBAAVA,YAAA,CAAYG,QAAQ,CAACJ,MAAM,CAACK,EAAE;OACxC;IAAA,EAAC;IAEF,IAAIC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACf,OAAO,CAAC,IAAI,IAAI,CAACA,OAAO,CAACK,MAAM,EAAE;MACtD,IAAI,CAACW,cAAc,CAACC,IAAI,CAAC,IAAI,CAACjB,OAAO,CAAC;IACxC;EACF;EAEA,IAAIJ,IAAIA,CAAA;IACN,OAAO,IAAI,CAACG,KAAK;EACnB;MAGImB,QAAQA,CAAEA,QAAiB;IAC7B,IAAI,CAACC,gBAAgB,CAACD,QAAQ,CAAC;EACjC;EAEA,IAAIA,QAAQA,CAAA;IACV,OAAO,IAAI,CAACE,SAAS;EACvB;MAGIhB,KAAKA,CAAEP,GAAkC;IAC3C,IAAI,CAACwB,kBAAkB,CAACxB,GAAG,CAAC;EAC9B;EAEA,IAAIO,KAAKA,CAAA;IAAA,IAAAkB,qBAAA;IACP,QAAAA,qBAAA,GAAO,IAAI,CAACC,aAAa,CAACnB,KAAK,cAAAkB,qBAAA,uBAAxBA,qBAAA,CAA0BjC,GAAG,CAAGmC,CAAwB,IAAMA,CAAC,CAACX,EAAE,CAAC;EAC5E;EAEA,IAAIY,KAAKA,CAAA;IAAA,IAAAC,sBAAA;IACP,OAAO,GAAAA,sBAAA,GAAC,IAAI,CAACH,aAAa,CAACnB,KAAK,cAAAsB,sBAAA,eAAxBA,sBAAA,CAA0BrB,MAAM;EAC1C;EAEA,IAAIsB,cAAcA,CAAA;IAChB,IAAItB,MAAM,GAAG,IAAI,CAACT,IAAI,CAACS,MAAM;IAC7BA,MAAM,GAAGuB,IAAI,CAACC,KAAK,CAACxB,MAAM,GAAG,IAAI,CAACyB,UAAU,CAAC,IAAI,IAAI,CAACA,UAAU,IAAIzB,MAAM,GAAG,IAAI,CAACyB,UAAU;IAC5F,OAAOzB,MAAM,GAAG,IAAI,CAAC0B,UAAU;EACjC;EA0BAC,YAA8BC,EAAqB;IAArB,KAAAA,EAAE,GAAFA,EAAE;IAnFvB,KAAAC,iBAAiB,GAAG,QAAQ;IAC5B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAApC,gBAAgB,GAAG,KAAK;IACxB,KAAAqC,OAAO,GAAU,EAAE;IACnB,KAAAR,UAAU,GAAG,CAAC;IACd,KAAAS,OAAO,GAAG,KAAK;IAoDxB,KAAApC,UAAU,GAAG,KAAK;IAClB,KAAAqC,YAAY,GAAG,KAAK;IACpB,KAAAxC,OAAO,GAA4B,EAAE;IAErC,KAAA+B,UAAU,GAAG,EAAE;IAEf,KAAAf,cAAc,GAAG,IAAI9B,aAAa,CAA0B,CAAC,CAAC;IAErD,KAAAuD,WAAW,GAAGjD,YAAY;IAC1B,KAAAkD,aAAa,GAAG,IAAI1D,kBAAkB,CAAC,EAAE,CAAC;IAC1C,KAAAuC,aAAa,GAAG,IAAIvC,kBAAkB,EAAE;IACxC,KAAA2D,UAAU,GAAG,IAAIxD,OAAO,EAAQ;IAMjB,KAAA0B,EAAE,GAAG,GAAGrB,YAAY,IAAIC,YAAY,EAAE,EAAE;IAExD,KAAAM,KAAK,GAA4B,EAAE;IACnC,KAAAqB,SAAS,GAAG,KAAK;IAEjB,KAAAwB,gBAAgB,GAAa,EAAE;IAKvC,KAAAC,QAAQ,GAAKC,CAAM,IAAK,CACxB,CAAC;EAHD;EAKAC,QAAQA,CAAA;IACN,IAAI,CAACL,aAAa,CAACM,YAAY,CAACC,IAAI,CAClC7D,MAAM,CAASQ,IAAI,IAAI,IAAI,CAACuC,UAAU,KAAK,CAAAvC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,MAAM,MAAK,IAAI,CAAC+B,iBAAiB,IAAI,CAAC,CAAC,IAAI,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,MAAM,MAAK,CAAC,CAAC,CAAC,EAChHhB,GAAG,CAAiB6D,YAAY,IAAIA,YAAY,CAACC,WAAW,EAAE,CAAC,EAC/D5D,SAAS,CAAC,IAAI,CAACoD,UAAU,CAAC,CAC3B,CAACS,SAAS,CAACC,MAAM,IAAG;MACnB,IAAI,CAACT,gBAAgB,GAAGS,MAAM,GAAG,IAAI,CAAC9B,aAAa,CAACnB,KAAK,GAAG,EAAE;MAC9D,IAAI,CAACJ,OAAO,GAAG,IAAI,CAACJ,IAAI,CAACR,MAAM,CAACoB,MAAM,IAAG;QACvC,KAAK,MAAM8C,MAAM,IAAI,IAAI,CAAChB,OAAO,EAAE;UACjC,IAAI9B,MAAM,CAAC8C,MAAM,CAACC,KAAK,CAAC,IAAI/C,MAAM,CAAC8C,MAAM,CAACC,KAAK,CAAC,CAACJ,WAAW,EAAE,CAACK,OAAO,CAACH,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;YACnF,OAAO,IAAI;UACb;QACF;QACA,OAAO,KAAK;MACd,CAAC,CAAC;MACF,IAAI,CAACpB,EAAE,CAACwB,YAAY,EAAE;IACxB,CAAC,CAAC;IAEF,IAAI,CAAClC,aAAa,CAACyB,YAAY,CAACC,IAAI,CAClC5D,GAAG,CAAmDQ,GAAG,IAAG;MAC1D,MAAM+C,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACxD,MAAM,CAACyB,EAAE,IAAI,CAAC,IAAI,CAACb,OAAO,CAAC0D,IAAI,CAAClD,MAAM,IAAIA,MAAM,CAACK,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC3G,MAAM8C,MAAM,GAAG,IAAI,CAACrD,WAAW,CAAClB,MAAM,CAAC,CAAE;QAAEyB;MAAE,CAAE,KAAM+B,gBAAgB,CAACc,IAAI,CAACE,IAAI,IAAIA,IAAI,KAAK/C,EAAE,CAAC,CAAC;MAChG,OAAO,CAAC,GAAG8C,MAAM,EAAE,GAAG9D,GAAG,CAAC;IAC5B,CAAC,CAAC,EACFR,GAAG,CAAoCQ,GAAG,IAAIA,GAAG,CAACR,GAAG,CAACuE,IAAI,IAAIA,IAAI,GAAGA,IAAI,CAAC/C,EAAE,GAAG,EAAE,CAAC,CAAC,EACnFtB,SAAS,CAAC,IAAI,CAACoD,UAAU,CAAC,CAC3B,CAACS,SAAS,CAACvD,GAAG,IAAG;MAChB,IAAI,CAACgD,QAAQ,CAAChD,GAAG,IAAI,IAAI,CAAC;MAC1B,IAAI,CAACoC,EAAE,CAAC4B,aAAa,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,UAAUA,CAAEjE,GAAkC;IAC5C,IAAI,CAACwB,kBAAkB,CAACxB,GAAG,CAAC;EAC9B;EAEAwB,kBAAkBA,CAAExB,GAAkC;IACpD,IAAI,CAACmB,cAAc,CAChBiC,IAAI,CACH3D,IAAI,CAAC,CAAC,CAAC,CACR,CACA8D,SAAS,CACRpD,OAAO,IAAG;MACR,IAAI,CAAC,IAAI,CAACC,gBAAgB,EAAE;QAC1B,IAAI,CAACE,UAAU,GAAGW,KAAK,CAACC,OAAO,CAAClB,GAAG,CAAC,IAAIA,GAAG,CAACQ,MAAM,KAAK,IAAI,CAACC,WAAW,CAACD,MAAM;MAChF;MAEA,MAAMsD,MAAM,GAAGpF,WAAW,CAACsB,GAAG,IAAI,EAAE,CAAC;MAErCG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEO,OAAO,CAACC,MAAM,IAAIA,MAAM,CAACE,KAAK,GAAG;QACxCC,OAAO,EAAEgD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE/C,QAAQ,CAACJ,MAAM,CAACK,EAAE;OACpC,CAAC;MAEF,MAAMT,KAAK,GAAGJ,OAAO,CAACZ,MAAM,CAAC2E,GAAG,IAAIJ,MAAM,CAAC/C,QAAQ,CAACmD,GAAG,CAAClD,EAAE,CAAC,CAAC;MAE5D,IAAI,CAACU,aAAa,CAACyC,UAAU,CAAC5D,KAAK,EAAE;QAAE6D,SAAS,EAAE;MAAK,CAAE,CAAC;MAE1D,IAAI,CAAChC,EAAE,CAAC4B,aAAa,EAAE;IACzB,CAAC,CACF;EACL;EAEAK,SAASA,CAAEC,KAAkB;IAC3BA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,cAAc,EAAE;IACvBD,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEE,eAAe,EAAE;IAExB,IAAI,CAAClE,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,OAAO,CAACO,OAAO,CAACC,MAAM,IAAG;MAC5B,IAAI,CAACA,MAAM,CAACU,QAAQ,EAAE;QACpBV,MAAM,CAACE,KAAK,CAACC,OAAO,GAAG,IAAI,CAACR,UAAU;MACxC;IACF,CAAC,CAAC;IAEF,IAAImE,cAAc,GAAG,IAAI,CAACtE,OAAO,CAACZ,MAAM,CAACoB,MAAM,IAAIA,MAAM,CAACE,KAAK,CAACC,OAAO,CAAC;IAExE,IAAI,CAACY,aAAa,CAACgD,QAAQ,CAACD,cAAc,CAAC;EAC7C;EAEAE,YAAYA,CAAA;IACV,IAAI,CAAChC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,CAACI,gBAAgB,GAAG,IAAI,CAACJ,YAAY,GAAG,IAAI,CAACjB,aAAa,CAACnB,KAAK,GAAG,EAAE;IACzE,IAAI,CAACJ,OAAO,GAAG,IAAI,CAACwC,YAAY,GAAG,IAAI,CAAC5C,IAAI,CAACR,MAAM,CAACoB,MAAM,IAAG;MAC3D,OAAOA,MAAM,CAACE,KAAK,CAACC,OAAO;IAC7B,CAAC,CAAC,GAAG,IAAI,CAACf,IAAI;IACd,IAAI,CAACqC,EAAE,CAACwB,YAAY,EAAE;EACxB;EAEAgB,gBAAgBA,CAAEN,KAAiB,EAAE3D,MAA6B;IAAA,IAAAkE,UAAA;IAChEP,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACE,eAAe,EAAE;IAEvB,IAAI7D,MAAM,CAACE,KAAK,EAAE;MAChBF,MAAM,CAACE,KAAK,CAACC,OAAO,GAAG,CAACH,MAAM,CAACE,KAAK,CAACC,OAAO;IAC9C;IAEA,IAAI,CAACY,aAAa,CAACyC,UAAU,EAAAU,UAAA,GAAC,IAAI,CAAC9E,IAAI,cAAA8E,UAAA,uBAATA,UAAA,CAAWtF,MAAM,CAACwE,IAAI;MAAA,IAAAe,WAAA;MAAA,QAAAA,WAAA,GAAIf,IAAI,CAAClD,KAAK,cAAAiE,WAAA,uBAAVA,WAAA,CAAYhE,OAAO;IAAA,EAAC,CAAC;EAC/E;EAEA0D,eAAeA,CAAEF,KAAY;IAC3BA,KAAK,CAACE,eAAe,EAAE;EACzB;EAEA,IAAY/D,WAAWA,CAAA;IACrB,OAAO,IAAI,CAACV,IAAI,CAACR,MAAM,CAAC,CAAE;MAAE8B;IAAQ,CAAE,KAAM,CAACA,QAAQ,CAAC;EACxD;EAEA0D,gBAAgBA,CAAEC,EAAO;IACvB,IAAI,CAAChC,QAAQ,GAAGgC,EAAE;EACpB;EAEAC,iBAAiBA,CAAA,GACjB;EAEA3D,gBAAgBA,CAAE4D,UAAmB;IACnC,IAAI,CAAC3D,SAAS,GAAG2D,UAAU;IAC3BA,UAAU,GAAG,IAAI,CAACrC,aAAa,CAACsC,OAAO,EAAE,GAAG,IAAI,CAACtC,aAAa,CAACuC,MAAM,EAAE;EACzE;;;;;UA9MCnG;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UAsBLA;EAAK;;UASLA;EAAK;;UAgCLC,SAAS;IAAAmG,IAAA,GAAC,SAAS;EAAA;;UACnBnG,SAAS;IAAAmG,IAAA,GAAC,QAAQ;EAAA;;UAClBnG,SAAS;IAAAmG,IAAA,GAAC1G,wBAAwB,EAAE;MAAE2G,MAAM,EAAE;IAAK,CAAE;EAAA;;UAErDtG;EAAW;;AA7EDa,wBAAwB,GAAA0F,UAAA,EAdpCzG,SAAS,CAAC;EACP0G,QAAQ,EAAE,uBAAuB;EACjCC,QAAA,EAAAC,oBAAiD;EAEjDC,SAAS,EAAE,CACP;IACIC,OAAO,EAAExG,iBAAiB;IAC1ByG,WAAW,EAAE9G,UAAU,CAAC,MAAMc,wBAAwB,CAAC;IACvDiG,KAAK,EAAE;GACV,CACJ;EACDC,eAAe,EAAEnH,uBAAuB,CAACoH,MAAM;EAC/CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWpG,wBAAwB,CAgNpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}