{"ast": null, "code": "var _SwuiGridRowActionsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./row-actions.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./row-actions.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nexport class RowAction {\n  constructor(value) {\n    const config = value || {};\n    this.title = config.title || '';\n    this.icon = config.icon || undefined;\n    this.svgIcon = config.svgIcon || undefined;\n    this.fontSet = config.fontSet || undefined;\n    this.fontIcon = config.fontIcon || undefined;\n    this.inMenu = typeof config.inMenu !== 'undefined' ? config.inMenu : true;\n    this.fn = config.fn || (() => true);\n    this.canActivateFn = config.canActivateFn || (() => true);\n    this.availableFn = config.availableFn || (() => true);\n    this.showOnHover = config.showOnHover || false;\n  }\n}\nlet SwuiGridRowActionsComponent = (_SwuiGridRowActionsComponent = class SwuiGridRowActionsComponent {\n  set actions(actionsItems) {\n    if (!Array.isArray(actionsItems) || !actionsItems.length) {\n      return;\n    }\n    this._actions = actionsItems.map(actionItem => new RowAction(actionItem));\n  }\n  get actions() {\n    return this._actions;\n  }\n  constructor() {\n    this.menuIcon = 'more_horiz';\n    this.ignorePlainLink = false;\n    this.standaloneActions = [];\n    this.menuActions = [];\n    this._actions = [];\n  }\n  ngOnInit() {\n    this.processActions();\n  }\n  handleMenuClick(event, action) {\n    event.preventDefault();\n    if (action.canActivateFn(this.row)) {\n      action.fn(this.row);\n    }\n  }\n  ngOnChanges(changes) {\n    if ('actions' in changes && !changes['actions'].isFirstChange()) {\n      this.processActions();\n    }\n  }\n  processActions() {\n    this._actions = this._actions.filter(action => {\n      let filtered = true;\n      if (!!action.availableFn) {\n        filtered = action.availableFn(this.row);\n      }\n      return filtered;\n    });\n    this.populateActions();\n  }\n  populateActions() {\n    this.menuActions = this._actions.filter(item => item.inMenu);\n    this.standaloneActions = this._actions.filter(item => !item.inMenu);\n  }\n}, _SwuiGridRowActionsComponent.ctorParameters = () => [], _SwuiGridRowActionsComponent.propDecorators = {\n  actions: [{\n    type: Input\n  }],\n  row: [{\n    type: Input\n  }],\n  menuIcon: [{\n    type: Input\n  }],\n  ignorePlainLink: [{\n    type: Input\n  }]\n}, _SwuiGridRowActionsComponent);\nSwuiGridRowActionsComponent = __decorate([Component({\n  selector: 'lib-swui-grid-row-actions',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiGridRowActionsComponent);\nexport { SwuiGridRowActionsComponent };", "map": {"version": 3, "names": ["Component", "Input", "RowAction", "constructor", "value", "config", "title", "icon", "undefined", "svgIcon", "fontSet", "fontIcon", "inMenu", "fn", "canActivateFn", "availableFn", "showOnHover", "SwuiGridRowActionsComponent", "_SwuiGridRowActionsComponent", "actions", "actionsItems", "Array", "isArray", "length", "_actions", "map", "actionItem", "menuIcon", "ignorePlainLink", "standaloneActions", "menuActions", "ngOnInit", "processActions", "handleMenuClick", "event", "action", "preventDefault", "row", "ngOnChanges", "changes", "isFirstChange", "filter", "filtered", "populateActions", "item", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/row-actions/row-actions.component.ts"], "sourcesContent": ["import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';\n\nexport class RowAction {\n  title: string;\n  icon?: string;\n  svgIcon?: string;\n  fontSet?: string;\n  fontIcon?: string;\n  inMenu?: boolean;\n  showOnHover?: boolean;\n  fn: (...args: any[]) => any | void;\n  canActivateFn: (item?: any) => boolean;\n  availableFn?: (item?: any) => boolean;\n\n  constructor(value: any) {\n    const config = value || {};\n    this.title = config.title || '';\n    this.icon = config.icon || undefined;\n    this.svgIcon = config.svgIcon || undefined;\n    this.fontSet = config.fontSet || undefined;\n    this.fontIcon = config.fontIcon || undefined;\n    this.inMenu = typeof config.inMenu !== 'undefined' ? config.inMenu : true;\n    this.fn = config.fn || (() => true);\n    this.canActivateFn = config.canActivateFn || (() => true);\n    this.availableFn = config.availableFn || (() => true);\n    this.showOnHover = config.showOnHover || false;\n  }\n}\n\n@Component({\n    selector: 'lib-swui-grid-row-actions',\n    templateUrl: './row-actions.component.html',\n    styleUrls: ['./row-actions.component.scss'],\n    standalone: false\n})\nexport class SwuiGridRowActionsComponent implements OnInit, OnChanges {\n  @Input()\n  set actions(actionsItems: any[]) {\n    if (!Array.isArray(actionsItems) || !actionsItems.length) {\n      return;\n    }\n    this._actions = actionsItems.map(actionItem => new RowAction(actionItem));\n  }\n\n  get actions(): RowAction[] {\n    return this._actions;\n  }\n\n  @Input() row: any;\n  @Input() menuIcon = 'more_horiz';\n  @Input() ignorePlainLink = false;\n\n  standaloneActions: RowAction[] = [];\n  menuActions: RowAction[] = [];\n  private _actions: RowAction[] = [];\n\n  constructor() {\n  }\n\n  ngOnInit(): void {\n    this.processActions();\n  }\n\n  handleMenuClick(event: MouseEvent, action: RowAction) {\n    event.preventDefault();\n    if (action.canActivateFn(this.row)) {\n      action.fn(this.row);\n    }\n  }\n\n  ngOnChanges( changes: SimpleChanges ): void {\n    if ('actions' in changes && !changes['actions'].isFirstChange()) {\n      this.processActions();\n    }\n  }\n\n  private processActions() {\n    this._actions = this._actions\n      .filter((action) => {\n        let filtered = true;\n        if (!!action.availableFn) {\n          filtered = action.availableFn(this.row);\n        }\n        return filtered;\n      });\n\n    this.populateActions();\n  }\n\n  private populateActions() {\n    this.menuActions = this._actions.filter(item => item.inMenu);\n    this.standaloneActions = this._actions.filter(item => !item.inMenu);\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,KAAK,QAA0C,eAAe;AAElF,OAAM,MAAOC,SAAS;EAYpBC,YAAYC,KAAU;IACpB,MAAMC,MAAM,GAAGD,KAAK,IAAI,EAAE;IAC1B,IAAI,CAACE,KAAK,GAAGD,MAAM,CAACC,KAAK,IAAI,EAAE;IAC/B,IAAI,CAACC,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAIC,SAAS;IACpC,IAAI,CAACC,OAAO,GAAGJ,MAAM,CAACI,OAAO,IAAID,SAAS;IAC1C,IAAI,CAACE,OAAO,GAAGL,MAAM,CAACK,OAAO,IAAIF,SAAS;IAC1C,IAAI,CAACG,QAAQ,GAAGN,MAAM,CAACM,QAAQ,IAAIH,SAAS;IAC5C,IAAI,CAACI,MAAM,GAAG,OAAOP,MAAM,CAACO,MAAM,KAAK,WAAW,GAAGP,MAAM,CAACO,MAAM,GAAG,IAAI;IACzE,IAAI,CAACC,EAAE,GAAGR,MAAM,CAACQ,EAAE,KAAK,MAAM,IAAI,CAAC;IACnC,IAAI,CAACC,aAAa,GAAGT,MAAM,CAACS,aAAa,KAAK,MAAM,IAAI,CAAC;IACzD,IAAI,CAACC,WAAW,GAAGV,MAAM,CAACU,WAAW,KAAK,MAAM,IAAI,CAAC;IACrD,IAAI,CAACC,WAAW,GAAGX,MAAM,CAACW,WAAW,IAAI,KAAK;EAChD;;AASK,IAAMC,2BAA2B,IAAAC,4BAAA,GAAjC,MAAMD,2BAA2B;MAElCE,OAAOA,CAACC,YAAmB;IAC7B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,IAAI,CAACA,YAAY,CAACG,MAAM,EAAE;MACxD;IACF;IACA,IAAI,CAACC,QAAQ,GAAGJ,YAAY,CAACK,GAAG,CAACC,UAAU,IAAI,IAAIxB,SAAS,CAACwB,UAAU,CAAC,CAAC;EAC3E;EAEA,IAAIP,OAAOA,CAAA;IACT,OAAO,IAAI,CAACK,QAAQ;EACtB;EAUArB,YAAA;IAPS,KAAAwB,QAAQ,GAAG,YAAY;IACvB,KAAAC,eAAe,GAAG,KAAK;IAEhC,KAAAC,iBAAiB,GAAgB,EAAE;IACnC,KAAAC,WAAW,GAAgB,EAAE;IACrB,KAAAN,QAAQ,GAAgB,EAAE;EAGlC;EAEAO,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,eAAeA,CAACC,KAAiB,EAAEC,MAAiB;IAClDD,KAAK,CAACE,cAAc,EAAE;IACtB,IAAID,MAAM,CAACrB,aAAa,CAAC,IAAI,CAACuB,GAAG,CAAC,EAAE;MAClCF,MAAM,CAACtB,EAAE,CAAC,IAAI,CAACwB,GAAG,CAAC;IACrB;EACF;EAEAC,WAAWA,CAAEC,OAAsB;IACjC,IAAI,SAAS,IAAIA,OAAO,IAAI,CAACA,OAAO,CAAC,SAAS,CAAC,CAACC,aAAa,EAAE,EAAE;MAC/D,IAAI,CAACR,cAAc,EAAE;IACvB;EACF;EAEQA,cAAcA,CAAA;IACpB,IAAI,CAACR,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAC1BiB,MAAM,CAAEN,MAAM,IAAI;MACjB,IAAIO,QAAQ,GAAG,IAAI;MACnB,IAAI,CAAC,CAACP,MAAM,CAACpB,WAAW,EAAE;QACxB2B,QAAQ,GAAGP,MAAM,CAACpB,WAAW,CAAC,IAAI,CAACsB,GAAG,CAAC;MACzC;MACA,OAAOK,QAAQ;IACjB,CAAC,CAAC;IAEJ,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQA,eAAeA,CAAA;IACrB,IAAI,CAACb,WAAW,GAAG,IAAI,CAACN,QAAQ,CAACiB,MAAM,CAACG,IAAI,IAAIA,IAAI,CAAChC,MAAM,CAAC;IAC5D,IAAI,CAACiB,iBAAiB,GAAG,IAAI,CAACL,QAAQ,CAACiB,MAAM,CAACG,IAAI,IAAI,CAACA,IAAI,CAAChC,MAAM,CAAC;EACrE;;;UAxDCX;EAAK;;UAYLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;AAfKgB,2BAA2B,GAAA4B,UAAA,EANvC7C,SAAS,CAAC;EACP8C,QAAQ,EAAE,2BAA2B;EACrCC,QAAA,EAAAC,oBAA2C;EAE3CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWhC,2BAA2B,CA0DvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}