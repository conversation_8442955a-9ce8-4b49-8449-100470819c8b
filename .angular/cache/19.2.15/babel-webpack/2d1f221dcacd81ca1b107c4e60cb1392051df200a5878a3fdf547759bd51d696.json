{"ast": null, "code": "export class SwuiDatePickerConfigModel {\n  constructor(config) {\n    this.dateFormat = config && config.dateFormat ? config.dateFormat : 'DD.MM.YYYY';\n    this.timeFormat = config && config.timeFormat ? config.timeFormat : 'HH:mm:ss';\n    this.timePicker = config && config.timePicker ? config.timePicker : false;\n    this.timeDisableLevel = config && config.timeDisableLevel ? config.timeDisableLevel : undefined;\n    this.timeZone = config && config.timeZone ? config.timeZone : undefined;\n    this.maxPeriod = config === null || config === void 0 ? void 0 : config.maxPeriod;\n    this.chooseStart = (config === null || config === void 0 ? void 0 : config.chooseStart) || false;\n  }\n}", "map": {"version": 3, "names": ["SwuiDatePickerConfigModel", "constructor", "config", "dateFormat", "timeFormat", "timePicker", "timeDisableLevel", "undefined", "timeZone", "max<PERSON><PERSON><PERSON>", "chooseStart"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-picker/swui-date-picker-config.model.ts"], "sourcesContent": ["import { unitOfTime } from 'moment';\nimport { SwuiTimepickerTimeDisableLevel } from '../swui-timepicker/swui-timepicker.interface';\n\nexport interface SwuiDatePickerConfig {\n  dateFormat?: string;\n  timePicker?: boolean;\n  timeDisableLevel?: SwuiTimepickerTimeDisableLevel;\n  timeFormat?: string;\n  timeZone?: string;\n  maxPeriod?: unitOfTime.Base;\n  chooseStart?: boolean;\n}\n\nexport class SwuiDatePickerConfigModel {\n  dateFormat?: string;\n  timePicker?: boolean;\n  timeDisableLevel?: SwuiTimepickerTimeDisableLevel;\n  timeFormat?: string;\n  timeZone?: string;\n  maxPeriod?: unitOfTime.Base;\n  chooseStart?: boolean;\n\n  constructor(config: SwuiDatePickerConfig | undefined) {\n    this.dateFormat = config && config.dateFormat ? config.dateFormat : 'DD.MM.YYYY';\n    this.timeFormat = config && config.timeFormat ? config.timeFormat : 'HH:mm:ss';\n    this.timePicker = config && config.timePicker ? config.timePicker : false;\n    this.timeDisableLevel = config && config.timeDisableLevel ? config.timeDisableLevel : undefined;\n    this.timeZone = config && config.timeZone ? config.timeZone : undefined;\n    this.maxPeriod = config?.maxPeriod;\n    this.chooseStart = config?.chooseStart || false;\n  }\n}\n"], "mappings": "AAaA,OAAM,MAAOA,yBAAyB;EASpCC,YAAYC,MAAwC;IAClD,IAAI,CAACC,UAAU,GAAGD,MAAM,IAAIA,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACC,UAAU,GAAG,YAAY;IAChF,IAAI,CAACC,UAAU,GAAGF,MAAM,IAAIA,MAAM,CAACE,UAAU,GAAGF,MAAM,CAACE,UAAU,GAAG,UAAU;IAC9E,IAAI,CAACC,UAAU,GAAGH,MAAM,IAAIA,MAAM,CAACG,UAAU,GAAGH,MAAM,CAACG,UAAU,GAAG,KAAK;IACzE,IAAI,CAACC,gBAAgB,GAAGJ,MAAM,IAAIA,MAAM,CAACI,gBAAgB,GAAGJ,MAAM,CAACI,gBAAgB,GAAGC,SAAS;IAC/F,IAAI,CAACC,QAAQ,GAAGN,MAAM,IAAIA,MAAM,CAACM,QAAQ,GAAGN,MAAM,CAACM,QAAQ,GAAGD,SAAS;IACvE,IAAI,CAACE,SAAS,GAAGP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,SAAS;IAClC,IAAI,CAACC,WAAW,GAAG,CAAAR,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,WAAW,KAAI,KAAK;EACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}