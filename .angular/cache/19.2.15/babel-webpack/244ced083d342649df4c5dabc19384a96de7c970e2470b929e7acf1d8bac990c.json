{"ast": null, "code": "import moment from 'moment';\nexport class SwuiDateRangeModel {\n  constructor(data) {\n    this.from = data ? data.from : '';\n    this.to = data ? data.to : '';\n  }\n}\nexport const CUSTOM_PERIODS = [{\n  title: 'COMPONENTS.DATE_RANGE.last7Days',\n  fn: (timezone, chooseStart) => {\n    const date = timezone ? moment().tz(timezone) : moment.utc();\n    const to = chooseStart ? date.clone().add(1, 'day').startOf('day') : date.clone().endOf('day');\n    const from = date.clone().startOf('day').subtract(6, 'd');\n    return {\n      from: from.toISOString(),\n      to: to.milliseconds(0).toISOString()\n    };\n  }\n}, {\n  title: 'COMPONENTS.DATE_RANGE.last14Days',\n  fn: (timezone, chooseStart) => {\n    const date = timezone ? moment().tz(timezone) : moment.utc();\n    const to = chooseStart ? date.clone().add(1, 'day').startOf('day') : date.clone().endOf('day');\n    const from = date.clone().startOf('day').subtract(13, 'd');\n    return {\n      from: from.toISOString(),\n      to: to.milliseconds(0).toISOString()\n    };\n  }\n}, {\n  title: 'COMPONENTS.DATE_RANGE.last30Days',\n  fn: (timezone, chooseStart) => {\n    const date = timezone ? moment().tz(timezone) : moment.utc();\n    const to = chooseStart ? date.clone().add(1, 'day').startOf('day') : date.clone().endOf('day');\n    const from = date.clone().startOf('day').subtract(29, 'd');\n    return {\n      from: from.toISOString(),\n      to: to.milliseconds(0).toISOString()\n    };\n  }\n}, {\n  title: 'COMPONENTS.DATE_RANGE.last90Days',\n  fn: (timezone, chooseStart) => {\n    const date = timezone ? moment().tz(timezone) : moment.utc();\n    const to = chooseStart ? date.clone().add(1, 'day').startOf('day') : date.clone().endOf('day');\n    const from = date.clone().startOf('day').subtract(89, 'd');\n    return {\n      from: from.toISOString(),\n      to: to.milliseconds(0).toISOString()\n    };\n  }\n}, {\n  title: 'COMPONENTS.DATE_RANGE.thisMonth',\n  fn: (timezone, chooseStart) => {\n    const date = timezone ? moment().tz(timezone) : moment.utc();\n    const from = date.startOf('month');\n    const to = chooseStart ? from.clone().add(1, 'month').startOf('day') : from.clone().add(1, 'month').add(-1, 'day').endOf('day');\n    return {\n      from: from.toISOString(),\n      to: to.milliseconds(0).toISOString()\n    };\n  }\n}, {\n  title: 'COMPONENTS.DATE_RANGE.lastMonth',\n  fn: (timezone, chooseStart) => {\n    const date = timezone ? moment().tz(timezone) : moment.utc();\n    const from = date.clone().subtract(1, 'month').startOf('month');\n    const to = chooseStart ? from.clone().endOf('month').add(1, 'day').startOf('day') : from.clone().endOf('month').endOf('day');\n    return {\n      from: from.toISOString(),\n      to: to.milliseconds(0).toISOString()\n    };\n  }\n}];", "map": {"version": 3, "names": ["moment", "SwuiDateRangeModel", "constructor", "data", "from", "to", "CUSTOM_PERIODS", "title", "fn", "timezone", "chooseStart", "date", "tz", "utc", "clone", "add", "startOf", "endOf", "subtract", "toISOString", "milliseconds"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-range/swui-date-range.model.ts"], "sourcesContent": ["import moment from 'moment';\nexport class SwuiDateRangeModel {\n    constructor(data) {\n        this.from = data ? data.from : '';\n        this.to = data ? data.to : '';\n    }\n}\nexport const CUSTOM_PERIODS = [\n    {\n        title: 'COMPONENTS.DATE_RANGE.last7Days',\n        fn: (timezone, chooseStart) => {\n            const date = timezone ? moment().tz(timezone) : moment.utc();\n            const to = chooseStart\n                ? date.clone().add(1, 'day').startOf('day')\n                : date.clone().endOf('day');\n            const from = date.clone().startOf('day').subtract(6, 'd');\n            return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };\n        },\n    },\n    {\n        title: 'COMPONENTS.DATE_RANGE.last14Days',\n        fn: (timezone, chooseStart) => {\n            const date = timezone ? moment().tz(timezone) : moment.utc();\n            const to = chooseStart\n                ? date.clone().add(1, 'day').startOf('day')\n                : date.clone().endOf('day');\n            const from = date.clone().startOf('day').subtract(13, 'd');\n            return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };\n        },\n    },\n    {\n        title: 'COMPONENTS.DATE_RANGE.last30Days',\n        fn: (timezone, chooseStart) => {\n            const date = timezone ? moment().tz(timezone) : moment.utc();\n            const to = chooseStart\n                ? date.clone().add(1, 'day').startOf('day')\n                : date.clone().endOf('day');\n            const from = date.clone().startOf('day').subtract(29, 'd');\n            return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };\n        },\n    },\n    {\n        title: 'COMPONENTS.DATE_RANGE.last90Days',\n        fn: (timezone, chooseStart) => {\n            const date = timezone ? moment().tz(timezone) : moment.utc();\n            const to = chooseStart\n                ? date.clone().add(1, 'day').startOf('day')\n                : date.clone().endOf('day');\n            const from = date.clone().startOf('day').subtract(89, 'd');\n            return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };\n        },\n    },\n    {\n        title: 'COMPONENTS.DATE_RANGE.thisMonth',\n        fn: (timezone, chooseStart) => {\n            const date = timezone ? moment().tz(timezone) : moment.utc();\n            const from = date.startOf('month');\n            const to = chooseStart\n                ? from.clone().add(1, 'month').startOf('day')\n                : from.clone().add(1, 'month').add(-1, 'day').endOf('day');\n            return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };\n        },\n    },\n    {\n        title: 'COMPONENTS.DATE_RANGE.lastMonth',\n        fn: (timezone, chooseStart) => {\n            const date = timezone ? moment().tz(timezone) : moment.utc();\n            const from = date.clone().subtract(1, 'month').startOf('month');\n            const to = chooseStart\n                ? from.clone().endOf('month').add(1, 'day').startOf('day')\n                : from.clone().endOf('month').endOf('day');\n            return { from: from.toISOString(), to: to.milliseconds(0).toISOString() };\n        },\n    },\n];\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,QAAQ;AAC3B,OAAO,MAAMC,kBAAkB,CAAC;EAC5BC,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACC,IAAI,GAAGD,IAAI,GAAGA,IAAI,CAACC,IAAI,GAAG,EAAE;IACjC,IAAI,CAACC,EAAE,GAAGF,IAAI,GAAGA,IAAI,CAACE,EAAE,GAAG,EAAE;EACjC;AACJ;AACA,OAAO,MAAMC,cAAc,GAAG,CAC1B;EACIC,KAAK,EAAE,iCAAiC;EACxCC,EAAE,EAAEA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC3B,MAAMC,IAAI,GAAGF,QAAQ,GAAGT,MAAM,CAAC,CAAC,CAACY,EAAE,CAACH,QAAQ,CAAC,GAAGT,MAAM,CAACa,GAAG,CAAC,CAAC;IAC5D,MAAMR,EAAE,GAAGK,WAAW,GAChBC,IAAI,CAACG,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC,GACzCL,IAAI,CAACG,KAAK,CAAC,CAAC,CAACG,KAAK,CAAC,KAAK,CAAC;IAC/B,MAAMb,IAAI,GAAGO,IAAI,CAACG,KAAK,CAAC,CAAC,CAACE,OAAO,CAAC,KAAK,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,OAAO;MAAEd,IAAI,EAAEA,IAAI,CAACe,WAAW,CAAC,CAAC;MAAEd,EAAE,EAAEA,EAAE,CAACe,YAAY,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC;IAAE,CAAC;EAC7E;AACJ,CAAC,EACD;EACIZ,KAAK,EAAE,kCAAkC;EACzCC,EAAE,EAAEA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC3B,MAAMC,IAAI,GAAGF,QAAQ,GAAGT,MAAM,CAAC,CAAC,CAACY,EAAE,CAACH,QAAQ,CAAC,GAAGT,MAAM,CAACa,GAAG,CAAC,CAAC;IAC5D,MAAMR,EAAE,GAAGK,WAAW,GAChBC,IAAI,CAACG,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC,GACzCL,IAAI,CAACG,KAAK,CAAC,CAAC,CAACG,KAAK,CAAC,KAAK,CAAC;IAC/B,MAAMb,IAAI,GAAGO,IAAI,CAACG,KAAK,CAAC,CAAC,CAACE,OAAO,CAAC,KAAK,CAAC,CAACE,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC;IAC1D,OAAO;MAAEd,IAAI,EAAEA,IAAI,CAACe,WAAW,CAAC,CAAC;MAAEd,EAAE,EAAEA,EAAE,CAACe,YAAY,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC;IAAE,CAAC;EAC7E;AACJ,CAAC,EACD;EACIZ,KAAK,EAAE,kCAAkC;EACzCC,EAAE,EAAEA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC3B,MAAMC,IAAI,GAAGF,QAAQ,GAAGT,MAAM,CAAC,CAAC,CAACY,EAAE,CAACH,QAAQ,CAAC,GAAGT,MAAM,CAACa,GAAG,CAAC,CAAC;IAC5D,MAAMR,EAAE,GAAGK,WAAW,GAChBC,IAAI,CAACG,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC,GACzCL,IAAI,CAACG,KAAK,CAAC,CAAC,CAACG,KAAK,CAAC,KAAK,CAAC;IAC/B,MAAMb,IAAI,GAAGO,IAAI,CAACG,KAAK,CAAC,CAAC,CAACE,OAAO,CAAC,KAAK,CAAC,CAACE,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC;IAC1D,OAAO;MAAEd,IAAI,EAAEA,IAAI,CAACe,WAAW,CAAC,CAAC;MAAEd,EAAE,EAAEA,EAAE,CAACe,YAAY,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC;IAAE,CAAC;EAC7E;AACJ,CAAC,EACD;EACIZ,KAAK,EAAE,kCAAkC;EACzCC,EAAE,EAAEA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC3B,MAAMC,IAAI,GAAGF,QAAQ,GAAGT,MAAM,CAAC,CAAC,CAACY,EAAE,CAACH,QAAQ,CAAC,GAAGT,MAAM,CAACa,GAAG,CAAC,CAAC;IAC5D,MAAMR,EAAE,GAAGK,WAAW,GAChBC,IAAI,CAACG,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC,GACzCL,IAAI,CAACG,KAAK,CAAC,CAAC,CAACG,KAAK,CAAC,KAAK,CAAC;IAC/B,MAAMb,IAAI,GAAGO,IAAI,CAACG,KAAK,CAAC,CAAC,CAACE,OAAO,CAAC,KAAK,CAAC,CAACE,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC;IAC1D,OAAO;MAAEd,IAAI,EAAEA,IAAI,CAACe,WAAW,CAAC,CAAC;MAAEd,EAAE,EAAEA,EAAE,CAACe,YAAY,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC;IAAE,CAAC;EAC7E;AACJ,CAAC,EACD;EACIZ,KAAK,EAAE,iCAAiC;EACxCC,EAAE,EAAEA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC3B,MAAMC,IAAI,GAAGF,QAAQ,GAAGT,MAAM,CAAC,CAAC,CAACY,EAAE,CAACH,QAAQ,CAAC,GAAGT,MAAM,CAACa,GAAG,CAAC,CAAC;IAC5D,MAAMT,IAAI,GAAGO,IAAI,CAACK,OAAO,CAAC,OAAO,CAAC;IAClC,MAAMX,EAAE,GAAGK,WAAW,GAChBN,IAAI,CAACU,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC,GAC3CZ,IAAI,CAACU,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAACA,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAACE,KAAK,CAAC,KAAK,CAAC;IAC9D,OAAO;MAAEb,IAAI,EAAEA,IAAI,CAACe,WAAW,CAAC,CAAC;MAAEd,EAAE,EAAEA,EAAE,CAACe,YAAY,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC;IAAE,CAAC;EAC7E;AACJ,CAAC,EACD;EACIZ,KAAK,EAAE,iCAAiC;EACxCC,EAAE,EAAEA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC3B,MAAMC,IAAI,GAAGF,QAAQ,GAAGT,MAAM,CAAC,CAAC,CAACY,EAAE,CAACH,QAAQ,CAAC,GAAGT,MAAM,CAACa,GAAG,CAAC,CAAC;IAC5D,MAAMT,IAAI,GAAGO,IAAI,CAACG,KAAK,CAAC,CAAC,CAACI,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAACF,OAAO,CAAC,OAAO,CAAC;IAC/D,MAAMX,EAAE,GAAGK,WAAW,GAChBN,IAAI,CAACU,KAAK,CAAC,CAAC,CAACG,KAAK,CAAC,OAAO,CAAC,CAACF,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC,GACxDZ,IAAI,CAACU,KAAK,CAAC,CAAC,CAACG,KAAK,CAAC,OAAO,CAAC,CAACA,KAAK,CAAC,KAAK,CAAC;IAC9C,OAAO;MAAEb,IAAI,EAAEA,IAAI,CAACe,WAAW,CAAC,CAAC;MAAEd,EAAE,EAAEA,EAAE,CAACe,YAAY,CAAC,CAAC,CAAC,CAACD,WAAW,CAAC;IAAE,CAAC;EAC7E;AACJ,CAAC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}