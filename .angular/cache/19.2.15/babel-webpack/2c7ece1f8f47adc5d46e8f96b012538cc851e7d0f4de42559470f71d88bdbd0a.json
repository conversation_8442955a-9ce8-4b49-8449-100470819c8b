{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuComponent } from './swui-menu.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTreeModule } from '@angular/material/tree';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { MatRippleModule } from '@angular/material/core';\nexport const MAT_MODULES = [MatListModule, MatMenuModule, MatTreeModule, MatIconModule, MatRippleModule];\nlet SwuiMenuModule = class SwuiMenuModule {};\nSwuiMenuModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule.forChild(), RouterModule, ...MAT_MODULES],\n  declarations: [SwuiMenuComponent],\n  exports: [SwuiMenuComponent]\n})], SwuiMenuModule);\nexport { SwuiMenuModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "RouterModule", "TranslateModule", "SwuiMenuComponent", "MatMenuModule", "MatTreeModule", "MatIconModule", "MatListModule", "MatRippleModule", "MAT_MODULES", "SwuiMenuModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-menu/swui-menu.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuComponent } from './swui-menu.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTreeModule } from '@angular/material/tree';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { MatRippleModule } from '@angular/material/core';\nexport const MAT_MODULES = [\n    MatListModule,\n    MatMenuModule,\n    MatTreeModule,\n    MatIconModule,\n    MatRippleModule,\n];\nlet SwuiMenuModule = class SwuiMenuModule {\n};\nSwuiMenuModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule.forChild(),\n            RouterModule,\n            ...MAT_MODULES,\n        ],\n        declarations: [SwuiMenuComponent],\n        exports: [SwuiMenuComponent]\n    })\n], SwuiMenuModule);\nexport { SwuiMenuModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,MAAMC,WAAW,GAAG,CACvBF,aAAa,EACbH,aAAa,EACbC,aAAa,EACbC,aAAa,EACbE,eAAe,CAClB;AACD,IAAIE,cAAc,GAAG,MAAMA,cAAc,CAAC,EACzC;AACDA,cAAc,GAAGZ,UAAU,CAAC,CACxBC,QAAQ,CAAC;EACLY,OAAO,EAAE,CACLX,YAAY,EACZE,eAAe,CAACU,QAAQ,CAAC,CAAC,EAC1BX,YAAY,EACZ,GAAGQ,WAAW,CACjB;EACDI,YAAY,EAAE,CAACV,iBAAiB,CAAC;EACjCW,OAAO,EAAE,CAACX,iBAAiB;AAC/B,CAAC,CAAC,CACL,EAAEO,cAAc,CAAC;AAClB,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}