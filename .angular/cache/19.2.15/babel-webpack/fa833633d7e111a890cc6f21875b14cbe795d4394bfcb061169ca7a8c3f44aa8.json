{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatPseudoCheckboxModule, MatRippleModule } from '@angular/material/core';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSelectModule } from '@angular/material/select';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiSelectComponent } from './swui-select.component';\nexport const SELECT_MODULES = [FormsModule, ReactiveFormsModule, MatSelectModule, MatInputModule, MatIconModule, MatCheckboxModule, MatRippleModule, ScrollingModule, MatMenuModule];\nlet SwuiSelectModule = class SwuiSelectModule {};\nSwuiSelectModule = __decorate([NgModule({\n  declarations: [SwuiSelectComponent],\n  imports: [CommonModule, TranslateModule.forChild(), ...SELECT_MODULES, MatMenuModule, MatPseudoCheckboxModule],\n  exports: [SwuiSelectComponent]\n})], SwuiSelectModule);\nexport { SwuiSelectModule };", "map": {"version": 3, "names": ["ScrollingModule", "CommonModule", "NgModule", "FormsModule", "ReactiveFormsModule", "MatCheckboxModule", "MatPseudoCheckboxModule", "MatRippleModule", "MatIconModule", "MatInputModule", "MatMenuModule", "MatSelectModule", "TranslateModule", "SwuiSelectComponent", "SELECT_MODULES", "SwuiSelectModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-select/swui-select.module.ts"], "sourcesContent": ["import { ScrollingModule } from '@angular/cdk/scrolling';\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatPseudoCheckboxModule, MatRippleModule } from '@angular/material/core';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSelectModule } from '@angular/material/select';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { SwuiSelectComponent } from './swui-select.component';\n\n\nexport const SELECT_MODULES = [\n  FormsModule,\n  ReactiveFormsModule,\n  MatSelectModule,\n  MatInputModule,\n  MatIconModule,\n  MatCheckboxModule,\n  MatRippleModule,\n  ScrollingModule,\n  MatMenuModule,\n];\n\n@NgModule({\n  declarations: [SwuiSelectComponent],\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    ...SELECT_MODULES,\n    MatMenuModule,\n    MatPseudoCheckboxModule\n  ],\n  exports: [SwuiSelectComponent],\n})\nexport class SwuiSelectModule {\n}\n"], "mappings": ";AAAA,SAASA,eAAe,QAAQ,wBAAwB;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,uBAAuB,EAAEC,eAAe,QAAQ,wBAAwB;AACjF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,mBAAmB,QAAQ,yBAAyB;AAG7D,OAAO,MAAMC,cAAc,GAAG,CAC5BX,WAAW,EACXC,mBAAmB,EACnBO,eAAe,EACfF,cAAc,EACdD,aAAa,EACbH,iBAAiB,EACjBE,eAAe,EACfP,eAAe,EACfU,aAAa,CACd;AAaM,IAAMK,gBAAgB,GAAtB,MAAMA,gBAAgB,GAC5B;AADYA,gBAAgB,GAAAC,UAAA,EAX5Bd,QAAQ,CAAC;EACRe,YAAY,EAAE,CAACJ,mBAAmB,CAAC;EACnCK,OAAO,EAAE,CACPjB,YAAY,EACZW,eAAe,CAACO,QAAQ,EAAE,EAC1B,GAAGL,cAAc,EACjBJ,aAAa,EACbJ,uBAAuB,CACxB;EACDc,OAAO,EAAE,CAACP,mBAAmB;CAC9B,CAAC,C,EACWE,gBAAgB,CAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}