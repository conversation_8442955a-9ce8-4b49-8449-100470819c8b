{"ast": null, "code": "var _SwuiMenuComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-menu.component.scss?ngResource\";\nimport { Component, Input, ViewChild } from '@angular/core';\nimport { NavigationEnd, Router } from '@angular/router';\nimport { NestedTreeControl } from '@angular/cdk/tree';\nimport { MatTreeNestedDataSource } from '@angular/material/tree';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nlet SwuiMenuComponent = (_SwuiMenuComponent = class SwuiMenuComponent {\n  get isSidebarCollapsed() {\n    return this._isSidebarCollapsed;\n  }\n  set isSidebarCollapsed(value) {\n    this._isSidebarCollapsed = value;\n    if (!value && this.currentParent) {\n      this.treeControl.expand(this.currentParent);\n    } else {\n      this.collapseAll();\n    }\n  }\n  get isSidebarHovered() {\n    return this._isSidebarHovered;\n  }\n  set isSidebarHovered(value) {\n    this._isSidebarHovered = value;\n    if (this.isSidebarCollapsed && !value) {\n      this.collapseAll();\n    } else if (this.currentParent) {\n      this.treeControl.expand(this.currentParent);\n    }\n  }\n  set items(items) {\n    if (!items) {\n      return;\n    }\n    this.loading = false;\n    this.dataSource.data = items;\n    this.expandParentItem();\n  }\n  constructor(router) {\n    this.router = router;\n    this.loading = true;\n    this.loadingArray = Array;\n    this.treeControl = new NestedTreeControl(item => item.children);\n    this.dataSource = new MatTreeNestedDataSource();\n    this._isSidebarCollapsed = false;\n    this._isSidebarHovered = false;\n    this.destroy$ = new Subject();\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe(event => {\n      const item = this.dataSource.data.find(({\n        url\n      }) => event.url.indexOf(url) !== -1);\n      if (item && this.currentParent !== item) {\n        var _item$children;\n        if ((_item$children = item.children) !== null && _item$children !== void 0 && _item$children.length) {\n          this.onParentClick(item);\n        } else {\n          this.onItemClick(item);\n        }\n        this.expandParentItem();\n      }\n    });\n  }\n  ngOnInit() {\n    if (this.menuRef) {\n      this.menuRef.nativeElement.style.setProperty('--active-color', this.activeColor || '#ffffff');\n    }\n  }\n  ngOnDestroy() {\n    if (this.menuRef) {\n      this.menuRef.nativeElement.style.removeProperty('--active-color');\n    }\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n  hasChild(_, item) {\n    return !!item.children && item.children.length > 0;\n  }\n  collapseAll() {\n    this.treeControl.collapseAll();\n  }\n  onItemClick(item) {\n    this.currentParent = this.getParentMenuItem(item.url);\n    this.collapseOther(this.currentParent);\n  }\n  onParentClick(item) {\n    this.treeControl.expand(item);\n    this.collapseOther(item);\n  }\n  collapseOther(item) {\n    this.dataSource.data.forEach(el => {\n      if (el !== item && el.children) {\n        this.treeControl.collapse(el);\n      }\n    });\n  }\n  getParentMenuItem(url) {\n    return this.dataSource.data.find(item => {\n      if (item.children && item.children.length > 0) {\n        return item.children.some(childItem => url.includes(childItem.url));\n      } else {\n        return item.url === url;\n      }\n    });\n  }\n  expandParentItem() {\n    this.currentParent = this.getParentMenuItem(this.router.url);\n    if (this.currentParent && !this.isSidebarCollapsed) {\n      this.treeControl.expand(this.currentParent);\n    }\n  }\n}, _SwuiMenuComponent.ctorParameters = () => [{\n  type: Router\n}], _SwuiMenuComponent.propDecorators = {\n  activeColor: [{\n    type: Input\n  }],\n  isSidebarCollapsed: [{\n    type: Input\n  }],\n  isSidebarHovered: [{\n    type: Input\n  }],\n  items: [{\n    type: Input\n  }],\n  menuRef: [{\n    type: ViewChild,\n    args: ['menu', {\n      static: true\n    }]\n  }]\n}, _SwuiMenuComponent);\nSwuiMenuComponent = __decorate([Component({\n  selector: 'lib-swui-menu',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiMenuComponent);\nexport { SwuiMenuComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "ViewChild", "NavigationEnd", "Router", "NestedTreeControl", "MatTreeNestedDataSource", "Subject", "filter", "takeUntil", "SwuiMenuComponent", "_SwuiMenuComponent", "isSidebarCollapsed", "_isSidebarCollapsed", "value", "currentParent", "treeControl", "expand", "collapseAll", "isSidebarHovered", "_isSidebarHovered", "items", "loading", "dataSource", "data", "expandParentItem", "constructor", "router", "loadingArray", "Array", "item", "children", "destroy$", "events", "pipe", "event", "subscribe", "find", "url", "indexOf", "_item$children", "length", "onParentClick", "onItemClick", "ngOnInit", "menuRef", "nativeElement", "style", "setProperty", "activeColor", "ngOnDestroy", "removeProperty", "next", "undefined", "complete", "<PERSON><PERSON><PERSON><PERSON>", "_", "getParentMenuItem", "collapseOther", "for<PERSON>ach", "el", "collapse", "some", "childItem", "includes", "ctorParameters", "type", "propDecorators", "args", "static", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-menu/swui-menu.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-menu.component.scss?ngResource\";\nimport { Component, Input, ViewChild } from '@angular/core';\nimport { NavigationEnd, Router } from '@angular/router';\nimport { NestedTreeControl } from '@angular/cdk/tree';\nimport { MatTreeNestedDataSource } from '@angular/material/tree';\nimport { Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nlet SwuiMenuComponent = class SwuiMenuComponent {\n    get isSidebarCollapsed() {\n        return this._isSidebarCollapsed;\n    }\n    set isSidebarCollapsed(value) {\n        this._isSidebarCollapsed = value;\n        if (!value && this.currentParent) {\n            this.treeControl.expand(this.currentParent);\n        }\n        else {\n            this.collapseAll();\n        }\n    }\n    get isSidebarHovered() {\n        return this._isSidebarHovered;\n    }\n    set isSidebarHovered(value) {\n        this._isSidebarHovered = value;\n        if (this.isSidebarCollapsed && !value) {\n            this.collapseAll();\n        }\n        else if (this.currentParent) {\n            this.treeControl.expand(this.currentParent);\n        }\n    }\n    set items(items) {\n        if (!items) {\n            return;\n        }\n        this.loading = false;\n        this.dataSource.data = items;\n        this.expandParentItem();\n    }\n    constructor(router) {\n        this.router = router;\n        this.loading = true;\n        this.loadingArray = Array;\n        this.treeControl = new NestedTreeControl(item => item.children);\n        this.dataSource = new MatTreeNestedDataSource();\n        this._isSidebarCollapsed = false;\n        this._isSidebarHovered = false;\n        this.destroy$ = new Subject();\n        this.router.events\n            .pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe((event) => {\n            const item = this.dataSource.data.find(({ url }) => event.url.indexOf(url) !== -1);\n            if (item && this.currentParent !== item) {\n                if (item.children?.length) {\n                    this.onParentClick(item);\n                }\n                else {\n                    this.onItemClick(item);\n                }\n                this.expandParentItem();\n            }\n        });\n    }\n    ngOnInit() {\n        if (this.menuRef) {\n            this.menuRef.nativeElement.style.setProperty('--active-color', this.activeColor || '#ffffff');\n        }\n    }\n    ngOnDestroy() {\n        if (this.menuRef) {\n            this.menuRef.nativeElement.style.removeProperty('--active-color');\n        }\n        this.destroy$.next(undefined);\n        this.destroy$.complete();\n    }\n    hasChild(_, item) {\n        return !!item.children && item.children.length > 0;\n    }\n    collapseAll() {\n        this.treeControl.collapseAll();\n    }\n    onItemClick(item) {\n        this.currentParent = this.getParentMenuItem(item.url);\n        this.collapseOther(this.currentParent);\n    }\n    onParentClick(item) {\n        this.treeControl.expand(item);\n        this.collapseOther(item);\n    }\n    collapseOther(item) {\n        this.dataSource.data.forEach((el) => {\n            if ((el !== item) && el.children) {\n                this.treeControl.collapse(el);\n            }\n        });\n    }\n    getParentMenuItem(url) {\n        return this.dataSource.data.find((item) => {\n            if (item.children && item.children.length > 0) {\n                return item.children.some((childItem) => url.includes(childItem.url));\n            }\n            else {\n                return item.url === url;\n            }\n        });\n    }\n    expandParentItem() {\n        this.currentParent = this.getParentMenuItem(this.router.url);\n        if (this.currentParent && !this.isSidebarCollapsed) {\n            this.treeControl.expand(this.currentParent);\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: Router }\n    ]; }\n    static { this.propDecorators = {\n        activeColor: [{ type: Input }],\n        isSidebarCollapsed: [{ type: Input }],\n        isSidebarHovered: [{ type: Input }],\n        items: [{ type: Input }],\n        menuRef: [{ type: ViewChild, args: ['menu', { static: true },] }]\n    }; }\n};\nSwuiMenuComponent = __decorate([\n    Component({\n        selector: 'lib-swui-menu',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiMenuComponent);\nexport { SwuiMenuComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,SAASC,SAAS,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AAC3D,SAASC,aAAa,EAAEC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAClD,IAAIC,iBAAiB,IAAAC,kBAAA,GAAG,MAAMD,iBAAiB,CAAC;EAC5C,IAAIE,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACC,mBAAmB;EACnC;EACA,IAAID,kBAAkBA,CAACE,KAAK,EAAE;IAC1B,IAAI,CAACD,mBAAmB,GAAGC,KAAK;IAChC,IAAI,CAACA,KAAK,IAAI,IAAI,CAACC,aAAa,EAAE;MAC9B,IAAI,CAACC,WAAW,CAACC,MAAM,CAAC,IAAI,CAACF,aAAa,CAAC;IAC/C,CAAC,MACI;MACD,IAAI,CAACG,WAAW,CAAC,CAAC;IACtB;EACJ;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,iBAAiB;EACjC;EACA,IAAID,gBAAgBA,CAACL,KAAK,EAAE;IACxB,IAAI,CAACM,iBAAiB,GAAGN,KAAK;IAC9B,IAAI,IAAI,CAACF,kBAAkB,IAAI,CAACE,KAAK,EAAE;MACnC,IAAI,CAACI,WAAW,CAAC,CAAC;IACtB,CAAC,MACI,IAAI,IAAI,CAACH,aAAa,EAAE;MACzB,IAAI,CAACC,WAAW,CAACC,MAAM,CAAC,IAAI,CAACF,aAAa,CAAC;IAC/C;EACJ;EACA,IAAIM,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,UAAU,CAACC,IAAI,GAAGH,KAAK;IAC5B,IAAI,CAACI,gBAAgB,CAAC,CAAC;EAC3B;EACAC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACL,OAAO,GAAG,IAAI;IACnB,IAAI,CAACM,YAAY,GAAGC,KAAK;IACzB,IAAI,CAACb,WAAW,GAAG,IAAIX,iBAAiB,CAACyB,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;IAC/D,IAAI,CAACR,UAAU,GAAG,IAAIjB,uBAAuB,CAAC,CAAC;IAC/C,IAAI,CAACO,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACO,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACY,QAAQ,GAAG,IAAIzB,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACoB,MAAM,CAACM,MAAM,CACbC,IAAI,CAAC1B,MAAM,CAAC2B,KAAK,IAAIA,KAAK,YAAYhC,aAAa,CAAC,EAAEM,SAAS,CAAC,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAACI,SAAS,CAAED,KAAK,IAAK;MACtG,MAAML,IAAI,GAAG,IAAI,CAACP,UAAU,CAACC,IAAI,CAACa,IAAI,CAAC,CAAC;QAAEC;MAAI,CAAC,KAAKH,KAAK,CAACG,GAAG,CAACC,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;MAClF,IAAIR,IAAI,IAAI,IAAI,CAACf,aAAa,KAAKe,IAAI,EAAE;QAAA,IAAAU,cAAA;QACrC,KAAAA,cAAA,GAAIV,IAAI,CAACC,QAAQ,cAAAS,cAAA,eAAbA,cAAA,CAAeC,MAAM,EAAE;UACvB,IAAI,CAACC,aAAa,CAACZ,IAAI,CAAC;QAC5B,CAAC,MACI;UACD,IAAI,CAACa,WAAW,CAACb,IAAI,CAAC;QAC1B;QACA,IAAI,CAACL,gBAAgB,CAAC,CAAC;MAC3B;IACJ,CAAC,CAAC;EACN;EACAmB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,aAAa,CAACC,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAACC,WAAW,IAAI,SAAS,CAAC;IACjG;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACL,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,aAAa,CAACC,KAAK,CAACI,cAAc,CAAC,gBAAgB,CAAC;IACrE;IACA,IAAI,CAACnB,QAAQ,CAACoB,IAAI,CAACC,SAAS,CAAC;IAC7B,IAAI,CAACrB,QAAQ,CAACsB,QAAQ,CAAC,CAAC;EAC5B;EACAC,QAAQA,CAACC,CAAC,EAAE1B,IAAI,EAAE;IACd,OAAO,CAAC,CAACA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,CAACU,MAAM,GAAG,CAAC;EACtD;EACAvB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,WAAW,CAACE,WAAW,CAAC,CAAC;EAClC;EACAyB,WAAWA,CAACb,IAAI,EAAE;IACd,IAAI,CAACf,aAAa,GAAG,IAAI,CAAC0C,iBAAiB,CAAC3B,IAAI,CAACQ,GAAG,CAAC;IACrD,IAAI,CAACoB,aAAa,CAAC,IAAI,CAAC3C,aAAa,CAAC;EAC1C;EACA2B,aAAaA,CAACZ,IAAI,EAAE;IAChB,IAAI,CAACd,WAAW,CAACC,MAAM,CAACa,IAAI,CAAC;IAC7B,IAAI,CAAC4B,aAAa,CAAC5B,IAAI,CAAC;EAC5B;EACA4B,aAAaA,CAAC5B,IAAI,EAAE;IAChB,IAAI,CAACP,UAAU,CAACC,IAAI,CAACmC,OAAO,CAAEC,EAAE,IAAK;MACjC,IAAKA,EAAE,KAAK9B,IAAI,IAAK8B,EAAE,CAAC7B,QAAQ,EAAE;QAC9B,IAAI,CAACf,WAAW,CAAC6C,QAAQ,CAACD,EAAE,CAAC;MACjC;IACJ,CAAC,CAAC;EACN;EACAH,iBAAiBA,CAACnB,GAAG,EAAE;IACnB,OAAO,IAAI,CAACf,UAAU,CAACC,IAAI,CAACa,IAAI,CAAEP,IAAI,IAAK;MACvC,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,CAACU,MAAM,GAAG,CAAC,EAAE;QAC3C,OAAOX,IAAI,CAACC,QAAQ,CAAC+B,IAAI,CAAEC,SAAS,IAAKzB,GAAG,CAAC0B,QAAQ,CAACD,SAAS,CAACzB,GAAG,CAAC,CAAC;MACzE,CAAC,MACI;QACD,OAAOR,IAAI,CAACQ,GAAG,KAAKA,GAAG;MAC3B;IACJ,CAAC,CAAC;EACN;EACAb,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACV,aAAa,GAAG,IAAI,CAAC0C,iBAAiB,CAAC,IAAI,CAAC9B,MAAM,CAACW,GAAG,CAAC;IAC5D,IAAI,IAAI,CAACvB,aAAa,IAAI,CAAC,IAAI,CAACH,kBAAkB,EAAE;MAChD,IAAI,CAACI,WAAW,CAACC,MAAM,CAAC,IAAI,CAACF,aAAa,CAAC;IAC/C;EACJ;AAWJ,CAAC,EAVYJ,kBAAA,CAAKsD,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE9D;AAAO,CAAC,CACnB,EACQO,kBAAA,CAAKwD,cAAc,GAAG;EAC3BlB,WAAW,EAAE,CAAC;IAAEiB,IAAI,EAAEjE;EAAM,CAAC,CAAC;EAC9BW,kBAAkB,EAAE,CAAC;IAAEsD,IAAI,EAAEjE;EAAM,CAAC,CAAC;EACrCkB,gBAAgB,EAAE,CAAC;IAAE+C,IAAI,EAAEjE;EAAM,CAAC,CAAC;EACnCoB,KAAK,EAAE,CAAC;IAAE6C,IAAI,EAAEjE;EAAM,CAAC,CAAC;EACxB4C,OAAO,EAAE,CAAC;IAAEqB,IAAI,EAAEhE,SAAS;IAAEkE,IAAI,EAAE,CAAC,MAAM,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC;AACpE,CAAC,EAAA1D,kBAAA,CACJ;AACDD,iBAAiB,GAAGb,UAAU,CAAC,CAC3BG,SAAS,CAAC;EACNsE,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAEzE,oBAAoB;EAC9B0E,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC1E,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEW,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}