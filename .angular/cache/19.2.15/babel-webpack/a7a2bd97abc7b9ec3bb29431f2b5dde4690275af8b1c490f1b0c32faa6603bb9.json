{"ast": null, "code": "var _SwuiNotificationsService;\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SwuiSnackbarComponent } from './swui-snackbar/swui-snackbar.component';\nconst DEFAULT_DURATION = 5000;\nconst DEFAULT_VERTICAL_POSITION = 'bottom';\nconst DEFAULT_HORIZONTAL_POSITION = 'left';\nconst DEFAULT_ERROR_MESSAGE = 'Oops, something went wrong. Please try again later.';\nlet SwuiNotificationsService = (_SwuiNotificationsService = class SwuiNotificationsService {\n  constructor(snackbar) {\n    this.snackbar = snackbar;\n  }\n  success(message, title, verticalPosition, horizontalPosition, duration) {\n    const data = {\n      message,\n      title,\n      verticalPosition,\n      horizontalPosition,\n      duration\n    };\n    return this.showSnackbar(data, 'swui-snackbar-success');\n  }\n  error(message, title, verticalPosition, horizontalPosition, duration) {\n    const data = {\n      message: message || DEFAULT_ERROR_MESSAGE,\n      title,\n      verticalPosition,\n      horizontalPosition,\n      duration\n    };\n    return this.showSnackbar(data, 'swui-snackbar-error');\n  }\n  warning(message, title, verticalPosition, horizontalPosition, duration) {\n    const data = {\n      message,\n      title,\n      verticalPosition,\n      horizontalPosition,\n      duration\n    };\n    return this.showSnackbar(data, 'swui-snackbar-warning');\n  }\n  showSnackbar(data, panelClass = 'swui-snackbar-success') {\n    const config = {\n      data,\n      panelClass,\n      duration: data.duration || DEFAULT_DURATION,\n      verticalPosition: data.verticalPosition || DEFAULT_VERTICAL_POSITION,\n      horizontalPosition: data.horizontalPosition || DEFAULT_HORIZONTAL_POSITION\n    };\n    return this.snackbar.openFromComponent(SwuiSnackbarComponent, config);\n  }\n}, _SwuiNotificationsService.ctorParameters = () => [{\n  type: MatSnackBar\n}], _SwuiNotificationsService);\nSwuiNotificationsService = __decorate([Injectable({\n  providedIn: 'root'\n})], SwuiNotificationsService);\nexport { SwuiNotificationsService };", "map": {"version": 3, "names": ["Injectable", "MatSnackBar", "SwuiSnackbarComponent", "DEFAULT_DURATION", "DEFAULT_VERTICAL_POSITION", "DEFAULT_HORIZONTAL_POSITION", "DEFAULT_ERROR_MESSAGE", "SwuiNotificationsService", "_SwuiNotificationsService", "constructor", "snackbar", "success", "message", "title", "verticalPosition", "horizontalPosition", "duration", "data", "showSnackbar", "error", "warning", "panelClass", "config", "openFromComponent", "__decorate", "providedIn"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-notifications/swui-notifications.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { MatSnackBar, MatSnackBarConfig, MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition } from '@angular/material/snack-bar';\n\nimport { SwuiSnackbarComponent } from './swui-snackbar/swui-snackbar.component';\n\nexport interface NotificationsMessage {\n  message: string;\n  title?: string;\n  verticalPosition?: MatSnackBarVerticalPosition;\n  horizontalPosition?: MatSnackBarHorizontalPosition;\n  duration?: number;\n}\n\nconst DEFAULT_DURATION = 5000;\nconst DEFAULT_VERTICAL_POSITION = 'bottom';\nconst DEFAULT_HORIZONTAL_POSITION = 'left';\nconst DEFAULT_ERROR_MESSAGE = 'Oops, something went wrong. Please try again later.';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SwuiNotificationsService {\n\n  constructor(\n    private snackbar: MatSnackBar,\n  ) {\n  }\n\n  success( message: string, title?: string, verticalPosition?: MatSnackBarVerticalPosition,\n           horizontalPosition?: MatSnackBarHorizontalPosition, duration?: number\n  ) {\n    const data = { message, title, verticalPosition, horizontalPosition, duration };\n    return this.showSnackbar(data, 'swui-snackbar-success');\n  }\n\n  error( message: string, title?: string, verticalPosition?: MatSnackBarVerticalPosition,\n         horizontalPosition?: MatSnackBarHorizontalPosition, duration?: number\n  ) {\n    const data = {\n      message: message || DEFAULT_ERROR_MESSAGE,\n      title,\n      verticalPosition,\n      horizontalPosition,\n      duration\n    };\n    return this.showSnackbar(data, 'swui-snackbar-error');\n  }\n\n  warning( message: string, title?: string, verticalPosition?: MatSnackBarVerticalPosition,\n           horizontalPosition?: MatSnackBarHorizontalPosition, duration?: number\n  ) {\n    const data = { message, title, verticalPosition, horizontalPosition, duration };\n    return this.showSnackbar(data, 'swui-snackbar-warning');\n  }\n\n  showSnackbar( data: NotificationsMessage, panelClass = 'swui-snackbar-success' ) {\n    const config: MatSnackBarConfig = {\n      data,\n      panelClass,\n      duration: data.duration || DEFAULT_DURATION,\n      verticalPosition: data.verticalPosition || DEFAULT_VERTICAL_POSITION,\n      horizontalPosition: data.horizontalPosition || DEFAULT_HORIZONTAL_POSITION,\n    };\n\n    return this.snackbar.openFromComponent(SwuiSnackbarComponent, config);\n  }\n}\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAuF,6BAA6B;AAExI,SAASC,qBAAqB,QAAQ,yCAAyC;AAU/E,MAAMC,gBAAgB,GAAG,IAAI;AAC7B,MAAMC,yBAAyB,GAAG,QAAQ;AAC1C,MAAMC,2BAA2B,GAAG,MAAM;AAC1C,MAAMC,qBAAqB,GAAG,qDAAqD;AAK5E,IAAMC,wBAAwB,IAAAC,yBAAA,GAA9B,MAAMD,wBAAwB;EAEnCE,YACUC,QAAqB;IAArB,KAAAA,QAAQ,GAARA,QAAQ;EAElB;EAEAC,OAAOA,CAAEC,OAAe,EAAEC,KAAc,EAAEC,gBAA8C,EAC/EC,kBAAkD,EAAEC,QAAiB;IAE5E,MAAMC,IAAI,GAAG;MAAEL,OAAO;MAAEC,KAAK;MAAEC,gBAAgB;MAAEC,kBAAkB;MAAEC;IAAQ,CAAE;IAC/E,OAAO,IAAI,CAACE,YAAY,CAACD,IAAI,EAAE,uBAAuB,CAAC;EACzD;EAEAE,KAAKA,CAAEP,OAAe,EAAEC,KAAc,EAAEC,gBAA8C,EAC/EC,kBAAkD,EAAEC,QAAiB;IAE1E,MAAMC,IAAI,GAAG;MACXL,OAAO,EAAEA,OAAO,IAAIN,qBAAqB;MACzCO,KAAK;MACLC,gBAAgB;MAChBC,kBAAkB;MAClBC;KACD;IACD,OAAO,IAAI,CAACE,YAAY,CAACD,IAAI,EAAE,qBAAqB,CAAC;EACvD;EAEAG,OAAOA,CAAER,OAAe,EAAEC,KAAc,EAAEC,gBAA8C,EAC/EC,kBAAkD,EAAEC,QAAiB;IAE5E,MAAMC,IAAI,GAAG;MAAEL,OAAO;MAAEC,KAAK;MAAEC,gBAAgB;MAAEC,kBAAkB;MAAEC;IAAQ,CAAE;IAC/E,OAAO,IAAI,CAACE,YAAY,CAACD,IAAI,EAAE,uBAAuB,CAAC;EACzD;EAEAC,YAAYA,CAAED,IAA0B,EAAEI,UAAU,GAAG,uBAAuB;IAC5E,MAAMC,MAAM,GAAsB;MAChCL,IAAI;MACJI,UAAU;MACVL,QAAQ,EAAEC,IAAI,CAACD,QAAQ,IAAIb,gBAAgB;MAC3CW,gBAAgB,EAAEG,IAAI,CAACH,gBAAgB,IAAIV,yBAAyB;MACpEW,kBAAkB,EAAEE,IAAI,CAACF,kBAAkB,IAAIV;KAChD;IAED,OAAO,IAAI,CAACK,QAAQ,CAACa,iBAAiB,CAACrB,qBAAqB,EAAEoB,MAAM,CAAC;EACvE;;;;AA5CWf,wBAAwB,GAAAiB,UAAA,EAHpCxB,UAAU,CAAC;EACVyB,UAAU,EAAE;CACb,CAAC,C,EACWlB,wBAAwB,CA6CpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}