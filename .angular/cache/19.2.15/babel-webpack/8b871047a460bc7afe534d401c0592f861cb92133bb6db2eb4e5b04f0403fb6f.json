{"ast": null, "code": "var _EntityPickerComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./entity-picker.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./entity-picker.component.scss?ngResource\";\nimport { ChangeDetectorRef, Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { UntypedFormControl } from '@angular/forms';\nimport { combineLatest, Subject, timer } from 'rxjs';\nimport { map, share, take, takeUntil } from 'rxjs/operators';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubEntityService } from '../../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwHubInitService } from '../../services/sw-hub-init/sw-hub-init.service';\nlet EntityPickerComponent = (_EntityPickerComponent = class EntityPickerComponent {\n  constructor(entityService, hubService, cdr, authService) {\n    this.entityService = entityService;\n    this.hubService = hubService;\n    this.cdr = cdr;\n    this.authService = authService;\n    this.showSearch = false;\n    this.menuClass = 'entity-picker-menu';\n    this.searchPlaceholder = 'Search';\n    this.settingsClick = new EventEmitter();\n    this.entities = [];\n    this.searchInputControl = new UntypedFormControl();\n    this.isSettingsDisabled = true;\n    this.destroyed$ = new Subject();\n    this.items$ = this.entityService.items$.pipe(takeUntil(this.destroyed$), share());\n    this.items$.subscribe(() => {\n      if (Array.isArray(this.entityService.entities) && this.entityService.entities.length) {\n        this.entityService.expandedEntities.clear();\n        this.toggleCollapse(this.entityService.entities[0].id);\n      }\n    });\n  }\n  ngOnInit() {\n    this.searchInputControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(searchString => {\n      if (searchString) {\n        var _this$entities;\n        this.entities = this.entityService.entities.filter(option => {\n          return option.name && option.name.toLowerCase().indexOf(searchString.toLowerCase()) > -1;\n        });\n        this.entityService.expandedEntities.clear();\n        (_this$entities = this.entities) === null || _this$entities === void 0 || _this$entities.forEach(entity => this.expandToRoot(entity.id));\n        this.entities = this.entityService.entities.filter(item => this.entityService.expandedEntities.get(item.id));\n        this.entityService.foundedEntities = this.entities;\n      } else {\n        this.entityService.expandedEntities.clear();\n        this.toggleCollapse(this.entityService.entities[0].id);\n      }\n    });\n    this.entityService.brief$.pipe(takeUntil(this.destroyed$)).subscribe(item => {\n      this.entity = item;\n      this.cdr.detectChanges();\n    });\n    combineLatest([this.entityService.brief$, this.entityService.itemSelected$]).pipe(map(([brief, item]) => item || brief), takeUntil(this.destroyed$)).subscribe(item => {\n      var _this$selected;\n      if (item && item.id !== ((_this$selected = this.selected) === null || _this$selected === void 0 ? void 0 : _this$selected.id)) {\n        this.hubService.sendEntityId(item.id);\n      }\n      this.selected = item;\n      this.isSettingsDisabled = (item === null || item === void 0 ? void 0 : item.path) === ':' && this.authService.isSuperAdmin;\n      this.cdr.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  get isReseller() {\n    if (!this.entity) {\n      return false;\n    }\n    return this.entity.type === 'entity';\n  }\n  onSettingsClick(event) {\n    var _this$selected2;\n    if (event) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n    this.settingsClick.emit((_this$selected2 = this.selected) === null || _this$selected2 === void 0 ? void 0 : _this$selected2.path);\n  }\n  select(item) {\n    var _this$pickerTrigger;\n    if (item !== undefined) {\n      this.hubService.sendEntityId(item.id);\n      this.searchInputControl.setValue('');\n      this.entityService.use(item.id, true);\n    }\n    (_this$pickerTrigger = this.pickerTrigger) === null || _this$pickerTrigger === void 0 || _this$pickerTrigger.closeMenu();\n  }\n  stopPropagation(event) {\n    event.stopPropagation();\n  }\n  closeChildren(current) {\n    this.entityService.expandedEntities.set(current, false);\n    if (this.entityService.entitiesObject) {\n      const {\n        children\n      } = this.entityService.entitiesObject[current];\n      children.forEach(id => {\n        this.closeChildren(id);\n      });\n    }\n  }\n  expandToRoot(id) {\n    if (id) {\n      this.entityService.expandedEntities.set(id, true);\n      if (this.entityService.entitiesObject) {\n        return this.expandToRoot(this.entityService.entitiesObject[id].parentId);\n      }\n    }\n  }\n  toggleCollapse(id) {\n    const isCollapsed = !this.entityService.expandedEntities.get(id);\n    if (isCollapsed) {\n      this.entityService.expandedEntities.set(id, true);\n    } else {\n      this.closeChildren(id);\n    }\n    this.entities = this.searchInputControl.value ? this.entityService.foundedEntities.filter((item, index) => {\n      return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n    }) : this.entityService.entities.filter((item, index) => {\n      return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n    });\n  }\n  onItemClick(event, row) {\n    this.stopPropagation(event);\n    if (row.level !== 0) {\n      this.toggleCollapse(row.id);\n    }\n  }\n  onClosed() {\n    this.searchInputControl.setValue(null);\n  }\n  isEntityExpanded(id) {\n    return !!this.entityService.expandedEntities.get(id);\n  }\n  onOpen() {\n    var _this$selected3;\n    if ((_this$selected3 = this.selected) !== null && _this$selected3 !== void 0 && _this$selected3.id) {\n      this.expandToRoot(this.selected.id);\n      this.entities = this.entityService.entities.filter((item, index) => {\n        return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n      });\n      timer(100).pipe(take(1)).subscribe(() => {\n        var _this$selected4;\n        if ((_this$selected4 = this.selected) !== null && _this$selected4 !== void 0 && _this$selected4.id) {\n          const el = document.getElementById(this.selected.id);\n          el === null || el === void 0 || el.scrollIntoView({\n            block: 'center'\n          });\n        }\n      });\n    }\n  }\n}, _EntityPickerComponent.ctorParameters = () => [{\n  type: SwHubEntityService\n}, {\n  type: SwHubInitService\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: SwHubAuthService\n}], _EntityPickerComponent.propDecorators = {\n  showSearch: [{\n    type: Input\n  }],\n  menuClass: [{\n    type: Input\n  }],\n  searchPlaceholder: [{\n    type: Input\n  }],\n  settingsClick: [{\n    type: Output\n  }],\n  pickerTrigger: [{\n    type: ViewChild,\n    args: ['pickerTrigger']\n  }],\n  searchRef: [{\n    type: ViewChild,\n    args: ['search']\n  }]\n}, _EntityPickerComponent);\nEntityPickerComponent = __decorate([Component({\n  selector: 'lib-swui-entity-picker',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], EntityPickerComponent);\nexport { EntityPickerComponent };", "map": {"version": 3, "names": ["ChangeDetectorRef", "Component", "EventEmitter", "Input", "Output", "ViewChild", "UntypedFormControl", "combineLatest", "Subject", "timer", "map", "share", "take", "takeUntil", "SwHubAuthService", "SwHubEntityService", "SwHubInitService", "EntityPickerComponent", "_EntityPickerComponent", "constructor", "entityService", "hubService", "cdr", "authService", "showSearch", "menuClass", "searchPlaceholder", "settingsClick", "entities", "searchInputControl", "isSettingsDisabled", "destroyed$", "items$", "pipe", "subscribe", "Array", "isArray", "length", "expandedEntities", "clear", "toggleCollapse", "id", "ngOnInit", "valueChanges", "searchString", "_this$entities", "filter", "option", "name", "toLowerCase", "indexOf", "for<PERSON>ach", "entity", "expandToRoot", "item", "get", "foundedEntities", "brief$", "detectChanges", "itemSelected$", "brief", "_this$selected", "selected", "sendEntityId", "path", "isSuperAdmin", "ngOnDestroy", "next", "undefined", "complete", "is<PERSON><PERSON>ller", "type", "onSettingsClick", "event", "_this$selected2", "preventDefault", "stopPropagation", "emit", "select", "_this$pickerTrigger", "setValue", "use", "picker<PERSON>rigger", "closeMenu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "set", "entitiesObject", "children", "parentId", "isCollapsed", "value", "index", "onItemClick", "row", "level", "onClosed", "isEntityExpanded", "onOpen", "_this$selected3", "_this$selected4", "el", "document", "getElementById", "scrollIntoView", "block", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/entity-picker/entity-picker.component.ts"], "sourcesContent": ["import {\n  ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild\n} from '@angular/core';\nimport { UntypedFormControl } from '@angular/forms';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { combineLatest, Observable, Subject, timer } from 'rxjs';\nimport { map, share, take, takeUntil } from 'rxjs/operators';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubBriefEntity, SwHubEntityItem } from '../../services/sw-hub-entity/sw-hub-entity.model';\nimport { ExtendedEntity, SwHubEntityService } from '../../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwHubInitService } from '../../services/sw-hub-init/sw-hub-init.service';\n\n@Component({\n    selector: 'lib-swui-entity-picker',\n    templateUrl: './entity-picker.component.html',\n    styleUrls: ['./entity-picker.component.scss'],\n    standalone: false\n})\nexport class EntityPickerComponent implements OnInit, OnDestroy {\n  @Input() showSearch = false;\n  @Input() menuClass = 'entity-picker-menu';\n  @Input() searchPlaceholder = 'Search';\n  @Output() settingsClick = new EventEmitter<string>();\n\n  entity?: SwHubBriefEntity | null;\n  selected?: { name?: string, id?: string, path?: string } | null;\n  items$: Observable<SwHubEntityItem[]> | undefined;\n  entities: ExtendedEntity[] = [];\n  searchInputControl = new UntypedFormControl();\n  isSettingsDisabled = true;\n\n  @ViewChild('pickerTrigger') pickerTrigger?: MatMenuTrigger;\n  @ViewChild('search') searchRef?: ElementRef;\n\n  private readonly destroyed$ = new Subject<void>();\n\n  constructor( private readonly entityService: SwHubEntityService,\n               private readonly hubService: SwHubInitService,\n               private readonly cdr: ChangeDetectorRef,\n               private readonly authService: SwHubAuthService\n  ) {\n    this.items$ = this.entityService.items$.pipe(\n      takeUntil(this.destroyed$),\n      share(),\n    );\n\n    this.items$.subscribe(() => {\n      if (Array.isArray(this.entityService.entities) && this.entityService.entities.length) {\n        this.entityService.expandedEntities.clear();\n        this.toggleCollapse(this.entityService.entities[0].id);\n      }\n    });\n  }\n\n  ngOnInit(): void {\n    this.searchInputControl.valueChanges.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(\n      searchString => {\n        if (searchString) {\n          this.entities = this.entityService.entities.filter(( option: SwHubEntityItem ) => {\n            return option.name && option.name.toLowerCase().indexOf(searchString.toLowerCase()) > -1;\n          });\n\n          this.entityService.expandedEntities.clear();\n          this.entities?.forEach(entity => this.expandToRoot(entity.id));\n          this.entities = this.entityService.entities.filter(item => this.entityService.expandedEntities.get(item.id));\n          this.entityService.foundedEntities = this.entities;\n        } else {\n          this.entityService.expandedEntities.clear();\n          this.toggleCollapse(this.entityService.entities[0].id);\n        }\n      }\n    );\n\n    this.entityService.brief$.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(item => {\n      this.entity = item;\n      this.cdr.detectChanges();\n    });\n\n    combineLatest([this.entityService.brief$, this.entityService.itemSelected$]).pipe(\n      map(( [brief, item] ) => item || brief),\n      takeUntil(this.destroyed$)\n    ).subscribe(item => {\n      if (item && item.id !== this.selected?.id) {\n        this.hubService.sendEntityId(item.id as string);\n      }\n\n      this.selected = item;\n      this.isSettingsDisabled = item?.path === ':' && this.authService.isSuperAdmin;\n      this.cdr.detectChanges();\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n\n  get isReseller(): boolean {\n    if (!this.entity) {\n      return false;\n    }\n    return this.entity.type === 'entity';\n  }\n\n  onSettingsClick( event: MouseEvent ) {\n    if (event) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n\n    this.settingsClick.emit(this.selected?.path);\n  }\n\n  select( item: SwHubEntityItem | undefined ): void {\n    if (item !== undefined) {\n      this.hubService.sendEntityId(item.id);\n      this.searchInputControl.setValue('');\n      this.entityService.use(item.id, true);\n    }\n\n    this.pickerTrigger?.closeMenu();\n  }\n\n  stopPropagation( event: Event ) {\n    event.stopPropagation();\n  }\n\n  closeChildren( current: string ) {\n    this.entityService.expandedEntities.set(current, false);\n\n    if (this.entityService.entitiesObject) {\n      const { children } = this.entityService.entitiesObject[current];\n\n      children.forEach(( id: string ) => {\n        this.closeChildren(id);\n      });\n    }\n  }\n\n  expandToRoot( id: string ): any {\n    if (id) {\n      this.entityService.expandedEntities.set(id, true);\n\n      if (this.entityService.entitiesObject) {\n        return this.expandToRoot(this.entityService.entitiesObject[id].parentId);\n      }\n    }\n  }\n\n  toggleCollapse( id: string ) {\n    const isCollapsed = !this.entityService.expandedEntities.get(id);\n\n    if (isCollapsed) {\n      this.entityService.expandedEntities.set(id, true);\n    } else {\n      this.closeChildren(id);\n    }\n\n    this.entities = this.searchInputControl.value ?\n      this.entityService.foundedEntities.filter(( item: any, index: number ) => {\n        return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n      }) :\n      this.entityService.entities.filter(( item: any, index: number ) => {\n        return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n      });\n  }\n\n  onItemClick( event: Event, row: ExtendedEntity ) {\n    this.stopPropagation(event);\n\n    if (row.level !== 0) {\n      this.toggleCollapse(row.id);\n    }\n  }\n\n  onClosed() {\n    this.searchInputControl.setValue(null);\n  }\n\n  isEntityExpanded( id: string ): boolean {\n    return !!this.entityService.expandedEntities.get(id);\n  }\n\n  onOpen() {\n    if (this.selected?.id) {\n      this.expandToRoot(this.selected.id);\n      this.entities = this.entityService.entities.filter(( item: any, index: number ) => {\n        return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n      });\n      timer(100)\n        .pipe(take(1))\n        .subscribe(() => {\n          if (this.selected?.id) {\n            const el = document.getElementById(this.selected.id);\n            el?.scrollIntoView({ block: 'center' });\n          }\n        });\n    }\n  }\n}\n"], "mappings": ";;;;AAAA,SACEA,iBAAiB,EAAEC,SAAS,EAAcC,YAAY,EAAEC,KAAK,EAAqBC,MAAM,EAAEC,SAAS,QAC9F,eAAe;AACtB,SAASC,kBAAkB,QAAQ,gBAAgB;AAEnD,SAASC,aAAa,EAAcC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AAChE,SAASC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAC5D,SAASC,gBAAgB,QAAQ,gDAAgD;AAEjF,SAAyBC,kBAAkB,QAAQ,oDAAoD;AACvG,SAASC,gBAAgB,QAAQ,gDAAgD;AAQ1E,IAAMC,qBAAqB,IAAAC,sBAAA,GAA3B,MAAMD,qBAAqB;EAkBhCE,YAA8BC,aAAiC,EACjCC,UAA4B,EAC5BC,GAAsB,EACtBC,WAA6B;IAH7B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,WAAW,GAAXA,WAAW;IApBhC,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,oBAAoB;IAChC,KAAAC,iBAAiB,GAAG,QAAQ;IAC3B,KAAAC,aAAa,GAAG,IAAIzB,YAAY,EAAU;IAKpD,KAAA0B,QAAQ,GAAqB,EAAE;IAC/B,KAAAC,kBAAkB,GAAG,IAAIvB,kBAAkB,EAAE;IAC7C,KAAAwB,kBAAkB,GAAG,IAAI;IAKR,KAAAC,UAAU,GAAG,IAAIvB,OAAO,EAAQ;IAO/C,IAAI,CAACwB,MAAM,GAAG,IAAI,CAACZ,aAAa,CAACY,MAAM,CAACC,IAAI,CAC1CpB,SAAS,CAAC,IAAI,CAACkB,UAAU,CAAC,EAC1BpB,KAAK,EAAE,CACR;IAED,IAAI,CAACqB,MAAM,CAACE,SAAS,CAAC,MAAK;MACzB,IAAIC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAChB,aAAa,CAACQ,QAAQ,CAAC,IAAI,IAAI,CAACR,aAAa,CAACQ,QAAQ,CAACS,MAAM,EAAE;QACpF,IAAI,CAACjB,aAAa,CAACkB,gBAAgB,CAACC,KAAK,EAAE;QAC3C,IAAI,CAACC,cAAc,CAAC,IAAI,CAACpB,aAAa,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACa,EAAE,CAAC;MACxD;IACF,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACb,kBAAkB,CAACc,YAAY,CAACV,IAAI,CACvCpB,SAAS,CAAC,IAAI,CAACkB,UAAU,CAAC,CAC3B,CAACG,SAAS,CACTU,YAAY,IAAG;MACb,IAAIA,YAAY,EAAE;QAAA,IAAAC,cAAA;QAChB,IAAI,CAACjB,QAAQ,GAAG,IAAI,CAACR,aAAa,CAACQ,QAAQ,CAACkB,MAAM,CAAGC,MAAuB,IAAK;UAC/E,OAAOA,MAAM,CAACC,IAAI,IAAID,MAAM,CAACC,IAAI,CAACC,WAAW,EAAE,CAACC,OAAO,CAACN,YAAY,CAACK,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC;QAC1F,CAAC,CAAC;QAEF,IAAI,CAAC7B,aAAa,CAACkB,gBAAgB,CAACC,KAAK,EAAE;QAC3C,CAAAM,cAAA,OAAI,CAACjB,QAAQ,cAAAiB,cAAA,eAAbA,cAAA,CAAeM,OAAO,CAACC,MAAM,IAAI,IAAI,CAACC,YAAY,CAACD,MAAM,CAACX,EAAE,CAAC,CAAC;QAC9D,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACR,aAAa,CAACQ,QAAQ,CAACkB,MAAM,CAACQ,IAAI,IAAI,IAAI,CAAClC,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACb,EAAE,CAAC,CAAC;QAC5G,IAAI,CAACrB,aAAa,CAACoC,eAAe,GAAG,IAAI,CAAC5B,QAAQ;MACpD,CAAC,MAAM;QACL,IAAI,CAACR,aAAa,CAACkB,gBAAgB,CAACC,KAAK,EAAE;QAC3C,IAAI,CAACC,cAAc,CAAC,IAAI,CAACpB,aAAa,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACa,EAAE,CAAC;MACxD;IACF,CAAC,CACF;IAED,IAAI,CAACrB,aAAa,CAACqC,MAAM,CAACxB,IAAI,CAC5BpB,SAAS,CAAC,IAAI,CAACkB,UAAU,CAAC,CAC3B,CAACG,SAAS,CAACoB,IAAI,IAAG;MACjB,IAAI,CAACF,MAAM,GAAGE,IAAI;MAClB,IAAI,CAAChC,GAAG,CAACoC,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEFnD,aAAa,CAAC,CAAC,IAAI,CAACa,aAAa,CAACqC,MAAM,EAAE,IAAI,CAACrC,aAAa,CAACuC,aAAa,CAAC,CAAC,CAAC1B,IAAI,CAC/EvB,GAAG,CAAC,CAAE,CAACkD,KAAK,EAAEN,IAAI,CAAC,KAAMA,IAAI,IAAIM,KAAK,CAAC,EACvC/C,SAAS,CAAC,IAAI,CAACkB,UAAU,CAAC,CAC3B,CAACG,SAAS,CAACoB,IAAI,IAAG;MAAA,IAAAO,cAAA;MACjB,IAAIP,IAAI,IAAIA,IAAI,CAACb,EAAE,OAAAoB,cAAA,GAAK,IAAI,CAACC,QAAQ,cAAAD,cAAA,uBAAbA,cAAA,CAAepB,EAAE,GAAE;QACzC,IAAI,CAACpB,UAAU,CAAC0C,YAAY,CAACT,IAAI,CAACb,EAAY,CAAC;MACjD;MAEA,IAAI,CAACqB,QAAQ,GAAGR,IAAI;MACpB,IAAI,CAACxB,kBAAkB,GAAG,CAAAwB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,MAAK,GAAG,IAAI,IAAI,CAACzC,WAAW,CAAC0C,YAAY;MAC7E,IAAI,CAAC3C,GAAG,CAACoC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAQ,WAAWA,CAAA;IACT,IAAI,CAACnC,UAAU,CAACoC,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACrC,UAAU,CAACsC,QAAQ,EAAE;EAC5B;EAEA,IAAIC,UAAUA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAClB,MAAM,EAAE;MAChB,OAAO,KAAK;IACd;IACA,OAAO,IAAI,CAACA,MAAM,CAACmB,IAAI,KAAK,QAAQ;EACtC;EAEAC,eAAeA,CAAEC,KAAiB;IAAA,IAAAC,eAAA;IAChC,IAAID,KAAK,EAAE;MACTA,KAAK,CAACE,cAAc,EAAE;MACtBF,KAAK,CAACG,eAAe,EAAE;IACzB;IAEA,IAAI,CAACjD,aAAa,CAACkD,IAAI,EAAAH,eAAA,GAAC,IAAI,CAACZ,QAAQ,cAAAY,eAAA,uBAAbA,eAAA,CAAeV,IAAI,CAAC;EAC9C;EAEAc,MAAMA,CAAExB,IAAiC;IAAA,IAAAyB,mBAAA;IACvC,IAAIzB,IAAI,KAAKc,SAAS,EAAE;MACtB,IAAI,CAAC/C,UAAU,CAAC0C,YAAY,CAACT,IAAI,CAACb,EAAE,CAAC;MACrC,IAAI,CAACZ,kBAAkB,CAACmD,QAAQ,CAAC,EAAE,CAAC;MACpC,IAAI,CAAC5D,aAAa,CAAC6D,GAAG,CAAC3B,IAAI,CAACb,EAAE,EAAE,IAAI,CAAC;IACvC;IAEA,CAAAsC,mBAAA,OAAI,CAACG,aAAa,cAAAH,mBAAA,eAAlBA,mBAAA,CAAoBI,SAAS,EAAE;EACjC;EAEAP,eAAeA,CAAEH,KAAY;IAC3BA,KAAK,CAACG,eAAe,EAAE;EACzB;EAEAQ,aAAaA,CAAEC,OAAe;IAC5B,IAAI,CAACjE,aAAa,CAACkB,gBAAgB,CAACgD,GAAG,CAACD,OAAO,EAAE,KAAK,CAAC;IAEvD,IAAI,IAAI,CAACjE,aAAa,CAACmE,cAAc,EAAE;MACrC,MAAM;QAAEC;MAAQ,CAAE,GAAG,IAAI,CAACpE,aAAa,CAACmE,cAAc,CAACF,OAAO,CAAC;MAE/DG,QAAQ,CAACrC,OAAO,CAAGV,EAAU,IAAK;QAChC,IAAI,CAAC2C,aAAa,CAAC3C,EAAE,CAAC;MACxB,CAAC,CAAC;IACJ;EACF;EAEAY,YAAYA,CAAEZ,EAAU;IACtB,IAAIA,EAAE,EAAE;MACN,IAAI,CAACrB,aAAa,CAACkB,gBAAgB,CAACgD,GAAG,CAAC7C,EAAE,EAAE,IAAI,CAAC;MAEjD,IAAI,IAAI,CAACrB,aAAa,CAACmE,cAAc,EAAE;QACrC,OAAO,IAAI,CAAClC,YAAY,CAAC,IAAI,CAACjC,aAAa,CAACmE,cAAc,CAAC9C,EAAE,CAAC,CAACgD,QAAQ,CAAC;MAC1E;IACF;EACF;EAEAjD,cAAcA,CAAEC,EAAU;IACxB,MAAMiD,WAAW,GAAG,CAAC,IAAI,CAACtE,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACd,EAAE,CAAC;IAEhE,IAAIiD,WAAW,EAAE;MACf,IAAI,CAACtE,aAAa,CAACkB,gBAAgB,CAACgD,GAAG,CAAC7C,EAAE,EAAE,IAAI,CAAC;IACnD,CAAC,MAAM;MACL,IAAI,CAAC2C,aAAa,CAAC3C,EAAE,CAAC;IACxB;IAEA,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAAC8D,KAAK,GAC3C,IAAI,CAACvE,aAAa,CAACoC,eAAe,CAACV,MAAM,CAAC,CAAEQ,IAAS,EAAEsC,KAAa,KAAK;MACvE,OAAO,IAAI,CAACxE,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACb,EAAE,CAAC,IAAI,IAAI,CAACrB,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAACG,KAAK;IAC7H,CAAC,CAAC,GACF,IAAI,CAACxE,aAAa,CAACQ,QAAQ,CAACkB,MAAM,CAAC,CAAEQ,IAAS,EAAEsC,KAAa,KAAK;MAChE,OAAO,IAAI,CAACxE,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACb,EAAE,CAAC,IAAI,IAAI,CAACrB,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAACG,KAAK;IAC7H,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAEpB,KAAY,EAAEqB,GAAmB;IAC5C,IAAI,CAAClB,eAAe,CAACH,KAAK,CAAC;IAE3B,IAAIqB,GAAG,CAACC,KAAK,KAAK,CAAC,EAAE;MACnB,IAAI,CAACvD,cAAc,CAACsD,GAAG,CAACrD,EAAE,CAAC;IAC7B;EACF;EAEAuD,QAAQA,CAAA;IACN,IAAI,CAACnE,kBAAkB,CAACmD,QAAQ,CAAC,IAAI,CAAC;EACxC;EAEAiB,gBAAgBA,CAAExD,EAAU;IAC1B,OAAO,CAAC,CAAC,IAAI,CAACrB,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACd,EAAE,CAAC;EACtD;EAEAyD,MAAMA,CAAA;IAAA,IAAAC,eAAA;IACJ,KAAAA,eAAA,GAAI,IAAI,CAACrC,QAAQ,cAAAqC,eAAA,eAAbA,eAAA,CAAe1D,EAAE,EAAE;MACrB,IAAI,CAACY,YAAY,CAAC,IAAI,CAACS,QAAQ,CAACrB,EAAE,CAAC;MACnC,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACR,aAAa,CAACQ,QAAQ,CAACkB,MAAM,CAAC,CAAEQ,IAAS,EAAEsC,KAAa,KAAK;QAChF,OAAO,IAAI,CAACxE,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACb,EAAE,CAAC,IAAI,IAAI,CAACrB,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAACG,KAAK;MAC7H,CAAC,CAAC;MACFnF,KAAK,CAAC,GAAG,CAAC,CACPwB,IAAI,CAACrB,IAAI,CAAC,CAAC,CAAC,CAAC,CACbsB,SAAS,CAAC,MAAK;QAAA,IAAAkE,eAAA;QACd,KAAAA,eAAA,GAAI,IAAI,CAACtC,QAAQ,cAAAsC,eAAA,eAAbA,eAAA,CAAe3D,EAAE,EAAE;UACrB,MAAM4D,EAAE,GAAGC,QAAQ,CAACC,cAAc,CAAC,IAAI,CAACzC,QAAQ,CAACrB,EAAE,CAAC;UACpD4D,EAAE,aAAFA,EAAE,eAAFA,EAAE,CAAEG,cAAc,CAAC;YAAEC,KAAK,EAAE;UAAQ,CAAE,CAAC;QACzC;MACF,CAAC,CAAC;IACN;EACF;;;;;;;;;;;UAvLCtG;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLC;EAAM;;UASNC,SAAS;IAAAqG,IAAA,GAAC,eAAe;EAAA;;UACzBrG,SAAS;IAAAqG,IAAA,GAAC,QAAQ;EAAA;;AAdRzF,qBAAqB,GAAA0F,UAAA,EANjC1G,SAAS,CAAC;EACP2G,QAAQ,EAAE,wBAAwB;EAClCC,QAAA,EAAAC,oBAA6C;EAE7CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACW9F,qBAAqB,CAyLjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}