{"ast": null, "code": "import { <PERSON><PERSON><PERSON>e, ChangeDetectorRef, DebugElement, ElementRef, NO_ERRORS_SCHEMA, Component, NgModule, Directive, Input, HostListener, NgZone, EventEmitter } from '@angular/core';\nimport { Observable, ReplaySubject, Subject } from 'rxjs';\nimport { TestBed, tick, async } from '@angular/core/testing';\nimport { By } from '@angular/platform-browser';\nimport { queries, getDefaultNormalizer } from '@testing-library/dom';\nimport { BrowserDynamicTestingModule } from '@angular/platform-browser-dynamic/testing';\nimport $ from 'jquery';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { Router, ActivatedRoute, convertToParamMap, ActivatedRouteSnapshot, RouterLink } from '@angular/router';\nimport { map } from 'rxjs/operators';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { HttpClient } from '@angular/common/http';\nimport { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nclass DOMSelector {\n  // Wrap selector functions in a class to make reflection easier in getChild\n  constructor(execute) {\n    this.execute = execute;\n  }\n}\nconst byLabel = (matcher, options) => new DOMSelector(el => queries.queryAllByLabelText(el, matcher, options));\nconst byPlaceholder = (matcher, options) => new DOMSelector(el => queries.queryAllByPlaceholderText(el, matcher, options));\nconst byText = (matcher, options) => new DOMSelector(el => queries.queryAllByText(el, matcher, options));\nconst byTextContent = (matcher, options) => {\n  let textContentMatcher;\n  const normalizer = (options === null || options === void 0 ? void 0 : options.normalizer) || getDefaultNormalizer(options);\n  const getTextContent = elem => {\n    var _a;\n    return normalizer((_a = elem.textContent) !== null && _a !== void 0 ? _a : '');\n  };\n  if (typeof matcher === 'string') {\n    textContentMatcher = (_, elem) => {\n      if ((options === null || options === void 0 ? void 0 : options.exact) === false) {\n        return getTextContent(elem).toLowerCase().indexOf(matcher.toLowerCase()) >= 0;\n      }\n      return getTextContent(elem) === matcher;\n    };\n  } else if (matcher instanceof RegExp) {\n    textContentMatcher = (_, elem) => matcher.test(getTextContent(elem));\n  } else {\n    textContentMatcher = (_, elem) => matcher(getTextContent(elem), elem);\n  }\n  return new DOMSelector(el => queries.queryAllByText(el, textContentMatcher, options));\n};\nconst byAltText = (matcher, options) => new DOMSelector(el => queries.queryAllByAltText(el, matcher, options));\nconst byTitle = (matcher, options) => new DOMSelector(el => queries.queryAllByTitle(el, matcher, options));\nconst byTestId = (matcher, options) => new DOMSelector(el => queries.queryAllByTestId(el, matcher, options));\nconst byValue = (matcher, options) => new DOMSelector(el => queries.queryAllByDisplayValue(el, matcher, options));\nconst byRole = (matcher, options) => new DOMSelector(el => queries.queryAllByRole(el, matcher, options));\nfunction doesServiceImplementsOnDestroy(testedService) {\n  return 'ngOnDestroy' in testedService && typeof testedService['ngOnDestroy'] === 'function';\n}\nfunction isString(value) {\n  return typeof value === 'string';\n}\nfunction isNumber(value) {\n  return typeof value === 'number';\n}\nfunction isType(v) {\n  return typeof v === 'function';\n}\nfunction isHTMLOptionElementArray(value) {\n  return Array.isArray(value) && !!value.length && value.every(item => item instanceof HTMLOptionElement);\n}\nfunction isObject(v) {\n  return v && typeof v === 'object';\n}\nfunction getChildren(debugElementRoot) {\n  return (directiveOrSelector, options = {\n    root: false,\n    read: undefined\n  }) => {\n    if (directiveOrSelector instanceof DOMSelector) {\n      return directiveOrSelector.execute(debugElementRoot.nativeElement);\n    }\n    const debugElements = debugElementRoot.queryAll(isString(directiveOrSelector) ? By.css(directiveOrSelector) : By.directive(directiveOrSelector));\n    if (options.read) {\n      return debugElements.map(debug => debug.injector.get(options.read));\n    }\n    if (isString(directiveOrSelector)) {\n      return debugElements.map(debug => debug.nativeElement);\n    }\n    return debugElements.map(debug => debug.injector.get(directiveOrSelector));\n  };\n}\nfunction setProps(instance, keyOrKeyValues, value, firstChange = true) {\n  var _a;\n  const changes = {};\n  const update = (key, newValue) => {\n    if (instance[key] !== newValue) {\n      changes[key] = new SimpleChange(instance[key], newValue, firstChange);\n    }\n    instance[key] = newValue;\n  };\n  if (isString(keyOrKeyValues)) {\n    update(keyOrKeyValues, value);\n  } else {\n    // tslint:disable-next-line:forin\n    for (const p in keyOrKeyValues) {\n      update(p, keyOrKeyValues[p]);\n    }\n  }\n  if (Object.keys(changes).length) {\n    // tslint:disable-next-line:no-life-cycle-call\n    (_a = instance.ngOnChanges) === null || _a === void 0 ? void 0 : _a.call(instance, changes);\n  }\n  return instance;\n}\nconst parseKeyOptions = keyOrKeyCode => {\n  if (isNumber(keyOrKeyCode) && keyOrKeyCode) {\n    return {\n      key: false,\n      keyCode: keyOrKeyCode,\n      modifiers: {}\n    };\n  }\n  if (isString(keyOrKeyCode) && keyOrKeyCode) {\n    return parseKey(keyOrKeyCode);\n  }\n  if (isObject(keyOrKeyCode)) {\n    const parsedKey = parseKey(keyOrKeyCode.key);\n    return Object.assign(Object.assign({}, parsedKey), {\n      keyCode: keyOrKeyCode.keyCode\n    });\n  }\n  throw new Error('keyboard.pressKey() requires a valid key or keyCode');\n};\nconst parseKey = keyStr => {\n  if (keyStr.indexOf('.') < 0 || '.' === keyStr) {\n    return {\n      key: keyStr,\n      keyCode: false,\n      modifiers: {}\n    };\n  }\n  const keyParts = keyStr.split('.');\n  const key = keyParts.pop();\n  const modifiers = keyParts.reduce((mods, part) => {\n    switch (part) {\n      case 'control':\n      case 'ctrl':\n        mods.control = true;\n        return mods;\n      case 'shift':\n        mods.shift = true;\n        return mods;\n      case 'alt':\n        mods.alt = true;\n        return mods;\n      case 'meta':\n      case 'cmd':\n      case 'win':\n        mods.meta = true;\n        return mods;\n      default:\n        throw new Error(`invalid key modifier: ${part ? part : 'undefined'}, keyStr: ${keyStr}`);\n    }\n  }, {\n    alt: false,\n    control: false,\n    shift: false,\n    meta: false\n  });\n  return {\n    key,\n    keyCode: false,\n    modifiers\n  };\n};\nconst ɵ0 = parseKey;\n\n/**\n * Credit - Angular Material\n */\n/** Creates a browser MouseEvent with the specified options. */\nfunction createMouseEvent(type, x = 0, y = 0, button = 0) {\n  const event = document.createEvent('MouseEvent');\n  event.initMouseEvent(type, true, false, window, 0, x, y, x, y, false, false, false, false, button, null);\n  // `initMouseEvent` doesn't allow us to pass the `buttons` and\n  // defaults it to 0 which looks like a fake event.\n  Object.defineProperty(event, 'buttons', {\n    get: () => 1\n  });\n  return event;\n}\n/**\n * Creates a browser TouchEvent with the specified pointer coordinates.\n */\nfunction createTouchEvent(type, pageX = 0, pageY = 0) {\n  // In favor of creating events that work for most of the browsers, the event is created\n  // as a basic UI Event. The necessary details for the event will be set manually.\n  const event = new UIEvent(type, {\n    bubbles: true,\n    cancelable: true,\n    view: window,\n    detail: 0\n  });\n  // Most of the browsers don't have a \"initTouchEvent\" method that can be used to define\n  // the touch details.\n  Object.defineProperties(event, {\n    touches: {\n      value: [{\n        pageX,\n        pageY\n      }]\n    }\n  });\n  return event;\n}\n/** Dispatches a keydown event from an element. */\nfunction createKeyboardEvent(type, keyOrKeyCode, target) {\n  const {\n    key,\n    keyCode,\n    modifiers\n  } = parseKeyOptions(keyOrKeyCode);\n  const event = document.createEvent('KeyboardEvent');\n  const originalPreventDefault = event.preventDefault;\n  // Firefox does not support `initKeyboardEvent`, but supports `initKeyEvent`.\n  if (event.initKeyEvent) {\n    event.initKeyEvent(type, true, true, window, modifiers.control, modifiers.alt, modifiers.shift, modifiers.meta, keyCode);\n  } else {\n    // `initKeyboardEvent` expects to receive modifiers as a whitespace-delimited string\n    // See https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/initKeyboardEvent\n    const modifiersStr = (modifiers.control ? 'Control ' : '' + modifiers.alt ? 'Alt ' : '' + modifiers.shift ? 'Shift ' : '' + modifiers.meta ? 'Meta' : '').trim();\n    event.initKeyboardEvent(type, true /* canBubble */, true /* cancelable */, window /* view */, 0 /* char */, key /* key */, 0 /* location */, modifiersStr /* modifiersList */, false /* repeat */);\n  }\n  // Webkit Browsers don't set the keyCode when calling the init function.\n  // See related bug https://bugs.webkit.org/show_bug.cgi?id=16735\n  Object.defineProperties(event, {\n    keyCode: {\n      get: () => keyCode\n    },\n    key: {\n      get: () => key\n    },\n    target: {\n      get: () => target\n    },\n    altKey: {\n      get: () => !!modifiers.alt\n    },\n    ctrlKey: {\n      get: () => !!modifiers.control\n    },\n    shiftKey: {\n      get: () => !!modifiers.shift\n    },\n    metaKey: {\n      get: () => !!modifiers.meta\n    }\n  });\n  // IE won't set `defaultPrevented` on synthetic events so we need to do it manually.\n  // tslint:disable-next-line\n  event.preventDefault = function () {\n    Object.defineProperty(event, 'defaultPrevented', {\n      configurable: true,\n      get: () => true\n    });\n    return originalPreventDefault.apply(this, arguments);\n  };\n  return event;\n}\n/** Creates a fake event object with any desired event type. */\nfunction createFakeEvent(type, canBubble = false, cancelable = true) {\n  const event = document.createEvent('Event');\n  event.initEvent(type, canBubble, cancelable);\n  return event;\n}\n\n/**\n * Credit - Angular Material\n */\n/**\n * Utility to dispatch any event on a Node.\n *\n * @publicApi\n */\nfunction dispatchEvent(node, event) {\n  node.dispatchEvent(event);\n  return event;\n}\n/**\n * Shorthand to dispatch a fake event on a specified node.\n *\n * dispatchFakeEvent(element, 'mousedown');\n *\n * @publicApi\n */\nfunction dispatchFakeEvent(node, type, canBubble) {\n  return dispatchEvent(node, createFakeEvent(type, canBubble));\n}\n/**\n * Shorthand to dispatch a keyboard event with a specified key.\n *\n *  dispatchKeyboardEvent(calendarBodyEl, 'keydown', 'LEFT_ARROW');\n *\n *  @publicApi\n */\nfunction dispatchKeyboardEvent(node, type, keyOrKeyCode, target) {\n  return dispatchEvent(node, createKeyboardEvent(type, keyOrKeyCode, target));\n}\n/**\n * Shorthand to dispatch a mouse event on the specified coordinates.\n *\n *  dispatchMouseEvent(rippleTarget, 'mousedown', 50, 75);\n *  dispatchMouseEvent(rippleTarget, 'mouseup');\n *\n *  @publicApi\n */\nfunction dispatchMouseEvent(node, type, x = 0, y = 0, event = createMouseEvent(type, x, y)) {\n  return dispatchEvent(node, event);\n}\n/**\n * Shorthand to dispatch a touch event on the specified coordinates.\n *\n * dispatchTouchEvent(rippleTarget, 'touchstart');\n *\n * @publicApi\n */\nfunction dispatchTouchEvent(node, type, x = 0, y = 0) {\n  return dispatchEvent(node, createTouchEvent(type, x, y));\n}\n\n/**\n * Patches an elements focus and blur methods to emit events consistently and predictably.\n * This is necessary, because some browsers, like IE11, will call the focus handlers asynchronously,\n * while others won't fire them at all if the browser window is not focused.\n *\n * patchElementFocus(triggerEl);\n */\nfunction patchElementFocus(element) {\n  element.focus = () => dispatchFakeEvent(element, 'focus');\n  element.blur = () => dispatchFakeEvent(element, 'blur');\n}\n\n/**\n * Focuses an input or textarea, sets its value and dispatches\n * the `input` or `textarea` event, simulating the user typing.\n * @param value Value to be set on the input.\n * @param element Element onto which to set the value.\n *\n * typeInElement('al', input);\n */\nfunction typeInElement(value, element) {\n  if (!(element instanceof HTMLInputElement) && !(element instanceof HTMLTextAreaElement)) {\n    return;\n  }\n  element.focus();\n  element.value = value;\n  dispatchFakeEvent(element, 'input', true);\n}\n\n/**\n * Focuses a select element, selects the correct options and dispatches\n * the `change` event, simulating the user selecting an option\n * @param options Options to be selected.\n * @param element Element onto which to select the options.\n * @param config Object with extra config to dispatch change event when option selected\n *\n * selectOption('al' | ['al', 'ab'], select, config);\n */\nfunction selectOption(options, element, config) {\n  if (!(element instanceof HTMLSelectElement)) {\n    return;\n  }\n  element.focus();\n  if (isString(options)) {\n    const option = element.querySelector(`option[value=\"${options}\"]`);\n    if (!option) {\n      return;\n    }\n    setOptionSelected(option, element, config);\n  } else if (options instanceof HTMLOptionElement) {\n    setOptionSelected(options, element, config);\n  } else {\n    if (!element.multiple) {\n      return;\n    }\n    if (isHTMLOptionElementArray(options)) {\n      options.forEach(option => setOptionSelected(option, element, config));\n    } else {\n      element.querySelectorAll('option').forEach(opt => {\n        if (options.includes(opt.value)) {\n          setOptionSelected(opt, element, config);\n        }\n      });\n    }\n  }\n}\n/**\n * Set the option in the HTMLSelectElement to selected\n * @param option HTMLOptionElement to select\n * @param select HTMLSelectElement to add the options to\n * @param config Object with extra config to dispatch change event when option selected\n *\n * setOptionSelected(option, element, config);\n */\nfunction setOptionSelected(option, select, config) {\n  option.selected = true;\n  if (config.emitEvents) {\n    dispatchFakeEvent(select, 'change', true);\n  }\n}\n\n/**\n * @internal\n */\nclass BaseSpectator {\n  inject(token) {\n    return TestBed.inject ? TestBed.inject(token) : TestBed.get(token);\n  }\n}\nconst KEY_UP = 'keyup';\n/**\n * @internal\n */\nclass DomSpectator extends BaseSpectator {\n  constructor(fixture, debugElement, instance, element) {\n    super();\n    this.fixture = fixture;\n    this.debugElement = debugElement;\n    this.instance = instance;\n    this.element = element;\n  }\n  inject(token) {\n    return super.inject(token);\n  }\n  detectChanges() {\n    this.fixture.detectChanges();\n  }\n  query(directiveOrSelector, options) {\n    if ((options || {}).root) {\n      if (isString(directiveOrSelector)) {\n        return document.querySelector(directiveOrSelector);\n      }\n      if (directiveOrSelector instanceof DOMSelector) {\n        return directiveOrSelector.execute(document)[0] || null;\n      }\n    }\n    return getChildren(this.debugElement)(directiveOrSelector, options)[0] || null;\n  }\n  queryAll(directiveOrSelector, options) {\n    if ((options || {}).root) {\n      if (isString(directiveOrSelector)) {\n        return Array.from(document.querySelectorAll(directiveOrSelector));\n      }\n      if (directiveOrSelector instanceof DOMSelector) {\n        return directiveOrSelector.execute(document);\n      }\n    }\n    return getChildren(this.debugElement)(directiveOrSelector, options);\n  }\n  queryLast(directiveOrSelector, options) {\n    let result = [];\n    if ((options || {}).root) {\n      if (isString(directiveOrSelector)) {\n        result = Array.from(document.querySelectorAll(directiveOrSelector));\n      }\n      if (directiveOrSelector instanceof DOMSelector) {\n        result = directiveOrSelector.execute(document);\n      }\n    } else {\n      result = getChildren(this.debugElement)(directiveOrSelector, options);\n    }\n    if (result && result.length) {\n      return result[result.length - 1];\n    }\n    return null;\n  }\n  setInput(input, value) {\n    setProps(this.instance, input, value, false);\n    this.debugElement.injector.get(ChangeDetectorRef).detectChanges();\n  }\n  output(output) {\n    const observable = this.instance[output];\n    if (!(observable instanceof Observable)) {\n      throw new Error(`${output} is not an @Output`);\n    }\n    return observable;\n  }\n  tick(millis) {\n    tick(millis);\n    this.detectChanges();\n  }\n  click(selector = this.element) {\n    const element = this.getNativeElement(selector);\n    if (!(element instanceof HTMLElement)) {\n      throw new Error(`Cannot click: ${selector} is not a HTMLElement`);\n    }\n    element.click();\n    this.detectChanges();\n  }\n  blur(selector = this.element) {\n    const element = this.getNativeElement(selector);\n    if (!(element instanceof HTMLElement)) {\n      throw new Error(`Cannot blur: ${selector} is not a HTMLElement`);\n    }\n    patchElementFocus(element);\n    element.blur();\n    this.detectChanges();\n  }\n  focus(selector = this.element) {\n    const element = this.getNativeElement(selector);\n    if (!(element instanceof HTMLElement)) {\n      throw new Error(`Cannot focus: ${selector} is not a HTMLElement`);\n    }\n    patchElementFocus(element);\n    element.focus();\n    this.detectChanges();\n  }\n  dispatchMouseEvent(selector = this.element, type, x = 0, y = 0, event = createMouseEvent(type, x, y)) {\n    const element = this.getNativeElement(selector);\n    if (!(element instanceof Node)) {\n      throw new Error(`Cannot dispatch mouse event: ${selector} is not a node`);\n    }\n    const dispatchedEvent = dispatchMouseEvent(element, type, x, y, event);\n    this.detectChanges();\n    return dispatchedEvent;\n  }\n  dispatchKeyboardEvent(selector = this.element, type, keyOrKeyCode, target) {\n    const element = this.getNativeElement(selector);\n    if (!(element instanceof Node)) {\n      throw new Error(`Cannot dispatch keyboard event: ${selector} is not a node`);\n    }\n    const event = dispatchKeyboardEvent(element, type, keyOrKeyCode, target);\n    this.detectChanges();\n    return event;\n  }\n  dispatchFakeEvent(selector = this.element, type, canBubble) {\n    const event = dispatchFakeEvent(this.getNativeElement(selector), type, canBubble);\n    this.detectChanges();\n    return event;\n  }\n  triggerEventHandler(directiveOrSelector, eventName, eventObj) {\n    const debugElement = this.getDebugElement(directiveOrSelector);\n    if (!debugElement) {\n      // tslint:disable:no-console\n      console.error(`${directiveOrSelector} does not exists`);\n      return;\n    }\n    debugElement.triggerEventHandler(eventName, eventObj);\n    this.detectChanges();\n  }\n  get keyboard() {\n    return {\n      pressKey: (key, selector = this.element, event = KEY_UP) => {\n        this.dispatchKeyboardEvent(selector, event, key);\n      },\n      pressEscape: (selector = this.element, event = KEY_UP) => {\n        this.dispatchKeyboardEvent(selector, event, {\n          key: 'Escape',\n          keyCode: 27\n        });\n      },\n      pressEnter: (selector = this.element, event = KEY_UP) => {\n        this.dispatchKeyboardEvent(selector, event, {\n          key: 'Enter',\n          keyCode: 13\n        });\n      },\n      pressTab: (selector = this.element, event = KEY_UP) => {\n        this.dispatchKeyboardEvent(selector, event, {\n          key: 'Tab',\n          keyCode: 9\n        });\n      },\n      pressBackspace: (selector = this.element, event = KEY_UP) => {\n        this.dispatchKeyboardEvent(selector, event, {\n          key: 'Backspace',\n          keyCode: 8\n        });\n      }\n    };\n  }\n  get mouse() {\n    return {\n      contextmenu: (selector = this.element) => {\n        this.dispatchMouseEvent(selector, 'contextmenu');\n      },\n      dblclick: (selector = this.element) => {\n        this.dispatchMouseEvent(selector, 'dblclick');\n      }\n    };\n  }\n  dispatchTouchEvent(selector = this.element, type, x = 0, y = 0) {\n    dispatchTouchEvent(this.getNativeElement(selector), type, x, y);\n    this.detectChanges();\n  }\n  typeInElement(value, selector = this.element) {\n    typeInElement(value, this.getNativeElement(selector));\n    this.detectChanges();\n  }\n  selectOption(selector = this.element, options, config = {\n    emitEvents: true\n  }) {\n    if (!selector) {\n      throw new Error(`Cannot find select: ${selector}`);\n    }\n    selectOption(options, this.getNativeElement(selector), config);\n    this.detectChanges();\n  }\n  getNativeElement(selector) {\n    let element;\n    // Support global objects window and document\n    if (selector === window || selector === document) {\n      return selector;\n    }\n    if (isString(selector)) {\n      const exists = this.debugElement.query(By.css(selector));\n      if (exists) {\n        element = exists.nativeElement;\n      } else {\n        // tslint:disable:no-console\n        console.error(`${selector} does not exists`);\n      }\n    } else if (selector instanceof DOMSelector) {\n      element = selector.execute(document)[0] || null;\n    } else {\n      if (selector instanceof DebugElement || selector instanceof ElementRef) {\n        element = selector.nativeElement;\n      } else {\n        element = selector;\n      }\n    }\n    return element;\n  }\n  getDebugElement(directiveOrSelector) {\n    let debugElement;\n    if (isString(directiveOrSelector)) {\n      debugElement = this.debugElement.query(By.css(directiveOrSelector));\n    } else if (directiveOrSelector instanceof DebugElement) {\n      debugElement = directiveOrSelector;\n    } else {\n      debugElement = this.debugElement.query(By.directive(directiveOrSelector));\n    }\n    return debugElement;\n  }\n}\n\n/**\n * @publicApi\n */\nclass Spectator extends DomSpectator {\n  constructor(fixture, debugElement, instance, element) {\n    super(fixture, debugElement, instance, element);\n    this.fixture = fixture;\n    this.debugElement = debugElement;\n    this.instance = instance;\n    this.element = element;\n  }\n  get component() {\n    return this.instance;\n  }\n  inject(token, fromComponentInjector = false) {\n    if (fromComponentInjector) {\n      return this.debugElement.injector.get(token);\n    }\n    return super.inject(token);\n  }\n  detectComponentChanges() {\n    if (this.debugElement) {\n      this.debugElement.injector.get(ChangeDetectorRef).detectChanges();\n    } else {\n      this.detectChanges();\n    }\n  }\n}\n\n/**\n * @license\n * Copyright Netanel Basal. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NetanelBasal/spectator/blob/master/LICENSE\n */\nfunction hex2rgb(hex) {\n  const h = hex.replace('#', '');\n  const matches = h.match(new RegExp('(.{' + h.length / 3 + '})', 'g'));\n  const [r, g, b] = matches.map(match => parseInt(match.length === 1 ? match + match : match, 16));\n  return `rgb(${r},${g},${b})`;\n}\nfunction isHex(value) {\n  return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value);\n}\nfunction trim(value) {\n  return (value || '').replace(/\\s/g, '');\n}\nfunction isRunningInJsDom() {\n  return navigator.userAgent.includes('Node.js') || navigator.userAgent.includes('jsdom');\n}\n\n/** Credit: https://github.com/unindented/custom-jquery-matchers/tree/master/packages/custom-jquery-matchers */\nconst hasProperty = (actual, expected) => {\n  return expected === undefined ? actual !== undefined : actual === expected;\n};\nconst ɵ0$1 = hasProperty;\nconst containsProperty = (actual, expected) => {\n  return expected === undefined ? true : actual.includes(expected);\n};\nconst ɵ1 = containsProperty;\nconst checkProperty = (el, prop, predicate) => {\n  let pass = false;\n  let failing = '';\n  for (const key of Object.keys(prop)) {\n    const actual = $(el).prop(key);\n    const addendum = prop[key] !== undefined ? ` with value '${prop[key]}'` : '';\n    pass = predicate(actual, prop[key]);\n    failing = !pass ? `'${prop}'${addendum}, but had '${actual}'` : '';\n  }\n  const message = () => `Expected element${pass ? ' not' : ''} to have property ${failing}`;\n  return {\n    pass,\n    message\n  };\n};\nconst ɵ2 = checkProperty;\nconst hasCss = (el, css) => {\n  let prop;\n  let value;\n  const $el = $(el);\n  for (prop in css) {\n    if (css.hasOwnProperty(prop)) {\n      value = css[prop];\n      if (isHex(value)) {\n        value = hex2rgb(css[prop]);\n      }\n      if (value === 'auto' && $el.get(0).style[prop] === 'auto') {\n        continue;\n      }\n      if (trim($el.css(prop)) !== trim(value) && trim(el.style[prop]) !== trim(value)) {\n        return false;\n      }\n    }\n  }\n  return true;\n};\nconst ɵ3 = hasCss;\nconst hasSameText = (el, expected, exact = false) => {\n  if (expected && Array.isArray(expected)) {\n    let actual;\n    let pass = false;\n    let failing;\n    $(el).each((i, e) => {\n      actual = exact ? $(e).text() : $.trim($(e).text());\n      pass = exact ? actual === expected[i] : actual.includes(expected[i]);\n      if (!pass) {\n        failing = expected[i];\n        return false;\n      }\n    });\n    const message = () => `Expected element${pass ? ' not' : ''} to have ${exact ? 'exact' : ''} text '${failing}', but had '${actual}'`;\n    return {\n      pass,\n      message\n    };\n  }\n  const actual = exact ? $(el).text() : $.trim($(el).text());\n  if (expected && typeof expected !== 'string') {\n    const pass = expected(actual);\n    const message = () => `Expected element${pass ? ' not' : ''} to have ${exact ? 'exact' : ''} text matching '${expected}',` + ` but had '${actual}'`;\n    return {\n      pass,\n      message\n    };\n  }\n  const pass = exact && !Array.isArray(expected) ? actual === expected : actual.indexOf(expected) !== -1;\n  const message = () => `Expected element${pass ? ' not' : ''} to have ${exact ? 'exact' : ''} text '${expected}', but had '${actual}'`;\n  return {\n    pass,\n    message\n  };\n};\nconst ɵ4 = hasSameText;\nconst comparator = func => () => ({\n  compare: func\n});\nconst ɵ5 = comparator;\nconst ɵ6 = el => {\n  const actual = $(el).length;\n  const pass = actual > 0;\n  const message = () => `Expected ${el} element${pass ? ' not' : ''} to exist`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n * expect('.zippy__content').not.toExist();\n */\nconst toExist = comparator(ɵ6);\nconst ɵ7 = (el, expected) => {\n  const actual = $(el).length;\n  const pass = actual === expected;\n  const message = () => `Expected element${pass ? ' not' : ''} to have length ${expected}, but had ${actual}`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n * expect('.zippy__content').toHaveLength(3);\n */\nconst toHaveLength = comparator(ɵ7);\nconst ɵ8 = (el, expected) => {\n  const actual = $(el).attr('id');\n  const pass = actual === expected;\n  const message = () => `Expected element${pass ? ' not' : ''} to have ID '${expected}', but had '${actual}'`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n * expect('.zippy__content').toHaveId('ID');\n */\nconst toHaveId = comparator(ɵ8);\nconst ɵ9 = (el, expected) => {\n  if (expected && Array.isArray(expected)) {\n    const actual = $(el).attr('class');\n    const expectedClasses = expected.join(' ');\n    const pass = $(el).hasClass(expectedClasses);\n    const message = () => `Expected element${pass ? ' not' : ''} to have value '${expectedClasses}', but had '${actual}'`;\n    return {\n      pass,\n      message\n    };\n  }\n  const actual = $(el).attr('class');\n  const pass = $(el).hasClass(expected);\n  const message = () => `Expected element${pass ? ' not' : ''} to have class '${expected}', but had '${actual}'`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n * expect('.zippy__content').toHaveClass('class');\n * expect('.zippy__content').toHaveClass('class a, class b');\n * expect('.zippy__content').toHaveClass(['class a, class b']);\n */\nconst toHaveClass = comparator(ɵ9);\nconst ɵ10 = (el, attr, val) => {\n  if (isObject(attr)) {\n    let pass = false;\n    let failing;\n    for (const key of Object.keys(attr)) {\n      const actual = $(el).attr(key);\n      const addendum = attr[key] !== undefined ? ` with value '${attr[key]}'` : '';\n      pass = hasProperty(actual, attr[key]);\n      failing = !pass ? `'${attr}'${addendum}, but had '${actual}'` : '';\n    }\n    const message = () => `Expected element${pass ? ' not' : ''} to have attribute ${failing}`;\n    return {\n      pass,\n      message\n    };\n  }\n  const actual = $(el).attr(attr);\n  const addendum = val !== undefined ? ` with value '${val}'` : '';\n  const pass = hasProperty(actual, val);\n  const message = () => `Expected element${pass ? ' not' : ''} to have attribute '${attr}'${addendum}, but had '${actual}'`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n * expect(host.query('.zippy')).toHaveAttribute('id', 'zippy');\n */\nconst toHaveAttribute = comparator(ɵ10);\nconst ɵ11 = (el, prop, val) => {\n  if (isObject(prop)) {\n    return checkProperty(el, prop, hasProperty);\n  }\n  const actual = $(el).prop(prop);\n  const addendum = val !== undefined ? ` with value '${val}'` : '';\n  const pass = hasProperty(actual, val);\n  const message = () => `Expected element${pass ? ' not' : ''} to have property '${prop}'${addendum}, but had '${actual}'`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *  expect(host.query('.checkbox')).toHaveProperty('checked', true);\n *  expect(host.query('.checkbox')).toHaveProperty({checked: true});\n */\nconst toHaveProperty = comparator(ɵ11);\nconst ɵ12 = (el, prop, val) => {\n  if (isObject(prop)) {\n    return checkProperty(el, prop, containsProperty);\n  }\n  const actual = $(el).prop(prop);\n  const addendum = val !== undefined ? ` with value '${val}'` : '';\n  const pass = containsProperty(actual, val);\n  const message = () => `Expected element${pass ? ' not' : ''} to have property '${prop}'${addendum}, but had '${actual}'`;\n  return {\n    pass,\n    message\n  };\n};\nconst toContainProperty = comparator(ɵ12);\nconst ɵ13 = (el, expected, exact = false) => hasSameText(el, expected, exact);\n/**\n *\n * expect('.zippy__content').toHaveText('Content');\n * expect('.zippy__content').toHaveText(['Content A', 'Content B']);\n *\n * expect('.zippy__content').toHaveText((text) => text.includes('..');\n */\nconst toHaveText = comparator(ɵ13);\nconst ɵ14 = (el, expected) => hasSameText(el, expected, true);\nconst toHaveExactText = comparator(ɵ14);\nconst toContainText = toHaveText;\nconst ɵ15 = (el, expected) => {\n  if (expected && Array.isArray(expected)) {\n    let actual;\n    let pass = false;\n    let failing;\n    $(el).each((i, e) => {\n      actual = $(e).val();\n      pass = actual === expected[i];\n      if (!pass) {\n        failing = expected[i];\n        return false;\n      }\n    });\n    const message = () => `Expected element${pass ? ' not' : ''} to have value '${failing}', but had '${actual}'`;\n    return {\n      pass,\n      message\n    };\n  }\n  const actual = $(el).val();\n  const pass = actual === expected;\n  const message = () => `Expected element${pass ? ' not' : ''} to have value '${expected}', but had '${actual}'`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n * expect('.zippy__content').toHaveValue('value');\n * expect('.zippy__content').toHaveValue(['value a', 'value b']);\n */\nconst toHaveValue = comparator(ɵ15);\nconst toContainValue = toHaveValue;\nconst ɵ16 = (el, expected) => {\n  const pass = hasCss(el, expected);\n  const message = () => `Expected element${pass ? ' not' : ''} to have CSS ${JSON.stringify(expected)}`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n *  expect(host.element).toHaveStyle({\n *    backgroundColor: 'rgba(0, 0, 0, 0.1)'\n *  });\n */\nconst toHaveStyle = comparator(ɵ16);\nconst ɵ17 = (el, {\n  data,\n  val\n}) => {\n  const actual = $(el).data(data);\n  const addendum = val !== undefined ? ` with value '${val}'` : '';\n  const pass = hasProperty(actual, val);\n  const message = () => `Expected element${pass ? ' not' : ''} to have data '${data}'${addendum}, but had '${actual}'`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n * expect('.zippy__content').toHaveData({data: 'role', val: 'admin'});\n */\nconst toHaveData = comparator(ɵ17);\nconst ɵ18 = el => {\n  const pass = $(el).is(':checked');\n  const message = () => `Expected element${pass ? ' not' : ''} to be checked`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n * expect('.checkbox').toBeChecked();\n */\nconst toBeChecked = comparator(ɵ18);\nconst ɵ19 = el => {\n  const pass = $(el).is(':disabled');\n  const message = () => `Expected element${pass ? ' not' : ''} to be disabled`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n * expect('.checkbox').toBeDisabled();\n */\nconst toBeDisabled = comparator(ɵ19);\nconst ɵ20 = el => {\n  const pass = $(el).is(':empty');\n  const message = () => `Expected element${pass ? ' not' : ''} to be empty`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n * An empty element is an element without child elements or text.\n *\n * expect('div').toBeEmpty();\n */\nconst toBeEmpty = comparator(ɵ20);\n/**\n * Hidden elements are elements that have:\n * 1. Display property set to \"none\"\n * 2. Width and height set to 0 (check not applied in jest)\n * 3. A hidden parent element (this also hides child elements)\n * 4. Type equal to \"hidden\" (only for form elements)\n * 5. A \"hidden\" attribute\n */\nfunction isHidden(elOrSelector) {\n  let el = $(elOrSelector)[0];\n  if (!el) {\n    return true;\n  }\n  const hiddenWhen = [el => !(el.offsetWidth || el.offsetHeight || el.getClientRects().length), el => el.style.display === 'none', el => el.style.visibility === 'hidden', el => el.type === 'hidden', el => el.hasAttribute('hidden')];\n  if (isRunningInJsDom()) {\n    // When running in JSDOM (Jest), offset-properties and client rects are always reported as 0\n    // - hence, let's take a more \"naive\" approach here. (https://github.com/jsdom/jsdom/issues/135)\n    hiddenWhen.shift();\n  }\n  while (el) {\n    if (el === document) {\n      break;\n    }\n    if (hiddenWhen.some(rule => rule(el))) {\n      return true;\n    }\n    el = el.parentNode;\n  }\n  return false;\n}\nconst ɵ21 = el => {\n  const pass = isHidden(el);\n  const message = () => `Expected element${pass ? ' not' : ''} to be hidden`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n * Hidden elements are elements that have:\n * 1. Display property set to \"none\"\n * 2. Width and height set to 0\n * 3. A hidden parent element (this also hides child elements)\n * 4. Type equal to \"hidden\" (only for form elements)\n * 5. A \"hidden\" attribute\n *\n * expect('div').toBeHidden();\n *\n */\nconst toBeHidden = comparator(ɵ21);\nconst ɵ22 = el => {\n  const pass = $(el).is(':selected');\n  const message = () => `Expected element${pass ? ' not' : ''} to be selected`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n * The :selected selector selects option elements that are pre-selected.\n *\n * expect('div').toBeSelected();\n *\n */\nconst toBeSelected = comparator(ɵ22);\nconst ɵ23 = el => {\n  const pass = !isHidden(el);\n  const message = () => `Expected element${pass ? ' not' : ''} to be visible`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n * Hidden elements are elements that have:\n * 1. Display property set to \"none\"\n * 2. Width and height set to 0\n * 3. A hidden parent element (this also hides child elements)\n * 4. Type equal to \"hidden\" (only for form elements)\n * 5. A \"hidden\" attribute\n *\n * expect('div').toBeVisible();\n *\n */\nconst toBeVisible = comparator(ɵ23);\nconst ɵ24 = el => {\n  const element = $(el).get(0);\n  const pass = element === element.ownerDocument.activeElement;\n  const message = () => `Expected element${pass ? ' not' : ''} to be focused`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n * The :focus selector selects the element that currently has focus.\n *\n * expect('input').toBeFocused();\n */\nconst toBeFocused = comparator(ɵ24);\nconst ɵ25 = (el, expected) => {\n  const actual = $(el).filter(expected).length;\n  const pass = actual > 0;\n  const message = () => `Expected element${pass ? ' not' : ''} to be matched by '${expected}'`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n * Check to see if the set of matched elements matches the given selector\n * returns true if the dom contains the element\n *\n * expect('div').toBeMatchedBy('.js-something')\n */\nconst toBeMatchedBy = comparator(ɵ25);\nconst ɵ26 = (el, selector) => {\n  const actual = $(el).find(selector).length;\n  const pass = actual > 0;\n  const message = () => `Expected element${pass ? ' not' : ''} to contain child '${selector}'`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n * expect('div').toHaveDescendant('.child')\n */\nconst toHaveDescendant = comparator(ɵ26);\nconst ɵ27 = (el, {\n  selector,\n  text\n}) => {\n  const actual = $.trim($(el).find(selector).text());\n  if (text && $.isFunction(text.test)) {\n    const pass = text.test(actual);\n    const message = () => `Expected element${pass ? ' not' : ''} to have descendant '${selector}' with text matching '${text}',` + ` but had '${actual}'`;\n    return {\n      pass,\n      message\n    };\n  }\n  const pass = actual.indexOf(text) !== -1;\n  const message = () => `Expected element${pass ? ' not' : ''} to have descendant '${selector}' with text '${text}', but had '${actual}'`;\n  return {\n    pass,\n    message\n  };\n};\n/**\n *\n * expect('div').toHaveDescendantWithText({selector: '.child', text: 'text'})\n */\nconst toHaveDescendantWithText = comparator(ɵ27);\nconst ɵ28 = (el, expected) => {\n  if (expected instanceof HTMLOptionElement) {\n    const actual = $(el).find(':selected');\n    const pass = actual.is($(expected));\n    const message = () => `Expected element${pass ? ' not' : ''} to have options '[${expected.outerHTML}]' but had '[${actual[0].outerHTML}]'`;\n    return {\n      pass,\n      message\n    };\n  }\n  if (isHTMLOptionElementArray(expected)) {\n    const actual = $(el).find(':selected');\n    const pass = actual.length === expected.length && actual.toArray().every((_, index) => $(actual[index]).is(expected[index]));\n    const expectedOptionsString = $(expected).get().map(option => option.outerHTML).join(',');\n    const actualOptionsString = actual.get().map(option => option.outerHTML).join(',');\n    const message = () => `Expected element${pass ? ' not' : ''} to have options '[${expectedOptionsString}]' but had '[${actualOptionsString}]'`;\n    return {\n      pass,\n      message\n    };\n  }\n  const actual = $(el).val();\n  const pass = JSON.stringify([...actual]) === JSON.stringify([...expected]);\n  const expectedOptionsString = Array.isArray(expected) ? expected.reduce((acc, val, i) => acc + `${i === expected.length ? '' : ','}${val}`) : expected;\n  const message = () => `Expected element${pass ? ' not' : ''} to have options '[${expectedOptionsString}]' but had '[${actual}]'`;\n  return {\n    pass,\n    message\n  };\n};\nconst toHaveSelectedOptions = comparator(ɵ28);\nvar customMatchers = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  toExist: toExist,\n  toHaveLength: toHaveLength,\n  toHaveId: toHaveId,\n  toHaveClass: toHaveClass,\n  toHaveAttribute: toHaveAttribute,\n  toHaveProperty: toHaveProperty,\n  toContainProperty: toContainProperty,\n  toHaveText: toHaveText,\n  toHaveExactText: toHaveExactText,\n  toContainText: toContainText,\n  toHaveValue: toHaveValue,\n  toContainValue: toContainValue,\n  toHaveStyle: toHaveStyle,\n  toHaveData: toHaveData,\n  toBeChecked: toBeChecked,\n  toBeDisabled: toBeDisabled,\n  toBeEmpty: toBeEmpty,\n  toBeHidden: toBeHidden,\n  toBeSelected: toBeSelected,\n  toBeVisible: toBeVisible,\n  toBeFocused: toBeFocused,\n  toBeMatchedBy: toBeMatchedBy,\n  toHaveDescendant: toHaveDescendant,\n  toHaveDescendantWithText: toHaveDescendantWithText,\n  toHaveSelectedOptions: toHaveSelectedOptions,\n  ɵ0: ɵ0$1,\n  ɵ1: ɵ1,\n  ɵ2: ɵ2,\n  ɵ3: ɵ3,\n  ɵ4: ɵ4,\n  ɵ5: ɵ5,\n  ɵ6: ɵ6,\n  ɵ7: ɵ7,\n  ɵ8: ɵ8,\n  ɵ9: ɵ9,\n  ɵ10: ɵ10,\n  ɵ11: ɵ11,\n  ɵ12: ɵ12,\n  ɵ13: ɵ13,\n  ɵ14: ɵ14,\n  ɵ15: ɵ15,\n  ɵ16: ɵ16,\n  ɵ17: ɵ17,\n  ɵ18: ɵ18,\n  ɵ19: ɵ19,\n  ɵ20: ɵ20,\n  ɵ21: ɵ21,\n  ɵ22: ɵ22,\n  ɵ23: ɵ23,\n  ɵ24: ɵ24,\n  ɵ25: ɵ25,\n  ɵ26: ɵ26,\n  ɵ27: ɵ27,\n  ɵ28: ɵ28\n});\nfunction addMatchers(matchers) {\n  if (!matchers) return;\n  if (typeof jasmine !== 'undefined') {\n    jasmine.addMatchers(matchers);\n  }\n  if (typeof jest !== 'undefined') {\n    const jestExpectExtend = {};\n    for (const key of Object.keys(matchers)) {\n      if (key.startsWith('to')) jestExpectExtend[key] = matchers[key]().compare;\n    }\n    expect.extend(jestExpectExtend);\n  }\n}\nlet globals = {\n  providers: [],\n  declarations: [],\n  imports: []\n};\nfunction defineGlobalsInjections(config) {\n  globals = Object.assign(Object.assign({}, globals), config);\n}\nfunction getGlobalsInjections() {\n  return globals;\n}\n\n/**\n * @internal\n */\nfunction initialModule(options) {\n  const globals = Object.assign({\n    imports: [],\n    declarations: [],\n    providers: []\n  }, getGlobalsInjections());\n  return {\n    declarations: [...globals.declarations, ...options.declarations, ...options.entryComponents],\n    imports: [...(options.disableAnimations ? [NoopAnimationsModule] : []), ...globals.imports, ...options.imports],\n    providers: [...globals.providers, ...options.providers, ...options.mocks.map(type => options.mockProvider(type))],\n    entryComponents: [...options.entryComponents]\n  };\n}\n\n/**\n * @internal\n */\nfunction initialSpectatorModule(options) {\n  const moduleMetadata = initialModule(options);\n  if (options.declareComponent) {\n    moduleMetadata.declarations.push(options.component);\n  }\n  moduleMetadata.schemas = [options.shallow ? NO_ERRORS_SCHEMA : options.schemas || []];\n  return moduleMetadata;\n}\n\n/**\n * @internal\n */\nfunction merge(defaults, overrides) {\n  // tslint:disable-next-line:no-object-literal-type-assertion\n  return Object.assign(Object.assign({}, defaults), overrides);\n}\n\n/**\n * @internal\n */\nfunction installProtoMethods(mock, proto, createSpyFn) {\n  if (proto === null || proto === Object.prototype) {\n    return;\n  }\n  for (const key of Object.getOwnPropertyNames(proto)) {\n    const descriptor = Object.getOwnPropertyDescriptor(proto, key);\n    if (!descriptor) {\n      continue;\n    }\n    if (typeof descriptor.value === 'function' && key !== 'constructor' && typeof mock[key] === 'undefined') {\n      mock[key] = createSpyFn(key);\n    } else if (descriptor.get && !mock.hasOwnProperty(key)) {\n      Object.defineProperty(mock, key, {\n        set: value => mock[`_${key}`] = value,\n        get: () => mock[`_${key}`],\n        configurable: true\n      });\n    }\n  }\n  installProtoMethods(mock, Object.getPrototypeOf(proto), createSpyFn);\n  mock.castToWritable = () => mock;\n}\n/**\n * @publicApi\n */\nfunction createSpyObject(type, template) {\n  const mock = Object.assign({}, template) || {};\n  installProtoMethods(mock, type.prototype, name => {\n    const newSpy = jasmine.createSpy(name);\n    newSpy.andCallFake = fn => newSpy.and.callFake(fn);\n    newSpy.andReturn = val => newSpy.and.returnValue(val);\n    newSpy.reset = () => newSpy.calls.reset();\n    // revisit return null here (previously needed for rtts_assert).\n    newSpy.and.returnValue(null);\n    return newSpy;\n  });\n  return mock;\n}\n/**\n * @publicApi\n */\nfunction mockProvider(type, properties) {\n  return {\n    provide: type,\n    useFactory: () => createSpyObject(type, properties)\n  };\n}\nconst defaultOptions = {\n  disableAnimations: true,\n  entryComponents: [],\n  mocks: [],\n  mockProvider,\n  providers: [],\n  declarations: [],\n  imports: [],\n  schemas: [],\n  overrideModules: []\n};\n/**\n * @internal\n */\nfunction getDefaultBaseOptions(options) {\n  return merge(defaultOptions, options);\n}\nconst defaultSpectatorOptions = Object.assign(Object.assign({}, getDefaultBaseOptions()), {\n  shallow: false,\n  declareComponent: true,\n  detectChanges: true,\n  componentProviders: [],\n  componentViewProviders: [],\n  componentMocks: [],\n  componentViewProvidersMocks: []\n});\n/**\n * @internal\n */\nfunction getSpectatorDefaultOptions(overrides) {\n  return merge(defaultSpectatorOptions, overrides);\n}\n\n/**\n * @internal\n */\nfunction overrideComponentIfProviderOverridesSpecified(options) {\n  const hasProviderOverrides = options.componentProviders.length || options.componentMocks.length;\n  const hasViewProviders = options.componentViewProviders.length || options.componentViewProvidersMocks.length;\n  if (hasProviderOverrides || hasViewProviders) {\n    let providerConfiguration = {};\n    if (hasProviderOverrides) {\n      providerConfiguration = {\n        providers: [...options.componentProviders, ...options.componentMocks.map(p => options.mockProvider(p))]\n      };\n    }\n    if (hasViewProviders) {\n      providerConfiguration = Object.assign(Object.assign({}, providerConfiguration), {\n        viewProviders: [...options.componentViewProviders, ...options.componentViewProvidersMocks.map(p => options.mockProvider(p))]\n      });\n    }\n    TestBed.overrideComponent(options.component, {\n      set: providerConfiguration\n    });\n  }\n}\n/**\n * @internal\n */\nfunction overrideModules(options) {\n  if (options.overrideModules.length) {\n    options.overrideModules.forEach(overrideModule => {\n      const [ngModule, override] = overrideModule;\n      TestBed.overrideModule(ngModule, override);\n    });\n  }\n}\n/**\n * @publicApi\n */\nfunction createComponentFactory(typeOrOptions) {\n  const options = isType(typeOrOptions) ? getSpectatorDefaultOptions({\n    component: typeOrOptions\n  }) : getSpectatorDefaultOptions(typeOrOptions);\n  const moduleMetadata = initialSpectatorModule(options);\n  beforeEach(async(() => {\n    addMatchers(customMatchers);\n    TestBed.configureTestingModule(moduleMetadata).overrideModule(BrowserDynamicTestingModule, {\n      set: {\n        entryComponents: moduleMetadata.entryComponents\n      }\n    });\n    overrideModules(options);\n    overrideComponentIfProviderOverridesSpecified(options);\n    TestBed.compileComponents();\n  }));\n  return overrides => {\n    const defaults = {\n      props: {},\n      detectChanges: true,\n      providers: []\n    };\n    const {\n      detectChanges,\n      props,\n      providers\n    } = Object.assign(Object.assign({}, defaults), overrides);\n    if (providers && providers.length) {\n      providers.forEach(provider => {\n        TestBed.overrideProvider(provider.provide, provider);\n      });\n    }\n    const spectator = createSpectator(options, props);\n    if (options.detectChanges && detectChanges) {\n      spectator.detectChanges();\n    }\n    return spectator;\n  };\n}\nfunction createSpectator(options, props) {\n  const fixture = TestBed.createComponent(options.component);\n  const debugElement = fixture.debugElement;\n  const component = setProps(fixture.componentInstance, props);\n  return new Spectator(fixture, debugElement, component, debugElement.nativeElement);\n}\n\n/**\n * @publicApi\n */\nclass SpectatorHost extends Spectator {\n  constructor(hostComponent, hostDebugElement, hostElement, hostFixture, debugElement, componentInstance, element) {\n    super(hostFixture, debugElement, componentInstance, element);\n    this.hostComponent = hostComponent;\n    this.hostDebugElement = hostDebugElement;\n    this.hostElement = hostElement;\n    this.hostFixture = hostFixture;\n    this.debugElement = debugElement;\n    this.element = element;\n  }\n  queryHost(directiveOrSelector, options) {\n    if ((options || {}).root && isString(directiveOrSelector)) {\n      return document.querySelector(directiveOrSelector);\n    }\n    return getChildren(this.hostDebugElement)(directiveOrSelector, options)[0] || null;\n  }\n  queryHostAll(directiveOrSelector, options) {\n    if ((options || {}).root && isString(directiveOrSelector)) {\n      return Array.from(document.querySelectorAll(directiveOrSelector));\n    }\n    return getChildren(this.hostDebugElement)(directiveOrSelector, options);\n  }\n  setHostInput(input, value) {\n    setProps(this.hostComponent, input, value, false);\n    this.detectChanges();\n  }\n}\n\n// TODO (dirkluijk): remove after upgrading to Angular 8.2\n// see: https://github.com/angular/angular/commit/10a1e1974b816ebb979dc10586b160ee07ad8356\nfunction nodeByDirective(type) {\n  return debugNode => debugNode.providerTokens.includes(type);\n}\n\n/**\n * @internal\n */\nfunction initialSpectatorWithHostModule(options) {\n  const moduleMetadata = initialSpectatorModule(options);\n  moduleMetadata.declarations.push(options.host);\n  return moduleMetadata;\n}\nclass HostComponent {}\nHostComponent.decorators = [{\n  type: Component,\n  args: [{\n    template: ''\n  }]\n}];\n/*\n  This is an unused module to resolve the ng build error:\n    'Cannot determine the module for class HostComponent'\n\n  Reference: https://github.com/angular/issues/13590\n*/\nclass HostModule {}\nHostModule.decorators = [{\n  type: NgModule,\n  args: [{\n    declarations: [HostComponent]\n  }]\n}];\nconst defaultSpectatorHostOptions = Object.assign(Object.assign({}, getSpectatorDefaultOptions()), {\n  host: HostComponent,\n  template: ''\n});\n/**\n * @internal\n */\nfunction getSpectatorHostDefaultOptions(overrides) {\n  return merge(defaultSpectatorHostOptions, overrides);\n}\nfunction createHostFactory(typeOrOptions) {\n  const options = isType(typeOrOptions) ? getSpectatorHostDefaultOptions({\n    component: typeOrOptions\n  }) : getSpectatorHostDefaultOptions(typeOrOptions);\n  const moduleMetadata = initialSpectatorWithHostModule(options);\n  beforeEach(async(() => {\n    addMatchers(customMatchers);\n    TestBed.configureTestingModule(moduleMetadata);\n    overrideModules(options);\n    overrideComponentIfProviderOverridesSpecified(options);\n  }));\n  return (template, overrides) => {\n    const defaults = {\n      props: {},\n      hostProps: {},\n      detectChanges: true,\n      providers: []\n    };\n    const {\n      detectChanges,\n      props,\n      hostProps,\n      providers\n    } = Object.assign(Object.assign({}, defaults), overrides);\n    if (providers && providers.length) {\n      providers.forEach(provider => {\n        TestBed.overrideProvider(provider.provide, provider);\n      });\n    }\n    TestBed.overrideModule(BrowserDynamicTestingModule, {\n      set: {\n        entryComponents: moduleMetadata.entryComponents\n      }\n    }).overrideComponent(options.host, {\n      set: {\n        template: template || options.template\n      }\n    });\n    const spectator = createSpectatorHost(options, props, hostProps);\n    if (options.detectChanges && detectChanges) {\n      spectator.detectChanges();\n    }\n    return spectator;\n  };\n}\nfunction createSpectatorHost(options, props, hostProps) {\n  const hostFixture = TestBed.createComponent(options.host);\n  const debugElement = hostFixture.debugElement.query(By.directive(options.component)) || hostFixture.debugElement;\n  const debugNode = hostFixture.debugElement.queryAllNodes(nodeByDirective(options.component))[0];\n  if (!debugNode) {\n    throw new Error(`Cannot find component/directive ${options.component} in host template 😔`);\n  }\n  const hostComponent = setProps(hostFixture.componentInstance, hostProps);\n  const component = setProps(debugNode.injector.get(options.component), props);\n  return new SpectatorHost(hostComponent, hostFixture.debugElement, hostFixture.nativeElement, hostFixture, debugElement, component, debugElement.nativeElement);\n}\n\n/**\n * @publicApi\n */\nclass SpectatorDirective extends DomSpectator {\n  constructor(hostComponent, fixture, debugElement, instance, element) {\n    super(fixture, debugElement, instance, element);\n    this.hostComponent = hostComponent;\n    this.fixture = fixture;\n    this.debugElement = debugElement;\n    this.instance = instance;\n    this.element = element;\n  }\n  get directive() {\n    return this.instance;\n  }\n  inject(token, fromDirectiveInjector = false) {\n    if (fromDirectiveInjector) {\n      return this.debugElement.injector.get(token);\n    }\n    return super.inject(token);\n  }\n  setHostInput(input, value) {\n    setProps(this.hostComponent, input, value, false);\n    this.detectChanges();\n  }\n}\n\n/**\n * @internal\n */\nfunction initialSpectatorDirectiveModule(options) {\n  const moduleMetadata = initialModule(options);\n  if (options.declareDirective) {\n    moduleMetadata.declarations.push(options.directive);\n  }\n  moduleMetadata.declarations.push(options.host);\n  moduleMetadata.schemas = [options.shallow ? NO_ERRORS_SCHEMA : options.schemas || []];\n  return moduleMetadata;\n}\nconst defaultSpectatorRoutingOptions = Object.assign(Object.assign({}, getDefaultBaseOptions()), {\n  host: HostComponent,\n  template: '',\n  shallow: false,\n  detectChanges: true,\n  directiveProviders: [],\n  directiveMocks: [],\n  declareDirective: true\n});\n/**\n * @internal\n */\nfunction getSpectatorDirectiveDefaultOptions(overrides) {\n  return merge(defaultSpectatorRoutingOptions, overrides);\n}\nfunction createDirectiveFactory(typeOrOptions) {\n  const options = isType(typeOrOptions) ? getSpectatorDirectiveDefaultOptions({\n    directive: typeOrOptions\n  }) : getSpectatorDirectiveDefaultOptions(typeOrOptions);\n  const moduleMetadata = initialSpectatorDirectiveModule(options);\n  beforeEach(async(() => {\n    addMatchers(customMatchers);\n    TestBed.configureTestingModule(moduleMetadata);\n    overrideModules(options);\n  }));\n  return (template, overrides) => {\n    const defaults = {\n      props: {},\n      hostProps: {},\n      detectChanges: true,\n      providers: []\n    };\n    const {\n      detectChanges,\n      props,\n      hostProps,\n      providers\n    } = Object.assign(Object.assign({}, defaults), overrides);\n    if (providers && providers.length) {\n      providers.forEach(provider => {\n        TestBed.overrideProvider(provider.provide, provider);\n      });\n    }\n    TestBed.overrideModule(BrowserDynamicTestingModule, {\n      set: {\n        entryComponents: moduleMetadata.entryComponents\n      }\n    }).overrideComponent(options.host, {\n      set: {\n        template: template || options.template\n      }\n    });\n    if (options.directiveProviders.length || options.directiveMocks.length) {\n      TestBed.overrideDirective(options.directive, {\n        set: {\n          providers: [...options.directiveProviders, ...options.directiveMocks.map(p => options.mockProvider(p))]\n        }\n      });\n    }\n    const spectator = createSpectatorDirective(options, props, hostProps);\n    if (options.detectChanges && detectChanges) {\n      spectator.detectChanges();\n    }\n    return spectator;\n  };\n}\nfunction createSpectatorDirective(options, props, hostProps) {\n  const hostFixture = TestBed.createComponent(options.host);\n  const debugElement = hostFixture.debugElement.query(By.directive(options.directive)) || hostFixture.debugElement;\n  const debugNode = hostFixture.debugElement.queryAllNodes(nodeByDirective(options.directive))[0];\n  if (!debugNode) {\n    throw new Error(`Cannot find directive ${options.directive} in host template 😔`);\n  }\n  const hostComponent = setProps(hostFixture.componentInstance, hostProps);\n  const directive = setProps(debugNode.injector.get(options.directive), props);\n  return new SpectatorDirective(hostComponent, hostFixture, hostFixture.debugElement, directive, debugElement.nativeElement);\n}\n\n/**\n * @publicApi\n */\nclass SpectatorService extends BaseSpectator {\n  constructor(service) {\n    super();\n    this.service = service;\n  }\n}\n\n/**\n * @internal\n */\nfunction initialServiceModule(options) {\n  const moduleMetadata = initialModule(options);\n  moduleMetadata.providers.push(options.service);\n  return moduleMetadata;\n}\nconst defaultServiceOptions = Object.assign({}, getDefaultBaseOptions());\n/**\n * @internal\n */\nfunction getDefaultServiceOptions(overrides) {\n  return merge(defaultServiceOptions, overrides);\n}\n\n/**\n * @publicApi\n */\nfunction createServiceFactory(typeOrOptions) {\n  const service = isType(typeOrOptions) ? typeOrOptions : typeOrOptions.service;\n  const options = isType(typeOrOptions) ? getDefaultServiceOptions({\n    service\n  }) : getDefaultServiceOptions(typeOrOptions);\n  const moduleMetadata = initialServiceModule(options);\n  beforeEach(() => {\n    TestBed.configureTestingModule(moduleMetadata);\n    overrideModules(options);\n  });\n  afterEach(() => {\n    const testedService = TestBed.inject ? TestBed.inject(service) : TestBed.get(service);\n    if (doesServiceImplementsOnDestroy(testedService)) {\n      // tslint:disable-next-line:no-life-cycle-call\n      testedService.ngOnDestroy();\n    }\n  });\n  return overrides => {\n    const defaults = {\n      providers: []\n    };\n    const {\n      providers\n    } = Object.assign(Object.assign({}, defaults), overrides);\n    if (providers && providers.length) {\n      providers.forEach(provider => {\n        TestBed.overrideProvider(provider.provide, provider);\n      });\n    }\n    return new SpectatorService(TestBed.inject ? TestBed.inject(service) : TestBed.get(service));\n  };\n}\nclass RouterStub extends Router {}\nfunction isRouterStub(router) {\n  return 'emitRouterEvent' in router;\n}\n\n/**\n * @publicApi\n */\nclass SpectatorRouting extends Spectator {\n  constructor(fixture, debugElement, instance, router, activatedRouteStub) {\n    super(fixture, debugElement, instance, debugElement.nativeElement);\n    this.router = router;\n    this.activatedRouteStub = activatedRouteStub;\n  }\n  /**\n   * Simulates a route navigation by updating the Params, QueryParams and Data observable streams.\n   */\n  triggerNavigation(options) {\n    if (!this.checkStubPresent()) {\n      return;\n    }\n    if (options && options.params) {\n      this.activatedRouteStub.setParams(options.params);\n    }\n    if (options && options.queryParams) {\n      this.activatedRouteStub.setQueryParams(options.queryParams);\n    }\n    if (options && options.data) {\n      this.activatedRouteStub.setAllData(options.data);\n    }\n    if (options && options.fragment) {\n      this.activatedRouteStub.setFragment(options.fragment);\n    }\n    this.triggerNavigationAndUpdate();\n  }\n  /**\n   * Updates the route params and triggers a route navigation.\n   */\n  setRouteParam(name, value) {\n    if (this.checkStubPresent()) {\n      this.activatedRouteStub.setParam(name, value);\n      this.triggerNavigationAndUpdate();\n    }\n  }\n  /**\n   * Updates the route query params and triggers a route navigation.\n   */\n  setRouteQueryParam(name, value) {\n    if (this.checkStubPresent()) {\n      this.activatedRouteStub.setQueryParam(name, value);\n      this.triggerNavigationAndUpdate();\n    }\n  }\n  /**\n   * Updates the route data and triggers a route navigation.\n   */\n  setRouteData(name, value) {\n    if (this.checkStubPresent()) {\n      this.activatedRouteStub.setData(name, value);\n      this.triggerNavigationAndUpdate();\n    }\n  }\n  /**\n   * Updates the route fragment and triggers a route navigation.\n   */\n  setRouteFragment(fragment) {\n    if (this.checkStubPresent()) {\n      this.activatedRouteStub.setFragment(fragment);\n      this.triggerNavigationAndUpdate();\n    }\n  }\n  /**\n   * Updates the route url and triggers a route navigation.\n   */\n  setRouteUrl(url) {\n    if (this.checkStubPresent()) {\n      this.activatedRouteStub.setUrl(url);\n      this.triggerNavigationAndUpdate();\n    }\n  }\n  /**\n   * Emits a router event\n   */\n  emitRouterEvent(event) {\n    if (!isRouterStub(this.router)) {\n      // tslint:disable-next-line:no-console\n      console.warn('No stub for Router present. Set Spectator option \"stubsEnabled\" to true if you want to use this ' + 'helper, or use Router navigation to trigger events.');\n      return;\n    }\n    this.router.emitRouterEvent(event);\n  }\n  triggerNavigationAndUpdate() {\n    this.activatedRouteStub.triggerNavigation();\n    this.detectChanges();\n  }\n  checkStubPresent() {\n    if (!this.activatedRouteStub) {\n      // tslint:disable-next-line:no-console\n      console.warn('No stub for ActivatedRoute present. Set Spectator option \"stubsEnabled\" to true if you want to use this ' + 'helper, or use Router to trigger navigation.');\n      return false;\n    }\n    return true;\n  }\n}\n\n/**\n * @publicApi\n *\n * Utility class for stubbing ActivatedRoute of @angular/router\n */\nclass ActivatedRouteStub extends ActivatedRoute {\n  constructor(options) {\n    super();\n    this.testParams = {};\n    this.testQueryParams = {};\n    this.testData = {};\n    this.testFragment = null;\n    this.testUrl = [];\n    this.testRoot = null;\n    this.testParent = null;\n    this.testFirstChild = null;\n    this.testChildren = null;\n    this.paramsSubject = new ReplaySubject(1);\n    this.queryParamsSubject = new ReplaySubject(1);\n    this.dataSubject = new ReplaySubject(1);\n    this.fragmentSubject = new ReplaySubject(1);\n    this.urlSubject = new ReplaySubject(1);\n    if (options) {\n      this.testParams = options.params || {};\n      this.testQueryParams = options.queryParams || {};\n      this.testData = options.data || {};\n      this.testFragment = options.fragment || null;\n      this.testUrl = options.url || [];\n      this.testRoot = options.root || null;\n      this.testParent = options.parent || null;\n      this.testFirstChild = options.firstChild || null;\n      this.testChildren = options.children || null;\n    }\n    this.params = this.paramsSubject.asObservable();\n    this.queryParams = this.queryParamsSubject.asObservable();\n    this.data = this.dataSubject.asObservable();\n    this.fragment = this.fragmentSubject.asObservable();\n    this.url = this.urlSubject.asObservable();\n    this.snapshot = this.buildSnapshot();\n    this.triggerNavigation();\n  }\n  get paramMap() {\n    return this.paramsSubject.asObservable().pipe(map(params => convertToParamMap(params)));\n  }\n  setParams(params) {\n    this.testParams = params;\n    this.snapshot = this.buildSnapshot();\n  }\n  setParam(name, value) {\n    this.testParams = Object.assign(Object.assign({}, this.testParams), {\n      [name]: value\n    });\n    this.snapshot = this.buildSnapshot();\n  }\n  setQueryParams(queryParams) {\n    this.testQueryParams = queryParams;\n    this.snapshot = this.buildSnapshot();\n  }\n  setQueryParam(name, value) {\n    this.testQueryParams = Object.assign(Object.assign({}, this.testQueryParams), {\n      [name]: value\n    });\n    this.snapshot = this.buildSnapshot();\n  }\n  setAllData(data) {\n    this.testData = data;\n    this.snapshot = this.buildSnapshot();\n  }\n  setData(name, value) {\n    this.testData = Object.assign(Object.assign({}, this.testData), {\n      [name]: value\n    });\n    this.snapshot = this.buildSnapshot();\n  }\n  setFragment(fragment) {\n    this.testFragment = fragment;\n    this.snapshot = this.buildSnapshot();\n  }\n  setUrl(url) {\n    this.testUrl = url;\n    this.snapshot = this.buildSnapshot();\n  }\n  get root() {\n    return this.testRoot || this;\n  }\n  get parent() {\n    return this.testParent || null;\n  }\n  get children() {\n    return this.testChildren || [this];\n  }\n  get firstChild() {\n    return this.testFirstChild || null;\n  }\n  /**\n   * Simulates a route navigation by updating the Params, QueryParams and Data observable streams.\n   */\n  triggerNavigation() {\n    this.paramsSubject.next(this.testParams);\n    this.queryParamsSubject.next(this.testQueryParams);\n    this.dataSubject.next(this.testData);\n    this.fragmentSubject.next(this.testFragment);\n    this.urlSubject.next(this.testUrl);\n  }\n  toString() {\n    return 'activatedRouteStub';\n  }\n  buildSnapshot() {\n    const snapshot = new ActivatedRouteSnapshot();\n    snapshot.params = this.testParams;\n    snapshot.queryParams = this.testQueryParams;\n    snapshot.data = this.testData;\n    snapshot.fragment = this.testFragment;\n    snapshot.url = this.testUrl;\n    return snapshot;\n  }\n}\n\n// tslint:disable\nclass RouterLinkDirectiveStub {\n  constructor() {\n    this.navigatedTo = null;\n  }\n  onClick() {\n    this.navigatedTo = this.routerLink;\n    return true;\n  }\n}\nRouterLinkDirectiveStub.decorators = [{\n  type: Directive,\n  args: [{\n    selector: '[routerLink]',\n    providers: [{\n      provide: RouterLink,\n      useExisting: RouterLinkDirectiveStub\n    }]\n  }]\n}];\nRouterLinkDirectiveStub.propDecorators = {\n  routerLink: [{\n    type: Input\n  }],\n  onClick: [{\n    type: HostListener,\n    args: ['click']\n  }]\n};\n/*\n  This is an unused module to resolve the ng build error:\n    'Cannot determine the module for class RouterLinkDirectiveStub'\n\n  Reference: https://github.com/angular/issues/13590\n*/\nclass RouterLinkDirectiveStubModule {}\nRouterLinkDirectiveStubModule.decorators = [{\n  type: NgModule,\n  args: [{\n    declarations: [RouterLinkDirectiveStub]\n  }]\n}];\n\n/**\n * @internal\n */\nfunction initialRoutingModule(options) {\n  const moduleMetadata = initialSpectatorModule(options);\n  if (options.mockRouterLinks && options.stubsEnabled) {\n    moduleMetadata.declarations.push(RouterLinkDirectiveStub);\n  }\n  if (options.stubsEnabled) {\n    moduleMetadata.providers.push(options.mockProvider(RouterStub, {\n      events: new Subject(),\n      emitRouterEvent(event) {\n        this.events.next(event);\n      }\n    }), {\n      provide: Router,\n      useExisting: RouterStub\n    });\n    moduleMetadata.providers.push({\n      provide: ActivatedRouteStub,\n      useValue: new ActivatedRouteStub({\n        params: options.params,\n        queryParams: options.queryParams,\n        data: options.data\n      })\n    }, {\n      provide: ActivatedRoute,\n      useExisting: ActivatedRouteStub\n    });\n  } else {\n    moduleMetadata.imports.push(RouterTestingModule.withRoutes(options.routes));\n  }\n  return moduleMetadata;\n}\nconst ɵ0$2 = {};\nconst defaultRoutingOptions = Object.assign(Object.assign({}, getSpectatorDefaultOptions()), {\n  params: {},\n  queryParams: {},\n  data: ɵ0$2,\n  fragment: null,\n  mockRouterLinks: true,\n  stubsEnabled: true,\n  routes: [],\n  url: [],\n  root: null,\n  parent: null,\n  children: null,\n  firstChild: null\n});\n/**\n * @internal\n */\nfunction getRoutingDefaultOptions(overrides) {\n  return merge(defaultRoutingOptions, overrides);\n}\n\n/**\n * @publicApi\n */\nfunction createRoutingFactory(typeOrOptions) {\n  const options = isType(typeOrOptions) ? getRoutingDefaultOptions({\n    component: typeOrOptions\n  }) : getRoutingDefaultOptions(typeOrOptions);\n  const moduleMetadata = initialRoutingModule(options);\n  beforeEach(async(() => {\n    addMatchers(customMatchers);\n    TestBed.configureTestingModule(moduleMetadata);\n    overrideModules(options);\n    overrideComponentIfProviderOverridesSpecified(options);\n    TestBed.compileComponents();\n  }));\n  return overrides => {\n    const defaults = {\n      props: {},\n      detectChanges: true,\n      providers: []\n    };\n    const {\n      detectChanges,\n      props,\n      providers\n    } = Object.assign(Object.assign({}, defaults), overrides);\n    if (providers && providers.length) {\n      providers.forEach(provider => {\n        TestBed.overrideProvider(provider.provide, provider);\n      });\n    }\n    const {\n      params,\n      queryParams,\n      data,\n      fragment,\n      url,\n      root,\n      parent,\n      children,\n      firstChild\n    } = Object.assign(Object.assign({}, options), overrides);\n    TestBed.overrideProvider(ActivatedRoute, {\n      useValue: new ActivatedRouteStub({\n        params,\n        queryParams,\n        data,\n        fragment,\n        url,\n        root,\n        parent,\n        children,\n        firstChild\n      })\n    });\n    const ngZone = TestBed.inject ? TestBed.inject(NgZone) : TestBed.get(NgZone);\n    return ngZone.run(() => {\n      const spectator = createSpectatorRouting(options, props);\n      spectator.router.initialNavigation();\n      if (options.detectChanges && detectChanges) {\n        spectator.detectChanges();\n      }\n      return spectator;\n    });\n  };\n}\nfunction createSpectatorRouting(options, props) {\n  const fixture = TestBed.createComponent(options.component);\n  const debugElement = fixture.debugElement;\n  const component = setProps(fixture.componentInstance, props);\n  /**\n   * Back compatibility, angular under 9 version doesnt have a inject function\n   */\n  if (!TestBed.inject) {\n    return new SpectatorRouting(fixture, debugElement, component, TestBed.get(Router), TestBed.get(ActivatedRoute));\n  }\n  return new SpectatorRouting(fixture, debugElement, component, TestBed.inject(Router), TestBed.inject(ActivatedRoute));\n}\n\n/**\n * @publicApi\n */\nvar HttpMethod;\n(function (HttpMethod) {\n  HttpMethod[\"GET\"] = \"GET\";\n  HttpMethod[\"POST\"] = \"POST\";\n  HttpMethod[\"DELETE\"] = \"DELETE\";\n  HttpMethod[\"PUT\"] = \"PUT\";\n  HttpMethod[\"PATCH\"] = \"PATCH\";\n  HttpMethod[\"HEAD\"] = \"HEAD\";\n  HttpMethod[\"JSONP\"] = \"JSONP\";\n  HttpMethod[\"OPTIONS\"] = \"OPTIONS\";\n})(HttpMethod || (HttpMethod = {}));\n/**\n * @publicApi\n */\nclass SpectatorHttp extends BaseSpectator {\n  constructor(service, httpClient, controller) {\n    super();\n    this.service = service;\n    this.httpClient = httpClient;\n    this.controller = controller;\n    // small workaround to prevent issues if destructuring SpectatorHttp, which was common in Spectator 3\n    // remove in v5?\n    this.expectOne = this.expectOne.bind(this);\n    this.expectConcurrent = this.expectConcurrent.bind(this);\n  }\n  expectOne(url, method) {\n    expect(true).toBe(true); // workaround to avoid `Spec has no expectations` https://github.com/NetanelBasal/spectator/issues/75\n    const req = this.controller.expectOne({\n      url,\n      method\n    });\n    // assert that there are no outstanding requests.\n    this.controller.verify();\n    return req;\n  }\n  expectConcurrent(expectations) {\n    const requests = expectations.map(expectation => {\n      return this.controller.expectOne({\n        url: expectation.url,\n        method: expectation.method\n      });\n    });\n    this.controller.verify();\n    return requests;\n  }\n  flushAll(requests, args) {\n    requests.forEach((request, idx) => {\n      request.flush(args[idx]);\n    });\n  }\n}\n\n/**\n * @internal\n */\nfunction initialHttpModule(options) {\n  const moduleMetadata = initialModule(options);\n  moduleMetadata.providers.push(options.service);\n  moduleMetadata.imports.push(HttpClientTestingModule);\n  return moduleMetadata;\n}\nconst defaultHttpOptions = Object.assign({}, getDefaultBaseOptions());\n/**\n * @internal\n */\nfunction getDefaultHttpOptions(overrides) {\n  return merge(defaultHttpOptions, overrides);\n}\n\n/**\n * @publicApi\n */\nfunction createHttpFactory(typeOrOptions) {\n  const service = isType(typeOrOptions) ? typeOrOptions : typeOrOptions.service;\n  const options = isType(typeOrOptions) ? getDefaultHttpOptions({\n    service\n  }) : getDefaultHttpOptions(typeOrOptions);\n  const moduleMetadata = initialHttpModule(options);\n  beforeEach(() => {\n    TestBed.configureTestingModule(moduleMetadata);\n    overrideModules(options);\n  });\n  afterEach(() => {\n    if (TestBed.inject) {\n      TestBed.inject(HttpTestingController).verify();\n    } else {\n      TestBed.get(HttpTestingController).verify();\n    }\n  });\n  return overrides => {\n    const defaults = {\n      providers: []\n    };\n    const {\n      providers\n    } = Object.assign(Object.assign({}, defaults), overrides);\n    if (providers && providers.length) {\n      providers.forEach(provider => {\n        TestBed.overrideProvider(provider.provide, provider);\n      });\n    }\n    /**\n     * Back compatibility, angular under 9 version doesnt have a inject function\n     */\n    if (!TestBed.inject) {\n      return new SpectatorHttp(TestBed.get(service), TestBed.get(HttpClient), TestBed.get(HttpTestingController));\n    }\n    return new SpectatorHttp(TestBed.inject(service), TestBed.inject(HttpClient), TestBed.inject(HttpTestingController));\n  };\n}\n\n/**\n * @publicApi\n */\nclass SpectatorPipe extends BaseSpectator {\n  constructor(hostComponent, fixture, debugElement, element) {\n    super();\n    this.hostComponent = hostComponent;\n    this.fixture = fixture;\n    this.debugElement = debugElement;\n    this.element = element;\n  }\n  detectChanges() {\n    this.fixture.detectChanges();\n  }\n  setHostInput(input, value) {\n    setProps(this.hostComponent, input, value, false);\n    this.detectChanges();\n  }\n}\n\n/**\n * @internal\n */\nfunction initialSpectatorPipeModule(options) {\n  const moduleMetadata = initialModule(options);\n  moduleMetadata.declarations.push(options.pipe);\n  moduleMetadata.declarations.push(options.host);\n  return moduleMetadata;\n}\nconst defaultSpectatorPipeOptions = Object.assign(Object.assign({}, getDefaultBaseOptions()), {\n  host: HostComponent,\n  detectChanges: true,\n  template: ''\n});\n/**\n * @internal\n */\nfunction getSpectatorPipeDefaultOptions(overrides) {\n  return merge(defaultSpectatorPipeOptions, overrides);\n}\n\n/**\n * @publicApi\n */\nfunction createPipeFactory(typeOrOptions) {\n  const options = isType(typeOrOptions) ? getSpectatorPipeDefaultOptions({\n    pipe: typeOrOptions\n  }) : getSpectatorPipeDefaultOptions(typeOrOptions);\n  const moduleMetadata = initialSpectatorPipeModule(options);\n  beforeEach(async(() => {\n    addMatchers(customMatchers);\n    TestBed.configureTestingModule(moduleMetadata);\n    overrideModules(options);\n  }));\n  return (templateOrOverrides, overrides) => {\n    const defaults = {\n      hostProps: {},\n      detectChanges: true,\n      providers: []\n    };\n    const resolvedOverrides = typeof templateOrOverrides === 'object' ? templateOrOverrides : overrides;\n    const {\n      detectChanges,\n      hostProps,\n      providers\n    } = Object.assign(Object.assign({}, defaults), resolvedOverrides);\n    const template = typeof templateOrOverrides === 'string' ? templateOrOverrides : options.template;\n    if (providers && providers.length) {\n      providers.forEach(provider => {\n        TestBed.overrideProvider(provider.provide, provider);\n      });\n    }\n    if (template) {\n      TestBed.overrideModule(BrowserDynamicTestingModule, {\n        set: {\n          entryComponents: moduleMetadata.entryComponents\n        }\n      }).overrideComponent(options.host, {\n        set: {\n          template\n        }\n      });\n    }\n    const spectator = createSpectatorPipe(options, hostProps);\n    if (options.detectChanges && detectChanges) {\n      spectator.detectChanges();\n    }\n    return spectator;\n  };\n}\nfunction createSpectatorPipe(options, hostProps) {\n  const hostFixture = TestBed.createComponent(options.host);\n  const debugElement = hostFixture.debugElement;\n  const hostComponent = setProps(hostFixture.componentInstance, hostProps);\n  return new SpectatorPipe(hostComponent, hostFixture, hostFixture.debugElement, debugElement.nativeElement);\n}\nfunction MockComponentDeprecated(options) {\n  const metadata = {\n    selector: options.selector,\n    template: options.template || '',\n    inputs: options.inputs,\n    outputs: options.outputs || [],\n    exportAs: options.exportAs || ''\n  };\n  class Mock {}\n  metadata.outputs.forEach(method => {\n    Mock.prototype[method] = new EventEmitter();\n  });\n  return Component(metadata)(Mock);\n}\nfunction MockDirectiveDeprecated(options) {\n  const metadata = {\n    selector: options.selector,\n    inputs: options.inputs,\n    outputs: options.outputs || [],\n    exportAs: options.exportAs || ''\n  };\n  class Mock {}\n  metadata.outputs.forEach(method => {\n    Mock.prototype[method] = new EventEmitter();\n  });\n  return Directive(metadata)(Mock);\n}\n\n/// <reference path=\"./lib/matchers-types.ts\" />\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ActivatedRouteStub, DOMSelector, HostComponent, HostModule, HttpMethod, MockComponentDeprecated, MockDirectiveDeprecated, Spectator, SpectatorDirective, SpectatorHost, SpectatorHttp, SpectatorPipe, SpectatorRouting, SpectatorService, byAltText, byLabel, byPlaceholder, byRole, byTestId, byText, byTextContent, byTitle, byValue, createComponentFactory, createDirectiveFactory, createFakeEvent, createHostFactory, createHttpFactory, createKeyboardEvent, createMouseEvent, createPipeFactory, createRoutingFactory, createServiceFactory, createSpyObject, createTouchEvent, defineGlobalsInjections, dispatchEvent, dispatchFakeEvent, dispatchKeyboardEvent, dispatchMouseEvent, dispatchTouchEvent, doesServiceImplementsOnDestroy, initialSpectatorDirectiveModule, initialSpectatorModule, initialSpectatorPipeModule, initialSpectatorWithHostModule, installProtoMethods, isHTMLOptionElementArray, isNumber, isObject, isString, isType, mockProvider, toBeChecked, toBeDisabled, toBeEmpty, toBeFocused, toBeHidden, toBeMatchedBy, toBeSelected, toBeVisible, toContainProperty, toContainText, toContainValue, toExist, toHaveAttribute, toHaveClass, toHaveData, toHaveDescendant, toHaveDescendantWithText, toHaveExactText, toHaveId, toHaveLength, toHaveProperty, toHaveSelectedOptions, toHaveStyle, toHaveText, toHaveValue, typeInElement, ɵ0$1 as ɵ0, ɵ1, ɵ10, ɵ11, ɵ12, ɵ13, ɵ14, ɵ15, ɵ16, ɵ17, ɵ18, ɵ19, ɵ2, ɵ20, ɵ21, ɵ22, ɵ23, ɵ24, ɵ25, ɵ26, ɵ27, ɵ28, ɵ3, ɵ4, ɵ5, ɵ6, ɵ7, ɵ8, ɵ9, DomSpectator as ɵa, BaseSpectator as ɵb };", "map": {"version": 3, "names": ["SimpleChange", "ChangeDetectorRef", "DebugElement", "ElementRef", "NO_ERRORS_SCHEMA", "Component", "NgModule", "Directive", "Input", "HostListener", "NgZone", "EventEmitter", "Observable", "ReplaySubject", "Subject", "TestBed", "tick", "async", "By", "queries", "getDefaultNormalizer", "BrowserDynamicTestingModule", "$", "NoopAnimationsModule", "Router", "ActivatedRoute", "convertToParamMap", "ActivatedRouteSnapshot", "RouterLink", "map", "RouterTestingModule", "HttpClient", "HttpClientTestingModule", "HttpTestingController", "DOMSelector", "constructor", "execute", "<PERSON><PERSON><PERSON><PERSON>", "matcher", "options", "el", "queryAllByLabelText", "byPlaceholder", "queryAllByPlaceholderText", "byText", "queryAllByText", "byTextContent", "textContentMatcher", "normalizer", "getTextContent", "elem", "_a", "textContent", "_", "exact", "toLowerCase", "indexOf", "RegExp", "test", "byAltText", "queryAllByAltText", "byTitle", "queryAllByTitle", "byTestId", "queryAllByTestId", "byV<PERSON>ue", "queryAllByDisplayValue", "byRole", "queryAllByRole", "doesServiceImplementsOnDestroy", "testedService", "isString", "value", "isNumber", "isType", "v", "isHTMLOptionElementArray", "Array", "isArray", "length", "every", "item", "HTMLOptionElement", "isObject", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "debugElementRoot", "directiveOrSelector", "root", "read", "undefined", "nativeElement", "debugElements", "queryAll", "css", "directive", "debug", "injector", "get", "setProps", "instance", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstChange", "changes", "update", "key", "newValue", "p", "Object", "keys", "ngOnChanges", "call", "parseKeyOptions", "keyOrKeyCode", "keyCode", "modifiers", "parse<PERSON>ey", "parsed<PERSON><PERSON>", "assign", "Error", "keyStr", "keyParts", "split", "pop", "reduce", "mods", "part", "control", "shift", "alt", "meta", "ɵ0", "createMouseEvent", "type", "x", "y", "button", "event", "document", "createEvent", "initMouseEvent", "window", "defineProperty", "createTouchEvent", "pageX", "pageY", "UIEvent", "bubbles", "cancelable", "view", "detail", "defineProperties", "touches", "createKeyboardEvent", "target", "originalPreventDefault", "preventDefault", "initKeyEvent", "modifiersStr", "trim", "initKeyboardEvent", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "metaKey", "configurable", "apply", "arguments", "createFakeEvent", "canBubble", "initEvent", "dispatchEvent", "node", "dispatchFakeEvent", "dispatchKeyboardEvent", "dispatchMouseEvent", "dispatchTouchEvent", "patchElementFocus", "element", "focus", "blur", "typeInElement", "HTMLInputElement", "HTMLTextAreaElement", "selectOption", "config", "HTMLSelectElement", "option", "querySelector", "setOptionSelected", "multiple", "for<PERSON>ach", "querySelectorAll", "opt", "includes", "select", "selected", "emitEvents", "BaseSpectator", "inject", "token", "KEY_UP", "DomSpectator", "fixture", "debugElement", "detectChanges", "query", "from", "queryLast", "result", "setInput", "input", "output", "observable", "millis", "click", "selector", "getNativeElement", "HTMLElement", "Node", "dispatchedEvent", "triggerEventHandler", "eventName", "eventObj", "getDebugElement", "console", "error", "keyboard", "pressKey", "pressEscape", "pressEnter", "pressTab", "pressBackspace", "mouse", "contextmenu", "dblclick", "exists", "Spectator", "component", "fromComponentInjector", "detectComponentChanges", "hex2rgb", "hex", "h", "replace", "matches", "match", "r", "g", "b", "parseInt", "isHex", "isRunningInJsDom", "navigator", "userAgent", "hasProperty", "actual", "expected", "ɵ0$1", "containsProperty", "ɵ1", "checkProperty", "prop", "predicate", "pass", "failing", "addendum", "message", "ɵ2", "has<PERSON>s", "$el", "hasOwnProperty", "style", "ɵ3", "hasSameText", "each", "i", "e", "text", "ɵ4", "comparator", "func", "compare", "ɵ5", "ɵ6", "toExist", "ɵ7", "toHave<PERSON>ength", "ɵ8", "attr", "toHaveId", "ɵ9", "expectedClasses", "join", "hasClass", "toHaveClass", "ɵ10", "val", "toHaveAttribute", "ɵ11", "toHaveProperty", "ɵ12", "toContainProperty", "ɵ13", "toHaveText", "ɵ14", "toHaveExactText", "toContainText", "ɵ15", "toHaveValue", "toContainValue", "ɵ16", "JSON", "stringify", "toHaveStyle", "ɵ17", "data", "toHaveData", "ɵ18", "is", "toBeChecked", "ɵ19", "toBeDisabled", "ɵ20", "toBeEmpty", "isHidden", "elOrSelector", "hidden<PERSON>hen", "offsetWidth", "offsetHeight", "getClientRects", "display", "visibility", "hasAttribute", "some", "rule", "parentNode", "ɵ21", "toBeHidden", "ɵ22", "toBeSelected", "ɵ23", "toBeVisible", "ɵ24", "ownerDocument", "activeElement", "toBeFocused", "ɵ25", "filter", "toBeMatchedBy", "ɵ26", "find", "toHaveDescendant", "ɵ27", "isFunction", "toHaveDescendantWithText", "ɵ28", "outerHTML", "toArray", "index", "expectedOptionsString", "actualOptionsString", "acc", "toHaveSelectedOptions", "customMatchers", "freeze", "__proto__", "addMatchers", "matchers", "jasmine", "jest", "jestExpectExtend", "startsWith", "expect", "extend", "globals", "providers", "declarations", "imports", "defineGlobalsInjections", "getGlobalsInjections", "initialModule", "entryComponents", "disableAnimations", "mocks", "mockProvider", "initialSpectatorModule", "moduleMetadata", "declareComponent", "push", "schemas", "shallow", "merge", "defaults", "overrides", "installProtoMethods", "mock", "proto", "createSpyFn", "prototype", "getOwnPropertyNames", "descriptor", "getOwnPropertyDescriptor", "set", "getPrototypeOf", "castToWritable", "createSpyObject", "template", "name", "newSpy", "createSpy", "andCallFake", "fn", "and", "callFake", "andReturn", "returnValue", "reset", "calls", "properties", "provide", "useFactory", "defaultOptions", "overrideModules", "getDefaultBaseOptions", "defaultSpectatorOptions", "componentProviders", "componentViewProviders", "componentMocks", "componentViewProvidersMocks", "getSpectatorDefaultOptions", "overrideComponentIfProviderOverridesSpecified", "hasProviderOverrides", "hasViewProviders", "providerConfiguration", "viewProviders", "overrideComponent", "overrideModule", "ngModule", "override", "createComponentFactory", "typeOrOptions", "beforeEach", "configureTestingModule", "compileComponents", "props", "provider", "override<PERSON><PERSON><PERSON>", "spectator", "createSpectator", "createComponent", "componentInstance", "SpectatorHost", "hostComponent", "hostDebugElement", "hostElement", "hostFixture", "queryHost", "queryHostAll", "setHostInput", "nodeByDirective", "debugNode", "providerTokens", "initialSpectatorWithHostModule", "host", "HostComponent", "decorators", "args", "HostModule", "defaultSpectatorHostOptions", "getSpectatorHostDefaultOptions", "createHostFactory", "hostProps", "createSpectatorHost", "queryAllNodes", "SpectatorDirective", "fromDirectiveInjector", "initialSpectatorDirectiveModule", "declareDirective", "defaultSpectatorRoutingOptions", "directiveProviders", "directiveMocks", "getSpectatorDirectiveDefaultOptions", "createDirectiveFactory", "overrideDirective", "createSpectatorDirective", "SpectatorService", "service", "initialServiceModule", "defaultServiceOptions", "getDefaultServiceOptions", "createServiceFactory", "after<PERSON>ach", "ngOnDestroy", "RouterStub", "isRouterStub", "router", "SpectatorRouting", "activatedRouteStub", "triggerNavigation", "checkStubPresent", "params", "setParams", "queryParams", "setQueryParams", "setAllData", "fragment", "setFragment", "triggerNavigationAndUpdate", "setRouteParam", "set<PERSON>ara<PERSON>", "setRouteQueryParam", "setQueryParam", "setRouteData", "setData", "setRouteFragment", "setRouteUrl", "url", "setUrl", "emitRouterEvent", "warn", "ActivatedRouteStub", "testParams", "testQueryParams", "testData", "testFragment", "testUrl", "testRoot", "testParent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramsSubject", "queryParamsSubject", "dataSubject", "fragmentSubject", "urlSubject", "parent", "<PERSON><PERSON><PERSON><PERSON>", "children", "asObservable", "snapshot", "buildSnapshot", "paramMap", "pipe", "next", "toString", "RouterLinkDirectiveStub", "navigatedTo", "onClick", "routerLink", "useExisting", "propDecorators", "RouterLinkDirectiveStubModule", "initialRoutingModule", "mockRouterLinks", "stubsEnabled", "events", "useValue", "with<PERSON>out<PERSON>", "routes", "ɵ0$2", "defaultRoutingOptions", "getRoutingDefaultOptions", "createRoutingFactory", "ngZone", "run", "createSpectatorRouting", "initialNavigation", "HttpMethod", "SpectatorHttp", "httpClient", "controller", "expectOne", "bind", "expectConcurrent", "method", "toBe", "req", "verify", "expectations", "requests", "expectation", "flushAll", "request", "idx", "flush", "initialHttpModule", "defaultHttpOptions", "getDefaultHttpOptions", "createHttpFactory", "SpectatorPipe", "initialSpectatorPipeModule", "defaultSpectatorPipeOptions", "getSpectatorPipeDefaultOptions", "createPipeFactory", "templateOrOverrides", "resolvedOverrides", "createSpectatorPipe", "MockComponentDeprecated", "metadata", "inputs", "outputs", "exportAs", "<PERSON><PERSON>", "MockDirectiveDeprecated", "ɵa", "ɵb"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@ngneat/spectator/fesm2015/ngneat-spectator.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>e, ChangeDetectorRef, DebugElement, ElementRef, NO_ERRORS_SCHEMA, Component, NgModule, Directive, Input, HostListener, NgZone, EventEmitter } from '@angular/core';\nimport { Observable, ReplaySubject, Subject } from 'rxjs';\nimport { TestBed, tick, async } from '@angular/core/testing';\nimport { By } from '@angular/platform-browser';\nimport { queries, getDefaultNormalizer } from '@testing-library/dom';\nimport { BrowserDynamicTestingModule } from '@angular/platform-browser-dynamic/testing';\nimport $ from 'jquery';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { Router, ActivatedRoute, convertToParamMap, ActivatedRouteSnapshot, RouterLink } from '@angular/router';\nimport { map } from 'rxjs/operators';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { HttpClient } from '@angular/common/http';\nimport { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\n\nclass DOMSelector {\n    // Wrap selector functions in a class to make reflection easier in getChild\n    constructor(execute) {\n        this.execute = execute;\n    }\n}\nconst byLabel = (matcher, options) => new DOMSelector(el => queries.queryAllByLabelText(el, matcher, options));\nconst byPlaceholder = (matcher, options) => new DOMSelector(el => queries.queryAllByPlaceholderText(el, matcher, options));\nconst byText = (matcher, options) => new DOMSelector(el => queries.queryAllByText(el, matcher, options));\nconst byTextContent = (matcher, options) => {\n    let textContentMatcher;\n    const normalizer = (options === null || options === void 0 ? void 0 : options.normalizer) || getDefaultNormalizer(options);\n    const getTextContent = (elem) => { var _a; return normalizer((_a = elem.textContent) !== null && _a !== void 0 ? _a : ''); };\n    if (typeof matcher === 'string') {\n        textContentMatcher = (_, elem) => {\n            if ((options === null || options === void 0 ? void 0 : options.exact) === false) {\n                return (getTextContent(elem)\n                    .toLowerCase()\n                    .indexOf(matcher.toLowerCase()) >= 0);\n            }\n            return getTextContent(elem) === matcher;\n        };\n    }\n    else if (matcher instanceof RegExp) {\n        textContentMatcher = (_, elem) => matcher.test(getTextContent(elem));\n    }\n    else {\n        textContentMatcher = (_, elem) => matcher(getTextContent(elem), elem);\n    }\n    return new DOMSelector(el => queries.queryAllByText(el, textContentMatcher, options));\n};\nconst byAltText = (matcher, options) => new DOMSelector(el => queries.queryAllByAltText(el, matcher, options));\nconst byTitle = (matcher, options) => new DOMSelector(el => queries.queryAllByTitle(el, matcher, options));\nconst byTestId = (matcher, options) => new DOMSelector(el => queries.queryAllByTestId(el, matcher, options));\nconst byValue = (matcher, options) => new DOMSelector(el => queries.queryAllByDisplayValue(el, matcher, options));\nconst byRole = (matcher, options) => new DOMSelector(el => queries.queryAllByRole(el, matcher, options));\n\nfunction doesServiceImplementsOnDestroy(testedService) {\n    return 'ngOnDestroy' in testedService && typeof testedService['ngOnDestroy'] === 'function';\n}\nfunction isString(value) {\n    return typeof value === 'string';\n}\nfunction isNumber(value) {\n    return typeof value === 'number';\n}\nfunction isType(v) {\n    return typeof v === 'function';\n}\nfunction isHTMLOptionElementArray(value) {\n    return Array.isArray(value) && !!value.length && value.every(item => item instanceof HTMLOptionElement);\n}\nfunction isObject(v) {\n    return v && typeof v === 'object';\n}\n\nfunction getChildren(debugElementRoot) {\n    return (directiveOrSelector, options = { root: false, read: undefined }) => {\n        if (directiveOrSelector instanceof DOMSelector) {\n            return directiveOrSelector.execute(debugElementRoot.nativeElement);\n        }\n        const debugElements = debugElementRoot.queryAll(isString(directiveOrSelector) ? By.css(directiveOrSelector) : By.directive(directiveOrSelector));\n        if (options.read) {\n            return debugElements.map(debug => debug.injector.get(options.read));\n        }\n        if (isString(directiveOrSelector)) {\n            return debugElements.map(debug => debug.nativeElement);\n        }\n        return debugElements.map(debug => debug.injector.get(directiveOrSelector));\n    };\n}\nfunction setProps(instance, keyOrKeyValues, value, firstChange = true) {\n    var _a;\n    const changes = {};\n    const update = (key, newValue) => {\n        if (instance[key] !== newValue) {\n            changes[key] = new SimpleChange(instance[key], newValue, firstChange);\n        }\n        instance[key] = newValue;\n    };\n    if (isString(keyOrKeyValues)) {\n        update(keyOrKeyValues, value);\n    }\n    else {\n        // tslint:disable-next-line:forin\n        for (const p in keyOrKeyValues) {\n            update(p, keyOrKeyValues[p]);\n        }\n    }\n    if (Object.keys(changes).length) {\n        // tslint:disable-next-line:no-life-cycle-call\n        (_a = instance.ngOnChanges) === null || _a === void 0 ? void 0 : _a.call(instance, changes);\n    }\n    return instance;\n}\n\nconst parseKeyOptions = (keyOrKeyCode) => {\n    if (isNumber(keyOrKeyCode) && keyOrKeyCode) {\n        return { key: false, keyCode: keyOrKeyCode, modifiers: {} };\n    }\n    if (isString(keyOrKeyCode) && keyOrKeyCode) {\n        return parseKey(keyOrKeyCode);\n    }\n    if (isObject(keyOrKeyCode)) {\n        const parsedKey = parseKey(keyOrKeyCode.key);\n        return Object.assign(Object.assign({}, parsedKey), { keyCode: keyOrKeyCode.keyCode });\n    }\n    throw new Error('keyboard.pressKey() requires a valid key or keyCode');\n};\nconst parseKey = (keyStr) => {\n    if (keyStr.indexOf('.') < 0 || '.' === keyStr) {\n        return { key: keyStr, keyCode: false, modifiers: {} };\n    }\n    const keyParts = keyStr.split('.');\n    const key = keyParts.pop();\n    const modifiers = keyParts.reduce((mods, part) => {\n        switch (part) {\n            case 'control':\n            case 'ctrl':\n                mods.control = true;\n                return mods;\n            case 'shift':\n                mods.shift = true;\n                return mods;\n            case 'alt':\n                mods.alt = true;\n                return mods;\n            case 'meta':\n            case 'cmd':\n            case 'win':\n                mods.meta = true;\n                return mods;\n            default:\n                throw new Error(`invalid key modifier: ${part ? part : 'undefined'}, keyStr: ${keyStr}`);\n        }\n    }, { alt: false, control: false, shift: false, meta: false });\n    return { key, keyCode: false, modifiers };\n};\nconst ɵ0 = parseKey;\n\n/**\n * Credit - Angular Material\n */\n/** Creates a browser MouseEvent with the specified options. */\nfunction createMouseEvent(type, x = 0, y = 0, button = 0) {\n    const event = document.createEvent('MouseEvent');\n    event.initMouseEvent(type, true, false, window, 0, x, y, x, y, false, false, false, false, button, null);\n    // `initMouseEvent` doesn't allow us to pass the `buttons` and\n    // defaults it to 0 which looks like a fake event.\n    Object.defineProperty(event, 'buttons', { get: () => 1 });\n    return event;\n}\n/**\n * Creates a browser TouchEvent with the specified pointer coordinates.\n */\nfunction createTouchEvent(type, pageX = 0, pageY = 0) {\n    // In favor of creating events that work for most of the browsers, the event is created\n    // as a basic UI Event. The necessary details for the event will be set manually.\n    const event = new UIEvent(type, {\n        bubbles: true,\n        cancelable: true,\n        view: window,\n        detail: 0\n    });\n    // Most of the browsers don't have a \"initTouchEvent\" method that can be used to define\n    // the touch details.\n    Object.defineProperties(event, {\n        touches: { value: [{ pageX, pageY }] }\n    });\n    return event;\n}\n/** Dispatches a keydown event from an element. */\nfunction createKeyboardEvent(type, keyOrKeyCode, target) {\n    const { key, keyCode, modifiers } = parseKeyOptions(keyOrKeyCode);\n    const event = document.createEvent('KeyboardEvent');\n    const originalPreventDefault = event.preventDefault;\n    // Firefox does not support `initKeyboardEvent`, but supports `initKeyEvent`.\n    if (event.initKeyEvent) {\n        event.initKeyEvent(type, true, true, window, modifiers.control, modifiers.alt, modifiers.shift, modifiers.meta, keyCode);\n    }\n    else {\n        // `initKeyboardEvent` expects to receive modifiers as a whitespace-delimited string\n        // See https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/initKeyboardEvent\n        const modifiersStr = (modifiers.control\n            ? 'Control '\n            : '' + modifiers.alt\n                ? 'Alt '\n                : '' + modifiers.shift\n                    ? 'Shift '\n                    : '' + modifiers.meta\n                        ? 'Meta'\n                        : '').trim();\n        event.initKeyboardEvent(type, true /* canBubble */, true /* cancelable */, window /* view */, 0 /* char */, key /* key */, 0 /* location */, modifiersStr /* modifiersList */, false /* repeat */);\n    }\n    // Webkit Browsers don't set the keyCode when calling the init function.\n    // See related bug https://bugs.webkit.org/show_bug.cgi?id=16735\n    Object.defineProperties(event, {\n        keyCode: { get: () => keyCode },\n        key: { get: () => key },\n        target: { get: () => target },\n        altKey: { get: () => !!modifiers.alt },\n        ctrlKey: { get: () => !!modifiers.control },\n        shiftKey: { get: () => !!modifiers.shift },\n        metaKey: { get: () => !!modifiers.meta }\n    });\n    // IE won't set `defaultPrevented` on synthetic events so we need to do it manually.\n    // tslint:disable-next-line\n    event.preventDefault = function () {\n        Object.defineProperty(event, 'defaultPrevented', { configurable: true, get: () => true });\n        return originalPreventDefault.apply(this, arguments);\n    };\n    return event;\n}\n/** Creates a fake event object with any desired event type. */\nfunction createFakeEvent(type, canBubble = false, cancelable = true) {\n    const event = document.createEvent('Event');\n    event.initEvent(type, canBubble, cancelable);\n    return event;\n}\n\n/**\n * Credit - Angular Material\n */\n/**\n * Utility to dispatch any event on a Node.\n *\n * @publicApi\n */\nfunction dispatchEvent(node, event) {\n    node.dispatchEvent(event);\n    return event;\n}\n/**\n * Shorthand to dispatch a fake event on a specified node.\n *\n * dispatchFakeEvent(element, 'mousedown');\n *\n * @publicApi\n */\nfunction dispatchFakeEvent(node, type, canBubble) {\n    return dispatchEvent(node, createFakeEvent(type, canBubble));\n}\n/**\n * Shorthand to dispatch a keyboard event with a specified key.\n *\n *  dispatchKeyboardEvent(calendarBodyEl, 'keydown', 'LEFT_ARROW');\n *\n *  @publicApi\n */\nfunction dispatchKeyboardEvent(node, type, keyOrKeyCode, target) {\n    return dispatchEvent(node, createKeyboardEvent(type, keyOrKeyCode, target));\n}\n/**\n * Shorthand to dispatch a mouse event on the specified coordinates.\n *\n *  dispatchMouseEvent(rippleTarget, 'mousedown', 50, 75);\n *  dispatchMouseEvent(rippleTarget, 'mouseup');\n *\n *  @publicApi\n */\nfunction dispatchMouseEvent(node, type, x = 0, y = 0, event = createMouseEvent(type, x, y)) {\n    return dispatchEvent(node, event);\n}\n/**\n * Shorthand to dispatch a touch event on the specified coordinates.\n *\n * dispatchTouchEvent(rippleTarget, 'touchstart');\n *\n * @publicApi\n */\nfunction dispatchTouchEvent(node, type, x = 0, y = 0) {\n    return dispatchEvent(node, createTouchEvent(type, x, y));\n}\n\n/**\n * Patches an elements focus and blur methods to emit events consistently and predictably.\n * This is necessary, because some browsers, like IE11, will call the focus handlers asynchronously,\n * while others won't fire them at all if the browser window is not focused.\n *\n * patchElementFocus(triggerEl);\n */\nfunction patchElementFocus(element) {\n    element.focus = () => dispatchFakeEvent(element, 'focus');\n    element.blur = () => dispatchFakeEvent(element, 'blur');\n}\n\n/**\n * Focuses an input or textarea, sets its value and dispatches\n * the `input` or `textarea` event, simulating the user typing.\n * @param value Value to be set on the input.\n * @param element Element onto which to set the value.\n *\n * typeInElement('al', input);\n */\nfunction typeInElement(value, element) {\n    if (!(element instanceof HTMLInputElement) && !(element instanceof HTMLTextAreaElement)) {\n        return;\n    }\n    element.focus();\n    element.value = value;\n    dispatchFakeEvent(element, 'input', true);\n}\n\n/**\n * Focuses a select element, selects the correct options and dispatches\n * the `change` event, simulating the user selecting an option\n * @param options Options to be selected.\n * @param element Element onto which to select the options.\n * @param config Object with extra config to dispatch change event when option selected\n *\n * selectOption('al' | ['al', 'ab'], select, config);\n */\nfunction selectOption(options, element, config) {\n    if (!(element instanceof HTMLSelectElement)) {\n        return;\n    }\n    element.focus();\n    if (isString(options)) {\n        const option = element.querySelector(`option[value=\"${options}\"]`);\n        if (!option) {\n            return;\n        }\n        setOptionSelected(option, element, config);\n    }\n    else if (options instanceof HTMLOptionElement) {\n        setOptionSelected(options, element, config);\n    }\n    else {\n        if (!element.multiple) {\n            return;\n        }\n        if (isHTMLOptionElementArray(options)) {\n            options.forEach(option => setOptionSelected(option, element, config));\n        }\n        else {\n            element.querySelectorAll('option').forEach(opt => {\n                if (options.includes(opt.value)) {\n                    setOptionSelected(opt, element, config);\n                }\n            });\n        }\n    }\n}\n/**\n * Set the option in the HTMLSelectElement to selected\n * @param option HTMLOptionElement to select\n * @param select HTMLSelectElement to add the options to\n * @param config Object with extra config to dispatch change event when option selected\n *\n * setOptionSelected(option, element, config);\n */\nfunction setOptionSelected(option, select, config) {\n    option.selected = true;\n    if (config.emitEvents) {\n        dispatchFakeEvent(select, 'change', true);\n    }\n}\n\n/**\n * @internal\n */\nclass BaseSpectator {\n    inject(token) {\n        return TestBed.inject ? TestBed.inject(token) : TestBed.get(token);\n    }\n}\n\nconst KEY_UP = 'keyup';\n/**\n * @internal\n */\nclass DomSpectator extends BaseSpectator {\n    constructor(fixture, debugElement, instance, element) {\n        super();\n        this.fixture = fixture;\n        this.debugElement = debugElement;\n        this.instance = instance;\n        this.element = element;\n    }\n    inject(token) {\n        return super.inject(token);\n    }\n    detectChanges() {\n        this.fixture.detectChanges();\n    }\n    query(directiveOrSelector, options) {\n        if ((options || {}).root) {\n            if (isString(directiveOrSelector)) {\n                return document.querySelector(directiveOrSelector);\n            }\n            if (directiveOrSelector instanceof DOMSelector) {\n                return directiveOrSelector.execute(document)[0] || null;\n            }\n        }\n        return getChildren(this.debugElement)(directiveOrSelector, options)[0] || null;\n    }\n    queryAll(directiveOrSelector, options) {\n        if ((options || {}).root) {\n            if (isString(directiveOrSelector)) {\n                return Array.from(document.querySelectorAll(directiveOrSelector));\n            }\n            if (directiveOrSelector instanceof DOMSelector) {\n                return directiveOrSelector.execute(document);\n            }\n        }\n        return getChildren(this.debugElement)(directiveOrSelector, options);\n    }\n    queryLast(directiveOrSelector, options) {\n        let result = [];\n        if ((options || {}).root) {\n            if (isString(directiveOrSelector)) {\n                result = Array.from(document.querySelectorAll(directiveOrSelector));\n            }\n            if (directiveOrSelector instanceof DOMSelector) {\n                result = directiveOrSelector.execute(document);\n            }\n        }\n        else {\n            result = getChildren(this.debugElement)(directiveOrSelector, options);\n        }\n        if (result && result.length) {\n            return result[result.length - 1];\n        }\n        return null;\n    }\n    setInput(input, value) {\n        setProps(this.instance, input, value, false);\n        this.debugElement.injector.get(ChangeDetectorRef).detectChanges();\n    }\n    output(output) {\n        const observable = this.instance[output];\n        if (!(observable instanceof Observable)) {\n            throw new Error(`${output} is not an @Output`);\n        }\n        return observable;\n    }\n    tick(millis) {\n        tick(millis);\n        this.detectChanges();\n    }\n    click(selector = this.element) {\n        const element = this.getNativeElement(selector);\n        if (!(element instanceof HTMLElement)) {\n            throw new Error(`Cannot click: ${selector} is not a HTMLElement`);\n        }\n        element.click();\n        this.detectChanges();\n    }\n    blur(selector = this.element) {\n        const element = this.getNativeElement(selector);\n        if (!(element instanceof HTMLElement)) {\n            throw new Error(`Cannot blur: ${selector} is not a HTMLElement`);\n        }\n        patchElementFocus(element);\n        element.blur();\n        this.detectChanges();\n    }\n    focus(selector = this.element) {\n        const element = this.getNativeElement(selector);\n        if (!(element instanceof HTMLElement)) {\n            throw new Error(`Cannot focus: ${selector} is not a HTMLElement`);\n        }\n        patchElementFocus(element);\n        element.focus();\n        this.detectChanges();\n    }\n    dispatchMouseEvent(selector = this.element, type, x = 0, y = 0, event = createMouseEvent(type, x, y)) {\n        const element = this.getNativeElement(selector);\n        if (!(element instanceof Node)) {\n            throw new Error(`Cannot dispatch mouse event: ${selector} is not a node`);\n        }\n        const dispatchedEvent = dispatchMouseEvent(element, type, x, y, event);\n        this.detectChanges();\n        return dispatchedEvent;\n    }\n    dispatchKeyboardEvent(selector = this.element, type, keyOrKeyCode, target) {\n        const element = this.getNativeElement(selector);\n        if (!(element instanceof Node)) {\n            throw new Error(`Cannot dispatch keyboard event: ${selector} is not a node`);\n        }\n        const event = dispatchKeyboardEvent(element, type, keyOrKeyCode, target);\n        this.detectChanges();\n        return event;\n    }\n    dispatchFakeEvent(selector = this.element, type, canBubble) {\n        const event = dispatchFakeEvent(this.getNativeElement(selector), type, canBubble);\n        this.detectChanges();\n        return event;\n    }\n    triggerEventHandler(directiveOrSelector, eventName, eventObj) {\n        const debugElement = this.getDebugElement(directiveOrSelector);\n        if (!debugElement) {\n            // tslint:disable:no-console\n            console.error(`${directiveOrSelector} does not exists`);\n            return;\n        }\n        debugElement.triggerEventHandler(eventName, eventObj);\n        this.detectChanges();\n    }\n    get keyboard() {\n        return {\n            pressKey: (key, selector = this.element, event = KEY_UP) => {\n                this.dispatchKeyboardEvent(selector, event, key);\n            },\n            pressEscape: (selector = this.element, event = KEY_UP) => {\n                this.dispatchKeyboardEvent(selector, event, { key: 'Escape', keyCode: 27 });\n            },\n            pressEnter: (selector = this.element, event = KEY_UP) => {\n                this.dispatchKeyboardEvent(selector, event, { key: 'Enter', keyCode: 13 });\n            },\n            pressTab: (selector = this.element, event = KEY_UP) => {\n                this.dispatchKeyboardEvent(selector, event, { key: 'Tab', keyCode: 9 });\n            },\n            pressBackspace: (selector = this.element, event = KEY_UP) => {\n                this.dispatchKeyboardEvent(selector, event, { key: 'Backspace', keyCode: 8 });\n            }\n        };\n    }\n    get mouse() {\n        return {\n            contextmenu: (selector = this.element) => {\n                this.dispatchMouseEvent(selector, 'contextmenu');\n            },\n            dblclick: (selector = this.element) => {\n                this.dispatchMouseEvent(selector, 'dblclick');\n            }\n        };\n    }\n    dispatchTouchEvent(selector = this.element, type, x = 0, y = 0) {\n        dispatchTouchEvent(this.getNativeElement(selector), type, x, y);\n        this.detectChanges();\n    }\n    typeInElement(value, selector = this.element) {\n        typeInElement(value, this.getNativeElement(selector));\n        this.detectChanges();\n    }\n    selectOption(selector = this.element, options, config = { emitEvents: true }) {\n        if (!selector) {\n            throw new Error(`Cannot find select: ${selector}`);\n        }\n        selectOption(options, this.getNativeElement(selector), config);\n        this.detectChanges();\n    }\n    getNativeElement(selector) {\n        let element;\n        // Support global objects window and document\n        if (selector === window || selector === document) {\n            return selector;\n        }\n        if (isString(selector)) {\n            const exists = this.debugElement.query(By.css(selector));\n            if (exists) {\n                element = exists.nativeElement;\n            }\n            else {\n                // tslint:disable:no-console\n                console.error(`${selector} does not exists`);\n            }\n        }\n        else if (selector instanceof DOMSelector) {\n            element = selector.execute(document)[0] || null;\n        }\n        else {\n            if (selector instanceof DebugElement || selector instanceof ElementRef) {\n                element = selector.nativeElement;\n            }\n            else {\n                element = selector;\n            }\n        }\n        return element;\n    }\n    getDebugElement(directiveOrSelector) {\n        let debugElement;\n        if (isString(directiveOrSelector)) {\n            debugElement = this.debugElement.query(By.css(directiveOrSelector));\n        }\n        else if (directiveOrSelector instanceof DebugElement) {\n            debugElement = directiveOrSelector;\n        }\n        else {\n            debugElement = this.debugElement.query(By.directive(directiveOrSelector));\n        }\n        return debugElement;\n    }\n}\n\n/**\n * @publicApi\n */\nclass Spectator extends DomSpectator {\n    constructor(fixture, debugElement, instance, element) {\n        super(fixture, debugElement, instance, element);\n        this.fixture = fixture;\n        this.debugElement = debugElement;\n        this.instance = instance;\n        this.element = element;\n    }\n    get component() {\n        return this.instance;\n    }\n    inject(token, fromComponentInjector = false) {\n        if (fromComponentInjector) {\n            return this.debugElement.injector.get(token);\n        }\n        return super.inject(token);\n    }\n    detectComponentChanges() {\n        if (this.debugElement) {\n            this.debugElement.injector.get(ChangeDetectorRef).detectChanges();\n        }\n        else {\n            this.detectChanges();\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Netanel Basal. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NetanelBasal/spectator/blob/master/LICENSE\n */\nfunction hex2rgb(hex) {\n    const h = hex.replace('#', '');\n    const matches = h.match(new RegExp('(.{' + h.length / 3 + '})', 'g'));\n    const [r, g, b] = matches.map(match => parseInt(match.length === 1 ? match + match : match, 16));\n    return `rgb(${r},${g},${b})`;\n}\nfunction isHex(value) {\n    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value);\n}\nfunction trim(value) {\n    return (value || '').replace(/\\s/g, '');\n}\n\nfunction isRunningInJsDom() {\n    return navigator.userAgent.includes('Node.js') || navigator.userAgent.includes('jsdom');\n}\n\n/** Credit: https://github.com/unindented/custom-jquery-matchers/tree/master/packages/custom-jquery-matchers */\nconst hasProperty = (actual, expected) => {\n    return expected === undefined ? actual !== undefined : actual === expected;\n};\nconst ɵ0$1 = hasProperty;\nconst containsProperty = (actual, expected) => {\n    return expected === undefined ? true : actual.includes(expected);\n};\nconst ɵ1 = containsProperty;\nconst checkProperty = (el, prop, predicate) => {\n    let pass = false;\n    let failing = '';\n    for (const key of Object.keys(prop)) {\n        const actual = $(el).prop(key);\n        const addendum = prop[key] !== undefined ? ` with value '${prop[key]}'` : '';\n        pass = predicate(actual, prop[key]);\n        failing = !pass ? `'${prop}'${addendum}, but had '${actual}'` : '';\n    }\n    const message = () => `Expected element${pass ? ' not' : ''} to have property ${failing}`;\n    return { pass, message };\n};\nconst ɵ2 = checkProperty;\nconst hasCss = (el, css) => {\n    let prop;\n    let value;\n    const $el = $(el);\n    for (prop in css) {\n        if (css.hasOwnProperty(prop)) {\n            value = css[prop];\n            if (isHex(value)) {\n                value = hex2rgb(css[prop]);\n            }\n            if (value === 'auto' && $el.get(0).style[prop] === 'auto') {\n                continue;\n            }\n            if (trim($el.css(prop)) !== trim(value) && trim(el.style[prop]) !== trim(value)) {\n                return false;\n            }\n        }\n    }\n    return true;\n};\nconst ɵ3 = hasCss;\nconst hasSameText = (el, expected, exact = false) => {\n    if (expected && Array.isArray(expected)) {\n        let actual;\n        let pass = false;\n        let failing;\n        $(el).each((i, e) => {\n            actual = exact ? $(e).text() : $.trim($(e).text());\n            pass = exact ? actual === expected[i] : actual.includes(expected[i]);\n            if (!pass) {\n                failing = expected[i];\n                return false;\n            }\n        });\n        const message = () => `Expected element${pass ? ' not' : ''} to have ${exact ? 'exact' : ''} text '${failing}', but had '${actual}'`;\n        return { pass, message };\n    }\n    const actual = exact ? $(el).text() : $.trim($(el).text());\n    if (expected && typeof expected !== 'string') {\n        const pass = expected(actual);\n        const message = () => `Expected element${pass ? ' not' : ''} to have ${exact ? 'exact' : ''} text matching '${expected}',` + ` but had '${actual}'`;\n        return { pass, message };\n    }\n    const pass = exact && !Array.isArray(expected) ? actual === expected : actual.indexOf(expected) !== -1;\n    const message = () => `Expected element${pass ? ' not' : ''} to have ${exact ? 'exact' : ''} text '${expected}', but had '${actual}'`;\n    return { pass, message };\n};\nconst ɵ4 = hasSameText;\nconst comparator = (func) => () => ({\n    compare: func\n});\nconst ɵ5 = comparator;\nconst ɵ6 = (el) => {\n    const actual = $(el).length;\n    const pass = actual > 0;\n    const message = () => `Expected ${el} element${pass ? ' not' : ''} to exist`;\n    return { pass, message };\n};\n/**\n *\n * expect('.zippy__content').not.toExist();\n */\nconst toExist = comparator(ɵ6);\nconst ɵ7 = (el, expected) => {\n    const actual = $(el).length;\n    const pass = actual === expected;\n    const message = () => `Expected element${pass ? ' not' : ''} to have length ${expected}, but had ${actual}`;\n    return { pass, message };\n};\n/**\n *\n * expect('.zippy__content').toHaveLength(3);\n */\nconst toHaveLength = comparator(ɵ7);\nconst ɵ8 = (el, expected) => {\n    const actual = $(el).attr('id');\n    const pass = actual === expected;\n    const message = () => `Expected element${pass ? ' not' : ''} to have ID '${expected}', but had '${actual}'`;\n    return { pass, message };\n};\n/**\n *\n * expect('.zippy__content').toHaveId('ID');\n */\nconst toHaveId = comparator(ɵ8);\nconst ɵ9 = (el, expected) => {\n    if (expected && Array.isArray(expected)) {\n        const actual = $(el).attr('class');\n        const expectedClasses = expected.join(' ');\n        const pass = $(el).hasClass(expectedClasses);\n        const message = () => `Expected element${pass ? ' not' : ''} to have value '${expectedClasses}', but had '${actual}'`;\n        return { pass, message };\n    }\n    const actual = $(el).attr('class');\n    const pass = $(el).hasClass(expected);\n    const message = () => `Expected element${pass ? ' not' : ''} to have class '${expected}', but had '${actual}'`;\n    return { pass, message };\n};\n/**\n *\n * expect('.zippy__content').toHaveClass('class');\n * expect('.zippy__content').toHaveClass('class a, class b');\n * expect('.zippy__content').toHaveClass(['class a, class b']);\n */\nconst toHaveClass = comparator(ɵ9);\nconst ɵ10 = (el, attr, val) => {\n    if (isObject(attr)) {\n        let pass = false;\n        let failing;\n        for (const key of Object.keys(attr)) {\n            const actual = $(el).attr(key);\n            const addendum = attr[key] !== undefined ? ` with value '${attr[key]}'` : '';\n            pass = hasProperty(actual, attr[key]);\n            failing = !pass ? `'${attr}'${addendum}, but had '${actual}'` : '';\n        }\n        const message = () => `Expected element${pass ? ' not' : ''} to have attribute ${failing}`;\n        return { pass, message };\n    }\n    const actual = $(el).attr(attr);\n    const addendum = val !== undefined ? ` with value '${val}'` : '';\n    const pass = hasProperty(actual, val);\n    const message = () => `Expected element${pass ? ' not' : ''} to have attribute '${attr}'${addendum}, but had '${actual}'`;\n    return { pass, message };\n};\n/**\n * expect(host.query('.zippy')).toHaveAttribute('id', 'zippy');\n */\nconst toHaveAttribute = comparator(ɵ10);\nconst ɵ11 = (el, prop, val) => {\n    if (isObject(prop)) {\n        return checkProperty(el, prop, hasProperty);\n    }\n    const actual = $(el).prop(prop);\n    const addendum = val !== undefined ? ` with value '${val}'` : '';\n    const pass = hasProperty(actual, val);\n    const message = () => `Expected element${pass ? ' not' : ''} to have property '${prop}'${addendum}, but had '${actual}'`;\n    return { pass, message };\n};\n/**\n *  expect(host.query('.checkbox')).toHaveProperty('checked', true);\n *  expect(host.query('.checkbox')).toHaveProperty({checked: true});\n */\nconst toHaveProperty = comparator(ɵ11);\nconst ɵ12 = (el, prop, val) => {\n    if (isObject(prop)) {\n        return checkProperty(el, prop, containsProperty);\n    }\n    const actual = $(el).prop(prop);\n    const addendum = val !== undefined ? ` with value '${val}'` : '';\n    const pass = containsProperty(actual, val);\n    const message = () => `Expected element${pass ? ' not' : ''} to have property '${prop}'${addendum}, but had '${actual}'`;\n    return { pass, message };\n};\nconst toContainProperty = comparator(ɵ12);\nconst ɵ13 = (el, expected, exact = false) => hasSameText(el, expected, exact);\n/**\n *\n * expect('.zippy__content').toHaveText('Content');\n * expect('.zippy__content').toHaveText(['Content A', 'Content B']);\n *\n * expect('.zippy__content').toHaveText((text) => text.includes('..');\n */\nconst toHaveText = comparator(ɵ13);\nconst ɵ14 = (el, expected) => hasSameText(el, expected, true);\nconst toHaveExactText = comparator(ɵ14);\nconst toContainText = toHaveText;\nconst ɵ15 = (el, expected) => {\n    if (expected && Array.isArray(expected)) {\n        let actual;\n        let pass = false;\n        let failing;\n        $(el).each((i, e) => {\n            actual = $(e).val();\n            pass = actual === expected[i];\n            if (!pass) {\n                failing = expected[i];\n                return false;\n            }\n        });\n        const message = () => `Expected element${pass ? ' not' : ''} to have value '${failing}', but had '${actual}'`;\n        return { pass, message };\n    }\n    const actual = $(el).val();\n    const pass = actual === expected;\n    const message = () => `Expected element${pass ? ' not' : ''} to have value '${expected}', but had '${actual}'`;\n    return { pass, message };\n};\n/**\n *\n * expect('.zippy__content').toHaveValue('value');\n * expect('.zippy__content').toHaveValue(['value a', 'value b']);\n */\nconst toHaveValue = comparator(ɵ15);\nconst toContainValue = toHaveValue;\nconst ɵ16 = (el, expected) => {\n    const pass = hasCss(el, expected);\n    const message = () => `Expected element${pass ? ' not' : ''} to have CSS ${JSON.stringify(expected)}`;\n    return { pass, message };\n};\n/**\n *\n *  expect(host.element).toHaveStyle({\n *    backgroundColor: 'rgba(0, 0, 0, 0.1)'\n *  });\n */\nconst toHaveStyle = comparator(ɵ16);\nconst ɵ17 = (el, { data, val }) => {\n    const actual = $(el).data(data);\n    const addendum = val !== undefined ? ` with value '${val}'` : '';\n    const pass = hasProperty(actual, val);\n    const message = () => `Expected element${pass ? ' not' : ''} to have data '${data}'${addendum}, but had '${actual}'`;\n    return { pass, message };\n};\n/**\n *\n * expect('.zippy__content').toHaveData({data: 'role', val: 'admin'});\n */\nconst toHaveData = comparator(ɵ17);\nconst ɵ18 = el => {\n    const pass = $(el).is(':checked');\n    const message = () => `Expected element${pass ? ' not' : ''} to be checked`;\n    return { pass, message };\n};\n/**\n *\n * expect('.checkbox').toBeChecked();\n */\nconst toBeChecked = comparator(ɵ18);\nconst ɵ19 = el => {\n    const pass = $(el).is(':disabled');\n    const message = () => `Expected element${pass ? ' not' : ''} to be disabled`;\n    return { pass, message };\n};\n/**\n *\n * expect('.checkbox').toBeDisabled();\n */\nconst toBeDisabled = comparator(ɵ19);\nconst ɵ20 = el => {\n    const pass = $(el).is(':empty');\n    const message = () => `Expected element${pass ? ' not' : ''} to be empty`;\n    return { pass, message };\n};\n/**\n * An empty element is an element without child elements or text.\n *\n * expect('div').toBeEmpty();\n */\nconst toBeEmpty = comparator(ɵ20);\n/**\n * Hidden elements are elements that have:\n * 1. Display property set to \"none\"\n * 2. Width and height set to 0 (check not applied in jest)\n * 3. A hidden parent element (this also hides child elements)\n * 4. Type equal to \"hidden\" (only for form elements)\n * 5. A \"hidden\" attribute\n */\nfunction isHidden(elOrSelector) {\n    let el = $(elOrSelector)[0];\n    if (!el) {\n        return true;\n    }\n    const hiddenWhen = [\n        el => !(el.offsetWidth || el.offsetHeight || el.getClientRects().length),\n        el => el.style.display === 'none',\n        el => el.style.visibility === 'hidden',\n        el => el.type === 'hidden',\n        el => el.hasAttribute('hidden')\n    ];\n    if (isRunningInJsDom()) {\n        // When running in JSDOM (Jest), offset-properties and client rects are always reported as 0\n        // - hence, let's take a more \"naive\" approach here. (https://github.com/jsdom/jsdom/issues/135)\n        hiddenWhen.shift();\n    }\n    while (el) {\n        if (el === document) {\n            break;\n        }\n        if (hiddenWhen.some(rule => rule(el))) {\n            return true;\n        }\n        el = el.parentNode;\n    }\n    return false;\n}\nconst ɵ21 = el => {\n    const pass = isHidden(el);\n    const message = () => `Expected element${pass ? ' not' : ''} to be hidden`;\n    return { pass, message };\n};\n/**\n * Hidden elements are elements that have:\n * 1. Display property set to \"none\"\n * 2. Width and height set to 0\n * 3. A hidden parent element (this also hides child elements)\n * 4. Type equal to \"hidden\" (only for form elements)\n * 5. A \"hidden\" attribute\n *\n * expect('div').toBeHidden();\n *\n */\nconst toBeHidden = comparator(ɵ21);\nconst ɵ22 = el => {\n    const pass = $(el).is(':selected');\n    const message = () => `Expected element${pass ? ' not' : ''} to be selected`;\n    return { pass, message };\n};\n/**\n * The :selected selector selects option elements that are pre-selected.\n *\n * expect('div').toBeSelected();\n *\n */\nconst toBeSelected = comparator(ɵ22);\nconst ɵ23 = el => {\n    const pass = !isHidden(el);\n    const message = () => `Expected element${pass ? ' not' : ''} to be visible`;\n    return { pass, message };\n};\n/**\n * Hidden elements are elements that have:\n * 1. Display property set to \"none\"\n * 2. Width and height set to 0\n * 3. A hidden parent element (this also hides child elements)\n * 4. Type equal to \"hidden\" (only for form elements)\n * 5. A \"hidden\" attribute\n *\n * expect('div').toBeVisible();\n *\n */\nconst toBeVisible = comparator(ɵ23);\nconst ɵ24 = el => {\n    const element = $(el).get(0);\n    const pass = element === element.ownerDocument.activeElement;\n    const message = () => `Expected element${pass ? ' not' : ''} to be focused`;\n    return { pass, message };\n};\n/**\n * The :focus selector selects the element that currently has focus.\n *\n * expect('input').toBeFocused();\n */\nconst toBeFocused = comparator(ɵ24);\nconst ɵ25 = (el, expected) => {\n    const actual = $(el).filter(expected).length;\n    const pass = actual > 0;\n    const message = () => `Expected element${pass ? ' not' : ''} to be matched by '${expected}'`;\n    return { pass, message };\n};\n/**\n * Check to see if the set of matched elements matches the given selector\n * returns true if the dom contains the element\n *\n * expect('div').toBeMatchedBy('.js-something')\n */\nconst toBeMatchedBy = comparator(ɵ25);\nconst ɵ26 = (el, selector) => {\n    const actual = $(el).find(selector).length;\n    const pass = actual > 0;\n    const message = () => `Expected element${pass ? ' not' : ''} to contain child '${selector}'`;\n    return { pass, message };\n};\n/**\n *\n * expect('div').toHaveDescendant('.child')\n */\nconst toHaveDescendant = comparator(ɵ26);\nconst ɵ27 = (el, { selector, text }) => {\n    const actual = $.trim($(el)\n        .find(selector)\n        .text());\n    if (text && $.isFunction(text.test)) {\n        const pass = text.test(actual);\n        const message = () => `Expected element${pass ? ' not' : ''} to have descendant '${selector}' with text matching '${text}',` + ` but had '${actual}'`;\n        return { pass, message };\n    }\n    const pass = actual.indexOf(text) !== -1;\n    const message = () => `Expected element${pass ? ' not' : ''} to have descendant '${selector}' with text '${text}', but had '${actual}'`;\n    return { pass, message };\n};\n/**\n *\n * expect('div').toHaveDescendantWithText({selector: '.child', text: 'text'})\n */\nconst toHaveDescendantWithText = comparator(ɵ27);\nconst ɵ28 = (el, expected) => {\n    if (expected instanceof HTMLOptionElement) {\n        const actual = $(el).find(':selected');\n        const pass = actual.is($(expected));\n        const message = () => `Expected element${pass ? ' not' : ''} to have options '[${expected.outerHTML}]' but had '[${actual[0].outerHTML}]'`;\n        return { pass, message };\n    }\n    if (isHTMLOptionElementArray(expected)) {\n        const actual = $(el).find(':selected');\n        const pass = actual.length === expected.length && actual.toArray().every((_, index) => $(actual[index]).is(expected[index]));\n        const expectedOptionsString = $(expected)\n            .get()\n            .map(option => option.outerHTML)\n            .join(',');\n        const actualOptionsString = actual\n            .get()\n            .map(option => option.outerHTML)\n            .join(',');\n        const message = () => `Expected element${pass ? ' not' : ''} to have options '[${expectedOptionsString}]' but had '[${actualOptionsString}]'`;\n        return { pass, message };\n    }\n    const actual = $(el).val();\n    const pass = JSON.stringify([...actual]) === JSON.stringify([...expected]);\n    const expectedOptionsString = Array.isArray(expected)\n        ? expected.reduce((acc, val, i) => acc + `${i === expected.length ? '' : ','}${val}`)\n        : expected;\n    const message = () => `Expected element${pass ? ' not' : ''} to have options '[${expectedOptionsString}]' but had '[${actual}]'`;\n    return { pass, message };\n};\nconst toHaveSelectedOptions = comparator(ɵ28);\n\nvar customMatchers = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    toExist: toExist,\n    toHaveLength: toHaveLength,\n    toHaveId: toHaveId,\n    toHaveClass: toHaveClass,\n    toHaveAttribute: toHaveAttribute,\n    toHaveProperty: toHaveProperty,\n    toContainProperty: toContainProperty,\n    toHaveText: toHaveText,\n    toHaveExactText: toHaveExactText,\n    toContainText: toContainText,\n    toHaveValue: toHaveValue,\n    toContainValue: toContainValue,\n    toHaveStyle: toHaveStyle,\n    toHaveData: toHaveData,\n    toBeChecked: toBeChecked,\n    toBeDisabled: toBeDisabled,\n    toBeEmpty: toBeEmpty,\n    toBeHidden: toBeHidden,\n    toBeSelected: toBeSelected,\n    toBeVisible: toBeVisible,\n    toBeFocused: toBeFocused,\n    toBeMatchedBy: toBeMatchedBy,\n    toHaveDescendant: toHaveDescendant,\n    toHaveDescendantWithText: toHaveDescendantWithText,\n    toHaveSelectedOptions: toHaveSelectedOptions,\n    ɵ0: ɵ0$1,\n    ɵ1: ɵ1,\n    ɵ2: ɵ2,\n    ɵ3: ɵ3,\n    ɵ4: ɵ4,\n    ɵ5: ɵ5,\n    ɵ6: ɵ6,\n    ɵ7: ɵ7,\n    ɵ8: ɵ8,\n    ɵ9: ɵ9,\n    ɵ10: ɵ10,\n    ɵ11: ɵ11,\n    ɵ12: ɵ12,\n    ɵ13: ɵ13,\n    ɵ14: ɵ14,\n    ɵ15: ɵ15,\n    ɵ16: ɵ16,\n    ɵ17: ɵ17,\n    ɵ18: ɵ18,\n    ɵ19: ɵ19,\n    ɵ20: ɵ20,\n    ɵ21: ɵ21,\n    ɵ22: ɵ22,\n    ɵ23: ɵ23,\n    ɵ24: ɵ24,\n    ɵ25: ɵ25,\n    ɵ26: ɵ26,\n    ɵ27: ɵ27,\n    ɵ28: ɵ28\n});\n\nfunction addMatchers(matchers) {\n    if (!matchers)\n        return;\n    if (typeof jasmine !== 'undefined') {\n        jasmine.addMatchers(matchers);\n    }\n    if (typeof jest !== 'undefined') {\n        const jestExpectExtend = {};\n        for (const key of Object.keys(matchers)) {\n            if (key.startsWith('to'))\n                jestExpectExtend[key] = matchers[key]().compare;\n        }\n        expect.extend(jestExpectExtend);\n    }\n}\n\nlet globals = {\n    providers: [],\n    declarations: [],\n    imports: []\n};\nfunction defineGlobalsInjections(config) {\n    globals = Object.assign(Object.assign({}, globals), config);\n}\nfunction getGlobalsInjections() {\n    return globals;\n}\n\n/**\n * @internal\n */\nfunction initialModule(options) {\n    const globals = Object.assign({ imports: [], declarations: [], providers: [] }, getGlobalsInjections());\n    return {\n        declarations: [...globals.declarations, ...options.declarations, ...options.entryComponents],\n        imports: [...(options.disableAnimations ? [NoopAnimationsModule] : []), ...globals.imports, ...options.imports],\n        providers: [...globals.providers, ...options.providers, ...options.mocks.map(type => options.mockProvider(type))],\n        entryComponents: [...options.entryComponents]\n    };\n}\n\n/**\n * @internal\n */\nfunction initialSpectatorModule(options) {\n    const moduleMetadata = initialModule(options);\n    if (options.declareComponent) {\n        moduleMetadata.declarations.push(options.component);\n    }\n    moduleMetadata.schemas = [options.shallow ? NO_ERRORS_SCHEMA : options.schemas || []];\n    return moduleMetadata;\n}\n\n/**\n * @internal\n */\nfunction merge(defaults, overrides) {\n    // tslint:disable-next-line:no-object-literal-type-assertion\n    return Object.assign(Object.assign({}, defaults), overrides);\n}\n\n/**\n * @internal\n */\nfunction installProtoMethods(mock, proto, createSpyFn) {\n    if (proto === null || proto === Object.prototype) {\n        return;\n    }\n    for (const key of Object.getOwnPropertyNames(proto)) {\n        const descriptor = Object.getOwnPropertyDescriptor(proto, key);\n        if (!descriptor) {\n            continue;\n        }\n        if (typeof descriptor.value === 'function' && key !== 'constructor' && typeof mock[key] === 'undefined') {\n            mock[key] = createSpyFn(key);\n        }\n        else if (descriptor.get && !mock.hasOwnProperty(key)) {\n            Object.defineProperty(mock, key, {\n                set: value => (mock[`_${key}`] = value),\n                get: () => mock[`_${key}`],\n                configurable: true\n            });\n        }\n    }\n    installProtoMethods(mock, Object.getPrototypeOf(proto), createSpyFn);\n    mock.castToWritable = () => mock;\n}\n/**\n * @publicApi\n */\nfunction createSpyObject(type, template) {\n    const mock = Object.assign({}, template) || {};\n    installProtoMethods(mock, type.prototype, name => {\n        const newSpy = jasmine.createSpy(name);\n        newSpy.andCallFake = (fn) => newSpy.and.callFake(fn);\n        newSpy.andReturn = val => newSpy.and.returnValue(val);\n        newSpy.reset = () => newSpy.calls.reset();\n        // revisit return null here (previously needed for rtts_assert).\n        newSpy.and.returnValue(null);\n        return newSpy;\n    });\n    return mock;\n}\n/**\n * @publicApi\n */\nfunction mockProvider(type, properties) {\n    return {\n        provide: type,\n        useFactory: () => createSpyObject(type, properties)\n    };\n}\n\nconst defaultOptions = {\n    disableAnimations: true,\n    entryComponents: [],\n    mocks: [],\n    mockProvider,\n    providers: [],\n    declarations: [],\n    imports: [],\n    schemas: [],\n    overrideModules: []\n};\n/**\n * @internal\n */\nfunction getDefaultBaseOptions(options) {\n    return merge(defaultOptions, options);\n}\n\nconst defaultSpectatorOptions = Object.assign(Object.assign({}, getDefaultBaseOptions()), { shallow: false, declareComponent: true, detectChanges: true, componentProviders: [], componentViewProviders: [], componentMocks: [], componentViewProvidersMocks: [] });\n/**\n * @internal\n */\nfunction getSpectatorDefaultOptions(overrides) {\n    return merge(defaultSpectatorOptions, overrides);\n}\n\n/**\n * @internal\n */\nfunction overrideComponentIfProviderOverridesSpecified(options) {\n    const hasProviderOverrides = options.componentProviders.length || options.componentMocks.length;\n    const hasViewProviders = options.componentViewProviders.length || options.componentViewProvidersMocks.length;\n    if (hasProviderOverrides || hasViewProviders) {\n        let providerConfiguration = {};\n        if (hasProviderOverrides) {\n            providerConfiguration = {\n                providers: [...options.componentProviders, ...options.componentMocks.map(p => options.mockProvider(p))]\n            };\n        }\n        if (hasViewProviders) {\n            providerConfiguration = Object.assign(Object.assign({}, providerConfiguration), { viewProviders: [...options.componentViewProviders, ...options.componentViewProvidersMocks.map(p => options.mockProvider(p))] });\n        }\n        TestBed.overrideComponent(options.component, {\n            set: providerConfiguration\n        });\n    }\n}\n/**\n * @internal\n */\nfunction overrideModules(options) {\n    if (options.overrideModules.length) {\n        options.overrideModules.forEach(overrideModule => {\n            const [ngModule, override] = overrideModule;\n            TestBed.overrideModule(ngModule, override);\n        });\n    }\n}\n/**\n * @publicApi\n */\nfunction createComponentFactory(typeOrOptions) {\n    const options = isType(typeOrOptions)\n        ? getSpectatorDefaultOptions({ component: typeOrOptions })\n        : getSpectatorDefaultOptions(typeOrOptions);\n    const moduleMetadata = initialSpectatorModule(options);\n    beforeEach(async(() => {\n        addMatchers(customMatchers);\n        TestBed.configureTestingModule(moduleMetadata).overrideModule(BrowserDynamicTestingModule, {\n            set: {\n                entryComponents: moduleMetadata.entryComponents\n            }\n        });\n        overrideModules(options);\n        overrideComponentIfProviderOverridesSpecified(options);\n        TestBed.compileComponents();\n    }));\n    return (overrides) => {\n        const defaults = { props: {}, detectChanges: true, providers: [] };\n        const { detectChanges, props, providers } = Object.assign(Object.assign({}, defaults), overrides);\n        if (providers && providers.length) {\n            providers.forEach((provider) => {\n                TestBed.overrideProvider(provider.provide, provider);\n            });\n        }\n        const spectator = createSpectator(options, props);\n        if (options.detectChanges && detectChanges) {\n            spectator.detectChanges();\n        }\n        return spectator;\n    };\n}\nfunction createSpectator(options, props) {\n    const fixture = TestBed.createComponent(options.component);\n    const debugElement = fixture.debugElement;\n    const component = setProps(fixture.componentInstance, props);\n    return new Spectator(fixture, debugElement, component, debugElement.nativeElement);\n}\n\n/**\n * @publicApi\n */\nclass SpectatorHost extends Spectator {\n    constructor(hostComponent, hostDebugElement, hostElement, hostFixture, debugElement, componentInstance, element) {\n        super(hostFixture, debugElement, componentInstance, element);\n        this.hostComponent = hostComponent;\n        this.hostDebugElement = hostDebugElement;\n        this.hostElement = hostElement;\n        this.hostFixture = hostFixture;\n        this.debugElement = debugElement;\n        this.element = element;\n    }\n    queryHost(directiveOrSelector, options) {\n        if ((options || {}).root && isString(directiveOrSelector)) {\n            return document.querySelector(directiveOrSelector);\n        }\n        return getChildren(this.hostDebugElement)(directiveOrSelector, options)[0] || null;\n    }\n    queryHostAll(directiveOrSelector, options) {\n        if ((options || {}).root && isString(directiveOrSelector)) {\n            return Array.from(document.querySelectorAll(directiveOrSelector));\n        }\n        return getChildren(this.hostDebugElement)(directiveOrSelector, options);\n    }\n    setHostInput(input, value) {\n        setProps(this.hostComponent, input, value, false);\n        this.detectChanges();\n    }\n}\n\n// TODO (dirkluijk): remove after upgrading to Angular 8.2\n// see: https://github.com/angular/angular/commit/10a1e1974b816ebb979dc10586b160ee07ad8356\nfunction nodeByDirective(type) {\n    return debugNode => debugNode.providerTokens.includes(type);\n}\n\n/**\n * @internal\n */\nfunction initialSpectatorWithHostModule(options) {\n    const moduleMetadata = initialSpectatorModule(options);\n    moduleMetadata.declarations.push(options.host);\n    return moduleMetadata;\n}\n\nclass HostComponent {\n}\nHostComponent.decorators = [\n    { type: Component, args: [{\n                template: ''\n            },] }\n];\n/*\n  This is an unused module to resolve the ng build error:\n    'Cannot determine the module for class HostComponent'\n\n  Reference: https://github.com/angular/issues/13590\n*/\nclass HostModule {\n}\nHostModule.decorators = [\n    { type: NgModule, args: [{\n                declarations: [HostComponent]\n            },] }\n];\n\nconst defaultSpectatorHostOptions = Object.assign(Object.assign({}, getSpectatorDefaultOptions()), { host: HostComponent, template: '' });\n/**\n * @internal\n */\nfunction getSpectatorHostDefaultOptions(overrides) {\n    return merge(defaultSpectatorHostOptions, overrides);\n}\n\nfunction createHostFactory(typeOrOptions) {\n    const options = isType(typeOrOptions)\n        ? getSpectatorHostDefaultOptions({ component: typeOrOptions })\n        : getSpectatorHostDefaultOptions(typeOrOptions);\n    const moduleMetadata = initialSpectatorWithHostModule(options);\n    beforeEach(async(() => {\n        addMatchers(customMatchers);\n        TestBed.configureTestingModule(moduleMetadata);\n        overrideModules(options);\n        overrideComponentIfProviderOverridesSpecified(options);\n    }));\n    return (template, overrides) => {\n        const defaults = { props: {}, hostProps: {}, detectChanges: true, providers: [] };\n        const { detectChanges, props, hostProps, providers } = Object.assign(Object.assign({}, defaults), overrides);\n        if (providers && providers.length) {\n            providers.forEach((provider) => {\n                TestBed.overrideProvider(provider.provide, provider);\n            });\n        }\n        TestBed.overrideModule(BrowserDynamicTestingModule, {\n            set: {\n                entryComponents: moduleMetadata.entryComponents\n            }\n        }).overrideComponent(options.host, {\n            set: { template: template || options.template }\n        });\n        const spectator = createSpectatorHost(options, props, hostProps);\n        if (options.detectChanges && detectChanges) {\n            spectator.detectChanges();\n        }\n        return spectator;\n    };\n}\nfunction createSpectatorHost(options, props, hostProps) {\n    const hostFixture = TestBed.createComponent(options.host);\n    const debugElement = hostFixture.debugElement.query(By.directive(options.component)) || hostFixture.debugElement;\n    const debugNode = hostFixture.debugElement.queryAllNodes(nodeByDirective(options.component))[0];\n    if (!debugNode) {\n        throw new Error(`Cannot find component/directive ${options.component} in host template 😔`);\n    }\n    const hostComponent = setProps(hostFixture.componentInstance, hostProps);\n    const component = setProps(debugNode.injector.get(options.component), props);\n    return new SpectatorHost(hostComponent, hostFixture.debugElement, hostFixture.nativeElement, hostFixture, debugElement, component, debugElement.nativeElement);\n}\n\n/**\n * @publicApi\n */\nclass SpectatorDirective extends DomSpectator {\n    constructor(hostComponent, fixture, debugElement, instance, element) {\n        super(fixture, debugElement, instance, element);\n        this.hostComponent = hostComponent;\n        this.fixture = fixture;\n        this.debugElement = debugElement;\n        this.instance = instance;\n        this.element = element;\n    }\n    get directive() {\n        return this.instance;\n    }\n    inject(token, fromDirectiveInjector = false) {\n        if (fromDirectiveInjector) {\n            return this.debugElement.injector.get(token);\n        }\n        return super.inject(token);\n    }\n    setHostInput(input, value) {\n        setProps(this.hostComponent, input, value, false);\n        this.detectChanges();\n    }\n}\n\n/**\n * @internal\n */\nfunction initialSpectatorDirectiveModule(options) {\n    const moduleMetadata = initialModule(options);\n    if (options.declareDirective) {\n        moduleMetadata.declarations.push(options.directive);\n    }\n    moduleMetadata.declarations.push(options.host);\n    moduleMetadata.schemas = [options.shallow ? NO_ERRORS_SCHEMA : options.schemas || []];\n    return moduleMetadata;\n}\n\nconst defaultSpectatorRoutingOptions = Object.assign(Object.assign({}, getDefaultBaseOptions()), { host: HostComponent, template: '', shallow: false, detectChanges: true, directiveProviders: [], directiveMocks: [], declareDirective: true });\n/**\n * @internal\n */\nfunction getSpectatorDirectiveDefaultOptions(overrides) {\n    return merge(defaultSpectatorRoutingOptions, overrides);\n}\n\nfunction createDirectiveFactory(typeOrOptions) {\n    const options = isType(typeOrOptions)\n        ? getSpectatorDirectiveDefaultOptions({ directive: typeOrOptions })\n        : getSpectatorDirectiveDefaultOptions(typeOrOptions);\n    const moduleMetadata = initialSpectatorDirectiveModule(options);\n    beforeEach(async(() => {\n        addMatchers(customMatchers);\n        TestBed.configureTestingModule(moduleMetadata);\n        overrideModules(options);\n    }));\n    return (template, overrides) => {\n        const defaults = {\n            props: {},\n            hostProps: {},\n            detectChanges: true,\n            providers: []\n        };\n        const { detectChanges, props, hostProps, providers } = Object.assign(Object.assign({}, defaults), overrides);\n        if (providers && providers.length) {\n            providers.forEach((provider) => {\n                TestBed.overrideProvider(provider.provide, provider);\n            });\n        }\n        TestBed.overrideModule(BrowserDynamicTestingModule, {\n            set: {\n                entryComponents: moduleMetadata.entryComponents\n            }\n        }).overrideComponent(options.host, {\n            set: { template: template || options.template }\n        });\n        if (options.directiveProviders.length || options.directiveMocks.length) {\n            TestBed.overrideDirective(options.directive, {\n                set: { providers: [...options.directiveProviders, ...options.directiveMocks.map(p => options.mockProvider(p))] }\n            });\n        }\n        const spectator = createSpectatorDirective(options, props, hostProps);\n        if (options.detectChanges && detectChanges) {\n            spectator.detectChanges();\n        }\n        return spectator;\n    };\n}\nfunction createSpectatorDirective(options, props, hostProps) {\n    const hostFixture = TestBed.createComponent(options.host);\n    const debugElement = hostFixture.debugElement.query(By.directive(options.directive)) || hostFixture.debugElement;\n    const debugNode = hostFixture.debugElement.queryAllNodes(nodeByDirective(options.directive))[0];\n    if (!debugNode) {\n        throw new Error(`Cannot find directive ${options.directive} in host template 😔`);\n    }\n    const hostComponent = setProps(hostFixture.componentInstance, hostProps);\n    const directive = setProps(debugNode.injector.get(options.directive), props);\n    return new SpectatorDirective(hostComponent, hostFixture, hostFixture.debugElement, directive, debugElement.nativeElement);\n}\n\n/**\n * @publicApi\n */\nclass SpectatorService extends BaseSpectator {\n    constructor(service) {\n        super();\n        this.service = service;\n    }\n}\n\n/**\n * @internal\n */\nfunction initialServiceModule(options) {\n    const moduleMetadata = initialModule(options);\n    moduleMetadata.providers.push(options.service);\n    return moduleMetadata;\n}\n\nconst defaultServiceOptions = Object.assign({}, getDefaultBaseOptions());\n/**\n * @internal\n */\nfunction getDefaultServiceOptions(overrides) {\n    return merge(defaultServiceOptions, overrides);\n}\n\n/**\n * @publicApi\n */\nfunction createServiceFactory(typeOrOptions) {\n    const service = isType(typeOrOptions) ? typeOrOptions : typeOrOptions.service;\n    const options = isType(typeOrOptions) ? getDefaultServiceOptions({ service }) : getDefaultServiceOptions(typeOrOptions);\n    const moduleMetadata = initialServiceModule(options);\n    beforeEach(() => {\n        TestBed.configureTestingModule(moduleMetadata);\n        overrideModules(options);\n    });\n    afterEach(() => {\n        const testedService = TestBed.inject\n            ? TestBed.inject(service)\n            : TestBed.get(service);\n        if (doesServiceImplementsOnDestroy(testedService)) {\n            // tslint:disable-next-line:no-life-cycle-call\n            testedService.ngOnDestroy();\n        }\n    });\n    return (overrides) => {\n        const defaults = { providers: [] };\n        const { providers } = Object.assign(Object.assign({}, defaults), overrides);\n        if (providers && providers.length) {\n            providers.forEach((provider) => {\n                TestBed.overrideProvider(provider.provide, provider);\n            });\n        }\n        return new SpectatorService(TestBed.inject ? TestBed.inject(service) : TestBed.get(service));\n    };\n}\n\nclass RouterStub extends Router {\n}\nfunction isRouterStub(router) {\n    return 'emitRouterEvent' in router;\n}\n\n/**\n * @publicApi\n */\nclass SpectatorRouting extends Spectator {\n    constructor(fixture, debugElement, instance, router, activatedRouteStub) {\n        super(fixture, debugElement, instance, debugElement.nativeElement);\n        this.router = router;\n        this.activatedRouteStub = activatedRouteStub;\n    }\n    /**\n     * Simulates a route navigation by updating the Params, QueryParams and Data observable streams.\n     */\n    triggerNavigation(options) {\n        if (!this.checkStubPresent()) {\n            return;\n        }\n        if (options && options.params) {\n            this.activatedRouteStub.setParams(options.params);\n        }\n        if (options && options.queryParams) {\n            this.activatedRouteStub.setQueryParams(options.queryParams);\n        }\n        if (options && options.data) {\n            this.activatedRouteStub.setAllData(options.data);\n        }\n        if (options && options.fragment) {\n            this.activatedRouteStub.setFragment(options.fragment);\n        }\n        this.triggerNavigationAndUpdate();\n    }\n    /**\n     * Updates the route params and triggers a route navigation.\n     */\n    setRouteParam(name, value) {\n        if (this.checkStubPresent()) {\n            this.activatedRouteStub.setParam(name, value);\n            this.triggerNavigationAndUpdate();\n        }\n    }\n    /**\n     * Updates the route query params and triggers a route navigation.\n     */\n    setRouteQueryParam(name, value) {\n        if (this.checkStubPresent()) {\n            this.activatedRouteStub.setQueryParam(name, value);\n            this.triggerNavigationAndUpdate();\n        }\n    }\n    /**\n     * Updates the route data and triggers a route navigation.\n     */\n    setRouteData(name, value) {\n        if (this.checkStubPresent()) {\n            this.activatedRouteStub.setData(name, value);\n            this.triggerNavigationAndUpdate();\n        }\n    }\n    /**\n     * Updates the route fragment and triggers a route navigation.\n     */\n    setRouteFragment(fragment) {\n        if (this.checkStubPresent()) {\n            this.activatedRouteStub.setFragment(fragment);\n            this.triggerNavigationAndUpdate();\n        }\n    }\n    /**\n     * Updates the route url and triggers a route navigation.\n     */\n    setRouteUrl(url) {\n        if (this.checkStubPresent()) {\n            this.activatedRouteStub.setUrl(url);\n            this.triggerNavigationAndUpdate();\n        }\n    }\n    /**\n     * Emits a router event\n     */\n    emitRouterEvent(event) {\n        if (!isRouterStub(this.router)) {\n            // tslint:disable-next-line:no-console\n            console.warn('No stub for Router present. Set Spectator option \"stubsEnabled\" to true if you want to use this ' +\n                'helper, or use Router navigation to trigger events.');\n            return;\n        }\n        this.router.emitRouterEvent(event);\n    }\n    triggerNavigationAndUpdate() {\n        this.activatedRouteStub.triggerNavigation();\n        this.detectChanges();\n    }\n    checkStubPresent() {\n        if (!this.activatedRouteStub) {\n            // tslint:disable-next-line:no-console\n            console.warn('No stub for ActivatedRoute present. Set Spectator option \"stubsEnabled\" to true if you want to use this ' +\n                'helper, or use Router to trigger navigation.');\n            return false;\n        }\n        return true;\n    }\n}\n\n/**\n * @publicApi\n *\n * Utility class for stubbing ActivatedRoute of @angular/router\n */\nclass ActivatedRouteStub extends ActivatedRoute {\n    constructor(options) {\n        super();\n        this.testParams = {};\n        this.testQueryParams = {};\n        this.testData = {};\n        this.testFragment = null;\n        this.testUrl = [];\n        this.testRoot = null;\n        this.testParent = null;\n        this.testFirstChild = null;\n        this.testChildren = null;\n        this.paramsSubject = new ReplaySubject(1);\n        this.queryParamsSubject = new ReplaySubject(1);\n        this.dataSubject = new ReplaySubject(1);\n        this.fragmentSubject = new ReplaySubject(1);\n        this.urlSubject = new ReplaySubject(1);\n        if (options) {\n            this.testParams = options.params || {};\n            this.testQueryParams = options.queryParams || {};\n            this.testData = options.data || {};\n            this.testFragment = options.fragment || null;\n            this.testUrl = options.url || [];\n            this.testRoot = options.root || null;\n            this.testParent = options.parent || null;\n            this.testFirstChild = options.firstChild || null;\n            this.testChildren = options.children || null;\n        }\n        this.params = this.paramsSubject.asObservable();\n        this.queryParams = this.queryParamsSubject.asObservable();\n        this.data = this.dataSubject.asObservable();\n        this.fragment = this.fragmentSubject.asObservable();\n        this.url = this.urlSubject.asObservable();\n        this.snapshot = this.buildSnapshot();\n        this.triggerNavigation();\n    }\n    get paramMap() {\n        return this.paramsSubject.asObservable().pipe(map(params => convertToParamMap(params)));\n    }\n    setParams(params) {\n        this.testParams = params;\n        this.snapshot = this.buildSnapshot();\n    }\n    setParam(name, value) {\n        this.testParams = Object.assign(Object.assign({}, this.testParams), { [name]: value });\n        this.snapshot = this.buildSnapshot();\n    }\n    setQueryParams(queryParams) {\n        this.testQueryParams = queryParams;\n        this.snapshot = this.buildSnapshot();\n    }\n    setQueryParam(name, value) {\n        this.testQueryParams = Object.assign(Object.assign({}, this.testQueryParams), { [name]: value });\n        this.snapshot = this.buildSnapshot();\n    }\n    setAllData(data) {\n        this.testData = data;\n        this.snapshot = this.buildSnapshot();\n    }\n    setData(name, value) {\n        this.testData = Object.assign(Object.assign({}, this.testData), { [name]: value });\n        this.snapshot = this.buildSnapshot();\n    }\n    setFragment(fragment) {\n        this.testFragment = fragment;\n        this.snapshot = this.buildSnapshot();\n    }\n    setUrl(url) {\n        this.testUrl = url;\n        this.snapshot = this.buildSnapshot();\n    }\n    get root() {\n        return this.testRoot || this;\n    }\n    get parent() {\n        return this.testParent || null;\n    }\n    get children() {\n        return this.testChildren || [this];\n    }\n    get firstChild() {\n        return this.testFirstChild || null;\n    }\n    /**\n     * Simulates a route navigation by updating the Params, QueryParams and Data observable streams.\n     */\n    triggerNavigation() {\n        this.paramsSubject.next(this.testParams);\n        this.queryParamsSubject.next(this.testQueryParams);\n        this.dataSubject.next(this.testData);\n        this.fragmentSubject.next(this.testFragment);\n        this.urlSubject.next(this.testUrl);\n    }\n    toString() {\n        return 'activatedRouteStub';\n    }\n    buildSnapshot() {\n        const snapshot = new ActivatedRouteSnapshot();\n        snapshot.params = this.testParams;\n        snapshot.queryParams = this.testQueryParams;\n        snapshot.data = this.testData;\n        snapshot.fragment = this.testFragment;\n        snapshot.url = this.testUrl;\n        return snapshot;\n    }\n}\n\n// tslint:disable\nclass RouterLinkDirectiveStub {\n    constructor() {\n        this.navigatedTo = null;\n    }\n    onClick() {\n        this.navigatedTo = this.routerLink;\n        return true;\n    }\n}\nRouterLinkDirectiveStub.decorators = [\n    { type: Directive, args: [{\n                selector: '[routerLink]',\n                providers: [\n                    {\n                        provide: RouterLink,\n                        useExisting: RouterLinkDirectiveStub\n                    }\n                ]\n            },] }\n];\nRouterLinkDirectiveStub.propDecorators = {\n    routerLink: [{ type: Input }],\n    onClick: [{ type: HostListener, args: ['click',] }]\n};\n/*\n  This is an unused module to resolve the ng build error:\n    'Cannot determine the module for class RouterLinkDirectiveStub'\n\n  Reference: https://github.com/angular/issues/13590\n*/\nclass RouterLinkDirectiveStubModule {\n}\nRouterLinkDirectiveStubModule.decorators = [\n    { type: NgModule, args: [{\n                declarations: [RouterLinkDirectiveStub]\n            },] }\n];\n\n/**\n * @internal\n */\nfunction initialRoutingModule(options) {\n    const moduleMetadata = initialSpectatorModule(options);\n    if (options.mockRouterLinks && options.stubsEnabled) {\n        moduleMetadata.declarations.push(RouterLinkDirectiveStub);\n    }\n    if (options.stubsEnabled) {\n        moduleMetadata.providers.push(options.mockProvider(RouterStub, {\n            events: new Subject(),\n            emitRouterEvent(event) {\n                this.events.next(event);\n            }\n        }), {\n            provide: Router,\n            useExisting: RouterStub\n        });\n        moduleMetadata.providers.push({\n            provide: ActivatedRouteStub,\n            useValue: new ActivatedRouteStub({\n                params: options.params,\n                queryParams: options.queryParams,\n                data: options.data\n            })\n        }, {\n            provide: ActivatedRoute,\n            useExisting: ActivatedRouteStub\n        });\n    }\n    else {\n        moduleMetadata.imports.push(RouterTestingModule.withRoutes(options.routes));\n    }\n    return moduleMetadata;\n}\n\nconst ɵ0$2 = {};\nconst defaultRoutingOptions = Object.assign(Object.assign({}, getSpectatorDefaultOptions()), { params: {}, queryParams: {}, data: ɵ0$2, fragment: null, mockRouterLinks: true, stubsEnabled: true, routes: [], url: [], root: null, parent: null, children: null, firstChild: null });\n/**\n * @internal\n */\nfunction getRoutingDefaultOptions(overrides) {\n    return merge(defaultRoutingOptions, overrides);\n}\n\n/**\n * @publicApi\n */\nfunction createRoutingFactory(typeOrOptions) {\n    const options = isType(typeOrOptions)\n        ? getRoutingDefaultOptions({ component: typeOrOptions })\n        : getRoutingDefaultOptions(typeOrOptions);\n    const moduleMetadata = initialRoutingModule(options);\n    beforeEach(async(() => {\n        addMatchers(customMatchers);\n        TestBed.configureTestingModule(moduleMetadata);\n        overrideModules(options);\n        overrideComponentIfProviderOverridesSpecified(options);\n        TestBed.compileComponents();\n    }));\n    return (overrides) => {\n        const defaults = {\n            props: {},\n            detectChanges: true,\n            providers: []\n        };\n        const { detectChanges, props, providers } = Object.assign(Object.assign({}, defaults), overrides);\n        if (providers && providers.length) {\n            providers.forEach((provider) => {\n                TestBed.overrideProvider(provider.provide, provider);\n            });\n        }\n        const { params, queryParams, data, fragment, url, root, parent, children, firstChild } = Object.assign(Object.assign({}, options), overrides);\n        TestBed.overrideProvider(ActivatedRoute, {\n            useValue: new ActivatedRouteStub({ params, queryParams, data, fragment, url, root, parent, children, firstChild })\n        });\n        const ngZone = TestBed.inject ? TestBed.inject(NgZone) : TestBed.get(NgZone);\n        return ngZone.run(() => {\n            const spectator = createSpectatorRouting(options, props);\n            spectator.router.initialNavigation();\n            if (options.detectChanges && detectChanges) {\n                spectator.detectChanges();\n            }\n            return spectator;\n        });\n    };\n}\nfunction createSpectatorRouting(options, props) {\n    const fixture = TestBed.createComponent(options.component);\n    const debugElement = fixture.debugElement;\n    const component = setProps(fixture.componentInstance, props);\n    /**\n     * Back compatibility, angular under 9 version doesnt have a inject function\n     */\n    if (!TestBed.inject) {\n        return new SpectatorRouting(fixture, debugElement, component, TestBed.get(Router), TestBed.get(ActivatedRoute));\n    }\n    return new SpectatorRouting(fixture, debugElement, component, TestBed.inject(Router), TestBed.inject(ActivatedRoute));\n}\n\n/**\n * @publicApi\n */\nvar HttpMethod;\n(function (HttpMethod) {\n    HttpMethod[\"GET\"] = \"GET\";\n    HttpMethod[\"POST\"] = \"POST\";\n    HttpMethod[\"DELETE\"] = \"DELETE\";\n    HttpMethod[\"PUT\"] = \"PUT\";\n    HttpMethod[\"PATCH\"] = \"PATCH\";\n    HttpMethod[\"HEAD\"] = \"HEAD\";\n    HttpMethod[\"JSONP\"] = \"JSONP\";\n    HttpMethod[\"OPTIONS\"] = \"OPTIONS\";\n})(HttpMethod || (HttpMethod = {}));\n/**\n * @publicApi\n */\nclass SpectatorHttp extends BaseSpectator {\n    constructor(service, httpClient, controller) {\n        super();\n        this.service = service;\n        this.httpClient = httpClient;\n        this.controller = controller;\n        // small workaround to prevent issues if destructuring SpectatorHttp, which was common in Spectator 3\n        // remove in v5?\n        this.expectOne = this.expectOne.bind(this);\n        this.expectConcurrent = this.expectConcurrent.bind(this);\n    }\n    expectOne(url, method) {\n        expect(true).toBe(true); // workaround to avoid `Spec has no expectations` https://github.com/NetanelBasal/spectator/issues/75\n        const req = this.controller.expectOne({\n            url,\n            method\n        });\n        // assert that there are no outstanding requests.\n        this.controller.verify();\n        return req;\n    }\n    expectConcurrent(expectations) {\n        const requests = expectations.map((expectation) => {\n            return this.controller.expectOne({\n                url: expectation.url,\n                method: expectation.method\n            });\n        });\n        this.controller.verify();\n        return requests;\n    }\n    flushAll(requests, args) {\n        requests.forEach((request, idx) => {\n            request.flush(args[idx]);\n        });\n    }\n}\n\n/**\n * @internal\n */\nfunction initialHttpModule(options) {\n    const moduleMetadata = initialModule(options);\n    moduleMetadata.providers.push(options.service);\n    moduleMetadata.imports.push(HttpClientTestingModule);\n    return moduleMetadata;\n}\n\nconst defaultHttpOptions = Object.assign({}, getDefaultBaseOptions());\n/**\n * @internal\n */\nfunction getDefaultHttpOptions(overrides) {\n    return merge(defaultHttpOptions, overrides);\n}\n\n/**\n * @publicApi\n */\nfunction createHttpFactory(typeOrOptions) {\n    const service = isType(typeOrOptions) ? typeOrOptions : typeOrOptions.service;\n    const options = isType(typeOrOptions) ? getDefaultHttpOptions({ service }) : getDefaultHttpOptions(typeOrOptions);\n    const moduleMetadata = initialHttpModule(options);\n    beforeEach(() => {\n        TestBed.configureTestingModule(moduleMetadata);\n        overrideModules(options);\n    });\n    afterEach(() => {\n        if (TestBed.inject) {\n            TestBed.inject(HttpTestingController).verify();\n        }\n        else {\n            TestBed.get(HttpTestingController).verify();\n        }\n    });\n    return (overrides) => {\n        const defaults = { providers: [] };\n        const { providers } = Object.assign(Object.assign({}, defaults), overrides);\n        if (providers && providers.length) {\n            providers.forEach((provider) => {\n                TestBed.overrideProvider(provider.provide, provider);\n            });\n        }\n        /**\n         * Back compatibility, angular under 9 version doesnt have a inject function\n         */\n        if (!TestBed.inject) {\n            return new SpectatorHttp(TestBed.get(service), TestBed.get(HttpClient), TestBed.get(HttpTestingController));\n        }\n        return new SpectatorHttp(TestBed.inject(service), TestBed.inject(HttpClient), TestBed.inject(HttpTestingController));\n    };\n}\n\n/**\n * @publicApi\n */\nclass SpectatorPipe extends BaseSpectator {\n    constructor(hostComponent, fixture, debugElement, element) {\n        super();\n        this.hostComponent = hostComponent;\n        this.fixture = fixture;\n        this.debugElement = debugElement;\n        this.element = element;\n    }\n    detectChanges() {\n        this.fixture.detectChanges();\n    }\n    setHostInput(input, value) {\n        setProps(this.hostComponent, input, value, false);\n        this.detectChanges();\n    }\n}\n\n/**\n * @internal\n */\nfunction initialSpectatorPipeModule(options) {\n    const moduleMetadata = initialModule(options);\n    moduleMetadata.declarations.push(options.pipe);\n    moduleMetadata.declarations.push(options.host);\n    return moduleMetadata;\n}\n\nconst defaultSpectatorPipeOptions = Object.assign(Object.assign({}, getDefaultBaseOptions()), { host: HostComponent, detectChanges: true, template: '' });\n/**\n * @internal\n */\nfunction getSpectatorPipeDefaultOptions(overrides) {\n    return merge(defaultSpectatorPipeOptions, overrides);\n}\n\n/**\n * @publicApi\n */\nfunction createPipeFactory(typeOrOptions) {\n    const options = isType(typeOrOptions)\n        ? getSpectatorPipeDefaultOptions({ pipe: typeOrOptions })\n        : getSpectatorPipeDefaultOptions(typeOrOptions);\n    const moduleMetadata = initialSpectatorPipeModule(options);\n    beforeEach(async(() => {\n        addMatchers(customMatchers);\n        TestBed.configureTestingModule(moduleMetadata);\n        overrideModules(options);\n    }));\n    return (templateOrOverrides, overrides) => {\n        const defaults = {\n            hostProps: {},\n            detectChanges: true,\n            providers: []\n        };\n        const resolvedOverrides = typeof templateOrOverrides === 'object' ? templateOrOverrides : overrides;\n        const { detectChanges, hostProps, providers } = Object.assign(Object.assign({}, defaults), resolvedOverrides);\n        const template = typeof templateOrOverrides === 'string' ? templateOrOverrides : options.template;\n        if (providers && providers.length) {\n            providers.forEach((provider) => {\n                TestBed.overrideProvider(provider.provide, provider);\n            });\n        }\n        if (template) {\n            TestBed.overrideModule(BrowserDynamicTestingModule, {\n                set: {\n                    entryComponents: moduleMetadata.entryComponents\n                }\n            }).overrideComponent(options.host, {\n                set: { template }\n            });\n        }\n        const spectator = createSpectatorPipe(options, hostProps);\n        if (options.detectChanges && detectChanges) {\n            spectator.detectChanges();\n        }\n        return spectator;\n    };\n}\nfunction createSpectatorPipe(options, hostProps) {\n    const hostFixture = TestBed.createComponent(options.host);\n    const debugElement = hostFixture.debugElement;\n    const hostComponent = setProps(hostFixture.componentInstance, hostProps);\n    return new SpectatorPipe(hostComponent, hostFixture, hostFixture.debugElement, debugElement.nativeElement);\n}\n\nfunction MockComponentDeprecated(options) {\n    const metadata = {\n        selector: options.selector,\n        template: options.template || '',\n        inputs: options.inputs,\n        outputs: options.outputs || [],\n        exportAs: options.exportAs || ''\n    };\n    class Mock {\n    }\n    metadata.outputs.forEach(method => {\n        Mock.prototype[method] = new EventEmitter();\n    });\n    return Component(metadata)(Mock);\n}\nfunction MockDirectiveDeprecated(options) {\n    const metadata = {\n        selector: options.selector,\n        inputs: options.inputs,\n        outputs: options.outputs || [],\n        exportAs: options.exportAs || ''\n    };\n    class Mock {\n    }\n    metadata.outputs.forEach(method => {\n        Mock.prototype[method] = new EventEmitter();\n    });\n    return Directive(metadata)(Mock);\n}\n\n/// <reference path=\"./lib/matchers-types.ts\" />\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ActivatedRouteStub, DOMSelector, HostComponent, HostModule, HttpMethod, MockComponentDeprecated, MockDirectiveDeprecated, Spectator, SpectatorDirective, SpectatorHost, SpectatorHttp, SpectatorPipe, SpectatorRouting, SpectatorService, byAltText, byLabel, byPlaceholder, byRole, byTestId, byText, byTextContent, byTitle, byValue, createComponentFactory, createDirectiveFactory, createFakeEvent, createHostFactory, createHttpFactory, createKeyboardEvent, createMouseEvent, createPipeFactory, createRoutingFactory, createServiceFactory, createSpyObject, createTouchEvent, defineGlobalsInjections, dispatchEvent, dispatchFakeEvent, dispatchKeyboardEvent, dispatchMouseEvent, dispatchTouchEvent, doesServiceImplementsOnDestroy, initialSpectatorDirectiveModule, initialSpectatorModule, initialSpectatorPipeModule, initialSpectatorWithHostModule, installProtoMethods, isHTMLOptionElementArray, isNumber, isObject, isString, isType, mockProvider, toBeChecked, toBeDisabled, toBeEmpty, toBeFocused, toBeHidden, toBeMatchedBy, toBeSelected, toBeVisible, toContainProperty, toContainText, toContainValue, toExist, toHaveAttribute, toHaveClass, toHaveData, toHaveDescendant, toHaveDescendantWithText, toHaveExactText, toHaveId, toHaveLength, toHaveProperty, toHaveSelectedOptions, toHaveStyle, toHaveText, toHaveValue, typeInElement, ɵ0$1 as ɵ0, ɵ1, ɵ10, ɵ11, ɵ12, ɵ13, ɵ14, ɵ15, ɵ16, ɵ17, ɵ18, ɵ19, ɵ2, ɵ20, ɵ21, ɵ22, ɵ23, ɵ24, ɵ25, ɵ26, ɵ27, ɵ28, ɵ3, ɵ4, ɵ5, ɵ6, ɵ7, ɵ8, ɵ9, DomSpectator as ɵa, BaseSpectator as ɵb };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAEC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AACtL,SAASC,UAAU,EAAEC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AACzD,SAASC,OAAO,EAAEC,IAAI,EAAEC,KAAK,QAAQ,uBAAuB;AAC5D,SAASC,EAAE,QAAQ,2BAA2B;AAC9C,SAASC,OAAO,EAAEC,oBAAoB,QAAQ,sBAAsB;AACpE,SAASC,2BAA2B,QAAQ,2CAA2C;AACvF,OAAOC,CAAC,MAAM,QAAQ;AACtB,SAASC,oBAAoB,QAAQ,sCAAsC;AAC3E,SAASC,MAAM,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,UAAU,QAAQ,iBAAiB;AAC/G,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,uBAAuB,EAAEC,qBAAqB,QAAQ,8BAA8B;AAE7F,MAAMC,WAAW,CAAC;EACd;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA,MAAMC,OAAO,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK,IAAIL,WAAW,CAACM,EAAE,IAAIrB,OAAO,CAACsB,mBAAmB,CAACD,EAAE,EAAEF,OAAO,EAAEC,OAAO,CAAC,CAAC;AAC9G,MAAMG,aAAa,GAAGA,CAACJ,OAAO,EAAEC,OAAO,KAAK,IAAIL,WAAW,CAACM,EAAE,IAAIrB,OAAO,CAACwB,yBAAyB,CAACH,EAAE,EAAEF,OAAO,EAAEC,OAAO,CAAC,CAAC;AAC1H,MAAMK,MAAM,GAAGA,CAACN,OAAO,EAAEC,OAAO,KAAK,IAAIL,WAAW,CAACM,EAAE,IAAIrB,OAAO,CAAC0B,cAAc,CAACL,EAAE,EAAEF,OAAO,EAAEC,OAAO,CAAC,CAAC;AACxG,MAAMO,aAAa,GAAGA,CAACR,OAAO,EAAEC,OAAO,KAAK;EACxC,IAAIQ,kBAAkB;EACtB,MAAMC,UAAU,GAAG,CAACT,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACS,UAAU,KAAK5B,oBAAoB,CAACmB,OAAO,CAAC;EAC1H,MAAMU,cAAc,GAAIC,IAAI,IAAK;IAAE,IAAIC,EAAE;IAAE,OAAOH,UAAU,CAAC,CAACG,EAAE,GAAGD,IAAI,CAACE,WAAW,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC;EAAE,CAAC;EAC5H,IAAI,OAAOb,OAAO,KAAK,QAAQ,EAAE;IAC7BS,kBAAkB,GAAGA,CAACM,CAAC,EAAEH,IAAI,KAAK;MAC9B,IAAI,CAACX,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,KAAK,MAAM,KAAK,EAAE;QAC7E,OAAQL,cAAc,CAACC,IAAI,CAAC,CACvBK,WAAW,CAAC,CAAC,CACbC,OAAO,CAAClB,OAAO,CAACiB,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;MAC5C;MACA,OAAON,cAAc,CAACC,IAAI,CAAC,KAAKZ,OAAO;IAC3C,CAAC;EACL,CAAC,MACI,IAAIA,OAAO,YAAYmB,MAAM,EAAE;IAChCV,kBAAkB,GAAGA,CAACM,CAAC,EAAEH,IAAI,KAAKZ,OAAO,CAACoB,IAAI,CAACT,cAAc,CAACC,IAAI,CAAC,CAAC;EACxE,CAAC,MACI;IACDH,kBAAkB,GAAGA,CAACM,CAAC,EAAEH,IAAI,KAAKZ,OAAO,CAACW,cAAc,CAACC,IAAI,CAAC,EAAEA,IAAI,CAAC;EACzE;EACA,OAAO,IAAIhB,WAAW,CAACM,EAAE,IAAIrB,OAAO,CAAC0B,cAAc,CAACL,EAAE,EAAEO,kBAAkB,EAAER,OAAO,CAAC,CAAC;AACzF,CAAC;AACD,MAAMoB,SAAS,GAAGA,CAACrB,OAAO,EAAEC,OAAO,KAAK,IAAIL,WAAW,CAACM,EAAE,IAAIrB,OAAO,CAACyC,iBAAiB,CAACpB,EAAE,EAAEF,OAAO,EAAEC,OAAO,CAAC,CAAC;AAC9G,MAAMsB,OAAO,GAAGA,CAACvB,OAAO,EAAEC,OAAO,KAAK,IAAIL,WAAW,CAACM,EAAE,IAAIrB,OAAO,CAAC2C,eAAe,CAACtB,EAAE,EAAEF,OAAO,EAAEC,OAAO,CAAC,CAAC;AAC1G,MAAMwB,QAAQ,GAAGA,CAACzB,OAAO,EAAEC,OAAO,KAAK,IAAIL,WAAW,CAACM,EAAE,IAAIrB,OAAO,CAAC6C,gBAAgB,CAACxB,EAAE,EAAEF,OAAO,EAAEC,OAAO,CAAC,CAAC;AAC5G,MAAM0B,OAAO,GAAGA,CAAC3B,OAAO,EAAEC,OAAO,KAAK,IAAIL,WAAW,CAACM,EAAE,IAAIrB,OAAO,CAAC+C,sBAAsB,CAAC1B,EAAE,EAAEF,OAAO,EAAEC,OAAO,CAAC,CAAC;AACjH,MAAM4B,MAAM,GAAGA,CAAC7B,OAAO,EAAEC,OAAO,KAAK,IAAIL,WAAW,CAACM,EAAE,IAAIrB,OAAO,CAACiD,cAAc,CAAC5B,EAAE,EAAEF,OAAO,EAAEC,OAAO,CAAC,CAAC;AAExG,SAAS8B,8BAA8BA,CAACC,aAAa,EAAE;EACnD,OAAO,aAAa,IAAIA,aAAa,IAAI,OAAOA,aAAa,CAAC,aAAa,CAAC,KAAK,UAAU;AAC/F;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC;AACA,SAASC,QAAQA,CAACD,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC;AACA,SAASE,MAAMA,CAACC,CAAC,EAAE;EACf,OAAO,OAAOA,CAAC,KAAK,UAAU;AAClC;AACA,SAASC,wBAAwBA,CAACJ,KAAK,EAAE;EACrC,OAAOK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,IAAI,CAAC,CAACA,KAAK,CAACO,MAAM,IAAIP,KAAK,CAACQ,KAAK,CAACC,IAAI,IAAIA,IAAI,YAAYC,iBAAiB,CAAC;AAC3G;AACA,SAASC,QAAQA,CAACR,CAAC,EAAE;EACjB,OAAOA,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ;AACrC;AAEA,SAASS,WAAWA,CAACC,gBAAgB,EAAE;EACnC,OAAO,CAACC,mBAAmB,EAAE/C,OAAO,GAAG;IAAEgD,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAEC;EAAU,CAAC,KAAK;IACxE,IAAIH,mBAAmB,YAAYpD,WAAW,EAAE;MAC5C,OAAOoD,mBAAmB,CAAClD,OAAO,CAACiD,gBAAgB,CAACK,aAAa,CAAC;IACtE;IACA,MAAMC,aAAa,GAAGN,gBAAgB,CAACO,QAAQ,CAACrB,QAAQ,CAACe,mBAAmB,CAAC,GAAGpE,EAAE,CAAC2E,GAAG,CAACP,mBAAmB,CAAC,GAAGpE,EAAE,CAAC4E,SAAS,CAACR,mBAAmB,CAAC,CAAC;IAChJ,IAAI/C,OAAO,CAACiD,IAAI,EAAE;MACd,OAAOG,aAAa,CAAC9D,GAAG,CAACkE,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAACC,GAAG,CAAC1D,OAAO,CAACiD,IAAI,CAAC,CAAC;IACvE;IACA,IAAIjB,QAAQ,CAACe,mBAAmB,CAAC,EAAE;MAC/B,OAAOK,aAAa,CAAC9D,GAAG,CAACkE,KAAK,IAAIA,KAAK,CAACL,aAAa,CAAC;IAC1D;IACA,OAAOC,aAAa,CAAC9D,GAAG,CAACkE,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAACC,GAAG,CAACX,mBAAmB,CAAC,CAAC;EAC9E,CAAC;AACL;AACA,SAASY,QAAQA,CAACC,QAAQ,EAAEC,cAAc,EAAE5B,KAAK,EAAE6B,WAAW,GAAG,IAAI,EAAE;EACnE,IAAIlD,EAAE;EACN,MAAMmD,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMC,MAAM,GAAGA,CAACC,GAAG,EAAEC,QAAQ,KAAK;IAC9B,IAAIN,QAAQ,CAACK,GAAG,CAAC,KAAKC,QAAQ,EAAE;MAC5BH,OAAO,CAACE,GAAG,CAAC,GAAG,IAAIxG,YAAY,CAACmG,QAAQ,CAACK,GAAG,CAAC,EAAEC,QAAQ,EAAEJ,WAAW,CAAC;IACzE;IACAF,QAAQ,CAACK,GAAG,CAAC,GAAGC,QAAQ;EAC5B,CAAC;EACD,IAAIlC,QAAQ,CAAC6B,cAAc,CAAC,EAAE;IAC1BG,MAAM,CAACH,cAAc,EAAE5B,KAAK,CAAC;EACjC,CAAC,MACI;IACD;IACA,KAAK,MAAMkC,CAAC,IAAIN,cAAc,EAAE;MAC5BG,MAAM,CAACG,CAAC,EAAEN,cAAc,CAACM,CAAC,CAAC,CAAC;IAChC;EACJ;EACA,IAAIC,MAAM,CAACC,IAAI,CAACN,OAAO,CAAC,CAACvB,MAAM,EAAE;IAC7B;IACA,CAAC5B,EAAE,GAAGgD,QAAQ,CAACU,WAAW,MAAM,IAAI,IAAI1D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2D,IAAI,CAACX,QAAQ,EAAEG,OAAO,CAAC;EAC/F;EACA,OAAOH,QAAQ;AACnB;AAEA,MAAMY,eAAe,GAAIC,YAAY,IAAK;EACtC,IAAIvC,QAAQ,CAACuC,YAAY,CAAC,IAAIA,YAAY,EAAE;IACxC,OAAO;MAAER,GAAG,EAAE,KAAK;MAAES,OAAO,EAAED,YAAY;MAAEE,SAAS,EAAE,CAAC;IAAE,CAAC;EAC/D;EACA,IAAI3C,QAAQ,CAACyC,YAAY,CAAC,IAAIA,YAAY,EAAE;IACxC,OAAOG,QAAQ,CAACH,YAAY,CAAC;EACjC;EACA,IAAI7B,QAAQ,CAAC6B,YAAY,CAAC,EAAE;IACxB,MAAMI,SAAS,GAAGD,QAAQ,CAACH,YAAY,CAACR,GAAG,CAAC;IAC5C,OAAOG,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,EAAE;MAAEH,OAAO,EAAED,YAAY,CAACC;IAAQ,CAAC,CAAC;EACzF;EACA,MAAM,IAAIK,KAAK,CAAC,qDAAqD,CAAC;AAC1E,CAAC;AACD,MAAMH,QAAQ,GAAII,MAAM,IAAK;EACzB,IAAIA,MAAM,CAAC/D,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK+D,MAAM,EAAE;IAC3C,OAAO;MAAEf,GAAG,EAAEe,MAAM;MAAEN,OAAO,EAAE,KAAK;MAAEC,SAAS,EAAE,CAAC;IAAE,CAAC;EACzD;EACA,MAAMM,QAAQ,GAAGD,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;EAClC,MAAMjB,GAAG,GAAGgB,QAAQ,CAACE,GAAG,CAAC,CAAC;EAC1B,MAAMR,SAAS,GAAGM,QAAQ,CAACG,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAK;IAC9C,QAAQA,IAAI;MACR,KAAK,SAAS;MACd,KAAK,MAAM;QACPD,IAAI,CAACE,OAAO,GAAG,IAAI;QACnB,OAAOF,IAAI;MACf,KAAK,OAAO;QACRA,IAAI,CAACG,KAAK,GAAG,IAAI;QACjB,OAAOH,IAAI;MACf,KAAK,KAAK;QACNA,IAAI,CAACI,GAAG,GAAG,IAAI;QACf,OAAOJ,IAAI;MACf,KAAK,MAAM;MACX,KAAK,KAAK;MACV,KAAK,KAAK;QACNA,IAAI,CAACK,IAAI,GAAG,IAAI;QAChB,OAAOL,IAAI;MACf;QACI,MAAM,IAAIN,KAAK,CAAC,yBAAyBO,IAAI,GAAGA,IAAI,GAAG,WAAW,aAAaN,MAAM,EAAE,CAAC;IAChG;EACJ,CAAC,EAAE;IAAES,GAAG,EAAE,KAAK;IAAEF,OAAO,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEE,IAAI,EAAE;EAAM,CAAC,CAAC;EAC7D,OAAO;IAAEzB,GAAG;IAAES,OAAO,EAAE,KAAK;IAAEC;EAAU,CAAC;AAC7C,CAAC;AACD,MAAMgB,EAAE,GAAGf,QAAQ;;AAEnB;AACA;AACA;AACA;AACA,SAASgB,gBAAgBA,CAACC,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAE;EACtD,MAAMC,KAAK,GAAGC,QAAQ,CAACC,WAAW,CAAC,YAAY,CAAC;EAChDF,KAAK,CAACG,cAAc,CAACP,IAAI,EAAE,IAAI,EAAE,KAAK,EAAEQ,MAAM,EAAE,CAAC,EAAEP,CAAC,EAAEC,CAAC,EAAED,CAAC,EAAEC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAEC,MAAM,EAAE,IAAI,CAAC;EACxG;EACA;EACA5B,MAAM,CAACkC,cAAc,CAACL,KAAK,EAAE,SAAS,EAAE;IAAEvC,GAAG,EAAEA,CAAA,KAAM;EAAE,CAAC,CAAC;EACzD,OAAOuC,KAAK;AAChB;AACA;AACA;AACA;AACA,SAASM,gBAAgBA,CAACV,IAAI,EAAEW,KAAK,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAE;EAClD;EACA;EACA,MAAMR,KAAK,GAAG,IAAIS,OAAO,CAACb,IAAI,EAAE;IAC5Bc,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBC,IAAI,EAAER,MAAM;IACZS,MAAM,EAAE;EACZ,CAAC,CAAC;EACF;EACA;EACA1C,MAAM,CAAC2C,gBAAgB,CAACd,KAAK,EAAE;IAC3Be,OAAO,EAAE;MAAE/E,KAAK,EAAE,CAAC;QAAEuE,KAAK;QAAEC;MAAM,CAAC;IAAE;EACzC,CAAC,CAAC;EACF,OAAOR,KAAK;AAChB;AACA;AACA,SAASgB,mBAAmBA,CAACpB,IAAI,EAAEpB,YAAY,EAAEyC,MAAM,EAAE;EACrD,MAAM;IAAEjD,GAAG;IAAES,OAAO;IAAEC;EAAU,CAAC,GAAGH,eAAe,CAACC,YAAY,CAAC;EACjE,MAAMwB,KAAK,GAAGC,QAAQ,CAACC,WAAW,CAAC,eAAe,CAAC;EACnD,MAAMgB,sBAAsB,GAAGlB,KAAK,CAACmB,cAAc;EACnD;EACA,IAAInB,KAAK,CAACoB,YAAY,EAAE;IACpBpB,KAAK,CAACoB,YAAY,CAACxB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAEQ,MAAM,EAAE1B,SAAS,CAACY,OAAO,EAAEZ,SAAS,CAACc,GAAG,EAAEd,SAAS,CAACa,KAAK,EAAEb,SAAS,CAACe,IAAI,EAAEhB,OAAO,CAAC;EAC5H,CAAC,MACI;IACD;IACA;IACA,MAAM4C,YAAY,GAAG,CAAC3C,SAAS,CAACY,OAAO,GACjC,UAAU,GACV,EAAE,GAAGZ,SAAS,CAACc,GAAG,GACd,MAAM,GACN,EAAE,GAAGd,SAAS,CAACa,KAAK,GAChB,QAAQ,GACR,EAAE,GAAGb,SAAS,CAACe,IAAI,GACf,MAAM,GACN,EAAE,EAAE6B,IAAI,CAAC,CAAC;IAC5BtB,KAAK,CAACuB,iBAAiB,CAAC3B,IAAI,EAAE,IAAI,CAAC,iBAAiB,IAAI,CAAC,kBAAkBQ,MAAM,CAAC,YAAY,CAAC,CAAC,YAAYpC,GAAG,CAAC,WAAW,CAAC,CAAC,gBAAgBqD,YAAY,CAAC,qBAAqB,KAAK,CAAC,YAAY,CAAC;EACtM;EACA;EACA;EACAlD,MAAM,CAAC2C,gBAAgB,CAACd,KAAK,EAAE;IAC3BvB,OAAO,EAAE;MAAEhB,GAAG,EAAEA,CAAA,KAAMgB;IAAQ,CAAC;IAC/BT,GAAG,EAAE;MAAEP,GAAG,EAAEA,CAAA,KAAMO;IAAI,CAAC;IACvBiD,MAAM,EAAE;MAAExD,GAAG,EAAEA,CAAA,KAAMwD;IAAO,CAAC;IAC7BO,MAAM,EAAE;MAAE/D,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACiB,SAAS,CAACc;IAAI,CAAC;IACtCiC,OAAO,EAAE;MAAEhE,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACiB,SAAS,CAACY;IAAQ,CAAC;IAC3CoC,QAAQ,EAAE;MAAEjE,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACiB,SAAS,CAACa;IAAM,CAAC;IAC1CoC,OAAO,EAAE;MAAElE,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACiB,SAAS,CAACe;IAAK;EAC3C,CAAC,CAAC;EACF;EACA;EACAO,KAAK,CAACmB,cAAc,GAAG,YAAY;IAC/BhD,MAAM,CAACkC,cAAc,CAACL,KAAK,EAAE,kBAAkB,EAAE;MAAE4B,YAAY,EAAE,IAAI;MAAEnE,GAAG,EAAEA,CAAA,KAAM;IAAK,CAAC,CAAC;IACzF,OAAOyD,sBAAsB,CAACW,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACxD,CAAC;EACD,OAAO9B,KAAK;AAChB;AACA;AACA,SAAS+B,eAAeA,CAACnC,IAAI,EAAEoC,SAAS,GAAG,KAAK,EAAErB,UAAU,GAAG,IAAI,EAAE;EACjE,MAAMX,KAAK,GAAGC,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC;EAC3CF,KAAK,CAACiC,SAAS,CAACrC,IAAI,EAAEoC,SAAS,EAAErB,UAAU,CAAC;EAC5C,OAAOX,KAAK;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkC,aAAaA,CAACC,IAAI,EAAEnC,KAAK,EAAE;EAChCmC,IAAI,CAACD,aAAa,CAAClC,KAAK,CAAC;EACzB,OAAOA,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoC,iBAAiBA,CAACD,IAAI,EAAEvC,IAAI,EAAEoC,SAAS,EAAE;EAC9C,OAAOE,aAAa,CAACC,IAAI,EAAEJ,eAAe,CAACnC,IAAI,EAAEoC,SAAS,CAAC,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,qBAAqBA,CAACF,IAAI,EAAEvC,IAAI,EAAEpB,YAAY,EAAEyC,MAAM,EAAE;EAC7D,OAAOiB,aAAa,CAACC,IAAI,EAAEnB,mBAAmB,CAACpB,IAAI,EAAEpB,YAAY,EAAEyC,MAAM,CAAC,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,kBAAkBA,CAACH,IAAI,EAAEvC,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEE,KAAK,GAAGL,gBAAgB,CAACC,IAAI,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;EACxF,OAAOoC,aAAa,CAACC,IAAI,EAAEnC,KAAK,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuC,kBAAkBA,CAACJ,IAAI,EAAEvC,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAE;EAClD,OAAOoC,aAAa,CAACC,IAAI,EAAE7B,gBAAgB,CAACV,IAAI,EAAEC,CAAC,EAAEC,CAAC,CAAC,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0C,iBAAiBA,CAACC,OAAO,EAAE;EAChCA,OAAO,CAACC,KAAK,GAAG,MAAMN,iBAAiB,CAACK,OAAO,EAAE,OAAO,CAAC;EACzDA,OAAO,CAACE,IAAI,GAAG,MAAMP,iBAAiB,CAACK,OAAO,EAAE,MAAM,CAAC;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,aAAaA,CAAC5G,KAAK,EAAEyG,OAAO,EAAE;EACnC,IAAI,EAAEA,OAAO,YAAYI,gBAAgB,CAAC,IAAI,EAAEJ,OAAO,YAAYK,mBAAmB,CAAC,EAAE;IACrF;EACJ;EACAL,OAAO,CAACC,KAAK,CAAC,CAAC;EACfD,OAAO,CAACzG,KAAK,GAAGA,KAAK;EACrBoG,iBAAiB,CAACK,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,YAAYA,CAAChJ,OAAO,EAAE0I,OAAO,EAAEO,MAAM,EAAE;EAC5C,IAAI,EAAEP,OAAO,YAAYQ,iBAAiB,CAAC,EAAE;IACzC;EACJ;EACAR,OAAO,CAACC,KAAK,CAAC,CAAC;EACf,IAAI3G,QAAQ,CAAChC,OAAO,CAAC,EAAE;IACnB,MAAMmJ,MAAM,GAAGT,OAAO,CAACU,aAAa,CAAC,iBAAiBpJ,OAAO,IAAI,CAAC;IAClE,IAAI,CAACmJ,MAAM,EAAE;MACT;IACJ;IACAE,iBAAiB,CAACF,MAAM,EAAET,OAAO,EAAEO,MAAM,CAAC;EAC9C,CAAC,MACI,IAAIjJ,OAAO,YAAY2C,iBAAiB,EAAE;IAC3C0G,iBAAiB,CAACrJ,OAAO,EAAE0I,OAAO,EAAEO,MAAM,CAAC;EAC/C,CAAC,MACI;IACD,IAAI,CAACP,OAAO,CAACY,QAAQ,EAAE;MACnB;IACJ;IACA,IAAIjH,wBAAwB,CAACrC,OAAO,CAAC,EAAE;MACnCA,OAAO,CAACuJ,OAAO,CAACJ,MAAM,IAAIE,iBAAiB,CAACF,MAAM,EAAET,OAAO,EAAEO,MAAM,CAAC,CAAC;IACzE,CAAC,MACI;MACDP,OAAO,CAACc,gBAAgB,CAAC,QAAQ,CAAC,CAACD,OAAO,CAACE,GAAG,IAAI;QAC9C,IAAIzJ,OAAO,CAAC0J,QAAQ,CAACD,GAAG,CAACxH,KAAK,CAAC,EAAE;UAC7BoH,iBAAiB,CAACI,GAAG,EAAEf,OAAO,EAAEO,MAAM,CAAC;QAC3C;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,iBAAiBA,CAACF,MAAM,EAAEQ,MAAM,EAAEV,MAAM,EAAE;EAC/CE,MAAM,CAACS,QAAQ,GAAG,IAAI;EACtB,IAAIX,MAAM,CAACY,UAAU,EAAE;IACnBxB,iBAAiB,CAACsB,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC7C;AACJ;;AAEA;AACA;AACA;AACA,MAAMG,aAAa,CAAC;EAChBC,MAAMA,CAACC,KAAK,EAAE;IACV,OAAOxL,OAAO,CAACuL,MAAM,GAAGvL,OAAO,CAACuL,MAAM,CAACC,KAAK,CAAC,GAAGxL,OAAO,CAACkF,GAAG,CAACsG,KAAK,CAAC;EACtE;AACJ;AAEA,MAAMC,MAAM,GAAG,OAAO;AACtB;AACA;AACA;AACA,MAAMC,YAAY,SAASJ,aAAa,CAAC;EACrClK,WAAWA,CAACuK,OAAO,EAAEC,YAAY,EAAExG,QAAQ,EAAE8E,OAAO,EAAE;IAClD,KAAK,CAAC,CAAC;IACP,IAAI,CAACyB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACxG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC8E,OAAO,GAAGA,OAAO;EAC1B;EACAqB,MAAMA,CAACC,KAAK,EAAE;IACV,OAAO,KAAK,CAACD,MAAM,CAACC,KAAK,CAAC;EAC9B;EACAK,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACF,OAAO,CAACE,aAAa,CAAC,CAAC;EAChC;EACAC,KAAKA,CAACvH,mBAAmB,EAAE/C,OAAO,EAAE;IAChC,IAAI,CAACA,OAAO,IAAI,CAAC,CAAC,EAAEgD,IAAI,EAAE;MACtB,IAAIhB,QAAQ,CAACe,mBAAmB,CAAC,EAAE;QAC/B,OAAOmD,QAAQ,CAACkD,aAAa,CAACrG,mBAAmB,CAAC;MACtD;MACA,IAAIA,mBAAmB,YAAYpD,WAAW,EAAE;QAC5C,OAAOoD,mBAAmB,CAAClD,OAAO,CAACqG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;MAC3D;IACJ;IACA,OAAOrD,WAAW,CAAC,IAAI,CAACuH,YAAY,CAAC,CAACrH,mBAAmB,EAAE/C,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;EAClF;EACAqD,QAAQA,CAACN,mBAAmB,EAAE/C,OAAO,EAAE;IACnC,IAAI,CAACA,OAAO,IAAI,CAAC,CAAC,EAAEgD,IAAI,EAAE;MACtB,IAAIhB,QAAQ,CAACe,mBAAmB,CAAC,EAAE;QAC/B,OAAOT,KAAK,CAACiI,IAAI,CAACrE,QAAQ,CAACsD,gBAAgB,CAACzG,mBAAmB,CAAC,CAAC;MACrE;MACA,IAAIA,mBAAmB,YAAYpD,WAAW,EAAE;QAC5C,OAAOoD,mBAAmB,CAAClD,OAAO,CAACqG,QAAQ,CAAC;MAChD;IACJ;IACA,OAAOrD,WAAW,CAAC,IAAI,CAACuH,YAAY,CAAC,CAACrH,mBAAmB,EAAE/C,OAAO,CAAC;EACvE;EACAwK,SAASA,CAACzH,mBAAmB,EAAE/C,OAAO,EAAE;IACpC,IAAIyK,MAAM,GAAG,EAAE;IACf,IAAI,CAACzK,OAAO,IAAI,CAAC,CAAC,EAAEgD,IAAI,EAAE;MACtB,IAAIhB,QAAQ,CAACe,mBAAmB,CAAC,EAAE;QAC/B0H,MAAM,GAAGnI,KAAK,CAACiI,IAAI,CAACrE,QAAQ,CAACsD,gBAAgB,CAACzG,mBAAmB,CAAC,CAAC;MACvE;MACA,IAAIA,mBAAmB,YAAYpD,WAAW,EAAE;QAC5C8K,MAAM,GAAG1H,mBAAmB,CAAClD,OAAO,CAACqG,QAAQ,CAAC;MAClD;IACJ,CAAC,MACI;MACDuE,MAAM,GAAG5H,WAAW,CAAC,IAAI,CAACuH,YAAY,CAAC,CAACrH,mBAAmB,EAAE/C,OAAO,CAAC;IACzE;IACA,IAAIyK,MAAM,IAAIA,MAAM,CAACjI,MAAM,EAAE;MACzB,OAAOiI,MAAM,CAACA,MAAM,CAACjI,MAAM,GAAG,CAAC,CAAC;IACpC;IACA,OAAO,IAAI;EACf;EACAkI,QAAQA,CAACC,KAAK,EAAE1I,KAAK,EAAE;IACnB0B,QAAQ,CAAC,IAAI,CAACC,QAAQ,EAAE+G,KAAK,EAAE1I,KAAK,EAAE,KAAK,CAAC;IAC5C,IAAI,CAACmI,YAAY,CAAC3G,QAAQ,CAACC,GAAG,CAAChG,iBAAiB,CAAC,CAAC2M,aAAa,CAAC,CAAC;EACrE;EACAO,MAAMA,CAACA,MAAM,EAAE;IACX,MAAMC,UAAU,GAAG,IAAI,CAACjH,QAAQ,CAACgH,MAAM,CAAC;IACxC,IAAI,EAAEC,UAAU,YAAYxM,UAAU,CAAC,EAAE;MACrC,MAAM,IAAI0G,KAAK,CAAC,GAAG6F,MAAM,oBAAoB,CAAC;IAClD;IACA,OAAOC,UAAU;EACrB;EACApM,IAAIA,CAACqM,MAAM,EAAE;IACTrM,IAAI,CAACqM,MAAM,CAAC;IACZ,IAAI,CAACT,aAAa,CAAC,CAAC;EACxB;EACAU,KAAKA,CAACC,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAE;IAC3B,MAAMA,OAAO,GAAG,IAAI,CAACuC,gBAAgB,CAACD,QAAQ,CAAC;IAC/C,IAAI,EAAEtC,OAAO,YAAYwC,WAAW,CAAC,EAAE;MACnC,MAAM,IAAInG,KAAK,CAAC,iBAAiBiG,QAAQ,uBAAuB,CAAC;IACrE;IACAtC,OAAO,CAACqC,KAAK,CAAC,CAAC;IACf,IAAI,CAACV,aAAa,CAAC,CAAC;EACxB;EACAzB,IAAIA,CAACoC,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAE;IAC1B,MAAMA,OAAO,GAAG,IAAI,CAACuC,gBAAgB,CAACD,QAAQ,CAAC;IAC/C,IAAI,EAAEtC,OAAO,YAAYwC,WAAW,CAAC,EAAE;MACnC,MAAM,IAAInG,KAAK,CAAC,gBAAgBiG,QAAQ,uBAAuB,CAAC;IACpE;IACAvC,iBAAiB,CAACC,OAAO,CAAC;IAC1BA,OAAO,CAACE,IAAI,CAAC,CAAC;IACd,IAAI,CAACyB,aAAa,CAAC,CAAC;EACxB;EACA1B,KAAKA,CAACqC,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAE;IAC3B,MAAMA,OAAO,GAAG,IAAI,CAACuC,gBAAgB,CAACD,QAAQ,CAAC;IAC/C,IAAI,EAAEtC,OAAO,YAAYwC,WAAW,CAAC,EAAE;MACnC,MAAM,IAAInG,KAAK,CAAC,iBAAiBiG,QAAQ,uBAAuB,CAAC;IACrE;IACAvC,iBAAiB,CAACC,OAAO,CAAC;IAC1BA,OAAO,CAACC,KAAK,CAAC,CAAC;IACf,IAAI,CAAC0B,aAAa,CAAC,CAAC;EACxB;EACA9B,kBAAkBA,CAACyC,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAE7C,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEE,KAAK,GAAGL,gBAAgB,CAACC,IAAI,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE;IAClG,MAAM2C,OAAO,GAAG,IAAI,CAACuC,gBAAgB,CAACD,QAAQ,CAAC;IAC/C,IAAI,EAAEtC,OAAO,YAAYyC,IAAI,CAAC,EAAE;MAC5B,MAAM,IAAIpG,KAAK,CAAC,gCAAgCiG,QAAQ,gBAAgB,CAAC;IAC7E;IACA,MAAMI,eAAe,GAAG7C,kBAAkB,CAACG,OAAO,EAAE7C,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEE,KAAK,CAAC;IACtE,IAAI,CAACoE,aAAa,CAAC,CAAC;IACpB,OAAOe,eAAe;EAC1B;EACA9C,qBAAqBA,CAAC0C,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAE7C,IAAI,EAAEpB,YAAY,EAAEyC,MAAM,EAAE;IACvE,MAAMwB,OAAO,GAAG,IAAI,CAACuC,gBAAgB,CAACD,QAAQ,CAAC;IAC/C,IAAI,EAAEtC,OAAO,YAAYyC,IAAI,CAAC,EAAE;MAC5B,MAAM,IAAIpG,KAAK,CAAC,mCAAmCiG,QAAQ,gBAAgB,CAAC;IAChF;IACA,MAAM/E,KAAK,GAAGqC,qBAAqB,CAACI,OAAO,EAAE7C,IAAI,EAAEpB,YAAY,EAAEyC,MAAM,CAAC;IACxE,IAAI,CAACmD,aAAa,CAAC,CAAC;IACpB,OAAOpE,KAAK;EAChB;EACAoC,iBAAiBA,CAAC2C,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAE7C,IAAI,EAAEoC,SAAS,EAAE;IACxD,MAAMhC,KAAK,GAAGoC,iBAAiB,CAAC,IAAI,CAAC4C,gBAAgB,CAACD,QAAQ,CAAC,EAAEnF,IAAI,EAAEoC,SAAS,CAAC;IACjF,IAAI,CAACoC,aAAa,CAAC,CAAC;IACpB,OAAOpE,KAAK;EAChB;EACAoF,mBAAmBA,CAACtI,mBAAmB,EAAEuI,SAAS,EAAEC,QAAQ,EAAE;IAC1D,MAAMnB,YAAY,GAAG,IAAI,CAACoB,eAAe,CAACzI,mBAAmB,CAAC;IAC9D,IAAI,CAACqH,YAAY,EAAE;MACf;MACAqB,OAAO,CAACC,KAAK,CAAC,GAAG3I,mBAAmB,kBAAkB,CAAC;MACvD;IACJ;IACAqH,YAAY,CAACiB,mBAAmB,CAACC,SAAS,EAAEC,QAAQ,CAAC;IACrD,IAAI,CAAClB,aAAa,CAAC,CAAC;EACxB;EACA,IAAIsB,QAAQA,CAAA,EAAG;IACX,OAAO;MACHC,QAAQ,EAAEA,CAAC3H,GAAG,EAAE+G,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAEzC,KAAK,GAAGgE,MAAM,KAAK;QACxD,IAAI,CAAC3B,qBAAqB,CAAC0C,QAAQ,EAAE/E,KAAK,EAAEhC,GAAG,CAAC;MACpD,CAAC;MACD4H,WAAW,EAAEA,CAACb,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAEzC,KAAK,GAAGgE,MAAM,KAAK;QACtD,IAAI,CAAC3B,qBAAqB,CAAC0C,QAAQ,EAAE/E,KAAK,EAAE;UAAEhC,GAAG,EAAE,QAAQ;UAAES,OAAO,EAAE;QAAG,CAAC,CAAC;MAC/E,CAAC;MACDoH,UAAU,EAAEA,CAACd,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAEzC,KAAK,GAAGgE,MAAM,KAAK;QACrD,IAAI,CAAC3B,qBAAqB,CAAC0C,QAAQ,EAAE/E,KAAK,EAAE;UAAEhC,GAAG,EAAE,OAAO;UAAES,OAAO,EAAE;QAAG,CAAC,CAAC;MAC9E,CAAC;MACDqH,QAAQ,EAAEA,CAACf,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAEzC,KAAK,GAAGgE,MAAM,KAAK;QACnD,IAAI,CAAC3B,qBAAqB,CAAC0C,QAAQ,EAAE/E,KAAK,EAAE;UAAEhC,GAAG,EAAE,KAAK;UAAES,OAAO,EAAE;QAAE,CAAC,CAAC;MAC3E,CAAC;MACDsH,cAAc,EAAEA,CAAChB,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAEzC,KAAK,GAAGgE,MAAM,KAAK;QACzD,IAAI,CAAC3B,qBAAqB,CAAC0C,QAAQ,EAAE/E,KAAK,EAAE;UAAEhC,GAAG,EAAE,WAAW;UAAES,OAAO,EAAE;QAAE,CAAC,CAAC;MACjF;IACJ,CAAC;EACL;EACA,IAAIuH,KAAKA,CAAA,EAAG;IACR,OAAO;MACHC,WAAW,EAAEA,CAAClB,QAAQ,GAAG,IAAI,CAACtC,OAAO,KAAK;QACtC,IAAI,CAACH,kBAAkB,CAACyC,QAAQ,EAAE,aAAa,CAAC;MACpD,CAAC;MACDmB,QAAQ,EAAEA,CAACnB,QAAQ,GAAG,IAAI,CAACtC,OAAO,KAAK;QACnC,IAAI,CAACH,kBAAkB,CAACyC,QAAQ,EAAE,UAAU,CAAC;MACjD;IACJ,CAAC;EACL;EACAxC,kBAAkBA,CAACwC,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAE7C,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAE;IAC5DyC,kBAAkB,CAAC,IAAI,CAACyC,gBAAgB,CAACD,QAAQ,CAAC,EAAEnF,IAAI,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAC/D,IAAI,CAACsE,aAAa,CAAC,CAAC;EACxB;EACAxB,aAAaA,CAAC5G,KAAK,EAAE+I,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAE;IAC1CG,aAAa,CAAC5G,KAAK,EAAE,IAAI,CAACgJ,gBAAgB,CAACD,QAAQ,CAAC,CAAC;IACrD,IAAI,CAACX,aAAa,CAAC,CAAC;EACxB;EACArB,YAAYA,CAACgC,QAAQ,GAAG,IAAI,CAACtC,OAAO,EAAE1I,OAAO,EAAEiJ,MAAM,GAAG;IAAEY,UAAU,EAAE;EAAK,CAAC,EAAE;IAC1E,IAAI,CAACmB,QAAQ,EAAE;MACX,MAAM,IAAIjG,KAAK,CAAC,uBAAuBiG,QAAQ,EAAE,CAAC;IACtD;IACAhC,YAAY,CAAChJ,OAAO,EAAE,IAAI,CAACiL,gBAAgB,CAACD,QAAQ,CAAC,EAAE/B,MAAM,CAAC;IAC9D,IAAI,CAACoB,aAAa,CAAC,CAAC;EACxB;EACAY,gBAAgBA,CAACD,QAAQ,EAAE;IACvB,IAAItC,OAAO;IACX;IACA,IAAIsC,QAAQ,KAAK3E,MAAM,IAAI2E,QAAQ,KAAK9E,QAAQ,EAAE;MAC9C,OAAO8E,QAAQ;IACnB;IACA,IAAIhJ,QAAQ,CAACgJ,QAAQ,CAAC,EAAE;MACpB,MAAMoB,MAAM,GAAG,IAAI,CAAChC,YAAY,CAACE,KAAK,CAAC3L,EAAE,CAAC2E,GAAG,CAAC0H,QAAQ,CAAC,CAAC;MACxD,IAAIoB,MAAM,EAAE;QACR1D,OAAO,GAAG0D,MAAM,CAACjJ,aAAa;MAClC,CAAC,MACI;QACD;QACAsI,OAAO,CAACC,KAAK,CAAC,GAAGV,QAAQ,kBAAkB,CAAC;MAChD;IACJ,CAAC,MACI,IAAIA,QAAQ,YAAYrL,WAAW,EAAE;MACtC+I,OAAO,GAAGsC,QAAQ,CAACnL,OAAO,CAACqG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;IACnD,CAAC,MACI;MACD,IAAI8E,QAAQ,YAAYrN,YAAY,IAAIqN,QAAQ,YAAYpN,UAAU,EAAE;QACpE8K,OAAO,GAAGsC,QAAQ,CAAC7H,aAAa;MACpC,CAAC,MACI;QACDuF,OAAO,GAAGsC,QAAQ;MACtB;IACJ;IACA,OAAOtC,OAAO;EAClB;EACA8C,eAAeA,CAACzI,mBAAmB,EAAE;IACjC,IAAIqH,YAAY;IAChB,IAAIpI,QAAQ,CAACe,mBAAmB,CAAC,EAAE;MAC/BqH,YAAY,GAAG,IAAI,CAACA,YAAY,CAACE,KAAK,CAAC3L,EAAE,CAAC2E,GAAG,CAACP,mBAAmB,CAAC,CAAC;IACvE,CAAC,MACI,IAAIA,mBAAmB,YAAYpF,YAAY,EAAE;MAClDyM,YAAY,GAAGrH,mBAAmB;IACtC,CAAC,MACI;MACDqH,YAAY,GAAG,IAAI,CAACA,YAAY,CAACE,KAAK,CAAC3L,EAAE,CAAC4E,SAAS,CAACR,mBAAmB,CAAC,CAAC;IAC7E;IACA,OAAOqH,YAAY;EACvB;AACJ;;AAEA;AACA;AACA;AACA,MAAMiC,SAAS,SAASnC,YAAY,CAAC;EACjCtK,WAAWA,CAACuK,OAAO,EAAEC,YAAY,EAAExG,QAAQ,EAAE8E,OAAO,EAAE;IAClD,KAAK,CAACyB,OAAO,EAAEC,YAAY,EAAExG,QAAQ,EAAE8E,OAAO,CAAC;IAC/C,IAAI,CAACyB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACxG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC8E,OAAO,GAAGA,OAAO;EAC1B;EACA,IAAI4D,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC1I,QAAQ;EACxB;EACAmG,MAAMA,CAACC,KAAK,EAAEuC,qBAAqB,GAAG,KAAK,EAAE;IACzC,IAAIA,qBAAqB,EAAE;MACvB,OAAO,IAAI,CAACnC,YAAY,CAAC3G,QAAQ,CAACC,GAAG,CAACsG,KAAK,CAAC;IAChD;IACA,OAAO,KAAK,CAACD,MAAM,CAACC,KAAK,CAAC;EAC9B;EACAwC,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACpC,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC3G,QAAQ,CAACC,GAAG,CAAChG,iBAAiB,CAAC,CAAC2M,aAAa,CAAC,CAAC;IACrE,CAAC,MACI;MACD,IAAI,CAACA,aAAa,CAAC,CAAC;IACxB;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoC,OAAOA,CAACC,GAAG,EAAE;EAClB,MAAMC,CAAC,GAAGD,GAAG,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EAC9B,MAAMC,OAAO,GAAGF,CAAC,CAACG,KAAK,CAAC,IAAI5L,MAAM,CAAC,KAAK,GAAGyL,CAAC,CAACnK,MAAM,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;EACrE,MAAM,CAACuK,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGJ,OAAO,CAACvN,GAAG,CAACwN,KAAK,IAAII,QAAQ,CAACJ,KAAK,CAACtK,MAAM,KAAK,CAAC,GAAGsK,KAAK,GAAGA,KAAK,GAAGA,KAAK,EAAE,EAAE,CAAC,CAAC;EAChG,OAAO,OAAOC,CAAC,IAAIC,CAAC,IAAIC,CAAC,GAAG;AAChC;AACA,SAASE,KAAKA,CAAClL,KAAK,EAAE;EAClB,OAAO,oCAAoC,CAACd,IAAI,CAACc,KAAK,CAAC;AAC3D;AACA,SAASsF,IAAIA,CAACtF,KAAK,EAAE;EACjB,OAAO,CAACA,KAAK,IAAI,EAAE,EAAE2K,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAC3C;AAEA,SAASQ,gBAAgBA,CAAA,EAAG;EACxB,OAAOC,SAAS,CAACC,SAAS,CAAC5D,QAAQ,CAAC,SAAS,CAAC,IAAI2D,SAAS,CAACC,SAAS,CAAC5D,QAAQ,CAAC,OAAO,CAAC;AAC3F;;AAEA;AACA,MAAM6D,WAAW,GAAGA,CAACC,MAAM,EAAEC,QAAQ,KAAK;EACtC,OAAOA,QAAQ,KAAKvK,SAAS,GAAGsK,MAAM,KAAKtK,SAAS,GAAGsK,MAAM,KAAKC,QAAQ;AAC9E,CAAC;AACD,MAAMC,IAAI,GAAGH,WAAW;AACxB,MAAMI,gBAAgB,GAAGA,CAACH,MAAM,EAAEC,QAAQ,KAAK;EAC3C,OAAOA,QAAQ,KAAKvK,SAAS,GAAG,IAAI,GAAGsK,MAAM,CAAC9D,QAAQ,CAAC+D,QAAQ,CAAC;AACpE,CAAC;AACD,MAAMG,EAAE,GAAGD,gBAAgB;AAC3B,MAAME,aAAa,GAAGA,CAAC5N,EAAE,EAAE6N,IAAI,EAAEC,SAAS,KAAK;EAC3C,IAAIC,IAAI,GAAG,KAAK;EAChB,IAAIC,OAAO,GAAG,EAAE;EAChB,KAAK,MAAMhK,GAAG,IAAIG,MAAM,CAACC,IAAI,CAACyJ,IAAI,CAAC,EAAE;IACjC,MAAMN,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAAC6N,IAAI,CAAC7J,GAAG,CAAC;IAC9B,MAAMiK,QAAQ,GAAGJ,IAAI,CAAC7J,GAAG,CAAC,KAAKf,SAAS,GAAG,gBAAgB4K,IAAI,CAAC7J,GAAG,CAAC,GAAG,GAAG,EAAE;IAC5E+J,IAAI,GAAGD,SAAS,CAACP,MAAM,EAAEM,IAAI,CAAC7J,GAAG,CAAC,CAAC;IACnCgK,OAAO,GAAG,CAACD,IAAI,GAAG,IAAIF,IAAI,IAAII,QAAQ,cAAcV,MAAM,GAAG,GAAG,EAAE;EACtE;EACA,MAAMW,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,qBAAqBC,OAAO,EAAE;EACzF,OAAO;IAAED,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD,MAAMC,EAAE,GAAGP,aAAa;AACxB,MAAMQ,MAAM,GAAGA,CAACpO,EAAE,EAAEqD,GAAG,KAAK;EACxB,IAAIwK,IAAI;EACR,IAAI7L,KAAK;EACT,MAAMqM,GAAG,GAAGvP,CAAC,CAACkB,EAAE,CAAC;EACjB,KAAK6N,IAAI,IAAIxK,GAAG,EAAE;IACd,IAAIA,GAAG,CAACiL,cAAc,CAACT,IAAI,CAAC,EAAE;MAC1B7L,KAAK,GAAGqB,GAAG,CAACwK,IAAI,CAAC;MACjB,IAAIX,KAAK,CAAClL,KAAK,CAAC,EAAE;QACdA,KAAK,GAAGwK,OAAO,CAACnJ,GAAG,CAACwK,IAAI,CAAC,CAAC;MAC9B;MACA,IAAI7L,KAAK,KAAK,MAAM,IAAIqM,GAAG,CAAC5K,GAAG,CAAC,CAAC,CAAC,CAAC8K,KAAK,CAACV,IAAI,CAAC,KAAK,MAAM,EAAE;QACvD;MACJ;MACA,IAAIvG,IAAI,CAAC+G,GAAG,CAAChL,GAAG,CAACwK,IAAI,CAAC,CAAC,KAAKvG,IAAI,CAACtF,KAAK,CAAC,IAAIsF,IAAI,CAACtH,EAAE,CAACuO,KAAK,CAACV,IAAI,CAAC,CAAC,KAAKvG,IAAI,CAACtF,KAAK,CAAC,EAAE;QAC7E,OAAO,KAAK;MAChB;IACJ;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAMwM,EAAE,GAAGJ,MAAM;AACjB,MAAMK,WAAW,GAAGA,CAACzO,EAAE,EAAEwN,QAAQ,EAAE1M,KAAK,GAAG,KAAK,KAAK;EACjD,IAAI0M,QAAQ,IAAInL,KAAK,CAACC,OAAO,CAACkL,QAAQ,CAAC,EAAE;IACrC,IAAID,MAAM;IACV,IAAIQ,IAAI,GAAG,KAAK;IAChB,IAAIC,OAAO;IACXlP,CAAC,CAACkB,EAAE,CAAC,CAAC0O,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACjBrB,MAAM,GAAGzM,KAAK,GAAGhC,CAAC,CAAC8P,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,GAAG/P,CAAC,CAACwI,IAAI,CAACxI,CAAC,CAAC8P,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;MAClDd,IAAI,GAAGjN,KAAK,GAAGyM,MAAM,KAAKC,QAAQ,CAACmB,CAAC,CAAC,GAAGpB,MAAM,CAAC9D,QAAQ,CAAC+D,QAAQ,CAACmB,CAAC,CAAC,CAAC;MACpE,IAAI,CAACZ,IAAI,EAAE;QACPC,OAAO,GAAGR,QAAQ,CAACmB,CAAC,CAAC;QACrB,OAAO,KAAK;MAChB;IACJ,CAAC,CAAC;IACF,MAAMT,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,YAAYjN,KAAK,GAAG,OAAO,GAAG,EAAE,UAAUkN,OAAO,eAAeT,MAAM,GAAG;IACpI,OAAO;MAAEQ,IAAI;MAAEG;IAAQ,CAAC;EAC5B;EACA,MAAMX,MAAM,GAAGzM,KAAK,GAAGhC,CAAC,CAACkB,EAAE,CAAC,CAAC6O,IAAI,CAAC,CAAC,GAAG/P,CAAC,CAACwI,IAAI,CAACxI,CAAC,CAACkB,EAAE,CAAC,CAAC6O,IAAI,CAAC,CAAC,CAAC;EAC1D,IAAIrB,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC1C,MAAMO,IAAI,GAAGP,QAAQ,CAACD,MAAM,CAAC;IAC7B,MAAMW,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,YAAYjN,KAAK,GAAG,OAAO,GAAG,EAAE,mBAAmB0M,QAAQ,IAAI,GAAG,aAAaD,MAAM,GAAG;IACnJ,OAAO;MAAEQ,IAAI;MAAEG;IAAQ,CAAC;EAC5B;EACA,MAAMH,IAAI,GAAGjN,KAAK,IAAI,CAACuB,KAAK,CAACC,OAAO,CAACkL,QAAQ,CAAC,GAAGD,MAAM,KAAKC,QAAQ,GAAGD,MAAM,CAACvM,OAAO,CAACwM,QAAQ,CAAC,KAAK,CAAC,CAAC;EACtG,MAAMU,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,YAAYjN,KAAK,GAAG,OAAO,GAAG,EAAE,UAAU0M,QAAQ,eAAeD,MAAM,GAAG;EACrI,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD,MAAMY,EAAE,GAAGL,WAAW;AACtB,MAAMM,UAAU,GAAIC,IAAI,IAAK,OAAO;EAChCC,OAAO,EAAED;AACb,CAAC,CAAC;AACF,MAAME,EAAE,GAAGH,UAAU;AACrB,MAAMI,EAAE,GAAInP,EAAE,IAAK;EACf,MAAMuN,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACuC,MAAM;EAC3B,MAAMwL,IAAI,GAAGR,MAAM,GAAG,CAAC;EACvB,MAAMW,OAAO,GAAGA,CAAA,KAAM,YAAYlO,EAAE,WAAW+N,IAAI,GAAG,MAAM,GAAG,EAAE,WAAW;EAC5E,OAAO;IAAEA,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMkB,OAAO,GAAGL,UAAU,CAACI,EAAE,CAAC;AAC9B,MAAME,EAAE,GAAGA,CAACrP,EAAE,EAAEwN,QAAQ,KAAK;EACzB,MAAMD,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACuC,MAAM;EAC3B,MAAMwL,IAAI,GAAGR,MAAM,KAAKC,QAAQ;EAChC,MAAMU,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,mBAAmBP,QAAQ,aAAaD,MAAM,EAAE;EAC3G,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMoB,YAAY,GAAGP,UAAU,CAACM,EAAE,CAAC;AACnC,MAAME,EAAE,GAAGA,CAACvP,EAAE,EAAEwN,QAAQ,KAAK;EACzB,MAAMD,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACwP,IAAI,CAAC,IAAI,CAAC;EAC/B,MAAMzB,IAAI,GAAGR,MAAM,KAAKC,QAAQ;EAChC,MAAMU,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,gBAAgBP,QAAQ,eAAeD,MAAM,GAAG;EAC3G,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMuB,QAAQ,GAAGV,UAAU,CAACQ,EAAE,CAAC;AAC/B,MAAMG,EAAE,GAAGA,CAAC1P,EAAE,EAAEwN,QAAQ,KAAK;EACzB,IAAIA,QAAQ,IAAInL,KAAK,CAACC,OAAO,CAACkL,QAAQ,CAAC,EAAE;IACrC,MAAMD,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACwP,IAAI,CAAC,OAAO,CAAC;IAClC,MAAMG,eAAe,GAAGnC,QAAQ,CAACoC,IAAI,CAAC,GAAG,CAAC;IAC1C,MAAM7B,IAAI,GAAGjP,CAAC,CAACkB,EAAE,CAAC,CAAC6P,QAAQ,CAACF,eAAe,CAAC;IAC5C,MAAMzB,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,mBAAmB4B,eAAe,eAAepC,MAAM,GAAG;IACrH,OAAO;MAAEQ,IAAI;MAAEG;IAAQ,CAAC;EAC5B;EACA,MAAMX,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACwP,IAAI,CAAC,OAAO,CAAC;EAClC,MAAMzB,IAAI,GAAGjP,CAAC,CAACkB,EAAE,CAAC,CAAC6P,QAAQ,CAACrC,QAAQ,CAAC;EACrC,MAAMU,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,mBAAmBP,QAAQ,eAAeD,MAAM,GAAG;EAC9G,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4B,WAAW,GAAGf,UAAU,CAACW,EAAE,CAAC;AAClC,MAAMK,GAAG,GAAGA,CAAC/P,EAAE,EAAEwP,IAAI,EAAEQ,GAAG,KAAK;EAC3B,IAAIrN,QAAQ,CAAC6M,IAAI,CAAC,EAAE;IAChB,IAAIzB,IAAI,GAAG,KAAK;IAChB,IAAIC,OAAO;IACX,KAAK,MAAMhK,GAAG,IAAIG,MAAM,CAACC,IAAI,CAACoL,IAAI,CAAC,EAAE;MACjC,MAAMjC,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACwP,IAAI,CAACxL,GAAG,CAAC;MAC9B,MAAMiK,QAAQ,GAAGuB,IAAI,CAACxL,GAAG,CAAC,KAAKf,SAAS,GAAG,gBAAgBuM,IAAI,CAACxL,GAAG,CAAC,GAAG,GAAG,EAAE;MAC5E+J,IAAI,GAAGT,WAAW,CAACC,MAAM,EAAEiC,IAAI,CAACxL,GAAG,CAAC,CAAC;MACrCgK,OAAO,GAAG,CAACD,IAAI,GAAG,IAAIyB,IAAI,IAAIvB,QAAQ,cAAcV,MAAM,GAAG,GAAG,EAAE;IACtE;IACA,MAAMW,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,sBAAsBC,OAAO,EAAE;IAC1F,OAAO;MAAED,IAAI;MAAEG;IAAQ,CAAC;EAC5B;EACA,MAAMX,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACwP,IAAI,CAACA,IAAI,CAAC;EAC/B,MAAMvB,QAAQ,GAAG+B,GAAG,KAAK/M,SAAS,GAAG,gBAAgB+M,GAAG,GAAG,GAAG,EAAE;EAChE,MAAMjC,IAAI,GAAGT,WAAW,CAACC,MAAM,EAAEyC,GAAG,CAAC;EACrC,MAAM9B,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,uBAAuByB,IAAI,IAAIvB,QAAQ,cAAcV,MAAM,GAAG;EACzH,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA,MAAM+B,eAAe,GAAGlB,UAAU,CAACgB,GAAG,CAAC;AACvC,MAAMG,GAAG,GAAGA,CAAClQ,EAAE,EAAE6N,IAAI,EAAEmC,GAAG,KAAK;EAC3B,IAAIrN,QAAQ,CAACkL,IAAI,CAAC,EAAE;IAChB,OAAOD,aAAa,CAAC5N,EAAE,EAAE6N,IAAI,EAAEP,WAAW,CAAC;EAC/C;EACA,MAAMC,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAAC6N,IAAI,CAACA,IAAI,CAAC;EAC/B,MAAMI,QAAQ,GAAG+B,GAAG,KAAK/M,SAAS,GAAG,gBAAgB+M,GAAG,GAAG,GAAG,EAAE;EAChE,MAAMjC,IAAI,GAAGT,WAAW,CAACC,MAAM,EAAEyC,GAAG,CAAC;EACrC,MAAM9B,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,sBAAsBF,IAAI,IAAII,QAAQ,cAAcV,MAAM,GAAG;EACxH,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMiC,cAAc,GAAGpB,UAAU,CAACmB,GAAG,CAAC;AACtC,MAAME,GAAG,GAAGA,CAACpQ,EAAE,EAAE6N,IAAI,EAAEmC,GAAG,KAAK;EAC3B,IAAIrN,QAAQ,CAACkL,IAAI,CAAC,EAAE;IAChB,OAAOD,aAAa,CAAC5N,EAAE,EAAE6N,IAAI,EAAEH,gBAAgB,CAAC;EACpD;EACA,MAAMH,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAAC6N,IAAI,CAACA,IAAI,CAAC;EAC/B,MAAMI,QAAQ,GAAG+B,GAAG,KAAK/M,SAAS,GAAG,gBAAgB+M,GAAG,GAAG,GAAG,EAAE;EAChE,MAAMjC,IAAI,GAAGL,gBAAgB,CAACH,MAAM,EAAEyC,GAAG,CAAC;EAC1C,MAAM9B,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,sBAAsBF,IAAI,IAAII,QAAQ,cAAcV,MAAM,GAAG;EACxH,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD,MAAMmC,iBAAiB,GAAGtB,UAAU,CAACqB,GAAG,CAAC;AACzC,MAAME,GAAG,GAAGA,CAACtQ,EAAE,EAAEwN,QAAQ,EAAE1M,KAAK,GAAG,KAAK,KAAK2N,WAAW,CAACzO,EAAE,EAAEwN,QAAQ,EAAE1M,KAAK,CAAC;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyP,UAAU,GAAGxB,UAAU,CAACuB,GAAG,CAAC;AAClC,MAAME,GAAG,GAAGA,CAACxQ,EAAE,EAAEwN,QAAQ,KAAKiB,WAAW,CAACzO,EAAE,EAAEwN,QAAQ,EAAE,IAAI,CAAC;AAC7D,MAAMiD,eAAe,GAAG1B,UAAU,CAACyB,GAAG,CAAC;AACvC,MAAME,aAAa,GAAGH,UAAU;AAChC,MAAMI,GAAG,GAAGA,CAAC3Q,EAAE,EAAEwN,QAAQ,KAAK;EAC1B,IAAIA,QAAQ,IAAInL,KAAK,CAACC,OAAO,CAACkL,QAAQ,CAAC,EAAE;IACrC,IAAID,MAAM;IACV,IAAIQ,IAAI,GAAG,KAAK;IAChB,IAAIC,OAAO;IACXlP,CAAC,CAACkB,EAAE,CAAC,CAAC0O,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACjBrB,MAAM,GAAGzO,CAAC,CAAC8P,CAAC,CAAC,CAACoB,GAAG,CAAC,CAAC;MACnBjC,IAAI,GAAGR,MAAM,KAAKC,QAAQ,CAACmB,CAAC,CAAC;MAC7B,IAAI,CAACZ,IAAI,EAAE;QACPC,OAAO,GAAGR,QAAQ,CAACmB,CAAC,CAAC;QACrB,OAAO,KAAK;MAChB;IACJ,CAAC,CAAC;IACF,MAAMT,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,mBAAmBC,OAAO,eAAeT,MAAM,GAAG;IAC7G,OAAO;MAAEQ,IAAI;MAAEG;IAAQ,CAAC;EAC5B;EACA,MAAMX,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACgQ,GAAG,CAAC,CAAC;EAC1B,MAAMjC,IAAI,GAAGR,MAAM,KAAKC,QAAQ;EAChC,MAAMU,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,mBAAmBP,QAAQ,eAAeD,MAAM,GAAG;EAC9G,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM0C,WAAW,GAAG7B,UAAU,CAAC4B,GAAG,CAAC;AACnC,MAAME,cAAc,GAAGD,WAAW;AAClC,MAAME,GAAG,GAAGA,CAAC9Q,EAAE,EAAEwN,QAAQ,KAAK;EAC1B,MAAMO,IAAI,GAAGK,MAAM,CAACpO,EAAE,EAAEwN,QAAQ,CAAC;EACjC,MAAMU,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,gBAAgBgD,IAAI,CAACC,SAAS,CAACxD,QAAQ,CAAC,EAAE;EACrG,OAAO;IAAEO,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+C,WAAW,GAAGlC,UAAU,CAAC+B,GAAG,CAAC;AACnC,MAAMI,GAAG,GAAGA,CAAClR,EAAE,EAAE;EAAEmR,IAAI;EAAEnB;AAAI,CAAC,KAAK;EAC/B,MAAMzC,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACmR,IAAI,CAACA,IAAI,CAAC;EAC/B,MAAMlD,QAAQ,GAAG+B,GAAG,KAAK/M,SAAS,GAAG,gBAAgB+M,GAAG,GAAG,GAAG,EAAE;EAChE,MAAMjC,IAAI,GAAGT,WAAW,CAACC,MAAM,EAAEyC,GAAG,CAAC;EACrC,MAAM9B,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,kBAAkBoD,IAAI,IAAIlD,QAAQ,cAAcV,MAAM,GAAG;EACpH,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMkD,UAAU,GAAGrC,UAAU,CAACmC,GAAG,CAAC;AAClC,MAAMG,GAAG,GAAGrR,EAAE,IAAI;EACd,MAAM+N,IAAI,GAAGjP,CAAC,CAACkB,EAAE,CAAC,CAACsR,EAAE,CAAC,UAAU,CAAC;EACjC,MAAMpD,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,gBAAgB;EAC3E,OAAO;IAAEA,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMqD,WAAW,GAAGxC,UAAU,CAACsC,GAAG,CAAC;AACnC,MAAMG,GAAG,GAAGxR,EAAE,IAAI;EACd,MAAM+N,IAAI,GAAGjP,CAAC,CAACkB,EAAE,CAAC,CAACsR,EAAE,CAAC,WAAW,CAAC;EAClC,MAAMpD,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,iBAAiB;EAC5E,OAAO;IAAEA,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMuD,YAAY,GAAG1C,UAAU,CAACyC,GAAG,CAAC;AACpC,MAAME,GAAG,GAAG1R,EAAE,IAAI;EACd,MAAM+N,IAAI,GAAGjP,CAAC,CAACkB,EAAE,CAAC,CAACsR,EAAE,CAAC,QAAQ,CAAC;EAC/B,MAAMpD,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,cAAc;EACzE,OAAO;IAAEA,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMyD,SAAS,GAAG5C,UAAU,CAAC2C,GAAG,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,QAAQA,CAACC,YAAY,EAAE;EAC5B,IAAI7R,EAAE,GAAGlB,CAAC,CAAC+S,YAAY,CAAC,CAAC,CAAC,CAAC;EAC3B,IAAI,CAAC7R,EAAE,EAAE;IACL,OAAO,IAAI;EACf;EACA,MAAM8R,UAAU,GAAG,CACf9R,EAAE,IAAI,EAAEA,EAAE,CAAC+R,WAAW,IAAI/R,EAAE,CAACgS,YAAY,IAAIhS,EAAE,CAACiS,cAAc,CAAC,CAAC,CAAC1P,MAAM,CAAC,EACxEvC,EAAE,IAAIA,EAAE,CAACuO,KAAK,CAAC2D,OAAO,KAAK,MAAM,EACjClS,EAAE,IAAIA,EAAE,CAACuO,KAAK,CAAC4D,UAAU,KAAK,QAAQ,EACtCnS,EAAE,IAAIA,EAAE,CAAC4F,IAAI,KAAK,QAAQ,EAC1B5F,EAAE,IAAIA,EAAE,CAACoS,YAAY,CAAC,QAAQ,CAAC,CAClC;EACD,IAAIjF,gBAAgB,CAAC,CAAC,EAAE;IACpB;IACA;IACA2E,UAAU,CAACvM,KAAK,CAAC,CAAC;EACtB;EACA,OAAOvF,EAAE,EAAE;IACP,IAAIA,EAAE,KAAKiG,QAAQ,EAAE;MACjB;IACJ;IACA,IAAI6L,UAAU,CAACO,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACtS,EAAE,CAAC,CAAC,EAAE;MACnC,OAAO,IAAI;IACf;IACAA,EAAE,GAAGA,EAAE,CAACuS,UAAU;EACtB;EACA,OAAO,KAAK;AAChB;AACA,MAAMC,GAAG,GAAGxS,EAAE,IAAI;EACd,MAAM+N,IAAI,GAAG6D,QAAQ,CAAC5R,EAAE,CAAC;EACzB,MAAMkO,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,eAAe;EAC1E,OAAO;IAAEA,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuE,UAAU,GAAG1D,UAAU,CAACyD,GAAG,CAAC;AAClC,MAAME,GAAG,GAAG1S,EAAE,IAAI;EACd,MAAM+N,IAAI,GAAGjP,CAAC,CAACkB,EAAE,CAAC,CAACsR,EAAE,CAAC,WAAW,CAAC;EAClC,MAAMpD,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,iBAAiB;EAC5E,OAAO;IAAEA,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyE,YAAY,GAAG5D,UAAU,CAAC2D,GAAG,CAAC;AACpC,MAAME,GAAG,GAAG5S,EAAE,IAAI;EACd,MAAM+N,IAAI,GAAG,CAAC6D,QAAQ,CAAC5R,EAAE,CAAC;EAC1B,MAAMkO,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,gBAAgB;EAC3E,OAAO;IAAEA,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2E,WAAW,GAAG9D,UAAU,CAAC6D,GAAG,CAAC;AACnC,MAAME,GAAG,GAAG9S,EAAE,IAAI;EACd,MAAMyI,OAAO,GAAG3J,CAAC,CAACkB,EAAE,CAAC,CAACyD,GAAG,CAAC,CAAC,CAAC;EAC5B,MAAMsK,IAAI,GAAGtF,OAAO,KAAKA,OAAO,CAACsK,aAAa,CAACC,aAAa;EAC5D,MAAM9E,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,gBAAgB;EAC3E,OAAO;IAAEA,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM+E,WAAW,GAAGlE,UAAU,CAAC+D,GAAG,CAAC;AACnC,MAAMI,GAAG,GAAGA,CAAClT,EAAE,EAAEwN,QAAQ,KAAK;EAC1B,MAAMD,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACmT,MAAM,CAAC3F,QAAQ,CAAC,CAACjL,MAAM;EAC5C,MAAMwL,IAAI,GAAGR,MAAM,GAAG,CAAC;EACvB,MAAMW,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,sBAAsBP,QAAQ,GAAG;EAC5F,OAAO;IAAEO,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkF,aAAa,GAAGrE,UAAU,CAACmE,GAAG,CAAC;AACrC,MAAMG,GAAG,GAAGA,CAACrT,EAAE,EAAE+K,QAAQ,KAAK;EAC1B,MAAMwC,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACsT,IAAI,CAACvI,QAAQ,CAAC,CAACxI,MAAM;EAC1C,MAAMwL,IAAI,GAAGR,MAAM,GAAG,CAAC;EACvB,MAAMW,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,sBAAsBhD,QAAQ,GAAG;EAC5F,OAAO;IAAEgD,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMqF,gBAAgB,GAAGxE,UAAU,CAACsE,GAAG,CAAC;AACxC,MAAMG,GAAG,GAAGA,CAACxT,EAAE,EAAE;EAAE+K,QAAQ;EAAE8D;AAAK,CAAC,KAAK;EACpC,MAAMtB,MAAM,GAAGzO,CAAC,CAACwI,IAAI,CAACxI,CAAC,CAACkB,EAAE,CAAC,CACtBsT,IAAI,CAACvI,QAAQ,CAAC,CACd8D,IAAI,CAAC,CAAC,CAAC;EACZ,IAAIA,IAAI,IAAI/P,CAAC,CAAC2U,UAAU,CAAC5E,IAAI,CAAC3N,IAAI,CAAC,EAAE;IACjC,MAAM6M,IAAI,GAAGc,IAAI,CAAC3N,IAAI,CAACqM,MAAM,CAAC;IAC9B,MAAMW,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,wBAAwBhD,QAAQ,yBAAyB8D,IAAI,IAAI,GAAG,aAAatB,MAAM,GAAG;IACrJ,OAAO;MAAEQ,IAAI;MAAEG;IAAQ,CAAC;EAC5B;EACA,MAAMH,IAAI,GAAGR,MAAM,CAACvM,OAAO,CAAC6N,IAAI,CAAC,KAAK,CAAC,CAAC;EACxC,MAAMX,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,wBAAwBhD,QAAQ,gBAAgB8D,IAAI,eAAetB,MAAM,GAAG;EACvI,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMwF,wBAAwB,GAAG3E,UAAU,CAACyE,GAAG,CAAC;AAChD,MAAMG,GAAG,GAAGA,CAAC3T,EAAE,EAAEwN,QAAQ,KAAK;EAC1B,IAAIA,QAAQ,YAAY9K,iBAAiB,EAAE;IACvC,MAAM6K,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACsT,IAAI,CAAC,WAAW,CAAC;IACtC,MAAMvF,IAAI,GAAGR,MAAM,CAAC+D,EAAE,CAACxS,CAAC,CAAC0O,QAAQ,CAAC,CAAC;IACnC,MAAMU,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,sBAAsBP,QAAQ,CAACoG,SAAS,gBAAgBrG,MAAM,CAAC,CAAC,CAAC,CAACqG,SAAS,IAAI;IAC1I,OAAO;MAAE7F,IAAI;MAAEG;IAAQ,CAAC;EAC5B;EACA,IAAI9L,wBAAwB,CAACoL,QAAQ,CAAC,EAAE;IACpC,MAAMD,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACsT,IAAI,CAAC,WAAW,CAAC;IACtC,MAAMvF,IAAI,GAAGR,MAAM,CAAChL,MAAM,KAAKiL,QAAQ,CAACjL,MAAM,IAAIgL,MAAM,CAACsG,OAAO,CAAC,CAAC,CAACrR,KAAK,CAAC,CAAC3B,CAAC,EAAEiT,KAAK,KAAKhV,CAAC,CAACyO,MAAM,CAACuG,KAAK,CAAC,CAAC,CAACxC,EAAE,CAAC9D,QAAQ,CAACsG,KAAK,CAAC,CAAC,CAAC;IAC5H,MAAMC,qBAAqB,GAAGjV,CAAC,CAAC0O,QAAQ,CAAC,CACpC/J,GAAG,CAAC,CAAC,CACLpE,GAAG,CAAC6J,MAAM,IAAIA,MAAM,CAAC0K,SAAS,CAAC,CAC/BhE,IAAI,CAAC,GAAG,CAAC;IACd,MAAMoE,mBAAmB,GAAGzG,MAAM,CAC7B9J,GAAG,CAAC,CAAC,CACLpE,GAAG,CAAC6J,MAAM,IAAIA,MAAM,CAAC0K,SAAS,CAAC,CAC/BhE,IAAI,CAAC,GAAG,CAAC;IACd,MAAM1B,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,sBAAsBgG,qBAAqB,gBAAgBC,mBAAmB,IAAI;IAC7I,OAAO;MAAEjG,IAAI;MAAEG;IAAQ,CAAC;EAC5B;EACA,MAAMX,MAAM,GAAGzO,CAAC,CAACkB,EAAE,CAAC,CAACgQ,GAAG,CAAC,CAAC;EAC1B,MAAMjC,IAAI,GAAGgD,IAAI,CAACC,SAAS,CAAC,CAAC,GAAGzD,MAAM,CAAC,CAAC,KAAKwD,IAAI,CAACC,SAAS,CAAC,CAAC,GAAGxD,QAAQ,CAAC,CAAC;EAC1E,MAAMuG,qBAAqB,GAAG1R,KAAK,CAACC,OAAO,CAACkL,QAAQ,CAAC,GAC/CA,QAAQ,CAACrI,MAAM,CAAC,CAAC8O,GAAG,EAAEjE,GAAG,EAAErB,CAAC,KAAKsF,GAAG,GAAG,GAAGtF,CAAC,KAAKnB,QAAQ,CAACjL,MAAM,GAAG,EAAE,GAAG,GAAG,GAAGyN,GAAG,EAAE,CAAC,GACnFxC,QAAQ;EACd,MAAMU,OAAO,GAAGA,CAAA,KAAM,mBAAmBH,IAAI,GAAG,MAAM,GAAG,EAAE,sBAAsBgG,qBAAqB,gBAAgBxG,MAAM,IAAI;EAChI,OAAO;IAAEQ,IAAI;IAAEG;EAAQ,CAAC;AAC5B,CAAC;AACD,MAAMgG,qBAAqB,GAAGnF,UAAU,CAAC4E,GAAG,CAAC;AAE7C,IAAIQ,cAAc,GAAG,aAAahQ,MAAM,CAACiQ,MAAM,CAAC;EAC5CC,SAAS,EAAE,IAAI;EACfjF,OAAO,EAAEA,OAAO;EAChBE,YAAY,EAAEA,YAAY;EAC1BG,QAAQ,EAAEA,QAAQ;EAClBK,WAAW,EAAEA,WAAW;EACxBG,eAAe,EAAEA,eAAe;EAChCE,cAAc,EAAEA,cAAc;EAC9BE,iBAAiB,EAAEA,iBAAiB;EACpCE,UAAU,EAAEA,UAAU;EACtBE,eAAe,EAAEA,eAAe;EAChCC,aAAa,EAAEA,aAAa;EAC5BE,WAAW,EAAEA,WAAW;EACxBC,cAAc,EAAEA,cAAc;EAC9BI,WAAW,EAAEA,WAAW;EACxBG,UAAU,EAAEA,UAAU;EACtBG,WAAW,EAAEA,WAAW;EACxBE,YAAY,EAAEA,YAAY;EAC1BE,SAAS,EAAEA,SAAS;EACpBc,UAAU,EAAEA,UAAU;EACtBE,YAAY,EAAEA,YAAY;EAC1BE,WAAW,EAAEA,WAAW;EACxBI,WAAW,EAAEA,WAAW;EACxBG,aAAa,EAAEA,aAAa;EAC5BG,gBAAgB,EAAEA,gBAAgB;EAClCG,wBAAwB,EAAEA,wBAAwB;EAClDQ,qBAAqB,EAAEA,qBAAqB;EAC5CxO,EAAE,EAAE+H,IAAI;EACRE,EAAE,EAAEA,EAAE;EACNQ,EAAE,EAAEA,EAAE;EACNK,EAAE,EAAEA,EAAE;EACNM,EAAE,EAAEA,EAAE;EACNI,EAAE,EAAEA,EAAE;EACNC,EAAE,EAAEA,EAAE;EACNE,EAAE,EAAEA,EAAE;EACNE,EAAE,EAAEA,EAAE;EACNG,EAAE,EAAEA,EAAE;EACNK,GAAG,EAAEA,GAAG;EACRG,GAAG,EAAEA,GAAG;EACRE,GAAG,EAAEA,GAAG;EACRE,GAAG,EAAEA,GAAG;EACRE,GAAG,EAAEA,GAAG;EACRG,GAAG,EAAEA,GAAG;EACRG,GAAG,EAAEA,GAAG;EACRI,GAAG,EAAEA,GAAG;EACRG,GAAG,EAAEA,GAAG;EACRG,GAAG,EAAEA,GAAG;EACRE,GAAG,EAAEA,GAAG;EACRc,GAAG,EAAEA,GAAG;EACRE,GAAG,EAAEA,GAAG;EACRE,GAAG,EAAEA,GAAG;EACRE,GAAG,EAAEA,GAAG;EACRI,GAAG,EAAEA,GAAG;EACRG,GAAG,EAAEA,GAAG;EACRG,GAAG,EAAEA,GAAG;EACRG,GAAG,EAAEA;AACT,CAAC,CAAC;AAEF,SAASW,WAAWA,CAACC,QAAQ,EAAE;EAC3B,IAAI,CAACA,QAAQ,EACT;EACJ,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;IAChCA,OAAO,CAACF,WAAW,CAACC,QAAQ,CAAC;EACjC;EACA,IAAI,OAAOE,IAAI,KAAK,WAAW,EAAE;IAC7B,MAAMC,gBAAgB,GAAG,CAAC,CAAC;IAC3B,KAAK,MAAM1Q,GAAG,IAAIG,MAAM,CAACC,IAAI,CAACmQ,QAAQ,CAAC,EAAE;MACrC,IAAIvQ,GAAG,CAAC2Q,UAAU,CAAC,IAAI,CAAC,EACpBD,gBAAgB,CAAC1Q,GAAG,CAAC,GAAGuQ,QAAQ,CAACvQ,GAAG,CAAC,CAAC,CAAC,CAACiL,OAAO;IACvD;IACA2F,MAAM,CAACC,MAAM,CAACH,gBAAgB,CAAC;EACnC;AACJ;AAEA,IAAII,OAAO,GAAG;EACVC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE;AACb,CAAC;AACD,SAASC,uBAAuBA,CAAClM,MAAM,EAAE;EACrC8L,OAAO,GAAG3Q,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEiQ,OAAO,CAAC,EAAE9L,MAAM,CAAC;AAC/D;AACA,SAASmM,oBAAoBA,CAAA,EAAG;EAC5B,OAAOL,OAAO;AAClB;;AAEA;AACA;AACA;AACA,SAASM,aAAaA,CAACrV,OAAO,EAAE;EAC5B,MAAM+U,OAAO,GAAG3Q,MAAM,CAACU,MAAM,CAAC;IAAEoQ,OAAO,EAAE,EAAE;IAAED,YAAY,EAAE,EAAE;IAAED,SAAS,EAAE;EAAG,CAAC,EAAEI,oBAAoB,CAAC,CAAC,CAAC;EACvG,OAAO;IACHH,YAAY,EAAE,CAAC,GAAGF,OAAO,CAACE,YAAY,EAAE,GAAGjV,OAAO,CAACiV,YAAY,EAAE,GAAGjV,OAAO,CAACsV,eAAe,CAAC;IAC5FJ,OAAO,EAAE,CAAC,IAAIlV,OAAO,CAACuV,iBAAiB,GAAG,CAACvW,oBAAoB,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG+V,OAAO,CAACG,OAAO,EAAE,GAAGlV,OAAO,CAACkV,OAAO,CAAC;IAC/GF,SAAS,EAAE,CAAC,GAAGD,OAAO,CAACC,SAAS,EAAE,GAAGhV,OAAO,CAACgV,SAAS,EAAE,GAAGhV,OAAO,CAACwV,KAAK,CAAClW,GAAG,CAACuG,IAAI,IAAI7F,OAAO,CAACyV,YAAY,CAAC5P,IAAI,CAAC,CAAC,CAAC;IACjHyP,eAAe,EAAE,CAAC,GAAGtV,OAAO,CAACsV,eAAe;EAChD,CAAC;AACL;;AAEA;AACA;AACA;AACA,SAASI,sBAAsBA,CAAC1V,OAAO,EAAE;EACrC,MAAM2V,cAAc,GAAGN,aAAa,CAACrV,OAAO,CAAC;EAC7C,IAAIA,OAAO,CAAC4V,gBAAgB,EAAE;IAC1BD,cAAc,CAACV,YAAY,CAACY,IAAI,CAAC7V,OAAO,CAACsM,SAAS,CAAC;EACvD;EACAqJ,cAAc,CAACG,OAAO,GAAG,CAAC9V,OAAO,CAAC+V,OAAO,GAAGlY,gBAAgB,GAAGmC,OAAO,CAAC8V,OAAO,IAAI,EAAE,CAAC;EACrF,OAAOH,cAAc;AACzB;;AAEA;AACA;AACA;AACA,SAASK,KAAKA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAChC;EACA,OAAO9R,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEmR,QAAQ,CAAC,EAAEC,SAAS,CAAC;AAChE;;AAEA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,KAAK,EAAEC,WAAW,EAAE;EACnD,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKjS,MAAM,CAACmS,SAAS,EAAE;IAC9C;EACJ;EACA,KAAK,MAAMtS,GAAG,IAAIG,MAAM,CAACoS,mBAAmB,CAACH,KAAK,CAAC,EAAE;IACjD,MAAMI,UAAU,GAAGrS,MAAM,CAACsS,wBAAwB,CAACL,KAAK,EAAEpS,GAAG,CAAC;IAC9D,IAAI,CAACwS,UAAU,EAAE;MACb;IACJ;IACA,IAAI,OAAOA,UAAU,CAACxU,KAAK,KAAK,UAAU,IAAIgC,GAAG,KAAK,aAAa,IAAI,OAAOmS,IAAI,CAACnS,GAAG,CAAC,KAAK,WAAW,EAAE;MACrGmS,IAAI,CAACnS,GAAG,CAAC,GAAGqS,WAAW,CAACrS,GAAG,CAAC;IAChC,CAAC,MACI,IAAIwS,UAAU,CAAC/S,GAAG,IAAI,CAAC0S,IAAI,CAAC7H,cAAc,CAACtK,GAAG,CAAC,EAAE;MAClDG,MAAM,CAACkC,cAAc,CAAC8P,IAAI,EAAEnS,GAAG,EAAE;QAC7B0S,GAAG,EAAE1U,KAAK,IAAKmU,IAAI,CAAC,IAAInS,GAAG,EAAE,CAAC,GAAGhC,KAAM;QACvCyB,GAAG,EAAEA,CAAA,KAAM0S,IAAI,CAAC,IAAInS,GAAG,EAAE,CAAC;QAC1B4D,YAAY,EAAE;MAClB,CAAC,CAAC;IACN;EACJ;EACAsO,mBAAmB,CAACC,IAAI,EAAEhS,MAAM,CAACwS,cAAc,CAACP,KAAK,CAAC,EAAEC,WAAW,CAAC;EACpEF,IAAI,CAACS,cAAc,GAAG,MAAMT,IAAI;AACpC;AACA;AACA;AACA;AACA,SAASU,eAAeA,CAACjR,IAAI,EAAEkR,QAAQ,EAAE;EACrC,MAAMX,IAAI,GAAGhS,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEiS,QAAQ,CAAC,IAAI,CAAC,CAAC;EAC9CZ,mBAAmB,CAACC,IAAI,EAAEvQ,IAAI,CAAC0Q,SAAS,EAAES,IAAI,IAAI;IAC9C,MAAMC,MAAM,GAAGxC,OAAO,CAACyC,SAAS,CAACF,IAAI,CAAC;IACtCC,MAAM,CAACE,WAAW,GAAIC,EAAE,IAAKH,MAAM,CAACI,GAAG,CAACC,QAAQ,CAACF,EAAE,CAAC;IACpDH,MAAM,CAACM,SAAS,GAAGtH,GAAG,IAAIgH,MAAM,CAACI,GAAG,CAACG,WAAW,CAACvH,GAAG,CAAC;IACrDgH,MAAM,CAACQ,KAAK,GAAG,MAAMR,MAAM,CAACS,KAAK,CAACD,KAAK,CAAC,CAAC;IACzC;IACAR,MAAM,CAACI,GAAG,CAACG,WAAW,CAAC,IAAI,CAAC;IAC5B,OAAOP,MAAM;EACjB,CAAC,CAAC;EACF,OAAOb,IAAI;AACf;AACA;AACA;AACA;AACA,SAASX,YAAYA,CAAC5P,IAAI,EAAE8R,UAAU,EAAE;EACpC,OAAO;IACHC,OAAO,EAAE/R,IAAI;IACbgS,UAAU,EAAEA,CAAA,KAAMf,eAAe,CAACjR,IAAI,EAAE8R,UAAU;EACtD,CAAC;AACL;AAEA,MAAMG,cAAc,GAAG;EACnBvC,iBAAiB,EAAE,IAAI;EACvBD,eAAe,EAAE,EAAE;EACnBE,KAAK,EAAE,EAAE;EACTC,YAAY;EACZT,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,EAAE;EACXY,OAAO,EAAE,EAAE;EACXiC,eAAe,EAAE;AACrB,CAAC;AACD;AACA;AACA;AACA,SAASC,qBAAqBA,CAAChY,OAAO,EAAE;EACpC,OAAOgW,KAAK,CAAC8B,cAAc,EAAE9X,OAAO,CAAC;AACzC;AAEA,MAAMiY,uBAAuB,GAAG7T,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEkT,qBAAqB,CAAC,CAAC,CAAC,EAAE;EAAEjC,OAAO,EAAE,KAAK;EAAEH,gBAAgB,EAAE,IAAI;EAAEvL,aAAa,EAAE,IAAI;EAAE6N,kBAAkB,EAAE,EAAE;EAAEC,sBAAsB,EAAE,EAAE;EAAEC,cAAc,EAAE,EAAE;EAAEC,2BAA2B,EAAE;AAAG,CAAC,CAAC;AACnQ;AACA;AACA;AACA,SAASC,0BAA0BA,CAACpC,SAAS,EAAE;EAC3C,OAAOF,KAAK,CAACiC,uBAAuB,EAAE/B,SAAS,CAAC;AACpD;;AAEA;AACA;AACA;AACA,SAASqC,6CAA6CA,CAACvY,OAAO,EAAE;EAC5D,MAAMwY,oBAAoB,GAAGxY,OAAO,CAACkY,kBAAkB,CAAC1V,MAAM,IAAIxC,OAAO,CAACoY,cAAc,CAAC5V,MAAM;EAC/F,MAAMiW,gBAAgB,GAAGzY,OAAO,CAACmY,sBAAsB,CAAC3V,MAAM,IAAIxC,OAAO,CAACqY,2BAA2B,CAAC7V,MAAM;EAC5G,IAAIgW,oBAAoB,IAAIC,gBAAgB,EAAE;IAC1C,IAAIC,qBAAqB,GAAG,CAAC,CAAC;IAC9B,IAAIF,oBAAoB,EAAE;MACtBE,qBAAqB,GAAG;QACpB1D,SAAS,EAAE,CAAC,GAAGhV,OAAO,CAACkY,kBAAkB,EAAE,GAAGlY,OAAO,CAACoY,cAAc,CAAC9Y,GAAG,CAAC6E,CAAC,IAAInE,OAAO,CAACyV,YAAY,CAACtR,CAAC,CAAC,CAAC;MAC1G,CAAC;IACL;IACA,IAAIsU,gBAAgB,EAAE;MAClBC,qBAAqB,GAAGtU,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAE4T,qBAAqB,CAAC,EAAE;QAAEC,aAAa,EAAE,CAAC,GAAG3Y,OAAO,CAACmY,sBAAsB,EAAE,GAAGnY,OAAO,CAACqY,2BAA2B,CAAC/Y,GAAG,CAAC6E,CAAC,IAAInE,OAAO,CAACyV,YAAY,CAACtR,CAAC,CAAC,CAAC;MAAE,CAAC,CAAC;IACrN;IACA3F,OAAO,CAACoa,iBAAiB,CAAC5Y,OAAO,CAACsM,SAAS,EAAE;MACzCqK,GAAG,EAAE+B;IACT,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA,SAASX,eAAeA,CAAC/X,OAAO,EAAE;EAC9B,IAAIA,OAAO,CAAC+X,eAAe,CAACvV,MAAM,EAAE;IAChCxC,OAAO,CAAC+X,eAAe,CAACxO,OAAO,CAACsP,cAAc,IAAI;MAC9C,MAAM,CAACC,QAAQ,EAAEC,QAAQ,CAAC,GAAGF,cAAc;MAC3Cra,OAAO,CAACqa,cAAc,CAACC,QAAQ,EAAEC,QAAQ,CAAC;IAC9C,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,aAAa,EAAE;EAC3C,MAAMjZ,OAAO,GAAGmC,MAAM,CAAC8W,aAAa,CAAC,GAC/BX,0BAA0B,CAAC;IAAEhM,SAAS,EAAE2M;EAAc,CAAC,CAAC,GACxDX,0BAA0B,CAACW,aAAa,CAAC;EAC/C,MAAMtD,cAAc,GAAGD,sBAAsB,CAAC1V,OAAO,CAAC;EACtDkZ,UAAU,CAACxa,KAAK,CAAC,MAAM;IACnB6V,WAAW,CAACH,cAAc,CAAC;IAC3B5V,OAAO,CAAC2a,sBAAsB,CAACxD,cAAc,CAAC,CAACkD,cAAc,CAAC/Z,2BAA2B,EAAE;MACvF6X,GAAG,EAAE;QACDrB,eAAe,EAAEK,cAAc,CAACL;MACpC;IACJ,CAAC,CAAC;IACFyC,eAAe,CAAC/X,OAAO,CAAC;IACxBuY,6CAA6C,CAACvY,OAAO,CAAC;IACtDxB,OAAO,CAAC4a,iBAAiB,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC;EACH,OAAQlD,SAAS,IAAK;IAClB,MAAMD,QAAQ,GAAG;MAAEoD,KAAK,EAAE,CAAC,CAAC;MAAEhP,aAAa,EAAE,IAAI;MAAE2K,SAAS,EAAE;IAAG,CAAC;IAClE,MAAM;MAAE3K,aAAa;MAAEgP,KAAK;MAAErE;IAAU,CAAC,GAAG5Q,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEmR,QAAQ,CAAC,EAAEC,SAAS,CAAC;IACjG,IAAIlB,SAAS,IAAIA,SAAS,CAACxS,MAAM,EAAE;MAC/BwS,SAAS,CAACzL,OAAO,CAAE+P,QAAQ,IAAK;QAC5B9a,OAAO,CAAC+a,gBAAgB,CAACD,QAAQ,CAAC1B,OAAO,EAAE0B,QAAQ,CAAC;MACxD,CAAC,CAAC;IACN;IACA,MAAME,SAAS,GAAGC,eAAe,CAACzZ,OAAO,EAAEqZ,KAAK,CAAC;IACjD,IAAIrZ,OAAO,CAACqK,aAAa,IAAIA,aAAa,EAAE;MACxCmP,SAAS,CAACnP,aAAa,CAAC,CAAC;IAC7B;IACA,OAAOmP,SAAS;EACpB,CAAC;AACL;AACA,SAASC,eAAeA,CAACzZ,OAAO,EAAEqZ,KAAK,EAAE;EACrC,MAAMlP,OAAO,GAAG3L,OAAO,CAACkb,eAAe,CAAC1Z,OAAO,CAACsM,SAAS,CAAC;EAC1D,MAAMlC,YAAY,GAAGD,OAAO,CAACC,YAAY;EACzC,MAAMkC,SAAS,GAAG3I,QAAQ,CAACwG,OAAO,CAACwP,iBAAiB,EAAEN,KAAK,CAAC;EAC5D,OAAO,IAAIhN,SAAS,CAAClC,OAAO,EAAEC,YAAY,EAAEkC,SAAS,EAAElC,YAAY,CAACjH,aAAa,CAAC;AACtF;;AAEA;AACA;AACA;AACA,MAAMyW,aAAa,SAASvN,SAAS,CAAC;EAClCzM,WAAWA,CAACia,aAAa,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAE5P,YAAY,EAAEuP,iBAAiB,EAAEjR,OAAO,EAAE;IAC7G,KAAK,CAACsR,WAAW,EAAE5P,YAAY,EAAEuP,iBAAiB,EAAEjR,OAAO,CAAC;IAC5D,IAAI,CAACmR,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC5P,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC1B,OAAO,GAAGA,OAAO;EAC1B;EACAuR,SAASA,CAAClX,mBAAmB,EAAE/C,OAAO,EAAE;IACpC,IAAI,CAACA,OAAO,IAAI,CAAC,CAAC,EAAEgD,IAAI,IAAIhB,QAAQ,CAACe,mBAAmB,CAAC,EAAE;MACvD,OAAOmD,QAAQ,CAACkD,aAAa,CAACrG,mBAAmB,CAAC;IACtD;IACA,OAAOF,WAAW,CAAC,IAAI,CAACiX,gBAAgB,CAAC,CAAC/W,mBAAmB,EAAE/C,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;EACtF;EACAka,YAAYA,CAACnX,mBAAmB,EAAE/C,OAAO,EAAE;IACvC,IAAI,CAACA,OAAO,IAAI,CAAC,CAAC,EAAEgD,IAAI,IAAIhB,QAAQ,CAACe,mBAAmB,CAAC,EAAE;MACvD,OAAOT,KAAK,CAACiI,IAAI,CAACrE,QAAQ,CAACsD,gBAAgB,CAACzG,mBAAmB,CAAC,CAAC;IACrE;IACA,OAAOF,WAAW,CAAC,IAAI,CAACiX,gBAAgB,CAAC,CAAC/W,mBAAmB,EAAE/C,OAAO,CAAC;EAC3E;EACAma,YAAYA,CAACxP,KAAK,EAAE1I,KAAK,EAAE;IACvB0B,QAAQ,CAAC,IAAI,CAACkW,aAAa,EAAElP,KAAK,EAAE1I,KAAK,EAAE,KAAK,CAAC;IACjD,IAAI,CAACoI,aAAa,CAAC,CAAC;EACxB;AACJ;;AAEA;AACA;AACA,SAAS+P,eAAeA,CAACvU,IAAI,EAAE;EAC3B,OAAOwU,SAAS,IAAIA,SAAS,CAACC,cAAc,CAAC5Q,QAAQ,CAAC7D,IAAI,CAAC;AAC/D;;AAEA;AACA;AACA;AACA,SAAS0U,8BAA8BA,CAACva,OAAO,EAAE;EAC7C,MAAM2V,cAAc,GAAGD,sBAAsB,CAAC1V,OAAO,CAAC;EACtD2V,cAAc,CAACV,YAAY,CAACY,IAAI,CAAC7V,OAAO,CAACwa,IAAI,CAAC;EAC9C,OAAO7E,cAAc;AACzB;AAEA,MAAM8E,aAAa,CAAC;AAEpBA,aAAa,CAACC,UAAU,GAAG,CACvB;EAAE7U,IAAI,EAAE/H,SAAS;EAAE6c,IAAI,EAAE,CAAC;IACd5D,QAAQ,EAAE;EACd,CAAC;AAAG,CAAC,CAChB;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6D,UAAU,CAAC;AAEjBA,UAAU,CAACF,UAAU,GAAG,CACpB;EAAE7U,IAAI,EAAE9H,QAAQ;EAAE4c,IAAI,EAAE,CAAC;IACb1F,YAAY,EAAE,CAACwF,aAAa;EAChC,CAAC;AAAG,CAAC,CAChB;AAED,MAAMI,2BAA2B,GAAGzW,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEwT,0BAA0B,CAAC,CAAC,CAAC,EAAE;EAAEkC,IAAI,EAAEC,aAAa;EAAE1D,QAAQ,EAAE;AAAG,CAAC,CAAC;AACzI;AACA;AACA;AACA,SAAS+D,8BAA8BA,CAAC5E,SAAS,EAAE;EAC/C,OAAOF,KAAK,CAAC6E,2BAA2B,EAAE3E,SAAS,CAAC;AACxD;AAEA,SAAS6E,iBAAiBA,CAAC9B,aAAa,EAAE;EACtC,MAAMjZ,OAAO,GAAGmC,MAAM,CAAC8W,aAAa,CAAC,GAC/B6B,8BAA8B,CAAC;IAAExO,SAAS,EAAE2M;EAAc,CAAC,CAAC,GAC5D6B,8BAA8B,CAAC7B,aAAa,CAAC;EACnD,MAAMtD,cAAc,GAAG4E,8BAA8B,CAACva,OAAO,CAAC;EAC9DkZ,UAAU,CAACxa,KAAK,CAAC,MAAM;IACnB6V,WAAW,CAACH,cAAc,CAAC;IAC3B5V,OAAO,CAAC2a,sBAAsB,CAACxD,cAAc,CAAC;IAC9CoC,eAAe,CAAC/X,OAAO,CAAC;IACxBuY,6CAA6C,CAACvY,OAAO,CAAC;EAC1D,CAAC,CAAC,CAAC;EACH,OAAO,CAAC+W,QAAQ,EAAEb,SAAS,KAAK;IAC5B,MAAMD,QAAQ,GAAG;MAAEoD,KAAK,EAAE,CAAC,CAAC;MAAE2B,SAAS,EAAE,CAAC,CAAC;MAAE3Q,aAAa,EAAE,IAAI;MAAE2K,SAAS,EAAE;IAAG,CAAC;IACjF,MAAM;MAAE3K,aAAa;MAAEgP,KAAK;MAAE2B,SAAS;MAAEhG;IAAU,CAAC,GAAG5Q,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEmR,QAAQ,CAAC,EAAEC,SAAS,CAAC;IAC5G,IAAIlB,SAAS,IAAIA,SAAS,CAACxS,MAAM,EAAE;MAC/BwS,SAAS,CAACzL,OAAO,CAAE+P,QAAQ,IAAK;QAC5B9a,OAAO,CAAC+a,gBAAgB,CAACD,QAAQ,CAAC1B,OAAO,EAAE0B,QAAQ,CAAC;MACxD,CAAC,CAAC;IACN;IACA9a,OAAO,CAACqa,cAAc,CAAC/Z,2BAA2B,EAAE;MAChD6X,GAAG,EAAE;QACDrB,eAAe,EAAEK,cAAc,CAACL;MACpC;IACJ,CAAC,CAAC,CAACsD,iBAAiB,CAAC5Y,OAAO,CAACwa,IAAI,EAAE;MAC/B7D,GAAG,EAAE;QAAEI,QAAQ,EAAEA,QAAQ,IAAI/W,OAAO,CAAC+W;MAAS;IAClD,CAAC,CAAC;IACF,MAAMyC,SAAS,GAAGyB,mBAAmB,CAACjb,OAAO,EAAEqZ,KAAK,EAAE2B,SAAS,CAAC;IAChE,IAAIhb,OAAO,CAACqK,aAAa,IAAIA,aAAa,EAAE;MACxCmP,SAAS,CAACnP,aAAa,CAAC,CAAC;IAC7B;IACA,OAAOmP,SAAS;EACpB,CAAC;AACL;AACA,SAASyB,mBAAmBA,CAACjb,OAAO,EAAEqZ,KAAK,EAAE2B,SAAS,EAAE;EACpD,MAAMhB,WAAW,GAAGxb,OAAO,CAACkb,eAAe,CAAC1Z,OAAO,CAACwa,IAAI,CAAC;EACzD,MAAMpQ,YAAY,GAAG4P,WAAW,CAAC5P,YAAY,CAACE,KAAK,CAAC3L,EAAE,CAAC4E,SAAS,CAACvD,OAAO,CAACsM,SAAS,CAAC,CAAC,IAAI0N,WAAW,CAAC5P,YAAY;EAChH,MAAMiQ,SAAS,GAAGL,WAAW,CAAC5P,YAAY,CAAC8Q,aAAa,CAACd,eAAe,CAACpa,OAAO,CAACsM,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/F,IAAI,CAAC+N,SAAS,EAAE;IACZ,MAAM,IAAItV,KAAK,CAAC,mCAAmC/E,OAAO,CAACsM,SAAS,sBAAsB,CAAC;EAC/F;EACA,MAAMuN,aAAa,GAAGlW,QAAQ,CAACqW,WAAW,CAACL,iBAAiB,EAAEqB,SAAS,CAAC;EACxE,MAAM1O,SAAS,GAAG3I,QAAQ,CAAC0W,SAAS,CAAC5W,QAAQ,CAACC,GAAG,CAAC1D,OAAO,CAACsM,SAAS,CAAC,EAAE+M,KAAK,CAAC;EAC5E,OAAO,IAAIO,aAAa,CAACC,aAAa,EAAEG,WAAW,CAAC5P,YAAY,EAAE4P,WAAW,CAAC7W,aAAa,EAAE6W,WAAW,EAAE5P,YAAY,EAAEkC,SAAS,EAAElC,YAAY,CAACjH,aAAa,CAAC;AAClK;;AAEA;AACA;AACA;AACA,MAAMgY,kBAAkB,SAASjR,YAAY,CAAC;EAC1CtK,WAAWA,CAACia,aAAa,EAAE1P,OAAO,EAAEC,YAAY,EAAExG,QAAQ,EAAE8E,OAAO,EAAE;IACjE,KAAK,CAACyB,OAAO,EAAEC,YAAY,EAAExG,QAAQ,EAAE8E,OAAO,CAAC;IAC/C,IAAI,CAACmR,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC1P,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACxG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC8E,OAAO,GAAGA,OAAO;EAC1B;EACA,IAAInF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACK,QAAQ;EACxB;EACAmG,MAAMA,CAACC,KAAK,EAAEoR,qBAAqB,GAAG,KAAK,EAAE;IACzC,IAAIA,qBAAqB,EAAE;MACvB,OAAO,IAAI,CAAChR,YAAY,CAAC3G,QAAQ,CAACC,GAAG,CAACsG,KAAK,CAAC;IAChD;IACA,OAAO,KAAK,CAACD,MAAM,CAACC,KAAK,CAAC;EAC9B;EACAmQ,YAAYA,CAACxP,KAAK,EAAE1I,KAAK,EAAE;IACvB0B,QAAQ,CAAC,IAAI,CAACkW,aAAa,EAAElP,KAAK,EAAE1I,KAAK,EAAE,KAAK,CAAC;IACjD,IAAI,CAACoI,aAAa,CAAC,CAAC;EACxB;AACJ;;AAEA;AACA;AACA;AACA,SAASgR,+BAA+BA,CAACrb,OAAO,EAAE;EAC9C,MAAM2V,cAAc,GAAGN,aAAa,CAACrV,OAAO,CAAC;EAC7C,IAAIA,OAAO,CAACsb,gBAAgB,EAAE;IAC1B3F,cAAc,CAACV,YAAY,CAACY,IAAI,CAAC7V,OAAO,CAACuD,SAAS,CAAC;EACvD;EACAoS,cAAc,CAACV,YAAY,CAACY,IAAI,CAAC7V,OAAO,CAACwa,IAAI,CAAC;EAC9C7E,cAAc,CAACG,OAAO,GAAG,CAAC9V,OAAO,CAAC+V,OAAO,GAAGlY,gBAAgB,GAAGmC,OAAO,CAAC8V,OAAO,IAAI,EAAE,CAAC;EACrF,OAAOH,cAAc;AACzB;AAEA,MAAM4F,8BAA8B,GAAGnX,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEkT,qBAAqB,CAAC,CAAC,CAAC,EAAE;EAAEwC,IAAI,EAAEC,aAAa;EAAE1D,QAAQ,EAAE,EAAE;EAAEhB,OAAO,EAAE,KAAK;EAAE1L,aAAa,EAAE,IAAI;EAAEmR,kBAAkB,EAAE,EAAE;EAAEC,cAAc,EAAE,EAAE;EAAEH,gBAAgB,EAAE;AAAK,CAAC,CAAC;AAChP;AACA;AACA;AACA,SAASI,mCAAmCA,CAACxF,SAAS,EAAE;EACpD,OAAOF,KAAK,CAACuF,8BAA8B,EAAErF,SAAS,CAAC;AAC3D;AAEA,SAASyF,sBAAsBA,CAAC1C,aAAa,EAAE;EAC3C,MAAMjZ,OAAO,GAAGmC,MAAM,CAAC8W,aAAa,CAAC,GAC/ByC,mCAAmC,CAAC;IAAEnY,SAAS,EAAE0V;EAAc,CAAC,CAAC,GACjEyC,mCAAmC,CAACzC,aAAa,CAAC;EACxD,MAAMtD,cAAc,GAAG0F,+BAA+B,CAACrb,OAAO,CAAC;EAC/DkZ,UAAU,CAACxa,KAAK,CAAC,MAAM;IACnB6V,WAAW,CAACH,cAAc,CAAC;IAC3B5V,OAAO,CAAC2a,sBAAsB,CAACxD,cAAc,CAAC;IAC9CoC,eAAe,CAAC/X,OAAO,CAAC;EAC5B,CAAC,CAAC,CAAC;EACH,OAAO,CAAC+W,QAAQ,EAAEb,SAAS,KAAK;IAC5B,MAAMD,QAAQ,GAAG;MACboD,KAAK,EAAE,CAAC,CAAC;MACT2B,SAAS,EAAE,CAAC,CAAC;MACb3Q,aAAa,EAAE,IAAI;MACnB2K,SAAS,EAAE;IACf,CAAC;IACD,MAAM;MAAE3K,aAAa;MAAEgP,KAAK;MAAE2B,SAAS;MAAEhG;IAAU,CAAC,GAAG5Q,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEmR,QAAQ,CAAC,EAAEC,SAAS,CAAC;IAC5G,IAAIlB,SAAS,IAAIA,SAAS,CAACxS,MAAM,EAAE;MAC/BwS,SAAS,CAACzL,OAAO,CAAE+P,QAAQ,IAAK;QAC5B9a,OAAO,CAAC+a,gBAAgB,CAACD,QAAQ,CAAC1B,OAAO,EAAE0B,QAAQ,CAAC;MACxD,CAAC,CAAC;IACN;IACA9a,OAAO,CAACqa,cAAc,CAAC/Z,2BAA2B,EAAE;MAChD6X,GAAG,EAAE;QACDrB,eAAe,EAAEK,cAAc,CAACL;MACpC;IACJ,CAAC,CAAC,CAACsD,iBAAiB,CAAC5Y,OAAO,CAACwa,IAAI,EAAE;MAC/B7D,GAAG,EAAE;QAAEI,QAAQ,EAAEA,QAAQ,IAAI/W,OAAO,CAAC+W;MAAS;IAClD,CAAC,CAAC;IACF,IAAI/W,OAAO,CAACwb,kBAAkB,CAAChZ,MAAM,IAAIxC,OAAO,CAACyb,cAAc,CAACjZ,MAAM,EAAE;MACpEhE,OAAO,CAACod,iBAAiB,CAAC5b,OAAO,CAACuD,SAAS,EAAE;QACzCoT,GAAG,EAAE;UAAE3B,SAAS,EAAE,CAAC,GAAGhV,OAAO,CAACwb,kBAAkB,EAAE,GAAGxb,OAAO,CAACyb,cAAc,CAACnc,GAAG,CAAC6E,CAAC,IAAInE,OAAO,CAACyV,YAAY,CAACtR,CAAC,CAAC,CAAC;QAAE;MACnH,CAAC,CAAC;IACN;IACA,MAAMqV,SAAS,GAAGqC,wBAAwB,CAAC7b,OAAO,EAAEqZ,KAAK,EAAE2B,SAAS,CAAC;IACrE,IAAIhb,OAAO,CAACqK,aAAa,IAAIA,aAAa,EAAE;MACxCmP,SAAS,CAACnP,aAAa,CAAC,CAAC;IAC7B;IACA,OAAOmP,SAAS;EACpB,CAAC;AACL;AACA,SAASqC,wBAAwBA,CAAC7b,OAAO,EAAEqZ,KAAK,EAAE2B,SAAS,EAAE;EACzD,MAAMhB,WAAW,GAAGxb,OAAO,CAACkb,eAAe,CAAC1Z,OAAO,CAACwa,IAAI,CAAC;EACzD,MAAMpQ,YAAY,GAAG4P,WAAW,CAAC5P,YAAY,CAACE,KAAK,CAAC3L,EAAE,CAAC4E,SAAS,CAACvD,OAAO,CAACuD,SAAS,CAAC,CAAC,IAAIyW,WAAW,CAAC5P,YAAY;EAChH,MAAMiQ,SAAS,GAAGL,WAAW,CAAC5P,YAAY,CAAC8Q,aAAa,CAACd,eAAe,CAACpa,OAAO,CAACuD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/F,IAAI,CAAC8W,SAAS,EAAE;IACZ,MAAM,IAAItV,KAAK,CAAC,yBAAyB/E,OAAO,CAACuD,SAAS,sBAAsB,CAAC;EACrF;EACA,MAAMsW,aAAa,GAAGlW,QAAQ,CAACqW,WAAW,CAACL,iBAAiB,EAAEqB,SAAS,CAAC;EACxE,MAAMzX,SAAS,GAAGI,QAAQ,CAAC0W,SAAS,CAAC5W,QAAQ,CAACC,GAAG,CAAC1D,OAAO,CAACuD,SAAS,CAAC,EAAE8V,KAAK,CAAC;EAC5E,OAAO,IAAI8B,kBAAkB,CAACtB,aAAa,EAAEG,WAAW,EAAEA,WAAW,CAAC5P,YAAY,EAAE7G,SAAS,EAAE6G,YAAY,CAACjH,aAAa,CAAC;AAC9H;;AAEA;AACA;AACA;AACA,MAAM2Y,gBAAgB,SAAShS,aAAa,CAAC;EACzClK,WAAWA,CAACmc,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;AACJ;;AAEA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAChc,OAAO,EAAE;EACnC,MAAM2V,cAAc,GAAGN,aAAa,CAACrV,OAAO,CAAC;EAC7C2V,cAAc,CAACX,SAAS,CAACa,IAAI,CAAC7V,OAAO,CAAC+b,OAAO,CAAC;EAC9C,OAAOpG,cAAc;AACzB;AAEA,MAAMsG,qBAAqB,GAAG7X,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEkT,qBAAqB,CAAC,CAAC,CAAC;AACxE;AACA;AACA;AACA,SAASkE,wBAAwBA,CAAChG,SAAS,EAAE;EACzC,OAAOF,KAAK,CAACiG,qBAAqB,EAAE/F,SAAS,CAAC;AAClD;;AAEA;AACA;AACA;AACA,SAASiG,oBAAoBA,CAAClD,aAAa,EAAE;EACzC,MAAM8C,OAAO,GAAG5Z,MAAM,CAAC8W,aAAa,CAAC,GAAGA,aAAa,GAAGA,aAAa,CAAC8C,OAAO;EAC7E,MAAM/b,OAAO,GAAGmC,MAAM,CAAC8W,aAAa,CAAC,GAAGiD,wBAAwB,CAAC;IAAEH;EAAQ,CAAC,CAAC,GAAGG,wBAAwB,CAACjD,aAAa,CAAC;EACvH,MAAMtD,cAAc,GAAGqG,oBAAoB,CAAChc,OAAO,CAAC;EACpDkZ,UAAU,CAAC,MAAM;IACb1a,OAAO,CAAC2a,sBAAsB,CAACxD,cAAc,CAAC;IAC9CoC,eAAe,CAAC/X,OAAO,CAAC;EAC5B,CAAC,CAAC;EACFoc,SAAS,CAAC,MAAM;IACZ,MAAMra,aAAa,GAAGvD,OAAO,CAACuL,MAAM,GAC9BvL,OAAO,CAACuL,MAAM,CAACgS,OAAO,CAAC,GACvBvd,OAAO,CAACkF,GAAG,CAACqY,OAAO,CAAC;IAC1B,IAAIja,8BAA8B,CAACC,aAAa,CAAC,EAAE;MAC/C;MACAA,aAAa,CAACsa,WAAW,CAAC,CAAC;IAC/B;EACJ,CAAC,CAAC;EACF,OAAQnG,SAAS,IAAK;IAClB,MAAMD,QAAQ,GAAG;MAAEjB,SAAS,EAAE;IAAG,CAAC;IAClC,MAAM;MAAEA;IAAU,CAAC,GAAG5Q,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEmR,QAAQ,CAAC,EAAEC,SAAS,CAAC;IAC3E,IAAIlB,SAAS,IAAIA,SAAS,CAACxS,MAAM,EAAE;MAC/BwS,SAAS,CAACzL,OAAO,CAAE+P,QAAQ,IAAK;QAC5B9a,OAAO,CAAC+a,gBAAgB,CAACD,QAAQ,CAAC1B,OAAO,EAAE0B,QAAQ,CAAC;MACxD,CAAC,CAAC;IACN;IACA,OAAO,IAAIwC,gBAAgB,CAACtd,OAAO,CAACuL,MAAM,GAAGvL,OAAO,CAACuL,MAAM,CAACgS,OAAO,CAAC,GAAGvd,OAAO,CAACkF,GAAG,CAACqY,OAAO,CAAC,CAAC;EAChG,CAAC;AACL;AAEA,MAAMO,UAAU,SAASrd,MAAM,CAAC;AAEhC,SAASsd,YAAYA,CAACC,MAAM,EAAE;EAC1B,OAAO,iBAAiB,IAAIA,MAAM;AACtC;;AAEA;AACA;AACA;AACA,MAAMC,gBAAgB,SAASpQ,SAAS,CAAC;EACrCzM,WAAWA,CAACuK,OAAO,EAAEC,YAAY,EAAExG,QAAQ,EAAE4Y,MAAM,EAAEE,kBAAkB,EAAE;IACrE,KAAK,CAACvS,OAAO,EAAEC,YAAY,EAAExG,QAAQ,EAAEwG,YAAY,CAACjH,aAAa,CAAC;IAClE,IAAI,CAACqZ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,kBAAkB,GAAGA,kBAAkB;EAChD;EACA;AACJ;AACA;EACIC,iBAAiBA,CAAC3c,OAAO,EAAE;IACvB,IAAI,CAAC,IAAI,CAAC4c,gBAAgB,CAAC,CAAC,EAAE;MAC1B;IACJ;IACA,IAAI5c,OAAO,IAAIA,OAAO,CAAC6c,MAAM,EAAE;MAC3B,IAAI,CAACH,kBAAkB,CAACI,SAAS,CAAC9c,OAAO,CAAC6c,MAAM,CAAC;IACrD;IACA,IAAI7c,OAAO,IAAIA,OAAO,CAAC+c,WAAW,EAAE;MAChC,IAAI,CAACL,kBAAkB,CAACM,cAAc,CAAChd,OAAO,CAAC+c,WAAW,CAAC;IAC/D;IACA,IAAI/c,OAAO,IAAIA,OAAO,CAACoR,IAAI,EAAE;MACzB,IAAI,CAACsL,kBAAkB,CAACO,UAAU,CAACjd,OAAO,CAACoR,IAAI,CAAC;IACpD;IACA,IAAIpR,OAAO,IAAIA,OAAO,CAACkd,QAAQ,EAAE;MAC7B,IAAI,CAACR,kBAAkB,CAACS,WAAW,CAACnd,OAAO,CAACkd,QAAQ,CAAC;IACzD;IACA,IAAI,CAACE,0BAA0B,CAAC,CAAC;EACrC;EACA;AACJ;AACA;EACIC,aAAaA,CAACrG,IAAI,EAAE/U,KAAK,EAAE;IACvB,IAAI,IAAI,CAAC2a,gBAAgB,CAAC,CAAC,EAAE;MACzB,IAAI,CAACF,kBAAkB,CAACY,QAAQ,CAACtG,IAAI,EAAE/U,KAAK,CAAC;MAC7C,IAAI,CAACmb,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACA;AACJ;AACA;EACIG,kBAAkBA,CAACvG,IAAI,EAAE/U,KAAK,EAAE;IAC5B,IAAI,IAAI,CAAC2a,gBAAgB,CAAC,CAAC,EAAE;MACzB,IAAI,CAACF,kBAAkB,CAACc,aAAa,CAACxG,IAAI,EAAE/U,KAAK,CAAC;MAClD,IAAI,CAACmb,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACA;AACJ;AACA;EACIK,YAAYA,CAACzG,IAAI,EAAE/U,KAAK,EAAE;IACtB,IAAI,IAAI,CAAC2a,gBAAgB,CAAC,CAAC,EAAE;MACzB,IAAI,CAACF,kBAAkB,CAACgB,OAAO,CAAC1G,IAAI,EAAE/U,KAAK,CAAC;MAC5C,IAAI,CAACmb,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACA;AACJ;AACA;EACIO,gBAAgBA,CAACT,QAAQ,EAAE;IACvB,IAAI,IAAI,CAACN,gBAAgB,CAAC,CAAC,EAAE;MACzB,IAAI,CAACF,kBAAkB,CAACS,WAAW,CAACD,QAAQ,CAAC;MAC7C,IAAI,CAACE,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACA;AACJ;AACA;EACIQ,WAAWA,CAACC,GAAG,EAAE;IACb,IAAI,IAAI,CAACjB,gBAAgB,CAAC,CAAC,EAAE;MACzB,IAAI,CAACF,kBAAkB,CAACoB,MAAM,CAACD,GAAG,CAAC;MACnC,IAAI,CAACT,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACA;AACJ;AACA;EACIW,eAAeA,CAAC9X,KAAK,EAAE;IACnB,IAAI,CAACsW,YAAY,CAAC,IAAI,CAACC,MAAM,CAAC,EAAE;MAC5B;MACA/Q,OAAO,CAACuS,IAAI,CAAC,kGAAkG,GAC3G,qDAAqD,CAAC;MAC1D;IACJ;IACA,IAAI,CAACxB,MAAM,CAACuB,eAAe,CAAC9X,KAAK,CAAC;EACtC;EACAmX,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAACV,kBAAkB,CAACC,iBAAiB,CAAC,CAAC;IAC3C,IAAI,CAACtS,aAAa,CAAC,CAAC;EACxB;EACAuS,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACF,kBAAkB,EAAE;MAC1B;MACAjR,OAAO,CAACuS,IAAI,CAAC,0GAA0G,GACnH,8CAA8C,CAAC;MACnD,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAAS/e,cAAc,CAAC;EAC5CU,WAAWA,CAACI,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACke,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAG,IAAIrgB,aAAa,CAAC,CAAC,CAAC;IACzC,IAAI,CAACsgB,kBAAkB,GAAG,IAAItgB,aAAa,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACugB,WAAW,GAAG,IAAIvgB,aAAa,CAAC,CAAC,CAAC;IACvC,IAAI,CAACwgB,eAAe,GAAG,IAAIxgB,aAAa,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACygB,UAAU,GAAG,IAAIzgB,aAAa,CAAC,CAAC,CAAC;IACtC,IAAI0B,OAAO,EAAE;MACT,IAAI,CAACke,UAAU,GAAGle,OAAO,CAAC6c,MAAM,IAAI,CAAC,CAAC;MACtC,IAAI,CAACsB,eAAe,GAAGne,OAAO,CAAC+c,WAAW,IAAI,CAAC,CAAC;MAChD,IAAI,CAACqB,QAAQ,GAAGpe,OAAO,CAACoR,IAAI,IAAI,CAAC,CAAC;MAClC,IAAI,CAACiN,YAAY,GAAGre,OAAO,CAACkd,QAAQ,IAAI,IAAI;MAC5C,IAAI,CAACoB,OAAO,GAAGte,OAAO,CAAC6d,GAAG,IAAI,EAAE;MAChC,IAAI,CAACU,QAAQ,GAAGve,OAAO,CAACgD,IAAI,IAAI,IAAI;MACpC,IAAI,CAACwb,UAAU,GAAGxe,OAAO,CAACgf,MAAM,IAAI,IAAI;MACxC,IAAI,CAACP,cAAc,GAAGze,OAAO,CAACif,UAAU,IAAI,IAAI;MAChD,IAAI,CAACP,YAAY,GAAG1e,OAAO,CAACkf,QAAQ,IAAI,IAAI;IAChD;IACA,IAAI,CAACrC,MAAM,GAAG,IAAI,CAAC8B,aAAa,CAACQ,YAAY,CAAC,CAAC;IAC/C,IAAI,CAACpC,WAAW,GAAG,IAAI,CAAC6B,kBAAkB,CAACO,YAAY,CAAC,CAAC;IACzD,IAAI,CAAC/N,IAAI,GAAG,IAAI,CAACyN,WAAW,CAACM,YAAY,CAAC,CAAC;IAC3C,IAAI,CAACjC,QAAQ,GAAG,IAAI,CAAC4B,eAAe,CAACK,YAAY,CAAC,CAAC;IACnD,IAAI,CAACtB,GAAG,GAAG,IAAI,CAACkB,UAAU,CAACI,YAAY,CAAC,CAAC;IACzC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACpC,IAAI,CAAC1C,iBAAiB,CAAC,CAAC;EAC5B;EACA,IAAI2C,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACX,aAAa,CAACQ,YAAY,CAAC,CAAC,CAACI,IAAI,CAACjgB,GAAG,CAACud,MAAM,IAAI1d,iBAAiB,CAAC0d,MAAM,CAAC,CAAC,CAAC;EAC3F;EACAC,SAASA,CAACD,MAAM,EAAE;IACd,IAAI,CAACqB,UAAU,GAAGrB,MAAM;IACxB,IAAI,CAACuC,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;EACxC;EACA/B,QAAQA,CAACtG,IAAI,EAAE/U,KAAK,EAAE;IAClB,IAAI,CAACic,UAAU,GAAG9Z,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACoZ,UAAU,CAAC,EAAE;MAAE,CAAClH,IAAI,GAAG/U;IAAM,CAAC,CAAC;IACtF,IAAI,CAACmd,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;EACxC;EACArC,cAAcA,CAACD,WAAW,EAAE;IACxB,IAAI,CAACoB,eAAe,GAAGpB,WAAW;IAClC,IAAI,CAACqC,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;EACxC;EACA7B,aAAaA,CAACxG,IAAI,EAAE/U,KAAK,EAAE;IACvB,IAAI,CAACkc,eAAe,GAAG/Z,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACqZ,eAAe,CAAC,EAAE;MAAE,CAACnH,IAAI,GAAG/U;IAAM,CAAC,CAAC;IAChG,IAAI,CAACmd,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;EACxC;EACApC,UAAUA,CAAC7L,IAAI,EAAE;IACb,IAAI,CAACgN,QAAQ,GAAGhN,IAAI;IACpB,IAAI,CAACgO,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;EACxC;EACA3B,OAAOA,CAAC1G,IAAI,EAAE/U,KAAK,EAAE;IACjB,IAAI,CAACmc,QAAQ,GAAGha,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsZ,QAAQ,CAAC,EAAE;MAAE,CAACpH,IAAI,GAAG/U;IAAM,CAAC,CAAC;IAClF,IAAI,CAACmd,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;EACxC;EACAlC,WAAWA,CAACD,QAAQ,EAAE;IAClB,IAAI,CAACmB,YAAY,GAAGnB,QAAQ;IAC5B,IAAI,CAACkC,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;EACxC;EACAvB,MAAMA,CAACD,GAAG,EAAE;IACR,IAAI,CAACS,OAAO,GAAGT,GAAG;IAClB,IAAI,CAACuB,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;EACxC;EACA,IAAIrc,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACub,QAAQ,IAAI,IAAI;EAChC;EACA,IAAIS,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACR,UAAU,IAAI,IAAI;EAClC;EACA,IAAIU,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACR,YAAY,IAAI,CAAC,IAAI,CAAC;EACtC;EACA,IAAIO,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACR,cAAc,IAAI,IAAI;EACtC;EACA;AACJ;AACA;EACI9B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACgC,aAAa,CAACa,IAAI,CAAC,IAAI,CAACtB,UAAU,CAAC;IACxC,IAAI,CAACU,kBAAkB,CAACY,IAAI,CAAC,IAAI,CAACrB,eAAe,CAAC;IAClD,IAAI,CAACU,WAAW,CAACW,IAAI,CAAC,IAAI,CAACpB,QAAQ,CAAC;IACpC,IAAI,CAACU,eAAe,CAACU,IAAI,CAAC,IAAI,CAACnB,YAAY,CAAC;IAC5C,IAAI,CAACU,UAAU,CAACS,IAAI,CAAC,IAAI,CAAClB,OAAO,CAAC;EACtC;EACAmB,QAAQA,CAAA,EAAG;IACP,OAAO,oBAAoB;EAC/B;EACAJ,aAAaA,CAAA,EAAG;IACZ,MAAMD,QAAQ,GAAG,IAAIhgB,sBAAsB,CAAC,CAAC;IAC7CggB,QAAQ,CAACvC,MAAM,GAAG,IAAI,CAACqB,UAAU;IACjCkB,QAAQ,CAACrC,WAAW,GAAG,IAAI,CAACoB,eAAe;IAC3CiB,QAAQ,CAAChO,IAAI,GAAG,IAAI,CAACgN,QAAQ;IAC7BgB,QAAQ,CAAClC,QAAQ,GAAG,IAAI,CAACmB,YAAY;IACrCe,QAAQ,CAACvB,GAAG,GAAG,IAAI,CAACS,OAAO;IAC3B,OAAOc,QAAQ;EACnB;AACJ;;AAEA;AACA,MAAMM,uBAAuB,CAAC;EAC1B9f,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+f,WAAW,GAAG,IAAI;EAC3B;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACD,WAAW,GAAG,IAAI,CAACE,UAAU;IAClC,OAAO,IAAI;EACf;AACJ;AACAH,uBAAuB,CAAChF,UAAU,GAAG,CACjC;EAAE7U,IAAI,EAAE7H,SAAS;EAAE2c,IAAI,EAAE,CAAC;IACd3P,QAAQ,EAAE,cAAc;IACxBgK,SAAS,EAAE,CACP;MACI4C,OAAO,EAAEvY,UAAU;MACnBygB,WAAW,EAAEJ;IACjB,CAAC;EAET,CAAC;AAAG,CAAC,CAChB;AACDA,uBAAuB,CAACK,cAAc,GAAG;EACrCF,UAAU,EAAE,CAAC;IAAEha,IAAI,EAAE5H;EAAM,CAAC,CAAC;EAC7B2hB,OAAO,EAAE,CAAC;IAAE/Z,IAAI,EAAE3H,YAAY;IAAEyc,IAAI,EAAE,CAAC,OAAO;EAAG,CAAC;AACtD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqF,6BAA6B,CAAC;AAEpCA,6BAA6B,CAACtF,UAAU,GAAG,CACvC;EAAE7U,IAAI,EAAE9H,QAAQ;EAAE4c,IAAI,EAAE,CAAC;IACb1F,YAAY,EAAE,CAACyK,uBAAuB;EAC1C,CAAC;AAAG,CAAC,CAChB;;AAED;AACA;AACA;AACA,SAASO,oBAAoBA,CAACjgB,OAAO,EAAE;EACnC,MAAM2V,cAAc,GAAGD,sBAAsB,CAAC1V,OAAO,CAAC;EACtD,IAAIA,OAAO,CAACkgB,eAAe,IAAIlgB,OAAO,CAACmgB,YAAY,EAAE;IACjDxK,cAAc,CAACV,YAAY,CAACY,IAAI,CAAC6J,uBAAuB,CAAC;EAC7D;EACA,IAAI1f,OAAO,CAACmgB,YAAY,EAAE;IACtBxK,cAAc,CAACX,SAAS,CAACa,IAAI,CAAC7V,OAAO,CAACyV,YAAY,CAAC6G,UAAU,EAAE;MAC3D8D,MAAM,EAAE,IAAI7hB,OAAO,CAAC,CAAC;MACrBwf,eAAeA,CAAC9X,KAAK,EAAE;QACnB,IAAI,CAACma,MAAM,CAACZ,IAAI,CAACvZ,KAAK,CAAC;MAC3B;IACJ,CAAC,CAAC,EAAE;MACA2R,OAAO,EAAE3Y,MAAM;MACf6gB,WAAW,EAAExD;IACjB,CAAC,CAAC;IACF3G,cAAc,CAACX,SAAS,CAACa,IAAI,CAAC;MAC1B+B,OAAO,EAAEqG,kBAAkB;MAC3BoC,QAAQ,EAAE,IAAIpC,kBAAkB,CAAC;QAC7BpB,MAAM,EAAE7c,OAAO,CAAC6c,MAAM;QACtBE,WAAW,EAAE/c,OAAO,CAAC+c,WAAW;QAChC3L,IAAI,EAAEpR,OAAO,CAACoR;MAClB,CAAC;IACL,CAAC,EAAE;MACCwG,OAAO,EAAE1Y,cAAc;MACvB4gB,WAAW,EAAE7B;IACjB,CAAC,CAAC;EACN,CAAC,MACI;IACDtI,cAAc,CAACT,OAAO,CAACW,IAAI,CAACtW,mBAAmB,CAAC+gB,UAAU,CAACtgB,OAAO,CAACugB,MAAM,CAAC,CAAC;EAC/E;EACA,OAAO5K,cAAc;AACzB;AAEA,MAAM6K,IAAI,GAAG,CAAC,CAAC;AACf,MAAMC,qBAAqB,GAAGrc,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEwT,0BAA0B,CAAC,CAAC,CAAC,EAAE;EAAEuE,MAAM,EAAE,CAAC,CAAC;EAAEE,WAAW,EAAE,CAAC,CAAC;EAAE3L,IAAI,EAAEoP,IAAI;EAAEtD,QAAQ,EAAE,IAAI;EAAEgD,eAAe,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEI,MAAM,EAAE,EAAE;EAAE1C,GAAG,EAAE,EAAE;EAAE7a,IAAI,EAAE,IAAI;EAAEgc,MAAM,EAAE,IAAI;EAAEE,QAAQ,EAAE,IAAI;EAAED,UAAU,EAAE;AAAK,CAAC,CAAC;AACrR;AACA;AACA;AACA,SAASyB,wBAAwBA,CAACxK,SAAS,EAAE;EACzC,OAAOF,KAAK,CAACyK,qBAAqB,EAAEvK,SAAS,CAAC;AAClD;;AAEA;AACA;AACA;AACA,SAASyK,oBAAoBA,CAAC1H,aAAa,EAAE;EACzC,MAAMjZ,OAAO,GAAGmC,MAAM,CAAC8W,aAAa,CAAC,GAC/ByH,wBAAwB,CAAC;IAAEpU,SAAS,EAAE2M;EAAc,CAAC,CAAC,GACtDyH,wBAAwB,CAACzH,aAAa,CAAC;EAC7C,MAAMtD,cAAc,GAAGsK,oBAAoB,CAACjgB,OAAO,CAAC;EACpDkZ,UAAU,CAACxa,KAAK,CAAC,MAAM;IACnB6V,WAAW,CAACH,cAAc,CAAC;IAC3B5V,OAAO,CAAC2a,sBAAsB,CAACxD,cAAc,CAAC;IAC9CoC,eAAe,CAAC/X,OAAO,CAAC;IACxBuY,6CAA6C,CAACvY,OAAO,CAAC;IACtDxB,OAAO,CAAC4a,iBAAiB,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC;EACH,OAAQlD,SAAS,IAAK;IAClB,MAAMD,QAAQ,GAAG;MACboD,KAAK,EAAE,CAAC,CAAC;MACThP,aAAa,EAAE,IAAI;MACnB2K,SAAS,EAAE;IACf,CAAC;IACD,MAAM;MAAE3K,aAAa;MAAEgP,KAAK;MAAErE;IAAU,CAAC,GAAG5Q,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEmR,QAAQ,CAAC,EAAEC,SAAS,CAAC;IACjG,IAAIlB,SAAS,IAAIA,SAAS,CAACxS,MAAM,EAAE;MAC/BwS,SAAS,CAACzL,OAAO,CAAE+P,QAAQ,IAAK;QAC5B9a,OAAO,CAAC+a,gBAAgB,CAACD,QAAQ,CAAC1B,OAAO,EAAE0B,QAAQ,CAAC;MACxD,CAAC,CAAC;IACN;IACA,MAAM;MAAEuD,MAAM;MAAEE,WAAW;MAAE3L,IAAI;MAAE8L,QAAQ;MAAEW,GAAG;MAAE7a,IAAI;MAAEgc,MAAM;MAAEE,QAAQ;MAAED;IAAW,CAAC,GAAG7a,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAE9E,OAAO,CAAC,EAAEkW,SAAS,CAAC;IAC7I1X,OAAO,CAAC+a,gBAAgB,CAACra,cAAc,EAAE;MACrCmhB,QAAQ,EAAE,IAAIpC,kBAAkB,CAAC;QAAEpB,MAAM;QAAEE,WAAW;QAAE3L,IAAI;QAAE8L,QAAQ;QAAEW,GAAG;QAAE7a,IAAI;QAAEgc,MAAM;QAAEE,QAAQ;QAAED;MAAW,CAAC;IACrH,CAAC,CAAC;IACF,MAAM2B,MAAM,GAAGpiB,OAAO,CAACuL,MAAM,GAAGvL,OAAO,CAACuL,MAAM,CAAC5L,MAAM,CAAC,GAAGK,OAAO,CAACkF,GAAG,CAACvF,MAAM,CAAC;IAC5E,OAAOyiB,MAAM,CAACC,GAAG,CAAC,MAAM;MACpB,MAAMrH,SAAS,GAAGsH,sBAAsB,CAAC9gB,OAAO,EAAEqZ,KAAK,CAAC;MACxDG,SAAS,CAACgD,MAAM,CAACuE,iBAAiB,CAAC,CAAC;MACpC,IAAI/gB,OAAO,CAACqK,aAAa,IAAIA,aAAa,EAAE;QACxCmP,SAAS,CAACnP,aAAa,CAAC,CAAC;MAC7B;MACA,OAAOmP,SAAS;IACpB,CAAC,CAAC;EACN,CAAC;AACL;AACA,SAASsH,sBAAsBA,CAAC9gB,OAAO,EAAEqZ,KAAK,EAAE;EAC5C,MAAMlP,OAAO,GAAG3L,OAAO,CAACkb,eAAe,CAAC1Z,OAAO,CAACsM,SAAS,CAAC;EAC1D,MAAMlC,YAAY,GAAGD,OAAO,CAACC,YAAY;EACzC,MAAMkC,SAAS,GAAG3I,QAAQ,CAACwG,OAAO,CAACwP,iBAAiB,EAAEN,KAAK,CAAC;EAC5D;AACJ;AACA;EACI,IAAI,CAAC7a,OAAO,CAACuL,MAAM,EAAE;IACjB,OAAO,IAAI0S,gBAAgB,CAACtS,OAAO,EAAEC,YAAY,EAAEkC,SAAS,EAAE9N,OAAO,CAACkF,GAAG,CAACzE,MAAM,CAAC,EAAET,OAAO,CAACkF,GAAG,CAACxE,cAAc,CAAC,CAAC;EACnH;EACA,OAAO,IAAIud,gBAAgB,CAACtS,OAAO,EAAEC,YAAY,EAAEkC,SAAS,EAAE9N,OAAO,CAACuL,MAAM,CAAC9K,MAAM,CAAC,EAAET,OAAO,CAACuL,MAAM,CAAC7K,cAAc,CAAC,CAAC;AACzH;;AAEA;AACA;AACA;AACA,IAAI8hB,UAAU;AACd,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK;EACzBA,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;EAC3BA,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC/BA,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK;EACzBA,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO;EAC7BA,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;EAC3BA,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO;EAC7BA,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS;AACrC,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA,MAAMC,aAAa,SAASnX,aAAa,CAAC;EACtClK,WAAWA,CAACmc,OAAO,EAAEmF,UAAU,EAAEC,UAAU,EAAE;IACzC,KAAK,CAAC,CAAC;IACP,IAAI,CAACpF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B;IACA;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACD,IAAI,CAAC,IAAI,CAAC;EAC5D;EACAD,SAASA,CAACvD,GAAG,EAAE0D,MAAM,EAAE;IACnB1M,MAAM,CAAC,IAAI,CAAC,CAAC2M,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,MAAMC,GAAG,GAAG,IAAI,CAACN,UAAU,CAACC,SAAS,CAAC;MAClCvD,GAAG;MACH0D;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACJ,UAAU,CAACO,MAAM,CAAC,CAAC;IACxB,OAAOD,GAAG;EACd;EACAH,gBAAgBA,CAACK,YAAY,EAAE;IAC3B,MAAMC,QAAQ,GAAGD,YAAY,CAACriB,GAAG,CAAEuiB,WAAW,IAAK;MAC/C,OAAO,IAAI,CAACV,UAAU,CAACC,SAAS,CAAC;QAC7BvD,GAAG,EAAEgE,WAAW,CAAChE,GAAG;QACpB0D,MAAM,EAAEM,WAAW,CAACN;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACJ,UAAU,CAACO,MAAM,CAAC,CAAC;IACxB,OAAOE,QAAQ;EACnB;EACAE,QAAQA,CAACF,QAAQ,EAAEjH,IAAI,EAAE;IACrBiH,QAAQ,CAACrY,OAAO,CAAC,CAACwY,OAAO,EAAEC,GAAG,KAAK;MAC/BD,OAAO,CAACE,KAAK,CAACtH,IAAI,CAACqH,GAAG,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACliB,OAAO,EAAE;EAChC,MAAM2V,cAAc,GAAGN,aAAa,CAACrV,OAAO,CAAC;EAC7C2V,cAAc,CAACX,SAAS,CAACa,IAAI,CAAC7V,OAAO,CAAC+b,OAAO,CAAC;EAC9CpG,cAAc,CAACT,OAAO,CAACW,IAAI,CAACpW,uBAAuB,CAAC;EACpD,OAAOkW,cAAc;AACzB;AAEA,MAAMwM,kBAAkB,GAAG/d,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEkT,qBAAqB,CAAC,CAAC,CAAC;AACrE;AACA;AACA;AACA,SAASoK,qBAAqBA,CAAClM,SAAS,EAAE;EACtC,OAAOF,KAAK,CAACmM,kBAAkB,EAAEjM,SAAS,CAAC;AAC/C;;AAEA;AACA;AACA;AACA,SAASmM,iBAAiBA,CAACpJ,aAAa,EAAE;EACtC,MAAM8C,OAAO,GAAG5Z,MAAM,CAAC8W,aAAa,CAAC,GAAGA,aAAa,GAAGA,aAAa,CAAC8C,OAAO;EAC7E,MAAM/b,OAAO,GAAGmC,MAAM,CAAC8W,aAAa,CAAC,GAAGmJ,qBAAqB,CAAC;IAAErG;EAAQ,CAAC,CAAC,GAAGqG,qBAAqB,CAACnJ,aAAa,CAAC;EACjH,MAAMtD,cAAc,GAAGuM,iBAAiB,CAACliB,OAAO,CAAC;EACjDkZ,UAAU,CAAC,MAAM;IACb1a,OAAO,CAAC2a,sBAAsB,CAACxD,cAAc,CAAC;IAC9CoC,eAAe,CAAC/X,OAAO,CAAC;EAC5B,CAAC,CAAC;EACFoc,SAAS,CAAC,MAAM;IACZ,IAAI5d,OAAO,CAACuL,MAAM,EAAE;MAChBvL,OAAO,CAACuL,MAAM,CAACrK,qBAAqB,CAAC,CAACgiB,MAAM,CAAC,CAAC;IAClD,CAAC,MACI;MACDljB,OAAO,CAACkF,GAAG,CAAChE,qBAAqB,CAAC,CAACgiB,MAAM,CAAC,CAAC;IAC/C;EACJ,CAAC,CAAC;EACF,OAAQxL,SAAS,IAAK;IAClB,MAAMD,QAAQ,GAAG;MAAEjB,SAAS,EAAE;IAAG,CAAC;IAClC,MAAM;MAAEA;IAAU,CAAC,GAAG5Q,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEmR,QAAQ,CAAC,EAAEC,SAAS,CAAC;IAC3E,IAAIlB,SAAS,IAAIA,SAAS,CAACxS,MAAM,EAAE;MAC/BwS,SAAS,CAACzL,OAAO,CAAE+P,QAAQ,IAAK;QAC5B9a,OAAO,CAAC+a,gBAAgB,CAACD,QAAQ,CAAC1B,OAAO,EAAE0B,QAAQ,CAAC;MACxD,CAAC,CAAC;IACN;IACA;AACR;AACA;IACQ,IAAI,CAAC9a,OAAO,CAACuL,MAAM,EAAE;MACjB,OAAO,IAAIkX,aAAa,CAACziB,OAAO,CAACkF,GAAG,CAACqY,OAAO,CAAC,EAAEvd,OAAO,CAACkF,GAAG,CAAClE,UAAU,CAAC,EAAEhB,OAAO,CAACkF,GAAG,CAAChE,qBAAqB,CAAC,CAAC;IAC/G;IACA,OAAO,IAAIuhB,aAAa,CAACziB,OAAO,CAACuL,MAAM,CAACgS,OAAO,CAAC,EAAEvd,OAAO,CAACuL,MAAM,CAACvK,UAAU,CAAC,EAAEhB,OAAO,CAACuL,MAAM,CAACrK,qBAAqB,CAAC,CAAC;EACxH,CAAC;AACL;;AAEA;AACA;AACA;AACA,MAAM4iB,aAAa,SAASxY,aAAa,CAAC;EACtClK,WAAWA,CAACia,aAAa,EAAE1P,OAAO,EAAEC,YAAY,EAAE1B,OAAO,EAAE;IACvD,KAAK,CAAC,CAAC;IACP,IAAI,CAACmR,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC1P,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC1B,OAAO,GAAGA,OAAO;EAC1B;EACA2B,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACF,OAAO,CAACE,aAAa,CAAC,CAAC;EAChC;EACA8P,YAAYA,CAACxP,KAAK,EAAE1I,KAAK,EAAE;IACvB0B,QAAQ,CAAC,IAAI,CAACkW,aAAa,EAAElP,KAAK,EAAE1I,KAAK,EAAE,KAAK,CAAC;IACjD,IAAI,CAACoI,aAAa,CAAC,CAAC;EACxB;AACJ;;AAEA;AACA;AACA;AACA,SAASkY,0BAA0BA,CAACviB,OAAO,EAAE;EACzC,MAAM2V,cAAc,GAAGN,aAAa,CAACrV,OAAO,CAAC;EAC7C2V,cAAc,CAACV,YAAY,CAACY,IAAI,CAAC7V,OAAO,CAACuf,IAAI,CAAC;EAC9C5J,cAAc,CAACV,YAAY,CAACY,IAAI,CAAC7V,OAAO,CAACwa,IAAI,CAAC;EAC9C,OAAO7E,cAAc;AACzB;AAEA,MAAM6M,2BAA2B,GAAGpe,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEkT,qBAAqB,CAAC,CAAC,CAAC,EAAE;EAAEwC,IAAI,EAAEC,aAAa;EAAEpQ,aAAa,EAAE,IAAI;EAAE0M,QAAQ,EAAE;AAAG,CAAC,CAAC;AACzJ;AACA;AACA;AACA,SAAS0L,8BAA8BA,CAACvM,SAAS,EAAE;EAC/C,OAAOF,KAAK,CAACwM,2BAA2B,EAAEtM,SAAS,CAAC;AACxD;;AAEA;AACA;AACA;AACA,SAASwM,iBAAiBA,CAACzJ,aAAa,EAAE;EACtC,MAAMjZ,OAAO,GAAGmC,MAAM,CAAC8W,aAAa,CAAC,GAC/BwJ,8BAA8B,CAAC;IAAElD,IAAI,EAAEtG;EAAc,CAAC,CAAC,GACvDwJ,8BAA8B,CAACxJ,aAAa,CAAC;EACnD,MAAMtD,cAAc,GAAG4M,0BAA0B,CAACviB,OAAO,CAAC;EAC1DkZ,UAAU,CAACxa,KAAK,CAAC,MAAM;IACnB6V,WAAW,CAACH,cAAc,CAAC;IAC3B5V,OAAO,CAAC2a,sBAAsB,CAACxD,cAAc,CAAC;IAC9CoC,eAAe,CAAC/X,OAAO,CAAC;EAC5B,CAAC,CAAC,CAAC;EACH,OAAO,CAAC2iB,mBAAmB,EAAEzM,SAAS,KAAK;IACvC,MAAMD,QAAQ,GAAG;MACb+E,SAAS,EAAE,CAAC,CAAC;MACb3Q,aAAa,EAAE,IAAI;MACnB2K,SAAS,EAAE;IACf,CAAC;IACD,MAAM4N,iBAAiB,GAAG,OAAOD,mBAAmB,KAAK,QAAQ,GAAGA,mBAAmB,GAAGzM,SAAS;IACnG,MAAM;MAAE7L,aAAa;MAAE2Q,SAAS;MAAEhG;IAAU,CAAC,GAAG5Q,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEmR,QAAQ,CAAC,EAAE2M,iBAAiB,CAAC;IAC7G,MAAM7L,QAAQ,GAAG,OAAO4L,mBAAmB,KAAK,QAAQ,GAAGA,mBAAmB,GAAG3iB,OAAO,CAAC+W,QAAQ;IACjG,IAAI/B,SAAS,IAAIA,SAAS,CAACxS,MAAM,EAAE;MAC/BwS,SAAS,CAACzL,OAAO,CAAE+P,QAAQ,IAAK;QAC5B9a,OAAO,CAAC+a,gBAAgB,CAACD,QAAQ,CAAC1B,OAAO,EAAE0B,QAAQ,CAAC;MACxD,CAAC,CAAC;IACN;IACA,IAAIvC,QAAQ,EAAE;MACVvY,OAAO,CAACqa,cAAc,CAAC/Z,2BAA2B,EAAE;QAChD6X,GAAG,EAAE;UACDrB,eAAe,EAAEK,cAAc,CAACL;QACpC;MACJ,CAAC,CAAC,CAACsD,iBAAiB,CAAC5Y,OAAO,CAACwa,IAAI,EAAE;QAC/B7D,GAAG,EAAE;UAAEI;QAAS;MACpB,CAAC,CAAC;IACN;IACA,MAAMyC,SAAS,GAAGqJ,mBAAmB,CAAC7iB,OAAO,EAAEgb,SAAS,CAAC;IACzD,IAAIhb,OAAO,CAACqK,aAAa,IAAIA,aAAa,EAAE;MACxCmP,SAAS,CAACnP,aAAa,CAAC,CAAC;IAC7B;IACA,OAAOmP,SAAS;EACpB,CAAC;AACL;AACA,SAASqJ,mBAAmBA,CAAC7iB,OAAO,EAAEgb,SAAS,EAAE;EAC7C,MAAMhB,WAAW,GAAGxb,OAAO,CAACkb,eAAe,CAAC1Z,OAAO,CAACwa,IAAI,CAAC;EACzD,MAAMpQ,YAAY,GAAG4P,WAAW,CAAC5P,YAAY;EAC7C,MAAMyP,aAAa,GAAGlW,QAAQ,CAACqW,WAAW,CAACL,iBAAiB,EAAEqB,SAAS,CAAC;EACxE,OAAO,IAAIsH,aAAa,CAACzI,aAAa,EAAEG,WAAW,EAAEA,WAAW,CAAC5P,YAAY,EAAEA,YAAY,CAACjH,aAAa,CAAC;AAC9G;AAEA,SAAS2f,uBAAuBA,CAAC9iB,OAAO,EAAE;EACtC,MAAM+iB,QAAQ,GAAG;IACb/X,QAAQ,EAAEhL,OAAO,CAACgL,QAAQ;IAC1B+L,QAAQ,EAAE/W,OAAO,CAAC+W,QAAQ,IAAI,EAAE;IAChCiM,MAAM,EAAEhjB,OAAO,CAACgjB,MAAM;IACtBC,OAAO,EAAEjjB,OAAO,CAACijB,OAAO,IAAI,EAAE;IAC9BC,QAAQ,EAAEljB,OAAO,CAACkjB,QAAQ,IAAI;EAClC,CAAC;EACD,MAAMC,IAAI,CAAC;EAEXJ,QAAQ,CAACE,OAAO,CAAC1Z,OAAO,CAACgY,MAAM,IAAI;IAC/B4B,IAAI,CAAC5M,SAAS,CAACgL,MAAM,CAAC,GAAG,IAAInjB,YAAY,CAAC,CAAC;EAC/C,CAAC,CAAC;EACF,OAAON,SAAS,CAACilB,QAAQ,CAAC,CAACI,IAAI,CAAC;AACpC;AACA,SAASC,uBAAuBA,CAACpjB,OAAO,EAAE;EACtC,MAAM+iB,QAAQ,GAAG;IACb/X,QAAQ,EAAEhL,OAAO,CAACgL,QAAQ;IAC1BgY,MAAM,EAAEhjB,OAAO,CAACgjB,MAAM;IACtBC,OAAO,EAAEjjB,OAAO,CAACijB,OAAO,IAAI,EAAE;IAC9BC,QAAQ,EAAEljB,OAAO,CAACkjB,QAAQ,IAAI;EAClC,CAAC;EACD,MAAMC,IAAI,CAAC;EAEXJ,QAAQ,CAACE,OAAO,CAAC1Z,OAAO,CAACgY,MAAM,IAAI;IAC/B4B,IAAI,CAAC5M,SAAS,CAACgL,MAAM,CAAC,GAAG,IAAInjB,YAAY,CAAC,CAAC;EAC/C,CAAC,CAAC;EACF,OAAOJ,SAAS,CAAC+kB,QAAQ,CAAC,CAACI,IAAI,CAAC;AACpC;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASlF,kBAAkB,EAAEte,WAAW,EAAE8a,aAAa,EAAEG,UAAU,EAAEoG,UAAU,EAAE8B,uBAAuB,EAAEM,uBAAuB,EAAE/W,SAAS,EAAE8O,kBAAkB,EAAEvB,aAAa,EAAEqH,aAAa,EAAEqB,aAAa,EAAE7F,gBAAgB,EAAEX,gBAAgB,EAAE1a,SAAS,EAAEtB,OAAO,EAAEK,aAAa,EAAEyB,MAAM,EAAEJ,QAAQ,EAAEnB,MAAM,EAAEE,aAAa,EAAEe,OAAO,EAAEI,OAAO,EAAEsX,sBAAsB,EAAE2C,sBAAsB,EAAE3T,eAAe,EAAE+S,iBAAiB,EAAEsH,iBAAiB,EAAEpb,mBAAmB,EAAErB,gBAAgB,EAAE8c,iBAAiB,EAAE/B,oBAAoB,EAAExE,oBAAoB,EAAErF,eAAe,EAAEvQ,gBAAgB,EAAE4O,uBAAuB,EAAEhN,aAAa,EAAEE,iBAAiB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAE1G,8BAA8B,EAAEuZ,+BAA+B,EAAE3F,sBAAsB,EAAE6M,0BAA0B,EAAEhI,8BAA8B,EAAEpE,mBAAmB,EAAE9T,wBAAwB,EAAEH,QAAQ,EAAEU,QAAQ,EAAEZ,QAAQ,EAAEG,MAAM,EAAEsT,YAAY,EAAEjE,WAAW,EAAEE,YAAY,EAAEE,SAAS,EAAEsB,WAAW,EAAER,UAAU,EAAEW,aAAa,EAAET,YAAY,EAAEE,WAAW,EAAExC,iBAAiB,EAAEK,aAAa,EAAEG,cAAc,EAAEzB,OAAO,EAAEa,eAAe,EAAEH,WAAW,EAAEsB,UAAU,EAAEmC,gBAAgB,EAAEG,wBAAwB,EAAEjD,eAAe,EAAEhB,QAAQ,EAAEH,YAAY,EAAEa,cAAc,EAAE+D,qBAAqB,EAAEjD,WAAW,EAAEV,UAAU,EAAEK,WAAW,EAAEhI,aAAa,EAAE6E,IAAI,IAAI/H,EAAE,EAAEiI,EAAE,EAAEoC,GAAG,EAAEG,GAAG,EAAEE,GAAG,EAAEE,GAAG,EAAEE,GAAG,EAAEG,GAAG,EAAEG,GAAG,EAAEI,GAAG,EAAEG,GAAG,EAAEG,GAAG,EAAErD,EAAE,EAAEuD,GAAG,EAAEc,GAAG,EAAEE,GAAG,EAAEE,GAAG,EAAEE,GAAG,EAAEI,GAAG,EAAEG,GAAG,EAAEG,GAAG,EAAEG,GAAG,EAAEnF,EAAE,EAAEM,EAAE,EAAEI,EAAE,EAAEC,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEG,EAAE,EAAEzF,YAAY,IAAImZ,EAAE,EAAEvZ,aAAa,IAAIwZ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}