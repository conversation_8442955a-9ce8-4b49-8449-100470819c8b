{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatTreeNode, _MatTreeNodeDef, _MatNestedTreeNode, _MatTreeNodePadding, _MatTreeNodeOutlet, _MatTree, _MatTreeNodeToggle, _MatTreeModule;\nimport { CdkTreeNode, CdkTreeNodeDef, CdkNestedTreeNode, CDK_TREE_NODE_OUTLET_NODE, CdkTreeNodePadding, CdkTreeNodeOutlet, CdkTree, CdkTreeNodeToggle, CdkTreeModule } from '@angular/cdk/tree';\nimport * as i0 from '@angular/core';\nimport { inject, HostAttributeToken, numberAttribute, booleanAttribute, Directive, Input, ViewContainerRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { DataSource } from '@angular/cdk/collections';\nimport { BehaviorSubject, merge } from 'rxjs';\nimport { take, map } from 'rxjs/operators';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/**\n * Determinte if argument TreeKeyManager is the NoopTreeKeyManager. This function is safe to use with SSR.\n */\nfunction isNoopTreeKeyManager(keyManager) {\n  return !!keyManager._isNoopTreeKeyManager;\n}\n/**\n * Wrapper for the CdkTree node with Material design styles.\n */\nclass MatTreeNode extends CdkTreeNode {\n  /**\n   * The tabindex of the tree node.\n   *\n   * @deprecated By default MatTreeNode manages focus using TreeKeyManager instead of tabIndex.\n   *   Recommend to avoid setting tabIndex directly to prevent TreeKeyManager form getting into\n   *   an unexpected state. Tabindex to be removed in a future version.\n   * @breaking-change 21.0.0 Remove this attribute.\n   */\n  get tabIndexInputBinding() {\n    return this._tabIndexInputBinding;\n  }\n  set tabIndexInputBinding(value) {\n    // If the specified tabIndex value is null or undefined, fall back to the default value.\n    this._tabIndexInputBinding = value;\n  }\n  _getTabindexAttribute() {\n    if (isNoopTreeKeyManager(this._tree._keyManager)) {\n      return this.tabIndexInputBinding;\n    }\n    return this._tabindex;\n  }\n  /**\n   * Whether the component is disabled.\n   *\n   * @deprecated This is an alias for `isDisabled`.\n   * @breaking-change 21.0.0 Remove this input\n   */\n  get disabled() {\n    return this.isDisabled;\n  }\n  set disabled(value) {\n    this.isDisabled = value;\n  }\n  constructor() {\n    super();\n    _defineProperty(this, \"_tabIndexInputBinding\", void 0);\n    /**\n     * The default tabindex of the tree node.\n     *\n     * @deprecated By default MatTreeNode manages focus using TreeKeyManager instead of tabIndex.\n     *   Recommend to avoid setting tabIndex directly to prevent TreeKeyManager form getting into\n     *   an unexpected state. Tabindex to be removed in a future version.\n     * @breaking-change 21.0.0 Remove this attribute.\n     */\n    _defineProperty(this, \"defaultTabIndex\", 0);\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    this.tabIndexInputBinding = Number(tabIndex) || this.defaultTabIndex;\n  }\n  // This is a workaround for https://github.com/angular/angular/issues/23091\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  ngOnInit() {\n    super.ngOnInit();\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n  }\n}\n_MatTreeNode = MatTreeNode;\n_defineProperty(MatTreeNode, \"\\u0275fac\", function _MatTreeNode_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTreeNode)();\n});\n_defineProperty(MatTreeNode, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTreeNode,\n  selectors: [[\"mat-tree-node\"]],\n  hostAttrs: [1, \"mat-tree-node\"],\n  hostVars: 5,\n  hostBindings: function _MatTreeNode_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _MatTreeNode_click_HostBindingHandler() {\n        return ctx._focusItem();\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"tabindex\", ctx._getTabindexAttribute());\n      i0.ɵɵattribute(\"aria-expanded\", ctx._getAriaExpanded())(\"aria-level\", ctx.level + 1)(\"aria-posinset\", ctx._getPositionInSet())(\"aria-setsize\", ctx._getSetSize());\n    }\n  },\n  inputs: {\n    tabIndexInputBinding: [2, \"tabIndex\", \"tabIndexInputBinding\", value => value == null ? 0 : numberAttribute(value)],\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n  },\n  outputs: {\n    activation: \"activation\",\n    expandedChange: \"expandedChange\"\n  },\n  exportAs: [\"matTreeNode\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkTreeNode,\n    useExisting: _MatTreeNode\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-tree-node',\n      exportAs: 'matTreeNode',\n      outputs: ['activation', 'expandedChange'],\n      providers: [{\n        provide: CdkTreeNode,\n        useExisting: MatTreeNode\n      }],\n      host: {\n        'class': 'mat-tree-node',\n        '[attr.aria-expanded]': '_getAriaExpanded()',\n        '[attr.aria-level]': 'level + 1',\n        '[attr.aria-posinset]': '_getPositionInSet()',\n        '[attr.aria-setsize]': '_getSetSize()',\n        '(click)': '_focusItem()',\n        '[tabindex]': '_getTabindexAttribute()'\n      }\n    }]\n  }], () => [], {\n    tabIndexInputBinding: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value),\n        alias: 'tabIndex'\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Wrapper for the CdkTree node definition with Material design styles.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nclass MatTreeNodeDef extends CdkTreeNodeDef {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"data\", void 0);\n  }\n}\n_MatTreeNodeDef = MatTreeNodeDef;\n_defineProperty(MatTreeNodeDef, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatTreeNodeDef_BaseFactory;\n  return function _MatTreeNodeDef_Factory(__ngFactoryType__) {\n    return (ɵ_MatTreeNodeDef_BaseFactory || (ɵ_MatTreeNodeDef_BaseFactory = i0.ɵɵgetInheritedFactory(_MatTreeNodeDef)))(__ngFactoryType__ || _MatTreeNodeDef);\n  };\n})());\n_defineProperty(MatTreeNodeDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTreeNodeDef,\n  selectors: [[\"\", \"matTreeNodeDef\", \"\"]],\n  inputs: {\n    when: [0, \"matTreeNodeDefWhen\", \"when\"],\n    data: [0, \"matTreeNode\", \"data\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkTreeNodeDef,\n    useExisting: _MatTreeNodeDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodeDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodeDef]',\n      inputs: [{\n        name: 'when',\n        alias: 'matTreeNodeDefWhen'\n      }],\n      providers: [{\n        provide: CdkTreeNodeDef,\n        useExisting: MatTreeNodeDef\n      }]\n    }]\n  }], null, {\n    data: [{\n      type: Input,\n      args: ['matTreeNode']\n    }]\n  });\n})();\n/**\n * Wrapper for the CdkTree nested node with Material design styles.\n */\nclass MatNestedTreeNode extends CdkNestedTreeNode {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"node\", void 0);\n    _defineProperty(this, \"_tabIndex\", void 0);\n  }\n  /**\n   * Whether the node is disabled.\n   *\n   * @deprecated This is an alias for `isDisabled`.\n   * @breaking-change 21.0.0 Remove this input\n   */\n  get disabled() {\n    return this.isDisabled;\n  }\n  set disabled(value) {\n    this.isDisabled = value;\n  }\n  /** Tabindex of the node. */\n  get tabIndex() {\n    return this.isDisabled ? -1 : this._tabIndex;\n  }\n  set tabIndex(value) {\n    // If the specified tabIndex value is null or undefined, fall back to the default value.\n    this._tabIndex = value;\n  }\n  // This is a workaround for https://github.com/angular/angular/issues/19145\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  // TODO(tinayuangao): Remove when the angular issue #19145 is fixed\n  ngOnInit() {\n    super.ngOnInit();\n  }\n  ngAfterContentInit() {\n    super.ngAfterContentInit();\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n  }\n}\n_MatNestedTreeNode = MatNestedTreeNode;\n_defineProperty(MatNestedTreeNode, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatNestedTreeNode_BaseFactory;\n  return function _MatNestedTreeNode_Factory(__ngFactoryType__) {\n    return (ɵ_MatNestedTreeNode_BaseFactory || (ɵ_MatNestedTreeNode_BaseFactory = i0.ɵɵgetInheritedFactory(_MatNestedTreeNode)))(__ngFactoryType__ || _MatNestedTreeNode);\n  };\n})());\n_defineProperty(MatNestedTreeNode, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatNestedTreeNode,\n  selectors: [[\"mat-nested-tree-node\"]],\n  hostAttrs: [1, \"mat-nested-tree-node\"],\n  inputs: {\n    node: [0, \"matNestedTreeNode\", \"node\"],\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n    tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n  },\n  outputs: {\n    activation: \"activation\",\n    expandedChange: \"expandedChange\"\n  },\n  exportAs: [\"matNestedTreeNode\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkNestedTreeNode,\n    useExisting: _MatNestedTreeNode\n  }, {\n    provide: CdkTreeNode,\n    useExisting: _MatNestedTreeNode\n  }, {\n    provide: CDK_TREE_NODE_OUTLET_NODE,\n    useExisting: _MatNestedTreeNode\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNestedTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-nested-tree-node',\n      exportAs: 'matNestedTreeNode',\n      outputs: ['activation', 'expandedChange'],\n      providers: [{\n        provide: CdkNestedTreeNode,\n        useExisting: MatNestedTreeNode\n      }, {\n        provide: CdkTreeNode,\n        useExisting: MatNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: MatNestedTreeNode\n      }],\n      host: {\n        'class': 'mat-nested-tree-node'\n      }\n    }]\n  }], null, {\n    node: [{\n      type: Input,\n      args: ['matNestedTreeNode']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }]\n  });\n})();\n\n/**\n * Wrapper for the CdkTree padding with Material design styles.\n */\nclass MatTreeNodePadding extends CdkTreeNodePadding {\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  get level() {\n    return this._level;\n  }\n  set level(value) {\n    this._setLevelInput(value);\n  }\n  /** The indent for each level. Default number 40px from material design menu sub-menu spec. */\n  get indent() {\n    return this._indent;\n  }\n  set indent(indent) {\n    this._setIndentInput(indent);\n  }\n}\n_MatTreeNodePadding = MatTreeNodePadding;\n_defineProperty(MatTreeNodePadding, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatTreeNodePadding_BaseFactory;\n  return function _MatTreeNodePadding_Factory(__ngFactoryType__) {\n    return (ɵ_MatTreeNodePadding_BaseFactory || (ɵ_MatTreeNodePadding_BaseFactory = i0.ɵɵgetInheritedFactory(_MatTreeNodePadding)))(__ngFactoryType__ || _MatTreeNodePadding);\n  };\n})());\n_defineProperty(MatTreeNodePadding, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTreeNodePadding,\n  selectors: [[\"\", \"matTreeNodePadding\", \"\"]],\n  inputs: {\n    level: [2, \"matTreeNodePadding\", \"level\", numberAttribute],\n    indent: [0, \"matTreeNodePaddingIndent\", \"indent\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkTreeNodePadding,\n    useExisting: _MatTreeNodePadding\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodePadding, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodePadding]',\n      providers: [{\n        provide: CdkTreeNodePadding,\n        useExisting: MatTreeNodePadding\n      }]\n    }]\n  }], null, {\n    level: [{\n      type: Input,\n      args: [{\n        alias: 'matTreeNodePadding',\n        transform: numberAttribute\n      }]\n    }],\n    indent: [{\n      type: Input,\n      args: ['matTreeNodePaddingIndent']\n    }]\n  });\n})();\n\n/**\n * Outlet for nested CdkNode. Put `[matTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nclass MatTreeNodeOutlet {\n  constructor() {\n    _defineProperty(this, \"viewContainer\", inject(ViewContainerRef));\n    _defineProperty(this, \"_node\", inject(CDK_TREE_NODE_OUTLET_NODE, {\n      optional: true\n    }));\n  }\n}\n_MatTreeNodeOutlet = MatTreeNodeOutlet;\n_defineProperty(MatTreeNodeOutlet, \"\\u0275fac\", function _MatTreeNodeOutlet_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTreeNodeOutlet)();\n});\n_defineProperty(MatTreeNodeOutlet, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTreeNodeOutlet,\n  selectors: [[\"\", \"matTreeNodeOutlet\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkTreeNodeOutlet,\n    useExisting: _MatTreeNodeOutlet\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodeOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodeOutlet]',\n      providers: [{\n        provide: CdkTreeNodeOutlet,\n        useExisting: MatTreeNodeOutlet\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Wrapper for the CdkTable with Material design styles.\n */\nclass MatTree extends CdkTree {\n  constructor(...args) {\n    super(...args);\n    // Outlets within the tree's template where the dataNodes will be inserted.\n    // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n    _defineProperty(this, \"_nodeOutlet\", undefined);\n  }\n}\n_MatTree = MatTree;\n_defineProperty(MatTree, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatTree_BaseFactory;\n  return function _MatTree_Factory(__ngFactoryType__) {\n    return (ɵ_MatTree_BaseFactory || (ɵ_MatTree_BaseFactory = i0.ɵɵgetInheritedFactory(_MatTree)))(__ngFactoryType__ || _MatTree);\n  };\n})());\n_defineProperty(MatTree, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatTree,\n  selectors: [[\"mat-tree\"]],\n  viewQuery: function _MatTree_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(MatTreeNodeOutlet, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeOutlet = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-tree\"],\n  exportAs: [\"matTree\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkTree,\n    useExisting: _MatTree\n  }]), i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 0,\n  consts: [[\"matTreeNodeOutlet\", \"\"]],\n  template: function _MatTree_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [MatTreeNodeOutlet],\n  styles: [\".mat-tree{display:block;background-color:var(--mat-tree-container-background-color, var(--mat-sys-surface))}.mat-tree-node,.mat-nested-tree-node{color:var(--mat-tree-node-text-color, var(--mat-sys-on-surface));font-family:var(--mat-tree-node-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-tree-node-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-tree-node-text-weight, var(--mat-sys-body-large-weight))}.mat-tree-node{display:flex;align-items:center;flex:1;word-wrap:break-word;min-height:var(--mat-tree-node-min-height, 48px)}.mat-nested-tree-node{border-bottom-width:0}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTree, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tree',\n      exportAs: 'matTree',\n      template: `<ng-container matTreeNodeOutlet></ng-container>`,\n      host: {\n        'class': 'mat-tree'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: CdkTree,\n        useExisting: MatTree\n      }],\n      imports: [MatTreeNodeOutlet],\n      styles: [\".mat-tree{display:block;background-color:var(--mat-tree-container-background-color, var(--mat-sys-surface))}.mat-tree-node,.mat-nested-tree-node{color:var(--mat-tree-node-text-color, var(--mat-sys-on-surface));font-family:var(--mat-tree-node-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-tree-node-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-tree-node-text-weight, var(--mat-sys-body-large-weight))}.mat-tree-node{display:flex;align-items:center;flex:1;word-wrap:break-word;min-height:var(--mat-tree-node-min-height, 48px)}.mat-nested-tree-node{border-bottom-width:0}\\n\"]\n    }]\n  }], null, {\n    _nodeOutlet: [{\n      type: ViewChild,\n      args: [MatTreeNodeOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Wrapper for the CdkTree's toggle with Material design styles.\n */\nclass MatTreeNodeToggle extends CdkTreeNodeToggle {}\n_MatTreeNodeToggle = MatTreeNodeToggle;\n_defineProperty(MatTreeNodeToggle, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatTreeNodeToggle_BaseFactory;\n  return function _MatTreeNodeToggle_Factory(__ngFactoryType__) {\n    return (ɵ_MatTreeNodeToggle_BaseFactory || (ɵ_MatTreeNodeToggle_BaseFactory = i0.ɵɵgetInheritedFactory(_MatTreeNodeToggle)))(__ngFactoryType__ || _MatTreeNodeToggle);\n  };\n})());\n_defineProperty(MatTreeNodeToggle, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTreeNodeToggle,\n  selectors: [[\"\", \"matTreeNodeToggle\", \"\"]],\n  inputs: {\n    recursive: [0, \"matTreeNodeToggleRecursive\", \"recursive\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkTreeNodeToggle,\n    useExisting: _MatTreeNodeToggle\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodeToggle, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodeToggle]',\n      providers: [{\n        provide: CdkTreeNodeToggle,\n        useExisting: MatTreeNodeToggle\n      }],\n      inputs: [{\n        name: 'recursive',\n        alias: 'matTreeNodeToggleRecursive'\n      }]\n    }]\n  }], null, null);\n})();\nconst MAT_TREE_DIRECTIVES = [MatNestedTreeNode, MatTreeNodeDef, MatTreeNodePadding, MatTreeNodeToggle, MatTree, MatTreeNode, MatTreeNodeOutlet];\nclass MatTreeModule {}\n_MatTreeModule = MatTreeModule;\n_defineProperty(MatTreeModule, \"\\u0275fac\", function _MatTreeModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTreeModule)();\n});\n_defineProperty(MatTreeModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatTreeModule,\n  imports: [CdkTreeModule, MatCommonModule, MatNestedTreeNode, MatTreeNodeDef, MatTreeNodePadding, MatTreeNodeToggle, MatTree, MatTreeNode, MatTreeNodeOutlet],\n  exports: [MatCommonModule, MatNestedTreeNode, MatTreeNodeDef, MatTreeNodePadding, MatTreeNodeToggle, MatTree, MatTreeNode, MatTreeNodeOutlet]\n}));\n_defineProperty(MatTreeModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CdkTreeModule, MatCommonModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkTreeModule, MatCommonModule, ...MAT_TREE_DIRECTIVES],\n      exports: [MatCommonModule, MAT_TREE_DIRECTIVES]\n    }]\n  }], null, null);\n})();\n\n/**\n * Tree flattener to convert a normal type of node to node with children & level information.\n * Transform nested nodes of type `T` to flattened nodes of type `F`.\n *\n * For example, the input data of type `T` is nested, and contains its children data:\n *   SomeNode: {\n *     key: 'Fruits',\n *     children: [\n *       NodeOne: {\n *         key: 'Apple',\n *       },\n *       NodeTwo: {\n *        key: 'Pear',\n *      }\n *    ]\n *  }\n *  After flattener flatten the tree, the structure will become\n *  SomeNode: {\n *    key: 'Fruits',\n *    expandable: true,\n *    level: 1\n *  },\n *  NodeOne: {\n *    key: 'Apple',\n *    expandable: false,\n *    level: 2\n *  },\n *  NodeTwo: {\n *   key: 'Pear',\n *   expandable: false,\n *   level: 2\n * }\n * and the output flattened type is `F` with additional information.\n *\n * @deprecated Use MatTree#childrenAccessor and MatTreeNode#isExpandable\n * instead. To be removed in a future version.\n * @breaking-change 21.0.0\n */\nclass MatTreeFlattener {\n  constructor(transformFunction, getLevel, isExpandable, getChildren) {\n    _defineProperty(this, \"transformFunction\", void 0);\n    _defineProperty(this, \"getLevel\", void 0);\n    _defineProperty(this, \"isExpandable\", void 0);\n    _defineProperty(this, \"getChildren\", void 0);\n    this.transformFunction = transformFunction;\n    this.getLevel = getLevel;\n    this.isExpandable = isExpandable;\n    this.getChildren = getChildren;\n  }\n  _flattenNode(node, level, resultNodes, parentMap) {\n    const flatNode = this.transformFunction(node, level);\n    resultNodes.push(flatNode);\n    if (this.isExpandable(flatNode)) {\n      const childrenNodes = this.getChildren(node);\n      if (childrenNodes) {\n        if (Array.isArray(childrenNodes)) {\n          this._flattenChildren(childrenNodes, level, resultNodes, parentMap);\n        } else {\n          childrenNodes.pipe(take(1)).subscribe(children => {\n            this._flattenChildren(children, level, resultNodes, parentMap);\n          });\n        }\n      }\n    }\n    return resultNodes;\n  }\n  _flattenChildren(children, level, resultNodes, parentMap) {\n    children.forEach((child, index) => {\n      let childParentMap = parentMap.slice();\n      childParentMap.push(index != children.length - 1);\n      this._flattenNode(child, level + 1, resultNodes, childParentMap);\n    });\n  }\n  /**\n   * Flatten a list of node type T to flattened version of node F.\n   * Please note that type T may be nested, and the length of `structuredData` may be different\n   * from that of returned list `F[]`.\n   */\n  flattenNodes(structuredData) {\n    let resultNodes = [];\n    structuredData.forEach(node => this._flattenNode(node, 0, resultNodes, []));\n    return resultNodes;\n  }\n  /**\n   * Expand flattened node with current expansion status.\n   * The returned list may have different length.\n   */\n  expandFlattenedNodes(nodes, treeControl) {\n    let results = [];\n    let currentExpand = [];\n    currentExpand[0] = true;\n    nodes.forEach(node => {\n      let expand = true;\n      for (let i = 0; i <= this.getLevel(node); i++) {\n        expand = expand && currentExpand[i];\n      }\n      if (expand) {\n        results.push(node);\n      }\n      if (this.isExpandable(node)) {\n        currentExpand[this.getLevel(node) + 1] = treeControl.isExpanded(node);\n      }\n    });\n    return results;\n  }\n}\n/**\n * Data source for flat tree.\n * The data source need to handle expansion/collapsion of the tree node and change the data feed\n * to `MatTree`.\n * The nested tree nodes of type `T` are flattened through `MatTreeFlattener`, and converted\n * to type `F` for `MatTree` to consume.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nclass MatTreeFlatDataSource extends DataSource {\n  get data() {\n    return this._data.value;\n  }\n  set data(value) {\n    this._data.next(value);\n    this._flattenedData.next(this._treeFlattener.flattenNodes(this.data));\n    this._treeControl.dataNodes = this._flattenedData.value;\n  }\n  constructor(_treeControl, _treeFlattener, initialData) {\n    super();\n    _defineProperty(this, \"_treeControl\", void 0);\n    _defineProperty(this, \"_treeFlattener\", void 0);\n    _defineProperty(this, \"_flattenedData\", new BehaviorSubject([]));\n    _defineProperty(this, \"_expandedData\", new BehaviorSubject([]));\n    _defineProperty(this, \"_data\", new BehaviorSubject([]));\n    this._treeControl = _treeControl;\n    this._treeFlattener = _treeFlattener;\n    if (initialData) {\n      // Assign the data through the constructor to ensure that all of the logic is executed.\n      this.data = initialData;\n    }\n  }\n  connect(collectionViewer) {\n    return merge(collectionViewer.viewChange, this._treeControl.expansionModel.changed, this._flattenedData).pipe(map(() => {\n      this._expandedData.next(this._treeFlattener.expandFlattenedNodes(this._flattenedData.value, this._treeControl));\n      return this._expandedData.value;\n    }));\n  }\n  disconnect() {\n    // no op\n  }\n}\n\n/**\n * Data source for nested tree.\n *\n * The data source for nested tree doesn't have to consider node flattener, or the way to expand\n * or collapse. The expansion/collapsion will be handled by TreeControl and each non-leaf node.\n */\nclass MatTreeNestedDataSource extends DataSource {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"_data\", new BehaviorSubject([]));\n  }\n  /**\n   * Data for the nested tree\n   */\n  get data() {\n    return this._data.value;\n  }\n  set data(value) {\n    this._data.next(value);\n  }\n  connect(collectionViewer) {\n    return merge(...[collectionViewer.viewChange, this._data]).pipe(map(() => this.data));\n  }\n  disconnect() {\n    // no op\n  }\n}\nexport { MatNestedTreeNode, MatTree, MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule, MatTreeNestedDataSource, MatTreeNode, MatTreeNodeDef, MatTreeNodeOutlet, MatTreeNodePadding, MatTreeNodeToggle };\n//# sourceMappingURL=tree.mjs.map", "map": {"version": 3, "names": ["CdkTreeNode", "CdkTreeNodeDef", "CdkNestedTreeNode", "CDK_TREE_NODE_OUTLET_NODE", "CdkTreeNodePadding", "CdkTreeNodeOutlet", "CdkTree", "CdkTreeNodeToggle", "CdkTreeModule", "i0", "inject", "HostAttributeToken", "numberAttribute", "booleanAttribute", "Directive", "Input", "ViewContainerRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "NgModule", "M", "MatCommonModule", "DataSource", "BehaviorSubject", "merge", "take", "map", "isNoopTreeKeyManager", "keyManager", "_isNoopTreeKeyManager", "MatTreeNode", "tabIndexInputBinding", "_tabIndexInputBinding", "value", "_getTabindexAttribute", "_tree", "_keyManager", "_tabindex", "disabled", "isDisabled", "constructor", "_defineProperty", "tabIndex", "optional", "Number", "defaultTabIndex", "ngOnInit", "ngOnDestroy", "_MatTreeNode", "_MatTreeNode_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "_MatTreeNode_HostBindings", "rf", "ctx", "ɵɵlistener", "_MatTreeNode_click_HostBindingHandler", "_focusItem", "ɵɵhostProperty", "ɵɵattribute", "_getAriaExpanded", "level", "_getPositionInSet", "_getSetSize", "inputs", "outputs", "activation", "expandedChange", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "host", "transform", "alias", "MatTreeNodeDef", "_MatTreeNodeDef", "ɵ_MatTreeNodeDef_BaseFactory", "_MatTreeNodeDef_Factory", "ɵɵgetInheritedFactory", "when", "data", "name", "MatNestedTreeNode", "_tabIndex", "ngAfterContentInit", "_MatNestedTreeNode", "ɵ_MatNestedTreeNode_BaseFactory", "_MatNestedTreeNode_Factory", "node", "MatTreeNodePadding", "_level", "_setLevelInput", "indent", "_indent", "_setIndentInput", "_MatTreeNodePadding", "ɵ_MatTreeNodePadding_BaseFactory", "_MatTreeNodePadding_Factory", "MatTreeNodeOutlet", "_MatTreeNodeOutlet", "_MatTreeNodeOutlet_Factory", "<PERSON><PERSON><PERSON>", "undefined", "_Mat<PERSON>ree", "ɵ_MatTree_BaseFactory", "_MatTree_Factory", "ɵɵdefineComponent", "viewQuery", "_MatTree_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "_nodeOutlet", "first", "decls", "vars", "consts", "template", "_MatTree_Template", "ɵɵelementContainer", "dependencies", "styles", "encapsulation", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "static", "MatTreeNodeToggle", "_MatTreeNodeToggle", "ɵ_MatTreeNodeToggle_BaseFactory", "_MatTreeNodeToggle_Factory", "recursive", "MAT_TREE_DIRECTIVES", "MatTreeModule", "_MatTreeModule", "_MatTreeModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "MatTreeFlattener", "transformFunction", "getLevel", "isExpandable", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_flattenNode", "resultNodes", "parentMap", "flatNode", "push", "childrenNodes", "Array", "isArray", "_flatten<PERSON><PERSON><PERSON>n", "pipe", "subscribe", "children", "for<PERSON>ach", "child", "index", "childParentMap", "slice", "length", "flattenNodes", "structuredData", "expandFlattenedNodes", "nodes", "treeControl", "results", "currentExpand", "expand", "i", "isExpanded", "MatTreeFlatDataSource", "_data", "next", "_flattenedData", "_treeFlattener", "_treeControl", "dataNodes", "initialData", "connect", "collectionViewer", "viewChange", "expansionModel", "changed", "_expandedData", "disconnect", "MatTreeNestedDataSource"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/tree.mjs"], "sourcesContent": ["import { CdkTreeNode, CdkTreeNodeDef, CdkNestedTreeNode, CDK_TREE_NODE_OUTLET_NODE, CdkTreeNodePadding, CdkTreeNodeOutlet, CdkTree, CdkTreeNodeToggle, CdkTreeModule } from '@angular/cdk/tree';\nimport * as i0 from '@angular/core';\nimport { inject, HostAttributeToken, numberAttribute, booleanAttribute, Directive, Input, ViewContainerRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { DataSource } from '@angular/cdk/collections';\nimport { BehaviorSubject, merge } from 'rxjs';\nimport { take, map } from 'rxjs/operators';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/**\n * Determinte if argument TreeKeyManager is the NoopTreeKeyManager. This function is safe to use with SSR.\n */\nfunction isNoopTreeKeyManager(keyManager) {\n    return !!keyManager._isNoopTreeKeyManager;\n}\n/**\n * Wrapper for the CdkTree node with Material design styles.\n */\nclass MatTreeNode extends CdkTreeNode {\n    /**\n     * The tabindex of the tree node.\n     *\n     * @deprecated By default MatTreeNode manages focus using TreeKeyManager instead of tabIndex.\n     *   Recommend to avoid setting tabIndex directly to prevent TreeKeyManager form getting into\n     *   an unexpected state. Tabindex to be removed in a future version.\n     * @breaking-change 21.0.0 Remove this attribute.\n     */\n    get tabIndexInputBinding() {\n        return this._tabIndexInputBinding;\n    }\n    set tabIndexInputBinding(value) {\n        // If the specified tabIndex value is null or undefined, fall back to the default value.\n        this._tabIndexInputBinding = value;\n    }\n    _tabIndexInputBinding;\n    /**\n     * The default tabindex of the tree node.\n     *\n     * @deprecated By default MatTreeNode manages focus using TreeKeyManager instead of tabIndex.\n     *   Recommend to avoid setting tabIndex directly to prevent TreeKeyManager form getting into\n     *   an unexpected state. Tabindex to be removed in a future version.\n     * @breaking-change 21.0.0 Remove this attribute.\n     */\n    defaultTabIndex = 0;\n    _getTabindexAttribute() {\n        if (isNoopTreeKeyManager(this._tree._keyManager)) {\n            return this.tabIndexInputBinding;\n        }\n        return this._tabindex;\n    }\n    /**\n     * Whether the component is disabled.\n     *\n     * @deprecated This is an alias for `isDisabled`.\n     * @breaking-change 21.0.0 Remove this input\n     */\n    get disabled() {\n        return this.isDisabled;\n    }\n    set disabled(value) {\n        this.isDisabled = value;\n    }\n    constructor() {\n        super();\n        const tabIndex = inject(new HostAttributeToken('tabindex'), { optional: true });\n        this.tabIndexInputBinding = Number(tabIndex) || this.defaultTabIndex;\n    }\n    // This is a workaround for https://github.com/angular/angular/issues/23091\n    // In aot mode, the lifecycle hooks from parent class are not called.\n    ngOnInit() {\n        super.ngOnInit();\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeNode, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatTreeNode, isStandalone: true, selector: \"mat-tree-node\", inputs: { tabIndexInputBinding: [\"tabIndex\", \"tabIndexInputBinding\", (value) => (value == null ? 0 : numberAttribute(value))], disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { activation: \"activation\", expandedChange: \"expandedChange\" }, host: { listeners: { \"click\": \"_focusItem()\" }, properties: { \"attr.aria-expanded\": \"_getAriaExpanded()\", \"attr.aria-level\": \"level + 1\", \"attr.aria-posinset\": \"_getPositionInSet()\", \"attr.aria-setsize\": \"_getSetSize()\", \"tabindex\": \"_getTabindexAttribute()\" }, classAttribute: \"mat-tree-node\" }, providers: [{ provide: CdkTreeNode, useExisting: MatTreeNode }], exportAs: [\"matTreeNode\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeNode, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-tree-node',\n                    exportAs: 'matTreeNode',\n                    outputs: ['activation', 'expandedChange'],\n                    providers: [{ provide: CdkTreeNode, useExisting: MatTreeNode }],\n                    host: {\n                        'class': 'mat-tree-node',\n                        '[attr.aria-expanded]': '_getAriaExpanded()',\n                        '[attr.aria-level]': 'level + 1',\n                        '[attr.aria-posinset]': '_getPositionInSet()',\n                        '[attr.aria-setsize]': '_getSetSize()',\n                        '(click)': '_focusItem()',\n                        '[tabindex]': '_getTabindexAttribute()',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { tabIndexInputBinding: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                        alias: 'tabIndex',\n                    }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n/**\n * Wrapper for the CdkTree node definition with Material design styles.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nclass MatTreeNodeDef extends CdkTreeNodeDef {\n    data;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeNodeDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTreeNodeDef, isStandalone: true, selector: \"[matTreeNodeDef]\", inputs: { when: [\"matTreeNodeDefWhen\", \"when\"], data: [\"matTreeNode\", \"data\"] }, providers: [{ provide: CdkTreeNodeDef, useExisting: MatTreeNodeDef }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeNodeDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTreeNodeDef]',\n                    inputs: [{ name: 'when', alias: 'matTreeNodeDefWhen' }],\n                    providers: [{ provide: CdkTreeNodeDef, useExisting: MatTreeNodeDef }],\n                }]\n        }], propDecorators: { data: [{\n                type: Input,\n                args: ['matTreeNode']\n            }] } });\n/**\n * Wrapper for the CdkTree nested node with Material design styles.\n */\nclass MatNestedTreeNode extends CdkNestedTreeNode {\n    node;\n    /**\n     * Whether the node is disabled.\n     *\n     * @deprecated This is an alias for `isDisabled`.\n     * @breaking-change 21.0.0 Remove this input\n     */\n    get disabled() {\n        return this.isDisabled;\n    }\n    set disabled(value) {\n        this.isDisabled = value;\n    }\n    /** Tabindex of the node. */\n    get tabIndex() {\n        return this.isDisabled ? -1 : this._tabIndex;\n    }\n    set tabIndex(value) {\n        // If the specified tabIndex value is null or undefined, fall back to the default value.\n        this._tabIndex = value;\n    }\n    _tabIndex;\n    // This is a workaround for https://github.com/angular/angular/issues/19145\n    // In aot mode, the lifecycle hooks from parent class are not called.\n    // TODO(tinayuangao): Remove when the angular issue #19145 is fixed\n    ngOnInit() {\n        super.ngOnInit();\n    }\n    ngAfterContentInit() {\n        super.ngAfterContentInit();\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatNestedTreeNode, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatNestedTreeNode, isStandalone: true, selector: \"mat-nested-tree-node\", inputs: { node: [\"matNestedTreeNode\", \"node\"], disabled: [\"disabled\", \"disabled\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))] }, outputs: { activation: \"activation\", expandedChange: \"expandedChange\" }, host: { classAttribute: \"mat-nested-tree-node\" }, providers: [\n            { provide: CdkNestedTreeNode, useExisting: MatNestedTreeNode },\n            { provide: CdkTreeNode, useExisting: MatNestedTreeNode },\n            { provide: CDK_TREE_NODE_OUTLET_NODE, useExisting: MatNestedTreeNode },\n        ], exportAs: [\"matNestedTreeNode\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatNestedTreeNode, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-nested-tree-node',\n                    exportAs: 'matNestedTreeNode',\n                    outputs: ['activation', 'expandedChange'],\n                    providers: [\n                        { provide: CdkNestedTreeNode, useExisting: MatNestedTreeNode },\n                        { provide: CdkTreeNode, useExisting: MatNestedTreeNode },\n                        { provide: CDK_TREE_NODE_OUTLET_NODE, useExisting: MatNestedTreeNode },\n                    ],\n                    host: {\n                        'class': 'mat-nested-tree-node',\n                    },\n                }]\n        }], propDecorators: { node: [{\n                type: Input,\n                args: ['matNestedTreeNode']\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }] } });\n\n/**\n * Wrapper for the CdkTree padding with Material design styles.\n */\nclass MatTreeNodePadding extends CdkTreeNodePadding {\n    /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n    get level() {\n        return this._level;\n    }\n    set level(value) {\n        this._setLevelInput(value);\n    }\n    /** The indent for each level. Default number 40px from material design menu sub-menu spec. */\n    get indent() {\n        return this._indent;\n    }\n    set indent(indent) {\n        this._setIndentInput(indent);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeNodePadding, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatTreeNodePadding, isStandalone: true, selector: \"[matTreeNodePadding]\", inputs: { level: [\"matTreeNodePadding\", \"level\", numberAttribute], indent: [\"matTreeNodePaddingIndent\", \"indent\"] }, providers: [{ provide: CdkTreeNodePadding, useExisting: MatTreeNodePadding }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeNodePadding, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTreeNodePadding]',\n                    providers: [{ provide: CdkTreeNodePadding, useExisting: MatTreeNodePadding }],\n                }]\n        }], propDecorators: { level: [{\n                type: Input,\n                args: [{ alias: 'matTreeNodePadding', transform: numberAttribute }]\n            }], indent: [{\n                type: Input,\n                args: ['matTreeNodePaddingIndent']\n            }] } });\n\n/**\n * Outlet for nested CdkNode. Put `[matTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nclass MatTreeNodeOutlet {\n    viewContainer = inject(ViewContainerRef);\n    _node = inject(CDK_TREE_NODE_OUTLET_NODE, { optional: true });\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeNodeOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTreeNodeOutlet, isStandalone: true, selector: \"[matTreeNodeOutlet]\", providers: [\n            {\n                provide: CdkTreeNodeOutlet,\n                useExisting: MatTreeNodeOutlet,\n            },\n        ], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeNodeOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTreeNodeOutlet]',\n                    providers: [\n                        {\n                            provide: CdkTreeNodeOutlet,\n                            useExisting: MatTreeNodeOutlet,\n                        },\n                    ],\n                }]\n        }] });\n\n/**\n * Wrapper for the CdkTable with Material design styles.\n */\nclass MatTree extends CdkTree {\n    // Outlets within the tree's template where the dataNodes will be inserted.\n    // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n    _nodeOutlet = undefined;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTree, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTree, isStandalone: true, selector: \"mat-tree\", host: { classAttribute: \"mat-tree\" }, providers: [{ provide: CdkTree, useExisting: MatTree }], viewQueries: [{ propertyName: \"_nodeOutlet\", first: true, predicate: MatTreeNodeOutlet, descendants: true, static: true }], exportAs: [\"matTree\"], usesInheritance: true, ngImport: i0, template: `<ng-container matTreeNodeOutlet></ng-container>`, isInline: true, styles: [\".mat-tree{display:block;background-color:var(--mat-tree-container-background-color, var(--mat-sys-surface))}.mat-tree-node,.mat-nested-tree-node{color:var(--mat-tree-node-text-color, var(--mat-sys-on-surface));font-family:var(--mat-tree-node-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-tree-node-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-tree-node-text-weight, var(--mat-sys-body-large-weight))}.mat-tree-node{display:flex;align-items:center;flex:1;word-wrap:break-word;min-height:var(--mat-tree-node-min-height, 48px)}.mat-nested-tree-node{border-bottom-width:0}\\n\"], dependencies: [{ kind: \"directive\", type: MatTreeNodeOutlet, selector: \"[matTreeNodeOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTree, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tree', exportAs: 'matTree', template: `<ng-container matTreeNodeOutlet></ng-container>`, host: {\n                        'class': 'mat-tree',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, providers: [{ provide: CdkTree, useExisting: MatTree }], imports: [MatTreeNodeOutlet], styles: [\".mat-tree{display:block;background-color:var(--mat-tree-container-background-color, var(--mat-sys-surface))}.mat-tree-node,.mat-nested-tree-node{color:var(--mat-tree-node-text-color, var(--mat-sys-on-surface));font-family:var(--mat-tree-node-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-tree-node-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-tree-node-text-weight, var(--mat-sys-body-large-weight))}.mat-tree-node{display:flex;align-items:center;flex:1;word-wrap:break-word;min-height:var(--mat-tree-node-min-height, 48px)}.mat-nested-tree-node{border-bottom-width:0}\\n\"] }]\n        }], propDecorators: { _nodeOutlet: [{\n                type: ViewChild,\n                args: [MatTreeNodeOutlet, { static: true }]\n            }] } });\n\n/**\n * Wrapper for the CdkTree's toggle with Material design styles.\n */\nclass MatTreeNodeToggle extends CdkTreeNodeToggle {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeNodeToggle, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTreeNodeToggle, isStandalone: true, selector: \"[matTreeNodeToggle]\", inputs: { recursive: [\"matTreeNodeToggleRecursive\", \"recursive\"] }, providers: [{ provide: CdkTreeNodeToggle, useExisting: MatTreeNodeToggle }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeNodeToggle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTreeNodeToggle]',\n                    providers: [{ provide: CdkTreeNodeToggle, useExisting: MatTreeNodeToggle }],\n                    inputs: [{ name: 'recursive', alias: 'matTreeNodeToggleRecursive' }],\n                }]\n        }] });\n\nconst MAT_TREE_DIRECTIVES = [\n    MatNestedTreeNode,\n    MatTreeNodeDef,\n    MatTreeNodePadding,\n    MatTreeNodeToggle,\n    MatTree,\n    MatTreeNode,\n    MatTreeNodeOutlet,\n];\nclass MatTreeModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeModule, imports: [CdkTreeModule, MatCommonModule, MatNestedTreeNode,\n            MatTreeNodeDef,\n            MatTreeNodePadding,\n            MatTreeNodeToggle,\n            MatTree,\n            MatTreeNode,\n            MatTreeNodeOutlet], exports: [MatCommonModule, MatNestedTreeNode,\n            MatTreeNodeDef,\n            MatTreeNodePadding,\n            MatTreeNodeToggle,\n            MatTree,\n            MatTreeNode,\n            MatTreeNodeOutlet] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeModule, imports: [CdkTreeModule, MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTreeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkTreeModule, MatCommonModule, ...MAT_TREE_DIRECTIVES],\n                    exports: [MatCommonModule, MAT_TREE_DIRECTIVES],\n                }]\n        }] });\n\n/**\n * Tree flattener to convert a normal type of node to node with children & level information.\n * Transform nested nodes of type `T` to flattened nodes of type `F`.\n *\n * For example, the input data of type `T` is nested, and contains its children data:\n *   SomeNode: {\n *     key: 'Fruits',\n *     children: [\n *       NodeOne: {\n *         key: 'Apple',\n *       },\n *       NodeTwo: {\n *        key: 'Pear',\n *      }\n *    ]\n *  }\n *  After flattener flatten the tree, the structure will become\n *  SomeNode: {\n *    key: 'Fruits',\n *    expandable: true,\n *    level: 1\n *  },\n *  NodeOne: {\n *    key: 'Apple',\n *    expandable: false,\n *    level: 2\n *  },\n *  NodeTwo: {\n *   key: 'Pear',\n *   expandable: false,\n *   level: 2\n * }\n * and the output flattened type is `F` with additional information.\n *\n * @deprecated Use MatTree#childrenAccessor and MatTreeNode#isExpandable\n * instead. To be removed in a future version.\n * @breaking-change 21.0.0\n */\nclass MatTreeFlattener {\n    transformFunction;\n    getLevel;\n    isExpandable;\n    getChildren;\n    constructor(transformFunction, getLevel, isExpandable, getChildren) {\n        this.transformFunction = transformFunction;\n        this.getLevel = getLevel;\n        this.isExpandable = isExpandable;\n        this.getChildren = getChildren;\n    }\n    _flattenNode(node, level, resultNodes, parentMap) {\n        const flatNode = this.transformFunction(node, level);\n        resultNodes.push(flatNode);\n        if (this.isExpandable(flatNode)) {\n            const childrenNodes = this.getChildren(node);\n            if (childrenNodes) {\n                if (Array.isArray(childrenNodes)) {\n                    this._flattenChildren(childrenNodes, level, resultNodes, parentMap);\n                }\n                else {\n                    childrenNodes.pipe(take(1)).subscribe(children => {\n                        this._flattenChildren(children, level, resultNodes, parentMap);\n                    });\n                }\n            }\n        }\n        return resultNodes;\n    }\n    _flattenChildren(children, level, resultNodes, parentMap) {\n        children.forEach((child, index) => {\n            let childParentMap = parentMap.slice();\n            childParentMap.push(index != children.length - 1);\n            this._flattenNode(child, level + 1, resultNodes, childParentMap);\n        });\n    }\n    /**\n     * Flatten a list of node type T to flattened version of node F.\n     * Please note that type T may be nested, and the length of `structuredData` may be different\n     * from that of returned list `F[]`.\n     */\n    flattenNodes(structuredData) {\n        let resultNodes = [];\n        structuredData.forEach(node => this._flattenNode(node, 0, resultNodes, []));\n        return resultNodes;\n    }\n    /**\n     * Expand flattened node with current expansion status.\n     * The returned list may have different length.\n     */\n    expandFlattenedNodes(nodes, treeControl) {\n        let results = [];\n        let currentExpand = [];\n        currentExpand[0] = true;\n        nodes.forEach(node => {\n            let expand = true;\n            for (let i = 0; i <= this.getLevel(node); i++) {\n                expand = expand && currentExpand[i];\n            }\n            if (expand) {\n                results.push(node);\n            }\n            if (this.isExpandable(node)) {\n                currentExpand[this.getLevel(node) + 1] = treeControl.isExpanded(node);\n            }\n        });\n        return results;\n    }\n}\n/**\n * Data source for flat tree.\n * The data source need to handle expansion/collapsion of the tree node and change the data feed\n * to `MatTree`.\n * The nested tree nodes of type `T` are flattened through `MatTreeFlattener`, and converted\n * to type `F` for `MatTree` to consume.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nclass MatTreeFlatDataSource extends DataSource {\n    _treeControl;\n    _treeFlattener;\n    _flattenedData = new BehaviorSubject([]);\n    _expandedData = new BehaviorSubject([]);\n    get data() {\n        return this._data.value;\n    }\n    set data(value) {\n        this._data.next(value);\n        this._flattenedData.next(this._treeFlattener.flattenNodes(this.data));\n        this._treeControl.dataNodes = this._flattenedData.value;\n    }\n    _data = new BehaviorSubject([]);\n    constructor(_treeControl, _treeFlattener, initialData) {\n        super();\n        this._treeControl = _treeControl;\n        this._treeFlattener = _treeFlattener;\n        if (initialData) {\n            // Assign the data through the constructor to ensure that all of the logic is executed.\n            this.data = initialData;\n        }\n    }\n    connect(collectionViewer) {\n        return merge(collectionViewer.viewChange, this._treeControl.expansionModel.changed, this._flattenedData).pipe(map(() => {\n            this._expandedData.next(this._treeFlattener.expandFlattenedNodes(this._flattenedData.value, this._treeControl));\n            return this._expandedData.value;\n        }));\n    }\n    disconnect() {\n        // no op\n    }\n}\n\n/**\n * Data source for nested tree.\n *\n * The data source for nested tree doesn't have to consider node flattener, or the way to expand\n * or collapse. The expansion/collapsion will be handled by TreeControl and each non-leaf node.\n */\nclass MatTreeNestedDataSource extends DataSource {\n    /**\n     * Data for the nested tree\n     */\n    get data() {\n        return this._data.value;\n    }\n    set data(value) {\n        this._data.next(value);\n    }\n    _data = new BehaviorSubject([]);\n    connect(collectionViewer) {\n        return merge(...[collectionViewer.viewChange, this._data]).pipe(map(() => this.data));\n    }\n    disconnect() {\n        // no op\n    }\n}\n\nexport { MatNestedTreeNode, MatTree, MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule, MatTreeNestedDataSource, MatTreeNode, MatTreeNodeDef, MatTreeNodeOutlet, MatTreeNodePadding, MatTreeNodeToggle };\n//# sourceMappingURL=tree.mjs.map\n"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,yBAAyB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,mBAAmB;AAC/L,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC7M,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,eAAe,EAAEC,KAAK,QAAQ,MAAM;AAC7C,SAASC,IAAI,EAAEC,GAAG,QAAQ,gBAAgB;AAC1C,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;;AAE1B;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,UAAU,EAAE;EACtC,OAAO,CAAC,CAACA,UAAU,CAACC,qBAAqB;AAC7C;AACA;AACA;AACA;AACA,MAAMC,WAAW,SAAShC,WAAW,CAAC;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIiC,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACC,qBAAqB;EACrC;EACA,IAAID,oBAAoBA,CAACE,KAAK,EAAE;IAC5B;IACA,IAAI,CAACD,qBAAqB,GAAGC,KAAK;EACtC;EAWAC,qBAAqBA,CAAA,EAAG;IACpB,IAAIP,oBAAoB,CAAC,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,EAAE;MAC9C,OAAO,IAAI,CAACL,oBAAoB;IACpC;IACA,OAAO,IAAI,CAACM,SAAS;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,QAAQA,CAACL,KAAK,EAAE;IAChB,IAAI,CAACM,UAAU,GAAGN,KAAK;EAC3B;EACAO,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IAACC,eAAA;IA5BZ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IAPIA,eAAA,0BAQkB,CAAC;IAqBf,MAAMC,QAAQ,GAAGlC,MAAM,CAAC,IAAIC,kBAAkB,CAAC,UAAU,CAAC,EAAE;MAAEkC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC/E,IAAI,CAACZ,oBAAoB,GAAGa,MAAM,CAACF,QAAQ,CAAC,IAAI,IAAI,CAACG,eAAe;EACxE;EACA;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;EACpB;EACAC,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;EACvB;AAGJ;AAACC,YAAA,GA3DKlB,WAAW;AAAAW,eAAA,CAAXX,WAAW,wBAAAmB,qBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAyDsFpB,YAAW;AAAA;AAAAW,eAAA,CAzD5GX,WAAW,8BA4DgEvB,EAAE,CAAA4C,iBAAA;EAAAC,IAAA,EAFQtB,YAAW;EAAAuB,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAErBnD,EAAE,CAAAqD,UAAA,mBAAAC,sCAAA;QAAA,OAFQF,GAAA,CAAAG,UAAA,CAAW,CAAC;MAAA,CAAF,CAAC;IAAA;IAAA,IAAAJ,EAAA;MAErBnD,EAAE,CAAAwD,cAAA,aAFQJ,GAAA,CAAAzB,qBAAA,CAAsB,CAAZ,CAAC;MAErB3B,EAAE,CAAAyD,WAAA,kBAFQL,GAAA,CAAAM,gBAAA,CAAiB,CAAC,gBAAAN,GAAA,CAAAO,KAAA,GAAV,CAAC,mBAATP,GAAA,CAAAQ,iBAAA,CAAkB,CAAC,kBAAnBR,GAAA,CAAAS,WAAA,CAAY,CAAC;IAAA;EAAA;EAAAC,MAAA;IAAAtC,oBAAA,0CAAqHE,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGvB,eAAe,CAACuB,KAAK,CAAE;IAAAK,QAAA,8BAAsC3B,gBAAgB;EAAA;EAAA2D,OAAA;IAAAC,UAAA;IAAAC,cAAA;EAAA;EAAAC,QAAA;EAAAC,QAAA,GAExPnE,EAAE,CAAAoE,kBAAA,CAFwnB,CAAC;IAAEC,OAAO,EAAE9E,WAAW;IAAE+E,WAAW,EAAE/C;EAAY,CAAC,CAAC,GAE9qBvB,EAAE,CAAAuE,0BAAA;AAAA;AAAnF;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFxE,EAAE,CAAAyE,iBAAA,CAAQlD,WAAW,EAAc,CAAC;IACzGsB,IAAI,EAAExC,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBT,QAAQ,EAAE,aAAa;MACvBH,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;MACzCa,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE9E,WAAW;QAAE+E,WAAW,EAAE/C;MAAY,CAAC,CAAC;MAC/DsD,IAAI,EAAE;QACF,OAAO,EAAE,eAAe;QACxB,sBAAsB,EAAE,oBAAoB;QAC5C,mBAAmB,EAAE,WAAW;QAChC,sBAAsB,EAAE,qBAAqB;QAC7C,qBAAqB,EAAE,eAAe;QACtC,SAAS,EAAE,cAAc;QACzB,YAAY,EAAE;MAClB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAErD,oBAAoB,EAAE,CAAC;MAC/DqB,IAAI,EAAEvC,KAAK;MACXoE,IAAI,EAAE,CAAC;QACCI,SAAS,EAAGpD,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGvB,eAAe,CAACuB,KAAK,CAAE;QAClEqD,KAAK,EAAE;MACX,CAAC;IACT,CAAC,CAAC;IAAEhD,QAAQ,EAAE,CAAC;MACXc,IAAI,EAAEvC,KAAK;MACXoE,IAAI,EAAE,CAAC;QAAEI,SAAS,EAAE1E;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAM4E,cAAc,SAASxF,cAAc,CAAC;EAAAyC,YAAA,GAAAyC,IAAA;IAAA,SAAAA,IAAA;IAAAxC,eAAA;EAAA;AAI5C;AAAC+C,eAAA,GAJKD,cAAc;AAAA9C,eAAA,CAAd8C,cAAc;EAAA,IAAAE,4BAAA;EAAA,gBAAAC,wBAAAxC,iBAAA;IAAA,QAAAuC,4BAAA,KAAAA,4BAAA,GA/B6DlF,EAAE,CAAAoF,qBAAA,CAiCoBJ,eAAc,IAAArC,iBAAA,IAAdqC,eAAc;EAAA;AAAA;AAAA9C,eAAA,CAF/G8C,cAAc,8BA/B6DhF,EAAE,CAAA4C,iBAAA;EAAAC,IAAA,EAkCQmC,eAAc;EAAAlC,SAAA;EAAAgB,MAAA;IAAAuB,IAAA;IAAAC,IAAA;EAAA;EAAAnB,QAAA,GAlCxBnE,EAAE,CAAAoE,kBAAA,CAkCsK,CAAC;IAAEC,OAAO,EAAE7E,cAAc;IAAE8E,WAAW,EAAEU;EAAe,CAAC,CAAC,GAlClOhF,EAAE,CAAAuE,0BAAA;AAAA;AAoCnF;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApCiFxE,EAAE,CAAAyE,iBAAA,CAoCQO,cAAc,EAAc,CAAC;IAC5GnC,IAAI,EAAExC,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5Bb,MAAM,EAAE,CAAC;QAAEyB,IAAI,EAAE,MAAM;QAAER,KAAK,EAAE;MAAqB,CAAC,CAAC;MACvDH,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE7E,cAAc;QAAE8E,WAAW,EAAEU;MAAe,CAAC;IACxE,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEM,IAAI,EAAE,CAAC;MACrBzC,IAAI,EAAEvC,KAAK;MACXoE,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMc,iBAAiB,SAAS/F,iBAAiB,CAAC;EAAAwC,YAAA,GAAAyC,IAAA;IAAA,SAAAA,IAAA;IAAAxC,eAAA;IAAAA,eAAA;EAAA;EAE9C;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIH,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,QAAQA,CAACL,KAAK,EAAE;IAChB,IAAI,CAACM,UAAU,GAAGN,KAAK;EAC3B;EACA;EACA,IAAIS,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACH,UAAU,GAAG,CAAC,CAAC,GAAG,IAAI,CAACyD,SAAS;EAChD;EACA,IAAItD,QAAQA,CAACT,KAAK,EAAE;IAChB;IACA,IAAI,CAAC+D,SAAS,GAAG/D,KAAK;EAC1B;EAEA;EACA;EACA;EACAa,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;EACpB;EACAmD,kBAAkBA,CAAA,EAAG;IACjB,KAAK,CAACA,kBAAkB,CAAC,CAAC;EAC9B;EACAlD,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;EACvB;AAOJ;AAACmD,kBAAA,GAzCKH,iBAAiB;AAAAtD,eAAA,CAAjBsD,iBAAiB;EAAA,IAAAI,+BAAA;EAAA,gBAAAC,2BAAAlD,iBAAA;IAAA,QAAAiD,+BAAA,KAAAA,+BAAA,GAlD0D5F,EAAE,CAAAoF,qBAAA,CAqFoBI,kBAAiB,IAAA7C,iBAAA,IAAjB6C,kBAAiB;EAAA;AAAA;AAAAtD,eAAA,CAnClHsD,iBAAiB,8BAlD0DxF,EAAE,CAAA4C,iBAAA;EAAAC,IAAA,EAsFQ2C,kBAAiB;EAAA1C,SAAA;EAAAC,SAAA;EAAAe,MAAA;IAAAgC,IAAA;IAAA/D,QAAA,8BAA0I3B,gBAAgB;IAAA+B,QAAA,8BAAuCT,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGvB,eAAe,CAACuB,KAAK,CAAE;EAAA;EAAAqC,OAAA;IAAAC,UAAA;IAAAC,cAAA;EAAA;EAAAC,QAAA;EAAAC,QAAA,GAtFlRnE,EAAE,CAAAoE,kBAAA,CAsF2Z,CACle;IAAEC,OAAO,EAAE5E,iBAAiB;IAAE6E,WAAW,EAAEkB;EAAkB,CAAC,EAC9D;IAAEnB,OAAO,EAAE9E,WAAW;IAAE+E,WAAW,EAAEkB;EAAkB,CAAC,EACxD;IAAEnB,OAAO,EAAE3E,yBAAyB;IAAE4E,WAAW,EAAEkB;EAAkB,CAAC,CACzE,GA1FwExF,EAAE,CAAAuE,0BAAA;AAAA;AA4FnF;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5FiFxE,EAAE,CAAAyE,iBAAA,CA4FQe,iBAAiB,EAAc,CAAC;IAC/G3C,IAAI,EAAExC,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCT,QAAQ,EAAE,mBAAmB;MAC7BH,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;MACzCa,SAAS,EAAE,CACP;QAAEP,OAAO,EAAE5E,iBAAiB;QAAE6E,WAAW,EAAEkB;MAAkB,CAAC,EAC9D;QAAEnB,OAAO,EAAE9E,WAAW;QAAE+E,WAAW,EAAEkB;MAAkB,CAAC,EACxD;QAAEnB,OAAO,EAAE3E,yBAAyB;QAAE4E,WAAW,EAAEkB;MAAkB,CAAC,CACzE;MACDX,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEiB,IAAI,EAAE,CAAC;MACrBjD,IAAI,EAAEvC,KAAK;MACXoE,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE3C,QAAQ,EAAE,CAAC;MACXc,IAAI,EAAEvC,KAAK;MACXoE,IAAI,EAAE,CAAC;QAAEI,SAAS,EAAE1E;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+B,QAAQ,EAAE,CAAC;MACXU,IAAI,EAAEvC,KAAK;MACXoE,IAAI,EAAE,CAAC;QACCI,SAAS,EAAGpD,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGvB,eAAe,CAACuB,KAAK;MACpE,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMqE,kBAAkB,SAASpG,kBAAkB,CAAC;EAChD;EACA,IAAIgE,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACqC,MAAM;EACtB;EACA,IAAIrC,KAAKA,CAACjC,KAAK,EAAE;IACb,IAAI,CAACuE,cAAc,CAACvE,KAAK,CAAC;EAC9B;EACA;EACA,IAAIwE,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACE,eAAe,CAACF,MAAM,CAAC;EAChC;AAGJ;AAACG,mBAAA,GAjBKN,kBAAkB;AAAA7D,eAAA,CAAlB6D,kBAAkB;EAAA,IAAAO,gCAAA;EAAA,gBAAAC,4BAAA5D,iBAAA;IAAA,QAAA2D,gCAAA,KAAAA,gCAAA,GA3HyDtG,EAAE,CAAAoF,qBAAA,CA0IoBW,mBAAkB,IAAApD,iBAAA,IAAlBoD,mBAAkB;EAAA;AAAA;AAAA7D,eAAA,CAfnH6D,kBAAkB,8BA3HyD/F,EAAE,CAAA4C,iBAAA;EAAAC,IAAA,EA2IQkD,mBAAkB;EAAAjD,SAAA;EAAAgB,MAAA;IAAAH,KAAA,qCAAyGxD,eAAe;IAAA+F,MAAA;EAAA;EAAA/B,QAAA,GA3IpJnE,EAAE,CAAAoE,kBAAA,CA2IkN,CAAC;IAAEC,OAAO,EAAE1E,kBAAkB;IAAE2E,WAAW,EAAEyB;EAAmB,CAAC,CAAC,GA3ItR/F,EAAE,CAAAuE,0BAAA;AAAA;AA6InF;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7IiFxE,EAAE,CAAAyE,iBAAA,CA6IQsB,kBAAkB,EAAc,CAAC;IAChHlD,IAAI,EAAExC,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE1E,kBAAkB;QAAE2E,WAAW,EAAEyB;MAAmB,CAAC;IAChF,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEpC,KAAK,EAAE,CAAC;MACtBd,IAAI,EAAEvC,KAAK;MACXoE,IAAI,EAAE,CAAC;QAAEK,KAAK,EAAE,oBAAoB;QAAED,SAAS,EAAE3E;MAAgB,CAAC;IACtE,CAAC,CAAC;IAAE+F,MAAM,EAAE,CAAC;MACTrD,IAAI,EAAEvC,KAAK;MACXoE,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM8B,iBAAiB,CAAC;EAAAvE,YAAA;IAAAC,eAAA,wBACJjC,MAAM,CAACM,gBAAgB,CAAC;IAAA2B,eAAA,gBAChCjC,MAAM,CAACP,yBAAyB,EAAE;MAAE0C,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAA;AAQjE;AAACqE,kBAAA,GAVKD,iBAAiB;AAAAtE,eAAA,CAAjBsE,iBAAiB,wBAAAE,2BAAA/D,iBAAA;EAAA,YAAAA,iBAAA,IAGgF6D,kBAAiB;AAAA;AAAAtE,eAAA,CAHlHsE,iBAAiB,8BA/J0DxG,EAAE,CAAA4C,iBAAA;EAAAC,IAAA,EAmKQ2D,kBAAiB;EAAA1D,SAAA;EAAAqB,QAAA,GAnK3BnE,EAAE,CAAAoE,kBAAA,CAmK2F,CAClK;IACIC,OAAO,EAAEzE,iBAAiB;IAC1B0E,WAAW,EAAEkC;EACjB,CAAC,CACJ;AAAA;AAET;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KA1KiFxE,EAAE,CAAAyE,iBAAA,CA0KQ+B,iBAAiB,EAAc,CAAC;IAC/G3D,IAAI,EAAExC,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BC,SAAS,EAAE,CACP;QACIP,OAAO,EAAEzE,iBAAiB;QAC1B0E,WAAW,EAAEkC;MACjB,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMG,OAAO,SAAS9G,OAAO,CAAC;EAAAoC,YAAA,GAAAyC,IAAA;IAAA,SAAAA,IAAA;IAC1B;IACA;IAAAxC,eAAA,sBACc0E,SAAS;EAAA;AAG3B;AAACC,QAAA,GANKF,OAAO;AAAAzE,eAAA,CAAPyE,OAAO;EAAA,IAAAG,qBAAA;EAAA,gBAAAC,iBAAApE,iBAAA;IAAA,QAAAmE,qBAAA,KAAAA,qBAAA,GA1LoE9G,EAAE,CAAAoF,qBAAA,CA8LoBuB,QAAO,IAAAhE,iBAAA,IAAPgE,QAAO;EAAA;AAAA;AAAAzE,eAAA,CAJxGyE,OAAO,8BA1LoE3G,EAAE,CAAAgH,iBAAA;EAAAnE,IAAA,EA+LQ8D,QAAO;EAAA7D,SAAA;EAAAmE,SAAA,WAAAC,eAAA/D,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA/LjBnD,EAAE,CAAAmH,WAAA,CA+L+NX,iBAAiB;IAAA;IAAA,IAAArD,EAAA;MAAA,IAAAiE,EAAA;MA/LlPpH,EAAE,CAAAqH,cAAA,CAAAD,EAAA,GAAFpH,EAAE,CAAAsH,WAAA,QAAAlE,GAAA,CAAAmE,WAAA,GAAAH,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAAzE,SAAA;EAAAmB,QAAA;EAAAC,QAAA,GAAFnE,EAAE,CAAAoE,kBAAA,CA+L4G,CAAC;IAAEC,OAAO,EAAExE,OAAO;IAAEyE,WAAW,EAAEqC;EAAQ,CAAC,CAAC,GA/L1J3G,EAAE,CAAAuE,0BAAA;EAAAkD,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,kBAAA1E,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFnD,EAAE,CAAA8H,kBAAA,KA+L2Y,CAAC;IAAA;EAAA;EAAAC,YAAA,GAA0qBvB,iBAAiB;EAAAwB,MAAA;EAAAC,aAAA;AAAA;AAE1pC;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KAjMiFxE,EAAE,CAAAyE,iBAAA,CAiMQkC,OAAO,EAAc,CAAC;IACrG9D,IAAI,EAAErC,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAET,QAAQ,EAAE,SAAS;MAAE0D,QAAQ,EAAE,iDAAiD;MAAE/C,IAAI,EAAE;QAC3G,OAAO,EAAE;MACb,CAAC;MAAEoD,aAAa,EAAExH,iBAAiB,CAACyH,IAAI;MAAEC,eAAe,EAAEzH,uBAAuB,CAAC0H,OAAO;MAAExD,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAExE,OAAO;QAAEyE,WAAW,EAAEqC;MAAQ,CAAC,CAAC;MAAE0B,OAAO,EAAE,CAAC7B,iBAAiB,CAAC;MAAEwB,MAAM,EAAE,CAAC,imBAAimB;IAAE,CAAC;EAC5yB,CAAC,CAAC,QAAkB;IAAET,WAAW,EAAE,CAAC;MAC5B1E,IAAI,EAAElC,SAAS;MACf+D,IAAI,EAAE,CAAC8B,iBAAiB,EAAE;QAAE8B,MAAM,EAAE;MAAK,CAAC;IAC9C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMC,iBAAiB,SAASzI,iBAAiB,CAAC;AAGjD0I,kBAAA,GAHKD,iBAAiB;AAAArG,eAAA,CAAjBqG,iBAAiB;EAAA,IAAAE,+BAAA;EAAA,gBAAAC,2BAAA/F,iBAAA;IAAA,QAAA8F,+BAAA,KAAAA,+BAAA,GA9M0DzI,EAAE,CAAAoF,qBAAA,CA+MoBmD,kBAAiB,IAAA5F,iBAAA,IAAjB4F,kBAAiB;EAAA;AAAA;AAAArG,eAAA,CADlHqG,iBAAiB,8BA9M0DvI,EAAE,CAAA4C,iBAAA;EAAAC,IAAA,EAgNQ0F,kBAAiB;EAAAzF,SAAA;EAAAgB,MAAA;IAAA6E,SAAA;EAAA;EAAAxE,QAAA,GAhN3BnE,EAAE,CAAAoE,kBAAA,CAgN+J,CAAC;IAAEC,OAAO,EAAEvE,iBAAiB;IAAEwE,WAAW,EAAEiE;EAAkB,CAAC,CAAC,GAhNjOvI,EAAE,CAAAuE,0BAAA;AAAA;AAkNnF;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlNiFxE,EAAE,CAAAyE,iBAAA,CAkNQ8D,iBAAiB,EAAc,CAAC;IAC/G1F,IAAI,EAAExC,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAEvE,iBAAiB;QAAEwE,WAAW,EAAEiE;MAAkB,CAAC,CAAC;MAC3EzE,MAAM,EAAE,CAAC;QAAEyB,IAAI,EAAE,WAAW;QAAER,KAAK,EAAE;MAA6B,CAAC;IACvE,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM6D,mBAAmB,GAAG,CACxBpD,iBAAiB,EACjBR,cAAc,EACde,kBAAkB,EAClBwC,iBAAiB,EACjB5B,OAAO,EACPpF,WAAW,EACXiF,iBAAiB,CACpB;AACD,MAAMqC,aAAa,CAAC;AAgBnBC,cAAA,GAhBKD,aAAa;AAAA3G,eAAA,CAAb2G,aAAa,wBAAAE,uBAAApG,iBAAA;EAAA,YAAAA,iBAAA,IACoFkG,cAAa;AAAA;AAAA3G,eAAA,CAD9G2G,aAAa,8BApO8D7I,EAAE,CAAAgJ,gBAAA;EAAAnG,IAAA,EAsOqBgG,cAAa;EAAAR,OAAA,GAAYtI,aAAa,EAAEe,eAAe,EAAE0E,iBAAiB,EACtKR,cAAc,EACde,kBAAkB,EAClBwC,iBAAiB,EACjB5B,OAAO,EACPpF,WAAW,EACXiF,iBAAiB;EAAAyC,OAAA,GAAanI,eAAe,EAAE0E,iBAAiB,EAChER,cAAc,EACde,kBAAkB,EAClBwC,iBAAiB,EACjB5B,OAAO,EACPpF,WAAW,EACXiF,iBAAiB;AAAA;AAAAtE,eAAA,CAdvB2G,aAAa,8BApO8D7I,EAAE,CAAAkJ,gBAAA;EAAAb,OAAA,GAmP8CtI,aAAa,EAAEe,eAAe,EAAEA,eAAe;AAAA;AAEhL;EAAA,QAAA0D,SAAA,oBAAAA,SAAA,KArPiFxE,EAAE,CAAAyE,iBAAA,CAqPQoE,aAAa,EAAc,CAAC;IAC3GhG,IAAI,EAAEjC,QAAQ;IACd8D,IAAI,EAAE,CAAC;MACC2D,OAAO,EAAE,CAACtI,aAAa,EAAEe,eAAe,EAAE,GAAG8H,mBAAmB,CAAC;MACjEK,OAAO,EAAE,CAACnI,eAAe,EAAE8H,mBAAmB;IAClD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,gBAAgB,CAAC;EAKnBlH,WAAWA,CAACmH,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,WAAW,EAAE;IAAArH,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAChE,IAAI,CAACkH,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACAC,YAAYA,CAAC1D,IAAI,EAAEnC,KAAK,EAAE8F,WAAW,EAAEC,SAAS,EAAE;IAC9C,MAAMC,QAAQ,GAAG,IAAI,CAACP,iBAAiB,CAACtD,IAAI,EAAEnC,KAAK,CAAC;IACpD8F,WAAW,CAACG,IAAI,CAACD,QAAQ,CAAC;IAC1B,IAAI,IAAI,CAACL,YAAY,CAACK,QAAQ,CAAC,EAAE;MAC7B,MAAME,aAAa,GAAG,IAAI,CAACN,WAAW,CAACzD,IAAI,CAAC;MAC5C,IAAI+D,aAAa,EAAE;QACf,IAAIC,KAAK,CAACC,OAAO,CAACF,aAAa,CAAC,EAAE;UAC9B,IAAI,CAACG,gBAAgB,CAACH,aAAa,EAAElG,KAAK,EAAE8F,WAAW,EAAEC,SAAS,CAAC;QACvE,CAAC,MACI;UACDG,aAAa,CAACI,IAAI,CAAC/I,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgJ,SAAS,CAACC,QAAQ,IAAI;YAC9C,IAAI,CAACH,gBAAgB,CAACG,QAAQ,EAAExG,KAAK,EAAE8F,WAAW,EAAEC,SAAS,CAAC;UAClE,CAAC,CAAC;QACN;MACJ;IACJ;IACA,OAAOD,WAAW;EACtB;EACAO,gBAAgBA,CAACG,QAAQ,EAAExG,KAAK,EAAE8F,WAAW,EAAEC,SAAS,EAAE;IACtDS,QAAQ,CAACC,OAAO,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;MAC/B,IAAIC,cAAc,GAAGb,SAAS,CAACc,KAAK,CAAC,CAAC;MACtCD,cAAc,CAACX,IAAI,CAACU,KAAK,IAAIH,QAAQ,CAACM,MAAM,GAAG,CAAC,CAAC;MACjD,IAAI,CAACjB,YAAY,CAACa,KAAK,EAAE1G,KAAK,GAAG,CAAC,EAAE8F,WAAW,EAAEc,cAAc,CAAC;IACpE,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIG,YAAYA,CAACC,cAAc,EAAE;IACzB,IAAIlB,WAAW,GAAG,EAAE;IACpBkB,cAAc,CAACP,OAAO,CAACtE,IAAI,IAAI,IAAI,CAAC0D,YAAY,CAAC1D,IAAI,EAAE,CAAC,EAAE2D,WAAW,EAAE,EAAE,CAAC,CAAC;IAC3E,OAAOA,WAAW;EACtB;EACA;AACJ;AACA;AACA;EACImB,oBAAoBA,CAACC,KAAK,EAAEC,WAAW,EAAE;IACrC,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,aAAa,GAAG,EAAE;IACtBA,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI;IACvBH,KAAK,CAACT,OAAO,CAACtE,IAAI,IAAI;MAClB,IAAImF,MAAM,GAAG,IAAI;MACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAAC7B,QAAQ,CAACvD,IAAI,CAAC,EAAEoF,CAAC,EAAE,EAAE;QAC3CD,MAAM,GAAGA,MAAM,IAAID,aAAa,CAACE,CAAC,CAAC;MACvC;MACA,IAAID,MAAM,EAAE;QACRF,OAAO,CAACnB,IAAI,CAAC9D,IAAI,CAAC;MACtB;MACA,IAAI,IAAI,CAACwD,YAAY,CAACxD,IAAI,CAAC,EAAE;QACzBkF,aAAa,CAAC,IAAI,CAAC3B,QAAQ,CAACvD,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGgF,WAAW,CAACK,UAAU,CAACrF,IAAI,CAAC;MACzE;IACJ,CAAC,CAAC;IACF,OAAOiF,OAAO;EAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,qBAAqB,SAASrK,UAAU,CAAC;EAK3C,IAAIuE,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC+F,KAAK,CAAC3J,KAAK;EAC3B;EACA,IAAI4D,IAAIA,CAAC5D,KAAK,EAAE;IACZ,IAAI,CAAC2J,KAAK,CAACC,IAAI,CAAC5J,KAAK,CAAC;IACtB,IAAI,CAAC6J,cAAc,CAACD,IAAI,CAAC,IAAI,CAACE,cAAc,CAACd,YAAY,CAAC,IAAI,CAACpF,IAAI,CAAC,CAAC;IACrE,IAAI,CAACmG,YAAY,CAACC,SAAS,GAAG,IAAI,CAACH,cAAc,CAAC7J,KAAK;EAC3D;EAEAO,WAAWA,CAACwJ,YAAY,EAAED,cAAc,EAAEG,WAAW,EAAE;IACnD,KAAK,CAAC,CAAC;IAACzJ,eAAA;IAAAA,eAAA;IAAAA,eAAA,yBAZK,IAAIlB,eAAe,CAAC,EAAE,CAAC;IAAAkB,eAAA,wBACxB,IAAIlB,eAAe,CAAC,EAAE,CAAC;IAAAkB,eAAA,gBAS/B,IAAIlB,eAAe,CAAC,EAAE,CAAC;IAG3B,IAAI,CAACyK,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAIG,WAAW,EAAE;MACb;MACA,IAAI,CAACrG,IAAI,GAAGqG,WAAW;IAC3B;EACJ;EACAC,OAAOA,CAACC,gBAAgB,EAAE;IACtB,OAAO5K,KAAK,CAAC4K,gBAAgB,CAACC,UAAU,EAAE,IAAI,CAACL,YAAY,CAACM,cAAc,CAACC,OAAO,EAAE,IAAI,CAACT,cAAc,CAAC,CAACtB,IAAI,CAAC9I,GAAG,CAAC,MAAM;MACpH,IAAI,CAAC8K,aAAa,CAACX,IAAI,CAAC,IAAI,CAACE,cAAc,CAACZ,oBAAoB,CAAC,IAAI,CAACW,cAAc,CAAC7J,KAAK,EAAE,IAAI,CAAC+J,YAAY,CAAC,CAAC;MAC/G,OAAO,IAAI,CAACQ,aAAa,CAACvK,KAAK;IACnC,CAAC,CAAC,CAAC;EACP;EACAwK,UAAUA,CAAA,EAAG;IACT;EAAA;AAER;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,SAASpL,UAAU,CAAC;EAAAkB,YAAA,GAAAyC,IAAA;IAAA,SAAAA,IAAA;IAAAxC,eAAA,gBAUrC,IAAIlB,eAAe,CAAC,EAAE,CAAC;EAAA;EAT/B;AACJ;AACA;EACI,IAAIsE,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC+F,KAAK,CAAC3J,KAAK;EAC3B;EACA,IAAI4D,IAAIA,CAAC5D,KAAK,EAAE;IACZ,IAAI,CAAC2J,KAAK,CAACC,IAAI,CAAC5J,KAAK,CAAC;EAC1B;EAEAkK,OAAOA,CAACC,gBAAgB,EAAE;IACtB,OAAO5K,KAAK,CAAC,GAAG,CAAC4K,gBAAgB,CAACC,UAAU,EAAE,IAAI,CAACT,KAAK,CAAC,CAAC,CAACpB,IAAI,CAAC9I,GAAG,CAAC,MAAM,IAAI,CAACmE,IAAI,CAAC,CAAC;EACzF;EACA4G,UAAUA,CAAA,EAAG;IACT;EAAA;AAER;AAEA,SAAS1G,iBAAiB,EAAEmB,OAAO,EAAEyE,qBAAqB,EAAEjC,gBAAgB,EAAEN,aAAa,EAAEsD,uBAAuB,EAAE5K,WAAW,EAAEyD,cAAc,EAAEwB,iBAAiB,EAAET,kBAAkB,EAAEwC,iBAAiB;AAC3M", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}