{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiGridComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-grid.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-grid.component.scss?ngResource\";\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { ChangeDetectorRef, Component, ElementRef, EventEmitter, Inject, InjectionToken, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { BehaviorSubject, merge, Subject } from 'rxjs';\nimport { debounceTime, skip, switchMap, take, takeUntil } from 'rxjs/operators';\nimport { DEFAULT_PAGE_SIZE } from '../services/settings/app-settings';\nimport { SettingsService } from '../services/settings/settings.service';\nimport { SwHubEntityService } from '../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwuiTopFilterDataService } from '../swui-schema-top-filter/top-filter-data.service';\nimport { WidgetRegistry } from './registry/registry';\nimport { SwuiGridDataService } from './services/grid-data.service';\nimport { SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';\nimport { SwuiGridDataSource } from './swui-grid.datasource';\nimport { SwDexieService } from '../services/sw-dexie/sw-dexie.service';\nexport const SWUI_GRID_SELECTION_TRANSFORMER_TOKEN = new InjectionToken('grid-selection-transformer');\nexport const SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN = new InjectionToken('grid-selection-row-available');\nlet SwuiGridComponent = (_SwuiGridComponent = class SwuiGridComponent {\n  get schema() {\n    return this._schema;\n  }\n  set schema(value) {\n    this._schema = [...value];\n    this.addActionsToSchema();\n    this.updateDisplayedColumns();\n  }\n  get data() {\n    return this._data;\n  }\n  set data(value) {\n    if (typeof value === 'undefined') {\n      return;\n    }\n    this._data = value;\n  }\n  constructor(registry, cdr, urlHandler, element, dexieService, filter, hubEntityService, settings, service, selectionTransformer, selectionRowAvailable) {\n    this.registry = registry;\n    this.cdr = cdr;\n    this.urlHandler = urlHandler;\n    this.element = element;\n    this.dexieService = dexieService;\n    this.filter = filter;\n    this.hubEntityService = hubEntityService;\n    this.settings = settings;\n    this.service = service;\n    this.selectionTransformer = selectionTransformer;\n    this.selectionRowAvailable = selectionRowAvailable;\n    this.selected = [];\n    this.rowActions = [];\n    this.rowActionsColumnTitle = 'COMPONENTS.GRID.GRID_ROW_ACTIONS';\n    this.rowActionsMenuIcon = 'more_horiz';\n    this.bulkActions = [];\n    this.ignorePlainLink = false;\n    this.pagination = true;\n    this.stickyHeader = false;\n    this.columnsManagement = false;\n    this.gridId = 'default-grid-id';\n    this.queryParamsAffectsPageSize = false;\n    this.ignoreQueryParams = false;\n    this.useHubEntity = false;\n    this.bulkSelectionOnly = false;\n    this.footer = false;\n    this.blindPaginator = false;\n    this.disableRefreshAction = false;\n    this.showTotalItems = true;\n    this.totalItemsTitle = 'COMPONENTS.GRID.ITEMS_FOUND';\n    this.sortDirection = '';\n    this.loading = false;\n    this.widgetActionEmitted = new EventEmitter();\n    // schema items which will be rendered in mat-table\n    this.columnDefSchema = [];\n    this.displayedColumns = [];\n    this.loading$ = new BehaviorSubject(true);\n    this.total = 0;\n    this.bulkActionsColumnName = 'bulk-actions-column';\n    this.rowActionsColumnName = 'row-actions-column';\n    this._schema = [];\n    this.destroyed$ = new Subject();\n    this.selectionPageCheckedOutdated = true;\n    this.selectionPageChecked = false;\n    this.dataSource = new SwuiGridDataSource(this.service, this.hubEntityService, this.filter, this.urlHandler);\n  }\n  get isEmpty() {\n    return !this.dataSource.data || this.dataSource.data.length === 0;\n  }\n  ngOnInit() {\n    this.dataSource.initDatasource(this.useHubEntity);\n    this.initSelection();\n    this.addActionsToSchema();\n    this.buildColumnDefSchema();\n    this.updateDisplayedColumns();\n    this.configureUrlHandler();\n    this.dataSource.total$.pipe(takeUntil(this.destroyed$)).subscribe(total => {\n      this.total = total;\n    });\n  }\n  ngAfterViewInit() {\n    this.setupPageSize();\n    this.addPaginatorToDataSource();\n    this.watchForLoading();\n    this.useQueryParamsData();\n    this.setFilterState();\n    this.attachSort();\n    if (!this.filter) {\n      this.dataSource.loadData();\n    }\n    this.hideTotal();\n    this.cdr.detectChanges();\n  }\n  ngOnChanges(changes) {\n    if ('data' in changes) {\n      this.dataSource.data = changes['data'].currentValue;\n      if (!changes['data'].isFirstChange()) {\n        if (this.paginator) {\n          this.paginator.firstPage();\n        }\n        this.dataSource.loadData();\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  isAllSelected() {\n    if (this.selectionPageCheckedOutdated && this.dataSource.data) {\n      this.selectionPageChecked = this.dataSource.data.filter(row => this.isRowAvailable(row)).every(row => this.selection.isSelected(this.getSelectionRow(row)));\n      this.selectionPageCheckedOutdated = false;\n    }\n    return this.selectionPageChecked;\n  }\n  // adds or removes to selection\n  // Please note that we can have multiple data arrays in selection model due to pagination of data\n  masterToggle() {\n    if (this.dataSource.data) {\n      const allSelected = this.isAllSelected();\n      this.dataSource.data.filter(row => this.isRowAvailable(row)).forEach(row => allSelected ? this.selection.deselect(this.getSelectionRow(row)) : this.selection.select(this.getSelectionRow(row)));\n    }\n  }\n  columnsChanged(visibilityData) {\n    this._schema.forEach(item => {\n      if (item.field in visibilityData) {\n        item.isListVisible = visibilityData[item.field].isListVisible;\n      }\n    });\n    this.updateDisplayedColumns();\n  }\n  onPageClick() {\n    this.showLoading();\n  }\n  getSelectionRow(row) {\n    let transformFn;\n    if (!!this.selectionTransformer) {\n      transformFn = this.selectionTransformer.transform;\n    }\n    return transformFn ? transformFn(row) : row;\n  }\n  isRowAvailable(row) {\n    let availableFn;\n    if (!!this.selectionRowAvailable) {\n      availableFn = this.selectionRowAvailable.available;\n    }\n    return availableFn ? availableFn(row) : true;\n  }\n  refreshData() {\n    this.dataSource.loadData();\n  }\n  attachSort() {\n    if (this.sort) {\n      this.dataSource.addSort(this.sort);\n      this.sort.sortChange.pipe(takeUntil(this.destroyed$)).subscribe(() => this.showLoading());\n    }\n  }\n  addPaginatorToDataSource() {\n    if (this.paginator && this.pageSize) {\n      this.paginator.pageSize = this.pageSize;\n      this.dataSource.addPaginator(this.paginator, this.blindPaginator);\n    }\n  }\n  /**\n   * Adds internal actions columns\n   * Needs to work in Columns Visibility with it\n   */\n  addActionsToSchema() {\n    // bulk action column is first by default\n    if (this.bulkActions && this.bulkActions.length) {\n      const bulkActionsSchemaItem = {\n        field: this.bulkActionsColumnName,\n        title: 'Bulk Actions',\n        isList: true,\n        isListVisible: true,\n        type: 'internal'\n      };\n      this.schema.unshift(bulkActionsSchemaItem);\n    }\n    // row actions column is last by default\n    if (this.rowActions && this.rowActions.length) {\n      const rowActionsSchemaItem = {\n        field: this.rowActionsColumnName,\n        title: this.rowActionsColumnTitle,\n        isList: true,\n        isListVisible: true,\n        type: 'internal'\n      };\n      this.schema.push(rowActionsSchemaItem);\n    }\n  }\n  /**\n   * Updates schema which is used in *ngFor for building column definitions in mat-table template\n   * We need to remove internal items such as row/bulk actions, because they are already presents in mat-table template\n   */\n  buildColumnDefSchema() {\n    this.columnDefSchema = this._schema.filter(({\n      field\n    }) => field !== this.rowActionsColumnName && field !== this.bulkActionsColumnName);\n  }\n  /**\n   * Updates column names which will be shown in mat-table\n   */\n  updateDisplayedColumns() {\n    this.displayedColumns = this._schema.filter(({\n      isListVisible\n    }) => isListVisible !== false).map(({\n      field\n    }) => field);\n  }\n  watchForLoading() {\n    const skipCount = this._data ? 0 : 1; // hotfix to prevent first empty data loading\n    this.dataSource.connect().pipe(skip(skipCount), takeUntil(this.destroyed$)).subscribe(() => this.hideLoading());\n    if (this.filter) {\n      this.filter.appliedFilter.pipe(takeUntil(this.destroyed$)).subscribe(() => this.showLoading());\n    }\n    if (this.hubEntityService) {\n      this.hubEntityService.entitySelected$.pipe(takeUntil(this.destroyed$)).subscribe(() => this.showLoading());\n    }\n  }\n  showLoading() {\n    this.loading$.next(true);\n  }\n  hideLoading() {\n    this.loading$.next(false);\n    this.hideTotal();\n  }\n  useQueryParamsData() {\n    if (this.ignoreQueryParams) {\n      return;\n    }\n    if (this.paginator) {\n      if (this.queryParamsAffectsPageSize) {\n        this.pageSize = this.urlHandler.fetchPageSize(this.pageSize);\n        this.paginator.pageSize = this.pageSize;\n      }\n      this.urlHandler.setParamsToPaginator(this.paginator);\n    }\n    if (this.sort) {\n      this.urlHandler.setParamsToSort(this.sort);\n    }\n    if (this.filter) {\n      const filterParams = this.urlHandler.getFilterQueryParams();\n      if (filterParams.hasOwnProperty('path')) {\n        delete filterParams['path'];\n      }\n      if (Object.keys(filterParams).length) {\n        this.dexieService.getFilterState(this.savedFilteredPageName).then(filterState => {\n          const state = Object.keys(filterState).reduce((res, key) => {\n            res[key] = null;\n            return res;\n          }, {});\n          this.filter.submitFilter(_objectSpread(_objectSpread({}, state || {}), filterParams || {}));\n        }).catch(() => this.filter.submitFilter(filterParams));\n        return;\n      }\n      if (!this.savedFilteredPageName) {\n        this.filter.submitFilter(filterParams);\n      } else {\n        this.dexieService.getFilterState(this.savedFilteredPageName).then(filterState => {\n          this.filter.submitFilter(_objectSpread(_objectSpread(_objectSpread({}, filterState || {}), this.savedFilteredPageParams || {}), filterParams || {}));\n        }).catch(() => this.filter.submitFilter(filterParams));\n      }\n    }\n  }\n  setFilterState() {\n    var _this$filter;\n    (_this$filter = this.filter) === null || _this$filter === void 0 || _this$filter.appliedFilter.pipe(take(1), switchMap(() => this.filter.filterFormState), debounceTime(500)).subscribe(filter => {\n      if (this.savedFilteredPageName) {\n        this.dexieService.putFilterState(this.savedFilteredPageName, filter).catch(e => console.log(e));\n      }\n    });\n  }\n  configureUrlHandler() {\n    this.urlHandler.setAllowQueryParamsUpdate(!this.ignoreQueryParams);\n  }\n  initSelection() {\n    this.selection = new SelectionModel(true, this.selected);\n    merge(this.selection.changed.asObservable(), this.dataSource.connect()).pipe(takeUntil(this.destroyed$)).subscribe(() => this.selectionPageCheckedOutdated = true);\n  }\n  setupPageSize() {\n    if (typeof this.pageSize === 'undefined') {\n      if (this.settings) {\n        this.subscribeForSettings();\n      } else {\n        this.pageSize = DEFAULT_PAGE_SIZE;\n      }\n    }\n  }\n  /**\n   * Subscribes for settings changes and updates specific fields\n   */\n  subscribeForSettings() {\n    this.settings.appSettings$.pipe(takeUntil(this.destroyed$)).subscribe(({\n      pageSize\n    }) => {\n      if (this.paginator) {\n        if (pageSize) {\n          this.overwritePageSize(pageSize);\n        } else {\n          this.addPaginatorToDataSource();\n        }\n      }\n    });\n  }\n  /**\n   * Changes page size for existing grid with different pageSize value\n   * (e.g. if we changed pageSize in settings dialog from 10 to 20)\n   * @param pageSize\n   */\n  overwritePageSize(pageSize) {\n    if (this.pageSize !== pageSize) {\n      this.pageSize = pageSize;\n      if (this.paginator) {\n        this.showLoading();\n        this.dataSource.changePageSize(pageSize);\n      }\n    }\n  }\n  hideTotal() {\n    if (!this.blindPaginator || !this.paginator || !this.dataSource.data) {\n      return;\n    }\n    const el = this.element.nativeElement.getElementsByClassName('mat-paginator-range-label');\n    if (el && el[0]) {\n      if (this.dataSource.data.length) {\n        const firstIndex = this.paginator.pageIndex * this.paginator.pageSize;\n        el[0].innerText = `${firstIndex + 1} - ${firstIndex + this.dataSource.data.length}`;\n      } else {\n        el[0].innerText = '0 - 0';\n      }\n    }\n  }\n}, _SwuiGridComponent.ctorParameters = () => [{\n  type: WidgetRegistry\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: SwuiGridUrlHandlerService\n}, {\n  type: ElementRef\n}, {\n  type: SwDexieService\n}, {\n  type: SwuiTopFilterDataService,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: SwHubEntityService,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: SettingsService,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: SwuiGridDataService,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [SwuiGridDataService]\n  }]\n}, {\n  type: undefined,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [SWUI_GRID_SELECTION_TRANSFORMER_TOKEN]\n  }]\n}, {\n  type: undefined,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN]\n  }]\n}], _SwuiGridComponent.propDecorators = {\n  schema: [{\n    type: Input\n  }],\n  data: [{\n    type: Input\n  }],\n  selected: [{\n    type: Input\n  }],\n  rowActions: [{\n    type: Input\n  }],\n  rowActionsColumnTitle: [{\n    type: Input\n  }],\n  rowActionsMenuIcon: [{\n    type: Input\n  }],\n  bulkActions: [{\n    type: Input\n  }],\n  ignorePlainLink: [{\n    type: Input\n  }],\n  pagination: [{\n    type: Input\n  }],\n  stickyHeader: [{\n    type: Input\n  }],\n  columnsManagement: [{\n    type: Input\n  }],\n  gridId: [{\n    type: Input\n  }],\n  queryParamsAffectsPageSize: [{\n    type: Input\n  }],\n  ignoreQueryParams: [{\n    type: Input\n  }],\n  useHubEntity: [{\n    type: Input\n  }],\n  bulkSelectionOnly: [{\n    type: Input\n  }],\n  footer: [{\n    type: Input\n  }],\n  blindPaginator: [{\n    type: Input\n  }],\n  disableRefreshAction: [{\n    type: Input\n  }],\n  showTotalItems: [{\n    type: Input\n  }],\n  totalItemsTitle: [{\n    type: Input\n  }],\n  sortActive: [{\n    type: Input\n  }],\n  sortDirection: [{\n    type: Input\n  }],\n  savedFilteredPageName: [{\n    type: Input\n  }],\n  savedFilteredPageParams: [{\n    type: Input\n  }],\n  loading: [{\n    type: Input\n  }],\n  pageSize: [{\n    type: Input\n  }],\n  widgetActionEmitted: [{\n    type: Output\n  }],\n  paginator: [{\n    type: ViewChild,\n    args: [MatPaginator]\n  }],\n  sort: [{\n    type: ViewChild,\n    args: [MatSort]\n  }]\n}, _SwuiGridComponent);\nSwuiGridComponent = __decorate([Component({\n  selector: 'lib-swui-grid',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiGridComponent);\nexport { SwuiGridComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "SelectionModel", "ChangeDetectorRef", "Component", "ElementRef", "EventEmitter", "Inject", "InjectionToken", "Input", "Optional", "Output", "ViewChild", "MatPaginator", "MatSort", "BehaviorSubject", "merge", "Subject", "debounceTime", "skip", "switchMap", "take", "takeUntil", "DEFAULT_PAGE_SIZE", "SettingsService", "SwHubEntityService", "SwuiTopFilterDataService", "WidgetRegistry", "SwuiGridDataService", "SwuiGridUrlHandlerService", "SwuiGridDataSource", "SwDexieService", "SWUI_GRID_SELECTION_TRANSFORMER_TOKEN", "SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN", "SwuiGridComponent", "_SwuiGridComponent", "schema", "_schema", "value", "addActionsToSchema", "updateDisplayedColumns", "data", "_data", "constructor", "registry", "cdr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "dexieService", "filter", "hubEntityService", "settings", "service", "selectionTransformer", "selectionRowAvailable", "selected", "rowActions", "rowActionsColumnTitle", "rowActionsMenuIcon", "bulkActions", "ignorePlainLink", "pagination", "<PERSON><PERSON><PERSON><PERSON>", "columnsManagement", "gridId", "queryParamsAffectsPageSize", "ignoreQueryParams", "useHubEntity", "bulkSelectionOnly", "footer", "blindPaginator", "disableRefreshAction", "showTotalItems", "totalItemsTitle", "sortDirection", "loading", "widgetActionEmitted", "columnDefSchema", "displayedColumns", "loading$", "total", "bulkActionsColumnName", "rowActionsColumnName", "destroyed$", "selectionPageCheckedOutdated", "selectionPageChecked", "dataSource", "isEmpty", "length", "ngOnInit", "initDatasource", "initSelection", "buildColumnDefSchema", "configure<PERSON><PERSON><PERSON><PERSON><PERSON>", "total$", "pipe", "subscribe", "ngAfterViewInit", "setupPageSize", "addPaginatorToDataSource", "watchForLoading", "useQueryParamsData", "setFilterState", "attachSort", "loadData", "hideTotal", "detectChanges", "ngOnChanges", "changes", "currentValue", "isFirstChange", "paginator", "firstPage", "ngOnDestroy", "next", "undefined", "complete", "isAllSelected", "row", "isRowAvailable", "every", "selection", "isSelected", "getSelectionRow", "masterToggle", "allSelected", "for<PERSON>ach", "deselect", "select", "columnsChanged", "visibilityData", "item", "field", "isListVisible", "onPageClick", "showLoading", "transformFn", "transform", "availableFn", "available", "refreshData", "sort", "addSort", "sortChange", "pageSize", "addPaginator", "bulkActionsSchemaItem", "title", "isList", "type", "unshift", "rowActionsSchemaItem", "push", "map", "skip<PERSON><PERSON>nt", "connect", "hideLoading", "appliedFilter", "entitySelected$", "fetchPageSize", "setParamsToPaginator", "setParamsToSort", "filterParams", "getFilterQueryParams", "hasOwnProperty", "Object", "keys", "getFilterState", "savedFilteredPageName", "then", "filterState", "state", "reduce", "res", "key", "submitFilter", "_objectSpread", "catch", "savedFilteredPageParams", "_this$filter", "filterFormState", "putFilterState", "e", "console", "log", "setAllowQueryParamsUpdate", "changed", "asObservable", "subscribeForSettings", "appSettings$", "overwritePageSize", "changePageSize", "el", "nativeElement", "getElementsByClassName", "firstIndex", "pageIndex", "innerText", "ctorParameters", "decorators", "args", "propDecorators", "sortActive", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-grid.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-grid.component.scss?ngResource\";\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { ChangeDetectorRef, Component, ElementRef, EventEmitter, Inject, InjectionToken, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { BehaviorSubject, merge, Subject } from 'rxjs';\nimport { debounceTime, skip, switchMap, take, takeUntil } from 'rxjs/operators';\nimport { DEFAULT_PAGE_SIZE } from '../services/settings/app-settings';\nimport { SettingsService } from '../services/settings/settings.service';\nimport { SwHubEntityService } from '../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwuiTopFilterDataService } from '../swui-schema-top-filter/top-filter-data.service';\nimport { WidgetRegistry } from './registry/registry';\nimport { SwuiGridDataService } from './services/grid-data.service';\nimport { SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';\nimport { SwuiGridDataSource } from './swui-grid.datasource';\nimport { SwDexieService } from '../services/sw-dexie/sw-dexie.service';\nexport const SWUI_GRID_SELECTION_TRANSFORMER_TOKEN = new InjectionToken('grid-selection-transformer');\nexport const SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN = new InjectionToken('grid-selection-row-available');\nlet SwuiGridComponent = class SwuiGridComponent {\n    get schema() {\n        return this._schema;\n    }\n    set schema(value) {\n        this._schema = [...value];\n        this.addActionsToSchema();\n        this.updateDisplayedColumns();\n    }\n    get data() {\n        return this._data;\n    }\n    set data(value) {\n        if (typeof value === 'undefined') {\n            return;\n        }\n        this._data = value;\n    }\n    constructor(registry, cdr, urlHandler, element, dexieService, filter, hubEntityService, settings, service, selectionTransformer, selectionRowAvailable) {\n        this.registry = registry;\n        this.cdr = cdr;\n        this.urlHandler = urlHandler;\n        this.element = element;\n        this.dexieService = dexieService;\n        this.filter = filter;\n        this.hubEntityService = hubEntityService;\n        this.settings = settings;\n        this.service = service;\n        this.selectionTransformer = selectionTransformer;\n        this.selectionRowAvailable = selectionRowAvailable;\n        this.selected = [];\n        this.rowActions = [];\n        this.rowActionsColumnTitle = 'COMPONENTS.GRID.GRID_ROW_ACTIONS';\n        this.rowActionsMenuIcon = 'more_horiz';\n        this.bulkActions = [];\n        this.ignorePlainLink = false;\n        this.pagination = true;\n        this.stickyHeader = false;\n        this.columnsManagement = false;\n        this.gridId = 'default-grid-id';\n        this.queryParamsAffectsPageSize = false;\n        this.ignoreQueryParams = false;\n        this.useHubEntity = false;\n        this.bulkSelectionOnly = false;\n        this.footer = false;\n        this.blindPaginator = false;\n        this.disableRefreshAction = false;\n        this.showTotalItems = true;\n        this.totalItemsTitle = 'COMPONENTS.GRID.ITEMS_FOUND';\n        this.sortDirection = '';\n        this.loading = false;\n        this.widgetActionEmitted = new EventEmitter();\n        // schema items which will be rendered in mat-table\n        this.columnDefSchema = [];\n        this.displayedColumns = [];\n        this.loading$ = new BehaviorSubject(true);\n        this.total = 0;\n        this.bulkActionsColumnName = 'bulk-actions-column';\n        this.rowActionsColumnName = 'row-actions-column';\n        this._schema = [];\n        this.destroyed$ = new Subject();\n        this.selectionPageCheckedOutdated = true;\n        this.selectionPageChecked = false;\n        this.dataSource = new SwuiGridDataSource(this.service, this.hubEntityService, this.filter, this.urlHandler);\n    }\n    get isEmpty() {\n        return !this.dataSource.data || this.dataSource.data.length === 0;\n    }\n    ngOnInit() {\n        this.dataSource.initDatasource(this.useHubEntity);\n        this.initSelection();\n        this.addActionsToSchema();\n        this.buildColumnDefSchema();\n        this.updateDisplayedColumns();\n        this.configureUrlHandler();\n        this.dataSource.total$.pipe(takeUntil(this.destroyed$)).subscribe(total => {\n            this.total = total;\n        });\n    }\n    ngAfterViewInit() {\n        this.setupPageSize();\n        this.addPaginatorToDataSource();\n        this.watchForLoading();\n        this.useQueryParamsData();\n        this.setFilterState();\n        this.attachSort();\n        if (!this.filter) {\n            this.dataSource.loadData();\n        }\n        this.hideTotal();\n        this.cdr.detectChanges();\n    }\n    ngOnChanges(changes) {\n        if ('data' in changes) {\n            this.dataSource.data = changes['data'].currentValue;\n            if (!changes['data'].isFirstChange()) {\n                if (this.paginator) {\n                    this.paginator.firstPage();\n                }\n                this.dataSource.loadData();\n            }\n        }\n    }\n    ngOnDestroy() {\n        this.destroyed$.next(undefined);\n        this.destroyed$.complete();\n    }\n    isAllSelected() {\n        if (this.selectionPageCheckedOutdated && this.dataSource.data) {\n            this.selectionPageChecked = this.dataSource.data\n                .filter((row) => this.isRowAvailable(row))\n                .every((row) => this.selection.isSelected(this.getSelectionRow(row)));\n            this.selectionPageCheckedOutdated = false;\n        }\n        return this.selectionPageChecked;\n    }\n    // adds or removes to selection\n    // Please note that we can have multiple data arrays in selection model due to pagination of data\n    masterToggle() {\n        if (this.dataSource.data) {\n            const allSelected = this.isAllSelected();\n            this.dataSource.data\n                .filter((row) => this.isRowAvailable(row))\n                .forEach((row) => allSelected\n                ? this.selection.deselect(this.getSelectionRow(row))\n                : this.selection.select(this.getSelectionRow(row)));\n        }\n    }\n    columnsChanged(visibilityData) {\n        this._schema.forEach(item => {\n            if (item.field in visibilityData) {\n                item.isListVisible = visibilityData[item.field].isListVisible;\n            }\n        });\n        this.updateDisplayedColumns();\n    }\n    onPageClick() {\n        this.showLoading();\n    }\n    getSelectionRow(row) {\n        let transformFn;\n        if (!!this.selectionTransformer) {\n            transformFn = this.selectionTransformer.transform;\n        }\n        return transformFn ? transformFn(row) : row;\n    }\n    isRowAvailable(row) {\n        let availableFn;\n        if (!!this.selectionRowAvailable) {\n            availableFn = this.selectionRowAvailable.available;\n        }\n        return availableFn ? availableFn(row) : true;\n    }\n    refreshData() {\n        this.dataSource.loadData();\n    }\n    attachSort() {\n        if (this.sort) {\n            this.dataSource.addSort(this.sort);\n            this.sort.sortChange\n                .pipe(takeUntil(this.destroyed$))\n                .subscribe(() => this.showLoading());\n        }\n    }\n    addPaginatorToDataSource() {\n        if (this.paginator && this.pageSize) {\n            this.paginator.pageSize = this.pageSize;\n            this.dataSource.addPaginator(this.paginator, this.blindPaginator);\n        }\n    }\n    /**\n     * Adds internal actions columns\n     * Needs to work in Columns Visibility with it\n     */\n    addActionsToSchema() {\n        // bulk action column is first by default\n        if (this.bulkActions && this.bulkActions.length) {\n            const bulkActionsSchemaItem = {\n                field: this.bulkActionsColumnName,\n                title: 'Bulk Actions',\n                isList: true,\n                isListVisible: true,\n                type: 'internal'\n            };\n            this.schema.unshift(bulkActionsSchemaItem);\n        }\n        // row actions column is last by default\n        if (this.rowActions && this.rowActions.length) {\n            const rowActionsSchemaItem = {\n                field: this.rowActionsColumnName,\n                title: this.rowActionsColumnTitle,\n                isList: true,\n                isListVisible: true,\n                type: 'internal'\n            };\n            this.schema.push(rowActionsSchemaItem);\n        }\n    }\n    /**\n     * Updates schema which is used in *ngFor for building column definitions in mat-table template\n     * We need to remove internal items such as row/bulk actions, because they are already presents in mat-table template\n     */\n    buildColumnDefSchema() {\n        this.columnDefSchema = this._schema\n            .filter(({ field }) => field !== this.rowActionsColumnName && field !== this.bulkActionsColumnName);\n    }\n    /**\n     * Updates column names which will be shown in mat-table\n     */\n    updateDisplayedColumns() {\n        this.displayedColumns = this._schema\n            .filter(({ isListVisible }) => isListVisible !== false)\n            .map(({ field }) => field);\n    }\n    watchForLoading() {\n        const skipCount = this._data ? 0 : 1; // hotfix to prevent first empty data loading\n        this.dataSource.connect()\n            .pipe(skip(skipCount), takeUntil(this.destroyed$)).subscribe(() => this.hideLoading());\n        if (this.filter) {\n            this.filter.appliedFilter\n                .pipe(takeUntil(this.destroyed$))\n                .subscribe(() => this.showLoading());\n        }\n        if (this.hubEntityService) {\n            this.hubEntityService.entitySelected$\n                .pipe(takeUntil(this.destroyed$))\n                .subscribe(() => this.showLoading());\n        }\n    }\n    showLoading() {\n        this.loading$.next(true);\n    }\n    hideLoading() {\n        this.loading$.next(false);\n        this.hideTotal();\n    }\n    useQueryParamsData() {\n        if (this.ignoreQueryParams) {\n            return;\n        }\n        if (this.paginator) {\n            if (this.queryParamsAffectsPageSize) {\n                this.pageSize = this.urlHandler.fetchPageSize(this.pageSize);\n                this.paginator.pageSize = this.pageSize;\n            }\n            this.urlHandler.setParamsToPaginator(this.paginator);\n        }\n        if (this.sort) {\n            this.urlHandler.setParamsToSort(this.sort);\n        }\n        if (this.filter) {\n            const filterParams = this.urlHandler.getFilterQueryParams();\n            if (filterParams.hasOwnProperty('path')) {\n                delete filterParams['path'];\n            }\n            if (Object.keys(filterParams).length) {\n                this.dexieService.getFilterState(this.savedFilteredPageName)\n                    .then(filterState => {\n                    const state = Object.keys(filterState).reduce((res, key) => {\n                        res[key] = null;\n                        return res;\n                    }, {});\n                    this.filter.submitFilter({ ...(state || {}), ...(filterParams || {}) });\n                })\n                    .catch(() => this.filter.submitFilter(filterParams));\n                return;\n            }\n            if (!this.savedFilteredPageName) {\n                this.filter.submitFilter(filterParams);\n            }\n            else {\n                this.dexieService.getFilterState(this.savedFilteredPageName)\n                    .then(filterState => {\n                    this.filter.submitFilter({ ...(filterState || {}), ...(this.savedFilteredPageParams || {}), ...(filterParams || {}) });\n                })\n                    .catch(() => this.filter.submitFilter(filterParams));\n            }\n        }\n    }\n    setFilterState() {\n        this.filter?.appliedFilter\n            .pipe(take(1), switchMap(() => this.filter.filterFormState), debounceTime(500))\n            .subscribe(filter => {\n            if (this.savedFilteredPageName) {\n                this.dexieService.putFilterState(this.savedFilteredPageName, filter)\n                    .catch((e) => console.log(e));\n            }\n        });\n    }\n    configureUrlHandler() {\n        this.urlHandler.setAllowQueryParamsUpdate(!this.ignoreQueryParams);\n    }\n    initSelection() {\n        this.selection = new SelectionModel(true, this.selected);\n        merge(this.selection.changed.asObservable(), this.dataSource.connect())\n            .pipe(takeUntil(this.destroyed$))\n            .subscribe(() => this.selectionPageCheckedOutdated = true);\n    }\n    setupPageSize() {\n        if (typeof this.pageSize === 'undefined') {\n            if (this.settings) {\n                this.subscribeForSettings();\n            }\n            else {\n                this.pageSize = DEFAULT_PAGE_SIZE;\n            }\n        }\n    }\n    /**\n     * Subscribes for settings changes and updates specific fields\n     */\n    subscribeForSettings() {\n        this.settings.appSettings$\n            .pipe(takeUntil(this.destroyed$))\n            .subscribe(({ pageSize }) => {\n            if (this.paginator) {\n                if (pageSize) {\n                    this.overwritePageSize(pageSize);\n                }\n                else {\n                    this.addPaginatorToDataSource();\n                }\n            }\n        });\n    }\n    /**\n     * Changes page size for existing grid with different pageSize value\n     * (e.g. if we changed pageSize in settings dialog from 10 to 20)\n     * @param pageSize\n     */\n    overwritePageSize(pageSize) {\n        if (this.pageSize !== pageSize) {\n            this.pageSize = pageSize;\n            if (this.paginator) {\n                this.showLoading();\n                this.dataSource.changePageSize(pageSize);\n            }\n        }\n    }\n    hideTotal() {\n        if (!this.blindPaginator || !this.paginator || !this.dataSource.data) {\n            return;\n        }\n        const el = this.element.nativeElement.getElementsByClassName('mat-paginator-range-label');\n        if (el && el[0]) {\n            if (this.dataSource.data.length) {\n                const firstIndex = this.paginator.pageIndex * this.paginator.pageSize;\n                el[0].innerText = `${firstIndex + 1} - ${firstIndex + this.dataSource.data.length}`;\n            }\n            else {\n                el[0].innerText = '0 - 0';\n            }\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: WidgetRegistry },\n        { type: ChangeDetectorRef },\n        { type: SwuiGridUrlHandlerService },\n        { type: ElementRef },\n        { type: SwDexieService },\n        { type: SwuiTopFilterDataService, decorators: [{ type: Optional }] },\n        { type: SwHubEntityService, decorators: [{ type: Optional }] },\n        { type: SettingsService, decorators: [{ type: Optional }] },\n        { type: SwuiGridDataService, decorators: [{ type: Optional }, { type: Inject, args: [SwuiGridDataService,] }] },\n        { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [SWUI_GRID_SELECTION_TRANSFORMER_TOKEN,] }] },\n        { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN,] }] }\n    ]; }\n    static { this.propDecorators = {\n        schema: [{ type: Input }],\n        data: [{ type: Input }],\n        selected: [{ type: Input }],\n        rowActions: [{ type: Input }],\n        rowActionsColumnTitle: [{ type: Input }],\n        rowActionsMenuIcon: [{ type: Input }],\n        bulkActions: [{ type: Input }],\n        ignorePlainLink: [{ type: Input }],\n        pagination: [{ type: Input }],\n        stickyHeader: [{ type: Input }],\n        columnsManagement: [{ type: Input }],\n        gridId: [{ type: Input }],\n        queryParamsAffectsPageSize: [{ type: Input }],\n        ignoreQueryParams: [{ type: Input }],\n        useHubEntity: [{ type: Input }],\n        bulkSelectionOnly: [{ type: Input }],\n        footer: [{ type: Input }],\n        blindPaginator: [{ type: Input }],\n        disableRefreshAction: [{ type: Input }],\n        showTotalItems: [{ type: Input }],\n        totalItemsTitle: [{ type: Input }],\n        sortActive: [{ type: Input }],\n        sortDirection: [{ type: Input }],\n        savedFilteredPageName: [{ type: Input }],\n        savedFilteredPageParams: [{ type: Input }],\n        loading: [{ type: Input }],\n        pageSize: [{ type: Input }],\n        widgetActionEmitted: [{ type: Output }],\n        paginator: [{ type: ViewChild, args: [MatPaginator,] }],\n        sort: [{ type: ViewChild, args: [MatSort,] }]\n    }; }\n};\nSwuiGridComponent = __decorate([\n    Component({\n        selector: 'lib-swui-grid',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiGridComponent);\nexport { SwuiGridComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,YAAY,EAAEC,MAAM,EAAEC,cAAc,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAClJ,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,eAAe,EAAEC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AACtD,SAASC,YAAY,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAC/E,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,eAAe,QAAQ,uCAAuC;AACvE,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,wBAAwB,QAAQ,mDAAmD;AAC5F,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,uCAAuC;AACtE,OAAO,MAAMC,qCAAqC,GAAG,IAAIxB,cAAc,CAAC,4BAA4B,CAAC;AACrG,OAAO,MAAMyB,uCAAuC,GAAG,IAAIzB,cAAc,CAAC,8BAA8B,CAAC;AACzG,IAAI0B,iBAAiB,IAAAC,kBAAA,GAAG,MAAMD,iBAAiB,CAAC;EAC5C,IAAIE,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAI,CAACD,OAAO,GAAG,CAAC,GAAGC,KAAK,CAAC;IACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,sBAAsB,CAAC,CAAC;EACjC;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACH,KAAK,EAAE;IACZ,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MAC9B;IACJ;IACA,IAAI,CAACI,KAAK,GAAGJ,KAAK;EACtB;EACAK,WAAWA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,YAAY,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAE;IACpJ,IAAI,CAACV,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,qBAAqB,GAAG,kCAAkC;IAC/D,IAAI,CAACC,kBAAkB,GAAG,YAAY;IACtC,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,MAAM,GAAG,iBAAiB;IAC/B,IAAI,CAACC,0BAA0B,GAAG,KAAK;IACvC,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,eAAe,GAAG,6BAA6B;IACpD,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,mBAAmB,GAAG,IAAItE,YAAY,CAAC,CAAC;IAC7C;IACA,IAAI,CAACuE,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,QAAQ,GAAG,IAAIhE,eAAe,CAAC,IAAI,CAAC;IACzC,IAAI,CAACiE,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;IAClD,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;IAChD,IAAI,CAAC7C,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC8C,UAAU,GAAG,IAAIlE,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACmE,4BAA4B,GAAG,IAAI;IACxC,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,UAAU,GAAG,IAAIxD,kBAAkB,CAAC,IAAI,CAACsB,OAAO,EAAE,IAAI,CAACF,gBAAgB,EAAE,IAAI,CAACD,MAAM,EAAE,IAAI,CAACH,UAAU,CAAC;EAC/G;EACA,IAAIyC,OAAOA,CAAA,EAAG;IACV,OAAO,CAAC,IAAI,CAACD,UAAU,CAAC7C,IAAI,IAAI,IAAI,CAAC6C,UAAU,CAAC7C,IAAI,CAAC+C,MAAM,KAAK,CAAC;EACrE;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACH,UAAU,CAACI,cAAc,CAAC,IAAI,CAACvB,YAAY,CAAC;IACjD,IAAI,CAACwB,aAAa,CAAC,CAAC;IACpB,IAAI,CAACpD,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACqD,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACpD,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACqD,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACP,UAAU,CAACQ,MAAM,CAACC,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAACa,SAAS,CAAChB,KAAK,IAAI;MACvE,IAAI,CAACA,KAAK,GAAGA,KAAK;IACtB,CAAC,CAAC;EACN;EACAiB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAAC,IAAI,CAACtD,MAAM,EAAE;MACd,IAAI,CAACqC,UAAU,CAACkB,QAAQ,CAAC,CAAC;IAC9B;IACA,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,IAAI,CAAC5D,GAAG,CAAC6D,aAAa,CAAC,CAAC;EAC5B;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,MAAM,IAAIA,OAAO,EAAE;MACnB,IAAI,CAACtB,UAAU,CAAC7C,IAAI,GAAGmE,OAAO,CAAC,MAAM,CAAC,CAACC,YAAY;MACnD,IAAI,CAACD,OAAO,CAAC,MAAM,CAAC,CAACE,aAAa,CAAC,CAAC,EAAE;QAClC,IAAI,IAAI,CAACC,SAAS,EAAE;UAChB,IAAI,CAACA,SAAS,CAACC,SAAS,CAAC,CAAC;QAC9B;QACA,IAAI,CAAC1B,UAAU,CAACkB,QAAQ,CAAC,CAAC;MAC9B;IACJ;EACJ;EACAS,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9B,UAAU,CAAC+B,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAAChC,UAAU,CAACiC,QAAQ,CAAC,CAAC;EAC9B;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACjC,4BAA4B,IAAI,IAAI,CAACE,UAAU,CAAC7C,IAAI,EAAE;MAC3D,IAAI,CAAC4C,oBAAoB,GAAG,IAAI,CAACC,UAAU,CAAC7C,IAAI,CAC3CQ,MAAM,CAAEqE,GAAG,IAAK,IAAI,CAACC,cAAc,CAACD,GAAG,CAAC,CAAC,CACzCE,KAAK,CAAEF,GAAG,IAAK,IAAI,CAACG,SAAS,CAACC,UAAU,CAAC,IAAI,CAACC,eAAe,CAACL,GAAG,CAAC,CAAC,CAAC;MACzE,IAAI,CAAClC,4BAA4B,GAAG,KAAK;IAC7C;IACA,OAAO,IAAI,CAACC,oBAAoB;EACpC;EACA;EACA;EACAuC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACtC,UAAU,CAAC7C,IAAI,EAAE;MACtB,MAAMoF,WAAW,GAAG,IAAI,CAACR,aAAa,CAAC,CAAC;MACxC,IAAI,CAAC/B,UAAU,CAAC7C,IAAI,CACfQ,MAAM,CAAEqE,GAAG,IAAK,IAAI,CAACC,cAAc,CAACD,GAAG,CAAC,CAAC,CACzCQ,OAAO,CAAER,GAAG,IAAKO,WAAW,GAC3B,IAAI,CAACJ,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACJ,eAAe,CAACL,GAAG,CAAC,CAAC,GAClD,IAAI,CAACG,SAAS,CAACO,MAAM,CAAC,IAAI,CAACL,eAAe,CAACL,GAAG,CAAC,CAAC,CAAC;IAC3D;EACJ;EACAW,cAAcA,CAACC,cAAc,EAAE;IAC3B,IAAI,CAAC7F,OAAO,CAACyF,OAAO,CAACK,IAAI,IAAI;MACzB,IAAIA,IAAI,CAACC,KAAK,IAAIF,cAAc,EAAE;QAC9BC,IAAI,CAACE,aAAa,GAAGH,cAAc,CAACC,IAAI,CAACC,KAAK,CAAC,CAACC,aAAa;MACjE;IACJ,CAAC,CAAC;IACF,IAAI,CAAC7F,sBAAsB,CAAC,CAAC;EACjC;EACA8F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;EACAZ,eAAeA,CAACL,GAAG,EAAE;IACjB,IAAIkB,WAAW;IACf,IAAI,CAAC,CAAC,IAAI,CAACnF,oBAAoB,EAAE;MAC7BmF,WAAW,GAAG,IAAI,CAACnF,oBAAoB,CAACoF,SAAS;IACrD;IACA,OAAOD,WAAW,GAAGA,WAAW,CAAClB,GAAG,CAAC,GAAGA,GAAG;EAC/C;EACAC,cAAcA,CAACD,GAAG,EAAE;IAChB,IAAIoB,WAAW;IACf,IAAI,CAAC,CAAC,IAAI,CAACpF,qBAAqB,EAAE;MAC9BoF,WAAW,GAAG,IAAI,CAACpF,qBAAqB,CAACqF,SAAS;IACtD;IACA,OAAOD,WAAW,GAAGA,WAAW,CAACpB,GAAG,CAAC,GAAG,IAAI;EAChD;EACAsB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACtD,UAAU,CAACkB,QAAQ,CAAC,CAAC;EAC9B;EACAD,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACsC,IAAI,EAAE;MACX,IAAI,CAACvD,UAAU,CAACwD,OAAO,CAAC,IAAI,CAACD,IAAI,CAAC;MAClC,IAAI,CAACA,IAAI,CAACE,UAAU,CACfhD,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAChCa,SAAS,CAAC,MAAM,IAAI,CAACuC,WAAW,CAAC,CAAC,CAAC;IAC5C;EACJ;EACApC,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACY,SAAS,IAAI,IAAI,CAACiC,QAAQ,EAAE;MACjC,IAAI,CAACjC,SAAS,CAACiC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACvC,IAAI,CAAC1D,UAAU,CAAC2D,YAAY,CAAC,IAAI,CAAClC,SAAS,EAAE,IAAI,CAACzC,cAAc,CAAC;IACrE;EACJ;EACA;AACJ;AACA;AACA;EACI/B,kBAAkBA,CAAA,EAAG;IACjB;IACA,IAAI,IAAI,CAACoB,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC6B,MAAM,EAAE;MAC7C,MAAM0D,qBAAqB,GAAG;QAC1Bd,KAAK,EAAE,IAAI,CAACnD,qBAAqB;QACjCkE,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,IAAI;QACZf,aAAa,EAAE,IAAI;QACnBgB,IAAI,EAAE;MACV,CAAC;MACD,IAAI,CAACjH,MAAM,CAACkH,OAAO,CAACJ,qBAAqB,CAAC;IAC9C;IACA;IACA,IAAI,IAAI,CAAC1F,UAAU,IAAI,IAAI,CAACA,UAAU,CAACgC,MAAM,EAAE;MAC3C,MAAM+D,oBAAoB,GAAG;QACzBnB,KAAK,EAAE,IAAI,CAAClD,oBAAoB;QAChCiE,KAAK,EAAE,IAAI,CAAC1F,qBAAqB;QACjC2F,MAAM,EAAE,IAAI;QACZf,aAAa,EAAE,IAAI;QACnBgB,IAAI,EAAE;MACV,CAAC;MACD,IAAI,CAACjH,MAAM,CAACoH,IAAI,CAACD,oBAAoB,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;EACI3D,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACf,eAAe,GAAG,IAAI,CAACxC,OAAO,CAC9BY,MAAM,CAAC,CAAC;MAAEmF;IAAM,CAAC,KAAKA,KAAK,KAAK,IAAI,CAAClD,oBAAoB,IAAIkD,KAAK,KAAK,IAAI,CAACnD,qBAAqB,CAAC;EAC3G;EACA;AACJ;AACA;EACIzC,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACsC,gBAAgB,GAAG,IAAI,CAACzC,OAAO,CAC/BY,MAAM,CAAC,CAAC;MAAEoF;IAAc,CAAC,KAAKA,aAAa,KAAK,KAAK,CAAC,CACtDoB,GAAG,CAAC,CAAC;MAAErB;IAAM,CAAC,KAAKA,KAAK,CAAC;EAClC;EACAhC,eAAeA,CAAA,EAAG;IACd,MAAMsD,SAAS,GAAG,IAAI,CAAChH,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACtC,IAAI,CAAC4C,UAAU,CAACqE,OAAO,CAAC,CAAC,CACpB5D,IAAI,CAAC5E,IAAI,CAACuI,SAAS,CAAC,EAAEpI,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAACa,SAAS,CAAC,MAAM,IAAI,CAAC4D,WAAW,CAAC,CAAC,CAAC;IAC1F,IAAI,IAAI,CAAC3G,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAAC4G,aAAa,CACpB9D,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAChCa,SAAS,CAAC,MAAM,IAAI,CAACuC,WAAW,CAAC,CAAC,CAAC;IAC5C;IACA,IAAI,IAAI,CAACrF,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAAC4G,eAAe,CAChC/D,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAChCa,SAAS,CAAC,MAAM,IAAI,CAACuC,WAAW,CAAC,CAAC,CAAC;IAC5C;EACJ;EACAA,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxD,QAAQ,CAACmC,IAAI,CAAC,IAAI,CAAC;EAC5B;EACA0C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC7E,QAAQ,CAACmC,IAAI,CAAC,KAAK,CAAC;IACzB,IAAI,CAACT,SAAS,CAAC,CAAC;EACpB;EACAJ,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACnC,iBAAiB,EAAE;MACxB;IACJ;IACA,IAAI,IAAI,CAAC6C,SAAS,EAAE;MAChB,IAAI,IAAI,CAAC9C,0BAA0B,EAAE;QACjC,IAAI,CAAC+E,QAAQ,GAAG,IAAI,CAAClG,UAAU,CAACiH,aAAa,CAAC,IAAI,CAACf,QAAQ,CAAC;QAC5D,IAAI,CAACjC,SAAS,CAACiC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC3C;MACA,IAAI,CAAClG,UAAU,CAACkH,oBAAoB,CAAC,IAAI,CAACjD,SAAS,CAAC;IACxD;IACA,IAAI,IAAI,CAAC8B,IAAI,EAAE;MACX,IAAI,CAAC/F,UAAU,CAACmH,eAAe,CAAC,IAAI,CAACpB,IAAI,CAAC;IAC9C;IACA,IAAI,IAAI,CAAC5F,MAAM,EAAE;MACb,MAAMiH,YAAY,GAAG,IAAI,CAACpH,UAAU,CAACqH,oBAAoB,CAAC,CAAC;MAC3D,IAAID,YAAY,CAACE,cAAc,CAAC,MAAM,CAAC,EAAE;QACrC,OAAOF,YAAY,CAAC,MAAM,CAAC;MAC/B;MACA,IAAIG,MAAM,CAACC,IAAI,CAACJ,YAAY,CAAC,CAAC1E,MAAM,EAAE;QAClC,IAAI,CAACxC,YAAY,CAACuH,cAAc,CAAC,IAAI,CAACC,qBAAqB,CAAC,CACvDC,IAAI,CAACC,WAAW,IAAI;UACrB,MAAMC,KAAK,GAAGN,MAAM,CAACC,IAAI,CAACI,WAAW,CAAC,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;YACxDD,GAAG,CAACC,GAAG,CAAC,GAAG,IAAI;YACf,OAAOD,GAAG;UACd,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,IAAI,CAAC5H,MAAM,CAAC8H,YAAY,CAAAC,aAAA,CAAAA,aAAA,KAAOL,KAAK,IAAI,CAAC,CAAC,GAAOT,YAAY,IAAI,CAAC,CAAC,CAAG,CAAC;QAC3E,CAAC,CAAC,CACGe,KAAK,CAAC,MAAM,IAAI,CAAChI,MAAM,CAAC8H,YAAY,CAACb,YAAY,CAAC,CAAC;QACxD;MACJ;MACA,IAAI,CAAC,IAAI,CAACM,qBAAqB,EAAE;QAC7B,IAAI,CAACvH,MAAM,CAAC8H,YAAY,CAACb,YAAY,CAAC;MAC1C,CAAC,MACI;QACD,IAAI,CAAClH,YAAY,CAACuH,cAAc,CAAC,IAAI,CAACC,qBAAqB,CAAC,CACvDC,IAAI,CAACC,WAAW,IAAI;UACrB,IAAI,CAACzH,MAAM,CAAC8H,YAAY,CAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAON,WAAW,IAAI,CAAC,CAAC,GAAO,IAAI,CAACQ,uBAAuB,IAAI,CAAC,CAAC,GAAOhB,YAAY,IAAI,CAAC,CAAC,CAAG,CAAC;QAC1H,CAAC,CAAC,CACGe,KAAK,CAAC,MAAM,IAAI,CAAChI,MAAM,CAAC8H,YAAY,CAACb,YAAY,CAAC,CAAC;MAC5D;IACJ;EACJ;EACA5D,cAAcA,CAAA,EAAG;IAAA,IAAA6E,YAAA;IACb,CAAAA,YAAA,OAAI,CAAClI,MAAM,cAAAkI,YAAA,eAAXA,YAAA,CAAatB,aAAa,CACrB9D,IAAI,CAAC1E,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,MAAM,IAAI,CAAC6B,MAAM,CAACmI,eAAe,CAAC,EAAElK,YAAY,CAAC,GAAG,CAAC,CAAC,CAC9E8E,SAAS,CAAC/C,MAAM,IAAI;MACrB,IAAI,IAAI,CAACuH,qBAAqB,EAAE;QAC5B,IAAI,CAACxH,YAAY,CAACqI,cAAc,CAAC,IAAI,CAACb,qBAAqB,EAAEvH,MAAM,CAAC,CAC/DgI,KAAK,CAAEK,CAAC,IAAKC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC,CAAC;MACrC;IACJ,CAAC,CAAC;EACN;EACAzF,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC/C,UAAU,CAAC2I,yBAAyB,CAAC,CAAC,IAAI,CAACvH,iBAAiB,CAAC;EACtE;EACAyB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC8B,SAAS,GAAG,IAAIvH,cAAc,CAAC,IAAI,EAAE,IAAI,CAACqD,QAAQ,CAAC;IACxDvC,KAAK,CAAC,IAAI,CAACyG,SAAS,CAACiE,OAAO,CAACC,YAAY,CAAC,CAAC,EAAE,IAAI,CAACrG,UAAU,CAACqE,OAAO,CAAC,CAAC,CAAC,CAClE5D,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAChCa,SAAS,CAAC,MAAM,IAAI,CAACZ,4BAA4B,GAAG,IAAI,CAAC;EAClE;EACAc,aAAaA,CAAA,EAAG;IACZ,IAAI,OAAO,IAAI,CAAC8C,QAAQ,KAAK,WAAW,EAAE;MACtC,IAAI,IAAI,CAAC7F,QAAQ,EAAE;QACf,IAAI,CAACyI,oBAAoB,CAAC,CAAC;MAC/B,CAAC,MACI;QACD,IAAI,CAAC5C,QAAQ,GAAGzH,iBAAiB;MACrC;IACJ;EACJ;EACA;AACJ;AACA;EACIqK,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACzI,QAAQ,CAAC0I,YAAY,CACrB9F,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAChCa,SAAS,CAAC,CAAC;MAAEgD;IAAS,CAAC,KAAK;MAC7B,IAAI,IAAI,CAACjC,SAAS,EAAE;QAChB,IAAIiC,QAAQ,EAAE;UACV,IAAI,CAAC8C,iBAAiB,CAAC9C,QAAQ,CAAC;QACpC,CAAC,MACI;UACD,IAAI,CAAC7C,wBAAwB,CAAC,CAAC;QACnC;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI2F,iBAAiBA,CAAC9C,QAAQ,EAAE;IACxB,IAAI,IAAI,CAACA,QAAQ,KAAKA,QAAQ,EAAE;MAC5B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,IAAI,CAACjC,SAAS,EAAE;QAChB,IAAI,CAACwB,WAAW,CAAC,CAAC;QAClB,IAAI,CAACjD,UAAU,CAACyG,cAAc,CAAC/C,QAAQ,CAAC;MAC5C;IACJ;EACJ;EACAvC,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACnC,cAAc,IAAI,CAAC,IAAI,CAACyC,SAAS,IAAI,CAAC,IAAI,CAACzB,UAAU,CAAC7C,IAAI,EAAE;MAClE;IACJ;IACA,MAAMuJ,EAAE,GAAG,IAAI,CAACjJ,OAAO,CAACkJ,aAAa,CAACC,sBAAsB,CAAC,2BAA2B,CAAC;IACzF,IAAIF,EAAE,IAAIA,EAAE,CAAC,CAAC,CAAC,EAAE;MACb,IAAI,IAAI,CAAC1G,UAAU,CAAC7C,IAAI,CAAC+C,MAAM,EAAE;QAC7B,MAAM2G,UAAU,GAAG,IAAI,CAACpF,SAAS,CAACqF,SAAS,GAAG,IAAI,CAACrF,SAAS,CAACiC,QAAQ;QACrEgD,EAAE,CAAC,CAAC,CAAC,CAACK,SAAS,GAAG,GAAGF,UAAU,GAAG,CAAC,MAAMA,UAAU,GAAG,IAAI,CAAC7G,UAAU,CAAC7C,IAAI,CAAC+C,MAAM,EAAE;MACvF,CAAC,MACI;QACDwG,EAAE,CAAC,CAAC,CAAC,CAACK,SAAS,GAAG,OAAO;MAC7B;IACJ;EACJ;AA8CJ,CAAC,EA7CYlK,kBAAA,CAAKmK,cAAc,GAAG,MAAM,CACjC;EAAEjD,IAAI,EAAE1H;AAAe,CAAC,EACxB;EAAE0H,IAAI,EAAElJ;AAAkB,CAAC,EAC3B;EAAEkJ,IAAI,EAAExH;AAA0B,CAAC,EACnC;EAAEwH,IAAI,EAAEhJ;AAAW,CAAC,EACpB;EAAEgJ,IAAI,EAAEtH;AAAe,CAAC,EACxB;EAAEsH,IAAI,EAAE3H,wBAAwB;EAAE6K,UAAU,EAAE,CAAC;IAAElD,IAAI,EAAE3I;EAAS,CAAC;AAAE,CAAC,EACpE;EAAE2I,IAAI,EAAE5H,kBAAkB;EAAE8K,UAAU,EAAE,CAAC;IAAElD,IAAI,EAAE3I;EAAS,CAAC;AAAE,CAAC,EAC9D;EAAE2I,IAAI,EAAE7H,eAAe;EAAE+K,UAAU,EAAE,CAAC;IAAElD,IAAI,EAAE3I;EAAS,CAAC;AAAE,CAAC,EAC3D;EAAE2I,IAAI,EAAEzH,mBAAmB;EAAE2K,UAAU,EAAE,CAAC;IAAElD,IAAI,EAAE3I;EAAS,CAAC,EAAE;IAAE2I,IAAI,EAAE9I,MAAM;IAAEiM,IAAI,EAAE,CAAC5K,mBAAmB;EAAG,CAAC;AAAE,CAAC,EAC/G;EAAEyH,IAAI,EAAElC,SAAS;EAAEoF,UAAU,EAAE,CAAC;IAAElD,IAAI,EAAE3I;EAAS,CAAC,EAAE;IAAE2I,IAAI,EAAE9I,MAAM;IAAEiM,IAAI,EAAE,CAACxK,qCAAqC;EAAG,CAAC;AAAE,CAAC,EACvH;EAAEqH,IAAI,EAAElC,SAAS;EAAEoF,UAAU,EAAE,CAAC;IAAElD,IAAI,EAAE3I;EAAS,CAAC,EAAE;IAAE2I,IAAI,EAAE9I,MAAM;IAAEiM,IAAI,EAAE,CAACvK,uCAAuC;EAAG,CAAC;AAAE,CAAC,CAC5H,EACQE,kBAAA,CAAKsK,cAAc,GAAG;EAC3BrK,MAAM,EAAE,CAAC;IAAEiH,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACzBgC,IAAI,EAAE,CAAC;IAAE4G,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACvB8C,QAAQ,EAAE,CAAC;IAAE8F,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC3B+C,UAAU,EAAE,CAAC;IAAE6F,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC7BgD,qBAAqB,EAAE,CAAC;IAAE4F,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACxCiD,kBAAkB,EAAE,CAAC;IAAE2F,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACrCkD,WAAW,EAAE,CAAC;IAAE0F,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC9BmD,eAAe,EAAE,CAAC;IAAEyF,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAClCoD,UAAU,EAAE,CAAC;IAAEwF,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC7BqD,YAAY,EAAE,CAAC;IAAEuF,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC/BsD,iBAAiB,EAAE,CAAC;IAAEsF,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACpCuD,MAAM,EAAE,CAAC;IAAEqF,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACzBwD,0BAA0B,EAAE,CAAC;IAAEoF,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC7CyD,iBAAiB,EAAE,CAAC;IAAEmF,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACpC0D,YAAY,EAAE,CAAC;IAAEkF,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC/B2D,iBAAiB,EAAE,CAAC;IAAEiF,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACpC4D,MAAM,EAAE,CAAC;IAAEgF,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACzB6D,cAAc,EAAE,CAAC;IAAE+E,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACjC8D,oBAAoB,EAAE,CAAC;IAAE8E,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACvC+D,cAAc,EAAE,CAAC;IAAE6E,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACjCgE,eAAe,EAAE,CAAC;IAAE4E,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAClCiM,UAAU,EAAE,CAAC;IAAErD,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC7BiE,aAAa,EAAE,CAAC;IAAE2E,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAChC+J,qBAAqB,EAAE,CAAC;IAAEnB,IAAI,EAAE5I;EAAM,CAAC,CAAC;EACxCyK,uBAAuB,EAAE,CAAC;IAAE7B,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC1CkE,OAAO,EAAE,CAAC;IAAE0E,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC1BuI,QAAQ,EAAE,CAAC;IAAEK,IAAI,EAAE5I;EAAM,CAAC,CAAC;EAC3BmE,mBAAmB,EAAE,CAAC;IAAEyE,IAAI,EAAE1I;EAAO,CAAC,CAAC;EACvCoG,SAAS,EAAE,CAAC;IAAEsC,IAAI,EAAEzI,SAAS;IAAE4L,IAAI,EAAE,CAAC3L,YAAY;EAAG,CAAC,CAAC;EACvDgI,IAAI,EAAE,CAAC;IAAEQ,IAAI,EAAEzI,SAAS;IAAE4L,IAAI,EAAE,CAAC1L,OAAO;EAAG,CAAC;AAChD,CAAC,EAAAqB,kBAAA,CACJ;AACDD,iBAAiB,GAAGnC,UAAU,CAAC,CAC3BK,SAAS,CAAC;EACNuM,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE5M,oBAAoB;EAC9B6M,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC7M,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEiC,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}