{"ast": null, "code": "var _SwuiDateRangeComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-date-range.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-date-range.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { ChangeDetectorRef, Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormBuilder, UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatMenu, MatMenuTrigger } from '@angular/material/menu';\nimport moment from 'moment';\nimport { BehaviorSubject, combineLatest } from 'rxjs';\nimport { distinctUntilChanged, map, takeUntil } from 'rxjs/operators';\nimport { SwuiDatePickerConfigModel } from '../swui-date-picker/swui-date-picker-config.model';\nimport { processInputString } from '../swui-date-picker/swui-date-picker.component';\nimport { CUSTOM_PERIODS, SwuiDateRangeModel } from './swui-date-range.model';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nconst CONTROL_NAME = 'lib-swui-date-range';\nlet nextUniqueId = 0;\nlet SwuiDateRangeComponent = (_SwuiDateRangeComponent = class SwuiDateRangeComponent extends SwuiMatFormFieldControl {\n  set customPeriods(customPeriods) {\n    if (customPeriods) {\n      this._customPeriods = customPeriods;\n    }\n  }\n  get customPeriods() {\n    return this._customPeriods;\n  }\n  set value(val) {\n    this.writeValue(val);\n  }\n  get value() {\n    return this._currentControlValue;\n  }\n  set config(val) {\n    this._config$.next(new SwuiDatePickerConfigModel(val));\n  }\n  get config() {\n    return this._config$.value;\n  }\n  get empty() {\n    return !this.valueControl.value;\n  }\n  get shouldLabelFloat() {\n    return !this.empty;\n  }\n  constructor(fm, elRef, cdr, ngControl, parentFormGroup, errorStateMatcher, fb) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.cdr = cdr;\n    this.minDate = '';\n    this.maxDate = '';\n    this.title = '';\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this.controlType = CONTROL_NAME;\n    this.valueControl = new UntypedFormControl('');\n    this.selectedIndex = 0;\n    this._customPeriods = CUSTOM_PERIODS;\n    this._value$ = new BehaviorSubject(undefined);\n    this._currentControlValue = {\n      from: '',\n      to: ''\n    };\n    this._sourceValue = new SwuiDateRangeModel();\n    this._config$ = new BehaviorSubject(new SwuiDatePickerConfigModel(undefined));\n    this.form = fb.group({\n      from: [],\n      to: []\n    });\n  }\n  ngOnInit() {\n    this.processedMinDate = this.processedMinDate || this.minDate;\n    this.processedMaxDate = this.processedMaxDate || this.maxDate;\n    this.fromControl.valueChanges.pipe(distinctUntilChanged(), takeUntil(this.destroyed$)).subscribe(from => {\n      const fromMoment = from && moment(from);\n      const maxPeriod = this.config.chooseStart ? this.config.maxPeriod && fromMoment && fromMoment.clone().add(1, this.config.maxPeriod).toISOString() : this.config.maxPeriod && fromMoment && fromMoment.clone().add(1, this.config.maxPeriod).subtract(1, 'seconds').toISOString();\n      this.processedMaxDate = this.maxDate ? maxPeriod && moment.min(maxPeriod, moment(this.maxDate)) || this.maxDate : maxPeriod;\n    });\n    this.toControl.valueChanges.pipe(distinctUntilChanged(), takeUntil(this.destroyed$)).subscribe(to => {\n      const toMoment = to && moment(to);\n      let daysOffset = null;\n      if (this.config.maxPeriod === 'month' && toMoment) {\n        const isLastDay = toMoment.daysInMonth() === toMoment.date();\n        let offset = 0;\n        const diff = toMoment.date() - toMoment.clone().subtract(1, this.config.maxPeriod).daysInMonth();\n        if (diff > 0) {\n          offset = diff;\n        }\n        daysOffset = isLastDay ? toMoment.clone().daysInMonth() : toMoment.clone().subtract(1, this.config.maxPeriod).daysInMonth() + offset;\n      }\n      const minMomentPeriod = this.config.chooseStart ? this.config.maxPeriod && toMoment && toMoment.clone() : this.config.maxPeriod && toMoment && toMoment.clone().add(1, 'second');\n      const minPeriod = daysOffset ? this.config.maxPeriod && toMoment && (minMomentPeriod === null || minMomentPeriod === void 0 ? void 0 : minMomentPeriod.subtract(daysOffset, 'day').toISOString()) : this.config.maxPeriod && toMoment && (minMomentPeriod === null || minMomentPeriod === void 0 ? void 0 : minMomentPeriod.subtract(1, this.config.maxPeriod).toISOString());\n      this.processedMinDate = this.minDate ? minPeriod && moment.max(minPeriod, moment(this.minDate)) || this.minDate : minPeriod;\n    });\n    combineLatest([this._value$, this._config$]).pipe(map(([, config]) => {\n      let offset = 0;\n      const val = this._currentControlValue;\n      if (this.oldConfig) {\n        if (this.oldConfig !== config && this.oldConfig.timeZone && this.oldConfig.timeZone !== config.timeZone) {\n          const oldOffset = Number(moment().tz(this.oldConfig.timeZone).utcOffset());\n          const currentOffset = Number(moment().tz(config.timeZone).utcOffset());\n          offset = oldOffset - currentOffset;\n        }\n      }\n      this.oldConfig = config;\n      const from = val && val.from && moment.utc(val.from).isValid() ? val.from : '';\n      const fromFormatted = from && moment(from).add(offset, 'minutes').utc().toISOString();\n      const to = val && val.to && moment.utc(val.to).isValid() ? val.to : '';\n      const toFormatted = to && moment(to).add(offset, 'minutes').milliseconds(0).utc().toISOString();\n      const processed = new SwuiDateRangeModel({\n        from: fromFormatted,\n        to: toFormatted\n      });\n      return {\n        processed,\n        config\n      };\n    }), takeUntil(this.destroyed$)).subscribe(({\n      processed,\n      config\n    }) => {\n      this._sourceValue = processed;\n      this._currentControlValue = processed;\n      this.form.setValue(processed);\n      const {\n        from,\n        to\n      } = processed;\n      const formattedFrom = processInputString(from, config);\n      const formattedTo = processInputString(to, config);\n      const formattedValue = `${formattedFrom}${!!from && !!to ? ' - ' : ''}${formattedTo}`;\n      this.valueControl.setValue(formattedValue);\n      if (this.config.timeZone && from && to) {\n        let fromDate = moment.tz(moment(from), this.config.timeZone);\n        let toDate = moment.tz(moment(to), this.config.timeZone);\n        const currentDate = moment.tz(moment(), this.config.timeZone);\n        fromDate = fromDate.add(fromDate.utcOffset() - currentDate.utcOffset(), 'minutes');\n        toDate = toDate.add(toDate.utcOffset() - currentDate.utcOffset(), 'minutes');\n        this.onChange({\n          from: fromDate.toISOString(),\n          to: toDate.toISOString()\n        });\n      } else {\n        this.onChange(processed);\n      }\n    });\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.elRef && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n    }\n  }\n  writeValue(val) {\n    const {\n      from,\n      to\n    } = val || {};\n    let data = val;\n    if (this.config.timeZone && from && to) {\n      let fromDate = moment.tz(moment(from), this.config.timeZone);\n      let toDate = moment.tz(moment(to), this.config.timeZone);\n      const currentDate = moment.tz(moment(), this.config.timeZone);\n      fromDate = fromDate.add(currentDate.utcOffset() - fromDate.utcOffset(), 'minutes');\n      toDate = toDate.add(currentDate.utcOffset() - toDate.utcOffset(), 'minutes');\n      data = {\n        from: fromDate.toISOString(),\n        to: toDate.toISOString()\n      };\n    }\n    this._currentControlValue = new SwuiDateRangeModel(data);\n    this._value$.next(undefined);\n  }\n  get fromControl() {\n    return this.form.get('from');\n  }\n  get toControl() {\n    return this.form.get('to');\n  }\n  prevent(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  onPeriodSelect(period) {\n    var _this$config, _this$config2;\n    this.form.patchValue(period((_this$config = this.config) === null || _this$config === void 0 ? void 0 : _this$config.timeZone, (_this$config2 = this.config) === null || _this$config2 === void 0 ? void 0 : _this$config2.chooseStart));\n  }\n  clear(event) {\n    event.preventDefault();\n    this.form.setValue({\n      from: '',\n      to: ''\n    });\n  }\n  cancel(event) {\n    event.preventDefault();\n    if (this.menuTriggerRef) {\n      this.menuTriggerRef.closeMenu();\n    }\n  }\n  apply(event) {\n    event.preventDefault();\n    this._currentControlValue = this.form.value;\n    this._value$.next(undefined);\n    if (this.menuTriggerRef) {\n      this.menuTriggerRef.closeMenu();\n    }\n  }\n  onMenuOpen() {\n    if (this.tabsRef) {\n      this.tabsRef.realignInkBar();\n    }\n  }\n  onMenuClose() {\n    this.form.patchValue(this._sourceValue);\n  }\n  onSelectedIndexChange(tabIndex) {\n    this.selectedIndex = tabIndex;\n    this.recalculateMenu();\n  }\n  isSelected(period) {\n    return JSON.stringify(period.fn(this.config.timeZone, this.config.chooseStart)) === JSON.stringify(this.form.value);\n  }\n  onDisabledState(disabled) {\n    disabled ? this.valueControl.disable() : this.valueControl.enable();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n  recalculateMenu() {\n    window.dispatchEvent(new Event('resize'));\n    this.cdr.markForCheck();\n  }\n}, _SwuiDateRangeComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}, {\n  type: UntypedFormBuilder\n}], _SwuiDateRangeComponent.propDecorators = {\n  customPeriods: [{\n    type: Input\n  }],\n  value: [{\n    type: Input\n  }],\n  customClass: [{\n    type: Input\n  }],\n  minDate: [{\n    type: Input\n  }],\n  maxDate: [{\n    type: Input\n  }],\n  config: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }],\n  input: [{\n    type: ViewChild,\n    args: ['input']\n  }],\n  tabsRef: [{\n    type: ViewChild,\n    args: ['tabSet', {\n      static: true\n    }]\n  }],\n  menuTriggerRef: [{\n    type: ViewChild,\n    args: ['date', {\n      read: MatMenuTrigger\n    }]\n  }],\n  matMenu: [{\n    type: ViewChild,\n    args: ['matMenu', {\n      read: MatMenu\n    }]\n  }]\n}, _SwuiDateRangeComponent);\nSwuiDateRangeComponent = __decorate([Component({\n  selector: 'lib-swui-date-range',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiDateRangeComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiDateRangeComponent);\nexport { SwuiDateRangeComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "FocusMonitor", "ChangeDetectorRef", "Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormBuilder", "UntypedFormControl", "FormGroupDirective", "NgControl", "MatFormFieldControl", "MatMenu", "MatMenuTrigger", "moment", "BehaviorSubject", "combineLatest", "distinctUntilChanged", "map", "takeUntil", "SwuiDatePickerConfigModel", "processInputString", "CUSTOM_PERIODS", "SwuiDateRangeModel", "SwuiMatFormFieldControl", "ErrorStateMatcher", "CONTROL_NAME", "nextUniqueId", "SwuiDateRangeComponent", "_SwuiDateRangeComponent", "customPeriods", "_customPeriods", "value", "val", "writeValue", "_currentControlValue", "config", "_config$", "next", "empty", "valueControl", "shouldLabelFloat", "constructor", "fm", "elRef", "cdr", "ngControl", "parentFormGroup", "errorStateMatcher", "fb", "minDate", "maxDate", "title", "id", "controlType", "selectedIndex", "_value$", "undefined", "from", "to", "_sourceValue", "form", "group", "ngOnInit", "processedMinDate", "processedMaxDate", "fromControl", "valueChanges", "pipe", "destroyed$", "subscribe", "fromMoment", "max<PERSON><PERSON><PERSON>", "chooseStart", "clone", "add", "toISOString", "subtract", "min", "toControl", "toMoment", "daysOffset", "isLastDay", "daysInMonth", "date", "offset", "diff", "minMomentPeriod", "min<PERSON><PERSON><PERSON>", "max", "oldConfig", "timeZone", "oldOffset", "Number", "tz", "utcOffset", "currentOffset", "utc", "<PERSON><PERSON><PERSON><PERSON>", "fromFormatted", "toFormatted", "milliseconds", "processed", "setValue", "formattedFrom", "formattedTo", "formattedValue", "fromDate", "toDate", "currentDate", "onChange", "onContainerClick", "event", "stopPropagation", "target", "tagName", "toLowerCase", "disabled", "nativeElement", "focus", "data", "get", "prevent", "preventDefault", "onPeriodSelect", "period", "_this$config", "_this$config2", "patchValue", "clear", "cancel", "menuTriggerRef", "closeMenu", "apply", "onMenuOpen", "tabsRef", "realignInkBar", "onMenuClose", "onSelectedIndexChange", "tabIndex", "recalculateMenu", "isSelected", "JSON", "stringify", "fn", "onDisabledState", "disable", "enable", "isErrorState", "input", "errorState", "window", "dispatchEvent", "Event", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ctorParameters", "type", "decorators", "propDecorators", "customClass", "args", "static", "read", "mat<PERSON>enu", "selector", "template", "providers", "provide", "useExisting", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-range/swui-date-range.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-date-range.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-date-range.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { ChangeDetectorRef, Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormBuilder, UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatMenu, MatMenuTrigger } from '@angular/material/menu';\nimport moment from 'moment';\nimport { BehaviorSubject, combineLatest } from 'rxjs';\nimport { distinctUntilChanged, map, takeUntil } from 'rxjs/operators';\nimport { SwuiDatePickerConfigModel } from '../swui-date-picker/swui-date-picker-config.model';\nimport { processInputString } from '../swui-date-picker/swui-date-picker.component';\nimport { CUSTOM_PERIODS, SwuiDateRangeModel } from './swui-date-range.model';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nconst CONTROL_NAME = 'lib-swui-date-range';\nlet nextUniqueId = 0;\nlet SwuiDateRangeComponent = class SwuiDateRangeComponent extends SwuiMatFormFieldControl {\n    set customPeriods(customPeriods) {\n        if (customPeriods) {\n            this._customPeriods = customPeriods;\n        }\n    }\n    get customPeriods() {\n        return this._customPeriods;\n    }\n    set value(val) {\n        this.writeValue(val);\n    }\n    get value() {\n        return this._currentControlValue;\n    }\n    set config(val) {\n        this._config$.next(new SwuiDatePickerConfigModel(val));\n    }\n    get config() {\n        return this._config$.value;\n    }\n    get empty() {\n        return !this.valueControl.value;\n    }\n    get shouldLabelFloat() {\n        return !this.empty;\n    }\n    constructor(fm, elRef, cdr, ngControl, parentFormGroup, errorStateMatcher, fb) {\n        super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n        this.cdr = cdr;\n        this.minDate = '';\n        this.maxDate = '';\n        this.title = '';\n        this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n        this.controlType = CONTROL_NAME;\n        this.valueControl = new UntypedFormControl('');\n        this.selectedIndex = 0;\n        this._customPeriods = CUSTOM_PERIODS;\n        this._value$ = new BehaviorSubject(undefined);\n        this._currentControlValue = { from: '', to: '' };\n        this._sourceValue = new SwuiDateRangeModel();\n        this._config$ = new BehaviorSubject(new SwuiDatePickerConfigModel(undefined));\n        this.form = fb.group({\n            from: [],\n            to: []\n        });\n    }\n    ngOnInit() {\n        this.processedMinDate = this.processedMinDate || this.minDate;\n        this.processedMaxDate = this.processedMaxDate || this.maxDate;\n        this.fromControl.valueChanges\n            .pipe(distinctUntilChanged(), takeUntil(this.destroyed$))\n            .subscribe(from => {\n            const fromMoment = from && moment(from);\n            const maxPeriod = this.config.chooseStart\n                ? this.config.maxPeriod && fromMoment\n                    && fromMoment.clone()\n                        .add(1, this.config.maxPeriod).toISOString()\n                : this.config.maxPeriod && fromMoment\n                    && fromMoment.clone()\n                        .add(1, this.config.maxPeriod).subtract(1, 'seconds').toISOString();\n            this.processedMaxDate = this.maxDate\n                ? maxPeriod && moment.min(maxPeriod, moment(this.maxDate)) || this.maxDate\n                : maxPeriod;\n        });\n        this.toControl.valueChanges\n            .pipe(distinctUntilChanged(), takeUntil(this.destroyed$))\n            .subscribe(to => {\n            const toMoment = to && moment(to);\n            let daysOffset = null;\n            if (this.config.maxPeriod === 'month' && toMoment) {\n                const isLastDay = toMoment.daysInMonth() === toMoment.date();\n                let offset = 0;\n                const diff = toMoment.date() - toMoment.clone().subtract(1, this.config.maxPeriod).daysInMonth();\n                if (diff > 0) {\n                    offset = diff;\n                }\n                daysOffset = isLastDay\n                    ? toMoment.clone().daysInMonth()\n                    : toMoment.clone().subtract(1, this.config.maxPeriod).daysInMonth() + offset;\n            }\n            const minMomentPeriod = this.config.chooseStart\n                ? this.config.maxPeriod && toMoment && toMoment.clone()\n                : this.config.maxPeriod && toMoment && toMoment.clone().add(1, 'second');\n            const minPeriod = daysOffset\n                ? this.config.maxPeriod && toMoment && minMomentPeriod?.subtract(daysOffset, 'day').toISOString()\n                : this.config.maxPeriod && toMoment && minMomentPeriod?.subtract(1, this.config.maxPeriod).toISOString();\n            this.processedMinDate = this.minDate\n                ? minPeriod && moment.max(minPeriod, moment(this.minDate)) || this.minDate\n                : minPeriod;\n        });\n        combineLatest([this._value$, this._config$])\n            .pipe(map(([, config]) => {\n            let offset = 0;\n            const val = this._currentControlValue;\n            if (this.oldConfig) {\n                if (this.oldConfig !== config && this.oldConfig.timeZone && this.oldConfig.timeZone !== config.timeZone) {\n                    const oldOffset = Number(moment().tz(this.oldConfig.timeZone).utcOffset());\n                    const currentOffset = Number(moment().tz(config.timeZone).utcOffset());\n                    offset = oldOffset - currentOffset;\n                }\n            }\n            this.oldConfig = config;\n            const from = val && val.from && moment.utc(val.from).isValid() ? val.from : '';\n            const fromFormatted = from && moment(from).add(offset, 'minutes').utc().toISOString();\n            const to = val && val.to && moment.utc(val.to).isValid() ? val.to : '';\n            const toFormatted = to && moment(to).add(offset, 'minutes').milliseconds(0).utc().toISOString();\n            const processed = new SwuiDateRangeModel({ from: fromFormatted, to: toFormatted });\n            return { processed, config };\n        }), takeUntil(this.destroyed$))\n            .subscribe(({ processed, config }) => {\n            this._sourceValue = processed;\n            this._currentControlValue = processed;\n            this.form.setValue(processed);\n            const { from, to } = processed;\n            const formattedFrom = processInputString(from, config);\n            const formattedTo = processInputString(to, config);\n            const formattedValue = `${formattedFrom}${!!from && !!to ? ' - ' : ''}${formattedTo}`;\n            this.valueControl.setValue(formattedValue);\n            if (this.config.timeZone && from && to) {\n                let fromDate = moment.tz(moment(from), this.config.timeZone);\n                let toDate = moment.tz(moment(to), this.config.timeZone);\n                const currentDate = moment.tz(moment(), this.config.timeZone);\n                fromDate = fromDate.add(fromDate.utcOffset() - currentDate.utcOffset(), 'minutes');\n                toDate = toDate.add(toDate.utcOffset() - currentDate.utcOffset(), 'minutes');\n                this.onChange({ from: fromDate.toISOString(), to: toDate.toISOString() });\n            }\n            else {\n                this.onChange(processed);\n            }\n        });\n    }\n    onContainerClick(event) {\n        event.stopPropagation();\n        if (this.elRef && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n            this.elRef.nativeElement.focus();\n        }\n    }\n    writeValue(val) {\n        const { from, to } = val || {};\n        let data = val;\n        if (this.config.timeZone && from && to) {\n            let fromDate = moment.tz(moment(from), this.config.timeZone);\n            let toDate = moment.tz(moment(to), this.config.timeZone);\n            const currentDate = moment.tz(moment(), this.config.timeZone);\n            fromDate = fromDate.add(currentDate.utcOffset() - fromDate.utcOffset(), 'minutes');\n            toDate = toDate.add(currentDate.utcOffset() - toDate.utcOffset(), 'minutes');\n            data = { from: fromDate.toISOString(), to: toDate.toISOString() };\n        }\n        this._currentControlValue = new SwuiDateRangeModel(data);\n        this._value$.next(undefined);\n    }\n    get fromControl() {\n        return this.form.get('from');\n    }\n    get toControl() {\n        return this.form.get('to');\n    }\n    prevent(event) {\n        event.preventDefault();\n        event.stopPropagation();\n    }\n    onPeriodSelect(period) {\n        this.form.patchValue(period(this.config?.timeZone, this.config?.chooseStart));\n    }\n    clear(event) {\n        event.preventDefault();\n        this.form.setValue({ from: '', to: '' });\n    }\n    cancel(event) {\n        event.preventDefault();\n        if (this.menuTriggerRef) {\n            this.menuTriggerRef.closeMenu();\n        }\n    }\n    apply(event) {\n        event.preventDefault();\n        this._currentControlValue = this.form.value;\n        this._value$.next(undefined);\n        if (this.menuTriggerRef) {\n            this.menuTriggerRef.closeMenu();\n        }\n    }\n    onMenuOpen() {\n        if (this.tabsRef) {\n            this.tabsRef.realignInkBar();\n        }\n    }\n    onMenuClose() {\n        this.form.patchValue(this._sourceValue);\n    }\n    onSelectedIndexChange(tabIndex) {\n        this.selectedIndex = tabIndex;\n        this.recalculateMenu();\n    }\n    isSelected(period) {\n        return JSON.stringify(period.fn(this.config.timeZone, this.config.chooseStart)) === JSON.stringify(this.form.value);\n    }\n    onDisabledState(disabled) {\n        disabled ? this.valueControl.disable() : this.valueControl.enable();\n    }\n    isErrorState() {\n        if (this.input) {\n            return this.input.errorState;\n        }\n        return false;\n    }\n    recalculateMenu() {\n        window.dispatchEvent(new Event('resize'));\n        this.cdr.markForCheck();\n    }\n    static { this.ctorParameters = () => [\n        { type: FocusMonitor },\n        { type: ElementRef },\n        { type: ChangeDetectorRef },\n        { type: NgControl, decorators: [{ type: Optional }, { type: Self }] },\n        { type: FormGroupDirective, decorators: [{ type: Optional }] },\n        { type: ErrorStateMatcher },\n        { type: UntypedFormBuilder }\n    ]; }\n    static { this.propDecorators = {\n        customPeriods: [{ type: Input }],\n        value: [{ type: Input }],\n        customClass: [{ type: Input }],\n        minDate: [{ type: Input }],\n        maxDate: [{ type: Input }],\n        config: [{ type: Input }],\n        title: [{ type: Input }],\n        id: [{ type: HostBinding }],\n        shouldLabelFloat: [{ type: HostBinding, args: ['class.floating',] }],\n        input: [{ type: ViewChild, args: ['input',] }],\n        tabsRef: [{ type: ViewChild, args: ['tabSet', { static: true },] }],\n        menuTriggerRef: [{ type: ViewChild, args: ['date', { read: MatMenuTrigger },] }],\n        matMenu: [{ type: ViewChild, args: ['matMenu', { read: MatMenu },] }]\n    }; }\n};\nSwuiDateRangeComponent = __decorate([\n    Component({\n        selector: 'lib-swui-date-range',\n        template: __NG_CLI_RESOURCE__0,\n        providers: [{ provide: MatFormFieldControl, useExisting: SwuiDateRangeComponent }],\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiDateRangeComponent);\nexport { SwuiDateRangeComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AACvH,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AACtG,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,OAAO,EAAEC,cAAc,QAAQ,wBAAwB;AAChE,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,eAAe,EAAEC,aAAa,QAAQ,MAAM;AACrD,SAASC,oBAAoB,EAAEC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AACrE,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,kBAAkB,QAAQ,gDAAgD;AACnF,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,yBAAyB;AAC5E,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,MAAMC,YAAY,GAAG,qBAAqB;AAC1C,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,sBAAsB,IAAAC,uBAAA,GAAG,MAAMD,sBAAsB,SAASJ,uBAAuB,CAAC;EACtF,IAAIM,aAAaA,CAACA,aAAa,EAAE;IAC7B,IAAIA,aAAa,EAAE;MACf,IAAI,CAACC,cAAc,GAAGD,aAAa;IACvC;EACJ;EACA,IAAIA,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAIC,KAAKA,CAACC,GAAG,EAAE;IACX,IAAI,CAACC,UAAU,CAACD,GAAG,CAAC;EACxB;EACA,IAAID,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACG,oBAAoB;EACpC;EACA,IAAIC,MAAMA,CAACH,GAAG,EAAE;IACZ,IAAI,CAACI,QAAQ,CAACC,IAAI,CAAC,IAAIlB,yBAAyB,CAACa,GAAG,CAAC,CAAC;EAC1D;EACA,IAAIG,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,QAAQ,CAACL,KAAK;EAC9B;EACA,IAAIO,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACC,YAAY,CAACR,KAAK;EACnC;EACA,IAAIS,gBAAgBA,CAAA,EAAG;IACnB,OAAO,CAAC,IAAI,CAACF,KAAK;EACtB;EACAG,WAAWA,CAACC,EAAE,EAAEC,KAAK,EAAEC,GAAG,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,EAAE,EAAE;IAC3E,KAAK,CAACN,EAAE,EAAEC,KAAK,EAAEE,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAC/D,IAAI,CAACH,GAAG,GAAGA,GAAG;IACd,IAAI,CAACK,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,EAAE,GAAG,GAAG3B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAC7C,IAAI,CAAC2B,WAAW,GAAG5B,YAAY;IAC/B,IAAI,CAACc,YAAY,GAAG,IAAIhC,kBAAkB,CAAC,EAAE,CAAC;IAC9C,IAAI,CAAC+C,aAAa,GAAG,CAAC;IACtB,IAAI,CAACxB,cAAc,GAAGT,cAAc;IACpC,IAAI,CAACkC,OAAO,GAAG,IAAIzC,eAAe,CAAC0C,SAAS,CAAC;IAC7C,IAAI,CAACtB,oBAAoB,GAAG;MAAEuB,IAAI,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IAChD,IAAI,CAACC,YAAY,GAAG,IAAIrC,kBAAkB,CAAC,CAAC;IAC5C,IAAI,CAACc,QAAQ,GAAG,IAAItB,eAAe,CAAC,IAAIK,yBAAyB,CAACqC,SAAS,CAAC,CAAC;IAC7E,IAAI,CAACI,IAAI,GAAGZ,EAAE,CAACa,KAAK,CAAC;MACjBJ,IAAI,EAAE,EAAE;MACRC,EAAE,EAAE;IACR,CAAC,CAAC;EACN;EACAI,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACd,OAAO;IAC7D,IAAI,CAACe,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACd,OAAO;IAC7D,IAAI,CAACe,WAAW,CAACC,YAAY,CACxBC,IAAI,CAACnD,oBAAoB,CAAC,CAAC,EAAEE,SAAS,CAAC,IAAI,CAACkD,UAAU,CAAC,CAAC,CACxDC,SAAS,CAACZ,IAAI,IAAI;MACnB,MAAMa,UAAU,GAAGb,IAAI,IAAI5C,MAAM,CAAC4C,IAAI,CAAC;MACvC,MAAMc,SAAS,GAAG,IAAI,CAACpC,MAAM,CAACqC,WAAW,GACnC,IAAI,CAACrC,MAAM,CAACoC,SAAS,IAAID,UAAU,IAC9BA,UAAU,CAACG,KAAK,CAAC,CAAC,CAChBC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACvC,MAAM,CAACoC,SAAS,CAAC,CAACI,WAAW,CAAC,CAAC,GAClD,IAAI,CAACxC,MAAM,CAACoC,SAAS,IAAID,UAAU,IAC9BA,UAAU,CAACG,KAAK,CAAC,CAAC,CAChBC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACvC,MAAM,CAACoC,SAAS,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,CAACD,WAAW,CAAC,CAAC;MAC/E,IAAI,CAACX,gBAAgB,GAAG,IAAI,CAACd,OAAO,GAC9BqB,SAAS,IAAI1D,MAAM,CAACgE,GAAG,CAACN,SAAS,EAAE1D,MAAM,CAAC,IAAI,CAACqC,OAAO,CAAC,CAAC,IAAI,IAAI,CAACA,OAAO,GACxEqB,SAAS;IACnB,CAAC,CAAC;IACF,IAAI,CAACO,SAAS,CAACZ,YAAY,CACtBC,IAAI,CAACnD,oBAAoB,CAAC,CAAC,EAAEE,SAAS,CAAC,IAAI,CAACkD,UAAU,CAAC,CAAC,CACxDC,SAAS,CAACX,EAAE,IAAI;MACjB,MAAMqB,QAAQ,GAAGrB,EAAE,IAAI7C,MAAM,CAAC6C,EAAE,CAAC;MACjC,IAAIsB,UAAU,GAAG,IAAI;MACrB,IAAI,IAAI,CAAC7C,MAAM,CAACoC,SAAS,KAAK,OAAO,IAAIQ,QAAQ,EAAE;QAC/C,MAAME,SAAS,GAAGF,QAAQ,CAACG,WAAW,CAAC,CAAC,KAAKH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAC5D,IAAIC,MAAM,GAAG,CAAC;QACd,MAAMC,IAAI,GAAGN,QAAQ,CAACI,IAAI,CAAC,CAAC,GAAGJ,QAAQ,CAACN,KAAK,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACzC,MAAM,CAACoC,SAAS,CAAC,CAACW,WAAW,CAAC,CAAC;QAChG,IAAIG,IAAI,GAAG,CAAC,EAAE;UACVD,MAAM,GAAGC,IAAI;QACjB;QACAL,UAAU,GAAGC,SAAS,GAChBF,QAAQ,CAACN,KAAK,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC,GAC9BH,QAAQ,CAACN,KAAK,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACzC,MAAM,CAACoC,SAAS,CAAC,CAACW,WAAW,CAAC,CAAC,GAAGE,MAAM;MACpF;MACA,MAAME,eAAe,GAAG,IAAI,CAACnD,MAAM,CAACqC,WAAW,GACzC,IAAI,CAACrC,MAAM,CAACoC,SAAS,IAAIQ,QAAQ,IAAIA,QAAQ,CAACN,KAAK,CAAC,CAAC,GACrD,IAAI,CAACtC,MAAM,CAACoC,SAAS,IAAIQ,QAAQ,IAAIA,QAAQ,CAACN,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC;MAC5E,MAAMa,SAAS,GAAGP,UAAU,GACtB,IAAI,CAAC7C,MAAM,CAACoC,SAAS,IAAIQ,QAAQ,KAAIO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEV,QAAQ,CAACI,UAAU,EAAE,KAAK,CAAC,CAACL,WAAW,CAAC,CAAC,IAC/F,IAAI,CAACxC,MAAM,CAACoC,SAAS,IAAIQ,QAAQ,KAAIO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEV,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACzC,MAAM,CAACoC,SAAS,CAAC,CAACI,WAAW,CAAC,CAAC;MAC5G,IAAI,CAACZ,gBAAgB,GAAG,IAAI,CAACd,OAAO,GAC9BsC,SAAS,IAAI1E,MAAM,CAAC2E,GAAG,CAACD,SAAS,EAAE1E,MAAM,CAAC,IAAI,CAACoC,OAAO,CAAC,CAAC,IAAI,IAAI,CAACA,OAAO,GACxEsC,SAAS;IACnB,CAAC,CAAC;IACFxE,aAAa,CAAC,CAAC,IAAI,CAACwC,OAAO,EAAE,IAAI,CAACnB,QAAQ,CAAC,CAAC,CACvC+B,IAAI,CAAClD,GAAG,CAAC,CAAC,GAAGkB,MAAM,CAAC,KAAK;MAC1B,IAAIiD,MAAM,GAAG,CAAC;MACd,MAAMpD,GAAG,GAAG,IAAI,CAACE,oBAAoB;MACrC,IAAI,IAAI,CAACuD,SAAS,EAAE;QAChB,IAAI,IAAI,CAACA,SAAS,KAAKtD,MAAM,IAAI,IAAI,CAACsD,SAAS,CAACC,QAAQ,IAAI,IAAI,CAACD,SAAS,CAACC,QAAQ,KAAKvD,MAAM,CAACuD,QAAQ,EAAE;UACrG,MAAMC,SAAS,GAAGC,MAAM,CAAC/E,MAAM,CAAC,CAAC,CAACgF,EAAE,CAAC,IAAI,CAACJ,SAAS,CAACC,QAAQ,CAAC,CAACI,SAAS,CAAC,CAAC,CAAC;UAC1E,MAAMC,aAAa,GAAGH,MAAM,CAAC/E,MAAM,CAAC,CAAC,CAACgF,EAAE,CAAC1D,MAAM,CAACuD,QAAQ,CAAC,CAACI,SAAS,CAAC,CAAC,CAAC;UACtEV,MAAM,GAAGO,SAAS,GAAGI,aAAa;QACtC;MACJ;MACA,IAAI,CAACN,SAAS,GAAGtD,MAAM;MACvB,MAAMsB,IAAI,GAAGzB,GAAG,IAAIA,GAAG,CAACyB,IAAI,IAAI5C,MAAM,CAACmF,GAAG,CAAChE,GAAG,CAACyB,IAAI,CAAC,CAACwC,OAAO,CAAC,CAAC,GAAGjE,GAAG,CAACyB,IAAI,GAAG,EAAE;MAC9E,MAAMyC,aAAa,GAAGzC,IAAI,IAAI5C,MAAM,CAAC4C,IAAI,CAAC,CAACiB,GAAG,CAACU,MAAM,EAAE,SAAS,CAAC,CAACY,GAAG,CAAC,CAAC,CAACrB,WAAW,CAAC,CAAC;MACrF,MAAMjB,EAAE,GAAG1B,GAAG,IAAIA,GAAG,CAAC0B,EAAE,IAAI7C,MAAM,CAACmF,GAAG,CAAChE,GAAG,CAAC0B,EAAE,CAAC,CAACuC,OAAO,CAAC,CAAC,GAAGjE,GAAG,CAAC0B,EAAE,GAAG,EAAE;MACtE,MAAMyC,WAAW,GAAGzC,EAAE,IAAI7C,MAAM,CAAC6C,EAAE,CAAC,CAACgB,GAAG,CAACU,MAAM,EAAE,SAAS,CAAC,CAACgB,YAAY,CAAC,CAAC,CAAC,CAACJ,GAAG,CAAC,CAAC,CAACrB,WAAW,CAAC,CAAC;MAC/F,MAAM0B,SAAS,GAAG,IAAI/E,kBAAkB,CAAC;QAAEmC,IAAI,EAAEyC,aAAa;QAAExC,EAAE,EAAEyC;MAAY,CAAC,CAAC;MAClF,OAAO;QAAEE,SAAS;QAAElE;MAAO,CAAC;IAChC,CAAC,CAAC,EAAEjB,SAAS,CAAC,IAAI,CAACkD,UAAU,CAAC,CAAC,CAC1BC,SAAS,CAAC,CAAC;MAAEgC,SAAS;MAAElE;IAAO,CAAC,KAAK;MACtC,IAAI,CAACwB,YAAY,GAAG0C,SAAS;MAC7B,IAAI,CAACnE,oBAAoB,GAAGmE,SAAS;MACrC,IAAI,CAACzC,IAAI,CAAC0C,QAAQ,CAACD,SAAS,CAAC;MAC7B,MAAM;QAAE5C,IAAI;QAAEC;MAAG,CAAC,GAAG2C,SAAS;MAC9B,MAAME,aAAa,GAAGnF,kBAAkB,CAACqC,IAAI,EAAEtB,MAAM,CAAC;MACtD,MAAMqE,WAAW,GAAGpF,kBAAkB,CAACsC,EAAE,EAAEvB,MAAM,CAAC;MAClD,MAAMsE,cAAc,GAAG,GAAGF,aAAa,GAAG,CAAC,CAAC9C,IAAI,IAAI,CAAC,CAACC,EAAE,GAAG,KAAK,GAAG,EAAE,GAAG8C,WAAW,EAAE;MACrF,IAAI,CAACjE,YAAY,CAAC+D,QAAQ,CAACG,cAAc,CAAC;MAC1C,IAAI,IAAI,CAACtE,MAAM,CAACuD,QAAQ,IAAIjC,IAAI,IAAIC,EAAE,EAAE;QACpC,IAAIgD,QAAQ,GAAG7F,MAAM,CAACgF,EAAE,CAAChF,MAAM,CAAC4C,IAAI,CAAC,EAAE,IAAI,CAACtB,MAAM,CAACuD,QAAQ,CAAC;QAC5D,IAAIiB,MAAM,GAAG9F,MAAM,CAACgF,EAAE,CAAChF,MAAM,CAAC6C,EAAE,CAAC,EAAE,IAAI,CAACvB,MAAM,CAACuD,QAAQ,CAAC;QACxD,MAAMkB,WAAW,GAAG/F,MAAM,CAACgF,EAAE,CAAChF,MAAM,CAAC,CAAC,EAAE,IAAI,CAACsB,MAAM,CAACuD,QAAQ,CAAC;QAC7DgB,QAAQ,GAAGA,QAAQ,CAAChC,GAAG,CAACgC,QAAQ,CAACZ,SAAS,CAAC,CAAC,GAAGc,WAAW,CAACd,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC;QAClFa,MAAM,GAAGA,MAAM,CAACjC,GAAG,CAACiC,MAAM,CAACb,SAAS,CAAC,CAAC,GAAGc,WAAW,CAACd,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC;QAC5E,IAAI,CAACe,QAAQ,CAAC;UAAEpD,IAAI,EAAEiD,QAAQ,CAAC/B,WAAW,CAAC,CAAC;UAAEjB,EAAE,EAAEiD,MAAM,CAAChC,WAAW,CAAC;QAAE,CAAC,CAAC;MAC7E,CAAC,MACI;QACD,IAAI,CAACkC,QAAQ,CAACR,SAAS,CAAC;MAC5B;IACJ,CAAC,CAAC;EACN;EACAS,gBAAgBA,CAACC,KAAK,EAAE;IACpBA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACrE,KAAK,IAAIoE,KAAK,CAACE,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAChF,IAAI,CAACzE,KAAK,CAAC0E,aAAa,CAACC,KAAK,CAAC,CAAC;IACpC;EACJ;EACArF,UAAUA,CAACD,GAAG,EAAE;IACZ,MAAM;MAAEyB,IAAI;MAAEC;IAAG,CAAC,GAAG1B,GAAG,IAAI,CAAC,CAAC;IAC9B,IAAIuF,IAAI,GAAGvF,GAAG;IACd,IAAI,IAAI,CAACG,MAAM,CAACuD,QAAQ,IAAIjC,IAAI,IAAIC,EAAE,EAAE;MACpC,IAAIgD,QAAQ,GAAG7F,MAAM,CAACgF,EAAE,CAAChF,MAAM,CAAC4C,IAAI,CAAC,EAAE,IAAI,CAACtB,MAAM,CAACuD,QAAQ,CAAC;MAC5D,IAAIiB,MAAM,GAAG9F,MAAM,CAACgF,EAAE,CAAChF,MAAM,CAAC6C,EAAE,CAAC,EAAE,IAAI,CAACvB,MAAM,CAACuD,QAAQ,CAAC;MACxD,MAAMkB,WAAW,GAAG/F,MAAM,CAACgF,EAAE,CAAChF,MAAM,CAAC,CAAC,EAAE,IAAI,CAACsB,MAAM,CAACuD,QAAQ,CAAC;MAC7DgB,QAAQ,GAAGA,QAAQ,CAAChC,GAAG,CAACkC,WAAW,CAACd,SAAS,CAAC,CAAC,GAAGY,QAAQ,CAACZ,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC;MAClFa,MAAM,GAAGA,MAAM,CAACjC,GAAG,CAACkC,WAAW,CAACd,SAAS,CAAC,CAAC,GAAGa,MAAM,CAACb,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC;MAC5EyB,IAAI,GAAG;QAAE9D,IAAI,EAAEiD,QAAQ,CAAC/B,WAAW,CAAC,CAAC;QAAEjB,EAAE,EAAEiD,MAAM,CAAChC,WAAW,CAAC;MAAE,CAAC;IACrE;IACA,IAAI,CAACzC,oBAAoB,GAAG,IAAIZ,kBAAkB,CAACiG,IAAI,CAAC;IACxD,IAAI,CAAChE,OAAO,CAAClB,IAAI,CAACmB,SAAS,CAAC;EAChC;EACA,IAAIS,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACL,IAAI,CAAC4D,GAAG,CAAC,MAAM,CAAC;EAChC;EACA,IAAI1C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAClB,IAAI,CAAC4D,GAAG,CAAC,IAAI,CAAC;EAC9B;EACAC,OAAOA,CAACV,KAAK,EAAE;IACXA,KAAK,CAACW,cAAc,CAAC,CAAC;IACtBX,KAAK,CAACC,eAAe,CAAC,CAAC;EAC3B;EACAW,cAAcA,CAACC,MAAM,EAAE;IAAA,IAAAC,YAAA,EAAAC,aAAA;IACnB,IAAI,CAAClE,IAAI,CAACmE,UAAU,CAACH,MAAM,EAAAC,YAAA,GAAC,IAAI,CAAC1F,MAAM,cAAA0F,YAAA,uBAAXA,YAAA,CAAanC,QAAQ,GAAAoC,aAAA,GAAE,IAAI,CAAC3F,MAAM,cAAA2F,aAAA,uBAAXA,aAAA,CAAatD,WAAW,CAAC,CAAC;EACjF;EACAwD,KAAKA,CAACjB,KAAK,EAAE;IACTA,KAAK,CAACW,cAAc,CAAC,CAAC;IACtB,IAAI,CAAC9D,IAAI,CAAC0C,QAAQ,CAAC;MAAE7C,IAAI,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC,CAAC;EAC5C;EACAuE,MAAMA,CAAClB,KAAK,EAAE;IACVA,KAAK,CAACW,cAAc,CAAC,CAAC;IACtB,IAAI,IAAI,CAACQ,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACC,SAAS,CAAC,CAAC;IACnC;EACJ;EACAC,KAAKA,CAACrB,KAAK,EAAE;IACTA,KAAK,CAACW,cAAc,CAAC,CAAC;IACtB,IAAI,CAACxF,oBAAoB,GAAG,IAAI,CAAC0B,IAAI,CAAC7B,KAAK;IAC3C,IAAI,CAACwB,OAAO,CAAClB,IAAI,CAACmB,SAAS,CAAC;IAC5B,IAAI,IAAI,CAAC0E,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACC,SAAS,CAAC,CAAC;IACnC;EACJ;EACAE,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,aAAa,CAAC,CAAC;IAChC;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5E,IAAI,CAACmE,UAAU,CAAC,IAAI,CAACpE,YAAY,CAAC;EAC3C;EACA8E,qBAAqBA,CAACC,QAAQ,EAAE;IAC5B,IAAI,CAACpF,aAAa,GAAGoF,QAAQ;IAC7B,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACAC,UAAUA,CAAChB,MAAM,EAAE;IACf,OAAOiB,IAAI,CAACC,SAAS,CAAClB,MAAM,CAACmB,EAAE,CAAC,IAAI,CAAC5G,MAAM,CAACuD,QAAQ,EAAE,IAAI,CAACvD,MAAM,CAACqC,WAAW,CAAC,CAAC,KAAKqE,IAAI,CAACC,SAAS,CAAC,IAAI,CAAClF,IAAI,CAAC7B,KAAK,CAAC;EACvH;EACAiH,eAAeA,CAAC5B,QAAQ,EAAE;IACtBA,QAAQ,GAAG,IAAI,CAAC7E,YAAY,CAAC0G,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC1G,YAAY,CAAC2G,MAAM,CAAC,CAAC;EACvE;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,OAAO,IAAI,CAACA,KAAK,CAACC,UAAU;IAChC;IACA,OAAO,KAAK;EAChB;EACAV,eAAeA,CAAA,EAAG;IACdW,MAAM,CAACC,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACzC,IAAI,CAAC5G,GAAG,CAAC6G,YAAY,CAAC,CAAC;EAC3B;AAyBJ,CAAC,EAxBY7H,uBAAA,CAAK8H,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE9J;AAAa,CAAC,EACtB;EAAE8J,IAAI,EAAE3J;AAAW,CAAC,EACpB;EAAE2J,IAAI,EAAE7J;AAAkB,CAAC,EAC3B;EAAE6J,IAAI,EAAElJ,SAAS;EAAEmJ,UAAU,EAAE,CAAC;IAAED,IAAI,EAAExJ;EAAS,CAAC,EAAE;IAAEwJ,IAAI,EAAEvJ;EAAK,CAAC;AAAE,CAAC,EACrE;EAAEuJ,IAAI,EAAEnJ,kBAAkB;EAAEoJ,UAAU,EAAE,CAAC;IAAED,IAAI,EAAExJ;EAAS,CAAC;AAAE,CAAC,EAC9D;EAAEwJ,IAAI,EAAEnI;AAAkB,CAAC,EAC3B;EAAEmI,IAAI,EAAErJ;AAAmB,CAAC,CAC/B,EACQsB,uBAAA,CAAKiI,cAAc,GAAG;EAC3BhI,aAAa,EAAE,CAAC;IAAE8H,IAAI,EAAEzJ;EAAM,CAAC,CAAC;EAChC6B,KAAK,EAAE,CAAC;IAAE4H,IAAI,EAAEzJ;EAAM,CAAC,CAAC;EACxB4J,WAAW,EAAE,CAAC;IAAEH,IAAI,EAAEzJ;EAAM,CAAC,CAAC;EAC9B+C,OAAO,EAAE,CAAC;IAAE0G,IAAI,EAAEzJ;EAAM,CAAC,CAAC;EAC1BgD,OAAO,EAAE,CAAC;IAAEyG,IAAI,EAAEzJ;EAAM,CAAC,CAAC;EAC1BiC,MAAM,EAAE,CAAC;IAAEwH,IAAI,EAAEzJ;EAAM,CAAC,CAAC;EACzBiD,KAAK,EAAE,CAAC;IAAEwG,IAAI,EAAEzJ;EAAM,CAAC,CAAC;EACxBkD,EAAE,EAAE,CAAC;IAAEuG,IAAI,EAAE1J;EAAY,CAAC,CAAC;EAC3BuC,gBAAgB,EAAE,CAAC;IAAEmH,IAAI,EAAE1J,WAAW;IAAE8J,IAAI,EAAE,CAAC,gBAAgB;EAAG,CAAC,CAAC;EACpEX,KAAK,EAAE,CAAC;IAAEO,IAAI,EAAEtJ,SAAS;IAAE0J,IAAI,EAAE,CAAC,OAAO;EAAG,CAAC,CAAC;EAC9CzB,OAAO,EAAE,CAAC;IAAEqB,IAAI,EAAEtJ,SAAS;IAAE0J,IAAI,EAAE,CAAC,QAAQ,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC,CAAC;EACnE9B,cAAc,EAAE,CAAC;IAAEyB,IAAI,EAAEtJ,SAAS;IAAE0J,IAAI,EAAE,CAAC,MAAM,EAAE;MAAEE,IAAI,EAAErJ;IAAe,CAAC;EAAG,CAAC,CAAC;EAChFsJ,OAAO,EAAE,CAAC;IAAEP,IAAI,EAAEtJ,SAAS;IAAE0J,IAAI,EAAE,CAAC,SAAS,EAAE;MAAEE,IAAI,EAAEtJ;IAAQ,CAAC;EAAG,CAAC;AACxE,CAAC,EAAAiB,uBAAA,CACJ;AACDD,sBAAsB,GAAGjC,UAAU,CAAC,CAChCK,SAAS,CAAC;EACNoK,QAAQ,EAAE,qBAAqB;EAC/BC,QAAQ,EAAEzK,oBAAoB;EAC9B0K,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAE5J,mBAAmB;IAAE6J,WAAW,EAAE5I;EAAuB,CAAC,CAAC;EAClF6I,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC7K,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAE+B,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}