{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CustomPeriodModule } from './custom-period/custom-period.module';\nimport { SwuiDatetimepickerModule } from '../swui-datetimepicker/swui-datetimepicker.module';\nimport { SwuiDateTimeRangeComponent } from './swui-date-time-range.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const DATE_TIME_RANGE_MODULES = [TranslateModule.forChild(), MatFormFieldModule, MatIconModule, MatButtonModule, MatInputModule, MatRippleModule, SwuiDatetimepickerModule, CustomPeriodModule, ReactiveFormsModule];\nlet SwuiDateTimeRangeModule = class SwuiDateTimeRangeModule {};\nSwuiDateTimeRangeModule = __decorate([NgModule({\n  imports: [CommonModule, ...DATE_TIME_RANGE_MODULES],\n  exports: [SwuiDateTimeRangeComponent],\n  declarations: [SwuiDateTimeRangeComponent]\n})], SwuiDateTimeRangeModule);\nexport { SwuiDateTimeRangeModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "ReactiveFormsModule", "TranslateModule", "CustomPeriodModule", "SwuiDatetimepickerModule", "SwuiDateTimeRangeComponent", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatButtonModule", "MatRippleModule", "DATE_TIME_RANGE_MODULES", "<PERSON><PERSON><PERSON><PERSON>", "SwuiDateTimeRangeModule", "imports", "exports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-range/swui-date-time-range.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CustomPeriodModule } from './custom-period/custom-period.module';\nimport { SwuiDatetimepickerModule } from '../swui-datetimepicker/swui-datetimepicker.module';\nimport { SwuiDateTimeRangeComponent } from './swui-date-time-range.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const DATE_TIME_RANGE_MODULES = [\n    TranslateModule.forChild(),\n    MatFormFieldModule,\n    MatIconModule,\n    MatButtonModule,\n    MatInputModule,\n    MatRippleModule,\n    SwuiDatetimepickerModule,\n    CustomPeriodModule,\n    ReactiveFormsModule,\n];\nlet SwuiDateTimeRangeModule = class SwuiDateTimeRangeModule {\n};\nSwuiDateTimeRangeModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            ...DATE_TIME_RANGE_MODULES,\n        ],\n        exports: [\n            SwuiDateTimeRangeComponent\n        ],\n        declarations: [SwuiDateTimeRangeComponent]\n    })\n], SwuiDateTimeRangeModule);\nexport { SwuiDateTimeRangeModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,wBAAwB,QAAQ,mDAAmD;AAC5F,SAASC,0BAA0B,QAAQ,kCAAkC;AAC7E,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,MAAMC,uBAAuB,GAAG,CACnCT,eAAe,CAACU,QAAQ,CAAC,CAAC,EAC1BN,kBAAkB,EAClBC,aAAa,EACbE,eAAe,EACfD,cAAc,EACdE,eAAe,EACfN,wBAAwB,EACxBD,kBAAkB,EAClBF,mBAAmB,CACtB;AACD,IAAIY,uBAAuB,GAAG,MAAMA,uBAAuB,CAAC,EAC3D;AACDA,uBAAuB,GAAGf,UAAU,CAAC,CACjCC,QAAQ,CAAC;EACLe,OAAO,EAAE,CACLd,YAAY,EACZ,GAAGW,uBAAuB,CAC7B;EACDI,OAAO,EAAE,CACLV,0BAA0B,CAC7B;EACDW,YAAY,EAAE,CAACX,0BAA0B;AAC7C,CAAC,CAAC,CACL,EAAEQ,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}