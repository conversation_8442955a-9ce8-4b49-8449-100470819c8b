{"ast": null, "code": "var _SwuiMultiselectComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-multiselect.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-multiselect.component.scss?ngResource\";\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { TranslateService } from '@ngx-translate/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nconst CONTROL_NAME = 'lib-swui-multiselect';\nlet nextUniqueId = 0;\nlet SwuiMultiselectComponent = (_SwuiMultiselectComponent = class SwuiMultiselectComponent extends SwuiMatFormFieldControl {\n  set data(val) {\n    this._data = val && Array.isArray(val) ? val : [];\n  }\n  get data() {\n    return this._data;\n  }\n  get value() {\n    return this._value;\n  }\n  set value(val) {\n    this._value = val && Array.isArray(val) ? val : [];\n    this.setSelectValue(this._value);\n  }\n  get empty() {\n    return !this.value || !this.value.length;\n  }\n  get shouldLabelFloat() {\n    return this.selectControl.value;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, translate) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.translate = translate;\n    this.searchPlaceholder = 'COMPONENTS.MULTISELECT.search';\n    this.title = '';\n    this.showSearch = false;\n    this.disableAllOption = false;\n    this.selectControl = new UntypedFormControl();\n    this.controlType = CONTROL_NAME;\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._data = [];\n    this._value = [];\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.elRef && this.selectRef && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n      this.selectRef.openMenu();\n    }\n  }\n  writeValue(val) {\n    this._value = val && Array.isArray(val) ? val : [];\n    this.setSelectValue(this._value);\n  }\n  onCancel() {\n    if (this.selectRef) {\n      this.selectRef.closeMenu();\n    }\n  }\n  onApply(data) {\n    this.setSelectValue(data);\n    this._value = data;\n    this.onChange(data);\n    if (this.selectRef) {\n      this.selectRef.closeMenu();\n    }\n  }\n  onDisabledState(value) {\n    value ? this.selectControl.disable() : this.selectControl.enable();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n  setSelectValue(data) {\n    const multiMessage = this.translate.instant('COMPONENTS.MULTISELECT.multiple');\n    const singleItem = this.data.find(el => el.id === data[0]);\n    const selectValue = data.length > 1 ? `(${multiMessage})` : singleItem ? singleItem.text : '';\n    this.selectControl.setValue(selectValue);\n  }\n}, _SwuiMultiselectComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}, {\n  type: TranslateService\n}], _SwuiMultiselectComponent.propDecorators = {\n  searchPlaceholder: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  showSearch: [{\n    type: Input\n  }],\n  disableAllOption: [{\n    type: Input\n  }],\n  data: [{\n    type: Input\n  }],\n  value: [{\n    type: Input\n  }],\n  input: [{\n    type: ViewChild,\n    args: ['input']\n  }],\n  selectRef: [{\n    type: ViewChild,\n    args: [MatMenuTrigger, {\n      static: true\n    }]\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiMultiselectComponent);\nSwuiMultiselectComponent = __decorate([Component({\n  selector: 'lib-swui-multiselect',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiMultiselectComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiMultiselectComponent);\nexport { SwuiMultiselectComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormControl", "FormGroupDirective", "NgControl", "FocusMonitor", "TranslateService", "MatMenuTrigger", "MatFormFieldControl", "SwuiMatFormFieldControl", "ErrorStateMatcher", "CONTROL_NAME", "nextUniqueId", "SwuiMultiselectComponent", "_SwuiMultiselectComponent", "data", "val", "_data", "Array", "isArray", "value", "_value", "setSelectValue", "empty", "length", "shouldLabelFloat", "selectControl", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "translate", "searchPlaceholder", "title", "showSearch", "disableAllOption", "controlType", "id", "onContainerClick", "event", "stopPropagation", "selectRef", "target", "tagName", "toLowerCase", "disabled", "nativeElement", "focus", "openMenu", "writeValue", "onCancel", "closeMenu", "onApply", "onChange", "onDisabledState", "disable", "enable", "isErrorState", "input", "errorState", "multiMessage", "instant", "singleItem", "find", "el", "selectValue", "text", "setValue", "ctorParameters", "type", "decorators", "propDecorators", "args", "static", "selector", "template", "providers", "provide", "useExisting", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-multiselect/swui-multiselect.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-multiselect.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-multiselect.component.scss?ngResource\";\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { TranslateService } from '@ngx-translate/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nconst CONTROL_NAME = 'lib-swui-multiselect';\nlet nextUniqueId = 0;\nlet SwuiMultiselectComponent = class SwuiMultiselectComponent extends SwuiMatFormFieldControl {\n    set data(val) {\n        this._data = val && Array.isArray(val) ? val : [];\n    }\n    get data() {\n        return this._data;\n    }\n    get value() {\n        return this._value;\n    }\n    set value(val) {\n        this._value = val && Array.isArray(val) ? val : [];\n        this.setSelectValue(this._value);\n    }\n    get empty() {\n        return !this.value || !this.value.length;\n    }\n    get shouldLabelFloat() {\n        return this.selectControl.value;\n    }\n    constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher, translate) {\n        super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n        this.translate = translate;\n        this.searchPlaceholder = 'COMPONENTS.MULTISELECT.search';\n        this.title = '';\n        this.showSearch = false;\n        this.disableAllOption = false;\n        this.selectControl = new UntypedFormControl();\n        this.controlType = CONTROL_NAME;\n        this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n        this._data = [];\n        this._value = [];\n    }\n    onContainerClick(event) {\n        event.stopPropagation();\n        if (this.elRef && this.selectRef && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n            this.elRef.nativeElement.focus();\n            this.selectRef.openMenu();\n        }\n    }\n    writeValue(val) {\n        this._value = val && Array.isArray(val) ? val : [];\n        this.setSelectValue(this._value);\n    }\n    onCancel() {\n        if (this.selectRef) {\n            this.selectRef.closeMenu();\n        }\n    }\n    onApply(data) {\n        this.setSelectValue(data);\n        this._value = data;\n        this.onChange(data);\n        if (this.selectRef) {\n            this.selectRef.closeMenu();\n        }\n    }\n    onDisabledState(value) {\n        value ? this.selectControl.disable() : this.selectControl.enable();\n    }\n    isErrorState() {\n        if (this.input) {\n            return this.input.errorState;\n        }\n        return false;\n    }\n    setSelectValue(data) {\n        const multiMessage = this.translate.instant('COMPONENTS.MULTISELECT.multiple');\n        const singleItem = this.data.find((el) => el.id === data[0]);\n        const selectValue = data.length > 1 ? `(${multiMessage})` : (singleItem ? singleItem.text : '');\n        this.selectControl.setValue(selectValue);\n    }\n    static { this.ctorParameters = () => [\n        { type: FocusMonitor },\n        { type: ElementRef },\n        { type: NgControl, decorators: [{ type: Optional }, { type: Self }] },\n        { type: FormGroupDirective, decorators: [{ type: Optional }] },\n        { type: ErrorStateMatcher },\n        { type: TranslateService }\n    ]; }\n    static { this.propDecorators = {\n        searchPlaceholder: [{ type: Input }],\n        title: [{ type: Input }],\n        showSearch: [{ type: Input }],\n        disableAllOption: [{ type: Input }],\n        data: [{ type: Input }],\n        value: [{ type: Input }],\n        input: [{ type: ViewChild, args: ['input',] }],\n        selectRef: [{ type: ViewChild, args: [MatMenuTrigger, { static: true },] }],\n        id: [{ type: HostBinding }],\n        shouldLabelFloat: [{ type: HostBinding, args: ['class.floating',] }]\n    }; }\n};\nSwuiMultiselectComponent = __decorate([\n    Component({\n        selector: 'lib-swui-multiselect',\n        template: __NG_CLI_RESOURCE__0,\n        providers: [{ provide: MatFormFieldControl, useExisting: SwuiMultiselectComponent }],\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiMultiselectComponent);\nexport { SwuiMultiselectComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,SAASC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AACpG,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAClF,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,MAAMC,YAAY,GAAG,sBAAsB;AAC3C,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,wBAAwB,IAAAC,yBAAA,GAAG,MAAMD,wBAAwB,SAASJ,uBAAuB,CAAC;EAC1F,IAAIM,IAAIA,CAACC,GAAG,EAAE;IACV,IAAI,CAACC,KAAK,GAAGD,GAAG,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;EACrD;EACA,IAAID,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACE,KAAK;EACrB;EACA,IAAIG,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACJ,GAAG,EAAE;IACX,IAAI,CAACK,MAAM,GAAGL,GAAG,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;IAClD,IAAI,CAACM,cAAc,CAAC,IAAI,CAACD,MAAM,CAAC;EACpC;EACA,IAAIE,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACH,KAAK,IAAI,CAAC,IAAI,CAACA,KAAK,CAACI,MAAM;EAC5C;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,aAAa,CAACN,KAAK;EACnC;EACAO,WAAWA,CAACC,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,SAAS,EAAE;IAC7E,KAAK,CAACL,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAC/D,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,iBAAiB,GAAG,+BAA+B;IACxD,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACX,aAAa,GAAG,IAAIxB,kBAAkB,CAAC,CAAC;IAC7C,IAAI,CAACoC,WAAW,GAAG3B,YAAY;IAC/B,IAAI,CAAC4B,EAAE,GAAG,GAAG5B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAC7C,IAAI,CAACK,KAAK,GAAG,EAAE;IACf,IAAI,CAACI,MAAM,GAAG,EAAE;EACpB;EACAmB,gBAAgBA,CAACC,KAAK,EAAE;IACpBA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACb,KAAK,IAAI,IAAI,CAACc,SAAS,IAAIF,KAAK,CAACG,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAClG,IAAI,CAAClB,KAAK,CAACmB,aAAa,CAACC,KAAK,CAAC,CAAC;MAChC,IAAI,CAACN,SAAS,CAACO,QAAQ,CAAC,CAAC;IAC7B;EACJ;EACAC,UAAUA,CAACnC,GAAG,EAAE;IACZ,IAAI,CAACK,MAAM,GAAGL,GAAG,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;IAClD,IAAI,CAACM,cAAc,CAAC,IAAI,CAACD,MAAM,CAAC;EACpC;EACA+B,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACT,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACU,SAAS,CAAC,CAAC;IAC9B;EACJ;EACAC,OAAOA,CAACvC,IAAI,EAAE;IACV,IAAI,CAACO,cAAc,CAACP,IAAI,CAAC;IACzB,IAAI,CAACM,MAAM,GAAGN,IAAI;IAClB,IAAI,CAACwC,QAAQ,CAACxC,IAAI,CAAC;IACnB,IAAI,IAAI,CAAC4B,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACU,SAAS,CAAC,CAAC;IAC9B;EACJ;EACAG,eAAeA,CAACpC,KAAK,EAAE;IACnBA,KAAK,GAAG,IAAI,CAACM,aAAa,CAAC+B,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC/B,aAAa,CAACgC,MAAM,CAAC,CAAC;EACtE;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,OAAO,IAAI,CAACA,KAAK,CAACC,UAAU;IAChC;IACA,OAAO,KAAK;EAChB;EACAvC,cAAcA,CAACP,IAAI,EAAE;IACjB,MAAM+C,YAAY,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,OAAO,CAAC,iCAAiC,CAAC;IAC9E,MAAMC,UAAU,GAAG,IAAI,CAACjD,IAAI,CAACkD,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAAC3B,EAAE,KAAKxB,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5D,MAAMoD,WAAW,GAAGpD,IAAI,CAACS,MAAM,GAAG,CAAC,GAAG,IAAIsC,YAAY,GAAG,GAAIE,UAAU,GAAGA,UAAU,CAACI,IAAI,GAAG,EAAG;IAC/F,IAAI,CAAC1C,aAAa,CAAC2C,QAAQ,CAACF,WAAW,CAAC;EAC5C;AAqBJ,CAAC,EApBYrD,yBAAA,CAAKwD,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAElE;AAAa,CAAC,EACtB;EAAEkE,IAAI,EAAE3E;AAAW,CAAC,EACpB;EAAE2E,IAAI,EAAEnE,SAAS;EAAEoE,UAAU,EAAE,CAAC;IAAED,IAAI,EAAExE;EAAS,CAAC,EAAE;IAAEwE,IAAI,EAAEvE;EAAK,CAAC;AAAE,CAAC,EACrE;EAAEuE,IAAI,EAAEpE,kBAAkB;EAAEqE,UAAU,EAAE,CAAC;IAAED,IAAI,EAAExE;EAAS,CAAC;AAAE,CAAC,EAC9D;EAAEwE,IAAI,EAAE7D;AAAkB,CAAC,EAC3B;EAAE6D,IAAI,EAAEjE;AAAiB,CAAC,CAC7B,EACQQ,yBAAA,CAAK2D,cAAc,GAAG;EAC3BvC,iBAAiB,EAAE,CAAC;IAAEqC,IAAI,EAAEzE;EAAM,CAAC,CAAC;EACpCqC,KAAK,EAAE,CAAC;IAAEoC,IAAI,EAAEzE;EAAM,CAAC,CAAC;EACxBsC,UAAU,EAAE,CAAC;IAAEmC,IAAI,EAAEzE;EAAM,CAAC,CAAC;EAC7BuC,gBAAgB,EAAE,CAAC;IAAEkC,IAAI,EAAEzE;EAAM,CAAC,CAAC;EACnCiB,IAAI,EAAE,CAAC;IAAEwD,IAAI,EAAEzE;EAAM,CAAC,CAAC;EACvBsB,KAAK,EAAE,CAAC;IAAEmD,IAAI,EAAEzE;EAAM,CAAC,CAAC;EACxB8D,KAAK,EAAE,CAAC;IAAEW,IAAI,EAAEtE,SAAS;IAAEyE,IAAI,EAAE,CAAC,OAAO;EAAG,CAAC,CAAC;EAC9C/B,SAAS,EAAE,CAAC;IAAE4B,IAAI,EAAEtE,SAAS;IAAEyE,IAAI,EAAE,CAACnE,cAAc,EAAE;MAAEoE,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC,CAAC;EAC3EpC,EAAE,EAAE,CAAC;IAAEgC,IAAI,EAAE1E;EAAY,CAAC,CAAC;EAC3B4B,gBAAgB,EAAE,CAAC;IAAE8C,IAAI,EAAE1E,WAAW;IAAE6E,IAAI,EAAE,CAAC,gBAAgB;EAAG,CAAC;AACvE,CAAC,EAAA5D,yBAAA,CACJ;AACDD,wBAAwB,GAAGrB,UAAU,CAAC,CAClCG,SAAS,CAAC;EACNiF,QAAQ,EAAE,sBAAsB;EAChCC,QAAQ,EAAEpF,oBAAoB;EAC9BqF,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAEvE,mBAAmB;IAAEwE,WAAW,EAAEnE;EAAyB,CAAC,CAAC;EACpFoE,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACxF,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEmB,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}