{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Kazakh [kk]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/nurlan\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var suffixes = {\n    0: '-ші',\n    1: '-ші',\n    2: '-ші',\n    3: '-ші',\n    4: '-ші',\n    5: '-ші',\n    6: '-шы',\n    7: '-ші',\n    8: '-ші',\n    9: '-шы',\n    10: '-шы',\n    20: '-шы',\n    30: '-шы',\n    40: '-шы',\n    50: '-ші',\n    60: '-шы',\n    70: '-ші',\n    80: '-ші',\n    90: '-шы',\n    100: '-ші'\n  };\n  var kk = moment.defineLocale('kk', {\n    months: 'қаңтар_ақпан_наурыз_сәуір_мамыр_маусым_шілде_тамыз_қыркүйек_қазан_қараша_желтоқсан'.split('_'),\n    monthsShort: 'қаң_ақп_нау_сәу_мам_мау_шіл_там_қыр_қаз_қар_жел'.split('_'),\n    weekdays: 'жексенбі_дүйсенбі_сейсенбі_сәрсенбі_бейсенбі_жұма_сенбі'.split('_'),\n    weekdaysShort: 'жек_дүй_сей_сәр_бей_жұм_сен'.split('_'),\n    weekdaysMin: 'жк_дй_сй_ср_бй_жм_сн'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Бүгін сағат] LT',\n      nextDay: '[Ертең сағат] LT',\n      nextWeek: 'dddd [сағат] LT',\n      lastDay: '[Кеше сағат] LT',\n      lastWeek: '[Өткен аптаның] dddd [сағат] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s ішінде',\n      past: '%s бұрын',\n      s: 'бірнеше секунд',\n      ss: '%d секунд',\n      m: 'бір минут',\n      mm: '%d минут',\n      h: 'бір сағат',\n      hh: '%d сағат',\n      d: 'бір күн',\n      dd: '%d күн',\n      M: 'бір ай',\n      MM: '%d ай',\n      y: 'бір жыл',\n      yy: '%d жыл'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(ші|шы)/,\n    ordinal: function (number) {\n      var a = number % 10,\n        b = number >= 100 ? 100 : null;\n      return number + (suffixes[number] || suffixes[a] || suffixes[b]);\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return kk;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "suffixes", "kk", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "a", "b", "week", "dow", "doy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/moment/locale/kk.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Kazakh [kk]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/nurlan\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var suffixes = {\n        0: '-ші',\n        1: '-ші',\n        2: '-ші',\n        3: '-ші',\n        4: '-ші',\n        5: '-ші',\n        6: '-шы',\n        7: '-ші',\n        8: '-ші',\n        9: '-шы',\n        10: '-шы',\n        20: '-шы',\n        30: '-шы',\n        40: '-шы',\n        50: '-ші',\n        60: '-шы',\n        70: '-ші',\n        80: '-ші',\n        90: '-шы',\n        100: '-ші',\n    };\n\n    var kk = moment.defineLocale('kk', {\n        months: 'қаңтар_ақпан_наурыз_сәуір_мамыр_маусым_шілде_тамыз_қыркүйек_қазан_қараша_желтоқсан'.split(\n            '_'\n        ),\n        monthsShort: 'қаң_ақп_нау_сәу_мам_мау_шіл_там_қыр_қаз_қар_жел'.split('_'),\n        weekdays: 'жексенбі_дүйсенбі_сейсенбі_сәрсенбі_бейсенбі_жұма_сенбі'.split(\n            '_'\n        ),\n        weekdaysShort: 'жек_дүй_сей_сәр_бей_жұм_сен'.split('_'),\n        weekdaysMin: 'жк_дй_сй_ср_бй_жм_сн'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Бүгін сағат] LT',\n            nextDay: '[Ертең сағат] LT',\n            nextWeek: 'dddd [сағат] LT',\n            lastDay: '[Кеше сағат] LT',\n            lastWeek: '[Өткен аптаның] dddd [сағат] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s ішінде',\n            past: '%s бұрын',\n            s: 'бірнеше секунд',\n            ss: '%d секунд',\n            m: 'бір минут',\n            mm: '%d минут',\n            h: 'бір сағат',\n            hh: '%d сағат',\n            d: 'бір күн',\n            dd: '%d күн',\n            M: 'бір ай',\n            MM: '%d ай',\n            y: 'бір жыл',\n            yy: '%d жыл',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}-(ші|шы)/,\n        ordinal: function (number) {\n            var a = number % 10,\n                b = number >= 100 ? 100 : null;\n            return number + (suffixes[number] || suffixes[a] || suffixes[b]);\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return kk;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,QAAQ,GAAG;IACX,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,GAAG,EAAE;EACT,CAAC;EAED,IAAIC,EAAE,GAAGF,MAAM,CAACG,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,oFAAoF,CAACC,KAAK,CAC9F,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,yDAAyD,CAACF,KAAK,CACrE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,kBAAkB;MAC3BC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,iBAAiB;MAC3BC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,iCAAiC;MAC3CC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,gBAAgB;MACnBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,iBAAiB;IACzCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIC,CAAC,GAAGD,MAAM,GAAG,EAAE;QACfE,CAAC,GAAGF,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI;MAClC,OAAOA,MAAM,IAAIxC,QAAQ,CAACwC,MAAM,CAAC,IAAIxC,QAAQ,CAACyC,CAAC,CAAC,IAAIzC,QAAQ,CAAC0C,CAAC,CAAC,CAAC;IACpE,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO5C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}