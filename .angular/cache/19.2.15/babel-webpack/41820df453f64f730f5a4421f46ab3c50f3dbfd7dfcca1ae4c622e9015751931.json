{"ast": null, "code": "import { __read } from \"tslib\";\nimport { innerFrom } from '../observable/innerFrom';\nimport { Observable } from '../Observable';\nimport { mergeMap } from '../operators/mergeMap';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nvar nodeEventEmitterMethods = ['addListener', 'removeListener'];\nvar eventTargetMethods = ['addEventListener', 'removeEventListener'];\nvar jqueryMethods = ['on', 'off'];\nexport function fromEvent(target, eventName, options, resultSelector) {\n  if (isFunction(options)) {\n    resultSelector = options;\n    options = undefined;\n  }\n  if (resultSelector) {\n    return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs(resultSelector));\n  }\n  var _a = __read(isEventTarget(target) ? eventTargetMethods.map(function (methodName) {\n      return function (handler) {\n        return target[methodName](eventName, handler, options);\n      };\n    }) : isNodeStyleEventEmitter(target) ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName)) : isJQueryStyleEventEmitter(target) ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName)) : [], 2),\n    add = _a[0],\n    remove = _a[1];\n  if (!add) {\n    if (isArrayLike(target)) {\n      return mergeMap(function (subTarget) {\n        return fromEvent(subTarget, eventName, options);\n      })(innerFrom(target));\n    }\n  }\n  if (!add) {\n    throw new TypeError('Invalid event target');\n  }\n  return new Observable(function (subscriber) {\n    var handler = function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return subscriber.next(1 < args.length ? args : args[0]);\n    };\n    add(handler);\n    return function () {\n      return remove(handler);\n    };\n  });\n}\nfunction toCommonHandlerRegistry(target, eventName) {\n  return function (methodName) {\n    return function (handler) {\n      return target[methodName](eventName, handler);\n    };\n  };\n}\nfunction isNodeStyleEventEmitter(target) {\n  return isFunction(target.addListener) && isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n  return isFunction(target.on) && isFunction(target.off);\n}\nfunction isEventTarget(target) {\n  return isFunction(target.addEventListener) && isFunction(target.removeEventListener);\n}\n//# sourceMappingURL=fromEvent.js.map", "map": {"version": 3, "names": ["__read", "innerFrom", "Observable", "mergeMap", "isArrayLike", "isFunction", "mapOneOrManyArgs", "nodeEventEmitterMethods", "eventTargetMethods", "jqueryMethods", "fromEvent", "target", "eventName", "options", "resultSelector", "undefined", "pipe", "_a", "isEventTarget", "map", "methodName", "handler", "isNodeStyleEventEmitter", "toCommonHandlerRegistry", "isJQueryStyleEventEmitter", "add", "remove", "subTarget", "TypeError", "subscriber", "args", "_i", "arguments", "length", "next", "addListener", "removeListener", "on", "off", "addEventListener", "removeEventListener"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/fromEvent.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { innerFrom } from '../observable/innerFrom';\nimport { Observable } from '../Observable';\nimport { mergeMap } from '../operators/mergeMap';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nvar nodeEventEmitterMethods = ['addListener', 'removeListener'];\nvar eventTargetMethods = ['addEventListener', 'removeEventListener'];\nvar jqueryMethods = ['on', 'off'];\nexport function fromEvent(target, eventName, options, resultSelector) {\n    if (isFunction(options)) {\n        resultSelector = options;\n        options = undefined;\n    }\n    if (resultSelector) {\n        return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs(resultSelector));\n    }\n    var _a = __read(isEventTarget(target)\n        ? eventTargetMethods.map(function (methodName) { return function (handler) { return target[methodName](eventName, handler, options); }; })\n        :\n            isNodeStyleEventEmitter(target)\n                ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName))\n                : isJQueryStyleEventEmitter(target)\n                    ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName))\n                    : [], 2), add = _a[0], remove = _a[1];\n    if (!add) {\n        if (isArrayLike(target)) {\n            return mergeMap(function (subTarget) { return fromEvent(subTarget, eventName, options); })(innerFrom(target));\n        }\n    }\n    if (!add) {\n        throw new TypeError('Invalid event target');\n    }\n    return new Observable(function (subscriber) {\n        var handler = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return subscriber.next(1 < args.length ? args : args[0]);\n        };\n        add(handler);\n        return function () { return remove(handler); };\n    });\n}\nfunction toCommonHandlerRegistry(target, eventName) {\n    return function (methodName) { return function (handler) { return target[methodName](eventName, handler); }; };\n}\nfunction isNodeStyleEventEmitter(target) {\n    return isFunction(target.addListener) && isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n    return isFunction(target.on) && isFunction(target.off);\n}\nfunction isEventTarget(target) {\n    return isFunction(target.addEventListener) && isFunction(target.removeEventListener);\n}\n//# sourceMappingURL=fromEvent.js.map"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,IAAIC,uBAAuB,GAAG,CAAC,aAAa,EAAE,gBAAgB,CAAC;AAC/D,IAAIC,kBAAkB,GAAG,CAAC,kBAAkB,EAAE,qBAAqB,CAAC;AACpE,IAAIC,aAAa,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;AACjC,OAAO,SAASC,SAASA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,cAAc,EAAE;EAClE,IAAIT,UAAU,CAACQ,OAAO,CAAC,EAAE;IACrBC,cAAc,GAAGD,OAAO;IACxBA,OAAO,GAAGE,SAAS;EACvB;EACA,IAAID,cAAc,EAAE;IAChB,OAAOJ,SAAS,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,CAAC,CAACG,IAAI,CAACV,gBAAgB,CAACQ,cAAc,CAAC,CAAC;EACvF;EACA,IAAIG,EAAE,GAAGjB,MAAM,CAACkB,aAAa,CAACP,MAAM,CAAC,GAC/BH,kBAAkB,CAACW,GAAG,CAAC,UAAUC,UAAU,EAAE;MAAE,OAAO,UAAUC,OAAO,EAAE;QAAE,OAAOV,MAAM,CAACS,UAAU,CAAC,CAACR,SAAS,EAAES,OAAO,EAAER,OAAO,CAAC;MAAE,CAAC;IAAE,CAAC,CAAC,GAEtIS,uBAAuB,CAACX,MAAM,CAAC,GACzBJ,uBAAuB,CAACY,GAAG,CAACI,uBAAuB,CAACZ,MAAM,EAAEC,SAAS,CAAC,CAAC,GACvEY,yBAAyB,CAACb,MAAM,CAAC,GAC7BF,aAAa,CAACU,GAAG,CAACI,uBAAuB,CAACZ,MAAM,EAAEC,SAAS,CAAC,CAAC,GAC7D,EAAE,EAAE,CAAC,CAAC;IAAEa,GAAG,GAAGR,EAAE,CAAC,CAAC,CAAC;IAAES,MAAM,GAAGT,EAAE,CAAC,CAAC,CAAC;EACrD,IAAI,CAACQ,GAAG,EAAE;IACN,IAAIrB,WAAW,CAACO,MAAM,CAAC,EAAE;MACrB,OAAOR,QAAQ,CAAC,UAAUwB,SAAS,EAAE;QAAE,OAAOjB,SAAS,CAACiB,SAAS,EAAEf,SAAS,EAAEC,OAAO,CAAC;MAAE,CAAC,CAAC,CAACZ,SAAS,CAACU,MAAM,CAAC,CAAC;IACjH;EACJ;EACA,IAAI,CAACc,GAAG,EAAE;IACN,MAAM,IAAIG,SAAS,CAAC,sBAAsB,CAAC;EAC/C;EACA,OAAO,IAAI1B,UAAU,CAAC,UAAU2B,UAAU,EAAE;IACxC,IAAIR,OAAO,GAAG,SAAAA,CAAA,EAAY;MACtB,IAAIS,IAAI,GAAG,EAAE;MACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;QAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;MAC5B;MACA,OAAOF,UAAU,CAACK,IAAI,CAAC,CAAC,GAAGJ,IAAI,CAACG,MAAM,GAAGH,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IACDL,GAAG,CAACJ,OAAO,CAAC;IACZ,OAAO,YAAY;MAAE,OAAOK,MAAM,CAACL,OAAO,CAAC;IAAE,CAAC;EAClD,CAAC,CAAC;AACN;AACA,SAASE,uBAAuBA,CAACZ,MAAM,EAAEC,SAAS,EAAE;EAChD,OAAO,UAAUQ,UAAU,EAAE;IAAE,OAAO,UAAUC,OAAO,EAAE;MAAE,OAAOV,MAAM,CAACS,UAAU,CAAC,CAACR,SAAS,EAAES,OAAO,CAAC;IAAE,CAAC;EAAE,CAAC;AAClH;AACA,SAASC,uBAAuBA,CAACX,MAAM,EAAE;EACrC,OAAON,UAAU,CAACM,MAAM,CAACwB,WAAW,CAAC,IAAI9B,UAAU,CAACM,MAAM,CAACyB,cAAc,CAAC;AAC9E;AACA,SAASZ,yBAAyBA,CAACb,MAAM,EAAE;EACvC,OAAON,UAAU,CAACM,MAAM,CAAC0B,EAAE,CAAC,IAAIhC,UAAU,CAACM,MAAM,CAAC2B,GAAG,CAAC;AAC1D;AACA,SAASpB,aAAaA,CAACP,MAAM,EAAE;EAC3B,OAAON,UAAU,CAACM,MAAM,CAAC4B,gBAAgB,CAAC,IAAIlC,UAAU,CAACM,MAAM,CAAC6B,mBAAmB,CAAC;AACxF;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}