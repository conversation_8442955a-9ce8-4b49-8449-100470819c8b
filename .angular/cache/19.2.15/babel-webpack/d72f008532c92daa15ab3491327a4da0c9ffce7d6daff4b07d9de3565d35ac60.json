{"ast": null, "code": "var _SwuiNotificationsService;\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SwuiSnackbarComponent } from './swui-snackbar/swui-snackbar.component';\nconst DEFAULT_DURATION = 5000;\nconst DEFAULT_VERTICAL_POSITION = 'bottom';\nconst DEFAULT_HORIZONTAL_POSITION = 'left';\nconst DEFAULT_ERROR_MESSAGE = 'Oops, something went wrong. Please try again later.';\nlet SwuiNotificationsService = (_SwuiNotificationsService = class SwuiNotificationsService {\n  constructor(snackbar) {\n    this.snackbar = snackbar;\n  }\n  success(message, title, verticalPosition, horizontalPosition, duration) {\n    const data = {\n      message,\n      title,\n      verticalPosition,\n      horizontalPosition,\n      duration\n    };\n    return this.showSnackbar(data, 'swui-snackbar-success');\n  }\n  error(message, title, verticalPosition, horizontalPosition, duration) {\n    const data = {\n      message: message || DEFAULT_ERROR_MESSAGE,\n      title,\n      verticalPosition,\n      horizontalPosition,\n      duration\n    };\n    return this.showSnackbar(data, 'swui-snackbar-error');\n  }\n  warning(message, title, verticalPosition, horizontalPosition, duration) {\n    const data = {\n      message,\n      title,\n      verticalPosition,\n      horizontalPosition,\n      duration\n    };\n    return this.showSnackbar(data, 'swui-snackbar-warning');\n  }\n  showSnackbar(data, panelClass = 'swui-snackbar-success') {\n    const config = {\n      data,\n      panelClass,\n      duration: data.duration || DEFAULT_DURATION,\n      verticalPosition: data.verticalPosition || DEFAULT_VERTICAL_POSITION,\n      horizontalPosition: data.horizontalPosition || DEFAULT_HORIZONTAL_POSITION\n    };\n    return this.snackbar.openFromComponent(SwuiSnackbarComponent, config);\n  }\n}, _SwuiNotificationsService.ctorParameters = () => [{\n  type: MatSnackBar\n}], _SwuiNotificationsService);\nSwuiNotificationsService = __decorate([Injectable({\n  providedIn: 'root'\n})], SwuiNotificationsService);\nexport { SwuiNotificationsService };", "map": {"version": 3, "names": ["__decorate", "Injectable", "MatSnackBar", "SwuiSnackbarComponent", "DEFAULT_DURATION", "DEFAULT_VERTICAL_POSITION", "DEFAULT_HORIZONTAL_POSITION", "DEFAULT_ERROR_MESSAGE", "SwuiNotificationsService", "_SwuiNotificationsService", "constructor", "snackbar", "success", "message", "title", "verticalPosition", "horizontalPosition", "duration", "data", "showSnackbar", "error", "warning", "panelClass", "config", "openFromComponent", "ctorParameters", "type", "providedIn"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-notifications/swui-notifications.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { SwuiSnackbarComponent } from './swui-snackbar/swui-snackbar.component';\nconst DEFAULT_DURATION = 5000;\nconst DEFAULT_VERTICAL_POSITION = 'bottom';\nconst DEFAULT_HORIZONTAL_POSITION = 'left';\nconst DEFAULT_ERROR_MESSAGE = 'Oops, something went wrong. Please try again later.';\nlet SwuiNotificationsService = class SwuiNotificationsService {\n    constructor(snackbar) {\n        this.snackbar = snackbar;\n    }\n    success(message, title, verticalPosition, horizontalPosition, duration) {\n        const data = { message, title, verticalPosition, horizontalPosition, duration };\n        return this.showSnackbar(data, 'swui-snackbar-success');\n    }\n    error(message, title, verticalPosition, horizontalPosition, duration) {\n        const data = {\n            message: message || DEFAULT_ERROR_MESSAGE,\n            title,\n            verticalPosition,\n            horizontalPosition,\n            duration\n        };\n        return this.showSnackbar(data, 'swui-snackbar-error');\n    }\n    warning(message, title, verticalPosition, horizontalPosition, duration) {\n        const data = { message, title, verticalPosition, horizontalPosition, duration };\n        return this.showSnackbar(data, 'swui-snackbar-warning');\n    }\n    showSnackbar(data, panelClass = 'swui-snackbar-success') {\n        const config = {\n            data,\n            panelClass,\n            duration: data.duration || DEFAULT_DURATION,\n            verticalPosition: data.verticalPosition || DEFAULT_VERTICAL_POSITION,\n            horizontalPosition: data.horizontalPosition || DEFAULT_HORIZONTAL_POSITION,\n        };\n        return this.snackbar.openFromComponent(SwuiSnackbarComponent, config);\n    }\n    static { this.ctorParameters = () => [\n        { type: MatSnackBar }\n    ]; }\n};\nSwuiNotificationsService = __decorate([\n    Injectable({\n        providedIn: 'root'\n    })\n], SwuiNotificationsService);\nexport { SwuiNotificationsService };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,MAAMC,gBAAgB,GAAG,IAAI;AAC7B,MAAMC,yBAAyB,GAAG,QAAQ;AAC1C,MAAMC,2BAA2B,GAAG,MAAM;AAC1C,MAAMC,qBAAqB,GAAG,qDAAqD;AACnF,IAAIC,wBAAwB,IAAAC,yBAAA,GAAG,MAAMD,wBAAwB,CAAC;EAC1DE,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,OAAOA,CAACC,OAAO,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,QAAQ,EAAE;IACpE,MAAMC,IAAI,GAAG;MAAEL,OAAO;MAAEC,KAAK;MAAEC,gBAAgB;MAAEC,kBAAkB;MAAEC;IAAS,CAAC;IAC/E,OAAO,IAAI,CAACE,YAAY,CAACD,IAAI,EAAE,uBAAuB,CAAC;EAC3D;EACAE,KAAKA,CAACP,OAAO,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,QAAQ,EAAE;IAClE,MAAMC,IAAI,GAAG;MACTL,OAAO,EAAEA,OAAO,IAAIN,qBAAqB;MACzCO,KAAK;MACLC,gBAAgB;MAChBC,kBAAkB;MAClBC;IACJ,CAAC;IACD,OAAO,IAAI,CAACE,YAAY,CAACD,IAAI,EAAE,qBAAqB,CAAC;EACzD;EACAG,OAAOA,CAACR,OAAO,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,QAAQ,EAAE;IACpE,MAAMC,IAAI,GAAG;MAAEL,OAAO;MAAEC,KAAK;MAAEC,gBAAgB;MAAEC,kBAAkB;MAAEC;IAAS,CAAC;IAC/E,OAAO,IAAI,CAACE,YAAY,CAACD,IAAI,EAAE,uBAAuB,CAAC;EAC3D;EACAC,YAAYA,CAACD,IAAI,EAAEI,UAAU,GAAG,uBAAuB,EAAE;IACrD,MAAMC,MAAM,GAAG;MACXL,IAAI;MACJI,UAAU;MACVL,QAAQ,EAAEC,IAAI,CAACD,QAAQ,IAAIb,gBAAgB;MAC3CW,gBAAgB,EAAEG,IAAI,CAACH,gBAAgB,IAAIV,yBAAyB;MACpEW,kBAAkB,EAAEE,IAAI,CAACF,kBAAkB,IAAIV;IACnD,CAAC;IACD,OAAO,IAAI,CAACK,QAAQ,CAACa,iBAAiB,CAACrB,qBAAqB,EAAEoB,MAAM,CAAC;EACzE;AAIJ,CAAC,EAHYd,yBAAA,CAAKgB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAExB;AAAY,CAAC,CACxB,EAAAO,yBAAA,CACJ;AACDD,wBAAwB,GAAGR,UAAU,CAAC,CAClCC,UAAU,CAAC;EACP0B,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEnB,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}