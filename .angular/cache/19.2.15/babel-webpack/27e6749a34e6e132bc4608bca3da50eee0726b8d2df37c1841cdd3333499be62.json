{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period, scheduler) {\n  if (period === void 0) {\n    period = 0;\n  }\n  if (scheduler === void 0) {\n    scheduler = asyncScheduler;\n  }\n  if (period < 0) {\n    period = 0;\n  }\n  return timer(period, period, scheduler);\n}\n//# sourceMappingURL=interval.js.map", "map": {"version": 3, "names": ["asyncScheduler", "timer", "interval", "period", "scheduler"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/interval.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period, scheduler) {\n    if (period === void 0) { period = 0; }\n    if (scheduler === void 0) { scheduler = asyncScheduler; }\n    if (period < 0) {\n        period = 0;\n    }\n    return timer(period, period, scheduler);\n}\n//# sourceMappingURL=interval.js.map"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,QAAQA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACxC,IAAID,MAAM,KAAK,KAAK,CAAC,EAAE;IAAEA,MAAM,GAAG,CAAC;EAAE;EACrC,IAAIC,SAAS,KAAK,KAAK,CAAC,EAAE;IAAEA,SAAS,GAAGJ,cAAc;EAAE;EACxD,IAAIG,MAAM,GAAG,CAAC,EAAE;IACZA,MAAM,GAAG,CAAC;EACd;EACA,OAAOF,KAAK,CAACE,MAAM,EAAEA,MAAM,EAAEC,SAAS,CAAC;AAC3C;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}