{"ast": null, "code": "var _SwuiTdBooleanWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./boolean.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdBooleanWidgetComponent = (_SwuiTdBooleanWidgetComponent = class SwuiTdBooleanWidgetComponent {\n  constructor({\n    value,\n    row,\n    schema\n  }) {\n    this.value = value;\n    if (schema.td && schema.td.titleFn) {\n      this.tooltipText = schema.td.titleFn(row);\n    }\n  }\n}, _SwuiTdBooleanWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdBooleanWidgetComponent);\nSwuiTdBooleanWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-boolean-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdBooleanWidgetComponent);\nexport { SwuiTdBooleanWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdBooleanWidgetComponent", "_SwuiTdBooleanWidgetComponent", "constructor", "value", "row", "schema", "td", "titleFn", "tooltipText", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/boolean/boolean.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./boolean.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdBooleanWidgetComponent = class SwuiTdBooleanWidgetComponent {\n    constructor({ value, row, schema }) {\n        this.value = value;\n        if (schema.td && schema.td.titleFn) {\n            this.tooltipText = schema.td.titleFn(row);\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdBooleanWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-boolean-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdBooleanWidgetComponent);\nexport { SwuiTdBooleanWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,4BAA4B,IAAAC,6BAAA,GAAG,MAAMD,4BAA4B,CAAC;EAClEE,WAAWA,CAAC;IAAEC,KAAK;IAAEC,GAAG;IAAEC;EAAO,CAAC,EAAE;IAChC,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAIE,MAAM,CAACC,EAAE,IAAID,MAAM,CAACC,EAAE,CAACC,OAAO,EAAE;MAChC,IAAI,CAACC,WAAW,GAAGH,MAAM,CAACC,EAAE,CAACC,OAAO,CAACH,GAAG,CAAC;IAC7C;EACJ;AAIJ,CAAC,EAHYH,6BAAA,CAAKQ,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEZ,MAAM;IAAEe,IAAI,EAAE,CAACd,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,6BAAA,CACJ;AACDD,4BAA4B,GAAGL,UAAU,CAAC,CACtCE,SAAS,CAAC;EACNiB,QAAQ,EAAE,4BAA4B;EACtCC,QAAQ,EAAEnB,oBAAoB;EAC9BoB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEhB,4BAA4B,CAAC;AAChC,SAASA,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}