{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiCalendarComponent } from './swui-calendar.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nexport const CALENDAR_MODULES = [MatButtonModule, MatIconModule];\nlet SwuiCalendarModule = class SwuiCalendarModule {};\nSwuiCalendarModule = __decorate([NgModule({\n  declarations: [SwuiCalendarComponent],\n  imports: [CommonModule, ...CALENDAR_MODULES],\n  exports: [SwuiCalendarComponent]\n})], SwuiCalendarModule);\nexport { SwuiCalendarModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "SwuiCalendarComponent", "MatIconModule", "MatButtonModule", "CALENDAR_MODULES", "SwuiCalendarModule", "__decorate", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-calendar/swui-calendar.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { SwuiCalendarComponent } from './swui-calendar.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\n\nexport const CALENDAR_MODULES = [\n  MatButtonModule,\n  MatIconModule,\n];\n\n@NgModule({\n  declarations: [SwuiCalendarComponent],\n  imports: [\n    CommonModule,\n    ...CALENDAR_MODULES,\n  ],\n  exports: [\n    SwuiCalendarComponent,\n  ]\n})\nexport class SwuiCalendarModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,OAAO,MAAMC,gBAAgB,GAAG,CAC9BD,eAAe,EACfD,aAAa,CACd;AAYM,IAAMG,kBAAkB,GAAxB,MAAMA,kBAAkB,GAC9B;AADYA,kBAAkB,GAAAC,UAAA,EAV9BP,QAAQ,CAAC;EACRQ,YAAY,EAAE,CAACN,qBAAqB,CAAC;EACrCO,OAAO,EAAE,CACPR,YAAY,EACZ,GAAGI,gBAAgB,CACpB;EACDK,OAAO,EAAE,CACPR,qBAAqB;CAExB,CAAC,C,EACWI,kBAAkB,CAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}