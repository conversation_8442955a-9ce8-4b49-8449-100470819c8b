{"ast": null, "code": "export class DefaultColumnComponent {\n  constructor() {\n    this.values = [];\n    this.params = {};\n    this.disabled = false;\n  }\n  initWithData(data) {\n    this.game = data && data.game || undefined;\n    this.values = data && data.values || [];\n    this.params = data && data.params || {};\n    this.valueChange = data && data.valueChange || undefined;\n    this.disabled = data && data.disabled || false;\n  }\n}", "map": {"version": 3, "names": ["DefaultColumnComponent", "constructor", "values", "params", "disabled", "initWithData", "data", "game", "undefined", "valueChange"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/default-column.component.ts"], "sourcesContent": ["export class DefaultColumnComponent {\n    constructor() {\n        this.values = [];\n        this.params = {};\n        this.disabled = false;\n    }\n    initWithData(data) {\n        this.game = data && data.game || undefined;\n        this.values = data && data.values || [];\n        this.params = data && data.params || {};\n        this.valueChange = data && data.valueChange || undefined;\n        this.disabled = data && data.disabled || false;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,sBAAsB,CAAC;EAChCC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACzB;EACAC,YAAYA,CAACC,IAAI,EAAE;IACf,IAAI,CAACC,IAAI,GAAGD,IAAI,IAAIA,IAAI,CAACC,IAAI,IAAIC,SAAS;IAC1C,IAAI,CAACN,MAAM,GAAGI,IAAI,IAAIA,IAAI,CAACJ,MAAM,IAAI,EAAE;IACvC,IAAI,CAACC,MAAM,GAAGG,IAAI,IAAIA,IAAI,CAACH,MAAM,IAAI,CAAC,CAAC;IACvC,IAAI,CAACM,WAAW,GAAGH,IAAI,IAAIA,IAAI,CAACG,WAAW,IAAID,SAAS;IACxD,IAAI,CAACJ,QAAQ,GAAGE,IAAI,IAAIA,IAAI,CAACF,QAAQ,IAAI,KAAK;EAClD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}