{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { from } from './from';\nimport { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { popResultSelector, popScheduler } from '../util/args';\nimport { createObject } from '../util/createObject';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function combineLatest() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = popScheduler(args);\n  var resultSelector = popResultSelector(args);\n  var _a = argsArgArrayOrObject(args),\n    observables = _a.args,\n    keys = _a.keys;\n  if (observables.length === 0) {\n    return from([], scheduler);\n  }\n  var result = new Observable(combineLatestInit(observables, scheduler, keys ? function (values) {\n    return createObject(keys, values);\n  } : identity));\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\nexport function combineLatestInit(observables, scheduler, valueTransform) {\n  if (valueTransform === void 0) {\n    valueTransform = identity;\n  }\n  return function (subscriber) {\n    maybeSchedule(scheduler, function () {\n      var length = observables.length;\n      var values = new Array(length);\n      var active = length;\n      var remainingFirstValues = length;\n      var _loop_1 = function (i) {\n        maybeSchedule(scheduler, function () {\n          var source = from(observables[i], scheduler);\n          var hasFirstValue = false;\n          source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n            values[i] = value;\n            if (!hasFirstValue) {\n              hasFirstValue = true;\n              remainingFirstValues--;\n            }\n            if (!remainingFirstValues) {\n              subscriber.next(valueTransform(values.slice()));\n            }\n          }, function () {\n            if (! --active) {\n              subscriber.complete();\n            }\n          }));\n        }, subscriber);\n      };\n      for (var i = 0; i < length; i++) {\n        _loop_1(i);\n      }\n    }, subscriber);\n  };\n}\nfunction maybeSchedule(scheduler, execute, subscription) {\n  if (scheduler) {\n    executeSchedule(subscription, scheduler, execute);\n  } else {\n    execute();\n  }\n}\n//# sourceMappingURL=combineLatest.js.map", "map": {"version": 3, "names": ["Observable", "argsArgArrayOrObject", "from", "identity", "mapOneOrManyArgs", "popResultSelector", "popScheduler", "createObject", "createOperatorSubscriber", "executeSchedule", "combineLatest", "args", "_i", "arguments", "length", "scheduler", "resultSelector", "_a", "observables", "keys", "result", "combineLatestInit", "values", "pipe", "valueTransform", "subscriber", "maybeSchedule", "Array", "active", "remainingFirstValues", "_loop_1", "i", "source", "hasFirstValue", "subscribe", "value", "next", "slice", "complete", "execute", "subscription"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/combineLatest.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { from } from './from';\nimport { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { popResultSelector, popScheduler } from '../util/args';\nimport { createObject } from '../util/createObject';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function combineLatest() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var scheduler = popScheduler(args);\n    var resultSelector = popResultSelector(args);\n    var _a = argsArgArrayOrObject(args), observables = _a.args, keys = _a.keys;\n    if (observables.length === 0) {\n        return from([], scheduler);\n    }\n    var result = new Observable(combineLatestInit(observables, scheduler, keys\n        ?\n            function (values) { return createObject(keys, values); }\n        :\n            identity));\n    return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\nexport function combineLatestInit(observables, scheduler, valueTransform) {\n    if (valueTransform === void 0) { valueTransform = identity; }\n    return function (subscriber) {\n        maybeSchedule(scheduler, function () {\n            var length = observables.length;\n            var values = new Array(length);\n            var active = length;\n            var remainingFirstValues = length;\n            var _loop_1 = function (i) {\n                maybeSchedule(scheduler, function () {\n                    var source = from(observables[i], scheduler);\n                    var hasFirstValue = false;\n                    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n                        values[i] = value;\n                        if (!hasFirstValue) {\n                            hasFirstValue = true;\n                            remainingFirstValues--;\n                        }\n                        if (!remainingFirstValues) {\n                            subscriber.next(valueTransform(values.slice()));\n                        }\n                    }, function () {\n                        if (!--active) {\n                            subscriber.complete();\n                        }\n                    }));\n                }, subscriber);\n            };\n            for (var i = 0; i < length; i++) {\n                _loop_1(i);\n            }\n        }, subscriber);\n    };\n}\nfunction maybeSchedule(scheduler, execute, subscription) {\n    if (scheduler) {\n        executeSchedule(subscription, scheduler, execute);\n    }\n    else {\n        execute();\n    }\n}\n//# sourceMappingURL=combineLatest.js.map"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,cAAc;AAC9D,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC5B,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,IAAIG,SAAS,GAAGT,YAAY,CAACK,IAAI,CAAC;EAClC,IAAIK,cAAc,GAAGX,iBAAiB,CAACM,IAAI,CAAC;EAC5C,IAAIM,EAAE,GAAGhB,oBAAoB,CAACU,IAAI,CAAC;IAAEO,WAAW,GAAGD,EAAE,CAACN,IAAI;IAAEQ,IAAI,GAAGF,EAAE,CAACE,IAAI;EAC1E,IAAID,WAAW,CAACJ,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOZ,IAAI,CAAC,EAAE,EAAEa,SAAS,CAAC;EAC9B;EACA,IAAIK,MAAM,GAAG,IAAIpB,UAAU,CAACqB,iBAAiB,CAACH,WAAW,EAAEH,SAAS,EAAEI,IAAI,GAElE,UAAUG,MAAM,EAAE;IAAE,OAAOf,YAAY,CAACY,IAAI,EAAEG,MAAM,CAAC;EAAE,CAAC,GAExDnB,QAAQ,CAAC,CAAC;EAClB,OAAOa,cAAc,GAAGI,MAAM,CAACG,IAAI,CAACnB,gBAAgB,CAACY,cAAc,CAAC,CAAC,GAAGI,MAAM;AAClF;AACA,OAAO,SAASC,iBAAiBA,CAACH,WAAW,EAAEH,SAAS,EAAES,cAAc,EAAE;EACtE,IAAIA,cAAc,KAAK,KAAK,CAAC,EAAE;IAAEA,cAAc,GAAGrB,QAAQ;EAAE;EAC5D,OAAO,UAAUsB,UAAU,EAAE;IACzBC,aAAa,CAACX,SAAS,EAAE,YAAY;MACjC,IAAID,MAAM,GAAGI,WAAW,CAACJ,MAAM;MAC/B,IAAIQ,MAAM,GAAG,IAAIK,KAAK,CAACb,MAAM,CAAC;MAC9B,IAAIc,MAAM,GAAGd,MAAM;MACnB,IAAIe,oBAAoB,GAAGf,MAAM;MACjC,IAAIgB,OAAO,GAAG,SAAAA,CAAUC,CAAC,EAAE;QACvBL,aAAa,CAACX,SAAS,EAAE,YAAY;UACjC,IAAIiB,MAAM,GAAG9B,IAAI,CAACgB,WAAW,CAACa,CAAC,CAAC,EAAEhB,SAAS,CAAC;UAC5C,IAAIkB,aAAa,GAAG,KAAK;UACzBD,MAAM,CAACE,SAAS,CAAC1B,wBAAwB,CAACiB,UAAU,EAAE,UAAUU,KAAK,EAAE;YACnEb,MAAM,CAACS,CAAC,CAAC,GAAGI,KAAK;YACjB,IAAI,CAACF,aAAa,EAAE;cAChBA,aAAa,GAAG,IAAI;cACpBJ,oBAAoB,EAAE;YAC1B;YACA,IAAI,CAACA,oBAAoB,EAAE;cACvBJ,UAAU,CAACW,IAAI,CAACZ,cAAc,CAACF,MAAM,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC;YACnD;UACJ,CAAC,EAAE,YAAY;YACX,IAAI,CAAC,GAAET,MAAM,EAAE;cACXH,UAAU,CAACa,QAAQ,CAAC,CAAC;YACzB;UACJ,CAAC,CAAC,CAAC;QACP,CAAC,EAAEb,UAAU,CAAC;MAClB,CAAC;MACD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,MAAM,EAAEiB,CAAC,EAAE,EAAE;QAC7BD,OAAO,CAACC,CAAC,CAAC;MACd;IACJ,CAAC,EAAEN,UAAU,CAAC;EAClB,CAAC;AACL;AACA,SAASC,aAAaA,CAACX,SAAS,EAAEwB,OAAO,EAAEC,YAAY,EAAE;EACrD,IAAIzB,SAAS,EAAE;IACXN,eAAe,CAAC+B,YAAY,EAAEzB,SAAS,EAAEwB,OAAO,CAAC;EACrD,CAAC,MACI;IACDA,OAAO,CAAC,CAAC;EACb;AACJ;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}