{"ast": null, "code": "var _DocsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./docs.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsComponent = (_DocsComponent = class DocsComponent {\n  constructor() {}\n  ngOnInit() {}\n}, _DocsComponent.ctorParameters = () => [], _DocsComponent);\nDocsComponent = __decorate([Component({\n  selector: 'lib-docs',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DocsComponent);\nexport { DocsComponent };", "map": {"version": 3, "names": ["Component", "DocsComponent", "_DocsComponent", "constructor", "ngOnInit", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/styles/docs/docs.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n    selector: 'lib-docs',\n    templateUrl: './docs.component.html',\n    styleUrls: ['./docs.component.scss'],\n    standalone: false\n})\nexport class DocsComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,QAAgB,eAAe;AAQ1C,IAAMC,aAAa,IAAAC,cAAA,GAAnB,MAAMD,aAAa;EAExBE,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;;AALWH,aAAa,GAAAI,UAAA,EANzBL,SAAS,CAAC;EACPM,QAAQ,EAAE,UAAU;EACpBC,QAAA,EAAAC,oBAAoC;EAEpCC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWR,aAAa,CAOzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}