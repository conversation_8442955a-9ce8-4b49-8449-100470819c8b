{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Afrikaans [af]\n//! author : <PERSON> : https://github.com/wernerm\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var af = moment.defineLocale('af', {\n    months: 'Januarie_Februarie_Maart_April_Mei_Junie_<PERSON>_<PERSON>_September_Oktober_November_Desember'.split('_'),\n    monthsShort: 'Jan_Feb_Mrt_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des'.split('_'),\n    weekdays: 'Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag'.split('_'),\n    weekdaysShort: '<PERSON>_<PERSON><PERSON>_<PERSON>_Woe_Don_Vry_Sat'.split('_'),\n    weekdaysMin: 'So_Ma_Di_Wo_Do_Vr_Sa'.split('_'),\n    meridiemParse: /vm|nm/i,\n    isPM: function (input) {\n      return /^nm$/i.test(input);\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 12) {\n        return isLower ? 'vm' : 'VM';\n      } else {\n        return isLower ? 'nm' : 'NM';\n      }\n    },\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Vandag om] LT',\n      nextDay: '[Môre om] LT',\n      nextWeek: 'dddd [om] LT',\n      lastDay: '[Gister om] LT',\n      lastWeek: '[Laas] dddd [om] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'oor %s',\n      past: '%s gelede',\n      s: \"'n paar sekondes\",\n      ss: '%d sekondes',\n      m: \"'n minuut\",\n      mm: '%d minute',\n      h: \"'n uur\",\n      hh: '%d ure',\n      d: \"'n dag\",\n      dd: '%d dae',\n      M: \"'n maand\",\n      MM: '%d maande',\n      y: \"'n jaar\",\n      yy: '%d jaar'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n    ordinal: function (number) {\n      return number + (number === 1 || number === 8 || number >= 20 ? 'ste' : 'de'); // Thanks to Joris Röling : https://github.com/jjupiter\n    },\n    week: {\n      dow: 1,\n      // Maandag is die eerste dag van die week.\n      doy: 4 // Die week wat die 4de Januarie bevat is die eerste week van die jaar.\n    }\n  });\n  return af;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "af", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "meridiemParse", "isPM", "input", "test", "meridiem", "hours", "minutes", "isLower", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "week", "dow", "doy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/moment/locale/af.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Afrikaans [af]\n//! author : <PERSON> : https://github.com/wernerm\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var af = moment.defineLocale('af', {\n        months: 'Januarie_Februarie_Maart_April_Mei_Junie_<PERSON>_<PERSON>_September_Oktober_November_Desember'.split(\n            '_'\n        ),\n        monthsShort: 'Jan_Feb_Mrt_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des'.split('_'),\n        weekdays: 'Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag'.split(\n            '_'\n        ),\n        weekdaysShort: '<PERSON>_<PERSON><PERSON>_<PERSON>_Woe_Don_Vry_Sat'.split('_'),\n        weekdaysMin: 'So_Ma_Di_Wo_Do_Vr_Sa'.split('_'),\n        meridiemParse: /vm|nm/i,\n        isPM: function (input) {\n            return /^nm$/i.test(input);\n        },\n        meridiem: function (hours, minutes, isLower) {\n            if (hours < 12) {\n                return isLower ? 'vm' : 'VM';\n            } else {\n                return isLower ? 'nm' : 'NM';\n            }\n        },\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Vandag om] LT',\n            nextDay: '[Môre om] LT',\n            nextWeek: 'dddd [om] LT',\n            lastDay: '[Gister om] LT',\n            lastWeek: '[Laas] dddd [om] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'oor %s',\n            past: '%s gelede',\n            s: \"'n paar sekondes\",\n            ss: '%d sekondes',\n            m: \"'n minuut\",\n            mm: '%d minute',\n            h: \"'n uur\",\n            hh: '%d ure',\n            d: \"'n dag\",\n            dd: '%d dae',\n            M: \"'n maand\",\n            MM: '%d maande',\n            y: \"'n jaar\",\n            yy: '%d jaar',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n        ordinal: function (number) {\n            return (\n                number +\n                (number === 1 || number === 8 || number >= 20 ? 'ste' : 'de')\n            ); // Thanks to Joris Röling : https://github.com/jjupiter\n        },\n        week: {\n            dow: 1, // Maandag is die eerste dag van die week.\n            doy: 4, // Die week wat die 4de Januarie bevat is die eerste week van die jaar.\n        },\n    });\n\n    return af;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,6FAA6F,CAACC,KAAK,CACvG,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,2DAA2D,CAACF,KAAK,CACvE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,aAAa,EAAE,QAAQ;IACvBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,OAAO,CAACC,IAAI,CAACD,KAAK,CAAC;IAC9B,CAAC;IACDE,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAOE,OAAO,GAAG,IAAI,GAAG,IAAI;MAChC,CAAC,MAAM;QACH,OAAOA,OAAO,GAAG,IAAI,GAAG,IAAI;MAChC;IACJ,CAAC;IACDC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,qBAAqB;MAC/BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,WAAW;MACjBC,CAAC,EAAE,kBAAkB;MACrBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,iBAAiB;IACzCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,OACIA,MAAM,IACLA,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,IAAIA,MAAM,IAAI,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC,CAC/D,CAAC;IACP,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOlD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}