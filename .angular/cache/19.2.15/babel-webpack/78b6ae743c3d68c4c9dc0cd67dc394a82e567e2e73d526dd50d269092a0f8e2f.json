{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar AsapScheduler = function (_super) {\n  __extends(AsapScheduler, _super);\n  function AsapScheduler() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  AsapScheduler.prototype.flush = function (action) {\n    this._active = true;\n    var flushId = this._scheduled;\n    this._scheduled = undefined;\n    var actions = this.actions;\n    var error;\n    action = action || actions.shift();\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n    this._active = false;\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  return AsapScheduler;\n}(AsyncScheduler);\nexport { AsapScheduler };\n//# sourceMappingURL=AsapScheduler.js.map", "map": {"version": 3, "names": ["__extends", "AsyncScheduler", "AsapScheduler", "_super", "apply", "arguments", "prototype", "flush", "action", "_active", "flushId", "_scheduled", "undefined", "actions", "error", "shift", "execute", "state", "delay", "id", "unsubscribe"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/AsapScheduler.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar AsapScheduler = (function (_super) {\n    __extends(AsapScheduler, _super);\n    function AsapScheduler() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AsapScheduler.prototype.flush = function (action) {\n        this._active = true;\n        var flushId = this._scheduled;\n        this._scheduled = undefined;\n        var actions = this.actions;\n        var error;\n        action = action || actions.shift();\n        do {\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        } while ((action = actions[0]) && action.id === flushId && actions.shift());\n        this._active = false;\n        if (error) {\n            while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    };\n    return AsapScheduler;\n}(AsyncScheduler));\nexport { AsapScheduler };\n//# sourceMappingURL=AsapScheduler.js.map"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,IAAIC,aAAa,GAAI,UAAUC,MAAM,EAAE;EACnCH,SAAS,CAACE,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACrB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACnE;EACAH,aAAa,CAACI,SAAS,CAACC,KAAK,GAAG,UAAUC,MAAM,EAAE;IAC9C,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAIC,OAAO,GAAG,IAAI,CAACC,UAAU;IAC7B,IAAI,CAACA,UAAU,GAAGC,SAAS;IAC3B,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIC,KAAK;IACTN,MAAM,GAAGA,MAAM,IAAIK,OAAO,CAACE,KAAK,CAAC,CAAC;IAClC,GAAG;MACC,IAAKD,KAAK,GAAGN,MAAM,CAACQ,OAAO,CAACR,MAAM,CAACS,KAAK,EAAET,MAAM,CAACU,KAAK,CAAC,EAAG;QACtD;MACJ;IACJ,CAAC,QAAQ,CAACV,MAAM,GAAGK,OAAO,CAAC,CAAC,CAAC,KAAKL,MAAM,CAACW,EAAE,KAAKT,OAAO,IAAIG,OAAO,CAACE,KAAK,CAAC,CAAC;IAC1E,IAAI,CAACN,OAAO,GAAG,KAAK;IACpB,IAAIK,KAAK,EAAE;MACP,OAAO,CAACN,MAAM,GAAGK,OAAO,CAAC,CAAC,CAAC,KAAKL,MAAM,CAACW,EAAE,KAAKT,OAAO,IAAIG,OAAO,CAACE,KAAK,CAAC,CAAC,EAAE;QACtEP,MAAM,CAACY,WAAW,CAAC,CAAC;MACxB;MACA,MAAMN,KAAK;IACf;EACJ,CAAC;EACD,OAAOZ,aAAa;AACxB,CAAC,CAACD,cAAc,CAAE;AAClB,SAASC,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}