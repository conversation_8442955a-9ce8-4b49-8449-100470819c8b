{"ast": null, "code": "import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n  return bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}\n//# sourceMappingURL=bindNodeCallback.js.map", "map": {"version": 3, "names": ["bindCallbackInternals", "bindNodeCallback", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/bindNodeCallback.js"], "sourcesContent": ["import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n    return bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}\n//# sourceMappingURL=bindNodeCallback.js.map"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,yBAAyB;AAC/D,OAAO,SAASC,gBAAgBA,CAACC,YAAY,EAAEC,cAAc,EAAEC,SAAS,EAAE;EACtE,OAAOJ,qBAAqB,CAAC,IAAI,EAAEE,YAAY,EAAEC,cAAc,EAAEC,SAAS,CAAC;AAC/E;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}