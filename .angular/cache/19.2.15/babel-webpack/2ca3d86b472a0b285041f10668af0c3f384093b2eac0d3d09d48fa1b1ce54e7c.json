{"ast": null, "code": "var _LanguageSelectorComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./language-selector.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./language-selector.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, Inject, Input, Output } from '@angular/core';\nimport { TranslateService } from '@ngx-translate/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nlet LanguageSelectorComponent = (_LanguageSelectorComponent = class LanguageSelectorComponent {\n  constructor(config, translate, elementRef, cdr) {\n    this.elementRef = elementRef;\n    this.cdr = cdr;\n    this.inMenu = false;\n    this.valueChanges = new EventEmitter();\n    this.isOpen = false;\n    this.items = config.langs || [];\n    this.active = this.items.find(({\n      id\n    }) => id === translate.currentLang);\n    translate.onLangChange.subscribe(({\n      lang\n    }) => {\n      this.active = this.items.find(({\n        id\n      }) => id === lang);\n      this.cdr.detectChanges();\n    });\n  }\n  onClick(target) {\n    const inside = this.elementRef.nativeElement.contains(target);\n    if (!inside && this.isOpen) {\n      this.isOpen = false;\n    }\n  }\n  select(item) {\n    if (item !== undefined) {\n      this.active = item;\n      this.valueChanges.emit(item.id);\n    }\n  }\n  prevent(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  triggerDropdown(event) {\n    event.preventDefault();\n    this.isOpen = !this.isOpen;\n  }\n}, _LanguageSelectorComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_HUB_MESSAGE_CONFIG]\n  }]\n}, {\n  type: TranslateService\n}, {\n  type: ElementRef\n}, {\n  type: ChangeDetectorRef\n}], _LanguageSelectorComponent.propDecorators = {\n  inMenu: [{\n    type: Input\n  }],\n  valueChanges: [{\n    type: Output\n  }],\n  onClick: [{\n    type: HostListener,\n    args: ['document:click', ['$event.target']]\n  }]\n}, _LanguageSelectorComponent);\nLanguageSelectorComponent = __decorate([Component({\n  selector: 'lib-swui-language-selector',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], LanguageSelectorComponent);\nexport { LanguageSelectorComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectionStrategy", "ChangeDetectorRef", "Component", "ElementRef", "EventEmitter", "HostListener", "Inject", "Input", "Output", "TranslateService", "SWUI_HUB_MESSAGE_CONFIG", "LanguageSelectorComponent", "_LanguageSelectorComponent", "constructor", "config", "translate", "elementRef", "cdr", "inMenu", "valueChanges", "isOpen", "items", "langs", "active", "find", "id", "currentLang", "onLangChange", "subscribe", "lang", "detectChanges", "onClick", "target", "inside", "nativeElement", "contains", "select", "item", "undefined", "emit", "prevent", "event", "preventDefault", "stopPropagation", "triggerDropdown", "ctorParameters", "type", "decorators", "args", "propDecorators", "selector", "template", "changeDetection", "OnPush", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/language-selector/language-selector.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./language-selector.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./language-selector.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, Inject, Input, Output } from '@angular/core';\nimport { TranslateService } from '@ngx-translate/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nlet LanguageSelectorComponent = class LanguageSelectorComponent {\n    constructor(config, translate, elementRef, cdr) {\n        this.elementRef = elementRef;\n        this.cdr = cdr;\n        this.inMenu = false;\n        this.valueChanges = new EventEmitter();\n        this.isOpen = false;\n        this.items = config.langs || [];\n        this.active = this.items.find(({ id }) => id === translate.currentLang);\n        translate.onLangChange.subscribe(({ lang }) => {\n            this.active = this.items.find(({ id }) => id === lang);\n            this.cdr.detectChanges();\n        });\n    }\n    onClick(target) {\n        const inside = this.elementRef.nativeElement.contains(target);\n        if (!inside && this.isOpen) {\n            this.isOpen = false;\n        }\n    }\n    select(item) {\n        if (item !== undefined) {\n            this.active = item;\n            this.valueChanges.emit(item.id);\n        }\n    }\n    prevent(event) {\n        event.preventDefault();\n        event.stopPropagation();\n    }\n    triggerDropdown(event) {\n        event.preventDefault();\n        this.isOpen = !this.isOpen;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_HUB_MESSAGE_CONFIG,] }] },\n        { type: TranslateService },\n        { type: ElementRef },\n        { type: ChangeDetectorRef }\n    ]; }\n    static { this.propDecorators = {\n        inMenu: [{ type: Input }],\n        valueChanges: [{ type: Output }],\n        onClick: [{ type: HostListener, args: ['document:click', ['$event.target'],] }]\n    }; }\n};\nLanguageSelectorComponent = __decorate([\n    Component({\n        selector: 'lib-swui-language-selector',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], LanguageSelectorComponent);\nexport { LanguageSelectorComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,SAASC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,YAAY,EAAEC,YAAY,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AACpJ,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,IAAIC,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,CAAC;EAC5DE,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,GAAG,EAAE;IAC5C,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,YAAY,GAAG,IAAIf,YAAY,CAAC,CAAC;IACtC,IAAI,CAACgB,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,KAAK,GAAGP,MAAM,CAACQ,KAAK,IAAI,EAAE;IAC/B,IAAI,CAACC,MAAM,GAAG,IAAI,CAACF,KAAK,CAACG,IAAI,CAAC,CAAC;MAAEC;IAAG,CAAC,KAAKA,EAAE,KAAKV,SAAS,CAACW,WAAW,CAAC;IACvEX,SAAS,CAACY,YAAY,CAACC,SAAS,CAAC,CAAC;MAAEC;IAAK,CAAC,KAAK;MAC3C,IAAI,CAACN,MAAM,GAAG,IAAI,CAACF,KAAK,CAACG,IAAI,CAAC,CAAC;QAAEC;MAAG,CAAC,KAAKA,EAAE,KAAKI,IAAI,CAAC;MACtD,IAAI,CAACZ,GAAG,CAACa,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACAC,OAAOA,CAACC,MAAM,EAAE;IACZ,MAAMC,MAAM,GAAG,IAAI,CAACjB,UAAU,CAACkB,aAAa,CAACC,QAAQ,CAACH,MAAM,CAAC;IAC7D,IAAI,CAACC,MAAM,IAAI,IAAI,CAACb,MAAM,EAAE;MACxB,IAAI,CAACA,MAAM,GAAG,KAAK;IACvB;EACJ;EACAgB,MAAMA,CAACC,IAAI,EAAE;IACT,IAAIA,IAAI,KAAKC,SAAS,EAAE;MACpB,IAAI,CAACf,MAAM,GAAGc,IAAI;MAClB,IAAI,CAAClB,YAAY,CAACoB,IAAI,CAACF,IAAI,CAACZ,EAAE,CAAC;IACnC;EACJ;EACAe,OAAOA,CAACC,KAAK,EAAE;IACXA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACAC,eAAeA,CAACH,KAAK,EAAE;IACnBA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACtB,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;EAC9B;AAYJ,CAAC,EAXYR,0BAAA,CAAKiC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAER,SAAS;EAAES,UAAU,EAAE,CAAC;IAAED,IAAI,EAAExC,MAAM;IAAE0C,IAAI,EAAE,CAACtC,uBAAuB;EAAG,CAAC;AAAE,CAAC,EACrF;EAAEoC,IAAI,EAAErC;AAAiB,CAAC,EAC1B;EAAEqC,IAAI,EAAE3C;AAAW,CAAC,EACpB;EAAE2C,IAAI,EAAE7C;AAAkB,CAAC,CAC9B,EACQW,0BAAA,CAAKqC,cAAc,GAAG;EAC3B/B,MAAM,EAAE,CAAC;IAAE4B,IAAI,EAAEvC;EAAM,CAAC,CAAC;EACzBY,YAAY,EAAE,CAAC;IAAE2B,IAAI,EAAEtC;EAAO,CAAC,CAAC;EAChCuB,OAAO,EAAE,CAAC;IAAEe,IAAI,EAAEzC,YAAY;IAAE2C,IAAI,EAAE,CAAC,gBAAgB,EAAE,CAAC,eAAe,CAAC;EAAG,CAAC;AAClF,CAAC,EAAApC,0BAAA,CACJ;AACDD,yBAAyB,GAAGd,UAAU,CAAC,CACnCK,SAAS,CAAC;EACNgD,QAAQ,EAAE,4BAA4B;EACtCC,QAAQ,EAAErD,oBAAoB;EAC9BsD,eAAe,EAAEpD,uBAAuB,CAACqD,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACxD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEY,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}