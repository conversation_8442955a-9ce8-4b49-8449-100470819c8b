{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nexport { a as BasePortalHost, B as BasePortalOutlet, d as CdkPortal, f as CdkPortalOutlet, C as ComponentPortal, D as DomPortal, c as DomPortalHost, b as DomPortalOutlet, P as Portal, g as PortalHostDirective, h as PortalModule, T as TemplatePortal, e as TemplatePortalDirective } from './portal-directives-Bw5woq8I.mjs';\nimport '@angular/core';\nimport '@angular/common';\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\nclass PortalInjector {\n  constructor(_parentInjector, _customTokens) {\n    _defineProperty(this, \"_parentInjector\", void 0);\n    _defineProperty(this, \"_customTokens\", void 0);\n    this._parentInjector = _parentInjector;\n    this._customTokens = _customTokens;\n  }\n  get(token, notFoundValue) {\n    const value = this._customTokens.get(token);\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n    return this._parentInjector.get(token, notFoundValue);\n  }\n}\nexport { PortalInjector };\n//# sourceMappingURL=portal.mjs.map", "map": {"version": 3, "names": ["a", "BasePortalHost", "B", "BasePortalOutlet", "d", "CdkPortal", "f", "CdkPortalOutlet", "C", "ComponentPortal", "D", "<PERSON><PERSON><PERSON><PERSON>", "c", "DomPortalHost", "b", "DomPortalOutlet", "P", "Portal", "g", "PortalHostDirective", "h", "PortalModule", "T", "TemplatePortal", "e", "TemplatePortalDirective", "PortalInjector", "constructor", "_parentInjector", "_customTokens", "_defineProperty", "get", "token", "notFoundValue", "value"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/portal.mjs"], "sourcesContent": ["export { a as BasePortalHost, B as BasePortalOutlet, d as CdkPortal, f as CdkPortalOutlet, C as ComponentPortal, D as DomPortal, c as DomPortalHost, b as DomPortalOutlet, P as Portal, g as PortalHostDirective, h as PortalModule, T as TemplatePortal, e as TemplatePortalDirective } from './portal-directives-Bw5woq8I.mjs';\nimport '@angular/core';\nimport '@angular/common';\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\nclass PortalInjector {\n    _parentInjector;\n    _customTokens;\n    constructor(_parentInjector, _customTokens) {\n        this._parentInjector = _parentInjector;\n        this._customTokens = _customTokens;\n    }\n    get(token, notFoundValue) {\n        const value = this._customTokens.get(token);\n        if (typeof value !== 'undefined') {\n            return value;\n        }\n        return this._parentInjector.get(token, notFoundValue);\n    }\n}\n\nexport { PortalInjector };\n//# sourceMappingURL=portal.mjs.map\n"], "mappings": ";AAAA,SAASA,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAChU,OAAO,eAAe;AACtB,OAAO,iBAAiB;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EAGjBC,WAAWA,CAACC,eAAe,EAAEC,aAAa,EAAE;IAAAC,eAAA;IAAAA,eAAA;IACxC,IAAI,CAACF,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACAE,GAAGA,CAACC,KAAK,EAAEC,aAAa,EAAE;IACtB,MAAMC,KAAK,GAAG,IAAI,CAACL,aAAa,CAACE,GAAG,CAACC,KAAK,CAAC;IAC3C,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;MAC9B,OAAOA,KAAK;IAChB;IACA,OAAO,IAAI,CAACN,eAAe,CAACG,GAAG,CAACC,KAAK,EAAEC,aAAa,CAAC;EACzD;AACJ;AAEA,SAASP,cAAc;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}