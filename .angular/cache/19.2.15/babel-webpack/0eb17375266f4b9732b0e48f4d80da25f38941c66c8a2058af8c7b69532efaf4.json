{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { DataSource } from '@angular/cdk/collections';\nimport { HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';\nimport * as moment from 'moment';\nimport { BehaviorSubject, combineLatest, of, Subject, throwError } from 'rxjs';\nimport { catchError, debounceTime, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { LocalDataService } from './services/local-data.service';\nconst FIELD_POSTFIX_DELIM = '__';\nexport class SwuiGridDataSource extends DataSource {\n  set data(value) {\n    if (typeof value === 'undefined') {\n      return;\n    }\n    this._data = value;\n    this.dataService = new LocalDataService();\n    this.dataService.localData = this._data;\n  }\n  get data() {\n    return this._data;\n  }\n  constructor(dataService, hubEntityService, filterService, urlHandler) {\n    super();\n    this.dataService = dataService;\n    this.hubEntityService = hubEntityService;\n    this.filterService = filterService;\n    this.urlHandler = urlHandler;\n    this.total$ = new BehaviorSubject(0);\n    /**\n     * required for not replacing URL queryParams for first request - when params are already in url but they also\n     * adding via regular flow\n     */\n    this.firstRequest = true;\n    this.useHubEntity = false;\n    this.entity = null;\n    this.data$ = new BehaviorSubject([]);\n    this.loadData$ = new Subject();\n    this.destroy$ = new Subject();\n  }\n  get canUseEntitySelect() {\n    return this.useHubEntity && !!this.hubEntityService;\n  }\n  get entity$() {\n    return this.canUseEntitySelect ? this.hubEntityService.entitySelected$ : of(null);\n  }\n  get filterEntity$() {\n    const filter$ = this.filterService ? this.filterService.appliedFilter : of(null);\n    return combineLatest([filter$, this.entity$]);\n  }\n  initDatasource(useHubEntity) {\n    this.useHubEntity = useHubEntity;\n    this.loadData$.pipe(debounceTime(250), switchMap(() => {\n      if (typeof this.dataService === 'undefined' || this.dataService === null) {\n        return of(new HttpResponse({\n          body: undefined,\n          headers: new HttpHeaders()\n        }));\n      }\n      let params = this.getRequestParams();\n      let requestData = this.requestData || {};\n      if (this.entity && this.entity.path) {\n        const path = this.entity.path.charAt(0) === ':' ? this.entity.path.substring(1) : this.entity.path;\n        const type = this.entity.type;\n        params = _objectSpread(_objectSpread({}, params), {}, {\n          path: this.entity.path\n        });\n        if (path) {\n          requestData = _objectSpread(_objectSpread({}, requestData), {}, {\n            path,\n            type\n          });\n        }\n      }\n      this.urlHandler.setParams(params, this.firstRequest ? 'merge' : '');\n      let httpParams = new HttpParams({\n        fromObject: params\n      });\n      const request = this.dataService ? this.dataService.getGridData(httpParams, requestData) : throwError('');\n      return request.pipe(take(1), catchError(() => of(new HttpResponse({\n        body: undefined,\n        headers: new HttpHeaders()\n      }))), tap(() => this.firstRequest = false));\n    }), takeUntil(this.destroy$)).subscribe(({\n      body,\n      headers\n    }) => {\n      var _this$data;\n      this._data = body || undefined;\n      this.data$.next(body || []);\n      this.total$.next(Number(headers.get('x-paging-total') || '0'));\n      if (!((_this$data = this.data) !== null && _this$data !== void 0 && _this$data.length) && this.paginator && this.paginator.pageIndex !== 0) {\n        this.paginator.pageIndex = this.paginator.pageIndex - 1;\n        this.loadData();\n      }\n    });\n    this.subscribeToFilter();\n  }\n  connect() {\n    return this.data$.asObservable();\n  }\n  disconnect() {\n    this.data$.complete();\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n  addSort(sort) {\n    this.sort = sort;\n    this.sort.sortChange.subscribe(() => this.loadData());\n  }\n  addPaginator(paginator, blind = false) {\n    this.paginator = paginator;\n    this.paginator.page.subscribe(() => this.loadData());\n    this.total$.asObservable().subscribe(total => {\n      if (!this.paginator) {\n        return;\n      }\n      if (!blind) {\n        this.paginator.length = total;\n        return;\n      }\n      const previousLength = this.paginator.pageSize * this.paginator.pageIndex;\n      const additionalItems = (this._data || []).length === this.paginator.pageSize ? 1 : 0;\n      this.paginator.length = (this._data || []).length + additionalItems + previousLength;\n    });\n  }\n  changePageSize(pageSize) {\n    if (this.paginator) {\n      const oldPageSize = this.paginator.pageSize;\n      const oldPageIndex = this.paginator.pageIndex;\n      this.paginator.pageSize = pageSize;\n      this.paginator.pageIndex = Math.floor(oldPageIndex * oldPageSize / pageSize);\n      if (this._data && this._data.length) {\n        this.loadData();\n      }\n    }\n  }\n  loadData() {\n    this.loadData$.next(undefined);\n  }\n  subscribeToFilter() {\n    if (this.filterService !== null) {\n      this.filterService.displayedFilter.pipe(takeUntil(this.destroy$)).subscribe(filterData => {\n        this.filterData = filterData;\n      });\n      this.filterEntity$.pipe(takeUntil(this.destroy$)).subscribe(([, entity]) => {\n        this.entity = entity;\n        if (!this.firstRequest) {\n          if (this.paginator) {\n            this.paginator.pageIndex = 0;\n          }\n        }\n        this.loadData();\n      });\n      return;\n    }\n    if (this.canUseEntitySelect) {\n      this.hubEntityService.entitySelected$.pipe(takeUntil(this.destroy$)).subscribe(entity => {\n        this.entity = entity;\n        this.loadData();\n      });\n    }\n  }\n  getRequestParams() {\n    let params = {};\n    // applying filter data\n    if (this.filterData) {\n      params = this.processFilterParams(this.filterData, params);\n    }\n    // applying paginator values\n    if (this.paginator) {\n      const {\n        pageIndex,\n        pageSize\n      } = this.paginator;\n      params['offset'] = (pageIndex * pageSize).toString();\n      params['limit'] = pageSize.toString();\n    } else if (this.data) {\n      params['offset'] = 0;\n    }\n    // applying sorting\n    if (this.sort) {\n      const {\n        active,\n        direction\n      } = this.sort;\n      if (direction !== '') {\n        params['sortOrder'] = direction.toUpperCase();\n        params['sortBy'] = active;\n      }\n    }\n    return params;\n  }\n  processFilterParams(filterData, initialParams) {\n    return Object.keys(filterData).filter(key => {\n      const value = filterData[key];\n      return typeof value !== 'undefined' && value !== null && value !== '' && key !== 'undefined';\n    }).reduce((params, key) => {\n      let value = filterData[key];\n      if (typeof value === 'object' && Object.keys(value).every(k => k.includes(FIELD_POSTFIX_DELIM))) {\n        params = this.processFilterParams(value, params);\n      } else {\n        if (moment.isMoment(value)) {\n          value = value.toISOString();\n        }\n        params[key] = value;\n      }\n      return params;\n    }, initialParams);\n  }\n}", "map": {"version": 3, "names": ["DataSource", "HttpHeaders", "HttpParams", "HttpResponse", "moment", "BehaviorSubject", "combineLatest", "of", "Subject", "throwError", "catchError", "debounceTime", "switchMap", "take", "takeUntil", "tap", "LocalDataService", "FIELD_POSTFIX_DELIM", "SwuiGridDataSource", "data", "value", "_data", "dataService", "localData", "constructor", "hubEntityService", "filterService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "total$", "firstRequest", "useHubEntity", "entity", "data$", "loadData$", "destroy$", "canUseEntitySelect", "entity$", "entitySelected$", "filterEntity$", "filter$", "appliedFilter", "initDatasource", "pipe", "body", "undefined", "headers", "params", "getRequestParams", "requestData", "path", "char<PERSON>t", "substring", "type", "_objectSpread", "setParams", "httpParams", "fromObject", "request", "getGridData", "subscribe", "_this$data", "next", "Number", "get", "length", "paginator", "pageIndex", "loadData", "subscribeToFilter", "connect", "asObservable", "disconnect", "complete", "addSort", "sort", "sortChange", "addPaginator", "blind", "page", "total", "<PERSON><PERSON><PERSON><PERSON>", "pageSize", "additionalItems", "changePageSize", "oldPageSize", "oldPageIndex", "Math", "floor", "displayedFilter", "filterData", "processFilterParams", "toString", "active", "direction", "toUpperCase", "initialParams", "Object", "keys", "filter", "key", "reduce", "every", "k", "includes", "isMoment", "toISOString"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid.datasource.ts"], "sourcesContent": ["import { DataSource } from '@angular/cdk/collections';\nimport { HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';\nimport * as moment from 'moment';\nimport { BehaviorSubject, combineLatest, of, Subject, throwError } from 'rxjs';\nimport { catchError, debounceTime, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { LocalDataService } from './services/local-data.service';\nconst FIELD_POSTFIX_DELIM = '__';\nexport class SwuiGridDataSource extends DataSource {\n    set data(value) {\n        if (typeof value === 'undefined') {\n            return;\n        }\n        this._data = value;\n        this.dataService = new LocalDataService();\n        this.dataService.localData = this._data;\n    }\n    get data() {\n        return this._data;\n    }\n    constructor(dataService, hubEntityService, filterService, urlHandler) {\n        super();\n        this.dataService = dataService;\n        this.hubEntityService = hubEntityService;\n        this.filterService = filterService;\n        this.urlHandler = urlHandler;\n        this.total$ = new BehaviorSubject(0);\n        /**\n         * required for not replacing URL queryParams for first request - when params are already in url but they also\n         * adding via regular flow\n         */\n        this.firstRequest = true;\n        this.useHubEntity = false;\n        this.entity = null;\n        this.data$ = new BehaviorSubject([]);\n        this.loadData$ = new Subject();\n        this.destroy$ = new Subject();\n    }\n    get canUseEntitySelect() {\n        return this.useHubEntity && !!this.hubEntityService;\n    }\n    get entity$() {\n        return this.canUseEntitySelect ? this.hubEntityService.entitySelected$ : of(null);\n    }\n    get filterEntity$() {\n        const filter$ = this.filterService ? this.filterService.appliedFilter : of(null);\n        return combineLatest([filter$, this.entity$]);\n    }\n    initDatasource(useHubEntity) {\n        this.useHubEntity = useHubEntity;\n        this.loadData$.pipe(debounceTime(250), switchMap(() => {\n            if (typeof this.dataService === 'undefined' || this.dataService === null) {\n                return of(new HttpResponse({\n                    body: undefined,\n                    headers: new HttpHeaders()\n                }));\n            }\n            let params = this.getRequestParams();\n            let requestData = this.requestData || {};\n            if (this.entity && this.entity.path) {\n                const path = this.entity.path.charAt(0) === ':' ? this.entity.path.substring(1) : this.entity.path;\n                const type = this.entity.type;\n                params = { ...params, path: this.entity.path };\n                if (path) {\n                    requestData = { ...requestData, path, type };\n                }\n            }\n            this.urlHandler.setParams(params, this.firstRequest ? 'merge' : '');\n            let httpParams = new HttpParams({ fromObject: params });\n            const request = this.dataService ? this.dataService.getGridData(httpParams, requestData) : throwError('');\n            return request.pipe(take(1), catchError(() => of(new HttpResponse({\n                body: undefined,\n                headers: new HttpHeaders()\n            }))), tap(() => this.firstRequest = false));\n        }), takeUntil(this.destroy$)).subscribe(({ body, headers }) => {\n            this._data = body || undefined;\n            this.data$.next(body || []);\n            this.total$.next(Number(headers.get('x-paging-total') || '0'));\n            if (!this.data?.length && this.paginator && this.paginator.pageIndex !== 0) {\n                this.paginator.pageIndex = (this.paginator.pageIndex - 1);\n                this.loadData();\n            }\n        });\n        this.subscribeToFilter();\n    }\n    connect() {\n        return this.data$.asObservable();\n    }\n    disconnect() {\n        this.data$.complete();\n        this.destroy$.next(undefined);\n        this.destroy$.complete();\n    }\n    addSort(sort) {\n        this.sort = sort;\n        this.sort.sortChange.subscribe(() => this.loadData());\n    }\n    addPaginator(paginator, blind = false) {\n        this.paginator = paginator;\n        this.paginator.page.subscribe(() => this.loadData());\n        this.total$.asObservable().subscribe((total) => {\n            if (!this.paginator) {\n                return;\n            }\n            if (!blind) {\n                this.paginator.length = total;\n                return;\n            }\n            const previousLength = this.paginator.pageSize * this.paginator.pageIndex;\n            const additionalItems = (this._data || []).length === this.paginator.pageSize ? 1 : 0;\n            this.paginator.length = (this._data || []).length + additionalItems + previousLength;\n        });\n    }\n    changePageSize(pageSize) {\n        if (this.paginator) {\n            const oldPageSize = this.paginator.pageSize;\n            const oldPageIndex = this.paginator.pageIndex;\n            this.paginator.pageSize = pageSize;\n            this.paginator.pageIndex = Math.floor((oldPageIndex * oldPageSize) / pageSize);\n            if (this._data && this._data.length) {\n                this.loadData();\n            }\n        }\n    }\n    loadData() {\n        this.loadData$.next(undefined);\n    }\n    subscribeToFilter() {\n        if (this.filterService !== null) {\n            this.filterService.displayedFilter\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(filterData => {\n                this.filterData = filterData;\n            });\n            this.filterEntity$\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(([, entity]) => {\n                this.entity = entity;\n                if (!this.firstRequest) {\n                    if (this.paginator) {\n                        this.paginator.pageIndex = 0;\n                    }\n                }\n                this.loadData();\n            });\n            return;\n        }\n        if (this.canUseEntitySelect) {\n            this.hubEntityService.entitySelected$\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(entity => {\n                this.entity = entity;\n                this.loadData();\n            });\n        }\n    }\n    getRequestParams() {\n        let params = {};\n        // applying filter data\n        if (this.filterData) {\n            params = this.processFilterParams(this.filterData, params);\n        }\n        // applying paginator values\n        if (this.paginator) {\n            const { pageIndex, pageSize } = this.paginator;\n            params['offset'] = (pageIndex * pageSize).toString();\n            params['limit'] = pageSize.toString();\n        }\n        else if (this.data) {\n            params['offset'] = 0;\n        }\n        // applying sorting\n        if (this.sort) {\n            const { active, direction } = this.sort;\n            if (direction !== '') {\n                params['sortOrder'] = direction.toUpperCase();\n                params['sortBy'] = active;\n            }\n        }\n        return params;\n    }\n    processFilterParams(filterData, initialParams) {\n        return Object.keys(filterData)\n            .filter((key) => {\n            const value = filterData[key];\n            return typeof value !== 'undefined' && value !== null && value !== '' && key !== 'undefined';\n        })\n            .reduce((params, key) => {\n            let value = filterData[key];\n            if (typeof value === 'object' && Object.keys(value).every(k => k.includes(FIELD_POSTFIX_DELIM))) {\n                params = this.processFilterParams(value, params);\n            }\n            else {\n                if (moment.isMoment(value)) {\n                    value = value.toISOString();\n                }\n                params[key] = value;\n            }\n            return params;\n        }, initialParams);\n    }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,0BAA0B;AACrD,SAASC,WAAW,EAAEC,UAAU,EAAEC,YAAY,QAAQ,sBAAsB;AAC5E,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,eAAe,EAAEC,aAAa,EAAEC,EAAE,EAAEC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC9E,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AAC1F,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,MAAMC,mBAAmB,GAAG,IAAI;AAChC,OAAO,MAAMC,kBAAkB,SAASlB,UAAU,CAAC;EAC/C,IAAImB,IAAIA,CAACC,KAAK,EAAE;IACZ,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MAC9B;IACJ;IACA,IAAI,CAACC,KAAK,GAAGD,KAAK;IAClB,IAAI,CAACE,WAAW,GAAG,IAAIN,gBAAgB,CAAC,CAAC;IACzC,IAAI,CAACM,WAAW,CAACC,SAAS,GAAG,IAAI,CAACF,KAAK;EAC3C;EACA,IAAIF,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACE,KAAK;EACrB;EACAG,WAAWA,CAACF,WAAW,EAAEG,gBAAgB,EAAEC,aAAa,EAAEC,UAAU,EAAE;IAClE,KAAK,CAAC,CAAC;IACP,IAAI,CAACL,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACG,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAG,IAAIvB,eAAe,CAAC,CAAC,CAAC;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACwB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,KAAK,GAAG,IAAI3B,eAAe,CAAC,EAAE,CAAC;IACpC,IAAI,CAAC4B,SAAS,GAAG,IAAIzB,OAAO,CAAC,CAAC;IAC9B,IAAI,CAAC0B,QAAQ,GAAG,IAAI1B,OAAO,CAAC,CAAC;EACjC;EACA,IAAI2B,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACL,YAAY,IAAI,CAAC,CAAC,IAAI,CAACL,gBAAgB;EACvD;EACA,IAAIW,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,kBAAkB,GAAG,IAAI,CAACV,gBAAgB,CAACY,eAAe,GAAG9B,EAAE,CAAC,IAAI,CAAC;EACrF;EACA,IAAI+B,aAAaA,CAAA,EAAG;IAChB,MAAMC,OAAO,GAAG,IAAI,CAACb,aAAa,GAAG,IAAI,CAACA,aAAa,CAACc,aAAa,GAAGjC,EAAE,CAAC,IAAI,CAAC;IAChF,OAAOD,aAAa,CAAC,CAACiC,OAAO,EAAE,IAAI,CAACH,OAAO,CAAC,CAAC;EACjD;EACAK,cAAcA,CAACX,YAAY,EAAE;IACzB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACG,SAAS,CAACS,IAAI,CAAC/B,YAAY,CAAC,GAAG,CAAC,EAAEC,SAAS,CAAC,MAAM;MACnD,IAAI,OAAO,IAAI,CAACU,WAAW,KAAK,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,IAAI,EAAE;QACtE,OAAOf,EAAE,CAAC,IAAIJ,YAAY,CAAC;UACvBwC,IAAI,EAAEC,SAAS;UACfC,OAAO,EAAE,IAAI5C,WAAW,CAAC;QAC7B,CAAC,CAAC,CAAC;MACP;MACA,IAAI6C,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACpC,IAAIC,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;MACxC,IAAI,IAAI,CAACjB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACkB,IAAI,EAAE;QACjC,MAAMA,IAAI,GAAG,IAAI,CAAClB,MAAM,CAACkB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,CAACnB,MAAM,CAACkB,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACpB,MAAM,CAACkB,IAAI;QAClG,MAAMG,IAAI,GAAG,IAAI,CAACrB,MAAM,CAACqB,IAAI;QAC7BN,MAAM,GAAAO,aAAA,CAAAA,aAAA,KAAQP,MAAM;UAAEG,IAAI,EAAE,IAAI,CAAClB,MAAM,CAACkB;QAAI,EAAE;QAC9C,IAAIA,IAAI,EAAE;UACND,WAAW,GAAAK,aAAA,CAAAA,aAAA,KAAQL,WAAW;YAAEC,IAAI;YAAEG;UAAI,EAAE;QAChD;MACJ;MACA,IAAI,CAACzB,UAAU,CAAC2B,SAAS,CAACR,MAAM,EAAE,IAAI,CAACjB,YAAY,GAAG,OAAO,GAAG,EAAE,CAAC;MACnE,IAAI0B,UAAU,GAAG,IAAIrD,UAAU,CAAC;QAAEsD,UAAU,EAAEV;MAAO,CAAC,CAAC;MACvD,MAAMW,OAAO,GAAG,IAAI,CAACnC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACoC,WAAW,CAACH,UAAU,EAAEP,WAAW,CAAC,GAAGvC,UAAU,CAAC,EAAE,CAAC;MACzG,OAAOgD,OAAO,CAACf,IAAI,CAAC7B,IAAI,CAAC,CAAC,CAAC,EAAEH,UAAU,CAAC,MAAMH,EAAE,CAAC,IAAIJ,YAAY,CAAC;QAC9DwC,IAAI,EAAEC,SAAS;QACfC,OAAO,EAAE,IAAI5C,WAAW,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,EAAEc,GAAG,CAAC,MAAM,IAAI,CAACc,YAAY,GAAG,KAAK,CAAC,CAAC;IAC/C,CAAC,CAAC,EAAEf,SAAS,CAAC,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAACyB,SAAS,CAAC,CAAC;MAAEhB,IAAI;MAAEE;IAAQ,CAAC,KAAK;MAAA,IAAAe,UAAA;MAC3D,IAAI,CAACvC,KAAK,GAAGsB,IAAI,IAAIC,SAAS;MAC9B,IAAI,CAACZ,KAAK,CAAC6B,IAAI,CAAClB,IAAI,IAAI,EAAE,CAAC;MAC3B,IAAI,CAACf,MAAM,CAACiC,IAAI,CAACC,MAAM,CAACjB,OAAO,CAACkB,GAAG,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC,CAAC;MAC9D,IAAI,GAAAH,UAAA,GAAC,IAAI,CAACzC,IAAI,cAAAyC,UAAA,eAATA,UAAA,CAAWI,MAAM,KAAI,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACC,SAAS,KAAK,CAAC,EAAE;QACxE,IAAI,CAACD,SAAS,CAACC,SAAS,GAAI,IAAI,CAACD,SAAS,CAACC,SAAS,GAAG,CAAE;QACzD,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB;IACJ,CAAC,CAAC;IACF,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC5B;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACrC,KAAK,CAACsC,YAAY,CAAC,CAAC;EACpC;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACvC,KAAK,CAACwC,QAAQ,CAAC,CAAC;IACrB,IAAI,CAACtC,QAAQ,CAAC2B,IAAI,CAACjB,SAAS,CAAC;IAC7B,IAAI,CAACV,QAAQ,CAACsC,QAAQ,CAAC,CAAC;EAC5B;EACAC,OAAOA,CAACC,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACA,IAAI,CAACC,UAAU,CAAChB,SAAS,CAAC,MAAM,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC;EACzD;EACAS,YAAYA,CAACX,SAAS,EAAEY,KAAK,GAAG,KAAK,EAAE;IACnC,IAAI,CAACZ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACA,SAAS,CAACa,IAAI,CAACnB,SAAS,CAAC,MAAM,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC;IACpD,IAAI,CAACvC,MAAM,CAAC0C,YAAY,CAAC,CAAC,CAACX,SAAS,CAAEoB,KAAK,IAAK;MAC5C,IAAI,CAAC,IAAI,CAACd,SAAS,EAAE;QACjB;MACJ;MACA,IAAI,CAACY,KAAK,EAAE;QACR,IAAI,CAACZ,SAAS,CAACD,MAAM,GAAGe,KAAK;QAC7B;MACJ;MACA,MAAMC,cAAc,GAAG,IAAI,CAACf,SAAS,CAACgB,QAAQ,GAAG,IAAI,CAAChB,SAAS,CAACC,SAAS;MACzE,MAAMgB,eAAe,GAAG,CAAC,IAAI,CAAC7D,KAAK,IAAI,EAAE,EAAE2C,MAAM,KAAK,IAAI,CAACC,SAAS,CAACgB,QAAQ,GAAG,CAAC,GAAG,CAAC;MACrF,IAAI,CAAChB,SAAS,CAACD,MAAM,GAAG,CAAC,IAAI,CAAC3C,KAAK,IAAI,EAAE,EAAE2C,MAAM,GAAGkB,eAAe,GAAGF,cAAc;IACxF,CAAC,CAAC;EACN;EACAG,cAAcA,CAACF,QAAQ,EAAE;IACrB,IAAI,IAAI,CAAChB,SAAS,EAAE;MAChB,MAAMmB,WAAW,GAAG,IAAI,CAACnB,SAAS,CAACgB,QAAQ;MAC3C,MAAMI,YAAY,GAAG,IAAI,CAACpB,SAAS,CAACC,SAAS;MAC7C,IAAI,CAACD,SAAS,CAACgB,QAAQ,GAAGA,QAAQ;MAClC,IAAI,CAAChB,SAAS,CAACC,SAAS,GAAGoB,IAAI,CAACC,KAAK,CAAEF,YAAY,GAAGD,WAAW,GAAIH,QAAQ,CAAC;MAC9E,IAAI,IAAI,CAAC5D,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC2C,MAAM,EAAE;QACjC,IAAI,CAACG,QAAQ,CAAC,CAAC;MACnB;IACJ;EACJ;EACAA,QAAQA,CAAA,EAAG;IACP,IAAI,CAAClC,SAAS,CAAC4B,IAAI,CAACjB,SAAS,CAAC;EAClC;EACAwB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC1C,aAAa,KAAK,IAAI,EAAE;MAC7B,IAAI,CAACA,aAAa,CAAC8D,eAAe,CAC7B9C,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAC9ByB,SAAS,CAAC8B,UAAU,IAAI;QACzB,IAAI,CAACA,UAAU,GAAGA,UAAU;MAChC,CAAC,CAAC;MACF,IAAI,CAACnD,aAAa,CACbI,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAC9ByB,SAAS,CAAC,CAAC,GAAG5B,MAAM,CAAC,KAAK;QAC3B,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;UACpB,IAAI,IAAI,CAACoC,SAAS,EAAE;YAChB,IAAI,CAACA,SAAS,CAACC,SAAS,GAAG,CAAC;UAChC;QACJ;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC;MACF;IACJ;IACA,IAAI,IAAI,CAAChC,kBAAkB,EAAE;MACzB,IAAI,CAACV,gBAAgB,CAACY,eAAe,CAChCK,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAC9ByB,SAAS,CAAC5B,MAAM,IAAI;QACrB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACoC,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC;IACN;EACJ;EACApB,gBAAgBA,CAAA,EAAG;IACf,IAAID,MAAM,GAAG,CAAC,CAAC;IACf;IACA,IAAI,IAAI,CAAC2C,UAAU,EAAE;MACjB3C,MAAM,GAAG,IAAI,CAAC4C,mBAAmB,CAAC,IAAI,CAACD,UAAU,EAAE3C,MAAM,CAAC;IAC9D;IACA;IACA,IAAI,IAAI,CAACmB,SAAS,EAAE;MAChB,MAAM;QAAEC,SAAS;QAAEe;MAAS,CAAC,GAAG,IAAI,CAAChB,SAAS;MAC9CnB,MAAM,CAAC,QAAQ,CAAC,GAAG,CAACoB,SAAS,GAAGe,QAAQ,EAAEU,QAAQ,CAAC,CAAC;MACpD7C,MAAM,CAAC,OAAO,CAAC,GAAGmC,QAAQ,CAACU,QAAQ,CAAC,CAAC;IACzC,CAAC,MACI,IAAI,IAAI,CAACxE,IAAI,EAAE;MAChB2B,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;IACxB;IACA;IACA,IAAI,IAAI,CAAC4B,IAAI,EAAE;MACX,MAAM;QAAEkB,MAAM;QAAEC;MAAU,CAAC,GAAG,IAAI,CAACnB,IAAI;MACvC,IAAImB,SAAS,KAAK,EAAE,EAAE;QAClB/C,MAAM,CAAC,WAAW,CAAC,GAAG+C,SAAS,CAACC,WAAW,CAAC,CAAC;QAC7ChD,MAAM,CAAC,QAAQ,CAAC,GAAG8C,MAAM;MAC7B;IACJ;IACA,OAAO9C,MAAM;EACjB;EACA4C,mBAAmBA,CAACD,UAAU,EAAEM,aAAa,EAAE;IAC3C,OAAOC,MAAM,CAACC,IAAI,CAACR,UAAU,CAAC,CACzBS,MAAM,CAAEC,GAAG,IAAK;MACjB,MAAM/E,KAAK,GAAGqE,UAAU,CAACU,GAAG,CAAC;MAC7B,OAAO,OAAO/E,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,IAAI+E,GAAG,KAAK,WAAW;IAChG,CAAC,CAAC,CACGC,MAAM,CAAC,CAACtD,MAAM,EAAEqD,GAAG,KAAK;MACzB,IAAI/E,KAAK,GAAGqE,UAAU,CAACU,GAAG,CAAC;MAC3B,IAAI,OAAO/E,KAAK,KAAK,QAAQ,IAAI4E,MAAM,CAACC,IAAI,CAAC7E,KAAK,CAAC,CAACiF,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAACtF,mBAAmB,CAAC,CAAC,EAAE;QAC7F6B,MAAM,GAAG,IAAI,CAAC4C,mBAAmB,CAACtE,KAAK,EAAE0B,MAAM,CAAC;MACpD,CAAC,MACI;QACD,IAAI1C,MAAM,CAACoG,QAAQ,CAACpF,KAAK,CAAC,EAAE;UACxBA,KAAK,GAAGA,KAAK,CAACqF,WAAW,CAAC,CAAC;QAC/B;QACA3D,MAAM,CAACqD,GAAG,CAAC,GAAG/E,KAAK;MACvB;MACA,OAAO0B,MAAM;IACjB,CAAC,EAAEiD,aAAa,CAAC;EACrB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}