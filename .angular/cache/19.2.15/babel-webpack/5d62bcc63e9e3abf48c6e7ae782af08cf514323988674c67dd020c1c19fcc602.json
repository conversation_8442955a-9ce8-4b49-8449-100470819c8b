{"ast": null, "code": "var _AvailableGamesComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./available-games.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../games-select-manager.scss?ngResource\";\nimport { Component, Input, ViewChild } from '@angular/core';\nimport { GamesSelectManagerService } from '../games-select-manager.service';\nimport { getLabelClass } from '../game-select-item/game-select-item.model';\nimport { fromEvent } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';\nlet AvailableGamesComponent = (_AvailableGamesComponent = class AvailableGamesComponent {\n  constructor(service) {\n    this.service = service;\n    this.disabled = false;\n    this.loading = false;\n    this.height = '500px';\n    this.availableChecked = false;\n    this.subscriptions = [];\n    this.subscribeToChanges();\n  }\n  get scrollPortHeight() {\n    return parseFloat(this.height) - 58;\n  }\n  ngOnInit() {\n    this.createSearchStream();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  checkAvailableChanged() {\n    this.setChecked(this.available, this.availableChecked);\n    this.calculateCheckedGames();\n  }\n  getLabelClass(label) {\n    return getLabelClass(label);\n  }\n  gameSelectionChanged() {\n    this.refreshAvailableChecked();\n    this.calculateCheckedGames();\n  }\n  trackByFn(_index, item) {\n    return item.id;\n  }\n  refreshAvailableChecked() {\n    this.availableChecked = !!this.available && this.available.every(game => game.checked) && this.available.length > 0;\n  }\n  createSearchStream() {\n    if (this.searchInput) {\n      let sub = fromEvent(this.searchInput.nativeElement, 'input').pipe(map(e => e['currentTarget']['value']), debounceTime(100), distinctUntilChanged()).subscribe(search => this.setSearchTerm(search));\n      this.subscriptions.push(sub);\n    }\n  }\n  calculateCheckedGames() {\n    this.service.calculateCheckedAvailableGames((this.allGames || []).filter(game => game.checked));\n  }\n  setChecked(games, checked) {\n    (games || []).forEach(game => {\n      game.checked = checked;\n    });\n  }\n  setSearchTerm(search) {\n    this.searchTerm = search;\n    this.available = this.service.filterGames(this.allGames || [], this.searchTerm);\n    this.refreshAvailableChecked();\n  }\n  subscribeToChanges() {\n    this.subscriptions.push(this.service.availableGames$.subscribe(games => {\n      this.allGames = games;\n      this.available = games;\n      this.setSearchTerm(this.searchTerm || '');\n      this.calculateCheckedGames();\n    }));\n  }\n}, _AvailableGamesComponent.ctorParameters = () => [{\n  type: GamesSelectManagerService\n}], _AvailableGamesComponent.propDecorators = {\n  disabled: [{\n    type: Input\n  }],\n  loading: [{\n    type: Input\n  }],\n  height: [{\n    type: Input\n  }],\n  searchInput: [{\n    type: ViewChild,\n    args: ['searchInput', {\n      static: true\n    }]\n  }]\n}, _AvailableGamesComponent);\nAvailableGamesComponent = __decorate([Component({\n  // tslint:disable-next-line:component-selector\n  selector: 'sw-available-games',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AvailableGamesComponent);\nexport { AvailableGamesComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "ViewChild", "GamesSelectManagerService", "getLabelClass", "fromEvent", "debounceTime", "distinctUntilChanged", "map", "AvailableGamesComponent", "_AvailableGamesComponent", "constructor", "service", "disabled", "loading", "height", "availableChecked", "subscriptions", "subscribeToChanges", "scrollPortHeight", "parseFloat", "ngOnInit", "createSearchStream", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "checkAvailableChanged", "setChecked", "available", "calculateCheckedGames", "label", "gameSelectionChanged", "refreshAvailableChecked", "trackByFn", "_index", "item", "id", "every", "game", "checked", "length", "searchInput", "nativeElement", "pipe", "e", "subscribe", "search", "setSearchTerm", "push", "calculateCheckedAvailableGames", "allGames", "filter", "games", "searchTerm", "filterGames", "availableGames$", "ctorParameters", "type", "propDecorators", "args", "static", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/available-games/available-games.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./available-games.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../games-select-manager.scss?ngResource\";\nimport { Component, Input, ViewChild } from '@angular/core';\nimport { GamesSelectManagerService } from '../games-select-manager.service';\nimport { getLabelClass } from '../game-select-item/game-select-item.model';\nimport { fromEvent } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';\nlet AvailableGamesComponent = class AvailableGamesComponent {\n    constructor(service) {\n        this.service = service;\n        this.disabled = false;\n        this.loading = false;\n        this.height = '500px';\n        this.availableChecked = false;\n        this.subscriptions = [];\n        this.subscribeToChanges();\n    }\n    get scrollPortHeight() {\n        return parseFloat(this.height) - 58;\n    }\n    ngOnInit() {\n        this.createSearchStream();\n    }\n    ngOnDestroy() {\n        this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    checkAvailableChanged() {\n        this.setChecked(this.available, this.availableChecked);\n        this.calculateCheckedGames();\n    }\n    getLabelClass(label) {\n        return getLabelClass(label);\n    }\n    gameSelectionChanged() {\n        this.refreshAvailableChecked();\n        this.calculateCheckedGames();\n    }\n    trackByFn(_index, item) {\n        return item.id;\n    }\n    refreshAvailableChecked() {\n        this.availableChecked = !!this.available && this.available.every(game => game.checked) && this.available.length > 0;\n    }\n    createSearchStream() {\n        if (this.searchInput) {\n            let sub = fromEvent(this.searchInput.nativeElement, 'input')\n                .pipe(map((e) => e['currentTarget']['value']), debounceTime(100), distinctUntilChanged())\n                .subscribe((search) => this.setSearchTerm(search));\n            this.subscriptions.push(sub);\n        }\n    }\n    calculateCheckedGames() {\n        this.service.calculateCheckedAvailableGames((this.allGames || []).filter(game => game.checked));\n    }\n    setChecked(games, checked) {\n        (games || []).forEach(game => {\n            game.checked = checked;\n        });\n    }\n    setSearchTerm(search) {\n        this.searchTerm = search;\n        this.available = this.service.filterGames(this.allGames || [], this.searchTerm);\n        this.refreshAvailableChecked();\n    }\n    subscribeToChanges() {\n        this.subscriptions.push(this.service.availableGames$.subscribe(games => {\n            this.allGames = games;\n            this.available = games;\n            this.setSearchTerm(this.searchTerm || '');\n            this.calculateCheckedGames();\n        }));\n    }\n    static { this.ctorParameters = () => [\n        { type: GamesSelectManagerService }\n    ]; }\n    static { this.propDecorators = {\n        disabled: [{ type: Input }],\n        loading: [{ type: Input }],\n        height: [{ type: Input }],\n        searchInput: [{ type: ViewChild, args: ['searchInput', { static: true },] }]\n    }; }\n};\nAvailableGamesComponent = __decorate([\n    Component({\n        // tslint:disable-next-line:component-selector\n        selector: 'sw-available-games',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], AvailableGamesComponent);\nexport { AvailableGamesComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,SAASC,SAAS,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AAC3D,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,aAAa,QAAQ,4CAA4C;AAC1E,SAASC,SAAS,QAAQ,MAAM;AAChC,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,QAAQ,gBAAgB;AACxE,IAAIC,uBAAuB,IAAAC,wBAAA,GAAG,MAAMD,uBAAuB,CAAC;EACxDE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,MAAM,GAAG,OAAO;IACrB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAOC,UAAU,CAAC,IAAI,CAACL,MAAM,CAAC,GAAG,EAAE;EACvC;EACAM,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,aAAa,CAACO,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC;EACxD;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACC,UAAU,CAAC,IAAI,CAACC,SAAS,EAAE,IAAI,CAACb,gBAAgB,CAAC;IACtD,IAAI,CAACc,qBAAqB,CAAC,CAAC;EAChC;EACA1B,aAAaA,CAAC2B,KAAK,EAAE;IACjB,OAAO3B,aAAa,CAAC2B,KAAK,CAAC;EAC/B;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACH,qBAAqB,CAAC,CAAC;EAChC;EACAI,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACpB,OAAOA,IAAI,CAACC,EAAE;EAClB;EACAJ,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACjB,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAACa,SAAS,IAAI,IAAI,CAACA,SAAS,CAACS,KAAK,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,IAAI,IAAI,CAACX,SAAS,CAACY,MAAM,GAAG,CAAC;EACvH;EACAnB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACoB,WAAW,EAAE;MAClB,IAAIjB,GAAG,GAAGpB,SAAS,CAAC,IAAI,CAACqC,WAAW,CAACC,aAAa,EAAE,OAAO,CAAC,CACvDC,IAAI,CAACpC,GAAG,CAAEqC,CAAC,IAAKA,CAAC,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,EAAEvC,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,CAAC,CAAC,CAAC,CACxFuC,SAAS,CAAEC,MAAM,IAAK,IAAI,CAACC,aAAa,CAACD,MAAM,CAAC,CAAC;MACtD,IAAI,CAAC9B,aAAa,CAACgC,IAAI,CAACxB,GAAG,CAAC;IAChC;EACJ;EACAK,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAClB,OAAO,CAACsC,8BAA8B,CAAC,CAAC,IAAI,CAACC,QAAQ,IAAI,EAAE,EAAEC,MAAM,CAACb,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,CAAC;EACnG;EACAZ,UAAUA,CAACyB,KAAK,EAAEb,OAAO,EAAE;IACvB,CAACa,KAAK,IAAI,EAAE,EAAE7B,OAAO,CAACe,IAAI,IAAI;MAC1BA,IAAI,CAACC,OAAO,GAAGA,OAAO;IAC1B,CAAC,CAAC;EACN;EACAQ,aAAaA,CAACD,MAAM,EAAE;IAClB,IAAI,CAACO,UAAU,GAAGP,MAAM;IACxB,IAAI,CAAClB,SAAS,GAAG,IAAI,CAACjB,OAAO,CAAC2C,WAAW,CAAC,IAAI,CAACJ,QAAQ,IAAI,EAAE,EAAE,IAAI,CAACG,UAAU,CAAC;IAC/E,IAAI,CAACrB,uBAAuB,CAAC,CAAC;EAClC;EACAf,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACD,aAAa,CAACgC,IAAI,CAAC,IAAI,CAACrC,OAAO,CAAC4C,eAAe,CAACV,SAAS,CAACO,KAAK,IAAI;MACpE,IAAI,CAACF,QAAQ,GAAGE,KAAK;MACrB,IAAI,CAACxB,SAAS,GAAGwB,KAAK;MACtB,IAAI,CAACL,aAAa,CAAC,IAAI,CAACM,UAAU,IAAI,EAAE,CAAC;MACzC,IAAI,CAACxB,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;EACP;AAUJ,CAAC,EATYpB,wBAAA,CAAK+C,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEvD;AAA0B,CAAC,CACtC,EACQO,wBAAA,CAAKiD,cAAc,GAAG;EAC3B9C,QAAQ,EAAE,CAAC;IAAE6C,IAAI,EAAEzD;EAAM,CAAC,CAAC;EAC3Ba,OAAO,EAAE,CAAC;IAAE4C,IAAI,EAAEzD;EAAM,CAAC,CAAC;EAC1Bc,MAAM,EAAE,CAAC;IAAE2C,IAAI,EAAEzD;EAAM,CAAC,CAAC;EACzByC,WAAW,EAAE,CAAC;IAAEgB,IAAI,EAAExD,SAAS;IAAE0D,IAAI,EAAE,CAAC,aAAa,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC;AAC/E,CAAC,EAAAnD,wBAAA,CACJ;AACDD,uBAAuB,GAAGZ,UAAU,CAAC,CACjCG,SAAS,CAAC;EACN;EACA8D,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAEjE,oBAAoB;EAC9BkE,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAClE,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEU,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}