{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatOptgroup, _MatOption;\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\nconst _c2 = [\"text\"];\nconst _c3 = [[[\"mat-icon\"]], \"*\"];\nconst _c4 = [\"mat-icon\", \"*\"];\nfunction _MatOption_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled)(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\");\n  }\n}\nfunction _MatOption_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled);\n  }\n}\nfunction _MatOption_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.group.label, \")\");\n  }\n}\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ElementRef, ChangeDetectorRef, EventEmitter, isSignal, Output, ViewChild } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-CJ7seqQH.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup {\n  constructor() {\n    var _parent$inertGroups;\n    /** Label for the option group. */\n    _defineProperty(this, \"label\", void 0);\n    /** whether the option group is disabled. */\n    _defineProperty(this, \"disabled\", false);\n    /** Unique id for the underlying label. */\n    _defineProperty(this, \"_labelId\", inject(_IdGenerator).getId('mat-optgroup-label-'));\n    /** Whether the group is in inert a11y mode. */\n    _defineProperty(this, \"_inert\", void 0);\n    const parent = inject(MAT_OPTION_PARENT_COMPONENT, {\n      optional: true\n    });\n    this._inert = (_parent$inertGroups = parent === null || parent === void 0 ? void 0 : parent.inertGroups) !== null && _parent$inertGroups !== void 0 ? _parent$inertGroups : false;\n  }\n}\n_MatOptgroup = MatOptgroup;\n_defineProperty(MatOptgroup, \"\\u0275fac\", function _MatOptgroup_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatOptgroup)();\n});\n_defineProperty(MatOptgroup, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatOptgroup,\n  selectors: [[\"mat-optgroup\"]],\n  hostAttrs: [1, \"mat-mdc-optgroup\"],\n  hostVars: 3,\n  hostBindings: function _MatOptgroup_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n    }\n  },\n  inputs: {\n    label: \"label\",\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n  },\n  exportAs: [\"matOptgroup\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_OPTGROUP,\n    useExisting: _MatOptgroup\n  }])],\n  ngContentSelectors: _c1,\n  decls: 5,\n  vars: 4,\n  consts: [[\"role\", \"presentation\", 1, \"mat-mdc-optgroup-label\", 3, \"id\"], [1, \"mdc-list-item__primary-text\"]],\n  template: function _MatOptgroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵelementStart(0, \"span\", 0)(1, \"span\", 1);\n      i0.ɵɵtext(2);\n      i0.ɵɵprojection(3);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵprojection(4, 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n      i0.ɵɵproperty(\"id\", ctx._labelId);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n    }\n  },\n  styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptgroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-optgroup',\n      exportAs: 'matOptgroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-optgroup',\n        '[attr.role]': '_inert ? null : \"group\"',\n        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n        '[attr.aria-labelledby]': '_inert ? null : _labelId'\n      },\n      providers: [{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }],\n      template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\",\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\\n\"]\n    }]\n  }], () => [], {\n    label: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n  constructor(/** Reference to the option that emitted the event. */\n  source, /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    _defineProperty(this, \"source\", void 0);\n    _defineProperty(this, \"isUserInput\", void 0);\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption {\n  /** Whether the wrapping component is in multiple selection mode. */\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n  /** Whether or not the option is currently selected. */\n  get selected() {\n    return this._selected;\n  }\n  /** The form value of the option. */\n\n  /** Whether the option is disabled. */\n  get disabled() {\n    return this.group && this.group.disabled || this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  /** Whether ripples for the option are disabled. */\n  get disableRipple() {\n    var _this$_parent;\n    return this._signalDisableRipple ? this._parent.disableRipple() : !!((_this$_parent = this._parent) !== null && _this$_parent !== void 0 && _this$_parent.disableRipple);\n  }\n  /** Whether to display checkmark for single-selection. */\n  get hideSingleSelectionIndicator() {\n    return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n  }\n  /** Event emitted when the option is selected or deselected. */\n  // tslint:disable-next-line:no-output-on-prefix\n\n  constructor() {\n    _defineProperty(this, \"_element\", inject(ElementRef));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_parent\", inject(MAT_OPTION_PARENT_COMPONENT, {\n      optional: true\n    }));\n    _defineProperty(this, \"group\", inject(MAT_OPTGROUP, {\n      optional: true\n    }));\n    _defineProperty(this, \"_signalDisableRipple\", false);\n    _defineProperty(this, \"_selected\", false);\n    _defineProperty(this, \"_active\", false);\n    _defineProperty(this, \"_disabled\", false);\n    _defineProperty(this, \"_mostRecentViewValue\", '');\n    _defineProperty(this, \"value\", void 0);\n    /** The unique ID of the option. */\n    _defineProperty(this, \"id\", inject(_IdGenerator).getId('mat-option-'));\n    _defineProperty(this, \"onSelectionChange\", new EventEmitter());\n    /** Element containing the option's text. */\n    _defineProperty(this, \"_text\", void 0);\n    /** Emits when the state of the option changes and any parents have to be notified. */\n    _defineProperty(this, \"_stateChanges\", new Subject());\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_StructuralStylesLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n    this._signalDisableRipple = !!this._parent && isSignal(this._parent.disableRipple);\n  }\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n  get active() {\n    return this._active;\n  }\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n  get viewValue() {\n    var _this$_text;\n    // TODO(kara): Add input property alternative for node envs.\n    return (((_this$_text = this._text) === null || _this$_text === void 0 ? void 0 : _this$_text.nativeElement.textContent) || '').trim();\n  }\n  /** Selects the option. */\n  select(emitEvent = true) {\n    if (!this._selected) {\n      this._selected = true;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Deselects the option. */\n  deselect(emitEvent = true) {\n    if (this._selected) {\n      this._selected = false;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Sets focus onto this option. */\n  focus(_origin, options) {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setActiveStyles() {\n    if (!this._active) {\n      this._active = true;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setInactiveStyles() {\n    if (this._active) {\n      this._active = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    return this.viewValue;\n  }\n  /** Ensures the option is selected when activated from the keyboard. */\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction();\n      // Prevent the page from scrolling down and form submits.\n      event.preventDefault();\n    }\n  }\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n  _selectViaInteraction() {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n  /** Returns the correct tabindex for the option depending on disabled state. */\n  // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n  // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n  // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Gets the host DOM element. */\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n      if (viewValue !== this._mostRecentViewValue) {\n        if (this._mostRecentViewValue) {\n          this._stateChanges.next();\n        }\n        this._mostRecentViewValue = viewValue;\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  /** Emits the selection change event. */\n  _emitSelectionChangeEvent(isUserInput = false) {\n    this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n  }\n}\n_MatOption = MatOption;\n_defineProperty(MatOption, \"\\u0275fac\", function _MatOption_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatOption)();\n});\n_defineProperty(MatOption, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatOption,\n  selectors: [[\"mat-option\"]],\n  viewQuery: function _MatOption_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c2, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._text = _t.first);\n    }\n  },\n  hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-option\", \"mdc-list-item\"],\n  hostVars: 11,\n  hostBindings: function _MatOption_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _MatOption_click_HostBindingHandler() {\n        return ctx._selectViaInteraction();\n      })(\"keydown\", function _MatOption_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id);\n      i0.ɵɵattribute(\"aria-selected\", ctx.selected)(\"aria-disabled\", ctx.disabled.toString());\n      i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected)(\"mat-mdc-option-multiple\", ctx.multiple)(\"mat-mdc-option-active\", ctx.active)(\"mdc-list-item--disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    value: \"value\",\n    id: \"id\",\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n  },\n  outputs: {\n    onSelectionChange: \"onSelectionChange\"\n  },\n  exportAs: [\"matOption\"],\n  ngContentSelectors: _c4,\n  decls: 8,\n  vars: 5,\n  consts: [[\"text\", \"\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\", \"state\"], [1, \"mdc-list-item__primary-text\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"cdk-visually-hidden\"], [\"aria-hidden\", \"true\", \"mat-ripple\", \"\", 1, \"mat-mdc-option-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"]],\n  template: function _MatOption_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c3);\n      i0.ɵɵtemplate(0, _MatOption_Conditional_0_Template, 1, 2, \"mat-pseudo-checkbox\", 1);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementStart(2, \"span\", 2, 0);\n      i0.ɵɵprojection(4, 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(5, _MatOption_Conditional_5_Template, 1, 1, \"mat-pseudo-checkbox\", 3)(6, _MatOption_Conditional_6_Template, 2, 1, \"span\", 4);\n      i0.ɵɵelement(7, \"div\", 5);\n    }\n    if (rf & 2) {\n      i0.ɵɵconditional(ctx.multiple ? 0 : -1);\n      i0.ɵɵadvance(5);\n      i0.ɵɵconditional(!ctx.multiple && ctx.selected && !ctx.hideSingleSelectionIndicator ? 5 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.group && ctx.group._inert ? 6 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n    }\n  },\n  dependencies: [MatPseudoCheckbox, MatRipple],\n  styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-option',\n      exportAs: 'matOption',\n      host: {\n        'role': 'option',\n        '[class.mdc-list-item--selected]': 'selected',\n        '[class.mat-mdc-option-multiple]': 'multiple',\n        '[class.mat-mdc-option-active]': 'active',\n        '[class.mdc-list-item--disabled]': 'disabled',\n        '[id]': 'id',\n        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n        // [WAI ARIA Listbox authoring practices guide](\n        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n        // selected option has either aria-selected or aria-checked  set to true. All options that are\n        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n        // aria-selected implementation of Chips and List components.\n        //\n        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n        // every option as \"selected\" (#21491).\n        '[attr.aria-selected]': 'selected',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '(click)': '_selectViaInteraction()',\n        '(keydown)': '_handleKeydown($event)',\n        'class': 'mat-mdc-option mdc-list-item'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatPseudoCheckbox, MatRipple],\n      template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\",\n      styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\\n\"]\n    }]\n  }], () => [], {\n    value: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    _text: [{\n      type: ViewChild,\n      args: ['text', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n    return groupCounter;\n  }\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n  return currentScrollPosition;\n}\nexport { MatOption as M, _countGroupLabelsBeforeOption as _, MatOptgroup as a, _getOptionScrollPosition as b, MAT_OPTION_PARENT_COMPONENT as c, MAT_OPTGROUP as d, MatOptionSelectionChange as e };", "map": {"version": 3, "names": ["i0", "ɵɵelement", "rf", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "disabled", "selected", "_MatOption_Conditional_5_Template", "ctx", "_MatOption_Conditional_6_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "group", "label", "_IdGenerator", "ENTER", "SPACE", "hasModifierKey", "InjectionToken", "inject", "booleanAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "ElementRef", "ChangeDetectorRef", "EventEmitter", "isSignal", "Output", "ViewChild", "Subject", "M", "<PERSON><PERSON><PERSON><PERSON>", "MatPseudoCheckbox", "_", "_StructuralStylesLoader", "_CdkPrivateStyleLoader", "_VisuallyHiddenLoader", "MAT_OPTION_PARENT_COMPONENT", "MAT_OPTGROUP", "MatOptgroup", "constructor", "_parent$inertGroups", "_defineProperty", "getId", "parent", "optional", "_inert", "inertGroups", "_MatOptgroup", "_MatOptgroup_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "_MatOptgroup_HostBindings", "ɵɵattribute", "toString", "_labelId", "inputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ngContentSelectors", "_c1", "decls", "vars", "consts", "template", "_MatOptgroup_Template", "ɵɵprojectionDef", "_c0", "ɵɵprojection", "ɵɵclassProp", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "host", "providers", "transform", "MatOptionSelectionChange", "source", "isUserInput", "MatOption", "multiple", "_parent", "_selected", "_disabled", "value", "disable<PERSON><PERSON><PERSON>", "_this$_parent", "_signalDisableRipple", "hideSingleSelectionIndicator", "<PERSON><PERSON><PERSON><PERSON>", "load", "active", "_active", "viewValue", "_this$_text", "_text", "nativeElement", "textContent", "trim", "select", "emitEvent", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_emitSelectionChangeEvent", "deselect", "focus", "_origin", "options", "element", "_getHostElement", "setActiveStyles", "setInactiveStyles", "get<PERSON><PERSON><PERSON>", "_handleKeydown", "event", "keyCode", "_selectViaInteraction", "preventDefault", "_getTabIndex", "_element", "ngAfterViewChecked", "_mostRecentViewValue", "_stateChanges", "next", "ngOnDestroy", "complete", "onSelectionChange", "emit", "_MatOption", "_MatOption_Factory", "viewQuery", "_MatOption_Query", "ɵɵviewQuery", "_c2", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "_MatOption_HostBindings", "ɵɵlistener", "_MatOption_click_HostBindingHandler", "_MatOption_keydown_HostBindingHandler", "$event", "ɵɵhostProperty", "id", "outputs", "_c4", "_MatOption_Template", "_c3", "ɵɵtemplate", "_MatOption_Conditional_0_Template", "ɵɵconditional", "dependencies", "imports", "static", "_countGroupLabelsBeforeOption", "optionIndex", "optionGroups", "length", "optionsArray", "toArray", "groups", "groupCounter", "i", "_getOptionScrollPosition", "optionOffset", "optionHeight", "currentScrollPosition", "panelHeight", "Math", "max", "a", "b", "c", "d", "e"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/option-ChV6uQgD.mjs"], "sourcesContent": ["import { _IdGenerator } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, hasModifier<PERSON><PERSON> } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ElementRef, ChangeDetectorRef, EventEmitter, isSignal, Output, ViewChild } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-CJ7seqQH.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup {\n    /** Label for the option group. */\n    label;\n    /** whether the option group is disabled. */\n    disabled = false;\n    /** Unique id for the underlying label. */\n    _labelId = inject(_IdGenerator).getId('mat-optgroup-label-');\n    /** Whether the group is in inert a11y mode. */\n    _inert;\n    constructor() {\n        const parent = inject(MAT_OPTION_PARENT_COMPONENT, { optional: true });\n        this._inert = parent?.inertGroups ?? false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatOptgroup, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatOptgroup, isStandalone: true, selector: \"mat-optgroup\", inputs: { label: \"label\", disabled: [\"disabled\", \"disabled\", booleanAttribute] }, host: { properties: { \"attr.role\": \"_inert ? null : \\\"group\\\"\", \"attr.aria-disabled\": \"_inert ? null : disabled.toString()\", \"attr.aria-labelledby\": \"_inert ? null : _labelId\" }, classAttribute: \"mat-mdc-optgroup\" }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], exportAs: [\"matOptgroup\"], ngImport: i0, template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatOptgroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-optgroup', exportAs: 'matOptgroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mat-mdc-optgroup',\n                        '[attr.role]': '_inert ? null : \"group\"',\n                        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n                        '[attr.aria-labelledby]': '_inert ? null : _labelId',\n                    }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { label: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n    source;\n    isUserInput;\n    constructor(\n    /** Reference to the option that emitted the event. */\n    source, \n    /** Whether the change in the option's value was a result of a user action. */\n    isUserInput = false) {\n        this.source = source;\n        this.isUserInput = isUserInput;\n    }\n}\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption {\n    _element = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _parent = inject(MAT_OPTION_PARENT_COMPONENT, { optional: true });\n    group = inject(MAT_OPTGROUP, { optional: true });\n    _signalDisableRipple = false;\n    _selected = false;\n    _active = false;\n    _disabled = false;\n    _mostRecentViewValue = '';\n    /** Whether the wrapping component is in multiple selection mode. */\n    get multiple() {\n        return this._parent && this._parent.multiple;\n    }\n    /** Whether or not the option is currently selected. */\n    get selected() {\n        return this._selected;\n    }\n    /** The form value of the option. */\n    value;\n    /** The unique ID of the option. */\n    id = inject(_IdGenerator).getId('mat-option-');\n    /** Whether the option is disabled. */\n    get disabled() {\n        return (this.group && this.group.disabled) || this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    /** Whether ripples for the option are disabled. */\n    get disableRipple() {\n        return this._signalDisableRipple\n            ? this._parent.disableRipple()\n            : !!this._parent?.disableRipple;\n    }\n    /** Whether to display checkmark for single-selection. */\n    get hideSingleSelectionIndicator() {\n        return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n    }\n    /** Event emitted when the option is selected or deselected. */\n    // tslint:disable-next-line:no-output-on-prefix\n    onSelectionChange = new EventEmitter();\n    /** Element containing the option's text. */\n    _text;\n    /** Emits when the state of the option changes and any parents have to be notified. */\n    _stateChanges = new Subject();\n    constructor() {\n        const styleLoader = inject(_CdkPrivateStyleLoader);\n        styleLoader.load(_StructuralStylesLoader);\n        styleLoader.load(_VisuallyHiddenLoader);\n        this._signalDisableRipple = !!this._parent && isSignal(this._parent.disableRipple);\n    }\n    /**\n     * Whether or not the option is currently active and ready to be selected.\n     * An active option displays styles as if it is focused, but the\n     * focus is actually retained somewhere else. This comes in handy\n     * for components like autocomplete where focus must remain on the input.\n     */\n    get active() {\n        return this._active;\n    }\n    /**\n     * The displayed value of the option. It is necessary to show the selected option in the\n     * select's trigger.\n     */\n    get viewValue() {\n        // TODO(kara): Add input property alternative for node envs.\n        return (this._text?.nativeElement.textContent || '').trim();\n    }\n    /** Selects the option. */\n    select(emitEvent = true) {\n        if (!this._selected) {\n            this._selected = true;\n            this._changeDetectorRef.markForCheck();\n            if (emitEvent) {\n                this._emitSelectionChangeEvent();\n            }\n        }\n    }\n    /** Deselects the option. */\n    deselect(emitEvent = true) {\n        if (this._selected) {\n            this._selected = false;\n            this._changeDetectorRef.markForCheck();\n            if (emitEvent) {\n                this._emitSelectionChangeEvent();\n            }\n        }\n    }\n    /** Sets focus onto this option. */\n    focus(_origin, options) {\n        // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n        // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n        const element = this._getHostElement();\n        if (typeof element.focus === 'function') {\n            element.focus(options);\n        }\n    }\n    /**\n     * This method sets display styles on the option to make it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setActiveStyles() {\n        if (!this._active) {\n            this._active = true;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method removes display styles on the option that made it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setInactiveStyles() {\n        if (this._active) {\n            this._active = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        return this.viewValue;\n    }\n    /** Ensures the option is selected when activated from the keyboard. */\n    _handleKeydown(event) {\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n            this._selectViaInteraction();\n            // Prevent the page from scrolling down and form submits.\n            event.preventDefault();\n        }\n    }\n    /**\n     * `Selects the option while indicating the selection came from the user. Used to\n     * determine if the select's view -> model callback should be invoked.`\n     */\n    _selectViaInteraction() {\n        if (!this.disabled) {\n            this._selected = this.multiple ? !this._selected : true;\n            this._changeDetectorRef.markForCheck();\n            this._emitSelectionChangeEvent(true);\n        }\n    }\n    /** Returns the correct tabindex for the option depending on disabled state. */\n    // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n    // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n    // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Gets the host DOM element. */\n    _getHostElement() {\n        return this._element.nativeElement;\n    }\n    ngAfterViewChecked() {\n        // Since parent components could be using the option's label to display the selected values\n        // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n        // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n        // relatively cheap, however we still limit them only to selected options in order to avoid\n        // hitting the DOM too often.\n        if (this._selected) {\n            const viewValue = this.viewValue;\n            if (viewValue !== this._mostRecentViewValue) {\n                if (this._mostRecentViewValue) {\n                    this._stateChanges.next();\n                }\n                this._mostRecentViewValue = viewValue;\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    /** Emits the selection change event. */\n    _emitSelectionChangeEvent(isUserInput = false) {\n        this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatOption, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatOption, isStandalone: true, selector: \"mat-option\", inputs: { value: \"value\", id: \"id\", disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { onSelectionChange: \"onSelectionChange\" }, host: { attributes: { \"role\": \"option\" }, listeners: { \"click\": \"_selectViaInteraction()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"class.mdc-list-item--selected\": \"selected\", \"class.mat-mdc-option-multiple\": \"multiple\", \"class.mat-mdc-option-active\": \"active\", \"class.mdc-list-item--disabled\": \"disabled\", \"id\": \"id\", \"attr.aria-selected\": \"selected\", \"attr.aria-disabled\": \"disabled.toString()\" }, classAttribute: \"mat-mdc-option mdc-list-item\" }, viewQueries: [{ propertyName: \"_text\", first: true, predicate: [\"text\"], descendants: true, static: true }], exportAs: [\"matOption\"], ngImport: i0, template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\\n\"], dependencies: [{ kind: \"component\", type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: [\"state\", \"disabled\", \"appearance\"] }, { kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatOption, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-option', exportAs: 'matOption', host: {\n                        'role': 'option',\n                        '[class.mdc-list-item--selected]': 'selected',\n                        '[class.mat-mdc-option-multiple]': 'multiple',\n                        '[class.mat-mdc-option-active]': 'active',\n                        '[class.mdc-list-item--disabled]': 'disabled',\n                        '[id]': 'id',\n                        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n                        // [WAI ARIA Listbox authoring practices guide](\n                        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n                        // selected option has either aria-selected or aria-checked  set to true. All options that are\n                        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n                        // aria-selected implementation of Chips and List components.\n                        //\n                        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n                        // every option as \"selected\" (#21491).\n                        '[attr.aria-selected]': 'selected',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '(click)': '_selectViaInteraction()',\n                        '(keydown)': '_handleKeydown($event)',\n                        'class': 'mat-mdc-option mdc-list-item',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatPseudoCheckbox, MatRipple], template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-minimal-pseudo-checkbox-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { value: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], onSelectionChange: [{\n                type: Output\n            }], _text: [{\n                type: ViewChild,\n                args: ['text', { static: true }]\n            }] } });\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n    if (optionGroups.length) {\n        let optionsArray = options.toArray();\n        let groups = optionGroups.toArray();\n        let groupCounter = 0;\n        for (let i = 0; i < optionIndex + 1; i++) {\n            if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n                groupCounter++;\n            }\n        }\n        return groupCounter;\n    }\n    return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n    if (optionOffset < currentScrollPosition) {\n        return optionOffset;\n    }\n    if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n        return Math.max(0, optionOffset - panelHeight + optionHeight);\n    }\n    return currentScrollPosition;\n}\n\nexport { MatOption as M, _countGroupLabelsBeforeOption as _, MatOptgroup as a, _getOptionScrollPosition as b, MAT_OPTION_PARENT_COMPONENT as c, MAT_OPTGROUP as d, MatOptionSelectionChange as e };\n"], "mappings": ";;;;;;;;;IA2DiFA,EAAE,CAAAC,SAAA,4BAiN86C,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAC,MAAA,GAjNj7CH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAK,UAAA,aAAAF,MAAA,CAAAG,QAiNi0C,CAAC,UAAAH,MAAA,CAAAI,QAAA,0BAAuD,CAAC;EAAA;AAAA;AAAA,SAAAC,kCAAAN,EAAA,EAAAO,GAAA;EAAA,IAAAP,EAAA;IAjN53CF,EAAE,CAAAC,SAAA,4BAiNm5D,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAC,MAAA,GAjNt5DH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAK,UAAA,aAAAF,MAAA,CAAAG,QAiNmyD,CAAC;EAAA;AAAA;AAAA,SAAAI,kCAAAR,EAAA,EAAAO,GAAA;EAAA,IAAAP,EAAA;IAjNtyDF,EAAE,CAAAW,cAAA,aAiN8iE,CAAC;IAjNjjEX,EAAE,CAAAY,MAAA,EAiNikE,CAAC;IAjNpkEZ,EAAE,CAAAa,YAAA,CAiNwkE,CAAC;EAAA;EAAA,IAAAX,EAAA;IAAA,MAAAC,MAAA,GAjN3kEH,EAAE,CAAAI,aAAA;IAAFJ,EAAE,CAAAc,SAAA,CAiNikE,CAAC;IAjNpkEd,EAAE,CAAAe,kBAAA,MAAAZ,MAAA,CAAAa,KAAA,CAAAC,KAAA,KAiNikE,CAAC;EAAA;AAAA;AA5QrpE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,KAAK,EAAEC,KAAK,EAAEC,cAAc,QAAQ,uBAAuB;AACpE,OAAO,KAAKrB,EAAE,MAAM,eAAe;AACnC,SAASsB,cAAc,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAChN,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,CAAC,IAAIC,SAAS,QAAQ,uBAAuB;AACtD,SAASD,CAAC,IAAIE,iBAAiB,QAAQ,gCAAgC;AACvE,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,sBAAsB,EAAEC,qBAAqB,QAAQ,sBAAsB;;AAEpF;AACA;AACA;AACA,MAAMC,2BAA2B,GAAG,IAAIrB,cAAc,CAAC,6BAA6B,CAAC;;AAErF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsB,YAAY,GAAG,IAAItB,cAAc,CAAC,aAAa,CAAC;AACtD;AACA;AACA;AACA,MAAMuB,WAAW,CAAC;EASdC,WAAWA,CAAA,EAAG;IAAA,IAAAC,mBAAA;IARd;IAAAC,eAAA;IAEA;IAAAA,eAAA,mBACW,KAAK;IAChB;IAAAA,eAAA,mBACWzB,MAAM,CAACL,YAAY,CAAC,CAAC+B,KAAK,CAAC,qBAAqB,CAAC;IAC5D;IAAAD,eAAA;IAGI,MAAME,MAAM,GAAG3B,MAAM,CAACoB,2BAA2B,EAAE;MAAEQ,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtE,IAAI,CAACC,MAAM,IAAAL,mBAAA,GAAGG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,WAAW,cAAAN,mBAAA,cAAAA,mBAAA,GAAI,KAAK;EAC9C;AAGJ;AAACO,YAAA,GAfKT,WAAW;AAAAG,eAAA,CAAXH,WAAW,wBAAAU,qBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAasFX,YAAW;AAAA;AAAAG,eAAA,CAb5GH,WAAW,8BAgBgE7C,EAAE,CAAAyD,iBAAA;EAAAC,IAAA,EAFQb,YAAW;EAAAc,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,0BAAA7D,EAAA,EAAAO,GAAA;IAAA,IAAAP,EAAA;MAErBF,EAAE,CAAAgE,WAAA,SAAAvD,GAAA,CAAA2C,MAAA,GAFiB,IAAI,GAAG,OAAO,mBAAA3C,GAAA,CAAA2C,MAAA,GAAd,IAAI,GAAG3C,GAAA,CAAAH,QAAA,CAAA2D,QAAA,CAAkB,CAAC,qBAAAxD,GAAA,CAAA2C,MAAA,GAA1B,IAAI,GAAA3C,GAAA,CAAAyD,QAAA;IAAA;EAAA;EAAAC,MAAA;IAAAlD,KAAA;IAAAX,QAAA,8BAA2GkB,gBAAgB;EAAA;EAAA4C,QAAA;EAAAC,QAAA,GAElJrE,EAAE,CAAAsE,kBAAA,CAFyX,CAAC;IAAEC,OAAO,EAAE3B,YAAY;IAAE4B,WAAW,EAAE3B;EAAY,CAAC,CAAC;EAAA4B,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,sBAAA7E,EAAA,EAAAO,GAAA;IAAA,IAAAP,EAAA;MAEhbF,EAAE,CAAAgF,eAAA,CAAAC,GAAA;MAAFjF,EAAE,CAAAW,cAAA,aAF2mB,CAAC,aAA+C,CAAC;MAE9pBX,EAAE,CAAAY,MAAA,EAFuqB,CAAC;MAE1qBZ,EAAE,CAAAkF,YAAA,EAFgsB,CAAC;MAEnsBlF,EAAE,CAAAa,YAAA,CAFusB,CAAC,CAAQ,CAAC;MAEntBb,EAAE,CAAAkF,YAAA,KAFixB,CAAC;IAAA;IAAA,IAAAhF,EAAA;MAEpxBF,EAAE,CAAAmF,WAAA,4BAAA1E,GAAA,CAAAH,QAFqlB,CAAC;MAExlBN,EAAE,CAAAK,UAAA,OAAAI,GAAA,CAAAyD,QAF0mB,CAAC;MAE7mBlE,EAAE,CAAAc,SAAA,EAFuqB,CAAC;MAE1qBd,EAAE,CAAAe,kBAAA,KAAAN,GAAA,CAAAQ,KAAA,KAFuqB,CAAC;IAAA;EAAA;EAAAmE,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAE3vB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFvF,EAAE,CAAAwF,iBAAA,CAAQ3C,WAAW,EAAc,CAAC;IACzGa,IAAI,EAAEjC,SAAS;IACfgE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEtB,QAAQ,EAAE,aAAa;MAAEiB,aAAa,EAAE3D,iBAAiB,CAACiE,IAAI;MAAEL,eAAe,EAAE3D,uBAAuB,CAACiE,MAAM;MAAEC,IAAI,EAAE;QAC9I,OAAO,EAAE,kBAAkB;QAC3B,aAAa,EAAE,yBAAyB;QACxC,sBAAsB,EAAE,qCAAqC;QAC7D,wBAAwB,EAAE;MAC9B,CAAC;MAAEC,SAAS,EAAE,CAAC;QAAEvB,OAAO,EAAE3B,YAAY;QAAE4B,WAAW,EAAE3B;MAAY,CAAC,CAAC;MAAEiC,QAAQ,EAAE,kTAAkT;MAAEM,MAAM,EAAE,CAAC,29BAA29B;IAAE,CAAC;EACt3C,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEnE,KAAK,EAAE,CAAC;MAChDyC,IAAI,EAAE9B;IACV,CAAC,CAAC;IAAEtB,QAAQ,EAAE,CAAC;MACXoD,IAAI,EAAE9B,KAAK;MACX6D,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEvE;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMwE,wBAAwB,CAAC;EAG3BlD,WAAWA,CACX;EACAmD,MAAM,EACN;EACAC,WAAW,GAAG,KAAK,EAAE;IAAAlD,eAAA;IAAAA,eAAA;IACjB,IAAI,CAACiD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;AACJ;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EAUZ;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,OAAO,IAAI,IAAI,CAACA,OAAO,CAACD,QAAQ;EAChD;EACA;EACA,IAAI7F,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC+F,SAAS;EACzB;EACA;;EAIA;EACA,IAAIhG,QAAQA,CAAA,EAAG;IACX,OAAQ,IAAI,CAACU,KAAK,IAAI,IAAI,CAACA,KAAK,CAACV,QAAQ,IAAK,IAAI,CAACiG,SAAS;EAChE;EACA,IAAIjG,QAAQA,CAACkG,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;EAC1B;EACA;EACA,IAAIC,aAAaA,CAAA,EAAG;IAAA,IAAAC,aAAA;IAChB,OAAO,IAAI,CAACC,oBAAoB,GAC1B,IAAI,CAACN,OAAO,CAACI,aAAa,CAAC,CAAC,GAC5B,CAAC,GAAAC,aAAA,GAAC,IAAI,CAACL,OAAO,cAAAK,aAAA,eAAZA,aAAA,CAAcD,aAAa;EACvC;EACA;EACA,IAAIG,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,CAAC,EAAE,IAAI,CAACP,OAAO,IAAI,IAAI,CAACA,OAAO,CAACO,4BAA4B,CAAC;EACxE;EACA;EACA;;EAMA9D,WAAWA,CAAA,EAAG;IAAAE,eAAA,mBA7CHzB,MAAM,CAACM,UAAU,CAAC;IAAAmB,eAAA,6BACRzB,MAAM,CAACO,iBAAiB,CAAC;IAAAkB,eAAA,kBACpCzB,MAAM,CAACoB,2BAA2B,EAAE;MAAEQ,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAH,eAAA,gBACzDzB,MAAM,CAACqB,YAAY,EAAE;MAAEO,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAH,eAAA,+BACzB,KAAK;IAAAA,eAAA,oBAChB,KAAK;IAAAA,eAAA,kBACP,KAAK;IAAAA,eAAA,oBACH,KAAK;IAAAA,eAAA,+BACM,EAAE;IAAAA,eAAA;IAWzB;IAAAA,eAAA,aACKzB,MAAM,CAACL,YAAY,CAAC,CAAC+B,KAAK,CAAC,aAAa,CAAC;IAAAD,eAAA,4BAoB1B,IAAIjB,YAAY,CAAC,CAAC;IACtC;IAAAiB,eAAA;IAEA;IAAAA,eAAA,wBACgB,IAAIb,OAAO,CAAC,CAAC;IAEzB,MAAM0E,WAAW,GAAGtF,MAAM,CAACkB,sBAAsB,CAAC;IAClDoE,WAAW,CAACC,IAAI,CAACtE,uBAAuB,CAAC;IACzCqE,WAAW,CAACC,IAAI,CAACpE,qBAAqB,CAAC;IACvC,IAAI,CAACiE,oBAAoB,GAAG,CAAC,CAAC,IAAI,CAACN,OAAO,IAAIrE,QAAQ,CAAC,IAAI,CAACqE,OAAO,CAACI,aAAa,CAAC;EACtF;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIM,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IAAA,IAAAC,WAAA;IACZ;IACA,OAAO,CAAC,EAAAA,WAAA,OAAI,CAACC,KAAK,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,aAAa,CAACC,WAAW,KAAI,EAAE,EAAEC,IAAI,CAAC,CAAC;EAC/D;EACA;EACAC,MAAMA,CAACC,SAAS,GAAG,IAAI,EAAE;IACrB,IAAI,CAAC,IAAI,CAAClB,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACmB,kBAAkB,CAACC,YAAY,CAAC,CAAC;MACtC,IAAIF,SAAS,EAAE;QACX,IAAI,CAACG,yBAAyB,CAAC,CAAC;MACpC;IACJ;EACJ;EACA;EACAC,QAAQA,CAACJ,SAAS,GAAG,IAAI,EAAE;IACvB,IAAI,IAAI,CAAClB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,IAAI,CAACmB,kBAAkB,CAACC,YAAY,CAAC,CAAC;MACtC,IAAIF,SAAS,EAAE;QACX,IAAI,CAACG,yBAAyB,CAAC,CAAC;MACpC;IACJ;EACJ;EACA;EACAE,KAAKA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACpB;IACA;IACA,MAAMC,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACtC,IAAI,OAAOD,OAAO,CAACH,KAAK,KAAK,UAAU,EAAE;MACrCG,OAAO,CAACH,KAAK,CAACE,OAAO,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIG,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAAClB,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB,IAAI,CAACS,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIS,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACnB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACS,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;EACAU,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACnB,SAAS;EACzB;EACA;EACAoB,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,CAACA,KAAK,CAACC,OAAO,KAAKpH,KAAK,IAAImH,KAAK,CAACC,OAAO,KAAKnH,KAAK,KAAK,CAACC,cAAc,CAACiH,KAAK,CAAC,EAAE;MAChF,IAAI,CAACE,qBAAqB,CAAC,CAAC;MAC5B;MACAF,KAAK,CAACG,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACID,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAAClI,QAAQ,EAAE;MAChB,IAAI,CAACgG,SAAS,GAAG,IAAI,CAACF,QAAQ,GAAG,CAAC,IAAI,CAACE,SAAS,GAAG,IAAI;MACvD,IAAI,CAACmB,kBAAkB,CAACC,YAAY,CAAC,CAAC;MACtC,IAAI,CAACC,yBAAyB,CAAC,IAAI,CAAC;IACxC;EACJ;EACA;EACA;EACA;EACA;EACAe,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACpI,QAAQ,GAAG,IAAI,GAAG,GAAG;EACrC;EACA;EACA2H,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACU,QAAQ,CAACvB,aAAa;EACtC;EACAwB,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACtC,SAAS,EAAE;MAChB,MAAMW,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAIA,SAAS,KAAK,IAAI,CAAC4B,oBAAoB,EAAE;QACzC,IAAI,IAAI,CAACA,oBAAoB,EAAE;UAC3B,IAAI,CAACC,aAAa,CAACC,IAAI,CAAC,CAAC;QAC7B;QACA,IAAI,CAACF,oBAAoB,GAAG5B,SAAS;MACzC;IACJ;EACJ;EACA+B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,aAAa,CAACG,QAAQ,CAAC,CAAC;EACjC;EACA;EACAtB,yBAAyBA,CAACzB,WAAW,GAAG,KAAK,EAAE;IAC3C,IAAI,CAACgD,iBAAiB,CAACC,IAAI,CAAC,IAAInD,wBAAwB,CAAC,IAAI,EAAEE,WAAW,CAAC,CAAC;EAChF;AAGJ;AAACkD,UAAA,GAnLKjD,SAAS;AAAAnD,eAAA,CAATmD,SAAS,wBAAAkD,mBAAA7F,iBAAA;EAAA,YAAAA,iBAAA,IAiLwF2C,UAAS;AAAA;AAAAnD,eAAA,CAjL1GmD,SAAS,8BA/BkEnG,EAAE,CAAAyD,iBAAA;EAAAC,IAAA,EAiNQyC,UAAS;EAAAxC,SAAA;EAAA2F,SAAA,WAAAC,iBAAArJ,EAAA,EAAAO,GAAA;IAAA,IAAAP,EAAA;MAjNnBF,EAAE,CAAAwJ,WAAA,CAAAC,GAAA;IAAA;IAAA,IAAAvJ,EAAA;MAAA,IAAAwJ,EAAA;MAAF1J,EAAE,CAAA2J,cAAA,CAAAD,EAAA,GAAF1J,EAAE,CAAA4J,WAAA,QAAAnJ,GAAA,CAAA0G,KAAA,GAAAuC,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAjG,SAAA,WAiN8O,QAAQ;EAAAC,QAAA;EAAAC,YAAA,WAAAgG,wBAAA5J,EAAA,EAAAO,GAAA;IAAA,IAAAP,EAAA;MAjNxPF,EAAE,CAAA+J,UAAA,mBAAAC,oCAAA;QAAA,OAiNQvJ,GAAA,CAAA+H,qBAAA,CAAsB,CAAC;MAAA,CAAf,CAAC,qBAAAyB,sCAAAC,MAAA;QAAA,OAATzJ,GAAA,CAAA4H,cAAA,CAAA6B,MAAqB,CAAC;MAAA,CAAd,CAAC;IAAA;IAAA,IAAAhK,EAAA;MAjNnBF,EAAE,CAAAmK,cAAA,OAAA1J,GAAA,CAAA2J,EAiNgB,CAAC;MAjNnBpK,EAAE,CAAAgE,WAAA,kBAAAvD,GAAA,CAAAF,QAAA,mBAiNQE,GAAA,CAAAH,QAAA,CAAA2D,QAAA,CAAkB,CAAC;MAjN7BjE,EAAE,CAAAmF,WAAA,4BAAA1E,GAAA,CAAAF,QAiNgB,CAAC,4BAAAE,GAAA,CAAA2F,QAAD,CAAC,0BAAA3F,GAAA,CAAAsG,MAAD,CAAC,4BAAAtG,GAAA,CAAAH,QAAD,CAAC;IAAA;EAAA;EAAA6D,MAAA;IAAAqC,KAAA;IAAA4D,EAAA;IAAA9J,QAAA,8BAAqHkB,gBAAgB;EAAA;EAAA6I,OAAA;IAAAnB,iBAAA;EAAA;EAAA9E,QAAA;EAAAK,kBAAA,EAAA6F,GAAA;EAAA3F,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAyF,oBAAArK,EAAA,EAAAO,GAAA;IAAA,IAAAP,EAAA;MAjNxJF,EAAE,CAAAgF,eAAA,CAAAwF,GAAA;MAAFxK,EAAE,CAAAyK,UAAA,IAAAC,iCAAA,gCAiNotC,CAAC;MAjNvtC1K,EAAE,CAAAkF,YAAA,EAiNk+C,CAAC;MAjNr+ClF,EAAE,CAAAW,cAAA,gBAiNwhD,CAAC;MAjN3hDX,EAAE,CAAAkF,YAAA,KAiNijD,CAAC;MAjNpjDlF,EAAE,CAAAa,YAAA,CAiNwjD,CAAC;MAjN3jDb,EAAE,CAAAyK,UAAA,IAAAjK,iCAAA,gCAiNsrD,CAAC,IAAAE,iCAAA,iBAA6U,CAAC;MAjNvgEV,EAAE,CAAAC,SAAA,YAiNixE,CAAC;IAAA;IAAA,IAAAC,EAAA;MAjNpxEF,EAAE,CAAA2K,aAAA,CAAAlK,GAAA,CAAA2F,QAAA,SAiNi7C,CAAC;MAjNp7CpG,EAAE,CAAAc,SAAA,EAiNs5D,CAAC;MAjNz5Dd,EAAE,CAAA2K,aAAA,EAAAlK,GAAA,CAAA2F,QAAA,IAAA3F,GAAA,CAAAF,QAAA,KAAAE,GAAA,CAAAmG,4BAAA,SAiNs5D,CAAC;MAjNz5D5G,EAAE,CAAAc,SAAA,CAiN2kE,CAAC;MAjN9kEd,EAAE,CAAA2K,aAAA,CAAAlK,GAAA,CAAAO,KAAA,IAAAP,GAAA,CAAAO,KAAA,CAAAoC,MAAA,SAiN2kE,CAAC;MAjN9kEpD,EAAE,CAAAc,SAAA,CAiNstE,CAAC;MAjNztEd,EAAE,CAAAK,UAAA,qBAAAI,GAAA,CAAAwH,eAAA,EAiNstE,CAAC,sBAAAxH,GAAA,CAAAH,QAAA,IAAAG,GAAA,CAAAgG,aAAiD,CAAC;IAAA;EAAA;EAAAmE,YAAA,GAAqzHtI,iBAAiB,EAA6GD,SAAS;EAAA+C,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAExxM;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnNiFvF,EAAE,CAAAwF,iBAAA,CAmNQW,SAAS,EAAc,CAAC;IACvGzC,IAAI,EAAEjC,SAAS;IACfgE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEtB,QAAQ,EAAE,WAAW;MAAEyB,IAAI,EAAE;QAClD,MAAM,EAAE,QAAQ;QAChB,iCAAiC,EAAE,UAAU;QAC7C,iCAAiC,EAAE,UAAU;QAC7C,+BAA+B,EAAE,QAAQ;QACzC,iCAAiC,EAAE,UAAU;QAC7C,MAAM,EAAE,IAAI;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,qBAAqB;QAC7C,SAAS,EAAE,yBAAyB;QACpC,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE;MACb,CAAC;MAAER,aAAa,EAAE3D,iBAAiB,CAACiE,IAAI;MAAEL,eAAe,EAAE3D,uBAAuB,CAACiE,MAAM;MAAEiF,OAAO,EAAE,CAACvI,iBAAiB,EAAED,SAAS,CAAC;MAAEyC,QAAQ,EAAE,w9CAAw9C;MAAEM,MAAM,EAAE,CAAC,ivHAAivH;IAAE,CAAC;EACj3K,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEoB,KAAK,EAAE,CAAC;MAChD9C,IAAI,EAAE9B;IACV,CAAC,CAAC;IAAEwI,EAAE,EAAE,CAAC;MACL1G,IAAI,EAAE9B;IACV,CAAC,CAAC;IAAEtB,QAAQ,EAAE,CAAC;MACXoD,IAAI,EAAE9B,KAAK;MACX6D,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEvE;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0H,iBAAiB,EAAE,CAAC;MACpBxF,IAAI,EAAEzB;IACV,CAAC,CAAC;IAAEkF,KAAK,EAAE,CAAC;MACRzD,IAAI,EAAExB,SAAS;MACfuD,IAAI,EAAE,CAAC,MAAM,EAAE;QAAEqF,MAAM,EAAE;MAAK,CAAC;IACnC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAACC,WAAW,EAAEjD,OAAO,EAAEkD,YAAY,EAAE;EACvE,IAAIA,YAAY,CAACC,MAAM,EAAE;IACrB,IAAIC,YAAY,GAAGpD,OAAO,CAACqD,OAAO,CAAC,CAAC;IACpC,IAAIC,MAAM,GAAGJ,YAAY,CAACG,OAAO,CAAC,CAAC;IACnC,IAAIE,YAAY,GAAG,CAAC;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,WAAW,GAAG,CAAC,EAAEO,CAAC,EAAE,EAAE;MACtC,IAAIJ,YAAY,CAACI,CAAC,CAAC,CAACvK,KAAK,IAAImK,YAAY,CAACI,CAAC,CAAC,CAACvK,KAAK,KAAKqK,MAAM,CAACC,YAAY,CAAC,EAAE;QACzEA,YAAY,EAAE;MAClB;IACJ;IACA,OAAOA,YAAY;EACvB;EACA,OAAO,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,wBAAwBA,CAACC,YAAY,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,WAAW,EAAE;EAC9F,IAAIH,YAAY,GAAGE,qBAAqB,EAAE;IACtC,OAAOF,YAAY;EACvB;EACA,IAAIA,YAAY,GAAGC,YAAY,GAAGC,qBAAqB,GAAGC,WAAW,EAAE;IACnE,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGG,WAAW,GAAGF,YAAY,CAAC;EACjE;EACA,OAAOC,qBAAqB;AAChC;AAEA,SAASxF,SAAS,IAAI/D,CAAC,EAAE2I,6BAA6B,IAAIxI,CAAC,EAAEM,WAAW,IAAIkJ,CAAC,EAAEP,wBAAwB,IAAIQ,CAAC,EAAErJ,2BAA2B,IAAIsJ,CAAC,EAAErJ,YAAY,IAAIsJ,CAAC,EAAElG,wBAAwB,IAAImG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}