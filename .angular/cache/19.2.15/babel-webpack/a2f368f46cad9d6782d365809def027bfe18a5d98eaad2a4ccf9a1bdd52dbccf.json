{"ast": null, "code": "var _DefaultWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../swui-grid.model';\nlet DefaultWidgetComponent = (_DefaultWidgetComponent = class DefaultWidgetComponent {\n  constructor({\n    field,\n    title,\n    value,\n    schema: {\n      type\n    }\n  }) {\n    this.field = field;\n    this.title = title;\n    this.value = value;\n    this.type = type;\n  }\n}, _DefaultWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _DefaultWidgetComponent);\nDefaultWidgetComponent = __decorate([Component({\n  template: `\n    <span class=\"label label-danger\">Widget:</span>\n    <span class=\"label label-danger\">Cannot find '{{ field }}: {{ type }}'</span> {{ value || title }}\n  `,\n  standalone: false\n})], DefaultWidgetComponent);\nexport { DefaultWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "DefaultWidgetComponent", "_DefaultWidgetComponent", "constructor", "field", "title", "value", "schema", "type", "ctorParameters", "undefined", "decorators", "args", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/default-widget/default-widget.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../swui-grid.model';\nlet DefaultWidgetComponent = class DefaultWidgetComponent {\n    constructor({ field, title, value, schema: { type } }) {\n        this.field = field;\n        this.title = title;\n        this.value = value;\n        this.type = type;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nDefaultWidgetComponent = __decorate([\n    Component({\n        template: `\n    <span class=\"label label-danger\">Widget:</span>\n    <span class=\"label label-danger\">Cannot find '{{ field }}: {{ type }}'</span> {{ value || title }}\n  `,\n        standalone: false\n    })\n], DefaultWidgetComponent);\nexport { DefaultWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,oBAAoB;AAC5D,IAAIC,sBAAsB,IAAAC,uBAAA,GAAG,MAAMD,sBAAsB,CAAC;EACtDE,WAAWA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,KAAK;IAAEC,MAAM,EAAE;MAAEC;IAAK;EAAE,CAAC,EAAE;IACnD,IAAI,CAACJ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,IAAI,GAAGA,IAAI;EACpB;AAIJ,CAAC,EAHYN,uBAAA,CAAKO,cAAc,GAAG,MAAM,CACjC;EAAED,IAAI,EAAEE,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEH,IAAI,EAAET,MAAM;IAAEa,IAAI,EAAE,CAACZ,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,uBAAA,CACJ;AACDD,sBAAsB,GAAGJ,UAAU,CAAC,CAChCC,SAAS,CAAC;EACNe,QAAQ,EAAE;AAClB;AACA;AACA,GAAG;EACKC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEb,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}