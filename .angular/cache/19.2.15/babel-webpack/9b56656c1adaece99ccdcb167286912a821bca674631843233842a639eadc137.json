{"ast": null, "code": "var _DocsButtonsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs-buttons.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../docs.component.scss?ngResource\";\nimport __NG_CLI_RESOURCE__2 from \"./docs-buttons.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsButtonsComponent = (_DocsButtonsComponent = class DocsButtonsComponent {\n  constructor() {}\n  ngOnInit() {}\n}, _DocsButtonsComponent.ctorParameters = () => [], _DocsButtonsComponent);\nDocsButtonsComponent = __decorate([Component({\n  selector: 'lib-docs-buttons',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1, __NG_CLI_RESOURCE__2]\n})], DocsButtonsComponent);\nexport { DocsButtonsComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "__NG_CLI_RESOURCE__2", "Component", "DocsButtonsComponent", "_DocsButtonsComponent", "constructor", "ngOnInit", "ctorParameters", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/styles/docs/docs-buttons/docs-buttons.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs-buttons.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../docs.component.scss?ngResource\";\nimport __NG_CLI_RESOURCE__2 from \"./docs-buttons.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsButtonsComponent = class DocsButtonsComponent {\n    constructor() { }\n    ngOnInit() {\n    }\n    static { this.ctorParameters = () => []; }\n};\nDocsButtonsComponent = __decorate([\n    Component({\n        selector: 'lib-docs-buttons',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1, __NG_CLI_RESOURCE__2]\n    })\n], DocsButtonsComponent);\nexport { DocsButtonsComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,SAASC,SAAS,QAAQ,eAAe;AACzC,IAAIC,oBAAoB,IAAAC,qBAAA,GAAG,MAAMD,oBAAoB,CAAC;EAClDE,WAAWA,CAAA,EAAG,CAAE;EAChBC,QAAQA,CAAA,EAAG,CACX;AAEJ,CAAC,EADYF,qBAAA,CAAKG,cAAc,GAAG,MAAM,EAAE,EAAAH,qBAAA,CAC1C;AACDD,oBAAoB,GAAGL,UAAU,CAAC,CAC9BI,SAAS,CAAC;EACNM,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAEV,oBAAoB;EAC9BW,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACX,oBAAoB,EAAEC,oBAAoB;AACvD,CAAC,CAAC,CACL,EAAEE,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}