{"ast": null, "code": "import { mergeAll } from '../operators/mergeAll';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from './from';\nexport function merge() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = popScheduler(args);\n  var concurrent = popNumber(args, Infinity);\n  var sources = args;\n  return !sources.length ? EMPTY : sources.length === 1 ? innerFrom(sources[0]) : mergeAll(concurrent)(from(sources, scheduler));\n}\n//# sourceMappingURL=merge.js.map", "map": {"version": 3, "names": ["mergeAll", "innerFrom", "EMPTY", "popNumber", "popScheduler", "from", "merge", "args", "_i", "arguments", "length", "scheduler", "concurrent", "Infinity", "sources"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/merge.js"], "sourcesContent": ["import { mergeAll } from '../operators/mergeAll';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from './from';\nexport function merge() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var scheduler = popScheduler(args);\n    var concurrent = popNumber(args, Infinity);\n    var sources = args;\n    return !sources.length\n        ?\n            EMPTY\n        : sources.length === 1\n            ?\n                innerFrom(sources[0])\n            :\n                mergeAll(concurrent)(from(sources, scheduler));\n}\n//# sourceMappingURL=merge.js.map"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAO,SAASC,KAAKA,CAAA,EAAG;EACpB,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,IAAIG,SAAS,GAAGP,YAAY,CAACG,IAAI,CAAC;EAClC,IAAIK,UAAU,GAAGT,SAAS,CAACI,IAAI,EAAEM,QAAQ,CAAC;EAC1C,IAAIC,OAAO,GAAGP,IAAI;EAClB,OAAO,CAACO,OAAO,CAACJ,MAAM,GAEdR,KAAK,GACPY,OAAO,CAACJ,MAAM,KAAK,CAAC,GAEdT,SAAS,CAACa,OAAO,CAAC,CAAC,CAAC,CAAC,GAErBd,QAAQ,CAACY,UAAU,CAAC,CAACP,IAAI,CAACS,OAAO,EAAEH,SAAS,CAAC,CAAC;AAC9D;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}