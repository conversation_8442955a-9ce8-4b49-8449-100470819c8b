{"ast": null, "code": "export var performanceTimestampProvider = {\n  now: function () {\n    return (performanceTimestampProvider.delegate || performance).now();\n  },\n  delegate: undefined\n};\n//# sourceMappingURL=performanceTimestampProvider.js.map", "map": {"version": 3, "names": ["performanceTimestampProvider", "now", "delegate", "performance", "undefined"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/performanceTimestampProvider.js"], "sourcesContent": ["export var performanceTimestampProvider = {\n    now: function () {\n        return (performanceTimestampProvider.delegate || performance).now();\n    },\n    delegate: undefined,\n};\n//# sourceMappingURL=performanceTimestampProvider.js.map"], "mappings": "AAAA,OAAO,IAAIA,4BAA4B,GAAG;EACtCC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACb,OAAO,CAACD,4BAA4B,CAACE,QAAQ,IAAIC,WAAW,EAAEF,GAAG,CAAC,CAAC;EACvE,CAAC;EACDC,QAAQ,EAAEE;AACd,CAAC;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}