{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"hubs\"];\nvar _SwHubConfigService;\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { catchError, first, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { PERMISSIONS_NAMES } from '../sw-hub-auth/permissions';\nclass BaseHubConfig {}\nexport class HubConfig extends BaseHubConfig {}\nlet SwHubConfigService = (_SwHubConfigService = class SwHubConfigService extends HubConfig {\n  constructor(http) {\n    super();\n    this.http = http;\n  }\n  fetch() {\n    return new Promise((resolve, reject) => {\n      this.http.get('/api/config').pipe(first(), map(config => {\n        if (!config) {\n          return undefined;\n        }\n        const {\n            hubs\n          } = config,\n          data = _objectWithoutProperties(config, _excluded);\n        return _objectSpread(_objectSpread({}, data), {}, {\n          hubs: _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, hubs !== null && hubs !== void 0 && hubs.casino ? {\n            casino: {\n              url: hubs.casino,\n              name: 'HUBS.casino',\n              cssClass: 'hub-casino',\n              permission: PERMISSIONS_NAMES.HUB_CASINO\n            }\n          } : {}), hubs !== null && hubs !== void 0 && hubs.engagement ? {\n            engagement: {\n              url: hubs.engagement,\n              name: 'HUBS.engagement',\n              cssClass: 'hub-engagement',\n              permission: PERMISSIONS_NAMES.HUB_ENGAGEMENT\n            }\n          } : {}), hubs !== null && hubs !== void 0 && hubs.analytics ? {\n            analytics: {\n              url: hubs.analytics,\n              name: 'HUBS.analytics',\n              cssClass: 'hub-analytics',\n              permission: PERMISSIONS_NAMES.HUB_ANALYTICS\n            }\n          } : {}), hubs !== null && hubs !== void 0 && hubs.studio ? {\n            studio: {\n              url: hubs.studio,\n              name: 'HUBS.studio',\n              cssClass: 'hub-studio',\n              permission: PERMISSIONS_NAMES.HUB_STUDIO\n            }\n          } : {})\n        });\n      }), catchError(err => {\n        reject(err);\n        return of(undefined);\n      })).subscribe(config => {\n        Object.assign(this, config);\n        resolve(true);\n      });\n    });\n  }\n}, _SwHubConfigService.ctorParameters = () => [{\n  type: HttpClient\n}], _SwHubConfigService);\nSwHubConfigService = __decorate([Injectable()], SwHubConfigService);\nexport { SwHubConfigService };", "map": {"version": 3, "names": ["Injectable", "HttpClient", "catchError", "first", "map", "of", "PERMISSIONS_NAMES", "BaseHubConfig", "HubConfig", "SwHubConfigService", "_SwHubConfigService", "constructor", "http", "fetch", "Promise", "resolve", "reject", "get", "pipe", "config", "undefined", "hubs", "data", "_objectWithoutProperties", "_excluded", "_objectSpread", "casino", "url", "name", "cssClass", "permission", "HUB_CASINO", "engagement", "HUB_ENGAGEMENT", "analytics", "HUB_ANALYTICS", "studio", "HUB_STUDIO", "err", "subscribe", "Object", "assign", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-config/sw-hub-config.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { catchError, first, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { PERMISSIONS_NAMES } from '../sw-hub-auth/permissions';\n\ntype EnvType = 'qa' | 'dev' | 'stg' | 'prod';\ntype LocationType = 'asia' | 'europe';\n\nexport interface HubConfigHub {\n  url: string;\n  name?: string;\n  cssClass?: string;\n  permission?: string | string[];\n}\n\nexport interface HubConfigHubs {\n  [key: string]: HubConfigHub;\n}\n\nabstract class BaseHubConfig {\n  bridge?: string;\n  loginUrl?: string;\n  logoutUrl?: string;\n  defaultUrl?: string;\n  oauthClientId?: string;\n  envName?: EnvType;\n  locationName?: LocationType;\n  logo?: {\n    main?: string;\n    solo?: string;\n    white?: string;\n    favicon?: string;\n  };\n}\n\nexport interface HubUrls extends BaseHubConfig {\n  hubs?: {\n    casino?: string;\n    engagement?: string;\n    analytics?: string;\n    studio?: string;\n  };\n}\n\nexport abstract class HubConfig extends BaseHubConfig {\n  hubs?: HubConfigHubs;\n}\n\n@Injectable()\nexport class SwHubConfigService extends HubConfig {\n\n  constructor( private readonly http: HttpClient ) {\n    super();\n  }\n\n  fetch(): Promise<boolean> {\n    return new Promise(( resolve, reject ) => {\n      this.http.get<HubUrls>('/api/config').pipe(\n        first(),\n        map<HubUrls, HubConfig | undefined>(config => {\n          if (!config) {\n            return undefined;\n          }\n          const { hubs, ...data } = config;\n          return {\n            ...data,\n            hubs: {\n              ...(hubs?.casino ? {\n                casino: {\n                  url: hubs.casino,\n                  name: 'HUBS.casino',\n                  cssClass: 'hub-casino',\n                  permission: PERMISSIONS_NAMES.HUB_CASINO\n                }\n              } : {}),\n              ...(hubs?.engagement ? {\n                engagement: {\n                  url: hubs.engagement,\n                  name: 'HUBS.engagement',\n                  cssClass: 'hub-engagement',\n                  permission: PERMISSIONS_NAMES.HUB_ENGAGEMENT\n                }\n              } : {}),\n              ...(hubs?.analytics ? {\n                analytics: {\n                  url: hubs.analytics,\n                  name: 'HUBS.analytics',\n                  cssClass: 'hub-analytics',\n                  permission: PERMISSIONS_NAMES.HUB_ANALYTICS\n                }\n              } : {}),\n              ...(hubs?.studio ? {\n                studio: {\n                  url: hubs.studio,\n                  name: 'HUBS.studio',\n                  cssClass: 'hub-studio',\n                  permission: PERMISSIONS_NAMES.HUB_STUDIO\n                }\n              } : {}),\n            }\n          };\n        }),\n        catchError(err => {\n          reject(err);\n          return of(undefined);\n        })\n      ).subscribe(config => {\n        Object.assign(this, config);\n        resolve(true);\n      });\n    });\n  }\n}\n"], "mappings": ";;;;;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,UAAU,EAAEC,KAAK,EAAEC,GAAG,QAAQ,gBAAgB;AACvD,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,iBAAiB,QAAQ,4BAA4B;AAgB9D,MAAeC,aAAa;AAyB5B,OAAM,MAAgBC,SAAU,SAAQD,aAAa;AAK9C,IAAME,kBAAkB,IAAAC,mBAAA,GAAxB,MAAMD,kBAAmB,SAAQD,SAAS;EAE/CG,YAA8BC,IAAgB;IAC5C,KAAK,EAAE;IADqB,KAAAA,IAAI,GAAJA,IAAI;EAElC;EAEAC,KAAKA,CAAA;IACH,OAAO,IAAIC,OAAO,CAAC,CAAEC,OAAO,EAAEC,MAAM,KAAK;MACvC,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAU,aAAa,CAAC,CAACC,IAAI,CACxCf,KAAK,EAAE,EACPC,GAAG,CAAiCe,MAAM,IAAG;QAC3C,IAAI,CAACA,MAAM,EAAE;UACX,OAAOC,SAAS;QAClB;QACA,MAAM;YAAEC;UAAa,CAAE,GAAGF,MAAM;UAAfG,IAAI,GAAAC,wBAAA,CAAKJ,MAAM,EAAAK,SAAA;QAChC,OAAAC,aAAA,CAAAA,aAAA,KACKH,IAAI;UACPD,IAAI,EAAAI,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACEJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,MAAM,GAAG;YACjBA,MAAM,EAAE;cACNC,GAAG,EAAEN,IAAI,CAACK,MAAM;cAChBE,IAAI,EAAE,aAAa;cACnBC,QAAQ,EAAE,YAAY;cACtBC,UAAU,EAAExB,iBAAiB,CAACyB;;WAEjC,GAAG,EAAE,GACFV,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEW,UAAU,GAAG;YACrBA,UAAU,EAAE;cACVL,GAAG,EAAEN,IAAI,CAACW,UAAU;cACpBJ,IAAI,EAAE,iBAAiB;cACvBC,QAAQ,EAAE,gBAAgB;cAC1BC,UAAU,EAAExB,iBAAiB,CAAC2B;;WAEjC,GAAG,EAAE,GACFZ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEa,SAAS,GAAG;YACpBA,SAAS,EAAE;cACTP,GAAG,EAAEN,IAAI,CAACa,SAAS;cACnBN,IAAI,EAAE,gBAAgB;cACtBC,QAAQ,EAAE,eAAe;cACzBC,UAAU,EAAExB,iBAAiB,CAAC6B;;WAEjC,GAAG,EAAE,GACFd,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEe,MAAM,GAAG;YACjBA,MAAM,EAAE;cACNT,GAAG,EAAEN,IAAI,CAACe,MAAM;cAChBR,IAAI,EAAE,aAAa;cACnBC,QAAQ,EAAE,YAAY;cACtBC,UAAU,EAAExB,iBAAiB,CAAC+B;;WAEjC,GAAG,EAAE;QACP;MAEL,CAAC,CAAC,EACFnC,UAAU,CAACoC,GAAG,IAAG;QACftB,MAAM,CAACsB,GAAG,CAAC;QACX,OAAOjC,EAAE,CAACe,SAAS,CAAC;MACtB,CAAC,CAAC,CACH,CAACmB,SAAS,CAACpB,MAAM,IAAG;QACnBqB,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEtB,MAAM,CAAC;QAC3BJ,OAAO,CAAC,IAAI,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;;;;AA9DWN,kBAAkB,GAAAiC,UAAA,EAD9B1C,UAAU,EAAE,C,EACAS,kBAAkB,CA+D9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}