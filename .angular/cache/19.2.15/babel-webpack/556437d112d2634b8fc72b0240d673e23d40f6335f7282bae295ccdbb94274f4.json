{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _ResourceLoaderImpl;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Version, ViewEncapsulation, Injector, Compiler, Injectable, createPlatformFactory, COMPILER_OPTIONS, CompilerFactory } from '@angular/core';\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport { platformBrowser } from '@angular/platform-browser';\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.2.14');\nconst COMPILER_PROVIDERS = [{\n  provide: Compiler,\n  useFactory: () => new Compiler()\n}];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n */\nclass JitCompilerFactory {\n  /** @internal */\n  constructor(defaultOptions) {\n    _defineProperty(this, \"_defaultOptions\", void 0);\n    const compilerOptions = {\n      defaultEncapsulation: ViewEncapsulation.Emulated\n    };\n    this._defaultOptions = [compilerOptions, ...defaultOptions];\n  }\n  createCompiler(options = []) {\n    const opts = _mergeOptions(this._defaultOptions.concat(options));\n    const injector = Injector.create({\n      providers: [COMPILER_PROVIDERS, {\n        provide: CompilerConfig,\n        useFactory: () => {\n          return new CompilerConfig({\n            defaultEncapsulation: opts.defaultEncapsulation,\n            preserveWhitespaces: opts.preserveWhitespaces\n          });\n        },\n        deps: []\n      }, opts.providers]\n    });\n    return injector.get(Compiler);\n  }\n}\nfunction _mergeOptions(optionsArr) {\n  return {\n    defaultEncapsulation: _lastDefined(optionsArr.map(options => options.defaultEncapsulation)),\n    providers: _mergeArrays(optionsArr.map(options => options.providers)),\n    preserveWhitespaces: _lastDefined(optionsArr.map(options => options.preserveWhitespaces))\n  };\n}\nfunction _lastDefined(args) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (args[i] !== undefined) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\nfunction _mergeArrays(parts) {\n  const result = [];\n  parts.forEach(part => part && result.push(...part));\n  return result;\n}\nclass ResourceLoaderImpl extends ResourceLoader {\n  get(url) {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n    xhr.onload = function () {\n      const response = xhr.response;\n      let status = xhr.status;\n      // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n    xhr.onerror = function () {\n      reject(`Failed to load ${url}`);\n    };\n    xhr.send();\n    return promise;\n  }\n}\n_ResourceLoaderImpl = ResourceLoaderImpl;\n_defineProperty(ResourceLoaderImpl, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_ResourceLoaderImpl_BaseFactory;\n  return function _ResourceLoaderImpl_Factory(__ngFactoryType__) {\n    return (ɵ_ResourceLoaderImpl_BaseFactory || (ɵ_ResourceLoaderImpl_BaseFactory = i0.ɵɵgetInheritedFactory(_ResourceLoaderImpl)))(__ngFactoryType__ || _ResourceLoaderImpl);\n  };\n})());\n_defineProperty(ResourceLoaderImpl, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _ResourceLoaderImpl,\n  factory: _ResourceLoaderImpl.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResourceLoaderImpl, [{\n    type: Injectable\n  }], null, null);\n})();\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [{\n  provide: COMPILER_OPTIONS,\n  useValue: {\n    providers: [{\n      provide: ResourceLoader,\n      useClass: ResourceLoaderImpl,\n      deps: []\n    }]\n  },\n  multi: true\n}, {\n  provide: CompilerFactory,\n  useClass: JitCompilerFactory,\n  deps: [COMPILER_OPTIONS]\n}];\n/**\n * @publicApi\n */\nconst platformBrowserDynamic = createPlatformFactory(platformBrowser, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\nexport { JitCompilerFactory, VERSION, platformBrowserDynamic, INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS };", "map": {"version": 3, "names": ["i0", "Version", "ViewEncapsulation", "Injector", "Compiler", "Injectable", "createPlatformFactory", "COMPILER_OPTIONS", "CompilerFactory", "CompilerConfig", "Resource<PERSON><PERSON>der", "platformBrowser", "VERSION", "COMPILER_PROVIDERS", "provide", "useFactory", "JitCompilerFactory", "constructor", "defaultOptions", "_defineProperty", "compilerOptions", "defaultEncapsulation", "Emulated", "_defaultOptions", "createCompiler", "options", "opts", "_mergeOptions", "concat", "injector", "create", "providers", "preserveWhitespaces", "deps", "get", "optionsArr", "_lastDefined", "map", "_mergeArrays", "args", "i", "length", "undefined", "parts", "result", "for<PERSON>ach", "part", "push", "ResourceLoaderImpl", "url", "resolve", "reject", "promise", "Promise", "res", "rej", "xhr", "XMLHttpRequest", "open", "responseType", "onload", "response", "status", "onerror", "send", "_ResourceLoaderImpl", "ɵ_ResourceLoaderImpl_BaseFactory", "_ResourceLoaderImpl_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "ngDevMode", "ɵsetClassMetadata", "type", "INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "useValue", "useClass", "multi", "platformBrowserDynamic", "ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/platform-browser-dynamic/fesm2022/platform-browser-dynamic.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Version, ViewEncapsulation, Injector, Compiler, Injectable, createPlatformFactory, COMPILER_OPTIONS, CompilerFactory } from '@angular/core';\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport { platformBrowser } from '@angular/platform-browser';\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.2.14');\n\nconst COMPILER_PROVIDERS = [\n    { provide: Compiler, useFactory: () => new Compiler() },\n];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n */\nclass JitCompilerFactory {\n    _defaultOptions;\n    /** @internal */\n    constructor(defaultOptions) {\n        const compilerOptions = {\n            defaultEncapsulation: ViewEncapsulation.Emulated,\n        };\n        this._defaultOptions = [compilerOptions, ...defaultOptions];\n    }\n    createCompiler(options = []) {\n        const opts = _mergeOptions(this._defaultOptions.concat(options));\n        const injector = Injector.create({\n            providers: [\n                COMPILER_PROVIDERS,\n                {\n                    provide: CompilerConfig,\n                    useFactory: () => {\n                        return new CompilerConfig({\n                            defaultEncapsulation: opts.defaultEncapsulation,\n                            preserveWhitespaces: opts.preserveWhitespaces,\n                        });\n                    },\n                    deps: [],\n                },\n                opts.providers,\n            ],\n        });\n        return injector.get(Compiler);\n    }\n}\nfunction _mergeOptions(optionsArr) {\n    return {\n        defaultEncapsulation: _lastDefined(optionsArr.map((options) => options.defaultEncapsulation)),\n        providers: _mergeArrays(optionsArr.map((options) => options.providers)),\n        preserveWhitespaces: _lastDefined(optionsArr.map((options) => options.preserveWhitespaces)),\n    };\n}\nfunction _lastDefined(args) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (args[i] !== undefined) {\n            return args[i];\n        }\n    }\n    return undefined;\n}\nfunction _mergeArrays(parts) {\n    const result = [];\n    parts.forEach((part) => part && result.push(...part));\n    return result;\n}\n\nclass ResourceLoaderImpl extends ResourceLoader {\n    get(url) {\n        let resolve;\n        let reject;\n        const promise = new Promise((res, rej) => {\n            resolve = res;\n            reject = rej;\n        });\n        const xhr = new XMLHttpRequest();\n        xhr.open('GET', url, true);\n        xhr.responseType = 'text';\n        xhr.onload = function () {\n            const response = xhr.response;\n            let status = xhr.status;\n            // fix status code when it is 0 (0 status is undocumented).\n            // Occurs when accessing file resources or on Android 4.1 stock browser\n            // while retrieving files from application cache.\n            if (status === 0) {\n                status = response ? 200 : 0;\n            }\n            if (200 <= status && status <= 300) {\n                resolve(response);\n            }\n            else {\n                reject(`Failed to load ${url}`);\n            }\n        };\n        xhr.onerror = function () {\n            reject(`Failed to load ${url}`);\n        };\n        xhr.send();\n        return promise;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: ResourceLoaderImpl, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: ResourceLoaderImpl });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: ResourceLoaderImpl, decorators: [{\n            type: Injectable\n        }] });\n\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [\n    {\n        provide: COMPILER_OPTIONS,\n        useValue: { providers: [{ provide: ResourceLoader, useClass: ResourceLoaderImpl, deps: [] }] },\n        multi: true,\n    },\n    { provide: CompilerFactory, useClass: JitCompilerFactory, deps: [COMPILER_OPTIONS] },\n];\n/**\n * @publicApi\n */\nconst platformBrowserDynamic = createPlatformFactory(platformBrowser, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n\nexport { JitCompilerFactory, VERSION, platformBrowserDynamic, INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS };\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,eAAe;AACpJ,SAASC,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,SAASC,eAAe,QAAQ,2BAA2B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,IAAIX,OAAO,CAAC,SAAS,CAAC;AAEtC,MAAMY,kBAAkB,GAAG,CACvB;EAAEC,OAAO,EAAEV,QAAQ;EAAEW,UAAU,EAAEA,CAAA,KAAM,IAAIX,QAAQ,CAAC;AAAE,CAAC,CAC1D;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,kBAAkB,CAAC;EAErB;EACAC,WAAWA,CAACC,cAAc,EAAE;IAAAC,eAAA;IACxB,MAAMC,eAAe,GAAG;MACpBC,oBAAoB,EAAEnB,iBAAiB,CAACoB;IAC5C,CAAC;IACD,IAAI,CAACC,eAAe,GAAG,CAACH,eAAe,EAAE,GAAGF,cAAc,CAAC;EAC/D;EACAM,cAAcA,CAACC,OAAO,GAAG,EAAE,EAAE;IACzB,MAAMC,IAAI,GAAGC,aAAa,CAAC,IAAI,CAACJ,eAAe,CAACK,MAAM,CAACH,OAAO,CAAC,CAAC;IAChE,MAAMI,QAAQ,GAAG1B,QAAQ,CAAC2B,MAAM,CAAC;MAC7BC,SAAS,EAAE,CACPlB,kBAAkB,EAClB;QACIC,OAAO,EAAEL,cAAc;QACvBM,UAAU,EAAEA,CAAA,KAAM;UACd,OAAO,IAAIN,cAAc,CAAC;YACtBY,oBAAoB,EAAEK,IAAI,CAACL,oBAAoB;YAC/CW,mBAAmB,EAAEN,IAAI,CAACM;UAC9B,CAAC,CAAC;QACN,CAAC;QACDC,IAAI,EAAE;MACV,CAAC,EACDP,IAAI,CAACK,SAAS;IAEtB,CAAC,CAAC;IACF,OAAOF,QAAQ,CAACK,GAAG,CAAC9B,QAAQ,CAAC;EACjC;AACJ;AACA,SAASuB,aAAaA,CAACQ,UAAU,EAAE;EAC/B,OAAO;IACHd,oBAAoB,EAAEe,YAAY,CAACD,UAAU,CAACE,GAAG,CAAEZ,OAAO,IAAKA,OAAO,CAACJ,oBAAoB,CAAC,CAAC;IAC7FU,SAAS,EAAEO,YAAY,CAACH,UAAU,CAACE,GAAG,CAAEZ,OAAO,IAAKA,OAAO,CAACM,SAAS,CAAC,CAAC;IACvEC,mBAAmB,EAAEI,YAAY,CAACD,UAAU,CAACE,GAAG,CAAEZ,OAAO,IAAKA,OAAO,CAACO,mBAAmB,CAAC;EAC9F,CAAC;AACL;AACA,SAASI,YAAYA,CAACG,IAAI,EAAE;EACxB,KAAK,IAAIC,CAAC,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,IAAID,IAAI,CAACC,CAAC,CAAC,KAAKE,SAAS,EAAE;MACvB,OAAOH,IAAI,CAACC,CAAC,CAAC;IAClB;EACJ;EACA,OAAOE,SAAS;AACpB;AACA,SAASJ,YAAYA,CAACK,KAAK,EAAE;EACzB,MAAMC,MAAM,GAAG,EAAE;EACjBD,KAAK,CAACE,OAAO,CAAEC,IAAI,IAAKA,IAAI,IAAIF,MAAM,CAACG,IAAI,CAAC,GAAGD,IAAI,CAAC,CAAC;EACrD,OAAOF,MAAM;AACjB;AAEA,MAAMI,kBAAkB,SAAStC,cAAc,CAAC;EAC5CwB,GAAGA,CAACe,GAAG,EAAE;IACL,IAAIC,OAAO;IACX,IAAIC,MAAM;IACV,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACtCL,OAAO,GAAGI,GAAG;MACbH,MAAM,GAAGI,GAAG;IAChB,CAAC,CAAC;IACF,MAAMC,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAET,GAAG,EAAE,IAAI,CAAC;IAC1BO,GAAG,CAACG,YAAY,GAAG,MAAM;IACzBH,GAAG,CAACI,MAAM,GAAG,YAAY;MACrB,MAAMC,QAAQ,GAAGL,GAAG,CAACK,QAAQ;MAC7B,IAAIC,MAAM,GAAGN,GAAG,CAACM,MAAM;MACvB;MACA;MACA;MACA,IAAIA,MAAM,KAAK,CAAC,EAAE;QACdA,MAAM,GAAGD,QAAQ,GAAG,GAAG,GAAG,CAAC;MAC/B;MACA,IAAI,GAAG,IAAIC,MAAM,IAAIA,MAAM,IAAI,GAAG,EAAE;QAChCZ,OAAO,CAACW,QAAQ,CAAC;MACrB,CAAC,MACI;QACDV,MAAM,CAAC,kBAAkBF,GAAG,EAAE,CAAC;MACnC;IACJ,CAAC;IACDO,GAAG,CAACO,OAAO,GAAG,YAAY;MACtBZ,MAAM,CAAC,kBAAkBF,GAAG,EAAE,CAAC;IACnC,CAAC;IACDO,GAAG,CAACQ,IAAI,CAAC,CAAC;IACV,OAAOZ,OAAO;EAClB;AAGJ;AAACa,mBAAA,GAnCKjB,kBAAkB;AAAA7B,eAAA,CAAlB6B,kBAAkB;EAAA,IAAAkB,gCAAA;EAAA,gBAAAC,4BAAAC,iBAAA;IAAA,QAAAF,gCAAA,KAAAA,gCAAA,GAoC0DlE,EAAE,CAAAqE,qBAAA,CAHoBrB,mBAAkB,IAAAoB,iBAAA,IAAlBpB,mBAAkB;EAAA;AAAA;AAAA7B,eAAA,CAjCpH6B,kBAAkB,+BAoC0DhD,EAAE,CAAAsE,kBAAA;EAAAC,KAAA,EAFwBvB,mBAAkB;EAAAwB,OAAA,EAAlBxB,mBAAkB,CAAAyB;AAAA;AAE9H;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAkF1E,EAAE,CAAA2E,iBAAA,CAAQ3B,kBAAkB,EAAc,CAAC;IACjH4B,IAAI,EAAEvE;EACV,CAAC,CAAC;AAAA;AAEV,MAAMwE,2CAA2C,GAAG,CAChD;EACI/D,OAAO,EAAEP,gBAAgB;EACzBuE,QAAQ,EAAE;IAAE/C,SAAS,EAAE,CAAC;MAAEjB,OAAO,EAAEJ,cAAc;MAAEqE,QAAQ,EAAE/B,kBAAkB;MAAEf,IAAI,EAAE;IAAG,CAAC;EAAE,CAAC;EAC9F+C,KAAK,EAAE;AACX,CAAC,EACD;EAAElE,OAAO,EAAEN,eAAe;EAAEuE,QAAQ,EAAE/D,kBAAkB;EAAEiB,IAAI,EAAE,CAAC1B,gBAAgB;AAAE,CAAC,CACvF;AACD;AACA;AACA;AACA,MAAM0E,sBAAsB,GAAG3E,qBAAqB,CAACK,eAAe,EAAE,gBAAgB,EAAEkE,2CAA2C,CAAC;AAEpI,SAAS7D,kBAAkB,EAAEJ,OAAO,EAAEqE,sBAAsB,EAAEJ,2CAA2C,IAAIK,4CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}