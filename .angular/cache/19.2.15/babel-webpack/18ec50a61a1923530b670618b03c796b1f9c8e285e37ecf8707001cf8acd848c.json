{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiColumnsManagementComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./columns-management.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./columns-management.component.scss?ngResource\";\nimport { Component, EventEmitter, Inject, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { BehaviorSubject, of, Subject } from 'rxjs';\nimport { switchMap, take, takeUntil } from 'rxjs/operators';\nimport { ColumnsManagementDataProvider, LocalStorageColumnsDataProvider } from './columns-management.model';\nfunction buildSelectOptions(schema, columnsSet) {\n  return (schema || []).filter(({\n    field\n  }) => field !== 'row-actions-column' && field !== 'bulk-actions-column').map(({\n    field,\n    title,\n    isListVisible\n  }, index) => {\n    const data = (columnsSet || {})[field];\n    return {\n      id: field,\n      text: title || '',\n      index: data ? data.index : index,\n      selected: data ? data.isListVisible : isListVisible === undefined ? true : isListVisible\n    };\n  }).sort(({\n    index: a\n  }, {\n    index: b\n  }) => a - b);\n}\nlet SwuiColumnsManagementComponent = (_SwuiColumnsManagementComponent = class SwuiColumnsManagementComponent {\n  set gridId(value) {\n    this.gridId$.next(value);\n  }\n  set schema(value) {\n    this._schema = value;\n    this.setSelectOptions(value, this.columnsSet);\n  }\n  constructor(provider) {\n    this.columnsChange = new EventEmitter();\n    this.data = [];\n    this.selected = [];\n    this.columnsSet = {};\n    this.selectOptions = [];\n    this.gridId$ = new BehaviorSubject(undefined);\n    this.destroyed = new Subject();\n    this.dataProvider = provider || new LocalStorageColumnsDataProvider();\n  }\n  ngOnInit() {\n    this.gridId$.pipe(switchMap(gridId => gridId ? this.dataProvider.getColumns(gridId) : of({})), takeUntil(this.destroyed)).subscribe(columnsSet => {\n      this.setColumnsSet(columnsSet);\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed.next(undefined);\n    this.destroyed.complete();\n  }\n  onApply(values) {\n    if (this.menuRef) {\n      this.menuRef.closeMenu();\n    }\n    const gridId = this.gridId$.value;\n    if (gridId) {\n      const selectedData = new Set(values);\n      const data = this.selectOptions.reduce((result, {\n        id,\n        index\n      }) => _objectSpread(_objectSpread({}, result), {}, {\n        [id]: {\n          isListVisible: selectedData.has(id),\n          index\n        }\n      }), {});\n      this.dataProvider.saveColumns(gridId, data).pipe(take(1)).subscribe(columnsSet => {\n        this.setColumnsSet(columnsSet);\n      });\n    }\n  }\n  onCancel() {\n    if (this.menuRef) {\n      this.menuRef.closeMenu();\n    }\n  }\n  setColumnsSet(value) {\n    this.columnsSet = value;\n    this.setSelectOptions(this._schema, value);\n    this.emit();\n  }\n  setSelectOptions(schema, value) {\n    this.selectOptions = buildSelectOptions(schema, value);\n    this.data = this.selectOptions.map(({\n      id,\n      text\n    }) => ({\n      id,\n      text\n    }));\n    this.selected = this.selectOptions.filter(({\n      selected\n    }) => selected).map(({\n      id\n    }) => id);\n  }\n  emit() {\n    if (this._schema) {\n      this.columnsChange.emit(this._schema.reduce((result, {\n        field,\n        isListVisible\n      }, index) => {\n        const data = this.columnsSet[field];\n        return _objectSpread(_objectSpread({}, result), {}, {\n          [field]: {\n            index: data ? data.index : index,\n            isListVisible: data ? data.isListVisible : isListVisible\n          }\n        });\n      }, {}));\n    }\n  }\n}, _SwuiColumnsManagementComponent.ctorParameters = () => [{\n  type: ColumnsManagementDataProvider,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [ColumnsManagementDataProvider]\n  }]\n}], _SwuiColumnsManagementComponent.propDecorators = {\n  gridId: [{\n    type: Input\n  }],\n  schema: [{\n    type: Input\n  }],\n  columnsChange: [{\n    type: Output\n  }],\n  menuRef: [{\n    type: ViewChild,\n    args: [MatMenuTrigger, {\n      static: true\n    }]\n  }]\n}, _SwuiColumnsManagementComponent);\nSwuiColumnsManagementComponent = __decorate([Component({\n  selector: 'lib-swui-columns-management',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiColumnsManagementComponent);\nexport { SwuiColumnsManagementComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "EventEmitter", "Inject", "Input", "Optional", "Output", "ViewChild", "MatMenuTrigger", "BehaviorSubject", "of", "Subject", "switchMap", "take", "takeUntil", "ColumnsManagementDataProvider", "LocalStorageColumnsDataProvider", "buildSelectOptions", "schema", "columnsSet", "filter", "field", "map", "title", "isListVisible", "index", "data", "id", "text", "selected", "undefined", "sort", "a", "b", "SwuiColumnsManagementComponent", "_SwuiColumnsManagementComponent", "gridId", "value", "gridId$", "next", "_schema", "setSelectOptions", "constructor", "provider", "columnsChange", "selectOptions", "destroyed", "dataProvider", "ngOnInit", "pipe", "getColumns", "subscribe", "setColumnsSet", "ngOnDestroy", "complete", "onApply", "values", "menuRef", "closeMenu", "selectedData", "Set", "reduce", "result", "_objectSpread", "has", "saveColumns", "onCancel", "emit", "ctorParameters", "type", "decorators", "args", "propDecorators", "static", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/columns-management/columns-management.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./columns-management.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./columns-management.component.scss?ngResource\";\nimport { Component, EventEmitter, Inject, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { BehaviorSubject, of, Subject } from 'rxjs';\nimport { switchMap, take, takeUntil } from 'rxjs/operators';\nimport { ColumnsManagementDataProvider, LocalStorageColumnsDataProvider } from './columns-management.model';\nfunction buildSelectOptions(schema, columnsSet) {\n    return (schema || [])\n        .filter(({ field }) => field !== 'row-actions-column' && field !== 'bulk-actions-column')\n        .map(({ field, title, isListVisible }, index) => {\n        const data = (columnsSet || {})[field];\n        return {\n            id: field,\n            text: title || '',\n            index: data ? data.index : index,\n            selected: data ? data.isListVisible : isListVisible === undefined ? true : isListVisible\n        };\n    })\n        .sort(({ index: a }, { index: b }) => a - b);\n}\nlet SwuiColumnsManagementComponent = class SwuiColumnsManagementComponent {\n    set gridId(value) {\n        this.gridId$.next(value);\n    }\n    set schema(value) {\n        this._schema = value;\n        this.setSelectOptions(value, this.columnsSet);\n    }\n    constructor(provider) {\n        this.columnsChange = new EventEmitter();\n        this.data = [];\n        this.selected = [];\n        this.columnsSet = {};\n        this.selectOptions = [];\n        this.gridId$ = new BehaviorSubject(undefined);\n        this.destroyed = new Subject();\n        this.dataProvider = provider || new LocalStorageColumnsDataProvider();\n    }\n    ngOnInit() {\n        this.gridId$.pipe(switchMap(gridId => gridId ? this.dataProvider.getColumns(gridId) : of({})), takeUntil(this.destroyed)).subscribe(columnsSet => {\n            this.setColumnsSet(columnsSet);\n        });\n    }\n    ngOnDestroy() {\n        this.destroyed.next(undefined);\n        this.destroyed.complete();\n    }\n    onApply(values) {\n        if (this.menuRef) {\n            this.menuRef.closeMenu();\n        }\n        const gridId = this.gridId$.value;\n        if (gridId) {\n            const selectedData = new Set(values);\n            const data = this.selectOptions.reduce((result, { id, index }) => ({\n                ...result,\n                [id]: {\n                    isListVisible: selectedData.has(id),\n                    index\n                }\n            }), {});\n            this.dataProvider.saveColumns(gridId, data).pipe(take(1)).subscribe(columnsSet => {\n                this.setColumnsSet(columnsSet);\n            });\n        }\n    }\n    onCancel() {\n        if (this.menuRef) {\n            this.menuRef.closeMenu();\n        }\n    }\n    setColumnsSet(value) {\n        this.columnsSet = value;\n        this.setSelectOptions(this._schema, value);\n        this.emit();\n    }\n    setSelectOptions(schema, value) {\n        this.selectOptions = buildSelectOptions(schema, value);\n        this.data = this.selectOptions.map(({ id, text }) => ({ id, text }));\n        this.selected = this.selectOptions\n            .filter(({ selected }) => selected)\n            .map(({ id }) => id);\n    }\n    emit() {\n        if (this._schema) {\n            this.columnsChange.emit(this._schema.reduce((result, { field, isListVisible }, index) => {\n                const data = this.columnsSet[field];\n                return ({\n                    ...result,\n                    [field]: {\n                        index: data ? data.index : index,\n                        isListVisible: data ? data.isListVisible : isListVisible\n                    }\n                });\n            }, {}));\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: ColumnsManagementDataProvider, decorators: [{ type: Optional }, { type: Inject, args: [ColumnsManagementDataProvider,] }] }\n    ]; }\n    static { this.propDecorators = {\n        gridId: [{ type: Input }],\n        schema: [{ type: Input }],\n        columnsChange: [{ type: Output }],\n        menuRef: [{ type: ViewChild, args: [MatMenuTrigger, { static: true },] }]\n    }; }\n};\nSwuiColumnsManagementComponent = __decorate([\n    Component({\n        selector: 'lib-swui-columns-management',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiColumnsManagementComponent);\nexport { SwuiColumnsManagementComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,SAASC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACnG,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,eAAe,EAAEC,EAAE,EAAEC,OAAO,QAAQ,MAAM;AACnD,SAASC,SAAS,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAASC,6BAA6B,EAAEC,+BAA+B,QAAQ,4BAA4B;AAC3G,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,UAAU,EAAE;EAC5C,OAAO,CAACD,MAAM,IAAI,EAAE,EACfE,MAAM,CAAC,CAAC;IAAEC;EAAM,CAAC,KAAKA,KAAK,KAAK,oBAAoB,IAAIA,KAAK,KAAK,qBAAqB,CAAC,CACxFC,GAAG,CAAC,CAAC;IAAED,KAAK;IAAEE,KAAK;IAAEC;EAAc,CAAC,EAAEC,KAAK,KAAK;IACjD,MAAMC,IAAI,GAAG,CAACP,UAAU,IAAI,CAAC,CAAC,EAAEE,KAAK,CAAC;IACtC,OAAO;MACHM,EAAE,EAAEN,KAAK;MACTO,IAAI,EAAEL,KAAK,IAAI,EAAE;MACjBE,KAAK,EAAEC,IAAI,GAAGA,IAAI,CAACD,KAAK,GAAGA,KAAK;MAChCI,QAAQ,EAAEH,IAAI,GAAGA,IAAI,CAACF,aAAa,GAAGA,aAAa,KAAKM,SAAS,GAAG,IAAI,GAAGN;IAC/E,CAAC;EACL,CAAC,CAAC,CACGO,IAAI,CAAC,CAAC;IAAEN,KAAK,EAAEO;EAAE,CAAC,EAAE;IAAEP,KAAK,EAAEQ;EAAE,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;AACpD;AACA,IAAIC,8BAA8B,IAAAC,+BAAA,GAAG,MAAMD,8BAA8B,CAAC;EACtE,IAAIE,MAAMA,CAACC,KAAK,EAAE;IACd,IAAI,CAACC,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;EAC5B;EACA,IAAInB,MAAMA,CAACmB,KAAK,EAAE;IACd,IAAI,CAACG,OAAO,GAAGH,KAAK;IACpB,IAAI,CAACI,gBAAgB,CAACJ,KAAK,EAAE,IAAI,CAAClB,UAAU,CAAC;EACjD;EACAuB,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACC,aAAa,GAAG,IAAI1C,YAAY,CAAC,CAAC;IACvC,IAAI,CAACwB,IAAI,GAAG,EAAE;IACd,IAAI,CAACG,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACV,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAAC0B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACP,OAAO,GAAG,IAAI7B,eAAe,CAACqB,SAAS,CAAC;IAC7C,IAAI,CAACgB,SAAS,GAAG,IAAInC,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACoC,YAAY,GAAGJ,QAAQ,IAAI,IAAI3B,+BAA+B,CAAC,CAAC;EACzE;EACAgC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACV,OAAO,CAACW,IAAI,CAACrC,SAAS,CAACwB,MAAM,IAAIA,MAAM,GAAG,IAAI,CAACW,YAAY,CAACG,UAAU,CAACd,MAAM,CAAC,GAAG1B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEI,SAAS,CAAC,IAAI,CAACgC,SAAS,CAAC,CAAC,CAACK,SAAS,CAAChC,UAAU,IAAI;MAC9I,IAAI,CAACiC,aAAa,CAACjC,UAAU,CAAC;IAClC,CAAC,CAAC;EACN;EACAkC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACP,SAAS,CAACP,IAAI,CAACT,SAAS,CAAC;IAC9B,IAAI,CAACgB,SAAS,CAACQ,QAAQ,CAAC,CAAC;EAC7B;EACAC,OAAOA,CAACC,MAAM,EAAE;IACZ,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,SAAS,CAAC,CAAC;IAC5B;IACA,MAAMtB,MAAM,GAAG,IAAI,CAACE,OAAO,CAACD,KAAK;IACjC,IAAID,MAAM,EAAE;MACR,MAAMuB,YAAY,GAAG,IAAIC,GAAG,CAACJ,MAAM,CAAC;MACpC,MAAM9B,IAAI,GAAG,IAAI,CAACmB,aAAa,CAACgB,MAAM,CAAC,CAACC,MAAM,EAAE;QAAEnC,EAAE;QAAEF;MAAM,CAAC,KAAAsC,aAAA,CAAAA,aAAA,KACtDD,MAAM;QACT,CAACnC,EAAE,GAAG;UACFH,aAAa,EAAEmC,YAAY,CAACK,GAAG,CAACrC,EAAE,CAAC;UACnCF;QACJ;MAAC,EACH,EAAE,CAAC,CAAC,CAAC;MACP,IAAI,CAACsB,YAAY,CAACkB,WAAW,CAAC7B,MAAM,EAAEV,IAAI,CAAC,CAACuB,IAAI,CAACpC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACsC,SAAS,CAAChC,UAAU,IAAI;QAC9E,IAAI,CAACiC,aAAa,CAACjC,UAAU,CAAC;MAClC,CAAC,CAAC;IACN;EACJ;EACA+C,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACT,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,SAAS,CAAC,CAAC;IAC5B;EACJ;EACAN,aAAaA,CAACf,KAAK,EAAE;IACjB,IAAI,CAAClB,UAAU,GAAGkB,KAAK;IACvB,IAAI,CAACI,gBAAgB,CAAC,IAAI,CAACD,OAAO,EAAEH,KAAK,CAAC;IAC1C,IAAI,CAAC8B,IAAI,CAAC,CAAC;EACf;EACA1B,gBAAgBA,CAACvB,MAAM,EAAEmB,KAAK,EAAE;IAC5B,IAAI,CAACQ,aAAa,GAAG5B,kBAAkB,CAACC,MAAM,EAAEmB,KAAK,CAAC;IACtD,IAAI,CAACX,IAAI,GAAG,IAAI,CAACmB,aAAa,CAACvB,GAAG,CAAC,CAAC;MAAEK,EAAE;MAAEC;IAAK,CAAC,MAAM;MAAED,EAAE;MAAEC;IAAK,CAAC,CAAC,CAAC;IACpE,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACgB,aAAa,CAC7BzB,MAAM,CAAC,CAAC;MAAES;IAAS,CAAC,KAAKA,QAAQ,CAAC,CAClCP,GAAG,CAAC,CAAC;MAAEK;IAAG,CAAC,KAAKA,EAAE,CAAC;EAC5B;EACAwC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC3B,OAAO,EAAE;MACd,IAAI,CAACI,aAAa,CAACuB,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAACqB,MAAM,CAAC,CAACC,MAAM,EAAE;QAAEzC,KAAK;QAAEG;MAAc,CAAC,EAAEC,KAAK,KAAK;QACrF,MAAMC,IAAI,GAAG,IAAI,CAACP,UAAU,CAACE,KAAK,CAAC;QACnC,OAAA0C,aAAA,CAAAA,aAAA,KACOD,MAAM;UACT,CAACzC,KAAK,GAAG;YACLI,KAAK,EAAEC,IAAI,GAAGA,IAAI,CAACD,KAAK,GAAGA,KAAK;YAChCD,aAAa,EAAEE,IAAI,GAAGA,IAAI,CAACF,aAAa,GAAGA;UAC/C;QAAC;MAET,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX;EACJ;AAUJ,CAAC,EATYW,+BAAA,CAAKiC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEtD,6BAA6B;EAAEuD,UAAU,EAAE,CAAC;IAAED,IAAI,EAAEhE;EAAS,CAAC,EAAE;IAAEgE,IAAI,EAAElE,MAAM;IAAEoE,IAAI,EAAE,CAACxD,6BAA6B;EAAG,CAAC;AAAE,CAAC,CACtI,EACQoB,+BAAA,CAAKqC,cAAc,GAAG;EAC3BpC,MAAM,EAAE,CAAC;IAAEiC,IAAI,EAAEjE;EAAM,CAAC,CAAC;EACzBc,MAAM,EAAE,CAAC;IAAEmD,IAAI,EAAEjE;EAAM,CAAC,CAAC;EACzBwC,aAAa,EAAE,CAAC;IAAEyB,IAAI,EAAE/D;EAAO,CAAC,CAAC;EACjCmD,OAAO,EAAE,CAAC;IAAEY,IAAI,EAAE9D,SAAS;IAAEgE,IAAI,EAAE,CAAC/D,cAAc,EAAE;MAAEiE,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC;AAC5E,CAAC,EAAAtC,+BAAA,CACJ;AACDD,8BAA8B,GAAGpC,UAAU,CAAC,CACxCG,SAAS,CAAC;EACNyE,QAAQ,EAAE,6BAA6B;EACvCC,QAAQ,EAAE5E,oBAAoB;EAC9B6E,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC7E,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEkC,8BAA8B,CAAC;AAClC,SAASA,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}