{"ast": null, "code": "var _InputPasswordComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-password.component.html?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { shuffle } from 'lodash';\nconst CHARS_MAP = {\n  'lowercase': ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'],\n  'uppercase': ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],\n  'symbols': ['!', '#', '$', '%', '&', '\\'', '(', ')', '*', '+', ',', '-', '.', '/', ':', ';', '<', '=', '>', '?', '@', '[', '\\\\', ']', '^', '_', '`', '{', '|', '}', '~'],\n  'digits': ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']\n};\nfunction setArray(array, amount) {\n  let result = [];\n  for (let i = 0; i <= amount - 1; i++) {\n    result.push(array[Math.floor(Math.random() * array.length)]);\n  }\n  return result;\n}\n// Random amount of chars of chosen type. Amount min is 1 (max depends on amount of active types)\nfunction generatePassword(passwordConfig) {\n  const resultArray = [];\n  let passwordCharsCounter = passwordConfig.length; // counts how many characters are left\n  const activePasswordTypes = Object.keys(passwordConfig).filter(el => {\n    const key = el;\n    return key !== 'length' && passwordConfig[key] !== false;\n  });\n  const amount = Math.floor(Math.random() * passwordConfig.length / (activePasswordTypes.length + 1)) + 1;\n  activePasswordTypes.forEach(el => {\n    const processedArray = setArray(CHARS_MAP[el], amount);\n    resultArray.push(...processedArray);\n    passwordCharsCounter -= processedArray.length;\n  });\n  const lowercaseArray = setArray(CHARS_MAP.lowercase, passwordCharsCounter);\n  resultArray.push(...lowercaseArray);\n  return shuffle(resultArray).join('');\n}\nlet InputPasswordComponent = (_InputPasswordComponent = class InputPasswordComponent {\n  constructor() {\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n  }\n  set componentOptions(value) {\n    var _value$validation;\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.required = value === null || value === void 0 ? void 0 : value.required;\n    this.errorMessages = value === null || value === void 0 || (_value$validation = value.validation) === null || _value$validation === void 0 ? void 0 : _value$validation.messages;\n  }\n  generatePassword() {\n    if (this.control) {\n      this.control.setValue(generatePassword({\n        length: 16,\n        uppercase: true,\n        digits: true\n      }));\n    }\n  }\n}, _InputPasswordComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }]\n}, _InputPasswordComponent);\nInputPasswordComponent = __decorate([Component({\n  selector: 'lib-input-password',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], InputPasswordComponent);\nexport { InputPasswordComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Input", "shuffle", "CHARS_MAP", "set<PERSON><PERSON>y", "array", "amount", "result", "i", "push", "Math", "floor", "random", "length", "generatePassword", "passwordConfig", "resultArray", "passwordCharsCounter", "activePasswordTypes", "Object", "keys", "filter", "el", "key", "for<PERSON>ach", "processedArray", "lowercaseArray", "lowercase", "join", "InputPasswordComponent", "_InputPasswordComponent", "constructor", "id", "readonly", "submitted", "componentOptions", "value", "_value$validation", "title", "required", "errorMessages", "validation", "messages", "control", "setValue", "uppercase", "digits", "propDecorators", "type", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-password/input-password.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-password.component.html?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { shuffle } from 'lodash';\nconst CHARS_MAP = {\n    'lowercase': ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r',\n        's', 't', 'u', 'v', 'w', 'x', 'y', 'z'],\n    'uppercase': ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q',\n        'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],\n    'symbols': ['!', '#', '$', '%', '&', '\\'', '(', ')', '*', '+', ',', '-', '.', '/', ':', ';', '<', '=', '>', '?',\n        '@', '[', '\\\\', ']', '^', '_', '`', '{', '|', '}', '~'],\n    'digits': ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],\n};\nfunction setArray(array, amount) {\n    let result = [];\n    for (let i = 0; i <= (amount - 1); i++) {\n        result.push(array[Math.floor(Math.random() * array.length)]);\n    }\n    return result;\n}\n// Random amount of chars of chosen type. Amount min is 1 (max depends on amount of active types)\nfunction generatePassword(passwordConfig) {\n    const resultArray = [];\n    let passwordCharsCounter = passwordConfig.length; // counts how many characters are left\n    const activePasswordTypes = Object.keys(passwordConfig).filter(el => {\n        const key = el;\n        return key !== 'length' && passwordConfig[key] !== false;\n    });\n    const amount = Math.floor(Math.random() * passwordConfig.length / (activePasswordTypes.length + 1)) + 1;\n    activePasswordTypes.forEach(el => {\n        const processedArray = setArray(CHARS_MAP[el], amount);\n        resultArray.push(...processedArray);\n        passwordCharsCounter -= processedArray.length;\n    });\n    const lowercaseArray = setArray(CHARS_MAP.lowercase, passwordCharsCounter);\n    resultArray.push(...lowercaseArray);\n    return shuffle(resultArray).join('');\n}\nlet InputPasswordComponent = class InputPasswordComponent {\n    constructor() {\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n    }\n    set componentOptions(value) {\n        this.title = value?.title;\n        this.required = value?.required;\n        this.errorMessages = value?.validation?.messages;\n    }\n    generatePassword() {\n        if (this.control) {\n            this.control.setValue(generatePassword({\n                length: 16,\n                uppercase: true,\n                digits: true,\n            }));\n        }\n    }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }]\n    }; }\n};\nInputPasswordComponent = __decorate([\n    Component({\n        selector: 'lib-input-password',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], InputPasswordComponent);\nexport { InputPasswordComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,OAAO,QAAQ,QAAQ;AAChC,MAAMC,SAAS,GAAG;EACd,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3C,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC7F,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAChD,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC3G,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3D,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;AAC/D,CAAC;AACD,SAASC,QAAQA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC7B,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAKF,MAAM,GAAG,CAAE,EAAEE,CAAC,EAAE,EAAE;IACpCD,MAAM,CAACE,IAAI,CAACJ,KAAK,CAACK,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGP,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC;EAChE;EACA,OAAON,MAAM;AACjB;AACA;AACA,SAASO,gBAAgBA,CAACC,cAAc,EAAE;EACtC,MAAMC,WAAW,GAAG,EAAE;EACtB,IAAIC,oBAAoB,GAAGF,cAAc,CAACF,MAAM,CAAC,CAAC;EAClD,MAAMK,mBAAmB,GAAGC,MAAM,CAACC,IAAI,CAACL,cAAc,CAAC,CAACM,MAAM,CAACC,EAAE,IAAI;IACjE,MAAMC,GAAG,GAAGD,EAAE;IACd,OAAOC,GAAG,KAAK,QAAQ,IAAIR,cAAc,CAACQ,GAAG,CAAC,KAAK,KAAK;EAC5D,CAAC,CAAC;EACF,MAAMjB,MAAM,GAAGI,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGG,cAAc,CAACF,MAAM,IAAIK,mBAAmB,CAACL,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACvGK,mBAAmB,CAACM,OAAO,CAACF,EAAE,IAAI;IAC9B,MAAMG,cAAc,GAAGrB,QAAQ,CAACD,SAAS,CAACmB,EAAE,CAAC,EAAEhB,MAAM,CAAC;IACtDU,WAAW,CAACP,IAAI,CAAC,GAAGgB,cAAc,CAAC;IACnCR,oBAAoB,IAAIQ,cAAc,CAACZ,MAAM;EACjD,CAAC,CAAC;EACF,MAAMa,cAAc,GAAGtB,QAAQ,CAACD,SAAS,CAACwB,SAAS,EAAEV,oBAAoB,CAAC;EAC1ED,WAAW,CAACP,IAAI,CAAC,GAAGiB,cAAc,CAAC;EACnC,OAAOxB,OAAO,CAACc,WAAW,CAAC,CAACY,IAAI,CAAC,EAAE,CAAC;AACxC;AACA,IAAIC,sBAAsB,IAAAC,uBAAA,GAAG,MAAMD,sBAAsB,CAAC;EACtDE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;EAC1B;EACA,IAAIC,gBAAgBA,CAACC,KAAK,EAAE;IAAA,IAAAC,iBAAA;IACxB,IAAI,CAACC,KAAK,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,KAAK;IACzB,IAAI,CAACC,QAAQ,GAAGH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,QAAQ;IAC/B,IAAI,CAACC,aAAa,GAAGJ,KAAK,aAALA,KAAK,gBAAAC,iBAAA,GAALD,KAAK,CAAEK,UAAU,cAAAJ,iBAAA,uBAAjBA,iBAAA,CAAmBK,QAAQ;EACpD;EACA5B,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC6B,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,QAAQ,CAAC9B,gBAAgB,CAAC;QACnCD,MAAM,EAAE,EAAE;QACVgC,SAAS,EAAE,IAAI;QACfC,MAAM,EAAE;MACZ,CAAC,CAAC,CAAC;IACP;EACJ;AAQJ,CAAC,EAPYhB,uBAAA,CAAKiB,cAAc,GAAG;EAC3BJ,OAAO,EAAE,CAAC;IAAEK,IAAI,EAAE/C;EAAM,CAAC,CAAC;EAC1B+B,EAAE,EAAE,CAAC;IAAEgB,IAAI,EAAE/C;EAAM,CAAC,CAAC;EACrBgC,QAAQ,EAAE,CAAC;IAAEe,IAAI,EAAE/C;EAAM,CAAC,CAAC;EAC3BiC,SAAS,EAAE,CAAC;IAAEc,IAAI,EAAE/C;EAAM,CAAC,CAAC;EAC5BkC,gBAAgB,EAAE,CAAC;IAAEa,IAAI,EAAE/C;EAAM,CAAC;AACtC,CAAC,EAAA6B,uBAAA,CACJ;AACDD,sBAAsB,GAAG/B,UAAU,CAAC,CAChCE,SAAS,CAAC;EACNiD,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAEnD,oBAAoB;EAC9BoD,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEtB,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}