{"ast": null, "code": "var _SwuiTdCurrencyWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./currency.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/currency/currency.widget.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=Lm5vLXdyYXAge3doaXRlLXNwYWNlOiBub3dyYXB9!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/currency/currency.widget.ts\";\nimport { Component, Inject, Optional } from '@angular/core';\nimport { SettingsService } from '../../../services/settings/settings.service';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdCurrencyWidgetComponent = (_SwuiTdCurrencyWidgetComponent = class SwuiTdCurrencyWidgetComponent {\n  constructor({\n    value,\n    schema\n  }, settings) {\n    var _schema$td, _schema$td2;\n    this.settings = settings;\n    this.currencyLocale = window.navigator.language;\n    this.nowrap = false;\n    this.destroyed$ = new Subject();\n    this.value = value;\n    this.delimiter = (_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.delimiter;\n    this.fractionCount = (_schema$td2 = schema.td) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.fractionCount;\n    this.nowrap = schema.td && schema.td.nowrap || false;\n    this.subscribeForSettings();\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  subscribeForSettings() {\n    if (this.settings) {\n      this.settings.appSettings$.pipe(takeUntil(this.destroyed$)).subscribe(({\n        currencyFormat\n      }) => {\n        this.currencyLocale = currencyFormat;\n      });\n    }\n  }\n}, _SwuiTdCurrencyWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}, {\n  type: SettingsService,\n  decorators: [{\n    type: Optional\n  }]\n}], _SwuiTdCurrencyWidgetComponent);\nSwuiTdCurrencyWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-currency-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdCurrencyWidgetComponent);\nexport { SwuiTdCurrencyWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "Optional", "SettingsService", "Subject", "takeUntil", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdCurrencyWidgetComponent", "_SwuiTdCurrencyWidgetComponent", "constructor", "value", "schema", "settings", "_schema$td", "_schema$td2", "currencyLocale", "window", "navigator", "language", "nowrap", "destroyed$", "delimiter", "td", "fractionCount", "subscribeForSettings", "ngOnDestroy", "next", "undefined", "complete", "appSettings$", "pipe", "subscribe", "currencyFormat", "ctorParameters", "type", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/currency/currency.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./currency.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/currency/currency.widget.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=Lm5vLXdyYXAge3doaXRlLXNwYWNlOiBub3dyYXB9!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/currency/currency.widget.ts\";\nimport { Component, Inject, Optional } from '@angular/core';\nimport { SettingsService } from '../../../services/settings/settings.service';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdCurrencyWidgetComponent = class SwuiTdCurrencyWidgetComponent {\n    constructor({ value, schema }, settings) {\n        this.settings = settings;\n        this.currencyLocale = window.navigator.language;\n        this.nowrap = false;\n        this.destroyed$ = new Subject();\n        this.value = value;\n        this.delimiter = schema.td?.delimiter;\n        this.fractionCount = schema.td?.fractionCount;\n        this.nowrap = schema.td && schema.td.nowrap || false;\n        this.subscribeForSettings();\n    }\n    ngOnDestroy() {\n        this.destroyed$.next(undefined);\n        this.destroyed$.complete();\n    }\n    subscribeForSettings() {\n        if (this.settings) {\n            this.settings.appSettings$\n                .pipe(takeUntil(this.destroyed$))\n                .subscribe(({ currencyFormat }) => {\n                this.currencyLocale = currencyFormat;\n            });\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] },\n        { type: SettingsService, decorators: [{ type: Optional }] }\n    ]; }\n};\nSwuiTdCurrencyWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-currency-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdCurrencyWidgetComponent);\nexport { SwuiTdCurrencyWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,oBAAoB,MAAM,0dAA0d;AAC3f,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC3D,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,6BAA6B,IAAAC,8BAAA,GAAG,MAAMD,6BAA6B,CAAC;EACpEE,WAAWA,CAAC;IAAEC,KAAK;IAAEC;EAAO,CAAC,EAAEC,QAAQ,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA;IACrC,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACG,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ;IAC/C,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,UAAU,GAAG,IAAIhB,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACM,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACW,SAAS,IAAAR,UAAA,GAAGF,MAAM,CAACW,EAAE,cAAAT,UAAA,uBAATA,UAAA,CAAWQ,SAAS;IACrC,IAAI,CAACE,aAAa,IAAAT,WAAA,GAAGH,MAAM,CAACW,EAAE,cAAAR,WAAA,uBAATA,WAAA,CAAWS,aAAa;IAC7C,IAAI,CAACJ,MAAM,GAAGR,MAAM,CAACW,EAAE,IAAIX,MAAM,CAACW,EAAE,CAACH,MAAM,IAAI,KAAK;IACpD,IAAI,CAACK,oBAAoB,CAAC,CAAC;EAC/B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,UAAU,CAACM,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACP,UAAU,CAACQ,QAAQ,CAAC,CAAC;EAC9B;EACAJ,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACZ,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACiB,YAAY,CACrBC,IAAI,CAACzB,SAAS,CAAC,IAAI,CAACe,UAAU,CAAC,CAAC,CAChCW,SAAS,CAAC,CAAC;QAAEC;MAAe,CAAC,KAAK;QACnC,IAAI,CAACjB,cAAc,GAAGiB,cAAc;MACxC,CAAC,CAAC;IACN;EACJ;AAKJ,CAAC,EAJYxB,8BAAA,CAAKyB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEP,SAAS;EAAEQ,UAAU,EAAE,CAAC;IAAED,IAAI,EAAEjC,MAAM;IAAEmC,IAAI,EAAE,CAAC9B,uBAAuB;EAAG,CAAC;AAAE,CAAC,EACrF;EAAE4B,IAAI,EAAE/B,eAAe;EAAEgC,UAAU,EAAE,CAAC;IAAED,IAAI,EAAEhC;EAAS,CAAC;AAAE,CAAC,CAC9D,EAAAM,8BAAA,CACJ;AACDD,6BAA6B,GAAGV,UAAU,CAAC,CACvCG,SAAS,CAAC;EACNqC,QAAQ,EAAE,6BAA6B;EACvCC,QAAQ,EAAExC,oBAAoB;EAC9ByC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACzC,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEQ,6BAA6B,CAAC;AACjC,SAASA,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}