{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatInternalFormField2;\nconst _c0 = [\"mat-internal-form-field\", \"\"];\nconst _c1 = [\"*\"];\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\nclass _MatInternalFormField {\n  constructor() {\n    /** Position of the label relative to the content. */\n    _defineProperty(this, \"labelPosition\", void 0);\n  }\n}\n_MatInternalFormField2 = _MatInternalFormField;\n_defineProperty(_MatInternalFormField, \"\\u0275fac\", function _MatInternalFormField2_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatInternalFormField2)();\n});\n_defineProperty(_MatInternalFormField, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatInternalFormField2,\n  selectors: [[\"div\", \"mat-internal-form-field\", \"\"]],\n  hostAttrs: [1, \"mdc-form-field\", \"mat-internal-form-field\"],\n  hostVars: 2,\n  hostBindings: function _MatInternalFormField2_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mdc-form-field--align-end\", ctx.labelPosition === \"before\");\n    }\n  },\n  inputs: {\n    labelPosition: \"labelPosition\"\n  },\n  attrs: _c0,\n  ngContentSelectors: _c1,\n  decls: 1,\n  vars: 0,\n  template: function _MatInternalFormField2_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatInternalFormField, [{\n    type: Component,\n    args: [{\n      selector: 'div[mat-internal-form-field]',\n      template: '<ng-content></ng-content>',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mdc-form-field mat-internal-form-field',\n        '[class.mdc-form-field--align-end]': 'labelPosition === \"before\"'\n      },\n      styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\\n\"]\n    }]\n  }], null, {\n    labelPosition: [{\n      type: Input,\n      args: [{\n        required: true\n      }]\n    }]\n  });\n})();\nexport { _MatInternalFormField as _ };\n//# sourceMappingURL=internal-form-field-grv62mCZ.mjs.map", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "_MatInternalFormField", "constructor", "_defineProperty", "_MatInternalFormField2", "_MatInternalFormField2_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "_MatInternalFormField2_HostBindings", "rf", "ctx", "ɵɵclassProp", "labelPosition", "inputs", "attrs", "_c0", "ngContentSelectors", "_c1", "decls", "vars", "template", "_MatInternalFormField2_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "host", "required", "_"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/internal-form-field-grv62mCZ.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\nclass _MatInternalFormField {\n    /** Position of the label relative to the content. */\n    labelPosition;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _MatInternalFormField, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: _MatInternalFormField, isStandalone: true, selector: \"div[mat-internal-form-field]\", inputs: { labelPosition: \"labelPosition\" }, host: { properties: { \"class.mdc-form-field--align-end\": \"labelPosition === \\\"before\\\"\" }, classAttribute: \"mdc-form-field mat-internal-form-field\" }, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _MatInternalFormField, decorators: [{\n            type: Component,\n            args: [{ selector: 'div[mat-internal-form-field]', template: '<ng-content></ng-content>', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mdc-form-field mat-internal-form-field',\n                        '[class.mdc-form-field--align-end]': 'labelPosition === \"before\"',\n                    }, styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\\n\"] }]\n        }], propDecorators: { labelPosition: [{\n                type: Input,\n                args: [{ required: true }]\n            }] } });\n\nexport { _MatInternalFormField as _ };\n//# sourceMappingURL=internal-form-field-grv62mCZ.mjs.map\n"], "mappings": ";;;;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;;AAE5F;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EAAAC,YAAA;IACxB;IAAAC,eAAA;EAAA;AAIJ;AAACC,sBAAA,GALKH,qBAAqB;AAAAE,eAAA,CAArBF,qBAAqB,wBAAAI,+BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAG4EL,sBAAqB;AAAA;AAAAE,eAAA,CAHtHF,qBAAqB,8BAMsDL,EAAE,CAAAW,iBAAA;EAAAC,IAAA,EAFQP,sBAAqB;EAAAQ,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAE/BlB,EAAE,CAAAoB,WAAA,8BAAAD,GAAA,CAAAE,aAAA,KAF0B,QAAE,CAAC;IAAA;EAAA;EAAAC,MAAA;IAAAD,aAAA;EAAA;EAAAE,KAAA,EAAAC,GAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,gCAAAZ,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAE/BlB,EAAE,CAAA+B,eAAA;MAAF/B,EAAE,CAAAgC,YAAA,EAFiV,CAAC;IAAA;EAAA;EAAAC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAEra;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFpC,EAAE,CAAAqC,iBAAA,CAAQhC,qBAAqB,EAAc,CAAC;IACnHO,IAAI,EAAEX,SAAS;IACfqC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,8BAA8B;MAAEV,QAAQ,EAAE,2BAA2B;MAAEK,aAAa,EAAEhC,iBAAiB,CAACsC,IAAI;MAAEL,eAAe,EAAEhC,uBAAuB,CAACsC,MAAM;MAAEC,IAAI,EAAE;QAC5K,OAAO,EAAE,wCAAwC;QACjD,mCAAmC,EAAE;MACzC,CAAC;MAAET,MAAM,EAAE,CAAC,mmBAAmmB;IAAE,CAAC;EAC9nB,CAAC,CAAC,QAAkB;IAAEZ,aAAa,EAAE,CAAC;MAC9BT,IAAI,EAAER,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAEK,QAAQ,EAAE;MAAK,CAAC;IAC7B,CAAC;EAAE,CAAC;AAAA;AAEhB,SAAStC,qBAAqB,IAAIuC,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}