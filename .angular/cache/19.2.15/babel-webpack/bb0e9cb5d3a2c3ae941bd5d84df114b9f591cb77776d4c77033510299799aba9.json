{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { SwuiBreadcrumbsComponent } from './swui-breadcrumbs.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { MatButtonModule } from '@angular/material/button';\nexport const MODULES = [MatButtonModule, MatIconModule, MatListModule];\nlet SwuiBreadcrumbsModule = class SwuiBreadcrumbsModule {};\nSwuiBreadcrumbsModule = __decorate([NgModule({\n  declarations: [SwuiBreadcrumbsComponent],\n  exports: [SwuiBreadcrumbsComponent],\n  imports: [CommonModule, ...MODULES, RouterModule]\n})], SwuiBreadcrumbsModule);\nexport { SwuiBreadcrumbsModule };", "map": {"version": 3, "names": ["__decorate", "CommonModule", "NgModule", "RouterModule", "SwuiBreadcrumbsComponent", "MatIconModule", "MatListModule", "MatButtonModule", "MODULES", "SwuiBreadcrumbsModule", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-breadcrumbs/swui-breadcrumbs.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { SwuiBreadcrumbsComponent } from './swui-breadcrumbs.component';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { MatButtonModule } from '@angular/material/button';\nexport const MODULES = [\n    MatButtonModule,\n    MatIconModule,\n    MatListModule,\n];\nlet SwuiBreadcrumbsModule = class SwuiBreadcrumbsModule {\n};\nSwuiBreadcrumbsModule = __decorate([\n    NgModule({\n        declarations: [\n            SwuiBreadcrumbsComponent,\n        ],\n        exports: [\n            SwuiBreadcrumbsComponent,\n        ],\n        imports: [\n            CommonModule,\n            ...MODULES,\n            RouterModule,\n        ]\n    })\n], SwuiBreadcrumbsModule);\nexport { SwuiBreadcrumbsModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,OAAO,GAAG,CACnBD,eAAe,EACfF,aAAa,EACbC,aAAa,CAChB;AACD,IAAIG,qBAAqB,GAAG,MAAMA,qBAAqB,CAAC,EACvD;AACDA,qBAAqB,GAAGT,UAAU,CAAC,CAC/BE,QAAQ,CAAC;EACLQ,YAAY,EAAE,CACVN,wBAAwB,CAC3B;EACDO,OAAO,EAAE,CACLP,wBAAwB,CAC3B;EACDQ,OAAO,EAAE,CACLX,YAAY,EACZ,GAAGO,OAAO,EACVL,YAAY;AAEpB,CAAC,CAAC,CACL,EAAEM,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}