{"ast": null, "code": "var _ActionConfirmDialogComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./action-confirm-dialog.component.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nlet ActionConfirmDialogComponent = (_ActionConfirmDialogComponent = class ActionConfirmDialogComponent {\n  constructor(data, dialogRef) {\n    this.data = data;\n    this.dialogRef = dialogRef;\n  }\n  ngOnInit() {}\n  doConfirm() {\n    this.dialogRef.close({\n      confirmed: true\n    });\n  }\n}, _ActionConfirmDialogComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [MAT_DIALOG_DATA]\n  }]\n}, {\n  type: MatDialogRef\n}], _ActionConfirmDialogComponent);\nActionConfirmDialogComponent = __decorate([Component({\n  selector: 'lib-swui-action-confirm-dialog',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], ActionConfirmDialogComponent);\nexport { ActionConfirmDialogComponent };", "map": {"version": 3, "names": ["Component", "Inject", "MAT_DIALOG_DATA", "MatDialogRef", "ActionConfirmDialogComponent", "_ActionConfirmDialogComponent", "constructor", "data", "dialogRef", "ngOnInit", "doConfirm", "close", "confirmed", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-page-panel/action-confirm-dialog/action-confirm-dialog.component.ts"], "sourcesContent": ["import { Component, Inject, OnInit } from '@angular/core';\nimport { PanelAction } from '../swui-page-panel.component';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\n\nexport interface ConfirmDialogResult {\n  confirmed: boolean;\n}\n\nexport interface ConfirmDialogData {\n  title?: string;\n  action: PanelAction;\n  closeButtonText?: string;\n  confirmButtonText: string;\n}\n\n@Component({\n    selector: 'lib-swui-action-confirm-dialog',\n    templateUrl: 'action-confirm-dialog.component.html',\n    standalone: false\n})\nexport class ActionConfirmDialogComponent implements OnInit {\n  constructor(\n    @Inject(MAT_DIALOG_DATA) public data: ConfirmDialogData,\n    public dialogRef: MatDialogRef<ActionConfirmDialogComponent>\n  ) {\n  }\n\n  ngOnInit() {\n  }\n\n  doConfirm() {\n    this.dialogRef.close({ confirmed: true });\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAgB,eAAe;AAEzD,SAASC,eAAe,EAAEC,YAAY,QAAQ,0BAA0B;AAkBjE,IAAMC,4BAA4B,IAAAC,6BAAA,GAAlC,MAAMD,4BAA4B;EACvCE,YACkCC,IAAuB,EAChDC,SAAqD;IAD5B,KAAAD,IAAI,GAAJA,IAAI;IAC7B,KAAAC,SAAS,GAATA,SAAS;EAElB;EAEAC,QAAQA,CAAA,GACR;EAEAC,SAASA,CAAA;IACP,IAAI,CAACF,SAAS,CAACG,KAAK,CAAC;MAAEC,SAAS,EAAE;IAAI,CAAE,CAAC;EAC3C;;;;UAVGX,MAAM;IAAAY,IAAA,GAACX,eAAe;EAAA;AAAA,G;;;AAFdE,4BAA4B,GAAAU,UAAA,EALxCd,SAAS,CAAC;EACPe,QAAQ,EAAE,gCAAgC;EAC1CC,QAAA,EAAAC,oBAAmD;EACnDC,UAAU,EAAE;CACf,CAAC,C,EACWd,4BAA4B,CAaxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}