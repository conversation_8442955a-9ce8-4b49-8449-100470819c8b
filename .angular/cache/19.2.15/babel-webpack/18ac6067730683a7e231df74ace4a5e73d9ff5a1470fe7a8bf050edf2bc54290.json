{"ast": null, "code": "var _DocsTypographyComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs-typography.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../docs.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsTypographyComponent = (_DocsTypographyComponent = class DocsTypographyComponent {\n  constructor() {}\n  ngOnInit() {}\n}, _DocsTypographyComponent.ctorParameters = () => [], _DocsTypographyComponent);\nDocsTypographyComponent = __decorate([Component({\n  selector: 'lib-docs-typography',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DocsTypographyComponent);\nexport { DocsTypographyComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "DocsTypographyComponent", "_DocsTypographyComponent", "constructor", "ngOnInit", "ctorParameters", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/styles/docs/docs-typography/docs-typography.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs-typography.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../docs.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsTypographyComponent = class DocsTypographyComponent {\n    constructor() { }\n    ngOnInit() {\n    }\n    static { this.ctorParameters = () => []; }\n};\nDocsTypographyComponent = __decorate([\n    Component({\n        selector: 'lib-docs-typography',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], DocsTypographyComponent);\nexport { DocsTypographyComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,SAASC,SAAS,QAAQ,eAAe;AACzC,IAAIC,uBAAuB,IAAAC,wBAAA,GAAG,MAAMD,uBAAuB,CAAC;EACxDE,WAAWA,CAAA,EAAG,CAAE;EAChBC,QAAQA,CAAA,EAAG,CACX;AAEJ,CAAC,EADYF,wBAAA,CAAKG,cAAc,GAAG,MAAM,EAAE,EAAAH,wBAAA,CAC1C;AACDD,uBAAuB,GAAGJ,UAAU,CAAC,CACjCG,SAAS,CAAC;EACNM,QAAQ,EAAE,qBAAqB;EAC/BC,QAAQ,EAAET,oBAAoB;EAC9BU,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACV,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEE,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}