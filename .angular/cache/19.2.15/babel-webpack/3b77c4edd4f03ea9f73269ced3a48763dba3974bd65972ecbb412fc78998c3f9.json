{"ast": null, "code": "export class SwuiGridDataService {}", "map": {"version": 3, "names": ["SwuiGridDataService"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/services/grid-data.service.ts"], "sourcesContent": ["export class SwuiGridDataService {\n}\n"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}