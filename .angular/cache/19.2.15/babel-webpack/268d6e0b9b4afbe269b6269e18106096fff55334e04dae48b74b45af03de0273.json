{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HubSelectorComponent } from './hub-selector.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\nlet HubSelectorModule = class HubSelectorModule {};\nHubSelectorModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, MatRippleModule, MatMenuModule, MatIconModule],\n  declarations: [HubSelectorComponent],\n  exports: [HubSelectorComponent]\n})], HubSelectorModule);\nexport { HubSelectorModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "HubSelectorComponent", "TranslateModule", "MatMenuModule", "MatIconModule", "MatRippleModule", "HubSelectorModule", "__decorate", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/hub-selector/hub-selector.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HubSelectorComponent } from './hub-selector.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n    MatRippleModule,\n    MatMenuModule,\n    MatIconModule,\n  ],\n  declarations: [HubSelectorComponent],\n  exports: [HubSelectorComponent],\n})\nexport class HubSelectorModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AAajD,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB,GAC7B;AADYA,iBAAiB,GAAAC,UAAA,EAX7BR,QAAQ,CAAC;EACRS,OAAO,EAAE,CACPR,YAAY,EACZE,eAAe,EACfG,eAAe,EACfF,aAAa,EACbC,aAAa,CACd;EACDK,YAAY,EAAE,CAACR,oBAAoB,CAAC;EACpCS,OAAO,EAAE,CAACT,oBAAoB;CAC/B,CAAC,C,EACWK,iBAAiB,CAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}