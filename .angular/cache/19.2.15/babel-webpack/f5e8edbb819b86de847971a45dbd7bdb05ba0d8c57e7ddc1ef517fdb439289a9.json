{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatRecycleRows, _MatTable, _MatCellDef, _MatHeaderCellDef, _MatFooterCellDef, _MatColumnDef, _<PERSON><PERSON>eaderCell, _<PERSON>Footer<PERSON>ell, _Mat<PERSON>ell, _MatHeaderRowDef, _MatFooterRowDef, _MatRowDef, _<PERSON>HeaderRow, _<PERSON><PERSON>ooter<PERSON>ow, _MatRow, _MatNoDataRow, _MatTextColumn, _MatTableModule;\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]], \"*\"];\nconst _c1 = [\"caption\", \"colgroup, col\", \"*\"];\nfunction _MatTable_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction _MatTable_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 0);\n    i0.ɵɵelementContainer(1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"tbody\", 2);\n    i0.ɵɵelementContainer(3, 3)(4, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"tfoot\", 0);\n    i0.ɵɵelementContainer(6, 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _MatTable_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 1)(1, 3)(2, 4)(3, 5);\n  }\n}\nfunction _MatTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction _MatTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dataAccessor(data_r2, ctx_r0.name), \" \");\n  }\n}\nimport * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, booleanAttribute, NgModule } from '@angular/core';\nimport { CdkTable, CDK_TABLE, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, STICKY_POSITIONING_LISTENER, HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet, CdkCellDef, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkHeaderCell, CdkFooterCell, CdkCell, CdkHeaderRowDef, CdkFooterRowDef, CdkRowDef, CdkHeaderRow, CdkCellOutlet, CdkFooterRow, CdkRow, CdkNoDataRow, CdkTextColumn, CdkTableModule } from '@angular/cdk/table';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, _DisposeViewRepeaterStrategy, DataSource } from '@angular/cdk/collections';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { BehaviorSubject, Subject, merge, of, combineLatest } from 'rxjs';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { map } from 'rxjs/operators';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass MatRecycleRows {}\n_MatRecycleRows = MatRecycleRows;\n_defineProperty(MatRecycleRows, \"\\u0275fac\", function _MatRecycleRows_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatRecycleRows)();\n});\n_defineProperty(MatRecycleRows, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatRecycleRows,\n  selectors: [[\"mat-table\", \"recycleRows\", \"\"], [\"table\", \"mat-table\", \"\", \"recycleRows\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: _VIEW_REPEATER_STRATEGY,\n    useClass: _RecycleViewRepeaterStrategy\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRecycleRows, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-table[recycleRows], table[mat-table][recycleRows]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], null, null);\n})();\nclass MatTable extends CdkTable {\n  constructor(...args) {\n    super(...args);\n    /** Overrides the sticky CSS class set by the `CdkTable`. */\n    _defineProperty(this, \"stickyCssClass\", 'mat-mdc-table-sticky');\n    /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n    _defineProperty(this, \"needsPositionStickyOnElement\", false);\n  }\n}\n_MatTable = MatTable;\n_defineProperty(MatTable, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatTable_BaseFactory;\n  return function _MatTable_Factory(__ngFactoryType__) {\n    return (ɵ_MatTable_BaseFactory || (ɵ_MatTable_BaseFactory = i0.ɵɵgetInheritedFactory(_MatTable)))(__ngFactoryType__ || _MatTable);\n  };\n})());\n_defineProperty(MatTable, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatTable,\n  selectors: [[\"mat-table\"], [\"table\", \"mat-table\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-table\", \"mdc-data-table__table\"],\n  hostVars: 2,\n  hostBindings: function _MatTable_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mdc-table-fixed-layout\", ctx.fixedLayout);\n    }\n  },\n  exportAs: [\"matTable\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkTable,\n    useExisting: _MatTable\n  }, {\n    provide: CDK_TABLE,\n    useExisting: _MatTable\n  }, {\n    provide: _COALESCED_STYLE_SCHEDULER,\n    useClass: _CoalescedStyleScheduler\n  },\n  // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n  //  is only included in the build if used.\n  {\n    provide: _VIEW_REPEATER_STRATEGY,\n    useClass: _DisposeViewRepeaterStrategy\n  },\n  // Prevent nested tables from seeing this table's StickyPositioningListener.\n  {\n    provide: STICKY_POSITIONING_LISTENER,\n    useValue: null\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 5,\n  vars: 2,\n  consts: [[\"role\", \"rowgroup\"], [\"headerRowOutlet\", \"\"], [\"role\", \"rowgroup\", 1, \"mdc-data-table__content\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n  template: function _MatTable_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵprojection(0);\n      i0.ɵɵprojection(1, 1);\n      i0.ɵɵtemplate(2, _MatTable_Conditional_2_Template, 1, 0)(3, _MatTable_Conditional_3_Template, 7, 0)(4, _MatTable_Conditional_4_Template, 4, 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(ctx._isServer ? 2 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx._isNativeHtmlTable ? 3 : 4);\n    }\n  },\n  dependencies: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n  styles: [\".mat-mdc-table-sticky{position:sticky !important}mat-table{display:block}mat-header-row{min-height:var(--mat-table-header-container-height, 56px)}mat-row{min-height:var(--mat-table-row-item-container-height, 52px)}mat-footer-row{min-height:var(--mat-table-footer-container-height, 52px)}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{min-width:100%;border:0;border-spacing:0;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color, var(--mat-sys-surface))}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell{text-align:right}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-header-headline-font, var(--mat-sys-title-small-font, Roboto, sans-serif));line-height:var(--mat-table-header-headline-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-table-header-headline-size, var(--mat-sys-title-small-size, 14px));font-weight:var(--mat-table-header-headline-weight, var(--mat-sys-title-small-weight, 500))}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-row-item-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-row-item-label-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-row-item-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-footer-supporting-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-footer-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-footer-supporting-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-footer-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-table-footer-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking, var(--mat-sys-title-small-tracking));font-weight:inherit;line-height:inherit;box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mat-mdc-header-cell{text-align:right}.mdc-data-table__row:last-child>.mat-mdc-header-cell{border-bottom:none}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking));line-height:inherit}.mdc-data-table__row:last-child>.mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking))}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTable, [{\n    type: Component,\n    args: [{\n      selector: 'mat-table, table[mat-table]',\n      exportAs: 'matTable',\n      template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody class=\"mdc-data-table__content\" role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `,\n      host: {\n        'class': 'mat-mdc-table mdc-data-table__table',\n        '[class.mdc-table-fixed-layout]': 'fixedLayout'\n      },\n      providers: [{\n        provide: CdkTable,\n        useExisting: MatTable\n      }, {\n        provide: CDK_TABLE,\n        useExisting: MatTable\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n      //  is only included in the build if used.\n      {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n      styles: [\".mat-mdc-table-sticky{position:sticky !important}mat-table{display:block}mat-header-row{min-height:var(--mat-table-header-container-height, 56px)}mat-row{min-height:var(--mat-table-row-item-container-height, 52px)}mat-footer-row{min-height:var(--mat-table-footer-container-height, 52px)}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{min-width:100%;border:0;border-spacing:0;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color, var(--mat-sys-surface))}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell{text-align:right}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-header-headline-font, var(--mat-sys-title-small-font, Roboto, sans-serif));line-height:var(--mat-table-header-headline-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-table-header-headline-size, var(--mat-sys-title-small-size, 14px));font-weight:var(--mat-table-header-headline-weight, var(--mat-sys-title-small-weight, 500))}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-row-item-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-row-item-label-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-row-item-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-footer-supporting-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-footer-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-footer-supporting-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-footer-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-table-footer-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking, var(--mat-sys-title-small-tracking));font-weight:inherit;line-height:inherit;box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mat-mdc-header-cell{text-align:right}.mdc-data-table__row:last-child>.mat-mdc-header-cell{border-bottom:none}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking));line-height:inherit}.mdc-data-table__row:last-child>.mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking))}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass MatCellDef extends CdkCellDef {}\n_MatCellDef = MatCellDef;\n_defineProperty(MatCellDef, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatCellDef_BaseFactory;\n  return function _MatCellDef_Factory(__ngFactoryType__) {\n    return (ɵ_MatCellDef_BaseFactory || (ɵ_MatCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(_MatCellDef)))(__ngFactoryType__ || _MatCellDef);\n  };\n})());\n_defineProperty(MatCellDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCellDef,\n  selectors: [[\"\", \"matCellDef\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkCellDef,\n    useExisting: _MatCellDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matCellDef]',\n      providers: [{\n        provide: CdkCellDef,\n        useExisting: MatCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass MatHeaderCellDef extends CdkHeaderCellDef {}\n_MatHeaderCellDef = MatHeaderCellDef;\n_defineProperty(MatHeaderCellDef, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatHeaderCellDef_BaseFactory;\n  return function _MatHeaderCellDef_Factory(__ngFactoryType__) {\n    return (ɵ_MatHeaderCellDef_BaseFactory || (ɵ_MatHeaderCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(_MatHeaderCellDef)))(__ngFactoryType__ || _MatHeaderCellDef);\n  };\n})());\n_defineProperty(MatHeaderCellDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatHeaderCellDef,\n  selectors: [[\"\", \"matHeaderCellDef\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkHeaderCellDef,\n    useExisting: _MatHeaderCellDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matHeaderCellDef]',\n      providers: [{\n        provide: CdkHeaderCellDef,\n        useExisting: MatHeaderCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass MatFooterCellDef extends CdkFooterCellDef {}\n_MatFooterCellDef = MatFooterCellDef;\n_defineProperty(MatFooterCellDef, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatFooterCellDef_BaseFactory;\n  return function _MatFooterCellDef_Factory(__ngFactoryType__) {\n    return (ɵ_MatFooterCellDef_BaseFactory || (ɵ_MatFooterCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(_MatFooterCellDef)))(__ngFactoryType__ || _MatFooterCellDef);\n  };\n})());\n_defineProperty(MatFooterCellDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatFooterCellDef,\n  selectors: [[\"\", \"matFooterCellDef\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkFooterCellDef,\n    useExisting: _MatFooterCellDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matFooterCellDef]',\n      providers: [{\n        provide: CdkFooterCellDef,\n        useExisting: MatFooterCellDef\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\nclass MatColumnDef extends CdkColumnDef {\n  /** Unique name for this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._setNameInput(name);\n  }\n  /**\n   * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n   * In the future, this will only add \"mat-column-\" and columnCssClassName\n   * will change from type string[] to string.\n   * @docs-private\n   */\n  _updateColumnCssClassName() {\n    super._updateColumnCssClassName();\n    this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`);\n  }\n}\n_MatColumnDef = MatColumnDef;\n_defineProperty(MatColumnDef, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatColumnDef_BaseFactory;\n  return function _MatColumnDef_Factory(__ngFactoryType__) {\n    return (ɵ_MatColumnDef_BaseFactory || (ɵ_MatColumnDef_BaseFactory = i0.ɵɵgetInheritedFactory(_MatColumnDef)))(__ngFactoryType__ || _MatColumnDef);\n  };\n})());\n_defineProperty(MatColumnDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatColumnDef,\n  selectors: [[\"\", \"matColumnDef\", \"\"]],\n  inputs: {\n    name: [0, \"matColumnDef\", \"name\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkColumnDef,\n    useExisting: _MatColumnDef\n  }, {\n    provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n    useExisting: _MatColumnDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatColumnDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matColumnDef]',\n      providers: [{\n        provide: CdkColumnDef,\n        useExisting: MatColumnDef\n      }, {\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: MatColumnDef\n      }]\n    }]\n  }], null, {\n    name: [{\n      type: Input,\n      args: ['matColumnDef']\n    }]\n  });\n})();\n/** Header cell template container that adds the right classes and role. */\nclass MatHeaderCell extends CdkHeaderCell {}\n_MatHeaderCell = MatHeaderCell;\n_defineProperty(MatHeaderCell, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatHeaderCell_BaseFactory;\n  return function _MatHeaderCell_Factory(__ngFactoryType__) {\n    return (ɵ_MatHeaderCell_BaseFactory || (ɵ_MatHeaderCell_BaseFactory = i0.ɵɵgetInheritedFactory(_MatHeaderCell)))(__ngFactoryType__ || _MatHeaderCell);\n  };\n})());\n_defineProperty(MatHeaderCell, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatHeaderCell,\n  selectors: [[\"mat-header-cell\"], [\"th\", \"mat-header-cell\", \"\"]],\n  hostAttrs: [\"role\", \"columnheader\", 1, \"mat-mdc-header-cell\", \"mdc-data-table__header-cell\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-header-cell, th[mat-header-cell]',\n      host: {\n        'class': 'mat-mdc-header-cell mdc-data-table__header-cell',\n        'role': 'columnheader'\n      }\n    }]\n  }], null, null);\n})();\n/** Footer cell template container that adds the right classes and role. */\nclass MatFooterCell extends CdkFooterCell {}\n_MatFooterCell = MatFooterCell;\n_defineProperty(MatFooterCell, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatFooterCell_BaseFactory;\n  return function _MatFooterCell_Factory(__ngFactoryType__) {\n    return (ɵ_MatFooterCell_BaseFactory || (ɵ_MatFooterCell_BaseFactory = i0.ɵɵgetInheritedFactory(_MatFooterCell)))(__ngFactoryType__ || _MatFooterCell);\n  };\n})());\n_defineProperty(MatFooterCell, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatFooterCell,\n  selectors: [[\"mat-footer-cell\"], [\"td\", \"mat-footer-cell\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-footer-cell\", \"mdc-data-table__cell\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-footer-cell, td[mat-footer-cell]',\n      host: {\n        'class': 'mat-mdc-footer-cell mdc-data-table__cell'\n      }\n    }]\n  }], null, null);\n})();\n/** Cell template container that adds the right classes and role. */\nclass MatCell extends CdkCell {}\n_MatCell = MatCell;\n_defineProperty(MatCell, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatCell_BaseFactory;\n  return function _MatCell_Factory(__ngFactoryType__) {\n    return (ɵ_MatCell_BaseFactory || (ɵ_MatCell_BaseFactory = i0.ɵɵgetInheritedFactory(_MatCell)))(__ngFactoryType__ || _MatCell);\n  };\n})());\n_defineProperty(MatCell, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCell,\n  selectors: [[\"mat-cell\"], [\"td\", \"mat-cell\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-cell\", \"mdc-data-table__cell\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-cell, td[mat-cell]',\n      host: {\n        'class': 'mat-mdc-cell mdc-data-table__cell'\n      }\n    }]\n  }], null, null);\n})();\n\n// We can't reuse `CDK_ROW_TEMPLATE` because it's incompatible with local compilation mode.\nconst ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass MatHeaderRowDef extends CdkHeaderRowDef {}\n_MatHeaderRowDef = MatHeaderRowDef;\n_defineProperty(MatHeaderRowDef, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatHeaderRowDef_BaseFactory;\n  return function _MatHeaderRowDef_Factory(__ngFactoryType__) {\n    return (ɵ_MatHeaderRowDef_BaseFactory || (ɵ_MatHeaderRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(_MatHeaderRowDef)))(__ngFactoryType__ || _MatHeaderRowDef);\n  };\n})());\n_defineProperty(MatHeaderRowDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatHeaderRowDef,\n  selectors: [[\"\", \"matHeaderRowDef\", \"\"]],\n  inputs: {\n    columns: [0, \"matHeaderRowDef\", \"columns\"],\n    sticky: [2, \"matHeaderRowDefSticky\", \"sticky\", booleanAttribute]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkHeaderRowDef,\n    useExisting: _MatHeaderRowDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matHeaderRowDef]',\n      providers: [{\n        provide: CdkHeaderRowDef,\n        useExisting: MatHeaderRowDef\n      }],\n      inputs: [{\n        name: 'columns',\n        alias: 'matHeaderRowDef'\n      }, {\n        name: 'sticky',\n        alias: 'matHeaderRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass MatFooterRowDef extends CdkFooterRowDef {}\n_MatFooterRowDef = MatFooterRowDef;\n_defineProperty(MatFooterRowDef, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatFooterRowDef_BaseFactory;\n  return function _MatFooterRowDef_Factory(__ngFactoryType__) {\n    return (ɵ_MatFooterRowDef_BaseFactory || (ɵ_MatFooterRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(_MatFooterRowDef)))(__ngFactoryType__ || _MatFooterRowDef);\n  };\n})());\n_defineProperty(MatFooterRowDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatFooterRowDef,\n  selectors: [[\"\", \"matFooterRowDef\", \"\"]],\n  inputs: {\n    columns: [0, \"matFooterRowDef\", \"columns\"],\n    sticky: [2, \"matFooterRowDefSticky\", \"sticky\", booleanAttribute]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkFooterRowDef,\n    useExisting: _MatFooterRowDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matFooterRowDef]',\n      providers: [{\n        provide: CdkFooterRowDef,\n        useExisting: MatFooterRowDef\n      }],\n      inputs: [{\n        name: 'columns',\n        alias: 'matFooterRowDef'\n      }, {\n        name: 'sticky',\n        alias: 'matFooterRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass MatRowDef extends CdkRowDef {}\n_MatRowDef = MatRowDef;\n_defineProperty(MatRowDef, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatRowDef_BaseFactory;\n  return function _MatRowDef_Factory(__ngFactoryType__) {\n    return (ɵ_MatRowDef_BaseFactory || (ɵ_MatRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(_MatRowDef)))(__ngFactoryType__ || _MatRowDef);\n  };\n})());\n_defineProperty(MatRowDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatRowDef,\n  selectors: [[\"\", \"matRowDef\", \"\"]],\n  inputs: {\n    columns: [0, \"matRowDefColumns\", \"columns\"],\n    when: [0, \"matRowDefWhen\", \"when\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkRowDef,\n    useExisting: _MatRowDef\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matRowDef]',\n      providers: [{\n        provide: CdkRowDef,\n        useExisting: MatRowDef\n      }],\n      inputs: [{\n        name: 'columns',\n        alias: 'matRowDefColumns'\n      }, {\n        name: 'when',\n        alias: 'matRowDefWhen'\n      }]\n    }]\n  }], null, null);\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass MatHeaderRow extends CdkHeaderRow {}\n_MatHeaderRow = MatHeaderRow;\n_defineProperty(MatHeaderRow, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatHeaderRow_BaseFactory;\n  return function _MatHeaderRow_Factory(__ngFactoryType__) {\n    return (ɵ_MatHeaderRow_BaseFactory || (ɵ_MatHeaderRow_BaseFactory = i0.ɵɵgetInheritedFactory(_MatHeaderRow)))(__ngFactoryType__ || _MatHeaderRow);\n  };\n})());\n_defineProperty(MatHeaderRow, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatHeaderRow,\n  selectors: [[\"mat-header-row\"], [\"tr\", \"mat-header-row\", \"\"]],\n  hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-header-row\", \"mdc-data-table__header-row\"],\n  exportAs: [\"matHeaderRow\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkHeaderRow,\n    useExisting: _MatHeaderRow\n  }]), i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkCellOutlet\", \"\"]],\n  template: function _MatHeaderRow_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [CdkCellOutlet],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-header-row, tr[mat-header-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-header-row mdc-data-table__header-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matHeaderRow',\n      providers: [{\n        provide: CdkHeaderRow,\n        useExisting: MatHeaderRow\n      }],\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatFooterRow extends CdkFooterRow {}\n_MatFooterRow = MatFooterRow;\n_defineProperty(MatFooterRow, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatFooterRow_BaseFactory;\n  return function _MatFooterRow_Factory(__ngFactoryType__) {\n    return (ɵ_MatFooterRow_BaseFactory || (ɵ_MatFooterRow_BaseFactory = i0.ɵɵgetInheritedFactory(_MatFooterRow)))(__ngFactoryType__ || _MatFooterRow);\n  };\n})());\n_defineProperty(MatFooterRow, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatFooterRow,\n  selectors: [[\"mat-footer-row\"], [\"tr\", \"mat-footer-row\", \"\"]],\n  hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-footer-row\", \"mdc-data-table__row\"],\n  exportAs: [\"matFooterRow\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkFooterRow,\n    useExisting: _MatFooterRow\n  }]), i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkCellOutlet\", \"\"]],\n  template: function _MatFooterRow_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [CdkCellOutlet],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-footer-row, tr[mat-footer-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-footer-row mdc-data-table__row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matFooterRow',\n      providers: [{\n        provide: CdkFooterRow,\n        useExisting: MatFooterRow\n      }],\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass MatRow extends CdkRow {}\n_MatRow = MatRow;\n_defineProperty(MatRow, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatRow_BaseFactory;\n  return function _MatRow_Factory(__ngFactoryType__) {\n    return (ɵ_MatRow_BaseFactory || (ɵ_MatRow_BaseFactory = i0.ɵɵgetInheritedFactory(_MatRow)))(__ngFactoryType__ || _MatRow);\n  };\n})());\n_defineProperty(MatRow, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatRow,\n  selectors: [[\"mat-row\"], [\"tr\", \"mat-row\", \"\"]],\n  hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-row\", \"mdc-data-table__row\"],\n  exportAs: [\"matRow\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkRow,\n    useExisting: _MatRow\n  }]), i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkCellOutlet\", \"\"]],\n  template: function _MatRow_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [CdkCellOutlet],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-row, tr[mat-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-row mdc-data-table__row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matRow',\n      providers: [{\n        provide: CdkRow,\n        useExisting: MatRow\n      }],\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nclass MatNoDataRow extends CdkNoDataRow {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"_contentClassName\", 'mat-mdc-no-data-row');\n  }\n}\n_MatNoDataRow = MatNoDataRow;\n_defineProperty(MatNoDataRow, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatNoDataRow_BaseFactory;\n  return function _MatNoDataRow_Factory(__ngFactoryType__) {\n    return (ɵ_MatNoDataRow_BaseFactory || (ɵ_MatNoDataRow_BaseFactory = i0.ɵɵgetInheritedFactory(_MatNoDataRow)))(__ngFactoryType__ || _MatNoDataRow);\n  };\n})());\n_defineProperty(MatNoDataRow, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatNoDataRow,\n  selectors: [[\"ng-template\", \"matNoDataRow\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkNoDataRow,\n    useExisting: _MatNoDataRow\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNoDataRow, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matNoDataRow]',\n      providers: [{\n        provide: CdkNoDataRow,\n        useExisting: MatNoDataRow\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass MatTextColumn extends CdkTextColumn {}\n_MatTextColumn = MatTextColumn;\n_defineProperty(MatTextColumn, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatTextColumn_BaseFactory;\n  return function _MatTextColumn_Factory(__ngFactoryType__) {\n    return (ɵ_MatTextColumn_BaseFactory || (ɵ_MatTextColumn_BaseFactory = i0.ɵɵgetInheritedFactory(_MatTextColumn)))(__ngFactoryType__ || _MatTextColumn);\n  };\n})());\n_defineProperty(MatTextColumn, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatTextColumn,\n  selectors: [[\"mat-text-column\"]],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 3,\n  vars: 0,\n  consts: [[\"matColumnDef\", \"\"], [\"mat-header-cell\", \"\", 3, \"text-align\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 3, \"text-align\", 4, \"matCellDef\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"]],\n  template: function _MatTextColumn_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainerStart(0, 0);\n      i0.ɵɵtemplate(1, _MatTextColumn_th_1_Template, 2, 3, \"th\", 1)(2, _MatTextColumn_td_2_Template, 2, 3, \"td\", 2);\n      i0.ɵɵelementContainerEnd();\n    }\n  },\n  dependencies: [MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTextColumn, [{\n    type: Component,\n    args: [{\n      selector: 'mat-text-column',\n      template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      // Change detection is intentionally not set to OnPush. This component's template will be provided\n      // to the table to be inserted into its view. This is problematic when change detection runs since\n      // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n      // mean's the template in the table's view will not have the updated value (and in fact will cause\n      // an ExpressionChangedAfterItHasBeenCheckedError).\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell]\n    }]\n  }], null, null);\n})();\nconst EXPORTED_DECLARATIONS = [\n// Table\nMatTable, MatRecycleRows,\n// Template defs\nMatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatFooterCellDef, MatFooterRowDef,\n// Cell directives\nMatHeaderCell, MatCell, MatFooterCell,\n// Row directives\nMatHeaderRow, MatRow, MatFooterRow, MatNoDataRow, MatTextColumn];\nclass MatTableModule {}\n_MatTableModule = MatTableModule;\n_defineProperty(MatTableModule, \"\\u0275fac\", function _MatTableModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTableModule)();\n});\n_defineProperty(MatTableModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatTableModule,\n  imports: [MatCommonModule, CdkTableModule,\n  // Table\n  MatTable, MatRecycleRows,\n  // Template defs\n  MatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatFooterCellDef, MatFooterRowDef,\n  // Cell directives\n  MatHeaderCell, MatCell, MatFooterCell,\n  // Row directives\n  MatHeaderRow, MatRow, MatFooterRow, MatNoDataRow, MatTextColumn],\n  exports: [MatCommonModule,\n  // Table\n  MatTable, MatRecycleRows,\n  // Template defs\n  MatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatFooterCellDef, MatFooterRowDef,\n  // Cell directives\n  MatHeaderCell, MatCell, MatFooterCell,\n  // Row directives\n  MatHeaderRow, MatRow, MatFooterRow, MatNoDataRow, MatTextColumn]\n}));\n_defineProperty(MatTableModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, CdkTableModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTableModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CdkTableModule, ...EXPORTED_DECLARATIONS],\n      exports: [MatCommonModule, EXPORTED_DECLARATIONS]\n    }]\n  }], null, null);\n})();\n\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nclass MatTableDataSource extends DataSource {\n  /** Array of data that should be rendered by the table, where each object represents one row. */\n  get data() {\n    return this._data.value;\n  }\n  set data(data) {\n    data = Array.isArray(data) ? data : [];\n    this._data.next(data);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(data);\n    }\n  }\n  /**\n   * Filter term that should be used to filter out objects from the data array. To override how\n   * data objects match to this filter string, provide a custom function for filterPredicate.\n   */\n  get filter() {\n    return this._filter.value;\n  }\n  set filter(filter) {\n    this._filter.next(filter);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(this.data);\n    }\n  }\n  /**\n   * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n   * emitted by the MatSort will trigger an update to the table's rendered data.\n   */\n  get sort() {\n    return this._sort;\n  }\n  set sort(sort) {\n    this._sort = sort;\n    this._updateChangeSubscription();\n  }\n  /**\n   * Instance of the paginator component used by the table to control what page of the data is\n   * displayed. Page changes emitted by the paginator will trigger an update to the\n   * table's rendered data.\n   *\n   * Note that the data source uses the paginator's properties to calculate which page of data\n   * should be displayed. If the paginator receives its properties as template inputs,\n   * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n   * initialized before assigning it to this data source.\n   */\n  get paginator() {\n    return this._paginator;\n  }\n  set paginator(paginator) {\n    this._paginator = paginator;\n    this._updateChangeSubscription();\n  }\n  constructor(initialData = []) {\n    super();\n    /** Stream that emits when a new data array is set on the data source. */\n    _defineProperty(this, \"_data\", void 0);\n    /** Stream emitting render data to the table (depends on ordered data changes). */\n    _defineProperty(this, \"_renderData\", new BehaviorSubject([]));\n    /** Stream that emits when a new filter string is set on the data source. */\n    _defineProperty(this, \"_filter\", new BehaviorSubject(''));\n    /** Used to react to internal changes of the paginator that are made by the data source itself. */\n    _defineProperty(this, \"_internalPageChanges\", new Subject());\n    /**\n     * Subscription to the changes that should trigger an update to the table's rendered rows, such\n     * as filtering, sorting, pagination, or base data changes.\n     */\n    _defineProperty(this, \"_renderChangesSubscription\", null);\n    /**\n     * The filtered set of data that has been matched by the filter string, or all the data if there\n     * is no filter. Useful for knowing the set of data the table represents.\n     * For example, a 'selectAll()' function would likely want to select the set of filtered data\n     * shown to the user rather than all the data.\n     */\n    _defineProperty(this, \"filteredData\", void 0);\n    _defineProperty(this, \"_sort\", void 0);\n    _defineProperty(this, \"_paginator\", void 0);\n    /**\n     * Data accessor function that is used for accessing data properties for sorting through\n     * the default sortData function.\n     * This default function assumes that the sort header IDs (which defaults to the column name)\n     * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n     * May be set to a custom function for different behavior.\n     * @param data Data object that is being accessed.\n     * @param sortHeaderId The name of the column that represents the data.\n     */\n    _defineProperty(this, \"sortingDataAccessor\", (data, sortHeaderId) => {\n      const value = data[sortHeaderId];\n      if (_isNumberValue(value)) {\n        const numberValue = Number(value);\n        // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we leave them as strings.\n        // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER\n        return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n      }\n      return value;\n    });\n    /**\n     * Gets a sorted copy of the data array based on the state of the MatSort. Called\n     * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n     * By default, the function retrieves the active sort and its direction and compares data\n     * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n     * of data ordering.\n     * @param data The array of data that should be sorted.\n     * @param sort The connected MatSort that holds the current sort state.\n     */\n    _defineProperty(this, \"sortData\", (data, sort) => {\n      const active = sort.active;\n      const direction = sort.direction;\n      if (!active || direction == '') {\n        return data;\n      }\n      return data.sort((a, b) => {\n        let valueA = this.sortingDataAccessor(a, active);\n        let valueB = this.sortingDataAccessor(b, active);\n        // If there are data in the column that can be converted to a number,\n        // it must be ensured that the rest of the data\n        // is of the same type so as not to order incorrectly.\n        const valueAType = typeof valueA;\n        const valueBType = typeof valueB;\n        if (valueAType !== valueBType) {\n          if (valueAType === 'number') {\n            valueA += '';\n          }\n          if (valueBType === 'number') {\n            valueB += '';\n          }\n        }\n        // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n        // one value exists while the other doesn't. In this case, existing value should come last.\n        // This avoids inconsistent results when comparing values to undefined/null.\n        // If neither value exists, return 0 (equal).\n        let comparatorResult = 0;\n        if (valueA != null && valueB != null) {\n          // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n          if (valueA > valueB) {\n            comparatorResult = 1;\n          } else if (valueA < valueB) {\n            comparatorResult = -1;\n          }\n        } else if (valueA != null) {\n          comparatorResult = 1;\n        } else if (valueB != null) {\n          comparatorResult = -1;\n        }\n        return comparatorResult * (direction == 'asc' ? 1 : -1);\n      });\n    });\n    /**\n     * Checks if a data object matches the data source's filter string. By default, each data object\n     * is converted to a string of its properties and returns true if the filter has\n     * at least one occurrence in that string. By default, the filter string has its whitespace\n     * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n     * filter matching.\n     * @param data Data object used to check against the filter.\n     * @param filter Filter string that has been set on the data source.\n     * @returns Whether the filter matches against the data\n     */\n    _defineProperty(this, \"filterPredicate\", (data, filter) => {\n      // Transform the filter by converting it to lowercase and removing whitespace.\n      const transformedFilter = filter.trim().toLowerCase();\n      // Loops over the values in the array and returns true if any of them match the filter string\n      return Object.values(data).some(value => `${value}`.toLowerCase().includes(transformedFilter));\n    });\n    this._data = new BehaviorSubject(initialData);\n    this._updateChangeSubscription();\n  }\n  /**\n   * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n   * changes occur, process the current state of the filter, sort, and pagination along with\n   * the provided base data and send it to the table for rendering.\n   */\n  _updateChangeSubscription() {\n    var _this$_renderChangesS;\n    // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n    // The events should emit whenever the component emits a change or initializes, or if no\n    // component is provided, a stream with just a null event should be provided.\n    // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n    // pipeline can progress to the next step. Note that the value from these streams are not used,\n    // they purely act as a signal to progress in the pipeline.\n    const sortChange = this._sort ? merge(this._sort.sortChange, this._sort.initialized) : of(null);\n    const pageChange = this._paginator ? merge(this._paginator.page, this._internalPageChanges, this._paginator.initialized) : of(null);\n    const dataStream = this._data;\n    // Watch for base data or filter changes to provide a filtered set of data.\n    const filteredData = combineLatest([dataStream, this._filter]).pipe(map(([data]) => this._filterData(data)));\n    // Watch for filtered data or sort changes to provide an ordered set of data.\n    const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n    // Watch for ordered data or page changes to provide a paged set of data.\n    const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n    // Watched for paged data changes and send the result to the table to render.\n    (_this$_renderChangesS = this._renderChangesSubscription) === null || _this$_renderChangesS === void 0 || _this$_renderChangesS.unsubscribe();\n    this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n  }\n  /**\n   * Returns a filtered data array where each filter object contains the filter string within\n   * the result of the filterPredicate function. If no filter is set, returns the data array\n   * as provided.\n   */\n  _filterData(data) {\n    // If there is a filter string, filter out data that does not contain it.\n    // Each data object is converted to a string using the function defined by filterPredicate.\n    // May be overridden for customization.\n    this.filteredData = this.filter == null || this.filter === '' ? data : data.filter(obj => this.filterPredicate(obj, this.filter));\n    if (this.paginator) {\n      this._updatePaginator(this.filteredData.length);\n    }\n    return this.filteredData;\n  }\n  /**\n   * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n   * data array as provided. Uses the default data accessor for data lookup, unless a\n   * sortDataAccessor function is defined.\n   */\n  _orderData(data) {\n    // If there is no active sort or direction, return the data without trying to sort.\n    if (!this.sort) {\n      return data;\n    }\n    return this.sortData(data.slice(), this.sort);\n  }\n  /**\n   * Returns a paged slice of the provided data array according to the provided paginator's page\n   * index and length. If there is no paginator provided, returns the data array as provided.\n   */\n  _pageData(data) {\n    if (!this.paginator) {\n      return data;\n    }\n    const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n    return data.slice(startIndex, startIndex + this.paginator.pageSize);\n  }\n  /**\n   * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n   * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n   * guard against making property changes within a round of change detection.\n   */\n  _updatePaginator(filteredDataLength) {\n    Promise.resolve().then(() => {\n      const paginator = this.paginator;\n      if (!paginator) {\n        return;\n      }\n      paginator.length = filteredDataLength;\n      // If the page index is set beyond the page, reduce it to the last page.\n      if (paginator.pageIndex > 0) {\n        const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n        const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n        if (newPageIndex !== paginator.pageIndex) {\n          paginator.pageIndex = newPageIndex;\n          // Since the paginator only emits after user-generated changes,\n          // we need our own stream so we know to should re-render the data.\n          this._internalPageChanges.next();\n        }\n      }\n    });\n  }\n  /**\n   * Used by the MatTable. Called when it connects to the data source.\n   * @docs-private\n   */\n  connect() {\n    if (!this._renderChangesSubscription) {\n      this._updateChangeSubscription();\n    }\n    return this._renderData;\n  }\n  /**\n   * Used by the MatTable. Called when it disconnects from the data source.\n   * @docs-private\n   */\n  disconnect() {\n    var _this$_renderChangesS2;\n    (_this$_renderChangesS2 = this._renderChangesSubscription) === null || _this$_renderChangesS2 === void 0 || _this$_renderChangesS2.unsubscribe();\n    this._renderChangesSubscription = null;\n  }\n}\nexport { MatCell, MatCellDef, MatColumnDef, MatFooterCell, MatFooterCellDef, MatFooterRow, MatFooterRowDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatNoDataRow, MatRecycleRows, MatRow, MatRowDef, MatTable, MatTableDataSource, MatTableModule, MatTextColumn };", "map": {"version": 3, "names": ["i0", "ɵɵprojection", "_MatTable_Conditional_3_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainer", "ɵɵelementEnd", "_MatTable_Conditional_4_Template", "_MatTextColumn_th_1_Template", "ɵɵtext", "ctx_r0", "ɵɵnextContext", "ɵɵstyleProp", "justify", "ɵɵadvance", "ɵɵtextInterpolate1", "headerText", "_MatTextColumn_td_2_Template", "data_r2", "$implicit", "dataAccessor", "name", "Directive", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "booleanAttribute", "NgModule", "CdkTable", "CDK_TABLE", "_COALESCED_STYLE_SCHEDULER", "_CoalescedStyleScheduler", "STICKY_POSITIONING_LISTENER", "HeaderRowOutlet", "DataRowOutlet", "NoDataRowOutlet", "FooterRowOutlet", "CdkCellDef", "CdkHeaderCellDef", "CdkFooterCellDef", "CdkColumnDef", "CdkHeaderCell", "CdkFooterCell", "CdkCell", "CdkHeaderRowDef", "CdkFooterRowDef", "CdkRowDef", "CdkHeaderRow", "CdkCellOutlet", "CdkFooterRow", "CdkRow", "CdkNoDataRow", "CdkTextColumn", "CdkTableModule", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "_DisposeViewRepeaterStrategy", "DataSource", "M", "MatCommonModule", "BehaviorSubject", "Subject", "merge", "of", "combineLatest", "_isNumberValue", "map", "MatRecycleRows", "_MatRecycleRows", "_defineProperty", "_MatRecycleRows_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "features", "ɵɵProvidersFeature", "provide", "useClass", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "MatTable", "constructor", "_MatTable", "ɵ_MatTable_BaseFactory", "_MatTable_Factory", "ɵɵgetInheritedFactory", "ɵɵdefineComponent", "hostAttrs", "hostVars", "hostBindings", "_MatTable_HostBindings", "ɵɵclassProp", "fixedLayout", "exportAs", "useExisting", "useValue", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "_c1", "decls", "vars", "consts", "template", "_MatTable_Template", "ɵɵprojectionDef", "_c0", "ɵɵtemplate", "_MatTable_Conditional_2_Template", "ɵɵconditional", "_isServer", "_isNativeHtmlTable", "dependencies", "styles", "encapsulation", "host", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "MatCellDef", "_MatCellDef", "ɵ_MatCellDef_BaseFactory", "_MatCellDef_Factory", "MatHeaderCellDef", "_MatHeaderCellDef", "ɵ_MatHeaderCellDef_BaseFactory", "_MatHeaderCellDef_Factory", "MatFooterCellDef", "_MatFooterCellDef", "ɵ_MatFooterCellDef_BaseFactory", "_MatFooterCellDef_Factory", "MatColumnDef", "_name", "_setNameInput", "_updateColumnCssClassName", "_columnCssClassName", "push", "cssClassFriendlyName", "_MatColumnDef", "ɵ_MatColumnDef_BaseFactory", "_MatColumnDef_Factory", "inputs", "MatHeaderCell", "_Mat<PERSON><PERSON>erCell", "ɵ_MatHeaderCell_BaseFactory", "_MatHeaderCell_Factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵ_MatFooterCell_BaseFactory", "_MatFooterCell_Factory", "Mat<PERSON>ell", "_<PERSON><PERSON><PERSON>", "ɵ_MatCell_BaseFactory", "_MatCell_Factory", "ROW_TEMPLATE", "MatHeaderRowDef", "_MatHeaderRowDef", "ɵ_MatHeaderRowDef_BaseFactory", "_MatHeaderRowDef_Factory", "columns", "sticky", "alias", "transform", "MatFooterRowDef", "_MatFooterRowDef", "ɵ_MatFooterRowDef_BaseFactory", "_MatFooterRowDef_Factory", "MatRowDef", "_MatRowDef", "ɵ_MatRowDef_BaseFactory", "_MatRowDef_Factory", "when", "MatHeaderRow", "_MatHeaderRow", "ɵ_MatHeaderRow_BaseFactory", "_MatHeaderRow_Factory", "_MatHeaderRow_Template", "MatFooterRow", "_Mat<PERSON><PERSON>erRow", "ɵ_MatFooterRow_BaseFactory", "_MatFooterRow_Factory", "_MatFooterRow_Template", "MatRow", "_MatRow", "ɵ_MatRow_BaseFactory", "_MatRow_Factory", "_MatRow_Template", "MatNoDataRow", "_MatNoDataRow", "ɵ_MatNoDataRow_BaseFactory", "_MatNoDataRow_Factory", "MatTextColumn", "_MatTextColumn", "ɵ_MatTextColumn_BaseFactory", "_MatTextColumn_Factory", "_MatTextColumn_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "EXPORTED_DECLARATIONS", "MatTableModule", "_MatTableModule", "_MatTableModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "MAX_SAFE_INTEGER", "MatTableDataSource", "data", "_data", "value", "Array", "isArray", "next", "_renderChangesSubscription", "_filterData", "filter", "_filter", "sort", "_sort", "_updateChangeSubscription", "paginator", "_paginator", "initialData", "sortHeaderId", "numberValue", "Number", "active", "direction", "a", "b", "valueA", "sortingDataAccessor", "valueB", "valueAType", "valueBType", "comparatorResult", "<PERSON><PERSON><PERSON>er", "trim", "toLowerCase", "Object", "values", "some", "includes", "_this$_renderChangesS", "sortChange", "initialized", "pageChange", "page", "_internalPageChanges", "dataStream", "filteredData", "pipe", "orderedData", "_orderData", "paginatedData", "_pageData", "unsubscribe", "subscribe", "_renderData", "obj", "filterPredicate", "_updatePaginator", "length", "sortData", "slice", "startIndex", "pageIndex", "pageSize", "filteredDataLength", "Promise", "resolve", "then", "lastPageIndex", "Math", "ceil", "newPageIndex", "min", "connect", "disconnect", "_this$_renderChangesS2"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/table.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, booleanAttribute, NgModule } from '@angular/core';\nimport { CdkTable, CDK_TABLE, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, STICKY_POSITIONING_LISTENER, HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet, CdkCellDef, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkHeaderCell, CdkFooterCell, CdkCell, CdkHeaderRowDef, CdkFooterRowDef, CdkRowDef, CdkHeaderRow, CdkCellOutlet, CdkFooterRow, CdkRow, CdkNoDataRow, CdkTextColumn, CdkTableModule } from '@angular/cdk/table';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, _DisposeViewRepeaterStrategy, DataSource } from '@angular/cdk/collections';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { BehaviorSubject, Subject, merge, of, combineLatest } from 'rxjs';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { map } from 'rxjs/operators';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass MatRecycleRows {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRecycleRows, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatRecycleRows, isStandalone: true, selector: \"mat-table[recycleRows], table[mat-table][recycleRows]\", providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRecycleRows, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-table[recycleRows], table[mat-table][recycleRows]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }] });\nclass MatTable extends CdkTable {\n    /** Overrides the sticky CSS class set by the `CdkTable`. */\n    stickyCssClass = 'mat-mdc-table-sticky';\n    /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n    needsPositionStickyOnElement = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTable, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatTable, isStandalone: true, selector: \"mat-table, table[mat-table]\", host: { properties: { \"class.mdc-table-fixed-layout\": \"fixedLayout\" }, classAttribute: \"mat-mdc-table mdc-data-table__table\" }, providers: [\n            { provide: CdkTable, useExisting: MatTable },\n            { provide: CDK_TABLE, useExisting: MatTable },\n            { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n            // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n            //  is only included in the build if used.\n            { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n            // Prevent nested tables from seeing this table's StickyPositioningListener.\n            { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n        ], exportAs: [\"matTable\"], usesInheritance: true, ngImport: i0, template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody class=\"mdc-data-table__content\" role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `, isInline: true, styles: [\".mat-mdc-table-sticky{position:sticky !important}mat-table{display:block}mat-header-row{min-height:var(--mat-table-header-container-height, 56px)}mat-row{min-height:var(--mat-table-row-item-container-height, 52px)}mat-footer-row{min-height:var(--mat-table-footer-container-height, 52px)}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{min-width:100%;border:0;border-spacing:0;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color, var(--mat-sys-surface))}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell{text-align:right}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-header-headline-font, var(--mat-sys-title-small-font, Roboto, sans-serif));line-height:var(--mat-table-header-headline-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-table-header-headline-size, var(--mat-sys-title-small-size, 14px));font-weight:var(--mat-table-header-headline-weight, var(--mat-sys-title-small-weight, 500))}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-row-item-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-row-item-label-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-row-item-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-footer-supporting-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-footer-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-footer-supporting-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-footer-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-table-footer-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking, var(--mat-sys-title-small-tracking));font-weight:inherit;line-height:inherit;box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mat-mdc-header-cell{text-align:right}.mdc-data-table__row:last-child>.mat-mdc-header-cell{border-bottom:none}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking));line-height:inherit}.mdc-data-table__row:last-child>.mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking))}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\\n\"], dependencies: [{ kind: \"directive\", type: HeaderRowOutlet, selector: \"[headerRowOutlet]\" }, { kind: \"directive\", type: DataRowOutlet, selector: \"[rowOutlet]\" }, { kind: \"directive\", type: NoDataRowOutlet, selector: \"[noDataRowOutlet]\" }, { kind: \"directive\", type: FooterRowOutlet, selector: \"[footerRowOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTable, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-table, table[mat-table]', exportAs: 'matTable', template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody class=\"mdc-data-table__content\" role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `, host: {\n                        'class': 'mat-mdc-table mdc-data-table__table',\n                        '[class.mdc-table-fixed-layout]': 'fixedLayout',\n                    }, providers: [\n                        { provide: CdkTable, useExisting: MatTable },\n                        { provide: CDK_TABLE, useExisting: MatTable },\n                        { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n                        // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n                        //  is only included in the build if used.\n                        { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n                        // Prevent nested tables from seeing this table's StickyPositioningListener.\n                        { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n                    ], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet], styles: [\".mat-mdc-table-sticky{position:sticky !important}mat-table{display:block}mat-header-row{min-height:var(--mat-table-header-container-height, 56px)}mat-row{min-height:var(--mat-table-row-item-container-height, 52px)}mat-footer-row{min-height:var(--mat-table-footer-container-height, 52px)}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{min-width:100%;border:0;border-spacing:0;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color, var(--mat-sys-surface))}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell{text-align:right}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-header-headline-font, var(--mat-sys-title-small-font, Roboto, sans-serif));line-height:var(--mat-table-header-headline-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-table-header-headline-size, var(--mat-sys-title-small-size, 14px));font-weight:var(--mat-table-header-headline-weight, var(--mat-sys-title-small-weight, 500))}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-row-item-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-row-item-label-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-row-item-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-footer-supporting-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-footer-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-footer-supporting-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-footer-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-table-footer-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking, var(--mat-sys-title-small-tracking));font-weight:inherit;line-height:inherit;box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mat-mdc-header-cell{text-align:right}.mdc-data-table__row:last-child>.mat-mdc-header-cell{border-bottom:none}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking));line-height:inherit}.mdc-data-table__row:last-child>.mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking))}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\\n\"] }]\n        }] });\n\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass MatCellDef extends CdkCellDef {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCellDef, isStandalone: true, selector: \"[matCellDef]\", providers: [{ provide: CdkCellDef, useExisting: MatCellDef }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matCellDef]',\n                    providers: [{ provide: CdkCellDef, useExisting: MatCellDef }],\n                }]\n        }] });\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass MatHeaderCellDef extends CdkHeaderCellDef {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatHeaderCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatHeaderCellDef, isStandalone: true, selector: \"[matHeaderCellDef]\", providers: [{ provide: CdkHeaderCellDef, useExisting: MatHeaderCellDef }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatHeaderCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matHeaderCellDef]',\n                    providers: [{ provide: CdkHeaderCellDef, useExisting: MatHeaderCellDef }],\n                }]\n        }] });\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass MatFooterCellDef extends CdkFooterCellDef {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFooterCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatFooterCellDef, isStandalone: true, selector: \"[matFooterCellDef]\", providers: [{ provide: CdkFooterCellDef, useExisting: MatFooterCellDef }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFooterCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matFooterCellDef]',\n                    providers: [{ provide: CdkFooterCellDef, useExisting: MatFooterCellDef }],\n                }]\n        }] });\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\nclass MatColumnDef extends CdkColumnDef {\n    /** Unique name for this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._setNameInput(name);\n    }\n    /**\n     * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n     * In the future, this will only add \"mat-column-\" and columnCssClassName\n     * will change from type string[] to string.\n     * @docs-private\n     */\n    _updateColumnCssClassName() {\n        super._updateColumnCssClassName();\n        this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatColumnDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatColumnDef, isStandalone: true, selector: \"[matColumnDef]\", inputs: { name: [\"matColumnDef\", \"name\"] }, providers: [\n            { provide: CdkColumnDef, useExisting: MatColumnDef },\n            { provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: MatColumnDef },\n        ], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatColumnDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matColumnDef]',\n                    providers: [\n                        { provide: CdkColumnDef, useExisting: MatColumnDef },\n                        { provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: MatColumnDef },\n                    ],\n                }]\n        }], propDecorators: { name: [{\n                type: Input,\n                args: ['matColumnDef']\n            }] } });\n/** Header cell template container that adds the right classes and role. */\nclass MatHeaderCell extends CdkHeaderCell {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatHeaderCell, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatHeaderCell, isStandalone: true, selector: \"mat-header-cell, th[mat-header-cell]\", host: { attributes: { \"role\": \"columnheader\" }, classAttribute: \"mat-mdc-header-cell mdc-data-table__header-cell\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatHeaderCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-header-cell, th[mat-header-cell]',\n                    host: {\n                        'class': 'mat-mdc-header-cell mdc-data-table__header-cell',\n                        'role': 'columnheader',\n                    },\n                }]\n        }] });\n/** Footer cell template container that adds the right classes and role. */\nclass MatFooterCell extends CdkFooterCell {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFooterCell, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatFooterCell, isStandalone: true, selector: \"mat-footer-cell, td[mat-footer-cell]\", host: { classAttribute: \"mat-mdc-footer-cell mdc-data-table__cell\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFooterCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-footer-cell, td[mat-footer-cell]',\n                    host: {\n                        'class': 'mat-mdc-footer-cell mdc-data-table__cell',\n                    },\n                }]\n        }] });\n/** Cell template container that adds the right classes and role. */\nclass MatCell extends CdkCell {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCell, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCell, isStandalone: true, selector: \"mat-cell, td[mat-cell]\", host: { classAttribute: \"mat-mdc-cell mdc-data-table__cell\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-cell, td[mat-cell]',\n                    host: {\n                        'class': 'mat-mdc-cell mdc-data-table__cell',\n                    },\n                }]\n        }] });\n\n// We can't reuse `CDK_ROW_TEMPLATE` because it's incompatible with local compilation mode.\nconst ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass MatHeaderRowDef extends CdkHeaderRowDef {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatHeaderRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatHeaderRowDef, isStandalone: true, selector: \"[matHeaderRowDef]\", inputs: { columns: [\"matHeaderRowDef\", \"columns\"], sticky: [\"matHeaderRowDefSticky\", \"sticky\", booleanAttribute] }, providers: [{ provide: CdkHeaderRowDef, useExisting: MatHeaderRowDef }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatHeaderRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matHeaderRowDef]',\n                    providers: [{ provide: CdkHeaderRowDef, useExisting: MatHeaderRowDef }],\n                    inputs: [\n                        { name: 'columns', alias: 'matHeaderRowDef' },\n                        { name: 'sticky', alias: 'matHeaderRowDefSticky', transform: booleanAttribute },\n                    ],\n                }]\n        }] });\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass MatFooterRowDef extends CdkFooterRowDef {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFooterRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatFooterRowDef, isStandalone: true, selector: \"[matFooterRowDef]\", inputs: { columns: [\"matFooterRowDef\", \"columns\"], sticky: [\"matFooterRowDefSticky\", \"sticky\", booleanAttribute] }, providers: [{ provide: CdkFooterRowDef, useExisting: MatFooterRowDef }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFooterRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matFooterRowDef]',\n                    providers: [{ provide: CdkFooterRowDef, useExisting: MatFooterRowDef }],\n                    inputs: [\n                        { name: 'columns', alias: 'matFooterRowDef' },\n                        { name: 'sticky', alias: 'matFooterRowDefSticky', transform: booleanAttribute },\n                    ],\n                }]\n        }] });\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass MatRowDef extends CdkRowDef {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatRowDef, isStandalone: true, selector: \"[matRowDef]\", inputs: { columns: [\"matRowDefColumns\", \"columns\"], when: [\"matRowDefWhen\", \"when\"] }, providers: [{ provide: CdkRowDef, useExisting: MatRowDef }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matRowDef]',\n                    providers: [{ provide: CdkRowDef, useExisting: MatRowDef }],\n                    inputs: [\n                        { name: 'columns', alias: 'matRowDefColumns' },\n                        { name: 'when', alias: 'matRowDefWhen' },\n                    ],\n                }]\n        }] });\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass MatHeaderRow extends CdkHeaderRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatHeaderRow, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatHeaderRow, isStandalone: true, selector: \"mat-header-row, tr[mat-header-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-header-row mdc-data-table__header-row\" }, providers: [{ provide: CdkHeaderRow, useExisting: MatHeaderRow }], exportAs: [\"matHeaderRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatHeaderRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-header-row, tr[mat-header-row]',\n                    template: ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-header-row mdc-data-table__header-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matHeaderRow',\n                    providers: [{ provide: CdkHeaderRow, useExisting: MatHeaderRow }],\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatFooterRow extends CdkFooterRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFooterRow, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatFooterRow, isStandalone: true, selector: \"mat-footer-row, tr[mat-footer-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-footer-row mdc-data-table__row\" }, providers: [{ provide: CdkFooterRow, useExisting: MatFooterRow }], exportAs: [\"matFooterRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatFooterRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-footer-row, tr[mat-footer-row]',\n                    template: ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-footer-row mdc-data-table__row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matFooterRow',\n                    providers: [{ provide: CdkFooterRow, useExisting: MatFooterRow }],\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass MatRow extends CdkRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRow, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatRow, isStandalone: true, selector: \"mat-row, tr[mat-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-row mdc-data-table__row\" }, providers: [{ provide: CdkRow, useExisting: MatRow }], exportAs: [\"matRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-row, tr[mat-row]',\n                    template: ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-row mdc-data-table__row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matRow',\n                    providers: [{ provide: CdkRow, useExisting: MatRow }],\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Row that can be used to display a message when no data is shown in the table. */\nclass MatNoDataRow extends CdkNoDataRow {\n    _contentClassName = 'mat-mdc-no-data-row';\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatNoDataRow, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatNoDataRow, isStandalone: true, selector: \"ng-template[matNoDataRow]\", providers: [{ provide: CdkNoDataRow, useExisting: MatNoDataRow }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatNoDataRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matNoDataRow]',\n                    providers: [{ provide: CdkNoDataRow, useExisting: MatNoDataRow }],\n                }]\n        }] });\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass MatTextColumn extends CdkTextColumn {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTextColumn, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTextColumn, isStandalone: true, selector: \"mat-text-column\", usesInheritance: true, ngImport: i0, template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: MatColumnDef, selector: \"[matColumnDef]\", inputs: [\"matColumnDef\"] }, { kind: \"directive\", type: MatHeaderCellDef, selector: \"[matHeaderCellDef]\" }, { kind: \"directive\", type: MatHeaderCell, selector: \"mat-header-cell, th[mat-header-cell]\" }, { kind: \"directive\", type: MatCellDef, selector: \"[matCellDef]\" }, { kind: \"directive\", type: MatCell, selector: \"mat-cell, td[mat-cell]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTextColumn, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-text-column',\n                    template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n                    encapsulation: ViewEncapsulation.None,\n                    // Change detection is intentionally not set to OnPush. This component's template will be provided\n                    // to the table to be inserted into its view. This is problematic when change detection runs since\n                    // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n                    // mean's the template in the table's view will not have the updated value (and in fact will cause\n                    // an ExpressionChangedAfterItHasBeenCheckedError).\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    imports: [MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell],\n                }]\n        }] });\n\nconst EXPORTED_DECLARATIONS = [\n    // Table\n    MatTable,\n    MatRecycleRows,\n    // Template defs\n    MatHeaderCellDef,\n    MatHeaderRowDef,\n    MatColumnDef,\n    MatCellDef,\n    MatRowDef,\n    MatFooterCellDef,\n    MatFooterRowDef,\n    // Cell directives\n    MatHeaderCell,\n    MatCell,\n    MatFooterCell,\n    // Row directives\n    MatHeaderRow,\n    MatRow,\n    MatFooterRow,\n    MatNoDataRow,\n    MatTextColumn,\n];\nclass MatTableModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTableModule, imports: [MatCommonModule, CdkTableModule, \n            // Table\n            MatTable,\n            MatRecycleRows,\n            // Template defs\n            MatHeaderCellDef,\n            MatHeaderRowDef,\n            MatColumnDef,\n            MatCellDef,\n            MatRowDef,\n            MatFooterCellDef,\n            MatFooterRowDef,\n            // Cell directives\n            MatHeaderCell,\n            MatCell,\n            MatFooterCell,\n            // Row directives\n            MatHeaderRow,\n            MatRow,\n            MatFooterRow,\n            MatNoDataRow,\n            MatTextColumn], exports: [MatCommonModule, \n            // Table\n            MatTable,\n            MatRecycleRows,\n            // Template defs\n            MatHeaderCellDef,\n            MatHeaderRowDef,\n            MatColumnDef,\n            MatCellDef,\n            MatRowDef,\n            MatFooterCellDef,\n            MatFooterRowDef,\n            // Cell directives\n            MatHeaderCell,\n            MatCell,\n            MatFooterCell,\n            // Row directives\n            MatHeaderRow,\n            MatRow,\n            MatFooterRow,\n            MatNoDataRow,\n            MatTextColumn] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTableModule, imports: [MatCommonModule, CdkTableModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CdkTableModule, ...EXPORTED_DECLARATIONS],\n                    exports: [MatCommonModule, EXPORTED_DECLARATIONS],\n                }]\n        }] });\n\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nclass MatTableDataSource extends DataSource {\n    /** Stream that emits when a new data array is set on the data source. */\n    _data;\n    /** Stream emitting render data to the table (depends on ordered data changes). */\n    _renderData = new BehaviorSubject([]);\n    /** Stream that emits when a new filter string is set on the data source. */\n    _filter = new BehaviorSubject('');\n    /** Used to react to internal changes of the paginator that are made by the data source itself. */\n    _internalPageChanges = new Subject();\n    /**\n     * Subscription to the changes that should trigger an update to the table's rendered rows, such\n     * as filtering, sorting, pagination, or base data changes.\n     */\n    _renderChangesSubscription = null;\n    /**\n     * The filtered set of data that has been matched by the filter string, or all the data if there\n     * is no filter. Useful for knowing the set of data the table represents.\n     * For example, a 'selectAll()' function would likely want to select the set of filtered data\n     * shown to the user rather than all the data.\n     */\n    filteredData;\n    /** Array of data that should be rendered by the table, where each object represents one row. */\n    get data() {\n        return this._data.value;\n    }\n    set data(data) {\n        data = Array.isArray(data) ? data : [];\n        this._data.next(data);\n        // Normally the `filteredData` is updated by the re-render\n        // subscription, but that won't happen if it's inactive.\n        if (!this._renderChangesSubscription) {\n            this._filterData(data);\n        }\n    }\n    /**\n     * Filter term that should be used to filter out objects from the data array. To override how\n     * data objects match to this filter string, provide a custom function for filterPredicate.\n     */\n    get filter() {\n        return this._filter.value;\n    }\n    set filter(filter) {\n        this._filter.next(filter);\n        // Normally the `filteredData` is updated by the re-render\n        // subscription, but that won't happen if it's inactive.\n        if (!this._renderChangesSubscription) {\n            this._filterData(this.data);\n        }\n    }\n    /**\n     * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n     * emitted by the MatSort will trigger an update to the table's rendered data.\n     */\n    get sort() {\n        return this._sort;\n    }\n    set sort(sort) {\n        this._sort = sort;\n        this._updateChangeSubscription();\n    }\n    _sort;\n    /**\n     * Instance of the paginator component used by the table to control what page of the data is\n     * displayed. Page changes emitted by the paginator will trigger an update to the\n     * table's rendered data.\n     *\n     * Note that the data source uses the paginator's properties to calculate which page of data\n     * should be displayed. If the paginator receives its properties as template inputs,\n     * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n     * initialized before assigning it to this data source.\n     */\n    get paginator() {\n        return this._paginator;\n    }\n    set paginator(paginator) {\n        this._paginator = paginator;\n        this._updateChangeSubscription();\n    }\n    _paginator;\n    /**\n     * Data accessor function that is used for accessing data properties for sorting through\n     * the default sortData function.\n     * This default function assumes that the sort header IDs (which defaults to the column name)\n     * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n     * May be set to a custom function for different behavior.\n     * @param data Data object that is being accessed.\n     * @param sortHeaderId The name of the column that represents the data.\n     */\n    sortingDataAccessor = (data, sortHeaderId) => {\n        const value = data[sortHeaderId];\n        if (_isNumberValue(value)) {\n            const numberValue = Number(value);\n            // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we leave them as strings.\n            // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER\n            return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n        }\n        return value;\n    };\n    /**\n     * Gets a sorted copy of the data array based on the state of the MatSort. Called\n     * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n     * By default, the function retrieves the active sort and its direction and compares data\n     * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n     * of data ordering.\n     * @param data The array of data that should be sorted.\n     * @param sort The connected MatSort that holds the current sort state.\n     */\n    sortData = (data, sort) => {\n        const active = sort.active;\n        const direction = sort.direction;\n        if (!active || direction == '') {\n            return data;\n        }\n        return data.sort((a, b) => {\n            let valueA = this.sortingDataAccessor(a, active);\n            let valueB = this.sortingDataAccessor(b, active);\n            // If there are data in the column that can be converted to a number,\n            // it must be ensured that the rest of the data\n            // is of the same type so as not to order incorrectly.\n            const valueAType = typeof valueA;\n            const valueBType = typeof valueB;\n            if (valueAType !== valueBType) {\n                if (valueAType === 'number') {\n                    valueA += '';\n                }\n                if (valueBType === 'number') {\n                    valueB += '';\n                }\n            }\n            // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n            // one value exists while the other doesn't. In this case, existing value should come last.\n            // This avoids inconsistent results when comparing values to undefined/null.\n            // If neither value exists, return 0 (equal).\n            let comparatorResult = 0;\n            if (valueA != null && valueB != null) {\n                // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n                if (valueA > valueB) {\n                    comparatorResult = 1;\n                }\n                else if (valueA < valueB) {\n                    comparatorResult = -1;\n                }\n            }\n            else if (valueA != null) {\n                comparatorResult = 1;\n            }\n            else if (valueB != null) {\n                comparatorResult = -1;\n            }\n            return comparatorResult * (direction == 'asc' ? 1 : -1);\n        });\n    };\n    /**\n     * Checks if a data object matches the data source's filter string. By default, each data object\n     * is converted to a string of its properties and returns true if the filter has\n     * at least one occurrence in that string. By default, the filter string has its whitespace\n     * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n     * filter matching.\n     * @param data Data object used to check against the filter.\n     * @param filter Filter string that has been set on the data source.\n     * @returns Whether the filter matches against the data\n     */\n    filterPredicate = (data, filter) => {\n        // Transform the filter by converting it to lowercase and removing whitespace.\n        const transformedFilter = filter.trim().toLowerCase();\n        // Loops over the values in the array and returns true if any of them match the filter string\n        return Object.values(data).some(value => `${value}`.toLowerCase().includes(transformedFilter));\n    };\n    constructor(initialData = []) {\n        super();\n        this._data = new BehaviorSubject(initialData);\n        this._updateChangeSubscription();\n    }\n    /**\n     * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n     * changes occur, process the current state of the filter, sort, and pagination along with\n     * the provided base data and send it to the table for rendering.\n     */\n    _updateChangeSubscription() {\n        // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n        // The events should emit whenever the component emits a change or initializes, or if no\n        // component is provided, a stream with just a null event should be provided.\n        // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n        // pipeline can progress to the next step. Note that the value from these streams are not used,\n        // they purely act as a signal to progress in the pipeline.\n        const sortChange = this._sort\n            ? merge(this._sort.sortChange, this._sort.initialized)\n            : of(null);\n        const pageChange = this._paginator\n            ? merge(this._paginator.page, this._internalPageChanges, this._paginator.initialized)\n            : of(null);\n        const dataStream = this._data;\n        // Watch for base data or filter changes to provide a filtered set of data.\n        const filteredData = combineLatest([dataStream, this._filter]).pipe(map(([data]) => this._filterData(data)));\n        // Watch for filtered data or sort changes to provide an ordered set of data.\n        const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n        // Watch for ordered data or page changes to provide a paged set of data.\n        const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n        // Watched for paged data changes and send the result to the table to render.\n        this._renderChangesSubscription?.unsubscribe();\n        this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n    }\n    /**\n     * Returns a filtered data array where each filter object contains the filter string within\n     * the result of the filterPredicate function. If no filter is set, returns the data array\n     * as provided.\n     */\n    _filterData(data) {\n        // If there is a filter string, filter out data that does not contain it.\n        // Each data object is converted to a string using the function defined by filterPredicate.\n        // May be overridden for customization.\n        this.filteredData =\n            this.filter == null || this.filter === ''\n                ? data\n                : data.filter(obj => this.filterPredicate(obj, this.filter));\n        if (this.paginator) {\n            this._updatePaginator(this.filteredData.length);\n        }\n        return this.filteredData;\n    }\n    /**\n     * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n     * data array as provided. Uses the default data accessor for data lookup, unless a\n     * sortDataAccessor function is defined.\n     */\n    _orderData(data) {\n        // If there is no active sort or direction, return the data without trying to sort.\n        if (!this.sort) {\n            return data;\n        }\n        return this.sortData(data.slice(), this.sort);\n    }\n    /**\n     * Returns a paged slice of the provided data array according to the provided paginator's page\n     * index and length. If there is no paginator provided, returns the data array as provided.\n     */\n    _pageData(data) {\n        if (!this.paginator) {\n            return data;\n        }\n        const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n        return data.slice(startIndex, startIndex + this.paginator.pageSize);\n    }\n    /**\n     * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n     * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n     * guard against making property changes within a round of change detection.\n     */\n    _updatePaginator(filteredDataLength) {\n        Promise.resolve().then(() => {\n            const paginator = this.paginator;\n            if (!paginator) {\n                return;\n            }\n            paginator.length = filteredDataLength;\n            // If the page index is set beyond the page, reduce it to the last page.\n            if (paginator.pageIndex > 0) {\n                const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n                const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n                if (newPageIndex !== paginator.pageIndex) {\n                    paginator.pageIndex = newPageIndex;\n                    // Since the paginator only emits after user-generated changes,\n                    // we need our own stream so we know to should re-render the data.\n                    this._internalPageChanges.next();\n                }\n            }\n        });\n    }\n    /**\n     * Used by the MatTable. Called when it connects to the data source.\n     * @docs-private\n     */\n    connect() {\n        if (!this._renderChangesSubscription) {\n            this._updateChangeSubscription();\n        }\n        return this._renderData;\n    }\n    /**\n     * Used by the MatTable. Called when it disconnects from the data source.\n     * @docs-private\n     */\n    disconnect() {\n        this._renderChangesSubscription?.unsubscribe();\n        this._renderChangesSubscription = null;\n    }\n}\n\nexport { MatCell, MatCellDef, MatColumnDef, MatFooterCell, MatFooterCellDef, MatFooterRow, MatFooterRowDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatNoDataRow, MatRecycleRows, MatRow, MatRowDef, MatTable, MatTableDataSource, MatTableModule, MatTextColumn };\n"], "mappings": ";;;;;;IAmBiFA,EAAE,CAAAC,YAAA,KA+BjE,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/B8DH,EAAE,CAAAK,cAAA,cAmCvD,CAAC;IAnCoDL,EAAE,CAAAM,kBAAA,KAoC7C,CAAC;IApC0CN,EAAE,CAAAO,YAAA,CAqCtE,CAAC;IArCmEP,EAAE,CAAAK,cAAA,cAsCvB,CAAC;IAtCoBL,EAAE,CAAAM,kBAAA,KAuCnD,CAAC,KACK,CAAC;IAxC0CN,EAAE,CAAAO,YAAA,CAyCtE,CAAC;IAzCmEP,EAAE,CAAAK,cAAA,cA0CvD,CAAC;IA1CoDL,EAAE,CAAAM,kBAAA,KA2C7C,CAAC;IA3C0CN,EAAE,CAAAO,YAAA,CA4CtE,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5CmEH,EAAE,CAAAM,kBAAA,KA8C/C,CAAC,KACP,CAAC,KACK,CAAC,KACD,CAAC;EAAA;AAAA;AAAA,SAAAG,6BAAAN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjD4CH,EAAE,CAAAK,cAAA,WAiYX,CAAC;IAjYQL,EAAE,CAAAU,MAAA,EAmY9E,CAAC;IAnY2EV,EAAE,CAAAO,YAAA,CAmYzE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAQ,MAAA,GAnYsEX,EAAE,CAAAY,aAAA;IAAFZ,EAAE,CAAAa,WAAA,eAAAF,MAAA,CAAAG,OAiYZ,CAAC;IAjYSd,EAAE,CAAAe,SAAA,CAmY9E,CAAC;IAnY2Ef,EAAE,CAAAgB,kBAAA,MAAAL,MAAA,CAAAM,UAAA,KAmY9E,CAAC;EAAA;AAAA;AAAA,SAAAC,6BAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnY2EH,EAAE,CAAAK,cAAA,WAoYb,CAAC;IApYUL,EAAE,CAAAU,MAAA,EAsY9E,CAAC;IAtY2EV,EAAE,CAAAO,YAAA,CAsYzE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAgB,OAAA,GAAAf,GAAA,CAAAgB,SAAA;IAAA,MAAAT,MAAA,GAtYsEX,EAAE,CAAAY,aAAA;IAAFZ,EAAE,CAAAa,WAAA,eAAAF,MAAA,CAAAG,OAoYd,CAAC;IApYWd,EAAE,CAAAe,SAAA,CAsY9E,CAAC;IAtY2Ef,EAAE,CAAAgB,kBAAA,MAAAL,MAAA,CAAAU,YAAA,CAAAF,OAAA,EAAAR,MAAA,CAAAW,IAAA,MAsY9E,CAAC;EAAA;AAAA;AAzZN,OAAO,KAAKtB,EAAE,MAAM,eAAe;AACnC,SAASuB,SAAS,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,eAAe;AACnI,SAASC,QAAQ,EAAEC,SAAS,EAAEC,0BAA0B,EAAEC,wBAAwB,EAAEC,2BAA2B,EAAEC,eAAe,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAAEC,eAAe,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,QAAQ,oBAAoB;AAC/b,SAASC,uBAAuB,EAAEC,4BAA4B,EAAEC,4BAA4B,EAAEC,UAAU,QAAQ,0BAA0B;AAC1I,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASC,eAAe,EAAEC,OAAO,EAAEC,KAAK,EAAEC,EAAE,EAAEC,aAAa,QAAQ,MAAM;AACzE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;;AAE1B;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;AAGpBC,eAAA,GAHKD,cAAc;AAAAE,eAAA,CAAdF,cAAc,wBAAAG,wBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IACmFJ,eAAc;AAAA;AAAAE,eAAA,CAD/GF,cAAc,8BAI6DrE,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EAFQN,eAAc;EAAAO,SAAA;EAAAC,QAAA,GAExB7E,EAAE,CAAA8E,kBAAA,CAF0H,CAAC;IAAEC,OAAO,EAAEvB,uBAAuB;IAAEwB,QAAQ,EAAEvB;EAA6B,CAAC,CAAC;AAAA;AAE3R;EAAA,QAAAwB,SAAA,oBAAAA,SAAA,KAAiFjF,EAAE,CAAAkF,iBAAA,CAAQb,cAAc,EAAc,CAAC;IAC5GM,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uDAAuD;MACjEC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEvB,uBAAuB;QAAEwB,QAAQ,EAAEvB;MAA6B,CAAC;IAC5F,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAM6B,QAAQ,SAASxD,QAAQ,CAAC;EAAAyD,YAAA,GAAAJ,IAAA;IAAA,SAAAA,IAAA;IAC5B;IAAAZ,eAAA,yBACiB,sBAAsB;IACvC;IAAAA,eAAA,uCAC+B,KAAK;EAAA;AAyCxC;AAACiB,SAAA,GA7CKF,QAAQ;AAAAf,eAAA,CAARe,QAAQ;EAAA,IAAAG,sBAAA;EAAA,gBAAAC,kBAAAjB,iBAAA;IAAA,QAAAgB,sBAAA,KAAAA,sBAAA,GAPmEzF,EAAE,CAAA2F,qBAAA,CAYoBL,SAAQ,IAAAb,iBAAA,IAARa,SAAQ;EAAA;AAAA;AAAAf,eAAA,CALzGe,QAAQ,8BAPmEtF,EAAE,CAAA4F,iBAAA;EAAAjB,IAAA,EAaQW,SAAQ;EAAAV,SAAA;EAAAiB,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,uBAAA7F,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAblBH,EAAE,CAAAiG,WAAA,2BAAA7F,GAAA,CAAA8F,WAae,CAAC;IAAA;EAAA;EAAAC,QAAA;EAAAtB,QAAA,GAblB7E,EAAE,CAAA8E,kBAAA,CAa0N,CACjS;IAAEC,OAAO,EAAEjD,QAAQ;IAAEsE,WAAW,EAAEd;EAAS,CAAC,EAC5C;IAAEP,OAAO,EAAEhD,SAAS;IAAEqE,WAAW,EAAEd;EAAS,CAAC,EAC7C;IAAEP,OAAO,EAAE/C,0BAA0B;IAAEgD,QAAQ,EAAE/C;EAAyB,CAAC;EAC3E;EACA;EACA;IAAE8C,OAAO,EAAEvB,uBAAuB;IAAEwB,QAAQ,EAAEtB;EAA6B,CAAC;EAC5E;EACA;IAAEqB,OAAO,EAAE7C,2BAA2B;IAAEmE,QAAQ,EAAE;EAAK,CAAC,CAC3D,GAtBwErG,EAAE,CAAAsG,0BAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mBAAA1G,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFH,EAAE,CAAA8G,eAAA,CAAAC,GAAA;MAAF/G,EAAE,CAAAC,YAAA,EAuBlD,CAAC;MAvB+CD,EAAE,CAAAC,YAAA,KAwB5C,CAAC;MAxByCD,EAAE,CAAAgH,UAAA,IAAAC,gCAAA,MA8B/D,CAAC,IAAA/G,gCAAA,MAIQ,CAAC,IAAAM,gCAAA,MAWlB,CAAC;IAAA;IAAA,IAAAL,EAAA;MA7CoEH,EAAE,CAAAe,SAAA,EAgC/E,CAAC;MAhC4Ef,EAAE,CAAAkH,aAAA,CAAA9G,GAAA,CAAA+G,SAAA,SAgC/E,CAAC;MAhC4EnH,EAAE,CAAAe,SAAA,CAkD/E,CAAC;MAlD4Ef,EAAE,CAAAkH,aAAA,CAAA9G,GAAA,CAAAgH,kBAAA,QAkD/E,CAAC;IAAA;EAAA;EAAAC,YAAA,GACqrKlF,eAAe,EAA8DC,aAAa,EAAwDC,eAAe,EAA8DC,eAAe;EAAAgF,MAAA;EAAAC,aAAA;AAAA;AAEx6K;EAAA,QAAAtC,SAAA,oBAAAA,SAAA,KArDiFjF,EAAE,CAAAkF,iBAAA,CAqDQI,QAAQ,EAAc,CAAC;IACtGX,IAAI,EAAEnD,SAAS;IACf2D,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEe,QAAQ,EAAE,UAAU;MAAES,QAAQ,EAAE;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEY,IAAI,EAAE;QACa,OAAO,EAAE,qCAAqC;QAC9C,gCAAgC,EAAE;MACtC,CAAC;MAAEnC,SAAS,EAAE,CACV;QAAEN,OAAO,EAAEjD,QAAQ;QAAEsE,WAAW,EAAEd;MAAS,CAAC,EAC5C;QAAEP,OAAO,EAAEhD,SAAS;QAAEqE,WAAW,EAAEd;MAAS,CAAC,EAC7C;QAAEP,OAAO,EAAE/C,0BAA0B;QAAEgD,QAAQ,EAAE/C;MAAyB,CAAC;MAC3E;MACA;MACA;QAAE8C,OAAO,EAAEvB,uBAAuB;QAAEwB,QAAQ,EAAEtB;MAA6B,CAAC;MAC5E;MACA;QAAEqB,OAAO,EAAE7C,2BAA2B;QAAEmE,QAAQ,EAAE;MAAK,CAAC,CAC3D;MAAEkB,aAAa,EAAE9F,iBAAiB,CAACgG,IAAI;MAAEC,eAAe,EAAEhG,uBAAuB,CAACiG,OAAO;MAAEC,OAAO,EAAE,CAACzF,eAAe,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,CAAC;MAAEgF,MAAM,EAAE,CAAC,+mKAA+mK;IAAE,CAAC;EAChzK,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMO,UAAU,SAAStF,UAAU,CAAC;AAGnCuF,WAAA,GAHKD,UAAU;AAAAtD,eAAA,CAAVsD,UAAU;EAAA,IAAAE,wBAAA;EAAA,gBAAAC,oBAAAvD,iBAAA;IAAA,QAAAsD,wBAAA,KAAAA,wBAAA,GAvGiE/H,EAAE,CAAA2F,qBAAA,CAwGoBkC,WAAU,IAAApD,iBAAA,IAAVoD,WAAU;EAAA;AAAA;AAAAtD,eAAA,CAD3GsD,UAAU,8BAvGiE7H,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EAyGQkD,WAAU;EAAAjD,SAAA;EAAAC,QAAA,GAzGpB7E,EAAE,CAAA8E,kBAAA,CAyG6E,CAAC;IAAEC,OAAO,EAAExC,UAAU;IAAE6D,WAAW,EAAEyB;EAAW,CAAC,CAAC,GAzGjI7H,EAAE,CAAAsG,0BAAA;AAAA;AA2GnF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA3GiFjF,EAAE,CAAAkF,iBAAA,CA2GQ2C,UAAU,EAAc,CAAC;IACxGlD,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAExC,UAAU;QAAE6D,WAAW,EAAEyB;MAAW,CAAC;IAChE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMI,gBAAgB,SAASzF,gBAAgB,CAAC;AAG/C0F,iBAAA,GAHKD,gBAAgB;AAAA1D,eAAA,CAAhB0D,gBAAgB;EAAA,IAAAE,8BAAA;EAAA,gBAAAC,0BAAA3D,iBAAA;IAAA,QAAA0D,8BAAA,KAAAA,8BAAA,GAtH2DnI,EAAE,CAAA2F,qBAAA,CAuHoBsC,iBAAgB,IAAAxD,iBAAA,IAAhBwD,iBAAgB;EAAA;AAAA;AAAA1D,eAAA,CADjH0D,gBAAgB,8BAtH2DjI,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EAwHQsD,iBAAgB;EAAArD,SAAA;EAAAC,QAAA,GAxH1B7E,EAAE,CAAA8E,kBAAA,CAwHyF,CAAC;IAAEC,OAAO,EAAEvC,gBAAgB;IAAE4D,WAAW,EAAE6B;EAAiB,CAAC,CAAC,GAxHzJjI,EAAE,CAAAsG,0BAAA;AAAA;AA0HnF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA1HiFjF,EAAE,CAAAkF,iBAAA,CA0HQ+C,gBAAgB,EAAc,CAAC;IAC9GtD,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEvC,gBAAgB;QAAE4D,WAAW,EAAE6B;MAAiB,CAAC;IAC5E,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMI,gBAAgB,SAAS5F,gBAAgB,CAAC;AAG/C6F,iBAAA,GAHKD,gBAAgB;AAAA9D,eAAA,CAAhB8D,gBAAgB;EAAA,IAAAE,8BAAA;EAAA,gBAAAC,0BAAA/D,iBAAA;IAAA,QAAA8D,8BAAA,KAAAA,8BAAA,GArI2DvI,EAAE,CAAA2F,qBAAA,CAsIoB0C,iBAAgB,IAAA5D,iBAAA,IAAhB4D,iBAAgB;EAAA;AAAA;AAAA9D,eAAA,CADjH8D,gBAAgB,8BArI2DrI,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EAuIQ0D,iBAAgB;EAAAzD,SAAA;EAAAC,QAAA,GAvI1B7E,EAAE,CAAA8E,kBAAA,CAuIyF,CAAC;IAAEC,OAAO,EAAEtC,gBAAgB;IAAE2D,WAAW,EAAEiC;EAAiB,CAAC,CAAC,GAvIzJrI,EAAE,CAAAsG,0BAAA;AAAA;AAyInF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAzIiFjF,EAAE,CAAAkF,iBAAA,CAyIQmD,gBAAgB,EAAc,CAAC;IAC9G1D,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEtC,gBAAgB;QAAE2D,WAAW,EAAEiC;MAAiB,CAAC;IAC5E,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMI,YAAY,SAAS/F,YAAY,CAAC;EACpC;EACA,IAAIpB,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACoH,KAAK;EACrB;EACA,IAAIpH,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACqH,aAAa,CAACrH,IAAI,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsH,yBAAyBA,CAAA,EAAG;IACxB,KAAK,CAACA,yBAAyB,CAAC,CAAC;IACjC,IAAI,CAACC,mBAAmB,CAACC,IAAI,CAAC,cAAc,IAAI,CAACC,oBAAoB,EAAE,CAAC;EAC5E;AAMJ;AAACC,aAAA,GAvBKP,YAAY;AAAAlE,eAAA,CAAZkE,YAAY;EAAA,IAAAQ,0BAAA;EAAA,gBAAAC,sBAAAzE,iBAAA;IAAA,QAAAwE,0BAAA,KAAAA,0BAAA,GApJ+DjJ,EAAE,CAAA2F,qBAAA,CAsKoB8C,aAAY,IAAAhE,iBAAA,IAAZgE,aAAY;EAAA;AAAA;AAAAlE,eAAA,CAlB7GkE,YAAY,8BApJ+DzI,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EAuKQ8D,aAAY;EAAA7D,SAAA;EAAAuE,MAAA;IAAA7H,IAAA;EAAA;EAAAuD,QAAA,GAvKtB7E,EAAE,CAAA8E,kBAAA,CAuK6H,CACpM;IAAEC,OAAO,EAAErC,YAAY;IAAE0D,WAAW,EAAEqC;EAAa,CAAC,EACpD;IAAE1D,OAAO,EAAE,4BAA4B;IAAEqB,WAAW,EAAEqC;EAAa,CAAC,CACvE,GA1KwEzI,EAAE,CAAAsG,0BAAA;AAAA;AA4KnF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA5KiFjF,EAAE,CAAAkF,iBAAA,CA4KQuD,YAAY,EAAc,CAAC;IAC1G9D,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,CACP;QAAEN,OAAO,EAAErC,YAAY;QAAE0D,WAAW,EAAEqC;MAAa,CAAC,EACpD;QAAE1D,OAAO,EAAE,4BAA4B;QAAEqB,WAAW,EAAEqC;MAAa,CAAC;IAE5E,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEnH,IAAI,EAAE,CAAC;MACrBqD,IAAI,EAAEhD,KAAK;MACXwD,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMiE,aAAa,SAASzG,aAAa,CAAC;AAGzC0G,cAAA,GAHKD,aAAa;AAAA7E,eAAA,CAAb6E,aAAa;EAAA,IAAAE,2BAAA;EAAA,gBAAAC,uBAAA9E,iBAAA;IAAA,QAAA6E,2BAAA,KAAAA,2BAAA,GA1L8DtJ,EAAE,CAAA2F,qBAAA,CA2LoByD,cAAa,IAAA3E,iBAAA,IAAb2E,cAAa;EAAA;AAAA;AAAA7E,eAAA,CAD9G6E,aAAa,8BA1L8DpJ,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EA4LQyE,cAAa;EAAAxE,SAAA;EAAAiB,SAAA,WAAsG,cAAc;EAAAhB,QAAA,GA5L3I7E,EAAE,CAAAsG,0BAAA;AAAA;AA8LnF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA9LiFjF,EAAE,CAAAkF,iBAAA,CA8LQkE,aAAa,EAAc,CAAC;IAC3GzE,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDoC,IAAI,EAAE;QACF,OAAO,EAAE,iDAAiD;QAC1D,MAAM,EAAE;MACZ;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMgC,aAAa,SAAS5G,aAAa,CAAC;AAGzC6G,cAAA,GAHKD,aAAa;AAAAjF,eAAA,CAAbiF,aAAa;EAAA,IAAAE,2BAAA;EAAA,gBAAAC,uBAAAlF,iBAAA;IAAA,QAAAiF,2BAAA,KAAAA,2BAAA,GAzM8D1J,EAAE,CAAA2F,qBAAA,CA0MoB6D,cAAa,IAAA/E,iBAAA,IAAb+E,cAAa;EAAA;AAAA;AAAAjF,eAAA,CAD9GiF,aAAa,8BAzM8DxJ,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EA2MQ6E,cAAa;EAAA5E,SAAA;EAAAiB,SAAA;EAAAhB,QAAA,GA3MvB7E,EAAE,CAAAsG,0BAAA;AAAA;AA6MnF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA7MiFjF,EAAE,CAAAkF,iBAAA,CA6MQsE,aAAa,EAAc,CAAC;IAC3G7E,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDoC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMoC,OAAO,SAAS/G,OAAO,CAAC;AAG7BgH,QAAA,GAHKD,OAAO;AAAArF,eAAA,CAAPqF,OAAO;EAAA,IAAAE,qBAAA;EAAA,gBAAAC,iBAAAtF,iBAAA;IAAA,QAAAqF,qBAAA,KAAAA,qBAAA,GAvNoE9J,EAAE,CAAA2F,qBAAA,CAwNoBiE,QAAO,IAAAnF,iBAAA,IAAPmF,QAAO;EAAA;AAAA;AAAArF,eAAA,CADxGqF,OAAO,8BAvNoE5J,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EAyNQiF,QAAO;EAAAhF,SAAA;EAAAiB,SAAA;EAAAhB,QAAA,GAzNjB7E,EAAE,CAAAsG,0BAAA;AAAA;AA2NnF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA3NiFjF,EAAE,CAAAkF,iBAAA,CA2NQ0E,OAAO,EAAc,CAAC;IACrGjF,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCoC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMwC,YAAY,GAAG,6CAA6C;AAClE;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASnH,eAAe,CAAC;AAG7CoH,gBAAA,GAHKD,eAAe;AAAA1F,eAAA,CAAf0F,eAAe;EAAA,IAAAE,6BAAA;EAAA,gBAAAC,yBAAA3F,iBAAA;IAAA,QAAA0F,6BAAA,KAAAA,6BAAA,GA3O4DnK,EAAE,CAAA2F,qBAAA,CA4OoBsE,gBAAe,IAAAxF,iBAAA,IAAfwF,gBAAe;EAAA;AAAA;AAAA1F,eAAA,CADhH0F,eAAe,8BA3O4DjK,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EA6OQsF,gBAAe;EAAArF,SAAA;EAAAuE,MAAA;IAAAkB,OAAA;IAAAC,MAAA,yCAAoJ1I,gBAAgB;EAAA;EAAAiD,QAAA,GA7O7L7E,EAAE,CAAA8E,kBAAA,CA6O2M,CAAC;IAAEC,OAAO,EAAEjC,eAAe;IAAEsD,WAAW,EAAE6D;EAAgB,CAAC,CAAC,GA7OzQjK,EAAE,CAAAsG,0BAAA;AAAA;AA+OnF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA/OiFjF,EAAE,CAAAkF,iBAAA,CA+OQ+E,eAAe,EAAc,CAAC;IAC7GtF,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEjC,eAAe;QAAEsD,WAAW,EAAE6D;MAAgB,CAAC,CAAC;MACvEd,MAAM,EAAE,CACJ;QAAE7H,IAAI,EAAE,SAAS;QAAEiJ,KAAK,EAAE;MAAkB,CAAC,EAC7C;QAAEjJ,IAAI,EAAE,QAAQ;QAAEiJ,KAAK,EAAE,uBAAuB;QAAEC,SAAS,EAAE5I;MAAiB,CAAC;IAEvF,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAM6I,eAAe,SAAS1H,eAAe,CAAC;AAG7C2H,gBAAA,GAHKD,eAAe;AAAAlG,eAAA,CAAfkG,eAAe;EAAA,IAAAE,6BAAA;EAAA,gBAAAC,yBAAAnG,iBAAA;IAAA,QAAAkG,6BAAA,KAAAA,6BAAA,GA9P4D3K,EAAE,CAAA2F,qBAAA,CA+PoB8E,gBAAe,IAAAhG,iBAAA,IAAfgG,gBAAe;EAAA;AAAA;AAAAlG,eAAA,CADhHkG,eAAe,8BA9P4DzK,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EAgQQ8F,gBAAe;EAAA7F,SAAA;EAAAuE,MAAA;IAAAkB,OAAA;IAAAC,MAAA,yCAAoJ1I,gBAAgB;EAAA;EAAAiD,QAAA,GAhQ7L7E,EAAE,CAAA8E,kBAAA,CAgQ2M,CAAC;IAAEC,OAAO,EAAEhC,eAAe;IAAEqD,WAAW,EAAEqE;EAAgB,CAAC,CAAC,GAhQzQzK,EAAE,CAAAsG,0BAAA;AAAA;AAkQnF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAlQiFjF,EAAE,CAAAkF,iBAAA,CAkQQuF,eAAe,EAAc,CAAC;IAC7G9F,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEhC,eAAe;QAAEqD,WAAW,EAAEqE;MAAgB,CAAC,CAAC;MACvEtB,MAAM,EAAE,CACJ;QAAE7H,IAAI,EAAE,SAAS;QAAEiJ,KAAK,EAAE;MAAkB,CAAC,EAC7C;QAAEjJ,IAAI,EAAE,QAAQ;QAAEiJ,KAAK,EAAE,uBAAuB;QAAEC,SAAS,EAAE5I;MAAiB,CAAC;IAEvF,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMiJ,SAAS,SAAS7H,SAAS,CAAC;AAGjC8H,UAAA,GAHKD,SAAS;AAAAtG,eAAA,CAATsG,SAAS;EAAA,IAAAE,uBAAA;EAAA,gBAAAC,mBAAAvG,iBAAA;IAAA,QAAAsG,uBAAA,KAAAA,uBAAA,GAlRkE/K,EAAE,CAAA2F,qBAAA,CAmRoBkF,UAAS,IAAApG,iBAAA,IAAToG,UAAS;EAAA;AAAA;AAAAtG,eAAA,CAD1GsG,SAAS,8BAlRkE7K,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EAoRQkG,UAAS;EAAAjG,SAAA;EAAAuE,MAAA;IAAAkB,OAAA;IAAAY,IAAA;EAAA;EAAApG,QAAA,GApRnB7E,EAAE,CAAA8E,kBAAA,CAoRkK,CAAC;IAAEC,OAAO,EAAE/B,SAAS;IAAEoD,WAAW,EAAEyE;EAAU,CAAC,CAAC,GApRpN7K,EAAE,CAAAsG,0BAAA;AAAA;AAsRnF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAtRiFjF,EAAE,CAAAkF,iBAAA,CAsRQ2F,SAAS,EAAc,CAAC;IACvGlG,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE/B,SAAS;QAAEoD,WAAW,EAAEyE;MAAU,CAAC,CAAC;MAC3D1B,MAAM,EAAE,CACJ;QAAE7H,IAAI,EAAE,SAAS;QAAEiJ,KAAK,EAAE;MAAmB,CAAC,EAC9C;QAAEjJ,IAAI,EAAE,MAAM;QAAEiJ,KAAK,EAAE;MAAgB,CAAC;IAEhD,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMW,YAAY,SAASjI,YAAY,CAAC;AAGvCkI,aAAA,GAHKD,YAAY;AAAA3G,eAAA,CAAZ2G,YAAY;EAAA,IAAAE,0BAAA;EAAA,gBAAAC,sBAAA5G,iBAAA;IAAA,QAAA2G,0BAAA,KAAAA,0BAAA,GAlS+DpL,EAAE,CAAA2F,qBAAA,CAmSoBuF,aAAY,IAAAzG,iBAAA,IAAZyG,aAAY;EAAA;AAAA;AAAA3G,eAAA,CAD7G2G,YAAY,8BAlS+DlL,EAAE,CAAA4F,iBAAA;EAAAjB,IAAA,EAoSQuG,aAAY;EAAAtG,SAAA;EAAAiB,SAAA,WAAoG,KAAK;EAAAM,QAAA;EAAAtB,QAAA,GApS/H7E,EAAE,CAAA8E,kBAAA,CAoS+M,CAAC;IAAEC,OAAO,EAAE9B,YAAY;IAAEmD,WAAW,EAAE8E;EAAa,CAAC,CAAC,GApSvQlL,EAAE,CAAAsG,0BAAA;EAAAG,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA0E,uBAAAnL,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFH,EAAE,CAAAM,kBAAA,KAoS6X,CAAC;IAAA;EAAA;EAAA+G,YAAA,GAA6DnE,aAAa;EAAAqE,aAAA;AAAA;AAE3hB;EAAA,QAAAtC,SAAA,oBAAAA,SAAA,KAtSiFjF,EAAE,CAAAkF,iBAAA,CAsSQgG,YAAY,EAAc,CAAC;IAC1GvG,IAAI,EAAEnD,SAAS;IACf2D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CwB,QAAQ,EAAEoD,YAAY;MACtBxC,IAAI,EAAE;QACF,OAAO,EAAE,+CAA+C;QACxD,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAE,eAAe,EAAEhG,uBAAuB,CAACiG,OAAO;MAChDJ,aAAa,EAAE9F,iBAAiB,CAACgG,IAAI;MACrCtB,QAAQ,EAAE,cAAc;MACxBd,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE9B,YAAY;QAAEmD,WAAW,EAAE8E;MAAa,CAAC,CAAC;MACjEtD,OAAO,EAAE,CAAC1E,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMqI,YAAY,SAASpI,YAAY,CAAC;AAGvCqI,aAAA,GAHKD,YAAY;AAAAhH,eAAA,CAAZgH,YAAY;EAAA,IAAAE,0BAAA;EAAA,gBAAAC,sBAAAjH,iBAAA;IAAA,QAAAgH,0BAAA,KAAAA,0BAAA,GAzT+DzL,EAAE,CAAA2F,qBAAA,CA0ToB4F,aAAY,IAAA9G,iBAAA,IAAZ8G,aAAY;EAAA;AAAA;AAAAhH,eAAA,CAD7GgH,YAAY,8BAzT+DvL,EAAE,CAAA4F,iBAAA;EAAAjB,IAAA,EA2TQ4G,aAAY;EAAA3G,SAAA;EAAAiB,SAAA,WAAoG,KAAK;EAAAM,QAAA;EAAAtB,QAAA,GA3T/H7E,EAAE,CAAA8E,kBAAA,CA2TwM,CAAC;IAAEC,OAAO,EAAE5B,YAAY;IAAEiD,WAAW,EAAEmF;EAAa,CAAC,CAAC,GA3ThQvL,EAAE,CAAAsG,0BAAA;EAAAG,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA+E,uBAAAxL,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFH,EAAE,CAAAM,kBAAA,KA2TsX,CAAC;IAAA;EAAA;EAAA+G,YAAA,GAA6DnE,aAAa;EAAAqE,aAAA;AAAA;AAEphB;EAAA,QAAAtC,SAAA,oBAAAA,SAAA,KA7TiFjF,EAAE,CAAAkF,iBAAA,CA6TQqG,YAAY,EAAc,CAAC;IAC1G5G,IAAI,EAAEnD,SAAS;IACf2D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CwB,QAAQ,EAAEoD,YAAY;MACtBxC,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAE,eAAe,EAAEhG,uBAAuB,CAACiG,OAAO;MAChDJ,aAAa,EAAE9F,iBAAiB,CAACgG,IAAI;MACrCtB,QAAQ,EAAE,cAAc;MACxBd,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE5B,YAAY;QAAEiD,WAAW,EAAEmF;MAAa,CAAC,CAAC;MACjE3D,OAAO,EAAE,CAAC1E,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM0I,MAAM,SAASxI,MAAM,CAAC;AAG3ByI,OAAA,GAHKD,MAAM;AAAArH,eAAA,CAANqH,MAAM;EAAA,IAAAE,oBAAA;EAAA,gBAAAC,gBAAAtH,iBAAA;IAAA,QAAAqH,oBAAA,KAAAA,oBAAA,GAhVqE9L,EAAE,CAAA2F,qBAAA,CAiVoBiG,OAAM,IAAAnH,iBAAA,IAANmH,OAAM;EAAA;AAAA;AAAArH,eAAA,CADvGqH,MAAM,8BAhVqE5L,EAAE,CAAA4F,iBAAA;EAAAjB,IAAA,EAkVQiH,OAAM;EAAAhH,SAAA;EAAAiB,SAAA,WAAsF,KAAK;EAAAM,QAAA;EAAAtB,QAAA,GAlV3G7E,EAAE,CAAA8E,kBAAA,CAkV6K,CAAC;IAAEC,OAAO,EAAE3B,MAAM;IAAEgD,WAAW,EAAEwF;EAAO,CAAC,CAAC,GAlVzN5L,EAAE,CAAAsG,0BAAA;EAAAG,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAoF,iBAAA7L,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFH,EAAE,CAAAM,kBAAA,KAkVyU,CAAC;IAAA;EAAA;EAAA+G,YAAA,GAA6DnE,aAAa;EAAAqE,aAAA;AAAA;AAEve;EAAA,QAAAtC,SAAA,oBAAAA,SAAA,KApViFjF,EAAE,CAAAkF,iBAAA,CAoVQ0G,MAAM,EAAc,CAAC;IACpGjH,IAAI,EAAEnD,SAAS;IACf2D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCwB,QAAQ,EAAEoD,YAAY;MACtBxC,IAAI,EAAE;QACF,OAAO,EAAE,iCAAiC;QAC1C,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAE,eAAe,EAAEhG,uBAAuB,CAACiG,OAAO;MAChDJ,aAAa,EAAE9F,iBAAiB,CAACgG,IAAI;MACrCtB,QAAQ,EAAE,QAAQ;MAClBd,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE3B,MAAM;QAAEgD,WAAW,EAAEwF;MAAO,CAAC,CAAC;MACrDhE,OAAO,EAAE,CAAC1E,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM+I,YAAY,SAAS5I,YAAY,CAAC;EAAAkC,YAAA,GAAAJ,IAAA;IAAA,SAAAA,IAAA;IAAAZ,eAAA,4BAChB,qBAAqB;EAAA;AAG7C;AAAC2H,aAAA,GAJKD,YAAY;AAAA1H,eAAA,CAAZ0H,YAAY;EAAA,IAAAE,0BAAA;EAAA,gBAAAC,sBAAA3H,iBAAA;IAAA,QAAA0H,0BAAA,KAAAA,0BAAA,GAvW+DnM,EAAE,CAAA2F,qBAAA,CAyWoBsG,aAAY,IAAAxH,iBAAA,IAAZwH,aAAY;EAAA;AAAA;AAAA1H,eAAA,CAF7G0H,YAAY,8BAvW+DjM,EAAE,CAAA0E,iBAAA;EAAAC,IAAA,EA0WQsH,aAAY;EAAArH,SAAA;EAAAC,QAAA,GA1WtB7E,EAAE,CAAA8E,kBAAA,CA0W4F,CAAC;IAAEC,OAAO,EAAE1B,YAAY;IAAE+C,WAAW,EAAE6F;EAAa,CAAC,CAAC,GA1WpJjM,EAAE,CAAAsG,0BAAA;AAAA;AA4WnF;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA5WiFjF,EAAE,CAAAkF,iBAAA,CA4WQ+G,YAAY,EAAc,CAAC;IAC1GtH,IAAI,EAAEpD,SAAS;IACf4D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2BAA2B;MACrCC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE1B,YAAY;QAAE+C,WAAW,EAAE6F;MAAa,CAAC;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,aAAa,SAAS/I,aAAa,CAAC;AAYzCgJ,cAAA,GAZKD,aAAa;AAAA9H,eAAA,CAAb8H,aAAa;EAAA,IAAAE,2BAAA;EAAA,gBAAAC,uBAAA/H,iBAAA;IAAA,QAAA8H,2BAAA,KAAAA,2BAAA,GA7X8DvM,EAAE,CAAA2F,qBAAA,CA8XoB0G,cAAa,IAAA5H,iBAAA,IAAb4H,cAAa;EAAA;AAAA;AAAA9H,eAAA,CAD9G8H,aAAa,8BA7X8DrM,EAAE,CAAA4F,iBAAA;EAAAjB,IAAA,EA+XQ0H,cAAa;EAAAzH,SAAA;EAAAC,QAAA,GA/XvB7E,EAAE,CAAAsG,0BAAA;EAAAG,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA6F,wBAAAtM,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFH,EAAE,CAAA0M,uBAAA,KAgYrD,CAAC;MAhYkD1M,EAAE,CAAAgH,UAAA,IAAAvG,4BAAA,eAiYX,CAAC,IAAAS,4BAAA,eAGH,CAAC;MApYUlB,EAAE,CAAA2M,qBAAA;IAAA;EAAA;EAAAtF,YAAA,GAwYpBoB,YAAY,EAAqFR,gBAAgB,EAA+DmB,aAAa,EAAiFvB,UAAU,EAAyD+B,OAAO;EAAArC,aAAA;AAAA;AAEvZ;EAAA,QAAAtC,SAAA,oBAAAA,SAAA,KA1YiFjF,EAAE,CAAAkF,iBAAA,CA0YQmH,aAAa,EAAc,CAAC;IAC3G1H,IAAI,EAAEnD,SAAS;IACf2D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BwB,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBW,aAAa,EAAE9F,iBAAiB,CAACgG,IAAI;MACrC;MACA;MACA;MACA;MACA;MACA;MACAC,eAAe,EAAEhG,uBAAuB,CAACiG,OAAO;MAChDC,OAAO,EAAE,CAACa,YAAY,EAAER,gBAAgB,EAAEmB,aAAa,EAAEvB,UAAU,EAAE+B,OAAO;IAChF,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMgD,qBAAqB,GAAG;AAC1B;AACAtH,QAAQ,EACRjB,cAAc;AACd;AACA4D,gBAAgB,EAChBgC,eAAe,EACfxB,YAAY,EACZZ,UAAU,EACVgD,SAAS,EACTxC,gBAAgB,EAChBoC,eAAe;AACf;AACArB,aAAa,EACbQ,OAAO,EACPJ,aAAa;AACb;AACA0B,YAAY,EACZU,MAAM,EACNL,YAAY,EACZU,YAAY,EACZI,aAAa,CAChB;AACD,MAAMQ,cAAc,CAAC;AA8CpBC,eAAA,GA9CKD,cAAc;AAAAtI,eAAA,CAAdsI,cAAc,wBAAAE,wBAAAtI,iBAAA;EAAA,YAAAA,iBAAA,IACmFoI,eAAc;AAAA;AAAAtI,eAAA,CAD/GsI,cAAc,8BA3b6D7M,EAAE,CAAAgN,gBAAA;EAAArI,IAAA,EA6bqBkI,eAAc;EAAAjF,OAAA,GAAY/D,eAAe,EAAEN,cAAc;EACrJ;EACA+B,QAAQ,EACRjB,cAAc;EACd;EACA4D,gBAAgB,EAChBgC,eAAe,EACfxB,YAAY,EACZZ,UAAU,EACVgD,SAAS,EACTxC,gBAAgB,EAChBoC,eAAe;EACf;EACArB,aAAa,EACbQ,OAAO,EACPJ,aAAa;EACb;EACA0B,YAAY,EACZU,MAAM,EACNL,YAAY,EACZU,YAAY,EACZI,aAAa;EAAAY,OAAA,GAAapJ,eAAe;EACzC;EACAyB,QAAQ,EACRjB,cAAc;EACd;EACA4D,gBAAgB,EAChBgC,eAAe,EACfxB,YAAY,EACZZ,UAAU,EACVgD,SAAS,EACTxC,gBAAgB,EAChBoC,eAAe;EACf;EACArB,aAAa,EACbQ,OAAO,EACPJ,aAAa;EACb;EACA0B,YAAY,EACZU,MAAM,EACNL,YAAY,EACZU,YAAY,EACZI,aAAa;AAAA;AAAA9H,eAAA,CA5CnBsI,cAAc,8BA3b6D7M,EAAE,CAAAkN,gBAAA;EAAAtF,OAAA,GAwe+C/D,eAAe,EAAEN,cAAc,EAAEM,eAAe;AAAA;AAElL;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KA1eiFjF,EAAE,CAAAkF,iBAAA,CA0eQ2H,cAAc,EAAc,CAAC;IAC5GlI,IAAI,EAAE9C,QAAQ;IACdsD,IAAI,EAAE,CAAC;MACCyC,OAAO,EAAE,CAAC/D,eAAe,EAAEN,cAAc,EAAE,GAAGqJ,qBAAqB,CAAC;MACpEK,OAAO,EAAE,CAACpJ,eAAe,EAAE+I,qBAAqB;IACpD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMO,gBAAgB,GAAG,gBAAgB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAASzJ,UAAU,CAAC;EAqBxC;EACA,IAAI0J,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAACC,KAAK;EAC3B;EACA,IAAIF,IAAIA,CAACA,IAAI,EAAE;IACXA,IAAI,GAAGG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;IACtC,IAAI,CAACC,KAAK,CAACI,IAAI,CAACL,IAAI,CAAC;IACrB;IACA;IACA,IAAI,CAAC,IAAI,CAACM,0BAA0B,EAAE;MAClC,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIQ,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO,CAACP,KAAK;EAC7B;EACA,IAAIM,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACC,OAAO,CAACJ,IAAI,CAACG,MAAM,CAAC;IACzB;IACA;IACA,IAAI,CAAC,IAAI,CAACF,0BAA0B,EAAE;MAClC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACP,IAAI,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIU,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,IAAI,CAACE,yBAAyB,CAAC,CAAC;EACpC;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC3B,IAAI,CAACD,yBAAyB,CAAC,CAAC;EACpC;EA2FA1I,WAAWA,CAAC6I,WAAW,GAAG,EAAE,EAAE;IAC1B,KAAK,CAAC,CAAC;IAxKX;IAAA7J,eAAA;IAEA;IAAAA,eAAA,sBACc,IAAIT,eAAe,CAAC,EAAE,CAAC;IACrC;IAAAS,eAAA,kBACU,IAAIT,eAAe,CAAC,EAAE,CAAC;IACjC;IAAAS,eAAA,+BACuB,IAAIR,OAAO,CAAC,CAAC;IACpC;AACJ;AACA;AACA;IAHIQ,eAAA,qCAI6B,IAAI;IACjC;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAiEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IARIA,eAAA,8BASsB,CAAC8I,IAAI,EAAEgB,YAAY,KAAK;MAC1C,MAAMd,KAAK,GAAGF,IAAI,CAACgB,YAAY,CAAC;MAChC,IAAIlK,cAAc,CAACoJ,KAAK,CAAC,EAAE;QACvB,MAAMe,WAAW,GAAGC,MAAM,CAAChB,KAAK,CAAC;QACjC;QACA;QACA,OAAOe,WAAW,GAAGnB,gBAAgB,GAAGmB,WAAW,GAAGf,KAAK;MAC/D;MACA,OAAOA,KAAK;IAChB,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IARIhJ,eAAA,mBASW,CAAC8I,IAAI,EAAEU,IAAI,KAAK;MACvB,MAAMS,MAAM,GAAGT,IAAI,CAACS,MAAM;MAC1B,MAAMC,SAAS,GAAGV,IAAI,CAACU,SAAS;MAChC,IAAI,CAACD,MAAM,IAAIC,SAAS,IAAI,EAAE,EAAE;QAC5B,OAAOpB,IAAI;MACf;MACA,OAAOA,IAAI,CAACU,IAAI,CAAC,CAACW,CAAC,EAAEC,CAAC,KAAK;QACvB,IAAIC,MAAM,GAAG,IAAI,CAACC,mBAAmB,CAACH,CAAC,EAAEF,MAAM,CAAC;QAChD,IAAIM,MAAM,GAAG,IAAI,CAACD,mBAAmB,CAACF,CAAC,EAAEH,MAAM,CAAC;QAChD;QACA;QACA;QACA,MAAMO,UAAU,GAAG,OAAOH,MAAM;QAChC,MAAMI,UAAU,GAAG,OAAOF,MAAM;QAChC,IAAIC,UAAU,KAAKC,UAAU,EAAE;UAC3B,IAAID,UAAU,KAAK,QAAQ,EAAE;YACzBH,MAAM,IAAI,EAAE;UAChB;UACA,IAAII,UAAU,KAAK,QAAQ,EAAE;YACzBF,MAAM,IAAI,EAAE;UAChB;QACJ;QACA;QACA;QACA;QACA;QACA,IAAIG,gBAAgB,GAAG,CAAC;QACxB,IAAIL,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAE;UAClC;UACA,IAAIF,MAAM,GAAGE,MAAM,EAAE;YACjBG,gBAAgB,GAAG,CAAC;UACxB,CAAC,MACI,IAAIL,MAAM,GAAGE,MAAM,EAAE;YACtBG,gBAAgB,GAAG,CAAC,CAAC;UACzB;QACJ,CAAC,MACI,IAAIL,MAAM,IAAI,IAAI,EAAE;UACrBK,gBAAgB,GAAG,CAAC;QACxB,CAAC,MACI,IAAIH,MAAM,IAAI,IAAI,EAAE;UACrBG,gBAAgB,GAAG,CAAC,CAAC;QACzB;QACA,OAAOA,gBAAgB,IAAIR,SAAS,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC;IACN,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IATIlK,eAAA,0BAUkB,CAAC8I,IAAI,EAAEQ,MAAM,KAAK;MAChC;MACA,MAAMqB,iBAAiB,GAAGrB,MAAM,CAACsB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACrD;MACA,OAAOC,MAAM,CAACC,MAAM,CAACjC,IAAI,CAAC,CAACkC,IAAI,CAAChC,KAAK,IAAI,GAAGA,KAAK,EAAE,CAAC6B,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACN,iBAAiB,CAAC,CAAC;IAClG,CAAC;IAGG,IAAI,CAAC5B,KAAK,GAAG,IAAIxJ,eAAe,CAACsK,WAAW,CAAC;IAC7C,IAAI,CAACH,yBAAyB,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACIA,yBAAyBA,CAAA,EAAG;IAAA,IAAAwB,qBAAA;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,UAAU,GAAG,IAAI,CAAC1B,KAAK,GACvBhK,KAAK,CAAC,IAAI,CAACgK,KAAK,CAAC0B,UAAU,EAAE,IAAI,CAAC1B,KAAK,CAAC2B,WAAW,CAAC,GACpD1L,EAAE,CAAC,IAAI,CAAC;IACd,MAAM2L,UAAU,GAAG,IAAI,CAACzB,UAAU,GAC5BnK,KAAK,CAAC,IAAI,CAACmK,UAAU,CAAC0B,IAAI,EAAE,IAAI,CAACC,oBAAoB,EAAE,IAAI,CAAC3B,UAAU,CAACwB,WAAW,CAAC,GACnF1L,EAAE,CAAC,IAAI,CAAC;IACd,MAAM8L,UAAU,GAAG,IAAI,CAACzC,KAAK;IAC7B;IACA,MAAM0C,YAAY,GAAG9L,aAAa,CAAC,CAAC6L,UAAU,EAAE,IAAI,CAACjC,OAAO,CAAC,CAAC,CAACmC,IAAI,CAAC7L,GAAG,CAAC,CAAC,CAACiJ,IAAI,CAAC,KAAK,IAAI,CAACO,WAAW,CAACP,IAAI,CAAC,CAAC,CAAC;IAC5G;IACA,MAAM6C,WAAW,GAAGhM,aAAa,CAAC,CAAC8L,YAAY,EAAEN,UAAU,CAAC,CAAC,CAACO,IAAI,CAAC7L,GAAG,CAAC,CAAC,CAACiJ,IAAI,CAAC,KAAK,IAAI,CAAC8C,UAAU,CAAC9C,IAAI,CAAC,CAAC,CAAC;IAC1G;IACA,MAAM+C,aAAa,GAAGlM,aAAa,CAAC,CAACgM,WAAW,EAAEN,UAAU,CAAC,CAAC,CAACK,IAAI,CAAC7L,GAAG,CAAC,CAAC,CAACiJ,IAAI,CAAC,KAAK,IAAI,CAACgD,SAAS,CAAChD,IAAI,CAAC,CAAC,CAAC;IAC1G;IACA,CAAAoC,qBAAA,OAAI,CAAC9B,0BAA0B,cAAA8B,qBAAA,eAA/BA,qBAAA,CAAiCa,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC3C,0BAA0B,GAAGyC,aAAa,CAACG,SAAS,CAAClD,IAAI,IAAI,IAAI,CAACmD,WAAW,CAAC9C,IAAI,CAACL,IAAI,CAAC,CAAC;EAClG;EACA;AACJ;AACA;AACA;AACA;EACIO,WAAWA,CAACP,IAAI,EAAE;IACd;IACA;IACA;IACA,IAAI,CAAC2C,YAAY,GACb,IAAI,CAACnC,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,KAAK,EAAE,GACnCR,IAAI,GACJA,IAAI,CAACQ,MAAM,CAAC4C,GAAG,IAAI,IAAI,CAACC,eAAe,CAACD,GAAG,EAAE,IAAI,CAAC5C,MAAM,CAAC,CAAC;IACpE,IAAI,IAAI,CAACK,SAAS,EAAE;MAChB,IAAI,CAACyC,gBAAgB,CAAC,IAAI,CAACX,YAAY,CAACY,MAAM,CAAC;IACnD;IACA,OAAO,IAAI,CAACZ,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIG,UAAUA,CAAC9C,IAAI,EAAE;IACb;IACA,IAAI,CAAC,IAAI,CAACU,IAAI,EAAE;MACZ,OAAOV,IAAI;IACf;IACA,OAAO,IAAI,CAACwD,QAAQ,CAACxD,IAAI,CAACyD,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC/C,IAAI,CAAC;EACjD;EACA;AACJ;AACA;AACA;EACIsC,SAASA,CAAChD,IAAI,EAAE;IACZ,IAAI,CAAC,IAAI,CAACa,SAAS,EAAE;MACjB,OAAOb,IAAI;IACf;IACA,MAAM0D,UAAU,GAAG,IAAI,CAAC7C,SAAS,CAAC8C,SAAS,GAAG,IAAI,CAAC9C,SAAS,CAAC+C,QAAQ;IACrE,OAAO5D,IAAI,CAACyD,KAAK,CAACC,UAAU,EAAEA,UAAU,GAAG,IAAI,CAAC7C,SAAS,CAAC+C,QAAQ,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;EACIN,gBAAgBA,CAACO,kBAAkB,EAAE;IACjCC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,MAAMnD,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAI,CAACA,SAAS,EAAE;QACZ;MACJ;MACAA,SAAS,CAAC0C,MAAM,GAAGM,kBAAkB;MACrC;MACA,IAAIhD,SAAS,CAAC8C,SAAS,GAAG,CAAC,EAAE;QACzB,MAAMM,aAAa,GAAGC,IAAI,CAACC,IAAI,CAACtD,SAAS,CAAC0C,MAAM,GAAG1C,SAAS,CAAC+C,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;QAC/E,MAAMQ,YAAY,GAAGF,IAAI,CAACG,GAAG,CAACxD,SAAS,CAAC8C,SAAS,EAAEM,aAAa,CAAC;QACjE,IAAIG,YAAY,KAAKvD,SAAS,CAAC8C,SAAS,EAAE;UACtC9C,SAAS,CAAC8C,SAAS,GAAGS,YAAY;UAClC;UACA;UACA,IAAI,CAAC3B,oBAAoB,CAACpC,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIiE,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAChE,0BAA0B,EAAE;MAClC,IAAI,CAACM,yBAAyB,CAAC,CAAC;IACpC;IACA,OAAO,IAAI,CAACuC,WAAW;EAC3B;EACA;AACJ;AACA;AACA;EACIoB,UAAUA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACT,CAAAA,sBAAA,OAAI,CAAClE,0BAA0B,cAAAkE,sBAAA,eAA/BA,sBAAA,CAAiCvB,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC3C,0BAA0B,GAAG,IAAI;EAC1C;AACJ;AAEA,SAAS/D,OAAO,EAAE/B,UAAU,EAAEY,YAAY,EAAEe,aAAa,EAAEnB,gBAAgB,EAAEkD,YAAY,EAAEd,eAAe,EAAErB,aAAa,EAAEnB,gBAAgB,EAAEiD,YAAY,EAAEjB,eAAe,EAAEgC,YAAY,EAAE5H,cAAc,EAAEuH,MAAM,EAAEf,SAAS,EAAEvF,QAAQ,EAAE8H,kBAAkB,EAAEP,cAAc,EAAER,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}