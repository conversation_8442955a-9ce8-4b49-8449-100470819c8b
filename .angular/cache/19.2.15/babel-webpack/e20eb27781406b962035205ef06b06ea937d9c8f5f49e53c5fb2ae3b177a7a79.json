{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { FormattedNumberPipe } from './formatted-number.pipe';\nimport { FormattedMoneyPipe } from './formatted-money.pipe';\nimport { TruncatePipe } from './truncate.pipe';\nlet GridPipesModule = class GridPipesModule {};\nGridPipesModule = __decorate([NgModule({\n  imports: [],\n  declarations: [FormattedNumberPipe, FormattedMoneyPipe, TruncatePipe],\n  exports: [FormattedNumberPipe, FormattedMoneyPipe, TruncatePipe],\n  providers: []\n})], GridPipesModule);\nexport { GridPipesModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "FormattedNumberPipe", "FormattedMoneyPipe", "TruncatePipe", "GridPipesModule", "imports", "declarations", "exports", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/pipes/grid-pipes.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { FormattedNumberPipe } from './formatted-number.pipe';\nimport { FormattedMoneyPipe } from './formatted-money.pipe';\nimport { TruncatePipe } from './truncate.pipe';\nlet GridPipesModule = class GridPipesModule {\n};\nGridPipesModule = __decorate([\n    NgModule({\n        imports: [],\n        declarations: [\n            FormattedNumberPipe,\n            FormattedMoneyPipe,\n            TruncatePipe,\n        ],\n        exports: [\n            FormattedNumberPipe,\n            FormattedMoneyPipe,\n            TruncatePipe,\n        ],\n        providers: [],\n    })\n], GridPipesModule);\nexport { GridPipesModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,IAAIC,eAAe,GAAG,MAAMA,eAAe,CAAC,EAC3C;AACDA,eAAe,GAAGL,UAAU,CAAC,CACzBC,QAAQ,CAAC;EACLK,OAAO,EAAE,EAAE;EACXC,YAAY,EAAE,CACVL,mBAAmB,EACnBC,kBAAkB,EAClBC,YAAY,CACf;EACDI,OAAO,EAAE,CACLN,mBAAmB,EACnBC,kBAAkB,EAClBC,YAAY,CACf;EACDK,SAAS,EAAE;AACf,CAAC,CAAC,CACL,EAAEJ,eAAe,CAAC;AACnB,SAASA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}