{"ast": null, "code": "var _SwuiSidebarComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-sidebar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-sidebar.component.scss?ngResource\";\nimport { Component, HostBinding, HostListener, Input, ViewChild } from '@angular/core';\nimport { SwuiSidebarService } from './swui-sidebar.service';\nimport { DomSanitizer } from '@angular/platform-browser';\nlet SwuiSidebarComponent = (_SwuiSidebarComponent = class SwuiSidebarComponent {\n  constructor(sidebarStateService, sanitizer) {\n    this.sidebarStateService = sidebarStateService;\n    this.sanitizer = sanitizer;\n    this.topPadding = '0px';\n    this.isCollapsed = false;\n    this.isHovered = false;\n    this.collapseOnDimension();\n    this._sidebarStateSubscription = this.sidebarStateService.isCollapsed.subscribe(val => {\n      this.isCollapsed = val;\n    });\n  }\n  get valueAsStyle() {\n    return this.sanitizer.bypassSecurityTrustStyle(`--top-padding: ${this.topPadding}`);\n  }\n  onResize() {\n    this.collapseOnDimension();\n  }\n  onClick(targetElement) {\n    if (this.sidebarRef) {\n      const clickedInside = this.sidebarRef.nativeElement.contains(targetElement);\n      if (!clickedInside && window.innerWidth <= 1024) {\n        this.sidebarStateService.isCollapsed.next(true);\n      }\n    }\n  }\n  ngAfterViewInit() {\n    this.bindMouseenterEvent();\n    this.bindMouseLeaveEvent();\n  }\n  ngOnDestroy() {\n    if (this._sidebarStateSubscription) {\n      this._sidebarStateSubscription.unsubscribe();\n    }\n  }\n  toggleSidebar(event) {\n    event.preventDefault();\n    this.sidebarStateService.isCollapsed.next(!this.isCollapsed);\n  }\n  bindMouseenterEvent() {\n    if (this.sidebarRef) {\n      this.sidebarRef.nativeElement.addEventListener('mouseenter', () => {\n        if (this.isCollapsed) {\n          this.isHovered = true;\n        }\n      });\n    }\n  }\n  bindMouseLeaveEvent() {\n    if (this.sidebarRef) {\n      this.sidebarRef.nativeElement.addEventListener('mouseleave', () => {\n        this.isHovered = false;\n      });\n    }\n  }\n  collapseOnDimension() {\n    if (window.innerWidth <= 1024) {\n      this.sidebarStateService.isCollapsed.next(true);\n    }\n  }\n}, _SwuiSidebarComponent.ctorParameters = () => [{\n  type: SwuiSidebarService\n}, {\n  type: DomSanitizer\n}], _SwuiSidebarComponent.propDecorators = {\n  sidebarRef: [{\n    type: ViewChild,\n    args: ['sidebar']\n  }],\n  menuItems: [{\n    type: Input\n  }],\n  activeColor: [{\n    type: Input\n  }],\n  topPadding: [{\n    type: Input\n  }],\n  valueAsStyle: [{\n    type: HostBinding,\n    args: ['attr.style']\n  }],\n  onResize: [{\n    type: HostListener,\n    args: ['window:resize']\n  }],\n  onClick: [{\n    type: HostListener,\n    args: ['document:click', ['$event.target']]\n  }]\n}, _SwuiSidebarComponent);\nSwuiSidebarComponent = __decorate([Component({\n  selector: 'lib-swui-sidebar',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSidebarComponent);\nexport { SwuiSidebarComponent };", "map": {"version": 3, "names": ["Component", "HostBinding", "HostListener", "Input", "ViewChild", "SwuiSidebarService", "Dom<PERSON><PERSON><PERSON>zer", "SwuiSidebarComponent", "_SwuiSidebarComponent", "constructor", "sidebarStateService", "sanitizer", "topPadding", "isCollapsed", "isHovered", "collapseOnDimension", "_sidebarStateSubscription", "subscribe", "val", "valueAsStyle", "bypassSecurityTrustStyle", "onResize", "onClick", "targetElement", "sidebarRef", "clickedInside", "nativeElement", "contains", "window", "innerWidth", "next", "ngAfterViewInit", "bindMouseenterEvent", "bindMouseLeaveEvent", "ngOnDestroy", "unsubscribe", "toggleSidebar", "event", "preventDefault", "addEventListener", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-sidebar/swui-sidebar.component.ts"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, HostBinding, HostListener, Input, OnDestroy, ViewChild } from '@angular/core';\nimport { Subscription } from 'rxjs';\n\nimport { SwuiSidebarService } from './swui-sidebar.service';\nimport { SwuiMenuItem } from '../swui-menu/swui-menu.interface';\nimport { DomSanitizer, SafeStyle } from '@angular/platform-browser';\n\n@Component({\n    selector: 'lib-swui-sidebar',\n    templateUrl: './swui-sidebar.component.html',\n    styleUrls: ['./swui-sidebar.component.scss'],\n    standalone: false\n})\nexport class SwuiSidebarComponent implements AfterViewInit, OnDestroy {\n  @ViewChild('sidebar') sidebarRef?: ElementRef;\n\n  @Input() menuItems?: SwuiMenuItem[];\n  @Input() activeColor?: string;\n  @Input() topPadding = '0px';\n\n  isCollapsed = false;\n  isHovered = false;\n\n  private _sidebarStateSubscription: Subscription | undefined;\n\n  constructor( private sidebarStateService: SwuiSidebarService,\n               private sanitizer: DomSanitizer ) {\n    this.collapseOnDimension();\n    this._sidebarStateSubscription = this.sidebarStateService.isCollapsed.subscribe(( val: boolean ) => {\n      this.isCollapsed = val;\n    });\n  }\n\n  @HostBinding('attr.style')\n  public get valueAsStyle(): SafeStyle {\n    return this.sanitizer.bypassSecurityTrustStyle(`--top-padding: ${this.topPadding}`);\n  }\n\n  @HostListener('window:resize') onResize() {\n    this.collapseOnDimension();\n  }\n\n  @HostListener('document:click', ['$event.target'])\n  public onClick( targetElement: HTMLElement ) {\n    if (this.sidebarRef) {\n      const clickedInside = this.sidebarRef.nativeElement.contains(targetElement);\n      if (!clickedInside && window.innerWidth <= 1024) {\n        this.sidebarStateService.isCollapsed.next(true);\n      }\n    }\n  }\n\n  ngAfterViewInit() {\n    this.bindMouseenterEvent();\n    this.bindMouseLeaveEvent();\n  }\n\n  ngOnDestroy() {\n    if (this._sidebarStateSubscription) {\n      this._sidebarStateSubscription.unsubscribe();\n    }\n  }\n\n  toggleSidebar( event: MouseEvent ) {\n    event.preventDefault();\n    this.sidebarStateService.isCollapsed.next(!this.isCollapsed);\n  }\n\n  private bindMouseenterEvent() {\n    if (this.sidebarRef) {\n      this.sidebarRef.nativeElement.addEventListener('mouseenter', () => {\n        if (this.isCollapsed) {\n          this.isHovered = true;\n        }\n      });\n    }\n  }\n\n  private bindMouseLeaveEvent() {\n    if (this.sidebarRef) {\n      this.sidebarRef.nativeElement.addEventListener('mouseleave', () => {\n        this.isHovered = false;\n      });\n    }\n  }\n\n  private collapseOnDimension() {\n    if (window.innerWidth <= 1024) {\n      this.sidebarStateService.isCollapsed.next(true);\n    }\n  }\n\n}\n"], "mappings": ";;;;AAAA,SAAwBA,SAAS,EAAcC,WAAW,EAAEC,YAAY,EAAEC,KAAK,EAAaC,SAAS,QAAQ,eAAe;AAG5H,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3D,SAASC,YAAY,QAAmB,2BAA2B;AAQ5D,IAAMC,oBAAoB,IAAAC,qBAAA,GAA1B,MAAMD,oBAAoB;EAY/BE,YAAqBC,mBAAuC,EACvCC,SAAuB;IADvB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,SAAS,GAATA,SAAS;IARrB,KAAAC,UAAU,GAAG,KAAK;IAE3B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,SAAS,GAAG,KAAK;IAMf,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACN,mBAAmB,CAACG,WAAW,CAACI,SAAS,CAAGC,GAAY,IAAK;MACjG,IAAI,CAACL,WAAW,GAAGK,GAAG;IACxB,CAAC,CAAC;EACJ;MAGWC,YAAYA,CAAA;IACrB,OAAO,IAAI,CAACR,SAAS,CAACS,wBAAwB,CAAC,kBAAkB,IAAI,CAACR,UAAU,EAAE,CAAC;EACrF;EAE+BS,QAAQA,CAAA;IACrC,IAAI,CAACN,mBAAmB,EAAE;EAC5B;EAGOO,OAAOA,CAAEC,aAA0B;IACxC,IAAI,IAAI,CAACC,UAAU,EAAE;MACnB,MAAMC,aAAa,GAAG,IAAI,CAACD,UAAU,CAACE,aAAa,CAACC,QAAQ,CAACJ,aAAa,CAAC;MAC3E,IAAI,CAACE,aAAa,IAAIG,MAAM,CAACC,UAAU,IAAI,IAAI,EAAE;QAC/C,IAAI,CAACnB,mBAAmB,CAACG,WAAW,CAACiB,IAAI,CAAC,IAAI,CAAC;MACjD;IACF;EACF;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAClB,yBAAyB,EAAE;MAClC,IAAI,CAACA,yBAAyB,CAACmB,WAAW,EAAE;IAC9C;EACF;EAEAC,aAAaA,CAAEC,KAAiB;IAC9BA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAAC5B,mBAAmB,CAACG,WAAW,CAACiB,IAAI,CAAC,CAAC,IAAI,CAACjB,WAAW,CAAC;EAC9D;EAEQmB,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACR,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACE,aAAa,CAACa,gBAAgB,CAAC,YAAY,EAAE,MAAK;QAChE,IAAI,IAAI,CAAC1B,WAAW,EAAE;UACpB,IAAI,CAACC,SAAS,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ;EACF;EAEQmB,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACT,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACE,aAAa,CAACa,gBAAgB,CAAC,YAAY,EAAE,MAAK;QAChE,IAAI,CAACzB,SAAS,GAAG,KAAK;MACxB,CAAC,CAAC;IACJ;EACF;EAEQC,mBAAmBA,CAAA;IACzB,IAAIa,MAAM,CAACC,UAAU,IAAI,IAAI,EAAE;MAC7B,IAAI,CAACnB,mBAAmB,CAACG,WAAW,CAACiB,IAAI,CAAC,IAAI,CAAC;IACjD;EACF;;;;;;;UA5EC1B,SAAS;IAAAoC,IAAA,GAAC,SAAS;EAAA;;UAEnBrC;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAeLF,WAAW;IAAAuC,IAAA,GAAC,YAAY;EAAA;;UAKxBtC,YAAY;IAAAsC,IAAA,GAAC,eAAe;EAAA;;UAI5BtC,YAAY;IAAAsC,IAAA,GAAC,gBAAgB,EAAE,CAAC,eAAe,CAAC;EAAA;;AA7BtCjC,oBAAoB,GAAAkC,UAAA,EANhCzC,SAAS,CAAC;EACP0C,QAAQ,EAAE,kBAAkB;EAC5BC,QAAA,EAAAC,oBAA4C;EAE5CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWtC,oBAAoB,CA+EhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}