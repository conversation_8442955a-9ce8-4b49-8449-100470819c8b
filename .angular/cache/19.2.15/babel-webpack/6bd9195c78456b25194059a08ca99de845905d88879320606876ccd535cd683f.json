{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiSelectModule } from '../../swui-select/swui-select.module';\nimport { SwuiSettingsDialogComponent } from './swui-settings-dialog.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatButtonModule } from '@angular/material/button';\nlet SwuiSettingsDialogModule = class SwuiSettingsDialogModule {};\nSwuiSettingsDialogModule = __decorate([NgModule({\n  imports: [CommonModule, ReactiveFormsModule, TranslateModule, MatButtonModule, MatSelectModule, MatDialogModule, SwuiSelectModule],\n  declarations: [SwuiSettingsDialogComponent]\n})], SwuiSettingsDialogModule);\nexport { SwuiSettingsDialogModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "SwuiSelectModule", "SwuiSettingsDialogComponent", "ReactiveFormsModule", "TranslateModule", "MatDialogModule", "MatSelectModule", "MatButtonModule", "SwuiSettingsDialogModule", "imports", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/settings-dialog/swui-settings-dialog.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiSelectModule } from '../../swui-select/swui-select.module';\nimport { SwuiSettingsDialogComponent } from './swui-settings-dialog.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatButtonModule } from '@angular/material/button';\nlet SwuiSettingsDialogModule = class SwuiSettingsDialogModule {\n};\nSwuiSettingsDialogModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            ReactiveFormsModule,\n            TranslateModule,\n            MatButtonModule,\n            MatSelectModule,\n            MatDialogModule,\n            SwuiSelectModule,\n        ],\n        declarations: [\n            SwuiSettingsDialogComponent\n        ],\n    })\n], SwuiSettingsDialogModule);\nexport { SwuiSettingsDialogModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,IAAIC,wBAAwB,GAAG,MAAMA,wBAAwB,CAAC,EAC7D;AACDA,wBAAwB,GAAGV,UAAU,CAAC,CAClCC,QAAQ,CAAC;EACLU,OAAO,EAAE,CACLT,YAAY,EACZG,mBAAmB,EACnBC,eAAe,EACfG,eAAe,EACfD,eAAe,EACfD,eAAe,EACfJ,gBAAgB,CACnB;EACDS,YAAY,EAAE,CACVR,2BAA2B;AAEnC,CAAC,CAAC,CACL,EAAEM,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}