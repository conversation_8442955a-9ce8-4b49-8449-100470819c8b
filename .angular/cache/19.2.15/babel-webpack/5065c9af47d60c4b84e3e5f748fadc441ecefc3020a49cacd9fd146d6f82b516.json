{"ast": null, "code": "var SwHubAuthModule_1;\nimport { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { JWT_OPTIONS, JwtHelperService, JwtInterceptor, JwtModule } from '@auth0/angular-jwt';\nimport { SwHubAuthService } from './sw-hub-auth.service';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { SwHubAuthGuard } from './sw-hub-auth.guard';\nimport { SWUI_HUB_AUTH_CONFIG } from './sw-hub-auth.token';\nexport function jwtOptionsFactory(config, auth) {\n  return {\n    tokenGetter: auth.tokenGetter(),\n    headerName: 'X-ACCESS-TOKEN',\n    authScheme: '',\n    whitelistedDomains: config.whitelistedDomains,\n    blacklistedRoutes: config.blacklistedRoutes\n  };\n}\nlet SwHubAuthModule = SwHubAuthModule_1 = class SwHubAuthModule {\n  static forRoot(config) {\n    return {\n      ngModule: SwHubAuthModule_1,\n      providers: [SwHubAuthService, JwtHelperService, SwHubAuthGuard, {\n        provide: SWUI_HUB_AUTH_CONFIG,\n        useValue: config\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: JwtInterceptor,\n        multi: true\n      }, {\n        provide: JWT_OPTIONS,\n        useFactory: jwtOptionsFactory,\n        deps: [SWUI_HUB_AUTH_CONFIG, SwHubAuthService]\n      }]\n    };\n  }\n};\nSwHubAuthModule = SwHubAuthModule_1 = __decorate([NgModule({\n  imports: [CommonModule, JwtModule],\n  providers: [SwHubAuthService]\n})], SwHubAuthModule);\nexport { SwHubAuthModule };", "map": {"version": 3, "names": ["CommonModule", "NgModule", "JWT_OPTIONS", "JwtHelperService", "JwtInterceptor", "JwtModule", "SwHubAuthService", "HTTP_INTERCEPTORS", "SwHubAuthGuard", "SWUI_HUB_AUTH_CONFIG", "jwtOptionsFactory", "config", "auth", "tokenGetter", "headerName", "authScheme", "whitelistedDomains", "blacklistedRoutes", "SwHubAuthModule", "SwHubAuthModule_1", "forRoot", "ngModule", "providers", "provide", "useValue", "useClass", "multi", "useFactory", "deps", "__decorate", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-auth/sw-hub-auth.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ModuleWithProviders, NgModule } from '@angular/core';\nimport { JWT_OPTIONS, JwtHelperService, JwtInterceptor, JwtModule } from '@auth0/angular-jwt';\nimport { SwHubAuthService } from './sw-hub-auth.service';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { SwHubAuthGuard } from './sw-hub-auth.guard';\nimport { SWUI_HUB_AUTH_CONFIG } from './sw-hub-auth.token';\n\nexport interface SwHubAuthModuleConfig {\n  whitelistedDomains?: Array<string | RegExp>;\n  blacklistedRoutes?: Array<string | RegExp>;\n}\n\nexport function jwtOptionsFactory( config: SwHubAuthModuleConfig, auth: SwHubAuthService ) {\n  return {\n    tokenGetter: auth.tokenGetter(),\n    headerName: 'X-ACCESS-TOKEN',\n    authScheme: '',\n    whitelistedDomains: config.whitelistedDomains,\n    blacklistedRoutes: config.blacklistedRoutes\n  };\n}\n\n@NgModule({\n  imports: [\n    CommonModule,\n    JwtModule,\n  ],\n  providers: [\n    SwHubAuthService,\n  ]\n})\nexport class SwHubAuthModule {\n\n  static forRoot( config: SwHubAuthModuleConfig ): ModuleWithProviders<SwHubAuthModule> {\n    return {\n      ngModule: SwHubAuthModule,\n      providers: [\n        SwHubAuthService,\n        JwtHelperService,\n        SwHubAuthGuard,\n        {\n          provide: SWUI_HUB_AUTH_CONFIG,\n          useValue: config\n        },\n        {\n          provide: HTTP_INTERCEPTORS,\n          useClass: JwtInterceptor,\n          multi: true\n        },\n        {\n          provide: JWT_OPTIONS,\n          useFactory: jwtOptionsFactory,\n          deps: [SWUI_HUB_AUTH_CONFIG, SwHubAuthService]\n        }\n      ]\n    };\n  }\n}\n"], "mappings": ";;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAA8BC,QAAQ,QAAQ,eAAe;AAC7D,SAASC,WAAW,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,SAAS,QAAQ,oBAAoB;AAC7F,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,oBAAoB,QAAQ,qBAAqB;AAO1D,OAAM,SAAUC,iBAAiBA,CAAEC,MAA6B,EAAEC,IAAsB;EACtF,OAAO;IACLC,WAAW,EAAED,IAAI,CAACC,WAAW,EAAE;IAC/BC,UAAU,EAAE,gBAAgB;IAC5BC,UAAU,EAAE,EAAE;IACdC,kBAAkB,EAAEL,MAAM,CAACK,kBAAkB;IAC7CC,iBAAiB,EAAEN,MAAM,CAACM;GAC3B;AACH;AAWO,IAAMC,eAAe,GAAAC,iBAAA,GAArB,MAAMD,eAAe;EAE1B,OAAOE,OAAOA,CAAET,MAA6B;IAC3C,OAAO;MACLU,QAAQ,EAAEF,iBAAe;MACzBG,SAAS,EAAE,CACThB,gBAAgB,EAChBH,gBAAgB,EAChBK,cAAc,EACd;QACEe,OAAO,EAAEd,oBAAoB;QAC7Be,QAAQ,EAAEb;OACX,EACD;QACEY,OAAO,EAAEhB,iBAAiB;QAC1BkB,QAAQ,EAAErB,cAAc;QACxBsB,KAAK,EAAE;OACR,EACD;QACEH,OAAO,EAAErB,WAAW;QACpByB,UAAU,EAAEjB,iBAAiB;QAC7BkB,IAAI,EAAE,CAACnB,oBAAoB,EAAEH,gBAAgB;OAC9C;KAEJ;EACH;CACD;AA1BYY,eAAe,GAAAC,iBAAA,GAAAU,UAAA,EAT3B5B,QAAQ,CAAC;EACR6B,OAAO,EAAE,CACP9B,YAAY,EACZK,SAAS,CACV;EACDiB,SAAS,EAAE,CACThB,gBAAgB;CAEnB,CAAC,C,EACWY,eAAe,CA0B3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}