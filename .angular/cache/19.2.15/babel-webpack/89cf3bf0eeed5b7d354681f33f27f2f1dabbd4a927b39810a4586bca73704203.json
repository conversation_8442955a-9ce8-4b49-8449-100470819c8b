{"ast": null, "code": "import { __generator } from \"tslib\";\nimport { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nimport { defer } from './defer';\nimport { scheduleIterable } from '../scheduled/scheduleIterable';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n  var _a, _b;\n  var resultSelector;\n  var initialState;\n  if (arguments.length === 1) {\n    _a = initialStateOrOptions, initialState = _a.initialState, condition = _a.condition, iterate = _a.iterate, _b = _a.resultSelector, resultSelector = _b === void 0 ? identity : _b, scheduler = _a.scheduler;\n  } else {\n    initialState = initialStateOrOptions;\n    if (!resultSelectorOrScheduler || isScheduler(resultSelectorOrScheduler)) {\n      resultSelector = identity;\n      scheduler = resultSelectorOrScheduler;\n    } else {\n      resultSelector = resultSelectorOrScheduler;\n    }\n  }\n  function gen() {\n    var state;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          state = initialState;\n          _a.label = 1;\n        case 1:\n          if (!(!condition || condition(state))) return [3, 4];\n          return [4, resultSelector(state)];\n        case 2:\n          _a.sent();\n          _a.label = 3;\n        case 3:\n          state = iterate(state);\n          return [3, 1];\n        case 4:\n          return [2];\n      }\n    });\n  }\n  return defer(scheduler ? function () {\n    return scheduleIterable(gen(), scheduler);\n  } : gen);\n}\n//# sourceMappingURL=generate.js.map", "map": {"version": 3, "names": ["__generator", "identity", "isScheduler", "defer", "scheduleIterable", "generate", "initialStateOrOptions", "condition", "iterate", "resultSelectorOrScheduler", "scheduler", "_a", "_b", "resultSelector", "initialState", "arguments", "length", "gen", "state", "label", "sent"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/generate.js"], "sourcesContent": ["import { __generator } from \"tslib\";\nimport { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nimport { defer } from './defer';\nimport { scheduleIterable } from '../scheduled/scheduleIterable';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n    var _a, _b;\n    var resultSelector;\n    var initialState;\n    if (arguments.length === 1) {\n        (_a = initialStateOrOptions, initialState = _a.initialState, condition = _a.condition, iterate = _a.iterate, _b = _a.resultSelector, resultSelector = _b === void 0 ? identity : _b, scheduler = _a.scheduler);\n    }\n    else {\n        initialState = initialStateOrOptions;\n        if (!resultSelectorOrScheduler || isScheduler(resultSelectorOrScheduler)) {\n            resultSelector = identity;\n            scheduler = resultSelectorOrScheduler;\n        }\n        else {\n            resultSelector = resultSelectorOrScheduler;\n        }\n    }\n    function gen() {\n        var state;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    state = initialState;\n                    _a.label = 1;\n                case 1:\n                    if (!(!condition || condition(state))) return [3, 4];\n                    return [4, resultSelector(state)];\n                case 2:\n                    _a.sent();\n                    _a.label = 3;\n                case 3:\n                    state = iterate(state);\n                    return [3, 1];\n                case 4: return [2];\n            }\n        });\n    }\n    return defer((scheduler\n        ?\n            function () { return scheduleIterable(gen(), scheduler); }\n        :\n            gen));\n}\n//# sourceMappingURL=generate.js.map"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,OAAO,SAASC,QAAQA,CAACC,qBAAqB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,yBAAyB,EAAEC,SAAS,EAAE;EACtG,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIC,cAAc;EAClB,IAAIC,YAAY;EAChB,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IACvBL,EAAE,GAAGL,qBAAqB,EAAEQ,YAAY,GAAGH,EAAE,CAACG,YAAY,EAAEP,SAAS,GAAGI,EAAE,CAACJ,SAAS,EAAEC,OAAO,GAAGG,EAAE,CAACH,OAAO,EAAEI,EAAE,GAAGD,EAAE,CAACE,cAAc,EAAEA,cAAc,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGX,QAAQ,GAAGW,EAAE,EAAEF,SAAS,GAAGC,EAAE,CAACD,SAAS;EACjN,CAAC,MACI;IACDI,YAAY,GAAGR,qBAAqB;IACpC,IAAI,CAACG,yBAAyB,IAAIP,WAAW,CAACO,yBAAyB,CAAC,EAAE;MACtEI,cAAc,GAAGZ,QAAQ;MACzBS,SAAS,GAAGD,yBAAyB;IACzC,CAAC,MACI;MACDI,cAAc,GAAGJ,yBAAyB;IAC9C;EACJ;EACA,SAASQ,GAAGA,CAAA,EAAG;IACX,IAAIC,KAAK;IACT,OAAOlB,WAAW,CAAC,IAAI,EAAE,UAAUW,EAAE,EAAE;MACnC,QAAQA,EAAE,CAACQ,KAAK;QACZ,KAAK,CAAC;UACFD,KAAK,GAAGJ,YAAY;UACpBH,EAAE,CAACQ,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UACF,IAAI,EAAE,CAACZ,SAAS,IAAIA,SAAS,CAACW,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;UACpD,OAAO,CAAC,CAAC,EAAEL,cAAc,CAACK,KAAK,CAAC,CAAC;QACrC,KAAK,CAAC;UACFP,EAAE,CAACS,IAAI,CAAC,CAAC;UACTT,EAAE,CAACQ,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UACFD,KAAK,GAAGV,OAAO,CAACU,KAAK,CAAC;UACtB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC;EACN;EACA,OAAOf,KAAK,CAAEO,SAAS,GAEf,YAAY;IAAE,OAAON,gBAAgB,CAACa,GAAG,CAAC,CAAC,EAAEP,SAAS,CAAC;EAAE,CAAC,GAE1DO,GAAI,CAAC;AACjB;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}