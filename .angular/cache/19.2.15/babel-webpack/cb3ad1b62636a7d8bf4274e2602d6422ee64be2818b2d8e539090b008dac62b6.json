{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiDateRangeComponent } from './swui-date-range.component';\nimport { SwuiDateTimeChooserModule } from '../swui-date-time-chooser/swui-date-time-chooser.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const SWUI_DATE_RANGE = [ReactiveFormsModule, MatInputModule, MatMenuModule, MatTabsModule, MatButtonModule, MatRippleModule, SwuiDateTimeChooserModule];\nlet SwuiDateRangeModule = class SwuiDateRangeModule {};\nSwuiDateRangeModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule.forChild(), ...SWUI_DATE_RANGE],\n  declarations: [SwuiDateRangeComponent],\n  exports: [SwuiDateRangeComponent]\n})], SwuiDateRangeModule);\nexport { SwuiDateRangeModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "ReactiveFormsModule", "TranslateModule", "SwuiDateRangeComponent", "SwuiDateTimeChooserModule", "MatMenuModule", "MatInputModule", "MatTabsModule", "MatButtonModule", "MatRippleModule", "SWUI_DATE_RANGE", "SwuiDateRangeModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-range/swui-date-range.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiDateRangeComponent } from './swui-date-range.component';\nimport { SwuiDateTimeChooserModule } from '../swui-date-time-chooser/swui-date-time-chooser.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const SWUI_DATE_RANGE = [\n    ReactiveFormsModule,\n    MatInputModule,\n    MatMenuModule,\n    MatTabsModule,\n    MatButtonModule,\n    MatRippleModule,\n    SwuiDateTimeChooserModule,\n];\nlet SwuiDateRangeModule = class SwuiDateRangeModule {\n};\nSwuiDateRangeModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule.forChild(),\n            ...SWUI_DATE_RANGE,\n        ],\n        declarations: [\n            SwuiDateRangeComponent,\n        ],\n        exports: [\n            SwuiDateRangeComponent,\n        ]\n    })\n], SwuiDateRangeModule);\nexport { SwuiDateRangeModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,yBAAyB,QAAQ,yDAAyD;AACnG,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,MAAMC,eAAe,GAAG,CAC3BT,mBAAmB,EACnBK,cAAc,EACdD,aAAa,EACbE,aAAa,EACbC,eAAe,EACfC,eAAe,EACfL,yBAAyB,CAC5B;AACD,IAAIO,mBAAmB,GAAG,MAAMA,mBAAmB,CAAC,EACnD;AACDA,mBAAmB,GAAGb,UAAU,CAAC,CAC7BC,QAAQ,CAAC;EACLa,OAAO,EAAE,CACLZ,YAAY,EACZE,eAAe,CAACW,QAAQ,CAAC,CAAC,EAC1B,GAAGH,eAAe,CACrB;EACDI,YAAY,EAAE,CACVX,sBAAsB,CACzB;EACDY,OAAO,EAAE,CACLZ,sBAAsB;AAE9B,CAAC,CAAC,CACL,EAAEQ,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}