{"ast": null, "code": "var SwuiNotificationsModule_1;\nimport { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { SwuiNotificationsService } from './swui-notifications.service';\nimport { SwuiSnackbarComponent } from './swui-snackbar/swui-snackbar.component';\nimport { MAT_SNACK_BAR_DEFAULT_OPTIONS, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nexport const notificationsMatModules = [MatButtonModule, MatIconModule, MatSnackBarModule];\nlet SwuiNotificationsModule = SwuiNotificationsModule_1 = class SwuiNotificationsModule {\n  static forRoot() {\n    return {\n      ngModule: SwuiNotificationsModule_1,\n      providers: [SwuiNotificationsService, {\n        provide: MAT_SNACK_BAR_DEFAULT_OPTIONS,\n        useValue: {\n          duration: 2500\n        }\n      }]\n    };\n  }\n};\nSwuiNotificationsModule = SwuiNotificationsModule_1 = __decorate([NgModule({\n  imports: [CommonModule, ...notificationsMatModules],\n  declarations: [SwuiSnackbarComponent],\n  exports: [...notificationsMatModules]\n})], SwuiNotificationsModule);\nexport { SwuiNotificationsModule };", "map": {"version": 3, "names": ["SwuiNotificationsModule_1", "__decorate", "CommonModule", "NgModule", "SwuiNotificationsService", "SwuiSnackbarComponent", "MAT_SNACK_BAR_DEFAULT_OPTIONS", "MatSnackBarModule", "MatIconModule", "MatButtonModule", "notificationsMatModules", "SwuiNotificationsModule", "forRoot", "ngModule", "providers", "provide", "useValue", "duration", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-notifications/swui-notifications.module.ts"], "sourcesContent": ["var SwuiNotificationsModule_1;\nimport { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { SwuiNotificationsService } from './swui-notifications.service';\nimport { SwuiSnackbarComponent } from './swui-snackbar/swui-snackbar.component';\nimport { MAT_SNACK_BAR_DEFAULT_OPTIONS, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nexport const notificationsMatModules = [\n    MatButtonModule,\n    MatIconModule,\n    MatSnackBarModule,\n];\nlet SwuiNotificationsModule = SwuiNotificationsModule_1 = class SwuiNotificationsModule {\n    static forRoot() {\n        return {\n            ngModule: SwuiNotificationsModule_1,\n            providers: [\n                SwuiNotificationsService,\n                { provide: MAT_SNACK_BAR_DEFAULT_OPTIONS, useValue: { duration: 2500 } }\n            ]\n        };\n    }\n};\nSwuiNotificationsModule = SwuiNotificationsModule_1 = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            ...notificationsMatModules,\n        ],\n        declarations: [\n            SwuiSnackbarComponent,\n        ],\n        exports: [\n            ...notificationsMatModules,\n        ],\n    })\n], SwuiNotificationsModule);\nexport { SwuiNotificationsModule };\n"], "mappings": "AAAA,IAAIA,yBAAyB;AAC7B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,6BAA6B,EAAEC,iBAAiB,QAAQ,6BAA6B;AAC9F,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,uBAAuB,GAAG,CACnCD,eAAe,EACfD,aAAa,EACbD,iBAAiB,CACpB;AACD,IAAII,uBAAuB,GAAGX,yBAAyB,GAAG,MAAMW,uBAAuB,CAAC;EACpF,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEb,yBAAyB;MACnCc,SAAS,EAAE,CACPV,wBAAwB,EACxB;QAAEW,OAAO,EAAET,6BAA6B;QAAEU,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAK;MAAE,CAAC;IAEhF,CAAC;EACL;AACJ,CAAC;AACDN,uBAAuB,GAAGX,yBAAyB,GAAGC,UAAU,CAAC,CAC7DE,QAAQ,CAAC;EACLe,OAAO,EAAE,CACLhB,YAAY,EACZ,GAAGQ,uBAAuB,CAC7B;EACDS,YAAY,EAAE,CACVd,qBAAqB,CACxB;EACDe,OAAO,EAAE,CACL,GAAGV,uBAAuB;AAElC,CAAC,CAAC,CACL,EAAEC,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}