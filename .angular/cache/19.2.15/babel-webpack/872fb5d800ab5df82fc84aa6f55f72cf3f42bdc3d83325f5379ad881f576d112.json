{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiDatePickerComponent } from './swui-date-picker.component';\nimport { SwuiDateTimeChooserModule } from '../swui-date-time-chooser/swui-date-time-chooser.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const DATE_PICKER_MODULES = [SwuiDateTimeChooserModule, ReactiveFormsModule, MatMenuModule, MatInputModule, MatButtonModule, MatRippleModule];\nlet SwuiDatePickerModule = class SwuiDatePickerModule {};\nSwuiDatePickerModule = __decorate([NgModule({\n  declarations: [SwuiDatePickerComponent],\n  imports: [CommonModule, TranslateModule.forChild(), ...DATE_PICKER_MODULES],\n  exports: [SwuiDatePickerComponent]\n})], SwuiDatePickerModule);\nexport { SwuiDatePickerModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "TranslateModule", "ReactiveFormsModule", "SwuiDatePickerComponent", "SwuiDateTimeChooserModule", "MatMenuModule", "MatInputModule", "MatButtonModule", "MatRippleModule", "DATE_PICKER_MODULES", "SwuiDatePickerModule", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-picker/swui-date-picker.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiDatePickerComponent } from './swui-date-picker.component';\nimport { SwuiDateTimeChooserModule } from '../swui-date-time-chooser/swui-date-time-chooser.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const DATE_PICKER_MODULES = [\n    SwuiDateTimeChooserModule,\n    ReactiveFormsModule,\n    MatMenuModule,\n    MatInputModule,\n    MatButtonModule,\n    MatRippleModule,\n];\nlet SwuiDatePickerModule = class SwuiDatePickerModule {\n};\nSwuiDatePickerModule = __decorate([\n    NgModule({\n        declarations: [SwuiDatePickerComponent],\n        imports: [\n            CommonModule,\n            TranslateModule.forChild(),\n            ...DATE_PICKER_MODULES,\n        ],\n        exports: [\n            SwuiDatePickerComponent,\n        ]\n    })\n], SwuiDatePickerModule);\nexport { SwuiDatePickerModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,yBAAyB,QAAQ,yDAAyD;AACnG,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,MAAMC,mBAAmB,GAAG,CAC/BL,yBAAyB,EACzBF,mBAAmB,EACnBG,aAAa,EACbC,cAAc,EACdC,eAAe,EACfC,eAAe,CAClB;AACD,IAAIE,oBAAoB,GAAG,MAAMA,oBAAoB,CAAC,EACrD;AACDA,oBAAoB,GAAGZ,UAAU,CAAC,CAC9BC,QAAQ,CAAC;EACLY,YAAY,EAAE,CAACR,uBAAuB,CAAC;EACvCS,OAAO,EAAE,CACLZ,YAAY,EACZC,eAAe,CAACY,QAAQ,CAAC,CAAC,EAC1B,GAAGJ,mBAAmB,CACzB;EACDK,OAAO,EAAE,CACLX,uBAAuB;AAE/B,CAAC,CAAC,CACL,EAAEO,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}