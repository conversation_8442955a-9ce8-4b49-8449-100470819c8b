{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiColumnsManagementComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./columns-management.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./columns-management.component.scss?ngResource\";\nimport { Component, EventEmitter, Inject, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { BehaviorSubject, of, Subject } from 'rxjs';\nimport { switchMap, take, takeUntil } from 'rxjs/operators';\nimport { ColumnsManagementDataProvider, LocalStorageColumnsDataProvider } from './columns-management.model';\nfunction buildSelectOptions(schema, columnsSet) {\n  return (schema || []).filter(({\n    field\n  }) => field !== 'row-actions-column' && field !== 'bulk-actions-column').map(({\n    field,\n    title,\n    isListVisible\n  }, index) => {\n    const data = (columnsSet || {})[field];\n    return {\n      id: field,\n      text: title || '',\n      index: data ? data.index : index,\n      selected: data ? data.isListVisible : isListVisible === undefined ? true : isListVisible\n    };\n  }).sort(({\n    index: a\n  }, {\n    index: b\n  }) => a - b);\n}\nlet SwuiColumnsManagementComponent = (_SwuiColumnsManagementComponent = class SwuiColumnsManagementComponent {\n  set gridId(value) {\n    this.gridId$.next(value);\n  }\n  set schema(value) {\n    this._schema = value;\n    this.setSelectOptions(value, this.columnsSet);\n  }\n  constructor(provider) {\n    this.columnsChange = new EventEmitter();\n    this.data = [];\n    this.selected = [];\n    this.columnsSet = {};\n    this.selectOptions = [];\n    this.gridId$ = new BehaviorSubject(undefined);\n    this.destroyed = new Subject();\n    this.dataProvider = provider || new LocalStorageColumnsDataProvider();\n  }\n  ngOnInit() {\n    this.gridId$.pipe(switchMap(gridId => gridId ? this.dataProvider.getColumns(gridId) : of({})), takeUntil(this.destroyed)).subscribe(columnsSet => {\n      this.setColumnsSet(columnsSet);\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed.next(undefined);\n    this.destroyed.complete();\n  }\n  onApply(values) {\n    if (this.menuRef) {\n      this.menuRef.closeMenu();\n    }\n    const gridId = this.gridId$.value;\n    if (gridId) {\n      const selectedData = new Set(values);\n      const data = this.selectOptions.reduce((result, {\n        id,\n        index\n      }) => _objectSpread(_objectSpread({}, result), {}, {\n        [id]: {\n          isListVisible: selectedData.has(id),\n          index\n        }\n      }), {});\n      this.dataProvider.saveColumns(gridId, data).pipe(take(1)).subscribe(columnsSet => {\n        this.setColumnsSet(columnsSet);\n      });\n    }\n  }\n  onCancel() {\n    if (this.menuRef) {\n      this.menuRef.closeMenu();\n    }\n  }\n  setColumnsSet(value) {\n    this.columnsSet = value;\n    this.setSelectOptions(this._schema, value);\n    this.emit();\n  }\n  setSelectOptions(schema, value) {\n    this.selectOptions = buildSelectOptions(schema, value);\n    this.data = this.selectOptions.map(({\n      id,\n      text\n    }) => ({\n      id,\n      text\n    }));\n    this.selected = this.selectOptions.filter(({\n      selected\n    }) => selected).map(({\n      id\n    }) => id);\n  }\n  emit() {\n    if (this._schema) {\n      this.columnsChange.emit(this._schema.reduce((result, {\n        field,\n        isListVisible\n      }, index) => {\n        const data = this.columnsSet[field];\n        return _objectSpread(_objectSpread({}, result), {}, {\n          [field]: {\n            index: data ? data.index : index,\n            isListVisible: data ? data.isListVisible : isListVisible\n          }\n        });\n      }, {}));\n    }\n  }\n}, _SwuiColumnsManagementComponent.ctorParameters = () => [{\n  type: ColumnsManagementDataProvider,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [ColumnsManagementDataProvider]\n  }]\n}], _SwuiColumnsManagementComponent.propDecorators = {\n  gridId: [{\n    type: Input\n  }],\n  schema: [{\n    type: Input\n  }],\n  columnsChange: [{\n    type: Output\n  }],\n  menuRef: [{\n    type: ViewChild,\n    args: [MatMenuTrigger, {\n      static: true\n    }]\n  }]\n}, _SwuiColumnsManagementComponent);\nSwuiColumnsManagementComponent = __decorate([Component({\n  selector: 'lib-swui-columns-management',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiColumnsManagementComponent);\nexport { SwuiColumnsManagementComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Inject", "Input", "Optional", "Output", "ViewChild", "MatMenuTrigger", "BehaviorSubject", "of", "Subject", "switchMap", "take", "takeUntil", "ColumnsManagementDataProvider", "LocalStorageColumnsDataProvider", "buildSelectOptions", "schema", "columnsSet", "filter", "field", "map", "title", "isListVisible", "index", "data", "id", "text", "selected", "undefined", "sort", "a", "b", "SwuiColumnsManagementComponent", "_SwuiColumnsManagementComponent", "gridId", "value", "gridId$", "next", "_schema", "setSelectOptions", "constructor", "provider", "columnsChange", "selectOptions", "destroyed", "dataProvider", "ngOnInit", "pipe", "getColumns", "subscribe", "setColumnsSet", "ngOnDestroy", "complete", "onApply", "values", "menuRef", "closeMenu", "selectedData", "Set", "reduce", "result", "_objectSpread", "has", "saveColumns", "onCancel", "emit", "type", "args", "static", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/columns-management/columns-management.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Inject, Input, OnDestroy, OnInit, Optional, Output, ViewChild } from '@angular/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { BehaviorSubject, of, Subject } from 'rxjs';\nimport { switchMap, take, takeUntil } from 'rxjs/operators';\nimport { SwuiSelectOption } from '../../swui-select/swui-select.interface';\nimport { ColumnsManagementDataProvider, LocalStorageColumnsDataProvider, SchemaVisibilityData } from './columns-management.model';\nimport { SwuiGridSchemaField } from '../swui-grid.model';\n\n\ntype SelectOption = SwuiSelectOption & { index: number; selected: boolean; };\n\nfunction buildSelectOptions( schema: SwuiGridSchemaField[] | undefined, columnsSet: SchemaVisibilityData ): SelectOption[] {\n  return (schema || [])\n    .filter(( { field } ) => field !== 'row-actions-column' && field !== 'bulk-actions-column')\n    .map<SelectOption>(( { field, title, isListVisible }, index ) => {\n      const data = (columnsSet || {})[field];\n      return {\n        id: field,\n        text: title || '',\n        index: data ? data.index : index,\n        selected: data ? data.isListVisible : isListVisible === undefined ? true : isListVisible\n      };\n    })\n    .sort(( { index: a }, { index: b } ) => a - b);\n}\n\n@Component({\n    selector: 'lib-swui-columns-management',\n    templateUrl: './columns-management.component.html',\n    styleUrls: ['./columns-management.component.scss'],\n    standalone: false\n})\nexport class SwuiColumnsManagementComponent implements OnInit, OnDestroy {\n\n  @Input() set gridId( value: string | undefined ) {\n    this.gridId$.next(value);\n  }\n\n  @Input() set schema( value: SwuiGridSchemaField[] | undefined ) {\n    this._schema = value;\n    this.setSelectOptions(value, this.columnsSet);\n  }\n\n  @Output() columnsChange = new EventEmitter<SchemaVisibilityData>();\n\n  @ViewChild(MatMenuTrigger, { static: true }) menuRef?: MatMenuTrigger;\n\n  data: SwuiSelectOption[] = [];\n  selected: string[] = [];\n\n  private _schema?: SwuiGridSchemaField[];\n  private columnsSet: SchemaVisibilityData = {};\n  private selectOptions: SelectOption[] = [];\n\n  private readonly gridId$ = new BehaviorSubject<string | undefined>(undefined);\n  private readonly destroyed = new Subject<void>();\n  private readonly dataProvider;\n\n  constructor( @Optional() @Inject(ColumnsManagementDataProvider) provider: ColumnsManagementDataProvider ) {\n    this.dataProvider = provider || new LocalStorageColumnsDataProvider();\n  }\n\n  ngOnInit(): void {\n    this.gridId$.pipe(\n      switchMap(gridId => gridId ? this.dataProvider.getColumns(gridId) : of({})),\n      takeUntil(this.destroyed)\n    ).subscribe(columnsSet => {\n      this.setColumnsSet(columnsSet);\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed.next(undefined);\n    this.destroyed.complete();\n  }\n\n  onApply( values: string[] ) {\n    if (this.menuRef) {\n      this.menuRef.closeMenu();\n    }\n    const gridId = this.gridId$.value;\n    if (gridId) {\n      const selectedData = new Set(values);\n      const data = this.selectOptions.reduce<SchemaVisibilityData>(( result, { id, index } ) => ({\n        ...result,\n        [id]: {\n          isListVisible: selectedData.has(id),\n          index\n        }\n      }), {});\n      this.dataProvider.saveColumns(gridId, data).pipe(\n        take(1)\n      ).subscribe(columnsSet => {\n        this.setColumnsSet(columnsSet);\n      });\n    }\n  }\n\n  onCancel() {\n    if (this.menuRef) {\n      this.menuRef.closeMenu();\n    }\n  }\n\n  private setColumnsSet( value: SchemaVisibilityData ): void {\n    this.columnsSet = value;\n    this.setSelectOptions(this._schema, value);\n    this.emit();\n  }\n\n  private setSelectOptions( schema: SwuiGridSchemaField[] | undefined, value: SchemaVisibilityData ): void {\n    this.selectOptions = buildSelectOptions(schema, value);\n    this.data = this.selectOptions.map<SwuiSelectOption>(( { id, text } ) => ({ id, text }));\n    this.selected = this.selectOptions\n      .filter(( { selected } ) => selected)\n      .map<string>(( { id } ) => id);\n  }\n\n  private emit(): void {\n    if (this._schema) {\n      this.columnsChange.emit(this._schema.reduce(( result, { field, isListVisible }, index ) => {\n        const data = this.columnsSet[field];\n        return ({\n          ...result,\n          [field]: {\n            index: data ? data.index : index,\n            isListVisible: data ? data.isListVisible : isListVisible\n          }\n        });\n      }, {}));\n    }\n  }\n}\n"], "mappings": ";;;;;AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,KAAK,EAAqBC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACtH,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,eAAe,EAAEC,EAAE,EAAEC,OAAO,QAAQ,MAAM;AACnD,SAASC,SAAS,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAE3D,SAASC,6BAA6B,EAAEC,+BAA+B,QAA8B,4BAA4B;AAMjI,SAASC,kBAAkBA,CAAEC,MAAyC,EAAEC,UAAgC;EACtG,OAAO,CAACD,MAAM,IAAI,EAAE,EACjBE,MAAM,CAAC,CAAE;IAAEC;EAAK,CAAE,KAAMA,KAAK,KAAK,oBAAoB,IAAIA,KAAK,KAAK,qBAAqB,CAAC,CAC1FC,GAAG,CAAe,CAAE;IAAED,KAAK;IAAEE,KAAK;IAAEC;EAAa,CAAE,EAAEC,KAAK,KAAK;IAC9D,MAAMC,IAAI,GAAG,CAACP,UAAU,IAAI,EAAE,EAAEE,KAAK,CAAC;IACtC,OAAO;MACLM,EAAE,EAAEN,KAAK;MACTO,IAAI,EAAEL,KAAK,IAAI,EAAE;MACjBE,KAAK,EAAEC,IAAI,GAAGA,IAAI,CAACD,KAAK,GAAGA,KAAK;MAChCI,QAAQ,EAAEH,IAAI,GAAGA,IAAI,CAACF,aAAa,GAAGA,aAAa,KAAKM,SAAS,GAAG,IAAI,GAAGN;KAC5E;EACH,CAAC,CAAC,CACDO,IAAI,CAAC,CAAE;IAAEN,KAAK,EAAEO;EAAC,CAAE,EAAE;IAAEP,KAAK,EAAEQ;EAAC,CAAE,KAAMD,CAAC,GAAGC,CAAC,CAAC;AAClD;AAQO,IAAMC,8BAA8B,IAAAC,+BAAA,GAApC,MAAMD,8BAA8B;MAE5BE,MAAMA,CAAEC,KAAyB;IAC5C,IAAI,CAACC,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;EAC1B;MAEanB,MAAMA,CAAEmB,KAAwC;IAC3D,IAAI,CAACG,OAAO,GAAGH,KAAK;IACpB,IAAI,CAACI,gBAAgB,CAACJ,KAAK,EAAE,IAAI,CAAClB,UAAU,CAAC;EAC/C;EAiBAuB,YAAgEC,QAAuC;IAf7F,KAAAC,aAAa,GAAG,IAAI1C,YAAY,EAAwB;IAIlE,KAAAwB,IAAI,GAAuB,EAAE;IAC7B,KAAAG,QAAQ,GAAa,EAAE;IAGf,KAAAV,UAAU,GAAyB,EAAE;IACrC,KAAA0B,aAAa,GAAmB,EAAE;IAEzB,KAAAP,OAAO,GAAG,IAAI7B,eAAe,CAAqBqB,SAAS,CAAC;IAC5D,KAAAgB,SAAS,GAAG,IAAInC,OAAO,EAAQ;IAI9C,IAAI,CAACoC,YAAY,GAAGJ,QAAQ,IAAI,IAAI3B,+BAA+B,EAAE;EACvE;EAEAgC,QAAQA,CAAA;IACN,IAAI,CAACV,OAAO,CAACW,IAAI,CACfrC,SAAS,CAACwB,MAAM,IAAIA,MAAM,GAAG,IAAI,CAACW,YAAY,CAACG,UAAU,CAACd,MAAM,CAAC,GAAG1B,EAAE,CAAC,EAAE,CAAC,CAAC,EAC3EI,SAAS,CAAC,IAAI,CAACgC,SAAS,CAAC,CAC1B,CAACK,SAAS,CAAChC,UAAU,IAAG;MACvB,IAAI,CAACiC,aAAa,CAACjC,UAAU,CAAC;IAChC,CAAC,CAAC;EACJ;EAEAkC,WAAWA,CAAA;IACT,IAAI,CAACP,SAAS,CAACP,IAAI,CAACT,SAAS,CAAC;IAC9B,IAAI,CAACgB,SAAS,CAACQ,QAAQ,EAAE;EAC3B;EAEAC,OAAOA,CAAEC,MAAgB;IACvB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE;IAC1B;IACA,MAAMtB,MAAM,GAAG,IAAI,CAACE,OAAO,CAACD,KAAK;IACjC,IAAID,MAAM,EAAE;MACV,MAAMuB,YAAY,GAAG,IAAIC,GAAG,CAACJ,MAAM,CAAC;MACpC,MAAM9B,IAAI,GAAG,IAAI,CAACmB,aAAa,CAACgB,MAAM,CAAuB,CAAEC,MAAM,EAAE;QAAEnC,EAAE;QAAEF;MAAK,CAAE,KAAAsC,aAAA,CAAAA,aAAA,KAC/ED,MAAM;QACT,CAACnC,EAAE,GAAG;UACJH,aAAa,EAAEmC,YAAY,CAACK,GAAG,CAACrC,EAAE,CAAC;UACnCF;;MACD,EACD,EAAE,EAAE,CAAC;MACP,IAAI,CAACsB,YAAY,CAACkB,WAAW,CAAC7B,MAAM,EAAEV,IAAI,CAAC,CAACuB,IAAI,CAC9CpC,IAAI,CAAC,CAAC,CAAC,CACR,CAACsC,SAAS,CAAChC,UAAU,IAAG;QACvB,IAAI,CAACiC,aAAa,CAACjC,UAAU,CAAC;MAChC,CAAC,CAAC;IACJ;EACF;EAEA+C,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE;IAC1B;EACF;EAEQN,aAAaA,CAAEf,KAA2B;IAChD,IAAI,CAAClB,UAAU,GAAGkB,KAAK;IACvB,IAAI,CAACI,gBAAgB,CAAC,IAAI,CAACD,OAAO,EAAEH,KAAK,CAAC;IAC1C,IAAI,CAAC8B,IAAI,EAAE;EACb;EAEQ1B,gBAAgBA,CAAEvB,MAAyC,EAAEmB,KAA2B;IAC9F,IAAI,CAACQ,aAAa,GAAG5B,kBAAkB,CAACC,MAAM,EAAEmB,KAAK,CAAC;IACtD,IAAI,CAACX,IAAI,GAAG,IAAI,CAACmB,aAAa,CAACvB,GAAG,CAAmB,CAAE;MAAEK,EAAE;MAAEC;IAAI,CAAE,MAAO;MAAED,EAAE;MAAEC;IAAI,CAAE,CAAC,CAAC;IACxF,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACgB,aAAa,CAC/BzB,MAAM,CAAC,CAAE;MAAES;IAAQ,CAAE,KAAMA,QAAQ,CAAC,CACpCP,GAAG,CAAS,CAAE;MAAEK;IAAE,CAAE,KAAMA,EAAE,CAAC;EAClC;EAEQwC,IAAIA,CAAA;IACV,IAAI,IAAI,CAAC3B,OAAO,EAAE;MAChB,IAAI,CAACI,aAAa,CAACuB,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAACqB,MAAM,CAAC,CAAEC,MAAM,EAAE;QAAEzC,KAAK;QAAEG;MAAa,CAAE,EAAEC,KAAK,KAAK;QACxF,MAAMC,IAAI,GAAG,IAAI,CAACP,UAAU,CAACE,KAAK,CAAC;QACnC,OAAA0C,aAAA,CAAAA,aAAA,KACKD,MAAM;UACT,CAACzC,KAAK,GAAG;YACPI,KAAK,EAAEC,IAAI,GAAGA,IAAI,CAACD,KAAK,GAAGA,KAAK;YAChCD,aAAa,EAAEE,IAAI,GAAGA,IAAI,CAACF,aAAa,GAAGA;;QAC5C;MAEL,CAAC,EAAE,EAAE,CAAC,CAAC;IACT;EACF;;;;UAzEcnB;EAAQ;IAAA+D,IAAA,EAAIjE,MAAM;IAAAkE,IAAA,GAACtD,6BAA6B;EAAA;AAAA,E;;UAxB7DX;EAAK;;UAILA;EAAK;;UAKLE;EAAM;;UAENC,SAAS;IAAA8D,IAAA,GAAC7D,cAAc,EAAE;MAAE8D,MAAM,EAAE;IAAI,CAAE;EAAA;;AAbhCpC,8BAA8B,GAAAqC,UAAA,EAN1CtE,SAAS,CAAC;EACPuE,QAAQ,EAAE,6BAA6B;EACvCC,QAAA,EAAAC,oBAAkD;EAElDC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWzC,8BAA8B,CAoG1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}