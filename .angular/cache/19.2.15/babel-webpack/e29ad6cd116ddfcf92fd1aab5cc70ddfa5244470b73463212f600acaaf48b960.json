{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatSelect, _MatSelectTrigger, _MatSelectModule;\nconst _c0 = [\"trigger\"];\nconst _c1 = [\"panel\"];\nconst _c2 = [[[\"mat-select-trigger\"]], \"*\"];\nconst _c3 = [\"mat-select-trigger\", \"*\"];\nfunction _MatSelect_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.placeholder);\n  }\n}\nfunction _MatSelect_Conditional_5_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction _MatSelect_Conditional_5_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.triggerValue);\n  }\n}\nfunction _MatSelect_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtemplate(1, _MatSelect_Conditional_5_Conditional_1_Template, 1, 0)(2, _MatSelect_Conditional_5_Conditional_2_Template, 2, 1, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.customTrigger ? 1 : 2);\n  }\n}\nfunction _MatSelect_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 1);\n    i0.ɵɵlistener(\"keydown\", function _MatSelect_ng_template_10_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    });\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open \", ctx_r1._getPanelTheme(), \"\");\n    i0.ɵɵclassProp(\"mat-select-panel-animations-enabled\", !ctx_r1._animationsDisabled);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.panelClass);\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"-panel\")(\"aria-multiselectable\", ctx_r1.multiple)(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1._getPanelAriaLabelledby());\n  }\n}\nimport { Overlay, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, Renderer2, ANIMATION_MODULE_TYPE, EventEmitter, HostAttributeToken, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, Input, ViewChild, Output, Directive, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _IdGenerator, LiveAnnouncer, removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { hasModifierKey, ENTER, SPACE, A, ESCAPE, DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW } from '@angular/cdk/keycodes';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, filter, map, takeUntil, take } from 'rxjs/operators';\nimport { NgClass } from '@angular/common';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-DqPi4knt.mjs';\nimport { _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition, c as MAT_OPTION_PARENT_COMPONENT, M as MatOption, d as MAT_OPTGROUP } from './option-ChV6uQgD.mjs';\nimport { E as ErrorStateMatcher } from './error-options-Dm2JJUbF.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatOptionModule } from './index-DOxJc1m4.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatFormFieldModule } from './module-BXZhw7pQ.mjs';\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n  return Error('`compareWith` must be a function.');\n}\n\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n  constructor(/** Reference to the select that emitted the change event. */\n  source, /** Current value of the select that emitted the event. */\n  value) {\n    _defineProperty(this, \"source\", void 0);\n    _defineProperty(this, \"value\", void 0);\n    this.source = source;\n    this.value = value;\n  }\n}\nclass MatSelect {\n  /** Scrolls a particular option into the view. */\n  _scrollOptionIntoView(index) {\n    const option = this.options.toArray()[index];\n    if (option) {\n      const panel = this.panel.nativeElement;\n      const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n      const element = option._getHostElement();\n      if (index === 0 && labelCount === 1) {\n        // If we've got one group label before the option and we're at the top option,\n        // scroll the list to the top. This is better UX than scrolling the list to the\n        // top of the option, because it allows the user to read the top group's label.\n        panel.scrollTop = 0;\n      } else {\n        panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n      }\n    }\n  }\n  /** Called when the panel has been opened and the overlay has settled on its final position. */\n  _positioningSettled() {\n    this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n  }\n  /** Creates a change event object that should be emitted by the select. */\n  _getChangeEvent(value) {\n    return new MatSelectChange(this, value);\n  }\n  /** Factory function used to create a scroll strategy for this select. */\n\n  /** Whether the select is focused. */\n  get focused() {\n    return this._focused || this._panelOpen;\n  }\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncParentProperties();\n  }\n  /** Placeholder to be shown if no value has been selected. */\n  get placeholder() {\n    return this._placeholder;\n  }\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  /** Whether the component is required. */\n  get required() {\n    var _ref, _this$_required, _this$ngControl;\n    return (_ref = (_this$_required = this._required) !== null && _this$_required !== void 0 ? _this$_required : (_this$ngControl = this.ngControl) === null || _this$ngControl === void 0 || (_this$ngControl = _this$ngControl.control) === null || _this$ngControl === void 0 ? void 0 : _this$ngControl.hasValidator(Validators.required)) !== null && _ref !== void 0 ? _ref : false;\n  }\n  set required(value) {\n    this._required = value;\n    this.stateChanges.next();\n  }\n  /** Whether the user should be allowed to select multiple options. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectDynamicMultipleError();\n    }\n    this._multiple = value;\n  }\n  /**\n   * Function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  get compareWith() {\n    return this._compareWith;\n  }\n  set compareWith(fn) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectNonFunctionValueError();\n    }\n    this._compareWith = fn;\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  /** Value of the select control. */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    const hasAssigned = this._assignValue(newValue);\n    if (hasAssigned) {\n      this._onChange(newValue);\n    }\n  }\n  /** Object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n\n  /** Unique id of the element. */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n    this.stateChanges.next();\n  }\n  /** Whether the select is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  /**\n   * Width of the panel. If set to `auto`, the panel will match the trigger width.\n   * If set to null or an empty string, the panel will grow to match the longest option's text.\n   */\n\n  constructor() {\n    var _this$_defaultOptions, _this$_defaultOptions2, _this$_defaultOptions3, _this$_defaultOptions4, _this$_defaultOptions5, _this$_defaultOptions6, _this$_defaultOptions7, _this$_defaultOptions8;\n    _defineProperty(this, \"_viewportRuler\", inject(ViewportRuler));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_dir\", inject(Directionality, {\n      optional: true\n    }));\n    _defineProperty(this, \"_idGenerator\", inject(_IdGenerator));\n    _defineProperty(this, \"_renderer\", inject(Renderer2));\n    _defineProperty(this, \"_parentFormField\", inject(MAT_FORM_FIELD, {\n      optional: true\n    }));\n    _defineProperty(this, \"ngControl\", inject(NgControl, {\n      self: true,\n      optional: true\n    }));\n    _defineProperty(this, \"_liveAnnouncer\", inject(LiveAnnouncer));\n    _defineProperty(this, \"_defaultOptions\", inject(MAT_SELECT_CONFIG, {\n      optional: true\n    }));\n    _defineProperty(this, \"_animationsDisabled\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }) === 'NoopAnimations');\n    _defineProperty(this, \"_initialized\", new Subject());\n    _defineProperty(this, \"_cleanupDetach\", void 0);\n    /** All of the defined select options. */\n    _defineProperty(this, \"options\", void 0);\n    // TODO(crisbeto): this is only necessary for the non-MDC select, but it's technically a\n    // public API so we have to keep it. It should be deprecated and removed eventually.\n    /** All of the defined groups of options. */\n    _defineProperty(this, \"optionGroups\", void 0);\n    /** User-supplied override of the trigger element. */\n    _defineProperty(this, \"customTrigger\", void 0);\n    /**\n     * This position config ensures that the top \"start\" corner of the overlay\n     * is aligned with with the top \"start\" of the origin by default (overlapping\n     * the trigger completely). If the panel cannot fit below the trigger, it\n     * will fall back to a position above the trigger.\n     */\n    _defineProperty(this, \"_positions\", [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    }, {\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }, {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }]);\n    _defineProperty(this, \"_scrollStrategyFactory\", inject(MAT_SELECT_SCROLL_STRATEGY));\n    /** Whether or not the overlay panel is open. */\n    _defineProperty(this, \"_panelOpen\", false);\n    /** Comparison function to specify which option is displayed. Defaults to object equality. */\n    _defineProperty(this, \"_compareWith\", (o1, o2) => o1 === o2);\n    /** Unique id for this input. */\n    _defineProperty(this, \"_uid\", this._idGenerator.getId('mat-select-'));\n    /** Current `aria-labelledby` value for the select trigger. */\n    _defineProperty(this, \"_triggerAriaLabelledBy\", null);\n    /**\n     * Keeps track of the previous form control assigned to the select.\n     * Used to detect if it has changed.\n     */\n    _defineProperty(this, \"_previousControl\", void 0);\n    /** Emits whenever the component is destroyed. */\n    _defineProperty(this, \"_destroy\", new Subject());\n    /** Tracks the error state of the select. */\n    _defineProperty(this, \"_errorStateTracker\", void 0);\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    _defineProperty(this, \"stateChanges\", new Subject());\n    /**\n     * Disable the automatic labeling to avoid issues like #27241.\n     * @docs-private\n     */\n    _defineProperty(this, \"disableAutomaticLabeling\", true);\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    _defineProperty(this, \"userAriaDescribedBy\", void 0);\n    /** Deals with the selection logic. */\n    _defineProperty(this, \"_selectionModel\", void 0);\n    /** Manages keyboard events for options in the panel. */\n    _defineProperty(this, \"_keyManager\", void 0);\n    /** Ideal origin for the overlay panel. */\n    _defineProperty(this, \"_preferredOverlayOrigin\", void 0);\n    /** Width of the overlay panel. */\n    _defineProperty(this, \"_overlayWidth\", void 0);\n    /** `View -> model callback called when value changes` */\n    _defineProperty(this, \"_onChange\", () => {});\n    /** `View -> model callback called when select has been touched` */\n    _defineProperty(this, \"_onTouched\", () => {});\n    /** ID for the DOM node containing the select's value. */\n    _defineProperty(this, \"_valueId\", this._idGenerator.getId('mat-select-value-'));\n    /** Strategy that will be used to handle scrolling while the select panel is open. */\n    _defineProperty(this, \"_scrollStrategy\", void 0);\n    _defineProperty(this, \"_overlayPanelClass\", ((_this$_defaultOptions = this._defaultOptions) === null || _this$_defaultOptions === void 0 ? void 0 : _this$_defaultOptions.overlayPanelClass) || '');\n    _defineProperty(this, \"_focused\", false);\n    /** A name for this control that can be used by `mat-form-field`. */\n    _defineProperty(this, \"controlType\", 'mat-select');\n    /** Trigger that opens the select. */\n    _defineProperty(this, \"trigger\", void 0);\n    /** Panel containing the select options. */\n    _defineProperty(this, \"panel\", void 0);\n    /** Overlay pane containing the options. */\n    _defineProperty(this, \"_overlayDir\", void 0);\n    /** Classes to be passed to the select panel. Supports the same syntax as `ngClass`. */\n    _defineProperty(this, \"panelClass\", void 0);\n    /** Whether the select is disabled. */\n    _defineProperty(this, \"disabled\", false);\n    /** Whether ripples in the select are disabled. */\n    _defineProperty(this, \"disableRipple\", false);\n    /** Tab index of the select. */\n    _defineProperty(this, \"tabIndex\", 0);\n    _defineProperty(this, \"_hideSingleSelectionIndicator\", (_this$_defaultOptions2 = (_this$_defaultOptions3 = this._defaultOptions) === null || _this$_defaultOptions3 === void 0 ? void 0 : _this$_defaultOptions3.hideSingleSelectionIndicator) !== null && _this$_defaultOptions2 !== void 0 ? _this$_defaultOptions2 : false);\n    _defineProperty(this, \"_placeholder\", void 0);\n    _defineProperty(this, \"_required\", void 0);\n    _defineProperty(this, \"_multiple\", false);\n    /** Whether to center the active option over the trigger. */\n    _defineProperty(this, \"disableOptionCentering\", (_this$_defaultOptions4 = (_this$_defaultOptions5 = this._defaultOptions) === null || _this$_defaultOptions5 === void 0 ? void 0 : _this$_defaultOptions5.disableOptionCentering) !== null && _this$_defaultOptions4 !== void 0 ? _this$_defaultOptions4 : false);\n    _defineProperty(this, \"_value\", void 0);\n    /** Aria label of the select. */\n    _defineProperty(this, \"ariaLabel\", '');\n    /** Input that can be used to specify the `aria-labelledby` attribute. */\n    _defineProperty(this, \"ariaLabelledby\", void 0);\n    _defineProperty(this, \"typeaheadDebounceInterval\", void 0);\n    /**\n     * Function used to sort the values in a select in multiple mode.\n     * Follows the same logic as `Array.prototype.sort`.\n     */\n    _defineProperty(this, \"sortComparator\", void 0);\n    _defineProperty(this, \"_id\", void 0);\n    _defineProperty(this, \"panelWidth\", this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined' ? this._defaultOptions.panelWidth : 'auto');\n    /**\n     * By default selecting an option with a `null` or `undefined` value will reset the select's\n     * value. Enable this option if the reset behavior doesn't match your requirements and instead\n     * the nullable options should become selected. The value of this input can be controlled app-wide\n     * using the `MAT_SELECT_CONFIG` injection token.\n     */\n    _defineProperty(this, \"canSelectNullableOptions\", (_this$_defaultOptions6 = (_this$_defaultOptions7 = this._defaultOptions) === null || _this$_defaultOptions7 === void 0 ? void 0 : _this$_defaultOptions7.canSelectNullableOptions) !== null && _this$_defaultOptions6 !== void 0 ? _this$_defaultOptions6 : false);\n    /** Combined stream of all of the child options' change events. */\n    _defineProperty(this, \"optionSelectionChanges\", defer(() => {\n      const options = this.options;\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      }\n      return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n    }));\n    /** Event emitted when the select panel has been toggled. */\n    _defineProperty(this, \"openedChange\", new EventEmitter());\n    /** Event emitted when the select has been opened. */\n    _defineProperty(this, \"_openedStream\", this.openedChange.pipe(filter(o => o), map(() => {})));\n    /** Event emitted when the select has been closed. */\n    _defineProperty(this, \"_closedStream\", this.openedChange.pipe(filter(o => !o), map(() => {})));\n    /** Event emitted when the selected value has been changed by the user. */\n    _defineProperty(this, \"selectionChange\", new EventEmitter());\n    /**\n     * Event that emits whenever the raw value of the select changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    _defineProperty(this, \"valueChange\", new EventEmitter());\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    _defineProperty(this, \"_trackedModal\", null);\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    _defineProperty(this, \"_skipPredicate\", option => {\n      if (this.panelOpen) {\n        // Support keyboard focusing disabled options in an ARIA listbox.\n        return false;\n      }\n      // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n      // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n      // closed.\n      return option.disabled;\n    });\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n    const parentForm = inject(NgForm, {\n      optional: true\n    });\n    const parentFormGroup = inject(FormGroupDirective, {\n      optional: true\n    });\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    if (this.ngControl) {\n      // Note: we provide the value accessor through here, instead of\n      // the `providers` to avoid running into a circular import.\n      this.ngControl.valueAccessor = this;\n    }\n    // Note that we only want to set this when the defaults pass it in, otherwise it should\n    // stay as `undefined` so that it falls back to the default in the key manager.\n    if (((_this$_defaultOptions8 = this._defaultOptions) === null || _this$_defaultOptions8 === void 0 ? void 0 : _this$_defaultOptions8.typeaheadDebounceInterval) != null) {\n      this.typeaheadDebounceInterval = this._defaultOptions.typeaheadDebounceInterval;\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n    this._scrollStrategy = this._scrollStrategyFactory();\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n  }\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple);\n    this.stateChanges.next();\n    this._viewportRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => {\n      if (this.panelOpen) {\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        this._changeDetectorRef.detectChanges();\n      }\n    });\n  }\n  ngAfterContentInit() {\n    this._initialized.next();\n    this._initialized.complete();\n    this._initKeyManager();\n    this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n      event.added.forEach(option => option.select());\n      event.removed.forEach(option => option.deselect());\n    });\n    this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n      this._resetOptions();\n      this._initializeSelection();\n    });\n  }\n  ngDoCheck() {\n    const newAriaLabelledby = this._getTriggerAriaLabelledby();\n    const ngControl = this.ngControl;\n    // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n    // is computed as a result of a content query which can cause this binding to trigger a\n    // \"changed after checked\" error.\n    if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n      const element = this._elementRef.nativeElement;\n      this._triggerAriaLabelledBy = newAriaLabelledby;\n      if (newAriaLabelledby) {\n        element.setAttribute('aria-labelledby', newAriaLabelledby);\n      } else {\n        element.removeAttribute('aria-labelledby');\n      }\n    }\n    if (ngControl) {\n      // The disabled state might go out of sync if the form group is swapped out. See #17860.\n      if (this._previousControl !== ngControl.control) {\n        if (this._previousControl !== undefined && ngControl.disabled !== null && ngControl.disabled !== this.disabled) {\n          this.disabled = ngControl.disabled;\n        }\n        this._previousControl = ngControl.control;\n      }\n      this.updateErrorState();\n    }\n  }\n  ngOnChanges(changes) {\n    // Updating the disabled state is handled by the input, but we need to additionally let\n    // the parent form field know to run change detection when the disabled state changes.\n    if (changes['disabled'] || changes['userAriaDescribedBy']) {\n      this.stateChanges.next();\n    }\n    if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n      this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n    }\n  }\n  ngOnDestroy() {\n    var _this$_cleanupDetach, _this$_keyManager;\n    (_this$_cleanupDetach = this._cleanupDetach) === null || _this$_cleanupDetach === void 0 || _this$_cleanupDetach.call(this);\n    (_this$_keyManager = this._keyManager) === null || _this$_keyManager === void 0 || _this$_keyManager.destroy();\n    this._destroy.next();\n    this._destroy.complete();\n    this.stateChanges.complete();\n    this._clearFromModal();\n  }\n  /** Toggles the overlay panel open or closed. */\n  toggle() {\n    this.panelOpen ? this.close() : this.open();\n  }\n  /** Opens the overlay panel. */\n  open() {\n    var _this$_cleanupDetach2;\n    if (!this._canOpen()) {\n      return;\n    }\n    // It's important that we read this as late as possible, because doing so earlier will\n    // return a different element since it's based on queries in the form field which may\n    // not have run yet. Also this needs to be assigned before we measure the overlay width.\n    if (this._parentFormField) {\n      this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n    }\n    (_this$_cleanupDetach2 = this._cleanupDetach) === null || _this$_cleanupDetach2 === void 0 || _this$_cleanupDetach2.call(this);\n    this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n    this._applyModalPanelOwnership();\n    this._panelOpen = true;\n    this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n      this._changeDetectorRef.detectChanges();\n      this._positioningSettled();\n    });\n    this._overlayDir.attachOverlay();\n    this._keyManager.withHorizontalOrientation(null);\n    this._highlightCorrectOption();\n    this._changeDetectorRef.markForCheck();\n    // Required for the MDC form field to pick up when the overlay has been opened.\n    this.stateChanges.next();\n    // Simulate the animation event before we moved away from `@angular/animations`.\n    Promise.resolve().then(() => this.openedChange.emit(true));\n  }\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n  /** Clears the reference to the listbox overlay element from the modal it was added to. */\n  _clearFromModal() {\n    if (!this._trackedModal) {\n      // Most commonly, the autocomplete trigger is not used inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    this._trackedModal = null;\n  }\n  /** Closes the overlay panel and focuses the host element. */\n  close() {\n    if (this._panelOpen) {\n      this._panelOpen = false;\n      this._exitAndDetach();\n      this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n      this._changeDetectorRef.markForCheck();\n      this._onTouched();\n      // Required for the MDC form field to pick up when the overlay has been closed.\n      this.stateChanges.next();\n      // Simulate the animation event before we moved away from `@angular/animations`.\n      Promise.resolve().then(() => this.openedChange.emit(false));\n    }\n  }\n  /** Triggers the exit animation and detaches the overlay at the end. */\n  _exitAndDetach() {\n    var _this$_cleanupDetach3;\n    if (this._animationsDisabled || !this.panel) {\n      this._detachOverlay();\n      return;\n    }\n    (_this$_cleanupDetach3 = this._cleanupDetach) === null || _this$_cleanupDetach3 === void 0 || _this$_cleanupDetach3.call(this);\n    this._cleanupDetach = () => {\n      cleanupEvent();\n      clearTimeout(exitFallbackTimer);\n      this._cleanupDetach = undefined;\n    };\n    const panel = this.panel.nativeElement;\n    const cleanupEvent = this._renderer.listen(panel, 'animationend', event => {\n      if (event.animationName === '_mat-select-exit') {\n        var _this$_cleanupDetach4;\n        (_this$_cleanupDetach4 = this._cleanupDetach) === null || _this$_cleanupDetach4 === void 0 || _this$_cleanupDetach4.call(this);\n        this._detachOverlay();\n      }\n    });\n    // Since closing the overlay depends on the animation, we have a fallback in case the panel\n    // doesn't animate. This can happen in some internal tests that do `* {animation: none}`.\n    const exitFallbackTimer = setTimeout(() => {\n      var _this$_cleanupDetach5;\n      (_this$_cleanupDetach5 = this._cleanupDetach) === null || _this$_cleanupDetach5 === void 0 || _this$_cleanupDetach5.call(this);\n      this._detachOverlay();\n    }, 200);\n    panel.classList.add('mat-select-panel-exit');\n  }\n  /** Detaches the current overlay directive. */\n  _detachOverlay() {\n    this._overlayDir.detachOverlay();\n    // Some of the overlay detachment logic depends on change detection.\n    // Mark for check to ensure that things get picked up in a timely manner.\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Sets the select's value. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param value New value to be written to the model.\n   */\n  writeValue(value) {\n    this._assignValue(value);\n  }\n  /**\n   * Saves a callback function to be invoked when the select's value\n   * changes from user input. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the value changes.\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Saves a callback function to be invoked when the select is blurred\n   * by the user. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the component has been touched.\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Disables the select. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param isDisabled Sets whether the component is disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n  /** Whether or not the overlay panel is open. */\n  get panelOpen() {\n    return this._panelOpen;\n  }\n  /** The currently selected option. */\n  get selected() {\n    var _this$_selectionModel, _this$_selectionModel2;\n    return this.multiple ? ((_this$_selectionModel = this._selectionModel) === null || _this$_selectionModel === void 0 ? void 0 : _this$_selectionModel.selected) || [] : (_this$_selectionModel2 = this._selectionModel) === null || _this$_selectionModel2 === void 0 ? void 0 : _this$_selectionModel2.selected[0];\n  }\n  /** The value displayed in the trigger. */\n  get triggerValue() {\n    if (this.empty) {\n      return '';\n    }\n    if (this._multiple) {\n      const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n      if (this._isRtl()) {\n        selectedOptions.reverse();\n      }\n      // TODO(crisbeto): delimiter should be configurable for proper localization.\n      return selectedOptions.join(', ');\n    }\n    return this._selectionModel.selected[0].viewValue;\n  }\n  /** Refreshes the error state of the select. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Whether the element is in RTL mode. */\n  _isRtl() {\n    return this._dir ? this._dir.value === 'rtl' : false;\n  }\n  /** Handles all keydown events on the select. */\n  _handleKeydown(event) {\n    if (!this.disabled) {\n      this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n    }\n  }\n  /** Handles keyboard events while the select is closed. */\n  _handleClosedKeydown(event) {\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW;\n    const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n    const manager = this._keyManager;\n    // Open the select on ALT + arrow key to match the native <select>\n    if (!manager.isTyping() && isOpenKey && !hasModifierKey(event) || (this.multiple || event.altKey) && isArrowKey) {\n      event.preventDefault(); // prevents the page from scrolling down when pressing space\n      this.open();\n    } else if (!this.multiple) {\n      const previouslySelectedOption = this.selected;\n      manager.onKeydown(event);\n      const selectedOption = this.selected;\n      // Since the value has changed, we need to announce it ourselves.\n      if (selectedOption && previouslySelectedOption !== selectedOption) {\n        // We set a duration on the live announcement, because we want the live element to be\n        // cleared after a while so that users can't navigate to it using the arrow keys.\n        this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n      }\n    }\n  }\n  /** Handles keyboard events when the selected is open. */\n  _handleOpenKeydown(event) {\n    const manager = this._keyManager;\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n    const isTyping = manager.isTyping();\n    if (isArrowKey && event.altKey) {\n      // Close the select on ALT + arrow key to match the native <select>\n      event.preventDefault();\n      this.close();\n      // Don't do anything in this case if the user is typing,\n      // because the typing sequence can include the space key.\n    } else if (!isTyping && (keyCode === ENTER || keyCode === SPACE) && manager.activeItem && !hasModifierKey(event)) {\n      event.preventDefault();\n      manager.activeItem._selectViaInteraction();\n    } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n      event.preventDefault();\n      const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n      this.options.forEach(option => {\n        if (!option.disabled) {\n          hasDeselectedOptions ? option.select() : option.deselect();\n        }\n      });\n    } else {\n      const previouslyFocusedIndex = manager.activeItemIndex;\n      manager.onKeydown(event);\n      if (this._multiple && isArrowKey && event.shiftKey && manager.activeItem && manager.activeItemIndex !== previouslyFocusedIndex) {\n        manager.activeItem._selectViaInteraction();\n      }\n    }\n  }\n  /** Handles keyboard events coming from the overlay. */\n  _handleOverlayKeydown(event) {\n    // TODO(crisbeto): prior to #30363 this was being handled inside the overlay directive, but we\n    // need control over the animation timing so we do it manually. We should remove the `keydown`\n    // listener from `.mat-mdc-select-panel` and handle all the events here. That may cause\n    // further test breakages so it's left for a follow-up.\n    if (event.keyCode === ESCAPE && !hasModifierKey(event)) {\n      event.preventDefault();\n      this.close();\n    }\n  }\n  _onFocus() {\n    if (!this.disabled) {\n      this._focused = true;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n   * \"blur\" to the panel when it opens, causing a false positive.\n   */\n  _onBlur() {\n    var _this$_keyManager2;\n    this._focused = false;\n    (_this$_keyManager2 = this._keyManager) === null || _this$_keyManager2 === void 0 || _this$_keyManager2.cancelTypeahead();\n    if (!this.disabled && !this.panelOpen) {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n  }\n  /** Returns the theme to be used on the panel. */\n  _getPanelTheme() {\n    return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n  }\n  /** Whether the select has a value. */\n  get empty() {\n    return !this._selectionModel || this._selectionModel.isEmpty();\n  }\n  _initializeSelection() {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl) {\n        this._value = this.ngControl.value;\n      }\n      this._setSelectionByValue(this._value);\n      this.stateChanges.next();\n    });\n  }\n  /**\n   * Sets the selected option based on a value. If no option can be\n   * found with the designated value, the select trigger is cleared.\n   */\n  _setSelectionByValue(value) {\n    this.options.forEach(option => option.setInactiveStyles());\n    this._selectionModel.clear();\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonArrayValueError();\n      }\n      value.forEach(currentValue => this._selectOptionByValue(currentValue));\n      this._sortValues();\n    } else {\n      const correspondingOption = this._selectOptionByValue(value);\n      // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what option the user interacted with last.\n      if (correspondingOption) {\n        this._keyManager.updateActiveItem(correspondingOption);\n      } else if (!this.panelOpen) {\n        // Otherwise reset the highlighted option. Note that we only want to do this while\n        // closed, because doing it while open can shift the user's focus unnecessarily.\n        this._keyManager.updateActiveItem(-1);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Finds and selects and option based on its value.\n   * @returns Option that has the corresponding value.\n   */\n  _selectOptionByValue(value) {\n    const correspondingOption = this.options.find(option => {\n      // Skip options that are already in the model. This allows us to handle cases\n      // where the same primitive value is selected multiple times.\n      if (this._selectionModel.isSelected(option)) {\n        return false;\n      }\n      try {\n        // Treat null as a special reset value.\n        return (option.value != null || this.canSelectNullableOptions) && this._compareWith(option.value, value);\n      } catch (error) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          // Notify developers of errors in their comparator.\n          console.warn(error);\n        }\n        return false;\n      }\n    });\n    if (correspondingOption) {\n      this._selectionModel.select(correspondingOption);\n    }\n    return correspondingOption;\n  }\n  /** Assigns a specific value to the select. Returns whether the value has changed. */\n  _assignValue(newValue) {\n    // Always re-assign an array, because it might have been mutated.\n    if (newValue !== this._value || this._multiple && Array.isArray(newValue)) {\n      if (this.options) {\n        this._setSelectionByValue(newValue);\n      }\n      this._value = newValue;\n      return true;\n    }\n    return false;\n  }\n  /** Gets how wide the overlay panel should be. */\n  _getOverlayWidth(preferredOrigin) {\n    if (this.panelWidth === 'auto') {\n      const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin ? preferredOrigin.elementRef : preferredOrigin || this._elementRef;\n      return refToMeasure.nativeElement.getBoundingClientRect().width;\n    }\n    return this.panelWidth === null ? '' : this.panelWidth;\n  }\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties() {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n  _initKeyManager() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withTypeAhead(this.typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr').withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(['shiftKey']).skipPredicate(this._skipPredicate);\n    this._keyManager.tabOut.subscribe(() => {\n      if (this.panelOpen) {\n        // Select the active item when tabbing away. This is consistent with how the native\n        // select behaves. Note that we only want to do this in single selection mode.\n        if (!this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        }\n        // Restore focus to the trigger before closing. Ensures that the focus\n        // position won't be lost if the user got focus into the overlay.\n        this.focus();\n        this.close();\n      }\n    });\n    this._keyManager.change.subscribe(() => {\n      if (this._panelOpen && this.panel) {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n      } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n        this._keyManager.activeItem._selectViaInteraction();\n      }\n    });\n  }\n  /** Drops current option subscriptions and IDs and resets from scratch. */\n  _resetOptions() {\n    const changedOrDestroyed = merge(this.options.changes, this._destroy);\n    this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n      this._onSelect(event.source, event.isUserInput);\n      if (event.isUserInput && !this.multiple && this._panelOpen) {\n        this.close();\n        this.focus();\n      }\n    });\n    // Listen to changes in the internal state of the options and react accordingly.\n    // Handles cases like the labels of the selected options changing.\n    merge(...this.options.map(option => option._stateChanges)).pipe(takeUntil(changedOrDestroyed)).subscribe(() => {\n      // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n      // be the result of an expression changing. We have to use `detectChanges` in order\n      // to avoid \"changed after checked\" errors (see #14793).\n      this._changeDetectorRef.detectChanges();\n      this.stateChanges.next();\n    });\n  }\n  /** Invoked when an option is clicked. */\n  _onSelect(option, isUserInput) {\n    const wasSelected = this._selectionModel.isSelected(option);\n    if (!this.canSelectNullableOptions && option.value == null && !this._multiple) {\n      option.deselect();\n      this._selectionModel.clear();\n      if (this.value != null) {\n        this._propagateChanges(option.value);\n      }\n    } else {\n      if (wasSelected !== option.selected) {\n        option.selected ? this._selectionModel.select(option) : this._selectionModel.deselect(option);\n      }\n      if (isUserInput) {\n        this._keyManager.setActiveItem(option);\n      }\n      if (this.multiple) {\n        this._sortValues();\n        if (isUserInput) {\n          // In case the user selected the option with their mouse, we\n          // want to restore focus back to the trigger, in order to\n          // prevent the select keyboard controls from clashing with\n          // the ones from `mat-option`.\n          this.focus();\n        }\n      }\n    }\n    if (wasSelected !== this._selectionModel.isSelected(option)) {\n      this._propagateChanges();\n    }\n    this.stateChanges.next();\n  }\n  /** Sorts the selected values in the selected based on their order in the panel. */\n  _sortValues() {\n    if (this.multiple) {\n      const options = this.options.toArray();\n      this._selectionModel.sort((a, b) => {\n        return this.sortComparator ? this.sortComparator(a, b, options) : options.indexOf(a) - options.indexOf(b);\n      });\n      this.stateChanges.next();\n    }\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges(fallbackValue) {\n    let valueToEmit;\n    if (this.multiple) {\n      valueToEmit = this.selected.map(option => option.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n    this._value = valueToEmit;\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Highlights the selected item. If no option is selected, it will highlight\n   * the first *enabled* option.\n   */\n  _highlightCorrectOption() {\n    if (this._keyManager) {\n      if (this.empty) {\n        // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n        // because it activates the first option that passes the skip predicate, rather than the\n        // first *enabled* option.\n        let firstEnabledOptionIndex = -1;\n        for (let index = 0; index < this.options.length; index++) {\n          const option = this.options.get(index);\n          if (!option.disabled) {\n            firstEnabledOptionIndex = index;\n            break;\n          }\n        }\n        this._keyManager.setActiveItem(firstEnabledOptionIndex);\n      } else {\n        this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n      }\n    }\n  }\n  /** Whether the panel is allowed to open. */\n  _canOpen() {\n    var _this$options;\n    return !this._panelOpen && !this.disabled && ((_this$options = this.options) === null || _this$options === void 0 ? void 0 : _this$options.length) > 0 && !!this._overlayDir;\n  }\n  /** Focuses the select element. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Gets the aria-labelledby for the select panel. */\n  _getPanelAriaLabelledby() {\n    var _this$_parentFormFiel;\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = ((_this$_parentFormFiel = this._parentFormField) === null || _this$_parentFormFiel === void 0 ? void 0 : _this$_parentFormFiel.getLabelId()) || null;\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Determines the `aria-activedescendant` to be set on the host. */\n  _getAriaActiveDescendant() {\n    if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n      return this._keyManager.activeItem.id;\n    }\n    return null;\n  }\n  /** Gets the aria-labelledby of the select component trigger. */\n  _getTriggerAriaLabelledby() {\n    var _this$_parentFormFiel2;\n    if (this.ariaLabel) {\n      return null;\n    }\n    let value = ((_this$_parentFormFiel2 = this._parentFormField) === null || _this$_parentFormFiel2 === void 0 ? void 0 : _this$_parentFormFiel2.getLabelId()) || '';\n    if (this.ariaLabelledby) {\n      value += ' ' + this.ariaLabelledby;\n    }\n    // The value should not be used for the trigger's aria-labelledby,\n    // but this currently \"breaks\" accessibility tests since they complain\n    // there is no aria-labelledby. This is because they are not setting an\n    // appropriate label on the form field or select.\n    // TODO: remove this conditional after fixing clients by ensuring their\n    // selects have a label applied.\n    if (!value) {\n      value = this._valueId;\n    }\n    return value;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    this.focus();\n    this.open();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    // Since the panel doesn't overlap the trigger, we\n    // want the label to only float when there's a value.\n    return this.panelOpen || !this.empty || this.focused && !!this.placeholder;\n  }\n}\n_MatSelect = MatSelect;\n_defineProperty(MatSelect, \"\\u0275fac\", function _MatSelect_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSelect)();\n});\n_defineProperty(MatSelect, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatSelect,\n  selectors: [[\"mat-select\"]],\n  contentQueries: function _MatSelect_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_SELECT_TRIGGER, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customTrigger = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n    }\n  },\n  viewQuery: function _MatSelect_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n      i0.ɵɵviewQuery(CdkConnectedOverlay, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trigger = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._overlayDir = _t.first);\n    }\n  },\n  hostAttrs: [\"role\", \"combobox\", \"aria-haspopup\", \"listbox\", 1, \"mat-mdc-select\"],\n  hostVars: 19,\n  hostBindings: function _MatSelect_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function _MatSelect_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      })(\"focus\", function _MatSelect_focus_HostBindingHandler() {\n        return ctx._onFocus();\n      })(\"blur\", function _MatSelect_blur_HostBindingHandler() {\n        return ctx._onBlur();\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx.panelOpen ? ctx.id + \"-panel\" : null)(\"aria-expanded\", ctx.panelOpen)(\"aria-label\", ctx.ariaLabel || null)(\"aria-required\", ctx.required.toString())(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-activedescendant\", ctx._getAriaActiveDescendant());\n      i0.ɵɵclassProp(\"mat-mdc-select-disabled\", ctx.disabled)(\"mat-mdc-select-invalid\", ctx.errorState)(\"mat-mdc-select-required\", ctx.required)(\"mat-mdc-select-empty\", ctx.empty)(\"mat-mdc-select-multiple\", ctx.multiple);\n    }\n  },\n  inputs: {\n    userAriaDescribedBy: [0, \"aria-describedby\", \"userAriaDescribedBy\"],\n    panelClass: \"panelClass\",\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n    disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n    tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n    hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n    placeholder: \"placeholder\",\n    required: [2, \"required\", \"required\", booleanAttribute],\n    multiple: [2, \"multiple\", \"multiple\", booleanAttribute],\n    disableOptionCentering: [2, \"disableOptionCentering\", \"disableOptionCentering\", booleanAttribute],\n    compareWith: \"compareWith\",\n    value: \"value\",\n    ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n    errorStateMatcher: \"errorStateMatcher\",\n    typeaheadDebounceInterval: [2, \"typeaheadDebounceInterval\", \"typeaheadDebounceInterval\", numberAttribute],\n    sortComparator: \"sortComparator\",\n    id: \"id\",\n    panelWidth: \"panelWidth\",\n    canSelectNullableOptions: [2, \"canSelectNullableOptions\", \"canSelectNullableOptions\", booleanAttribute]\n  },\n  outputs: {\n    openedChange: \"openedChange\",\n    _openedStream: \"opened\",\n    _closedStream: \"closed\",\n    selectionChange: \"selectionChange\",\n    valueChange: \"valueChange\"\n  },\n  exportAs: [\"matSelect\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MatFormFieldControl,\n    useExisting: _MatSelect\n  }, {\n    provide: MAT_OPTION_PARENT_COMPONENT,\n    useExisting: _MatSelect\n  }]), i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c3,\n  decls: 11,\n  vars: 9,\n  consts: [[\"fallbackOverlayOrigin\", \"cdkOverlayOrigin\", \"trigger\", \"\"], [\"panel\", \"\"], [\"cdk-overlay-origin\", \"\", 1, \"mat-mdc-select-trigger\", 3, \"click\"], [1, \"mat-mdc-select-value\"], [1, \"mat-mdc-select-placeholder\", \"mat-mdc-select-min-line\"], [1, \"mat-mdc-select-value-text\"], [1, \"mat-mdc-select-arrow-wrapper\"], [1, \"mat-mdc-select-arrow\"], [\"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M7 10l5 5 5-5z\"], [\"cdk-connected-overlay\", \"\", \"cdkConnectedOverlayLockPosition\", \"\", \"cdkConnectedOverlayHasBackdrop\", \"\", \"cdkConnectedOverlayBackdropClass\", \"cdk-overlay-transparent-backdrop\", 3, \"detach\", \"backdropClick\", \"overlayKeydown\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayFlexibleDimensions\"], [1, \"mat-mdc-select-min-line\"], [\"role\", \"listbox\", \"tabindex\", \"-1\", 3, \"keydown\", \"ngClass\"]],\n  template: function _MatSelect_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵprojectionDef(_c2);\n      i0.ɵɵelementStart(0, \"div\", 2, 0);\n      i0.ɵɵlistener(\"click\", function _MatSelect_Template_div_click_0_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.open());\n      });\n      i0.ɵɵelementStart(3, \"div\", 3);\n      i0.ɵɵtemplate(4, _MatSelect_Conditional_4_Template, 2, 1, \"span\", 4)(5, _MatSelect_Conditional_5_Template, 3, 1, \"span\", 5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(8, \"svg\", 8);\n      i0.ɵɵelement(9, \"path\", 9);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(10, _MatSelect_ng_template_10_Template, 3, 10, \"ng-template\", 10);\n      i0.ɵɵlistener(\"detach\", function _MatSelect_Template_ng_template_detach_10_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.close());\n      })(\"backdropClick\", function _MatSelect_Template_ng_template_backdropClick_10_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.close());\n      })(\"overlayKeydown\", function _MatSelect_Template_ng_template_overlayKeydown_10_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handleOverlayKeydown($event));\n      });\n    }\n    if (rf & 2) {\n      const fallbackOverlayOrigin_r4 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵattribute(\"id\", ctx._valueId);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.empty ? 4 : 5);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"cdkConnectedOverlayDisableClose\", true)(\"cdkConnectedOverlayPanelClass\", ctx._overlayPanelClass)(\"cdkConnectedOverlayScrollStrategy\", ctx._scrollStrategy)(\"cdkConnectedOverlayOrigin\", ctx._preferredOverlayOrigin || fallbackOverlayOrigin_r4)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayWidth\", ctx._overlayWidth)(\"cdkConnectedOverlayFlexibleDimensions\", true);\n    }\n  },\n  dependencies: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n  styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelect, [{\n    type: Component,\n    args: [{\n      selector: 'mat-select',\n      exportAs: 'matSelect',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'role': 'combobox',\n        'aria-haspopup': 'listbox',\n        'class': 'mat-mdc-select',\n        '[attr.id]': 'id',\n        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n        '[attr.aria-expanded]': 'panelOpen',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.aria-required]': 'required.toString()',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n        '[class.mat-mdc-select-disabled]': 'disabled',\n        '[class.mat-mdc-select-invalid]': 'errorState',\n        '[class.mat-mdc-select-required]': 'required',\n        '[class.mat-mdc-select-empty]': 'empty',\n        '[class.mat-mdc-select-multiple]': 'multiple',\n        '(keydown)': '_handleKeydown($event)',\n        '(focus)': '_onFocus()',\n        '(blur)': '_onBlur()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }],\n      imports: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n      template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayDisableClose]=\\\"true\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  [cdkConnectedOverlayFlexibleDimensions]=\\\"true\\\"\\n  (detach)=\\\"close()\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (overlayKeydown)=\\\"_handleOverlayKeydown($event)\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [class.mat-select-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"]\n    }]\n  }], () => [], {\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    customTrigger: [{\n      type: ContentChild,\n      args: [MAT_SELECT_TRIGGER]\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    trigger: [{\n      type: ViewChild,\n      args: ['trigger']\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    _overlayDir: [{\n      type: ViewChild,\n      args: [CdkConnectedOverlay]\n    }],\n    panelClass: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableOptionCentering: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    typeaheadDebounceInterval: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    sortComparator: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    canSelectNullableOptions: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {}\n_MatSelectTrigger = MatSelectTrigger;\n_defineProperty(MatSelectTrigger, \"\\u0275fac\", function _MatSelectTrigger_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSelectTrigger)();\n});\n_defineProperty(MatSelectTrigger, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatSelectTrigger,\n  selectors: [[\"mat-select-trigger\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_SELECT_TRIGGER,\n    useExisting: _MatSelectTrigger\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectTrigger, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-select-trigger',\n      providers: [{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }]\n    }]\n  }], null, null);\n})();\nclass MatSelectModule {}\n_MatSelectModule = MatSelectModule;\n_defineProperty(MatSelectModule, \"\\u0275fac\", function _MatSelectModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSelectModule)();\n});\n_defineProperty(MatSelectModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatSelectModule,\n  imports: [OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger],\n  exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule]\n}));\n_defineProperty(MatSelectModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n  imports: [OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatFormFieldModule, MatOptionModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger],\n      exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule],\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { MatSelectModule as M, MAT_SELECT_SCROLL_STRATEGY as a, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY as b, MAT_SELECT_CONFIG as c, MAT_SELECT_SCROLL_STRATEGY_PROVIDER as d, MAT_SELECT_TRIGGER as e, MatSelectChange as f, MatSelect as g, MatSelectTrigger as h };\n//# sourceMappingURL=module-Cbt8Fcmv.mjs.map", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "rf", "ctx_r1", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate", "placeholder", "_MatSelect_Conditional_5_Conditional_1_Template", "ctx", "ɵɵprojection", "_MatSelect_Conditional_5_Conditional_2_Template", "triggerValue", "_MatSelect_Conditional_5_Template", "ɵɵtemplate", "ɵɵconditional", "customTrigger", "_MatSelect_ng_template_10_Template", "_r3", "ɵɵgetCurrentView", "ɵɵlistener", "_MatSelect_ng_template_10_Template_div_keydown_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "_handleKeydown", "ɵɵclassMapInterpolate1", "_getPanelTheme", "ɵɵclassProp", "_animationsDisabled", "ɵɵproperty", "panelClass", "ɵɵattribute", "id", "multiple", "aria<PERSON><PERSON><PERSON>", "_getPanelAriaLabe<PERSON>by", "Overlay", "CdkConnectedOverlay", "CdkOverlayOrigin", "OverlayModule", "InjectionToken", "inject", "ChangeDetectorRef", "ElementRef", "Renderer2", "ANIMATION_MODULE_TYPE", "EventEmitter", "HostAttributeToken", "booleanAttribute", "numberAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChildren", "ContentChild", "Input", "ViewChild", "Output", "Directive", "NgModule", "ViewportRuler", "CdkScrollableModule", "_IdGenerator", "LiveAnnouncer", "removeAriaReferencedId", "addAriaReferencedId", "ActiveDescendantKeyManager", "Directionality", "SelectionModel", "hasModifierKey", "ENTER", "SPACE", "A", "ESCAPE", "DOWN_ARROW", "UP_ARROW", "LEFT_ARROW", "RIGHT_ARROW", "NgControl", "Validators", "NgForm", "FormGroupDirective", "Subject", "defer", "merge", "startWith", "switchMap", "filter", "map", "takeUntil", "take", "Ng<PERSON><PERSON>", "h", "MAT_FORM_FIELD", "k", "MatFormFieldControl", "_", "_countGroupLabelsBeforeOption", "b", "_getOptionScrollPosition", "c", "MAT_OPTION_PARENT_COMPONENT", "M", "MatOption", "d", "MAT_OPTGROUP", "E", "ErrorStateMatcher", "_ErrorStateTracker", "MatOptionModule", "MatCommonModule", "MatFormFieldModule", "getMatSelectDynamicMultipleError", "Error", "getMatSelectNonArrayValueError", "getMatSelectNonFunctionValueError", "MAT_SELECT_SCROLL_STRATEGY", "providedIn", "factory", "overlay", "scrollStrategies", "reposition", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY", "MAT_SELECT_CONFIG", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "MAT_SELECT_TRIGGER", "MatSelectChange", "constructor", "source", "value", "_defineProperty", "MatSelect", "_scrollOptionIntoView", "index", "option", "options", "toArray", "panel", "nativeElement", "labelCount", "optionGroups", "element", "_getHostElement", "scrollTop", "offsetTop", "offsetHeight", "_positioningSettled", "_keyManager", "activeItemIndex", "_getChangeEvent", "focused", "_focused", "_panelOpen", "hideSingleSelectionIndicator", "_hideSingleSelectionIndicator", "_syncParentProperties", "_placeholder", "stateChanges", "next", "required", "_ref", "_this$_required", "_this$ngControl", "_required", "ngControl", "control", "hasValidator", "_multiple", "_selectionModel", "ngDevMode", "compareWith", "_compareWith", "fn", "_initializeSelection", "_value", "newValue", "hasAssigned", "_assignValue", "_onChange", "errorStateMatcher", "_errorStateTracker", "matcher", "_id", "_uid", "errorState", "_this$_defaultOptions", "_this$_defaultOptions2", "_this$_defaultOptions3", "_this$_defaultOptions4", "_this$_defaultOptions5", "_this$_defaultOptions6", "_this$_defaultOptions7", "_this$_defaultOptions8", "optional", "self", "originX", "originY", "overlayX", "overlayY", "o1", "o2", "_idGenerator", "getId", "_defaultOptions", "overlayPanelClass", "disableOptionCentering", "panelWidth", "canSelectNullableOptions", "changes", "pipe", "onSelectionChange", "_initialized", "optionSelectionChanges", "openedChange", "o", "panelOpen", "disabled", "defaultErrorStateMatcher", "parentForm", "parentFormGroup", "tabIndex", "valueAccessor", "typeaheadDebounceInterval", "_scrollStrategy", "_scrollStrategyFactory", "parseInt", "ngOnInit", "_viewportRuler", "change", "_destroy", "subscribe", "_overlayWidth", "_getOverlayWidth", "_preferredOverlayOrigin", "_changeDetectorRef", "detectChanges", "ngAfterContentInit", "complete", "_initKeyManager", "changed", "event", "added", "for<PERSON>ach", "select", "removed", "deselect", "_resetOptions", "ngDoCheck", "newAria<PERSON><PERSON><PERSON><PERSON>", "_getTriggerAriaLabe<PERSON>by", "_triggerAriaLabelledBy", "_elementRef", "setAttribute", "removeAttribute", "_previousControl", "undefined", "updateErrorState", "ngOnChanges", "withTypeAhead", "ngOnDestroy", "_this$_cleanupDetach", "_this$_keyManager", "_cleanupDetach", "call", "destroy", "_clearFromModal", "toggle", "close", "open", "_this$_cleanupDetach2", "_canOpen", "_parentFormField", "getConnectedOverlayOrigin", "_applyModalPanelOwnership", "_overlayDir", "positionChange", "attachOverlay", "withHorizontalOrientation", "_highlightCorrectOption", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "then", "emit", "modal", "closest", "panelId", "_trackedModal", "_exitAndDetach", "_isRtl", "_onTouched", "_this$_cleanupDetach3", "_detachOverlay", "cleanupEvent", "clearTimeout", "exitFallbackTimer", "_renderer", "listen", "animationName", "_this$_cleanupDetach4", "setTimeout", "_this$_cleanupDetach5", "classList", "add", "detachOverlay", "writeValue", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "selected", "_this$_selectionModel", "_this$_selectionModel2", "empty", "selectedOptions", "viewValue", "reverse", "join", "_dir", "_handleOpenKeydown", "_handleClosedKeydown", "keyCode", "isArrowKey", "isOpenKey", "manager", "isTyping", "altKey", "preventDefault", "previouslySelectedOption", "onKeydown", "selectedOption", "_liveAnnouncer", "announce", "activeItem", "_selectViaInteraction", "ctrl<PERSON>ey", "hasDeselectedOptions", "some", "opt", "previouslyFocusedIndex", "shift<PERSON>ey", "_handleOverlayKeydown", "_onFocus", "_onBlur", "_this$_keyManager2", "cancelTypeahead", "color", "isEmpty", "_setSelectionByValue", "setInactiveStyles", "clear", "Array", "isArray", "currentValue", "_selectOptionByValue", "_sortValues", "correspondingOption", "updateActiveItem", "find", "isSelected", "error", "console", "warn", "preferred<PERSON><PERSON>in", "refToMeasure", "elementRef", "getBoundingClientRect", "width", "withVerticalOrientation", "withHomeAndEnd", "withPageUpDown", "withAllowedModifierKeys", "skipPredicate", "_skipPredicate", "tabOut", "focus", "changedOrDestroyed", "_onSelect", "isUserInput", "_stateChanges", "wasSelected", "_propagateChanges", "setActiveItem", "sort", "a", "sortComparator", "indexOf", "fallback<PERSON><PERSON><PERSON>", "valueToEmit", "valueChange", "selectionChange", "firstEnabledOptionIndex", "length", "get", "_this$options", "_this$_parentFormFiel", "labelId", "getLabelId", "labelExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getAriaActiveDescendant", "_this$_parentFormFiel2", "_valueId", "setDescribedByIds", "ids", "onContainerClick", "shouldLabelFloat", "_MatSelect", "_MatSelect_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "_MatSelect_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "_MatSelect_Query", "ɵɵviewQuery", "_c0", "_c1", "trigger", "hostAttrs", "hostVars", "hostBindings", "_MatSelect_HostBindings", "_MatSelect_keydown_HostBindingHandler", "_MatSelect_focus_HostBindingHandler", "_MatSelect_blur_HostBindingHandler", "toString", "inputs", "userAriaDescribedBy", "disable<PERSON><PERSON><PERSON>", "outputs", "_openedStream", "_closedStream", "exportAs", "features", "ɵɵProvidersFeature", "useExisting", "ɵɵNgOnChangesFeature", "ngContentSelectors", "_c3", "decls", "vars", "consts", "template", "_MatSelect_Template", "_r1", "ɵɵprojectionDef", "_c2", "_MatSelect_Template_div_click_0_listener", "_MatSelect_Conditional_4_Template", "ɵɵnamespaceSVG", "ɵɵelement", "_MatSelect_Template_ng_template_detach_10_listener", "_MatSelect_Template_ng_template_backdropClick_10_listener", "_MatSelect_Template_ng_template_overlayKeydown_10_listener", "fallbackOverlayOrigin_r4", "ɵɵreference", "_overlayPanelClass", "_positions", "dependencies", "styles", "encapsulation", "changeDetection", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "host", "providers", "imports", "descendants", "transform", "MatSelectTrigger", "_MatSelectTrigger", "_MatSelectTrigger_Factory", "ɵɵdefineDirective", "MatSelectModule", "_MatSelectModule", "_MatSelectModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "e", "f", "g"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/module-Cbt8Fcmv.mjs"], "sourcesContent": ["import { Overlay, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, Renderer2, ANIMATION_MODULE_TYPE, EventEmitter, HostAttributeToken, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, Input, ViewChild, Output, Directive, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _IdGenerator, LiveAnnouncer, removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { hasModifier<PERSON>ey, ENTER, SPACE, A, ESCAPE, DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW } from '@angular/cdk/keycodes';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, filter, map, takeUntil, take } from 'rxjs/operators';\nimport { NgClass } from '@angular/common';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-DqPi4knt.mjs';\nimport { _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition, c as MAT_OPTION_PARENT_COMPONENT, M as MatOption, d as MAT_OPTGROUP } from './option-ChV6uQgD.mjs';\nimport { E as ErrorStateMatcher } from './error-options-Dm2JJUbF.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatOptionModule } from './index-DOxJc1m4.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatFormFieldModule } from './module-BXZhw7pQ.mjs';\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n    return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n    return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n    return Error('`compareWith` must be a function.');\n}\n\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition();\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n    provide: MAT_SELECT_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n    source;\n    value;\n    constructor(\n    /** Reference to the select that emitted the change event. */\n    source, \n    /** Current value of the select that emitted the event. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\nclass MatSelect {\n    _viewportRuler = inject(ViewportRuler);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _dir = inject(Directionality, { optional: true });\n    _idGenerator = inject(_IdGenerator);\n    _renderer = inject(Renderer2);\n    _parentFormField = inject(MAT_FORM_FIELD, { optional: true });\n    ngControl = inject(NgControl, { self: true, optional: true });\n    _liveAnnouncer = inject(LiveAnnouncer);\n    _defaultOptions = inject(MAT_SELECT_CONFIG, { optional: true });\n    _animationsDisabled = inject(ANIMATION_MODULE_TYPE, { optional: true }) === 'NoopAnimations';\n    _initialized = new Subject();\n    _cleanupDetach;\n    /** All of the defined select options. */\n    options;\n    // TODO(crisbeto): this is only necessary for the non-MDC select, but it's technically a\n    // public API so we have to keep it. It should be deprecated and removed eventually.\n    /** All of the defined groups of options. */\n    optionGroups;\n    /** User-supplied override of the trigger element. */\n    customTrigger;\n    /**\n     * This position config ensures that the top \"start\" corner of the overlay\n     * is aligned with with the top \"start\" of the origin by default (overlapping\n     * the trigger completely). If the panel cannot fit below the trigger, it\n     * will fall back to a position above the trigger.\n     */\n    _positions = [\n        {\n            originX: 'start',\n            originY: 'bottom',\n            overlayX: 'start',\n            overlayY: 'top',\n        },\n        {\n            originX: 'end',\n            originY: 'bottom',\n            overlayX: 'end',\n            overlayY: 'top',\n        },\n        {\n            originX: 'start',\n            originY: 'top',\n            overlayX: 'start',\n            overlayY: 'bottom',\n            panelClass: 'mat-mdc-select-panel-above',\n        },\n        {\n            originX: 'end',\n            originY: 'top',\n            overlayX: 'end',\n            overlayY: 'bottom',\n            panelClass: 'mat-mdc-select-panel-above',\n        },\n    ];\n    /** Scrolls a particular option into the view. */\n    _scrollOptionIntoView(index) {\n        const option = this.options.toArray()[index];\n        if (option) {\n            const panel = this.panel.nativeElement;\n            const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n            const element = option._getHostElement();\n            if (index === 0 && labelCount === 1) {\n                // If we've got one group label before the option and we're at the top option,\n                // scroll the list to the top. This is better UX than scrolling the list to the\n                // top of the option, because it allows the user to read the top group's label.\n                panel.scrollTop = 0;\n            }\n            else {\n                panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n            }\n        }\n    }\n    /** Called when the panel has been opened and the overlay has settled on its final position. */\n    _positioningSettled() {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n    }\n    /** Creates a change event object that should be emitted by the select. */\n    _getChangeEvent(value) {\n        return new MatSelectChange(this, value);\n    }\n    /** Factory function used to create a scroll strategy for this select. */\n    _scrollStrategyFactory = inject(MAT_SELECT_SCROLL_STRATEGY);\n    /** Whether or not the overlay panel is open. */\n    _panelOpen = false;\n    /** Comparison function to specify which option is displayed. Defaults to object equality. */\n    _compareWith = (o1, o2) => o1 === o2;\n    /** Unique id for this input. */\n    _uid = this._idGenerator.getId('mat-select-');\n    /** Current `aria-labelledby` value for the select trigger. */\n    _triggerAriaLabelledBy = null;\n    /**\n     * Keeps track of the previous form control assigned to the select.\n     * Used to detect if it has changed.\n     */\n    _previousControl;\n    /** Emits whenever the component is destroyed. */\n    _destroy = new Subject();\n    /** Tracks the error state of the select. */\n    _errorStateTracker;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    stateChanges = new Subject();\n    /**\n     * Disable the automatic labeling to avoid issues like #27241.\n     * @docs-private\n     */\n    disableAutomaticLabeling = true;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    userAriaDescribedBy;\n    /** Deals with the selection logic. */\n    _selectionModel;\n    /** Manages keyboard events for options in the panel. */\n    _keyManager;\n    /** Ideal origin for the overlay panel. */\n    _preferredOverlayOrigin;\n    /** Width of the overlay panel. */\n    _overlayWidth;\n    /** `View -> model callback called when value changes` */\n    _onChange = () => { };\n    /** `View -> model callback called when select has been touched` */\n    _onTouched = () => { };\n    /** ID for the DOM node containing the select's value. */\n    _valueId = this._idGenerator.getId('mat-select-value-');\n    /** Strategy that will be used to handle scrolling while the select panel is open. */\n    _scrollStrategy;\n    _overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n    /** Whether the select is focused. */\n    get focused() {\n        return this._focused || this._panelOpen;\n    }\n    _focused = false;\n    /** A name for this control that can be used by `mat-form-field`. */\n    controlType = 'mat-select';\n    /** Trigger that opens the select. */\n    trigger;\n    /** Panel containing the select options. */\n    panel;\n    /** Overlay pane containing the options. */\n    _overlayDir;\n    /** Classes to be passed to the select panel. Supports the same syntax as `ngClass`. */\n    panelClass;\n    /** Whether the select is disabled. */\n    disabled = false;\n    /** Whether ripples in the select are disabled. */\n    disableRipple = false;\n    /** Tab index of the select. */\n    tabIndex = 0;\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = value;\n        this._syncParentProperties();\n    }\n    _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /** Placeholder to be shown if no value has been selected. */\n    get placeholder() {\n        return this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    _placeholder;\n    /** Whether the component is required. */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = value;\n        this.stateChanges.next();\n    }\n    _required;\n    /** Whether the user should be allowed to select multiple options. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectDynamicMultipleError();\n        }\n        this._multiple = value;\n    }\n    _multiple = false;\n    /** Whether to center the active option over the trigger. */\n    disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n    /**\n     * Function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n        return this._compareWith;\n    }\n    set compareWith(fn) {\n        if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectNonFunctionValueError();\n        }\n        this._compareWith = fn;\n        if (this._selectionModel) {\n            // A different comparator means the selection could change.\n            this._initializeSelection();\n        }\n    }\n    /** Value of the select control. */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        const hasAssigned = this._assignValue(newValue);\n        if (hasAssigned) {\n            this._onChange(newValue);\n        }\n    }\n    _value;\n    /** Aria label of the select. */\n    ariaLabel = '';\n    /** Input that can be used to specify the `aria-labelledby` attribute. */\n    ariaLabelledby;\n    /** Object used to control when error messages are shown. */\n    get errorStateMatcher() {\n        return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n        this._errorStateTracker.matcher = value;\n    }\n    /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n    typeaheadDebounceInterval;\n    /**\n     * Function used to sort the values in a select in multiple mode.\n     * Follows the same logic as `Array.prototype.sort`.\n     */\n    sortComparator;\n    /** Unique id of the element. */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n        this.stateChanges.next();\n    }\n    _id;\n    /** Whether the select is in an error state. */\n    get errorState() {\n        return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n        this._errorStateTracker.errorState = value;\n    }\n    /**\n     * Width of the panel. If set to `auto`, the panel will match the trigger width.\n     * If set to null or an empty string, the panel will grow to match the longest option's text.\n     */\n    panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined'\n        ? this._defaultOptions.panelWidth\n        : 'auto';\n    /**\n     * By default selecting an option with a `null` or `undefined` value will reset the select's\n     * value. Enable this option if the reset behavior doesn't match your requirements and instead\n     * the nullable options should become selected. The value of this input can be controlled app-wide\n     * using the `MAT_SELECT_CONFIG` injection token.\n     */\n    canSelectNullableOptions = this._defaultOptions?.canSelectNullableOptions ?? false;\n    /** Combined stream of all of the child options' change events. */\n    optionSelectionChanges = defer(() => {\n        const options = this.options;\n        if (options) {\n            return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n        }\n        return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n    });\n    /** Event emitted when the select panel has been toggled. */\n    openedChange = new EventEmitter();\n    /** Event emitted when the select has been opened. */\n    _openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n    /** Event emitted when the select has been closed. */\n    _closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n    /** Event emitted when the selected value has been changed by the user. */\n    selectionChange = new EventEmitter();\n    /**\n     * Event that emits whenever the raw value of the select changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    valueChange = new EventEmitter();\n    constructor() {\n        const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n        const parentForm = inject(NgForm, { optional: true });\n        const parentFormGroup = inject(FormGroupDirective, { optional: true });\n        const tabIndex = inject(new HostAttributeToken('tabindex'), { optional: true });\n        if (this.ngControl) {\n            // Note: we provide the value accessor through here, instead of\n            // the `providers` to avoid running into a circular import.\n            this.ngControl.valueAccessor = this;\n        }\n        // Note that we only want to set this when the defaults pass it in, otherwise it should\n        // stay as `undefined` so that it falls back to the default in the key manager.\n        if (this._defaultOptions?.typeaheadDebounceInterval != null) {\n            this.typeaheadDebounceInterval = this._defaultOptions.typeaheadDebounceInterval;\n        }\n        this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n        this._scrollStrategy = this._scrollStrategyFactory();\n        this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple);\n        this.stateChanges.next();\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroy))\n            .subscribe(() => {\n            if (this.panelOpen) {\n                this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n                this._changeDetectorRef.detectChanges();\n            }\n        });\n    }\n    ngAfterContentInit() {\n        this._initialized.next();\n        this._initialized.complete();\n        this._initKeyManager();\n        this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n            event.added.forEach(option => option.select());\n            event.removed.forEach(option => option.deselect());\n        });\n        this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n            this._resetOptions();\n            this._initializeSelection();\n        });\n    }\n    ngDoCheck() {\n        const newAriaLabelledby = this._getTriggerAriaLabelledby();\n        const ngControl = this.ngControl;\n        // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n        // is computed as a result of a content query which can cause this binding to trigger a\n        // \"changed after checked\" error.\n        if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n            const element = this._elementRef.nativeElement;\n            this._triggerAriaLabelledBy = newAriaLabelledby;\n            if (newAriaLabelledby) {\n                element.setAttribute('aria-labelledby', newAriaLabelledby);\n            }\n            else {\n                element.removeAttribute('aria-labelledby');\n            }\n        }\n        if (ngControl) {\n            // The disabled state might go out of sync if the form group is swapped out. See #17860.\n            if (this._previousControl !== ngControl.control) {\n                if (this._previousControl !== undefined &&\n                    ngControl.disabled !== null &&\n                    ngControl.disabled !== this.disabled) {\n                    this.disabled = ngControl.disabled;\n                }\n                this._previousControl = ngControl.control;\n            }\n            this.updateErrorState();\n        }\n    }\n    ngOnChanges(changes) {\n        // Updating the disabled state is handled by the input, but we need to additionally let\n        // the parent form field know to run change detection when the disabled state changes.\n        if (changes['disabled'] || changes['userAriaDescribedBy']) {\n            this.stateChanges.next();\n        }\n        if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n            this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n        }\n    }\n    ngOnDestroy() {\n        this._cleanupDetach?.();\n        this._keyManager?.destroy();\n        this._destroy.next();\n        this._destroy.complete();\n        this.stateChanges.complete();\n        this._clearFromModal();\n    }\n    /** Toggles the overlay panel open or closed. */\n    toggle() {\n        this.panelOpen ? this.close() : this.open();\n    }\n    /** Opens the overlay panel. */\n    open() {\n        if (!this._canOpen()) {\n            return;\n        }\n        // It's important that we read this as late as possible, because doing so earlier will\n        // return a different element since it's based on queries in the form field which may\n        // not have run yet. Also this needs to be assigned before we measure the overlay width.\n        if (this._parentFormField) {\n            this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n        }\n        this._cleanupDetach?.();\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        this._applyModalPanelOwnership();\n        this._panelOpen = true;\n        this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n            this._changeDetectorRef.detectChanges();\n            this._positioningSettled();\n        });\n        this._overlayDir.attachOverlay();\n        this._keyManager.withHorizontalOrientation(null);\n        this._highlightCorrectOption();\n        this._changeDetectorRef.markForCheck();\n        // Required for the MDC form field to pick up when the overlay has been opened.\n        this.stateChanges.next();\n        // Simulate the animation event before we moved away from `@angular/animations`.\n        Promise.resolve().then(() => this.openedChange.emit(true));\n    }\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    _trackedModal = null;\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        if (!modal) {\n            // Most commonly, the autocomplete trigger is not inside a modal.\n            return;\n        }\n        const panelId = `${this.id}-panel`;\n        if (this._trackedModal) {\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n        addAriaReferencedId(modal, 'aria-owns', panelId);\n        this._trackedModal = modal;\n    }\n    /** Clears the reference to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n        if (!this._trackedModal) {\n            // Most commonly, the autocomplete trigger is not used inside a modal.\n            return;\n        }\n        const panelId = `${this.id}-panel`;\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        this._trackedModal = null;\n    }\n    /** Closes the overlay panel and focuses the host element. */\n    close() {\n        if (this._panelOpen) {\n            this._panelOpen = false;\n            this._exitAndDetach();\n            this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n            this._changeDetectorRef.markForCheck();\n            this._onTouched();\n            // Required for the MDC form field to pick up when the overlay has been closed.\n            this.stateChanges.next();\n            // Simulate the animation event before we moved away from `@angular/animations`.\n            Promise.resolve().then(() => this.openedChange.emit(false));\n        }\n    }\n    /** Triggers the exit animation and detaches the overlay at the end. */\n    _exitAndDetach() {\n        if (this._animationsDisabled || !this.panel) {\n            this._detachOverlay();\n            return;\n        }\n        this._cleanupDetach?.();\n        this._cleanupDetach = () => {\n            cleanupEvent();\n            clearTimeout(exitFallbackTimer);\n            this._cleanupDetach = undefined;\n        };\n        const panel = this.panel.nativeElement;\n        const cleanupEvent = this._renderer.listen(panel, 'animationend', (event) => {\n            if (event.animationName === '_mat-select-exit') {\n                this._cleanupDetach?.();\n                this._detachOverlay();\n            }\n        });\n        // Since closing the overlay depends on the animation, we have a fallback in case the panel\n        // doesn't animate. This can happen in some internal tests that do `* {animation: none}`.\n        const exitFallbackTimer = setTimeout(() => {\n            this._cleanupDetach?.();\n            this._detachOverlay();\n        }, 200);\n        panel.classList.add('mat-select-panel-exit');\n    }\n    /** Detaches the current overlay directive. */\n    _detachOverlay() {\n        this._overlayDir.detachOverlay();\n        // Some of the overlay detachment logic depends on change detection.\n        // Mark for check to ensure that things get picked up in a timely manner.\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Sets the select's value. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param value New value to be written to the model.\n     */\n    writeValue(value) {\n        this._assignValue(value);\n    }\n    /**\n     * Saves a callback function to be invoked when the select's value\n     * changes from user input. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the value changes.\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Saves a callback function to be invoked when the select is blurred\n     * by the user. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the component has been touched.\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Disables the select. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param isDisabled Sets whether the component is disabled.\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    /** Whether or not the overlay panel is open. */\n    get panelOpen() {\n        return this._panelOpen;\n    }\n    /** The currently selected option. */\n    get selected() {\n        return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The value displayed in the trigger. */\n    get triggerValue() {\n        if (this.empty) {\n            return '';\n        }\n        if (this._multiple) {\n            const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n            if (this._isRtl()) {\n                selectedOptions.reverse();\n            }\n            // TODO(crisbeto): delimiter should be configurable for proper localization.\n            return selectedOptions.join(', ');\n        }\n        return this._selectionModel.selected[0].viewValue;\n    }\n    /** Refreshes the error state of the select. */\n    updateErrorState() {\n        this._errorStateTracker.updateErrorState();\n    }\n    /** Whether the element is in RTL mode. */\n    _isRtl() {\n        return this._dir ? this._dir.value === 'rtl' : false;\n    }\n    /** Handles all keydown events on the select. */\n    _handleKeydown(event) {\n        if (!this.disabled) {\n            this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n        }\n    }\n    /** Handles keyboard events while the select is closed. */\n    _handleClosedKeydown(event) {\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW ||\n            keyCode === UP_ARROW ||\n            keyCode === LEFT_ARROW ||\n            keyCode === RIGHT_ARROW;\n        const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n        const manager = this._keyManager;\n        // Open the select on ALT + arrow key to match the native <select>\n        if ((!manager.isTyping() && isOpenKey && !hasModifierKey(event)) ||\n            ((this.multiple || event.altKey) && isArrowKey)) {\n            event.preventDefault(); // prevents the page from scrolling down when pressing space\n            this.open();\n        }\n        else if (!this.multiple) {\n            const previouslySelectedOption = this.selected;\n            manager.onKeydown(event);\n            const selectedOption = this.selected;\n            // Since the value has changed, we need to announce it ourselves.\n            if (selectedOption && previouslySelectedOption !== selectedOption) {\n                // We set a duration on the live announcement, because we want the live element to be\n                // cleared after a while so that users can't navigate to it using the arrow keys.\n                this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n            }\n        }\n    }\n    /** Handles keyboard events when the selected is open. */\n    _handleOpenKeydown(event) {\n        const manager = this._keyManager;\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n        const isTyping = manager.isTyping();\n        if (isArrowKey && event.altKey) {\n            // Close the select on ALT + arrow key to match the native <select>\n            event.preventDefault();\n            this.close();\n            // Don't do anything in this case if the user is typing,\n            // because the typing sequence can include the space key.\n        }\n        else if (!isTyping &&\n            (keyCode === ENTER || keyCode === SPACE) &&\n            manager.activeItem &&\n            !hasModifierKey(event)) {\n            event.preventDefault();\n            manager.activeItem._selectViaInteraction();\n        }\n        else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n            event.preventDefault();\n            const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n            this.options.forEach(option => {\n                if (!option.disabled) {\n                    hasDeselectedOptions ? option.select() : option.deselect();\n                }\n            });\n        }\n        else {\n            const previouslyFocusedIndex = manager.activeItemIndex;\n            manager.onKeydown(event);\n            if (this._multiple &&\n                isArrowKey &&\n                event.shiftKey &&\n                manager.activeItem &&\n                manager.activeItemIndex !== previouslyFocusedIndex) {\n                manager.activeItem._selectViaInteraction();\n            }\n        }\n    }\n    /** Handles keyboard events coming from the overlay. */\n    _handleOverlayKeydown(event) {\n        // TODO(crisbeto): prior to #30363 this was being handled inside the overlay directive, but we\n        // need control over the animation timing so we do it manually. We should remove the `keydown`\n        // listener from `.mat-mdc-select-panel` and handle all the events here. That may cause\n        // further test breakages so it's left for a follow-up.\n        if (event.keyCode === ESCAPE && !hasModifierKey(event)) {\n            event.preventDefault();\n            this.close();\n        }\n    }\n    _onFocus() {\n        if (!this.disabled) {\n            this._focused = true;\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n     * \"blur\" to the panel when it opens, causing a false positive.\n     */\n    _onBlur() {\n        this._focused = false;\n        this._keyManager?.cancelTypeahead();\n        if (!this.disabled && !this.panelOpen) {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n            this.stateChanges.next();\n        }\n    }\n    /** Returns the theme to be used on the panel. */\n    _getPanelTheme() {\n        return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n    }\n    /** Whether the select has a value. */\n    get empty() {\n        return !this._selectionModel || this._selectionModel.isEmpty();\n    }\n    _initializeSelection() {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n            if (this.ngControl) {\n                this._value = this.ngControl.value;\n            }\n            this._setSelectionByValue(this._value);\n            this.stateChanges.next();\n        });\n    }\n    /**\n     * Sets the selected option based on a value. If no option can be\n     * found with the designated value, the select trigger is cleared.\n     */\n    _setSelectionByValue(value) {\n        this.options.forEach(option => option.setInactiveStyles());\n        this._selectionModel.clear();\n        if (this.multiple && value) {\n            if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getMatSelectNonArrayValueError();\n            }\n            value.forEach((currentValue) => this._selectOptionByValue(currentValue));\n            this._sortValues();\n        }\n        else {\n            const correspondingOption = this._selectOptionByValue(value);\n            // Shift focus to the active item. Note that we shouldn't do this in multiple\n            // mode, because we don't know what option the user interacted with last.\n            if (correspondingOption) {\n                this._keyManager.updateActiveItem(correspondingOption);\n            }\n            else if (!this.panelOpen) {\n                // Otherwise reset the highlighted option. Note that we only want to do this while\n                // closed, because doing it while open can shift the user's focus unnecessarily.\n                this._keyManager.updateActiveItem(-1);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Finds and selects and option based on its value.\n     * @returns Option that has the corresponding value.\n     */\n    _selectOptionByValue(value) {\n        const correspondingOption = this.options.find((option) => {\n            // Skip options that are already in the model. This allows us to handle cases\n            // where the same primitive value is selected multiple times.\n            if (this._selectionModel.isSelected(option)) {\n                return false;\n            }\n            try {\n                // Treat null as a special reset value.\n                return ((option.value != null || this.canSelectNullableOptions) &&\n                    this._compareWith(option.value, value));\n            }\n            catch (error) {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    // Notify developers of errors in their comparator.\n                    console.warn(error);\n                }\n                return false;\n            }\n        });\n        if (correspondingOption) {\n            this._selectionModel.select(correspondingOption);\n        }\n        return correspondingOption;\n    }\n    /** Assigns a specific value to the select. Returns whether the value has changed. */\n    _assignValue(newValue) {\n        // Always re-assign an array, because it might have been mutated.\n        if (newValue !== this._value || (this._multiple && Array.isArray(newValue))) {\n            if (this.options) {\n                this._setSelectionByValue(newValue);\n            }\n            this._value = newValue;\n            return true;\n        }\n        return false;\n    }\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    _skipPredicate = (option) => {\n        if (this.panelOpen) {\n            // Support keyboard focusing disabled options in an ARIA listbox.\n            return false;\n        }\n        // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n        // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n        // closed.\n        return option.disabled;\n    };\n    /** Gets how wide the overlay panel should be. */\n    _getOverlayWidth(preferredOrigin) {\n        if (this.panelWidth === 'auto') {\n            const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin\n                ? preferredOrigin.elementRef\n                : preferredOrigin || this._elementRef;\n            return refToMeasure.nativeElement.getBoundingClientRect().width;\n        }\n        return this.panelWidth === null ? '' : this.panelWidth;\n    }\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n        if (this.options) {\n            for (const option of this.options) {\n                option._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n    _initKeyManager() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options)\n            .withTypeAhead(this.typeaheadDebounceInterval)\n            .withVerticalOrientation()\n            .withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr')\n            .withHomeAndEnd()\n            .withPageUpDown()\n            .withAllowedModifierKeys(['shiftKey'])\n            .skipPredicate(this._skipPredicate);\n        this._keyManager.tabOut.subscribe(() => {\n            if (this.panelOpen) {\n                // Select the active item when tabbing away. This is consistent with how the native\n                // select behaves. Note that we only want to do this in single selection mode.\n                if (!this.multiple && this._keyManager.activeItem) {\n                    this._keyManager.activeItem._selectViaInteraction();\n                }\n                // Restore focus to the trigger before closing. Ensures that the focus\n                // position won't be lost if the user got focus into the overlay.\n                this.focus();\n                this.close();\n            }\n        });\n        this._keyManager.change.subscribe(() => {\n            if (this._panelOpen && this.panel) {\n                this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n            }\n            else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n                this._keyManager.activeItem._selectViaInteraction();\n            }\n        });\n    }\n    /** Drops current option subscriptions and IDs and resets from scratch. */\n    _resetOptions() {\n        const changedOrDestroyed = merge(this.options.changes, this._destroy);\n        this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n            this._onSelect(event.source, event.isUserInput);\n            if (event.isUserInput && !this.multiple && this._panelOpen) {\n                this.close();\n                this.focus();\n            }\n        });\n        // Listen to changes in the internal state of the options and react accordingly.\n        // Handles cases like the labels of the selected options changing.\n        merge(...this.options.map(option => option._stateChanges))\n            .pipe(takeUntil(changedOrDestroyed))\n            .subscribe(() => {\n            // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n            // be the result of an expression changing. We have to use `detectChanges` in order\n            // to avoid \"changed after checked\" errors (see #14793).\n            this._changeDetectorRef.detectChanges();\n            this.stateChanges.next();\n        });\n    }\n    /** Invoked when an option is clicked. */\n    _onSelect(option, isUserInput) {\n        const wasSelected = this._selectionModel.isSelected(option);\n        if (!this.canSelectNullableOptions && option.value == null && !this._multiple) {\n            option.deselect();\n            this._selectionModel.clear();\n            if (this.value != null) {\n                this._propagateChanges(option.value);\n            }\n        }\n        else {\n            if (wasSelected !== option.selected) {\n                option.selected\n                    ? this._selectionModel.select(option)\n                    : this._selectionModel.deselect(option);\n            }\n            if (isUserInput) {\n                this._keyManager.setActiveItem(option);\n            }\n            if (this.multiple) {\n                this._sortValues();\n                if (isUserInput) {\n                    // In case the user selected the option with their mouse, we\n                    // want to restore focus back to the trigger, in order to\n                    // prevent the select keyboard controls from clashing with\n                    // the ones from `mat-option`.\n                    this.focus();\n                }\n            }\n        }\n        if (wasSelected !== this._selectionModel.isSelected(option)) {\n            this._propagateChanges();\n        }\n        this.stateChanges.next();\n    }\n    /** Sorts the selected values in the selected based on their order in the panel. */\n    _sortValues() {\n        if (this.multiple) {\n            const options = this.options.toArray();\n            this._selectionModel.sort((a, b) => {\n                return this.sortComparator\n                    ? this.sortComparator(a, b, options)\n                    : options.indexOf(a) - options.indexOf(b);\n            });\n            this.stateChanges.next();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n        let valueToEmit;\n        if (this.multiple) {\n            valueToEmit = this.selected.map(option => option.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : fallbackValue;\n        }\n        this._value = valueToEmit;\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Highlights the selected item. If no option is selected, it will highlight\n     * the first *enabled* option.\n     */\n    _highlightCorrectOption() {\n        if (this._keyManager) {\n            if (this.empty) {\n                // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n                // because it activates the first option that passes the skip predicate, rather than the\n                // first *enabled* option.\n                let firstEnabledOptionIndex = -1;\n                for (let index = 0; index < this.options.length; index++) {\n                    const option = this.options.get(index);\n                    if (!option.disabled) {\n                        firstEnabledOptionIndex = index;\n                        break;\n                    }\n                }\n                this._keyManager.setActiveItem(firstEnabledOptionIndex);\n            }\n            else {\n                this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n            }\n        }\n    }\n    /** Whether the panel is allowed to open. */\n    _canOpen() {\n        return !this._panelOpen && !this.disabled && this.options?.length > 0 && !!this._overlayDir;\n    }\n    /** Focuses the select element. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Gets the aria-labelledby for the select panel. */\n    _getPanelAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId() || null;\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Determines the `aria-activedescendant` to be set on the host. */\n    _getAriaActiveDescendant() {\n        if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n            return this._keyManager.activeItem.id;\n        }\n        return null;\n    }\n    /** Gets the aria-labelledby of the select component trigger. */\n    _getTriggerAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        let value = this._parentFormField?.getLabelId() || '';\n        if (this.ariaLabelledby) {\n            value += ' ' + this.ariaLabelledby;\n        }\n        // The value should not be used for the trigger's aria-labelledby,\n        // but this currently \"breaks\" accessibility tests since they complain\n        // there is no aria-labelledby. This is because they are not setting an\n        // appropriate label on the form field or select.\n        // TODO: remove this conditional after fixing clients by ensuring their\n        // selects have a label applied.\n        if (!value) {\n            value = this._valueId;\n        }\n        return value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        if (ids.length) {\n            this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            this._elementRef.nativeElement.removeAttribute('aria-describedby');\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        this.focus();\n        this.open();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        // Since the panel doesn't overlap the trigger, we\n        // want the label to only float when there's a value.\n        return this.panelOpen || !this.empty || (this.focused && !!this.placeholder);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSelect, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatSelect, isStandalone: true, selector: \"mat-select\", inputs: { userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], panelClass: \"panelClass\", disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], hideSingleSelectionIndicator: [\"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute], placeholder: \"placeholder\", required: [\"required\", \"required\", booleanAttribute], multiple: [\"multiple\", \"multiple\", booleanAttribute], disableOptionCentering: [\"disableOptionCentering\", \"disableOptionCentering\", booleanAttribute], compareWith: \"compareWith\", value: \"value\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], errorStateMatcher: \"errorStateMatcher\", typeaheadDebounceInterval: [\"typeaheadDebounceInterval\", \"typeaheadDebounceInterval\", numberAttribute], sortComparator: \"sortComparator\", id: \"id\", panelWidth: \"panelWidth\", canSelectNullableOptions: [\"canSelectNullableOptions\", \"canSelectNullableOptions\", booleanAttribute] }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", _closedStream: \"closed\", selectionChange: \"selectionChange\", valueChange: \"valueChange\" }, host: { attributes: { \"role\": \"combobox\", \"aria-haspopup\": \"listbox\" }, listeners: { \"keydown\": \"_handleKeydown($event)\", \"focus\": \"_onFocus()\", \"blur\": \"_onBlur()\" }, properties: { \"attr.id\": \"id\", \"attr.tabindex\": \"disabled ? -1 : tabIndex\", \"attr.aria-controls\": \"panelOpen ? id + \\\"-panel\\\" : null\", \"attr.aria-expanded\": \"panelOpen\", \"attr.aria-label\": \"ariaLabel || null\", \"attr.aria-required\": \"required.toString()\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"attr.aria-activedescendant\": \"_getAriaActiveDescendant()\", \"class.mat-mdc-select-disabled\": \"disabled\", \"class.mat-mdc-select-invalid\": \"errorState\", \"class.mat-mdc-select-required\": \"required\", \"class.mat-mdc-select-empty\": \"empty\", \"class.mat-mdc-select-multiple\": \"multiple\" }, classAttribute: \"mat-mdc-select\" }, providers: [\n            { provide: MatFormFieldControl, useExisting: MatSelect },\n            { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n        ], queries: [{ propertyName: \"customTrigger\", first: true, predicate: MAT_SELECT_TRIGGER, descendants: true }, { propertyName: \"options\", predicate: MatOption, descendants: true }, { propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }], viewQueries: [{ propertyName: \"trigger\", first: true, predicate: [\"trigger\"], descendants: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }, { propertyName: \"_overlayDir\", first: true, predicate: CdkConnectedOverlay, descendants: true }], exportAs: [\"matSelect\"], usesOnChanges: true, ngImport: i0, template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayDisableClose]=\\\"true\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  [cdkConnectedOverlayFlexibleDimensions]=\\\"true\\\"\\n  (detach)=\\\"close()\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (overlayKeydown)=\\\"_handleOverlayKeydown($event)\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [class.mat-select-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"], dependencies: [{ kind: \"directive\", type: CdkOverlayOrigin, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"] }, { kind: \"directive\", type: CdkConnectedOverlay, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: [\"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPositionStrategy\", \"cdkConnectedOverlayOffsetX\", \"cdkConnectedOverlayOffsetY\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayHeight\", \"cdkConnectedOverlayMinWidth\", \"cdkConnectedOverlayMinHeight\", \"cdkConnectedOverlayBackdropClass\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayViewportMargin\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayTransformOriginOn\", \"cdkConnectedOverlayHasBackdrop\", \"cdkConnectedOverlayLockPosition\", \"cdkConnectedOverlayFlexibleDimensions\", \"cdkConnectedOverlayGrowAfterOpen\", \"cdkConnectedOverlayPush\", \"cdkConnectedOverlayDisposeOnNavigation\"], outputs: [\"backdropClick\", \"positionChange\", \"attach\", \"detach\", \"overlayKeydown\", \"overlayOutsideClick\"], exportAs: [\"cdkConnectedOverlay\"] }, { kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSelect, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-select', exportAs: 'matSelect', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'role': 'combobox',\n                        'aria-haspopup': 'listbox',\n                        'class': 'mat-mdc-select',\n                        '[attr.id]': 'id',\n                        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n                        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n                        '[attr.aria-expanded]': 'panelOpen',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.aria-required]': 'required.toString()',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n                        '[class.mat-mdc-select-disabled]': 'disabled',\n                        '[class.mat-mdc-select-invalid]': 'errorState',\n                        '[class.mat-mdc-select-required]': 'required',\n                        '[class.mat-mdc-select-empty]': 'empty',\n                        '[class.mat-mdc-select-multiple]': 'multiple',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(focus)': '_onFocus()',\n                        '(blur)': '_onBlur()',\n                    }, providers: [\n                        { provide: MatFormFieldControl, useExisting: MatSelect },\n                        { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n                    ], imports: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass], template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayDisableClose]=\\\"true\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  [cdkConnectedOverlayFlexibleDimensions]=\\\"true\\\"\\n  (detach)=\\\"close()\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (overlayKeydown)=\\\"_handleOverlayKeydown($event)\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [class.mat-select-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }], optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], customTrigger: [{\n                type: ContentChild,\n                args: [MAT_SELECT_TRIGGER]\n            }], userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], trigger: [{\n                type: ViewChild,\n                args: ['trigger']\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], _overlayDir: [{\n                type: ViewChild,\n                args: [CdkConnectedOverlay]\n            }], panelClass: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], hideSingleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], multiple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableOptionCentering: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], compareWith: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], errorStateMatcher: [{\n                type: Input\n            }], typeaheadDebounceInterval: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], sortComparator: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], panelWidth: [{\n                type: Input\n            }], canSelectNullableOptions: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], selectionChange: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }] } });\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSelectTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatSelectTrigger, isStandalone: true, selector: \"mat-select-trigger\", providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSelectTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-select-trigger',\n                    providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }],\n                }]\n        }] });\n\nclass MatSelectModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSelectModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSelectModule, imports: [OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger], exports: [CdkScrollableModule,\n            MatFormFieldModule,\n            MatSelect,\n            MatSelectTrigger,\n            MatOptionModule,\n            MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSelectModule, providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER], imports: [OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule,\n            MatFormFieldModule,\n            MatOptionModule,\n            MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSelectModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger],\n                    exports: [\n                        CdkScrollableModule,\n                        MatFormFieldModule,\n                        MatSelect,\n                        MatSelectTrigger,\n                        MatOptionModule,\n                        MatCommonModule,\n                    ],\n                    providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\nexport { MatSelectModule as M, MAT_SELECT_SCROLL_STRATEGY as a, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY as b, MAT_SELECT_CONFIG as c, MAT_SELECT_SCROLL_STRATEGY_PROVIDER as d, MAT_SELECT_TRIGGER as e, MatSelectChange as f, MatSelect as g, MatSelectTrigger as h };\n//# sourceMappingURL=module-Cbt8Fcmv.mjs.map\n"], "mappings": ";;;;;;;;IAunCiFA,EAAE,CAAAC,cAAA,aAF60B,CAAC;IAEh1BD,EAAE,CAAAE,MAAA,EAF41B,CAAC;IAE/1BF,EAAE,CAAAG,YAAA,CAFm2B,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAC,MAAA,GAEt2BL,EAAE,CAAAM,aAAA;IAAFN,EAAE,CAAAO,SAAA,CAF41B,CAAC;IAE/1BP,EAAE,CAAAQ,iBAAA,CAAAH,MAAA,CAAAI,WAF41B,CAAC;EAAA;AAAA;AAAA,SAAAC,gDAAAN,EAAA,EAAAO,GAAA;EAAA,IAAAP,EAAA;IAE/1BJ,EAAE,CAAAY,YAAA,EAFsgC,CAAC;EAAA;AAAA;AAAA,SAAAC,gDAAAT,EAAA,EAAAO,GAAA;EAAA,IAAAP,EAAA;IAEzgCJ,EAAE,CAAAC,cAAA,cAF6kC,CAAC;IAEhlCD,EAAE,CAAAE,MAAA,EAF6lC,CAAC;IAEhmCF,EAAE,CAAAG,YAAA,CAFomC,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAC,MAAA,GAEvmCL,EAAE,CAAAM,aAAA;IAAFN,EAAE,CAAAO,SAAA,CAF6lC,CAAC;IAEhmCP,EAAE,CAAAQ,iBAAA,CAAAH,MAAA,CAAAS,YAF6lC,CAAC;EAAA;AAAA;AAAA,SAAAC,kCAAAX,EAAA,EAAAO,GAAA;EAAA,IAAAP,EAAA;IAEhmCJ,EAAE,CAAAC,cAAA,aAFo6B,CAAC;IAEv6BD,EAAE,CAAAgB,UAAA,IAAAN,+CAAA,MAFm8B,CAAC,IAAAG,+CAAA,kBAAqF,CAAC;IAE5hCb,EAAE,CAAAG,YAAA,CAF8nC,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAC,MAAA,GAEjoCL,EAAE,CAAAM,aAAA;IAAFN,EAAE,CAAAO,SAAA,CAF+mC,CAAC;IAElnCP,EAAE,CAAAiB,aAAA,CAAAZ,MAAA,CAAAa,aAAA,QAF+mC,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAAf,EAAA,EAAAO,GAAA;EAAA,IAAAP,EAAA;IAAA,MAAAgB,GAAA,GAElnCpB,EAAE,CAAAqB,gBAAA;IAAFrB,EAAE,CAAAC,cAAA,gBAF0qF,CAAC;IAE7qFD,EAAE,CAAAsB,UAAA,qBAAAC,0DAAAC,MAAA;MAAFxB,EAAE,CAAAyB,aAAA,CAAAL,GAAA;MAAA,MAAAf,MAAA,GAAFL,EAAE,CAAAM,aAAA;MAAA,OAAFN,EAAE,CAAA0B,WAAA,CAFkpFrB,MAAA,CAAAsB,cAAA,CAAAH,MAAqB,CAAC;IAAA,CAAC,CAAC;IAE5qFxB,EAAE,CAAAY,YAAA,KAFysF,CAAC;IAE5sFZ,EAAE,CAAAG,YAAA,CAFmtF,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAC,MAAA,GAEttFL,EAAE,CAAAM,aAAA;IAAFN,EAAE,CAAA4B,sBAAA,kEAAAvB,MAAA,CAAAwB,cAAA,MAFi2E,CAAC;IAEp2E7B,EAAE,CAAA8B,WAAA,yCAAAzB,MAAA,CAAA0B,mBAF26E,CAAC;IAE96E/B,EAAE,CAAAgC,UAAA,YAAA3B,MAAA,CAAA4B,UAF+nF,CAAC;IAEloFjC,EAAE,CAAAkC,WAAA,OAAA7B,MAAA,CAAA8B,EAAA,qCAAA9B,MAAA,CAAA+B,QAAA,gBAAA/B,MAAA,CAAAgC,SAAA,6BAAAhC,MAAA,CAAAiC,uBAAA;EAAA;AAAA;AAvnCnF,SAASC,OAAO,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,sBAAsB;AACpG,OAAO,KAAK1C,EAAE,MAAM,eAAe;AACnC,SAAS2C,cAAc,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACjU,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC3E,SAASC,YAAY,EAAEC,aAAa,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,0BAA0B,QAAQ,mBAAmB;AACxI,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,cAAc,EAAEC,KAAK,EAAEC,KAAK,EAAEC,CAAC,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,QAAQ,uBAAuB;AAC9H,SAASC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,QAAQ,gBAAgB;AAClF,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AACnF,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,mBAAmB,QAAQ,2BAA2B;AACzF,SAASC,CAAC,IAAIC,6BAA6B,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,YAAY,QAAQ,uBAAuB;AAC9K,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,8BAA8B;AACrE,SAASX,CAAC,IAAIY,kBAAkB,QAAQ,4BAA4B;AACpE,SAASN,CAAC,IAAIO,eAAe,QAAQ,sBAAsB;AAC3D,SAASP,CAAC,IAAIQ,eAAe,QAAQ,8BAA8B;AACnE,SAASR,CAAC,IAAIS,kBAAkB,QAAQ,uBAAuB;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gCAAgCA,CAAA,EAAG;EACxC,OAAOC,KAAK,CAAC,+DAA+D,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,8BAA8BA,CAAA,EAAG;EACtC,OAAOD,KAAK,CAAC,oDAAoD,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iCAAiCA,CAAA,EAAG;EACzC,OAAOF,KAAK,CAAC,mCAAmC,CAAC;AACrD;;AAEA;AACA,MAAMG,0BAA0B,GAAG,IAAI5E,cAAc,CAAC,4BAA4B,EAAE;EAChF6E,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,OAAO,GAAG9E,MAAM,CAACL,OAAO,CAAC;IAC/B,OAAO,MAAMmF,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;EACtD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASC,2CAA2CA,CAACH,OAAO,EAAE;EAC1D,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;AACtD;AACA;AACA,MAAME,iBAAiB,GAAG,IAAInF,cAAc,CAAC,mBAAmB,CAAC;AACjE;AACA;AACA;AACA;AACA;AACA,MAAMoF,mCAAmC,GAAG;EACxCC,OAAO,EAAET,0BAA0B;EACnCU,IAAI,EAAE,CAAC1F,OAAO,CAAC;EACf2F,UAAU,EAAEL;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMM,kBAAkB,GAAG,IAAIxF,cAAc,CAAC,kBAAkB,CAAC;AACjE;AACA,MAAMyF,eAAe,CAAC;EAGlBC,WAAWA,CACX;EACAC,MAAM,EACN;EACAC,KAAK,EAAE;IAAAC,eAAA;IAAAA,eAAA;IACH,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA,MAAME,SAAS,CAAC;EAwDZ;EACAC,qBAAqBA,CAACC,KAAK,EAAE;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACH,KAAK,CAAC;IAC5C,IAAIC,MAAM,EAAE;MACR,MAAMG,KAAK,GAAG,IAAI,CAACA,KAAK,CAACC,aAAa;MACtC,MAAMC,UAAU,GAAG7C,6BAA6B,CAACuC,KAAK,EAAE,IAAI,CAACE,OAAO,EAAE,IAAI,CAACK,YAAY,CAAC;MACxF,MAAMC,OAAO,GAAGP,MAAM,CAACQ,eAAe,CAAC,CAAC;MACxC,IAAIT,KAAK,KAAK,CAAC,IAAIM,UAAU,KAAK,CAAC,EAAE;QACjC;QACA;QACA;QACAF,KAAK,CAACM,SAAS,GAAG,CAAC;MACvB,CAAC,MACI;QACDN,KAAK,CAACM,SAAS,GAAG/C,wBAAwB,CAAC6C,OAAO,CAACG,SAAS,EAAEH,OAAO,CAACI,YAAY,EAAER,KAAK,CAACM,SAAS,EAAEN,KAAK,CAACQ,YAAY,CAAC;MAC5H;IACJ;EACJ;EACA;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACd,qBAAqB,CAAC,IAAI,CAACe,WAAW,CAACC,eAAe,IAAI,CAAC,CAAC;EACrE;EACA;EACAC,eAAeA,CAACpB,KAAK,EAAE;IACnB,OAAO,IAAIH,eAAe,CAAC,IAAI,EAAEG,KAAK,CAAC;EAC3C;EACA;;EAoDA;EACA,IAAIqB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACC,UAAU;EAC3C;EAkBA;EACA,IAAIC,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACC,6BAA6B;EAC7C;EACA,IAAID,4BAA4BA,CAACxB,KAAK,EAAE;IACpC,IAAI,CAACyB,6BAA6B,GAAGzB,KAAK;IAC1C,IAAI,CAAC0B,qBAAqB,CAAC,CAAC;EAChC;EAEA;EACA,IAAIxJ,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACyJ,YAAY;EAC5B;EACA,IAAIzJ,WAAWA,CAAC8H,KAAK,EAAE;IACnB,IAAI,CAAC2B,YAAY,GAAG3B,KAAK;IACzB,IAAI,CAAC4B,YAAY,CAACC,IAAI,CAAC,CAAC;EAC5B;EAEA;EACA,IAAIC,QAAQA,CAAA,EAAG;IAAA,IAAAC,IAAA,EAAAC,eAAA,EAAAC,eAAA;IACX,QAAAF,IAAA,IAAAC,eAAA,GAAO,IAAI,CAACE,SAAS,cAAAF,eAAA,cAAAA,eAAA,IAAAC,eAAA,GAAI,IAAI,CAACE,SAAS,cAAAF,eAAA,gBAAAA,eAAA,GAAdA,eAAA,CAAgBG,OAAO,cAAAH,eAAA,uBAAvBA,eAAA,CAAyBI,YAAY,CAAC1F,UAAU,CAACmF,QAAQ,CAAC,cAAAC,IAAA,cAAAA,IAAA,GAAI,KAAK;EAChG;EACA,IAAID,QAAQA,CAAC9B,KAAK,EAAE;IAChB,IAAI,CAACkC,SAAS,GAAGlC,KAAK;IACtB,IAAI,CAAC4B,YAAY,CAACC,IAAI,CAAC,CAAC;EAC5B;EAEA;EACA,IAAIhI,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACyI,SAAS;EACzB;EACA,IAAIzI,QAAQA,CAACmG,KAAK,EAAE;IAChB,IAAI,IAAI,CAACuC,eAAe,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzE,MAAM5D,gCAAgC,CAAC,CAAC;IAC5C;IACA,IAAI,CAAC0D,SAAS,GAAGtC,KAAK;EAC1B;EAIA;AACJ;AACA;AACA;AACA;EACI,IAAIyC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACE,EAAE,EAAE;IAChB,IAAI,OAAOA,EAAE,KAAK,UAAU,KAAK,OAAOH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E,MAAMzD,iCAAiC,CAAC,CAAC;IAC7C;IACA,IAAI,CAAC2D,YAAY,GAAGC,EAAE;IACtB,IAAI,IAAI,CAACJ,eAAe,EAAE;MACtB;MACA,IAAI,CAACK,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;EACA,IAAI5C,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC6C,MAAM;EACtB;EACA,IAAI7C,KAAKA,CAAC8C,QAAQ,EAAE;IAChB,MAAMC,WAAW,GAAG,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC;IAC/C,IAAIC,WAAW,EAAE;MACb,IAAI,CAACE,SAAS,CAACH,QAAQ,CAAC;IAC5B;EACJ;EAMA;EACA,IAAII,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB,CAACC,OAAO;EAC1C;EACA,IAAIF,iBAAiBA,CAAClD,KAAK,EAAE;IACzB,IAAI,CAACmD,kBAAkB,CAACC,OAAO,GAAGpD,KAAK;EAC3C;EACA;;EAOA;EACA,IAAIpG,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACyJ,GAAG;EACnB;EACA,IAAIzJ,EAAEA,CAACoG,KAAK,EAAE;IACV,IAAI,CAACqD,GAAG,GAAGrD,KAAK,IAAI,IAAI,CAACsD,IAAI;IAC7B,IAAI,CAAC1B,YAAY,CAACC,IAAI,CAAC,CAAC;EAC5B;EAEA;EACA,IAAI0B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,kBAAkB,CAACI,UAAU;EAC7C;EACA,IAAIA,UAAUA,CAACvD,KAAK,EAAE;IAClB,IAAI,CAACmD,kBAAkB,CAACI,UAAU,GAAGvD,KAAK;EAC9C;EACA;AACJ;AACA;AACA;;EAiCIF,WAAWA,CAAA,EAAG;IAAA,IAAA0D,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAAA9D,eAAA,yBArSG5F,MAAM,CAACmB,aAAa,CAAC;IAAAyE,eAAA,6BACjB5F,MAAM,CAACC,iBAAiB,CAAC;IAAA2F,eAAA,sBAChC5F,MAAM,CAACE,UAAU,CAAC;IAAA0F,eAAA,eACzB5F,MAAM,CAAC0B,cAAc,EAAE;MAAEiI,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA/D,eAAA,uBAClC5F,MAAM,CAACqB,YAAY,CAAC;IAAAuE,eAAA,oBACvB5F,MAAM,CAACG,SAAS,CAAC;IAAAyF,eAAA,2BACV5F,MAAM,CAACoD,cAAc,EAAE;MAAEuG,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA/D,eAAA,oBACjD5F,MAAM,CAACqC,SAAS,EAAE;MAAEuH,IAAI,EAAE,IAAI;MAAED,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA/D,eAAA,yBAC5C5F,MAAM,CAACsB,aAAa,CAAC;IAAAsE,eAAA,0BACpB5F,MAAM,CAACkF,iBAAiB,EAAE;MAAEyE,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA/D,eAAA,8BACzC5F,MAAM,CAACI,qBAAqB,EAAE;MAAEuJ,QAAQ,EAAE;IAAK,CAAC,CAAC,KAAK,gBAAgB;IAAA/D,eAAA,uBAC7E,IAAInD,OAAO,CAAC,CAAC;IAAAmD,eAAA;IAE5B;IAAAA,eAAA;IAEA;IACA;IACA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,qBAMa,CACT;MACIiE,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACd,CAAC,EACD;MACIH,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;IACd,CAAC,EACD;MACIH,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,QAAQ;MAClB3K,UAAU,EAAE;IAChB,CAAC,EACD;MACIwK,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,QAAQ;MAClB3K,UAAU,EAAE;IAChB,CAAC,CACJ;IAAAuG,eAAA,iCA4BwB5F,MAAM,CAAC2E,0BAA0B,CAAC;IAC3D;IAAAiB,eAAA,qBACa,KAAK;IAClB;IAAAA,eAAA,uBACe,CAACqE,EAAE,EAAEC,EAAE,KAAKD,EAAE,KAAKC,EAAE;IACpC;IAAAtE,eAAA,eACO,IAAI,CAACuE,YAAY,CAACC,KAAK,CAAC,aAAa,CAAC;IAC7C;IAAAxE,eAAA,iCACyB,IAAI;IAC7B;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA,mBACW,IAAInD,OAAO,CAAC,CAAC;IACxB;IAAAmD,eAAA;IAEA;AACJ;AACA;AACA;AACA;IAJIA,eAAA,uBAKe,IAAInD,OAAO,CAAC,CAAC;IAC5B;AACJ;AACA;AACA;IAHImD,eAAA,mCAI2B,IAAI;IAC/B;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,oBACY,MAAM,CAAE,CAAC;IACrB;IAAAA,eAAA,qBACa,MAAM,CAAE,CAAC;IACtB;IAAAA,eAAA,mBACW,IAAI,CAACuE,YAAY,CAACC,KAAK,CAAC,mBAAmB,CAAC;IACvD;IAAAxE,eAAA;IAAAA,eAAA,6BAEqB,EAAAuD,qBAAA,OAAI,CAACkB,eAAe,cAAAlB,qBAAA,uBAApBA,qBAAA,CAAsBmB,iBAAiB,KAAI,EAAE;IAAA1E,eAAA,mBAKvD,KAAK;IAChB;IAAAA,eAAA,sBACc,YAAY;IAC1B;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,mBACW,KAAK;IAChB;IAAAA,eAAA,wBACgB,KAAK;IACrB;IAAAA,eAAA,mBACW,CAAC;IAAAA,eAAA,yCAAAwD,sBAAA,IAAAC,sBAAA,GASoB,IAAI,CAACgB,eAAe,cAAAhB,sBAAA,uBAApBA,sBAAA,CAAsBlC,4BAA4B,cAAAiC,sBAAA,cAAAA,sBAAA,GAAI,KAAK;IAAAxD,eAAA;IAAAA,eAAA;IAAAA,eAAA,oBA6B/E,KAAK;IACjB;IAAAA,eAAA,kCAAA0D,sBAAA,IAAAC,sBAAA,GACyB,IAAI,CAACc,eAAe,cAAAd,sBAAA,uBAApBA,sBAAA,CAAsBgB,sBAAsB,cAAAjB,sBAAA,cAAAA,sBAAA,GAAI,KAAK;IAAA1D,eAAA;IA8B9E;IAAAA,eAAA,oBACY,EAAE;IACd;IAAAA,eAAA;IAAAA,eAAA;IAWA;AACJ;AACA;AACA;IAHIA,eAAA;IAAAA,eAAA;IAAAA,eAAA,qBAyBa,IAAI,CAACyE,eAAe,IAAI,OAAO,IAAI,CAACA,eAAe,CAACG,UAAU,KAAK,WAAW,GACrF,IAAI,CAACH,eAAe,CAACG,UAAU,GAC/B,MAAM;IACZ;AACJ;AACA;AACA;AACA;AACA;IALI5E,eAAA,oCAAA4D,sBAAA,IAAAC,sBAAA,GAM2B,IAAI,CAACY,eAAe,cAAAZ,sBAAA,uBAApBA,sBAAA,CAAsBgB,wBAAwB,cAAAjB,sBAAA,cAAAA,sBAAA,GAAI,KAAK;IAClF;IAAA5D,eAAA,iCACyBlD,KAAK,CAAC,MAAM;MACjC,MAAMuD,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACT,OAAOA,OAAO,CAACyE,OAAO,CAACC,IAAI,CAAC/H,SAAS,CAACqD,OAAO,CAAC,EAAEpD,SAAS,CAAC,MAAMF,KAAK,CAAC,GAAGsD,OAAO,CAAClD,GAAG,CAACiD,MAAM,IAAIA,MAAM,CAAC4E,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC/H;MACA,OAAO,IAAI,CAACC,YAAY,CAACF,IAAI,CAAC9H,SAAS,CAAC,MAAM,IAAI,CAACiI,sBAAsB,CAAC,CAAC;IAC/E,CAAC,CAAC;IACF;IAAAlF,eAAA,uBACe,IAAIvF,YAAY,CAAC,CAAC;IACjC;IAAAuF,eAAA,wBACgB,IAAI,CAACmF,YAAY,CAACJ,IAAI,CAAC7H,MAAM,CAACkI,CAAC,IAAIA,CAAC,CAAC,EAAEjI,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IACtE;IAAA6C,eAAA,wBACgB,IAAI,CAACmF,YAAY,CAACJ,IAAI,CAAC7H,MAAM,CAACkI,CAAC,IAAI,CAACA,CAAC,CAAC,EAAEjI,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IACvE;IAAA6C,eAAA,0BACkB,IAAIvF,YAAY,CAAC,CAAC;IACpC;AACJ;AACA;AACA;AACA;IAJIuF,eAAA,sBAKc,IAAIvF,YAAY,CAAC,CAAC;IA+HhC;AACJ;AACA;AACA;AACA;IAJIuF,eAAA,wBAKgB,IAAI;IAyWpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAAAA,eAAA,yBACkBI,MAAM,IAAK;MACzB,IAAI,IAAI,CAACiF,SAAS,EAAE;QAChB;QACA,OAAO,KAAK;MAChB;MACA;MACA;MACA;MACA,OAAOjF,MAAM,CAACkF,QAAQ;IAC1B,CAAC;IAlgBG,MAAMC,wBAAwB,GAAGnL,MAAM,CAACkE,iBAAiB,CAAC;IAC1D,MAAMkH,UAAU,GAAGpL,MAAM,CAACuC,MAAM,EAAE;MAAEoH,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrD,MAAM0B,eAAe,GAAGrL,MAAM,CAACwC,kBAAkB,EAAE;MAAEmH,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtE,MAAM2B,QAAQ,GAAGtL,MAAM,CAAC,IAAIM,kBAAkB,CAAC,UAAU,CAAC,EAAE;MAAEqJ,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC/E,IAAI,IAAI,CAAC7B,SAAS,EAAE;MAChB;MACA;MACA,IAAI,CAACA,SAAS,CAACyD,aAAa,GAAG,IAAI;IACvC;IACA;IACA;IACA,IAAI,EAAA7B,sBAAA,OAAI,CAACW,eAAe,cAAAX,sBAAA,uBAApBA,sBAAA,CAAsB8B,yBAAyB,KAAI,IAAI,EAAE;MACzD,IAAI,CAACA,yBAAyB,GAAG,IAAI,CAACnB,eAAe,CAACmB,yBAAyB;IACnF;IACA,IAAI,CAAC1C,kBAAkB,GAAG,IAAI3E,kBAAkB,CAACgH,wBAAwB,EAAE,IAAI,CAACrD,SAAS,EAAEuD,eAAe,EAAED,UAAU,EAAE,IAAI,CAAC7D,YAAY,CAAC;IAC1I,IAAI,CAACkE,eAAe,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;IACpD,IAAI,CAACJ,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAGK,QAAQ,CAACL,QAAQ,CAAC,IAAI,CAAC;IAC9D;IACA,IAAI,CAAC/L,EAAE,GAAG,IAAI,CAACA,EAAE;EACrB;EACAqM,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC1D,eAAe,GAAG,IAAIvG,cAAc,CAAC,IAAI,CAACnC,QAAQ,CAAC;IACxD,IAAI,CAAC+H,YAAY,CAACC,IAAI,CAAC,CAAC;IACxB,IAAI,CAACqE,cAAc,CACdC,MAAM,CAAC,CAAC,CACRnB,IAAI,CAAC3H,SAAS,CAAC,IAAI,CAAC+I,QAAQ,CAAC,CAAC,CAC9BC,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACf,SAAS,EAAE;QAChB,IAAI,CAACgB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,uBAAuB,CAAC;QACxE,IAAI,CAACC,kBAAkB,CAACC,aAAa,CAAC,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACzB,YAAY,CAACrD,IAAI,CAAC,CAAC;IACxB,IAAI,CAACqD,YAAY,CAAC0B,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACtE,eAAe,CAACuE,OAAO,CAAC9B,IAAI,CAAC3H,SAAS,CAAC,IAAI,CAAC+I,QAAQ,CAAC,CAAC,CAACC,SAAS,CAACU,KAAK,IAAI;MAC3EA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC5G,MAAM,IAAIA,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC;MAC9CH,KAAK,CAACI,OAAO,CAACF,OAAO,CAAC5G,MAAM,IAAIA,MAAM,CAAC+G,QAAQ,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAAC9G,OAAO,CAACyE,OAAO,CAACC,IAAI,CAAC/H,SAAS,CAAC,IAAI,CAAC,EAAEI,SAAS,CAAC,IAAI,CAAC+I,QAAQ,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;MACjF,IAAI,CAACgB,aAAa,CAAC,CAAC;MACpB,IAAI,CAACzE,oBAAoB,CAAC,CAAC;IAC/B,CAAC,CAAC;EACN;EACA0E,SAASA,CAAA,EAAG;IACR,MAAMC,iBAAiB,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAC1D,MAAMrF,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC;IACA;IACA;IACA,IAAIoF,iBAAiB,KAAK,IAAI,CAACE,sBAAsB,EAAE;MACnD,MAAM7G,OAAO,GAAG,IAAI,CAAC8G,WAAW,CAACjH,aAAa;MAC9C,IAAI,CAACgH,sBAAsB,GAAGF,iBAAiB;MAC/C,IAAIA,iBAAiB,EAAE;QACnB3G,OAAO,CAAC+G,YAAY,CAAC,iBAAiB,EAAEJ,iBAAiB,CAAC;MAC9D,CAAC,MACI;QACD3G,OAAO,CAACgH,eAAe,CAAC,iBAAiB,CAAC;MAC9C;IACJ;IACA,IAAIzF,SAAS,EAAE;MACX;MACA,IAAI,IAAI,CAAC0F,gBAAgB,KAAK1F,SAAS,CAACC,OAAO,EAAE;QAC7C,IAAI,IAAI,CAACyF,gBAAgB,KAAKC,SAAS,IACnC3F,SAAS,CAACoD,QAAQ,KAAK,IAAI,IAC3BpD,SAAS,CAACoD,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;UACtC,IAAI,CAACA,QAAQ,GAAGpD,SAAS,CAACoD,QAAQ;QACtC;QACA,IAAI,CAACsC,gBAAgB,GAAG1F,SAAS,CAACC,OAAO;MAC7C;MACA,IAAI,CAAC2F,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAC,WAAWA,CAACjD,OAAO,EAAE;IACjB;IACA;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,qBAAqB,CAAC,EAAE;MACvD,IAAI,CAACnD,YAAY,CAACC,IAAI,CAAC,CAAC;IAC5B;IACA,IAAIkD,OAAO,CAAC,2BAA2B,CAAC,IAAI,IAAI,CAAC7D,WAAW,EAAE;MAC1D,IAAI,CAACA,WAAW,CAAC+G,aAAa,CAAC,IAAI,CAACpC,yBAAyB,CAAC;IAClE;EACJ;EACAqC,WAAWA,CAAA,EAAG;IAAA,IAAAC,oBAAA,EAAAC,iBAAA;IACV,CAAAD,oBAAA,OAAI,CAACE,cAAc,cAAAF,oBAAA,eAAnBA,oBAAA,CAAAG,IAAA,KAAsB,CAAC;IACvB,CAAAF,iBAAA,OAAI,CAAClH,WAAW,cAAAkH,iBAAA,eAAhBA,iBAAA,CAAkBG,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACnC,QAAQ,CAACvE,IAAI,CAAC,CAAC;IACpB,IAAI,CAACuE,QAAQ,CAACQ,QAAQ,CAAC,CAAC;IACxB,IAAI,CAAChF,YAAY,CAACgF,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAC4B,eAAe,CAAC,CAAC;EAC1B;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACnD,SAAS,GAAG,IAAI,CAACoD,KAAK,CAAC,CAAC,GAAG,IAAI,CAACC,IAAI,CAAC,CAAC;EAC/C;EACA;EACAA,IAAIA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACH,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;MAClB;IACJ;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACtC,uBAAuB,GAAG,IAAI,CAACsC,gBAAgB,CAACC,yBAAyB,CAAC,CAAC;IACpF;IACA,CAAAH,qBAAA,OAAI,CAACP,cAAc,cAAAO,qBAAA,eAAnBA,qBAAA,CAAAN,IAAA,KAAsB,CAAC;IACvB,IAAI,CAAChC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,uBAAuB,CAAC;IACxE,IAAI,CAACwC,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACzH,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC0H,WAAW,CAACC,cAAc,CAAClE,IAAI,CAAC1H,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC+I,SAAS,CAAC,MAAM;MAC1D,IAAI,CAACI,kBAAkB,CAACC,aAAa,CAAC,CAAC;MACvC,IAAI,CAACzF,mBAAmB,CAAC,CAAC;IAC9B,CAAC,CAAC;IACF,IAAI,CAACgI,WAAW,CAACE,aAAa,CAAC,CAAC;IAChC,IAAI,CAACjI,WAAW,CAACkI,yBAAyB,CAAC,IAAI,CAAC;IAChD,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAAC5C,kBAAkB,CAAC6C,YAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAAC1H,YAAY,CAACC,IAAI,CAAC,CAAC;IACxB;IACA0H,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACrE,YAAY,CAACsE,IAAI,CAAC,IAAI,CAAC,CAAC;EAC9D;EAOA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIV,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMW,KAAK,GAAG,IAAI,CAACjC,WAAW,CAACjH,aAAa,CAACmJ,OAAO,CAAC,mDAAmD,CAAC;IACzG,IAAI,CAACD,KAAK,EAAE;MACR;MACA;IACJ;IACA,MAAME,OAAO,GAAG,GAAG,IAAI,CAACjQ,EAAE,QAAQ;IAClC,IAAI,IAAI,CAACkQ,aAAa,EAAE;MACpBlO,sBAAsB,CAAC,IAAI,CAACkO,aAAa,EAAE,WAAW,EAAED,OAAO,CAAC;IACpE;IACAhO,mBAAmB,CAAC8N,KAAK,EAAE,WAAW,EAAEE,OAAO,CAAC;IAChD,IAAI,CAACC,aAAa,GAAGH,KAAK;EAC9B;EACA;EACAnB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACsB,aAAa,EAAE;MACrB;MACA;IACJ;IACA,MAAMD,OAAO,GAAG,GAAG,IAAI,CAACjQ,EAAE,QAAQ;IAClCgC,sBAAsB,CAAC,IAAI,CAACkO,aAAa,EAAE,WAAW,EAAED,OAAO,CAAC;IAChE,IAAI,CAACC,aAAa,GAAG,IAAI;EAC7B;EACA;EACApB,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACnH,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAACwI,cAAc,CAAC,CAAC;MACrB,IAAI,CAAC7I,WAAW,CAACkI,yBAAyB,CAAC,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;MACzE,IAAI,CAACvD,kBAAkB,CAAC6C,YAAY,CAAC,CAAC;MACtC,IAAI,CAACW,UAAU,CAAC,CAAC;MACjB;MACA,IAAI,CAACrI,YAAY,CAACC,IAAI,CAAC,CAAC;MACxB;MACA0H,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACrE,YAAY,CAACsE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/D;EACJ;EACA;EACAK,cAAcA,CAAA,EAAG;IAAA,IAAAG,qBAAA;IACb,IAAI,IAAI,CAAC1Q,mBAAmB,IAAI,CAAC,IAAI,CAACgH,KAAK,EAAE;MACzC,IAAI,CAAC2J,cAAc,CAAC,CAAC;MACrB;IACJ;IACA,CAAAD,qBAAA,OAAI,CAAC7B,cAAc,cAAA6B,qBAAA,eAAnBA,qBAAA,CAAA5B,IAAA,KAAsB,CAAC;IACvB,IAAI,CAACD,cAAc,GAAG,MAAM;MACxB+B,YAAY,CAAC,CAAC;MACdC,YAAY,CAACC,iBAAiB,CAAC;MAC/B,IAAI,CAACjC,cAAc,GAAGP,SAAS;IACnC,CAAC;IACD,MAAMtH,KAAK,GAAG,IAAI,CAACA,KAAK,CAACC,aAAa;IACtC,MAAM2J,YAAY,GAAG,IAAI,CAACG,SAAS,CAACC,MAAM,CAAChK,KAAK,EAAE,cAAc,EAAGuG,KAAK,IAAK;MACzE,IAAIA,KAAK,CAAC0D,aAAa,KAAK,kBAAkB,EAAE;QAAA,IAAAC,qBAAA;QAC5C,CAAAA,qBAAA,OAAI,CAACrC,cAAc,cAAAqC,qBAAA,eAAnBA,qBAAA,CAAApC,IAAA,KAAsB,CAAC;QACvB,IAAI,CAAC6B,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF;IACA;IACA,MAAMG,iBAAiB,GAAGK,UAAU,CAAC,MAAM;MAAA,IAAAC,qBAAA;MACvC,CAAAA,qBAAA,OAAI,CAACvC,cAAc,cAAAuC,qBAAA,eAAnBA,qBAAA,CAAAtC,IAAA,KAAsB,CAAC;MACvB,IAAI,CAAC6B,cAAc,CAAC,CAAC;IACzB,CAAC,EAAE,GAAG,CAAC;IACP3J,KAAK,CAACqK,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;EAChD;EACA;EACAX,cAAcA,CAAA,EAAG;IACb,IAAI,CAAClB,WAAW,CAAC8B,aAAa,CAAC,CAAC;IAChC;IACA;IACA,IAAI,CAACtE,kBAAkB,CAAC6C,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;EACI0B,UAAUA,CAAChL,KAAK,EAAE;IACd,IAAI,CAACgD,YAAY,CAAChD,KAAK,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiL,gBAAgBA,CAACtI,EAAE,EAAE;IACjB,IAAI,CAACM,SAAS,GAAGN,EAAE;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIuI,iBAAiBA,CAACvI,EAAE,EAAE;IAClB,IAAI,CAACsH,UAAU,GAAGtH,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwI,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAC7F,QAAQ,GAAG6F,UAAU;IAC1B,IAAI,CAAC3E,kBAAkB,CAAC6C,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC1H,YAAY,CAACC,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIyD,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC/D,UAAU;EAC1B;EACA;EACA,IAAI8J,QAAQA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACX,OAAO,IAAI,CAAC1R,QAAQ,GAAG,EAAAyR,qBAAA,OAAI,CAAC/I,eAAe,cAAA+I,qBAAA,uBAApBA,qBAAA,CAAsBD,QAAQ,KAAI,EAAE,IAAAE,sBAAA,GAAG,IAAI,CAAChJ,eAAe,cAAAgJ,sBAAA,uBAApBA,sBAAA,CAAsBF,QAAQ,CAAC,CAAC,CAAC;EACnG;EACA;EACA,IAAI9S,YAAYA,CAAA,EAAG;IACf,IAAI,IAAI,CAACiT,KAAK,EAAE;MACZ,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAAClJ,SAAS,EAAE;MAChB,MAAMmJ,eAAe,GAAG,IAAI,CAAClJ,eAAe,CAAC8I,QAAQ,CAACjO,GAAG,CAACiD,MAAM,IAAIA,MAAM,CAACqL,SAAS,CAAC;MACrF,IAAI,IAAI,CAAC1B,MAAM,CAAC,CAAC,EAAE;QACfyB,eAAe,CAACE,OAAO,CAAC,CAAC;MAC7B;MACA;MACA,OAAOF,eAAe,CAACG,IAAI,CAAC,IAAI,CAAC;IACrC;IACA,OAAO,IAAI,CAACrJ,eAAe,CAAC8I,QAAQ,CAAC,CAAC,CAAC,CAACK,SAAS;EACrD;EACA;EACA3D,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC5E,kBAAkB,CAAC4E,gBAAgB,CAAC,CAAC;EAC9C;EACA;EACAiC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC6B,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC7L,KAAK,KAAK,KAAK,GAAG,KAAK;EACxD;EACA;EACA5G,cAAcA,CAAC2N,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACxB,QAAQ,EAAE;MAChB,IAAI,CAACD,SAAS,GAAG,IAAI,CAACwG,kBAAkB,CAAC/E,KAAK,CAAC,GAAG,IAAI,CAACgF,oBAAoB,CAAChF,KAAK,CAAC;IACtF;EACJ;EACA;EACAgF,oBAAoBA,CAAChF,KAAK,EAAE;IACxB,MAAMiF,OAAO,GAAGjF,KAAK,CAACiF,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAK1P,UAAU,IACrC0P,OAAO,KAAKzP,QAAQ,IACpByP,OAAO,KAAKxP,UAAU,IACtBwP,OAAO,KAAKvP,WAAW;IAC3B,MAAMyP,SAAS,GAAGF,OAAO,KAAK9P,KAAK,IAAI8P,OAAO,KAAK7P,KAAK;IACxD,MAAMgQ,OAAO,GAAG,IAAI,CAACjL,WAAW;IAChC;IACA,IAAK,CAACiL,OAAO,CAACC,QAAQ,CAAC,CAAC,IAAIF,SAAS,IAAI,CAACjQ,cAAc,CAAC8K,KAAK,CAAC,IAC1D,CAAC,IAAI,CAAClN,QAAQ,IAAIkN,KAAK,CAACsF,MAAM,KAAKJ,UAAW,EAAE;MACjDlF,KAAK,CAACuF,cAAc,CAAC,CAAC,CAAC,CAAC;MACxB,IAAI,CAAC3D,IAAI,CAAC,CAAC;IACf,CAAC,MACI,IAAI,CAAC,IAAI,CAAC9O,QAAQ,EAAE;MACrB,MAAM0S,wBAAwB,GAAG,IAAI,CAAClB,QAAQ;MAC9Cc,OAAO,CAACK,SAAS,CAACzF,KAAK,CAAC;MACxB,MAAM0F,cAAc,GAAG,IAAI,CAACpB,QAAQ;MACpC;MACA,IAAIoB,cAAc,IAAIF,wBAAwB,KAAKE,cAAc,EAAE;QAC/D;QACA;QACA,IAAI,CAACC,cAAc,CAACC,QAAQ,CAACF,cAAc,CAACf,SAAS,EAAE,KAAK,CAAC;MACjE;IACJ;EACJ;EACA;EACAI,kBAAkBA,CAAC/E,KAAK,EAAE;IACtB,MAAMoF,OAAO,GAAG,IAAI,CAACjL,WAAW;IAChC,MAAM8K,OAAO,GAAGjF,KAAK,CAACiF,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAK1P,UAAU,IAAI0P,OAAO,KAAKzP,QAAQ;IACjE,MAAM6P,QAAQ,GAAGD,OAAO,CAACC,QAAQ,CAAC,CAAC;IACnC,IAAIH,UAAU,IAAIlF,KAAK,CAACsF,MAAM,EAAE;MAC5B;MACAtF,KAAK,CAACuF,cAAc,CAAC,CAAC;MACtB,IAAI,CAAC5D,KAAK,CAAC,CAAC;MACZ;MACA;IACJ,CAAC,MACI,IAAI,CAAC0D,QAAQ,KACbJ,OAAO,KAAK9P,KAAK,IAAI8P,OAAO,KAAK7P,KAAK,CAAC,IACxCgQ,OAAO,CAACS,UAAU,IAClB,CAAC3Q,cAAc,CAAC8K,KAAK,CAAC,EAAE;MACxBA,KAAK,CAACuF,cAAc,CAAC,CAAC;MACtBH,OAAO,CAACS,UAAU,CAACC,qBAAqB,CAAC,CAAC;IAC9C,CAAC,MACI,IAAI,CAACT,QAAQ,IAAI,IAAI,CAAC9J,SAAS,IAAI0J,OAAO,KAAK5P,CAAC,IAAI2K,KAAK,CAAC+F,OAAO,EAAE;MACpE/F,KAAK,CAACuF,cAAc,CAAC,CAAC;MACtB,MAAMS,oBAAoB,GAAG,IAAI,CAACzM,OAAO,CAAC0M,IAAI,CAACC,GAAG,IAAI,CAACA,GAAG,CAAC1H,QAAQ,IAAI,CAAC0H,GAAG,CAAC5B,QAAQ,CAAC;MACrF,IAAI,CAAC/K,OAAO,CAAC2G,OAAO,CAAC5G,MAAM,IAAI;QAC3B,IAAI,CAACA,MAAM,CAACkF,QAAQ,EAAE;UAClBwH,oBAAoB,GAAG1M,MAAM,CAAC6G,MAAM,CAAC,CAAC,GAAG7G,MAAM,CAAC+G,QAAQ,CAAC,CAAC;QAC9D;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,MAAM8F,sBAAsB,GAAGf,OAAO,CAAChL,eAAe;MACtDgL,OAAO,CAACK,SAAS,CAACzF,KAAK,CAAC;MACxB,IAAI,IAAI,CAACzE,SAAS,IACd2J,UAAU,IACVlF,KAAK,CAACoG,QAAQ,IACdhB,OAAO,CAACS,UAAU,IAClBT,OAAO,CAAChL,eAAe,KAAK+L,sBAAsB,EAAE;QACpDf,OAAO,CAACS,UAAU,CAACC,qBAAqB,CAAC,CAAC;MAC9C;IACJ;EACJ;EACA;EACAO,qBAAqBA,CAACrG,KAAK,EAAE;IACzB;IACA;IACA;IACA;IACA,IAAIA,KAAK,CAACiF,OAAO,KAAK3P,MAAM,IAAI,CAACJ,cAAc,CAAC8K,KAAK,CAAC,EAAE;MACpDA,KAAK,CAACuF,cAAc,CAAC,CAAC;MACtB,IAAI,CAAC5D,KAAK,CAAC,CAAC;IAChB;EACJ;EACA2E,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAC9H,QAAQ,EAAE;MAChB,IAAI,CAACjE,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACM,YAAY,CAACC,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACIyL,OAAOA,CAAA,EAAG;IAAA,IAAAC,kBAAA;IACN,IAAI,CAACjM,QAAQ,GAAG,KAAK;IACrB,CAAAiM,kBAAA,OAAI,CAACrM,WAAW,cAAAqM,kBAAA,eAAhBA,kBAAA,CAAkBC,eAAe,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,CAACjI,QAAQ,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;MACnC,IAAI,CAAC2E,UAAU,CAAC,CAAC;MACjB,IAAI,CAACxD,kBAAkB,CAAC6C,YAAY,CAAC,CAAC;MACtC,IAAI,CAAC1H,YAAY,CAACC,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACAvI,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACwP,gBAAgB,GAAG,OAAO,IAAI,CAACA,gBAAgB,CAAC2E,KAAK,EAAE,GAAG,EAAE;EAC5E;EACA;EACA,IAAIjC,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACjJ,eAAe,IAAI,IAAI,CAACA,eAAe,CAACmL,OAAO,CAAC,CAAC;EAClE;EACA9K,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACA2G,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,IAAI,CAACtH,SAAS,EAAE;QAChB,IAAI,CAACU,MAAM,GAAG,IAAI,CAACV,SAAS,CAACnC,KAAK;MACtC;MACA,IAAI,CAAC2N,oBAAoB,CAAC,IAAI,CAAC9K,MAAM,CAAC;MACtC,IAAI,CAACjB,YAAY,CAACC,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI8L,oBAAoBA,CAAC3N,KAAK,EAAE;IACxB,IAAI,CAACM,OAAO,CAAC2G,OAAO,CAAC5G,MAAM,IAAIA,MAAM,CAACuN,iBAAiB,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACrL,eAAe,CAACsL,KAAK,CAAC,CAAC;IAC5B,IAAI,IAAI,CAAChU,QAAQ,IAAImG,KAAK,EAAE;MACxB,IAAI,CAAC8N,KAAK,CAACC,OAAO,CAAC/N,KAAK,CAAC,KAAK,OAAOwC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC1E,MAAM1D,8BAA8B,CAAC,CAAC;MAC1C;MACAkB,KAAK,CAACiH,OAAO,CAAE+G,YAAY,IAAK,IAAI,CAACC,oBAAoB,CAACD,YAAY,CAAC,CAAC;MACxE,IAAI,CAACE,WAAW,CAAC,CAAC;IACtB,CAAC,MACI;MACD,MAAMC,mBAAmB,GAAG,IAAI,CAACF,oBAAoB,CAACjO,KAAK,CAAC;MAC5D;MACA;MACA,IAAImO,mBAAmB,EAAE;QACrB,IAAI,CAACjN,WAAW,CAACkN,gBAAgB,CAACD,mBAAmB,CAAC;MAC1D,CAAC,MACI,IAAI,CAAC,IAAI,CAAC7I,SAAS,EAAE;QACtB;QACA;QACA,IAAI,CAACpE,WAAW,CAACkN,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACzC;IACJ;IACA,IAAI,CAAC3H,kBAAkB,CAAC6C,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACI2E,oBAAoBA,CAACjO,KAAK,EAAE;IACxB,MAAMmO,mBAAmB,GAAG,IAAI,CAAC7N,OAAO,CAAC+N,IAAI,CAAEhO,MAAM,IAAK;MACtD;MACA;MACA,IAAI,IAAI,CAACkC,eAAe,CAAC+L,UAAU,CAACjO,MAAM,CAAC,EAAE;QACzC,OAAO,KAAK;MAChB;MACA,IAAI;QACA;QACA,OAAQ,CAACA,MAAM,CAACL,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC8E,wBAAwB,KAC1D,IAAI,CAACpC,YAAY,CAACrC,MAAM,CAACL,KAAK,EAAEA,KAAK,CAAC;MAC9C,CAAC,CACD,OAAOuO,KAAK,EAAE;QACV,IAAI,OAAO/L,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C;UACAgM,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;QACvB;QACA,OAAO,KAAK;MAChB;IACJ,CAAC,CAAC;IACF,IAAIJ,mBAAmB,EAAE;MACrB,IAAI,CAAC5L,eAAe,CAAC2E,MAAM,CAACiH,mBAAmB,CAAC;IACpD;IACA,OAAOA,mBAAmB;EAC9B;EACA;EACAnL,YAAYA,CAACF,QAAQ,EAAE;IACnB;IACA,IAAIA,QAAQ,KAAK,IAAI,CAACD,MAAM,IAAK,IAAI,CAACP,SAAS,IAAIwL,KAAK,CAACC,OAAO,CAACjL,QAAQ,CAAE,EAAE;MACzE,IAAI,IAAI,CAACxC,OAAO,EAAE;QACd,IAAI,CAACqN,oBAAoB,CAAC7K,QAAQ,CAAC;MACvC;MACA,IAAI,CAACD,MAAM,GAAGC,QAAQ;MACtB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EAyBA;EACAyD,gBAAgBA,CAACmI,eAAe,EAAE;IAC9B,IAAI,IAAI,CAAC7J,UAAU,KAAK,MAAM,EAAE;MAC5B,MAAM8J,YAAY,GAAGD,eAAe,YAAYxU,gBAAgB,GAC1DwU,eAAe,CAACE,UAAU,GAC1BF,eAAe,IAAI,IAAI,CAAChH,WAAW;MACzC,OAAOiH,YAAY,CAAClO,aAAa,CAACoO,qBAAqB,CAAC,CAAC,CAACC,KAAK;IACnE;IACA,OAAO,IAAI,CAACjK,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAACA,UAAU;EAC1D;EACA;EACAnD,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACpB,OAAO,EAAE;MACd,KAAK,MAAMD,MAAM,IAAI,IAAI,CAACC,OAAO,EAAE;QAC/BD,MAAM,CAACoG,kBAAkB,CAAC6C,YAAY,CAAC,CAAC;MAC5C;IACJ;EACJ;EACA;EACAzC,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC3F,WAAW,GAAG,IAAIpF,0BAA0B,CAAC,IAAI,CAACwE,OAAO,CAAC,CAC1D2H,aAAa,CAAC,IAAI,CAACpC,yBAAyB,CAAC,CAC7CkJ,uBAAuB,CAAC,CAAC,CACzB3F,yBAAyB,CAAC,IAAI,CAACY,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CACxDgF,cAAc,CAAC,CAAC,CAChBC,cAAc,CAAC,CAAC,CAChBC,uBAAuB,CAAC,CAAC,UAAU,CAAC,CAAC,CACrCC,aAAa,CAAC,IAAI,CAACC,cAAc,CAAC;IACvC,IAAI,CAAClO,WAAW,CAACmO,MAAM,CAAChJ,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAACf,SAAS,EAAE;QAChB;QACA;QACA,IAAI,CAAC,IAAI,CAACzL,QAAQ,IAAI,IAAI,CAACqH,WAAW,CAAC0L,UAAU,EAAE;UAC/C,IAAI,CAAC1L,WAAW,CAAC0L,UAAU,CAACC,qBAAqB,CAAC,CAAC;QACvD;QACA;QACA;QACA,IAAI,CAACyC,KAAK,CAAC,CAAC;QACZ,IAAI,CAAC5G,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF,IAAI,CAACxH,WAAW,CAACiF,MAAM,CAACE,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAAC9E,UAAU,IAAI,IAAI,CAACf,KAAK,EAAE;QAC/B,IAAI,CAACL,qBAAqB,CAAC,IAAI,CAACe,WAAW,CAACC,eAAe,IAAI,CAAC,CAAC;MACrE,CAAC,MACI,IAAI,CAAC,IAAI,CAACI,UAAU,IAAI,CAAC,IAAI,CAAC1H,QAAQ,IAAI,IAAI,CAACqH,WAAW,CAAC0L,UAAU,EAAE;QACxE,IAAI,CAAC1L,WAAW,CAAC0L,UAAU,CAACC,qBAAqB,CAAC,CAAC;MACvD;IACJ,CAAC,CAAC;EACN;EACA;EACAxF,aAAaA,CAAA,EAAG;IACZ,MAAMkI,kBAAkB,GAAGvS,KAAK,CAAC,IAAI,CAACsD,OAAO,CAACyE,OAAO,EAAE,IAAI,CAACqB,QAAQ,CAAC;IACrE,IAAI,CAACjB,sBAAsB,CAACH,IAAI,CAAC3H,SAAS,CAACkS,kBAAkB,CAAC,CAAC,CAAClJ,SAAS,CAACU,KAAK,IAAI;MAC/E,IAAI,CAACyI,SAAS,CAACzI,KAAK,CAAChH,MAAM,EAAEgH,KAAK,CAAC0I,WAAW,CAAC;MAC/C,IAAI1I,KAAK,CAAC0I,WAAW,IAAI,CAAC,IAAI,CAAC5V,QAAQ,IAAI,IAAI,CAAC0H,UAAU,EAAE;QACxD,IAAI,CAACmH,KAAK,CAAC,CAAC;QACZ,IAAI,CAAC4G,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF;IACA;IACAtS,KAAK,CAAC,GAAG,IAAI,CAACsD,OAAO,CAAClD,GAAG,CAACiD,MAAM,IAAIA,MAAM,CAACqP,aAAa,CAAC,CAAC,CACrD1K,IAAI,CAAC3H,SAAS,CAACkS,kBAAkB,CAAC,CAAC,CACnClJ,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACI,kBAAkB,CAACC,aAAa,CAAC,CAAC;MACvC,IAAI,CAAC9E,YAAY,CAACC,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;EACA2N,SAASA,CAACnP,MAAM,EAAEoP,WAAW,EAAE;IAC3B,MAAME,WAAW,GAAG,IAAI,CAACpN,eAAe,CAAC+L,UAAU,CAACjO,MAAM,CAAC;IAC3D,IAAI,CAAC,IAAI,CAACyE,wBAAwB,IAAIzE,MAAM,CAACL,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAACsC,SAAS,EAAE;MAC3EjC,MAAM,CAAC+G,QAAQ,CAAC,CAAC;MACjB,IAAI,CAAC7E,eAAe,CAACsL,KAAK,CAAC,CAAC;MAC5B,IAAI,IAAI,CAAC7N,KAAK,IAAI,IAAI,EAAE;QACpB,IAAI,CAAC4P,iBAAiB,CAACvP,MAAM,CAACL,KAAK,CAAC;MACxC;IACJ,CAAC,MACI;MACD,IAAI2P,WAAW,KAAKtP,MAAM,CAACgL,QAAQ,EAAE;QACjChL,MAAM,CAACgL,QAAQ,GACT,IAAI,CAAC9I,eAAe,CAAC2E,MAAM,CAAC7G,MAAM,CAAC,GACnC,IAAI,CAACkC,eAAe,CAAC6E,QAAQ,CAAC/G,MAAM,CAAC;MAC/C;MACA,IAAIoP,WAAW,EAAE;QACb,IAAI,CAACvO,WAAW,CAAC2O,aAAa,CAACxP,MAAM,CAAC;MAC1C;MACA,IAAI,IAAI,CAACxG,QAAQ,EAAE;QACf,IAAI,CAACqU,WAAW,CAAC,CAAC;QAClB,IAAIuB,WAAW,EAAE;UACb;UACA;UACA;UACA;UACA,IAAI,CAACH,KAAK,CAAC,CAAC;QAChB;MACJ;IACJ;IACA,IAAIK,WAAW,KAAK,IAAI,CAACpN,eAAe,CAAC+L,UAAU,CAACjO,MAAM,CAAC,EAAE;MACzD,IAAI,CAACuP,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAChO,YAAY,CAACC,IAAI,CAAC,CAAC;EAC5B;EACA;EACAqM,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACrU,QAAQ,EAAE;MACf,MAAMyG,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtC,IAAI,CAACgC,eAAe,CAACuN,IAAI,CAAC,CAACC,CAAC,EAAEjS,CAAC,KAAK;QAChC,OAAO,IAAI,CAACkS,cAAc,GACpB,IAAI,CAACA,cAAc,CAACD,CAAC,EAAEjS,CAAC,EAAEwC,OAAO,CAAC,GAClCA,OAAO,CAAC2P,OAAO,CAACF,CAAC,CAAC,GAAGzP,OAAO,CAAC2P,OAAO,CAACnS,CAAC,CAAC;MACjD,CAAC,CAAC;MACF,IAAI,CAAC8D,YAAY,CAACC,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACA+N,iBAAiBA,CAACM,aAAa,EAAE;IAC7B,IAAIC,WAAW;IACf,IAAI,IAAI,CAACtW,QAAQ,EAAE;MACfsW,WAAW,GAAG,IAAI,CAAC9E,QAAQ,CAACjO,GAAG,CAACiD,MAAM,IAAIA,MAAM,CAACL,KAAK,CAAC;IAC3D,CAAC,MACI;MACDmQ,WAAW,GAAG,IAAI,CAAC9E,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACrL,KAAK,GAAGkQ,aAAa;IACrE;IACA,IAAI,CAACrN,MAAM,GAAGsN,WAAW;IACzB,IAAI,CAACC,WAAW,CAAC1G,IAAI,CAACyG,WAAW,CAAC;IAClC,IAAI,CAAClN,SAAS,CAACkN,WAAW,CAAC;IAC3B,IAAI,CAACE,eAAe,CAAC3G,IAAI,CAAC,IAAI,CAACtI,eAAe,CAAC+O,WAAW,CAAC,CAAC;IAC5D,IAAI,CAAC1J,kBAAkB,CAAC6C,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACID,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACnI,WAAW,EAAE;MAClB,IAAI,IAAI,CAACsK,KAAK,EAAE;QACZ;QACA;QACA;QACA,IAAI8E,uBAAuB,GAAG,CAAC,CAAC;QAChC,KAAK,IAAIlQ,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACE,OAAO,CAACiQ,MAAM,EAAEnQ,KAAK,EAAE,EAAE;UACtD,MAAMC,MAAM,GAAG,IAAI,CAACC,OAAO,CAACkQ,GAAG,CAACpQ,KAAK,CAAC;UACtC,IAAI,CAACC,MAAM,CAACkF,QAAQ,EAAE;YAClB+K,uBAAuB,GAAGlQ,KAAK;YAC/B;UACJ;QACJ;QACA,IAAI,CAACc,WAAW,CAAC2O,aAAa,CAACS,uBAAuB,CAAC;MAC3D,CAAC,MACI;QACD,IAAI,CAACpP,WAAW,CAAC2O,aAAa,CAAC,IAAI,CAACtN,eAAe,CAAC8I,QAAQ,CAAC,CAAC,CAAC,CAAC;MACpE;IACJ;EACJ;EACA;EACAxC,QAAQA,CAAA,EAAG;IAAA,IAAA4H,aAAA;IACP,OAAO,CAAC,IAAI,CAAClP,UAAU,IAAI,CAAC,IAAI,CAACgE,QAAQ,IAAI,EAAAkL,aAAA,OAAI,CAACnQ,OAAO,cAAAmQ,aAAA,uBAAZA,aAAA,CAAcF,MAAM,IAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAACtH,WAAW;EAC/F;EACA;EACAqG,KAAKA,CAAChP,OAAO,EAAE;IACX,IAAI,CAACoH,WAAW,CAACjH,aAAa,CAAC6O,KAAK,CAAChP,OAAO,CAAC;EACjD;EACA;EACAvG,uBAAuBA,CAAA,EAAG;IAAA,IAAA2W,qBAAA;IACtB,IAAI,IAAI,CAAC5W,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAM6W,OAAO,GAAG,EAAAD,qBAAA,OAAI,CAAC5H,gBAAgB,cAAA4H,qBAAA,uBAArBA,qBAAA,CAAuBE,UAAU,CAAC,CAAC,KAAI,IAAI;IAC3D,MAAMC,eAAe,GAAGF,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE;IACpD,OAAO,IAAI,CAACG,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACC,cAAc,GAAGH,OAAO;EAChF;EACA;EACAI,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACzL,SAAS,IAAI,IAAI,CAACpE,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC0L,UAAU,EAAE;MACnE,OAAO,IAAI,CAAC1L,WAAW,CAAC0L,UAAU,CAAChT,EAAE;IACzC;IACA,OAAO,IAAI;EACf;EACA;EACA4N,yBAAyBA,CAAA,EAAG;IAAA,IAAAwJ,sBAAA;IACxB,IAAI,IAAI,CAAClX,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,IAAIkG,KAAK,GAAG,EAAAgR,sBAAA,OAAI,CAAClI,gBAAgB,cAAAkI,sBAAA,uBAArBA,sBAAA,CAAuBJ,UAAU,CAAC,CAAC,KAAI,EAAE;IACrD,IAAI,IAAI,CAACE,cAAc,EAAE;MACrB9Q,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC8Q,cAAc;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC9Q,KAAK,EAAE;MACRA,KAAK,GAAG,IAAI,CAACiR,QAAQ;IACzB;IACA,OAAOjR,KAAK;EAChB;EACA;AACJ;AACA;AACA;EACIkR,iBAAiBA,CAACC,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACZ,MAAM,EAAE;MACZ,IAAI,CAAC7I,WAAW,CAACjH,aAAa,CAACkH,YAAY,CAAC,kBAAkB,EAAEwJ,GAAG,CAACvF,IAAI,CAAC,GAAG,CAAC,CAAC;IAClF,CAAC,MACI;MACD,IAAI,CAAClE,WAAW,CAACjH,aAAa,CAACmH,eAAe,CAAC,kBAAkB,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;EACIwJ,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC9B,KAAK,CAAC,CAAC;IACZ,IAAI,CAAC3G,IAAI,CAAC,CAAC;EACf;EACA;AACJ;AACA;AACA;EACI,IAAI0I,gBAAgBA,CAAA,EAAG;IACnB;IACA;IACA,OAAO,IAAI,CAAC/L,SAAS,IAAI,CAAC,IAAI,CAACkG,KAAK,IAAK,IAAI,CAACnK,OAAO,IAAI,CAAC,CAAC,IAAI,CAACnJ,WAAY;EAChF;AAMJ;AAACoZ,UAAA,GAvhCKpR,SAAS;AAAAD,eAAA,CAATC,SAAS,wBAAAqR,mBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAkhCwFtR,UAAS;AAAA;AAAAD,eAAA,CAlhC1GC,SAAS,8BAwhCkEzI,EAAE,CAAAga,iBAAA;EAAAC,IAAA,EALQxR,UAAS;EAAAyR,SAAA;EAAAC,cAAA,WAAAC,0BAAAha,EAAA,EAAAO,GAAA,EAAA0Z,QAAA;IAAA,IAAAja,EAAA;MAKnBJ,EAAE,CAAAsa,cAAA,CAAAD,QAAA,EAFLlS,kBAAkB;MAEfnI,EAAE,CAAAsa,cAAA,CAAAD,QAAA,EAF0E3T,SAAS;MAErF1G,EAAE,CAAAsa,cAAA,CAAAD,QAAA,EAFqJzT,YAAY;IAAA;IAAA,IAAAxG,EAAA;MAAA,IAAAma,EAAA;MAEnKva,EAAE,CAAAwa,cAAA,CAAAD,EAAA,GAAFva,EAAE,CAAAya,WAAA,QAAA9Z,GAAA,CAAAO,aAAA,GAAAqZ,EAAA,CAAAG,KAAA;MAAF1a,EAAE,CAAAwa,cAAA,CAAAD,EAAA,GAAFva,EAAE,CAAAya,WAAA,QAAA9Z,GAAA,CAAAkI,OAAA,GAAA0R,EAAA;MAAFva,EAAE,CAAAwa,cAAA,CAAAD,EAAA,GAAFva,EAAE,CAAAya,WAAA,QAAA9Z,GAAA,CAAAuI,YAAA,GAAAqR,EAAA;IAAA;EAAA;EAAAI,SAAA,WAAAC,iBAAAxa,EAAA,EAAAO,GAAA;IAAA,IAAAP,EAAA;MAAFJ,EAAE,CAAA6a,WAAA,CAAAC,GAAA;MAAF9a,EAAE,CAAA6a,WAAA,CAAAE,GAAA;MAAF/a,EAAE,CAAA6a,WAAA,CAFoarY,mBAAmB;IAAA;IAAA,IAAApC,EAAA;MAAA,IAAAma,EAAA;MAEzbva,EAAE,CAAAwa,cAAA,CAAAD,EAAA,GAAFva,EAAE,CAAAya,WAAA,QAAA9Z,GAAA,CAAAqa,OAAA,GAAAT,EAAA,CAAAG,KAAA;MAAF1a,EAAE,CAAAwa,cAAA,CAAAD,EAAA,GAAFva,EAAE,CAAAya,WAAA,QAAA9Z,GAAA,CAAAoI,KAAA,GAAAwR,EAAA,CAAAG,KAAA;MAAF1a,EAAE,CAAAwa,cAAA,CAAAD,EAAA,GAAFva,EAAE,CAAAya,WAAA,QAAA9Z,GAAA,CAAA6Q,WAAA,GAAA+I,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAO,SAAA,WAL21C,UAAU,mBAAmB,SAAS;EAAAC,QAAA;EAAAC,YAAA,WAAAC,wBAAAhb,EAAA,EAAAO,GAAA;IAAA,IAAAP,EAAA;MAKn4CJ,EAAE,CAAAsB,UAAA,qBAAA+Z,sCAAA7Z,MAAA;QAAA,OALQb,GAAA,CAAAgB,cAAA,CAAAH,MAAqB,CAAC;MAAA,CAAd,CAAC,mBAAA8Z,oCAAA;QAAA,OAAT3a,GAAA,CAAAiV,QAAA,CAAS,CAAC;MAAA,CAAF,CAAC,kBAAA2F,mCAAA;QAAA,OAAT5a,GAAA,CAAAkV,OAAA,CAAQ,CAAC;MAAA,CAAD,CAAC;IAAA;IAAA,IAAAzV,EAAA;MAKnBJ,EAAE,CAAAkC,WAAA,OAAAvB,GAAA,CAAAwB,EAAA,cAAAxB,GAAA,CAAAmN,QAAA,IALoB,CAAC,GAAAnN,GAAA,CAAAuN,QAAA,mBAAAvN,GAAA,CAAAkN,SAAA,GAAAlN,GAAA,CAAAwB,EAAA,GAAI,QAAQ,GAAG,IAAI,mBAAAxB,GAAA,CAAAkN,SAAA,gBAAAlN,GAAA,CAAA0B,SAAA,IAAnB,IAAI,mBAAjB1B,GAAA,CAAA0J,QAAA,CAAAmR,QAAA,CAAkB,CAAC,mBAAnB7a,GAAA,CAAAmN,QAAA,CAAA0N,QAAA,CAAkB,CAAC,kBAAA7a,GAAA,CAAAmL,UAAA,2BAAnBnL,GAAA,CAAA2Y,wBAAA,CAAyB,CAAC;MAKpCtZ,EAAE,CAAA8B,WAAA,4BAAAnB,GAAA,CAAAmN,QALgB,CAAC,2BAAAnN,GAAA,CAAAmL,UAAD,CAAC,4BAAAnL,GAAA,CAAA0J,QAAD,CAAC,yBAAA1J,GAAA,CAAAoT,KAAD,CAAC,4BAAApT,GAAA,CAAAyB,QAAD,CAAC;IAAA;EAAA;EAAAqZ,MAAA;IAAAC,mBAAA;IAAAzZ,UAAA;IAAA6L,QAAA,8BAAuL3K,gBAAgB;IAAAwY,aAAA,wCAAqDxY,gBAAgB;IAAA+K,QAAA,8BAAuC3F,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGnF,eAAe,CAACmF,KAAK,CAAE;IAAAwB,4BAAA,sEAAkG5G,gBAAgB;IAAA1C,WAAA;IAAA4J,QAAA,8BAAkElH,gBAAgB;IAAAf,QAAA,8BAAsCe,gBAAgB;IAAAgK,sBAAA,0DAAgFhK,gBAAgB;IAAA6H,WAAA;IAAAzC,KAAA;IAAAlG,SAAA;IAAAgX,cAAA;IAAA5N,iBAAA;IAAA2C,yBAAA,gEAA4QhL,eAAe;IAAAmV,cAAA;IAAApW,EAAA;IAAAiL,UAAA;IAAAC,wBAAA,8DAA4JlK,gBAAgB;EAAA;EAAAyY,OAAA;IAAAjO,YAAA;IAAAkO,aAAA;IAAAC,aAAA;IAAAlD,eAAA;IAAAD,WAAA;EAAA;EAAAoD,QAAA;EAAAC,QAAA,GAK7pChc,EAAE,CAAAic,kBAAA,CAL8nE,CACrsE;IAAEjU,OAAO,EAAE9B,mBAAmB;IAAEgW,WAAW,EAAEzT;EAAU,CAAC,EACxD;IAAET,OAAO,EAAExB,2BAA2B;IAAE0V,WAAW,EAAEzT;EAAU,CAAC,CACnE,GAEwEzI,EAAE,CAAAmc,oBAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,oBAAAtc,EAAA,EAAAO,GAAA;IAAA,IAAAP,EAAA;MAAA,MAAAuc,GAAA,GAAF3c,EAAE,CAAAqB,gBAAA;MAAFrB,EAAE,CAAA4c,eAAA,CAAAC,GAAA;MAAF7c,EAAE,CAAAC,cAAA,eAF8qB,CAAC;MAEjrBD,EAAE,CAAAsB,UAAA,mBAAAwb,yCAAA;QAAF9c,EAAE,CAAAyB,aAAA,CAAAkb,GAAA;QAAA,OAAF3c,EAAE,CAAA0B,WAAA,CAFqmBf,GAAA,CAAAuQ,IAAA,CAAK,CAAC;MAAA,CAAC,CAAC;MAE/mBlR,EAAE,CAAAC,cAAA,YAF+uB,CAAC;MAElvBD,EAAE,CAAAgB,UAAA,IAAA+b,iCAAA,iBAFkwB,CAAC,IAAAhc,iCAAA,iBAA+G,CAAC;MAEr3Bf,EAAE,CAAAG,YAAA,CAF+oC,CAAC;MAElpCH,EAAE,CAAAC,cAAA,YAFisC,CAAC,YAAyC,CAAC;MAE9uCD,EAAE,CAAAgd,cAAA;MAAFhd,EAAE,CAAAC,cAAA,YAF47C,CAAC;MAE/7CD,EAAE,CAAAid,SAAA,aAFk+C,CAAC;MAEr+Cjd,EAAE,CAAAG,YAAA,CAFg/C,CAAC,CAAW,CAAC,CAAS,CAAC,CAAO,CAAC;MAEjhDH,EAAE,CAAAgB,UAAA,KAAAG,kCAAA,0BAF+rE,CAAC;MAElsEnB,EAAE,CAAAsB,UAAA,oBAAA4b,mDAAA;QAAFld,EAAE,CAAAyB,aAAA,CAAAkb,GAAA;QAAA,OAAF3c,EAAE,CAAA0B,WAAA,CAFimEf,GAAA,CAAAsQ,KAAA,CAAM,CAAC;MAAA,CAAC,CAAC,2BAAAkM,0DAAA;QAE5mEnd,EAAE,CAAAyB,aAAA,CAAAkb,GAAA;QAAA,OAAF3c,EAAE,CAAA0B,WAAA,CAFgoEf,GAAA,CAAAsQ,KAAA,CAAM,CAAC;MAAA,CAAC,CAAC,4BAAAmM,2DAAA5b,MAAA;QAE3oExB,EAAE,CAAAyB,aAAA,CAAAkb,GAAA;QAAA,OAAF3c,EAAE,CAAA0B,WAAA,CAFgqEf,GAAA,CAAAgV,qBAAA,CAAAnU,MAA4B,CAAC;MAAA,CAAC,CAAC;IAAA;IAAA,IAAApB,EAAA;MAAA,MAAAid,wBAAA,GAEjsErd,EAAE,CAAAsd,WAAA;MAAFtd,EAAE,CAAAO,SAAA,EAF8uB,CAAC;MAEjvBP,EAAE,CAAAkC,WAAA,OAAAvB,GAAA,CAAA6Y,QAAA;MAAFxZ,EAAE,CAAAO,SAAA,CAFqoC,CAAC;MAExoCP,EAAE,CAAAiB,aAAA,CAAAN,GAAA,CAAAoT,KAAA,QAFqoC,CAAC;MAExoC/T,EAAE,CAAAO,SAAA,EAFmvD,CAAC;MAEtvDP,EAAE,CAAAgC,UAAA,wCAFmvD,CAAC,kCAAArB,GAAA,CAAA4c,kBAAyD,CAAC,sCAAA5c,GAAA,CAAA0N,eAA0D,CAAC,8BAAA1N,GAAA,CAAAoO,uBAAA,IAAAsO,wBAAmF,CAAC,iCAAA1c,GAAA,CAAA6c,UAAgD,CAAC,6BAAA7c,GAAA,CAAAkO,aAA+C,CAAC,8CAAmD,CAAC;IAAA;EAAA;EAAA4O,YAAA,GAA4mLhb,gBAAgB,EAAuID,mBAAmB,EAA4+BsD,OAAO;EAAA4X,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAE96R;EAAA,QAAA7S,SAAA,oBAAAA,SAAA,KAAiF/K,EAAE,CAAA6d,iBAAA,CAAQpV,SAAS,EAAc,CAAC;IACvGwR,IAAI,EAAE5W,SAAS;IACfya,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEhC,QAAQ,EAAE,WAAW;MAAE4B,aAAa,EAAEra,iBAAiB,CAAC0a,IAAI;MAAEJ,eAAe,EAAEra,uBAAuB,CAAC0a,MAAM;MAAEC,IAAI,EAAE;QAC1I,MAAM,EAAE,UAAU;QAClB,eAAe,EAAE,SAAS;QAC1B,OAAO,EAAE,gBAAgB;QACzB,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,0BAA0B;QAC7C,sBAAsB,EAAE,kCAAkC;QAC1D,sBAAsB,EAAE,WAAW;QACnC,mBAAmB,EAAE,mBAAmB;QACxC,sBAAsB,EAAE,qBAAqB;QAC7C,sBAAsB,EAAE,qBAAqB;QAC7C,qBAAqB,EAAE,YAAY;QACnC,8BAA8B,EAAE,4BAA4B;QAC5D,iCAAiC,EAAE,UAAU;QAC7C,gCAAgC,EAAE,YAAY;QAC9C,iCAAiC,EAAE,UAAU;QAC7C,8BAA8B,EAAE,OAAO;QACvC,iCAAiC,EAAE,UAAU;QAC7C,WAAW,EAAE,wBAAwB;QACrC,SAAS,EAAE,YAAY;QACvB,QAAQ,EAAE;MACd,CAAC;MAAEC,SAAS,EAAE,CACV;QAAEnW,OAAO,EAAE9B,mBAAmB;QAAEgW,WAAW,EAAEzT;MAAU,CAAC,EACxD;QAAET,OAAO,EAAExB,2BAA2B;QAAE0V,WAAW,EAAEzT;MAAU,CAAC,CACnE;MAAE2V,OAAO,EAAE,CAAC3b,gBAAgB,EAAED,mBAAmB,EAAEsD,OAAO,CAAC;MAAE2W,QAAQ,EAAE,ktEAAktE;MAAEiB,MAAM,EAAE,CAAC,+5JAA+5J;IAAE,CAAC;EACntO,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE7U,OAAO,EAAE,CAAC;MAClDoR,IAAI,EAAEzW,eAAe;MACrBsa,IAAI,EAAE,CAACpX,SAAS,EAAE;QAAE2X,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEnV,YAAY,EAAE,CAAC;MACf+Q,IAAI,EAAEzW,eAAe;MACrBsa,IAAI,EAAE,CAAClX,YAAY,EAAE;QAAEyX,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEnd,aAAa,EAAE,CAAC;MAChB+Y,IAAI,EAAExW,YAAY;MAClBqa,IAAI,EAAE,CAAC3V,kBAAkB;IAC7B,CAAC,CAAC;IAAEuT,mBAAmB,EAAE,CAAC;MACtBzB,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE9C,OAAO,EAAE,CAAC;MACVf,IAAI,EAAEtW,SAAS;MACfma,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE/U,KAAK,EAAE,CAAC;MACRkR,IAAI,EAAEtW,SAAS;MACfma,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEtM,WAAW,EAAE,CAAC;MACdyI,IAAI,EAAEtW,SAAS;MACfma,IAAI,EAAE,CAACtb,mBAAmB;IAC9B,CAAC,CAAC;IAAEP,UAAU,EAAE,CAAC;MACbgY,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAEoK,QAAQ,EAAE,CAAC;MACXmM,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEnb;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwY,aAAa,EAAE,CAAC;MAChB1B,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEnb;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+K,QAAQ,EAAE,CAAC;MACX+L,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC;QACCQ,SAAS,EAAG/V,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGnF,eAAe,CAACmF,KAAK;MACpE,CAAC;IACT,CAAC,CAAC;IAAEwB,4BAA4B,EAAE,CAAC;MAC/BkQ,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEnb;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE1C,WAAW,EAAE,CAAC;MACdwZ,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAE2G,QAAQ,EAAE,CAAC;MACX4P,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEnb;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEf,QAAQ,EAAE,CAAC;MACX6X,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEnb;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgK,sBAAsB,EAAE,CAAC;MACzB8M,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEnb;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6H,WAAW,EAAE,CAAC;MACdiP,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAE6E,KAAK,EAAE,CAAC;MACR0R,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAErB,SAAS,EAAE,CAAC;MACZ4X,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEzE,cAAc,EAAE,CAAC;MACjBY,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAErS,iBAAiB,EAAE,CAAC;MACpBwO,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAE0K,yBAAyB,EAAE,CAAC;MAC5B6L,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAElb;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEmV,cAAc,EAAE,CAAC;MACjB0B,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAEvB,EAAE,EAAE,CAAC;MACL8X,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAE0J,UAAU,EAAE,CAAC;MACb6M,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAE2J,wBAAwB,EAAE,CAAC;MAC3B4M,IAAI,EAAEvW,KAAK;MACXoa,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEnb;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwK,YAAY,EAAE,CAAC;MACfsM,IAAI,EAAErW;IACV,CAAC,CAAC;IAAEiY,aAAa,EAAE,CAAC;MAChB5B,IAAI,EAAErW,MAAM;MACZka,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAEhC,aAAa,EAAE,CAAC;MAChB7B,IAAI,EAAErW,MAAM;MACZka,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAElF,eAAe,EAAE,CAAC;MAClBqB,IAAI,EAAErW;IACV,CAAC,CAAC;IAAE+U,WAAW,EAAE,CAAC;MACdsB,IAAI,EAAErW;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAM2a,gBAAgB,CAAC;AAGtBC,iBAAA,GAHKD,gBAAgB;AAAA/V,eAAA,CAAhB+V,gBAAgB,wBAAAE,0BAAA1E,iBAAA;EAAA,YAAAA,iBAAA,IACiFwE,iBAAgB;AAAA;AAAA/V,eAAA,CADjH+V,gBAAgB,8BAnH2Dve,EAAE,CAAA0e,iBAAA;EAAAzE,IAAA,EAqHQsE,iBAAgB;EAAArE,SAAA;EAAA8B,QAAA,GArH1Bhc,EAAE,CAAAic,kBAAA,CAqHyF,CAAC;IAAEjU,OAAO,EAAEG,kBAAkB;IAAE+T,WAAW,EAAEqC;EAAiB,CAAC,CAAC;AAAA;AAE5O;EAAA,QAAAxT,SAAA,oBAAAA,SAAA,KAvHiF/K,EAAE,CAAA6d,iBAAA,CAuHQU,gBAAgB,EAAc,CAAC;IAC9GtE,IAAI,EAAEpW,SAAS;IACfia,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BI,SAAS,EAAE,CAAC;QAAEnW,OAAO,EAAEG,kBAAkB;QAAE+T,WAAW,EAAEqC;MAAiB,CAAC;IAC9E,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMI,eAAe,CAAC;AAYrBC,gBAAA,GAZKD,eAAe;AAAAnW,eAAA,CAAfmW,eAAe,wBAAAE,yBAAA9E,iBAAA;EAAA,YAAAA,iBAAA,IACkF4E,gBAAe;AAAA;AAAAnW,eAAA,CADhHmW,eAAe,8BA/H4D3e,EAAE,CAAA8e,gBAAA;EAAA7E,IAAA,EAiIqB0E,gBAAe;EAAAP,OAAA,GAAY1b,aAAa,EAAEsE,eAAe,EAAEC,eAAe,EAAEwB,SAAS,EAAE8V,gBAAgB;EAAAQ,OAAA,GAAa/a,mBAAmB,EACnOkD,kBAAkB,EAClBuB,SAAS,EACT8V,gBAAgB,EAChBvX,eAAe,EACfC,eAAe;AAAA;AAAAuB,eAAA,CAPrBmW,eAAe,8BA/H4D3e,EAAE,CAAAgf,gBAAA;EAAAb,SAAA,EAuIiD,CAACpW,mCAAmC,CAAC;EAAAqW,OAAA,GAAY1b,aAAa,EAAEsE,eAAe,EAAEC,eAAe,EAAEjD,mBAAmB,EAC7OkD,kBAAkB,EAClBF,eAAe,EACfC,eAAe;AAAA;AAE3B;EAAA,QAAA8D,SAAA,oBAAAA,SAAA,KA5IiF/K,EAAE,CAAA6d,iBAAA,CA4IQc,eAAe,EAAc,CAAC;IAC7G1E,IAAI,EAAEnW,QAAQ;IACdga,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAAC1b,aAAa,EAAEsE,eAAe,EAAEC,eAAe,EAAEwB,SAAS,EAAE8V,gBAAgB,CAAC;MACvFQ,OAAO,EAAE,CACL/a,mBAAmB,EACnBkD,kBAAkB,EAClBuB,SAAS,EACT8V,gBAAgB,EAChBvX,eAAe,EACfC,eAAe,CAClB;MACDkX,SAAS,EAAE,CAACpW,mCAAmC;IACnD,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAAS4W,eAAe,IAAIlY,CAAC,EAAEc,0BAA0B,IAAI+Q,CAAC,EAAEzQ,2CAA2C,IAAIxB,CAAC,EAAEyB,iBAAiB,IAAIvB,CAAC,EAAEwB,mCAAmC,IAAIpB,CAAC,EAAEwB,kBAAkB,IAAI8W,CAAC,EAAE7W,eAAe,IAAI8W,CAAC,EAAEzW,SAAS,IAAI0W,CAAC,EAAEZ,gBAAgB,IAAIxY,CAAC;AACxQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}