{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Latvian [lv]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/skakri\n//! author : <PERSON><PERSON><PERSON> : https://github.com/JanisE\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var units = {\n    ss: 'sekundes_sekundēm_sekunde_sekundes'.split('_'),\n    m: 'minūtes_minūtēm_minūte_minūtes'.split('_'),\n    mm: 'minūtes_minūtēm_minūte_minūtes'.split('_'),\n    h: 'stundas_stundām_stunda_stundas'.split('_'),\n    hh: 'stundas_stundām_stunda_stundas'.split('_'),\n    d: 'dienas_dienām_diena_dienas'.split('_'),\n    dd: 'dienas_dienām_diena_dienas'.split('_'),\n    M: 'mēneša_mēnešiem_mēnesis_mēneši'.split('_'),\n    MM: 'mēneša_mēnešiem_mēnesis_mēneši'.split('_'),\n    y: 'gada_gadiem_gads_gadi'.split('_'),\n    yy: 'gada_gadiem_gads_gadi'.split('_')\n  };\n  /**\n   * @param withoutSuffix boolean true = a length of time; false = before/after a period of time.\n   */\n  function format(forms, number, withoutSuffix) {\n    if (withoutSuffix) {\n      // E.g. \"21 minūte\", \"3 minūtes\".\n      return number % 10 === 1 && number % 100 !== 11 ? forms[2] : forms[3];\n    } else {\n      // E.g. \"21 minūtes\" as in \"pēc 21 minūtes\".\n      // E.g. \"3 minūtēm\" as in \"pēc 3 minūtēm\".\n      return number % 10 === 1 && number % 100 !== 11 ? forms[0] : forms[1];\n    }\n  }\n  function relativeTimeWithPlural(number, withoutSuffix, key) {\n    return number + ' ' + format(units[key], number, withoutSuffix);\n  }\n  function relativeTimeWithSingular(number, withoutSuffix, key) {\n    return format(units[key], number, withoutSuffix);\n  }\n  function relativeSeconds(number, withoutSuffix) {\n    return withoutSuffix ? 'dažas sekundes' : 'dažām sekundēm';\n  }\n  var lv = moment.defineLocale('lv', {\n    months: 'janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris'.split('_'),\n    monthsShort: 'jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec'.split('_'),\n    weekdays: 'svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena'.split('_'),\n    weekdaysShort: 'Sv_P_O_T_C_Pk_S'.split('_'),\n    weekdaysMin: 'Sv_P_O_T_C_Pk_S'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY.',\n      LL: 'YYYY. [gada] D. MMMM',\n      LLL: 'YYYY. [gada] D. MMMM, HH:mm',\n      LLLL: 'YYYY. [gada] D. MMMM, dddd, HH:mm'\n    },\n    calendar: {\n      sameDay: '[Šodien pulksten] LT',\n      nextDay: '[Rīt pulksten] LT',\n      nextWeek: 'dddd [pulksten] LT',\n      lastDay: '[Vakar pulksten] LT',\n      lastWeek: '[Pagājušā] dddd [pulksten] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'pēc %s',\n      past: 'pirms %s',\n      s: relativeSeconds,\n      ss: relativeTimeWithPlural,\n      m: relativeTimeWithSingular,\n      mm: relativeTimeWithPlural,\n      h: relativeTimeWithSingular,\n      hh: relativeTimeWithPlural,\n      d: relativeTimeWithSingular,\n      dd: relativeTimeWithPlural,\n      M: relativeTimeWithSingular,\n      MM: relativeTimeWithPlural,\n      y: relativeTimeWithSingular,\n      yy: relativeTimeWithPlural\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return lv;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "units", "ss", "split", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "format", "forms", "number", "withoutSuffix", "relativeTimeWithPlural", "key", "relativeTimeWithSingular", "relativeSeconds", "lv", "defineLocale", "months", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/moment/locale/lv.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Latvian [lv]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/skakri\n//! author : <PERSON><PERSON><PERSON> : https://github.com/JanisE\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var units = {\n        ss: 'sekundes_sekundēm_sekunde_sekundes'.split('_'),\n        m: 'minūtes_minūtēm_minūte_minūtes'.split('_'),\n        mm: 'minūtes_minūtēm_minūte_minūtes'.split('_'),\n        h: 'stundas_stundām_stunda_stundas'.split('_'),\n        hh: 'stundas_stundām_stunda_stundas'.split('_'),\n        d: 'dienas_dienām_diena_dienas'.split('_'),\n        dd: 'dienas_dienām_diena_dienas'.split('_'),\n        M: 'mēneša_mēnešiem_mēnesis_mēneši'.split('_'),\n        MM: 'mēneša_mēnešiem_mēnesis_mēneši'.split('_'),\n        y: 'gada_gadiem_gads_gadi'.split('_'),\n        yy: 'gada_gadiem_gads_gadi'.split('_'),\n    };\n    /**\n     * @param withoutSuffix boolean true = a length of time; false = before/after a period of time.\n     */\n    function format(forms, number, withoutSuffix) {\n        if (withoutSuffix) {\n            // E.g. \"21 minūte\", \"3 minūtes\".\n            return number % 10 === 1 && number % 100 !== 11 ? forms[2] : forms[3];\n        } else {\n            // E.g. \"21 minūtes\" as in \"pēc 21 minūtes\".\n            // E.g. \"3 minūtēm\" as in \"pēc 3 minūtēm\".\n            return number % 10 === 1 && number % 100 !== 11 ? forms[0] : forms[1];\n        }\n    }\n    function relativeTimeWithPlural(number, withoutSuffix, key) {\n        return number + ' ' + format(units[key], number, withoutSuffix);\n    }\n    function relativeTimeWithSingular(number, withoutSuffix, key) {\n        return format(units[key], number, withoutSuffix);\n    }\n    function relativeSeconds(number, withoutSuffix) {\n        return withoutSuffix ? 'dažas sekundes' : 'dažām sekundēm';\n    }\n\n    var lv = moment.defineLocale('lv', {\n        months: 'janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris'.split(\n            '_'\n        ),\n        monthsShort: 'jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec'.split('_'),\n        weekdays: 'svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena'.split(\n            '_'\n        ),\n        weekdaysShort: 'Sv_P_O_T_C_Pk_S'.split('_'),\n        weekdaysMin: 'Sv_P_O_T_C_Pk_S'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY.',\n            LL: 'YYYY. [gada] D. MMMM',\n            LLL: 'YYYY. [gada] D. MMMM, HH:mm',\n            LLLL: 'YYYY. [gada] D. MMMM, dddd, HH:mm',\n        },\n        calendar: {\n            sameDay: '[Šodien pulksten] LT',\n            nextDay: '[Rīt pulksten] LT',\n            nextWeek: 'dddd [pulksten] LT',\n            lastDay: '[Vakar pulksten] LT',\n            lastWeek: '[Pagājušā] dddd [pulksten] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'pēc %s',\n            past: 'pirms %s',\n            s: relativeSeconds,\n            ss: relativeTimeWithPlural,\n            m: relativeTimeWithSingular,\n            mm: relativeTimeWithPlural,\n            h: relativeTimeWithSingular,\n            hh: relativeTimeWithPlural,\n            d: relativeTimeWithSingular,\n            dd: relativeTimeWithPlural,\n            M: relativeTimeWithSingular,\n            MM: relativeTimeWithPlural,\n            y: relativeTimeWithSingular,\n            yy: relativeTimeWithPlural,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return lv;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,KAAK,GAAG;IACRC,EAAE,EAAE,oCAAoC,CAACC,KAAK,CAAC,GAAG,CAAC;IACnDC,CAAC,EAAE,gCAAgC,CAACD,KAAK,CAAC,GAAG,CAAC;IAC9CE,EAAE,EAAE,gCAAgC,CAACF,KAAK,CAAC,GAAG,CAAC;IAC/CG,CAAC,EAAE,gCAAgC,CAACH,KAAK,CAAC,GAAG,CAAC;IAC9CI,EAAE,EAAE,gCAAgC,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC/CK,CAAC,EAAE,4BAA4B,CAACL,KAAK,CAAC,GAAG,CAAC;IAC1CM,EAAE,EAAE,4BAA4B,CAACN,KAAK,CAAC,GAAG,CAAC;IAC3CO,CAAC,EAAE,gCAAgC,CAACP,KAAK,CAAC,GAAG,CAAC;IAC9CQ,EAAE,EAAE,gCAAgC,CAACR,KAAK,CAAC,GAAG,CAAC;IAC/CS,CAAC,EAAE,uBAAuB,CAACT,KAAK,CAAC,GAAG,CAAC;IACrCU,EAAE,EAAE,uBAAuB,CAACV,KAAK,CAAC,GAAG;EACzC,CAAC;EACD;AACJ;AACA;EACI,SAASW,MAAMA,CAACC,KAAK,EAAEC,MAAM,EAAEC,aAAa,EAAE;IAC1C,IAAIA,aAAa,EAAE;MACf;MACA,OAAOD,MAAM,GAAG,EAAE,KAAK,CAAC,IAAIA,MAAM,GAAG,GAAG,KAAK,EAAE,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;IACzE,CAAC,MAAM;MACH;MACA;MACA,OAAOC,MAAM,GAAG,EAAE,KAAK,CAAC,IAAIA,MAAM,GAAG,GAAG,KAAK,EAAE,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;IACzE;EACJ;EACA,SAASG,sBAAsBA,CAACF,MAAM,EAAEC,aAAa,EAAEE,GAAG,EAAE;IACxD,OAAOH,MAAM,GAAG,GAAG,GAAGF,MAAM,CAACb,KAAK,CAACkB,GAAG,CAAC,EAAEH,MAAM,EAAEC,aAAa,CAAC;EACnE;EACA,SAASG,wBAAwBA,CAACJ,MAAM,EAAEC,aAAa,EAAEE,GAAG,EAAE;IAC1D,OAAOL,MAAM,CAACb,KAAK,CAACkB,GAAG,CAAC,EAAEH,MAAM,EAAEC,aAAa,CAAC;EACpD;EACA,SAASI,eAAeA,CAACL,MAAM,EAAEC,aAAa,EAAE;IAC5C,OAAOA,aAAa,GAAG,gBAAgB,GAAG,gBAAgB;EAC9D;EAEA,IAAIK,EAAE,GAAGtB,MAAM,CAACuB,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,sGAAsG,CAACrB,KAAK,CAChH,GACJ,CAAC;IACDsB,WAAW,EAAE,iDAAiD,CAACtB,KAAK,CAAC,GAAG,CAAC;IACzEuB,QAAQ,EAAE,yEAAyE,CAACvB,KAAK,CACrF,GACJ,CAAC;IACDwB,aAAa,EAAE,iBAAiB,CAACxB,KAAK,CAAC,GAAG,CAAC;IAC3CyB,WAAW,EAAE,iBAAiB,CAACzB,KAAK,CAAC,GAAG,CAAC;IACzC0B,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,sBAAsB;MAC1BC,GAAG,EAAE,6BAA6B;MAClCC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,sBAAsB;MAC/BC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,oBAAoB;MAC9BC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,+BAA+B;MACzCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE1B,eAAe;MAClBnB,EAAE,EAAEgB,sBAAsB;MAC1Bd,CAAC,EAAEgB,wBAAwB;MAC3Bf,EAAE,EAAEa,sBAAsB;MAC1BZ,CAAC,EAAEc,wBAAwB;MAC3Bb,EAAE,EAAEW,sBAAsB;MAC1BV,CAAC,EAAEY,wBAAwB;MAC3BX,EAAE,EAAES,sBAAsB;MAC1BR,CAAC,EAAEU,wBAAwB;MAC3BT,EAAE,EAAEO,sBAAsB;MAC1BN,CAAC,EAAEQ,wBAAwB;MAC3BP,EAAE,EAAEK;IACR,CAAC;IACD8B,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO9B,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}