{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiDatetimepickerComponent } from './swui-datetimepicker.component';\nimport { SwuiCalendarModule } from '../swui-calendar/swui-calendar.module';\nimport { SwuiTimepickerModule } from '../swui-timepicker/swui-timepicker.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nexport const DATETIMEPICKER_MODULES = [MatDividerModule, MatButtonModule, MatInputModule, MatMenuModule, ReactiveFormsModule, SwuiCalendarModule, SwuiTimepickerModule];\n/**\n * @deprecated use lib-swui-date-picker\n */\nlet SwuiDatetimepickerModule = class SwuiDatetimepickerModule {};\nSwuiDatetimepickerModule = __decorate([NgModule({\n  declarations: [SwuiDatetimepickerComponent],\n  exports: [SwuiDatetimepickerComponent],\n  imports: [CommonModule, ...DATETIMEPICKER_MODULES]\n})], SwuiDatetimepickerModule);\nexport { SwuiDatetimepickerModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ReactiveFormsModule", "SwuiDatetimepickerComponent", "SwuiCalendarModule", "SwuiTimepickerModule", "MatMenuModule", "MatDividerModule", "MatInputModule", "MatButtonModule", "DATETIMEPICKER_MODULES", "SwuiDatetimepickerModule", "__decorate", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-datetimepicker/swui-datetimepicker.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\n\nimport { SwuiDatetimepickerComponent } from './swui-datetimepicker.component';\nimport { SwuiCalendarModule } from '../swui-calendar/swui-calendar.module';\nimport { SwuiTimepickerModule } from '../swui-timepicker/swui-timepicker.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\n\nexport const DATETIMEPICKER_MODULES = [\n  MatDividerModule,\n  MatButtonModule,\n  MatInputModule,\n  MatMenuModule,\n  ReactiveFormsModule,\n  SwuiCalendarModule,\n  SwuiTimepickerModule,\n];\n\n/**\n * @deprecated use lib-swui-date-picker\n */\n\n@NgModule({\n  declarations: [\n    SwuiDatetimepickerComponent\n  ],\n  exports: [\n    SwuiDatetimepickerComponent\n  ],\n  imports: [\n    CommonModule,\n    ...DATETIMEPICKER_MODULES,\n  ]\n})\nexport class SwuiDatetimepickerModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,OAAO,MAAMC,sBAAsB,GAAG,CACpCH,gBAAgB,EAChBE,eAAe,EACfD,cAAc,EACdF,aAAa,EACbJ,mBAAmB,EACnBE,kBAAkB,EAClBC,oBAAoB,CACrB;AAED;;;AAgBO,IAAMM,wBAAwB,GAA9B,MAAMA,wBAAwB,GACpC;AADYA,wBAAwB,GAAAC,UAAA,EAZpCZ,QAAQ,CAAC;EACRa,YAAY,EAAE,CACZV,2BAA2B,CAC5B;EACDW,OAAO,EAAE,CACPX,2BAA2B,CAC5B;EACDY,OAAO,EAAE,CACPd,YAAY,EACZ,GAAGS,sBAAsB;CAE5B,CAAC,C,EACWC,wBAAwB,CACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}