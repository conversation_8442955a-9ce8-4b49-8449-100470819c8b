{"ast": null, "code": "import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\nexport var asapScheduler = new AsapScheduler(AsapAction);\nexport var asap = asapScheduler;\n//# sourceMappingURL=asap.js.map", "map": {"version": 3, "names": ["AsapAction", "AsapScheduler", "asapScheduler", "asap"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/asap.js"], "sourcesContent": ["import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\nexport var asapScheduler = new AsapScheduler(AsapAction);\nexport var asap = asapScheduler;\n//# sourceMappingURL=asap.js.map"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,OAAO,IAAIC,aAAa,GAAG,IAAID,aAAa,CAACD,UAAU,CAAC;AACxD,OAAO,IAAIG,IAAI,GAAGD,aAAa;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}