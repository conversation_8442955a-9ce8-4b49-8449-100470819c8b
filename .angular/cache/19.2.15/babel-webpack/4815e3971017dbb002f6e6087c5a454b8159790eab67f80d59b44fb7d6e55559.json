{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _CdkAccordion, _CdkAccordionItem, _CdkAccordionModule;\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, booleanAttribute, Directive, Input, ChangeDetectorRef, EventEmitter, Output, NgModule } from '@angular/core';\nimport { Subject, Subscription } from 'rxjs';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { U as UniqueSelectionDispatcher } from './unique-selection-dispatcher-DtHZDqyJ.mjs';\n\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_ACCORDION = new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\nclass CdkAccordion {\n  constructor() {\n    /** Emits when the state of the accordion changes */\n    _defineProperty(this, \"_stateChanges\", new Subject());\n    /** Stream that emits true/false when openAll/closeAll is triggered. */\n    _defineProperty(this, \"_openCloseAllActions\", new Subject());\n    /** A readonly id value to use for unique selection coordination. */\n    _defineProperty(this, \"id\", inject(_IdGenerator).getId('cdk-accordion-'));\n    /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n    _defineProperty(this, \"multi\", false);\n  }\n  /** Opens all enabled accordion items in an accordion where multi is enabled. */\n  openAll() {\n    if (this.multi) {\n      this._openCloseAllActions.next(true);\n    }\n  }\n  /** Closes all enabled accordion items. */\n  closeAll() {\n    this._openCloseAllActions.next(false);\n  }\n  ngOnChanges(changes) {\n    this._stateChanges.next(changes);\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._openCloseAllActions.complete();\n  }\n}\n_CdkAccordion = CdkAccordion;\n_defineProperty(CdkAccordion, \"\\u0275fac\", function _CdkAccordion_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkAccordion)();\n});\n_defineProperty(CdkAccordion, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkAccordion,\n  selectors: [[\"cdk-accordion\"], [\"\", \"cdkAccordion\", \"\"]],\n  inputs: {\n    multi: [2, \"multi\", \"multi\", booleanAttribute]\n  },\n  exportAs: [\"cdkAccordion\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_ACCORDION,\n    useExisting: _CdkAccordion\n  }]), i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-accordion, [cdkAccordion]',\n      exportAs: 'cdkAccordion',\n      providers: [{\n        provide: CDK_ACCORDION,\n        useExisting: CdkAccordion\n      }]\n    }]\n  }], null, {\n    multi: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * A basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\nclass CdkAccordionItem {\n  /** Whether the AccordionItem is expanded. */\n  get expanded() {\n    return this._expanded;\n  }\n  set expanded(expanded) {\n    // Only emit events and update the internal value if the value changes.\n    if (this._expanded !== expanded) {\n      this._expanded = expanded;\n      this.expandedChange.emit(expanded);\n      if (expanded) {\n        this.opened.emit();\n        /**\n         * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n         * the name value is the id of the accordion.\n         */\n        const accordionId = this.accordion ? this.accordion.id : this.id;\n        this._expansionDispatcher.notify(this.id, accordionId);\n      } else {\n        this.closed.emit();\n      }\n      // Ensures that the animation will run when the value is set outside of an `@Input`.\n      // This includes cases like the open, close and toggle methods.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  constructor() {\n    _defineProperty(this, \"accordion\", inject(CDK_ACCORDION, {\n      optional: true,\n      skipSelf: true\n    }));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_expansionDispatcher\", inject(UniqueSelectionDispatcher));\n    /** Subscription to openAll/closeAll events. */\n    _defineProperty(this, \"_openCloseAllSubscription\", Subscription.EMPTY);\n    /** Event emitted every time the AccordionItem is closed. */\n    _defineProperty(this, \"closed\", new EventEmitter());\n    /** Event emitted every time the AccordionItem is opened. */\n    _defineProperty(this, \"opened\", new EventEmitter());\n    /** Event emitted when the AccordionItem is destroyed. */\n    _defineProperty(this, \"destroyed\", new EventEmitter());\n    /**\n     * Emits whenever the expanded state of the accordion changes.\n     * Primarily used to facilitate two-way binding.\n     * @docs-private\n     */\n    _defineProperty(this, \"expandedChange\", new EventEmitter());\n    /** The unique AccordionItem id. */\n    _defineProperty(this, \"id\", inject(_IdGenerator).getId('cdk-accordion-child-'));\n    _defineProperty(this, \"_expanded\", false);\n    /** Whether the AccordionItem is disabled. */\n    _defineProperty(this, \"disabled\", false);\n    /** Unregister function for _expansionDispatcher. */\n    _defineProperty(this, \"_removeUniqueSelectionListener\", () => {});\n  }\n  ngOnInit() {\n    this._removeUniqueSelectionListener = this._expansionDispatcher.listen((id, accordionId) => {\n      if (this.accordion && !this.accordion.multi && this.accordion.id === accordionId && this.id !== id) {\n        this.expanded = false;\n      }\n    });\n    // When an accordion item is hosted in an accordion, subscribe to open/close events.\n    if (this.accordion) {\n      this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n    }\n  }\n  /** Emits an event for the accordion item being destroyed. */\n  ngOnDestroy() {\n    this.opened.complete();\n    this.closed.complete();\n    this.destroyed.emit();\n    this.destroyed.complete();\n    this._removeUniqueSelectionListener();\n    this._openCloseAllSubscription.unsubscribe();\n  }\n  /** Toggles the expanded state of the accordion item. */\n  toggle() {\n    if (!this.disabled) {\n      this.expanded = !this.expanded;\n    }\n  }\n  /** Sets the expanded state of the accordion item to false. */\n  close() {\n    if (!this.disabled) {\n      this.expanded = false;\n    }\n  }\n  /** Sets the expanded state of the accordion item to true. */\n  open() {\n    if (!this.disabled) {\n      this.expanded = true;\n    }\n  }\n  _subscribeToOpenCloseAllActions() {\n    return this.accordion._openCloseAllActions.subscribe(expanded => {\n      // Only change expanded state if item is enabled\n      if (!this.disabled) {\n        this.expanded = expanded;\n      }\n    });\n  }\n}\n_CdkAccordionItem = CdkAccordionItem;\n_defineProperty(CdkAccordionItem, \"\\u0275fac\", function _CdkAccordionItem_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkAccordionItem)();\n});\n_defineProperty(CdkAccordionItem, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkAccordionItem,\n  selectors: [[\"cdk-accordion-item\"], [\"\", \"cdkAccordionItem\", \"\"]],\n  inputs: {\n    expanded: [2, \"expanded\", \"expanded\", booleanAttribute],\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n  },\n  outputs: {\n    closed: \"closed\",\n    opened: \"opened\",\n    destroyed: \"destroyed\",\n    expandedChange: \"expandedChange\"\n  },\n  exportAs: [\"cdkAccordionItem\"],\n  features: [i0.ɵɵProvidersFeature([\n  // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n  // registering to the same accordion.\n  {\n    provide: CDK_ACCORDION,\n    useValue: undefined\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordionItem, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-accordion-item, [cdkAccordionItem]',\n      exportAs: 'cdkAccordionItem',\n      providers: [\n      // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n      // registering to the same accordion.\n      {\n        provide: CDK_ACCORDION,\n        useValue: undefined\n      }]\n    }]\n  }], () => [], {\n    closed: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    destroyed: [{\n      type: Output\n    }],\n    expandedChange: [{\n      type: Output\n    }],\n    expanded: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass CdkAccordionModule {}\n_CdkAccordionModule = CdkAccordionModule;\n_defineProperty(CdkAccordionModule, \"\\u0275fac\", function _CdkAccordionModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkAccordionModule)();\n});\n_defineProperty(CdkAccordionModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _CdkAccordionModule,\n  imports: [CdkAccordion, CdkAccordionItem],\n  exports: [CdkAccordion, CdkAccordionItem]\n}));\n_defineProperty(CdkAccordionModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkAccordion, CdkAccordionItem],\n      exports: [CdkAccordion, CdkAccordionItem]\n    }]\n  }], null, null);\n})();\nexport { CDK_ACCORDION, CdkAccordion, CdkAccordionItem, CdkAccordionModule };\n//# sourceMappingURL=accordion.mjs.map", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "booleanAttribute", "Directive", "Input", "ChangeDetectorRef", "EventEmitter", "Output", "NgModule", "Subject", "Subscription", "_", "_IdGenerator", "U", "UniqueSelectionDispatcher", "CDK_ACCORDION", "CdkAccordion", "constructor", "_defineProperty", "getId", "openAll", "multi", "_openCloseAllActions", "next", "closeAll", "ngOnChanges", "changes", "_stateChanges", "ngOnDestroy", "complete", "_CdkAccordion", "_CdkAccordion_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "inputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "transform", "CdkAccordionItem", "expanded", "_expanded", "expandedChange", "emit", "opened", "accordionId", "accordion", "id", "_expansionDispatcher", "notify", "closed", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "optional", "skipSelf", "EMPTY", "ngOnInit", "_removeUniqueSelectionListener", "listen", "_openCloseAllSubscription", "_subscribeToOpenCloseAllActions", "destroyed", "unsubscribe", "toggle", "disabled", "close", "open", "subscribe", "_CdkAccordionItem", "_CdkAccordionItem_Factory", "outputs", "useValue", "undefined", "CdkAccordionModule", "_CdkAccordionModule", "_CdkAccordionModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/accordion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, booleanAttribute, Directive, Input, ChangeDetectorRef, EventEmitter, Output, NgModule } from '@angular/core';\nimport { Subject, Subscription } from 'rxjs';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { U as UniqueSelectionDispatcher } from './unique-selection-dispatcher-DtHZDqyJ.mjs';\n\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_ACCORDION = new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\nclass CdkAccordion {\n    /** Emits when the state of the accordion changes */\n    _stateChanges = new Subject();\n    /** Stream that emits true/false when openAll/closeAll is triggered. */\n    _openCloseAllActions = new Subject();\n    /** A readonly id value to use for unique selection coordination. */\n    id = inject(_IdGenerator).getId('cdk-accordion-');\n    /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n    multi = false;\n    /** Opens all enabled accordion items in an accordion where multi is enabled. */\n    openAll() {\n        if (this.multi) {\n            this._openCloseAllActions.next(true);\n        }\n    }\n    /** Closes all enabled accordion items. */\n    closeAll() {\n        this._openCloseAllActions.next(false);\n    }\n    ngOnChanges(changes) {\n        this._stateChanges.next(changes);\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n        this._openCloseAllActions.complete();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAccordion, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkAccordion, isStandalone: true, selector: \"cdk-accordion, [cdkAccordion]\", inputs: { multi: [\"multi\", \"multi\", booleanAttribute] }, providers: [{ provide: CDK_ACCORDION, useExisting: CdkAccordion }], exportAs: [\"cdkAccordion\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAccordion, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-accordion, [cdkAccordion]',\n                    exportAs: 'cdkAccordion',\n                    providers: [{ provide: CDK_ACCORDION, useExisting: CdkAccordion }],\n                }]\n        }], propDecorators: { multi: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * A basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\nclass CdkAccordionItem {\n    accordion = inject(CDK_ACCORDION, { optional: true, skipSelf: true });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _expansionDispatcher = inject(UniqueSelectionDispatcher);\n    /** Subscription to openAll/closeAll events. */\n    _openCloseAllSubscription = Subscription.EMPTY;\n    /** Event emitted every time the AccordionItem is closed. */\n    closed = new EventEmitter();\n    /** Event emitted every time the AccordionItem is opened. */\n    opened = new EventEmitter();\n    /** Event emitted when the AccordionItem is destroyed. */\n    destroyed = new EventEmitter();\n    /**\n     * Emits whenever the expanded state of the accordion changes.\n     * Primarily used to facilitate two-way binding.\n     * @docs-private\n     */\n    expandedChange = new EventEmitter();\n    /** The unique AccordionItem id. */\n    id = inject(_IdGenerator).getId('cdk-accordion-child-');\n    /** Whether the AccordionItem is expanded. */\n    get expanded() {\n        return this._expanded;\n    }\n    set expanded(expanded) {\n        // Only emit events and update the internal value if the value changes.\n        if (this._expanded !== expanded) {\n            this._expanded = expanded;\n            this.expandedChange.emit(expanded);\n            if (expanded) {\n                this.opened.emit();\n                /**\n                 * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n                 * the name value is the id of the accordion.\n                 */\n                const accordionId = this.accordion ? this.accordion.id : this.id;\n                this._expansionDispatcher.notify(this.id, accordionId);\n            }\n            else {\n                this.closed.emit();\n            }\n            // Ensures that the animation will run when the value is set outside of an `@Input`.\n            // This includes cases like the open, close and toggle methods.\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _expanded = false;\n    /** Whether the AccordionItem is disabled. */\n    disabled = false;\n    /** Unregister function for _expansionDispatcher. */\n    _removeUniqueSelectionListener = () => { };\n    constructor() { }\n    ngOnInit() {\n        this._removeUniqueSelectionListener = this._expansionDispatcher.listen((id, accordionId) => {\n            if (this.accordion &&\n                !this.accordion.multi &&\n                this.accordion.id === accordionId &&\n                this.id !== id) {\n                this.expanded = false;\n            }\n        });\n        // When an accordion item is hosted in an accordion, subscribe to open/close events.\n        if (this.accordion) {\n            this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n        }\n    }\n    /** Emits an event for the accordion item being destroyed. */\n    ngOnDestroy() {\n        this.opened.complete();\n        this.closed.complete();\n        this.destroyed.emit();\n        this.destroyed.complete();\n        this._removeUniqueSelectionListener();\n        this._openCloseAllSubscription.unsubscribe();\n    }\n    /** Toggles the expanded state of the accordion item. */\n    toggle() {\n        if (!this.disabled) {\n            this.expanded = !this.expanded;\n        }\n    }\n    /** Sets the expanded state of the accordion item to false. */\n    close() {\n        if (!this.disabled) {\n            this.expanded = false;\n        }\n    }\n    /** Sets the expanded state of the accordion item to true. */\n    open() {\n        if (!this.disabled) {\n            this.expanded = true;\n        }\n    }\n    _subscribeToOpenCloseAllActions() {\n        return this.accordion._openCloseAllActions.subscribe(expanded => {\n            // Only change expanded state if item is enabled\n            if (!this.disabled) {\n                this.expanded = expanded;\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAccordionItem, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkAccordionItem, isStandalone: true, selector: \"cdk-accordion-item, [cdkAccordionItem]\", inputs: { expanded: [\"expanded\", \"expanded\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { closed: \"closed\", opened: \"opened\", destroyed: \"destroyed\", expandedChange: \"expandedChange\" }, providers: [\n            // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n            // registering to the same accordion.\n            { provide: CDK_ACCORDION, useValue: undefined },\n        ], exportAs: [\"cdkAccordionItem\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAccordionItem, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-accordion-item, [cdkAccordionItem]',\n                    exportAs: 'cdkAccordionItem',\n                    providers: [\n                        // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n                        // registering to the same accordion.\n                        { provide: CDK_ACCORDION, useValue: undefined },\n                    ],\n                }]\n        }], ctorParameters: () => [], propDecorators: { closed: [{\n                type: Output\n            }], opened: [{\n                type: Output\n            }], destroyed: [{\n                type: Output\n            }], expandedChange: [{\n                type: Output\n            }], expanded: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\nclass CdkAccordionModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAccordionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAccordionModule, imports: [CdkAccordion, CdkAccordionItem], exports: [CdkAccordion, CdkAccordionItem] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAccordionModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAccordionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkAccordion, CdkAccordionItem],\n                    exports: [CdkAccordion, CdkAccordionItem],\n                }]\n        }] });\n\nexport { CDK_ACCORDION, CdkAccordion, CdkAccordionItem, CdkAccordionModule };\n//# sourceMappingURL=accordion.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC7I,SAASC,OAAO,EAAEC,YAAY,QAAQ,MAAM;AAC5C,SAASC,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,yBAAyB,QAAQ,4CAA4C;;AAE3F;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAIf,cAAc,CAAC,cAAc,CAAC;AACxD;AACA;AACA;AACA,MAAMgB,YAAY,CAAC;EAAAC,YAAA;IACf;IAAAC,eAAA,wBACgB,IAAIT,OAAO,CAAC,CAAC;IAC7B;IAAAS,eAAA,+BACuB,IAAIT,OAAO,CAAC,CAAC;IACpC;IAAAS,eAAA,aACKjB,MAAM,CAACW,YAAY,CAAC,CAACO,KAAK,CAAC,gBAAgB,CAAC;IACjD;IAAAD,eAAA,gBACQ,KAAK;EAAA;EACb;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,IAAI,CAACC,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAAC;IACxC;EACJ;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,oBAAoB,CAACC,IAAI,CAAC,KAAK,CAAC;EACzC;EACAE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACC,aAAa,CAACJ,IAAI,CAACG,OAAO,CAAC;EACpC;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,aAAa,CAACE,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACP,oBAAoB,CAACO,QAAQ,CAAC,CAAC;EACxC;AAGJ;AAACC,aAAA,GA5BKd,YAAY;AAAAE,eAAA,CAAZF,YAAY,wBAAAe,sBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA0BqFhB,aAAY;AAAA;AAAAE,eAAA,CA1B7GF,YAAY,8BA6B+DjB,EAAE,CAAAkC,iBAAA;EAAAC,IAAA,EAFQlB,aAAY;EAAAmB,SAAA;EAAAC,MAAA;IAAAf,KAAA,wBAAqGnB,gBAAgB;EAAA;EAAAmC,QAAA;EAAAC,QAAA,GAE3IvC,EAAE,CAAAwC,kBAAA,CAFyJ,CAAC;IAAEC,OAAO,EAAEzB,aAAa;IAAE0B,WAAW,EAAEzB;EAAa,CAAC,CAAC,GAElNjB,EAAE,CAAA2C,oBAAA;AAAA;AAAnF;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF5C,EAAE,CAAA6C,iBAAA,CAAQ5B,YAAY,EAAc,CAAC;IAC1GkB,IAAI,EAAE/B,SAAS;IACf0C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+BAA+B;MACzCT,QAAQ,EAAE,cAAc;MACxBU,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAEzB,aAAa;QAAE0B,WAAW,EAAEzB;MAAa,CAAC;IACrE,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEK,KAAK,EAAE,CAAC;MACtBa,IAAI,EAAE9B,KAAK;MACXyC,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAE9C;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM+C,gBAAgB,CAAC;EAoBnB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACA,QAAQ,EAAE;IACnB;IACA,IAAI,IAAI,CAACC,SAAS,KAAKD,QAAQ,EAAE;MAC7B,IAAI,CAACC,SAAS,GAAGD,QAAQ;MACzB,IAAI,CAACE,cAAc,CAACC,IAAI,CAACH,QAAQ,CAAC;MAClC,IAAIA,QAAQ,EAAE;QACV,IAAI,CAACI,MAAM,CAACD,IAAI,CAAC,CAAC;QAClB;AAChB;AACA;AACA;QACgB,MAAME,WAAW,GAAG,IAAI,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACC,EAAE,GAAG,IAAI,CAACA,EAAE;QAChE,IAAI,CAACC,oBAAoB,CAACC,MAAM,CAAC,IAAI,CAACF,EAAE,EAAEF,WAAW,CAAC;MAC1D,CAAC,MACI;QACD,IAAI,CAACK,MAAM,CAACP,IAAI,CAAC,CAAC;MACtB;MACA;MACA;MACA,IAAI,CAACQ,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EAMA7C,WAAWA,CAAA,EAAG;IAAAC,eAAA,oBAlDFjB,MAAM,CAACc,aAAa,EAAE;MAAEgD,QAAQ,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA9C,eAAA,6BAChDjB,MAAM,CAACI,iBAAiB,CAAC;IAAAa,eAAA,+BACvBjB,MAAM,CAACa,yBAAyB,CAAC;IACxD;IAAAI,eAAA,oCAC4BR,YAAY,CAACuD,KAAK;IAC9C;IAAA/C,eAAA,iBACS,IAAIZ,YAAY,CAAC,CAAC;IAC3B;IAAAY,eAAA,iBACS,IAAIZ,YAAY,CAAC,CAAC;IAC3B;IAAAY,eAAA,oBACY,IAAIZ,YAAY,CAAC,CAAC;IAC9B;AACJ;AACA;AACA;AACA;IAJIY,eAAA,yBAKiB,IAAIZ,YAAY,CAAC,CAAC;IACnC;IAAAY,eAAA,aACKjB,MAAM,CAACW,YAAY,CAAC,CAACO,KAAK,CAAC,sBAAsB,CAAC;IAAAD,eAAA,oBA2B3C,KAAK;IACjB;IAAAA,eAAA,mBACW,KAAK;IAChB;IAAAA,eAAA,yCACiC,MAAM,CAAE,CAAC;EAC1B;EAChBgD,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,8BAA8B,GAAG,IAAI,CAACT,oBAAoB,CAACU,MAAM,CAAC,CAACX,EAAE,EAAEF,WAAW,KAAK;MACxF,IAAI,IAAI,CAACC,SAAS,IACd,CAAC,IAAI,CAACA,SAAS,CAACnC,KAAK,IACrB,IAAI,CAACmC,SAAS,CAACC,EAAE,KAAKF,WAAW,IACjC,IAAI,CAACE,EAAE,KAAKA,EAAE,EAAE;QAChB,IAAI,CAACP,QAAQ,GAAG,KAAK;MACzB;IACJ,CAAC,CAAC;IACF;IACA,IAAI,IAAI,CAACM,SAAS,EAAE;MAChB,IAAI,CAACa,yBAAyB,GAAG,IAAI,CAACC,+BAA+B,CAAC,CAAC;IAC3E;EACJ;EACA;EACA1C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0B,MAAM,CAACzB,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC+B,MAAM,CAAC/B,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC0C,SAAS,CAAClB,IAAI,CAAC,CAAC;IACrB,IAAI,CAACkB,SAAS,CAAC1C,QAAQ,CAAC,CAAC;IACzB,IAAI,CAACsC,8BAA8B,CAAC,CAAC;IACrC,IAAI,CAACE,yBAAyB,CAACG,WAAW,CAAC,CAAC;EAChD;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAChB,IAAI,CAACxB,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;IAClC;EACJ;EACA;EACAyB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;MAChB,IAAI,CAACxB,QAAQ,GAAG,KAAK;IACzB;EACJ;EACA;EACA0B,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACF,QAAQ,EAAE;MAChB,IAAI,CAACxB,QAAQ,GAAG,IAAI;IACxB;EACJ;EACAoB,+BAA+BA,CAAA,EAAG;IAC9B,OAAO,IAAI,CAACd,SAAS,CAAClC,oBAAoB,CAACuD,SAAS,CAAC3B,QAAQ,IAAI;MAC7D;MACA,IAAI,CAAC,IAAI,CAACwB,QAAQ,EAAE;QAChB,IAAI,CAACxB,QAAQ,GAAGA,QAAQ;MAC5B;IACJ,CAAC,CAAC;EACN;AAOJ;AAAC4B,iBAAA,GA3GK7B,gBAAgB;AAAA/B,eAAA,CAAhB+B,gBAAgB,wBAAA8B,0BAAA/C,iBAAA;EAAA,YAAAA,iBAAA,IAqGiFiB,iBAAgB;AAAA;AAAA/B,eAAA,CArGjH+B,gBAAgB,8BAhB2DlD,EAAE,CAAAkC,iBAAA;EAAAC,IAAA,EAsHQe,iBAAgB;EAAAd,SAAA;EAAAC,MAAA;IAAAc,QAAA,8BAAuHhD,gBAAgB;IAAAwE,QAAA,8BAAsCxE,gBAAgB;EAAA;EAAA8E,OAAA;IAAApB,MAAA;IAAAN,MAAA;IAAAiB,SAAA;IAAAnB,cAAA;EAAA;EAAAf,QAAA;EAAAC,QAAA,GAtHvNvC,EAAE,CAAAwC,kBAAA,CAsHgV;EACvZ;EACA;EACA;IAAEC,OAAO,EAAEzB,aAAa;IAAEkE,QAAQ,EAAEC;EAAU,CAAC,CAClD;AAAA;AAET;EAAA,QAAAvC,SAAA,oBAAAA,SAAA,KA5HiF5C,EAAE,CAAA6C,iBAAA,CA4HQK,gBAAgB,EAAc,CAAC;IAC9Gf,IAAI,EAAE/B,SAAS;IACf0C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wCAAwC;MAClDT,QAAQ,EAAE,kBAAkB;MAC5BU,SAAS,EAAE;MACP;MACA;MACA;QAAEP,OAAO,EAAEzB,aAAa;QAAEkE,QAAQ,EAAEC;MAAU,CAAC;IAEvD,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEtB,MAAM,EAAE,CAAC;MACjD1B,IAAI,EAAE3B;IACV,CAAC,CAAC;IAAE+C,MAAM,EAAE,CAAC;MACTpB,IAAI,EAAE3B;IACV,CAAC,CAAC;IAAEgE,SAAS,EAAE,CAAC;MACZrC,IAAI,EAAE3B;IACV,CAAC,CAAC;IAAE6C,cAAc,EAAE,CAAC;MACjBlB,IAAI,EAAE3B;IACV,CAAC,CAAC;IAAE2C,QAAQ,EAAE,CAAC;MACXhB,IAAI,EAAE9B,KAAK;MACXyC,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAE9C;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwE,QAAQ,EAAE,CAAC;MACXxC,IAAI,EAAE9B,KAAK;MACXyC,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAE9C;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiF,kBAAkB,CAAC;AAIxBC,mBAAA,GAJKD,kBAAkB;AAAAjE,eAAA,CAAlBiE,kBAAkB,wBAAAE,4BAAArD,iBAAA;EAAA,YAAAA,iBAAA,IAC+EmD,mBAAkB;AAAA;AAAAjE,eAAA,CADnHiE,kBAAkB,8BAvJyDpF,EAAE,CAAAuF,gBAAA;EAAApD,IAAA,EAyJqBiD,mBAAkB;EAAAI,OAAA,GAAYvE,YAAY,EAAEiC,gBAAgB;EAAAuC,OAAA,GAAaxE,YAAY,EAAEiC,gBAAgB;AAAA;AAAA/B,eAAA,CAFzMiE,kBAAkB,8BAvJyDpF,EAAE,CAAA0F,gBAAA;AA4JnF;EAAA,QAAA9C,SAAA,oBAAAA,SAAA,KA5JiF5C,EAAE,CAAA6C,iBAAA,CA4JQuC,kBAAkB,EAAc,CAAC;IAChHjD,IAAI,EAAE1B,QAAQ;IACdqC,IAAI,EAAE,CAAC;MACC0C,OAAO,EAAE,CAACvE,YAAY,EAAEiC,gBAAgB,CAAC;MACzCuC,OAAO,EAAE,CAACxE,YAAY,EAAEiC,gBAAgB;IAC5C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASlC,aAAa,EAAEC,YAAY,EAAEiC,gBAAgB,EAAEkC,kBAAkB;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}