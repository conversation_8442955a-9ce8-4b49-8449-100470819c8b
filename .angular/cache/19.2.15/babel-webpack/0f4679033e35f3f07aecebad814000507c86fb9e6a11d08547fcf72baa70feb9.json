{"ast": null, "code": "import { InjectionToken } from '@angular/core';\nexport const SWUI_HUB_MESSAGE_CONFIG = new InjectionToken('SWUI_HUB_MESSAGE_CONFIG');", "map": {"version": 3, "names": ["InjectionToken", "SWUI_HUB_MESSAGE_CONFIG"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-init/sw-hub-init.token.ts"], "sourcesContent": ["import { InjectionToken } from '@angular/core';\nexport const SWUI_HUB_MESSAGE_CONFIG = new InjectionToken('SWUI_HUB_MESSAGE_CONFIG');\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,eAAe;AAC9C,OAAO,MAAMC,uBAAuB,GAAG,IAAID,cAAc,CAAC,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}