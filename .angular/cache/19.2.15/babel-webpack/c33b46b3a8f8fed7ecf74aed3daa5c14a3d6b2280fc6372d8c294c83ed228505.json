{"ast": null, "code": "let shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n  if (shadowDomIsSupported == null) {\n    const head = typeof document !== 'undefined' ? document.head : null;\n    shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n  }\n  return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n  if (_supportsShadowDom()) {\n    const rootNode = element.getRootNode ? element.getRootNode() : null;\n    // Note that this should be caught by `_supportsShadowDom`, but some\n    // teams have been able to hit this code path on unsupported browsers.\n    if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n      return rootNode;\n    }\n  }\n  return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n  let activeElement = typeof document !== 'undefined' && document ? document.activeElement : null;\n  while (activeElement && activeElement.shadowRoot) {\n    const newActiveElement = activeElement.shadowRoot.activeElement;\n    if (newActiveElement === activeElement) {\n      break;\n    } else {\n      activeElement = newActiveElement;\n    }\n  }\n  return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n  // If an event is bound outside the Shadow DOM, the `event.target` will\n  // point to the shadow root so we have to use `composedPath` instead.\n  return event.composedPath ? event.composedPath()[0] : event.target;\n}\nexport { _getEventTarget as _, _getShadowRoot as a, _supportsShadowDom as b, _getFocusedElementPierceShadowDom as c };\n//# sourceMappingURL=shadow-dom-B0oHn41l.mjs.map", "map": {"version": 3, "names": ["shadowDomIsSupported", "_supportsShadowDom", "head", "document", "createShadowRoot", "attachShadow", "_getShadowRoot", "element", "rootNode", "getRootNode", "ShadowRoot", "_getFocusedElementPierceShadowDom", "activeElement", "shadowRoot", "newActiveElement", "_getEventTarget", "event", "<PERSON><PERSON><PERSON>", "target", "_", "a", "b", "c"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/shadow-dom-B0oHn41l.mjs"], "sourcesContent": ["let shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n    if (shadowDomIsSupported == null) {\n        const head = typeof document !== 'undefined' ? document.head : null;\n        shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n    }\n    return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n    if (_supportsShadowDom()) {\n        const rootNode = element.getRootNode ? element.getRootNode() : null;\n        // Note that this should be caught by `_supportsShadowDom`, but some\n        // teams have been able to hit this code path on unsupported browsers.\n        if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n            return rootNode;\n        }\n    }\n    return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n    let activeElement = typeof document !== 'undefined' && document\n        ? document.activeElement\n        : null;\n    while (activeElement && activeElement.shadowRoot) {\n        const newActiveElement = activeElement.shadowRoot.activeElement;\n        if (newActiveElement === activeElement) {\n            break;\n        }\n        else {\n            activeElement = newActiveElement;\n        }\n    }\n    return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n    // If an event is bound outside the Shadow DOM, the `event.target` will\n    // point to the shadow root so we have to use `composedPath` instead.\n    return (event.composedPath ? event.composedPath()[0] : event.target);\n}\n\nexport { _getEventTarget as _, _getShadowRoot as a, _supportsShadowDom as b, _getFocusedElementPierceShadowDom as c };\n//# sourceMappingURL=shadow-dom-B0oHn41l.mjs.map\n"], "mappings": "AAAA,IAAIA,oBAAoB;AACxB;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,IAAID,oBAAoB,IAAI,IAAI,EAAE;IAC9B,MAAME,IAAI,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACD,IAAI,GAAG,IAAI;IACnEF,oBAAoB,GAAG,CAAC,EAAEE,IAAI,KAAKA,IAAI,CAACE,gBAAgB,IAAIF,IAAI,CAACG,YAAY,CAAC,CAAC;EACnF;EACA,OAAOL,oBAAoB;AAC/B;AACA;AACA,SAASM,cAAcA,CAACC,OAAO,EAAE;EAC7B,IAAIN,kBAAkB,CAAC,CAAC,EAAE;IACtB,MAAMO,QAAQ,GAAGD,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACE,WAAW,CAAC,CAAC,GAAG,IAAI;IACnE;IACA;IACA,IAAI,OAAOC,UAAU,KAAK,WAAW,IAAIA,UAAU,IAAIF,QAAQ,YAAYE,UAAU,EAAE;MACnF,OAAOF,QAAQ;IACnB;EACJ;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA,SAASG,iCAAiCA,CAAA,EAAG;EACzC,IAAIC,aAAa,GAAG,OAAOT,QAAQ,KAAK,WAAW,IAAIA,QAAQ,GACzDA,QAAQ,CAACS,aAAa,GACtB,IAAI;EACV,OAAOA,aAAa,IAAIA,aAAa,CAACC,UAAU,EAAE;IAC9C,MAAMC,gBAAgB,GAAGF,aAAa,CAACC,UAAU,CAACD,aAAa;IAC/D,IAAIE,gBAAgB,KAAKF,aAAa,EAAE;MACpC;IACJ,CAAC,MACI;MACDA,aAAa,GAAGE,gBAAgB;IACpC;EACJ;EACA,OAAOF,aAAa;AACxB;AACA;AACA,SAASG,eAAeA,CAACC,KAAK,EAAE;EAC5B;EACA;EACA,OAAQA,KAAK,CAACC,YAAY,GAAGD,KAAK,CAACC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,KAAK,CAACE,MAAM;AACvE;AAEA,SAASH,eAAe,IAAII,CAAC,EAAEb,cAAc,IAAIc,CAAC,EAAEnB,kBAAkB,IAAIoB,CAAC,EAAEV,iCAAiC,IAAIW,CAAC;AACnH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}