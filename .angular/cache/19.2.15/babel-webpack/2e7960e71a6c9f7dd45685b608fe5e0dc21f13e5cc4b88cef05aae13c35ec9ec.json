{"ast": null, "code": "export const PERMISSIONS_NAMES = Object.freeze({\n  KEYENTITY_REPORT: 'keyentity:report',\n  KEYENTITY_REPORT_CURRENCY: 'keyentity:report:currency',\n  KEYENTITY_REPORT_WALLET_CURRENCY: 'keyentity:report:wallet-currency',\n  K<PERSON><PERSON>ENTITY_REPORT_PLAYERS: 'keyentity:report:players',\n  KEYENTITY_REPORT_PROMO: 'keyentity:report:promo',\n  REPORT: 'report',\n  REPORT_CURRENCY: 'report:currency',\n  KEYENTITY_CASHIER: 'keyentity:cashier',\n  REPORT_WALLET_CURRENCY: 'report:wallet-currency',\n  REPORT_PLAYERS: 'report:players',\n  REPORT_WITHOUT_LIMIT: 'report-without-limit',\n  REPORT_PROMO: 'report:promo',\n  PLAYER: 'player',\n  PLAYER_RESET_CHANGE_NICKNAME: 'player:reset-change-nickname-attempts',\n  ENTITY_PLAYER: 'entity:player',\n  <PERSON><PERSON><PERSON>ENTITY_PLAYER: 'keyentity:player',\n  <PERSON><PERSON><PERSON>EN<PERSON>TY_PLAYER_DEPOSIT: 'keyentity:player:deposit',\n  KEYENTITY_PLAYER_WITHDRAWAL: 'keyentity:player:withdrawal',\n  PLAYER_PROMOTION: 'player:promotion',\n  KEYENTITY_PLAYER_PROMOTION: 'keyentity:player:promotion',\n  PLAYER_VIEW: 'player:view',\n  ENTITY_PLAYER_VIEW: 'entity:player:view',\n  KEYENTITY_PLAYER_VIEW: 'keyentity:player:view',\n  KEYENTITY_PLAYER_CREATE: 'keyentity:player:create',\n  USER: 'user',\n  USER_EDIT: 'user:edit',\n  USER_CREATE: 'user:create',\n  USER_CHANGE_TYPE: 'user:change-type',\n  ENTITY_USER: 'entity:user',\n  KEYENTITY_USER: 'keyentity:user',\n  KEYENTITY_USER_CREATE: 'keyentity:user:create',\n  KEYENTITY_USER_CHANGE_TYPE: 'keyentity:user:change-type',\n  KEYENTITY_USER_EDIT: 'keyentity:user:edit',\n  USER_VIEW: 'user:view',\n  ENTITY_USER_VIEW: 'entity:user:view',\n  KEYENTITY_USER_VIEW: 'keyentity:user:view',\n  LOBBY: 'lobby',\n  LOBBY_CREATE: 'lobby:create',\n  LOBBY_VIEW: 'lobby:view',\n  LOBBY_EDIT: 'lobby:edit',\n  LOBBY_DELETE: 'lobby:delete',\n  KEYENTITY_LOBBY: 'keyentity:lobby',\n  KEYENTITY_LOBBY_CREATE: 'keyentity:lobby:create',\n  KEYENTITY_LOBBY_VIEW: 'keyentity:lobby:view',\n  KEYENTITY_LOBBY_EDIT: 'keyentity:lobby:edit',\n  KEYENTITY_LOBBY_DELETE: 'keyentity:lobby:delete',\n  KEYENTITY_MERCHANT_VIEW: 'keyentity:merchant:view',\n  KEYENTITY_INTEGRATION_VIEW: 'keyentity:integration:view',\n  TERMINAL: 'keyentity:terminal',\n  TERMINAL_VIEW: 'keyentity:terminal:view',\n  ENTITY_GAME: 'entity:game',\n  ENTITY_GAME_URL: 'entity:game:url',\n  ENTITY_INFO: 'entity:info',\n  ENTITY_GAME_UNFINISHED: 'entity:game:unfinished',\n  ENTITY_GAME_HISTORY: 'entity:game:history',\n  ENTITY_GAME_ADD_GAME_CASCADE: 'entity:game:add-game-cascade',\n  ENTITY_GAME_CHANGE_STATE: 'entity:game:change-state',\n  ENTITY_GAME_CHANGE_STATE_DISABLED: 'entity:game:change-state:disabled',\n  ENTITY_GAME_CHANGE_STATE_ENABLED: 'entity:game:change-state:enabled',\n  KEYENTITY_GOS_GAME_HISTORY: 'keyentity:gos:game:history',\n  ENTITY_GOS_GAME_HISTORY: 'entity:gos:game:history',\n  KEYENTITY_EXTERNAL_GAME_PROVIDER_HISTORY: 'keyentity:external-game-provider:history',\n  KEYENTITY_EXTERNAL_GAME_PROVIDER_GAMECLOSE_FORCEFINISH: 'keyentity:external-game-provider:gameclose:forcefinish',\n  ENTITY_EXTERNAL_GAME_PROVIDER_GAMECLOSE_FORCEFINISH: 'entity:external-game-provider:gameclose:forcefinish',\n  ENTITY_EXTERNAL_GAME_PROVIDER_HISTORY: 'entity:external-game-provider:history',\n  KEYENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY: 'keyentity:external-game-provider:availability',\n  ENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY: 'entity:external-game-provider:availability',\n  DISABLE_ENTITY_GAME_HISTORY_BALANCES: 'disable:entity:game-history:balances',\n  DISABLE_KEYENTITY_GAME_HISTORY_BALANCES: 'disable:keyentity:game-history:balances',\n  KEYENTITY_GAME: 'keyentity:game',\n  KEYENTITY_GAME_HISTORY: 'keyentity:game:history',\n  KEYENTITY_GAME_CHANGE_STATE: 'keyentity:game:change-state',\n  KEYENTITY_GAME_CHANGE_STATE_ENABLED: 'keyentity:game:change-state:enabled',\n  KEYENTITY_GAME_CHANGE_STATE_DISABLED: 'keyentity:game:change-state:disabled',\n  KEYENTITY_GAMECATEGORY: 'keyentity:gamecategory',\n  KEYENTITY_GAMECATEGORY_VIEW: 'keyentity:gamecategory:view',\n  KEYENTITY_GAME_UNFINISHED: 'keyentity:game:unfinished',\n  KEYENTITY_GAMEPROVIDER_GAME: 'keyentity:gameprovider:game',\n  KEYENTITY_GAMEPROVIDER_GAME_CREATE: 'keyentity:gameprovider:game:create',\n  KEYENTITY_GAMEPROVIDER_GAME_EDIT: 'keyentity:gameprovider:game:edit',\n  KEYENTITY_MASTER_GAMECATEGORY_VIEW: 'keyentity:master:gamecategory:view',\n  KEYENTITY_MASTER_GAMECATEGORY: 'keyentity:master:gamecategory',\n  GRANTED_ALL: 'granted:all',\n  GRANTED_MOCK: 'granted:mock',\n  DENIED_ALL: 'denied:all',\n  ENTITY: 'entity',\n  ENTITY_VIEW: 'entity:view',\n  ENTITY_CREATE: 'entity:create',\n  ENTITY_EDIT: 'entity:edit',\n  ENTITY_BALANCE: 'entity:balance',\n  ENTITY_CHANGESTATE: 'entity:change-state',\n  ENTITY_CHANGESTATE_TEST: 'entity:change-state-test',\n  ENTITY_GAMECLOSE_FORCEFINISH: 'entity:gameclose:forcefinish',\n  KEYENTITY_GAMECLOSE_FORCEFINISH: 'keyentity:gameclose:forcefinish',\n  ENTITY_GAMECLOSE_REVERT: 'entity:gameclose:revert',\n  KEYENTITY_GAMECLOSE_REVERT: 'keyentity:gameclose:revert',\n  ENTITY_GAMECLOSE_RETRY: 'entity:gameclose:retry',\n  KEYENTITY_GAMECLOSE_RETRY: 'keyentity:gameclose:retry',\n  ENTITY_GAMECLOSE_TRANSFER_OUT: 'entity:gameclose:transfer-out',\n  KEYENTITY_GAMECLOSE_TRANSFER_OUT: 'keyentity:gameclose:transfer-out',\n  AGENT: 'agent',\n  AGENT_VIEW: 'agent:view',\n  KEYENTITY_AGENT: 'keyentity:agent',\n  KEYENTITY_AGENT_VIEW: 'keyentity:agent:view',\n  AUDIT: 'audit',\n  KEYENTITY_AUDIT: 'keyentity:audit',\n  PAYMENT: 'payment',\n  PAYMENT_VIEW: 'payment:view',\n  PROMOTION: 'promotion',\n  PROMOTION_VIEW: 'promotion:view',\n  PROMOTION_CREATE: 'promotion:create',\n  PROMOTION_EDIT: 'promotion:edit',\n  PROMOTION_DELETE: 'promotion:delete',\n  PROMOTION_BONUSCOIN: 'promotion:bonuscoin',\n  PROMOTION_REBATE: 'promotion:rebate',\n  KEYENTITY_PROMOTION: 'keyentity:promotion',\n  KEYENTITY_PROMOTION_VIEW: 'keyentity:promotion:view',\n  KEYENTITY_PROMOTION_CREATE: 'keyentity:promotion:create',\n  KEYENTITY_PROMOTION_EDIT: 'keyentity:promotion:edit',\n  KEYENTITY_PROMOTION_DELETE: 'keyentity:promotion:delete',\n  KEYENTITY_PROMOTION_BONUSCOIN: 'keyentity:promotion:bonuscoin',\n  KEYENTITY_PROMOTION_BONUSCOIN_VIEW: 'keyentity:promotion:bonuscoin:view',\n  KEYENTITY_PROMOTION_BONUSCOIN_CREATE: 'keyentity:promotion:bonuscoin:create',\n  KEYENTITY_PROMOTION_BONUSCOIN_EDIT: 'keyentity:promotion:bonuscoin:edit',\n  KEYENTITY_PROMOTION_BONUSCOIN_DELETE: 'keyentity:promotion:bonuscoin:delete',\n  KEYENTITY_PROMOTION_REBATE: 'keyentity:promotion:rebate',\n  KEYENTITY_PROMOTION_REBATE_VIEW: 'keyentity:promotion:rebate:view',\n  KEYENTITY_PROMOTION_REBATE_CREATE: 'keyentity:promotion:rebate:create',\n  KEYENTITY_PROMOTION_REBATE_EDIT: 'keyentity:promotion:rebate:edit',\n  KEYENTITY_PROMOTION_REBATE_DELETE: 'keyentity:promotion:rebate:delete',\n  KEYENTITY_PROMOTION_VIRTUALMONEY: 'keyentity:promotion:virtualmoney',\n  KEYENTITY_PROMOTION_VIRTUALMONEY_VIEW: 'keyentity:promotion:virtualmoney:view',\n  KEYENTITY_PROMOTION_VIRTUALMONEY_CREATE: 'keyentity:promotion:virtualmoney:create',\n  KEYENTITY_PROMOTION_VIRTUALMONEY_EDIT: 'keyentity:promotion:virtualmoney:edit',\n  KEYENTITY_PROMOTION_VIRTUALMONEY_DELETE: 'keyentity:promotion:virtualmoney:delete',\n  KEYENTITY_PROMOTION_FREEBET: 'keyentity:promotion:freebet',\n  KEYENTITY_PROMOTION_FREEBET_VIEW: 'keyentity:promotion:freebet:view',\n  KEYENTITY_PROMOTION_FREEBET_CREATE: 'keyentity:promotion:freebet:create',\n  KEYENTITY_PROMOTION_FREEBET_EDIT: 'keyentity:promotion:freebet:edit',\n  KEYENTITY_PROMOTION_FREEBET_DELETE: 'keyentity:promotion:freebet:delete',\n  KEYENTITY_PROMOTION_SKYWIND: 'keyentity:promotion:skywind',\n  // Manage skywind promotions\n  KEYENTITY_PROMOTION_SKYWIND_CREATE: 'keyentity:promotion:skywind:create',\n  // Create skywind promotions\n  KEYENTITY_PROMOTION_SKYWIND_EDIT: 'keyentity:promotion:skywind:edit',\n  // Update skywind promotions\n  KEYENTITY_PROMOTION_OWNER: 'keyentity:promotion:owner',\n  // Update owner of promotion\n  KEYENTITY_PAYMENT: 'keyentity:payment',\n  KEYENTITY_PAYMENT_VIEW: 'keyentity:payment:view',\n  ROLE: 'role',\n  ROLE_CREATE: 'role:create',\n  ROLE_EDIT: 'role:edit',\n  ROLE_VIEW: 'role:view',\n  ROLE_DELETE: 'role:delete',\n  DOMAIN: 'domain',\n  FINANCE: 'finance',\n  FINANCE_VIEW: 'finance:view',\n  FINANCE_CREDIT: 'finance:credit',\n  FINANCE_DEBIT: 'finance:debit',\n  COUNTRY_ADD: 'country:add',\n  BI_REPORTS_VIEW: 'bi:reports:view',\n  KEYENTITY_BI_REPORTS_VIEW: 'keyentity:bi:reports:view',\n  FORCE_RESET_PASSWORD: 'user-extra:force-reset-password',\n  KEYENTITY_FORCE_RESET_PASSWORD: 'keyentity:user-extra:force-reset-password',\n  FORCE_SET_EMAIL: 'user-extra:email:force-set',\n  KEYENTITY_FORCE_SET_EMAIL: 'keyentity:user-extra:email:force-set',\n  USER_DELETE: 'user-extra:delete',\n  KEYENTITY_USER_DELETE: 'keyentity:user-extra:delete',\n  KEYENTITY_RESPONSIBLEGAMING_PLAYER: 'keyentity:responsiblegaming:player',\n  KEYENTITY_RESPONSIBLEGAMING_PLAYER_VIEW: 'keyentity:responsiblegaming:player:view',\n  KEYENTITY_RESPONSIBLEGAMING_PLAYER_EDIT: 'keyentity:responsiblegaming:player:edit',\n  RESPONSIBLEGAMING_PLAYER: 'responsiblegaming:player',\n  RESPONSIBLEGAMING_PLAYER_VIEW: 'responsiblegaming:player:view',\n  RESPONSIBLEGAMING_PLAYER_EDIT: 'responsiblegaming:player:edit',\n  USER_UNBLOCK_CHANGE_PASSWORD: 'user-extra:change-password-unlock',\n  KEYENTITY_USER_UNBLOCK_CHANGE_PASSWORD: 'keyentity:user-extra:change-password-unlock',\n  USER_UNBLOCK_LOGIN: 'user-extra:login-unlock',\n  KEYENTITY_USER_UNBLOCK_LOGIN: 'keyentity:user-extra:login-unlock',\n  KEYENTITY_JURISDICTION: 'keyentity:jurisdiction',\n  KEYENTITY_JURISDICTION_VIEW: 'keyentity:jurisdiction:view',\n  KEYENTITY_ENTITYDOMAIN: 'keyentity:entitydomain',\n  KEYENTITY_ENTITYDOMAIN_DYNAMIC: 'keyentity:entitydomain:dynamic',\n  KEYENTITY_ENTITYDOMAIN_DYNAMIC_VIEW: 'keyentity:entitydomain:dynamic:view',\n  ENTITY_ENTITYDOMAIN_DYNAMIC: 'entity:entitydomain:dynamic',\n  ENTITY_ENTITYDOMAIN_DYNAMIC_VIEW: 'entity:entitydomain:dynamic:view',\n  KEYENTITY_ENTITYDOMAIN_STATIC: 'keyentity:entitydomain:static',\n  KEYENTITY_ENTITYDOMAIN_STATIC_VIEW: 'keyentity:entitydomain:static:view',\n  ENTITY_ENTITYDOMAIN_STATIC: 'entity:entitydomain:static',\n  ENTITY_ENTITYDOMAIN_STATIC_VIEW: 'entity:entitydomain:static:view',\n  KEYENTITY_ENTITYDOMAIN_BULKOPERATION: 'keyentity:entitydomain:bulk-operation',\n  KEYENTITY_BULKOPERATION: 'keyentity:bulk-operation',\n  KEYENTITY_BI_REPORT_DOMAINS: 'keyentity:bi-reports-domains',\n  KEYENTITY_BI_REPORT_DOMAINS_VIEW: 'keyentity:bi-reports-domains:view',\n  KEYENTITY_BI_REPORT_DOMAINS_EDIT: 'keyentity:bi-reports-domains:edit',\n  KEYENTITY_BI_REPORT_DOMAINS_SELECT: 'keyentity:bi-reports-domains:select',\n  KEYENTITY_ROLE_CREATE: 'keyentity:role:create',\n  KEYENTITY_ROLE_EDIT: 'keyentity:role:edit',\n  KEYENTITY_ROLE_VIEW: 'keyentity:role:view',\n  KEYENTITY_ROLE_DELETE: 'keyentity:role:delete',\n  JURISDICTION: 'jurisdiction',\n  JURISDICTION_VIEW: 'jurisdiction:view',\n  SRT: 'srt',\n  SRT_CHALLENGE: 'srt:challenge',\n  SRT_TOURNAMENT: 'srt:tournament',\n  GAME_SERVER: 'gs:settings',\n  GAME_SERVER_VIEW: 'gs:settings:view',\n  GAME_SERVER_CREATE: 'gs:settings:create',\n  GAME_SERVER_EDIT: 'gs:settings:edit',\n  GAME_SERVER_REMOVE: 'gs:settings:remove',\n  GAME_GROUP_CREATE: 'gamegroup:create',\n  KEYENTITY_GAME_GROUP_CREATE: 'keyentity:gamegroup:create',\n  GAME_GROUP: 'gamegroup',\n  GAME_GROUP_VIEW: 'gamegroup:view',\n  KEYENTITY_GAME_GROUP: 'keyentity:gamegroup',\n  KEYENTITY_GAME_GROUP_VIEW: 'keyentity:gamegroup:view',\n  GAME_GROUP_EDIT: 'gamegroup:edit',\n  KEYENTITY_GAME_GROUP_EDIT: 'keyentity:gamegroup:edit',\n  GAME_GROUP_DELETE: 'gamegroup:delete',\n  KEYENTITY_GAME_GROUP_DELETE: 'keyentity:gamegroup:delete',\n  HUB_CASINO: 'hub:casino',\n  HUB_ANALYTICS: 'hub:analytics',\n  HUB_ENGAGEMENT: 'hub:engagement',\n  HUB_ENGAGEMENT_TOURNAMENTS: 'hub:engagement:tournaments',\n  HUB_ENGAGEMENT_PRIZE_DROPS: 'hub:engagement:prize-drops',\n  HUB_ENGAGEMENT_MUST_WIN_JACKPOTS: 'hub:engagement:must-win-jackpots',\n  HUB_STUDIO: 'hub:studio',\n  KEYENTITY_BI_REPORT_PLAYER_SHOW_HIDE_COLUMN_DEBITS_CREDITS: 'keyentity:bi:report:player-show-hide-column:debits-credits',\n  BI_REPORT_PLAYER_SHOW_HIDE_COLUMN_DEBITS_CREDITS: 'bi:report:player-show-hide-column:debits-credits',\n  GAME_LIMITS: 'gamelimits',\n  KEYENTITY_GAME_LIMITS: 'keyentity:gamelimits',\n  ENTITY_GAMECLOSE_FINALIZE: 'entity:gameclose:finalize',\n  KEYENTITY_GAMECLOSE_FINALIZE: 'keyentity:gameclose:finalize',\n  KEYENTITY_USER_CHANGE_PASSWORD: 'keyentity:user:change-password',\n  GAMERTP: 'gamertp',\n  GAMERTP_VIEW: 'gamertp:view',\n  KEYENTITY_GAMERTP: 'keyentity:gamertp',\n  KEYENTITY_GAMERTP_VIEW: 'keyentity:gamertp:view',\n  KEYENTITY_GAMELABEL: 'keyentity:gamelabel',\n  KEYENTITY_GAMELABEL_VIEW: 'keyentity:gamelabel:view',\n  KEYENTITY_ENTITYLABELS: 'keyentity:entitylabels',\n  KEYENTITY_ENTITYLABELS_VIEW: 'keyentity:entitylabels:view',\n  KEYENTITY_ENTITYLABELS_CREATE: 'keyentity:entitylabels:create',\n  ENTITYLABELS: 'entitylabels',\n  ENTITYLABELS_VIEW: 'entitylabels:view',\n  ENTITYLABELS_CREATE: 'entitylabels:create',\n  JACKPOT: 'jackpot',\n  JACKPOT_INSTANCE: 'jackpot:instance',\n  JACKPOT_INSTANCE_VIEW: 'jackpot:instance:view',\n  ENTITY_LIVEGAME: 'entity:live-game',\n  ENTITY_LIVEGAME_ADD: 'entity:live-game:add-live-game',\n  ENTITY_LIVEGAME_REMOVE: 'entity:live-game:remove-live-game',\n  ENTITY_LIVEGAME_CHANGE_STATE: 'entity:live-game:change-state',\n  ENTITY_LIVEGAME_CHANGE_STATE_ENABLED: 'entity:live-game:change-state:enabled',\n  ENTITY_LIVEGAME_CHANGE_STATE_DISABLED: 'entity:live-game:change-state:disabled',\n  KEYENTITY_LIVEGAME: 'keyentity:live-game',\n  KEYENTITY_LIVEGAME_CHANGE_STATE: 'keyentity:live-game:change-state',\n  KEYENTITY_LIVEGAME_CHANGE_STATE_ENABLED: 'keyentity:live-game:change-state:enabled',\n  KEYENTITY_LIVEGAME_CHANGE_STATE_DISABLED: 'keyentity:live-game:change-state:disabled',\n  FLAT_REPORTS: 'flat-reports',\n  FLAT_REPORTS_VIEW: 'flat-reports:view',\n  KEYENTITY_FLAT_REPORTS: 'keyentity:flat-reports',\n  KEYENTITY_FLAT_REPORTS_VIEW: 'keyentity:flat-reports:view',\n  ID_DECODE: 'id:decode',\n  ID_ENCODE: 'id:encode',\n  RESTRICTED_COUNTRIES_SOLUTION: 'restricted-countries-solution',\n  JP_CONFIG_REPORT: 'jp-config-report',\n  DEPLOYMENT: 'deployment',\n  COUNTRY_REMOVE: 'country:remove'\n});\nexport const PERMISSIONS_LIST = Object.freeze({\n  GAME_STORE: [PERMISSIONS_NAMES.GRANTED_MOCK],\n  REPORT: [PERMISSIONS_NAMES.KEYENTITY_REPORT, PERMISSIONS_NAMES.KEYENTITY_REPORT_CURRENCY, PERMISSIONS_NAMES.KEYENTITY_REPORT_WALLET_CURRENCY, PERMISSIONS_NAMES.KEYENTITY_REPORT_PLAYERS, PERMISSIONS_NAMES.REPORT, PERMISSIONS_NAMES.REPORT_CURRENCY, PERMISSIONS_NAMES.REPORT_WALLET_CURRENCY, PERMISSIONS_NAMES.REPORT_PLAYERS],\n  PLAYER: [PERMISSIONS_NAMES.PLAYER, PERMISSIONS_NAMES.ENTITY_PLAYER, PERMISSIONS_NAMES.KEYENTITY_PLAYER],\n  PLAYER_VIEW: [PERMISSIONS_NAMES.PLAYER_VIEW, PERMISSIONS_NAMES.ENTITY_PLAYER_VIEW, PERMISSIONS_NAMES.KEYENTITY_PLAYER_VIEW],\n  USER: [PERMISSIONS_NAMES.USER, PERMISSIONS_NAMES.ENTITY_USER, PERMISSIONS_NAMES.KEYENTITY_USER],\n  USER_VIEW: [PERMISSIONS_NAMES.USER_VIEW, PERMISSIONS_NAMES.ENTITY_USER_VIEW, PERMISSIONS_NAMES.KEYENTITY_USER_VIEW],\n  LOBBY: [PERMISSIONS_NAMES.LOBBY, PERMISSIONS_NAMES.LOBBY_VIEW, PERMISSIONS_NAMES.KEYENTITY_LOBBY, PERMISSIONS_NAMES.KEYENTITY_LOBBY_VIEW],\n  TERMINAL: [PERMISSIONS_NAMES.TERMINAL, PERMISSIONS_NAMES.TERMINAL_VIEW],\n  REPORT_CURRENCY: [PERMISSIONS_NAMES.REPORT_CURRENCY, PERMISSIONS_NAMES.REPORT_WALLET_CURRENCY, PERMISSIONS_NAMES.KEYENTITY_REPORT_CURRENCY, PERMISSIONS_NAMES.KEYENTITY_REPORT_WALLET_CURRENCY],\n  REPORT_PLAYERS: [PERMISSIONS_NAMES.REPORT_PLAYERS, PERMISSIONS_NAMES.KEYENTITY_REPORT_PLAYERS],\n  REPORT_FINANCE: [PERMISSIONS_NAMES.FINANCE_VIEW],\n  GAME: [PERMISSIONS_NAMES.ENTITY_GAME, PERMISSIONS_NAMES.KEYENTITY_GAME],\n  GAME_CREATE: [PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME, PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME_CREATE],\n  GAME_EDIT: [PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME, PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME_EDIT],\n  GAMES_CASCADE_ADD: [PERMISSIONS_NAMES.ENTITY_GAME, PERMISSIONS_NAMES.ENTITY_GAME_ADD_GAME_CASCADE],\n  GAME_HISTORY: [PERMISSIONS_NAMES.ENTITY_GAME_HISTORY, PERMISSIONS_NAMES.KEYENTITY_GAME_HISTORY],\n  GOS_GAME_HISTORY: [PERMISSIONS_NAMES.ENTITY_GOS_GAME_HISTORY, PERMISSIONS_NAMES.KEYENTITY_GOS_GAME_HISTORY],\n  EXTERNAL_GAME_PROVIDER_HISTORY: [PERMISSIONS_NAMES.ENTITY_EXTERNAL_GAME_PROVIDER_HISTORY, PERMISSIONS_NAMES.KEYENTITY_EXTERNAL_GAME_PROVIDER_HISTORY],\n  EXTERNAL_GAME_PROVIDER_AVAILABILITY: [PERMISSIONS_NAMES.ENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY, PERMISSIONS_NAMES.KEYENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY],\n  EXTERNAL_GAME_PROVIDER_AVAILABILITY_ENTITY: [PERMISSIONS_NAMES.ENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY],\n  EXTERNAL_GAME_PROVIDER_AVAILABILITY_KEYENTITY: [PERMISSIONS_NAMES.KEYENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY],\n  GAMECATEGORY: [PERMISSIONS_NAMES.KEYENTITY_GAMECATEGORY, PERMISSIONS_NAMES.KEYENTITY_GAMECATEGORY_VIEW],\n  GAMELABEL: [PERMISSIONS_NAMES.KEYENTITY_GAMELABEL, PERMISSIONS_NAMES.KEYENTITY_GAMELABEL_VIEW],\n  ENTITY: [PERMISSIONS_NAMES.ENTITY, PERMISSIONS_NAMES.ENTITY_VIEW],\n  AGENT: [PERMISSIONS_NAMES.AGENT, PERMISSIONS_NAMES.AGENT_VIEW, PERMISSIONS_NAMES.KEYENTITY_AGENT, PERMISSIONS_NAMES.KEYENTITY_AGENT_VIEW],\n  AUDIT: [PERMISSIONS_NAMES.AUDIT, PERMISSIONS_NAMES.KEYENTITY_AUDIT],\n  PAYMENT: [PERMISSIONS_NAMES.PAYMENT, PERMISSIONS_NAMES.PAYMENT_VIEW, PERMISSIONS_NAMES.KEYENTITY_PAYMENT, PERMISSIONS_NAMES.KEYENTITY_PAYMENT_VIEW],\n  PROMOTION: [PERMISSIONS_NAMES.PROMOTION, PERMISSIONS_NAMES.KEYENTITY_PROMOTION],\n  PROMOTION_VIEW: [PERMISSIONS_NAMES.PROMOTION_VIEW, PERMISSIONS_NAMES.KEYENTITY_PROMOTION_VIEW],\n  PROMOTION_CREATE: [PERMISSIONS_NAMES.PROMOTION_CREATE, PERMISSIONS_NAMES.KEYENTITY_PROMOTION_CREATE],\n  PROMOTION_EDIT: [PERMISSIONS_NAMES.PROMOTION_EDIT, PERMISSIONS_NAMES.KEYENTITY_PROMOTION_EDIT],\n  PROMOTION_BONUSCOIN: [PERMISSIONS_NAMES.PROMOTION_BONUSCOIN, PERMISSIONS_NAMES.KEYENTITY_PROMOTION_BONUSCOIN],\n  PROMOTION_REBATE: [PERMISSIONS_NAMES.PROMOTION_REBATE, PERMISSIONS_NAMES.KEYENTITY_PROMOTION_REBATE],\n  PLAYER_PROMOTION: [PERMISSIONS_NAMES.KEYENTITY_PLAYER_PROMOTION, PERMISSIONS_NAMES.PLAYER_PROMOTION],\n  ROLE: [PERMISSIONS_NAMES.KEYENTITY_ROLE_VIEW, PERMISSIONS_NAMES.KEYENTITY_ROLE_EDIT, PERMISSIONS_NAMES.KEYENTITY_ROLE_CREATE, PERMISSIONS_NAMES.KEYENTITY_ROLE_DELETE],\n  DOMAIN: [PERMISSIONS_NAMES.DOMAIN],\n  DENIED_ALL: [PERMISSIONS_NAMES.DENIED_ALL],\n  GRANTED_ALL: [PERMISSIONS_NAMES.GRANTED_ALL],\n  GRANTED_MOCK: [PERMISSIONS_NAMES.GRANTED_MOCK],\n  TABLEAU_VIEW: [PERMISSIONS_NAMES.BI_REPORTS_VIEW, PERMISSIONS_NAMES.KEYENTITY_BI_REPORTS_VIEW],\n  RESPONSIBLEGAMING: [PERMISSIONS_NAMES.KEYENTITY_RESPONSIBLEGAMING_PLAYER, PERMISSIONS_NAMES.KEYENTITY_RESPONSIBLEGAMING_PLAYER_VIEW, PERMISSIONS_NAMES.KEYENTITY_RESPONSIBLEGAMING_PLAYER_EDIT, PERMISSIONS_NAMES.RESPONSIBLEGAMING_PLAYER, PERMISSIONS_NAMES.RESPONSIBLEGAMING_PLAYER_VIEW, PERMISSIONS_NAMES.RESPONSIBLEGAMING_PLAYER_EDIT],\n  SRT: [PERMISSIONS_NAMES.SRT_CHALLENGE, PERMISSIONS_NAMES.SRT_TOURNAMENT],\n  GAME_SERVER: [PERMISSIONS_NAMES.GAME_SERVER_VIEW, PERMISSIONS_NAMES.GAME_SERVER_CREATE, PERMISSIONS_NAMES.GAME_SERVER_EDIT, PERMISSIONS_NAMES.GAME_SERVER_REMOVE],\n  ENTITY_SETTINGS: [PERMISSIONS_NAMES.ENTITY, PERMISSIONS_NAMES.ENTITY_EDIT],\n  GAME_GROUP_VIEW: [PERMISSIONS_NAMES.GAME_GROUP, PERMISSIONS_NAMES.GAME_GROUP_VIEW, PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP, PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_VIEW],\n  GAME_GROUP_CREATE: [PERMISSIONS_NAMES.GAME_GROUP, PERMISSIONS_NAMES.GAME_GROUP_CREATE, PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP, PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_CREATE],\n  GAME_GROUP_EDIT: [PERMISSIONS_NAMES.GAME_GROUP, PERMISSIONS_NAMES.GAME_GROUP_EDIT, PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP, PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_EDIT],\n  GAME_GROUP_DELETE: [PERMISSIONS_NAMES.GAME_GROUP, PERMISSIONS_NAMES.GAME_GROUP_DELETE, PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP, PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_DELETE],\n  GAME_CHANGE_STATE: [PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE],\n  GAME_CHANGE_STATE_DISABLED: [PERMISSIONS_NAMES.KEYENTITY_GAME, PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE, PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE_DISABLED],\n  GAME_CHANGE_STATE_ENABLED: [PERMISSIONS_NAMES.KEYENTITY_GAME, PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE, PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE_ENABLED],\n  GAMERTP: [PERMISSIONS_NAMES.GAMERTP, PERMISSIONS_NAMES.GAMERTP_VIEW, PERMISSIONS_NAMES.KEYENTITY_GAMERTP, PERMISSIONS_NAMES.KEYENTITY_GAMERTP_VIEW],\n  ENTITY_LABELS: [PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS, PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_VIEW, PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_CREATE, PERMISSIONS_NAMES.ENTITYLABELS, PERMISSIONS_NAMES.ENTITYLABELS_VIEW, PERMISSIONS_NAMES.ENTITYLABELS_CREATE],\n  JACKPOT_VIEW: [PERMISSIONS_NAMES.JACKPOT, PERMISSIONS_NAMES.JACKPOT_INSTANCE, PERMISSIONS_NAMES.JACKPOT_INSTANCE_VIEW],\n  FLAT_REPORTS: [PERMISSIONS_NAMES.FLAT_REPORTS, PERMISSIONS_NAMES.FLAT_REPORTS_VIEW, PERMISSIONS_NAMES.KEYENTITY_FLAT_REPORTS, PERMISSIONS_NAMES.KEYENTITY_FLAT_REPORTS_VIEW],\n  ENTITY_BULK_ACTIONS: [PERMISSIONS_NAMES.KEYENTITY_ENTITYDOMAIN_BULKOPERATION, PERMISSIONS_NAMES.KEYENTITY_BULKOPERATION]\n});", "map": {"version": 3, "names": ["PERMISSIONS_NAMES", "Object", "freeze", "KEYENTITY_REPORT", "KEYENTITY_REPORT_CURRENCY", "KEYENTITY_REPORT_WALLET_CURRENCY", "KEYENTITY_REPORT_PLAYERS", "KEYENTITY_REPORT_PROMO", "REPORT", "REPORT_CURRENCY", "KEYENTITY_CASHIER", "REPORT_WALLET_CURRENCY", "REPORT_PLAYERS", "REPORT_WITHOUT_LIMIT", "REPORT_PROMO", "PLAYER", "PLAYER_RESET_CHANGE_NICKNAME", "ENTITY_PLAYER", "KEYENTITY_PLAYER", "KEYENTITY_PLAYER_DEPOSIT", "KEYENTITY_PLAYER_WITHDRAWAL", "PLAYER_PROMOTION", "KEYENTITY_PLAYER_PROMOTION", "PLAYER_VIEW", "ENTITY_PLAYER_VIEW", "KEYENTITY_PLAYER_VIEW", "KEYENTITY_PLAYER_CREATE", "USER", "USER_EDIT", "USER_CREATE", "USER_CHANGE_TYPE", "ENTITY_USER", "KEYENTITY_USER", "KEYENTITY_USER_CREATE", "KEYENTITY_USER_CHANGE_TYPE", "KEYENTITY_USER_EDIT", "USER_VIEW", "ENTITY_USER_VIEW", "KEYENTITY_USER_VIEW", "LOBBY", "LOBBY_CREATE", "LOBBY_VIEW", "LOBBY_EDIT", "LOBBY_DELETE", "KEYENTITY_LOBBY", "KEYENTITY_LOBBY_CREATE", "KEYENTITY_LOBBY_VIEW", "KEYENTITY_LOBBY_EDIT", "KEYENTITY_LOBBY_DELETE", "KEYENTITY_MERCHANT_VIEW", "KEYENTITY_INTEGRATION_VIEW", "TERMINAL", "TERMINAL_VIEW", "ENTITY_GAME", "ENTITY_GAME_URL", "ENTITY_INFO", "ENTITY_GAME_UNFINISHED", "ENTITY_GAME_HISTORY", "ENTITY_GAME_ADD_GAME_CASCADE", "ENTITY_GAME_CHANGE_STATE", "ENTITY_GAME_CHANGE_STATE_DISABLED", "ENTITY_GAME_CHANGE_STATE_ENABLED", "KEYENTITY_GOS_GAME_HISTORY", "ENTITY_GOS_GAME_HISTORY", "KEYENTITY_EXTERNAL_GAME_PROVIDER_HISTORY", "KEYENTITY_EXTERNAL_GAME_PROVIDER_GAMECLOSE_FORCEFINISH", "ENTITY_EXTERNAL_GAME_PROVIDER_GAMECLOSE_FORCEFINISH", "ENTITY_EXTERNAL_GAME_PROVIDER_HISTORY", "KEYENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY", "ENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY", "DISABLE_ENTITY_GAME_HISTORY_BALANCES", "DISABLE_KEYENTITY_GAME_HISTORY_BALANCES", "KEYENTITY_GAME", "KEYENTITY_GAME_HISTORY", "KEYENTITY_GAME_CHANGE_STATE", "KEYENTITY_GAME_CHANGE_STATE_ENABLED", "KEYENTITY_GAME_CHANGE_STATE_DISABLED", "KEYENTITY_GAMECATEGORY", "KEYENTITY_GAMECATEGORY_VIEW", "KEYENTITY_GAME_UNFINISHED", "KEYENTITY_GAMEPROVIDER_GAME", "KEYENTITY_GAMEPROVIDER_GAME_CREATE", "KEYENTITY_GAMEPROVIDER_GAME_EDIT", "KEYENTITY_MASTER_GAMECATEGORY_VIEW", "KEYENTITY_MASTER_GAMECATEGORY", "GRANTED_ALL", "GRANTED_MOCK", "DENIED_ALL", "ENTITY", "ENTITY_VIEW", "ENTITY_CREATE", "ENTITY_EDIT", "ENTITY_BALANCE", "ENTITY_CHANGESTATE", "ENTITY_CHANGESTATE_TEST", "ENTITY_GAMECLOSE_FORCEFINISH", "KEYENTITY_GAMECLOSE_FORCEFINISH", "ENTITY_GAMECLOSE_REVERT", "KEYENTITY_GAMECLOSE_REVERT", "ENTITY_GAMECLOSE_RETRY", "KEYENTITY_GAMECLOSE_RETRY", "ENTITY_GAMECLOSE_TRANSFER_OUT", "KEYENTITY_GAMECLOSE_TRANSFER_OUT", "AGENT", "AGENT_VIEW", "KEYENTITY_AGENT", "KEYENTITY_AGENT_VIEW", "AUDIT", "KEYENTITY_AUDIT", "PAYMENT", "PAYMENT_VIEW", "PROMOTION", "PROMOTION_VIEW", "PROMOTION_CREATE", "PROMOTION_EDIT", "PROMOTION_DELETE", "PROMOTION_BONUSCOIN", "PROMOTION_REBATE", "KEYENTITY_PROMOTION", "KEYENTITY_PROMOTION_VIEW", "KEYENTITY_PROMOTION_CREATE", "KEYENTITY_PROMOTION_EDIT", "KEYENTITY_PROMOTION_DELETE", "KEYENTITY_PROMOTION_BONUSCOIN", "KEYENTITY_PROMOTION_BONUSCOIN_VIEW", "KEYENTITY_PROMOTION_BONUSCOIN_CREATE", "KEYENTITY_PROMOTION_BONUSCOIN_EDIT", "KEYENTITY_PROMOTION_BONUSCOIN_DELETE", "KEYENTITY_PROMOTION_REBATE", "KEYENTITY_PROMOTION_REBATE_VIEW", "KEYENTITY_PROMOTION_REBATE_CREATE", "KEYENTITY_PROMOTION_REBATE_EDIT", "KEYENTITY_PROMOTION_REBATE_DELETE", "KEYENTITY_PROMOTION_VIRTUALMONEY", "KEYENTITY_PROMOTION_VIRTUALMONEY_VIEW", "KEYENTITY_PROMOTION_VIRTUALMONEY_CREATE", "KEYENTITY_PROMOTION_VIRTUALMONEY_EDIT", "KEYENTITY_PROMOTION_VIRTUALMONEY_DELETE", "KEYENTITY_PROMOTION_FREEBET", "KEYENTITY_PROMOTION_FREEBET_VIEW", "KEYENTITY_PROMOTION_FREEBET_CREATE", "KEYENTITY_PROMOTION_FREEBET_EDIT", "KEYENTITY_PROMOTION_FREEBET_DELETE", "KEYENTITY_PROMOTION_SKYWIND", "KEYENTITY_PROMOTION_SKYWIND_CREATE", "KEYENTITY_PROMOTION_SKYWIND_EDIT", "KEYENTITY_PROMOTION_OWNER", "KEYENTITY_PAYMENT", "KEYENTITY_PAYMENT_VIEW", "ROLE", "ROLE_CREATE", "ROLE_EDIT", "ROLE_VIEW", "ROLE_DELETE", "DOMAIN", "FINANCE", "FINANCE_VIEW", "FINANCE_CREDIT", "FINANCE_DEBIT", "COUNTRY_ADD", "BI_REPORTS_VIEW", "KEYENTITY_BI_REPORTS_VIEW", "FORCE_RESET_PASSWORD", "KEYENTITY_FORCE_RESET_PASSWORD", "FORCE_SET_EMAIL", "KEYENTITY_FORCE_SET_EMAIL", "USER_DELETE", "KEYENTITY_USER_DELETE", "KEYENTITY_RESPONSIBLEGAMING_PLAYER", "KEYENTITY_RESPONSIBLEGAMING_PLAYER_VIEW", "KEYENTITY_RESPONSIBLEGAMING_PLAYER_EDIT", "RESPONSIBLEGAMING_PLAYER", "RESPONSIBLEGAMING_PLAYER_VIEW", "RESPONSIBLEGAMING_PLAYER_EDIT", "USER_UNBLOCK_CHANGE_PASSWORD", "KEYENTITY_USER_UNBLOCK_CHANGE_PASSWORD", "USER_UNBLOCK_LOGIN", "KEYENTITY_USER_UNBLOCK_LOGIN", "KEYENTITY_JURISDICTION", "KEYENTITY_JURISDICTION_VIEW", "KEYENTITY_ENTITYDOMAIN", "KEYENTITY_ENTITYDOMAIN_DYNAMIC", "KEYENTITY_ENTITYDOMAIN_DYNAMIC_VIEW", "ENTITY_ENTITYDOMAIN_DYNAMIC", "ENTITY_ENTITYDOMAIN_DYNAMIC_VIEW", "KEYENTITY_ENTITYDOMAIN_STATIC", "KEYENTITY_ENTITYDOMAIN_STATIC_VIEW", "ENTITY_ENTITYDOMAIN_STATIC", "ENTITY_ENTITYDOMAIN_STATIC_VIEW", "KEYENTITY_ENTITYDOMAIN_BULKOPERATION", "KEYENTITY_BULKOPERATION", "KEYENTITY_BI_REPORT_DOMAINS", "KEYENTITY_BI_REPORT_DOMAINS_VIEW", "KEYENTITY_BI_REPORT_DOMAINS_EDIT", "KEYENTITY_BI_REPORT_DOMAINS_SELECT", "KEYENTITY_ROLE_CREATE", "KEYENTITY_ROLE_EDIT", "KEYENTITY_ROLE_VIEW", "KEYENTITY_ROLE_DELETE", "JURISDICTION", "JURISDICTION_VIEW", "SRT", "SRT_CHALLENGE", "SRT_TOURNAMENT", "GAME_SERVER", "GAME_SERVER_VIEW", "GAME_SERVER_CREATE", "GAME_SERVER_EDIT", "GAME_SERVER_REMOVE", "GAME_GROUP_CREATE", "KEYENTITY_GAME_GROUP_CREATE", "GAME_GROUP", "GAME_GROUP_VIEW", "KEYENTITY_GAME_GROUP", "KEYENTITY_GAME_GROUP_VIEW", "GAME_GROUP_EDIT", "KEYENTITY_GAME_GROUP_EDIT", "GAME_GROUP_DELETE", "KEYENTITY_GAME_GROUP_DELETE", "HUB_CASINO", "HUB_ANALYTICS", "HUB_ENGAGEMENT", "HUB_ENGAGEMENT_TOURNAMENTS", "HUB_ENGAGEMENT_PRIZE_DROPS", "HUB_ENGAGEMENT_MUST_WIN_JACKPOTS", "HUB_STUDIO", "KEYENTITY_BI_REPORT_PLAYER_SHOW_HIDE_COLUMN_DEBITS_CREDITS", "BI_REPORT_PLAYER_SHOW_HIDE_COLUMN_DEBITS_CREDITS", "GAME_LIMITS", "KEYENTITY_GAME_LIMITS", "ENTITY_GAMECLOSE_FINALIZE", "KEYENTITY_GAMECLOSE_FINALIZE", "KEYENTITY_USER_CHANGE_PASSWORD", "GAMERTP", "GAMERTP_VIEW", "KEYENTITY_GAMERTP", "KEYENTITY_GAMERTP_VIEW", "KEYENTITY_GAMELABEL", "KEYENTITY_GAMELABEL_VIEW", "KEYENTITY_ENTITYLABELS", "KEYENTITY_ENTITYLABELS_VIEW", "KEYENTITY_ENTITYLABELS_CREATE", "ENTITYLABELS", "ENTITYLABELS_VIEW", "ENTITYLABELS_CREATE", "JACKPOT", "JACKPOT_INSTANCE", "JACKPOT_INSTANCE_VIEW", "ENTITY_LIVEGAME", "ENTITY_LIVEGAME_ADD", "ENTITY_LIVEGAME_REMOVE", "ENTITY_LIVEGAME_CHANGE_STATE", "ENTITY_LIVEGAME_CHANGE_STATE_ENABLED", "ENTITY_LIVEGAME_CHANGE_STATE_DISABLED", "KEYENTITY_LIVEGAME", "KEYENTITY_LIVEGAME_CHANGE_STATE", "KEYENTITY_LIVEGAME_CHANGE_STATE_ENABLED", "KEYENTITY_LIVEGAME_CHANGE_STATE_DISABLED", "FLAT_REPORTS", "FLAT_REPORTS_VIEW", "KEYENTITY_FLAT_REPORTS", "KEYENTITY_FLAT_REPORTS_VIEW", "ID_DECODE", "ID_ENCODE", "RESTRICTED_COUNTRIES_SOLUTION", "JP_CONFIG_REPORT", "DEPLOYMENT", "COUNTRY_REMOVE", "PERMISSIONS_LIST", "GAME_STORE", "REPORT_FINANCE", "GAME", "GAME_CREATE", "GAME_EDIT", "GAMES_CASCADE_ADD", "GAME_HISTORY", "GOS_GAME_HISTORY", "EXTERNAL_GAME_PROVIDER_HISTORY", "EXTERNAL_GAME_PROVIDER_AVAILABILITY", "EXTERNAL_GAME_PROVIDER_AVAILABILITY_ENTITY", "EXTERNAL_GAME_PROVIDER_AVAILABILITY_KEYENTITY", "GAMECATEGORY", "GAMELABEL", "TABLEAU_VIEW", "RESPONSIBLEGAMING", "ENTITY_SETTINGS", "GAME_CHANGE_STATE", "GAME_CHANGE_STATE_DISABLED", "GAME_CHANGE_STATE_ENABLED", "ENTITY_LABELS", "JACKPOT_VIEW", "ENTITY_BULK_ACTIONS"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-auth/permissions.ts"], "sourcesContent": ["export const PERMISSIONS_NAMES = Object.freeze({\n  KEYENTITY_REPORT: 'keyentity:report',\n  KEYENTITY_REPORT_CURRENCY: 'keyentity:report:currency',\n  KEYENTITY_REPORT_WALLET_CURRENCY: 'keyentity:report:wallet-currency',\n  K<PERSON><PERSON>ENTITY_REPORT_PLAYERS: 'keyentity:report:players',\n  KEYENTITY_REPORT_PROMO: 'keyentity:report:promo',\n  REPORT: 'report',\n  REPORT_CURRENCY: 'report:currency',\n  KEYENTITY_CASHIER: 'keyentity:cashier',\n  REPORT_WALLET_CURRENCY: 'report:wallet-currency',\n  REPORT_PLAYERS: 'report:players',\n  REPORT_WITHOUT_LIMIT: 'report-without-limit',\n  REPORT_PROMO: 'report:promo',\n  PLAYER: 'player',\n  PLAYER_RESET_CHANGE_NICKNAME: 'player:reset-change-nickname-attempts',\n  ENTITY_PLAYER: 'entity:player',\n  <PERSON><PERSON><PERSON>ENTITY_PLAYER: 'keyentity:player',\n  <PERSON><PERSON><PERSON>EN<PERSON>TY_PLAYER_DEPOSIT: 'keyentity:player:deposit',\n  KEYENTITY_PLAYER_WITHDRAWAL: 'keyentity:player:withdrawal',\n  PLAYER_PROMOTION: 'player:promotion',\n  KEYENTITY_PLAYER_PROMOTION: 'keyentity:player:promotion',\n  PLAYER_VIEW: 'player:view',\n  ENTITY_PLAYER_VIEW: 'entity:player:view',\n  KEYENTITY_PLAYER_VIEW: 'keyentity:player:view',\n  KEYENTITY_PLAYER_CREATE: 'keyentity:player:create',\n  USER: 'user',\n  USER_EDIT: 'user:edit',\n  USER_CREATE: 'user:create',\n  USER_CHANGE_TYPE: 'user:change-type',\n  ENTITY_USER: 'entity:user',\n  KEYENTITY_USER: 'keyentity:user',\n  KEYENTITY_USER_CREATE: 'keyentity:user:create',\n  KEYENTITY_USER_CHANGE_TYPE: 'keyentity:user:change-type',\n  KEYENTITY_USER_EDIT: 'keyentity:user:edit',\n  USER_VIEW: 'user:view',\n  ENTITY_USER_VIEW: 'entity:user:view',\n  KEYENTITY_USER_VIEW: 'keyentity:user:view',\n  LOBBY: 'lobby',\n  LOBBY_CREATE: 'lobby:create',\n  LOBBY_VIEW: 'lobby:view',\n  LOBBY_EDIT: 'lobby:edit',\n  LOBBY_DELETE: 'lobby:delete',\n  KEYENTITY_LOBBY: 'keyentity:lobby',\n  KEYENTITY_LOBBY_CREATE: 'keyentity:lobby:create',\n  KEYENTITY_LOBBY_VIEW: 'keyentity:lobby:view',\n  KEYENTITY_LOBBY_EDIT: 'keyentity:lobby:edit',\n  KEYENTITY_LOBBY_DELETE: 'keyentity:lobby:delete',\n  KEYENTITY_MERCHANT_VIEW: 'keyentity:merchant:view',\n  KEYENTITY_INTEGRATION_VIEW: 'keyentity:integration:view',\n  TERMINAL: 'keyentity:terminal',\n  TERMINAL_VIEW: 'keyentity:terminal:view',\n  ENTITY_GAME: 'entity:game',\n  ENTITY_GAME_URL: 'entity:game:url',\n  ENTITY_INFO: 'entity:info',\n  ENTITY_GAME_UNFINISHED: 'entity:game:unfinished',\n  ENTITY_GAME_HISTORY: 'entity:game:history',\n  ENTITY_GAME_ADD_GAME_CASCADE: 'entity:game:add-game-cascade',\n  ENTITY_GAME_CHANGE_STATE: 'entity:game:change-state',\n  ENTITY_GAME_CHANGE_STATE_DISABLED: 'entity:game:change-state:disabled',\n  ENTITY_GAME_CHANGE_STATE_ENABLED: 'entity:game:change-state:enabled',\n  KEYENTITY_GOS_GAME_HISTORY: 'keyentity:gos:game:history',\n  ENTITY_GOS_GAME_HISTORY: 'entity:gos:game:history',\n  KEYENTITY_EXTERNAL_GAME_PROVIDER_HISTORY: 'keyentity:external-game-provider:history',\n  KEYENTITY_EXTERNAL_GAME_PROVIDER_GAMECLOSE_FORCEFINISH: 'keyentity:external-game-provider:gameclose:forcefinish',\n  ENTITY_EXTERNAL_GAME_PROVIDER_GAMECLOSE_FORCEFINISH: 'entity:external-game-provider:gameclose:forcefinish',\n  ENTITY_EXTERNAL_GAME_PROVIDER_HISTORY: 'entity:external-game-provider:history',\n  KEYENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY: 'keyentity:external-game-provider:availability',\n  ENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY: 'entity:external-game-provider:availability',\n  DISABLE_ENTITY_GAME_HISTORY_BALANCES: 'disable:entity:game-history:balances',\n  DISABLE_KEYENTITY_GAME_HISTORY_BALANCES: 'disable:keyentity:game-history:balances',\n  KEYENTITY_GAME: 'keyentity:game',\n  KEYENTITY_GAME_HISTORY: 'keyentity:game:history',\n  KEYENTITY_GAME_CHANGE_STATE: 'keyentity:game:change-state',\n  KEYENTITY_GAME_CHANGE_STATE_ENABLED: 'keyentity:game:change-state:enabled',\n  KEYENTITY_GAME_CHANGE_STATE_DISABLED: 'keyentity:game:change-state:disabled',\n  KEYENTITY_GAMECATEGORY: 'keyentity:gamecategory',\n  KEYENTITY_GAMECATEGORY_VIEW: 'keyentity:gamecategory:view',\n  KEYENTITY_GAME_UNFINISHED: 'keyentity:game:unfinished',\n  KEYENTITY_GAMEPROVIDER_GAME: 'keyentity:gameprovider:game',\n  KEYENTITY_GAMEPROVIDER_GAME_CREATE: 'keyentity:gameprovider:game:create',\n  KEYENTITY_GAMEPROVIDER_GAME_EDIT: 'keyentity:gameprovider:game:edit',\n  KEYENTITY_MASTER_GAMECATEGORY_VIEW: 'keyentity:master:gamecategory:view',\n  KEYENTITY_MASTER_GAMECATEGORY: 'keyentity:master:gamecategory',\n  GRANTED_ALL: 'granted:all',\n  GRANTED_MOCK: 'granted:mock',\n  DENIED_ALL: 'denied:all',\n  ENTITY: 'entity',\n  ENTITY_VIEW: 'entity:view',\n  ENTITY_CREATE: 'entity:create',\n  ENTITY_EDIT: 'entity:edit',\n  ENTITY_BALANCE: 'entity:balance',\n  ENTITY_CHANGESTATE: 'entity:change-state',\n  ENTITY_CHANGESTATE_TEST: 'entity:change-state-test',\n  ENTITY_GAMECLOSE_FORCEFINISH: 'entity:gameclose:forcefinish',\n  KEYENTITY_GAMECLOSE_FORCEFINISH: 'keyentity:gameclose:forcefinish',\n  ENTITY_GAMECLOSE_REVERT: 'entity:gameclose:revert',\n  KEYENTITY_GAMECLOSE_REVERT: 'keyentity:gameclose:revert',\n  ENTITY_GAMECLOSE_RETRY: 'entity:gameclose:retry',\n  KEYENTITY_GAMECLOSE_RETRY: 'keyentity:gameclose:retry',\n  ENTITY_GAMECLOSE_TRANSFER_OUT: 'entity:gameclose:transfer-out',\n  KEYENTITY_GAMECLOSE_TRANSFER_OUT: 'keyentity:gameclose:transfer-out',\n  AGENT: 'agent',\n  AGENT_VIEW: 'agent:view',\n  KEYENTITY_AGENT: 'keyentity:agent',\n  KEYENTITY_AGENT_VIEW: 'keyentity:agent:view',\n  AUDIT: 'audit',\n  KEYENTITY_AUDIT: 'keyentity:audit',\n  PAYMENT: 'payment',\n  PAYMENT_VIEW: 'payment:view',\n  PROMOTION: 'promotion',\n  PROMOTION_VIEW: 'promotion:view',\n  PROMOTION_CREATE: 'promotion:create',\n  PROMOTION_EDIT: 'promotion:edit',\n  PROMOTION_DELETE: 'promotion:delete',\n  PROMOTION_BONUSCOIN: 'promotion:bonuscoin',\n  PROMOTION_REBATE: 'promotion:rebate',\n  KEYENTITY_PROMOTION: 'keyentity:promotion',\n  KEYENTITY_PROMOTION_VIEW: 'keyentity:promotion:view',\n  KEYENTITY_PROMOTION_CREATE: 'keyentity:promotion:create',\n  KEYENTITY_PROMOTION_EDIT: 'keyentity:promotion:edit',\n  KEYENTITY_PROMOTION_DELETE: 'keyentity:promotion:delete',\n  KEYENTITY_PROMOTION_BONUSCOIN: 'keyentity:promotion:bonuscoin',\n  KEYENTITY_PROMOTION_BONUSCOIN_VIEW: 'keyentity:promotion:bonuscoin:view',\n  KEYENTITY_PROMOTION_BONUSCOIN_CREATE: 'keyentity:promotion:bonuscoin:create',\n  KEYENTITY_PROMOTION_BONUSCOIN_EDIT: 'keyentity:promotion:bonuscoin:edit',\n  KEYENTITY_PROMOTION_BONUSCOIN_DELETE: 'keyentity:promotion:bonuscoin:delete',\n  KEYENTITY_PROMOTION_REBATE: 'keyentity:promotion:rebate',\n  KEYENTITY_PROMOTION_REBATE_VIEW: 'keyentity:promotion:rebate:view',\n  KEYENTITY_PROMOTION_REBATE_CREATE: 'keyentity:promotion:rebate:create',\n  KEYENTITY_PROMOTION_REBATE_EDIT: 'keyentity:promotion:rebate:edit',\n  KEYENTITY_PROMOTION_REBATE_DELETE: 'keyentity:promotion:rebate:delete',\n  KEYENTITY_PROMOTION_VIRTUALMONEY: 'keyentity:promotion:virtualmoney',\n  KEYENTITY_PROMOTION_VIRTUALMONEY_VIEW: 'keyentity:promotion:virtualmoney:view',\n  KEYENTITY_PROMOTION_VIRTUALMONEY_CREATE: 'keyentity:promotion:virtualmoney:create',\n  KEYENTITY_PROMOTION_VIRTUALMONEY_EDIT: 'keyentity:promotion:virtualmoney:edit',\n  KEYENTITY_PROMOTION_VIRTUALMONEY_DELETE: 'keyentity:promotion:virtualmoney:delete',\n  KEYENTITY_PROMOTION_FREEBET: 'keyentity:promotion:freebet',\n  KEYENTITY_PROMOTION_FREEBET_VIEW: 'keyentity:promotion:freebet:view',\n  KEYENTITY_PROMOTION_FREEBET_CREATE: 'keyentity:promotion:freebet:create',\n  KEYENTITY_PROMOTION_FREEBET_EDIT: 'keyentity:promotion:freebet:edit',\n  KEYENTITY_PROMOTION_FREEBET_DELETE: 'keyentity:promotion:freebet:delete',\n  KEYENTITY_PROMOTION_SKYWIND: 'keyentity:promotion:skywind', // Manage skywind promotions\n  KEYENTITY_PROMOTION_SKYWIND_CREATE: 'keyentity:promotion:skywind:create', // Create skywind promotions\n  KEYENTITY_PROMOTION_SKYWIND_EDIT: 'keyentity:promotion:skywind:edit', // Update skywind promotions\n  KEYENTITY_PROMOTION_OWNER: 'keyentity:promotion:owner', // Update owner of promotion\n  KEYENTITY_PAYMENT: 'keyentity:payment',\n  KEYENTITY_PAYMENT_VIEW: 'keyentity:payment:view',\n  ROLE: 'role',\n  ROLE_CREATE: 'role:create',\n  ROLE_EDIT: 'role:edit',\n  ROLE_VIEW: 'role:view',\n  ROLE_DELETE: 'role:delete',\n  DOMAIN: 'domain',\n  FINANCE: 'finance',\n  FINANCE_VIEW: 'finance:view',\n  FINANCE_CREDIT: 'finance:credit',\n  FINANCE_DEBIT: 'finance:debit',\n  COUNTRY_ADD: 'country:add',\n  BI_REPORTS_VIEW: 'bi:reports:view',\n  KEYENTITY_BI_REPORTS_VIEW: 'keyentity:bi:reports:view',\n  FORCE_RESET_PASSWORD: 'user-extra:force-reset-password',\n  KEYENTITY_FORCE_RESET_PASSWORD: 'keyentity:user-extra:force-reset-password',\n  FORCE_SET_EMAIL: 'user-extra:email:force-set',\n  KEYENTITY_FORCE_SET_EMAIL: 'keyentity:user-extra:email:force-set',\n  USER_DELETE: 'user-extra:delete',\n  KEYENTITY_USER_DELETE: 'keyentity:user-extra:delete',\n  KEYENTITY_RESPONSIBLEGAMING_PLAYER: 'keyentity:responsiblegaming:player',\n  KEYENTITY_RESPONSIBLEGAMING_PLAYER_VIEW: 'keyentity:responsiblegaming:player:view',\n  KEYENTITY_RESPONSIBLEGAMING_PLAYER_EDIT: 'keyentity:responsiblegaming:player:edit',\n  RESPONSIBLEGAMING_PLAYER: 'responsiblegaming:player',\n  RESPONSIBLEGAMING_PLAYER_VIEW: 'responsiblegaming:player:view',\n  RESPONSIBLEGAMING_PLAYER_EDIT: 'responsiblegaming:player:edit',\n  USER_UNBLOCK_CHANGE_PASSWORD: 'user-extra:change-password-unlock',\n  KEYENTITY_USER_UNBLOCK_CHANGE_PASSWORD: 'keyentity:user-extra:change-password-unlock',\n  USER_UNBLOCK_LOGIN: 'user-extra:login-unlock',\n  KEYENTITY_USER_UNBLOCK_LOGIN: 'keyentity:user-extra:login-unlock',\n  KEYENTITY_JURISDICTION: 'keyentity:jurisdiction',\n  KEYENTITY_JURISDICTION_VIEW: 'keyentity:jurisdiction:view',\n  KEYENTITY_ENTITYDOMAIN: 'keyentity:entitydomain',\n  KEYENTITY_ENTITYDOMAIN_DYNAMIC: 'keyentity:entitydomain:dynamic',\n  KEYENTITY_ENTITYDOMAIN_DYNAMIC_VIEW: 'keyentity:entitydomain:dynamic:view',\n  ENTITY_ENTITYDOMAIN_DYNAMIC: 'entity:entitydomain:dynamic',\n  ENTITY_ENTITYDOMAIN_DYNAMIC_VIEW: 'entity:entitydomain:dynamic:view',\n  KEYENTITY_ENTITYDOMAIN_STATIC: 'keyentity:entitydomain:static',\n  KEYENTITY_ENTITYDOMAIN_STATIC_VIEW: 'keyentity:entitydomain:static:view',\n  ENTITY_ENTITYDOMAIN_STATIC: 'entity:entitydomain:static',\n  ENTITY_ENTITYDOMAIN_STATIC_VIEW: 'entity:entitydomain:static:view',\n  KEYENTITY_ENTITYDOMAIN_BULKOPERATION: 'keyentity:entitydomain:bulk-operation',\n  KEYENTITY_BULKOPERATION: 'keyentity:bulk-operation',\n  KEYENTITY_BI_REPORT_DOMAINS: 'keyentity:bi-reports-domains',\n  KEYENTITY_BI_REPORT_DOMAINS_VIEW: 'keyentity:bi-reports-domains:view',\n  KEYENTITY_BI_REPORT_DOMAINS_EDIT: 'keyentity:bi-reports-domains:edit',\n  KEYENTITY_BI_REPORT_DOMAINS_SELECT: 'keyentity:bi-reports-domains:select',\n  KEYENTITY_ROLE_CREATE: 'keyentity:role:create',\n  KEYENTITY_ROLE_EDIT: 'keyentity:role:edit',\n  KEYENTITY_ROLE_VIEW: 'keyentity:role:view',\n  KEYENTITY_ROLE_DELETE: 'keyentity:role:delete',\n  JURISDICTION: 'jurisdiction',\n  JURISDICTION_VIEW: 'jurisdiction:view',\n  SRT: 'srt',\n  SRT_CHALLENGE: 'srt:challenge',\n  SRT_TOURNAMENT: 'srt:tournament',\n  GAME_SERVER: 'gs:settings',\n  GAME_SERVER_VIEW: 'gs:settings:view',\n  GAME_SERVER_CREATE: 'gs:settings:create',\n  GAME_SERVER_EDIT: 'gs:settings:edit',\n  GAME_SERVER_REMOVE: 'gs:settings:remove',\n  GAME_GROUP_CREATE: 'gamegroup:create',\n  KEYENTITY_GAME_GROUP_CREATE: 'keyentity:gamegroup:create',\n  GAME_GROUP: 'gamegroup',\n  GAME_GROUP_VIEW: 'gamegroup:view',\n  KEYENTITY_GAME_GROUP: 'keyentity:gamegroup',\n  KEYENTITY_GAME_GROUP_VIEW: 'keyentity:gamegroup:view',\n  GAME_GROUP_EDIT: 'gamegroup:edit',\n  KEYENTITY_GAME_GROUP_EDIT: 'keyentity:gamegroup:edit',\n  GAME_GROUP_DELETE: 'gamegroup:delete',\n  KEYENTITY_GAME_GROUP_DELETE: 'keyentity:gamegroup:delete',\n  HUB_CASINO: 'hub:casino',\n  HUB_ANALYTICS: 'hub:analytics',\n  HUB_ENGAGEMENT: 'hub:engagement',\n  HUB_ENGAGEMENT_TOURNAMENTS: 'hub:engagement:tournaments',\n  HUB_ENGAGEMENT_PRIZE_DROPS: 'hub:engagement:prize-drops',\n  HUB_ENGAGEMENT_MUST_WIN_JACKPOTS: 'hub:engagement:must-win-jackpots',\n  HUB_STUDIO: 'hub:studio',\n  KEYENTITY_BI_REPORT_PLAYER_SHOW_HIDE_COLUMN_DEBITS_CREDITS:\n    'keyentity:bi:report:player-show-hide-column:debits-credits',\n  BI_REPORT_PLAYER_SHOW_HIDE_COLUMN_DEBITS_CREDITS: 'bi:report:player-show-hide-column:debits-credits',\n  GAME_LIMITS: 'gamelimits',\n  KEYENTITY_GAME_LIMITS: 'keyentity:gamelimits',\n  ENTITY_GAMECLOSE_FINALIZE: 'entity:gameclose:finalize',\n  KEYENTITY_GAMECLOSE_FINALIZE: 'keyentity:gameclose:finalize',\n  KEYENTITY_USER_CHANGE_PASSWORD: 'keyentity:user:change-password',\n  GAMERTP: 'gamertp',\n  GAMERTP_VIEW: 'gamertp:view',\n  KEYENTITY_GAMERTP: 'keyentity:gamertp',\n  KEYENTITY_GAMERTP_VIEW: 'keyentity:gamertp:view',\n  KEYENTITY_GAMELABEL: 'keyentity:gamelabel',\n  KEYENTITY_GAMELABEL_VIEW: 'keyentity:gamelabel:view',\n  KEYENTITY_ENTITYLABELS: 'keyentity:entitylabels',\n  KEYENTITY_ENTITYLABELS_VIEW: 'keyentity:entitylabels:view',\n  KEYENTITY_ENTITYLABELS_CREATE: 'keyentity:entitylabels:create',\n  ENTITYLABELS: 'entitylabels',\n  ENTITYLABELS_VIEW: 'entitylabels:view',\n  ENTITYLABELS_CREATE: 'entitylabels:create',\n  JACKPOT: 'jackpot',\n  JACKPOT_INSTANCE: 'jackpot:instance',\n  JACKPOT_INSTANCE_VIEW: 'jackpot:instance:view',\n  ENTITY_LIVEGAME: 'entity:live-game',\n  ENTITY_LIVEGAME_ADD: 'entity:live-game:add-live-game',\n  ENTITY_LIVEGAME_REMOVE: 'entity:live-game:remove-live-game',\n  ENTITY_LIVEGAME_CHANGE_STATE: 'entity:live-game:change-state',\n  ENTITY_LIVEGAME_CHANGE_STATE_ENABLED: 'entity:live-game:change-state:enabled',\n  ENTITY_LIVEGAME_CHANGE_STATE_DISABLED: 'entity:live-game:change-state:disabled',\n  KEYENTITY_LIVEGAME: 'keyentity:live-game',\n  KEYENTITY_LIVEGAME_CHANGE_STATE: 'keyentity:live-game:change-state',\n  KEYENTITY_LIVEGAME_CHANGE_STATE_ENABLED: 'keyentity:live-game:change-state:enabled',\n  KEYENTITY_LIVEGAME_CHANGE_STATE_DISABLED: 'keyentity:live-game:change-state:disabled',\n  FLAT_REPORTS: 'flat-reports',\n  FLAT_REPORTS_VIEW: 'flat-reports:view',\n  KEYENTITY_FLAT_REPORTS: 'keyentity:flat-reports',\n  KEYENTITY_FLAT_REPORTS_VIEW: 'keyentity:flat-reports:view',\n  ID_DECODE: 'id:decode',\n  ID_ENCODE: 'id:encode',\n  RESTRICTED_COUNTRIES_SOLUTION: 'restricted-countries-solution',\n  JP_CONFIG_REPORT: 'jp-config-report',\n  DEPLOYMENT: 'deployment',\n  COUNTRY_REMOVE: 'country:remove',\n});\n\nexport const PERMISSIONS_LIST = Object.freeze({\n  GAME_STORE: [\n    PERMISSIONS_NAMES.GRANTED_MOCK,\n  ],\n  REPORT: [\n    PERMISSIONS_NAMES.KEYENTITY_REPORT,\n    PERMISSIONS_NAMES.KEYENTITY_REPORT_CURRENCY,\n    PERMISSIONS_NAMES.KEYENTITY_REPORT_WALLET_CURRENCY,\n    PERMISSIONS_NAMES.KEYENTITY_REPORT_PLAYERS,\n    PERMISSIONS_NAMES.REPORT,\n    PERMISSIONS_NAMES.REPORT_CURRENCY,\n    PERMISSIONS_NAMES.REPORT_WALLET_CURRENCY,\n    PERMISSIONS_NAMES.REPORT_PLAYERS,\n  ],\n  PLAYER: [\n    PERMISSIONS_NAMES.PLAYER,\n    PERMISSIONS_NAMES.ENTITY_PLAYER,\n    PERMISSIONS_NAMES.KEYENTITY_PLAYER,\n  ],\n  PLAYER_VIEW: [\n    PERMISSIONS_NAMES.PLAYER_VIEW,\n    PERMISSIONS_NAMES.ENTITY_PLAYER_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_PLAYER_VIEW,\n  ],\n  USER: [\n    PERMISSIONS_NAMES.USER,\n    PERMISSIONS_NAMES.ENTITY_USER,\n    PERMISSIONS_NAMES.KEYENTITY_USER,\n  ],\n  USER_VIEW: [\n    PERMISSIONS_NAMES.USER_VIEW,\n    PERMISSIONS_NAMES.ENTITY_USER_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_USER_VIEW,\n  ],\n  LOBBY: [\n    PERMISSIONS_NAMES.LOBBY,\n    PERMISSIONS_NAMES.LOBBY_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_LOBBY,\n    PERMISSIONS_NAMES.KEYENTITY_LOBBY_VIEW,\n  ],\n  TERMINAL: [\n    PERMISSIONS_NAMES.TERMINAL,\n    PERMISSIONS_NAMES.TERMINAL_VIEW,\n  ],\n  REPORT_CURRENCY: [\n    PERMISSIONS_NAMES.REPORT_CURRENCY,\n    PERMISSIONS_NAMES.REPORT_WALLET_CURRENCY,\n    PERMISSIONS_NAMES.KEYENTITY_REPORT_CURRENCY,\n    PERMISSIONS_NAMES.KEYENTITY_REPORT_WALLET_CURRENCY,\n  ],\n  REPORT_PLAYERS: [\n    PERMISSIONS_NAMES.REPORT_PLAYERS,\n    PERMISSIONS_NAMES.KEYENTITY_REPORT_PLAYERS,\n  ],\n  REPORT_FINANCE: [\n    PERMISSIONS_NAMES.FINANCE_VIEW,\n  ],\n  GAME: [\n    PERMISSIONS_NAMES.ENTITY_GAME,\n    PERMISSIONS_NAMES.KEYENTITY_GAME,\n  ],\n  GAME_CREATE: [\n    PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME,\n    PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME_CREATE\n  ],\n  GAME_EDIT: [\n    PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME,\n    PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME_EDIT\n  ],\n  GAMES_CASCADE_ADD: [\n    PERMISSIONS_NAMES.ENTITY_GAME,\n    PERMISSIONS_NAMES.ENTITY_GAME_ADD_GAME_CASCADE,\n  ],\n  GAME_HISTORY: [\n    PERMISSIONS_NAMES.ENTITY_GAME_HISTORY,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_HISTORY,\n  ],\n  GOS_GAME_HISTORY: [\n    PERMISSIONS_NAMES.ENTITY_GOS_GAME_HISTORY,\n    PERMISSIONS_NAMES.KEYENTITY_GOS_GAME_HISTORY,\n  ],\n  EXTERNAL_GAME_PROVIDER_HISTORY: [\n    PERMISSIONS_NAMES.ENTITY_EXTERNAL_GAME_PROVIDER_HISTORY,\n    PERMISSIONS_NAMES.KEYENTITY_EXTERNAL_GAME_PROVIDER_HISTORY,\n  ],\n  EXTERNAL_GAME_PROVIDER_AVAILABILITY: [\n    PERMISSIONS_NAMES.ENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY,\n    PERMISSIONS_NAMES.KEYENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY,\n  ],\n  EXTERNAL_GAME_PROVIDER_AVAILABILITY_ENTITY: [\n    PERMISSIONS_NAMES.ENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY,\n  ],\n  EXTERNAL_GAME_PROVIDER_AVAILABILITY_KEYENTITY: [\n    PERMISSIONS_NAMES.KEYENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY,\n  ],\n  GAMECATEGORY: [\n    PERMISSIONS_NAMES.KEYENTITY_GAMECATEGORY,\n    PERMISSIONS_NAMES.KEYENTITY_GAMECATEGORY_VIEW,\n  ],\n  GAMELABEL: [\n    PERMISSIONS_NAMES.KEYENTITY_GAMELABEL,\n    PERMISSIONS_NAMES.KEYENTITY_GAMELABEL_VIEW\n  ],\n  ENTITY: [\n    PERMISSIONS_NAMES.ENTITY,\n    PERMISSIONS_NAMES.ENTITY_VIEW,\n  ],\n  AGENT: [\n    PERMISSIONS_NAMES.AGENT,\n    PERMISSIONS_NAMES.AGENT_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_AGENT,\n    PERMISSIONS_NAMES.KEYENTITY_AGENT_VIEW,\n  ],\n  AUDIT: [\n    PERMISSIONS_NAMES.AUDIT,\n    PERMISSIONS_NAMES.KEYENTITY_AUDIT,\n  ],\n  PAYMENT: [\n    PERMISSIONS_NAMES.PAYMENT,\n    PERMISSIONS_NAMES.PAYMENT_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_PAYMENT,\n    PERMISSIONS_NAMES.KEYENTITY_PAYMENT_VIEW,\n  ],\n  PROMOTION: [\n    PERMISSIONS_NAMES.PROMOTION,\n    PERMISSIONS_NAMES.KEYENTITY_PROMOTION,\n  ],\n  PROMOTION_VIEW: [\n    PERMISSIONS_NAMES.PROMOTION_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_PROMOTION_VIEW,\n  ],\n  PROMOTION_CREATE: [\n    PERMISSIONS_NAMES.PROMOTION_CREATE,\n    PERMISSIONS_NAMES.KEYENTITY_PROMOTION_CREATE,\n  ],\n  PROMOTION_EDIT: [\n    PERMISSIONS_NAMES.PROMOTION_EDIT,\n    PERMISSIONS_NAMES.KEYENTITY_PROMOTION_EDIT,\n  ],\n  PROMOTION_BONUSCOIN: [\n    PERMISSIONS_NAMES.PROMOTION_BONUSCOIN,\n    PERMISSIONS_NAMES.KEYENTITY_PROMOTION_BONUSCOIN,\n  ],\n  PROMOTION_REBATE: [\n    PERMISSIONS_NAMES.PROMOTION_REBATE,\n    PERMISSIONS_NAMES.KEYENTITY_PROMOTION_REBATE,\n  ],\n  PLAYER_PROMOTION: [\n    PERMISSIONS_NAMES.KEYENTITY_PLAYER_PROMOTION,\n    PERMISSIONS_NAMES.PLAYER_PROMOTION,\n  ],\n  ROLE: [\n    PERMISSIONS_NAMES.KEYENTITY_ROLE_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_ROLE_EDIT,\n    PERMISSIONS_NAMES.KEYENTITY_ROLE_CREATE,\n    PERMISSIONS_NAMES.KEYENTITY_ROLE_DELETE,\n  ],\n  DOMAIN: [\n    PERMISSIONS_NAMES.DOMAIN,\n  ],\n  DENIED_ALL: [\n    PERMISSIONS_NAMES.DENIED_ALL\n  ],\n  GRANTED_ALL: [\n    PERMISSIONS_NAMES.GRANTED_ALL\n  ],\n  GRANTED_MOCK: [\n    PERMISSIONS_NAMES.GRANTED_MOCK\n  ],\n  TABLEAU_VIEW: [\n    PERMISSIONS_NAMES.BI_REPORTS_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_BI_REPORTS_VIEW,\n  ],\n  RESPONSIBLEGAMING: [\n    PERMISSIONS_NAMES.KEYENTITY_RESPONSIBLEGAMING_PLAYER,\n    PERMISSIONS_NAMES.KEYENTITY_RESPONSIBLEGAMING_PLAYER_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_RESPONSIBLEGAMING_PLAYER_EDIT,\n    PERMISSIONS_NAMES.RESPONSIBLEGAMING_PLAYER,\n    PERMISSIONS_NAMES.RESPONSIBLEGAMING_PLAYER_VIEW,\n    PERMISSIONS_NAMES.RESPONSIBLEGAMING_PLAYER_EDIT,\n  ],\n  SRT: [\n    PERMISSIONS_NAMES.SRT_CHALLENGE,\n    PERMISSIONS_NAMES.SRT_TOURNAMENT,\n  ],\n  GAME_SERVER: [\n    PERMISSIONS_NAMES.GAME_SERVER_VIEW,\n    PERMISSIONS_NAMES.GAME_SERVER_CREATE,\n    PERMISSIONS_NAMES.GAME_SERVER_EDIT,\n    PERMISSIONS_NAMES.GAME_SERVER_REMOVE,\n  ],\n  ENTITY_SETTINGS: [\n    PERMISSIONS_NAMES.ENTITY,\n    PERMISSIONS_NAMES.ENTITY_EDIT,\n  ],\n  GAME_GROUP_VIEW: [\n    PERMISSIONS_NAMES.GAME_GROUP,\n    PERMISSIONS_NAMES.GAME_GROUP_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_VIEW,\n  ],\n  GAME_GROUP_CREATE: [\n    PERMISSIONS_NAMES.GAME_GROUP,\n    PERMISSIONS_NAMES.GAME_GROUP_CREATE,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_CREATE,\n  ],\n  GAME_GROUP_EDIT: [\n    PERMISSIONS_NAMES.GAME_GROUP,\n    PERMISSIONS_NAMES.GAME_GROUP_EDIT,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_EDIT,\n  ],\n  GAME_GROUP_DELETE: [\n    PERMISSIONS_NAMES.GAME_GROUP,\n    PERMISSIONS_NAMES.GAME_GROUP_DELETE,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_DELETE,\n  ],\n  GAME_CHANGE_STATE: [\n    PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE,\n  ],\n  GAME_CHANGE_STATE_DISABLED: [\n    PERMISSIONS_NAMES.KEYENTITY_GAME,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE_DISABLED\n  ],\n  GAME_CHANGE_STATE_ENABLED: [\n    PERMISSIONS_NAMES.KEYENTITY_GAME,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE,\n    PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE_ENABLED\n  ],\n  GAMERTP: [\n    PERMISSIONS_NAMES.GAMERTP,\n    PERMISSIONS_NAMES.GAMERTP_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_GAMERTP,\n    PERMISSIONS_NAMES.KEYENTITY_GAMERTP_VIEW,\n  ],\n  ENTITY_LABELS: [\n    PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS,\n    PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_CREATE,\n    PERMISSIONS_NAMES.ENTITYLABELS,\n    PERMISSIONS_NAMES.ENTITYLABELS_VIEW,\n    PERMISSIONS_NAMES.ENTITYLABELS_CREATE,\n  ],\n  JACKPOT_VIEW: [\n    PERMISSIONS_NAMES.JACKPOT,\n    PERMISSIONS_NAMES.JACKPOT_INSTANCE,\n    PERMISSIONS_NAMES.JACKPOT_INSTANCE_VIEW,\n  ],\n  FLAT_REPORTS: [\n    PERMISSIONS_NAMES.FLAT_REPORTS,\n    PERMISSIONS_NAMES.FLAT_REPORTS_VIEW,\n    PERMISSIONS_NAMES.KEYENTITY_FLAT_REPORTS,\n    PERMISSIONS_NAMES.KEYENTITY_FLAT_REPORTS_VIEW,\n  ],\n  ENTITY_BULK_ACTIONS: [\n    PERMISSIONS_NAMES.KEYENTITY_ENTITYDOMAIN_BULKOPERATION,\n    PERMISSIONS_NAMES.KEYENTITY_BULKOPERATION\n  ]\n});\n"], "mappings": "AAAA,OAAO,MAAMA,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAAC;EAC7CC,gBAAgB,EAAE,kBAAkB;EACpCC,yBAAyB,EAAE,2BAA2B;EACtDC,gCAAgC,EAAE,kCAAkC;EACpEC,wBAAwB,EAAE,0BAA0B;EACpDC,sBAAsB,EAAE,wBAAwB;EAChDC,MAAM,EAAE,QAAQ;EAChBC,eAAe,EAAE,iBAAiB;EAClCC,iBAAiB,EAAE,mBAAmB;EACtCC,sBAAsB,EAAE,wBAAwB;EAChDC,cAAc,EAAE,gBAAgB;EAChCC,oBAAoB,EAAE,sBAAsB;EAC5CC,YAAY,EAAE,cAAc;EAC5BC,MAAM,EAAE,QAAQ;EAChBC,4BAA4B,EAAE,uCAAuC;EACrEC,aAAa,EAAE,eAAe;EAC9BC,gBAAgB,EAAE,kBAAkB;EACpCC,wBAAwB,EAAE,0BAA0B;EACpDC,2BAA2B,EAAE,6BAA6B;EAC1DC,gBAAgB,EAAE,kBAAkB;EACpCC,0BAA0B,EAAE,4BAA4B;EACxDC,WAAW,EAAE,aAAa;EAC1BC,kBAAkB,EAAE,oBAAoB;EACxCC,qBAAqB,EAAE,uBAAuB;EAC9CC,uBAAuB,EAAE,yBAAyB;EAClDC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE,kBAAkB;EACpCC,WAAW,EAAE,aAAa;EAC1BC,cAAc,EAAE,gBAAgB;EAChCC,qBAAqB,EAAE,uBAAuB;EAC9CC,0BAA0B,EAAE,4BAA4B;EACxDC,mBAAmB,EAAE,qBAAqB;EAC1CC,SAAS,EAAE,WAAW;EACtBC,gBAAgB,EAAE,kBAAkB;EACpCC,mBAAmB,EAAE,qBAAqB;EAC1CC,KAAK,EAAE,OAAO;EACdC,YAAY,EAAE,cAAc;EAC5BC,UAAU,EAAE,YAAY;EACxBC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE,cAAc;EAC5BC,eAAe,EAAE,iBAAiB;EAClCC,sBAAsB,EAAE,wBAAwB;EAChDC,oBAAoB,EAAE,sBAAsB;EAC5CC,oBAAoB,EAAE,sBAAsB;EAC5CC,sBAAsB,EAAE,wBAAwB;EAChDC,uBAAuB,EAAE,yBAAyB;EAClDC,0BAA0B,EAAE,4BAA4B;EACxDC,QAAQ,EAAE,oBAAoB;EAC9BC,aAAa,EAAE,yBAAyB;EACxCC,WAAW,EAAE,aAAa;EAC1BC,eAAe,EAAE,iBAAiB;EAClCC,WAAW,EAAE,aAAa;EAC1BC,sBAAsB,EAAE,wBAAwB;EAChDC,mBAAmB,EAAE,qBAAqB;EAC1CC,4BAA4B,EAAE,8BAA8B;EAC5DC,wBAAwB,EAAE,0BAA0B;EACpDC,iCAAiC,EAAE,mCAAmC;EACtEC,gCAAgC,EAAE,kCAAkC;EACpEC,0BAA0B,EAAE,4BAA4B;EACxDC,uBAAuB,EAAE,yBAAyB;EAClDC,wCAAwC,EAAE,0CAA0C;EACpFC,sDAAsD,EAAE,wDAAwD;EAChHC,mDAAmD,EAAE,qDAAqD;EAC1GC,qCAAqC,EAAE,uCAAuC;EAC9EC,6CAA6C,EAAE,+CAA+C;EAC9FC,0CAA0C,EAAE,4CAA4C;EACxFC,oCAAoC,EAAE,sCAAsC;EAC5EC,uCAAuC,EAAE,yCAAyC;EAClFC,cAAc,EAAE,gBAAgB;EAChCC,sBAAsB,EAAE,wBAAwB;EAChDC,2BAA2B,EAAE,6BAA6B;EAC1DC,mCAAmC,EAAE,qCAAqC;EAC1EC,oCAAoC,EAAE,sCAAsC;EAC5EC,sBAAsB,EAAE,wBAAwB;EAChDC,2BAA2B,EAAE,6BAA6B;EAC1DC,yBAAyB,EAAE,2BAA2B;EACtDC,2BAA2B,EAAE,6BAA6B;EAC1DC,kCAAkC,EAAE,oCAAoC;EACxEC,gCAAgC,EAAE,kCAAkC;EACpEC,kCAAkC,EAAE,oCAAoC;EACxEC,6BAA6B,EAAE,+BAA+B;EAC9DC,WAAW,EAAE,aAAa;EAC1BC,YAAY,EAAE,cAAc;EAC5BC,UAAU,EAAE,YAAY;EACxBC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,WAAW,EAAE,aAAa;EAC1BC,cAAc,EAAE,gBAAgB;EAChCC,kBAAkB,EAAE,qBAAqB;EACzCC,uBAAuB,EAAE,0BAA0B;EACnDC,4BAA4B,EAAE,8BAA8B;EAC5DC,+BAA+B,EAAE,iCAAiC;EAClEC,uBAAuB,EAAE,yBAAyB;EAClDC,0BAA0B,EAAE,4BAA4B;EACxDC,sBAAsB,EAAE,wBAAwB;EAChDC,yBAAyB,EAAE,2BAA2B;EACtDC,6BAA6B,EAAE,+BAA+B;EAC9DC,gCAAgC,EAAE,kCAAkC;EACpEC,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE,YAAY;EACxBC,eAAe,EAAE,iBAAiB;EAClCC,oBAAoB,EAAE,sBAAsB;EAC5CC,KAAK,EAAE,OAAO;EACdC,eAAe,EAAE,iBAAiB;EAClCC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,cAAc;EAC5BC,SAAS,EAAE,WAAW;EACtBC,cAAc,EAAE,gBAAgB;EAChCC,gBAAgB,EAAE,kBAAkB;EACpCC,cAAc,EAAE,gBAAgB;EAChCC,gBAAgB,EAAE,kBAAkB;EACpCC,mBAAmB,EAAE,qBAAqB;EAC1CC,gBAAgB,EAAE,kBAAkB;EACpCC,mBAAmB,EAAE,qBAAqB;EAC1CC,wBAAwB,EAAE,0BAA0B;EACpDC,0BAA0B,EAAE,4BAA4B;EACxDC,wBAAwB,EAAE,0BAA0B;EACpDC,0BAA0B,EAAE,4BAA4B;EACxDC,6BAA6B,EAAE,+BAA+B;EAC9DC,kCAAkC,EAAE,oCAAoC;EACxEC,oCAAoC,EAAE,sCAAsC;EAC5EC,kCAAkC,EAAE,oCAAoC;EACxEC,oCAAoC,EAAE,sCAAsC;EAC5EC,0BAA0B,EAAE,4BAA4B;EACxDC,+BAA+B,EAAE,iCAAiC;EAClEC,iCAAiC,EAAE,mCAAmC;EACtEC,+BAA+B,EAAE,iCAAiC;EAClEC,iCAAiC,EAAE,mCAAmC;EACtEC,gCAAgC,EAAE,kCAAkC;EACpEC,qCAAqC,EAAE,uCAAuC;EAC9EC,uCAAuC,EAAE,yCAAyC;EAClFC,qCAAqC,EAAE,uCAAuC;EAC9EC,uCAAuC,EAAE,yCAAyC;EAClFC,2BAA2B,EAAE,6BAA6B;EAC1DC,gCAAgC,EAAE,kCAAkC;EACpEC,kCAAkC,EAAE,oCAAoC;EACxEC,gCAAgC,EAAE,kCAAkC;EACpEC,kCAAkC,EAAE,oCAAoC;EACxEC,2BAA2B,EAAE,6BAA6B;EAAE;EAC5DC,kCAAkC,EAAE,oCAAoC;EAAE;EAC1EC,gCAAgC,EAAE,kCAAkC;EAAE;EACtEC,yBAAyB,EAAE,2BAA2B;EAAE;EACxDC,iBAAiB,EAAE,mBAAmB;EACtCC,sBAAsB,EAAE,wBAAwB;EAChDC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,cAAc;EAC5BC,cAAc,EAAE,gBAAgB;EAChCC,aAAa,EAAE,eAAe;EAC9BC,WAAW,EAAE,aAAa;EAC1BC,eAAe,EAAE,iBAAiB;EAClCC,yBAAyB,EAAE,2BAA2B;EACtDC,oBAAoB,EAAE,iCAAiC;EACvDC,8BAA8B,EAAE,2CAA2C;EAC3EC,eAAe,EAAE,4BAA4B;EAC7CC,yBAAyB,EAAE,sCAAsC;EACjEC,WAAW,EAAE,mBAAmB;EAChCC,qBAAqB,EAAE,6BAA6B;EACpDC,kCAAkC,EAAE,oCAAoC;EACxEC,uCAAuC,EAAE,yCAAyC;EAClFC,uCAAuC,EAAE,yCAAyC;EAClFC,wBAAwB,EAAE,0BAA0B;EACpDC,6BAA6B,EAAE,+BAA+B;EAC9DC,6BAA6B,EAAE,+BAA+B;EAC9DC,4BAA4B,EAAE,mCAAmC;EACjEC,sCAAsC,EAAE,6CAA6C;EACrFC,kBAAkB,EAAE,yBAAyB;EAC7CC,4BAA4B,EAAE,mCAAmC;EACjEC,sBAAsB,EAAE,wBAAwB;EAChDC,2BAA2B,EAAE,6BAA6B;EAC1DC,sBAAsB,EAAE,wBAAwB;EAChDC,8BAA8B,EAAE,gCAAgC;EAChEC,mCAAmC,EAAE,qCAAqC;EAC1EC,2BAA2B,EAAE,6BAA6B;EAC1DC,gCAAgC,EAAE,kCAAkC;EACpEC,6BAA6B,EAAE,+BAA+B;EAC9DC,kCAAkC,EAAE,oCAAoC;EACxEC,0BAA0B,EAAE,4BAA4B;EACxDC,+BAA+B,EAAE,iCAAiC;EAClEC,oCAAoC,EAAE,uCAAuC;EAC7EC,uBAAuB,EAAE,0BAA0B;EACnDC,2BAA2B,EAAE,8BAA8B;EAC3DC,gCAAgC,EAAE,mCAAmC;EACrEC,gCAAgC,EAAE,mCAAmC;EACrEC,kCAAkC,EAAE,qCAAqC;EACzEC,qBAAqB,EAAE,uBAAuB;EAC9CC,mBAAmB,EAAE,qBAAqB;EAC1CC,mBAAmB,EAAE,qBAAqB;EAC1CC,qBAAqB,EAAE,uBAAuB;EAC9CC,YAAY,EAAE,cAAc;EAC5BC,iBAAiB,EAAE,mBAAmB;EACtCC,GAAG,EAAE,KAAK;EACVC,aAAa,EAAE,eAAe;EAC9BC,cAAc,EAAE,gBAAgB;EAChCC,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE,kBAAkB;EACpCC,kBAAkB,EAAE,oBAAoB;EACxCC,gBAAgB,EAAE,kBAAkB;EACpCC,kBAAkB,EAAE,oBAAoB;EACxCC,iBAAiB,EAAE,kBAAkB;EACrCC,2BAA2B,EAAE,4BAA4B;EACzDC,UAAU,EAAE,WAAW;EACvBC,eAAe,EAAE,gBAAgB;EACjCC,oBAAoB,EAAE,qBAAqB;EAC3CC,yBAAyB,EAAE,0BAA0B;EACrDC,eAAe,EAAE,gBAAgB;EACjCC,yBAAyB,EAAE,0BAA0B;EACrDC,iBAAiB,EAAE,kBAAkB;EACrCC,2BAA2B,EAAE,4BAA4B;EACzDC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,eAAe;EAC9BC,cAAc,EAAE,gBAAgB;EAChCC,0BAA0B,EAAE,4BAA4B;EACxDC,0BAA0B,EAAE,4BAA4B;EACxDC,gCAAgC,EAAE,kCAAkC;EACpEC,UAAU,EAAE,YAAY;EACxBC,0DAA0D,EACxD,4DAA4D;EAC9DC,gDAAgD,EAAE,kDAAkD;EACpGC,WAAW,EAAE,YAAY;EACzBC,qBAAqB,EAAE,sBAAsB;EAC7CC,yBAAyB,EAAE,2BAA2B;EACtDC,4BAA4B,EAAE,8BAA8B;EAC5DC,8BAA8B,EAAE,gCAAgC;EAChEC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,cAAc;EAC5BC,iBAAiB,EAAE,mBAAmB;EACtCC,sBAAsB,EAAE,wBAAwB;EAChDC,mBAAmB,EAAE,qBAAqB;EAC1CC,wBAAwB,EAAE,0BAA0B;EACpDC,sBAAsB,EAAE,wBAAwB;EAChDC,2BAA2B,EAAE,6BAA6B;EAC1DC,6BAA6B,EAAE,+BAA+B;EAC9DC,YAAY,EAAE,cAAc;EAC5BC,iBAAiB,EAAE,mBAAmB;EACtCC,mBAAmB,EAAE,qBAAqB;EAC1CC,OAAO,EAAE,SAAS;EAClBC,gBAAgB,EAAE,kBAAkB;EACpCC,qBAAqB,EAAE,uBAAuB;EAC9CC,eAAe,EAAE,kBAAkB;EACnCC,mBAAmB,EAAE,gCAAgC;EACrDC,sBAAsB,EAAE,mCAAmC;EAC3DC,4BAA4B,EAAE,+BAA+B;EAC7DC,oCAAoC,EAAE,uCAAuC;EAC7EC,qCAAqC,EAAE,wCAAwC;EAC/EC,kBAAkB,EAAE,qBAAqB;EACzCC,+BAA+B,EAAE,kCAAkC;EACnEC,uCAAuC,EAAE,0CAA0C;EACnFC,wCAAwC,EAAE,2CAA2C;EACrFC,YAAY,EAAE,cAAc;EAC5BC,iBAAiB,EAAE,mBAAmB;EACtCC,sBAAsB,EAAE,wBAAwB;EAChDC,2BAA2B,EAAE,6BAA6B;EAC1DC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,6BAA6B,EAAE,+BAA+B;EAC9DC,gBAAgB,EAAE,kBAAkB;EACpCC,UAAU,EAAE,YAAY;EACxBC,cAAc,EAAE;CACjB,CAAC;AAEF,OAAO,MAAMC,gBAAgB,GAAG3Q,MAAM,CAACC,MAAM,CAAC;EAC5C2Q,UAAU,EAAE,CACV7Q,iBAAiB,CAACsF,YAAY,CAC/B;EACD9E,MAAM,EAAE,CACNR,iBAAiB,CAACG,gBAAgB,EAClCH,iBAAiB,CAACI,yBAAyB,EAC3CJ,iBAAiB,CAACK,gCAAgC,EAClDL,iBAAiB,CAACM,wBAAwB,EAC1CN,iBAAiB,CAACQ,MAAM,EACxBR,iBAAiB,CAACS,eAAe,EACjCT,iBAAiB,CAACW,sBAAsB,EACxCX,iBAAiB,CAACY,cAAc,CACjC;EACDG,MAAM,EAAE,CACNf,iBAAiB,CAACe,MAAM,EACxBf,iBAAiB,CAACiB,aAAa,EAC/BjB,iBAAiB,CAACkB,gBAAgB,CACnC;EACDK,WAAW,EAAE,CACXvB,iBAAiB,CAACuB,WAAW,EAC7BvB,iBAAiB,CAACwB,kBAAkB,EACpCxB,iBAAiB,CAACyB,qBAAqB,CACxC;EACDE,IAAI,EAAE,CACJ3B,iBAAiB,CAAC2B,IAAI,EACtB3B,iBAAiB,CAAC+B,WAAW,EAC7B/B,iBAAiB,CAACgC,cAAc,CACjC;EACDI,SAAS,EAAE,CACTpC,iBAAiB,CAACoC,SAAS,EAC3BpC,iBAAiB,CAACqC,gBAAgB,EAClCrC,iBAAiB,CAACsC,mBAAmB,CACtC;EACDC,KAAK,EAAE,CACLvC,iBAAiB,CAACuC,KAAK,EACvBvC,iBAAiB,CAACyC,UAAU,EAC5BzC,iBAAiB,CAAC4C,eAAe,EACjC5C,iBAAiB,CAAC8C,oBAAoB,CACvC;EACDK,QAAQ,EAAE,CACRnD,iBAAiB,CAACmD,QAAQ,EAC1BnD,iBAAiB,CAACoD,aAAa,CAChC;EACD3C,eAAe,EAAE,CACfT,iBAAiB,CAACS,eAAe,EACjCT,iBAAiB,CAACW,sBAAsB,EACxCX,iBAAiB,CAACI,yBAAyB,EAC3CJ,iBAAiB,CAACK,gCAAgC,CACnD;EACDO,cAAc,EAAE,CACdZ,iBAAiB,CAACY,cAAc,EAChCZ,iBAAiB,CAACM,wBAAwB,CAC3C;EACDwQ,cAAc,EAAE,CACd9Q,iBAAiB,CAAC4J,YAAY,CAC/B;EACDmH,IAAI,EAAE,CACJ/Q,iBAAiB,CAACqD,WAAW,EAC7BrD,iBAAiB,CAACwE,cAAc,CACjC;EACDwM,WAAW,EAAE,CACXhR,iBAAiB,CAACgF,2BAA2B,EAC7ChF,iBAAiB,CAACiF,kCAAkC,CACrD;EACDgM,SAAS,EAAE,CACTjR,iBAAiB,CAACgF,2BAA2B,EAC7ChF,iBAAiB,CAACkF,gCAAgC,CACnD;EACDgM,iBAAiB,EAAE,CACjBlR,iBAAiB,CAACqD,WAAW,EAC7BrD,iBAAiB,CAAC0D,4BAA4B,CAC/C;EACDyN,YAAY,EAAE,CACZnR,iBAAiB,CAACyD,mBAAmB,EACrCzD,iBAAiB,CAACyE,sBAAsB,CACzC;EACD2M,gBAAgB,EAAE,CAChBpR,iBAAiB,CAAC+D,uBAAuB,EACzC/D,iBAAiB,CAAC8D,0BAA0B,CAC7C;EACDuN,8BAA8B,EAAE,CAC9BrR,iBAAiB,CAACmE,qCAAqC,EACvDnE,iBAAiB,CAACgE,wCAAwC,CAC3D;EACDsN,mCAAmC,EAAE,CACnCtR,iBAAiB,CAACqE,0CAA0C,EAC5DrE,iBAAiB,CAACoE,6CAA6C,CAChE;EACDmN,0CAA0C,EAAE,CAC1CvR,iBAAiB,CAACqE,0CAA0C,CAC7D;EACDmN,6CAA6C,EAAE,CAC7CxR,iBAAiB,CAACoE,6CAA6C,CAChE;EACDqN,YAAY,EAAE,CACZzR,iBAAiB,CAAC6E,sBAAsB,EACxC7E,iBAAiB,CAAC8E,2BAA2B,CAC9C;EACD4M,SAAS,EAAE,CACT1R,iBAAiB,CAAC6O,mBAAmB,EACrC7O,iBAAiB,CAAC8O,wBAAwB,CAC3C;EACDtJ,MAAM,EAAE,CACNxF,iBAAiB,CAACwF,MAAM,EACxBxF,iBAAiB,CAACyF,WAAW,CAC9B;EACDc,KAAK,EAAE,CACLvG,iBAAiB,CAACuG,KAAK,EACvBvG,iBAAiB,CAACwG,UAAU,EAC5BxG,iBAAiB,CAACyG,eAAe,EACjCzG,iBAAiB,CAAC0G,oBAAoB,CACvC;EACDC,KAAK,EAAE,CACL3G,iBAAiB,CAAC2G,KAAK,EACvB3G,iBAAiB,CAAC4G,eAAe,CAClC;EACDC,OAAO,EAAE,CACP7G,iBAAiB,CAAC6G,OAAO,EACzB7G,iBAAiB,CAAC8G,YAAY,EAC9B9G,iBAAiB,CAACmJ,iBAAiB,EACnCnJ,iBAAiB,CAACoJ,sBAAsB,CACzC;EACDrC,SAAS,EAAE,CACT/G,iBAAiB,CAAC+G,SAAS,EAC3B/G,iBAAiB,CAACsH,mBAAmB,CACtC;EACDN,cAAc,EAAE,CACdhH,iBAAiB,CAACgH,cAAc,EAChChH,iBAAiB,CAACuH,wBAAwB,CAC3C;EACDN,gBAAgB,EAAE,CAChBjH,iBAAiB,CAACiH,gBAAgB,EAClCjH,iBAAiB,CAACwH,0BAA0B,CAC7C;EACDN,cAAc,EAAE,CACdlH,iBAAiB,CAACkH,cAAc,EAChClH,iBAAiB,CAACyH,wBAAwB,CAC3C;EACDL,mBAAmB,EAAE,CACnBpH,iBAAiB,CAACoH,mBAAmB,EACrCpH,iBAAiB,CAAC2H,6BAA6B,CAChD;EACDN,gBAAgB,EAAE,CAChBrH,iBAAiB,CAACqH,gBAAgB,EAClCrH,iBAAiB,CAACgI,0BAA0B,CAC7C;EACD3G,gBAAgB,EAAE,CAChBrB,iBAAiB,CAACsB,0BAA0B,EAC5CtB,iBAAiB,CAACqB,gBAAgB,CACnC;EACDgI,IAAI,EAAE,CACJrJ,iBAAiB,CAACqM,mBAAmB,EACrCrM,iBAAiB,CAACoM,mBAAmB,EACrCpM,iBAAiB,CAACmM,qBAAqB,EACvCnM,iBAAiB,CAACsM,qBAAqB,CACxC;EACD5C,MAAM,EAAE,CACN1J,iBAAiB,CAAC0J,MAAM,CACzB;EACDnE,UAAU,EAAE,CACVvF,iBAAiB,CAACuF,UAAU,CAC7B;EACDF,WAAW,EAAE,CACXrF,iBAAiB,CAACqF,WAAW,CAC9B;EACDC,YAAY,EAAE,CACZtF,iBAAiB,CAACsF,YAAY,CAC/B;EACDqM,YAAY,EAAE,CACZ3R,iBAAiB,CAACgK,eAAe,EACjChK,iBAAiB,CAACiK,yBAAyB,CAC5C;EACD2H,iBAAiB,EAAE,CACjB5R,iBAAiB,CAACwK,kCAAkC,EACpDxK,iBAAiB,CAACyK,uCAAuC,EACzDzK,iBAAiB,CAAC0K,uCAAuC,EACzD1K,iBAAiB,CAAC2K,wBAAwB,EAC1C3K,iBAAiB,CAAC4K,6BAA6B,EAC/C5K,iBAAiB,CAAC6K,6BAA6B,CAChD;EACD4B,GAAG,EAAE,CACHzM,iBAAiB,CAAC0M,aAAa,EAC/B1M,iBAAiB,CAAC2M,cAAc,CACjC;EACDC,WAAW,EAAE,CACX5M,iBAAiB,CAAC6M,gBAAgB,EAClC7M,iBAAiB,CAAC8M,kBAAkB,EACpC9M,iBAAiB,CAAC+M,gBAAgB,EAClC/M,iBAAiB,CAACgN,kBAAkB,CACrC;EACD6E,eAAe,EAAE,CACf7R,iBAAiB,CAACwF,MAAM,EACxBxF,iBAAiB,CAAC2F,WAAW,CAC9B;EACDyH,eAAe,EAAE,CACfpN,iBAAiB,CAACmN,UAAU,EAC5BnN,iBAAiB,CAACoN,eAAe,EACjCpN,iBAAiB,CAACqN,oBAAoB,EACtCrN,iBAAiB,CAACsN,yBAAyB,CAC5C;EACDL,iBAAiB,EAAE,CACjBjN,iBAAiB,CAACmN,UAAU,EAC5BnN,iBAAiB,CAACiN,iBAAiB,EACnCjN,iBAAiB,CAACqN,oBAAoB,EACtCrN,iBAAiB,CAACkN,2BAA2B,CAC9C;EACDK,eAAe,EAAE,CACfvN,iBAAiB,CAACmN,UAAU,EAC5BnN,iBAAiB,CAACuN,eAAe,EACjCvN,iBAAiB,CAACqN,oBAAoB,EACtCrN,iBAAiB,CAACwN,yBAAyB,CAC5C;EACDC,iBAAiB,EAAE,CACjBzN,iBAAiB,CAACmN,UAAU,EAC5BnN,iBAAiB,CAACyN,iBAAiB,EACnCzN,iBAAiB,CAACqN,oBAAoB,EACtCrN,iBAAiB,CAAC0N,2BAA2B,CAC9C;EACDoE,iBAAiB,EAAE,CACjB9R,iBAAiB,CAAC2D,wBAAwB,CAC3C;EACDoO,0BAA0B,EAAE,CAC1B/R,iBAAiB,CAACwE,cAAc,EAChCxE,iBAAiB,CAAC0E,2BAA2B,EAC7C1E,iBAAiB,CAAC4E,oCAAoC,CACvD;EACDoN,yBAAyB,EAAE,CACzBhS,iBAAiB,CAACwE,cAAc,EAChCxE,iBAAiB,CAAC0E,2BAA2B,EAC7C1E,iBAAiB,CAAC2E,mCAAmC,CACtD;EACD8J,OAAO,EAAE,CACPzO,iBAAiB,CAACyO,OAAO,EACzBzO,iBAAiB,CAAC0O,YAAY,EAC9B1O,iBAAiB,CAAC2O,iBAAiB,EACnC3O,iBAAiB,CAAC4O,sBAAsB,CACzC;EACDqD,aAAa,EAAE,CACbjS,iBAAiB,CAAC+O,sBAAsB,EACxC/O,iBAAiB,CAACgP,2BAA2B,EAC7ChP,iBAAiB,CAACiP,6BAA6B,EAC/CjP,iBAAiB,CAACkP,YAAY,EAC9BlP,iBAAiB,CAACmP,iBAAiB,EACnCnP,iBAAiB,CAACoP,mBAAmB,CACtC;EACD8C,YAAY,EAAE,CACZlS,iBAAiB,CAACqP,OAAO,EACzBrP,iBAAiB,CAACsP,gBAAgB,EAClCtP,iBAAiB,CAACuP,qBAAqB,CACxC;EACDW,YAAY,EAAE,CACZlQ,iBAAiB,CAACkQ,YAAY,EAC9BlQ,iBAAiB,CAACmQ,iBAAiB,EACnCnQ,iBAAiB,CAACoQ,sBAAsB,EACxCpQ,iBAAiB,CAACqQ,2BAA2B,CAC9C;EACD8B,mBAAmB,EAAE,CACnBnS,iBAAiB,CAAC6L,oCAAoC,EACtD7L,iBAAiB,CAAC8L,uBAAuB;CAE5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}