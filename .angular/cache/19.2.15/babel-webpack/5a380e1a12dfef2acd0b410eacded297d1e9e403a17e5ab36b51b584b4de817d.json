{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _IdGenerator2;\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable } from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters = {};\n/** Service that generates unique IDs for DOM nodes. */\nclass _IdGenerator {\n  constructor() {\n    _defineProperty(this, \"_appId\", inject(APP_ID));\n  }\n  /**\n   * Generates a unique ID with a specific prefix.\n   * @param prefix Prefix to add to the ID.\n   */\n  getId(prefix) {\n    // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n    // Angular app on them, we can reduce the amount of breakages by not adding it.\n    if (this._appId !== 'ng') {\n      prefix += this._appId;\n    }\n    if (!counters.hasOwnProperty(prefix)) {\n      counters[prefix] = 0;\n    }\n    return `${prefix}${counters[prefix]++}`;\n  }\n}\n_IdGenerator2 = _IdGenerator;\n_defineProperty(_IdGenerator, \"\\u0275fac\", function _IdGenerator2_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _IdGenerator2)();\n});\n_defineProperty(_IdGenerator, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _IdGenerator2,\n  factory: _IdGenerator2.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_IdGenerator, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _IdGenerator as _ };", "map": {"version": 3, "names": ["i0", "inject", "APP_ID", "Injectable", "counters", "_IdGenerator", "constructor", "_defineProperty", "getId", "prefix", "_appId", "hasOwnProperty", "_IdGenerator2", "_IdGenerator2_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "_"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/id-generator-Dw_9dSDu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable } from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters = {};\n/** Service that generates unique IDs for DOM nodes. */\nclass _IdGenerator {\n    _appId = inject(APP_ID);\n    /**\n     * Generates a unique ID with a specific prefix.\n     * @param prefix Prefix to add to the ID.\n     */\n    getId(prefix) {\n        // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n        // Angular app on them, we can reduce the amount of breakages by not adding it.\n        if (this._appId !== 'ng') {\n            prefix += this._appId;\n        }\n        if (!counters.hasOwnProperty(prefix)) {\n            counters[prefix] = 0;\n        }\n        return `${prefix}${counters[prefix]++}`;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _IdGenerator, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _IdGenerator, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _IdGenerator, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nexport { _IdGenerator as _ };\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,QAAQ,eAAe;;AAE1D;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAG,CAAC,CAAC;AACnB;AACA,MAAMC,YAAY,CAAC;EAAAC,YAAA;IAAAC,eAAA,iBACNN,MAAM,CAACC,MAAM,CAAC;EAAA;EACvB;AACJ;AACA;AACA;EACIM,KAAKA,CAACC,MAAM,EAAE;IACV;IACA;IACA,IAAI,IAAI,CAACC,MAAM,KAAK,IAAI,EAAE;MACtBD,MAAM,IAAI,IAAI,CAACC,MAAM;IACzB;IACA,IAAI,CAACN,QAAQ,CAACO,cAAc,CAACF,MAAM,CAAC,EAAE;MAClCL,QAAQ,CAACK,MAAM,CAAC,GAAG,CAAC;IACxB;IACA,OAAO,GAAGA,MAAM,GAAGL,QAAQ,CAACK,MAAM,CAAC,EAAE,EAAE;EAC3C;AAGJ;AAACG,aAAA,GAnBKP,YAAY;AAAAE,eAAA,CAAZF,YAAY,wBAAAQ,sBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAiBqFT,aAAY;AAAA;AAAAE,eAAA,CAjB7GF,YAAY,+BAoB+DL,EAAE,CAAAe,kBAAA;EAAAC,KAAA,EAFwBX,aAAY;EAAAY,OAAA,EAAZZ,aAAY,CAAAa,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE3I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFpB,EAAE,CAAAqB,iBAAA,CAAQhB,YAAY,EAAc,CAAC;IAC1GiB,IAAI,EAAEnB,UAAU;IAChBoB,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,SAASd,YAAY,IAAImB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}