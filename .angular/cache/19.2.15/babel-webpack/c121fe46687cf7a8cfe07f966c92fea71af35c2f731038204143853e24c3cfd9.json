{"ast": null, "code": "import { action } from '@storybook/addon-actions';\nimport { SwuiCheckboxComponent } from './swui-checkbox.component';\nconst meta = {\n  title: 'Forms/Checkbox',\n  component: SwuiCheckboxComponent,\n  parameters: {\n    layout: 'centered'\n  },\n  tags: ['autodocs'],\n  argTypes: {\n    ngModelChange: {\n      action: 'ngModelChange'\n    }\n  }\n};\nexport default meta;\nexport const Default = {\n  args: {\n    ngModel: true,\n    ngModelChange: action('ngModelChange')\n  }\n};", "map": {"version": 3, "names": ["action", "SwuiCheckboxComponent", "meta", "title", "component", "parameters", "layout", "tags", "argTypes", "ngModelChange", "<PERSON><PERSON><PERSON>", "args", "ngModel"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-checkbox/swui-checkbox.stories.ts"], "sourcesContent": ["import { action } from '@storybook/addon-actions';\nimport { SwuiCheckboxComponent } from './swui-checkbox.component';\nconst meta = {\n    title: 'Forms/Checkbox',\n    component: SwuiCheckboxComponent,\n    parameters: {\n        layout: 'centered',\n    },\n    tags: ['autodocs'],\n    argTypes: {\n        ngModelChange: { action: 'ngModelChange' },\n    },\n};\nexport default meta;\nexport const Default = {\n    args: {\n        ngModel: true,\n        ngModelChange: action('ngModelChange'),\n    },\n};\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,0BAA0B;AACjD,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,MAAMC,IAAI,GAAG;EACTC,KAAK,EAAE,gBAAgB;EACvBC,SAAS,EAAEH,qBAAqB;EAChCI,UAAU,EAAE;IACRC,MAAM,EAAE;EACZ,CAAC;EACDC,IAAI,EAAE,CAAC,UAAU,CAAC;EAClBC,QAAQ,EAAE;IACNC,aAAa,EAAE;MAAET,MAAM,EAAE;IAAgB;EAC7C;AACJ,CAAC;AACD,eAAeE,IAAI;AACnB,OAAO,MAAMQ,OAAO,GAAG;EACnBC,IAAI,EAAE;IACFC,OAAO,EAAE,IAAI;IACbH,aAAa,EAAET,MAAM,CAAC,eAAe;EACzC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}