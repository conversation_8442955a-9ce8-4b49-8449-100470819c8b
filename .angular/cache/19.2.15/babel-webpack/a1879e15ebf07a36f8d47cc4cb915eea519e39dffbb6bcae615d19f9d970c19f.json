{"ast": null, "code": "import { SwuiCurrencySymbolPipe } from './swui-currency-symbol.pipe';\ndescribe('SwuiCurrencySymbolPipe', () => {\n  let pipe;\n  beforeEach(() => {\n    pipe = new SwuiCurrencySymbolPipe();\n  });\n  it('create an instance', () => {\n    expect(pipe).toBeTruthy();\n  });\n  it('should transform currency into symbol', () => {\n    expect(pipe.transform('USD')).toBe('$');\n  });\n  it('should return currency ig not found', () => {\n    expect(pipe.transform('Test')).toBe('Test');\n  });\n});", "map": {"version": 3, "names": ["SwuiCurrencySymbolPipe", "describe", "pipe", "beforeEach", "it", "expect", "toBeTruthy", "transform", "toBe"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/pipes/swui-currency-symbol/swui-currency-symbol.pipe.spec.ts"], "sourcesContent": ["import { SwuiCurrencySymbolPipe } from './swui-currency-symbol.pipe';\n\ndescribe('SwuiCurrencySymbolPipe', () => {\n  let pipe: SwuiCurrencySymbolPipe;\n\n  beforeEach(() => {\n    pipe = new SwuiCurrencySymbolPipe();\n  });\n\n  it('create an instance', () => {\n    expect(pipe).toBeTruthy();\n  });\n\n  it('should transform currency into symbol', () => {\n    expect(pipe.transform('USD')).toBe('$');\n  });\n\n  it('should return currency ig not found', () => {\n    expect(pipe.transform('Test')).toBe('Test');\n  });\n});\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,6BAA6B;AAEpEC,QAAQ,CAAC,wBAAwB,EAAE,MAAK;EACtC,IAAIC,IAA4B;EAEhCC,UAAU,CAAC,MAAK;IACdD,IAAI,GAAG,IAAIF,sBAAsB,EAAE;EACrC,CAAC,CAAC;EAEFI,EAAE,CAAC,oBAAoB,EAAE,MAAK;IAC5BC,MAAM,CAACH,IAAI,CAAC,CAACI,UAAU,EAAE;EAC3B,CAAC,CAAC;EAEFF,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/CC,MAAM,CAACH,IAAI,CAACK,SAAS,CAAC,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACzC,CAAC,CAAC;EAEFJ,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7CC,MAAM,CAACH,IAAI,CAACK,SAAS,CAAC,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;EAC7C,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}