{"ast": null, "code": "var _SwuiTdLinkWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./link.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./link.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdLinkWidgetComponent = (_SwuiTdLinkWidgetComponent = class SwuiTdLinkWidgetComponent {\n  constructor({\n    row,\n    schema,\n    value\n  }) {\n    var _schema$td, _schema$td2, _schema$td3, _schema$td4, _schema$td5;\n    const titleFn = (_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.titleFn;\n    const isDisabled = (_schema$td2 = schema.td) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.isDisabled;\n    const titlePostfixFn = (_schema$td3 = schema.td) === null || _schema$td3 === void 0 ? void 0 : _schema$td3.titlePostfixFn;\n    const linkFn = (_schema$td4 = schema.td) === null || _schema$td4 === void 0 ? void 0 : _schema$td4.linkFn;\n    if (schema.td && 'truncate' in schema.td) {\n      this.truncate = schema.td.truncate;\n    }\n    this.useTranslate = schema.td && 'useTranslate' in schema.td ? ((_schema$td5 = schema.td) === null || _schema$td5 === void 0 ? void 0 : _schema$td5.useTranslate) || false : true;\n    this.title = titleFn && titleFn(row, schema) || value;\n    this.disabled = isDisabled && isDisabled(row, schema) || false;\n    this.titlePostfix = titlePostfixFn && titlePostfixFn(row, schema) || '';\n    this.routerLinkData = linkFn && linkFn(row, schema) || [];\n  }\n}, _SwuiTdLinkWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdLinkWidgetComponent);\nSwuiTdLinkWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-link-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdLinkWidgetComponent);\nexport { SwuiTdLinkWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdLinkWidgetComponent", "_SwuiTdLinkWidgetComponent", "constructor", "row", "schema", "value", "_schema$td", "_schema$td2", "_schema$td3", "_schema$td4", "_schema$td5", "titleFn", "td", "isDisabled", "titlePostfixFn", "linkFn", "truncate", "useTranslate", "title", "disabled", "titlePostfix", "routerLinkData", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/link/link.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./link.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./link.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdLinkWidgetComponent = class SwuiTdLinkWidgetComponent {\n    constructor({ row, schema, value }) {\n        const titleFn = schema.td?.titleFn;\n        const isDisabled = schema.td?.isDisabled;\n        const titlePostfixFn = schema.td?.titlePostfixFn;\n        const linkFn = schema.td?.linkFn;\n        if (schema.td && 'truncate' in schema.td) {\n            this.truncate = schema.td.truncate;\n        }\n        this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;\n        this.title = (titleFn && titleFn(row, schema)) || value;\n        this.disabled = (isDisabled && isDisabled(row, schema)) || false;\n        this.titlePostfix = (titlePostfixFn && titlePostfixFn(row, schema)) || '';\n        this.routerLinkData = linkFn && linkFn(row, schema) || [];\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdLinkWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-link-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdLinkWidgetComponent);\nexport { SwuiTdLinkWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,CAAC;EAC5DE,WAAWA,CAAC;IAAEC,GAAG;IAAEC,MAAM;IAAEC;EAAM,CAAC,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;IAChC,MAAMC,OAAO,IAAAL,UAAA,GAAGF,MAAM,CAACQ,EAAE,cAAAN,UAAA,uBAATA,UAAA,CAAWK,OAAO;IAClC,MAAME,UAAU,IAAAN,WAAA,GAAGH,MAAM,CAACQ,EAAE,cAAAL,WAAA,uBAATA,WAAA,CAAWM,UAAU;IACxC,MAAMC,cAAc,IAAAN,WAAA,GAAGJ,MAAM,CAACQ,EAAE,cAAAJ,WAAA,uBAATA,WAAA,CAAWM,cAAc;IAChD,MAAMC,MAAM,IAAAN,WAAA,GAAGL,MAAM,CAACQ,EAAE,cAAAH,WAAA,uBAATA,WAAA,CAAWM,MAAM;IAChC,IAAIX,MAAM,CAACQ,EAAE,IAAI,UAAU,IAAIR,MAAM,CAACQ,EAAE,EAAE;MACtC,IAAI,CAACI,QAAQ,GAAGZ,MAAM,CAACQ,EAAE,CAACI,QAAQ;IACtC;IACA,IAAI,CAACC,YAAY,GAAGb,MAAM,CAACQ,EAAE,IAAI,cAAc,IAAIR,MAAM,CAACQ,EAAE,GAAG,EAAAF,WAAA,GAAAN,MAAM,CAACQ,EAAE,cAAAF,WAAA,uBAATA,WAAA,CAAWO,YAAY,KAAI,KAAK,GAAG,IAAI;IACtG,IAAI,CAACC,KAAK,GAAIP,OAAO,IAAIA,OAAO,CAACR,GAAG,EAAEC,MAAM,CAAC,IAAKC,KAAK;IACvD,IAAI,CAACc,QAAQ,GAAIN,UAAU,IAAIA,UAAU,CAACV,GAAG,EAAEC,MAAM,CAAC,IAAK,KAAK;IAChE,IAAI,CAACgB,YAAY,GAAIN,cAAc,IAAIA,cAAc,CAACX,GAAG,EAAEC,MAAM,CAAC,IAAK,EAAE;IACzE,IAAI,CAACiB,cAAc,GAAGN,MAAM,IAAIA,MAAM,CAACZ,GAAG,EAAEC,MAAM,CAAC,IAAI,EAAE;EAC7D;AAIJ,CAAC,EAHYH,0BAAA,CAAKqB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEzB,MAAM;IAAE4B,IAAI,EAAE,CAAC3B,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,0BAAA,CACJ;AACDD,yBAAyB,GAAGN,UAAU,CAAC,CACnCG,SAAS,CAAC;EACN8B,QAAQ,EAAE,yBAAyB;EACnCC,QAAQ,EAAEjC,oBAAoB;EAC9BkC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAClC,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEI,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}