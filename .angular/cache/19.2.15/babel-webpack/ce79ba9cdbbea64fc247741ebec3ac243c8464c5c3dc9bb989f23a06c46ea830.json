{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwHubEntityService;\nimport { __decorate } from \"tslib\";\nimport { Inject, Injectable, Optional } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { SwHubEntityDataSource } from './sw-hub-entity-data-source';\nfunction findEntity(entity, id) {\n  if (entity.id === id) {\n    return entity;\n  }\n  if (Array.isArray(entity.child)) {\n    let found = null;\n    entity.child.every(item => {\n      found = findEntity(item, id);\n      return !found;\n    });\n    return found;\n  }\n  return null;\n}\nlet SwHubEntityService = (_SwHubEntityService = class SwHubEntityService {\n  constructor(activatedRoute, service) {\n    this.activatedRoute = activatedRoute;\n    this.entities = [];\n    this.foundedEntities = [];\n    this.expandedEntities = new Map();\n    this.id$ = new BehaviorSubject(null);\n    this.brief$ = service ? service.getBrief() : of(null);\n    this.top$ = service ? service.getEntity() : of(null);\n    this.items$ = this.top$.pipe(map(entity => {\n      this.entities = this.convertStructure(entity);\n      this.entitiesObject = this.entities.reduce((res, item) => {\n        res[item.id] = item;\n        return res;\n      }, {});\n      return this.entities;\n    }));\n    this.itemSelected$ = combineLatest([this.id$, this.items$]).pipe(map(([id, items]) => {\n      if (items.length === 0) {\n        return null;\n      }\n      if (!id) {\n        return items[0] || null;\n      }\n      return items.find(item => item.id === id) || items[0] || null;\n    }));\n    this.entitySelected$ = combineLatest([this.top$, this.itemSelected$]).pipe(map(([entity, selected]) => {\n      if (selected && entity) {\n        return findEntity(entity, selected.id);\n      }\n      return null;\n    }));\n  }\n  convertStructure(structure) {\n    if (!structure) {\n      return [];\n    }\n    const entities = [];\n    this.convertItem(structure, entities, 0);\n    return entities;\n  }\n  convertItem(item, result, level, parent) {\n    const children = item.child ? [...item.child] : [];\n    const childrenIds = children.map(({\n      id\n    }) => id);\n    const currItem = _objectSpread(_objectSpread({}, item), {}, {\n      child: [],\n      level,\n      parentId: parent === null || parent === void 0 ? void 0 : parent.id,\n      children: childrenIds\n    });\n    currItem.parentId = parent === null || parent === void 0 ? void 0 : parent.id;\n    result.push(currItem);\n    if (children) {\n      children.forEach(child => this.convertItem(child, result, level + 1, currItem));\n    }\n  }\n  useByPath(entityId) {\n    this.items$.pipe(take(1), map(items => {\n      const {\n        path\n      } = this.activatedRoute.snapshot.queryParams;\n      if (!path) {\n        return entityId;\n      }\n      if (!items) {\n        return null;\n      }\n      const entity = items.find(item => item.path === path);\n      return (entity === null || entity === void 0 ? void 0 : entity.id) || null;\n    })).subscribe(id => {\n      this.id$.next(id);\n    });\n  }\n  use(entityId, force) {\n    this.entitySelected$.pipe(take(1)).subscribe(entity => {\n      if (!entity && !force || force && (entity === null || entity === void 0 ? void 0 : entity.id) !== entityId) {\n        this.id$.next(entityId);\n      }\n    });\n  }\n}, _SwHubEntityService.ctorParameters = () => [{\n  type: ActivatedRoute\n}, {\n  type: SwHubEntityDataSource,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [SwHubEntityDataSource]\n  }]\n}], _SwHubEntityService);\nSwHubEntityService = __decorate([Injectable()], SwHubEntityService);\nexport { SwHubEntityService };", "map": {"version": 3, "names": ["Inject", "Injectable", "Optional", "ActivatedRoute", "BehaviorSubject", "combineLatest", "of", "map", "take", "SwHubEntityDataSource", "findEntity", "entity", "id", "Array", "isArray", "child", "found", "every", "item", "SwHubEntityService", "_SwHubEntityService", "constructor", "activatedRoute", "service", "entities", "foundedEntities", "expandedEntities", "Map", "id$", "brief$", "getBrief", "top$", "getEntity", "items$", "pipe", "convertStructure", "entitiesObject", "reduce", "res", "itemSelected$", "items", "length", "find", "entitySelected$", "selected", "structure", "convertItem", "result", "level", "parent", "children", "childrenIds", "currItem", "_objectSpread", "parentId", "push", "for<PERSON>ach", "useByPath", "entityId", "path", "snapshot", "queryParams", "subscribe", "next", "use", "force", "type", "args", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-entity/sw-hub-entity.service.ts"], "sourcesContent": ["import { Inject, Injectable, Optional } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { SwHubEntityDataSource } from './sw-hub-entity-data-source';\nimport { SwHubBriefEntity, SwHubEntityItem, SwHubShortEntity } from './sw-hub-entity.model';\n\nexport interface ExtendedEntity extends SwHubEntityItem {\n  child: any;\n  parentId?: string;\n  children: string[];\n  level: number;\n}\n\nfunction findEntity( entity: SwHubShortEntity, id: string ): SwHubShortEntity | null {\n  if (entity.id === id) {\n    return entity;\n  }\n  if (Array.isArray(entity.child)) {\n    let found: SwHubShortEntity | null = null;\n    entity.child.every(item => {\n      found = findEntity(item, id);\n      return !found;\n    });\n    return found;\n  }\n  return null;\n}\n\n@Injectable()\nexport class SwHubEntityService {\n  readonly brief$: Observable<SwHubBriefEntity | null>;\n  readonly items$: Observable<SwHubEntityItem[]>;\n  readonly itemSelected$: Observable<SwHubEntityItem | null>;\n  readonly entitySelected$: Observable<SwHubShortEntity | null>;\n\n  entities: ExtendedEntity[] = [];\n  foundedEntities: ExtendedEntity[] = [];\n  entitiesObject: Record<string, any> | undefined;\n  expandedEntities = new Map<string, boolean>();\n\n  private readonly id$ = new BehaviorSubject<string | null>(null);\n  private readonly top$: Observable<SwHubShortEntity | null>;\n\n  constructor( private readonly activatedRoute: ActivatedRoute,\n               @Optional() @Inject(SwHubEntityDataSource) service: SwHubEntityDataSource | null\n  ) {\n    this.brief$ = service ? service.getBrief() : of(null);\n    this.top$ = service ? service.getEntity() : of(null);\n\n    this.items$ = this.top$.pipe(\n      map(entity => {\n        this.entities = this.convertStructure(entity);\n\n        this.entitiesObject = this.entities.reduce(( res: Record<string, ExtendedEntity>, item ) => {\n          res[item.id] = item;\n\n          return res;\n        }, {});\n\n        return this.entities;\n      })\n    );\n\n    this.itemSelected$ = combineLatest([this.id$, this.items$]).pipe(\n      map(( [id, items] ) => {\n        if (items.length === 0) {\n          return null;\n        }\n        if (!id) {\n          return items[0] || null;\n        }\n        return items.find(item => item.id === id) || items[0] || null;\n      })\n    );\n    this.entitySelected$ = combineLatest([this.top$, this.itemSelected$]).pipe(\n      map(( [entity, selected] ) => {\n        if (selected && entity) {\n          return findEntity(entity, selected.id);\n        }\n        return null;\n      })\n    );\n  }\n\n  convertStructure( structure: SwHubShortEntity | null ) {\n    if (!structure) {\n      return [];\n    }\n\n    const entities: ExtendedEntity[] = [];\n    this.convertItem(structure, entities, 0);\n\n    return entities;\n  }\n\n  convertItem( item: SwHubShortEntity, result: any[], level: number, parent?: { id: any; } | undefined ) {\n    const children = item.child ? [...item.child] : [];\n    const childrenIds = children.map(( { id } ) => id);\n\n    const currItem: ExtendedEntity = { ...item, child: [], level, parentId: parent?.id, children: childrenIds };\n    currItem.parentId = parent?.id;\n    result.push(currItem);\n    if (children) {\n      children.forEach(( child: any ) => this.convertItem(child, result, level + 1, currItem));\n    }\n  }\n\n  useByPath( entityId: string ) {\n    this.items$\n      .pipe(\n        take(1),\n        map(items => {\n          const { path } = this.activatedRoute.snapshot.queryParams;\n\n          if (!path) {\n            return entityId;\n          }\n\n          if (!items) {\n            return null;\n          }\n\n          const entity = items.find(item => item.path === path);\n\n          return entity?.id || null;\n        })\n      )\n      .subscribe(id => {\n        this.id$.next(id);\n      });\n  }\n\n  use( entityId: string, force?: boolean ) {\n    this.entitySelected$\n      .pipe(take(1))\n      .subscribe(entity => {\n        if ((!entity && !force) || (force && entity?.id !== entityId)) {\n          this.id$.next(entityId);\n        }\n      });\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,MAAM,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC5D,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,eAAe,EAAEC,aAAa,EAAcC,EAAE,QAAQ,MAAM;AACrE,SAASC,GAAG,EAAEC,IAAI,QAAQ,gBAAgB;AAC1C,SAASC,qBAAqB,QAAQ,6BAA6B;AAUnE,SAASC,UAAUA,CAAEC,MAAwB,EAAEC,EAAU;EACvD,IAAID,MAAM,CAACC,EAAE,KAAKA,EAAE,EAAE;IACpB,OAAOD,MAAM;EACf;EACA,IAAIE,KAAK,CAACC,OAAO,CAACH,MAAM,CAACI,KAAK,CAAC,EAAE;IAC/B,IAAIC,KAAK,GAA4B,IAAI;IACzCL,MAAM,CAACI,KAAK,CAACE,KAAK,CAACC,IAAI,IAAG;MACxBF,KAAK,GAAGN,UAAU,CAACQ,IAAI,EAAEN,EAAE,CAAC;MAC5B,OAAO,CAACI,KAAK;IACf,CAAC,CAAC;IACF,OAAOA,KAAK;EACd;EACA,OAAO,IAAI;AACb;AAGO,IAAMG,kBAAkB,IAAAC,mBAAA,GAAxB,MAAMD,kBAAkB;EAc7BE,YAA8BC,cAA8B,EACJC,OAAqC;IAD/D,KAAAD,cAAc,GAAdA,cAAc;IAR5C,KAAAE,QAAQ,GAAqB,EAAE;IAC/B,KAAAC,eAAe,GAAqB,EAAE;IAEtC,KAAAC,gBAAgB,GAAG,IAAIC,GAAG,EAAmB;IAE5B,KAAAC,GAAG,GAAG,IAAIxB,eAAe,CAAgB,IAAI,CAAC;IAM7D,IAAI,CAACyB,MAAM,GAAGN,OAAO,GAAGA,OAAO,CAACO,QAAQ,EAAE,GAAGxB,EAAE,CAAC,IAAI,CAAC;IACrD,IAAI,CAACyB,IAAI,GAAGR,OAAO,GAAGA,OAAO,CAACS,SAAS,EAAE,GAAG1B,EAAE,CAAC,IAAI,CAAC;IAEpD,IAAI,CAAC2B,MAAM,GAAG,IAAI,CAACF,IAAI,CAACG,IAAI,CAC1B3B,GAAG,CAACI,MAAM,IAAG;MACX,IAAI,CAACa,QAAQ,GAAG,IAAI,CAACW,gBAAgB,CAACxB,MAAM,CAAC;MAE7C,IAAI,CAACyB,cAAc,GAAG,IAAI,CAACZ,QAAQ,CAACa,MAAM,CAAC,CAAEC,GAAmC,EAAEpB,IAAI,KAAK;QACzFoB,GAAG,CAACpB,IAAI,CAACN,EAAE,CAAC,GAAGM,IAAI;QAEnB,OAAOoB,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC;MAEN,OAAO,IAAI,CAACd,QAAQ;IACtB,CAAC,CAAC,CACH;IAED,IAAI,CAACe,aAAa,GAAGlC,aAAa,CAAC,CAAC,IAAI,CAACuB,GAAG,EAAE,IAAI,CAACK,MAAM,CAAC,CAAC,CAACC,IAAI,CAC9D3B,GAAG,CAAC,CAAE,CAACK,EAAE,EAAE4B,KAAK,CAAC,KAAK;MACpB,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI;MACb;MACA,IAAI,CAAC7B,EAAE,EAAE;QACP,OAAO4B,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;MACzB;MACA,OAAOA,KAAK,CAACE,IAAI,CAACxB,IAAI,IAAIA,IAAI,CAACN,EAAE,KAAKA,EAAE,CAAC,IAAI4B,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAC/D,CAAC,CAAC,CACH;IACD,IAAI,CAACG,eAAe,GAAGtC,aAAa,CAAC,CAAC,IAAI,CAAC0B,IAAI,EAAE,IAAI,CAACQ,aAAa,CAAC,CAAC,CAACL,IAAI,CACxE3B,GAAG,CAAC,CAAE,CAACI,MAAM,EAAEiC,QAAQ,CAAC,KAAK;MAC3B,IAAIA,QAAQ,IAAIjC,MAAM,EAAE;QACtB,OAAOD,UAAU,CAACC,MAAM,EAAEiC,QAAQ,CAAChC,EAAE,CAAC;MACxC;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CACH;EACH;EAEAuB,gBAAgBA,CAAEU,SAAkC;IAClD,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,EAAE;IACX;IAEA,MAAMrB,QAAQ,GAAqB,EAAE;IACrC,IAAI,CAACsB,WAAW,CAACD,SAAS,EAAErB,QAAQ,EAAE,CAAC,CAAC;IAExC,OAAOA,QAAQ;EACjB;EAEAsB,WAAWA,CAAE5B,IAAsB,EAAE6B,MAAa,EAAEC,KAAa,EAAEC,MAAiC;IAClG,MAAMC,QAAQ,GAAGhC,IAAI,CAACH,KAAK,GAAG,CAAC,GAAGG,IAAI,CAACH,KAAK,CAAC,GAAG,EAAE;IAClD,MAAMoC,WAAW,GAAGD,QAAQ,CAAC3C,GAAG,CAAC,CAAE;MAAEK;IAAE,CAAE,KAAMA,EAAE,CAAC;IAElD,MAAMwC,QAAQ,GAAAC,aAAA,CAAAA,aAAA,KAAwBnC,IAAI;MAAEH,KAAK,EAAE,EAAE;MAAEiC,KAAK;MAAEM,QAAQ,EAAEL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAErC,EAAE;MAAEsC,QAAQ,EAAEC;IAAW,EAAE;IAC3GC,QAAQ,CAACE,QAAQ,GAAGL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAErC,EAAE;IAC9BmC,MAAM,CAACQ,IAAI,CAACH,QAAQ,CAAC;IACrB,IAAIF,QAAQ,EAAE;MACZA,QAAQ,CAACM,OAAO,CAAGzC,KAAU,IAAM,IAAI,CAAC+B,WAAW,CAAC/B,KAAK,EAAEgC,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEI,QAAQ,CAAC,CAAC;IAC1F;EACF;EAEAK,SAASA,CAAEC,QAAgB;IACzB,IAAI,CAACzB,MAAM,CACRC,IAAI,CACH1B,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACiC,KAAK,IAAG;MACV,MAAM;QAAEmB;MAAI,CAAE,GAAG,IAAI,CAACrC,cAAc,CAACsC,QAAQ,CAACC,WAAW;MAEzD,IAAI,CAACF,IAAI,EAAE;QACT,OAAOD,QAAQ;MACjB;MAEA,IAAI,CAAClB,KAAK,EAAE;QACV,OAAO,IAAI;MACb;MAEA,MAAM7B,MAAM,GAAG6B,KAAK,CAACE,IAAI,CAACxB,IAAI,IAAIA,IAAI,CAACyC,IAAI,KAAKA,IAAI,CAAC;MAErD,OAAO,CAAAhD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,EAAE,KAAI,IAAI;IAC3B,CAAC,CAAC,CACH,CACAkD,SAAS,CAAClD,EAAE,IAAG;MACd,IAAI,CAACgB,GAAG,CAACmC,IAAI,CAACnD,EAAE,CAAC;IACnB,CAAC,CAAC;EACN;EAEAoD,GAAGA,CAAEN,QAAgB,EAAEO,KAAe;IACpC,IAAI,CAACtB,eAAe,CACjBT,IAAI,CAAC1B,IAAI,CAAC,CAAC,CAAC,CAAC,CACbsD,SAAS,CAACnD,MAAM,IAAG;MAClB,IAAK,CAACA,MAAM,IAAI,CAACsD,KAAK,IAAMA,KAAK,IAAI,CAAAtD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,EAAE,MAAK8C,QAAS,EAAE;QAC7D,IAAI,CAAC9B,GAAG,CAACmC,IAAI,CAACL,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC;EACN;;;;;;UAhGcxD;EAAQ;IAAAgE,IAAA,EAAIlE,MAAM;IAAAmE,IAAA,GAAC1D,qBAAqB;EAAA;AAAA,E;AAf3CU,kBAAkB,GAAAiD,UAAA,EAD9BnE,UAAU,EAAE,C,EACAkB,kBAAkB,CAgH9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}