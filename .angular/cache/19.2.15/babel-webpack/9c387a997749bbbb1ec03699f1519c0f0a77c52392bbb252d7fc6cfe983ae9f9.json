{"ast": null, "code": "var _FilteredPipe;\nimport { __decorate } from \"tslib\";\nimport { Pi<PERSON> } from '@angular/core';\nimport { GamesSelectManagerService } from './games-select-manager.service';\nimport { gameSelectItemTypes } from './game-select-item/game-select-item-types';\nlet FilteredPipe = (_FilteredPipe = class FilteredPipe {\n  constructor(service) {\n    this.service = service;\n  }\n  transform(items, search, type) {\n    if (!items) {\n      return [];\n    }\n    if (!search) {\n      return items;\n    }\n    return type === gameSelectItemTypes.GAME ? this.service.filterGames(items, search) : this.service.filterLabels(items, search);\n  }\n}, _FilteredPipe.ctorParameters = () => [{\n  type: GamesSelectManagerService\n}], _FilteredPipe);\nFilteredPipe = __decorate([Pipe({\n  name: 'filtered',\n  standalone: false\n})], FilteredPipe);\nexport { FilteredPipe };", "map": {"version": 3, "names": ["__decorate", "<PERSON><PERSON>", "GamesSelectManagerService", "gameSelectItemTypes", "FilteredPipe", "_FilteredPipe", "constructor", "service", "transform", "items", "search", "type", "GAME", "filterGames", "filterLabels", "ctorParameters", "name", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/filtered.pipe.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { <PERSON><PERSON> } from '@angular/core';\nimport { GamesSelectManagerService } from './games-select-manager.service';\nimport { gameSelectItemTypes } from './game-select-item/game-select-item-types';\nlet FilteredPipe = class FilteredPipe {\n    constructor(service) {\n        this.service = service;\n    }\n    transform(items, search, type) {\n        if (!items) {\n            return [];\n        }\n        if (!search) {\n            return items;\n        }\n        return type === gameSelectItemTypes.GAME\n            ? this.service.filterGames(items, search)\n            : this.service.filterLabels(items, search);\n    }\n    static { this.ctorParameters = () => [\n        { type: GamesSelectManagerService }\n    ]; }\n};\nFilteredPipe = __decorate([\n    Pipe({\n        name: 'filtered',\n        standalone: false\n    })\n], FilteredPipe);\nexport { FilteredPipe };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,IAAI,QAAQ,eAAe;AACpC,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,mBAAmB,QAAQ,2CAA2C;AAC/E,IAAIC,YAAY,IAAAC,aAAA,GAAG,MAAMD,YAAY,CAAC;EAClCE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAC,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAE;IAC3B,IAAI,CAACF,KAAK,EAAE;MACR,OAAO,EAAE;IACb;IACA,IAAI,CAACC,MAAM,EAAE;MACT,OAAOD,KAAK;IAChB;IACA,OAAOE,IAAI,KAAKR,mBAAmB,CAACS,IAAI,GAClC,IAAI,CAACL,OAAO,CAACM,WAAW,CAACJ,KAAK,EAAEC,MAAM,CAAC,GACvC,IAAI,CAACH,OAAO,CAACO,YAAY,CAACL,KAAK,EAAEC,MAAM,CAAC;EAClD;AAIJ,CAAC,EAHYL,aAAA,CAAKU,cAAc,GAAG,MAAM,CACjC;EAAEJ,IAAI,EAAET;AAA0B,CAAC,CACtC,EAAAG,aAAA,CACJ;AACDD,YAAY,GAAGJ,UAAU,CAAC,CACtBC,IAAI,CAAC;EACDe,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEb,YAAY,CAAC;AAChB,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}