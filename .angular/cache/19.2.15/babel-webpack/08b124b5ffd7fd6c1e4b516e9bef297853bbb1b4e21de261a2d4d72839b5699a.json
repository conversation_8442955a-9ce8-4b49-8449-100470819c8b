{"ast": null, "code": "import { gameSelectItemTypes } from './game-select-item-types';\nexport class GameSelectItem {\n  constructor(obj) {\n    this.items = []; // for labels and intersections\n    this.checked = false;\n    if ('id' in obj) {\n      this.id = obj.id;\n    }\n    this.type = obj.type;\n    this.data = obj.data || {};\n    if (this.type === gameSelectItemTypes.INTERSECTION && obj.items) {\n      this.items = obj.items;\n    } else {\n      this.items = [];\n    }\n  }\n  get label() {\n    return this.data;\n  }\n  get gameInfo() {\n    return this.data;\n  }\n  get intersection() {\n    return this.data;\n  }\n  get title() {\n    if ('title' in this.data) {\n      return this.gameInfo.title;\n    }\n    return '';\n  }\n  get isLabel() {\n    return [gameSelectItemTypes.LABEL, gameSelectItemTypes.PROVIDER].indexOf(this.type) > -1;\n  }\n  toggleCheck() {\n    this.checked = !this.checked;\n  }\n  addGameToLabel(game) {\n    if (this.isLabel) {\n      this.items.push(game);\n    }\n  }\n  getGameLabels() {\n    if (this.type !== gameSelectItemTypes.GAME) {\n      return;\n    }\n    return this.gameInfo.labels;\n  }\n}\nexport function fromGameInfo(game) {\n  if (!game.labels.find(label => label.id === game.providerCode)) {\n    game.labels.push({\n      id: game.providerCode,\n      title: game.providerTitle,\n      group: 'provider'\n    }); // adding provider as label\n  }\n  if ('code' in game && 'providerCode' in game) {\n    return new GameSelectItem({\n      id: game.code,\n      type: gameSelectItemTypes.GAME,\n      data: game\n    });\n  } else {\n    return new GameSelectItem({\n      type: gameSelectItemTypes.CORRUPTION,\n      data: game\n    });\n  }\n}\nexport function createFromLabel(label) {\n  return new GameSelectItem({\n    id: label.id,\n    type: label.group === 'provider' ? gameSelectItemTypes.PROVIDER : gameSelectItemTypes.LABEL,\n    data: label\n  });\n}\nexport const getUniqueLabelGames = labels => {\n  let codes = []; // array of codes is required for determining unique games\n  return labels.reduce((result, {\n    items\n  }) => {\n    const labelGames = items.filter(item => {\n      return codes.indexOf(item.gameInfo.code) === -1;\n    });\n    codes = [...codes, ...labelGames.map(game => game.gameInfo.code)];\n    return [...result, ...labelGames];\n  }, []);\n};\nexport const getIntersectedGames = labels => {\n  let labelIds = labels.map(label => label.label.id);\n  return getUniqueLabelGames(labels).filter(game => labelIds.every(id => (game.gameInfo.labels || []).map(l => l.id).indexOf(id) > -1));\n};\nexport function createLabelIntersection(items) {\n  const intersectionItems = items.filter(item => item && item.isLabel);\n  return new GameSelectItem({\n    type: gameSelectItemTypes.INTERSECTION,\n    items: intersectionItems,\n    data: {\n      games: getIntersectedGames(intersectionItems)\n    }\n  });\n}\nexport function getLabelClass(item) {\n  const group = 'data' in item ? item.label.group : item.group;\n  switch (group) {\n    case 'platform':\n      return 'sw-chip-green';\n    case 'class':\n      return 'sw-chip-blue';\n    case 'feature':\n      return 'sw-chip-green';\n    default:\n      return '';\n  }\n}", "map": {"version": 3, "names": ["gameSelectItemTypes", "GameSelectItem", "constructor", "obj", "items", "checked", "id", "type", "data", "INTERSECTION", "label", "gameInfo", "intersection", "title", "isLabel", "LABEL", "PROVIDER", "indexOf", "to<PERSON><PERSON><PERSON><PERSON>", "addGameToLabel", "game", "push", "getGameLabels", "GAME", "labels", "fromGameInfo", "find", "providerCode", "providerTitle", "group", "code", "CORRUPTION", "createFromLabel", "getUniqueLabelGames", "codes", "reduce", "result", "labelGames", "filter", "item", "map", "getIntersectedGames", "labelIds", "every", "l", "createLabelIntersection", "intersectionItems", "games", "getLabelClass"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-item/game-select-item.model.ts"], "sourcesContent": ["import { gameSelectItemTypes } from './game-select-item-types';\nexport class GameSelectItem {\n    constructor(obj) {\n        this.items = []; // for labels and intersections\n        this.checked = false;\n        if ('id' in obj) {\n            this.id = obj.id;\n        }\n        this.type = obj.type;\n        this.data = obj.data || {};\n        if (this.type === gameSelectItemTypes.INTERSECTION && obj.items) {\n            this.items = obj.items;\n        }\n        else {\n            this.items = [];\n        }\n    }\n    get label() {\n        return this.data;\n    }\n    get gameInfo() {\n        return this.data;\n    }\n    get intersection() {\n        return this.data;\n    }\n    get title() {\n        if ('title' in this.data) {\n            return this.gameInfo.title;\n        }\n        return '';\n    }\n    get isLabel() {\n        return [\n            gameSelectItemTypes.LABEL,\n            gameSelectItemTypes.PROVIDER,\n        ].indexOf(this.type) > -1;\n    }\n    toggleCheck() {\n        this.checked = !this.checked;\n    }\n    addGameToLabel(game) {\n        if (this.isLabel) {\n            this.items.push(game);\n        }\n    }\n    getGameLabels() {\n        if (this.type !== gameSelectItemTypes.GAME) {\n            return;\n        }\n        return this.gameInfo.labels;\n    }\n}\nexport function fromGameInfo(game) {\n    if (!game.labels.find(label => label.id === game.providerCode)) {\n        game.labels.push({\n            id: game.providerCode,\n            title: game.providerTitle,\n            group: 'provider',\n        }); // adding provider as label\n    }\n    if ('code' in game && 'providerCode' in game) {\n        return new GameSelectItem({\n            id: game.code,\n            type: gameSelectItemTypes.GAME,\n            data: game,\n        });\n    }\n    else {\n        return new GameSelectItem({\n            type: gameSelectItemTypes.CORRUPTION,\n            data: game,\n        });\n    }\n}\nexport function createFromLabel(label) {\n    return new GameSelectItem({\n        id: label.id,\n        type: label.group === 'provider' ? gameSelectItemTypes.PROVIDER : gameSelectItemTypes.LABEL,\n        data: label,\n    });\n}\nexport const getUniqueLabelGames = (labels) => {\n    let codes = []; // array of codes is required for determining unique games\n    return labels.reduce((result, { items }) => {\n        const labelGames = items.filter(item => {\n            return codes.indexOf(item.gameInfo.code) === -1;\n        });\n        codes = [...codes, ...labelGames.map(game => game.gameInfo.code)];\n        return [...result, ...labelGames];\n    }, []);\n};\nexport const getIntersectedGames = (labels) => {\n    let labelIds = labels.map(label => label.label.id);\n    return getUniqueLabelGames(labels).filter(game => labelIds.every(id => (game.gameInfo.labels || []).map(l => l.id).indexOf(id) > -1));\n};\nexport function createLabelIntersection(items) {\n    const intersectionItems = items.filter(item => item && item.isLabel);\n    return new GameSelectItem({\n        type: gameSelectItemTypes.INTERSECTION,\n        items: intersectionItems,\n        data: {\n            games: getIntersectedGames(intersectionItems)\n        }\n    });\n}\nexport function getLabelClass(item) {\n    const group = 'data' in item ? item.label.group : item.group;\n    switch (group) {\n        case 'platform':\n            return 'sw-chip-green';\n        case 'class':\n            return 'sw-chip-blue';\n        case 'feature':\n            return 'sw-chip-green';\n        default:\n            return '';\n    }\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,GAAG,EAAE;IACb,IAAI,CAACC,KAAK,GAAG,EAAE,CAAC,CAAC;IACjB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,IAAI,IAAIF,GAAG,EAAE;MACb,IAAI,CAACG,EAAE,GAAGH,GAAG,CAACG,EAAE;IACpB;IACA,IAAI,CAACC,IAAI,GAAGJ,GAAG,CAACI,IAAI;IACpB,IAAI,CAACC,IAAI,GAAGL,GAAG,CAACK,IAAI,IAAI,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACD,IAAI,KAAKP,mBAAmB,CAACS,YAAY,IAAIN,GAAG,CAACC,KAAK,EAAE;MAC7D,IAAI,CAACA,KAAK,GAAGD,GAAG,CAACC,KAAK;IAC1B,CAAC,MACI;MACD,IAAI,CAACA,KAAK,GAAG,EAAE;IACnB;EACJ;EACA,IAAIM,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACF,IAAI;EACpB;EACA,IAAIG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACH,IAAI;EACpB;EACA,IAAII,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACJ,IAAI;EACpB;EACA,IAAIK,KAAKA,CAAA,EAAG;IACR,IAAI,OAAO,IAAI,IAAI,CAACL,IAAI,EAAE;MACtB,OAAO,IAAI,CAACG,QAAQ,CAACE,KAAK;IAC9B;IACA,OAAO,EAAE;EACb;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,CACHd,mBAAmB,CAACe,KAAK,EACzBf,mBAAmB,CAACgB,QAAQ,CAC/B,CAACC,OAAO,CAAC,IAAI,CAACV,IAAI,CAAC,GAAG,CAAC,CAAC;EAC7B;EACAW,WAAWA,CAAA,EAAG;IACV,IAAI,CAACb,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;EAChC;EACAc,cAAcA,CAACC,IAAI,EAAE;IACjB,IAAI,IAAI,CAACN,OAAO,EAAE;MACd,IAAI,CAACV,KAAK,CAACiB,IAAI,CAACD,IAAI,CAAC;IACzB;EACJ;EACAE,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACf,IAAI,KAAKP,mBAAmB,CAACuB,IAAI,EAAE;MACxC;IACJ;IACA,OAAO,IAAI,CAACZ,QAAQ,CAACa,MAAM;EAC/B;AACJ;AACA,OAAO,SAASC,YAAYA,CAACL,IAAI,EAAE;EAC/B,IAAI,CAACA,IAAI,CAACI,MAAM,CAACE,IAAI,CAAChB,KAAK,IAAIA,KAAK,CAACJ,EAAE,KAAKc,IAAI,CAACO,YAAY,CAAC,EAAE;IAC5DP,IAAI,CAACI,MAAM,CAACH,IAAI,CAAC;MACbf,EAAE,EAAEc,IAAI,CAACO,YAAY;MACrBd,KAAK,EAAEO,IAAI,CAACQ,aAAa;MACzBC,KAAK,EAAE;IACX,CAAC,CAAC,CAAC,CAAC;EACR;EACA,IAAI,MAAM,IAAIT,IAAI,IAAI,cAAc,IAAIA,IAAI,EAAE;IAC1C,OAAO,IAAInB,cAAc,CAAC;MACtBK,EAAE,EAAEc,IAAI,CAACU,IAAI;MACbvB,IAAI,EAAEP,mBAAmB,CAACuB,IAAI;MAC9Bf,IAAI,EAAEY;IACV,CAAC,CAAC;EACN,CAAC,MACI;IACD,OAAO,IAAInB,cAAc,CAAC;MACtBM,IAAI,EAAEP,mBAAmB,CAAC+B,UAAU;MACpCvB,IAAI,EAAEY;IACV,CAAC,CAAC;EACN;AACJ;AACA,OAAO,SAASY,eAAeA,CAACtB,KAAK,EAAE;EACnC,OAAO,IAAIT,cAAc,CAAC;IACtBK,EAAE,EAAEI,KAAK,CAACJ,EAAE;IACZC,IAAI,EAAEG,KAAK,CAACmB,KAAK,KAAK,UAAU,GAAG7B,mBAAmB,CAACgB,QAAQ,GAAGhB,mBAAmB,CAACe,KAAK;IAC3FP,IAAI,EAAEE;EACV,CAAC,CAAC;AACN;AACA,OAAO,MAAMuB,mBAAmB,GAAIT,MAAM,IAAK;EAC3C,IAAIU,KAAK,GAAG,EAAE,CAAC,CAAC;EAChB,OAAOV,MAAM,CAACW,MAAM,CAAC,CAACC,MAAM,EAAE;IAAEhC;EAAM,CAAC,KAAK;IACxC,MAAMiC,UAAU,GAAGjC,KAAK,CAACkC,MAAM,CAACC,IAAI,IAAI;MACpC,OAAOL,KAAK,CAACjB,OAAO,CAACsB,IAAI,CAAC5B,QAAQ,CAACmB,IAAI,CAAC,KAAK,CAAC,CAAC;IACnD,CAAC,CAAC;IACFI,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAE,GAAGG,UAAU,CAACG,GAAG,CAACpB,IAAI,IAAIA,IAAI,CAACT,QAAQ,CAACmB,IAAI,CAAC,CAAC;IACjE,OAAO,CAAC,GAAGM,MAAM,EAAE,GAAGC,UAAU,CAAC;EACrC,CAAC,EAAE,EAAE,CAAC;AACV,CAAC;AACD,OAAO,MAAMI,mBAAmB,GAAIjB,MAAM,IAAK;EAC3C,IAAIkB,QAAQ,GAAGlB,MAAM,CAACgB,GAAG,CAAC9B,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACJ,EAAE,CAAC;EAClD,OAAO2B,mBAAmB,CAACT,MAAM,CAAC,CAACc,MAAM,CAAClB,IAAI,IAAIsB,QAAQ,CAACC,KAAK,CAACrC,EAAE,IAAI,CAACc,IAAI,CAACT,QAAQ,CAACa,MAAM,IAAI,EAAE,EAAEgB,GAAG,CAACI,CAAC,IAAIA,CAAC,CAACtC,EAAE,CAAC,CAACW,OAAO,CAACX,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzI,CAAC;AACD,OAAO,SAASuC,uBAAuBA,CAACzC,KAAK,EAAE;EAC3C,MAAM0C,iBAAiB,GAAG1C,KAAK,CAACkC,MAAM,CAACC,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAACzB,OAAO,CAAC;EACpE,OAAO,IAAIb,cAAc,CAAC;IACtBM,IAAI,EAAEP,mBAAmB,CAACS,YAAY;IACtCL,KAAK,EAAE0C,iBAAiB;IACxBtC,IAAI,EAAE;MACFuC,KAAK,EAAEN,mBAAmB,CAACK,iBAAiB;IAChD;EACJ,CAAC,CAAC;AACN;AACA,OAAO,SAASE,aAAaA,CAACT,IAAI,EAAE;EAChC,MAAMV,KAAK,GAAG,MAAM,IAAIU,IAAI,GAAGA,IAAI,CAAC7B,KAAK,CAACmB,KAAK,GAAGU,IAAI,CAACV,KAAK;EAC5D,QAAQA,KAAK;IACT,KAAK,UAAU;MACX,OAAO,eAAe;IAC1B,KAAK,OAAO;MACR,OAAO,cAAc;IACzB,KAAK,SAAS;MACV,OAAO,eAAe;IAC1B;MACI,OAAO,EAAE;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}