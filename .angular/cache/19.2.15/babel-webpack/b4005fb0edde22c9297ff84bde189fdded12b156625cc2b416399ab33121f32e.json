{"ast": null, "code": "var _SwuiTopMenuComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-top-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-top-menu.component.scss?ngResource\";\nimport { Component, EventEmitter, Inject, Input, Output } from '@angular/core';\nimport { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubInitService } from '../services/sw-hub-init/sw-hub-init.service';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../services/sw-hub-init/sw-hub-init.token';\nlet SwuiTopMenuComponent = (_SwuiTopMenuComponent = class SwuiTopMenuComponent {\n  constructor({\n    logo,\n    logoSymbols\n  }, auth, hubService, configService) {\n    var _configService$logo$m, _configService$logo, _configService$logo$w, _configService$logo2;\n    this.auth = auth;\n    this.hubService = hubService;\n    this.configService = configService;\n    this.sidebarChanges = new EventEmitter();\n    this.settingsClick = new EventEmitter();\n    this.showSearch = false;\n    this.logo = (_configService$logo$m = (_configService$logo = configService.logo) === null || _configService$logo === void 0 ? void 0 : _configService$logo.main) !== null && _configService$logo$m !== void 0 ? _configService$logo$m : logo;\n    this.logoSymbols = (_configService$logo$w = (_configService$logo2 = configService.logo) === null || _configService$logo2 === void 0 ? void 0 : _configService$logo2.white) !== null && _configService$logo$w !== void 0 ? _configService$logo$w : logoSymbols;\n  }\n  get isLogged() {\n    return this.auth.isLogged();\n  }\n  get username() {\n    return this.auth.username;\n  }\n  get entityKey() {\n    return this.auth.entityKey;\n  }\n  get isSuperAdminOnly() {\n    return this.auth.isSuperAdmin;\n  }\n  get hasTwoFactor() {\n    return this.auth.isTwoFactor;\n  }\n  get config() {\n    return this.configService;\n  }\n  get envName() {\n    return this.config.envName ? this.config.envName.toLocaleUpperCase() : '-';\n  }\n  get locationName() {\n    return this.config.locationName ? this.config.locationName.toLocaleUpperCase() : '-';\n  }\n  selectLang(lang) {\n    this.hubService.sendLocale(lang);\n  }\n  setSettings(value) {\n    this.hubService.sendAppSettings(value);\n  }\n  onLogout() {\n    this.hubService.sendLogout();\n  }\n}, _SwuiTopMenuComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_HUB_MESSAGE_CONFIG]\n  }]\n}, {\n  type: SwHubAuthService\n}, {\n  type: SwHubInitService\n}, {\n  type: SwHubConfigService\n}], _SwuiTopMenuComponent.propDecorators = {\n  sidebarChanges: [{\n    type: Output\n  }],\n  settingsClick: [{\n    type: Output\n  }],\n  showSearch: [{\n    type: Input\n  }]\n}, _SwuiTopMenuComponent);\nSwuiTopMenuComponent = __decorate([Component({\n  selector: 'lib-swui-top-menu',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTopMenuComponent);\nexport { SwuiTopMenuComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "EventEmitter", "Inject", "Input", "Output", "SwHubAuthService", "SwHubConfigService", "SwHubInitService", "SWUI_HUB_MESSAGE_CONFIG", "SwuiTopMenuComponent", "_SwuiTopMenuComponent", "constructor", "logo", "logoSymbols", "auth", "hubService", "configService", "_configService$logo$m", "_configService$logo", "_configService$logo$w", "_configService$logo2", "sidebarChanges", "settingsClick", "showSearch", "main", "white", "isLogged", "username", "entityKey", "isSuperAdminOnly", "isSuperAdmin", "hasTwoFactor", "isTwoFactor", "config", "envName", "toLocaleUpperCase", "locationName", "selectLang", "lang", "sendLocale", "setSettings", "value", "sendAppSettings", "onLogout", "sendLogout", "ctorParameters", "type", "undefined", "decorators", "args", "propDecorators", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/swui-top-menu.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-top-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-top-menu.component.scss?ngResource\";\nimport { Component, EventEmitter, Inject, Input, Output } from '@angular/core';\nimport { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubInitService } from '../services/sw-hub-init/sw-hub-init.service';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../services/sw-hub-init/sw-hub-init.token';\nlet SwuiTopMenuComponent = class SwuiTopMenuComponent {\n    constructor({ logo, logoSymbols }, auth, hubService, configService) {\n        this.auth = auth;\n        this.hubService = hubService;\n        this.configService = configService;\n        this.sidebarChanges = new EventEmitter();\n        this.settingsClick = new EventEmitter();\n        this.showSearch = false;\n        this.logo = configService.logo?.main ?? logo;\n        this.logoSymbols = configService.logo?.white ?? logoSymbols;\n    }\n    get isLogged() {\n        return this.auth.isLogged();\n    }\n    get username() {\n        return this.auth.username;\n    }\n    get entityKey() {\n        return this.auth.entityKey;\n    }\n    get isSuperAdminOnly() {\n        return this.auth.isSuperAdmin;\n    }\n    get hasTwoFactor() {\n        return this.auth.isTwoFactor;\n    }\n    get config() {\n        return this.configService;\n    }\n    get envName() {\n        return this.config.envName ? this.config.envName.toLocaleUpperCase() : '-';\n    }\n    get locationName() {\n        return this.config.locationName ? this.config.locationName.toLocaleUpperCase() : '-';\n    }\n    selectLang(lang) {\n        this.hubService.sendLocale(lang);\n    }\n    setSettings(value) {\n        this.hubService.sendAppSettings(value);\n    }\n    onLogout() {\n        this.hubService.sendLogout();\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_HUB_MESSAGE_CONFIG,] }] },\n        { type: SwHubAuthService },\n        { type: SwHubInitService },\n        { type: SwHubConfigService }\n    ]; }\n    static { this.propDecorators = {\n        sidebarChanges: [{ type: Output }],\n        settingsClick: [{ type: Output }],\n        showSearch: [{ type: Input }]\n    }; }\n};\nSwuiTopMenuComponent = __decorate([\n    Component({\n        selector: 'lib-swui-top-menu',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTopMenuComponent);\nexport { SwuiTopMenuComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,SAASC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC9E,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,uBAAuB,QAAQ,2CAA2C;AACnF,IAAIC,oBAAoB,IAAAC,qBAAA,GAAG,MAAMD,oBAAoB,CAAC;EAClDE,WAAWA,CAAC;IAAEC,IAAI;IAAEC;EAAY,CAAC,EAAEC,IAAI,EAAEC,UAAU,EAAEC,aAAa,EAAE;IAAA,IAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,oBAAA;IAChE,IAAI,CAACN,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACK,cAAc,GAAG,IAAIpB,YAAY,CAAC,CAAC;IACxC,IAAI,CAACqB,aAAa,GAAG,IAAIrB,YAAY,CAAC,CAAC;IACvC,IAAI,CAACsB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACX,IAAI,IAAAK,qBAAA,IAAAC,mBAAA,GAAGF,aAAa,CAACJ,IAAI,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBM,IAAI,cAAAP,qBAAA,cAAAA,qBAAA,GAAIL,IAAI;IAC5C,IAAI,CAACC,WAAW,IAAAM,qBAAA,IAAAC,oBAAA,GAAGJ,aAAa,CAACJ,IAAI,cAAAQ,oBAAA,uBAAlBA,oBAAA,CAAoBK,KAAK,cAAAN,qBAAA,cAAAA,qBAAA,GAAIN,WAAW;EAC/D;EACA,IAAIa,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACZ,IAAI,CAACY,QAAQ,CAAC,CAAC;EAC/B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACb,IAAI,CAACa,QAAQ;EAC7B;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACd,IAAI,CAACc,SAAS;EAC9B;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACf,IAAI,CAACgB,YAAY;EACjC;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACjB,IAAI,CAACkB,WAAW;EAChC;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACjB,aAAa;EAC7B;EACA,IAAIkB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,MAAM,CAACC,OAAO,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO,CAACC,iBAAiB,CAAC,CAAC,GAAG,GAAG;EAC9E;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACH,MAAM,CAACG,YAAY,GAAG,IAAI,CAACH,MAAM,CAACG,YAAY,CAACD,iBAAiB,CAAC,CAAC,GAAG,GAAG;EACxF;EACAE,UAAUA,CAACC,IAAI,EAAE;IACb,IAAI,CAACvB,UAAU,CAACwB,UAAU,CAACD,IAAI,CAAC;EACpC;EACAE,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAAC1B,UAAU,CAAC2B,eAAe,CAACD,KAAK,CAAC;EAC1C;EACAE,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC5B,UAAU,CAAC6B,UAAU,CAAC,CAAC;EAChC;AAYJ,CAAC,EAXYlC,qBAAA,CAAKmC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAE5C,MAAM;IAAE+C,IAAI,EAAE,CAACzC,uBAAuB;EAAG,CAAC;AAAE,CAAC,EACrF;EAAEsC,IAAI,EAAEzC;AAAiB,CAAC,EAC1B;EAAEyC,IAAI,EAAEvC;AAAiB,CAAC,EAC1B;EAAEuC,IAAI,EAAExC;AAAmB,CAAC,CAC/B,EACQI,qBAAA,CAAKwC,cAAc,GAAG;EAC3B7B,cAAc,EAAE,CAAC;IAAEyB,IAAI,EAAE1C;EAAO,CAAC,CAAC;EAClCkB,aAAa,EAAE,CAAC;IAAEwB,IAAI,EAAE1C;EAAO,CAAC,CAAC;EACjCmB,UAAU,EAAE,CAAC;IAAEuB,IAAI,EAAE3C;EAAM,CAAC;AAChC,CAAC,EAAAO,qBAAA,CACJ;AACDD,oBAAoB,GAAGZ,UAAU,CAAC,CAC9BG,SAAS,CAAC;EACNmD,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAEtD,oBAAoB;EAC9BuD,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACvD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEU,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}