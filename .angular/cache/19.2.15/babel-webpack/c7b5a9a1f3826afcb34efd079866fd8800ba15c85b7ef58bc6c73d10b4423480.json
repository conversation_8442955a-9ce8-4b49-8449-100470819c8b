{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _Directionality;\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Injectable } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction DIR_DOCUMENT_FACTORY() {\n  return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n  var _navigator;\n  const value = (rawValue === null || rawValue === void 0 ? void 0 : rawValue.toLowerCase()) || '';\n  if (value === 'auto' && typeof navigator !== 'undefined' && (_navigator = navigator) !== null && _navigator !== void 0 && _navigator.language) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nclass Directionality {\n  constructor() {\n    /** The current 'ltr' or 'rtl' value. */\n    _defineProperty(this, \"value\", 'ltr');\n    /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n    _defineProperty(this, \"change\", new EventEmitter());\n    const _document = inject(DIR_DOCUMENT, {\n      optional: true\n    });\n    if (_document) {\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.value = _resolveDirectionality(bodyDir || htmlDir || 'ltr');\n    }\n  }\n  ngOnDestroy() {\n    this.change.complete();\n  }\n}\n_Directionality = Directionality;\n_defineProperty(Directionality, \"\\u0275fac\", function _Directionality_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Directionality)();\n});\n_defineProperty(Directionality, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _Directionality,\n  factory: _Directionality.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Directionality, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { Directionality as D, _resolveDirectionality as _, DIR_DOCUMENT as a };\n//# sourceMappingURL=directionality-CBXD4hga.mjs.map", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "EventEmitter", "Injectable", "DOCUMENT", "DIR_DOCUMENT", "providedIn", "factory", "DIR_DOCUMENT_FACTORY", "RTL_LOCALE_PATTERN", "_resolveDirectionality", "rawValue", "_navigator", "value", "toLowerCase", "navigator", "language", "test", "Directionality", "constructor", "_defineProperty", "_document", "optional", "bodyDir", "body", "dir", "htmlDir", "documentElement", "ngOnDestroy", "change", "complete", "_Directionality", "_Directionality_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "ɵfac", "ngDevMode", "ɵsetClassMetadata", "type", "args", "D", "_", "a"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/directionality-CBXD4hga.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Injectable } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n    providedIn: 'root',\n    factory: DIR_DOCUMENT_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction DIR_DOCUMENT_FACTORY() {\n    return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n    const value = rawValue?.toLowerCase() || '';\n    if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n        return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n    }\n    return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nclass Directionality {\n    /** The current 'ltr' or 'rtl' value. */\n    value = 'ltr';\n    /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n    change = new EventEmitter();\n    constructor() {\n        const _document = inject(DIR_DOCUMENT, { optional: true });\n        if (_document) {\n            const bodyDir = _document.body ? _document.body.dir : null;\n            const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n            this.value = _resolveDirectionality(bodyDir || htmlDir || 'ltr');\n        }\n    }\n    ngOnDestroy() {\n        this.change.complete();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Directionality, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Directionality, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Directionality, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nexport { Directionality as D, _resolveDirectionality as _, DIR_DOCUMENT as a };\n//# sourceMappingURL=directionality-CBXD4hga.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,QAAQ,eAAe;AAChF,SAASC,QAAQ,QAAQ,iBAAiB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,IAAIL,cAAc,CAAC,aAAa,EAAE;EACnDM,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,oBAAoBA,CAAA,EAAG;EAC5B,OAAOP,MAAM,CAACG,QAAQ,CAAC;AAC3B;;AAEA;AACA,MAAMK,kBAAkB,GAAG,oHAAoH;AAC/I;AACA,SAASC,sBAAsBA,CAACC,QAAQ,EAAE;EAAA,IAAAC,UAAA;EACtC,MAAMC,KAAK,GAAG,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,WAAW,CAAC,CAAC,KAAI,EAAE;EAC3C,IAAID,KAAK,KAAK,MAAM,IAAI,OAAOE,SAAS,KAAK,WAAW,KAAAH,UAAA,GAAIG,SAAS,cAAAH,UAAA,eAATA,UAAA,CAAWI,QAAQ,EAAE;IAC7E,OAAOP,kBAAkB,CAACQ,IAAI,CAACF,SAAS,CAACC,QAAQ,CAAC,GAAG,KAAK,GAAG,KAAK;EACtE;EACA,OAAOH,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;AAC1C;AACA;AACA;AACA;AACA;AACA,MAAMK,cAAc,CAAC;EAKjBC,WAAWA,CAAA,EAAG;IAJd;IAAAC,eAAA,gBACQ,KAAK;IACb;IAAAA,eAAA,iBACS,IAAIlB,YAAY,CAAC,CAAC;IAEvB,MAAMmB,SAAS,GAAGpB,MAAM,CAACI,YAAY,EAAE;MAAEiB,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC1D,IAAID,SAAS,EAAE;MACX,MAAME,OAAO,GAAGF,SAAS,CAACG,IAAI,GAAGH,SAAS,CAACG,IAAI,CAACC,GAAG,GAAG,IAAI;MAC1D,MAAMC,OAAO,GAAGL,SAAS,CAACM,eAAe,GAAGN,SAAS,CAACM,eAAe,CAACF,GAAG,GAAG,IAAI;MAChF,IAAI,CAACZ,KAAK,GAAGH,sBAAsB,CAACa,OAAO,IAAIG,OAAO,IAAI,KAAK,CAAC;IACpE;EACJ;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC;EAC1B;AAGJ;AAACC,eAAA,GAlBKb,cAAc;AAAAE,eAAA,CAAdF,cAAc,wBAAAc,wBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAgBmFf,eAAc;AAAA;AAAAE,eAAA,CAhB/GF,cAAc,+BAmB6DnB,EAAE,CAAAmC,kBAAA;EAAAC,KAAA,EAFwBjB,eAAc;EAAAX,OAAA,EAAdW,eAAc,CAAAkB,IAAA;EAAA9B,UAAA,EAAc;AAAM;AAE7I;EAAA,QAAA+B,SAAA,oBAAAA,SAAA,KAAiFtC,EAAE,CAAAuC,iBAAA,CAAQpB,cAAc,EAAc,CAAC;IAC5GqB,IAAI,EAAEpC,UAAU;IAChBqC,IAAI,EAAE,CAAC;MAAElC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASY,cAAc,IAAIuB,CAAC,EAAE/B,sBAAsB,IAAIgC,CAAC,EAAErC,YAAY,IAAIsC,CAAC;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}