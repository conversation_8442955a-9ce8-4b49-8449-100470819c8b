{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs-additional-colors.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../docs.component.scss?ngResource\";\nimport __NG_CLI_RESOURCE__2 from \"./docs-additional-colors.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsAdditionalColorsComponent = class DocsAdditionalColorsComponent {\n  constructor() {\n    this.colors = ['red', 'pink', 'purple', 'deep-purple', 'indigo', 'blue', 'light-blue', 'cyan', 'teal', 'green', 'light-green', 'lime', 'yellow', 'amber', 'orange', 'deep-orange', 'brown', 'grey', 'blue-grey'];\n  }\n};\nDocsAdditionalColorsComponent = __decorate([Component({\n  selector: 'lib-docs-additional-colors',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1, __NG_CLI_RESOURCE__2]\n})], DocsAdditionalColorsComponent);\nexport { DocsAdditionalColorsComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "__NG_CLI_RESOURCE__2", "Component", "DocsAdditionalColorsComponent", "constructor", "colors", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/styles/docs/docs-additional-colors/docs-additional-colors.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs-additional-colors.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../docs.component.scss?ngResource\";\nimport __NG_CLI_RESOURCE__2 from \"./docs-additional-colors.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsAdditionalColorsComponent = class DocsAdditionalColorsComponent {\n    constructor() {\n        this.colors = [\n            'red',\n            'pink',\n            'purple',\n            'deep-purple',\n            'indigo',\n            'blue',\n            'light-blue',\n            'cyan',\n            'teal',\n            'green',\n            'light-green',\n            'lime',\n            'yellow',\n            'amber',\n            'orange',\n            'deep-orange',\n            'brown',\n            'grey',\n            'blue-grey',\n        ];\n    }\n};\nDocsAdditionalColorsComponent = __decorate([\n    Component({\n        selector: 'lib-docs-additional-colors',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1, __NG_CLI_RESOURCE__2]\n    })\n], DocsAdditionalColorsComponent);\nexport { DocsAdditionalColorsComponent };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,oDAAoD;AACrF,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,oBAAoB,MAAM,oDAAoD;AACrF,SAASC,SAAS,QAAQ,eAAe;AACzC,IAAIC,6BAA6B,GAAG,MAAMA,6BAA6B,CAAC;EACpEC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CACV,KAAK,EACL,MAAM,EACN,QAAQ,EACR,aAAa,EACb,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,MAAM,EACN,MAAM,EACN,OAAO,EACP,aAAa,EACb,MAAM,EACN,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,aAAa,EACb,OAAO,EACP,MAAM,EACN,WAAW,CACd;EACL;AACJ,CAAC;AACDF,6BAA6B,GAAGL,UAAU,CAAC,CACvCI,SAAS,CAAC;EACNI,QAAQ,EAAE,4BAA4B;EACtCC,QAAQ,EAAER,oBAAoB;EAC9BS,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACT,oBAAoB,EAAEC,oBAAoB;AACvD,CAAC,CAAC,CACL,EAAEE,6BAA6B,CAAC;AACjC,SAASA,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}