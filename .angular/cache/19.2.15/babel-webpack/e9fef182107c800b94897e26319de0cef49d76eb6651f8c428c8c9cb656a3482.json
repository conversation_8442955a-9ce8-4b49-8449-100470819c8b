{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatProgressSpinner, _MatProgressSpinnerModule;\nconst _c0 = [\"determinateSpinner\"];\nfunction _MatProgressSpinner_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 11);\n    i0.ɵɵelement(1, \"circle\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"viewBox\", ctx_r0._viewBox());\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"stroke-dasharray\", ctx_r0._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx_r0._strokeCircumference() / 2, \"px\")(\"stroke-width\", ctx_r0._circleStrokeWidth(), \"%\");\n    i0.ɵɵattribute(\"r\", ctx_r0._circleRadius());\n  }\n}\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, ANIMATION_MODULE_TYPE, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, NgModule } from '@angular/core';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n  providedIn: 'root',\n  factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    diameter: BASE_SIZE\n  };\n}\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\nclass MatProgressSpinner {\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the progress spinner. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-spinner/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value) {\n    this._color = value;\n  }\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n    _defineProperty(this, \"_noopAnimations\", void 0);\n    _defineProperty(this, \"_color\", void 0);\n    _defineProperty(this, \"_defaultColor\", 'primary');\n    /** The element of the determinate spinner. */\n    _defineProperty(this, \"_determinateCircle\", void 0);\n    /**\n     * Mode of the progress bar.\n     *\n     * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n     * 'determinate'.\n     * Mirrored to mode attribute.\n     */\n    _defineProperty(this, \"mode\", void 0);\n    _defineProperty(this, \"_value\", 0);\n    _defineProperty(this, \"_diameter\", BASE_SIZE);\n    _defineProperty(this, \"_strokeWidth\", void 0);\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    const defaults = inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS);\n    this._noopAnimations = animationMode === 'NoopAnimations' && !!defaults && !defaults._forceAnimations;\n    this.mode = this._elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner' ? 'indeterminate' : 'determinate';\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n      if (defaults.diameter) {\n        this.diameter = defaults.diameter;\n      }\n      if (defaults.strokeWidth) {\n        this.strokeWidth = defaults.strokeWidth;\n      }\n    }\n  }\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  get value() {\n    return this.mode === 'determinate' ? this._value : 0;\n  }\n  set value(v) {\n    this._value = Math.max(0, Math.min(100, v || 0));\n  }\n  /** The diameter of the progress spinner (will set width and height of svg). */\n  get diameter() {\n    return this._diameter;\n  }\n  set diameter(size) {\n    this._diameter = size || 0;\n  }\n  /** Stroke width of the progress spinner. */\n  get strokeWidth() {\n    var _this$_strokeWidth;\n    return (_this$_strokeWidth = this._strokeWidth) !== null && _this$_strokeWidth !== void 0 ? _this$_strokeWidth : this.diameter / 10;\n  }\n  set strokeWidth(value) {\n    this._strokeWidth = value || 0;\n  }\n  /** The radius of the spinner, adjusted for stroke width. */\n  _circleRadius() {\n    return (this.diameter - BASE_STROKE_WIDTH) / 2;\n  }\n  /** The view box of the spinner's svg element. */\n  _viewBox() {\n    const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n    return `0 0 ${viewBox} ${viewBox}`;\n  }\n  /** The stroke circumference of the svg circle. */\n  _strokeCircumference() {\n    return 2 * Math.PI * this._circleRadius();\n  }\n  /** The dash offset of the svg circle. */\n  _strokeDashOffset() {\n    if (this.mode === 'determinate') {\n      return this._strokeCircumference() * (100 - this._value) / 100;\n    }\n    return null;\n  }\n  /** Stroke width of the circle in percent. */\n  _circleStrokeWidth() {\n    return this.strokeWidth / this.diameter * 100;\n  }\n}\n_MatProgressSpinner = MatProgressSpinner;\n_defineProperty(MatProgressSpinner, \"\\u0275fac\", function _MatProgressSpinner_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatProgressSpinner)();\n});\n_defineProperty(MatProgressSpinner, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatProgressSpinner,\n  selectors: [[\"mat-progress-spinner\"], [\"mat-spinner\"]],\n  viewQuery: function _MatProgressSpinner_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._determinateCircle = _t.first);\n    }\n  },\n  hostAttrs: [\"role\", \"progressbar\", \"tabindex\", \"-1\", 1, \"mat-mdc-progress-spinner\", \"mdc-circular-progress\"],\n  hostVars: 18,\n  hostBindings: function _MatProgressSpinner_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-valuemin\", 0)(\"aria-valuemax\", 100)(\"aria-valuenow\", ctx.mode === \"determinate\" ? ctx.value : null)(\"mode\", ctx.mode);\n      i0.ɵɵclassMap(\"mat-\" + ctx.color);\n      i0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\")(\"--mdc-circular-progress-size\", ctx.diameter + \"px\")(\"--mdc-circular-progress-active-indicator-width\", ctx.diameter + \"px\");\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._noopAnimations)(\"mdc-circular-progress--indeterminate\", ctx.mode === \"indeterminate\");\n    }\n  },\n  inputs: {\n    color: \"color\",\n    mode: \"mode\",\n    value: [2, \"value\", \"value\", numberAttribute],\n    diameter: [2, \"diameter\", \"diameter\", numberAttribute],\n    strokeWidth: [2, \"strokeWidth\", \"strokeWidth\", numberAttribute]\n  },\n  exportAs: [\"matProgressSpinner\"],\n  decls: 14,\n  vars: 11,\n  consts: [[\"circle\", \"\"], [\"determinateSpinner\", \"\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__determinate-container\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__determinate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\", 1, \"mdc-circular-progress__determinate-circle\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__indeterminate-container\"], [1, \"mdc-circular-progress__spinner-layer\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-left\"], [3, \"ngTemplateOutlet\"], [1, \"mdc-circular-progress__gap-patch\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-right\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__indeterminate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\"]],\n  template: function _MatProgressSpinner_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, _MatProgressSpinner_ng_template_0_Template, 2, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(2, \"div\", 2, 1);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(4, \"svg\", 3);\n      i0.ɵɵelement(5, \"circle\", 4);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n      i0.ɵɵelementContainer(9, 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(10, \"div\", 9);\n      i0.ɵɵelementContainer(11, 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"div\", 10);\n      i0.ɵɵelementContainer(13, 8);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      const circle_r2 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵattribute(\"viewBox\", ctx._viewBox());\n      i0.ɵɵadvance();\n      i0.ɵɵstyleProp(\"stroke-dasharray\", ctx._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx._strokeDashOffset(), \"px\")(\"stroke-width\", ctx._circleStrokeWidth(), \"%\");\n      i0.ɵɵattribute(\"r\", ctx._circleRadius());\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n    }\n  },\n  dependencies: [NgTemplateOutlet],\n  styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'mat-progress-spinner, mat-spinner',\n      exportAs: 'matProgressSpinner',\n      host: {\n        'role': 'progressbar',\n        'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n        // set tab index to -1 so screen readers will read the aria-label\n        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n        'tabindex': '-1',\n        '[class]': '\"mat-\" + color',\n        '[class._mat-animation-noopable]': `_noopAnimations`,\n        '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n        '[style.width.px]': 'diameter',\n        '[style.height.px]': 'diameter',\n        '[style.--mdc-circular-progress-size]': 'diameter + \"px\"',\n        '[style.--mdc-circular-progress-active-indicator-width]': 'diameter + \"px\"',\n        '[attr.aria-valuemin]': '0',\n        '[attr.aria-valuemax]': '100',\n        '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n        '[attr.mode]': 'mode'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [NgTemplateOutlet],\n      template: \"<ng-template #circle>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__indeterminate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeCircumference() / 2\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</ng-template>\\n\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-circular-progress__determinate-container\\\" aria-hidden=\\\"true\\\" #determinateSpinner>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__determinate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeDashOffset()\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            class=\\\"mdc-circular-progress__determinate-circle\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</div>\\n<!--TODO: figure out why there are 3 separate svgs-->\\n<div class=\\\"mdc-circular-progress__indeterminate-container\\\" aria-hidden=\\\"true\\\">\\n  <div class=\\\"mdc-circular-progress__spinner-layer\\\">\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__gap-patch\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    _determinateCircle: [{\n      type: ViewChild,\n      args: ['determinateSpinner']\n    }],\n    mode: [{\n      type: Input\n    }],\n    value: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    diameter: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    strokeWidth: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }]\n  });\n})();\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nconst MatSpinner = MatProgressSpinner;\nclass MatProgressSpinnerModule {}\n_MatProgressSpinnerModule = MatProgressSpinnerModule;\n_defineProperty(MatProgressSpinnerModule, \"\\u0275fac\", function _MatProgressSpinnerModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatProgressSpinnerModule)();\n});\n_defineProperty(MatProgressSpinnerModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatProgressSpinnerModule,\n  imports: [MatProgressSpinner, MatSpinner],\n  exports: [MatProgressSpinner, MatSpinner, MatCommonModule]\n}));\n_defineProperty(MatProgressSpinnerModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatProgressSpinner, MatSpinner],\n      exports: [MatProgressSpinner, MatSpinner, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };\n//# sourceMappingURL=progress-spinner.mjs.map", "map": {"version": 3, "names": ["i0", "ɵɵnamespaceSVG", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "rf", "ctx_r0", "ɵɵnextContext", "ɵɵattribute", "_viewBox", "ɵɵadvance", "ɵɵstyleProp", "_strokeCircumference", "_circleStrokeWidth", "_circleRadius", "InjectionToken", "inject", "ElementRef", "ANIMATION_MODULE_TYPE", "numberAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ViewChild", "NgModule", "NgTemplateOutlet", "M", "MatCommonModule", "MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY", "diameter", "BASE_SIZE", "BASE_STROKE_WIDTH", "MatProgressSpinner", "color", "_color", "_defaultColor", "value", "constructor", "_defineProperty", "animationMode", "optional", "defaults", "_noopAnimations", "_forceAnimations", "mode", "_elementRef", "nativeElement", "nodeName", "toLowerCase", "strokeWidth", "_value", "v", "Math", "max", "min", "_diameter", "size", "_this$_strokeWidth", "_strokeWidth", "viewBox", "PI", "_strokeDashOffset", "_MatProgressSpinner", "_MatProgressSpinner_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "_MatProgressSpinner_Query", "ctx", "ɵɵviewQuery", "_c0", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "_determinateCircle", "first", "hostAttrs", "hostVars", "hostBindings", "_MatProgressSpinner_HostBindings", "ɵɵclassMap", "ɵɵclassProp", "inputs", "exportAs", "decls", "vars", "consts", "template", "_MatProgressSpinner_Template", "ɵɵtemplate", "_MatProgressSpinner_ng_template_0_Template", "ɵɵtemplateRefExtractor", "ɵɵnamespaceHTML", "ɵɵelementContainer", "circle_r2", "ɵɵreference", "ɵɵproperty", "dependencies", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "imports", "transform", "<PERSON><PERSON><PERSON><PERSON>", "MatProgressSpinnerModule", "_MatProgressSpinnerModule", "_MatProgressSpinnerModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/progress-spinner.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, ANIMATION_MODULE_TYPE, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, NgModule } from '@angular/core';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n    providedIn: 'root',\n    factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n    return { diameter: BASE_SIZE };\n}\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\nclass MatProgressSpinner {\n    _elementRef = inject(ElementRef);\n    /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n    _noopAnimations;\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the progress spinner. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-spinner/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n        return this._color || this._defaultColor;\n    }\n    set color(value) {\n        this._color = value;\n    }\n    _color;\n    _defaultColor = 'primary';\n    /** The element of the determinate spinner. */\n    _determinateCircle;\n    constructor() {\n        const animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n        const defaults = inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS);\n        this._noopAnimations =\n            animationMode === 'NoopAnimations' && !!defaults && !defaults._forceAnimations;\n        this.mode =\n            this._elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner'\n                ? 'indeterminate'\n                : 'determinate';\n        if (defaults) {\n            if (defaults.color) {\n                this.color = this._defaultColor = defaults.color;\n            }\n            if (defaults.diameter) {\n                this.diameter = defaults.diameter;\n            }\n            if (defaults.strokeWidth) {\n                this.strokeWidth = defaults.strokeWidth;\n            }\n        }\n    }\n    /**\n     * Mode of the progress bar.\n     *\n     * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n     * 'determinate'.\n     * Mirrored to mode attribute.\n     */\n    mode;\n    /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n    get value() {\n        return this.mode === 'determinate' ? this._value : 0;\n    }\n    set value(v) {\n        this._value = Math.max(0, Math.min(100, v || 0));\n    }\n    _value = 0;\n    /** The diameter of the progress spinner (will set width and height of svg). */\n    get diameter() {\n        return this._diameter;\n    }\n    set diameter(size) {\n        this._diameter = size || 0;\n    }\n    _diameter = BASE_SIZE;\n    /** Stroke width of the progress spinner. */\n    get strokeWidth() {\n        return this._strokeWidth ?? this.diameter / 10;\n    }\n    set strokeWidth(value) {\n        this._strokeWidth = value || 0;\n    }\n    _strokeWidth;\n    /** The radius of the spinner, adjusted for stroke width. */\n    _circleRadius() {\n        return (this.diameter - BASE_STROKE_WIDTH) / 2;\n    }\n    /** The view box of the spinner's svg element. */\n    _viewBox() {\n        const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n        return `0 0 ${viewBox} ${viewBox}`;\n    }\n    /** The stroke circumference of the svg circle. */\n    _strokeCircumference() {\n        return 2 * Math.PI * this._circleRadius();\n    }\n    /** The dash offset of the svg circle. */\n    _strokeDashOffset() {\n        if (this.mode === 'determinate') {\n            return (this._strokeCircumference() * (100 - this._value)) / 100;\n        }\n        return null;\n    }\n    /** Stroke width of the circle in percent. */\n    _circleStrokeWidth() {\n        return (this.strokeWidth / this.diameter) * 100;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatProgressSpinner, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatProgressSpinner, isStandalone: true, selector: \"mat-progress-spinner, mat-spinner\", inputs: { color: \"color\", mode: \"mode\", value: [\"value\", \"value\", numberAttribute], diameter: [\"diameter\", \"diameter\", numberAttribute], strokeWidth: [\"strokeWidth\", \"strokeWidth\", numberAttribute] }, host: { attributes: { \"role\": \"progressbar\", \"tabindex\": \"-1\" }, properties: { \"class\": \"\\\"mat-\\\" + color\", \"class._mat-animation-noopable\": \"_noopAnimations\", \"class.mdc-circular-progress--indeterminate\": \"mode === \\\"indeterminate\\\"\", \"style.width.px\": \"diameter\", \"style.height.px\": \"diameter\", \"style.--mdc-circular-progress-size\": \"diameter + \\\"px\\\"\", \"style.--mdc-circular-progress-active-indicator-width\": \"diameter + \\\"px\\\"\", \"attr.aria-valuemin\": \"0\", \"attr.aria-valuemax\": \"100\", \"attr.aria-valuenow\": \"mode === \\\"determinate\\\" ? value : null\", \"attr.mode\": \"mode\" }, classAttribute: \"mat-mdc-progress-spinner mdc-circular-progress\" }, viewQueries: [{ propertyName: \"_determinateCircle\", first: true, predicate: [\"determinateSpinner\"], descendants: true }], exportAs: [\"matProgressSpinner\"], ngImport: i0, template: \"<ng-template #circle>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__indeterminate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeCircumference() / 2\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</ng-template>\\n\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-circular-progress__determinate-container\\\" aria-hidden=\\\"true\\\" #determinateSpinner>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__determinate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeDashOffset()\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            class=\\\"mdc-circular-progress__determinate-circle\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</div>\\n<!--TODO: figure out why there are 3 separate svgs-->\\n<div class=\\\"mdc-circular-progress__indeterminate-container\\\" aria-hidden=\\\"true\\\">\\n  <div class=\\\"mdc-circular-progress__spinner-layer\\\">\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__gap-patch\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"], dependencies: [{ kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatProgressSpinner, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-progress-spinner, mat-spinner', exportAs: 'matProgressSpinner', host: {\n                        'role': 'progressbar',\n                        'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n                        // set tab index to -1 so screen readers will read the aria-label\n                        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n                        'tabindex': '-1',\n                        '[class]': '\"mat-\" + color',\n                        '[class._mat-animation-noopable]': `_noopAnimations`,\n                        '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n                        '[style.width.px]': 'diameter',\n                        '[style.height.px]': 'diameter',\n                        '[style.--mdc-circular-progress-size]': 'diameter + \"px\"',\n                        '[style.--mdc-circular-progress-active-indicator-width]': 'diameter + \"px\"',\n                        '[attr.aria-valuemin]': '0',\n                        '[attr.aria-valuemax]': '100',\n                        '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n                        '[attr.mode]': 'mode',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, imports: [NgTemplateOutlet], template: \"<ng-template #circle>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__indeterminate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeCircumference() / 2\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</ng-template>\\n\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-circular-progress__determinate-container\\\" aria-hidden=\\\"true\\\" #determinateSpinner>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__determinate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeDashOffset()\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            class=\\\"mdc-circular-progress__determinate-circle\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</div>\\n<!--TODO: figure out why there are 3 separate svgs-->\\n<div class=\\\"mdc-circular-progress__indeterminate-container\\\" aria-hidden=\\\"true\\\">\\n  <div class=\\\"mdc-circular-progress__spinner-layer\\\">\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__gap-patch\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input\n            }], _determinateCircle: [{\n                type: ViewChild,\n                args: ['determinateSpinner']\n            }], mode: [{\n                type: Input\n            }], value: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], diameter: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], strokeWidth: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }] } });\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nconst MatSpinner = MatProgressSpinner;\n\nclass MatProgressSpinnerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatProgressSpinnerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatProgressSpinnerModule, imports: [MatProgressSpinner, MatSpinner], exports: [MatProgressSpinner, MatSpinner, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatProgressSpinnerModule, imports: [MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatProgressSpinnerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatProgressSpinner, MatSpinner],\n                    exports: [MatProgressSpinner, MatSpinner, MatCommonModule],\n                }]\n        }] });\n\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };\n//# sourceMappingURL=progress-spinner.mjs.map\n"], "mappings": ";;;;;IAkIiFA,EAAE,CAAAC,cAAA;IAAFD,EAAE,CAAAE,cAAA,aAF4xC,CAAC;IAE/xCF,EAAE,CAAAG,SAAA,gBAFqjD,CAAC;IAExjDH,EAAE,CAAAI,YAAA,CAF+jD,CAAC;EAAA;EAAA,IAAAC,EAAA;IAAA,MAAAC,MAAA,GAElkDN,EAAE,CAAAO,aAAA;IAAFP,EAAE,CAAAQ,WAAA,YAAAF,MAAA,CAAAG,QAAA;IAAFT,EAAE,CAAAU,SAAA,CAF04C,CAAC;IAE74CV,EAAE,CAAAW,WAAA,qBAAAL,MAAA,CAAAM,oBAAA,QAF04C,CAAC,sBAAAN,MAAA,CAAAM,oBAAA,YAAwE,CAAC,iBAAAN,MAAA,CAAAO,kBAAA,OAA4D,CAAC;IAEnhDb,EAAE,CAAAQ,WAAA,MAAAF,MAAA,CAAAQ,aAAA;EAAA;AAAA;AAlInF,OAAO,KAAKd,EAAE,MAAM,eAAe;AACnC,SAASe,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC7L,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;;AAE1B;AACA,MAAMC,oCAAoC,GAAG,IAAId,cAAc,CAAC,sCAAsC,EAAE;EACpGe,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,4CAA4CA,CAAA,EAAG;EACpD,OAAO;IAAEC,QAAQ,EAAEC;EAAU,CAAC;AAClC;AACA;AACA;AACA;AACA,MAAMA,SAAS,GAAG,GAAG;AACrB;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,EAAE;AAC5B,MAAMC,kBAAkB,CAAC;EAIrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM,IAAI,IAAI,CAACC,aAAa;EAC5C;EACA,IAAIF,KAAKA,CAACG,KAAK,EAAE;IACb,IAAI,CAACF,MAAM,GAAGE,KAAK;EACvB;EAKAC,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBArBA1B,MAAM,CAACC,UAAU,CAAC;IAChC;IAAAyB,eAAA;IAAAA,eAAA;IAAAA,eAAA,wBAiBgB,SAAS;IACzB;IAAAA,eAAA;IAuBA;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA;IAAAA,eAAA,iBAeS,CAAC;IAAAA,eAAA,oBAQER,SAAS;IAAAQ,eAAA;IA3CjB,MAAMC,aAAa,GAAG3B,MAAM,CAACE,qBAAqB,EAAE;MAAE0B,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,MAAMC,QAAQ,GAAG7B,MAAM,CAACa,oCAAoC,CAAC;IAC7D,IAAI,CAACiB,eAAe,GAChBH,aAAa,KAAK,gBAAgB,IAAI,CAAC,CAACE,QAAQ,IAAI,CAACA,QAAQ,CAACE,gBAAgB;IAClF,IAAI,CAACC,IAAI,GACL,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,aAAa,GACjE,eAAe,GACf,aAAa;IACvB,IAAIP,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACR,KAAK,EAAE;QAChB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACE,aAAa,GAAGM,QAAQ,CAACR,KAAK;MACpD;MACA,IAAIQ,QAAQ,CAACZ,QAAQ,EAAE;QACnB,IAAI,CAACA,QAAQ,GAAGY,QAAQ,CAACZ,QAAQ;MACrC;MACA,IAAIY,QAAQ,CAACQ,WAAW,EAAE;QACtB,IAAI,CAACA,WAAW,GAAGR,QAAQ,CAACQ,WAAW;MAC3C;IACJ;EACJ;EASA;EACA,IAAIb,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACQ,IAAI,KAAK,aAAa,GAAG,IAAI,CAACM,MAAM,GAAG,CAAC;EACxD;EACA,IAAId,KAAKA,CAACe,CAAC,EAAE;IACT,IAAI,CAACD,MAAM,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEH,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD;EAEA;EACA,IAAItB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC0B,SAAS;EACzB;EACA,IAAI1B,QAAQA,CAAC2B,IAAI,EAAE;IACf,IAAI,CAACD,SAAS,GAAGC,IAAI,IAAI,CAAC;EAC9B;EAEA;EACA,IAAIP,WAAWA,CAAA,EAAG;IAAA,IAAAQ,kBAAA;IACd,QAAAA,kBAAA,GAAO,IAAI,CAACC,YAAY,cAAAD,kBAAA,cAAAA,kBAAA,GAAI,IAAI,CAAC5B,QAAQ,GAAG,EAAE;EAClD;EACA,IAAIoB,WAAWA,CAACb,KAAK,EAAE;IACnB,IAAI,CAACsB,YAAY,GAAGtB,KAAK,IAAI,CAAC;EAClC;EAEA;EACA1B,aAAaA,CAAA,EAAG;IACZ,OAAO,CAAC,IAAI,CAACmB,QAAQ,GAAGE,iBAAiB,IAAI,CAAC;EAClD;EACA;EACA1B,QAAQA,CAAA,EAAG;IACP,MAAMsD,OAAO,GAAG,IAAI,CAACjD,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACuC,WAAW;IAC3D,OAAO,OAAOU,OAAO,IAAIA,OAAO,EAAE;EACtC;EACA;EACAnD,oBAAoBA,CAAA,EAAG;IACnB,OAAO,CAAC,GAAG4C,IAAI,CAACQ,EAAE,GAAG,IAAI,CAAClD,aAAa,CAAC,CAAC;EAC7C;EACA;EACAmD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACjB,IAAI,KAAK,aAAa,EAAE;MAC7B,OAAQ,IAAI,CAACpC,oBAAoB,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC0C,MAAM,CAAC,GAAI,GAAG;IACpE;IACA,OAAO,IAAI;EACf;EACA;EACAzC,kBAAkBA,CAAA,EAAG;IACjB,OAAQ,IAAI,CAACwC,WAAW,GAAG,IAAI,CAACpB,QAAQ,GAAI,GAAG;EACnD;AAGJ;AAACiC,mBAAA,GArGK9B,kBAAkB;AAAAM,eAAA,CAAlBN,kBAAkB,wBAAA+B,4BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAmG+EhC,mBAAkB;AAAA;AAAAM,eAAA,CAnGnHN,kBAAkB,8BAsGyDpC,EAAE,CAAAqE,iBAAA;EAAAC,IAAA,EAFQlC,mBAAkB;EAAAmC,SAAA;EAAAC,SAAA,WAAAC,0BAAApE,EAAA,EAAAqE,GAAA;IAAA,IAAArE,EAAA;MAE5BL,EAAE,CAAA2E,WAAA,CAAAC,GAAA;IAAA;IAAA,IAAAvE,EAAA;MAAA,IAAAwE,EAAA;MAAF7E,EAAE,CAAA8E,cAAA,CAAAD,EAAA,GAAF7E,EAAE,CAAA+E,WAAA,QAAAL,GAAA,CAAAM,kBAAA,GAAAH,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAAC,SAAA,WAFsU,aAAa,cAAc,IAAI;EAAAC,QAAA;EAAAC,YAAA,WAAAC,iCAAAhF,EAAA,EAAAqE,GAAA;IAAA,IAAArE,EAAA;MAEvWL,EAAE,CAAAQ,WAAA,kBAFQ,CAAC,mBAAD,GAAG,mBAAAkE,GAAA,CAAA1B,IAAA,KAAM,aAAa,GAAA0B,GAAA,CAAAlC,KAAA,GAAW,IAAI,UAAAkC,GAAA,CAAA1B,IAAA;MAE/ChD,EAAE,CAAAsF,UAAA,CAFQ,MAAM,GAAAZ,GAAA,CAAArC,KAAW,CAAC;MAE5BrC,EAAE,CAAAW,WAAA,UAAA+D,GAAA,CAAAzC,QAAA,MAFyB,CAAC,WAAAyC,GAAA,CAAAzC,QAAA,MAAD,CAAC,iCAAAyC,GAAA,CAAAzC,QAAA,GAAP,IAAM,CAAC,mDAAAyC,GAAA,CAAAzC,QAAA,GAAP,IAAM,CAAC;MAE5BjC,EAAE,CAAAuF,WAAA,4BAAAb,GAAA,CAAA5B,eAFyB,CAAC,yCAAA4B,GAAA,CAAA1B,IAAA,KAAT,eAAQ,CAAC;IAAA;EAAA;EAAAwC,MAAA;IAAAnD,KAAA;IAAAW,IAAA;IAAAR,KAAA,wBAAuIrB,eAAe;IAAAc,QAAA,8BAAsCd,eAAe;IAAAkC,WAAA,oCAA+ClC,eAAe;EAAA;EAAAsE,QAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,6BAAAzF,EAAA,EAAAqE,GAAA;IAAA,IAAArE,EAAA;MAErSL,EAAE,CAAA+F,UAAA,IAAAC,0CAAA,gCAAFhG,EAAE,CAAAiG,sBAFsnC,CAAC;MAEznCjG,EAAE,CAAAE,cAAA,eAFy2D,CAAC;MAE52DF,EAAE,CAAAC,cAAA;MAAFD,EAAE,CAAAE,cAAA,YAF6gE,CAAC;MAEhhEF,EAAE,CAAAG,SAAA,eAFg2E,CAAC;MAEn2EH,EAAE,CAAAI,YAAA,CAF02E,CAAC,CAAO,CAAC;MAEr3EJ,EAAE,CAAAkG,eAAA;MAAFlG,EAAE,CAAAE,cAAA,YAF8/E,CAAC,YAAuD,CAAC,YAA6F,CAAC;MAEvpFF,EAAE,CAAAmG,kBAAA,KAFutF,CAAC;MAE1tFnG,EAAE,CAAAI,YAAA,CAFmuF,CAAC;MAEtuFJ,EAAE,CAAAE,cAAA,aAFyxF,CAAC;MAE5xFF,EAAE,CAAAmG,kBAAA,MAF41F,CAAC;MAE/1FnG,EAAE,CAAAI,YAAA,CAFw2F,CAAC;MAE32FJ,EAAE,CAAAE,cAAA,cAFu8F,CAAC;MAE18FF,EAAE,CAAAmG,kBAAA,MAF0gG,CAAC;MAE7gGnG,EAAE,CAAAI,YAAA,CAFshG,CAAC,CAAS,CAAC,CAAO,CAAC;IAAA;IAAA,IAAAC,EAAA;MAAA,MAAA+F,SAAA,GAE3iGpG,EAAE,CAAAqG,WAAA;MAAFrG,EAAE,CAAAU,SAAA,EAF+4D,CAAC;MAEl5DV,EAAE,CAAAQ,WAAA,YAAAkE,GAAA,CAAAjE,QAAA;MAAFT,EAAE,CAAAU,SAAA,CAF2nE,CAAC;MAE9nEV,EAAE,CAAAW,WAAA,qBAAA+D,GAAA,CAAA9D,oBAAA,QAF2nE,CAAC,sBAAA8D,GAAA,CAAAT,iBAAA,QAAiE,CAAC,iBAAAS,GAAA,CAAA7D,kBAAA,OAA4D,CAAC;MAE7vEb,EAAE,CAAAQ,WAAA,MAAAkE,GAAA,CAAA5D,aAAA;MAAFd,EAAE,CAAAU,SAAA,EAFusF,CAAC;MAE1sFV,EAAE,CAAAsG,UAAA,qBAAAF,SAFusF,CAAC;MAE1sFpG,EAAE,CAAAU,SAAA,EAF40F,CAAC;MAE/0FV,EAAE,CAAAsG,UAAA,qBAAAF,SAF40F,CAAC;MAE/0FpG,EAAE,CAAAU,SAAA,EAF0/F,CAAC;MAE7/FV,EAAE,CAAAsG,UAAA,qBAAAF,SAF0/F,CAAC;IAAA;EAAA;EAAAG,YAAA,GAA4xI7E,gBAAgB;EAAA8E,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAE13O;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF3G,EAAE,CAAA4G,iBAAA,CAAQxE,kBAAkB,EAAc,CAAC;IAChHkC,IAAI,EAAElD,SAAS;IACfyF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mCAAmC;MAAErB,QAAQ,EAAE,oBAAoB;MAAEsB,IAAI,EAAE;QAClF,MAAM,EAAE,aAAa;QACrB,OAAO,EAAE,gDAAgD;QACzD;QACA;QACA,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,gBAAgB;QAC3B,iCAAiC,EAAE,iBAAiB;QACpD,8CAA8C,EAAE,0BAA0B;QAC1E,kBAAkB,EAAE,UAAU;QAC9B,mBAAmB,EAAE,UAAU;QAC/B,sCAAsC,EAAE,iBAAiB;QACzD,wDAAwD,EAAE,iBAAiB;QAC3E,sBAAsB,EAAE,GAAG;QAC3B,sBAAsB,EAAE,KAAK;QAC7B,sBAAsB,EAAE,uCAAuC;QAC/D,aAAa,EAAE;MACnB,CAAC;MAAEL,eAAe,EAAErF,uBAAuB,CAAC2F,MAAM;MAAEP,aAAa,EAAEnF,iBAAiB,CAAC2F,IAAI;MAAEC,OAAO,EAAE,CAACxF,gBAAgB,CAAC;MAAEmE,QAAQ,EAAE,28DAA28D;MAAEW,MAAM,EAAE,CAAC,mrIAAmrI;IAAE,CAAC;EAC1xM,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEnE,KAAK,EAAE,CAAC;MAChDiC,IAAI,EAAE/C;IACV,CAAC,CAAC;IAAEyD,kBAAkB,EAAE,CAAC;MACrBV,IAAI,EAAE9C,SAAS;MACfqF,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE7D,IAAI,EAAE,CAAC;MACPsB,IAAI,EAAE/C;IACV,CAAC,CAAC;IAAEiB,KAAK,EAAE,CAAC;MACR8B,IAAI,EAAE/C,KAAK;MACXsF,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEhG;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEc,QAAQ,EAAE,CAAC;MACXqC,IAAI,EAAE/C,KAAK;MACXsF,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEhG;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEkC,WAAW,EAAE,CAAC;MACdiB,IAAI,EAAE/C,KAAK;MACXsF,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEhG;MAAgB,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiG,UAAU,GAAGhF,kBAAkB;AAErC,MAAMiF,wBAAwB,CAAC;AAI9BC,yBAAA,GAJKD,wBAAwB;AAAA3E,eAAA,CAAxB2E,wBAAwB,wBAAAE,kCAAAnD,iBAAA;EAAA,YAAAA,iBAAA,IACyEiD,yBAAwB;AAAA;AAAA3E,eAAA,CADzH2E,wBAAwB,8BA7CmDrH,EAAE,CAAAwH,gBAAA;EAAAlD,IAAA,EA+CqB+C,yBAAwB;EAAAH,OAAA,GAAY9E,kBAAkB,EAAEgF,UAAU;EAAAK,OAAA,GAAarF,kBAAkB,EAAEgF,UAAU,EAAExF,eAAe;AAAA;AAAAc,eAAA,CAFhO2E,wBAAwB,8BA7CmDrH,EAAE,CAAA0H,gBAAA;EAAAR,OAAA,GAgDyDtF,eAAe;AAAA;AAE3J;EAAA,QAAA+E,SAAA,oBAAAA,SAAA,KAlDiF3G,EAAE,CAAA4G,iBAAA,CAkDQS,wBAAwB,EAAc,CAAC;IACtH/C,IAAI,EAAE7C,QAAQ;IACdoF,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CAAC9E,kBAAkB,EAAEgF,UAAU,CAAC;MACzCK,OAAO,EAAE,CAACrF,kBAAkB,EAAEgF,UAAU,EAAExF,eAAe;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASC,oCAAoC,EAAEG,4CAA4C,EAAEI,kBAAkB,EAAEiF,wBAAwB,EAAED,UAAU;AACrJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}