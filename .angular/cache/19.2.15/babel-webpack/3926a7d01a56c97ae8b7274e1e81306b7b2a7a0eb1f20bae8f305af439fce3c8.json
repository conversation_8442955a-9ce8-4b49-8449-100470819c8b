{"ast": null, "code": "import { concatAll } from '../operators/concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function concat() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  return concatAll()(from(args, popScheduler(args)));\n}\n//# sourceMappingURL=concat.js.map", "map": {"version": 3, "names": ["concatAll", "popScheduler", "from", "concat", "args", "_i", "arguments", "length"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/concat.js"], "sourcesContent": ["import { concatAll } from '../operators/concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function concat() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    return concatAll()(from(args, popScheduler(args)));\n}\n//# sourceMappingURL=concat.js.map"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAO,SAASC,MAAMA,CAAA,EAAG;EACrB,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,OAAOL,SAAS,CAAC,CAAC,CAACE,IAAI,CAACE,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC,CAAC,CAAC;AACtD;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}