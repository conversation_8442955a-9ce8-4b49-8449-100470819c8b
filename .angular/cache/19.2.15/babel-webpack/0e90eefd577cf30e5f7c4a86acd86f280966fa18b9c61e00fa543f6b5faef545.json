{"ast": null, "code": "var _SelectOnClickDirective;\nimport { __decorate } from \"tslib\";\nimport { Directive, ElementRef, HostListener } from '@angular/core';\nlet SelectOnClickDirective = (_SelectOnClickDirective = class SelectOnClickDirective {\n  constructor(elementRef) {\n    elementRef.nativeElement.style.cursor = 'text';\n  }\n  onClick({\n    target\n  }) {\n    target.select();\n  }\n}, _SelectOnClickDirective.ctorParameters = () => [{\n  type: ElementRef\n}], _SelectOnClickDirective.propDecorators = {\n  onClick: [{\n    type: HostListener,\n    args: ['click', ['$event']]\n  }]\n}, _SelectOnClickDirective);\nSelectOnClickDirective = __decorate([Directive({\n  // tslint:disable-next-line:directive-selector\n  selector: 'input[select-on-click], textarea[select-on-click]',\n  standalone: false\n})], SelectOnClickDirective);\nexport { SelectOnClickDirective };", "map": {"version": 3, "names": ["__decorate", "Directive", "ElementRef", "HostListener", "SelectOnClickDirective", "_SelectOnClickDirective", "constructor", "elementRef", "nativeElement", "style", "cursor", "onClick", "target", "select", "ctorParameters", "type", "propDecorators", "args", "selector", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/directives/selectOnClick/select-on-click.directive.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Directive, ElementRef, HostListener } from '@angular/core';\nlet SelectOnClickDirective = class SelectOnClickDirective {\n    constructor(elementRef) {\n        elementRef.nativeElement.style.cursor = 'text';\n    }\n    onClick({ target }) {\n        target.select();\n    }\n    static { this.ctorParameters = () => [\n        { type: ElementRef }\n    ]; }\n    static { this.propDecorators = {\n        onClick: [{ type: HostListener, args: ['click', ['$event'],] }]\n    }; }\n};\nSelectOnClickDirective = __decorate([\n    Directive({\n        // tslint:disable-next-line:directive-selector\n        selector: 'input[select-on-click], textarea[select-on-click]',\n        standalone: false\n    })\n], SelectOnClickDirective);\nexport { SelectOnClickDirective };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,UAAU,EAAEC,YAAY,QAAQ,eAAe;AACnE,IAAIC,sBAAsB,IAAAC,uBAAA,GAAG,MAAMD,sBAAsB,CAAC;EACtDE,WAAWA,CAACC,UAAU,EAAE;IACpBA,UAAU,CAACC,aAAa,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;EAClD;EACAC,OAAOA,CAAC;IAAEC;EAAO,CAAC,EAAE;IAChBA,MAAM,CAACC,MAAM,CAAC,CAAC;EACnB;AAOJ,CAAC,EANYR,uBAAA,CAAKS,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEb;AAAW,CAAC,CACvB,EACQG,uBAAA,CAAKW,cAAc,GAAG;EAC3BL,OAAO,EAAE,CAAC;IAAEI,IAAI,EAAEZ,YAAY;IAAEc,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;EAAG,CAAC;AAClE,CAAC,EAAAZ,uBAAA,CACJ;AACDD,sBAAsB,GAAGJ,UAAU,CAAC,CAChCC,SAAS,CAAC;EACN;EACAiB,QAAQ,EAAE,mDAAmD;EAC7DC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEf,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}