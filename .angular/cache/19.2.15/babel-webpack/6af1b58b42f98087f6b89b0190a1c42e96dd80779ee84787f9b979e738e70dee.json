{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Hungarian [hu]\n//! author : <PERSON> : https://github.com/adambrunner\n//! author : <PERSON>  : https://github.com/passatgt\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var weekEndings = 'vasárnap hétfőn kedden szerdán csütörtökön pénteken szombaton'.split(' ');\n  function translate(number, withoutSuffix, key, isFuture) {\n    var num = number;\n    switch (key) {\n      case 's':\n        return isFuture || withoutSuffix ? 'néhány másodperc' : 'néhány más<PERSON>';\n      case 'ss':\n        return num + (isFuture || withoutSuffix) ? ' másodperc' : ' másodperce';\n      case 'm':\n        return 'egy' + (isFuture || withoutSuffix ? ' perc' : ' perce');\n      case 'mm':\n        return num + (isFuture || withoutSuffix ? ' perc' : ' perce');\n      case 'h':\n        return 'egy' + (isFuture || withoutSuffix ? ' óra' : ' órája');\n      case 'hh':\n        return num + (isFuture || withoutSuffix ? ' óra' : ' órája');\n      case 'd':\n        return 'egy' + (isFuture || withoutSuffix ? ' nap' : ' napja');\n      case 'dd':\n        return num + (isFuture || withoutSuffix ? ' nap' : ' napja');\n      case 'M':\n        return 'egy' + (isFuture || withoutSuffix ? ' hónap' : ' hónapja');\n      case 'MM':\n        return num + (isFuture || withoutSuffix ? ' hónap' : ' hónapja');\n      case 'y':\n        return 'egy' + (isFuture || withoutSuffix ? ' év' : ' éve');\n      case 'yy':\n        return num + (isFuture || withoutSuffix ? ' év' : ' éve');\n    }\n    return '';\n  }\n  function week(isFuture) {\n    return (isFuture ? '' : '[múlt] ') + '[' + weekEndings[this.day()] + '] LT[-kor]';\n  }\n  var hu = moment.defineLocale('hu', {\n    months: 'január_február_március_április_május_június_július_augusztus_szeptember_október_november_december'.split('_'),\n    monthsShort: 'jan._feb._márc._ápr._máj._jún._júl._aug._szept._okt._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat'.split('_'),\n    weekdaysShort: 'vas_hét_kedd_sze_csüt_pén_szo'.split('_'),\n    weekdaysMin: 'v_h_k_sze_cs_p_szo'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'YYYY.MM.DD.',\n      LL: 'YYYY. MMMM D.',\n      LLL: 'YYYY. MMMM D. H:mm',\n      LLLL: 'YYYY. MMMM D., dddd H:mm'\n    },\n    meridiemParse: /de|du/i,\n    isPM: function (input) {\n      return input.charAt(1).toLowerCase() === 'u';\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 12) {\n        return isLower === true ? 'de' : 'DE';\n      } else {\n        return isLower === true ? 'du' : 'DU';\n      }\n    },\n    calendar: {\n      sameDay: '[ma] LT[-kor]',\n      nextDay: '[holnap] LT[-kor]',\n      nextWeek: function () {\n        return week.call(this, true);\n      },\n      lastDay: '[tegnap] LT[-kor]',\n      lastWeek: function () {\n        return week.call(this, false);\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s múlva',\n      past: '%s',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return hu;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "weekEndings", "split", "translate", "number", "withoutSuffix", "key", "isFuture", "num", "week", "day", "hu", "defineLocale", "months", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "char<PERSON>t", "toLowerCase", "meridiem", "hours", "minutes", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "call", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "dow", "doy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/moment/locale/hu.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Hungarian [hu]\n//! author : <PERSON> : https://github.com/adambrunner\n//! author : <PERSON>  : https://github.com/passatgt\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var weekEndings = 'vasárnap hétfőn kedden szerdán csütörtökön pénteken szombaton'.split(\n        ' '\n    );\n    function translate(number, withoutSuffix, key, isFuture) {\n        var num = number;\n        switch (key) {\n            case 's':\n                return isFuture || withoutSuffix\n                    ? 'néhány másodperc'\n                    : 'néhány más<PERSON>';\n            case 'ss':\n                return num + (isFuture || withoutSuffix)\n                    ? ' másodperc'\n                    : ' másodperce';\n            case 'm':\n                return 'egy' + (isFuture || withoutSuffix ? ' perc' : ' perce');\n            case 'mm':\n                return num + (isFuture || withoutSuffix ? ' perc' : ' perce');\n            case 'h':\n                return 'egy' + (isFuture || withoutSuffix ? ' óra' : ' órája');\n            case 'hh':\n                return num + (isFuture || withoutSuffix ? ' óra' : ' órája');\n            case 'd':\n                return 'egy' + (isFuture || withoutSuffix ? ' nap' : ' napja');\n            case 'dd':\n                return num + (isFuture || withoutSuffix ? ' nap' : ' napja');\n            case 'M':\n                return 'egy' + (isFuture || withoutSuffix ? ' hónap' : ' hónapja');\n            case 'MM':\n                return num + (isFuture || withoutSuffix ? ' hónap' : ' hónapja');\n            case 'y':\n                return 'egy' + (isFuture || withoutSuffix ? ' év' : ' éve');\n            case 'yy':\n                return num + (isFuture || withoutSuffix ? ' év' : ' éve');\n        }\n        return '';\n    }\n    function week(isFuture) {\n        return (\n            (isFuture ? '' : '[múlt] ') +\n            '[' +\n            weekEndings[this.day()] +\n            '] LT[-kor]'\n        );\n    }\n\n    var hu = moment.defineLocale('hu', {\n        months: 'január_február_március_április_május_június_július_augusztus_szeptember_október_november_december'.split(\n            '_'\n        ),\n        monthsShort: 'jan._feb._márc._ápr._máj._jún._júl._aug._szept._okt._nov._dec.'.split(\n            '_'\n        ),\n        monthsParseExact: true,\n        weekdays: 'vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat'.split('_'),\n        weekdaysShort: 'vas_hét_kedd_sze_csüt_pén_szo'.split('_'),\n        weekdaysMin: 'v_h_k_sze_cs_p_szo'.split('_'),\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'YYYY.MM.DD.',\n            LL: 'YYYY. MMMM D.',\n            LLL: 'YYYY. MMMM D. H:mm',\n            LLLL: 'YYYY. MMMM D., dddd H:mm',\n        },\n        meridiemParse: /de|du/i,\n        isPM: function (input) {\n            return input.charAt(1).toLowerCase() === 'u';\n        },\n        meridiem: function (hours, minutes, isLower) {\n            if (hours < 12) {\n                return isLower === true ? 'de' : 'DE';\n            } else {\n                return isLower === true ? 'du' : 'DU';\n            }\n        },\n        calendar: {\n            sameDay: '[ma] LT[-kor]',\n            nextDay: '[holnap] LT[-kor]',\n            nextWeek: function () {\n                return week.call(this, true);\n            },\n            lastDay: '[tegnap] LT[-kor]',\n            lastWeek: function () {\n                return week.call(this, false);\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s múlva',\n            past: '%s',\n            s: translate,\n            ss: translate,\n            m: translate,\n            mm: translate,\n            h: translate,\n            hh: translate,\n            d: translate,\n            dd: translate,\n            M: translate,\n            MM: translate,\n            y: translate,\n            yy: translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return hu;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,WAAW,GAAG,+DAA+D,CAACC,KAAK,CACnF,GACJ,CAAC;EACD,SAASC,SAASA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IACrD,IAAIC,GAAG,GAAGJ,MAAM;IAChB,QAAQE,GAAG;MACP,KAAK,GAAG;QACJ,OAAOC,QAAQ,IAAIF,aAAa,GAC1B,kBAAkB,GAClB,mBAAmB;MAC7B,KAAK,IAAI;QACL,OAAOG,GAAG,IAAID,QAAQ,IAAIF,aAAa,CAAC,GAClC,YAAY,GACZ,aAAa;MACvB,KAAK,GAAG;QACJ,OAAO,KAAK,IAAIE,QAAQ,IAAIF,aAAa,GAAG,OAAO,GAAG,QAAQ,CAAC;MACnE,KAAK,IAAI;QACL,OAAOG,GAAG,IAAID,QAAQ,IAAIF,aAAa,GAAG,OAAO,GAAG,QAAQ,CAAC;MACjE,KAAK,GAAG;QACJ,OAAO,KAAK,IAAIE,QAAQ,IAAIF,aAAa,GAAG,MAAM,GAAG,QAAQ,CAAC;MAClE,KAAK,IAAI;QACL,OAAOG,GAAG,IAAID,QAAQ,IAAIF,aAAa,GAAG,MAAM,GAAG,QAAQ,CAAC;MAChE,KAAK,GAAG;QACJ,OAAO,KAAK,IAAIE,QAAQ,IAAIF,aAAa,GAAG,MAAM,GAAG,QAAQ,CAAC;MAClE,KAAK,IAAI;QACL,OAAOG,GAAG,IAAID,QAAQ,IAAIF,aAAa,GAAG,MAAM,GAAG,QAAQ,CAAC;MAChE,KAAK,GAAG;QACJ,OAAO,KAAK,IAAIE,QAAQ,IAAIF,aAAa,GAAG,QAAQ,GAAG,UAAU,CAAC;MACtE,KAAK,IAAI;QACL,OAAOG,GAAG,IAAID,QAAQ,IAAIF,aAAa,GAAG,QAAQ,GAAG,UAAU,CAAC;MACpE,KAAK,GAAG;QACJ,OAAO,KAAK,IAAIE,QAAQ,IAAIF,aAAa,GAAG,KAAK,GAAG,MAAM,CAAC;MAC/D,KAAK,IAAI;QACL,OAAOG,GAAG,IAAID,QAAQ,IAAIF,aAAa,GAAG,KAAK,GAAG,MAAM,CAAC;IACjE;IACA,OAAO,EAAE;EACb;EACA,SAASI,IAAIA,CAACF,QAAQ,EAAE;IACpB,OACI,CAACA,QAAQ,GAAG,EAAE,GAAG,SAAS,IAC1B,GAAG,GACHN,WAAW,CAAC,IAAI,CAACS,GAAG,CAAC,CAAC,CAAC,GACvB,YAAY;EAEpB;EAEA,IAAIC,EAAE,GAAGX,MAAM,CAACY,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,mGAAmG,CAACX,KAAK,CAC7G,GACJ,CAAC;IACDY,WAAW,EAAE,gEAAgE,CAACZ,KAAK,CAC/E,GACJ,CAAC;IACDa,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,qDAAqD,CAACd,KAAK,CAAC,GAAG,CAAC;IAC1Ee,aAAa,EAAE,+BAA+B,CAACf,KAAK,CAAC,GAAG,CAAC;IACzDgB,WAAW,EAAE,oBAAoB,CAAChB,KAAK,CAAC,GAAG,CAAC;IAC5CiB,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,eAAe;MACnBC,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,QAAQ;IACvBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAK,GAAG;IAChD,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAOE,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;MACzC,CAAC,MAAM;QACH,OAAOA,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;MACzC;IACJ,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OAAO7B,IAAI,CAAC8B,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;MAChC,CAAC;MACDC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OAAOhC,IAAI,CAAC8B,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;MACjC,CAAC;MACDG,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,IAAI;MACVC,CAAC,EAAE3C,SAAS;MACZ4C,EAAE,EAAE5C,SAAS;MACb6C,CAAC,EAAE7C,SAAS;MACZ8C,EAAE,EAAE9C,SAAS;MACb+C,CAAC,EAAE/C,SAAS;MACZgD,EAAE,EAAEhD,SAAS;MACbiD,CAAC,EAAEjD,SAAS;MACZkD,EAAE,EAAElD,SAAS;MACbmD,CAAC,EAAEnD,SAAS;MACZoD,EAAE,EAAEpD,SAAS;MACbqD,CAAC,EAAErD,SAAS;MACZsD,EAAE,EAAEtD;IACR,CAAC;IACDuD,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdlD,IAAI,EAAE;MACFmD,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOlD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}