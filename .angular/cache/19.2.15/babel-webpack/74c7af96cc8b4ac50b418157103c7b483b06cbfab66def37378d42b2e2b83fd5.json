{"ast": null, "code": "var _DocsIconsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs-icons.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../docs.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsIconsComponent = (_DocsIconsComponent = class DocsIconsComponent {\n  constructor() {}\n  ngOnInit() {}\n}, _DocsIconsComponent.ctorParameters = () => [], _DocsIconsComponent);\nDocsIconsComponent = __decorate([Component({\n  selector: 'lib-docs-icons',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DocsIconsComponent);\nexport { DocsIconsComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "DocsIconsComponent", "_DocsIconsComponent", "constructor", "ngOnInit", "ctorParameters", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/styles/docs/docs-icons/docs-icons.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./docs-icons.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"../docs.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet DocsIconsComponent = class DocsIconsComponent {\n    constructor() { }\n    ngOnInit() {\n    }\n    static { this.ctorParameters = () => []; }\n};\nDocsIconsComponent = __decorate([\n    Component({\n        selector: 'lib-docs-icons',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], DocsIconsComponent);\nexport { DocsIconsComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,wCAAwC;AACzE,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,SAASC,SAAS,QAAQ,eAAe;AACzC,IAAIC,kBAAkB,IAAAC,mBAAA,GAAG,MAAMD,kBAAkB,CAAC;EAC9CE,WAAWA,CAAA,EAAG,CAAE;EAChBC,QAAQA,CAAA,EAAG,CACX;AAEJ,CAAC,EADYF,mBAAA,CAAKG,cAAc,GAAG,MAAM,EAAE,EAAAH,mBAAA,CAC1C;AACDD,kBAAkB,GAAGJ,UAAU,CAAC,CAC5BG,SAAS,CAAC;EACNM,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAET,oBAAoB;EAC9BU,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACV,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEE,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}