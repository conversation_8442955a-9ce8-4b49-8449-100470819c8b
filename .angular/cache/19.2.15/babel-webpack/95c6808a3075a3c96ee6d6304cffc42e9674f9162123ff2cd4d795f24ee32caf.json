{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiControlMessagesComponent } from './swui-control-messages.component';\nlet SwuiControlMessagesModule = class SwuiControlMessagesModule {};\nSwuiControlMessagesModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule],\n  declarations: [SwuiControlMessagesComponent],\n  exports: [SwuiControlMessagesComponent]\n})], SwuiControlMessagesModule);\nexport { SwuiControlMessagesModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "TranslateModule", "SwuiControlMessagesComponent", "SwuiControlMessagesModule", "__decorate", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-control-messages/swui-control-messages.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { SwuiControlMessagesComponent } from './swui-control-messages.component';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule,\n  ],\n  declarations: [\n    SwuiControlMessagesComponent,\n  ],\n  exports: [\n    SwuiControlMessagesComponent\n  ]\n})\nexport class SwuiControlMessagesModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,4BAA4B,QAAQ,mCAAmC;AAczE,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB,GACrC;AADYA,yBAAyB,GAAAC,UAAA,EAZrCL,QAAQ,CAAC;EACRM,OAAO,EAAE,CACPL,YAAY,EACZC,eAAe,CAChB;EACDK,YAAY,EAAE,CACZJ,4BAA4B,CAC7B;EACDK,OAAO,EAAE,CACPL,4BAA4B;CAE/B,CAAC,C,EACWC,yBAAyB,CACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}