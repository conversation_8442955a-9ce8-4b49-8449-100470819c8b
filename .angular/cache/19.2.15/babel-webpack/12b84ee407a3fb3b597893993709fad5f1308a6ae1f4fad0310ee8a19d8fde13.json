{"ast": null, "code": "import { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { TestBed, waitForAsync } from '@angular/core/testing';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubEntityService } from '../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwHubInitService } from '../services/sw-hub-init/sw-hub-init.service';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../services/sw-hub-init/sw-hub-init.token';\nimport { HubSelectorModule } from './hub-selector/hub-selector.module';\nimport { LanguageSelectorModule } from './language-selector/language-selector.module';\nimport { SwuiTopMenuComponent } from './swui-top-menu.component';\nimport { UserMenuModule } from './user-menu/user-menu.module';\ndescribe('TopMenuComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      schemas: [NO_ERRORS_SCHEMA],\n      declarations: [SwuiTopMenuComponent],\n      imports: [TranslateModule.forRoot(), MatToolbarModule, MatButtonModule, MatIconModule, HubSelectorModule, UserMenuModule, LanguageSelectorModule, MatMenuModule],\n      providers: [{\n        provide: SWUI_HUB_MESSAGE_CONFIG,\n        useValue: {}\n      }, {\n        provide: SwHubConfigService,\n        useValue: {}\n      }, {\n        provide: SwHubAuthService,\n        useValue: {\n          isLogged() {\n            return true;\n          },\n          allowedTo() {\n            return true;\n          }\n        }\n      }, {\n        provide: SwHubInitService,\n        useValue: {}\n      }, SwHubEntityService]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiTopMenuComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["NO_ERRORS_SCHEMA", "TestBed", "waitForAsync", "MatButtonModule", "MatIconModule", "MatMenuModule", "MatToolbarModule", "TranslateModule", "SwHubAuthService", "SwHubConfigService", "SwHubEntityService", "SwHubInitService", "SWUI_HUB_MESSAGE_CONFIG", "HubSelectorModule", "LanguageSelectorModule", "SwuiTopMenuComponent", "UserMenuModule", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "schemas", "declarations", "imports", "forRoot", "providers", "provide", "useValue", "isLogged", "allowedTo", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/swui-top-menu.component.spec.ts"], "sourcesContent": ["import { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubEntityService } from '../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwHubInitService } from '../services/sw-hub-init/sw-hub-init.service';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../services/sw-hub-init/sw-hub-init.token';\nimport { HubSelectorModule } from './hub-selector/hub-selector.module';\nimport { LanguageSelectorModule } from './language-selector/language-selector.module';\nimport { SwuiTopMenuComponent } from './swui-top-menu.component';\nimport { UserMenuModule } from './user-menu/user-menu.module';\n\n\ndescribe('TopMenuComponent', () => {\n  let component: SwuiTopMenuComponent;\n  let fixture: ComponentFixture<SwuiTopMenuComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      schemas: [NO_ERRORS_SCHEMA],\n      declarations: [SwuiTopMenuComponent],\n      imports: [\n        TranslateModule.forRoot(),\n        MatToolbarModule,\n        MatButtonModule,\n        MatIconModule,\n        HubSelectorModule,\n        UserMenuModule,\n        LanguageSelectorModule,\n        MatMenuModule\n      ],\n      providers: [\n        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: {} },\n        { provide: SwHubConfigService, useValue: {} },\n        {\n          provide: SwHubAuthService, useValue: {\n            isLogged() {\n              return true;\n            },\n            allowedTo() {\n              return true;\n            },\n          }\n        },\n        { provide: SwHubInitService, useValue: {} },\n        SwHubEntityService,\n      ]\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiTopMenuComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,eAAe;AAChD,SAA2BC,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAC/E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,uBAAuB,QAAQ,2CAA2C;AACnF,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,sBAAsB,QAAQ,8CAA8C;AACrF,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,cAAc,QAAQ,8BAA8B;AAG7DC,QAAQ,CAAC,kBAAkB,EAAE,MAAK;EAChC,IAAIC,SAA+B;EACnC,IAAIC,OAA+C;EAEnDC,UAAU,CAAClB,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACoB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACtB,gBAAgB,CAAC;MAC3BuB,YAAY,EAAE,CAACR,oBAAoB,CAAC;MACpCS,OAAO,EAAE,CACPjB,eAAe,CAACkB,OAAO,EAAE,EACzBnB,gBAAgB,EAChBH,eAAe,EACfC,aAAa,EACbS,iBAAiB,EACjBG,cAAc,EACdF,sBAAsB,EACtBT,aAAa,CACd;MACDqB,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEf,uBAAuB;QAAEgB,QAAQ,EAAE;MAAE,CAAE,EAClD;QAAED,OAAO,EAAElB,kBAAkB;QAAEmB,QAAQ,EAAE;MAAE,CAAE,EAC7C;QACED,OAAO,EAAEnB,gBAAgB;QAAEoB,QAAQ,EAAE;UACnCC,QAAQA,CAAA;YACN,OAAO,IAAI;UACb,CAAC;UACDC,SAASA,CAAA;YACP,OAAO,IAAI;UACb;;OAEH,EACD;QAAEH,OAAO,EAAEhB,gBAAgB;QAAEiB,QAAQ,EAAE;MAAE,CAAE,EAC3ClB,kBAAkB;KAErB,CAAC,CACCqB,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHX,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGlB,OAAO,CAAC+B,eAAe,CAACjB,oBAAoB,CAAC;IACvDG,SAAS,GAAGC,OAAO,CAACc,iBAAiB;IACrCd,OAAO,CAACe,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAClB,SAAS,CAAC,CAACmB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}