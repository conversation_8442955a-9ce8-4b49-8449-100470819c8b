{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuComponent } from './swui-menu.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTreeModule } from '@angular/material/tree';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { MatRippleModule } from '@angular/material/core';\nexport const MAT_MODULES = [MatListModule, MatMenuModule, MatTreeModule, MatIconModule, MatRippleModule];\nlet SwuiMenuModule = class SwuiMenuModule {};\nSwuiMenuModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule.forChild(), RouterModule, ...MAT_MODULES],\n  declarations: [SwuiMenuComponent],\n  exports: [SwuiMenuComponent]\n})], SwuiMenuModule);\nexport { SwuiMenuModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "TranslateModule", "SwuiMenuComponent", "MatMenuModule", "MatTreeModule", "MatIconModule", "MatListModule", "MatRippleModule", "MAT_MODULES", "SwuiMenuModule", "__decorate", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-menu/swui-menu.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { SwuiMenuComponent } from './swui-menu.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTreeModule } from '@angular/material/tree';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatListModule } from '@angular/material/list';\nimport { MatRippleModule } from '@angular/material/core';\n\n\nexport const MAT_MODULES = [\n  MatListModule,\n  MatMenuModule,\n  MatTreeModule,\n  MatIconModule,\n  MatRippleModule,\n];\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    RouterModule,\n    ...MAT_MODULES,\n  ],\n  declarations: [SwuiMenuComponent],\n  exports: [SwuiMenuComponent]\n})\nexport class SwuiMenuModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AAGxD,OAAO,MAAMC,WAAW,GAAG,CACzBF,aAAa,EACbH,aAAa,EACbC,aAAa,EACbC,aAAa,EACbE,eAAe,CAChB;AAYM,IAAME,cAAc,GAApB,MAAMA,cAAc,GAC1B;AADYA,cAAc,GAAAC,UAAA,EAV1BZ,QAAQ,CAAC;EACRa,OAAO,EAAE,CACPZ,YAAY,EACZE,eAAe,CAACW,QAAQ,EAAE,EAC1BZ,YAAY,EACZ,GAAGQ,WAAW,CACf;EACDK,YAAY,EAAE,CAACX,iBAAiB,CAAC;EACjCY,OAAO,EAAE,CAACZ,iBAAiB;CAC5B,CAAC,C,EACWO,cAAc,CAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}