{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwitcheryComponent } from './switchery.component';\nlet SwitcheryModule = class SwitcheryModule {};\nSwitcheryModule = __decorate([NgModule({\n  imports: [CommonModule],\n  declarations: [SwitcheryComponent],\n  exports: [SwitcheryComponent]\n})], SwitcheryModule);\nexport { SwitcheryModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "SwitcheryComponent", "SwitcheryModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-switchery/switchery.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwitcheryComponent } from './switchery.component';\nlet SwitcheryModule = class SwitcheryModule {\n};\nSwitcheryModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n        ],\n        declarations: [\n            SwitcheryComponent,\n        ],\n        exports: [\n            SwitcheryComponent,\n        ]\n    })\n], SwitcheryModule);\nexport { SwitcheryModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,IAAIC,eAAe,GAAG,MAAMA,eAAe,CAAC,EAC3C;AACDA,eAAe,GAAGJ,UAAU,CAAC,CACzBC,QAAQ,CAAC;EACLI,OAAO,EAAE,CACLH,YAAY,CACf;EACDI,YAAY,EAAE,CACVH,kBAAkB,CACrB;EACDI,OAAO,EAAE,CACLJ,kBAAkB;AAE1B,CAAC,CAAC,CACL,EAAEC,eAAe,CAAC;AACnB,SAASA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}