{"ast": null, "code": "import { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { moduleMetadata } from '@storybook/angular';\nimport { SwuiInputComponent } from './swui-input.component';\nimport { SwuiInputModule } from './swui-input.module';\nconst meta = {\n  title: 'Forms/Input',\n  component: SwuiInputComponent,\n  decorators: [moduleMetadata({\n    imports: [BrowserAnimationsModule, SwuiInputModule]\n  })],\n  parameters: {\n    layout: 'centered'\n  },\n  tags: ['autodocs']\n};\nexport default meta;\nexport const Default = {\n  args: {}\n};\nexport const WithLabelAndPlaceholder = {\n  args: {\n    label: 'Input label',\n    placeholder: 'Search Item',\n    ngModel: 'Init value'\n  }\n};", "map": {"version": 3, "names": ["BrowserAnimationsModule", "moduleMetadata", "SwuiInputComponent", "SwuiInputModule", "meta", "title", "component", "decorators", "imports", "parameters", "layout", "tags", "<PERSON><PERSON><PERSON>", "args", "WithLabelAndPlaceholder", "label", "placeholder", "ngModel"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-input/swui-input.stories.ts"], "sourcesContent": ["import { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { moduleMetadata } from '@storybook/angular';\nimport { SwuiInputComponent } from './swui-input.component';\nimport { SwuiInputModule } from './swui-input.module';\nconst meta = {\n    title: 'Forms/Input',\n    component: SwuiInputComponent,\n    decorators: [\n        moduleMetadata({\n            imports: [\n                BrowserAnimationsModule,\n                SwuiInputModule\n            ],\n        })\n    ],\n    parameters: {\n        layout: 'centered',\n    },\n    tags: ['autodocs'],\n};\nexport default meta;\nexport const Default = {\n    args: {},\n};\nexport const WithLabelAndPlaceholder = {\n    args: {\n        label: 'Input label',\n        placeholder: 'Search Item',\n        ngModel: 'Init value'\n    },\n};\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,MAAMC,IAAI,GAAG;EACTC,KAAK,EAAE,aAAa;EACpBC,SAAS,EAAEJ,kBAAkB;EAC7BK,UAAU,EAAE,CACRN,cAAc,CAAC;IACXO,OAAO,EAAE,CACLR,uBAAuB,EACvBG,eAAe;EAEvB,CAAC,CAAC,CACL;EACDM,UAAU,EAAE;IACRC,MAAM,EAAE;EACZ,CAAC;EACDC,IAAI,EAAE,CAAC,UAAU;AACrB,CAAC;AACD,eAAeP,IAAI;AACnB,OAAO,MAAMQ,OAAO,GAAG;EACnBC,IAAI,EAAE,CAAC;AACX,CAAC;AACD,OAAO,MAAMC,uBAAuB,GAAG;EACnCD,IAAI,EAAE;IACFE,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE;EACb;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}