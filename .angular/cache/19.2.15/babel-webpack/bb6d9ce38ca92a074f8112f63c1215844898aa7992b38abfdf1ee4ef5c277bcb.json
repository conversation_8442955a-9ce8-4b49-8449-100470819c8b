{"ast": null, "code": "var _SwHubInitService;\nimport { __decorate } from \"tslib\";\nimport { Inject, Injectable } from '@angular/core';\nimport { NavigationEnd, Router } from '@angular/router';\nimport { TranslateService } from '@ngx-translate/core';\nimport { BehaviorSubject, fromEvent, merge, Subject } from 'rxjs';\nimport { filter, map, share, take, takeUntil, tap, throttleTime } from 'rxjs/operators';\nimport { SwuiSidebarService } from '../../swui-sidebar/swui-sidebar.service';\nimport { SettingsService } from '../settings/settings.service';\nimport { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';\nimport { SwHubEntityService } from '../sw-hub-entity/sw-hub-entity.service';\nimport { isHubMessage, TYPES } from './sw-hub-init.model';\nimport { SWUI_HUB_MESSAGE_CONFIG } from './sw-hub-init.token';\nlet SwHubInitService = (_SwHubInitService = class SwHubInitService {\n  constructor(router, translate, authService, settingsService, entityService, configService, sidebarService, config) {\n    this.router = router;\n    this.translate = translate;\n    this.authService = authService;\n    this.settingsService = settingsService;\n    this.entityService = entityService;\n    this.configService = configService;\n    this.sidebarService = sidebarService;\n    this.config = config;\n    this.navigation$ = new BehaviorSubject(null);\n    this.destroyed$ = new Subject();\n    this.ready$ = new Subject();\n    this.ready = false;\n    const source = fromEvent(window, 'message').pipe(filter(event => isHubMessage(event)), map(({\n      data\n    }) => data), filter(({\n      target\n    }) => target === config.name || target === '*'), tap(data => console.log('[hub] getMessage', data)), share());\n    this.message$ = source.pipe(filter(({\n      type\n    }) => ![TYPES.BRIDGE_LOADED, TYPES.LOGIN, TYPES.LOGOUT].includes(type)));\n    source.pipe(takeUntil(this.destroyed$)).subscribe(({\n      type,\n      body\n    }) => {\n      switch (type) {\n        case TYPES.BRIDGE_LOADED:\n          const {\n            accessToken,\n            lang,\n            settings,\n            navigation,\n            entityId,\n            grantedPermissions,\n            twoFactor\n          } = body;\n          this.ready = true;\n          if (this.win) {\n            this.ready$.next(this.win);\n          }\n          this.authService.setToken(accessToken, grantedPermissions, twoFactor);\n          if (lang) {\n            this.translate.use(lang);\n          }\n          if (settings) {\n            this.settingsService.use(settings);\n          }\n          if (navigation) {\n            this.navigation$.next(navigation);\n          }\n          if (entityId) {\n            this.entityService.useByPath(entityId);\n          }\n          break;\n        case TYPES.TOKEN:\n        case TYPES.LOGIN:\n          this.authService.setToken(body.accessToken, body.grantedPermissions, body.twoFactor);\n          break;\n        case TYPES.LOGOUT:\n          this.authService.logout();\n          if (this.configService.loginUrl) {\n            location.href = this.configService.loginUrl;\n          }\n          break;\n        case TYPES.LOCALE_CHANGED:\n          this.translate.use(body);\n          break;\n        case TYPES.APP_SETTINGS_CHANGED:\n          this.settingsService.use(body);\n          break;\n        case TYPES.ENTITY_ID_CHANGED:\n          this.entityService.use(body);\n          break;\n        case TYPES.SIDEBAR_COLLAPSED:\n          this.sidebarService.isCollapsed.next(body === true);\n          break;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  init() {\n    this.translate.addLangs(this.config.langs.map(({\n      id\n    }) => id));\n    if (this.config.defaultLang) {\n      this.translate.setDefaultLang(this.config.defaultLang);\n    }\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), map(event => event), map(event => {\n      var _this$config;\n      const foundException = (_this$config = this.config) === null || _this$config === void 0 || (_this$config = _this$config.lastLocationExceptions) === null || _this$config === void 0 ? void 0 : _this$config.find(exception => event.url.startsWith(exception.url));\n      return foundException ? foundException.replaceTo : event.url;\n    }), takeUntil(this.destroyed$)).subscribe(url => {\n      const body = {\n        hub: this.config.name,\n        url: url\n      };\n      this.postMessage(TYPES.LOCATION_CHANGED, body);\n    });\n    if (this.configService.bridge) {\n      this.create(this.configService.bridge);\n    }\n    this.postMessage(TYPES.HUB_LOADED);\n  }\n  initInactivityWatcher() {\n    merge(fromEvent(window, 'mousemove'), fromEvent(window, 'keypress')).pipe(throttleTime(10000), takeUntil(this.destroyed$)).subscribe(() => {\n      this.postMessage(TYPES.USER_ACTIVITY, new Date());\n    });\n    this.postMessage(TYPES.USER_ACTIVITY, new Date());\n  }\n  sendLogin(accessToken, grantedPermissions, twoFactor) {\n    this.postMessage(TYPES.LOGIN, {\n      accessToken,\n      grantedPermissions,\n      twoFactor\n    });\n  }\n  sendLogout() {\n    this.postMessage(TYPES.LOGOUT);\n  }\n  sendLocale(lang) {\n    this.postMessage(TYPES.LOCALE_CHANGED, lang);\n  }\n  sendAppSettings(settings) {\n    this.postMessage(TYPES.APP_SETTINGS_CHANGED, settings);\n  }\n  sendEntityId(entityId) {\n    this.postMessage(TYPES.ENTITY_ID_CHANGED, entityId);\n  }\n  sendTokenExpired(body) {\n    this.postMessage(TYPES.TOKEN_EXPIRED, body);\n  }\n  sendUnknownUserLocation(url) {\n    this.postMessage(TYPES.UNKNOWN_LOCATION, url);\n  }\n  postMessage(type, body) {\n    const message = {\n      target: 'bridge',\n      initiator: this.config.name,\n      type,\n      body\n    };\n    if (this.win && this.ready) {\n      this.win.postMessage(message, '*');\n    } else {\n      console.log('[hub] queue message', type);\n      this.ready$.pipe(take(1)).subscribe(win => {\n        console.log('[hub] post queued message', type);\n        win.postMessage(message, '*');\n      });\n    }\n  }\n  create(url) {\n    const el = document.createElement('iframe');\n    el.src = url;\n    el.style.display = 'none';\n    const frame = document.body.appendChild(el);\n    console.log('[hub] frame created');\n    frame.onload = () => {\n      if (frame.contentWindow) {\n        console.log('[hub] frame loaded');\n        this.win = frame.contentWindow;\n        if (this.ready) {\n          this.ready$.next(this.win);\n        }\n      }\n    };\n  }\n}, _SwHubInitService.ctorParameters = () => [{\n  type: Router\n}, {\n  type: TranslateService\n}, {\n  type: SwHubAuthService\n}, {\n  type: SettingsService\n}, {\n  type: SwHubEntityService\n}, {\n  type: SwHubConfigService\n}, {\n  type: SwuiSidebarService\n}, {\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_HUB_MESSAGE_CONFIG]\n  }]\n}], _SwHubInitService);\nSwHubInitService = __decorate([Injectable()], SwHubInitService);\nexport { SwHubInitService };", "map": {"version": 3, "names": ["__decorate", "Inject", "Injectable", "NavigationEnd", "Router", "TranslateService", "BehaviorSubject", "fromEvent", "merge", "Subject", "filter", "map", "share", "take", "takeUntil", "tap", "throttleTime", "SwuiSidebarService", "SettingsService", "SwHubAuthService", "SwHubConfigService", "SwHubEntityService", "isHubMessage", "TYPES", "SWUI_HUB_MESSAGE_CONFIG", "SwHubInitService", "_SwHubInitService", "constructor", "router", "translate", "authService", "settingsService", "entityService", "configService", "sidebarService", "config", "navigation$", "destroyed$", "ready$", "ready", "source", "window", "pipe", "event", "data", "target", "name", "console", "log", "message$", "type", "BRIDGE_LOADED", "LOGIN", "LOGOUT", "includes", "subscribe", "body", "accessToken", "lang", "settings", "navigation", "entityId", "grantedPermissions", "twoFactor", "win", "next", "setToken", "use", "useByPath", "TOKEN", "logout", "loginUrl", "location", "href", "LOCALE_CHANGED", "APP_SETTINGS_CHANGED", "ENTITY_ID_CHANGED", "SIDEBAR_COLLAPSED", "isCollapsed", "ngOnDestroy", "undefined", "complete", "init", "addLangs", "langs", "id", "defaultLang", "setDefaultLang", "events", "_this$config", "foundException", "lastLocationExceptions", "find", "exception", "url", "startsWith", "replaceTo", "hub", "postMessage", "LOCATION_CHANGED", "bridge", "create", "HUB_LOADED", "initInactivityWatcher", "USER_ACTIVITY", "Date", "send<PERSON><PERSON><PERSON>", "sendLogout", "sendLocale", "sendAppSettings", "sendEntityId", "sendTokenExpired", "TOKEN_EXPIRED", "sendUnknownUserLocation", "UNKNOWN_LOCATION", "message", "initiator", "el", "document", "createElement", "src", "style", "display", "frame", "append<PERSON><PERSON><PERSON>", "onload", "contentWindow", "ctorParameters", "decorators", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-init/sw-hub-init.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Inject, Injectable } from '@angular/core';\nimport { NavigationEnd, Router } from '@angular/router';\nimport { TranslateService } from '@ngx-translate/core';\nimport { BehaviorSubject, fromEvent, merge, Subject } from 'rxjs';\nimport { filter, map, share, take, takeUntil, tap, throttleTime } from 'rxjs/operators';\nimport { SwuiSidebarService } from '../../swui-sidebar/swui-sidebar.service';\nimport { SettingsService } from '../settings/settings.service';\nimport { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';\nimport { SwHubEntityService } from '../sw-hub-entity/sw-hub-entity.service';\nimport { isHubMessage, TYPES } from './sw-hub-init.model';\nimport { SWUI_HUB_MESSAGE_CONFIG } from './sw-hub-init.token';\nlet SwHubInitService = class SwHubInitService {\n    constructor(router, translate, authService, settingsService, entityService, configService, sidebarService, config) {\n        this.router = router;\n        this.translate = translate;\n        this.authService = authService;\n        this.settingsService = settingsService;\n        this.entityService = entityService;\n        this.configService = configService;\n        this.sidebarService = sidebarService;\n        this.config = config;\n        this.navigation$ = new BehaviorSubject(null);\n        this.destroyed$ = new Subject();\n        this.ready$ = new Subject();\n        this.ready = false;\n        const source = fromEvent(window, 'message').pipe(filter(event => isHubMessage(event)), map(({ data }) => data), filter(({ target }) => target === config.name || target === '*'), tap(data => console.log('[hub] getMessage', data)), share());\n        this.message$ = source.pipe(filter(({ type }) => ![TYPES.BRIDGE_LOADED, TYPES.LOGIN, TYPES.LOGOUT].includes(type)));\n        source.pipe(takeUntil(this.destroyed$)).subscribe(({ type, body }) => {\n            switch (type) {\n                case TYPES.BRIDGE_LOADED:\n                    const { accessToken, lang, settings, navigation, entityId, grantedPermissions, twoFactor } = body;\n                    this.ready = true;\n                    if (this.win) {\n                        this.ready$.next(this.win);\n                    }\n                    this.authService.setToken(accessToken, grantedPermissions, twoFactor);\n                    if (lang) {\n                        this.translate.use(lang);\n                    }\n                    if (settings) {\n                        this.settingsService.use(settings);\n                    }\n                    if (navigation) {\n                        this.navigation$.next(navigation);\n                    }\n                    if (entityId) {\n                        this.entityService.useByPath(entityId);\n                    }\n                    break;\n                case TYPES.TOKEN:\n                case TYPES.LOGIN:\n                    this.authService.setToken(body.accessToken, body.grantedPermissions, body.twoFactor);\n                    break;\n                case TYPES.LOGOUT:\n                    this.authService.logout();\n                    if (this.configService.loginUrl) {\n                        location.href = this.configService.loginUrl;\n                    }\n                    break;\n                case TYPES.LOCALE_CHANGED:\n                    this.translate.use(body);\n                    break;\n                case TYPES.APP_SETTINGS_CHANGED:\n                    this.settingsService.use(body);\n                    break;\n                case TYPES.ENTITY_ID_CHANGED:\n                    this.entityService.use(body);\n                    break;\n                case TYPES.SIDEBAR_COLLAPSED:\n                    this.sidebarService.isCollapsed.next(body === true);\n                    break;\n            }\n        });\n    }\n    ngOnDestroy() {\n        this.destroyed$.next(undefined);\n        this.destroyed$.complete();\n    }\n    init() {\n        this.translate.addLangs(this.config.langs.map(({ id }) => id));\n        if (this.config.defaultLang) {\n            this.translate.setDefaultLang(this.config.defaultLang);\n        }\n        this.router.events.pipe(filter(event => event instanceof NavigationEnd), map(event => event), map(event => {\n            const foundException = this.config?.lastLocationExceptions?.find(exception => event.url.startsWith(exception.url));\n            return foundException ? foundException.replaceTo : event.url;\n        }), takeUntil(this.destroyed$)).subscribe(url => {\n            const body = { hub: this.config.name, url: url };\n            this.postMessage(TYPES.LOCATION_CHANGED, body);\n        });\n        if (this.configService.bridge) {\n            this.create(this.configService.bridge);\n        }\n        this.postMessage(TYPES.HUB_LOADED);\n    }\n    initInactivityWatcher() {\n        merge(fromEvent(window, 'mousemove'), fromEvent(window, 'keypress')).pipe(throttleTime(10000), takeUntil(this.destroyed$)).subscribe(() => {\n            this.postMessage(TYPES.USER_ACTIVITY, new Date());\n        });\n        this.postMessage(TYPES.USER_ACTIVITY, new Date());\n    }\n    sendLogin(accessToken, grantedPermissions, twoFactor) {\n        this.postMessage(TYPES.LOGIN, {\n            accessToken,\n            grantedPermissions,\n            twoFactor\n        });\n    }\n    sendLogout() {\n        this.postMessage(TYPES.LOGOUT);\n    }\n    sendLocale(lang) {\n        this.postMessage(TYPES.LOCALE_CHANGED, lang);\n    }\n    sendAppSettings(settings) {\n        this.postMessage(TYPES.APP_SETTINGS_CHANGED, settings);\n    }\n    sendEntityId(entityId) {\n        this.postMessage(TYPES.ENTITY_ID_CHANGED, entityId);\n    }\n    sendTokenExpired(body) {\n        this.postMessage(TYPES.TOKEN_EXPIRED, body);\n    }\n    sendUnknownUserLocation(url) {\n        this.postMessage(TYPES.UNKNOWN_LOCATION, url);\n    }\n    postMessage(type, body) {\n        const message = {\n            target: 'bridge',\n            initiator: this.config.name,\n            type,\n            body\n        };\n        if (this.win && this.ready) {\n            this.win.postMessage(message, '*');\n        }\n        else {\n            console.log('[hub] queue message', type);\n            this.ready$.pipe(take(1)).subscribe(win => {\n                console.log('[hub] post queued message', type);\n                win.postMessage(message, '*');\n            });\n        }\n    }\n    create(url) {\n        const el = document.createElement('iframe');\n        el.src = url;\n        el.style.display = 'none';\n        const frame = document.body.appendChild(el);\n        console.log('[hub] frame created');\n        frame.onload = () => {\n            if (frame.contentWindow) {\n                console.log('[hub] frame loaded');\n                this.win = frame.contentWindow;\n                if (this.ready) {\n                    this.ready$.next(this.win);\n                }\n            }\n        };\n    }\n    static { this.ctorParameters = () => [\n        { type: Router },\n        { type: TranslateService },\n        { type: SwHubAuthService },\n        { type: SettingsService },\n        { type: SwHubEntityService },\n        { type: SwHubConfigService },\n        { type: SwuiSidebarService },\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_HUB_MESSAGE_CONFIG,] }] }\n    ]; }\n};\nSwHubInitService = __decorate([\n    Injectable()\n], SwHubInitService);\nexport { SwHubInitService };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,MAAM,EAAEC,UAAU,QAAQ,eAAe;AAClD,SAASC,aAAa,EAAEC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AACjE,SAASC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAEC,YAAY,QAAQ,gBAAgB;AACvF,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,YAAY,EAAEC,KAAK,QAAQ,qBAAqB;AACzD,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,IAAIC,gBAAgB,IAAAC,iBAAA,GAAG,MAAMD,gBAAgB,CAAC;EAC1CE,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,EAAEC,aAAa,EAAEC,aAAa,EAAEC,cAAc,EAAEC,MAAM,EAAE;IAC/G,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAG,IAAI9B,eAAe,CAAC,IAAI,CAAC;IAC5C,IAAI,CAAC+B,UAAU,GAAG,IAAI5B,OAAO,CAAC,CAAC;IAC/B,IAAI,CAAC6B,MAAM,GAAG,IAAI7B,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC8B,KAAK,GAAG,KAAK;IAClB,MAAMC,MAAM,GAAGjC,SAAS,CAACkC,MAAM,EAAE,SAAS,CAAC,CAACC,IAAI,CAAChC,MAAM,CAACiC,KAAK,IAAIrB,YAAY,CAACqB,KAAK,CAAC,CAAC,EAAEhC,GAAG,CAAC,CAAC;MAAEiC;IAAK,CAAC,KAAKA,IAAI,CAAC,EAAElC,MAAM,CAAC,CAAC;MAAEmC;IAAO,CAAC,KAAKA,MAAM,KAAKV,MAAM,CAACW,IAAI,IAAID,MAAM,KAAK,GAAG,CAAC,EAAE9B,GAAG,CAAC6B,IAAI,IAAIG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEJ,IAAI,CAAC,CAAC,EAAEhC,KAAK,CAAC,CAAC,CAAC;IAC9O,IAAI,CAACqC,QAAQ,GAAGT,MAAM,CAACE,IAAI,CAAChC,MAAM,CAAC,CAAC;MAAEwC;IAAK,CAAC,KAAK,CAAC,CAAC3B,KAAK,CAAC4B,aAAa,EAAE5B,KAAK,CAAC6B,KAAK,EAAE7B,KAAK,CAAC8B,MAAM,CAAC,CAACC,QAAQ,CAACJ,IAAI,CAAC,CAAC,CAAC;IACnHV,MAAM,CAACE,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACuB,UAAU,CAAC,CAAC,CAACkB,SAAS,CAAC,CAAC;MAAEL,IAAI;MAAEM;IAAK,CAAC,KAAK;MAClE,QAAQN,IAAI;QACR,KAAK3B,KAAK,CAAC4B,aAAa;UACpB,MAAM;YAAEM,WAAW;YAAEC,IAAI;YAAEC,QAAQ;YAAEC,UAAU;YAAEC,QAAQ;YAAEC,kBAAkB;YAAEC;UAAU,CAAC,GAAGP,IAAI;UACjG,IAAI,CAACjB,KAAK,GAAG,IAAI;UACjB,IAAI,IAAI,CAACyB,GAAG,EAAE;YACV,IAAI,CAAC1B,MAAM,CAAC2B,IAAI,CAAC,IAAI,CAACD,GAAG,CAAC;UAC9B;UACA,IAAI,CAAClC,WAAW,CAACoC,QAAQ,CAACT,WAAW,EAAEK,kBAAkB,EAAEC,SAAS,CAAC;UACrE,IAAIL,IAAI,EAAE;YACN,IAAI,CAAC7B,SAAS,CAACsC,GAAG,CAACT,IAAI,CAAC;UAC5B;UACA,IAAIC,QAAQ,EAAE;YACV,IAAI,CAAC5B,eAAe,CAACoC,GAAG,CAACR,QAAQ,CAAC;UACtC;UACA,IAAIC,UAAU,EAAE;YACZ,IAAI,CAACxB,WAAW,CAAC6B,IAAI,CAACL,UAAU,CAAC;UACrC;UACA,IAAIC,QAAQ,EAAE;YACV,IAAI,CAAC7B,aAAa,CAACoC,SAAS,CAACP,QAAQ,CAAC;UAC1C;UACA;QACJ,KAAKtC,KAAK,CAAC8C,KAAK;QAChB,KAAK9C,KAAK,CAAC6B,KAAK;UACZ,IAAI,CAACtB,WAAW,CAACoC,QAAQ,CAACV,IAAI,CAACC,WAAW,EAAED,IAAI,CAACM,kBAAkB,EAAEN,IAAI,CAACO,SAAS,CAAC;UACpF;QACJ,KAAKxC,KAAK,CAAC8B,MAAM;UACb,IAAI,CAACvB,WAAW,CAACwC,MAAM,CAAC,CAAC;UACzB,IAAI,IAAI,CAACrC,aAAa,CAACsC,QAAQ,EAAE;YAC7BC,QAAQ,CAACC,IAAI,GAAG,IAAI,CAACxC,aAAa,CAACsC,QAAQ;UAC/C;UACA;QACJ,KAAKhD,KAAK,CAACmD,cAAc;UACrB,IAAI,CAAC7C,SAAS,CAACsC,GAAG,CAACX,IAAI,CAAC;UACxB;QACJ,KAAKjC,KAAK,CAACoD,oBAAoB;UAC3B,IAAI,CAAC5C,eAAe,CAACoC,GAAG,CAACX,IAAI,CAAC;UAC9B;QACJ,KAAKjC,KAAK,CAACqD,iBAAiB;UACxB,IAAI,CAAC5C,aAAa,CAACmC,GAAG,CAACX,IAAI,CAAC;UAC5B;QACJ,KAAKjC,KAAK,CAACsD,iBAAiB;UACxB,IAAI,CAAC3C,cAAc,CAAC4C,WAAW,CAACb,IAAI,CAACT,IAAI,KAAK,IAAI,CAAC;UACnD;MACR;IACJ,CAAC,CAAC;EACN;EACAuB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC1C,UAAU,CAAC4B,IAAI,CAACe,SAAS,CAAC;IAC/B,IAAI,CAAC3C,UAAU,CAAC4C,QAAQ,CAAC,CAAC;EAC9B;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACrD,SAAS,CAACsD,QAAQ,CAAC,IAAI,CAAChD,MAAM,CAACiD,KAAK,CAACzE,GAAG,CAAC,CAAC;MAAE0E;IAAG,CAAC,KAAKA,EAAE,CAAC,CAAC;IAC9D,IAAI,IAAI,CAAClD,MAAM,CAACmD,WAAW,EAAE;MACzB,IAAI,CAACzD,SAAS,CAAC0D,cAAc,CAAC,IAAI,CAACpD,MAAM,CAACmD,WAAW,CAAC;IAC1D;IACA,IAAI,CAAC1D,MAAM,CAAC4D,MAAM,CAAC9C,IAAI,CAAChC,MAAM,CAACiC,KAAK,IAAIA,KAAK,YAAYxC,aAAa,CAAC,EAAEQ,GAAG,CAACgC,KAAK,IAAIA,KAAK,CAAC,EAAEhC,GAAG,CAACgC,KAAK,IAAI;MAAA,IAAA8C,YAAA;MACvG,MAAMC,cAAc,IAAAD,YAAA,GAAG,IAAI,CAACtD,MAAM,cAAAsD,YAAA,gBAAAA,YAAA,GAAXA,YAAA,CAAaE,sBAAsB,cAAAF,YAAA,uBAAnCA,YAAA,CAAqCG,IAAI,CAACC,SAAS,IAAIlD,KAAK,CAACmD,GAAG,CAACC,UAAU,CAACF,SAAS,CAACC,GAAG,CAAC,CAAC;MAClH,OAAOJ,cAAc,GAAGA,cAAc,CAACM,SAAS,GAAGrD,KAAK,CAACmD,GAAG;IAChE,CAAC,CAAC,EAAEhF,SAAS,CAAC,IAAI,CAACuB,UAAU,CAAC,CAAC,CAACkB,SAAS,CAACuC,GAAG,IAAI;MAC7C,MAAMtC,IAAI,GAAG;QAAEyC,GAAG,EAAE,IAAI,CAAC9D,MAAM,CAACW,IAAI;QAAEgD,GAAG,EAAEA;MAAI,CAAC;MAChD,IAAI,CAACI,WAAW,CAAC3E,KAAK,CAAC4E,gBAAgB,EAAE3C,IAAI,CAAC;IAClD,CAAC,CAAC;IACF,IAAI,IAAI,CAACvB,aAAa,CAACmE,MAAM,EAAE;MAC3B,IAAI,CAACC,MAAM,CAAC,IAAI,CAACpE,aAAa,CAACmE,MAAM,CAAC;IAC1C;IACA,IAAI,CAACF,WAAW,CAAC3E,KAAK,CAAC+E,UAAU,CAAC;EACtC;EACAC,qBAAqBA,CAAA,EAAG;IACpB/F,KAAK,CAACD,SAAS,CAACkC,MAAM,EAAE,WAAW,CAAC,EAAElC,SAAS,CAACkC,MAAM,EAAE,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC1B,YAAY,CAAC,KAAK,CAAC,EAAEF,SAAS,CAAC,IAAI,CAACuB,UAAU,CAAC,CAAC,CAACkB,SAAS,CAAC,MAAM;MACvI,IAAI,CAAC2C,WAAW,CAAC3E,KAAK,CAACiF,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC;IACF,IAAI,CAACP,WAAW,CAAC3E,KAAK,CAACiF,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAAC;EACrD;EACAC,SAASA,CAACjD,WAAW,EAAEK,kBAAkB,EAAEC,SAAS,EAAE;IAClD,IAAI,CAACmC,WAAW,CAAC3E,KAAK,CAAC6B,KAAK,EAAE;MAC1BK,WAAW;MACXK,kBAAkB;MAClBC;IACJ,CAAC,CAAC;EACN;EACA4C,UAAUA,CAAA,EAAG;IACT,IAAI,CAACT,WAAW,CAAC3E,KAAK,CAAC8B,MAAM,CAAC;EAClC;EACAuD,UAAUA,CAAClD,IAAI,EAAE;IACb,IAAI,CAACwC,WAAW,CAAC3E,KAAK,CAACmD,cAAc,EAAEhB,IAAI,CAAC;EAChD;EACAmD,eAAeA,CAAClD,QAAQ,EAAE;IACtB,IAAI,CAACuC,WAAW,CAAC3E,KAAK,CAACoD,oBAAoB,EAAEhB,QAAQ,CAAC;EAC1D;EACAmD,YAAYA,CAACjD,QAAQ,EAAE;IACnB,IAAI,CAACqC,WAAW,CAAC3E,KAAK,CAACqD,iBAAiB,EAAEf,QAAQ,CAAC;EACvD;EACAkD,gBAAgBA,CAACvD,IAAI,EAAE;IACnB,IAAI,CAAC0C,WAAW,CAAC3E,KAAK,CAACyF,aAAa,EAAExD,IAAI,CAAC;EAC/C;EACAyD,uBAAuBA,CAACnB,GAAG,EAAE;IACzB,IAAI,CAACI,WAAW,CAAC3E,KAAK,CAAC2F,gBAAgB,EAAEpB,GAAG,CAAC;EACjD;EACAI,WAAWA,CAAChD,IAAI,EAAEM,IAAI,EAAE;IACpB,MAAM2D,OAAO,GAAG;MACZtE,MAAM,EAAE,QAAQ;MAChBuE,SAAS,EAAE,IAAI,CAACjF,MAAM,CAACW,IAAI;MAC3BI,IAAI;MACJM;IACJ,CAAC;IACD,IAAI,IAAI,CAACQ,GAAG,IAAI,IAAI,CAACzB,KAAK,EAAE;MACxB,IAAI,CAACyB,GAAG,CAACkC,WAAW,CAACiB,OAAO,EAAE,GAAG,CAAC;IACtC,CAAC,MACI;MACDpE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEE,IAAI,CAAC;MACxC,IAAI,CAACZ,MAAM,CAACI,IAAI,CAAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0C,SAAS,CAACS,GAAG,IAAI;QACvCjB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEE,IAAI,CAAC;QAC9Cc,GAAG,CAACkC,WAAW,CAACiB,OAAO,EAAE,GAAG,CAAC;MACjC,CAAC,CAAC;IACN;EACJ;EACAd,MAAMA,CAACP,GAAG,EAAE;IACR,MAAMuB,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC3CF,EAAE,CAACG,GAAG,GAAG1B,GAAG;IACZuB,EAAE,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;IACzB,MAAMC,KAAK,GAAGL,QAAQ,CAAC9D,IAAI,CAACoE,WAAW,CAACP,EAAE,CAAC;IAC3CtE,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC2E,KAAK,CAACE,MAAM,GAAG,MAAM;MACjB,IAAIF,KAAK,CAACG,aAAa,EAAE;QACrB/E,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;QACjC,IAAI,CAACgB,GAAG,GAAG2D,KAAK,CAACG,aAAa;QAC9B,IAAI,IAAI,CAACvF,KAAK,EAAE;UACZ,IAAI,CAACD,MAAM,CAAC2B,IAAI,CAAC,IAAI,CAACD,GAAG,CAAC;QAC9B;MACJ;IACJ,CAAC;EACL;AAWJ,CAAC,EAVYtC,iBAAA,CAAKqG,cAAc,GAAG,MAAM,CACjC;EAAE7E,IAAI,EAAE9C;AAAO,CAAC,EAChB;EAAE8C,IAAI,EAAE7C;AAAiB,CAAC,EAC1B;EAAE6C,IAAI,EAAE/B;AAAiB,CAAC,EAC1B;EAAE+B,IAAI,EAAEhC;AAAgB,CAAC,EACzB;EAAEgC,IAAI,EAAE7B;AAAmB,CAAC,EAC5B;EAAE6B,IAAI,EAAE9B;AAAmB,CAAC,EAC5B;EAAE8B,IAAI,EAAEjC;AAAmB,CAAC,EAC5B;EAAEiC,IAAI,EAAE8B,SAAS;EAAEgD,UAAU,EAAE,CAAC;IAAE9E,IAAI,EAAEjD,MAAM;IAAEgI,IAAI,EAAE,CAACzG,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,iBAAA,CACJ;AACDD,gBAAgB,GAAGzB,UAAU,CAAC,CAC1BE,UAAU,CAAC,CAAC,CACf,EAAEuB,gBAAgB,CAAC;AACpB,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}