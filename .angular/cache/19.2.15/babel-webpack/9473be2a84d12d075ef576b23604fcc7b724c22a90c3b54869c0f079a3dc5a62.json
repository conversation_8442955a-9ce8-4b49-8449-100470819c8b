{"ast": null, "code": "var _InputVideoUrlComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-video-url.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-video-url.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, Input, ViewChild } from '@angular/core';\nimport { DomSanitizer } from '@angular/platform-browser';\nconst urlValidation = control => {\n  const regexp = /(^(?:http(s)?:\\/\\/)?([\\{]?)+[\\w]+([\\-\\.]{1}[\\w]+)*\\.[\\w]{2,5}(:[\\d]{1,5})?([\\}]?)+(\\/.*)?$)|(^(?:http(s)?:\\/\\/)?([\\{]?)+[\\w]+([\\}]?)+(\\/.*)?$)/gm;\n  const value = control.value === null ? '' : control.value.toString();\n  if (!value || value.match(regexp)) {\n    return null;\n  }\n  return {\n    'urlIsNotCorrect': true\n  };\n};\nconst videoUrlValidation = control => {\n  const value = control.value === null ? '' : control.value.toString();\n  if (value) {\n    const extension = value.substring(value.lastIndexOf('.') + 1);\n    if (['mp4', '3gp', 'ogg'].includes(extension)) {\n      return null;\n    }\n    return {\n      'urlIsNotVideo': true\n    };\n  }\n  return null;\n};\nlet InputVideoUrlComponent = (_InputVideoUrlComponent = class InputVideoUrlComponent {\n  set componentOptions(value) {\n    var _value$validation;\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.defaultValue = value === null || value === void 0 ? void 0 : value.defaultValue;\n    this.required = value === null || value === void 0 ? void 0 : value.required;\n    this.errorMessages = value === null || value === void 0 || (_value$validation = value.validation) === null || _value$validation === void 0 ? void 0 : _value$validation.messages;\n  }\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n  }\n  ngOnInit() {\n    var _this$control;\n    (_this$control = this.control) === null || _this$control === void 0 || _this$control.addValidators([urlValidation, videoUrlValidation]);\n  }\n  get videoUrl() {\n    var _this$control2;\n    return (_this$control2 = this.control) === null || _this$control2 === void 0 ? void 0 : _this$control2.value;\n  }\n  get safeVideoUrl() {\n    return this.sanitizer.bypassSecurityTrustUrl(this.videoUrl || '');\n  }\n}, _InputVideoUrlComponent.ctorParameters = () => [{\n  type: DomSanitizer\n}], _InputVideoUrlComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }],\n  fileInputRef: [{\n    type: ViewChild,\n    args: ['fileInput']\n  }]\n}, _InputVideoUrlComponent);\nInputVideoUrlComponent = __decorate([Component({\n  selector: 'lib-input-video-url',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputVideoUrlComponent);\nexport { InputVideoUrlComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectionStrategy", "Component", "Input", "ViewChild", "Dom<PERSON><PERSON><PERSON>zer", "urlValidation", "control", "regexp", "value", "toString", "match", "videoUrlValidation", "extension", "substring", "lastIndexOf", "includes", "InputVideoUrlComponent", "_InputVideoUrlComponent", "componentOptions", "_value$validation", "title", "defaultValue", "required", "errorMessages", "validation", "messages", "constructor", "sanitizer", "id", "readonly", "submitted", "ngOnInit", "_this$control", "addValidators", "videoUrl", "_this$control2", "safeVideoUrl", "bypassSecurityTrustUrl", "ctorParameters", "type", "propDecorators", "fileInputRef", "args", "selector", "template", "changeDetection", "OnPush", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-video-url/input-video-url.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-video-url.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-video-url.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, Input, ViewChild } from '@angular/core';\nimport { DomSanitizer } from '@angular/platform-browser';\nconst urlValidation = (control) => {\n    const regexp = /(^(?:http(s)?:\\/\\/)?([\\{]?)+[\\w]+([\\-\\.]{1}[\\w]+)*\\.[\\w]{2,5}(:[\\d]{1,5})?([\\}]?)+(\\/.*)?$)|(^(?:http(s)?:\\/\\/)?([\\{]?)+[\\w]+([\\}]?)+(\\/.*)?$)/gm;\n    const value = control.value === null ? '' : control.value.toString();\n    if (!value || value.match(regexp)) {\n        return null;\n    }\n    return { 'urlIsNotCorrect': true };\n};\nconst videoUrlValidation = (control) => {\n    const value = control.value === null ? '' : control.value.toString();\n    if (value) {\n        const extension = value.substring(value.lastIndexOf('.') + 1);\n        if (['mp4', '3gp', 'ogg'].includes(extension)) {\n            return null;\n        }\n        return { 'urlIsNotVideo': true };\n    }\n    return null;\n};\nlet InputVideoUrlComponent = class InputVideoUrlComponent {\n    set componentOptions(value) {\n        this.title = value?.title;\n        this.defaultValue = value?.defaultValue;\n        this.required = value?.required;\n        this.errorMessages = value?.validation?.messages;\n    }\n    constructor(sanitizer) {\n        this.sanitizer = sanitizer;\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n    }\n    ngOnInit() {\n        this.control?.addValidators([urlValidation, videoUrlValidation]);\n    }\n    get videoUrl() {\n        return this.control?.value;\n    }\n    get safeVideoUrl() {\n        return this.sanitizer.bypassSecurityTrustUrl(this.videoUrl || '');\n    }\n    static { this.ctorParameters = () => [\n        { type: DomSanitizer }\n    ]; }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }],\n        fileInputRef: [{ type: ViewChild, args: ['fileInput',] }]\n    }; }\n};\nInputVideoUrlComponent = __decorate([\n    Component({\n        selector: 'lib-input-video-url',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputVideoUrlComponent);\nexport { InputVideoUrlComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,SAASC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AACpF,SAASC,YAAY,QAAQ,2BAA2B;AACxD,MAAMC,aAAa,GAAIC,OAAO,IAAK;EAC/B,MAAMC,MAAM,GAAG,kJAAkJ;EACjK,MAAMC,KAAK,GAAGF,OAAO,CAACE,KAAK,KAAK,IAAI,GAAG,EAAE,GAAGF,OAAO,CAACE,KAAK,CAACC,QAAQ,CAAC,CAAC;EACpE,IAAI,CAACD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACH,MAAM,CAAC,EAAE;IAC/B,OAAO,IAAI;EACf;EACA,OAAO;IAAE,iBAAiB,EAAE;EAAK,CAAC;AACtC,CAAC;AACD,MAAMI,kBAAkB,GAAIL,OAAO,IAAK;EACpC,MAAME,KAAK,GAAGF,OAAO,CAACE,KAAK,KAAK,IAAI,GAAG,EAAE,GAAGF,OAAO,CAACE,KAAK,CAACC,QAAQ,CAAC,CAAC;EACpE,IAAID,KAAK,EAAE;IACP,MAAMI,SAAS,GAAGJ,KAAK,CAACK,SAAS,CAACL,KAAK,CAACM,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7D,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACH,SAAS,CAAC,EAAE;MAC3C,OAAO,IAAI;IACf;IACA,OAAO;MAAE,eAAe,EAAE;IAAK,CAAC;EACpC;EACA,OAAO,IAAI;AACf,CAAC;AACD,IAAII,sBAAsB,IAAAC,uBAAA,GAAG,MAAMD,sBAAsB,CAAC;EACtD,IAAIE,gBAAgBA,CAACV,KAAK,EAAE;IAAA,IAAAW,iBAAA;IACxB,IAAI,CAACC,KAAK,GAAGZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,KAAK;IACzB,IAAI,CAACC,YAAY,GAAGb,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEa,YAAY;IACvC,IAAI,CAACC,QAAQ,GAAGd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,QAAQ;IAC/B,IAAI,CAACC,aAAa,GAAGf,KAAK,aAALA,KAAK,gBAAAW,iBAAA,GAALX,KAAK,CAAEgB,UAAU,cAAAL,iBAAA,uBAAjBA,iBAAA,CAAmBM,QAAQ;EACpD;EACAC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;EAC1B;EACAC,QAAQA,CAAA,EAAG;IAAA,IAAAC,aAAA;IACP,CAAAA,aAAA,OAAI,CAAC1B,OAAO,cAAA0B,aAAA,eAAZA,aAAA,CAAcC,aAAa,CAAC,CAAC5B,aAAa,EAAEM,kBAAkB,CAAC,CAAC;EACpE;EACA,IAAIuB,QAAQA,CAAA,EAAG;IAAA,IAAAC,cAAA;IACX,QAAAA,cAAA,GAAO,IAAI,CAAC7B,OAAO,cAAA6B,cAAA,uBAAZA,cAAA,CAAc3B,KAAK;EAC9B;EACA,IAAI4B,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACT,SAAS,CAACU,sBAAsB,CAAC,IAAI,CAACH,QAAQ,IAAI,EAAE,CAAC;EACrE;AAYJ,CAAC,EAXYjB,uBAAA,CAAKqB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEnC;AAAa,CAAC,CACzB,EACQa,uBAAA,CAAKuB,cAAc,GAAG;EAC3BlC,OAAO,EAAE,CAAC;IAAEiC,IAAI,EAAErC;EAAM,CAAC,CAAC;EAC1B0B,EAAE,EAAE,CAAC;IAAEW,IAAI,EAAErC;EAAM,CAAC,CAAC;EACrB2B,QAAQ,EAAE,CAAC;IAAEU,IAAI,EAAErC;EAAM,CAAC,CAAC;EAC3B4B,SAAS,EAAE,CAAC;IAAES,IAAI,EAAErC;EAAM,CAAC,CAAC;EAC5BgB,gBAAgB,EAAE,CAAC;IAAEqB,IAAI,EAAErC;EAAM,CAAC,CAAC;EACnCuC,YAAY,EAAE,CAAC;IAAEF,IAAI,EAAEpC,SAAS;IAAEuC,IAAI,EAAE,CAAC,WAAW;EAAG,CAAC;AAC5D,CAAC,EAAAzB,uBAAA,CACJ;AACDD,sBAAsB,GAAGnB,UAAU,CAAC,CAChCI,SAAS,CAAC;EACN0C,QAAQ,EAAE,qBAAqB;EAC/BC,QAAQ,EAAE9C,oBAAoB;EAC9B+C,eAAe,EAAE7C,uBAAuB,CAAC8C,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACjD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEiB,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}