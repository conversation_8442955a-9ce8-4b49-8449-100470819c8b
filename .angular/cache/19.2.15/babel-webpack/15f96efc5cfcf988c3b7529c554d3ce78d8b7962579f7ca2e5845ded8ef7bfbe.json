{"ast": null, "code": "var _SwuiCalendarComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-calendar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-calendar.component.scss?ngResource\";\nimport { Component, forwardRef, HostBinding, HostListener, Input } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as moment from 'moment';\nfunction toMoment(value) {\n  if (typeof value === 'undefined' || value === null) {\n    return null;\n  }\n  if (moment.isMoment(value)) {\n    return value;\n  }\n  const date = moment.parseZone(value);\n  if (date.isValid()) {\n    return date;\n  }\n  return null;\n}\nlet SwuiCalendarComponent = (_SwuiCalendarComponent = class SwuiCalendarComponent {\n  set timeZone(val) {\n    if (!val) {\n      return;\n    }\n    this._timeZone = val;\n    const current = moment.tz(val);\n    if (current.isValid()) {\n      this.currentDate = current.clone();\n      this.setMonth(this.currentDate);\n    }\n  }\n  get timeZone() {\n    return this._timeZone;\n  }\n  constructor() {\n    this.weekDayNames = moment.weekdaysShort();\n    this.monthNames = moment.monthsShort();\n    this.currentDate = moment.utc();\n    this.currentMonth = [];\n    this.isCalendarDisabled = false;\n    this.tabindex = 0;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.setMonth(this.currentDate);\n  }\n  onblur() {\n    this.onTouched();\n  }\n  writeValue(value) {\n    const date = toMoment(value);\n    if (date && !this.isDayDisabled(date)) {\n      this.selectedDate = date.clone();\n      if (!this.selectedDate.isSame(this.currentDate, 'month')) {\n        this.setMonth(this.selectedDate.clone());\n      }\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.isCalendarDisabled = disabled;\n  }\n  nextMonth(event) {\n    event.preventDefault();\n    this.setMonth(this.currentDate.add(1, 'months'));\n  }\n  prevMonth(event) {\n    event.preventDefault();\n    this.setMonth(this.currentDate.add(-1, 'months'));\n  }\n  selectDay(day, event) {\n    if (event) {\n      event.preventDefault();\n    }\n    if (day && !this.isDayDisabled(day)) {\n      this.selectedDate = day.clone();\n      if (this.selectedDate && !this.selectedDate.isSame(this.currentDate, 'month')) {\n        this.setMonth(this.selectedDate.clone());\n      }\n      this.onChange(this.selectedDate);\n    }\n  }\n  isDayToday(day) {\n    return day ? day.isSame(this.timeZone ? moment.tz(this.timeZone) : moment.utc(), 'date') : false;\n  }\n  isDaySelected(day) {\n    return day && this.selectedDate ? day.isSame(this.selectedDate, 'date') : false;\n  }\n  isDayDisabled(day) {\n    const minDate = this.minDate ? toMoment(this.minDate) : undefined;\n    const maxDate = this.maxDate ? toMoment(this.maxDate) : undefined;\n    return day && minDate && day.isBefore(minDate, 'date') || day && maxDate && day.isAfter(maxDate, 'date') || false;\n  }\n  setMonth(date) {\n    const firstDay = date.clone().startOf('month');\n    const lastDay = date.clone().endOf('month');\n    const result = [];\n    while (firstDay.date() <= lastDay.date()) {\n      if (!result.length || !firstDay.day()) {\n        result.push([]);\n      }\n      result[result.length - 1][firstDay.day()] = firstDay.clone();\n      if (firstDay.date() === lastDay.date()) {\n        break;\n      } else {\n        firstDay.add(1, 'days');\n      }\n    }\n    this.currentDate = date.clone().startOf('day');\n    this.currentMonth = result;\n  }\n}, _SwuiCalendarComponent.ctorParameters = () => [], _SwuiCalendarComponent.propDecorators = {\n  minDate: [{\n    type: Input\n  }],\n  maxDate: [{\n    type: Input\n  }],\n  timeZone: [{\n    type: Input\n  }],\n  tabindex: [{\n    type: HostBinding,\n    args: ['attr.tabindex']\n  }],\n  onblur: [{\n    type: HostListener,\n    args: ['blur']\n  }]\n}, _SwuiCalendarComponent);\nSwuiCalendarComponent = __decorate([Component({\n  selector: 'lib-swui-calendar',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SwuiCalendarComponent),\n    multi: true\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiCalendarComponent);\nexport { SwuiCalendarComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "forwardRef", "HostBinding", "HostListener", "Input", "NG_VALUE_ACCESSOR", "moment", "toMoment", "value", "isMoment", "date", "parseZone", "<PERSON><PERSON><PERSON><PERSON>", "SwuiCalendarComponent", "_SwuiCalendarComponent", "timeZone", "val", "_timeZone", "current", "tz", "currentDate", "clone", "setMonth", "constructor", "weekDayNames", "weekdaysShort", "monthNames", "monthsShort", "utc", "currentMonth", "isCalendarDisabled", "tabindex", "onChange", "onTouched", "onblur", "writeValue", "isDayDisabled", "selectedDate", "isSame", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "disabled", "nextMonth", "event", "preventDefault", "add", "prevMonth", "selectDay", "day", "isDayToday", "isDaySelected", "minDate", "undefined", "maxDate", "isBefore", "isAfter", "firstDay", "startOf", "lastDay", "endOf", "result", "length", "push", "ctorParameters", "propDecorators", "type", "args", "selector", "template", "providers", "provide", "useExisting", "multi", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-calendar/swui-calendar.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-calendar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-calendar.component.scss?ngResource\";\nimport { Component, forwardRef, HostBinding, HostListener, Input } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as moment from 'moment';\nfunction toMoment(value) {\n    if (typeof value === 'undefined' || value === null) {\n        return null;\n    }\n    if (moment.isMoment(value)) {\n        return value;\n    }\n    const date = moment.parseZone(value);\n    if (date.isValid()) {\n        return date;\n    }\n    return null;\n}\nlet SwuiCalendarComponent = class SwuiCalendarComponent {\n    set timeZone(val) {\n        if (!val) {\n            return;\n        }\n        this._timeZone = val;\n        const current = moment.tz(val);\n        if (current.isValid()) {\n            this.currentDate = current.clone();\n            this.setMonth(this.currentDate);\n        }\n    }\n    get timeZone() {\n        return this._timeZone;\n    }\n    constructor() {\n        this.weekDayNames = moment.weekdaysShort();\n        this.monthNames = moment.monthsShort();\n        this.currentDate = moment.utc();\n        this.currentMonth = [];\n        this.isCalendarDisabled = false;\n        this.tabindex = 0;\n        this.onChange = (() => {\n        });\n        this.onTouched = () => {\n        };\n        this.setMonth(this.currentDate);\n    }\n    onblur() {\n        this.onTouched();\n    }\n    writeValue(value) {\n        const date = toMoment(value);\n        if (date && !this.isDayDisabled(date)) {\n            this.selectedDate = date.clone();\n            if (!this.selectedDate.isSame(this.currentDate, 'month')) {\n                this.setMonth(this.selectedDate.clone());\n            }\n        }\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(disabled) {\n        this.isCalendarDisabled = disabled;\n    }\n    nextMonth(event) {\n        event.preventDefault();\n        this.setMonth(this.currentDate.add(1, 'months'));\n    }\n    prevMonth(event) {\n        event.preventDefault();\n        this.setMonth(this.currentDate.add(-1, 'months'));\n    }\n    selectDay(day, event) {\n        if (event) {\n            event.preventDefault();\n        }\n        if (day && !this.isDayDisabled(day)) {\n            this.selectedDate = day.clone();\n            if (this.selectedDate && !this.selectedDate.isSame(this.currentDate, 'month')) {\n                this.setMonth(this.selectedDate.clone());\n            }\n            this.onChange(this.selectedDate);\n        }\n    }\n    isDayToday(day) {\n        return day ? day.isSame(this.timeZone ? moment.tz(this.timeZone) : moment.utc(), 'date') : false;\n    }\n    isDaySelected(day) {\n        return day && this.selectedDate ? day.isSame(this.selectedDate, 'date') : false;\n    }\n    isDayDisabled(day) {\n        const minDate = this.minDate ? toMoment(this.minDate) : undefined;\n        const maxDate = this.maxDate ? toMoment(this.maxDate) : undefined;\n        return (day && minDate && day.isBefore(minDate, 'date')) ||\n            (day && maxDate && day.isAfter(maxDate, 'date')) ||\n            false;\n    }\n    setMonth(date) {\n        const firstDay = date.clone().startOf('month');\n        const lastDay = date.clone().endOf('month');\n        const result = [];\n        while (firstDay.date() <= lastDay.date()) {\n            if (!result.length || !firstDay.day()) {\n                result.push([]);\n            }\n            result[result.length - 1][firstDay.day()] = firstDay.clone();\n            if (firstDay.date() === lastDay.date()) {\n                break;\n            }\n            else {\n                firstDay.add(1, 'days');\n            }\n        }\n        this.currentDate = date.clone().startOf('day');\n        this.currentMonth = result;\n    }\n    static { this.ctorParameters = () => []; }\n    static { this.propDecorators = {\n        minDate: [{ type: Input }],\n        maxDate: [{ type: Input }],\n        timeZone: [{ type: Input }],\n        tabindex: [{ type: HostBinding, args: ['attr.tabindex',] }],\n        onblur: [{ type: HostListener, args: ['blur',] }]\n    }; }\n};\nSwuiCalendarComponent = __decorate([\n    Component({\n        selector: 'lib-swui-calendar',\n        template: __NG_CLI_RESOURCE__0,\n        providers: [\n            {\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => SwuiCalendarComponent),\n                multi: true\n            },\n        ],\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiCalendarComponent);\nexport { SwuiCalendarComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,SAASC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,KAAK,QAAQ,eAAe;AACvF,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,QAAQA,CAACC,KAAK,EAAE;EACrB,IAAI,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,EAAE;IAChD,OAAO,IAAI;EACf;EACA,IAAIF,MAAM,CAACG,QAAQ,CAACD,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK;EAChB;EACA,MAAME,IAAI,GAAGJ,MAAM,CAACK,SAAS,CAACH,KAAK,CAAC;EACpC,IAAIE,IAAI,CAACE,OAAO,CAAC,CAAC,EAAE;IAChB,OAAOF,IAAI;EACf;EACA,OAAO,IAAI;AACf;AACA,IAAIG,qBAAqB,IAAAC,sBAAA,GAAG,MAAMD,qBAAqB,CAAC;EACpD,IAAIE,QAAQA,CAACC,GAAG,EAAE;IACd,IAAI,CAACA,GAAG,EAAE;MACN;IACJ;IACA,IAAI,CAACC,SAAS,GAAGD,GAAG;IACpB,MAAME,OAAO,GAAGZ,MAAM,CAACa,EAAE,CAACH,GAAG,CAAC;IAC9B,IAAIE,OAAO,CAACN,OAAO,CAAC,CAAC,EAAE;MACnB,IAAI,CAACQ,WAAW,GAAGF,OAAO,CAACG,KAAK,CAAC,CAAC;MAClC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC;IACnC;EACJ;EACA,IAAIL,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACE,SAAS;EACzB;EACAM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAGlB,MAAM,CAACmB,aAAa,CAAC,CAAC;IAC1C,IAAI,CAACC,UAAU,GAAGpB,MAAM,CAACqB,WAAW,CAAC,CAAC;IACtC,IAAI,CAACP,WAAW,GAAGd,MAAM,CAACsB,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,QAAQ,GAAI,MAAM,CACvB,CAAE;IACF,IAAI,CAACC,SAAS,GAAG,MAAM,CACvB,CAAC;IACD,IAAI,CAACX,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC;EACnC;EACAc,MAAMA,CAAA,EAAG;IACL,IAAI,CAACD,SAAS,CAAC,CAAC;EACpB;EACAE,UAAUA,CAAC3B,KAAK,EAAE;IACd,MAAME,IAAI,GAAGH,QAAQ,CAACC,KAAK,CAAC;IAC5B,IAAIE,IAAI,IAAI,CAAC,IAAI,CAAC0B,aAAa,CAAC1B,IAAI,CAAC,EAAE;MACnC,IAAI,CAAC2B,YAAY,GAAG3B,IAAI,CAACW,KAAK,CAAC,CAAC;MAChC,IAAI,CAAC,IAAI,CAACgB,YAAY,CAACC,MAAM,CAAC,IAAI,CAAClB,WAAW,EAAE,OAAO,CAAC,EAAE;QACtD,IAAI,CAACE,QAAQ,CAAC,IAAI,CAACe,YAAY,CAAChB,KAAK,CAAC,CAAC,CAAC;MAC5C;IACJ;EACJ;EACAkB,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACR,QAAQ,GAAGQ,EAAE;EACtB;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACP,SAAS,GAAGO,EAAE;EACvB;EACAE,gBAAgBA,CAACC,QAAQ,EAAE;IACvB,IAAI,CAACb,kBAAkB,GAAGa,QAAQ;EACtC;EACAC,SAASA,CAACC,KAAK,EAAE;IACbA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACxB,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC2B,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;EACpD;EACAC,SAASA,CAACH,KAAK,EAAE;IACbA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACxB,QAAQ,CAAC,IAAI,CAACF,WAAW,CAAC2B,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;EACrD;EACAE,SAASA,CAACC,GAAG,EAAEL,KAAK,EAAE;IAClB,IAAIA,KAAK,EAAE;MACPA,KAAK,CAACC,cAAc,CAAC,CAAC;IAC1B;IACA,IAAII,GAAG,IAAI,CAAC,IAAI,CAACd,aAAa,CAACc,GAAG,CAAC,EAAE;MACjC,IAAI,CAACb,YAAY,GAAGa,GAAG,CAAC7B,KAAK,CAAC,CAAC;MAC/B,IAAI,IAAI,CAACgB,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAACC,MAAM,CAAC,IAAI,CAAClB,WAAW,EAAE,OAAO,CAAC,EAAE;QAC3E,IAAI,CAACE,QAAQ,CAAC,IAAI,CAACe,YAAY,CAAChB,KAAK,CAAC,CAAC,CAAC;MAC5C;MACA,IAAI,CAACW,QAAQ,CAAC,IAAI,CAACK,YAAY,CAAC;IACpC;EACJ;EACAc,UAAUA,CAACD,GAAG,EAAE;IACZ,OAAOA,GAAG,GAAGA,GAAG,CAACZ,MAAM,CAAC,IAAI,CAACvB,QAAQ,GAAGT,MAAM,CAACa,EAAE,CAAC,IAAI,CAACJ,QAAQ,CAAC,GAAGT,MAAM,CAACsB,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,KAAK;EACpG;EACAwB,aAAaA,CAACF,GAAG,EAAE;IACf,OAAOA,GAAG,IAAI,IAAI,CAACb,YAAY,GAAGa,GAAG,CAACZ,MAAM,CAAC,IAAI,CAACD,YAAY,EAAE,MAAM,CAAC,GAAG,KAAK;EACnF;EACAD,aAAaA,CAACc,GAAG,EAAE;IACf,MAAMG,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG9C,QAAQ,CAAC,IAAI,CAAC8C,OAAO,CAAC,GAAGC,SAAS;IACjE,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO,GAAGhD,QAAQ,CAAC,IAAI,CAACgD,OAAO,CAAC,GAAGD,SAAS;IACjE,OAAQJ,GAAG,IAAIG,OAAO,IAAIH,GAAG,CAACM,QAAQ,CAACH,OAAO,EAAE,MAAM,CAAC,IAClDH,GAAG,IAAIK,OAAO,IAAIL,GAAG,CAACO,OAAO,CAACF,OAAO,EAAE,MAAM,CAAE,IAChD,KAAK;EACb;EACAjC,QAAQA,CAACZ,IAAI,EAAE;IACX,MAAMgD,QAAQ,GAAGhD,IAAI,CAACW,KAAK,CAAC,CAAC,CAACsC,OAAO,CAAC,OAAO,CAAC;IAC9C,MAAMC,OAAO,GAAGlD,IAAI,CAACW,KAAK,CAAC,CAAC,CAACwC,KAAK,CAAC,OAAO,CAAC;IAC3C,MAAMC,MAAM,GAAG,EAAE;IACjB,OAAOJ,QAAQ,CAAChD,IAAI,CAAC,CAAC,IAAIkD,OAAO,CAAClD,IAAI,CAAC,CAAC,EAAE;MACtC,IAAI,CAACoD,MAAM,CAACC,MAAM,IAAI,CAACL,QAAQ,CAACR,GAAG,CAAC,CAAC,EAAE;QACnCY,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC;MACnB;MACAF,MAAM,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAACL,QAAQ,CAACR,GAAG,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAACrC,KAAK,CAAC,CAAC;MAC5D,IAAIqC,QAAQ,CAAChD,IAAI,CAAC,CAAC,KAAKkD,OAAO,CAAClD,IAAI,CAAC,CAAC,EAAE;QACpC;MACJ,CAAC,MACI;QACDgD,QAAQ,CAACX,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;MAC3B;IACJ;IACA,IAAI,CAAC3B,WAAW,GAAGV,IAAI,CAACW,KAAK,CAAC,CAAC,CAACsC,OAAO,CAAC,KAAK,CAAC;IAC9C,IAAI,CAAC9B,YAAY,GAAGiC,MAAM;EAC9B;AASJ,CAAC,EARYhD,sBAAA,CAAKmD,cAAc,GAAG,MAAM,EAAE,EAC9BnD,sBAAA,CAAKoD,cAAc,GAAG;EAC3Bb,OAAO,EAAE,CAAC;IAAEc,IAAI,EAAE/D;EAAM,CAAC,CAAC;EAC1BmD,OAAO,EAAE,CAAC;IAAEY,IAAI,EAAE/D;EAAM,CAAC,CAAC;EAC1BW,QAAQ,EAAE,CAAC;IAAEoD,IAAI,EAAE/D;EAAM,CAAC,CAAC;EAC3B2B,QAAQ,EAAE,CAAC;IAAEoC,IAAI,EAAEjE,WAAW;IAAEkE,IAAI,EAAE,CAAC,eAAe;EAAG,CAAC,CAAC;EAC3DlC,MAAM,EAAE,CAAC;IAAEiC,IAAI,EAAEhE,YAAY;IAAEiE,IAAI,EAAE,CAAC,MAAM;EAAG,CAAC;AACpD,CAAC,EAAAtD,sBAAA,CACJ;AACDD,qBAAqB,GAAGhB,UAAU,CAAC,CAC/BG,SAAS,CAAC;EACNqE,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAExE,oBAAoB;EAC9ByE,SAAS,EAAE,CACP;IACIC,OAAO,EAAEnE,iBAAiB;IAC1BoE,WAAW,EAAExE,UAAU,CAAC,MAAMY,qBAAqB,CAAC;IACpD6D,KAAK,EAAE;EACX,CAAC,CACJ;EACDC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC7E,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEc,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}