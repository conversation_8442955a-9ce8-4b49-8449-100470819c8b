{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiControlMessagesComponent;\nimport { __decorate } from \"tslib\";\nimport { Component, Inject, Input, Optional } from '@angular/core';\nimport { SWUI_CONTROL_MESSAGES } from './swui-control-messages.token';\nimport { SwuiIsControlInvalidService } from '../swui-is-control-invalid/swui-is-control-invalid.service';\nlet SwuiControlMessagesComponent = (_SwuiControlMessagesComponent = class SwuiControlMessagesComponent {\n  set setMessages(value) {\n    if (value) {\n      this.messages = _objectSpread(_objectSpread({}, this.config || {}), value);\n    }\n  }\n  constructor(config, service) {\n    this.config = config;\n    this.service = service;\n    this.force = false;\n    this.messages = config || {};\n  }\n  get errorMessage() {\n    const control = this.control;\n    if (control) {\n      if (this.force && !control.touched) {\n        control.markAsTouched();\n      }\n      const [key, values] = this.error(control);\n      this.params = values;\n      return key && this.messages[key];\n    }\n    return undefined;\n  }\n  error(control) {\n    if (this.isControlInvalid(control)) {\n      return Object.entries(control.errors || {}).shift() || [undefined, undefined];\n    }\n    return [undefined, undefined];\n  }\n  isControlInvalid(control) {\n    if (this.service) {\n      return this.service.isControlInvalid(control);\n    } else {\n      return control.touched && control.invalid;\n    }\n  }\n}, _SwuiControlMessagesComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [SWUI_CONTROL_MESSAGES]\n  }]\n}, {\n  type: SwuiIsControlInvalidService,\n  decorators: [{\n    type: Optional\n  }]\n}], _SwuiControlMessagesComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  force: [{\n    type: Input\n  }],\n  setMessages: [{\n    type: Input,\n    args: ['messages']\n  }]\n}, _SwuiControlMessagesComponent);\nSwuiControlMessagesComponent = __decorate([Component({\n  selector: 'lib-swui-control-messages',\n  template: `\n    <span *ngIf=\"errorMessage\">{{ errorMessage | translate: params }}</span>\n  `,\n  standalone: false\n})], SwuiControlMessagesComponent);\nexport { SwuiControlMessagesComponent };", "map": {"version": 3, "names": ["Component", "Inject", "Input", "Optional", "SWUI_CONTROL_MESSAGES", "SwuiIsControlInvalidService", "SwuiControlMessagesComponent", "_SwuiControlMessagesComponent", "setMessages", "value", "messages", "_objectSpread", "config", "constructor", "service", "force", "errorMessage", "control", "touched", "<PERSON><PERSON><PERSON><PERSON>ched", "key", "values", "error", "params", "undefined", "isControlInvalid", "Object", "entries", "errors", "shift", "invalid", "type", "args", "__decorate", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-control-messages/swui-control-messages.component.ts"], "sourcesContent": ["import { Component, Inject, Input, Optional } from '@angular/core';\nimport { AbstractControl } from '@angular/forms';\nimport { SWUI_CONTROL_MESSAGES } from './swui-control-messages.token';\nimport { SwuiIsControlInvalidService } from '../swui-is-control-invalid/swui-is-control-invalid.service';\n\nexport interface SwuiControlMessages {\n  [key: string]: string;\n}\n\n@Component({\n    selector: 'lib-swui-control-messages',\n    template: `\n    <span *ngIf=\"errorMessage\">{{ errorMessage | translate: params }}</span>\n  `,\n    standalone: false\n})\nexport class SwuiControlMessagesComponent {\n  @Input() control?: AbstractControl;\n  /**\n   * @deprecated use SwuiIsControlInvalidService\n   */\n  @Input() force = false;\n\n  @Input('messages') set setMessages( value: SwuiControlMessages | undefined ) {\n    if (value) {\n      this.messages = { ...(this.config || {}), ...value };\n    }\n  }\n\n  params?: Object;\n  private messages: SwuiControlMessages;\n\n  constructor( @Optional() @Inject(SWUI_CONTROL_MESSAGES) private readonly config: SwuiControlMessages,\n               @Optional() private readonly service: SwuiIsControlInvalidService ) {\n    this.messages = config || {};\n  }\n\n  get errorMessage(): string | undefined {\n    const control = this.control;\n    if (control) {\n      if (this.force && !control.touched) {\n        control.markAsTouched();\n      }\n      const [key, values] = this.error(control);\n      this.params = values;\n      return key && this.messages[key];\n    }\n    return undefined;\n  }\n\n  private error( control: AbstractControl ): [string | undefined, any] {\n    if (this.isControlInvalid(control)) {\n      return Object.entries(control.errors || {}).shift() || [undefined, undefined];\n    }\n    return [undefined, undefined];\n  }\n\n  private isControlInvalid( control: AbstractControl ): boolean {\n    if (this.service) {\n      return this.service.isControlInvalid(control);\n    } else {\n      return control.touched && control.invalid;\n    }\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAElE,SAASC,qBAAqB,QAAQ,+BAA+B;AACrE,SAASC,2BAA2B,QAAQ,4DAA4D;AAajG,IAAMC,4BAA4B,IAAAC,6BAAA,GAAlC,MAAMD,4BAA4B;MAOhBE,WAAWA,CAAEC,KAAsC;IACxE,IAAIA,KAAK,EAAE;MACT,IAAI,CAACC,QAAQ,GAAAC,aAAA,CAAAA,aAAA,KAAS,IAAI,CAACC,MAAM,IAAI,EAAE,GAAMH,KAAK,CAAE;IACtD;EACF;EAKAI,YAAyED,MAA2B,EAC1DE,OAAoC;IADL,KAAAF,MAAM,GAANA,MAAM;IACrC,KAAAE,OAAO,GAAPA,OAAO;IAZxC,KAAAC,KAAK,GAAG,KAAK;IAapB,IAAI,CAACL,QAAQ,GAAGE,MAAM,IAAI,EAAE;EAC9B;EAEA,IAAII,YAAYA,CAAA;IACd,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,EAAE;MACX,IAAI,IAAI,CAACF,KAAK,IAAI,CAACE,OAAO,CAACC,OAAO,EAAE;QAClCD,OAAO,CAACE,aAAa,EAAE;MACzB;MACA,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,KAAK,CAACL,OAAO,CAAC;MACzC,IAAI,CAACM,MAAM,GAAGF,MAAM;MACpB,OAAOD,GAAG,IAAI,IAAI,CAACV,QAAQ,CAACU,GAAG,CAAC;IAClC;IACA,OAAOI,SAAS;EAClB;EAEQF,KAAKA,CAAEL,OAAwB;IACrC,IAAI,IAAI,CAACQ,gBAAgB,CAACR,OAAO,CAAC,EAAE;MAClC,OAAOS,MAAM,CAACC,OAAO,CAACV,OAAO,CAACW,MAAM,IAAI,EAAE,CAAC,CAACC,KAAK,EAAE,IAAI,CAACL,SAAS,EAAEA,SAAS,CAAC;IAC/E;IACA,OAAO,CAACA,SAAS,EAAEA,SAAS,CAAC;EAC/B;EAEQC,gBAAgBA,CAAER,OAAwB;IAChD,IAAI,IAAI,CAACH,OAAO,EAAE;MAChB,OAAO,IAAI,CAACA,OAAO,CAACW,gBAAgB,CAACR,OAAO,CAAC;IAC/C,CAAC,MAAM;MACL,OAAOA,OAAO,CAACC,OAAO,IAAID,OAAO,CAACa,OAAO;IAC3C;EACF;;;;UA/Bc3B;EAAQ;IAAA4B,IAAA,EAAI9B,MAAM;IAAA+B,IAAA,GAAC5B,qBAAqB;EAAA;AAAA,G;;;UACxCD;EAAQ;AAAA,E;;UAhBrBD;EAAK;;UAILA;EAAK;;UAELA,KAAK;IAAA8B,IAAA,GAAC,UAAU;EAAA;;AAPN1B,4BAA4B,GAAA2B,UAAA,EAPxCjC,SAAS,CAAC;EACPkC,QAAQ,EAAE,2BAA2B;EACrCC,QAAQ,EAAE;;GAEX;EACCC,UAAU,EAAE;CACf,CAAC,C,EACW9B,4BAA4B,CAgDxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}