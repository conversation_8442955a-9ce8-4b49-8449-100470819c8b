{"ast": null, "code": "var _NoDataDialogComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./no-data-dialog.component.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nlet NoDataDialogComponent = (_NoDataDialogComponent = class NoDataDialogComponent {\n  constructor(dialog, data) {\n    this.dialog = dialog;\n    this.currentAction = data.currentAction;\n    this.declineAction = data.declineAction;\n  }\n}, _NoDataDialogComponent.ctorParameters = () => [{\n  type: MatDialog\n}, {\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [MAT_DIALOG_DATA]\n  }]\n}], _NoDataDialogComponent);\nNoDataDialogComponent = __decorate([Component({\n  selector: 'lib-swui-no-data-dialog',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], NoDataDialogComponent);\nexport { NoDataDialogComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "MAT_DIALOG_DATA", "MatDialog", "NoDataDialogComponent", "_NoDataDialogComponent", "constructor", "dialog", "data", "currentAction", "declineAction", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/bulk-actions/dialogs/no-data-dialog.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./no-data-dialog.component.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nlet NoDataDialogComponent = class NoDataDialogComponent {\n    constructor(dialog, data) {\n        this.dialog = dialog;\n        this.currentAction = data.currentAction;\n        this.declineAction = data.declineAction;\n    }\n    static { this.ctorParameters = () => [\n        { type: MatDialog },\n        { type: undefined, decorators: [{ type: Inject, args: [MAT_DIALOG_DATA,] }] }\n    ]; }\n};\nNoDataDialogComponent = __decorate([\n    Component({\n        selector: 'lib-swui-no-data-dialog',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], NoDataDialogComponent);\nexport { NoDataDialogComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,eAAe,EAAEC,SAAS,QAAQ,0BAA0B;AACrE,IAAIC,qBAAqB,IAAAC,sBAAA,GAAG,MAAMD,qBAAqB,CAAC;EACpDE,WAAWA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,aAAa,GAAGD,IAAI,CAACC,aAAa;IACvC,IAAI,CAACC,aAAa,GAAGF,IAAI,CAACE,aAAa;EAC3C;AAKJ,CAAC,EAJYL,sBAAA,CAAKM,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAET;AAAU,CAAC,EACnB;EAAES,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEX,MAAM;IAAEc,IAAI,EAAE,CAACb,eAAe;EAAG,CAAC;AAAE,CAAC,CAChF,EAAAG,sBAAA,CACJ;AACDD,qBAAqB,GAAGN,UAAU,CAAC,CAC/BE,SAAS,CAAC;EACNgB,QAAQ,EAAE,yBAAyB;EACnCC,QAAQ,EAAElB,oBAAoB;EAC9BmB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEd,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}