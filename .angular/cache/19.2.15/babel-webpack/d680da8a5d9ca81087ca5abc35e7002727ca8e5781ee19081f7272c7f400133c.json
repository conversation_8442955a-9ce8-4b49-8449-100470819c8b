{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { zip as zipStatic } from '../observable/zip';\nimport { operate } from '../util/lift';\nexport function zip() {\n  var sources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    sources[_i] = arguments[_i];\n  }\n  return operate(function (source, subscriber) {\n    zipStatic.apply(void 0, __spreadArray([source], __read(sources))).subscribe(subscriber);\n  });\n}\n//# sourceMappingURL=zip.js.map", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "zip", "zipStatic", "operate", "sources", "_i", "arguments", "length", "source", "subscriber", "apply", "subscribe"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/operators/zip.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { zip as zipStatic } from '../observable/zip';\nimport { operate } from '../util/lift';\nexport function zip() {\n    var sources = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        sources[_i] = arguments[_i];\n    }\n    return operate(function (source, subscriber) {\n        zipStatic.apply(void 0, __spreadArray([source], __read(sources))).subscribe(subscriber);\n    });\n}\n//# sourceMappingURL=zip.js.map"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,GAAG,IAAIC,SAAS,QAAQ,mBAAmB;AACpD,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASF,GAAGA,CAAA,EAAG;EAClB,IAAIG,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,OAAO,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC/B;EACA,OAAOF,OAAO,CAAC,UAAUK,MAAM,EAAEC,UAAU,EAAE;IACzCP,SAAS,CAACQ,KAAK,CAAC,KAAK,CAAC,EAAEV,aAAa,CAAC,CAACQ,MAAM,CAAC,EAAET,MAAM,CAACK,OAAO,CAAC,CAAC,CAAC,CAACO,SAAS,CAACF,UAAU,CAAC;EAC3F,CAAC,CAAC;AACN;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}