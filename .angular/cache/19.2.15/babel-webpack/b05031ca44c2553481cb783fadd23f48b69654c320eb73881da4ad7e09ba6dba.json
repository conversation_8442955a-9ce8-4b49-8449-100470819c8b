{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { SwuiSelectModule } from '../../swui-select/swui-select.module';\nimport { SwuiSettingsDialogComponent } from './swui-settings-dialog.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { of } from 'rxjs';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { SettingsService } from '../../services/settings/settings.service';\nclass MockSettingsService {\n  get appSettings$() {\n    return of();\n  }\n}\ndescribe('SettingsDialogComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [NoopAnimationsModule, TranslateModule.forRoot(), ReactiveFormsModule, MatDialogModule, MatFormFieldModule, MatSelectModule, SwuiSelectModule],\n      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],\n      declarations: [SwuiSettingsDialogComponent],\n      providers: [{\n        provide: SettingsService,\n        useClass: MockSettingsService\n      }]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiSettingsDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "SwuiSelectModule", "SwuiSettingsDialogComponent", "TranslateModule", "of", "NoopAnimationsModule", "CUSTOM_ELEMENTS_SCHEMA", "NO_ERRORS_SCHEMA", "ReactiveFormsModule", "MatDialogModule", "MatFormFieldModule", "MatSelectModule", "SettingsService", "MockSettingsService", "appSettings$", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "forRoot", "schemas", "declarations", "providers", "provide", "useClass", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/settings-dialog/swui-settings-dialog.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\nimport { SwuiSelectModule } from '../../swui-select/swui-select.module';\n\nimport { SwuiSettingsDialogComponent } from './swui-settings-dialog.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { of } from 'rxjs';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { SettingsService } from '../../services/settings/settings.service';\n\nclass MockSettingsService {\n  get appSettings$() {\n    return of();\n  }\n}\n\ndescribe('SettingsDialogComponent', () => {\n  let component: SwuiSettingsDialogComponent;\n  let fixture: ComponentFixture<SwuiSettingsDialogComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      imports: [\n        NoopAnimationsModule,\n        TranslateModule.forRoot(),\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatFormFieldModule,\n        MatSelectModule,\n        SwuiSelectModule,\n      ],\n      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],\n      declarations: [\n        SwuiSettingsDialogComponent\n      ],\n      providers: [\n        { provide: SettingsService, useClass: MockSettingsService },\n      ]\n    }).compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiSettingsDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAC/E,SAASC,gBAAgB,QAAQ,sCAAsC;AAEvE,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,oBAAoB,QAAQ,sCAAsC;AAC3E,SAASC,sBAAsB,EAAEC,gBAAgB,QAAQ,eAAe;AACxE,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0CAA0C;AAE1E,MAAMC,mBAAmB;EACvB,IAAIC,YAAYA,CAAA;IACd,OAAOV,EAAE,EAAE;EACb;;AAGFW,QAAQ,CAAC,yBAAyB,EAAE,MAAK;EACvC,IAAIC,SAAsC;EAC1C,IAAIC,OAAsD;EAE1DC,UAAU,CAAClB,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACoB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPf,oBAAoB,EACpBF,eAAe,CAACkB,OAAO,EAAE,EACzBb,mBAAmB,EACnBC,eAAe,EACfC,kBAAkB,EAClBC,eAAe,EACfV,gBAAgB,CACjB;MACDqB,OAAO,EAAE,CAAChB,sBAAsB,EAAEC,gBAAgB,CAAC;MACnDgB,YAAY,EAAE,CACZrB,2BAA2B,CAC5B;MACDsB,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEb,eAAe;QAAEc,QAAQ,EAAEb;MAAmB,CAAE;KAE9D,CAAC,CAACc,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHT,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGlB,OAAO,CAAC6B,eAAe,CAAC1B,2BAA2B,CAAC;IAC9Dc,SAAS,GAAGC,OAAO,CAACY,iBAAiB;IACrCZ,OAAO,CAACa,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAChB,SAAS,CAAC,CAACiB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}