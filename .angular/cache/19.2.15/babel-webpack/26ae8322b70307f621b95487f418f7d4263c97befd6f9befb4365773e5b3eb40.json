{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { SwBrowserTitleService } from './sw-browser-title.service';\nimport { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';\nimport { provideHttpClientTesting } from '@angular/common/http/testing';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\ndescribe('BrowserTitleService', () => {\n  let service;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [RouterTestingModule],\n      providers: [SwBrowserTitleService, SwHubConfigService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()]\n    });\n    service = TestBed.inject(SwBrowserTitleService);\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "SwBrowserTitleService", "SwHubConfigService", "provideHttpClientTesting", "RouterTestingModule", "provideHttpClient", "withInterceptorsFromDi", "describe", "service", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-browser-title/sw-browser-title.service.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\n\nimport { SwBrowserTitleService } from './sw-browser-title.service';\nimport { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';\nimport { provideHttpClientTesting } from '@angular/common/http/testing';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\n\ndescribe('BrowserTitleService', () => {\n  let service: SwBrowserTitleService;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n    imports: [RouterTestingModule],\n    providers: [\n        SwBrowserTitleService,\n        SwHubConfigService,\n        provideHttpClient(withInterceptorsFromDi()),\n        provideHttpClientTesting()\n    ]\n});\n    service = TestBed.inject(SwBrowserTitleService);\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,sBAAsB;AAEhFC,QAAQ,CAAC,qBAAqB,EAAE,MAAK;EACnC,IAAIC,OAA8B;EAElCC,UAAU,CAAC,MAAK;IACdT,OAAO,CAACU,sBAAsB,CAAC;MAC/BC,OAAO,EAAE,CAACP,mBAAmB,CAAC;MAC9BQ,SAAS,EAAE,CACPX,qBAAqB,EACrBC,kBAAkB,EAClBG,iBAAiB,CAACC,sBAAsB,EAAE,CAAC,EAC3CH,wBAAwB,EAAE;KAEjC,CAAC;IACEK,OAAO,GAAGR,OAAO,CAACa,MAAM,CAACZ,qBAAqB,CAAC;EACjD,CAAC,CAAC;EAEFa,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACP,OAAO,CAAC,CAACQ,UAAU,EAAE;EAC9B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}