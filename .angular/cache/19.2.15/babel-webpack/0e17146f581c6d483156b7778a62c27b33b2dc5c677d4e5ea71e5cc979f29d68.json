{"ast": null, "code": "var _SwuiNumericRangeComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-numeric-range.component.html?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { ControlContainer, UntypedFormControl } from '@angular/forms';\nlet SwuiNumericRangeComponent = (_SwuiNumericRangeComponent = class SwuiNumericRangeComponent {\n  set setOptions(options) {\n    if (Array.isArray(options.enableCloseButton)) {\n      this.fromIsCloseEnable = Boolean(options.enableCloseButton[0]);\n      this.toIsCloseEnable = Boolean(options.enableCloseButton[1]);\n    } else {\n      this.fromIsCloseEnable = this.toIsCloseEnable = Boolean(options.enableCloseButton);\n    }\n    if (Array.isArray(options.placeholder)) {\n      this.fromPlaceholder = options.placeholder[0] || '';\n      this.toPlaceholder = options.placeholder[1] || '';\n    }\n    if (Array.isArray(options.errorMessages)) {\n      this.fromTransformFn = options.errorMessages[0];\n      this.toTransformFn = options.errorMessages[1];\n    } else {\n      this.fromTransformFn = this.toTransformFn = options.errorMessages;\n    }\n  }\n  constructor(controlContainer) {\n    this.controlContainer = controlContainer;\n    this.fromControl = new UntypedFormControl('');\n    this.toControl = new UntypedFormControl('');\n    this.fromIsCloseEnable = true;\n    this.toIsCloseEnable = true;\n    this.fromPlaceholder = '';\n    this.toPlaceholder = '';\n    this.fromTransformFn = null;\n    this.toTransformFn = null;\n  }\n  ngOnInit() {\n    if (this.controlContainer.control) {\n      const formGroup = this.controlContainer.control;\n      let keys = Object.keys(formGroup.controls);\n      if (keys.length !== 2) {\n        if (!keys[0]) {\n          formGroup.addControl('_from', new UntypedFormControl(''));\n        }\n        if (!keys[1]) {\n          formGroup.addControl('_to', new UntypedFormControl(''));\n        }\n        keys = Object.keys(formGroup.controls);\n      }\n      this.fromControl = formGroup.get(keys[0]);\n      this.toControl = formGroup.get(keys[1]);\n    }\n  }\n}, _SwuiNumericRangeComponent.ctorParameters = () => [{\n  type: ControlContainer\n}], _SwuiNumericRangeComponent.propDecorators = {\n  setOptions: [{\n    type: Input,\n    args: ['options']\n  }]\n}, _SwuiNumericRangeComponent);\nSwuiNumericRangeComponent = __decorate([Component({\n  selector: 'lib-swui-numeric-range',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiNumericRangeComponent);\nexport { SwuiNumericRangeComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Input", "ControlContainer", "UntypedFormControl", "SwuiNumericRangeComponent", "_SwuiNumericRangeComponent", "setOptions", "options", "Array", "isArray", "enableCloseButton", "fromIsCloseEnable", "Boolean", "toIsCloseEnable", "placeholder", "fromPlaceholder", "toPlaceholder", "errorMessages", "fromTransformFn", "toTransformFn", "constructor", "controlContainer", "fromControl", "toControl", "ngOnInit", "control", "formGroup", "keys", "Object", "controls", "length", "addControl", "get", "ctorParameters", "type", "propDecorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-numeric-range/swui-numeric-range.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-numeric-range.component.html?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { ControlContainer, UntypedFormControl, } from '@angular/forms';\nlet SwuiNumericRangeComponent = class SwuiNumericRangeComponent {\n    set setOptions(options) {\n        if (Array.isArray(options.enableCloseButton)) {\n            this.fromIsCloseEnable = Boolean(options.enableCloseButton[0]);\n            this.toIsCloseEnable = Boolean(options.enableCloseButton[1]);\n        }\n        else {\n            this.fromIsCloseEnable = this.toIsCloseEnable = Boolean(options.enableCloseButton);\n        }\n        if (Array.isArray(options.placeholder)) {\n            this.fromPlaceholder = options.placeholder[0] || '';\n            this.toPlaceholder = options.placeholder[1] || '';\n        }\n        if (Array.isArray(options.errorMessages)) {\n            this.fromTransformFn = options.errorMessages[0];\n            this.toTransformFn = options.errorMessages[1];\n        }\n        else {\n            this.fromTransformFn = this.toTransformFn = options.errorMessages;\n        }\n    }\n    constructor(controlContainer) {\n        this.controlContainer = controlContainer;\n        this.fromControl = new UntypedFormControl('');\n        this.toControl = new UntypedFormControl('');\n        this.fromIsCloseEnable = true;\n        this.toIsCloseEnable = true;\n        this.fromPlaceholder = '';\n        this.toPlaceholder = '';\n        this.fromTransformFn = null;\n        this.toTransformFn = null;\n    }\n    ngOnInit() {\n        if (this.controlContainer.control) {\n            const formGroup = this.controlContainer.control;\n            let keys = Object.keys(formGroup.controls);\n            if (keys.length !== 2) {\n                if (!keys[0]) {\n                    formGroup.addControl('_from', new UntypedFormControl(''));\n                }\n                if (!keys[1]) {\n                    formGroup.addControl('_to', new UntypedFormControl(''));\n                }\n                keys = Object.keys(formGroup.controls);\n            }\n            this.fromControl = formGroup.get(keys[0]);\n            this.toControl = formGroup.get(keys[1]);\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: ControlContainer }\n    ]; }\n    static { this.propDecorators = {\n        setOptions: [{ type: Input, args: ['options',] }]\n    }; }\n};\nSwuiNumericRangeComponent = __decorate([\n    Component({\n        selector: 'lib-swui-numeric-range',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiNumericRangeComponent);\nexport { SwuiNumericRangeComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,gBAAgB,EAAEC,kBAAkB,QAAS,gBAAgB;AACtE,IAAIC,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,CAAC;EAC5D,IAAIE,UAAUA,CAACC,OAAO,EAAE;IACpB,IAAIC,KAAK,CAACC,OAAO,CAACF,OAAO,CAACG,iBAAiB,CAAC,EAAE;MAC1C,IAAI,CAACC,iBAAiB,GAAGC,OAAO,CAACL,OAAO,CAACG,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC9D,IAAI,CAACG,eAAe,GAAGD,OAAO,CAACL,OAAO,CAACG,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC,MACI;MACD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACE,eAAe,GAAGD,OAAO,CAACL,OAAO,CAACG,iBAAiB,CAAC;IACtF;IACA,IAAIF,KAAK,CAACC,OAAO,CAACF,OAAO,CAACO,WAAW,CAAC,EAAE;MACpC,IAAI,CAACC,eAAe,GAAGR,OAAO,CAACO,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE;MACnD,IAAI,CAACE,aAAa,GAAGT,OAAO,CAACO,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE;IACrD;IACA,IAAIN,KAAK,CAACC,OAAO,CAACF,OAAO,CAACU,aAAa,CAAC,EAAE;MACtC,IAAI,CAACC,eAAe,GAAGX,OAAO,CAACU,aAAa,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACE,aAAa,GAAGZ,OAAO,CAACU,aAAa,CAAC,CAAC,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAACC,eAAe,GAAG,IAAI,CAACC,aAAa,GAAGZ,OAAO,CAACU,aAAa;IACrE;EACJ;EACAG,WAAWA,CAACC,gBAAgB,EAAE;IAC1B,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,WAAW,GAAG,IAAInB,kBAAkB,CAAC,EAAE,CAAC;IAC7C,IAAI,CAACoB,SAAS,GAAG,IAAIpB,kBAAkB,CAAC,EAAE,CAAC;IAC3C,IAAI,CAACQ,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACE,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACE,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACE,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,aAAa,GAAG,IAAI;EAC7B;EACAK,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACH,gBAAgB,CAACI,OAAO,EAAE;MAC/B,MAAMC,SAAS,GAAG,IAAI,CAACL,gBAAgB,CAACI,OAAO;MAC/C,IAAIE,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,SAAS,CAACG,QAAQ,CAAC;MAC1C,IAAIF,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;QACnB,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE;UACVD,SAAS,CAACK,UAAU,CAAC,OAAO,EAAE,IAAI5B,kBAAkB,CAAC,EAAE,CAAC,CAAC;QAC7D;QACA,IAAI,CAACwB,IAAI,CAAC,CAAC,CAAC,EAAE;UACVD,SAAS,CAACK,UAAU,CAAC,KAAK,EAAE,IAAI5B,kBAAkB,CAAC,EAAE,CAAC,CAAC;QAC3D;QACAwB,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,SAAS,CAACG,QAAQ,CAAC;MAC1C;MACA,IAAI,CAACP,WAAW,GAAGI,SAAS,CAACM,GAAG,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;MACzC,IAAI,CAACJ,SAAS,GAAGG,SAAS,CAACM,GAAG,CAACL,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3C;EACJ;AAOJ,CAAC,EANYtB,0BAAA,CAAK4B,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEhC;AAAiB,CAAC,CAC7B,EACQG,0BAAA,CAAK8B,cAAc,GAAG;EAC3B7B,UAAU,EAAE,CAAC;IAAE4B,IAAI,EAAEjC,KAAK;IAAEmC,IAAI,EAAE,CAAC,SAAS;EAAG,CAAC;AACpD,CAAC,EAAA/B,0BAAA,CACJ;AACDD,yBAAyB,GAAGN,UAAU,CAAC,CACnCE,SAAS,CAAC;EACNqC,QAAQ,EAAE,wBAAwB;EAClCC,QAAQ,EAAEvC,oBAAoB;EAC9BwC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEnC,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}