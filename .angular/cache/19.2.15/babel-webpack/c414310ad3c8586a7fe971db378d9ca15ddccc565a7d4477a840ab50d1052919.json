{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _InputSelectTableComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-select-table.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-select-table.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { BehaviorSubject, isObservable, of } from 'rxjs';\nimport { map, tap } from 'rxjs/operators';\nfunction asSelectOptions(data, loading$) {\n  loading$.next(true);\n  const source = isObservable(data) ? data : of(data || []);\n  return source.pipe(map(opts => opts.map(opt => _objectSpread(_objectSpread({}, opt), {}, {\n    text: opt.text || opt.title || opt.displayName,\n    id: opt.value || opt.id,\n    disabled: opt.disabled,\n    data: opt\n  }))), map(opts => opts.filter(({\n    id,\n    text\n  }) => !!id && !!text)), tap(() => {\n    loading$.next(false);\n  }));\n}\nlet InputSelectTableComponent = (_InputSelectTableComponent = class InputSelectTableComponent {\n  constructor() {\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n    this.multiple = false;\n    this.selectOptions = of([]);\n    this.loading = false;\n    this.columns = [];\n    this.loading$ = new BehaviorSubject(false);\n  }\n  set componentOptions(value) {\n    this.selectOptions = asSelectOptions((value === null || value === void 0 ? void 0 : value.data) || [], this.loading$);\n    this.columns = (value === null || value === void 0 ? void 0 : value.columns) || [];\n    this.rowsNumber = value === null || value === void 0 ? void 0 : value.rowsNumber;\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.showSearch = value === null || value === void 0 ? void 0 : value.showSearch;\n    this.loading = (value === null || value === void 0 ? void 0 : value.loading) || false;\n  }\n  clear(event) {\n    event.stopPropagation();\n    if (this.control) {\n      this.control.setValue(null);\n    }\n  }\n}, _InputSelectTableComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }]\n}, _InputSelectTableComponent);\nInputSelectTableComponent = __decorate([Component({\n  selector: 'lib-input-select-table',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputSelectTableComponent);\nexport { InputSelectTableComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "BehaviorSubject", "isObservable", "of", "map", "tap", "asSelectOptions", "data", "loading$", "next", "source", "pipe", "opts", "opt", "_objectSpread", "text", "title", "displayName", "id", "value", "disabled", "filter", "InputSelectTableComponent", "_InputSelectTableComponent", "constructor", "readonly", "submitted", "multiple", "selectOptions", "loading", "columns", "componentOptions", "rowsNumber", "showSearch", "clear", "event", "stopPropagation", "control", "setValue", "propDecorators", "type", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-select-table/input-select-table.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-select-table.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-select-table.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { BehaviorSubject, isObservable, of } from 'rxjs';\nimport { map, tap } from 'rxjs/operators';\nfunction asSelectOptions(data, loading$) {\n    loading$.next(true);\n    const source = isObservable(data) ? data : of(data || []);\n    return source.pipe(map(opts => opts.map(opt => ({\n        ...opt,\n        text: opt.text || opt.title || opt.displayName,\n        id: opt.value || opt.id,\n        disabled: opt.disabled,\n        data: opt\n    }))), map(opts => opts.filter(({ id, text }) => !!id && !!text)), tap(() => {\n        loading$.next(false);\n    }));\n}\nlet InputSelectTableComponent = class InputSelectTableComponent {\n    constructor() {\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n        this.multiple = false;\n        this.selectOptions = of([]);\n        this.loading = false;\n        this.columns = [];\n        this.loading$ = new BehaviorSubject(false);\n    }\n    set componentOptions(value) {\n        this.selectOptions = asSelectOptions(value?.data || [], this.loading$);\n        this.columns = value?.columns || [];\n        this.rowsNumber = value?.rowsNumber;\n        this.title = value?.title;\n        this.showSearch = value?.showSearch;\n        this.loading = value?.loading || false;\n    }\n    clear(event) {\n        event.stopPropagation();\n        if (this.control) {\n            this.control.setValue(null);\n        }\n    }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }]\n    }; }\n};\nInputSelectTableComponent = __decorate([\n    Component({\n        selector: 'lib-input-select-table',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputSelectTableComponent);\nexport { InputSelectTableComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,eAAe,EAAEC,YAAY,EAAEC,EAAE,QAAQ,MAAM;AACxD,SAASC,GAAG,EAAEC,GAAG,QAAQ,gBAAgB;AACzC,SAASC,eAAeA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACrCA,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;EACnB,MAAMC,MAAM,GAAGR,YAAY,CAACK,IAAI,CAAC,GAAGA,IAAI,GAAGJ,EAAE,CAACI,IAAI,IAAI,EAAE,CAAC;EACzD,OAAOG,MAAM,CAACC,IAAI,CAACP,GAAG,CAACQ,IAAI,IAAIA,IAAI,CAACR,GAAG,CAACS,GAAG,IAAAC,aAAA,CAAAA,aAAA,KACpCD,GAAG;IACNE,IAAI,EAAEF,GAAG,CAACE,IAAI,IAAIF,GAAG,CAACG,KAAK,IAAIH,GAAG,CAACI,WAAW;IAC9CC,EAAE,EAAEL,GAAG,CAACM,KAAK,IAAIN,GAAG,CAACK,EAAE;IACvBE,QAAQ,EAAEP,GAAG,CAACO,QAAQ;IACtBb,IAAI,EAAEM;EAAG,EACX,CAAC,CAAC,EAAET,GAAG,CAACQ,IAAI,IAAIA,IAAI,CAACS,MAAM,CAAC,CAAC;IAAEH,EAAE;IAAEH;EAAK,CAAC,KAAK,CAAC,CAACG,EAAE,IAAI,CAAC,CAACH,IAAI,CAAC,CAAC,EAAEV,GAAG,CAAC,MAAM;IACxEG,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;EACxB,CAAC,CAAC,CAAC;AACP;AACA,IAAIa,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,CAAC;EAC5DE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,EAAE,GAAG,EAAE;IACZ,IAAI,CAACO,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,aAAa,GAAGzB,EAAE,CAAC,EAAE,CAAC;IAC3B,IAAI,CAAC0B,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACtB,QAAQ,GAAG,IAAIP,eAAe,CAAC,KAAK,CAAC;EAC9C;EACA,IAAI8B,gBAAgBA,CAACZ,KAAK,EAAE;IACxB,IAAI,CAACS,aAAa,GAAGtB,eAAe,CAAC,CAAAa,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEZ,IAAI,KAAI,EAAE,EAAE,IAAI,CAACC,QAAQ,CAAC;IACtE,IAAI,CAACsB,OAAO,GAAG,CAAAX,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,OAAO,KAAI,EAAE;IACnC,IAAI,CAACE,UAAU,GAAGb,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEa,UAAU;IACnC,IAAI,CAAChB,KAAK,GAAGG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEH,KAAK;IACzB,IAAI,CAACiB,UAAU,GAAGd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,UAAU;IACnC,IAAI,CAACJ,OAAO,GAAG,CAAAV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEU,OAAO,KAAI,KAAK;EAC1C;EACAK,KAAKA,CAACC,KAAK,EAAE;IACTA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,QAAQ,CAAC,IAAI,CAAC;IAC/B;EACJ;AAQJ,CAAC,EAPYf,0BAAA,CAAKgB,cAAc,GAAG;EAC3BF,OAAO,EAAE,CAAC;IAAEG,IAAI,EAAExC;EAAM,CAAC,CAAC;EAC1BkB,EAAE,EAAE,CAAC;IAAEsB,IAAI,EAAExC;EAAM,CAAC,CAAC;EACrByB,QAAQ,EAAE,CAAC;IAAEe,IAAI,EAAExC;EAAM,CAAC,CAAC;EAC3B0B,SAAS,EAAE,CAAC;IAAEc,IAAI,EAAExC;EAAM,CAAC,CAAC;EAC5B+B,gBAAgB,EAAE,CAAC;IAAES,IAAI,EAAExC;EAAM,CAAC;AACtC,CAAC,EAAAuB,0BAAA,CACJ;AACDD,yBAAyB,GAAG1B,UAAU,CAAC,CACnCG,SAAS,CAAC;EACN0C,QAAQ,EAAE,wBAAwB;EAClCC,QAAQ,EAAE7C,oBAAoB;EAC9B8C,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC9C,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEwB,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}