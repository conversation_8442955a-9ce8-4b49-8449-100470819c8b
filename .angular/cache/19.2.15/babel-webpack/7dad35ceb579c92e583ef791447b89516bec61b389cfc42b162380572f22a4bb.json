{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _UniqueSelectionDispatcher;\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n  constructor() {\n    _defineProperty(this, \"_listeners\", []);\n  }\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id, name) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener) {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter(registered => {\n        return listener !== registered;\n      });\n    };\n  }\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n}\n_UniqueSelectionDispatcher = UniqueSelectionDispatcher;\n_defineProperty(UniqueSelectionDispatcher, \"\\u0275fac\", function _UniqueSelectionDispatcher_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _UniqueSelectionDispatcher)();\n});\n_defineProperty(UniqueSelectionDispatcher, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _UniqueSelectionDispatcher,\n  factory: _UniqueSelectionDispatcher.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UniqueSelectionDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { UniqueSelectionDispatcher as U };\n//# sourceMappingURL=unique-selection-dispatcher-DtHZDqyJ.mjs.map", "map": {"version": 3, "names": ["i0", "Injectable", "UniqueSelectionDispatcher", "constructor", "_defineProperty", "notify", "id", "name", "listener", "_listeners", "listen", "push", "filter", "registered", "ngOnDestroy", "_UniqueSelectionDispatcher", "_UniqueSelectionDispatcher_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "U"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/unique-selection-dispatcher-DtHZDqyJ.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n    _listeners = [];\n    /**\n     * Notify other items that selection for the given name has been set.\n     * @param id ID of the item.\n     * @param name Name of the item.\n     */\n    notify(id, name) {\n        for (let listener of this._listeners) {\n            listener(id, name);\n        }\n    }\n    /**\n     * Listen for future changes to item selection.\n     * @return Function used to deregister listener\n     */\n    listen(listener) {\n        this._listeners.push(listener);\n        return () => {\n            this._listeners = this._listeners.filter((registered) => {\n                return listener !== registered;\n            });\n        };\n    }\n    ngOnDestroy() {\n        this._listeners = [];\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: UniqueSelectionDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: UniqueSelectionDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: UniqueSelectionDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nexport { UniqueSelectionDispatcher as U };\n//# sourceMappingURL=unique-selection-dispatcher-DtHZDqyJ.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAAAC,YAAA;IAAAC,eAAA,qBACf,EAAE;EAAA;EACf;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAACC,EAAE,EAAEC,IAAI,EAAE;IACb,KAAK,IAAIC,QAAQ,IAAI,IAAI,CAACC,UAAU,EAAE;MAClCD,QAAQ,CAACF,EAAE,EAAEC,IAAI,CAAC;IACtB;EACJ;EACA;AACJ;AACA;AACA;EACIG,MAAMA,CAACF,QAAQ,EAAE;IACb,IAAI,CAACC,UAAU,CAACE,IAAI,CAACH,QAAQ,CAAC;IAC9B,OAAO,MAAM;MACT,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,CAACG,MAAM,CAAEC,UAAU,IAAK;QACrD,OAAOL,QAAQ,KAAKK,UAAU;MAClC,CAAC,CAAC;IACN,CAAC;EACL;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,UAAU,GAAG,EAAE;EACxB;AAGJ;AAACM,0BAAA,GA7BKb,yBAAyB;AAAAE,eAAA,CAAzBF,yBAAyB,wBAAAc,mCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA2BwEf,0BAAyB;AAAA;AAAAE,eAAA,CA3B1HF,yBAAyB,+BA8BkDF,EAAE,CAAAkB,kBAAA;EAAAC,KAAA,EAFwBjB,0BAAyB;EAAAkB,OAAA,EAAzBlB,0BAAyB,CAAAmB,IAAA;EAAAC,UAAA,EAAc;AAAM;AAExJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFvB,EAAE,CAAAwB,iBAAA,CAAQtB,yBAAyB,EAAc,CAAC;IACvHuB,IAAI,EAAExB,UAAU;IAChByB,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,SAASpB,yBAAyB,IAAIyB,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}