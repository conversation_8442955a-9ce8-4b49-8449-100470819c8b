{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (i) {\n  return i[1];\n};", "map": {"version": 3, "names": ["module", "exports", "i"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/css-loader/dist/runtime/noSourceMaps.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = function (i) {\n  return i[1];\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,UAAUC,CAAC,EAAE;EAC5B,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}