{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiDateTimeChooserComponent } from './swui-date-time-chooser.component';\nimport { SwuiMatCalendarModule } from '../swui-mat-calendar/swui-mat-calendar.module';\nimport { SwuiTimepickerModule } from '../swui-timepicker/swui-timepicker.module';\nexport const DATE_TIME_CHOOSER_MODULES = [ReactiveFormsModule, SwuiMatCalendarModule, SwuiTimepickerModule];\nlet SwuiDateTimeChooserModule = class SwuiDateTimeChooserModule {};\nSwuiDateTimeChooserModule = __decorate([NgModule({\n  declarations: [SwuiDateTimeChooserComponent],\n  imports: [CommonModule, ...DATE_TIME_CHOOSER_MODULES],\n  exports: [SwuiDateTimeChooserComponent]\n})], SwuiDateTimeChooserModule);\nexport { SwuiDateTimeChooserModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "ReactiveFormsModule", "SwuiDateTimeChooserComponent", "SwuiMatCalendarModule", "SwuiTimepickerModule", "DATE_TIME_CHOOSER_MODULES", "SwuiDateTimeChooserModule", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-chooser/swui-date-time-chooser.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiDateTimeChooserComponent } from './swui-date-time-chooser.component';\nimport { SwuiMatCalendarModule } from '../swui-mat-calendar/swui-mat-calendar.module';\nimport { SwuiTimepickerModule } from '../swui-timepicker/swui-timepicker.module';\nexport const DATE_TIME_CHOOSER_MODULES = [\n    ReactiveFormsModule,\n    SwuiMatCalendarModule,\n    SwuiTimepickerModule,\n];\nlet SwuiDateTimeChooserModule = class SwuiDateTimeChooserModule {\n};\nSwuiDateTimeChooserModule = __decorate([\n    NgModule({\n        declarations: [SwuiDateTimeChooserComponent],\n        imports: [\n            CommonModule,\n            ...DATE_TIME_CHOOSER_MODULES,\n        ],\n        exports: [\n            SwuiDateTimeChooserComponent,\n        ]\n    })\n], SwuiDateTimeChooserModule);\nexport { SwuiDateTimeChooserModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,OAAO,MAAMC,yBAAyB,GAAG,CACrCJ,mBAAmB,EACnBE,qBAAqB,EACrBC,oBAAoB,CACvB;AACD,IAAIE,yBAAyB,GAAG,MAAMA,yBAAyB,CAAC,EAC/D;AACDA,yBAAyB,GAAGR,UAAU,CAAC,CACnCC,QAAQ,CAAC;EACLQ,YAAY,EAAE,CAACL,4BAA4B,CAAC;EAC5CM,OAAO,EAAE,CACLR,YAAY,EACZ,GAAGK,yBAAyB,CAC/B;EACDI,OAAO,EAAE,CACLP,4BAA4B;AAEpC,CAAC,CAAC,CACL,EAAEI,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}