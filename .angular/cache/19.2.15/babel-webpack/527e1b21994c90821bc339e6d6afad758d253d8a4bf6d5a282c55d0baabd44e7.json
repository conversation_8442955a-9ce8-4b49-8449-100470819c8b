{"ast": null, "code": "export class ActionModel {\n  constructor(config) {\n    this.title = config.title || null;\n    this.icon = config.icon || '';\n    this.fn = config.fn || (() => true);\n    this.dialog = config.dialog;\n    this.canActivateFn = config.canActivateFn || (() => true);\n    this.canActivateMessage = config.canActivateMessage || null;\n    this.isDisabledAction = config.isDisabledAction || (() => false);\n    this.isDisabledMessage = config.isDisabledMessage || null;\n  }\n}\nexport class BulkAction extends ActionModel {}", "map": {"version": 3, "names": ["ActionModel", "constructor", "config", "title", "icon", "fn", "dialog", "canActivateFn", "canActivateMessage", "isDisabledAction", "isDisabledMessage", "BulkAction"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/bulk-actions/bulk-actions.model.ts"], "sourcesContent": ["export class ActionModel {\n    constructor(config) {\n        this.title = config.title || null;\n        this.icon = config.icon || '';\n        this.fn = config.fn || (() => true);\n        this.dialog = config.dialog;\n        this.canActivateFn = config.canActivateFn || (() => true);\n        this.canActivateMessage = config.canActivateMessage || null;\n        this.isDisabledAction = config.isDisabledAction || (() => false);\n        this.isDisabledMessage = config.isDisabledMessage || null;\n    }\n}\nexport class BulkAction extends ActionModel {\n}\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,CAAC;EACrBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,KAAK,GAAGD,MAAM,CAACC,KAAK,IAAI,IAAI;IACjC,IAAI,CAACC,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAI,EAAE;IAC7B,IAAI,CAACC,EAAE,GAAGH,MAAM,CAACG,EAAE,KAAK,MAAM,IAAI,CAAC;IACnC,IAAI,CAACC,MAAM,GAAGJ,MAAM,CAACI,MAAM;IAC3B,IAAI,CAACC,aAAa,GAAGL,MAAM,CAACK,aAAa,KAAK,MAAM,IAAI,CAAC;IACzD,IAAI,CAACC,kBAAkB,GAAGN,MAAM,CAACM,kBAAkB,IAAI,IAAI;IAC3D,IAAI,CAACC,gBAAgB,GAAGP,MAAM,CAACO,gBAAgB,KAAK,MAAM,KAAK,CAAC;IAChE,IAAI,CAACC,iBAAiB,GAAGR,MAAM,CAACQ,iBAAiB,IAAI,IAAI;EAC7D;AACJ;AACA,OAAO,MAAMC,UAAU,SAASX,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}