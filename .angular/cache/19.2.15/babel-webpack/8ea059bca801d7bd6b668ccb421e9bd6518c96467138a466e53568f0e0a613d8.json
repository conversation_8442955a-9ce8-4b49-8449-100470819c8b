{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatSort, _MatSortHeaderIntl, _MatSortHeader, _MatSortModule;\nconst _c0 = [\"mat-sort-header\", \"\"];\nconst _c1 = [\"*\"];\nfunction _MatSortHeader_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 3);\n    i0.ɵɵelement(2, \"path\", 4);\n    i0.ɵɵelementEnd()();\n  }\n}\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, booleanAttribute, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, inject, ChangeDetectorRef, ElementRef, ANIMATION_MODULE_TYPE, signal, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport { <PERSON>Monitor, AriaDescriber } from '@angular/cdk/a11y';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { ReplaySubject, Subject, merge } from 'rxjs';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/bidi';\n\n/** @docs-private */\nfunction getSortDuplicateSortableIdError(id) {\n  return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n  return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n  return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n  return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nclass MatSort {\n  /** The sort direction of the currently active MatSortable. */\n  get direction() {\n    return this._direction;\n  }\n  set direction(direction) {\n    if (direction && direction !== 'asc' && direction !== 'desc' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortInvalidDirectionError(direction);\n    }\n    this._direction = direction;\n  }\n  constructor(_defaultOptions) {\n    _defineProperty(this, \"_defaultOptions\", void 0);\n    _defineProperty(this, \"_initializedStream\", new ReplaySubject(1));\n    /** Collection of all registered sortables that this directive manages. */\n    _defineProperty(this, \"sortables\", new Map());\n    /** Used to notify any child components listening to state changes. */\n    _defineProperty(this, \"_stateChanges\", new Subject());\n    /** The id of the most recently sorted MatSortable. */\n    _defineProperty(this, \"active\", void 0);\n    /**\n     * The direction to set when an MatSortable is initially sorted.\n     * May be overridden by the MatSortable's sort start.\n     */\n    _defineProperty(this, \"start\", 'asc');\n    _defineProperty(this, \"_direction\", '');\n    /**\n     * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n     * May be overridden by the MatSortable's disable clear input.\n     */\n    _defineProperty(this, \"disableClear\", void 0);\n    /** Whether the sortable is disabled. */\n    _defineProperty(this, \"disabled\", false);\n    /** Event emitted when the user changes either the active sort or sort direction. */\n    _defineProperty(this, \"sortChange\", new EventEmitter());\n    /** Emits when the paginator is initialized. */\n    _defineProperty(this, \"initialized\", this._initializedStream);\n    this._defaultOptions = _defaultOptions;\n  }\n  /**\n   * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n   * collection of MatSortables.\n   */\n  register(sortable) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!sortable.id) {\n        throw getSortHeaderMissingIdError();\n      }\n      if (this.sortables.has(sortable.id)) {\n        throw getSortDuplicateSortableIdError(sortable.id);\n      }\n    }\n    this.sortables.set(sortable.id, sortable);\n  }\n  /**\n   * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n   * collection of contained MatSortables.\n   */\n  deregister(sortable) {\n    this.sortables.delete(sortable.id);\n  }\n  /** Sets the active sort id and determines the new sort direction. */\n  sort(sortable) {\n    if (this.active != sortable.id) {\n      this.active = sortable.id;\n      this.direction = sortable.start ? sortable.start : this.start;\n    } else {\n      this.direction = this.getNextSortDirection(sortable);\n    }\n    this.sortChange.emit({\n      active: this.active,\n      direction: this.direction\n    });\n  }\n  /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n  getNextSortDirection(sortable) {\n    var _ref, _sortable$disableClea, _this$_defaultOptions;\n    if (!sortable) {\n      return '';\n    }\n    // Get the sort direction cycle with the potential sortable overrides.\n    const disableClear = (_ref = (_sortable$disableClea = sortable === null || sortable === void 0 ? void 0 : sortable.disableClear) !== null && _sortable$disableClea !== void 0 ? _sortable$disableClea : this.disableClear) !== null && _ref !== void 0 ? _ref : !!((_this$_defaultOptions = this._defaultOptions) !== null && _this$_defaultOptions !== void 0 && _this$_defaultOptions.disableClear);\n    let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n    // Get and return the next direction in the cycle\n    let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n    if (nextDirectionIndex >= sortDirectionCycle.length) {\n      nextDirectionIndex = 0;\n    }\n    return sortDirectionCycle[nextDirectionIndex];\n  }\n  ngOnInit() {\n    this._initializedStream.next();\n  }\n  ngOnChanges() {\n    this._stateChanges.next();\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._initializedStream.complete();\n  }\n}\n_MatSort = MatSort;\n_defineProperty(MatSort, \"\\u0275fac\", function _MatSort_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSort)(i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n});\n_defineProperty(MatSort, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatSort,\n  selectors: [[\"\", \"matSort\", \"\"]],\n  hostAttrs: [1, \"mat-sort\"],\n  inputs: {\n    active: [0, \"matSortActive\", \"active\"],\n    start: [0, \"matSortStart\", \"start\"],\n    direction: [0, \"matSortDirection\", \"direction\"],\n    disableClear: [2, \"matSortDisableClear\", \"disableClear\", booleanAttribute],\n    disabled: [2, \"matSortDisabled\", \"disabled\", booleanAttribute]\n  },\n  outputs: {\n    sortChange: \"matSortChange\"\n  },\n  exportAs: [\"matSort\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSort, [{\n    type: Directive,\n    args: [{\n      selector: '[matSort]',\n      exportAs: 'matSort',\n      host: {\n        'class': 'mat-sort'\n      }\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_SORT_DEFAULT_OPTIONS]\n    }]\n  }], {\n    active: [{\n      type: Input,\n      args: ['matSortActive']\n    }],\n    start: [{\n      type: Input,\n      args: ['matSortStart']\n    }],\n    direction: [{\n      type: Input,\n      args: ['matSortDirection']\n    }],\n    disableClear: [{\n      type: Input,\n      args: [{\n        alias: 'matSortDisableClear',\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'matSortDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    sortChange: [{\n      type: Output,\n      args: ['matSortChange']\n    }]\n  });\n})();\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n  let sortOrder = ['asc', 'desc'];\n  if (start == 'desc') {\n    sortOrder.reverse();\n  }\n  if (!disableClear) {\n    sortOrder.push('');\n  }\n  return sortOrder;\n}\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nclass MatSortHeaderIntl {\n  constructor() {\n    /**\n     * Stream that emits whenever the labels here are changed. Use this to notify\n     * components if the labels have changed after initialization.\n     */\n    _defineProperty(this, \"changes\", new Subject());\n  }\n}\n_MatSortHeaderIntl = MatSortHeaderIntl;\n_defineProperty(MatSortHeaderIntl, \"\\u0275fac\", function _MatSortHeaderIntl_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSortHeaderIntl)();\n});\n_defineProperty(MatSortHeaderIntl, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _MatSortHeaderIntl,\n  factory: _MatSortHeaderIntl.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortHeaderIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatSortHeaderIntl();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n  // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n  provide: MatSortHeaderIntl,\n  deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n  useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY\n};\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nclass MatSortHeader {\n  /**\n   * Description applied to MatSortHeader's button element with aria-describedby. This text should\n   * describe the action that will occur when the user clicks the sort header.\n   */\n  get sortActionDescription() {\n    return this._sortActionDescription;\n  }\n  set sortActionDescription(value) {\n    this._updateSortActionDescription(value);\n  }\n  // Default the action description to \"Sort\" because it's better than nothing.\n  // Without a description, the button's label comes from the sort header text content,\n  // which doesn't give any indication that it performs a sorting operation.\n\n  constructor() {\n    _defineProperty(this, \"_intl\", inject(MatSortHeaderIntl));\n    _defineProperty(this, \"_sort\", inject(MatSort, {\n      optional: true\n    }));\n    _defineProperty(this, \"_columnDef\", inject('MAT_SORT_HEADER_COLUMN_DEF', {\n      optional: true\n    }));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_focusMonitor\", inject(FocusMonitor));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_ariaDescriber\", inject(AriaDescriber, {\n      optional: true\n    }));\n    _defineProperty(this, \"_renderChanges\", void 0);\n    _defineProperty(this, \"_animationModule\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }));\n    /**\n     * Indicates which state was just cleared from the sort header.\n     * Will be reset on the next interaction. Used for coordinating animations.\n     */\n    _defineProperty(this, \"_recentlyCleared\", signal(null));\n    /**\n     * The element with role=\"button\" inside this component's view. We need this\n     * in order to apply a description with AriaDescriber.\n     */\n    _defineProperty(this, \"_sortButton\", void 0);\n    /**\n     * ID of this sort header. If used within the context of a CdkColumnDef, this will default to\n     * the column's name.\n     */\n    _defineProperty(this, \"id\", void 0);\n    /** Sets the position of the arrow that displays when sorted. */\n    _defineProperty(this, \"arrowPosition\", 'after');\n    /** Overrides the sort start value of the containing MatSort for this MatSortable. */\n    _defineProperty(this, \"start\", void 0);\n    /** whether the sort header is disabled. */\n    _defineProperty(this, \"disabled\", false);\n    _defineProperty(this, \"_sortActionDescription\", 'Sort');\n    /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n    _defineProperty(this, \"disableClear\", void 0);\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const defaultOptions = inject(MAT_SORT_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    // Note that we use a string token for the `_columnDef`, because the value is provided both by\n    // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n    // and we want to avoid having the sort header depending on the CDK table because\n    // of this single reference.\n    if (!this._sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortHeaderNotContainedWithinSortError();\n    }\n    if (defaultOptions !== null && defaultOptions !== void 0 && defaultOptions.arrowPosition) {\n      this.arrowPosition = defaultOptions === null || defaultOptions === void 0 ? void 0 : defaultOptions.arrowPosition;\n    }\n  }\n  ngOnInit() {\n    if (!this.id && this._columnDef) {\n      this.id = this._columnDef.name;\n    }\n    this._sort.register(this);\n    this._renderChanges = merge(this._sort._stateChanges, this._sort.sortChange).subscribe(() => this._changeDetectorRef.markForCheck());\n    this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n    this._updateSortActionDescription(this._sortActionDescription);\n  }\n  ngAfterViewInit() {\n    // We use the focus monitor because we also want to style\n    // things differently based on the focus origin.\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(() => this._recentlyCleared.set(null));\n  }\n  ngOnDestroy() {\n    var _this$_renderChanges;\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._sort.deregister(this);\n    (_this$_renderChanges = this._renderChanges) === null || _this$_renderChanges === void 0 || _this$_renderChanges.unsubscribe();\n    if (this._sortButton) {\n      var _this$_ariaDescriber;\n      (_this$_ariaDescriber = this._ariaDescriber) === null || _this$_ariaDescriber === void 0 || _this$_ariaDescriber.removeDescription(this._sortButton, this._sortActionDescription);\n    }\n  }\n  /** Triggers the sort on this sort header and removes the indicator hint. */\n  _toggleOnInteraction() {\n    if (!this._isDisabled()) {\n      const wasSorted = this._isSorted();\n      const prevDirection = this._sort.direction;\n      this._sort.sort(this);\n      this._recentlyCleared.set(wasSorted && !this._isSorted() ? prevDirection : null);\n    }\n  }\n  _handleKeydown(event) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      event.preventDefault();\n      this._toggleOnInteraction();\n    }\n  }\n  /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n  _isSorted() {\n    return this._sort.active == this.id && (this._sort.direction === 'asc' || this._sort.direction === 'desc');\n  }\n  _isDisabled() {\n    return this._sort.disabled || this.disabled;\n  }\n  /**\n   * Gets the aria-sort attribute that should be applied to this sort header. If this header\n   * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n   * says that the aria-sort property should only be present on one header at a time, so removing\n   * ensures this is true.\n   */\n  _getAriaSortAttribute() {\n    if (!this._isSorted()) {\n      return 'none';\n    }\n    return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n  }\n  /** Whether the arrow inside the sort header should be rendered. */\n  _renderArrow() {\n    return !this._isDisabled() || this._isSorted();\n  }\n  _updateSortActionDescription(newDescription) {\n    // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n    // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n    // for every *cell* in the table, creating a lot of unnecessary noise.\n    // If _sortButton is undefined, the component hasn't been initialized yet so there's\n    // nothing to update in the DOM.\n    if (this._sortButton) {\n      var _this$_ariaDescriber2, _this$_ariaDescriber3;\n      // removeDescription will no-op if there is no existing message.\n      // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n      (_this$_ariaDescriber2 = this._ariaDescriber) === null || _this$_ariaDescriber2 === void 0 || _this$_ariaDescriber2.removeDescription(this._sortButton, this._sortActionDescription);\n      (_this$_ariaDescriber3 = this._ariaDescriber) === null || _this$_ariaDescriber3 === void 0 || _this$_ariaDescriber3.describe(this._sortButton, newDescription);\n    }\n    this._sortActionDescription = newDescription;\n  }\n}\n_MatSortHeader = MatSortHeader;\n_defineProperty(MatSortHeader, \"\\u0275fac\", function _MatSortHeader_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSortHeader)();\n});\n_defineProperty(MatSortHeader, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatSortHeader,\n  selectors: [[\"\", \"mat-sort-header\", \"\"]],\n  hostAttrs: [1, \"mat-sort-header\"],\n  hostVars: 3,\n  hostBindings: function _MatSortHeader_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _MatSortHeader_click_HostBindingHandler() {\n        return ctx._toggleOnInteraction();\n      })(\"keydown\", function _MatSortHeader_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      })(\"mouseleave\", function _MatSortHeader_mouseleave_HostBindingHandler() {\n        return ctx._recentlyCleared.set(null);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-sort\", ctx._getAriaSortAttribute());\n      i0.ɵɵclassProp(\"mat-sort-header-disabled\", ctx._isDisabled());\n    }\n  },\n  inputs: {\n    id: [0, \"mat-sort-header\", \"id\"],\n    arrowPosition: \"arrowPosition\",\n    start: \"start\",\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n    sortActionDescription: \"sortActionDescription\",\n    disableClear: [2, \"disableClear\", \"disableClear\", booleanAttribute]\n  },\n  exportAs: [\"matSortHeader\"],\n  attrs: _c0,\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 17,\n  consts: [[1, \"mat-sort-header-container\", \"mat-focus-indicator\"], [1, \"mat-sort-header-content\"], [1, \"mat-sort-header-arrow\"], [\"viewBox\", \"0 -960 960 960\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z\"]],\n  template: function _MatSortHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(3, _MatSortHeader_Conditional_3_Template, 3, 0, \"div\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-sort-header-sorted\", ctx._isSorted())(\"mat-sort-header-position-before\", ctx.arrowPosition === \"before\")(\"mat-sort-header-descending\", ctx._sort.direction === \"desc\")(\"mat-sort-header-ascending\", ctx._sort.direction === \"asc\")(\"mat-sort-header-recently-cleared-ascending\", ctx._recentlyCleared() === \"asc\")(\"mat-sort-header-recently-cleared-descending\", ctx._recentlyCleared() === \"desc\")(\"mat-sort-header-animations-disabled\", ctx._animationModule === \"NoopAnimations\");\n      i0.ɵɵattribute(\"tabindex\", ctx._isDisabled() ? null : 0)(\"role\", ctx._isDisabled() ? null : \"button\");\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx._renderArrow() ? 3 : -1);\n    }\n  },\n  styles: [\".mat-sort-header{cursor:pointer}.mat-sort-header-disabled{cursor:default}.mat-sort-header-container{display:flex;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortHeader, [{\n    type: Component,\n    args: [{\n      selector: '[mat-sort-header]',\n      exportAs: 'matSortHeader',\n      host: {\n        'class': 'mat-sort-header',\n        '(click)': '_toggleOnInteraction()',\n        '(keydown)': '_handleKeydown($event)',\n        '(mouseleave)': '_recentlyCleared.set(null)',\n        '[attr.aria-sort]': '_getAriaSortAttribute()',\n        '[class.mat-sort-header-disabled]': '_isDisabled()'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [class.mat-sort-header-descending]=\\\"this._sort.direction === 'desc'\\\"\\n     [class.mat-sort-header-ascending]=\\\"this._sort.direction === 'asc'\\\"\\n     [class.mat-sort-header-recently-cleared-ascending]=\\\"_recentlyCleared() === 'asc'\\\"\\n     [class.mat-sort-header-recently-cleared-descending]=\\\"_recentlyCleared() === 'desc'\\\"\\n     [class.mat-sort-header-animations-disabled]=\\\"_animationModule === 'NoopAnimations'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  @if (_renderArrow()) {\\n    <div class=\\\"mat-sort-header-arrow\\\">\\n      <svg viewBox=\\\"0 -960 960 960\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z\\\"/>\\n      </svg>\\n    </div>\\n  }\\n</div>\\n\",\n      styles: [\".mat-sort-header{cursor:pointer}.mat-sort-header-disabled{cursor:default}.mat-sort-header-container{display:flex;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}\\n\"]\n    }]\n  }], () => [], {\n    id: [{\n      type: Input,\n      args: ['mat-sort-header']\n    }],\n    arrowPosition: [{\n      type: Input\n    }],\n    start: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    sortActionDescription: [{\n      type: Input\n    }],\n    disableClear: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatSortModule {}\n_MatSortModule = MatSortModule;\n_defineProperty(MatSortModule, \"\\u0275fac\", function _MatSortModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatSortModule)();\n});\n_defineProperty(MatSortModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatSortModule,\n  imports: [MatCommonModule, MatSort, MatSortHeader],\n  exports: [MatSort, MatSortHeader]\n}));\n_defineProperty(MatSortModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n  imports: [MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatSort, MatSortHeader],\n      exports: [MatSort, MatSortHeader],\n      providers: [MAT_SORT_HEADER_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by MatSort.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matSortAnimations = {\n  // Represents:\n  // trigger('indicator', [\n  //   state('active-asc, asc', style({transform: 'translateY(0px)'})),\n  //   // 10px is the height of the sort indicator, minus the width of the pointers\n  //   state('active-desc, desc', style({transform: 'translateY(10px)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that moves the sort indicator. */\n  indicator: {\n    type: 7,\n    name: 'indicator',\n    definitions: [{\n      type: 0,\n      name: 'active-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(0px)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'active-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(10px)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'active-asc <=> active-desc',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('leftPointer', [\n  //   state('active-asc, asc', style({transform: 'rotate(-45deg)'})),\n  //   state('active-desc, desc', style({transform: 'rotate(45deg)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n  leftPointer: {\n    type: 7,\n    name: 'leftPointer',\n    definitions: [{\n      type: 0,\n      name: 'active-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(-45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'active-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'active-asc <=> active-desc',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('rightPointer', [\n  //   state('active-asc, asc', style({transform: 'rotate(45deg)'})),\n  //   state('active-desc, desc', style({transform: 'rotate(-45deg)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n  rightPointer: {\n    type: 7,\n    name: 'rightPointer',\n    definitions: [{\n      type: 0,\n      name: 'active-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'active-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(-45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'active-asc <=> active-desc',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('arrowOpacity', [\n  //   state('desc-to-active, asc-to-active, active', style({opacity: 1})),\n  //   state('desc-to-hint, asc-to-hint, hint', style({opacity: 0.54})),\n  //   state(\n  //     'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n  //     style({opacity: 0}),\n  //   ),\n  //   // Transition between all states except for immediate transitions\n  //   transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')),\n  //   transition('* <=> *', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that controls the arrow opacity. */\n  arrowOpacity: {\n    type: 7,\n    name: 'arrowOpacity',\n    definitions: [{\n      type: 0,\n      name: 'desc-to-active, asc-to-active, active',\n      styles: {\n        type: 6,\n        styles: {\n          'opacity': 1\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'desc-to-hint, asc-to-hint, hint',\n      styles: {\n        type: 6,\n        styles: {\n          'opacity': 0.54\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n      styles: {\n        type: 6,\n        styles: {\n          'opacity': 0\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => asc, * => desc, * => active, * => hint, * => void',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '0ms'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* <=> *',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('arrowPosition', [\n  //   // Hidden Above => Hint Center\n  //   transition(\n  //     '* => desc-to-hint, * => desc-to-active',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(-25%)'}), style({transform: 'translateY(0)'})]),\n  //     ),\n  //   ),\n  //   // Hint Center => Hidden Below\n  //   transition(\n  //     '* => hint-to-desc, * => active-to-desc',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(25%)'})]),\n  //     ),\n  //   ),\n  //   // Hidden Below => Hint Center\n  //   transition(\n  //     '* => asc-to-hint, * => asc-to-active',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(25%)'}), style({transform: 'translateY(0)'})]),\n  //     ),\n  //   ),\n  //   // Hint Center => Hidden Above\n  //   transition(\n  //     '* => hint-to-asc, * => active-to-asc',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(-25%)'})]),\n  //     ),\n  //   ),\n  //   state(\n  //     'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n  //     style({transform: 'translateY(0)'}),\n  //   ),\n  //   state('hint-to-desc, active-to-desc, desc', style({transform: 'translateY(-25%)'})),\n  //   state('hint-to-asc, active-to-asc, asc', style({transform: 'translateY(25%)'})),\n  // ])\n  /**\n   * Animation for the translation of the arrow as a whole. States are separated into two\n   * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n   * peek, and active. The other states define a specific animation (source-to-destination)\n   * and are determined as a function of their prev user-perceived state and what the next state\n   * should be.\n   */\n  arrowPosition: {\n    type: 7,\n    name: 'arrowPosition',\n    definitions: [{\n      type: 1,\n      expr: '* => desc-to-hint, * => desc-to-active',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(-25%)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => hint-to-desc, * => active-to-desc',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(25%)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => asc-to-hint, * => asc-to-active',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(25%)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => hint-to-asc, * => active-to-asc',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(-25%)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 0,\n      name: 'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(0)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'hint-to-desc, active-to-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(-25%)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'hint-to-asc, active-to-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(25%)'\n        },\n        offset: null\n      }\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('allowChildren', [\n  //   transition('* <=> *', [query('@*', animateChild(), {optional: true})]),\n  // ])\n  /** Necessary trigger that calls animate on children animations. */\n  allowChildren: {\n    type: 7,\n    name: 'allowChildren',\n    definitions: [{\n      type: 1,\n      expr: '* <=> *',\n      animation: [{\n        type: 11,\n        selector: '@*',\n        animation: {\n          type: 9,\n          options: null\n        },\n        options: {\n          optional: true\n        }\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };\n//# sourceMappingURL=sort.mjs.map", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "InjectionToken", "EventEmitter", "booleanAttribute", "Directive", "Optional", "Inject", "Input", "Output", "Injectable", "SkipSelf", "inject", "ChangeDetectorRef", "ElementRef", "ANIMATION_MODULE_TYPE", "signal", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "NgModule", "FocusMonitor", "AriaDescriber", "SPACE", "ENTER", "ReplaySubject", "Subject", "merge", "_CdkPrivateStyleLoader", "_", "_StructuralStylesLoader", "M", "MatCommonModule", "getSortDuplicateSortableIdError", "id", "Error", "getSortHeaderNotContainedWithinSortError", "getSortHeaderMissingIdError", "getSortInvalidDirectionError", "direction", "MAT_SORT_DEFAULT_OPTIONS", "MatSort", "_direction", "ngDevMode", "constructor", "_defaultOptions", "_defineProperty", "Map", "_initializedStream", "register", "sortable", "sortables", "has", "set", "deregister", "delete", "sort", "active", "start", "getNextSortDirection", "sortChange", "emit", "_ref", "_sortable$disableClea", "_this$_defaultOptions", "disableClear", "sortDirectionCycle", "getSortDirectionCycle", "nextDirectionIndex", "indexOf", "length", "ngOnInit", "next", "ngOnChanges", "_stateChanges", "ngOnDestroy", "complete", "_MatSort", "_MatSort_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "disabled", "outputs", "exportAs", "features", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "args", "selector", "host", "undefined", "decorators", "alias", "transform", "sortOrder", "reverse", "push", "MatSortHeaderIntl", "_MatSortHeaderIntl", "_MatSortHeaderIntl_Factory", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "MAT_SORT_HEADER_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_SORT_HEADER_INTL_PROVIDER", "provide", "deps", "useFactory", "Mat<PERSON>ort<PERSON><PERSON>er", "sortActionDescription", "_sortActionDescription", "value", "_updateSortActionDescription", "optional", "load", "defaultOptions", "_sort", "arrowPosition", "_columnDef", "name", "_renderChanges", "subscribe", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_sortButton", "_elementRef", "nativeElement", "querySelector", "ngAfterViewInit", "_focusMonitor", "monitor", "_recentlyCleared", "_this$_renderChanges", "stopMonitoring", "unsubscribe", "_this$_ariaDescriber", "_ariaDescriber", "removeDescription", "_toggleOnInteraction", "_isDisabled", "wasSorted", "_isSorted", "prevDirection", "_handleKeydown", "event", "keyCode", "preventDefault", "_getAriaSortAttribute", "_renderArrow", "newDescription", "_this$_ariaDescriber2", "_this$_ariaDescriber3", "describe", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_MatSortHeader_Factory", "ɵɵdefineComponent", "hostVars", "hostBindings", "_MatSortHeader_HostBindings", "rf", "ctx", "ɵɵlistener", "_MatSortHeader_click_HostB<PERSON>ingHandler", "_MatSortHeader_keydown_HostBindingHandler", "$event", "_MatSortHeader_mouseleave_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "attrs", "_c0", "ngContentSelectors", "_c1", "decls", "vars", "consts", "template", "_MatSortHeader_Template", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵtemplate", "_MatSortHeader_Conditional_3_Template", "_animationModule", "ɵɵadvance", "ɵɵconditional", "styles", "encapsulation", "changeDetection", "None", "OnPush", "MatSortModule", "_MatSortModule", "_MatSortModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector", "providers", "matSortAnimations", "indicator", "definitions", "offset", "expr", "animation", "timings", "options", "leftPointer", "rightPointer", "arrowOpacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/sort.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, booleanAttribute, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, inject, ChangeDetectorRef, ElementRef, ANIMATION_MODULE_TYPE, signal, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport { FocusMonitor, AriaDescriber } from '@angular/cdk/a11y';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { ReplaySubject, Subject, merge } from 'rxjs';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/bidi';\n\n/** @docs-private */\nfunction getSortDuplicateSortableIdError(id) {\n    return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n    return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n    return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n    return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nclass MatSort {\n    _defaultOptions;\n    _initializedStream = new ReplaySubject(1);\n    /** Collection of all registered sortables that this directive manages. */\n    sortables = new Map();\n    /** Used to notify any child components listening to state changes. */\n    _stateChanges = new Subject();\n    /** The id of the most recently sorted MatSortable. */\n    active;\n    /**\n     * The direction to set when an MatSortable is initially sorted.\n     * May be overridden by the MatSortable's sort start.\n     */\n    start = 'asc';\n    /** The sort direction of the currently active MatSortable. */\n    get direction() {\n        return this._direction;\n    }\n    set direction(direction) {\n        if (direction &&\n            direction !== 'asc' &&\n            direction !== 'desc' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getSortInvalidDirectionError(direction);\n        }\n        this._direction = direction;\n    }\n    _direction = '';\n    /**\n     * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n     * May be overridden by the MatSortable's disable clear input.\n     */\n    disableClear;\n    /** Whether the sortable is disabled. */\n    disabled = false;\n    /** Event emitted when the user changes either the active sort or sort direction. */\n    sortChange = new EventEmitter();\n    /** Emits when the paginator is initialized. */\n    initialized = this._initializedStream;\n    constructor(_defaultOptions) {\n        this._defaultOptions = _defaultOptions;\n    }\n    /**\n     * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n     * collection of MatSortables.\n     */\n    register(sortable) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!sortable.id) {\n                throw getSortHeaderMissingIdError();\n            }\n            if (this.sortables.has(sortable.id)) {\n                throw getSortDuplicateSortableIdError(sortable.id);\n            }\n        }\n        this.sortables.set(sortable.id, sortable);\n    }\n    /**\n     * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n     * collection of contained MatSortables.\n     */\n    deregister(sortable) {\n        this.sortables.delete(sortable.id);\n    }\n    /** Sets the active sort id and determines the new sort direction. */\n    sort(sortable) {\n        if (this.active != sortable.id) {\n            this.active = sortable.id;\n            this.direction = sortable.start ? sortable.start : this.start;\n        }\n        else {\n            this.direction = this.getNextSortDirection(sortable);\n        }\n        this.sortChange.emit({ active: this.active, direction: this.direction });\n    }\n    /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n    getNextSortDirection(sortable) {\n        if (!sortable) {\n            return '';\n        }\n        // Get the sort direction cycle with the potential sortable overrides.\n        const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n        let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n        // Get and return the next direction in the cycle\n        let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n        if (nextDirectionIndex >= sortDirectionCycle.length) {\n            nextDirectionIndex = 0;\n        }\n        return sortDirectionCycle[nextDirectionIndex];\n    }\n    ngOnInit() {\n        this._initializedStream.next();\n    }\n    ngOnChanges() {\n        this._stateChanges.next();\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n        this._initializedStream.complete();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSort, deps: [{ token: MAT_SORT_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatSort, isStandalone: true, selector: \"[matSort]\", inputs: { active: [\"matSortActive\", \"active\"], start: [\"matSortStart\", \"start\"], direction: [\"matSortDirection\", \"direction\"], disableClear: [\"matSortDisableClear\", \"disableClear\", booleanAttribute], disabled: [\"matSortDisabled\", \"disabled\", booleanAttribute] }, outputs: { sortChange: \"matSortChange\" }, host: { classAttribute: \"mat-sort\" }, exportAs: [\"matSort\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSort, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matSort]',\n                    exportAs: 'matSort',\n                    host: {\n                        'class': 'mat-sort',\n                    },\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SORT_DEFAULT_OPTIONS]\n                }] }], propDecorators: { active: [{\n                type: Input,\n                args: ['matSortActive']\n            }], start: [{\n                type: Input,\n                args: ['matSortStart']\n            }], direction: [{\n                type: Input,\n                args: ['matSortDirection']\n            }], disableClear: [{\n                type: Input,\n                args: [{ alias: 'matSortDisableClear', transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'matSortDisabled', transform: booleanAttribute }]\n            }], sortChange: [{\n                type: Output,\n                args: ['matSortChange']\n            }] } });\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n    let sortOrder = ['asc', 'desc'];\n    if (start == 'desc') {\n        sortOrder.reverse();\n    }\n    if (!disableClear) {\n        sortOrder.push('');\n    }\n    return sortOrder;\n}\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nclass MatSortHeaderIntl {\n    /**\n     * Stream that emits whenever the labels here are changed. Use this to notify\n     * components if the labels have changed after initialization.\n     */\n    changes = new Subject();\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSortHeaderIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSortHeaderIntl, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSortHeaderIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatSortHeaderIntl();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n    // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n    provide: MatSortHeaderIntl,\n    deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n    useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY,\n};\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nclass MatSortHeader {\n    _intl = inject(MatSortHeaderIntl);\n    _sort = inject(MatSort, { optional: true });\n    _columnDef = inject('MAT_SORT_HEADER_COLUMN_DEF', {\n        optional: true,\n    });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _focusMonitor = inject(FocusMonitor);\n    _elementRef = inject(ElementRef);\n    _ariaDescriber = inject(AriaDescriber, { optional: true });\n    _renderChanges;\n    _animationModule = inject(ANIMATION_MODULE_TYPE, { optional: true });\n    /**\n     * Indicates which state was just cleared from the sort header.\n     * Will be reset on the next interaction. Used for coordinating animations.\n     */\n    _recentlyCleared = signal(null);\n    /**\n     * The element with role=\"button\" inside this component's view. We need this\n     * in order to apply a description with AriaDescriber.\n     */\n    _sortButton;\n    /**\n     * ID of this sort header. If used within the context of a CdkColumnDef, this will default to\n     * the column's name.\n     */\n    id;\n    /** Sets the position of the arrow that displays when sorted. */\n    arrowPosition = 'after';\n    /** Overrides the sort start value of the containing MatSort for this MatSortable. */\n    start;\n    /** whether the sort header is disabled. */\n    disabled = false;\n    /**\n     * Description applied to MatSortHeader's button element with aria-describedby. This text should\n     * describe the action that will occur when the user clicks the sort header.\n     */\n    get sortActionDescription() {\n        return this._sortActionDescription;\n    }\n    set sortActionDescription(value) {\n        this._updateSortActionDescription(value);\n    }\n    // Default the action description to \"Sort\" because it's better than nothing.\n    // Without a description, the button's label comes from the sort header text content,\n    // which doesn't give any indication that it performs a sorting operation.\n    _sortActionDescription = 'Sort';\n    /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n    disableClear;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const defaultOptions = inject(MAT_SORT_DEFAULT_OPTIONS, {\n            optional: true,\n        });\n        // Note that we use a string token for the `_columnDef`, because the value is provided both by\n        // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n        // and we want to avoid having the sort header depending on the CDK table because\n        // of this single reference.\n        if (!this._sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getSortHeaderNotContainedWithinSortError();\n        }\n        if (defaultOptions?.arrowPosition) {\n            this.arrowPosition = defaultOptions?.arrowPosition;\n        }\n    }\n    ngOnInit() {\n        if (!this.id && this._columnDef) {\n            this.id = this._columnDef.name;\n        }\n        this._sort.register(this);\n        this._renderChanges = merge(this._sort._stateChanges, this._sort.sortChange).subscribe(() => this._changeDetectorRef.markForCheck());\n        this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n        this._updateSortActionDescription(this._sortActionDescription);\n    }\n    ngAfterViewInit() {\n        // We use the focus monitor because we also want to style\n        // things differently based on the focus origin.\n        this._focusMonitor\n            .monitor(this._elementRef, true)\n            .subscribe(() => this._recentlyCleared.set(null));\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._sort.deregister(this);\n        this._renderChanges?.unsubscribe();\n        if (this._sortButton) {\n            this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n        }\n    }\n    /** Triggers the sort on this sort header and removes the indicator hint. */\n    _toggleOnInteraction() {\n        if (!this._isDisabled()) {\n            const wasSorted = this._isSorted();\n            const prevDirection = this._sort.direction;\n            this._sort.sort(this);\n            this._recentlyCleared.set(wasSorted && !this._isSorted() ? prevDirection : null);\n        }\n    }\n    _handleKeydown(event) {\n        if (event.keyCode === SPACE || event.keyCode === ENTER) {\n            event.preventDefault();\n            this._toggleOnInteraction();\n        }\n    }\n    /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n    _isSorted() {\n        return (this._sort.active == this.id &&\n            (this._sort.direction === 'asc' || this._sort.direction === 'desc'));\n    }\n    _isDisabled() {\n        return this._sort.disabled || this.disabled;\n    }\n    /**\n     * Gets the aria-sort attribute that should be applied to this sort header. If this header\n     * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n     * says that the aria-sort property should only be present on one header at a time, so removing\n     * ensures this is true.\n     */\n    _getAriaSortAttribute() {\n        if (!this._isSorted()) {\n            return 'none';\n        }\n        return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n    }\n    /** Whether the arrow inside the sort header should be rendered. */\n    _renderArrow() {\n        return !this._isDisabled() || this._isSorted();\n    }\n    _updateSortActionDescription(newDescription) {\n        // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n        // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n        // for every *cell* in the table, creating a lot of unnecessary noise.\n        // If _sortButton is undefined, the component hasn't been initialized yet so there's\n        // nothing to update in the DOM.\n        if (this._sortButton) {\n            // removeDescription will no-op if there is no existing message.\n            // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n            this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n            this._ariaDescriber?.describe(this._sortButton, newDescription);\n        }\n        this._sortActionDescription = newDescription;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSortHeader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatSortHeader, isStandalone: true, selector: \"[mat-sort-header]\", inputs: { id: [\"mat-sort-header\", \"id\"], arrowPosition: \"arrowPosition\", start: \"start\", disabled: [\"disabled\", \"disabled\", booleanAttribute], sortActionDescription: \"sortActionDescription\", disableClear: [\"disableClear\", \"disableClear\", booleanAttribute] }, host: { listeners: { \"click\": \"_toggleOnInteraction()\", \"keydown\": \"_handleKeydown($event)\", \"mouseleave\": \"_recentlyCleared.set(null)\" }, properties: { \"attr.aria-sort\": \"_getAriaSortAttribute()\", \"class.mat-sort-header-disabled\": \"_isDisabled()\" }, classAttribute: \"mat-sort-header\" }, exportAs: [\"matSortHeader\"], ngImport: i0, template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [class.mat-sort-header-descending]=\\\"this._sort.direction === 'desc'\\\"\\n     [class.mat-sort-header-ascending]=\\\"this._sort.direction === 'asc'\\\"\\n     [class.mat-sort-header-recently-cleared-ascending]=\\\"_recentlyCleared() === 'asc'\\\"\\n     [class.mat-sort-header-recently-cleared-descending]=\\\"_recentlyCleared() === 'desc'\\\"\\n     [class.mat-sort-header-animations-disabled]=\\\"_animationModule === 'NoopAnimations'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  @if (_renderArrow()) {\\n    <div class=\\\"mat-sort-header-arrow\\\">\\n      <svg viewBox=\\\"0 -960 960 960\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z\\\"/>\\n      </svg>\\n    </div>\\n  }\\n</div>\\n\", styles: [\".mat-sort-header{cursor:pointer}.mat-sort-header-disabled{cursor:default}.mat-sort-header-container{display:flex;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSortHeader, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-sort-header]', exportAs: 'matSortHeader', host: {\n                        'class': 'mat-sort-header',\n                        '(click)': '_toggleOnInteraction()',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(mouseleave)': '_recentlyCleared.set(null)',\n                        '[attr.aria-sort]': '_getAriaSortAttribute()',\n                        '[class.mat-sort-header-disabled]': '_isDisabled()',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [class.mat-sort-header-descending]=\\\"this._sort.direction === 'desc'\\\"\\n     [class.mat-sort-header-ascending]=\\\"this._sort.direction === 'asc'\\\"\\n     [class.mat-sort-header-recently-cleared-ascending]=\\\"_recentlyCleared() === 'asc'\\\"\\n     [class.mat-sort-header-recently-cleared-descending]=\\\"_recentlyCleared() === 'desc'\\\"\\n     [class.mat-sort-header-animations-disabled]=\\\"_animationModule === 'NoopAnimations'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  @if (_renderArrow()) {\\n    <div class=\\\"mat-sort-header-arrow\\\">\\n      <svg viewBox=\\\"0 -960 960 960\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z\\\"/>\\n      </svg>\\n    </div>\\n  }\\n</div>\\n\", styles: [\".mat-sort-header{cursor:pointer}.mat-sort-header-disabled{cursor:default}.mat-sort-header-container{display:flex;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { id: [{\n                type: Input,\n                args: ['mat-sort-header']\n            }], arrowPosition: [{\n                type: Input\n            }], start: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], sortActionDescription: [{\n                type: Input\n            }], disableClear: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\nclass MatSortModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSortModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSortModule, imports: [MatCommonModule, MatSort, MatSortHeader], exports: [MatSort, MatSortHeader] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSortModule, providers: [MAT_SORT_HEADER_INTL_PROVIDER], imports: [MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSortModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatSort, MatSortHeader],\n                    exports: [MatSort, MatSortHeader],\n                    providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n                }]\n        }] });\n\n/**\n * Animations used by MatSort.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matSortAnimations = {\n    // Represents:\n    // trigger('indicator', [\n    //   state('active-asc, asc', style({transform: 'translateY(0px)'})),\n    //   // 10px is the height of the sort indicator, minus the width of the pointers\n    //   state('active-desc, desc', style({transform: 'translateY(10px)'})),\n    //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    // ])\n    /** Animation that moves the sort indicator. */\n    indicator: {\n        type: 7,\n        name: 'indicator',\n        definitions: [\n            {\n                type: 0,\n                name: 'active-asc, asc',\n                styles: { type: 6, styles: { transform: 'translateY(0px)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'active-desc, desc',\n                styles: { type: 6, styles: { transform: 'translateY(10px)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'active-asc <=> active-desc',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('leftPointer', [\n    //   state('active-asc, asc', style({transform: 'rotate(-45deg)'})),\n    //   state('active-desc, desc', style({transform: 'rotate(45deg)'})),\n    //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    // ])\n    /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n    leftPointer: {\n        type: 7,\n        name: 'leftPointer',\n        definitions: [\n            {\n                type: 0,\n                name: 'active-asc, asc',\n                styles: { type: 6, styles: { transform: 'rotate(-45deg)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'active-desc, desc',\n                styles: { type: 6, styles: { transform: 'rotate(45deg)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'active-asc <=> active-desc',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('rightPointer', [\n    //   state('active-asc, asc', style({transform: 'rotate(45deg)'})),\n    //   state('active-desc, desc', style({transform: 'rotate(-45deg)'})),\n    //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n    // ])\n    /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n    rightPointer: {\n        type: 7,\n        name: 'rightPointer',\n        definitions: [\n            {\n                type: 0,\n                name: 'active-asc, asc',\n                styles: { type: 6, styles: { transform: 'rotate(45deg)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'active-desc, desc',\n                styles: { type: 6, styles: { transform: 'rotate(-45deg)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: 'active-asc <=> active-desc',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('arrowOpacity', [\n    //   state('desc-to-active, asc-to-active, active', style({opacity: 1})),\n    //   state('desc-to-hint, asc-to-hint, hint', style({opacity: 0.54})),\n    //   state(\n    //     'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n    //     style({opacity: 0}),\n    //   ),\n    //   // Transition between all states except for immediate transitions\n    //   transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')),\n    //   transition('* <=> *', animate(SORT_ANIMATION_TRANSITION)),\n    // ])\n    /** Animation that controls the arrow opacity. */\n    arrowOpacity: {\n        type: 7,\n        name: 'arrowOpacity',\n        definitions: [\n            {\n                type: 0,\n                name: 'desc-to-active, asc-to-active, active',\n                styles: { type: 6, styles: { 'opacity': 1 }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'desc-to-hint, asc-to-hint, hint',\n                styles: { type: 6, styles: { 'opacity': 0.54 }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n                styles: { type: 6, styles: { 'opacity': 0 }, offset: null },\n            },\n            {\n                type: 1,\n                expr: '* => asc, * => desc, * => active, * => hint, * => void',\n                animation: { type: 4, styles: null, timings: '0ms' },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* <=> *',\n                animation: { type: 4, styles: null, timings: '225ms cubic-bezier(0.4,0.0,0.2,1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('arrowPosition', [\n    //   // Hidden Above => Hint Center\n    //   transition(\n    //     '* => desc-to-hint, * => desc-to-active',\n    //     animate(\n    //       SORT_ANIMATION_TRANSITION,\n    //       keyframes([style({transform: 'translateY(-25%)'}), style({transform: 'translateY(0)'})]),\n    //     ),\n    //   ),\n    //   // Hint Center => Hidden Below\n    //   transition(\n    //     '* => hint-to-desc, * => active-to-desc',\n    //     animate(\n    //       SORT_ANIMATION_TRANSITION,\n    //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(25%)'})]),\n    //     ),\n    //   ),\n    //   // Hidden Below => Hint Center\n    //   transition(\n    //     '* => asc-to-hint, * => asc-to-active',\n    //     animate(\n    //       SORT_ANIMATION_TRANSITION,\n    //       keyframes([style({transform: 'translateY(25%)'}), style({transform: 'translateY(0)'})]),\n    //     ),\n    //   ),\n    //   // Hint Center => Hidden Above\n    //   transition(\n    //     '* => hint-to-asc, * => active-to-asc',\n    //     animate(\n    //       SORT_ANIMATION_TRANSITION,\n    //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(-25%)'})]),\n    //     ),\n    //   ),\n    //   state(\n    //     'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n    //     style({transform: 'translateY(0)'}),\n    //   ),\n    //   state('hint-to-desc, active-to-desc, desc', style({transform: 'translateY(-25%)'})),\n    //   state('hint-to-asc, active-to-asc, asc', style({transform: 'translateY(25%)'})),\n    // ])\n    /**\n     * Animation for the translation of the arrow as a whole. States are separated into two\n     * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n     * peek, and active. The other states define a specific animation (source-to-destination)\n     * and are determined as a function of their prev user-perceived state and what the next state\n     * should be.\n     */\n    arrowPosition: {\n        type: 7,\n        name: 'arrowPosition',\n        definitions: [\n            {\n                type: 1,\n                expr: '* => desc-to-hint, * => desc-to-active',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 5,\n                        'steps': [\n                            { type: 6, styles: { transform: 'translateY(-25%)' }, offset: null },\n                            { type: 6, styles: { transform: 'translateY(0)' }, offset: null },\n                        ],\n                    },\n                    timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => hint-to-desc, * => active-to-desc',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 5,\n                        'steps': [\n                            { type: 6, styles: { transform: 'translateY(0)' }, offset: null },\n                            { type: 6, styles: { transform: 'translateY(25%)' }, offset: null },\n                        ],\n                    },\n                    timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => asc-to-hint, * => asc-to-active',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 5,\n                        'steps': [\n                            { type: 6, styles: { transform: 'translateY(25%)' }, offset: null },\n                            { type: 6, styles: { transform: 'translateY(0)' }, offset: null },\n                        ],\n                    },\n                    timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => hint-to-asc, * => active-to-asc',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 5,\n                        'steps': [\n                            { type: 6, styles: { transform: 'translateY(0)' }, offset: null },\n                            { type: 6, styles: { transform: 'translateY(-25%)' }, offset: null },\n                        ],\n                    },\n                    timings: '225ms cubic-bezier(0.4,0.0,0.2,1)',\n                },\n                options: null,\n            },\n            {\n                type: 0,\n                name: 'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n                styles: { type: 6, styles: { transform: 'translateY(0)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'hint-to-desc, active-to-desc, desc',\n                styles: { type: 6, styles: { transform: 'translateY(-25%)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'hint-to-asc, active-to-asc, asc',\n                styles: { type: 6, styles: { transform: 'translateY(25%)' }, offset: null },\n            },\n        ],\n        options: {},\n    },\n    // Represents:\n    // trigger('allowChildren', [\n    //   transition('* <=> *', [query('@*', animateChild(), {optional: true})]),\n    // ])\n    /** Necessary trigger that calls animate on children animations. */\n    allowChildren: {\n        type: 7,\n        name: 'allowChildren',\n        definitions: [\n            {\n                type: 1,\n                expr: '* <=> *',\n                animation: [\n                    {\n                        type: 11,\n                        selector: '@*',\n                        animation: { type: 9, options: null },\n                        options: { optional: true },\n                    },\n                ],\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };\n//# sourceMappingURL=sort.mjs.map\n"], "mappings": ";;;;;;IAqIiFA,EAAE,CAAAC,cAAA,YA0OgoF,CAAC;IA1OnoFD,EAAE,CAAAE,cAAA;IAAFF,EAAE,CAAAC,cAAA,YA0OitF,CAAC;IA1OptFD,EAAE,CAAAG,SAAA,aA0O4yF,CAAC;IA1O/yFH,EAAE,CAAAI,YAAA,CA0O0zF,CAAC,CAAW,CAAC;EAAA;AAAA;AA/W15F,OAAO,KAAKJ,EAAE,MAAM,eAAe;AACnC,SAASK,cAAc,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,QAAQ,eAAe;AACvR,SAASC,YAAY,EAAEC,aAAa,QAAQ,mBAAmB;AAC/D,SAASC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpD,SAASC,aAAa,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACpD,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;;AAE1B;AACA,SAASC,+BAA+BA,CAACC,EAAE,EAAE;EACzC,OAAOC,KAAK,CAAC,kDAAkDD,EAAE,IAAI,CAAC;AAC1E;AACA;AACA,SAASE,wCAAwCA,CAAA,EAAG;EAChD,OAAOD,KAAK,CAAC,kFAAkF,CAAC;AACpG;AACA;AACA,SAASE,2BAA2BA,CAAA,EAAG;EACnC,OAAOF,KAAK,CAAC,kDAAkD,CAAC;AACpE;AACA;AACA,SAASG,4BAA4BA,CAACC,SAAS,EAAE;EAC7C,OAAOJ,KAAK,CAAC,GAAGI,SAAS,mDAAmD,CAAC;AACjF;;AAEA;AACA,MAAMC,wBAAwB,GAAG,IAAItC,cAAc,CAAC,0BAA0B,CAAC;AAC/E;AACA,MAAMuC,OAAO,CAAC;EAcV;EACA,IAAIF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACG,UAAU;EAC1B;EACA,IAAIH,SAASA,CAACA,SAAS,EAAE;IACrB,IAAIA,SAAS,IACTA,SAAS,KAAK,KAAK,IACnBA,SAAS,KAAK,MAAM,KACnB,OAAOI,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAML,4BAA4B,CAACC,SAAS,CAAC;IACjD;IACA,IAAI,CAACG,UAAU,GAAGH,SAAS;EAC/B;EAaAK,WAAWA,CAACC,eAAe,EAAE;IAAAC,eAAA;IAAAA,eAAA,6BArCR,IAAIrB,aAAa,CAAC,CAAC,CAAC;IACzC;IAAAqB,eAAA,oBACY,IAAIC,GAAG,CAAC,CAAC;IACrB;IAAAD,eAAA,wBACgB,IAAIpB,OAAO,CAAC,CAAC;IAC7B;IAAAoB,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA,gBAIQ,KAAK;IAAAA,eAAA,qBAcA,EAAE;IACf;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA,mBACW,KAAK;IAChB;IAAAA,eAAA,qBACa,IAAI3C,YAAY,CAAC,CAAC;IAC/B;IAAA2C,eAAA,sBACc,IAAI,CAACE,kBAAkB;IAEjC,IAAI,CAACH,eAAe,GAAGA,eAAe;EAC1C;EACA;AACJ;AACA;AACA;EACII,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,OAAOP,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAACO,QAAQ,CAAChB,EAAE,EAAE;QACd,MAAMG,2BAA2B,CAAC,CAAC;MACvC;MACA,IAAI,IAAI,CAACc,SAAS,CAACC,GAAG,CAACF,QAAQ,CAAChB,EAAE,CAAC,EAAE;QACjC,MAAMD,+BAA+B,CAACiB,QAAQ,CAAChB,EAAE,CAAC;MACtD;IACJ;IACA,IAAI,CAACiB,SAAS,CAACE,GAAG,CAACH,QAAQ,CAAChB,EAAE,EAAEgB,QAAQ,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACII,UAAUA,CAACJ,QAAQ,EAAE;IACjB,IAAI,CAACC,SAAS,CAACI,MAAM,CAACL,QAAQ,CAAChB,EAAE,CAAC;EACtC;EACA;EACAsB,IAAIA,CAACN,QAAQ,EAAE;IACX,IAAI,IAAI,CAACO,MAAM,IAAIP,QAAQ,CAAChB,EAAE,EAAE;MAC5B,IAAI,CAACuB,MAAM,GAAGP,QAAQ,CAAChB,EAAE;MACzB,IAAI,CAACK,SAAS,GAAGW,QAAQ,CAACQ,KAAK,GAAGR,QAAQ,CAACQ,KAAK,GAAG,IAAI,CAACA,KAAK;IACjE,CAAC,MACI;MACD,IAAI,CAACnB,SAAS,GAAG,IAAI,CAACoB,oBAAoB,CAACT,QAAQ,CAAC;IACxD;IACA,IAAI,CAACU,UAAU,CAACC,IAAI,CAAC;MAAEJ,MAAM,EAAE,IAAI,CAACA,MAAM;MAAElB,SAAS,EAAE,IAAI,CAACA;IAAU,CAAC,CAAC;EAC5E;EACA;EACAoB,oBAAoBA,CAACT,QAAQ,EAAE;IAAA,IAAAY,IAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAC3B,IAAI,CAACd,QAAQ,EAAE;MACX,OAAO,EAAE;IACb;IACA;IACA,MAAMe,YAAY,IAAAH,IAAA,IAAAC,qBAAA,GAAGb,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,YAAY,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAACE,YAAY,cAAAH,IAAA,cAAAA,IAAA,GAAI,CAAC,GAAAE,qBAAA,GAAC,IAAI,CAACnB,eAAe,cAAAmB,qBAAA,eAApBA,qBAAA,CAAsBC,YAAY;IACxG,IAAIC,kBAAkB,GAAGC,qBAAqB,CAACjB,QAAQ,CAACQ,KAAK,IAAI,IAAI,CAACA,KAAK,EAAEO,YAAY,CAAC;IAC1F;IACA,IAAIG,kBAAkB,GAAGF,kBAAkB,CAACG,OAAO,CAAC,IAAI,CAAC9B,SAAS,CAAC,GAAG,CAAC;IACvE,IAAI6B,kBAAkB,IAAIF,kBAAkB,CAACI,MAAM,EAAE;MACjDF,kBAAkB,GAAG,CAAC;IAC1B;IACA,OAAOF,kBAAkB,CAACE,kBAAkB,CAAC;EACjD;EACAG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACvB,kBAAkB,CAACwB,IAAI,CAAC,CAAC;EAClC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,aAAa,CAACF,IAAI,CAAC,CAAC;EAC7B;EACAG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,aAAa,CAACE,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC5B,kBAAkB,CAAC4B,QAAQ,CAAC,CAAC;EACtC;AAGJ;AAACC,QAAA,GAtGKpC,OAAO;AAAAK,eAAA,CAAPL,OAAO,wBAAAqC,iBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAoG0FtC,QAAO,EAG7B5C,EAAE,CAAAmF,iBAAA,CAH6CxC,wBAAwB;AAAA;AAAAM,eAAA,CApGlJL,OAAO,8BAuGoE5C,EAAE,CAAAoF,iBAAA;EAAAC,IAAA,EAFQzC,QAAO;EAAA0C,SAAA;EAAAC,SAAA;EAAAC,MAAA;IAAA5B,MAAA;IAAAC,KAAA;IAAAnB,SAAA;IAAA0B,YAAA,6CAAkO7D,gBAAgB;IAAAkF,QAAA,qCAA6ClF,gBAAgB;EAAA;EAAAmF,OAAA;IAAA3B,UAAA;EAAA;EAAA4B,QAAA;EAAAC,QAAA,GAEhU5F,EAAE,CAAA6F,oBAAA;AAAA;AAAnF;EAAA,QAAA/C,SAAA,oBAAAA,SAAA,KAAiF9C,EAAE,CAAA8F,iBAAA,CAAQlD,OAAO,EAAc,CAAC;IACrGyC,IAAI,EAAE7E,SAAS;IACfuF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WAAW;MACrBL,QAAQ,EAAE,SAAS;MACnBM,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEZ,IAAI,EAAEa,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/Cd,IAAI,EAAE5E;IACV,CAAC,EAAE;MACC4E,IAAI,EAAE3E,MAAM;MACZqF,IAAI,EAAE,CAACpD,wBAAwB;IACnC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEiB,MAAM,EAAE,CAAC;MAClCyB,IAAI,EAAE1E,KAAK;MACXoF,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAElC,KAAK,EAAE,CAAC;MACRwB,IAAI,EAAE1E,KAAK;MACXoF,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAErD,SAAS,EAAE,CAAC;MACZ2C,IAAI,EAAE1E,KAAK;MACXoF,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE3B,YAAY,EAAE,CAAC;MACfiB,IAAI,EAAE1E,KAAK;MACXoF,IAAI,EAAE,CAAC;QAAEK,KAAK,EAAE,qBAAqB;QAAEC,SAAS,EAAE9F;MAAiB,CAAC;IACxE,CAAC,CAAC;IAAEkF,QAAQ,EAAE,CAAC;MACXJ,IAAI,EAAE1E,KAAK;MACXoF,IAAI,EAAE,CAAC;QAAEK,KAAK,EAAE,iBAAiB;QAAEC,SAAS,EAAE9F;MAAiB,CAAC;IACpE,CAAC,CAAC;IAAEwD,UAAU,EAAE,CAAC;MACbsB,IAAI,EAAEzE,MAAM;MACZmF,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAASzB,qBAAqBA,CAACT,KAAK,EAAEO,YAAY,EAAE;EAChD,IAAIkC,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;EAC/B,IAAIzC,KAAK,IAAI,MAAM,EAAE;IACjByC,SAAS,CAACC,OAAO,CAAC,CAAC;EACvB;EACA,IAAI,CAACnC,YAAY,EAAE;IACfkC,SAAS,CAACE,IAAI,CAAC,EAAE,CAAC;EACtB;EACA,OAAOF,SAAS;AACpB;;AAEA;AACA;AACA;AACA;AACA,MAAMG,iBAAiB,CAAC;EAAA1D,YAAA;IACpB;AACJ;AACA;AACA;IAHIE,eAAA,kBAIU,IAAIpB,OAAO,CAAC,CAAC;EAAA;AAG3B;AAAC6E,kBAAA,GARKD,iBAAiB;AAAAxD,eAAA,CAAjBwD,iBAAiB,wBAAAE,2BAAAzB,iBAAA;EAAA,YAAAA,iBAAA,IAMgFuB,kBAAiB;AAAA;AAAAxD,eAAA,CANlHwD,iBAAiB,+BAjD0DzG,EAAE,CAAA4G,kBAAA;EAAAC,KAAA,EAwDwBJ,kBAAiB;EAAAK,OAAA,EAAjBL,kBAAiB,CAAAM,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEhJ;EAAA,QAAAlE,SAAA,oBAAAA,SAAA,KA1DiF9C,EAAE,CAAA8F,iBAAA,CA0DQW,iBAAiB,EAAc,CAAC;IAC/GpB,IAAI,EAAExE,UAAU;IAChBkF,IAAI,EAAE,CAAC;MAAEiB,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,SAASC,qCAAqCA,CAACC,UAAU,EAAE;EACvD,OAAOA,UAAU,IAAI,IAAIT,iBAAiB,CAAC,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,6BAA6B,GAAG;EAClC;EACAC,OAAO,EAAEX,iBAAiB;EAC1BY,IAAI,EAAE,CAAC,CAAC,IAAI5G,QAAQ,CAAC,CAAC,EAAE,IAAIK,QAAQ,CAAC,CAAC,EAAE2F,iBAAiB,CAAC,CAAC;EAC3Da,UAAU,EAAEL;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,aAAa,CAAC;EAiChB;AACJ;AACA;AACA;EACI,IAAIC,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,sBAAsB;EACtC;EACA,IAAID,qBAAqBA,CAACE,KAAK,EAAE;IAC7B,IAAI,CAACC,4BAA4B,CAACD,KAAK,CAAC;EAC5C;EACA;EACA;EACA;;EAIA3E,WAAWA,CAAA,EAAG;IAAAE,eAAA,gBAhDNlC,MAAM,CAAC0F,iBAAiB,CAAC;IAAAxD,eAAA,gBACzBlC,MAAM,CAAC6B,OAAO,EAAE;MAAEgF,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA3E,eAAA,qBAC9BlC,MAAM,CAAC,4BAA4B,EAAE;MAC9C6G,QAAQ,EAAE;IACd,CAAC,CAAC;IAAA3E,eAAA,6BACmBlC,MAAM,CAACC,iBAAiB,CAAC;IAAAiC,eAAA,wBAC9BlC,MAAM,CAACS,YAAY,CAAC;IAAAyB,eAAA,sBACtBlC,MAAM,CAACE,UAAU,CAAC;IAAAgC,eAAA,yBACflC,MAAM,CAACU,aAAa,EAAE;MAAEmG,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA3E,eAAA;IAAAA,eAAA,2BAEvClC,MAAM,CAACG,qBAAqB,EAAE;MAAE0G,QAAQ,EAAE;IAAK,CAAC,CAAC;IACpE;AACJ;AACA;AACA;IAHI3E,eAAA,2BAImB9B,MAAM,CAAC,IAAI,CAAC;IAC/B;AACJ;AACA;AACA;IAHI8B,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA,wBACgB,OAAO;IACvB;IAAAA,eAAA;IAEA;IAAAA,eAAA,mBACW,KAAK;IAAAA,eAAA,iCAcS,MAAM;IAC/B;IAAAA,eAAA;IAGIlC,MAAM,CAACgB,sBAAsB,CAAC,CAAC8F,IAAI,CAAC5F,uBAAuB,CAAC;IAC5D,MAAM6F,cAAc,GAAG/G,MAAM,CAAC4B,wBAAwB,EAAE;MACpDiF,QAAQ,EAAE;IACd,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACG,KAAK,KAAK,OAAOjF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAChE,MAAMP,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAIuF,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEE,aAAa,EAAE;MAC/B,IAAI,CAACA,aAAa,GAAGF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEE,aAAa;IACtD;EACJ;EACAtD,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACrC,EAAE,IAAI,IAAI,CAAC4F,UAAU,EAAE;MAC7B,IAAI,CAAC5F,EAAE,GAAG,IAAI,CAAC4F,UAAU,CAACC,IAAI;IAClC;IACA,IAAI,CAACH,KAAK,CAAC3E,QAAQ,CAAC,IAAI,CAAC;IACzB,IAAI,CAAC+E,cAAc,GAAGrG,KAAK,CAAC,IAAI,CAACiG,KAAK,CAAClD,aAAa,EAAE,IAAI,CAACkD,KAAK,CAAChE,UAAU,CAAC,CAACqE,SAAS,CAAC,MAAM,IAAI,CAACC,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAAC;IACpI,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,aAAa,CAAC,4BAA4B,CAAC;IAC7F,IAAI,CAACf,4BAA4B,CAAC,IAAI,CAACF,sBAAsB,CAAC;EAClE;EACAkB,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAACC,aAAa,CACbC,OAAO,CAAC,IAAI,CAACL,WAAW,EAAE,IAAI,CAAC,CAC/BJ,SAAS,CAAC,MAAM,IAAI,CAACU,gBAAgB,CAACtF,GAAG,CAAC,IAAI,CAAC,CAAC;EACzD;EACAsB,WAAWA,CAAA,EAAG;IAAA,IAAAiE,oBAAA;IACV,IAAI,CAACH,aAAa,CAACI,cAAc,CAAC,IAAI,CAACR,WAAW,CAAC;IACnD,IAAI,CAACT,KAAK,CAACtE,UAAU,CAAC,IAAI,CAAC;IAC3B,CAAAsF,oBAAA,OAAI,CAACZ,cAAc,cAAAY,oBAAA,eAAnBA,oBAAA,CAAqBE,WAAW,CAAC,CAAC;IAClC,IAAI,IAAI,CAACV,WAAW,EAAE;MAAA,IAAAW,oBAAA;MAClB,CAAAA,oBAAA,OAAI,CAACC,cAAc,cAAAD,oBAAA,eAAnBA,oBAAA,CAAqBE,iBAAiB,CAAC,IAAI,CAACb,WAAW,EAAE,IAAI,CAACd,sBAAsB,CAAC;IACzF;EACJ;EACA;EACA4B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;MACrB,MAAMC,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;MAClC,MAAMC,aAAa,GAAG,IAAI,CAAC1B,KAAK,CAACrF,SAAS;MAC1C,IAAI,CAACqF,KAAK,CAACpE,IAAI,CAAC,IAAI,CAAC;MACrB,IAAI,CAACmF,gBAAgB,CAACtF,GAAG,CAAC+F,SAAS,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC,GAAGC,aAAa,GAAG,IAAI,CAAC;IACpF;EACJ;EACAC,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAIA,KAAK,CAACC,OAAO,KAAKlI,KAAK,IAAIiI,KAAK,CAACC,OAAO,KAAKjI,KAAK,EAAE;MACpDgI,KAAK,CAACE,cAAc,CAAC,CAAC;MACtB,IAAI,CAACR,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;EACAG,SAASA,CAAA,EAAG;IACR,OAAQ,IAAI,CAACzB,KAAK,CAACnE,MAAM,IAAI,IAAI,CAACvB,EAAE,KAC/B,IAAI,CAAC0F,KAAK,CAACrF,SAAS,KAAK,KAAK,IAAI,IAAI,CAACqF,KAAK,CAACrF,SAAS,KAAK,MAAM,CAAC;EAC3E;EACA4G,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACvB,KAAK,CAACtC,QAAQ,IAAI,IAAI,CAACA,QAAQ;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqE,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC,EAAE;MACnB,OAAO,MAAM;IACjB;IACA,OAAO,IAAI,CAACzB,KAAK,CAACrF,SAAS,IAAI,KAAK,GAAG,WAAW,GAAG,YAAY;EACrE;EACA;EACAqH,YAAYA,CAAA,EAAG;IACX,OAAO,CAAC,IAAI,CAACT,WAAW,CAAC,CAAC,IAAI,IAAI,CAACE,SAAS,CAAC,CAAC;EAClD;EACA7B,4BAA4BA,CAACqC,cAAc,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACzB,WAAW,EAAE;MAAA,IAAA0B,qBAAA,EAAAC,qBAAA;MAClB;MACA;MACA,CAAAD,qBAAA,OAAI,CAACd,cAAc,cAAAc,qBAAA,eAAnBA,qBAAA,CAAqBb,iBAAiB,CAAC,IAAI,CAACb,WAAW,EAAE,IAAI,CAACd,sBAAsB,CAAC;MACrF,CAAAyC,qBAAA,OAAI,CAACf,cAAc,cAAAe,qBAAA,eAAnBA,qBAAA,CAAqBC,QAAQ,CAAC,IAAI,CAAC5B,WAAW,EAAEyB,cAAc,CAAC;IACnE;IACA,IAAI,CAACvC,sBAAsB,GAAGuC,cAAc;EAChD;AAGJ;AAACI,cAAA,GAhJK7C,aAAa;AAAAtE,eAAA,CAAbsE,aAAa,wBAAA8C,uBAAAnF,iBAAA;EAAA,YAAAA,iBAAA,IA8IoFqC,cAAa;AAAA;AAAAtE,eAAA,CA9I9GsE,aAAa,8BA3F8DvH,EAAE,CAAAsK,iBAAA;EAAAjF,IAAA,EA0OQkC,cAAa;EAAAjC,SAAA;EAAAC,SAAA;EAAAgF,QAAA;EAAAC,YAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA1OvB1K,EAAE,CAAA4K,UAAA,mBAAAC,wCAAA;QAAA,OA0OQF,GAAA,CAAAtB,oBAAA,CAAqB,CAAC;MAAA,CAAV,CAAC,qBAAAyB,0CAAAC,MAAA;QAAA,OAAbJ,GAAA,CAAAjB,cAAA,CAAAqB,MAAqB,CAAC;MAAA,CAAV,CAAC,wBAAAC,6CAAA;QAAA,OAAbL,GAAA,CAAA7B,gBAAA,CAAAtF,GAAA,CAAqB,IAAI,CAAC;MAAA,CAAd,CAAC;IAAA;IAAA,IAAAkH,EAAA;MA1OvB1K,EAAE,CAAAiL,WAAA,cA0OQN,GAAA,CAAAb,qBAAA,CAAsB,CAAC;MA1OjC9J,EAAE,CAAAkL,WAAA,6BA0OQP,GAAA,CAAArB,WAAA,CAAY,EAAC;IAAA;EAAA;EAAA9D,MAAA;IAAAnD,EAAA;IAAA2F,aAAA;IAAAnE,KAAA;IAAA4B,QAAA,8BAAiLlF,gBAAgB;IAAAiH,qBAAA;IAAApD,YAAA,sCAAkG7D,gBAAgB;EAAA;EAAAoF,QAAA;EAAAwF,KAAA,EAAAC,GAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,wBAAAjB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA1O1U1K,EAAE,CAAA4L,eAAA;MAAF5L,EAAE,CAAAC,cAAA,YA0OohE,CAAC,YAAub,CAAC;MA1O/8ED,EAAE,CAAA6L,YAAA,EA0O2+E,CAAC;MA1O9+E7L,EAAE,CAAAI,YAAA,CA0Oq/E,CAAC;MA1Ox/EJ,EAAE,CAAA8L,UAAA,IAAAC,qCAAA,gBA0OqlF,CAAC;MA1OxlF/L,EAAE,CAAAI,YAAA,CA0Om1F,CAAC;IAAA;IAAA,IAAAsK,EAAA;MA1Ot1F1K,EAAE,CAAAkL,WAAA,2BAAAP,GAAA,CAAAnB,SAAA,EA0Om7C,CAAC,oCAAAmB,GAAA,CAAA3C,aAAA,aAA4E,CAAC,+BAAA2C,GAAA,CAAA5C,KAAA,CAAArF,SAAA,WAA4E,CAAC,8BAAAiI,GAAA,CAAA5C,KAAA,CAAArF,SAAA,UAA0E,CAAC,+CAAAiI,GAAA,CAAA7B,gBAAA,YAAyF,CAAC,gDAAA6B,GAAA,CAAA7B,gBAAA,aAA2F,CAAC,wCAAA6B,GAAA,CAAAqB,gBAAA,qBAA2F,CAAC;MA1O76DhM,EAAE,CAAAiL,WAAA,aAAAN,GAAA,CAAArB,WAAA,uBAAAqB,GAAA,CAAArB,WAAA;MAAFtJ,EAAE,CAAAiM,SAAA,EA0O20F,CAAC;MA1O90FjM,EAAE,CAAAkM,aAAA,CAAAvB,GAAA,CAAAZ,YAAA,WA0O20F,CAAC;IAAA;EAAA;EAAAoC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAE/5F;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KA5OiF9C,EAAE,CAAA8F,iBAAA,CA4OQyB,aAAa,EAAc,CAAC;IAC3GlC,IAAI,EAAEjE,SAAS;IACf2E,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEL,QAAQ,EAAE,eAAe;MAAEM,IAAI,EAAE;QAC7D,OAAO,EAAE,iBAAiB;QAC1B,SAAS,EAAE,wBAAwB;QACnC,WAAW,EAAE,wBAAwB;QACrC,cAAc,EAAE,4BAA4B;QAC5C,kBAAkB,EAAE,yBAAyB;QAC7C,kCAAkC,EAAE;MACxC,CAAC;MAAEmG,aAAa,EAAE/K,iBAAiB,CAACiL,IAAI;MAAED,eAAe,EAAE/K,uBAAuB,CAACiL,MAAM;MAAEb,QAAQ,EAAE,qrEAAqrE;MAAES,MAAM,EAAE,CAAC,g0EAAg0E;IAAE,CAAC;EACpnJ,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE9J,EAAE,EAAE,CAAC;MAC7CgD,IAAI,EAAE1E,KAAK;MACXoF,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEiC,aAAa,EAAE,CAAC;MAChB3C,IAAI,EAAE1E;IACV,CAAC,CAAC;IAAEkD,KAAK,EAAE,CAAC;MACRwB,IAAI,EAAE1E;IACV,CAAC,CAAC;IAAE8E,QAAQ,EAAE,CAAC;MACXJ,IAAI,EAAE1E,KAAK;MACXoF,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE9F;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiH,qBAAqB,EAAE,CAAC;MACxBnC,IAAI,EAAE1E;IACV,CAAC,CAAC;IAAEyD,YAAY,EAAE,CAAC;MACfiB,IAAI,EAAE1E,KAAK;MACXoF,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAE9F;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiM,aAAa,CAAC;AAInBC,cAAA,GAJKD,aAAa;AAAAvJ,eAAA,CAAbuJ,aAAa,wBAAAE,uBAAAxH,iBAAA;EAAA,YAAAA,iBAAA,IACoFsH,cAAa;AAAA;AAAAvJ,eAAA,CAD9GuJ,aAAa,8BAvQ8DxM,EAAE,CAAA2M,gBAAA;EAAAtH,IAAA,EAyQqBmH,cAAa;EAAAI,OAAA,GAAYzK,eAAe,EAAES,OAAO,EAAE2E,aAAa;EAAAsF,OAAA,GAAajK,OAAO,EAAE2E,aAAa;AAAA;AAAAtE,eAAA,CAFrMuJ,aAAa,8BAvQ8DxM,EAAE,CAAA8M,gBAAA;EAAAC,SAAA,EA0Q+C,CAAC5F,6BAA6B,CAAC;EAAAyF,OAAA,GAAYzK,eAAe;AAAA;AAE5L;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KA5QiF9C,EAAE,CAAA8F,iBAAA,CA4QQ0G,aAAa,EAAc,CAAC;IAC3GnH,IAAI,EAAE9D,QAAQ;IACdwE,IAAI,EAAE,CAAC;MACC6G,OAAO,EAAE,CAACzK,eAAe,EAAES,OAAO,EAAE2E,aAAa,CAAC;MAClDsF,OAAO,EAAE,CAACjK,OAAO,EAAE2E,aAAa,CAAC;MACjCwF,SAAS,EAAE,CAAC5F,6BAA6B;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6F,iBAAiB,GAAG;EACtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,SAAS,EAAE;IACP5H,IAAI,EAAE,CAAC;IACP6C,IAAI,EAAE,WAAW;IACjBgF,WAAW,EAAE,CACT;MACI7H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,iBAAiB;MACvBiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE9F,SAAS,EAAE;QAAkB,CAAC;QAAE8G,MAAM,EAAE;MAAK;IAC9E,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,mBAAmB;MACzBiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE9F,SAAS,EAAE;QAAmB,CAAC;QAAE8G,MAAM,EAAE;MAAK;IAC/E,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAE,4BAA4B;MAClCC,SAAS,EAAE;QAAEhI,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE,IAAI;QAAEmB,OAAO,EAAE;MAAoC,CAAC;MAClFC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,WAAW,EAAE;IACTnI,IAAI,EAAE,CAAC;IACP6C,IAAI,EAAE,aAAa;IACnBgF,WAAW,EAAE,CACT;MACI7H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,iBAAiB;MACvBiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE9F,SAAS,EAAE;QAAiB,CAAC;QAAE8G,MAAM,EAAE;MAAK;IAC7E,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,mBAAmB;MACzBiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE9F,SAAS,EAAE;QAAgB,CAAC;QAAE8G,MAAM,EAAE;MAAK;IAC5E,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAE,4BAA4B;MAClCC,SAAS,EAAE;QAAEhI,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE,IAAI;QAAEmB,OAAO,EAAE;MAAoC,CAAC;MAClFC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACAE,YAAY,EAAE;IACVpI,IAAI,EAAE,CAAC;IACP6C,IAAI,EAAE,cAAc;IACpBgF,WAAW,EAAE,CACT;MACI7H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,iBAAiB;MACvBiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE9F,SAAS,EAAE;QAAgB,CAAC;QAAE8G,MAAM,EAAE;MAAK;IAC5E,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,mBAAmB;MACzBiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE9F,SAAS,EAAE;QAAiB,CAAC;QAAE8G,MAAM,EAAE;MAAK;IAC7E,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAE,4BAA4B;MAClCC,SAAS,EAAE;QAAEhI,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE,IAAI;QAAEmB,OAAO,EAAE;MAAoC,CAAC;MAClFC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAG,YAAY,EAAE;IACVrI,IAAI,EAAE,CAAC;IACP6C,IAAI,EAAE,cAAc;IACpBgF,WAAW,EAAE,CACT;MACI7H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,uCAAuC;MAC7CiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE,SAAS,EAAE;QAAE,CAAC;QAAEgB,MAAM,EAAE;MAAK;IAC9D,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,iCAAiC;MACvCiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE,SAAS,EAAE;QAAK,CAAC;QAAEgB,MAAM,EAAE;MAAK;IACjE,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,2EAA2E;MACjFiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE,SAAS,EAAE;QAAE,CAAC;QAAEgB,MAAM,EAAE;MAAK;IAC9D,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAE,wDAAwD;MAC9DC,SAAS,EAAE;QAAEhI,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE,IAAI;QAAEmB,OAAO,EAAE;MAAM,CAAC;MACpDC,OAAO,EAAE;IACb,CAAC,EACD;MACIlI,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE;QAAEhI,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE,IAAI;QAAEmB,OAAO,EAAE;MAAoC,CAAC;MAClFC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIvF,aAAa,EAAE;IACX3C,IAAI,EAAE,CAAC;IACP6C,IAAI,EAAE,eAAe;IACrBgF,WAAW,EAAE,CACT;MACI7H,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAE,wCAAwC;MAC9CC,SAAS,EAAE;QACPhI,IAAI,EAAE,CAAC;QACP8G,MAAM,EAAE;UACJ9G,IAAI,EAAE,CAAC;UACP,OAAO,EAAE,CACL;YAAEA,IAAI,EAAE,CAAC;YAAE8G,MAAM,EAAE;cAAE9F,SAAS,EAAE;YAAmB,CAAC;YAAE8G,MAAM,EAAE;UAAK,CAAC,EACpE;YAAE9H,IAAI,EAAE,CAAC;YAAE8G,MAAM,EAAE;cAAE9F,SAAS,EAAE;YAAgB,CAAC;YAAE8G,MAAM,EAAE;UAAK,CAAC;QAEzE,CAAC;QACDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACIlI,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAE,wCAAwC;MAC9CC,SAAS,EAAE;QACPhI,IAAI,EAAE,CAAC;QACP8G,MAAM,EAAE;UACJ9G,IAAI,EAAE,CAAC;UACP,OAAO,EAAE,CACL;YAAEA,IAAI,EAAE,CAAC;YAAE8G,MAAM,EAAE;cAAE9F,SAAS,EAAE;YAAgB,CAAC;YAAE8G,MAAM,EAAE;UAAK,CAAC,EACjE;YAAE9H,IAAI,EAAE,CAAC;YAAE8G,MAAM,EAAE;cAAE9F,SAAS,EAAE;YAAkB,CAAC;YAAE8G,MAAM,EAAE;UAAK,CAAC;QAE3E,CAAC;QACDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACIlI,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAE,sCAAsC;MAC5CC,SAAS,EAAE;QACPhI,IAAI,EAAE,CAAC;QACP8G,MAAM,EAAE;UACJ9G,IAAI,EAAE,CAAC;UACP,OAAO,EAAE,CACL;YAAEA,IAAI,EAAE,CAAC;YAAE8G,MAAM,EAAE;cAAE9F,SAAS,EAAE;YAAkB,CAAC;YAAE8G,MAAM,EAAE;UAAK,CAAC,EACnE;YAAE9H,IAAI,EAAE,CAAC;YAAE8G,MAAM,EAAE;cAAE9F,SAAS,EAAE;YAAgB,CAAC;YAAE8G,MAAM,EAAE;UAAK,CAAC;QAEzE,CAAC;QACDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACIlI,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAE,sCAAsC;MAC5CC,SAAS,EAAE;QACPhI,IAAI,EAAE,CAAC;QACP8G,MAAM,EAAE;UACJ9G,IAAI,EAAE,CAAC;UACP,OAAO,EAAE,CACL;YAAEA,IAAI,EAAE,CAAC;YAAE8G,MAAM,EAAE;cAAE9F,SAAS,EAAE;YAAgB,CAAC;YAAE8G,MAAM,EAAE;UAAK,CAAC,EACjE;YAAE9H,IAAI,EAAE,CAAC;YAAE8G,MAAM,EAAE;cAAE9F,SAAS,EAAE;YAAmB,CAAC;YAAE8G,MAAM,EAAE;UAAK,CAAC;QAE5E,CAAC;QACDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACIlI,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,wEAAwE;MAC9EiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE9F,SAAS,EAAE;QAAgB,CAAC;QAAE8G,MAAM,EAAE;MAAK;IAC5E,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,oCAAoC;MAC1CiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE9F,SAAS,EAAE;QAAmB,CAAC;QAAE8G,MAAM,EAAE;MAAK;IAC/E,CAAC,EACD;MACI9H,IAAI,EAAE,CAAC;MACP6C,IAAI,EAAE,iCAAiC;MACvCiE,MAAM,EAAE;QAAE9G,IAAI,EAAE,CAAC;QAAE8G,MAAM,EAAE;UAAE9F,SAAS,EAAE;QAAkB,CAAC;QAAE8G,MAAM,EAAE;MAAK;IAC9E,CAAC,CACJ;IACDI,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACAI,aAAa,EAAE;IACXtI,IAAI,EAAE,CAAC;IACP6C,IAAI,EAAE,eAAe;IACrBgF,WAAW,EAAE,CACT;MACI7H,IAAI,EAAE,CAAC;MACP+H,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,CACP;QACIhI,IAAI,EAAE,EAAE;QACRW,QAAQ,EAAE,IAAI;QACdqH,SAAS,EAAE;UAAEhI,IAAI,EAAE,CAAC;UAAEkI,OAAO,EAAE;QAAK,CAAC;QACrCA,OAAO,EAAE;UAAE3F,QAAQ,EAAE;QAAK;MAC9B,CAAC,CACJ;MACD2F,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAAS5K,wBAAwB,EAAEwE,6BAA6B,EAAEF,qCAAqC,EAAErE,OAAO,EAAE2E,aAAa,EAAEd,iBAAiB,EAAE+F,aAAa,EAAEQ,iBAAiB;AACpL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}