{"ast": null, "code": "import { moduleMetadata } from '@storybook/angular';\nimport { MatCardModule } from '@angular/material/card';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport moment from 'moment';\nimport { MAT_CALENDAR_MODULES, SwuiMatCalendarModule } from './swui-mat-calendar.module';\nimport { SwuiMatCalendarComponent } from './swui-mat-calendar.component';\nconst template = `\n  <mat-card style=\"margin: 32px\">\n    <lib-swui-mat-calendar\n      [disabled]=\"disabled\"\n      [timeZone]=\"timeZone\"\n      [maxDate]=\"maxDate\"\n      [minDate]=\"minDate\"\n      [(ngModel)]=\"value\">\n    </lib-swui-mat-calendar>\n  </mat-card>\n`;\nconst meta = {\n  title: 'Date/MatCalendar',\n  component: SwuiMatCalendarComponent,\n  decorators: [moduleMetadata({\n    imports: [BrowserAnimationsModule, SwuiMatCalendarModule, MAT_CALENDAR_MODULES, MatCardModule]\n  })],\n  parameters: {\n    layout: 'centered'\n  },\n  tags: ['autodocs']\n};\nexport default meta;\nexport const Default = {\n  args: {},\n  render: args => ({\n    props: args,\n    template\n  })\n};\nexport const TimeZonePlus13 = {\n  args: {\n    timeZone: 'Pacific/Tongatapu'\n  },\n  render: args => ({\n    props: args,\n    template\n  })\n};\nexport const ValueWithTimezoneMinus1 = {\n  args: {\n    timeZone: 'America/Godthab',\n    value: '2020-06-30T00:00:00.000Z'\n  },\n  render: args => ({\n    props: args,\n    template\n  })\n};\nexport const MinDateTodayMinus2 = {\n  args: {\n    minDate: moment().add(-2, 'days').toISOString()\n  },\n  render: args => ({\n    props: args,\n    template\n  })\n};\nexport const MaxDateTodayPlus2 = {\n  args: {\n    maxDate: moment().add(2, 'days').toISOString()\n  },\n  render: args => ({\n    props: args,\n    template\n  })\n};\nexport const Disabled = {\n  args: {\n    disabled: true\n  },\n  render: args => ({\n    props: args,\n    template\n  })\n};", "map": {"version": 3, "names": ["moduleMetadata", "MatCardModule", "BrowserAnimationsModule", "moment", "MAT_CALENDAR_MODULES", "SwuiMatCalendarModule", "SwuiMatCalendarComponent", "template", "meta", "title", "component", "decorators", "imports", "parameters", "layout", "tags", "<PERSON><PERSON><PERSON>", "args", "render", "props", "TimeZonePlus13", "timeZone", "ValueWithTimezoneMinus1", "value", "MinDateTodayMinus2", "minDate", "add", "toISOString", "MaxDateTodayPlus2", "maxDate", "Disabled", "disabled"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-mat-calendar/swui-mat-calendar.stories.ts"], "sourcesContent": ["import { moduleMetadata } from '@storybook/angular';\nimport { MatCardModule } from '@angular/material/card';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport moment from 'moment';\nimport { MAT_CALENDAR_MODULES, SwuiMatCalendarModule } from './swui-mat-calendar.module';\nimport { SwuiMatCalendarComponent } from './swui-mat-calendar.component';\nconst template = `\n  <mat-card style=\"margin: 32px\">\n    <lib-swui-mat-calendar\n      [disabled]=\"disabled\"\n      [timeZone]=\"timeZone\"\n      [maxDate]=\"maxDate\"\n      [minDate]=\"minDate\"\n      [(ngModel)]=\"value\">\n    </lib-swui-mat-calendar>\n  </mat-card>\n`;\nconst meta = {\n    title: 'Date/MatCalendar',\n    component: SwuiMatCalendarComponent,\n    decorators: [\n        moduleMetadata({\n            imports: [\n                BrowserAnimationsModule,\n                SwuiMatCalendarModule,\n                MAT_CALENDAR_MODULES,\n                MatCardModule,\n            ],\n        })\n    ],\n    parameters: {\n        layout: 'centered',\n    },\n    tags: ['autodocs'],\n};\nexport default meta;\nexport const Default = {\n    args: {},\n    render: (args) => ({\n        props: args,\n        template,\n    }),\n};\nexport const TimeZonePlus13 = {\n    args: {\n        timeZone: 'Pacific/Tongatapu',\n    },\n    render: (args) => ({\n        props: args,\n        template,\n    }),\n};\nexport const ValueWithTimezoneMinus1 = {\n    args: {\n        timeZone: 'America/Godthab',\n        value: '2020-06-30T00:00:00.000Z'\n    },\n    render: (args) => ({\n        props: args,\n        template,\n    }),\n};\nexport const MinDateTodayMinus2 = {\n    args: {\n        minDate: moment().add(-2, 'days').toISOString(),\n    },\n    render: (args) => ({\n        props: args,\n        template,\n    }),\n};\nexport const MaxDateTodayPlus2 = {\n    args: {\n        maxDate: moment().add(2, 'days').toISOString()\n    },\n    render: (args) => ({\n        props: args,\n        template,\n    }),\n};\nexport const Disabled = {\n    args: {\n        disabled: true\n    },\n    render: (args) => ({\n        props: args,\n        template,\n    }),\n};\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,oBAAoB,EAAEC,qBAAqB,QAAQ,4BAA4B;AACxF,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,MAAMC,QAAQ,GAAG;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,MAAMC,IAAI,GAAG;EACTC,KAAK,EAAE,kBAAkB;EACzBC,SAAS,EAAEJ,wBAAwB;EACnCK,UAAU,EAAE,CACRX,cAAc,CAAC;IACXY,OAAO,EAAE,CACLV,uBAAuB,EACvBG,qBAAqB,EACrBD,oBAAoB,EACpBH,aAAa;EAErB,CAAC,CAAC,CACL;EACDY,UAAU,EAAE;IACRC,MAAM,EAAE;EACZ,CAAC;EACDC,IAAI,EAAE,CAAC,UAAU;AACrB,CAAC;AACD,eAAeP,IAAI;AACnB,OAAO,MAAMQ,OAAO,GAAG;EACnBC,IAAI,EAAE,CAAC,CAAC;EACRC,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXV;EACJ,CAAC;AACL,CAAC;AACD,OAAO,MAAMa,cAAc,GAAG;EAC1BH,IAAI,EAAE;IACFI,QAAQ,EAAE;EACd,CAAC;EACDH,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXV;EACJ,CAAC;AACL,CAAC;AACD,OAAO,MAAMe,uBAAuB,GAAG;EACnCL,IAAI,EAAE;IACFI,QAAQ,EAAE,iBAAiB;IAC3BE,KAAK,EAAE;EACX,CAAC;EACDL,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXV;EACJ,CAAC;AACL,CAAC;AACD,OAAO,MAAMiB,kBAAkB,GAAG;EAC9BP,IAAI,EAAE;IACFQ,OAAO,EAAEtB,MAAM,CAAC,CAAC,CAACuB,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAACC,WAAW,CAAC;EAClD,CAAC;EACDT,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXV;EACJ,CAAC;AACL,CAAC;AACD,OAAO,MAAMqB,iBAAiB,GAAG;EAC7BX,IAAI,EAAE;IACFY,OAAO,EAAE1B,MAAM,CAAC,CAAC,CAACuB,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACC,WAAW,CAAC;EACjD,CAAC;EACDT,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXV;EACJ,CAAC;AACL,CAAC;AACD,OAAO,MAAMuB,QAAQ,GAAG;EACpBb,IAAI,EAAE;IACFc,QAAQ,EAAE;EACd,CAAC;EACDb,MAAM,EAAGD,IAAI,KAAM;IACfE,KAAK,EAAEF,IAAI;IACXV;EACJ,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}