{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _TranslateFakeLoader, _FakeMissingTranslationHandler, _TranslateDefaultParser, _TranslateFakeCompiler, _TranslateService, _TranslateDirective, _TranslatePipe, _TranslateModule;\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, InjectionToken, Inject, Directive, Input, Pipe, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { of, isObservable, forkJoin, concat, defer } from 'rxjs';\nimport { take, shareReplay, map, concatMap, switchMap } from 'rxjs/operators';\nclass TranslateLoader {}\n/**\n * This loader is just a placeholder that does nothing, in case you don't need a loader at all\n */\nclass TranslateFakeLoader extends TranslateLoader {\n  getTranslation(lang) {\n    void lang;\n    return of({});\n  }\n}\n_TranslateFakeLoader = TranslateFakeLoader;\n_defineProperty(TranslateFakeLoader, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_TranslateFakeLoader_BaseFactory;\n  return function _TranslateFakeLoader_Factory(__ngFactoryType__) {\n    return (ɵ_TranslateFakeLoader_BaseFactory || (ɵ_TranslateFakeLoader_BaseFactory = i0.ɵɵgetInheritedFactory(_TranslateFakeLoader)))(__ngFactoryType__ || _TranslateFakeLoader);\n  };\n})());\n_defineProperty(TranslateFakeLoader, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _TranslateFakeLoader,\n  factory: _TranslateFakeLoader.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateFakeLoader, [{\n    type: Injectable\n  }], null, null);\n})();\nclass MissingTranslationHandler {}\n/**\n * This handler is just a placeholder that does nothing, in case you don't need a missing translation handler at all\n */\nclass FakeMissingTranslationHandler {\n  handle(params) {\n    return params.key;\n  }\n}\n_FakeMissingTranslationHandler = FakeMissingTranslationHandler;\n_defineProperty(FakeMissingTranslationHandler, \"\\u0275fac\", function _FakeMissingTranslationHandler_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FakeMissingTranslationHandler)();\n});\n_defineProperty(FakeMissingTranslationHandler, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _FakeMissingTranslationHandler,\n  factory: _FakeMissingTranslationHandler.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FakeMissingTranslationHandler, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Determines if two objects or two values are equivalent.\n *\n * Two objects or values are considered equivalent if at least one of the following is true:\n *\n * * Both objects or values pass `===` comparison.\n * * Both objects or values are of the same type and all of their properties are equal by\n *   comparing them with `equals`.\n *\n * @param o1 Object or value to compare.\n * @param o2 Object or value to compare.\n * @returns true if arguments are equal.\n */\nfunction equals(o1, o2) {\n  if (o1 === o2) return true;\n  if (o1 === null || o2 === null) return false;\n  if (o1 !== o1 && o2 !== o2) return true; // NaN === NaN\n  const t1 = typeof o1,\n    t2 = typeof o2;\n  let length, key, keySet;\n  if (t1 == t2 && t1 == 'object') {\n    if (Array.isArray(o1)) {\n      if (!Array.isArray(o2)) return false;\n      if ((length = o1.length) == o2.length) {\n        for (key = 0; key < length; key++) {\n          if (!equals(o1[key], o2[key])) return false;\n        }\n        return true;\n      }\n    } else {\n      if (Array.isArray(o2)) {\n        return false;\n      }\n      keySet = Object.create(null);\n      for (key in o1) {\n        if (!equals(o1[key], o2[key])) {\n          return false;\n        }\n        keySet[key] = true;\n      }\n      for (key in o2) {\n        if (!(key in keySet) && typeof o2[key] !== 'undefined') {\n          return false;\n        }\n      }\n      return true;\n    }\n  }\n  return false;\n}\nfunction isDefined(value) {\n  return typeof value !== 'undefined' && value !== null;\n}\nfunction isDict(value) {\n  return isObject(value) && !isArray(value) && value !== null;\n}\nfunction isObject(value) {\n  return typeof value === 'object';\n}\nfunction isArray(value) {\n  return Array.isArray(value);\n}\nfunction isString(value) {\n  return typeof value === 'string';\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\nfunction mergeDeep(target, source) {\n  const output = Object.assign({}, target);\n  if (!isObject(target)) {\n    return mergeDeep({}, source);\n  }\n  if (isObject(target) && isObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (isDict(source[key])) {\n        if (key in target) {\n          output[key] = mergeDeep(target[key], source[key]);\n        } else {\n          Object.assign(output, {\n            [key]: source[key]\n          });\n        }\n      } else {\n        Object.assign(output, {\n          [key]: source[key]\n        });\n      }\n    });\n  }\n  return output;\n}\n/**\n * Gets a value from an object by composed key\n * getValue({ key1: { keyA: 'valueI' }}, 'key1.keyA') ==> 'valueI'\n * @param target\n * @param key\n */\nfunction getValue(target, key) {\n  const keys = key.split(\".\");\n  key = \"\";\n  do {\n    key += keys.shift();\n    if (isDefined(target) && isDefined(target[key]) && (isDict(target[key]) || isArray(target[key]) || !keys.length)) {\n      target = target[key];\n      key = \"\";\n    } else if (!keys.length) {\n      target = undefined;\n    } else {\n      key += \".\";\n    }\n  } while (keys.length);\n  return target;\n}\n/**\n * Gets a value from an object by composed key\n * parser.setValue({a:{b:{c: \"test\"}}}, 'a.b.c', \"test2\") ==> {a:{b:{c: \"test2\"}}}\n * @param target an object\n * @param key E.g. \"a.b.c\"\n * @param value to set\n */\nfunction setValue(target, key, value) {\n  const keys = key.split('.');\n  let current = target;\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    // If we're at the last key, set the value\n    if (i === keys.length - 1) {\n      current[key] = value;\n    } else {\n      // If the key doesn't exist or isn't an object, create an empty object\n      if (!current[key] || !isDict(current[key])) {\n        current[key] = {};\n      }\n      current = current[key];\n    }\n  }\n}\nclass TranslateParser {}\nclass TranslateDefaultParser extends TranslateParser {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"templateMatcher\", /{{\\s?([^{}\\s]*)\\s?}}/g);\n  }\n  interpolate(expr, params) {\n    if (isString(expr)) {\n      return this.interpolateString(expr, params);\n    } else if (isFunction(expr)) {\n      return this.interpolateFunction(expr, params);\n    }\n    return undefined;\n  }\n  interpolateFunction(fn, params) {\n    return fn(params);\n  }\n  interpolateString(expr, params) {\n    if (!params) {\n      return expr;\n    }\n    return expr.replace(this.templateMatcher, (substring, b) => {\n      const r = getValue(params, b);\n      return isDefined(r) ? r : substring;\n    });\n  }\n}\n_TranslateDefaultParser = TranslateDefaultParser;\n_defineProperty(TranslateDefaultParser, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_TranslateDefaultParser_BaseFactory;\n  return function _TranslateDefaultParser_Factory(__ngFactoryType__) {\n    return (ɵ_TranslateDefaultParser_BaseFactory || (ɵ_TranslateDefaultParser_BaseFactory = i0.ɵɵgetInheritedFactory(_TranslateDefaultParser)))(__ngFactoryType__ || _TranslateDefaultParser);\n  };\n})());\n_defineProperty(TranslateDefaultParser, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _TranslateDefaultParser,\n  factory: _TranslateDefaultParser.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateDefaultParser, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TranslateCompiler {}\n/**\n * This compiler is just a placeholder that does nothing, in case you don't need a compiler at all\n */\nclass TranslateFakeCompiler extends TranslateCompiler {\n  compile(value, lang) {\n    void lang;\n    return value;\n  }\n  compileTranslations(translations, lang) {\n    void lang;\n    return translations;\n  }\n}\n_TranslateFakeCompiler = TranslateFakeCompiler;\n_defineProperty(TranslateFakeCompiler, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_TranslateFakeCompiler_BaseFactory;\n  return function _TranslateFakeCompiler_Factory(__ngFactoryType__) {\n    return (ɵ_TranslateFakeCompiler_BaseFactory || (ɵ_TranslateFakeCompiler_BaseFactory = i0.ɵɵgetInheritedFactory(_TranslateFakeCompiler)))(__ngFactoryType__ || _TranslateFakeCompiler);\n  };\n})());\n_defineProperty(TranslateFakeCompiler, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _TranslateFakeCompiler,\n  factory: _TranslateFakeCompiler.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateFakeCompiler, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TranslateStore {\n  constructor() {\n    /**\n     * The default lang to fallback when translations are missing on the current lang\n     */\n    _defineProperty(this, \"defaultLang\", void 0);\n    /**\n     * The lang currently used\n     */\n    _defineProperty(this, \"currentLang\", this.defaultLang);\n    /**\n     * a list of translations per lang\n     */\n    _defineProperty(this, \"translations\", {});\n    /**\n     * an array of langs\n     */\n    _defineProperty(this, \"langs\", []);\n    /**\n     * An EventEmitter to listen to translation change events\n     * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n       *     // do something\n       * });\n     */\n    _defineProperty(this, \"onTranslationChange\", new EventEmitter());\n    /**\n     * An EventEmitter to listen to lang change events\n     * onLangChange.subscribe((params: LangChangeEvent) => {\n       *     // do something\n       * });\n     */\n    _defineProperty(this, \"onLangChange\", new EventEmitter());\n    /**\n     * An EventEmitter to listen to default lang change events\n     * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n       *     // do something\n       * });\n     */\n    _defineProperty(this, \"onDefaultLangChange\", new EventEmitter());\n  }\n}\nconst ISOLATE_TRANSLATE_SERVICE = new InjectionToken('ISOLATE_TRANSLATE_SERVICE');\nconst USE_DEFAULT_LANG = new InjectionToken('USE_DEFAULT_LANG');\nconst DEFAULT_LANGUAGE = new InjectionToken('DEFAULT_LANGUAGE');\nconst USE_EXTEND = new InjectionToken('USE_EXTEND');\nconst makeObservable = value => {\n  return isObservable(value) ? value : of(value);\n};\nclass TranslateService {\n  /**\n   * An EventEmitter to listen to translation change events\n   * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n     *     // do something\n     * });\n   */\n  get onTranslationChange() {\n    return this.store.onTranslationChange;\n  }\n  /**\n   * An EventEmitter to listen to lang change events\n   * onLangChange.subscribe((params: LangChangeEvent) => {\n     *     // do something\n     * });\n   */\n  get onLangChange() {\n    return this.store.onLangChange;\n  }\n  /**\n   * An EventEmitter to listen to default lang change events\n   * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n     *     // do something\n     * });\n   */\n  get onDefaultLangChange() {\n    return this.store.onDefaultLangChange;\n  }\n  /**\n   * The default lang to fallback when translations are missing on the current lang\n   */\n  get defaultLang() {\n    return this.store.defaultLang;\n  }\n  set defaultLang(defaultLang) {\n    this.store.defaultLang = defaultLang;\n  }\n  /**\n   * The lang currently used\n   */\n  get currentLang() {\n    return this.store.currentLang;\n  }\n  set currentLang(currentLang) {\n    this.store.currentLang = currentLang;\n  }\n  /**\n   * an array of langs\n   */\n  get langs() {\n    return this.store.langs;\n  }\n  set langs(langs) {\n    this.store.langs = langs;\n  }\n  /**\n   * a list of translations per lang\n   */\n  get translations() {\n    return this.store.translations;\n  }\n  set translations(translations) {\n    this.store.translations = translations;\n  }\n  /**\n   *\n   * @param store an instance of the store (that is supposed to be unique)\n   * @param currentLoader An instance of the loader currently used\n   * @param compiler An instance of the compiler currently used\n   * @param parser An instance of the parser currently used\n   * @param missingTranslationHandler A handler for missing translations.\n   * @param useDefaultLang whether we should use default language translation when current language translation is missing.\n   * @param isolate whether this service should use the store or not\n   * @param extend To make a child module extend (and use) translations from parent modules.\n   * @param defaultLanguage Set the default language using configuration\n   */\n  constructor(store, currentLoader, compiler, parser, missingTranslationHandler, useDefaultLang = true, isolate = false, extend = false, defaultLanguage) {\n    _defineProperty(this, \"store\", void 0);\n    _defineProperty(this, \"currentLoader\", void 0);\n    _defineProperty(this, \"compiler\", void 0);\n    _defineProperty(this, \"parser\", void 0);\n    _defineProperty(this, \"missingTranslationHandler\", void 0);\n    _defineProperty(this, \"useDefaultLang\", void 0);\n    _defineProperty(this, \"extend\", void 0);\n    _defineProperty(this, \"loadingTranslations\", void 0);\n    _defineProperty(this, \"pending\", false);\n    _defineProperty(this, \"_translationRequests\", {});\n    _defineProperty(this, \"lastUseLanguage\", null);\n    this.store = store;\n    this.currentLoader = currentLoader;\n    this.compiler = compiler;\n    this.parser = parser;\n    this.missingTranslationHandler = missingTranslationHandler;\n    this.useDefaultLang = useDefaultLang;\n    this.extend = extend;\n    if (isolate) {\n      this.store = new TranslateStore();\n    }\n    if (defaultLanguage) {\n      this.setDefaultLang(defaultLanguage);\n    }\n  }\n  /**\n   * Sets the default language to use as a fallback\n   */\n  setDefaultLang(lang) {\n    if (lang === this.defaultLang) {\n      return;\n    }\n    const pending = this.retrieveTranslations(lang);\n    if (typeof pending !== \"undefined\") {\n      // on init set the defaultLang immediately\n      if (this.defaultLang == null) {\n        this.defaultLang = lang;\n      }\n      pending.pipe(take(1)).subscribe(() => {\n        this.changeDefaultLang(lang);\n      });\n    } else {\n      // we already have this language\n      this.changeDefaultLang(lang);\n    }\n  }\n  /**\n   * Gets the default language used\n   */\n  getDefaultLang() {\n    return this.defaultLang;\n  }\n  /**\n   * Changes the lang currently used\n   */\n  use(lang) {\n    // remember the language that was called\n    // we need this with multiple fast calls to use()\n    // where translation loads might complete in random order\n    this.lastUseLanguage = lang;\n    // don't change the language if the language given is already selected\n    if (lang === this.currentLang) {\n      return of(this.translations[lang]);\n    }\n    // on init set the currentLang immediately\n    if (!this.currentLang) {\n      this.currentLang = lang;\n    }\n    const pending = this.retrieveTranslations(lang);\n    if (isObservable(pending)) {\n      pending.pipe(take(1)).subscribe(() => {\n        this.changeLang(lang);\n      });\n      return pending;\n    } else {\n      // we have this language, return an Observable\n      this.changeLang(lang);\n      return of(this.translations[lang]);\n    }\n  }\n  /**\n   * Changes the current lang\n   */\n  changeLang(lang) {\n    // received a new language file\n    // but this was not the one requested last\n    if (lang !== this.lastUseLanguage) {\n      return;\n    }\n    this.currentLang = lang;\n    this.onLangChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n    // if there is no default lang, use the one that we just set\n    if (this.defaultLang == null) {\n      this.changeDefaultLang(lang);\n    }\n  }\n  /**\n   * Retrieves the given translations\n   */\n  retrieveTranslations(lang) {\n    // if this language is unavailable or extend is true, ask for it\n    if (typeof this.translations[lang] === \"undefined\" || this.extend) {\n      this._translationRequests[lang] = this._translationRequests[lang] || this.loadAndCompileTranslations(lang);\n      return this._translationRequests[lang];\n    }\n    return undefined;\n  }\n  /**\n   * Gets an object of translations for a given language with the current loader\n   * and passes it through the compiler\n   *\n   * @deprecated This function is meant for internal use. There should\n   * be no reason to use outside this service. You can plug into this\n   * functionality by using a customer TranslateLoader or TranslateCompiler.\n   * To load a new language use setDefaultLang() and/or use()\n   */\n  getTranslation(lang) {\n    return this.loadAndCompileTranslations(lang);\n  }\n  loadAndCompileTranslations(lang) {\n    this.pending = true;\n    const loadingTranslations = this.currentLoader.getTranslation(lang).pipe(shareReplay(1), take(1));\n    this.loadingTranslations = loadingTranslations.pipe(map(res => this.compiler.compileTranslations(res, lang)), shareReplay(1), take(1));\n    this.loadingTranslations.subscribe({\n      next: res => {\n        this.translations[lang] = this.extend && this.translations[lang] ? _objectSpread(_objectSpread({}, res), this.translations[lang]) : res;\n        this.updateLangs();\n        this.pending = false;\n      },\n      error: err => {\n        void err;\n        this.pending = false;\n      }\n    });\n    return loadingTranslations;\n  }\n  /**\n   * Manually sets an object of translations for a given language\n   * after passing it through the compiler\n   */\n  setTranslation(lang, translations, shouldMerge = false) {\n    const interpolatableTranslations = this.compiler.compileTranslations(translations, lang);\n    if ((shouldMerge || this.extend) && this.translations[lang]) {\n      this.translations[lang] = mergeDeep(this.translations[lang], interpolatableTranslations);\n    } else {\n      this.translations[lang] = interpolatableTranslations;\n    }\n    this.updateLangs();\n    this.onTranslationChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Returns an array of currently available langs\n   */\n  getLangs() {\n    return this.langs;\n  }\n  /**\n   * Add available languages\n   */\n  addLangs(langs) {\n    const newLangs = langs.filter(lang => !this.langs.includes(lang));\n    if (newLangs.length > 0) {\n      this.langs = [...this.langs, ...newLangs];\n    }\n  }\n  /**\n   * Update the list of available languages\n   */\n  updateLangs() {\n    this.addLangs(Object.keys(this.translations));\n  }\n  getParsedResultForKey(translations, key, interpolateParams) {\n    let res;\n    if (translations) {\n      res = this.runInterpolation(getValue(translations, key), interpolateParams);\n    }\n    if (res === undefined && this.defaultLang != null && this.defaultLang !== this.currentLang && this.useDefaultLang) {\n      res = this.runInterpolation(getValue(this.translations[this.defaultLang], key), interpolateParams);\n    }\n    if (res === undefined) {\n      const params = {\n        key,\n        translateService: this\n      };\n      if (typeof interpolateParams !== 'undefined') {\n        params.interpolateParams = interpolateParams;\n      }\n      res = this.missingTranslationHandler.handle(params);\n    }\n    return res !== undefined ? res : key;\n  }\n  runInterpolation(translations, interpolateParams) {\n    if (isArray(translations)) {\n      return translations.map(translation => this.runInterpolation(translation, interpolateParams));\n    } else if (isDict(translations)) {\n      const result = {};\n      for (const key in translations) {\n        const res = this.runInterpolation(translations[key], interpolateParams);\n        if (res !== undefined) {\n          result[key] = res;\n        }\n      }\n      return result;\n    } else {\n      return this.parser.interpolate(translations, interpolateParams);\n    }\n  }\n  /**\n   * Returns the parsed result of the translations\n   */\n  getParsedResult(translations, key, interpolateParams) {\n    // handle a bunch of keys\n    if (key instanceof Array) {\n      const result = {};\n      let observables = false;\n      for (const k of key) {\n        result[k] = this.getParsedResultForKey(translations, k, interpolateParams);\n        observables = observables || isObservable(result[k]);\n      }\n      if (!observables) {\n        return result;\n      }\n      const sources = key.map(k => makeObservable(result[k]));\n      return forkJoin(sources).pipe(map(arr => {\n        const obj = {};\n        arr.forEach((value, index) => {\n          obj[key[index]] = value;\n        });\n        return obj;\n      }));\n    }\n    return this.getParsedResultForKey(translations, key, interpolateParams);\n  }\n  /**\n   * Gets the translated value of a key (or an array of keys)\n   * @returns the translated key, or an object of translated keys\n   */\n  get(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" is required and cannot be empty`);\n    }\n    // check if we are loading a new translation to use\n    if (this.pending) {\n      return this.loadingTranslations.pipe(concatMap(res => {\n        return makeObservable(this.getParsedResult(res, key, interpolateParams));\n      }));\n    }\n    return makeObservable(this.getParsedResult(this.translations[this.currentLang], key, interpolateParams));\n  }\n  /**\n   * Returns a stream of translated values of a key (or an array of keys) which updates\n   * whenever the translation changes.\n   * @returns A stream of the translated key, or an object of translated keys\n   */\n  getStreamOnTranslationChange(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" is required and cannot be empty`);\n    }\n    return concat(defer(() => this.get(key, interpolateParams)), this.onTranslationChange.pipe(switchMap(event => {\n      const res = this.getParsedResult(event.translations, key, interpolateParams);\n      return makeObservable(res);\n    })));\n  }\n  /**\n   * Returns a stream of translated values of a key (or an array of keys) which updates\n   * whenever the language changes.\n   * @returns A stream of the translated key, or an object of translated keys\n   */\n  stream(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    return concat(defer(() => this.get(key, interpolateParams)), this.onLangChange.pipe(switchMap(event => {\n      const res = this.getParsedResult(event.translations, key, interpolateParams);\n      return makeObservable(res);\n    })));\n  }\n  /**\n   * Returns a translation instantly from the internal state of loaded translation.\n   * All rules regarding the current language, the preferred language of even fallback languages\n   * will be used except any promise handling.\n   */\n  instant(key, interpolateParams) {\n    if (!isDefined(key) || key.length === 0) {\n      throw new Error('Parameter \"key\" is required and cannot be empty');\n    }\n    const result = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n    if (isObservable(result)) {\n      if (Array.isArray(key)) {\n        return key.reduce((acc, currKey) => {\n          acc[currKey] = currKey;\n          return acc;\n        }, {});\n      }\n      return key;\n    }\n    return result;\n  }\n  /**\n   * Sets the translated value of a key, after compiling it\n   */\n  set(key, translation, lang = this.currentLang) {\n    setValue(this.translations[lang], key, isString(translation) ? this.compiler.compile(translation, lang) : this.compiler.compileTranslations(translation, lang));\n    this.updateLangs();\n    this.onTranslationChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Changes the default lang\n   */\n  changeDefaultLang(lang) {\n    this.defaultLang = lang;\n    this.onDefaultLangChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Allows to reload the lang file from the file\n   */\n  reloadLang(lang) {\n    this.resetLang(lang);\n    return this.loadAndCompileTranslations(lang);\n  }\n  /**\n   * Deletes inner translation\n   */\n  resetLang(lang) {\n    delete this._translationRequests[lang];\n    delete this.translations[lang];\n  }\n  /**\n   * Returns the language code name from the browser, e.g. \"de\"\n   */\n  getBrowserLang() {\n    if (typeof window === 'undefined' || !window.navigator) {\n      return undefined;\n    }\n    const browserLang = this.getBrowserCultureLang();\n    return browserLang ? browserLang.split(/[-_]/)[0] : undefined;\n  }\n  /**\n   * Returns the culture language code name from the browser, e.g. \"de-DE\"\n   */\n  getBrowserCultureLang() {\n    if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n      return undefined;\n    }\n    return window.navigator.languages ? window.navigator.languages[0] : window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n  }\n}\n_TranslateService = TranslateService;\n_defineProperty(TranslateService, \"\\u0275fac\", function _TranslateService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TranslateService)(i0.ɵɵinject(TranslateStore), i0.ɵɵinject(TranslateLoader), i0.ɵɵinject(TranslateCompiler), i0.ɵɵinject(TranslateParser), i0.ɵɵinject(MissingTranslationHandler), i0.ɵɵinject(USE_DEFAULT_LANG), i0.ɵɵinject(ISOLATE_TRANSLATE_SERVICE), i0.ɵɵinject(USE_EXTEND), i0.ɵɵinject(DEFAULT_LANGUAGE));\n});\n_defineProperty(TranslateService, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _TranslateService,\n  factory: _TranslateService.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: TranslateStore\n  }, {\n    type: TranslateLoader\n  }, {\n    type: TranslateCompiler\n  }, {\n    type: TranslateParser\n  }, {\n    type: MissingTranslationHandler\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [USE_DEFAULT_LANG]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [ISOLATE_TRANSLATE_SERVICE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [USE_EXTEND]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DEFAULT_LANGUAGE]\n    }]\n  }], null);\n})();\nclass TranslateDirective {\n  set translate(key) {\n    if (key) {\n      this.key = key;\n      this.checkNodes();\n    }\n  }\n  set translateParams(params) {\n    if (!equals(this.currentParams, params)) {\n      this.currentParams = params;\n      this.checkNodes(true);\n    }\n  }\n  constructor(translateService, element, _ref) {\n    _defineProperty(this, \"translateService\", void 0);\n    _defineProperty(this, \"element\", void 0);\n    _defineProperty(this, \"_ref\", void 0);\n    _defineProperty(this, \"key\", void 0);\n    _defineProperty(this, \"lastParams\", void 0);\n    _defineProperty(this, \"currentParams\", void 0);\n    _defineProperty(this, \"onLangChangeSub\", void 0);\n    _defineProperty(this, \"onDefaultLangChangeSub\", void 0);\n    _defineProperty(this, \"onTranslationChangeSub\", void 0);\n    this.translateService = translateService;\n    this.element = element;\n    this._ref = _ref;\n    // subscribe to onTranslationChange event, in case the translations of the current lang change\n    if (!this.onTranslationChangeSub) {\n      this.onTranslationChangeSub = this.translateService.onTranslationChange.subscribe(event => {\n        if (event.lang === this.translateService.currentLang) {\n          this.checkNodes(true, event.translations);\n        }\n      });\n    }\n    // subscribe to onLangChange event, in case the language changes\n    if (!this.onLangChangeSub) {\n      this.onLangChangeSub = this.translateService.onLangChange.subscribe(event => {\n        this.checkNodes(true, event.translations);\n      });\n    }\n    // subscribe to onDefaultLangChange event, in case the default language changes\n    if (!this.onDefaultLangChangeSub) {\n      this.onDefaultLangChangeSub = this.translateService.onDefaultLangChange.subscribe(event => {\n        void event;\n        this.checkNodes(true);\n      });\n    }\n  }\n  ngAfterViewChecked() {\n    this.checkNodes();\n  }\n  checkNodes(forceUpdate = false, translations) {\n    let nodes = this.element.nativeElement.childNodes;\n    // if the element is empty\n    if (!nodes.length) {\n      // we add the key as content\n      this.setContent(this.element.nativeElement, this.key);\n      nodes = this.element.nativeElement.childNodes;\n    }\n    nodes.forEach(n => {\n      const node = n;\n      if (node.nodeType === 3) {\n        // node type 3 is a text node\n        let key;\n        if (forceUpdate) {\n          node.lastKey = null;\n        }\n        if (isDefined(node.lookupKey)) {\n          key = node.lookupKey;\n        } else if (this.key) {\n          key = this.key;\n        } else {\n          const content = this.getContent(node);\n          const trimmedContent = content.trim();\n          if (trimmedContent.length) {\n            node.lookupKey = trimmedContent;\n            // we want to use the content as a key, not the translation value\n            if (content !== node.currentValue) {\n              key = trimmedContent;\n              // the content was changed from the user, we'll use it as a reference if needed\n              node.originalContent = content || node.originalContent;\n            } else if (node.originalContent) {\n              // the content seems ok, but the lang has changed\n              // the current content is the translation, not the key, use the last real content as key\n              key = node.originalContent.trim();\n            }\n          }\n        }\n        this.updateValue(key, node, translations);\n      }\n    });\n  }\n  updateValue(key, node, translations) {\n    if (key) {\n      if (node.lastKey === key && this.lastParams === this.currentParams) {\n        return;\n      }\n      this.lastParams = this.currentParams;\n      const onTranslation = res => {\n        if (res !== key || !node.lastKey) {\n          node.lastKey = key;\n        }\n        if (!node.originalContent) {\n          node.originalContent = this.getContent(node);\n        }\n        node.currentValue = isDefined(res) ? res : node.originalContent || key;\n        // we replace in the original content to preserve spaces that we might have trimmed\n        this.setContent(node, this.key ? node.currentValue : node.originalContent.replace(key, node.currentValue));\n        this._ref.markForCheck();\n      };\n      if (isDefined(translations)) {\n        const res = this.translateService.getParsedResult(translations, key, this.currentParams);\n        if (isObservable(res)) {\n          res.subscribe({\n            next: onTranslation\n          });\n        } else {\n          onTranslation(res);\n        }\n      } else {\n        this.translateService.get(key, this.currentParams).subscribe(onTranslation);\n      }\n    }\n  }\n  getContent(node) {\n    return isDefined(node.textContent) ? node.textContent : node.data;\n  }\n  setContent(node, content) {\n    if (isDefined(node.textContent)) {\n      node.textContent = content;\n    } else {\n      node.data = content;\n    }\n  }\n  ngOnDestroy() {\n    if (this.onLangChangeSub) {\n      this.onLangChangeSub.unsubscribe();\n    }\n    if (this.onDefaultLangChangeSub) {\n      this.onDefaultLangChangeSub.unsubscribe();\n    }\n    if (this.onTranslationChangeSub) {\n      this.onTranslationChangeSub.unsubscribe();\n    }\n  }\n}\n_TranslateDirective = TranslateDirective;\n_defineProperty(TranslateDirective, \"\\u0275fac\", function _TranslateDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TranslateDirective)(i0.ɵɵdirectiveInject(TranslateService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n});\n_defineProperty(TranslateDirective, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _TranslateDirective,\n  selectors: [[\"\", \"translate\", \"\"], [\"\", \"ngx-translate\", \"\"]],\n  inputs: {\n    translate: \"translate\",\n    translateParams: \"translateParams\"\n  }\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[translate],[ngx-translate]',\n      standalone: true\n    }]\n  }], () => [{\n    type: TranslateService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    translate: [{\n      type: Input\n    }],\n    translateParams: [{\n      type: Input\n    }]\n  });\n})();\nclass TranslatePipe {\n  constructor(translate, _ref) {\n    _defineProperty(this, \"translate\", void 0);\n    _defineProperty(this, \"_ref\", void 0);\n    _defineProperty(this, \"value\", '');\n    _defineProperty(this, \"lastKey\", null);\n    _defineProperty(this, \"lastParams\", []);\n    _defineProperty(this, \"onTranslationChange\", void 0);\n    _defineProperty(this, \"onLangChange\", void 0);\n    _defineProperty(this, \"onDefaultLangChange\", void 0);\n    this.translate = translate;\n    this._ref = _ref;\n  }\n  updateValue(key, interpolateParams, translations) {\n    const onTranslation = res => {\n      this.value = res !== undefined ? res : key;\n      this.lastKey = key;\n      this._ref.markForCheck();\n    };\n    if (translations) {\n      const res = this.translate.getParsedResult(translations, key, interpolateParams);\n      if (isObservable(res)) {\n        res.subscribe(onTranslation);\n      } else {\n        onTranslation(res);\n      }\n    }\n    this.translate.get(key, interpolateParams).subscribe(onTranslation);\n  }\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  transform(query, ...args) {\n    if (!query || !query.length) {\n      return query;\n    }\n    // if we ask another time for the same key, return the last value\n    if (equals(query, this.lastKey) && equals(args, this.lastParams)) {\n      return this.value;\n    }\n    let interpolateParams = undefined;\n    if (isDefined(args[0]) && args.length) {\n      if (isString(args[0]) && args[0].length) {\n        // we accept objects written in the template such as {n:1}, {'n':1}, {n:'v'}\n        // which is why we might need to change it to real JSON objects such as {\"n\":1} or {\"n\":\"v\"}\n        const validArgs = args[0].replace(/(')?([a-zA-Z0-9_]+)(')?(\\s)?:/g, '\"$2\":').replace(/:(\\s)?(')(.*?)(')/g, ':\"$3\"');\n        try {\n          interpolateParams = JSON.parse(validArgs);\n        } catch (e) {\n          void e;\n          throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${args[0]}`);\n        }\n      } else if (isDict(args[0])) {\n        interpolateParams = args[0];\n      }\n    }\n    // store the query, in case it changes\n    this.lastKey = query;\n    // store the params, in case they change\n    this.lastParams = args;\n    // set the value\n    this.updateValue(query, interpolateParams);\n    // if there is a subscription to onLangChange, clean it\n    this._dispose();\n    // subscribe to onTranslationChange event, in case the translations change\n    if (!this.onTranslationChange) {\n      this.onTranslationChange = this.translate.onTranslationChange.subscribe(event => {\n        if (this.lastKey && event.lang === this.translate.currentLang) {\n          this.lastKey = null;\n          this.updateValue(query, interpolateParams, event.translations);\n        }\n      });\n    }\n    // subscribe to onLangChange event, in case the language changes\n    if (!this.onLangChange) {\n      this.onLangChange = this.translate.onLangChange.subscribe(event => {\n        if (this.lastKey) {\n          this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n          this.updateValue(query, interpolateParams, event.translations);\n        }\n      });\n    }\n    // subscribe to onDefaultLangChange event, in case the default language changes\n    if (!this.onDefaultLangChange) {\n      this.onDefaultLangChange = this.translate.onDefaultLangChange.subscribe(() => {\n        if (this.lastKey) {\n          this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n          this.updateValue(query, interpolateParams);\n        }\n      });\n    }\n    return this.value;\n  }\n  /**\n   * Clean any existing subscription to change events\n   */\n  _dispose() {\n    if (typeof this.onTranslationChange !== 'undefined') {\n      this.onTranslationChange.unsubscribe();\n      this.onTranslationChange = undefined;\n    }\n    if (typeof this.onLangChange !== 'undefined') {\n      this.onLangChange.unsubscribe();\n      this.onLangChange = undefined;\n    }\n    if (typeof this.onDefaultLangChange !== 'undefined') {\n      this.onDefaultLangChange.unsubscribe();\n      this.onDefaultLangChange = undefined;\n    }\n  }\n  ngOnDestroy() {\n    this._dispose();\n  }\n}\n_TranslatePipe = TranslatePipe;\n_defineProperty(TranslatePipe, \"\\u0275fac\", function _TranslatePipe_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TranslatePipe)(i0.ɵɵdirectiveInject(TranslateService, 16), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef, 16));\n});\n_defineProperty(TranslatePipe, \"\\u0275pipe\", /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"translate\",\n  type: _TranslatePipe,\n  pure: false\n}));\n_defineProperty(TranslatePipe, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _TranslatePipe,\n  factory: _TranslatePipe.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslatePipe, [{\n    type: Injectable\n  }, {\n    type: Pipe,\n    args: [{\n      name: 'translate',\n      standalone: true,\n      pure: false // required to update the value when the promise is resolved\n    }]\n  }], () => [{\n    type: TranslateService\n  }, {\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\nfunction _(key) {\n  return key;\n}\nconst provideTranslateService = (config = {}) => {\n  return makeEnvironmentProviders([config.loader || {\n    provide: TranslateLoader,\n    useClass: TranslateFakeLoader\n  }, config.compiler || {\n    provide: TranslateCompiler,\n    useClass: TranslateFakeCompiler\n  }, config.parser || {\n    provide: TranslateParser,\n    useClass: TranslateDefaultParser\n  }, config.missingTranslationHandler || {\n    provide: MissingTranslationHandler,\n    useClass: FakeMissingTranslationHandler\n  }, TranslateStore, {\n    provide: ISOLATE_TRANSLATE_SERVICE,\n    useValue: config.isolate\n  }, {\n    provide: USE_DEFAULT_LANG,\n    useValue: config.useDefaultLang\n  }, {\n    provide: USE_EXTEND,\n    useValue: config.extend\n  }, {\n    provide: DEFAULT_LANGUAGE,\n    useValue: config.defaultLanguage\n  }, TranslateService]);\n};\nclass TranslateModule {\n  /**\n   * Use this method in your root module to provide the TranslateService\n   */\n  static forRoot(config = {}) {\n    return {\n      ngModule: TranslateModule,\n      providers: [config.loader || {\n        provide: TranslateLoader,\n        useClass: TranslateFakeLoader\n      }, config.compiler || {\n        provide: TranslateCompiler,\n        useClass: TranslateFakeCompiler\n      }, config.parser || {\n        provide: TranslateParser,\n        useClass: TranslateDefaultParser\n      }, config.missingTranslationHandler || {\n        provide: MissingTranslationHandler,\n        useClass: FakeMissingTranslationHandler\n      }, TranslateStore, {\n        provide: ISOLATE_TRANSLATE_SERVICE,\n        useValue: config.isolate\n      }, {\n        provide: USE_DEFAULT_LANG,\n        useValue: config.useDefaultLang\n      }, {\n        provide: USE_EXTEND,\n        useValue: config.extend\n      }, {\n        provide: DEFAULT_LANGUAGE,\n        useValue: config.defaultLanguage\n      }, TranslateService]\n    };\n  }\n  /**\n   * Use this method in your other (non-root) modules to import the directive/pipe\n   */\n  static forChild(config = {}) {\n    return {\n      ngModule: TranslateModule,\n      providers: [config.loader || {\n        provide: TranslateLoader,\n        useClass: TranslateFakeLoader\n      }, config.compiler || {\n        provide: TranslateCompiler,\n        useClass: TranslateFakeCompiler\n      }, config.parser || {\n        provide: TranslateParser,\n        useClass: TranslateDefaultParser\n      }, config.missingTranslationHandler || {\n        provide: MissingTranslationHandler,\n        useClass: FakeMissingTranslationHandler\n      }, {\n        provide: ISOLATE_TRANSLATE_SERVICE,\n        useValue: config.isolate\n      }, {\n        provide: USE_DEFAULT_LANG,\n        useValue: config.useDefaultLang\n      }, {\n        provide: USE_EXTEND,\n        useValue: config.extend\n      }, {\n        provide: DEFAULT_LANGUAGE,\n        useValue: config.defaultLanguage\n      }, TranslateService]\n    };\n  }\n}\n_TranslateModule = TranslateModule;\n_defineProperty(TranslateModule, \"\\u0275fac\", function _TranslateModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TranslateModule)();\n});\n_defineProperty(TranslateModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _TranslateModule,\n  imports: [TranslatePipe, TranslateDirective],\n  exports: [TranslatePipe, TranslateDirective]\n}));\n_defineProperty(TranslateModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TranslatePipe, TranslateDirective],\n      exports: [TranslatePipe, TranslateDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULT_LANGUAGE, FakeMissingTranslationHandler, ISOLATE_TRANSLATE_SERVICE, MissingTranslationHandler, TranslateCompiler, TranslateDefaultParser, TranslateDirective, TranslateFakeCompiler, TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateParser, TranslatePipe, TranslateService, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, _, equals, getValue, isArray, isDefined, isDict, isFunction, isObject, isString, mergeDeep, provideTranslateService, setValue };\n//# sourceMappingURL=ngx-translate-core.mjs.map", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "InjectionToken", "Inject", "Directive", "Input", "<PERSON><PERSON>", "makeEnvironmentProviders", "NgModule", "of", "isObservable", "fork<PERSON><PERSON>n", "concat", "defer", "take", "shareReplay", "map", "concatMap", "switchMap", "Translate<PERSON><PERSON><PERSON>", "TranslateFakeLoader", "getTranslation", "lang", "_TranslateFake<PERSON>oader", "_defineProperty", "ɵ_TranslateFakeLoader_BaseFactory", "_TranslateFakeLoader_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "ngDevMode", "ɵsetClassMetadata", "type", "MissingTranslationHandler", "FakeMissingTranslationHandler", "handle", "params", "key", "_FakeMissingTranslationHandler", "_FakeMissingTranslationHandler_Factory", "equals", "o1", "o2", "t1", "t2", "length", "keySet", "Array", "isArray", "Object", "create", "isDefined", "value", "isDict", "isObject", "isString", "isFunction", "mergeDeep", "target", "source", "output", "assign", "keys", "for<PERSON>ach", "getValue", "split", "shift", "undefined", "setValue", "current", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TranslateDefault<PERSON><PERSON><PERSON>", "constructor", "args", "interpolate", "expr", "interpolateString", "interpolateFunction", "fn", "replace", "templateMatcher", "substring", "b", "r", "_TranslateDefaultParser", "ɵ_TranslateDefaultParser_BaseFactory", "_TranslateDefaultParser_Factory", "TranslateCompiler", "TranslateFakeCompiler", "compile", "compileTranslations", "translations", "_TranslateFakeCompiler", "ɵ_TranslateFakeCompiler_BaseFactory", "_TranslateFakeCompiler_Factory", "TranslateStore", "defaultLang", "ISOLATE_TRANSLATE_SERVICE", "USE_DEFAULT_LANG", "DEFAULT_LANGUAGE", "USE_EXTEND", "makeObservable", "TranslateService", "onTranslationChange", "store", "onLangChange", "onDefaultLangChange", "currentLang", "langs", "<PERSON><PERSON><PERSON><PERSON>", "compiler", "parser", "missingTranslation<PERSON><PERSON><PERSON>", "useDefaultLang", "isolate", "extend", "defaultLanguage", "setDefaultLang", "pending", "retrieveTranslations", "pipe", "subscribe", "changeDefaultLang", "getDefaultLang", "use", "lastUseLanguage", "changeLang", "emit", "_translationRequests", "loadAndCompileTranslations", "loadingTranslations", "res", "next", "_objectSpread", "updateLangs", "error", "err", "setTranslation", "shouldMerge", "interpolatableTranslations", "get<PERSON>angs", "addLangs", "newLangs", "filter", "includes", "getParsedResultForKey", "interpolateParams", "runInterpolation", "translateService", "translation", "result", "getParsedResult", "observables", "k", "sources", "arr", "obj", "index", "get", "Error", "getStreamOnTranslationChange", "event", "stream", "instant", "reduce", "acc", "curr<PERSON><PERSON>", "set", "reloadLang", "resetLang", "getBrowserLang", "window", "navigator", "browserLang", "getBrowserCultureLang", "languages", "language", "browserLanguage", "userLanguage", "_TranslateService", "_TranslateService_Factory", "ɵɵinject", "providedIn", "decorators", "TranslateDirective", "translate", "checkNodes", "translateParams", "currentParams", "element", "_ref", "onTranslationChangeSub", "onLangChangeSub", "onDefaultLangChangeSub", "ngAfterViewChecked", "forceUpdate", "nodes", "nativeElement", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "n", "node", "nodeType", "last<PERSON>ey", "lookup<PERSON><PERSON>", "content", "get<PERSON>ontent", "<PERSON><PERSON><PERSON>nt", "trim", "currentValue", "originalContent", "updateValue", "lastParams", "onTranslation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "data", "ngOnDestroy", "unsubscribe", "_TranslateDirective", "_TranslateDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "ChangeDetectorRef", "ɵɵdefineDirective", "selectors", "inputs", "selector", "standalone", "TranslatePipe", "transform", "query", "validArgs", "JSON", "parse", "e", "SyntaxError", "_dispose", "_TranslatePipe", "_TranslatePipe_Factory", "ɵɵdefinePipe", "name", "pure", "_", "provideTranslateService", "config", "loader", "provide", "useClass", "useValue", "TranslateModule", "forRoot", "ngModule", "providers", "<PERSON><PERSON><PERSON><PERSON>", "_TranslateModule", "_TranslateModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@ngx-translate/core/fesm2022/ngx-translate-core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, InjectionToken, Inject, Directive, Input, Pipe, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { of, isObservable, forkJoin, concat, defer } from 'rxjs';\nimport { take, shareReplay, map, concatMap, switchMap } from 'rxjs/operators';\n\nclass TranslateLoader {\n}\n/**\n * This loader is just a placeholder that does nothing, in case you don't need a loader at all\n */\nclass TranslateFakeLoader extends TranslateLoader {\n    getTranslation(lang) {\n        void lang;\n        return of({});\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateFakeLoader, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateFakeLoader });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateFakeLoader, decorators: [{\n            type: Injectable\n        }] });\n\nclass MissingTranslationHandler {\n}\n/**\n * This handler is just a placeholder that does nothing, in case you don't need a missing translation handler at all\n */\nclass FakeMissingTranslationHandler {\n    handle(params) {\n        return params.key;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: FakeMissingTranslationHandler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: FakeMissingTranslationHandler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: FakeMissingTranslationHandler, decorators: [{\n            type: Injectable\n        }] });\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Determines if two objects or two values are equivalent.\n *\n * Two objects or values are considered equivalent if at least one of the following is true:\n *\n * * Both objects or values pass `===` comparison.\n * * Both objects or values are of the same type and all of their properties are equal by\n *   comparing them with `equals`.\n *\n * @param o1 Object or value to compare.\n * @param o2 Object or value to compare.\n * @returns true if arguments are equal.\n */\nfunction equals(o1, o2) {\n    if (o1 === o2)\n        return true;\n    if (o1 === null || o2 === null)\n        return false;\n    if (o1 !== o1 && o2 !== o2)\n        return true; // NaN === NaN\n    const t1 = typeof o1, t2 = typeof o2;\n    let length, key, keySet;\n    if (t1 == t2 && t1 == 'object') {\n        if (Array.isArray(o1)) {\n            if (!Array.isArray(o2))\n                return false;\n            if ((length = o1.length) == o2.length) {\n                for (key = 0; key < length; key++) {\n                    if (!equals(o1[key], o2[key]))\n                        return false;\n                }\n                return true;\n            }\n        }\n        else {\n            if (Array.isArray(o2)) {\n                return false;\n            }\n            keySet = Object.create(null);\n            for (key in o1) {\n                if (!equals(o1[key], o2[key])) {\n                    return false;\n                }\n                keySet[key] = true;\n            }\n            for (key in o2) {\n                if (!(key in keySet) && typeof o2[key] !== 'undefined') {\n                    return false;\n                }\n            }\n            return true;\n        }\n    }\n    return false;\n}\nfunction isDefined(value) {\n    return typeof value !== 'undefined' && value !== null;\n}\nfunction isDict(value) {\n    return isObject(value) && !isArray(value) && value !== null;\n}\nfunction isObject(value) {\n    return typeof value === 'object';\n}\nfunction isArray(value) {\n    return Array.isArray(value);\n}\nfunction isString(value) {\n    return typeof value === 'string';\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\nfunction mergeDeep(target, source) {\n    const output = Object.assign({}, target);\n    if (!isObject(target)) {\n        return mergeDeep({}, source);\n    }\n    if (isObject(target) && isObject(source)) {\n        Object.keys(source).forEach((key) => {\n            if (isDict(source[key])) {\n                if (key in target) {\n                    output[key] = mergeDeep(target[key], source[key]);\n                }\n                else {\n                    Object.assign(output, { [key]: source[key] });\n                }\n            }\n            else {\n                Object.assign(output, { [key]: source[key] });\n            }\n        });\n    }\n    return output;\n}\n/**\n * Gets a value from an object by composed key\n * getValue({ key1: { keyA: 'valueI' }}, 'key1.keyA') ==> 'valueI'\n * @param target\n * @param key\n */\nfunction getValue(target, key) {\n    const keys = key.split(\".\");\n    key = \"\";\n    do {\n        key += keys.shift();\n        if (isDefined(target) && isDefined(target[key]) && (isDict(target[key]) || isArray(target[key]) || !keys.length)) {\n            target = target[key];\n            key = \"\";\n        }\n        else if (!keys.length) {\n            target = undefined;\n        }\n        else {\n            key += \".\";\n        }\n    } while (keys.length);\n    return target;\n}\n/**\n * Gets a value from an object by composed key\n * parser.setValue({a:{b:{c: \"test\"}}}, 'a.b.c', \"test2\") ==> {a:{b:{c: \"test2\"}}}\n * @param target an object\n * @param key E.g. \"a.b.c\"\n * @param value to set\n */\nfunction setValue(target, key, value) {\n    const keys = key.split('.');\n    let current = target;\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        // If we're at the last key, set the value\n        if (i === keys.length - 1) {\n            current[key] = value;\n        }\n        else {\n            // If the key doesn't exist or isn't an object, create an empty object\n            if (!current[key] || !isDict(current[key])) {\n                current[key] = {};\n            }\n            current = current[key];\n        }\n    }\n}\n\nclass TranslateParser {\n}\nclass TranslateDefaultParser extends TranslateParser {\n    templateMatcher = /{{\\s?([^{}\\s]*)\\s?}}/g;\n    interpolate(expr, params) {\n        if (isString(expr)) {\n            return this.interpolateString(expr, params);\n        }\n        else if (isFunction(expr)) {\n            return this.interpolateFunction(expr, params);\n        }\n        return undefined;\n    }\n    interpolateFunction(fn, params) {\n        return fn(params);\n    }\n    interpolateString(expr, params) {\n        if (!params) {\n            return expr;\n        }\n        return expr.replace(this.templateMatcher, (substring, b) => {\n            const r = getValue(params, b);\n            return isDefined(r)\n                ? r\n                : substring;\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateDefaultParser, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateDefaultParser });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateDefaultParser, decorators: [{\n            type: Injectable\n        }] });\n\nclass TranslateCompiler {\n}\n/**\n * This compiler is just a placeholder that does nothing, in case you don't need a compiler at all\n */\nclass TranslateFakeCompiler extends TranslateCompiler {\n    compile(value, lang) {\n        void lang;\n        return value;\n    }\n    compileTranslations(translations, lang) {\n        void lang;\n        return translations;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateFakeCompiler, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateFakeCompiler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateFakeCompiler, decorators: [{\n            type: Injectable\n        }] });\n\nclass TranslateStore {\n    /**\n     * The default lang to fallback when translations are missing on the current lang\n     */\n    defaultLang;\n    /**\n     * The lang currently used\n     */\n    currentLang = this.defaultLang;\n    /**\n     * a list of translations per lang\n     */\n    translations = {};\n    /**\n     * an array of langs\n     */\n    langs = [];\n    /**\n     * An EventEmitter to listen to translation change events\n     * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n       *     // do something\n       * });\n     */\n    onTranslationChange = new EventEmitter();\n    /**\n     * An EventEmitter to listen to lang change events\n     * onLangChange.subscribe((params: LangChangeEvent) => {\n       *     // do something\n       * });\n     */\n    onLangChange = new EventEmitter();\n    /**\n     * An EventEmitter to listen to default lang change events\n     * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n       *     // do something\n       * });\n     */\n    onDefaultLangChange = new EventEmitter();\n}\n\nconst ISOLATE_TRANSLATE_SERVICE = new InjectionToken('ISOLATE_TRANSLATE_SERVICE');\nconst USE_DEFAULT_LANG = new InjectionToken('USE_DEFAULT_LANG');\nconst DEFAULT_LANGUAGE = new InjectionToken('DEFAULT_LANGUAGE');\nconst USE_EXTEND = new InjectionToken('USE_EXTEND');\nconst makeObservable = (value) => {\n    return isObservable(value) ? value : of(value);\n};\nclass TranslateService {\n    store;\n    currentLoader;\n    compiler;\n    parser;\n    missingTranslationHandler;\n    useDefaultLang;\n    extend;\n    loadingTranslations;\n    pending = false;\n    _translationRequests = {};\n    lastUseLanguage = null;\n    /**\n     * An EventEmitter to listen to translation change events\n     * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n       *     // do something\n       * });\n     */\n    get onTranslationChange() {\n        return this.store.onTranslationChange;\n    }\n    /**\n     * An EventEmitter to listen to lang change events\n     * onLangChange.subscribe((params: LangChangeEvent) => {\n       *     // do something\n       * });\n     */\n    get onLangChange() {\n        return this.store.onLangChange;\n    }\n    /**\n     * An EventEmitter to listen to default lang change events\n     * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n       *     // do something\n       * });\n     */\n    get onDefaultLangChange() {\n        return this.store.onDefaultLangChange;\n    }\n    /**\n     * The default lang to fallback when translations are missing on the current lang\n     */\n    get defaultLang() {\n        return this.store.defaultLang;\n    }\n    set defaultLang(defaultLang) {\n        this.store.defaultLang = defaultLang;\n    }\n    /**\n     * The lang currently used\n     */\n    get currentLang() {\n        return this.store.currentLang;\n    }\n    set currentLang(currentLang) {\n        this.store.currentLang = currentLang;\n    }\n    /**\n     * an array of langs\n     */\n    get langs() {\n        return this.store.langs;\n    }\n    set langs(langs) {\n        this.store.langs = langs;\n    }\n    /**\n     * a list of translations per lang\n     */\n    get translations() {\n        return this.store.translations;\n    }\n    set translations(translations) {\n        this.store.translations = translations;\n    }\n    /**\n     *\n     * @param store an instance of the store (that is supposed to be unique)\n     * @param currentLoader An instance of the loader currently used\n     * @param compiler An instance of the compiler currently used\n     * @param parser An instance of the parser currently used\n     * @param missingTranslationHandler A handler for missing translations.\n     * @param useDefaultLang whether we should use default language translation when current language translation is missing.\n     * @param isolate whether this service should use the store or not\n     * @param extend To make a child module extend (and use) translations from parent modules.\n     * @param defaultLanguage Set the default language using configuration\n     */\n    constructor(store, currentLoader, compiler, parser, missingTranslationHandler, useDefaultLang = true, isolate = false, extend = false, defaultLanguage) {\n        this.store = store;\n        this.currentLoader = currentLoader;\n        this.compiler = compiler;\n        this.parser = parser;\n        this.missingTranslationHandler = missingTranslationHandler;\n        this.useDefaultLang = useDefaultLang;\n        this.extend = extend;\n        if (isolate) {\n            this.store = new TranslateStore();\n        }\n        if (defaultLanguage) {\n            this.setDefaultLang(defaultLanguage);\n        }\n    }\n    /**\n     * Sets the default language to use as a fallback\n     */\n    setDefaultLang(lang) {\n        if (lang === this.defaultLang) {\n            return;\n        }\n        const pending = this.retrieveTranslations(lang);\n        if (typeof pending !== \"undefined\") {\n            // on init set the defaultLang immediately\n            if (this.defaultLang == null) {\n                this.defaultLang = lang;\n            }\n            pending.pipe(take(1))\n                .subscribe(() => {\n                this.changeDefaultLang(lang);\n            });\n        }\n        else { // we already have this language\n            this.changeDefaultLang(lang);\n        }\n    }\n    /**\n     * Gets the default language used\n     */\n    getDefaultLang() {\n        return this.defaultLang;\n    }\n    /**\n     * Changes the lang currently used\n     */\n    use(lang) {\n        // remember the language that was called\n        // we need this with multiple fast calls to use()\n        // where translation loads might complete in random order\n        this.lastUseLanguage = lang;\n        // don't change the language if the language given is already selected\n        if (lang === this.currentLang) {\n            return of(this.translations[lang]);\n        }\n        // on init set the currentLang immediately\n        if (!this.currentLang) {\n            this.currentLang = lang;\n        }\n        const pending = this.retrieveTranslations(lang);\n        if (isObservable(pending)) {\n            pending.pipe(take(1))\n                .subscribe(() => {\n                this.changeLang(lang);\n            });\n            return pending;\n        }\n        else {\n            // we have this language, return an Observable\n            this.changeLang(lang);\n            return of(this.translations[lang]);\n        }\n    }\n    /**\n     * Changes the current lang\n     */\n    changeLang(lang) {\n        // received a new language file\n        // but this was not the one requested last\n        if (lang !== this.lastUseLanguage) {\n            return;\n        }\n        this.currentLang = lang;\n        this.onLangChange.emit({ lang: lang, translations: this.translations[lang] });\n        // if there is no default lang, use the one that we just set\n        if (this.defaultLang == null) {\n            this.changeDefaultLang(lang);\n        }\n    }\n    /**\n     * Retrieves the given translations\n     */\n    retrieveTranslations(lang) {\n        // if this language is unavailable or extend is true, ask for it\n        if (typeof this.translations[lang] === \"undefined\" || this.extend) {\n            this._translationRequests[lang] = this._translationRequests[lang] || this.loadAndCompileTranslations(lang);\n            return this._translationRequests[lang];\n        }\n        return undefined;\n    }\n    /**\n     * Gets an object of translations for a given language with the current loader\n     * and passes it through the compiler\n     *\n     * @deprecated This function is meant for internal use. There should\n     * be no reason to use outside this service. You can plug into this\n     * functionality by using a customer TranslateLoader or TranslateCompiler.\n     * To load a new language use setDefaultLang() and/or use()\n     */\n    getTranslation(lang) {\n        return this.loadAndCompileTranslations(lang);\n    }\n    loadAndCompileTranslations(lang) {\n        this.pending = true;\n        const loadingTranslations = this.currentLoader.getTranslation(lang).pipe(shareReplay(1), take(1));\n        this.loadingTranslations = loadingTranslations.pipe(map((res) => this.compiler.compileTranslations(res, lang)), shareReplay(1), take(1));\n        this.loadingTranslations\n            .subscribe({\n            next: (res) => {\n                this.translations[lang] = (this.extend && this.translations[lang]) ? { ...res, ...this.translations[lang] } : res;\n                this.updateLangs();\n                this.pending = false;\n            },\n            error: (err) => {\n                void err;\n                this.pending = false;\n            }\n        });\n        return loadingTranslations;\n    }\n    /**\n     * Manually sets an object of translations for a given language\n     * after passing it through the compiler\n     */\n    setTranslation(lang, translations, shouldMerge = false) {\n        const interpolatableTranslations = this.compiler.compileTranslations(translations, lang);\n        if ((shouldMerge || this.extend) && this.translations[lang]) {\n            this.translations[lang] = mergeDeep(this.translations[lang], interpolatableTranslations);\n        }\n        else {\n            this.translations[lang] = interpolatableTranslations;\n        }\n        this.updateLangs();\n        this.onTranslationChange.emit({ lang: lang, translations: this.translations[lang] });\n    }\n    /**\n     * Returns an array of currently available langs\n     */\n    getLangs() {\n        return this.langs;\n    }\n    /**\n     * Add available languages\n     */\n    addLangs(langs) {\n        const newLangs = langs.filter(lang => !this.langs.includes(lang));\n        if (newLangs.length > 0) {\n            this.langs = [...this.langs, ...newLangs];\n        }\n    }\n    /**\n     * Update the list of available languages\n     */\n    updateLangs() {\n        this.addLangs(Object.keys(this.translations));\n    }\n    getParsedResultForKey(translations, key, interpolateParams) {\n        let res;\n        if (translations) {\n            res = this.runInterpolation(getValue(translations, key), interpolateParams);\n        }\n        if (res === undefined && this.defaultLang != null && this.defaultLang !== this.currentLang && this.useDefaultLang) {\n            res = this.runInterpolation(getValue(this.translations[this.defaultLang], key), interpolateParams);\n        }\n        if (res === undefined) {\n            const params = { key, translateService: this };\n            if (typeof interpolateParams !== 'undefined') {\n                params.interpolateParams = interpolateParams;\n            }\n            res = this.missingTranslationHandler.handle(params);\n        }\n        return res !== undefined ? res : key;\n    }\n    runInterpolation(translations, interpolateParams) {\n        if (isArray(translations)) {\n            return translations.map((translation) => this.runInterpolation(translation, interpolateParams));\n        }\n        else if (isDict(translations)) {\n            const result = {};\n            for (const key in translations) {\n                const res = this.runInterpolation(translations[key], interpolateParams);\n                if (res !== undefined) {\n                    result[key] = res;\n                }\n            }\n            return result;\n        }\n        else {\n            return this.parser.interpolate(translations, interpolateParams);\n        }\n    }\n    /**\n     * Returns the parsed result of the translations\n     */\n    getParsedResult(translations, key, interpolateParams) {\n        // handle a bunch of keys\n        if (key instanceof Array) {\n            const result = {};\n            let observables = false;\n            for (const k of key) {\n                result[k] = this.getParsedResultForKey(translations, k, interpolateParams);\n                observables = observables || isObservable(result[k]);\n            }\n            if (!observables) {\n                return result;\n            }\n            const sources = key.map(k => makeObservable(result[k]));\n            return forkJoin(sources).pipe(map((arr) => {\n                const obj = {};\n                arr.forEach((value, index) => {\n                    obj[key[index]] = value;\n                });\n                return obj;\n            }));\n        }\n        return this.getParsedResultForKey(translations, key, interpolateParams);\n    }\n    /**\n     * Gets the translated value of a key (or an array of keys)\n     * @returns the translated key, or an object of translated keys\n     */\n    get(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" is required and cannot be empty`);\n        }\n        // check if we are loading a new translation to use\n        if (this.pending) {\n            return this.loadingTranslations.pipe(concatMap((res) => {\n                return makeObservable(this.getParsedResult(res, key, interpolateParams));\n            }));\n        }\n        return makeObservable(this.getParsedResult(this.translations[this.currentLang], key, interpolateParams));\n    }\n    /**\n     * Returns a stream of translated values of a key (or an array of keys) which updates\n     * whenever the translation changes.\n     * @returns A stream of the translated key, or an object of translated keys\n     */\n    getStreamOnTranslationChange(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" is required and cannot be empty`);\n        }\n        return concat(defer(() => this.get(key, interpolateParams)), this.onTranslationChange.pipe(switchMap((event) => {\n            const res = this.getParsedResult(event.translations, key, interpolateParams);\n            return makeObservable(res);\n        })));\n    }\n    /**\n     * Returns a stream of translated values of a key (or an array of keys) which updates\n     * whenever the language changes.\n     * @returns A stream of the translated key, or an object of translated keys\n     */\n    stream(key, interpolateParams) {\n        if (!isDefined(key) || !key.length) {\n            throw new Error(`Parameter \"key\" required`);\n        }\n        return concat(defer(() => this.get(key, interpolateParams)), this.onLangChange.pipe(switchMap((event) => {\n            const res = this.getParsedResult(event.translations, key, interpolateParams);\n            return makeObservable(res);\n        })));\n    }\n    /**\n     * Returns a translation instantly from the internal state of loaded translation.\n     * All rules regarding the current language, the preferred language of even fallback languages\n     * will be used except any promise handling.\n     */\n    instant(key, interpolateParams) {\n        if (!isDefined(key) || key.length === 0) {\n            throw new Error('Parameter \"key\" is required and cannot be empty');\n        }\n        const result = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n        if (isObservable(result)) {\n            if (Array.isArray(key)) {\n                return key.reduce((acc, currKey) => {\n                    acc[currKey] = currKey;\n                    return acc;\n                }, {});\n            }\n            return key;\n        }\n        return result;\n    }\n    /**\n     * Sets the translated value of a key, after compiling it\n     */\n    set(key, translation, lang = this.currentLang) {\n        setValue(this.translations[lang], key, isString(translation)\n            ? this.compiler.compile(translation, lang)\n            : this.compiler.compileTranslations(translation, lang));\n        this.updateLangs();\n        this.onTranslationChange.emit({ lang: lang, translations: this.translations[lang] });\n    }\n    /**\n     * Changes the default lang\n     */\n    changeDefaultLang(lang) {\n        this.defaultLang = lang;\n        this.onDefaultLangChange.emit({ lang: lang, translations: this.translations[lang] });\n    }\n    /**\n     * Allows to reload the lang file from the file\n     */\n    reloadLang(lang) {\n        this.resetLang(lang);\n        return this.loadAndCompileTranslations(lang);\n    }\n    /**\n     * Deletes inner translation\n     */\n    resetLang(lang) {\n        delete this._translationRequests[lang];\n        delete this.translations[lang];\n    }\n    /**\n     * Returns the language code name from the browser, e.g. \"de\"\n     */\n    getBrowserLang() {\n        if (typeof window === 'undefined' || !window.navigator) {\n            return undefined;\n        }\n        const browserLang = this.getBrowserCultureLang();\n        return browserLang ? browserLang.split(/[-_]/)[0] : undefined;\n    }\n    /**\n     * Returns the culture language code name from the browser, e.g. \"de-DE\"\n     */\n    getBrowserCultureLang() {\n        if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n            return undefined;\n        }\n        return window.navigator.languages\n            ? window.navigator.languages[0]\n            : (window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateService, deps: [{ token: TranslateStore }, { token: TranslateLoader }, { token: TranslateCompiler }, { token: TranslateParser }, { token: MissingTranslationHandler }, { token: USE_DEFAULT_LANG }, { token: ISOLATE_TRANSLATE_SERVICE }, { token: USE_EXTEND }, { token: DEFAULT_LANGUAGE }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateService, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: () => [{ type: TranslateStore }, { type: TranslateLoader }, { type: TranslateCompiler }, { type: TranslateParser }, { type: MissingTranslationHandler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [USE_DEFAULT_LANG]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [ISOLATE_TRANSLATE_SERVICE]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [USE_EXTEND]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DEFAULT_LANGUAGE]\n                }] }] });\n\nclass TranslateDirective {\n    translateService;\n    element;\n    _ref;\n    key;\n    lastParams;\n    currentParams;\n    onLangChangeSub;\n    onDefaultLangChangeSub;\n    onTranslationChangeSub;\n    set translate(key) {\n        if (key) {\n            this.key = key;\n            this.checkNodes();\n        }\n    }\n    set translateParams(params) {\n        if (!equals(this.currentParams, params)) {\n            this.currentParams = params;\n            this.checkNodes(true);\n        }\n    }\n    constructor(translateService, element, _ref) {\n        this.translateService = translateService;\n        this.element = element;\n        this._ref = _ref;\n        // subscribe to onTranslationChange event, in case the translations of the current lang change\n        if (!this.onTranslationChangeSub) {\n            this.onTranslationChangeSub = this.translateService.onTranslationChange.subscribe((event) => {\n                if (event.lang === this.translateService.currentLang) {\n                    this.checkNodes(true, event.translations);\n                }\n            });\n        }\n        // subscribe to onLangChange event, in case the language changes\n        if (!this.onLangChangeSub) {\n            this.onLangChangeSub = this.translateService.onLangChange.subscribe((event) => {\n                this.checkNodes(true, event.translations);\n            });\n        }\n        // subscribe to onDefaultLangChange event, in case the default language changes\n        if (!this.onDefaultLangChangeSub) {\n            this.onDefaultLangChangeSub = this.translateService.onDefaultLangChange.subscribe((event) => {\n                void event;\n                this.checkNodes(true);\n            });\n        }\n    }\n    ngAfterViewChecked() {\n        this.checkNodes();\n    }\n    checkNodes(forceUpdate = false, translations) {\n        let nodes = this.element.nativeElement.childNodes;\n        // if the element is empty\n        if (!nodes.length) {\n            // we add the key as content\n            this.setContent(this.element.nativeElement, this.key);\n            nodes = this.element.nativeElement.childNodes;\n        }\n        nodes.forEach((n) => {\n            const node = n;\n            if (node.nodeType === 3) { // node type 3 is a text node\n                let key;\n                if (forceUpdate) {\n                    node.lastKey = null;\n                }\n                if (isDefined(node.lookupKey)) {\n                    key = node.lookupKey;\n                }\n                else if (this.key) {\n                    key = this.key;\n                }\n                else {\n                    const content = this.getContent(node);\n                    const trimmedContent = content.trim();\n                    if (trimmedContent.length) {\n                        node.lookupKey = trimmedContent;\n                        // we want to use the content as a key, not the translation value\n                        if (content !== node.currentValue) {\n                            key = trimmedContent;\n                            // the content was changed from the user, we'll use it as a reference if needed\n                            node.originalContent = content || node.originalContent;\n                        }\n                        else if (node.originalContent) { // the content seems ok, but the lang has changed\n                            // the current content is the translation, not the key, use the last real content as key\n                            key = node.originalContent.trim();\n                        }\n                    }\n                }\n                this.updateValue(key, node, translations);\n            }\n        });\n    }\n    updateValue(key, node, translations) {\n        if (key) {\n            if (node.lastKey === key && this.lastParams === this.currentParams) {\n                return;\n            }\n            this.lastParams = this.currentParams;\n            const onTranslation = (res) => {\n                if (res !== key || !node.lastKey) {\n                    node.lastKey = key;\n                }\n                if (!node.originalContent) {\n                    node.originalContent = this.getContent(node);\n                }\n                node.currentValue = isDefined(res) ? res : (node.originalContent || key);\n                // we replace in the original content to preserve spaces that we might have trimmed\n                this.setContent(node, this.key ? node.currentValue : node.originalContent.replace(key, node.currentValue));\n                this._ref.markForCheck();\n            };\n            if (isDefined(translations)) {\n                const res = this.translateService.getParsedResult(translations, key, this.currentParams);\n                if (isObservable(res)) {\n                    res.subscribe({ next: onTranslation });\n                }\n                else {\n                    onTranslation(res);\n                }\n            }\n            else {\n                this.translateService.get(key, this.currentParams).subscribe(onTranslation);\n            }\n        }\n    }\n    getContent(node) {\n        return (isDefined(node.textContent) ? node.textContent : node.data);\n    }\n    setContent(node, content) {\n        if (isDefined(node.textContent)) {\n            node.textContent = content;\n        }\n        else {\n            node.data = content;\n        }\n    }\n    ngOnDestroy() {\n        if (this.onLangChangeSub) {\n            this.onLangChangeSub.unsubscribe();\n        }\n        if (this.onDefaultLangChangeSub) {\n            this.onDefaultLangChangeSub.unsubscribe();\n        }\n        if (this.onTranslationChangeSub) {\n            this.onTranslationChangeSub.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateDirective, deps: [{ token: TranslateService }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.0.5\", type: TranslateDirective, isStandalone: true, selector: \"[translate],[ngx-translate]\", inputs: { translate: \"translate\", translateParams: \"translateParams\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    // eslint-disable-next-line @angular-eslint/directive-selector\n                    selector: '[translate],[ngx-translate]',\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: TranslateService }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }], propDecorators: { translate: [{\n                type: Input\n            }], translateParams: [{\n                type: Input\n            }] } });\n\nclass TranslatePipe {\n    translate;\n    _ref;\n    value = '';\n    lastKey = null;\n    lastParams = [];\n    onTranslationChange;\n    onLangChange;\n    onDefaultLangChange;\n    constructor(translate, _ref) {\n        this.translate = translate;\n        this._ref = _ref;\n    }\n    updateValue(key, interpolateParams, translations) {\n        const onTranslation = (res) => {\n            this.value = res !== undefined ? res : key;\n            this.lastKey = key;\n            this._ref.markForCheck();\n        };\n        if (translations) {\n            const res = this.translate.getParsedResult(translations, key, interpolateParams);\n            if (isObservable(res)) {\n                res.subscribe(onTranslation);\n            }\n            else {\n                onTranslation(res);\n            }\n        }\n        this.translate.get(key, interpolateParams).subscribe(onTranslation);\n    }\n    /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n    transform(query, ...args) {\n        if (!query || !query.length) {\n            return query;\n        }\n        // if we ask another time for the same key, return the last value\n        if (equals(query, this.lastKey) && equals(args, this.lastParams)) {\n            return this.value;\n        }\n        let interpolateParams = undefined;\n        if (isDefined(args[0]) && args.length) {\n            if (isString(args[0]) && args[0].length) {\n                // we accept objects written in the template such as {n:1}, {'n':1}, {n:'v'}\n                // which is why we might need to change it to real JSON objects such as {\"n\":1} or {\"n\":\"v\"}\n                const validArgs = args[0]\n                    .replace(/(')?([a-zA-Z0-9_]+)(')?(\\s)?:/g, '\"$2\":')\n                    .replace(/:(\\s)?(')(.*?)(')/g, ':\"$3\"');\n                try {\n                    interpolateParams = JSON.parse(validArgs);\n                }\n                catch (e) {\n                    void e;\n                    throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${args[0]}`);\n                }\n            }\n            else if (isDict(args[0])) {\n                interpolateParams = args[0];\n            }\n        }\n        // store the query, in case it changes\n        this.lastKey = query;\n        // store the params, in case they change\n        this.lastParams = args;\n        // set the value\n        this.updateValue(query, interpolateParams);\n        // if there is a subscription to onLangChange, clean it\n        this._dispose();\n        // subscribe to onTranslationChange event, in case the translations change\n        if (!this.onTranslationChange) {\n            this.onTranslationChange = this.translate.onTranslationChange.subscribe((event) => {\n                if (this.lastKey && event.lang === this.translate.currentLang) {\n                    this.lastKey = null;\n                    this.updateValue(query, interpolateParams, event.translations);\n                }\n            });\n        }\n        // subscribe to onLangChange event, in case the language changes\n        if (!this.onLangChange) {\n            this.onLangChange = this.translate.onLangChange.subscribe((event) => {\n                if (this.lastKey) {\n                    this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n                    this.updateValue(query, interpolateParams, event.translations);\n                }\n            });\n        }\n        // subscribe to onDefaultLangChange event, in case the default language changes\n        if (!this.onDefaultLangChange) {\n            this.onDefaultLangChange = this.translate.onDefaultLangChange.subscribe(() => {\n                if (this.lastKey) {\n                    this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n                    this.updateValue(query, interpolateParams);\n                }\n            });\n        }\n        return this.value;\n    }\n    /**\n     * Clean any existing subscription to change events\n     */\n    _dispose() {\n        if (typeof this.onTranslationChange !== 'undefined') {\n            this.onTranslationChange.unsubscribe();\n            this.onTranslationChange = undefined;\n        }\n        if (typeof this.onLangChange !== 'undefined') {\n            this.onLangChange.unsubscribe();\n            this.onLangChange = undefined;\n        }\n        if (typeof this.onDefaultLangChange !== 'undefined') {\n            this.onDefaultLangChange.unsubscribe();\n            this.onDefaultLangChange = undefined;\n        }\n    }\n    ngOnDestroy() {\n        this._dispose();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslatePipe, deps: [{ token: TranslateService }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Pipe });\n    static ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslatePipe, isStandalone: true, name: \"translate\", pure: false });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslatePipe });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslatePipe, decorators: [{\n            type: Injectable\n        }, {\n            type: Pipe,\n            args: [{\n                    name: 'translate',\n                    standalone: true,\n                    pure: false // required to update the value when the promise is resolved\n                }]\n        }], ctorParameters: () => [{ type: TranslateService }, { type: i0.ChangeDetectorRef }] });\n\nfunction _(key) {\n    return key;\n}\n\nconst provideTranslateService = (config = {}) => {\n    return makeEnvironmentProviders([\n        config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },\n        config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },\n        config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },\n        config.missingTranslationHandler || { provide: MissingTranslationHandler, useClass: FakeMissingTranslationHandler },\n        TranslateStore,\n        { provide: ISOLATE_TRANSLATE_SERVICE, useValue: config.isolate },\n        { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },\n        { provide: USE_EXTEND, useValue: config.extend },\n        { provide: DEFAULT_LANGUAGE, useValue: config.defaultLanguage },\n        TranslateService\n    ]);\n};\nclass TranslateModule {\n    /**\n     * Use this method in your root module to provide the TranslateService\n     */\n    static forRoot(config = {}) {\n        return {\n            ngModule: TranslateModule,\n            providers: [\n                config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },\n                config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },\n                config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },\n                config.missingTranslationHandler || { provide: MissingTranslationHandler, useClass: FakeMissingTranslationHandler },\n                TranslateStore,\n                { provide: ISOLATE_TRANSLATE_SERVICE, useValue: config.isolate },\n                { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },\n                { provide: USE_EXTEND, useValue: config.extend },\n                { provide: DEFAULT_LANGUAGE, useValue: config.defaultLanguage },\n                TranslateService\n            ]\n        };\n    }\n    /**\n     * Use this method in your other (non-root) modules to import the directive/pipe\n     */\n    static forChild(config = {}) {\n        return {\n            ngModule: TranslateModule,\n            providers: [\n                config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },\n                config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },\n                config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },\n                config.missingTranslationHandler || { provide: MissingTranslationHandler, useClass: FakeMissingTranslationHandler },\n                { provide: ISOLATE_TRANSLATE_SERVICE, useValue: config.isolate },\n                { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },\n                { provide: USE_EXTEND, useValue: config.extend },\n                { provide: DEFAULT_LANGUAGE, useValue: config.defaultLanguage },\n                TranslateService\n            ]\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateModule, imports: [TranslatePipe,\n            TranslateDirective], exports: [TranslatePipe,\n            TranslateDirective] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: TranslateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        TranslatePipe,\n                        TranslateDirective\n                    ],\n                    exports: [\n                        TranslatePipe,\n                        TranslateDirective\n                    ]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULT_LANGUAGE, FakeMissingTranslationHandler, ISOLATE_TRANSLATE_SERVICE, MissingTranslationHandler, TranslateCompiler, TranslateDefaultParser, TranslateDirective, TranslateFakeCompiler, TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateParser, TranslatePipe, TranslateService, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, _, equals, getValue, isArray, isDefined, isDict, isFunction, isObject, isString, mergeDeep, provideTranslateService, setValue };\n//# sourceMappingURL=ngx-translate-core.mjs.map\n"], "mappings": ";;;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,IAAI,EAAEC,wBAAwB,EAAEC,QAAQ,QAAQ,eAAe;AAC5I,SAASC,EAAE,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAChE,SAASC,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAE7E,MAAMC,eAAe,CAAC;AAEtB;AACA;AACA;AACA,MAAMC,mBAAmB,SAASD,eAAe,CAAC;EAC9CE,cAAcA,CAACC,IAAI,EAAE;IACjB,KAAKA,IAAI;IACT,OAAOb,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;AAGJ;AAACc,oBAAA,GAPKH,mBAAmB;AAAAI,eAAA,CAAnBJ,mBAAmB;EAAA,IAAAK,iCAAA;EAAA,gBAAAC,6BAAAC,iBAAA;IAAA,QAAAF,iCAAA,KAAAA,iCAAA,GAQwD1B,EAAE,CAAA6B,qBAAA,CAHoBR,oBAAmB,IAAAO,iBAAA,IAAnBP,oBAAmB;EAAA;AAAA;AAAAI,eAAA,CALpHJ,mBAAmB,+BAQwDrB,EAAE,CAAA8B,kBAAA;EAAAC,KAAA,EAFwBV,oBAAmB;EAAAW,OAAA,EAAnBX,oBAAmB,CAAAY;AAAA;AAE9H;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFlC,EAAE,CAAAmC,iBAAA,CAAQd,mBAAmB,EAAc,CAAC;IACjHe,IAAI,EAAEnC;EACV,CAAC,CAAC;AAAA;AAEV,MAAMoC,yBAAyB,CAAC;AAEhC;AACA;AACA;AACA,MAAMC,6BAA6B,CAAC;EAChCC,MAAMA,CAACC,MAAM,EAAE;IACX,OAAOA,MAAM,CAACC,GAAG;EACrB;AAGJ;AAACC,8BAAA,GANKJ,6BAA6B;AAAAb,eAAA,CAA7Ba,6BAA6B,wBAAAK,uCAAAf,iBAAA;EAAA,YAAAA,iBAAA,IAIoEU,8BAA6B;AAAA;AAAAb,eAAA,CAJ9Ha,6BAA6B,+BAT8CtC,EAAE,CAAA8B,kBAAA;EAAAC,KAAA,EAcwBO,8BAA6B;EAAAN,OAAA,EAA7BM,8BAA6B,CAAAL;AAAA;AAExI;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhBiFlC,EAAE,CAAAmC,iBAAA,CAgBQG,6BAA6B,EAAc,CAAC;IAC3HF,IAAI,EAAEnC;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2C,MAAMA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACpB,IAAID,EAAE,KAAKC,EAAE,EACT,OAAO,IAAI;EACf,IAAID,EAAE,KAAK,IAAI,IAAIC,EAAE,KAAK,IAAI,EAC1B,OAAO,KAAK;EAChB,IAAID,EAAE,KAAKA,EAAE,IAAIC,EAAE,KAAKA,EAAE,EACtB,OAAO,IAAI,CAAC,CAAC;EACjB,MAAMC,EAAE,GAAG,OAAOF,EAAE;IAAEG,EAAE,GAAG,OAAOF,EAAE;EACpC,IAAIG,MAAM,EAAER,GAAG,EAAES,MAAM;EACvB,IAAIH,EAAE,IAAIC,EAAE,IAAID,EAAE,IAAI,QAAQ,EAAE;IAC5B,IAAII,KAAK,CAACC,OAAO,CAACP,EAAE,CAAC,EAAE;MACnB,IAAI,CAACM,KAAK,CAACC,OAAO,CAACN,EAAE,CAAC,EAClB,OAAO,KAAK;MAChB,IAAI,CAACG,MAAM,GAAGJ,EAAE,CAACI,MAAM,KAAKH,EAAE,CAACG,MAAM,EAAE;QACnC,KAAKR,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGQ,MAAM,EAAER,GAAG,EAAE,EAAE;UAC/B,IAAI,CAACG,MAAM,CAACC,EAAE,CAACJ,GAAG,CAAC,EAAEK,EAAE,CAACL,GAAG,CAAC,CAAC,EACzB,OAAO,KAAK;QACpB;QACA,OAAO,IAAI;MACf;IACJ,CAAC,MACI;MACD,IAAIU,KAAK,CAACC,OAAO,CAACN,EAAE,CAAC,EAAE;QACnB,OAAO,KAAK;MAChB;MACAI,MAAM,GAAGG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC5B,KAAKb,GAAG,IAAII,EAAE,EAAE;QACZ,IAAI,CAACD,MAAM,CAACC,EAAE,CAACJ,GAAG,CAAC,EAAEK,EAAE,CAACL,GAAG,CAAC,CAAC,EAAE;UAC3B,OAAO,KAAK;QAChB;QACAS,MAAM,CAACT,GAAG,CAAC,GAAG,IAAI;MACtB;MACA,KAAKA,GAAG,IAAIK,EAAE,EAAE;QACZ,IAAI,EAAEL,GAAG,IAAIS,MAAM,CAAC,IAAI,OAAOJ,EAAE,CAACL,GAAG,CAAC,KAAK,WAAW,EAAE;UACpD,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAASc,SAASA,CAACC,KAAK,EAAE;EACtB,OAAO,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI;AACzD;AACA,SAASC,MAAMA,CAACD,KAAK,EAAE;EACnB,OAAOE,QAAQ,CAACF,KAAK,CAAC,IAAI,CAACJ,OAAO,CAACI,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI;AAC/D;AACA,SAASE,QAAQA,CAACF,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC;AACA,SAASJ,OAAOA,CAACI,KAAK,EAAE;EACpB,OAAOL,KAAK,CAACC,OAAO,CAACI,KAAK,CAAC;AAC/B;AACA,SAASG,QAAQA,CAACH,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC;AACA,SAASI,UAAUA,CAACJ,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,KAAK,UAAU;AACtC;AACA,SAASK,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC/B,MAAMC,MAAM,GAAGX,MAAM,CAACY,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;EACxC,IAAI,CAACJ,QAAQ,CAACI,MAAM,CAAC,EAAE;IACnB,OAAOD,SAAS,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC;EAChC;EACA,IAAIL,QAAQ,CAACI,MAAM,CAAC,IAAIJ,QAAQ,CAACK,MAAM,CAAC,EAAE;IACtCV,MAAM,CAACa,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAAE1B,GAAG,IAAK;MACjC,IAAIgB,MAAM,CAACM,MAAM,CAACtB,GAAG,CAAC,CAAC,EAAE;QACrB,IAAIA,GAAG,IAAIqB,MAAM,EAAE;UACfE,MAAM,CAACvB,GAAG,CAAC,GAAGoB,SAAS,CAACC,MAAM,CAACrB,GAAG,CAAC,EAAEsB,MAAM,CAACtB,GAAG,CAAC,CAAC;QACrD,CAAC,MACI;UACDY,MAAM,CAACY,MAAM,CAACD,MAAM,EAAE;YAAE,CAACvB,GAAG,GAAGsB,MAAM,CAACtB,GAAG;UAAE,CAAC,CAAC;QACjD;MACJ,CAAC,MACI;QACDY,MAAM,CAACY,MAAM,CAACD,MAAM,EAAE;UAAE,CAACvB,GAAG,GAAGsB,MAAM,CAACtB,GAAG;QAAE,CAAC,CAAC;MACjD;IACJ,CAAC,CAAC;EACN;EACA,OAAOuB,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,QAAQA,CAACN,MAAM,EAAErB,GAAG,EAAE;EAC3B,MAAMyB,IAAI,GAAGzB,GAAG,CAAC4B,KAAK,CAAC,GAAG,CAAC;EAC3B5B,GAAG,GAAG,EAAE;EACR,GAAG;IACCA,GAAG,IAAIyB,IAAI,CAACI,KAAK,CAAC,CAAC;IACnB,IAAIf,SAAS,CAACO,MAAM,CAAC,IAAIP,SAAS,CAACO,MAAM,CAACrB,GAAG,CAAC,CAAC,KAAKgB,MAAM,CAACK,MAAM,CAACrB,GAAG,CAAC,CAAC,IAAIW,OAAO,CAACU,MAAM,CAACrB,GAAG,CAAC,CAAC,IAAI,CAACyB,IAAI,CAACjB,MAAM,CAAC,EAAE;MAC9Ga,MAAM,GAAGA,MAAM,CAACrB,GAAG,CAAC;MACpBA,GAAG,GAAG,EAAE;IACZ,CAAC,MACI,IAAI,CAACyB,IAAI,CAACjB,MAAM,EAAE;MACnBa,MAAM,GAAGS,SAAS;IACtB,CAAC,MACI;MACD9B,GAAG,IAAI,GAAG;IACd;EACJ,CAAC,QAAQyB,IAAI,CAACjB,MAAM;EACpB,OAAOa,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,QAAQA,CAACV,MAAM,EAAErB,GAAG,EAAEe,KAAK,EAAE;EAClC,MAAMU,IAAI,GAAGzB,GAAG,CAAC4B,KAAK,CAAC,GAAG,CAAC;EAC3B,IAAII,OAAO,GAAGX,MAAM;EACpB,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACjB,MAAM,EAAEyB,CAAC,EAAE,EAAE;IAClC,MAAMjC,GAAG,GAAGyB,IAAI,CAACQ,CAAC,CAAC;IACnB;IACA,IAAIA,CAAC,KAAKR,IAAI,CAACjB,MAAM,GAAG,CAAC,EAAE;MACvBwB,OAAO,CAAChC,GAAG,CAAC,GAAGe,KAAK;IACxB,CAAC,MACI;MACD;MACA,IAAI,CAACiB,OAAO,CAAChC,GAAG,CAAC,IAAI,CAACgB,MAAM,CAACgB,OAAO,CAAChC,GAAG,CAAC,CAAC,EAAE;QACxCgC,OAAO,CAAChC,GAAG,CAAC,GAAG,CAAC,CAAC;MACrB;MACAgC,OAAO,GAAGA,OAAO,CAAChC,GAAG,CAAC;IAC1B;EACJ;AACJ;AAEA,MAAMkC,eAAe,CAAC;AAEtB,MAAMC,sBAAsB,SAASD,eAAe,CAAC;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAArD,eAAA,0BAC/B,uBAAuB;EAAA;EACzCsD,WAAWA,CAACC,IAAI,EAAExC,MAAM,EAAE;IACtB,IAAImB,QAAQ,CAACqB,IAAI,CAAC,EAAE;MAChB,OAAO,IAAI,CAACC,iBAAiB,CAACD,IAAI,EAAExC,MAAM,CAAC;IAC/C,CAAC,MACI,IAAIoB,UAAU,CAACoB,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI,CAACE,mBAAmB,CAACF,IAAI,EAAExC,MAAM,CAAC;IACjD;IACA,OAAO+B,SAAS;EACpB;EACAW,mBAAmBA,CAACC,EAAE,EAAE3C,MAAM,EAAE;IAC5B,OAAO2C,EAAE,CAAC3C,MAAM,CAAC;EACrB;EACAyC,iBAAiBA,CAACD,IAAI,EAAExC,MAAM,EAAE;IAC5B,IAAI,CAACA,MAAM,EAAE;MACT,OAAOwC,IAAI;IACf;IACA,OAAOA,IAAI,CAACI,OAAO,CAAC,IAAI,CAACC,eAAe,EAAE,CAACC,SAAS,EAAEC,CAAC,KAAK;MACxD,MAAMC,CAAC,GAAGpB,QAAQ,CAAC5B,MAAM,EAAE+C,CAAC,CAAC;MAC7B,OAAOhC,SAAS,CAACiC,CAAC,CAAC,GACbA,CAAC,GACDF,SAAS;IACnB,CAAC,CAAC;EACN;AAGJ;AAACG,uBAAA,GA3BKb,sBAAsB;AAAAnD,eAAA,CAAtBmD,sBAAsB;EAAA,IAAAc,oCAAA;EAAA,gBAAAC,gCAAA/D,iBAAA;IAAA,QAAA8D,oCAAA,KAAAA,oCAAA,GAxKqD1F,EAAE,CAAA6B,qBAAA,CAiMoB+C,uBAAsB,IAAAhD,iBAAA,IAAtBgD,uBAAsB;EAAA;AAAA;AAAAnD,eAAA,CAzBvHmD,sBAAsB,+BAxKqD5E,EAAE,CAAA8B,kBAAA;EAAAC,KAAA,EAkMwB6C,uBAAsB;EAAA5C,OAAA,EAAtB4C,uBAAsB,CAAA3C;AAAA;AAEjI;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApMiFlC,EAAE,CAAAmC,iBAAA,CAoMQyC,sBAAsB,EAAc,CAAC;IACpHxC,IAAI,EAAEnC;EACV,CAAC,CAAC;AAAA;AAEV,MAAM2F,iBAAiB,CAAC;AAExB;AACA;AACA;AACA,MAAMC,qBAAqB,SAASD,iBAAiB,CAAC;EAClDE,OAAOA,CAACtC,KAAK,EAAEjC,IAAI,EAAE;IACjB,KAAKA,IAAI;IACT,OAAOiC,KAAK;EAChB;EACAuC,mBAAmBA,CAACC,YAAY,EAAEzE,IAAI,EAAE;IACpC,KAAKA,IAAI;IACT,OAAOyE,YAAY;EACvB;AAGJ;AAACC,sBAAA,GAXKJ,qBAAqB;AAAApE,eAAA,CAArBoE,qBAAqB;EAAA,IAAAK,mCAAA;EAAA,gBAAAC,+BAAAvE,iBAAA;IAAA,QAAAsE,mCAAA,KAAAA,mCAAA,GA7MsDlG,EAAE,CAAA6B,qBAAA,CAsNoBgE,sBAAqB,IAAAjE,iBAAA,IAArBiE,sBAAqB;EAAA;AAAA;AAAApE,eAAA,CATtHoE,qBAAqB,+BA7MsD7F,EAAE,CAAA8B,kBAAA;EAAAC,KAAA,EAuNwB8D,sBAAqB;EAAA7D,OAAA,EAArB6D,sBAAqB,CAAA5D;AAAA;AAEhI;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzNiFlC,EAAE,CAAAmC,iBAAA,CAyNQ0D,qBAAqB,EAAc,CAAC;IACnHzD,IAAI,EAAEnC;EACV,CAAC,CAAC;AAAA;AAEV,MAAMmG,cAAc,CAAC;EAAAvB,YAAA;IACjB;AACJ;AACA;IAFIpD,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA,sBAGc,IAAI,CAAC4E,WAAW;IAC9B;AACJ;AACA;IAFI5E,eAAA,uBAGe,CAAC,CAAC;IACjB;AACJ;AACA;IAFIA,eAAA,gBAGQ,EAAE;IACV;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,8BAMsB,IAAIvB,YAAY,CAAC,CAAC;IACxC;AACJ;AACA;AACA;AACA;AACA;IALIuB,eAAA,uBAMe,IAAIvB,YAAY,CAAC,CAAC;IACjC;AACJ;AACA;AACA;AACA;AACA;IALIuB,eAAA,8BAMsB,IAAIvB,YAAY,CAAC,CAAC;EAAA;AAC5C;AAEA,MAAMoG,yBAAyB,GAAG,IAAInG,cAAc,CAAC,2BAA2B,CAAC;AACjF,MAAMoG,gBAAgB,GAAG,IAAIpG,cAAc,CAAC,kBAAkB,CAAC;AAC/D,MAAMqG,gBAAgB,GAAG,IAAIrG,cAAc,CAAC,kBAAkB,CAAC;AAC/D,MAAMsG,UAAU,GAAG,IAAItG,cAAc,CAAC,YAAY,CAAC;AACnD,MAAMuG,cAAc,GAAIlD,KAAK,IAAK;EAC9B,OAAO7C,YAAY,CAAC6C,KAAK,CAAC,GAAGA,KAAK,GAAG9C,EAAE,CAAC8C,KAAK,CAAC;AAClD,CAAC;AACD,MAAMmD,gBAAgB,CAAC;EAYnB;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACC,KAAK,CAACD,mBAAmB;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIE,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACD,KAAK,CAACC,YAAY;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACF,KAAK,CAACE,mBAAmB;EACzC;EACA;AACJ;AACA;EACI,IAAIV,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACQ,KAAK,CAACR,WAAW;EACjC;EACA,IAAIA,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAACQ,KAAK,CAACR,WAAW,GAAGA,WAAW;EACxC;EACA;AACJ;AACA;EACI,IAAIW,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACH,KAAK,CAACG,WAAW;EACjC;EACA,IAAIA,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAACH,KAAK,CAACG,WAAW,GAAGA,WAAW;EACxC;EACA;AACJ;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACJ,KAAK,CAACI,KAAK;EAC3B;EACA,IAAIA,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACJ,KAAK,CAACI,KAAK,GAAGA,KAAK;EAC5B;EACA;AACJ;AACA;EACI,IAAIjB,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACa,KAAK,CAACb,YAAY;EAClC;EACA,IAAIA,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,CAACa,KAAK,CAACb,YAAY,GAAGA,YAAY;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACInB,WAAWA,CAACgC,KAAK,EAAEK,aAAa,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,yBAAyB,EAAEC,cAAc,GAAG,IAAI,EAAEC,OAAO,GAAG,KAAK,EAAEC,MAAM,GAAG,KAAK,EAAEC,eAAe,EAAE;IAAAhG,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,kBA9E9I,KAAK;IAAAA,eAAA,+BACQ,CAAC,CAAC;IAAAA,eAAA,0BACP,IAAI;IA6ElB,IAAI,CAACoF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACK,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACE,MAAM,GAAGA,MAAM;IACpB,IAAID,OAAO,EAAE;MACT,IAAI,CAACV,KAAK,GAAG,IAAIT,cAAc,CAAC,CAAC;IACrC;IACA,IAAIqB,eAAe,EAAE;MACjB,IAAI,CAACC,cAAc,CAACD,eAAe,CAAC;IACxC;EACJ;EACA;AACJ;AACA;EACIC,cAAcA,CAACnG,IAAI,EAAE;IACjB,IAAIA,IAAI,KAAK,IAAI,CAAC8E,WAAW,EAAE;MAC3B;IACJ;IACA,MAAMsB,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAACrG,IAAI,CAAC;IAC/C,IAAI,OAAOoG,OAAO,KAAK,WAAW,EAAE;MAChC;MACA,IAAI,IAAI,CAACtB,WAAW,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACA,WAAW,GAAG9E,IAAI;MAC3B;MACAoG,OAAO,CAACE,IAAI,CAAC9G,IAAI,CAAC,CAAC,CAAC,CAAC,CAChB+G,SAAS,CAAC,MAAM;QACjB,IAAI,CAACC,iBAAiB,CAACxG,IAAI,CAAC;MAChC,CAAC,CAAC;IACN,CAAC,MACI;MAAE;MACH,IAAI,CAACwG,iBAAiB,CAACxG,IAAI,CAAC;IAChC;EACJ;EACA;AACJ;AACA;EACIyG,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC3B,WAAW;EAC3B;EACA;AACJ;AACA;EACI4B,GAAGA,CAAC1G,IAAI,EAAE;IACN;IACA;IACA;IACA,IAAI,CAAC2G,eAAe,GAAG3G,IAAI;IAC3B;IACA,IAAIA,IAAI,KAAK,IAAI,CAACyF,WAAW,EAAE;MAC3B,OAAOtG,EAAE,CAAC,IAAI,CAACsF,YAAY,CAACzE,IAAI,CAAC,CAAC;IACtC;IACA;IACA,IAAI,CAAC,IAAI,CAACyF,WAAW,EAAE;MACnB,IAAI,CAACA,WAAW,GAAGzF,IAAI;IAC3B;IACA,MAAMoG,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAACrG,IAAI,CAAC;IAC/C,IAAIZ,YAAY,CAACgH,OAAO,CAAC,EAAE;MACvBA,OAAO,CAACE,IAAI,CAAC9G,IAAI,CAAC,CAAC,CAAC,CAAC,CAChB+G,SAAS,CAAC,MAAM;QACjB,IAAI,CAACK,UAAU,CAAC5G,IAAI,CAAC;MACzB,CAAC,CAAC;MACF,OAAOoG,OAAO;IAClB,CAAC,MACI;MACD;MACA,IAAI,CAACQ,UAAU,CAAC5G,IAAI,CAAC;MACrB,OAAOb,EAAE,CAAC,IAAI,CAACsF,YAAY,CAACzE,IAAI,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;EACI4G,UAAUA,CAAC5G,IAAI,EAAE;IACb;IACA;IACA,IAAIA,IAAI,KAAK,IAAI,CAAC2G,eAAe,EAAE;MAC/B;IACJ;IACA,IAAI,CAAClB,WAAW,GAAGzF,IAAI;IACvB,IAAI,CAACuF,YAAY,CAACsB,IAAI,CAAC;MAAE7G,IAAI,EAAEA,IAAI;MAAEyE,YAAY,EAAE,IAAI,CAACA,YAAY,CAACzE,IAAI;IAAE,CAAC,CAAC;IAC7E;IACA,IAAI,IAAI,CAAC8E,WAAW,IAAI,IAAI,EAAE;MAC1B,IAAI,CAAC0B,iBAAiB,CAACxG,IAAI,CAAC;IAChC;EACJ;EACA;AACJ;AACA;EACIqG,oBAAoBA,CAACrG,IAAI,EAAE;IACvB;IACA,IAAI,OAAO,IAAI,CAACyE,YAAY,CAACzE,IAAI,CAAC,KAAK,WAAW,IAAI,IAAI,CAACiG,MAAM,EAAE;MAC/D,IAAI,CAACa,oBAAoB,CAAC9G,IAAI,CAAC,GAAG,IAAI,CAAC8G,oBAAoB,CAAC9G,IAAI,CAAC,IAAI,IAAI,CAAC+G,0BAA0B,CAAC/G,IAAI,CAAC;MAC1G,OAAO,IAAI,CAAC8G,oBAAoB,CAAC9G,IAAI,CAAC;IAC1C;IACA,OAAOgD,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIjD,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAO,IAAI,CAAC+G,0BAA0B,CAAC/G,IAAI,CAAC;EAChD;EACA+G,0BAA0BA,CAAC/G,IAAI,EAAE;IAC7B,IAAI,CAACoG,OAAO,GAAG,IAAI;IACnB,MAAMY,mBAAmB,GAAG,IAAI,CAACrB,aAAa,CAAC5F,cAAc,CAACC,IAAI,CAAC,CAACsG,IAAI,CAAC7G,WAAW,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;IACjG,IAAI,CAACwH,mBAAmB,GAAGA,mBAAmB,CAACV,IAAI,CAAC5G,GAAG,CAAEuH,GAAG,IAAK,IAAI,CAACrB,QAAQ,CAACpB,mBAAmB,CAACyC,GAAG,EAAEjH,IAAI,CAAC,CAAC,EAAEP,WAAW,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;IACxI,IAAI,CAACwH,mBAAmB,CACnBT,SAAS,CAAC;MACXW,IAAI,EAAGD,GAAG,IAAK;QACX,IAAI,CAACxC,YAAY,CAACzE,IAAI,CAAC,GAAI,IAAI,CAACiG,MAAM,IAAI,IAAI,CAACxB,YAAY,CAACzE,IAAI,CAAC,GAAAmH,aAAA,CAAAA,aAAA,KAASF,GAAG,GAAK,IAAI,CAACxC,YAAY,CAACzE,IAAI,CAAC,IAAKiH,GAAG;QACjH,IAAI,CAACG,WAAW,CAAC,CAAC;QAClB,IAAI,CAAChB,OAAO,GAAG,KAAK;MACxB,CAAC;MACDiB,KAAK,EAAGC,GAAG,IAAK;QACZ,KAAKA,GAAG;QACR,IAAI,CAAClB,OAAO,GAAG,KAAK;MACxB;IACJ,CAAC,CAAC;IACF,OAAOY,mBAAmB;EAC9B;EACA;AACJ;AACA;AACA;EACIO,cAAcA,CAACvH,IAAI,EAAEyE,YAAY,EAAE+C,WAAW,GAAG,KAAK,EAAE;IACpD,MAAMC,0BAA0B,GAAG,IAAI,CAAC7B,QAAQ,CAACpB,mBAAmB,CAACC,YAAY,EAAEzE,IAAI,CAAC;IACxF,IAAI,CAACwH,WAAW,IAAI,IAAI,CAACvB,MAAM,KAAK,IAAI,CAACxB,YAAY,CAACzE,IAAI,CAAC,EAAE;MACzD,IAAI,CAACyE,YAAY,CAACzE,IAAI,CAAC,GAAGsC,SAAS,CAAC,IAAI,CAACmC,YAAY,CAACzE,IAAI,CAAC,EAAEyH,0BAA0B,CAAC;IAC5F,CAAC,MACI;MACD,IAAI,CAAChD,YAAY,CAACzE,IAAI,CAAC,GAAGyH,0BAA0B;IACxD;IACA,IAAI,CAACL,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC/B,mBAAmB,CAACwB,IAAI,CAAC;MAAE7G,IAAI,EAAEA,IAAI;MAAEyE,YAAY,EAAE,IAAI,CAACA,YAAY,CAACzE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;EACI0H,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAChC,KAAK;EACrB;EACA;AACJ;AACA;EACIiC,QAAQA,CAACjC,KAAK,EAAE;IACZ,MAAMkC,QAAQ,GAAGlC,KAAK,CAACmC,MAAM,CAAC7H,IAAI,IAAI,CAAC,IAAI,CAAC0F,KAAK,CAACoC,QAAQ,CAAC9H,IAAI,CAAC,CAAC;IACjE,IAAI4H,QAAQ,CAAClG,MAAM,GAAG,CAAC,EAAE;MACrB,IAAI,CAACgE,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,EAAE,GAAGkC,QAAQ,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;EACIR,WAAWA,CAAA,EAAG;IACV,IAAI,CAACO,QAAQ,CAAC7F,MAAM,CAACa,IAAI,CAAC,IAAI,CAAC8B,YAAY,CAAC,CAAC;EACjD;EACAsD,qBAAqBA,CAACtD,YAAY,EAAEvD,GAAG,EAAE8G,iBAAiB,EAAE;IACxD,IAAIf,GAAG;IACP,IAAIxC,YAAY,EAAE;MACdwC,GAAG,GAAG,IAAI,CAACgB,gBAAgB,CAACpF,QAAQ,CAAC4B,YAAY,EAAEvD,GAAG,CAAC,EAAE8G,iBAAiB,CAAC;IAC/E;IACA,IAAIf,GAAG,KAAKjE,SAAS,IAAI,IAAI,CAAC8B,WAAW,IAAI,IAAI,IAAI,IAAI,CAACA,WAAW,KAAK,IAAI,CAACW,WAAW,IAAI,IAAI,CAACM,cAAc,EAAE;MAC/GkB,GAAG,GAAG,IAAI,CAACgB,gBAAgB,CAACpF,QAAQ,CAAC,IAAI,CAAC4B,YAAY,CAAC,IAAI,CAACK,WAAW,CAAC,EAAE5D,GAAG,CAAC,EAAE8G,iBAAiB,CAAC;IACtG;IACA,IAAIf,GAAG,KAAKjE,SAAS,EAAE;MACnB,MAAM/B,MAAM,GAAG;QAAEC,GAAG;QAAEgH,gBAAgB,EAAE;MAAK,CAAC;MAC9C,IAAI,OAAOF,iBAAiB,KAAK,WAAW,EAAE;QAC1C/G,MAAM,CAAC+G,iBAAiB,GAAGA,iBAAiB;MAChD;MACAf,GAAG,GAAG,IAAI,CAACnB,yBAAyB,CAAC9E,MAAM,CAACC,MAAM,CAAC;IACvD;IACA,OAAOgG,GAAG,KAAKjE,SAAS,GAAGiE,GAAG,GAAG/F,GAAG;EACxC;EACA+G,gBAAgBA,CAACxD,YAAY,EAAEuD,iBAAiB,EAAE;IAC9C,IAAInG,OAAO,CAAC4C,YAAY,CAAC,EAAE;MACvB,OAAOA,YAAY,CAAC/E,GAAG,CAAEyI,WAAW,IAAK,IAAI,CAACF,gBAAgB,CAACE,WAAW,EAAEH,iBAAiB,CAAC,CAAC;IACnG,CAAC,MACI,IAAI9F,MAAM,CAACuC,YAAY,CAAC,EAAE;MAC3B,MAAM2D,MAAM,GAAG,CAAC,CAAC;MACjB,KAAK,MAAMlH,GAAG,IAAIuD,YAAY,EAAE;QAC5B,MAAMwC,GAAG,GAAG,IAAI,CAACgB,gBAAgB,CAACxD,YAAY,CAACvD,GAAG,CAAC,EAAE8G,iBAAiB,CAAC;QACvE,IAAIf,GAAG,KAAKjE,SAAS,EAAE;UACnBoF,MAAM,CAAClH,GAAG,CAAC,GAAG+F,GAAG;QACrB;MACJ;MACA,OAAOmB,MAAM;IACjB,CAAC,MACI;MACD,OAAO,IAAI,CAACvC,MAAM,CAACrC,WAAW,CAACiB,YAAY,EAAEuD,iBAAiB,CAAC;IACnE;EACJ;EACA;AACJ;AACA;EACIK,eAAeA,CAAC5D,YAAY,EAAEvD,GAAG,EAAE8G,iBAAiB,EAAE;IAClD;IACA,IAAI9G,GAAG,YAAYU,KAAK,EAAE;MACtB,MAAMwG,MAAM,GAAG,CAAC,CAAC;MACjB,IAAIE,WAAW,GAAG,KAAK;MACvB,KAAK,MAAMC,CAAC,IAAIrH,GAAG,EAAE;QACjBkH,MAAM,CAACG,CAAC,CAAC,GAAG,IAAI,CAACR,qBAAqB,CAACtD,YAAY,EAAE8D,CAAC,EAAEP,iBAAiB,CAAC;QAC1EM,WAAW,GAAGA,WAAW,IAAIlJ,YAAY,CAACgJ,MAAM,CAACG,CAAC,CAAC,CAAC;MACxD;MACA,IAAI,CAACD,WAAW,EAAE;QACd,OAAOF,MAAM;MACjB;MACA,MAAMI,OAAO,GAAGtH,GAAG,CAACxB,GAAG,CAAC6I,CAAC,IAAIpD,cAAc,CAACiD,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC;MACvD,OAAOlJ,QAAQ,CAACmJ,OAAO,CAAC,CAAClC,IAAI,CAAC5G,GAAG,CAAE+I,GAAG,IAAK;QACvC,MAAMC,GAAG,GAAG,CAAC,CAAC;QACdD,GAAG,CAAC7F,OAAO,CAAC,CAACX,KAAK,EAAE0G,KAAK,KAAK;UAC1BD,GAAG,CAACxH,GAAG,CAACyH,KAAK,CAAC,CAAC,GAAG1G,KAAK;QAC3B,CAAC,CAAC;QACF,OAAOyG,GAAG;MACd,CAAC,CAAC,CAAC;IACP;IACA,OAAO,IAAI,CAACX,qBAAqB,CAACtD,YAAY,EAAEvD,GAAG,EAAE8G,iBAAiB,CAAC;EAC3E;EACA;AACJ;AACA;AACA;EACIY,GAAGA,CAAC1H,GAAG,EAAE8G,iBAAiB,EAAE;IACxB,IAAI,CAAChG,SAAS,CAACd,GAAG,CAAC,IAAI,CAACA,GAAG,CAACQ,MAAM,EAAE;MAChC,MAAM,IAAImH,KAAK,CAAC,iDAAiD,CAAC;IACtE;IACA;IACA,IAAI,IAAI,CAACzC,OAAO,EAAE;MACd,OAAO,IAAI,CAACY,mBAAmB,CAACV,IAAI,CAAC3G,SAAS,CAAEsH,GAAG,IAAK;QACpD,OAAO9B,cAAc,CAAC,IAAI,CAACkD,eAAe,CAACpB,GAAG,EAAE/F,GAAG,EAAE8G,iBAAiB,CAAC,CAAC;MAC5E,CAAC,CAAC,CAAC;IACP;IACA,OAAO7C,cAAc,CAAC,IAAI,CAACkD,eAAe,CAAC,IAAI,CAAC5D,YAAY,CAAC,IAAI,CAACgB,WAAW,CAAC,EAAEvE,GAAG,EAAE8G,iBAAiB,CAAC,CAAC;EAC5G;EACA;AACJ;AACA;AACA;AACA;EACIc,4BAA4BA,CAAC5H,GAAG,EAAE8G,iBAAiB,EAAE;IACjD,IAAI,CAAChG,SAAS,CAACd,GAAG,CAAC,IAAI,CAACA,GAAG,CAACQ,MAAM,EAAE;MAChC,MAAM,IAAImH,KAAK,CAAC,iDAAiD,CAAC;IACtE;IACA,OAAOvJ,MAAM,CAACC,KAAK,CAAC,MAAM,IAAI,CAACqJ,GAAG,CAAC1H,GAAG,EAAE8G,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC3C,mBAAmB,CAACiB,IAAI,CAAC1G,SAAS,CAAEmJ,KAAK,IAAK;MAC5G,MAAM9B,GAAG,GAAG,IAAI,CAACoB,eAAe,CAACU,KAAK,CAACtE,YAAY,EAAEvD,GAAG,EAAE8G,iBAAiB,CAAC;MAC5E,OAAO7C,cAAc,CAAC8B,GAAG,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC;EACR;EACA;AACJ;AACA;AACA;AACA;EACI+B,MAAMA,CAAC9H,GAAG,EAAE8G,iBAAiB,EAAE;IAC3B,IAAI,CAAChG,SAAS,CAACd,GAAG,CAAC,IAAI,CAACA,GAAG,CAACQ,MAAM,EAAE;MAChC,MAAM,IAAImH,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,OAAOvJ,MAAM,CAACC,KAAK,CAAC,MAAM,IAAI,CAACqJ,GAAG,CAAC1H,GAAG,EAAE8G,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACzC,YAAY,CAACe,IAAI,CAAC1G,SAAS,CAAEmJ,KAAK,IAAK;MACrG,MAAM9B,GAAG,GAAG,IAAI,CAACoB,eAAe,CAACU,KAAK,CAACtE,YAAY,EAAEvD,GAAG,EAAE8G,iBAAiB,CAAC;MAC5E,OAAO7C,cAAc,CAAC8B,GAAG,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC;EACR;EACA;AACJ;AACA;AACA;AACA;EACIgC,OAAOA,CAAC/H,GAAG,EAAE8G,iBAAiB,EAAE;IAC5B,IAAI,CAAChG,SAAS,CAACd,GAAG,CAAC,IAAIA,GAAG,CAACQ,MAAM,KAAK,CAAC,EAAE;MACrC,MAAM,IAAImH,KAAK,CAAC,iDAAiD,CAAC;IACtE;IACA,MAAMT,MAAM,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC5D,YAAY,CAAC,IAAI,CAACgB,WAAW,CAAC,EAAEvE,GAAG,EAAE8G,iBAAiB,CAAC;IAChG,IAAI5I,YAAY,CAACgJ,MAAM,CAAC,EAAE;MACtB,IAAIxG,KAAK,CAACC,OAAO,CAACX,GAAG,CAAC,EAAE;QACpB,OAAOA,GAAG,CAACgI,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;UAChCD,GAAG,CAACC,OAAO,CAAC,GAAGA,OAAO;UACtB,OAAOD,GAAG;QACd,CAAC,EAAE,CAAC,CAAC,CAAC;MACV;MACA,OAAOjI,GAAG;IACd;IACA,OAAOkH,MAAM;EACjB;EACA;AACJ;AACA;EACIiB,GAAGA,CAACnI,GAAG,EAAEiH,WAAW,EAAEnI,IAAI,GAAG,IAAI,CAACyF,WAAW,EAAE;IAC3CxC,QAAQ,CAAC,IAAI,CAACwB,YAAY,CAACzE,IAAI,CAAC,EAAEkB,GAAG,EAAEkB,QAAQ,CAAC+F,WAAW,CAAC,GACtD,IAAI,CAACvC,QAAQ,CAACrB,OAAO,CAAC4D,WAAW,EAAEnI,IAAI,CAAC,GACxC,IAAI,CAAC4F,QAAQ,CAACpB,mBAAmB,CAAC2D,WAAW,EAAEnI,IAAI,CAAC,CAAC;IAC3D,IAAI,CAACoH,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC/B,mBAAmB,CAACwB,IAAI,CAAC;MAAE7G,IAAI,EAAEA,IAAI;MAAEyE,YAAY,EAAE,IAAI,CAACA,YAAY,CAACzE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;EACIwG,iBAAiBA,CAACxG,IAAI,EAAE;IACpB,IAAI,CAAC8E,WAAW,GAAG9E,IAAI;IACvB,IAAI,CAACwF,mBAAmB,CAACqB,IAAI,CAAC;MAAE7G,IAAI,EAAEA,IAAI;MAAEyE,YAAY,EAAE,IAAI,CAACA,YAAY,CAACzE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;EACIsJ,UAAUA,CAACtJ,IAAI,EAAE;IACb,IAAI,CAACuJ,SAAS,CAACvJ,IAAI,CAAC;IACpB,OAAO,IAAI,CAAC+G,0BAA0B,CAAC/G,IAAI,CAAC;EAChD;EACA;AACJ;AACA;EACIuJ,SAASA,CAACvJ,IAAI,EAAE;IACZ,OAAO,IAAI,CAAC8G,oBAAoB,CAAC9G,IAAI,CAAC;IACtC,OAAO,IAAI,CAACyE,YAAY,CAACzE,IAAI,CAAC;EAClC;EACA;AACJ;AACA;EACIwJ,cAAcA,CAAA,EAAG;IACb,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,SAAS,EAAE;MACpD,OAAO1G,SAAS;IACpB;IACA,MAAM2G,WAAW,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChD,OAAOD,WAAW,GAAGA,WAAW,CAAC7G,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGE,SAAS;EACjE;EACA;AACJ;AACA;EACI4G,qBAAqBA,CAAA,EAAG;IACpB,IAAI,OAAOH,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,SAAS,KAAK,WAAW,EAAE;MAC1E,OAAO1G,SAAS;IACpB;IACA,OAAOyG,MAAM,CAACC,SAAS,CAACG,SAAS,GAC3BJ,MAAM,CAACC,SAAS,CAACG,SAAS,CAAC,CAAC,CAAC,GAC5BJ,MAAM,CAACC,SAAS,CAACI,QAAQ,IAAIL,MAAM,CAACC,SAAS,CAACK,eAAe,IAAIN,MAAM,CAACC,SAAS,CAACM,YAAa;EAC1G;AAGJ;AAACC,iBAAA,GAjbK7E,gBAAgB;AAAAlF,eAAA,CAAhBkF,gBAAgB,wBAAA8E,0BAAA7J,iBAAA;EAAA,YAAAA,iBAAA,IA+aiF+E,iBAAgB,EA3rBtC3G,EAAE,CAAA0L,QAAA,CA2rBsDtF,cAAc,GA3rBtEpG,EAAE,CAAA0L,QAAA,CA2rBiFtK,eAAe,GA3rBlGpB,EAAE,CAAA0L,QAAA,CA2rB6G9F,iBAAiB,GA3rBhI5F,EAAE,CAAA0L,QAAA,CA2rB2I/G,eAAe,GA3rB5J3E,EAAE,CAAA0L,QAAA,CA2rBuKrJ,yBAAyB,GA3rBlMrC,EAAE,CAAA0L,QAAA,CA2rB6MnF,gBAAgB,GA3rB/NvG,EAAE,CAAA0L,QAAA,CA2rB0OpF,yBAAyB,GA3rBrQtG,EAAE,CAAA0L,QAAA,CA2rBgRjF,UAAU,GA3rB5RzG,EAAE,CAAA0L,QAAA,CA2rBuSlF,gBAAgB;AAAA;AAAA/E,eAAA,CA/apYkF,gBAAgB,+BA5Q2D3G,EAAE,CAAA8B,kBAAA;EAAAC,KAAA,EA4rBwB4E,iBAAgB;EAAA3E,OAAA,EAAhB2E,iBAAgB,CAAA1E,IAAA;EAAA0J,UAAA,EAAc;AAAM;AAE/I;EAAA,QAAAzJ,SAAA,oBAAAA,SAAA,KA9rBiFlC,EAAE,CAAAmC,iBAAA,CA8rBQwE,gBAAgB,EAAc,CAAC;IAC9GvE,IAAI,EAAEnC,UAAU;IAChB6E,IAAI,EAAE,CAAC;MACC6G,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvJ,IAAI,EAAEgE;EAAe,CAAC,EAAE;IAAEhE,IAAI,EAAEhB;EAAgB,CAAC,EAAE;IAAEgB,IAAI,EAAEwD;EAAkB,CAAC,EAAE;IAAExD,IAAI,EAAEuC;EAAgB,CAAC,EAAE;IAAEvC,IAAI,EAAEC;EAA0B,CAAC,EAAE;IAAED,IAAI,EAAEmC,SAAS;IAAEqH,UAAU,EAAE,CAAC;MACjMxJ,IAAI,EAAEhC,MAAM;MACZ0E,IAAI,EAAE,CAACyB,gBAAgB;IAC3B,CAAC;EAAE,CAAC,EAAE;IAAEnE,IAAI,EAAEmC,SAAS;IAAEqH,UAAU,EAAE,CAAC;MAClCxJ,IAAI,EAAEhC,MAAM;MACZ0E,IAAI,EAAE,CAACwB,yBAAyB;IACpC,CAAC;EAAE,CAAC,EAAE;IAAElE,IAAI,EAAEmC,SAAS;IAAEqH,UAAU,EAAE,CAAC;MAClCxJ,IAAI,EAAEhC,MAAM;MACZ0E,IAAI,EAAE,CAAC2B,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAErE,IAAI,EAAEmC,SAAS;IAAEqH,UAAU,EAAE,CAAC;MAClCxJ,IAAI,EAAEhC,MAAM;MACZ0E,IAAI,EAAE,CAAC0B,gBAAgB;IAC3B,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMqF,kBAAkB,CAAC;EAUrB,IAAIC,SAASA,CAACrJ,GAAG,EAAE;IACf,IAAIA,GAAG,EAAE;MACL,IAAI,CAACA,GAAG,GAAGA,GAAG;MACd,IAAI,CAACsJ,UAAU,CAAC,CAAC;IACrB;EACJ;EACA,IAAIC,eAAeA,CAACxJ,MAAM,EAAE;IACxB,IAAI,CAACI,MAAM,CAAC,IAAI,CAACqJ,aAAa,EAAEzJ,MAAM,CAAC,EAAE;MACrC,IAAI,CAACyJ,aAAa,GAAGzJ,MAAM;MAC3B,IAAI,CAACuJ,UAAU,CAAC,IAAI,CAAC;IACzB;EACJ;EACAlH,WAAWA,CAAC4E,gBAAgB,EAAEyC,OAAO,EAAEC,IAAI,EAAE;IAAA1K,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACzC,IAAI,CAACgI,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACyC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAAC,IAAI,CAACC,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC3C,gBAAgB,CAAC7C,mBAAmB,CAACkB,SAAS,CAAEwC,KAAK,IAAK;QACzF,IAAIA,KAAK,CAAC/I,IAAI,KAAK,IAAI,CAACkI,gBAAgB,CAACzC,WAAW,EAAE;UAClD,IAAI,CAAC+E,UAAU,CAAC,IAAI,EAAEzB,KAAK,CAACtE,YAAY,CAAC;QAC7C;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAACqG,eAAe,EAAE;MACvB,IAAI,CAACA,eAAe,GAAG,IAAI,CAAC5C,gBAAgB,CAAC3C,YAAY,CAACgB,SAAS,CAAEwC,KAAK,IAAK;QAC3E,IAAI,CAACyB,UAAU,CAAC,IAAI,EAAEzB,KAAK,CAACtE,YAAY,CAAC;MAC7C,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAACsG,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC7C,gBAAgB,CAAC1C,mBAAmB,CAACe,SAAS,CAAEwC,KAAK,IAAK;QACzF,KAAKA,KAAK;QACV,IAAI,CAACyB,UAAU,CAAC,IAAI,CAAC;MACzB,CAAC,CAAC;IACN;EACJ;EACAQ,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACR,UAAU,CAAC,CAAC;EACrB;EACAA,UAAUA,CAACS,WAAW,GAAG,KAAK,EAAExG,YAAY,EAAE;IAC1C,IAAIyG,KAAK,GAAG,IAAI,CAACP,OAAO,CAACQ,aAAa,CAACC,UAAU;IACjD;IACA,IAAI,CAACF,KAAK,CAACxJ,MAAM,EAAE;MACf;MACA,IAAI,CAAC2J,UAAU,CAAC,IAAI,CAACV,OAAO,CAACQ,aAAa,EAAE,IAAI,CAACjK,GAAG,CAAC;MACrDgK,KAAK,GAAG,IAAI,CAACP,OAAO,CAACQ,aAAa,CAACC,UAAU;IACjD;IACAF,KAAK,CAACtI,OAAO,CAAE0I,CAAC,IAAK;MACjB,MAAMC,IAAI,GAAGD,CAAC;MACd,IAAIC,IAAI,CAACC,QAAQ,KAAK,CAAC,EAAE;QAAE;QACvB,IAAItK,GAAG;QACP,IAAI+J,WAAW,EAAE;UACbM,IAAI,CAACE,OAAO,GAAG,IAAI;QACvB;QACA,IAAIzJ,SAAS,CAACuJ,IAAI,CAACG,SAAS,CAAC,EAAE;UAC3BxK,GAAG,GAAGqK,IAAI,CAACG,SAAS;QACxB,CAAC,MACI,IAAI,IAAI,CAACxK,GAAG,EAAE;UACfA,GAAG,GAAG,IAAI,CAACA,GAAG;QAClB,CAAC,MACI;UACD,MAAMyK,OAAO,GAAG,IAAI,CAACC,UAAU,CAACL,IAAI,CAAC;UACrC,MAAMM,cAAc,GAAGF,OAAO,CAACG,IAAI,CAAC,CAAC;UACrC,IAAID,cAAc,CAACnK,MAAM,EAAE;YACvB6J,IAAI,CAACG,SAAS,GAAGG,cAAc;YAC/B;YACA,IAAIF,OAAO,KAAKJ,IAAI,CAACQ,YAAY,EAAE;cAC/B7K,GAAG,GAAG2K,cAAc;cACpB;cACAN,IAAI,CAACS,eAAe,GAAGL,OAAO,IAAIJ,IAAI,CAACS,eAAe;YAC1D,CAAC,MACI,IAAIT,IAAI,CAACS,eAAe,EAAE;cAAE;cAC7B;cACA9K,GAAG,GAAGqK,IAAI,CAACS,eAAe,CAACF,IAAI,CAAC,CAAC;YACrC;UACJ;QACJ;QACA,IAAI,CAACG,WAAW,CAAC/K,GAAG,EAAEqK,IAAI,EAAE9G,YAAY,CAAC;MAC7C;IACJ,CAAC,CAAC;EACN;EACAwH,WAAWA,CAAC/K,GAAG,EAAEqK,IAAI,EAAE9G,YAAY,EAAE;IACjC,IAAIvD,GAAG,EAAE;MACL,IAAIqK,IAAI,CAACE,OAAO,KAAKvK,GAAG,IAAI,IAAI,CAACgL,UAAU,KAAK,IAAI,CAACxB,aAAa,EAAE;QAChE;MACJ;MACA,IAAI,CAACwB,UAAU,GAAG,IAAI,CAACxB,aAAa;MACpC,MAAMyB,aAAa,GAAIlF,GAAG,IAAK;QAC3B,IAAIA,GAAG,KAAK/F,GAAG,IAAI,CAACqK,IAAI,CAACE,OAAO,EAAE;UAC9BF,IAAI,CAACE,OAAO,GAAGvK,GAAG;QACtB;QACA,IAAI,CAACqK,IAAI,CAACS,eAAe,EAAE;UACvBT,IAAI,CAACS,eAAe,GAAG,IAAI,CAACJ,UAAU,CAACL,IAAI,CAAC;QAChD;QACAA,IAAI,CAACQ,YAAY,GAAG/J,SAAS,CAACiF,GAAG,CAAC,GAAGA,GAAG,GAAIsE,IAAI,CAACS,eAAe,IAAI9K,GAAI;QACxE;QACA,IAAI,CAACmK,UAAU,CAACE,IAAI,EAAE,IAAI,CAACrK,GAAG,GAAGqK,IAAI,CAACQ,YAAY,GAAGR,IAAI,CAACS,eAAe,CAACnI,OAAO,CAAC3C,GAAG,EAAEqK,IAAI,CAACQ,YAAY,CAAC,CAAC;QAC1G,IAAI,CAACnB,IAAI,CAACwB,YAAY,CAAC,CAAC;MAC5B,CAAC;MACD,IAAIpK,SAAS,CAACyC,YAAY,CAAC,EAAE;QACzB,MAAMwC,GAAG,GAAG,IAAI,CAACiB,gBAAgB,CAACG,eAAe,CAAC5D,YAAY,EAAEvD,GAAG,EAAE,IAAI,CAACwJ,aAAa,CAAC;QACxF,IAAItL,YAAY,CAAC6H,GAAG,CAAC,EAAE;UACnBA,GAAG,CAACV,SAAS,CAAC;YAAEW,IAAI,EAAEiF;UAAc,CAAC,CAAC;QAC1C,CAAC,MACI;UACDA,aAAa,CAAClF,GAAG,CAAC;QACtB;MACJ,CAAC,MACI;QACD,IAAI,CAACiB,gBAAgB,CAACU,GAAG,CAAC1H,GAAG,EAAE,IAAI,CAACwJ,aAAa,CAAC,CAACnE,SAAS,CAAC4F,aAAa,CAAC;MAC/E;IACJ;EACJ;EACAP,UAAUA,CAACL,IAAI,EAAE;IACb,OAAQvJ,SAAS,CAACuJ,IAAI,CAACc,WAAW,CAAC,GAAGd,IAAI,CAACc,WAAW,GAAGd,IAAI,CAACe,IAAI;EACtE;EACAjB,UAAUA,CAACE,IAAI,EAAEI,OAAO,EAAE;IACtB,IAAI3J,SAAS,CAACuJ,IAAI,CAACc,WAAW,CAAC,EAAE;MAC7Bd,IAAI,CAACc,WAAW,GAAGV,OAAO;IAC9B,CAAC,MACI;MACDJ,IAAI,CAACe,IAAI,GAAGX,OAAO;IACvB;EACJ;EACAY,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACzB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC0B,WAAW,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAACzB,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAACyB,WAAW,CAAC,CAAC;IAC7C;IACA,IAAI,IAAI,CAAC3B,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC2B,WAAW,CAAC,CAAC;IAC7C;EACJ;AAGJ;AAACC,mBAAA,GArJKnC,kBAAkB;AAAApK,eAAA,CAAlBoK,kBAAkB,wBAAAoC,4BAAArM,iBAAA;EAAA,YAAAA,iBAAA,IAmJ+EiK,mBAAkB,EAp2BxC7L,EAAE,CAAAkO,iBAAA,CAo2BwDvH,gBAAgB,GAp2B1E3G,EAAE,CAAAkO,iBAAA,CAo2BqFlO,EAAE,CAACmO,UAAU,GAp2BpGnO,EAAE,CAAAkO,iBAAA,CAo2B+GlO,EAAE,CAACoO,iBAAiB;AAAA;AAAA3M,eAAA,CAnJhNoK,kBAAkB,8BAjtByD7L,EAAE,CAAAqO,iBAAA;EAAAjM,IAAA,EAq2BQyJ,mBAAkB;EAAAyC,SAAA;EAAAC,MAAA;IAAAzC,SAAA;IAAAE,eAAA;EAAA;AAAA;AAE7G;EAAA,QAAA9J,SAAA,oBAAAA,SAAA,KAv2BiFlC,EAAE,CAAAmC,iBAAA,CAu2BQ0J,kBAAkB,EAAc,CAAC;IAChHzJ,IAAI,EAAE/B,SAAS;IACfyE,IAAI,EAAE,CAAC;MACC;MACA0J,QAAQ,EAAE,6BAA6B;MACvCC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAErM,IAAI,EAAEuE;EAAiB,CAAC,EAAE;IAAEvE,IAAI,EAAEpC,EAAE,CAACmO;EAAW,CAAC,EAAE;IAAE/L,IAAI,EAAEpC,EAAE,CAACoO;EAAkB,CAAC,CAAC,EAAkB;IAAEtC,SAAS,EAAE,CAAC;MACvI1J,IAAI,EAAE9B;IACV,CAAC,CAAC;IAAE0L,eAAe,EAAE,CAAC;MAClB5J,IAAI,EAAE9B;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoO,aAAa,CAAC;EAShB7J,WAAWA,CAACiH,SAAS,EAAEK,IAAI,EAAE;IAAA1K,eAAA;IAAAA,eAAA;IAAAA,eAAA,gBANrB,EAAE;IAAAA,eAAA,kBACA,IAAI;IAAAA,eAAA,qBACD,EAAE;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAKX,IAAI,CAACqK,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACK,IAAI,GAAGA,IAAI;EACpB;EACAqB,WAAWA,CAAC/K,GAAG,EAAE8G,iBAAiB,EAAEvD,YAAY,EAAE;IAC9C,MAAM0H,aAAa,GAAIlF,GAAG,IAAK;MAC3B,IAAI,CAAChF,KAAK,GAAGgF,GAAG,KAAKjE,SAAS,GAAGiE,GAAG,GAAG/F,GAAG;MAC1C,IAAI,CAACuK,OAAO,GAAGvK,GAAG;MAClB,IAAI,CAAC0J,IAAI,CAACwB,YAAY,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI3H,YAAY,EAAE;MACd,MAAMwC,GAAG,GAAG,IAAI,CAACsD,SAAS,CAAClC,eAAe,CAAC5D,YAAY,EAAEvD,GAAG,EAAE8G,iBAAiB,CAAC;MAChF,IAAI5I,YAAY,CAAC6H,GAAG,CAAC,EAAE;QACnBA,GAAG,CAACV,SAAS,CAAC4F,aAAa,CAAC;MAChC,CAAC,MACI;QACDA,aAAa,CAAClF,GAAG,CAAC;MACtB;IACJ;IACA,IAAI,CAACsD,SAAS,CAAC3B,GAAG,CAAC1H,GAAG,EAAE8G,iBAAiB,CAAC,CAACzB,SAAS,CAAC4F,aAAa,CAAC;EACvE;EACA;EACAiB,SAASA,CAACC,KAAK,EAAE,GAAG9J,IAAI,EAAE;IACtB,IAAI,CAAC8J,KAAK,IAAI,CAACA,KAAK,CAAC3L,MAAM,EAAE;MACzB,OAAO2L,KAAK;IAChB;IACA;IACA,IAAIhM,MAAM,CAACgM,KAAK,EAAE,IAAI,CAAC5B,OAAO,CAAC,IAAIpK,MAAM,CAACkC,IAAI,EAAE,IAAI,CAAC2I,UAAU,CAAC,EAAE;MAC9D,OAAO,IAAI,CAACjK,KAAK;IACrB;IACA,IAAI+F,iBAAiB,GAAGhF,SAAS;IACjC,IAAIhB,SAAS,CAACuB,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC7B,MAAM,EAAE;MACnC,IAAIU,QAAQ,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC7B,MAAM,EAAE;QACrC;QACA;QACA,MAAM4L,SAAS,GAAG/J,IAAI,CAAC,CAAC,CAAC,CACpBM,OAAO,CAAC,gCAAgC,EAAE,OAAO,CAAC,CAClDA,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC;QAC3C,IAAI;UACAmE,iBAAiB,GAAGuF,IAAI,CAACC,KAAK,CAACF,SAAS,CAAC;QAC7C,CAAC,CACD,OAAOG,CAAC,EAAE;UACN,KAAKA,CAAC;UACN,MAAM,IAAIC,WAAW,CAAC,wEAAwEnK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5G;MACJ,CAAC,MACI,IAAIrB,MAAM,CAACqB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QACtByE,iBAAiB,GAAGzE,IAAI,CAAC,CAAC,CAAC;MAC/B;IACJ;IACA;IACA,IAAI,CAACkI,OAAO,GAAG4B,KAAK;IACpB;IACA,IAAI,CAACnB,UAAU,GAAG3I,IAAI;IACtB;IACA,IAAI,CAAC0I,WAAW,CAACoB,KAAK,EAAErF,iBAAiB,CAAC;IAC1C;IACA,IAAI,CAAC2F,QAAQ,CAAC,CAAC;IACf;IACA,IAAI,CAAC,IAAI,CAACtI,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAACkF,SAAS,CAAClF,mBAAmB,CAACkB,SAAS,CAAEwC,KAAK,IAAK;QAC/E,IAAI,IAAI,CAAC0C,OAAO,IAAI1C,KAAK,CAAC/I,IAAI,KAAK,IAAI,CAACuK,SAAS,CAAC9E,WAAW,EAAE;UAC3D,IAAI,CAACgG,OAAO,GAAG,IAAI;UACnB,IAAI,CAACQ,WAAW,CAACoB,KAAK,EAAErF,iBAAiB,EAAEe,KAAK,CAACtE,YAAY,CAAC;QAClE;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAACc,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACgF,SAAS,CAAChF,YAAY,CAACgB,SAAS,CAAEwC,KAAK,IAAK;QACjE,IAAI,IAAI,CAAC0C,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC,CAAC;UACrB,IAAI,CAACQ,WAAW,CAACoB,KAAK,EAAErF,iBAAiB,EAAEe,KAAK,CAACtE,YAAY,CAAC;QAClE;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAACe,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAAC+E,SAAS,CAAC/E,mBAAmB,CAACe,SAAS,CAAC,MAAM;QAC1E,IAAI,IAAI,CAACkF,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC,CAAC;UACrB,IAAI,CAACQ,WAAW,CAACoB,KAAK,EAAErF,iBAAiB,CAAC;QAC9C;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAAC/F,KAAK;EACrB;EACA;AACJ;AACA;EACI0L,QAAQA,CAAA,EAAG;IACP,IAAI,OAAO,IAAI,CAACtI,mBAAmB,KAAK,WAAW,EAAE;MACjD,IAAI,CAACA,mBAAmB,CAACmH,WAAW,CAAC,CAAC;MACtC,IAAI,CAACnH,mBAAmB,GAAGrC,SAAS;IACxC;IACA,IAAI,OAAO,IAAI,CAACuC,YAAY,KAAK,WAAW,EAAE;MAC1C,IAAI,CAACA,YAAY,CAACiH,WAAW,CAAC,CAAC;MAC/B,IAAI,CAACjH,YAAY,GAAGvC,SAAS;IACjC;IACA,IAAI,OAAO,IAAI,CAACwC,mBAAmB,KAAK,WAAW,EAAE;MACjD,IAAI,CAACA,mBAAmB,CAACgH,WAAW,CAAC,CAAC;MACtC,IAAI,CAAChH,mBAAmB,GAAGxC,SAAS;IACxC;EACJ;EACAuJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoB,QAAQ,CAAC,CAAC;EACnB;AAIJ;AAACC,cAAA,GAvHKT,aAAa;AAAAjN,eAAA,CAAbiN,aAAa,wBAAAU,uBAAAxN,iBAAA;EAAA,YAAAA,iBAAA,IAoHoF8M,cAAa,EAx+BnC1O,EAAE,CAAAkO,iBAAA,CAw+BmDvH,gBAAgB,OAx+BrE3G,EAAE,CAAAkO,iBAAA,CAw+BgFlO,EAAE,CAACoO,iBAAiB;AAAA;AAAA3M,eAAA,CApHjLiN,aAAa,+BAp3B8D1O,EAAE,CAAAqP,YAAA;EAAAC,IAAA;EAAAlN,IAAA,EAy+BkBsM,cAAa;EAAAa,IAAA;AAAA;AAAA9N,eAAA,CArH5GiN,aAAa,+BAp3B8D1O,EAAE,CAAA8B,kBAAA;EAAAC,KAAA,EA0+BwB2M,cAAa;EAAA1M,OAAA,EAAb0M,cAAa,CAAAzM;AAAA;AAExH;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5+BiFlC,EAAE,CAAAmC,iBAAA,CA4+BQuM,aAAa,EAAc,CAAC;IAC3GtM,IAAI,EAAEnC;EACV,CAAC,EAAE;IACCmC,IAAI,EAAE7B,IAAI;IACVuE,IAAI,EAAE,CAAC;MACCwK,IAAI,EAAE,WAAW;MACjBb,UAAU,EAAE,IAAI;MAChBc,IAAI,EAAE,KAAK,CAAC;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnN,IAAI,EAAEuE;EAAiB,CAAC,EAAE;IAAEvE,IAAI,EAAEpC,EAAE,CAACoO;EAAkB,CAAC,CAAC;AAAA;AAE9F,SAASoB,CAACA,CAAC/M,GAAG,EAAE;EACZ,OAAOA,GAAG;AACd;AAEA,MAAMgN,uBAAuB,GAAGA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;EAC7C,OAAOlP,wBAAwB,CAAC,CAC5BkP,MAAM,CAACC,MAAM,IAAI;IAAEC,OAAO,EAAExO,eAAe;IAAEyO,QAAQ,EAAExO;EAAoB,CAAC,EAC5EqO,MAAM,CAACvI,QAAQ,IAAI;IAAEyI,OAAO,EAAEhK,iBAAiB;IAAEiK,QAAQ,EAAEhK;EAAsB,CAAC,EAClF6J,MAAM,CAACtI,MAAM,IAAI;IAAEwI,OAAO,EAAEjL,eAAe;IAAEkL,QAAQ,EAAEjL;EAAuB,CAAC,EAC/E8K,MAAM,CAACrI,yBAAyB,IAAI;IAAEuI,OAAO,EAAEvN,yBAAyB;IAAEwN,QAAQ,EAAEvN;EAA8B,CAAC,EACnH8D,cAAc,EACd;IAAEwJ,OAAO,EAAEtJ,yBAAyB;IAAEwJ,QAAQ,EAAEJ,MAAM,CAACnI;EAAQ,CAAC,EAChE;IAAEqI,OAAO,EAAErJ,gBAAgB;IAAEuJ,QAAQ,EAAEJ,MAAM,CAACpI;EAAe,CAAC,EAC9D;IAAEsI,OAAO,EAAEnJ,UAAU;IAAEqJ,QAAQ,EAAEJ,MAAM,CAAClI;EAAO,CAAC,EAChD;IAAEoI,OAAO,EAAEpJ,gBAAgB;IAAEsJ,QAAQ,EAAEJ,MAAM,CAACjI;EAAgB,CAAC,EAC/Dd,gBAAgB,CACnB,CAAC;AACN,CAAC;AACD,MAAMoJ,eAAe,CAAC;EAClB;AACJ;AACA;EACI,OAAOC,OAAOA,CAACN,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO;MACHO,QAAQ,EAAEF,eAAe;MACzBG,SAAS,EAAE,CACPR,MAAM,CAACC,MAAM,IAAI;QAAEC,OAAO,EAAExO,eAAe;QAAEyO,QAAQ,EAAExO;MAAoB,CAAC,EAC5EqO,MAAM,CAACvI,QAAQ,IAAI;QAAEyI,OAAO,EAAEhK,iBAAiB;QAAEiK,QAAQ,EAAEhK;MAAsB,CAAC,EAClF6J,MAAM,CAACtI,MAAM,IAAI;QAAEwI,OAAO,EAAEjL,eAAe;QAAEkL,QAAQ,EAAEjL;MAAuB,CAAC,EAC/E8K,MAAM,CAACrI,yBAAyB,IAAI;QAAEuI,OAAO,EAAEvN,yBAAyB;QAAEwN,QAAQ,EAAEvN;MAA8B,CAAC,EACnH8D,cAAc,EACd;QAAEwJ,OAAO,EAAEtJ,yBAAyB;QAAEwJ,QAAQ,EAAEJ,MAAM,CAACnI;MAAQ,CAAC,EAChE;QAAEqI,OAAO,EAAErJ,gBAAgB;QAAEuJ,QAAQ,EAAEJ,MAAM,CAACpI;MAAe,CAAC,EAC9D;QAAEsI,OAAO,EAAEnJ,UAAU;QAAEqJ,QAAQ,EAAEJ,MAAM,CAAClI;MAAO,CAAC,EAChD;QAAEoI,OAAO,EAAEpJ,gBAAgB;QAAEsJ,QAAQ,EAAEJ,MAAM,CAACjI;MAAgB,CAAC,EAC/Dd,gBAAgB;IAExB,CAAC;EACL;EACA;AACJ;AACA;EACI,OAAOwJ,QAAQA,CAACT,MAAM,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO;MACHO,QAAQ,EAAEF,eAAe;MACzBG,SAAS,EAAE,CACPR,MAAM,CAACC,MAAM,IAAI;QAAEC,OAAO,EAAExO,eAAe;QAAEyO,QAAQ,EAAExO;MAAoB,CAAC,EAC5EqO,MAAM,CAACvI,QAAQ,IAAI;QAAEyI,OAAO,EAAEhK,iBAAiB;QAAEiK,QAAQ,EAAEhK;MAAsB,CAAC,EAClF6J,MAAM,CAACtI,MAAM,IAAI;QAAEwI,OAAO,EAAEjL,eAAe;QAAEkL,QAAQ,EAAEjL;MAAuB,CAAC,EAC/E8K,MAAM,CAACrI,yBAAyB,IAAI;QAAEuI,OAAO,EAAEvN,yBAAyB;QAAEwN,QAAQ,EAAEvN;MAA8B,CAAC,EACnH;QAAEsN,OAAO,EAAEtJ,yBAAyB;QAAEwJ,QAAQ,EAAEJ,MAAM,CAACnI;MAAQ,CAAC,EAChE;QAAEqI,OAAO,EAAErJ,gBAAgB;QAAEuJ,QAAQ,EAAEJ,MAAM,CAACpI;MAAe,CAAC,EAC9D;QAAEsI,OAAO,EAAEnJ,UAAU;QAAEqJ,QAAQ,EAAEJ,MAAM,CAAClI;MAAO,CAAC,EAChD;QAAEoI,OAAO,EAAEpJ,gBAAgB;QAAEsJ,QAAQ,EAAEJ,MAAM,CAACjI;MAAgB,CAAC,EAC/Dd,gBAAgB;IAExB,CAAC;EACL;AAMJ;AAACyJ,gBAAA,GA7CKL,eAAe;AAAAtO,eAAA,CAAfsO,eAAe,wBAAAM,yBAAAzO,iBAAA;EAAA,YAAAA,iBAAA,IAwCkFmO,gBAAe;AAAA;AAAAtO,eAAA,CAxChHsO,eAAe,8BAzgC4D/P,EAAE,CAAAsQ,gBAAA;EAAAlO,IAAA,EAkjCqB2N,gBAAe;EAAAQ,OAAA,GAAY7B,aAAa,EACpI7C,kBAAkB;EAAA2E,OAAA,GAAa9B,aAAa,EAC5C7C,kBAAkB;AAAA;AAAApK,eAAA,CA3CxBsO,eAAe,8BAzgC4D/P,EAAE,CAAAyQ,gBAAA;AAujCnF;EAAA,QAAAvO,SAAA,oBAAAA,SAAA,KAvjCiFlC,EAAE,CAAAmC,iBAAA,CAujCQ4N,eAAe,EAAc,CAAC;IAC7G3N,IAAI,EAAE3B,QAAQ;IACdqE,IAAI,EAAE,CAAC;MACCyL,OAAO,EAAE,CACL7B,aAAa,EACb7C,kBAAkB,CACrB;MACD2E,OAAO,EAAE,CACL9B,aAAa,EACb7C,kBAAkB;IAE1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASrF,gBAAgB,EAAElE,6BAA6B,EAAEgE,yBAAyB,EAAEjE,yBAAyB,EAAEuD,iBAAiB,EAAEhB,sBAAsB,EAAEiH,kBAAkB,EAAEhG,qBAAqB,EAAExE,mBAAmB,EAAED,eAAe,EAAE2O,eAAe,EAAEpL,eAAe,EAAE+J,aAAa,EAAE/H,gBAAgB,EAAEP,cAAc,EAAEG,gBAAgB,EAAEE,UAAU,EAAE+I,CAAC,EAAE5M,MAAM,EAAEwB,QAAQ,EAAEhB,OAAO,EAAEG,SAAS,EAAEE,MAAM,EAAEG,UAAU,EAAEF,QAAQ,EAAEC,QAAQ,EAAEE,SAAS,EAAE4L,uBAAuB,EAAEjL,QAAQ;AAC1d", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}