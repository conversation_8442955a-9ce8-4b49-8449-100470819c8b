{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatRippleModule } from '@angular/material/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CustomPeriodComponent } from './custom-period.component';\nexport const MODULES = [MatMenuModule, MatButtonModule, MatRippleModule];\nlet CustomPeriodModule = class CustomPeriodModule {};\nCustomPeriodModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule.forChild(), ...MODULES],\n  declarations: [CustomPeriodComponent],\n  exports: [CustomPeriodComponent]\n})], CustomPeriodModule);\nexport { CustomPeriodModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "MatButtonModule", "MatMenuModule", "MatRippleModule", "TranslateModule", "CustomPeriodComponent", "MODULES", "CustomPeriodModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-range/custom-period/custom-period.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatRippleModule } from '@angular/material/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { CustomPeriodComponent } from './custom-period.component';\nexport const MODULES = [\n    MatMenuModule,\n    MatButtonModule,\n    MatRippleModule,\n];\nlet CustomPeriodModule = class CustomPeriodModule {\n};\nCustomPeriodModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule.forChild(),\n            ...MODULES,\n        ],\n        declarations: [\n            CustomPeriodComponent\n        ],\n        exports: [\n            CustomPeriodComponent\n        ]\n    })\n], CustomPeriodModule);\nexport { CustomPeriodModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,OAAO,MAAMC,OAAO,GAAG,CACnBJ,aAAa,EACbD,eAAe,EACfE,eAAe,CAClB;AACD,IAAII,kBAAkB,GAAG,MAAMA,kBAAkB,CAAC,EACjD;AACDA,kBAAkB,GAAGT,UAAU,CAAC,CAC5BC,QAAQ,CAAC;EACLS,OAAO,EAAE,CACLR,YAAY,EACZI,eAAe,CAACK,QAAQ,CAAC,CAAC,EAC1B,GAAGH,OAAO,CACb;EACDI,YAAY,EAAE,CACVL,qBAAqB,CACxB;EACDM,OAAO,EAAE,CACLN,qBAAqB;AAE7B,CAAC,CAAC,CACL,EAAEE,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}