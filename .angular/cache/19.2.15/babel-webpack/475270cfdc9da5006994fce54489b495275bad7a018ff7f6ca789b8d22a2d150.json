{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatDynamicFormModule } from '../swui-dynamic-form/mat-dynamic-form.module';\nimport { MatDynamicFormService } from '../swui-dynamic-form/mat-dynamic-form.service';\nimport { SwuiMenuSelectModule } from '../swui-menu-select/swui-menu-select.module';\nimport { SwuiSchemaTopFilterComponent } from './swui-schema-top-filter.component';\nlet SwuiSchemaTopFilterModule = class SwuiSchemaTopFilterModule {};\nSwuiSchemaTopFilterModule = __decorate([NgModule({\n  imports: [MatDynamicFormModule, MatTooltipModule, MatMenuModule, SwuiMenuSelectModule, TranslateModule, MatIconModule, MatButtonModule, CommonModule],\n  exports: [SwuiSchemaTopFilterComponent],\n  declarations: [SwuiSchemaTopFilterComponent],\n  providers: [MatDynamicFormService]\n})], SwuiSchemaTopFilterModule);\nexport { SwuiSchemaTopFilterModule };", "map": {"version": 3, "names": ["__decorate", "CommonModule", "NgModule", "MatButtonModule", "MatIconModule", "MatMenuModule", "MatTooltipModule", "TranslateModule", "MatDynamicFormModule", "MatDynamicFormService", "SwuiMenuSelectModule", "SwuiSchemaTopFilterComponent", "SwuiSchemaTopFilterModule", "imports", "exports", "declarations", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-top-filter/swui-schema-top-filter.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatDynamicFormModule } from '../swui-dynamic-form/mat-dynamic-form.module';\nimport { MatDynamicFormService } from '../swui-dynamic-form/mat-dynamic-form.service';\nimport { SwuiMenuSelectModule } from '../swui-menu-select/swui-menu-select.module';\nimport { SwuiSchemaTopFilterComponent } from './swui-schema-top-filter.component';\nlet SwuiSchemaTopFilterModule = class SwuiSchemaTopFilterModule {\n};\nSwuiSchemaTopFilterModule = __decorate([\n    NgModule({\n        imports: [\n            MatDynamicFormModule,\n            MatTooltipModule,\n            MatMenuModule,\n            SwuiMenuSelectModule,\n            TranslateModule,\n            MatIconModule,\n            MatButtonModule,\n            CommonModule\n        ],\n        exports: [SwuiSchemaTopFilterComponent],\n        declarations: [SwuiSchemaTopFilterComponent],\n        providers: [MatDynamicFormService],\n    })\n], SwuiSchemaTopFilterModule);\nexport { SwuiSchemaTopFilterModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,oBAAoB,QAAQ,6CAA6C;AAClF,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,IAAIC,yBAAyB,GAAG,MAAMA,yBAAyB,CAAC,EAC/D;AACDA,yBAAyB,GAAGZ,UAAU,CAAC,CACnCE,QAAQ,CAAC;EACLW,OAAO,EAAE,CACLL,oBAAoB,EACpBF,gBAAgB,EAChBD,aAAa,EACbK,oBAAoB,EACpBH,eAAe,EACfH,aAAa,EACbD,eAAe,EACfF,YAAY,CACf;EACDa,OAAO,EAAE,CAACH,4BAA4B,CAAC;EACvCI,YAAY,EAAE,CAACJ,4BAA4B,CAAC;EAC5CK,SAAS,EAAE,CAACP,qBAAqB;AACrC,CAAC,CAAC,CACL,EAAEG,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}