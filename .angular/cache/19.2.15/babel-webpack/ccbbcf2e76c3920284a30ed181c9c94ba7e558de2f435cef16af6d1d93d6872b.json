{"ast": null, "code": "var _SwitcheryComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./switchery.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./switchery.component.scss?ngResource\";\nimport { Component, forwardRef, Input } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet SwitcheryComponent = (_SwitcheryComponent = class SwitcheryComponent {\n  constructor() {\n    this.prefix = [];\n    this.inline = false;\n    this.isDisabled = false;\n    this._checked = false;\n  }\n  set checked(value) {\n    this._checked = value;\n    if (typeof this.propagateChange === 'function') {\n      this.propagateChange(this.checked);\n    }\n  }\n  get checked() {\n    return this._checked;\n  }\n  writeValue(value) {\n    this.checked = value === true;\n  }\n  registerOnChange(fn) {\n    this.propagateChange = fn;\n  }\n  registerOnTouched() {}\n  setDisabledState(isDisabled) {\n    this.isDisabled = isDisabled;\n  }\n  checkedClass() {\n    return {\n      'checked': this.checked,\n      'disabled': this.isDisabled\n    };\n  }\n  actualPrefix() {\n    let prefix = '';\n    if (!!this.prefix[0] && this.checked) {\n      prefix = this.prefix[0];\n    }\n    if (!!this.prefix[1] && !this.checked) {\n      prefix = this.prefix[1];\n    }\n    return prefix;\n  }\n  toggleChecked(event) {\n    event.stopPropagation();\n    event.preventDefault();\n    if (this.isDisabled) {\n      return;\n    }\n    this.checked = !this.checked;\n  }\n}, _SwitcheryComponent.propDecorators = {\n  prefix: [{\n    type: Input\n  }],\n  inline: [{\n    type: Input\n  }],\n  checked: [{\n    type: Input\n  }]\n}, _SwitcheryComponent);\nSwitcheryComponent = __decorate([Component({\n  selector: 'lib-switchery',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SwitcheryComponent),\n    multi: true\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwitcheryComponent);\nexport { SwitcheryComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "forwardRef", "Input", "NG_VALUE_ACCESSOR", "SwitcheryComponent", "_SwitcheryComponent", "constructor", "prefix", "inline", "isDisabled", "_checked", "checked", "value", "propagateChange", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "checkedClass", "actualPrefix", "toggleChecked", "event", "stopPropagation", "preventDefault", "propDecorators", "type", "selector", "template", "providers", "provide", "useExisting", "multi", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-switchery/switchery.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./switchery.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./switchery.component.scss?ngResource\";\nimport { Component, forwardRef, Input } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet SwitcheryComponent = class SwitcheryComponent {\n    constructor() {\n        this.prefix = [];\n        this.inline = false;\n        this.isDisabled = false;\n        this._checked = false;\n    }\n    set checked(value) {\n        this._checked = value;\n        if (typeof this.propagateChange === 'function') {\n            this.propagateChange(this.checked);\n        }\n    }\n    get checked() {\n        return this._checked;\n    }\n    writeValue(value) {\n        this.checked = value === true;\n    }\n    registerOnChange(fn) {\n        this.propagateChange = fn;\n    }\n    registerOnTouched() {\n    }\n    setDisabledState(isDisabled) {\n        this.isDisabled = isDisabled;\n    }\n    checkedClass() {\n        return {\n            'checked': this.checked,\n            'disabled': this.isDisabled,\n        };\n    }\n    actualPrefix() {\n        let prefix = '';\n        if (!!this.prefix[0] && this.checked) {\n            prefix = this.prefix[0];\n        }\n        if (!!this.prefix[1] && !this.checked) {\n            prefix = this.prefix[1];\n        }\n        return prefix;\n    }\n    toggleChecked(event) {\n        event.stopPropagation();\n        event.preventDefault();\n        if (this.isDisabled) {\n            return;\n        }\n        this.checked = !this.checked;\n    }\n    static { this.propDecorators = {\n        prefix: [{ type: Input }],\n        inline: [{ type: Input }],\n        checked: [{ type: Input }]\n    }; }\n};\nSwitcheryComponent = __decorate([\n    Component({\n        selector: 'lib-switchery',\n        template: __NG_CLI_RESOURCE__0,\n        providers: [\n            { provide: NG_VALUE_ACCESSOR, useExisting: forwardRef(() => SwitcheryComponent), multi: true }\n        ],\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwitcheryComponent);\nexport { SwitcheryComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,SAASC,SAAS,EAAEC,UAAU,EAAEC,KAAK,QAAQ,eAAe;AAC5D,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,IAAIC,kBAAkB,IAAAC,mBAAA,GAAG,MAAMD,kBAAkB,CAAC;EAC9CE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACzB;EACA,IAAIC,OAAOA,CAACC,KAAK,EAAE;IACf,IAAI,CAACF,QAAQ,GAAGE,KAAK;IACrB,IAAI,OAAO,IAAI,CAACC,eAAe,KAAK,UAAU,EAAE;MAC5C,IAAI,CAACA,eAAe,CAAC,IAAI,CAACF,OAAO,CAAC;IACtC;EACJ;EACA,IAAIA,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,QAAQ;EACxB;EACAI,UAAUA,CAACF,KAAK,EAAE;IACd,IAAI,CAACD,OAAO,GAAGC,KAAK,KAAK,IAAI;EACjC;EACAG,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACH,eAAe,GAAGG,EAAE;EAC7B;EACAC,iBAAiBA,CAAA,EAAG,CACpB;EACAC,gBAAgBA,CAACT,UAAU,EAAE;IACzB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAU,YAAYA,CAAA,EAAG;IACX,OAAO;MACH,SAAS,EAAE,IAAI,CAACR,OAAO;MACvB,UAAU,EAAE,IAAI,CAACF;IACrB,CAAC;EACL;EACAW,YAAYA,CAAA,EAAG;IACX,IAAIb,MAAM,GAAG,EAAE;IACf,IAAI,CAAC,CAAC,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAACI,OAAO,EAAE;MAClCJ,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC;IAC3B;IACA,IAAI,CAAC,CAAC,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACI,OAAO,EAAE;MACnCJ,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC;IAC3B;IACA,OAAOA,MAAM;EACjB;EACAc,aAAaA,CAACC,KAAK,EAAE;IACjBA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvBD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtB,IAAI,IAAI,CAACf,UAAU,EAAE;MACjB;IACJ;IACA,IAAI,CAACE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;EAChC;AAMJ,CAAC,EALYN,mBAAA,CAAKoB,cAAc,GAAG;EAC3BlB,MAAM,EAAE,CAAC;IAAEmB,IAAI,EAAExB;EAAM,CAAC,CAAC;EACzBM,MAAM,EAAE,CAAC;IAAEkB,IAAI,EAAExB;EAAM,CAAC,CAAC;EACzBS,OAAO,EAAE,CAAC;IAAEe,IAAI,EAAExB;EAAM,CAAC;AAC7B,CAAC,EAAAG,mBAAA,CACJ;AACDD,kBAAkB,GAAGP,UAAU,CAAC,CAC5BG,SAAS,CAAC;EACN2B,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE9B,oBAAoB;EAC9B+B,SAAS,EAAE,CACP;IAAEC,OAAO,EAAE3B,iBAAiB;IAAE4B,WAAW,EAAE9B,UAAU,CAAC,MAAMG,kBAAkB,CAAC;IAAE4B,KAAK,EAAE;EAAK,CAAC,CACjG;EACDC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACnC,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEK,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}