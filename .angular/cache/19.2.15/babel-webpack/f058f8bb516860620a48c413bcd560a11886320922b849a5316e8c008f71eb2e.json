{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _ScrollStrategyOptions, _BaseOverlayDispatcher, _OverlayKeyboardDispatcher, _OverlayOutsideClickDispatcher, _CdkOverlayStyleLoader2, _OverlayContainer, _OverlayPositionBuilder, _Overlay, _CdkOverlayOrigin, _CdkConnectedOverlay, _OverlayModule;\nimport * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable, RendererFactory2, Component, ChangeDetectionStrategy, ViewEncapsulation, untracked, afterRender, afterNextRender, ElementRef, Injector, ANIMATION_MODULE_TYPE, EnvironmentInjector, ApplicationRef, InjectionToken, Directive, EventEmitter, TemplateRef, ViewContainerRef, booleanAttribute, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT, Location } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { _ as _getEventTarget } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { filter, takeUntil, takeWhile } from 'rxjs/operators';\nimport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\nimport { ScrollDispatcher, ViewportRuler, ScrollingModule } from './scrolling.mjs';\nimport { s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { b as DomPortalOutlet, T as TemplatePortal, h as PortalModule } from './portal-directives-Bw5woq8I.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { BidiModule } from './bidi.mjs';\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n  constructor(_viewportRuler, document) {\n    _defineProperty(this, \"_viewportRuler\", void 0);\n    _defineProperty(this, \"_previousHTMLStyles\", {\n      top: '',\n      left: ''\n    });\n    _defineProperty(this, \"_previousScrollPosition\", void 0);\n    _defineProperty(this, \"_isEnabled\", false);\n    _defineProperty(this, \"_document\", void 0);\n    this._viewportRuler = _viewportRuler;\n    this._document = document;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach() {}\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n      this._isEnabled = false;\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n  _canBeEnabled() {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n    const rootElement = this._document.documentElement;\n    const viewport = this._viewportRuler.getViewportSize();\n    return rootElement.scrollHeight > viewport.height || rootElement.scrollWidth > viewport.width;\n  }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n  return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n  constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n    _defineProperty(this, \"_scrollDispatcher\", void 0);\n    _defineProperty(this, \"_ngZone\", void 0);\n    _defineProperty(this, \"_viewportRuler\", void 0);\n    _defineProperty(this, \"_config\", void 0);\n    _defineProperty(this, \"_scrollSubscription\", null);\n    _defineProperty(this, \"_overlayRef\", void 0);\n    _defineProperty(this, \"_initialScrollPosition\", void 0);\n    /** Detaches the overlay ref and disables the scroll strategy. */\n    _defineProperty(this, \"_detach\", () => {\n      this.disable();\n      if (this._overlayRef.hasAttached()) {\n        this._ngZone.run(() => this._overlayRef.detach());\n      }\n    });\n    this._scrollDispatcher = _scrollDispatcher;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._config = _config;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n    const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n      return !scrollable || !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement);\n    }));\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() {}\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n    _defineProperty(this, \"_scrollDispatcher\", void 0);\n    _defineProperty(this, \"_viewportRuler\", void 0);\n    _defineProperty(this, \"_ngZone\", void 0);\n    _defineProperty(this, \"_config\", void 0);\n    _defineProperty(this, \"_scrollSubscription\", null);\n    _defineProperty(this, \"_overlayRef\", void 0);\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    this._config = _config;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {\n            width,\n            height\n          } = this._viewportRuler.getViewportSize();\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{\n            width,\n            height,\n            bottom: height,\n            right: width,\n            top: 0,\n            left: 0\n          }];\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n  constructor() {\n    _defineProperty(this, \"_scrollDispatcher\", inject(ScrollDispatcher));\n    _defineProperty(this, \"_viewportRuler\", inject(ViewportRuler));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    /** Do nothing on scroll. */\n    _defineProperty(this, \"noop\", () => new NoopScrollStrategy());\n    /**\n     * Close the overlay as soon as the user scrolls.\n     * @param config Configuration to be used inside the scroll strategy.\n     */\n    _defineProperty(this, \"close\", config => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config));\n    /** Block scrolling. */\n    _defineProperty(this, \"block\", () => new BlockScrollStrategy(this._viewportRuler, this._document));\n    /**\n     * Update the overlay's position on scroll.\n     * @param config Configuration to be used inside the scroll strategy.\n     * Allows debouncing the reposition calls.\n     */\n    _defineProperty(this, \"reposition\", config => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config));\n  }\n}\n_ScrollStrategyOptions = ScrollStrategyOptions;\n_defineProperty(ScrollStrategyOptions, \"\\u0275fac\", function _ScrollStrategyOptions_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ScrollStrategyOptions)();\n});\n_defineProperty(ScrollStrategyOptions, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _ScrollStrategyOptions,\n  factory: _ScrollStrategyOptions.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollStrategyOptions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n  constructor(config) {\n    /** Strategy with which to position the overlay. */\n    _defineProperty(this, \"positionStrategy\", void 0);\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    _defineProperty(this, \"scrollStrategy\", new NoopScrollStrategy());\n    /** Custom class to add to the overlay pane. */\n    _defineProperty(this, \"panelClass\", '');\n    /** Whether the overlay has a backdrop. */\n    _defineProperty(this, \"hasBackdrop\", false);\n    /** Custom class to add to the backdrop */\n    _defineProperty(this, \"backdropClass\", 'cdk-overlay-dark-backdrop');\n    /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n    _defineProperty(this, \"width\", void 0);\n    /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n    _defineProperty(this, \"height\", void 0);\n    /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n    _defineProperty(this, \"minWidth\", void 0);\n    /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n    _defineProperty(this, \"minHeight\", void 0);\n    /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n    _defineProperty(this, \"maxWidth\", void 0);\n    /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n    _defineProperty(this, \"maxHeight\", void 0);\n    /**\n     * Direction of the text in the overlay panel. If a `Directionality` instance\n     * is passed in, the overlay will handle changes to its value automatically.\n     */\n    _defineProperty(this, \"direction\", void 0);\n    /**\n     * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    _defineProperty(this, \"disposeOnNavigation\", false);\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config);\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key];\n        }\n      }\n    }\n  }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n  constructor(origin, overlay, /** Offset along the X axis. */\n  offsetX, /** Offset along the Y axis. */\n  offsetY, /** Class(es) to be applied to the panel while this position is active. */\n  panelClass) {\n    _defineProperty(this, \"offsetX\", void 0);\n    _defineProperty(this, \"offsetY\", void 0);\n    _defineProperty(this, \"panelClass\", void 0);\n    /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n    _defineProperty(this, \"originX\", void 0);\n    /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n    _defineProperty(this, \"originY\", void 0);\n    /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n    _defineProperty(this, \"overlayX\", void 0);\n    /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n    _defineProperty(this, \"overlayY\", void 0);\n    this.offsetX = offsetX;\n    this.offsetY = offsetY;\n    this.panelClass = panelClass;\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n  constructor() {\n    _defineProperty(this, \"isOriginClipped\", void 0);\n    _defineProperty(this, \"isOriginOutsideView\", void 0);\n    _defineProperty(this, \"isOverlayClipped\", void 0);\n    _defineProperty(this, \"isOverlayOutsideView\", void 0);\n  }\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n  constructor(/** The position used as a result of this change. */\n  connectionPair, /** @docs-private */\n  scrollableViewProperties) {\n    _defineProperty(this, \"connectionPair\", void 0);\n    _defineProperty(this, \"scrollableViewProperties\", void 0);\n    this.connectionPair = connectionPair;\n    this.scrollableViewProperties = scrollableViewProperties;\n  }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"top\", \"bottom\" or \"center\".`);\n  }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"start\", \"end\" or \"center\".`);\n  }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n  constructor() {\n    /** Currently attached overlays in the order they were attached. */\n    _defineProperty(this, \"_attachedOverlays\", []);\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_isAttached\", void 0);\n  }\n  ngOnDestroy() {\n    this.detach();\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n    this._attachedOverlays.push(overlayRef);\n  }\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef) {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n}\n_BaseOverlayDispatcher = BaseOverlayDispatcher;\n_defineProperty(BaseOverlayDispatcher, \"\\u0275fac\", function _BaseOverlayDispatcher_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BaseOverlayDispatcher)();\n});\n_defineProperty(BaseOverlayDispatcher, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _BaseOverlayDispatcher,\n  factory: _BaseOverlayDispatcher.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseOverlayDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_renderer\", inject(RendererFactory2).createRenderer(null, null));\n    _defineProperty(this, \"_cleanupKeydown\", void 0);\n    /** Keyboard event listener that will be attached to the body. */\n    _defineProperty(this, \"_keydownListener\", event => {\n      const overlays = this._attachedOverlays;\n      for (let i = overlays.length - 1; i > -1; i--) {\n        // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n        // We want to target the most recent overlay, rather than trying to match where the event came\n        // from, because some components might open an overlay, but keep focus on a trigger element\n        // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n        // because we don't want overlays that don't handle keyboard events to block the ones below\n        // them that do.\n        if (overlays[i]._keydownEvents.observers.length > 0) {\n          this._ngZone.run(() => overlays[i]._keydownEvents.next(event));\n          break;\n        }\n      }\n    });\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Lazily start dispatcher once first overlay is added\n    if (!this._isAttached) {\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupKeydown = this._renderer.listen('body', 'keydown', this._keydownListener);\n      });\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      var _this$_cleanupKeydown;\n      (_this$_cleanupKeydown = this._cleanupKeydown) === null || _this$_cleanupKeydown === void 0 || _this$_cleanupKeydown.call(this);\n      this._isAttached = false;\n    }\n  }\n}\n_OverlayKeyboardDispatcher = OverlayKeyboardDispatcher;\n_defineProperty(OverlayKeyboardDispatcher, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_OverlayKeyboardDispatcher_BaseFactory;\n  return function _OverlayKeyboardDispatcher_Factory(__ngFactoryType__) {\n    return (ɵ_OverlayKeyboardDispatcher_BaseFactory || (ɵ_OverlayKeyboardDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(_OverlayKeyboardDispatcher)))(__ngFactoryType__ || _OverlayKeyboardDispatcher);\n  };\n})());\n_defineProperty(OverlayKeyboardDispatcher, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _OverlayKeyboardDispatcher,\n  factory: _OverlayKeyboardDispatcher.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayKeyboardDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_renderer\", inject(RendererFactory2).createRenderer(null, null));\n    _defineProperty(this, \"_cursorOriginalValue\", void 0);\n    _defineProperty(this, \"_cursorStyleIsSet\", false);\n    _defineProperty(this, \"_pointerDownEventTarget\", void 0);\n    _defineProperty(this, \"_cleanups\", void 0);\n    /** Store pointerdown event target to track origin of click. */\n    _defineProperty(this, \"_pointerDownListener\", event => {\n      this._pointerDownEventTarget = _getEventTarget(event);\n    });\n    /** Click event listener that will be attached to the body propagate phase. */\n    _defineProperty(this, \"_clickListener\", event => {\n      const target = _getEventTarget(event);\n      // In case of a click event, we want to check the origin of the click\n      // (e.g. in case where a user starts a click inside the overlay and\n      // releases the click outside of it).\n      // This is done by using the event target of the preceding pointerdown event.\n      // Every click event caused by a pointer device has a preceding pointerdown\n      // event, unless the click was programmatically triggered (e.g. in a unit test).\n      const origin = event.type === 'click' && this._pointerDownEventTarget ? this._pointerDownEventTarget : target;\n      // Reset the stored pointerdown event target, to avoid having it interfere\n      // in subsequent events.\n      this._pointerDownEventTarget = null;\n      // We copy the array because the original may be modified asynchronously if the\n      // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n      // the for loop.\n      const overlays = this._attachedOverlays.slice();\n      // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n      // We want to target all overlays for which the click could be considered as outside click.\n      // As soon as we reach an overlay for which the click is not outside click we break off\n      // the loop.\n      for (let i = overlays.length - 1; i > -1; i--) {\n        const overlayRef = overlays[i];\n        if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n          continue;\n        }\n        // If it's a click inside the overlay, just break - we should do nothing\n        // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n        // and proceed with the next overlay\n        if (containsPierceShadowDom(overlayRef.overlayElement, target) || containsPierceShadowDom(overlayRef.overlayElement, origin)) {\n          break;\n        }\n        const outsidePointerEvents = overlayRef._outsidePointerEvents;\n        /** @breaking-change 14.0.0 _ngZone will be required. */\n        if (this._ngZone) {\n          this._ngZone.run(() => outsidePointerEvents.next(event));\n        } else {\n          outsidePointerEvents.next(event);\n        }\n      }\n    });\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n    if (!this._isAttached) {\n      const body = this._document.body;\n      const eventOptions = {\n        capture: true\n      };\n      this._cleanups = this._ngZone.runOutsideAngular(() => [_bindEventWithOptions(this._renderer, body, 'pointerdown', this._pointerDownListener, eventOptions), _bindEventWithOptions(this._renderer, body, 'click', this._clickListener, eventOptions), _bindEventWithOptions(this._renderer, body, 'auxclick', this._clickListener, eventOptions), _bindEventWithOptions(this._renderer, body, 'contextmenu', this._clickListener, eventOptions)]);\n      // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      var _this$_cleanups;\n      (_this$_cleanups = this._cleanups) === null || _this$_cleanups === void 0 || _this$_cleanups.forEach(cleanup => cleanup());\n      this._cleanups = undefined;\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        this._document.body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n      this._isAttached = false;\n    }\n  }\n}\n_OverlayOutsideClickDispatcher = OverlayOutsideClickDispatcher;\n_defineProperty(OverlayOutsideClickDispatcher, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_OverlayOutsideClickDispatcher_BaseFactory;\n  return function _OverlayOutsideClickDispatcher_Factory(__ngFactoryType__) {\n    return (ɵ_OverlayOutsideClickDispatcher_BaseFactory || (ɵ_OverlayOutsideClickDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(_OverlayOutsideClickDispatcher)))(__ngFactoryType__ || _OverlayOutsideClickDispatcher);\n  };\n})());\n_defineProperty(OverlayOutsideClickDispatcher, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _OverlayOutsideClickDispatcher,\n  factory: _OverlayOutsideClickDispatcher.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayOutsideClickDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent, child) {\n  const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n  let current = child;\n  while (current) {\n    if (current === parent) {\n      return true;\n    }\n    current = supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n  }\n  return false;\n}\nclass _CdkOverlayStyleLoader {}\n_CdkOverlayStyleLoader2 = _CdkOverlayStyleLoader;\n_defineProperty(_CdkOverlayStyleLoader, \"\\u0275fac\", function _CdkOverlayStyleLoader2_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkOverlayStyleLoader2)();\n});\n_defineProperty(_CdkOverlayStyleLoader, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _CdkOverlayStyleLoader2,\n  selectors: [[\"ng-component\"]],\n  hostAttrs: [\"cdk-overlay-style-loader\", \"\"],\n  decls: 0,\n  vars: 0,\n  template: function _CdkOverlayStyleLoader2_Template(rf, ctx) {},\n  styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkOverlayStyleLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'cdk-overlay-style-loader': ''\n      },\n      styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"]\n    }]\n  }], null, null);\n})();\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n  constructor() {\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_containerElement\", void 0);\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_styleLoader\", inject(_CdkPrivateStyleLoader));\n  }\n  ngOnDestroy() {\n    var _this$_containerEleme;\n    (_this$_containerEleme = this._containerElement) === null || _this$_containerEleme === void 0 || _this$_containerEleme.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    this._loadStyles();\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container';\n    // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n  /** Loads the structural styles necessary for the overlay to work. */\n  _loadStyles() {\n    this._styleLoader.load(_CdkOverlayStyleLoader);\n  }\n}\n_OverlayContainer = OverlayContainer;\n_defineProperty(OverlayContainer, \"\\u0275fac\", function _OverlayContainer_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _OverlayContainer)();\n});\n_defineProperty(OverlayContainer, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _OverlayContainer,\n  factory: _OverlayContainer.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Encapsulates the logic for attaching and detaching a backdrop. */\nclass BackdropRef {\n  constructor(document, _renderer, _ngZone, onClick) {\n    _defineProperty(this, \"_renderer\", void 0);\n    _defineProperty(this, \"_ngZone\", void 0);\n    _defineProperty(this, \"element\", void 0);\n    _defineProperty(this, \"_cleanupClick\", void 0);\n    _defineProperty(this, \"_cleanupTransitionEnd\", void 0);\n    _defineProperty(this, \"_fallbackTimeout\", void 0);\n    _defineProperty(this, \"dispose\", () => {\n      var _this$_cleanupClick, _this$_cleanupTransit;\n      clearTimeout(this._fallbackTimeout);\n      (_this$_cleanupClick = this._cleanupClick) === null || _this$_cleanupClick === void 0 || _this$_cleanupClick.call(this);\n      (_this$_cleanupTransit = this._cleanupTransitionEnd) === null || _this$_cleanupTransit === void 0 || _this$_cleanupTransit.call(this);\n      this._cleanupClick = this._cleanupTransitionEnd = this._fallbackTimeout = undefined;\n      this.element.remove();\n    });\n    this._renderer = _renderer;\n    this._ngZone = _ngZone;\n    this.element = document.createElement('div');\n    this.element.classList.add('cdk-overlay-backdrop');\n    this._cleanupClick = _renderer.listen(this.element, 'click', onClick);\n  }\n  detach() {\n    this._ngZone.runOutsideAngular(() => {\n      var _this$_cleanupTransit2;\n      const element = this.element;\n      clearTimeout(this._fallbackTimeout);\n      (_this$_cleanupTransit2 = this._cleanupTransitionEnd) === null || _this$_cleanupTransit2 === void 0 || _this$_cleanupTransit2.call(this);\n      this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this.dispose);\n      this._fallbackTimeout = setTimeout(this.dispose, 500);\n      // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n      // In this case we make it unclickable and we try to remove it after a delay.\n      element.style.pointerEvents = 'none';\n      element.classList.remove('cdk-overlay-backdrop-showing');\n    });\n  }\n}\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n  constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false, _injector, _renderer) {\n    _defineProperty(this, \"_portalOutlet\", void 0);\n    _defineProperty(this, \"_host\", void 0);\n    _defineProperty(this, \"_pane\", void 0);\n    _defineProperty(this, \"_config\", void 0);\n    _defineProperty(this, \"_ngZone\", void 0);\n    _defineProperty(this, \"_keyboardDispatcher\", void 0);\n    _defineProperty(this, \"_document\", void 0);\n    _defineProperty(this, \"_location\", void 0);\n    _defineProperty(this, \"_outsideClickDispatcher\", void 0);\n    _defineProperty(this, \"_animationsDisabled\", void 0);\n    _defineProperty(this, \"_injector\", void 0);\n    _defineProperty(this, \"_renderer\", void 0);\n    _defineProperty(this, \"_backdropClick\", new Subject());\n    _defineProperty(this, \"_attachments\", new Subject());\n    _defineProperty(this, \"_detachments\", new Subject());\n    _defineProperty(this, \"_positionStrategy\", void 0);\n    _defineProperty(this, \"_scrollStrategy\", void 0);\n    _defineProperty(this, \"_locationChanges\", Subscription.EMPTY);\n    _defineProperty(this, \"_backdropRef\", null);\n    /**\n     * Reference to the parent of the `_host` at the time it was detached. Used to restore\n     * the `_host` to its original position in the DOM when it gets re-attached.\n     */\n    _defineProperty(this, \"_previousHostParent\", void 0);\n    /** Stream of keydown events dispatched to this overlay. */\n    _defineProperty(this, \"_keydownEvents\", new Subject());\n    /** Stream of mouse outside events dispatched to this overlay. */\n    _defineProperty(this, \"_outsidePointerEvents\", new Subject());\n    _defineProperty(this, \"_renders\", new Subject());\n    _defineProperty(this, \"_afterRenderRef\", void 0);\n    /** Reference to the currently-running `afterNextRender` call. */\n    _defineProperty(this, \"_afterNextRenderRef\", void 0);\n    this._portalOutlet = _portalOutlet;\n    this._host = _host;\n    this._pane = _pane;\n    this._config = _config;\n    this._ngZone = _ngZone;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._document = _document;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsDisabled = _animationsDisabled;\n    this._injector = _injector;\n    this._renderer = _renderer;\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n      this._scrollStrategy.attach(this);\n    }\n    this._positionStrategy = _config.positionStrategy;\n    // Users could open the overlay from an `effect`, in which case we need to\n    // run the `afterRender` as `untracked`. We don't recommend that users do\n    // this, but we also don't want to break users who are doing it.\n    this._afterRenderRef = untracked(() => afterRender(() => {\n      this._renders.next();\n    }, {\n      injector: this._injector\n    }));\n  }\n  /** The overlay's HTML element */\n  get overlayElement() {\n    return this._pane;\n  }\n  /** The overlay's backdrop HTML element. */\n  get backdropElement() {\n    var _this$_backdropRef;\n    return ((_this$_backdropRef = this._backdropRef) === null || _this$_backdropRef === void 0 ? void 0 : _this$_backdropRef.element) || null;\n  }\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n  get hostElement() {\n    return this._host;\n  }\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal) {\n    var _this$_afterNextRende;\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n    const attachResult = this._portalOutlet.attach(portal);\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    }\n    // We need to clean this up ourselves, because we're passing in an\n    // `EnvironmentInjector` below which won't ever be destroyed.\n    // Otherwise it causes some callbacks to be retained (see #29696).\n    (_this$_afterNextRende = this._afterNextRenderRef) === null || _this$_afterNextRende === void 0 || _this$_afterNextRende.destroy();\n    // Update the position once the overlay is fully rendered before attempting to position it,\n    // as the position may depend on the size of the rendered content.\n    this._afterNextRenderRef = afterNextRender(() => {\n      // The overlay could've been detached before the callback executed.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    }, {\n      injector: this._injector\n    });\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    }\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n    this._outsideClickDispatcher.add(this);\n    // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n    if (typeof (attachResult === null || attachResult === void 0 ? void 0 : attachResult.onDestroy) === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n    return attachResult;\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach() {\n    if (!this.hasAttached()) {\n      return;\n    }\n    this.detachBackdrop();\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n    const detachmentResult = this._portalOutlet.detach();\n    // Only emit after everything is detached.\n    this._detachments.next();\n    // Remove this overlay from keyboard dispatcher tracking.\n    this._keyboardDispatcher.remove(this);\n    // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n    this._detachContentWhenEmpty();\n    this._locationChanges.unsubscribe();\n    this._outsideClickDispatcher.remove(this);\n    return detachmentResult;\n  }\n  /** Cleans up the overlay from the DOM. */\n  dispose() {\n    var _this$_backdropRef2, _this$_host, _this$_afterNextRende2;\n    const isAttached = this.hasAttached();\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._disposeScrollStrategy();\n    (_this$_backdropRef2 = this._backdropRef) === null || _this$_backdropRef2 === void 0 || _this$_backdropRef2.dispose();\n    this._locationChanges.unsubscribe();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n    this._outsidePointerEvents.complete();\n    this._outsideClickDispatcher.remove(this);\n    (_this$_host = this._host) === null || _this$_host === void 0 || _this$_host.remove();\n    (_this$_afterNextRende2 = this._afterNextRenderRef) === null || _this$_afterNextRende2 === void 0 || _this$_afterNextRende2.destroy();\n    this._previousHostParent = this._pane = this._host = this._backdropRef = null;\n    if (isAttached) {\n      this._detachments.next();\n    }\n    this._detachments.complete();\n    this._afterRenderRef.destroy();\n    this._renders.complete();\n  }\n  /** Whether the overlay has attached content. */\n  hasAttached() {\n    return this._portalOutlet.hasAttached();\n  }\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick() {\n    return this._backdropClick;\n  }\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments() {\n    return this._attachments;\n  }\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments() {\n    return this._detachments;\n  }\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents() {\n    return this._keydownEvents;\n  }\n  /** Gets an observable of pointer events targeted outside this overlay. */\n  outsidePointerEvents() {\n    return this._outsidePointerEvents;\n  }\n  /** Gets the current overlay configuration, which is immutable. */\n  getConfig() {\n    return this._config;\n  }\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition() {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n  /** Switches to a new position strategy and updates the overlay position. */\n  updatePositionStrategy(strategy) {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._positionStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig) {\n    this._config = _objectSpread(_objectSpread({}, this._config), sizeConfig);\n    this._updateElementSize();\n  }\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir) {\n    this._config = _objectSpread(_objectSpread({}, this._config), {}, {\n      direction: dir\n    });\n    this._updateElementDirection();\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n  getDirection() {\n    const direction = this._config.direction;\n    if (!direction) {\n      return 'ltr';\n    }\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n  /** Switches to a new scroll strategy. */\n  updateScrollStrategy(strategy) {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n    this._disposeScrollStrategy();\n    this._scrollStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n  /** Updates the text direction of the overlay panel. */\n  _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n  /** Updates the size of the overlay element based on the overlay config. */\n  _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n    const style = this._pane.style;\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n  /** Toggles the pointer events for the overlay pane element. */\n  _togglePointerEvents(enablePointer) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n  /** Attaches a backdrop for this overlay. */\n  _attachBackdrop() {\n    var _this$_backdropRef3;\n    const showingClass = 'cdk-overlay-backdrop-showing';\n    (_this$_backdropRef3 = this._backdropRef) === null || _this$_backdropRef3 === void 0 || _this$_backdropRef3.dispose();\n    this._backdropRef = new BackdropRef(this._document, this._renderer, this._ngZone, event => {\n      this._backdropClick.next(event);\n    });\n    if (this._animationsDisabled) {\n      this._backdropRef.element.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropRef.element, this._config.backdropClass, true);\n    }\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._host.parentElement.insertBefore(this._backdropRef.element, this._host);\n    // Add class to fade-in the backdrop after one frame.\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          var _this$_backdropRef4;\n          return (_this$_backdropRef4 = this._backdropRef) === null || _this$_backdropRef4 === void 0 ? void 0 : _this$_backdropRef4.element.classList.add(showingClass);\n        });\n      });\n    } else {\n      this._backdropRef.element.classList.add(showingClass);\n    }\n  }\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode.appendChild(this._host);\n    }\n  }\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop() {\n    if (this._animationsDisabled) {\n      var _this$_backdropRef5;\n      (_this$_backdropRef5 = this._backdropRef) === null || _this$_backdropRef5 === void 0 || _this$_backdropRef5.dispose();\n      this._backdropRef = null;\n    } else {\n      var _this$_backdropRef6;\n      (_this$_backdropRef6 = this._backdropRef) === null || _this$_backdropRef6 === void 0 || _this$_backdropRef6.detach();\n    }\n  }\n  /** Toggles a single CSS class or an array of classes on an element. */\n  _toggleClasses(element, cssClasses, isAdd) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n  /** Detaches the overlay content next time the zone stabilizes. */\n  _detachContentWhenEmpty() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._renders.pipe(takeUntil(merge(this._attachments, this._detachments))).subscribe(() => {\n        // Needs a couple of checks for the pane and host, because\n        // they may have been removed by the time the zone stabilizes.\n        if (!this._pane || !this._host || this._pane.children.length === 0) {\n          if (this._pane && this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, false);\n          }\n          if (this._host && this._host.parentElement) {\n            this._previousHostParent = this._host.parentElement;\n            this._host.remove();\n          }\n          subscription.unsubscribe();\n        }\n      });\n    });\n  }\n  /** Disposes of a scroll strategy. */\n  _disposeScrollStrategy() {\n    var _scrollStrategy$detac;\n    const scrollStrategy = this._scrollStrategy;\n    scrollStrategy === null || scrollStrategy === void 0 || scrollStrategy.disable();\n    scrollStrategy === null || scrollStrategy === void 0 || (_scrollStrategy$detac = scrollStrategy.detach) === null || _scrollStrategy$detac === void 0 || _scrollStrategy$detac.call(scrollStrategy);\n  }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions() {\n    return this._preferredPositions;\n  }\n  constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n    _defineProperty(this, \"_viewportRuler\", void 0);\n    _defineProperty(this, \"_document\", void 0);\n    _defineProperty(this, \"_platform\", void 0);\n    _defineProperty(this, \"_overlayContainer\", void 0);\n    /** The overlay to which this strategy is attached. */\n    _defineProperty(this, \"_overlayRef\", void 0);\n    /** Whether we're performing the very first positioning of the overlay. */\n    _defineProperty(this, \"_isInitialRender\", void 0);\n    /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n    _defineProperty(this, \"_lastBoundingBoxSize\", {\n      width: 0,\n      height: 0\n    });\n    /** Whether the overlay was pushed in a previous positioning. */\n    _defineProperty(this, \"_isPushed\", false);\n    /** Whether the overlay can be pushed on-screen on the initial open. */\n    _defineProperty(this, \"_canPush\", true);\n    /** Whether the overlay can grow via flexible width/height after the initial open. */\n    _defineProperty(this, \"_growAfterOpen\", false);\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    _defineProperty(this, \"_hasFlexibleDimensions\", true);\n    /** Whether the overlay position is locked. */\n    _defineProperty(this, \"_positionLocked\", false);\n    /** Cached origin dimensions */\n    _defineProperty(this, \"_originRect\", void 0);\n    /** Cached overlay dimensions */\n    _defineProperty(this, \"_overlayRect\", void 0);\n    /** Cached viewport dimensions */\n    _defineProperty(this, \"_viewportRect\", void 0);\n    /** Cached container dimensions */\n    _defineProperty(this, \"_containerRect\", void 0);\n    /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n    _defineProperty(this, \"_viewportMargin\", 0);\n    /** The Scrollable containers used to check scrollable view properties on position change. */\n    _defineProperty(this, \"_scrollables\", []);\n    /** Ordered list of preferred positions, from most to least desirable. */\n    _defineProperty(this, \"_preferredPositions\", []);\n    /** The origin element against which the overlay will be positioned. */\n    _defineProperty(this, \"_origin\", void 0);\n    /** The overlay pane element. */\n    _defineProperty(this, \"_pane\", void 0);\n    /** Whether the strategy has been disposed of already. */\n    _defineProperty(this, \"_isDisposed\", void 0);\n    /**\n     * Parent element for the overlay panel used to constrain the overlay panel's size to fit\n     * within the viewport.\n     */\n    _defineProperty(this, \"_boundingBox\", void 0);\n    /** The last position to have been calculated as the best fit position. */\n    _defineProperty(this, \"_lastPosition\", void 0);\n    /** The last calculated scroll visibility. Only tracked  */\n    _defineProperty(this, \"_lastScrollVisibility\", void 0);\n    /** Subject that emits whenever the position changes. */\n    _defineProperty(this, \"_positionChanges\", new Subject());\n    /** Subscription to viewport size changes. */\n    _defineProperty(this, \"_resizeSubscription\", Subscription.EMPTY);\n    /** Default offset for the overlay along the x axis. */\n    _defineProperty(this, \"_offsetX\", 0);\n    /** Default offset for the overlay along the y axis. */\n    _defineProperty(this, \"_offsetY\", 0);\n    /** Selector to be used when finding the elements on which to set the transform origin. */\n    _defineProperty(this, \"_transformOriginSelector\", void 0);\n    /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n    _defineProperty(this, \"_appliedPanelClasses\", []);\n    /** Amount by which the overlay was pushed in each axis during the last time it was positioned. */\n    _defineProperty(this, \"_previousPushAmount\", void 0);\n    /** Observable sequence of position changes. */\n    _defineProperty(this, \"positionChanges\", this._positionChanges);\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n    this.setOrigin(connectedTo);\n  }\n  /** Attaches this position strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && overlayRef !== this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n    this._validatePositions();\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n  apply() {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n    this._clearPanelClasses();\n    this._resetOverlayElementStyles();\n    this._resetBoundingBoxStyles();\n    // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect;\n    // Positions where the overlay will fit with flexible dimensions.\n    const flexibleFits = [];\n    // Fallback if none of the preferred positions fit within the viewport.\n    let fallback;\n    // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n      // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n      // Calculate how well the overlay would fit into the viewport with this point.\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n      // If the overlay, without any further work, fits into the viewport, use this position.\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n        this._applyPosition(pos, originPoint);\n        return;\n      }\n      // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos)\n        });\n        continue;\n      }\n      // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {\n          overlayFit,\n          overlayPoint,\n          originPoint,\n          position: pos,\n          overlayRect\n        };\n      }\n    }\n    // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n    if (flexibleFits.length) {\n      let bestFit = null;\n      let bestScore = -1;\n      for (const fit of flexibleFits) {\n        const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n      this._isPushed = false;\n      this._applyPosition(bestFit.position, bestFit.origin);\n      return;\n    }\n    // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n      this._applyPosition(fallback.position, fallback.originPoint);\n      return;\n    }\n    // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n    this._applyPosition(fallback.position, fallback.originPoint);\n  }\n  detach() {\n    this._clearPanelClasses();\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Cleanup after the element gets destroyed. */\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    }\n    // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: ''\n      });\n    }\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n    this.detach();\n    this._positionChanges.complete();\n    this._overlayRef = this._boundingBox = null;\n    this._isDisposed = true;\n  }\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  reapplyLastPosition() {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    const lastPosition = this._lastPosition;\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables) {\n    this._scrollables = scrollables;\n    return this;\n  }\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n  withPositions(positions) {\n    this._preferredPositions = positions;\n    // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n    if (positions.indexOf(this._lastPosition) === -1) {\n      this._lastPosition = null;\n    }\n    this._validatePositions();\n    return this;\n  }\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n  withViewportMargin(margin) {\n    this._viewportMargin = margin;\n    return this;\n  }\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n  withFlexibleDimensions(flexibleDimensions = true) {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n  withGrowAfterOpen(growAfterOpen = true) {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  withPush(canPush = true) {\n    this._canPush = canPush;\n    return this;\n  }\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked = true) {\n    this._positionLocked = isLocked;\n    return this;\n  }\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n  setOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n  withDefaultOffsetX(offset) {\n    this._offsetX = offset;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n  withDefaultOffsetY(offset) {\n    this._offsetY = offset;\n    return this;\n  }\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n  withTransformOriginOn(selector) {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n  _getOriginPoint(originRect, containerRect, pos) {\n    let x;\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    }\n    // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n    let y;\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n    // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n  _getOverlayPoint(originPoint, overlayRect, pos) {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n    let overlayStartY;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n    // The (x, y) coordinates of the overlay.\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY\n    };\n  }\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n  _getOverlayFit(point, rawOverlayRect, viewport, position) {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {\n      x,\n      y\n    } = point;\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    // Account for the offsets since they could push the overlay out of the viewport.\n    if (offsetX) {\n      x += offsetX;\n    }\n    if (offsetY) {\n      y += offsetY;\n    }\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height;\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n    let visibleArea = visibleWidth * visibleHeight;\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width\n    };\n  }\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n  _canFitWithFlexibleDimensions(fit, point, viewport) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n      const verticalFit = fit.fitsInViewportVertically || minHeight != null && minHeight <= availableHeight;\n      const horizontalFit = fit.fitsInViewportHorizontally || minWidth != null && minWidth <= availableWidth;\n      return verticalFit && horizontalFit;\n    }\n    return false;\n  }\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n  _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y\n      };\n    }\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect;\n    // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n    // Amount by which to push the overlay in each axis such that it remains on-screen.\n    let pushX = 0;\n    let pushY = 0;\n    // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n    this._previousPushAmount = {\n      x: pushX,\n      y: pushY\n    };\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY\n    };\n  }\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n  _applyPosition(position, originPoint) {\n    this._setTransformOrigin(position);\n    this._setOverlayElementStyles(originPoint, position);\n    this._setBoundingBoxStyles(originPoint, position);\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    }\n    // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n    if (this._positionChanges.observers.length) {\n      const scrollVisibility = this._getScrollVisibility();\n      // We're recalculating on scroll, but we only want to emit if anything\n      // changed since downstream code might be hitting the `NgZone`.\n      if (position !== this._lastPosition || !this._lastScrollVisibility || !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)) {\n        const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n        this._positionChanges.next(changeEvent);\n      }\n      this._lastScrollVisibility = scrollVisibility;\n    }\n    // Save the last connected position in case the position needs to be re-calculated.\n    this._lastPosition = position;\n    this._isInitialRender = false;\n  }\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n  _setTransformOrigin(position) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n    const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n    let xOrigin;\n    let yOrigin = position.overlayY;\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n  _calculateBoundingBoxRect(origin, position) {\n    const viewport = this._viewportRect;\n    const isRtl = this._isRtl();\n    let height, top, bottom;\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n      const previousHeight = this._lastBoundingBoxSize.height;\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    }\n    // The overlay is opening 'right-ward' (the content flows to the right).\n    const isBoundedByRightViewportEdge = position.overlayX === 'start' && !isRtl || position.overlayX === 'end' && isRtl;\n    // The overlay is opening 'left-ward' (the content flows to the left).\n    const isBoundedByLeftViewportEdge = position.overlayX === 'end' && !isRtl || position.overlayX === 'start' && isRtl;\n    let width, left, right;\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin * 2;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n      const previousWidth = this._lastBoundingBoxSize.width;\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n    return {\n      top: top,\n      left: left,\n      bottom: bottom,\n      right: right,\n      width,\n      height\n    };\n  }\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n  _setBoundingBoxStyles(origin, position) {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n    // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n    const styles = {};\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right);\n      // Push the pane content towards the proper direction.\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n    this._lastBoundingBoxSize = boundingBoxRect;\n    extendStyles(this._boundingBox.style, styles);\n  }\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n  _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: ''\n    });\n  }\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n  _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: ''\n    });\n  }\n  /** Sets positioning styles to the overlay element. */\n  _setOverlayElementStyles(originPoint, position) {\n    const styles = {};\n    const hasExactPosition = this._hasExactPosition();\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n    const config = this._overlayRef.getConfig();\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    }\n    // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n    let transformString = '';\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n    styles.transform = transformString.trim();\n    // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n    extendStyles(this._pane.style, styles);\n  }\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayY(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {\n      top: '',\n      bottom: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n    return styles;\n  }\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayX(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {\n      left: '',\n      right: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty;\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    }\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n    return styles;\n  }\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  _getScrollVisibility() {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n    const overlayBounds = this._pane.getBoundingClientRect();\n    // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds)\n    };\n  }\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n  _subtractOverflows(length, ...overflows) {\n    return overflows.reduce((currentValue, currentOverflow) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n  _getNarrowedViewportRect() {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement.clientWidth;\n    const height = this._document.documentElement.clientHeight;\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin\n    };\n  }\n  /** Whether the we're dealing with an RTL context */\n  _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n  /** Determines whether the overlay uses exact or flexible positioning. */\n  _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n  /** Retrieves the offset of a position along the x or y axis. */\n  _getOffset(position, axis) {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n  /** Validates that the current position match the expected values. */\n  _validatePositions() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      }\n      // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n  _addPanelClasses(cssClasses) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n  _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n      this._appliedPanelClasses = [];\n    }\n  }\n  /** Returns the DOMRect of the current origin. */\n  _getOriginRect() {\n    const origin = this._origin;\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n  return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n  return input || null;\n}\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height)\n  };\n}\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a, b) {\n  if (a === b) {\n    return true;\n  }\n  return a.isOriginClipped === b.isOriginClipped && a.isOriginOutsideView === b.isOriginOutsideView && a.isOverlayClipped === b.isOverlayClipped && a.isOverlayOutsideView === b.isOverlayOutsideView;\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [{\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n  constructor() {\n    /** The overlay to which this strategy is attached. */\n    _defineProperty(this, \"_overlayRef\", void 0);\n    _defineProperty(this, \"_cssPosition\", 'static');\n    _defineProperty(this, \"_topOffset\", '');\n    _defineProperty(this, \"_bottomOffset\", '');\n    _defineProperty(this, \"_alignItems\", '');\n    _defineProperty(this, \"_xPosition\", '');\n    _defineProperty(this, \"_xOffset\", '');\n    _defineProperty(this, \"_width\", '');\n    _defineProperty(this, \"_height\", '');\n    _defineProperty(this, \"_isDisposed\", false);\n  }\n  attach(overlayRef) {\n    const config = overlayRef.getConfig();\n    this._overlayRef = overlayRef;\n    if (this._width && !config.width) {\n      overlayRef.updateSize({\n        width: this._width\n      });\n    }\n    if (this._height && !config.height) {\n      overlayRef.updateSize({\n        height: this._height\n      });\n    }\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value = '') {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value = '') {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  start(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  end(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  width(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        width: value\n      });\n    } else {\n      this._width = value;\n    }\n    return this;\n  }\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  height(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        height: value\n      });\n    } else {\n      this._height = value;\n    }\n    return this;\n  }\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset = '') {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset = '') {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n  apply() {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n    const config = this._overlayRef.getConfig();\n    const {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    } = config;\n    const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') && (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically = (height === '100%' || height === '100vh') && (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n  dispose() {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent = parentStyles.alignItems = styles.marginTop = styles.marginBottom = styles.marginLeft = styles.marginRight = styles.position = '';\n    this._overlayRef = null;\n    this._isDisposed = true;\n  }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n  constructor() {\n    _defineProperty(this, \"_viewportRuler\", inject(ViewportRuler));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_overlayContainer\", inject(OverlayContainer));\n  }\n  /**\n   * Creates a global position strategy.\n   */\n  global() {\n    return new GlobalPositionStrategy();\n  }\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n  flexibleConnectedTo(origin) {\n    return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n  }\n}\n_OverlayPositionBuilder = OverlayPositionBuilder;\n_defineProperty(OverlayPositionBuilder, \"\\u0275fac\", function _OverlayPositionBuilder_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _OverlayPositionBuilder)();\n});\n_defineProperty(OverlayPositionBuilder, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _OverlayPositionBuilder,\n  factory: _OverlayPositionBuilder.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPositionBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n  constructor() {\n    _defineProperty(this, \"scrollStrategies\", inject(ScrollStrategyOptions));\n    _defineProperty(this, \"_overlayContainer\", inject(OverlayContainer));\n    _defineProperty(this, \"_positionBuilder\", inject(OverlayPositionBuilder));\n    _defineProperty(this, \"_keyboardDispatcher\", inject(OverlayKeyboardDispatcher));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_directionality\", inject(Directionality));\n    _defineProperty(this, \"_location\", inject(Location));\n    _defineProperty(this, \"_outsideClickDispatcher\", inject(OverlayOutsideClickDispatcher));\n    _defineProperty(this, \"_animationsModuleType\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }));\n    _defineProperty(this, \"_idGenerator\", inject(_IdGenerator));\n    _defineProperty(this, \"_renderer\", inject(RendererFactory2).createRenderer(null, null));\n    _defineProperty(this, \"_appRef\", void 0);\n    _defineProperty(this, \"_styleLoader\", inject(_CdkPrivateStyleLoader));\n  }\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config) {\n    // This is done in the overlay container as well, but we have it here\n    // since it's common to mock out the overlay container in tests.\n    this._styleLoader.load(_CdkOverlayStyleLoader);\n    const host = this._createHostElement();\n    const pane = this._createPaneElement(host);\n    const portalOutlet = this._createPortalOutlet(pane);\n    const overlayConfig = new OverlayConfig(config);\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n    return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations', this._injector.get(EnvironmentInjector), this._renderer);\n  }\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position() {\n    return this._positionBuilder;\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  _createPaneElement(host) {\n    const pane = this._document.createElement('div');\n    pane.id = this._idGenerator.getId('cdk-overlay-');\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n    return pane;\n  }\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n  _createHostElement() {\n    const host = this._document.createElement('div');\n    this._overlayContainer.getContainerElement().appendChild(host);\n    return host;\n  }\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  _createPortalOutlet(pane) {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get(ApplicationRef);\n    }\n    return new DomPortalOutlet(pane, null, this._appRef, this._injector, this._document);\n  }\n}\n_Overlay = Overlay;\n_defineProperty(Overlay, \"\\u0275fac\", function _Overlay_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Overlay)();\n});\n_defineProperty(Overlay, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _Overlay,\n  factory: _Overlay.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n  constructor() {\n    _defineProperty(this, \"elementRef\", inject(ElementRef));\n  }\n}\n_CdkOverlayOrigin = CdkOverlayOrigin;\n_defineProperty(CdkOverlayOrigin, \"\\u0275fac\", function _CdkOverlayOrigin_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkOverlayOrigin)();\n});\n_defineProperty(CdkOverlayOrigin, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkOverlayOrigin,\n  selectors: [[\"\", \"cdk-overlay-origin\", \"\"], [\"\", \"overlay-origin\", \"\"], [\"\", \"cdkOverlayOrigin\", \"\"]],\n  exportAs: [\"cdkOverlayOrigin\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkOverlayOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n      exportAs: 'cdkOverlayOrigin'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  get offsetX() {\n    return this._offsetX;\n  }\n  set offsetX(offsetX) {\n    this._offsetX = offsetX;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  get offsetY() {\n    return this._offsetY;\n  }\n  set offsetY(offsetY) {\n    this._offsetY = offsetY;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The width of the overlay panel. */\n\n  /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n  get disposeOnNavigation() {\n    return this._disposeOnNavigation;\n  }\n  set disposeOnNavigation(value) {\n    this._disposeOnNavigation = value;\n  }\n  /** Event emitted when the backdrop is clicked. */\n\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n  constructor() {\n    _defineProperty(this, \"_overlay\", inject(Overlay));\n    _defineProperty(this, \"_dir\", inject(Directionality, {\n      optional: true\n    }));\n    _defineProperty(this, \"_overlayRef\", void 0);\n    _defineProperty(this, \"_templatePortal\", void 0);\n    _defineProperty(this, \"_backdropSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_attachSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_detachSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_positionSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_offsetX\", void 0);\n    _defineProperty(this, \"_offsetY\", void 0);\n    _defineProperty(this, \"_position\", void 0);\n    _defineProperty(this, \"_scrollStrategyFactory\", inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY));\n    _defineProperty(this, \"_disposeOnNavigation\", false);\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    /** Origin for the connected overlay. */\n    _defineProperty(this, \"origin\", void 0);\n    /** Registered connected position pairs. */\n    _defineProperty(this, \"positions\", void 0);\n    /**\n     * This input overrides the positions input if specified. It lets users pass\n     * in arbitrary positioning strategies.\n     */\n    _defineProperty(this, \"positionStrategy\", void 0);\n    _defineProperty(this, \"width\", void 0);\n    /** The height of the overlay panel. */\n    _defineProperty(this, \"height\", void 0);\n    /** The min width of the overlay panel. */\n    _defineProperty(this, \"minWidth\", void 0);\n    /** The min height of the overlay panel. */\n    _defineProperty(this, \"minHeight\", void 0);\n    /** The custom class to be set on the backdrop element. */\n    _defineProperty(this, \"backdropClass\", void 0);\n    /** The custom class to add to the overlay pane element. */\n    _defineProperty(this, \"panelClass\", void 0);\n    /** Margin between the overlay and the viewport edges. */\n    _defineProperty(this, \"viewportMargin\", 0);\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    _defineProperty(this, \"scrollStrategy\", void 0);\n    /** Whether the overlay is open. */\n    _defineProperty(this, \"open\", false);\n    /** Whether the overlay can be closed by user interaction. */\n    _defineProperty(this, \"disableClose\", false);\n    /** CSS selector which to set the transform origin. */\n    _defineProperty(this, \"transformOriginSelector\", void 0);\n    /** Whether or not the overlay should attach a backdrop. */\n    _defineProperty(this, \"hasBackdrop\", false);\n    /** Whether or not the overlay should be locked when scrolling. */\n    _defineProperty(this, \"lockPosition\", false);\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    _defineProperty(this, \"flexibleDimensions\", false);\n    /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n    _defineProperty(this, \"growAfterOpen\", false);\n    /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    _defineProperty(this, \"push\", false);\n    _defineProperty(this, \"backdropClick\", new EventEmitter());\n    /** Event emitted when the position has changed. */\n    _defineProperty(this, \"positionChange\", new EventEmitter());\n    /** Event emitted when the overlay has been attached. */\n    _defineProperty(this, \"attach\", new EventEmitter());\n    /** Event emitted when the overlay has been detached. */\n    _defineProperty(this, \"detach\", new EventEmitter());\n    /** Emits when there are keyboard events that are targeted at the overlay. */\n    _defineProperty(this, \"overlayKeydown\", new EventEmitter());\n    /** Emits when there are mouse outside click events that are targeted at the overlay. */\n    _defineProperty(this, \"overlayOutsideClick\", new EventEmitter());\n    const templateRef = inject(TemplateRef);\n    const viewContainerRef = inject(ViewContainerRef);\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n  /** The associated overlay reference. */\n  get overlayRef() {\n    return this._overlayRef;\n  }\n  /** The element's layout direction. */\n  get dir() {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n  ngOnDestroy() {\n    var _this$_overlayRef;\n    this._attachSubscription.unsubscribe();\n    this._detachSubscription.unsubscribe();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    (_this$_overlayRef = this._overlayRef) === null || _this$_overlayRef === void 0 || _this$_overlayRef.dispose();\n  }\n  ngOnChanges(changes) {\n    if (this._position) {\n      var _this$_overlayRef2;\n      this._updatePositionStrategy(this._position);\n      (_this$_overlayRef2 = this._overlayRef) === null || _this$_overlayRef2 === void 0 || _this$_overlayRef2.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight\n      });\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n    if (changes['open']) {\n      this.open ? this.attachOverlay() : this.detachOverlay();\n    }\n  }\n  /** Creates an overlay */\n  _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n    const overlayRef = this._overlayRef = this._overlay.create(this._buildConfig());\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe(event => {\n      this.overlayKeydown.next(event);\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.detachOverlay();\n      }\n    });\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      const origin = this._getOriginElement();\n      const target = _getEventTarget(event);\n      if (!origin || origin !== target && !origin.contains(target)) {\n        this.overlayOutsideClick.next(event);\n      }\n    });\n  }\n  /** Builds the overlay config based on the directive's inputs */\n  _buildConfig() {\n    const positionStrategy = this._position = this.positionStrategy || this._createPositionStrategy();\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir || 'ltr',\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop,\n      disposeOnNavigation: this.disposeOnNavigation\n    });\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n    return overlayConfig;\n  }\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n  _updatePositionStrategy(positionStrategy) {\n    const positions = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined\n    }));\n    return positionStrategy.setOrigin(this._getOrigin()).withPositions(positions).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector);\n  }\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  _createPositionStrategy() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getOrigin());\n    this._updatePositionStrategy(strategy);\n    return strategy;\n  }\n  _getOrigin() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n  _getOriginElement() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef.nativeElement;\n    }\n    if (this.origin instanceof ElementRef) {\n      return this.origin.nativeElement;\n    }\n    if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n      return this.origin;\n    }\n    return null;\n  }\n  /** Attaches the overlay. */\n  attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n    this._positionSubscription.unsubscribe();\n    // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges.pipe(takeWhile(() => this.positionChange.observers.length > 0)).subscribe(position => {\n        this._ngZone.run(() => this.positionChange.emit(position));\n        if (this.positionChange.observers.length === 0) {\n          this._positionSubscription.unsubscribe();\n        }\n      });\n    }\n    this.open = true;\n  }\n  /** Detaches the overlay. */\n  detachOverlay() {\n    var _this$_overlayRef3;\n    (_this$_overlayRef3 = this._overlayRef) === null || _this$_overlayRef3 === void 0 || _this$_overlayRef3.detach();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    this.open = false;\n  }\n}\n_CdkConnectedOverlay = CdkConnectedOverlay;\n_defineProperty(CdkConnectedOverlay, \"\\u0275fac\", function _CdkConnectedOverlay_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkConnectedOverlay)();\n});\n_defineProperty(CdkConnectedOverlay, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkConnectedOverlay,\n  selectors: [[\"\", \"cdk-connected-overlay\", \"\"], [\"\", \"connected-overlay\", \"\"], [\"\", \"cdkConnectedOverlay\", \"\"]],\n  inputs: {\n    origin: [0, \"cdkConnectedOverlayOrigin\", \"origin\"],\n    positions: [0, \"cdkConnectedOverlayPositions\", \"positions\"],\n    positionStrategy: [0, \"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"],\n    offsetX: [0, \"cdkConnectedOverlayOffsetX\", \"offsetX\"],\n    offsetY: [0, \"cdkConnectedOverlayOffsetY\", \"offsetY\"],\n    width: [0, \"cdkConnectedOverlayWidth\", \"width\"],\n    height: [0, \"cdkConnectedOverlayHeight\", \"height\"],\n    minWidth: [0, \"cdkConnectedOverlayMinWidth\", \"minWidth\"],\n    minHeight: [0, \"cdkConnectedOverlayMinHeight\", \"minHeight\"],\n    backdropClass: [0, \"cdkConnectedOverlayBackdropClass\", \"backdropClass\"],\n    panelClass: [0, \"cdkConnectedOverlayPanelClass\", \"panelClass\"],\n    viewportMargin: [0, \"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"],\n    scrollStrategy: [0, \"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"],\n    open: [0, \"cdkConnectedOverlayOpen\", \"open\"],\n    disableClose: [0, \"cdkConnectedOverlayDisableClose\", \"disableClose\"],\n    transformOriginSelector: [0, \"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"],\n    hasBackdrop: [2, \"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\", booleanAttribute],\n    lockPosition: [2, \"cdkConnectedOverlayLockPosition\", \"lockPosition\", booleanAttribute],\n    flexibleDimensions: [2, \"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\", booleanAttribute],\n    growAfterOpen: [2, \"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\", booleanAttribute],\n    push: [2, \"cdkConnectedOverlayPush\", \"push\", booleanAttribute],\n    disposeOnNavigation: [2, \"cdkConnectedOverlayDisposeOnNavigation\", \"disposeOnNavigation\", booleanAttribute]\n  },\n  outputs: {\n    backdropClick: \"backdropClick\",\n    positionChange: \"positionChange\",\n    attach: \"attach\",\n    detach: \"detach\",\n    overlayKeydown: \"overlayKeydown\",\n    overlayOutsideClick: \"overlayOutsideClick\"\n  },\n  exportAs: [\"cdkConnectedOverlay\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkConnectedOverlay, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n      exportAs: 'cdkConnectedOverlay'\n    }]\n  }], () => [], {\n    origin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOrigin']\n    }],\n    positions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositions']\n    }],\n    positionStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositionStrategy']\n    }],\n    offsetX: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetX']\n    }],\n    offsetY: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetY']\n    }],\n    width: [{\n      type: Input,\n      args: ['cdkConnectedOverlayWidth']\n    }],\n    height: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHeight']\n    }],\n    minWidth: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinHeight']\n    }],\n    backdropClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayBackdropClass']\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPanelClass']\n    }],\n    viewportMargin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayViewportMargin']\n    }],\n    scrollStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayScrollStrategy']\n    }],\n    open: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOpen']\n    }],\n    disableClose: [{\n      type: Input,\n      args: ['cdkConnectedOverlayDisableClose']\n    }],\n    transformOriginSelector: [{\n      type: Input,\n      args: ['cdkConnectedOverlayTransformOriginOn']\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayHasBackdrop',\n        transform: booleanAttribute\n      }]\n    }],\n    lockPosition: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayLockPosition',\n        transform: booleanAttribute\n      }]\n    }],\n    flexibleDimensions: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayFlexibleDimensions',\n        transform: booleanAttribute\n      }]\n    }],\n    growAfterOpen: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayGrowAfterOpen',\n        transform: booleanAttribute\n      }]\n    }],\n    push: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayPush',\n        transform: booleanAttribute\n      }]\n    }],\n    disposeOnNavigation: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayDisposeOnNavigation',\n        transform: booleanAttribute\n      }]\n    }],\n    backdropClick: [{\n      type: Output\n    }],\n    positionChange: [{\n      type: Output\n    }],\n    attach: [{\n      type: Output\n    }],\n    detach: [{\n      type: Output\n    }],\n    overlayKeydown: [{\n      type: Output\n    }],\n    overlayOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nclass OverlayModule {}\n_OverlayModule = OverlayModule;\n_defineProperty(OverlayModule, \"\\u0275fac\", function _OverlayModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _OverlayModule)();\n});\n_defineProperty(OverlayModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _OverlayModule,\n  imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n  exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule]\n}));\n_defineProperty(OverlayModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n  imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { BlockScrollStrategy as B, CdkOverlayOrigin as C, FlexibleConnectedPositionStrategy as F, GlobalPositionStrategy as G, NoopScrollStrategy as N, OverlayContainer as O, RepositionScrollStrategy as R, STANDARD_DROPDOWN_ADJACENT_POSITIONS as S, Overlay as a, CdkConnectedOverlay as b, OverlayRef as c, OverlayPositionBuilder as d, STANDARD_DROPDOWN_BELOW_POSITIONS as e, OverlayConfig as f, ConnectionPositionPair as g, ScrollingVisibility as h, ConnectedOverlayPositionChange as i, validateHorizontalPosition as j, ScrollStrategyOptions as k, CloseScrollStrategy as l, OverlayModule as m, OverlayOutsideClickDispatcher as n, OverlayKeyboardDispatcher as o, validateVerticalPosition as v };", "map": {"version": 3, "names": ["i0", "inject", "NgZone", "Injectable", "RendererFactory2", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "untracked", "afterRender", "afterNextRender", "ElementRef", "Injector", "ANIMATION_MODULE_TYPE", "EnvironmentInjector", "ApplicationRef", "InjectionToken", "Directive", "EventEmitter", "TemplateRef", "ViewContainerRef", "booleanAttribute", "Input", "Output", "NgModule", "DOCUMENT", "Location", "P", "Platform", "_", "_bindEventWithOptions", "_getEventTarget", "_isTestEnvironment", "_CdkPrivateStyleLoader", "Subject", "Subscription", "merge", "filter", "takeUntil", "<PERSON><PERSON><PERSON><PERSON>", "c", "coerceCssPixelValue", "coerce<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "ScrollingModule", "s", "supportsScrollBehavior", "b", "DomPortalOutlet", "T", "TemplatePortal", "h", "PortalModule", "D", "Directionality", "_IdGenerator", "g", "ESCAPE", "hasModifierKey", "BidiModule", "scrollBehaviorSupported", "BlockScrollStrategy", "constructor", "_viewportRuler", "document", "_defineProperty", "top", "left", "_document", "attach", "enable", "_canBeEnabled", "root", "documentElement", "_previousScrollPosition", "getViewportScrollPosition", "_previousHTMLStyles", "style", "classList", "add", "_isEnabled", "disable", "html", "body", "htmlStyle", "bodyStyle", "previousHtmlScrollBehavior", "scroll<PERSON>eh<PERSON>or", "previousBodyScrollBehavior", "remove", "window", "scroll", "contains", "rootElement", "viewport", "getViewportSize", "scrollHeight", "height", "scrollWidth", "width", "getMatScrollStrategyAlreadyAttachedError", "Error", "CloseScrollStrategy", "_scrollDispatcher", "_ngZone", "_config", "_overlayRef", "has<PERSON>tta<PERSON>", "run", "detach", "overlayRef", "ngDevMode", "_scrollSubscription", "stream", "scrolled", "pipe", "scrollable", "overlayElement", "getElementRef", "nativeElement", "threshold", "_initialScrollPosition", "subscribe", "scrollPosition", "Math", "abs", "_detach", "updatePosition", "unsubscribe", "NoopScrollStrategy", "isElementScrolledOutsideView", "element", "scrollContainers", "some", "containerBounds", "outsideAbove", "bottom", "outsideBelow", "outsideLeft", "right", "outsideRight", "isElementClippedByScrolling", "scrollContainerRect", "clippedAbove", "<PERSON><PERSON><PERSON><PERSON>", "clippedLeft", "clippedRight", "RepositionScrollStrategy", "throttle", "scrollThrottle", "autoClose", "overlayRect", "getBoundingClientRect", "parentRects", "ScrollStrategyOptions", "config", "_ScrollStrategyOptions", "_ScrollStrategyOptions_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ɵsetClassMetadata", "type", "args", "OverlayConfig", "config<PERSON><PERSON><PERSON>", "Object", "keys", "key", "undefined", "ConnectionPositionPair", "origin", "overlay", "offsetX", "offsetY", "panelClass", "originX", "originY", "overlayX", "overlayY", "ScrollingVisibility", "ConnectedOverlayPositionChange", "connectionPair", "scrollableViewProperties", "validateVerticalPosition", "property", "value", "validateHorizontalPosition", "BaseOverlayDispatcher", "ngOnDestroy", "_attachedOverlays", "push", "index", "indexOf", "splice", "length", "_BaseOverlayDispatcher", "_BaseOverlayDispatcher_Factory", "OverlayKeyboardDispatcher", "<PERSON><PERSON><PERSON><PERSON>", "event", "overlays", "i", "_keydownEvents", "observers", "next", "_isAttached", "runOutsideAngular", "_cleanupKeydown", "_renderer", "listen", "_keydownListener", "_this$_cleanupKeydown", "call", "_OverlayKeyboardDispatcher", "ɵ_OverlayKeyboardDispatcher_BaseFactory", "_OverlayKeyboardDispatcher_Factory", "ɵɵgetInheritedFactory", "OverlayOutsideClickDispatcher", "_pointerDownEventTarget", "target", "slice", "_outsidePointerEvents", "containsPierceShadowDom", "outsidePointerEvents", "eventOptions", "capture", "_cleanups", "_pointerDownListener", "_clickListener", "_platform", "IOS", "_cursorStyleIsSet", "_cursorOriginalV<PERSON>ue", "cursor", "_this$_cleanups", "for<PERSON>ach", "cleanup", "_OverlayOutsideClickDispatcher", "ɵ_OverlayOutsideClickDispatcher_BaseFactory", "_OverlayOutsideClickDispatcher_Factory", "parent", "child", "supportsShadowRoot", "ShadowRoot", "current", "host", "parentNode", "_CdkOverlayStyleLoader", "_CdkOverlayStyleLoader2", "_CdkOverlayStyleLoader2_Factory", "ɵɵdefineComponent", "selectors", "hostAttrs", "decls", "vars", "template", "_CdkOverlayStyleLoader2_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "OnPush", "None", "OverlayContainer", "_this$_containerEleme", "_containerElement", "getContainerElement", "_loadStyles", "_createContainer", "containerClass", "<PERSON><PERSON><PERSON><PERSON>", "oppositePlatformContainers", "querySelectorAll", "container", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "_styleLoader", "load", "_OverlayContainer", "_OverlayContainer_Factory", "BackdropRef", "onClick", "_this$_cleanupClick", "_this$_cleanupTransit", "clearTimeout", "_fallbackTimeout", "_cleanupClick", "_cleanupTransitionEnd", "_this$_cleanupTransit2", "dispose", "setTimeout", "pointerEvents", "OverlayRef", "_portalOutlet", "_host", "_pane", "_keyboardDispatcher", "_location", "_outsideClickDis<PERSON>tcher", "_animationsDisabled", "_injector", "EMPTY", "scrollStrategy", "_scrollStrategy", "_positionStrategy", "positionStrategy", "_afterRenderRef", "_renders", "injector", "backdropElement", "_this$_backdropRef", "_backdropRef", "hostElement", "portal", "_this$_afterNextRende", "parentElement", "_previousHostParent", "attachResult", "_updateStackingOrder", "_updateElementSize", "_updateElementDirection", "_afterNextRenderRef", "destroy", "_togglePointerEvents", "hasBackdrop", "_attachBackdrop", "_toggleClasses", "_attachments", "disposeOnNavigation", "_locationChanges", "onDestroy", "Promise", "resolve", "then", "detachBackdrop", "detachmentResult", "_detachments", "_detachContentWhenEmpty", "_this$_backdropRef2", "_this$_host", "_this$_afterNextRende2", "isAttached", "_disposeScrollStrategy", "complete", "_backdropClick", "backdropClick", "attachments", "detachments", "keydownEvents", "getConfig", "apply", "updatePositionStrategy", "strategy", "updateSize", "sizeConfig", "_objectSpread", "setDirection", "dir", "direction", "addPanelClass", "classes", "removePanelClass", "getDirection", "updateScrollStrategy", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "enablePointer", "_this$_backdropRef3", "showingClass", "backdropClass", "insertBefore", "requestAnimationFrame", "_this$_backdropRef4", "nextS<PERSON>ling", "_this$_backdropRef5", "_this$_backdropRef6", "cssClasses", "isAdd", "subscription", "children", "_scrollStrategy$detac", "boundingBoxClass", "cssUnitPattern", "FlexibleConnectedPositionStrategy", "positions", "_preferredPositions", "connectedTo", "_overlayContainer", "_positionChanges", "<PERSON><PERSON><PERSON><PERSON>", "_validatePositions", "_boundingBox", "_isDisposed", "_isInitialRender", "_lastPosition", "_resizeSubscription", "change", "_positionLocked", "reapplyLastPosition", "_clearPanelClasses", "_resetOverlayElementStyles", "_resetBoundingBoxStyles", "_viewportRect", "_getNarrowedViewportRect", "_originRect", "_getOriginRect", "_overlayRect", "_containerRect", "originRect", "viewportRect", "containerRect", "flexibleFits", "fallback", "pos", "originPoint", "_getOriginPoint", "overlayPoint", "_getOverlayPoint", "overlayFit", "_getOverlayFit", "isCompletelyWithinViewport", "_isPushed", "_applyPosition", "_canFitWithFlexibleDimensions", "position", "boundingBoxRect", "_calculateBoundingBoxRect", "visibleArea", "bestFit", "bestScore", "fit", "score", "weight", "_canPush", "_previousPushAmount", "extendStyles", "alignItems", "justifyContent", "lastPosition", "withScrollableContainers", "scrollables", "_scrollables", "withPositions", "withViewportMargin", "margin", "_viewportMargin", "withFlexibleDimensions", "flexibleDimensions", "_hasFlexibleDimensions", "withGrowAfterOpen", "growAfterOpen", "_growAfterOpen", "with<PERSON><PERSON>", "canPush", "withLockedPosition", "isLocked", "_origin", "withDefaultOffsetX", "offset", "_offsetX", "withDefaultOffsetY", "_offsetY", "withTransformOriginOn", "selector", "_transformOriginSelector", "x", "startX", "_isRtl", "endX", "y", "overlayStartX", "overlayStartY", "point", "rawOverlayRect", "getRoundedBoundingClientRect", "_getOffset", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "visibleWidth", "_subtractOverflows", "visibleHeight", "fitsInViewportVertically", "fitsInViewportHorizontally", "availableHeight", "availableWidth", "getPixelValue", "verticalFit", "horizontalFit", "_pushOverlayOnScreen", "start", "overflowRight", "max", "overflowBottom", "overflowTop", "overflowLeft", "pushX", "pushY", "_setTransformOrigin", "_setOverlayElementStyles", "_setBoundingBoxStyles", "_addPanelClasses", "scrollVisibility", "_getScrollVisibility", "_lastScrollVisibility", "compareScrollVisibility", "changeEvent", "elements", "xOrigin", "y<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "isRtl", "smallestDistanceToViewportEdge", "min", "previousHeight", "_lastBoundingBoxSize", "isBoundedByRightViewportEdge", "isBoundedByLeftViewportEdge", "previousWidth", "_hasExactPosition", "transform", "hasExactPosition", "hasFlexibleDimensions", "_getExactOverlayY", "_getExactOverlayX", "transformString", "trim", "documentHeight", "clientHeight", "horizontalStyleProperty", "documentWidth", "clientWidth", "originBounds", "overlayBounds", "scrollContainerBounds", "map", "isOriginClipped", "isOriginOutsideView", "isOverlayClipped", "isOverlayOutsideView", "overflows", "reduce", "currentValue", "currentOverflow", "axis", "pair", "cssClass", "_appliedPanelClasses", "Element", "destination", "source", "hasOwnProperty", "input", "units", "split", "parseFloat", "clientRect", "floor", "a", "STANDARD_DROPDOWN_BELOW_POSITIONS", "STANDARD_DROPDOWN_ADJACENT_POSITIONS", "wrapperClass", "GlobalPositionStrategy", "_width", "_height", "_bottomOffset", "_topOffset", "_alignItems", "_xOffset", "_xPosition", "end", "centerHorizontally", "centerVertically", "parentStyles", "shouldBeFlushHorizontally", "shouldBeFlushVertically", "xPosition", "xOffset", "marginLeft", "marginRight", "_cssPosition", "marginTop", "marginBottom", "OverlayPositionBuilder", "global", "flexibleConnectedTo", "_OverlayPositionBuilder", "_OverlayPositionBuilder_Factory", "Overlay", "optional", "create", "_createHostElement", "pane", "_createPaneElement", "portalOutlet", "_createPortalOutlet", "overlayConfig", "_directionality", "_animationsModuleType", "get", "_positionBuilder", "id", "_idGenerator", "getId", "_appRef", "_Overlay", "_Overlay_Factory", "defaultPositionList", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY", "scrollStrategies", "reposition", "CdkOverlayOrigin", "_CdkOverlayOrigin", "_CdkOverlayOrigin_Factory", "ɵɵdefineDirective", "exportAs", "CdkConnectedOverlay", "_position", "_updatePositionStrategy", "_disposeOnNavigation", "templateRef", "viewContainerRef", "_templatePortal", "_scrollStrategyFactory", "_dir", "_this$_overlayRef", "_attachSubscription", "_detachSubscription", "_backdropSubscription", "_positionSubscription", "ngOnChanges", "changes", "_this$_overlayRef2", "open", "attachOverlay", "detachOverlay", "_createOverlay", "_overlay", "_buildConfig", "emit", "overlayKeydown", "keyCode", "disableClose", "preventDefault", "_get<PERSON><PERSON>in<PERSON><PERSON>", "overlayOutsideClick", "_createPositionStrategy", "currentPosition", "_get<PERSON><PERSON>in", "viewportMargin", "lockPosition", "transformOriginSelector", "elementRef", "positionChange", "position<PERSON><PERSON>es", "_this$_overlayRef3", "_CdkConnectedOverlay", "_CdkConnectedOverlay_Factory", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "alias", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "OverlayModule", "_OverlayModule", "_OverlayModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector", "providers", "B", "C", "F", "G", "N", "O", "R", "S", "d", "e", "f", "j", "k", "l", "m", "n", "o", "v"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/overlay-module-BUj0D19H.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable, RendererFactory2, Component, ChangeDetectionStrategy, ViewEncapsulation, untracked, afterRender, afterNextRender, ElementRef, Injector, ANIMATION_MODULE_TYPE, EnvironmentInjector, ApplicationRef, InjectionToken, Directive, EventEmitter, TemplateRef, ViewContainerRef, booleanAttribute, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT, Location } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { _ as _getEventTarget } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { filter, takeUntil, takeWhile } from 'rxjs/operators';\nimport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\nimport { ScrollDispatcher, ViewportRuler, ScrollingModule } from './scrolling.mjs';\nimport { s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { b as DomPortalOutlet, T as TemplatePortal, h as PortalModule } from './portal-directives-Bw5woq8I.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { BidiModule } from './bidi.mjs';\n\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n    _viewportRuler;\n    _previousHTMLStyles = { top: '', left: '' };\n    _previousScrollPosition;\n    _isEnabled = false;\n    _document;\n    constructor(_viewportRuler, document) {\n        this._viewportRuler = _viewportRuler;\n        this._document = document;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach() { }\n    /** Blocks page-level scroll while the attached overlay is open. */\n    enable() {\n        if (this._canBeEnabled()) {\n            const root = this._document.documentElement;\n            this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n            // Cache the previous inline styles in case the user had set them.\n            this._previousHTMLStyles.left = root.style.left || '';\n            this._previousHTMLStyles.top = root.style.top || '';\n            // Note: we're using the `html` node, instead of the `body`, because the `body` may\n            // have the user agent margin, whereas the `html` is guaranteed not to have one.\n            root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n            root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n            root.classList.add('cdk-global-scrollblock');\n            this._isEnabled = true;\n        }\n    }\n    /** Unblocks page-level scroll while the attached overlay is open. */\n    disable() {\n        if (this._isEnabled) {\n            const html = this._document.documentElement;\n            const body = this._document.body;\n            const htmlStyle = html.style;\n            const bodyStyle = body.style;\n            const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n            const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n            this._isEnabled = false;\n            htmlStyle.left = this._previousHTMLStyles.left;\n            htmlStyle.top = this._previousHTMLStyles.top;\n            html.classList.remove('cdk-global-scrollblock');\n            // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n            // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n            // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n            // because it can throw off feature detections in `supportsScrollBehavior` which\n            // checks for `'scrollBehavior' in documentElement.style`.\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n            }\n            window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n                bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n            }\n        }\n    }\n    _canBeEnabled() {\n        // Since the scroll strategies can't be singletons, we have to use a global CSS class\n        // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n        // scrolling multiple times.\n        const html = this._document.documentElement;\n        if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n            return false;\n        }\n        const rootElement = this._document.documentElement;\n        const viewport = this._viewportRuler.getViewportSize();\n        return rootElement.scrollHeight > viewport.height || rootElement.scrollWidth > viewport.width;\n    }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n    return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n    _scrollDispatcher;\n    _ngZone;\n    _viewportRuler;\n    _config;\n    _scrollSubscription = null;\n    _overlayRef;\n    _initialScrollPosition;\n    constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._config = _config;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables the closing of the attached overlay on scroll. */\n    enable() {\n        if (this._scrollSubscription) {\n            return;\n        }\n        const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n            return (!scrollable ||\n                !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement));\n        }));\n        if (this._config && this._config.threshold && this._config.threshold > 1) {\n            this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n            this._scrollSubscription = stream.subscribe(() => {\n                const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n                if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n                    this._detach();\n                }\n                else {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n        else {\n            this._scrollSubscription = stream.subscribe(this._detach);\n        }\n    }\n    /** Disables the closing the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n    /** Detaches the overlay ref and disables the scroll strategy. */\n    _detach = () => {\n        this.disable();\n        if (this._overlayRef.hasAttached()) {\n            this._ngZone.run(() => this._overlayRef.detach());\n        }\n    };\n}\n\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n    /** Does nothing, as this scroll strategy is a no-op. */\n    enable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    disable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    attach() { }\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n    return scrollContainers.some(containerBounds => {\n        const outsideAbove = element.bottom < containerBounds.top;\n        const outsideBelow = element.top > containerBounds.bottom;\n        const outsideLeft = element.right < containerBounds.left;\n        const outsideRight = element.left > containerBounds.right;\n        return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n    });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n    return scrollContainers.some(scrollContainerRect => {\n        const clippedAbove = element.top < scrollContainerRect.top;\n        const clippedBelow = element.bottom > scrollContainerRect.bottom;\n        const clippedLeft = element.left < scrollContainerRect.left;\n        const clippedRight = element.right > scrollContainerRect.right;\n        return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n    });\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n    _scrollDispatcher;\n    _viewportRuler;\n    _ngZone;\n    _config;\n    _scrollSubscription = null;\n    _overlayRef;\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        this._config = _config;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables repositioning of the attached overlay on scroll. */\n    enable() {\n        if (!this._scrollSubscription) {\n            const throttle = this._config ? this._config.scrollThrottle : 0;\n            this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n                this._overlayRef.updatePosition();\n                // TODO(crisbeto): make `close` on by default once all components can handle it.\n                if (this._config && this._config.autoClose) {\n                    const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n                    const { width, height } = this._viewportRuler.getViewportSize();\n                    // TODO(crisbeto): include all ancestor scroll containers here once\n                    // we have a way of exposing the trigger element to the scroll strategy.\n                    const parentRects = [{ width, height, bottom: height, right: width, top: 0, left: 0 }];\n                    if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n                        this.disable();\n                        this._ngZone.run(() => this._overlayRef.detach());\n                    }\n                }\n            });\n        }\n    }\n    /** Disables repositioning of the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n    _scrollDispatcher = inject(ScrollDispatcher);\n    _viewportRuler = inject(ViewportRuler);\n    _ngZone = inject(NgZone);\n    _document = inject(DOCUMENT);\n    constructor() { }\n    /** Do nothing on scroll. */\n    noop = () => new NoopScrollStrategy();\n    /**\n     * Close the overlay as soon as the user scrolls.\n     * @param config Configuration to be used inside the scroll strategy.\n     */\n    close = (config) => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n    /** Block scrolling. */\n    block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n    /**\n     * Update the overlay's position on scroll.\n     * @param config Configuration to be used inside the scroll strategy.\n     * Allows debouncing the reposition calls.\n     */\n    reposition = (config) => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollStrategyOptions, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollStrategyOptions, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollStrategyOptions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n    /** Strategy with which to position the overlay. */\n    positionStrategy;\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    scrollStrategy = new NoopScrollStrategy();\n    /** Custom class to add to the overlay pane. */\n    panelClass = '';\n    /** Whether the overlay has a backdrop. */\n    hasBackdrop = false;\n    /** Custom class to add to the backdrop */\n    backdropClass = 'cdk-overlay-dark-backdrop';\n    /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n    width;\n    /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n    height;\n    /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n    minWidth;\n    /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n    minHeight;\n    /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n    maxWidth;\n    /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n    maxHeight;\n    /**\n     * Direction of the text in the overlay panel. If a `Directionality` instance\n     * is passed in, the overlay will handle changes to its value automatically.\n     */\n    direction;\n    /**\n     * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    disposeOnNavigation = false;\n    constructor(config) {\n        if (config) {\n            // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n            // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n            // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n            const configKeys = Object.keys(config);\n            for (const key of configKeys) {\n                if (config[key] !== undefined) {\n                    // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n                    // as \"I don't know *which* key this is, so the only valid value is the intersection\n                    // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n                    // is not smart enough to see that the right-hand-side is actually an access of the same\n                    // exact type with the same exact key, meaning that the value type must be identical.\n                    // So we use `any` to work around this.\n                    this[key] = config[key];\n                }\n            }\n        }\n    }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n    offsetX;\n    offsetY;\n    panelClass;\n    /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n    originX;\n    /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n    originY;\n    /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n    overlayX;\n    /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n    overlayY;\n    constructor(origin, overlay, \n    /** Offset along the X axis. */\n    offsetX, \n    /** Offset along the Y axis. */\n    offsetY, \n    /** Class(es) to be applied to the panel while this position is active. */\n    panelClass) {\n        this.offsetX = offsetX;\n        this.offsetY = offsetY;\n        this.panelClass = panelClass;\n        this.originX = origin.originX;\n        this.originY = origin.originY;\n        this.overlayX = overlay.overlayX;\n        this.overlayY = overlay.overlayY;\n    }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n    isOriginClipped;\n    isOriginOutsideView;\n    isOverlayClipped;\n    isOverlayOutsideView;\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n    connectionPair;\n    scrollableViewProperties;\n    constructor(\n    /** The position used as a result of this change. */\n    connectionPair, \n    /** @docs-private */\n    scrollableViewProperties) {\n        this.connectionPair = connectionPair;\n        this.scrollableViewProperties = scrollableViewProperties;\n    }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n    if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"top\", \"bottom\" or \"center\".`);\n    }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n    if (value !== 'start' && value !== 'end' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"start\", \"end\" or \"center\".`);\n    }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n    /** Currently attached overlays in the order they were attached. */\n    _attachedOverlays = [];\n    _document = inject(DOCUMENT);\n    _isAttached;\n    constructor() { }\n    ngOnDestroy() {\n        this.detach();\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        // Ensure that we don't get the same overlay multiple times.\n        this.remove(overlayRef);\n        this._attachedOverlays.push(overlayRef);\n    }\n    /** Remove an overlay from the list of attached overlay refs. */\n    remove(overlayRef) {\n        const index = this._attachedOverlays.indexOf(overlayRef);\n        if (index > -1) {\n            this._attachedOverlays.splice(index, 1);\n        }\n        // Remove the global listener once there are no more overlays.\n        if (this._attachedOverlays.length === 0) {\n            this.detach();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BaseOverlayDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BaseOverlayDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BaseOverlayDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n    _ngZone = inject(NgZone);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _cleanupKeydown;\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Lazily start dispatcher once first overlay is added\n        if (!this._isAttached) {\n            this._ngZone.runOutsideAngular(() => {\n                this._cleanupKeydown = this._renderer.listen('body', 'keydown', this._keydownListener);\n            });\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._cleanupKeydown?.();\n            this._isAttached = false;\n        }\n    }\n    /** Keyboard event listener that will be attached to the body. */\n    _keydownListener = (event) => {\n        const overlays = this._attachedOverlays;\n        for (let i = overlays.length - 1; i > -1; i--) {\n            // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n            // We want to target the most recent overlay, rather than trying to match where the event came\n            // from, because some components might open an overlay, but keep focus on a trigger element\n            // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n            // because we don't want overlays that don't handle keyboard events to block the ones below\n            // them that do.\n            if (overlays[i]._keydownEvents.observers.length > 0) {\n                this._ngZone.run(() => overlays[i]._keydownEvents.next(event));\n                break;\n            }\n        }\n    };\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayKeyboardDispatcher, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayKeyboardDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayKeyboardDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _cursorOriginalValue;\n    _cursorStyleIsSet = false;\n    _pointerDownEventTarget;\n    _cleanups;\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Safari on iOS does not generate click events for non-interactive\n        // elements. However, we want to receive a click for any element outside\n        // the overlay. We can force a \"clickable\" state by setting\n        // `cursor: pointer` on the document body. See:\n        // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n        // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n        if (!this._isAttached) {\n            const body = this._document.body;\n            const eventOptions = { capture: true };\n            this._cleanups = this._ngZone.runOutsideAngular(() => [\n                _bindEventWithOptions(this._renderer, body, 'pointerdown', this._pointerDownListener, eventOptions),\n                _bindEventWithOptions(this._renderer, body, 'click', this._clickListener, eventOptions),\n                _bindEventWithOptions(this._renderer, body, 'auxclick', this._clickListener, eventOptions),\n                _bindEventWithOptions(this._renderer, body, 'contextmenu', this._clickListener, eventOptions),\n            ]);\n            // click event is not fired on iOS. To make element \"clickable\" we are\n            // setting the cursor to pointer\n            if (this._platform.IOS && !this._cursorStyleIsSet) {\n                this._cursorOriginalValue = body.style.cursor;\n                body.style.cursor = 'pointer';\n                this._cursorStyleIsSet = true;\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._cleanups?.forEach(cleanup => cleanup());\n            this._cleanups = undefined;\n            if (this._platform.IOS && this._cursorStyleIsSet) {\n                this._document.body.style.cursor = this._cursorOriginalValue;\n                this._cursorStyleIsSet = false;\n            }\n            this._isAttached = false;\n        }\n    }\n    /** Store pointerdown event target to track origin of click. */\n    _pointerDownListener = (event) => {\n        this._pointerDownEventTarget = _getEventTarget(event);\n    };\n    /** Click event listener that will be attached to the body propagate phase. */\n    _clickListener = (event) => {\n        const target = _getEventTarget(event);\n        // In case of a click event, we want to check the origin of the click\n        // (e.g. in case where a user starts a click inside the overlay and\n        // releases the click outside of it).\n        // This is done by using the event target of the preceding pointerdown event.\n        // Every click event caused by a pointer device has a preceding pointerdown\n        // event, unless the click was programmatically triggered (e.g. in a unit test).\n        const origin = event.type === 'click' && this._pointerDownEventTarget\n            ? this._pointerDownEventTarget\n            : target;\n        // Reset the stored pointerdown event target, to avoid having it interfere\n        // in subsequent events.\n        this._pointerDownEventTarget = null;\n        // We copy the array because the original may be modified asynchronously if the\n        // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n        // the for loop.\n        const overlays = this._attachedOverlays.slice();\n        // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n        // We want to target all overlays for which the click could be considered as outside click.\n        // As soon as we reach an overlay for which the click is not outside click we break off\n        // the loop.\n        for (let i = overlays.length - 1; i > -1; i--) {\n            const overlayRef = overlays[i];\n            if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n                continue;\n            }\n            // If it's a click inside the overlay, just break - we should do nothing\n            // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n            // and proceed with the next overlay\n            if (containsPierceShadowDom(overlayRef.overlayElement, target) ||\n                containsPierceShadowDom(overlayRef.overlayElement, origin)) {\n                break;\n            }\n            const outsidePointerEvents = overlayRef._outsidePointerEvents;\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.run(() => outsidePointerEvents.next(event));\n            }\n            else {\n                outsidePointerEvents.next(event);\n            }\n        }\n    };\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayOutsideClickDispatcher, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayOutsideClickDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayOutsideClickDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent, child) {\n    const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n    let current = child;\n    while (current) {\n        if (current === parent) {\n            return true;\n        }\n        current =\n            supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n    }\n    return false;\n}\n\nclass _CdkOverlayStyleLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkOverlayStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: _CdkOverlayStyleLoader, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"cdk-overlay-style-loader\": \"\" } }, ngImport: i0, template: '', isInline: true, styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkOverlayStyleLoader, decorators: [{\n            type: Component,\n            args: [{ template: '', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: { 'cdk-overlay-style-loader': '' }, styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"] }]\n        }] });\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n    _platform = inject(Platform);\n    _containerElement;\n    _document = inject(DOCUMENT);\n    _styleLoader = inject(_CdkPrivateStyleLoader);\n    constructor() { }\n    ngOnDestroy() {\n        this._containerElement?.remove();\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        this._loadStyles();\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body.\n     */\n    _createContainer() {\n        const containerClass = 'cdk-overlay-container';\n        // TODO(crisbeto): remove the testing check once we have an overlay testing\n        // module or Angular starts tearing down the testing `NgModule`. See:\n        // https://github.com/angular/angular/issues/18831\n        if (this._platform.isBrowser || _isTestEnvironment()) {\n            const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n            // Remove any old containers from the opposite platform.\n            // This can happen when transitioning from the server to the client.\n            for (let i = 0; i < oppositePlatformContainers.length; i++) {\n                oppositePlatformContainers[i].remove();\n            }\n        }\n        const container = this._document.createElement('div');\n        container.classList.add(containerClass);\n        // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n        // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n        // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n        // To mitigate the problem we made it so that only containers from a different platform are\n        // cleared, but the side-effect was that people started depending on the overly-aggressive\n        // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n        // module which does the cleanup, we try to detect that we're in a test environment and we\n        // always clear the container. See #17006.\n        // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n        if (_isTestEnvironment()) {\n            container.setAttribute('platform', 'test');\n        }\n        else if (!this._platform.isBrowser) {\n            container.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n    /** Loads the structural styles necessary for the overlay to work. */\n    _loadStyles() {\n        this._styleLoader.load(_CdkOverlayStyleLoader);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayContainer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayContainer, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Encapsulates the logic for attaching and detaching a backdrop. */\nclass BackdropRef {\n    _renderer;\n    _ngZone;\n    element;\n    _cleanupClick;\n    _cleanupTransitionEnd;\n    _fallbackTimeout;\n    constructor(document, _renderer, _ngZone, onClick) {\n        this._renderer = _renderer;\n        this._ngZone = _ngZone;\n        this.element = document.createElement('div');\n        this.element.classList.add('cdk-overlay-backdrop');\n        this._cleanupClick = _renderer.listen(this.element, 'click', onClick);\n    }\n    detach() {\n        this._ngZone.runOutsideAngular(() => {\n            const element = this.element;\n            clearTimeout(this._fallbackTimeout);\n            this._cleanupTransitionEnd?.();\n            this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this.dispose);\n            this._fallbackTimeout = setTimeout(this.dispose, 500);\n            // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n            // In this case we make it unclickable and we try to remove it after a delay.\n            element.style.pointerEvents = 'none';\n            element.classList.remove('cdk-overlay-backdrop-showing');\n        });\n    }\n    dispose = () => {\n        clearTimeout(this._fallbackTimeout);\n        this._cleanupClick?.();\n        this._cleanupTransitionEnd?.();\n        this._cleanupClick = this._cleanupTransitionEnd = this._fallbackTimeout = undefined;\n        this.element.remove();\n    };\n}\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    _portalOutlet;\n    _host;\n    _pane;\n    _config;\n    _ngZone;\n    _keyboardDispatcher;\n    _document;\n    _location;\n    _outsideClickDispatcher;\n    _animationsDisabled;\n    _injector;\n    _renderer;\n    _backdropClick = new Subject();\n    _attachments = new Subject();\n    _detachments = new Subject();\n    _positionStrategy;\n    _scrollStrategy;\n    _locationChanges = Subscription.EMPTY;\n    _backdropRef = null;\n    /**\n     * Reference to the parent of the `_host` at the time it was detached. Used to restore\n     * the `_host` to its original position in the DOM when it gets re-attached.\n     */\n    _previousHostParent;\n    /** Stream of keydown events dispatched to this overlay. */\n    _keydownEvents = new Subject();\n    /** Stream of mouse outside events dispatched to this overlay. */\n    _outsidePointerEvents = new Subject();\n    _renders = new Subject();\n    _afterRenderRef;\n    /** Reference to the currently-running `afterNextRender` call. */\n    _afterNextRenderRef;\n    constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false, _injector, _renderer) {\n        this._portalOutlet = _portalOutlet;\n        this._host = _host;\n        this._pane = _pane;\n        this._config = _config;\n        this._ngZone = _ngZone;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._document = _document;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsDisabled = _animationsDisabled;\n        this._injector = _injector;\n        this._renderer = _renderer;\n        if (_config.scrollStrategy) {\n            this._scrollStrategy = _config.scrollStrategy;\n            this._scrollStrategy.attach(this);\n        }\n        this._positionStrategy = _config.positionStrategy;\n        // Users could open the overlay from an `effect`, in which case we need to\n        // run the `afterRender` as `untracked`. We don't recommend that users do\n        // this, but we also don't want to break users who are doing it.\n        this._afterRenderRef = untracked(() => afterRender(() => {\n            this._renders.next();\n        }, { injector: this._injector }));\n    }\n    /** The overlay's HTML element */\n    get overlayElement() {\n        return this._pane;\n    }\n    /** The overlay's backdrop HTML element. */\n    get backdropElement() {\n        return this._backdropRef?.element || null;\n    }\n    /**\n     * Wrapper around the panel element. Can be used for advanced\n     * positioning where a wrapper with specific styling is\n     * required around the overlay pane.\n     */\n    get hostElement() {\n        return this._host;\n    }\n    /**\n     * Attaches content, given via a Portal, to the overlay.\n     * If the overlay is configured to have a backdrop, it will be created.\n     *\n     * @param portal Portal instance to which to attach the overlay.\n     * @returns The portal attachment result.\n     */\n    attach(portal) {\n        // Insert the host into the DOM before attaching the portal, otherwise\n        // the animations module will skip animations on repeat attachments.\n        if (!this._host.parentElement && this._previousHostParent) {\n            this._previousHostParent.appendChild(this._host);\n        }\n        const attachResult = this._portalOutlet.attach(portal);\n        if (this._positionStrategy) {\n            this._positionStrategy.attach(this);\n        }\n        this._updateStackingOrder();\n        this._updateElementSize();\n        this._updateElementDirection();\n        if (this._scrollStrategy) {\n            this._scrollStrategy.enable();\n        }\n        // We need to clean this up ourselves, because we're passing in an\n        // `EnvironmentInjector` below which won't ever be destroyed.\n        // Otherwise it causes some callbacks to be retained (see #29696).\n        this._afterNextRenderRef?.destroy();\n        // Update the position once the overlay is fully rendered before attempting to position it,\n        // as the position may depend on the size of the rendered content.\n        this._afterNextRenderRef = afterNextRender(() => {\n            // The overlay could've been detached before the callback executed.\n            if (this.hasAttached()) {\n                this.updatePosition();\n            }\n        }, { injector: this._injector });\n        // Enable pointer events for the overlay pane element.\n        this._togglePointerEvents(true);\n        if (this._config.hasBackdrop) {\n            this._attachBackdrop();\n        }\n        if (this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, true);\n        }\n        // Only emit the `attachments` event once all other setup is done.\n        this._attachments.next();\n        // Track this overlay by the keyboard dispatcher\n        this._keyboardDispatcher.add(this);\n        if (this._config.disposeOnNavigation) {\n            this._locationChanges = this._location.subscribe(() => this.dispose());\n        }\n        this._outsideClickDispatcher.add(this);\n        // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n        // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n        // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n        if (typeof attachResult?.onDestroy === 'function') {\n            // In most cases we control the portal and we know when it is being detached so that\n            // we can finish the disposal process. The exception is if the user passes in a custom\n            // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n            // `detach` here instead of `dispose`, because we don't know if the user intends to\n            // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n            attachResult.onDestroy(() => {\n                if (this.hasAttached()) {\n                    // We have to delay the `detach` call, because detaching immediately prevents\n                    // other destroy hooks from running. This is likely a framework bug similar to\n                    // https://github.com/angular/angular/issues/46119\n                    this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n                }\n            });\n        }\n        return attachResult;\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns The portal detachment result.\n     */\n    detach() {\n        if (!this.hasAttached()) {\n            return;\n        }\n        this.detachBackdrop();\n        // When the overlay is detached, the pane element should disable pointer events.\n        // This is necessary because otherwise the pane element will cover the page and disable\n        // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n        this._togglePointerEvents(false);\n        if (this._positionStrategy && this._positionStrategy.detach) {\n            this._positionStrategy.detach();\n        }\n        if (this._scrollStrategy) {\n            this._scrollStrategy.disable();\n        }\n        const detachmentResult = this._portalOutlet.detach();\n        // Only emit after everything is detached.\n        this._detachments.next();\n        // Remove this overlay from keyboard dispatcher tracking.\n        this._keyboardDispatcher.remove(this);\n        // Keeping the host element in the DOM can cause scroll jank, because it still gets\n        // rendered, even though it's transparent and unclickable which is why we remove it.\n        this._detachContentWhenEmpty();\n        this._locationChanges.unsubscribe();\n        this._outsideClickDispatcher.remove(this);\n        return detachmentResult;\n    }\n    /** Cleans up the overlay from the DOM. */\n    dispose() {\n        const isAttached = this.hasAttached();\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._disposeScrollStrategy();\n        this._backdropRef?.dispose();\n        this._locationChanges.unsubscribe();\n        this._keyboardDispatcher.remove(this);\n        this._portalOutlet.dispose();\n        this._attachments.complete();\n        this._backdropClick.complete();\n        this._keydownEvents.complete();\n        this._outsidePointerEvents.complete();\n        this._outsideClickDispatcher.remove(this);\n        this._host?.remove();\n        this._afterNextRenderRef?.destroy();\n        this._previousHostParent = this._pane = this._host = this._backdropRef = null;\n        if (isAttached) {\n            this._detachments.next();\n        }\n        this._detachments.complete();\n        this._afterRenderRef.destroy();\n        this._renders.complete();\n    }\n    /** Whether the overlay has attached content. */\n    hasAttached() {\n        return this._portalOutlet.hasAttached();\n    }\n    /** Gets an observable that emits when the backdrop has been clicked. */\n    backdropClick() {\n        return this._backdropClick;\n    }\n    /** Gets an observable that emits when the overlay has been attached. */\n    attachments() {\n        return this._attachments;\n    }\n    /** Gets an observable that emits when the overlay has been detached. */\n    detachments() {\n        return this._detachments;\n    }\n    /** Gets an observable of keydown events targeted to this overlay. */\n    keydownEvents() {\n        return this._keydownEvents;\n    }\n    /** Gets an observable of pointer events targeted outside this overlay. */\n    outsidePointerEvents() {\n        return this._outsidePointerEvents;\n    }\n    /** Gets the current overlay configuration, which is immutable. */\n    getConfig() {\n        return this._config;\n    }\n    /** Updates the position of the overlay based on the position strategy. */\n    updatePosition() {\n        if (this._positionStrategy) {\n            this._positionStrategy.apply();\n        }\n    }\n    /** Switches to a new position strategy and updates the overlay position. */\n    updatePositionStrategy(strategy) {\n        if (strategy === this._positionStrategy) {\n            return;\n        }\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._positionStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            this.updatePosition();\n        }\n    }\n    /** Update the size properties of the overlay. */\n    updateSize(sizeConfig) {\n        this._config = { ...this._config, ...sizeConfig };\n        this._updateElementSize();\n    }\n    /** Sets the LTR/RTL direction for the overlay. */\n    setDirection(dir) {\n        this._config = { ...this._config, direction: dir };\n        this._updateElementDirection();\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, true);\n        }\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, false);\n        }\n    }\n    /**\n     * Returns the layout direction of the overlay panel.\n     */\n    getDirection() {\n        const direction = this._config.direction;\n        if (!direction) {\n            return 'ltr';\n        }\n        return typeof direction === 'string' ? direction : direction.value;\n    }\n    /** Switches to a new scroll strategy. */\n    updateScrollStrategy(strategy) {\n        if (strategy === this._scrollStrategy) {\n            return;\n        }\n        this._disposeScrollStrategy();\n        this._scrollStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            strategy.enable();\n        }\n    }\n    /** Updates the text direction of the overlay panel. */\n    _updateElementDirection() {\n        this._host.setAttribute('dir', this.getDirection());\n    }\n    /** Updates the size of the overlay element based on the overlay config. */\n    _updateElementSize() {\n        if (!this._pane) {\n            return;\n        }\n        const style = this._pane.style;\n        style.width = coerceCssPixelValue(this._config.width);\n        style.height = coerceCssPixelValue(this._config.height);\n        style.minWidth = coerceCssPixelValue(this._config.minWidth);\n        style.minHeight = coerceCssPixelValue(this._config.minHeight);\n        style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n        style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n    }\n    /** Toggles the pointer events for the overlay pane element. */\n    _togglePointerEvents(enablePointer) {\n        this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n    }\n    /** Attaches a backdrop for this overlay. */\n    _attachBackdrop() {\n        const showingClass = 'cdk-overlay-backdrop-showing';\n        this._backdropRef?.dispose();\n        this._backdropRef = new BackdropRef(this._document, this._renderer, this._ngZone, event => {\n            this._backdropClick.next(event);\n        });\n        if (this._animationsDisabled) {\n            this._backdropRef.element.classList.add('cdk-overlay-backdrop-noop-animation');\n        }\n        if (this._config.backdropClass) {\n            this._toggleClasses(this._backdropRef.element, this._config.backdropClass, true);\n        }\n        // Insert the backdrop before the pane in the DOM order,\n        // in order to handle stacked overlays properly.\n        this._host.parentElement.insertBefore(this._backdropRef.element, this._host);\n        // Add class to fade-in the backdrop after one frame.\n        if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => this._backdropRef?.element.classList.add(showingClass));\n            });\n        }\n        else {\n            this._backdropRef.element.classList.add(showingClass);\n        }\n    }\n    /**\n     * Updates the stacking order of the element, moving it to the top if necessary.\n     * This is required in cases where one overlay was detached, while another one,\n     * that should be behind it, was destroyed. The next time both of them are opened,\n     * the stacking will be wrong, because the detached element's pane will still be\n     * in its original DOM position.\n     */\n    _updateStackingOrder() {\n        if (this._host.nextSibling) {\n            this._host.parentNode.appendChild(this._host);\n        }\n    }\n    /** Detaches the backdrop (if any) associated with the overlay. */\n    detachBackdrop() {\n        if (this._animationsDisabled) {\n            this._backdropRef?.dispose();\n            this._backdropRef = null;\n        }\n        else {\n            this._backdropRef?.detach();\n        }\n    }\n    /** Toggles a single CSS class or an array of classes on an element. */\n    _toggleClasses(element, cssClasses, isAdd) {\n        const classes = coerceArray(cssClasses || []).filter(c => !!c);\n        if (classes.length) {\n            isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n        }\n    }\n    /** Detaches the overlay content next time the zone stabilizes. */\n    _detachContentWhenEmpty() {\n        // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n        // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n        // be patched to run inside the zone, which will throw us into an infinite loop.\n        this._ngZone.runOutsideAngular(() => {\n            // We can't remove the host here immediately, because the overlay pane's content\n            // might still be animating. This stream helps us avoid interrupting the animation\n            // by waiting for the pane to become empty.\n            const subscription = this._renders\n                .pipe(takeUntil(merge(this._attachments, this._detachments)))\n                .subscribe(() => {\n                // Needs a couple of checks for the pane and host, because\n                // they may have been removed by the time the zone stabilizes.\n                if (!this._pane || !this._host || this._pane.children.length === 0) {\n                    if (this._pane && this._config.panelClass) {\n                        this._toggleClasses(this._pane, this._config.panelClass, false);\n                    }\n                    if (this._host && this._host.parentElement) {\n                        this._previousHostParent = this._host.parentElement;\n                        this._host.remove();\n                    }\n                    subscription.unsubscribe();\n                }\n            });\n        });\n    }\n    /** Disposes of a scroll strategy. */\n    _disposeScrollStrategy() {\n        const scrollStrategy = this._scrollStrategy;\n        scrollStrategy?.disable();\n        scrollStrategy?.detach?.();\n    }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n    _viewportRuler;\n    _document;\n    _platform;\n    _overlayContainer;\n    /** The overlay to which this strategy is attached. */\n    _overlayRef;\n    /** Whether we're performing the very first positioning of the overlay. */\n    _isInitialRender;\n    /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n    _lastBoundingBoxSize = { width: 0, height: 0 };\n    /** Whether the overlay was pushed in a previous positioning. */\n    _isPushed = false;\n    /** Whether the overlay can be pushed on-screen on the initial open. */\n    _canPush = true;\n    /** Whether the overlay can grow via flexible width/height after the initial open. */\n    _growAfterOpen = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    _hasFlexibleDimensions = true;\n    /** Whether the overlay position is locked. */\n    _positionLocked = false;\n    /** Cached origin dimensions */\n    _originRect;\n    /** Cached overlay dimensions */\n    _overlayRect;\n    /** Cached viewport dimensions */\n    _viewportRect;\n    /** Cached container dimensions */\n    _containerRect;\n    /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n    _viewportMargin = 0;\n    /** The Scrollable containers used to check scrollable view properties on position change. */\n    _scrollables = [];\n    /** Ordered list of preferred positions, from most to least desirable. */\n    _preferredPositions = [];\n    /** The origin element against which the overlay will be positioned. */\n    _origin;\n    /** The overlay pane element. */\n    _pane;\n    /** Whether the strategy has been disposed of already. */\n    _isDisposed;\n    /**\n     * Parent element for the overlay panel used to constrain the overlay panel's size to fit\n     * within the viewport.\n     */\n    _boundingBox;\n    /** The last position to have been calculated as the best fit position. */\n    _lastPosition;\n    /** The last calculated scroll visibility. Only tracked  */\n    _lastScrollVisibility;\n    /** Subject that emits whenever the position changes. */\n    _positionChanges = new Subject();\n    /** Subscription to viewport size changes. */\n    _resizeSubscription = Subscription.EMPTY;\n    /** Default offset for the overlay along the x axis. */\n    _offsetX = 0;\n    /** Default offset for the overlay along the y axis. */\n    _offsetY = 0;\n    /** Selector to be used when finding the elements on which to set the transform origin. */\n    _transformOriginSelector;\n    /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n    _appliedPanelClasses = [];\n    /** Amount by which the overlay was pushed in each axis during the last time it was positioned. */\n    _previousPushAmount;\n    /** Observable sequence of position changes. */\n    positionChanges = this._positionChanges;\n    /** Ordered list of preferred positions, from most to least desirable. */\n    get positions() {\n        return this._preferredPositions;\n    }\n    constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n        this.setOrigin(connectedTo);\n    }\n    /** Attaches this position strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef &&\n            overlayRef !== this._overlayRef &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('This position strategy is already attached to an overlay');\n        }\n        this._validatePositions();\n        overlayRef.hostElement.classList.add(boundingBoxClass);\n        this._overlayRef = overlayRef;\n        this._boundingBox = overlayRef.hostElement;\n        this._pane = overlayRef.overlayElement;\n        this._isDisposed = false;\n        this._isInitialRender = true;\n        this._lastPosition = null;\n        this._resizeSubscription.unsubscribe();\n        this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n            // When the window is resized, we want to trigger the next reposition as if it\n            // was an initial render, in order for the strategy to pick a new optimal position,\n            // otherwise position locking will cause it to stay at the old one.\n            this._isInitialRender = true;\n            this.apply();\n        });\n    }\n    /**\n     * Updates the position of the overlay element, using whichever preferred position relative\n     * to the origin best fits on-screen.\n     *\n     * The selection of a position goes as follows:\n     *  - If any positions fit completely within the viewport as-is,\n     *      choose the first position that does so.\n     *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n     *      choose the position with the greatest available size modified by the positions' weight.\n     *  - If pushing is enabled, take the position that went off-screen the least and push it\n     *      on-screen.\n     *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n     * @docs-private\n     */\n    apply() {\n        // We shouldn't do anything if the strategy was disposed or we're on the server.\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        // If the position has been applied already (e.g. when the overlay was opened) and the\n        // consumer opted into locking in the position, re-use the old position, in order to\n        // prevent the overlay from jumping around.\n        if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n            this.reapplyLastPosition();\n            return;\n        }\n        this._clearPanelClasses();\n        this._resetOverlayElementStyles();\n        this._resetBoundingBoxStyles();\n        // We need the bounding rects for the origin, the overlay and the container to determine how to position\n        // the overlay relative to the origin.\n        // We use the viewport rect to determine whether a position would go off-screen.\n        this._viewportRect = this._getNarrowedViewportRect();\n        this._originRect = this._getOriginRect();\n        this._overlayRect = this._pane.getBoundingClientRect();\n        this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n        const originRect = this._originRect;\n        const overlayRect = this._overlayRect;\n        const viewportRect = this._viewportRect;\n        const containerRect = this._containerRect;\n        // Positions where the overlay will fit with flexible dimensions.\n        const flexibleFits = [];\n        // Fallback if none of the preferred positions fit within the viewport.\n        let fallback;\n        // Go through each of the preferred positions looking for a good fit.\n        // If a good fit is found, it will be applied immediately.\n        for (let pos of this._preferredPositions) {\n            // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n            let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n            // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n            // overlay in this position. We use the top-left corner for calculations and later translate\n            // this into an appropriate (top, left, bottom, right) style.\n            let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n            // Calculate how well the overlay would fit into the viewport with this point.\n            let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n            // If the overlay, without any further work, fits into the viewport, use this position.\n            if (overlayFit.isCompletelyWithinViewport) {\n                this._isPushed = false;\n                this._applyPosition(pos, originPoint);\n                return;\n            }\n            // If the overlay has flexible dimensions, we can use this position\n            // so long as there's enough space for the minimum dimensions.\n            if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n                // Save positions where the overlay will fit with flexible dimensions. We will use these\n                // if none of the positions fit *without* flexible dimensions.\n                flexibleFits.push({\n                    position: pos,\n                    origin: originPoint,\n                    overlayRect,\n                    boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos),\n                });\n                continue;\n            }\n            // If the current preferred position does not fit on the screen, remember the position\n            // if it has more visible area on-screen than we've seen and move onto the next preferred\n            // position.\n            if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n                fallback = { overlayFit, overlayPoint, originPoint, position: pos, overlayRect };\n            }\n        }\n        // If there are any positions where the overlay would fit with flexible dimensions, choose the\n        // one that has the greatest area available modified by the position's weight\n        if (flexibleFits.length) {\n            let bestFit = null;\n            let bestScore = -1;\n            for (const fit of flexibleFits) {\n                const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n                if (score > bestScore) {\n                    bestScore = score;\n                    bestFit = fit;\n                }\n            }\n            this._isPushed = false;\n            this._applyPosition(bestFit.position, bestFit.origin);\n            return;\n        }\n        // When none of the preferred positions fit within the viewport, take the position\n        // that went off-screen the least and attempt to push it on-screen.\n        if (this._canPush) {\n            // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n            this._isPushed = true;\n            this._applyPosition(fallback.position, fallback.originPoint);\n            return;\n        }\n        // All options for getting the overlay within the viewport have been exhausted, so go with the\n        // position that went off-screen the least.\n        this._applyPosition(fallback.position, fallback.originPoint);\n    }\n    detach() {\n        this._clearPanelClasses();\n        this._lastPosition = null;\n        this._previousPushAmount = null;\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Cleanup after the element gets destroyed. */\n    dispose() {\n        if (this._isDisposed) {\n            return;\n        }\n        // We can't use `_resetBoundingBoxStyles` here, because it resets\n        // some properties to zero, rather than removing them.\n        if (this._boundingBox) {\n            extendStyles(this._boundingBox.style, {\n                top: '',\n                left: '',\n                right: '',\n                bottom: '',\n                height: '',\n                width: '',\n                alignItems: '',\n                justifyContent: '',\n            });\n        }\n        if (this._pane) {\n            this._resetOverlayElementStyles();\n        }\n        if (this._overlayRef) {\n            this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n        }\n        this.detach();\n        this._positionChanges.complete();\n        this._overlayRef = this._boundingBox = null;\n        this._isDisposed = true;\n    }\n    /**\n     * This re-aligns the overlay element with the trigger in its last calculated position,\n     * even if a position higher in the \"preferred positions\" list would now fit. This\n     * allows one to re-align the panel without changing the orientation of the panel.\n     */\n    reapplyLastPosition() {\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        const lastPosition = this._lastPosition;\n        if (lastPosition) {\n            this._originRect = this._getOriginRect();\n            this._overlayRect = this._pane.getBoundingClientRect();\n            this._viewportRect = this._getNarrowedViewportRect();\n            this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n            const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n            this._applyPosition(lastPosition, originPoint);\n        }\n        else {\n            this.apply();\n        }\n    }\n    /**\n     * Sets the list of Scrollable containers that host the origin element so that\n     * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n     * Scrollable must be an ancestor element of the strategy's origin element.\n     */\n    withScrollableContainers(scrollables) {\n        this._scrollables = scrollables;\n        return this;\n    }\n    /**\n     * Adds new preferred positions.\n     * @param positions List of positions options for this overlay.\n     */\n    withPositions(positions) {\n        this._preferredPositions = positions;\n        // If the last calculated position object isn't part of the positions anymore, clear\n        // it in order to avoid it being picked up if the consumer tries to re-apply.\n        if (positions.indexOf(this._lastPosition) === -1) {\n            this._lastPosition = null;\n        }\n        this._validatePositions();\n        return this;\n    }\n    /**\n     * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n     * @param margin Required margin between the overlay and the viewport edge in pixels.\n     */\n    withViewportMargin(margin) {\n        this._viewportMargin = margin;\n        return this;\n    }\n    /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n    withFlexibleDimensions(flexibleDimensions = true) {\n        this._hasFlexibleDimensions = flexibleDimensions;\n        return this;\n    }\n    /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n    withGrowAfterOpen(growAfterOpen = true) {\n        this._growAfterOpen = growAfterOpen;\n        return this;\n    }\n    /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    withPush(canPush = true) {\n        this._canPush = canPush;\n        return this;\n    }\n    /**\n     * Sets whether the overlay's position should be locked in after it is positioned\n     * initially. When an overlay is locked in, it won't attempt to reposition itself\n     * when the position is re-applied (e.g. when the user scrolls away).\n     * @param isLocked Whether the overlay should locked in.\n     */\n    withLockedPosition(isLocked = true) {\n        this._positionLocked = isLocked;\n        return this;\n    }\n    /**\n     * Sets the origin, relative to which to position the overlay.\n     * Using an element origin is useful for building components that need to be positioned\n     * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n     * used for cases like contextual menus which open relative to the user's pointer.\n     * @param origin Reference to the new origin.\n     */\n    setOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the x-axis.\n     * @param offset New offset in the X axis.\n     */\n    withDefaultOffsetX(offset) {\n        this._offsetX = offset;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the y-axis.\n     * @param offset New offset in the Y axis.\n     */\n    withDefaultOffsetY(offset) {\n        this._offsetY = offset;\n        return this;\n    }\n    /**\n     * Configures that the position strategy should set a `transform-origin` on some elements\n     * inside the overlay, depending on the current position that is being applied. This is\n     * useful for the cases where the origin of an animation can change depending on the\n     * alignment of the overlay.\n     * @param selector CSS selector that will be used to find the target\n     *    elements onto which to set the transform origin.\n     */\n    withTransformOriginOn(selector) {\n        this._transformOriginSelector = selector;\n        return this;\n    }\n    /**\n     * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n     */\n    _getOriginPoint(originRect, containerRect, pos) {\n        let x;\n        if (pos.originX == 'center') {\n            // Note: when centering we should always use the `left`\n            // offset, otherwise the position will be wrong in RTL.\n            x = originRect.left + originRect.width / 2;\n        }\n        else {\n            const startX = this._isRtl() ? originRect.right : originRect.left;\n            const endX = this._isRtl() ? originRect.left : originRect.right;\n            x = pos.originX == 'start' ? startX : endX;\n        }\n        // When zooming in Safari the container rectangle contains negative values for the position\n        // and we need to re-add them to the calculated coordinates.\n        if (containerRect.left < 0) {\n            x -= containerRect.left;\n        }\n        let y;\n        if (pos.originY == 'center') {\n            y = originRect.top + originRect.height / 2;\n        }\n        else {\n            y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n        }\n        // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n        // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n        // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n        // otherwise our positioning will be thrown off.\n        // Additionally, when zooming in Safari this fixes the vertical position.\n        if (containerRect.top < 0) {\n            y -= containerRect.top;\n        }\n        return { x, y };\n    }\n    /**\n     * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n     * origin point to which the overlay should be connected.\n     */\n    _getOverlayPoint(originPoint, overlayRect, pos) {\n        // Calculate the (overlayStartX, overlayStartY), the start of the\n        // potential overlay position relative to the origin point.\n        let overlayStartX;\n        if (pos.overlayX == 'center') {\n            overlayStartX = -overlayRect.width / 2;\n        }\n        else if (pos.overlayX === 'start') {\n            overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n        }\n        else {\n            overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n        }\n        let overlayStartY;\n        if (pos.overlayY == 'center') {\n            overlayStartY = -overlayRect.height / 2;\n        }\n        else {\n            overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n        }\n        // The (x, y) coordinates of the overlay.\n        return {\n            x: originPoint.x + overlayStartX,\n            y: originPoint.y + overlayStartY,\n        };\n    }\n    /** Gets how well an overlay at the given point will fit within the viewport. */\n    _getOverlayFit(point, rawOverlayRect, viewport, position) {\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        let { x, y } = point;\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        // Account for the offsets since they could push the overlay out of the viewport.\n        if (offsetX) {\n            x += offsetX;\n        }\n        if (offsetY) {\n            y += offsetY;\n        }\n        // How much the overlay would overflow at this position, on each side.\n        let leftOverflow = 0 - x;\n        let rightOverflow = x + overlay.width - viewport.width;\n        let topOverflow = 0 - y;\n        let bottomOverflow = y + overlay.height - viewport.height;\n        // Visible parts of the element on each axis.\n        let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n        let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n        let visibleArea = visibleWidth * visibleHeight;\n        return {\n            visibleArea,\n            isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n            fitsInViewportVertically: visibleHeight === overlay.height,\n            fitsInViewportHorizontally: visibleWidth == overlay.width,\n        };\n    }\n    /**\n     * Whether the overlay can fit within the viewport when it may resize either its width or height.\n     * @param fit How well the overlay fits in the viewport at some position.\n     * @param point The (x, y) coordinates of the overlay at some position.\n     * @param viewport The geometry of the viewport.\n     */\n    _canFitWithFlexibleDimensions(fit, point, viewport) {\n        if (this._hasFlexibleDimensions) {\n            const availableHeight = viewport.bottom - point.y;\n            const availableWidth = viewport.right - point.x;\n            const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n            const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n            const verticalFit = fit.fitsInViewportVertically || (minHeight != null && minHeight <= availableHeight);\n            const horizontalFit = fit.fitsInViewportHorizontally || (minWidth != null && minWidth <= availableWidth);\n            return verticalFit && horizontalFit;\n        }\n        return false;\n    }\n    /**\n     * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n     * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n     * right and bottom).\n     *\n     * @param start Starting point from which the overlay is pushed.\n     * @param rawOverlayRect Dimensions of the overlay.\n     * @param scrollPosition Current viewport scroll position.\n     * @returns The point at which to position the overlay after pushing. This is effectively a new\n     *     originPoint.\n     */\n    _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n        // If the position is locked and we've pushed the overlay already, reuse the previous push\n        // amount, rather than pushing it again. If we were to continue pushing, the element would\n        // remain in the viewport, which goes against the expectations when position locking is enabled.\n        if (this._previousPushAmount && this._positionLocked) {\n            return {\n                x: start.x + this._previousPushAmount.x,\n                y: start.y + this._previousPushAmount.y,\n            };\n        }\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        const viewport = this._viewportRect;\n        // Determine how much the overlay goes outside the viewport on each\n        // side, which we'll use to decide which direction to push it.\n        const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n        const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n        const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n        const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n        // Amount by which to push the overlay in each axis such that it remains on-screen.\n        let pushX = 0;\n        let pushY = 0;\n        // If the overlay fits completely within the bounds of the viewport, push it from whichever\n        // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n        // viewport and allow for the trailing end of the overlay to go out of bounds.\n        if (overlay.width <= viewport.width) {\n            pushX = overflowLeft || -overflowRight;\n        }\n        else {\n            pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n        }\n        if (overlay.height <= viewport.height) {\n            pushY = overflowTop || -overflowBottom;\n        }\n        else {\n            pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n        }\n        this._previousPushAmount = { x: pushX, y: pushY };\n        return {\n            x: start.x + pushX,\n            y: start.y + pushY,\n        };\n    }\n    /**\n     * Applies a computed position to the overlay and emits a position change.\n     * @param position The position preference\n     * @param originPoint The point on the origin element where the overlay is connected.\n     */\n    _applyPosition(position, originPoint) {\n        this._setTransformOrigin(position);\n        this._setOverlayElementStyles(originPoint, position);\n        this._setBoundingBoxStyles(originPoint, position);\n        if (position.panelClass) {\n            this._addPanelClasses(position.panelClass);\n        }\n        // Notify that the position has been changed along with its change properties.\n        // We only emit if we've got any subscriptions, because the scroll visibility\n        // calculations can be somewhat expensive.\n        if (this._positionChanges.observers.length) {\n            const scrollVisibility = this._getScrollVisibility();\n            // We're recalculating on scroll, but we only want to emit if anything\n            // changed since downstream code might be hitting the `NgZone`.\n            if (position !== this._lastPosition ||\n                !this._lastScrollVisibility ||\n                !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)) {\n                const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n                this._positionChanges.next(changeEvent);\n            }\n            this._lastScrollVisibility = scrollVisibility;\n        }\n        // Save the last connected position in case the position needs to be re-calculated.\n        this._lastPosition = position;\n        this._isInitialRender = false;\n    }\n    /** Sets the transform origin based on the configured selector and the passed-in position.  */\n    _setTransformOrigin(position) {\n        if (!this._transformOriginSelector) {\n            return;\n        }\n        const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n        let xOrigin;\n        let yOrigin = position.overlayY;\n        if (position.overlayX === 'center') {\n            xOrigin = 'center';\n        }\n        else if (this._isRtl()) {\n            xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n        }\n        else {\n            xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n        }\n        for (let i = 0; i < elements.length; i++) {\n            elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n        }\n    }\n    /**\n     * Gets the position and size of the overlay's sizing container.\n     *\n     * This method does no measuring and applies no styles so that we can cheaply compute the\n     * bounds for all positions and choose the best fit based on these results.\n     */\n    _calculateBoundingBoxRect(origin, position) {\n        const viewport = this._viewportRect;\n        const isRtl = this._isRtl();\n        let height, top, bottom;\n        if (position.overlayY === 'top') {\n            // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n            top = origin.y;\n            height = viewport.height - top + this._viewportMargin;\n        }\n        else if (position.overlayY === 'bottom') {\n            // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n            // the viewport margin back in, because the viewport rect is narrowed down to remove the\n            // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n            bottom = viewport.height - origin.y + this._viewportMargin * 2;\n            height = viewport.height - bottom + this._viewportMargin;\n        }\n        else {\n            // If neither top nor bottom, it means that the overlay is vertically centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n            // `origin.y - viewport.top`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n            const previousHeight = this._lastBoundingBoxSize.height;\n            height = smallestDistanceToViewportEdge * 2;\n            top = origin.y - smallestDistanceToViewportEdge;\n            if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n                top = origin.y - previousHeight / 2;\n            }\n        }\n        // The overlay is opening 'right-ward' (the content flows to the right).\n        const isBoundedByRightViewportEdge = (position.overlayX === 'start' && !isRtl) || (position.overlayX === 'end' && isRtl);\n        // The overlay is opening 'left-ward' (the content flows to the left).\n        const isBoundedByLeftViewportEdge = (position.overlayX === 'end' && !isRtl) || (position.overlayX === 'start' && isRtl);\n        let width, left, right;\n        if (isBoundedByLeftViewportEdge) {\n            right = viewport.width - origin.x + this._viewportMargin * 2;\n            width = origin.x - this._viewportMargin;\n        }\n        else if (isBoundedByRightViewportEdge) {\n            left = origin.x;\n            width = viewport.right - origin.x;\n        }\n        else {\n            // If neither start nor end, it means that the overlay is horizontally centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.right - origin.x` and\n            // `origin.x - viewport.left`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n            const previousWidth = this._lastBoundingBoxSize.width;\n            width = smallestDistanceToViewportEdge * 2;\n            left = origin.x - smallestDistanceToViewportEdge;\n            if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n                left = origin.x - previousWidth / 2;\n            }\n        }\n        return { top: top, left: left, bottom: bottom, right: right, width, height };\n    }\n    /**\n     * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n     * origin's connection point and stretches to the bounds of the viewport.\n     *\n     * @param origin The point on the origin element where the overlay is connected.\n     * @param position The position preference\n     */\n    _setBoundingBoxStyles(origin, position) {\n        const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n        // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n        // when applying a new size.\n        if (!this._isInitialRender && !this._growAfterOpen) {\n            boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n            boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n        }\n        const styles = {};\n        if (this._hasExactPosition()) {\n            styles.top = styles.left = '0';\n            styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n            styles.width = styles.height = '100%';\n        }\n        else {\n            const maxHeight = this._overlayRef.getConfig().maxHeight;\n            const maxWidth = this._overlayRef.getConfig().maxWidth;\n            styles.height = coerceCssPixelValue(boundingBoxRect.height);\n            styles.top = coerceCssPixelValue(boundingBoxRect.top);\n            styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n            styles.width = coerceCssPixelValue(boundingBoxRect.width);\n            styles.left = coerceCssPixelValue(boundingBoxRect.left);\n            styles.right = coerceCssPixelValue(boundingBoxRect.right);\n            // Push the pane content towards the proper direction.\n            if (position.overlayX === 'center') {\n                styles.alignItems = 'center';\n            }\n            else {\n                styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n            }\n            if (position.overlayY === 'center') {\n                styles.justifyContent = 'center';\n            }\n            else {\n                styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n            }\n            if (maxHeight) {\n                styles.maxHeight = coerceCssPixelValue(maxHeight);\n            }\n            if (maxWidth) {\n                styles.maxWidth = coerceCssPixelValue(maxWidth);\n            }\n        }\n        this._lastBoundingBoxSize = boundingBoxRect;\n        extendStyles(this._boundingBox.style, styles);\n    }\n    /** Resets the styles for the bounding box so that a new positioning can be computed. */\n    _resetBoundingBoxStyles() {\n        extendStyles(this._boundingBox.style, {\n            top: '0',\n            left: '0',\n            right: '0',\n            bottom: '0',\n            height: '',\n            width: '',\n            alignItems: '',\n            justifyContent: '',\n        });\n    }\n    /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n    _resetOverlayElementStyles() {\n        extendStyles(this._pane.style, {\n            top: '',\n            left: '',\n            bottom: '',\n            right: '',\n            position: '',\n            transform: '',\n        });\n    }\n    /** Sets positioning styles to the overlay element. */\n    _setOverlayElementStyles(originPoint, position) {\n        const styles = {};\n        const hasExactPosition = this._hasExactPosition();\n        const hasFlexibleDimensions = this._hasFlexibleDimensions;\n        const config = this._overlayRef.getConfig();\n        if (hasExactPosition) {\n            const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n            extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n            extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n        }\n        else {\n            styles.position = 'static';\n        }\n        // Use a transform to apply the offsets. We do this because the `center` positions rely on\n        // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n        // off the position. We also can't use margins, because they won't have an effect in some\n        // cases where the element doesn't have anything to \"push off of\". Finally, this works\n        // better both with flexible and non-flexible positioning.\n        let transformString = '';\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        if (offsetX) {\n            transformString += `translateX(${offsetX}px) `;\n        }\n        if (offsetY) {\n            transformString += `translateY(${offsetY}px)`;\n        }\n        styles.transform = transformString.trim();\n        // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n        // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n        // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n        // Note that this doesn't apply when we have an exact position, in which case we do want to\n        // apply them because they'll be cleared from the bounding box.\n        if (config.maxHeight) {\n            if (hasExactPosition) {\n                styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxHeight = '';\n            }\n        }\n        if (config.maxWidth) {\n            if (hasExactPosition) {\n                styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxWidth = '';\n            }\n        }\n        extendStyles(this._pane.style, styles);\n    }\n    /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayY(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the\n        // preferred position has changed since the last `apply`.\n        let styles = { top: '', bottom: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n        // above or below the origin and the direction in which the element will expand.\n        if (position.overlayY === 'bottom') {\n            // When using `bottom`, we adjust the y position such that it is the distance\n            // from the bottom of the viewport rather than the top.\n            const documentHeight = this._document.documentElement.clientHeight;\n            styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n        }\n        else {\n            styles.top = coerceCssPixelValue(overlayPoint.y);\n        }\n        return styles;\n    }\n    /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayX(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the preferred position has\n        // changed since the last `apply`.\n        let styles = { left: '', right: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n        // or \"after\" the origin, which determines the direction in which the element will expand.\n        // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n        // page is in RTL or LTR.\n        let horizontalStyleProperty;\n        if (this._isRtl()) {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n        }\n        else {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n        }\n        // When we're setting `right`, we adjust the x position such that it is the distance\n        // from the right edge of the viewport rather than the left edge.\n        if (horizontalStyleProperty === 'right') {\n            const documentWidth = this._document.documentElement.clientWidth;\n            styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n        }\n        else {\n            styles.left = coerceCssPixelValue(overlayPoint.x);\n        }\n        return styles;\n    }\n    /**\n     * Gets the view properties of the trigger and overlay, including whether they are clipped\n     * or completely outside the view of any of the strategy's scrollables.\n     */\n    _getScrollVisibility() {\n        // Note: needs fresh rects since the position could've changed.\n        const originBounds = this._getOriginRect();\n        const overlayBounds = this._pane.getBoundingClientRect();\n        // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n        // every time, we should be able to use the scrollTop of the containers if the size of those\n        // containers hasn't changed.\n        const scrollContainerBounds = this._scrollables.map(scrollable => {\n            return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n        });\n        return {\n            isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n            isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n            isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n            isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n        };\n    }\n    /** Subtracts the amount that an element is overflowing on an axis from its length. */\n    _subtractOverflows(length, ...overflows) {\n        return overflows.reduce((currentValue, currentOverflow) => {\n            return currentValue - Math.max(currentOverflow, 0);\n        }, length);\n    }\n    /** Narrows the given viewport rect by the current _viewportMargin. */\n    _getNarrowedViewportRect() {\n        // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n        // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n        // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n        // and `innerHeight` that do. This is necessary, because the overlay container uses\n        // 100% `width` and `height` which don't include the scrollbar either.\n        const width = this._document.documentElement.clientWidth;\n        const height = this._document.documentElement.clientHeight;\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n        return {\n            top: scrollPosition.top + this._viewportMargin,\n            left: scrollPosition.left + this._viewportMargin,\n            right: scrollPosition.left + width - this._viewportMargin,\n            bottom: scrollPosition.top + height - this._viewportMargin,\n            width: width - 2 * this._viewportMargin,\n            height: height - 2 * this._viewportMargin,\n        };\n    }\n    /** Whether the we're dealing with an RTL context */\n    _isRtl() {\n        return this._overlayRef.getDirection() === 'rtl';\n    }\n    /** Determines whether the overlay uses exact or flexible positioning. */\n    _hasExactPosition() {\n        return !this._hasFlexibleDimensions || this._isPushed;\n    }\n    /** Retrieves the offset of a position along the x or y axis. */\n    _getOffset(position, axis) {\n        if (axis === 'x') {\n            // We don't do something like `position['offset' + axis]` in\n            // order to avoid breaking minifiers that rename properties.\n            return position.offsetX == null ? this._offsetX : position.offsetX;\n        }\n        return position.offsetY == null ? this._offsetY : position.offsetY;\n    }\n    /** Validates that the current position match the expected values. */\n    _validatePositions() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!this._preferredPositions.length) {\n                throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n            }\n            // TODO(crisbeto): remove these once Angular's template type\n            // checking is advanced enough to catch these cases.\n            this._preferredPositions.forEach(pair => {\n                validateHorizontalPosition('originX', pair.originX);\n                validateVerticalPosition('originY', pair.originY);\n                validateHorizontalPosition('overlayX', pair.overlayX);\n                validateVerticalPosition('overlayY', pair.overlayY);\n            });\n        }\n    }\n    /** Adds a single CSS class or an array of classes on the overlay panel. */\n    _addPanelClasses(cssClasses) {\n        if (this._pane) {\n            coerceArray(cssClasses).forEach(cssClass => {\n                if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n                    this._appliedPanelClasses.push(cssClass);\n                    this._pane.classList.add(cssClass);\n                }\n            });\n        }\n    }\n    /** Clears the classes that the position strategy has applied from the overlay panel. */\n    _clearPanelClasses() {\n        if (this._pane) {\n            this._appliedPanelClasses.forEach(cssClass => {\n                this._pane.classList.remove(cssClass);\n            });\n            this._appliedPanelClasses = [];\n        }\n    }\n    /** Returns the DOMRect of the current origin. */\n    _getOriginRect() {\n        const origin = this._origin;\n        if (origin instanceof ElementRef) {\n            return origin.nativeElement.getBoundingClientRect();\n        }\n        // Check for Element so SVG elements are also supported.\n        if (origin instanceof Element) {\n            return origin.getBoundingClientRect();\n        }\n        const width = origin.width || 0;\n        const height = origin.height || 0;\n        // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n        return {\n            top: origin.y,\n            bottom: origin.y + height,\n            left: origin.x,\n            right: origin.x + width,\n            height,\n            width,\n        };\n    }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            destination[key] = source[key];\n        }\n    }\n    return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n    if (typeof input !== 'number' && input != null) {\n        const [value, units] = input.split(cssUnitPattern);\n        return !units || units === 'px' ? parseFloat(value) : null;\n    }\n    return input || null;\n}\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n    return {\n        top: Math.floor(clientRect.top),\n        right: Math.floor(clientRect.right),\n        bottom: Math.floor(clientRect.bottom),\n        left: Math.floor(clientRect.left),\n        width: Math.floor(clientRect.width),\n        height: Math.floor(clientRect.height),\n    };\n}\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a, b) {\n    if (a === b) {\n        return true;\n    }\n    return (a.isOriginClipped === b.isOriginClipped &&\n        a.isOriginOutsideView === b.isOriginOutsideView &&\n        a.isOverlayClipped === b.isOverlayClipped &&\n        a.isOverlayOutsideView === b.isOverlayOutsideView);\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [\n    { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n    { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n    { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom' },\n];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [\n    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top' },\n    { originX: 'end', originY: 'bottom', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top' },\n    { originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'bottom' },\n];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n    /** The overlay to which this strategy is attached. */\n    _overlayRef;\n    _cssPosition = 'static';\n    _topOffset = '';\n    _bottomOffset = '';\n    _alignItems = '';\n    _xPosition = '';\n    _xOffset = '';\n    _width = '';\n    _height = '';\n    _isDisposed = false;\n    attach(overlayRef) {\n        const config = overlayRef.getConfig();\n        this._overlayRef = overlayRef;\n        if (this._width && !config.width) {\n            overlayRef.updateSize({ width: this._width });\n        }\n        if (this._height && !config.height) {\n            overlayRef.updateSize({ height: this._height });\n        }\n        overlayRef.hostElement.classList.add(wrapperClass);\n        this._isDisposed = false;\n    }\n    /**\n     * Sets the top position of the overlay. Clears any previously set vertical position.\n     * @param value New top offset.\n     */\n    top(value = '') {\n        this._bottomOffset = '';\n        this._topOffset = value;\n        this._alignItems = 'flex-start';\n        return this;\n    }\n    /**\n     * Sets the left position of the overlay. Clears any previously set horizontal position.\n     * @param value New left offset.\n     */\n    left(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'left';\n        return this;\n    }\n    /**\n     * Sets the bottom position of the overlay. Clears any previously set vertical position.\n     * @param value New bottom offset.\n     */\n    bottom(value = '') {\n        this._topOffset = '';\n        this._bottomOffset = value;\n        this._alignItems = 'flex-end';\n        return this;\n    }\n    /**\n     * Sets the right position of the overlay. Clears any previously set horizontal position.\n     * @param value New right offset.\n     */\n    right(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'right';\n        return this;\n    }\n    /**\n     * Sets the overlay to the start of the viewport, depending on the overlay direction.\n     * This will be to the left in LTR layouts and to the right in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    start(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'start';\n        return this;\n    }\n    /**\n     * Sets the overlay to the end of the viewport, depending on the overlay direction.\n     * This will be to the right in LTR layouts and to the left in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    end(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'end';\n        return this;\n    }\n    /**\n     * Sets the overlay width and clears any previously set width.\n     * @param value New width for the overlay\n     * @deprecated Pass the `width` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    width(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ width: value });\n        }\n        else {\n            this._width = value;\n        }\n        return this;\n    }\n    /**\n     * Sets the overlay height and clears any previously set height.\n     * @param value New height for the overlay\n     * @deprecated Pass the `height` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    height(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ height: value });\n        }\n        else {\n            this._height = value;\n        }\n        return this;\n    }\n    /**\n     * Centers the overlay horizontally with an optional offset.\n     * Clears any previously set horizontal position.\n     *\n     * @param offset Overlay offset from the horizontal center.\n     */\n    centerHorizontally(offset = '') {\n        this.left(offset);\n        this._xPosition = 'center';\n        return this;\n    }\n    /**\n     * Centers the overlay vertically with an optional offset.\n     * Clears any previously set vertical position.\n     *\n     * @param offset Overlay offset from the vertical center.\n     */\n    centerVertically(offset = '') {\n        this.top(offset);\n        this._alignItems = 'center';\n        return this;\n    }\n    /**\n     * Apply the position to the element.\n     * @docs-private\n     */\n    apply() {\n        // Since the overlay ref applies the strategy asynchronously, it could\n        // have been disposed before it ends up being applied. If that is the\n        // case, we shouldn't do anything.\n        if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parentStyles = this._overlayRef.hostElement.style;\n        const config = this._overlayRef.getConfig();\n        const { width, height, maxWidth, maxHeight } = config;\n        const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') &&\n            (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n        const shouldBeFlushVertically = (height === '100%' || height === '100vh') &&\n            (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n        const xPosition = this._xPosition;\n        const xOffset = this._xOffset;\n        const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n        let marginLeft = '';\n        let marginRight = '';\n        let justifyContent = '';\n        if (shouldBeFlushHorizontally) {\n            justifyContent = 'flex-start';\n        }\n        else if (xPosition === 'center') {\n            justifyContent = 'center';\n            if (isRtl) {\n                marginRight = xOffset;\n            }\n            else {\n                marginLeft = xOffset;\n            }\n        }\n        else if (isRtl) {\n            if (xPosition === 'left' || xPosition === 'end') {\n                justifyContent = 'flex-end';\n                marginLeft = xOffset;\n            }\n            else if (xPosition === 'right' || xPosition === 'start') {\n                justifyContent = 'flex-start';\n                marginRight = xOffset;\n            }\n        }\n        else if (xPosition === 'left' || xPosition === 'start') {\n            justifyContent = 'flex-start';\n            marginLeft = xOffset;\n        }\n        else if (xPosition === 'right' || xPosition === 'end') {\n            justifyContent = 'flex-end';\n            marginRight = xOffset;\n        }\n        styles.position = this._cssPosition;\n        styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n        styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n        styles.marginBottom = this._bottomOffset;\n        styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n        parentStyles.justifyContent = justifyContent;\n        parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n    }\n    /**\n     * Cleans up the DOM changes from the position strategy.\n     * @docs-private\n     */\n    dispose() {\n        if (this._isDisposed || !this._overlayRef) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parent = this._overlayRef.hostElement;\n        const parentStyles = parent.style;\n        parent.classList.remove(wrapperClass);\n        parentStyles.justifyContent =\n            parentStyles.alignItems =\n                styles.marginTop =\n                    styles.marginBottom =\n                        styles.marginLeft =\n                            styles.marginRight =\n                                styles.position =\n                                    '';\n        this._overlayRef = null;\n        this._isDisposed = true;\n    }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n    _viewportRuler = inject(ViewportRuler);\n    _document = inject(DOCUMENT);\n    _platform = inject(Platform);\n    _overlayContainer = inject(OverlayContainer);\n    constructor() { }\n    /**\n     * Creates a global position strategy.\n     */\n    global() {\n        return new GlobalPositionStrategy();\n    }\n    /**\n     * Creates a flexible position strategy.\n     * @param origin Origin relative to which to position the overlay.\n     */\n    flexibleConnectedTo(origin) {\n        return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayPositionBuilder, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayPositionBuilder, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayPositionBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n    scrollStrategies = inject(ScrollStrategyOptions);\n    _overlayContainer = inject(OverlayContainer);\n    _positionBuilder = inject(OverlayPositionBuilder);\n    _keyboardDispatcher = inject(OverlayKeyboardDispatcher);\n    _injector = inject(Injector);\n    _ngZone = inject(NgZone);\n    _document = inject(DOCUMENT);\n    _directionality = inject(Directionality);\n    _location = inject(Location);\n    _outsideClickDispatcher = inject(OverlayOutsideClickDispatcher);\n    _animationsModuleType = inject(ANIMATION_MODULE_TYPE, { optional: true });\n    _idGenerator = inject(_IdGenerator);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _appRef;\n    _styleLoader = inject(_CdkPrivateStyleLoader);\n    constructor() { }\n    /**\n     * Creates an overlay.\n     * @param config Configuration applied to the overlay.\n     * @returns Reference to the created overlay.\n     */\n    create(config) {\n        // This is done in the overlay container as well, but we have it here\n        // since it's common to mock out the overlay container in tests.\n        this._styleLoader.load(_CdkOverlayStyleLoader);\n        const host = this._createHostElement();\n        const pane = this._createPaneElement(host);\n        const portalOutlet = this._createPortalOutlet(pane);\n        const overlayConfig = new OverlayConfig(config);\n        overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n        return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations', this._injector.get(EnvironmentInjector), this._renderer);\n    }\n    /**\n     * Gets a position builder that can be used, via fluent API,\n     * to construct and configure a position strategy.\n     * @returns An overlay position builder.\n     */\n    position() {\n        return this._positionBuilder;\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n    _createPaneElement(host) {\n        const pane = this._document.createElement('div');\n        pane.id = this._idGenerator.getId('cdk-overlay-');\n        pane.classList.add('cdk-overlay-pane');\n        host.appendChild(pane);\n        return pane;\n    }\n    /**\n     * Creates the host element that wraps around an overlay\n     * and can be used for advanced positioning.\n     * @returns Newly-create host element.\n     */\n    _createHostElement() {\n        const host = this._document.createElement('div');\n        this._overlayContainer.getContainerElement().appendChild(host);\n        return host;\n    }\n    /**\n     * Create a DomPortalOutlet into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal outlet.\n     * @returns A portal outlet for the given DOM element.\n     */\n    _createPortalOutlet(pane) {\n        // We have to resolve the ApplicationRef later in order to allow people\n        // to use overlay-based providers during app initialization.\n        if (!this._appRef) {\n            this._appRef = this._injector.get(ApplicationRef);\n        }\n        return new DomPortalOutlet(pane, null, this._appRef, this._injector, this._document);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Overlay, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Overlay, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Overlay, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [\n    {\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top',\n    },\n    {\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top',\n    },\n];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition();\n    },\n});\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n    elementRef = inject(ElementRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkOverlayOrigin, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkOverlayOrigin, isStandalone: true, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkOverlayOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n                    exportAs: 'cdkOverlayOrigin',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n    _overlay = inject(Overlay);\n    _dir = inject(Directionality, { optional: true });\n    _overlayRef;\n    _templatePortal;\n    _backdropSubscription = Subscription.EMPTY;\n    _attachSubscription = Subscription.EMPTY;\n    _detachSubscription = Subscription.EMPTY;\n    _positionSubscription = Subscription.EMPTY;\n    _offsetX;\n    _offsetY;\n    _position;\n    _scrollStrategyFactory = inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY);\n    _disposeOnNavigation = false;\n    _ngZone = inject(NgZone);\n    /** Origin for the connected overlay. */\n    origin;\n    /** Registered connected position pairs. */\n    positions;\n    /**\n     * This input overrides the positions input if specified. It lets users pass\n     * in arbitrary positioning strategies.\n     */\n    positionStrategy;\n    /** The offset in pixels for the overlay connection point on the x-axis */\n    get offsetX() {\n        return this._offsetX;\n    }\n    set offsetX(offsetX) {\n        this._offsetX = offsetX;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The offset in pixels for the overlay connection point on the y-axis */\n    get offsetY() {\n        return this._offsetY;\n    }\n    set offsetY(offsetY) {\n        this._offsetY = offsetY;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The width of the overlay panel. */\n    width;\n    /** The height of the overlay panel. */\n    height;\n    /** The min width of the overlay panel. */\n    minWidth;\n    /** The min height of the overlay panel. */\n    minHeight;\n    /** The custom class to be set on the backdrop element. */\n    backdropClass;\n    /** The custom class to add to the overlay pane element. */\n    panelClass;\n    /** Margin between the overlay and the viewport edges. */\n    viewportMargin = 0;\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    scrollStrategy;\n    /** Whether the overlay is open. */\n    open = false;\n    /** Whether the overlay can be closed by user interaction. */\n    disableClose = false;\n    /** CSS selector which to set the transform origin. */\n    transformOriginSelector;\n    /** Whether or not the overlay should attach a backdrop. */\n    hasBackdrop = false;\n    /** Whether or not the overlay should be locked when scrolling. */\n    lockPosition = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    flexibleDimensions = false;\n    /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n    growAfterOpen = false;\n    /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    push = false;\n    /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n    get disposeOnNavigation() {\n        return this._disposeOnNavigation;\n    }\n    set disposeOnNavigation(value) {\n        this._disposeOnNavigation = value;\n    }\n    /** Event emitted when the backdrop is clicked. */\n    backdropClick = new EventEmitter();\n    /** Event emitted when the position has changed. */\n    positionChange = new EventEmitter();\n    /** Event emitted when the overlay has been attached. */\n    attach = new EventEmitter();\n    /** Event emitted when the overlay has been detached. */\n    detach = new EventEmitter();\n    /** Emits when there are keyboard events that are targeted at the overlay. */\n    overlayKeydown = new EventEmitter();\n    /** Emits when there are mouse outside click events that are targeted at the overlay. */\n    overlayOutsideClick = new EventEmitter();\n    // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n    constructor() {\n        const templateRef = inject(TemplateRef);\n        const viewContainerRef = inject(ViewContainerRef);\n        this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n        this.scrollStrategy = this._scrollStrategyFactory();\n    }\n    /** The associated overlay reference. */\n    get overlayRef() {\n        return this._overlayRef;\n    }\n    /** The element's layout direction. */\n    get dir() {\n        return this._dir ? this._dir.value : 'ltr';\n    }\n    ngOnDestroy() {\n        this._attachSubscription.unsubscribe();\n        this._detachSubscription.unsubscribe();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        this._overlayRef?.dispose();\n    }\n    ngOnChanges(changes) {\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n            this._overlayRef?.updateSize({\n                width: this.width,\n                minWidth: this.minWidth,\n                height: this.height,\n                minHeight: this.minHeight,\n            });\n            if (changes['origin'] && this.open) {\n                this._position.apply();\n            }\n        }\n        if (changes['open']) {\n            this.open ? this.attachOverlay() : this.detachOverlay();\n        }\n    }\n    /** Creates an overlay */\n    _createOverlay() {\n        if (!this.positions || !this.positions.length) {\n            this.positions = defaultPositionList;\n        }\n        const overlayRef = (this._overlayRef = this._overlay.create(this._buildConfig()));\n        this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n        this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n        overlayRef.keydownEvents().subscribe((event) => {\n            this.overlayKeydown.next(event);\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.detachOverlay();\n            }\n        });\n        this._overlayRef.outsidePointerEvents().subscribe((event) => {\n            const origin = this._getOriginElement();\n            const target = _getEventTarget(event);\n            if (!origin || (origin !== target && !origin.contains(target))) {\n                this.overlayOutsideClick.next(event);\n            }\n        });\n    }\n    /** Builds the overlay config based on the directive's inputs */\n    _buildConfig() {\n        const positionStrategy = (this._position =\n            this.positionStrategy || this._createPositionStrategy());\n        const overlayConfig = new OverlayConfig({\n            direction: this._dir || 'ltr',\n            positionStrategy,\n            scrollStrategy: this.scrollStrategy,\n            hasBackdrop: this.hasBackdrop,\n            disposeOnNavigation: this.disposeOnNavigation,\n        });\n        if (this.width || this.width === 0) {\n            overlayConfig.width = this.width;\n        }\n        if (this.height || this.height === 0) {\n            overlayConfig.height = this.height;\n        }\n        if (this.minWidth || this.minWidth === 0) {\n            overlayConfig.minWidth = this.minWidth;\n        }\n        if (this.minHeight || this.minHeight === 0) {\n            overlayConfig.minHeight = this.minHeight;\n        }\n        if (this.backdropClass) {\n            overlayConfig.backdropClass = this.backdropClass;\n        }\n        if (this.panelClass) {\n            overlayConfig.panelClass = this.panelClass;\n        }\n        return overlayConfig;\n    }\n    /** Updates the state of a position strategy, based on the values of the directive inputs. */\n    _updatePositionStrategy(positionStrategy) {\n        const positions = this.positions.map(currentPosition => ({\n            originX: currentPosition.originX,\n            originY: currentPosition.originY,\n            overlayX: currentPosition.overlayX,\n            overlayY: currentPosition.overlayY,\n            offsetX: currentPosition.offsetX || this.offsetX,\n            offsetY: currentPosition.offsetY || this.offsetY,\n            panelClass: currentPosition.panelClass || undefined,\n        }));\n        return positionStrategy\n            .setOrigin(this._getOrigin())\n            .withPositions(positions)\n            .withFlexibleDimensions(this.flexibleDimensions)\n            .withPush(this.push)\n            .withGrowAfterOpen(this.growAfterOpen)\n            .withViewportMargin(this.viewportMargin)\n            .withLockedPosition(this.lockPosition)\n            .withTransformOriginOn(this.transformOriginSelector);\n    }\n    /** Returns the position strategy of the overlay to be set on the overlay config */\n    _createPositionStrategy() {\n        const strategy = this._overlay.position().flexibleConnectedTo(this._getOrigin());\n        this._updatePositionStrategy(strategy);\n        return strategy;\n    }\n    _getOrigin() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef;\n        }\n        else {\n            return this.origin;\n        }\n    }\n    _getOriginElement() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef.nativeElement;\n        }\n        if (this.origin instanceof ElementRef) {\n            return this.origin.nativeElement;\n        }\n        if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n            return this.origin;\n        }\n        return null;\n    }\n    /** Attaches the overlay. */\n    attachOverlay() {\n        if (!this._overlayRef) {\n            this._createOverlay();\n        }\n        else {\n            // Update the overlay size, in case the directive's inputs have changed\n            this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n        }\n        if (!this._overlayRef.hasAttached()) {\n            this._overlayRef.attach(this._templatePortal);\n        }\n        if (this.hasBackdrop) {\n            this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n                this.backdropClick.emit(event);\n            });\n        }\n        else {\n            this._backdropSubscription.unsubscribe();\n        }\n        this._positionSubscription.unsubscribe();\n        // Only subscribe to `positionChanges` if requested, because putting\n        // together all the information for it can be expensive.\n        if (this.positionChange.observers.length > 0) {\n            this._positionSubscription = this._position.positionChanges\n                .pipe(takeWhile(() => this.positionChange.observers.length > 0))\n                .subscribe(position => {\n                this._ngZone.run(() => this.positionChange.emit(position));\n                if (this.positionChange.observers.length === 0) {\n                    this._positionSubscription.unsubscribe();\n                }\n            });\n        }\n        this.open = true;\n    }\n    /** Detaches the overlay. */\n    detachOverlay() {\n        this._overlayRef?.detach();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        this.open = false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkConnectedOverlay, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkConnectedOverlay, isStandalone: true, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: { origin: [\"cdkConnectedOverlayOrigin\", \"origin\"], positions: [\"cdkConnectedOverlayPositions\", \"positions\"], positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"], offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"], offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"], width: [\"cdkConnectedOverlayWidth\", \"width\"], height: [\"cdkConnectedOverlayHeight\", \"height\"], minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"], minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"], backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"], panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"], viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"], scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"], open: [\"cdkConnectedOverlayOpen\", \"open\"], disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"], transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"], hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\", booleanAttribute], lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\", booleanAttribute], flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\", booleanAttribute], growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\", booleanAttribute], push: [\"cdkConnectedOverlayPush\", \"push\", booleanAttribute], disposeOnNavigation: [\"cdkConnectedOverlayDisposeOnNavigation\", \"disposeOnNavigation\", booleanAttribute] }, outputs: { backdropClick: \"backdropClick\", positionChange: \"positionChange\", attach: \"attach\", detach: \"detach\", overlayKeydown: \"overlayKeydown\", overlayOutsideClick: \"overlayOutsideClick\" }, exportAs: [\"cdkConnectedOverlay\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkConnectedOverlay, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n                    exportAs: 'cdkConnectedOverlay',\n                }]\n        }], ctorParameters: () => [], propDecorators: { origin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOrigin']\n            }], positions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositions']\n            }], positionStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositionStrategy']\n            }], offsetX: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetX']\n            }], offsetY: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetY']\n            }], width: [{\n                type: Input,\n                args: ['cdkConnectedOverlayWidth']\n            }], height: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHeight']\n            }], minWidth: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinWidth']\n            }], minHeight: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinHeight']\n            }], backdropClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayBackdropClass']\n            }], panelClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPanelClass']\n            }], viewportMargin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayViewportMargin']\n            }], scrollStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayScrollStrategy']\n            }], open: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOpen']\n            }], disableClose: [{\n                type: Input,\n                args: ['cdkConnectedOverlayDisableClose']\n            }], transformOriginSelector: [{\n                type: Input,\n                args: ['cdkConnectedOverlayTransformOriginOn']\n            }], hasBackdrop: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayHasBackdrop', transform: booleanAttribute }]\n            }], lockPosition: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayLockPosition', transform: booleanAttribute }]\n            }], flexibleDimensions: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayFlexibleDimensions', transform: booleanAttribute }]\n            }], growAfterOpen: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayGrowAfterOpen', transform: booleanAttribute }]\n            }], push: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayPush', transform: booleanAttribute }]\n            }], disposeOnNavigation: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayDisposeOnNavigation', transform: booleanAttribute }]\n            }], backdropClick: [{\n                type: Output\n            }], positionChange: [{\n                type: Output\n            }], attach: [{\n                type: Output\n            }], detach: [{\n                type: Output\n            }], overlayKeydown: [{\n                type: Output\n            }], overlayOutsideClick: [{\n                type: Output\n            }] } });\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n    provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\nclass OverlayModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayModule, imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin], exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayModule, providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER], imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n                    exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n                    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\nexport { BlockScrollStrategy as B, CdkOverlayOrigin as C, FlexibleConnectedPositionStrategy as F, GlobalPositionStrategy as G, NoopScrollStrategy as N, OverlayContainer as O, RepositionScrollStrategy as R, STANDARD_DROPDOWN_ADJACENT_POSITIONS as S, Overlay as a, CdkConnectedOverlay as b, OverlayRef as c, OverlayPositionBuilder as d, STANDARD_DROPDOWN_BELOW_POSITIONS as e, OverlayConfig as f, ConnectionPositionPair as g, ScrollingVisibility as h, ConnectedOverlayPositionChange as i, validateHorizontalPosition as j, ScrollStrategyOptions as k, CloseScrollStrategy as l, OverlayModule as m, OverlayOutsideClickDispatcher as n, OverlayKeyboardDispatcher as o, validateVerticalPosition as v };\n"], "mappings": ";;;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,SAAS,EAAEC,YAAY,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACjX,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,iBAAiB;AACpD,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,qBAAqB,QAAQ,wCAAwC;AACnF,SAASD,CAAC,IAAIE,eAAe,QAAQ,2BAA2B;AAChE,SAASF,CAAC,IAAIG,kBAAkB,QAAQ,iCAAiC;AACzE,SAASH,CAAC,IAAII,sBAAsB,QAAQ,6BAA6B;AACzE,SAASC,OAAO,EAAEC,YAAY,EAAEC,KAAK,QAAQ,MAAM;AACnD,SAASC,MAAM,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAC7D,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,gCAAgC;AACzE,SAASD,CAAC,IAAIE,WAAW,QAAQ,sBAAsB;AACvD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,eAAe,QAAQ,iBAAiB;AAClF,SAASC,CAAC,IAAIC,sBAAsB,QAAQ,0BAA0B;AACtE,SAASC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,YAAY,QAAQ,kCAAkC;AAC/G,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,SAAS1B,CAAC,IAAI2B,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,MAAM,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,UAAU,QAAQ,YAAY;AAEvC,MAAMC,uBAAuB,GAAGd,sBAAsB,CAAC,CAAC;AACxD;AACA;AACA;AACA,MAAMe,mBAAmB,CAAC;EAMtBC,WAAWA,CAACC,cAAc,EAAEC,QAAQ,EAAE;IAAAC,eAAA;IAAAA,eAAA,8BAJhB;MAAEC,GAAG,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC;IAAAF,eAAA;IAAAA,eAAA,qBAE9B,KAAK;IAAAA,eAAA;IAGd,IAAI,CAACF,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACK,SAAS,GAAGJ,QAAQ;EAC7B;EACA;EACAK,MAAMA,CAAA,EAAG,CAAE;EACX;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;MACtB,MAAMC,IAAI,GAAG,IAAI,CAACJ,SAAS,CAACK,eAAe;MAC3C,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACX,cAAc,CAACY,yBAAyB,CAAC,CAAC;MAC9E;MACA,IAAI,CAACC,mBAAmB,CAACT,IAAI,GAAGK,IAAI,CAACK,KAAK,CAACV,IAAI,IAAI,EAAE;MACrD,IAAI,CAACS,mBAAmB,CAACV,GAAG,GAAGM,IAAI,CAACK,KAAK,CAACX,GAAG,IAAI,EAAE;MACnD;MACA;MACAM,IAAI,CAACK,KAAK,CAACV,IAAI,GAAG3B,mBAAmB,CAAC,CAAC,IAAI,CAACkC,uBAAuB,CAACP,IAAI,CAAC;MACzEK,IAAI,CAACK,KAAK,CAACX,GAAG,GAAG1B,mBAAmB,CAAC,CAAC,IAAI,CAACkC,uBAAuB,CAACR,GAAG,CAAC;MACvEM,IAAI,CAACM,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MAC5C,IAAI,CAACC,UAAU,GAAG,IAAI;IAC1B;EACJ;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACD,UAAU,EAAE;MACjB,MAAME,IAAI,GAAG,IAAI,CAACd,SAAS,CAACK,eAAe;MAC3C,MAAMU,IAAI,GAAG,IAAI,CAACf,SAAS,CAACe,IAAI;MAChC,MAAMC,SAAS,GAAGF,IAAI,CAACL,KAAK;MAC5B,MAAMQ,SAAS,GAAGF,IAAI,CAACN,KAAK;MAC5B,MAAMS,0BAA0B,GAAGF,SAAS,CAACG,cAAc,IAAI,EAAE;MACjE,MAAMC,0BAA0B,GAAGH,SAAS,CAACE,cAAc,IAAI,EAAE;MACjE,IAAI,CAACP,UAAU,GAAG,KAAK;MACvBI,SAAS,CAACjB,IAAI,GAAG,IAAI,CAACS,mBAAmB,CAACT,IAAI;MAC9CiB,SAAS,CAAClB,GAAG,GAAG,IAAI,CAACU,mBAAmB,CAACV,GAAG;MAC5CgB,IAAI,CAACJ,SAAS,CAACW,MAAM,CAAC,wBAAwB,CAAC;MAC/C;MACA;MACA;MACA;MACA;MACA,IAAI7B,uBAAuB,EAAE;QACzBwB,SAAS,CAACG,cAAc,GAAGF,SAAS,CAACE,cAAc,GAAG,MAAM;MAChE;MACAG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACjB,uBAAuB,CAACP,IAAI,EAAE,IAAI,CAACO,uBAAuB,CAACR,GAAG,CAAC;MAClF,IAAIN,uBAAuB,EAAE;QACzBwB,SAAS,CAACG,cAAc,GAAGD,0BAA0B;QACrDD,SAAS,CAACE,cAAc,GAAGC,0BAA0B;MACzD;IACJ;EACJ;EACAjB,aAAaA,CAAA,EAAG;IACZ;IACA;IACA;IACA,MAAMW,IAAI,GAAG,IAAI,CAACd,SAAS,CAACK,eAAe;IAC3C,IAAIS,IAAI,CAACJ,SAAS,CAACc,QAAQ,CAAC,wBAAwB,CAAC,IAAI,IAAI,CAACZ,UAAU,EAAE;MACtE,OAAO,KAAK;IAChB;IACA,MAAMa,WAAW,GAAG,IAAI,CAACzB,SAAS,CAACK,eAAe;IAClD,MAAMqB,QAAQ,GAAG,IAAI,CAAC/B,cAAc,CAACgC,eAAe,CAAC,CAAC;IACtD,OAAOF,WAAW,CAACG,YAAY,GAAGF,QAAQ,CAACG,MAAM,IAAIJ,WAAW,CAACK,WAAW,GAAGJ,QAAQ,CAACK,KAAK;EACjG;AACJ;;AAEA;AACA;AACA;AACA,SAASC,wCAAwCA,CAAA,EAAG;EAChD,OAAOC,KAAK,CAAC,4CAA4C,CAAC;AAC9D;;AAEA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EAQtBxC,WAAWA,CAACyC,iBAAiB,EAAEC,OAAO,EAAEzC,cAAc,EAAE0C,OAAO,EAAE;IAAAxC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,8BAH3C,IAAI;IAAAA,eAAA;IAAAA,eAAA;IAoD1B;IAAAA,eAAA,kBACU,MAAM;MACZ,IAAI,CAACgB,OAAO,CAAC,CAAC;MACd,IAAI,IAAI,CAACyB,WAAW,CAACC,WAAW,CAAC,CAAC,EAAE;QAChC,IAAI,CAACH,OAAO,CAACI,GAAG,CAAC,MAAM,IAAI,CAACF,WAAW,CAACG,MAAM,CAAC,CAAC,CAAC;MACrD;IACJ,CAAC;IAtDG,IAAI,CAACN,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACzC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC0C,OAAO,GAAGA,OAAO;EAC1B;EACA;EACApC,MAAMA,CAACyC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,KAAK,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMX,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI,CAACM,WAAW,GAAGI,UAAU;EACjC;EACA;EACAxC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC0C,mBAAmB,EAAE;MAC1B;IACJ;IACA,MAAMC,MAAM,GAAG,IAAI,CAACV,iBAAiB,CAACW,QAAQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC/E,MAAM,CAACgF,UAAU,IAAI;MACxE,OAAQ,CAACA,UAAU,IACf,CAAC,IAAI,CAACV,WAAW,CAACW,cAAc,CAACzB,QAAQ,CAACwB,UAAU,CAACE,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC;IAC3F,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAACd,OAAO,IAAI,IAAI,CAACA,OAAO,CAACe,SAAS,IAAI,IAAI,CAACf,OAAO,CAACe,SAAS,GAAG,CAAC,EAAE;MACtE,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAAC1D,cAAc,CAACY,yBAAyB,CAAC,CAAC,CAACT,GAAG;MACjF,IAAI,CAAC8C,mBAAmB,GAAGC,MAAM,CAACS,SAAS,CAAC,MAAM;QAC9C,MAAMC,cAAc,GAAG,IAAI,CAAC5D,cAAc,CAACY,yBAAyB,CAAC,CAAC,CAACT,GAAG;QAC1E,IAAI0D,IAAI,CAACC,GAAG,CAACF,cAAc,GAAG,IAAI,CAACF,sBAAsB,CAAC,GAAG,IAAI,CAAChB,OAAO,CAACe,SAAS,EAAE;UACjF,IAAI,CAACM,OAAO,CAAC,CAAC;QAClB,CAAC,MACI;UACD,IAAI,CAACpB,WAAW,CAACqB,cAAc,CAAC,CAAC;QACrC;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACf,mBAAmB,GAAGC,MAAM,CAACS,SAAS,CAAC,IAAI,CAACI,OAAO,CAAC;IAC7D;EACJ;EACA;EACA7C,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC+B,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACgB,WAAW,CAAC,CAAC;MACtC,IAAI,CAAChB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAH,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC5B,OAAO,CAAC,CAAC;IACd,IAAI,CAACyB,WAAW,GAAG,IAAI;EAC3B;AAQJ;;AAEA;AACA,MAAMuB,kBAAkB,CAAC;EACrB;EACA3D,MAAMA,CAAA,EAAG,CAAE;EACX;EACAW,OAAOA,CAAA,EAAG,CAAE;EACZ;EACAZ,MAAMA,CAAA,EAAG,CAAE;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6D,4BAA4BA,CAACC,OAAO,EAAEC,gBAAgB,EAAE;EAC7D,OAAOA,gBAAgB,CAACC,IAAI,CAACC,eAAe,IAAI;IAC5C,MAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAM,GAAGF,eAAe,CAACpE,GAAG;IACzD,MAAMuE,YAAY,GAAGN,OAAO,CAACjE,GAAG,GAAGoE,eAAe,CAACE,MAAM;IACzD,MAAME,WAAW,GAAGP,OAAO,CAACQ,KAAK,GAAGL,eAAe,CAACnE,IAAI;IACxD,MAAMyE,YAAY,GAAGT,OAAO,CAAChE,IAAI,GAAGmE,eAAe,CAACK,KAAK;IACzD,OAAOJ,YAAY,IAAIE,YAAY,IAAIC,WAAW,IAAIE,YAAY;EACtE,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2BA,CAACV,OAAO,EAAEC,gBAAgB,EAAE;EAC5D,OAAOA,gBAAgB,CAACC,IAAI,CAACS,mBAAmB,IAAI;IAChD,MAAMC,YAAY,GAAGZ,OAAO,CAACjE,GAAG,GAAG4E,mBAAmB,CAAC5E,GAAG;IAC1D,MAAM8E,YAAY,GAAGb,OAAO,CAACK,MAAM,GAAGM,mBAAmB,CAACN,MAAM;IAChE,MAAMS,WAAW,GAAGd,OAAO,CAAChE,IAAI,GAAG2E,mBAAmB,CAAC3E,IAAI;IAC3D,MAAM+E,YAAY,GAAGf,OAAO,CAACQ,KAAK,GAAGG,mBAAmB,CAACH,KAAK;IAC9D,OAAOI,YAAY,IAAIC,YAAY,IAAIC,WAAW,IAAIC,YAAY;EACtE,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAO3BrF,WAAWA,CAACyC,iBAAiB,EAAExC,cAAc,EAAEyC,OAAO,EAAEC,OAAO,EAAE;IAAAxC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,8BAF3C,IAAI;IAAAA,eAAA;IAGtB,IAAI,CAACsC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACxC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACyC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACA;EACApC,MAAMA,CAACyC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,KAAK,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMX,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI,CAACM,WAAW,GAAGI,UAAU;EACjC;EACA;EACAxC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAAC0C,mBAAmB,EAAE;MAC3B,MAAMoC,QAAQ,GAAG,IAAI,CAAC3C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC4C,cAAc,GAAG,CAAC;MAC/D,IAAI,CAACrC,mBAAmB,GAAG,IAAI,CAACT,iBAAiB,CAACW,QAAQ,CAACkC,QAAQ,CAAC,CAAC1B,SAAS,CAAC,MAAM;QACjF,IAAI,CAAChB,WAAW,CAACqB,cAAc,CAAC,CAAC;QACjC;QACA,IAAI,IAAI,CAACtB,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC6C,SAAS,EAAE;UACxC,MAAMC,WAAW,GAAG,IAAI,CAAC7C,WAAW,CAACW,cAAc,CAACmC,qBAAqB,CAAC,CAAC;UAC3E,MAAM;YAAErD,KAAK;YAAEF;UAAO,CAAC,GAAG,IAAI,CAAClC,cAAc,CAACgC,eAAe,CAAC,CAAC;UAC/D;UACA;UACA,MAAM0D,WAAW,GAAG,CAAC;YAAEtD,KAAK;YAAEF,MAAM;YAAEuC,MAAM,EAAEvC,MAAM;YAAE0C,KAAK,EAAExC,KAAK;YAAEjC,GAAG,EAAE,CAAC;YAAEC,IAAI,EAAE;UAAE,CAAC,CAAC;UACtF,IAAI+D,4BAA4B,CAACqB,WAAW,EAAEE,WAAW,CAAC,EAAE;YACxD,IAAI,CAACxE,OAAO,CAAC,CAAC;YACd,IAAI,CAACuB,OAAO,CAACI,GAAG,CAAC,MAAM,IAAI,CAACF,WAAW,CAACG,MAAM,CAAC,CAAC,CAAC;UACrD;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA5B,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC+B,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACgB,WAAW,CAAC,CAAC;MACtC,IAAI,CAAChB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAH,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC5B,OAAO,CAAC,CAAC;IACd,IAAI,CAACyB,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgD,qBAAqB,CAAC;EAKxB5F,WAAWA,CAAA,EAAG;IAAAG,eAAA,4BAJMjE,MAAM,CAAC0C,gBAAgB,CAAC;IAAAuB,eAAA,yBAC3BjE,MAAM,CAAC2C,aAAa,CAAC;IAAAsB,eAAA,kBAC5BjE,MAAM,CAACC,MAAM,CAAC;IAAAgE,eAAA,oBACZjE,MAAM,CAACwB,QAAQ,CAAC;IAE5B;IAAAyC,eAAA,eACO,MAAM,IAAIgE,kBAAkB,CAAC,CAAC;IACrC;AACJ;AACA;AACA;IAHIhE,eAAA,gBAIS0F,MAAM,IAAK,IAAIrD,mBAAmB,CAAC,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACzC,cAAc,EAAE4F,MAAM,CAAC;IAC9G;IAAA1F,eAAA,gBACQ,MAAM,IAAIJ,mBAAmB,CAAC,IAAI,CAACE,cAAc,EAAE,IAAI,CAACK,SAAS,CAAC;IAC1E;AACJ;AACA;AACA;AACA;IAJIH,eAAA,qBAKc0F,MAAM,IAAK,IAAIR,wBAAwB,CAAC,IAAI,CAAC5C,iBAAiB,EAAE,IAAI,CAACxC,cAAc,EAAE,IAAI,CAACyC,OAAO,EAAEmD,MAAM,CAAC;EAfxG;AAkBpB;AAACC,sBAAA,GAvBKF,qBAAqB;AAAAzF,eAAA,CAArByF,qBAAqB,wBAAAG,+BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAqB4EJ,sBAAqB;AAAA;AAAAzF,eAAA,CArBtHyF,qBAAqB,+BAwBsD3J,EAAE,CAAAgK,kBAAA;EAAAC,KAAA,EAFwBN,sBAAqB;EAAAO,OAAA,EAArBP,sBAAqB,CAAAQ,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEpJ;EAAA,QAAApD,SAAA,oBAAAA,SAAA,KAAiFhH,EAAE,CAAAqK,iBAAA,CAAQV,qBAAqB,EAAc,CAAC;IACnHW,IAAI,EAAEnK,UAAU;IAChBoK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAMI,aAAa,CAAC;EAkChBzG,WAAWA,CAAC6F,MAAM,EAAE;IAjCpB;IAAA1F,eAAA;IAEA;IAAAA,eAAA,yBACiB,IAAIgE,kBAAkB,CAAC,CAAC;IACzC;IAAAhE,eAAA,qBACa,EAAE;IACf;IAAAA,eAAA,sBACc,KAAK;IACnB;IAAAA,eAAA,wBACgB,2BAA2B;IAC3C;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;AACA;IAJIA,eAAA,8BAKsB,KAAK;IAEvB,IAAI0F,MAAM,EAAE;MACR;MACA;MACA;MACA,MAAMa,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACf,MAAM,CAAC;MACtC,KAAK,MAAMgB,GAAG,IAAIH,UAAU,EAAE;QAC1B,IAAIb,MAAM,CAACgB,GAAG,CAAC,KAAKC,SAAS,EAAE;UAC3B;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAACD,GAAG,CAAC,GAAGhB,MAAM,CAACgB,GAAG,CAAC;QAC3B;MACJ;IACJ;EACJ;AACJ;;AAEA;AACA,MAAME,sBAAsB,CAAC;EAYzB/G,WAAWA,CAACgH,MAAM,EAAEC,OAAO,EAC3B;EACAC,OAAO,EACP;EACAC,OAAO,EACP;EACAC,UAAU,EAAE;IAAAjH,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAdZ;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IASI,IAAI,CAAC+G,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGL,MAAM,CAACK,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAGN,MAAM,CAACM,OAAO;IAC7B,IAAI,CAACC,QAAQ,GAAGN,OAAO,CAACM,QAAQ;IAChC,IAAI,CAACC,QAAQ,GAAGP,OAAO,CAACO,QAAQ;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EAAAzH,YAAA;IAAAG,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;EAAA;AAK1B;AACA;AACA,MAAMuH,8BAA8B,CAAC;EAGjC1H,WAAWA,CACX;EACA2H,cAAc,EACd;EACAC,wBAAwB,EAAE;IAAAzH,eAAA;IAAAA,eAAA;IACtB,IAAI,CAACwH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EAC/C,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC7D,MAAMxF,KAAK,CAAC,8BAA8BuF,QAAQ,KAAKC,KAAK,KAAK,GAC7D,uCAAuC,CAAC;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACF,QAAQ,EAAEC,KAAK,EAAE;EACjD,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC5D,MAAMxF,KAAK,CAAC,8BAA8BuF,QAAQ,KAAKC,KAAK,KAAK,GAC7D,sCAAsC,CAAC;EAC/C;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAME,qBAAqB,CAAC;EAKxBjI,WAAWA,CAAA,EAAG;IAJd;IAAAG,eAAA,4BACoB,EAAE;IAAAA,eAAA,oBACVjE,MAAM,CAACwB,QAAQ,CAAC;IAAAyC,eAAA;EAEZ;EAChB+H,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnF,MAAM,CAAC,CAAC;EACjB;EACA;EACA9B,GAAGA,CAAC+B,UAAU,EAAE;IACZ;IACA,IAAI,CAACrB,MAAM,CAACqB,UAAU,CAAC;IACvB,IAAI,CAACmF,iBAAiB,CAACC,IAAI,CAACpF,UAAU,CAAC;EAC3C;EACA;EACArB,MAAMA,CAACqB,UAAU,EAAE;IACf,MAAMqF,KAAK,GAAG,IAAI,CAACF,iBAAiB,CAACG,OAAO,CAACtF,UAAU,CAAC;IACxD,IAAIqF,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACF,iBAAiB,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,IAAI,CAACF,iBAAiB,CAACK,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACzF,MAAM,CAAC,CAAC;IACjB;EACJ;AAGJ;AAAC0F,sBAAA,GA5BKR,qBAAqB;AAAA9H,eAAA,CAArB8H,qBAAqB,wBAAAS,+BAAA1C,iBAAA;EAAA,YAAAA,iBAAA,IA0B4EiC,sBAAqB;AAAA;AAAA9H,eAAA,CA1BtH8H,qBAAqB,+BApKsDhM,EAAE,CAAAgK,kBAAA;EAAAC,KAAA,EA+LwB+B,sBAAqB;EAAA9B,OAAA,EAArB8B,sBAAqB,CAAA7B,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEpJ;EAAA,QAAApD,SAAA,oBAAAA,SAAA,KAjMiFhH,EAAE,CAAAqK,iBAAA,CAiMQ2B,qBAAqB,EAAc,CAAC;IACnH1B,IAAI,EAAEnK,UAAU;IAChBoK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA,MAAMsC,yBAAyB,SAASV,qBAAqB,CAAC;EAAAjI,YAAA,GAAAwG,IAAA;IAAA,SAAAA,IAAA;IAAArG,eAAA,kBAChDjE,MAAM,CAACC,MAAM,CAAC;IAAAgE,eAAA,oBACZjE,MAAM,CAACG,gBAAgB,CAAC,CAACuM,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IAAAzI,eAAA;IAoB/D;IAAAA,eAAA,2BACoB0I,KAAK,IAAK;MAC1B,MAAMC,QAAQ,GAAG,IAAI,CAACX,iBAAiB;MACvC,KAAK,IAAIY,CAAC,GAAGD,QAAQ,CAACN,MAAM,GAAG,CAAC,EAAEO,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA,IAAID,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc,CAACC,SAAS,CAACT,MAAM,GAAG,CAAC,EAAE;UACjD,IAAI,CAAC9F,OAAO,CAACI,GAAG,CAAC,MAAMgG,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc,CAACE,IAAI,CAACL,KAAK,CAAC,CAAC;UAC9D;QACJ;MACJ;IACJ,CAAC;EAAA;EAjCD;EACA5H,GAAGA,CAAC+B,UAAU,EAAE;IACZ,KAAK,CAAC/B,GAAG,CAAC+B,UAAU,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAACmG,WAAW,EAAE;MACnB,IAAI,CAACzG,OAAO,CAAC0G,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACC,eAAe,GAAG,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAACC,gBAAgB,CAAC;MAC1F,CAAC,CAAC;MACF,IAAI,CAACL,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACApG,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACoG,WAAW,EAAE;MAAA,IAAAM,qBAAA;MAClB,CAAAA,qBAAA,OAAI,CAACJ,eAAe,cAAAI,qBAAA,eAApBA,qBAAA,CAAAC,IAAA,KAAuB,CAAC;MACxB,IAAI,CAACP,WAAW,GAAG,KAAK;IAC5B;EACJ;AAmBJ;AAACQ,0BAAA,GAxCKhB,yBAAyB;AAAAxI,eAAA,CAAzBwI,yBAAyB;EAAA,IAAAiB,uCAAA;EAAA,gBAAAC,mCAAA7D,iBAAA;IAAA,QAAA4D,uCAAA,KAAAA,uCAAA,GA3MkD3N,EAAE,CAAA6N,qBAAA,CAiPoBnB,0BAAyB,IAAA3C,iBAAA,IAAzB2C,0BAAyB;EAAA;AAAA;AAAAxI,eAAA,CAtC1HwI,yBAAyB,+BA3MkD1M,EAAE,CAAAgK,kBAAA;EAAAC,KAAA,EAkPwByC,0BAAyB;EAAAxC,OAAA,EAAzBwC,0BAAyB,CAAAvC,IAAA;EAAAC,UAAA,EAAc;AAAM;AAExJ;EAAA,QAAApD,SAAA,oBAAAA,SAAA,KApPiFhH,EAAE,CAAAqK,iBAAA,CAoPQqC,yBAAyB,EAAc,CAAC;IACvHpC,IAAI,EAAEnK,UAAU;IAChBoK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAM0D,6BAA6B,SAAS9B,qBAAqB,CAAC;EAAAjI,YAAA,GAAAwG,IAAA;IAAA,SAAAA,IAAA;IAAArG,eAAA,oBAClDjE,MAAM,CAAC2B,QAAQ,CAAC;IAAAsC,eAAA,kBAClBjE,MAAM,CAACC,MAAM,CAAC;IAAAgE,eAAA,oBACZjE,MAAM,CAACG,gBAAgB,CAAC,CAACuM,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IAAAzI,eAAA;IAAAA,eAAA,4BAE3C,KAAK;IAAAA,eAAA;IAAAA,eAAA;IA2CzB;IAAAA,eAAA,+BACwB0I,KAAK,IAAK;MAC9B,IAAI,CAACmB,uBAAuB,GAAGhM,eAAe,CAAC6K,KAAK,CAAC;IACzD,CAAC;IACD;IAAA1I,eAAA,yBACkB0I,KAAK,IAAK;MACxB,MAAMoB,MAAM,GAAGjM,eAAe,CAAC6K,KAAK,CAAC;MACrC;MACA;MACA;MACA;MACA;MACA;MACA,MAAM7B,MAAM,GAAG6B,KAAK,CAACtC,IAAI,KAAK,OAAO,IAAI,IAAI,CAACyD,uBAAuB,GAC/D,IAAI,CAACA,uBAAuB,GAC5BC,MAAM;MACZ;MACA;MACA,IAAI,CAACD,uBAAuB,GAAG,IAAI;MACnC;MACA;MACA;MACA,MAAMlB,QAAQ,GAAG,IAAI,CAACX,iBAAiB,CAAC+B,KAAK,CAAC,CAAC;MAC/C;MACA;MACA;MACA;MACA,KAAK,IAAInB,CAAC,GAAGD,QAAQ,CAACN,MAAM,GAAG,CAAC,EAAEO,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,MAAM/F,UAAU,GAAG8F,QAAQ,CAACC,CAAC,CAAC;QAC9B,IAAI/F,UAAU,CAACmH,qBAAqB,CAAClB,SAAS,CAACT,MAAM,GAAG,CAAC,IAAI,CAACxF,UAAU,CAACH,WAAW,CAAC,CAAC,EAAE;UACpF;QACJ;QACA;QACA;QACA;QACA,IAAIuH,uBAAuB,CAACpH,UAAU,CAACO,cAAc,EAAE0G,MAAM,CAAC,IAC1DG,uBAAuB,CAACpH,UAAU,CAACO,cAAc,EAAEyD,MAAM,CAAC,EAAE;UAC5D;QACJ;QACA,MAAMqD,oBAAoB,GAAGrH,UAAU,CAACmH,qBAAqB;QAC7D;QACA,IAAI,IAAI,CAACzH,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAACI,GAAG,CAAC,MAAMuH,oBAAoB,CAACnB,IAAI,CAACL,KAAK,CAAC,CAAC;QAC5D,CAAC,MACI;UACDwB,oBAAoB,CAACnB,IAAI,CAACL,KAAK,CAAC;QACpC;MACJ;IACJ,CAAC;EAAA;EAxFD;EACA5H,GAAGA,CAAC+B,UAAU,EAAE;IACZ,KAAK,CAAC/B,GAAG,CAAC+B,UAAU,CAAC;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACmG,WAAW,EAAE;MACnB,MAAM9H,IAAI,GAAG,IAAI,CAACf,SAAS,CAACe,IAAI;MAChC,MAAMiJ,YAAY,GAAG;QAAEC,OAAO,EAAE;MAAK,CAAC;MACtC,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC9H,OAAO,CAAC0G,iBAAiB,CAAC,MAAM,CAClDrL,qBAAqB,CAAC,IAAI,CAACuL,SAAS,EAAEjI,IAAI,EAAE,aAAa,EAAE,IAAI,CAACoJ,oBAAoB,EAAEH,YAAY,CAAC,EACnGvM,qBAAqB,CAAC,IAAI,CAACuL,SAAS,EAAEjI,IAAI,EAAE,OAAO,EAAE,IAAI,CAACqJ,cAAc,EAAEJ,YAAY,CAAC,EACvFvM,qBAAqB,CAAC,IAAI,CAACuL,SAAS,EAAEjI,IAAI,EAAE,UAAU,EAAE,IAAI,CAACqJ,cAAc,EAAEJ,YAAY,CAAC,EAC1FvM,qBAAqB,CAAC,IAAI,CAACuL,SAAS,EAAEjI,IAAI,EAAE,aAAa,EAAE,IAAI,CAACqJ,cAAc,EAAEJ,YAAY,CAAC,CAChG,CAAC;MACF;MACA;MACA,IAAI,IAAI,CAACK,SAAS,CAACC,GAAG,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;QAC/C,IAAI,CAACC,oBAAoB,GAAGzJ,IAAI,CAACN,KAAK,CAACgK,MAAM;QAC7C1J,IAAI,CAACN,KAAK,CAACgK,MAAM,GAAG,SAAS;QAC7B,IAAI,CAACF,iBAAiB,GAAG,IAAI;MACjC;MACA,IAAI,CAAC1B,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACApG,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACoG,WAAW,EAAE;MAAA,IAAA6B,eAAA;MAClB,CAAAA,eAAA,OAAI,CAACR,SAAS,cAAAQ,eAAA,eAAdA,eAAA,CAAgBC,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACV,SAAS,GAAG1D,SAAS;MAC1B,IAAI,IAAI,CAAC6D,SAAS,CAACC,GAAG,IAAI,IAAI,CAACC,iBAAiB,EAAE;QAC9C,IAAI,CAACvK,SAAS,CAACe,IAAI,CAACN,KAAK,CAACgK,MAAM,GAAG,IAAI,CAACD,oBAAoB;QAC5D,IAAI,CAACD,iBAAiB,GAAG,KAAK;MAClC;MACA,IAAI,CAAC1B,WAAW,GAAG,KAAK;IAC5B;EACJ;AAoDJ;AAACgC,8BAAA,GAnGKpB,6BAA6B;AAAA5J,eAAA,CAA7B4J,6BAA6B;EAAA,IAAAqB,2CAAA;EAAA,gBAAAC,uCAAArF,iBAAA;IAAA,QAAAoF,2CAAA,KAAAA,2CAAA,GA9P8CnP,EAAE,CAAA6N,qBAAA,CA+VoBC,8BAA6B,IAAA/D,iBAAA,IAA7B+D,8BAA6B;EAAA;AAAA;AAAA5J,eAAA,CAjG9H4J,6BAA6B,+BA9P8C9N,EAAE,CAAAgK,kBAAA;EAAAC,KAAA,EAgWwB6D,8BAA6B;EAAA5D,OAAA,EAA7B4D,8BAA6B,CAAA3D,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE5J;EAAA,QAAApD,SAAA,oBAAAA,SAAA,KAlWiFhH,EAAE,CAAAqK,iBAAA,CAkWQyD,6BAA6B,EAAc,CAAC;IAC3HxD,IAAI,EAAEnK,UAAU;IAChBoK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,SAAS+D,uBAAuBA,CAACkB,MAAM,EAAEC,KAAK,EAAE;EAC5C,MAAMC,kBAAkB,GAAG,OAAOC,UAAU,KAAK,WAAW,IAAIA,UAAU;EAC1E,IAAIC,OAAO,GAAGH,KAAK;EACnB,OAAOG,OAAO,EAAE;IACZ,IAAIA,OAAO,KAAKJ,MAAM,EAAE;MACpB,OAAO,IAAI;IACf;IACAI,OAAO,GACHF,kBAAkB,IAAIE,OAAO,YAAYD,UAAU,GAAGC,OAAO,CAACC,IAAI,GAAGD,OAAO,CAACE,UAAU;EAC/F;EACA,OAAO,KAAK;AAChB;AAEA,MAAMC,sBAAsB,CAAC;AAG5BC,uBAAA,GAHKD,sBAAsB;AAAA1L,eAAA,CAAtB0L,sBAAsB,wBAAAE,gCAAA/F,iBAAA;EAAA,YAAAA,iBAAA,IAC2E6F,uBAAsB;AAAA;AAAA1L,eAAA,CADvH0L,sBAAsB,8BApXqD5P,EAAE,CAAA+P,iBAAA;EAAAzF,IAAA,EAsXQsF,uBAAsB;EAAAI,SAAA;EAAAC,SAAA,+BAAkG,EAAE;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;EAAAC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAErN;EAAA,QAAA1J,SAAA,oBAAAA,SAAA,KAxXiFhH,EAAE,CAAAqK,iBAAA,CAwXQuF,sBAAsB,EAAc,CAAC;IACpHtF,IAAI,EAAEjK,SAAS;IACfkK,IAAI,EAAE,CAAC;MAAE6F,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAEpQ,uBAAuB,CAACqQ,MAAM;MAAEF,aAAa,EAAElQ,iBAAiB,CAACqQ,IAAI;MAAElB,IAAI,EAAE;QAAE,0BAA0B,EAAE;MAAG,CAAC;MAAEc,MAAM,EAAE,CAAC,6oDAA6oD;IAAE,CAAC;EACtzD,CAAC,CAAC;AAAA;AACV;AACA,MAAMK,gBAAgB,CAAC;EAKnB9M,WAAWA,CAAA,EAAG;IAAAG,eAAA,oBAJFjE,MAAM,CAAC2B,QAAQ,CAAC;IAAAsC,eAAA;IAAAA,eAAA,oBAEhBjE,MAAM,CAACwB,QAAQ,CAAC;IAAAyC,eAAA,uBACbjE,MAAM,CAACgC,sBAAsB,CAAC;EAC7B;EAChBgK,WAAWA,CAAA,EAAG;IAAA,IAAA6E,qBAAA;IACV,CAAAA,qBAAA,OAAI,CAACC,iBAAiB,cAAAD,qBAAA,eAAtBA,qBAAA,CAAwBpL,MAAM,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsL,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC,IAAI,CAACF,iBAAiB,EAAE;MACzB,IAAI,CAACG,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAO,IAAI,CAACH,iBAAiB;EACjC;EACA;AACJ;AACA;AACA;EACIG,gBAAgBA,CAAA,EAAG;IACf,MAAMC,cAAc,GAAG,uBAAuB;IAC9C;IACA;IACA;IACA,IAAI,IAAI,CAACzC,SAAS,CAAC0C,SAAS,IAAIpP,kBAAkB,CAAC,CAAC,EAAE;MAClD,MAAMqP,0BAA0B,GAAG,IAAI,CAAChN,SAAS,CAACiN,gBAAgB,CAAC,IAAIH,cAAc,uBAAuB,GAAG,IAAIA,cAAc,mBAAmB,CAAC;MACrJ;MACA;MACA,KAAK,IAAIrE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuE,0BAA0B,CAAC9E,MAAM,EAAEO,CAAC,EAAE,EAAE;QACxDuE,0BAA0B,CAACvE,CAAC,CAAC,CAACpH,MAAM,CAAC,CAAC;MAC1C;IACJ;IACA,MAAM6L,SAAS,GAAG,IAAI,CAAClN,SAAS,CAACmN,aAAa,CAAC,KAAK,CAAC;IACrDD,SAAS,CAACxM,SAAS,CAACC,GAAG,CAACmM,cAAc,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAInP,kBAAkB,CAAC,CAAC,EAAE;MACtBuP,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;IAC9C,CAAC,MACI,IAAI,CAAC,IAAI,CAAC/C,SAAS,CAAC0C,SAAS,EAAE;MAChCG,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IAChD;IACA,IAAI,CAACpN,SAAS,CAACe,IAAI,CAACsM,WAAW,CAACH,SAAS,CAAC;IAC1C,IAAI,CAACR,iBAAiB,GAAGQ,SAAS;EACtC;EACA;EACAN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACU,YAAY,CAACC,IAAI,CAAChC,sBAAsB,CAAC;EAClD;AAGJ;AAACiC,iBAAA,GAjEKhB,gBAAgB;AAAA3M,eAAA,CAAhB2M,gBAAgB,wBAAAiB,0BAAA/H,iBAAA;EAAA,YAAAA,iBAAA,IA+DiF8G,iBAAgB;AAAA;AAAA3M,eAAA,CA/DjH2M,gBAAgB,+BA7X2D7Q,EAAE,CAAAgK,kBAAA;EAAAC,KAAA,EA6bwB4G,iBAAgB;EAAA3G,OAAA,EAAhB2G,iBAAgB,CAAA1G,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE/I;EAAA,QAAApD,SAAA,oBAAAA,SAAA,KA/biFhH,EAAE,CAAAqK,iBAAA,CA+bQwG,gBAAgB,EAAc,CAAC;IAC9GvG,IAAI,EAAEnK,UAAU;IAChBoK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAM2H,WAAW,CAAC;EAOdhO,WAAWA,CAACE,QAAQ,EAAEoJ,SAAS,EAAE5G,OAAO,EAAEuL,OAAO,EAAE;IAAA9N,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,kBAoBzC,MAAM;MAAA,IAAA+N,mBAAA,EAAAC,qBAAA;MACZC,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;MACnC,CAAAH,mBAAA,OAAI,CAACI,aAAa,cAAAJ,mBAAA,eAAlBA,mBAAA,CAAAxE,IAAA,KAAqB,CAAC;MACtB,CAAAyE,qBAAA,OAAI,CAACI,qBAAqB,cAAAJ,qBAAA,eAA1BA,qBAAA,CAAAzE,IAAA,KAA6B,CAAC;MAC9B,IAAI,CAAC4E,aAAa,GAAG,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACF,gBAAgB,GAAGvH,SAAS;MACnF,IAAI,CAACzC,OAAO,CAAC1C,MAAM,CAAC,CAAC;IACzB,CAAC;IAzBG,IAAI,CAAC2H,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC5G,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC2B,OAAO,GAAGnE,QAAQ,CAACuN,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACpJ,OAAO,CAACrD,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;IAClD,IAAI,CAACqN,aAAa,GAAGhF,SAAS,CAACC,MAAM,CAAC,IAAI,CAAClF,OAAO,EAAE,OAAO,EAAE4J,OAAO,CAAC;EACzE;EACAlL,MAAMA,CAAA,EAAG;IACL,IAAI,CAACL,OAAO,CAAC0G,iBAAiB,CAAC,MAAM;MAAA,IAAAoF,sBAAA;MACjC,MAAMnK,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B+J,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;MACnC,CAAAG,sBAAA,OAAI,CAACD,qBAAqB,cAAAC,sBAAA,eAA1BA,sBAAA,CAAA9E,IAAA,KAA6B,CAAC;MAC9B,IAAI,CAAC6E,qBAAqB,GAAG,IAAI,CAACjF,SAAS,CAACC,MAAM,CAAClF,OAAO,EAAE,eAAe,EAAE,IAAI,CAACoK,OAAO,CAAC;MAC1F,IAAI,CAACJ,gBAAgB,GAAGK,UAAU,CAAC,IAAI,CAACD,OAAO,EAAE,GAAG,CAAC;MACrD;MACA;MACApK,OAAO,CAACtD,KAAK,CAAC4N,aAAa,GAAG,MAAM;MACpCtK,OAAO,CAACrD,SAAS,CAACW,MAAM,CAAC,8BAA8B,CAAC;IAC5D,CAAC,CAAC;EACN;AAQJ;;AAEA;AACA;AACA;AACA;AACA,MAAMiN,UAAU,CAAC;EAiCb5O,WAAWA,CAAC6O,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAEpM,OAAO,EAAED,OAAO,EAAEsM,mBAAmB,EAAE1O,SAAS,EAAE2O,SAAS,EAAEC,uBAAuB,EAAEC,mBAAmB,GAAG,KAAK,EAAEC,SAAS,EAAE9F,SAAS,EAAE;IAAAnJ,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,yBApBjK,IAAIhC,OAAO,CAAC,CAAC;IAAAgC,eAAA,uBACf,IAAIhC,OAAO,CAAC,CAAC;IAAAgC,eAAA,uBACb,IAAIhC,OAAO,CAAC,CAAC;IAAAgC,eAAA;IAAAA,eAAA;IAAAA,eAAA,2BAGT/B,YAAY,CAACiR,KAAK;IAAAlP,eAAA,uBACtB,IAAI;IACnB;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA,yBACiB,IAAIhC,OAAO,CAAC,CAAC;IAC9B;IAAAgC,eAAA,gCACwB,IAAIhC,OAAO,CAAC,CAAC;IAAAgC,eAAA,mBAC1B,IAAIhC,OAAO,CAAC,CAAC;IAAAgC,eAAA;IAExB;IAAAA,eAAA;IAGI,IAAI,CAAC0O,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACpM,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACsM,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAC1O,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC2O,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC9F,SAAS,GAAGA,SAAS;IAC1B,IAAI3G,OAAO,CAAC2M,cAAc,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG5M,OAAO,CAAC2M,cAAc;MAC7C,IAAI,CAACC,eAAe,CAAChP,MAAM,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACiP,iBAAiB,GAAG7M,OAAO,CAAC8M,gBAAgB;IACjD;IACA;IACA;IACA,IAAI,CAACC,eAAe,GAAGjT,SAAS,CAAC,MAAMC,WAAW,CAAC,MAAM;MACrD,IAAI,CAACiT,QAAQ,CAACzG,IAAI,CAAC,CAAC;IACxB,CAAC,EAAE;MAAE0G,QAAQ,EAAE,IAAI,CAACR;IAAU,CAAC,CAAC,CAAC;EACrC;EACA;EACA,IAAI7L,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACwL,KAAK;EACrB;EACA;EACA,IAAIc,eAAeA,CAAA,EAAG;IAAA,IAAAC,kBAAA;IAClB,OAAO,EAAAA,kBAAA,OAAI,CAACC,YAAY,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBzL,OAAO,KAAI,IAAI;EAC7C;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI2L,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAClB,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIvO,MAAMA,CAAC0P,MAAM,EAAE;IAAA,IAAAC,qBAAA;IACX;IACA;IACA,IAAI,CAAC,IAAI,CAACpB,KAAK,CAACqB,aAAa,IAAI,IAAI,CAACC,mBAAmB,EAAE;MACvD,IAAI,CAACA,mBAAmB,CAACzC,WAAW,CAAC,IAAI,CAACmB,KAAK,CAAC;IACpD;IACA,MAAMuB,YAAY,GAAG,IAAI,CAACxB,aAAa,CAACtO,MAAM,CAAC0P,MAAM,CAAC;IACtD,IAAI,IAAI,CAACT,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACjP,MAAM,CAAC,IAAI,CAAC;IACvC;IACA,IAAI,CAAC+P,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACjB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC/O,MAAM,CAAC,CAAC;IACjC;IACA;IACA;IACA;IACA,CAAA0P,qBAAA,OAAI,CAACO,mBAAmB,cAAAP,qBAAA,eAAxBA,qBAAA,CAA0BQ,OAAO,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAACD,mBAAmB,GAAG9T,eAAe,CAAC,MAAM;MAC7C;MACA,IAAI,IAAI,CAACkG,WAAW,CAAC,CAAC,EAAE;QACpB,IAAI,CAACoB,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,EAAE;MAAE2L,QAAQ,EAAE,IAAI,CAACR;IAAU,CAAC,CAAC;IAChC;IACA,IAAI,CAACuB,oBAAoB,CAAC,IAAI,CAAC;IAC/B,IAAI,IAAI,CAAChO,OAAO,CAACiO,WAAW,EAAE;MAC1B,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;IACA,IAAI,IAAI,CAAClO,OAAO,CAACyE,UAAU,EAAE;MACzB,IAAI,CAAC0J,cAAc,CAAC,IAAI,CAAC/B,KAAK,EAAE,IAAI,CAACpM,OAAO,CAACyE,UAAU,EAAE,IAAI,CAAC;IAClE;IACA;IACA,IAAI,CAAC2J,YAAY,CAAC7H,IAAI,CAAC,CAAC;IACxB;IACA,IAAI,CAAC8F,mBAAmB,CAAC/N,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,IAAI,CAAC0B,OAAO,CAACqO,mBAAmB,EAAE;MAClC,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAAChC,SAAS,CAACrL,SAAS,CAAC,MAAM,IAAI,CAAC6K,OAAO,CAAC,CAAC,CAAC;IAC1E;IACA,IAAI,CAACS,uBAAuB,CAACjO,GAAG,CAAC,IAAI,CAAC;IACtC;IACA;IACA;IACA,IAAI,QAAOoP,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa,SAAS,MAAK,UAAU,EAAE;MAC/C;MACA;MACA;MACA;MACA;MACAb,YAAY,CAACa,SAAS,CAAC,MAAM;QACzB,IAAI,IAAI,CAACrO,WAAW,CAAC,CAAC,EAAE;UACpB;UACA;UACA;UACA,IAAI,CAACH,OAAO,CAAC0G,iBAAiB,CAAC,MAAM+H,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACtO,MAAM,CAAC,CAAC,CAAC,CAAC;QACrF;MACJ,CAAC,CAAC;IACN;IACA,OAAOsN,YAAY;EACvB;EACA;AACJ;AACA;AACA;EACItN,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACF,WAAW,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,IAAI,CAACyO,cAAc,CAAC,CAAC;IACrB;IACA;IACA;IACA,IAAI,CAACX,oBAAoB,CAAC,KAAK,CAAC;IAChC,IAAI,IAAI,CAACnB,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACzM,MAAM,EAAE;MACzD,IAAI,CAACyM,iBAAiB,CAACzM,MAAM,CAAC,CAAC;IACnC;IACA,IAAI,IAAI,CAACwM,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACpO,OAAO,CAAC,CAAC;IAClC;IACA,MAAMoQ,gBAAgB,GAAG,IAAI,CAAC1C,aAAa,CAAC9L,MAAM,CAAC,CAAC;IACpD;IACA,IAAI,CAACyO,YAAY,CAACtI,IAAI,CAAC,CAAC;IACxB;IACA,IAAI,CAAC8F,mBAAmB,CAACrN,MAAM,CAAC,IAAI,CAAC;IACrC;IACA;IACA,IAAI,CAAC8P,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACR,gBAAgB,CAAC/M,WAAW,CAAC,CAAC;IACnC,IAAI,CAACgL,uBAAuB,CAACvN,MAAM,CAAC,IAAI,CAAC;IACzC,OAAO4P,gBAAgB;EAC3B;EACA;EACA9C,OAAOA,CAAA,EAAG;IAAA,IAAAiD,mBAAA,EAAAC,WAAA,EAAAC,sBAAA;IACN,MAAMC,UAAU,GAAG,IAAI,CAAChP,WAAW,CAAC,CAAC;IACrC,IAAI,IAAI,CAAC2M,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACf,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAACqD,sBAAsB,CAAC,CAAC;IAC7B,CAAAJ,mBAAA,OAAI,CAAC3B,YAAY,cAAA2B,mBAAA,eAAjBA,mBAAA,CAAmBjD,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACwC,gBAAgB,CAAC/M,WAAW,CAAC,CAAC;IACnC,IAAI,CAAC8K,mBAAmB,CAACrN,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAACkN,aAAa,CAACJ,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACsC,YAAY,CAACgB,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACC,cAAc,CAACD,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAAC/I,cAAc,CAAC+I,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAAC5H,qBAAqB,CAAC4H,QAAQ,CAAC,CAAC;IACrC,IAAI,CAAC7C,uBAAuB,CAACvN,MAAM,CAAC,IAAI,CAAC;IACzC,CAAAgQ,WAAA,OAAI,CAAC7C,KAAK,cAAA6C,WAAA,eAAVA,WAAA,CAAYhQ,MAAM,CAAC,CAAC;IACpB,CAAAiQ,sBAAA,OAAI,CAACnB,mBAAmB,cAAAmB,sBAAA,eAAxBA,sBAAA,CAA0BlB,OAAO,CAAC,CAAC;IACnC,IAAI,CAACN,mBAAmB,GAAG,IAAI,CAACrB,KAAK,GAAG,IAAI,CAACD,KAAK,GAAG,IAAI,CAACiB,YAAY,GAAG,IAAI;IAC7E,IAAI8B,UAAU,EAAE;MACZ,IAAI,CAACL,YAAY,CAACtI,IAAI,CAAC,CAAC;IAC5B;IACA,IAAI,CAACsI,YAAY,CAACO,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACrC,eAAe,CAACgB,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACf,QAAQ,CAACoC,QAAQ,CAAC,CAAC;EAC5B;EACA;EACAlP,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACgM,aAAa,CAAChM,WAAW,CAAC,CAAC;EAC3C;EACA;EACAoP,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,cAAc;EAC9B;EACA;EACAE,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACnB,YAAY;EAC5B;EACA;EACAoB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACX,YAAY;EAC5B;EACA;EACAY,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpJ,cAAc;EAC9B;EACA;EACAqB,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACF,qBAAqB;EACrC;EACA;EACAkI,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC1P,OAAO;EACvB;EACA;EACAsB,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACuL,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC8C,KAAK,CAAC,CAAC;IAClC;EACJ;EACA;EACAC,sBAAsBA,CAACC,QAAQ,EAAE;IAC7B,IAAIA,QAAQ,KAAK,IAAI,CAAChD,iBAAiB,EAAE;MACrC;IACJ;IACA,IAAI,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACf,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAACe,iBAAiB,GAAGgD,QAAQ;IACjC,IAAI,IAAI,CAAC3P,WAAW,CAAC,CAAC,EAAE;MACpB2P,QAAQ,CAACjS,MAAM,CAAC,IAAI,CAAC;MACrB,IAAI,CAAC0D,cAAc,CAAC,CAAC;IACzB;EACJ;EACA;EACAwO,UAAUA,CAACC,UAAU,EAAE;IACnB,IAAI,CAAC/P,OAAO,GAAAgQ,aAAA,CAAAA,aAAA,KAAQ,IAAI,CAAChQ,OAAO,GAAK+P,UAAU,CAAE;IACjD,IAAI,CAACnC,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACAqC,YAAYA,CAACC,GAAG,EAAE;IACd,IAAI,CAAClQ,OAAO,GAAAgQ,aAAA,CAAAA,aAAA,KAAQ,IAAI,CAAChQ,OAAO;MAAEmQ,SAAS,EAAED;IAAG,EAAE;IAClD,IAAI,CAACrC,uBAAuB,CAAC,CAAC;EAClC;EACA;EACAuC,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACjE,KAAK,EAAE;MACZ,IAAI,CAAC+B,cAAc,CAAC,IAAI,CAAC/B,KAAK,EAAEiE,OAAO,EAAE,IAAI,CAAC;IAClD;EACJ;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAACjE,KAAK,EAAE;MACZ,IAAI,CAAC+B,cAAc,CAAC,IAAI,CAAC/B,KAAK,EAAEiE,OAAO,EAAE,KAAK,CAAC;IACnD;EACJ;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,MAAMJ,SAAS,GAAG,IAAI,CAACnQ,OAAO,CAACmQ,SAAS;IACxC,IAAI,CAACA,SAAS,EAAE;MACZ,OAAO,KAAK;IAChB;IACA,OAAO,OAAOA,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGA,SAAS,CAAC/K,KAAK;EACtE;EACA;EACAoL,oBAAoBA,CAACX,QAAQ,EAAE;IAC3B,IAAIA,QAAQ,KAAK,IAAI,CAACjD,eAAe,EAAE;MACnC;IACJ;IACA,IAAI,CAACuC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACvC,eAAe,GAAGiD,QAAQ;IAC/B,IAAI,IAAI,CAAC3P,WAAW,CAAC,CAAC,EAAE;MACpB2P,QAAQ,CAACjS,MAAM,CAAC,IAAI,CAAC;MACrBiS,QAAQ,CAAChS,MAAM,CAAC,CAAC;IACrB;EACJ;EACA;EACAgQ,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC1B,KAAK,CAACpB,YAAY,CAAC,KAAK,EAAE,IAAI,CAACwF,YAAY,CAAC,CAAC,CAAC;EACvD;EACA;EACA3C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACxB,KAAK,EAAE;MACb;IACJ;IACA,MAAMhO,KAAK,GAAG,IAAI,CAACgO,KAAK,CAAChO,KAAK;IAC9BA,KAAK,CAACsB,KAAK,GAAG3D,mBAAmB,CAAC,IAAI,CAACiE,OAAO,CAACN,KAAK,CAAC;IACrDtB,KAAK,CAACoB,MAAM,GAAGzD,mBAAmB,CAAC,IAAI,CAACiE,OAAO,CAACR,MAAM,CAAC;IACvDpB,KAAK,CAACqS,QAAQ,GAAG1U,mBAAmB,CAAC,IAAI,CAACiE,OAAO,CAACyQ,QAAQ,CAAC;IAC3DrS,KAAK,CAACsS,SAAS,GAAG3U,mBAAmB,CAAC,IAAI,CAACiE,OAAO,CAAC0Q,SAAS,CAAC;IAC7DtS,KAAK,CAACuS,QAAQ,GAAG5U,mBAAmB,CAAC,IAAI,CAACiE,OAAO,CAAC2Q,QAAQ,CAAC;IAC3DvS,KAAK,CAACwS,SAAS,GAAG7U,mBAAmB,CAAC,IAAI,CAACiE,OAAO,CAAC4Q,SAAS,CAAC;EACjE;EACA;EACA5C,oBAAoBA,CAAC6C,aAAa,EAAE;IAChC,IAAI,CAACzE,KAAK,CAAChO,KAAK,CAAC4N,aAAa,GAAG6E,aAAa,GAAG,EAAE,GAAG,MAAM;EAChE;EACA;EACA3C,eAAeA,CAAA,EAAG;IAAA,IAAA4C,mBAAA;IACd,MAAMC,YAAY,GAAG,8BAA8B;IACnD,CAAAD,mBAAA,OAAI,CAAC1D,YAAY,cAAA0D,mBAAA,eAAjBA,mBAAA,CAAmBhF,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACsB,YAAY,GAAG,IAAI/B,WAAW,CAAC,IAAI,CAAC1N,SAAS,EAAE,IAAI,CAACgJ,SAAS,EAAE,IAAI,CAAC5G,OAAO,EAAEmG,KAAK,IAAI;MACvF,IAAI,CAACmJ,cAAc,CAAC9I,IAAI,CAACL,KAAK,CAAC;IACnC,CAAC,CAAC;IACF,IAAI,IAAI,CAACsG,mBAAmB,EAAE;MAC1B,IAAI,CAACY,YAAY,CAAC1L,OAAO,CAACrD,SAAS,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClF;IACA,IAAI,IAAI,CAAC0B,OAAO,CAACgR,aAAa,EAAE;MAC5B,IAAI,CAAC7C,cAAc,CAAC,IAAI,CAACf,YAAY,CAAC1L,OAAO,EAAE,IAAI,CAAC1B,OAAO,CAACgR,aAAa,EAAE,IAAI,CAAC;IACpF;IACA;IACA;IACA,IAAI,CAAC7E,KAAK,CAACqB,aAAa,CAACyD,YAAY,CAAC,IAAI,CAAC7D,YAAY,CAAC1L,OAAO,EAAE,IAAI,CAACyK,KAAK,CAAC;IAC5E;IACA,IAAI,CAAC,IAAI,CAACK,mBAAmB,IAAI,OAAO0E,qBAAqB,KAAK,WAAW,EAAE;MAC3E,IAAI,CAACnR,OAAO,CAAC0G,iBAAiB,CAAC,MAAM;QACjCyK,qBAAqB,CAAC;UAAA,IAAAC,mBAAA;UAAA,QAAAA,mBAAA,GAAM,IAAI,CAAC/D,YAAY,cAAA+D,mBAAA,uBAAjBA,mBAAA,CAAmBzP,OAAO,CAACrD,SAAS,CAACC,GAAG,CAACyS,YAAY,CAAC;QAAA,EAAC;MACvF,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC3D,YAAY,CAAC1L,OAAO,CAACrD,SAAS,CAACC,GAAG,CAACyS,YAAY,CAAC;IACzD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIpD,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACxB,KAAK,CAACiF,WAAW,EAAE;MACxB,IAAI,CAACjF,KAAK,CAAClD,UAAU,CAAC+B,WAAW,CAAC,IAAI,CAACmB,KAAK,CAAC;IACjD;EACJ;EACA;EACAwC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACnC,mBAAmB,EAAE;MAAA,IAAA6E,mBAAA;MAC1B,CAAAA,mBAAA,OAAI,CAACjE,YAAY,cAAAiE,mBAAA,eAAjBA,mBAAA,CAAmBvF,OAAO,CAAC,CAAC;MAC5B,IAAI,CAACsB,YAAY,GAAG,IAAI;IAC5B,CAAC,MACI;MAAA,IAAAkE,mBAAA;MACD,CAAAA,mBAAA,OAAI,CAAClE,YAAY,cAAAkE,mBAAA,eAAjBA,mBAAA,CAAmBlR,MAAM,CAAC,CAAC;IAC/B;EACJ;EACA;EACA+N,cAAcA,CAACzM,OAAO,EAAE6P,UAAU,EAAEC,KAAK,EAAE;IACvC,MAAMnB,OAAO,GAAGrU,WAAW,CAACuV,UAAU,IAAI,EAAE,CAAC,CAAC5V,MAAM,CAACG,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;IAC9D,IAAIuU,OAAO,CAACxK,MAAM,EAAE;MAChB2L,KAAK,GAAG9P,OAAO,CAACrD,SAAS,CAACC,GAAG,CAAC,GAAG+R,OAAO,CAAC,GAAG3O,OAAO,CAACrD,SAAS,CAACW,MAAM,CAAC,GAAGqR,OAAO,CAAC;IACpF;EACJ;EACA;EACAvB,uBAAuBA,CAAA,EAAG;IACtB;IACA;IACA;IACA,IAAI,CAAC/O,OAAO,CAAC0G,iBAAiB,CAAC,MAAM;MACjC;MACA;MACA;MACA,MAAMgL,YAAY,GAAG,IAAI,CAACzE,QAAQ,CAC7BtM,IAAI,CAAC9E,SAAS,CAACF,KAAK,CAAC,IAAI,CAAC0S,YAAY,EAAE,IAAI,CAACS,YAAY,CAAC,CAAC,CAAC,CAC5D5N,SAAS,CAAC,MAAM;QACjB;QACA;QACA,IAAI,CAAC,IAAI,CAACmL,KAAK,IAAI,CAAC,IAAI,CAACD,KAAK,IAAI,IAAI,CAACC,KAAK,CAACsF,QAAQ,CAAC7L,MAAM,KAAK,CAAC,EAAE;UAChE,IAAI,IAAI,CAACuG,KAAK,IAAI,IAAI,CAACpM,OAAO,CAACyE,UAAU,EAAE;YACvC,IAAI,CAAC0J,cAAc,CAAC,IAAI,CAAC/B,KAAK,EAAE,IAAI,CAACpM,OAAO,CAACyE,UAAU,EAAE,KAAK,CAAC;UACnE;UACA,IAAI,IAAI,CAAC0H,KAAK,IAAI,IAAI,CAACA,KAAK,CAACqB,aAAa,EAAE;YACxC,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACtB,KAAK,CAACqB,aAAa;YACnD,IAAI,CAACrB,KAAK,CAACnN,MAAM,CAAC,CAAC;UACvB;UACAyS,YAAY,CAAClQ,WAAW,CAAC,CAAC;QAC9B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACA4N,sBAAsBA,CAAA,EAAG;IAAA,IAAAwC,qBAAA;IACrB,MAAMhF,cAAc,GAAG,IAAI,CAACC,eAAe;IAC3CD,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEnO,OAAO,CAAC,CAAC;IACzBmO,cAAc,aAAdA,cAAc,gBAAAgF,qBAAA,GAAdhF,cAAc,CAAEvM,MAAM,cAAAuR,qBAAA,eAAtBA,qBAAA,CAAA5K,IAAA,CAAA4F,cAAyB,CAAC;EAC9B;AACJ;;AAEA;AACA;AACA;AACA,MAAMiF,gBAAgB,GAAG,6CAA6C;AACtE;AACA,MAAMC,cAAc,GAAG,eAAe;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iCAAiC,CAAC;EAkEpC;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,mBAAmB;EACnC;EACA3U,WAAWA,CAAC4U,WAAW,EAAE3U,cAAc,EAAEK,SAAS,EAAEqK,SAAS,EAAEkK,iBAAiB,EAAE;IAAA1U,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAjElF;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,+BACuB;MAAEkC,KAAK,EAAE,CAAC;MAAEF,MAAM,EAAE;IAAE,CAAC;IAC9C;IAAAhC,eAAA,oBACY,KAAK;IACjB;IAAAA,eAAA,mBACW,IAAI;IACf;IAAAA,eAAA,yBACiB,KAAK;IACtB;IAAAA,eAAA,iCACyB,IAAI;IAC7B;IAAAA,eAAA,0BACkB,KAAK;IACvB;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,0BACkB,CAAC;IACnB;IAAAA,eAAA,uBACe,EAAE;IACjB;IAAAA,eAAA,8BACsB,EAAE;IACxB;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,2BACmB,IAAIhC,OAAO,CAAC,CAAC;IAChC;IAAAgC,eAAA,8BACsB/B,YAAY,CAACiR,KAAK;IACxC;IAAAlP,eAAA,mBACW,CAAC;IACZ;IAAAA,eAAA,mBACW,CAAC;IACZ;IAAAA,eAAA;IAEA;IAAAA,eAAA,+BACuB,EAAE;IACzB;IAAAA,eAAA;IAEA;IAAAA,eAAA,0BACkB,IAAI,CAAC2U,gBAAgB;IAMnC,IAAI,CAAC7U,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACK,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACqK,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkK,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,SAAS,CAACH,WAAW,CAAC;EAC/B;EACA;EACArU,MAAMA,CAACyC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,IAChBI,UAAU,KAAK,IAAI,CAACJ,WAAW,KAC9B,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMV,KAAK,CAAC,0DAA0D,CAAC;IAC3E;IACA,IAAI,CAACyS,kBAAkB,CAAC,CAAC;IACzBhS,UAAU,CAACgN,WAAW,CAAChP,SAAS,CAACC,GAAG,CAACsT,gBAAgB,CAAC;IACtD,IAAI,CAAC3R,WAAW,GAAGI,UAAU;IAC7B,IAAI,CAACiS,YAAY,GAAGjS,UAAU,CAACgN,WAAW;IAC1C,IAAI,CAACjB,KAAK,GAAG/L,UAAU,CAACO,cAAc;IACtC,IAAI,CAAC2R,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,mBAAmB,CAACnR,WAAW,CAAC,CAAC;IACtC,IAAI,CAACmR,mBAAmB,GAAG,IAAI,CAACpV,cAAc,CAACqV,MAAM,CAAC,CAAC,CAAC1R,SAAS,CAAC,MAAM;MACpE;MACA;MACA;MACA,IAAI,CAACuR,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC7C,KAAK,CAAC,CAAC;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,IAAI,CAAC4C,WAAW,IAAI,CAAC,IAAI,CAACvK,SAAS,CAAC0C,SAAS,EAAE;MAC/C;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC8H,gBAAgB,IAAI,IAAI,CAACI,eAAe,IAAI,IAAI,CAACH,aAAa,EAAE;MACtE,IAAI,CAACI,mBAAmB,CAAC,CAAC;MAC1B;IACJ;IACA,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B;IACA;IACA;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;IACpD,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACxC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACjH,KAAK,CAACrJ,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACuQ,cAAc,GAAG,IAAI,CAACpB,iBAAiB,CAAC5H,mBAAmB,CAAC,CAAC,CAACvH,qBAAqB,CAAC,CAAC;IAC1F,MAAMwQ,UAAU,GAAG,IAAI,CAACJ,WAAW;IACnC,MAAMrQ,WAAW,GAAG,IAAI,CAACuQ,YAAY;IACrC,MAAMG,YAAY,GAAG,IAAI,CAACP,aAAa;IACvC,MAAMQ,aAAa,GAAG,IAAI,CAACH,cAAc;IACzC;IACA,MAAMI,YAAY,GAAG,EAAE;IACvB;IACA,IAAIC,QAAQ;IACZ;IACA;IACA,KAAK,IAAIC,GAAG,IAAI,IAAI,CAAC5B,mBAAmB,EAAE;MACtC;MACA,IAAI6B,WAAW,GAAG,IAAI,CAACC,eAAe,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,CAAC;MACtE;MACA;MACA;MACA,IAAIG,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE/Q,WAAW,EAAE8Q,GAAG,CAAC;MACvE;MACA,IAAIK,UAAU,GAAG,IAAI,CAACC,cAAc,CAACH,YAAY,EAAEjR,WAAW,EAAE0Q,YAAY,EAAEI,GAAG,CAAC;MAClF;MACA,IAAIK,UAAU,CAACE,0BAA0B,EAAE;QACvC,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACC,cAAc,CAACT,GAAG,EAAEC,WAAW,CAAC;QACrC;MACJ;MACA;MACA;MACA,IAAI,IAAI,CAACS,6BAA6B,CAACL,UAAU,EAAEF,YAAY,EAAEP,YAAY,CAAC,EAAE;QAC5E;QACA;QACAE,YAAY,CAACjO,IAAI,CAAC;UACd8O,QAAQ,EAAEX,GAAG;UACbvP,MAAM,EAAEwP,WAAW;UACnB/Q,WAAW;UACX0R,eAAe,EAAE,IAAI,CAACC,yBAAyB,CAACZ,WAAW,EAAED,GAAG;QACpE,CAAC,CAAC;QACF;MACJ;MACA;MACA;MACA;MACA,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACM,UAAU,CAACS,WAAW,GAAGT,UAAU,CAACS,WAAW,EAAE;QACvEf,QAAQ,GAAG;UAAEM,UAAU;UAAEF,YAAY;UAAEF,WAAW;UAAEU,QAAQ,EAAEX,GAAG;UAAE9Q;QAAY,CAAC;MACpF;IACJ;IACA;IACA;IACA,IAAI4Q,YAAY,CAAC7N,MAAM,EAAE;MACrB,IAAI8O,OAAO,GAAG,IAAI;MAClB,IAAIC,SAAS,GAAG,CAAC,CAAC;MAClB,KAAK,MAAMC,GAAG,IAAInB,YAAY,EAAE;QAC5B,MAAMoB,KAAK,GAAGD,GAAG,CAACL,eAAe,CAAC9U,KAAK,GAAGmV,GAAG,CAACL,eAAe,CAAChV,MAAM,IAAIqV,GAAG,CAACN,QAAQ,CAACQ,MAAM,IAAI,CAAC,CAAC;QACjG,IAAID,KAAK,GAAGF,SAAS,EAAE;UACnBA,SAAS,GAAGE,KAAK;UACjBH,OAAO,GAAGE,GAAG;QACjB;MACJ;MACA,IAAI,CAACT,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,cAAc,CAACM,OAAO,CAACJ,QAAQ,EAAEI,OAAO,CAACtQ,MAAM,CAAC;MACrD;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAAC2Q,QAAQ,EAAE;MACf;MACA,IAAI,CAACZ,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,cAAc,CAACV,QAAQ,CAACY,QAAQ,EAAEZ,QAAQ,CAACE,WAAW,CAAC;MAC5D;IACJ;IACA;IACA;IACA,IAAI,CAACQ,cAAc,CAACV,QAAQ,CAACY,QAAQ,EAAEZ,QAAQ,CAACE,WAAW,CAAC;EAChE;EACAzT,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC0S,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACL,aAAa,GAAG,IAAI;IACzB,IAAI,CAACwC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACvC,mBAAmB,CAACnR,WAAW,CAAC,CAAC;EAC1C;EACA;EACAuK,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACyG,WAAW,EAAE;MAClB;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACD,YAAY,EAAE;MACnB4C,YAAY,CAAC,IAAI,CAAC5C,YAAY,CAAClU,KAAK,EAAE;QAClCX,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRwE,KAAK,EAAE,EAAE;QACTH,MAAM,EAAE,EAAE;QACVvC,MAAM,EAAE,EAAE;QACVE,KAAK,EAAE,EAAE;QACTyV,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE;MACpB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAAChJ,KAAK,EAAE;MACZ,IAAI,CAAC2G,0BAA0B,CAAC,CAAC;IACrC;IACA,IAAI,IAAI,CAAC9S,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACoN,WAAW,CAAChP,SAAS,CAACW,MAAM,CAAC4S,gBAAgB,CAAC;IACnE;IACA,IAAI,CAACxR,MAAM,CAAC,CAAC;IACb,IAAI,CAAC+R,gBAAgB,CAAC/C,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACnP,WAAW,GAAG,IAAI,CAACqS,YAAY,GAAG,IAAI;IAC3C,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACIM,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACN,WAAW,IAAI,CAAC,IAAI,CAACvK,SAAS,CAAC0C,SAAS,EAAE;MAC/C;IACJ;IACA,MAAM2K,YAAY,GAAG,IAAI,CAAC5C,aAAa;IACvC,IAAI4C,YAAY,EAAE;MACd,IAAI,CAAClC,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACxC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACjH,KAAK,CAACrJ,qBAAqB,CAAC,CAAC;MACtD,IAAI,CAACkQ,aAAa,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;MACpD,IAAI,CAACI,cAAc,GAAG,IAAI,CAACpB,iBAAiB,CAAC5H,mBAAmB,CAAC,CAAC,CAACvH,qBAAqB,CAAC,CAAC;MAC1F,MAAM8Q,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAACX,WAAW,EAAE,IAAI,CAACG,cAAc,EAAE+B,YAAY,CAAC;MAC7F,IAAI,CAAChB,cAAc,CAACgB,YAAY,EAAExB,WAAW,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAAClE,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2F,wBAAwBA,CAACC,WAAW,EAAE;IAClC,IAAI,CAACC,YAAY,GAAGD,WAAW;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,aAAaA,CAAC1D,SAAS,EAAE;IACrB,IAAI,CAACC,mBAAmB,GAAGD,SAAS;IACpC;IACA;IACA,IAAIA,SAAS,CAACpM,OAAO,CAAC,IAAI,CAAC8M,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACA,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACJ,kBAAkB,CAAC,CAAC;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIqD,kBAAkBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAACC,eAAe,GAAGD,MAAM;IAC7B,OAAO,IAAI;EACf;EACA;EACAE,sBAAsBA,CAACC,kBAAkB,GAAG,IAAI,EAAE;IAC9C,IAAI,CAACC,sBAAsB,GAAGD,kBAAkB;IAChD,OAAO,IAAI;EACf;EACA;EACAE,iBAAiBA,CAACC,aAAa,GAAG,IAAI,EAAE;IACpC,IAAI,CAACC,cAAc,GAAGD,aAAa;IACnC,OAAO,IAAI;EACf;EACA;EACAE,QAAQA,CAACC,OAAO,GAAG,IAAI,EAAE;IACrB,IAAI,CAACpB,QAAQ,GAAGoB,OAAO;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,kBAAkBA,CAACC,QAAQ,GAAG,IAAI,EAAE;IAChC,IAAI,CAAC1D,eAAe,GAAG0D,QAAQ;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIlE,SAASA,CAAC/N,MAAM,EAAE;IACd,IAAI,CAACkS,OAAO,GAAGlS,MAAM;IACrB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACImS,kBAAkBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAACC,QAAQ,GAAGD,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,kBAAkBA,CAACF,MAAM,EAAE;IACvB,IAAI,CAACG,QAAQ,GAAGH,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACII,qBAAqBA,CAACC,QAAQ,EAAE;IAC5B,IAAI,CAACC,wBAAwB,GAAGD,QAAQ;IACxC,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIhD,eAAeA,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,EAAE;IAC5C,IAAIoD,CAAC;IACL,IAAIpD,GAAG,CAAClP,OAAO,IAAI,QAAQ,EAAE;MACzB;MACA;MACAsS,CAAC,GAAGzD,UAAU,CAAC7V,IAAI,GAAG6V,UAAU,CAAC7T,KAAK,GAAG,CAAC;IAC9C,CAAC,MACI;MACD,MAAMuX,MAAM,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG3D,UAAU,CAACrR,KAAK,GAAGqR,UAAU,CAAC7V,IAAI;MACjE,MAAMyZ,IAAI,GAAG,IAAI,CAACD,MAAM,CAAC,CAAC,GAAG3D,UAAU,CAAC7V,IAAI,GAAG6V,UAAU,CAACrR,KAAK;MAC/D8U,CAAC,GAAGpD,GAAG,CAAClP,OAAO,IAAI,OAAO,GAAGuS,MAAM,GAAGE,IAAI;IAC9C;IACA;IACA;IACA,IAAI1D,aAAa,CAAC/V,IAAI,GAAG,CAAC,EAAE;MACxBsZ,CAAC,IAAIvD,aAAa,CAAC/V,IAAI;IAC3B;IACA,IAAI0Z,CAAC;IACL,IAAIxD,GAAG,CAACjP,OAAO,IAAI,QAAQ,EAAE;MACzByS,CAAC,GAAG7D,UAAU,CAAC9V,GAAG,GAAG8V,UAAU,CAAC/T,MAAM,GAAG,CAAC;IAC9C,CAAC,MACI;MACD4X,CAAC,GAAGxD,GAAG,CAACjP,OAAO,IAAI,KAAK,GAAG4O,UAAU,CAAC9V,GAAG,GAAG8V,UAAU,CAACxR,MAAM;IACjE;IACA;IACA;IACA;IACA;IACA;IACA,IAAI0R,aAAa,CAAChW,GAAG,GAAG,CAAC,EAAE;MACvB2Z,CAAC,IAAI3D,aAAa,CAAChW,GAAG;IAC1B;IACA,OAAO;MAAEuZ,CAAC;MAAEI;IAAE,CAAC;EACnB;EACA;AACJ;AACA;AACA;EACIpD,gBAAgBA,CAACH,WAAW,EAAE/Q,WAAW,EAAE8Q,GAAG,EAAE;IAC5C;IACA;IACA,IAAIyD,aAAa;IACjB,IAAIzD,GAAG,CAAChP,QAAQ,IAAI,QAAQ,EAAE;MAC1ByS,aAAa,GAAG,CAACvU,WAAW,CAACpD,KAAK,GAAG,CAAC;IAC1C,CAAC,MACI,IAAIkU,GAAG,CAAChP,QAAQ,KAAK,OAAO,EAAE;MAC/ByS,aAAa,GAAG,IAAI,CAACH,MAAM,CAAC,CAAC,GAAG,CAACpU,WAAW,CAACpD,KAAK,GAAG,CAAC;IAC1D,CAAC,MACI;MACD2X,aAAa,GAAG,IAAI,CAACH,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAACpU,WAAW,CAACpD,KAAK;IAC1D;IACA,IAAI4X,aAAa;IACjB,IAAI1D,GAAG,CAAC/O,QAAQ,IAAI,QAAQ,EAAE;MAC1ByS,aAAa,GAAG,CAACxU,WAAW,CAACtD,MAAM,GAAG,CAAC;IAC3C,CAAC,MACI;MACD8X,aAAa,GAAG1D,GAAG,CAAC/O,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC/B,WAAW,CAACtD,MAAM;IACnE;IACA;IACA,OAAO;MACHwX,CAAC,EAAEnD,WAAW,CAACmD,CAAC,GAAGK,aAAa;MAChCD,CAAC,EAAEvD,WAAW,CAACuD,CAAC,GAAGE;IACvB,CAAC;EACL;EACA;EACApD,cAAcA,CAACqD,KAAK,EAAEC,cAAc,EAAEnY,QAAQ,EAAEkV,QAAQ,EAAE;IACtD;IACA;IACA,MAAMjQ,OAAO,GAAGmT,4BAA4B,CAACD,cAAc,CAAC;IAC5D,IAAI;MAAER,CAAC;MAAEI;IAAE,CAAC,GAAGG,KAAK;IACpB,IAAIhT,OAAO,GAAG,IAAI,CAACmT,UAAU,CAACnD,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAI/P,OAAO,GAAG,IAAI,CAACkT,UAAU,CAACnD,QAAQ,EAAE,GAAG,CAAC;IAC5C;IACA,IAAIhQ,OAAO,EAAE;MACTyS,CAAC,IAAIzS,OAAO;IAChB;IACA,IAAIC,OAAO,EAAE;MACT4S,CAAC,IAAI5S,OAAO;IAChB;IACA;IACA,IAAImT,YAAY,GAAG,CAAC,GAAGX,CAAC;IACxB,IAAIY,aAAa,GAAGZ,CAAC,GAAG1S,OAAO,CAAC5E,KAAK,GAAGL,QAAQ,CAACK,KAAK;IACtD,IAAImY,WAAW,GAAG,CAAC,GAAGT,CAAC;IACvB,IAAIU,cAAc,GAAGV,CAAC,GAAG9S,OAAO,CAAC9E,MAAM,GAAGH,QAAQ,CAACG,MAAM;IACzD;IACA,IAAIuY,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAAC1T,OAAO,CAAC5E,KAAK,EAAEiY,YAAY,EAAEC,aAAa,CAAC;IACtF,IAAIK,aAAa,GAAG,IAAI,CAACD,kBAAkB,CAAC1T,OAAO,CAAC9E,MAAM,EAAEqY,WAAW,EAAEC,cAAc,CAAC;IACxF,IAAIpD,WAAW,GAAGqD,YAAY,GAAGE,aAAa;IAC9C,OAAO;MACHvD,WAAW;MACXP,0BAA0B,EAAE7P,OAAO,CAAC5E,KAAK,GAAG4E,OAAO,CAAC9E,MAAM,KAAKkV,WAAW;MAC1EwD,wBAAwB,EAAED,aAAa,KAAK3T,OAAO,CAAC9E,MAAM;MAC1D2Y,0BAA0B,EAAEJ,YAAY,IAAIzT,OAAO,CAAC5E;IACxD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4U,6BAA6BA,CAACO,GAAG,EAAE0C,KAAK,EAAElY,QAAQ,EAAE;IAChD,IAAI,IAAI,CAAC0W,sBAAsB,EAAE;MAC7B,MAAMqC,eAAe,GAAG/Y,QAAQ,CAAC0C,MAAM,GAAGwV,KAAK,CAACH,CAAC;MACjD,MAAMiB,cAAc,GAAGhZ,QAAQ,CAAC6C,KAAK,GAAGqV,KAAK,CAACP,CAAC;MAC/C,MAAMtG,SAAS,GAAG4H,aAAa,CAAC,IAAI,CAACrY,WAAW,CAACyP,SAAS,CAAC,CAAC,CAACgB,SAAS,CAAC;MACvE,MAAMD,QAAQ,GAAG6H,aAAa,CAAC,IAAI,CAACrY,WAAW,CAACyP,SAAS,CAAC,CAAC,CAACe,QAAQ,CAAC;MACrE,MAAM8H,WAAW,GAAG1D,GAAG,CAACqD,wBAAwB,IAAKxH,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAI0H,eAAgB;MACvG,MAAMI,aAAa,GAAG3D,GAAG,CAACsD,0BAA0B,IAAK1H,QAAQ,IAAI,IAAI,IAAIA,QAAQ,IAAI4H,cAAe;MACxG,OAAOE,WAAW,IAAIC,aAAa;IACvC;IACA,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,oBAAoBA,CAACC,KAAK,EAAElB,cAAc,EAAEtW,cAAc,EAAE;IACxD;IACA;IACA;IACA,IAAI,IAAI,CAAC+T,mBAAmB,IAAI,IAAI,CAACrC,eAAe,EAAE;MAClD,OAAO;QACHoE,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAAC/B,mBAAmB,CAAC+B,CAAC;QACvCI,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG,IAAI,CAACnC,mBAAmB,CAACmC;MAC1C,CAAC;IACL;IACA;IACA;IACA,MAAM9S,OAAO,GAAGmT,4BAA4B,CAACD,cAAc,CAAC;IAC5D,MAAMnY,QAAQ,GAAG,IAAI,CAAC4T,aAAa;IACnC;IACA;IACA,MAAM0F,aAAa,GAAGxX,IAAI,CAACyX,GAAG,CAACF,KAAK,CAAC1B,CAAC,GAAG1S,OAAO,CAAC5E,KAAK,GAAGL,QAAQ,CAACK,KAAK,EAAE,CAAC,CAAC;IAC3E,MAAMmZ,cAAc,GAAG1X,IAAI,CAACyX,GAAG,CAACF,KAAK,CAACtB,CAAC,GAAG9S,OAAO,CAAC9E,MAAM,GAAGH,QAAQ,CAACG,MAAM,EAAE,CAAC,CAAC;IAC9E,MAAMsZ,WAAW,GAAG3X,IAAI,CAACyX,GAAG,CAACvZ,QAAQ,CAAC5B,GAAG,GAAGyD,cAAc,CAACzD,GAAG,GAAGib,KAAK,CAACtB,CAAC,EAAE,CAAC,CAAC;IAC5E,MAAM2B,YAAY,GAAG5X,IAAI,CAACyX,GAAG,CAACvZ,QAAQ,CAAC3B,IAAI,GAAGwD,cAAc,CAACxD,IAAI,GAAGgb,KAAK,CAAC1B,CAAC,EAAE,CAAC,CAAC;IAC/E;IACA,IAAIgC,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb;IACA;IACA;IACA,IAAI3U,OAAO,CAAC5E,KAAK,IAAIL,QAAQ,CAACK,KAAK,EAAE;MACjCsZ,KAAK,GAAGD,YAAY,IAAI,CAACJ,aAAa;IAC1C,CAAC,MACI;MACDK,KAAK,GAAGN,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAACpB,eAAe,GAAGvW,QAAQ,CAAC3B,IAAI,GAAGwD,cAAc,CAACxD,IAAI,GAAGgb,KAAK,CAAC1B,CAAC,GAAG,CAAC;IAC9F;IACA,IAAI1S,OAAO,CAAC9E,MAAM,IAAIH,QAAQ,CAACG,MAAM,EAAE;MACnCyZ,KAAK,GAAGH,WAAW,IAAI,CAACD,cAAc;IAC1C,CAAC,MACI;MACDI,KAAK,GAAGP,KAAK,CAACtB,CAAC,GAAG,IAAI,CAACxB,eAAe,GAAGvW,QAAQ,CAAC5B,GAAG,GAAGyD,cAAc,CAACzD,GAAG,GAAGib,KAAK,CAACtB,CAAC,GAAG,CAAC;IAC5F;IACA,IAAI,CAACnC,mBAAmB,GAAG;MAAE+B,CAAC,EAAEgC,KAAK;MAAE5B,CAAC,EAAE6B;IAAM,CAAC;IACjD,OAAO;MACHjC,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAGgC,KAAK;MAClB5B,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG6B;IACjB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACI5E,cAAcA,CAACE,QAAQ,EAAEV,WAAW,EAAE;IAClC,IAAI,CAACqF,mBAAmB,CAAC3E,QAAQ,CAAC;IAClC,IAAI,CAAC4E,wBAAwB,CAACtF,WAAW,EAAEU,QAAQ,CAAC;IACpD,IAAI,CAAC6E,qBAAqB,CAACvF,WAAW,EAAEU,QAAQ,CAAC;IACjD,IAAIA,QAAQ,CAAC9P,UAAU,EAAE;MACrB,IAAI,CAAC4U,gBAAgB,CAAC9E,QAAQ,CAAC9P,UAAU,CAAC;IAC9C;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC0N,gBAAgB,CAAC7L,SAAS,CAACT,MAAM,EAAE;MACxC,MAAMyT,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;MACpD;MACA;MACA,IAAIhF,QAAQ,KAAK,IAAI,CAAC9B,aAAa,IAC/B,CAAC,IAAI,CAAC+G,qBAAqB,IAC3B,CAACC,uBAAuB,CAAC,IAAI,CAACD,qBAAqB,EAAEF,gBAAgB,CAAC,EAAE;QACxE,MAAMI,WAAW,GAAG,IAAI3U,8BAA8B,CAACwP,QAAQ,EAAE+E,gBAAgB,CAAC;QAClF,IAAI,CAACnH,gBAAgB,CAAC5L,IAAI,CAACmT,WAAW,CAAC;MAC3C;MACA,IAAI,CAACF,qBAAqB,GAAGF,gBAAgB;IACjD;IACA;IACA,IAAI,CAAC7G,aAAa,GAAG8B,QAAQ;IAC7B,IAAI,CAAC/B,gBAAgB,GAAG,KAAK;EACjC;EACA;EACA0G,mBAAmBA,CAAC3E,QAAQ,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACwC,wBAAwB,EAAE;MAChC;IACJ;IACA,MAAM4C,QAAQ,GAAG,IAAI,CAACrH,YAAY,CAAC1H,gBAAgB,CAAC,IAAI,CAACmM,wBAAwB,CAAC;IAClF,IAAI6C,OAAO;IACX,IAAIC,OAAO,GAAGtF,QAAQ,CAAC1P,QAAQ;IAC/B,IAAI0P,QAAQ,CAAC3P,QAAQ,KAAK,QAAQ,EAAE;MAChCgV,OAAO,GAAG,QAAQ;IACtB,CAAC,MACI,IAAI,IAAI,CAAC1C,MAAM,CAAC,CAAC,EAAE;MACpB0C,OAAO,GAAGrF,QAAQ,CAAC3P,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;IAC9D,CAAC,MACI;MACDgV,OAAO,GAAGrF,QAAQ,CAAC3P,QAAQ,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC9D;IACA,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuT,QAAQ,CAAC9T,MAAM,EAAEO,CAAC,EAAE,EAAE;MACtCuT,QAAQ,CAACvT,CAAC,CAAC,CAAChI,KAAK,CAAC0b,eAAe,GAAG,GAAGF,OAAO,IAAIC,OAAO,EAAE;IAC/D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIpF,yBAAyBA,CAACpQ,MAAM,EAAEkQ,QAAQ,EAAE;IACxC,MAAMlV,QAAQ,GAAG,IAAI,CAAC4T,aAAa;IACnC,MAAM8G,KAAK,GAAG,IAAI,CAAC7C,MAAM,CAAC,CAAC;IAC3B,IAAI1X,MAAM,EAAE/B,GAAG,EAAEsE,MAAM;IACvB,IAAIwS,QAAQ,CAAC1P,QAAQ,KAAK,KAAK,EAAE;MAC7B;MACApH,GAAG,GAAG4G,MAAM,CAAC+S,CAAC;MACd5X,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAG/B,GAAG,GAAG,IAAI,CAACmY,eAAe;IACzD,CAAC,MACI,IAAIrB,QAAQ,CAAC1P,QAAQ,KAAK,QAAQ,EAAE;MACrC;MACA;MACA;MACA9C,MAAM,GAAG1C,QAAQ,CAACG,MAAM,GAAG6E,MAAM,CAAC+S,CAAC,GAAG,IAAI,CAACxB,eAAe,GAAG,CAAC;MAC9DpW,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAGuC,MAAM,GAAG,IAAI,CAAC6T,eAAe;IAC5D,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAMoE,8BAA8B,GAAG7Y,IAAI,CAAC8Y,GAAG,CAAC5a,QAAQ,CAAC0C,MAAM,GAAGsC,MAAM,CAAC+S,CAAC,GAAG/X,QAAQ,CAAC5B,GAAG,EAAE4G,MAAM,CAAC+S,CAAC,CAAC;MACpG,MAAM8C,cAAc,GAAG,IAAI,CAACC,oBAAoB,CAAC3a,MAAM;MACvDA,MAAM,GAAGwa,8BAA8B,GAAG,CAAC;MAC3Cvc,GAAG,GAAG4G,MAAM,CAAC+S,CAAC,GAAG4C,8BAA8B;MAC/C,IAAIxa,MAAM,GAAG0a,cAAc,IAAI,CAAC,IAAI,CAAC1H,gBAAgB,IAAI,CAAC,IAAI,CAAC0D,cAAc,EAAE;QAC3EzY,GAAG,GAAG4G,MAAM,CAAC+S,CAAC,GAAG8C,cAAc,GAAG,CAAC;MACvC;IACJ;IACA;IACA,MAAME,4BAA4B,GAAI7F,QAAQ,CAAC3P,QAAQ,KAAK,OAAO,IAAI,CAACmV,KAAK,IAAMxF,QAAQ,CAAC3P,QAAQ,KAAK,KAAK,IAAImV,KAAM;IACxH;IACA,MAAMM,2BAA2B,GAAI9F,QAAQ,CAAC3P,QAAQ,KAAK,KAAK,IAAI,CAACmV,KAAK,IAAMxF,QAAQ,CAAC3P,QAAQ,KAAK,OAAO,IAAImV,KAAM;IACvH,IAAIra,KAAK,EAAEhC,IAAI,EAAEwE,KAAK;IACtB,IAAImY,2BAA2B,EAAE;MAC7BnY,KAAK,GAAG7C,QAAQ,CAACK,KAAK,GAAG2E,MAAM,CAAC2S,CAAC,GAAG,IAAI,CAACpB,eAAe,GAAG,CAAC;MAC5DlW,KAAK,GAAG2E,MAAM,CAAC2S,CAAC,GAAG,IAAI,CAACpB,eAAe;IAC3C,CAAC,MACI,IAAIwE,4BAA4B,EAAE;MACnC1c,IAAI,GAAG2G,MAAM,CAAC2S,CAAC;MACftX,KAAK,GAAGL,QAAQ,CAAC6C,KAAK,GAAGmC,MAAM,CAAC2S,CAAC;IACrC,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAMgD,8BAA8B,GAAG7Y,IAAI,CAAC8Y,GAAG,CAAC5a,QAAQ,CAAC6C,KAAK,GAAGmC,MAAM,CAAC2S,CAAC,GAAG3X,QAAQ,CAAC3B,IAAI,EAAE2G,MAAM,CAAC2S,CAAC,CAAC;MACpG,MAAMsD,aAAa,GAAG,IAAI,CAACH,oBAAoB,CAACza,KAAK;MACrDA,KAAK,GAAGsa,8BAA8B,GAAG,CAAC;MAC1Ctc,IAAI,GAAG2G,MAAM,CAAC2S,CAAC,GAAGgD,8BAA8B;MAChD,IAAIta,KAAK,GAAG4a,aAAa,IAAI,CAAC,IAAI,CAAC9H,gBAAgB,IAAI,CAAC,IAAI,CAAC0D,cAAc,EAAE;QACzExY,IAAI,GAAG2G,MAAM,CAAC2S,CAAC,GAAGsD,aAAa,GAAG,CAAC;MACvC;IACJ;IACA,OAAO;MAAE7c,GAAG,EAAEA,GAAG;MAAEC,IAAI,EAAEA,IAAI;MAAEqE,MAAM,EAAEA,MAAM;MAAEG,KAAK,EAAEA,KAAK;MAAExC,KAAK;MAAEF;IAAO,CAAC;EAChF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI4Z,qBAAqBA,CAAC/U,MAAM,EAAEkQ,QAAQ,EAAE;IACpC,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACpQ,MAAM,EAAEkQ,QAAQ,CAAC;IACxE;IACA;IACA,IAAI,CAAC,IAAI,CAAC/B,gBAAgB,IAAI,CAAC,IAAI,CAAC0D,cAAc,EAAE;MAChD1B,eAAe,CAAChV,MAAM,GAAG2B,IAAI,CAAC8Y,GAAG,CAACzF,eAAe,CAAChV,MAAM,EAAE,IAAI,CAAC2a,oBAAoB,CAAC3a,MAAM,CAAC;MAC3FgV,eAAe,CAAC9U,KAAK,GAAGyB,IAAI,CAAC8Y,GAAG,CAACzF,eAAe,CAAC9U,KAAK,EAAE,IAAI,CAACya,oBAAoB,CAACza,KAAK,CAAC;IAC5F;IACA,MAAMoK,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,IAAI,CAACyQ,iBAAiB,CAAC,CAAC,EAAE;MAC1BzQ,MAAM,CAACrM,GAAG,GAAGqM,MAAM,CAACpM,IAAI,GAAG,GAAG;MAC9BoM,MAAM,CAAC/H,MAAM,GAAG+H,MAAM,CAAC5H,KAAK,GAAG4H,MAAM,CAAC8G,SAAS,GAAG9G,MAAM,CAAC6G,QAAQ,GAAG,EAAE;MACtE7G,MAAM,CAACpK,KAAK,GAAGoK,MAAM,CAACtK,MAAM,GAAG,MAAM;IACzC,CAAC,MACI;MACD,MAAMoR,SAAS,GAAG,IAAI,CAAC3Q,WAAW,CAACyP,SAAS,CAAC,CAAC,CAACkB,SAAS;MACxD,MAAMD,QAAQ,GAAG,IAAI,CAAC1Q,WAAW,CAACyP,SAAS,CAAC,CAAC,CAACiB,QAAQ;MACtD7G,MAAM,CAACtK,MAAM,GAAGzD,mBAAmB,CAACyY,eAAe,CAAChV,MAAM,CAAC;MAC3DsK,MAAM,CAACrM,GAAG,GAAG1B,mBAAmB,CAACyY,eAAe,CAAC/W,GAAG,CAAC;MACrDqM,MAAM,CAAC/H,MAAM,GAAGhG,mBAAmB,CAACyY,eAAe,CAACzS,MAAM,CAAC;MAC3D+H,MAAM,CAACpK,KAAK,GAAG3D,mBAAmB,CAACyY,eAAe,CAAC9U,KAAK,CAAC;MACzDoK,MAAM,CAACpM,IAAI,GAAG3B,mBAAmB,CAACyY,eAAe,CAAC9W,IAAI,CAAC;MACvDoM,MAAM,CAAC5H,KAAK,GAAGnG,mBAAmB,CAACyY,eAAe,CAACtS,KAAK,CAAC;MACzD;MACA,IAAIqS,QAAQ,CAAC3P,QAAQ,KAAK,QAAQ,EAAE;QAChCkF,MAAM,CAACqL,UAAU,GAAG,QAAQ;MAChC,CAAC,MACI;QACDrL,MAAM,CAACqL,UAAU,GAAGZ,QAAQ,CAAC3P,QAAQ,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY;MAC/E;MACA,IAAI2P,QAAQ,CAAC1P,QAAQ,KAAK,QAAQ,EAAE;QAChCiF,MAAM,CAACsL,cAAc,GAAG,QAAQ;MACpC,CAAC,MACI;QACDtL,MAAM,CAACsL,cAAc,GAAGb,QAAQ,CAAC1P,QAAQ,KAAK,QAAQ,GAAG,UAAU,GAAG,YAAY;MACtF;MACA,IAAI+L,SAAS,EAAE;QACX9G,MAAM,CAAC8G,SAAS,GAAG7U,mBAAmB,CAAC6U,SAAS,CAAC;MACrD;MACA,IAAID,QAAQ,EAAE;QACV7G,MAAM,CAAC6G,QAAQ,GAAG5U,mBAAmB,CAAC4U,QAAQ,CAAC;MACnD;IACJ;IACA,IAAI,CAACwJ,oBAAoB,GAAG3F,eAAe;IAC3CU,YAAY,CAAC,IAAI,CAAC5C,YAAY,CAAClU,KAAK,EAAE0L,MAAM,CAAC;EACjD;EACA;EACAkJ,uBAAuBA,CAAA,EAAG;IACtBkC,YAAY,CAAC,IAAI,CAAC5C,YAAY,CAAClU,KAAK,EAAE;MAClCX,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,GAAG;MACTwE,KAAK,EAAE,GAAG;MACVH,MAAM,EAAE,GAAG;MACXvC,MAAM,EAAE,EAAE;MACVE,KAAK,EAAE,EAAE;MACTyV,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE;IACpB,CAAC,CAAC;EACN;EACA;EACArC,0BAA0BA,CAAA,EAAG;IACzBmC,YAAY,CAAC,IAAI,CAAC9I,KAAK,CAAChO,KAAK,EAAE;MAC3BX,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRqE,MAAM,EAAE,EAAE;MACVG,KAAK,EAAE,EAAE;MACTqS,QAAQ,EAAE,EAAE;MACZiG,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EACA;EACArB,wBAAwBA,CAACtF,WAAW,EAAEU,QAAQ,EAAE;IAC5C,MAAMzK,MAAM,GAAG,CAAC,CAAC;IACjB,MAAM2Q,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC;IACjD,MAAMG,qBAAqB,GAAG,IAAI,CAAC3E,sBAAsB;IACzD,MAAM7S,MAAM,GAAG,IAAI,CAACjD,WAAW,CAACyP,SAAS,CAAC,CAAC;IAC3C,IAAI+K,gBAAgB,EAAE;MAClB,MAAMvZ,cAAc,GAAG,IAAI,CAAC5D,cAAc,CAACY,yBAAyB,CAAC,CAAC;MACtEgX,YAAY,CAACpL,MAAM,EAAE,IAAI,CAAC6Q,iBAAiB,CAACpG,QAAQ,EAAEV,WAAW,EAAE3S,cAAc,CAAC,CAAC;MACnFgU,YAAY,CAACpL,MAAM,EAAE,IAAI,CAAC8Q,iBAAiB,CAACrG,QAAQ,EAAEV,WAAW,EAAE3S,cAAc,CAAC,CAAC;IACvF,CAAC,MACI;MACD4I,MAAM,CAACyK,QAAQ,GAAG,QAAQ;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA,IAAIsG,eAAe,GAAG,EAAE;IACxB,IAAItW,OAAO,GAAG,IAAI,CAACmT,UAAU,CAACnD,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAI/P,OAAO,GAAG,IAAI,CAACkT,UAAU,CAACnD,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIhQ,OAAO,EAAE;MACTsW,eAAe,IAAI,cAActW,OAAO,MAAM;IAClD;IACA,IAAIC,OAAO,EAAE;MACTqW,eAAe,IAAI,cAAcrW,OAAO,KAAK;IACjD;IACAsF,MAAM,CAAC0Q,SAAS,GAAGK,eAAe,CAACC,IAAI,CAAC,CAAC;IACzC;IACA;IACA;IACA;IACA;IACA,IAAI5X,MAAM,CAAC0N,SAAS,EAAE;MAClB,IAAI6J,gBAAgB,EAAE;QAClB3Q,MAAM,CAAC8G,SAAS,GAAG7U,mBAAmB,CAACmH,MAAM,CAAC0N,SAAS,CAAC;MAC5D,CAAC,MACI,IAAI8J,qBAAqB,EAAE;QAC5B5Q,MAAM,CAAC8G,SAAS,GAAG,EAAE;MACzB;IACJ;IACA,IAAI1N,MAAM,CAACyN,QAAQ,EAAE;MACjB,IAAI8J,gBAAgB,EAAE;QAClB3Q,MAAM,CAAC6G,QAAQ,GAAG5U,mBAAmB,CAACmH,MAAM,CAACyN,QAAQ,CAAC;MAC1D,CAAC,MACI,IAAI+J,qBAAqB,EAAE;QAC5B5Q,MAAM,CAAC6G,QAAQ,GAAG,EAAE;MACxB;IACJ;IACAuE,YAAY,CAAC,IAAI,CAAC9I,KAAK,CAAChO,KAAK,EAAE0L,MAAM,CAAC;EAC1C;EACA;EACA6Q,iBAAiBA,CAACpG,QAAQ,EAAEV,WAAW,EAAE3S,cAAc,EAAE;IACrD;IACA;IACA,IAAI4I,MAAM,GAAG;MAAErM,GAAG,EAAE,EAAE;MAAEsE,MAAM,EAAE;IAAG,CAAC;IACpC,IAAIgS,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACR,YAAY,EAAEkB,QAAQ,CAAC;IAClF,IAAI,IAAI,CAACH,SAAS,EAAE;MAChBL,YAAY,GAAG,IAAI,CAAC0E,oBAAoB,CAAC1E,YAAY,EAAE,IAAI,CAACV,YAAY,EAAEnS,cAAc,CAAC;IAC7F;IACA;IACA;IACA,IAAIqT,QAAQ,CAAC1P,QAAQ,KAAK,QAAQ,EAAE;MAChC;MACA;MACA,MAAMkW,cAAc,GAAG,IAAI,CAACpd,SAAS,CAACK,eAAe,CAACgd,YAAY;MAClElR,MAAM,CAAC/H,MAAM,GAAG,GAAGgZ,cAAc,IAAIhH,YAAY,CAACqD,CAAC,GAAG,IAAI,CAAC/D,YAAY,CAAC7T,MAAM,CAAC,IAAI;IACvF,CAAC,MACI;MACDsK,MAAM,CAACrM,GAAG,GAAG1B,mBAAmB,CAACgY,YAAY,CAACqD,CAAC,CAAC;IACpD;IACA,OAAOtN,MAAM;EACjB;EACA;EACA8Q,iBAAiBA,CAACrG,QAAQ,EAAEV,WAAW,EAAE3S,cAAc,EAAE;IACrD;IACA;IACA,IAAI4I,MAAM,GAAG;MAAEpM,IAAI,EAAE,EAAE;MAAEwE,KAAK,EAAE;IAAG,CAAC;IACpC,IAAI6R,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACR,YAAY,EAAEkB,QAAQ,CAAC;IAClF,IAAI,IAAI,CAACH,SAAS,EAAE;MAChBL,YAAY,GAAG,IAAI,CAAC0E,oBAAoB,CAAC1E,YAAY,EAAE,IAAI,CAACV,YAAY,EAAEnS,cAAc,CAAC;IAC7F;IACA;IACA;IACA;IACA;IACA,IAAI+Z,uBAAuB;IAC3B,IAAI,IAAI,CAAC/D,MAAM,CAAC,CAAC,EAAE;MACf+D,uBAAuB,GAAG1G,QAAQ,CAAC3P,QAAQ,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IAC5E,CAAC,MACI;MACDqW,uBAAuB,GAAG1G,QAAQ,CAAC3P,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IAC5E;IACA;IACA;IACA,IAAIqW,uBAAuB,KAAK,OAAO,EAAE;MACrC,MAAMC,aAAa,GAAG,IAAI,CAACvd,SAAS,CAACK,eAAe,CAACmd,WAAW;MAChErR,MAAM,CAAC5H,KAAK,GAAG,GAAGgZ,aAAa,IAAInH,YAAY,CAACiD,CAAC,GAAG,IAAI,CAAC3D,YAAY,CAAC3T,KAAK,CAAC,IAAI;IACpF,CAAC,MACI;MACDoK,MAAM,CAACpM,IAAI,GAAG3B,mBAAmB,CAACgY,YAAY,CAACiD,CAAC,CAAC;IACrD;IACA,OAAOlN,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACIyP,oBAAoBA,CAAA,EAAG;IACnB;IACA,MAAM6B,YAAY,GAAG,IAAI,CAAChI,cAAc,CAAC,CAAC;IAC1C,MAAMiI,aAAa,GAAG,IAAI,CAACjP,KAAK,CAACrJ,qBAAqB,CAAC,CAAC;IACxD;IACA;IACA;IACA,MAAMuY,qBAAqB,GAAG,IAAI,CAAC9F,YAAY,CAAC+F,GAAG,CAAC5a,UAAU,IAAI;MAC9D,OAAOA,UAAU,CAACE,aAAa,CAAC,CAAC,CAACC,aAAa,CAACiC,qBAAqB,CAAC,CAAC;IAC3E,CAAC,CAAC;IACF,OAAO;MACHyY,eAAe,EAAEpZ,2BAA2B,CAACgZ,YAAY,EAAEE,qBAAqB,CAAC;MACjFG,mBAAmB,EAAEha,4BAA4B,CAAC2Z,YAAY,EAAEE,qBAAqB,CAAC;MACtFI,gBAAgB,EAAEtZ,2BAA2B,CAACiZ,aAAa,EAAEC,qBAAqB,CAAC;MACnFK,oBAAoB,EAAEla,4BAA4B,CAAC4Z,aAAa,EAAEC,qBAAqB;IAC3F,CAAC;EACL;EACA;EACAtD,kBAAkBA,CAACnS,MAAM,EAAE,GAAG+V,SAAS,EAAE;IACrC,OAAOA,SAAS,CAACC,MAAM,CAAC,CAACC,YAAY,EAAEC,eAAe,KAAK;MACvD,OAAOD,YAAY,GAAG3a,IAAI,CAACyX,GAAG,CAACmD,eAAe,EAAE,CAAC,CAAC;IACtD,CAAC,EAAElW,MAAM,CAAC;EACd;EACA;EACAqN,wBAAwBA,CAAA,EAAG;IACvB;IACA;IACA;IACA;IACA;IACA,MAAMxT,KAAK,GAAG,IAAI,CAAC/B,SAAS,CAACK,eAAe,CAACmd,WAAW;IACxD,MAAM3b,MAAM,GAAG,IAAI,CAAC7B,SAAS,CAACK,eAAe,CAACgd,YAAY;IAC1D,MAAM9Z,cAAc,GAAG,IAAI,CAAC5D,cAAc,CAACY,yBAAyB,CAAC,CAAC;IACtE,OAAO;MACHT,GAAG,EAAEyD,cAAc,CAACzD,GAAG,GAAG,IAAI,CAACmY,eAAe;MAC9ClY,IAAI,EAAEwD,cAAc,CAACxD,IAAI,GAAG,IAAI,CAACkY,eAAe;MAChD1T,KAAK,EAAEhB,cAAc,CAACxD,IAAI,GAAGgC,KAAK,GAAG,IAAI,CAACkW,eAAe;MACzD7T,MAAM,EAAEb,cAAc,CAACzD,GAAG,GAAG+B,MAAM,GAAG,IAAI,CAACoW,eAAe;MAC1DlW,KAAK,EAAEA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACkW,eAAe;MACvCpW,MAAM,EAAEA,MAAM,GAAG,CAAC,GAAG,IAAI,CAACoW;IAC9B,CAAC;EACL;EACA;EACAsB,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACjX,WAAW,CAACsQ,YAAY,CAAC,CAAC,KAAK,KAAK;EACpD;EACA;EACAgK,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,IAAI,CAACxE,sBAAsB,IAAI,IAAI,CAAC3B,SAAS;EACzD;EACA;EACAsD,UAAUA,CAACnD,QAAQ,EAAEyH,IAAI,EAAE;IACvB,IAAIA,IAAI,KAAK,GAAG,EAAE;MACd;MACA;MACA,OAAOzH,QAAQ,CAAChQ,OAAO,IAAI,IAAI,GAAG,IAAI,CAACmS,QAAQ,GAAGnC,QAAQ,CAAChQ,OAAO;IACtE;IACA,OAAOgQ,QAAQ,CAAC/P,OAAO,IAAI,IAAI,GAAG,IAAI,CAACoS,QAAQ,GAAGrC,QAAQ,CAAC/P,OAAO;EACtE;EACA;EACA6N,kBAAkBA,CAAA,EAAG;IACjB,IAAI,OAAO/R,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAAC,IAAI,CAAC0R,mBAAmB,CAACnM,MAAM,EAAE;QAClC,MAAMjG,KAAK,CAAC,uEAAuE,CAAC;MACxF;MACA;MACA;MACA,IAAI,CAACoS,mBAAmB,CAAC1J,OAAO,CAAC2T,IAAI,IAAI;QACrC5W,0BAA0B,CAAC,SAAS,EAAE4W,IAAI,CAACvX,OAAO,CAAC;QACnDQ,wBAAwB,CAAC,SAAS,EAAE+W,IAAI,CAACtX,OAAO,CAAC;QACjDU,0BAA0B,CAAC,UAAU,EAAE4W,IAAI,CAACrX,QAAQ,CAAC;QACrDM,wBAAwB,CAAC,UAAU,EAAE+W,IAAI,CAACpX,QAAQ,CAAC;MACvD,CAAC,CAAC;IACN;EACJ;EACA;EACAwU,gBAAgBA,CAAC9H,UAAU,EAAE;IACzB,IAAI,IAAI,CAACnF,KAAK,EAAE;MACZpQ,WAAW,CAACuV,UAAU,CAAC,CAACjJ,OAAO,CAAC4T,QAAQ,IAAI;QACxC,IAAIA,QAAQ,KAAK,EAAE,IAAI,IAAI,CAACC,oBAAoB,CAACxW,OAAO,CAACuW,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;UACvE,IAAI,CAACC,oBAAoB,CAAC1W,IAAI,CAACyW,QAAQ,CAAC;UACxC,IAAI,CAAC9P,KAAK,CAAC/N,SAAS,CAACC,GAAG,CAAC4d,QAAQ,CAAC;QACtC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACApJ,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC1G,KAAK,EAAE;MACZ,IAAI,CAAC+P,oBAAoB,CAAC7T,OAAO,CAAC4T,QAAQ,IAAI;QAC1C,IAAI,CAAC9P,KAAK,CAAC/N,SAAS,CAACW,MAAM,CAACkd,QAAQ,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAClC;EACJ;EACA;EACA/I,cAAcA,CAAA,EAAG;IACb,MAAM/O,MAAM,GAAG,IAAI,CAACkS,OAAO;IAC3B,IAAIlS,MAAM,YAAYpK,UAAU,EAAE;MAC9B,OAAOoK,MAAM,CAACvD,aAAa,CAACiC,qBAAqB,CAAC,CAAC;IACvD;IACA;IACA,IAAIsB,MAAM,YAAY+X,OAAO,EAAE;MAC3B,OAAO/X,MAAM,CAACtB,qBAAqB,CAAC,CAAC;IACzC;IACA,MAAMrD,KAAK,GAAG2E,MAAM,CAAC3E,KAAK,IAAI,CAAC;IAC/B,MAAMF,MAAM,GAAG6E,MAAM,CAAC7E,MAAM,IAAI,CAAC;IACjC;IACA,OAAO;MACH/B,GAAG,EAAE4G,MAAM,CAAC+S,CAAC;MACbrV,MAAM,EAAEsC,MAAM,CAAC+S,CAAC,GAAG5X,MAAM;MACzB9B,IAAI,EAAE2G,MAAM,CAAC2S,CAAC;MACd9U,KAAK,EAAEmC,MAAM,CAAC2S,CAAC,GAAGtX,KAAK;MACvBF,MAAM;MACNE;IACJ,CAAC;EACL;AACJ;AACA;AACA,SAASwV,YAAYA,CAACmH,WAAW,EAAEC,MAAM,EAAE;EACvC,KAAK,IAAIpY,GAAG,IAAIoY,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACC,cAAc,CAACrY,GAAG,CAAC,EAAE;MAC5BmY,WAAW,CAACnY,GAAG,CAAC,GAAGoY,MAAM,CAACpY,GAAG,CAAC;IAClC;EACJ;EACA,OAAOmY,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA,SAAS/D,aAAaA,CAACkE,KAAK,EAAE;EAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,EAAE;IAC5C,MAAM,CAACpX,KAAK,EAAEqX,KAAK,CAAC,GAAGD,KAAK,CAACE,KAAK,CAAC7K,cAAc,CAAC;IAClD,OAAO,CAAC4K,KAAK,IAAIA,KAAK,KAAK,IAAI,GAAGE,UAAU,CAACvX,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA,OAAOoX,KAAK,IAAI,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/E,4BAA4BA,CAACmF,UAAU,EAAE;EAC9C,OAAO;IACHnf,GAAG,EAAE0D,IAAI,CAAC0b,KAAK,CAACD,UAAU,CAACnf,GAAG,CAAC;IAC/ByE,KAAK,EAAEf,IAAI,CAAC0b,KAAK,CAACD,UAAU,CAAC1a,KAAK,CAAC;IACnCH,MAAM,EAAEZ,IAAI,CAAC0b,KAAK,CAACD,UAAU,CAAC7a,MAAM,CAAC;IACrCrE,IAAI,EAAEyD,IAAI,CAAC0b,KAAK,CAACD,UAAU,CAAClf,IAAI,CAAC;IACjCgC,KAAK,EAAEyB,IAAI,CAAC0b,KAAK,CAACD,UAAU,CAACld,KAAK,CAAC;IACnCF,MAAM,EAAE2B,IAAI,CAAC0b,KAAK,CAACD,UAAU,CAACpd,MAAM;EACxC,CAAC;AACL;AACA;AACA,SAASia,uBAAuBA,CAACqD,CAAC,EAAExgB,CAAC,EAAE;EACnC,IAAIwgB,CAAC,KAAKxgB,CAAC,EAAE;IACT,OAAO,IAAI;EACf;EACA,OAAQwgB,CAAC,CAACtB,eAAe,KAAKlf,CAAC,CAACkf,eAAe,IAC3CsB,CAAC,CAACrB,mBAAmB,KAAKnf,CAAC,CAACmf,mBAAmB,IAC/CqB,CAAC,CAACpB,gBAAgB,KAAKpf,CAAC,CAACof,gBAAgB,IACzCoB,CAAC,CAACnB,oBAAoB,KAAKrf,CAAC,CAACqf,oBAAoB;AACzD;AACA,MAAMoB,iCAAiC,GAAG,CACtC;EAAErY,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAC3E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC3E;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACvE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC1E;AACD,MAAMmY,oCAAoC,GAAG,CACzC;EAAEtY,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC5E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC/E;;AAED;AACA,MAAMoY,YAAY,GAAG,4BAA4B;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EAAA7f,YAAA;IACzB;IAAAG,eAAA;IAAAA,eAAA,uBAEe,QAAQ;IAAAA,eAAA,qBACV,EAAE;IAAAA,eAAA,wBACC,EAAE;IAAAA,eAAA,sBACJ,EAAE;IAAAA,eAAA,qBACH,EAAE;IAAAA,eAAA,mBACJ,EAAE;IAAAA,eAAA,iBACJ,EAAE;IAAAA,eAAA,kBACD,EAAE;IAAAA,eAAA,sBACE,KAAK;EAAA;EACnBI,MAAMA,CAACyC,UAAU,EAAE;IACf,MAAM6C,MAAM,GAAG7C,UAAU,CAACqP,SAAS,CAAC,CAAC;IACrC,IAAI,CAACzP,WAAW,GAAGI,UAAU;IAC7B,IAAI,IAAI,CAAC8c,MAAM,IAAI,CAACja,MAAM,CAACxD,KAAK,EAAE;MAC9BW,UAAU,CAACyP,UAAU,CAAC;QAAEpQ,KAAK,EAAE,IAAI,CAACyd;MAAO,CAAC,CAAC;IACjD;IACA,IAAI,IAAI,CAACC,OAAO,IAAI,CAACla,MAAM,CAAC1D,MAAM,EAAE;MAChCa,UAAU,CAACyP,UAAU,CAAC;QAAEtQ,MAAM,EAAE,IAAI,CAAC4d;MAAQ,CAAC,CAAC;IACnD;IACA/c,UAAU,CAACgN,WAAW,CAAChP,SAAS,CAACC,GAAG,CAAC2e,YAAY,CAAC;IAClD,IAAI,CAAC1K,WAAW,GAAG,KAAK;EAC5B;EACA;AACJ;AACA;AACA;EACI9U,GAAGA,CAAC2H,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAACiY,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,UAAU,GAAGlY,KAAK;IACvB,IAAI,CAACmY,WAAW,GAAG,YAAY;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI7f,IAAIA,CAAC0H,KAAK,GAAG,EAAE,EAAE;IACb,IAAI,CAACoY,QAAQ,GAAGpY,KAAK;IACrB,IAAI,CAACqY,UAAU,GAAG,MAAM;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI1b,MAAMA,CAACqD,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,CAACkY,UAAU,GAAG,EAAE;IACpB,IAAI,CAACD,aAAa,GAAGjY,KAAK;IAC1B,IAAI,CAACmY,WAAW,GAAG,UAAU;IAC7B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIrb,KAAKA,CAACkD,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAACoY,QAAQ,GAAGpY,KAAK;IACrB,IAAI,CAACqY,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI/E,KAAKA,CAACtT,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAACoY,QAAQ,GAAGpY,KAAK;IACrB,IAAI,CAACqY,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,GAAGA,CAACtY,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAACoY,QAAQ,GAAGpY,KAAK;IACrB,IAAI,CAACqY,UAAU,GAAG,KAAK;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI/d,KAAKA,CAAC0F,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,IAAI,CAACnF,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC6P,UAAU,CAAC;QAAEpQ,KAAK,EAAE0F;MAAM,CAAC,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAAC+X,MAAM,GAAG/X,KAAK;IACvB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI5F,MAAMA,CAAC4F,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,IAAI,CAACnF,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC6P,UAAU,CAAC;QAAEtQ,MAAM,EAAE4F;MAAM,CAAC,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAACgY,OAAO,GAAGhY,KAAK;IACxB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuY,kBAAkBA,CAAClH,MAAM,GAAG,EAAE,EAAE;IAC5B,IAAI,CAAC/Y,IAAI,CAAC+Y,MAAM,CAAC;IACjB,IAAI,CAACgH,UAAU,GAAG,QAAQ;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,gBAAgBA,CAACnH,MAAM,GAAG,EAAE,EAAE;IAC1B,IAAI,CAAChZ,GAAG,CAACgZ,MAAM,CAAC;IAChB,IAAI,CAAC8G,WAAW,GAAG,QAAQ;IAC3B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI5N,KAAKA,CAAA,EAAG;IACJ;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC1P,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACC,WAAW,CAAC,CAAC,EAAE;MACtD;IACJ;IACA,MAAM4J,MAAM,GAAG,IAAI,CAAC7J,WAAW,CAACW,cAAc,CAACxC,KAAK;IACpD,MAAMyf,YAAY,GAAG,IAAI,CAAC5d,WAAW,CAACoN,WAAW,CAACjP,KAAK;IACvD,MAAM8E,MAAM,GAAG,IAAI,CAACjD,WAAW,CAACyP,SAAS,CAAC,CAAC;IAC3C,MAAM;MAAEhQ,KAAK;MAAEF,MAAM;MAAEmR,QAAQ;MAAEC;IAAU,CAAC,GAAG1N,MAAM;IACrD,MAAM4a,yBAAyB,GAAG,CAACpe,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,MACnE,CAACiR,QAAQ,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,CAAC;IAC9D,MAAMoN,uBAAuB,GAAG,CAACve,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,MACnE,CAACoR,SAAS,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,CAAC;IACjE,MAAMoN,SAAS,GAAG,IAAI,CAACP,UAAU;IACjC,MAAMQ,OAAO,GAAG,IAAI,CAACT,QAAQ;IAC7B,MAAMzD,KAAK,GAAG,IAAI,CAAC9Z,WAAW,CAACyP,SAAS,CAAC,CAAC,CAACS,SAAS,KAAK,KAAK;IAC9D,IAAI+N,UAAU,GAAG,EAAE;IACnB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAI/I,cAAc,GAAG,EAAE;IACvB,IAAI0I,yBAAyB,EAAE;MAC3B1I,cAAc,GAAG,YAAY;IACjC,CAAC,MACI,IAAI4I,SAAS,KAAK,QAAQ,EAAE;MAC7B5I,cAAc,GAAG,QAAQ;MACzB,IAAI2E,KAAK,EAAE;QACPoE,WAAW,GAAGF,OAAO;MACzB,CAAC,MACI;QACDC,UAAU,GAAGD,OAAO;MACxB;IACJ,CAAC,MACI,IAAIlE,KAAK,EAAE;MACZ,IAAIiE,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,EAAE;QAC7C5I,cAAc,GAAG,UAAU;QAC3B8I,UAAU,GAAGD,OAAO;MACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,OAAO,EAAE;QACrD5I,cAAc,GAAG,YAAY;QAC7B+I,WAAW,GAAGF,OAAO;MACzB;IACJ,CAAC,MACI,IAAID,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;MACpD5I,cAAc,GAAG,YAAY;MAC7B8I,UAAU,GAAGD,OAAO;IACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,KAAK,EAAE;MACnD5I,cAAc,GAAG,UAAU;MAC3B+I,WAAW,GAAGF,OAAO;IACzB;IACAnU,MAAM,CAACyK,QAAQ,GAAG,IAAI,CAAC6J,YAAY;IACnCtU,MAAM,CAACoU,UAAU,GAAGJ,yBAAyB,GAAG,GAAG,GAAGI,UAAU;IAChEpU,MAAM,CAACuU,SAAS,GAAGN,uBAAuB,GAAG,GAAG,GAAG,IAAI,CAACT,UAAU;IAClExT,MAAM,CAACwU,YAAY,GAAG,IAAI,CAACjB,aAAa;IACxCvT,MAAM,CAACqU,WAAW,GAAGL,yBAAyB,GAAG,GAAG,GAAGK,WAAW;IAClEN,YAAY,CAACzI,cAAc,GAAGA,cAAc;IAC5CyI,YAAY,CAAC1I,UAAU,GAAG4I,uBAAuB,GAAG,YAAY,GAAG,IAAI,CAACR,WAAW;EACvF;EACA;AACJ;AACA;AACA;EACIzR,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACyG,WAAW,IAAI,CAAC,IAAI,CAACtS,WAAW,EAAE;MACvC;IACJ;IACA,MAAM6J,MAAM,GAAG,IAAI,CAAC7J,WAAW,CAACW,cAAc,CAACxC,KAAK;IACpD,MAAMuK,MAAM,GAAG,IAAI,CAAC1I,WAAW,CAACoN,WAAW;IAC3C,MAAMwQ,YAAY,GAAGlV,MAAM,CAACvK,KAAK;IACjCuK,MAAM,CAACtK,SAAS,CAACW,MAAM,CAACie,YAAY,CAAC;IACrCY,YAAY,CAACzI,cAAc,GACvByI,YAAY,CAAC1I,UAAU,GACnBrL,MAAM,CAACuU,SAAS,GACZvU,MAAM,CAACwU,YAAY,GACfxU,MAAM,CAACoU,UAAU,GACbpU,MAAM,CAACqU,WAAW,GACdrU,MAAM,CAACyK,QAAQ,GACX,EAAE;IAC9B,IAAI,CAACtU,WAAW,GAAG,IAAI;IACvB,IAAI,CAACsS,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA,MAAMgM,sBAAsB,CAAC;EAKzBlhB,WAAWA,CAAA,EAAG;IAAAG,eAAA,yBAJGjE,MAAM,CAAC2C,aAAa,CAAC;IAAAsB,eAAA,oBAC1BjE,MAAM,CAACwB,QAAQ,CAAC;IAAAyC,eAAA,oBAChBjE,MAAM,CAAC2B,QAAQ,CAAC;IAAAsC,eAAA,4BACRjE,MAAM,CAAC4Q,gBAAgB,CAAC;EAC5B;EAChB;AACJ;AACA;EACIqU,MAAMA,CAAA,EAAG;IACL,OAAO,IAAItB,sBAAsB,CAAC,CAAC;EACvC;EACA;AACJ;AACA;AACA;EACIuB,mBAAmBA,CAACpa,MAAM,EAAE;IACxB,OAAO,IAAIyN,iCAAiC,CAACzN,MAAM,EAAE,IAAI,CAAC/G,cAAc,EAAE,IAAI,CAACK,SAAS,EAAE,IAAI,CAACqK,SAAS,EAAE,IAAI,CAACkK,iBAAiB,CAAC;EACrI;AAGJ;AAACwM,uBAAA,GArBKH,sBAAsB;AAAA/gB,eAAA,CAAtB+gB,sBAAsB,wBAAAI,gCAAAtb,iBAAA;EAAA,YAAAA,iBAAA,IAmB2Ekb,uBAAsB;AAAA;AAAA/gB,eAAA,CAnBvH+gB,sBAAsB,+BA1mEqDjlB,EAAE,CAAAgK,kBAAA;EAAAC,KAAA,EA8nEwBgb,uBAAsB;EAAA/a,OAAA,EAAtB+a,uBAAsB,CAAA9a,IAAA;EAAAC,UAAA,EAAc;AAAM;AAErJ;EAAA,QAAApD,SAAA,oBAAAA,SAAA,KAhoEiFhH,EAAE,CAAAqK,iBAAA,CAgoEQ4a,sBAAsB,EAAc,CAAC;IACpH3a,IAAI,EAAEnK,UAAU;IAChBoK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkb,OAAO,CAAC;EAgBVvhB,WAAWA,CAAA,EAAG;IAAAG,eAAA,2BAfKjE,MAAM,CAAC0J,qBAAqB,CAAC;IAAAzF,eAAA,4BAC5BjE,MAAM,CAAC4Q,gBAAgB,CAAC;IAAA3M,eAAA,2BACzBjE,MAAM,CAACglB,sBAAsB,CAAC;IAAA/gB,eAAA,8BAC3BjE,MAAM,CAACyM,yBAAyB,CAAC;IAAAxI,eAAA,oBAC3CjE,MAAM,CAACW,QAAQ,CAAC;IAAAsD,eAAA,kBAClBjE,MAAM,CAACC,MAAM,CAAC;IAAAgE,eAAA,oBACZjE,MAAM,CAACwB,QAAQ,CAAC;IAAAyC,eAAA,0BACVjE,MAAM,CAACsD,cAAc,CAAC;IAAAW,eAAA,oBAC5BjE,MAAM,CAACyB,QAAQ,CAAC;IAAAwC,eAAA,kCACFjE,MAAM,CAAC6N,6BAA6B,CAAC;IAAA5J,eAAA,gCACvCjE,MAAM,CAACY,qBAAqB,EAAE;MAAE0kB,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAArhB,eAAA,uBAC1DjE,MAAM,CAACuD,YAAY,CAAC;IAAAU,eAAA,oBACvBjE,MAAM,CAACG,gBAAgB,CAAC,CAACuM,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IAAAzI,eAAA;IAAAA,eAAA,uBAEhDjE,MAAM,CAACgC,sBAAsB,CAAC;EAC7B;EAChB;AACJ;AACA;AACA;AACA;EACIujB,MAAMA,CAAC5b,MAAM,EAAE;IACX;IACA;IACA,IAAI,CAAC+H,YAAY,CAACC,IAAI,CAAChC,sBAAsB,CAAC;IAC9C,MAAMF,IAAI,GAAG,IAAI,CAAC+V,kBAAkB,CAAC,CAAC;IACtC,MAAMC,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAACjW,IAAI,CAAC;IAC1C,MAAMkW,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACH,IAAI,CAAC;IACnD,MAAMI,aAAa,GAAG,IAAItb,aAAa,CAACZ,MAAM,CAAC;IAC/Ckc,aAAa,CAACjP,SAAS,GAAGiP,aAAa,CAACjP,SAAS,IAAI,IAAI,CAACkP,eAAe,CAACja,KAAK;IAC/E,OAAO,IAAI6G,UAAU,CAACiT,YAAY,EAAElW,IAAI,EAAEgW,IAAI,EAAEI,aAAa,EAAE,IAAI,CAACrf,OAAO,EAAE,IAAI,CAACsM,mBAAmB,EAAE,IAAI,CAAC1O,SAAS,EAAE,IAAI,CAAC2O,SAAS,EAAE,IAAI,CAACC,uBAAuB,EAAE,IAAI,CAAC+S,qBAAqB,KAAK,gBAAgB,EAAE,IAAI,CAAC7S,SAAS,CAAC8S,GAAG,CAACnlB,mBAAmB,CAAC,EAAE,IAAI,CAACuM,SAAS,CAAC;EAClR;EACA;AACJ;AACA;AACA;AACA;EACI4N,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACiL,gBAAgB;EAChC;EACA;AACJ;AACA;AACA;EACIP,kBAAkBA,CAACjW,IAAI,EAAE;IACrB,MAAMgW,IAAI,GAAG,IAAI,CAACrhB,SAAS,CAACmN,aAAa,CAAC,KAAK,CAAC;IAChDkU,IAAI,CAACS,EAAE,GAAG,IAAI,CAACC,YAAY,CAACC,KAAK,CAAC,cAAc,CAAC;IACjDX,IAAI,CAAC3gB,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACtC0K,IAAI,CAACgC,WAAW,CAACgU,IAAI,CAAC;IACtB,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACID,kBAAkBA,CAAA,EAAG;IACjB,MAAM/V,IAAI,GAAG,IAAI,CAACrL,SAAS,CAACmN,aAAa,CAAC,KAAK,CAAC;IAChD,IAAI,CAACoH,iBAAiB,CAAC5H,mBAAmB,CAAC,CAAC,CAACU,WAAW,CAAChC,IAAI,CAAC;IAC9D,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACImW,mBAAmBA,CAACH,IAAI,EAAE;IACtB;IACA;IACA,IAAI,CAAC,IAAI,CAACY,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI,CAACnT,SAAS,CAAC8S,GAAG,CAACllB,cAAc,CAAC;IACrD;IACA,OAAO,IAAIkC,eAAe,CAACyiB,IAAI,EAAE,IAAI,EAAE,IAAI,CAACY,OAAO,EAAE,IAAI,CAACnT,SAAS,EAAE,IAAI,CAAC9O,SAAS,CAAC;EACxF;AAGJ;AAACkiB,QAAA,GA7EKjB,OAAO;AAAAphB,eAAA,CAAPohB,OAAO,wBAAAkB,iBAAAzc,iBAAA;EAAA,YAAAA,iBAAA,IA2E0Fub,QAAO;AAAA;AAAAphB,eAAA,CA3ExGohB,OAAO,+BA7oEoEtlB,EAAE,CAAAgK,kBAAA;EAAAC,KAAA,EAytEwBqb,QAAO;EAAApb,OAAA,EAAPob,QAAO,CAAAnb,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEtI;EAAA,QAAApD,SAAA,oBAAAA,SAAA,KA3tEiFhH,EAAE,CAAAqK,iBAAA,CA2tEQib,OAAO,EAAc,CAAC;IACrGhb,IAAI,EAAEnK,UAAU;IAChBoK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAMqc,mBAAmB,GAAG,CACxB;EACIrb,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,CACJ;AACD;AACA,MAAMmb,qCAAqC,GAAG,IAAI1lB,cAAc,CAAC,uCAAuC,EAAE;EACtGoJ,UAAU,EAAE,MAAM;EAClBF,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMc,OAAO,GAAG/K,MAAM,CAACqlB,OAAO,CAAC;IAC/B,OAAO,MAAMta,OAAO,CAAC2b,gBAAgB,CAACC,UAAU,CAAC,CAAC;EACtD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EAEnB9iB,WAAWA,CAAA,EAAG;IAAAG,eAAA,qBADDjE,MAAM,CAACU,UAAU,CAAC;EACf;AAGpB;AAACmmB,iBAAA,GALKD,gBAAgB;AAAA3iB,eAAA,CAAhB2iB,gBAAgB,wBAAAE,0BAAAhd,iBAAA;EAAA,YAAAA,iBAAA,IAGiF8c,iBAAgB;AAAA;AAAA3iB,eAAA,CAHjH2iB,gBAAgB,8BAvwE2D7mB,EAAE,CAAAgnB,iBAAA;EAAA1c,IAAA,EA2wEQuc,iBAAgB;EAAA7W,SAAA;EAAAiX,QAAA;AAAA;AAE3G;EAAA,QAAAjgB,SAAA,oBAAAA,SAAA,KA7wEiFhH,EAAE,CAAAqK,iBAAA,CA6wEQwc,gBAAgB,EAAc,CAAC;IAC9Gvc,IAAI,EAAErJ,SAAS;IACfsJ,IAAI,EAAE,CAAC;MACCiT,QAAQ,EAAE,4DAA4D;MACtEyJ,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EAwBtB;EACA,IAAIjc,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACmS,QAAQ;EACxB;EACA,IAAInS,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACmS,QAAQ,GAAGnS,OAAO;IACvB,IAAI,IAAI,CAACkc,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;IAChD;EACJ;EACA;EACA,IAAIjc,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoS,QAAQ;EACxB;EACA,IAAIpS,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACoS,QAAQ,GAAGpS,OAAO;IACvB,IAAI,IAAI,CAACic,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;IAChD;EACJ;EACA;;EAgCA;EACA,IAAIpS,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACsS,oBAAoB;EACpC;EACA,IAAItS,mBAAmBA,CAACjJ,KAAK,EAAE;IAC3B,IAAI,CAACub,oBAAoB,GAAGvb,KAAK;EACrC;EACA;;EAYA;EACA/H,WAAWA,CAAA,EAAG;IAAAG,eAAA,mBA/FHjE,MAAM,CAACqlB,OAAO,CAAC;IAAAphB,eAAA,eACnBjE,MAAM,CAACsD,cAAc,EAAE;MAAEgiB,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAArhB,eAAA;IAAAA,eAAA;IAAAA,eAAA,gCAGzB/B,YAAY,CAACiR,KAAK;IAAAlP,eAAA,8BACpB/B,YAAY,CAACiR,KAAK;IAAAlP,eAAA,8BAClB/B,YAAY,CAACiR,KAAK;IAAAlP,eAAA,gCAChB/B,YAAY,CAACiR,KAAK;IAAAlP,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,iCAIjBjE,MAAM,CAACymB,qCAAqC,CAAC;IAAAxiB,eAAA,+BAC/C,KAAK;IAAAA,eAAA,kBAClBjE,MAAM,CAACC,MAAM,CAAC;IACxB;IAAAgE,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAAAA,eAAA;IA2BA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,yBACiB,CAAC;IAClB;IAAAA,eAAA;IAEA;IAAAA,eAAA,eACO,KAAK;IACZ;IAAAA,eAAA,uBACe,KAAK;IACpB;IAAAA,eAAA;IAEA;IAAAA,eAAA,sBACc,KAAK;IACnB;IAAAA,eAAA,uBACe,KAAK;IACpB;IAAAA,eAAA,6BACqB,KAAK;IAC1B;IAAAA,eAAA,wBACgB,KAAK;IACrB;IAAAA,eAAA,eACO,KAAK;IAAAA,eAAA,wBASI,IAAIhD,YAAY,CAAC,CAAC;IAClC;IAAAgD,eAAA,yBACiB,IAAIhD,YAAY,CAAC,CAAC;IACnC;IAAAgD,eAAA,iBACS,IAAIhD,YAAY,CAAC,CAAC;IAC3B;IAAAgD,eAAA,iBACS,IAAIhD,YAAY,CAAC,CAAC;IAC3B;IAAAgD,eAAA,yBACiB,IAAIhD,YAAY,CAAC,CAAC;IACnC;IAAAgD,eAAA,8BACsB,IAAIhD,YAAY,CAAC,CAAC;IAGpC,MAAMomB,WAAW,GAAGrnB,MAAM,CAACkB,WAAW,CAAC;IACvC,MAAMomB,gBAAgB,GAAGtnB,MAAM,CAACmB,gBAAgB,CAAC;IACjD,IAAI,CAAComB,eAAe,GAAG,IAAIrkB,cAAc,CAACmkB,WAAW,EAAEC,gBAAgB,CAAC;IACxE,IAAI,CAAClU,cAAc,GAAG,IAAI,CAACoU,sBAAsB,CAAC,CAAC;EACvD;EACA;EACA,IAAI1gB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,WAAW;EAC3B;EACA;EACA,IAAIiQ,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC8Q,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC5b,KAAK,GAAG,KAAK;EAC9C;EACAG,WAAWA,CAAA,EAAG;IAAA,IAAA0b,iBAAA;IACV,IAAI,CAACC,mBAAmB,CAAC3f,WAAW,CAAC,CAAC;IACtC,IAAI,CAAC4f,mBAAmB,CAAC5f,WAAW,CAAC,CAAC;IACtC,IAAI,CAAC6f,qBAAqB,CAAC7f,WAAW,CAAC,CAAC;IACxC,IAAI,CAAC8f,qBAAqB,CAAC9f,WAAW,CAAC,CAAC;IACxC,CAAA0f,iBAAA,OAAI,CAAChhB,WAAW,cAAAghB,iBAAA,eAAhBA,iBAAA,CAAkBnV,OAAO,CAAC,CAAC;EAC/B;EACAwV,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,IAAI,CAACd,SAAS,EAAE;MAAA,IAAAe,kBAAA;MAChB,IAAI,CAACd,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;MAC5C,CAAAe,kBAAA,OAAI,CAACvhB,WAAW,cAAAuhB,kBAAA,eAAhBA,kBAAA,CAAkB1R,UAAU,CAAC;QACzBpQ,KAAK,EAAE,IAAI,CAACA,KAAK;QACjB+Q,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBjR,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBkR,SAAS,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC;MACF,IAAI6Q,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAACE,IAAI,EAAE;QAChC,IAAI,CAAChB,SAAS,CAAC9Q,KAAK,CAAC,CAAC;MAC1B;IACJ;IACA,IAAI4R,OAAO,CAAC,MAAM,CAAC,EAAE;MACjB,IAAI,CAACE,IAAI,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IAC3D;EACJ;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAC7P,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAClM,MAAM,EAAE;MAC3C,IAAI,CAACkM,SAAS,GAAGgO,mBAAmB;IACxC;IACA,MAAM1f,UAAU,GAAI,IAAI,CAACJ,WAAW,GAAG,IAAI,CAAC4hB,QAAQ,CAAC/C,MAAM,CAAC,IAAI,CAACgD,YAAY,CAAC,CAAC,CAAE;IACjF,IAAI,CAACZ,mBAAmB,GAAG7gB,UAAU,CAACkP,WAAW,CAAC,CAAC,CAACtO,SAAS,CAAC,MAAM,IAAI,CAACrD,MAAM,CAACmkB,IAAI,CAAC,CAAC,CAAC;IACvF,IAAI,CAACZ,mBAAmB,GAAG9gB,UAAU,CAACmP,WAAW,CAAC,CAAC,CAACvO,SAAS,CAAC,MAAM,IAAI,CAACb,MAAM,CAAC2hB,IAAI,CAAC,CAAC,CAAC;IACvF1hB,UAAU,CAACoP,aAAa,CAAC,CAAC,CAACxO,SAAS,CAAEiF,KAAK,IAAK;MAC5C,IAAI,CAAC8b,cAAc,CAACzb,IAAI,CAACL,KAAK,CAAC;MAC/B,IAAIA,KAAK,CAAC+b,OAAO,KAAKjlB,MAAM,IAAI,CAAC,IAAI,CAACklB,YAAY,IAAI,CAACjlB,cAAc,CAACiJ,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACic,cAAc,CAAC,CAAC;QACtB,IAAI,CAACR,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;IACF,IAAI,CAAC1hB,WAAW,CAACyH,oBAAoB,CAAC,CAAC,CAACzG,SAAS,CAAEiF,KAAK,IAAK;MACzD,MAAM7B,MAAM,GAAG,IAAI,CAAC+d,iBAAiB,CAAC,CAAC;MACvC,MAAM9a,MAAM,GAAGjM,eAAe,CAAC6K,KAAK,CAAC;MACrC,IAAI,CAAC7B,MAAM,IAAKA,MAAM,KAAKiD,MAAM,IAAI,CAACjD,MAAM,CAAClF,QAAQ,CAACmI,MAAM,CAAE,EAAE;QAC5D,IAAI,CAAC+a,mBAAmB,CAAC9b,IAAI,CAACL,KAAK,CAAC;MACxC;IACJ,CAAC,CAAC;EACN;EACA;EACA4b,YAAYA,CAAA,EAAG;IACX,MAAMhV,gBAAgB,GAAI,IAAI,CAAC2T,SAAS,GACpC,IAAI,CAAC3T,gBAAgB,IAAI,IAAI,CAACwV,uBAAuB,CAAC,CAAE;IAC5D,MAAMlD,aAAa,GAAG,IAAItb,aAAa,CAAC;MACpCqM,SAAS,EAAE,IAAI,CAAC6Q,IAAI,IAAI,KAAK;MAC7BlU,gBAAgB;MAChBH,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCsB,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BI,mBAAmB,EAAE,IAAI,CAACA;IAC9B,CAAC,CAAC;IACF,IAAI,IAAI,CAAC3O,KAAK,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,EAAE;MAChC0f,aAAa,CAAC1f,KAAK,GAAG,IAAI,CAACA,KAAK;IACpC;IACA,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,CAACA,MAAM,KAAK,CAAC,EAAE;MAClC4f,aAAa,CAAC5f,MAAM,GAAG,IAAI,CAACA,MAAM;IACtC;IACA,IAAI,IAAI,CAACiR,QAAQ,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,EAAE;MACtC2O,aAAa,CAAC3O,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1C;IACA,IAAI,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;MACxC0O,aAAa,CAAC1O,SAAS,GAAG,IAAI,CAACA,SAAS;IAC5C;IACA,IAAI,IAAI,CAACM,aAAa,EAAE;MACpBoO,aAAa,CAACpO,aAAa,GAAG,IAAI,CAACA,aAAa;IACpD;IACA,IAAI,IAAI,CAACvM,UAAU,EAAE;MACjB2a,aAAa,CAAC3a,UAAU,GAAG,IAAI,CAACA,UAAU;IAC9C;IACA,OAAO2a,aAAa;EACxB;EACA;EACAsB,uBAAuBA,CAAC5T,gBAAgB,EAAE;IACtC,MAAMiF,SAAS,GAAG,IAAI,CAACA,SAAS,CAACwJ,GAAG,CAACgH,eAAe,KAAK;MACrD7d,OAAO,EAAE6d,eAAe,CAAC7d,OAAO;MAChCC,OAAO,EAAE4d,eAAe,CAAC5d,OAAO;MAChCC,QAAQ,EAAE2d,eAAe,CAAC3d,QAAQ;MAClCC,QAAQ,EAAE0d,eAAe,CAAC1d,QAAQ;MAClCN,OAAO,EAAEge,eAAe,CAAChe,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDC,OAAO,EAAE+d,eAAe,CAAC/d,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDC,UAAU,EAAE8d,eAAe,CAAC9d,UAAU,IAAIN;IAC9C,CAAC,CAAC,CAAC;IACH,OAAO2I,gBAAgB,CAClBsF,SAAS,CAAC,IAAI,CAACoQ,UAAU,CAAC,CAAC,CAAC,CAC5B/M,aAAa,CAAC1D,SAAS,CAAC,CACxB8D,sBAAsB,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAC/CK,QAAQ,CAAC,IAAI,CAAC1Q,IAAI,CAAC,CACnBuQ,iBAAiB,CAAC,IAAI,CAACC,aAAa,CAAC,CACrCP,kBAAkB,CAAC,IAAI,CAAC+M,cAAc,CAAC,CACvCpM,kBAAkB,CAAC,IAAI,CAACqM,YAAY,CAAC,CACrC7L,qBAAqB,CAAC,IAAI,CAAC8L,uBAAuB,CAAC;EAC5D;EACA;EACAL,uBAAuBA,CAAA,EAAG;IACtB,MAAMzS,QAAQ,GAAG,IAAI,CAACgS,QAAQ,CAACtN,QAAQ,CAAC,CAAC,CAACkK,mBAAmB,CAAC,IAAI,CAAC+D,UAAU,CAAC,CAAC,CAAC;IAChF,IAAI,CAAC9B,uBAAuB,CAAC7Q,QAAQ,CAAC;IACtC,OAAOA,QAAQ;EACnB;EACA2S,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACne,MAAM,YAAY8b,gBAAgB,EAAE;MACzC,OAAO,IAAI,CAAC9b,MAAM,CAACue,UAAU;IACjC,CAAC,MACI;MACD,OAAO,IAAI,CAACve,MAAM;IACtB;EACJ;EACA+d,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC/d,MAAM,YAAY8b,gBAAgB,EAAE;MACzC,OAAO,IAAI,CAAC9b,MAAM,CAACue,UAAU,CAAC9hB,aAAa;IAC/C;IACA,IAAI,IAAI,CAACuD,MAAM,YAAYpK,UAAU,EAAE;MACnC,OAAO,IAAI,CAACoK,MAAM,CAACvD,aAAa;IACpC;IACA,IAAI,OAAOsb,OAAO,KAAK,WAAW,IAAI,IAAI,CAAC/X,MAAM,YAAY+X,OAAO,EAAE;MAClE,OAAO,IAAI,CAAC/X,MAAM;IACtB;IACA,OAAO,IAAI;EACf;EACA;EACAqd,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACzhB,WAAW,EAAE;MACnB,IAAI,CAAC2hB,cAAc,CAAC,CAAC;IACzB,CAAC,MACI;MACD;MACA,IAAI,CAAC3hB,WAAW,CAACyP,SAAS,CAAC,CAAC,CAACzB,WAAW,GAAG,IAAI,CAACA,WAAW;IAC/D;IACA,IAAI,CAAC,IAAI,CAAChO,WAAW,CAACC,WAAW,CAAC,CAAC,EAAE;MACjC,IAAI,CAACD,WAAW,CAACrC,MAAM,CAAC,IAAI,CAACkjB,eAAe,CAAC;IACjD;IACA,IAAI,IAAI,CAAC7S,WAAW,EAAE;MAClB,IAAI,CAACmT,qBAAqB,GAAG,IAAI,CAACnhB,WAAW,CAACqP,aAAa,CAAC,CAAC,CAACrO,SAAS,CAACiF,KAAK,IAAI;QAC7E,IAAI,CAACoJ,aAAa,CAACyS,IAAI,CAAC7b,KAAK,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACkb,qBAAqB,CAAC7f,WAAW,CAAC,CAAC;IAC5C;IACA,IAAI,CAAC8f,qBAAqB,CAAC9f,WAAW,CAAC,CAAC;IACxC;IACA;IACA,IAAI,IAAI,CAACshB,cAAc,CAACvc,SAAS,CAACT,MAAM,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACwb,qBAAqB,GAAG,IAAI,CAACZ,SAAS,CAACqC,eAAe,CACtDpiB,IAAI,CAAC7E,SAAS,CAAC,MAAM,IAAI,CAACgnB,cAAc,CAACvc,SAAS,CAACT,MAAM,GAAG,CAAC,CAAC,CAAC,CAC/D5E,SAAS,CAACsT,QAAQ,IAAI;QACvB,IAAI,CAACxU,OAAO,CAACI,GAAG,CAAC,MAAM,IAAI,CAAC0iB,cAAc,CAACd,IAAI,CAACxN,QAAQ,CAAC,CAAC;QAC1D,IAAI,IAAI,CAACsO,cAAc,CAACvc,SAAS,CAACT,MAAM,KAAK,CAAC,EAAE;UAC5C,IAAI,CAACwb,qBAAqB,CAAC9f,WAAW,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACkgB,IAAI,GAAG,IAAI;EACpB;EACA;EACAE,aAAaA,CAAA,EAAG;IAAA,IAAAoB,kBAAA;IACZ,CAAAA,kBAAA,OAAI,CAAC9iB,WAAW,cAAA8iB,kBAAA,eAAhBA,kBAAA,CAAkB3iB,MAAM,CAAC,CAAC;IAC1B,IAAI,CAACghB,qBAAqB,CAAC7f,WAAW,CAAC,CAAC;IACxC,IAAI,CAAC8f,qBAAqB,CAAC9f,WAAW,CAAC,CAAC;IACxC,IAAI,CAACkgB,IAAI,GAAG,KAAK;EACrB;AAGJ;AAACuB,oBAAA,GAvRKxC,mBAAmB;AAAAhjB,eAAA,CAAnBgjB,mBAAmB,wBAAAyC,6BAAA5f,iBAAA;EAAA,YAAAA,iBAAA,IAqR8Emd,oBAAmB;AAAA;AAAAhjB,eAAA,CArRpHgjB,mBAAmB,8BAxxEwDlnB,EAAE,CAAAgnB,iBAAA;EAAA1c,IAAA,EA8iFQ4c,oBAAmB;EAAAlX,SAAA;EAAA4Z,MAAA;IAAA7e,MAAA;IAAA0N,SAAA;IAAAjF,gBAAA;IAAAvI,OAAA;IAAAC,OAAA;IAAA9E,KAAA;IAAAF,MAAA;IAAAiR,QAAA;IAAAC,SAAA;IAAAM,aAAA;IAAAvM,UAAA;IAAAge,cAAA;IAAA9V,cAAA;IAAA8U,IAAA;IAAAS,YAAA;IAAAS,uBAAA;IAAA1U,WAAA,uDAAmoCtT,gBAAgB;IAAA+nB,YAAA,yDAAqE/nB,gBAAgB;IAAAmb,kBAAA,qEAAuFnb,gBAAgB;IAAAsb,aAAA,2DAAwEtb,gBAAgB;IAAA8K,IAAA,yCAA6C9K,gBAAgB;IAAA0T,mBAAA,uEAA0F1T,gBAAgB;EAAA;EAAAwoB,OAAA;IAAA7T,aAAA;IAAAuT,cAAA;IAAAjlB,MAAA;IAAAwC,MAAA;IAAA4hB,cAAA;IAAAK,mBAAA;EAAA;EAAA9B,QAAA;EAAA6C,QAAA,GA9iF3mD9pB,EAAE,CAAA+pB,oBAAA;AAAA;AAgjFnF;EAAA,QAAA/iB,SAAA,oBAAAA,SAAA,KAhjFiFhH,EAAE,CAAAqK,iBAAA,CAgjFQ6c,mBAAmB,EAAc,CAAC;IACjH5c,IAAI,EAAErJ,SAAS;IACfsJ,IAAI,EAAE,CAAC;MACCiT,QAAQ,EAAE,qEAAqE;MAC/EyJ,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAElc,MAAM,EAAE,CAAC;MACjDT,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEkO,SAAS,EAAE,CAAC;MACZnO,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEiJ,gBAAgB,EAAE,CAAC;MACnBlJ,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,qCAAqC;IAChD,CAAC,CAAC;IAAEU,OAAO,EAAE,CAAC;MACVX,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEW,OAAO,EAAE,CAAC;MACVZ,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEnE,KAAK,EAAE,CAAC;MACRkE,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAErE,MAAM,EAAE,CAAC;MACToE,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAE4M,QAAQ,EAAE,CAAC;MACX7M,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,6BAA6B;IACxC,CAAC,CAAC;IAAE6M,SAAS,EAAE,CAAC;MACZ9M,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEmN,aAAa,EAAE,CAAC;MAChBpN,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,kCAAkC;IAC7C,CAAC,CAAC;IAAEY,UAAU,EAAE,CAAC;MACbb,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,+BAA+B;IAC1C,CAAC,CAAC;IAAE4e,cAAc,EAAE,CAAC;MACjB7e,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAE8I,cAAc,EAAE,CAAC;MACjB/I,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAE4d,IAAI,EAAE,CAAC;MACP7d,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEqe,YAAY,EAAE,CAAC;MACfte,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,iCAAiC;IAC5C,CAAC,CAAC;IAAE8e,uBAAuB,EAAE,CAAC;MAC1B/e,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,sCAAsC;IACjD,CAAC,CAAC;IAAEoK,WAAW,EAAE,CAAC;MACdrK,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC;QAAEyf,KAAK,EAAE,gCAAgC;QAAE9I,SAAS,EAAE7f;MAAiB,CAAC;IACnF,CAAC,CAAC;IAAE+nB,YAAY,EAAE,CAAC;MACf9e,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC;QAAEyf,KAAK,EAAE,iCAAiC;QAAE9I,SAAS,EAAE7f;MAAiB,CAAC;IACpF,CAAC,CAAC;IAAEmb,kBAAkB,EAAE,CAAC;MACrBlS,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC;QAAEyf,KAAK,EAAE,uCAAuC;QAAE9I,SAAS,EAAE7f;MAAiB,CAAC;IAC1F,CAAC,CAAC;IAAEsb,aAAa,EAAE,CAAC;MAChBrS,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC;QAAEyf,KAAK,EAAE,kCAAkC;QAAE9I,SAAS,EAAE7f;MAAiB,CAAC;IACrF,CAAC,CAAC;IAAE8K,IAAI,EAAE,CAAC;MACP7B,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC;QAAEyf,KAAK,EAAE,yBAAyB;QAAE9I,SAAS,EAAE7f;MAAiB,CAAC;IAC5E,CAAC,CAAC;IAAE0T,mBAAmB,EAAE,CAAC;MACtBzK,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC;QAAEyf,KAAK,EAAE,wCAAwC;QAAE9I,SAAS,EAAE7f;MAAiB,CAAC;IAC3F,CAAC,CAAC;IAAE2U,aAAa,EAAE,CAAC;MAChB1L,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAEgoB,cAAc,EAAE,CAAC;MACjBjf,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAE+C,MAAM,EAAE,CAAC;MACTgG,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAEuF,MAAM,EAAE,CAAC;MACTwD,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAEmnB,cAAc,EAAE,CAAC;MACjBpe,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAEwnB,mBAAmB,EAAE,CAAC;MACtBze,IAAI,EAAE/I;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,SAAS0oB,sDAAsDA,CAACjf,OAAO,EAAE;EACrE,OAAO,MAAMA,OAAO,CAAC2b,gBAAgB,CAACC,UAAU,CAAC,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsD,8CAA8C,GAAG;EACnDC,OAAO,EAAEzD,qCAAqC;EAC9C0D,IAAI,EAAE,CAAC9E,OAAO,CAAC;EACf+E,UAAU,EAAEJ;AAChB,CAAC;AAED,MAAMK,aAAa,CAAC;AAInBC,cAAA,GAJKD,aAAa;AAAApmB,eAAA,CAAbomB,aAAa,wBAAAE,uBAAAzgB,iBAAA;EAAA,YAAAA,iBAAA,IACoFugB,cAAa;AAAA;AAAApmB,eAAA,CAD9GomB,aAAa,8BAxpF8DtqB,EAAE,CAAAyqB,gBAAA;EAAAngB,IAAA,EA0pFqBggB,cAAa;EAAAI,OAAA,GAAY9mB,UAAU,EAAEP,YAAY,EAAER,eAAe,EAAEqkB,mBAAmB,EAAEL,gBAAgB;EAAA8D,OAAA,GAAazD,mBAAmB,EAAEL,gBAAgB,EAAEhkB,eAAe;AAAA;AAAAqB,eAAA,CAF9QomB,aAAa,8BAxpF8DtqB,EAAE,CAAA4qB,gBAAA;EAAAC,SAAA,EA2pF+C,CAACvF,OAAO,EAAE4E,8CAA8C,CAAC;EAAAQ,OAAA,GAAY9mB,UAAU,EAAEP,YAAY,EAAER,eAAe,EAAEA,eAAe;AAAA;AAEjQ;EAAA,QAAAmE,SAAA,oBAAAA,SAAA,KA7pFiFhH,EAAE,CAAAqK,iBAAA,CA6pFQigB,aAAa,EAAc,CAAC;IAC3GhgB,IAAI,EAAE9I,QAAQ;IACd+I,IAAI,EAAE,CAAC;MACCmgB,OAAO,EAAE,CAAC9mB,UAAU,EAAEP,YAAY,EAAER,eAAe,EAAEqkB,mBAAmB,EAAEL,gBAAgB,CAAC;MAC3F8D,OAAO,EAAE,CAACzD,mBAAmB,EAAEL,gBAAgB,EAAEhkB,eAAe,CAAC;MACjEgoB,SAAS,EAAE,CAACvF,OAAO,EAAE4E,8CAA8C;IACvE,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASpmB,mBAAmB,IAAIgnB,CAAC,EAAEjE,gBAAgB,IAAIkE,CAAC,EAAEvS,iCAAiC,IAAIwS,CAAC,EAAEpH,sBAAsB,IAAIqH,CAAC,EAAE/iB,kBAAkB,IAAIgjB,CAAC,EAAEra,gBAAgB,IAAIsa,CAAC,EAAE/hB,wBAAwB,IAAIgiB,CAAC,EAAE1H,oCAAoC,IAAI2H,CAAC,EAAE/F,OAAO,IAAI9B,CAAC,EAAE0D,mBAAmB,IAAIlkB,CAAC,EAAE2P,UAAU,IAAInQ,CAAC,EAAEyiB,sBAAsB,IAAIqG,CAAC,EAAE7H,iCAAiC,IAAI8H,CAAC,EAAE/gB,aAAa,IAAIghB,CAAC,EAAE1gB,sBAAsB,IAAIrH,CAAC,EAAE+H,mBAAmB,IAAIpI,CAAC,EAAEqI,8BAA8B,IAAIqB,CAAC,EAAEf,0BAA0B,IAAI0f,CAAC,EAAE9hB,qBAAqB,IAAI+hB,CAAC,EAAEnlB,mBAAmB,IAAIolB,CAAC,EAAErB,aAAa,IAAIsB,CAAC,EAAE9d,6BAA6B,IAAI+d,CAAC,EAAEnf,yBAAyB,IAAIof,CAAC,EAAElgB,wBAAwB,IAAImgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}