{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiGridComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-grid.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-grid.component.scss?ngResource\";\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { ChangeDetectorRef, Component, ElementRef, EventEmitter, Inject, InjectionToken, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { BehaviorSubject, merge, Subject } from 'rxjs';\nimport { debounceTime, skip, switchMap, take, takeUntil } from 'rxjs/operators';\nimport { DEFAULT_PAGE_SIZE } from '../services/settings/app-settings';\nimport { SettingsService } from '../services/settings/settings.service';\nimport { SwHubEntityService } from '../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwuiTopFilterDataService } from '../swui-schema-top-filter/top-filter-data.service';\nimport { WidgetRegistry } from './registry/registry';\nimport { SwuiGridDataService } from './services/grid-data.service';\nimport { SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';\nimport { SwuiGridDataSource } from './swui-grid.datasource';\nimport { SwDexieService } from '../services/sw-dexie/sw-dexie.service';\nexport const SWUI_GRID_SELECTION_TRANSFORMER_TOKEN = new InjectionToken('grid-selection-transformer');\nexport const SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN = new InjectionToken('grid-selection-row-available');\nlet SwuiGridComponent = (_SwuiGridComponent = class SwuiGridComponent {\n  get schema() {\n    return this._schema;\n  }\n  set schema(value) {\n    this._schema = [...value];\n    this.addActionsToSchema();\n    this.updateDisplayedColumns();\n  }\n  get data() {\n    return this._data;\n  }\n  set data(value) {\n    if (typeof value === 'undefined') {\n      return;\n    }\n    this._data = value;\n  }\n  constructor(registry, cdr, urlHandler, element, dexieService, filter, hubEntityService, settings, service, selectionTransformer, selectionRowAvailable) {\n    this.registry = registry;\n    this.cdr = cdr;\n    this.urlHandler = urlHandler;\n    this.element = element;\n    this.dexieService = dexieService;\n    this.filter = filter;\n    this.hubEntityService = hubEntityService;\n    this.settings = settings;\n    this.service = service;\n    this.selectionTransformer = selectionTransformer;\n    this.selectionRowAvailable = selectionRowAvailable;\n    this.selected = [];\n    this.rowActions = [];\n    this.rowActionsColumnTitle = 'COMPONENTS.GRID.GRID_ROW_ACTIONS';\n    this.rowActionsMenuIcon = 'more_horiz';\n    this.bulkActions = [];\n    this.ignorePlainLink = false;\n    this.pagination = true;\n    this.stickyHeader = false;\n    this.columnsManagement = false;\n    this.gridId = 'default-grid-id';\n    this.queryParamsAffectsPageSize = false;\n    this.ignoreQueryParams = false;\n    this.useHubEntity = false;\n    this.bulkSelectionOnly = false;\n    this.footer = false;\n    this.blindPaginator = false;\n    this.disableRefreshAction = false;\n    this.showTotalItems = true;\n    this.totalItemsTitle = 'COMPONENTS.GRID.ITEMS_FOUND';\n    this.sortDirection = '';\n    this.loading = false;\n    this.widgetActionEmitted = new EventEmitter();\n    // schema items which will be rendered in mat-table\n    this.columnDefSchema = [];\n    this.displayedColumns = [];\n    this.loading$ = new BehaviorSubject(true);\n    this.total = 0;\n    this.bulkActionsColumnName = 'bulk-actions-column';\n    this.rowActionsColumnName = 'row-actions-column';\n    this._schema = [];\n    this.destroyed$ = new Subject();\n    this.selectionPageCheckedOutdated = true;\n    this.selectionPageChecked = false;\n    this.dataSource = new SwuiGridDataSource(this.service, this.hubEntityService, this.filter, this.urlHandler);\n  }\n  get isEmpty() {\n    return !this.dataSource.data || this.dataSource.data.length === 0;\n  }\n  ngOnInit() {\n    this.dataSource.initDatasource(this.useHubEntity);\n    this.initSelection();\n    this.addActionsToSchema();\n    this.buildColumnDefSchema();\n    this.updateDisplayedColumns();\n    this.configureUrlHandler();\n    this.dataSource.total$.pipe(takeUntil(this.destroyed$)).subscribe(total => {\n      this.total = total;\n    });\n  }\n  ngAfterViewInit() {\n    this.setupPageSize();\n    this.addPaginatorToDataSource();\n    this.watchForLoading();\n    this.useQueryParamsData();\n    this.setFilterState();\n    this.attachSort();\n    if (!this.filter) {\n      this.dataSource.loadData();\n    }\n    this.hideTotal();\n    this.cdr.detectChanges();\n  }\n  ngOnChanges(changes) {\n    if ('data' in changes) {\n      this.dataSource.data = changes['data'].currentValue;\n      if (!changes['data'].isFirstChange()) {\n        if (this.paginator) {\n          this.paginator.firstPage();\n        }\n        this.dataSource.loadData();\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  isAllSelected() {\n    if (this.selectionPageCheckedOutdated && this.dataSource.data) {\n      this.selectionPageChecked = this.dataSource.data.filter(row => this.isRowAvailable(row)).every(row => this.selection.isSelected(this.getSelectionRow(row)));\n      this.selectionPageCheckedOutdated = false;\n    }\n    return this.selectionPageChecked;\n  }\n  // adds or removes to selection\n  // Please note that we can have multiple data arrays in selection model due to pagination of data\n  masterToggle() {\n    if (this.dataSource.data) {\n      const allSelected = this.isAllSelected();\n      this.dataSource.data.filter(row => this.isRowAvailable(row)).forEach(row => allSelected ? this.selection.deselect(this.getSelectionRow(row)) : this.selection.select(this.getSelectionRow(row)));\n    }\n  }\n  columnsChanged(visibilityData) {\n    this._schema.forEach(item => {\n      if (item.field in visibilityData) {\n        item.isListVisible = visibilityData[item.field].isListVisible;\n      }\n    });\n    this.updateDisplayedColumns();\n  }\n  onPageClick() {\n    this.showLoading();\n  }\n  getSelectionRow(row) {\n    let transformFn;\n    if (!!this.selectionTransformer) {\n      transformFn = this.selectionTransformer.transform;\n    }\n    return transformFn ? transformFn(row) : row;\n  }\n  isRowAvailable(row) {\n    let availableFn;\n    if (!!this.selectionRowAvailable) {\n      availableFn = this.selectionRowAvailable.available;\n    }\n    return availableFn ? availableFn(row) : true;\n  }\n  refreshData() {\n    this.dataSource.loadData();\n  }\n  attachSort() {\n    if (this.sort) {\n      this.dataSource.addSort(this.sort);\n      this.sort.sortChange.pipe(takeUntil(this.destroyed$)).subscribe(() => this.showLoading());\n    }\n  }\n  addPaginatorToDataSource() {\n    if (this.paginator && this.pageSize) {\n      this.paginator.pageSize = this.pageSize;\n      this.dataSource.addPaginator(this.paginator, this.blindPaginator);\n    }\n  }\n  /**\n   * Adds internal actions columns\n   * Needs to work in Columns Visibility with it\n   */\n  addActionsToSchema() {\n    // bulk action column is first by default\n    if (this.bulkActions && this.bulkActions.length) {\n      const bulkActionsSchemaItem = {\n        field: this.bulkActionsColumnName,\n        title: 'Bulk Actions',\n        isList: true,\n        isListVisible: true,\n        type: 'internal'\n      };\n      this.schema.unshift(bulkActionsSchemaItem);\n    }\n    // row actions column is last by default\n    if (this.rowActions && this.rowActions.length) {\n      const rowActionsSchemaItem = {\n        field: this.rowActionsColumnName,\n        title: this.rowActionsColumnTitle,\n        isList: true,\n        isListVisible: true,\n        type: 'internal'\n      };\n      this.schema.push(rowActionsSchemaItem);\n    }\n  }\n  /**\n   * Updates schema which is used in *ngFor for building column definitions in mat-table template\n   * We need to remove internal items such as row/bulk actions, because they are already presents in mat-table template\n   */\n  buildColumnDefSchema() {\n    this.columnDefSchema = this._schema.filter(({\n      field\n    }) => field !== this.rowActionsColumnName && field !== this.bulkActionsColumnName);\n  }\n  /**\n   * Updates column names which will be shown in mat-table\n   */\n  updateDisplayedColumns() {\n    this.displayedColumns = this._schema.filter(({\n      isListVisible\n    }) => isListVisible !== false).map(({\n      field\n    }) => field);\n  }\n  watchForLoading() {\n    const skipCount = this._data ? 0 : 1; // hotfix to prevent first empty data loading\n    this.dataSource.connect().pipe(skip(skipCount), takeUntil(this.destroyed$)).subscribe(() => this.hideLoading());\n    if (this.filter) {\n      this.filter.appliedFilter.pipe(takeUntil(this.destroyed$)).subscribe(() => this.showLoading());\n    }\n    if (this.hubEntityService) {\n      this.hubEntityService.entitySelected$.pipe(takeUntil(this.destroyed$)).subscribe(() => this.showLoading());\n    }\n  }\n  showLoading() {\n    this.loading$.next(true);\n  }\n  hideLoading() {\n    this.loading$.next(false);\n    this.hideTotal();\n  }\n  useQueryParamsData() {\n    if (this.ignoreQueryParams) {\n      return;\n    }\n    if (this.paginator) {\n      if (this.queryParamsAffectsPageSize) {\n        this.pageSize = this.urlHandler.fetchPageSize(this.pageSize);\n        this.paginator.pageSize = this.pageSize;\n      }\n      this.urlHandler.setParamsToPaginator(this.paginator);\n    }\n    if (this.sort) {\n      this.urlHandler.setParamsToSort(this.sort);\n    }\n    if (this.filter) {\n      const filterParams = this.urlHandler.getFilterQueryParams();\n      if (filterParams.hasOwnProperty('path')) {\n        delete filterParams['path'];\n      }\n      if (Object.keys(filterParams).length) {\n        this.dexieService.getFilterState(this.savedFilteredPageName).then(filterState => {\n          const state = Object.keys(filterState).reduce((res, key) => {\n            res[key] = null;\n            return res;\n          }, {});\n          this.filter.submitFilter(_objectSpread(_objectSpread({}, state || {}), filterParams || {}));\n        }).catch(() => this.filter.submitFilter(filterParams));\n        return;\n      }\n      if (!this.savedFilteredPageName) {\n        this.filter.submitFilter(filterParams);\n      } else {\n        this.dexieService.getFilterState(this.savedFilteredPageName).then(filterState => {\n          this.filter.submitFilter(_objectSpread(_objectSpread(_objectSpread({}, filterState || {}), this.savedFilteredPageParams || {}), filterParams || {}));\n        }).catch(() => this.filter.submitFilter(filterParams));\n      }\n    }\n  }\n  setFilterState() {\n    var _this$filter;\n    (_this$filter = this.filter) === null || _this$filter === void 0 || _this$filter.appliedFilter.pipe(take(1), switchMap(() => this.filter.filterFormState), debounceTime(500)).subscribe(filter => {\n      if (this.savedFilteredPageName) {\n        this.dexieService.putFilterState(this.savedFilteredPageName, filter).catch(e => console.log(e));\n      }\n    });\n  }\n  configureUrlHandler() {\n    this.urlHandler.setAllowQueryParamsUpdate(!this.ignoreQueryParams);\n  }\n  initSelection() {\n    this.selection = new SelectionModel(true, this.selected);\n    merge(this.selection.changed.asObservable(), this.dataSource.connect()).pipe(takeUntil(this.destroyed$)).subscribe(() => this.selectionPageCheckedOutdated = true);\n  }\n  setupPageSize() {\n    if (typeof this.pageSize === 'undefined') {\n      if (this.settings) {\n        this.subscribeForSettings();\n      } else {\n        this.pageSize = DEFAULT_PAGE_SIZE;\n      }\n    }\n  }\n  /**\n   * Subscribes for settings changes and updates specific fields\n   */\n  subscribeForSettings() {\n    this.settings.appSettings$.pipe(takeUntil(this.destroyed$)).subscribe(({\n      pageSize\n    }) => {\n      if (this.paginator) {\n        if (pageSize) {\n          this.overwritePageSize(pageSize);\n        } else {\n          this.addPaginatorToDataSource();\n        }\n      }\n    });\n  }\n  /**\n   * Changes page size for existing grid with different pageSize value\n   * (e.g. if we changed pageSize in settings dialog from 10 to 20)\n   * @param pageSize\n   */\n  overwritePageSize(pageSize) {\n    if (this.pageSize !== pageSize) {\n      this.pageSize = pageSize;\n      if (this.paginator) {\n        this.showLoading();\n        this.dataSource.changePageSize(pageSize);\n      }\n    }\n  }\n  hideTotal() {\n    if (!this.blindPaginator || !this.paginator || !this.dataSource.data) {\n      return;\n    }\n    const el = this.element.nativeElement.getElementsByClassName('mat-paginator-range-label');\n    if (el && el[0]) {\n      if (this.dataSource.data.length) {\n        const firstIndex = this.paginator.pageIndex * this.paginator.pageSize;\n        el[0].innerText = `${firstIndex + 1} - ${firstIndex + this.dataSource.data.length}`;\n      } else {\n        el[0].innerText = '0 - 0';\n      }\n    }\n  }\n}, _SwuiGridComponent.ctorParameters = () => [{\n  type: WidgetRegistry\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: SwuiGridUrlHandlerService\n}, {\n  type: ElementRef\n}, {\n  type: SwDexieService\n}, {\n  type: SwuiTopFilterDataService,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: SwHubEntityService,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: SettingsService,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: SwuiGridDataService,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [SwuiGridDataService]\n  }]\n}, {\n  type: undefined,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [SWUI_GRID_SELECTION_TRANSFORMER_TOKEN]\n  }]\n}, {\n  type: undefined,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN]\n  }]\n}], _SwuiGridComponent.propDecorators = {\n  schema: [{\n    type: Input\n  }],\n  data: [{\n    type: Input\n  }],\n  selected: [{\n    type: Input\n  }],\n  rowActions: [{\n    type: Input\n  }],\n  rowActionsColumnTitle: [{\n    type: Input\n  }],\n  rowActionsMenuIcon: [{\n    type: Input\n  }],\n  bulkActions: [{\n    type: Input\n  }],\n  ignorePlainLink: [{\n    type: Input\n  }],\n  pagination: [{\n    type: Input\n  }],\n  stickyHeader: [{\n    type: Input\n  }],\n  columnsManagement: [{\n    type: Input\n  }],\n  gridId: [{\n    type: Input\n  }],\n  queryParamsAffectsPageSize: [{\n    type: Input\n  }],\n  ignoreQueryParams: [{\n    type: Input\n  }],\n  useHubEntity: [{\n    type: Input\n  }],\n  bulkSelectionOnly: [{\n    type: Input\n  }],\n  footer: [{\n    type: Input\n  }],\n  blindPaginator: [{\n    type: Input\n  }],\n  disableRefreshAction: [{\n    type: Input\n  }],\n  showTotalItems: [{\n    type: Input\n  }],\n  totalItemsTitle: [{\n    type: Input\n  }],\n  sortActive: [{\n    type: Input\n  }],\n  sortDirection: [{\n    type: Input\n  }],\n  savedFilteredPageName: [{\n    type: Input\n  }],\n  savedFilteredPageParams: [{\n    type: Input\n  }],\n  loading: [{\n    type: Input\n  }],\n  pageSize: [{\n    type: Input\n  }],\n  widgetActionEmitted: [{\n    type: Output\n  }],\n  paginator: [{\n    type: ViewChild,\n    args: [MatPaginator]\n  }],\n  sort: [{\n    type: ViewChild,\n    args: [MatSort]\n  }]\n}, _SwuiGridComponent);\nSwuiGridComponent = __decorate([Component({\n  selector: 'lib-swui-grid',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiGridComponent);\nexport { SwuiGridComponent };", "map": {"version": 3, "names": ["SelectionModel", "ChangeDetectorRef", "Component", "ElementRef", "EventEmitter", "Inject", "InjectionToken", "Input", "Optional", "Output", "ViewChild", "MatPaginator", "MatSort", "BehaviorSubject", "merge", "Subject", "debounceTime", "skip", "switchMap", "take", "takeUntil", "DEFAULT_PAGE_SIZE", "SettingsService", "SwHubEntityService", "SwuiTopFilterDataService", "WidgetRegistry", "SwuiGridDataService", "SwuiGridUrlHandlerService", "SwuiGridDataSource", "SwDexieService", "SWUI_GRID_SELECTION_TRANSFORMER_TOKEN", "SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN", "SwuiGridComponent", "_SwuiGridComponent", "schema", "_schema", "value", "addActionsToSchema", "updateDisplayedColumns", "data", "_data", "constructor", "registry", "cdr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "dexieService", "filter", "hubEntityService", "settings", "service", "selectionTransformer", "selectionRowAvailable", "selected", "rowActions", "rowActionsColumnTitle", "rowActionsMenuIcon", "bulkActions", "ignorePlainLink", "pagination", "<PERSON><PERSON><PERSON><PERSON>", "columnsManagement", "gridId", "queryParamsAffectsPageSize", "ignoreQueryParams", "useHubEntity", "bulkSelectionOnly", "footer", "blindPaginator", "disableRefreshAction", "showTotalItems", "totalItemsTitle", "sortDirection", "loading", "widgetActionEmitted", "columnDefSchema", "displayedColumns", "loading$", "total", "bulkActionsColumnName", "rowActionsColumnName", "destroyed$", "selectionPageCheckedOutdated", "selectionPageChecked", "dataSource", "isEmpty", "length", "ngOnInit", "initDatasource", "initSelection", "buildColumnDefSchema", "configure<PERSON><PERSON><PERSON><PERSON><PERSON>", "total$", "pipe", "subscribe", "ngAfterViewInit", "setupPageSize", "addPaginatorToDataSource", "watchForLoading", "useQueryParamsData", "setFilterState", "attachSort", "loadData", "hideTotal", "detectChanges", "ngOnChanges", "changes", "currentValue", "isFirstChange", "paginator", "firstPage", "ngOnDestroy", "next", "undefined", "complete", "isAllSelected", "row", "isRowAvailable", "every", "selection", "isSelected", "getSelectionRow", "masterToggle", "allSelected", "for<PERSON>ach", "deselect", "select", "columnsChanged", "visibilityData", "item", "field", "isListVisible", "onPageClick", "showLoading", "transformFn", "transform", "availableFn", "available", "refreshData", "sort", "addSort", "sortChange", "pageSize", "addPaginator", "bulkActionsSchemaItem", "title", "isList", "type", "unshift", "rowActionsSchemaItem", "push", "map", "skip<PERSON><PERSON>nt", "connect", "hideLoading", "appliedFilter", "entitySelected$", "fetchPageSize", "setParamsToPaginator", "setParamsToSort", "filterParams", "getFilterQueryParams", "hasOwnProperty", "Object", "keys", "getFilterState", "savedFilteredPageName", "then", "filterState", "state", "reduce", "res", "key", "submitFilter", "_objectSpread", "catch", "savedFilteredPageParams", "_this$filter", "filterFormState", "putFilterState", "e", "console", "log", "setAllowQueryParamsUpdate", "changed", "asObservable", "subscribeForSettings", "appSettings$", "overwritePageSize", "changePageSize", "el", "nativeElement", "getElementsByClassName", "firstIndex", "pageIndex", "innerText", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid.component.ts"], "sourcesContent": ["import { SelectionModel } from '@angular/cdk/collections';\nimport {\n  AfterViewInit,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  SimpleChanges,\n  ViewChild\n} from '@angular/core';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort, SortDirection } from '@angular/material/sort';\nimport { BehaviorSubject, merge, Subject } from 'rxjs';\nimport { debounceTime, skip, switchMap, take, takeUntil } from 'rxjs/operators';\nimport { AppSettings, DEFAULT_PAGE_SIZE } from '../services/settings/app-settings';\nimport { SettingsService } from '../services/settings/settings.service';\nimport { SwHubEntityService } from '../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwuiTopFilterDataService } from '../swui-schema-top-filter/top-filter-data.service';\nimport { SchemaVisibilityData } from './columns-management/columns-management.model';\nimport { WidgetRegistry } from './registry/registry';\nimport { RowAction } from './row-actions/row-actions.component';\nimport { SwuiGridDataService } from './services/grid-data.service';\nimport { SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';\nimport { SwuiGridDataSource } from './swui-grid.datasource';\nimport { SwuiGridSchemaField } from './swui-grid.model';\nimport { SwDexieService } from '../services/sw-dexie/sw-dexie.service';\n\n\nexport interface SelectionTransformer {\n  transform( data: any ): any;\n}\n\nexport interface SelectionRowAvailable {\n  available( data: any ): boolean;\n}\n\nexport const SWUI_GRID_SELECTION_TRANSFORMER_TOKEN = new InjectionToken<SelectionTransformer>('grid-selection-transformer');\nexport const SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN = new InjectionToken<SelectionTransformer>('grid-selection-row-available');\n\n@Component({\n    selector: 'lib-swui-grid',\n    templateUrl: './swui-grid.component.html',\n    styleUrls: ['./swui-grid.component.scss'],\n    standalone: false\n})\nexport class SwuiGridComponent<T extends object, S = T> implements OnInit, OnDestroy, AfterViewInit, OnChanges {\n  get schema(): SwuiGridSchemaField[] {\n    return this._schema;\n  }\n\n  @Input() set schema( value: SwuiGridSchemaField[] ) {\n    this._schema = [...value];\n\n    this.addActionsToSchema();\n    this.updateDisplayedColumns();\n  }\n\n  get data(): T[] | undefined {\n    return this._data;\n  }\n\n  @Input() set data( value: T[] | undefined ) {\n    if (typeof value === 'undefined') {\n      return;\n    }\n    this._data = value;\n  }\n\n  @Input() selected: S[] = [];\n  @Input() rowActions: RowAction[] = [];\n  @Input() rowActionsColumnTitle = 'COMPONENTS.GRID.GRID_ROW_ACTIONS';\n  @Input() rowActionsMenuIcon = 'more_horiz';\n  @Input() bulkActions: RowAction[] = [];\n  @Input() ignorePlainLink = false;\n\n  @Input() pagination = true;\n  @Input() stickyHeader = false;\n  @Input() columnsManagement = false;\n  @Input() gridId = 'default-grid-id';\n  @Input() queryParamsAffectsPageSize = false;\n  @Input() ignoreQueryParams = false;\n  @Input() useHubEntity = false;\n  @Input() bulkSelectionOnly = false; // for case when we need checkboxes for rows but not need bulk actions in general\n  @Input() footer = false;\n  @Input() blindPaginator = false;\n  @Input() disableRefreshAction = false;\n  @Input() showTotalItems = true;\n  @Input() totalItemsTitle = 'COMPONENTS.GRID.ITEMS_FOUND';\n  @Input() sortActive?: string;\n  @Input() sortDirection: SortDirection = '';\n\n  @Input() savedFilteredPageName?: string;\n  @Input() savedFilteredPageParams?: any;\n  @Input() loading = false; // for case with data\n\n  /**\n   * if pageSize is undefined:\n   * - and we have settings service, then value will be taken from settings;\n   * - and there are no settings service, then default value will be set;\n   * if pageSize value was taken from input, then it becomes strict and can't be changed from settings\n   */\n  @Input() pageSize: number | undefined;\n\n  @Output() widgetActionEmitted = new EventEmitter<any>();\n\n  @ViewChild(MatPaginator) paginator?: MatPaginator;\n  @ViewChild(MatSort) sort?: MatSort;\n\n  // schema items which will be rendered in mat-table\n  columnDefSchema: SwuiGridSchemaField[] = [];\n  displayedColumns: string[] = [];\n  dataSource!: SwuiGridDataSource<T>;\n  selection!: SelectionModel<S>;\n  loading$ = new BehaviorSubject(true);\n  total = 0;\n\n  bulkActionsColumnName = 'bulk-actions-column';\n  rowActionsColumnName = 'row-actions-column';\n\n  private _schema: SwuiGridSchemaField[] = [];\n  private _data: T[] | undefined;\n  private readonly destroyed$ = new Subject<void>();\n\n  private selectionPageCheckedOutdated = true;\n  private selectionPageChecked = false;\n\n  constructor(\n    readonly registry: WidgetRegistry,\n    private readonly cdr: ChangeDetectorRef,\n    private readonly urlHandler: SwuiGridUrlHandlerService,\n    private readonly element: ElementRef,\n    private readonly dexieService: SwDexieService,\n    @Optional() private readonly filter: SwuiTopFilterDataService,\n    @Optional() private readonly hubEntityService: SwHubEntityService,\n    @Optional() private readonly settings: SettingsService,\n    @Optional() @Inject(SwuiGridDataService) private service: SwuiGridDataService<T>,\n    @Optional() @Inject(SWUI_GRID_SELECTION_TRANSFORMER_TOKEN) private selectionTransformer: SelectionTransformer,\n    @Optional() @Inject(SWUI_GRID_SELECTION_ROW_AVAILABLE_TOKEN) private selectionRowAvailable: SelectionRowAvailable,\n  ) {\n    this.dataSource = new SwuiGridDataSource<T>(this.service, this.hubEntityService, this.filter, this.urlHandler);\n  }\n\n  get isEmpty(): boolean | undefined {\n    return !this.dataSource.data || this.dataSource.data.length === 0;\n  }\n\n  ngOnInit(): void {\n    this.dataSource.initDatasource(this.useHubEntity);\n    this.initSelection();\n    this.addActionsToSchema();\n    this.buildColumnDefSchema();\n    this.updateDisplayedColumns();\n    this.configureUrlHandler();\n    this.dataSource.total$.pipe(\n      takeUntil(this.destroyed$)\n    ).subscribe(total => {\n      this.total = total;\n    });\n  }\n\n  ngAfterViewInit(): void {\n    this.setupPageSize();\n    this.addPaginatorToDataSource();\n    this.watchForLoading();\n    this.useQueryParamsData();\n    this.setFilterState();\n    this.attachSort();\n\n    if (!this.filter) {\n      this.dataSource.loadData();\n    }\n    this.hideTotal();\n    this.cdr.detectChanges();\n  }\n\n  ngOnChanges( changes: SimpleChanges ): void {\n    if ('data' in changes) {\n      this.dataSource.data = changes['data'].currentValue;\n      if (!changes['data'].isFirstChange()) {\n        if (this.paginator) {\n          this.paginator.firstPage();\n        }\n        this.dataSource.loadData();\n      }\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n\n  isAllSelected() {\n    if (this.selectionPageCheckedOutdated && this.dataSource.data) {\n      this.selectionPageChecked = this.dataSource.data\n        .filter(( row ) => this.isRowAvailable(row))\n        .every(( row ) => this.selection.isSelected(this.getSelectionRow(row)));\n      this.selectionPageCheckedOutdated = false;\n    }\n    return this.selectionPageChecked;\n  }\n\n  // adds or removes to selection\n  // Please note that we can have multiple data arrays in selection model due to pagination of data\n  masterToggle() {\n    if (this.dataSource.data) {\n      const allSelected = this.isAllSelected();\n      this.dataSource.data\n        .filter(( row ) => this.isRowAvailable(row))\n        .forEach(( row ) => allSelected\n          ? this.selection.deselect(this.getSelectionRow(row))\n          : this.selection.select(this.getSelectionRow(row))\n        );\n    }\n  }\n\n  columnsChanged( visibilityData: SchemaVisibilityData ) {\n    this._schema.forEach(item => {\n      if (item.field in visibilityData) {\n        item.isListVisible = visibilityData[item.field].isListVisible;\n      }\n    });\n    this.updateDisplayedColumns();\n  }\n\n  onPageClick() {\n    this.showLoading();\n  }\n\n  getSelectionRow( row: T ): S {\n    let transformFn;\n    if (!!this.selectionTransformer) {\n      transformFn = this.selectionTransformer.transform;\n    }\n    return transformFn ? transformFn(row) : (row as unknown as S);\n  }\n\n  isRowAvailable( row: T ): boolean {\n    let availableFn;\n    if (!!this.selectionRowAvailable) {\n      availableFn = this.selectionRowAvailable.available;\n    }\n    return availableFn ? availableFn(row) : true;\n  }\n\n  refreshData() {\n    this.dataSource.loadData();\n  }\n\n  private attachSort() {\n    if (this.sort) {\n      this.dataSource.addSort(this.sort);\n      this.sort.sortChange\n        .pipe(takeUntil(this.destroyed$))\n        .subscribe(() => this.showLoading());\n    }\n  }\n\n  private addPaginatorToDataSource() {\n    if (this.paginator && this.pageSize) {\n      this.paginator.pageSize = this.pageSize;\n      this.dataSource.addPaginator(this.paginator, this.blindPaginator);\n    }\n  }\n\n  /**\n   * Adds internal actions columns\n   * Needs to work in Columns Visibility with it\n   */\n  private addActionsToSchema() {\n    // bulk action column is first by default\n    if (this.bulkActions && this.bulkActions.length) {\n      const bulkActionsSchemaItem: SwuiGridSchemaField = {\n        field: this.bulkActionsColumnName,\n        title: 'Bulk Actions',\n        isList: true,\n        isListVisible: true,\n        type: 'internal'\n      };\n      this.schema.unshift(bulkActionsSchemaItem);\n    }\n    // row actions column is last by default\n    if (this.rowActions && this.rowActions.length) {\n      const rowActionsSchemaItem: SwuiGridSchemaField = {\n        field: this.rowActionsColumnName,\n        title: this.rowActionsColumnTitle,\n        isList: true,\n        isListVisible: true,\n        type: 'internal'\n      };\n      this.schema.push(rowActionsSchemaItem);\n    }\n  }\n\n  /**\n   * Updates schema which is used in *ngFor for building column definitions in mat-table template\n   * We need to remove internal items such as row/bulk actions, because they are already presents in mat-table template\n   */\n  private buildColumnDefSchema() {\n    this.columnDefSchema = this._schema\n      .filter(( { field } ) => field !== this.rowActionsColumnName && field !== this.bulkActionsColumnName);\n  }\n\n  /**\n   * Updates column names which will be shown in mat-table\n   */\n  private updateDisplayedColumns() {\n    this.displayedColumns = this._schema\n      .filter(( { isListVisible } ) => isListVisible !== false)\n      .map(( { field } ) => field);\n  }\n\n  private watchForLoading() {\n    const skipCount = this._data ? 0 : 1; // hotfix to prevent first empty data loading\n    this.dataSource.connect()\n      .pipe(\n        skip(skipCount),\n        takeUntil(this.destroyed$)\n      ).subscribe(() => this.hideLoading());\n\n    if (this.filter) {\n      this.filter.appliedFilter\n        .pipe(takeUntil(this.destroyed$))\n        .subscribe(() => this.showLoading());\n    }\n\n    if (this.hubEntityService) {\n      this.hubEntityService.entitySelected$\n        .pipe(takeUntil(this.destroyed$))\n        .subscribe(() => this.showLoading());\n    }\n  }\n\n  private showLoading() {\n    this.loading$.next(true);\n  }\n\n  private hideLoading() {\n    this.loading$.next(false);\n    this.hideTotal();\n  }\n\n  private useQueryParamsData() {\n    if (this.ignoreQueryParams) {\n      return;\n    }\n\n    if (this.paginator) {\n      if (this.queryParamsAffectsPageSize) {\n        this.pageSize = this.urlHandler.fetchPageSize(this.pageSize);\n        this.paginator.pageSize = this.pageSize;\n      }\n      this.urlHandler.setParamsToPaginator(this.paginator);\n    }\n\n    if (this.sort) {\n      this.urlHandler.setParamsToSort(this.sort);\n    }\n\n    if (this.filter) {\n      const filterParams = this.urlHandler.getFilterQueryParams();\n      if (filterParams.hasOwnProperty('path')) {\n        delete filterParams['path'];\n      }\n\n      if (Object.keys(filterParams).length) {\n        this.dexieService.getFilterState(this.savedFilteredPageName)\n          .then(filterState => {\n            const state = Object.keys(filterState).reduce((res: any, key) => {\n              res[key] = null;\n\n              return res;\n            }, {});\n\n            this.filter.submitFilter({ ...(state || {}), ...(filterParams || {}) });\n          })\n          .catch(() => this.filter.submitFilter(filterParams));\n\n        return;\n      }\n\n      if (!this.savedFilteredPageName) {\n        this.filter.submitFilter(filterParams);\n      } else {\n        this.dexieService.getFilterState(this.savedFilteredPageName)\n          .then(filterState => {\n            this.filter.submitFilter({ ...(filterState || {}), ...(this.savedFilteredPageParams || {}), ...(filterParams || {}) });\n          })\n          .catch(() => this.filter.submitFilter(filterParams));\n      }\n    }\n  }\n\n  private setFilterState() {\n    this.filter?.appliedFilter\n      .pipe(\n        take(1),\n        switchMap(() => this.filter.filterFormState),\n        debounceTime(500)\n      )\n      .subscribe(filter => {\n        if (this.savedFilteredPageName) {\n          this.dexieService.putFilterState(this.savedFilteredPageName, filter)\n            .catch(( e ) => console.log(e));\n        }\n      });\n  }\n\n  private configureUrlHandler() {\n    this.urlHandler.setAllowQueryParamsUpdate(!this.ignoreQueryParams);\n  }\n\n  private initSelection() {\n    this.selection = new SelectionModel<S>(true, this.selected);\n    merge(this.selection.changed.asObservable(), this.dataSource.connect())\n      .pipe(takeUntil(this.destroyed$))\n      .subscribe(() => this.selectionPageCheckedOutdated = true);\n  }\n\n  private setupPageSize() {\n    if (typeof this.pageSize === 'undefined') {\n      if (this.settings) {\n        this.subscribeForSettings();\n      } else {\n        this.pageSize = DEFAULT_PAGE_SIZE;\n      }\n    }\n  }\n\n  /**\n   * Subscribes for settings changes and updates specific fields\n   */\n  private subscribeForSettings() {\n    this.settings.appSettings$\n      .pipe(takeUntil(this.destroyed$))\n      .subscribe(( { pageSize }: AppSettings ) => {\n        if (this.paginator) {\n          if (pageSize) {\n            this.overwritePageSize(pageSize);\n          } else {\n            this.addPaginatorToDataSource();\n          }\n        }\n      });\n  }\n\n  /**\n   * Changes page size for existing grid with different pageSize value\n   * (e.g. if we changed pageSize in settings dialog from 10 to 20)\n   * @param pageSize\n   */\n  private overwritePageSize( pageSize: number ) {\n    if (this.pageSize !== pageSize) {\n      this.pageSize = pageSize;\n\n      if (this.paginator) {\n        this.showLoading();\n        this.dataSource.changePageSize(pageSize);\n      }\n    }\n  }\n\n  private hideTotal() {\n    if (!this.blindPaginator || !this.paginator || !this.dataSource.data) {\n      return;\n    }\n    const el = this.element.nativeElement.getElementsByClassName('mat-paginator-range-label');\n    if (el && el[0]) {\n      if (this.dataSource.data.length) {\n        const firstIndex = this.paginator.pageIndex * this.paginator.pageSize;\n        el[0].innerText = `${firstIndex + 1} - ${firstIndex + this.dataSource.data.length}`;\n      } else {\n        el[0].innerText = '0 - 0';\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAEEC,iBAAiB,EACjBC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,MAAM,EACNC,cAAc,EACdC,KAAK,EAILC,QAAQ,EACRC,MAAM,EAENC,SAAS,QACJ,eAAe;AACtB,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAuB,wBAAwB;AAC/D,SAASC,eAAe,EAAEC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AACtD,SAASC,YAAY,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAC/E,SAAsBC,iBAAiB,QAAQ,mCAAmC;AAClF,SAASC,eAAe,QAAQ,uCAAuC;AACvE,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,wBAAwB,QAAQ,mDAAmD;AAE5F,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3D,SAASC,cAAc,QAAQ,uCAAuC;AAWtE,OAAO,MAAMC,qCAAqC,GAAG,IAAIxB,cAAc,CAAuB,4BAA4B,CAAC;AAC3H,OAAO,MAAMyB,uCAAuC,GAAG,IAAIzB,cAAc,CAAuB,8BAA8B,CAAC;AAQxH,IAAM0B,iBAAiB,IAAAC,kBAAA,GAAvB,MAAMD,iBAAiB;EAC5B,IAAIE,MAAMA,CAAA;IACR,OAAO,IAAI,CAACC,OAAO;EACrB;MAEaD,MAAMA,CAAEE,KAA4B;IAC/C,IAAI,CAACD,OAAO,GAAG,CAAC,GAAGC,KAAK,CAAC;IAEzB,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEA,IAAIC,IAAIA,CAAA;IACN,OAAO,IAAI,CAACC,KAAK;EACnB;MAEaD,IAAIA,CAAEH,KAAsB;IACvC,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MAChC;IACF;IACA,IAAI,CAACI,KAAK,GAAGJ,KAAK;EACpB;EA4DAK,YACWC,QAAwB,EAChBC,GAAsB,EACtBC,UAAqC,EACrCC,OAAmB,EACnBC,YAA4B,EAChBC,MAAgC,EAChCC,gBAAoC,EACpCC,QAAyB,EACLC,OAA+B,EACbC,oBAA0C,EACxCC,qBAA4C;IAVxG,KAAAV,QAAQ,GAARA,QAAQ;IACA,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,YAAY,GAAZA,YAAY;IACA,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,QAAQ,GAARA,QAAQ;IACY,KAAAC,OAAO,GAAPA,OAAO;IACW,KAAAC,oBAAoB,GAApBA,oBAAoB;IAClB,KAAAC,qBAAqB,GAArBA,qBAAqB;IArEnF,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,UAAU,GAAgB,EAAE;IAC5B,KAAAC,qBAAqB,GAAG,kCAAkC;IAC1D,KAAAC,kBAAkB,GAAG,YAAY;IACjC,KAAAC,WAAW,GAAgB,EAAE;IAC7B,KAAAC,eAAe,GAAG,KAAK;IAEvB,KAAAC,UAAU,GAAG,IAAI;IACjB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,MAAM,GAAG,iBAAiB;IAC1B,KAAAC,0BAA0B,GAAG,KAAK;IAClC,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,eAAe,GAAG,6BAA6B;IAE/C,KAAAC,aAAa,GAAkB,EAAE;IAIjC,KAAAC,OAAO,GAAG,KAAK;IAUd,KAAAC,mBAAmB,GAAG,IAAItE,YAAY,EAAO;IAKvD;IACA,KAAAuE,eAAe,GAA0B,EAAE;IAC3C,KAAAC,gBAAgB,GAAa,EAAE;IAG/B,KAAAC,QAAQ,GAAG,IAAIhE,eAAe,CAAC,IAAI,CAAC;IACpC,KAAAiE,KAAK,GAAG,CAAC;IAET,KAAAC,qBAAqB,GAAG,qBAAqB;IAC7C,KAAAC,oBAAoB,GAAG,oBAAoB;IAEnC,KAAA7C,OAAO,GAA0B,EAAE;IAE1B,KAAA8C,UAAU,GAAG,IAAIlE,OAAO,EAAQ;IAEzC,KAAAmE,4BAA4B,GAAG,IAAI;IACnC,KAAAC,oBAAoB,GAAG,KAAK;IAelC,IAAI,CAACC,UAAU,GAAG,IAAIxD,kBAAkB,CAAI,IAAI,CAACsB,OAAO,EAAE,IAAI,CAACF,gBAAgB,EAAE,IAAI,CAACD,MAAM,EAAE,IAAI,CAACH,UAAU,CAAC;EAChH;EAEA,IAAIyC,OAAOA,CAAA;IACT,OAAO,CAAC,IAAI,CAACD,UAAU,CAAC7C,IAAI,IAAI,IAAI,CAAC6C,UAAU,CAAC7C,IAAI,CAAC+C,MAAM,KAAK,CAAC;EACnE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACH,UAAU,CAACI,cAAc,CAAC,IAAI,CAACvB,YAAY,CAAC;IACjD,IAAI,CAACwB,aAAa,EAAE;IACpB,IAAI,CAACpD,kBAAkB,EAAE;IACzB,IAAI,CAACqD,oBAAoB,EAAE;IAC3B,IAAI,CAACpD,sBAAsB,EAAE;IAC7B,IAAI,CAACqD,mBAAmB,EAAE;IAC1B,IAAI,CAACP,UAAU,CAACQ,MAAM,CAACC,IAAI,CACzBzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAC3B,CAACa,SAAS,CAAChB,KAAK,IAAG;MAClB,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB,CAAC,CAAC;EACJ;EAEAiB,eAAeA,CAAA;IACb,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,UAAU,EAAE;IAEjB,IAAI,CAAC,IAAI,CAACtD,MAAM,EAAE;MAChB,IAAI,CAACqC,UAAU,CAACkB,QAAQ,EAAE;IAC5B;IACA,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAAC5D,GAAG,CAAC6D,aAAa,EAAE;EAC1B;EAEAC,WAAWA,CAAEC,OAAsB;IACjC,IAAI,MAAM,IAAIA,OAAO,EAAE;MACrB,IAAI,CAACtB,UAAU,CAAC7C,IAAI,GAAGmE,OAAO,CAAC,MAAM,CAAC,CAACC,YAAY;MACnD,IAAI,CAACD,OAAO,CAAC,MAAM,CAAC,CAACE,aAAa,EAAE,EAAE;QACpC,IAAI,IAAI,CAACC,SAAS,EAAE;UAClB,IAAI,CAACA,SAAS,CAACC,SAAS,EAAE;QAC5B;QACA,IAAI,CAAC1B,UAAU,CAACkB,QAAQ,EAAE;MAC5B;IACF;EACF;EAEAS,WAAWA,CAAA;IACT,IAAI,CAAC9B,UAAU,CAAC+B,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAAChC,UAAU,CAACiC,QAAQ,EAAE;EAC5B;EAEAC,aAAaA,CAAA;IACX,IAAI,IAAI,CAACjC,4BAA4B,IAAI,IAAI,CAACE,UAAU,CAAC7C,IAAI,EAAE;MAC7D,IAAI,CAAC4C,oBAAoB,GAAG,IAAI,CAACC,UAAU,CAAC7C,IAAI,CAC7CQ,MAAM,CAAGqE,GAAG,IAAM,IAAI,CAACC,cAAc,CAACD,GAAG,CAAC,CAAC,CAC3CE,KAAK,CAAGF,GAAG,IAAM,IAAI,CAACG,SAAS,CAACC,UAAU,CAAC,IAAI,CAACC,eAAe,CAACL,GAAG,CAAC,CAAC,CAAC;MACzE,IAAI,CAAClC,4BAA4B,GAAG,KAAK;IAC3C;IACA,OAAO,IAAI,CAACC,oBAAoB;EAClC;EAEA;EACA;EACAuC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACtC,UAAU,CAAC7C,IAAI,EAAE;MACxB,MAAMoF,WAAW,GAAG,IAAI,CAACR,aAAa,EAAE;MACxC,IAAI,CAAC/B,UAAU,CAAC7C,IAAI,CACjBQ,MAAM,CAAGqE,GAAG,IAAM,IAAI,CAACC,cAAc,CAACD,GAAG,CAAC,CAAC,CAC3CQ,OAAO,CAAGR,GAAG,IAAMO,WAAW,GAC3B,IAAI,CAACJ,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACJ,eAAe,CAACL,GAAG,CAAC,CAAC,GAClD,IAAI,CAACG,SAAS,CAACO,MAAM,CAAC,IAAI,CAACL,eAAe,CAACL,GAAG,CAAC,CAAC,CACnD;IACL;EACF;EAEAW,cAAcA,CAAEC,cAAoC;IAClD,IAAI,CAAC7F,OAAO,CAACyF,OAAO,CAACK,IAAI,IAAG;MAC1B,IAAIA,IAAI,CAACC,KAAK,IAAIF,cAAc,EAAE;QAChCC,IAAI,CAACE,aAAa,GAAGH,cAAc,CAACC,IAAI,CAACC,KAAK,CAAC,CAACC,aAAa;MAC/D;IACF,CAAC,CAAC;IACF,IAAI,CAAC7F,sBAAsB,EAAE;EAC/B;EAEA8F,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAZ,eAAeA,CAAEL,GAAM;IACrB,IAAIkB,WAAW;IACf,IAAI,CAAC,CAAC,IAAI,CAACnF,oBAAoB,EAAE;MAC/BmF,WAAW,GAAG,IAAI,CAACnF,oBAAoB,CAACoF,SAAS;IACnD;IACA,OAAOD,WAAW,GAAGA,WAAW,CAAClB,GAAG,CAAC,GAAIA,GAAoB;EAC/D;EAEAC,cAAcA,CAAED,GAAM;IACpB,IAAIoB,WAAW;IACf,IAAI,CAAC,CAAC,IAAI,CAACpF,qBAAqB,EAAE;MAChCoF,WAAW,GAAG,IAAI,CAACpF,qBAAqB,CAACqF,SAAS;IACpD;IACA,OAAOD,WAAW,GAAGA,WAAW,CAACpB,GAAG,CAAC,GAAG,IAAI;EAC9C;EAEAsB,WAAWA,CAAA;IACT,IAAI,CAACtD,UAAU,CAACkB,QAAQ,EAAE;EAC5B;EAEQD,UAAUA,CAAA;IAChB,IAAI,IAAI,CAACsC,IAAI,EAAE;MACb,IAAI,CAACvD,UAAU,CAACwD,OAAO,CAAC,IAAI,CAACD,IAAI,CAAC;MAClC,IAAI,CAACA,IAAI,CAACE,UAAU,CACjBhD,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAChCa,SAAS,CAAC,MAAM,IAAI,CAACuC,WAAW,EAAE,CAAC;IACxC;EACF;EAEQpC,wBAAwBA,CAAA;IAC9B,IAAI,IAAI,CAACY,SAAS,IAAI,IAAI,CAACiC,QAAQ,EAAE;MACnC,IAAI,CAACjC,SAAS,CAACiC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACvC,IAAI,CAAC1D,UAAU,CAAC2D,YAAY,CAAC,IAAI,CAAClC,SAAS,EAAE,IAAI,CAACzC,cAAc,CAAC;IACnE;EACF;EAEA;;;;EAIQ/B,kBAAkBA,CAAA;IACxB;IACA,IAAI,IAAI,CAACoB,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC6B,MAAM,EAAE;MAC/C,MAAM0D,qBAAqB,GAAwB;QACjDd,KAAK,EAAE,IAAI,CAACnD,qBAAqB;QACjCkE,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,IAAI;QACZf,aAAa,EAAE,IAAI;QACnBgB,IAAI,EAAE;OACP;MACD,IAAI,CAACjH,MAAM,CAACkH,OAAO,CAACJ,qBAAqB,CAAC;IAC5C;IACA;IACA,IAAI,IAAI,CAAC1F,UAAU,IAAI,IAAI,CAACA,UAAU,CAACgC,MAAM,EAAE;MAC7C,MAAM+D,oBAAoB,GAAwB;QAChDnB,KAAK,EAAE,IAAI,CAAClD,oBAAoB;QAChCiE,KAAK,EAAE,IAAI,CAAC1F,qBAAqB;QACjC2F,MAAM,EAAE,IAAI;QACZf,aAAa,EAAE,IAAI;QACnBgB,IAAI,EAAE;OACP;MACD,IAAI,CAACjH,MAAM,CAACoH,IAAI,CAACD,oBAAoB,CAAC;IACxC;EACF;EAEA;;;;EAIQ3D,oBAAoBA,CAAA;IAC1B,IAAI,CAACf,eAAe,GAAG,IAAI,CAACxC,OAAO,CAChCY,MAAM,CAAC,CAAE;MAAEmF;IAAK,CAAE,KAAMA,KAAK,KAAK,IAAI,CAAClD,oBAAoB,IAAIkD,KAAK,KAAK,IAAI,CAACnD,qBAAqB,CAAC;EACzG;EAEA;;;EAGQzC,sBAAsBA,CAAA;IAC5B,IAAI,CAACsC,gBAAgB,GAAG,IAAI,CAACzC,OAAO,CACjCY,MAAM,CAAC,CAAE;MAAEoF;IAAa,CAAE,KAAMA,aAAa,KAAK,KAAK,CAAC,CACxDoB,GAAG,CAAC,CAAE;MAAErB;IAAK,CAAE,KAAMA,KAAK,CAAC;EAChC;EAEQhC,eAAeA,CAAA;IACrB,MAAMsD,SAAS,GAAG,IAAI,CAAChH,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACtC,IAAI,CAAC4C,UAAU,CAACqE,OAAO,EAAE,CACtB5D,IAAI,CACH5E,IAAI,CAACuI,SAAS,CAAC,EACfpI,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAC3B,CAACa,SAAS,CAAC,MAAM,IAAI,CAAC4D,WAAW,EAAE,CAAC;IAEvC,IAAI,IAAI,CAAC3G,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC4G,aAAa,CACtB9D,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAChCa,SAAS,CAAC,MAAM,IAAI,CAACuC,WAAW,EAAE,CAAC;IACxC;IAEA,IAAI,IAAI,CAACrF,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC4G,eAAe,CAClC/D,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAChCa,SAAS,CAAC,MAAM,IAAI,CAACuC,WAAW,EAAE,CAAC;IACxC;EACF;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAACxD,QAAQ,CAACmC,IAAI,CAAC,IAAI,CAAC;EAC1B;EAEQ0C,WAAWA,CAAA;IACjB,IAAI,CAAC7E,QAAQ,CAACmC,IAAI,CAAC,KAAK,CAAC;IACzB,IAAI,CAACT,SAAS,EAAE;EAClB;EAEQJ,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAACnC,iBAAiB,EAAE;MAC1B;IACF;IAEA,IAAI,IAAI,CAAC6C,SAAS,EAAE;MAClB,IAAI,IAAI,CAAC9C,0BAA0B,EAAE;QACnC,IAAI,CAAC+E,QAAQ,GAAG,IAAI,CAAClG,UAAU,CAACiH,aAAa,CAAC,IAAI,CAACf,QAAQ,CAAC;QAC5D,IAAI,CAACjC,SAAS,CAACiC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACzC;MACA,IAAI,CAAClG,UAAU,CAACkH,oBAAoB,CAAC,IAAI,CAACjD,SAAS,CAAC;IACtD;IAEA,IAAI,IAAI,CAAC8B,IAAI,EAAE;MACb,IAAI,CAAC/F,UAAU,CAACmH,eAAe,CAAC,IAAI,CAACpB,IAAI,CAAC;IAC5C;IAEA,IAAI,IAAI,CAAC5F,MAAM,EAAE;MACf,MAAMiH,YAAY,GAAG,IAAI,CAACpH,UAAU,CAACqH,oBAAoB,EAAE;MAC3D,IAAID,YAAY,CAACE,cAAc,CAAC,MAAM,CAAC,EAAE;QACvC,OAAOF,YAAY,CAAC,MAAM,CAAC;MAC7B;MAEA,IAAIG,MAAM,CAACC,IAAI,CAACJ,YAAY,CAAC,CAAC1E,MAAM,EAAE;QACpC,IAAI,CAACxC,YAAY,CAACuH,cAAc,CAAC,IAAI,CAACC,qBAAqB,CAAC,CACzDC,IAAI,CAACC,WAAW,IAAG;UAClB,MAAMC,KAAK,GAAGN,MAAM,CAACC,IAAI,CAACI,WAAW,CAAC,CAACE,MAAM,CAAC,CAACC,GAAQ,EAAEC,GAAG,KAAI;YAC9DD,GAAG,CAACC,GAAG,CAAC,GAAG,IAAI;YAEf,OAAOD,GAAG;UACZ,CAAC,EAAE,EAAE,CAAC;UAEN,IAAI,CAAC5H,MAAM,CAAC8H,YAAY,CAAAC,aAAA,CAAAA,aAAA,KAAOL,KAAK,IAAI,EAAE,GAAOT,YAAY,IAAI,EAAE,CAAG,CAAC;QACzE,CAAC,CAAC,CACDe,KAAK,CAAC,MAAM,IAAI,CAAChI,MAAM,CAAC8H,YAAY,CAACb,YAAY,CAAC,CAAC;QAEtD;MACF;MAEA,IAAI,CAAC,IAAI,CAACM,qBAAqB,EAAE;QAC/B,IAAI,CAACvH,MAAM,CAAC8H,YAAY,CAACb,YAAY,CAAC;MACxC,CAAC,MAAM;QACL,IAAI,CAAClH,YAAY,CAACuH,cAAc,CAAC,IAAI,CAACC,qBAAqB,CAAC,CACzDC,IAAI,CAACC,WAAW,IAAG;UAClB,IAAI,CAACzH,MAAM,CAAC8H,YAAY,CAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAAON,WAAW,IAAI,EAAE,GAAO,IAAI,CAACQ,uBAAuB,IAAI,EAAE,GAAOhB,YAAY,IAAI,EAAE,CAAG,CAAC;QACxH,CAAC,CAAC,CACDe,KAAK,CAAC,MAAM,IAAI,CAAChI,MAAM,CAAC8H,YAAY,CAACb,YAAY,CAAC,CAAC;MACxD;IACF;EACF;EAEQ5D,cAAcA,CAAA;IAAA,IAAA6E,YAAA;IACpB,CAAAA,YAAA,OAAI,CAAClI,MAAM,cAAAkI,YAAA,eAAXA,YAAA,CAAatB,aAAa,CACvB9D,IAAI,CACH1E,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAAC,MAAM,IAAI,CAAC6B,MAAM,CAACmI,eAAe,CAAC,EAC5ClK,YAAY,CAAC,GAAG,CAAC,CAClB,CACA8E,SAAS,CAAC/C,MAAM,IAAG;MAClB,IAAI,IAAI,CAACuH,qBAAqB,EAAE;QAC9B,IAAI,CAACxH,YAAY,CAACqI,cAAc,CAAC,IAAI,CAACb,qBAAqB,EAAEvH,MAAM,CAAC,CACjEgI,KAAK,CAAGK,CAAC,IAAMC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC,CAAC;MACnC;IACF,CAAC,CAAC;EACN;EAEQzF,mBAAmBA,CAAA;IACzB,IAAI,CAAC/C,UAAU,CAAC2I,yBAAyB,CAAC,CAAC,IAAI,CAACvH,iBAAiB,CAAC;EACpE;EAEQyB,aAAaA,CAAA;IACnB,IAAI,CAAC8B,SAAS,GAAG,IAAIvH,cAAc,CAAI,IAAI,EAAE,IAAI,CAACqD,QAAQ,CAAC;IAC3DvC,KAAK,CAAC,IAAI,CAACyG,SAAS,CAACiE,OAAO,CAACC,YAAY,EAAE,EAAE,IAAI,CAACrG,UAAU,CAACqE,OAAO,EAAE,CAAC,CACpE5D,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAChCa,SAAS,CAAC,MAAM,IAAI,CAACZ,4BAA4B,GAAG,IAAI,CAAC;EAC9D;EAEQc,aAAaA,CAAA;IACnB,IAAI,OAAO,IAAI,CAAC8C,QAAQ,KAAK,WAAW,EAAE;MACxC,IAAI,IAAI,CAAC7F,QAAQ,EAAE;QACjB,IAAI,CAACyI,oBAAoB,EAAE;MAC7B,CAAC,MAAM;QACL,IAAI,CAAC5C,QAAQ,GAAGzH,iBAAiB;MACnC;IACF;EACF;EAEA;;;EAGQqK,oBAAoBA,CAAA;IAC1B,IAAI,CAACzI,QAAQ,CAAC0I,YAAY,CACvB9F,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6D,UAAU,CAAC,CAAC,CAChCa,SAAS,CAAC,CAAE;MAAEgD;IAAQ,CAAe,KAAK;MACzC,IAAI,IAAI,CAACjC,SAAS,EAAE;QAClB,IAAIiC,QAAQ,EAAE;UACZ,IAAI,CAAC8C,iBAAiB,CAAC9C,QAAQ,CAAC;QAClC,CAAC,MAAM;UACL,IAAI,CAAC7C,wBAAwB,EAAE;QACjC;MACF;IACF,CAAC,CAAC;EACN;EAEA;;;;;EAKQ2F,iBAAiBA,CAAE9C,QAAgB;IACzC,IAAI,IAAI,CAACA,QAAQ,KAAKA,QAAQ,EAAE;MAC9B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MAExB,IAAI,IAAI,CAACjC,SAAS,EAAE;QAClB,IAAI,CAACwB,WAAW,EAAE;QAClB,IAAI,CAACjD,UAAU,CAACyG,cAAc,CAAC/C,QAAQ,CAAC;MAC1C;IACF;EACF;EAEQvC,SAASA,CAAA;IACf,IAAI,CAAC,IAAI,CAACnC,cAAc,IAAI,CAAC,IAAI,CAACyC,SAAS,IAAI,CAAC,IAAI,CAACzB,UAAU,CAAC7C,IAAI,EAAE;MACpE;IACF;IACA,MAAMuJ,EAAE,GAAG,IAAI,CAACjJ,OAAO,CAACkJ,aAAa,CAACC,sBAAsB,CAAC,2BAA2B,CAAC;IACzF,IAAIF,EAAE,IAAIA,EAAE,CAAC,CAAC,CAAC,EAAE;MACf,IAAI,IAAI,CAAC1G,UAAU,CAAC7C,IAAI,CAAC+C,MAAM,EAAE;QAC/B,MAAM2G,UAAU,GAAG,IAAI,CAACpF,SAAS,CAACqF,SAAS,GAAG,IAAI,CAACrF,SAAS,CAACiC,QAAQ;QACrEgD,EAAE,CAAC,CAAC,CAAC,CAACK,SAAS,GAAG,GAAGF,UAAU,GAAG,CAAC,MAAMA,UAAU,GAAG,IAAI,CAAC7G,UAAU,CAAC7C,IAAI,CAAC+C,MAAM,EAAE;MACrF,CAAC,MAAM;QACLwG,EAAE,CAAC,CAAC,CAAC,CAACK,SAAS,GAAG,OAAO;MAC3B;IACF;EACF;;;;;;;;;;;;;;UAvVG3L;EAAQ;AAAA,G;;;UACRA;EAAQ;AAAA,G;;;UACRA;EAAQ;AAAA,G;;;UACRA;EAAQ;IAAA2I,IAAA,EAAI9I,MAAM;IAAA+L,IAAA,GAAC1K,mBAAmB;EAAA;AAAA,G;;;UACtClB;EAAQ;IAAA2I,IAAA,EAAI9I,MAAM;IAAA+L,IAAA,GAACtK,qCAAqC;EAAA;AAAA,G;;;UACxDtB;EAAQ;IAAA2I,IAAA,EAAI9I,MAAM;IAAA+L,IAAA,GAACrK,uCAAuC;EAAA;AAAA,E;;UAvF5DxB;EAAK;;UAWLA;EAAK;;UAOLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAQLA;EAAK;;UAELE;EAAM;;UAENC,SAAS;IAAA0L,IAAA,GAACzL,YAAY;EAAA;;UACtBD,SAAS;IAAA0L,IAAA,GAACxL,OAAO;EAAA;;AA7DPoB,iBAAiB,GAAAqK,UAAA,EAN7BnM,SAAS,CAAC;EACPoM,QAAQ,EAAE,eAAe;EACzBC,QAAA,EAAAC,oBAAyC;EAEzCC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWzK,iBAAiB,CA+a7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}