{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _CdkFixedSizeVirtualScroll, _ScrollDispatcher, _CdkScrollable, _ViewportRuler, _CdkVirtualScrollable, _CdkVirtualScrollViewport, _CdkVirtualForOf, _CdkVirtualScrollableElement, _CdkVirtualScrollableWindow, _CdkScrollableModule, _ScrollingModule;\nconst _c0 = [\"contentWrapper\"];\nconst _c1 = [\"*\"];\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, inject, NgZone, RendererFactory2, Injectable, ElementRef, Renderer2, ChangeDetectorRef, Injector, afterNextRender, booleanAttribute, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, ViewContainerRef, TemplateRef, IterableDiffers, NgModule } from '@angular/core';\nimport { Subject, of, Observable, Subscription, animationFrameScheduler, asapScheduler, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, startWith, takeUntil, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { g as getRtlScrollAxisType, R as RtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { BidiModule } from './bidi.mjs';\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { b as _VIEW_REPEATER_STRATEGY, A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport { i as isDataSource } from './data-source-D34wiQZj.mjs';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n  /**\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  constructor(itemSize, minBufferPx, maxBufferPx) {\n    _defineProperty(this, \"_scrolledIndexChange\", new Subject());\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    _defineProperty(this, \"scrolledIndexChange\", this._scrolledIndexChange.pipe(distinctUntilChanged()));\n    /** The attached viewport. */\n    _defineProperty(this, \"_viewport\", null);\n    /** The size of the items in the virtually scrolling list. */\n    _defineProperty(this, \"_itemSize\", void 0);\n    /** The minimum amount of buffer rendered beyond the viewport (in pixels). */\n    _defineProperty(this, \"_minBufferPx\", void 0);\n    /** The number of buffer items to render beyond the edge of the viewport (in pixels). */\n    _defineProperty(this, \"_maxBufferPx\", void 0);\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n  }\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n  attach(viewport) {\n    this._viewport = viewport;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** Detaches this scroll strategy from the currently attached viewport. */\n  detach() {\n    this._scrolledIndexChange.complete();\n    this._viewport = null;\n  }\n  /**\n   * Update the item size and buffer size.\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n    if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n    }\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentScrolled() {\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onDataLengthChanged() {\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentRendered() {\n    /* no-op */\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onRenderedOffsetChanged() {\n    /* no-op */\n  }\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n  scrollToIndex(index, behavior) {\n    if (this._viewport) {\n      this._viewport.scrollToOffset(index * this._itemSize, behavior);\n    }\n  }\n  /** Update the viewport's total content size. */\n  _updateTotalContentSize() {\n    if (!this._viewport) {\n      return;\n    }\n    this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n  }\n  /** Update the viewport's rendered range. */\n  _updateRenderedRange() {\n    if (!this._viewport) {\n      return;\n    }\n    const renderedRange = this._viewport.getRenderedRange();\n    const newRange = {\n      start: renderedRange.start,\n      end: renderedRange.end\n    };\n    const viewportSize = this._viewport.getViewportSize();\n    const dataLength = this._viewport.getDataLength();\n    let scrollOffset = this._viewport.measureScrollOffset();\n    // Prevent NaN as result when dividing by zero.\n    let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n    // If user scrolls to the bottom of the list and data changes to a smaller list\n    if (newRange.end > dataLength) {\n      // We have to recalculate the first visible index based on new data length and viewport size.\n      const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n      const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n      // If first visible index changed we must update scroll offset to handle start/end buffers\n      // Current range must also be adjusted to cover the new position (bottom of new list).\n      if (firstVisibleIndex != newVisibleIndex) {\n        firstVisibleIndex = newVisibleIndex;\n        scrollOffset = newVisibleIndex * this._itemSize;\n        newRange.start = Math.floor(firstVisibleIndex);\n      }\n      newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n    }\n    const startBuffer = scrollOffset - newRange.start * this._itemSize;\n    if (startBuffer < this._minBufferPx && newRange.start != 0) {\n      const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n      newRange.start = Math.max(0, newRange.start - expandStart);\n      newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n    } else {\n      const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n      if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n        const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n        if (expandEnd > 0) {\n          newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n          newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n        }\n      }\n    }\n    this._viewport.setRenderedRange(newRange);\n    this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n    this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n  }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n  return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n  constructor() {\n    _defineProperty(this, \"_itemSize\", 20);\n    _defineProperty(this, \"_minBufferPx\", 100);\n    _defineProperty(this, \"_maxBufferPx\", 200);\n    /** The scroll strategy used by this directive. */\n    _defineProperty(this, \"_scrollStrategy\", new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx));\n  }\n  /** The size of the items in the list (in pixels). */\n  get itemSize() {\n    return this._itemSize;\n  }\n  set itemSize(value) {\n    this._itemSize = coerceNumberProperty(value);\n  }\n  /**\n   * The minimum amount of buffer rendered beyond the viewport (in pixels).\n   * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n   */\n  get minBufferPx() {\n    return this._minBufferPx;\n  }\n  set minBufferPx(value) {\n    this._minBufferPx = coerceNumberProperty(value);\n  }\n  /**\n   * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n   */\n  get maxBufferPx() {\n    return this._maxBufferPx;\n  }\n  set maxBufferPx(value) {\n    this._maxBufferPx = coerceNumberProperty(value);\n  }\n  ngOnChanges() {\n    this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n}\n_CdkFixedSizeVirtualScroll = CdkFixedSizeVirtualScroll;\n_defineProperty(CdkFixedSizeVirtualScroll, \"\\u0275fac\", function _CdkFixedSizeVirtualScroll_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkFixedSizeVirtualScroll)();\n});\n_defineProperty(CdkFixedSizeVirtualScroll, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkFixedSizeVirtualScroll,\n  selectors: [[\"cdk-virtual-scroll-viewport\", \"itemSize\", \"\"]],\n  inputs: {\n    itemSize: \"itemSize\",\n    minBufferPx: \"minBufferPx\",\n    maxBufferPx: \"maxBufferPx\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: VIRTUAL_SCROLL_STRATEGY,\n    useFactory: _fixedSizeVirtualScrollStrategyFactory,\n    deps: [forwardRef(() => _CdkFixedSizeVirtualScroll)]\n  }]), i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFixedSizeVirtualScroll, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[itemSize]',\n      providers: [{\n        provide: VIRTUAL_SCROLL_STRATEGY,\n        useFactory: _fixedSizeVirtualScrollStrategyFactory,\n        deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n      }]\n    }]\n  }], null, {\n    itemSize: [{\n      type: Input\n    }],\n    minBufferPx: [{\n      type: Input\n    }],\n    maxBufferPx: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n  constructor() {\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_renderer\", inject(RendererFactory2).createRenderer(null, null));\n    _defineProperty(this, \"_cleanupGlobalListener\", void 0);\n    /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n    _defineProperty(this, \"_scrolled\", new Subject());\n    /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n    _defineProperty(this, \"_scrolledCount\", 0);\n    /**\n     * Map of all the scrollable references that are registered with the service and their\n     * scroll event subscriptions.\n     */\n    _defineProperty(this, \"scrollContainers\", new Map());\n  }\n  /**\n   * Registers a scrollable instance with the service and listens for its scrolled events. When the\n   * scrollable is scrolled, the service emits the event to its scrolled observable.\n   * @param scrollable Scrollable instance to be registered.\n   */\n  register(scrollable) {\n    if (!this.scrollContainers.has(scrollable)) {\n      this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n    }\n  }\n  /**\n   * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n   * @param scrollable Scrollable instance to be deregistered.\n   */\n  deregister(scrollable) {\n    const scrollableReference = this.scrollContainers.get(scrollable);\n    if (scrollableReference) {\n      scrollableReference.unsubscribe();\n      this.scrollContainers.delete(scrollable);\n    }\n  }\n  /**\n   * Returns an observable that emits an event whenever any of the registered Scrollable\n   * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n   * to override the default \"throttle\" time.\n   *\n   * **Note:** in order to avoid hitting change detection for every scroll event,\n   * all of the events emitted from this stream will be run outside the Angular zone.\n   * If you need to update any data bindings as a result of a scroll event, you have\n   * to run the callback using `NgZone.run`.\n   */\n  scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n    if (!this._platform.isBrowser) {\n      return of();\n    }\n    return new Observable(observer => {\n      if (!this._cleanupGlobalListener) {\n        this._cleanupGlobalListener = this._ngZone.runOutsideAngular(() => this._renderer.listen('document', 'scroll', () => this._scrolled.next()));\n      }\n      // In the case of a 0ms delay, use an observable without auditTime\n      // since it does add a perceptible delay in processing overhead.\n      const subscription = auditTimeInMs > 0 ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) : this._scrolled.subscribe(observer);\n      this._scrolledCount++;\n      return () => {\n        subscription.unsubscribe();\n        this._scrolledCount--;\n        if (!this._scrolledCount) {\n          var _this$_cleanupGlobalL;\n          (_this$_cleanupGlobalL = this._cleanupGlobalListener) === null || _this$_cleanupGlobalL === void 0 || _this$_cleanupGlobalL.call(this);\n          this._cleanupGlobalListener = undefined;\n        }\n      };\n    });\n  }\n  ngOnDestroy() {\n    var _this$_cleanupGlobalL2;\n    (_this$_cleanupGlobalL2 = this._cleanupGlobalListener) === null || _this$_cleanupGlobalL2 === void 0 || _this$_cleanupGlobalL2.call(this);\n    this._cleanupGlobalListener = undefined;\n    this.scrollContainers.forEach((_, container) => this.deregister(container));\n    this._scrolled.complete();\n  }\n  /**\n   * Returns an observable that emits whenever any of the\n   * scrollable ancestors of an element are scrolled.\n   * @param elementOrElementRef Element whose ancestors to listen for.\n   * @param auditTimeInMs Time to throttle the scroll events.\n   */\n  ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n    const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n    return this.scrolled(auditTimeInMs).pipe(filter(target => !target || ancestors.indexOf(target) > -1));\n  }\n  /** Returns all registered Scrollables that contain the provided element. */\n  getAncestorScrollContainers(elementOrElementRef) {\n    const scrollingContainers = [];\n    this.scrollContainers.forEach((_subscription, scrollable) => {\n      if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n        scrollingContainers.push(scrollable);\n      }\n    });\n    return scrollingContainers;\n  }\n  /** Returns true if the element is contained within the provided Scrollable. */\n  _scrollableContainsElement(scrollable, elementOrElementRef) {\n    let element = coerceElement(elementOrElementRef);\n    let scrollableElement = scrollable.getElementRef().nativeElement;\n    // Traverse through the element parents until we reach null, checking if any of the elements\n    // are the scrollable's element.\n    do {\n      if (element == scrollableElement) {\n        return true;\n      }\n    } while (element = element.parentElement);\n    return false;\n  }\n}\n_ScrollDispatcher = ScrollDispatcher;\n_defineProperty(ScrollDispatcher, \"\\u0275fac\", function _ScrollDispatcher_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ScrollDispatcher)();\n});\n_defineProperty(ScrollDispatcher, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _ScrollDispatcher,\n  factory: _ScrollDispatcher.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n  constructor() {\n    _defineProperty(this, \"elementRef\", inject(ElementRef));\n    _defineProperty(this, \"scrollDispatcher\", inject(ScrollDispatcher));\n    _defineProperty(this, \"ngZone\", inject(NgZone));\n    _defineProperty(this, \"dir\", inject(Directionality, {\n      optional: true\n    }));\n    _defineProperty(this, \"_scrollElement\", this.elementRef.nativeElement);\n    _defineProperty(this, \"_destroyed\", new Subject());\n    _defineProperty(this, \"_renderer\", inject(Renderer2));\n    _defineProperty(this, \"_cleanupScroll\", void 0);\n    _defineProperty(this, \"_elementScrolled\", new Subject());\n  }\n  ngOnInit() {\n    this._cleanupScroll = this.ngZone.runOutsideAngular(() => this._renderer.listen(this._scrollElement, 'scroll', event => this._elementScrolled.next(event)));\n    this.scrollDispatcher.register(this);\n  }\n  ngOnDestroy() {\n    var _this$_cleanupScroll;\n    (_this$_cleanupScroll = this._cleanupScroll) === null || _this$_cleanupScroll === void 0 || _this$_cleanupScroll.call(this);\n    this._elementScrolled.complete();\n    this.scrollDispatcher.deregister(this);\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Returns observable that emits when a scroll event is fired on the host element. */\n  elementScrolled() {\n    return this._elementScrolled;\n  }\n  /** Gets the ElementRef for the viewport. */\n  getElementRef() {\n    return this.elementRef;\n  }\n  /**\n   * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n   * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param options specified the offsets to scroll to.\n   */\n  scrollTo(options) {\n    const el = this.elementRef.nativeElement;\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    // Rewrite start & end offsets as right or left offsets.\n    if (options.left == null) {\n      options.left = isRtl ? options.end : options.start;\n    }\n    if (options.right == null) {\n      options.right = isRtl ? options.start : options.end;\n    }\n    // Rewrite the bottom offset as a top offset.\n    if (options.bottom != null) {\n      options.top = el.scrollHeight - el.clientHeight - options.bottom;\n    }\n    // Rewrite the right offset as a left offset.\n    if (isRtl && getRtlScrollAxisType() != RtlScrollAxisType.NORMAL) {\n      if (options.left != null) {\n        options.right = el.scrollWidth - el.clientWidth - options.left;\n      }\n      if (getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n        options.left = options.right;\n      } else if (getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n        options.left = options.right ? -options.right : options.right;\n      }\n    } else {\n      if (options.right != null) {\n        options.left = el.scrollWidth - el.clientWidth - options.right;\n      }\n    }\n    this._applyScrollToOptions(options);\n  }\n  _applyScrollToOptions(options) {\n    const el = this.elementRef.nativeElement;\n    if (supportsScrollBehavior()) {\n      el.scrollTo(options);\n    } else {\n      if (options.top != null) {\n        el.scrollTop = options.top;\n      }\n      if (options.left != null) {\n        el.scrollLeft = options.left;\n      }\n    }\n  }\n  /**\n   * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n   * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n   * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param from The edge to measure from.\n   */\n  measureScrollOffset(from) {\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const el = this.elementRef.nativeElement;\n    if (from == 'top') {\n      return el.scrollTop;\n    }\n    if (from == 'bottom') {\n      return el.scrollHeight - el.clientHeight - el.scrollTop;\n    }\n    // Rewrite start & end as left or right offsets.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    if (from == 'start') {\n      from = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      from = isRtl ? LEFT : RIGHT;\n    }\n    if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n      // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      } else {\n        return el.scrollLeft;\n      }\n    } else if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n      // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft + el.scrollWidth - el.clientWidth;\n      } else {\n        return -el.scrollLeft;\n      }\n    } else {\n      // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n      // (scrollWidth - clientWidth) when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft;\n      } else {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      }\n    }\n  }\n}\n_CdkScrollable = CdkScrollable;\n_defineProperty(CdkScrollable, \"\\u0275fac\", function _CdkScrollable_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkScrollable)();\n});\n_defineProperty(CdkScrollable, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkScrollable,\n  selectors: [[\"\", \"cdk-scrollable\", \"\"], [\"\", \"cdkScrollable\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollable, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-scrollable], [cdkScrollable]'\n    }]\n  }], () => [], null);\n})();\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n  constructor() {\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_listeners\", void 0);\n    /** Cached viewport dimensions. */\n    _defineProperty(this, \"_viewportSize\", void 0);\n    /** Stream of viewport change events. */\n    _defineProperty(this, \"_change\", new Subject());\n    /** Used to reference correct document/window */\n    _defineProperty(this, \"_document\", inject(DOCUMENT, {\n      optional: true\n    }));\n    const ngZone = inject(NgZone);\n    const renderer = inject(RendererFactory2).createRenderer(null, null);\n    ngZone.runOutsideAngular(() => {\n      if (this._platform.isBrowser) {\n        const changeListener = event => this._change.next(event);\n        this._listeners = [renderer.listen('window', 'resize', changeListener), renderer.listen('window', 'orientationchange', changeListener)];\n      }\n      // Clear the cached position so that the viewport is re-measured next time it is required.\n      // We don't need to keep track of the subscription, because it is completed on destroy.\n      this.change().subscribe(() => this._viewportSize = null);\n    });\n  }\n  ngOnDestroy() {\n    var _this$_listeners;\n    (_this$_listeners = this._listeners) === null || _this$_listeners === void 0 || _this$_listeners.forEach(cleanup => cleanup());\n    this._change.complete();\n  }\n  /** Returns the viewport's width and height. */\n  getViewportSize() {\n    if (!this._viewportSize) {\n      this._updateViewportSize();\n    }\n    const output = {\n      width: this._viewportSize.width,\n      height: this._viewportSize.height\n    };\n    // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n    if (!this._platform.isBrowser) {\n      this._viewportSize = null;\n    }\n    return output;\n  }\n  /** Gets a DOMRect for the viewport's bounds. */\n  getViewportRect() {\n    // Use the document element's bounding rect rather than the window scroll properties\n    // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n    // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n    // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n    // can disagree when the page is pinch-zoomed (on devices that support touch).\n    // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n    // We use the documentElement instead of the body because, by default (without a css reset)\n    // browsers typically give the document body an 8px margin, which is not included in\n    // getBoundingClientRect().\n    const scrollPosition = this.getViewportScrollPosition();\n    const {\n      width,\n      height\n    } = this.getViewportSize();\n    return {\n      top: scrollPosition.top,\n      left: scrollPosition.left,\n      bottom: scrollPosition.top + height,\n      right: scrollPosition.left + width,\n      height,\n      width\n    };\n  }\n  /** Gets the (top, left) scroll position of the viewport. */\n  getViewportScrollPosition() {\n    // While we can get a reference to the fake document\n    // during SSR, it doesn't have getBoundingClientRect.\n    if (!this._platform.isBrowser) {\n      return {\n        top: 0,\n        left: 0\n      };\n    }\n    // The top-left-corner of the viewport is determined by the scroll position of the document\n    // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n    // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n    // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n    // `document.documentElement` works consistently, where the `top` and `left` values will\n    // equal negative the scroll position.\n    const document = this._document;\n    const window = this._getWindow();\n    const documentElement = document.documentElement;\n    const documentRect = documentElement.getBoundingClientRect();\n    const top = -documentRect.top || document.body.scrollTop || window.scrollY || documentElement.scrollTop || 0;\n    const left = -documentRect.left || document.body.scrollLeft || window.scrollX || documentElement.scrollLeft || 0;\n    return {\n      top,\n      left\n    };\n  }\n  /**\n   * Returns a stream that emits whenever the size of the viewport changes.\n   * This stream emits outside of the Angular zone.\n   * @param throttleTime Time in milliseconds to throttle the stream.\n   */\n  change(throttleTime = DEFAULT_RESIZE_TIME) {\n    return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n  /** Updates the cached viewport size. */\n  _updateViewportSize() {\n    const window = this._getWindow();\n    this._viewportSize = this._platform.isBrowser ? {\n      width: window.innerWidth,\n      height: window.innerHeight\n    } : {\n      width: 0,\n      height: 0\n    };\n  }\n}\n_ViewportRuler = ViewportRuler;\n_defineProperty(ViewportRuler, \"\\u0275fac\", function _ViewportRuler_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ViewportRuler)();\n});\n_defineProperty(ViewportRuler, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _ViewportRuler,\n  factory: _ViewportRuler.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ViewportRuler, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n  constructor() {\n    super();\n  }\n  /**\n   * Measure the viewport size for the provided orientation.\n   *\n   * @param orientation The orientation to measure the size from.\n   */\n  measureViewportSize(orientation) {\n    const viewportEl = this.elementRef.nativeElement;\n    return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n  }\n}\n_CdkVirtualScrollable = CdkVirtualScrollable;\n_defineProperty(CdkVirtualScrollable, \"\\u0275fac\", function _CdkVirtualScrollable_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkVirtualScrollable)();\n});\n_defineProperty(CdkVirtualScrollable, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkVirtualScrollable,\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollable, [{\n    type: Directive\n  }], () => [], null);\n})();\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n  return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n  /** The direction the viewport scrolls. */\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(orientation) {\n    if (this._orientation !== orientation) {\n      this._orientation = orientation;\n      this._calculateSpacerSize();\n    }\n  }\n  constructor() {\n    super();\n    _defineProperty(this, \"elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_scrollStrategy\", inject(VIRTUAL_SCROLL_STRATEGY, {\n      optional: true\n    }));\n    _defineProperty(this, \"scrollable\", inject(VIRTUAL_SCROLLABLE, {\n      optional: true\n    }));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    /** Emits when the viewport is detached from a CdkVirtualForOf. */\n    _defineProperty(this, \"_detachedSubject\", new Subject());\n    /** Emits when the rendered range changes. */\n    _defineProperty(this, \"_renderedRangeSubject\", new Subject());\n    _defineProperty(this, \"_orientation\", 'vertical');\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n    _defineProperty(this, \"appendOnly\", false);\n    // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n    // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n    // depending on how the strategy calculates the scrolled index, it may come at a cost to\n    // performance.\n    /** Emits when the index of the first element visible in the viewport changes. */\n    _defineProperty(this, \"scrolledIndexChange\", new Observable(observer => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index))))));\n    /** The element that wraps the rendered content. */\n    _defineProperty(this, \"_contentWrapper\", void 0);\n    /** A stream that emits whenever the rendered range changes. */\n    _defineProperty(this, \"renderedRangeStream\", this._renderedRangeSubject);\n    /**\n     * The total size of all content (in pixels), including content that is not currently rendered.\n     */\n    _defineProperty(this, \"_totalContentSize\", 0);\n    /** A string representing the `style.width` property value to be used for the spacer element. */\n    _defineProperty(this, \"_totalContentWidth\", '');\n    /** A string representing the `style.height` property value to be used for the spacer element. */\n    _defineProperty(this, \"_totalContentHeight\", '');\n    /**\n     * The CSS transform applied to the rendered subset of items so that they appear within the bounds\n     * of the visible viewport.\n     */\n    _defineProperty(this, \"_renderedContentTransform\", void 0);\n    /** The currently rendered range of indices. */\n    _defineProperty(this, \"_renderedRange\", {\n      start: 0,\n      end: 0\n    });\n    /** The length of the data bound to this viewport (in number of items). */\n    _defineProperty(this, \"_dataLength\", 0);\n    /** The size of the viewport (in pixels). */\n    _defineProperty(this, \"_viewportSize\", 0);\n    /** the currently attached CdkVirtualScrollRepeater. */\n    _defineProperty(this, \"_forOf\", void 0);\n    /** The last rendered content offset that was set. */\n    _defineProperty(this, \"_renderedContentOffset\", 0);\n    /**\n     * Whether the last rendered content offset was to the end of the content (and therefore needs to\n     * be rewritten as an offset to the start of the content).\n     */\n    _defineProperty(this, \"_renderedContentOffsetNeedsRewrite\", false);\n    /** Whether there is a pending change detection cycle. */\n    _defineProperty(this, \"_isChangeDetectionPending\", false);\n    /** A list of functions to run after the next change detection cycle. */\n    _defineProperty(this, \"_runAfterChangeDetection\", []);\n    /** Subscription to changes in the viewport size. */\n    _defineProperty(this, \"_viewportChanges\", Subscription.EMPTY);\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_isDestroyed\", false);\n    const viewportRuler = inject(ViewportRuler);\n    if (!this._scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n    }\n    this._viewportChanges = viewportRuler.change().subscribe(() => {\n      this.checkViewportSize();\n    });\n    if (!this.scrollable) {\n      // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n      this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n      this.scrollable = this;\n    }\n  }\n  ngOnInit() {\n    // Scrolling depends on the element dimensions which we can't get during SSR.\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    if (this.scrollable === this) {\n      super.ngOnInit();\n    }\n    // It's still too early to measure the viewport at this point. Deferring with a promise allows\n    // the Viewport to be rendered with the correct size before we measure. We run this outside the\n    // zone to avoid causing more change detection cycles. We handle the change detection loop\n    // ourselves instead.\n    this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n      this._measureViewportSize();\n      this._scrollStrategy.attach(this);\n      this.scrollable.elementScrolled().pipe(\n      // Start off with a fake scroll event so we properly detect our initial position.\n      startWith(null),\n      // Collect multiple events into one until the next animation frame. This way if\n      // there are multiple scroll events in the same frame we only need to recheck\n      // our layout once.\n      auditTime(0, SCROLL_SCHEDULER),\n      // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n      // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n      // to unsubscribe here just in case.\n      takeUntil(this._destroyed)).subscribe(() => this._scrollStrategy.onContentScrolled());\n      this._markChangeDetectionNeeded();\n    }));\n  }\n  ngOnDestroy() {\n    this.detach();\n    this._scrollStrategy.detach();\n    // Complete all subjects\n    this._renderedRangeSubject.complete();\n    this._detachedSubject.complete();\n    this._viewportChanges.unsubscribe();\n    this._isDestroyed = true;\n    super.ngOnDestroy();\n  }\n  /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n  attach(forOf) {\n    if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CdkVirtualScrollViewport is already attached.');\n    }\n    // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n    // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n    // change detection loop ourselves.\n    this.ngZone.runOutsideAngular(() => {\n      this._forOf = forOf;\n      this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n        const newLength = data.length;\n        if (newLength !== this._dataLength) {\n          this._dataLength = newLength;\n          this._scrollStrategy.onDataLengthChanged();\n        }\n        this._doChangeDetection();\n      });\n    });\n  }\n  /** Detaches the current `CdkVirtualForOf`. */\n  detach() {\n    this._forOf = null;\n    this._detachedSubject.next();\n  }\n  /** Gets the length of the data bound to this viewport (in number of items). */\n  getDataLength() {\n    return this._dataLength;\n  }\n  /** Gets the size of the viewport (in pixels). */\n  getViewportSize() {\n    return this._viewportSize;\n  }\n  // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n  // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n  // setting it to something else, but its error prone and should probably be split into\n  // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n  /** Get the current rendered range of items. */\n  getRenderedRange() {\n    return this._renderedRange;\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n  }\n  /**\n   * Sets the total size of all content (in pixels), including content that is not currently\n   * rendered.\n   */\n  setTotalContentSize(size) {\n    if (this._totalContentSize !== size) {\n      this._totalContentSize = size;\n      this._calculateSpacerSize();\n      this._markChangeDetectionNeeded();\n    }\n  }\n  /** Sets the currently rendered range of indices. */\n  setRenderedRange(range) {\n    if (!rangesEqual(this._renderedRange, range)) {\n      if (this.appendOnly) {\n        range = {\n          start: 0,\n          end: Math.max(this._renderedRange.end, range.end)\n        };\n      }\n      this._renderedRangeSubject.next(this._renderedRange = range);\n      this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n    }\n  }\n  /**\n   * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n   */\n  getOffsetToRenderedContentStart() {\n    return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n  }\n  /**\n   * Sets the offset from the start of the viewport to either the start or end of the rendered data\n   * (in pixels).\n   */\n  setRenderedContentOffset(offset, to = 'to-start') {\n    // In appendOnly, we always start from the top\n    offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n    // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n    // in the negative direction.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    const isHorizontal = this.orientation == 'horizontal';\n    const axis = isHorizontal ? 'X' : 'Y';\n    const axisDirection = isHorizontal && isRtl ? -1 : 1;\n    let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n    this._renderedContentOffset = offset;\n    if (to === 'to-end') {\n      transform += ` translate${axis}(-100%)`;\n      // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n      // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n      // expand upward).\n      this._renderedContentOffsetNeedsRewrite = true;\n    }\n    if (this._renderedContentTransform != transform) {\n      // We know this value is safe because we parse `offset` with `Number()` before passing it\n      // into the string.\n      this._renderedContentTransform = transform;\n      this._markChangeDetectionNeeded(() => {\n        if (this._renderedContentOffsetNeedsRewrite) {\n          this._renderedContentOffset -= this.measureRenderedContentSize();\n          this._renderedContentOffsetNeedsRewrite = false;\n          this.setRenderedContentOffset(this._renderedContentOffset);\n        } else {\n          this._scrollStrategy.onRenderedOffsetChanged();\n        }\n      });\n    }\n  }\n  /**\n   * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n   * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n   * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n   * @param offset The offset to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n  scrollToOffset(offset, behavior = 'auto') {\n    const options = {\n      behavior\n    };\n    if (this.orientation === 'horizontal') {\n      options.start = offset;\n    } else {\n      options.top = offset;\n    }\n    this.scrollable.scrollTo(options);\n  }\n  /**\n   * Scrolls to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n  scrollToIndex(index, behavior = 'auto') {\n    this._scrollStrategy.scrollToIndex(index, behavior);\n  }\n  /**\n   * Gets the current scroll offset from the start of the scrollable (in pixels).\n   * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n   *     in horizontal mode.\n   */\n  measureScrollOffset(from) {\n    // This is to break the call cycle\n    let measureScrollOffset;\n    if (this.scrollable == this) {\n      measureScrollOffset = _from => super.measureScrollOffset(_from);\n    } else {\n      measureScrollOffset = _from => this.scrollable.measureScrollOffset(_from);\n    }\n    return Math.max(0, measureScrollOffset(from !== null && from !== void 0 ? from : this.orientation === 'horizontal' ? 'start' : 'top') - this.measureViewportOffset());\n  }\n  /**\n   * Measures the offset of the viewport from the scrolling container\n   * @param from The edge to measure from.\n   */\n  measureViewportOffset(from) {\n    var _this$dir;\n    let fromRect;\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const isRtl = ((_this$dir = this.dir) === null || _this$dir === void 0 ? void 0 : _this$dir.value) == 'rtl';\n    if (from == 'start') {\n      fromRect = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      fromRect = isRtl ? LEFT : RIGHT;\n    } else if (from) {\n      fromRect = from;\n    } else {\n      fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n    }\n    const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n    const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n    return viewportClientRect - scrollerClientRect;\n  }\n  /** Measure the combined size of all of the rendered items. */\n  measureRenderedContentSize() {\n    const contentEl = this._contentWrapper.nativeElement;\n    return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n  }\n  /**\n   * Measure the total combined size of the given range. Throws if the range includes items that are\n   * not rendered.\n   */\n  measureRangeSize(range) {\n    if (!this._forOf) {\n      return 0;\n    }\n    return this._forOf.measureRangeSize(range, this.orientation);\n  }\n  /** Update the viewport dimensions and re-render. */\n  checkViewportSize() {\n    // TODO: Cleanup later when add logic for handling content resize\n    this._measureViewportSize();\n    this._scrollStrategy.onDataLengthChanged();\n  }\n  /** Measure the viewport size. */\n  _measureViewportSize() {\n    this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n  }\n  /** Queue up change detection to run. */\n  _markChangeDetectionNeeded(runAfter) {\n    if (runAfter) {\n      this._runAfterChangeDetection.push(runAfter);\n    }\n    // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n    // properties sequentially we only have to run `_doChangeDetection` once at the end.\n    if (!this._isChangeDetectionPending) {\n      this._isChangeDetectionPending = true;\n      this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n        this._doChangeDetection();\n      }));\n    }\n  }\n  /** Run change detection. */\n  _doChangeDetection() {\n    if (this._isDestroyed) {\n      return;\n    }\n    this.ngZone.run(() => {\n      // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n      // from the root, since the repeated items are content projected in. Calling `detectChanges`\n      // instead does not properly check the projected content.\n      this._changeDetectorRef.markForCheck();\n      // Apply the content transform. The transform can't be set via an Angular binding because\n      // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n      // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n      // the `Number` function first to coerce it to a numeric value.\n      this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n      afterNextRender(() => {\n        this._isChangeDetectionPending = false;\n        const runAfterChangeDetection = this._runAfterChangeDetection;\n        this._runAfterChangeDetection = [];\n        for (const fn of runAfterChangeDetection) {\n          fn();\n        }\n      }, {\n        injector: this._injector\n      });\n    });\n  }\n  /** Calculates the `style.width` and `style.height` for the spacer element. */\n  _calculateSpacerSize() {\n    this._totalContentHeight = this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n    this._totalContentWidth = this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n  }\n}\n_CdkVirtualScrollViewport = CdkVirtualScrollViewport;\n_defineProperty(CdkVirtualScrollViewport, \"\\u0275fac\", function _CdkVirtualScrollViewport_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkVirtualScrollViewport)();\n});\n_defineProperty(CdkVirtualScrollViewport, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _CdkVirtualScrollViewport,\n  selectors: [[\"cdk-virtual-scroll-viewport\"]],\n  viewQuery: function _CdkVirtualScrollViewport_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentWrapper = _t.first);\n    }\n  },\n  hostAttrs: [1, \"cdk-virtual-scroll-viewport\"],\n  hostVars: 4,\n  hostBindings: function _CdkVirtualScrollViewport_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cdk-virtual-scroll-orientation-horizontal\", ctx.orientation === \"horizontal\")(\"cdk-virtual-scroll-orientation-vertical\", ctx.orientation !== \"horizontal\");\n    }\n  },\n  inputs: {\n    orientation: \"orientation\",\n    appendOnly: [2, \"appendOnly\", \"appendOnly\", booleanAttribute]\n  },\n  outputs: {\n    scrolledIndexChange: \"scrolledIndexChange\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkScrollable,\n    useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n    deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], _CdkVirtualScrollViewport]\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 4,\n  consts: [[\"contentWrapper\", \"\"], [1, \"cdk-virtual-scroll-content-wrapper\"], [1, \"cdk-virtual-scroll-spacer\"]],\n  template: function _CdkVirtualScrollViewport_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 1, 0);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(3, \"div\", 2);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵstyleProp(\"width\", ctx._totalContentWidth)(\"height\", ctx._totalContentHeight);\n    }\n  },\n  styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollViewport, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport',\n      host: {\n        'class': 'cdk-virtual-scroll-viewport',\n        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: CdkScrollable,\n        useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n        deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport]\n      }],\n      template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\",\n      styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"]\n    }]\n  }], () => [], {\n    orientation: [{\n      type: Input\n    }],\n    appendOnly: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    scrolledIndexChange: [{\n      type: Output\n    }],\n    _contentWrapper: [{\n      type: ViewChild,\n      args: ['contentWrapper', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n  const el = node;\n  if (!el.getBoundingClientRect) {\n    return 0;\n  }\n  const rect = el.getBoundingClientRect();\n  if (orientation === 'horizontal') {\n    return direction === 'start' ? rect.left : rect.right;\n  }\n  return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n  /** The DataSource to display. */\n  get cdkVirtualForOf() {\n    return this._cdkVirtualForOf;\n  }\n  set cdkVirtualForOf(value) {\n    this._cdkVirtualForOf = value;\n    if (isDataSource(value)) {\n      this._dataSourceChanges.next(value);\n    } else {\n      // If value is an an NgIterable, convert it to an array.\n      this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n    }\n  }\n  /**\n   * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n   * the item and produces a value to be used as the item's identity when tracking changes.\n   */\n  get cdkVirtualForTrackBy() {\n    return this._cdkVirtualForTrackBy;\n  }\n  set cdkVirtualForTrackBy(fn) {\n    this._needsUpdate = true;\n    this._cdkVirtualForTrackBy = fn ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item) : undefined;\n  }\n  /** The template used to stamp out new elements. */\n  set cdkVirtualForTemplate(value) {\n    if (value) {\n      this._needsUpdate = true;\n      this._template = value;\n    }\n  }\n  /**\n   * The size of the cache used to store templates that are not being used for re-use later.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n   */\n  get cdkVirtualForTemplateCacheSize() {\n    return this._viewRepeater.viewCacheSize;\n  }\n  set cdkVirtualForTemplateCacheSize(size) {\n    this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n  }\n  /** Emits whenever the data in the current DataSource changes. */\n\n  constructor() {\n    _defineProperty(this, \"_viewContainerRef\", inject(ViewContainerRef));\n    _defineProperty(this, \"_template\", inject(TemplateRef));\n    _defineProperty(this, \"_differs\", inject(IterableDiffers));\n    _defineProperty(this, \"_viewRepeater\", inject(_VIEW_REPEATER_STRATEGY));\n    _defineProperty(this, \"_viewport\", inject(CdkVirtualScrollViewport, {\n      skipSelf: true\n    }));\n    /** Emits when the rendered view of the data changes. */\n    _defineProperty(this, \"viewChange\", new Subject());\n    /** Subject that emits when a new DataSource instance is given. */\n    _defineProperty(this, \"_dataSourceChanges\", new Subject());\n    _defineProperty(this, \"_cdkVirtualForOf\", void 0);\n    _defineProperty(this, \"_cdkVirtualForTrackBy\", void 0);\n    _defineProperty(this, \"dataStream\", this._dataSourceChanges.pipe(\n    // Start off with null `DataSource`.\n    startWith(null),\n    // Bundle up the previous and current data sources so we can work with both.\n    pairwise(),\n    // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n    // new one, passing back a stream of data changes which we run through `switchMap` to give\n    // us a data stream that emits the latest data from whatever the current `DataSource` is.\n    switchMap(([prev, cur]) => this._changeDataSource(prev, cur)),\n    // Replay the last emitted data when someone subscribes.\n    shareReplay(1)));\n    /** The differ used to calculate changes to the data. */\n    _defineProperty(this, \"_differ\", null);\n    /** The most recent data emitted from the DataSource. */\n    _defineProperty(this, \"_data\", void 0);\n    /** The currently rendered items. */\n    _defineProperty(this, \"_renderedItems\", void 0);\n    /** The currently rendered range of indices. */\n    _defineProperty(this, \"_renderedRange\", void 0);\n    /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n    _defineProperty(this, \"_needsUpdate\", false);\n    _defineProperty(this, \"_destroyed\", new Subject());\n    const ngZone = inject(NgZone);\n    this.dataStream.subscribe(data => {\n      this._data = data;\n      this._onRenderedDataChange();\n    });\n    this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n      this._renderedRange = range;\n      if (this.viewChange.observers.length) {\n        ngZone.run(() => this.viewChange.next(this._renderedRange));\n      }\n      this._onRenderedDataChange();\n    });\n    this._viewport.attach(this);\n  }\n  /**\n   * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n   * in the specified range. Throws an error if the range includes items that are not currently\n   * rendered.\n   */\n  measureRangeSize(range, orientation) {\n    if (range.start >= range.end) {\n      return 0;\n    }\n    if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Error: attempted to measure an item that isn't rendered.`);\n    }\n    // The index into the list of rendered views for the first item in the range.\n    const renderedStartIndex = range.start - this._renderedRange.start;\n    // The length of the range we're measuring.\n    const rangeLen = range.end - range.start;\n    // Loop over all the views, find the first and land node and compute the size by subtracting\n    // the top of the first node from the bottom of the last one.\n    let firstNode;\n    let lastNode;\n    // Find the first node by starting from the beginning and going forwards.\n    for (let i = 0; i < rangeLen; i++) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n      if (view && view.rootNodes.length) {\n        firstNode = lastNode = view.rootNodes[0];\n        break;\n      }\n    }\n    // Find the last node by starting from the end and going backwards.\n    for (let i = rangeLen - 1; i > -1; i--) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n      if (view && view.rootNodes.length) {\n        lastNode = view.rootNodes[view.rootNodes.length - 1];\n        break;\n      }\n    }\n    return firstNode && lastNode ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode) : 0;\n  }\n  ngDoCheck() {\n    if (this._differ && this._needsUpdate) {\n      // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n      // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n      // changing (need to do this diff).\n      const changes = this._differ.diff(this._renderedItems);\n      if (!changes) {\n        this._updateContext();\n      } else {\n        this._applyChanges(changes);\n      }\n      this._needsUpdate = false;\n    }\n  }\n  ngOnDestroy() {\n    this._viewport.detach();\n    this._dataSourceChanges.next(undefined);\n    this._dataSourceChanges.complete();\n    this.viewChange.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._viewRepeater.detach();\n  }\n  /** React to scroll state changes in the viewport. */\n  _onRenderedDataChange() {\n    if (!this._renderedRange) {\n      return;\n    }\n    this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n    if (!this._differ) {\n      // Use a wrapper function for the `trackBy` so any new values are\n      // picked up automatically without having to recreate the differ.\n      this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n        return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n      });\n    }\n    this._needsUpdate = true;\n  }\n  /** Swap out one `DataSource` for another. */\n  _changeDataSource(oldDs, newDs) {\n    if (oldDs) {\n      oldDs.disconnect(this);\n    }\n    this._needsUpdate = true;\n    return newDs ? newDs.connect(this) : of();\n  }\n  /** Update the `CdkVirtualForOfContext` for all views. */\n  _updateContext() {\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n      this._updateComputedContextProperties(view.context);\n      view.detectChanges();\n    }\n  }\n  /** Apply changes to the DOM. */\n  _applyChanges(changes) {\n    this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n    // Update $implicit for any items that had an identity change.\n    changes.forEachIdentityChange(record => {\n      const view = this._viewContainerRef.get(record.currentIndex);\n      view.context.$implicit = record.item;\n    });\n    // Update the context variables on all items.\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n      this._updateComputedContextProperties(view.context);\n    }\n  }\n  /** Update the computed properties on the `CdkVirtualForOfContext`. */\n  _updateComputedContextProperties(context) {\n    context.first = context.index === 0;\n    context.last = context.index === context.count - 1;\n    context.even = context.index % 2 === 0;\n    context.odd = !context.even;\n  }\n  _getEmbeddedViewArgs(record, index) {\n    // Note that it's important that we insert the item directly at the proper index,\n    // rather than inserting it and the moving it in place, because if there's a directive\n    // on the same node that injects the `ViewContainerRef`, Angular will insert another\n    // comment node which can throw off the move when it's being repeated for all items.\n    return {\n      templateRef: this._template,\n      context: {\n        $implicit: record.item,\n        // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n        // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n        cdkVirtualForOf: this._cdkVirtualForOf,\n        index: -1,\n        count: -1,\n        first: false,\n        last: false,\n        odd: false,\n        even: false\n      },\n      index\n    };\n  }\n  static ngTemplateContextGuard(directive, context) {\n    return true;\n  }\n}\n_CdkVirtualForOf = CdkVirtualForOf;\n_defineProperty(CdkVirtualForOf, \"\\u0275fac\", function _CdkVirtualForOf_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkVirtualForOf)();\n});\n_defineProperty(CdkVirtualForOf, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkVirtualForOf,\n  selectors: [[\"\", \"cdkVirtualFor\", \"\", \"cdkVirtualForOf\", \"\"]],\n  inputs: {\n    cdkVirtualForOf: \"cdkVirtualForOf\",\n    cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\",\n    cdkVirtualForTemplate: \"cdkVirtualForTemplate\",\n    cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: _VIEW_REPEATER_STRATEGY,\n    useClass: _RecycleViewRepeaterStrategy\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualForOf, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkVirtualFor][cdkVirtualForOf]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], () => [], {\n    cdkVirtualForOf: [{\n      type: Input\n    }],\n    cdkVirtualForTrackBy: [{\n      type: Input\n    }],\n    cdkVirtualForTemplate: [{\n      type: Input\n    }],\n    cdkVirtualForTemplateCacheSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n  constructor() {\n    super();\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from] - this.measureScrollOffset(from);\n  }\n}\n_CdkVirtualScrollableElement = CdkVirtualScrollableElement;\n_defineProperty(CdkVirtualScrollableElement, \"\\u0275fac\", function _CdkVirtualScrollableElement_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkVirtualScrollableElement)();\n});\n_defineProperty(CdkVirtualScrollableElement, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkVirtualScrollableElement,\n  selectors: [[\"\", \"cdkVirtualScrollingElement\", \"\"]],\n  hostAttrs: [1, \"cdk-virtual-scrollable\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: VIRTUAL_SCROLLABLE,\n    useExisting: _CdkVirtualScrollableElement\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollableElement, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkVirtualScrollingElement]',\n      providers: [{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableElement\n      }],\n      host: {\n        'class': 'cdk-virtual-scrollable'\n      }\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n  constructor() {\n    super();\n    const document = inject(DOCUMENT);\n    this.elementRef = new ElementRef(document.documentElement);\n    this._scrollElement = document;\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n  }\n}\n_CdkVirtualScrollableWindow = CdkVirtualScrollableWindow;\n_defineProperty(CdkVirtualScrollableWindow, \"\\u0275fac\", function _CdkVirtualScrollableWindow_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkVirtualScrollableWindow)();\n});\n_defineProperty(CdkVirtualScrollableWindow, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkVirtualScrollableWindow,\n  selectors: [[\"cdk-virtual-scroll-viewport\", \"scrollWindow\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: VIRTUAL_SCROLLABLE,\n    useExisting: _CdkVirtualScrollableWindow\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollableWindow, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n      providers: [{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableWindow\n      }]\n    }]\n  }], () => [], null);\n})();\nclass CdkScrollableModule {}\n_CdkScrollableModule = CdkScrollableModule;\n_defineProperty(CdkScrollableModule, \"\\u0275fac\", function _CdkScrollableModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkScrollableModule)();\n});\n_defineProperty(CdkScrollableModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _CdkScrollableModule,\n  imports: [CdkScrollable],\n  exports: [CdkScrollable]\n}));\n_defineProperty(CdkScrollableModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollableModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkScrollable],\n      imports: [CdkScrollable]\n    }]\n  }], null, null);\n})();\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {}\n_ScrollingModule = ScrollingModule;\n_defineProperty(ScrollingModule, \"\\u0275fac\", function _ScrollingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ScrollingModule)();\n});\n_defineProperty(ScrollingModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _ScrollingModule,\n  imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollableWindow, CdkVirtualScrollableElement],\n  exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollableWindow, CdkVirtualScrollableElement]\n}));\n_defineProperty(ScrollingModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [BidiModule, CdkScrollableModule, BidiModule, CdkScrollableModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollableWindow, CdkVirtualScrollableElement],\n      exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollableWindow, CdkVirtualScrollableElement]\n    }]\n  }], null, null);\n})();\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };\n//# sourceMappingURL=scrolling.mjs.map", "map": {"version": 3, "names": ["i0", "InjectionToken", "forwardRef", "Directive", "Input", "inject", "NgZone", "RendererFactory2", "Injectable", "ElementRef", "Renderer2", "ChangeDetectorRef", "Injector", "afterNextRender", "booleanAttribute", "Optional", "Inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Output", "ViewChild", "ViewContainerRef", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "NgModule", "Subject", "of", "Observable", "Subscription", "animationFrameScheduler", "asapScheduler", "isObservable", "distinctUntilChanged", "auditTime", "filter", "startWith", "takeUntil", "pairwise", "switchMap", "shareReplay", "c", "coerceNumberProperty", "a", "coerceElement", "P", "Platform", "D", "Directionality", "g", "getRtlScrollAxisType", "R", "RtlScrollAxisType", "s", "supportsScrollBehavior", "BidiModule", "<PERSON><PERSON>", "ɵɵDir", "DOCUMENT", "b", "_VIEW_REPEATER_STRATEGY", "A", "ArrayDataSource", "_", "_RecycleViewRepeaterStrategy", "i", "isDataSource", "VIRTUAL_SCROLL_STRATEGY", "FixedSizeVirtualScrollStrategy", "constructor", "itemSize", "minBufferPx", "maxBufferPx", "_defineProperty", "_scrolledIndexChange", "pipe", "_itemSize", "_minBufferPx", "_maxBufferPx", "attach", "viewport", "_viewport", "_updateTotalContentSize", "_updateRenderedRange", "detach", "complete", "updateItemAndBufferSize", "ngDevMode", "Error", "onContentScrolled", "onDataLengthChanged", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "onRenderedOffsetChanged", "scrollToIndex", "index", "behavior", "scrollToOffset", "setTotalContentSize", "getDataLength", "renderedRange", "getRenderedRange", "newRange", "start", "end", "viewportSize", "getViewportSize", "dataLength", "scrollOffset", "measureScrollOffset", "firstVisibleIndex", "maxVisibleItems", "Math", "ceil", "newVisibleIndex", "max", "min", "floor", "startBuffer", "expandStart", "end<PERSON><PERSON><PERSON>", "expandEnd", "setR<PERSON>edRange", "setRenderedContentOffset", "next", "_fixedSizeVirtualScrollStrategyFactory", "fixedSizeDir", "_scrollStrategy", "CdkFixedSizeVirtualScroll", "value", "ngOnChanges", "_CdkFixedSizeVirtualScroll", "_CdkFixedSizeVirtualScroll_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "inputs", "features", "ɵɵProvidersFeature", "provide", "useFactory", "deps", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "args", "selector", "providers", "DEFAULT_SCROLL_TIME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Map", "register", "scrollable", "scrollContainers", "has", "set", "elementScrolled", "subscribe", "_scrolled", "deregister", "scrollableReference", "get", "unsubscribe", "delete", "scrolled", "auditTimeInMs", "_platform", "<PERSON><PERSON><PERSON><PERSON>", "observer", "_cleanupGlobalListener", "_ngZone", "runOutsideAngular", "_renderer", "listen", "subscription", "_scrolledCount", "_this$_cleanupGlobalL", "call", "undefined", "ngOnDestroy", "_this$_cleanupGlobalL2", "for<PERSON>ach", "container", "ancestorScrolled", "elementOrElementRef", "ancestors", "getAncestorScrollContainers", "target", "indexOf", "scrollingContainers", "_subscription", "_scrollableContainsElement", "push", "element", "scrollableElement", "getElementRef", "nativeElement", "parentElement", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er", "_ScrollDispatcher_Factory", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "CdkScrollable", "optional", "elementRef", "ngOnInit", "_cleanupScroll", "ngZone", "_scrollElement", "event", "_elementScrolled", "scroll<PERSON><PERSON><PERSON>tcher", "_this$_cleanupScroll", "_destroyed", "scrollTo", "options", "el", "isRtl", "dir", "left", "right", "bottom", "top", "scrollHeight", "clientHeight", "NORMAL", "scrollWidth", "clientWidth", "INVERTED", "NEGATED", "_applyScrollToOptions", "scrollTop", "scrollLeft", "from", "LEFT", "RIGHT", "_CdkScrollable", "_CdkScrollable_Factory", "DEFAULT_RESIZE_TIME", "ViewportRuler", "renderer", "changeListener", "_change", "_listeners", "change", "_viewportSize", "_this$_listeners", "cleanup", "_updateViewportSize", "output", "width", "height", "getViewportRect", "scrollPosition", "getViewportScrollPosition", "document", "_document", "window", "_getWindow", "documentElement", "documentRect", "getBoundingClientRect", "body", "scrollY", "scrollX", "throttleTime", "defaultView", "innerWidth", "innerHeight", "_ViewportRuler", "_ViewportRuler_Factory", "VIRTUAL_SCROLLABLE", "CdkVirtualScrollable", "measureViewportSize", "orientation", "viewportEl", "_CdkVirtualScrollable", "_CdkVirtualScrollable_Factory", "ɵɵInheritDefinitionFeature", "rangesEqual", "r1", "r2", "SCROLL_SCHEDULER", "requestAnimationFrame", "CdkVirtualScrollViewport", "_orientation", "_calculateSpacerSize", "scrolledIndexChange", "Promise", "resolve", "then", "run", "_renderedRangeSubject", "EMPTY", "viewportRuler", "_viewportChanges", "checkViewportSize", "classList", "add", "_measureViewportSize", "_markChangeDetectionNeeded", "_detachedSubject", "_isDestroyed", "forOf", "_forOf", "dataStream", "data", "<PERSON><PERSON><PERSON><PERSON>", "length", "_dataLength", "_doChangeDetection", "_rendered<PERSON><PERSON>e", "measureBoundingClientRectWithScrollOffset", "size", "_totalContentSize", "range", "appendOnly", "getOffsetToRenderedContentStart", "_renderedContentOffsetNeedsRewrite", "_renderedContentOffset", "offset", "to", "isHorizontal", "axis", "axisDirection", "transform", "Number", "_renderedContentTransform", "measureRenderedContentSize", "_from", "measureViewportOffset", "_this$dir", "fromRect", "scrollerClientRect", "viewportClientRect", "contentEl", "_contentWrapper", "offsetWidth", "offsetHeight", "measureRangeSize", "runAfter", "_runAfterChangeDetection", "_isChangeDetectionPending", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "runAfterChangeDetection", "fn", "injector", "_injector", "_totalContentHeight", "_totalContentWidth", "_CdkVirtualScrollViewport", "_CdkVirtualScrollViewport_Factory", "ɵɵdefineComponent", "viewQuery", "_CdkVirtualScrollViewport_Query", "rf", "ctx", "ɵɵviewQuery", "_c0", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "_CdkVirtualScrollViewport_HostBindings", "ɵɵclassProp", "outputs", "virtualScrollable", "ngContentSelectors", "_c1", "decls", "vars", "consts", "template", "_CdkVirtualScrollViewport_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵstyleProp", "styles", "encapsulation", "changeDetection", "host", "None", "OnPush", "static", "getOffset", "direction", "node", "rect", "CdkVirtualForOf", "cdkVirtualForOf", "_cdkVirtualForOf", "_dataSourceChanges", "Array", "cdkVirtualForTrackBy", "_cdkVirtualForTrackBy", "_needsUpdate", "item", "cdkVirtualForTemplate", "_template", "cdkVirtualForTemplateCacheSize", "_view<PERSON><PERSON><PERSON>er", "viewCacheSize", "skipSelf", "prev", "cur", "_changeDataSource", "_data", "_onRenderedDataChange", "renderedRangeStream", "viewChange", "observers", "renderedStartIndex", "rangeLen", "firstNode", "lastNode", "view", "_viewContainerRef", "rootNodes", "ngDoCheck", "_differ", "changes", "diff", "_renderedItems", "_updateContext", "_applyChanges", "slice", "_differs", "find", "create", "oldDs", "newDs", "disconnect", "connect", "count", "context", "_updateComputedContextProperties", "detectChanges", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "forEachIdentityChange", "$implicit", "last", "even", "odd", "templateRef", "ngTemplateContextGuard", "directive", "_CdkVirtualForOf", "_CdkVirtualForOf_Factory", "useClass", "CdkVirtualScrollableElement", "_CdkVirtualScrollableElement", "_CdkVirtualScrollableElement_Factory", "useExisting", "CdkVirtualScrollableWindow", "_CdkVirtualScrollableWindow", "_CdkVirtualScrollableWindow_Factory", "CdkScrollableModule", "_CdkScrollableModule", "_CdkScrollableModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector", "ScrollingModule", "_ScrollingModule", "_ScrollingModule_Factory"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/scrolling.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, inject, NgZone, RendererFactory2, Injectable, ElementRef, Renderer2, ChangeDetectorRef, Injector, afterNextRender, booleanAttribute, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, ViewContainerRef, TemplateRef, IterableDiffers, NgModule } from '@angular/core';\nimport { Subject, of, Observable, Subscription, animationFrameScheduler, asapScheduler, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, startWith, takeUntil, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { g as getRtlScrollAxisType, R as RtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { BidiModule } from './bidi.mjs';\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { b as _VIEW_REPEATER_STRATEGY, A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport { i as isDataSource } from './data-source-D34wiQZj.mjs';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n    _scrolledIndexChange = new Subject();\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n    /** The attached viewport. */\n    _viewport = null;\n    /** The size of the items in the virtually scrolling list. */\n    _itemSize;\n    /** The minimum amount of buffer rendered beyond the viewport (in pixels). */\n    _minBufferPx;\n    /** The number of buffer items to render beyond the edge of the viewport (in pixels). */\n    _maxBufferPx;\n    /**\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    constructor(itemSize, minBufferPx, maxBufferPx) {\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n    }\n    /**\n     * Attaches this scroll strategy to a viewport.\n     * @param viewport The viewport to attach this strategy to.\n     */\n    attach(viewport) {\n        this._viewport = viewport;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** Detaches this scroll strategy from the currently attached viewport. */\n    detach() {\n        this._scrolledIndexChange.complete();\n        this._viewport = null;\n    }\n    /**\n     * Update the item size and buffer size.\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n        if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n        }\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentScrolled() {\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onDataLengthChanged() {\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentRendered() {\n        /* no-op */\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onRenderedOffsetChanged() {\n        /* no-op */\n    }\n    /**\n     * Scroll to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling.\n     */\n    scrollToIndex(index, behavior) {\n        if (this._viewport) {\n            this._viewport.scrollToOffset(index * this._itemSize, behavior);\n        }\n    }\n    /** Update the viewport's total content size. */\n    _updateTotalContentSize() {\n        if (!this._viewport) {\n            return;\n        }\n        this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n    }\n    /** Update the viewport's rendered range. */\n    _updateRenderedRange() {\n        if (!this._viewport) {\n            return;\n        }\n        const renderedRange = this._viewport.getRenderedRange();\n        const newRange = { start: renderedRange.start, end: renderedRange.end };\n        const viewportSize = this._viewport.getViewportSize();\n        const dataLength = this._viewport.getDataLength();\n        let scrollOffset = this._viewport.measureScrollOffset();\n        // Prevent NaN as result when dividing by zero.\n        let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n        // If user scrolls to the bottom of the list and data changes to a smaller list\n        if (newRange.end > dataLength) {\n            // We have to recalculate the first visible index based on new data length and viewport size.\n            const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n            const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n            // If first visible index changed we must update scroll offset to handle start/end buffers\n            // Current range must also be adjusted to cover the new position (bottom of new list).\n            if (firstVisibleIndex != newVisibleIndex) {\n                firstVisibleIndex = newVisibleIndex;\n                scrollOffset = newVisibleIndex * this._itemSize;\n                newRange.start = Math.floor(firstVisibleIndex);\n            }\n            newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n        }\n        const startBuffer = scrollOffset - newRange.start * this._itemSize;\n        if (startBuffer < this._minBufferPx && newRange.start != 0) {\n            const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n            newRange.start = Math.max(0, newRange.start - expandStart);\n            newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n        }\n        else {\n            const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n            if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n                const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n                if (expandEnd > 0) {\n                    newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n                    newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n                }\n            }\n        }\n        this._viewport.setRenderedRange(newRange);\n        this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n        this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n    }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n    return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(value) {\n        this._itemSize = coerceNumberProperty(value);\n    }\n    _itemSize = 20;\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n        return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n        this._minBufferPx = coerceNumberProperty(value);\n    }\n    _minBufferPx = 100;\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n        return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n        this._maxBufferPx = coerceNumberProperty(value);\n    }\n    _maxBufferPx = 200;\n    /** The scroll strategy used by this directive. */\n    _scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    ngOnChanges() {\n        this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkFixedSizeVirtualScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkFixedSizeVirtualScroll, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[itemSize]\", inputs: { itemSize: \"itemSize\", minBufferPx: \"minBufferPx\", maxBufferPx: \"maxBufferPx\" }, providers: [\n            {\n                provide: VIRTUAL_SCROLL_STRATEGY,\n                useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n            },\n        ], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkFixedSizeVirtualScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[itemSize]',\n                    providers: [\n                        {\n                            provide: VIRTUAL_SCROLL_STRATEGY,\n                            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n                        },\n                    ],\n                }]\n        }], propDecorators: { itemSize: [{\n                type: Input\n            }], minBufferPx: [{\n                type: Input\n            }], maxBufferPx: [{\n                type: Input\n            }] } });\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _cleanupGlobalListener;\n    constructor() { }\n    /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n    _scrolled = new Subject();\n    /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n    _scrolledCount = 0;\n    /**\n     * Map of all the scrollable references that are registered with the service and their\n     * scroll event subscriptions.\n     */\n    scrollContainers = new Map();\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n        if (!this.scrollContainers.has(scrollable)) {\n            this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n        }\n    }\n    /**\n     * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n        const scrollableReference = this.scrollContainers.get(scrollable);\n        if (scrollableReference) {\n            scrollableReference.unsubscribe();\n            this.scrollContainers.delete(scrollable);\n        }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n        if (!this._platform.isBrowser) {\n            return of();\n        }\n        return new Observable((observer) => {\n            if (!this._cleanupGlobalListener) {\n                this._cleanupGlobalListener = this._ngZone.runOutsideAngular(() => this._renderer.listen('document', 'scroll', () => this._scrolled.next()));\n            }\n            // In the case of a 0ms delay, use an observable without auditTime\n            // since it does add a perceptible delay in processing overhead.\n            const subscription = auditTimeInMs > 0\n                ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer)\n                : this._scrolled.subscribe(observer);\n            this._scrolledCount++;\n            return () => {\n                subscription.unsubscribe();\n                this._scrolledCount--;\n                if (!this._scrolledCount) {\n                    this._cleanupGlobalListener?.();\n                    this._cleanupGlobalListener = undefined;\n                }\n            };\n        });\n    }\n    ngOnDestroy() {\n        this._cleanupGlobalListener?.();\n        this._cleanupGlobalListener = undefined;\n        this.scrollContainers.forEach((_, container) => this.deregister(container));\n        this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n        const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n        return this.scrolled(auditTimeInMs).pipe(filter(target => !target || ancestors.indexOf(target) > -1));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n        const scrollingContainers = [];\n        this.scrollContainers.forEach((_subscription, scrollable) => {\n            if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n                scrollingContainers.push(scrollable);\n            }\n        });\n        return scrollingContainers;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n        let element = coerceElement(elementOrElementRef);\n        let scrollableElement = scrollable.getElementRef().nativeElement;\n        // Traverse through the element parents until we reach null, checking if any of the elements\n        // are the scrollable's element.\n        do {\n            if (element == scrollableElement) {\n                return true;\n            }\n        } while ((element = element.parentElement));\n        return false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n    elementRef = inject(ElementRef);\n    scrollDispatcher = inject(ScrollDispatcher);\n    ngZone = inject(NgZone);\n    dir = inject(Directionality, { optional: true });\n    _scrollElement = this.elementRef.nativeElement;\n    _destroyed = new Subject();\n    _renderer = inject(Renderer2);\n    _cleanupScroll;\n    _elementScrolled = new Subject();\n    constructor() { }\n    ngOnInit() {\n        this._cleanupScroll = this.ngZone.runOutsideAngular(() => this._renderer.listen(this._scrollElement, 'scroll', event => this._elementScrolled.next(event)));\n        this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n        this._cleanupScroll?.();\n        this._elementScrolled.complete();\n        this.scrollDispatcher.deregister(this);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n        return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n        return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n        const el = this.elementRef.nativeElement;\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        // Rewrite start & end offsets as right or left offsets.\n        if (options.left == null) {\n            options.left = isRtl ? options.end : options.start;\n        }\n        if (options.right == null) {\n            options.right = isRtl ? options.start : options.end;\n        }\n        // Rewrite the bottom offset as a top offset.\n        if (options.bottom != null) {\n            options.top =\n                el.scrollHeight - el.clientHeight - options.bottom;\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl && getRtlScrollAxisType() != RtlScrollAxisType.NORMAL) {\n            if (options.left != null) {\n                options.right =\n                    el.scrollWidth - el.clientWidth - options.left;\n            }\n            if (getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n                options.left = options.right;\n            }\n            else if (getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n                options.left = options.right ? -options.right : options.right;\n            }\n        }\n        else {\n            if (options.right != null) {\n                options.left =\n                    el.scrollWidth - el.clientWidth - options.right;\n            }\n        }\n        this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n        const el = this.elementRef.nativeElement;\n        if (supportsScrollBehavior()) {\n            el.scrollTo(options);\n        }\n        else {\n            if (options.top != null) {\n                el.scrollTop = options.top;\n            }\n            if (options.left != null) {\n                el.scrollLeft = options.left;\n            }\n        }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const el = this.elementRef.nativeElement;\n        if (from == 'top') {\n            return el.scrollTop;\n        }\n        if (from == 'bottom') {\n            return el.scrollHeight - el.clientHeight - el.scrollTop;\n        }\n        // Rewrite start & end as left or right offsets.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        if (from == 'start') {\n            from = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            from = isRtl ? LEFT : RIGHT;\n        }\n        if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n            // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n            else {\n                return el.scrollLeft;\n            }\n        }\n        else if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n            // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft + el.scrollWidth - el.clientWidth;\n            }\n            else {\n                return -el.scrollLeft;\n            }\n        }\n        else {\n            // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n            // (scrollWidth - clientWidth) when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft;\n            }\n            else {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkScrollable, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkScrollable, isStandalone: true, selector: \"[cdk-scrollable], [cdkScrollable]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkScrollable, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-scrollable], [cdkScrollable]',\n                }]\n        }], ctorParameters: () => [] });\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n    _platform = inject(Platform);\n    _listeners;\n    /** Cached viewport dimensions. */\n    _viewportSize;\n    /** Stream of viewport change events. */\n    _change = new Subject();\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, { optional: true });\n    constructor() {\n        const ngZone = inject(NgZone);\n        const renderer = inject(RendererFactory2).createRenderer(null, null);\n        ngZone.runOutsideAngular(() => {\n            if (this._platform.isBrowser) {\n                const changeListener = (event) => this._change.next(event);\n                this._listeners = [\n                    renderer.listen('window', 'resize', changeListener),\n                    renderer.listen('window', 'orientationchange', changeListener),\n                ];\n            }\n            // Clear the cached position so that the viewport is re-measured next time it is required.\n            // We don't need to keep track of the subscription, because it is completed on destroy.\n            this.change().subscribe(() => (this._viewportSize = null));\n        });\n    }\n    ngOnDestroy() {\n        this._listeners?.forEach(cleanup => cleanup());\n        this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n        if (!this._viewportSize) {\n            this._updateViewportSize();\n        }\n        const output = { width: this._viewportSize.width, height: this._viewportSize.height };\n        // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n        if (!this._platform.isBrowser) {\n            this._viewportSize = null;\n        }\n        return output;\n    }\n    /** Gets a DOMRect for the viewport's bounds. */\n    getViewportRect() {\n        // Use the document element's bounding rect rather than the window scroll properties\n        // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n        // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n        // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n        // can disagree when the page is pinch-zoomed (on devices that support touch).\n        // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n        // We use the documentElement instead of the body because, by default (without a css reset)\n        // browsers typically give the document body an 8px margin, which is not included in\n        // getBoundingClientRect().\n        const scrollPosition = this.getViewportScrollPosition();\n        const { width, height } = this.getViewportSize();\n        return {\n            top: scrollPosition.top,\n            left: scrollPosition.left,\n            bottom: scrollPosition.top + height,\n            right: scrollPosition.left + width,\n            height,\n            width,\n        };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n        // While we can get a reference to the fake document\n        // during SSR, it doesn't have getBoundingClientRect.\n        if (!this._platform.isBrowser) {\n            return { top: 0, left: 0 };\n        }\n        // The top-left-corner of the viewport is determined by the scroll position of the document\n        // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n        // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n        // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n        // `document.documentElement` works consistently, where the `top` and `left` values will\n        // equal negative the scroll position.\n        const document = this._document;\n        const window = this._getWindow();\n        const documentElement = document.documentElement;\n        const documentRect = documentElement.getBoundingClientRect();\n        const top = -documentRect.top ||\n            document.body.scrollTop ||\n            window.scrollY ||\n            documentElement.scrollTop ||\n            0;\n        const left = -documentRect.left ||\n            document.body.scrollLeft ||\n            window.scrollX ||\n            documentElement.scrollLeft ||\n            0;\n        return { top, left };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n        return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n        const window = this._getWindow();\n        this._viewportSize = this._platform.isBrowser\n            ? { width: window.innerWidth, height: window.innerHeight }\n            : { width: 0, height: 0 };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ViewportRuler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ViewportRuler, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ViewportRuler, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n    constructor() {\n        super();\n    }\n    /**\n     * Measure the viewport size for the provided orientation.\n     *\n     * @param orientation The orientation to measure the size from.\n     */\n    measureViewportSize(orientation) {\n        const viewportEl = this.elementRef.nativeElement;\n        return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkVirtualScrollable, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkVirtualScrollable, isStandalone: true, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkVirtualScrollable, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [] });\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n    return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n    elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _scrollStrategy = inject(VIRTUAL_SCROLL_STRATEGY, {\n        optional: true,\n    });\n    scrollable = inject(VIRTUAL_SCROLLABLE, { optional: true });\n    _platform = inject(Platform);\n    /** Emits when the viewport is detached from a CdkVirtualForOf. */\n    _detachedSubject = new Subject();\n    /** Emits when the rendered range changes. */\n    _renderedRangeSubject = new Subject();\n    /** The direction the viewport scrolls. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(orientation) {\n        if (this._orientation !== orientation) {\n            this._orientation = orientation;\n            this._calculateSpacerSize();\n        }\n    }\n    _orientation = 'vertical';\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n    appendOnly = false;\n    // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n    // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n    // depending on how the strategy calculates the scrolled index, it may come at a cost to\n    // performance.\n    /** Emits when the index of the first element visible in the viewport changes. */\n    scrolledIndexChange = new Observable((observer) => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n    /** The element that wraps the rendered content. */\n    _contentWrapper;\n    /** A stream that emits whenever the rendered range changes. */\n    renderedRangeStream = this._renderedRangeSubject;\n    /**\n     * The total size of all content (in pixels), including content that is not currently rendered.\n     */\n    _totalContentSize = 0;\n    /** A string representing the `style.width` property value to be used for the spacer element. */\n    _totalContentWidth = '';\n    /** A string representing the `style.height` property value to be used for the spacer element. */\n    _totalContentHeight = '';\n    /**\n     * The CSS transform applied to the rendered subset of items so that they appear within the bounds\n     * of the visible viewport.\n     */\n    _renderedContentTransform;\n    /** The currently rendered range of indices. */\n    _renderedRange = { start: 0, end: 0 };\n    /** The length of the data bound to this viewport (in number of items). */\n    _dataLength = 0;\n    /** The size of the viewport (in pixels). */\n    _viewportSize = 0;\n    /** the currently attached CdkVirtualScrollRepeater. */\n    _forOf;\n    /** The last rendered content offset that was set. */\n    _renderedContentOffset = 0;\n    /**\n     * Whether the last rendered content offset was to the end of the content (and therefore needs to\n     * be rewritten as an offset to the start of the content).\n     */\n    _renderedContentOffsetNeedsRewrite = false;\n    /** Whether there is a pending change detection cycle. */\n    _isChangeDetectionPending = false;\n    /** A list of functions to run after the next change detection cycle. */\n    _runAfterChangeDetection = [];\n    /** Subscription to changes in the viewport size. */\n    _viewportChanges = Subscription.EMPTY;\n    _injector = inject(Injector);\n    _isDestroyed = false;\n    constructor() {\n        super();\n        const viewportRuler = inject(ViewportRuler);\n        if (!this._scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n        }\n        this._viewportChanges = viewportRuler.change().subscribe(() => {\n            this.checkViewportSize();\n        });\n        if (!this.scrollable) {\n            // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n            this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n            this.scrollable = this;\n        }\n    }\n    ngOnInit() {\n        // Scrolling depends on the element dimensions which we can't get during SSR.\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        if (this.scrollable === this) {\n            super.ngOnInit();\n        }\n        // It's still too early to measure the viewport at this point. Deferring with a promise allows\n        // the Viewport to be rendered with the correct size before we measure. We run this outside the\n        // zone to avoid causing more change detection cycles. We handle the change detection loop\n        // ourselves instead.\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n            this._measureViewportSize();\n            this._scrollStrategy.attach(this);\n            this.scrollable\n                .elementScrolled()\n                .pipe(\n            // Start off with a fake scroll event so we properly detect our initial position.\n            startWith(null), \n            // Collect multiple events into one until the next animation frame. This way if\n            // there are multiple scroll events in the same frame we only need to recheck\n            // our layout once.\n            auditTime(0, SCROLL_SCHEDULER), \n            // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n            // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n            // to unsubscribe here just in case.\n            takeUntil(this._destroyed))\n                .subscribe(() => this._scrollStrategy.onContentScrolled());\n            this._markChangeDetectionNeeded();\n        }));\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._scrollStrategy.detach();\n        // Complete all subjects\n        this._renderedRangeSubject.complete();\n        this._detachedSubject.complete();\n        this._viewportChanges.unsubscribe();\n        this._isDestroyed = true;\n        super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n        if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CdkVirtualScrollViewport is already attached.');\n        }\n        // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n        // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n        // change detection loop ourselves.\n        this.ngZone.runOutsideAngular(() => {\n            this._forOf = forOf;\n            this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n                const newLength = data.length;\n                if (newLength !== this._dataLength) {\n                    this._dataLength = newLength;\n                    this._scrollStrategy.onDataLengthChanged();\n                }\n                this._doChangeDetection();\n            });\n        });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n        this._forOf = null;\n        this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n        return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n        return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n        return this._renderedRange;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n        if (this._totalContentSize !== size) {\n            this._totalContentSize = size;\n            this._calculateSpacerSize();\n            this._markChangeDetectionNeeded();\n        }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n        if (!rangesEqual(this._renderedRange, range)) {\n            if (this.appendOnly) {\n                range = { start: 0, end: Math.max(this._renderedRange.end, range.end) };\n            }\n            this._renderedRangeSubject.next((this._renderedRange = range));\n            this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n        }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n        return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n        // In appendOnly, we always start from the top\n        offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n        // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n        // in the negative direction.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        const isHorizontal = this.orientation == 'horizontal';\n        const axis = isHorizontal ? 'X' : 'Y';\n        const axisDirection = isHorizontal && isRtl ? -1 : 1;\n        let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n        this._renderedContentOffset = offset;\n        if (to === 'to-end') {\n            transform += ` translate${axis}(-100%)`;\n            // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n            // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n            // expand upward).\n            this._renderedContentOffsetNeedsRewrite = true;\n        }\n        if (this._renderedContentTransform != transform) {\n            // We know this value is safe because we parse `offset` with `Number()` before passing it\n            // into the string.\n            this._renderedContentTransform = transform;\n            this._markChangeDetectionNeeded(() => {\n                if (this._renderedContentOffsetNeedsRewrite) {\n                    this._renderedContentOffset -= this.measureRenderedContentSize();\n                    this._renderedContentOffsetNeedsRewrite = false;\n                    this.setRenderedContentOffset(this._renderedContentOffset);\n                }\n                else {\n                    this._scrollStrategy.onRenderedOffsetChanged();\n                }\n            });\n        }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n        const options = { behavior };\n        if (this.orientation === 'horizontal') {\n            options.start = offset;\n        }\n        else {\n            options.top = offset;\n        }\n        this.scrollable.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n        this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the scrollable (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n        // This is to break the call cycle\n        let measureScrollOffset;\n        if (this.scrollable == this) {\n            measureScrollOffset = (_from) => super.measureScrollOffset(_from);\n        }\n        else {\n            measureScrollOffset = (_from) => this.scrollable.measureScrollOffset(_from);\n        }\n        return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) -\n            this.measureViewportOffset());\n    }\n    /**\n     * Measures the offset of the viewport from the scrolling container\n     * @param from The edge to measure from.\n     */\n    measureViewportOffset(from) {\n        let fromRect;\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const isRtl = this.dir?.value == 'rtl';\n        if (from == 'start') {\n            fromRect = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            fromRect = isRtl ? LEFT : RIGHT;\n        }\n        else if (from) {\n            fromRect = from;\n        }\n        else {\n            fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n        }\n        const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n        const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n        return viewportClientRect - scrollerClientRect;\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n        const contentEl = this._contentWrapper.nativeElement;\n        return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n        if (!this._forOf) {\n            return 0;\n        }\n        return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n        // TODO: Cleanup later when add logic for handling content resize\n        this._measureViewportSize();\n        this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n        this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n        if (runAfter) {\n            this._runAfterChangeDetection.push(runAfter);\n        }\n        // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n        // properties sequentially we only have to run `_doChangeDetection` once at the end.\n        if (!this._isChangeDetectionPending) {\n            this._isChangeDetectionPending = true;\n            this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n                this._doChangeDetection();\n            }));\n        }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n        if (this._isDestroyed) {\n            return;\n        }\n        this.ngZone.run(() => {\n            // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n            // from the root, since the repeated items are content projected in. Calling `detectChanges`\n            // instead does not properly check the projected content.\n            this._changeDetectorRef.markForCheck();\n            // Apply the content transform. The transform can't be set via an Angular binding because\n            // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n            // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n            // the `Number` function first to coerce it to a numeric value.\n            this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n            afterNextRender(() => {\n                this._isChangeDetectionPending = false;\n                const runAfterChangeDetection = this._runAfterChangeDetection;\n                this._runAfterChangeDetection = [];\n                for (const fn of runAfterChangeDetection) {\n                    fn();\n                }\n            }, { injector: this._injector });\n        });\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n        this._totalContentHeight =\n            this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n        this._totalContentWidth =\n            this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkVirtualScrollViewport, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkVirtualScrollViewport, isStandalone: true, selector: \"cdk-virtual-scroll-viewport\", inputs: { orientation: \"orientation\", appendOnly: [\"appendOnly\", \"appendOnly\", booleanAttribute] }, outputs: { scrolledIndexChange: \"scrolledIndexChange\" }, host: { properties: { \"class.cdk-virtual-scroll-orientation-horizontal\": \"orientation === \\\"horizontal\\\"\", \"class.cdk-virtual-scroll-orientation-vertical\": \"orientation !== \\\"horizontal\\\"\" }, classAttribute: \"cdk-virtual-scroll-viewport\" }, providers: [\n            {\n                provide: CdkScrollable,\n                useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n            },\n        ], viewQueries: [{ propertyName: \"_contentWrapper\", first: true, predicate: [\"contentWrapper\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkVirtualScrollViewport, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-virtual-scroll-viewport', host: {\n                        'class': 'cdk-virtual-scroll-viewport',\n                        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n                        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        {\n                            provide: CdkScrollable,\n                            useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                            deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n                        },\n                    ], template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { orientation: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], scrolledIndexChange: [{\n                type: Output\n            }], _contentWrapper: [{\n                type: ViewChild,\n                args: ['contentWrapper', { static: true }]\n            }] } });\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n    const el = node;\n    if (!el.getBoundingClientRect) {\n        return 0;\n    }\n    const rect = el.getBoundingClientRect();\n    if (orientation === 'horizontal') {\n        return direction === 'start' ? rect.left : rect.right;\n    }\n    return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n    _viewContainerRef = inject(ViewContainerRef);\n    _template = inject(TemplateRef);\n    _differs = inject(IterableDiffers);\n    _viewRepeater = inject(_VIEW_REPEATER_STRATEGY);\n    _viewport = inject(CdkVirtualScrollViewport, { skipSelf: true });\n    /** Emits when the rendered view of the data changes. */\n    viewChange = new Subject();\n    /** Subject that emits when a new DataSource instance is given. */\n    _dataSourceChanges = new Subject();\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n        return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n        this._cdkVirtualForOf = value;\n        if (isDataSource(value)) {\n            this._dataSourceChanges.next(value);\n        }\n        else {\n            // If value is an an NgIterable, convert it to an array.\n            this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n        }\n    }\n    _cdkVirtualForOf;\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n        return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n        this._needsUpdate = true;\n        this._cdkVirtualForTrackBy = fn\n            ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item)\n            : undefined;\n    }\n    _cdkVirtualForTrackBy;\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n        if (value) {\n            this._needsUpdate = true;\n            this._template = value;\n        }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n        return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n        this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    /** Emits whenever the data in the current DataSource changes. */\n    dataStream = this._dataSourceChanges.pipe(\n    // Start off with null `DataSource`.\n    startWith(null), \n    // Bundle up the previous and current data sources so we can work with both.\n    pairwise(), \n    // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n    // new one, passing back a stream of data changes which we run through `switchMap` to give\n    // us a data stream that emits the latest data from whatever the current `DataSource` is.\n    switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), \n    // Replay the last emitted data when someone subscribes.\n    shareReplay(1));\n    /** The differ used to calculate changes to the data. */\n    _differ = null;\n    /** The most recent data emitted from the DataSource. */\n    _data;\n    /** The currently rendered items. */\n    _renderedItems;\n    /** The currently rendered range of indices. */\n    _renderedRange;\n    /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n    _needsUpdate = false;\n    _destroyed = new Subject();\n    constructor() {\n        const ngZone = inject(NgZone);\n        this.dataStream.subscribe(data => {\n            this._data = data;\n            this._onRenderedDataChange();\n        });\n        this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n            this._renderedRange = range;\n            if (this.viewChange.observers.length) {\n                ngZone.run(() => this.viewChange.next(this._renderedRange));\n            }\n            this._onRenderedDataChange();\n        });\n        this._viewport.attach(this);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n        if (range.start >= range.end) {\n            return 0;\n        }\n        if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Error: attempted to measure an item that isn't rendered.`);\n        }\n        // The index into the list of rendered views for the first item in the range.\n        const renderedStartIndex = range.start - this._renderedRange.start;\n        // The length of the range we're measuring.\n        const rangeLen = range.end - range.start;\n        // Loop over all the views, find the first and land node and compute the size by subtracting\n        // the top of the first node from the bottom of the last one.\n        let firstNode;\n        let lastNode;\n        // Find the first node by starting from the beginning and going forwards.\n        for (let i = 0; i < rangeLen; i++) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                firstNode = lastNode = view.rootNodes[0];\n                break;\n            }\n        }\n        // Find the last node by starting from the end and going backwards.\n        for (let i = rangeLen - 1; i > -1; i--) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                lastNode = view.rootNodes[view.rootNodes.length - 1];\n                break;\n            }\n        }\n        return firstNode && lastNode\n            ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode)\n            : 0;\n    }\n    ngDoCheck() {\n        if (this._differ && this._needsUpdate) {\n            // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n            // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n            // changing (need to do this diff).\n            const changes = this._differ.diff(this._renderedItems);\n            if (!changes) {\n                this._updateContext();\n            }\n            else {\n                this._applyChanges(changes);\n            }\n            this._needsUpdate = false;\n        }\n    }\n    ngOnDestroy() {\n        this._viewport.detach();\n        this._dataSourceChanges.next(undefined);\n        this._dataSourceChanges.complete();\n        this.viewChange.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n        if (!this._renderedRange) {\n            return;\n        }\n        this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n        if (!this._differ) {\n            // Use a wrapper function for the `trackBy` so any new values are\n            // picked up automatically without having to recreate the differ.\n            this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n                return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n            });\n        }\n        this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n        if (oldDs) {\n            oldDs.disconnect(this);\n        }\n        this._needsUpdate = true;\n        return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n            view.detectChanges();\n        }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n        this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n        // Update $implicit for any items that had an identity change.\n        changes.forEachIdentityChange((record) => {\n            const view = this._viewContainerRef.get(record.currentIndex);\n            view.context.$implicit = record.item;\n        });\n        // Update the context variables on all items.\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n        }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n        context.first = context.index === 0;\n        context.last = context.index === context.count - 1;\n        context.even = context.index % 2 === 0;\n        context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n        // Note that it's important that we insert the item directly at the proper index,\n        // rather than inserting it and the moving it in place, because if there's a directive\n        // on the same node that injects the `ViewContainerRef`, Angular will insert another\n        // comment node which can throw off the move when it's being repeated for all items.\n        return {\n            templateRef: this._template,\n            context: {\n                $implicit: record.item,\n                // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n                // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n                cdkVirtualForOf: this._cdkVirtualForOf,\n                index: -1,\n                count: -1,\n                first: false,\n                last: false,\n                odd: false,\n                even: false,\n            },\n            index,\n        };\n    }\n    static ngTemplateContextGuard(directive, context) {\n        return true;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkVirtualForOf, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkVirtualForOf, isStandalone: true, selector: \"[cdkVirtualFor][cdkVirtualForOf]\", inputs: { cdkVirtualForOf: \"cdkVirtualForOf\", cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\", cdkVirtualForTemplate: \"cdkVirtualForTemplate\", cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\" }, providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkVirtualForOf, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualFor][cdkVirtualForOf]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { cdkVirtualForOf: [{\n                type: Input\n            }], cdkVirtualForTrackBy: [{\n                type: Input\n            }], cdkVirtualForTemplate: [{\n                type: Input\n            }], cdkVirtualForTemplateCacheSize: [{\n                type: Input\n            }] } });\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n    constructor() {\n        super();\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return (this.getElementRef().nativeElement.getBoundingClientRect()[from] -\n            this.measureScrollOffset(from));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkVirtualScrollableElement, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkVirtualScrollableElement, isStandalone: true, selector: \"[cdkVirtualScrollingElement]\", host: { classAttribute: \"cdk-virtual-scrollable\" }, providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkVirtualScrollableElement, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualScrollingElement]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }],\n                    host: {\n                        'class': 'cdk-virtual-scrollable',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n    constructor() {\n        super();\n        const document = inject(DOCUMENT);\n        this.elementRef = new ElementRef(document.documentElement);\n        this._scrollElement = document;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkVirtualScrollableWindow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkVirtualScrollableWindow, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[scrollWindow]\", providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkVirtualScrollableWindow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }],\n                }]\n        }], ctorParameters: () => [] });\n\nclass CdkScrollableModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkScrollableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkScrollableModule, imports: [CdkScrollable], exports: [CdkScrollable] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkScrollableModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkScrollableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkScrollable],\n                    imports: [CdkScrollable],\n                }]\n        }] });\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollingModule, imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport,\n            CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement], exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollViewport,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollingModule, imports: [BidiModule,\n            CdkScrollableModule, BidiModule, CdkScrollableModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkVirtualScrollViewport,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                    exports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollViewport,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                }]\n        }] });\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };\n//# sourceMappingURL=scrolling.mjs.map\n"], "mappings": ";;;;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACvW,SAASC,OAAO,EAAEC,EAAE,EAAEC,UAAU,EAAEC,YAAY,EAAEC,uBAAuB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,MAAM;AAClH,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AAChI,SAASC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,aAAa,QAAQ,wBAAwB;AACtF,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,SAASC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,sBAAsB,QAAQ,0BAA0B;AACzH,SAASC,UAAU,QAAQ,YAAY;AACvC,SAASC,GAAG,IAAIC,KAAK,QAAQ,YAAY;AACzC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,4BAA4B,QAAQ,+CAA+C;AACrJ,SAASC,CAAC,IAAIC,YAAY,QAAQ,4BAA4B;;AAE9D;AACA,MAAMC,uBAAuB,GAAG,IAAIlE,cAAc,CAAC,yBAAyB,CAAC;;AAE7E;AACA,MAAMmE,8BAA8B,CAAC;EAYjC;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IAAAC,eAAA,+BAhBzB,IAAI/C,OAAO,CAAC,CAAC;IACpC;IAAA+C,eAAA,8BACsB,IAAI,CAACC,oBAAoB,CAACC,IAAI,CAAC1C,oBAAoB,CAAC,CAAC,CAAC;IAC5E;IAAAwC,eAAA,oBACY,IAAI;IAChB;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAQI,IAAI,CAACG,SAAS,GAAGN,QAAQ;IACzB,IAAI,CAACO,YAAY,GAAGN,WAAW;IAC/B,IAAI,CAACO,YAAY,GAAGN,WAAW;EACnC;EACA;AACJ;AACA;AACA;EACIO,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,IAAI,CAACE,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACV,oBAAoB,CAACW,QAAQ,CAAC,CAAC;IACpC,IAAI,CAACJ,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,uBAAuBA,CAAChB,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IACxD,IAAIA,WAAW,GAAGD,WAAW,KAAK,OAAOgB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC9E,MAAMC,KAAK,CAAC,8EAA8E,CAAC;IAC/F;IACA,IAAI,CAACZ,SAAS,GAAGN,QAAQ;IACzB,IAAI,CAACO,YAAY,GAAGN,WAAW;IAC/B,IAAI,CAACO,YAAY,GAAGN,WAAW;IAC/B,IAAI,CAACU,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACN,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAO,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACR,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAQ,iBAAiBA,CAAA,EAAG;IAChB;EAAA;EAEJ;EACAC,uBAAuBA,CAAA,EAAG;IACtB;EAAA;EAEJ;AACJ;AACA;AACA;AACA;EACIC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC3B,IAAI,IAAI,CAACd,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACe,cAAc,CAACF,KAAK,GAAG,IAAI,CAAClB,SAAS,EAAEmB,QAAQ,CAAC;IACnE;EACJ;EACA;EACAb,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;MACjB;IACJ;IACA,IAAI,CAACA,SAAS,CAACgB,mBAAmB,CAAC,IAAI,CAAChB,SAAS,CAACiB,aAAa,CAAC,CAAC,GAAG,IAAI,CAACtB,SAAS,CAAC;EACvF;EACA;EACAO,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACF,SAAS,EAAE;MACjB;IACJ;IACA,MAAMkB,aAAa,GAAG,IAAI,CAAClB,SAAS,CAACmB,gBAAgB,CAAC,CAAC;IACvD,MAAMC,QAAQ,GAAG;MAAEC,KAAK,EAAEH,aAAa,CAACG,KAAK;MAAEC,GAAG,EAAEJ,aAAa,CAACI;IAAI,CAAC;IACvE,MAAMC,YAAY,GAAG,IAAI,CAACvB,SAAS,CAACwB,eAAe,CAAC,CAAC;IACrD,MAAMC,UAAU,GAAG,IAAI,CAACzB,SAAS,CAACiB,aAAa,CAAC,CAAC;IACjD,IAAIS,YAAY,GAAG,IAAI,CAAC1B,SAAS,CAAC2B,mBAAmB,CAAC,CAAC;IACvD;IACA,IAAIC,iBAAiB,GAAG,IAAI,CAACjC,SAAS,GAAG,CAAC,GAAG+B,YAAY,GAAG,IAAI,CAAC/B,SAAS,GAAG,CAAC;IAC9E;IACA,IAAIyB,QAAQ,CAACE,GAAG,GAAGG,UAAU,EAAE;MAC3B;MACA,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAI,CAACR,YAAY,GAAG,IAAI,CAAC5B,SAAS,CAAC;MAChE,MAAMqC,eAAe,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACN,iBAAiB,EAAEH,UAAU,GAAGI,eAAe,CAAC,CAAC;MAC9F;MACA;MACA,IAAID,iBAAiB,IAAII,eAAe,EAAE;QACtCJ,iBAAiB,GAAGI,eAAe;QACnCN,YAAY,GAAGM,eAAe,GAAG,IAAI,CAACrC,SAAS;QAC/CyB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC;MAClD;MACAR,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACC,KAAK,GAAGQ,eAAe,CAAC,CAAC;IACtF;IACA,MAAMO,WAAW,GAAGV,YAAY,GAAGN,QAAQ,CAACC,KAAK,GAAG,IAAI,CAAC1B,SAAS;IAClE,IAAIyC,WAAW,GAAG,IAAI,CAACxC,YAAY,IAAIwB,QAAQ,CAACC,KAAK,IAAI,CAAC,EAAE;MACxD,MAAMgB,WAAW,GAAGP,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAAClC,YAAY,GAAGuC,WAAW,IAAI,IAAI,CAACzC,SAAS,CAAC;MACjFyB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEb,QAAQ,CAACC,KAAK,GAAGgB,WAAW,CAAC;MAC1DjB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEK,IAAI,CAACC,IAAI,CAACH,iBAAiB,GAAG,CAACL,YAAY,GAAG,IAAI,CAAC3B,YAAY,IAAI,IAAI,CAACD,SAAS,CAAC,CAAC;IAC3H,CAAC,MACI;MACD,MAAM2C,SAAS,GAAGlB,QAAQ,CAACE,GAAG,GAAG,IAAI,CAAC3B,SAAS,IAAI+B,YAAY,GAAGH,YAAY,CAAC;MAC/E,IAAIe,SAAS,GAAG,IAAI,CAAC1C,YAAY,IAAIwB,QAAQ,CAACE,GAAG,IAAIG,UAAU,EAAE;QAC7D,MAAMc,SAAS,GAAGT,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAAClC,YAAY,GAAGyC,SAAS,IAAI,IAAI,CAAC3C,SAAS,CAAC;QAC7E,IAAI4C,SAAS,GAAG,CAAC,EAAE;UACfnB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACE,GAAG,GAAGiB,SAAS,CAAC;UAC7DnB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACK,KAAK,CAACP,iBAAiB,GAAG,IAAI,CAAChC,YAAY,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC;QACpG;MACJ;IACJ;IACA,IAAI,CAACK,SAAS,CAACwC,gBAAgB,CAACpB,QAAQ,CAAC;IACzC,IAAI,CAACpB,SAAS,CAACyC,wBAAwB,CAAC,IAAI,CAAC9C,SAAS,GAAGyB,QAAQ,CAACC,KAAK,CAAC;IACxE,IAAI,CAAC5B,oBAAoB,CAACiD,IAAI,CAACZ,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC,CAAC;EACjE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,sCAAsCA,CAACC,YAAY,EAAE;EAC1D,OAAOA,YAAY,CAACC,eAAe;AACvC;AACA;AACA,MAAMC,yBAAyB,CAAC;EAAA1D,YAAA;IAAAI,eAAA,oBAQhB,EAAE;IAAAA,eAAA,uBAWC,GAAG;IAAAA,eAAA,uBAUH,GAAG;IAClB;IAAAA,eAAA,0BACkB,IAAIL,8BAA8B,CAAC,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;EAAA;EA9BvG;EACA,IAAIF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACM,SAAS;EACzB;EACA,IAAIN,QAAQA,CAAC0D,KAAK,EAAE;IAChB,IAAI,CAACpD,SAAS,GAAGlC,oBAAoB,CAACsF,KAAK,CAAC;EAChD;EAEA;AACJ;AACA;AACA;EACI,IAAIzD,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACM,YAAY;EAC5B;EACA,IAAIN,WAAWA,CAACyD,KAAK,EAAE;IACnB,IAAI,CAACnD,YAAY,GAAGnC,oBAAoB,CAACsF,KAAK,CAAC;EACnD;EAEA;AACJ;AACA;EACI,IAAIxD,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACM,YAAY;EAC5B;EACA,IAAIN,WAAWA,CAACwD,KAAK,EAAE;IACnB,IAAI,CAAClD,YAAY,GAAGpC,oBAAoB,CAACsF,KAAK,CAAC;EACnD;EAIAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,eAAe,CAACxC,uBAAuB,CAAC,IAAI,CAAChB,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;EACnG;AASJ;AAAC0D,0BAAA,GA3CKH,yBAAyB;AAAAtD,eAAA,CAAzBsD,yBAAyB,wBAAAI,mCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAmCwEL,0BAAyB;AAAA;AAAAtD,eAAA,CAnC1HsD,yBAAyB,8BA4CkD/H,EAAE,CAAAqI,iBAAA;EAAAC,IAAA,EARQP,0BAAyB;EAAAQ,SAAA;EAAAC,MAAA;IAAAlE,QAAA;IAAAC,WAAA;IAAAC,WAAA;EAAA;EAAAiE,QAAA,GAQnCzI,EAAE,CAAA0I,kBAAA,CAR+M,CACtR;IACIC,OAAO,EAAExE,uBAAuB;IAChCyE,UAAU,EAAEhB,sCAAsC;IAClDiB,IAAI,EAAE,CAAC3I,UAAU,CAAC,MAAM6H,0BAAyB,CAAC;EACtD,CAAC,CACJ,GAEwE/H,EAAE,CAAA8I,oBAAA;AAAA;AAAnF;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KAAiFvF,EAAE,CAAA+I,iBAAA,CAAQhB,yBAAyB,EAAc,CAAC;IACvHO,IAAI,EAAEnI,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uCAAuC;MACjDC,SAAS,EAAE,CACP;QACIP,OAAO,EAAExE,uBAAuB;QAChCyE,UAAU,EAAEhB,sCAAsC;QAClDiB,IAAI,EAAE,CAAC3I,UAAU,CAAC,MAAM6H,yBAAyB,CAAC;MACtD,CAAC;IAET,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEzD,QAAQ,EAAE,CAAC;MACzBgE,IAAI,EAAElI;IACV,CAAC,CAAC;IAAEmE,WAAW,EAAE,CAAC;MACd+D,IAAI,EAAElI;IACV,CAAC,CAAC;IAAEoE,WAAW,EAAE,CAAC;MACd8D,IAAI,EAAElI;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM+I,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EAKnB/E,WAAWA,CAAA,EAAG;IAAAI,eAAA,kBAJJpE,MAAM,CAACC,MAAM,CAAC;IAAAmE,eAAA,oBACZpE,MAAM,CAACyC,QAAQ,CAAC;IAAA2B,eAAA,oBAChBpE,MAAM,CAACE,gBAAgB,CAAC,CAAC8I,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IAAA5E,eAAA;IAG/D;IAAAA,eAAA,oBACY,IAAI/C,OAAO,CAAC,CAAC;IACzB;IAAA+C,eAAA,yBACiB,CAAC;IAClB;AACJ;AACA;AACA;IAHIA,eAAA,2BAImB,IAAI6E,GAAG,CAAC,CAAC;EATZ;EAUhB;AACJ;AACA;AACA;AACA;EACIC,QAAQA,CAACC,UAAU,EAAE;IACjB,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACC,GAAG,CAACF,UAAU,CAAC,EAAE;MACxC,IAAI,CAACC,gBAAgB,CAACE,GAAG,CAACH,UAAU,EAAEA,UAAU,CAACI,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,SAAS,CAACnC,IAAI,CAAC6B,UAAU,CAAC,CAAC,CAAC;IACxH;EACJ;EACA;AACJ;AACA;AACA;EACIO,UAAUA,CAACP,UAAU,EAAE;IACnB,MAAMQ,mBAAmB,GAAG,IAAI,CAACP,gBAAgB,CAACQ,GAAG,CAACT,UAAU,CAAC;IACjE,IAAIQ,mBAAmB,EAAE;MACrBA,mBAAmB,CAACE,WAAW,CAAC,CAAC;MACjC,IAAI,CAACT,gBAAgB,CAACU,MAAM,CAACX,UAAU,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIY,QAAQA,CAACC,aAAa,GAAGlB,mBAAmB,EAAE;IAC1C,IAAI,CAAC,IAAI,CAACmB,SAAS,CAACC,SAAS,EAAE;MAC3B,OAAO5I,EAAE,CAAC,CAAC;IACf;IACA,OAAO,IAAIC,UAAU,CAAE4I,QAAQ,IAAK;MAChC,IAAI,CAAC,IAAI,CAACC,sBAAsB,EAAE;QAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,MAAM,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,IAAI,CAACf,SAAS,CAACnC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChJ;MACA;MACA;MACA,MAAMmD,YAAY,GAAGT,aAAa,GAAG,CAAC,GAChC,IAAI,CAACP,SAAS,CAACnF,IAAI,CAACzC,SAAS,CAACmI,aAAa,CAAC,CAAC,CAACR,SAAS,CAACW,QAAQ,CAAC,GACjE,IAAI,CAACV,SAAS,CAACD,SAAS,CAACW,QAAQ,CAAC;MACxC,IAAI,CAACO,cAAc,EAAE;MACrB,OAAO,MAAM;QACTD,YAAY,CAACZ,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACa,cAAc,EAAE;QACrB,IAAI,CAAC,IAAI,CAACA,cAAc,EAAE;UAAA,IAAAC,qBAAA;UACtB,CAAAA,qBAAA,OAAI,CAACP,sBAAsB,cAAAO,qBAAA,eAA3BA,qBAAA,CAAAC,IAAA,KAA8B,CAAC;UAC/B,IAAI,CAACR,sBAAsB,GAAGS,SAAS;QAC3C;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACAC,WAAWA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACV,CAAAA,sBAAA,OAAI,CAACX,sBAAsB,cAAAW,sBAAA,eAA3BA,sBAAA,CAAAH,IAAA,KAA8B,CAAC;IAC/B,IAAI,CAACR,sBAAsB,GAAGS,SAAS;IACvC,IAAI,CAACzB,gBAAgB,CAAC4B,OAAO,CAAC,CAACtH,CAAC,EAAEuH,SAAS,KAAK,IAAI,CAACvB,UAAU,CAACuB,SAAS,CAAC,CAAC;IAC3E,IAAI,CAACxB,SAAS,CAACzE,QAAQ,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkG,gBAAgBA,CAACC,mBAAmB,EAAEnB,aAAa,EAAE;IACjD,MAAMoB,SAAS,GAAG,IAAI,CAACC,2BAA2B,CAACF,mBAAmB,CAAC;IACvE,OAAO,IAAI,CAACpB,QAAQ,CAACC,aAAa,CAAC,CAAC1F,IAAI,CAACxC,MAAM,CAACwJ,MAAM,IAAI,CAACA,MAAM,IAAIF,SAAS,CAACG,OAAO,CAACD,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzG;EACA;EACAD,2BAA2BA,CAACF,mBAAmB,EAAE;IAC7C,MAAMK,mBAAmB,GAAG,EAAE;IAC9B,IAAI,CAACpC,gBAAgB,CAAC4B,OAAO,CAAC,CAACS,aAAa,EAAEtC,UAAU,KAAK;MACzD,IAAI,IAAI,CAACuC,0BAA0B,CAACvC,UAAU,EAAEgC,mBAAmB,CAAC,EAAE;QAClEK,mBAAmB,CAACG,IAAI,CAACxC,UAAU,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,OAAOqC,mBAAmB;EAC9B;EACA;EACAE,0BAA0BA,CAACvC,UAAU,EAAEgC,mBAAmB,EAAE;IACxD,IAAIS,OAAO,GAAGrJ,aAAa,CAAC4I,mBAAmB,CAAC;IAChD,IAAIU,iBAAiB,GAAG1C,UAAU,CAAC2C,aAAa,CAAC,CAAC,CAACC,aAAa;IAChE;IACA;IACA,GAAG;MACC,IAAIH,OAAO,IAAIC,iBAAiB,EAAE;QAC9B,OAAO,IAAI;MACf;IACJ,CAAC,QAASD,OAAO,GAAGA,OAAO,CAACI,aAAa;IACzC,OAAO,KAAK;EAChB;AAGJ;AAACC,iBAAA,GA/GKlD,gBAAgB;AAAA3E,eAAA,CAAhB2E,gBAAgB,wBAAAmD,0BAAAnE,iBAAA;EAAA,YAAAA,iBAAA,IA6GiFgB,iBAAgB;AAAA;AAAA3E,eAAA,CA7GjH2E,gBAAgB,+BA1B2DpJ,EAAE,CAAAwM,kBAAA;EAAAC,KAAA,EAwIwBrD,iBAAgB;EAAAsD,OAAA,EAAhBtD,iBAAgB,CAAAuD,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE/I;EAAA,QAAArH,SAAA,oBAAAA,SAAA,KA1IiFvF,EAAE,CAAA+I,iBAAA,CA0IQK,gBAAgB,EAAc,CAAC;IAC9Gd,IAAI,EAAE9H,UAAU;IAChBwI,IAAI,EAAE,CAAC;MAAE4D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAUhBxI,WAAWA,CAAA,EAAG;IAAAI,eAAA,qBATDpE,MAAM,CAACI,UAAU,CAAC;IAAAgE,eAAA,2BACZpE,MAAM,CAAC+I,gBAAgB,CAAC;IAAA3E,eAAA,iBAClCpE,MAAM,CAACC,MAAM,CAAC;IAAAmE,eAAA,cACjBpE,MAAM,CAAC2C,cAAc,EAAE;MAAE8J,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAArI,eAAA,yBAC/B,IAAI,CAACsI,UAAU,CAACX,aAAa;IAAA3H,eAAA,qBACjC,IAAI/C,OAAO,CAAC,CAAC;IAAA+C,eAAA,oBACdpE,MAAM,CAACK,SAAS,CAAC;IAAA+D,eAAA;IAAAA,eAAA,2BAEV,IAAI/C,OAAO,CAAC,CAAC;EAChB;EAChBsL,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,MAAM,CAACvC,iBAAiB,CAAC,MAAM,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,IAAI,CAACsC,cAAc,EAAE,QAAQ,EAAEC,KAAK,IAAI,IAAI,CAACC,gBAAgB,CAAC1F,IAAI,CAACyF,KAAK,CAAC,CAAC,CAAC;IAC3J,IAAI,CAACE,gBAAgB,CAAC/D,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA4B,WAAWA,CAAA,EAAG;IAAA,IAAAoC,oBAAA;IACV,CAAAA,oBAAA,OAAI,CAACN,cAAc,cAAAM,oBAAA,eAAnBA,oBAAA,CAAAtC,IAAA,KAAsB,CAAC;IACvB,IAAI,CAACoC,gBAAgB,CAAChI,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACiI,gBAAgB,CAACvD,UAAU,CAAC,IAAI,CAAC;IACtC,IAAI,CAACyD,UAAU,CAAC7F,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC6F,UAAU,CAACnI,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAuE,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACyD,gBAAgB;EAChC;EACA;EACAlB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACY,UAAU;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,QAAQA,CAACC,OAAO,EAAE;IACd,MAAMC,EAAE,GAAG,IAAI,CAACZ,UAAU,CAACX,aAAa;IACxC,MAAMwB,KAAK,GAAG,IAAI,CAACC,GAAG,IAAI,IAAI,CAACA,GAAG,CAAC7F,KAAK,IAAI,KAAK;IACjD;IACA,IAAI0F,OAAO,CAACI,IAAI,IAAI,IAAI,EAAE;MACtBJ,OAAO,CAACI,IAAI,GAAGF,KAAK,GAAGF,OAAO,CAACnH,GAAG,GAAGmH,OAAO,CAACpH,KAAK;IACtD;IACA,IAAIoH,OAAO,CAACK,KAAK,IAAI,IAAI,EAAE;MACvBL,OAAO,CAACK,KAAK,GAAGH,KAAK,GAAGF,OAAO,CAACpH,KAAK,GAAGoH,OAAO,CAACnH,GAAG;IACvD;IACA;IACA,IAAImH,OAAO,CAACM,MAAM,IAAI,IAAI,EAAE;MACxBN,OAAO,CAACO,GAAG,GACPN,EAAE,CAACO,YAAY,GAAGP,EAAE,CAACQ,YAAY,GAAGT,OAAO,CAACM,MAAM;IAC1D;IACA;IACA,IAAIJ,KAAK,IAAI1K,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAACgL,MAAM,EAAE;MAC7D,IAAIV,OAAO,CAACI,IAAI,IAAI,IAAI,EAAE;QACtBJ,OAAO,CAACK,KAAK,GACTJ,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACW,WAAW,GAAGZ,OAAO,CAACI,IAAI;MACtD;MACA,IAAI5K,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAACmL,QAAQ,EAAE;QACtDb,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACK,KAAK;MAChC,CAAC,MACI,IAAI7K,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAACoL,OAAO,EAAE;QAC1Dd,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACK,KAAK,GAAG,CAACL,OAAO,CAACK,KAAK,GAAGL,OAAO,CAACK,KAAK;MACjE;IACJ,CAAC,MACI;MACD,IAAIL,OAAO,CAACK,KAAK,IAAI,IAAI,EAAE;QACvBL,OAAO,CAACI,IAAI,GACRH,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACW,WAAW,GAAGZ,OAAO,CAACK,KAAK;MACvD;IACJ;IACA,IAAI,CAACU,qBAAqB,CAACf,OAAO,CAAC;EACvC;EACAe,qBAAqBA,CAACf,OAAO,EAAE;IAC3B,MAAMC,EAAE,GAAG,IAAI,CAACZ,UAAU,CAACX,aAAa;IACxC,IAAI9I,sBAAsB,CAAC,CAAC,EAAE;MAC1BqK,EAAE,CAACF,QAAQ,CAACC,OAAO,CAAC;IACxB,CAAC,MACI;MACD,IAAIA,OAAO,CAACO,GAAG,IAAI,IAAI,EAAE;QACrBN,EAAE,CAACe,SAAS,GAAGhB,OAAO,CAACO,GAAG;MAC9B;MACA,IAAIP,OAAO,CAACI,IAAI,IAAI,IAAI,EAAE;QACtBH,EAAE,CAACgB,UAAU,GAAGjB,OAAO,CAACI,IAAI;MAChC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlH,mBAAmBA,CAACgI,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,MAAM;IACnB,MAAMC,KAAK,GAAG,OAAO;IACrB,MAAMnB,EAAE,GAAG,IAAI,CAACZ,UAAU,CAACX,aAAa;IACxC,IAAIwC,IAAI,IAAI,KAAK,EAAE;MACf,OAAOjB,EAAE,CAACe,SAAS;IACvB;IACA,IAAIE,IAAI,IAAI,QAAQ,EAAE;MAClB,OAAOjB,EAAE,CAACO,YAAY,GAAGP,EAAE,CAACQ,YAAY,GAAGR,EAAE,CAACe,SAAS;IAC3D;IACA;IACA,MAAMd,KAAK,GAAG,IAAI,CAACC,GAAG,IAAI,IAAI,CAACA,GAAG,CAAC7F,KAAK,IAAI,KAAK;IACjD,IAAI4G,IAAI,IAAI,OAAO,EAAE;MACjBA,IAAI,GAAGhB,KAAK,GAAGkB,KAAK,GAAGD,IAAI;IAC/B,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;MACpBA,IAAI,GAAGhB,KAAK,GAAGiB,IAAI,GAAGC,KAAK;IAC/B;IACA,IAAIlB,KAAK,IAAI1K,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAACmL,QAAQ,EAAE;MAC/D;MACA;MACA,IAAIK,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOlB,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACW,WAAW,GAAGX,EAAE,CAACgB,UAAU;MAC1D,CAAC,MACI;QACD,OAAOhB,EAAE,CAACgB,UAAU;MACxB;IACJ,CAAC,MACI,IAAIf,KAAK,IAAI1K,oBAAoB,CAAC,CAAC,IAAIE,iBAAiB,CAACoL,OAAO,EAAE;MACnE;MACA;MACA,IAAII,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOlB,EAAE,CAACgB,UAAU,GAAGhB,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACW,WAAW;MAC1D,CAAC,MACI;QACD,OAAO,CAACX,EAAE,CAACgB,UAAU;MACzB;IACJ,CAAC,MACI;MACD;MACA;MACA,IAAIC,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOlB,EAAE,CAACgB,UAAU;MACxB,CAAC,MACI;QACD,OAAOhB,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACW,WAAW,GAAGX,EAAE,CAACgB,UAAU;MAC1D;IACJ;EACJ;AAGJ;AAACI,cAAA,GApJKlC,aAAa;AAAApI,eAAA,CAAboI,aAAa,wBAAAmC,uBAAA5G,iBAAA;EAAA,YAAAA,iBAAA,IAkJoFyE,cAAa;AAAA;AAAApI,eAAA,CAlJ9GoI,aAAa,8BApJ8D7M,EAAE,CAAAqI,iBAAA;EAAAC,IAAA,EAuSQuE,cAAa;EAAAtE,SAAA;AAAA;AAExG;EAAA,QAAAhD,SAAA,oBAAAA,SAAA,KAzSiFvF,EAAE,CAAA+I,iBAAA,CAySQ8D,aAAa,EAAc,CAAC;IAC3GvE,IAAI,EAAEnI,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAMgG,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAShB7K,WAAWA,CAAA,EAAG;IAAAI,eAAA,oBARFpE,MAAM,CAACyC,QAAQ,CAAC;IAAA2B,eAAA;IAE5B;IAAAA,eAAA;IAEA;IAAAA,eAAA,kBACU,IAAI/C,OAAO,CAAC,CAAC;IACvB;IAAA+C,eAAA,oBACYpE,MAAM,CAACqD,QAAQ,EAAE;MAAEoJ,QAAQ,EAAE;IAAK,CAAC,CAAC;IAE5C,MAAMI,MAAM,GAAG7M,MAAM,CAACC,MAAM,CAAC;IAC7B,MAAM6O,QAAQ,GAAG9O,MAAM,CAACE,gBAAgB,CAAC,CAAC8I,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;IACpE6D,MAAM,CAACvC,iBAAiB,CAAC,MAAM;MAC3B,IAAI,IAAI,CAACL,SAAS,CAACC,SAAS,EAAE;QAC1B,MAAM6E,cAAc,GAAIhC,KAAK,IAAK,IAAI,CAACiC,OAAO,CAAC1H,IAAI,CAACyF,KAAK,CAAC;QAC1D,IAAI,CAACkC,UAAU,GAAG,CACdH,QAAQ,CAACtE,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAEuE,cAAc,CAAC,EACnDD,QAAQ,CAACtE,MAAM,CAAC,QAAQ,EAAE,mBAAmB,EAAEuE,cAAc,CAAC,CACjE;MACL;MACA;MACA;MACA,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC1F,SAAS,CAAC,MAAO,IAAI,CAAC2F,aAAa,GAAG,IAAK,CAAC;IAC9D,CAAC,CAAC;EACN;EACArE,WAAWA,CAAA,EAAG;IAAA,IAAAsE,gBAAA;IACV,CAAAA,gBAAA,OAAI,CAACH,UAAU,cAAAG,gBAAA,eAAfA,gBAAA,CAAiBpE,OAAO,CAACqE,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACL,OAAO,CAAChK,QAAQ,CAAC,CAAC;EAC3B;EACA;EACAoB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAAC+I,aAAa,EAAE;MACrB,IAAI,CAACG,mBAAmB,CAAC,CAAC;IAC9B;IACA,MAAMC,MAAM,GAAG;MAAEC,KAAK,EAAE,IAAI,CAACL,aAAa,CAACK,KAAK;MAAEC,MAAM,EAAE,IAAI,CAACN,aAAa,CAACM;IAAO,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAACxF,SAAS,CAACC,SAAS,EAAE;MAC3B,IAAI,CAACiF,aAAa,GAAG,IAAI;IAC7B;IACA,OAAOI,MAAM;EACjB;EACA;EACAG,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,cAAc,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACvD,MAAM;MAAEJ,KAAK;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACrJ,eAAe,CAAC,CAAC;IAChD,OAAO;MACHwH,GAAG,EAAE+B,cAAc,CAAC/B,GAAG;MACvBH,IAAI,EAAEkC,cAAc,CAAClC,IAAI;MACzBE,MAAM,EAAEgC,cAAc,CAAC/B,GAAG,GAAG6B,MAAM;MACnC/B,KAAK,EAAEiC,cAAc,CAAClC,IAAI,GAAG+B,KAAK;MAClCC,MAAM;MACND;IACJ,CAAC;EACL;EACA;EACAI,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA,IAAI,CAAC,IAAI,CAAC3F,SAAS,CAACC,SAAS,EAAE;MAC3B,OAAO;QAAE0D,GAAG,EAAE,CAAC;QAAEH,IAAI,EAAE;MAAE,CAAC;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMoC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,MAAMC,eAAe,GAAGJ,QAAQ,CAACI,eAAe;IAChD,MAAMC,YAAY,GAAGD,eAAe,CAACE,qBAAqB,CAAC,CAAC;IAC5D,MAAMvC,GAAG,GAAG,CAACsC,YAAY,CAACtC,GAAG,IACzBiC,QAAQ,CAACO,IAAI,CAAC/B,SAAS,IACvB0B,MAAM,CAACM,OAAO,IACdJ,eAAe,CAAC5B,SAAS,IACzB,CAAC;IACL,MAAMZ,IAAI,GAAG,CAACyC,YAAY,CAACzC,IAAI,IAC3BoC,QAAQ,CAACO,IAAI,CAAC9B,UAAU,IACxByB,MAAM,CAACO,OAAO,IACdL,eAAe,CAAC3B,UAAU,IAC1B,CAAC;IACL,OAAO;MAAEV,GAAG;MAAEH;IAAK,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIyB,MAAMA,CAACqB,YAAY,GAAG3B,mBAAmB,EAAE;IACvC,OAAO2B,YAAY,GAAG,CAAC,GAAG,IAAI,CAACvB,OAAO,CAAC1K,IAAI,CAACzC,SAAS,CAAC0O,YAAY,CAAC,CAAC,GAAG,IAAI,CAACvB,OAAO;EACvF;EACA;EACAgB,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,SAAS,CAACU,WAAW,IAAIT,MAAM;EAC/C;EACA;EACAT,mBAAmBA,CAAA,EAAG;IAClB,MAAMS,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,IAAI,CAACb,aAAa,GAAG,IAAI,CAAClF,SAAS,CAACC,SAAS,GACvC;MAAEsF,KAAK,EAAEO,MAAM,CAACU,UAAU;MAAEhB,MAAM,EAAEM,MAAM,CAACW;IAAY,CAAC,GACxD;MAAElB,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;EACjC;AAGJ;AAACkB,cAAA,GAjHK9B,aAAa;AAAAzK,eAAA,CAAbyK,aAAa,wBAAA+B,uBAAA7I,iBAAA;EAAA,YAAAA,iBAAA,IA+GoF8G,cAAa;AAAA;AAAAzK,eAAA,CA/G9GyK,aAAa,+BAtT8DlP,EAAE,CAAAwM,kBAAA;EAAAC,KAAA,EAsawByC,cAAa;EAAAxC,OAAA,EAAbwC,cAAa,CAAAvC,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE5I;EAAA,QAAArH,SAAA,oBAAAA,SAAA,KAxaiFvF,EAAE,CAAA+I,iBAAA,CAwaQmG,aAAa,EAAc,CAAC;IAC3G5G,IAAI,EAAE9H,UAAU;IAChBwI,IAAI,EAAE,CAAC;MAAE4D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMsE,kBAAkB,GAAG,IAAIjR,cAAc,CAAC,oBAAoB,CAAC;AACnE;AACA;AACA;AACA,MAAMkR,oBAAoB,SAAStE,aAAa,CAAC;EAC7CxI,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACA;AACJ;AACA;AACA;AACA;EACI+M,mBAAmBA,CAACC,WAAW,EAAE;IAC7B,MAAMC,UAAU,GAAG,IAAI,CAACvE,UAAU,CAACX,aAAa;IAChD,OAAOiF,WAAW,KAAK,YAAY,GAAGC,UAAU,CAAChD,WAAW,GAAGgD,UAAU,CAACnD,YAAY;EAC1F;AAGJ;AAACoD,qBAAA,GAfKJ,oBAAoB;AAAA1M,eAAA,CAApB0M,oBAAoB,wBAAAK,8BAAApJ,iBAAA;EAAA,YAAAA,iBAAA,IAa6E+I,qBAAoB;AAAA;AAAA1M,eAAA,CAbrH0M,oBAAoB,8BAjbuDnR,EAAE,CAAAqI,iBAAA;EAAAC,IAAA,EA+bQ6I,qBAAoB;EAAA1I,QAAA,GA/b9BzI,EAAE,CAAAyR,0BAAA;AAAA;AAicnF;EAAA,QAAAlM,SAAA,oBAAAA,SAAA,KAjciFvF,EAAE,CAAA+I,iBAAA,CAicQoI,oBAAoB,EAAc,CAAC;IAClH7I,IAAI,EAAEnI;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,SAASuR,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACzB,OAAOD,EAAE,CAACrL,KAAK,IAAIsL,EAAE,CAACtL,KAAK,IAAIqL,EAAE,CAACpL,GAAG,IAAIqL,EAAE,CAACrL,GAAG;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsL,gBAAgB,GAAG,OAAOC,qBAAqB,KAAK,WAAW,GAAGhQ,uBAAuB,GAAGC,aAAa;AAC/G;AACA,MAAMgQ,wBAAwB,SAASZ,oBAAoB,CAAC;EAYxD;EACA,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACW,YAAY;EAC5B;EACA,IAAIX,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACW,YAAY,KAAKX,WAAW,EAAE;MACnC,IAAI,CAACW,YAAY,GAAGX,WAAW;MAC/B,IAAI,CAACY,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EAqDA5N,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IAACI,eAAA,qBA1ECpE,MAAM,CAACI,UAAU,CAAC;IAAAgE,eAAA,6BACVpE,MAAM,CAACM,iBAAiB,CAAC;IAAA8D,eAAA,0BAC5BpE,MAAM,CAAC8D,uBAAuB,EAAE;MAC9C2I,QAAQ,EAAE;IACd,CAAC,CAAC;IAAArI,eAAA,qBACWpE,MAAM,CAAC6Q,kBAAkB,EAAE;MAAEpE,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAArI,eAAA,oBAC/CpE,MAAM,CAACyC,QAAQ,CAAC;IAC5B;IAAA2B,eAAA,2BACmB,IAAI/C,OAAO,CAAC,CAAC;IAChC;IAAA+C,eAAA,gCACwB,IAAI/C,OAAO,CAAC,CAAC;IAAA+C,eAAA,uBAWtB,UAAU;IACzB;AACJ;AACA;AACA;IAHIA,eAAA,qBAIa,KAAK;IAClB;IACA;IACA;IACA;IACA;IAAAA,eAAA,8BACsB,IAAI7C,UAAU,CAAE4I,QAAQ,IAAK,IAAI,CAAC1C,eAAe,CAACoK,mBAAmB,CAACrI,SAAS,CAAC/D,KAAK,IAAIqM,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACnF,MAAM,CAACoF,GAAG,CAAC,MAAM9H,QAAQ,CAAC7C,IAAI,CAAC7B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1L;IAAArB,eAAA;IAEA;IAAAA,eAAA,8BACsB,IAAI,CAAC8N,qBAAqB;IAChD;AACJ;AACA;IAFI9N,eAAA,4BAGoB,CAAC;IACrB;IAAAA,eAAA,6BACqB,EAAE;IACvB;IAAAA,eAAA,8BACsB,EAAE;IACxB;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA,yBACiB;MAAE6B,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IACrC;IAAA9B,eAAA,sBACc,CAAC;IACf;IAAAA,eAAA,wBACgB,CAAC;IACjB;IAAAA,eAAA;IAEA;IAAAA,eAAA,iCACyB,CAAC;IAC1B;AACJ;AACA;AACA;IAHIA,eAAA,6CAIqC,KAAK;IAC1C;IAAAA,eAAA,oCAC4B,KAAK;IACjC;IAAAA,eAAA,mCAC2B,EAAE;IAC7B;IAAAA,eAAA,2BACmB5C,YAAY,CAAC2Q,KAAK;IAAA/N,eAAA,oBACzBpE,MAAM,CAACO,QAAQ,CAAC;IAAA6D,eAAA,uBACb,KAAK;IAGhB,MAAMgO,aAAa,GAAGpS,MAAM,CAAC6O,aAAa,CAAC;IAC3C,IAAI,CAAC,IAAI,CAACpH,eAAe,KAAK,OAAOvC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1E,MAAMC,KAAK,CAAC,gFAAgF,CAAC;IACjG;IACA,IAAI,CAACkN,gBAAgB,GAAGD,aAAa,CAAClD,MAAM,CAAC,CAAC,CAAC1F,SAAS,CAAC,MAAM;MAC3D,IAAI,CAAC8I,iBAAiB,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAACnJ,UAAU,EAAE;MAClB;MACA,IAAI,CAACuD,UAAU,CAACX,aAAa,CAACwG,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrE,IAAI,CAACrJ,UAAU,GAAG,IAAI;IAC1B;EACJ;EACAwD,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAAC,IAAI,CAAC1C,SAAS,CAACC,SAAS,EAAE;MAC3B;IACJ;IACA,IAAI,IAAI,CAACf,UAAU,KAAK,IAAI,EAAE;MAC1B,KAAK,CAACwD,QAAQ,CAAC,CAAC;IACpB;IACA;IACA;IACA;IACA;IACA,IAAI,CAACE,MAAM,CAACvC,iBAAiB,CAAC,MAAMwH,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAC7D,IAAI,CAACS,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAChL,eAAe,CAAC/C,MAAM,CAAC,IAAI,CAAC;MACjC,IAAI,CAACyE,UAAU,CACVI,eAAe,CAAC,CAAC,CACjBjF,IAAI;MACT;MACAvC,SAAS,CAAC,IAAI,CAAC;MACf;MACA;MACA;MACAF,SAAS,CAAC,CAAC,EAAE2P,gBAAgB,CAAC;MAC9B;MACA;MACA;MACAxP,SAAS,CAAC,IAAI,CAACmL,UAAU,CAAC,CAAC,CACtB3D,SAAS,CAAC,MAAM,IAAI,CAAC/B,eAAe,CAACrC,iBAAiB,CAAC,CAAC,CAAC;MAC9D,IAAI,CAACsN,0BAA0B,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;EACP;EACA5H,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC/F,MAAM,CAAC,CAAC;IACb,IAAI,CAAC0C,eAAe,CAAC1C,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAACmN,qBAAqB,CAAClN,QAAQ,CAAC,CAAC;IACrC,IAAI,CAAC2N,gBAAgB,CAAC3N,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACqN,gBAAgB,CAACxI,WAAW,CAAC,CAAC;IACnC,IAAI,CAAC+I,YAAY,GAAG,IAAI;IACxB,KAAK,CAAC9H,WAAW,CAAC,CAAC;EACvB;EACA;EACApG,MAAMA,CAACmO,KAAK,EAAE;IACV,IAAI,IAAI,CAACC,MAAM,KAAK,OAAO5N,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAChE,MAAMC,KAAK,CAAC,+CAA+C,CAAC;IAChE;IACA;IACA;IACA;IACA,IAAI,CAAC0H,MAAM,CAACvC,iBAAiB,CAAC,MAAM;MAChC,IAAI,CAACwI,MAAM,GAAGD,KAAK;MACnB,IAAI,CAACC,MAAM,CAACC,UAAU,CAACzO,IAAI,CAACtC,SAAS,CAAC,IAAI,CAAC2Q,gBAAgB,CAAC,CAAC,CAACnJ,SAAS,CAACwJ,IAAI,IAAI;QAC5E,MAAMC,SAAS,GAAGD,IAAI,CAACE,MAAM;QAC7B,IAAID,SAAS,KAAK,IAAI,CAACE,WAAW,EAAE;UAChC,IAAI,CAACA,WAAW,GAAGF,SAAS;UAC5B,IAAI,CAACxL,eAAe,CAACpC,mBAAmB,CAAC,CAAC;QAC9C;QACA,IAAI,CAAC+N,kBAAkB,CAAC,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACArO,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC+N,MAAM,GAAG,IAAI;IAClB,IAAI,CAACH,gBAAgB,CAACrL,IAAI,CAAC,CAAC;EAChC;EACA;EACAzB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACsN,WAAW;EAC3B;EACA;EACA/M,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC+I,aAAa;EAC7B;EACA;EACA;EACA;EACA;EACA;EACApJ,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACsN,cAAc;EAC9B;EACAC,yCAAyCA,CAAC/E,IAAI,EAAE;IAC5C,OAAO,IAAI,CAACzC,aAAa,CAAC,CAAC,CAACC,aAAa,CAACoE,qBAAqB,CAAC,CAAC,CAAC5B,IAAI,CAAC;EAC3E;EACA;AACJ;AACA;AACA;EACI3I,mBAAmBA,CAAC2N,IAAI,EAAE;IACtB,IAAI,IAAI,CAACC,iBAAiB,KAAKD,IAAI,EAAE;MACjC,IAAI,CAACC,iBAAiB,GAAGD,IAAI;MAC7B,IAAI,CAAC3B,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACc,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACA;EACAtL,gBAAgBA,CAACqM,KAAK,EAAE;IACpB,IAAI,CAACpC,WAAW,CAAC,IAAI,CAACgC,cAAc,EAAEI,KAAK,CAAC,EAAE;MAC1C,IAAI,IAAI,CAACC,UAAU,EAAE;QACjBD,KAAK,GAAG;UAAExN,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAEQ,IAAI,CAACG,GAAG,CAAC,IAAI,CAACwM,cAAc,CAACnN,GAAG,EAAEuN,KAAK,CAACvN,GAAG;QAAE,CAAC;MAC3E;MACA,IAAI,CAACgM,qBAAqB,CAAC5K,IAAI,CAAE,IAAI,CAAC+L,cAAc,GAAGI,KAAM,CAAC;MAC9D,IAAI,CAACf,0BAA0B,CAAC,MAAM,IAAI,CAACjL,eAAe,CAACnC,iBAAiB,CAAC,CAAC,CAAC;IACnF;EACJ;EACA;AACJ;AACA;EACIqO,+BAA+BA,CAAA,EAAG;IAC9B,OAAO,IAAI,CAACC,kCAAkC,GAAG,IAAI,GAAG,IAAI,CAACC,sBAAsB;EACvF;EACA;AACJ;AACA;AACA;EACIxM,wBAAwBA,CAACyM,MAAM,EAAEC,EAAE,GAAG,UAAU,EAAE;IAC9C;IACAD,MAAM,GAAG,IAAI,CAACJ,UAAU,IAAIK,EAAE,KAAK,UAAU,GAAG,CAAC,GAAGD,MAAM;IAC1D;IACA;IACA,MAAMvG,KAAK,GAAG,IAAI,CAACC,GAAG,IAAI,IAAI,CAACA,GAAG,CAAC7F,KAAK,IAAI,KAAK;IACjD,MAAMqM,YAAY,GAAG,IAAI,CAAChD,WAAW,IAAI,YAAY;IACrD,MAAMiD,IAAI,GAAGD,YAAY,GAAG,GAAG,GAAG,GAAG;IACrC,MAAME,aAAa,GAAGF,YAAY,IAAIzG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IACpD,IAAI4G,SAAS,GAAG,YAAYF,IAAI,IAAIG,MAAM,CAACF,aAAa,GAAGJ,MAAM,CAAC,KAAK;IACvE,IAAI,CAACD,sBAAsB,GAAGC,MAAM;IACpC,IAAIC,EAAE,KAAK,QAAQ,EAAE;MACjBI,SAAS,IAAI,aAAaF,IAAI,SAAS;MACvC;MACA;MACA;MACA,IAAI,CAACL,kCAAkC,GAAG,IAAI;IAClD;IACA,IAAI,IAAI,CAACS,yBAAyB,IAAIF,SAAS,EAAE;MAC7C;MACA;MACA,IAAI,CAACE,yBAAyB,GAAGF,SAAS;MAC1C,IAAI,CAACzB,0BAA0B,CAAC,MAAM;QAClC,IAAI,IAAI,CAACkB,kCAAkC,EAAE;UACzC,IAAI,CAACC,sBAAsB,IAAI,IAAI,CAACS,0BAA0B,CAAC,CAAC;UAChE,IAAI,CAACV,kCAAkC,GAAG,KAAK;UAC/C,IAAI,CAACvM,wBAAwB,CAAC,IAAI,CAACwM,sBAAsB,CAAC;QAC9D,CAAC,MACI;UACD,IAAI,CAACpM,eAAe,CAAClC,uBAAuB,CAAC,CAAC;QAClD;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,cAAcA,CAACmO,MAAM,EAAEpO,QAAQ,GAAG,MAAM,EAAE;IACtC,MAAM2H,OAAO,GAAG;MAAE3H;IAAS,CAAC;IAC5B,IAAI,IAAI,CAACsL,WAAW,KAAK,YAAY,EAAE;MACnC3D,OAAO,CAACpH,KAAK,GAAG6N,MAAM;IAC1B,CAAC,MACI;MACDzG,OAAO,CAACO,GAAG,GAAGkG,MAAM;IACxB;IACA,IAAI,CAAC3K,UAAU,CAACiE,QAAQ,CAACC,OAAO,CAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;EACI7H,aAAaA,CAACC,KAAK,EAAEC,QAAQ,GAAG,MAAM,EAAE;IACpC,IAAI,CAAC+B,eAAe,CAACjC,aAAa,CAACC,KAAK,EAAEC,QAAQ,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;EACIa,mBAAmBA,CAACgI,IAAI,EAAE;IACtB;IACA,IAAIhI,mBAAmB;IACvB,IAAI,IAAI,CAAC4C,UAAU,IAAI,IAAI,EAAE;MACzB5C,mBAAmB,GAAIgO,KAAK,IAAK,KAAK,CAAChO,mBAAmB,CAACgO,KAAK,CAAC;IACrE,CAAC,MACI;MACDhO,mBAAmB,GAAIgO,KAAK,IAAK,IAAI,CAACpL,UAAU,CAAC5C,mBAAmB,CAACgO,KAAK,CAAC;IAC/E;IACA,OAAO7N,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEN,mBAAmB,CAACgI,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAK,IAAI,CAACyC,WAAW,KAAK,YAAY,GAAG,OAAO,GAAG,KAAM,CAAC,GACjG,IAAI,CAACwD,qBAAqB,CAAC,CAAC,CAAC;EACrC;EACA;AACJ;AACA;AACA;EACIA,qBAAqBA,CAACjG,IAAI,EAAE;IAAA,IAAAkG,SAAA;IACxB,IAAIC,QAAQ;IACZ,MAAMlG,IAAI,GAAG,MAAM;IACnB,MAAMC,KAAK,GAAG,OAAO;IACrB,MAAMlB,KAAK,GAAG,EAAAkH,SAAA,OAAI,CAACjH,GAAG,cAAAiH,SAAA,uBAARA,SAAA,CAAU9M,KAAK,KAAI,KAAK;IACtC,IAAI4G,IAAI,IAAI,OAAO,EAAE;MACjBmG,QAAQ,GAAGnH,KAAK,GAAGkB,KAAK,GAAGD,IAAI;IACnC,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;MACpBmG,QAAQ,GAAGnH,KAAK,GAAGiB,IAAI,GAAGC,KAAK;IACnC,CAAC,MACI,IAAIF,IAAI,EAAE;MACXmG,QAAQ,GAAGnG,IAAI;IACnB,CAAC,MACI;MACDmG,QAAQ,GAAG,IAAI,CAAC1D,WAAW,KAAK,YAAY,GAAG,MAAM,GAAG,KAAK;IACjE;IACA,MAAM2D,kBAAkB,GAAG,IAAI,CAACxL,UAAU,CAACmK,yCAAyC,CAACoB,QAAQ,CAAC;IAC9F,MAAME,kBAAkB,GAAG,IAAI,CAAClI,UAAU,CAACX,aAAa,CAACoE,qBAAqB,CAAC,CAAC,CAACuE,QAAQ,CAAC;IAC1F,OAAOE,kBAAkB,GAAGD,kBAAkB;EAClD;EACA;EACAL,0BAA0BA,CAAA,EAAG;IACzB,MAAMO,SAAS,GAAG,IAAI,CAACC,eAAe,CAAC/I,aAAa;IACpD,OAAO,IAAI,CAACiF,WAAW,KAAK,YAAY,GAAG6D,SAAS,CAACE,WAAW,GAAGF,SAAS,CAACG,YAAY;EAC7F;EACA;AACJ;AACA;AACA;EACIC,gBAAgBA,CAACxB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACX,MAAM,EAAE;MACd,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACA,MAAM,CAACmC,gBAAgB,CAACxB,KAAK,EAAE,IAAI,CAACzC,WAAW,CAAC;EAChE;EACA;EACAsB,iBAAiBA,CAAA,EAAG;IAChB;IACA,IAAI,CAACG,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAChL,eAAe,CAACpC,mBAAmB,CAAC,CAAC;EAC9C;EACA;EACAoN,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACtD,aAAa,GAAG,IAAI,CAAChG,UAAU,CAAC4H,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC;EAC9E;EACA;EACA0B,0BAA0BA,CAACwC,QAAQ,EAAE;IACjC,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACC,wBAAwB,CAACxJ,IAAI,CAACuJ,QAAQ,CAAC;IAChD;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACE,yBAAyB,EAAE;MACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;MACrC,IAAI,CAACvI,MAAM,CAACvC,iBAAiB,CAAC,MAAMwH,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC7D,IAAI,CAACoB,kBAAkB,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC;IACP;EACJ;EACA;EACAA,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACR,YAAY,EAAE;MACnB;IACJ;IACA,IAAI,CAAC/F,MAAM,CAACoF,GAAG,CAAC,MAAM;MAClB;MACA;MACA;MACA,IAAI,CAACoD,kBAAkB,CAACC,YAAY,CAAC,CAAC;MACtC;MACA;MACA;MACA;MACA,IAAI,CAACR,eAAe,CAAC/I,aAAa,CAACwJ,KAAK,CAACpB,SAAS,GAAG,IAAI,CAACE,yBAAyB;MACnF7T,eAAe,CAAC,MAAM;QAClB,IAAI,CAAC4U,yBAAyB,GAAG,KAAK;QACtC,MAAMI,uBAAuB,GAAG,IAAI,CAACL,wBAAwB;QAC7D,IAAI,CAACA,wBAAwB,GAAG,EAAE;QAClC,KAAK,MAAMM,EAAE,IAAID,uBAAuB,EAAE;UACtCC,EAAE,CAAC,CAAC;QACR;MACJ,CAAC,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAACC;MAAU,CAAC,CAAC;IACpC,CAAC,CAAC;EACN;EACA;EACA/D,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACgE,mBAAmB,GACpB,IAAI,CAAC5E,WAAW,KAAK,YAAY,GAAG,EAAE,GAAG,GAAG,IAAI,CAACwC,iBAAiB,IAAI;IAC1E,IAAI,CAACqC,kBAAkB,GACnB,IAAI,CAAC7E,WAAW,KAAK,YAAY,GAAG,GAAG,IAAI,CAACwC,iBAAiB,IAAI,GAAG,EAAE;EAC9E;AASJ;AAACsC,yBAAA,GAlYKpE,wBAAwB;AAAAtN,eAAA,CAAxBsN,wBAAwB,wBAAAqE,kCAAAhO,iBAAA;EAAA,YAAAA,iBAAA,IA0XyE2J,yBAAwB;AAAA;AAAAtN,eAAA,CA1XzHsN,wBAAwB,8BAhdmD/R,EAAE,CAAAqW,iBAAA;EAAA/N,IAAA,EA20BQyJ,yBAAwB;EAAAxJ,SAAA;EAAA+N,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA30BlCxW,EAAE,CAAA0W,WAAA,CAAAC,GAAA;IAAA;IAAA,IAAAH,EAAA;MAAA,IAAAI,EAAA;MAAF5W,EAAE,CAAA6W,cAAA,CAAAD,EAAA,GAAF5W,EAAE,CAAA8W,WAAA,QAAAL,GAAA,CAAAtB,eAAA,GAAAyB,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,uCAAAX,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFxW,EAAE,CAAAoX,WAAA,8CAAAX,GAAA,CAAApF,WAAA,KA20BwB,YAAO,CAAC,4CAAAoF,GAAA,CAAApF,WAAA,KAAR,YAAO,CAAC;IAAA;EAAA;EAAA7I,MAAA;IAAA6I,WAAA;IAAA0C,UAAA,kCAA8IjT,gBAAgB;EAAA;EAAAuW,OAAA;IAAAnF,mBAAA;EAAA;EAAAzJ,QAAA,GA30BhMzI,EAAE,CAAA0I,kBAAA,CA20Bwf,CAC/jB;IACIC,OAAO,EAAEkE,aAAa;IACtBjE,UAAU,EAAEA,CAAC0O,iBAAiB,EAAEtS,QAAQ,KAAKsS,iBAAiB,IAAItS,QAAQ;IAC1E6D,IAAI,EAAE,CAAC,CAAC,IAAI9H,QAAQ,CAAC,CAAC,EAAE,IAAIC,MAAM,CAACkQ,kBAAkB,CAAC,CAAC,EAAEa,yBAAwB;EACrF,CAAC,CACJ,GAj1BwE/R,EAAE,CAAAyR,0BAAA;EAAA8F,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mCAAArB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFxW,EAAE,CAAA8X,eAAA;MAAF9X,EAAE,CAAA+X,cAAA,eAi1BgS,CAAC;MAj1BnS/X,EAAE,CAAAgY,YAAA,EAi1B6T,CAAC;MAj1BhUhY,EAAE,CAAAiY,YAAA,CAi1BqU,CAAC;MAj1BxUjY,EAAE,CAAAkY,SAAA,YAi1B0nB,CAAC;IAAA;IAAA,IAAA1B,EAAA;MAj1B7nBxW,EAAE,CAAAmY,SAAA,EAi1B4kB,CAAC;MAj1B/kBnY,EAAE,CAAAoY,WAAA,UAAA3B,GAAA,CAAAP,kBAi1B4kB,CAAC,WAAAO,GAAA,CAAAR,mBAAsC,CAAC;IAAA;EAAA;EAAAoC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAEvsB;EAAA,QAAAhT,SAAA,oBAAAA,SAAA,KAn1BiFvF,EAAE,CAAA+I,iBAAA,CAm1BQgJ,wBAAwB,EAAc,CAAC;IACtHzJ,IAAI,EAAErH,SAAS;IACf+H,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEuP,IAAI,EAAE;QAC5C,OAAO,EAAE,6BAA6B;QACtC,mDAAmD,EAAE,8BAA8B;QACnF,iDAAiD,EAAE;MACvD,CAAC;MAAEF,aAAa,EAAEpX,iBAAiB,CAACuX,IAAI;MAAEF,eAAe,EAAEpX,uBAAuB,CAACuX,MAAM;MAAExP,SAAS,EAAE,CAClG;QACIP,OAAO,EAAEkE,aAAa;QACtBjE,UAAU,EAAEA,CAAC0O,iBAAiB,EAAEtS,QAAQ,KAAKsS,iBAAiB,IAAItS,QAAQ;QAC1E6D,IAAI,EAAE,CAAC,CAAC,IAAI9H,QAAQ,CAAC,CAAC,EAAE,IAAIC,MAAM,CAACkQ,kBAAkB,CAAC,CAAC,EAAEa,wBAAwB;MACrF,CAAC,CACJ;MAAE6F,QAAQ,EAAE,shBAAshB;MAAES,MAAM,EAAE,CAAC,upDAAupD;IAAE,CAAC;EACptE,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEhH,WAAW,EAAE,CAAC;MACtD/I,IAAI,EAAElI;IACV,CAAC,CAAC;IAAE2T,UAAU,EAAE,CAAC;MACbzL,IAAI,EAAElI,KAAK;MACX4I,IAAI,EAAE,CAAC;QAAEwL,SAAS,EAAE1T;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoR,mBAAmB,EAAE,CAAC;MACtB5J,IAAI,EAAElH;IACV,CAAC,CAAC;IAAE+T,eAAe,EAAE,CAAC;MAClB7M,IAAI,EAAEjH,SAAS;MACf2H,IAAI,EAAE,CAAC,gBAAgB,EAAE;QAAE2P,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,SAASC,SAASA,CAACvH,WAAW,EAAEwH,SAAS,EAAEC,IAAI,EAAE;EAC7C,MAAMnL,EAAE,GAAGmL,IAAI;EACf,IAAI,CAACnL,EAAE,CAAC6C,qBAAqB,EAAE;IAC3B,OAAO,CAAC;EACZ;EACA,MAAMuI,IAAI,GAAGpL,EAAE,CAAC6C,qBAAqB,CAAC,CAAC;EACvC,IAAIa,WAAW,KAAK,YAAY,EAAE;IAC9B,OAAOwH,SAAS,KAAK,OAAO,GAAGE,IAAI,CAACjL,IAAI,GAAGiL,IAAI,CAAChL,KAAK;EACzD;EACA,OAAO8K,SAAS,KAAK,OAAO,GAAGE,IAAI,CAAC9K,GAAG,GAAG8K,IAAI,CAAC/K,MAAM;AACzD;AACA;AACA;AACA;AACA;AACA,MAAMgL,eAAe,CAAC;EAUlB;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACjR,KAAK,EAAE;IACvB,IAAI,CAACkR,gBAAgB,GAAGlR,KAAK;IAC7B,IAAI9D,YAAY,CAAC8D,KAAK,CAAC,EAAE;MACrB,IAAI,CAACmR,kBAAkB,CAACxR,IAAI,CAACK,KAAK,CAAC;IACvC,CAAC,MACI;MACD;MACA,IAAI,CAACmR,kBAAkB,CAACxR,IAAI,CAAC,IAAI7D,eAAe,CAAC9B,YAAY,CAACgG,KAAK,CAAC,GAAGA,KAAK,GAAGoR,KAAK,CAACxK,IAAI,CAAC5G,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5G;EACJ;EAEA;AACJ;AACA;AACA;EACI,IAAIqR,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACC,qBAAqB;EACrC;EACA,IAAID,oBAAoBA,CAACvD,EAAE,EAAE;IACzB,IAAI,CAACyD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACD,qBAAqB,GAAGxD,EAAE,GACzB,CAAChQ,KAAK,EAAE0T,IAAI,KAAK1D,EAAE,CAAChQ,KAAK,IAAI,IAAI,CAAC4N,cAAc,GAAG,IAAI,CAACA,cAAc,CAACpN,KAAK,GAAG,CAAC,CAAC,EAAEkT,IAAI,CAAC,GACxFtO,SAAS;EACnB;EAEA;EACA,IAAIuO,qBAAqBA,CAACzR,KAAK,EAAE;IAC7B,IAAIA,KAAK,EAAE;MACP,IAAI,CAACuR,YAAY,GAAG,IAAI;MACxB,IAAI,CAACG,SAAS,GAAG1R,KAAK;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAI2R,8BAA8BA,CAAA,EAAG;IACjC,OAAO,IAAI,CAACC,aAAa,CAACC,aAAa;EAC3C;EACA,IAAIF,8BAA8BA,CAAC/F,IAAI,EAAE;IACrC,IAAI,CAACgG,aAAa,CAACC,aAAa,GAAGnX,oBAAoB,CAACkR,IAAI,CAAC;EACjE;EACA;;EAuBAvP,WAAWA,CAAA,EAAG;IAAAI,eAAA,4BA9EMpE,MAAM,CAACiB,gBAAgB,CAAC;IAAAmD,eAAA,oBAChCpE,MAAM,CAACkB,WAAW,CAAC;IAAAkD,eAAA,mBACpBpE,MAAM,CAACmB,eAAe,CAAC;IAAAiD,eAAA,wBAClBpE,MAAM,CAACuD,uBAAuB,CAAC;IAAAa,eAAA,oBACnCpE,MAAM,CAAC0R,wBAAwB,EAAE;MAAE+H,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChE;IAAArV,eAAA,qBACa,IAAI/C,OAAO,CAAC,CAAC;IAC1B;IAAA+C,eAAA,6BACqB,IAAI/C,OAAO,CAAC,CAAC;IAAA+C,eAAA;IAAAA,eAAA;IAAAA,eAAA,qBAgDrB,IAAI,CAAC0U,kBAAkB,CAACxU,IAAI;IACzC;IACAvC,SAAS,CAAC,IAAI,CAAC;IACf;IACAE,QAAQ,CAAC,CAAC;IACV;IACA;IACA;IACAC,SAAS,CAAC,CAAC,CAACwX,IAAI,EAAEC,GAAG,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACF,IAAI,EAAEC,GAAG,CAAC,CAAC;IAC7D;IACAxX,WAAW,CAAC,CAAC,CAAC,CAAC;IACf;IAAAiC,eAAA,kBACU,IAAI;IACd;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,uBACe,KAAK;IAAAA,eAAA,qBACP,IAAI/C,OAAO,CAAC,CAAC;IAEtB,MAAMwL,MAAM,GAAG7M,MAAM,CAACC,MAAM,CAAC;IAC7B,IAAI,CAAC8S,UAAU,CAACvJ,SAAS,CAACwJ,IAAI,IAAI;MAC9B,IAAI,CAAC6G,KAAK,GAAG7G,IAAI;MACjB,IAAI,CAAC8G,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,CAAClV,SAAS,CAACmV,mBAAmB,CAACzV,IAAI,CAACtC,SAAS,CAAC,IAAI,CAACmL,UAAU,CAAC,CAAC,CAAC3D,SAAS,CAACiK,KAAK,IAAI;MACnF,IAAI,CAACJ,cAAc,GAAGI,KAAK;MAC3B,IAAI,IAAI,CAACuG,UAAU,CAACC,SAAS,CAAC/G,MAAM,EAAE;QAClCrG,MAAM,CAACoF,GAAG,CAAC,MAAM,IAAI,CAAC+H,UAAU,CAAC1S,IAAI,CAAC,IAAI,CAAC+L,cAAc,CAAC,CAAC;MAC/D;MACA,IAAI,CAACyG,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,CAAClV,SAAS,CAACF,MAAM,CAAC,IAAI,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACIuQ,gBAAgBA,CAACxB,KAAK,EAAEzC,WAAW,EAAE;IACjC,IAAIyC,KAAK,CAACxN,KAAK,IAAIwN,KAAK,CAACvN,GAAG,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,IAAI,CAACuN,KAAK,CAACxN,KAAK,GAAG,IAAI,CAACoN,cAAc,CAACpN,KAAK,IAAIwN,KAAK,CAACvN,GAAG,GAAG,IAAI,CAACmN,cAAc,CAACnN,GAAG,MAC9E,OAAOhB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMC,KAAK,CAAC,0DAA0D,CAAC;IAC3E;IACA;IACA,MAAM+U,kBAAkB,GAAGzG,KAAK,CAACxN,KAAK,GAAG,IAAI,CAACoN,cAAc,CAACpN,KAAK;IAClE;IACA,MAAMkU,QAAQ,GAAG1G,KAAK,CAACvN,GAAG,GAAGuN,KAAK,CAACxN,KAAK;IACxC;IACA;IACA,IAAImU,SAAS;IACb,IAAIC,QAAQ;IACZ;IACA,KAAK,IAAIzW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuW,QAAQ,EAAEvW,CAAC,EAAE,EAAE;MAC/B,MAAM0W,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC3Q,GAAG,CAAChG,CAAC,GAAGsW,kBAAkB,CAAC;MAC/D,IAAII,IAAI,IAAIA,IAAI,CAACE,SAAS,CAACtH,MAAM,EAAE;QAC/BkH,SAAS,GAAGC,QAAQ,GAAGC,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ;IACA;IACA,KAAK,IAAI5W,CAAC,GAAGuW,QAAQ,GAAG,CAAC,EAAEvW,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpC,MAAM0W,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC3Q,GAAG,CAAChG,CAAC,GAAGsW,kBAAkB,CAAC;MAC/D,IAAII,IAAI,IAAIA,IAAI,CAACE,SAAS,CAACtH,MAAM,EAAE;QAC/BmH,QAAQ,GAAGC,IAAI,CAACE,SAAS,CAACF,IAAI,CAACE,SAAS,CAACtH,MAAM,GAAG,CAAC,CAAC;QACpD;MACJ;IACJ;IACA,OAAOkH,SAAS,IAAIC,QAAQ,GACtB9B,SAAS,CAACvH,WAAW,EAAE,KAAK,EAAEqJ,QAAQ,CAAC,GAAG9B,SAAS,CAACvH,WAAW,EAAE,OAAO,EAAEoJ,SAAS,CAAC,GACpF,CAAC;EACX;EACAK,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACC,OAAO,IAAI,IAAI,CAACxB,YAAY,EAAE;MACnC;MACA;MACA;MACA,MAAMyB,OAAO,GAAG,IAAI,CAACD,OAAO,CAACE,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC;MACtD,IAAI,CAACF,OAAO,EAAE;QACV,IAAI,CAACG,cAAc,CAAC,CAAC;MACzB,CAAC,MACI;QACD,IAAI,CAACC,aAAa,CAACJ,OAAO,CAAC;MAC/B;MACA,IAAI,CAACzB,YAAY,GAAG,KAAK;IAC7B;EACJ;EACApO,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClG,SAAS,CAACG,MAAM,CAAC,CAAC;IACvB,IAAI,CAAC+T,kBAAkB,CAACxR,IAAI,CAACuD,SAAS,CAAC;IACvC,IAAI,CAACiO,kBAAkB,CAAC9T,QAAQ,CAAC,CAAC;IAClC,IAAI,CAACgV,UAAU,CAAChV,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACmI,UAAU,CAAC7F,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC6F,UAAU,CAACnI,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACuU,aAAa,CAACxU,MAAM,CAAC,CAAC;EAC/B;EACA;EACA+U,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACzG,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,CAACwH,cAAc,GAAG,IAAI,CAAChB,KAAK,CAACmB,KAAK,CAAC,IAAI,CAAC3H,cAAc,CAACpN,KAAK,EAAE,IAAI,CAACoN,cAAc,CAACnN,GAAG,CAAC;IAC1F,IAAI,CAAC,IAAI,CAACwU,OAAO,EAAE;MACf;MACA;MACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAACO,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACL,cAAc,CAAC,CAACM,MAAM,CAAC,CAAC1V,KAAK,EAAE0T,IAAI,KAAK;QAC3E,OAAO,IAAI,CAACH,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAACvT,KAAK,EAAE0T,IAAI,CAAC,GAAGA,IAAI;MACpF,CAAC,CAAC;IACN;IACA,IAAI,CAACD,YAAY,GAAG,IAAI;EAC5B;EACA;EACAU,iBAAiBA,CAACwB,KAAK,EAAEC,KAAK,EAAE;IAC5B,IAAID,KAAK,EAAE;MACPA,KAAK,CAACE,UAAU,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,CAACpC,YAAY,GAAG,IAAI;IACxB,OAAOmC,KAAK,GAAGA,KAAK,CAACE,OAAO,CAAC,IAAI,CAAC,GAAGja,EAAE,CAAC,CAAC;EAC7C;EACA;EACAwZ,cAAcA,CAAA,EAAG;IACb,MAAMU,KAAK,GAAG,IAAI,CAAC3B,KAAK,CAAC3G,MAAM;IAC/B,IAAItP,CAAC,GAAG,IAAI,CAAC2W,iBAAiB,CAACrH,MAAM;IACrC,OAAOtP,CAAC,EAAE,EAAE;MACR,MAAM0W,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC3Q,GAAG,CAAChG,CAAC,CAAC;MAC1C0W,IAAI,CAACmB,OAAO,CAAChW,KAAK,GAAG,IAAI,CAAC4N,cAAc,CAACpN,KAAK,GAAGrC,CAAC;MAClD0W,IAAI,CAACmB,OAAO,CAACD,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAACE,gCAAgC,CAACpB,IAAI,CAACmB,OAAO,CAAC;MACnDnB,IAAI,CAACqB,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACAZ,aAAaA,CAACJ,OAAO,EAAE;IACnB,IAAI,CAACpB,aAAa,CAACqC,YAAY,CAACjB,OAAO,EAAE,IAAI,CAACJ,iBAAiB,EAAE,CAACsB,MAAM,EAAEC,sBAAsB,EAAEC,YAAY,KAAK,IAAI,CAACC,oBAAoB,CAACH,MAAM,EAAEE,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAAC1C,IAAI,CAAC;IAC1L;IACAwB,OAAO,CAACsB,qBAAqB,CAAEJ,MAAM,IAAK;MACtC,MAAMvB,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC3Q,GAAG,CAACiS,MAAM,CAACE,YAAY,CAAC;MAC5DzB,IAAI,CAACmB,OAAO,CAACS,SAAS,GAAGL,MAAM,CAAC1C,IAAI;IACxC,CAAC,CAAC;IACF;IACA,MAAMqC,KAAK,GAAG,IAAI,CAAC3B,KAAK,CAAC3G,MAAM;IAC/B,IAAItP,CAAC,GAAG,IAAI,CAAC2W,iBAAiB,CAACrH,MAAM;IACrC,OAAOtP,CAAC,EAAE,EAAE;MACR,MAAM0W,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC3Q,GAAG,CAAChG,CAAC,CAAC;MAC1C0W,IAAI,CAACmB,OAAO,CAAChW,KAAK,GAAG,IAAI,CAAC4N,cAAc,CAACpN,KAAK,GAAGrC,CAAC;MAClD0W,IAAI,CAACmB,OAAO,CAACD,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAACE,gCAAgC,CAACpB,IAAI,CAACmB,OAAO,CAAC;IACvD;EACJ;EACA;EACAC,gCAAgCA,CAACD,OAAO,EAAE;IACtCA,OAAO,CAAC/E,KAAK,GAAG+E,OAAO,CAAChW,KAAK,KAAK,CAAC;IACnCgW,OAAO,CAACU,IAAI,GAAGV,OAAO,CAAChW,KAAK,KAAKgW,OAAO,CAACD,KAAK,GAAG,CAAC;IAClDC,OAAO,CAACW,IAAI,GAAGX,OAAO,CAAChW,KAAK,GAAG,CAAC,KAAK,CAAC;IACtCgW,OAAO,CAACY,GAAG,GAAG,CAACZ,OAAO,CAACW,IAAI;EAC/B;EACAJ,oBAAoBA,CAACH,MAAM,EAAEpW,KAAK,EAAE;IAChC;IACA;IACA;IACA;IACA,OAAO;MACH6W,WAAW,EAAE,IAAI,CAACjD,SAAS;MAC3BoC,OAAO,EAAE;QACLS,SAAS,EAAEL,MAAM,CAAC1C,IAAI;QACtB;QACA;QACAP,eAAe,EAAE,IAAI,CAACC,gBAAgB;QACtCpT,KAAK,EAAE,CAAC,CAAC;QACT+V,KAAK,EAAE,CAAC,CAAC;QACT9E,KAAK,EAAE,KAAK;QACZyF,IAAI,EAAE,KAAK;QACXE,GAAG,EAAE,KAAK;QACVD,IAAI,EAAE;MACV,CAAC;MACD3W;IACJ,CAAC;EACL;EACA,OAAO8W,sBAAsBA,CAACC,SAAS,EAAEf,OAAO,EAAE;IAC9C,OAAO,IAAI;EACf;AAGJ;AAACgB,gBAAA,GAtPK9D,eAAe;AAAAvU,eAAA,CAAfuU,eAAe,wBAAA+D,yBAAA3U,iBAAA;EAAA,YAAAA,iBAAA,IAoPkF4Q,gBAAe;AAAA;AAAAvU,eAAA,CApPhHuU,eAAe,8BA53B4DhZ,EAAE,CAAAqI,iBAAA;EAAAC,IAAA,EAinCQ0Q,gBAAe;EAAAzQ,SAAA;EAAAC,MAAA;IAAAyQ,eAAA;IAAAI,oBAAA;IAAAI,qBAAA;IAAAE,8BAAA;EAAA;EAAAlR,QAAA,GAjnCzBzI,EAAE,CAAA0I,kBAAA,CAinCsT,CAAC;IAAEC,OAAO,EAAE/E,uBAAuB;IAAEoZ,QAAQ,EAAEhZ;EAA6B,CAAC,CAAC;AAAA;AAEvd;EAAA,QAAAuB,SAAA,oBAAAA,SAAA,KAnnCiFvF,EAAE,CAAA+I,iBAAA,CAmnCQiQ,eAAe,EAAc,CAAC;IAC7G1Q,IAAI,EAAEnI,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE/E,uBAAuB;QAAEoZ,QAAQ,EAAEhZ;MAA6B,CAAC;IAC5F,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEiV,eAAe,EAAE,CAAC;MAC1D3Q,IAAI,EAAElI;IACV,CAAC,CAAC;IAAEiZ,oBAAoB,EAAE,CAAC;MACvB/Q,IAAI,EAAElI;IACV,CAAC,CAAC;IAAEqZ,qBAAqB,EAAE,CAAC;MACxBnR,IAAI,EAAElI;IACV,CAAC,CAAC;IAAEuZ,8BAA8B,EAAE,CAAC;MACjCrR,IAAI,EAAElI;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAM6c,2BAA2B,SAAS9L,oBAAoB,CAAC;EAC3D9M,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACAsP,yCAAyCA,CAAC/E,IAAI,EAAE;IAC5C,OAAQ,IAAI,CAACzC,aAAa,CAAC,CAAC,CAACC,aAAa,CAACoE,qBAAqB,CAAC,CAAC,CAAC5B,IAAI,CAAC,GACpE,IAAI,CAAChI,mBAAmB,CAACgI,IAAI,CAAC;EACtC;AAGJ;AAACsO,4BAAA,GAVKD,2BAA2B;AAAAxY,eAAA,CAA3BwY,2BAA2B,wBAAAE,qCAAA/U,iBAAA;EAAA,YAAAA,iBAAA,IAQsE6U,4BAA2B;AAAA;AAAAxY,eAAA,CAR5HwY,2BAA2B,8BAtoCgDjd,EAAE,CAAAqI,iBAAA;EAAAC,IAAA,EA+oCQ2U,4BAA2B;EAAA1U,SAAA;EAAAyO,SAAA;EAAAvO,QAAA,GA/oCrCzI,EAAE,CAAA0I,kBAAA,CA+oCkK,CAAC;IAAEC,OAAO,EAAEuI,kBAAkB;IAAEkM,WAAW,EAAEH;EAA4B,CAAC,CAAC,GA/oC/Ojd,EAAE,CAAAyR,0BAAA;AAAA;AAipCnF;EAAA,QAAAlM,SAAA,oBAAAA,SAAA,KAjpCiFvF,EAAE,CAAA+I,iBAAA,CAipCQkU,2BAA2B,EAAc,CAAC;IACzH3U,IAAI,EAAEnI,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,8BAA8B;MACxCC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAEuI,kBAAkB;QAAEkM,WAAW,EAAEH;MAA4B,CAAC,CAAC;MACtFzE,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA,MAAM6E,0BAA0B,SAASlM,oBAAoB,CAAC;EAC1D9M,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,MAAM6L,QAAQ,GAAG7P,MAAM,CAACqD,QAAQ,CAAC;IACjC,IAAI,CAACqJ,UAAU,GAAG,IAAItM,UAAU,CAACyP,QAAQ,CAACI,eAAe,CAAC;IAC1D,IAAI,CAACnD,cAAc,GAAG+C,QAAQ;EAClC;EACAyD,yCAAyCA,CAAC/E,IAAI,EAAE;IAC5C,OAAO,IAAI,CAACzC,aAAa,CAAC,CAAC,CAACC,aAAa,CAACoE,qBAAqB,CAAC,CAAC,CAAC5B,IAAI,CAAC;EAC3E;AAGJ;AAAC0O,2BAAA,GAZKD,0BAA0B;AAAA5Y,eAAA,CAA1B4Y,0BAA0B,wBAAAE,oCAAAnV,iBAAA;EAAA,YAAAA,iBAAA,IAUuEiV,2BAA0B;AAAA;AAAA5Y,eAAA,CAV3H4Y,0BAA0B,8BA/pCiDrd,EAAE,CAAAqI,iBAAA;EAAAC,IAAA,EA0qCQ+U,2BAA0B;EAAA9U,SAAA;EAAAE,QAAA,GA1qCpCzI,EAAE,CAAA0I,kBAAA,CA0qC0H,CAAC;IAAEC,OAAO,EAAEuI,kBAAkB;IAAEkM,WAAW,EAAEC;EAA2B,CAAC,CAAC,GA1qCtMrd,EAAE,CAAAyR,0BAAA;AAAA;AA4qCnF;EAAA,QAAAlM,SAAA,oBAAAA,SAAA,KA5qCiFvF,EAAE,CAAA+I,iBAAA,CA4qCQsU,0BAA0B,EAAc,CAAC;IACxH/U,IAAI,EAAEnI,SAAS;IACf6I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAEuI,kBAAkB;QAAEkM,WAAW,EAAEC;MAA2B,CAAC;IACxF,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMG,mBAAmB,CAAC;AAIzBC,oBAAA,GAJKD,mBAAmB;AAAA/Y,eAAA,CAAnB+Y,mBAAmB,wBAAAE,6BAAAtV,iBAAA;EAAA,YAAAA,iBAAA,IAC8EoV,oBAAmB;AAAA;AAAA/Y,eAAA,CADpH+Y,mBAAmB,8BAprCwDxd,EAAE,CAAA2d,gBAAA;EAAArV,IAAA,EAsrCqBkV,oBAAmB;EAAAI,OAAA,GAAY/Q,aAAa;EAAAgR,OAAA,GAAahR,aAAa;AAAA;AAAApI,eAAA,CAFxK+Y,mBAAmB,8BAprCwDxd,EAAE,CAAA8d,gBAAA;AAyrCnF;EAAA,QAAAvY,SAAA,oBAAAA,SAAA,KAzrCiFvF,EAAE,CAAA+I,iBAAA,CAyrCQyU,mBAAmB,EAAc,CAAC;IACjHlV,IAAI,EAAE7G,QAAQ;IACduH,IAAI,EAAE,CAAC;MACC6U,OAAO,EAAE,CAAChR,aAAa,CAAC;MACxB+Q,OAAO,EAAE,CAAC/Q,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAMkR,eAAe,CAAC;AAarBC,gBAAA,GAbKD,eAAe;AAAAtZ,eAAA,CAAfsZ,eAAe,wBAAAE,yBAAA7V,iBAAA;EAAA,YAAAA,iBAAA,IACkF2V,gBAAe;AAAA;AAAAtZ,eAAA,CADhHsZ,eAAe,8BAnsC4D/d,EAAE,CAAA2d,gBAAA;EAAArV,IAAA,EAqsCqByV,gBAAe;EAAAH,OAAA,GAAYra,UAAU,EAAEia,mBAAmB,EAAEzL,wBAAwB,EAChLhK,yBAAyB,EACzBiR,eAAe,EACfqE,0BAA0B,EAC1BJ,2BAA2B;EAAAY,OAAA,GAAata,UAAU,EAAEia,mBAAmB,EAAEzV,yBAAyB,EAClGiR,eAAe,EACfjH,wBAAwB,EACxBsL,0BAA0B,EAC1BJ,2BAA2B;AAAA;AAAAxY,eAAA,CAVjCsZ,eAAe,8BAnsC4D/d,EAAE,CAAA8d,gBAAA;EAAAF,OAAA,GA8sCgDra,UAAU,EACjIia,mBAAmB,EAAEja,UAAU,EAAEia,mBAAmB;AAAA;AAEhE;EAAA,QAAAjY,SAAA,oBAAAA,SAAA,KAjtCiFvF,EAAE,CAAA+I,iBAAA,CAitCQgV,eAAe,EAAc,CAAC;IAC7GzV,IAAI,EAAE7G,QAAQ;IACduH,IAAI,EAAE,CAAC;MACC4U,OAAO,EAAE,CACLra,UAAU,EACVia,mBAAmB,EACnBzL,wBAAwB,EACxBhK,yBAAyB,EACzBiR,eAAe,EACfqE,0BAA0B,EAC1BJ,2BAA2B,CAC9B;MACDY,OAAO,EAAE,CACLta,UAAU,EACVia,mBAAmB,EACnBzV,yBAAyB,EACzBiR,eAAe,EACfjH,wBAAwB,EACxBsL,0BAA0B,EAC1BJ,2BAA2B;IAEnC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASlV,yBAAyB,EAAE8E,aAAa,EAAE2Q,mBAAmB,EAAExE,eAAe,EAAEjH,wBAAwB,EAAEZ,oBAAoB,EAAE8L,2BAA2B,EAAEI,0BAA0B,EAAEpO,mBAAmB,EAAE9F,mBAAmB,EAAE/E,8BAA8B,EAAEgF,gBAAgB,EAAE2U,eAAe,EAAE7M,kBAAkB,EAAE/M,uBAAuB,EAAE+K,aAAa,EAAEtH,sCAAsC;AACjZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}