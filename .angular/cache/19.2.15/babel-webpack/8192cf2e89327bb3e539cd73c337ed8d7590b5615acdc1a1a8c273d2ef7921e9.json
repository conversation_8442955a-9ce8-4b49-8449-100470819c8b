{"ast": null, "code": "var _SwuiTdListWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./list.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./list.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdListWidgetComponent = (_SwuiTdListWidgetComponent = class SwuiTdListWidgetComponent {\n  constructor({\n    schema,\n    row,\n    value\n  }) {\n    var _schema$td, _schema$td2;\n    this.value = ((_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.valueFn) && schema.td.valueFn(row, schema) || value;\n    const key = (_schema$td2 = schema.td) !== null && _schema$td2 !== void 0 && _schema$td2.arrayKey ? schema.td.arrayKey : 'id';\n    this.value = this.value.reduce((acc, curr) => {\n      if (curr && typeof curr === 'object') {\n        if (curr[key]) {\n          acc.push(curr[key]);\n        }\n      } else {\n        acc.push(curr);\n      }\n      return acc;\n    }, []);\n  }\n}, _SwuiTdListWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdListWidgetComponent);\nSwuiTdListWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-list-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdListWidgetComponent);\nexport { SwuiTdListWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdListWidgetComponent", "_SwuiTdListWidgetComponent", "constructor", "schema", "row", "value", "_schema$td", "_schema$td2", "td", "valueFn", "key", "<PERSON><PERSON><PERSON>", "reduce", "acc", "curr", "push", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/list/list.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./list.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./list.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdListWidgetComponent = class SwuiTdListWidgetComponent {\n    constructor({ schema, row, value }) {\n        this.value = (schema.td?.valueFn && schema.td.valueFn(row, schema)) || value;\n        const key = schema.td?.arrayKey ? schema.td.arrayKey : 'id';\n        this.value = this.value.reduce((acc, curr) => {\n            if (curr && typeof curr === 'object') {\n                if (curr[key]) {\n                    acc.push(curr[key]);\n                }\n            }\n            else {\n                acc.push(curr);\n            }\n            return acc;\n        }, []);\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdListWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-list-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdListWidgetComponent);\nexport { SwuiTdListWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,yBAAyB,IAAAC,0BAAA,GAAG,MAAMD,yBAAyB,CAAC;EAC5DE,WAAWA,CAAC;IAAEC,MAAM;IAAEC,GAAG;IAAEC;EAAM,CAAC,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA;IAChC,IAAI,CAACF,KAAK,GAAI,EAAAC,UAAA,GAAAH,MAAM,CAACK,EAAE,cAAAF,UAAA,uBAATA,UAAA,CAAWG,OAAO,KAAIN,MAAM,CAACK,EAAE,CAACC,OAAO,CAACL,GAAG,EAAED,MAAM,CAAC,IAAKE,KAAK;IAC5E,MAAMK,GAAG,GAAG,CAAAH,WAAA,GAAAJ,MAAM,CAACK,EAAE,cAAAD,WAAA,eAATA,WAAA,CAAWI,QAAQ,GAAGR,MAAM,CAACK,EAAE,CAACG,QAAQ,GAAG,IAAI;IAC3D,IAAI,CAACN,KAAK,GAAG,IAAI,CAACA,KAAK,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MAC1C,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAClC,IAAIA,IAAI,CAACJ,GAAG,CAAC,EAAE;UACXG,GAAG,CAACE,IAAI,CAACD,IAAI,CAACJ,GAAG,CAAC,CAAC;QACvB;MACJ,CAAC,MACI;QACDG,GAAG,CAACE,IAAI,CAACD,IAAI,CAAC;MAClB;MACA,OAAOD,GAAG;IACd,CAAC,EAAE,EAAE,CAAC;EACV;AAIJ,CAAC,EAHYZ,0BAAA,CAAKe,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEnB,MAAM;IAAEsB,IAAI,EAAE,CAACrB,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,0BAAA,CACJ;AACDD,yBAAyB,GAAGN,UAAU,CAAC,CACnCG,SAAS,CAAC;EACNwB,QAAQ,EAAE,yBAAyB;EACnCC,QAAQ,EAAE3B,oBAAoB;EAC9B4B,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC5B,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEI,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}