{"ast": null, "code": "export var observable = function () {\n  return typeof Symbol === 'function' && Symbol.observable || '@@observable';\n}();\n//# sourceMappingURL=observable.js.map", "map": {"version": 3, "names": ["observable", "Symbol"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/symbol/observable.js"], "sourcesContent": ["export var observable = (function () { return (typeof Symbol === 'function' && Symbol.observable) || '@@observable'; })();\n//# sourceMappingURL=observable.js.map"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAI,YAAY;EAAE,OAAQ,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACD,UAAU,IAAK,cAAc;AAAE,CAAC,CAAE,CAAC;AACzH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}