{"ast": null, "code": "var _CustomPeriodComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./custom-period.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./custom-period.component.scss?ngResource\";\nimport { Component, EventEmitter, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { CUSTOM_PERIODS } from './custom-period.interface';\nimport { SwuiTopFilterDataService } from '../../swui-schema-top-filter/top-filter-data.service';\nlet CustomPeriodComponent = (_CustomPeriodComponent = class CustomPeriodComponent {\n  constructor(filter) {\n    this.hideCustomPeriods = false;\n    this.smallCustomPeriodsButton = false;\n    this.disabled = false;\n    this.title = 'COMPONENTS.DATERANGE.customPeriod';\n    this.periods = CUSTOM_PERIODS;\n    this.periodChange = new EventEmitter();\n    this.destroy$ = new Subject();\n    if (filter) {\n      filter.onReset.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.currentPeriod = undefined;\n      });\n    }\n  }\n  onClick(event, period) {\n    event.preventDefault();\n    if (this.trigger) {\n      this.trigger.closeMenu();\n    }\n    this.currentPeriod = period;\n    this.periodChange.emit(period.fn());\n  }\n  reset() {\n    this.currentPeriod = undefined;\n  }\n  ngOnDestroy() {\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n}, _CustomPeriodComponent.ctorParameters = () => [{\n  type: SwuiTopFilterDataService,\n  decorators: [{\n    type: Optional\n  }]\n}], _CustomPeriodComponent.propDecorators = {\n  hideCustomPeriods: [{\n    type: Input\n  }],\n  smallCustomPeriodsButton: [{\n    type: Input\n  }],\n  disabled: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  periods: [{\n    type: Input\n  }],\n  periodChange: [{\n    type: Output\n  }],\n  trigger: [{\n    type: ViewChild,\n    args: [MatMenuTrigger]\n  }]\n}, _CustomPeriodComponent);\nCustomPeriodComponent = __decorate([Component({\n  selector: 'lib-swui-custom-period',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CustomPeriodComponent);\nexport { CustomPeriodComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "EventEmitter", "Input", "Optional", "Output", "ViewChild", "MatMenuTrigger", "Subject", "takeUntil", "CUSTOM_PERIODS", "SwuiTopFilterDataService", "CustomPeriodComponent", "_CustomPeriodComponent", "constructor", "filter", "hideCustomPeriods", "smallCustomPeriodsButton", "disabled", "title", "periods", "periodChange", "destroy$", "onReset", "pipe", "subscribe", "currentPeriod", "undefined", "onClick", "event", "period", "preventDefault", "trigger", "closeMenu", "emit", "fn", "reset", "ngOnDestroy", "next", "complete", "ctorParameters", "type", "decorators", "propDecorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-range/custom-period/custom-period.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./custom-period.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./custom-period.component.scss?ngResource\";\nimport { Component, EventEmitter, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { CUSTOM_PERIODS } from './custom-period.interface';\nimport { SwuiTopFilterDataService } from '../../swui-schema-top-filter/top-filter-data.service';\nlet CustomPeriodComponent = class CustomPeriodComponent {\n    constructor(filter) {\n        this.hideCustomPeriods = false;\n        this.smallCustomPeriodsButton = false;\n        this.disabled = false;\n        this.title = 'COMPONENTS.DATERANGE.customPeriod';\n        this.periods = CUSTOM_PERIODS;\n        this.periodChange = new EventEmitter();\n        this.destroy$ = new Subject();\n        if (filter) {\n            filter.onReset\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(() => {\n                this.currentPeriod = undefined;\n            });\n        }\n    }\n    onClick(event, period) {\n        event.preventDefault();\n        if (this.trigger) {\n            this.trigger.closeMenu();\n        }\n        this.currentPeriod = period;\n        this.periodChange.emit(period.fn());\n    }\n    reset() {\n        this.currentPeriod = undefined;\n    }\n    ngOnDestroy() {\n        this.destroy$.next(undefined);\n        this.destroy$.complete();\n    }\n    static { this.ctorParameters = () => [\n        { type: SwuiTopFilterDataService, decorators: [{ type: Optional }] }\n    ]; }\n    static { this.propDecorators = {\n        hideCustomPeriods: [{ type: Input }],\n        smallCustomPeriodsButton: [{ type: Input }],\n        disabled: [{ type: Input }],\n        title: [{ type: Input }],\n        periods: [{ type: Input }],\n        periodChange: [{ type: Output }],\n        trigger: [{ type: ViewChild, args: [MatMenuTrigger,] }]\n    }; }\n};\nCustomPeriodComponent = __decorate([\n    Component({\n        selector: 'lib-swui-custom-period',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], CustomPeriodComponent);\nexport { CustomPeriodComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,SAASC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAC3F,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,wBAAwB,QAAQ,sDAAsD;AAC/F,IAAIC,qBAAqB,IAAAC,sBAAA,GAAG,MAAMD,qBAAqB,CAAC;EACpDE,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,KAAK,GAAG,mCAAmC;IAChD,IAAI,CAACC,OAAO,GAAGV,cAAc;IAC7B,IAAI,CAACW,YAAY,GAAG,IAAInB,YAAY,CAAC,CAAC;IACtC,IAAI,CAACoB,QAAQ,GAAG,IAAId,OAAO,CAAC,CAAC;IAC7B,IAAIO,MAAM,EAAE;MACRA,MAAM,CAACQ,OAAO,CACTC,IAAI,CAACf,SAAS,CAAC,IAAI,CAACa,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAAC,MAAM;QACjB,IAAI,CAACC,aAAa,GAAGC,SAAS;MAClC,CAAC,CAAC;IACN;EACJ;EACAC,OAAOA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACnBD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,SAAS,CAAC,CAAC;IAC5B;IACA,IAAI,CAACP,aAAa,GAAGI,MAAM;IAC3B,IAAI,CAACT,YAAY,CAACa,IAAI,CAACJ,MAAM,CAACK,EAAE,CAAC,CAAC,CAAC;EACvC;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACV,aAAa,GAAGC,SAAS;EAClC;EACAU,WAAWA,CAAA,EAAG;IACV,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAACX,SAAS,CAAC;IAC7B,IAAI,CAACL,QAAQ,CAACiB,QAAQ,CAAC,CAAC;EAC5B;AAaJ,CAAC,EAZY1B,sBAAA,CAAK2B,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE9B,wBAAwB;EAAE+B,UAAU,EAAE,CAAC;IAAED,IAAI,EAAErC;EAAS,CAAC;AAAE,CAAC,CACvE,EACQS,sBAAA,CAAK8B,cAAc,GAAG;EAC3B3B,iBAAiB,EAAE,CAAC;IAAEyB,IAAI,EAAEtC;EAAM,CAAC,CAAC;EACpCc,wBAAwB,EAAE,CAAC;IAAEwB,IAAI,EAAEtC;EAAM,CAAC,CAAC;EAC3Ce,QAAQ,EAAE,CAAC;IAAEuB,IAAI,EAAEtC;EAAM,CAAC,CAAC;EAC3BgB,KAAK,EAAE,CAAC;IAAEsB,IAAI,EAAEtC;EAAM,CAAC,CAAC;EACxBiB,OAAO,EAAE,CAAC;IAAEqB,IAAI,EAAEtC;EAAM,CAAC,CAAC;EAC1BkB,YAAY,EAAE,CAAC;IAAEoB,IAAI,EAAEpC;EAAO,CAAC,CAAC;EAChC2B,OAAO,EAAE,CAAC;IAAES,IAAI,EAAEnC,SAAS;IAAEsC,IAAI,EAAE,CAACrC,cAAc;EAAG,CAAC;AAC1D,CAAC,EAAAM,sBAAA,CACJ;AACDD,qBAAqB,GAAGd,UAAU,CAAC,CAC/BG,SAAS,CAAC;EACN4C,QAAQ,EAAE,wBAAwB;EAClCC,QAAQ,EAAE/C,oBAAoB;EAC9BgD,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAChD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEY,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}