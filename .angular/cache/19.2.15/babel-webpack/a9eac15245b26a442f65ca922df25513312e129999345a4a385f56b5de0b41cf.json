{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _CdkCellDef, _CdkHeaderCellDef, _CdkFooterCellDef, _CdkColumnDef, _CdkHeaderCell, _CdkFooterCell, _CdkCell, _CoalescedStyleScheduler2, _BaseRowDef, _CdkHeaderRowDef, _CdkFooterRowDef, _CdkRowDef, _CdkCellOutlet, _CdkHeaderRow, _CdkFooterRow, _CdkRow, _CdkNoDataRow, _CdkRecycleRows, _DataRowOutlet, _HeaderRowOutlet, _FooterRowOutlet, _NoDataRowOutlet, _CdkTable, _CdkTextColumn, _CdkTableModule;\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]], \"*\"];\nconst _c1 = [\"caption\", \"colgroup, col\", \"*\"];\nfunction _CdkTable_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction _CdkTable_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 0);\n    i0.ɵɵelementContainer(1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"tbody\", 0);\n    i0.ɵɵelementContainer(3, 2)(4, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"tfoot\", 0);\n    i0.ɵɵelementContainer(6, 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction _CdkTable_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 1)(1, 2)(2, 3)(3, 4);\n  }\n}\nfunction _CdkTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction _CdkTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dataAccessor(data_r2, ctx_r0.name), \" \");\n  }\n}\nimport { i as isDataSource } from './data-source-D34wiQZj.mjs';\nexport { D as DataSource } from './data-source-D34wiQZj.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, booleanAttribute, Input, ContentChild, ElementRef, NgZone, Injectable, IterableDiffers, ViewContainerRef, Component, ChangeDetectionStrategy, ViewEncapsulation, afterNextRender, ChangeDetectorRef, EventEmitter, Injector, HostAttributeToken, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, isObservable, of } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { b as _VIEW_REPEATER_STRATEGY, _ as _RecycleViewRepeaterStrategy, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-D_JReLI1.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { ViewportRuler, ScrollingModule } from './scrolling.mjs';\nimport { c as coerceBooleanProperty } from './boolean-property-DaaVhX5A.mjs';\nimport './element-x4z00URv.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nconst CDK_TABLE = new InjectionToken('CDK_TABLE');\n/** Injection token that can be used to specify the text column options. */\nconst TEXT_COLUMN_OPTIONS = new InjectionToken('text-column-options');\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass CdkCellDef {\n  constructor() {\n    /** @docs-private */\n    _defineProperty(this, \"template\", inject(TemplateRef));\n  }\n}\n_CdkCellDef = CdkCellDef;\n_defineProperty(CdkCellDef, \"\\u0275fac\", function _CdkCellDef_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkCellDef)();\n});\n_defineProperty(CdkCellDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkCellDef,\n  selectors: [[\"\", \"cdkCellDef\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCellDef]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass CdkHeaderCellDef {\n  constructor() {\n    /** @docs-private */\n    _defineProperty(this, \"template\", inject(TemplateRef));\n  }\n}\n_CdkHeaderCellDef = CdkHeaderCellDef;\n_defineProperty(CdkHeaderCellDef, \"\\u0275fac\", function _CdkHeaderCellDef_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkHeaderCellDef)();\n});\n_defineProperty(CdkHeaderCellDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkHeaderCellDef,\n  selectors: [[\"\", \"cdkHeaderCellDef\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkHeaderCellDef]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass CdkFooterCellDef {\n  constructor() {\n    /** @docs-private */\n    _defineProperty(this, \"template\", inject(TemplateRef));\n  }\n}\n_CdkFooterCellDef = CdkFooterCellDef;\n_defineProperty(CdkFooterCellDef, \"\\u0275fac\", function _CdkFooterCellDef_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkFooterCellDef)();\n});\n_defineProperty(CdkFooterCellDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkFooterCellDef,\n  selectors: [[\"\", \"cdkFooterCellDef\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkFooterCellDef]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\nclass CdkColumnDef {\n  /** Unique name for this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._setNameInput(name);\n  }\n  /** Whether the cell is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  /**\n   * Whether this column should be sticky positioned on the end of the row. Should make sure\n   * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n   * has been changed.\n   */\n  get stickyEnd() {\n    return this._stickyEnd;\n  }\n  set stickyEnd(value) {\n    if (value !== this._stickyEnd) {\n      this._stickyEnd = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  constructor() {\n    _defineProperty(this, \"_table\", inject(CDK_TABLE, {\n      optional: true\n    }));\n    _defineProperty(this, \"_hasStickyChanged\", false);\n    _defineProperty(this, \"_name\", void 0);\n    _defineProperty(this, \"_sticky\", false);\n    _defineProperty(this, \"_stickyEnd\", false);\n    /** @docs-private */\n    _defineProperty(this, \"cell\", void 0);\n    /** @docs-private */\n    _defineProperty(this, \"headerCell\", void 0);\n    /** @docs-private */\n    _defineProperty(this, \"footerCell\", void 0);\n    /**\n     * Transformed version of the column name that can be used as part of a CSS classname. Excludes\n     * all non-alphanumeric characters and the special characters '-' and '_'. Any characters that\n     * do not match are replaced by the '-' character.\n     */\n    _defineProperty(this, \"cssClassFriendlyName\", void 0);\n    /**\n     * Class name for cells in this column.\n     * @docs-private\n     */\n    _defineProperty(this, \"_columnCssClassName\", void 0);\n  }\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n  /**\n   * Overridable method that sets the css classes that will be added to every cell in this\n   * column.\n   * In the future, columnCssClassName will change from type string[] to string and this\n   * will set a single string value.\n   * @docs-private\n   */\n  _updateColumnCssClassName() {\n    this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setNameInput(value) {\n    // If the directive is set without a name (updated programmatically), then this setter will\n    // trigger with an empty string and should not overwrite the programmatically set value.\n    if (value) {\n      this._name = value;\n      this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n      this._updateColumnCssClassName();\n    }\n  }\n}\n_CdkColumnDef = CdkColumnDef;\n_defineProperty(CdkColumnDef, \"\\u0275fac\", function _CdkColumnDef_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkColumnDef)();\n});\n_defineProperty(CdkColumnDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkColumnDef,\n  selectors: [[\"\", \"cdkColumnDef\", \"\"]],\n  contentQueries: function _CdkColumnDef_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, CdkCellDef, 5);\n      i0.ɵɵcontentQuery(dirIndex, CdkHeaderCellDef, 5);\n      i0.ɵɵcontentQuery(dirIndex, CdkFooterCellDef, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerCell = _t.first);\n    }\n  },\n  inputs: {\n    name: [0, \"cdkColumnDef\", \"name\"],\n    sticky: [2, \"sticky\", \"sticky\", booleanAttribute],\n    stickyEnd: [2, \"stickyEnd\", \"stickyEnd\", booleanAttribute]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n    useExisting: _CdkColumnDef\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkColumnDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkColumnDef]',\n      providers: [{\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: CdkColumnDef\n      }]\n    }]\n  }], () => [], {\n    name: [{\n      type: Input,\n      args: ['cdkColumnDef']\n    }],\n    sticky: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stickyEnd: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    cell: [{\n      type: ContentChild,\n      args: [CdkCellDef]\n    }],\n    headerCell: [{\n      type: ContentChild,\n      args: [CdkHeaderCellDef]\n    }],\n    footerCell: [{\n      type: ContentChild,\n      args: [CdkFooterCellDef]\n    }]\n  });\n})();\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nclass BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n  }\n}\n/** Header cell template container that adds the right classes and role. */\nclass CdkHeaderCell extends BaseCdkCell {\n  constructor() {\n    super(inject(CdkColumnDef), inject(ElementRef));\n  }\n}\n_CdkHeaderCell = CdkHeaderCell;\n_defineProperty(CdkHeaderCell, \"\\u0275fac\", function _CdkHeaderCell_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkHeaderCell)();\n});\n_defineProperty(CdkHeaderCell, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkHeaderCell,\n  selectors: [[\"cdk-header-cell\"], [\"th\", \"cdk-header-cell\", \"\"]],\n  hostAttrs: [\"role\", \"columnheader\", 1, \"cdk-header-cell\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-header-cell, th[cdk-header-cell]',\n      host: {\n        'class': 'cdk-header-cell',\n        'role': 'columnheader'\n      }\n    }]\n  }], () => [], null);\n})();\n/** Footer cell template container that adds the right classes and role. */\nclass CdkFooterCell extends BaseCdkCell {\n  constructor() {\n    var _columnDef$_table;\n    const columnDef = inject(CdkColumnDef);\n    const elementRef = inject(ElementRef);\n    super(columnDef, elementRef);\n    const role = (_columnDef$_table = columnDef._table) === null || _columnDef$_table === void 0 ? void 0 : _columnDef$_table._getCellRole();\n    if (role) {\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n}\n_CdkFooterCell = CdkFooterCell;\n_defineProperty(CdkFooterCell, \"\\u0275fac\", function _CdkFooterCell_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkFooterCell)();\n});\n_defineProperty(CdkFooterCell, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkFooterCell,\n  selectors: [[\"cdk-footer-cell\"], [\"td\", \"cdk-footer-cell\", \"\"]],\n  hostAttrs: [1, \"cdk-footer-cell\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-footer-cell, td[cdk-footer-cell]',\n      host: {\n        'class': 'cdk-footer-cell'\n      }\n    }]\n  }], () => [], null);\n})();\n/** Cell template container that adds the right classes and role. */\nclass CdkCell extends BaseCdkCell {\n  constructor() {\n    var _columnDef$_table2;\n    const columnDef = inject(CdkColumnDef);\n    const elementRef = inject(ElementRef);\n    super(columnDef, elementRef);\n    const role = (_columnDef$_table2 = columnDef._table) === null || _columnDef$_table2 === void 0 ? void 0 : _columnDef$_table2._getCellRole();\n    if (role) {\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n}\n_CdkCell = CdkCell;\n_defineProperty(CdkCell, \"\\u0275fac\", function _CdkCell_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkCell)();\n});\n_defineProperty(CdkCell, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkCell,\n  selectors: [[\"cdk-cell\"], [\"td\", \"cdk-cell\", \"\"]],\n  hostAttrs: [1, \"cdk-cell\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-cell, td[cdk-cell]',\n      host: {\n        'class': 'cdk-cell'\n      }\n    }]\n  }], () => [], null);\n})();\n\n/**\n * @docs-private\n */\nclass _Schedule {\n  constructor() {\n    _defineProperty(this, \"tasks\", []);\n    _defineProperty(this, \"endTasks\", []);\n  }\n}\n/** Injection token used to provide a coalesced style scheduler. */\nconst _COALESCED_STYLE_SCHEDULER = new InjectionToken('_COALESCED_STYLE_SCHEDULER');\n/**\n * Allows grouping up CSSDom mutations after the current execution context.\n * This can significantly improve performance when separate consecutive functions are\n * reading from the CSSDom and then mutating it.\n *\n * @docs-private\n */\nclass _CoalescedStyleScheduler {\n  constructor() {\n    _defineProperty(this, \"_currentSchedule\", null);\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n  }\n  /**\n   * Schedules the specified task to run at the end of the current VM turn.\n   */\n  schedule(task) {\n    this._createScheduleIfNeeded();\n    this._currentSchedule.tasks.push(task);\n  }\n  /**\n   * Schedules the specified task to run after other scheduled tasks at the end of the current\n   * VM turn.\n   */\n  scheduleEnd(task) {\n    this._createScheduleIfNeeded();\n    this._currentSchedule.endTasks.push(task);\n  }\n  _createScheduleIfNeeded() {\n    if (this._currentSchedule) {\n      return;\n    }\n    this._currentSchedule = new _Schedule();\n    this._ngZone.runOutsideAngular(() =>\n    // TODO(mmalerba): Scheduling this using something that runs less frequently\n    //  (e.g. requestAnimationFrame, setTimeout, etc.) causes noticeable jank with the column\n    //  resizer. We should audit the usages of schedule / scheduleEnd in that component and see\n    //  if we can refactor it so that we don't need to flush the tasks quite so frequently.\n    queueMicrotask(() => {\n      while (this._currentSchedule.tasks.length || this._currentSchedule.endTasks.length) {\n        const schedule = this._currentSchedule;\n        // Capture new tasks scheduled by the current set of tasks.\n        this._currentSchedule = new _Schedule();\n        for (const task of schedule.tasks) {\n          task();\n        }\n        for (const task of schedule.endTasks) {\n          task();\n        }\n      }\n      this._currentSchedule = null;\n    }));\n  }\n}\n_CoalescedStyleScheduler2 = _CoalescedStyleScheduler;\n_defineProperty(_CoalescedStyleScheduler, \"\\u0275fac\", function _CoalescedStyleScheduler2_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CoalescedStyleScheduler2)();\n});\n_defineProperty(_CoalescedStyleScheduler, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _CoalescedStyleScheduler2,\n  factory: _CoalescedStyleScheduler2.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CoalescedStyleScheduler, [{\n    type: Injectable\n  }], () => [], null);\n})();\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nconst CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nclass BaseRowDef {\n  constructor() {\n    _defineProperty(this, \"template\", inject(TemplateRef));\n    _defineProperty(this, \"_differs\", inject(IterableDiffers));\n    /** The columns to be displayed on this row. */\n    _defineProperty(this, \"columns\", void 0);\n    /** Differ used to check if any changes were made to the columns. */\n    _defineProperty(this, \"_columnsDiffer\", void 0);\n  }\n  ngOnChanges(changes) {\n    // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n    // of the columns property or an empty array if none is provided.\n    if (!this._columnsDiffer) {\n      const columns = changes['columns'] && changes['columns'].currentValue || [];\n      this._columnsDiffer = this._differs.find(columns).create();\n      this._columnsDiffer.diff(columns);\n    }\n  }\n  /**\n   * Returns the difference between the current columns and the columns from the last diff, or null\n   * if there is no difference.\n   */\n  getColumnsDiff() {\n    return this._columnsDiffer.diff(this.columns);\n  }\n  /** Gets this row def's relevant cell template from the provided column def. */\n  extractCellTemplate(column) {\n    if (this instanceof CdkHeaderRowDef) {\n      return column.headerCell.template;\n    }\n    if (this instanceof CdkFooterRowDef) {\n      return column.footerCell.template;\n    } else {\n      return column.cell.template;\n    }\n  }\n}\n_BaseRowDef = BaseRowDef;\n_defineProperty(BaseRowDef, \"\\u0275fac\", function _BaseRowDef_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BaseRowDef)();\n});\n_defineProperty(BaseRowDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _BaseRowDef,\n  features: [i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseRowDef, [{\n    type: Directive\n  }], () => [], null);\n})();\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass CdkHeaderRowDef extends BaseRowDef {\n  /** Whether the row is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  constructor() {\n    super(inject(TemplateRef), inject(IterableDiffers));\n    _defineProperty(this, \"_table\", inject(CDK_TABLE, {\n      optional: true\n    }));\n    _defineProperty(this, \"_hasStickyChanged\", false);\n    _defineProperty(this, \"_sticky\", false);\n  }\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n  }\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n}\n_CdkHeaderRowDef = CdkHeaderRowDef;\n_defineProperty(CdkHeaderRowDef, \"\\u0275fac\", function _CdkHeaderRowDef_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkHeaderRowDef)();\n});\n_defineProperty(CdkHeaderRowDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkHeaderRowDef,\n  selectors: [[\"\", \"cdkHeaderRowDef\", \"\"]],\n  inputs: {\n    columns: [0, \"cdkHeaderRowDef\", \"columns\"],\n    sticky: [2, \"cdkHeaderRowDefSticky\", \"sticky\", booleanAttribute]\n  },\n  features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkHeaderRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkHeaderRowDef'\n      }]\n    }]\n  }], () => [], {\n    sticky: [{\n      type: Input,\n      args: [{\n        alias: 'cdkHeaderRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass CdkFooterRowDef extends BaseRowDef {\n  /** Whether the row is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  constructor() {\n    super(inject(TemplateRef), inject(IterableDiffers));\n    _defineProperty(this, \"_table\", inject(CDK_TABLE, {\n      optional: true\n    }));\n    _defineProperty(this, \"_hasStickyChanged\", false);\n    _defineProperty(this, \"_sticky\", false);\n  }\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n  }\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n}\n_CdkFooterRowDef = CdkFooterRowDef;\n_defineProperty(CdkFooterRowDef, \"\\u0275fac\", function _CdkFooterRowDef_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkFooterRowDef)();\n});\n_defineProperty(CdkFooterRowDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkFooterRowDef,\n  selectors: [[\"\", \"cdkFooterRowDef\", \"\"]],\n  inputs: {\n    columns: [0, \"cdkFooterRowDef\", \"columns\"],\n    sticky: [2, \"cdkFooterRowDefSticky\", \"sticky\", booleanAttribute]\n  },\n  features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkFooterRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkFooterRowDef'\n      }]\n    }]\n  }], () => [], {\n    sticky: [{\n      type: Input,\n      args: [{\n        alias: 'cdkFooterRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass CdkRowDef extends BaseRowDef {\n  constructor() {\n    // TODO(andrewseguin): Add an input for providing a switch function to determine\n    //   if this template should be used.\n    super(inject(TemplateRef), inject(IterableDiffers));\n    _defineProperty(this, \"_table\", inject(CDK_TABLE, {\n      optional: true\n    }));\n    /**\n     * Function that should return true if this row template should be used for the provided index\n     * and row data. If left undefined, this row will be considered the default row template to use\n     * when no other when functions return true for the data.\n     * For every row, there must be at least one when function that passes or an undefined to default.\n     */\n    _defineProperty(this, \"when\", void 0);\n  }\n}\n_CdkRowDef = CdkRowDef;\n_defineProperty(CdkRowDef, \"\\u0275fac\", function _CdkRowDef_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkRowDef)();\n});\n_defineProperty(CdkRowDef, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkRowDef,\n  selectors: [[\"\", \"cdkRowDef\", \"\"]],\n  inputs: {\n    columns: [0, \"cdkRowDefColumns\", \"columns\"],\n    when: [0, \"cdkRowDefWhen\", \"when\"]\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkRowDefColumns'\n      }, {\n        name: 'when',\n        alias: 'cdkRowDefWhen'\n      }]\n    }]\n  }], () => [], null);\n})();\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\nclass CdkCellOutlet {\n  constructor() {\n    _defineProperty(this, \"_viewContainer\", inject(ViewContainerRef));\n    /** The ordered list of cells to render within this outlet's view container */\n    _defineProperty(this, \"cells\", void 0);\n    /** The data context to be provided to each cell */\n    _defineProperty(this, \"context\", void 0);\n    CdkCellOutlet.mostRecentCellOutlet = this;\n  }\n  ngOnDestroy() {\n    // If this was the last outlet being rendered in the view, remove the reference\n    // from the static property after it has been destroyed to avoid leaking memory.\n    if (CdkCellOutlet.mostRecentCellOutlet === this) {\n      CdkCellOutlet.mostRecentCellOutlet = null;\n    }\n  }\n}\n_CdkCellOutlet = CdkCellOutlet;\n/**\n * Static property containing the latest constructed instance of this class.\n * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n * createEmbeddedView. After one of these components are created, this property will provide\n * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n * construct the cells with the provided context.\n */\n_defineProperty(CdkCellOutlet, \"mostRecentCellOutlet\", null);\n_defineProperty(CdkCellOutlet, \"\\u0275fac\", function _CdkCellOutlet_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkCellOutlet)();\n});\n_defineProperty(CdkCellOutlet, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkCellOutlet,\n  selectors: [[\"\", \"cdkCellOutlet\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCellOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCellOutlet]'\n    }]\n  }], () => [], null);\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass CdkHeaderRow {}\n_CdkHeaderRow = CdkHeaderRow;\n_defineProperty(CdkHeaderRow, \"\\u0275fac\", function _CdkHeaderRow_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkHeaderRow)();\n});\n_defineProperty(CdkHeaderRow, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _CdkHeaderRow,\n  selectors: [[\"cdk-header-row\"], [\"tr\", \"cdk-header-row\", \"\"]],\n  hostAttrs: [\"role\", \"row\", 1, \"cdk-header-row\"],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkCellOutlet\", \"\"]],\n  template: function _CdkHeaderRow_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [CdkCellOutlet],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-header-row, tr[cdk-header-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-header-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass CdkFooterRow {}\n_CdkFooterRow = CdkFooterRow;\n_defineProperty(CdkFooterRow, \"\\u0275fac\", function _CdkFooterRow_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkFooterRow)();\n});\n_defineProperty(CdkFooterRow, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _CdkFooterRow,\n  selectors: [[\"cdk-footer-row\"], [\"tr\", \"cdk-footer-row\", \"\"]],\n  hostAttrs: [\"role\", \"row\", 1, \"cdk-footer-row\"],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkCellOutlet\", \"\"]],\n  template: function _CdkFooterRow_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [CdkCellOutlet],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-footer-row, tr[cdk-footer-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-footer-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass CdkRow {}\n_CdkRow = CdkRow;\n_defineProperty(CdkRow, \"\\u0275fac\", function _CdkRow_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkRow)();\n});\n_defineProperty(CdkRow, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _CdkRow,\n  selectors: [[\"cdk-row\"], [\"tr\", \"cdk-row\", \"\"]],\n  hostAttrs: [\"role\", \"row\", 1, \"cdk-row\"],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkCellOutlet\", \"\"]],\n  template: function _CdkRow_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainer(0, 0);\n    }\n  },\n  dependencies: [CdkCellOutlet],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-row, tr[cdk-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nclass CdkNoDataRow {\n  constructor() {\n    _defineProperty(this, \"templateRef\", inject(TemplateRef));\n    _defineProperty(this, \"_contentClassName\", 'cdk-no-data-row');\n  }\n}\n_CdkNoDataRow = CdkNoDataRow;\n_defineProperty(CdkNoDataRow, \"\\u0275fac\", function _CdkNoDataRow_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkNoDataRow)();\n});\n_defineProperty(CdkNoDataRow, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkNoDataRow,\n  selectors: [[\"ng-template\", \"cdkNoDataRow\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkNoDataRow, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkNoDataRow]'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Directions that can be used when setting sticky positioning.\n * @docs-private\n */\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nconst STICKY_DIRECTIONS = ['top', 'bottom', 'left', 'right'];\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nclass StickyStyler {\n  /**\n   * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n   *     that uses the native `<table>` element.\n   * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n   *     sticky positioning applied.\n   * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n   *     by reversing left/right positions.\n   * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n   * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n   *     using inline styles. If false, it is assumed that position: sticky is included in\n   *     the component stylesheet for _stickCellCss.\n   * @param _positionListener A listener that is notified of changes to sticky rows/columns\n   *     and their dimensions.\n   * @param _tableInjector The table's Injector.\n   */\n  constructor(_isNativeHtmlTable, _stickCellCss, direction, _coalescedStyleScheduler, _isBrowser = true, _needsPositionStickyOnElement = true, _positionListener, _tableInjector) {\n    _defineProperty(this, \"_isNativeHtmlTable\", void 0);\n    _defineProperty(this, \"_stickCellCss\", void 0);\n    _defineProperty(this, \"direction\", void 0);\n    _defineProperty(this, \"_coalescedStyleScheduler\", void 0);\n    _defineProperty(this, \"_isBrowser\", void 0);\n    _defineProperty(this, \"_needsPositionStickyOnElement\", void 0);\n    _defineProperty(this, \"_positionListener\", void 0);\n    _defineProperty(this, \"_tableInjector\", void 0);\n    _defineProperty(this, \"_elemSizeCache\", new WeakMap());\n    _defineProperty(this, \"_resizeObserver\", globalThis !== null && globalThis !== void 0 && globalThis.ResizeObserver ? new globalThis.ResizeObserver(entries => this._updateCachedSizes(entries)) : null);\n    _defineProperty(this, \"_updatedStickyColumnsParamsToReplay\", []);\n    _defineProperty(this, \"_stickyColumnsReplayTimeout\", null);\n    _defineProperty(this, \"_cachedCellWidths\", []);\n    _defineProperty(this, \"_borderCellCss\", void 0);\n    _defineProperty(this, \"_destroyed\", false);\n    this._isNativeHtmlTable = _isNativeHtmlTable;\n    this._stickCellCss = _stickCellCss;\n    this.direction = direction;\n    this._coalescedStyleScheduler = _coalescedStyleScheduler;\n    this._isBrowser = _isBrowser;\n    this._needsPositionStickyOnElement = _needsPositionStickyOnElement;\n    this._positionListener = _positionListener;\n    this._tableInjector = _tableInjector;\n    this._borderCellCss = {\n      'top': `${_stickCellCss}-border-elem-top`,\n      'bottom': `${_stickCellCss}-border-elem-bottom`,\n      'left': `${_stickCellCss}-border-elem-left`,\n      'right': `${_stickCellCss}-border-elem-right`\n    };\n  }\n  /**\n   * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n   * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n   * @param rows The list of rows that should be cleared from sticking in the provided directions\n   * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n   */\n  clearStickyPositioning(rows, stickyDirections) {\n    if (stickyDirections.includes('left') || stickyDirections.includes('right')) {\n      this._removeFromStickyColumnReplayQueue(rows);\n    }\n    const elementsToClear = [];\n    for (const row of rows) {\n      // If the row isn't an element (e.g. if it's an `ng-container`),\n      // it won't have inline styles or `children` so we skip it.\n      if (row.nodeType !== row.ELEMENT_NODE) {\n        continue;\n      }\n      elementsToClear.push(row, ...Array.from(row.children));\n    }\n    // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n    this._afterNextRender({\n      write: () => {\n        for (const element of elementsToClear) {\n          this._removeStickyStyle(element, stickyDirections);\n        }\n      }\n    });\n  }\n  /**\n   * Applies sticky left and right positions to the cells of each row according to the sticky\n   * states of the rendered column definitions.\n   * @param rows The rows that should have its set of cells stuck according to the sticky states.\n   * @param stickyStartStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the start of the row.\n   * @param stickyEndStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the end of the row.\n   * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n   *     column cell. If `false` cached widths will be used instead.\n   * @param replay Whether to enqueue this call for replay after a ResizeObserver update.\n   */\n  updateStickyColumns(rows, stickyStartStates, stickyEndStates, recalculateCellWidths = true, replay = true) {\n    // Don't cache any state if none of the columns are sticky.\n    if (!rows.length || !this._isBrowser || !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))) {\n      var _this$_positionListen, _this$_positionListen2;\n      (_this$_positionListen = this._positionListener) === null || _this$_positionListen === void 0 || _this$_positionListen.stickyColumnsUpdated({\n        sizes: []\n      });\n      (_this$_positionListen2 = this._positionListener) === null || _this$_positionListen2 === void 0 || _this$_positionListen2.stickyEndColumnsUpdated({\n        sizes: []\n      });\n      return;\n    }\n    // Coalesce with sticky row updates (and potentially other changes like column resize).\n    const firstRow = rows[0];\n    const numCells = firstRow.children.length;\n    const isRtl = this.direction === 'rtl';\n    const start = isRtl ? 'right' : 'left';\n    const end = isRtl ? 'left' : 'right';\n    const lastStickyStart = stickyStartStates.lastIndexOf(true);\n    const firstStickyEnd = stickyEndStates.indexOf(true);\n    let cellWidths;\n    let startPositions;\n    let endPositions;\n    if (replay) {\n      this._updateStickyColumnReplayQueue({\n        rows: [...rows],\n        stickyStartStates: [...stickyStartStates],\n        stickyEndStates: [...stickyEndStates]\n      });\n    }\n    this._afterNextRender({\n      earlyRead: () => {\n        cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n        startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n        endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n      },\n      write: () => {\n        for (const row of rows) {\n          for (let i = 0; i < numCells; i++) {\n            const cell = row.children[i];\n            if (stickyStartStates[i]) {\n              this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n            }\n            if (stickyEndStates[i]) {\n              this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n            }\n          }\n        }\n        if (this._positionListener && cellWidths.some(w => !!w)) {\n          this._positionListener.stickyColumnsUpdated({\n            sizes: lastStickyStart === -1 ? [] : cellWidths.slice(0, lastStickyStart + 1).map((width, index) => stickyStartStates[index] ? width : null)\n          });\n          this._positionListener.stickyEndColumnsUpdated({\n            sizes: firstStickyEnd === -1 ? [] : cellWidths.slice(firstStickyEnd).map((width, index) => stickyEndStates[index + firstStickyEnd] ? width : null).reverse()\n          });\n        }\n      }\n    });\n  }\n  /**\n   * Applies sticky positioning to the row's cells if using the native table layout, and to the\n   * row itself otherwise.\n   * @param rowsToStick The list of rows that should be stuck according to their corresponding\n   *     sticky state and to the provided top or bottom position.\n   * @param stickyStates A list of boolean states where each state represents whether the row\n   *     should be stuck in the particular top or bottom position.\n   * @param position The position direction in which the row should be stuck if that row should be\n   *     sticky.\n   *\n   */\n  stickRows(rowsToStick, stickyStates, position) {\n    // Since we can't measure the rows on the server, we can't stick the rows properly.\n    if (!this._isBrowser) {\n      return;\n    }\n    // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n    // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n    // sticky states need to be reversed as well.\n    const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n    const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n    // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n    const stickyOffsets = [];\n    const stickyCellHeights = [];\n    const elementsToStick = [];\n    // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n    // (and potentially other changes like column resize).\n    this._afterNextRender({\n      earlyRead: () => {\n        for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n          if (!states[rowIndex]) {\n            continue;\n          }\n          stickyOffsets[rowIndex] = stickyOffset;\n          const row = rows[rowIndex];\n          elementsToStick[rowIndex] = this._isNativeHtmlTable ? Array.from(row.children) : [row];\n          const height = this._retrieveElementSize(row).height;\n          stickyOffset += height;\n          stickyCellHeights[rowIndex] = height;\n        }\n      },\n      write: () => {\n        const borderedRowIndex = states.lastIndexOf(true);\n        for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n          if (!states[rowIndex]) {\n            continue;\n          }\n          const offset = stickyOffsets[rowIndex];\n          const isBorderedRowIndex = rowIndex === borderedRowIndex;\n          for (const element of elementsToStick[rowIndex]) {\n            this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n          }\n        }\n        if (position === 'top') {\n          var _this$_positionListen3;\n          (_this$_positionListen3 = this._positionListener) === null || _this$_positionListen3 === void 0 || _this$_positionListen3.stickyHeaderRowsUpdated({\n            sizes: stickyCellHeights,\n            offsets: stickyOffsets,\n            elements: elementsToStick\n          });\n        } else {\n          var _this$_positionListen4;\n          (_this$_positionListen4 = this._positionListener) === null || _this$_positionListen4 === void 0 || _this$_positionListen4.stickyFooterRowsUpdated({\n            sizes: stickyCellHeights,\n            offsets: stickyOffsets,\n            elements: elementsToStick\n          });\n        }\n      }\n    });\n  }\n  /**\n   * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n   * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n   * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n   * the tfoot element.\n   */\n  updateStickyFooterContainer(tableElement, stickyStates) {\n    if (!this._isNativeHtmlTable) {\n      return;\n    }\n    // Coalesce with other sticky updates (and potentially other changes like column resize).\n    this._afterNextRender({\n      write: () => {\n        const tfoot = tableElement.querySelector('tfoot');\n        if (tfoot) {\n          if (stickyStates.some(state => !state)) {\n            this._removeStickyStyle(tfoot, ['bottom']);\n          } else {\n            this._addStickyStyle(tfoot, 'bottom', 0, false);\n          }\n        }\n      }\n    });\n  }\n  /** Triggered by the table's OnDestroy hook. */\n  destroy() {\n    var _this$_resizeObserver;\n    if (this._stickyColumnsReplayTimeout) {\n      clearTimeout(this._stickyColumnsReplayTimeout);\n    }\n    (_this$_resizeObserver = this._resizeObserver) === null || _this$_resizeObserver === void 0 || _this$_resizeObserver.disconnect();\n    this._destroyed = true;\n  }\n  /**\n   * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n   * the zIndex, removing each of the provided sticky directions, and removing the\n   * sticky position if there are no more directions.\n   */\n  _removeStickyStyle(element, stickyDirections) {\n    if (!element.classList.contains(this._stickCellCss)) {\n      return;\n    }\n    for (const dir of stickyDirections) {\n      element.style[dir] = '';\n      element.classList.remove(this._borderCellCss[dir]);\n    }\n    // If the element no longer has any more sticky directions, remove sticky positioning and\n    // the sticky CSS class.\n    // Short-circuit checking element.style[dir] for stickyDirections as they\n    // were already removed above.\n    const hasDirection = STICKY_DIRECTIONS.some(dir => stickyDirections.indexOf(dir) === -1 && element.style[dir]);\n    if (hasDirection) {\n      element.style.zIndex = this._getCalculatedZIndex(element);\n    } else {\n      // When not hasDirection, _getCalculatedZIndex will always return ''.\n      element.style.zIndex = '';\n      if (this._needsPositionStickyOnElement) {\n        element.style.position = '';\n      }\n      element.classList.remove(this._stickCellCss);\n    }\n  }\n  /**\n   * Adds the sticky styling to the element by adding the sticky style class, changing position\n   * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n   * direction and value.\n   */\n  _addStickyStyle(element, dir, dirValue, isBorderElement) {\n    element.classList.add(this._stickCellCss);\n    if (isBorderElement) {\n      element.classList.add(this._borderCellCss[dir]);\n    }\n    element.style[dir] = `${dirValue}px`;\n    element.style.zIndex = this._getCalculatedZIndex(element);\n    if (this._needsPositionStickyOnElement) {\n      element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n    }\n  }\n  /**\n   * Calculate what the z-index should be for the element, depending on what directions (top,\n   * bottom, left, right) have been set. It should be true that elements with a top direction\n   * should have the highest index since these are elements like a table header. If any of those\n   * elements are also sticky in another direction, then they should appear above other elements\n   * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n   * (e.g. footer rows) should then be next in the ordering such that they are below the header\n   * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n   * should minimally increment so that they are above non-sticky elements but below top and bottom\n   * elements.\n   */\n  _getCalculatedZIndex(element) {\n    const zIndexIncrements = {\n      top: 100,\n      bottom: 10,\n      left: 1,\n      right: 1\n    };\n    let zIndex = 0;\n    // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n    // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n    // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n    for (const dir of STICKY_DIRECTIONS) {\n      if (element.style[dir]) {\n        zIndex += zIndexIncrements[dir];\n      }\n    }\n    return zIndex ? `${zIndex}` : '';\n  }\n  /** Gets the widths for each cell in the provided row. */\n  _getCellWidths(row, recalculateCellWidths = true) {\n    if (!recalculateCellWidths && this._cachedCellWidths.length) {\n      return this._cachedCellWidths;\n    }\n    const cellWidths = [];\n    const firstRowCells = row.children;\n    for (let i = 0; i < firstRowCells.length; i++) {\n      const cell = firstRowCells[i];\n      cellWidths.push(this._retrieveElementSize(cell).width);\n    }\n    this._cachedCellWidths = cellWidths;\n    return cellWidths;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyStartColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = 0; i < widths.length; i++) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyEndColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = widths.length; i > 0; i--) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n  /**\n   * Retreives the most recently observed size of the specified element from the cache, or\n   * meaures it directly if not yet cached.\n   */\n  _retrieveElementSize(element) {\n    const cachedSize = this._elemSizeCache.get(element);\n    if (cachedSize) {\n      return cachedSize;\n    }\n    const clientRect = element.getBoundingClientRect();\n    const size = {\n      width: clientRect.width,\n      height: clientRect.height\n    };\n    if (!this._resizeObserver) {\n      return size;\n    }\n    this._elemSizeCache.set(element, size);\n    this._resizeObserver.observe(element, {\n      box: 'border-box'\n    });\n    return size;\n  }\n  /**\n   * Conditionally enqueue the requested sticky update and clear previously queued updates\n   * for the same rows.\n   */\n  _updateStickyColumnReplayQueue(params) {\n    this._removeFromStickyColumnReplayQueue(params.rows);\n    // No need to replay if a flush is pending.\n    if (!this._stickyColumnsReplayTimeout) {\n      this._updatedStickyColumnsParamsToReplay.push(params);\n    }\n  }\n  /** Remove updates for the specified rows from the queue. */\n  _removeFromStickyColumnReplayQueue(rows) {\n    const rowsSet = new Set(rows);\n    for (const update of this._updatedStickyColumnsParamsToReplay) {\n      update.rows = update.rows.filter(row => !rowsSet.has(row));\n    }\n    this._updatedStickyColumnsParamsToReplay = this._updatedStickyColumnsParamsToReplay.filter(update => !!update.rows.length);\n  }\n  /** Update _elemSizeCache with the observed sizes. */\n  _updateCachedSizes(entries) {\n    let needsColumnUpdate = false;\n    for (const entry of entries) {\n      var _entry$borderBoxSize, _this$_elemSizeCache$;\n      const newEntry = (_entry$borderBoxSize = entry.borderBoxSize) !== null && _entry$borderBoxSize !== void 0 && _entry$borderBoxSize.length ? {\n        width: entry.borderBoxSize[0].inlineSize,\n        height: entry.borderBoxSize[0].blockSize\n      } : {\n        width: entry.contentRect.width,\n        height: entry.contentRect.height\n      };\n      if (newEntry.width !== ((_this$_elemSizeCache$ = this._elemSizeCache.get(entry.target)) === null || _this$_elemSizeCache$ === void 0 ? void 0 : _this$_elemSizeCache$.width) && isCell(entry.target)) {\n        needsColumnUpdate = true;\n      }\n      this._elemSizeCache.set(entry.target, newEntry);\n    }\n    if (needsColumnUpdate && this._updatedStickyColumnsParamsToReplay.length) {\n      if (this._stickyColumnsReplayTimeout) {\n        clearTimeout(this._stickyColumnsReplayTimeout);\n      }\n      this._stickyColumnsReplayTimeout = setTimeout(() => {\n        if (this._destroyed) {\n          return;\n        }\n        for (const update of this._updatedStickyColumnsParamsToReplay) {\n          this.updateStickyColumns(update.rows, update.stickyStartStates, update.stickyEndStates, true, false);\n        }\n        this._updatedStickyColumnsParamsToReplay = [];\n        this._stickyColumnsReplayTimeout = null;\n      }, 0);\n    }\n  }\n  /**\n   * Invoke afterNextRender with the table's injector, falling back to CoalescedStyleScheduler\n   * if the injector was not provided.\n   */\n  _afterNextRender(spec) {\n    if (this._tableInjector) {\n      afterNextRender(spec, {\n        injector: this._tableInjector\n      });\n    } else {\n      this._coalescedStyleScheduler.schedule(() => {\n        var _spec$earlyRead;\n        (_spec$earlyRead = spec.earlyRead) === null || _spec$earlyRead === void 0 || _spec$earlyRead.call(spec);\n        spec.write();\n      });\n    }\n  }\n}\nfunction isCell(element) {\n  return ['cdk-cell', 'cdk-header-cell', 'cdk-footer-cell'].some(klass => element.classList.contains(klass));\n}\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nfunction getTableUnknownColumnError(id) {\n  return Error(`Could not find column with id \"${id}\".`);\n}\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nfunction getTableDuplicateColumnNameError(name) {\n  return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nfunction getTableMultipleDefaultRowDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nfunction getTableMissingMatchingRowDefError(data) {\n  return Error(`Could not find a matching row definition for the` + `provided row data: ${JSON.stringify(data)}`);\n}\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nfunction getTableMissingRowDefsError() {\n  return Error('Missing definitions for header, footer, and row; ' + 'cannot determine which columns should be rendered.');\n}\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nfunction getTableUnknownDataSourceError() {\n  return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nfunction getTableTextColumnMissingParentTableError() {\n  return Error(`Text column could not find a parent table for registration.`);\n}\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nfunction getTableTextColumnMissingNameError() {\n  return Error(`Table text column must have a name.`);\n}\n\n/** The injection token used to specify the StickyPositioningListener. */\nconst STICKY_POSITIONING_LISTENER = new InjectionToken('CDK_SPL');\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass CdkRecycleRows {}\n_CdkRecycleRows = CdkRecycleRows;\n_defineProperty(CdkRecycleRows, \"\\u0275fac\", function _CdkRecycleRows_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkRecycleRows)();\n});\n_defineProperty(CdkRecycleRows, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkRecycleRows,\n  selectors: [[\"cdk-table\", \"recycleRows\", \"\"], [\"table\", \"cdk-table\", \"\", \"recycleRows\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: _VIEW_REPEATER_STRATEGY,\n    useClass: _RecycleViewRepeaterStrategy\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRecycleRows, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-table[recycleRows], table[cdk-table][recycleRows]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\nclass DataRowOutlet {\n  constructor() {\n    _defineProperty(this, \"viewContainer\", inject(ViewContainerRef));\n    _defineProperty(this, \"elementRef\", inject(ElementRef));\n    const table = inject(CDK_TABLE);\n    table._rowOutlet = this;\n    table._outletAssigned();\n  }\n}\n_DataRowOutlet = DataRowOutlet;\n_defineProperty(DataRowOutlet, \"\\u0275fac\", function _DataRowOutlet_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _DataRowOutlet)();\n});\n_defineProperty(DataRowOutlet, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _DataRowOutlet,\n  selectors: [[\"\", \"rowOutlet\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[rowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\nclass HeaderRowOutlet {\n  constructor() {\n    _defineProperty(this, \"viewContainer\", inject(ViewContainerRef));\n    _defineProperty(this, \"elementRef\", inject(ElementRef));\n    const table = inject(CDK_TABLE);\n    table._headerRowOutlet = this;\n    table._outletAssigned();\n  }\n}\n_HeaderRowOutlet = HeaderRowOutlet;\n_defineProperty(HeaderRowOutlet, \"\\u0275fac\", function _HeaderRowOutlet_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HeaderRowOutlet)();\n});\n_defineProperty(HeaderRowOutlet, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _HeaderRowOutlet,\n  selectors: [[\"\", \"headerRowOutlet\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HeaderRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[headerRowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\nclass FooterRowOutlet {\n  constructor() {\n    _defineProperty(this, \"viewContainer\", inject(ViewContainerRef));\n    _defineProperty(this, \"elementRef\", inject(ElementRef));\n    const table = inject(CDK_TABLE);\n    table._footerRowOutlet = this;\n    table._outletAssigned();\n  }\n}\n_FooterRowOutlet = FooterRowOutlet;\n_defineProperty(FooterRowOutlet, \"\\u0275fac\", function _FooterRowOutlet_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FooterRowOutlet)();\n});\n_defineProperty(FooterRowOutlet, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _FooterRowOutlet,\n  selectors: [[\"\", \"footerRowOutlet\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FooterRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[footerRowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\nclass NoDataRowOutlet {\n  constructor() {\n    _defineProperty(this, \"viewContainer\", inject(ViewContainerRef));\n    _defineProperty(this, \"elementRef\", inject(ElementRef));\n    const table = inject(CDK_TABLE);\n    table._noDataRowOutlet = this;\n    table._outletAssigned();\n  }\n}\n_NoDataRowOutlet = NoDataRowOutlet;\n_defineProperty(NoDataRowOutlet, \"\\u0275fac\", function _NoDataRowOutlet_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _NoDataRowOutlet)();\n});\n_defineProperty(NoDataRowOutlet, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _NoDataRowOutlet,\n  selectors: [[\"\", \"noDataRowOutlet\", \"\"]]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoDataRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[noDataRowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * The table template that can be used by the mat-table. Should not be used outside of the\n * material library.\n * @docs-private\n */\nconst CDK_TABLE_TEMPLATE =\n// Note that according to MDN, the `caption` element has to be projected as the **first**\n// element in the table. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/caption\n`\n  <ng-content select=\"caption\"/>\n  <ng-content select=\"colgroup, col\"/>\n\n  <!--\n    Unprojected content throws a hydration error so we need this to capture it.\n    It gets removed on the client so it doesn't affect the layout.\n  -->\n  @if (_isServer) {\n    <ng-content/>\n  }\n\n  @if (_isNativeHtmlTable) {\n    <thead role=\"rowgroup\">\n      <ng-container headerRowOutlet/>\n    </thead>\n    <tbody role=\"rowgroup\">\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n    </tbody>\n    <tfoot role=\"rowgroup\">\n      <ng-container footerRowOutlet/>\n    </tfoot>\n  } @else {\n    <ng-container headerRowOutlet/>\n    <ng-container rowOutlet/>\n    <ng-container noDataRowOutlet/>\n    <ng-container footerRowOutlet/>\n  }\n`;\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\nclass CdkTable {\n  /** Aria role to apply to the table's cells based on the table's own role. */\n  _getCellRole() {\n    // Perform this lazily in case the table's role was updated by a directive after construction.\n    if (this._cellRoleInternal === undefined) {\n      // Note that we set `role=\"cell\"` even on native `td` elements,\n      // because some browsers seem to require it. See #29784.\n      const tableRole = this._elementRef.nativeElement.getAttribute('role');\n      return tableRole === 'grid' || tableRole === 'treegrid' ? 'gridcell' : 'cell';\n    }\n    return this._cellRoleInternal;\n  }\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n   * relative to the function to know if a row should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  get trackBy() {\n    return this._trackByFn;\n  }\n  set trackBy(fn) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n      console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n    }\n    this._trackByFn = fn;\n  }\n  /**\n   * The table's source of data, which can be provided in three ways (in order of complexity):\n   *   - Simple data array (each object represents one table row)\n   *   - Stream that emits a data array each time the array changes\n   *   - `DataSource` object that implements the connect/disconnect interface.\n   *\n   * If a data array is provided, the table must be notified when the array's objects are\n   * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n   * render the diff since the last table render. If the data array reference is changed, the table\n   * will automatically trigger an update to the rows.\n   *\n   * When providing an Observable stream, the table will trigger an update automatically when the\n   * stream emits a new array of data.\n   *\n   * Finally, when providing a `DataSource` object, the table will use the Observable stream\n   * provided by the connect function and trigger updates when that stream emits new data array\n   * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n   * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n   * subscriptions registered during the connect process).\n   */\n  get dataSource() {\n    return this._dataSource;\n  }\n  set dataSource(dataSource) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  /**\n   * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n   * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n   * dataobject will render the first row that evaluates its when predicate to true, in the order\n   * defined in the table, or otherwise the default row which does not have a when predicate.\n   */\n  get multiTemplateDataRows() {\n    return this._multiTemplateDataRows;\n  }\n  set multiTemplateDataRows(value) {\n    this._multiTemplateDataRows = value;\n    // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n    // this setter will be invoked before the row outlet has been defined hence the null check.\n    if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n      this._forceRenderDataRows();\n      this.updateStickyColumnStyles();\n    }\n  }\n  /**\n   * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n   * and optimize rendering sticky styles for native tables. No-op for flex tables.\n   */\n  get fixedLayout() {\n    return this._fixedLayout;\n  }\n  set fixedLayout(value) {\n    this._fixedLayout = value;\n    // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n    this._forceRecalculateCellWidths = true;\n    this._stickyColumnStylesNeedReset = true;\n  }\n  constructor() {\n    _defineProperty(this, \"_differs\", inject(IterableDiffers));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_dir\", inject(Directionality, {\n      optional: true\n    }));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_viewRepeater\", inject(_VIEW_REPEATER_STRATEGY));\n    _defineProperty(this, \"_coalescedStyleScheduler\", inject(_COALESCED_STYLE_SCHEDULER));\n    _defineProperty(this, \"_viewportRuler\", inject(ViewportRuler));\n    _defineProperty(this, \"_stickyPositioningListener\", inject(STICKY_POSITIONING_LISTENER, {\n      optional: true,\n      skipSelf: true\n    }));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    /** Latest data provided by the data source. */\n    _defineProperty(this, \"_data\", void 0);\n    /** Subject that emits when the component has been destroyed. */\n    _defineProperty(this, \"_onDestroy\", new Subject());\n    /** List of the rendered rows as identified by their `RenderRow` object. */\n    _defineProperty(this, \"_renderRows\", void 0);\n    /** Subscription that listens for the data provided by the data source. */\n    _defineProperty(this, \"_renderChangeSubscription\", void 0);\n    /**\n     * Map of all the user's defined columns (header, data, and footer cell template) identified by\n     * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n     * any custom column definitions added to `_customColumnDefs`.\n     */\n    _defineProperty(this, \"_columnDefsByName\", new Map());\n    /**\n     * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n     * using `ContentChildren` as well as any custom row definitions added to `_customRowDefs`.\n     */\n    _defineProperty(this, \"_rowDefs\", void 0);\n    /**\n     * Set of all header row definitions that can be used by this table. Populated by the rows\n     * gathered by using `ContentChildren` as well as any custom row definitions added to\n     * `_customHeaderRowDefs`.\n     */\n    _defineProperty(this, \"_headerRowDefs\", void 0);\n    /**\n     * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n     * using `ContentChildren` as well as any custom row definitions added to\n     * `_customFooterRowDefs`.\n     */\n    _defineProperty(this, \"_footerRowDefs\", void 0);\n    /** Differ used to find the changes in the data provided by the data source. */\n    _defineProperty(this, \"_dataDiffer\", void 0);\n    /** Stores the row definition that does not have a when predicate. */\n    _defineProperty(this, \"_defaultRowDef\", void 0);\n    /**\n     * Column definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * column definitions as *its* content child.\n     */\n    _defineProperty(this, \"_customColumnDefs\", new Set());\n    /**\n     * Data row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * built-in data rows as *its* content child.\n     */\n    _defineProperty(this, \"_customRowDefs\", new Set());\n    /**\n     * Header row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * built-in header rows as *its* content child.\n     */\n    _defineProperty(this, \"_customHeaderRowDefs\", new Set());\n    /**\n     * Footer row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n     * built-in footer row as *its* content child.\n     */\n    _defineProperty(this, \"_customFooterRowDefs\", new Set());\n    /** No data row that was defined outside of the direct content children of the table. */\n    _defineProperty(this, \"_customNoDataRow\", void 0);\n    /**\n     * Whether the header row definition has been changed. Triggers an update to the header row after\n     * content is checked. Initialized as true so that the table renders the initial set of rows.\n     */\n    _defineProperty(this, \"_headerRowDefChanged\", true);\n    /**\n     * Whether the footer row definition has been changed. Triggers an update to the footer row after\n     * content is checked. Initialized as true so that the table renders the initial set of rows.\n     */\n    _defineProperty(this, \"_footerRowDefChanged\", true);\n    /**\n     * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n     * change.\n     */\n    _defineProperty(this, \"_stickyColumnStylesNeedReset\", true);\n    /**\n     * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n     * `false`, cached values will be used instead. This is only applicable to tables with\n     * {@link fixedLayout} enabled. For other tables, cell widths will always be recalculated.\n     */\n    _defineProperty(this, \"_forceRecalculateCellWidths\", true);\n    /**\n     * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n     * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n     * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n     * and row template matches, which allows the `IterableDiffer` to check rows by reference\n     * and understand which rows are added/moved/removed.\n     *\n     * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n     * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n     * contains an array of created pairs. The array is necessary to handle cases where the data\n     * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n     * stored.\n     */\n    _defineProperty(this, \"_cachedRenderRowsMap\", new Map());\n    /** Whether the table is applied to a native `<table>`. */\n    _defineProperty(this, \"_isNativeHtmlTable\", void 0);\n    /**\n     * Utility class that is responsible for applying the appropriate sticky positioning styles to\n     * the table's rows and cells.\n     */\n    _defineProperty(this, \"_stickyStyler\", void 0);\n    /**\n     * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n     * table subclasses.\n     */\n    _defineProperty(this, \"stickyCssClass\", 'cdk-table-sticky');\n    /**\n     * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n     * the position is set in a selector associated with the value of stickyCssClass. May be\n     * overridden by table subclasses\n     */\n    _defineProperty(this, \"needsPositionStickyOnElement\", true);\n    /** Whether the component is being rendered on the server. */\n    _defineProperty(this, \"_isServer\", void 0);\n    /** Whether the no data row is currently showing anything. */\n    _defineProperty(this, \"_isShowingNoDataRow\", false);\n    /** Whether the table has rendered out all the outlets for the first time. */\n    _defineProperty(this, \"_hasAllOutlets\", false);\n    /** Whether the table is done initializing. */\n    _defineProperty(this, \"_hasInitialized\", false);\n    _defineProperty(this, \"_cellRoleInternal\", undefined);\n    _defineProperty(this, \"_trackByFn\", void 0);\n    _defineProperty(this, \"_dataSource\", void 0);\n    _defineProperty(this, \"_multiTemplateDataRows\", false);\n    _defineProperty(this, \"_fixedLayout\", false);\n    /**\n     * Emits when the table completes rendering a set of data rows based on the latest data from the\n     * data source, even if the set of rows is empty.\n     */\n    _defineProperty(this, \"contentChanged\", new EventEmitter());\n    // TODO(andrewseguin): Remove max value as the end index\n    //   and instead calculate the view on init and scroll.\n    /**\n     * Stream containing the latest information on what rows are being displayed on screen.\n     * Can be used by the data source to as a heuristic of what data should be provided.\n     *\n     * @docs-private\n     */\n    _defineProperty(this, \"viewChange\", new BehaviorSubject({\n      start: 0,\n      end: Number.MAX_VALUE\n    }));\n    // Outlets in the table's template where the header, data rows, and footer will be inserted.\n    _defineProperty(this, \"_rowOutlet\", void 0);\n    _defineProperty(this, \"_headerRowOutlet\", void 0);\n    _defineProperty(this, \"_footerRowOutlet\", void 0);\n    _defineProperty(this, \"_noDataRowOutlet\", void 0);\n    /**\n     * The column definitions provided by the user that contain what the header, data, and footer\n     * cells should render for each column.\n     */\n    _defineProperty(this, \"_contentColumnDefs\", void 0);\n    /** Set of data row definitions that were provided to the table as content children. */\n    _defineProperty(this, \"_contentRowDefs\", void 0);\n    /** Set of header row definitions that were provided to the table as content children. */\n    _defineProperty(this, \"_contentHeaderRowDefs\", void 0);\n    /** Set of footer row definitions that were provided to the table as content children. */\n    _defineProperty(this, \"_contentFooterRowDefs\", void 0);\n    /** Row definition that will only be rendered if there's no data in the table. */\n    _defineProperty(this, \"_noDataRow\", void 0);\n    _defineProperty(this, \"_injector\", inject(Injector));\n    const role = inject(new HostAttributeToken('role'), {\n      optional: true\n    });\n    if (!role) {\n      this._elementRef.nativeElement.setAttribute('role', 'table');\n    }\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeHtmlTable = this._elementRef.nativeElement.nodeName === 'TABLE';\n    // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n    // the user has provided a custom trackBy, return the result of that function as evaluated\n    // with the values of the `RenderRow`'s data and index.\n    this._dataDiffer = this._differs.find([]).create((_i, dataRow) => {\n      return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n    });\n  }\n  ngOnInit() {\n    this._setupStickyStyler();\n    this._viewportRuler.change().pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this._forceRecalculateCellWidths = true;\n    });\n  }\n  ngAfterContentInit() {\n    this._hasInitialized = true;\n  }\n  ngAfterContentChecked() {\n    // Only start re-rendering in `ngAfterContentChecked` after the first render.\n    if (this._canRender()) {\n      this._render();\n    }\n  }\n  ngOnDestroy() {\n    var _this$_stickyStyler, _this$_rowOutlet, _this$_headerRowOutle, _this$_footerRowOutle;\n    (_this$_stickyStyler = this._stickyStyler) === null || _this$_stickyStyler === void 0 || _this$_stickyStyler.destroy();\n    [(_this$_rowOutlet = this._rowOutlet) === null || _this$_rowOutlet === void 0 ? void 0 : _this$_rowOutlet.viewContainer, (_this$_headerRowOutle = this._headerRowOutlet) === null || _this$_headerRowOutle === void 0 ? void 0 : _this$_headerRowOutle.viewContainer, (_this$_footerRowOutle = this._footerRowOutlet) === null || _this$_footerRowOutle === void 0 ? void 0 : _this$_footerRowOutle.viewContainer, this._cachedRenderRowsMap, this._customColumnDefs, this._customRowDefs, this._customHeaderRowDefs, this._customFooterRowDefs, this._columnDefsByName].forEach(def => {\n      def === null || def === void 0 || def.clear();\n    });\n    this._headerRowDefs = [];\n    this._footerRowDefs = [];\n    this._defaultRowDef = null;\n    this._onDestroy.next();\n    this._onDestroy.complete();\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n  }\n  /**\n   * Renders rows based on the table's latest set of data, which was either provided directly as an\n   * input or retrieved through an Observable stream (directly or from a DataSource).\n   * Checks for differences in the data since the last diff to perform only the necessary\n   * changes (add/remove/move rows).\n   *\n   * If the table's data source is a DataSource or Observable, this will be invoked automatically\n   * each time the provided Observable stream emits a new data array. Otherwise if your data is\n   * an array, this function will need to be called to render any changes.\n   */\n  renderRows() {\n    this._renderRows = this._getAllRenderRows();\n    const changes = this._dataDiffer.diff(this._renderRows);\n    if (!changes) {\n      this._updateNoDataRow();\n      this.contentChanged.next();\n      return;\n    }\n    const viewContainer = this._rowOutlet.viewContainer;\n    this._viewRepeater.applyChanges(changes, viewContainer, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record.item, currentIndex), record => record.item.data, change => {\n      if (change.operation === _ViewRepeaterOperation.INSERTED && change.context) {\n        this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n      }\n    });\n    // Update the meta context of a row's context data (index, count, first, last, ...)\n    this._updateRowIndexContext();\n    // Update rows that did not get added/removed/moved but may have had their identity changed,\n    // e.g. if trackBy matched data on some property but the actual data reference changed.\n    changes.forEachIdentityChange(record => {\n      const rowView = viewContainer.get(record.currentIndex);\n      rowView.context.$implicit = record.item.data;\n    });\n    this._updateNoDataRow();\n    this.contentChanged.next();\n    this.updateStickyColumnStyles();\n  }\n  /** Adds a column definition that was not included as part of the content children. */\n  addColumnDef(columnDef) {\n    this._customColumnDefs.add(columnDef);\n  }\n  /** Removes a column definition that was not included as part of the content children. */\n  removeColumnDef(columnDef) {\n    this._customColumnDefs.delete(columnDef);\n  }\n  /** Adds a row definition that was not included as part of the content children. */\n  addRowDef(rowDef) {\n    this._customRowDefs.add(rowDef);\n  }\n  /** Removes a row definition that was not included as part of the content children. */\n  removeRowDef(rowDef) {\n    this._customRowDefs.delete(rowDef);\n  }\n  /** Adds a header row definition that was not included as part of the content children. */\n  addHeaderRowDef(headerRowDef) {\n    this._customHeaderRowDefs.add(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n  /** Removes a header row definition that was not included as part of the content children. */\n  removeHeaderRowDef(headerRowDef) {\n    this._customHeaderRowDefs.delete(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n  /** Adds a footer row definition that was not included as part of the content children. */\n  addFooterRowDef(footerRowDef) {\n    this._customFooterRowDefs.add(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n  /** Removes a footer row definition that was not included as part of the content children. */\n  removeFooterRowDef(footerRowDef) {\n    this._customFooterRowDefs.delete(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n  /** Sets a no data row definition that was not included as a part of the content children. */\n  setNoDataRow(noDataRow) {\n    this._customNoDataRow = noDataRow;\n  }\n  /**\n   * Updates the header sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n   * automatically called when the header row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyHeaderRowStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    // Hide the thead element if there are no header rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    if (this._isNativeHtmlTable) {\n      const thead = closestTableSection(this._headerRowOutlet, 'thead');\n      if (thead) {\n        thead.style.display = headerRows.length ? '' : 'none';\n      }\n    }\n    const stickyStates = this._headerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n    this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._headerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n   * automatically called when the footer row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyFooterRowStyles() {\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n    // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    if (this._isNativeHtmlTable) {\n      const tfoot = closestTableSection(this._footerRowOutlet, 'tfoot');\n      if (tfoot) {\n        tfoot.style.display = footerRows.length ? '' : 'none';\n      }\n    }\n    const stickyStates = this._footerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n    this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n    this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._footerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Updates the column sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the left and right. Then sticky styles are added for the left and right according\n   * to the column definitions for each cell in each row. This is automatically called when\n   * the data source provides a new set of data or when a column definition changes its sticky\n   * input. May be called manually for cases where the cell content changes outside of these events.\n   */\n  updateStickyColumnStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    const dataRows = this._getRenderedRows(this._rowOutlet);\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n    // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n    // In a table using a fixed layout, row content won't affect column width, so sticky styles\n    // don't need to be cleared unless either the sticky column config changes or one of the row\n    // defs change.\n    if (this._isNativeHtmlTable && !this._fixedLayout || this._stickyColumnStylesNeedReset) {\n      // Clear the left and right positioning from all columns in the table across all rows since\n      // sticky columns span across all table sections (header, data, footer)\n      this._stickyStyler.clearStickyPositioning([...headerRows, ...dataRows, ...footerRows], ['left', 'right']);\n      this._stickyColumnStylesNeedReset = false;\n    }\n    // Update the sticky styles for each header row depending on the def's sticky state\n    headerRows.forEach((headerRow, i) => {\n      this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n    });\n    // Update the sticky styles for each data row depending on its def's sticky state\n    this._rowDefs.forEach(rowDef => {\n      // Collect all the rows rendered with this row definition.\n      const rows = [];\n      for (let i = 0; i < dataRows.length; i++) {\n        if (this._renderRows[i].rowDef === rowDef) {\n          rows.push(dataRows[i]);\n        }\n      }\n      this._addStickyColumnStyles(rows, rowDef);\n    });\n    // Update the sticky styles for each footer row depending on the def's sticky state\n    footerRows.forEach((footerRow, i) => {\n      this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n    });\n    // Reset the dirty state of the sticky input change since it has been used.\n    Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n  }\n  /** Invoked whenever an outlet is created and has been assigned to the table. */\n  _outletAssigned() {\n    // Trigger the first render once all outlets have been assigned. We do it this way, as\n    // opposed to waiting for the next `ngAfterContentChecked`, because we don't know when\n    // the next change detection will happen.\n    // Also we can't use queries to resolve the outlets, because they're wrapped in a\n    // conditional, so we have to rely on them being assigned via DI.\n    if (!this._hasAllOutlets && this._rowOutlet && this._headerRowOutlet && this._footerRowOutlet && this._noDataRowOutlet) {\n      this._hasAllOutlets = true;\n      // In some setups this may fire before `ngAfterContentInit`\n      // so we need a check here. See #28538.\n      if (this._canRender()) {\n        this._render();\n      }\n    }\n  }\n  /** Whether the table has all the information to start rendering. */\n  _canRender() {\n    return this._hasAllOutlets && this._hasInitialized;\n  }\n  /** Renders the table if its state has changed. */\n  _render() {\n    // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n    this._cacheRowDefs();\n    this._cacheColumnDefs();\n    // Make sure that the user has at least added header, footer, or data row def.\n    if (!this._headerRowDefs.length && !this._footerRowDefs.length && !this._rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingRowDefsError();\n    }\n    // Render updates if the list of columns have been changed for the header, row, or footer defs.\n    const columnsChanged = this._renderUpdatedColumns();\n    const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n    // Ensure sticky column styles are reset if set to `true` elsewhere.\n    this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n    this._forceRecalculateCellWidths = rowDefsChanged;\n    // If the header row definition has been changed, trigger a render to the header row.\n    if (this._headerRowDefChanged) {\n      this._forceRenderHeaderRows();\n      this._headerRowDefChanged = false;\n    }\n    // If the footer row definition has been changed, trigger a render to the footer row.\n    if (this._footerRowDefChanged) {\n      this._forceRenderFooterRows();\n      this._footerRowDefChanged = false;\n    }\n    // If there is a data source and row definitions, connect to the data source unless a\n    // connection has already been made.\n    if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n      this._observeRenderChanges();\n    } else if (this._stickyColumnStylesNeedReset) {\n      // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n      // called when it row data arrives. Otherwise, we need to call it proactively.\n      this.updateStickyColumnStyles();\n    }\n    this._checkStickyStates();\n  }\n  /**\n   * Get the list of RenderRow objects to render according to the current list of data and defined\n   * row definitions. If the previous list already contained a particular pair, it should be reused\n   * so that the differ equates their references.\n   */\n  _getAllRenderRows() {\n    const renderRows = [];\n    // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n    // new cache while unused ones can be picked up by garbage collection.\n    const prevCachedRenderRows = this._cachedRenderRowsMap;\n    this._cachedRenderRowsMap = new Map();\n    if (!this._data) {\n      return renderRows;\n    }\n    // For each data object, get the list of rows that should be rendered, represented by the\n    // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n    for (let i = 0; i < this._data.length; i++) {\n      let data = this._data[i];\n      const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n      if (!this._cachedRenderRowsMap.has(data)) {\n        this._cachedRenderRowsMap.set(data, new WeakMap());\n      }\n      for (let j = 0; j < renderRowsForData.length; j++) {\n        let renderRow = renderRowsForData[j];\n        const cache = this._cachedRenderRowsMap.get(renderRow.data);\n        if (cache.has(renderRow.rowDef)) {\n          cache.get(renderRow.rowDef).push(renderRow);\n        } else {\n          cache.set(renderRow.rowDef, [renderRow]);\n        }\n        renderRows.push(renderRow);\n      }\n    }\n    return renderRows;\n  }\n  /**\n   * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n   * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n   * `(T, CdkRowDef)` pair.\n   */\n  _getRenderRowsForData(data, dataIndex, cache) {\n    const rowDefs = this._getRowDefs(data, dataIndex);\n    return rowDefs.map(rowDef => {\n      const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef) : [];\n      if (cachedRenderRows.length) {\n        const dataRow = cachedRenderRows.shift();\n        dataRow.dataIndex = dataIndex;\n        return dataRow;\n      } else {\n        return {\n          data,\n          rowDef,\n          dataIndex\n        };\n      }\n    });\n  }\n  /** Update the map containing the content's column definitions. */\n  _cacheColumnDefs() {\n    this._columnDefsByName.clear();\n    const columnDefs = mergeArrayAndSet(this._getOwnDefs(this._contentColumnDefs), this._customColumnDefs);\n    columnDefs.forEach(columnDef => {\n      if (this._columnDefsByName.has(columnDef.name) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableDuplicateColumnNameError(columnDef.name);\n      }\n      this._columnDefsByName.set(columnDef.name, columnDef);\n    });\n  }\n  /** Update the list of all available row definitions that can be used. */\n  _cacheRowDefs() {\n    this._headerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentHeaderRowDefs), this._customHeaderRowDefs);\n    this._footerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentFooterRowDefs), this._customFooterRowDefs);\n    this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n    // After all row definitions are determined, find the row definition to be considered default.\n    const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n    if (!this.multiTemplateDataRows && defaultRowDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMultipleDefaultRowDefsError();\n    }\n    this._defaultRowDef = defaultRowDefs[0];\n  }\n  /**\n   * Check if the header, data, or footer rows have changed what columns they want to display or\n   * whether the sticky states have changed for the header or footer. If there is a diff, then\n   * re-render that section.\n   */\n  _renderUpdatedColumns() {\n    const columnsDiffReducer = (acc, def) => {\n      // The differ should be run for every column, even if `acc` is already\n      // true (see #29922)\n      const diff = !!def.getColumnsDiff();\n      return acc || diff;\n    };\n    // Force re-render data rows if the list of column definitions have changed.\n    const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n    if (dataColumnsChanged) {\n      this._forceRenderDataRows();\n    }\n    // Force re-render header/footer rows if the list of column definitions have changed.\n    const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n    if (headerColumnsChanged) {\n      this._forceRenderHeaderRows();\n    }\n    const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n    if (footerColumnsChanged) {\n      this._forceRenderFooterRows();\n    }\n    return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n  }\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the row outlet. Otherwise start listening for new data.\n   */\n  _switchDataSource(dataSource) {\n    this._data = [];\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n    // Stop listening for data from the previous data source.\n    if (this._renderChangeSubscription) {\n      this._renderChangeSubscription.unsubscribe();\n      this._renderChangeSubscription = null;\n    }\n    if (!dataSource) {\n      if (this._dataDiffer) {\n        this._dataDiffer.diff([]);\n      }\n      if (this._rowOutlet) {\n        this._rowOutlet.viewContainer.clear();\n      }\n    }\n    this._dataSource = dataSource;\n  }\n  /** Set up a subscription for the data provided by the data source. */\n  _observeRenderChanges() {\n    // If no data source has been set, there is nothing to observe for changes.\n    if (!this.dataSource) {\n      return;\n    }\n    let dataStream;\n    if (isDataSource(this.dataSource)) {\n      dataStream = this.dataSource.connect(this);\n    } else if (isObservable(this.dataSource)) {\n      dataStream = this.dataSource;\n    } else if (Array.isArray(this.dataSource)) {\n      dataStream = of(this.dataSource);\n    }\n    if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableUnknownDataSourceError();\n    }\n    this._renderChangeSubscription = dataStream.pipe(takeUntil(this._onDestroy)).subscribe(data => {\n      this._data = data || [];\n      this.renderRows();\n    });\n  }\n  /**\n   * Clears any existing content in the header row outlet and creates a new embedded view\n   * in the outlet using the header row definition.\n   */\n  _forceRenderHeaderRows() {\n    // Clear the header row outlet if any content exists.\n    if (this._headerRowOutlet.viewContainer.length > 0) {\n      this._headerRowOutlet.viewContainer.clear();\n    }\n    this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n    this.updateStickyHeaderRowStyles();\n  }\n  /**\n   * Clears any existing content in the footer row outlet and creates a new embedded view\n   * in the outlet using the footer row definition.\n   */\n  _forceRenderFooterRows() {\n    // Clear the footer row outlet if any content exists.\n    if (this._footerRowOutlet.viewContainer.length > 0) {\n      this._footerRowOutlet.viewContainer.clear();\n    }\n    this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n    this.updateStickyFooterRowStyles();\n  }\n  /** Adds the sticky column styles for the rows according to the columns' stick states. */\n  _addStickyColumnStyles(rows, rowDef) {\n    const columnDefs = Array.from((rowDef === null || rowDef === void 0 ? void 0 : rowDef.columns) || []).map(columnName => {\n      const columnDef = this._columnDefsByName.get(columnName);\n      if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnName);\n      }\n      return columnDef;\n    });\n    const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n    const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n    this._stickyStyler.updateStickyColumns(rows, stickyStartStates, stickyEndStates, !this._fixedLayout || this._forceRecalculateCellWidths);\n  }\n  /** Gets the list of rows that have been rendered in the row outlet. */\n  _getRenderedRows(rowOutlet) {\n    const renderedRows = [];\n    for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n      const viewRef = rowOutlet.viewContainer.get(i);\n      renderedRows.push(viewRef.rootNodes[0]);\n    }\n    return renderedRows;\n  }\n  /**\n   * Get the matching row definitions that should be used for this row data. If there is only\n   * one row definition, it is returned. Otherwise, find the row definitions that has a when\n   * predicate that returns true with the data. If none return true, return the default row\n   * definition.\n   */\n  _getRowDefs(data, dataIndex) {\n    if (this._rowDefs.length == 1) {\n      return [this._rowDefs[0]];\n    }\n    let rowDefs = [];\n    if (this.multiTemplateDataRows) {\n      rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n    } else {\n      let rowDef = this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n      if (rowDef) {\n        rowDefs.push(rowDef);\n      }\n    }\n    if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingMatchingRowDefError(data);\n    }\n    return rowDefs;\n  }\n  _getEmbeddedViewArgs(renderRow, index) {\n    const rowDef = renderRow.rowDef;\n    const context = {\n      $implicit: renderRow.data\n    };\n    return {\n      templateRef: rowDef.template,\n      context,\n      index\n    };\n  }\n  /**\n   * Creates a new row template in the outlet and fills it with the set of cell templates.\n   * Optionally takes a context to provide to the row and cells, as well as an optional index\n   * of where to place the new row template in the outlet.\n   */\n  _renderRow(outlet, rowDef, index, context = {}) {\n    // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n    const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n    this._renderCellTemplateForItem(rowDef, context);\n    return view;\n  }\n  _renderCellTemplateForItem(rowDef, context) {\n    for (let cellTemplate of this._getCellTemplates(rowDef)) {\n      if (CdkCellOutlet.mostRecentCellOutlet) {\n        CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Updates the index-related context for each row to reflect any changes in the index of the rows,\n   * e.g. first/last/even/odd.\n   */\n  _updateRowIndexContext() {\n    const viewContainer = this._rowOutlet.viewContainer;\n    for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n      const viewRef = viewContainer.get(renderIndex);\n      const context = viewRef.context;\n      context.count = count;\n      context.first = renderIndex === 0;\n      context.last = renderIndex === count - 1;\n      context.even = renderIndex % 2 === 0;\n      context.odd = !context.even;\n      if (this.multiTemplateDataRows) {\n        context.dataIndex = this._renderRows[renderIndex].dataIndex;\n        context.renderIndex = renderIndex;\n      } else {\n        context.index = this._renderRows[renderIndex].dataIndex;\n      }\n    }\n  }\n  /** Gets the column definitions for the provided row def. */\n  _getCellTemplates(rowDef) {\n    if (!rowDef || !rowDef.columns) {\n      return [];\n    }\n    return Array.from(rowDef.columns, columnId => {\n      const column = this._columnDefsByName.get(columnId);\n      if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnId);\n      }\n      return rowDef.extractCellTemplate(column);\n    });\n  }\n  /**\n   * Forces a re-render of the data rows. Should be called in cases where there has been an input\n   * change that affects the evaluation of which rows should be rendered, e.g. toggling\n   * `multiTemplateDataRows` or adding/removing row definitions.\n   */\n  _forceRenderDataRows() {\n    this._dataDiffer.diff([]);\n    this._rowOutlet.viewContainer.clear();\n    this.renderRows();\n  }\n  /**\n   * Checks if there has been a change in sticky states since last check and applies the correct\n   * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n   * during a change detection and after the inputs are settled (after content check).\n   */\n  _checkStickyStates() {\n    const stickyCheckReducer = (acc, d) => {\n      return acc || d.hasStickyChanged();\n    };\n    // Note that the check needs to occur for every definition since it notifies the definition\n    // that it can reset its dirty state. Using another operator like `some` may short-circuit\n    // remaining definitions and leave them in an unchecked state.\n    if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyHeaderRowStyles();\n    }\n    if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyFooterRowStyles();\n    }\n    if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n      this._stickyColumnStylesNeedReset = true;\n      this.updateStickyColumnStyles();\n    }\n  }\n  /**\n   * Creates the sticky styler that will be used for sticky rows and columns. Listens\n   * for directionality changes and provides the latest direction to the styler. Re-applies column\n   * stickiness when directionality changes.\n   */\n  _setupStickyStyler() {\n    const direction = this._dir ? this._dir.value : 'ltr';\n    this._stickyStyler = new StickyStyler(this._isNativeHtmlTable, this.stickyCssClass, direction, this._coalescedStyleScheduler, this._platform.isBrowser, this.needsPositionStickyOnElement, this._stickyPositioningListener, this._injector);\n    (this._dir ? this._dir.change : of()).pipe(takeUntil(this._onDestroy)).subscribe(value => {\n      this._stickyStyler.direction = value;\n      this.updateStickyColumnStyles();\n    });\n  }\n  /** Filters definitions that belong to this table from a QueryList. */\n  _getOwnDefs(items) {\n    return items.filter(item => !item._table || item._table === this);\n  }\n  /** Creates or removes the no data row, depending on whether any data is being shown. */\n  _updateNoDataRow() {\n    const noDataRow = this._customNoDataRow || this._noDataRow;\n    if (!noDataRow) {\n      return;\n    }\n    const shouldShow = this._rowOutlet.viewContainer.length === 0;\n    if (shouldShow === this._isShowingNoDataRow) {\n      return;\n    }\n    const container = this._noDataRowOutlet.viewContainer;\n    if (shouldShow) {\n      const view = container.createEmbeddedView(noDataRow.templateRef);\n      const rootNode = view.rootNodes[0];\n      // Only add the attributes if we have a single root node since it's hard\n      // to figure out which one to add it to when there are multiple.\n      if (view.rootNodes.length === 1 && (rootNode === null || rootNode === void 0 ? void 0 : rootNode.nodeType) === this._document.ELEMENT_NODE) {\n        rootNode.setAttribute('role', 'row');\n        rootNode.classList.add(noDataRow._contentClassName);\n      }\n    } else {\n      container.clear();\n    }\n    this._isShowingNoDataRow = shouldShow;\n    this._changeDetectorRef.markForCheck();\n  }\n}\n_CdkTable = CdkTable;\n_defineProperty(CdkTable, \"\\u0275fac\", function _CdkTable_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTable)();\n});\n_defineProperty(CdkTable, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _CdkTable,\n  selectors: [[\"cdk-table\"], [\"table\", \"cdk-table\", \"\"]],\n  contentQueries: function _CdkTable_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, CdkNoDataRow, 5);\n      i0.ɵɵcontentQuery(dirIndex, CdkColumnDef, 5);\n      i0.ɵɵcontentQuery(dirIndex, CdkRowDef, 5);\n      i0.ɵɵcontentQuery(dirIndex, CdkHeaderRowDef, 5);\n      i0.ɵɵcontentQuery(dirIndex, CdkFooterRowDef, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._noDataRow = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentColumnDefs = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentRowDefs = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentHeaderRowDefs = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentFooterRowDefs = _t);\n    }\n  },\n  hostAttrs: [1, \"cdk-table\"],\n  hostVars: 2,\n  hostBindings: function _CdkTable_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cdk-table-fixed-layout\", ctx.fixedLayout);\n    }\n  },\n  inputs: {\n    trackBy: \"trackBy\",\n    dataSource: \"dataSource\",\n    multiTemplateDataRows: [2, \"multiTemplateDataRows\", \"multiTemplateDataRows\", booleanAttribute],\n    fixedLayout: [2, \"fixedLayout\", \"fixedLayout\", booleanAttribute]\n  },\n  outputs: {\n    contentChanged: \"contentChanged\"\n  },\n  exportAs: [\"cdkTable\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_TABLE,\n    useExisting: _CdkTable\n  }, {\n    provide: _VIEW_REPEATER_STRATEGY,\n    useClass: _DisposeViewRepeaterStrategy\n  }, {\n    provide: _COALESCED_STYLE_SCHEDULER,\n    useClass: _CoalescedStyleScheduler\n  },\n  // Prevent nested tables from seeing this table's StickyPositioningListener.\n  {\n    provide: STICKY_POSITIONING_LISTENER,\n    useValue: null\n  }])],\n  ngContentSelectors: _c1,\n  decls: 5,\n  vars: 2,\n  consts: [[\"role\", \"rowgroup\"], [\"headerRowOutlet\", \"\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n  template: function _CdkTable_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵprojection(0);\n      i0.ɵɵprojection(1, 1);\n      i0.ɵɵtemplate(2, _CdkTable_Conditional_2_Template, 1, 0)(3, _CdkTable_Conditional_3_Template, 7, 0)(4, _CdkTable_Conditional_4_Template, 4, 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(ctx._isServer ? 2 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx._isNativeHtmlTable ? 3 : 4);\n    }\n  },\n  dependencies: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n  styles: [\".cdk-table-fixed-layout{table-layout:fixed}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTable, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-table, table[cdk-table]',\n      exportAs: 'cdkTable',\n      template: CDK_TABLE_TEMPLATE,\n      host: {\n        'class': 'cdk-table',\n        '[class.cdk-table-fixed-layout]': 'fixedLayout'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: CDK_TABLE,\n        useExisting: CdkTable\n      }, {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }],\n      imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n      styles: [\".cdk-table-fixed-layout{table-layout:fixed}\\n\"]\n    }]\n  }], () => [], {\n    trackBy: [{\n      type: Input\n    }],\n    dataSource: [{\n      type: Input\n    }],\n    multiTemplateDataRows: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fixedLayout: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    contentChanged: [{\n      type: Output\n    }],\n    _contentColumnDefs: [{\n      type: ContentChildren,\n      args: [CdkColumnDef, {\n        descendants: true\n      }]\n    }],\n    _contentRowDefs: [{\n      type: ContentChildren,\n      args: [CdkRowDef, {\n        descendants: true\n      }]\n    }],\n    _contentHeaderRowDefs: [{\n      type: ContentChildren,\n      args: [CdkHeaderRowDef, {\n        descendants: true\n      }]\n    }],\n    _contentFooterRowDefs: [{\n      type: ContentChildren,\n      args: [CdkFooterRowDef, {\n        descendants: true\n      }]\n    }],\n    _noDataRow: [{\n      type: ContentChild,\n      args: [CdkNoDataRow]\n    }]\n  });\n})();\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet(array, set) {\n  return array.concat(Array.from(set));\n}\n/**\n * Finds the closest table section to an outlet. We can't use `HTMLElement.closest` for this,\n * because the node representing the outlet is a comment.\n */\nfunction closestTableSection(outlet, section) {\n  const uppercaseSection = section.toUpperCase();\n  let current = outlet.viewContainer.element.nativeElement;\n  while (current) {\n    // 1 is an element node.\n    const nodeName = current.nodeType === 1 ? current.nodeName : null;\n    if (nodeName === uppercaseSection) {\n      return current;\n    } else if (nodeName === 'TABLE') {\n      // Stop traversing past the `table` node.\n      break;\n    }\n    current = current.parentNode;\n  }\n  return null;\n}\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass CdkTextColumn {\n  /** Column name that should be used to reference this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._name = name;\n    // With Ivy, inputs can be initialized before static query results are\n    // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n    this._syncColumnDefName();\n  }\n  constructor() {\n    _defineProperty(this, \"_table\", inject(CdkTable, {\n      optional: true\n    }));\n    _defineProperty(this, \"_options\", inject(TEXT_COLUMN_OPTIONS, {\n      optional: true\n    }));\n    _defineProperty(this, \"_name\", void 0);\n    /**\n     * Text label that should be used for the column header. If this property is not\n     * set, the header text will default to the column name with its first letter capitalized.\n     */\n    _defineProperty(this, \"headerText\", void 0);\n    /**\n     * Accessor function to retrieve the data rendered for each cell. If this\n     * property is not set, the data cells will render the value found in the data's property matching\n     * the column's name. For example, if the column is named `id`, then the rendered value will be\n     * value defined by the data's `id` property.\n     */\n    _defineProperty(this, \"dataAccessor\", void 0);\n    /** Alignment of the cell values. */\n    _defineProperty(this, \"justify\", 'start');\n    /** @docs-private */\n    _defineProperty(this, \"columnDef\", void 0);\n    /**\n     * The column cell is provided to the column during `ngOnInit` with a static query.\n     * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n     * column definition was provided in the same view as the table, which is not the case with this\n     * component.\n     * @docs-private\n     */\n    _defineProperty(this, \"cell\", void 0);\n    /**\n     * The column headerCell is provided to the column during `ngOnInit` with a static query.\n     * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n     * column definition was provided in the same view as the table, which is not the case with this\n     * component.\n     * @docs-private\n     */\n    _defineProperty(this, \"headerCell\", void 0);\n    this._options = this._options || {};\n  }\n  ngOnInit() {\n    this._syncColumnDefName();\n    if (this.headerText === undefined) {\n      this.headerText = this._createDefaultHeaderText();\n    }\n    if (!this.dataAccessor) {\n      this.dataAccessor = this._options.defaultDataAccessor || ((data, name) => data[name]);\n    }\n    if (this._table) {\n      // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n      // since the columnDef will not pick up its content by the time the table finishes checking\n      // its content and initializing the rows.\n      this.columnDef.cell = this.cell;\n      this.columnDef.headerCell = this.headerCell;\n      this._table.addColumnDef(this.columnDef);\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getTableTextColumnMissingParentTableError();\n    }\n  }\n  ngOnDestroy() {\n    if (this._table) {\n      this._table.removeColumnDef(this.columnDef);\n    }\n  }\n  /**\n   * Creates a default header text. Use the options' header text transformation function if one\n   * has been provided. Otherwise simply capitalize the column name.\n   */\n  _createDefaultHeaderText() {\n    const name = this.name;\n    if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableTextColumnMissingNameError();\n    }\n    if (this._options && this._options.defaultHeaderTextTransform) {\n      return this._options.defaultHeaderTextTransform(name);\n    }\n    return name[0].toUpperCase() + name.slice(1);\n  }\n  /** Synchronizes the column definition name with the text column name. */\n  _syncColumnDefName() {\n    if (this.columnDef) {\n      this.columnDef.name = this.name;\n    }\n  }\n}\n_CdkTextColumn = CdkTextColumn;\n_defineProperty(CdkTextColumn, \"\\u0275fac\", function _CdkTextColumn_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTextColumn)();\n});\n_defineProperty(CdkTextColumn, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _CdkTextColumn,\n  selectors: [[\"cdk-text-column\"]],\n  viewQuery: function _CdkTextColumn_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(CdkColumnDef, 7);\n      i0.ɵɵviewQuery(CdkCellDef, 7);\n      i0.ɵɵviewQuery(CdkHeaderCellDef, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.columnDef = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n    }\n  },\n  inputs: {\n    name: \"name\",\n    headerText: \"headerText\",\n    dataAccessor: \"dataAccessor\",\n    justify: \"justify\"\n  },\n  decls: 3,\n  vars: 0,\n  consts: [[\"cdkColumnDef\", \"\"], [\"cdk-header-cell\", \"\", 3, \"text-align\", 4, \"cdkHeaderCellDef\"], [\"cdk-cell\", \"\", 3, \"text-align\", 4, \"cdkCellDef\"], [\"cdk-header-cell\", \"\"], [\"cdk-cell\", \"\"]],\n  template: function _CdkTextColumn_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainerStart(0, 0);\n      i0.ɵɵtemplate(1, _CdkTextColumn_th_1_Template, 2, 3, \"th\", 1)(2, _CdkTextColumn_td_2_Template, 2, 3, \"td\", 2);\n      i0.ɵɵelementContainerEnd();\n    }\n  },\n  dependencies: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextColumn, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-text-column',\n      template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      // Change detection is intentionally not set to OnPush. This component's template will be provided\n      // to the table to be inserted into its view. This is problematic when change detection runs since\n      // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n      // mean's the template in the table's view will not have the updated value (and in fact will cause\n      // an ExpressionChangedAfterItHasBeenCheckedError).\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell]\n    }]\n  }], () => [], {\n    name: [{\n      type: Input\n    }],\n    headerText: [{\n      type: Input\n    }],\n    dataAccessor: [{\n      type: Input\n    }],\n    justify: [{\n      type: Input\n    }],\n    columnDef: [{\n      type: ViewChild,\n      args: [CdkColumnDef, {\n        static: true\n      }]\n    }],\n    cell: [{\n      type: ViewChild,\n      args: [CdkCellDef, {\n        static: true\n      }]\n    }],\n    headerCell: [{\n      type: ViewChild,\n      args: [CdkHeaderCellDef, {\n        static: true\n      }]\n    }]\n  });\n})();\nconst EXPORTED_DECLARATIONS = [CdkTable, CdkRowDef, CdkCellDef, CdkCellOutlet, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkCell, CdkRow, CdkHeaderCell, CdkFooterCell, CdkHeaderRow, CdkHeaderRowDef, CdkFooterRow, CdkFooterRowDef, DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, CdkTextColumn, CdkNoDataRow, CdkRecycleRows, NoDataRowOutlet];\nclass CdkTableModule {}\n_CdkTableModule = CdkTableModule;\n_defineProperty(CdkTableModule, \"\\u0275fac\", function _CdkTableModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTableModule)();\n});\n_defineProperty(CdkTableModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _CdkTableModule,\n  imports: [ScrollingModule, CdkTable, CdkRowDef, CdkCellDef, CdkCellOutlet, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkCell, CdkRow, CdkHeaderCell, CdkFooterCell, CdkHeaderRow, CdkHeaderRowDef, CdkFooterRow, CdkFooterRowDef, DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, CdkTextColumn, CdkNoDataRow, CdkRecycleRows, NoDataRowOutlet],\n  exports: [CdkTable, CdkRowDef, CdkCellDef, CdkCellOutlet, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkCell, CdkRow, CdkHeaderCell, CdkFooterCell, CdkHeaderRow, CdkHeaderRowDef, CdkFooterRow, CdkFooterRowDef, DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, CdkTextColumn, CdkNoDataRow, CdkRecycleRows, NoDataRowOutlet]\n}));\n_defineProperty(CdkTableModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [ScrollingModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTableModule, [{\n    type: NgModule,\n    args: [{\n      exports: EXPORTED_DECLARATIONS,\n      imports: [ScrollingModule, ...EXPORTED_DECLARATIONS]\n    }]\n  }], null, null);\n})();\n\n/**\n * Mixin to provide a directive with a function that checks if the sticky input has been\n * changed since the last time the function was called. Essentially adds a dirty-check to the\n * sticky value.\n * @docs-private\n * @deprecated Implement the `CanStick` interface instead.\n * @breaking-change 19.0.0\n */\nfunction mixinHasStickyInput(base) {\n  return class extends base {\n    /** Whether sticky positioning should be applied. */\n    get sticky() {\n      return this._sticky;\n    }\n    set sticky(v) {\n      const prevValue = this._sticky;\n      this._sticky = coerceBooleanProperty(v);\n      this._hasStickyChanged = prevValue !== this._sticky;\n    }\n    /** Whether the sticky value has changed since this was last called. */\n    hasStickyChanged() {\n      const hasStickyChanged = this._hasStickyChanged;\n      this._hasStickyChanged = false;\n      return hasStickyChanged;\n    }\n    /** Resets the dirty check for cases where the sticky state has been used without checking. */\n    resetStickyChanged() {\n      this._hasStickyChanged = false;\n    }\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"_sticky\", false);\n      /** Whether the sticky input has changed since it was last checked. */\n      _defineProperty(this, \"_hasStickyChanged\", false);\n    }\n  };\n}\nexport { BaseCdkCell, BaseRowDef, CDK_ROW_TEMPLATE, CDK_TABLE, CDK_TABLE_TEMPLATE, CdkCell, CdkCellDef, CdkCellOutlet, CdkColumnDef, CdkFooterCell, CdkFooterCellDef, CdkFooterRow, CdkFooterRowDef, CdkHeaderCell, CdkHeaderCellDef, CdkHeaderRow, CdkHeaderRowDef, CdkNoDataRow, CdkRecycleRows, CdkRow, CdkRowDef, CdkTable, CdkTableModule, CdkTextColumn, DataRowOutlet, FooterRowOutlet, HeaderRowOutlet, NoDataRowOutlet, STICKY_DIRECTIONS, STICKY_POSITIONING_LISTENER, StickyStyler, TEXT_COLUMN_OPTIONS, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, _Schedule, mixinHasStickyInput };\n//# sourceMappingURL=table.mjs.map", "map": {"version": 3, "names": ["i0", "ɵɵprojection", "_CdkTable_Conditional_3_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainer", "ɵɵelementEnd", "_CdkTable_Conditional_4_Template", "_CdkTextColumn_th_1_Template", "ɵɵtext", "ctx_r0", "ɵɵnextContext", "ɵɵstyleProp", "justify", "ɵɵadvance", "ɵɵtextInterpolate1", "headerText", "_CdkTextColumn_td_2_Template", "data_r2", "$implicit", "dataAccessor", "name", "i", "isDataSource", "D", "DataSource", "DOCUMENT", "InjectionToken", "inject", "TemplateRef", "Directive", "booleanAttribute", "Input", "ContentChild", "ElementRef", "NgZone", "Injectable", "Iterable<PERSON><PERSON><PERSON>", "ViewContainerRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "afterNextRender", "ChangeDetectorRef", "EventEmitter", "Injector", "HostAttributeToken", "Output", "ContentChildren", "ViewChild", "NgModule", "Subject", "BehaviorSubject", "isObservable", "of", "takeUntil", "b", "_VIEW_REPEATER_STRATEGY", "_", "_RecycleViewRepeaterStrategy", "a", "_ViewRepeaterOperation", "_DisposeViewRepeaterStrategy", "Directionality", "P", "Platform", "ViewportRuler", "ScrollingModule", "c", "coerceBooleanProperty", "CDK_TABLE", "TEXT_COLUMN_OPTIONS", "CdkCellDef", "constructor", "_defineProperty", "_CdkCellDef", "_CdkCellDef_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "CdkHeaderCellDef", "_CdkHeaderCellDef", "_CdkHeaderCellDef_Factory", "CdkFooterCellDef", "_CdkFooterCellDef", "_CdkFooterCellDef_Factory", "CdkColumnDef", "_name", "_setNameInput", "sticky", "_sticky", "value", "_hasStickyChanged", "stickyEnd", "_stickyEnd", "optional", "hasStickyChanged", "resetStickyChanged", "_updateColumnCssClassName", "_columnCssClassName", "cssClassFriendlyName", "replace", "_CdkColumnDef", "_CdkColumnDef_Factory", "contentQueries", "_CdkColumnDef_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "cell", "first", "headerCell", "<PERSON><PERSON><PERSON><PERSON>", "inputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "transform", "BaseCdkCell", "columnDef", "elementRef", "nativeElement", "classList", "add", "CdkHeaderCell", "_CdkHeaderCell", "_CdkHeaderCell_Factory", "hostAttrs", "ɵɵInheritDefinitionFeature", "host", "CdkFooterCell", "_columnDef$_table", "role", "_table", "_getCellRole", "setAttribute", "_CdkFooterCell", "_CdkFooterCell_Factory", "CdkCell", "_columnDef$_table2", "_CdkCell", "_CdkCell_Factory", "_Schedule", "_COALESCED_STYLE_SCHEDULER", "_CoalescedStyleScheduler", "schedule", "task", "_createScheduleIfNeeded", "_currentSchedule", "tasks", "push", "scheduleEnd", "endTasks", "_ngZone", "runOutsideAngular", "queueMicrotask", "length", "_CoalescedStyleScheduler2", "_CoalescedStyleScheduler2_Factory", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "CDK_ROW_TEMPLATE", "BaseRowDef", "ngOnChanges", "changes", "_<PERSON><PERSON><PERSON><PERSON>", "columns", "currentValue", "_differs", "find", "create", "diff", "getColumnsDiff", "extractCellTemplate", "column", "CdkHeaderRowDef", "template", "CdkFooterRowDef", "_BaseRowDef", "_BaseRowDef_Factory", "ɵɵNgOnChangesFeature", "_CdkHeaderRowDef", "_CdkHeaderRowDef_Factory", "alias", "_CdkFooterRowDef", "_CdkFooterRowDef_Factory", "CdkRowDef", "_CdkRowDef", "_CdkRowDef_Factory", "when", "CdkCellOutlet", "mostRecentCellOutlet", "ngOnDestroy", "_CdkCellOutlet", "_CdkCellOutlet_Factory", "CdkHeaderRow", "_CdkHeaderRow", "_CdkHeaderRow_Factory", "ɵɵdefineComponent", "decls", "vars", "consts", "_CdkHeaderRow_Template", "dependencies", "encapsulation", "changeDetection", "<PERSON><PERSON><PERSON>", "None", "imports", "CdkFooterRow", "_CdkFooterRow", "_CdkFooterRow_Factory", "_CdkFooterRow_Template", "CdkRow", "_CdkRow", "_CdkRow_Factory", "_CdkRow_Template", "CdkNoDataRow", "_CdkNoDataRow", "_CdkNoDataRow_Factory", "STICKY_DIRECTIONS", "<PERSON>y<PERSON><PERSON><PERSON>", "_isNativeHtmlTable", "_stickCellCss", "direction", "_coalescedStyleScheduler", "_isBrowser", "_needsPositionStickyOnElement", "_positionListener", "_tableInjector", "WeakMap", "globalThis", "ResizeObserver", "entries", "_updateCachedSizes", "_borderCellCss", "clearStickyPositioning", "rows", "stickyDirections", "includes", "_removeFromStickyColumnReplayQueue", "elementsToClear", "row", "nodeType", "ELEMENT_NODE", "Array", "from", "children", "_afterNextRender", "write", "element", "_removeStickyStyle", "updateStickyColumns", "stickyStartStates", "stickyEndStates", "recalculateCellWidths", "replay", "some", "state", "_this$_positionListen", "_this$_positionListen2", "stickyColumnsUpdated", "sizes", "stickyEndColumnsUpdated", "firstRow", "num<PERSON>ells", "isRtl", "start", "end", "lastStickyStart", "lastIndexOf", "firstStickyEnd", "indexOf", "cellWidths", "startPositions", "endPositions", "_updateStickyColumnReplayQueue", "earlyRead", "_get<PERSON><PERSON><PERSON><PERSON><PERSON>", "_getStickyStartColumnPositions", "_getStickyEndColumnPositions", "_addStickyStyle", "w", "slice", "map", "width", "index", "reverse", "stickRows", "rowsToStick", "stickyStates", "position", "states", "stickyOffsets", "stickyCellHeights", "elementsToStick", "rowIndex", "stickyOffset", "height", "_retrieveElementSize", "borderedRowIndex", "offset", "isBorderedRowIndex", "_this$_positionListen3", "stickyHeaderRowsUpdated", "offsets", "elements", "_this$_positionListen4", "stickyFooterRowsUpdated", "updateStickyFooterContainer", "tableElement", "tfoot", "querySelector", "destroy", "_this$_resizeObserver", "_stickyColumnsReplayTimeout", "clearTimeout", "_resizeObserver", "disconnect", "_destroyed", "contains", "dir", "style", "remove", "hasDirection", "zIndex", "_getCalculatedZIndex", "dir<PERSON><PERSON><PERSON>", "isBorderElement", "cssText", "zIndexIncrements", "top", "bottom", "left", "right", "_cachedCellWidths", "firstRowCells", "widths", "positions", "nextPosition", "cachedSize", "_elemSizeCache", "get", "clientRect", "getBoundingClientRect", "size", "set", "observe", "box", "params", "_updatedStickyColumnsParamsToReplay", "rowsSet", "Set", "update", "filter", "has", "needsColumnUpdate", "entry", "_entry$borderBoxSize", "_this$_elemSizeCache$", "newEntry", "borderBoxSize", "inlineSize", "blockSize", "contentRect", "target", "isCell", "setTimeout", "spec", "injector", "_spec$earlyRead", "call", "klass", "getTableUnknownColumnError", "id", "Error", "getTableDuplicateColumnNameError", "getTableMultipleDefaultRowDefsError", "getTableMissingMatchingRowDefError", "data", "JSON", "stringify", "getTableMissingRowDefsError", "getTableUnknownDataSourceError", "getTableTextColumnMissingParentTableError", "getTableTextColumnMissingNameError", "STICKY_POSITIONING_LISTENER", "CdkRecycleRows", "_CdkRecycleRows", "_CdkRecycleRows_Factory", "useClass", "DataRowOutlet", "table", "_rowOutlet", "_outletAssigned", "_DataRowOutlet", "_DataRowOutlet_Factory", "HeaderRowOutlet", "_headerRowOutlet", "_HeaderRowOutlet", "_HeaderRowOutlet_Factory", "FooterRowOutlet", "_footerRowOutlet", "_FooterRowOutlet", "_FooterRowOutlet_Factory", "NoDataRowOutlet", "_noDataRowOutlet", "_NoDataRowOutlet", "_NoDataRowOutlet_Factory", "CDK_TABLE_TEMPLATE", "CdkTable", "_cellRoleInternal", "undefined", "tableRole", "_elementRef", "getAttribute", "trackBy", "_trackByFn", "fn", "console", "warn", "dataSource", "_dataSource", "_switchDataSource", "multiTemplateDataRows", "_multiTemplateDataRows", "viewContainer", "_forceRenderDataRows", "updateStickyColumnStyles", "fixedLayout", "_fixedLayout", "_forceRecalculateCellWidths", "_stickyColumnStylesNeedReset", "skipSelf", "Map", "Number", "MAX_VALUE", "_isServer", "_platform", "<PERSON><PERSON><PERSON><PERSON>", "nodeName", "_data<PERSON><PERSON>er", "_i", "dataRow", "dataIndex", "ngOnInit", "_setupStickyStyler", "_viewportRuler", "change", "pipe", "_onD<PERSON>roy", "subscribe", "ngAfterContentInit", "_hasInitialized", "ngAfterContentChecked", "_canRender", "_render", "_this$_stickyStyler", "_this$_rowOutlet", "_this$_headerRowOutle", "_this$_footerRowOutle", "_sticky<PERSON><PERSON><PERSON>", "_cachedRenderRowsMap", "_customColumnDefs", "_customRowDefs", "_customHeaderRowDefs", "_customFooterRowDefs", "_columnDefsByName", "for<PERSON>ach", "def", "clear", "_headerRowDefs", "_footerRowDefs", "_defaultRowDef", "next", "complete", "renderRows", "_renderRows", "_getAllRenderRows", "_updateNoDataRow", "contentChanged", "_view<PERSON><PERSON><PERSON>er", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "item", "operation", "INSERTED", "context", "_renderCellTemplateForItem", "rowDef", "_updateRowIndexContext", "forEachIdentityChange", "row<PERSON>iew", "addColumnDef", "removeColumnDef", "delete", "addRowDef", "removeRowDef", "addHeaderRowDef", "headerRowDef", "_headerRowDefChanged", "removeHeaderRowDef", "addFooterRowDef", "footerRowDef", "_footerRowDefChanged", "removeFooterRowDef", "setNoDataRow", "noDataRow", "_customNoDataRow", "updateStickyHeaderRowStyles", "headerRows", "_getRenderedRows", "thead", "closestTableSection", "display", "updateStickyFooterRowStyles", "footerRows", "dataRows", "headerRow", "_addStickyColumnStyles", "_rowDefs", "footerRow", "values", "_hasAllOutlets", "_cacheRowDefs", "_cacheColumnDefs", "columnsChanged", "_renderUpdatedColumns", "rowDefsChanged", "_forceRenderHeaderRows", "_forceRenderFooterRows", "_renderChangeSubscription", "_observe<PERSON><PERSON><PERSON><PERSON><PERSON>", "_checkStickyStates", "prevCachedRenderRows", "_data", "renderRowsForData", "_getRenderRowsForData", "j", "renderRow", "cache", "rowDefs", "_getRowDefs", "cachedRenderRows", "shift", "columnDefs", "mergeArrayAndSet", "_getOwnDefs", "_contentColumnDefs", "_contentHeaderRowDefs", "_contentFooterRowDefs", "_contentRowDefs", "defaultRowDefs", "columnsDiffReducer", "acc", "dataColumnsChanged", "reduce", "headerColumnsChanged", "footerColumnsChanged", "unsubscribe", "dataStream", "connect", "isArray", "_renderRow", "columnName", "rowOutlet", "renderedRows", "viewRef", "rootNodes", "templateRef", "outlet", "view", "createEmbeddedView", "cellTemplate", "_getCellTemplates", "_viewContainer", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderIndex", "count", "last", "even", "odd", "columnId", "stickyCheckReducer", "d", "_dir", "stickyCssClass", "needsPositionStickyOnElement", "_stickyPositioningListener", "_injector", "items", "_noDataRow", "shouldShow", "_isShowingNoDataRow", "container", "rootNode", "_document", "_contentClassName", "_CdkTable", "_CdkTable_Factory", "_CdkTable_ContentQueries", "hostVars", "hostBindings", "_CdkTable_HostBindings", "ɵɵclassProp", "outputs", "exportAs", "useValue", "ngContentSelectors", "_c1", "_CdkTable_Template", "ɵɵprojectionDef", "_c0", "ɵɵtemplate", "_CdkTable_Conditional_2_Template", "ɵɵconditional", "styles", "descendants", "array", "concat", "section", "uppercaseSection", "toUpperCase", "current", "parentNode", "CdkTextColumn", "_syncColumnDefName", "_options", "_createDefaultHeaderText", "defaultDataAccessor", "defaultHeaderTextTransform", "_CdkTextColumn", "_CdkTextColumn_Factory", "viewQuery", "_CdkTextColumn_Query", "ɵɵviewQuery", "_CdkTextColumn_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "static", "EXPORTED_DECLARATIONS", "CdkTableModule", "_CdkTableModule", "_CdkTableModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "mixinHasStickyInput", "base", "v", "prevValue"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/table.mjs"], "sourcesContent": ["import { i as isDataSource } from './data-source-D34wiQZj.mjs';\nexport { D as DataSource } from './data-source-D34wiQZj.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, booleanAttribute, Input, ContentChild, ElementRef, NgZone, Injectable, IterableDiffers, ViewContainerRef, Component, ChangeDetectionStrategy, ViewEncapsulation, afterNextRender, ChangeDetectorRef, EventEmitter, Injector, HostAttributeToken, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, isObservable, of } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { b as _VIEW_REPEATER_STRATEGY, _ as _RecycleViewRepeaterStrategy, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-D_JReLI1.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { ViewportRuler, ScrollingModule } from './scrolling.mjs';\nimport { c as coerceBooleanProperty } from './boolean-property-DaaVhX5A.mjs';\nimport './element-x4z00URv.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nconst CDK_TABLE = new InjectionToken('CDK_TABLE');\n/** Injection token that can be used to specify the text column options. */\nconst TEXT_COLUMN_OPTIONS = new InjectionToken('text-column-options');\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass CdkCellDef {\n    /** @docs-private */\n    template = inject(TemplateRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkCellDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkCellDef, isStandalone: true, selector: \"[cdkCellDef]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkCellDef]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass CdkHeaderCellDef {\n    /** @docs-private */\n    template = inject(TemplateRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkHeaderCellDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkHeaderCellDef, isStandalone: true, selector: \"[cdkHeaderCellDef]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkHeaderCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkHeaderCellDef]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass CdkFooterCellDef {\n    /** @docs-private */\n    template = inject(TemplateRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkFooterCellDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkFooterCellDef, isStandalone: true, selector: \"[cdkFooterCellDef]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkFooterCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkFooterCellDef]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\nclass CdkColumnDef {\n    _table = inject(CDK_TABLE, { optional: true });\n    _hasStickyChanged = false;\n    /** Unique name for this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._setNameInput(name);\n    }\n    _name;\n    /** Whether the cell is sticky. */\n    get sticky() {\n        return this._sticky;\n    }\n    set sticky(value) {\n        if (value !== this._sticky) {\n            this._sticky = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    _sticky = false;\n    /**\n     * Whether this column should be sticky positioned on the end of the row. Should make sure\n     * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n     * has been changed.\n     */\n    get stickyEnd() {\n        return this._stickyEnd;\n    }\n    set stickyEnd(value) {\n        if (value !== this._stickyEnd) {\n            this._stickyEnd = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    _stickyEnd = false;\n    /** @docs-private */\n    cell;\n    /** @docs-private */\n    headerCell;\n    /** @docs-private */\n    footerCell;\n    /**\n     * Transformed version of the column name that can be used as part of a CSS classname. Excludes\n     * all non-alphanumeric characters and the special characters '-' and '_'. Any characters that\n     * do not match are replaced by the '-' character.\n     */\n    cssClassFriendlyName;\n    /**\n     * Class name for cells in this column.\n     * @docs-private\n     */\n    _columnCssClassName;\n    constructor() { }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n        const hasStickyChanged = this._hasStickyChanged;\n        this.resetStickyChanged();\n        return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n        this._hasStickyChanged = false;\n    }\n    /**\n     * Overridable method that sets the css classes that will be added to every cell in this\n     * column.\n     * In the future, columnCssClassName will change from type string[] to string and this\n     * will set a single string value.\n     * @docs-private\n     */\n    _updateColumnCssClassName() {\n        this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setNameInput(value) {\n        // If the directive is set without a name (updated programmatically), then this setter will\n        // trigger with an empty string and should not overwrite the programmatically set value.\n        if (value) {\n            this._name = value;\n            this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n            this._updateColumnCssClassName();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkColumnDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkColumnDef, isStandalone: true, selector: \"[cdkColumnDef]\", inputs: { name: [\"cdkColumnDef\", \"name\"], sticky: [\"sticky\", \"sticky\", booleanAttribute], stickyEnd: [\"stickyEnd\", \"stickyEnd\", booleanAttribute] }, providers: [{ provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: CdkColumnDef }], queries: [{ propertyName: \"cell\", first: true, predicate: CdkCellDef, descendants: true }, { propertyName: \"headerCell\", first: true, predicate: CdkHeaderCellDef, descendants: true }, { propertyName: \"footerCell\", first: true, predicate: CdkFooterCellDef, descendants: true }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkColumnDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkColumnDef]',\n                    providers: [{ provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: CdkColumnDef }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { name: [{\n                type: Input,\n                args: ['cdkColumnDef']\n            }], sticky: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stickyEnd: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], cell: [{\n                type: ContentChild,\n                args: [CdkCellDef]\n            }], headerCell: [{\n                type: ContentChild,\n                args: [CdkHeaderCellDef]\n            }], footerCell: [{\n                type: ContentChild,\n                args: [CdkFooterCellDef]\n            }] } });\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nclass BaseCdkCell {\n    constructor(columnDef, elementRef) {\n        elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n    }\n}\n/** Header cell template container that adds the right classes and role. */\nclass CdkHeaderCell extends BaseCdkCell {\n    constructor() {\n        super(inject(CdkColumnDef), inject(ElementRef));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkHeaderCell, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkHeaderCell, isStandalone: true, selector: \"cdk-header-cell, th[cdk-header-cell]\", host: { attributes: { \"role\": \"columnheader\" }, classAttribute: \"cdk-header-cell\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkHeaderCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-header-cell, th[cdk-header-cell]',\n                    host: {\n                        'class': 'cdk-header-cell',\n                        'role': 'columnheader',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n/** Footer cell template container that adds the right classes and role. */\nclass CdkFooterCell extends BaseCdkCell {\n    constructor() {\n        const columnDef = inject(CdkColumnDef);\n        const elementRef = inject(ElementRef);\n        super(columnDef, elementRef);\n        const role = columnDef._table?._getCellRole();\n        if (role) {\n            elementRef.nativeElement.setAttribute('role', role);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkFooterCell, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkFooterCell, isStandalone: true, selector: \"cdk-footer-cell, td[cdk-footer-cell]\", host: { classAttribute: \"cdk-footer-cell\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkFooterCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-footer-cell, td[cdk-footer-cell]',\n                    host: {\n                        'class': 'cdk-footer-cell',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n/** Cell template container that adds the right classes and role. */\nclass CdkCell extends BaseCdkCell {\n    constructor() {\n        const columnDef = inject(CdkColumnDef);\n        const elementRef = inject(ElementRef);\n        super(columnDef, elementRef);\n        const role = columnDef._table?._getCellRole();\n        if (role) {\n            elementRef.nativeElement.setAttribute('role', role);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkCell, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkCell, isStandalone: true, selector: \"cdk-cell, td[cdk-cell]\", host: { classAttribute: \"cdk-cell\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-cell, td[cdk-cell]',\n                    host: {\n                        'class': 'cdk-cell',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * @docs-private\n */\nclass _Schedule {\n    tasks = [];\n    endTasks = [];\n}\n/** Injection token used to provide a coalesced style scheduler. */\nconst _COALESCED_STYLE_SCHEDULER = new InjectionToken('_COALESCED_STYLE_SCHEDULER');\n/**\n * Allows grouping up CSSDom mutations after the current execution context.\n * This can significantly improve performance when separate consecutive functions are\n * reading from the CSSDom and then mutating it.\n *\n * @docs-private\n */\nclass _CoalescedStyleScheduler {\n    _currentSchedule = null;\n    _ngZone = inject(NgZone);\n    constructor() { }\n    /**\n     * Schedules the specified task to run at the end of the current VM turn.\n     */\n    schedule(task) {\n        this._createScheduleIfNeeded();\n        this._currentSchedule.tasks.push(task);\n    }\n    /**\n     * Schedules the specified task to run after other scheduled tasks at the end of the current\n     * VM turn.\n     */\n    scheduleEnd(task) {\n        this._createScheduleIfNeeded();\n        this._currentSchedule.endTasks.push(task);\n    }\n    _createScheduleIfNeeded() {\n        if (this._currentSchedule) {\n            return;\n        }\n        this._currentSchedule = new _Schedule();\n        this._ngZone.runOutsideAngular(() => \n        // TODO(mmalerba): Scheduling this using something that runs less frequently\n        //  (e.g. requestAnimationFrame, setTimeout, etc.) causes noticeable jank with the column\n        //  resizer. We should audit the usages of schedule / scheduleEnd in that component and see\n        //  if we can refactor it so that we don't need to flush the tasks quite so frequently.\n        queueMicrotask(() => {\n            while (this._currentSchedule.tasks.length || this._currentSchedule.endTasks.length) {\n                const schedule = this._currentSchedule;\n                // Capture new tasks scheduled by the current set of tasks.\n                this._currentSchedule = new _Schedule();\n                for (const task of schedule.tasks) {\n                    task();\n                }\n                for (const task of schedule.endTasks) {\n                    task();\n                }\n            }\n            this._currentSchedule = null;\n        }));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CoalescedStyleScheduler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CoalescedStyleScheduler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CoalescedStyleScheduler, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nconst CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nclass BaseRowDef {\n    template = inject(TemplateRef);\n    _differs = inject(IterableDiffers);\n    /** The columns to be displayed on this row. */\n    columns;\n    /** Differ used to check if any changes were made to the columns. */\n    _columnsDiffer;\n    constructor() { }\n    ngOnChanges(changes) {\n        // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n        // of the columns property or an empty array if none is provided.\n        if (!this._columnsDiffer) {\n            const columns = (changes['columns'] && changes['columns'].currentValue) || [];\n            this._columnsDiffer = this._differs.find(columns).create();\n            this._columnsDiffer.diff(columns);\n        }\n    }\n    /**\n     * Returns the difference between the current columns and the columns from the last diff, or null\n     * if there is no difference.\n     */\n    getColumnsDiff() {\n        return this._columnsDiffer.diff(this.columns);\n    }\n    /** Gets this row def's relevant cell template from the provided column def. */\n    extractCellTemplate(column) {\n        if (this instanceof CdkHeaderRowDef) {\n            return column.headerCell.template;\n        }\n        if (this instanceof CdkFooterRowDef) {\n            return column.footerCell.template;\n        }\n        else {\n            return column.cell.template;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BaseRowDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: BaseRowDef, isStandalone: true, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BaseRowDef, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [] });\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass CdkHeaderRowDef extends BaseRowDef {\n    _table = inject(CDK_TABLE, { optional: true });\n    _hasStickyChanged = false;\n    /** Whether the row is sticky. */\n    get sticky() {\n        return this._sticky;\n    }\n    set sticky(value) {\n        if (value !== this._sticky) {\n            this._sticky = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    _sticky = false;\n    constructor() {\n        super(inject(TemplateRef), inject(IterableDiffers));\n    }\n    // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n    // Explicitly define it so that the method is called as part of the Angular lifecycle.\n    ngOnChanges(changes) {\n        super.ngOnChanges(changes);\n    }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n        const hasStickyChanged = this._hasStickyChanged;\n        this.resetStickyChanged();\n        return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n        this._hasStickyChanged = false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkHeaderRowDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkHeaderRowDef, isStandalone: true, selector: \"[cdkHeaderRowDef]\", inputs: { columns: [\"cdkHeaderRowDef\", \"columns\"], sticky: [\"cdkHeaderRowDefSticky\", \"sticky\", booleanAttribute] }, usesInheritance: true, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkHeaderRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkHeaderRowDef]',\n                    inputs: [{ name: 'columns', alias: 'cdkHeaderRowDef' }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { sticky: [{\n                type: Input,\n                args: [{ alias: 'cdkHeaderRowDefSticky', transform: booleanAttribute }]\n            }] } });\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass CdkFooterRowDef extends BaseRowDef {\n    _table = inject(CDK_TABLE, { optional: true });\n    _hasStickyChanged = false;\n    /** Whether the row is sticky. */\n    get sticky() {\n        return this._sticky;\n    }\n    set sticky(value) {\n        if (value !== this._sticky) {\n            this._sticky = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    _sticky = false;\n    constructor() {\n        super(inject(TemplateRef), inject(IterableDiffers));\n    }\n    // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n    // Explicitly define it so that the method is called as part of the Angular lifecycle.\n    ngOnChanges(changes) {\n        super.ngOnChanges(changes);\n    }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n        const hasStickyChanged = this._hasStickyChanged;\n        this.resetStickyChanged();\n        return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n        this._hasStickyChanged = false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkFooterRowDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkFooterRowDef, isStandalone: true, selector: \"[cdkFooterRowDef]\", inputs: { columns: [\"cdkFooterRowDef\", \"columns\"], sticky: [\"cdkFooterRowDefSticky\", \"sticky\", booleanAttribute] }, usesInheritance: true, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkFooterRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkFooterRowDef]',\n                    inputs: [{ name: 'columns', alias: 'cdkFooterRowDef' }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { sticky: [{\n                type: Input,\n                args: [{ alias: 'cdkFooterRowDefSticky', transform: booleanAttribute }]\n            }] } });\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass CdkRowDef extends BaseRowDef {\n    _table = inject(CDK_TABLE, { optional: true });\n    /**\n     * Function that should return true if this row template should be used for the provided index\n     * and row data. If left undefined, this row will be considered the default row template to use\n     * when no other when functions return true for the data.\n     * For every row, there must be at least one when function that passes or an undefined to default.\n     */\n    when;\n    constructor() {\n        // TODO(andrewseguin): Add an input for providing a switch function to determine\n        //   if this template should be used.\n        super(inject(TemplateRef), inject(IterableDiffers));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkRowDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkRowDef, isStandalone: true, selector: \"[cdkRowDef]\", inputs: { columns: [\"cdkRowDefColumns\", \"columns\"], when: [\"cdkRowDefWhen\", \"when\"] }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkRowDef]',\n                    inputs: [\n                        { name: 'columns', alias: 'cdkRowDefColumns' },\n                        { name: 'when', alias: 'cdkRowDefWhen' },\n                    ],\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\nclass CdkCellOutlet {\n    _viewContainer = inject(ViewContainerRef);\n    /** The ordered list of cells to render within this outlet's view container */\n    cells;\n    /** The data context to be provided to each cell */\n    context;\n    /**\n     * Static property containing the latest constructed instance of this class.\n     * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n     * createEmbeddedView. After one of these components are created, this property will provide\n     * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n     * construct the cells with the provided context.\n     */\n    static mostRecentCellOutlet = null;\n    constructor() {\n        CdkCellOutlet.mostRecentCellOutlet = this;\n    }\n    ngOnDestroy() {\n        // If this was the last outlet being rendered in the view, remove the reference\n        // from the static property after it has been destroyed to avoid leaking memory.\n        if (CdkCellOutlet.mostRecentCellOutlet === this) {\n            CdkCellOutlet.mostRecentCellOutlet = null;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkCellOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkCellOutlet, isStandalone: true, selector: \"[cdkCellOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkCellOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkCellOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass CdkHeaderRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkHeaderRow, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkHeaderRow, isStandalone: true, selector: \"cdk-header-row, tr[cdk-header-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-header-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkHeaderRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-header-row, tr[cdk-header-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-header-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass CdkFooterRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkFooterRow, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkFooterRow, isStandalone: true, selector: \"cdk-footer-row, tr[cdk-footer-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-footer-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkFooterRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-footer-row, tr[cdk-footer-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-footer-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass CdkRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkRow, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkRow, isStandalone: true, selector: \"cdk-row, tr[cdk-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-row, tr[cdk-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Row that can be used to display a message when no data is shown in the table. */\nclass CdkNoDataRow {\n    templateRef = inject(TemplateRef);\n    _contentClassName = 'cdk-no-data-row';\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkNoDataRow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkNoDataRow, isStandalone: true, selector: \"ng-template[cdkNoDataRow]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkNoDataRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkNoDataRow]',\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Directions that can be used when setting sticky positioning.\n * @docs-private\n */\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nconst STICKY_DIRECTIONS = ['top', 'bottom', 'left', 'right'];\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nclass StickyStyler {\n    _isNativeHtmlTable;\n    _stickCellCss;\n    direction;\n    _coalescedStyleScheduler;\n    _isBrowser;\n    _needsPositionStickyOnElement;\n    _positionListener;\n    _tableInjector;\n    _elemSizeCache = new WeakMap();\n    _resizeObserver = globalThis?.ResizeObserver\n        ? new globalThis.ResizeObserver(entries => this._updateCachedSizes(entries))\n        : null;\n    _updatedStickyColumnsParamsToReplay = [];\n    _stickyColumnsReplayTimeout = null;\n    _cachedCellWidths = [];\n    _borderCellCss;\n    _destroyed = false;\n    /**\n     * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n     *     that uses the native `<table>` element.\n     * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n     *     sticky positioning applied.\n     * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n     *     by reversing left/right positions.\n     * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n     * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n     *     using inline styles. If false, it is assumed that position: sticky is included in\n     *     the component stylesheet for _stickCellCss.\n     * @param _positionListener A listener that is notified of changes to sticky rows/columns\n     *     and their dimensions.\n     * @param _tableInjector The table's Injector.\n     */\n    constructor(_isNativeHtmlTable, _stickCellCss, direction, _coalescedStyleScheduler, _isBrowser = true, _needsPositionStickyOnElement = true, _positionListener, _tableInjector) {\n        this._isNativeHtmlTable = _isNativeHtmlTable;\n        this._stickCellCss = _stickCellCss;\n        this.direction = direction;\n        this._coalescedStyleScheduler = _coalescedStyleScheduler;\n        this._isBrowser = _isBrowser;\n        this._needsPositionStickyOnElement = _needsPositionStickyOnElement;\n        this._positionListener = _positionListener;\n        this._tableInjector = _tableInjector;\n        this._borderCellCss = {\n            'top': `${_stickCellCss}-border-elem-top`,\n            'bottom': `${_stickCellCss}-border-elem-bottom`,\n            'left': `${_stickCellCss}-border-elem-left`,\n            'right': `${_stickCellCss}-border-elem-right`,\n        };\n    }\n    /**\n     * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n     * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n     * @param rows The list of rows that should be cleared from sticking in the provided directions\n     * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n     */\n    clearStickyPositioning(rows, stickyDirections) {\n        if (stickyDirections.includes('left') || stickyDirections.includes('right')) {\n            this._removeFromStickyColumnReplayQueue(rows);\n        }\n        const elementsToClear = [];\n        for (const row of rows) {\n            // If the row isn't an element (e.g. if it's an `ng-container`),\n            // it won't have inline styles or `children` so we skip it.\n            if (row.nodeType !== row.ELEMENT_NODE) {\n                continue;\n            }\n            elementsToClear.push(row, ...Array.from(row.children));\n        }\n        // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n        this._afterNextRender({\n            write: () => {\n                for (const element of elementsToClear) {\n                    this._removeStickyStyle(element, stickyDirections);\n                }\n            },\n        });\n    }\n    /**\n     * Applies sticky left and right positions to the cells of each row according to the sticky\n     * states of the rendered column definitions.\n     * @param rows The rows that should have its set of cells stuck according to the sticky states.\n     * @param stickyStartStates A list of boolean states where each state represents whether the cell\n     *     in this index position should be stuck to the start of the row.\n     * @param stickyEndStates A list of boolean states where each state represents whether the cell\n     *     in this index position should be stuck to the end of the row.\n     * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n     *     column cell. If `false` cached widths will be used instead.\n     * @param replay Whether to enqueue this call for replay after a ResizeObserver update.\n     */\n    updateStickyColumns(rows, stickyStartStates, stickyEndStates, recalculateCellWidths = true, replay = true) {\n        // Don't cache any state if none of the columns are sticky.\n        if (!rows.length ||\n            !this._isBrowser ||\n            !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))) {\n            this._positionListener?.stickyColumnsUpdated({ sizes: [] });\n            this._positionListener?.stickyEndColumnsUpdated({ sizes: [] });\n            return;\n        }\n        // Coalesce with sticky row updates (and potentially other changes like column resize).\n        const firstRow = rows[0];\n        const numCells = firstRow.children.length;\n        const isRtl = this.direction === 'rtl';\n        const start = isRtl ? 'right' : 'left';\n        const end = isRtl ? 'left' : 'right';\n        const lastStickyStart = stickyStartStates.lastIndexOf(true);\n        const firstStickyEnd = stickyEndStates.indexOf(true);\n        let cellWidths;\n        let startPositions;\n        let endPositions;\n        if (replay) {\n            this._updateStickyColumnReplayQueue({\n                rows: [...rows],\n                stickyStartStates: [...stickyStartStates],\n                stickyEndStates: [...stickyEndStates],\n            });\n        }\n        this._afterNextRender({\n            earlyRead: () => {\n                cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n                startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n                endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n            },\n            write: () => {\n                for (const row of rows) {\n                    for (let i = 0; i < numCells; i++) {\n                        const cell = row.children[i];\n                        if (stickyStartStates[i]) {\n                            this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n                        }\n                        if (stickyEndStates[i]) {\n                            this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n                        }\n                    }\n                }\n                if (this._positionListener && cellWidths.some(w => !!w)) {\n                    this._positionListener.stickyColumnsUpdated({\n                        sizes: lastStickyStart === -1\n                            ? []\n                            : cellWidths\n                                .slice(0, lastStickyStart + 1)\n                                .map((width, index) => (stickyStartStates[index] ? width : null)),\n                    });\n                    this._positionListener.stickyEndColumnsUpdated({\n                        sizes: firstStickyEnd === -1\n                            ? []\n                            : cellWidths\n                                .slice(firstStickyEnd)\n                                .map((width, index) => (stickyEndStates[index + firstStickyEnd] ? width : null))\n                                .reverse(),\n                    });\n                }\n            },\n        });\n    }\n    /**\n     * Applies sticky positioning to the row's cells if using the native table layout, and to the\n     * row itself otherwise.\n     * @param rowsToStick The list of rows that should be stuck according to their corresponding\n     *     sticky state and to the provided top or bottom position.\n     * @param stickyStates A list of boolean states where each state represents whether the row\n     *     should be stuck in the particular top or bottom position.\n     * @param position The position direction in which the row should be stuck if that row should be\n     *     sticky.\n     *\n     */\n    stickRows(rowsToStick, stickyStates, position) {\n        // Since we can't measure the rows on the server, we can't stick the rows properly.\n        if (!this._isBrowser) {\n            return;\n        }\n        // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n        // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n        // sticky states need to be reversed as well.\n        const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n        const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n        // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n        const stickyOffsets = [];\n        const stickyCellHeights = [];\n        const elementsToStick = [];\n        // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n        // (and potentially other changes like column resize).\n        this._afterNextRender({\n            earlyRead: () => {\n                for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n                    if (!states[rowIndex]) {\n                        continue;\n                    }\n                    stickyOffsets[rowIndex] = stickyOffset;\n                    const row = rows[rowIndex];\n                    elementsToStick[rowIndex] = this._isNativeHtmlTable\n                        ? Array.from(row.children)\n                        : [row];\n                    const height = this._retrieveElementSize(row).height;\n                    stickyOffset += height;\n                    stickyCellHeights[rowIndex] = height;\n                }\n            },\n            write: () => {\n                const borderedRowIndex = states.lastIndexOf(true);\n                for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n                    if (!states[rowIndex]) {\n                        continue;\n                    }\n                    const offset = stickyOffsets[rowIndex];\n                    const isBorderedRowIndex = rowIndex === borderedRowIndex;\n                    for (const element of elementsToStick[rowIndex]) {\n                        this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n                    }\n                }\n                if (position === 'top') {\n                    this._positionListener?.stickyHeaderRowsUpdated({\n                        sizes: stickyCellHeights,\n                        offsets: stickyOffsets,\n                        elements: elementsToStick,\n                    });\n                }\n                else {\n                    this._positionListener?.stickyFooterRowsUpdated({\n                        sizes: stickyCellHeights,\n                        offsets: stickyOffsets,\n                        elements: elementsToStick,\n                    });\n                }\n            },\n        });\n    }\n    /**\n     * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n     * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n     * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n     * the tfoot element.\n     */\n    updateStickyFooterContainer(tableElement, stickyStates) {\n        if (!this._isNativeHtmlTable) {\n            return;\n        }\n        // Coalesce with other sticky updates (and potentially other changes like column resize).\n        this._afterNextRender({\n            write: () => {\n                const tfoot = tableElement.querySelector('tfoot');\n                if (tfoot) {\n                    if (stickyStates.some(state => !state)) {\n                        this._removeStickyStyle(tfoot, ['bottom']);\n                    }\n                    else {\n                        this._addStickyStyle(tfoot, 'bottom', 0, false);\n                    }\n                }\n            },\n        });\n    }\n    /** Triggered by the table's OnDestroy hook. */\n    destroy() {\n        if (this._stickyColumnsReplayTimeout) {\n            clearTimeout(this._stickyColumnsReplayTimeout);\n        }\n        this._resizeObserver?.disconnect();\n        this._destroyed = true;\n    }\n    /**\n     * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n     * the zIndex, removing each of the provided sticky directions, and removing the\n     * sticky position if there are no more directions.\n     */\n    _removeStickyStyle(element, stickyDirections) {\n        if (!element.classList.contains(this._stickCellCss)) {\n            return;\n        }\n        for (const dir of stickyDirections) {\n            element.style[dir] = '';\n            element.classList.remove(this._borderCellCss[dir]);\n        }\n        // If the element no longer has any more sticky directions, remove sticky positioning and\n        // the sticky CSS class.\n        // Short-circuit checking element.style[dir] for stickyDirections as they\n        // were already removed above.\n        const hasDirection = STICKY_DIRECTIONS.some(dir => stickyDirections.indexOf(dir) === -1 && element.style[dir]);\n        if (hasDirection) {\n            element.style.zIndex = this._getCalculatedZIndex(element);\n        }\n        else {\n            // When not hasDirection, _getCalculatedZIndex will always return ''.\n            element.style.zIndex = '';\n            if (this._needsPositionStickyOnElement) {\n                element.style.position = '';\n            }\n            element.classList.remove(this._stickCellCss);\n        }\n    }\n    /**\n     * Adds the sticky styling to the element by adding the sticky style class, changing position\n     * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n     * direction and value.\n     */\n    _addStickyStyle(element, dir, dirValue, isBorderElement) {\n        element.classList.add(this._stickCellCss);\n        if (isBorderElement) {\n            element.classList.add(this._borderCellCss[dir]);\n        }\n        element.style[dir] = `${dirValue}px`;\n        element.style.zIndex = this._getCalculatedZIndex(element);\n        if (this._needsPositionStickyOnElement) {\n            element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n        }\n    }\n    /**\n     * Calculate what the z-index should be for the element, depending on what directions (top,\n     * bottom, left, right) have been set. It should be true that elements with a top direction\n     * should have the highest index since these are elements like a table header. If any of those\n     * elements are also sticky in another direction, then they should appear above other elements\n     * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n     * (e.g. footer rows) should then be next in the ordering such that they are below the header\n     * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n     * should minimally increment so that they are above non-sticky elements but below top and bottom\n     * elements.\n     */\n    _getCalculatedZIndex(element) {\n        const zIndexIncrements = {\n            top: 100,\n            bottom: 10,\n            left: 1,\n            right: 1,\n        };\n        let zIndex = 0;\n        // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n        // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n        // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n        for (const dir of STICKY_DIRECTIONS) {\n            if (element.style[dir]) {\n                zIndex += zIndexIncrements[dir];\n            }\n        }\n        return zIndex ? `${zIndex}` : '';\n    }\n    /** Gets the widths for each cell in the provided row. */\n    _getCellWidths(row, recalculateCellWidths = true) {\n        if (!recalculateCellWidths && this._cachedCellWidths.length) {\n            return this._cachedCellWidths;\n        }\n        const cellWidths = [];\n        const firstRowCells = row.children;\n        for (let i = 0; i < firstRowCells.length; i++) {\n            const cell = firstRowCells[i];\n            cellWidths.push(this._retrieveElementSize(cell).width);\n        }\n        this._cachedCellWidths = cellWidths;\n        return cellWidths;\n    }\n    /**\n     * Determines the left and right positions of each sticky column cell, which will be the\n     * accumulation of all sticky column cell widths to the left and right, respectively.\n     * Non-sticky cells do not need to have a value set since their positions will not be applied.\n     */\n    _getStickyStartColumnPositions(widths, stickyStates) {\n        const positions = [];\n        let nextPosition = 0;\n        for (let i = 0; i < widths.length; i++) {\n            if (stickyStates[i]) {\n                positions[i] = nextPosition;\n                nextPosition += widths[i];\n            }\n        }\n        return positions;\n    }\n    /**\n     * Determines the left and right positions of each sticky column cell, which will be the\n     * accumulation of all sticky column cell widths to the left and right, respectively.\n     * Non-sticky cells do not need to have a value set since their positions will not be applied.\n     */\n    _getStickyEndColumnPositions(widths, stickyStates) {\n        const positions = [];\n        let nextPosition = 0;\n        for (let i = widths.length; i > 0; i--) {\n            if (stickyStates[i]) {\n                positions[i] = nextPosition;\n                nextPosition += widths[i];\n            }\n        }\n        return positions;\n    }\n    /**\n     * Retreives the most recently observed size of the specified element from the cache, or\n     * meaures it directly if not yet cached.\n     */\n    _retrieveElementSize(element) {\n        const cachedSize = this._elemSizeCache.get(element);\n        if (cachedSize) {\n            return cachedSize;\n        }\n        const clientRect = element.getBoundingClientRect();\n        const size = { width: clientRect.width, height: clientRect.height };\n        if (!this._resizeObserver) {\n            return size;\n        }\n        this._elemSizeCache.set(element, size);\n        this._resizeObserver.observe(element, { box: 'border-box' });\n        return size;\n    }\n    /**\n     * Conditionally enqueue the requested sticky update and clear previously queued updates\n     * for the same rows.\n     */\n    _updateStickyColumnReplayQueue(params) {\n        this._removeFromStickyColumnReplayQueue(params.rows);\n        // No need to replay if a flush is pending.\n        if (!this._stickyColumnsReplayTimeout) {\n            this._updatedStickyColumnsParamsToReplay.push(params);\n        }\n    }\n    /** Remove updates for the specified rows from the queue. */\n    _removeFromStickyColumnReplayQueue(rows) {\n        const rowsSet = new Set(rows);\n        for (const update of this._updatedStickyColumnsParamsToReplay) {\n            update.rows = update.rows.filter(row => !rowsSet.has(row));\n        }\n        this._updatedStickyColumnsParamsToReplay = this._updatedStickyColumnsParamsToReplay.filter(update => !!update.rows.length);\n    }\n    /** Update _elemSizeCache with the observed sizes. */\n    _updateCachedSizes(entries) {\n        let needsColumnUpdate = false;\n        for (const entry of entries) {\n            const newEntry = entry.borderBoxSize?.length\n                ? {\n                    width: entry.borderBoxSize[0].inlineSize,\n                    height: entry.borderBoxSize[0].blockSize,\n                }\n                : {\n                    width: entry.contentRect.width,\n                    height: entry.contentRect.height,\n                };\n            if (newEntry.width !== this._elemSizeCache.get(entry.target)?.width &&\n                isCell(entry.target)) {\n                needsColumnUpdate = true;\n            }\n            this._elemSizeCache.set(entry.target, newEntry);\n        }\n        if (needsColumnUpdate && this._updatedStickyColumnsParamsToReplay.length) {\n            if (this._stickyColumnsReplayTimeout) {\n                clearTimeout(this._stickyColumnsReplayTimeout);\n            }\n            this._stickyColumnsReplayTimeout = setTimeout(() => {\n                if (this._destroyed) {\n                    return;\n                }\n                for (const update of this._updatedStickyColumnsParamsToReplay) {\n                    this.updateStickyColumns(update.rows, update.stickyStartStates, update.stickyEndStates, true, false);\n                }\n                this._updatedStickyColumnsParamsToReplay = [];\n                this._stickyColumnsReplayTimeout = null;\n            }, 0);\n        }\n    }\n    /**\n     * Invoke afterNextRender with the table's injector, falling back to CoalescedStyleScheduler\n     * if the injector was not provided.\n     */\n    _afterNextRender(spec) {\n        if (this._tableInjector) {\n            afterNextRender(spec, { injector: this._tableInjector });\n        }\n        else {\n            this._coalescedStyleScheduler.schedule(() => {\n                spec.earlyRead?.();\n                spec.write();\n            });\n        }\n    }\n}\nfunction isCell(element) {\n    return ['cdk-cell', 'cdk-header-cell', 'cdk-footer-cell'].some(klass => element.classList.contains(klass));\n}\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nfunction getTableUnknownColumnError(id) {\n    return Error(`Could not find column with id \"${id}\".`);\n}\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nfunction getTableDuplicateColumnNameError(name) {\n    return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nfunction getTableMultipleDefaultRowDefsError() {\n    return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nfunction getTableMissingMatchingRowDefError(data) {\n    return Error(`Could not find a matching row definition for the` +\n        `provided row data: ${JSON.stringify(data)}`);\n}\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nfunction getTableMissingRowDefsError() {\n    return Error('Missing definitions for header, footer, and row; ' +\n        'cannot determine which columns should be rendered.');\n}\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nfunction getTableUnknownDataSourceError() {\n    return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nfunction getTableTextColumnMissingParentTableError() {\n    return Error(`Text column could not find a parent table for registration.`);\n}\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nfunction getTableTextColumnMissingNameError() {\n    return Error(`Table text column must have a name.`);\n}\n\n/** The injection token used to specify the StickyPositioningListener. */\nconst STICKY_POSITIONING_LISTENER = new InjectionToken('CDK_SPL');\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass CdkRecycleRows {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkRecycleRows, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkRecycleRows, isStandalone: true, selector: \"cdk-table[recycleRows], table[cdk-table][recycleRows]\", providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkRecycleRows, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-table[recycleRows], table[cdk-table][recycleRows]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }] });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\nclass DataRowOutlet {\n    viewContainer = inject(ViewContainerRef);\n    elementRef = inject(ElementRef);\n    constructor() {\n        const table = inject(CDK_TABLE);\n        table._rowOutlet = this;\n        table._outletAssigned();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: DataRowOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: DataRowOutlet, isStandalone: true, selector: \"[rowOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: DataRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[rowOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\nclass HeaderRowOutlet {\n    viewContainer = inject(ViewContainerRef);\n    elementRef = inject(ElementRef);\n    constructor() {\n        const table = inject(CDK_TABLE);\n        table._headerRowOutlet = this;\n        table._outletAssigned();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: HeaderRowOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: HeaderRowOutlet, isStandalone: true, selector: \"[headerRowOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: HeaderRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[headerRowOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\nclass FooterRowOutlet {\n    viewContainer = inject(ViewContainerRef);\n    elementRef = inject(ElementRef);\n    constructor() {\n        const table = inject(CDK_TABLE);\n        table._footerRowOutlet = this;\n        table._outletAssigned();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FooterRowOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: FooterRowOutlet, isStandalone: true, selector: \"[footerRowOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FooterRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[footerRowOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\nclass NoDataRowOutlet {\n    viewContainer = inject(ViewContainerRef);\n    elementRef = inject(ElementRef);\n    constructor() {\n        const table = inject(CDK_TABLE);\n        table._noDataRowOutlet = this;\n        table._outletAssigned();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: NoDataRowOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: NoDataRowOutlet, isStandalone: true, selector: \"[noDataRowOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: NoDataRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[noDataRowOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * The table template that can be used by the mat-table. Should not be used outside of the\n * material library.\n * @docs-private\n */\nconst CDK_TABLE_TEMPLATE = \n// Note that according to MDN, the `caption` element has to be projected as the **first**\n// element in the table. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/caption\n`\n  <ng-content select=\"caption\"/>\n  <ng-content select=\"colgroup, col\"/>\n\n  <!--\n    Unprojected content throws a hydration error so we need this to capture it.\n    It gets removed on the client so it doesn't affect the layout.\n  -->\n  @if (_isServer) {\n    <ng-content/>\n  }\n\n  @if (_isNativeHtmlTable) {\n    <thead role=\"rowgroup\">\n      <ng-container headerRowOutlet/>\n    </thead>\n    <tbody role=\"rowgroup\">\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n    </tbody>\n    <tfoot role=\"rowgroup\">\n      <ng-container footerRowOutlet/>\n    </tfoot>\n  } @else {\n    <ng-container headerRowOutlet/>\n    <ng-container rowOutlet/>\n    <ng-container noDataRowOutlet/>\n    <ng-container footerRowOutlet/>\n  }\n`;\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\nclass CdkTable {\n    _differs = inject(IterableDiffers);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _dir = inject(Directionality, { optional: true });\n    _platform = inject(Platform);\n    _viewRepeater = inject(_VIEW_REPEATER_STRATEGY);\n    _coalescedStyleScheduler = inject(_COALESCED_STYLE_SCHEDULER);\n    _viewportRuler = inject(ViewportRuler);\n    _stickyPositioningListener = inject(STICKY_POSITIONING_LISTENER, { optional: true, skipSelf: true });\n    _document = inject(DOCUMENT);\n    /** Latest data provided by the data source. */\n    _data;\n    /** Subject that emits when the component has been destroyed. */\n    _onDestroy = new Subject();\n    /** List of the rendered rows as identified by their `RenderRow` object. */\n    _renderRows;\n    /** Subscription that listens for the data provided by the data source. */\n    _renderChangeSubscription;\n    /**\n     * Map of all the user's defined columns (header, data, and footer cell template) identified by\n     * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n     * any custom column definitions added to `_customColumnDefs`.\n     */\n    _columnDefsByName = new Map();\n    /**\n     * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n     * using `ContentChildren` as well as any custom row definitions added to `_customRowDefs`.\n     */\n    _rowDefs;\n    /**\n     * Set of all header row definitions that can be used by this table. Populated by the rows\n     * gathered by using `ContentChildren` as well as any custom row definitions added to\n     * `_customHeaderRowDefs`.\n     */\n    _headerRowDefs;\n    /**\n     * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n     * using `ContentChildren` as well as any custom row definitions added to\n     * `_customFooterRowDefs`.\n     */\n    _footerRowDefs;\n    /** Differ used to find the changes in the data provided by the data source. */\n    _dataDiffer;\n    /** Stores the row definition that does not have a when predicate. */\n    _defaultRowDef;\n    /**\n     * Column definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * column definitions as *its* content child.\n     */\n    _customColumnDefs = new Set();\n    /**\n     * Data row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * built-in data rows as *its* content child.\n     */\n    _customRowDefs = new Set();\n    /**\n     * Header row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * built-in header rows as *its* content child.\n     */\n    _customHeaderRowDefs = new Set();\n    /**\n     * Footer row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n     * built-in footer row as *its* content child.\n     */\n    _customFooterRowDefs = new Set();\n    /** No data row that was defined outside of the direct content children of the table. */\n    _customNoDataRow;\n    /**\n     * Whether the header row definition has been changed. Triggers an update to the header row after\n     * content is checked. Initialized as true so that the table renders the initial set of rows.\n     */\n    _headerRowDefChanged = true;\n    /**\n     * Whether the footer row definition has been changed. Triggers an update to the footer row after\n     * content is checked. Initialized as true so that the table renders the initial set of rows.\n     */\n    _footerRowDefChanged = true;\n    /**\n     * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n     * change.\n     */\n    _stickyColumnStylesNeedReset = true;\n    /**\n     * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n     * `false`, cached values will be used instead. This is only applicable to tables with\n     * {@link fixedLayout} enabled. For other tables, cell widths will always be recalculated.\n     */\n    _forceRecalculateCellWidths = true;\n    /**\n     * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n     * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n     * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n     * and row template matches, which allows the `IterableDiffer` to check rows by reference\n     * and understand which rows are added/moved/removed.\n     *\n     * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n     * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n     * contains an array of created pairs. The array is necessary to handle cases where the data\n     * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n     * stored.\n     */\n    _cachedRenderRowsMap = new Map();\n    /** Whether the table is applied to a native `<table>`. */\n    _isNativeHtmlTable;\n    /**\n     * Utility class that is responsible for applying the appropriate sticky positioning styles to\n     * the table's rows and cells.\n     */\n    _stickyStyler;\n    /**\n     * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n     * table subclasses.\n     */\n    stickyCssClass = 'cdk-table-sticky';\n    /**\n     * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n     * the position is set in a selector associated with the value of stickyCssClass. May be\n     * overridden by table subclasses\n     */\n    needsPositionStickyOnElement = true;\n    /** Whether the component is being rendered on the server. */\n    _isServer;\n    /** Whether the no data row is currently showing anything. */\n    _isShowingNoDataRow = false;\n    /** Whether the table has rendered out all the outlets for the first time. */\n    _hasAllOutlets = false;\n    /** Whether the table is done initializing. */\n    _hasInitialized = false;\n    /** Aria role to apply to the table's cells based on the table's own role. */\n    _getCellRole() {\n        // Perform this lazily in case the table's role was updated by a directive after construction.\n        if (this._cellRoleInternal === undefined) {\n            // Note that we set `role=\"cell\"` even on native `td` elements,\n            // because some browsers seem to require it. See #29784.\n            const tableRole = this._elementRef.nativeElement.getAttribute('role');\n            return tableRole === 'grid' || tableRole === 'treegrid' ? 'gridcell' : 'cell';\n        }\n        return this._cellRoleInternal;\n    }\n    _cellRoleInternal = undefined;\n    /**\n     * Tracking function that will be used to check the differences in data changes. Used similarly\n     * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n     * relative to the function to know if a row should be added/removed/moved.\n     * Accepts a function that takes two parameters, `index` and `item`.\n     */\n    get trackBy() {\n        return this._trackByFn;\n    }\n    set trackBy(fn) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n            console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n        }\n        this._trackByFn = fn;\n    }\n    _trackByFn;\n    /**\n     * The table's source of data, which can be provided in three ways (in order of complexity):\n     *   - Simple data array (each object represents one table row)\n     *   - Stream that emits a data array each time the array changes\n     *   - `DataSource` object that implements the connect/disconnect interface.\n     *\n     * If a data array is provided, the table must be notified when the array's objects are\n     * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n     * render the diff since the last table render. If the data array reference is changed, the table\n     * will automatically trigger an update to the rows.\n     *\n     * When providing an Observable stream, the table will trigger an update automatically when the\n     * stream emits a new array of data.\n     *\n     * Finally, when providing a `DataSource` object, the table will use the Observable stream\n     * provided by the connect function and trigger updates when that stream emits new data array\n     * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n     * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n     * subscriptions registered during the connect process).\n     */\n    get dataSource() {\n        return this._dataSource;\n    }\n    set dataSource(dataSource) {\n        if (this._dataSource !== dataSource) {\n            this._switchDataSource(dataSource);\n        }\n    }\n    _dataSource;\n    /**\n     * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n     * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n     * dataobject will render the first row that evaluates its when predicate to true, in the order\n     * defined in the table, or otherwise the default row which does not have a when predicate.\n     */\n    get multiTemplateDataRows() {\n        return this._multiTemplateDataRows;\n    }\n    set multiTemplateDataRows(value) {\n        this._multiTemplateDataRows = value;\n        // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n        // this setter will be invoked before the row outlet has been defined hence the null check.\n        if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n            this._forceRenderDataRows();\n            this.updateStickyColumnStyles();\n        }\n    }\n    _multiTemplateDataRows = false;\n    /**\n     * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n     * and optimize rendering sticky styles for native tables. No-op for flex tables.\n     */\n    get fixedLayout() {\n        return this._fixedLayout;\n    }\n    set fixedLayout(value) {\n        this._fixedLayout = value;\n        // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n        this._forceRecalculateCellWidths = true;\n        this._stickyColumnStylesNeedReset = true;\n    }\n    _fixedLayout = false;\n    /**\n     * Emits when the table completes rendering a set of data rows based on the latest data from the\n     * data source, even if the set of rows is empty.\n     */\n    contentChanged = new EventEmitter();\n    // TODO(andrewseguin): Remove max value as the end index\n    //   and instead calculate the view on init and scroll.\n    /**\n     * Stream containing the latest information on what rows are being displayed on screen.\n     * Can be used by the data source to as a heuristic of what data should be provided.\n     *\n     * @docs-private\n     */\n    viewChange = new BehaviorSubject({\n        start: 0,\n        end: Number.MAX_VALUE,\n    });\n    // Outlets in the table's template where the header, data rows, and footer will be inserted.\n    _rowOutlet;\n    _headerRowOutlet;\n    _footerRowOutlet;\n    _noDataRowOutlet;\n    /**\n     * The column definitions provided by the user that contain what the header, data, and footer\n     * cells should render for each column.\n     */\n    _contentColumnDefs;\n    /** Set of data row definitions that were provided to the table as content children. */\n    _contentRowDefs;\n    /** Set of header row definitions that were provided to the table as content children. */\n    _contentHeaderRowDefs;\n    /** Set of footer row definitions that were provided to the table as content children. */\n    _contentFooterRowDefs;\n    /** Row definition that will only be rendered if there's no data in the table. */\n    _noDataRow;\n    _injector = inject(Injector);\n    constructor() {\n        const role = inject(new HostAttributeToken('role'), { optional: true });\n        if (!role) {\n            this._elementRef.nativeElement.setAttribute('role', 'table');\n        }\n        this._isServer = !this._platform.isBrowser;\n        this._isNativeHtmlTable = this._elementRef.nativeElement.nodeName === 'TABLE';\n        // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n        // the user has provided a custom trackBy, return the result of that function as evaluated\n        // with the values of the `RenderRow`'s data and index.\n        this._dataDiffer = this._differs.find([]).create((_i, dataRow) => {\n            return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n        });\n    }\n    ngOnInit() {\n        this._setupStickyStyler();\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(() => {\n            this._forceRecalculateCellWidths = true;\n        });\n    }\n    ngAfterContentInit() {\n        this._hasInitialized = true;\n    }\n    ngAfterContentChecked() {\n        // Only start re-rendering in `ngAfterContentChecked` after the first render.\n        if (this._canRender()) {\n            this._render();\n        }\n    }\n    ngOnDestroy() {\n        this._stickyStyler?.destroy();\n        [\n            this._rowOutlet?.viewContainer,\n            this._headerRowOutlet?.viewContainer,\n            this._footerRowOutlet?.viewContainer,\n            this._cachedRenderRowsMap,\n            this._customColumnDefs,\n            this._customRowDefs,\n            this._customHeaderRowDefs,\n            this._customFooterRowDefs,\n            this._columnDefsByName,\n        ].forEach((def) => {\n            def?.clear();\n        });\n        this._headerRowDefs = [];\n        this._footerRowDefs = [];\n        this._defaultRowDef = null;\n        this._onDestroy.next();\n        this._onDestroy.complete();\n        if (isDataSource(this.dataSource)) {\n            this.dataSource.disconnect(this);\n        }\n    }\n    /**\n     * Renders rows based on the table's latest set of data, which was either provided directly as an\n     * input or retrieved through an Observable stream (directly or from a DataSource).\n     * Checks for differences in the data since the last diff to perform only the necessary\n     * changes (add/remove/move rows).\n     *\n     * If the table's data source is a DataSource or Observable, this will be invoked automatically\n     * each time the provided Observable stream emits a new data array. Otherwise if your data is\n     * an array, this function will need to be called to render any changes.\n     */\n    renderRows() {\n        this._renderRows = this._getAllRenderRows();\n        const changes = this._dataDiffer.diff(this._renderRows);\n        if (!changes) {\n            this._updateNoDataRow();\n            this.contentChanged.next();\n            return;\n        }\n        const viewContainer = this._rowOutlet.viewContainer;\n        this._viewRepeater.applyChanges(changes, viewContainer, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record.item, currentIndex), record => record.item.data, (change) => {\n            if (change.operation === _ViewRepeaterOperation.INSERTED && change.context) {\n                this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n            }\n        });\n        // Update the meta context of a row's context data (index, count, first, last, ...)\n        this._updateRowIndexContext();\n        // Update rows that did not get added/removed/moved but may have had their identity changed,\n        // e.g. if trackBy matched data on some property but the actual data reference changed.\n        changes.forEachIdentityChange((record) => {\n            const rowView = viewContainer.get(record.currentIndex);\n            rowView.context.$implicit = record.item.data;\n        });\n        this._updateNoDataRow();\n        this.contentChanged.next();\n        this.updateStickyColumnStyles();\n    }\n    /** Adds a column definition that was not included as part of the content children. */\n    addColumnDef(columnDef) {\n        this._customColumnDefs.add(columnDef);\n    }\n    /** Removes a column definition that was not included as part of the content children. */\n    removeColumnDef(columnDef) {\n        this._customColumnDefs.delete(columnDef);\n    }\n    /** Adds a row definition that was not included as part of the content children. */\n    addRowDef(rowDef) {\n        this._customRowDefs.add(rowDef);\n    }\n    /** Removes a row definition that was not included as part of the content children. */\n    removeRowDef(rowDef) {\n        this._customRowDefs.delete(rowDef);\n    }\n    /** Adds a header row definition that was not included as part of the content children. */\n    addHeaderRowDef(headerRowDef) {\n        this._customHeaderRowDefs.add(headerRowDef);\n        this._headerRowDefChanged = true;\n    }\n    /** Removes a header row definition that was not included as part of the content children. */\n    removeHeaderRowDef(headerRowDef) {\n        this._customHeaderRowDefs.delete(headerRowDef);\n        this._headerRowDefChanged = true;\n    }\n    /** Adds a footer row definition that was not included as part of the content children. */\n    addFooterRowDef(footerRowDef) {\n        this._customFooterRowDefs.add(footerRowDef);\n        this._footerRowDefChanged = true;\n    }\n    /** Removes a footer row definition that was not included as part of the content children. */\n    removeFooterRowDef(footerRowDef) {\n        this._customFooterRowDefs.delete(footerRowDef);\n        this._footerRowDefChanged = true;\n    }\n    /** Sets a no data row definition that was not included as a part of the content children. */\n    setNoDataRow(noDataRow) {\n        this._customNoDataRow = noDataRow;\n    }\n    /**\n     * Updates the header sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n     * automatically called when the header row changes its displayed set of columns, or if its\n     * sticky input changes. May be called manually for cases where the cell content changes outside\n     * of these events.\n     */\n    updateStickyHeaderRowStyles() {\n        const headerRows = this._getRenderedRows(this._headerRowOutlet);\n        // Hide the thead element if there are no header rows. This is necessary to satisfy\n        // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n        // required child `row`.\n        if (this._isNativeHtmlTable) {\n            const thead = closestTableSection(this._headerRowOutlet, 'thead');\n            if (thead) {\n                thead.style.display = headerRows.length ? '' : 'none';\n            }\n        }\n        const stickyStates = this._headerRowDefs.map(def => def.sticky);\n        this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n        this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n        // Reset the dirty state of the sticky input change since it has been used.\n        this._headerRowDefs.forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n     * automatically called when the footer row changes its displayed set of columns, or if its\n     * sticky input changes. May be called manually for cases where the cell content changes outside\n     * of these events.\n     */\n    updateStickyFooterRowStyles() {\n        const footerRows = this._getRenderedRows(this._footerRowOutlet);\n        // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n        // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n        // required child `row`.\n        if (this._isNativeHtmlTable) {\n            const tfoot = closestTableSection(this._footerRowOutlet, 'tfoot');\n            if (tfoot) {\n                tfoot.style.display = footerRows.length ? '' : 'none';\n            }\n        }\n        const stickyStates = this._footerRowDefs.map(def => def.sticky);\n        this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n        this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n        this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n        // Reset the dirty state of the sticky input change since it has been used.\n        this._footerRowDefs.forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Updates the column sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the left and right. Then sticky styles are added for the left and right according\n     * to the column definitions for each cell in each row. This is automatically called when\n     * the data source provides a new set of data or when a column definition changes its sticky\n     * input. May be called manually for cases where the cell content changes outside of these events.\n     */\n    updateStickyColumnStyles() {\n        const headerRows = this._getRenderedRows(this._headerRowOutlet);\n        const dataRows = this._getRenderedRows(this._rowOutlet);\n        const footerRows = this._getRenderedRows(this._footerRowOutlet);\n        // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n        // In a table using a fixed layout, row content won't affect column width, so sticky styles\n        // don't need to be cleared unless either the sticky column config changes or one of the row\n        // defs change.\n        if ((this._isNativeHtmlTable && !this._fixedLayout) || this._stickyColumnStylesNeedReset) {\n            // Clear the left and right positioning from all columns in the table across all rows since\n            // sticky columns span across all table sections (header, data, footer)\n            this._stickyStyler.clearStickyPositioning([...headerRows, ...dataRows, ...footerRows], ['left', 'right']);\n            this._stickyColumnStylesNeedReset = false;\n        }\n        // Update the sticky styles for each header row depending on the def's sticky state\n        headerRows.forEach((headerRow, i) => {\n            this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n        });\n        // Update the sticky styles for each data row depending on its def's sticky state\n        this._rowDefs.forEach(rowDef => {\n            // Collect all the rows rendered with this row definition.\n            const rows = [];\n            for (let i = 0; i < dataRows.length; i++) {\n                if (this._renderRows[i].rowDef === rowDef) {\n                    rows.push(dataRows[i]);\n                }\n            }\n            this._addStickyColumnStyles(rows, rowDef);\n        });\n        // Update the sticky styles for each footer row depending on the def's sticky state\n        footerRows.forEach((footerRow, i) => {\n            this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n        });\n        // Reset the dirty state of the sticky input change since it has been used.\n        Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n    }\n    /** Invoked whenever an outlet is created and has been assigned to the table. */\n    _outletAssigned() {\n        // Trigger the first render once all outlets have been assigned. We do it this way, as\n        // opposed to waiting for the next `ngAfterContentChecked`, because we don't know when\n        // the next change detection will happen.\n        // Also we can't use queries to resolve the outlets, because they're wrapped in a\n        // conditional, so we have to rely on them being assigned via DI.\n        if (!this._hasAllOutlets &&\n            this._rowOutlet &&\n            this._headerRowOutlet &&\n            this._footerRowOutlet &&\n            this._noDataRowOutlet) {\n            this._hasAllOutlets = true;\n            // In some setups this may fire before `ngAfterContentInit`\n            // so we need a check here. See #28538.\n            if (this._canRender()) {\n                this._render();\n            }\n        }\n    }\n    /** Whether the table has all the information to start rendering. */\n    _canRender() {\n        return this._hasAllOutlets && this._hasInitialized;\n    }\n    /** Renders the table if its state has changed. */\n    _render() {\n        // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n        this._cacheRowDefs();\n        this._cacheColumnDefs();\n        // Make sure that the user has at least added header, footer, or data row def.\n        if (!this._headerRowDefs.length &&\n            !this._footerRowDefs.length &&\n            !this._rowDefs.length &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMissingRowDefsError();\n        }\n        // Render updates if the list of columns have been changed for the header, row, or footer defs.\n        const columnsChanged = this._renderUpdatedColumns();\n        const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n        // Ensure sticky column styles are reset if set to `true` elsewhere.\n        this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n        this._forceRecalculateCellWidths = rowDefsChanged;\n        // If the header row definition has been changed, trigger a render to the header row.\n        if (this._headerRowDefChanged) {\n            this._forceRenderHeaderRows();\n            this._headerRowDefChanged = false;\n        }\n        // If the footer row definition has been changed, trigger a render to the footer row.\n        if (this._footerRowDefChanged) {\n            this._forceRenderFooterRows();\n            this._footerRowDefChanged = false;\n        }\n        // If there is a data source and row definitions, connect to the data source unless a\n        // connection has already been made.\n        if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n            this._observeRenderChanges();\n        }\n        else if (this._stickyColumnStylesNeedReset) {\n            // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n            // called when it row data arrives. Otherwise, we need to call it proactively.\n            this.updateStickyColumnStyles();\n        }\n        this._checkStickyStates();\n    }\n    /**\n     * Get the list of RenderRow objects to render according to the current list of data and defined\n     * row definitions. If the previous list already contained a particular pair, it should be reused\n     * so that the differ equates their references.\n     */\n    _getAllRenderRows() {\n        const renderRows = [];\n        // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n        // new cache while unused ones can be picked up by garbage collection.\n        const prevCachedRenderRows = this._cachedRenderRowsMap;\n        this._cachedRenderRowsMap = new Map();\n        if (!this._data) {\n            return renderRows;\n        }\n        // For each data object, get the list of rows that should be rendered, represented by the\n        // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n        for (let i = 0; i < this._data.length; i++) {\n            let data = this._data[i];\n            const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n            if (!this._cachedRenderRowsMap.has(data)) {\n                this._cachedRenderRowsMap.set(data, new WeakMap());\n            }\n            for (let j = 0; j < renderRowsForData.length; j++) {\n                let renderRow = renderRowsForData[j];\n                const cache = this._cachedRenderRowsMap.get(renderRow.data);\n                if (cache.has(renderRow.rowDef)) {\n                    cache.get(renderRow.rowDef).push(renderRow);\n                }\n                else {\n                    cache.set(renderRow.rowDef, [renderRow]);\n                }\n                renderRows.push(renderRow);\n            }\n        }\n        return renderRows;\n    }\n    /**\n     * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n     * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n     * `(T, CdkRowDef)` pair.\n     */\n    _getRenderRowsForData(data, dataIndex, cache) {\n        const rowDefs = this._getRowDefs(data, dataIndex);\n        return rowDefs.map(rowDef => {\n            const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef) : [];\n            if (cachedRenderRows.length) {\n                const dataRow = cachedRenderRows.shift();\n                dataRow.dataIndex = dataIndex;\n                return dataRow;\n            }\n            else {\n                return { data, rowDef, dataIndex };\n            }\n        });\n    }\n    /** Update the map containing the content's column definitions. */\n    _cacheColumnDefs() {\n        this._columnDefsByName.clear();\n        const columnDefs = mergeArrayAndSet(this._getOwnDefs(this._contentColumnDefs), this._customColumnDefs);\n        columnDefs.forEach(columnDef => {\n            if (this._columnDefsByName.has(columnDef.name) &&\n                (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableDuplicateColumnNameError(columnDef.name);\n            }\n            this._columnDefsByName.set(columnDef.name, columnDef);\n        });\n    }\n    /** Update the list of all available row definitions that can be used. */\n    _cacheRowDefs() {\n        this._headerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentHeaderRowDefs), this._customHeaderRowDefs);\n        this._footerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentFooterRowDefs), this._customFooterRowDefs);\n        this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n        // After all row definitions are determined, find the row definition to be considered default.\n        const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n        if (!this.multiTemplateDataRows &&\n            defaultRowDefs.length > 1 &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMultipleDefaultRowDefsError();\n        }\n        this._defaultRowDef = defaultRowDefs[0];\n    }\n    /**\n     * Check if the header, data, or footer rows have changed what columns they want to display or\n     * whether the sticky states have changed for the header or footer. If there is a diff, then\n     * re-render that section.\n     */\n    _renderUpdatedColumns() {\n        const columnsDiffReducer = (acc, def) => {\n            // The differ should be run for every column, even if `acc` is already\n            // true (see #29922)\n            const diff = !!def.getColumnsDiff();\n            return acc || diff;\n        };\n        // Force re-render data rows if the list of column definitions have changed.\n        const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n        if (dataColumnsChanged) {\n            this._forceRenderDataRows();\n        }\n        // Force re-render header/footer rows if the list of column definitions have changed.\n        const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n        if (headerColumnsChanged) {\n            this._forceRenderHeaderRows();\n        }\n        const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n        if (footerColumnsChanged) {\n            this._forceRenderFooterRows();\n        }\n        return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n    }\n    /**\n     * Switch to the provided data source by resetting the data and unsubscribing from the current\n     * render change subscription if one exists. If the data source is null, interpret this by\n     * clearing the row outlet. Otherwise start listening for new data.\n     */\n    _switchDataSource(dataSource) {\n        this._data = [];\n        if (isDataSource(this.dataSource)) {\n            this.dataSource.disconnect(this);\n        }\n        // Stop listening for data from the previous data source.\n        if (this._renderChangeSubscription) {\n            this._renderChangeSubscription.unsubscribe();\n            this._renderChangeSubscription = null;\n        }\n        if (!dataSource) {\n            if (this._dataDiffer) {\n                this._dataDiffer.diff([]);\n            }\n            if (this._rowOutlet) {\n                this._rowOutlet.viewContainer.clear();\n            }\n        }\n        this._dataSource = dataSource;\n    }\n    /** Set up a subscription for the data provided by the data source. */\n    _observeRenderChanges() {\n        // If no data source has been set, there is nothing to observe for changes.\n        if (!this.dataSource) {\n            return;\n        }\n        let dataStream;\n        if (isDataSource(this.dataSource)) {\n            dataStream = this.dataSource.connect(this);\n        }\n        else if (isObservable(this.dataSource)) {\n            dataStream = this.dataSource;\n        }\n        else if (Array.isArray(this.dataSource)) {\n            dataStream = of(this.dataSource);\n        }\n        if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableUnknownDataSourceError();\n        }\n        this._renderChangeSubscription = dataStream\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(data => {\n            this._data = data || [];\n            this.renderRows();\n        });\n    }\n    /**\n     * Clears any existing content in the header row outlet and creates a new embedded view\n     * in the outlet using the header row definition.\n     */\n    _forceRenderHeaderRows() {\n        // Clear the header row outlet if any content exists.\n        if (this._headerRowOutlet.viewContainer.length > 0) {\n            this._headerRowOutlet.viewContainer.clear();\n        }\n        this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n        this.updateStickyHeaderRowStyles();\n    }\n    /**\n     * Clears any existing content in the footer row outlet and creates a new embedded view\n     * in the outlet using the footer row definition.\n     */\n    _forceRenderFooterRows() {\n        // Clear the footer row outlet if any content exists.\n        if (this._footerRowOutlet.viewContainer.length > 0) {\n            this._footerRowOutlet.viewContainer.clear();\n        }\n        this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n        this.updateStickyFooterRowStyles();\n    }\n    /** Adds the sticky column styles for the rows according to the columns' stick states. */\n    _addStickyColumnStyles(rows, rowDef) {\n        const columnDefs = Array.from(rowDef?.columns || []).map(columnName => {\n            const columnDef = this._columnDefsByName.get(columnName);\n            if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableUnknownColumnError(columnName);\n            }\n            return columnDef;\n        });\n        const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n        const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n        this._stickyStyler.updateStickyColumns(rows, stickyStartStates, stickyEndStates, !this._fixedLayout || this._forceRecalculateCellWidths);\n    }\n    /** Gets the list of rows that have been rendered in the row outlet. */\n    _getRenderedRows(rowOutlet) {\n        const renderedRows = [];\n        for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n            const viewRef = rowOutlet.viewContainer.get(i);\n            renderedRows.push(viewRef.rootNodes[0]);\n        }\n        return renderedRows;\n    }\n    /**\n     * Get the matching row definitions that should be used for this row data. If there is only\n     * one row definition, it is returned. Otherwise, find the row definitions that has a when\n     * predicate that returns true with the data. If none return true, return the default row\n     * definition.\n     */\n    _getRowDefs(data, dataIndex) {\n        if (this._rowDefs.length == 1) {\n            return [this._rowDefs[0]];\n        }\n        let rowDefs = [];\n        if (this.multiTemplateDataRows) {\n            rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n        }\n        else {\n            let rowDef = this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n            if (rowDef) {\n                rowDefs.push(rowDef);\n            }\n        }\n        if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMissingMatchingRowDefError(data);\n        }\n        return rowDefs;\n    }\n    _getEmbeddedViewArgs(renderRow, index) {\n        const rowDef = renderRow.rowDef;\n        const context = { $implicit: renderRow.data };\n        return {\n            templateRef: rowDef.template,\n            context,\n            index,\n        };\n    }\n    /**\n     * Creates a new row template in the outlet and fills it with the set of cell templates.\n     * Optionally takes a context to provide to the row and cells, as well as an optional index\n     * of where to place the new row template in the outlet.\n     */\n    _renderRow(outlet, rowDef, index, context = {}) {\n        // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n        const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n        this._renderCellTemplateForItem(rowDef, context);\n        return view;\n    }\n    _renderCellTemplateForItem(rowDef, context) {\n        for (let cellTemplate of this._getCellTemplates(rowDef)) {\n            if (CdkCellOutlet.mostRecentCellOutlet) {\n                CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Updates the index-related context for each row to reflect any changes in the index of the rows,\n     * e.g. first/last/even/odd.\n     */\n    _updateRowIndexContext() {\n        const viewContainer = this._rowOutlet.viewContainer;\n        for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n            const viewRef = viewContainer.get(renderIndex);\n            const context = viewRef.context;\n            context.count = count;\n            context.first = renderIndex === 0;\n            context.last = renderIndex === count - 1;\n            context.even = renderIndex % 2 === 0;\n            context.odd = !context.even;\n            if (this.multiTemplateDataRows) {\n                context.dataIndex = this._renderRows[renderIndex].dataIndex;\n                context.renderIndex = renderIndex;\n            }\n            else {\n                context.index = this._renderRows[renderIndex].dataIndex;\n            }\n        }\n    }\n    /** Gets the column definitions for the provided row def. */\n    _getCellTemplates(rowDef) {\n        if (!rowDef || !rowDef.columns) {\n            return [];\n        }\n        return Array.from(rowDef.columns, columnId => {\n            const column = this._columnDefsByName.get(columnId);\n            if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableUnknownColumnError(columnId);\n            }\n            return rowDef.extractCellTemplate(column);\n        });\n    }\n    /**\n     * Forces a re-render of the data rows. Should be called in cases where there has been an input\n     * change that affects the evaluation of which rows should be rendered, e.g. toggling\n     * `multiTemplateDataRows` or adding/removing row definitions.\n     */\n    _forceRenderDataRows() {\n        this._dataDiffer.diff([]);\n        this._rowOutlet.viewContainer.clear();\n        this.renderRows();\n    }\n    /**\n     * Checks if there has been a change in sticky states since last check and applies the correct\n     * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n     * during a change detection and after the inputs are settled (after content check).\n     */\n    _checkStickyStates() {\n        const stickyCheckReducer = (acc, d) => {\n            return acc || d.hasStickyChanged();\n        };\n        // Note that the check needs to occur for every definition since it notifies the definition\n        // that it can reset its dirty state. Using another operator like `some` may short-circuit\n        // remaining definitions and leave them in an unchecked state.\n        if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n            this.updateStickyHeaderRowStyles();\n        }\n        if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n            this.updateStickyFooterRowStyles();\n        }\n        if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n            this._stickyColumnStylesNeedReset = true;\n            this.updateStickyColumnStyles();\n        }\n    }\n    /**\n     * Creates the sticky styler that will be used for sticky rows and columns. Listens\n     * for directionality changes and provides the latest direction to the styler. Re-applies column\n     * stickiness when directionality changes.\n     */\n    _setupStickyStyler() {\n        const direction = this._dir ? this._dir.value : 'ltr';\n        this._stickyStyler = new StickyStyler(this._isNativeHtmlTable, this.stickyCssClass, direction, this._coalescedStyleScheduler, this._platform.isBrowser, this.needsPositionStickyOnElement, this._stickyPositioningListener, this._injector);\n        (this._dir ? this._dir.change : of())\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(value => {\n            this._stickyStyler.direction = value;\n            this.updateStickyColumnStyles();\n        });\n    }\n    /** Filters definitions that belong to this table from a QueryList. */\n    _getOwnDefs(items) {\n        return items.filter(item => !item._table || item._table === this);\n    }\n    /** Creates or removes the no data row, depending on whether any data is being shown. */\n    _updateNoDataRow() {\n        const noDataRow = this._customNoDataRow || this._noDataRow;\n        if (!noDataRow) {\n            return;\n        }\n        const shouldShow = this._rowOutlet.viewContainer.length === 0;\n        if (shouldShow === this._isShowingNoDataRow) {\n            return;\n        }\n        const container = this._noDataRowOutlet.viewContainer;\n        if (shouldShow) {\n            const view = container.createEmbeddedView(noDataRow.templateRef);\n            const rootNode = view.rootNodes[0];\n            // Only add the attributes if we have a single root node since it's hard\n            // to figure out which one to add it to when there are multiple.\n            if (view.rootNodes.length === 1 && rootNode?.nodeType === this._document.ELEMENT_NODE) {\n                rootNode.setAttribute('role', 'row');\n                rootNode.classList.add(noDataRow._contentClassName);\n            }\n        }\n        else {\n            container.clear();\n        }\n        this._isShowingNoDataRow = shouldShow;\n        this._changeDetectorRef.markForCheck();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTable, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: CdkTable, isStandalone: true, selector: \"cdk-table, table[cdk-table]\", inputs: { trackBy: \"trackBy\", dataSource: \"dataSource\", multiTemplateDataRows: [\"multiTemplateDataRows\", \"multiTemplateDataRows\", booleanAttribute], fixedLayout: [\"fixedLayout\", \"fixedLayout\", booleanAttribute] }, outputs: { contentChanged: \"contentChanged\" }, host: { properties: { \"class.cdk-table-fixed-layout\": \"fixedLayout\" }, classAttribute: \"cdk-table\" }, providers: [\n            { provide: CDK_TABLE, useExisting: CdkTable },\n            { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n            { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n            // Prevent nested tables from seeing this table's StickyPositioningListener.\n            { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n        ], queries: [{ propertyName: \"_noDataRow\", first: true, predicate: CdkNoDataRow, descendants: true }, { propertyName: \"_contentColumnDefs\", predicate: CdkColumnDef, descendants: true }, { propertyName: \"_contentRowDefs\", predicate: CdkRowDef, descendants: true }, { propertyName: \"_contentHeaderRowDefs\", predicate: CdkHeaderRowDef, descendants: true }, { propertyName: \"_contentFooterRowDefs\", predicate: CdkFooterRowDef, descendants: true }], exportAs: [\"cdkTable\"], ngImport: i0, template: \"\\n  <ng-content select=\\\"caption\\\"/>\\n  <ng-content select=\\\"colgroup, col\\\"/>\\n\\n  <!--\\n    Unprojected content throws a hydration error so we need this to capture it.\\n    It gets removed on the client so it doesn't affect the layout.\\n  -->\\n  @if (_isServer) {\\n    <ng-content/>\\n  }\\n\\n  @if (_isNativeHtmlTable) {\\n    <thead role=\\\"rowgroup\\\">\\n      <ng-container headerRowOutlet/>\\n    </thead>\\n    <tbody role=\\\"rowgroup\\\">\\n      <ng-container rowOutlet/>\\n      <ng-container noDataRowOutlet/>\\n    </tbody>\\n    <tfoot role=\\\"rowgroup\\\">\\n      <ng-container footerRowOutlet/>\\n    </tfoot>\\n  } @else {\\n    <ng-container headerRowOutlet/>\\n    <ng-container rowOutlet/>\\n    <ng-container noDataRowOutlet/>\\n    <ng-container footerRowOutlet/>\\n  }\\n\", isInline: true, styles: [\".cdk-table-fixed-layout{table-layout:fixed}\\n\"], dependencies: [{ kind: \"directive\", type: HeaderRowOutlet, selector: \"[headerRowOutlet]\" }, { kind: \"directive\", type: DataRowOutlet, selector: \"[rowOutlet]\" }, { kind: \"directive\", type: NoDataRowOutlet, selector: \"[noDataRowOutlet]\" }, { kind: \"directive\", type: FooterRowOutlet, selector: \"[footerRowOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTable, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-table, table[cdk-table]', exportAs: 'cdkTable', template: CDK_TABLE_TEMPLATE, host: {\n                        'class': 'cdk-table',\n                        '[class.cdk-table-fixed-layout]': 'fixedLayout',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, providers: [\n                        { provide: CDK_TABLE, useExisting: CdkTable },\n                        { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n                        { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n                        // Prevent nested tables from seeing this table's StickyPositioningListener.\n                        { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n                    ], imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet], styles: [\".cdk-table-fixed-layout{table-layout:fixed}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { trackBy: [{\n                type: Input\n            }], dataSource: [{\n                type: Input\n            }], multiTemplateDataRows: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], fixedLayout: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], contentChanged: [{\n                type: Output\n            }], _contentColumnDefs: [{\n                type: ContentChildren,\n                args: [CdkColumnDef, { descendants: true }]\n            }], _contentRowDefs: [{\n                type: ContentChildren,\n                args: [CdkRowDef, { descendants: true }]\n            }], _contentHeaderRowDefs: [{\n                type: ContentChildren,\n                args: [CdkHeaderRowDef, {\n                        descendants: true,\n                    }]\n            }], _contentFooterRowDefs: [{\n                type: ContentChildren,\n                args: [CdkFooterRowDef, {\n                        descendants: true,\n                    }]\n            }], _noDataRow: [{\n                type: ContentChild,\n                args: [CdkNoDataRow]\n            }] } });\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet(array, set) {\n    return array.concat(Array.from(set));\n}\n/**\n * Finds the closest table section to an outlet. We can't use `HTMLElement.closest` for this,\n * because the node representing the outlet is a comment.\n */\nfunction closestTableSection(outlet, section) {\n    const uppercaseSection = section.toUpperCase();\n    let current = outlet.viewContainer.element.nativeElement;\n    while (current) {\n        // 1 is an element node.\n        const nodeName = current.nodeType === 1 ? current.nodeName : null;\n        if (nodeName === uppercaseSection) {\n            return current;\n        }\n        else if (nodeName === 'TABLE') {\n            // Stop traversing past the `table` node.\n            break;\n        }\n        current = current.parentNode;\n    }\n    return null;\n}\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass CdkTextColumn {\n    _table = inject(CdkTable, { optional: true });\n    _options = inject(TEXT_COLUMN_OPTIONS, { optional: true });\n    /** Column name that should be used to reference this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._name = name;\n        // With Ivy, inputs can be initialized before static query results are\n        // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n        this._syncColumnDefName();\n    }\n    _name;\n    /**\n     * Text label that should be used for the column header. If this property is not\n     * set, the header text will default to the column name with its first letter capitalized.\n     */\n    headerText;\n    /**\n     * Accessor function to retrieve the data rendered for each cell. If this\n     * property is not set, the data cells will render the value found in the data's property matching\n     * the column's name. For example, if the column is named `id`, then the rendered value will be\n     * value defined by the data's `id` property.\n     */\n    dataAccessor;\n    /** Alignment of the cell values. */\n    justify = 'start';\n    /** @docs-private */\n    columnDef;\n    /**\n     * The column cell is provided to the column during `ngOnInit` with a static query.\n     * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n     * column definition was provided in the same view as the table, which is not the case with this\n     * component.\n     * @docs-private\n     */\n    cell;\n    /**\n     * The column headerCell is provided to the column during `ngOnInit` with a static query.\n     * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n     * column definition was provided in the same view as the table, which is not the case with this\n     * component.\n     * @docs-private\n     */\n    headerCell;\n    constructor() {\n        this._options = this._options || {};\n    }\n    ngOnInit() {\n        this._syncColumnDefName();\n        if (this.headerText === undefined) {\n            this.headerText = this._createDefaultHeaderText();\n        }\n        if (!this.dataAccessor) {\n            this.dataAccessor =\n                this._options.defaultDataAccessor || ((data, name) => data[name]);\n        }\n        if (this._table) {\n            // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n            // since the columnDef will not pick up its content by the time the table finishes checking\n            // its content and initializing the rows.\n            this.columnDef.cell = this.cell;\n            this.columnDef.headerCell = this.headerCell;\n            this._table.addColumnDef(this.columnDef);\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getTableTextColumnMissingParentTableError();\n        }\n    }\n    ngOnDestroy() {\n        if (this._table) {\n            this._table.removeColumnDef(this.columnDef);\n        }\n    }\n    /**\n     * Creates a default header text. Use the options' header text transformation function if one\n     * has been provided. Otherwise simply capitalize the column name.\n     */\n    _createDefaultHeaderText() {\n        const name = this.name;\n        if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableTextColumnMissingNameError();\n        }\n        if (this._options && this._options.defaultHeaderTextTransform) {\n            return this._options.defaultHeaderTextTransform(name);\n        }\n        return name[0].toUpperCase() + name.slice(1);\n    }\n    /** Synchronizes the column definition name with the text column name. */\n    _syncColumnDefName() {\n        if (this.columnDef) {\n            this.columnDef.name = this.name;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTextColumn, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkTextColumn, isStandalone: true, selector: \"cdk-text-column\", inputs: { name: \"name\", headerText: \"headerText\", dataAccessor: \"dataAccessor\", justify: \"justify\" }, viewQueries: [{ propertyName: \"columnDef\", first: true, predicate: CdkColumnDef, descendants: true, static: true }, { propertyName: \"cell\", first: true, predicate: CdkCellDef, descendants: true, static: true }, { propertyName: \"headerCell\", first: true, predicate: CdkHeaderCellDef, descendants: true, static: true }], ngImport: i0, template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: CdkColumnDef, selector: \"[cdkColumnDef]\", inputs: [\"cdkColumnDef\", \"sticky\", \"stickyEnd\"] }, { kind: \"directive\", type: CdkHeaderCellDef, selector: \"[cdkHeaderCellDef]\" }, { kind: \"directive\", type: CdkHeaderCell, selector: \"cdk-header-cell, th[cdk-header-cell]\" }, { kind: \"directive\", type: CdkCellDef, selector: \"[cdkCellDef]\" }, { kind: \"directive\", type: CdkCell, selector: \"cdk-cell, td[cdk-cell]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTextColumn, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-text-column',\n                    template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n                    encapsulation: ViewEncapsulation.None,\n                    // Change detection is intentionally not set to OnPush. This component's template will be provided\n                    // to the table to be inserted into its view. This is problematic when change detection runs since\n                    // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n                    // mean's the template in the table's view will not have the updated value (and in fact will cause\n                    // an ExpressionChangedAfterItHasBeenCheckedError).\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    imports: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell],\n                }]\n        }], ctorParameters: () => [], propDecorators: { name: [{\n                type: Input\n            }], headerText: [{\n                type: Input\n            }], dataAccessor: [{\n                type: Input\n            }], justify: [{\n                type: Input\n            }], columnDef: [{\n                type: ViewChild,\n                args: [CdkColumnDef, { static: true }]\n            }], cell: [{\n                type: ViewChild,\n                args: [CdkCellDef, { static: true }]\n            }], headerCell: [{\n                type: ViewChild,\n                args: [CdkHeaderCellDef, { static: true }]\n            }] } });\n\nconst EXPORTED_DECLARATIONS = [\n    CdkTable,\n    CdkRowDef,\n    CdkCellDef,\n    CdkCellOutlet,\n    CdkHeaderCellDef,\n    CdkFooterCellDef,\n    CdkColumnDef,\n    CdkCell,\n    CdkRow,\n    CdkHeaderCell,\n    CdkFooterCell,\n    CdkHeaderRow,\n    CdkHeaderRowDef,\n    CdkFooterRow,\n    CdkFooterRowDef,\n    DataRowOutlet,\n    HeaderRowOutlet,\n    FooterRowOutlet,\n    CdkTextColumn,\n    CdkNoDataRow,\n    CdkRecycleRows,\n    NoDataRowOutlet,\n];\nclass CdkTableModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTableModule, imports: [ScrollingModule, CdkTable,\n            CdkRowDef,\n            CdkCellDef,\n            CdkCellOutlet,\n            CdkHeaderCellDef,\n            CdkFooterCellDef,\n            CdkColumnDef,\n            CdkCell,\n            CdkRow,\n            CdkHeaderCell,\n            CdkFooterCell,\n            CdkHeaderRow,\n            CdkHeaderRowDef,\n            CdkFooterRow,\n            CdkFooterRowDef,\n            DataRowOutlet,\n            HeaderRowOutlet,\n            FooterRowOutlet,\n            CdkTextColumn,\n            CdkNoDataRow,\n            CdkRecycleRows,\n            NoDataRowOutlet], exports: [CdkTable,\n            CdkRowDef,\n            CdkCellDef,\n            CdkCellOutlet,\n            CdkHeaderCellDef,\n            CdkFooterCellDef,\n            CdkColumnDef,\n            CdkCell,\n            CdkRow,\n            CdkHeaderCell,\n            CdkFooterCell,\n            CdkHeaderRow,\n            CdkHeaderRowDef,\n            CdkFooterRow,\n            CdkFooterRowDef,\n            DataRowOutlet,\n            HeaderRowOutlet,\n            FooterRowOutlet,\n            CdkTextColumn,\n            CdkNoDataRow,\n            CdkRecycleRows,\n            NoDataRowOutlet] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTableModule, imports: [ScrollingModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: EXPORTED_DECLARATIONS,\n                    imports: [ScrollingModule, ...EXPORTED_DECLARATIONS],\n                }]\n        }] });\n\n/**\n * Mixin to provide a directive with a function that checks if the sticky input has been\n * changed since the last time the function was called. Essentially adds a dirty-check to the\n * sticky value.\n * @docs-private\n * @deprecated Implement the `CanStick` interface instead.\n * @breaking-change 19.0.0\n */\nfunction mixinHasStickyInput(base) {\n    return class extends base {\n        /** Whether sticky positioning should be applied. */\n        get sticky() {\n            return this._sticky;\n        }\n        set sticky(v) {\n            const prevValue = this._sticky;\n            this._sticky = coerceBooleanProperty(v);\n            this._hasStickyChanged = prevValue !== this._sticky;\n        }\n        _sticky = false;\n        /** Whether the sticky input has changed since it was last checked. */\n        _hasStickyChanged = false;\n        /** Whether the sticky value has changed since this was last called. */\n        hasStickyChanged() {\n            const hasStickyChanged = this._hasStickyChanged;\n            this._hasStickyChanged = false;\n            return hasStickyChanged;\n        }\n        /** Resets the dirty check for cases where the sticky state has been used without checking. */\n        resetStickyChanged() {\n            this._hasStickyChanged = false;\n        }\n        constructor(...args) {\n            super(...args);\n        }\n    };\n}\n\nexport { BaseCdkCell, BaseRowDef, CDK_ROW_TEMPLATE, CDK_TABLE, CDK_TABLE_TEMPLATE, CdkCell, CdkCellDef, CdkCellOutlet, CdkColumnDef, CdkFooterCell, CdkFooterCellDef, CdkFooterRow, CdkFooterRowDef, CdkHeaderCell, CdkHeaderCellDef, CdkHeaderRow, CdkHeaderRowDef, CdkNoDataRow, CdkRecycleRows, CdkRow, CdkRowDef, CdkTable, CdkTableModule, CdkTextColumn, DataRowOutlet, FooterRowOutlet, HeaderRowOutlet, NoDataRowOutlet, STICKY_DIRECTIONS, STICKY_POSITIONING_LISTENER, StickyStyler, TEXT_COLUMN_OPTIONS, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, _Schedule, mixinHasStickyInput };\n//# sourceMappingURL=table.mjs.map\n"], "mappings": ";;;;;;IAoCiFA,EAAE,CAAAC,YAAA,KAwqE8rB,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxqEjsBH,EAAE,CAAAK,cAAA,cAwqEkwB,CAAC;IAxqErwBL,EAAE,CAAAM,kBAAA,KAwqEyyB,CAAC;IAxqE5yBN,EAAE,CAAAO,YAAA,CAwqEuzB,CAAC;IAxqE1zBP,EAAE,CAAAK,cAAA,cAwqEs1B,CAAC;IAxqEz1BL,EAAE,CAAAM,kBAAA,KAwqEu3B,CAAC,KAAsC,CAAC;IAxqEj6BN,EAAE,CAAAO,YAAA,CAwqE46B,CAAC;IAxqE/6BP,EAAE,CAAAK,cAAA,cAwqE28B,CAAC;IAxqE98BL,EAAE,CAAAM,kBAAA,KAwqEk/B,CAAC;IAxqEr/BN,EAAE,CAAAO,YAAA,CAwqEggC,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxqEngCH,EAAE,CAAAM,kBAAA,KAwqEkjC,CAAC,KAA8B,CAAC,KAAoC,CAAC,KAAoC,CAAC;EAAA;AAAA;AAAA,SAAAG,6BAAAN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxqE9pCH,EAAE,CAAAK,cAAA,WA21EX,CAAC;IA31EQL,EAAE,CAAAU,MAAA,EA61E9E,CAAC;IA71E2EV,EAAE,CAAAO,YAAA,CA61EzE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAQ,MAAA,GA71EsEX,EAAE,CAAAY,aAAA;IAAFZ,EAAE,CAAAa,WAAA,eAAAF,MAAA,CAAAG,OA21EZ,CAAC;IA31ESd,EAAE,CAAAe,SAAA,CA61E9E,CAAC;IA71E2Ef,EAAE,CAAAgB,kBAAA,MAAAL,MAAA,CAAAM,UAAA,KA61E9E,CAAC;EAAA;AAAA;AAAA,SAAAC,6BAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA71E2EH,EAAE,CAAAK,cAAA,WA81Eb,CAAC;IA91EUL,EAAE,CAAAU,MAAA,EAg2E9E,CAAC;IAh2E2EV,EAAE,CAAAO,YAAA,CAg2EzE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAgB,OAAA,GAAAf,GAAA,CAAAgB,SAAA;IAAA,MAAAT,MAAA,GAh2EsEX,EAAE,CAAAY,aAAA;IAAFZ,EAAE,CAAAa,WAAA,eAAAF,MAAA,CAAAG,OA81Ed,CAAC;IA91EWd,EAAE,CAAAe,SAAA,CAg2E9E,CAAC;IAh2E2Ef,EAAE,CAAAgB,kBAAA,MAAAL,MAAA,CAAAU,YAAA,CAAAF,OAAA,EAAAR,MAAA,CAAAW,IAAA,MAg2E9E,CAAC;EAAA;AAAA;AAp4EN,SAASC,CAAC,IAAIC,YAAY,QAAQ,4BAA4B;AAC9D,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC5D,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAK3B,EAAE,MAAM,eAAe;AACnC,SAAS4B,cAAc,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,YAAY,EAAEC,UAAU,EAAEC,MAAM,EAAEC,UAAU,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC7W,SAASC,OAAO,EAAEC,eAAe,EAAEC,YAAY,EAAEC,EAAE,QAAQ,MAAM;AACjE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,sBAAsB,QAAQ,+CAA+C;AAC5J,SAASH,CAAC,IAAII,4BAA4B,QAAQ,+CAA+C;AACjG,SAAStC,CAAC,IAAIuC,cAAc,QAAQ,+BAA+B;AACnE,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,aAAa,EAAEC,eAAe,QAAQ,iBAAiB;AAChE,SAASC,CAAC,IAAIC,qBAAqB,QAAQ,iCAAiC;AAC5E,OAAO,wBAAwB;AAC/B,OAAO,0BAA0B;AACjC,OAAO,YAAY;;AAEnB;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,IAAI3C,cAAc,CAAC,WAAW,CAAC;AACjD;AACA,MAAM4C,mBAAmB,GAAG,IAAI5C,cAAc,CAAC,qBAAqB,CAAC;;AAErE;AACA;AACA;AACA;AACA,MAAM6C,UAAU,CAAC;EAGbC,WAAWA,CAAA,EAAG;IAFd;IAAAC,eAAA,mBACW9C,MAAM,CAACC,WAAW,CAAC;EACd;AAGpB;AAAC8C,WAAA,GANKH,UAAU;AAAAE,eAAA,CAAVF,UAAU,wBAAAI,oBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAIuFL,WAAU;AAAA;AAAAE,eAAA,CAJ3GF,UAAU,8BAOiEzE,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAFQP,WAAU;EAAAQ,SAAA;AAAA;AAErG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFlF,EAAE,CAAAmF,iBAAA,CAAQV,UAAU,EAAc,CAAC;IACxGO,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EAGnBZ,WAAWA,CAAA,EAAG;IAFd;IAAAC,eAAA,mBACW9C,MAAM,CAACC,WAAW,CAAC;EACd;AAGpB;AAACyD,iBAAA,GANKD,gBAAgB;AAAAX,eAAA,CAAhBW,gBAAgB,wBAAAE,0BAAAV,iBAAA;EAAA,YAAAA,iBAAA,IAIiFQ,iBAAgB;AAAA;AAAAX,eAAA,CAJjHW,gBAAgB,8BAV2DtF,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAeQM,iBAAgB;EAAAL,SAAA;AAAA;AAE3G;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjBiFlF,EAAE,CAAAmF,iBAAA,CAiBQG,gBAAgB,EAAc,CAAC;IAC9GN,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMI,gBAAgB,CAAC;EAGnBf,WAAWA,CAAA,EAAG;IAFd;IAAAC,eAAA,mBACW9C,MAAM,CAACC,WAAW,CAAC;EACd;AAGpB;AAAC4D,iBAAA,GANKD,gBAAgB;AAAAd,eAAA,CAAhBc,gBAAgB,wBAAAE,0BAAAb,iBAAA;EAAA,YAAAA,iBAAA,IAIiFW,iBAAgB;AAAA;AAAAd,eAAA,CAJjHc,gBAAgB,8BA3B2DzF,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAgCQS,iBAAgB;EAAAR,SAAA;AAAA;AAE3G;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlCiFlF,EAAE,CAAAmF,iBAAA,CAkCQM,gBAAgB,EAAc,CAAC;IAC9GT,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMO,YAAY,CAAC;EAGf;EACA,IAAItE,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACuE,KAAK;EACrB;EACA,IAAIvE,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACwE,aAAa,CAACxE,IAAI,CAAC;EAC5B;EAEA;EACA,IAAIyE,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACD,OAAO,EAAE;MACxB,IAAI,CAACA,OAAO,GAAGC,KAAK;MACpB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IACjC;EACJ;EAEA;AACJ;AACA;AACA;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAACF,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,IAAI,CAACG,UAAU,EAAE;MAC3B,IAAI,CAACA,UAAU,GAAGH,KAAK;MACvB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IACjC;EACJ;EAmBAxB,WAAWA,CAAA,EAAG;IAAAC,eAAA,iBArDL9C,MAAM,CAAC0C,SAAS,EAAE;MAAE8B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA1B,eAAA,4BAC1B,KAAK;IAAAA,eAAA;IAAAA,eAAA,kBAmBf,KAAK;IAAAA,eAAA,qBAeF,KAAK;IAClB;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;AACJ;AACA;AACA;IAHIA,eAAA;EAKgB;EAChB;EACA2B,gBAAgBA,CAAA,EAAG;IACf,MAAMA,gBAAgB,GAAG,IAAI,CAACJ,iBAAiB;IAC/C,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACzB,OAAOD,gBAAgB;EAC3B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACL,iBAAiB,GAAG,KAAK;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAACC,mBAAmB,GAAG,CAAC,cAAc,IAAI,CAACC,oBAAoB,EAAE,CAAC;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIZ,aAAaA,CAACG,KAAK,EAAE;IACjB;IACA;IACA,IAAIA,KAAK,EAAE;MACP,IAAI,CAACJ,KAAK,GAAGI,KAAK;MAClB,IAAI,CAACS,oBAAoB,GAAGT,KAAK,CAACU,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MAC/D,IAAI,CAACH,yBAAyB,CAAC,CAAC;IACpC;EACJ;AAGJ;AAACI,aAAA,GA5FKhB,YAAY;AAAAjB,eAAA,CAAZiB,YAAY,wBAAAiB,sBAAA/B,iBAAA;EAAA,YAAAA,iBAAA,IA0FqFc,aAAY;AAAA;AAAAjB,eAAA,CA1F7GiB,YAAY,8BA5C+D5F,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAuIQY,aAAY;EAAAX,SAAA;EAAA6B,cAAA,WAAAC,6BAAA5G,EAAA,EAAAC,GAAA,EAAA4G,QAAA;IAAA,IAAA7G,EAAA;MAvItBH,EAAE,CAAAiH,cAAA,CAAAD,QAAA,EAuIwWvC,UAAU;MAvIpXzE,EAAE,CAAAiH,cAAA,CAAAD,QAAA,EAuI+b1B,gBAAgB;MAvIjdtF,EAAE,CAAAiH,cAAA,CAAAD,QAAA,EAuI4hBvB,gBAAgB;IAAA;IAAA,IAAAtF,EAAA;MAAA,IAAA+G,EAAA;MAvI9iBlH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAAiH,IAAA,GAAAH,EAAA,CAAAI,KAAA;MAAFtH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAAmH,UAAA,GAAAL,EAAA,CAAAI,KAAA;MAAFtH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAAoH,UAAA,GAAAN,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAAG,MAAA;IAAAnG,IAAA;IAAAyE,MAAA,0BAuI6I/D,gBAAgB;IAAAmE,SAAA,gCAAyCnE,gBAAgB;EAAA;EAAA0F,QAAA,GAvIxN1H,EAAE,CAAA2H,kBAAA,CAuIsO,CAAC;IAAEC,OAAO,EAAE,4BAA4B;IAAEC,WAAW,EAAEjC;EAAa,CAAC,CAAC;AAAA;AAE/X;EAAA,QAAAV,SAAA,oBAAAA,SAAA,KAzIiFlF,EAAE,CAAAmF,iBAAA,CAyIQS,YAAY,EAAc,CAAC;IAC1GZ,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1ByC,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAE,4BAA4B;QAAEC,WAAW,EAAEjC;MAAa,CAAC;IACpF,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEtE,IAAI,EAAE,CAAC;MAC/C0D,IAAI,EAAE/C,KAAK;MACXmD,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEW,MAAM,EAAE,CAAC;MACTf,IAAI,EAAE/C,KAAK;MACXmD,IAAI,EAAE,CAAC;QAAE2C,SAAS,EAAE/F;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmE,SAAS,EAAE,CAAC;MACZnB,IAAI,EAAE/C,KAAK;MACXmD,IAAI,EAAE,CAAC;QAAE2C,SAAS,EAAE/F;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqF,IAAI,EAAE,CAAC;MACPrC,IAAI,EAAE9C,YAAY;MAClBkD,IAAI,EAAE,CAACX,UAAU;IACrB,CAAC,CAAC;IAAE8C,UAAU,EAAE,CAAC;MACbvC,IAAI,EAAE9C,YAAY;MAClBkD,IAAI,EAAE,CAACE,gBAAgB;IAC3B,CAAC,CAAC;IAAEkC,UAAU,EAAE,CAAC;MACbxC,IAAI,EAAE9C,YAAY;MAClBkD,IAAI,EAAE,CAACK,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMuC,WAAW,CAAC;EACdtD,WAAWA,CAACuD,SAAS,EAAEC,UAAU,EAAE;IAC/BA,UAAU,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,GAAGJ,SAAS,CAACxB,mBAAmB,CAAC;EAC5E;AACJ;AACA;AACA,MAAM6B,aAAa,SAASN,WAAW,CAAC;EACpCtD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC7C,MAAM,CAAC+D,YAAY,CAAC,EAAE/D,MAAM,CAACM,UAAU,CAAC,CAAC;EACnD;AAGJ;AAACoG,cAAA,GANKD,aAAa;AAAA3D,eAAA,CAAb2D,aAAa,wBAAAE,uBAAA1D,iBAAA;EAAA,YAAAA,iBAAA,IAIoFwD,cAAa;AAAA;AAAA3D,eAAA,CAJ9G2D,aAAa,8BAzK8DtI,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EA8KQsD,cAAa;EAAArD,SAAA;EAAAwD,SAAA,WAAsG,cAAc;EAAAf,QAAA,GA9K3I1H,EAAE,CAAA0I,0BAAA;AAAA;AAgLnF;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KAhLiFlF,EAAE,CAAAmF,iBAAA,CAgLQmD,aAAa,EAAc,CAAC;IAC3GtD,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDsD,IAAI,EAAE;QACF,OAAO,EAAE,iBAAiB;QAC1B,MAAM,EAAE;MACZ;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,MAAMC,aAAa,SAASZ,WAAW,CAAC;EACpCtD,WAAWA,CAAA,EAAG;IAAA,IAAAmE,iBAAA;IACV,MAAMZ,SAAS,GAAGpG,MAAM,CAAC+D,YAAY,CAAC;IACtC,MAAMsC,UAAU,GAAGrG,MAAM,CAACM,UAAU,CAAC;IACrC,KAAK,CAAC8F,SAAS,EAAEC,UAAU,CAAC;IAC5B,MAAMY,IAAI,IAAAD,iBAAA,GAAGZ,SAAS,CAACc,MAAM,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBG,YAAY,CAAC,CAAC;IAC7C,IAAIF,IAAI,EAAE;MACNZ,UAAU,CAACC,aAAa,CAACc,YAAY,CAAC,MAAM,EAAEH,IAAI,CAAC;IACvD;EACJ;AAGJ;AAACI,cAAA,GAZKN,aAAa;AAAAjE,eAAA,CAAbiE,aAAa,wBAAAO,uBAAArE,iBAAA;EAAA,YAAAA,iBAAA,IAUoF8D,cAAa;AAAA;AAAAjE,eAAA,CAV9GiE,aAAa,8BA3L8D5I,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAsMQ4D,cAAa;EAAA3D,SAAA;EAAAwD,SAAA;EAAAf,QAAA,GAtMvB1H,EAAE,CAAA0I,0BAAA;AAAA;AAwMnF;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KAxMiFlF,EAAE,CAAAmF,iBAAA,CAwMQyD,aAAa,EAAc,CAAC;IAC3G5D,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDsD,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,MAAMS,OAAO,SAASpB,WAAW,CAAC;EAC9BtD,WAAWA,CAAA,EAAG;IAAA,IAAA2E,kBAAA;IACV,MAAMpB,SAAS,GAAGpG,MAAM,CAAC+D,YAAY,CAAC;IACtC,MAAMsC,UAAU,GAAGrG,MAAM,CAACM,UAAU,CAAC;IACrC,KAAK,CAAC8F,SAAS,EAAEC,UAAU,CAAC;IAC5B,MAAMY,IAAI,IAAAO,kBAAA,GAAGpB,SAAS,CAACc,MAAM,cAAAM,kBAAA,uBAAhBA,kBAAA,CAAkBL,YAAY,CAAC,CAAC;IAC7C,IAAIF,IAAI,EAAE;MACNZ,UAAU,CAACC,aAAa,CAACc,YAAY,CAAC,MAAM,EAAEH,IAAI,CAAC;IACvD;EACJ;AAGJ;AAACQ,QAAA,GAZKF,OAAO;AAAAzE,eAAA,CAAPyE,OAAO,wBAAAG,iBAAAzE,iBAAA;EAAA,YAAAA,iBAAA,IAU0FsE,QAAO;AAAA;AAAAzE,eAAA,CAVxGyE,OAAO,8BAlNoEpJ,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EA6NQoE,QAAO;EAAAnE,SAAA;EAAAwD,SAAA;EAAAf,QAAA,GA7NjB1H,EAAE,CAAA0I,0BAAA;AAAA;AA+NnF;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KA/NiFlF,EAAE,CAAAmF,iBAAA,CA+NQiE,OAAO,EAAc,CAAC;IACrGpE,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCsD,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA,MAAMa,SAAS,CAAC;EAAA9E,YAAA;IAAAC,eAAA,gBACJ,EAAE;IAAAA,eAAA,mBACC,EAAE;EAAA;AACjB;AACA;AACA,MAAM8E,0BAA0B,GAAG,IAAI7H,cAAc,CAAC,4BAA4B,CAAC;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8H,wBAAwB,CAAC;EAG3BhF,WAAWA,CAAA,EAAG;IAAAC,eAAA,2BAFK,IAAI;IAAAA,eAAA,kBACb9C,MAAM,CAACO,MAAM,CAAC;EACR;EAChB;AACJ;AACA;EACIuH,QAAQA,CAACC,IAAI,EAAE;IACX,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,gBAAgB,CAACC,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIK,WAAWA,CAACL,IAAI,EAAE;IACd,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,gBAAgB,CAACI,QAAQ,CAACF,IAAI,CAACJ,IAAI,CAAC;EAC7C;EACAC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACvB;IACJ;IACA,IAAI,CAACA,gBAAgB,GAAG,IAAIN,SAAS,CAAC,CAAC;IACvC,IAAI,CAACW,OAAO,CAACC,iBAAiB,CAAC;IAC/B;IACA;IACA;IACA;IACAC,cAAc,CAAC,MAAM;MACjB,OAAO,IAAI,CAACP,gBAAgB,CAACC,KAAK,CAACO,MAAM,IAAI,IAAI,CAACR,gBAAgB,CAACI,QAAQ,CAACI,MAAM,EAAE;QAChF,MAAMX,QAAQ,GAAG,IAAI,CAACG,gBAAgB;QACtC;QACA,IAAI,CAACA,gBAAgB,GAAG,IAAIN,SAAS,CAAC,CAAC;QACvC,KAAK,MAAMI,IAAI,IAAID,QAAQ,CAACI,KAAK,EAAE;UAC/BH,IAAI,CAAC,CAAC;QACV;QACA,KAAK,MAAMA,IAAI,IAAID,QAAQ,CAACO,QAAQ,EAAE;UAClCN,IAAI,CAAC,CAAC;QACV;MACJ;MACA,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAChC,CAAC,CAAC,CAAC;EACP;AAGJ;AAACS,yBAAA,GA9CKb,wBAAwB;AAAA/E,eAAA,CAAxB+E,wBAAwB,wBAAAc,kCAAA1F,iBAAA;EAAA,YAAAA,iBAAA,IA4CyE4E,yBAAwB;AAAA;AAAA/E,eAAA,CA5CzH+E,wBAAwB,+BAzPmD1J,EAAE,CAAAyK,kBAAA;EAAAC,KAAA,EAsSwBhB,yBAAwB;EAAAiB,OAAA,EAAxBjB,yBAAwB,CAAAkB;AAAA;AAEnI;EAAA,QAAA1F,SAAA,oBAAAA,SAAA,KAxSiFlF,EAAE,CAAAmF,iBAAA,CAwSQuE,wBAAwB,EAAc,CAAC;IACtH1E,IAAI,EAAE3C;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA,MAAMwI,gBAAgB,GAAG,6CAA6C;AACtE;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EAObpG,WAAWA,CAAA,EAAG;IAAAC,eAAA,mBANH9C,MAAM,CAACC,WAAW,CAAC;IAAA6C,eAAA,mBACnB9C,MAAM,CAACS,eAAe,CAAC;IAClC;IAAAqC,eAAA;IAEA;IAAAA,eAAA;EAEgB;EAChBoG,WAAWA,CAACC,OAAO,EAAE;IACjB;IACA;IACA,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;MACtB,MAAMC,OAAO,GAAIF,OAAO,CAAC,SAAS,CAAC,IAAIA,OAAO,CAAC,SAAS,CAAC,CAACG,YAAY,IAAK,EAAE;MAC7E,IAAI,CAACF,cAAc,GAAG,IAAI,CAACG,QAAQ,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,MAAM,CAAC,CAAC;MAC1D,IAAI,CAACL,cAAc,CAACM,IAAI,CAACL,OAAO,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;EACIM,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACP,cAAc,CAACM,IAAI,CAAC,IAAI,CAACL,OAAO,CAAC;EACjD;EACA;EACAO,mBAAmBA,CAACC,MAAM,EAAE;IACxB,IAAI,IAAI,YAAYC,eAAe,EAAE;MACjC,OAAOD,MAAM,CAACnE,UAAU,CAACqE,QAAQ;IACrC;IACA,IAAI,IAAI,YAAYC,eAAe,EAAE;MACjC,OAAOH,MAAM,CAAClE,UAAU,CAACoE,QAAQ;IACrC,CAAC,MACI;MACD,OAAOF,MAAM,CAACrE,IAAI,CAACuE,QAAQ;IAC/B;EACJ;AAGJ;AAACE,WAAA,GAtCKhB,UAAU;AAAAnG,eAAA,CAAVmG,UAAU,wBAAAiB,oBAAAjH,iBAAA;EAAA,YAAAA,iBAAA,IAoCuFgG,WAAU;AAAA;AAAAnG,eAAA,CApC3GmG,UAAU,8BArTiE9K,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EA0VQ8F,WAAU;EAAApD,QAAA,GA1VpB1H,EAAE,CAAAgM,oBAAA;AAAA;AA4VnF;EAAA,QAAA9G,SAAA,oBAAAA,SAAA,KA5ViFlF,EAAE,CAAAmF,iBAAA,CA4VQ2F,UAAU,EAAc,CAAC;IACxG9F,IAAI,EAAEjD;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAM4J,eAAe,SAASb,UAAU,CAAC;EAGrC;EACA,IAAI/E,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACD,OAAO,EAAE;MACxB,IAAI,CAACA,OAAO,GAAGC,KAAK;MACpB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IACjC;EACJ;EAEAxB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC7C,MAAM,CAACC,WAAW,CAAC,EAAED,MAAM,CAACS,eAAe,CAAC,CAAC;IAACqC,eAAA,iBAd/C9C,MAAM,CAAC0C,SAAS,EAAE;MAAE8B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA1B,eAAA,4BAC1B,KAAK;IAAAA,eAAA,kBAWf,KAAK;EAGf;EACA;EACA;EACAoG,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACD,WAAW,CAACC,OAAO,CAAC;EAC9B;EACA;EACA1E,gBAAgBA,CAAA,EAAG;IACf,MAAMA,gBAAgB,GAAG,IAAI,CAACJ,iBAAiB;IAC/C,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACzB,OAAOD,gBAAgB;EAC3B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACL,iBAAiB,GAAG,KAAK;EAClC;AAGJ;AAAC+F,gBAAA,GAlCKN,eAAe;AAAAhH,eAAA,CAAfgH,eAAe,wBAAAO,yBAAApH,iBAAA;EAAA,YAAAA,iBAAA,IAgCkF6G,gBAAe;AAAA;AAAAhH,eAAA,CAhChHgH,eAAe,8BAnW4D3L,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAoYQ2G,gBAAe;EAAA1G,SAAA;EAAAwC,MAAA;IAAAyD,OAAA;IAAAnF,MAAA,yCAAoJ/D,gBAAgB;EAAA;EAAA0F,QAAA,GApY7L1H,EAAE,CAAA0I,0BAAA,EAAF1I,EAAE,CAAAgM,oBAAA;AAAA;AAsYnF;EAAA,QAAA9G,SAAA,oBAAAA,SAAA,KAtYiFlF,EAAE,CAAAmF,iBAAA,CAsYQwG,eAAe,EAAc,CAAC;IAC7G3G,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BoC,MAAM,EAAE,CAAC;QAAEnG,IAAI,EAAE,SAAS;QAAE6K,KAAK,EAAE;MAAkB,CAAC;IAC1D,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEpG,MAAM,EAAE,CAAC;MACjDf,IAAI,EAAE/C,KAAK;MACXmD,IAAI,EAAE,CAAC;QAAE+G,KAAK,EAAE,uBAAuB;QAAEpE,SAAS,EAAE/F;MAAiB,CAAC;IAC1E,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAM6J,eAAe,SAASf,UAAU,CAAC;EAGrC;EACA,IAAI/E,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACD,OAAO,EAAE;MACxB,IAAI,CAACA,OAAO,GAAGC,KAAK;MACpB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IACjC;EACJ;EAEAxB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC7C,MAAM,CAACC,WAAW,CAAC,EAAED,MAAM,CAACS,eAAe,CAAC,CAAC;IAACqC,eAAA,iBAd/C9C,MAAM,CAAC0C,SAAS,EAAE;MAAE8B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA1B,eAAA,4BAC1B,KAAK;IAAAA,eAAA,kBAWf,KAAK;EAGf;EACA;EACA;EACAoG,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACD,WAAW,CAACC,OAAO,CAAC;EAC9B;EACA;EACA1E,gBAAgBA,CAAA,EAAG;IACf,MAAMA,gBAAgB,GAAG,IAAI,CAACJ,iBAAiB;IAC/C,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACzB,OAAOD,gBAAgB;EAC3B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACL,iBAAiB,GAAG,KAAK;EAClC;AAGJ;AAACkG,gBAAA,GAlCKP,eAAe;AAAAlH,eAAA,CAAfkH,eAAe,wBAAAQ,yBAAAvH,iBAAA;EAAA,YAAAA,iBAAA,IAgCkF+G,gBAAe;AAAA;AAAAlH,eAAA,CAhChHkH,eAAe,8BApZ4D7L,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAqbQ6G,gBAAe;EAAA5G,SAAA;EAAAwC,MAAA;IAAAyD,OAAA;IAAAnF,MAAA,yCAAoJ/D,gBAAgB;EAAA;EAAA0F,QAAA,GArb7L1H,EAAE,CAAA0I,0BAAA,EAAF1I,EAAE,CAAAgM,oBAAA;AAAA;AAubnF;EAAA,QAAA9G,SAAA,oBAAAA,SAAA,KAvbiFlF,EAAE,CAAAmF,iBAAA,CAubQ0G,eAAe,EAAc,CAAC;IAC7G7G,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BoC,MAAM,EAAE,CAAC;QAAEnG,IAAI,EAAE,SAAS;QAAE6K,KAAK,EAAE;MAAkB,CAAC;IAC1D,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEpG,MAAM,EAAE,CAAC;MACjDf,IAAI,EAAE/C,KAAK;MACXmD,IAAI,EAAE,CAAC;QAAE+G,KAAK,EAAE,uBAAuB;QAAEpE,SAAS,EAAE/F;MAAiB,CAAC;IAC1E,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,MAAMsK,SAAS,SAASxB,UAAU,CAAC;EAS/BpG,WAAWA,CAAA,EAAG;IACV;IACA;IACA,KAAK,CAAC7C,MAAM,CAACC,WAAW,CAAC,EAAED,MAAM,CAACS,eAAe,CAAC,CAAC;IAACqC,eAAA,iBAX/C9C,MAAM,CAAC0C,SAAS,EAAE;MAAE8B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC9C;AACJ;AACA;AACA;AACA;AACA;IALI1B,eAAA;EAWA;AAGJ;AAAC4H,UAAA,GAhBKD,SAAS;AAAA3H,eAAA,CAAT2H,SAAS,wBAAAE,mBAAA1H,iBAAA;EAAA,YAAAA,iBAAA,IAcwFwH,UAAS;AAAA;AAAA3H,eAAA,CAd1G2H,SAAS,8BAtckEtM,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAqdQsH,UAAS;EAAArH,SAAA;EAAAwC,MAAA;IAAAyD,OAAA;IAAAuB,IAAA;EAAA;EAAA/E,QAAA,GArdnB1H,EAAE,CAAA0I,0BAAA;AAAA;AAudnF;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KAvdiFlF,EAAE,CAAAmF,iBAAA,CAudQmH,SAAS,EAAc,CAAC;IACvGtH,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBoC,MAAM,EAAE,CACJ;QAAEnG,IAAI,EAAE,SAAS;QAAE6K,KAAK,EAAE;MAAmB,CAAC,EAC9C;QAAE7K,IAAI,EAAE,MAAM;QAAE6K,KAAK,EAAE;MAAgB,CAAC;IAEhD,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMO,aAAa,CAAC;EAchBhI,WAAWA,CAAA,EAAG;IAAAC,eAAA,yBAbG9C,MAAM,CAACU,gBAAgB,CAAC;IACzC;IAAAoC,eAAA;IAEA;IAAAA,eAAA;IAWI+H,aAAa,CAACC,oBAAoB,GAAG,IAAI;EAC7C;EACAC,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAIF,aAAa,CAACC,oBAAoB,KAAK,IAAI,EAAE;MAC7CD,aAAa,CAACC,oBAAoB,GAAG,IAAI;IAC7C;EACJ;AAGJ;AAACE,cAAA,GA1BKH,aAAa;AAMf;AACJ;AACA;AACA;AACA;AACA;AACA;AANI/H,eAAA,CANE+H,aAAa,0BAae,IAAI;AAAA/H,eAAA,CAbhC+H,aAAa,wBAAAI,uBAAAhI,iBAAA;EAAA,YAAAA,iBAAA,IAwBoF4H,cAAa;AAAA;AAAA/H,eAAA,CAxB9G+H,aAAa,8BAre8D1M,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EA8fQ0H,cAAa;EAAAzH,SAAA;AAAA;AAExG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhgBiFlF,EAAE,CAAAmF,iBAAA,CAggBQuH,aAAa,EAAc,CAAC;IAC3G1H,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,MAAM0H,YAAY,CAAC;AAGlBC,aAAA,GAHKD,YAAY;AAAApI,eAAA,CAAZoI,YAAY,wBAAAE,sBAAAnI,iBAAA;EAAA,YAAAA,iBAAA,IACqFiI,aAAY;AAAA;AAAApI,eAAA,CAD7GoI,YAAY,8BAvgB+D/M,EAAE,CAAAkN,iBAAA;EAAAlI,IAAA,EAygBQ+H,aAAY;EAAA9H,SAAA;EAAAwD,SAAA,WAAoG,KAAK;EAAA0E,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAzB,QAAA,WAAA0B,uBAAAnN,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAzgB/HH,EAAE,CAAAM,kBAAA,KAygBwO,CAAC;IAAA;EAAA;EAAAiN,YAAA,GAA6Db,aAAa;EAAAc,aAAA;AAAA;AAEtY;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KA3gBiFlF,EAAE,CAAAmF,iBAAA,CA2gBQ4H,YAAY,EAAc,CAAC;IAC1G/H,IAAI,EAAExC,SAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CuG,QAAQ,EAAEf,gBAAgB;MAC1BlC,IAAI,EAAE;QACF,OAAO,EAAE,gBAAgB;QACzB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACA8E,eAAe,EAAEhL,uBAAuB,CAACiL,OAAO;MAChDF,aAAa,EAAE9K,iBAAiB,CAACiL,IAAI;MACrCC,OAAO,EAAE,CAAClB,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMmB,YAAY,CAAC;AAGlBC,aAAA,GAHKD,YAAY;AAAAlJ,eAAA,CAAZkJ,YAAY,wBAAAE,sBAAAjJ,iBAAA;EAAA,YAAAA,iBAAA,IACqF+I,aAAY;AAAA;AAAAlJ,eAAA,CAD7GkJ,YAAY,8BA5hB+D7N,EAAE,CAAAkN,iBAAA;EAAAlI,IAAA,EA8hBQ6I,aAAY;EAAA5I,SAAA;EAAAwD,SAAA,WAAoG,KAAK;EAAA0E,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAzB,QAAA,WAAAoC,uBAAA7N,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA9hB/HH,EAAE,CAAAM,kBAAA,KA8hBwO,CAAC;IAAA;EAAA;EAAAiN,YAAA,GAA6Db,aAAa;EAAAc,aAAA;AAAA;AAEtY;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KAhiBiFlF,EAAE,CAAAmF,iBAAA,CAgiBQ0I,YAAY,EAAc,CAAC;IAC1G7I,IAAI,EAAExC,SAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CuG,QAAQ,EAAEf,gBAAgB;MAC1BlC,IAAI,EAAE;QACF,OAAO,EAAE,gBAAgB;QACzB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACA8E,eAAe,EAAEhL,uBAAuB,CAACiL,OAAO;MAChDF,aAAa,EAAE9K,iBAAiB,CAACiL,IAAI;MACrCC,OAAO,EAAE,CAAClB,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMuB,MAAM,CAAC;AAGZC,OAAA,GAHKD,MAAM;AAAAtJ,eAAA,CAANsJ,MAAM,wBAAAE,gBAAArJ,iBAAA;EAAA,YAAAA,iBAAA,IAC2FmJ,OAAM;AAAA;AAAAtJ,eAAA,CADvGsJ,MAAM,8BAjjBqEjO,EAAE,CAAAkN,iBAAA;EAAAlI,IAAA,EAmjBQiJ,OAAM;EAAAhJ,SAAA;EAAAwD,SAAA,WAAsF,KAAK;EAAA0E,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAzB,QAAA,WAAAwC,iBAAAjO,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAnjB3GH,EAAE,CAAAM,kBAAA,KAmjB6M,CAAC;IAAA;EAAA;EAAAiN,YAAA,GAA6Db,aAAa;EAAAc,aAAA;AAAA;AAE3W;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KArjBiFlF,EAAE,CAAAmF,iBAAA,CAqjBQ8I,MAAM,EAAc,CAAC;IACpGjJ,IAAI,EAAExC,SAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCuG,QAAQ,EAAEf,gBAAgB;MAC1BlC,IAAI,EAAE;QACF,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACA8E,eAAe,EAAEhL,uBAAuB,CAACiL,OAAO;MAChDF,aAAa,EAAE9K,iBAAiB,CAACiL,IAAI;MACrCC,OAAO,EAAE,CAAClB,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM2B,YAAY,CAAC;EAGf3J,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBAFA9C,MAAM,CAACC,WAAW,CAAC;IAAA6C,eAAA,4BACb,iBAAiB;EACrB;AAGpB;AAAC2J,aAAA,GANKD,YAAY;AAAA1J,eAAA,CAAZ0J,YAAY,wBAAAE,sBAAAzJ,iBAAA;EAAA,YAAAA,iBAAA,IAIqFuJ,aAAY;AAAA;AAAA1J,eAAA,CAJ7G0J,YAAY,8BAtkB+DrO,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EA2kBQqJ,aAAY;EAAApJ,SAAA;AAAA;AAEvG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7kBiFlF,EAAE,CAAAmF,iBAAA,CA6kBQkJ,YAAY,EAAc,CAAC;IAC1GrJ,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmJ,iBAAiB,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;AAC5D;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EAkBf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI/J,WAAWA,CAACgK,kBAAkB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,UAAU,GAAG,IAAI,EAAEC,6BAA6B,GAAG,IAAI,EAAEC,iBAAiB,EAAEC,cAAc,EAAE;IAAAtK,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,yBAxB/J,IAAIuK,OAAO,CAAC,CAAC;IAAAvK,eAAA,0BACZwK,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEC,cAAc,GACtC,IAAID,UAAU,CAACC,cAAc,CAACC,OAAO,IAAI,IAAI,CAACC,kBAAkB,CAACD,OAAO,CAAC,CAAC,GAC1E,IAAI;IAAA1K,eAAA,8CAC4B,EAAE;IAAAA,eAAA,sCACV,IAAI;IAAAA,eAAA,4BACd,EAAE;IAAAA,eAAA;IAAAA,eAAA,qBAET,KAAK;IAiBd,IAAI,CAAC+J,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,cAAc,GAAG;MAClB,KAAK,EAAE,GAAGZ,aAAa,kBAAkB;MACzC,QAAQ,EAAE,GAAGA,aAAa,qBAAqB;MAC/C,MAAM,EAAE,GAAGA,aAAa,mBAAmB;MAC3C,OAAO,EAAE,GAAGA,aAAa;IAC7B,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACIa,sBAAsBA,CAACC,IAAI,EAAEC,gBAAgB,EAAE;IAC3C,IAAIA,gBAAgB,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAID,gBAAgB,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzE,IAAI,CAACC,kCAAkC,CAACH,IAAI,CAAC;IACjD;IACA,MAAMI,eAAe,GAAG,EAAE;IAC1B,KAAK,MAAMC,GAAG,IAAIL,IAAI,EAAE;MACpB;MACA;MACA,IAAIK,GAAG,CAACC,QAAQ,KAAKD,GAAG,CAACE,YAAY,EAAE;QACnC;MACJ;MACAH,eAAe,CAAC7F,IAAI,CAAC8F,GAAG,EAAE,GAAGG,KAAK,CAACC,IAAI,CAACJ,GAAG,CAACK,QAAQ,CAAC,CAAC;IAC1D;IACA;IACA,IAAI,CAACC,gBAAgB,CAAC;MAClBC,KAAK,EAAEA,CAAA,KAAM;QACT,KAAK,MAAMC,OAAO,IAAIT,eAAe,EAAE;UACnC,IAAI,CAACU,kBAAkB,CAACD,OAAO,EAAEZ,gBAAgB,CAAC;QACtD;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIc,mBAAmBA,CAACf,IAAI,EAAEgB,iBAAiB,EAAEC,eAAe,EAAEC,qBAAqB,GAAG,IAAI,EAAEC,MAAM,GAAG,IAAI,EAAE;IACvG;IACA,IAAI,CAACnB,IAAI,CAACnF,MAAM,IACZ,CAAC,IAAI,CAACwE,UAAU,IAChB,EAAE2B,iBAAiB,CAACI,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC,IAAIJ,eAAe,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC,CAAC,EAAE;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MACnF,CAAAD,qBAAA,OAAI,CAAC/B,iBAAiB,cAAA+B,qBAAA,eAAtBA,qBAAA,CAAwBE,oBAAoB,CAAC;QAAEC,KAAK,EAAE;MAAG,CAAC,CAAC;MAC3D,CAAAF,sBAAA,OAAI,CAAChC,iBAAiB,cAAAgC,sBAAA,eAAtBA,sBAAA,CAAwBG,uBAAuB,CAAC;QAAED,KAAK,EAAE;MAAG,CAAC,CAAC;MAC9D;IACJ;IACA;IACA,MAAME,QAAQ,GAAG3B,IAAI,CAAC,CAAC,CAAC;IACxB,MAAM4B,QAAQ,GAAGD,QAAQ,CAACjB,QAAQ,CAAC7F,MAAM;IACzC,MAAMgH,KAAK,GAAG,IAAI,CAAC1C,SAAS,KAAK,KAAK;IACtC,MAAM2C,KAAK,GAAGD,KAAK,GAAG,OAAO,GAAG,MAAM;IACtC,MAAME,GAAG,GAAGF,KAAK,GAAG,MAAM,GAAG,OAAO;IACpC,MAAMG,eAAe,GAAGhB,iBAAiB,CAACiB,WAAW,CAAC,IAAI,CAAC;IAC3D,MAAMC,cAAc,GAAGjB,eAAe,CAACkB,OAAO,CAAC,IAAI,CAAC;IACpD,IAAIC,UAAU;IACd,IAAIC,cAAc;IAClB,IAAIC,YAAY;IAChB,IAAInB,MAAM,EAAE;MACR,IAAI,CAACoB,8BAA8B,CAAC;QAChCvC,IAAI,EAAE,CAAC,GAAGA,IAAI,CAAC;QACfgB,iBAAiB,EAAE,CAAC,GAAGA,iBAAiB,CAAC;QACzCC,eAAe,EAAE,CAAC,GAAGA,eAAe;MACxC,CAAC,CAAC;IACN;IACA,IAAI,CAACN,gBAAgB,CAAC;MAClB6B,SAAS,EAAEA,CAAA,KAAM;QACbJ,UAAU,GAAG,IAAI,CAACK,cAAc,CAACd,QAAQ,EAAET,qBAAqB,CAAC;QACjEmB,cAAc,GAAG,IAAI,CAACK,8BAA8B,CAACN,UAAU,EAAEpB,iBAAiB,CAAC;QACnFsB,YAAY,GAAG,IAAI,CAACK,4BAA4B,CAACP,UAAU,EAAEnB,eAAe,CAAC;MACjF,CAAC;MACDL,KAAK,EAAEA,CAAA,KAAM;QACT,KAAK,MAAMP,GAAG,IAAIL,IAAI,EAAE;UACpB,KAAK,IAAIlO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8P,QAAQ,EAAE9P,CAAC,EAAE,EAAE;YAC/B,MAAM8F,IAAI,GAAGyI,GAAG,CAACK,QAAQ,CAAC5O,CAAC,CAAC;YAC5B,IAAIkP,iBAAiB,CAAClP,CAAC,CAAC,EAAE;cACtB,IAAI,CAAC8Q,eAAe,CAAChL,IAAI,EAAEkK,KAAK,EAAEO,cAAc,CAACvQ,CAAC,CAAC,EAAEA,CAAC,KAAKkQ,eAAe,CAAC;YAC/E;YACA,IAAIf,eAAe,CAACnP,CAAC,CAAC,EAAE;cACpB,IAAI,CAAC8Q,eAAe,CAAChL,IAAI,EAAEmK,GAAG,EAAEO,YAAY,CAACxQ,CAAC,CAAC,EAAEA,CAAC,KAAKoQ,cAAc,CAAC;YAC1E;UACJ;QACJ;QACA,IAAI,IAAI,CAAC3C,iBAAiB,IAAI6C,UAAU,CAAChB,IAAI,CAACyB,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC,EAAE;UACrD,IAAI,CAACtD,iBAAiB,CAACiC,oBAAoB,CAAC;YACxCC,KAAK,EAAEO,eAAe,KAAK,CAAC,CAAC,GACvB,EAAE,GACFI,UAAU,CACPU,KAAK,CAAC,CAAC,EAAEd,eAAe,GAAG,CAAC,CAAC,CAC7Be,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAMjC,iBAAiB,CAACiC,KAAK,CAAC,GAAGD,KAAK,GAAG,IAAK;UAC5E,CAAC,CAAC;UACF,IAAI,CAACzD,iBAAiB,CAACmC,uBAAuB,CAAC;YAC3CD,KAAK,EAAES,cAAc,KAAK,CAAC,CAAC,GACtB,EAAE,GACFE,UAAU,CACPU,KAAK,CAACZ,cAAc,CAAC,CACrBa,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAMhC,eAAe,CAACgC,KAAK,GAAGf,cAAc,CAAC,GAAGc,KAAK,GAAG,IAAK,CAAC,CAC/EE,OAAO,CAAC;UACrB,CAAC,CAAC;QACN;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAE;IAC3C;IACA,IAAI,CAAC,IAAI,CAACjE,UAAU,EAAE;MAClB;IACJ;IACA;IACA;IACA;IACA,MAAMW,IAAI,GAAGsD,QAAQ,KAAK,QAAQ,GAAGF,WAAW,CAACN,KAAK,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,GAAGE,WAAW;IAChF,MAAMG,MAAM,GAAGD,QAAQ,KAAK,QAAQ,GAAGD,YAAY,CAACP,KAAK,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,GAAGG,YAAY;IACpF;IACA,MAAMG,aAAa,GAAG,EAAE;IACxB,MAAMC,iBAAiB,GAAG,EAAE;IAC5B,MAAMC,eAAe,GAAG,EAAE;IAC1B;IACA;IACA,IAAI,CAAC/C,gBAAgB,CAAC;MAClB6B,SAAS,EAAEA,CAAA,KAAM;QACb,KAAK,IAAImB,QAAQ,GAAG,CAAC,EAAEC,YAAY,GAAG,CAAC,EAAED,QAAQ,GAAG3D,IAAI,CAACnF,MAAM,EAAE8I,QAAQ,EAAE,EAAE;UACzE,IAAI,CAACJ,MAAM,CAACI,QAAQ,CAAC,EAAE;YACnB;UACJ;UACAH,aAAa,CAACG,QAAQ,CAAC,GAAGC,YAAY;UACtC,MAAMvD,GAAG,GAAGL,IAAI,CAAC2D,QAAQ,CAAC;UAC1BD,eAAe,CAACC,QAAQ,CAAC,GAAG,IAAI,CAAC1E,kBAAkB,GAC7CuB,KAAK,CAACC,IAAI,CAACJ,GAAG,CAACK,QAAQ,CAAC,GACxB,CAACL,GAAG,CAAC;UACX,MAAMwD,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAACzD,GAAG,CAAC,CAACwD,MAAM;UACpDD,YAAY,IAAIC,MAAM;UACtBJ,iBAAiB,CAACE,QAAQ,CAAC,GAAGE,MAAM;QACxC;MACJ,CAAC;MACDjD,KAAK,EAAEA,CAAA,KAAM;QACT,MAAMmD,gBAAgB,GAAGR,MAAM,CAACtB,WAAW,CAAC,IAAI,CAAC;QACjD,KAAK,IAAI0B,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG3D,IAAI,CAACnF,MAAM,EAAE8I,QAAQ,EAAE,EAAE;UACvD,IAAI,CAACJ,MAAM,CAACI,QAAQ,CAAC,EAAE;YACnB;UACJ;UACA,MAAMK,MAAM,GAAGR,aAAa,CAACG,QAAQ,CAAC;UACtC,MAAMM,kBAAkB,GAAGN,QAAQ,KAAKI,gBAAgB;UACxD,KAAK,MAAMlD,OAAO,IAAI6C,eAAe,CAACC,QAAQ,CAAC,EAAE;YAC7C,IAAI,CAACf,eAAe,CAAC/B,OAAO,EAAEyC,QAAQ,EAAEU,MAAM,EAAEC,kBAAkB,CAAC;UACvE;QACJ;QACA,IAAIX,QAAQ,KAAK,KAAK,EAAE;UAAA,IAAAY,sBAAA;UACpB,CAAAA,sBAAA,OAAI,CAAC3E,iBAAiB,cAAA2E,sBAAA,eAAtBA,sBAAA,CAAwBC,uBAAuB,CAAC;YAC5C1C,KAAK,EAAEgC,iBAAiB;YACxBW,OAAO,EAAEZ,aAAa;YACtBa,QAAQ,EAAEX;UACd,CAAC,CAAC;QACN,CAAC,MACI;UAAA,IAAAY,sBAAA;UACD,CAAAA,sBAAA,OAAI,CAAC/E,iBAAiB,cAAA+E,sBAAA,eAAtBA,sBAAA,CAAwBC,uBAAuB,CAAC;YAC5C9C,KAAK,EAAEgC,iBAAiB;YACxBW,OAAO,EAAEZ,aAAa;YACtBa,QAAQ,EAAEX;UACd,CAAC,CAAC;QACN;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIc,2BAA2BA,CAACC,YAAY,EAAEpB,YAAY,EAAE;IACpD,IAAI,CAAC,IAAI,CAACpE,kBAAkB,EAAE;MAC1B;IACJ;IACA;IACA,IAAI,CAAC0B,gBAAgB,CAAC;MAClBC,KAAK,EAAEA,CAAA,KAAM;QACT,MAAM8D,KAAK,GAAGD,YAAY,CAACE,aAAa,CAAC,OAAO,CAAC;QACjD,IAAID,KAAK,EAAE;UACP,IAAIrB,YAAY,CAACjC,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAAC,EAAE;YACpC,IAAI,CAACP,kBAAkB,CAAC4D,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC;UAC9C,CAAC,MACI;YACD,IAAI,CAAC9B,eAAe,CAAC8B,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC;UACnD;QACJ;MACJ;IACJ,CAAC,CAAC;EACN;EACA;EACAE,OAAOA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACN,IAAI,IAAI,CAACC,2BAA2B,EAAE;MAClCC,YAAY,CAAC,IAAI,CAACD,2BAA2B,CAAC;IAClD;IACA,CAAAD,qBAAA,OAAI,CAACG,eAAe,cAAAH,qBAAA,eAApBA,qBAAA,CAAsBI,UAAU,CAAC,CAAC;IAClC,IAAI,CAACC,UAAU,GAAG,IAAI;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIpE,kBAAkBA,CAACD,OAAO,EAAEZ,gBAAgB,EAAE;IAC1C,IAAI,CAACY,OAAO,CAAClI,SAAS,CAACwM,QAAQ,CAAC,IAAI,CAACjG,aAAa,CAAC,EAAE;MACjD;IACJ;IACA,KAAK,MAAMkG,GAAG,IAAInF,gBAAgB,EAAE;MAChCY,OAAO,CAACwE,KAAK,CAACD,GAAG,CAAC,GAAG,EAAE;MACvBvE,OAAO,CAAClI,SAAS,CAAC2M,MAAM,CAAC,IAAI,CAACxF,cAAc,CAACsF,GAAG,CAAC,CAAC;IACtD;IACA;IACA;IACA;IACA;IACA,MAAMG,YAAY,GAAGxG,iBAAiB,CAACqC,IAAI,CAACgE,GAAG,IAAInF,gBAAgB,CAACkC,OAAO,CAACiD,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIvE,OAAO,CAACwE,KAAK,CAACD,GAAG,CAAC,CAAC;IAC9G,IAAIG,YAAY,EAAE;MACd1E,OAAO,CAACwE,KAAK,CAACG,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAAC5E,OAAO,CAAC;IAC7D,CAAC,MACI;MACD;MACAA,OAAO,CAACwE,KAAK,CAACG,MAAM,GAAG,EAAE;MACzB,IAAI,IAAI,CAAClG,6BAA6B,EAAE;QACpCuB,OAAO,CAACwE,KAAK,CAAC/B,QAAQ,GAAG,EAAE;MAC/B;MACAzC,OAAO,CAAClI,SAAS,CAAC2M,MAAM,CAAC,IAAI,CAACpG,aAAa,CAAC;IAChD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI0D,eAAeA,CAAC/B,OAAO,EAAEuE,GAAG,EAAEM,QAAQ,EAAEC,eAAe,EAAE;IACrD9E,OAAO,CAAClI,SAAS,CAACC,GAAG,CAAC,IAAI,CAACsG,aAAa,CAAC;IACzC,IAAIyG,eAAe,EAAE;MACjB9E,OAAO,CAAClI,SAAS,CAACC,GAAG,CAAC,IAAI,CAACkH,cAAc,CAACsF,GAAG,CAAC,CAAC;IACnD;IACAvE,OAAO,CAACwE,KAAK,CAACD,GAAG,CAAC,GAAG,GAAGM,QAAQ,IAAI;IACpC7E,OAAO,CAACwE,KAAK,CAACG,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAAC5E,OAAO,CAAC;IACzD,IAAI,IAAI,CAACvB,6BAA6B,EAAE;MACpCuB,OAAO,CAACwE,KAAK,CAACO,OAAO,IAAI,8CAA8C;IAC3E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,oBAAoBA,CAAC5E,OAAO,EAAE;IAC1B,MAAMgF,gBAAgB,GAAG;MACrBC,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACX,CAAC;IACD,IAAIT,MAAM,GAAG,CAAC;IACd;IACA;IACA;IACA,KAAK,MAAMJ,GAAG,IAAIrG,iBAAiB,EAAE;MACjC,IAAI8B,OAAO,CAACwE,KAAK,CAACD,GAAG,CAAC,EAAE;QACpBI,MAAM,IAAIK,gBAAgB,CAACT,GAAG,CAAC;MACnC;IACJ;IACA,OAAOI,MAAM,GAAG,GAAGA,MAAM,EAAE,GAAG,EAAE;EACpC;EACA;EACA/C,cAAcA,CAACpC,GAAG,EAAEa,qBAAqB,GAAG,IAAI,EAAE;IAC9C,IAAI,CAACA,qBAAqB,IAAI,IAAI,CAACgF,iBAAiB,CAACrL,MAAM,EAAE;MACzD,OAAO,IAAI,CAACqL,iBAAiB;IACjC;IACA,MAAM9D,UAAU,GAAG,EAAE;IACrB,MAAM+D,aAAa,GAAG9F,GAAG,CAACK,QAAQ;IAClC,KAAK,IAAI5O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqU,aAAa,CAACtL,MAAM,EAAE/I,CAAC,EAAE,EAAE;MAC3C,MAAM8F,IAAI,GAAGuO,aAAa,CAACrU,CAAC,CAAC;MAC7BsQ,UAAU,CAAC7H,IAAI,CAAC,IAAI,CAACuJ,oBAAoB,CAAClM,IAAI,CAAC,CAACoL,KAAK,CAAC;IAC1D;IACA,IAAI,CAACkD,iBAAiB,GAAG9D,UAAU;IACnC,OAAOA,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIM,8BAA8BA,CAAC0D,MAAM,EAAE/C,YAAY,EAAE;IACjD,MAAMgD,SAAS,GAAG,EAAE;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,IAAIxU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsU,MAAM,CAACvL,MAAM,EAAE/I,CAAC,EAAE,EAAE;MACpC,IAAIuR,YAAY,CAACvR,CAAC,CAAC,EAAE;QACjBuU,SAAS,CAACvU,CAAC,CAAC,GAAGwU,YAAY;QAC3BA,YAAY,IAAIF,MAAM,CAACtU,CAAC,CAAC;MAC7B;IACJ;IACA,OAAOuU,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;EACI1D,4BAA4BA,CAACyD,MAAM,EAAE/C,YAAY,EAAE;IAC/C,MAAMgD,SAAS,GAAG,EAAE;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,IAAIxU,CAAC,GAAGsU,MAAM,CAACvL,MAAM,EAAE/I,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpC,IAAIuR,YAAY,CAACvR,CAAC,CAAC,EAAE;QACjBuU,SAAS,CAACvU,CAAC,CAAC,GAAGwU,YAAY;QAC3BA,YAAY,IAAIF,MAAM,CAACtU,CAAC,CAAC;MAC7B;IACJ;IACA,OAAOuU,SAAS;EACpB;EACA;AACJ;AACA;AACA;EACIvC,oBAAoBA,CAACjD,OAAO,EAAE;IAC1B,MAAM0F,UAAU,GAAG,IAAI,CAACC,cAAc,CAACC,GAAG,CAAC5F,OAAO,CAAC;IACnD,IAAI0F,UAAU,EAAE;MACZ,OAAOA,UAAU;IACrB;IACA,MAAMG,UAAU,GAAG7F,OAAO,CAAC8F,qBAAqB,CAAC,CAAC;IAClD,MAAMC,IAAI,GAAG;MAAE5D,KAAK,EAAE0D,UAAU,CAAC1D,KAAK;MAAEa,MAAM,EAAE6C,UAAU,CAAC7C;IAAO,CAAC;IACnE,IAAI,CAAC,IAAI,CAACmB,eAAe,EAAE;MACvB,OAAO4B,IAAI;IACf;IACA,IAAI,CAACJ,cAAc,CAACK,GAAG,CAAChG,OAAO,EAAE+F,IAAI,CAAC;IACtC,IAAI,CAAC5B,eAAe,CAAC8B,OAAO,CAACjG,OAAO,EAAE;MAAEkG,GAAG,EAAE;IAAa,CAAC,CAAC;IAC5D,OAAOH,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIrE,8BAA8BA,CAACyE,MAAM,EAAE;IACnC,IAAI,CAAC7G,kCAAkC,CAAC6G,MAAM,CAAChH,IAAI,CAAC;IACpD;IACA,IAAI,CAAC,IAAI,CAAC8E,2BAA2B,EAAE;MACnC,IAAI,CAACmC,mCAAmC,CAAC1M,IAAI,CAACyM,MAAM,CAAC;IACzD;EACJ;EACA;EACA7G,kCAAkCA,CAACH,IAAI,EAAE;IACrC,MAAMkH,OAAO,GAAG,IAAIC,GAAG,CAACnH,IAAI,CAAC;IAC7B,KAAK,MAAMoH,MAAM,IAAI,IAAI,CAACH,mCAAmC,EAAE;MAC3DG,MAAM,CAACpH,IAAI,GAAGoH,MAAM,CAACpH,IAAI,CAACqH,MAAM,CAAChH,GAAG,IAAI,CAAC6G,OAAO,CAACI,GAAG,CAACjH,GAAG,CAAC,CAAC;IAC9D;IACA,IAAI,CAAC4G,mCAAmC,GAAG,IAAI,CAACA,mCAAmC,CAACI,MAAM,CAACD,MAAM,IAAI,CAAC,CAACA,MAAM,CAACpH,IAAI,CAACnF,MAAM,CAAC;EAC9H;EACA;EACAgF,kBAAkBA,CAACD,OAAO,EAAE;IACxB,IAAI2H,iBAAiB,GAAG,KAAK;IAC7B,KAAK,MAAMC,KAAK,IAAI5H,OAAO,EAAE;MAAA,IAAA6H,oBAAA,EAAAC,qBAAA;MACzB,MAAMC,QAAQ,GAAG,CAAAF,oBAAA,GAAAD,KAAK,CAACI,aAAa,cAAAH,oBAAA,eAAnBA,oBAAA,CAAqB5M,MAAM,GACtC;QACEmI,KAAK,EAAEwE,KAAK,CAACI,aAAa,CAAC,CAAC,CAAC,CAACC,UAAU;QACxChE,MAAM,EAAE2D,KAAK,CAACI,aAAa,CAAC,CAAC,CAAC,CAACE;MACnC,CAAC,GACC;QACE9E,KAAK,EAAEwE,KAAK,CAACO,WAAW,CAAC/E,KAAK;QAC9Ba,MAAM,EAAE2D,KAAK,CAACO,WAAW,CAAClE;MAC9B,CAAC;MACL,IAAI8D,QAAQ,CAAC3E,KAAK,OAAA0E,qBAAA,GAAK,IAAI,CAAClB,cAAc,CAACC,GAAG,CAACe,KAAK,CAACQ,MAAM,CAAC,cAAAN,qBAAA,uBAArCA,qBAAA,CAAuC1E,KAAK,KAC/DiF,MAAM,CAACT,KAAK,CAACQ,MAAM,CAAC,EAAE;QACtBT,iBAAiB,GAAG,IAAI;MAC5B;MACA,IAAI,CAACf,cAAc,CAACK,GAAG,CAACW,KAAK,CAACQ,MAAM,EAAEL,QAAQ,CAAC;IACnD;IACA,IAAIJ,iBAAiB,IAAI,IAAI,CAACN,mCAAmC,CAACpM,MAAM,EAAE;MACtE,IAAI,IAAI,CAACiK,2BAA2B,EAAE;QAClCC,YAAY,CAAC,IAAI,CAACD,2BAA2B,CAAC;MAClD;MACA,IAAI,CAACA,2BAA2B,GAAGoD,UAAU,CAAC,MAAM;QAChD,IAAI,IAAI,CAAChD,UAAU,EAAE;UACjB;QACJ;QACA,KAAK,MAAMkC,MAAM,IAAI,IAAI,CAACH,mCAAmC,EAAE;UAC3D,IAAI,CAAClG,mBAAmB,CAACqG,MAAM,CAACpH,IAAI,EAAEoH,MAAM,CAACpG,iBAAiB,EAAEoG,MAAM,CAACnG,eAAe,EAAE,IAAI,EAAE,KAAK,CAAC;QACxG;QACA,IAAI,CAACgG,mCAAmC,GAAG,EAAE;QAC7C,IAAI,CAACnC,2BAA2B,GAAG,IAAI;MAC3C,CAAC,EAAE,CAAC,CAAC;IACT;EACJ;EACA;AACJ;AACA;AACA;EACInE,gBAAgBA,CAACwH,IAAI,EAAE;IACnB,IAAI,IAAI,CAAC3I,cAAc,EAAE;MACrBtM,eAAe,CAACiV,IAAI,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAAC5I;MAAe,CAAC,CAAC;IAC5D,CAAC,MACI;MACD,IAAI,CAACJ,wBAAwB,CAAClF,QAAQ,CAAC,MAAM;QAAA,IAAAmO,eAAA;QACzC,CAAAA,eAAA,GAAAF,IAAI,CAAC3F,SAAS,cAAA6F,eAAA,eAAdA,eAAA,CAAAC,IAAA,CAAAH,IAAiB,CAAC;QAClBA,IAAI,CAACvH,KAAK,CAAC,CAAC;MAChB,CAAC,CAAC;IACN;EACJ;AACJ;AACA,SAASqH,MAAMA,CAACpH,OAAO,EAAE;EACrB,OAAO,CAAC,UAAU,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAACO,IAAI,CAACmH,KAAK,IAAI1H,OAAO,CAAClI,SAAS,CAACwM,QAAQ,CAACoD,KAAK,CAAC,CAAC;AAC9G;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACC,EAAE,EAAE;EACpC,OAAOC,KAAK,CAAC,kCAAkCD,EAAE,IAAI,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA,SAASE,gCAAgCA,CAAC9W,IAAI,EAAE;EAC5C,OAAO6W,KAAK,CAAC,+CAA+C7W,IAAI,IAAI,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA,SAAS+W,mCAAmCA,CAAA,EAAG;EAC3C,OAAOF,KAAK,CAAC,sEAAsE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,SAASG,kCAAkCA,CAACC,IAAI,EAAE;EAC9C,OAAOJ,KAAK,CAAC,kDAAkD,GAC3D,sBAAsBK,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,EAAE,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,SAASG,2BAA2BA,CAAA,EAAG;EACnC,OAAOP,KAAK,CAAC,mDAAmD,GAC5D,oDAAoD,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA,SAASQ,8BAA8BA,CAAA,EAAG;EACtC,OAAOR,KAAK,CAAC,wEAAwE,CAAC;AAC1F;AACA;AACA;AACA;AACA;AACA,SAASS,yCAAyCA,CAAA,EAAG;EACjD,OAAOT,KAAK,CAAC,6DAA6D,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA,SAASU,kCAAkCA,CAAA,EAAG;EAC1C,OAAOV,KAAK,CAAC,qCAAqC,CAAC;AACvD;;AAEA;AACA,MAAMW,2BAA2B,GAAG,IAAIlX,cAAc,CAAC,SAAS,CAAC;;AAEjE;AACA;AACA;AACA;AACA,MAAMmX,cAAc,CAAC;AAGpBC,eAAA,GAHKD,cAAc;AAAApU,eAAA,CAAdoU,cAAc,wBAAAE,wBAAAnU,iBAAA;EAAA,YAAAA,iBAAA,IACmFiU,eAAc;AAAA;AAAApU,eAAA,CAD/GoU,cAAc,8BA5nC6D/Y,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EA8nCQ+T,eAAc;EAAA9T,SAAA;EAAAyC,QAAA,GA9nCxB1H,EAAE,CAAA2H,kBAAA,CA8nC0H,CAAC;IAAEC,OAAO,EAAElE,uBAAuB;IAAEwV,QAAQ,EAAEtV;EAA6B,CAAC,CAAC;AAAA;AAE3R;EAAA,QAAAsB,SAAA,oBAAAA,SAAA,KAhoCiFlF,EAAE,CAAAmF,iBAAA,CAgoCQ4T,cAAc,EAAc,CAAC;IAC5G/T,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uDAAuD;MACjEyC,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAElE,uBAAuB;QAAEwV,QAAQ,EAAEtV;MAA6B,CAAC;IAC5F,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMuV,aAAa,CAAC;EAGhBzU,WAAWA,CAAA,EAAG;IAAAC,eAAA,wBAFE9C,MAAM,CAACU,gBAAgB,CAAC;IAAAoC,eAAA,qBAC3B9C,MAAM,CAACM,UAAU,CAAC;IAE3B,MAAMiX,KAAK,GAAGvX,MAAM,CAAC0C,SAAS,CAAC;IAC/B6U,KAAK,CAACC,UAAU,GAAG,IAAI;IACvBD,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;AAGJ;AAACC,cAAA,GAVKJ,aAAa;AAAAxU,eAAA,CAAbwU,aAAa,wBAAAK,uBAAA1U,iBAAA;EAAA,YAAAA,iBAAA,IAQoFqU,cAAa;AAAA;AAAAxU,eAAA,CAR9GwU,aAAa,8BA3oC8DnZ,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAopCQmU,cAAa;EAAAlU,SAAA;AAAA;AAExG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtpCiFlF,EAAE,CAAAmF,iBAAA,CAspCQgU,aAAa,EAAc,CAAC;IAC3GnU,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMoU,eAAe,CAAC;EAGlB/U,WAAWA,CAAA,EAAG;IAAAC,eAAA,wBAFE9C,MAAM,CAACU,gBAAgB,CAAC;IAAAoC,eAAA,qBAC3B9C,MAAM,CAACM,UAAU,CAAC;IAE3B,MAAMiX,KAAK,GAAGvX,MAAM,CAAC0C,SAAS,CAAC;IAC/B6U,KAAK,CAACM,gBAAgB,GAAG,IAAI;IAC7BN,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;AAGJ;AAACK,gBAAA,GAVKF,eAAe;AAAA9U,eAAA,CAAf8U,eAAe,wBAAAG,yBAAA9U,iBAAA;EAAA,YAAAA,iBAAA,IAQkF2U,gBAAe;AAAA;AAAA9U,eAAA,CARhH8U,eAAe,8BAhqC4DzZ,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAyqCQyU,gBAAe;EAAAxU,SAAA;AAAA;AAE1G;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3qCiFlF,EAAE,CAAAmF,iBAAA,CA2qCQsU,eAAe,EAAc,CAAC;IAC7GzU,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMwU,eAAe,CAAC;EAGlBnV,WAAWA,CAAA,EAAG;IAAAC,eAAA,wBAFE9C,MAAM,CAACU,gBAAgB,CAAC;IAAAoC,eAAA,qBAC3B9C,MAAM,CAACM,UAAU,CAAC;IAE3B,MAAMiX,KAAK,GAAGvX,MAAM,CAAC0C,SAAS,CAAC;IAC/B6U,KAAK,CAACU,gBAAgB,GAAG,IAAI;IAC7BV,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;AAGJ;AAACS,gBAAA,GAVKF,eAAe;AAAAlV,eAAA,CAAfkV,eAAe,wBAAAG,yBAAAlV,iBAAA;EAAA,YAAAA,iBAAA,IAQkF+U,gBAAe;AAAA;AAAAlV,eAAA,CARhHkV,eAAe,8BArrC4D7Z,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EA8rCQ6U,gBAAe;EAAA5U,SAAA;AAAA;AAE1G;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhsCiFlF,EAAE,CAAAmF,iBAAA,CAgsCQ0U,eAAe,EAAc,CAAC;IAC7G7U,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA,MAAM4U,eAAe,CAAC;EAGlBvV,WAAWA,CAAA,EAAG;IAAAC,eAAA,wBAFE9C,MAAM,CAACU,gBAAgB,CAAC;IAAAoC,eAAA,qBAC3B9C,MAAM,CAACM,UAAU,CAAC;IAE3B,MAAMiX,KAAK,GAAGvX,MAAM,CAAC0C,SAAS,CAAC;IAC/B6U,KAAK,CAACc,gBAAgB,GAAG,IAAI;IAC7Bd,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;AAGJ;AAACa,gBAAA,GAVKF,eAAe;AAAAtV,eAAA,CAAfsV,eAAe,wBAAAG,yBAAAtV,iBAAA;EAAA,YAAAA,iBAAA,IAQkFmV,gBAAe;AAAA;AAAAtV,eAAA,CARhHsV,eAAe,8BA3sC4Dja,EAAE,CAAA+E,iBAAA;EAAAC,IAAA,EAotCQiV,gBAAe;EAAAhV,SAAA;AAAA;AAE1G;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAttCiFlF,EAAE,CAAAmF,iBAAA,CAstCQ8U,eAAe,EAAc,CAAC;IAC7GjV,IAAI,EAAEjD,SAAS;IACfqD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA,MAAMgV,kBAAkB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,CAAC;EAqIX;EACAtR,YAAYA,CAAA,EAAG;IACX;IACA,IAAI,IAAI,CAACuR,iBAAiB,KAAKC,SAAS,EAAE;MACtC;MACA;MACA,MAAMC,SAAS,GAAG,IAAI,CAACC,WAAW,CAACvS,aAAa,CAACwS,YAAY,CAAC,MAAM,CAAC;MACrE,OAAOF,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,UAAU,GAAG,UAAU,GAAG,MAAM;IACjF;IACA,OAAO,IAAI,CAACF,iBAAiB;EACjC;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIK,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,OAAOA,CAACE,EAAE,EAAE;IACZ,IAAI,CAAC,OAAO5V,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK4V,EAAE,IAAI,IAAI,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;MAC3FC,OAAO,CAACC,IAAI,CAAC,4CAA4CxC,IAAI,CAACC,SAAS,CAACqC,EAAE,CAAC,GAAG,CAAC;IACnF;IACA,IAAI,CAACD,UAAU,GAAGC,EAAE;EACxB;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,IAAI,CAACC,WAAW,KAAKD,UAAU,EAAE;MACjC,IAAI,CAACE,iBAAiB,CAACF,UAAU,CAAC;IACtC;EACJ;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIG,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,sBAAsB;EACtC;EACA,IAAID,qBAAqBA,CAACnV,KAAK,EAAE;IAC7B,IAAI,CAACoV,sBAAsB,GAAGpV,KAAK;IACnC;IACA;IACA,IAAI,IAAI,CAACoT,UAAU,IAAI,IAAI,CAACA,UAAU,CAACiC,aAAa,CAAChR,MAAM,EAAE;MACzD,IAAI,CAACiR,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,wBAAwB,CAAC,CAAC;IACnC;EACJ;EAEA;AACJ;AACA;AACA;EACI,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACxV,KAAK,EAAE;IACnB,IAAI,CAACyV,YAAY,GAAGzV,KAAK;IACzB;IACA,IAAI,CAAC0V,2BAA2B,GAAG,IAAI;IACvC,IAAI,CAACC,4BAA4B,GAAG,IAAI;EAC5C;EAsCAlX,WAAWA,CAAA,EAAG;IAAAC,eAAA,mBAlQH9C,MAAM,CAACS,eAAe,CAAC;IAAAqC,eAAA,6BACb9C,MAAM,CAACe,iBAAiB,CAAC;IAAA+B,eAAA,sBAChC9C,MAAM,CAACM,UAAU,CAAC;IAAAwC,eAAA,eACzB9C,MAAM,CAACmC,cAAc,EAAE;MAAEqC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA1B,eAAA,oBACrC9C,MAAM,CAACqC,QAAQ,CAAC;IAAAS,eAAA,wBACZ9C,MAAM,CAAC6B,uBAAuB,CAAC;IAAAiB,eAAA,mCACpB9C,MAAM,CAAC4H,0BAA0B,CAAC;IAAA9E,eAAA,yBAC5C9C,MAAM,CAACsC,aAAa,CAAC;IAAAQ,eAAA,qCACT9C,MAAM,CAACiX,2BAA2B,EAAE;MAAEzS,QAAQ,EAAE,IAAI;MAAEwV,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAlX,eAAA,oBACxF9C,MAAM,CAACF,QAAQ,CAAC;IAC5B;IAAAgD,eAAA;IAEA;IAAAA,eAAA,qBACa,IAAIvB,OAAO,CAAC,CAAC;IAC1B;IAAAuB,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;IAJIA,eAAA,4BAKoB,IAAImX,GAAG,CAAC,CAAC;IAC7B;AACJ;AACA;AACA;IAHInX,eAAA;IAKA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;IAJIA,eAAA,4BAKoB,IAAIiS,GAAG,CAAC,CAAC;IAC7B;AACJ;AACA;AACA;AACA;IAJIjS,eAAA,yBAKiB,IAAIiS,GAAG,CAAC,CAAC;IAC1B;AACJ;AACA;AACA;AACA;IAJIjS,eAAA,+BAKuB,IAAIiS,GAAG,CAAC,CAAC;IAChC;AACJ;AACA;AACA;AACA;IAJIjS,eAAA,+BAKuB,IAAIiS,GAAG,CAAC,CAAC;IAChC;IAAAjS,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA,+BAIuB,IAAI;IAC3B;AACJ;AACA;AACA;IAHIA,eAAA,+BAIuB,IAAI;IAC3B;AACJ;AACA;AACA;IAHIA,eAAA,uCAI+B,IAAI;IACnC;AACJ;AACA;AACA;AACA;IAJIA,eAAA,sCAK8B,IAAI;IAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAZIA,eAAA,+BAauB,IAAImX,GAAG,CAAC,CAAC;IAChC;IAAAnX,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA,yBAIiB,kBAAkB;IACnC;AACJ;AACA;AACA;AACA;IAJIA,eAAA,uCAK+B,IAAI;IACnC;IAAAA,eAAA;IAEA;IAAAA,eAAA,8BACsB,KAAK;IAC3B;IAAAA,eAAA,yBACiB,KAAK;IACtB;IAAAA,eAAA,0BACkB,KAAK;IAAAA,eAAA,4BAYH6V,SAAS;IAAA7V,eAAA;IAAAA,eAAA;IAAAA,eAAA,iCAgEJ,KAAK;IAAAA,eAAA,uBAcf,KAAK;IACpB;AACJ;AACA;AACA;IAHIA,eAAA,yBAIiB,IAAI9B,YAAY,CAAC,CAAC;IACnC;IACA;IACA;AACJ;AACA;AACA;AACA;AACA;IALI8B,eAAA,qBAMa,IAAItB,eAAe,CAAC;MAC7BkO,KAAK,EAAE,CAAC;MACRC,GAAG,EAAEuK,MAAM,CAACC;IAChB,CAAC,CAAC;IACF;IAAArX,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAAAA,eAAA,oBAEY9C,MAAM,CAACiB,QAAQ,CAAC;IAExB,MAAMgG,IAAI,GAAGjH,MAAM,CAAC,IAAIkB,kBAAkB,CAAC,MAAM,CAAC,EAAE;MAAEsD,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,IAAI,CAACyC,IAAI,EAAE;MACP,IAAI,CAAC4R,WAAW,CAACvS,aAAa,CAACc,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC;IAChE;IACA,IAAI,CAACgT,SAAS,GAAG,CAAC,IAAI,CAACC,SAAS,CAACC,SAAS;IAC1C,IAAI,CAACzN,kBAAkB,GAAG,IAAI,CAACgM,WAAW,CAACvS,aAAa,CAACiU,QAAQ,KAAK,OAAO;IAC7E;IACA;IACA;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAACjR,QAAQ,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAACgR,EAAE,EAAEC,OAAO,KAAK;MAC9D,OAAO,IAAI,CAAC3B,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC2B,OAAO,CAACC,SAAS,EAAED,OAAO,CAAChE,IAAI,CAAC,GAAGgE,OAAO;IACjF,CAAC,CAAC;EACN;EACAE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,cAAc,CACdC,MAAM,CAAC,CAAC,CACRC,IAAI,CAACrZ,SAAS,CAAC,IAAI,CAACsZ,UAAU,CAAC,CAAC,CAChCC,SAAS,CAAC,MAAM;MACjB,IAAI,CAACpB,2BAA2B,GAAG,IAAI;IAC3C,CAAC,CAAC;EACN;EACAqB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,eAAe,GAAG,IAAI;EAC/B;EACAC,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACC,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB;EACJ;EACAxQ,WAAWA,CAAA,EAAG;IAAA,IAAAyQ,mBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACV,CAAAH,mBAAA,OAAI,CAACI,aAAa,cAAAJ,mBAAA,eAAlBA,mBAAA,CAAoBhJ,OAAO,CAAC,CAAC;IAC7B,EAAAiJ,gBAAA,GACI,IAAI,CAACjE,UAAU,cAAAiE,gBAAA,uBAAfA,gBAAA,CAAiBhC,aAAa,GAAAiC,qBAAA,GAC9B,IAAI,CAAC7D,gBAAgB,cAAA6D,qBAAA,uBAArBA,qBAAA,CAAuBjC,aAAa,GAAAkC,qBAAA,GACpC,IAAI,CAAC1D,gBAAgB,cAAA0D,qBAAA,uBAArBA,qBAAA,CAAuBlC,aAAa,EACpC,IAAI,CAACoC,oBAAoB,EACzB,IAAI,CAACC,iBAAiB,EACtB,IAAI,CAACC,cAAc,EACnB,IAAI,CAACC,oBAAoB,EACzB,IAAI,CAACC,oBAAoB,EACzB,IAAI,CAACC,iBAAiB,CACzB,CAACC,OAAO,CAAEC,GAAG,IAAK;MACfA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEC,KAAK,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACvB,UAAU,CAACwB,IAAI,CAAC,CAAC;IACtB,IAAI,CAACxB,UAAU,CAACyB,QAAQ,CAAC,CAAC;IAC1B,IAAI/c,YAAY,CAAC,IAAI,CAACyZ,UAAU,CAAC,EAAE;MAC/B,IAAI,CAACA,UAAU,CAACvG,UAAU,CAAC,IAAI,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI8J,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC3C,MAAM1T,OAAO,GAAG,IAAI,CAACqR,WAAW,CAAC9Q,IAAI,CAAC,IAAI,CAACkT,WAAW,CAAC;IACvD,IAAI,CAACzT,OAAO,EAAE;MACV,IAAI,CAAC2T,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,cAAc,CAACN,IAAI,CAAC,CAAC;MAC1B;IACJ;IACA,MAAMhD,aAAa,GAAG,IAAI,CAACjC,UAAU,CAACiC,aAAa;IACnD,IAAI,CAACuD,aAAa,CAACC,YAAY,CAAC9T,OAAO,EAAEsQ,aAAa,EAAE,CAACyD,MAAM,EAAEC,sBAAsB,EAAEC,YAAY,KAAK,IAAI,CAACC,oBAAoB,CAACH,MAAM,CAACI,IAAI,EAAEF,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAACI,IAAI,CAAC5G,IAAI,EAAGqE,MAAM,IAAK;MACpM,IAAIA,MAAM,CAACwC,SAAS,KAAKtb,sBAAsB,CAACub,QAAQ,IAAIzC,MAAM,CAAC0C,OAAO,EAAE;QACxE,IAAI,CAACC,0BAA0B,CAAC3C,MAAM,CAACmC,MAAM,CAACI,IAAI,CAACK,MAAM,EAAE5C,MAAM,CAAC0C,OAAO,CAAC;MAC9E;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACG,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACAzU,OAAO,CAAC0U,qBAAqB,CAAEX,MAAM,IAAK;MACtC,MAAMY,OAAO,GAAGrE,aAAa,CAACpF,GAAG,CAAC6I,MAAM,CAACE,YAAY,CAAC;MACtDU,OAAO,CAACL,OAAO,CAACle,SAAS,GAAG2d,MAAM,CAACI,IAAI,CAAC5G,IAAI;IAChD,CAAC,CAAC;IACF,IAAI,CAACoG,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,cAAc,CAACN,IAAI,CAAC,CAAC;IAC1B,IAAI,CAAC9C,wBAAwB,CAAC,CAAC;EACnC;EACA;EACAoE,YAAYA,CAAC3X,SAAS,EAAE;IACpB,IAAI,CAAC0V,iBAAiB,CAACtV,GAAG,CAACJ,SAAS,CAAC;EACzC;EACA;EACA4X,eAAeA,CAAC5X,SAAS,EAAE;IACvB,IAAI,CAAC0V,iBAAiB,CAACmC,MAAM,CAAC7X,SAAS,CAAC;EAC5C;EACA;EACA8X,SAASA,CAACP,MAAM,EAAE;IACd,IAAI,CAAC5B,cAAc,CAACvV,GAAG,CAACmX,MAAM,CAAC;EACnC;EACA;EACAQ,YAAYA,CAACR,MAAM,EAAE;IACjB,IAAI,CAAC5B,cAAc,CAACkC,MAAM,CAACN,MAAM,CAAC;EACtC;EACA;EACAS,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAACrC,oBAAoB,CAACxV,GAAG,CAAC6X,YAAY,CAAC;IAC3C,IAAI,CAACC,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAC,kBAAkBA,CAACF,YAAY,EAAE;IAC7B,IAAI,CAACrC,oBAAoB,CAACiC,MAAM,CAACI,YAAY,CAAC;IAC9C,IAAI,CAACC,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAE,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAACxC,oBAAoB,CAACzV,GAAG,CAACiY,YAAY,CAAC;IAC3C,IAAI,CAACC,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAC,kBAAkBA,CAACF,YAAY,EAAE;IAC7B,IAAI,CAACxC,oBAAoB,CAACgC,MAAM,CAACQ,YAAY,CAAC;IAC9C,IAAI,CAACC,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAE,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAGD,SAAS;EACrC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACpH,gBAAgB,CAAC;IAC/D;IACA;IACA;IACA,IAAI,IAAI,CAAChL,kBAAkB,EAAE;MACzB,MAAMqS,KAAK,GAAGC,mBAAmB,CAAC,IAAI,CAACtH,gBAAgB,EAAE,OAAO,CAAC;MACjE,IAAIqH,KAAK,EAAE;QACPA,KAAK,CAACjM,KAAK,CAACmM,OAAO,GAAGJ,UAAU,CAACvW,MAAM,GAAG,EAAE,GAAG,MAAM;MACzD;IACJ;IACA,MAAMwI,YAAY,GAAG,IAAI,CAACqL,cAAc,CAAC3L,GAAG,CAACyL,GAAG,IAAIA,GAAG,CAAClY,MAAM,CAAC;IAC/D,IAAI,CAAC0X,aAAa,CAACjO,sBAAsB,CAACqR,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC;IAC9D,IAAI,CAACpD,aAAa,CAAC7K,SAAS,CAACiO,UAAU,EAAE/N,YAAY,EAAE,KAAK,CAAC;IAC7D;IACA,IAAI,CAACqL,cAAc,CAACH,OAAO,CAACC,GAAG,IAAIA,GAAG,CAAC1X,kBAAkB,CAAC,CAAC,CAAC;EAChE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2a,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAAC,IAAI,CAAChH,gBAAgB,CAAC;IAC/D;IACA;IACA;IACA,IAAI,IAAI,CAACpL,kBAAkB,EAAE;MACzB,MAAMyF,KAAK,GAAG6M,mBAAmB,CAAC,IAAI,CAAClH,gBAAgB,EAAE,OAAO,CAAC;MACjE,IAAI3F,KAAK,EAAE;QACPA,KAAK,CAACW,KAAK,CAACmM,OAAO,GAAGE,UAAU,CAAC7W,MAAM,GAAG,EAAE,GAAG,MAAM;MACzD;IACJ;IACA,MAAMwI,YAAY,GAAG,IAAI,CAACsL,cAAc,CAAC5L,GAAG,CAACyL,GAAG,IAAIA,GAAG,CAAClY,MAAM,CAAC;IAC/D,IAAI,CAAC0X,aAAa,CAACjO,sBAAsB,CAAC2R,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;IACjE,IAAI,CAAC1D,aAAa,CAAC7K,SAAS,CAACuO,UAAU,EAAErO,YAAY,EAAE,QAAQ,CAAC;IAChE,IAAI,CAAC2K,aAAa,CAACxJ,2BAA2B,CAAC,IAAI,CAACyG,WAAW,CAACvS,aAAa,EAAE2K,YAAY,CAAC;IAC5F;IACA,IAAI,CAACsL,cAAc,CAACJ,OAAO,CAACC,GAAG,IAAIA,GAAG,CAAC1X,kBAAkB,CAAC,CAAC,CAAC;EAChE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiV,wBAAwBA,CAAA,EAAG;IACvB,MAAMqF,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACpH,gBAAgB,CAAC;IAC/D,MAAM0H,QAAQ,GAAG,IAAI,CAACN,gBAAgB,CAAC,IAAI,CAACzH,UAAU,CAAC;IACvD,MAAM8H,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAAC,IAAI,CAAChH,gBAAgB,CAAC;IAC/D;IACA;IACA;IACA;IACA,IAAK,IAAI,CAACpL,kBAAkB,IAAI,CAAC,IAAI,CAACgN,YAAY,IAAK,IAAI,CAACE,4BAA4B,EAAE;MACtF;MACA;MACA,IAAI,CAAC6B,aAAa,CAACjO,sBAAsB,CAAC,CAAC,GAAGqR,UAAU,EAAE,GAAGO,QAAQ,EAAE,GAAGD,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;MACzG,IAAI,CAACvF,4BAA4B,GAAG,KAAK;IAC7C;IACA;IACAiF,UAAU,CAAC7C,OAAO,CAAC,CAACqD,SAAS,EAAE9f,CAAC,KAAK;MACjC,IAAI,CAAC+f,sBAAsB,CAAC,CAACD,SAAS,CAAC,EAAE,IAAI,CAAClD,cAAc,CAAC5c,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IACF;IACA,IAAI,CAACggB,QAAQ,CAACvD,OAAO,CAACwB,MAAM,IAAI;MAC5B;MACA,MAAM/P,IAAI,GAAG,EAAE;MACf,KAAK,IAAIlO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6f,QAAQ,CAAC9W,MAAM,EAAE/I,CAAC,EAAE,EAAE;QACtC,IAAI,IAAI,CAACkd,WAAW,CAACld,CAAC,CAAC,CAACie,MAAM,KAAKA,MAAM,EAAE;UACvC/P,IAAI,CAACzF,IAAI,CAACoX,QAAQ,CAAC7f,CAAC,CAAC,CAAC;QAC1B;MACJ;MACA,IAAI,CAAC+f,sBAAsB,CAAC7R,IAAI,EAAE+P,MAAM,CAAC;IAC7C,CAAC,CAAC;IACF;IACA2B,UAAU,CAACnD,OAAO,CAAC,CAACwD,SAAS,EAAEjgB,CAAC,KAAK;MACjC,IAAI,CAAC+f,sBAAsB,CAAC,CAACE,SAAS,CAAC,EAAE,IAAI,CAACpD,cAAc,CAAC7c,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IACF;IACA0O,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC6N,iBAAiB,CAAC0D,MAAM,CAAC,CAAC,CAAC,CAACzD,OAAO,CAACC,GAAG,IAAIA,GAAG,CAAC1X,kBAAkB,CAAC,CAAC,CAAC;EACxF;EACA;EACA+S,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACoI,cAAc,IACpB,IAAI,CAACrI,UAAU,IACf,IAAI,CAACK,gBAAgB,IACrB,IAAI,CAACI,gBAAgB,IACrB,IAAI,CAACI,gBAAgB,EAAE;MACvB,IAAI,CAACwH,cAAc,GAAG,IAAI;MAC1B;MACA;MACA,IAAI,IAAI,CAACvE,UAAU,CAAC,CAAC,EAAE;QACnB,IAAI,CAACC,OAAO,CAAC,CAAC;MAClB;IACJ;EACJ;EACA;EACAD,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACuE,cAAc,IAAI,IAAI,CAACzE,eAAe;EACtD;EACA;EACAG,OAAOA,CAAA,EAAG;IACN;IACA,IAAI,CAACuE,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC,IAAI,CAACzD,cAAc,CAAC7T,MAAM,IAC3B,CAAC,IAAI,CAAC8T,cAAc,CAAC9T,MAAM,IAC3B,CAAC,IAAI,CAACiX,QAAQ,CAACjX,MAAM,KACpB,OAAOpF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMwT,2BAA2B,CAAC,CAAC;IACvC;IACA;IACA,MAAMmJ,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACnD,MAAMC,cAAc,GAAGF,cAAc,IAAI,IAAI,CAAC1B,oBAAoB,IAAI,IAAI,CAACI,oBAAoB;IAC/F;IACA,IAAI,CAAC3E,4BAA4B,GAAG,IAAI,CAACA,4BAA4B,IAAImG,cAAc;IACvF,IAAI,CAACpG,2BAA2B,GAAGoG,cAAc;IACjD;IACA,IAAI,IAAI,CAAC5B,oBAAoB,EAAE;MAC3B,IAAI,CAAC6B,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAAC7B,oBAAoB,GAAG,KAAK;IACrC;IACA;IACA,IAAI,IAAI,CAACI,oBAAoB,EAAE;MAC3B,IAAI,CAAC0B,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAAC1B,oBAAoB,GAAG,KAAK;IACrC;IACA;IACA;IACA,IAAI,IAAI,CAACtF,UAAU,IAAI,IAAI,CAACsG,QAAQ,CAACjX,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC4X,yBAAyB,EAAE;MAChF,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChC,CAAC,MACI,IAAI,IAAI,CAACvG,4BAA4B,EAAE;MACxC;MACA;MACA,IAAI,CAACJ,wBAAwB,CAAC,CAAC;IACnC;IACA,IAAI,CAAC4G,kBAAkB,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACI1D,iBAAiBA,CAAA,EAAG;IAChB,MAAMF,UAAU,GAAG,EAAE;IACrB;IACA;IACA,MAAM6D,oBAAoB,GAAG,IAAI,CAAC3E,oBAAoB;IACtD,IAAI,CAACA,oBAAoB,GAAG,IAAI5B,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC,IAAI,CAACwG,KAAK,EAAE;MACb,OAAO9D,UAAU;IACrB;IACA;IACA;IACA,KAAK,IAAIjd,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC+gB,KAAK,CAAChY,MAAM,EAAE/I,CAAC,EAAE,EAAE;MACxC,IAAIgX,IAAI,GAAG,IAAI,CAAC+J,KAAK,CAAC/gB,CAAC,CAAC;MACxB,MAAMghB,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,CAACjK,IAAI,EAAEhX,CAAC,EAAE8gB,oBAAoB,CAACnM,GAAG,CAACqC,IAAI,CAAC,CAAC;MAC7F,IAAI,CAAC,IAAI,CAACmF,oBAAoB,CAAC3G,GAAG,CAACwB,IAAI,CAAC,EAAE;QACtC,IAAI,CAACmF,oBAAoB,CAACpH,GAAG,CAACiC,IAAI,EAAE,IAAIrJ,OAAO,CAAC,CAAC,CAAC;MACtD;MACA,KAAK,IAAIuT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,iBAAiB,CAACjY,MAAM,EAAEmY,CAAC,EAAE,EAAE;QAC/C,IAAIC,SAAS,GAAGH,iBAAiB,CAACE,CAAC,CAAC;QACpC,MAAME,KAAK,GAAG,IAAI,CAACjF,oBAAoB,CAACxH,GAAG,CAACwM,SAAS,CAACnK,IAAI,CAAC;QAC3D,IAAIoK,KAAK,CAAC5L,GAAG,CAAC2L,SAAS,CAAClD,MAAM,CAAC,EAAE;UAC7BmD,KAAK,CAACzM,GAAG,CAACwM,SAAS,CAAClD,MAAM,CAAC,CAACxV,IAAI,CAAC0Y,SAAS,CAAC;QAC/C,CAAC,MACI;UACDC,KAAK,CAACrM,GAAG,CAACoM,SAAS,CAAClD,MAAM,EAAE,CAACkD,SAAS,CAAC,CAAC;QAC5C;QACAlE,UAAU,CAACxU,IAAI,CAAC0Y,SAAS,CAAC;MAC9B;IACJ;IACA,OAAOlE,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIgE,qBAAqBA,CAACjK,IAAI,EAAEiE,SAAS,EAAEmG,KAAK,EAAE;IAC1C,MAAMC,OAAO,GAAG,IAAI,CAACC,WAAW,CAACtK,IAAI,EAAEiE,SAAS,CAAC;IACjD,OAAOoG,OAAO,CAACpQ,GAAG,CAACgN,MAAM,IAAI;MACzB,MAAMsD,gBAAgB,GAAGH,KAAK,IAAIA,KAAK,CAAC5L,GAAG,CAACyI,MAAM,CAAC,GAAGmD,KAAK,CAACzM,GAAG,CAACsJ,MAAM,CAAC,GAAG,EAAE;MAC5E,IAAIsD,gBAAgB,CAACxY,MAAM,EAAE;QACzB,MAAMiS,OAAO,GAAGuG,gBAAgB,CAACC,KAAK,CAAC,CAAC;QACxCxG,OAAO,CAACC,SAAS,GAAGA,SAAS;QAC7B,OAAOD,OAAO;MAClB,CAAC,MACI;QACD,OAAO;UAAEhE,IAAI;UAAEiH,MAAM;UAAEhD;QAAU,CAAC;MACtC;IACJ,CAAC,CAAC;EACN;EACA;EACAoF,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC7D,iBAAiB,CAACG,KAAK,CAAC,CAAC;IAC9B,MAAM8E,UAAU,GAAGC,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,kBAAkB,CAAC,EAAE,IAAI,CAACxF,iBAAiB,CAAC;IACtGqF,UAAU,CAAChF,OAAO,CAAC/V,SAAS,IAAI;MAC5B,IAAI,IAAI,CAAC8V,iBAAiB,CAAChH,GAAG,CAAC9O,SAAS,CAAC3G,IAAI,CAAC,KACzC,OAAO4D,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACjD,MAAMkT,gCAAgC,CAACnQ,SAAS,CAAC3G,IAAI,CAAC;MAC1D;MACA,IAAI,CAACyc,iBAAiB,CAACzH,GAAG,CAACrO,SAAS,CAAC3G,IAAI,EAAE2G,SAAS,CAAC;IACzD,CAAC,CAAC;EACN;EACA;EACA0Z,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACxD,cAAc,GAAG8E,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACE,qBAAqB,CAAC,EAAE,IAAI,CAACvF,oBAAoB,CAAC;IAC/G,IAAI,CAACO,cAAc,GAAG6E,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACG,qBAAqB,CAAC,EAAE,IAAI,CAACvF,oBAAoB,CAAC;IAC/G,IAAI,CAACyD,QAAQ,GAAG0B,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACI,eAAe,CAAC,EAAE,IAAI,CAAC1F,cAAc,CAAC;IAC7F;IACA,MAAM2F,cAAc,GAAG,IAAI,CAAChC,QAAQ,CAACzK,MAAM,CAACmH,GAAG,IAAI,CAACA,GAAG,CAACxR,IAAI,CAAC;IAC7D,IAAI,CAAC,IAAI,CAAC2O,qBAAqB,IAC3BmI,cAAc,CAACjZ,MAAM,GAAG,CAAC,KACxB,OAAOpF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMmT,mCAAmC,CAAC,CAAC;IAC/C;IACA,IAAI,CAACgG,cAAc,GAAGkF,cAAc,CAAC,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACIzB,qBAAqBA,CAAA,EAAG;IACpB,MAAM0B,kBAAkB,GAAGA,CAACC,GAAG,EAAExF,GAAG,KAAK;MACrC;MACA;MACA,MAAM1S,IAAI,GAAG,CAAC,CAAC0S,GAAG,CAACzS,cAAc,CAAC,CAAC;MACnC,OAAOiY,GAAG,IAAIlY,IAAI;IACtB,CAAC;IACD;IACA,MAAMmY,kBAAkB,GAAG,IAAI,CAACnC,QAAQ,CAACoC,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAC1E,IAAIE,kBAAkB,EAAE;MACpB,IAAI,CAACnI,oBAAoB,CAAC,CAAC;IAC/B;IACA;IACA,MAAMqI,oBAAoB,GAAG,IAAI,CAACzF,cAAc,CAACwF,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAClF,IAAII,oBAAoB,EAAE;MACtB,IAAI,CAAC5B,sBAAsB,CAAC,CAAC;IACjC;IACA,MAAM6B,oBAAoB,GAAG,IAAI,CAACzF,cAAc,CAACuF,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAClF,IAAIK,oBAAoB,EAAE;MACtB,IAAI,CAAC5B,sBAAsB,CAAC,CAAC;IACjC;IACA,OAAOyB,kBAAkB,IAAIE,oBAAoB,IAAIC,oBAAoB;EAC7E;EACA;AACJ;AACA;AACA;AACA;EACI1I,iBAAiBA,CAACF,UAAU,EAAE;IAC1B,IAAI,CAACqH,KAAK,GAAG,EAAE;IACf,IAAI9gB,YAAY,CAAC,IAAI,CAACyZ,UAAU,CAAC,EAAE;MAC/B,IAAI,CAACA,UAAU,CAACvG,UAAU,CAAC,IAAI,CAAC;IACpC;IACA;IACA,IAAI,IAAI,CAACwN,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAAC4B,WAAW,CAAC,CAAC;MAC5C,IAAI,CAAC5B,yBAAyB,GAAG,IAAI;IACzC;IACA,IAAI,CAACjH,UAAU,EAAE;MACb,IAAI,IAAI,CAACoB,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAAC9Q,IAAI,CAAC,EAAE,CAAC;MAC7B;MACA,IAAI,IAAI,CAAC8N,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACiC,aAAa,CAAC4C,KAAK,CAAC,CAAC;MACzC;IACJ;IACA,IAAI,CAAChD,WAAW,GAAGD,UAAU;EACjC;EACA;EACAkH,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,CAAC,IAAI,CAAClH,UAAU,EAAE;MAClB;IACJ;IACA,IAAI8I,UAAU;IACd,IAAIviB,YAAY,CAAC,IAAI,CAACyZ,UAAU,CAAC,EAAE;MAC/B8I,UAAU,GAAG,IAAI,CAAC9I,UAAU,CAAC+I,OAAO,CAAC,IAAI,CAAC;IAC9C,CAAC,MACI,IAAI1gB,YAAY,CAAC,IAAI,CAAC2X,UAAU,CAAC,EAAE;MACpC8I,UAAU,GAAG,IAAI,CAAC9I,UAAU;IAChC,CAAC,MACI,IAAIhL,KAAK,CAACgU,OAAO,CAAC,IAAI,CAAChJ,UAAU,CAAC,EAAE;MACrC8I,UAAU,GAAGxgB,EAAE,CAAC,IAAI,CAAC0X,UAAU,CAAC;IACpC;IACA,IAAI8I,UAAU,KAAKvJ,SAAS,KAAK,OAAOtV,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E,MAAMyT,8BAA8B,CAAC,CAAC;IAC1C;IACA,IAAI,CAACuJ,yBAAyB,GAAG6B,UAAU,CACtClH,IAAI,CAACrZ,SAAS,CAAC,IAAI,CAACsZ,UAAU,CAAC,CAAC,CAChCC,SAAS,CAACxE,IAAI,IAAI;MACnB,IAAI,CAAC+J,KAAK,GAAG/J,IAAI,IAAI,EAAE;MACvB,IAAI,CAACiG,UAAU,CAAC,CAAC;IACrB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIwD,sBAAsBA,CAAA,EAAG;IACrB;IACA,IAAI,IAAI,CAACtI,gBAAgB,CAAC4B,aAAa,CAAChR,MAAM,GAAG,CAAC,EAAE;MAChD,IAAI,CAACoP,gBAAgB,CAAC4B,aAAa,CAAC4C,KAAK,CAAC,CAAC;IAC/C;IACA,IAAI,CAACC,cAAc,CAACH,OAAO,CAAC,CAACC,GAAG,EAAE1c,CAAC,KAAK,IAAI,CAAC2iB,UAAU,CAAC,IAAI,CAACxK,gBAAgB,EAAEuE,GAAG,EAAE1c,CAAC,CAAC,CAAC;IACvF,IAAI,CAACqf,2BAA2B,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACIqB,sBAAsBA,CAAA,EAAG;IACrB;IACA,IAAI,IAAI,CAACnI,gBAAgB,CAACwB,aAAa,CAAChR,MAAM,GAAG,CAAC,EAAE;MAChD,IAAI,CAACwP,gBAAgB,CAACwB,aAAa,CAAC4C,KAAK,CAAC,CAAC;IAC/C;IACA,IAAI,CAACE,cAAc,CAACJ,OAAO,CAAC,CAACC,GAAG,EAAE1c,CAAC,KAAK,IAAI,CAAC2iB,UAAU,CAAC,IAAI,CAACpK,gBAAgB,EAAEmE,GAAG,EAAE1c,CAAC,CAAC,CAAC;IACvF,IAAI,CAAC2f,2BAA2B,CAAC,CAAC;EACtC;EACA;EACAI,sBAAsBA,CAAC7R,IAAI,EAAE+P,MAAM,EAAE;IACjC,MAAMwD,UAAU,GAAG/S,KAAK,CAACC,IAAI,CAAC,CAAAsP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEtU,OAAO,KAAI,EAAE,CAAC,CAACsH,GAAG,CAAC2R,UAAU,IAAI;MACnE,MAAMlc,SAAS,GAAG,IAAI,CAAC8V,iBAAiB,CAAC7H,GAAG,CAACiO,UAAU,CAAC;MACxD,IAAI,CAAClc,SAAS,KAAK,OAAO/C,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC/D,MAAM+S,0BAA0B,CAACkM,UAAU,CAAC;MAChD;MACA,OAAOlc,SAAS;IACpB,CAAC,CAAC;IACF,MAAMwI,iBAAiB,GAAGuS,UAAU,CAACxQ,GAAG,CAACvK,SAAS,IAAIA,SAAS,CAAClC,MAAM,CAAC;IACvE,MAAM2K,eAAe,GAAGsS,UAAU,CAACxQ,GAAG,CAACvK,SAAS,IAAIA,SAAS,CAAC9B,SAAS,CAAC;IACxE,IAAI,CAACsX,aAAa,CAACjN,mBAAmB,CAACf,IAAI,EAAEgB,iBAAiB,EAAEC,eAAe,EAAE,CAAC,IAAI,CAACgL,YAAY,IAAI,IAAI,CAACC,2BAA2B,CAAC;EAC5I;EACA;EACAmF,gBAAgBA,CAACsD,SAAS,EAAE;IACxB,MAAMC,YAAY,GAAG,EAAE;IACvB,KAAK,IAAI9iB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6iB,SAAS,CAAC9I,aAAa,CAAChR,MAAM,EAAE/I,CAAC,EAAE,EAAE;MACrD,MAAM+iB,OAAO,GAAGF,SAAS,CAAC9I,aAAa,CAACpF,GAAG,CAAC3U,CAAC,CAAC;MAC9C8iB,YAAY,CAACra,IAAI,CAACsa,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,OAAOF,YAAY;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIxB,WAAWA,CAACtK,IAAI,EAAEiE,SAAS,EAAE;IACzB,IAAI,IAAI,CAAC+E,QAAQ,CAACjX,MAAM,IAAI,CAAC,EAAE;MAC3B,OAAO,CAAC,IAAI,CAACiX,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7B;IACA,IAAIqB,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAACxH,qBAAqB,EAAE;MAC5BwH,OAAO,GAAG,IAAI,CAACrB,QAAQ,CAACzK,MAAM,CAACmH,GAAG,IAAI,CAACA,GAAG,CAACxR,IAAI,IAAIwR,GAAG,CAACxR,IAAI,CAAC+P,SAAS,EAAEjE,IAAI,CAAC,CAAC;IACjF,CAAC,MACI;MACD,IAAIiH,MAAM,GAAG,IAAI,CAAC+B,QAAQ,CAAClW,IAAI,CAAC4S,GAAG,IAAIA,GAAG,CAACxR,IAAI,IAAIwR,GAAG,CAACxR,IAAI,CAAC+P,SAAS,EAAEjE,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC8F,cAAc;MACpG,IAAImB,MAAM,EAAE;QACRoD,OAAO,CAAC5Y,IAAI,CAACwV,MAAM,CAAC;MACxB;IACJ;IACA,IAAI,CAACoD,OAAO,CAACtY,MAAM,KAAK,OAAOpF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACpE,MAAMoT,kCAAkC,CAACC,IAAI,CAAC;IAClD;IACA,OAAOqK,OAAO;EAClB;EACA1D,oBAAoBA,CAACwD,SAAS,EAAEhQ,KAAK,EAAE;IACnC,MAAM8M,MAAM,GAAGkD,SAAS,CAAClD,MAAM;IAC/B,MAAMF,OAAO,GAAG;MAAEle,SAAS,EAAEshB,SAAS,CAACnK;IAAK,CAAC;IAC7C,OAAO;MACHiM,WAAW,EAAEhF,MAAM,CAAC5T,QAAQ;MAC5B0T,OAAO;MACP5M;IACJ,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIwR,UAAUA,CAACO,MAAM,EAAEjF,MAAM,EAAE9M,KAAK,EAAE4M,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5C;IACA,MAAMoF,IAAI,GAAGD,MAAM,CAACnJ,aAAa,CAACqJ,kBAAkB,CAACnF,MAAM,CAAC5T,QAAQ,EAAE0T,OAAO,EAAE5M,KAAK,CAAC;IACrF,IAAI,CAAC6M,0BAA0B,CAACC,MAAM,EAAEF,OAAO,CAAC;IAChD,OAAOoF,IAAI;EACf;EACAnF,0BAA0BA,CAACC,MAAM,EAAEF,OAAO,EAAE;IACxC,KAAK,IAAIsF,YAAY,IAAI,IAAI,CAACC,iBAAiB,CAACrF,MAAM,CAAC,EAAE;MACrD,IAAI9S,aAAa,CAACC,oBAAoB,EAAE;QACpCD,aAAa,CAACC,oBAAoB,CAACmY,cAAc,CAACH,kBAAkB,CAACC,YAAY,EAAEtF,OAAO,CAAC;MAC/F;IACJ;IACA,IAAI,CAACyF,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIvF,sBAAsBA,CAAA,EAAG;IACrB,MAAMnE,aAAa,GAAG,IAAI,CAACjC,UAAU,CAACiC,aAAa;IACnD,KAAK,IAAI2J,WAAW,GAAG,CAAC,EAAEC,KAAK,GAAG5J,aAAa,CAAChR,MAAM,EAAE2a,WAAW,GAAGC,KAAK,EAAED,WAAW,EAAE,EAAE;MACxF,MAAMX,OAAO,GAAGhJ,aAAa,CAACpF,GAAG,CAAC+O,WAAW,CAAC;MAC9C,MAAM3F,OAAO,GAAGgF,OAAO,CAAChF,OAAO;MAC/BA,OAAO,CAAC4F,KAAK,GAAGA,KAAK;MACrB5F,OAAO,CAAChY,KAAK,GAAG2d,WAAW,KAAK,CAAC;MACjC3F,OAAO,CAAC6F,IAAI,GAAGF,WAAW,KAAKC,KAAK,GAAG,CAAC;MACxC5F,OAAO,CAAC8F,IAAI,GAAGH,WAAW,GAAG,CAAC,KAAK,CAAC;MACpC3F,OAAO,CAAC+F,GAAG,GAAG,CAAC/F,OAAO,CAAC8F,IAAI;MAC3B,IAAI,IAAI,CAAChK,qBAAqB,EAAE;QAC5BkE,OAAO,CAAC9C,SAAS,GAAG,IAAI,CAACiC,WAAW,CAACwG,WAAW,CAAC,CAACzI,SAAS;QAC3D8C,OAAO,CAAC2F,WAAW,GAAGA,WAAW;MACrC,CAAC,MACI;QACD3F,OAAO,CAAC5M,KAAK,GAAG,IAAI,CAAC+L,WAAW,CAACwG,WAAW,CAAC,CAACzI,SAAS;MAC3D;IACJ;EACJ;EACA;EACAqI,iBAAiBA,CAACrF,MAAM,EAAE;IACtB,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACtU,OAAO,EAAE;MAC5B,OAAO,EAAE;IACb;IACA,OAAO+E,KAAK,CAACC,IAAI,CAACsP,MAAM,CAACtU,OAAO,EAAEoa,QAAQ,IAAI;MAC1C,MAAM5Z,MAAM,GAAG,IAAI,CAACqS,iBAAiB,CAAC7H,GAAG,CAACoP,QAAQ,CAAC;MACnD,IAAI,CAAC5Z,MAAM,KAAK,OAAOxG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC5D,MAAM+S,0BAA0B,CAACqN,QAAQ,CAAC;MAC9C;MACA,OAAO9F,MAAM,CAAC/T,mBAAmB,CAACC,MAAM,CAAC;IAC7C,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI6P,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACc,WAAW,CAAC9Q,IAAI,CAAC,EAAE,CAAC;IACzB,IAAI,CAAC8N,UAAU,CAACiC,aAAa,CAAC4C,KAAK,CAAC,CAAC;IACrC,IAAI,CAACM,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;EACI4D,kBAAkBA,CAAA,EAAG;IACjB,MAAMmD,kBAAkB,GAAGA,CAAC9B,GAAG,EAAE+B,CAAC,KAAK;MACnC,OAAO/B,GAAG,IAAI+B,CAAC,CAAClf,gBAAgB,CAAC,CAAC;IACtC,CAAC;IACD;IACA;IACA;IACA,IAAI,IAAI,CAAC6X,cAAc,CAACwF,MAAM,CAAC4B,kBAAkB,EAAE,KAAK,CAAC,EAAE;MACvD,IAAI,CAAC3E,2BAA2B,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAACxC,cAAc,CAACuF,MAAM,CAAC4B,kBAAkB,EAAE,KAAK,CAAC,EAAE;MACvD,IAAI,CAACrE,2BAA2B,CAAC,CAAC;IACtC;IACA,IAAIjR,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC6N,iBAAiB,CAAC0D,MAAM,CAAC,CAAC,CAAC,CAACkC,MAAM,CAAC4B,kBAAkB,EAAE,KAAK,CAAC,EAAE;MAC/E,IAAI,CAAC3J,4BAA4B,GAAG,IAAI;MACxC,IAAI,CAACJ,wBAAwB,CAAC,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIkB,kBAAkBA,CAAA,EAAG;IACjB,MAAM9N,SAAS,GAAG,IAAI,CAAC6W,IAAI,GAAG,IAAI,CAACA,IAAI,CAACxf,KAAK,GAAG,KAAK;IACrD,IAAI,CAACwX,aAAa,GAAG,IAAIhP,YAAY,CAAC,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAACgX,cAAc,EAAE9W,SAAS,EAAE,IAAI,CAACC,wBAAwB,EAAE,IAAI,CAACqN,SAAS,CAACC,SAAS,EAAE,IAAI,CAACwJ,4BAA4B,EAAE,IAAI,CAACC,0BAA0B,EAAE,IAAI,CAACC,SAAS,CAAC;IAC3O,CAAC,IAAI,CAACJ,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC7I,MAAM,GAAGrZ,EAAE,CAAC,CAAC,EAC/BsZ,IAAI,CAACrZ,SAAS,CAAC,IAAI,CAACsZ,UAAU,CAAC,CAAC,CAChCC,SAAS,CAAC9W,KAAK,IAAI;MACpB,IAAI,CAACwX,aAAa,CAAC7O,SAAS,GAAG3I,KAAK;MACpC,IAAI,CAACuV,wBAAwB,CAAC,CAAC;IACnC,CAAC,CAAC;EACN;EACA;EACA0H,WAAWA,CAAC4C,KAAK,EAAE;IACf,OAAOA,KAAK,CAAChP,MAAM,CAACqI,IAAI,IAAI,CAACA,IAAI,CAACpW,MAAM,IAAIoW,IAAI,CAACpW,MAAM,KAAK,IAAI,CAAC;EACrE;EACA;EACA4V,gBAAgBA,CAAA,EAAG;IACf,MAAM+B,SAAS,GAAG,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACoF,UAAU;IAC1D,IAAI,CAACrF,SAAS,EAAE;MACZ;IACJ;IACA,MAAMsF,UAAU,GAAG,IAAI,CAAC3M,UAAU,CAACiC,aAAa,CAAChR,MAAM,KAAK,CAAC;IAC7D,IAAI0b,UAAU,KAAK,IAAI,CAACC,mBAAmB,EAAE;MACzC;IACJ;IACA,MAAMC,SAAS,GAAG,IAAI,CAAChM,gBAAgB,CAACoB,aAAa;IACrD,IAAI0K,UAAU,EAAE;MACZ,MAAMtB,IAAI,GAAGwB,SAAS,CAACvB,kBAAkB,CAACjE,SAAS,CAAC8D,WAAW,CAAC;MAChE,MAAM2B,QAAQ,GAAGzB,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC;MAClC;MACA;MACA,IAAIG,IAAI,CAACH,SAAS,CAACja,MAAM,KAAK,CAAC,IAAI,CAAA6b,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEpW,QAAQ,MAAK,IAAI,CAACqW,SAAS,CAACpW,YAAY,EAAE;QACnFmW,QAAQ,CAACld,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;QACpCkd,QAAQ,CAAC/d,SAAS,CAACC,GAAG,CAACqY,SAAS,CAAC2F,iBAAiB,CAAC;MACvD;IACJ,CAAC,MACI;MACDH,SAAS,CAAChI,KAAK,CAAC,CAAC;IACrB;IACA,IAAI,CAAC+H,mBAAmB,GAAGD,UAAU;IACrC,IAAI,CAACjB,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;AASJ;AAACsB,SAAA,GAj6BKhM,QAAQ;AAAA3V,eAAA,CAAR2V,QAAQ,wBAAAiM,kBAAAzhB,iBAAA;EAAA,YAAAA,iBAAA,IAy5ByFwV,SAAQ;AAAA;AAAA3V,eAAA,CAz5BzG2V,QAAQ,8BAxwCmEta,EAAE,CAAAkN,iBAAA;EAAAlI,IAAA,EAkqEQsV,SAAQ;EAAArV,SAAA;EAAA6B,cAAA,WAAA0f,yBAAArmB,EAAA,EAAAC,GAAA,EAAA4G,QAAA;IAAA,IAAA7G,EAAA;MAlqElBH,EAAE,CAAAiH,cAAA,CAAAD,QAAA,EAwqERqH,YAAY;MAxqENrO,EAAE,CAAAiH,cAAA,CAAAD,QAAA,EAwqE4EpB,YAAY;MAxqE1F5F,EAAE,CAAAiH,cAAA,CAAAD,QAAA,EAwqE6JsF,SAAS;MAxqExKtM,EAAE,CAAAiH,cAAA,CAAAD,QAAA,EAwqEiP2E,eAAe;MAxqElQ3L,EAAE,CAAAiH,cAAA,CAAAD,QAAA,EAwqE2U6E,eAAe;IAAA;IAAA,IAAA1L,EAAA;MAAA,IAAA+G,EAAA;MAxqE5VlH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAA2lB,UAAA,GAAA7e,EAAA,CAAAI,KAAA;MAAFtH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAA+iB,kBAAA,GAAAjc,EAAA;MAAFlH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAAkjB,eAAA,GAAApc,EAAA;MAAFlH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAAgjB,qBAAA,GAAAlc,EAAA;MAAFlH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAAijB,qBAAA,GAAAnc,EAAA;IAAA;EAAA;EAAAuB,SAAA;EAAAge,QAAA;EAAAC,YAAA,WAAAC,uBAAAxmB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFH,EAAE,CAAA4mB,WAAA,2BAAAxmB,GAAA,CAAAqb,WAkqEe,CAAC;IAAA;EAAA;EAAAhU,MAAA;IAAAmT,OAAA;IAAAK,UAAA;IAAAG,qBAAA,wDAAiMpZ,gBAAgB;IAAAyZ,WAAA,oCAA+CzZ,gBAAgB;EAAA;EAAA6kB,OAAA;IAAAjI,cAAA;EAAA;EAAAkI,QAAA;EAAApf,QAAA,GAlqElS1H,EAAE,CAAA2H,kBAAA,CAkqEqc,CAC5gB;IAAEC,OAAO,EAAErD,SAAS;IAAEsD,WAAW,EAAEyS;EAAS,CAAC,EAC7C;IAAE1S,OAAO,EAAElE,uBAAuB;IAAEwV,QAAQ,EAAEnV;EAA6B,CAAC,EAC5E;IAAE6D,OAAO,EAAE6B,0BAA0B;IAAEyP,QAAQ,EAAExP;EAAyB,CAAC;EAC3E;EACA;IAAE9B,OAAO,EAAEkR,2BAA2B;IAAEiO,QAAQ,EAAE;EAAK,CAAC,CAC3D;EAAAC,kBAAA,EAAAC,GAAA;EAAA9Z,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAzB,QAAA,WAAAsb,mBAAA/mB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAxqEwEH,EAAE,CAAAmnB,eAAA,CAAAC,GAAA;MAAFpnB,EAAE,CAAAC,YAAA,EAwqEsc,CAAC;MAxqEzcD,EAAE,CAAAC,YAAA,KAwqEgf,CAAC;MAxqEnfD,EAAE,CAAAqnB,UAAA,IAAAC,gCAAA,MAwqE2qB,CAAC,IAAApnB,gCAAA,MAAuD,CAAC,IAAAM,gCAAA,MAAyS,CAAC;IAAA;IAAA,IAAAL,EAAA;MAxqEhhCH,EAAE,CAAAe,SAAA,EAwqEmsB,CAAC;MAxqEtsBf,EAAE,CAAAunB,aAAA,CAAAnnB,GAAA,CAAA6b,SAAA,SAwqEmsB,CAAC;MAxqEtsBjc,EAAE,CAAAe,SAAA,CAwqEgqC,CAAC;MAxqEnqCf,EAAE,CAAAunB,aAAA,CAAAnnB,GAAA,CAAAsO,kBAAA,QAwqEgqC,CAAC;IAAA;EAAA;EAAAnB,YAAA,GAA0HkM,eAAe,EAA8DN,aAAa,EAAwDc,eAAe,EAA8DJ,eAAe;EAAA2N,MAAA;EAAAha,aAAA;AAAA;AAE5lD;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KA1qEiFlF,EAAE,CAAAmF,iBAAA,CA0qEQmV,QAAQ,EAAc,CAAC;IACtGtV,IAAI,EAAExC,SAAS;IACf4C,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEyhB,QAAQ,EAAE,UAAU;MAAElb,QAAQ,EAAEyO,kBAAkB;MAAE1R,IAAI,EAAE;QAChG,OAAO,EAAE,WAAW;QACpB,gCAAgC,EAAE;MACtC,CAAC;MAAE6E,aAAa,EAAE9K,iBAAiB,CAACiL,IAAI;MAAEF,eAAe,EAAEhL,uBAAuB,CAACiL,OAAO;MAAE5F,SAAS,EAAE,CACnG;QAAEF,OAAO,EAAErD,SAAS;QAAEsD,WAAW,EAAEyS;MAAS,CAAC,EAC7C;QAAE1S,OAAO,EAAElE,uBAAuB;QAAEwV,QAAQ,EAAEnV;MAA6B,CAAC,EAC5E;QAAE6D,OAAO,EAAE6B,0BAA0B;QAAEyP,QAAQ,EAAExP;MAAyB,CAAC;MAC3E;MACA;QAAE9B,OAAO,EAAEkR,2BAA2B;QAAEiO,QAAQ,EAAE;MAAK,CAAC,CAC3D;MAAEnZ,OAAO,EAAE,CAAC6L,eAAe,EAAEN,aAAa,EAAEc,eAAe,EAAEJ,eAAe,CAAC;MAAE2N,MAAM,EAAE,CAAC,+CAA+C;IAAE,CAAC;EACvJ,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE5M,OAAO,EAAE,CAAC;MAClD5V,IAAI,EAAE/C;IACV,CAAC,CAAC;IAAEgZ,UAAU,EAAE,CAAC;MACbjW,IAAI,EAAE/C;IACV,CAAC,CAAC;IAAEmZ,qBAAqB,EAAE,CAAC;MACxBpW,IAAI,EAAE/C,KAAK;MACXmD,IAAI,EAAE,CAAC;QAAE2C,SAAS,EAAE/F;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyZ,WAAW,EAAE,CAAC;MACdzW,IAAI,EAAE/C,KAAK;MACXmD,IAAI,EAAE,CAAC;QAAE2C,SAAS,EAAE/F;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4c,cAAc,EAAE,CAAC;MACjB5Z,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEmgB,kBAAkB,EAAE,CAAC;MACrBne,IAAI,EAAE/B,eAAe;MACrBmC,IAAI,EAAE,CAACQ,YAAY,EAAE;QAAE6hB,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEnE,eAAe,EAAE,CAAC;MAClBte,IAAI,EAAE/B,eAAe;MACrBmC,IAAI,EAAE,CAACkH,SAAS,EAAE;QAAEmb,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAErE,qBAAqB,EAAE,CAAC;MACxBpe,IAAI,EAAE/B,eAAe;MACrBmC,IAAI,EAAE,CAACuG,eAAe,EAAE;QAChB8b,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAEpE,qBAAqB,EAAE,CAAC;MACxBre,IAAI,EAAE/B,eAAe;MACrBmC,IAAI,EAAE,CAACyG,eAAe,EAAE;QAChB4b,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAE1B,UAAU,EAAE,CAAC;MACb/gB,IAAI,EAAE9C,YAAY;MAClBkD,IAAI,EAAE,CAACiJ,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAAS4U,gBAAgBA,CAACyE,KAAK,EAAEpR,GAAG,EAAE;EAClC,OAAOoR,KAAK,CAACC,MAAM,CAAC1X,KAAK,CAACC,IAAI,CAACoG,GAAG,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA,SAAS0K,mBAAmBA,CAACyD,MAAM,EAAEmD,OAAO,EAAE;EAC1C,MAAMC,gBAAgB,GAAGD,OAAO,CAACE,WAAW,CAAC,CAAC;EAC9C,IAAIC,OAAO,GAAGtD,MAAM,CAACnJ,aAAa,CAAChL,OAAO,CAACnI,aAAa;EACxD,OAAO4f,OAAO,EAAE;IACZ;IACA,MAAM3L,QAAQ,GAAG2L,OAAO,CAAChY,QAAQ,KAAK,CAAC,GAAGgY,OAAO,CAAC3L,QAAQ,GAAG,IAAI;IACjE,IAAIA,QAAQ,KAAKyL,gBAAgB,EAAE;MAC/B,OAAOE,OAAO;IAClB,CAAC,MACI,IAAI3L,QAAQ,KAAK,OAAO,EAAE;MAC3B;MACA;IACJ;IACA2L,OAAO,GAAGA,OAAO,CAACC,UAAU;EAChC;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAGhB;EACA,IAAI3mB,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACuE,KAAK;EACrB;EACA,IAAIvE,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACuE,KAAK,GAAGvE,IAAI;IACjB;IACA;IACA,IAAI,CAAC4mB,kBAAkB,CAAC,CAAC;EAC7B;EAkCAxjB,WAAWA,CAAA,EAAG;IAAAC,eAAA,iBA7CL9C,MAAM,CAACyY,QAAQ,EAAE;MAAEjU,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA1B,eAAA,mBAClC9C,MAAM,CAAC2C,mBAAmB,EAAE;MAAE6B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA1B,eAAA;IAY1D;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAOA;IAAAA,eAAA,kBACU,OAAO;IACjB;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA;IAQA;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA;IASI,IAAI,CAACwjB,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC;EACvC;EACA1L,QAAQA,CAAA,EAAG;IACP,IAAI,CAACyL,kBAAkB,CAAC,CAAC;IACzB,IAAI,IAAI,CAACjnB,UAAU,KAAKuZ,SAAS,EAAE;MAC/B,IAAI,CAACvZ,UAAU,GAAG,IAAI,CAACmnB,wBAAwB,CAAC,CAAC;IACrD;IACA,IAAI,CAAC,IAAI,CAAC/mB,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GACb,IAAI,CAAC8mB,QAAQ,CAACE,mBAAmB,KAAK,CAAC9P,IAAI,EAAEjX,IAAI,KAAKiX,IAAI,CAACjX,IAAI,CAAC,CAAC;IACzE;IACA,IAAI,IAAI,CAACyH,MAAM,EAAE;MACb;MACA;MACA;MACA,IAAI,CAACd,SAAS,CAACZ,IAAI,GAAG,IAAI,CAACA,IAAI;MAC/B,IAAI,CAACY,SAAS,CAACV,UAAU,GAAG,IAAI,CAACA,UAAU;MAC3C,IAAI,CAACwB,MAAM,CAAC6W,YAAY,CAAC,IAAI,CAAC3X,SAAS,CAAC;IAC5C,CAAC,MACI,IAAI,OAAO/C,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAM0T,yCAAyC,CAAC,CAAC;IACrD;EACJ;EACAhM,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC7D,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAAC8W,eAAe,CAAC,IAAI,CAAC5X,SAAS,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;EACImgB,wBAAwBA,CAAA,EAAG;IACvB,MAAM9mB,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACA,IAAI,KAAK,OAAO4D,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1D,MAAM2T,kCAAkC,CAAC,CAAC;IAC9C;IACA,IAAI,IAAI,CAACsP,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACG,0BAA0B,EAAE;MAC3D,OAAO,IAAI,CAACH,QAAQ,CAACG,0BAA0B,CAAChnB,IAAI,CAAC;IACzD;IACA,OAAOA,IAAI,CAAC,CAAC,CAAC,CAACwmB,WAAW,CAAC,CAAC,GAAGxmB,IAAI,CAACiR,KAAK,CAAC,CAAC,CAAC;EAChD;EACA;EACA2V,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACjgB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC3G,IAAI,GAAG,IAAI,CAACA,IAAI;IACnC;EACJ;AAYJ;AAACinB,cAAA,GA1GKN,aAAa;AAAAtjB,eAAA,CAAbsjB,aAAa,wBAAAO,uBAAA1jB,iBAAA;EAAA,YAAAA,iBAAA,IA+FoFmjB,cAAa;AAAA;AAAAtjB,eAAA,CA/F9GsjB,aAAa,8BAzvE8DjoB,EAAE,CAAAkN,iBAAA;EAAAlI,IAAA,EAy1EQijB,cAAa;EAAAhjB,SAAA;EAAAwjB,SAAA,WAAAC,qBAAAvoB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAz1EvBH,EAAE,CAAA2oB,WAAA,CAy1EiP/iB,YAAY;MAz1E/P5F,EAAE,CAAA2oB,WAAA,CAy1EkVlkB,UAAU;MAz1E9VzE,EAAE,CAAA2oB,WAAA,CAy1EubrjB,gBAAgB;IAAA;IAAA,IAAAnF,EAAA;MAAA,IAAA+G,EAAA;MAz1EzclH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAA6H,SAAA,GAAAf,EAAA,CAAAI,KAAA;MAAFtH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAAiH,IAAA,GAAAH,EAAA,CAAAI,KAAA;MAAFtH,EAAE,CAAAmH,cAAA,CAAAD,EAAA,GAAFlH,EAAE,CAAAoH,WAAA,QAAAhH,GAAA,CAAAmH,UAAA,GAAAL,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAAG,MAAA;IAAAnG,IAAA;IAAAL,UAAA;IAAAI,YAAA;IAAAP,OAAA;EAAA;EAAAqM,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAzB,QAAA,WAAAgd,wBAAAzoB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFH,EAAE,CAAA6oB,uBAAA,KA01ErD,CAAC;MA11EkD7oB,EAAE,CAAAqnB,UAAA,IAAA5mB,4BAAA,eA21EX,CAAC,IAAAS,4BAAA,eAGH,CAAC;MA91EUlB,EAAE,CAAA8oB,qBAAA;IAAA;EAAA;EAAAvb,YAAA,GAk2EpB3H,YAAY,EAA4GN,gBAAgB,EAA+DgD,aAAa,EAAiF7D,UAAU,EAAyD2E,OAAO;EAAAoE,aAAA;AAAA;AAE9a;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KAp2EiFlF,EAAE,CAAAmF,iBAAA,CAo2EQ8iB,aAAa,EAAc,CAAC;IAC3GjjB,IAAI,EAAExC,SAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BuG,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiB4B,aAAa,EAAE9K,iBAAiB,CAACiL,IAAI;MACrC;MACA;MACA;MACA;MACA;MACA;MACAF,eAAe,EAAEhL,uBAAuB,CAACiL,OAAO;MAChDE,OAAO,EAAE,CAAChI,YAAY,EAAEN,gBAAgB,EAAEgD,aAAa,EAAE7D,UAAU,EAAE2E,OAAO;IAChF,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE9H,IAAI,EAAE,CAAC;MAC/C0D,IAAI,EAAE/C;IACV,CAAC,CAAC;IAAEhB,UAAU,EAAE,CAAC;MACb+D,IAAI,EAAE/C;IACV,CAAC,CAAC;IAAEZ,YAAY,EAAE,CAAC;MACf2D,IAAI,EAAE/C;IACV,CAAC,CAAC;IAAEnB,OAAO,EAAE,CAAC;MACVkE,IAAI,EAAE/C;IACV,CAAC,CAAC;IAAEgG,SAAS,EAAE,CAAC;MACZjD,IAAI,EAAE9B,SAAS;MACfkC,IAAI,EAAE,CAACQ,YAAY,EAAE;QAAEmjB,MAAM,EAAE;MAAK,CAAC;IACzC,CAAC,CAAC;IAAE1hB,IAAI,EAAE,CAAC;MACPrC,IAAI,EAAE9B,SAAS;MACfkC,IAAI,EAAE,CAACX,UAAU,EAAE;QAAEskB,MAAM,EAAE;MAAK,CAAC;IACvC,CAAC,CAAC;IAAExhB,UAAU,EAAE,CAAC;MACbvC,IAAI,EAAE9B,SAAS;MACfkC,IAAI,EAAE,CAACE,gBAAgB,EAAE;QAAEyjB,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,qBAAqB,GAAG,CAC1B1O,QAAQ,EACRhO,SAAS,EACT7H,UAAU,EACViI,aAAa,EACbpH,gBAAgB,EAChBG,gBAAgB,EAChBG,YAAY,EACZwD,OAAO,EACP6E,MAAM,EACN3F,aAAa,EACbM,aAAa,EACbmE,YAAY,EACZpB,eAAe,EACfkC,YAAY,EACZhC,eAAe,EACfsN,aAAa,EACbM,eAAe,EACfI,eAAe,EACfoO,aAAa,EACb5Z,YAAY,EACZ0K,cAAc,EACdkB,eAAe,CAClB;AACD,MAAMgP,cAAc,CAAC;AA8CpBC,eAAA,GA9CKD,cAAc;AAAAtkB,eAAA,CAAdskB,cAAc,wBAAAE,wBAAArkB,iBAAA;EAAA,YAAAA,iBAAA,IACmFmkB,eAAc;AAAA;AAAAtkB,eAAA,CAD/GskB,cAAc,8BAv6E6DjpB,EAAE,CAAAopB,gBAAA;EAAApkB,IAAA,EAy6EqBikB,eAAc;EAAArb,OAAA,GAAYxJ,eAAe,EAAEkW,QAAQ,EAC/IhO,SAAS,EACT7H,UAAU,EACViI,aAAa,EACbpH,gBAAgB,EAChBG,gBAAgB,EAChBG,YAAY,EACZwD,OAAO,EACP6E,MAAM,EACN3F,aAAa,EACbM,aAAa,EACbmE,YAAY,EACZpB,eAAe,EACfkC,YAAY,EACZhC,eAAe,EACfsN,aAAa,EACbM,eAAe,EACfI,eAAe,EACfoO,aAAa,EACb5Z,YAAY,EACZ0K,cAAc,EACdkB,eAAe;EAAAoP,OAAA,GAAa/O,QAAQ,EACpChO,SAAS,EACT7H,UAAU,EACViI,aAAa,EACbpH,gBAAgB,EAChBG,gBAAgB,EAChBG,YAAY,EACZwD,OAAO,EACP6E,MAAM,EACN3F,aAAa,EACbM,aAAa,EACbmE,YAAY,EACZpB,eAAe,EACfkC,YAAY,EACZhC,eAAe,EACfsN,aAAa,EACbM,eAAe,EACfI,eAAe,EACfoO,aAAa,EACb5Z,YAAY,EACZ0K,cAAc,EACdkB,eAAe;AAAA;AAAAtV,eAAA,CA5CrBskB,cAAc,8BAv6E6DjpB,EAAE,CAAAspB,gBAAA;EAAA1b,OAAA,GAo9E+CxJ,eAAe;AAAA;AAEjJ;EAAA,QAAAc,SAAA,oBAAAA,SAAA,KAt9EiFlF,EAAE,CAAAmF,iBAAA,CAs9EQ8jB,cAAc,EAAc,CAAC;IAC5GjkB,IAAI,EAAE7B,QAAQ;IACdiC,IAAI,EAAE,CAAC;MACCikB,OAAO,EAAEL,qBAAqB;MAC9Bpb,OAAO,EAAE,CAACxJ,eAAe,EAAE,GAAG4kB,qBAAqB;IACvD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,mBAAmBA,CAACC,IAAI,EAAE;EAC/B,OAAO,cAAcA,IAAI,CAAC;IACtB;IACA,IAAIzjB,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACC,OAAO;IACvB;IACA,IAAID,MAAMA,CAAC0jB,CAAC,EAAE;MACV,MAAMC,SAAS,GAAG,IAAI,CAAC1jB,OAAO;MAC9B,IAAI,CAACA,OAAO,GAAG1B,qBAAqB,CAACmlB,CAAC,CAAC;MACvC,IAAI,CAACvjB,iBAAiB,GAAGwjB,SAAS,KAAK,IAAI,CAAC1jB,OAAO;IACvD;IAIA;IACAM,gBAAgBA,CAAA,EAAG;MACf,MAAMA,gBAAgB,GAAG,IAAI,CAACJ,iBAAiB;MAC/C,IAAI,CAACA,iBAAiB,GAAG,KAAK;MAC9B,OAAOI,gBAAgB;IAC3B;IACA;IACAC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACL,iBAAiB,GAAG,KAAK;IAClC;IACAxB,WAAWA,CAAC,GAAGU,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MAACT,eAAA,kBAdT,KAAK;MACf;MAAAA,eAAA,4BACoB,KAAK;IAazB;EACJ,CAAC;AACL;AAEA,SAASqD,WAAW,EAAE8C,UAAU,EAAED,gBAAgB,EAAEtG,SAAS,EAAE8V,kBAAkB,EAAEjR,OAAO,EAAE3E,UAAU,EAAEiI,aAAa,EAAE9G,YAAY,EAAEgD,aAAa,EAAEnD,gBAAgB,EAAEoI,YAAY,EAAEhC,eAAe,EAAEvD,aAAa,EAAEhD,gBAAgB,EAAEyH,YAAY,EAAEpB,eAAe,EAAE0C,YAAY,EAAE0K,cAAc,EAAE9K,MAAM,EAAE3B,SAAS,EAAEgO,QAAQ,EAAE2O,cAAc,EAAEhB,aAAa,EAAE9O,aAAa,EAAEU,eAAe,EAAEJ,eAAe,EAAEQ,eAAe,EAAEzL,iBAAiB,EAAEsK,2BAA2B,EAAErK,YAAY,EAAEjK,mBAAmB,EAAEiF,0BAA0B,EAAEC,wBAAwB,EAAEF,SAAS,EAAE+f,mBAAmB;AACxkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}