{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatTooltip, _TooltipComponent, _MatTooltipModule;\nconst _c0 = [\"tooltip\"];\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Injector, ViewContainerRef, afterNextRender, Directive, Input, ChangeDetectorRef, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { AriaDescriber, FocusMonitor, A11yModule } from '@angular/cdk/a11y';\nimport { Overlay, ScrollDispatcher, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { DOCUMENT, NgClass } from '@angular/common';\nimport { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n  return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition({\n      scrollThrottle: SCROLL_THROTTLE_MS\n    });\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition({\n    scrollThrottle: SCROLL_THROTTLE_MS\n  });\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY\n};\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    showDelay: 0,\n    hideDelay: 0,\n    touchendHideDelay: 1500\n  };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n  providedIn: 'root',\n  factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip {\n  /** Allows the user to define the position of the tooltip relative to the parent element */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    if (value !== this._position) {\n      this._position = value;\n      if (this._overlayRef) {\n        var _this$_tooltipInstanc;\n        this._updatePosition(this._overlayRef);\n        (_this$_tooltipInstanc = this._tooltipInstance) === null || _this$_tooltipInstanc === void 0 || _this$_tooltipInstanc.show(0);\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  /**\n   * Whether tooltip should be relative to the click or touch origin\n   * instead of outside the element bounding box.\n   */\n  get positionAtOrigin() {\n    return this._positionAtOrigin;\n  }\n  set positionAtOrigin(value) {\n    this._positionAtOrigin = coerceBooleanProperty(value);\n    this._detach();\n    this._overlayRef = null;\n  }\n  /** Disables the display of the tooltip. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    const isDisabled = coerceBooleanProperty(value);\n    if (this._disabled !== isDisabled) {\n      this._disabled = isDisabled;\n      // If tooltip is disabled, hide immediately.\n      if (isDisabled) {\n        this.hide(0);\n      } else {\n        this._setupPointerEnterEventsIfNeeded();\n      }\n      this._syncAriaDescription(this.message);\n    }\n  }\n  /** The default delay in ms before showing the tooltip after show is called */\n  get showDelay() {\n    return this._showDelay;\n  }\n  set showDelay(value) {\n    this._showDelay = coerceNumberProperty(value);\n  }\n  /** The default delay in ms before hiding the tooltip after hide is called */\n  get hideDelay() {\n    return this._hideDelay;\n  }\n  set hideDelay(value) {\n    this._hideDelay = coerceNumberProperty(value);\n    if (this._tooltipInstance) {\n      this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n    }\n  }\n  /** The message to be displayed in the tooltip */\n  get message() {\n    return this._message;\n  }\n  set message(value) {\n    const oldMessage = this._message;\n    // If the message is not a string (e.g. number), convert it to a string and trim it.\n    // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n    // away the string-conversion: https://github.com/angular/components/issues/20684\n    this._message = value != null ? String(value).trim() : '';\n    if (!this._message && this._isTooltipVisible()) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n      this._updateTooltipMessage();\n    }\n    this._syncAriaDescription(oldMessage);\n  }\n  /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n  get tooltipClass() {\n    return this._tooltipClass;\n  }\n  set tooltipClass(value) {\n    this._tooltipClass = value;\n    if (this._tooltipInstance) {\n      this._setTooltipClass(this._tooltipClass);\n    }\n  }\n  /** Manually-bound passive event listeners. */\n\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_ariaDescriber\", inject(AriaDescriber));\n    _defineProperty(this, \"_focusMonitor\", inject(FocusMonitor));\n    _defineProperty(this, \"_dir\", inject(Directionality));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_viewContainerRef\", inject(ViewContainerRef));\n    _defineProperty(this, \"_defaultOptions\", inject(MAT_TOOLTIP_DEFAULT_OPTIONS, {\n      optional: true\n    }));\n    _defineProperty(this, \"_overlayRef\", void 0);\n    _defineProperty(this, \"_tooltipInstance\", void 0);\n    _defineProperty(this, \"_portal\", void 0);\n    _defineProperty(this, \"_position\", 'below');\n    _defineProperty(this, \"_positionAtOrigin\", false);\n    _defineProperty(this, \"_disabled\", false);\n    _defineProperty(this, \"_tooltipClass\", void 0);\n    _defineProperty(this, \"_viewInitialized\", false);\n    _defineProperty(this, \"_pointerExitEventsInitialized\", false);\n    _defineProperty(this, \"_tooltipComponent\", TooltipComponent);\n    _defineProperty(this, \"_viewportMargin\", 8);\n    _defineProperty(this, \"_currentPosition\", void 0);\n    _defineProperty(this, \"_cssClassPrefix\", 'mat-mdc');\n    _defineProperty(this, \"_ariaDescriptionPending\", void 0);\n    _defineProperty(this, \"_dirSubscribed\", false);\n    _defineProperty(this, \"_showDelay\", void 0);\n    _defineProperty(this, \"_hideDelay\", void 0);\n    /**\n     * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n     * uses a long press gesture to show and hide, however it can conflict with the native browser\n     * gestures. To work around the conflict, Angular Material disables native gestures on the\n     * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n     * elements). The different values for this option configure the touch event handling as follows:\n     * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n     *   browser gestures on particular elements. In particular, it allows text selection on inputs\n     *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n     * - `on` - Enables touch gestures for all elements and disables native\n     *   browser gestures with no exceptions.\n     * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n     *   showing on touch devices.\n     */\n    _defineProperty(this, \"touchGestures\", 'auto');\n    _defineProperty(this, \"_message\", '');\n    _defineProperty(this, \"_passiveListeners\", []);\n    /** Timer started at the last `touchstart` event. */\n    _defineProperty(this, \"_touchstartTimeout\", null);\n    /** Emits when the component is destroyed. */\n    _defineProperty(this, \"_destroyed\", new Subject());\n    /** Whether ngOnDestroyed has been called. */\n    _defineProperty(this, \"_isDestroyed\", false);\n    const defaultOptions = this._defaultOptions;\n    if (defaultOptions) {\n      this._showDelay = defaultOptions.showDelay;\n      this._hideDelay = defaultOptions.hideDelay;\n      if (defaultOptions.position) {\n        this.position = defaultOptions.position;\n      }\n      if (defaultOptions.positionAtOrigin) {\n        this.positionAtOrigin = defaultOptions.positionAtOrigin;\n      }\n      if (defaultOptions.touchGestures) {\n        this.touchGestures = defaultOptions.touchGestures;\n      }\n      if (defaultOptions.tooltipClass) {\n        this.tooltipClass = defaultOptions.tooltipClass;\n      }\n    }\n    this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n  }\n  ngAfterViewInit() {\n    // This needs to happen after view init so the initial values for all inputs have been set.\n    this._viewInitialized = true;\n    this._setupPointerEnterEventsIfNeeded();\n    this._focusMonitor.monitor(this._elementRef).pipe(takeUntil(this._destroyed)).subscribe(origin => {\n      // Note that the focus monitor runs outside the Angular zone.\n      if (!origin) {\n        this._ngZone.run(() => this.hide(0));\n      } else if (origin === 'keyboard') {\n        this._ngZone.run(() => this.show());\n      }\n    });\n  }\n  /**\n   * Dispose the tooltip when destroyed.\n   */\n  ngOnDestroy() {\n    const nativeElement = this._elementRef.nativeElement;\n    // Optimization: Do not call clearTimeout unless there is an active timer.\n    if (this._touchstartTimeout) {\n      clearTimeout(this._touchstartTimeout);\n    }\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._tooltipInstance = null;\n    }\n    // Clean up the event listeners set in the constructor\n    this._passiveListeners.forEach(([event, listener]) => {\n      nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n    });\n    this._passiveListeners.length = 0;\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._isDestroyed = true;\n    this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n    this._focusMonitor.stopMonitoring(nativeElement);\n  }\n  /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n  show(delay = this.showDelay, origin) {\n    if (this.disabled || !this.message || this._isTooltipVisible()) {\n      var _this$_tooltipInstanc2;\n      (_this$_tooltipInstanc2 = this._tooltipInstance) === null || _this$_tooltipInstanc2 === void 0 || _this$_tooltipInstanc2._cancelPendingAnimations();\n      return;\n    }\n    const overlayRef = this._createOverlay(origin);\n    this._detach();\n    this._portal = this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n    const instance = this._tooltipInstance = overlayRef.attach(this._portal).instance;\n    instance._triggerElement = this._elementRef.nativeElement;\n    instance._mouseLeaveHideDelay = this._hideDelay;\n    instance.afterHidden().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._setTooltipClass(this._tooltipClass);\n    this._updateTooltipMessage();\n    instance.show(delay);\n  }\n  /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n  hide(delay = this.hideDelay) {\n    const instance = this._tooltipInstance;\n    if (instance) {\n      if (instance.isVisible()) {\n        instance.hide(delay);\n      } else {\n        instance._cancelPendingAnimations();\n        this._detach();\n      }\n    }\n  }\n  /** Shows/hides the tooltip */\n  toggle(origin) {\n    this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n  }\n  /** Returns true if the tooltip is currently visible to the user */\n  _isTooltipVisible() {\n    return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n  }\n  /** Create the overlay config and position strategy */\n  _createOverlay(origin) {\n    var _this$_defaultOptions;\n    if (this._overlayRef) {\n      const existingStrategy = this._overlayRef.getConfig().positionStrategy;\n      if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n        return this._overlayRef;\n      }\n      this._detach();\n    }\n    const scrollableAncestors = this._injector.get(ScrollDispatcher).getAncestorScrollContainers(this._elementRef);\n    const overlay = this._injector.get(Overlay);\n    // Create connected position strategy that listens for scroll events to reposition.\n    const strategy = overlay.position().flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(false).withViewportMargin(this._viewportMargin).withScrollableContainers(scrollableAncestors);\n    strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n      this._updateCurrentPositionClass(change.connectionPair);\n      if (this._tooltipInstance) {\n        if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n          // After position changes occur and the overlay is clipped by\n          // a parent scrollable then close the tooltip.\n          this._ngZone.run(() => this.hide(0));\n        }\n      }\n    });\n    this._overlayRef = overlay.create({\n      direction: this._dir,\n      positionStrategy: strategy,\n      panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n      scrollStrategy: this._injector.get(MAT_TOOLTIP_SCROLL_STRATEGY)()\n    });\n    this._updatePosition(this._overlayRef);\n    this._overlayRef.detachments().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._overlayRef.outsidePointerEvents().pipe(takeUntil(this._destroyed)).subscribe(() => {\n      var _this$_tooltipInstanc3;\n      return (_this$_tooltipInstanc3 = this._tooltipInstance) === null || _this$_tooltipInstanc3 === void 0 ? void 0 : _this$_tooltipInstanc3._handleBodyInteraction();\n    });\n    this._overlayRef.keydownEvents().pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n        event.preventDefault();\n        event.stopPropagation();\n        this._ngZone.run(() => this.hide(0));\n      }\n    });\n    if ((_this$_defaultOptions = this._defaultOptions) !== null && _this$_defaultOptions !== void 0 && _this$_defaultOptions.disableTooltipInteractivity) {\n      this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n    }\n    if (!this._dirSubscribed) {\n      this._dirSubscribed = true;\n      this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        if (this._overlayRef) {\n          this._updatePosition(this._overlayRef);\n        }\n      });\n    }\n    return this._overlayRef;\n  }\n  /** Detaches the currently-attached tooltip. */\n  _detach() {\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n    }\n    this._tooltipInstance = null;\n  }\n  /** Updates the position of the current tooltip. */\n  _updatePosition(overlayRef) {\n    const position = overlayRef.getConfig().positionStrategy;\n    const origin = this._getOrigin();\n    const overlay = this._getOverlayPosition();\n    position.withPositions([this._addOffset(_objectSpread(_objectSpread({}, origin.main), overlay.main)), this._addOffset(_objectSpread(_objectSpread({}, origin.fallback), overlay.fallback))]);\n  }\n  /** Adds the configured offset to a position. Used as a hook for child classes. */\n  _addOffset(position) {\n    const offset = UNBOUNDED_ANCHOR_GAP;\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    if (position.originY === 'top') {\n      position.offsetY = -offset;\n    } else if (position.originY === 'bottom') {\n      position.offsetY = offset;\n    } else if (position.originX === 'start') {\n      position.offsetX = isLtr ? -offset : offset;\n    } else if (position.originX === 'end') {\n      position.offsetX = isLtr ? offset : -offset;\n    }\n    return position;\n  }\n  /**\n   * Returns the origin position and a fallback position based on the user's position preference.\n   * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n   */\n  _getOrigin() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let originPosition;\n    if (position == 'above' || position == 'below') {\n      originPosition = {\n        originX: 'center',\n        originY: position == 'above' ? 'top' : 'bottom'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      originPosition = {\n        originX: 'start',\n        originY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      originPosition = {\n        originX: 'end',\n        originY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(originPosition.originX, originPosition.originY);\n    return {\n      main: originPosition,\n      fallback: {\n        originX: x,\n        originY: y\n      }\n    };\n  }\n  /** Returns the overlay position and a fallback position based on the user's preference */\n  _getOverlayPosition() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let overlayPosition;\n    if (position == 'above') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'bottom'\n      };\n    } else if (position == 'below') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'top'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'end',\n        overlayY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'start',\n        overlayY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n    return {\n      main: overlayPosition,\n      fallback: {\n        overlayX: x,\n        overlayY: y\n      }\n    };\n  }\n  /** Updates the tooltip message and repositions the overlay according to the new message length */\n  _updateTooltipMessage() {\n    // Must wait for the message to be painted to the tooltip so that the overlay can properly\n    // calculate the correct positioning based on the size of the text.\n    if (this._tooltipInstance) {\n      this._tooltipInstance.message = this.message;\n      this._tooltipInstance._markForCheck();\n      afterNextRender(() => {\n        if (this._tooltipInstance) {\n          this._overlayRef.updatePosition();\n        }\n      }, {\n        injector: this._injector\n      });\n    }\n  }\n  /** Updates the tooltip class */\n  _setTooltipClass(tooltipClass) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.tooltipClass = tooltipClass;\n      this._tooltipInstance._markForCheck();\n    }\n  }\n  /** Inverts an overlay position. */\n  _invertPosition(x, y) {\n    if (this.position === 'above' || this.position === 'below') {\n      if (y === 'top') {\n        y = 'bottom';\n      } else if (y === 'bottom') {\n        y = 'top';\n      }\n    } else {\n      if (x === 'end') {\n        x = 'start';\n      } else if (x === 'start') {\n        x = 'end';\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the class on the overlay panel based on the current position of the tooltip. */\n  _updateCurrentPositionClass(connectionPair) {\n    const {\n      overlayY,\n      originX,\n      originY\n    } = connectionPair;\n    let newPosition;\n    // If the overlay is in the middle along the Y axis,\n    // it means that it's either before or after.\n    if (overlayY === 'center') {\n      // Note that since this information is used for styling, we want to\n      // resolve `start` and `end` to their real values, otherwise consumers\n      // would have to remember to do it themselves on each consumption.\n      if (this._dir && this._dir.value === 'rtl') {\n        newPosition = originX === 'end' ? 'left' : 'right';\n      } else {\n        newPosition = originX === 'start' ? 'left' : 'right';\n      }\n    } else {\n      newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n    }\n    if (newPosition !== this._currentPosition) {\n      const overlayRef = this._overlayRef;\n      if (overlayRef) {\n        const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n        overlayRef.removePanelClass(classPrefix + this._currentPosition);\n        overlayRef.addPanelClass(classPrefix + newPosition);\n      }\n      this._currentPosition = newPosition;\n    }\n  }\n  /** Binds the pointer events to the tooltip trigger. */\n  _setupPointerEnterEventsIfNeeded() {\n    // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n    if (this._disabled || !this.message || !this._viewInitialized || this._passiveListeners.length) {\n      return;\n    }\n    // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n    // first tap from firing its click event or can cause the tooltip to open for clicks.\n    if (this._platformSupportsMouseEvents()) {\n      this._passiveListeners.push(['mouseenter', event => {\n        this._setupPointerExitEventsIfNeeded();\n        let point = undefined;\n        if (event.x !== undefined && event.y !== undefined) {\n          point = event;\n        }\n        this.show(undefined, point);\n      }]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      this._passiveListeners.push(['touchstart', event => {\n        var _event$targetTouches, _this$_defaultOptions2, _this$_defaultOptions3;\n        const touch = (_event$targetTouches = event.targetTouches) === null || _event$targetTouches === void 0 ? void 0 : _event$targetTouches[0];\n        const origin = touch ? {\n          x: touch.clientX,\n          y: touch.clientY\n        } : undefined;\n        // Note that it's important that we don't `preventDefault` here,\n        // because it can prevent click events from firing on the element.\n        this._setupPointerExitEventsIfNeeded();\n        if (this._touchstartTimeout) {\n          clearTimeout(this._touchstartTimeout);\n        }\n        const DEFAULT_LONGPRESS_DELAY = 500;\n        this._touchstartTimeout = setTimeout(() => {\n          this._touchstartTimeout = null;\n          this.show(undefined, origin);\n        }, (_this$_defaultOptions2 = (_this$_defaultOptions3 = this._defaultOptions) === null || _this$_defaultOptions3 === void 0 ? void 0 : _this$_defaultOptions3.touchLongPressShowDelay) !== null && _this$_defaultOptions2 !== void 0 ? _this$_defaultOptions2 : DEFAULT_LONGPRESS_DELAY);\n      }]);\n    }\n    this._addListeners(this._passiveListeners);\n  }\n  _setupPointerExitEventsIfNeeded() {\n    if (this._pointerExitEventsInitialized) {\n      return;\n    }\n    this._pointerExitEventsInitialized = true;\n    const exitListeners = [];\n    if (this._platformSupportsMouseEvents()) {\n      exitListeners.push(['mouseleave', event => {\n        var _this$_overlayRef;\n        const newTarget = event.relatedTarget;\n        if (!newTarget || !((_this$_overlayRef = this._overlayRef) !== null && _this$_overlayRef !== void 0 && _this$_overlayRef.overlayElement.contains(newTarget))) {\n          this.hide();\n        }\n      }], ['wheel', event => this._wheelListener(event)]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      const touchendListener = () => {\n        var _this$_defaultOptions4;\n        if (this._touchstartTimeout) {\n          clearTimeout(this._touchstartTimeout);\n        }\n        this.hide((_this$_defaultOptions4 = this._defaultOptions) === null || _this$_defaultOptions4 === void 0 ? void 0 : _this$_defaultOptions4.touchendHideDelay);\n      };\n      exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n    }\n    this._addListeners(exitListeners);\n    this._passiveListeners.push(...exitListeners);\n  }\n  _addListeners(listeners) {\n    listeners.forEach(([event, listener]) => {\n      this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n    });\n  }\n  _platformSupportsMouseEvents() {\n    return !this._platform.IOS && !this._platform.ANDROID;\n  }\n  /** Listener for the `wheel` event on the element. */\n  _wheelListener(event) {\n    if (this._isTooltipVisible()) {\n      const elementUnderPointer = this._injector.get(DOCUMENT).elementFromPoint(event.clientX, event.clientY);\n      const element = this._elementRef.nativeElement;\n      // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n      // won't fire if the user scrolls away using the wheel without moving their cursor. We\n      // work around it by finding the element under the user's cursor and closing the tooltip\n      // if it's not the trigger.\n      if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n        this.hide();\n      }\n    }\n  }\n  /** Disables the native browser gestures, based on how the tooltip has been configured. */\n  _disableNativeGesturesIfNecessary() {\n    const gestures = this.touchGestures;\n    if (gestures !== 'off') {\n      const element = this._elementRef.nativeElement;\n      const style = element.style;\n      // If gestures are set to `auto`, we don't disable text selection on inputs and\n      // textareas, because it prevents the user from typing into them on iOS Safari.\n      if (gestures === 'on' || element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA') {\n        style.userSelect = style.msUserSelect = style.webkitUserSelect = style.MozUserSelect = 'none';\n      }\n      // If we have `auto` gestures and the element uses native HTML dragging,\n      // we don't set `-webkit-user-drag` because it prevents the native behavior.\n      if (gestures === 'on' || !element.draggable) {\n        style.webkitUserDrag = 'none';\n      }\n      style.touchAction = 'none';\n      style.webkitTapHighlightColor = 'transparent';\n    }\n  }\n  /** Updates the tooltip's ARIA description based on it current state. */\n  _syncAriaDescription(oldMessage) {\n    if (this._ariaDescriptionPending) {\n      return;\n    }\n    this._ariaDescriptionPending = true;\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, oldMessage, 'tooltip');\n    // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n    // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n    // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n    // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n    if (!this._isDestroyed) {\n      afterNextRender({\n        write: () => {\n          this._ariaDescriptionPending = false;\n          if (this.message && !this.disabled) {\n            this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n          }\n        }\n      }, {\n        injector: this._injector\n      });\n    }\n  }\n}\n_MatTooltip = MatTooltip;\n_defineProperty(MatTooltip, \"\\u0275fac\", function _MatTooltip_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTooltip)();\n});\n_defineProperty(MatTooltip, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTooltip,\n  selectors: [[\"\", \"matTooltip\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-tooltip-trigger\"],\n  hostVars: 2,\n  hostBindings: function _MatTooltip_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-tooltip-disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    position: [0, \"matTooltipPosition\", \"position\"],\n    positionAtOrigin: [0, \"matTooltipPositionAtOrigin\", \"positionAtOrigin\"],\n    disabled: [0, \"matTooltipDisabled\", \"disabled\"],\n    showDelay: [0, \"matTooltipShowDelay\", \"showDelay\"],\n    hideDelay: [0, \"matTooltipHideDelay\", \"hideDelay\"],\n    touchGestures: [0, \"matTooltipTouchGestures\", \"touchGestures\"],\n    message: [0, \"matTooltip\", \"message\"],\n    tooltipClass: [0, \"matTooltipClass\", \"tooltipClass\"]\n  },\n  exportAs: [\"matTooltip\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[matTooltip]',\n      exportAs: 'matTooltip',\n      host: {\n        'class': 'mat-mdc-tooltip-trigger',\n        '[class.mat-mdc-tooltip-disabled]': 'disabled'\n      }\n    }]\n  }], () => [], {\n    position: [{\n      type: Input,\n      args: ['matTooltipPosition']\n    }],\n    positionAtOrigin: [{\n      type: Input,\n      args: ['matTooltipPositionAtOrigin']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matTooltipDisabled']\n    }],\n    showDelay: [{\n      type: Input,\n      args: ['matTooltipShowDelay']\n    }],\n    hideDelay: [{\n      type: Input,\n      args: ['matTooltipHideDelay']\n    }],\n    touchGestures: [{\n      type: Input,\n      args: ['matTooltipTouchGestures']\n    }],\n    message: [{\n      type: Input,\n      args: ['matTooltip']\n    }],\n    tooltipClass: [{\n      type: Input,\n      args: ['matTooltipClass']\n    }]\n  });\n})();\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent {\n  constructor() {\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    /* Whether the tooltip text overflows to multiple lines */\n    _defineProperty(this, \"_isMultiline\", false);\n    /** Message to display in the tooltip */\n    _defineProperty(this, \"message\", void 0);\n    /** Classes to be added to the tooltip. Supports the same syntax as `ngClass`. */\n    _defineProperty(this, \"tooltipClass\", void 0);\n    /** The timeout ID of any current timer set to show the tooltip */\n    _defineProperty(this, \"_showTimeoutId\", void 0);\n    /** The timeout ID of any current timer set to hide the tooltip */\n    _defineProperty(this, \"_hideTimeoutId\", void 0);\n    /** Element that caused the tooltip to open. */\n    _defineProperty(this, \"_triggerElement\", void 0);\n    /** Amount of milliseconds to delay the closing sequence. */\n    _defineProperty(this, \"_mouseLeaveHideDelay\", void 0);\n    /** Whether animations are currently disabled. */\n    _defineProperty(this, \"_animationsDisabled\", void 0);\n    /** Reference to the internal tooltip element. */\n    _defineProperty(this, \"_tooltip\", void 0);\n    /** Whether interactions on the page should close the tooltip */\n    _defineProperty(this, \"_closeOnInteraction\", false);\n    /** Whether the tooltip is currently visible. */\n    _defineProperty(this, \"_isVisible\", false);\n    /** Subject for notifying that the tooltip has been hidden from the view */\n    _defineProperty(this, \"_onHide\", new Subject());\n    /** Name of the show animation and the class that toggles it. */\n    _defineProperty(this, \"_showAnimation\", 'mat-mdc-tooltip-show');\n    /** Name of the hide animation and the class that toggles it. */\n    _defineProperty(this, \"_hideAnimation\", 'mat-mdc-tooltip-hide');\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n  }\n  /**\n   * Shows the tooltip with an animation originating from the provided origin\n   * @param delay Amount of milliseconds to the delay showing the tooltip.\n   */\n  show(delay) {\n    // Cancel the delayed hide if it is scheduled\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n    this._showTimeoutId = setTimeout(() => {\n      this._toggleVisibility(true);\n      this._showTimeoutId = undefined;\n    }, delay);\n  }\n  /**\n   * Begins the animation to hide the tooltip after the provided delay in ms.\n   * @param delay Amount of milliseconds to delay showing the tooltip.\n   */\n  hide(delay) {\n    // Cancel the delayed show if it is scheduled\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n    this._hideTimeoutId = setTimeout(() => {\n      this._toggleVisibility(false);\n      this._hideTimeoutId = undefined;\n    }, delay);\n  }\n  /** Returns an observable that notifies when the tooltip has been hidden from view. */\n  afterHidden() {\n    return this._onHide;\n  }\n  /** Whether the tooltip is being displayed. */\n  isVisible() {\n    return this._isVisible;\n  }\n  ngOnDestroy() {\n    this._cancelPendingAnimations();\n    this._onHide.complete();\n    this._triggerElement = null;\n  }\n  /**\n   * Interactions on the HTML body should close the tooltip immediately as defined in the\n   * material design spec.\n   * https://material.io/design/components/tooltips.html#behavior\n   */\n  _handleBodyInteraction() {\n    if (this._closeOnInteraction) {\n      this.hide(0);\n    }\n  }\n  /**\n   * Marks that the tooltip needs to be checked in the next change detection run.\n   * Mainly used for rendering the initial text before positioning a tooltip, which\n   * can be problematic in components with OnPush change detection.\n   */\n  _markForCheck() {\n    this._changeDetectorRef.markForCheck();\n  }\n  _handleMouseLeave({\n    relatedTarget\n  }) {\n    if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n      if (this.isVisible()) {\n        this.hide(this._mouseLeaveHideDelay);\n      } else {\n        this._finalizeAnimation(false);\n      }\n    }\n  }\n  /**\n   * Callback for when the timeout in this.show() gets completed.\n   * This method is only needed by the mdc-tooltip, and so it is only implemented\n   * in the mdc-tooltip, not here.\n   */\n  _onShow() {\n    this._isMultiline = this._isTooltipMultiline();\n    this._markForCheck();\n  }\n  /** Whether the tooltip text has overflown to the next line */\n  _isTooltipMultiline() {\n    const rect = this._elementRef.nativeElement.getBoundingClientRect();\n    return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n  }\n  /** Event listener dispatched when an animation on the tooltip finishes. */\n  _handleAnimationEnd({\n    animationName\n  }) {\n    if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n      this._finalizeAnimation(animationName === this._showAnimation);\n    }\n  }\n  /** Cancels any pending animation sequences. */\n  _cancelPendingAnimations() {\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n    this._showTimeoutId = this._hideTimeoutId = undefined;\n  }\n  /** Handles the cleanup after an animation has finished. */\n  _finalizeAnimation(toVisible) {\n    if (toVisible) {\n      this._closeOnInteraction = true;\n    } else if (!this.isVisible()) {\n      this._onHide.next();\n    }\n  }\n  /** Toggles the visibility of the tooltip element. */\n  _toggleVisibility(isVisible) {\n    // We set the classes directly here ourselves so that toggling the tooltip state\n    // isn't bound by change detection. This allows us to hide it even if the\n    // view ref has been detached from the CD tree.\n    const tooltip = this._tooltip.nativeElement;\n    const showClass = this._showAnimation;\n    const hideClass = this._hideAnimation;\n    tooltip.classList.remove(isVisible ? hideClass : showClass);\n    tooltip.classList.add(isVisible ? showClass : hideClass);\n    if (this._isVisible !== isVisible) {\n      this._isVisible = isVisible;\n      this._changeDetectorRef.markForCheck();\n    }\n    // It's common for internal apps to disable animations using `* { animation: none !important }`\n    // which can break the opening sequence. Try to detect such cases and work around them.\n    if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n      const styles = getComputedStyle(tooltip);\n      // Use `getPropertyValue` to avoid issues with property renaming.\n      if (styles.getPropertyValue('animation-duration') === '0s' || styles.getPropertyValue('animation-name') === 'none') {\n        this._animationsDisabled = true;\n      }\n    }\n    if (isVisible) {\n      this._onShow();\n    }\n    if (this._animationsDisabled) {\n      tooltip.classList.add('_mat-animation-noopable');\n      this._finalizeAnimation(isVisible);\n    }\n  }\n}\n_TooltipComponent = TooltipComponent;\n_defineProperty(TooltipComponent, \"\\u0275fac\", function _TooltipComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TooltipComponent)();\n});\n_defineProperty(TooltipComponent, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _TooltipComponent,\n  selectors: [[\"mat-tooltip-component\"]],\n  viewQuery: function _TooltipComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tooltip = _t.first);\n    }\n  },\n  hostAttrs: [\"aria-hidden\", \"true\"],\n  hostBindings: function _TooltipComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mouseleave\", function _TooltipComponent_mouseleave_HostBindingHandler($event) {\n        return ctx._handleMouseLeave($event);\n      });\n    }\n  },\n  decls: 4,\n  vars: 4,\n  consts: [[\"tooltip\", \"\"], [1, \"mdc-tooltip\", \"mat-mdc-tooltip\", 3, \"animationend\", \"ngClass\"], [1, \"mat-mdc-tooltip-surface\", \"mdc-tooltip__surface\"]],\n  template: function _TooltipComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵelementStart(0, \"div\", 1, 0);\n      i0.ɵɵlistener(\"animationend\", function _TooltipComponent_Template_div_animationend_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handleAnimationEnd($event));\n      });\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵtext(3);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mdc-tooltip--multiline\", ctx._isMultiline);\n      i0.ɵɵproperty(\"ngClass\", ctx.tooltipClass);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate(ctx.message);\n    }\n  },\n  dependencies: [NgClass],\n  styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tooltip-component',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '(mouseleave)': '_handleMouseLeave($event)',\n        'aria-hidden': 'true'\n      },\n      imports: [NgClass],\n      template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mat-mdc-tooltip-surface mdc-tooltip__surface\\\">{{message}}</div>\\n</div>\\n\",\n      styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\\n\"]\n    }]\n  }], () => [], {\n    _tooltip: [{\n      type: ViewChild,\n      args: ['tooltip', {\n        // Use a static query here since we interact directly with\n        // the DOM which can happen before `ngAfterViewInit`.\n        static: true\n      }]\n    }]\n  });\n})();\nclass MatTooltipModule {}\n_MatTooltipModule = MatTooltipModule;\n_defineProperty(MatTooltipModule, \"\\u0275fac\", function _MatTooltipModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTooltipModule)();\n});\n_defineProperty(MatTooltipModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatTooltipModule,\n  imports: [A11yModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n  exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule]\n}));\n_defineProperty(MatTooltipModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n  imports: [A11yModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [A11yModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n      exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n      providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { MAT_TOOLTIP_SCROLL_STRATEGY as M, SCROLL_THROTTLE_MS as S, TOOLTIP_PANEL_CLASS as T, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY as a, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER as b, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY as c, MAT_TOOLTIP_DEFAULT_OPTIONS as d, MatTooltip as e, TooltipComponent as f, getMatTooltipInvalidPositionError as g, MatTooltipModule as h };\n//# sourceMappingURL=module-C9K6ZqpI.mjs.map", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "ElementRef", "NgZone", "Injector", "ViewContainerRef", "afterNextRender", "Directive", "Input", "ChangeDetectorRef", "ANIMATION_MODULE_TYPE", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "NgModule", "AriaDescriber", "FocusMonitor", "A11yModule", "Overlay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OverlayModule", "CdkScrollableModule", "takeUntil", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "DOCUMENT", "Ng<PERSON><PERSON>", "normalizePassiveListenerOptions", "Platform", "Directionality", "ComponentPortal", "Subject", "M", "MatCommonModule", "SCROLL_THROTTLE_MS", "getMatTooltipInvalidPositionError", "position", "Error", "MAT_TOOLTIP_SCROLL_STRATEGY", "providedIn", "factory", "overlay", "scrollStrategies", "reposition", "scrollThrottle", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER", "provide", "deps", "useFactory", "MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "touchendHideDelay", "MAT_TOOLTIP_DEFAULT_OPTIONS", "TOOLTIP_PANEL_CLASS", "PANEL_CLASS", "passiveListenerOptions", "passive", "MIN_VIEWPORT_TOOLTIP_THRESHOLD", "UNBOUNDED_ANCHOR_GAP", "MIN_HEIGHT", "MAX_WIDTH", "MatTooltip", "_position", "value", "_overlayRef", "_this$_tooltipInstanc", "_updatePosition", "_tooltipInstance", "show", "updatePosition", "positionAt<PERSON><PERSON><PERSON>", "_position<PERSON><PERSON><PERSON><PERSON><PERSON>", "_detach", "disabled", "_disabled", "isDisabled", "hide", "_setupPointerEnterEventsIfNeeded", "_syncAriaDescription", "message", "_showDelay", "_hideDelay", "_mouseLeaveHideDelay", "_message", "oldMessage", "String", "trim", "_isTooltipVisible", "_updateTooltipMessage", "tooltipClass", "_tooltipClass", "_setTooltipClass", "constructor", "_defineProperty", "optional", "TooltipComponent", "defaultOptions", "_defaultOptions", "touchGestures", "_viewportMargin", "ngAfterViewInit", "_viewInitialized", "_focusMonitor", "monitor", "_elementRef", "pipe", "_destroyed", "subscribe", "origin", "_ngZone", "run", "ngOnDestroy", "nativeElement", "_touchstartTimeout", "clearTimeout", "dispose", "_passiveListeners", "for<PERSON>ach", "event", "listener", "removeEventListener", "length", "next", "complete", "_isDestroyed", "_ariaDescriber", "removeDescription", "stopMonitoring", "delay", "_this$_tooltipInstanc2", "_cancelPendingAnimations", "overlayRef", "_createOverlay", "_portal", "_tooltipComponent", "_viewContainerRef", "instance", "attach", "_triggerElement", "afterHidden", "isVisible", "toggle", "undefined", "_this$_defaultOptions", "existingStrategy", "getConfig", "positionStrategy", "_origin", "scrollableAncestors", "_injector", "get", "getAncestorScrollContainers", "strategy", "flexibleConnectedTo", "withTransformOriginOn", "_cssClassPrefix", "withFlexibleDimensions", "withViewportMargin", "withScrollableContainers", "position<PERSON><PERSON>es", "change", "_updateCurrentPositionClass", "connectionPair", "scrollableViewProperties", "isOverlayClipped", "create", "direction", "_dir", "panelClass", "scrollStrategy", "detachments", "outsidePointerEvents", "_this$_tooltipInstanc3", "_handleBodyInteraction", "keydownEvents", "keyCode", "preventDefault", "stopPropagation", "disableTooltipInteractivity", "addPanelClass", "_dirSubscribed", "has<PERSON>tta<PERSON>", "detach", "_get<PERSON><PERSON>in", "_getOverlayPosition", "withPositions", "_addOffset", "_objectSpread", "main", "fallback", "offset", "isLtr", "originY", "offsetY", "originX", "offsetX", "originPosition", "ngDevMode", "x", "y", "_invertPosition", "overlayPosition", "overlayX", "overlayY", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "injector", "newPosition", "_currentPosition", "classPrefix", "removePanelClass", "_platformSupportsMouseEvents", "push", "_setupPointerExitEventsIfNeeded", "point", "_disableNativeGesturesIfNecessary", "_event$targetTouches", "_this$_defaultOptions2", "_this$_defaultOptions3", "touch", "targetTouches", "clientX", "clientY", "DEFAULT_LONGPRESS_DELAY", "setTimeout", "touchLongPressShowDelay", "_addListeners", "_pointerExitEventsInitialized", "exitListeners", "_this$_overlayRef", "newTarget", "relatedTarget", "overlayElement", "contains", "_wheelListener", "touchendListener", "_this$_defaultOptions4", "listeners", "addEventListener", "_platform", "IOS", "ANDROID", "elementUnderPointer", "elementFromPoint", "element", "gestures", "style", "nodeName", "userSelect", "msUserSelect", "webkitUserSelect", "MozUserSelect", "draggable", "webkitUserDrag", "touchAction", "webkitTapHighlightColor", "_ariaDescriptionPending", "write", "describe", "_MatTooltip", "_MatTooltip_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "_MatTooltip_HostBindings", "rf", "ctx", "ɵɵclassProp", "inputs", "exportAs", "ɵsetClassMetadata", "args", "selector", "host", "animationMode", "_animationsDisabled", "_hideTimeoutId", "_showTimeoutId", "_toggleVisibility", "_onHide", "_isVisible", "_closeOnInteraction", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_handleMouseLeave", "_finalizeAnimation", "_onShow", "_isMultiline", "_isTooltipMultiline", "rect", "getBoundingClientRect", "height", "width", "_handleAnimationEnd", "animationName", "_showAnimation", "_hideAnimation", "toVisible", "tooltip", "_tooltip", "showClass", "hideClass", "classList", "remove", "add", "getComputedStyle", "styles", "getPropertyValue", "_TooltipComponent", "_TooltipComponent_Factory", "ɵɵdefineComponent", "viewQuery", "_TooltipComponent_Query", "ɵɵviewQuery", "_c0", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "_TooltipComponent_HostBindings", "ɵɵlistener", "_TooltipComponent_mouseleave_HostBindingHandler", "$event", "decls", "vars", "consts", "template", "_TooltipComponent_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "_TooltipComponent_Template_div_animationend_0_listener", "ɵɵrestoreView", "ɵɵresetView", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate", "dependencies", "encapsulation", "changeDetection", "None", "OnPush", "imports", "static", "MatTooltipModule", "_MatTooltipModule", "_MatTooltipModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "providers", "S", "T", "a", "b", "c", "d", "e", "f", "g", "h"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/module-C9K6ZqpI.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Ng<PERSON><PERSON>, Injector, ViewContainerRef, afterNextRender, Directive, Input, ChangeDetectorRef, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { AriaDescriber, FocusMonitor, A11yModule } from '@angular/cdk/a11y';\nimport { Overlay, ScrollDispatcher, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { DOCUMENT, NgClass } from '@angular/common';\nimport { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n    return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY,\n};\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        showDelay: 0,\n        hideDelay: 0,\n        touchendHideDelay: 1500,\n    };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n    providedIn: 'root',\n    factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({ passive: true });\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _ariaDescriber = inject(AriaDescriber);\n    _focusMonitor = inject(FocusMonitor);\n    _dir = inject(Directionality);\n    _injector = inject(Injector);\n    _viewContainerRef = inject(ViewContainerRef);\n    _defaultOptions = inject(MAT_TOOLTIP_DEFAULT_OPTIONS, {\n        optional: true,\n    });\n    _overlayRef;\n    _tooltipInstance;\n    _portal;\n    _position = 'below';\n    _positionAtOrigin = false;\n    _disabled = false;\n    _tooltipClass;\n    _viewInitialized = false;\n    _pointerExitEventsInitialized = false;\n    _tooltipComponent = TooltipComponent;\n    _viewportMargin = 8;\n    _currentPosition;\n    _cssClassPrefix = 'mat-mdc';\n    _ariaDescriptionPending;\n    _dirSubscribed = false;\n    /** Allows the user to define the position of the tooltip relative to the parent element */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        if (value !== this._position) {\n            this._position = value;\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n                this._tooltipInstance?.show(0);\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    /**\n     * Whether tooltip should be relative to the click or touch origin\n     * instead of outside the element bounding box.\n     */\n    get positionAtOrigin() {\n        return this._positionAtOrigin;\n    }\n    set positionAtOrigin(value) {\n        this._positionAtOrigin = coerceBooleanProperty(value);\n        this._detach();\n        this._overlayRef = null;\n    }\n    /** Disables the display of the tooltip. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        const isDisabled = coerceBooleanProperty(value);\n        if (this._disabled !== isDisabled) {\n            this._disabled = isDisabled;\n            // If tooltip is disabled, hide immediately.\n            if (isDisabled) {\n                this.hide(0);\n            }\n            else {\n                this._setupPointerEnterEventsIfNeeded();\n            }\n            this._syncAriaDescription(this.message);\n        }\n    }\n    /** The default delay in ms before showing the tooltip after show is called */\n    get showDelay() {\n        return this._showDelay;\n    }\n    set showDelay(value) {\n        this._showDelay = coerceNumberProperty(value);\n    }\n    _showDelay;\n    /** The default delay in ms before hiding the tooltip after hide is called */\n    get hideDelay() {\n        return this._hideDelay;\n    }\n    set hideDelay(value) {\n        this._hideDelay = coerceNumberProperty(value);\n        if (this._tooltipInstance) {\n            this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n        }\n    }\n    _hideDelay;\n    /**\n     * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n     * uses a long press gesture to show and hide, however it can conflict with the native browser\n     * gestures. To work around the conflict, Angular Material disables native gestures on the\n     * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n     * elements). The different values for this option configure the touch event handling as follows:\n     * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n     *   browser gestures on particular elements. In particular, it allows text selection on inputs\n     *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n     * - `on` - Enables touch gestures for all elements and disables native\n     *   browser gestures with no exceptions.\n     * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n     *   showing on touch devices.\n     */\n    touchGestures = 'auto';\n    /** The message to be displayed in the tooltip */\n    get message() {\n        return this._message;\n    }\n    set message(value) {\n        const oldMessage = this._message;\n        // If the message is not a string (e.g. number), convert it to a string and trim it.\n        // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n        // away the string-conversion: https://github.com/angular/components/issues/20684\n        this._message = value != null ? String(value).trim() : '';\n        if (!this._message && this._isTooltipVisible()) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n            this._updateTooltipMessage();\n        }\n        this._syncAriaDescription(oldMessage);\n    }\n    _message = '';\n    /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n    get tooltipClass() {\n        return this._tooltipClass;\n    }\n    set tooltipClass(value) {\n        this._tooltipClass = value;\n        if (this._tooltipInstance) {\n            this._setTooltipClass(this._tooltipClass);\n        }\n    }\n    /** Manually-bound passive event listeners. */\n    _passiveListeners = [];\n    /** Timer started at the last `touchstart` event. */\n    _touchstartTimeout = null;\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Whether ngOnDestroyed has been called. */\n    _isDestroyed = false;\n    constructor() {\n        const defaultOptions = this._defaultOptions;\n        if (defaultOptions) {\n            this._showDelay = defaultOptions.showDelay;\n            this._hideDelay = defaultOptions.hideDelay;\n            if (defaultOptions.position) {\n                this.position = defaultOptions.position;\n            }\n            if (defaultOptions.positionAtOrigin) {\n                this.positionAtOrigin = defaultOptions.positionAtOrigin;\n            }\n            if (defaultOptions.touchGestures) {\n                this.touchGestures = defaultOptions.touchGestures;\n            }\n            if (defaultOptions.tooltipClass) {\n                this.tooltipClass = defaultOptions.tooltipClass;\n            }\n        }\n        this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n    }\n    ngAfterViewInit() {\n        // This needs to happen after view init so the initial values for all inputs have been set.\n        this._viewInitialized = true;\n        this._setupPointerEnterEventsIfNeeded();\n        this._focusMonitor\n            .monitor(this._elementRef)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(origin => {\n            // Note that the focus monitor runs outside the Angular zone.\n            if (!origin) {\n                this._ngZone.run(() => this.hide(0));\n            }\n            else if (origin === 'keyboard') {\n                this._ngZone.run(() => this.show());\n            }\n        });\n    }\n    /**\n     * Dispose the tooltip when destroyed.\n     */\n    ngOnDestroy() {\n        const nativeElement = this._elementRef.nativeElement;\n        // Optimization: Do not call clearTimeout unless there is an active timer.\n        if (this._touchstartTimeout) {\n            clearTimeout(this._touchstartTimeout);\n        }\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._tooltipInstance = null;\n        }\n        // Clean up the event listeners set in the constructor\n        this._passiveListeners.forEach(([event, listener]) => {\n            nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n        });\n        this._passiveListeners.length = 0;\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._isDestroyed = true;\n        this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n        this._focusMonitor.stopMonitoring(nativeElement);\n    }\n    /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n    show(delay = this.showDelay, origin) {\n        if (this.disabled || !this.message || this._isTooltipVisible()) {\n            this._tooltipInstance?._cancelPendingAnimations();\n            return;\n        }\n        const overlayRef = this._createOverlay(origin);\n        this._detach();\n        this._portal =\n            this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n        const instance = (this._tooltipInstance = overlayRef.attach(this._portal).instance);\n        instance._triggerElement = this._elementRef.nativeElement;\n        instance._mouseLeaveHideDelay = this._hideDelay;\n        instance\n            .afterHidden()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._setTooltipClass(this._tooltipClass);\n        this._updateTooltipMessage();\n        instance.show(delay);\n    }\n    /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n    hide(delay = this.hideDelay) {\n        const instance = this._tooltipInstance;\n        if (instance) {\n            if (instance.isVisible()) {\n                instance.hide(delay);\n            }\n            else {\n                instance._cancelPendingAnimations();\n                this._detach();\n            }\n        }\n    }\n    /** Shows/hides the tooltip */\n    toggle(origin) {\n        this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n    }\n    /** Returns true if the tooltip is currently visible to the user */\n    _isTooltipVisible() {\n        return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n    }\n    /** Create the overlay config and position strategy */\n    _createOverlay(origin) {\n        if (this._overlayRef) {\n            const existingStrategy = this._overlayRef.getConfig()\n                .positionStrategy;\n            if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n                return this._overlayRef;\n            }\n            this._detach();\n        }\n        const scrollableAncestors = this._injector\n            .get(ScrollDispatcher)\n            .getAncestorScrollContainers(this._elementRef);\n        const overlay = this._injector.get(Overlay);\n        // Create connected position strategy that listens for scroll events to reposition.\n        const strategy = overlay\n            .position()\n            .flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef)\n            .withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`)\n            .withFlexibleDimensions(false)\n            .withViewportMargin(this._viewportMargin)\n            .withScrollableContainers(scrollableAncestors);\n        strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n            this._updateCurrentPositionClass(change.connectionPair);\n            if (this._tooltipInstance) {\n                if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n                    // After position changes occur and the overlay is clipped by\n                    // a parent scrollable then close the tooltip.\n                    this._ngZone.run(() => this.hide(0));\n                }\n            }\n        });\n        this._overlayRef = overlay.create({\n            direction: this._dir,\n            positionStrategy: strategy,\n            panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n            scrollStrategy: this._injector.get(MAT_TOOLTIP_SCROLL_STRATEGY)(),\n        });\n        this._updatePosition(this._overlayRef);\n        this._overlayRef\n            .detachments()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._overlayRef\n            .outsidePointerEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n        this._overlayRef\n            .keydownEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(event => {\n            if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n                event.preventDefault();\n                event.stopPropagation();\n                this._ngZone.run(() => this.hide(0));\n            }\n        });\n        if (this._defaultOptions?.disableTooltipInteractivity) {\n            this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n        }\n        if (!this._dirSubscribed) {\n            this._dirSubscribed = true;\n            this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n                if (this._overlayRef) {\n                    this._updatePosition(this._overlayRef);\n                }\n            });\n        }\n        return this._overlayRef;\n    }\n    /** Detaches the currently-attached tooltip. */\n    _detach() {\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n        }\n        this._tooltipInstance = null;\n    }\n    /** Updates the position of the current tooltip. */\n    _updatePosition(overlayRef) {\n        const position = overlayRef.getConfig().positionStrategy;\n        const origin = this._getOrigin();\n        const overlay = this._getOverlayPosition();\n        position.withPositions([\n            this._addOffset({ ...origin.main, ...overlay.main }),\n            this._addOffset({ ...origin.fallback, ...overlay.fallback }),\n        ]);\n    }\n    /** Adds the configured offset to a position. Used as a hook for child classes. */\n    _addOffset(position) {\n        const offset = UNBOUNDED_ANCHOR_GAP;\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        if (position.originY === 'top') {\n            position.offsetY = -offset;\n        }\n        else if (position.originY === 'bottom') {\n            position.offsetY = offset;\n        }\n        else if (position.originX === 'start') {\n            position.offsetX = isLtr ? -offset : offset;\n        }\n        else if (position.originX === 'end') {\n            position.offsetX = isLtr ? offset : -offset;\n        }\n        return position;\n    }\n    /**\n     * Returns the origin position and a fallback position based on the user's position preference.\n     * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n     */\n    _getOrigin() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let originPosition;\n        if (position == 'above' || position == 'below') {\n            originPosition = { originX: 'center', originY: position == 'above' ? 'top' : 'bottom' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            originPosition = { originX: 'start', originY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            originPosition = { originX: 'end', originY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(originPosition.originX, originPosition.originY);\n        return {\n            main: originPosition,\n            fallback: { originX: x, originY: y },\n        };\n    }\n    /** Returns the overlay position and a fallback position based on the user's preference */\n    _getOverlayPosition() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let overlayPosition;\n        if (position == 'above') {\n            overlayPosition = { overlayX: 'center', overlayY: 'bottom' };\n        }\n        else if (position == 'below') {\n            overlayPosition = { overlayX: 'center', overlayY: 'top' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            overlayPosition = { overlayX: 'end', overlayY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            overlayPosition = { overlayX: 'start', overlayY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n        return {\n            main: overlayPosition,\n            fallback: { overlayX: x, overlayY: y },\n        };\n    }\n    /** Updates the tooltip message and repositions the overlay according to the new message length */\n    _updateTooltipMessage() {\n        // Must wait for the message to be painted to the tooltip so that the overlay can properly\n        // calculate the correct positioning based on the size of the text.\n        if (this._tooltipInstance) {\n            this._tooltipInstance.message = this.message;\n            this._tooltipInstance._markForCheck();\n            afterNextRender(() => {\n                if (this._tooltipInstance) {\n                    this._overlayRef.updatePosition();\n                }\n            }, {\n                injector: this._injector,\n            });\n        }\n    }\n    /** Updates the tooltip class */\n    _setTooltipClass(tooltipClass) {\n        if (this._tooltipInstance) {\n            this._tooltipInstance.tooltipClass = tooltipClass;\n            this._tooltipInstance._markForCheck();\n        }\n    }\n    /** Inverts an overlay position. */\n    _invertPosition(x, y) {\n        if (this.position === 'above' || this.position === 'below') {\n            if (y === 'top') {\n                y = 'bottom';\n            }\n            else if (y === 'bottom') {\n                y = 'top';\n            }\n        }\n        else {\n            if (x === 'end') {\n                x = 'start';\n            }\n            else if (x === 'start') {\n                x = 'end';\n            }\n        }\n        return { x, y };\n    }\n    /** Updates the class on the overlay panel based on the current position of the tooltip. */\n    _updateCurrentPositionClass(connectionPair) {\n        const { overlayY, originX, originY } = connectionPair;\n        let newPosition;\n        // If the overlay is in the middle along the Y axis,\n        // it means that it's either before or after.\n        if (overlayY === 'center') {\n            // Note that since this information is used for styling, we want to\n            // resolve `start` and `end` to their real values, otherwise consumers\n            // would have to remember to do it themselves on each consumption.\n            if (this._dir && this._dir.value === 'rtl') {\n                newPosition = originX === 'end' ? 'left' : 'right';\n            }\n            else {\n                newPosition = originX === 'start' ? 'left' : 'right';\n            }\n        }\n        else {\n            newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n        }\n        if (newPosition !== this._currentPosition) {\n            const overlayRef = this._overlayRef;\n            if (overlayRef) {\n                const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n                overlayRef.removePanelClass(classPrefix + this._currentPosition);\n                overlayRef.addPanelClass(classPrefix + newPosition);\n            }\n            this._currentPosition = newPosition;\n        }\n    }\n    /** Binds the pointer events to the tooltip trigger. */\n    _setupPointerEnterEventsIfNeeded() {\n        // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n        if (this._disabled ||\n            !this.message ||\n            !this._viewInitialized ||\n            this._passiveListeners.length) {\n            return;\n        }\n        // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n        // first tap from firing its click event or can cause the tooltip to open for clicks.\n        if (this._platformSupportsMouseEvents()) {\n            this._passiveListeners.push([\n                'mouseenter',\n                event => {\n                    this._setupPointerExitEventsIfNeeded();\n                    let point = undefined;\n                    if (event.x !== undefined && event.y !== undefined) {\n                        point = event;\n                    }\n                    this.show(undefined, point);\n                },\n            ]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            this._passiveListeners.push([\n                'touchstart',\n                event => {\n                    const touch = event.targetTouches?.[0];\n                    const origin = touch ? { x: touch.clientX, y: touch.clientY } : undefined;\n                    // Note that it's important that we don't `preventDefault` here,\n                    // because it can prevent click events from firing on the element.\n                    this._setupPointerExitEventsIfNeeded();\n                    if (this._touchstartTimeout) {\n                        clearTimeout(this._touchstartTimeout);\n                    }\n                    const DEFAULT_LONGPRESS_DELAY = 500;\n                    this._touchstartTimeout = setTimeout(() => {\n                        this._touchstartTimeout = null;\n                        this.show(undefined, origin);\n                    }, this._defaultOptions?.touchLongPressShowDelay ?? DEFAULT_LONGPRESS_DELAY);\n                },\n            ]);\n        }\n        this._addListeners(this._passiveListeners);\n    }\n    _setupPointerExitEventsIfNeeded() {\n        if (this._pointerExitEventsInitialized) {\n            return;\n        }\n        this._pointerExitEventsInitialized = true;\n        const exitListeners = [];\n        if (this._platformSupportsMouseEvents()) {\n            exitListeners.push([\n                'mouseleave',\n                event => {\n                    const newTarget = event.relatedTarget;\n                    if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n                        this.hide();\n                    }\n                },\n            ], ['wheel', event => this._wheelListener(event)]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            const touchendListener = () => {\n                if (this._touchstartTimeout) {\n                    clearTimeout(this._touchstartTimeout);\n                }\n                this.hide(this._defaultOptions?.touchendHideDelay);\n            };\n            exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n        }\n        this._addListeners(exitListeners);\n        this._passiveListeners.push(...exitListeners);\n    }\n    _addListeners(listeners) {\n        listeners.forEach(([event, listener]) => {\n            this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n        });\n    }\n    _platformSupportsMouseEvents() {\n        return !this._platform.IOS && !this._platform.ANDROID;\n    }\n    /** Listener for the `wheel` event on the element. */\n    _wheelListener(event) {\n        if (this._isTooltipVisible()) {\n            const elementUnderPointer = this._injector\n                .get(DOCUMENT)\n                .elementFromPoint(event.clientX, event.clientY);\n            const element = this._elementRef.nativeElement;\n            // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n            // won't fire if the user scrolls away using the wheel without moving their cursor. We\n            // work around it by finding the element under the user's cursor and closing the tooltip\n            // if it's not the trigger.\n            if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n                this.hide();\n            }\n        }\n    }\n    /** Disables the native browser gestures, based on how the tooltip has been configured. */\n    _disableNativeGesturesIfNecessary() {\n        const gestures = this.touchGestures;\n        if (gestures !== 'off') {\n            const element = this._elementRef.nativeElement;\n            const style = element.style;\n            // If gestures are set to `auto`, we don't disable text selection on inputs and\n            // textareas, because it prevents the user from typing into them on iOS Safari.\n            if (gestures === 'on' || (element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA')) {\n                style.userSelect =\n                    style.msUserSelect =\n                        style.webkitUserSelect =\n                            style.MozUserSelect =\n                                'none';\n            }\n            // If we have `auto` gestures and the element uses native HTML dragging,\n            // we don't set `-webkit-user-drag` because it prevents the native behavior.\n            if (gestures === 'on' || !element.draggable) {\n                style.webkitUserDrag = 'none';\n            }\n            style.touchAction = 'none';\n            style.webkitTapHighlightColor = 'transparent';\n        }\n    }\n    /** Updates the tooltip's ARIA description based on it current state. */\n    _syncAriaDescription(oldMessage) {\n        if (this._ariaDescriptionPending) {\n            return;\n        }\n        this._ariaDescriptionPending = true;\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, oldMessage, 'tooltip');\n        // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n        // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n        // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n        // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n        if (!this._isDestroyed) {\n            afterNextRender({\n                write: () => {\n                    this._ariaDescriptionPending = false;\n                    if (this.message && !this.disabled) {\n                        this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n                    }\n                },\n            }, { injector: this._injector });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTooltip, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTooltip, isStandalone: true, selector: \"[matTooltip]\", inputs: { position: [\"matTooltipPosition\", \"position\"], positionAtOrigin: [\"matTooltipPositionAtOrigin\", \"positionAtOrigin\"], disabled: [\"matTooltipDisabled\", \"disabled\"], showDelay: [\"matTooltipShowDelay\", \"showDelay\"], hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"], touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"], message: [\"matTooltip\", \"message\"], tooltipClass: [\"matTooltipClass\", \"tooltipClass\"] }, host: { properties: { \"class.mat-mdc-tooltip-disabled\": \"disabled\" }, classAttribute: \"mat-mdc-tooltip-trigger\" }, exportAs: [\"matTooltip\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTooltip]',\n                    exportAs: 'matTooltip',\n                    host: {\n                        'class': 'mat-mdc-tooltip-trigger',\n                        '[class.mat-mdc-tooltip-disabled]': 'disabled',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { position: [{\n                type: Input,\n                args: ['matTooltipPosition']\n            }], positionAtOrigin: [{\n                type: Input,\n                args: ['matTooltipPositionAtOrigin']\n            }], disabled: [{\n                type: Input,\n                args: ['matTooltipDisabled']\n            }], showDelay: [{\n                type: Input,\n                args: ['matTooltipShowDelay']\n            }], hideDelay: [{\n                type: Input,\n                args: ['matTooltipHideDelay']\n            }], touchGestures: [{\n                type: Input,\n                args: ['matTooltipTouchGestures']\n            }], message: [{\n                type: Input,\n                args: ['matTooltip']\n            }], tooltipClass: [{\n                type: Input,\n                args: ['matTooltipClass']\n            }] } });\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent {\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    /* Whether the tooltip text overflows to multiple lines */\n    _isMultiline = false;\n    /** Message to display in the tooltip */\n    message;\n    /** Classes to be added to the tooltip. Supports the same syntax as `ngClass`. */\n    tooltipClass;\n    /** The timeout ID of any current timer set to show the tooltip */\n    _showTimeoutId;\n    /** The timeout ID of any current timer set to hide the tooltip */\n    _hideTimeoutId;\n    /** Element that caused the tooltip to open. */\n    _triggerElement;\n    /** Amount of milliseconds to delay the closing sequence. */\n    _mouseLeaveHideDelay;\n    /** Whether animations are currently disabled. */\n    _animationsDisabled;\n    /** Reference to the internal tooltip element. */\n    _tooltip;\n    /** Whether interactions on the page should close the tooltip */\n    _closeOnInteraction = false;\n    /** Whether the tooltip is currently visible. */\n    _isVisible = false;\n    /** Subject for notifying that the tooltip has been hidden from the view */\n    _onHide = new Subject();\n    /** Name of the show animation and the class that toggles it. */\n    _showAnimation = 'mat-mdc-tooltip-show';\n    /** Name of the hide animation and the class that toggles it. */\n    _hideAnimation = 'mat-mdc-tooltip-hide';\n    constructor() {\n        const animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n        this._animationsDisabled = animationMode === 'NoopAnimations';\n    }\n    /**\n     * Shows the tooltip with an animation originating from the provided origin\n     * @param delay Amount of milliseconds to the delay showing the tooltip.\n     */\n    show(delay) {\n        // Cancel the delayed hide if it is scheduled\n        if (this._hideTimeoutId != null) {\n            clearTimeout(this._hideTimeoutId);\n        }\n        this._showTimeoutId = setTimeout(() => {\n            this._toggleVisibility(true);\n            this._showTimeoutId = undefined;\n        }, delay);\n    }\n    /**\n     * Begins the animation to hide the tooltip after the provided delay in ms.\n     * @param delay Amount of milliseconds to delay showing the tooltip.\n     */\n    hide(delay) {\n        // Cancel the delayed show if it is scheduled\n        if (this._showTimeoutId != null) {\n            clearTimeout(this._showTimeoutId);\n        }\n        this._hideTimeoutId = setTimeout(() => {\n            this._toggleVisibility(false);\n            this._hideTimeoutId = undefined;\n        }, delay);\n    }\n    /** Returns an observable that notifies when the tooltip has been hidden from view. */\n    afterHidden() {\n        return this._onHide;\n    }\n    /** Whether the tooltip is being displayed. */\n    isVisible() {\n        return this._isVisible;\n    }\n    ngOnDestroy() {\n        this._cancelPendingAnimations();\n        this._onHide.complete();\n        this._triggerElement = null;\n    }\n    /**\n     * Interactions on the HTML body should close the tooltip immediately as defined in the\n     * material design spec.\n     * https://material.io/design/components/tooltips.html#behavior\n     */\n    _handleBodyInteraction() {\n        if (this._closeOnInteraction) {\n            this.hide(0);\n        }\n    }\n    /**\n     * Marks that the tooltip needs to be checked in the next change detection run.\n     * Mainly used for rendering the initial text before positioning a tooltip, which\n     * can be problematic in components with OnPush change detection.\n     */\n    _markForCheck() {\n        this._changeDetectorRef.markForCheck();\n    }\n    _handleMouseLeave({ relatedTarget }) {\n        if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n            if (this.isVisible()) {\n                this.hide(this._mouseLeaveHideDelay);\n            }\n            else {\n                this._finalizeAnimation(false);\n            }\n        }\n    }\n    /**\n     * Callback for when the timeout in this.show() gets completed.\n     * This method is only needed by the mdc-tooltip, and so it is only implemented\n     * in the mdc-tooltip, not here.\n     */\n    _onShow() {\n        this._isMultiline = this._isTooltipMultiline();\n        this._markForCheck();\n    }\n    /** Whether the tooltip text has overflown to the next line */\n    _isTooltipMultiline() {\n        const rect = this._elementRef.nativeElement.getBoundingClientRect();\n        return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n    }\n    /** Event listener dispatched when an animation on the tooltip finishes. */\n    _handleAnimationEnd({ animationName }) {\n        if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n            this._finalizeAnimation(animationName === this._showAnimation);\n        }\n    }\n    /** Cancels any pending animation sequences. */\n    _cancelPendingAnimations() {\n        if (this._showTimeoutId != null) {\n            clearTimeout(this._showTimeoutId);\n        }\n        if (this._hideTimeoutId != null) {\n            clearTimeout(this._hideTimeoutId);\n        }\n        this._showTimeoutId = this._hideTimeoutId = undefined;\n    }\n    /** Handles the cleanup after an animation has finished. */\n    _finalizeAnimation(toVisible) {\n        if (toVisible) {\n            this._closeOnInteraction = true;\n        }\n        else if (!this.isVisible()) {\n            this._onHide.next();\n        }\n    }\n    /** Toggles the visibility of the tooltip element. */\n    _toggleVisibility(isVisible) {\n        // We set the classes directly here ourselves so that toggling the tooltip state\n        // isn't bound by change detection. This allows us to hide it even if the\n        // view ref has been detached from the CD tree.\n        const tooltip = this._tooltip.nativeElement;\n        const showClass = this._showAnimation;\n        const hideClass = this._hideAnimation;\n        tooltip.classList.remove(isVisible ? hideClass : showClass);\n        tooltip.classList.add(isVisible ? showClass : hideClass);\n        if (this._isVisible !== isVisible) {\n            this._isVisible = isVisible;\n            this._changeDetectorRef.markForCheck();\n        }\n        // It's common for internal apps to disable animations using `* { animation: none !important }`\n        // which can break the opening sequence. Try to detect such cases and work around them.\n        if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n            const styles = getComputedStyle(tooltip);\n            // Use `getPropertyValue` to avoid issues with property renaming.\n            if (styles.getPropertyValue('animation-duration') === '0s' ||\n                styles.getPropertyValue('animation-name') === 'none') {\n                this._animationsDisabled = true;\n            }\n        }\n        if (isVisible) {\n            this._onShow();\n        }\n        if (this._animationsDisabled) {\n            tooltip.classList.add('_mat-animation-noopable');\n            this._finalizeAnimation(isVisible);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TooltipComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: TooltipComponent, isStandalone: true, selector: \"mat-tooltip-component\", host: { attributes: { \"aria-hidden\": \"true\" }, listeners: { \"mouseleave\": \"_handleMouseLeave($event)\" } }, viewQueries: [{ propertyName: \"_tooltip\", first: true, predicate: [\"tooltip\"], descendants: true, static: true }], ngImport: i0, template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mat-mdc-tooltip-surface mdc-tooltip__surface\\\">{{message}}</div>\\n</div>\\n\", styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\\n\"], dependencies: [{ kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: TooltipComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tooltip-component', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '(mouseleave)': '_handleMouseLeave($event)',\n                        'aria-hidden': 'true',\n                    }, imports: [NgClass], template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mat-mdc-tooltip-surface mdc-tooltip__surface\\\">{{message}}</div>\\n</div>\\n\", styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _tooltip: [{\n                type: ViewChild,\n                args: ['tooltip', {\n                        // Use a static query here since we interact directly with\n                        // the DOM which can happen before `ngAfterViewInit`.\n                        static: true,\n                    }]\n            }] } });\n\nclass MatTooltipModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTooltipModule, imports: [A11yModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent], exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTooltipModule, providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [A11yModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [A11yModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n                    exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n                    providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\nexport { MAT_TOOLTIP_SCROLL_STRATEGY as M, SCROLL_THROTTLE_MS as S, TOOLTIP_PANEL_CLASS as T, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY as a, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER as b, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY as c, MAT_TOOLTIP_DEFAULT_OPTIONS as d, MatTooltip as e, TooltipComponent as f, getMatTooltipInvalidPositionError as g, MatTooltipModule as h };\n//# sourceMappingURL=module-C9K6ZqpI.mjs.map\n"], "mappings": ";;;;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC/P,SAASC,aAAa,EAAEC,YAAY,EAAEC,UAAU,QAAQ,mBAAmB;AAC3E,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,sBAAsB;AAC/E,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,QAAQ,EAAEC,OAAO,QAAQ,iBAAiB;AACnD,SAASC,+BAA+B,EAAEC,QAAQ,QAAQ,uBAAuB;AACjF,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;;AAEnE;AACA,MAAMC,kBAAkB,GAAG,EAAE;AAC7B;AACA;AACA;AACA;AACA,SAASC,iCAAiCA,CAACC,QAAQ,EAAE;EACjD,OAAOC,KAAK,CAAC,qBAAqBD,QAAQ,eAAe,CAAC;AAC9D;AACA;AACA,MAAME,2BAA2B,GAAG,IAAIzC,cAAc,CAAC,6BAA6B,EAAE;EAClF0C,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,OAAO,GAAG3C,MAAM,CAACkB,OAAO,CAAC;IAC/B,OAAO,MAAMyB,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC;MAAEC,cAAc,EAAEV;IAAmB,CAAC,CAAC;EAC5F;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASW,mCAAmCA,CAACJ,OAAO,EAAE;EAClD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC;IAAEC,cAAc,EAAEV;EAAmB,CAAC,CAAC;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,4CAA4C,GAAG;EACjDC,OAAO,EAAET,2BAA2B;EACpCU,IAAI,EAAE,CAAChC,OAAO,CAAC;EACfiC,UAAU,EAAEJ;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAASK,mCAAmCA,CAAA,EAAG;EAC3C,OAAO;IACHC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE;EACvB,CAAC;AACL;AACA;AACA,MAAMC,2BAA2B,GAAG,IAAIzD,cAAc,CAAC,6BAA6B,EAAE;EAClF0C,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEU;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMK,mBAAmB,GAAG,uBAAuB;AACnD,MAAMC,WAAW,GAAG,eAAe;AACnC;AACA,MAAMC,sBAAsB,GAAG9B,+BAA+B,CAAC;EAAE+B,OAAO,EAAE;AAAK,CAAC,CAAC;AACjF;AACA;AACA,MAAMC,8BAA8B,GAAG,CAAC;AACxC,MAAMC,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,SAAS,GAAG,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EA2Bb;EACA,IAAI3B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC4B,SAAS;EACzB;EACA,IAAI5B,QAAQA,CAAC6B,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,CAACD,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,GAAGC,KAAK;MACtB,IAAI,IAAI,CAACC,WAAW,EAAE;QAAA,IAAAC,qBAAA;QAClB,IAAI,CAACC,eAAe,CAAC,IAAI,CAACF,WAAW,CAAC;QACtC,CAAAC,qBAAA,OAAI,CAACE,gBAAgB,cAAAF,qBAAA,eAArBA,qBAAA,CAAuBG,IAAI,CAAC,CAAC,CAAC;QAC9B,IAAI,CAACJ,WAAW,CAACK,cAAc,CAAC,CAAC;MACrC;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,iBAAiB;EACjC;EACA,IAAID,gBAAgBA,CAACP,KAAK,EAAE;IACxB,IAAI,CAACQ,iBAAiB,GAAGpD,qBAAqB,CAAC4C,KAAK,CAAC;IACrD,IAAI,CAACS,OAAO,CAAC,CAAC;IACd,IAAI,CAACR,WAAW,GAAG,IAAI;EAC3B;EACA;EACA,IAAIS,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACV,KAAK,EAAE;IAChB,MAAMY,UAAU,GAAGxD,qBAAqB,CAAC4C,KAAK,CAAC;IAC/C,IAAI,IAAI,CAACW,SAAS,KAAKC,UAAU,EAAE;MAC/B,IAAI,CAACD,SAAS,GAAGC,UAAU;MAC3B;MACA,IAAIA,UAAU,EAAE;QACZ,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;MAChB,CAAC,MACI;QACD,IAAI,CAACC,gCAAgC,CAAC,CAAC;MAC3C;MACA,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACC,OAAO,CAAC;IAC3C;EACJ;EACA;EACA,IAAI9B,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC+B,UAAU;EAC1B;EACA,IAAI/B,SAASA,CAACc,KAAK,EAAE;IACjB,IAAI,CAACiB,UAAU,GAAG5D,oBAAoB,CAAC2C,KAAK,CAAC;EACjD;EAEA;EACA,IAAIb,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC+B,UAAU;EAC1B;EACA,IAAI/B,SAASA,CAACa,KAAK,EAAE;IACjB,IAAI,CAACkB,UAAU,GAAG7D,oBAAoB,CAAC2C,KAAK,CAAC;IAC7C,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACe,oBAAoB,GAAG,IAAI,CAACD,UAAU;IAChE;EACJ;EAiBA;EACA,IAAIF,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACI,QAAQ;EACxB;EACA,IAAIJ,OAAOA,CAAChB,KAAK,EAAE;IACf,MAAMqB,UAAU,GAAG,IAAI,CAACD,QAAQ;IAChC;IACA;IACA;IACA,IAAI,CAACA,QAAQ,GAAGpB,KAAK,IAAI,IAAI,GAAGsB,MAAM,CAACtB,KAAK,CAAC,CAACuB,IAAI,CAAC,CAAC,GAAG,EAAE;IACzD,IAAI,CAAC,IAAI,CAACH,QAAQ,IAAI,IAAI,CAACI,iBAAiB,CAAC,CAAC,EAAE;MAC5C,IAAI,CAACX,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACC,gCAAgC,CAAC,CAAC;MACvC,IAAI,CAACW,qBAAqB,CAAC,CAAC;IAChC;IACA,IAAI,CAACV,oBAAoB,CAACM,UAAU,CAAC;EACzC;EAEA;EACA,IAAIK,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAAC1B,KAAK,EAAE;IACpB,IAAI,CAAC2B,aAAa,GAAG3B,KAAK;IAC1B,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACvB,IAAI,CAACwB,gBAAgB,CAAC,IAAI,CAACD,aAAa,CAAC;IAC7C;EACJ;EACA;;EAQAE,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBA9IAjG,MAAM,CAACC,UAAU,CAAC;IAAAgG,eAAA,kBACtBjG,MAAM,CAACE,MAAM,CAAC;IAAA+F,eAAA,oBACZjG,MAAM,CAAC8B,QAAQ,CAAC;IAAAmE,eAAA,yBACXjG,MAAM,CAACe,aAAa,CAAC;IAAAkF,eAAA,wBACtBjG,MAAM,CAACgB,YAAY,CAAC;IAAAiF,eAAA,eAC7BjG,MAAM,CAAC+B,cAAc,CAAC;IAAAkE,eAAA,oBACjBjG,MAAM,CAACG,QAAQ,CAAC;IAAA8F,eAAA,4BACRjG,MAAM,CAACI,gBAAgB,CAAC;IAAA6F,eAAA,0BAC1BjG,MAAM,CAACwD,2BAA2B,EAAE;MAClD0C,QAAQ,EAAE;IACd,CAAC,CAAC;IAAAD,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,oBAIU,OAAO;IAAAA,eAAA,4BACC,KAAK;IAAAA,eAAA,oBACb,KAAK;IAAAA,eAAA;IAAAA,eAAA,2BAEE,KAAK;IAAAA,eAAA,wCACQ,KAAK;IAAAA,eAAA,4BACjBE,gBAAgB;IAAAF,eAAA,0BAClB,CAAC;IAAAA,eAAA;IAAAA,eAAA,0BAED,SAAS;IAAAA,eAAA;IAAAA,eAAA,yBAEV,KAAK;IAAAA,eAAA;IAAAA,eAAA;IAgEtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAbIA,eAAA,wBAcgB,MAAM;IAAAA,eAAA,mBAoBX,EAAE;IAAAA,eAAA,4BAYO,EAAE;IACtB;IAAAA,eAAA,6BACqB,IAAI;IACzB;IAAAA,eAAA,qBACa,IAAIhE,OAAO,CAAC,CAAC;IAC1B;IAAAgE,eAAA,uBACe,KAAK;IAEhB,MAAMG,cAAc,GAAG,IAAI,CAACC,eAAe;IAC3C,IAAID,cAAc,EAAE;MAChB,IAAI,CAAChB,UAAU,GAAGgB,cAAc,CAAC/C,SAAS;MAC1C,IAAI,CAACgC,UAAU,GAAGe,cAAc,CAAC9C,SAAS;MAC1C,IAAI8C,cAAc,CAAC9D,QAAQ,EAAE;QACzB,IAAI,CAACA,QAAQ,GAAG8D,cAAc,CAAC9D,QAAQ;MAC3C;MACA,IAAI8D,cAAc,CAAC1B,gBAAgB,EAAE;QACjC,IAAI,CAACA,gBAAgB,GAAG0B,cAAc,CAAC1B,gBAAgB;MAC3D;MACA,IAAI0B,cAAc,CAACE,aAAa,EAAE;QAC9B,IAAI,CAACA,aAAa,GAAGF,cAAc,CAACE,aAAa;MACrD;MACA,IAAIF,cAAc,CAACP,YAAY,EAAE;QAC7B,IAAI,CAACA,YAAY,GAAGO,cAAc,CAACP,YAAY;MACnD;IACJ;IACA,IAAI,CAACU,eAAe,GAAG1C,8BAA8B;EACzD;EACA2C,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACxB,gCAAgC,CAAC,CAAC;IACvC,IAAI,CAACyB,aAAa,CACbC,OAAO,CAAC,IAAI,CAACC,WAAW,CAAC,CACzBC,IAAI,CAACvF,SAAS,CAAC,IAAI,CAACwF,UAAU,CAAC,CAAC,CAChCC,SAAS,CAACC,MAAM,IAAI;MACrB;MACA,IAAI,CAACA,MAAM,EAAE;QACT,IAAI,CAACC,OAAO,CAACC,GAAG,CAAC,MAAM,IAAI,CAAClC,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,MACI,IAAIgC,MAAM,KAAK,UAAU,EAAE;QAC5B,IAAI,CAACC,OAAO,CAACC,GAAG,CAAC,MAAM,IAAI,CAAC1C,IAAI,CAAC,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACI2C,WAAWA,CAAA,EAAG;IACV,MAAMC,aAAa,GAAG,IAAI,CAACR,WAAW,CAACQ,aAAa;IACpD;IACA,IAAI,IAAI,CAACC,kBAAkB,EAAE;MACzBC,YAAY,CAAC,IAAI,CAACD,kBAAkB,CAAC;IACzC;IACA,IAAI,IAAI,CAACjD,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACmD,OAAO,CAAC,CAAC;MAC1B,IAAI,CAAChD,gBAAgB,GAAG,IAAI;IAChC;IACA;IACA,IAAI,CAACiD,iBAAiB,CAACC,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;MAClDP,aAAa,CAACQ,mBAAmB,CAACF,KAAK,EAAEC,QAAQ,EAAEhE,sBAAsB,CAAC;IAC9E,CAAC,CAAC;IACF,IAAI,CAAC6D,iBAAiB,CAACK,MAAM,GAAG,CAAC;IACjC,IAAI,CAACf,UAAU,CAACgB,IAAI,CAAC,CAAC;IACtB,IAAI,CAAChB,UAAU,CAACiB,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,cAAc,CAACC,iBAAiB,CAACd,aAAa,EAAE,IAAI,CAACjC,OAAO,EAAE,SAAS,CAAC;IAC7E,IAAI,CAACuB,aAAa,CAACyB,cAAc,CAACf,aAAa,CAAC;EACpD;EACA;EACA5C,IAAIA,CAAC4D,KAAK,GAAG,IAAI,CAAC/E,SAAS,EAAE2D,MAAM,EAAE;IACjC,IAAI,IAAI,CAACnC,QAAQ,IAAI,CAAC,IAAI,CAACM,OAAO,IAAI,IAAI,CAACQ,iBAAiB,CAAC,CAAC,EAAE;MAAA,IAAA0C,sBAAA;MAC5D,CAAAA,sBAAA,OAAI,CAAC9D,gBAAgB,cAAA8D,sBAAA,eAArBA,sBAAA,CAAuBC,wBAAwB,CAAC,CAAC;MACjD;IACJ;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,cAAc,CAACxB,MAAM,CAAC;IAC9C,IAAI,CAACpC,OAAO,CAAC,CAAC;IACd,IAAI,CAAC6D,OAAO,GACR,IAAI,CAACA,OAAO,IAAI,IAAIzG,eAAe,CAAC,IAAI,CAAC0G,iBAAiB,EAAE,IAAI,CAACC,iBAAiB,CAAC;IACvF,MAAMC,QAAQ,GAAI,IAAI,CAACrE,gBAAgB,GAAGgE,UAAU,CAACM,MAAM,CAAC,IAAI,CAACJ,OAAO,CAAC,CAACG,QAAS;IACnFA,QAAQ,CAACE,eAAe,GAAG,IAAI,CAAClC,WAAW,CAACQ,aAAa;IACzDwB,QAAQ,CAACtD,oBAAoB,GAAG,IAAI,CAACD,UAAU;IAC/CuD,QAAQ,CACHG,WAAW,CAAC,CAAC,CACblC,IAAI,CAACvF,SAAS,CAAC,IAAI,CAACwF,UAAU,CAAC,CAAC,CAChCC,SAAS,CAAC,MAAM,IAAI,CAACnC,OAAO,CAAC,CAAC,CAAC;IACpC,IAAI,CAACmB,gBAAgB,CAAC,IAAI,CAACD,aAAa,CAAC;IACzC,IAAI,CAACF,qBAAqB,CAAC,CAAC;IAC5BgD,QAAQ,CAACpE,IAAI,CAAC4D,KAAK,CAAC;EACxB;EACA;EACApD,IAAIA,CAACoD,KAAK,GAAG,IAAI,CAAC9E,SAAS,EAAE;IACzB,MAAMsF,QAAQ,GAAG,IAAI,CAACrE,gBAAgB;IACtC,IAAIqE,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAE;QACtBJ,QAAQ,CAAC5D,IAAI,CAACoD,KAAK,CAAC;MACxB,CAAC,MACI;QACDQ,QAAQ,CAACN,wBAAwB,CAAC,CAAC;QACnC,IAAI,CAAC1D,OAAO,CAAC,CAAC;MAClB;IACJ;EACJ;EACA;EACAqE,MAAMA,CAACjC,MAAM,EAAE;IACX,IAAI,CAACrB,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACX,IAAI,CAAC,CAAC,GAAG,IAAI,CAACR,IAAI,CAAC0E,SAAS,EAAElC,MAAM,CAAC;EACzE;EACA;EACArB,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,CAAC,IAAI,CAACpB,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACyE,SAAS,CAAC,CAAC;EACvE;EACA;EACAR,cAAcA,CAACxB,MAAM,EAAE;IAAA,IAAAmC,qBAAA;IACnB,IAAI,IAAI,CAAC/E,WAAW,EAAE;MAClB,MAAMgF,gBAAgB,GAAG,IAAI,CAAChF,WAAW,CAACiF,SAAS,CAAC,CAAC,CAChDC,gBAAgB;MACrB,IAAI,CAAC,CAAC,IAAI,CAAC5E,gBAAgB,IAAI,CAACsC,MAAM,KAAKoC,gBAAgB,CAACG,OAAO,YAAYtJ,UAAU,EAAE;QACvF,OAAO,IAAI,CAACmE,WAAW;MAC3B;MACA,IAAI,CAACQ,OAAO,CAAC,CAAC;IAClB;IACA,MAAM4E,mBAAmB,GAAG,IAAI,CAACC,SAAS,CACrCC,GAAG,CAACvI,gBAAgB,CAAC,CACrBwI,2BAA2B,CAAC,IAAI,CAAC/C,WAAW,CAAC;IAClD,MAAMjE,OAAO,GAAG,IAAI,CAAC8G,SAAS,CAACC,GAAG,CAACxI,OAAO,CAAC;IAC3C;IACA,MAAM0I,QAAQ,GAAGjH,OAAO,CACnBL,QAAQ,CAAC,CAAC,CACVuH,mBAAmB,CAAC,IAAI,CAACnF,gBAAgB,GAAGsC,MAAM,IAAI,IAAI,CAACJ,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAC1FkD,qBAAqB,CAAC,IAAI,IAAI,CAACC,eAAe,UAAU,CAAC,CACzDC,sBAAsB,CAAC,KAAK,CAAC,CAC7BC,kBAAkB,CAAC,IAAI,CAAC1D,eAAe,CAAC,CACxC2D,wBAAwB,CAACV,mBAAmB,CAAC;IAClDI,QAAQ,CAACO,eAAe,CAACtD,IAAI,CAACvF,SAAS,CAAC,IAAI,CAACwF,UAAU,CAAC,CAAC,CAACC,SAAS,CAACqD,MAAM,IAAI;MAC1E,IAAI,CAACC,2BAA2B,CAACD,MAAM,CAACE,cAAc,CAAC;MACvD,IAAI,IAAI,CAAC/F,gBAAgB,EAAE;QACvB,IAAI6F,MAAM,CAACG,wBAAwB,CAACC,gBAAgB,IAAI,IAAI,CAACjG,gBAAgB,CAACyE,SAAS,CAAC,CAAC,EAAE;UACvF;UACA;UACA,IAAI,CAAC/B,OAAO,CAACC,GAAG,CAAC,MAAM,IAAI,CAAClC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACZ,WAAW,GAAGzB,OAAO,CAAC8H,MAAM,CAAC;MAC9BC,SAAS,EAAE,IAAI,CAACC,IAAI;MACpBrB,gBAAgB,EAAEM,QAAQ;MAC1BgB,UAAU,EAAE,GAAG,IAAI,CAACb,eAAe,IAAIrG,WAAW,EAAE;MACpDmH,cAAc,EAAE,IAAI,CAACpB,SAAS,CAACC,GAAG,CAAClH,2BAA2B,CAAC,CAAC;IACpE,CAAC,CAAC;IACF,IAAI,CAAC8B,eAAe,CAAC,IAAI,CAACF,WAAW,CAAC;IACtC,IAAI,CAACA,WAAW,CACX0G,WAAW,CAAC,CAAC,CACbjE,IAAI,CAACvF,SAAS,CAAC,IAAI,CAACwF,UAAU,CAAC,CAAC,CAChCC,SAAS,CAAC,MAAM,IAAI,CAACnC,OAAO,CAAC,CAAC,CAAC;IACpC,IAAI,CAACR,WAAW,CACX2G,oBAAoB,CAAC,CAAC,CACtBlE,IAAI,CAACvF,SAAS,CAAC,IAAI,CAACwF,UAAU,CAAC,CAAC,CAChCC,SAAS,CAAC;MAAA,IAAAiE,sBAAA;MAAA,QAAAA,sBAAA,GAAM,IAAI,CAACzG,gBAAgB,cAAAyG,sBAAA,uBAArBA,sBAAA,CAAuBC,sBAAsB,CAAC,CAAC;IAAA,EAAC;IACrE,IAAI,CAAC7G,WAAW,CACX8G,aAAa,CAAC,CAAC,CACfrE,IAAI,CAACvF,SAAS,CAAC,IAAI,CAACwF,UAAU,CAAC,CAAC,CAChCC,SAAS,CAACW,KAAK,IAAI;MACpB,IAAI,IAAI,CAAC/B,iBAAiB,CAAC,CAAC,IAAI+B,KAAK,CAACyD,OAAO,KAAK1J,MAAM,IAAI,CAACC,cAAc,CAACgG,KAAK,CAAC,EAAE;QAChFA,KAAK,CAAC0D,cAAc,CAAC,CAAC;QACtB1D,KAAK,CAAC2D,eAAe,CAAC,CAAC;QACvB,IAAI,CAACpE,OAAO,CAACC,GAAG,CAAC,MAAM,IAAI,CAAClC,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,KAAAmE,qBAAA,GAAI,IAAI,CAAC9C,eAAe,cAAA8C,qBAAA,eAApBA,qBAAA,CAAsBmC,2BAA2B,EAAE;MACnD,IAAI,CAAClH,WAAW,CAACmH,aAAa,CAAC,GAAG,IAAI,CAACxB,eAAe,gCAAgC,CAAC;IAC3F;IACA,IAAI,CAAC,IAAI,CAACyB,cAAc,EAAE;MACtB,IAAI,CAACA,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACb,IAAI,CAACP,MAAM,CAACvD,IAAI,CAACvF,SAAS,CAAC,IAAI,CAACwF,UAAU,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;QAC9D,IAAI,IAAI,CAAC3C,WAAW,EAAE;UAClB,IAAI,CAACE,eAAe,CAAC,IAAI,CAACF,WAAW,CAAC;QAC1C;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACA,WAAW;EAC3B;EACA;EACAQ,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACR,WAAW,IAAI,IAAI,CAACA,WAAW,CAACqH,WAAW,CAAC,CAAC,EAAE;MACpD,IAAI,CAACrH,WAAW,CAACsH,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAACnH,gBAAgB,GAAG,IAAI;EAChC;EACA;EACAD,eAAeA,CAACiE,UAAU,EAAE;IACxB,MAAMjG,QAAQ,GAAGiG,UAAU,CAACc,SAAS,CAAC,CAAC,CAACC,gBAAgB;IACxD,MAAMtC,MAAM,GAAG,IAAI,CAAC2E,UAAU,CAAC,CAAC;IAChC,MAAMhJ,OAAO,GAAG,IAAI,CAACiJ,mBAAmB,CAAC,CAAC;IAC1CtJ,QAAQ,CAACuJ,aAAa,CAAC,CACnB,IAAI,CAACC,UAAU,CAAAC,aAAA,CAAAA,aAAA,KAAM/E,MAAM,CAACgF,IAAI,GAAKrJ,OAAO,CAACqJ,IAAI,CAAE,CAAC,EACpD,IAAI,CAACF,UAAU,CAAAC,aAAA,CAAAA,aAAA,KAAM/E,MAAM,CAACiF,QAAQ,GAAKtJ,OAAO,CAACsJ,QAAQ,CAAE,CAAC,CAC/D,CAAC;EACN;EACA;EACAH,UAAUA,CAACxJ,QAAQ,EAAE;IACjB,MAAM4J,MAAM,GAAGpI,oBAAoB;IACnC,MAAMqI,KAAK,GAAG,CAAC,IAAI,CAACxB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACxG,KAAK,IAAI,KAAK;IACpD,IAAI7B,QAAQ,CAAC8J,OAAO,KAAK,KAAK,EAAE;MAC5B9J,QAAQ,CAAC+J,OAAO,GAAG,CAACH,MAAM;IAC9B,CAAC,MACI,IAAI5J,QAAQ,CAAC8J,OAAO,KAAK,QAAQ,EAAE;MACpC9J,QAAQ,CAAC+J,OAAO,GAAGH,MAAM;IAC7B,CAAC,MACI,IAAI5J,QAAQ,CAACgK,OAAO,KAAK,OAAO,EAAE;MACnChK,QAAQ,CAACiK,OAAO,GAAGJ,KAAK,GAAG,CAACD,MAAM,GAAGA,MAAM;IAC/C,CAAC,MACI,IAAI5J,QAAQ,CAACgK,OAAO,KAAK,KAAK,EAAE;MACjChK,QAAQ,CAACiK,OAAO,GAAGJ,KAAK,GAAGD,MAAM,GAAG,CAACA,MAAM;IAC/C;IACA,OAAO5J,QAAQ;EACnB;EACA;AACJ;AACA;AACA;EACIqJ,UAAUA,CAAA,EAAG;IACT,MAAMQ,KAAK,GAAG,CAAC,IAAI,CAACxB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACxG,KAAK,IAAI,KAAK;IACpD,MAAM7B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIkK,cAAc;IAClB,IAAIlK,QAAQ,IAAI,OAAO,IAAIA,QAAQ,IAAI,OAAO,EAAE;MAC5CkK,cAAc,GAAG;QAAEF,OAAO,EAAE,QAAQ;QAAEF,OAAO,EAAE9J,QAAQ,IAAI,OAAO,GAAG,KAAK,GAAG;MAAS,CAAC;IAC3F,CAAC,MACI,IAAIA,QAAQ,IAAI,QAAQ,IACxBA,QAAQ,IAAI,MAAM,IAAI6J,KAAM,IAC5B7J,QAAQ,IAAI,OAAO,IAAI,CAAC6J,KAAM,EAAE;MACjCK,cAAc,GAAG;QAAEF,OAAO,EAAE,OAAO;QAAEF,OAAO,EAAE;MAAS,CAAC;IAC5D,CAAC,MACI,IAAI9J,QAAQ,IAAI,OAAO,IACvBA,QAAQ,IAAI,OAAO,IAAI6J,KAAM,IAC7B7J,QAAQ,IAAI,MAAM,IAAI,CAAC6J,KAAM,EAAE;MAChCK,cAAc,GAAG;QAAEF,OAAO,EAAE,KAAK;QAAEF,OAAO,EAAE;MAAS,CAAC;IAC1D,CAAC,MACI,IAAI,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMpK,iCAAiC,CAACC,QAAQ,CAAC;IACrD;IACA,MAAM;MAAEoK,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAACJ,cAAc,CAACF,OAAO,EAAEE,cAAc,CAACJ,OAAO,CAAC;IACrF,OAAO;MACHJ,IAAI,EAAEQ,cAAc;MACpBP,QAAQ,EAAE;QAAEK,OAAO,EAAEI,CAAC;QAAEN,OAAO,EAAEO;MAAE;IACvC,CAAC;EACL;EACA;EACAf,mBAAmBA,CAAA,EAAG;IAClB,MAAMO,KAAK,GAAG,CAAC,IAAI,CAACxB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACxG,KAAK,IAAI,KAAK;IACpD,MAAM7B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIuK,eAAe;IACnB,IAAIvK,QAAQ,IAAI,OAAO,EAAE;MACrBuK,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAChE,CAAC,MACI,IAAIzK,QAAQ,IAAI,OAAO,EAAE;MAC1BuK,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAM,CAAC;IAC7D,CAAC,MACI,IAAIzK,QAAQ,IAAI,QAAQ,IACxBA,QAAQ,IAAI,MAAM,IAAI6J,KAAM,IAC5B7J,QAAQ,IAAI,OAAO,IAAI,CAAC6J,KAAM,EAAE;MACjCU,eAAe,GAAG;QAAEC,QAAQ,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAC7D,CAAC,MACI,IAAIzK,QAAQ,IAAI,OAAO,IACvBA,QAAQ,IAAI,OAAO,IAAI6J,KAAM,IAC7B7J,QAAQ,IAAI,MAAM,IAAI,CAAC6J,KAAM,EAAE;MAChCU,eAAe,GAAG;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAC/D,CAAC,MACI,IAAI,OAAON,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMpK,iCAAiC,CAACC,QAAQ,CAAC;IACrD;IACA,MAAM;MAAEoK,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAACC,eAAe,CAACC,QAAQ,EAAED,eAAe,CAACE,QAAQ,CAAC;IACzF,OAAO;MACHf,IAAI,EAAEa,eAAe;MACrBZ,QAAQ,EAAE;QAAEa,QAAQ,EAAEJ,CAAC;QAAEK,QAAQ,EAAEJ;MAAE;IACzC,CAAC;EACL;EACA;EACA/G,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,IAAI,IAAI,CAACrB,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACY,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5C,IAAI,CAACZ,gBAAgB,CAACyI,aAAa,CAAC,CAAC;MACrC3M,eAAe,CAAC,MAAM;QAClB,IAAI,IAAI,CAACkE,gBAAgB,EAAE;UACvB,IAAI,CAACH,WAAW,CAACK,cAAc,CAAC,CAAC;QACrC;MACJ,CAAC,EAAE;QACCwI,QAAQ,EAAE,IAAI,CAACxD;MACnB,CAAC,CAAC;IACN;EACJ;EACA;EACA1D,gBAAgBA,CAACF,YAAY,EAAE;IAC3B,IAAI,IAAI,CAACtB,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACsB,YAAY,GAAGA,YAAY;MACjD,IAAI,CAACtB,gBAAgB,CAACyI,aAAa,CAAC,CAAC;IACzC;EACJ;EACA;EACAJ,eAAeA,CAACF,CAAC,EAAEC,CAAC,EAAE;IAClB,IAAI,IAAI,CAACrK,QAAQ,KAAK,OAAO,IAAI,IAAI,CAACA,QAAQ,KAAK,OAAO,EAAE;MACxD,IAAIqK,CAAC,KAAK,KAAK,EAAE;QACbA,CAAC,GAAG,QAAQ;MAChB,CAAC,MACI,IAAIA,CAAC,KAAK,QAAQ,EAAE;QACrBA,CAAC,GAAG,KAAK;MACb;IACJ,CAAC,MACI;MACD,IAAID,CAAC,KAAK,KAAK,EAAE;QACbA,CAAC,GAAG,OAAO;MACf,CAAC,MACI,IAAIA,CAAC,KAAK,OAAO,EAAE;QACpBA,CAAC,GAAG,KAAK;MACb;IACJ;IACA,OAAO;MAAEA,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACAtC,2BAA2BA,CAACC,cAAc,EAAE;IACxC,MAAM;MAAEyC,QAAQ;MAAET,OAAO;MAAEF;IAAQ,CAAC,GAAG9B,cAAc;IACrD,IAAI4C,WAAW;IACf;IACA;IACA,IAAIH,QAAQ,KAAK,QAAQ,EAAE;MACvB;MACA;MACA;MACA,IAAI,IAAI,CAACpC,IAAI,IAAI,IAAI,CAACA,IAAI,CAACxG,KAAK,KAAK,KAAK,EAAE;QACxC+I,WAAW,GAAGZ,OAAO,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;MACtD,CAAC,MACI;QACDY,WAAW,GAAGZ,OAAO,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;MACxD;IACJ,CAAC,MACI;MACDY,WAAW,GAAGH,QAAQ,KAAK,QAAQ,IAAIX,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;IAChF;IACA,IAAIc,WAAW,KAAK,IAAI,CAACC,gBAAgB,EAAE;MACvC,MAAM5E,UAAU,GAAG,IAAI,CAACnE,WAAW;MACnC,IAAImE,UAAU,EAAE;QACZ,MAAM6E,WAAW,GAAG,GAAG,IAAI,CAACrD,eAAe,IAAIrG,WAAW,GAAG;QAC7D6E,UAAU,CAAC8E,gBAAgB,CAACD,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC;QAChE5E,UAAU,CAACgD,aAAa,CAAC6B,WAAW,GAAGF,WAAW,CAAC;MACvD;MACA,IAAI,CAACC,gBAAgB,GAAGD,WAAW;IACvC;EACJ;EACA;EACAjI,gCAAgCA,CAAA,EAAG;IAC/B;IACA,IAAI,IAAI,CAACH,SAAS,IACd,CAAC,IAAI,CAACK,OAAO,IACb,CAAC,IAAI,CAACsB,gBAAgB,IACtB,IAAI,CAACe,iBAAiB,CAACK,MAAM,EAAE;MAC/B;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACyF,4BAA4B,CAAC,CAAC,EAAE;MACrC,IAAI,CAAC9F,iBAAiB,CAAC+F,IAAI,CAAC,CACxB,YAAY,EACZ7F,KAAK,IAAI;QACL,IAAI,CAAC8F,+BAA+B,CAAC,CAAC;QACtC,IAAIC,KAAK,GAAGvE,SAAS;QACrB,IAAIxB,KAAK,CAACgF,CAAC,KAAKxD,SAAS,IAAIxB,KAAK,CAACiF,CAAC,KAAKzD,SAAS,EAAE;UAChDuE,KAAK,GAAG/F,KAAK;QACjB;QACA,IAAI,CAAClD,IAAI,CAAC0E,SAAS,EAAEuE,KAAK,CAAC;MAC/B,CAAC,CACJ,CAAC;IACN,CAAC,MACI,IAAI,IAAI,CAACnH,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAACoH,iCAAiC,CAAC,CAAC;MACxC,IAAI,CAAClG,iBAAiB,CAAC+F,IAAI,CAAC,CACxB,YAAY,EACZ7F,KAAK,IAAI;QAAA,IAAAiG,oBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACL,MAAMC,KAAK,IAAAH,oBAAA,GAAGjG,KAAK,CAACqG,aAAa,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAsB,CAAC,CAAC;QACtC,MAAM3G,MAAM,GAAG8G,KAAK,GAAG;UAAEpB,CAAC,EAAEoB,KAAK,CAACE,OAAO;UAAErB,CAAC,EAAEmB,KAAK,CAACG;QAAQ,CAAC,GAAG/E,SAAS;QACzE;QACA;QACA,IAAI,CAACsE,+BAA+B,CAAC,CAAC;QACtC,IAAI,IAAI,CAACnG,kBAAkB,EAAE;UACzBC,YAAY,CAAC,IAAI,CAACD,kBAAkB,CAAC;QACzC;QACA,MAAM6G,uBAAuB,GAAG,GAAG;QACnC,IAAI,CAAC7G,kBAAkB,GAAG8G,UAAU,CAAC,MAAM;UACvC,IAAI,CAAC9G,kBAAkB,GAAG,IAAI;UAC9B,IAAI,CAAC7C,IAAI,CAAC0E,SAAS,EAAElC,MAAM,CAAC;QAChC,CAAC,GAAA4G,sBAAA,IAAAC,sBAAA,GAAE,IAAI,CAACxH,eAAe,cAAAwH,sBAAA,uBAApBA,sBAAA,CAAsBO,uBAAuB,cAAAR,sBAAA,cAAAA,sBAAA,GAAIM,uBAAuB,CAAC;MAChF,CAAC,CACJ,CAAC;IACN;IACA,IAAI,CAACG,aAAa,CAAC,IAAI,CAAC7G,iBAAiB,CAAC;EAC9C;EACAgG,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAACc,6BAA6B,EAAE;MACpC;IACJ;IACA,IAAI,CAACA,6BAA6B,GAAG,IAAI;IACzC,MAAMC,aAAa,GAAG,EAAE;IACxB,IAAI,IAAI,CAACjB,4BAA4B,CAAC,CAAC,EAAE;MACrCiB,aAAa,CAAChB,IAAI,CAAC,CACf,YAAY,EACZ7F,KAAK,IAAI;QAAA,IAAA8G,iBAAA;QACL,MAAMC,SAAS,GAAG/G,KAAK,CAACgH,aAAa;QACrC,IAAI,CAACD,SAAS,IAAI,GAAAD,iBAAA,GAAC,IAAI,CAACpK,WAAW,cAAAoK,iBAAA,eAAhBA,iBAAA,CAAkBG,cAAc,CAACC,QAAQ,CAACH,SAAS,CAAC,GAAE;UACrE,IAAI,CAACzJ,IAAI,CAAC,CAAC;QACf;MACJ,CAAC,CACJ,EAAE,CAAC,OAAO,EAAE0C,KAAK,IAAI,IAAI,CAACmH,cAAc,CAACnH,KAAK,CAAC,CAAC,CAAC;IACtD,CAAC,MACI,IAAI,IAAI,CAACpB,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAACoH,iCAAiC,CAAC,CAAC;MACxC,MAAMoB,gBAAgB,GAAGA,CAAA,KAAM;QAAA,IAAAC,sBAAA;QAC3B,IAAI,IAAI,CAAC1H,kBAAkB,EAAE;UACzBC,YAAY,CAAC,IAAI,CAACD,kBAAkB,CAAC;QACzC;QACA,IAAI,CAACrC,IAAI,EAAA+J,sBAAA,GAAC,IAAI,CAAC1I,eAAe,cAAA0I,sBAAA,uBAApBA,sBAAA,CAAsBxL,iBAAiB,CAAC;MACtD,CAAC;MACDgL,aAAa,CAAChB,IAAI,CAAC,CAAC,UAAU,EAAEuB,gBAAgB,CAAC,EAAE,CAAC,aAAa,EAAEA,gBAAgB,CAAC,CAAC;IACzF;IACA,IAAI,CAACT,aAAa,CAACE,aAAa,CAAC;IACjC,IAAI,CAAC/G,iBAAiB,CAAC+F,IAAI,CAAC,GAAGgB,aAAa,CAAC;EACjD;EACAF,aAAaA,CAACW,SAAS,EAAE;IACrBA,SAAS,CAACvH,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;MACrC,IAAI,CAACf,WAAW,CAACQ,aAAa,CAAC6H,gBAAgB,CAACvH,KAAK,EAAEC,QAAQ,EAAEhE,sBAAsB,CAAC;IAC5F,CAAC,CAAC;EACN;EACA2J,4BAA4BA,CAAA,EAAG;IAC3B,OAAO,CAAC,IAAI,CAAC4B,SAAS,CAACC,GAAG,IAAI,CAAC,IAAI,CAACD,SAAS,CAACE,OAAO;EACzD;EACA;EACAP,cAAcA,CAACnH,KAAK,EAAE;IAClB,IAAI,IAAI,CAAC/B,iBAAiB,CAAC,CAAC,EAAE;MAC1B,MAAM0J,mBAAmB,GAAG,IAAI,CAAC5F,SAAS,CACrCC,GAAG,CAAC/H,QAAQ,CAAC,CACb2N,gBAAgB,CAAC5H,KAAK,CAACsG,OAAO,EAAEtG,KAAK,CAACuG,OAAO,CAAC;MACnD,MAAMsB,OAAO,GAAG,IAAI,CAAC3I,WAAW,CAACQ,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAIiI,mBAAmB,KAAKE,OAAO,IAAI,CAACA,OAAO,CAACX,QAAQ,CAACS,mBAAmB,CAAC,EAAE;QAC3E,IAAI,CAACrK,IAAI,CAAC,CAAC;MACf;IACJ;EACJ;EACA;EACA0I,iCAAiCA,CAAA,EAAG;IAChC,MAAM8B,QAAQ,GAAG,IAAI,CAAClJ,aAAa;IACnC,IAAIkJ,QAAQ,KAAK,KAAK,EAAE;MACpB,MAAMD,OAAO,GAAG,IAAI,CAAC3I,WAAW,CAACQ,aAAa;MAC9C,MAAMqI,KAAK,GAAGF,OAAO,CAACE,KAAK;MAC3B;MACA;MACA,IAAID,QAAQ,KAAK,IAAI,IAAKD,OAAO,CAACG,QAAQ,KAAK,OAAO,IAAIH,OAAO,CAACG,QAAQ,KAAK,UAAW,EAAE;QACxFD,KAAK,CAACE,UAAU,GACZF,KAAK,CAACG,YAAY,GACdH,KAAK,CAACI,gBAAgB,GAClBJ,KAAK,CAACK,aAAa,GACf,MAAM;MAC1B;MACA;MACA;MACA,IAAIN,QAAQ,KAAK,IAAI,IAAI,CAACD,OAAO,CAACQ,SAAS,EAAE;QACzCN,KAAK,CAACO,cAAc,GAAG,MAAM;MACjC;MACAP,KAAK,CAACQ,WAAW,GAAG,MAAM;MAC1BR,KAAK,CAACS,uBAAuB,GAAG,aAAa;IACjD;EACJ;EACA;EACAhL,oBAAoBA,CAACM,UAAU,EAAE;IAC7B,IAAI,IAAI,CAAC2K,uBAAuB,EAAE;MAC9B;IACJ;IACA,IAAI,CAACA,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAAClI,cAAc,CAACC,iBAAiB,CAAC,IAAI,CAACtB,WAAW,CAACQ,aAAa,EAAE5B,UAAU,EAAE,SAAS,CAAC;IAC5F;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACwC,YAAY,EAAE;MACpB3H,eAAe,CAAC;QACZ+P,KAAK,EAAEA,CAAA,KAAM;UACT,IAAI,CAACD,uBAAuB,GAAG,KAAK;UACpC,IAAI,IAAI,CAAChL,OAAO,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE;YAChC,IAAI,CAACoD,cAAc,CAACoI,QAAQ,CAAC,IAAI,CAACzJ,WAAW,CAACQ,aAAa,EAAE,IAAI,CAACjC,OAAO,EAAE,SAAS,CAAC;UACzF;QACJ;MACJ,CAAC,EAAE;QAAE8H,QAAQ,EAAE,IAAI,CAACxD;MAAU,CAAC,CAAC;IACpC;EACJ;AAGJ;AAAC6G,WAAA,GAznBKrM,UAAU;AAAAgC,eAAA,CAAVhC,UAAU,wBAAAsM,oBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAunBuFvM,WAAU;AAAA;AAAAgC,eAAA,CAvnB3GhC,UAAU,8BA0nBiEnE,EAAE,CAAA2Q,iBAAA;EAAAC,IAAA,EAFQzM,WAAU;EAAA0M,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAEpBlR,EAAE,CAAAoR,WAAA,6BAAAD,GAAA,CAAApM,QAFiB,CAAC;IAAA;EAAA;EAAAsM,MAAA;IAAA7O,QAAA;IAAAoC,gBAAA;IAAAG,QAAA;IAAAxB,SAAA;IAAAC,SAAA;IAAAgD,aAAA;IAAAnB,OAAA;IAAAU,YAAA;EAAA;EAAAuL,QAAA;AAAA;AAErG;EAAA,QAAA3E,SAAA,oBAAAA,SAAA,KAAiF3M,EAAE,CAAAuR,iBAAA,CAAQpN,UAAU,EAAc,CAAC;IACxGyM,IAAI,EAAEpQ,SAAS;IACfgR,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBH,QAAQ,EAAE,YAAY;MACtBI,IAAI,EAAE;QACF,OAAO,EAAE,yBAAyB;QAClC,kCAAkC,EAAE;MACxC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAElP,QAAQ,EAAE,CAAC;MACnDoO,IAAI,EAAEnQ,KAAK;MACX+Q,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE5M,gBAAgB,EAAE,CAAC;MACnBgM,IAAI,EAAEnQ,KAAK;MACX+Q,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEzM,QAAQ,EAAE,CAAC;MACX6L,IAAI,EAAEnQ,KAAK;MACX+Q,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEjO,SAAS,EAAE,CAAC;MACZqN,IAAI,EAAEnQ,KAAK;MACX+Q,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEhO,SAAS,EAAE,CAAC;MACZoN,IAAI,EAAEnQ,KAAK;MACX+Q,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEhL,aAAa,EAAE,CAAC;MAChBoK,IAAI,EAAEnQ,KAAK;MACX+Q,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEnM,OAAO,EAAE,CAAC;MACVuL,IAAI,EAAEnQ,KAAK;MACX+Q,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEzL,YAAY,EAAE,CAAC;MACf6K,IAAI,EAAEnQ,KAAK;MACX+Q,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMnL,gBAAgB,CAAC;EA+BnBH,WAAWA,CAAA,EAAG;IAAAC,eAAA,6BA9BOjG,MAAM,CAACQ,iBAAiB,CAAC;IAAAyF,eAAA,sBAChCjG,MAAM,CAACC,UAAU,CAAC;IAChC;IAAAgG,eAAA,uBACe,KAAK;IACpB;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,8BACsB,KAAK;IAC3B;IAAAA,eAAA,qBACa,KAAK;IAClB;IAAAA,eAAA,kBACU,IAAIhE,OAAO,CAAC,CAAC;IACvB;IAAAgE,eAAA,yBACiB,sBAAsB;IACvC;IAAAA,eAAA,yBACiB,sBAAsB;IAEnC,MAAMwL,aAAa,GAAGzR,MAAM,CAACS,qBAAqB,EAAE;MAAEyF,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,IAAI,CAACwL,mBAAmB,GAAGD,aAAa,KAAK,gBAAgB;EACjE;EACA;AACJ;AACA;AACA;EACIjN,IAAIA,CAAC4D,KAAK,EAAE;IACR;IACA,IAAI,IAAI,CAACuJ,cAAc,IAAI,IAAI,EAAE;MAC7BrK,YAAY,CAAC,IAAI,CAACqK,cAAc,CAAC;IACrC;IACA,IAAI,CAACC,cAAc,GAAGzD,UAAU,CAAC,MAAM;MACnC,IAAI,CAAC0D,iBAAiB,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACD,cAAc,GAAG1I,SAAS;IACnC,CAAC,EAAEd,KAAK,CAAC;EACb;EACA;AACJ;AACA;AACA;EACIpD,IAAIA,CAACoD,KAAK,EAAE;IACR;IACA,IAAI,IAAI,CAACwJ,cAAc,IAAI,IAAI,EAAE;MAC7BtK,YAAY,CAAC,IAAI,CAACsK,cAAc,CAAC;IACrC;IACA,IAAI,CAACD,cAAc,GAAGxD,UAAU,CAAC,MAAM;MACnC,IAAI,CAAC0D,iBAAiB,CAAC,KAAK,CAAC;MAC7B,IAAI,CAACF,cAAc,GAAGzI,SAAS;IACnC,CAAC,EAAEd,KAAK,CAAC;EACb;EACA;EACAW,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC+I,OAAO;EACvB;EACA;EACA9I,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC+I,UAAU;EAC1B;EACA5K,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmB,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACwJ,OAAO,CAAC/J,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACe,eAAe,GAAG,IAAI;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACImC,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC+G,mBAAmB,EAAE;MAC1B,IAAI,CAAChN,IAAI,CAAC,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIgI,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACiF,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACAC,iBAAiBA,CAAC;IAAEzD;EAAc,CAAC,EAAE;IACjC,IAAI,CAACA,aAAa,IAAI,CAAC,IAAI,CAAC5F,eAAe,CAAC8F,QAAQ,CAACF,aAAa,CAAC,EAAE;MACjE,IAAI,IAAI,CAAC1F,SAAS,CAAC,CAAC,EAAE;QAClB,IAAI,CAAChE,IAAI,CAAC,IAAI,CAACM,oBAAoB,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAAC8M,kBAAkB,CAAC,KAAK,CAAC;MAClC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC9C,IAAI,CAACvF,aAAa,CAAC,CAAC;EACxB;EACA;EACAuF,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,IAAI,GAAG,IAAI,CAAC5L,WAAW,CAACQ,aAAa,CAACqL,qBAAqB,CAAC,CAAC;IACnE,OAAOD,IAAI,CAACE,MAAM,GAAG3O,UAAU,IAAIyO,IAAI,CAACG,KAAK,IAAI3O,SAAS;EAC9D;EACA;EACA4O,mBAAmBA,CAAC;IAAEC;EAAc,CAAC,EAAE;IACnC,IAAIA,aAAa,KAAK,IAAI,CAACC,cAAc,IAAID,aAAa,KAAK,IAAI,CAACE,cAAc,EAAE;MAChF,IAAI,CAACX,kBAAkB,CAACS,aAAa,KAAK,IAAI,CAACC,cAAc,CAAC;IAClE;EACJ;EACA;EACAxK,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACsJ,cAAc,IAAI,IAAI,EAAE;MAC7BtK,YAAY,CAAC,IAAI,CAACsK,cAAc,CAAC;IACrC;IACA,IAAI,IAAI,CAACD,cAAc,IAAI,IAAI,EAAE;MAC7BrK,YAAY,CAAC,IAAI,CAACqK,cAAc,CAAC;IACrC;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACD,cAAc,GAAGzI,SAAS;EACzD;EACA;EACAkJ,kBAAkBA,CAACY,SAAS,EAAE;IAC1B,IAAIA,SAAS,EAAE;MACX,IAAI,CAAChB,mBAAmB,GAAG,IAAI;IACnC,CAAC,MACI,IAAI,CAAC,IAAI,CAAChJ,SAAS,CAAC,CAAC,EAAE;MACxB,IAAI,CAAC8I,OAAO,CAAChK,IAAI,CAAC,CAAC;IACvB;EACJ;EACA;EACA+J,iBAAiBA,CAAC7I,SAAS,EAAE;IACzB;IACA;IACA;IACA,MAAMiK,OAAO,GAAG,IAAI,CAACC,QAAQ,CAAC9L,aAAa;IAC3C,MAAM+L,SAAS,GAAG,IAAI,CAACL,cAAc;IACrC,MAAMM,SAAS,GAAG,IAAI,CAACL,cAAc;IACrCE,OAAO,CAACI,SAAS,CAACC,MAAM,CAACtK,SAAS,GAAGoK,SAAS,GAAGD,SAAS,CAAC;IAC3DF,OAAO,CAACI,SAAS,CAACE,GAAG,CAACvK,SAAS,GAAGmK,SAAS,GAAGC,SAAS,CAAC;IACxD,IAAI,IAAI,CAACrB,UAAU,KAAK/I,SAAS,EAAE;MAC/B,IAAI,CAAC+I,UAAU,GAAG/I,SAAS;MAC3B,IAAI,CAACiJ,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAIlJ,SAAS,IAAI,CAAC,IAAI,CAAC0I,mBAAmB,IAAI,OAAO8B,gBAAgB,KAAK,UAAU,EAAE;MAClF,MAAMC,MAAM,GAAGD,gBAAgB,CAACP,OAAO,CAAC;MACxC;MACA,IAAIQ,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,IAAI,IACtDD,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,MAAM,EAAE;QACtD,IAAI,CAAChC,mBAAmB,GAAG,IAAI;MACnC;IACJ;IACA,IAAI1I,SAAS,EAAE;MACX,IAAI,CAACqJ,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,IAAI,CAACX,mBAAmB,EAAE;MAC1BuB,OAAO,CAACI,SAAS,CAACE,GAAG,CAAC,yBAAyB,CAAC;MAChD,IAAI,CAACnB,kBAAkB,CAACpJ,SAAS,CAAC;IACtC;EACJ;AAGJ;AAAC2K,iBAAA,GAjLKxN,gBAAgB;AAAAF,eAAA,CAAhBE,gBAAgB,wBAAAyN,0BAAApD,iBAAA;EAAA,YAAAA,iBAAA,IA+KiFrK,iBAAgB;AAAA;AAAAF,eAAA,CA/KjHE,gBAAgB,8BAvC2DrG,EAAE,CAAA+T,iBAAA;EAAAnD,IAAA,EAuNQvK,iBAAgB;EAAAwK,SAAA;EAAAmD,SAAA,WAAAC,wBAAA/C,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAvN1BlR,EAAE,CAAAkU,WAAA,CAAAC,GAAA;IAAA;IAAA,IAAAjD,EAAA;MAAA,IAAAkD,EAAA;MAAFpU,EAAE,CAAAqU,cAAA,CAAAD,EAAA,GAAFpU,EAAE,CAAAsU,WAAA,QAAAnD,GAAA,CAAAiC,QAAA,GAAAgB,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAzD,SAAA,kBAuNsH,MAAM;EAAAE,YAAA,WAAAwD,+BAAAtD,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAvN9HlR,EAAE,CAAAyU,UAAA,wBAAAC,gDAAAC,MAAA;QAAA,OAuNQxD,GAAA,CAAAkB,iBAAA,CAAAsC,MAAwB,CAAC;MAAA,CAAV,CAAC;IAAA;EAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,2BAAA9D,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAA,MAAA+D,GAAA,GAvN1BjV,EAAE,CAAAkV,gBAAA;MAAFlV,EAAE,CAAAmV,cAAA,eAuNogB,CAAC;MAvNvgBnV,EAAE,CAAAyU,UAAA,0BAAAW,uDAAAT,MAAA;QAAF3U,EAAE,CAAAqV,aAAA,CAAAJ,GAAA;QAAA,OAAFjV,EAAE,CAAAsV,WAAA,CAuNobnE,GAAA,CAAA2B,mBAAA,CAAA6B,MAA0B,CAAC;MAAA,CAAC,CAAC;MAvNnd3U,EAAE,CAAAmV,cAAA,YAuNokB,CAAC;MAvNvkBnV,EAAE,CAAAuV,MAAA,EAuN+kB,CAAC;MAvNllBvV,EAAE,CAAAwV,YAAA,CAuNqlB,CAAC,CAAO,CAAC;IAAA;IAAA,IAAAtE,EAAA;MAvNhmBlR,EAAE,CAAAoR,WAAA,2BAAAD,GAAA,CAAAqB,YAuNmgB,CAAC;MAvNtgBxS,EAAE,CAAAyV,UAAA,YAAAtE,GAAA,CAAApL,YAuN8Z,CAAC;MAvNja/F,EAAE,CAAA0V,SAAA,EAuN+kB,CAAC;MAvNllB1V,EAAE,CAAA2V,iBAAA,CAAAxE,GAAA,CAAA9L,OAuN+kB,CAAC;IAAA;EAAA;EAAAuQ,YAAA,GAAs2E9T,OAAO;EAAA6R,MAAA;EAAAkC,aAAA;EAAAC,eAAA;AAAA;AAEhhG;EAAA,QAAAnJ,SAAA,oBAAAA,SAAA,KAzNiF3M,EAAE,CAAAuR,iBAAA,CAyNQlL,gBAAgB,EAAc,CAAC;IAC9GuK,IAAI,EAAEhQ,SAAS;IACf4Q,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,uBAAuB;MAAEoE,aAAa,EAAEhV,iBAAiB,CAACkV,IAAI;MAAED,eAAe,EAAEhV,uBAAuB,CAACkV,MAAM;MAAEtE,IAAI,EAAE;QAC9H,cAAc,EAAE,2BAA2B;QAC3C,aAAa,EAAE;MACnB,CAAC;MAAEuE,OAAO,EAAE,CAACnU,OAAO,CAAC;MAAEiT,QAAQ,EAAE,0RAA0R;MAAEpB,MAAM,EAAE,CAAC,6xEAA6xE;IAAE,CAAC;EAClnF,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEP,QAAQ,EAAE,CAAC;MACnDxC,IAAI,EAAE7P,SAAS;MACfyQ,IAAI,EAAE,CAAC,SAAS,EAAE;QACV;QACA;QACA0E,MAAM,EAAE;MACZ,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,gBAAgB,CAAC;AAItBC,iBAAA,GAJKD,gBAAgB;AAAAhQ,eAAA,CAAhBgQ,gBAAgB,wBAAAE,0BAAA3F,iBAAA;EAAA,YAAAA,iBAAA,IACiFyF,iBAAgB;AAAA;AAAAhQ,eAAA,CADjHgQ,gBAAgB,8BAxO2DnW,EAAE,CAAAsW,gBAAA;EAAA1F,IAAA,EA0OqBuF,iBAAgB;EAAAF,OAAA,GAAY9U,UAAU,EAAEG,aAAa,EAAEe,eAAe,EAAE8B,UAAU,EAAEkC,gBAAgB;EAAAkQ,OAAA,GAAapS,UAAU,EAAEkC,gBAAgB,EAAEhE,eAAe,EAAEd,mBAAmB;AAAA;AAAA4E,eAAA,CAFrRgQ,gBAAgB,8BAxO2DnW,EAAE,CAAAwW,gBAAA;EAAAC,SAAA,EA2OkD,CAACvT,4CAA4C,CAAC;EAAA+S,OAAA,GAAY9U,UAAU,EAAEG,aAAa,EAAEe,eAAe,EAAEA,eAAe,EAAEd,mBAAmB;AAAA;AAE/Q;EAAA,QAAAoL,SAAA,oBAAAA,SAAA,KA7OiF3M,EAAE,CAAAuR,iBAAA,CA6OQ4E,gBAAgB,EAAc,CAAC;IAC9GvF,IAAI,EAAE5P,QAAQ;IACdwQ,IAAI,EAAE,CAAC;MACCyE,OAAO,EAAE,CAAC9U,UAAU,EAAEG,aAAa,EAAEe,eAAe,EAAE8B,UAAU,EAAEkC,gBAAgB,CAAC;MACnFkQ,OAAO,EAAE,CAACpS,UAAU,EAAEkC,gBAAgB,EAAEhE,eAAe,EAAEd,mBAAmB,CAAC;MAC7EkV,SAAS,EAAE,CAACvT,4CAA4C;IAC5D,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASR,2BAA2B,IAAIN,CAAC,EAAEE,kBAAkB,IAAIoU,CAAC,EAAE/S,mBAAmB,IAAIgT,CAAC,EAAE1T,mCAAmC,IAAI2T,CAAC,EAAE1T,4CAA4C,IAAI2T,CAAC,EAAEvT,mCAAmC,IAAIwT,CAAC,EAAEpT,2BAA2B,IAAIqT,CAAC,EAAE5S,UAAU,IAAI6S,CAAC,EAAE3Q,gBAAgB,IAAI4Q,CAAC,EAAE1U,iCAAiC,IAAI2U,CAAC,EAAEf,gBAAgB,IAAIgB,CAAC;AAC5W", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}