{"ast": null, "code": "var _EntityPickerComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./entity-picker.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./entity-picker.component.scss?ngResource\";\nimport { ChangeDetectorRef, Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { UntypedFormControl } from '@angular/forms';\nimport { combineLatest, Subject, timer } from 'rxjs';\nimport { map, share, take, takeUntil } from 'rxjs/operators';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubEntityService } from '../../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwHubInitService } from '../../services/sw-hub-init/sw-hub-init.service';\nlet EntityPickerComponent = (_EntityPickerComponent = class EntityPickerComponent {\n  constructor(entityService, hubService, cdr, authService) {\n    this.entityService = entityService;\n    this.hubService = hubService;\n    this.cdr = cdr;\n    this.authService = authService;\n    this.showSearch = false;\n    this.menuClass = 'entity-picker-menu';\n    this.searchPlaceholder = 'Search';\n    this.settingsClick = new EventEmitter();\n    this.entities = [];\n    this.searchInputControl = new UntypedFormControl();\n    this.isSettingsDisabled = true;\n    this.destroyed$ = new Subject();\n    this.items$ = this.entityService.items$.pipe(takeUntil(this.destroyed$), share());\n    this.items$.subscribe(() => {\n      if (Array.isArray(this.entityService.entities) && this.entityService.entities.length) {\n        this.entityService.expandedEntities.clear();\n        this.toggleCollapse(this.entityService.entities[0].id);\n      }\n    });\n  }\n  ngOnInit() {\n    this.searchInputControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(searchString => {\n      if (searchString) {\n        var _this$entities;\n        this.entities = this.entityService.entities.filter(option => {\n          return option.name && option.name.toLowerCase().indexOf(searchString.toLowerCase()) > -1;\n        });\n        this.entityService.expandedEntities.clear();\n        (_this$entities = this.entities) === null || _this$entities === void 0 || _this$entities.forEach(entity => this.expandToRoot(entity.id));\n        this.entities = this.entityService.entities.filter(item => this.entityService.expandedEntities.get(item.id));\n        this.entityService.foundedEntities = this.entities;\n      } else {\n        this.entityService.expandedEntities.clear();\n        this.toggleCollapse(this.entityService.entities[0].id);\n      }\n    });\n    this.entityService.brief$.pipe(takeUntil(this.destroyed$)).subscribe(item => {\n      this.entity = item;\n      this.cdr.detectChanges();\n    });\n    combineLatest([this.entityService.brief$, this.entityService.itemSelected$]).pipe(map(([brief, item]) => item || brief), takeUntil(this.destroyed$)).subscribe(item => {\n      var _this$selected;\n      if (item && item.id !== ((_this$selected = this.selected) === null || _this$selected === void 0 ? void 0 : _this$selected.id)) {\n        this.hubService.sendEntityId(item.id);\n      }\n      this.selected = item;\n      this.isSettingsDisabled = (item === null || item === void 0 ? void 0 : item.path) === ':' && this.authService.isSuperAdmin;\n      this.cdr.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  get isReseller() {\n    if (!this.entity) {\n      return false;\n    }\n    return this.entity.type === 'entity';\n  }\n  onSettingsClick(event) {\n    var _this$selected2;\n    if (event) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n    this.settingsClick.emit((_this$selected2 = this.selected) === null || _this$selected2 === void 0 ? void 0 : _this$selected2.path);\n  }\n  select(item) {\n    var _this$pickerTrigger;\n    if (item !== undefined) {\n      this.hubService.sendEntityId(item.id);\n      this.searchInputControl.setValue('');\n      this.entityService.use(item.id, true);\n    }\n    (_this$pickerTrigger = this.pickerTrigger) === null || _this$pickerTrigger === void 0 || _this$pickerTrigger.closeMenu();\n  }\n  stopPropagation(event) {\n    event.stopPropagation();\n  }\n  closeChildren(current) {\n    this.entityService.expandedEntities.set(current, false);\n    if (this.entityService.entitiesObject) {\n      const {\n        children\n      } = this.entityService.entitiesObject[current];\n      children.forEach(id => {\n        this.closeChildren(id);\n      });\n    }\n  }\n  expandToRoot(id) {\n    if (id) {\n      this.entityService.expandedEntities.set(id, true);\n      if (this.entityService.entitiesObject) {\n        return this.expandToRoot(this.entityService.entitiesObject[id].parentId);\n      }\n    }\n  }\n  toggleCollapse(id) {\n    const isCollapsed = !this.entityService.expandedEntities.get(id);\n    if (isCollapsed) {\n      this.entityService.expandedEntities.set(id, true);\n    } else {\n      this.closeChildren(id);\n    }\n    this.entities = this.searchInputControl.value ? this.entityService.foundedEntities.filter((item, index) => {\n      return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n    }) : this.entityService.entities.filter((item, index) => {\n      return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n    });\n  }\n  onItemClick(event, row) {\n    this.stopPropagation(event);\n    if (row.level !== 0) {\n      this.toggleCollapse(row.id);\n    }\n  }\n  onClosed() {\n    this.searchInputControl.setValue(null);\n  }\n  isEntityExpanded(id) {\n    return !!this.entityService.expandedEntities.get(id);\n  }\n  onOpen() {\n    var _this$selected3;\n    if ((_this$selected3 = this.selected) !== null && _this$selected3 !== void 0 && _this$selected3.id) {\n      this.expandToRoot(this.selected.id);\n      this.entities = this.entityService.entities.filter((item, index) => {\n        return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n      });\n      timer(100).pipe(take(1)).subscribe(() => {\n        var _this$selected4;\n        if ((_this$selected4 = this.selected) !== null && _this$selected4 !== void 0 && _this$selected4.id) {\n          const el = document.getElementById(this.selected.id);\n          el === null || el === void 0 || el.scrollIntoView({\n            block: 'center'\n          });\n        }\n      });\n    }\n  }\n}, _EntityPickerComponent.ctorParameters = () => [{\n  type: SwHubEntityService\n}, {\n  type: SwHubInitService\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: SwHubAuthService\n}], _EntityPickerComponent.propDecorators = {\n  showSearch: [{\n    type: Input\n  }],\n  menuClass: [{\n    type: Input\n  }],\n  searchPlaceholder: [{\n    type: Input\n  }],\n  settingsClick: [{\n    type: Output\n  }],\n  pickerTrigger: [{\n    type: ViewChild,\n    args: ['pickerTrigger']\n  }],\n  searchRef: [{\n    type: ViewChild,\n    args: ['search']\n  }]\n}, _EntityPickerComponent);\nEntityPickerComponent = __decorate([Component({\n  selector: 'lib-swui-entity-picker',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], EntityPickerComponent);\nexport { EntityPickerComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectorRef", "Component", "EventEmitter", "Input", "Output", "ViewChild", "UntypedFormControl", "combineLatest", "Subject", "timer", "map", "share", "take", "takeUntil", "SwHubAuthService", "SwHubEntityService", "SwHubInitService", "EntityPickerComponent", "_EntityPickerComponent", "constructor", "entityService", "hubService", "cdr", "authService", "showSearch", "menuClass", "searchPlaceholder", "settingsClick", "entities", "searchInputControl", "isSettingsDisabled", "destroyed$", "items$", "pipe", "subscribe", "Array", "isArray", "length", "expandedEntities", "clear", "toggleCollapse", "id", "ngOnInit", "valueChanges", "searchString", "_this$entities", "filter", "option", "name", "toLowerCase", "indexOf", "for<PERSON>ach", "entity", "expandToRoot", "item", "get", "foundedEntities", "brief$", "detectChanges", "itemSelected$", "brief", "_this$selected", "selected", "sendEntityId", "path", "isSuperAdmin", "ngOnDestroy", "next", "undefined", "complete", "is<PERSON><PERSON>ller", "type", "onSettingsClick", "event", "_this$selected2", "preventDefault", "stopPropagation", "emit", "select", "_this$pickerTrigger", "setValue", "use", "picker<PERSON>rigger", "closeMenu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "set", "entitiesObject", "children", "parentId", "isCollapsed", "value", "index", "onItemClick", "row", "level", "onClosed", "isEntityExpanded", "onOpen", "_this$selected3", "_this$selected4", "el", "document", "getElementById", "scrollIntoView", "block", "ctorParameters", "propDecorators", "args", "searchRef", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/entity-picker/entity-picker.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./entity-picker.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./entity-picker.component.scss?ngResource\";\nimport { ChangeDetectorRef, Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { UntypedFormControl } from '@angular/forms';\nimport { combineLatest, Subject, timer } from 'rxjs';\nimport { map, share, take, takeUntil } from 'rxjs/operators';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubEntityService } from '../../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwHubInitService } from '../../services/sw-hub-init/sw-hub-init.service';\nlet EntityPickerComponent = class EntityPickerComponent {\n    constructor(entityService, hubService, cdr, authService) {\n        this.entityService = entityService;\n        this.hubService = hubService;\n        this.cdr = cdr;\n        this.authService = authService;\n        this.showSearch = false;\n        this.menuClass = 'entity-picker-menu';\n        this.searchPlaceholder = 'Search';\n        this.settingsClick = new EventEmitter();\n        this.entities = [];\n        this.searchInputControl = new UntypedFormControl();\n        this.isSettingsDisabled = true;\n        this.destroyed$ = new Subject();\n        this.items$ = this.entityService.items$.pipe(takeUntil(this.destroyed$), share());\n        this.items$.subscribe(() => {\n            if (Array.isArray(this.entityService.entities) && this.entityService.entities.length) {\n                this.entityService.expandedEntities.clear();\n                this.toggleCollapse(this.entityService.entities[0].id);\n            }\n        });\n    }\n    ngOnInit() {\n        this.searchInputControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(searchString => {\n            if (searchString) {\n                this.entities = this.entityService.entities.filter((option) => {\n                    return option.name && option.name.toLowerCase().indexOf(searchString.toLowerCase()) > -1;\n                });\n                this.entityService.expandedEntities.clear();\n                this.entities?.forEach(entity => this.expandToRoot(entity.id));\n                this.entities = this.entityService.entities.filter(item => this.entityService.expandedEntities.get(item.id));\n                this.entityService.foundedEntities = this.entities;\n            }\n            else {\n                this.entityService.expandedEntities.clear();\n                this.toggleCollapse(this.entityService.entities[0].id);\n            }\n        });\n        this.entityService.brief$.pipe(takeUntil(this.destroyed$)).subscribe(item => {\n            this.entity = item;\n            this.cdr.detectChanges();\n        });\n        combineLatest([this.entityService.brief$, this.entityService.itemSelected$]).pipe(map(([brief, item]) => item || brief), takeUntil(this.destroyed$)).subscribe(item => {\n            if (item && item.id !== this.selected?.id) {\n                this.hubService.sendEntityId(item.id);\n            }\n            this.selected = item;\n            this.isSettingsDisabled = item?.path === ':' && this.authService.isSuperAdmin;\n            this.cdr.detectChanges();\n        });\n    }\n    ngOnDestroy() {\n        this.destroyed$.next(undefined);\n        this.destroyed$.complete();\n    }\n    get isReseller() {\n        if (!this.entity) {\n            return false;\n        }\n        return this.entity.type === 'entity';\n    }\n    onSettingsClick(event) {\n        if (event) {\n            event.preventDefault();\n            event.stopPropagation();\n        }\n        this.settingsClick.emit(this.selected?.path);\n    }\n    select(item) {\n        if (item !== undefined) {\n            this.hubService.sendEntityId(item.id);\n            this.searchInputControl.setValue('');\n            this.entityService.use(item.id, true);\n        }\n        this.pickerTrigger?.closeMenu();\n    }\n    stopPropagation(event) {\n        event.stopPropagation();\n    }\n    closeChildren(current) {\n        this.entityService.expandedEntities.set(current, false);\n        if (this.entityService.entitiesObject) {\n            const { children } = this.entityService.entitiesObject[current];\n            children.forEach((id) => {\n                this.closeChildren(id);\n            });\n        }\n    }\n    expandToRoot(id) {\n        if (id) {\n            this.entityService.expandedEntities.set(id, true);\n            if (this.entityService.entitiesObject) {\n                return this.expandToRoot(this.entityService.entitiesObject[id].parentId);\n            }\n        }\n    }\n    toggleCollapse(id) {\n        const isCollapsed = !this.entityService.expandedEntities.get(id);\n        if (isCollapsed) {\n            this.entityService.expandedEntities.set(id, true);\n        }\n        else {\n            this.closeChildren(id);\n        }\n        this.entities = this.searchInputControl.value ?\n            this.entityService.foundedEntities.filter((item, index) => {\n                return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n            }) :\n            this.entityService.entities.filter((item, index) => {\n                return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n            });\n    }\n    onItemClick(event, row) {\n        this.stopPropagation(event);\n        if (row.level !== 0) {\n            this.toggleCollapse(row.id);\n        }\n    }\n    onClosed() {\n        this.searchInputControl.setValue(null);\n    }\n    isEntityExpanded(id) {\n        return !!this.entityService.expandedEntities.get(id);\n    }\n    onOpen() {\n        if (this.selected?.id) {\n            this.expandToRoot(this.selected.id);\n            this.entities = this.entityService.entities.filter((item, index) => {\n                return this.entityService.expandedEntities.get(item.id) || this.entityService.expandedEntities.get(item.parentId) || !index;\n            });\n            timer(100)\n                .pipe(take(1))\n                .subscribe(() => {\n                if (this.selected?.id) {\n                    const el = document.getElementById(this.selected.id);\n                    el?.scrollIntoView({ block: 'center' });\n                }\n            });\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: SwHubEntityService },\n        { type: SwHubInitService },\n        { type: ChangeDetectorRef },\n        { type: SwHubAuthService }\n    ]; }\n    static { this.propDecorators = {\n        showSearch: [{ type: Input }],\n        menuClass: [{ type: Input }],\n        searchPlaceholder: [{ type: Input }],\n        settingsClick: [{ type: Output }],\n        pickerTrigger: [{ type: ViewChild, args: ['pickerTrigger',] }],\n        searchRef: [{ type: ViewChild, args: ['search',] }]\n    }; }\n};\nEntityPickerComponent = __decorate([\n    Component({\n        selector: 'lib-swui-entity-picker',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], EntityPickerComponent);\nexport { EntityPickerComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,SAASC,iBAAiB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACpG,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,aAAa,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACpD,SAASC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAC5D,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,IAAIC,qBAAqB,IAAAC,sBAAA,GAAG,MAAMD,qBAAqB,CAAC;EACpDE,WAAWA,CAACC,aAAa,EAAEC,UAAU,EAAEC,GAAG,EAAEC,WAAW,EAAE;IACrD,IAAI,CAACH,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,oBAAoB;IACrC,IAAI,CAACC,iBAAiB,GAAG,QAAQ;IACjC,IAAI,CAACC,aAAa,GAAG,IAAIzB,YAAY,CAAC,CAAC;IACvC,IAAI,CAAC0B,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,kBAAkB,GAAG,IAAIvB,kBAAkB,CAAC,CAAC;IAClD,IAAI,CAACwB,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,UAAU,GAAG,IAAIvB,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACwB,MAAM,GAAG,IAAI,CAACZ,aAAa,CAACY,MAAM,CAACC,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACkB,UAAU,CAAC,EAAEpB,KAAK,CAAC,CAAC,CAAC;IACjF,IAAI,CAACqB,MAAM,CAACE,SAAS,CAAC,MAAM;MACxB,IAAIC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAChB,aAAa,CAACQ,QAAQ,CAAC,IAAI,IAAI,CAACR,aAAa,CAACQ,QAAQ,CAACS,MAAM,EAAE;QAClF,IAAI,CAACjB,aAAa,CAACkB,gBAAgB,CAACC,KAAK,CAAC,CAAC;QAC3C,IAAI,CAACC,cAAc,CAAC,IAAI,CAACpB,aAAa,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACa,EAAE,CAAC;MAC1D;IACJ,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACb,kBAAkB,CAACc,YAAY,CAACV,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACkB,UAAU,CAAC,CAAC,CAACG,SAAS,CAACU,YAAY,IAAI;MAC5F,IAAIA,YAAY,EAAE;QAAA,IAAAC,cAAA;QACd,IAAI,CAACjB,QAAQ,GAAG,IAAI,CAACR,aAAa,CAACQ,QAAQ,CAACkB,MAAM,CAAEC,MAAM,IAAK;UAC3D,OAAOA,MAAM,CAACC,IAAI,IAAID,MAAM,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAACN,YAAY,CAACK,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5F,CAAC,CAAC;QACF,IAAI,CAAC7B,aAAa,CAACkB,gBAAgB,CAACC,KAAK,CAAC,CAAC;QAC3C,CAAAM,cAAA,OAAI,CAACjB,QAAQ,cAAAiB,cAAA,eAAbA,cAAA,CAAeM,OAAO,CAACC,MAAM,IAAI,IAAI,CAACC,YAAY,CAACD,MAAM,CAACX,EAAE,CAAC,CAAC;QAC9D,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACR,aAAa,CAACQ,QAAQ,CAACkB,MAAM,CAACQ,IAAI,IAAI,IAAI,CAAClC,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACb,EAAE,CAAC,CAAC;QAC5G,IAAI,CAACrB,aAAa,CAACoC,eAAe,GAAG,IAAI,CAAC5B,QAAQ;MACtD,CAAC,MACI;QACD,IAAI,CAACR,aAAa,CAACkB,gBAAgB,CAACC,KAAK,CAAC,CAAC;QAC3C,IAAI,CAACC,cAAc,CAAC,IAAI,CAACpB,aAAa,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACa,EAAE,CAAC;MAC1D;IACJ,CAAC,CAAC;IACF,IAAI,CAACrB,aAAa,CAACqC,MAAM,CAACxB,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACkB,UAAU,CAAC,CAAC,CAACG,SAAS,CAACoB,IAAI,IAAI;MACzE,IAAI,CAACF,MAAM,GAAGE,IAAI;MAClB,IAAI,CAAChC,GAAG,CAACoC,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACFnD,aAAa,CAAC,CAAC,IAAI,CAACa,aAAa,CAACqC,MAAM,EAAE,IAAI,CAACrC,aAAa,CAACuC,aAAa,CAAC,CAAC,CAAC1B,IAAI,CAACvB,GAAG,CAAC,CAAC,CAACkD,KAAK,EAAEN,IAAI,CAAC,KAAKA,IAAI,IAAIM,KAAK,CAAC,EAAE/C,SAAS,CAAC,IAAI,CAACkB,UAAU,CAAC,CAAC,CAACG,SAAS,CAACoB,IAAI,IAAI;MAAA,IAAAO,cAAA;MACnK,IAAIP,IAAI,IAAIA,IAAI,CAACb,EAAE,OAAAoB,cAAA,GAAK,IAAI,CAACC,QAAQ,cAAAD,cAAA,uBAAbA,cAAA,CAAepB,EAAE,GAAE;QACvC,IAAI,CAACpB,UAAU,CAAC0C,YAAY,CAACT,IAAI,CAACb,EAAE,CAAC;MACzC;MACA,IAAI,CAACqB,QAAQ,GAAGR,IAAI;MACpB,IAAI,CAACxB,kBAAkB,GAAG,CAAAwB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,MAAK,GAAG,IAAI,IAAI,CAACzC,WAAW,CAAC0C,YAAY;MAC7E,IAAI,CAAC3C,GAAG,CAACoC,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACAQ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnC,UAAU,CAACoC,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACrC,UAAU,CAACsC,QAAQ,CAAC,CAAC;EAC9B;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAClB,MAAM,EAAE;MACd,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACA,MAAM,CAACmB,IAAI,KAAK,QAAQ;EACxC;EACAC,eAAeA,CAACC,KAAK,EAAE;IAAA,IAAAC,eAAA;IACnB,IAAID,KAAK,EAAE;MACPA,KAAK,CAACE,cAAc,CAAC,CAAC;MACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;IAC3B;IACA,IAAI,CAACjD,aAAa,CAACkD,IAAI,EAAAH,eAAA,GAAC,IAAI,CAACZ,QAAQ,cAAAY,eAAA,uBAAbA,eAAA,CAAeV,IAAI,CAAC;EAChD;EACAc,MAAMA,CAACxB,IAAI,EAAE;IAAA,IAAAyB,mBAAA;IACT,IAAIzB,IAAI,KAAKc,SAAS,EAAE;MACpB,IAAI,CAAC/C,UAAU,CAAC0C,YAAY,CAACT,IAAI,CAACb,EAAE,CAAC;MACrC,IAAI,CAACZ,kBAAkB,CAACmD,QAAQ,CAAC,EAAE,CAAC;MACpC,IAAI,CAAC5D,aAAa,CAAC6D,GAAG,CAAC3B,IAAI,CAACb,EAAE,EAAE,IAAI,CAAC;IACzC;IACA,CAAAsC,mBAAA,OAAI,CAACG,aAAa,cAAAH,mBAAA,eAAlBA,mBAAA,CAAoBI,SAAS,CAAC,CAAC;EACnC;EACAP,eAAeA,CAACH,KAAK,EAAE;IACnBA,KAAK,CAACG,eAAe,CAAC,CAAC;EAC3B;EACAQ,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACjE,aAAa,CAACkB,gBAAgB,CAACgD,GAAG,CAACD,OAAO,EAAE,KAAK,CAAC;IACvD,IAAI,IAAI,CAACjE,aAAa,CAACmE,cAAc,EAAE;MACnC,MAAM;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACpE,aAAa,CAACmE,cAAc,CAACF,OAAO,CAAC;MAC/DG,QAAQ,CAACrC,OAAO,CAAEV,EAAE,IAAK;QACrB,IAAI,CAAC2C,aAAa,CAAC3C,EAAE,CAAC;MAC1B,CAAC,CAAC;IACN;EACJ;EACAY,YAAYA,CAACZ,EAAE,EAAE;IACb,IAAIA,EAAE,EAAE;MACJ,IAAI,CAACrB,aAAa,CAACkB,gBAAgB,CAACgD,GAAG,CAAC7C,EAAE,EAAE,IAAI,CAAC;MACjD,IAAI,IAAI,CAACrB,aAAa,CAACmE,cAAc,EAAE;QACnC,OAAO,IAAI,CAAClC,YAAY,CAAC,IAAI,CAACjC,aAAa,CAACmE,cAAc,CAAC9C,EAAE,CAAC,CAACgD,QAAQ,CAAC;MAC5E;IACJ;EACJ;EACAjD,cAAcA,CAACC,EAAE,EAAE;IACf,MAAMiD,WAAW,GAAG,CAAC,IAAI,CAACtE,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACd,EAAE,CAAC;IAChE,IAAIiD,WAAW,EAAE;MACb,IAAI,CAACtE,aAAa,CAACkB,gBAAgB,CAACgD,GAAG,CAAC7C,EAAE,EAAE,IAAI,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAAC2C,aAAa,CAAC3C,EAAE,CAAC;IAC1B;IACA,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAAC8D,KAAK,GACzC,IAAI,CAACvE,aAAa,CAACoC,eAAe,CAACV,MAAM,CAAC,CAACQ,IAAI,EAAEsC,KAAK,KAAK;MACvD,OAAO,IAAI,CAACxE,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACb,EAAE,CAAC,IAAI,IAAI,CAACrB,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAACG,KAAK;IAC/H,CAAC,CAAC,GACF,IAAI,CAACxE,aAAa,CAACQ,QAAQ,CAACkB,MAAM,CAAC,CAACQ,IAAI,EAAEsC,KAAK,KAAK;MAChD,OAAO,IAAI,CAACxE,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACb,EAAE,CAAC,IAAI,IAAI,CAACrB,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAACG,KAAK;IAC/H,CAAC,CAAC;EACV;EACAC,WAAWA,CAACpB,KAAK,EAAEqB,GAAG,EAAE;IACpB,IAAI,CAAClB,eAAe,CAACH,KAAK,CAAC;IAC3B,IAAIqB,GAAG,CAACC,KAAK,KAAK,CAAC,EAAE;MACjB,IAAI,CAACvD,cAAc,CAACsD,GAAG,CAACrD,EAAE,CAAC;IAC/B;EACJ;EACAuD,QAAQA,CAAA,EAAG;IACP,IAAI,CAACnE,kBAAkB,CAACmD,QAAQ,CAAC,IAAI,CAAC;EAC1C;EACAiB,gBAAgBA,CAACxD,EAAE,EAAE;IACjB,OAAO,CAAC,CAAC,IAAI,CAACrB,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACd,EAAE,CAAC;EACxD;EACAyD,MAAMA,CAAA,EAAG;IAAA,IAAAC,eAAA;IACL,KAAAA,eAAA,GAAI,IAAI,CAACrC,QAAQ,cAAAqC,eAAA,eAAbA,eAAA,CAAe1D,EAAE,EAAE;MACnB,IAAI,CAACY,YAAY,CAAC,IAAI,CAACS,QAAQ,CAACrB,EAAE,CAAC;MACnC,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACR,aAAa,CAACQ,QAAQ,CAACkB,MAAM,CAAC,CAACQ,IAAI,EAAEsC,KAAK,KAAK;QAChE,OAAO,IAAI,CAACxE,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACb,EAAE,CAAC,IAAI,IAAI,CAACrB,aAAa,CAACkB,gBAAgB,CAACiB,GAAG,CAACD,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAACG,KAAK;MAC/H,CAAC,CAAC;MACFnF,KAAK,CAAC,GAAG,CAAC,CACLwB,IAAI,CAACrB,IAAI,CAAC,CAAC,CAAC,CAAC,CACbsB,SAAS,CAAC,MAAM;QAAA,IAAAkE,eAAA;QACjB,KAAAA,eAAA,GAAI,IAAI,CAACtC,QAAQ,cAAAsC,eAAA,eAAbA,eAAA,CAAe3D,EAAE,EAAE;UACnB,MAAM4D,EAAE,GAAGC,QAAQ,CAACC,cAAc,CAAC,IAAI,CAACzC,QAAQ,CAACrB,EAAE,CAAC;UACpD4D,EAAE,aAAFA,EAAE,eAAFA,EAAE,CAAEG,cAAc,CAAC;YAAEC,KAAK,EAAE;UAAS,CAAC,CAAC;QAC3C;MACJ,CAAC,CAAC;IACN;EACJ;AAeJ,CAAC,EAdYvF,sBAAA,CAAKwF,cAAc,GAAG,MAAM,CACjC;EAAEnC,IAAI,EAAExD;AAAmB,CAAC,EAC5B;EAAEwD,IAAI,EAAEvD;AAAiB,CAAC,EAC1B;EAAEuD,IAAI,EAAEvE;AAAkB,CAAC,EAC3B;EAAEuE,IAAI,EAAEzD;AAAiB,CAAC,CAC7B,EACQI,sBAAA,CAAKyF,cAAc,GAAG;EAC3BnF,UAAU,EAAE,CAAC;IAAE+C,IAAI,EAAEpE;EAAM,CAAC,CAAC;EAC7BsB,SAAS,EAAE,CAAC;IAAE8C,IAAI,EAAEpE;EAAM,CAAC,CAAC;EAC5BuB,iBAAiB,EAAE,CAAC;IAAE6C,IAAI,EAAEpE;EAAM,CAAC,CAAC;EACpCwB,aAAa,EAAE,CAAC;IAAE4C,IAAI,EAAEnE;EAAO,CAAC,CAAC;EACjC8E,aAAa,EAAE,CAAC;IAAEX,IAAI,EAAElE,SAAS;IAAEuG,IAAI,EAAE,CAAC,eAAe;EAAG,CAAC,CAAC;EAC9DC,SAAS,EAAE,CAAC;IAAEtC,IAAI,EAAElE,SAAS;IAAEuG,IAAI,EAAE,CAAC,QAAQ;EAAG,CAAC;AACtD,CAAC,EAAA1F,sBAAA,CACJ;AACDD,qBAAqB,GAAGpB,UAAU,CAAC,CAC/BI,SAAS,CAAC;EACN6G,QAAQ,EAAE,wBAAwB;EAClCC,QAAQ,EAAEjH,oBAAoB;EAC9BkH,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAClH,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEkB,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}