{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiDatePickerComponent } from './swui-date-picker.component';\nimport { SwuiDateTimeChooserModule } from '../swui-date-time-chooser/swui-date-time-chooser.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\nexport const DATE_PICKER_MODULES = [SwuiDateTimeChooserModule, ReactiveFormsModule, MatMenuModule, MatInputModule, MatButtonModule, MatRippleModule];\nlet SwuiDatePickerModule = class SwuiDatePickerModule {};\nSwuiDatePickerModule = __decorate([NgModule({\n  declarations: [SwuiDatePickerComponent],\n  imports: [CommonModule, TranslateModule.forChild(), ...DATE_PICKER_MODULES],\n  exports: [SwuiDatePickerComponent]\n})], SwuiDatePickerModule);\nexport { SwuiDatePickerModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "TranslateModule", "ReactiveFormsModule", "SwuiDatePickerComponent", "SwuiDateTimeChooserModule", "MatMenuModule", "MatInputModule", "MatButtonModule", "MatRippleModule", "DATE_PICKER_MODULES", "SwuiDatePickerModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-picker/swui-date-picker.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ReactiveFormsModule } from '@angular/forms';\n\nimport { SwuiDatePickerComponent } from './swui-date-picker.component';\nimport { SwuiDateTimeChooserModule } from '../swui-date-time-chooser/swui-date-time-chooser.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatRippleModule } from '@angular/material/core';\n\n\nexport const DATE_PICKER_MODULES = [\n  SwuiDateTimeChooserModule,\n  ReactiveFormsModule,\n  MatMenuModule,\n  MatInputModule,\n  MatButtonModule,\n  MatRippleModule,\n];\n\n@NgModule({\n  declarations: [SwuiDatePickerComponent],\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    ...DATE_PICKER_MODULES,\n  ],\n  exports: [\n    SwuiDatePickerComponent,\n  ]\n})\n\nexport class SwuiDatePickerModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,yBAAyB,QAAQ,yDAAyD;AACnG,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,wBAAwB;AAGxD,OAAO,MAAMC,mBAAmB,GAAG,CACjCL,yBAAyB,EACzBF,mBAAmB,EACnBG,aAAa,EACbC,cAAc,EACdC,eAAe,EACfC,eAAe,CAChB;AAcM,IAAME,oBAAoB,GAA1B,MAAMA,oBAAoB,GAChC;AADYA,oBAAoB,GAAAC,UAAA,EAZhCZ,QAAQ,CAAC;EACRa,YAAY,EAAE,CAACT,uBAAuB,CAAC;EACvCU,OAAO,EAAE,CACPb,YAAY,EACZC,eAAe,CAACa,QAAQ,EAAE,EAC1B,GAAGL,mBAAmB,CACvB;EACDM,OAAO,EAAE,CACPZ,uBAAuB;CAE1B,CAAC,C,EAEWO,oBAAoB,CAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}