{"ast": null, "code": "var SwuiGridModule_1;\nimport { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiTdWidgetsModule } from './td-widget/td-widgets.module';\nimport { WidgetRegistry } from './registry/registry';\nimport { DefaultWidgetRegistry } from './registry/default-registry';\nimport { SwuiGridWidgetChooserModule } from './widget-chooser/widget-chooser.module';\nimport { SwuiGridRowActionsModule } from './row-actions/row-actions.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridComponent } from './swui-grid.component';\nimport { SwuiGridBulkActionsModule } from './bulk-actions/bulk-actions.module';\nimport { SwuiDefaultWidgetModule } from './default-widget/default-widget.module';\nimport { SwuiColumnsManagementModule } from './columns-management/columns-management.module';\nimport { GRID_CONFIG } from './swui-grid.config';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { SwuiProgressContainerModule } from '../swui-progress-container/swui-progress-container.module';\nimport { SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';\nimport { SwuiFooterWidgetsModule } from './footer-widget/footer-widgets.module';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatButtonModule } from '@angular/material/button';\nlet SwuiGridModule = SwuiGridModule_1 = class SwuiGridModule {\n  static forRoot(config) {\n    return {\n      ngModule: SwuiGridModule_1,\n      providers: [{\n        provide: WidgetRegistry,\n        useClass: DefaultWidgetRegistry\n      }, {\n        provide: GRID_CONFIG,\n        useValue: config\n      }]\n    };\n  }\n};\nSwuiGridModule = SwuiGridModule_1 = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule.forChild(), MatTableModule, MatPaginatorModule, MatSortModule, MatCheckboxModule, MatButtonModule, MatIconModule, MatTooltipModule, SwuiTdWidgetsModule, SwuiFooterWidgetsModule, SwuiGridRowActionsModule, SwuiGridWidgetChooserModule, SwuiDefaultWidgetModule, SwuiGridBulkActionsModule, SwuiColumnsManagementModule, MatProgressSpinnerModule, SwuiProgressContainerModule],\n  declarations: [SwuiGridComponent],\n  exports: [SwuiGridComponent],\n  providers: [SwuiGridUrlHandlerService]\n})], SwuiGridModule);\nexport { SwuiGridModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "SwuiTdWidgetsModule", "WidgetRegistry", "DefaultWidgetRegistry", "SwuiGridWidgetChooserModule", "SwuiGridRowActionsModule", "TranslateModule", "SwuiGridComponent", "SwuiGridBulkActionsModule", "SwuiDefaultWidgetModule", "SwuiColumnsManagementModule", "GRID_CONFIG", "MatProgressSpinnerModule", "SwuiProgressContainerModule", "SwuiGridUrlHandlerService", "SwuiFooterWidgetsModule", "MatSortModule", "MatTableModule", "MatCheckboxModule", "MatPaginatorModule", "MatIconModule", "MatTooltipModule", "MatButtonModule", "SwuiGridModule", "SwuiGridModule_1", "forRoot", "config", "ngModule", "providers", "provide", "useClass", "useValue", "__decorate", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid.module.ts"], "sourcesContent": ["import { ModuleWithProviders, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiTdWidgetsModule } from './td-widget/td-widgets.module';\nimport { WidgetRegistry } from './registry/registry';\nimport { DefaultWidgetRegistry } from './registry/default-registry';\nimport { SwuiGridWidgetChooserModule } from './widget-chooser/widget-chooser.module';\nimport { SwuiGridRowActionsModule } from './row-actions/row-actions.module';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridComponent } from './swui-grid.component';\nimport { SwuiGridBulkActionsModule } from './bulk-actions/bulk-actions.module';\nimport { SwuiDefaultWidgetModule } from './default-widget/default-widget.module';\nimport { SwuiColumnsManagementModule } from './columns-management/columns-management.module';\nimport { GRID_CONFIG, SwuiGridConfig } from './swui-grid.config';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { SwuiProgressContainerModule } from '../swui-progress-container/swui-progress-container.module';\nimport { SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';\nimport { SwuiFooterWidgetsModule } from './footer-widget/footer-widgets.module';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatButtonModule } from '@angular/material/button';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n\n    MatTableModule,\n    MatPaginatorModule,\n    MatSortModule,\n    MatCheckboxModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTooltipModule,\n\n    SwuiTdWidgetsModule,\n    SwuiFooterWidgetsModule,\n    SwuiGridRowActionsModule,\n    SwuiGridWidgetChooserModule,\n    SwuiDefaultWidgetModule,\n    SwuiGridBulkActionsModule,\n    SwuiColumnsManagementModule,\n    MatProgressSpinnerModule,\n    SwuiProgressContainerModule,\n  ],\n  declarations: [\n    SwuiGridComponent,\n  ],\n  exports: [\n    SwuiGridComponent,\n  ],\n  providers: [\n    SwuiGridUrlHandlerService,\n  ]\n})\nexport class SwuiGridModule {\n  static forRoot( config?: SwuiGridConfig ): ModuleWithProviders<SwuiGridModule> {\n    return {\n      ngModule: SwuiGridModule,\n      providers: [\n        {\n          provide: WidgetRegistry,\n          useClass: DefaultWidgetRegistry,\n        },\n        {\n          provide: GRID_CONFIG,\n          useValue: config,\n        }\n      ]\n    };\n  }\n\n}\n"], "mappings": ";;AAAA,SAA8BA,QAAQ,QAAQ,eAAe;AAC7D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,2BAA2B,QAAQ,wCAAwC;AACpF,SAASC,wBAAwB,QAAQ,kCAAkC;AAC3E,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,yBAAyB,QAAQ,oCAAoC;AAC9E,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,2BAA2B,QAAQ,gDAAgD;AAC5F,SAASC,WAAW,QAAwB,oBAAoB;AAChE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,2BAA2B,QAAQ,2DAA2D;AACvG,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAmCnD,IAAMC,cAAc,GAAAC,gBAAA,GAApB,MAAMD,cAAc;EACzB,OAAOE,OAAOA,CAAEC,MAAuB;IACrC,OAAO;MACLC,QAAQ,EAAEH,gBAAc;MACxBI,SAAS,EAAE,CACT;QACEC,OAAO,EAAE3B,cAAc;QACvB4B,QAAQ,EAAE3B;OACX,EACD;QACE0B,OAAO,EAAElB,WAAW;QACpBoB,QAAQ,EAAEL;OACX;KAEJ;EACH;CAED;AAjBYH,cAAc,GAAAC,gBAAA,GAAAQ,UAAA,EAjC1BjC,QAAQ,CAAC;EACRkC,OAAO,EAAE,CACPjC,YAAY,EACZM,eAAe,CAAC4B,QAAQ,EAAE,EAE1BjB,cAAc,EACdE,kBAAkB,EAClBH,aAAa,EACbE,iBAAiB,EACjBI,eAAe,EACfF,aAAa,EACbC,gBAAgB,EAEhBpB,mBAAmB,EACnBc,uBAAuB,EACvBV,wBAAwB,EACxBD,2BAA2B,EAC3BK,uBAAuB,EACvBD,yBAAyB,EACzBE,2BAA2B,EAC3BE,wBAAwB,EACxBC,2BAA2B,CAC5B;EACDsB,YAAY,EAAE,CACZ5B,iBAAiB,CAClB;EACD6B,OAAO,EAAE,CACP7B,iBAAiB,CAClB;EACDqB,SAAS,EAAE,CACTd,yBAAyB;CAE5B,CAAC,C,EACWS,cAAc,CAiB1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}