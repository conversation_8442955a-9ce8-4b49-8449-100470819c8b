{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { SwuiTopFilterDataService } from './top-filter-data.service';\ndescribe('SwuiTopFilterDataService', () => {\n  let service;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      providers: [SwuiTopFilterDataService]\n    });\n    service = TestBed.inject(SwuiTopFilterDataService);\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('updateFilter', () => {\n    it('should not modify filter when no select fields are present', () => {\n      const initialFilter = {\n        field1: 'value1',\n        field2: 'value2'\n      };\n      service.submitFilter(initialFilter);\n      const schema = [{\n        field: 'field1',\n        type: 'string'\n      }, {\n        field: 'field2',\n        type: 'string'\n      }];\n      service.updateFilter(schema);\n      // filter should remain unchanged\n      service.appliedFilter.subscribe(filter => {\n        expect(filter).toEqual(initialFilter);\n      });\n    });\n    it('should remove values for select fields that are not in the schema options', () => {\n      const initialFilter = {\n        field1: 'value1',\n        selectField: 'option2'\n      };\n      service.submitFilter(initialFilter);\n      const schema = [{\n        field: 'field1',\n        type: 'string'\n      }, {\n        field: 'selectField',\n        type: 'select',\n        data: [{\n          id: 'option1',\n          text: 'Option 1'\n        },\n        // option2 is missing from the options\n        {\n          id: 'option3',\n          text: 'Option 3'\n        }]\n      }];\n      service.updateFilter(schema);\n      // selectField should be removed as its value doesn't exist in options\n      service.appliedFilter.subscribe(filter => {\n        expect(filter).toEqual({\n          field1: 'value1'\n        });\n        expect(filter.selectField).toBeUndefined();\n      });\n    });\n    it('should keep values for select fields that exist in the schema options', () => {\n      const initialFilter = {\n        field1: 'value1',\n        selectField: 'option2'\n      };\n      service.submitFilter(initialFilter);\n      const schema = [{\n        field: 'field1',\n        type: 'string'\n      }, {\n        field: 'selectField',\n        type: 'select',\n        data: [{\n          id: 'option1',\n          text: 'Option 1'\n        }, {\n          id: 'option2',\n          text: 'Option 2'\n        },\n        // option2 exists in the options\n        {\n          id: 'option3',\n          text: 'Option 3'\n        }]\n      }];\n      service.updateFilter(schema);\n      // filter should remain unchanged as all values exist in options\n      service.appliedFilter.subscribe(filter => {\n        expect(filter).toEqual(initialFilter);\n      });\n    });\n    it('should handle multiselect fields correctly', () => {\n      const initialFilter = {\n        field1: 'value1',\n        multiSelectField: ['option1', 'option2', 'option4']\n      };\n      service.submitFilter(initialFilter);\n      const schema = [{\n        field: 'field1',\n        type: 'string'\n      }, {\n        field: 'multiSelectField',\n        type: 'multiselect',\n        data: [{\n          id: 'option1',\n          text: 'Option 1'\n        }, {\n          id: 'option2',\n          text: 'Option 2'\n        }, {\n          id: 'option3',\n          text: 'Option 3'\n        }\n        // option4 is missing from the options\n        ]\n      }];\n      service.updateFilter(schema);\n      // multiSelectField should only contain values that exist in options\n      service.appliedFilter.subscribe(filter => {\n        expect(filter.field1).toEqual('value1');\n        expect(filter.multiSelectField).toEqual([]);\n      });\n    });\n    it('should handle select-table fields correctly', () => {\n      const initialFilter = {\n        field1: 'value1',\n        tableField: {\n          id: 'option2'\n        }\n      };\n      service.submitFilter(initialFilter);\n      const schema = [{\n        field: 'field1',\n        type: 'string'\n      }, {\n        field: 'tableField',\n        type: 'select-table',\n        data: [{\n          id: 'option1',\n          text: 'Option 1'\n        },\n        // option2 is missing from the options\n        {\n          id: 'option3',\n          text: 'Option 3'\n        }]\n      }];\n      service.updateFilter(schema);\n      // tableField should be removed as its value doesn't exist in options\n      service.appliedFilter.subscribe(filter => {\n        expect(filter).toEqual({\n          field1: 'value1'\n        });\n        expect(filter.tableField).toBeUndefined();\n      });\n    });\n    it('should not modify filter when schema is empty', () => {\n      const initialFilter = {\n        field1: 'value1',\n        field2: 'value2'\n      };\n      service.submitFilter(initialFilter);\n      service.updateFilter([]);\n      // filter should remain unchanged\n      service.appliedFilter.subscribe(filter => {\n        expect(filter).toEqual(initialFilter);\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["TestBed", "SwuiTopFilterDataService", "describe", "service", "beforeEach", "configureTestingModule", "providers", "inject", "it", "expect", "toBeTruthy", "initialFilter", "field1", "field2", "submitFilter", "schema", "field", "type", "updateFilter", "appliedFilter", "subscribe", "filter", "toEqual", "selectField", "data", "id", "text", "toBeUndefined", "multiSelectField", "tableField"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-top-filter/top-filter-data.service.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\nimport { SwuiTopFilterDataService } from './top-filter-data.service';\nimport type { SchemaTopFilterField } from './swui-schema-top-filter.model';\n\ndescribe('SwuiTopFilterDataService', () => {\n  let service: SwuiTopFilterDataService;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      providers: [SwuiTopFilterDataService]\n    });\n    service = TestBed.inject(SwuiTopFilterDataService);\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('updateFilter', () => {\n    it('should not modify filter when no select fields are present', () => {\n      const initialFilter = { field1: 'value1', field2: 'value2' };\n      service.submitFilter(initialFilter);\n\n      const schema: SchemaTopFilterField[] = [\n        { field: 'field1', type: 'string' },\n        { field: 'field2', type: 'string' }\n      ];\n\n      service.updateFilter(schema);\n\n      // filter should remain unchanged\n      service.appliedFilter.subscribe(filter => {\n        expect(filter).toEqual(initialFilter);\n      });\n    });\n\n    it('should remove values for select fields that are not in the schema options', () => {\n      const initialFilter = {\n        field1: 'value1',\n        selectField: 'option2'\n      };\n      service.submitFilter(initialFilter);\n\n      const schema: SchemaTopFilterField[] = [\n        { field: 'field1', type: 'string' },\n        {\n          field: 'selectField',\n          type: 'select',\n          data: [\n            { id: 'option1', text: 'Option 1' },\n            // option2 is missing from the options\n            { id: 'option3', text: 'Option 3' }\n          ]\n        }\n      ];\n\n      service.updateFilter(schema);\n\n      // selectField should be removed as its value doesn't exist in options\n      service.appliedFilter.subscribe(filter => {\n        expect(filter).toEqual({ field1: 'value1' });\n        expect(filter.selectField).toBeUndefined();\n      });\n    });\n\n    it('should keep values for select fields that exist in the schema options', () => {\n      const initialFilter = {\n        field1: 'value1',\n        selectField: 'option2'\n      };\n      service.submitFilter(initialFilter);\n\n      const schema: SchemaTopFilterField[] = [\n        { field: 'field1', type: 'string' },\n        {\n          field: 'selectField',\n          type: 'select',\n          data: [\n            { id: 'option1', text: 'Option 1' },\n            { id: 'option2', text: 'Option 2' }, // option2 exists in the options\n            { id: 'option3', text: 'Option 3' }\n          ]\n        }\n      ];\n\n      service.updateFilter(schema);\n\n      // filter should remain unchanged as all values exist in options\n      service.appliedFilter.subscribe(filter => {\n        expect(filter).toEqual(initialFilter);\n      });\n    });\n\n    it('should handle multiselect fields correctly', () => {\n      const initialFilter = {\n        field1: 'value1',\n        multiSelectField: ['option1', 'option2', 'option4']\n      };\n      service.submitFilter(initialFilter);\n\n      const schema: SchemaTopFilterField[] = [\n        { field: 'field1', type: 'string' },\n        {\n          field: 'multiSelectField',\n          type: 'multiselect',\n          data: [\n            { id: 'option1', text: 'Option 1' },\n            { id: 'option2', text: 'Option 2' },\n            { id: 'option3', text: 'Option 3' }\n            // option4 is missing from the options\n          ]\n        }\n      ];\n\n      service.updateFilter(schema);\n\n      // multiSelectField should only contain values that exist in options\n      service.appliedFilter.subscribe(filter => {\n        expect(filter.field1).toEqual('value1');\n        expect(filter.multiSelectField).toEqual([]);\n      });\n    });\n\n    it('should handle select-table fields correctly', () => {\n      const initialFilter = {\n        field1: 'value1',\n        tableField: { id: 'option2' }\n      };\n      service.submitFilter(initialFilter);\n\n      const schema: SchemaTopFilterField[] = [\n        { field: 'field1', type: 'string' },\n        {\n          field: 'tableField',\n          type: 'select-table',\n          data: [\n            { id: 'option1', text: 'Option 1' },\n            // option2 is missing from the options\n            { id: 'option3', text: 'Option 3' }\n          ]\n        }\n      ];\n\n      service.updateFilter(schema);\n\n      // tableField should be removed as its value doesn't exist in options\n      service.appliedFilter.subscribe(filter => {\n        expect(filter).toEqual({ field1: 'value1' });\n        expect(filter.tableField).toBeUndefined();\n      });\n    });\n\n    it('should not modify filter when schema is empty', () => {\n      const initialFilter = { field1: 'value1', field2: 'value2' };\n      service.submitFilter(initialFilter);\n\n      service.updateFilter([]);\n\n      // filter should remain unchanged\n      service.appliedFilter.subscribe(filter => {\n        expect(filter).toEqual(initialFilter);\n      });\n    });\n  });\n});\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,wBAAwB,QAAQ,2BAA2B;AAGpEC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;EACxC,IAAIC,OAAiC;EAErCC,UAAU,CAAC,MAAK;IACdJ,OAAO,CAACK,sBAAsB,CAAC;MAC7BC,SAAS,EAAE,CAACL,wBAAwB;KACrC,CAAC;IACFE,OAAO,GAAGH,OAAO,CAACO,MAAM,CAACN,wBAAwB,CAAC;EACpD,CAAC,CAAC;EAEFO,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACN,OAAO,CAAC,CAACO,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFR,QAAQ,CAAC,cAAc,EAAE,MAAK;IAC5BM,EAAE,CAAC,4DAA4D,EAAE,MAAK;MACpE,MAAMG,aAAa,GAAG;QAAEC,MAAM,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAC5DV,OAAO,CAACW,YAAY,CAACH,aAAa,CAAC;MAEnC,MAAMI,MAAM,GAA2B,CACrC;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAE,EACnC;QAAED,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAE,CACpC;MAEDd,OAAO,CAACe,YAAY,CAACH,MAAM,CAAC;MAE5B;MACAZ,OAAO,CAACgB,aAAa,CAACC,SAAS,CAACC,MAAM,IAAG;QACvCZ,MAAM,CAACY,MAAM,CAAC,CAACC,OAAO,CAACX,aAAa,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFH,EAAE,CAAC,2EAA2E,EAAE,MAAK;MACnF,MAAMG,aAAa,GAAG;QACpBC,MAAM,EAAE,QAAQ;QAChBW,WAAW,EAAE;OACd;MACDpB,OAAO,CAACW,YAAY,CAACH,aAAa,CAAC;MAEnC,MAAMI,MAAM,GAA2B,CACrC;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAE,EACnC;QACED,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE,QAAQ;QACdO,IAAI,EAAE,CACJ;UAAEC,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAE;QACnC;QACA;UAAED,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAE;OAEtC,CACF;MAEDvB,OAAO,CAACe,YAAY,CAACH,MAAM,CAAC;MAE5B;MACAZ,OAAO,CAACgB,aAAa,CAACC,SAAS,CAACC,MAAM,IAAG;QACvCZ,MAAM,CAACY,MAAM,CAAC,CAACC,OAAO,CAAC;UAAEV,MAAM,EAAE;QAAQ,CAAE,CAAC;QAC5CH,MAAM,CAACY,MAAM,CAACE,WAAW,CAAC,CAACI,aAAa,EAAE;MAC5C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFnB,EAAE,CAAC,uEAAuE,EAAE,MAAK;MAC/E,MAAMG,aAAa,GAAG;QACpBC,MAAM,EAAE,QAAQ;QAChBW,WAAW,EAAE;OACd;MACDpB,OAAO,CAACW,YAAY,CAACH,aAAa,CAAC;MAEnC,MAAMI,MAAM,GAA2B,CACrC;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAE,EACnC;QACED,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE,QAAQ;QACdO,IAAI,EAAE,CACJ;UAAEC,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAE,EACnC;UAAED,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAE;QAAE;QACrC;UAAED,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAE;OAEtC,CACF;MAEDvB,OAAO,CAACe,YAAY,CAACH,MAAM,CAAC;MAE5B;MACAZ,OAAO,CAACgB,aAAa,CAACC,SAAS,CAACC,MAAM,IAAG;QACvCZ,MAAM,CAACY,MAAM,CAAC,CAACC,OAAO,CAACX,aAAa,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFH,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpD,MAAMG,aAAa,GAAG;QACpBC,MAAM,EAAE,QAAQ;QAChBgB,gBAAgB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;OACnD;MACDzB,OAAO,CAACW,YAAY,CAACH,aAAa,CAAC;MAEnC,MAAMI,MAAM,GAA2B,CACrC;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAE,EACnC;QACED,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAE,aAAa;QACnBO,IAAI,EAAE,CACJ;UAAEC,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAE,EACnC;UAAED,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAE,EACnC;UAAED,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU;QACjC;QAAA;OAEH,CACF;MAEDvB,OAAO,CAACe,YAAY,CAACH,MAAM,CAAC;MAE5B;MACAZ,OAAO,CAACgB,aAAa,CAACC,SAAS,CAACC,MAAM,IAAG;QACvCZ,MAAM,CAACY,MAAM,CAACT,MAAM,CAAC,CAACU,OAAO,CAAC,QAAQ,CAAC;QACvCb,MAAM,CAACY,MAAM,CAACO,gBAAgB,CAAC,CAACN,OAAO,CAAC,EAAE,CAAC;MAC7C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFd,EAAE,CAAC,6CAA6C,EAAE,MAAK;MACrD,MAAMG,aAAa,GAAG;QACpBC,MAAM,EAAE,QAAQ;QAChBiB,UAAU,EAAE;UAAEJ,EAAE,EAAE;QAAS;OAC5B;MACDtB,OAAO,CAACW,YAAY,CAACH,aAAa,CAAC;MAEnC,MAAMI,MAAM,GAA2B,CACrC;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAE,EACnC;QACED,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,cAAc;QACpBO,IAAI,EAAE,CACJ;UAAEC,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAE;QACnC;QACA;UAAED,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAE;OAEtC,CACF;MAEDvB,OAAO,CAACe,YAAY,CAACH,MAAM,CAAC;MAE5B;MACAZ,OAAO,CAACgB,aAAa,CAACC,SAAS,CAACC,MAAM,IAAG;QACvCZ,MAAM,CAACY,MAAM,CAAC,CAACC,OAAO,CAAC;UAAEV,MAAM,EAAE;QAAQ,CAAE,CAAC;QAC5CH,MAAM,CAACY,MAAM,CAACQ,UAAU,CAAC,CAACF,aAAa,EAAE;MAC3C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFnB,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvD,MAAMG,aAAa,GAAG;QAAEC,MAAM,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAC5DV,OAAO,CAACW,YAAY,CAACH,aAAa,CAAC;MAEnCR,OAAO,CAACe,YAAY,CAAC,EAAE,CAAC;MAExB;MACAf,OAAO,CAACgB,aAAa,CAACC,SAAS,CAACC,MAAM,IAAG;QACvCZ,MAAM,CAACY,MAAM,CAAC,CAACC,OAAO,CAACX,aAAa,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}