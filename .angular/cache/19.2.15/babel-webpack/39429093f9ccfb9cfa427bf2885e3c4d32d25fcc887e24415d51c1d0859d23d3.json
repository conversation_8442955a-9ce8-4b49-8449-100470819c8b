{"ast": null, "code": "var _SwuiTdCalcAsyncWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./calc-async.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdCalcAsyncWidgetComponent = (_SwuiTdCalcAsyncWidgetComponent = class SwuiTdCalcAsyncWidgetComponent {\n  constructor({\n    schema,\n    row\n  }) {\n    var _schema$td, _schema$td2, _schema$td3, _schema$td4;\n    this.title = '';\n    this.class = '';\n    this.loading = true;\n    const source$ = (_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.source;\n    const titleFn = (_schema$td2 = schema.td) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.titleFn;\n    const classFn = (_schema$td3 = schema.td) === null || _schema$td3 === void 0 ? void 0 : _schema$td3.classFn;\n    if (titleFn && classFn) {\n      source$.subscribe(data => {\n        this.title = titleFn(data, row, schema);\n        this.class = classFn(data, row, schema);\n        this.loading = false;\n      });\n    }\n    this.useTranslate = schema.td && 'useTranslate' in schema.td ? ((_schema$td4 = schema.td) === null || _schema$td4 === void 0 ? void 0 : _schema$td4.useTranslate) || false : true;\n  }\n}, _SwuiTdCalcAsyncWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdCalcAsyncWidgetComponent);\nSwuiTdCalcAsyncWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-calc-async-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdCalcAsyncWidgetComponent);\nexport { SwuiTdCalcAsyncWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdCalcAsyncWidgetComponent", "_SwuiTdCalcAsyncWidgetComponent", "constructor", "schema", "row", "_schema$td", "_schema$td2", "_schema$td3", "_schema$td4", "title", "class", "loading", "source$", "td", "source", "titleFn", "classFn", "subscribe", "data", "useTranslate", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/calc-async/calc-async.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./calc-async.widget.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdCalcAsyncWidgetComponent = class SwuiTdCalcAsyncWidgetComponent {\n    constructor({ schema, row }) {\n        this.title = '';\n        this.class = '';\n        this.loading = true;\n        const source$ = schema.td?.source;\n        const titleFn = schema.td?.titleFn;\n        const classFn = schema.td?.classFn;\n        if (titleFn && classFn) {\n            source$.subscribe(data => {\n                this.title = titleFn(data, row, schema);\n                this.class = classFn(data, row, schema);\n                this.loading = false;\n            });\n        }\n        this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdCalcAsyncWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-calc-async-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdCalcAsyncWidgetComponent);\nexport { SwuiTdCalcAsyncWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,qCAAqC;AACtE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,8BAA8B,IAAAC,+BAAA,GAAG,MAAMD,8BAA8B,CAAC;EACtEE,WAAWA,CAAC;IAAEC,MAAM;IAAEC;EAAI,CAAC,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;IACzB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,MAAMC,OAAO,IAAAP,UAAA,GAAGF,MAAM,CAACU,EAAE,cAAAR,UAAA,uBAATA,UAAA,CAAWS,MAAM;IACjC,MAAMC,OAAO,IAAAT,WAAA,GAAGH,MAAM,CAACU,EAAE,cAAAP,WAAA,uBAATA,WAAA,CAAWS,OAAO;IAClC,MAAMC,OAAO,IAAAT,WAAA,GAAGJ,MAAM,CAACU,EAAE,cAAAN,WAAA,uBAATA,WAAA,CAAWS,OAAO;IAClC,IAAID,OAAO,IAAIC,OAAO,EAAE;MACpBJ,OAAO,CAACK,SAAS,CAACC,IAAI,IAAI;QACtB,IAAI,CAACT,KAAK,GAAGM,OAAO,CAACG,IAAI,EAAEd,GAAG,EAAED,MAAM,CAAC;QACvC,IAAI,CAACO,KAAK,GAAGM,OAAO,CAACE,IAAI,EAAEd,GAAG,EAAED,MAAM,CAAC;QACvC,IAAI,CAACQ,OAAO,GAAG,KAAK;MACxB,CAAC,CAAC;IACN;IACA,IAAI,CAACQ,YAAY,GAAGhB,MAAM,CAACU,EAAE,IAAI,cAAc,IAAIV,MAAM,CAACU,EAAE,GAAG,EAAAL,WAAA,GAAAL,MAAM,CAACU,EAAE,cAAAL,WAAA,uBAATA,WAAA,CAAWW,YAAY,KAAI,KAAK,GAAG,IAAI;EAC1G;AAIJ,CAAC,EAHYlB,+BAAA,CAAKmB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEvB,MAAM;IAAE0B,IAAI,EAAE,CAACzB,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,+BAAA,CACJ;AACDD,8BAA8B,GAAGL,UAAU,CAAC,CACxCE,SAAS,CAAC;EACN4B,QAAQ,EAAE,+BAA+B;EACzCC,QAAQ,EAAE9B,oBAAoB;EAC9B+B,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAE3B,8BAA8B,CAAC;AAClC,SAASA,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}