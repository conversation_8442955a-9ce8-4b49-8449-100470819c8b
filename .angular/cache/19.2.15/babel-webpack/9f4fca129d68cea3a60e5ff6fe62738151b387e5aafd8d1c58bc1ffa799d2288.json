{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiChipsAutocompleteComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-chips-autocomplete.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-chips-autocomplete.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { COMMA, ENTER } from '@angular/cdk/keycodes';\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatAutocompleteTrigger } from '@angular/material/autocomplete';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { startWith, switchMap, take, tap } from 'rxjs/operators';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nconst CONTROL_NAME = 'lib-swui-chips-autocomplete';\nlet nextUniqueId = 0;\nlet SwuiChipsAutocompleteComponent = (_SwuiChipsAutocompleteComponent = class SwuiChipsAutocompleteComponent extends SwuiMatFormFieldControl {\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    this._value = value;\n    this.setSelectedItems(value);\n    this.stateChanges.next(undefined);\n  }\n  set items(value) {\n    if (!!value) {\n      this.itemsMap.clear();\n      value.forEach(item => {\n        this.itemsMap.set(item.id, _objectSpread({}, item));\n      });\n      this._items = value;\n      this.inputFormControl.setValue(this.inputFormControl.value);\n      this.setSelectedItems(this.selectedItems$.value);\n    }\n  }\n  get items() {\n    return this._items;\n  }\n  get empty() {\n    return this.isEmpty;\n  }\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.filteredItems = new Observable();\n    this.inputFormControl = new UntypedFormControl();\n    this.itemsMap = new Map();\n    this.selectedItems = [];\n    this.separatorKeysCodes = [ENTER, COMMA];\n    this.isEmpty = true;\n    this.controlType = CONTROL_NAME;\n    this.hasFounded = true;\n    this.selectedItems$ = new BehaviorSubject([]);\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._value = [];\n    this._items = [];\n    this.blockBlurEvent = false;\n  }\n  ngOnInit() {\n    this.selectedItems$.subscribe(val => {\n      this.selectedItems = val;\n      this._value = this.selectedItems;\n      this.isEmpty = this.selectedItems.length === 0;\n      this.onChange(this._value.map(item => {\n        return this.mapFn ? this.mapFn(item) : item;\n      }));\n    });\n    this.initFilteredItems();\n  }\n  closePanel() {\n    if (!this.blockBlurEvent) {\n      var _this$autoCompleteTri;\n      (_this$autoCompleteTri = this.autoCompleteTrigger) === null || _this$autoCompleteTri === void 0 || _this$autoCompleteTri.closePanel();\n    }\n    this.blockBlurEvent = false;\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.input && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.input.focus();\n    }\n  }\n  writeValue(value) {\n    this.setSelectedItems(value);\n  }\n  initFilteredItems() {\n    const filter = text => {\n      return of(text ? this.filter(text) : this.items.slice());\n    };\n    const search = this.searchFn || filter;\n    let value = '';\n    this.filteredItems = this.inputFormControl.valueChanges.pipe(startWith(this.inputFormControl.value), tap(text => {\n      value = text && text.toString() || '';\n    }), switchMap(() => search(value)), tap(items => this.hasFounded = items.some(item => item.text.toLowerCase() === value.toLowerCase())));\n  }\n  getItemText(id) {\n    const item = this.itemsMap.get(id);\n    return item ? item.text : '';\n  }\n  add(option) {\n    const value = option.source.value;\n    if ((value || '').trim() && this.addFn) {\n      this.addFn(value).pipe(take(1)).subscribe(tag => {\n        this.itemsMap.set(tag.id, _objectSpread({}, tag));\n        this.selectItem(tag.id);\n        this.inputFormControl.setValue('');\n      });\n    }\n  }\n  remove(id) {\n    const index = this.selectedItems.indexOf(id);\n    if (index > -1) {\n      this.selectedItems.splice(index, 1);\n      setTimeout(() => {\n        this.selectedItems$.next(this.selectedItems);\n      }, 0);\n      this.enableOptionById(id);\n    }\n  }\n  selected(event) {\n    if (event.isUserInput) {\n      const item = event.source.value;\n      if (this.itemsMap.has(item.id)) {\n        if (this.itemsMap.get(item.id).disabled || this.itemsMap.get(item.id).selected) {\n          return;\n        }\n      } else {\n        this.itemsMap.set(item.id, _objectSpread({}, item));\n      }\n      this.selectItem(item.id);\n    }\n  }\n  clear() {\n    if (this.input) {\n      this.input.value = '';\n    }\n    this.inputFormControl.setValue(null);\n  }\n  onMenuItemMousedown() {\n    this.blockBlurEvent = true;\n  }\n  selectItem(id) {\n    if (!id) {\n      return;\n    }\n    this.disableOptionById(id);\n    this.selectedItems.push(id);\n    this.selectedItems$.next(this.selectedItems);\n  }\n  onInputClick() {\n    var _this$autoCompleteTri2;\n    (_this$autoCompleteTri2 = this.autoCompleteTrigger) === null || _this$autoCompleteTri2 === void 0 || _this$autoCompleteTri2.openPanel();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n  filter(value) {\n    return this.items.filter(item => item.text.toLowerCase().indexOf(value.toLowerCase()) > -1);\n  }\n  disableOptionById(id) {\n    if (this.itemsMap.has(id)) {\n      this.itemsMap.get(id).selected = true;\n    }\n  }\n  enableOptionById(id) {\n    if (this.itemsMap.has(id)) {\n      this.itemsMap.get(id).selected = false;\n    }\n  }\n  setSelectedItems(value) {\n    if (Array.isArray(value)) {\n      if (!value.length) {\n        var _this$selectedItems;\n        (_this$selectedItems = this.selectedItems) === null || _this$selectedItems === void 0 || _this$selectedItems.forEach(id => this.enableOptionById(id));\n      }\n      this.selectedItems = value.map(item => {\n        if (typeof item === 'string') {\n          return item;\n        }\n        if (!this.itemsMap.has(item.id)) {\n          this.itemsMap.set(item.id, _objectSpread({}, item));\n        }\n        return item.id;\n      });\n      this.selectedItems.forEach(id => this.disableOptionById(id));\n      this.selectedItems$.next(this.selectedItems);\n    }\n  }\n}, _SwuiChipsAutocompleteComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}], _SwuiChipsAutocompleteComponent.propDecorators = {\n  value: [{\n    type: Input\n  }],\n  searchFn: [{\n    type: Input\n  }],\n  addFn: [{\n    type: Input\n  }],\n  mapFn: [{\n    type: Input\n  }],\n  items: [{\n    type: Input\n  }],\n  input: [{\n    type: ViewChild,\n    args: [MatInput]\n  }],\n  autoCompleteTrigger: [{\n    type: ViewChild,\n    args: ['input', {\n      read: MatAutocompleteTrigger\n    }]\n  }],\n  matAutocomplete: [{\n    type: ViewChild,\n    args: ['auto']\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiChipsAutocompleteComponent);\nSwuiChipsAutocompleteComponent = __decorate([Component({\n  selector: 'lib-swui-chips-autocomplete',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiChipsAutocompleteComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiChipsAutocompleteComponent);\nexport { SwuiChipsAutocompleteComponent };", "map": {"version": 3, "names": ["FocusMonitor", "COMMA", "ENTER", "Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormControl", "FormGroupDirective", "NgControl", "MatAutocompleteTrigger", "ErrorStateMatcher", "MatFormFieldControl", "BehaviorSubject", "Observable", "of", "startWith", "switchMap", "take", "tap", "MatInput", "SwuiMatFormFieldControl", "CONTROL_NAME", "nextUniqueId", "SwuiChipsAutocompleteComponent", "_SwuiChipsAutocompleteComponent", "value", "_value", "setSelectedItems", "stateChanges", "next", "undefined", "items", "itemsMap", "clear", "for<PERSON>ach", "item", "set", "id", "_objectSpread", "_items", "inputFormControl", "setValue", "selectedItems$", "empty", "isEmpty", "shouldLabelFloat", "focused", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "filteredItems", "Map", "selectedItems", "separatorKeysCodes", "controlType", "hasFounded", "blockBlurEvent", "ngOnInit", "subscribe", "val", "length", "onChange", "map", "mapFn", "initFilteredItems", "closePanel", "_this$autoCompleteTri", "autoCompleteTrigger", "onContainerClick", "event", "stopPropagation", "input", "target", "tagName", "toLowerCase", "disabled", "focus", "writeValue", "filter", "text", "slice", "search", "searchFn", "valueChanges", "pipe", "toString", "some", "getItemText", "get", "add", "option", "source", "trim", "addFn", "tag", "selectItem", "remove", "index", "indexOf", "splice", "setTimeout", "enableOptionById", "selected", "isUserInput", "has", "onMenuItemMousedown", "disableOptionById", "push", "onInputClick", "_this$autoCompleteTri2", "openPanel", "isErrorState", "errorState", "Array", "isArray", "_this$selectedItems", "type", "args", "read", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "provide", "useExisting", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-chips-autocomplete/swui-chips-autocomplete.component.ts"], "sourcesContent": ["import { FocusMonitor } from '@angular/cdk/a11y';\nimport { COMMA, ENTER } from '@angular/cdk/keycodes';\nimport { Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatAutocomplete, MatAutocompleteTrigger } from '@angular/material/autocomplete';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { MatOptionSelectionChange } from '@angular/material/core';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { startWith, switchMap, take, tap } from 'rxjs/operators';\n\nimport { OptionModel } from '../swui-autoselect/option.model';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\n\nconst CONTROL_NAME = 'lib-swui-chips-autocomplete';\nlet nextUniqueId = 0;\n\n@Component({\n    selector: 'lib-swui-chips-autocomplete',\n    templateUrl: './swui-chips-autocomplete.component.html',\n    styleUrls: [\n        './swui-chips-autocomplete.component.scss',\n    ],\n    providers: [{ provide: MatFormFieldControl, useExisting: SwuiChipsAutocompleteComponent }],\n    standalone: false\n})\nexport class SwuiChipsAutocompleteComponent extends SwuiMatFormFieldControl<string[]> implements OnInit {\n  @Input()\n  get value(): string[] {\n    return this._value;\n  }\n\n  set value( value: string[] ) {\n    this._value = value;\n    this.setSelectedItems(value);\n    this.stateChanges.next(undefined);\n  }\n\n  @Input() searchFn?: ( text: string ) => Observable<any>;\n  @Input() addFn?: ( text: string ) => Observable<any>;\n  @Input() mapFn?: ( text: string ) => any;\n\n  @Input()\n  set items( value: OptionModel[] ) {\n    if (!!value) {\n      this.itemsMap.clear();\n      value.forEach(item => {\n        this.itemsMap.set(item.id, { ...item });\n      });\n      this._items = value;\n      this.inputFormControl.setValue(this.inputFormControl.value);\n      this.setSelectedItems(this.selectedItems$.value);\n    }\n  }\n\n  get items(): OptionModel[] {\n    return this._items;\n  }\n\n  get empty() {\n    return this.isEmpty;\n  }\n\n  filteredItems: Observable<OptionModel[]> = new Observable<OptionModel[]>();\n  readonly inputFormControl = new UntypedFormControl();\n  readonly itemsMap = new Map();\n  selectedItems: string[] = [];\n  readonly separatorKeysCodes: number[] = [ENTER, COMMA];\n  isEmpty = true;\n  readonly controlType = CONTROL_NAME;\n  hasFounded = true;\n\n  readonly selectedItems$ = new BehaviorSubject<string[]>([]);\n\n  @ViewChild(MatInput) input?: MatInput;\n  @ViewChild('input', { read: MatAutocompleteTrigger }) autoCompleteTrigger: MatAutocompleteTrigger | undefined;\n  @ViewChild('auto') matAutocomplete: MatAutocomplete | undefined;\n\n  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;\n\n  @HostBinding('class.floating')\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n\n  private _value: string[] = [];\n  private _items: OptionModel[] = [];\n  private blockBlurEvent = false;\n\n  constructor( fm: FocusMonitor,\n               elRef: ElementRef<HTMLElement>,\n               @Optional() @Self() ngControl: NgControl,\n               @Optional() parentFormGroup: FormGroupDirective,\n               errorStateMatcher: ErrorStateMatcher\n  ) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n  }\n\n  ngOnInit() {\n    this.selectedItems$\n      .subscribe(( val: string[] ) => {\n        this.selectedItems = val;\n        this._value = this.selectedItems;\n        this.isEmpty = this.selectedItems.length === 0;\n        this.onChange(this._value.map(item => {\n          return this.mapFn ? this.mapFn(item) : item;\n        }));\n      });\n    this.initFilteredItems();\n  }\n\n  closePanel() {\n    if (!this.blockBlurEvent) {\n      this.autoCompleteTrigger?.closePanel();\n    }\n    this.blockBlurEvent = false;\n  }\n\n  onContainerClick( event: Event ) {\n    event.stopPropagation();\n    if (this.input && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.input.focus();\n    }\n  }\n\n  writeValue( value: string[] ): void {\n    this.setSelectedItems(value);\n  }\n\n  initFilteredItems() {\n    const filter = ( text: string ) => {\n      return of(text ? this.filter(text) : this.items.slice());\n    };\n    const search = this.searchFn || filter;\n\n    let value = '';\n\n    this.filteredItems = this.inputFormControl.valueChanges.pipe(\n      startWith((this.inputFormControl.value) as string),\n      tap(text => {\n        value = text && text.toString() || '';\n      }),\n      switchMap(() => search(value)),\n      tap(items => this.hasFounded = items.some(item => item.text.toLowerCase() === value.toLowerCase()))\n    );\n  }\n\n  getItemText( id: string ) {\n    const item = this.itemsMap.get(id);\n    return item ? item.text : '';\n  }\n\n  add( option: MatOptionSelectionChange ) {\n    const value = option.source.value;\n\n    if ((value || '').trim() && this.addFn) {\n      this.addFn(value)\n        .pipe(take(1))\n        .subscribe(tag => {\n          this.itemsMap.set(tag.id, { ...tag });\n          this.selectItem(tag.id);\n          this.inputFormControl.setValue('');\n        });\n    }\n  }\n\n  remove( id: string ): void {\n    const index = this.selectedItems.indexOf(id);\n    if (index > -1) {\n      this.selectedItems.splice(index, 1);\n      setTimeout(() => {\n        this.selectedItems$.next(this.selectedItems);\n      }, 0);\n      this.enableOptionById(id);\n    }\n  }\n\n  selected( event: MatOptionSelectionChange ): void {\n    if (event.isUserInput) {\n      const item = event.source.value;\n\n      if (this.itemsMap.has(item.id)) {\n        if (this.itemsMap.get(item.id).disabled || this.itemsMap.get(item.id).selected) {\n          return;\n        }\n      } else {\n        this.itemsMap.set(item.id, { ...item });\n      }\n\n      this.selectItem(item.id);\n    }\n  }\n\n  clear(): void {\n    if (this.input) {\n      this.input.value = '';\n    }\n    this.inputFormControl.setValue(null);\n  }\n\n  onMenuItemMousedown() {\n    this.blockBlurEvent = true;\n  }\n\n  selectItem( id?: string ) {\n    if (!id) {\n      return;\n    }\n\n    this.disableOptionById(id);\n    this.selectedItems.push(id);\n    this.selectedItems$.next(this.selectedItems);\n  }\n\n  onInputClick() {\n    this.autoCompleteTrigger?.openPanel();\n  }\n\n  protected isErrorState(): boolean {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n\n  private filter( value: string ): OptionModel[] {\n    return this.items\n      .filter(item => item.text.toLowerCase().indexOf(value.toLowerCase()) > -1);\n  }\n\n  private disableOptionById( id: string ) {\n    if (this.itemsMap.has(id)) {\n      this.itemsMap.get(id).selected = true;\n    }\n  }\n\n  private enableOptionById( id: string ) {\n    if (this.itemsMap.has(id)) {\n      this.itemsMap.get(id).selected = false;\n    }\n  }\n\n  private setSelectedItems( value: any[] ) {\n    if (Array.isArray(value)) {\n      if (!value.length) {\n        this.selectedItems?.forEach(id => this.enableOptionById(id));\n      }\n\n      this.selectedItems = value.map(item => {\n        if (typeof item === 'string') {\n          return item;\n        }\n\n        if (!this.itemsMap.has(item.id)) {\n          this.itemsMap.set(item.id, { ...item });\n        }\n\n        return item.id;\n      });\n      this.selectedItems.forEach(id => this.disableOptionById(id));\n      this.selectedItems$.next(this.selectedItems);\n    }\n  }\n}\n"], "mappings": ";;;;;AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpD,SAASC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAUC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AAC5G,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAClF,SAA0BC,sBAAsB,QAAQ,gCAAgC;AACxF,SAASC,iBAAiB,QAAQ,wBAAwB;AAE1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,eAAe,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AACtD,SAASC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,QAAQ,gBAAgB;AAGhE,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,uBAAuB,QAAQ,uCAAuC;AAE/E,MAAMC,YAAY,GAAG,6BAA6B;AAClD,IAAIC,YAAY,GAAG,CAAC;AAWb,IAAMC,8BAA8B,IAAAC,+BAAA,GAApC,MAAMD,8BAA+B,SAAQH,uBAAiC;MAE/EK,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,MAAM;EACpB;EAEA,IAAID,KAAKA,CAAEA,KAAe;IACxB,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,gBAAgB,CAACF,KAAK,CAAC;IAC5B,IAAI,CAACG,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;EACnC;MAOIC,KAAKA,CAAEN,KAAoB;IAC7B,IAAI,CAAC,CAACA,KAAK,EAAE;MACX,IAAI,CAACO,QAAQ,CAACC,KAAK,EAAE;MACrBR,KAAK,CAACS,OAAO,CAACC,IAAI,IAAG;QACnB,IAAI,CAACH,QAAQ,CAACI,GAAG,CAACD,IAAI,CAACE,EAAE,EAAAC,aAAA,KAAOH,IAAI,CAAE,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACI,MAAM,GAAGd,KAAK;MACnB,IAAI,CAACe,gBAAgB,CAACC,QAAQ,CAAC,IAAI,CAACD,gBAAgB,CAACf,KAAK,CAAC;MAC3D,IAAI,CAACE,gBAAgB,CAAC,IAAI,CAACe,cAAc,CAACjB,KAAK,CAAC;IAClD;EACF;EAEA,IAAIM,KAAKA,CAAA;IACP,OAAO,IAAI,CAACQ,MAAM;EACpB;EAEA,IAAII,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,OAAO;EACrB;MAoBIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACH,KAAK;EACpC;EAMAI,YAAaC,EAAgB,EAChBC,KAA8B,EACVC,SAAoB,EAC5BC,eAAmC,EAC/CC,iBAAoC;IAE/C,KAAK,CAACJ,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAhCjE,KAAAC,aAAa,GAA8B,IAAIxC,UAAU,EAAiB;IACjE,KAAA2B,gBAAgB,GAAG,IAAIlC,kBAAkB,EAAE;IAC3C,KAAA0B,QAAQ,GAAG,IAAIsB,GAAG,EAAE;IAC7B,KAAAC,aAAa,GAAa,EAAE;IACnB,KAAAC,kBAAkB,GAAa,CAAC1D,KAAK,EAAED,KAAK,CAAC;IACtD,KAAA+C,OAAO,GAAG,IAAI;IACL,KAAAa,WAAW,GAAGpC,YAAY;IACnC,KAAAqC,UAAU,GAAG,IAAI;IAER,KAAAhB,cAAc,GAAG,IAAI9B,eAAe,CAAW,EAAE,CAAC;IAMnC,KAAAyB,EAAE,GAAG,GAAGhB,YAAY,IAAIC,YAAY,EAAE,EAAE;IAOxD,KAAAI,MAAM,GAAa,EAAE;IACrB,KAAAa,MAAM,GAAkB,EAAE;IAC1B,KAAAoB,cAAc,GAAG,KAAK;EAS9B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAClB,cAAc,CAChBmB,SAAS,CAAGC,GAAa,IAAK;MAC7B,IAAI,CAACP,aAAa,GAAGO,GAAG;MACxB,IAAI,CAACpC,MAAM,GAAG,IAAI,CAAC6B,aAAa;MAChC,IAAI,CAACX,OAAO,GAAG,IAAI,CAACW,aAAa,CAACQ,MAAM,KAAK,CAAC;MAC9C,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACtC,MAAM,CAACuC,GAAG,CAAC9B,IAAI,IAAG;QACnC,OAAO,IAAI,CAAC+B,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC/B,IAAI,CAAC,GAAGA,IAAI;MAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACJ,IAAI,CAACgC,iBAAiB,EAAE;EAC1B;EAEAC,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACT,cAAc,EAAE;MAAA,IAAAU,qBAAA;MACxB,CAAAA,qBAAA,OAAI,CAACC,mBAAmB,cAAAD,qBAAA,eAAxBA,qBAAA,CAA0BD,UAAU,EAAE;IACxC;IACA,IAAI,CAACT,cAAc,GAAG,KAAK;EAC7B;EAEAY,gBAAgBA,CAAEC,KAAY;IAC5BA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,IAAI,CAACC,KAAK,IAAKF,KAAK,CAACG,MAAkB,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAC/F,IAAI,CAACJ,KAAK,CAACK,KAAK,EAAE;IACpB;EACF;EAEAC,UAAUA,CAAEvD,KAAe;IACzB,IAAI,CAACE,gBAAgB,CAACF,KAAK,CAAC;EAC9B;EAEA0C,iBAAiBA,CAAA;IACf,MAAMc,MAAM,GAAKC,IAAY,IAAK;MAChC,OAAOpE,EAAE,CAACoE,IAAI,GAAG,IAAI,CAACD,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACnD,KAAK,CAACoD,KAAK,EAAE,CAAC;IAC1D,CAAC;IACD,MAAMC,MAAM,GAAG,IAAI,CAACC,QAAQ,IAAIJ,MAAM;IAEtC,IAAIxD,KAAK,GAAG,EAAE;IAEd,IAAI,CAAC4B,aAAa,GAAG,IAAI,CAACb,gBAAgB,CAAC8C,YAAY,CAACC,IAAI,CAC1DxE,SAAS,CAAE,IAAI,CAACyB,gBAAgB,CAACf,KAAgB,CAAC,EAClDP,GAAG,CAACgE,IAAI,IAAG;MACTzD,KAAK,GAAGyD,IAAI,IAAIA,IAAI,CAACM,QAAQ,EAAE,IAAI,EAAE;IACvC,CAAC,CAAC,EACFxE,SAAS,CAAC,MAAMoE,MAAM,CAAC3D,KAAK,CAAC,CAAC,EAC9BP,GAAG,CAACa,KAAK,IAAI,IAAI,CAAC2B,UAAU,GAAG3B,KAAK,CAAC0D,IAAI,CAACtD,IAAI,IAAIA,IAAI,CAAC+C,IAAI,CAACL,WAAW,EAAE,KAAKpD,KAAK,CAACoD,WAAW,EAAE,CAAC,CAAC,CACpG;EACH;EAEAa,WAAWA,CAAErD,EAAU;IACrB,MAAMF,IAAI,GAAG,IAAI,CAACH,QAAQ,CAAC2D,GAAG,CAACtD,EAAE,CAAC;IAClC,OAAOF,IAAI,GAAGA,IAAI,CAAC+C,IAAI,GAAG,EAAE;EAC9B;EAEAU,GAAGA,CAAEC,MAAgC;IACnC,MAAMpE,KAAK,GAAGoE,MAAM,CAACC,MAAM,CAACrE,KAAK;IAEjC,IAAI,CAACA,KAAK,IAAI,EAAE,EAAEsE,IAAI,EAAE,IAAI,IAAI,CAACC,KAAK,EAAE;MACtC,IAAI,CAACA,KAAK,CAACvE,KAAK,CAAC,CACd8D,IAAI,CAACtE,IAAI,CAAC,CAAC,CAAC,CAAC,CACb4C,SAAS,CAACoC,GAAG,IAAG;QACf,IAAI,CAACjE,QAAQ,CAACI,GAAG,CAAC6D,GAAG,CAAC5D,EAAE,EAAAC,aAAA,KAAO2D,GAAG,CAAE,CAAC;QACrC,IAAI,CAACC,UAAU,CAACD,GAAG,CAAC5D,EAAE,CAAC;QACvB,IAAI,CAACG,gBAAgB,CAACC,QAAQ,CAAC,EAAE,CAAC;MACpC,CAAC,CAAC;IACN;EACF;EAEA0D,MAAMA,CAAE9D,EAAU;IAChB,MAAM+D,KAAK,GAAG,IAAI,CAAC7C,aAAa,CAAC8C,OAAO,CAAChE,EAAE,CAAC;IAC5C,IAAI+D,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC7C,aAAa,CAAC+C,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACnCG,UAAU,CAAC,MAAK;QACd,IAAI,CAAC7D,cAAc,CAACb,IAAI,CAAC,IAAI,CAAC0B,aAAa,CAAC;MAC9C,CAAC,EAAE,CAAC,CAAC;MACL,IAAI,CAACiD,gBAAgB,CAACnE,EAAE,CAAC;IAC3B;EACF;EAEAoE,QAAQA,CAAEjC,KAA+B;IACvC,IAAIA,KAAK,CAACkC,WAAW,EAAE;MACrB,MAAMvE,IAAI,GAAGqC,KAAK,CAACsB,MAAM,CAACrE,KAAK;MAE/B,IAAI,IAAI,CAACO,QAAQ,CAAC2E,GAAG,CAACxE,IAAI,CAACE,EAAE,CAAC,EAAE;QAC9B,IAAI,IAAI,CAACL,QAAQ,CAAC2D,GAAG,CAACxD,IAAI,CAACE,EAAE,CAAC,CAACyC,QAAQ,IAAI,IAAI,CAAC9C,QAAQ,CAAC2D,GAAG,CAACxD,IAAI,CAACE,EAAE,CAAC,CAACoE,QAAQ,EAAE;UAC9E;QACF;MACF,CAAC,MAAM;QACL,IAAI,CAACzE,QAAQ,CAACI,GAAG,CAACD,IAAI,CAACE,EAAE,EAAAC,aAAA,KAAOH,IAAI,CAAE,CAAC;MACzC;MAEA,IAAI,CAAC+D,UAAU,CAAC/D,IAAI,CAACE,EAAE,CAAC;IAC1B;EACF;EAEAJ,KAAKA,CAAA;IACH,IAAI,IAAI,CAACyC,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACjD,KAAK,GAAG,EAAE;IACvB;IACA,IAAI,CAACe,gBAAgB,CAACC,QAAQ,CAAC,IAAI,CAAC;EACtC;EAEAmE,mBAAmBA,CAAA;IACjB,IAAI,CAACjD,cAAc,GAAG,IAAI;EAC5B;EAEAuC,UAAUA,CAAE7D,EAAW;IACrB,IAAI,CAACA,EAAE,EAAE;MACP;IACF;IAEA,IAAI,CAACwE,iBAAiB,CAACxE,EAAE,CAAC;IAC1B,IAAI,CAACkB,aAAa,CAACuD,IAAI,CAACzE,EAAE,CAAC;IAC3B,IAAI,CAACK,cAAc,CAACb,IAAI,CAAC,IAAI,CAAC0B,aAAa,CAAC;EAC9C;EAEAwD,YAAYA,CAAA;IAAA,IAAAC,sBAAA;IACV,CAAAA,sBAAA,OAAI,CAAC1C,mBAAmB,cAAA0C,sBAAA,eAAxBA,sBAAA,CAA0BC,SAAS,EAAE;EACvC;EAEUC,YAAYA,CAAA;IACpB,IAAI,IAAI,CAACxC,KAAK,EAAE;MACd,OAAO,IAAI,CAACA,KAAK,CAACyC,UAAU;IAC9B;IACA,OAAO,KAAK;EACd;EAEQlC,MAAMA,CAAExD,KAAa;IAC3B,OAAO,IAAI,CAACM,KAAK,CACdkD,MAAM,CAAC9C,IAAI,IAAIA,IAAI,CAAC+C,IAAI,CAACL,WAAW,EAAE,CAACwB,OAAO,CAAC5E,KAAK,CAACoD,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9E;EAEQgC,iBAAiBA,CAAExE,EAAU;IACnC,IAAI,IAAI,CAACL,QAAQ,CAAC2E,GAAG,CAACtE,EAAE,CAAC,EAAE;MACzB,IAAI,CAACL,QAAQ,CAAC2D,GAAG,CAACtD,EAAE,CAAC,CAACoE,QAAQ,GAAG,IAAI;IACvC;EACF;EAEQD,gBAAgBA,CAAEnE,EAAU;IAClC,IAAI,IAAI,CAACL,QAAQ,CAAC2E,GAAG,CAACtE,EAAE,CAAC,EAAE;MACzB,IAAI,CAACL,QAAQ,CAAC2D,GAAG,CAACtD,EAAE,CAAC,CAACoE,QAAQ,GAAG,KAAK;IACxC;EACF;EAEQ9E,gBAAgBA,CAAEF,KAAY;IACpC,IAAI2F,KAAK,CAACC,OAAO,CAAC5F,KAAK,CAAC,EAAE;MACxB,IAAI,CAACA,KAAK,CAACsC,MAAM,EAAE;QAAA,IAAAuD,mBAAA;QACjB,CAAAA,mBAAA,OAAI,CAAC/D,aAAa,cAAA+D,mBAAA,eAAlBA,mBAAA,CAAoBpF,OAAO,CAACG,EAAE,IAAI,IAAI,CAACmE,gBAAgB,CAACnE,EAAE,CAAC,CAAC;MAC9D;MAEA,IAAI,CAACkB,aAAa,GAAG9B,KAAK,CAACwC,GAAG,CAAC9B,IAAI,IAAG;QACpC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC5B,OAAOA,IAAI;QACb;QAEA,IAAI,CAAC,IAAI,CAACH,QAAQ,CAAC2E,GAAG,CAACxE,IAAI,CAACE,EAAE,CAAC,EAAE;UAC/B,IAAI,CAACL,QAAQ,CAACI,GAAG,CAACD,IAAI,CAACE,EAAE,EAAAC,aAAA,KAAOH,IAAI,CAAE,CAAC;QACzC;QAEA,OAAOA,IAAI,CAACE,EAAE;MAChB,CAAC,CAAC;MACF,IAAI,CAACkB,aAAa,CAACrB,OAAO,CAACG,EAAE,IAAI,IAAI,CAACwE,iBAAiB,CAACxE,EAAE,CAAC,CAAC;MAC5D,IAAI,CAACK,cAAc,CAACb,IAAI,CAAC,IAAI,CAAC0B,aAAa,CAAC;IAC9C;EACF;;;;;;;;UA3KcpD;EAAQ;IAAAoH,IAAA,EAAInH;EAAI;AAAA,G;;;UAChBD;EAAQ;AAAA,G;;;;UAjErBD;EAAK;;UAWLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UAgCLG,SAAS;IAAAmH,IAAA,GAACrG,QAAQ;EAAA;;UAClBd,SAAS;IAAAmH,IAAA,GAAC,OAAO,EAAE;MAAEC,IAAI,EAAEhH;IAAsB,CAAE;EAAA;;UACnDJ,SAAS;IAAAmH,IAAA,GAAC,MAAM;EAAA;;UAEhBvH;EAAW;;UAEXA,WAAW;IAAAuH,IAAA,GAAC,gBAAgB;EAAA;;AAtDlBjG,8BAA8B,GAAAmG,UAAA,EAT1C3H,SAAS,CAAC;EACP4H,QAAQ,EAAE,6BAA6B;EACvCC,QAAA,EAAAC,oBAAuD;EAIvDC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAEpH,mBAAmB;IAAEqH,WAAW,EAAEzG;EAA8B,CAAE,CAAC;EAC1F0G,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACW1G,8BAA8B,CA6O1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}