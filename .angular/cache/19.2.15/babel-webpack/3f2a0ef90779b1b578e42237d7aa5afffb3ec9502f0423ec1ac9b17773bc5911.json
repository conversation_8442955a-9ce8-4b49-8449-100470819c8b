{"ast": null, "code": "var _SwuiAutoselectComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-autoselect.component.html?ngResource\";\nimport { ChangeDetectionStrategy, Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { BehaviorSubject } from 'rxjs';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { takeUntil } from 'rxjs/operators';\nconst CONTROL_NAME = 'lib-swui-autoselect';\nlet nextUniqueId = 0;\nlet SwuiAutoselectComponent = (_SwuiAutoselectComponent = class SwuiAutoselectComponent extends SwuiMatFormFieldControl {\n  get value() {\n    return this._value;\n  }\n  set value(val) {\n    this._selectedId$.next(val || '');\n    this.stateChanges.next(undefined);\n  }\n  get options() {\n    return this._options;\n  }\n  set options(data) {\n    this._options = data;\n    this.filteredOptions = data;\n  }\n  get empty() {\n    return this._isEmpty;\n  }\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.isNotInOptionsValue = false;\n    this.controlType = CONTROL_NAME;\n    this.inputFormControl = new UntypedFormControl();\n    this.filteredOptions = [];\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._options = [];\n    this._selectedId$ = new BehaviorSubject('');\n    this._isEmpty = true;\n  }\n  ngOnInit() {\n    this.inputFormControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(text => {\n      this.filteredOptions = this._filter(text);\n      let item = this.options.find(option => option.text === text);\n      if (this.isNotInOptionsValue && text && !item) {\n        item = {\n          id: text,\n          text: text\n        };\n      }\n      this._value = item ? item.id : undefined;\n      this._isEmpty = !this._value;\n      this.onChange(this._value);\n    });\n    this._selectedId$.pipe(takeUntil(this.destroyed$)).subscribe(id => {\n      this.setInputValue(id);\n    });\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.input && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.input.focus();\n    }\n  }\n  writeValue(id) {\n    if (!id) {\n      return;\n    }\n    this._selectedId$.next(id);\n  }\n  onDisabledState(disabled) {\n    disabled ? this.inputFormControl.disable() : this.inputFormControl.enable();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n  _filter(value) {\n    const filterValue = value ? value.toLowerCase() : '';\n    return this.options.filter(option => option.text.toLowerCase().includes(filterValue));\n  }\n  setInputValue(id) {\n    let item = this.options.find(option => option.id === id && !option.disabled);\n    if (this.isNotInOptionsValue && id && !item) {\n      item = {\n        id: id,\n        text: id\n      };\n    }\n    this.inputFormControl.setValue(item ? item.text : undefined);\n  }\n}, _SwuiAutoselectComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}], _SwuiAutoselectComponent.propDecorators = {\n  value: [{\n    type: Input\n  }],\n  options: [{\n    type: Input\n  }],\n  isNotInOptionsValue: [{\n    type: Input\n  }],\n  input: [{\n    type: ViewChild,\n    args: [MatInput]\n  }],\n  matAutocomplete: [{\n    type: ViewChild,\n    args: ['auto']\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiAutoselectComponent);\nSwuiAutoselectComponent = __decorate([Component({\n  selector: 'lib-swui-autoselect',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiAutoselectComponent\n  }],\n  standalone: false\n})], SwuiAutoselectComponent);\nexport { SwuiAutoselectComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "ChangeDetectionStrategy", "Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormControl", "FormGroupDirective", "NgControl", "BehaviorSubject", "FocusMonitor", "MatFormFieldControl", "MatInput", "SwuiMatFormFieldControl", "ErrorStateMatcher", "takeUntil", "CONTROL_NAME", "nextUniqueId", "SwuiAutoselectComponent", "_SwuiAutoselectComponent", "value", "_value", "val", "_selectedId$", "next", "stateChanges", "undefined", "options", "_options", "data", "filteredOptions", "empty", "_isEmpty", "shouldLabelFloat", "focused", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "isNotInOptionsValue", "controlType", "inputFormControl", "id", "ngOnInit", "valueChanges", "pipe", "destroyed$", "subscribe", "text", "_filter", "item", "find", "option", "onChange", "setInputValue", "onContainerClick", "event", "stopPropagation", "input", "target", "tagName", "toLowerCase", "disabled", "focus", "writeValue", "onDisabledState", "disable", "enable", "isErrorState", "errorState", "filterValue", "filter", "includes", "setValue", "ctorParameters", "type", "decorators", "propDecorators", "args", "matAutocomplete", "selector", "template", "changeDetection", "OnPush", "providers", "provide", "useExisting", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-autoselect/swui-autoselect.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-autoselect.component.html?ngResource\";\nimport { ChangeDetectionStrategy, Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { BehaviorSubject } from 'rxjs';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { takeUntil } from 'rxjs/operators';\nconst CONTROL_NAME = 'lib-swui-autoselect';\nlet nextUniqueId = 0;\nlet SwuiAutoselectComponent = class SwuiAutoselectComponent extends SwuiMatFormFieldControl {\n    get value() {\n        return this._value;\n    }\n    set value(val) {\n        this._selectedId$.next(val || '');\n        this.stateChanges.next(undefined);\n    }\n    get options() {\n        return this._options;\n    }\n    set options(data) {\n        this._options = data;\n        this.filteredOptions = data;\n    }\n    get empty() {\n        return this._isEmpty;\n    }\n    get shouldLabelFloat() {\n        return this.focused || !this.empty;\n    }\n    constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n        super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n        this.isNotInOptionsValue = false;\n        this.controlType = CONTROL_NAME;\n        this.inputFormControl = new UntypedFormControl();\n        this.filteredOptions = [];\n        this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n        this._options = [];\n        this._selectedId$ = new BehaviorSubject('');\n        this._isEmpty = true;\n    }\n    ngOnInit() {\n        this.inputFormControl.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe((text) => {\n            this.filteredOptions = this._filter(text);\n            let item = this.options.find((option) => option.text === text);\n            if (this.isNotInOptionsValue && text && !item) {\n                item = {\n                    id: text,\n                    text: text,\n                };\n            }\n            this._value = item ? item.id : undefined;\n            this._isEmpty = !this._value;\n            this.onChange(this._value);\n        });\n        this._selectedId$.pipe(takeUntil(this.destroyed$)).subscribe((id) => {\n            this.setInputValue(id);\n        });\n    }\n    onContainerClick(event) {\n        event.stopPropagation();\n        if (this.input && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n            this.input.focus();\n        }\n    }\n    writeValue(id) {\n        if (!id) {\n            return;\n        }\n        this._selectedId$.next(id);\n    }\n    onDisabledState(disabled) {\n        disabled ? this.inputFormControl.disable() : this.inputFormControl.enable();\n    }\n    isErrorState() {\n        if (this.input) {\n            return this.input.errorState;\n        }\n        return false;\n    }\n    _filter(value) {\n        const filterValue = value ? value.toLowerCase() : '';\n        return this.options.filter((option) => option.text.toLowerCase().includes(filterValue));\n    }\n    setInputValue(id) {\n        let item = this.options.find((option) => option.id === id && !option.disabled);\n        if (this.isNotInOptionsValue && id && !item) {\n            item = {\n                id: id,\n                text: id,\n            };\n        }\n        this.inputFormControl.setValue(item ? item.text : undefined);\n    }\n    static { this.ctorParameters = () => [\n        { type: FocusMonitor },\n        { type: ElementRef },\n        { type: NgControl, decorators: [{ type: Optional }, { type: Self }] },\n        { type: FormGroupDirective, decorators: [{ type: Optional }] },\n        { type: ErrorStateMatcher }\n    ]; }\n    static { this.propDecorators = {\n        value: [{ type: Input }],\n        options: [{ type: Input }],\n        isNotInOptionsValue: [{ type: Input }],\n        input: [{ type: ViewChild, args: [MatInput,] }],\n        matAutocomplete: [{ type: ViewChild, args: ['auto',] }],\n        id: [{ type: HostBinding }],\n        shouldLabelFloat: [{ type: HostBinding, args: ['class.floating',] }]\n    }; }\n};\nSwuiAutoselectComponent = __decorate([\n    Component({\n        selector: 'lib-swui-autoselect',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        providers: [{ provide: MatFormFieldControl, useExisting: SwuiAutoselectComponent }],\n        standalone: false\n    })\n], SwuiAutoselectComponent);\nexport { SwuiAutoselectComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,SAASC,uBAAuB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AAC7H,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAClF,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,MAAMC,YAAY,GAAG,qBAAqB;AAC1C,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,uBAAuB,IAAAC,wBAAA,GAAG,MAAMD,uBAAuB,SAASL,uBAAuB,CAAC;EACxF,IAAIO,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACE,GAAG,EAAE;IACX,IAAI,CAACC,YAAY,CAACC,IAAI,CAACF,GAAG,IAAI,EAAE,CAAC;IACjC,IAAI,CAACG,YAAY,CAACD,IAAI,CAACE,SAAS,CAAC;EACrC;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,IAAI,EAAE;IACd,IAAI,CAACD,QAAQ,GAAGC,IAAI;IACpB,IAAI,CAACC,eAAe,GAAGD,IAAI;EAC/B;EACA,IAAIE,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACH,KAAK;EACtC;EACAI,WAAWA,CAACC,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,EAAE;IAClE,KAAK,CAACJ,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAC/D,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,WAAW,GAAG1B,YAAY;IAC/B,IAAI,CAAC2B,gBAAgB,GAAG,IAAIrC,kBAAkB,CAAC,CAAC;IAChD,IAAI,CAACwB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACc,EAAE,GAAG,GAAG5B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAC7C,IAAI,CAACW,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACL,YAAY,GAAG,IAAId,eAAe,CAAC,EAAE,CAAC;IAC3C,IAAI,CAACuB,QAAQ,GAAG,IAAI;EACxB;EACAa,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,gBAAgB,CAACG,YAAY,CAACC,IAAI,CAAChC,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAAC,CAACC,SAAS,CAAEC,IAAI,IAAK;MACpF,IAAI,CAACpB,eAAe,GAAG,IAAI,CAACqB,OAAO,CAACD,IAAI,CAAC;MACzC,IAAIE,IAAI,GAAG,IAAI,CAACzB,OAAO,CAAC0B,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACJ,IAAI,KAAKA,IAAI,CAAC;MAC9D,IAAI,IAAI,CAACT,mBAAmB,IAAIS,IAAI,IAAI,CAACE,IAAI,EAAE;QAC3CA,IAAI,GAAG;UACHR,EAAE,EAAEM,IAAI;UACRA,IAAI,EAAEA;QACV,CAAC;MACL;MACA,IAAI,CAAC7B,MAAM,GAAG+B,IAAI,GAAGA,IAAI,CAACR,EAAE,GAAGlB,SAAS;MACxC,IAAI,CAACM,QAAQ,GAAG,CAAC,IAAI,CAACX,MAAM;MAC5B,IAAI,CAACkC,QAAQ,CAAC,IAAI,CAAClC,MAAM,CAAC;IAC9B,CAAC,CAAC;IACF,IAAI,CAACE,YAAY,CAACwB,IAAI,CAAChC,SAAS,CAAC,IAAI,CAACiC,UAAU,CAAC,CAAC,CAACC,SAAS,CAAEL,EAAE,IAAK;MACjE,IAAI,CAACY,aAAa,CAACZ,EAAE,CAAC;IAC1B,CAAC,CAAC;EACN;EACAa,gBAAgBA,CAACC,KAAK,EAAE;IACpBA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACC,KAAK,IAAIF,KAAK,CAACG,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAChF,IAAI,CAACJ,KAAK,CAACK,KAAK,CAAC,CAAC;IACtB;EACJ;EACAC,UAAUA,CAACtB,EAAE,EAAE;IACX,IAAI,CAACA,EAAE,EAAE;MACL;IACJ;IACA,IAAI,CAACrB,YAAY,CAACC,IAAI,CAACoB,EAAE,CAAC;EAC9B;EACAuB,eAAeA,CAACH,QAAQ,EAAE;IACtBA,QAAQ,GAAG,IAAI,CAACrB,gBAAgB,CAACyB,OAAO,CAAC,CAAC,GAAG,IAAI,CAACzB,gBAAgB,CAAC0B,MAAM,CAAC,CAAC;EAC/E;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACV,KAAK,EAAE;MACZ,OAAO,IAAI,CAACA,KAAK,CAACW,UAAU;IAChC;IACA,OAAO,KAAK;EAChB;EACApB,OAAOA,CAAC/B,KAAK,EAAE;IACX,MAAMoD,WAAW,GAAGpD,KAAK,GAAGA,KAAK,CAAC2C,WAAW,CAAC,CAAC,GAAG,EAAE;IACpD,OAAO,IAAI,CAACpC,OAAO,CAAC8C,MAAM,CAAEnB,MAAM,IAAKA,MAAM,CAACJ,IAAI,CAACa,WAAW,CAAC,CAAC,CAACW,QAAQ,CAACF,WAAW,CAAC,CAAC;EAC3F;EACAhB,aAAaA,CAACZ,EAAE,EAAE;IACd,IAAIQ,IAAI,GAAG,IAAI,CAACzB,OAAO,CAAC0B,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACV,EAAE,KAAKA,EAAE,IAAI,CAACU,MAAM,CAACU,QAAQ,CAAC;IAC9E,IAAI,IAAI,CAACvB,mBAAmB,IAAIG,EAAE,IAAI,CAACQ,IAAI,EAAE;MACzCA,IAAI,GAAG;QACHR,EAAE,EAAEA,EAAE;QACNM,IAAI,EAAEN;MACV,CAAC;IACL;IACA,IAAI,CAACD,gBAAgB,CAACgC,QAAQ,CAACvB,IAAI,GAAGA,IAAI,CAACF,IAAI,GAAGxB,SAAS,CAAC;EAChE;AAiBJ,CAAC,EAhBYP,wBAAA,CAAKyD,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEnE;AAAa,CAAC,EACtB;EAAEmE,IAAI,EAAE7E;AAAW,CAAC,EACpB;EAAE6E,IAAI,EAAErE,SAAS;EAAEsE,UAAU,EAAE,CAAC;IAAED,IAAI,EAAE1E;EAAS,CAAC,EAAE;IAAE0E,IAAI,EAAEzE;EAAK,CAAC;AAAE,CAAC,EACrE;EAAEyE,IAAI,EAAEtE,kBAAkB;EAAEuE,UAAU,EAAE,CAAC;IAAED,IAAI,EAAE1E;EAAS,CAAC;AAAE,CAAC,EAC9D;EAAE0E,IAAI,EAAE/D;AAAkB,CAAC,CAC9B,EACQK,wBAAA,CAAK4D,cAAc,GAAG;EAC3B3D,KAAK,EAAE,CAAC;IAAEyD,IAAI,EAAE3E;EAAM,CAAC,CAAC;EACxByB,OAAO,EAAE,CAAC;IAAEkD,IAAI,EAAE3E;EAAM,CAAC,CAAC;EAC1BuC,mBAAmB,EAAE,CAAC;IAAEoC,IAAI,EAAE3E;EAAM,CAAC,CAAC;EACtC0D,KAAK,EAAE,CAAC;IAAEiB,IAAI,EAAExE,SAAS;IAAE2E,IAAI,EAAE,CAACpE,QAAQ;EAAG,CAAC,CAAC;EAC/CqE,eAAe,EAAE,CAAC;IAAEJ,IAAI,EAAExE,SAAS;IAAE2E,IAAI,EAAE,CAAC,MAAM;EAAG,CAAC,CAAC;EACvDpC,EAAE,EAAE,CAAC;IAAEiC,IAAI,EAAE5E;EAAY,CAAC,CAAC;EAC3BgC,gBAAgB,EAAE,CAAC;IAAE4C,IAAI,EAAE5E,WAAW;IAAE+E,IAAI,EAAE,CAAC,gBAAgB;EAAG,CAAC;AACvE,CAAC,EAAA7D,wBAAA,CACJ;AACDD,uBAAuB,GAAGtB,UAAU,CAAC,CACjCG,SAAS,CAAC;EACNmF,QAAQ,EAAE,qBAAqB;EAC/BC,QAAQ,EAAEtF,oBAAoB;EAC9BuF,eAAe,EAAEtF,uBAAuB,CAACuF,MAAM;EAC/CC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAE5E,mBAAmB;IAAE6E,WAAW,EAAEtE;EAAwB,CAAC,CAAC;EACnFuE,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEvE,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}