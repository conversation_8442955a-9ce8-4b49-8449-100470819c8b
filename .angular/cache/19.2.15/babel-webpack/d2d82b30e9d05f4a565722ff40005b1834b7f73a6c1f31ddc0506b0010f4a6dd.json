{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./number.widget.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { SwuiTdStringWidgetComponent } from '../string/string.widget';\nlet SwuiTdNumberWidgetComponent = class SwuiTdNumberWidgetComponent extends SwuiTdStringWidgetComponent {};\nSwuiTdNumberWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-number-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiTdNumberWidgetComponent);\nexport { SwuiTdNumberWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "SwuiTdStringWidgetComponent", "SwuiTdNumberWidgetComponent", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/number/number.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./number.widget.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { SwuiTdStringWidgetComponent } from '../string/string.widget';\nlet SwuiTdNumberWidgetComponent = class SwuiTdNumberWidgetComponent extends SwuiTdStringWidgetComponent {\n};\nSwuiTdNumberWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-number-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiTdNumberWidgetComponent);\nexport { SwuiTdNumberWidgetComponent };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,2BAA2B,QAAQ,yBAAyB;AACrE,IAAIC,2BAA2B,GAAG,MAAMA,2BAA2B,SAASD,2BAA2B,CAAC,EACvG;AACDC,2BAA2B,GAAGJ,UAAU,CAAC,CACrCE,SAAS,CAAC;EACNG,QAAQ,EAAE,2BAA2B;EACrCC,QAAQ,EAAEL,oBAAoB;EAC9BM,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEH,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}