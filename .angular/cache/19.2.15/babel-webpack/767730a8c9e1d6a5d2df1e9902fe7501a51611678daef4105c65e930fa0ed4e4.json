{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiChipsAutocompleteComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-chips-autocomplete.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-chips-autocomplete.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { COMMA, ENTER } from '@angular/cdk/keycodes';\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatAutocompleteTrigger } from '@angular/material/autocomplete';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { startWith, switchMap, take, tap } from 'rxjs/operators';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nconst CONTROL_NAME = 'lib-swui-chips-autocomplete';\nlet nextUniqueId = 0;\nlet SwuiChipsAutocompleteComponent = (_SwuiChipsAutocompleteComponent = class SwuiChipsAutocompleteComponent extends SwuiMatFormFieldControl {\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    this._value = value;\n    this.setSelectedItems(value);\n    this.stateChanges.next(undefined);\n  }\n  set items(value) {\n    if (!!value) {\n      this.itemsMap.clear();\n      value.forEach(item => {\n        this.itemsMap.set(item.id, _objectSpread({}, item));\n      });\n      this._items = value;\n      this.inputFormControl.setValue(this.inputFormControl.value);\n      this.setSelectedItems(this.selectedItems$.value);\n    }\n  }\n  get items() {\n    return this._items;\n  }\n  get empty() {\n    return this.isEmpty;\n  }\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.filteredItems = new Observable();\n    this.inputFormControl = new UntypedFormControl();\n    this.itemsMap = new Map();\n    this.selectedItems = [];\n    this.separatorKeysCodes = [ENTER, COMMA];\n    this.isEmpty = true;\n    this.controlType = CONTROL_NAME;\n    this.hasFounded = true;\n    this.selectedItems$ = new BehaviorSubject([]);\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._value = [];\n    this._items = [];\n    this.blockBlurEvent = false;\n  }\n  ngOnInit() {\n    this.selectedItems$.subscribe(val => {\n      this.selectedItems = val;\n      this._value = this.selectedItems;\n      this.isEmpty = this.selectedItems.length === 0;\n      this.onChange(this._value.map(item => {\n        return this.mapFn ? this.mapFn(item) : item;\n      }));\n    });\n    this.initFilteredItems();\n  }\n  closePanel() {\n    if (!this.blockBlurEvent) {\n      var _this$autoCompleteTri;\n      (_this$autoCompleteTri = this.autoCompleteTrigger) === null || _this$autoCompleteTri === void 0 || _this$autoCompleteTri.closePanel();\n    }\n    this.blockBlurEvent = false;\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.input && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.input.focus();\n    }\n  }\n  writeValue(value) {\n    this.setSelectedItems(value);\n  }\n  initFilteredItems() {\n    const filter = text => {\n      return of(text ? this.filter(text) : this.items.slice());\n    };\n    const search = this.searchFn || filter;\n    let value = '';\n    this.filteredItems = this.inputFormControl.valueChanges.pipe(startWith(this.inputFormControl.value), tap(text => {\n      value = text && text.toString() || '';\n    }), switchMap(() => search(value)), tap(items => this.hasFounded = items.some(item => item.text.toLowerCase() === value.toLowerCase())));\n  }\n  getItemText(id) {\n    const item = this.itemsMap.get(id);\n    return item ? item.text : '';\n  }\n  add(option) {\n    const value = option.source.value;\n    if ((value || '').trim() && this.addFn) {\n      this.addFn(value).pipe(take(1)).subscribe(tag => {\n        this.itemsMap.set(tag.id, _objectSpread({}, tag));\n        this.selectItem(tag.id);\n        this.inputFormControl.setValue('');\n      });\n    }\n  }\n  remove(id) {\n    const index = this.selectedItems.indexOf(id);\n    if (index > -1) {\n      this.selectedItems.splice(index, 1);\n      setTimeout(() => {\n        this.selectedItems$.next(this.selectedItems);\n      }, 0);\n      this.enableOptionById(id);\n    }\n  }\n  selected(event) {\n    if (event.isUserInput) {\n      const item = event.source.value;\n      if (this.itemsMap.has(item.id)) {\n        if (this.itemsMap.get(item.id).disabled || this.itemsMap.get(item.id).selected) {\n          return;\n        }\n      } else {\n        this.itemsMap.set(item.id, _objectSpread({}, item));\n      }\n      this.selectItem(item.id);\n    }\n  }\n  clear() {\n    if (this.input) {\n      this.input.value = '';\n    }\n    this.inputFormControl.setValue(null);\n  }\n  onMenuItemMousedown() {\n    this.blockBlurEvent = true;\n  }\n  selectItem(id) {\n    if (!id) {\n      return;\n    }\n    this.disableOptionById(id);\n    this.selectedItems.push(id);\n    this.selectedItems$.next(this.selectedItems);\n  }\n  onInputClick() {\n    var _this$autoCompleteTri2;\n    (_this$autoCompleteTri2 = this.autoCompleteTrigger) === null || _this$autoCompleteTri2 === void 0 || _this$autoCompleteTri2.openPanel();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n  filter(value) {\n    return this.items.filter(item => item.text.toLowerCase().indexOf(value.toLowerCase()) > -1);\n  }\n  disableOptionById(id) {\n    if (this.itemsMap.has(id)) {\n      this.itemsMap.get(id).selected = true;\n    }\n  }\n  enableOptionById(id) {\n    if (this.itemsMap.has(id)) {\n      this.itemsMap.get(id).selected = false;\n    }\n  }\n  setSelectedItems(value) {\n    if (Array.isArray(value)) {\n      if (!value.length) {\n        var _this$selectedItems;\n        (_this$selectedItems = this.selectedItems) === null || _this$selectedItems === void 0 || _this$selectedItems.forEach(id => this.enableOptionById(id));\n      }\n      this.selectedItems = value.map(item => {\n        if (typeof item === 'string') {\n          return item;\n        }\n        if (!this.itemsMap.has(item.id)) {\n          this.itemsMap.set(item.id, _objectSpread({}, item));\n        }\n        return item.id;\n      });\n      this.selectedItems.forEach(id => this.disableOptionById(id));\n      this.selectedItems$.next(this.selectedItems);\n    }\n  }\n}, _SwuiChipsAutocompleteComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}], _SwuiChipsAutocompleteComponent.propDecorators = {\n  value: [{\n    type: Input\n  }],\n  searchFn: [{\n    type: Input\n  }],\n  addFn: [{\n    type: Input\n  }],\n  mapFn: [{\n    type: Input\n  }],\n  items: [{\n    type: Input\n  }],\n  input: [{\n    type: ViewChild,\n    args: [MatInput]\n  }],\n  autoCompleteTrigger: [{\n    type: ViewChild,\n    args: ['input', {\n      read: MatAutocompleteTrigger\n    }]\n  }],\n  matAutocomplete: [{\n    type: ViewChild,\n    args: ['auto']\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiChipsAutocompleteComponent);\nSwuiChipsAutocompleteComponent = __decorate([Component({\n  selector: 'lib-swui-chips-autocomplete',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiChipsAutocompleteComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiChipsAutocompleteComponent);\nexport { SwuiChipsAutocompleteComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "FocusMonitor", "COMMA", "ENTER", "Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormControl", "FormGroupDirective", "NgControl", "MatAutocompleteTrigger", "ErrorStateMatcher", "MatFormFieldControl", "BehaviorSubject", "Observable", "of", "startWith", "switchMap", "take", "tap", "MatInput", "SwuiMatFormFieldControl", "CONTROL_NAME", "nextUniqueId", "SwuiChipsAutocompleteComponent", "_SwuiChipsAutocompleteComponent", "value", "_value", "setSelectedItems", "stateChanges", "next", "undefined", "items", "itemsMap", "clear", "for<PERSON>ach", "item", "set", "id", "_objectSpread", "_items", "inputFormControl", "setValue", "selectedItems$", "empty", "isEmpty", "shouldLabelFloat", "focused", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "filteredItems", "Map", "selectedItems", "separatorKeysCodes", "controlType", "hasFounded", "blockBlurEvent", "ngOnInit", "subscribe", "val", "length", "onChange", "map", "mapFn", "initFilteredItems", "closePanel", "_this$autoCompleteTri", "autoCompleteTrigger", "onContainerClick", "event", "stopPropagation", "input", "target", "tagName", "toLowerCase", "disabled", "focus", "writeValue", "filter", "text", "slice", "search", "searchFn", "valueChanges", "pipe", "toString", "some", "getItemText", "get", "add", "option", "source", "trim", "addFn", "tag", "selectItem", "remove", "index", "indexOf", "splice", "setTimeout", "enableOptionById", "selected", "isUserInput", "has", "onMenuItemMousedown", "disableOptionById", "push", "onInputClick", "_this$autoCompleteTri2", "openPanel", "isErrorState", "errorState", "Array", "isArray", "_this$selectedItems", "ctorParameters", "type", "decorators", "propDecorators", "args", "read", "matAutocomplete", "selector", "template", "providers", "provide", "useExisting", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-chips-autocomplete/swui-chips-autocomplete.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-chips-autocomplete.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-chips-autocomplete.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { COMMA, ENTER } from '@angular/cdk/keycodes';\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatAutocompleteTrigger } from '@angular/material/autocomplete';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { startWith, switchMap, take, tap } from 'rxjs/operators';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nconst CONTROL_NAME = 'lib-swui-chips-autocomplete';\nlet nextUniqueId = 0;\nlet SwuiChipsAutocompleteComponent = class SwuiChipsAutocompleteComponent extends SwuiMatFormFieldControl {\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        this._value = value;\n        this.setSelectedItems(value);\n        this.stateChanges.next(undefined);\n    }\n    set items(value) {\n        if (!!value) {\n            this.itemsMap.clear();\n            value.forEach(item => {\n                this.itemsMap.set(item.id, { ...item });\n            });\n            this._items = value;\n            this.inputFormControl.setValue(this.inputFormControl.value);\n            this.setSelectedItems(this.selectedItems$.value);\n        }\n    }\n    get items() {\n        return this._items;\n    }\n    get empty() {\n        return this.isEmpty;\n    }\n    get shouldLabelFloat() {\n        return this.focused || !this.empty;\n    }\n    constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n        super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n        this.filteredItems = new Observable();\n        this.inputFormControl = new UntypedFormControl();\n        this.itemsMap = new Map();\n        this.selectedItems = [];\n        this.separatorKeysCodes = [ENTER, COMMA];\n        this.isEmpty = true;\n        this.controlType = CONTROL_NAME;\n        this.hasFounded = true;\n        this.selectedItems$ = new BehaviorSubject([]);\n        this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n        this._value = [];\n        this._items = [];\n        this.blockBlurEvent = false;\n    }\n    ngOnInit() {\n        this.selectedItems$\n            .subscribe((val) => {\n            this.selectedItems = val;\n            this._value = this.selectedItems;\n            this.isEmpty = this.selectedItems.length === 0;\n            this.onChange(this._value.map(item => {\n                return this.mapFn ? this.mapFn(item) : item;\n            }));\n        });\n        this.initFilteredItems();\n    }\n    closePanel() {\n        if (!this.blockBlurEvent) {\n            this.autoCompleteTrigger?.closePanel();\n        }\n        this.blockBlurEvent = false;\n    }\n    onContainerClick(event) {\n        event.stopPropagation();\n        if (this.input && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n            this.input.focus();\n        }\n    }\n    writeValue(value) {\n        this.setSelectedItems(value);\n    }\n    initFilteredItems() {\n        const filter = (text) => {\n            return of(text ? this.filter(text) : this.items.slice());\n        };\n        const search = this.searchFn || filter;\n        let value = '';\n        this.filteredItems = this.inputFormControl.valueChanges.pipe(startWith((this.inputFormControl.value)), tap(text => {\n            value = text && text.toString() || '';\n        }), switchMap(() => search(value)), tap(items => this.hasFounded = items.some(item => item.text.toLowerCase() === value.toLowerCase())));\n    }\n    getItemText(id) {\n        const item = this.itemsMap.get(id);\n        return item ? item.text : '';\n    }\n    add(option) {\n        const value = option.source.value;\n        if ((value || '').trim() && this.addFn) {\n            this.addFn(value)\n                .pipe(take(1))\n                .subscribe(tag => {\n                this.itemsMap.set(tag.id, { ...tag });\n                this.selectItem(tag.id);\n                this.inputFormControl.setValue('');\n            });\n        }\n    }\n    remove(id) {\n        const index = this.selectedItems.indexOf(id);\n        if (index > -1) {\n            this.selectedItems.splice(index, 1);\n            setTimeout(() => {\n                this.selectedItems$.next(this.selectedItems);\n            }, 0);\n            this.enableOptionById(id);\n        }\n    }\n    selected(event) {\n        if (event.isUserInput) {\n            const item = event.source.value;\n            if (this.itemsMap.has(item.id)) {\n                if (this.itemsMap.get(item.id).disabled || this.itemsMap.get(item.id).selected) {\n                    return;\n                }\n            }\n            else {\n                this.itemsMap.set(item.id, { ...item });\n            }\n            this.selectItem(item.id);\n        }\n    }\n    clear() {\n        if (this.input) {\n            this.input.value = '';\n        }\n        this.inputFormControl.setValue(null);\n    }\n    onMenuItemMousedown() {\n        this.blockBlurEvent = true;\n    }\n    selectItem(id) {\n        if (!id) {\n            return;\n        }\n        this.disableOptionById(id);\n        this.selectedItems.push(id);\n        this.selectedItems$.next(this.selectedItems);\n    }\n    onInputClick() {\n        this.autoCompleteTrigger?.openPanel();\n    }\n    isErrorState() {\n        if (this.input) {\n            return this.input.errorState;\n        }\n        return false;\n    }\n    filter(value) {\n        return this.items\n            .filter(item => item.text.toLowerCase().indexOf(value.toLowerCase()) > -1);\n    }\n    disableOptionById(id) {\n        if (this.itemsMap.has(id)) {\n            this.itemsMap.get(id).selected = true;\n        }\n    }\n    enableOptionById(id) {\n        if (this.itemsMap.has(id)) {\n            this.itemsMap.get(id).selected = false;\n        }\n    }\n    setSelectedItems(value) {\n        if (Array.isArray(value)) {\n            if (!value.length) {\n                this.selectedItems?.forEach(id => this.enableOptionById(id));\n            }\n            this.selectedItems = value.map(item => {\n                if (typeof item === 'string') {\n                    return item;\n                }\n                if (!this.itemsMap.has(item.id)) {\n                    this.itemsMap.set(item.id, { ...item });\n                }\n                return item.id;\n            });\n            this.selectedItems.forEach(id => this.disableOptionById(id));\n            this.selectedItems$.next(this.selectedItems);\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: FocusMonitor },\n        { type: ElementRef },\n        { type: NgControl, decorators: [{ type: Optional }, { type: Self }] },\n        { type: FormGroupDirective, decorators: [{ type: Optional }] },\n        { type: ErrorStateMatcher }\n    ]; }\n    static { this.propDecorators = {\n        value: [{ type: Input }],\n        searchFn: [{ type: Input }],\n        addFn: [{ type: Input }],\n        mapFn: [{ type: Input }],\n        items: [{ type: Input }],\n        input: [{ type: ViewChild, args: [MatInput,] }],\n        autoCompleteTrigger: [{ type: ViewChild, args: ['input', { read: MatAutocompleteTrigger },] }],\n        matAutocomplete: [{ type: ViewChild, args: ['auto',] }],\n        id: [{ type: HostBinding }],\n        shouldLabelFloat: [{ type: HostBinding, args: ['class.floating',] }]\n    }; }\n};\nSwuiChipsAutocompleteComponent = __decorate([\n    Component({\n        selector: 'lib-swui-chips-autocomplete',\n        template: __NG_CLI_RESOURCE__0,\n        providers: [{ provide: MatFormFieldControl, useExisting: SwuiChipsAutocompleteComponent }],\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiChipsAutocompleteComponent);\nexport { SwuiChipsAutocompleteComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,qDAAqD;AACtF,OAAOC,oBAAoB,MAAM,qDAAqD;AACtF,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpD,SAASC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AACpG,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAClF,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,eAAe,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AACtD,SAASC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,QAAQ,gBAAgB;AAChE,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,MAAMC,YAAY,GAAG,6BAA6B;AAClD,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,8BAA8B,IAAAC,+BAAA,GAAG,MAAMD,8BAA8B,SAASH,uBAAuB,CAAC;EACtG,IAAIK,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,gBAAgB,CAACF,KAAK,CAAC;IAC5B,IAAI,CAACG,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;EACrC;EACA,IAAIC,KAAKA,CAACN,KAAK,EAAE;IACb,IAAI,CAAC,CAACA,KAAK,EAAE;MACT,IAAI,CAACO,QAAQ,CAACC,KAAK,CAAC,CAAC;MACrBR,KAAK,CAACS,OAAO,CAACC,IAAI,IAAI;QAClB,IAAI,CAACH,QAAQ,CAACI,GAAG,CAACD,IAAI,CAACE,EAAE,EAAAC,aAAA,KAAOH,IAAI,CAAE,CAAC;MAC3C,CAAC,CAAC;MACF,IAAI,CAACI,MAAM,GAAGd,KAAK;MACnB,IAAI,CAACe,gBAAgB,CAACC,QAAQ,CAAC,IAAI,CAACD,gBAAgB,CAACf,KAAK,CAAC;MAC3D,IAAI,CAACE,gBAAgB,CAAC,IAAI,CAACe,cAAc,CAACjB,KAAK,CAAC;IACpD;EACJ;EACA,IAAIM,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACQ,MAAM;EACtB;EACA,IAAII,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACH,KAAK;EACtC;EACAI,WAAWA,CAACC,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,EAAE;IAClE,KAAK,CAACJ,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAC/D,IAAI,CAACC,aAAa,GAAG,IAAIxC,UAAU,CAAC,CAAC;IACrC,IAAI,CAAC2B,gBAAgB,GAAG,IAAIlC,kBAAkB,CAAC,CAAC;IAChD,IAAI,CAAC0B,QAAQ,GAAG,IAAIsB,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,kBAAkB,GAAG,CAAC1D,KAAK,EAAED,KAAK,CAAC;IACxC,IAAI,CAAC+C,OAAO,GAAG,IAAI;IACnB,IAAI,CAACa,WAAW,GAAGpC,YAAY;IAC/B,IAAI,CAACqC,UAAU,GAAG,IAAI;IACtB,IAAI,CAAChB,cAAc,GAAG,IAAI9B,eAAe,CAAC,EAAE,CAAC;IAC7C,IAAI,CAACyB,EAAE,GAAG,GAAGhB,YAAY,IAAIC,YAAY,EAAE,EAAE;IAC7C,IAAI,CAACI,MAAM,GAAG,EAAE;IAChB,IAAI,CAACa,MAAM,GAAG,EAAE;IAChB,IAAI,CAACoB,cAAc,GAAG,KAAK;EAC/B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAClB,cAAc,CACdmB,SAAS,CAAEC,GAAG,IAAK;MACpB,IAAI,CAACP,aAAa,GAAGO,GAAG;MACxB,IAAI,CAACpC,MAAM,GAAG,IAAI,CAAC6B,aAAa;MAChC,IAAI,CAACX,OAAO,GAAG,IAAI,CAACW,aAAa,CAACQ,MAAM,KAAK,CAAC;MAC9C,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACtC,MAAM,CAACuC,GAAG,CAAC9B,IAAI,IAAI;QAClC,OAAO,IAAI,CAAC+B,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC/B,IAAI,CAAC,GAAGA,IAAI;MAC/C,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACF,IAAI,CAACgC,iBAAiB,CAAC,CAAC;EAC5B;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACT,cAAc,EAAE;MAAA,IAAAU,qBAAA;MACtB,CAAAA,qBAAA,OAAI,CAACC,mBAAmB,cAAAD,qBAAA,eAAxBA,qBAAA,CAA0BD,UAAU,CAAC,CAAC;IAC1C;IACA,IAAI,CAACT,cAAc,GAAG,KAAK;EAC/B;EACAY,gBAAgBA,CAACC,KAAK,EAAE;IACpBA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACC,KAAK,IAAIF,KAAK,CAACG,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAChF,IAAI,CAACJ,KAAK,CAACK,KAAK,CAAC,CAAC;IACtB;EACJ;EACAC,UAAUA,CAACvD,KAAK,EAAE;IACd,IAAI,CAACE,gBAAgB,CAACF,KAAK,CAAC;EAChC;EACA0C,iBAAiBA,CAAA,EAAG;IAChB,MAAMc,MAAM,GAAIC,IAAI,IAAK;MACrB,OAAOpE,EAAE,CAACoE,IAAI,GAAG,IAAI,CAACD,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACnD,KAAK,CAACoD,KAAK,CAAC,CAAC,CAAC;IAC5D,CAAC;IACD,MAAMC,MAAM,GAAG,IAAI,CAACC,QAAQ,IAAIJ,MAAM;IACtC,IAAIxD,KAAK,GAAG,EAAE;IACd,IAAI,CAAC4B,aAAa,GAAG,IAAI,CAACb,gBAAgB,CAAC8C,YAAY,CAACC,IAAI,CAACxE,SAAS,CAAE,IAAI,CAACyB,gBAAgB,CAACf,KAAM,CAAC,EAAEP,GAAG,CAACgE,IAAI,IAAI;MAC/GzD,KAAK,GAAGyD,IAAI,IAAIA,IAAI,CAACM,QAAQ,CAAC,CAAC,IAAI,EAAE;IACzC,CAAC,CAAC,EAAExE,SAAS,CAAC,MAAMoE,MAAM,CAAC3D,KAAK,CAAC,CAAC,EAAEP,GAAG,CAACa,KAAK,IAAI,IAAI,CAAC2B,UAAU,GAAG3B,KAAK,CAAC0D,IAAI,CAACtD,IAAI,IAAIA,IAAI,CAAC+C,IAAI,CAACL,WAAW,CAAC,CAAC,KAAKpD,KAAK,CAACoD,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5I;EACAa,WAAWA,CAACrD,EAAE,EAAE;IACZ,MAAMF,IAAI,GAAG,IAAI,CAACH,QAAQ,CAAC2D,GAAG,CAACtD,EAAE,CAAC;IAClC,OAAOF,IAAI,GAAGA,IAAI,CAAC+C,IAAI,GAAG,EAAE;EAChC;EACAU,GAAGA,CAACC,MAAM,EAAE;IACR,MAAMpE,KAAK,GAAGoE,MAAM,CAACC,MAAM,CAACrE,KAAK;IACjC,IAAI,CAACA,KAAK,IAAI,EAAE,EAAEsE,IAAI,CAAC,CAAC,IAAI,IAAI,CAACC,KAAK,EAAE;MACpC,IAAI,CAACA,KAAK,CAACvE,KAAK,CAAC,CACZ8D,IAAI,CAACtE,IAAI,CAAC,CAAC,CAAC,CAAC,CACb4C,SAAS,CAACoC,GAAG,IAAI;QAClB,IAAI,CAACjE,QAAQ,CAACI,GAAG,CAAC6D,GAAG,CAAC5D,EAAE,EAAAC,aAAA,KAAO2D,GAAG,CAAE,CAAC;QACrC,IAAI,CAACC,UAAU,CAACD,GAAG,CAAC5D,EAAE,CAAC;QACvB,IAAI,CAACG,gBAAgB,CAACC,QAAQ,CAAC,EAAE,CAAC;MACtC,CAAC,CAAC;IACN;EACJ;EACA0D,MAAMA,CAAC9D,EAAE,EAAE;IACP,MAAM+D,KAAK,GAAG,IAAI,CAAC7C,aAAa,CAAC8C,OAAO,CAAChE,EAAE,CAAC;IAC5C,IAAI+D,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAAC7C,aAAa,CAAC+C,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACnCG,UAAU,CAAC,MAAM;QACb,IAAI,CAAC7D,cAAc,CAACb,IAAI,CAAC,IAAI,CAAC0B,aAAa,CAAC;MAChD,CAAC,EAAE,CAAC,CAAC;MACL,IAAI,CAACiD,gBAAgB,CAACnE,EAAE,CAAC;IAC7B;EACJ;EACAoE,QAAQA,CAACjC,KAAK,EAAE;IACZ,IAAIA,KAAK,CAACkC,WAAW,EAAE;MACnB,MAAMvE,IAAI,GAAGqC,KAAK,CAACsB,MAAM,CAACrE,KAAK;MAC/B,IAAI,IAAI,CAACO,QAAQ,CAAC2E,GAAG,CAACxE,IAAI,CAACE,EAAE,CAAC,EAAE;QAC5B,IAAI,IAAI,CAACL,QAAQ,CAAC2D,GAAG,CAACxD,IAAI,CAACE,EAAE,CAAC,CAACyC,QAAQ,IAAI,IAAI,CAAC9C,QAAQ,CAAC2D,GAAG,CAACxD,IAAI,CAACE,EAAE,CAAC,CAACoE,QAAQ,EAAE;UAC5E;QACJ;MACJ,CAAC,MACI;QACD,IAAI,CAACzE,QAAQ,CAACI,GAAG,CAACD,IAAI,CAACE,EAAE,EAAAC,aAAA,KAAOH,IAAI,CAAE,CAAC;MAC3C;MACA,IAAI,CAAC+D,UAAU,CAAC/D,IAAI,CAACE,EAAE,CAAC;IAC5B;EACJ;EACAJ,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACyC,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACjD,KAAK,GAAG,EAAE;IACzB;IACA,IAAI,CAACe,gBAAgB,CAACC,QAAQ,CAAC,IAAI,CAAC;EACxC;EACAmE,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACjD,cAAc,GAAG,IAAI;EAC9B;EACAuC,UAAUA,CAAC7D,EAAE,EAAE;IACX,IAAI,CAACA,EAAE,EAAE;MACL;IACJ;IACA,IAAI,CAACwE,iBAAiB,CAACxE,EAAE,CAAC;IAC1B,IAAI,CAACkB,aAAa,CAACuD,IAAI,CAACzE,EAAE,CAAC;IAC3B,IAAI,CAACK,cAAc,CAACb,IAAI,CAAC,IAAI,CAAC0B,aAAa,CAAC;EAChD;EACAwD,YAAYA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACX,CAAAA,sBAAA,OAAI,CAAC1C,mBAAmB,cAAA0C,sBAAA,eAAxBA,sBAAA,CAA0BC,SAAS,CAAC,CAAC;EACzC;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACxC,KAAK,EAAE;MACZ,OAAO,IAAI,CAACA,KAAK,CAACyC,UAAU;IAChC;IACA,OAAO,KAAK;EAChB;EACAlC,MAAMA,CAACxD,KAAK,EAAE;IACV,OAAO,IAAI,CAACM,KAAK,CACZkD,MAAM,CAAC9C,IAAI,IAAIA,IAAI,CAAC+C,IAAI,CAACL,WAAW,CAAC,CAAC,CAACwB,OAAO,CAAC5E,KAAK,CAACoD,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAClF;EACAgC,iBAAiBA,CAACxE,EAAE,EAAE;IAClB,IAAI,IAAI,CAACL,QAAQ,CAAC2E,GAAG,CAACtE,EAAE,CAAC,EAAE;MACvB,IAAI,CAACL,QAAQ,CAAC2D,GAAG,CAACtD,EAAE,CAAC,CAACoE,QAAQ,GAAG,IAAI;IACzC;EACJ;EACAD,gBAAgBA,CAACnE,EAAE,EAAE;IACjB,IAAI,IAAI,CAACL,QAAQ,CAAC2E,GAAG,CAACtE,EAAE,CAAC,EAAE;MACvB,IAAI,CAACL,QAAQ,CAAC2D,GAAG,CAACtD,EAAE,CAAC,CAACoE,QAAQ,GAAG,KAAK;IAC1C;EACJ;EACA9E,gBAAgBA,CAACF,KAAK,EAAE;IACpB,IAAI2F,KAAK,CAACC,OAAO,CAAC5F,KAAK,CAAC,EAAE;MACtB,IAAI,CAACA,KAAK,CAACsC,MAAM,EAAE;QAAA,IAAAuD,mBAAA;QACf,CAAAA,mBAAA,OAAI,CAAC/D,aAAa,cAAA+D,mBAAA,eAAlBA,mBAAA,CAAoBpF,OAAO,CAACG,EAAE,IAAI,IAAI,CAACmE,gBAAgB,CAACnE,EAAE,CAAC,CAAC;MAChE;MACA,IAAI,CAACkB,aAAa,GAAG9B,KAAK,CAACwC,GAAG,CAAC9B,IAAI,IAAI;QACnC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC1B,OAAOA,IAAI;QACf;QACA,IAAI,CAAC,IAAI,CAACH,QAAQ,CAAC2E,GAAG,CAACxE,IAAI,CAACE,EAAE,CAAC,EAAE;UAC7B,IAAI,CAACL,QAAQ,CAACI,GAAG,CAACD,IAAI,CAACE,EAAE,EAAAC,aAAA,KAAOH,IAAI,CAAE,CAAC;QAC3C;QACA,OAAOA,IAAI,CAACE,EAAE;MAClB,CAAC,CAAC;MACF,IAAI,CAACkB,aAAa,CAACrB,OAAO,CAACG,EAAE,IAAI,IAAI,CAACwE,iBAAiB,CAACxE,EAAE,CAAC,CAAC;MAC5D,IAAI,CAACK,cAAc,CAACb,IAAI,CAAC,IAAI,CAAC0B,aAAa,CAAC;IAChD;EACJ;AAoBJ,CAAC,EAnBY/B,+BAAA,CAAK+F,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE5H;AAAa,CAAC,EACtB;EAAE4H,IAAI,EAAExH;AAAW,CAAC,EACpB;EAAEwH,IAAI,EAAEhH,SAAS;EAAEiH,UAAU,EAAE,CAAC;IAAED,IAAI,EAAErH;EAAS,CAAC,EAAE;IAAEqH,IAAI,EAAEpH;EAAK,CAAC;AAAE,CAAC,EACrE;EAAEoH,IAAI,EAAEjH,kBAAkB;EAAEkH,UAAU,EAAE,CAAC;IAAED,IAAI,EAAErH;EAAS,CAAC;AAAE,CAAC,EAC9D;EAAEqH,IAAI,EAAE9G;AAAkB,CAAC,CAC9B,EACQc,+BAAA,CAAKkG,cAAc,GAAG;EAC3BjG,KAAK,EAAE,CAAC;IAAE+F,IAAI,EAAEtH;EAAM,CAAC,CAAC;EACxBmF,QAAQ,EAAE,CAAC;IAAEmC,IAAI,EAAEtH;EAAM,CAAC,CAAC;EAC3B8F,KAAK,EAAE,CAAC;IAAEwB,IAAI,EAAEtH;EAAM,CAAC,CAAC;EACxBgE,KAAK,EAAE,CAAC;IAAEsD,IAAI,EAAEtH;EAAM,CAAC,CAAC;EACxB6B,KAAK,EAAE,CAAC;IAAEyF,IAAI,EAAEtH;EAAM,CAAC,CAAC;EACxBwE,KAAK,EAAE,CAAC;IAAE8C,IAAI,EAAEnH,SAAS;IAAEsH,IAAI,EAAE,CAACxG,QAAQ;EAAG,CAAC,CAAC;EAC/CmD,mBAAmB,EAAE,CAAC;IAAEkD,IAAI,EAAEnH,SAAS;IAAEsH,IAAI,EAAE,CAAC,OAAO,EAAE;MAAEC,IAAI,EAAEnH;IAAuB,CAAC;EAAG,CAAC,CAAC;EAC9FoH,eAAe,EAAE,CAAC;IAAEL,IAAI,EAAEnH,SAAS;IAAEsH,IAAI,EAAE,CAAC,MAAM;EAAG,CAAC,CAAC;EACvDtF,EAAE,EAAE,CAAC;IAAEmF,IAAI,EAAEvH;EAAY,CAAC,CAAC;EAC3B4C,gBAAgB,EAAE,CAAC;IAAE2E,IAAI,EAAEvH,WAAW;IAAE0H,IAAI,EAAE,CAAC,gBAAgB;EAAG,CAAC;AACvE,CAAC,EAAAnG,+BAAA,CACJ;AACDD,8BAA8B,GAAG9B,UAAU,CAAC,CACxCM,SAAS,CAAC;EACN+H,QAAQ,EAAE,6BAA6B;EACvCC,QAAQ,EAAErI,oBAAoB;EAC9BsI,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAEtH,mBAAmB;IAAEuH,WAAW,EAAE3G;EAA+B,CAAC,CAAC;EAC1F4G,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACzI,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAE4B,8BAA8B,CAAC;AAClC,SAASA,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}