{"ast": null, "code": "import { createBrowserChannel } from 'storybook/internal/channels';\nimport { STORY_HOT_UPDATED } from 'storybook/internal/core-events';\nimport { isPreview } from 'storybook/internal/csf';\nimport { global } from '@storybook/global';\nimport { PreviewWeb, addons, composeConfigs } from 'storybook/preview-api';\nimport { importFn } from './storybook-stories.js';\nconst getProjectAnnotations = () => {\n  var _previewAnnotations;\n  const previewAnnotations = [require('/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@storybook/angular/dist/client/config.mjs'), require('/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@storybook/addon-actions/dist/preview.mjs')];\n  // the last one in this array is the user preview\n  const userPreview = (_previewAnnotations = previewAnnotations[previewAnnotations.length - 1]) === null || _previewAnnotations === void 0 ? void 0 : _previewAnnotations.default;\n  if (isPreview(userPreview)) {\n    return userPreview.composed;\n  }\n  return composeConfigs(previewAnnotations);\n};\nconst channel = createBrowserChannel({\n  page: 'preview'\n});\naddons.setChannel(channel);\nif (global.CONFIG_TYPE === 'DEVELOPMENT') {\n  window.__STORYBOOK_SERVER_CHANNEL__ = channel;\n}\nconst preview = new PreviewWeb(importFn, getProjectAnnotations);\nwindow.__STORYBOOK_PREVIEW__ = preview;\nwindow.__STORYBOOK_STORY_STORE__ = preview.storyStore;\nwindow.__STORYBOOK_ADDONS_CHANNEL__ = channel;\nif (import.meta.webpackHot) {\n  import.meta.webpackHot.addStatusHandler(status => {\n    if (status === 'idle') {\n      preview.channel.emit(STORY_HOT_UPDATED);\n    }\n  });\n  import.meta.webpackHot.accept('./storybook-stories.js', () => {\n    // importFn has changed so we need to patch the new one in\n    preview.onStoriesChanged({\n      importFn\n    });\n  });\n  import.meta.webpackHot.accept(['/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@storybook/angular/dist/client/config.mjs', '/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@storybook/addon-actions/dist/preview.mjs'], () => {\n    // getProjectAnnotations has changed so we need to patch the new one in\n    preview.onGetProjectAnnotationsChanged({\n      getProjectAnnotations\n    });\n  });\n}", "map": {"version": 3, "names": ["createBrowserChannel", "STORY_HOT_UPDATED", "isPreview", "global", "PreviewWeb", "addons", "composeConfigs", "importFn", "getProjectAnnotations", "_previewAnnotations", "previewAnnotations", "require", "userPreview", "length", "default", "composed", "channel", "page", "setChannel", "CONFIG_TYPE", "window", "__STORYBOOK_SERVER_CHANNEL__", "preview", "__STORYBOOK_PREVIEW__", "__STORYBOOK_STORY_STORE__", "storyStore", "__STORYBOOK_ADDONS_CHANNEL__", "import", "meta", "webpackHot", "addStatusHandler", "status", "emit", "accept", "onStoriesChanged", "onGetProjectAnnotationsChanged"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/storybook-config-entry.js"], "sourcesContent": ["import { createBrowserChannel } from 'storybook/internal/channels';\nimport { STORY_HOT_UPDATED } from 'storybook/internal/core-events';\nimport { isPreview } from 'storybook/internal/csf';\n\nimport { global } from '@storybook/global';\n\nimport { PreviewWeb, addons, composeConfigs } from 'storybook/preview-api';\nimport { importFn } from './storybook-stories.js';\n\nconst getProjectAnnotations = () => {\n  const previewAnnotations = [require('/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@storybook/angular/dist/client/config.mjs'),require('/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@storybook/addon-actions/dist/preview.mjs')];\n  // the last one in this array is the user preview\n  const userPreview = previewAnnotations[previewAnnotations.length - 1]?.default;\n\n  if (isPreview(userPreview)) {\n    return userPreview.composed;\n  }\n\n  return composeConfigs(previewAnnotations);\n};\n\nconst channel = createBrowserChannel({ page: 'preview' });\naddons.setChannel(channel);\n\nif (global.CONFIG_TYPE === 'DEVELOPMENT') {\n  window.__STORYBOOK_SERVER_CHANNEL__ = channel;\n}\n\nconst preview = new PreviewWeb(importFn, getProjectAnnotations);\n\nwindow.__STORYBOOK_PREVIEW__ = preview;\nwindow.__STORYBOOK_STORY_STORE__ = preview.storyStore;\nwindow.__STORYBOOK_ADDONS_CHANNEL__ = channel;\n\nif (import.meta.webpackHot) {\n  import.meta.webpackHot.addStatusHandler((status) => {\n    if (status === 'idle') {\n      preview.channel.emit(STORY_HOT_UPDATED);\n    }\n  });\n\n  import.meta.webpackHot.accept('./storybook-stories.js', () => {\n    // importFn has changed so we need to patch the new one in\n    preview.onStoriesChanged({ importFn });\n  });\n\n  import.meta.webpackHot.accept(['/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@storybook/angular/dist/client/config.mjs','/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@storybook/addon-actions/dist/preview.mjs'], () => {\n    // getProjectAnnotations has changed so we need to patch the new one in\n    preview.onGetProjectAnnotationsChanged({ getProjectAnnotations });\n  });\n}\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,SAAS,QAAQ,wBAAwB;AAElD,SAASC,MAAM,QAAQ,mBAAmB;AAE1C,SAASC,UAAU,EAAEC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC1E,SAASC,QAAQ,QAAQ,wBAAwB;AAEjD,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAA,IAAAC,mBAAA;EAClC,MAAMC,kBAAkB,GAAG,CAACC,OAAO,CAAC,8GAA8G,CAAC,EAACA,OAAO,CAAC,8GAA8G,CAAC,CAAC;EAC5Q;EACA,MAAMC,WAAW,IAAAH,mBAAA,GAAGC,kBAAkB,CAACA,kBAAkB,CAACG,MAAM,GAAG,CAAC,CAAC,cAAAJ,mBAAA,uBAAjDA,mBAAA,CAAmDK,OAAO;EAE9E,IAAIZ,SAAS,CAACU,WAAW,CAAC,EAAE;IAC1B,OAAOA,WAAW,CAACG,QAAQ;EAC7B;EAEA,OAAOT,cAAc,CAACI,kBAAkB,CAAC;AAC3C,CAAC;AAED,MAAMM,OAAO,GAAGhB,oBAAoB,CAAC;EAAEiB,IAAI,EAAE;AAAU,CAAC,CAAC;AACzDZ,MAAM,CAACa,UAAU,CAACF,OAAO,CAAC;AAE1B,IAAIb,MAAM,CAACgB,WAAW,KAAK,aAAa,EAAE;EACxCC,MAAM,CAACC,4BAA4B,GAAGL,OAAO;AAC/C;AAEA,MAAMM,OAAO,GAAG,IAAIlB,UAAU,CAACG,QAAQ,EAAEC,qBAAqB,CAAC;AAE/DY,MAAM,CAACG,qBAAqB,GAAGD,OAAO;AACtCF,MAAM,CAACI,yBAAyB,GAAGF,OAAO,CAACG,UAAU;AACrDL,MAAM,CAACM,4BAA4B,GAAGV,OAAO;AAE7C,IAAIW,MAAM,CAACC,IAAI,CAACC,UAAU,EAAE;EAC1BF,MAAM,CAACC,IAAI,CAACC,UAAU,CAACC,gBAAgB,CAAEC,MAAM,IAAK;IAClD,IAAIA,MAAM,KAAK,MAAM,EAAE;MACrBT,OAAO,CAACN,OAAO,CAACgB,IAAI,CAAC/B,iBAAiB,CAAC;IACzC;EACF,CAAC,CAAC;EAEF0B,MAAM,CAACC,IAAI,CAACC,UAAU,CAACI,MAAM,CAAC,wBAAwB,EAAE,MAAM;IAC5D;IACAX,OAAO,CAACY,gBAAgB,CAAC;MAAE3B;IAAS,CAAC,CAAC;EACxC,CAAC,CAAC;EAEFoB,MAAM,CAACC,IAAI,CAACC,UAAU,CAACI,MAAM,CAAC,CAAC,8GAA8G,EAAC,8GAA8G,CAAC,EAAE,MAAM;IACnQ;IACAX,OAAO,CAACa,8BAA8B,CAAC;MAAE3B;IAAsB,CAAC,CAAC;EACnE,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}