{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiSidebarComponent } from './swui-sidebar.component';\nimport { SwuiMenuModule } from '../swui-menu/swui-menu.module';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\nlet SwuiSidebarModule = class SwuiSidebarModule {};\nSwuiSidebarModule = __decorate([NgModule({\n  declarations: [SwuiSidebarComponent],\n  imports: [CommonModule, MatIconModule, MatRippleModule, SwuiMenuModule],\n  exports: [SwuiSidebarComponent]\n})], SwuiSidebarModule);\nexport { SwuiSidebarModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "SwuiSidebarComponent", "SwuiMenuModule", "MatIconModule", "MatRippleModule", "SwuiSidebarModule", "__decorate", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-sidebar/swui-sidebar.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { SwuiSidebarComponent } from './swui-sidebar.component';\nimport { SwuiMenuModule } from '../swui-menu/swui-menu.module';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\n\n\n@NgModule({\n  declarations: [SwuiSidebarComponent],\n  imports: [\n    CommonModule,\n    MatIconModule,\n    MatRippleModule,\n    SwuiMenuModule,\n  ],\n  exports: [SwuiSidebarComponent],\n})\nexport class SwuiSidebarModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AAajD,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB,GAAI;AAArBA,iBAAiB,GAAAC,UAAA,EAV7BP,QAAQ,CAAC;EACRQ,YAAY,EAAE,CAACN,oBAAoB,CAAC;EACpCO,OAAO,EAAE,CACPR,YAAY,EACZG,aAAa,EACbC,eAAe,EACfF,cAAc,CACf;EACDO,OAAO,EAAE,CAACR,oBAAoB;CAC/B,CAAC,C,EACWI,iBAAiB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}