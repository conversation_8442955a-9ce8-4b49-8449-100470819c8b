{"ast": null, "code": "import { __asyncGenerator, __await, __generator } from \"tslib\";\nimport { isFunction } from './isFunction';\nexport function readableStreamLikeToAsyncGenerator(readableStream) {\n  return __asyncGenerator(this, arguments, function readableStreamLikeToAsyncGenerator_1() {\n    var reader, _a, value, done;\n    return __generator(this, function (_b) {\n      switch (_b.label) {\n        case 0:\n          reader = readableStream.getReader();\n          _b.label = 1;\n        case 1:\n          _b.trys.push([1,, 9, 10]);\n          _b.label = 2;\n        case 2:\n          if (!true) return [3, 8];\n          return [4, __await(reader.read())];\n        case 3:\n          _a = _b.sent(), value = _a.value, done = _a.done;\n          if (!done) return [3, 5];\n          return [4, __await(void 0)];\n        case 4:\n          return [2, _b.sent()];\n        case 5:\n          return [4, __await(value)];\n        case 6:\n          return [4, _b.sent()];\n        case 7:\n          _b.sent();\n          return [3, 2];\n        case 8:\n          return [3, 10];\n        case 9:\n          reader.releaseLock();\n          return [7];\n        case 10:\n          return [2];\n      }\n    });\n  });\n}\nexport function isReadableStreamLike(obj) {\n  return isFunction(obj === null || obj === void 0 ? void 0 : obj.getReader);\n}\n//# sourceMappingURL=isReadableStreamLike.js.map", "map": {"version": 3, "names": ["__asyncGenerator", "__await", "__generator", "isFunction", "readableStreamLikeToAsyncGenerator", "readableStream", "arguments", "readableStreamLikeToAsyncGenerator_1", "reader", "_a", "value", "done", "_b", "label", "<PERSON><PERSON><PERSON><PERSON>", "trys", "push", "read", "sent", "releaseLock", "isReadableStreamLike", "obj"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/isReadableStreamLike.js"], "sourcesContent": ["import { __asyncGenerator, __await, __generator } from \"tslib\";\nimport { isFunction } from './isFunction';\nexport function readableStreamLikeToAsyncGenerator(readableStream) {\n    return __asyncGenerator(this, arguments, function readableStreamLikeToAsyncGenerator_1() {\n        var reader, _a, value, done;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    reader = readableStream.getReader();\n                    _b.label = 1;\n                case 1:\n                    _b.trys.push([1, , 9, 10]);\n                    _b.label = 2;\n                case 2:\n                    if (!true) return [3, 8];\n                    return [4, __await(reader.read())];\n                case 3:\n                    _a = _b.sent(), value = _a.value, done = _a.done;\n                    if (!done) return [3, 5];\n                    return [4, __await(void 0)];\n                case 4: return [2, _b.sent()];\n                case 5: return [4, __await(value)];\n                case 6: return [4, _b.sent()];\n                case 7:\n                    _b.sent();\n                    return [3, 2];\n                case 8: return [3, 10];\n                case 9:\n                    reader.releaseLock();\n                    return [7];\n                case 10: return [2];\n            }\n        });\n    });\n}\nexport function isReadableStreamLike(obj) {\n    return isFunction(obj === null || obj === void 0 ? void 0 : obj.getReader);\n}\n//# sourceMappingURL=isReadableStreamLike.js.map"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC9D,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAO,SAASC,kCAAkCA,CAACC,cAAc,EAAE;EAC/D,OAAOL,gBAAgB,CAAC,IAAI,EAAEM,SAAS,EAAE,SAASC,oCAAoCA,CAAA,EAAG;IACrF,IAAIC,MAAM,EAAEC,EAAE,EAAEC,KAAK,EAAEC,IAAI;IAC3B,OAAOT,WAAW,CAAC,IAAI,EAAE,UAAUU,EAAE,EAAE;MACnC,QAAQA,EAAE,CAACC,KAAK;QACZ,KAAK,CAAC;UACFL,MAAM,GAAGH,cAAc,CAACS,SAAS,CAAC,CAAC;UACnCF,EAAE,CAACC,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UACFD,EAAE,CAACG,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAI,CAAC,EAAE,EAAE,CAAC,CAAC;UAC1BJ,EAAE,CAACC,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UACF,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;UACxB,OAAO,CAAC,CAAC,EAAEZ,OAAO,CAACO,MAAM,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,CAAC;UACFR,EAAE,GAAGG,EAAE,CAACM,IAAI,CAAC,CAAC,EAAER,KAAK,GAAGD,EAAE,CAACC,KAAK,EAAEC,IAAI,GAAGF,EAAE,CAACE,IAAI;UAChD,IAAI,CAACA,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;UACxB,OAAO,CAAC,CAAC,EAAEV,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/B,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAEW,EAAE,CAACM,IAAI,CAAC,CAAC,CAAC;QAC7B,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAEjB,OAAO,CAACS,KAAK,CAAC,CAAC;QAClC,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAEE,EAAE,CAACM,IAAI,CAAC,CAAC,CAAC;QAC7B,KAAK,CAAC;UACFN,EAAE,CAACM,IAAI,CAAC,CAAC;UACT,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC;QACtB,KAAK,CAAC;UACFV,MAAM,CAACW,WAAW,CAAC,CAAC;UACpB,OAAO,CAAC,CAAC,CAAC;QACd,KAAK,EAAE;UAAE,OAAO,CAAC,CAAC,CAAC;MACvB;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,OAAO,SAASC,oBAAoBA,CAACC,GAAG,EAAE;EACtC,OAAOlB,UAAU,CAACkB,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACP,SAAS,CAAC;AAC9E;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}