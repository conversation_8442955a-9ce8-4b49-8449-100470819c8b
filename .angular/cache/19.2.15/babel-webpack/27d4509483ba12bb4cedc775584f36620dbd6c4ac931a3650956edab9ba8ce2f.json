{"ast": null, "code": "import { SwuiTdClickWidgetComponent } from '../td-widget/click/click.widget';\nimport { SwuiTdStringWidgetComponent } from '../td-widget/string/string.widget';\nimport { SwuiTdTimestampWidgetComponent } from '../td-widget/timestamp/timestamp.widget';\nimport { SwuiTdUserWidgetComponent } from '../td-widget/user/user.widget';\nimport { SwuiTdBooleanWidgetComponent } from '../td-widget/boolean/boolean.widget';\nimport { SwuiTdGameLabelsWidgetComponent } from '../td-widget/game-labels/game-labels.widget';\nimport { SwuiTdGamesLabelsWidgetComponent } from '../td-widget/games-labels/games-labels.widget';\nimport { SwuiTdColorfulLabelsWidgetComponent } from '../td-widget/colorful-labels/colorful-labels.widget';\nimport { SwuiTdJackpotWidgetComponent } from '../td-widget/jackpot/jackpot.widget';\nimport { SwuiTdPercentWidgetComponent } from '../td-widget/percent/percent.widget';\nimport { SwuiTdCalcWidgetComponent } from '../td-widget/calc/calc.widget';\nimport { SwuiTdLinkWidgetComponent } from '../td-widget/link/link.widget';\nimport { SwuiTdCalcAsyncWidgetComponent } from '../td-widget/calc-async/calc-async.widget';\nimport { SwuiTdIconPopoverWidgetComponent } from '../td-widget/icon-popover/icon-popover.widget';\nimport { SwuiTdCurrencyWidgetComponent } from '../td-widget/currency/currency.widget';\nimport { SwuiTdIconWidgetComponent } from '../td-widget/icon/icon.widget';\nimport { SwuiFooterStringWidgetComponent } from '../footer-widget/string/string.widget';\nimport { SwuiFooterTotalWidgetComponent } from '../footer-widget/total/total.widget';\nimport { SwuiTdPercentEditableWidgetComponent } from '../td-widget/percent-editable/percent-editable.widget';\nimport { SwuiTdStatusWidgetComponent } from '../td-widget/status/status.widget';\nimport { SwuiTdInactivityWidgetComponent } from '../td-widget/inactivity/inactivity.widget';\nimport { SwuiTdListWidgetComponent } from '../td-widget/list/list.widget';\nimport { SwuiTdImageWidgetComponent } from '../td-widget/image/image.widget';\nexport function getDefaultWidgetsList() {\n  return [{\n    type: 'tdstring',\n    component: SwuiTdStringWidgetComponent\n  }, {\n    type: 'tdnumber',\n    component: SwuiTdStringWidgetComponent\n  }, {\n    type: 'tddate',\n    component: SwuiTdTimestampWidgetComponent\n  }, {\n    type: 'tdrange',\n    component: SwuiTdStringWidgetComponent\n  }, {\n    type: 'tdmultiselect',\n    component: SwuiTdStringWidgetComponent\n  }, {\n    type: 'tdchoice',\n    component: SwuiTdStringWidgetComponent\n  }, {\n    type: 'tdstatus',\n    component: SwuiTdStatusWidgetComponent\n  }, {\n    type: 'tddatetimerange',\n    component: SwuiTdTimestampWidgetComponent\n  }, {\n    type: 'tdarray',\n    component: SwuiTdStringWidgetComponent\n  }, {\n    type: 'tduser',\n    component: SwuiTdUserWidgetComponent\n  }, {\n    type: 'tdboolean',\n    component: SwuiTdBooleanWidgetComponent\n  }, {\n    type: 'tdgamelabels',\n    component: SwuiTdGameLabelsWidgetComponent\n  }, {\n    type: 'tdgameslabels',\n    component: SwuiTdGamesLabelsWidgetComponent\n  }, {\n    type: 'tdcolorfullabels',\n    component: SwuiTdColorfulLabelsWidgetComponent\n  }, {\n    type: 'tdjackpot',\n    component: SwuiTdJackpotWidgetComponent\n  }, {\n    type: 'tdtimestamp',\n    component: SwuiTdTimestampWidgetComponent\n  }, {\n    type: 'tdpercent',\n    component: SwuiTdPercentWidgetComponent\n  }, {\n    type: 'tdpercenteditable',\n    component: SwuiTdPercentEditableWidgetComponent\n  }, {\n    type: 'tdcalc',\n    component: SwuiTdCalcWidgetComponent\n  }, {\n    type: 'tdlink',\n    component: SwuiTdLinkWidgetComponent\n  }, {\n    type: 'tdclick',\n    component: SwuiTdClickWidgetComponent\n  }, {\n    type: 'tdcalcasync',\n    component: SwuiTdCalcAsyncWidgetComponent\n  }, {\n    type: 'tdiconpopover',\n    component: SwuiTdIconPopoverWidgetComponent\n  }, {\n    type: 'tdcurrency',\n    component: SwuiTdCurrencyWidgetComponent\n  }, {\n    type: 'tdicon',\n    component: SwuiTdIconWidgetComponent\n  }, {\n    type: 'tdinactivity',\n    component: SwuiTdInactivityWidgetComponent\n  }, {\n    type: 'tdlist',\n    component: SwuiTdListWidgetComponent\n  }, {\n    type: 'tdimage',\n    component: SwuiTdImageWidgetComponent\n  }, {\n    type: 'footerstring',\n    component: SwuiFooterStringWidgetComponent\n  }, {\n    type: 'footertotal',\n    component: SwuiFooterTotalWidgetComponent\n  }];\n}", "map": {"version": 3, "names": ["SwuiTdClickWidgetComponent", "SwuiTdStringWidgetComponent", "SwuiTdTimestampWidgetComponent", "SwuiTdUserWidgetComponent", "SwuiTdBooleanWidgetComponent", "SwuiTdGameLabelsWidgetComponent", "SwuiTdGamesLabelsWidgetComponent", "SwuiTdColorfulLabelsWidgetComponent", "SwuiTdJackpotWidgetComponent", "SwuiTdPercentWidgetComponent", "SwuiTdCalcWidgetComponent", "SwuiTdLinkWidgetComponent", "SwuiTdCalcAsyncWidgetComponent", "SwuiTdIconPopoverWidgetComponent", "SwuiTdCurrencyWidgetComponent", "SwuiTdIconWidgetComponent", "SwuiFooterStringWidgetComponent", "SwuiFooterTotalWidgetComponent", "SwuiTdPercentEditableWidgetComponent", "SwuiTdStatusWidgetComponent", "SwuiTdInactivityWidgetComponent", "SwuiTdListWidgetComponent", "SwuiTdImageWidgetComponent", "getDefaultWidgetsList", "type", "component"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/registry/default-list.ts"], "sourcesContent": ["import { SwuiTdClickWidgetComponent } from '../td-widget/click/click.widget';\nimport { SwuiTdStringWidgetComponent } from '../td-widget/string/string.widget';\nimport { SwuiTdTimestampWidgetComponent } from '../td-widget/timestamp/timestamp.widget';\nimport { SwuiTdUserWidgetComponent } from '../td-widget/user/user.widget';\nimport { SwuiTdBooleanWidgetComponent } from '../td-widget/boolean/boolean.widget';\nimport { SwuiTdGameLabelsWidgetComponent } from '../td-widget/game-labels/game-labels.widget';\nimport { SwuiTdGamesLabelsWidgetComponent } from '../td-widget/games-labels/games-labels.widget';\nimport { SwuiTdColorfulLabelsWidgetComponent } from '../td-widget/colorful-labels/colorful-labels.widget';\nimport { SwuiTdJackpotWidgetComponent } from '../td-widget/jackpot/jackpot.widget';\nimport { SwuiTdPercentWidgetComponent } from '../td-widget/percent/percent.widget';\nimport { SwuiTdCalcWidgetComponent } from '../td-widget/calc/calc.widget';\nimport { SwuiTdLinkWidgetComponent } from '../td-widget/link/link.widget';\nimport { SwuiTdCalcAsyncWidgetComponent } from '../td-widget/calc-async/calc-async.widget';\nimport { SwuiTdIconPopoverWidgetComponent } from '../td-widget/icon-popover/icon-popover.widget';\nimport { SwuiTdCurrencyWidgetComponent } from '../td-widget/currency/currency.widget';\nimport { SwuiTdIconWidgetComponent } from '../td-widget/icon/icon.widget';\nimport { SwuiFooterStringWidgetComponent } from '../footer-widget/string/string.widget';\nimport { SwuiFooterTotalWidgetComponent } from '../footer-widget/total/total.widget';\nimport { SwuiTdPercentEditableWidgetComponent } from '../td-widget/percent-editable/percent-editable.widget';\nimport { SwuiTdStatusWidgetComponent } from '../td-widget/status/status.widget';\nimport { SwuiTdInactivityWidgetComponent } from '../td-widget/inactivity/inactivity.widget';\nimport { SwuiTdListWidgetComponent } from '../td-widget/list/list.widget';\nimport { SwuiTdImageWidgetComponent } from '../td-widget/image/image.widget';\nexport function getDefaultWidgetsList() {\n    return [\n        { type: 'tdstring', component: SwuiTdStringWidgetComponent },\n        { type: 'tdnumber', component: SwuiTdStringWidgetComponent },\n        { type: 'tddate', component: SwuiTdTimestampWidgetComponent },\n        { type: 'tdrange', component: SwuiTdStringWidgetComponent },\n        { type: 'tdmultiselect', component: SwuiTdStringWidgetComponent },\n        { type: 'tdchoice', component: SwuiTdStringWidgetComponent },\n        { type: 'tdstatus', component: SwuiTdStatusWidgetComponent },\n        { type: 'tddatetimerange', component: SwuiTdTimestampWidgetComponent },\n        { type: 'tdarray', component: SwuiTdStringWidgetComponent },\n        { type: 'tduser', component: SwuiTdUserWidgetComponent },\n        { type: 'tdboolean', component: SwuiTdBooleanWidgetComponent },\n        { type: 'tdgamelabels', component: SwuiTdGameLabelsWidgetComponent },\n        { type: 'tdgameslabels', component: SwuiTdGamesLabelsWidgetComponent },\n        { type: 'tdcolorfullabels', component: SwuiTdColorfulLabelsWidgetComponent },\n        { type: 'tdjackpot', component: SwuiTdJackpotWidgetComponent },\n        { type: 'tdtimestamp', component: SwuiTdTimestampWidgetComponent },\n        { type: 'tdpercent', component: SwuiTdPercentWidgetComponent },\n        { type: 'tdpercenteditable', component: SwuiTdPercentEditableWidgetComponent },\n        { type: 'tdcalc', component: SwuiTdCalcWidgetComponent },\n        { type: 'tdlink', component: SwuiTdLinkWidgetComponent },\n        { type: 'tdclick', component: SwuiTdClickWidgetComponent },\n        { type: 'tdcalcasync', component: SwuiTdCalcAsyncWidgetComponent },\n        { type: 'tdiconpopover', component: SwuiTdIconPopoverWidgetComponent },\n        { type: 'tdcurrency', component: SwuiTdCurrencyWidgetComponent },\n        { type: 'tdicon', component: SwuiTdIconWidgetComponent },\n        { type: 'tdinactivity', component: SwuiTdInactivityWidgetComponent },\n        { type: 'tdlist', component: SwuiTdListWidgetComponent },\n        { type: 'tdimage', component: SwuiTdImageWidgetComponent },\n        { type: 'footerstring', component: SwuiFooterStringWidgetComponent },\n        { type: 'footertotal', component: SwuiFooterTotalWidgetComponent },\n    ];\n}\n"], "mappings": "AAAA,SAASA,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,2BAA2B,QAAQ,mCAAmC;AAC/E,SAASC,8BAA8B,QAAQ,yCAAyC;AACxF,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,4BAA4B,QAAQ,qCAAqC;AAClF,SAASC,+BAA+B,QAAQ,6CAA6C;AAC7F,SAASC,gCAAgC,QAAQ,+CAA+C;AAChG,SAASC,mCAAmC,QAAQ,qDAAqD;AACzG,SAASC,4BAA4B,QAAQ,qCAAqC;AAClF,SAASC,4BAA4B,QAAQ,qCAAqC;AAClF,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,8BAA8B,QAAQ,2CAA2C;AAC1F,SAASC,gCAAgC,QAAQ,+CAA+C;AAChG,SAASC,6BAA6B,QAAQ,uCAAuC;AACrF,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,+BAA+B,QAAQ,uCAAuC;AACvF,SAASC,8BAA8B,QAAQ,qCAAqC;AACpF,SAASC,oCAAoC,QAAQ,uDAAuD;AAC5G,SAASC,2BAA2B,QAAQ,mCAAmC;AAC/E,SAASC,+BAA+B,QAAQ,2CAA2C;AAC3F,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,OAAO,SAASC,qBAAqBA,CAAA,EAAG;EACpC,OAAO,CACH;IAAEC,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAExB;EAA4B,CAAC,EAC5D;IAAEuB,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAExB;EAA4B,CAAC,EAC5D;IAAEuB,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEvB;EAA+B,CAAC,EAC7D;IAAEsB,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAExB;EAA4B,CAAC,EAC3D;IAAEuB,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAExB;EAA4B,CAAC,EACjE;IAAEuB,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAExB;EAA4B,CAAC,EAC5D;IAAEuB,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEN;EAA4B,CAAC,EAC5D;IAAEK,IAAI,EAAE,iBAAiB;IAAEC,SAAS,EAAEvB;EAA+B,CAAC,EACtE;IAAEsB,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAExB;EAA4B,CAAC,EAC3D;IAAEuB,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEtB;EAA0B,CAAC,EACxD;IAAEqB,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAErB;EAA6B,CAAC,EAC9D;IAAEoB,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEpB;EAAgC,CAAC,EACpE;IAAEmB,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEnB;EAAiC,CAAC,EACtE;IAAEkB,IAAI,EAAE,kBAAkB;IAAEC,SAAS,EAAElB;EAAoC,CAAC,EAC5E;IAAEiB,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEjB;EAA6B,CAAC,EAC9D;IAAEgB,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEvB;EAA+B,CAAC,EAClE;IAAEsB,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEhB;EAA6B,CAAC,EAC9D;IAAEe,IAAI,EAAE,mBAAmB;IAAEC,SAAS,EAAEP;EAAqC,CAAC,EAC9E;IAAEM,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEf;EAA0B,CAAC,EACxD;IAAEc,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEd;EAA0B,CAAC,EACxD;IAAEa,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEzB;EAA2B,CAAC,EAC1D;IAAEwB,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEb;EAA+B,CAAC,EAClE;IAAEY,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEZ;EAAiC,CAAC,EACtE;IAAEW,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEX;EAA8B,CAAC,EAChE;IAAEU,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEV;EAA0B,CAAC,EACxD;IAAES,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEL;EAAgC,CAAC,EACpE;IAAEI,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEJ;EAA0B,CAAC,EACxD;IAAEG,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEH;EAA2B,CAAC,EAC1D;IAAEE,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAET;EAAgC,CAAC,EACpE;IAAEQ,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAER;EAA+B,CAAC,CACrE;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}