{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _VisuallyHiddenLoader2;\nexport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load the .cdk-visually-hidden styles.\n * @docs-private\n */\nclass _VisuallyHiddenLoader {}\n_VisuallyHiddenLoader2 = _VisuallyHiddenLoader;\n_defineProperty(_VisuallyHiddenLoader, \"\\u0275fac\", function _VisuallyHiddenLoader2_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _VisuallyHiddenLoader2)();\n});\n_defineProperty(_VisuallyHiddenLoader, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _VisuallyHiddenLoader2,\n  selectors: [[\"ng-component\"]],\n  exportAs: [\"cdkVisuallyHidden\"],\n  decls: 0,\n  vars: 0,\n  template: function _VisuallyHiddenLoader2_Template(rf, ctx) {},\n  styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_VisuallyHiddenLoader, [{\n    type: Component,\n    args: [{\n      exportAs: 'cdkVisuallyHidden',\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { _VisuallyHiddenLoader };\n//# sourceMappingURL=private.mjs.map", "map": {"version": 3, "names": ["_", "_CdkPrivateStyleLoader", "i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "_VisuallyHiddenLoader", "_VisuallyHiddenLoader2", "_defineProperty", "_VisuallyHiddenLoader2_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "exportAs", "decls", "vars", "template", "_VisuallyHiddenLoader2_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/private.mjs"], "sourcesContent": ["export { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load the .cdk-visually-hidden styles.\n * @docs-private\n */\nclass _VisuallyHiddenLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _VisuallyHiddenLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: _VisuallyHiddenLoader, isStandalone: true, selector: \"ng-component\", exportAs: [\"cdkVisuallyHidden\"], ngImport: i0, template: '', isInline: true, styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _VisuallyHiddenLoader, decorators: [{\n            type: Component,\n            args: [{ exportAs: 'cdkVisuallyHidden', encapsulation: ViewEncapsulation.None, template: '', changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"] }]\n        }] });\n\nexport { _VisuallyHiddenLoader };\n//# sourceMappingURL=private.mjs.map\n"], "mappings": ";;AAAA,SAASA,CAAC,IAAIC,sBAAsB,QAAQ,6BAA6B;AACzE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,QAAQ,eAAe;;AAErF;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;AAG3BC,sBAAA,GAHKD,qBAAqB;AAAAE,eAAA,CAArBF,qBAAqB,wBAAAG,+BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAC4EJ,sBAAqB;AAAA;AAAAE,eAAA,CADtHF,qBAAqB,8BAIsDJ,EAAE,CAAAS,iBAAA;EAAAC,IAAA,EAFQN,sBAAqB;EAAAO,SAAA;EAAAC,QAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;EAAAC,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAEhH;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFtB,EAAE,CAAAuB,iBAAA,CAAQnB,qBAAqB,EAAc,CAAC;IACnHM,IAAI,EAAET,SAAS;IACfuB,IAAI,EAAE,CAAC;MAAEZ,QAAQ,EAAE,mBAAmB;MAAEQ,aAAa,EAAElB,iBAAiB,CAACuB,IAAI;MAAEV,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAElB,uBAAuB,CAACuB,MAAM;MAAEP,MAAM,EAAE,CAAC,oQAAoQ;IAAE,CAAC;EACla,CAAC,CAAC;AAAA;AAEV,SAASf,qBAAqB;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}