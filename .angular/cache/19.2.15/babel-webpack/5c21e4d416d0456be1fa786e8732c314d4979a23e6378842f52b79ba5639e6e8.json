{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiTimeDurationComponent } from './swui-time-duration.component';\nimport { MatInputModule } from '@angular/material/input';\nlet SwuiTimeDurationModule = class SwuiTimeDurationModule {};\nSwuiTimeDurationModule = __decorate([NgModule({\n  declarations: [SwuiTimeDurationComponent],\n  imports: [CommonModule, ReactiveFormsModule, MatInputModule],\n  exports: [SwuiTimeDurationComponent]\n})], SwuiTimeDurationModule);\nexport { SwuiTimeDurationModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "ReactiveFormsModule", "SwuiTimeDurationComponent", "MatInputModule", "SwuiTimeDurationModule", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-time-duration/swui-time-duration.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiTimeDurationComponent } from './swui-time-duration.component';\nimport { MatInputModule } from '@angular/material/input';\nlet SwuiTimeDurationModule = class SwuiTimeDurationModule {\n};\nSwuiTimeDurationModule = __decorate([\n    NgModule({\n        declarations: [SwuiTimeDurationComponent],\n        imports: [\n            CommonModule,\n            ReactiveFormsModule,\n            MatInputModule,\n        ],\n        exports: [\n            SwuiTimeDurationComponent,\n        ]\n    })\n], SwuiTimeDurationModule);\nexport { SwuiTimeDurationModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,IAAIC,sBAAsB,GAAG,MAAMA,sBAAsB,CAAC,EACzD;AACDA,sBAAsB,GAAGN,UAAU,CAAC,CAChCC,QAAQ,CAAC;EACLM,YAAY,EAAE,CAACH,yBAAyB,CAAC;EACzCI,OAAO,EAAE,CACLN,YAAY,EACZC,mBAAmB,EACnBE,cAAc,CACjB;EACDI,OAAO,EAAE,CACLL,yBAAyB;AAEjC,CAAC,CAAC,CACL,EAAEE,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}