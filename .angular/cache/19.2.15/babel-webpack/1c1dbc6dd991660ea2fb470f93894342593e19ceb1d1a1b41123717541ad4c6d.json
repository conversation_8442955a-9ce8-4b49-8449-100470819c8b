{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { isEqual, cloneDeep } from 'lodash';\nimport { Observable, ReplaySubject, Subject } from 'rxjs';\nlet SwuiTopFilterDataService = class SwuiTopFilterDataService {\n  constructor() {\n    this.onReset = new Subject();\n    this._displayedFilterState = new Subject();\n    this._appliedFilterState = new ReplaySubject(1);\n    this._filterFormState = new ReplaySubject(1);\n    this.appliedFilterValue = {};\n    this.patched = false;\n  }\n  get displayedFilter() {\n    return this._displayedFilterState.asObservable();\n  }\n  get appliedFilter() {\n    return this._appliedFilterState.asObservable();\n  }\n  get filterFormState() {\n    return this._filterFormState.asObservable();\n  }\n  setFormState(value) {\n    this._filterFormState.next(value);\n  }\n  setDisplayFilter(value) {\n    this._displayedFilterState.next(value);\n  }\n  patchFilter(value) {\n    const needUpdate = Object.keys(value).every(key => this.appliedFilterValue.hasOwnProperty(key));\n    if (!needUpdate) {\n      return;\n    }\n    const newFilter = _objectSpread(_objectSpread({}, this.appliedFilterValue), value);\n    if (!isEqual(newFilter, this.appliedFilterValue)) {\n      this.submitFilter(newFilter);\n      this.patched = true;\n    }\n  }\n  /**\n   * Updates appliedFilter to remove values that don't exist in the provided schema\n   */\n  updateFilter(schema) {\n    const updatedFilter = cloneDeep(this.appliedFilterValue);\n    for (const [field, value] of Object.entries(updatedFilter)) {\n      const fieldSchema = schema.find(item => item.field === field);\n      let data;\n      if ((fieldSchema === null || fieldSchema === void 0 ? void 0 : fieldSchema.type) === 'select-table') {\n        data = fieldSchema.data;\n      } else if ((fieldSchema === null || fieldSchema === void 0 ? void 0 : fieldSchema.type) === 'multiselect') {\n        data = fieldSchema.data;\n      } else if ((fieldSchema === null || fieldSchema === void 0 ? void 0 : fieldSchema.type) === 'select') {\n        data = fieldSchema.data;\n      }\n      if (!data) {\n        continue;\n      }\n      if (data instanceof Observable) {\n        updatedFilter[field] = [];\n      } else {\n        const options = data;\n        if (Array.isArray(value)) {\n          // \"multiselect\"\n          updatedFilter[field] = value.filter(item => options.some(option => item.id === option.id));\n        } else if (typeof value === 'object' && value !== null) {\n          // \"select-table\"\n          if (!options.some(option => value.id === option.id)) {\n            delete updatedFilter[field];\n          }\n        } else {\n          // \"select\"\n          if (!options.some(option => value === option.id)) {\n            delete updatedFilter[field];\n          }\n        }\n      }\n    }\n    if (!isEqual(updatedFilter, this.appliedFilterValue)) {\n      this.submitFilter(updatedFilter);\n    }\n  }\n  submitFilter(value, checkPatched) {\n    if (!this.patched || !checkPatched) {\n      this.appliedFilterValue = value;\n      this.setDisplayFilter(value);\n      this._appliedFilterState.next(value);\n    }\n    this.patched = false;\n  }\n  resetFilter() {\n    this.onReset.next(undefined);\n    this.submitFilter({});\n  }\n};\nSwuiTopFilterDataService = __decorate([Injectable()], SwuiTopFilterDataService);\nexport { SwuiTopFilterDataService };", "map": {"version": 3, "names": ["__decorate", "Injectable", "isEqual", "cloneDeep", "Observable", "ReplaySubject", "Subject", "SwuiTopFilterDataService", "constructor", "onReset", "_displayedFilterState", "_appliedFilterState", "_filterFormState", "appliedFilterValue", "patched", "displayedFilter", "asObservable", "appliedFilter", "filterFormState", "setFormState", "value", "next", "setDisplayFilter", "patchFilter", "needUpdate", "Object", "keys", "every", "key", "hasOwnProperty", "newFilter", "_objectSpread", "submitFilter", "updateFilter", "schema", "updatedFilter", "field", "entries", "fieldSchema", "find", "item", "data", "type", "options", "Array", "isArray", "filter", "some", "option", "id", "checkPatched", "resetFilter", "undefined"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-top-filter/top-filter-data.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { isEqual, cloneDeep } from 'lodash';\nimport { Observable, ReplaySubject, Subject } from 'rxjs';\nlet SwuiTopFilterDataService = class SwuiTopFilterDataService {\n    constructor() {\n        this.onReset = new Subject();\n        this._displayedFilterState = new Subject();\n        this._appliedFilterState = new ReplaySubject(1);\n        this._filterFormState = new ReplaySubject(1);\n        this.appliedFilterValue = {};\n        this.patched = false;\n    }\n    get displayedFilter() {\n        return this._displayedFilterState.asObservable();\n    }\n    get appliedFilter() {\n        return this._appliedFilterState.asObservable();\n    }\n    get filterFormState() {\n        return this._filterFormState.asObservable();\n    }\n    setFormState(value) {\n        this._filterFormState.next(value);\n    }\n    setDisplayFilter(value) {\n        this._displayedFilterState.next(value);\n    }\n    patchFilter(value) {\n        const needUpdate = Object.keys(value).every(key => this.appliedFilterValue.hasOwnProperty(key));\n        if (!needUpdate) {\n            return;\n        }\n        const newFilter = { ...this.appliedFilterValue, ...value };\n        if (!isEqual(newFilter, this.appliedFilterValue)) {\n            this.submitFilter(newFilter);\n            this.patched = true;\n        }\n    }\n    /**\n     * Updates appliedFilter to remove values that don't exist in the provided schema\n     */\n    updateFilter(schema) {\n        const updatedFilter = cloneDeep(this.appliedFilterValue);\n        for (const [field, value] of Object.entries(updatedFilter)) {\n            const fieldSchema = schema.find(item => item.field === field);\n            let data;\n            if (fieldSchema?.type === 'select-table') {\n                data = fieldSchema.data;\n            }\n            else if (fieldSchema?.type === 'multiselect') {\n                data = fieldSchema.data;\n            }\n            else if (fieldSchema?.type === 'select') {\n                data = fieldSchema.data;\n            }\n            if (!data) {\n                continue;\n            }\n            if (data instanceof Observable) {\n                updatedFilter[field] = [];\n            }\n            else {\n                const options = data;\n                if (Array.isArray(value)) {\n                    // \"multiselect\"\n                    updatedFilter[field] = value.filter(item => options.some(option => item.id === option.id));\n                }\n                else if (typeof value === 'object' && value !== null) {\n                    // \"select-table\"\n                    if (!options.some(option => value.id === option.id)) {\n                        delete updatedFilter[field];\n                    }\n                }\n                else {\n                    // \"select\"\n                    if (!options.some(option => value === option.id)) {\n                        delete updatedFilter[field];\n                    }\n                }\n            }\n        }\n        if (!isEqual(updatedFilter, this.appliedFilterValue)) {\n            this.submitFilter(updatedFilter);\n        }\n    }\n    submitFilter(value, checkPatched) {\n        if (!this.patched || !checkPatched) {\n            this.appliedFilterValue = value;\n            this.setDisplayFilter(value);\n            this._appliedFilterState.next(value);\n        }\n        this.patched = false;\n    }\n    resetFilter() {\n        this.onReset.next(undefined);\n        this.submitFilter({});\n    }\n};\nSwuiTopFilterDataService = __decorate([\n    Injectable()\n], SwuiTopFilterDataService);\nexport { SwuiTopFilterDataService };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,OAAO,EAAEC,SAAS,QAAQ,QAAQ;AAC3C,SAASC,UAAU,EAAEC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AACzD,IAAIC,wBAAwB,GAAG,MAAMA,wBAAwB,CAAC;EAC1DC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,IAAIH,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACI,qBAAqB,GAAG,IAAIJ,OAAO,CAAC,CAAC;IAC1C,IAAI,CAACK,mBAAmB,GAAG,IAAIN,aAAa,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACO,gBAAgB,GAAG,IAAIP,aAAa,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACQ,kBAAkB,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACL,qBAAqB,CAACM,YAAY,CAAC,CAAC;EACpD;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACN,mBAAmB,CAACK,YAAY,CAAC,CAAC;EAClD;EACA,IAAIE,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACN,gBAAgB,CAACI,YAAY,CAAC,CAAC;EAC/C;EACAG,YAAYA,CAACC,KAAK,EAAE;IAChB,IAAI,CAACR,gBAAgB,CAACS,IAAI,CAACD,KAAK,CAAC;EACrC;EACAE,gBAAgBA,CAACF,KAAK,EAAE;IACpB,IAAI,CAACV,qBAAqB,CAACW,IAAI,CAACD,KAAK,CAAC;EAC1C;EACAG,WAAWA,CAACH,KAAK,EAAE;IACf,MAAMI,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACN,KAAK,CAAC,CAACO,KAAK,CAACC,GAAG,IAAI,IAAI,CAACf,kBAAkB,CAACgB,cAAc,CAACD,GAAG,CAAC,CAAC;IAC/F,IAAI,CAACJ,UAAU,EAAE;MACb;IACJ;IACA,MAAMM,SAAS,GAAAC,aAAA,CAAAA,aAAA,KAAQ,IAAI,CAAClB,kBAAkB,GAAKO,KAAK,CAAE;IAC1D,IAAI,CAAClB,OAAO,CAAC4B,SAAS,EAAE,IAAI,CAACjB,kBAAkB,CAAC,EAAE;MAC9C,IAAI,CAACmB,YAAY,CAACF,SAAS,CAAC;MAC5B,IAAI,CAAChB,OAAO,GAAG,IAAI;IACvB;EACJ;EACA;AACJ;AACA;EACImB,YAAYA,CAACC,MAAM,EAAE;IACjB,MAAMC,aAAa,GAAGhC,SAAS,CAAC,IAAI,CAACU,kBAAkB,CAAC;IACxD,KAAK,MAAM,CAACuB,KAAK,EAAEhB,KAAK,CAAC,IAAIK,MAAM,CAACY,OAAO,CAACF,aAAa,CAAC,EAAE;MACxD,MAAMG,WAAW,GAAGJ,MAAM,CAACK,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACJ,KAAK,KAAKA,KAAK,CAAC;MAC7D,IAAIK,IAAI;MACR,IAAI,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,MAAK,cAAc,EAAE;QACtCD,IAAI,GAAGH,WAAW,CAACG,IAAI;MAC3B,CAAC,MACI,IAAI,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,MAAK,aAAa,EAAE;QAC1CD,IAAI,GAAGH,WAAW,CAACG,IAAI;MAC3B,CAAC,MACI,IAAI,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,MAAK,QAAQ,EAAE;QACrCD,IAAI,GAAGH,WAAW,CAACG,IAAI;MAC3B;MACA,IAAI,CAACA,IAAI,EAAE;QACP;MACJ;MACA,IAAIA,IAAI,YAAYrC,UAAU,EAAE;QAC5B+B,aAAa,CAACC,KAAK,CAAC,GAAG,EAAE;MAC7B,CAAC,MACI;QACD,MAAMO,OAAO,GAAGF,IAAI;QACpB,IAAIG,KAAK,CAACC,OAAO,CAACzB,KAAK,CAAC,EAAE;UACtB;UACAe,aAAa,CAACC,KAAK,CAAC,GAAGhB,KAAK,CAAC0B,MAAM,CAACN,IAAI,IAAIG,OAAO,CAACI,IAAI,CAACC,MAAM,IAAIR,IAAI,CAACS,EAAE,KAAKD,MAAM,CAACC,EAAE,CAAC,CAAC;QAC9F,CAAC,MACI,IAAI,OAAO7B,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;UAClD;UACA,IAAI,CAACuB,OAAO,CAACI,IAAI,CAACC,MAAM,IAAI5B,KAAK,CAAC6B,EAAE,KAAKD,MAAM,CAACC,EAAE,CAAC,EAAE;YACjD,OAAOd,aAAa,CAACC,KAAK,CAAC;UAC/B;QACJ,CAAC,MACI;UACD;UACA,IAAI,CAACO,OAAO,CAACI,IAAI,CAACC,MAAM,IAAI5B,KAAK,KAAK4B,MAAM,CAACC,EAAE,CAAC,EAAE;YAC9C,OAAOd,aAAa,CAACC,KAAK,CAAC;UAC/B;QACJ;MACJ;IACJ;IACA,IAAI,CAAClC,OAAO,CAACiC,aAAa,EAAE,IAAI,CAACtB,kBAAkB,CAAC,EAAE;MAClD,IAAI,CAACmB,YAAY,CAACG,aAAa,CAAC;IACpC;EACJ;EACAH,YAAYA,CAACZ,KAAK,EAAE8B,YAAY,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACpC,OAAO,IAAI,CAACoC,YAAY,EAAE;MAChC,IAAI,CAACrC,kBAAkB,GAAGO,KAAK;MAC/B,IAAI,CAACE,gBAAgB,CAACF,KAAK,CAAC;MAC5B,IAAI,CAACT,mBAAmB,CAACU,IAAI,CAACD,KAAK,CAAC;IACxC;IACA,IAAI,CAACN,OAAO,GAAG,KAAK;EACxB;EACAqC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC1C,OAAO,CAACY,IAAI,CAAC+B,SAAS,CAAC;IAC5B,IAAI,CAACpB,YAAY,CAAC,CAAC,CAAC,CAAC;EACzB;AACJ,CAAC;AACDzB,wBAAwB,GAAGP,UAAU,CAAC,CAClCC,UAAU,CAAC,CAAC,CACf,EAAEM,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}