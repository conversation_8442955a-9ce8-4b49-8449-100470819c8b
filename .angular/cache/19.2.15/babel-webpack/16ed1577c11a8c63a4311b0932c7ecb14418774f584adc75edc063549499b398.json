{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiControlMessagesComponent;\nimport { __decorate } from \"tslib\";\nimport { Component, Inject, Input, Optional } from '@angular/core';\nimport { SWUI_CONTROL_MESSAGES } from './swui-control-messages.token';\nimport { SwuiIsControlInvalidService } from '../swui-is-control-invalid/swui-is-control-invalid.service';\nlet SwuiControlMessagesComponent = (_SwuiControlMessagesComponent = class SwuiControlMessagesComponent {\n  set setMessages(value) {\n    if (value) {\n      this.messages = _objectSpread(_objectSpread({}, this.config || {}), value);\n    }\n  }\n  constructor(config, service) {\n    this.config = config;\n    this.service = service;\n    this.force = false;\n    this.messages = config || {};\n  }\n  get errorMessage() {\n    const control = this.control;\n    if (control) {\n      if (this.force && !control.touched) {\n        control.markAsTouched();\n      }\n      const [key, values] = this.error(control);\n      this.params = values;\n      return key && this.messages[key];\n    }\n    return undefined;\n  }\n  error(control) {\n    if (this.isControlInvalid(control)) {\n      return Object.entries(control.errors || {}).shift() || [undefined, undefined];\n    }\n    return [undefined, undefined];\n  }\n  isControlInvalid(control) {\n    if (this.service) {\n      return this.service.isControlInvalid(control);\n    } else {\n      return control.touched && control.invalid;\n    }\n  }\n}, _SwuiControlMessagesComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Inject,\n    args: [SWUI_CONTROL_MESSAGES]\n  }]\n}, {\n  type: SwuiIsControlInvalidService,\n  decorators: [{\n    type: Optional\n  }]\n}], _SwuiControlMessagesComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  force: [{\n    type: Input\n  }],\n  setMessages: [{\n    type: Input,\n    args: ['messages']\n  }]\n}, _SwuiControlMessagesComponent);\nSwuiControlMessagesComponent = __decorate([Component({\n  selector: 'lib-swui-control-messages',\n  template: `\n    <span *ngIf=\"errorMessage\">{{ errorMessage | translate: params }}</span>\n  `,\n  standalone: false\n})], SwuiControlMessagesComponent);\nexport { SwuiControlMessagesComponent };", "map": {"version": 3, "names": ["__decorate", "Component", "Inject", "Input", "Optional", "SWUI_CONTROL_MESSAGES", "SwuiIsControlInvalidService", "SwuiControlMessagesComponent", "_SwuiControlMessagesComponent", "setMessages", "value", "messages", "_objectSpread", "config", "constructor", "service", "force", "errorMessage", "control", "touched", "<PERSON><PERSON><PERSON><PERSON>ched", "key", "values", "error", "params", "undefined", "isControlInvalid", "Object", "entries", "errors", "shift", "invalid", "ctorParameters", "type", "decorators", "args", "propDecorators", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-control-messages/swui-control-messages.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Component, Inject, Input, Optional } from '@angular/core';\nimport { SWUI_CONTROL_MESSAGES } from './swui-control-messages.token';\nimport { SwuiIsControlInvalidService } from '../swui-is-control-invalid/swui-is-control-invalid.service';\nlet SwuiControlMessagesComponent = class SwuiControlMessagesComponent {\n    set setMessages(value) {\n        if (value) {\n            this.messages = { ...(this.config || {}), ...value };\n        }\n    }\n    constructor(config, service) {\n        this.config = config;\n        this.service = service;\n        this.force = false;\n        this.messages = config || {};\n    }\n    get errorMessage() {\n        const control = this.control;\n        if (control) {\n            if (this.force && !control.touched) {\n                control.markAsTouched();\n            }\n            const [key, values] = this.error(control);\n            this.params = values;\n            return key && this.messages[key];\n        }\n        return undefined;\n    }\n    error(control) {\n        if (this.isControlInvalid(control)) {\n            return Object.entries(control.errors || {}).shift() || [undefined, undefined];\n        }\n        return [undefined, undefined];\n    }\n    isControlInvalid(control) {\n        if (this.service) {\n            return this.service.isControlInvalid(control);\n        }\n        else {\n            return control.touched && control.invalid;\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Optional }, { type: Inject, args: [SWUI_CONTROL_MESSAGES,] }] },\n        { type: SwuiIsControlInvalidService, decorators: [{ type: Optional }] }\n    ]; }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        force: [{ type: Input }],\n        setMessages: [{ type: Input, args: ['messages',] }]\n    }; }\n};\nSwuiControlMessagesComponent = __decorate([\n    Component({\n        selector: 'lib-swui-control-messages',\n        template: `\n    <span *ngIf=\"errorMessage\">{{ errorMessage | translate: params }}</span>\n  `,\n        standalone: false\n    })\n], SwuiControlMessagesComponent);\nexport { SwuiControlMessagesComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAClE,SAASC,qBAAqB,QAAQ,+BAA+B;AACrE,SAASC,2BAA2B,QAAQ,4DAA4D;AACxG,IAAIC,4BAA4B,IAAAC,6BAAA,GAAG,MAAMD,4BAA4B,CAAC;EAClE,IAAIE,WAAWA,CAACC,KAAK,EAAE;IACnB,IAAIA,KAAK,EAAE;MACP,IAAI,CAACC,QAAQ,GAAAC,aAAA,CAAAA,aAAA,KAAS,IAAI,CAACC,MAAM,IAAI,CAAC,CAAC,GAAMH,KAAK,CAAE;IACxD;EACJ;EACAI,WAAWA,CAACD,MAAM,EAAEE,OAAO,EAAE;IACzB,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACL,QAAQ,GAAGE,MAAM,IAAI,CAAC,CAAC;EAChC;EACA,IAAII,YAAYA,CAAA,EAAG;IACf,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAIA,OAAO,EAAE;MACT,IAAI,IAAI,CAACF,KAAK,IAAI,CAACE,OAAO,CAACC,OAAO,EAAE;QAChCD,OAAO,CAACE,aAAa,CAAC,CAAC;MAC3B;MACA,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,KAAK,CAACL,OAAO,CAAC;MACzC,IAAI,CAACM,MAAM,GAAGF,MAAM;MACpB,OAAOD,GAAG,IAAI,IAAI,CAACV,QAAQ,CAACU,GAAG,CAAC;IACpC;IACA,OAAOI,SAAS;EACpB;EACAF,KAAKA,CAACL,OAAO,EAAE;IACX,IAAI,IAAI,CAACQ,gBAAgB,CAACR,OAAO,CAAC,EAAE;MAChC,OAAOS,MAAM,CAACC,OAAO,CAACV,OAAO,CAACW,MAAM,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,IAAI,CAACL,SAAS,EAAEA,SAAS,CAAC;IACjF;IACA,OAAO,CAACA,SAAS,EAAEA,SAAS,CAAC;EACjC;EACAC,gBAAgBA,CAACR,OAAO,EAAE;IACtB,IAAI,IAAI,CAACH,OAAO,EAAE;MACd,OAAO,IAAI,CAACA,OAAO,CAACW,gBAAgB,CAACR,OAAO,CAAC;IACjD,CAAC,MACI;MACD,OAAOA,OAAO,CAACC,OAAO,IAAID,OAAO,CAACa,OAAO;IAC7C;EACJ;AAUJ,CAAC,EATYvB,6BAAA,CAAKwB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAER,SAAS;EAAES,UAAU,EAAE,CAAC;IAAED,IAAI,EAAE7B;EAAS,CAAC,EAAE;IAAE6B,IAAI,EAAE/B,MAAM;IAAEiC,IAAI,EAAE,CAAC9B,qBAAqB;EAAG,CAAC;AAAE,CAAC,EACvG;EAAE4B,IAAI,EAAE3B,2BAA2B;EAAE4B,UAAU,EAAE,CAAC;IAAED,IAAI,EAAE7B;EAAS,CAAC;AAAE,CAAC,CAC1E,EACQI,6BAAA,CAAK4B,cAAc,GAAG;EAC3BlB,OAAO,EAAE,CAAC;IAAEe,IAAI,EAAE9B;EAAM,CAAC,CAAC;EAC1Ba,KAAK,EAAE,CAAC;IAAEiB,IAAI,EAAE9B;EAAM,CAAC,CAAC;EACxBM,WAAW,EAAE,CAAC;IAAEwB,IAAI,EAAE9B,KAAK;IAAEgC,IAAI,EAAE,CAAC,UAAU;EAAG,CAAC;AACtD,CAAC,EAAA3B,6BAAA,CACJ;AACDD,4BAA4B,GAAGP,UAAU,CAAC,CACtCC,SAAS,CAAC;EACNoC,QAAQ,EAAE,2BAA2B;EACrCC,QAAQ,EAAE;AAClB;AACA,GAAG;EACKC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEhC,4BAA4B,CAAC;AAChC,SAASA,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}