{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridRowActionsComponent } from './row-actions.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatButtonModule } from '@angular/material/button';\nlet SwuiGridRowActionsModule = class SwuiGridRowActionsModule {};\nSwuiGridRowActionsModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule.forChild(), MatIconModule, MatButtonModule, MatMenuModule, MatTooltipModule],\n  declarations: [SwuiGridRowActionsComponent],\n  exports: [SwuiGridRowActionsComponent]\n})], SwuiGridRowActionsModule);\nexport { SwuiGridRowActionsModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "TranslateModule", "SwuiGridRowActionsComponent", "MatMenuModule", "MatIconModule", "MatTooltipModule", "MatButtonModule", "SwuiGridRowActionsModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/row-actions/row-actions.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiGridRowActionsComponent } from './row-actions.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatButtonModule } from '@angular/material/button';\nlet SwuiGridRowActionsModule = class SwuiGridRowActionsModule {\n};\nSwuiGridRowActionsModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule.forChild(),\n            MatIconModule,\n            MatButtonModule,\n            MatMenuModule,\n            MatTooltipModule,\n        ],\n        declarations: [\n            SwuiGridRowActionsComponent\n        ],\n        exports: [\n            SwuiGridRowActionsComponent\n        ]\n    })\n], SwuiGridRowActionsModule);\nexport { SwuiGridRowActionsModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,2BAA2B,QAAQ,yBAAyB;AACrE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,IAAIC,wBAAwB,GAAG,MAAMA,wBAAwB,CAAC,EAC7D;AACDA,wBAAwB,GAAGT,UAAU,CAAC,CAClCC,QAAQ,CAAC;EACLS,OAAO,EAAE,CACLR,YAAY,EACZC,eAAe,CAACQ,QAAQ,CAAC,CAAC,EAC1BL,aAAa,EACbE,eAAe,EACfH,aAAa,EACbE,gBAAgB,CACnB;EACDK,YAAY,EAAE,CACVR,2BAA2B,CAC9B;EACDS,OAAO,EAAE,CACLT,2BAA2B;AAEnC,CAAC,CAAC,CACL,EAAEK,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}