{"ast": null, "code": "var _InputNumericRangeComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-numeric-range.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-numeric-range.component.scss?ngResource\";\nimport { ChangeDetectorRef, Component, Input } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nlet InputNumericRangeComponent = (_InputNumericRangeComponent = class InputNumericRangeComponent {\n  set componentOptions(value) {\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.clearable = value === null || value === void 0 ? void 0 : value.clearable;\n    this.dependsOn = value === null || value === void 0 ? void 0 : value.dependsOn;\n    this.dependsOnFieldName = value === null || value === void 0 ? void 0 : value.dependsOnFieldName;\n    this.getDivider = value === null || value === void 0 ? void 0 : value.getDivider;\n    this.subscribeToDepend();\n  }\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n    this.divider = 1;\n    this.destroy$ = new Subject();\n  }\n  get dependsOnValue() {\n    var _this$control;\n    const formValue = (_this$control = this.control) === null || _this$control === void 0 || (_this$control = _this$control.parent) === null || _this$control === void 0 ? void 0 : _this$control.getRawValue();\n    if (!formValue) {\n      return this.dependsOnFieldName;\n    }\n    if (!this.dependsOn) {\n      return;\n    }\n    return formValue[this.dependsOn] ? undefined : this.dependsOnFieldName;\n  }\n  isEmpty() {\n    var _this$control2;\n    const value = (_this$control2 = this.control) === null || _this$control2 === void 0 ? void 0 : _this$control2.value;\n    return !value || Object.values(value).every(item => !item);\n  }\n  clear(event) {\n    event === null || event === void 0 || event.stopPropagation();\n    if (this.control) {\n      this.control.setValue({});\n    }\n  }\n  subscribeToDepend() {\n    var _this$control3;\n    this.destroy$.next(undefined);\n    const formControl = this.dependsOn && ((_this$control3 = this.control) === null || _this$control3 === void 0 || (_this$control3 = _this$control3.parent) === null || _this$control3 === void 0 ? void 0 : _this$control3.get(this.dependsOn));\n    if (!this.dependsOn || !formControl) {\n      return;\n    }\n    this.divider = this.getDivider && this.getDivider(formControl.value) || 1;\n    formControl.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(value => {\n      if (!value) {\n        this.clear();\n      }\n      this.divider = this.getDivider && this.getDivider(value) || 1;\n      this.cdr.detectChanges();\n    });\n  }\n}, _InputNumericRangeComponent.ctorParameters = () => [{\n  type: ChangeDetectorRef\n}], _InputNumericRangeComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }]\n}, _InputNumericRangeComponent);\nInputNumericRangeComponent = __decorate([Component({\n  selector: 'lib-input-numeric-range',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputNumericRangeComponent);\nexport { InputNumericRangeComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectorRef", "Component", "Input", "Subject", "takeUntil", "InputNumericRangeComponent", "_InputNumericRangeComponent", "componentOptions", "value", "title", "clearable", "dependsOn", "dependsOnFieldName", "getDivider", "subscribeToDepend", "constructor", "cdr", "id", "readonly", "submitted", "divider", "destroy$", "dependsOnValue", "_this$control", "formValue", "control", "parent", "getRawValue", "undefined", "isEmpty", "_this$control2", "Object", "values", "every", "item", "clear", "event", "stopPropagation", "setValue", "_this$control3", "next", "formControl", "get", "valueChanges", "pipe", "subscribe", "detectChanges", "ctorParameters", "type", "propDecorators", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-numeric-range/input-numeric-range.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-numeric-range.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-numeric-range.component.scss?ngResource\";\nimport { ChangeDetectorRef, Component, Input } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nlet InputNumericRangeComponent = class InputNumericRangeComponent {\n    set componentOptions(value) {\n        this.title = value?.title;\n        this.clearable = value?.clearable;\n        this.dependsOn = value?.dependsOn;\n        this.dependsOnFieldName = value?.dependsOnFieldName;\n        this.getDivider = value?.getDivider;\n        this.subscribeToDepend();\n    }\n    constructor(cdr) {\n        this.cdr = cdr;\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n        this.divider = 1;\n        this.destroy$ = new Subject();\n    }\n    get dependsOnValue() {\n        const formValue = this.control?.parent?.getRawValue();\n        if (!formValue) {\n            return this.dependsOnFieldName;\n        }\n        if (!this.dependsOn) {\n            return;\n        }\n        return formValue[this.dependsOn] ? undefined : this.dependsOnFieldName;\n    }\n    isEmpty() {\n        const value = this.control?.value;\n        return !value || Object.values(value).every(item => !item);\n    }\n    clear(event) {\n        event?.stopPropagation();\n        if (this.control) {\n            this.control.setValue({});\n        }\n    }\n    subscribeToDepend() {\n        this.destroy$.next(undefined);\n        const formControl = this.dependsOn && this.control?.parent?.get(this.dependsOn);\n        if (!this.dependsOn || !formControl) {\n            return;\n        }\n        this.divider = this.getDivider && this.getDivider(formControl.value) || 1;\n        formControl.valueChanges\n            .pipe(takeUntil(this.destroy$))\n            .subscribe((value) => {\n            if (!value) {\n                this.clear();\n            }\n            this.divider = this.getDivider && this.getDivider(value) || 1;\n            this.cdr.detectChanges();\n        });\n    }\n    static { this.ctorParameters = () => [\n        { type: ChangeDetectorRef }\n    ]; }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }]\n    }; }\n};\nInputNumericRangeComponent = __decorate([\n    Component({\n        selector: 'lib-input-numeric-range',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputNumericRangeComponent);\nexport { InputNumericRangeComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,iDAAiD;AAClF,OAAOC,oBAAoB,MAAM,iDAAiD;AAClF,SAASC,iBAAiB,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AACnE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,IAAIC,0BAA0B,IAAAC,2BAAA,GAAG,MAAMD,0BAA0B,CAAC;EAC9D,IAAIE,gBAAgBA,CAACC,KAAK,EAAE;IACxB,IAAI,CAACC,KAAK,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,KAAK;IACzB,IAAI,CAACC,SAAS,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,SAAS;IACjC,IAAI,CAACC,SAAS,GAAGH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,SAAS;IACjC,IAAI,CAACC,kBAAkB,GAAGJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,kBAAkB;IACnD,IAAI,CAACC,UAAU,GAAGL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEK,UAAU;IACnC,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC5B;EACAC,WAAWA,CAACC,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAIlB,OAAO,CAAC,CAAC;EACjC;EACA,IAAImB,cAAcA,CAAA,EAAG;IAAA,IAAAC,aAAA;IACjB,MAAMC,SAAS,IAAAD,aAAA,GAAG,IAAI,CAACE,OAAO,cAAAF,aAAA,gBAAAA,aAAA,GAAZA,aAAA,CAAcG,MAAM,cAAAH,aAAA,uBAApBA,aAAA,CAAsBI,WAAW,CAAC,CAAC;IACrD,IAAI,CAACH,SAAS,EAAE;MACZ,OAAO,IAAI,CAACZ,kBAAkB;IAClC;IACA,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;MACjB;IACJ;IACA,OAAOa,SAAS,CAAC,IAAI,CAACb,SAAS,CAAC,GAAGiB,SAAS,GAAG,IAAI,CAAChB,kBAAkB;EAC1E;EACAiB,OAAOA,CAAA,EAAG;IAAA,IAAAC,cAAA;IACN,MAAMtB,KAAK,IAAAsB,cAAA,GAAG,IAAI,CAACL,OAAO,cAAAK,cAAA,uBAAZA,cAAA,CAActB,KAAK;IACjC,OAAO,CAACA,KAAK,IAAIuB,MAAM,CAACC,MAAM,CAACxB,KAAK,CAAC,CAACyB,KAAK,CAACC,IAAI,IAAI,CAACA,IAAI,CAAC;EAC9D;EACAC,KAAKA,CAACC,KAAK,EAAE;IACTA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,eAAe,CAAC,CAAC;IACxB,IAAI,IAAI,CAACZ,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACa,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7B;EACJ;EACAxB,iBAAiBA,CAAA,EAAG;IAAA,IAAAyB,cAAA;IAChB,IAAI,CAAClB,QAAQ,CAACmB,IAAI,CAACZ,SAAS,CAAC;IAC7B,MAAMa,WAAW,GAAG,IAAI,CAAC9B,SAAS,MAAA4B,cAAA,GAAI,IAAI,CAACd,OAAO,cAAAc,cAAA,gBAAAA,cAAA,GAAZA,cAAA,CAAcb,MAAM,cAAAa,cAAA,uBAApBA,cAAA,CAAsBG,GAAG,CAAC,IAAI,CAAC/B,SAAS,CAAC;IAC/E,IAAI,CAAC,IAAI,CAACA,SAAS,IAAI,CAAC8B,WAAW,EAAE;MACjC;IACJ;IACA,IAAI,CAACrB,OAAO,GAAG,IAAI,CAACP,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC4B,WAAW,CAACjC,KAAK,CAAC,IAAI,CAAC;IACzEiC,WAAW,CAACE,YAAY,CACnBC,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACiB,QAAQ,CAAC,CAAC,CAC9BwB,SAAS,CAAErC,KAAK,IAAK;MACtB,IAAI,CAACA,KAAK,EAAE;QACR,IAAI,CAAC2B,KAAK,CAAC,CAAC;MAChB;MACA,IAAI,CAACf,OAAO,GAAG,IAAI,CAACP,UAAU,IAAI,IAAI,CAACA,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC;MAC7D,IAAI,CAACQ,GAAG,CAAC8B,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;AAWJ,CAAC,EAVYxC,2BAAA,CAAKyC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEhD;AAAkB,CAAC,CAC9B,EACQM,2BAAA,CAAK2C,cAAc,GAAG;EAC3BxB,OAAO,EAAE,CAAC;IAAEuB,IAAI,EAAE9C;EAAM,CAAC,CAAC;EAC1Be,EAAE,EAAE,CAAC;IAAE+B,IAAI,EAAE9C;EAAM,CAAC,CAAC;EACrBgB,QAAQ,EAAE,CAAC;IAAE8B,IAAI,EAAE9C;EAAM,CAAC,CAAC;EAC3BiB,SAAS,EAAE,CAAC;IAAE6B,IAAI,EAAE9C;EAAM,CAAC,CAAC;EAC5BK,gBAAgB,EAAE,CAAC;IAAEyC,IAAI,EAAE9C;EAAM,CAAC;AACtC,CAAC,EAAAI,2BAAA,CACJ;AACDD,0BAA0B,GAAGR,UAAU,CAAC,CACpCI,SAAS,CAAC;EACNiD,QAAQ,EAAE,yBAAyB;EACnCC,QAAQ,EAAErD,oBAAoB;EAC9BsD,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACtD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEM,0BAA0B,CAAC;AAC9B,SAASA,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}