{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nexport class PermissionService {\n  constructor(permissions) {\n    this.permissions = permissions;\n    this.permissionTree = this.permissions.reduce((tree, item) => _objectSpread(_objectSpread({}, tree), this.insertNode(tree, item.split(':'))), {});\n  }\n  allowedTo(permissions) {\n    if (permissions.length === 0) {\n      return false;\n    }\n    return permissions.some(permission => this.checkPermission(this.permissionTree, permission.split(':')));\n  }\n  areGranted(permissions) {\n    if (permissions.indexOf('granted:all') > -1) {\n      return true;\n    }\n    if (permissions.indexOf('denied:all') > -1) {\n      return false;\n    }\n    const shortPermissions = [];\n    permissions.forEach(permission => {\n      const match = permission.match(/(?:entity:|keyentity:)*(\\w|-)+/);\n      if (Array.isArray(match) && match.length) {\n        shortPermissions.push(match[0]);\n      }\n    });\n    if (shortPermissions.some(permission => this.permissions.indexOf(permission) > -1)) {\n      return true;\n    }\n    return permissions.some(permission => this.permissions.indexOf(permission) > -1);\n  }\n  checkPermission(tree, parts) {\n    const part = parts.shift();\n    const node = part ? tree[part] || {} : {};\n    if (!('__self' in node) && parts.length) {\n      return this.checkPermission(node, parts);\n    }\n    return '__self' in node;\n  }\n  insertNode(tree, parts) {\n    const part = parts.shift();\n    if (!part) {\n      return {};\n    }\n    if (parts.length) {\n      return _objectSpread(_objectSpread({}, tree), {}, {\n        [part]: this.insertNode(part in tree ? tree[part] : {}, parts)\n      });\n    }\n    return _objectSpread(_objectSpread({}, tree), {}, {\n      [part]: _objectSpread(_objectSpread({}, tree[part]), {}, {\n        __self: true\n      })\n    });\n  }\n}", "map": {"version": 3, "names": ["PermissionService", "constructor", "permissions", "permissionTree", "reduce", "tree", "item", "_objectSpread", "insertNode", "split", "allowedTo", "length", "some", "permission", "checkPermission", "areGranted", "indexOf", "shortPermissions", "for<PERSON>ach", "match", "Array", "isArray", "push", "parts", "part", "shift", "node", "__self"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-auth/permission.service.ts"], "sourcesContent": ["export class PermissionService {\n    constructor(permissions) {\n        this.permissions = permissions;\n        this.permissionTree = this.permissions.reduce((tree, item) => ({\n            ...tree,\n            ...this.insertNode(tree, item.split(':'))\n        }), {});\n    }\n    allowedTo(permissions) {\n        if (permissions.length === 0) {\n            return false;\n        }\n        return permissions.some(permission => this.checkPermission(this.permissionTree, permission.split(':')));\n    }\n    areGranted(permissions) {\n        if (permissions.indexOf('granted:all') > -1) {\n            return true;\n        }\n        if (permissions.indexOf('denied:all') > -1) {\n            return false;\n        }\n        const shortPermissions = [];\n        permissions.forEach(permission => {\n            const match = permission.match(/(?:entity:|keyentity:)*(\\w|-)+/);\n            if (Array.isArray(match) && match.length) {\n                shortPermissions.push(match[0]);\n            }\n        });\n        if (shortPermissions.some(permission => this.permissions.indexOf(permission) > -1)) {\n            return true;\n        }\n        return permissions.some(permission => this.permissions.indexOf(permission) > -1);\n    }\n    checkPermission(tree, parts) {\n        const part = parts.shift();\n        const node = part ? tree[part] || {} : {};\n        if (!('__self' in node) && parts.length) {\n            return this.checkPermission(node, parts);\n        }\n        return '__self' in node;\n    }\n    insertNode(tree, parts) {\n        const part = parts.shift();\n        if (!part) {\n            return {};\n        }\n        if (parts.length) {\n            return { ...tree, [part]: this.insertNode(part in tree ? tree[part] : {}, parts) };\n        }\n        return { ...tree, [part]: { ...tree[part], __self: true } };\n    }\n}\n"], "mappings": ";AAAA,OAAO,MAAMA,iBAAiB,CAAC;EAC3BC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACD,WAAW,CAACE,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAAC,aAAA,CAAAA,aAAA,KAClDF,IAAI,GACJ,IAAI,CAACG,UAAU,CAACH,IAAI,EAAEC,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAC3C,EAAE,CAAC,CAAC,CAAC;EACX;EACAC,SAASA,CAACR,WAAW,EAAE;IACnB,IAAIA,WAAW,CAACS,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO,KAAK;IAChB;IACA,OAAOT,WAAW,CAACU,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,eAAe,CAAC,IAAI,CAACX,cAAc,EAAEU,UAAU,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3G;EACAM,UAAUA,CAACb,WAAW,EAAE;IACpB,IAAIA,WAAW,CAACc,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;MACzC,OAAO,IAAI;IACf;IACA,IAAId,WAAW,CAACc,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;MACxC,OAAO,KAAK;IAChB;IACA,MAAMC,gBAAgB,GAAG,EAAE;IAC3Bf,WAAW,CAACgB,OAAO,CAACL,UAAU,IAAI;MAC9B,MAAMM,KAAK,GAAGN,UAAU,CAACM,KAAK,CAAC,gCAAgC,CAAC;MAChE,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACR,MAAM,EAAE;QACtCM,gBAAgB,CAACK,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MACnC;IACJ,CAAC,CAAC;IACF,IAAIF,gBAAgB,CAACL,IAAI,CAACC,UAAU,IAAI,IAAI,CAACX,WAAW,CAACc,OAAO,CAACH,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAChF,OAAO,IAAI;IACf;IACA,OAAOX,WAAW,CAACU,IAAI,CAACC,UAAU,IAAI,IAAI,CAACX,WAAW,CAACc,OAAO,CAACH,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;EACpF;EACAC,eAAeA,CAACT,IAAI,EAAEkB,KAAK,EAAE;IACzB,MAAMC,IAAI,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC;IAC1B,MAAMC,IAAI,GAAGF,IAAI,GAAGnB,IAAI,CAACmB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACzC,IAAI,EAAE,QAAQ,IAAIE,IAAI,CAAC,IAAIH,KAAK,CAACZ,MAAM,EAAE;MACrC,OAAO,IAAI,CAACG,eAAe,CAACY,IAAI,EAAEH,KAAK,CAAC;IAC5C;IACA,OAAO,QAAQ,IAAIG,IAAI;EAC3B;EACAlB,UAAUA,CAACH,IAAI,EAAEkB,KAAK,EAAE;IACpB,MAAMC,IAAI,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC;IAC1B,IAAI,CAACD,IAAI,EAAE;MACP,OAAO,CAAC,CAAC;IACb;IACA,IAAID,KAAK,CAACZ,MAAM,EAAE;MACd,OAAAJ,aAAA,CAAAA,aAAA,KAAYF,IAAI;QAAE,CAACmB,IAAI,GAAG,IAAI,CAAChB,UAAU,CAACgB,IAAI,IAAInB,IAAI,GAAGA,IAAI,CAACmB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAED,KAAK;MAAC;IACpF;IACA,OAAAhB,aAAA,CAAAA,aAAA,KAAYF,IAAI;MAAE,CAACmB,IAAI,GAAAjB,aAAA,CAAAA,aAAA,KAAQF,IAAI,CAACmB,IAAI,CAAC;QAAEG,MAAM,EAAE;MAAI;IAAE;EAC7D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}