{"ast": null, "code": "var _InputImageComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-image.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-image.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, ViewChild } from '@angular/core';\nimport { Observable, of, pipe, Subject } from 'rxjs';\nimport { catchError, map, switchMap, takeUntil } from 'rxjs/operators';\nimport { DomSanitizer } from '@angular/platform-browser';\nlet InputImageComponent = (_InputImageComponent = class InputImageComponent {\n  set componentOptions(value) {\n    var _value$validation;\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.defaultValue = value === null || value === void 0 ? void 0 : value.defaultValue;\n    this.fileInputLabel = value === null || value === void 0 ? void 0 : value.fileInputLabel;\n    this.required = value === null || value === void 0 ? void 0 : value.required;\n    this.errorMessages = value === null || value === void 0 || (_value$validation = value.validation) === null || _value$validation === void 0 ? void 0 : _value$validation.messages;\n  }\n  constructor(cd, sanitizer) {\n    this.cd = cd;\n    this.sanitizer = sanitizer;\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n    this.selectedFile = new Subject();\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.selectedFile.pipe(validateImageFile(), mapToBase64(), catchError(() => of(null)), takeUntil(this.destroy$)).subscribe(value => {\n      if (this.control && value) {\n        this.control.setValue(value);\n      }\n      this.cd.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n  get isIdentical() {\n    if (!Boolean(this.image) && !Boolean(this.defaultValue)) {\n      return true;\n    }\n    return this.image === this.defaultValue;\n  }\n  get image() {\n    var _this$control;\n    return (_this$control = this.control) === null || _this$control === void 0 ? void 0 : _this$control.value;\n  }\n  get safeImage() {\n    return this.sanitizer.bypassSecurityTrustUrl(this.image || '');\n  }\n  handleChange(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    const target = event.target;\n    const file = target.files && target.files[0];\n    if (file) {\n      this.selectedFile.next(file);\n    }\n  }\n  onResetClick(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    if (this.isIdentical) {\n      return;\n    }\n    if (this.control) {\n      this.control.setValue(this.defaultValue || '');\n      if (this.fileInputRef) {\n        const input = this.fileInputRef.nativeElement;\n        input.value = '';\n      }\n    }\n  }\n}, _InputImageComponent.ctorParameters = () => [{\n  type: ChangeDetectorRef\n}, {\n  type: DomSanitizer\n}], _InputImageComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }],\n  fileInputRef: [{\n    type: ViewChild,\n    args: ['fileInput']\n  }]\n}, _InputImageComponent);\nInputImageComponent = __decorate([Component({\n  selector: 'lib-input-image',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputImageComponent);\nexport { InputImageComponent };\nfunction validateImageFile() {\n  const supportedFileTypes = new Set(['png', 'svg', 'jpg', 'jpeg', 'gif']);\n  return pipe(switchMap(file => of(file).pipe(map(() => {\n    const ext = file === null || file === void 0 ? void 0 : file.name.substring((file === null || file === void 0 ? void 0 : file.name.lastIndexOf('.')) + 1);\n    return ext && supportedFileTypes.has(ext) ? file : null;\n  }))));\n}\nfunction mapToBase64() {\n  return pipe(switchMap(file => file ? new Observable(subscriber => {\n    const fileReader = new FileReader();\n    fileReader.onloadend = () => {\n      if (fileReader.readyState === FileReader.DONE) {\n        if (fileReader.error) {\n          subscriber.error(fileReader.error);\n        } else {\n          if (typeof fileReader.result === 'string') {\n            subscriber.next(fileReader.result);\n          }\n          subscriber.complete();\n        }\n      }\n    };\n    fileReader.readAsDataURL(file);\n    return () => {\n      fileReader.onloadend = null;\n      fileReader.abort();\n    };\n  }) : of(null)));\n}", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectionStrategy", "ChangeDetectorRef", "Component", "Input", "ViewChild", "Observable", "of", "pipe", "Subject", "catchError", "map", "switchMap", "takeUntil", "Dom<PERSON><PERSON><PERSON>zer", "InputImageComponent", "_InputImageComponent", "componentOptions", "value", "_value$validation", "title", "defaultValue", "fileInputLabel", "required", "errorMessages", "validation", "messages", "constructor", "cd", "sanitizer", "id", "readonly", "submitted", "selectedFile", "destroy$", "ngOnInit", "validateImageFile", "mapToBase64", "subscribe", "control", "setValue", "detectChanges", "ngOnDestroy", "next", "undefined", "complete", "isIdentical", "Boolean", "image", "_this$control", "safeImage", "bypassSecurityTrustUrl", "handleChange", "event", "preventDefault", "stopPropagation", "target", "file", "files", "onResetClick", "fileInputRef", "input", "nativeElement", "ctorParameters", "type", "propDecorators", "args", "selector", "template", "changeDetection", "OnPush", "standalone", "styles", "supportedFileTypes", "Set", "ext", "name", "substring", "lastIndexOf", "has", "subscriber", "fileReader", "FileReader", "onloadend", "readyState", "DONE", "error", "result", "readAsDataURL", "abort"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-image/input-image.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-image.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-image.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, ViewChild } from '@angular/core';\nimport { Observable, of, pipe, Subject } from 'rxjs';\nimport { catchError, map, switchMap, takeUntil } from 'rxjs/operators';\nimport { DomSanitizer } from '@angular/platform-browser';\nlet InputImageComponent = class InputImageComponent {\n    set componentOptions(value) {\n        this.title = value?.title;\n        this.defaultValue = value?.defaultValue;\n        this.fileInputLabel = value?.fileInputLabel;\n        this.required = value?.required;\n        this.errorMessages = value?.validation?.messages;\n    }\n    constructor(cd, sanitizer) {\n        this.cd = cd;\n        this.sanitizer = sanitizer;\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n        this.selectedFile = new Subject();\n        this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n        this.selectedFile.pipe(validateImageFile(), mapToBase64(), catchError(() => of(null)), takeUntil(this.destroy$)).subscribe(value => {\n            if (this.control && value) {\n                this.control.setValue(value);\n            }\n            this.cd.detectChanges();\n        });\n    }\n    ngOnDestroy() {\n        this.destroy$.next(undefined);\n        this.destroy$.complete();\n    }\n    get isIdentical() {\n        if (!Boolean(this.image) && !Boolean(this.defaultValue)) {\n            return true;\n        }\n        return this.image === this.defaultValue;\n    }\n    get image() {\n        return this.control?.value;\n    }\n    get safeImage() {\n        return this.sanitizer.bypassSecurityTrustUrl(this.image || '');\n    }\n    handleChange(event) {\n        event.preventDefault();\n        event.stopPropagation();\n        const target = event.target;\n        const file = target.files && target.files[0];\n        if (file) {\n            this.selectedFile.next(file);\n        }\n    }\n    onResetClick(event) {\n        event.preventDefault();\n        event.stopPropagation();\n        if (this.isIdentical) {\n            return;\n        }\n        if (this.control) {\n            this.control.setValue(this.defaultValue || '');\n            if (this.fileInputRef) {\n                const input = this.fileInputRef.nativeElement;\n                input.value = '';\n            }\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: ChangeDetectorRef },\n        { type: DomSanitizer }\n    ]; }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }],\n        fileInputRef: [{ type: ViewChild, args: ['fileInput',] }]\n    }; }\n};\nInputImageComponent = __decorate([\n    Component({\n        selector: 'lib-input-image',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputImageComponent);\nexport { InputImageComponent };\nfunction validateImageFile() {\n    const supportedFileTypes = new Set(['png', 'svg', 'jpg', 'jpeg', 'gif']);\n    return pipe(switchMap(file => of(file).pipe(map(() => {\n        const ext = file?.name.substring(file?.name.lastIndexOf('.') + 1);\n        return ext && supportedFileTypes.has(ext) ? file : null;\n    }))));\n}\nfunction mapToBase64() {\n    return pipe(switchMap(file => file ? new Observable((subscriber) => {\n        const fileReader = new FileReader();\n        fileReader.onloadend = () => {\n            if (fileReader.readyState === FileReader.DONE) {\n                if (fileReader.error) {\n                    subscriber.error(fileReader.error);\n                }\n                else {\n                    if (typeof fileReader.result === 'string') {\n                        subscriber.next(fileReader.result);\n                    }\n                    subscriber.complete();\n                }\n            }\n        };\n        fileReader.readAsDataURL(file);\n        return () => {\n            fileReader.onloadend = null;\n            fileReader.abort();\n        };\n    }) : of(null)));\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,SAASC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AACvG,SAASC,UAAU,EAAEC,EAAE,EAAEC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AACpD,SAASC,UAAU,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACtE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,IAAIC,mBAAmB,IAAAC,oBAAA,GAAG,MAAMD,mBAAmB,CAAC;EAChD,IAAIE,gBAAgBA,CAACC,KAAK,EAAE;IAAA,IAAAC,iBAAA;IACxB,IAAI,CAACC,KAAK,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,KAAK;IACzB,IAAI,CAACC,YAAY,GAAGH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,YAAY;IACvC,IAAI,CAACC,cAAc,GAAGJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,cAAc;IAC3C,IAAI,CAACC,QAAQ,GAAGL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEK,QAAQ;IAC/B,IAAI,CAACC,aAAa,GAAGN,KAAK,aAALA,KAAK,gBAAAC,iBAAA,GAALD,KAAK,CAAEO,UAAU,cAAAN,iBAAA,uBAAjBA,iBAAA,CAAmBO,QAAQ;EACpD;EACAC,WAAWA,CAACC,EAAE,EAAEC,SAAS,EAAE;IACvB,IAAI,CAACD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,YAAY,GAAG,IAAIxB,OAAO,CAAC,CAAC;IACjC,IAAI,CAACyB,QAAQ,GAAG,IAAIzB,OAAO,CAAC,CAAC;EACjC;EACA0B,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,YAAY,CAACzB,IAAI,CAAC4B,iBAAiB,CAAC,CAAC,EAAEC,WAAW,CAAC,CAAC,EAAE3B,UAAU,CAAC,MAAMH,EAAE,CAAC,IAAI,CAAC,CAAC,EAAEM,SAAS,CAAC,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAACI,SAAS,CAACpB,KAAK,IAAI;MAChI,IAAI,IAAI,CAACqB,OAAO,IAAIrB,KAAK,EAAE;QACvB,IAAI,CAACqB,OAAO,CAACC,QAAQ,CAACtB,KAAK,CAAC;MAChC;MACA,IAAI,CAACU,EAAE,CAACa,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,QAAQ,CAACS,IAAI,CAACC,SAAS,CAAC;IAC7B,IAAI,CAACV,QAAQ,CAACW,QAAQ,CAAC,CAAC;EAC5B;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,IAAI,CAACC,OAAO,CAAC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACD,OAAO,CAAC,IAAI,CAAC1B,YAAY,CAAC,EAAE;MACrD,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAAC2B,KAAK,KAAK,IAAI,CAAC3B,YAAY;EAC3C;EACA,IAAI2B,KAAKA,CAAA,EAAG;IAAA,IAAAC,aAAA;IACR,QAAAA,aAAA,GAAO,IAAI,CAACV,OAAO,cAAAU,aAAA,uBAAZA,aAAA,CAAc/B,KAAK;EAC9B;EACA,IAAIgC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrB,SAAS,CAACsB,sBAAsB,CAAC,IAAI,CAACH,KAAK,IAAI,EAAE,CAAC;EAClE;EACAI,YAAYA,CAACC,KAAK,EAAE;IAChBA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACvB,MAAMC,MAAM,GAAGH,KAAK,CAACG,MAAM;IAC3B,MAAMC,IAAI,GAAGD,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IAC5C,IAAID,IAAI,EAAE;MACN,IAAI,CAACxB,YAAY,CAACU,IAAI,CAACc,IAAI,CAAC;IAChC;EACJ;EACAE,YAAYA,CAACN,KAAK,EAAE;IAChBA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACvB,IAAI,IAAI,CAACT,WAAW,EAAE;MAClB;IACJ;IACA,IAAI,IAAI,CAACP,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,QAAQ,CAAC,IAAI,CAACnB,YAAY,IAAI,EAAE,CAAC;MAC9C,IAAI,IAAI,CAACuC,YAAY,EAAE;QACnB,MAAMC,KAAK,GAAG,IAAI,CAACD,YAAY,CAACE,aAAa;QAC7CD,KAAK,CAAC3C,KAAK,GAAG,EAAE;MACpB;IACJ;EACJ;AAaJ,CAAC,EAZYF,oBAAA,CAAK+C,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE9D;AAAkB,CAAC,EAC3B;EAAE8D,IAAI,EAAElD;AAAa,CAAC,CACzB,EACQE,oBAAA,CAAKiD,cAAc,GAAG;EAC3B1B,OAAO,EAAE,CAAC;IAAEyB,IAAI,EAAE5D;EAAM,CAAC,CAAC;EAC1B0B,EAAE,EAAE,CAAC;IAAEkC,IAAI,EAAE5D;EAAM,CAAC,CAAC;EACrB2B,QAAQ,EAAE,CAAC;IAAEiC,IAAI,EAAE5D;EAAM,CAAC,CAAC;EAC3B4B,SAAS,EAAE,CAAC;IAAEgC,IAAI,EAAE5D;EAAM,CAAC,CAAC;EAC5Ba,gBAAgB,EAAE,CAAC;IAAE+C,IAAI,EAAE5D;EAAM,CAAC,CAAC;EACnCwD,YAAY,EAAE,CAAC;IAAEI,IAAI,EAAE3D,SAAS;IAAE6D,IAAI,EAAE,CAAC,WAAW;EAAG,CAAC;AAC5D,CAAC,EAAAlD,oBAAA,CACJ;AACDD,mBAAmB,GAAGjB,UAAU,CAAC,CAC7BK,SAAS,CAAC;EACNgE,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAErE,oBAAoB;EAC9BsE,eAAe,EAAEpE,uBAAuB,CAACqE,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACxE,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEe,mBAAmB,CAAC;AACvB,SAASA,mBAAmB;AAC5B,SAASqB,iBAAiBA,CAAA,EAAG;EACzB,MAAMqC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;EACxE,OAAOlE,IAAI,CAACI,SAAS,CAAC6C,IAAI,IAAIlD,EAAE,CAACkD,IAAI,CAAC,CAACjD,IAAI,CAACG,GAAG,CAAC,MAAM;IAClD,MAAMgE,GAAG,GAAGlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,IAAI,CAACC,SAAS,CAAC,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC,IAAG,CAAC,CAAC;IACjE,OAAOH,GAAG,IAAIF,kBAAkB,CAACM,GAAG,CAACJ,GAAG,CAAC,GAAGlB,IAAI,GAAG,IAAI;EAC3D,CAAC,CAAC,CAAC,CAAC,CAAC;AACT;AACA,SAASpB,WAAWA,CAAA,EAAG;EACnB,OAAO7B,IAAI,CAACI,SAAS,CAAC6C,IAAI,IAAIA,IAAI,GAAG,IAAInD,UAAU,CAAE0E,UAAU,IAAK;IAChE,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAAC,CAAC;IACnCD,UAAU,CAACE,SAAS,GAAG,MAAM;MACzB,IAAIF,UAAU,CAACG,UAAU,KAAKF,UAAU,CAACG,IAAI,EAAE;QAC3C,IAAIJ,UAAU,CAACK,KAAK,EAAE;UAClBN,UAAU,CAACM,KAAK,CAACL,UAAU,CAACK,KAAK,CAAC;QACtC,CAAC,MACI;UACD,IAAI,OAAOL,UAAU,CAACM,MAAM,KAAK,QAAQ,EAAE;YACvCP,UAAU,CAACrC,IAAI,CAACsC,UAAU,CAACM,MAAM,CAAC;UACtC;UACAP,UAAU,CAACnC,QAAQ,CAAC,CAAC;QACzB;MACJ;IACJ,CAAC;IACDoC,UAAU,CAACO,aAAa,CAAC/B,IAAI,CAAC;IAC9B,OAAO,MAAM;MACTwB,UAAU,CAACE,SAAS,GAAG,IAAI;MAC3BF,UAAU,CAACQ,KAAK,CAAC,CAAC;IACtB,CAAC;EACL,CAAC,CAAC,GAAGlF,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}