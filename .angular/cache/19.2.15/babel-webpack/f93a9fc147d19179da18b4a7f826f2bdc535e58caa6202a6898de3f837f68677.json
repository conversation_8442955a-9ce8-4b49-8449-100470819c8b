{"ast": null, "code": "export const gameCategoryItemTypes = {\n  GAME: 'game',\n  PROVIDER: 'provider',\n  LABEL: 'label',\n  INTERSECTION: 'intersection'\n};", "map": {"version": 3, "names": ["gameCategoryItemTypes", "GAME", "PROVIDER", "LABEL", "INTERSECTION"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-category-item.model.ts"], "sourcesContent": ["export const gameCategoryItemTypes = {\n    GAME: 'game',\n    PROVIDER: 'provider',\n    LABEL: 'label',\n    INTERSECTION: 'intersection'\n};\n"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,GAAG;EACjCC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,YAAY,EAAE;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}