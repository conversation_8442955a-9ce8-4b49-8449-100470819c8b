{"ast": null, "code": "var _SwuiSelectTableComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-select-table.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-select-table.component.scss?ngResource\";\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, HostBinding, Input, ViewChild } from '@angular/core';\nimport { UntypedFormControl, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { filter, map, take, takeUntil } from 'rxjs/operators';\nconst CONTROL_NAME = 'lib-swui-select-table';\nlet nextUniqueId = 0;\nlet SwuiSelectTableComponent = (_SwuiSelectTableComponent = class SwuiSelectTableComponent {\n  set data(val) {\n    var _this$options;\n    this._data = val || [];\n    this.options = this._data;\n    if (!this.disableAllOption) {\n      var _this$value;\n      this.allChecked = ((_this$value = this.value) === null || _this$value === void 0 ? void 0 : _this$value.length) === this.enabledData.length;\n    }\n    (_this$options = this.options) === null || _this$options === void 0 || _this$options.forEach(option => {\n      var _this$value2;\n      return option.state = {\n        checked: (_this$value2 = this.value) === null || _this$value2 === void 0 ? void 0 : _this$value2.includes(option.id)\n      };\n    });\n    if (Array.isArray(this.options) && this.options.length) {\n      this.onDataReceived.next(this.options);\n    }\n  }\n  get data() {\n    return this._data;\n  }\n  set disabled(disabled) {\n    this.setDisabledState(disabled);\n  }\n  get disabled() {\n    return this._disabled;\n  }\n  set value(val) {\n    this.patchSelectControl(val);\n  }\n  get value() {\n    var _this$selectControl$v;\n    return (_this$selectControl$v = this.selectControl.value) === null || _this$selectControl$v === void 0 ? void 0 : _this$selectControl$v.map(v => v.id);\n  }\n  get empty() {\n    var _this$selectControl$v2;\n    return !((_this$selectControl$v2 = this.selectControl.value) !== null && _this$selectControl$v2 !== void 0 && _this$selectControl$v2.length);\n  }\n  get viewportHeight() {\n    let length = this.data.length;\n    length = Math.floor(length / this.rowsNumber) && this.rowsNumber || length % this.rowsNumber;\n    return length * this.itemHeight;\n  }\n  constructor(cd) {\n    this.cd = cd;\n    this.searchPlaceholder = 'Search';\n    this.showSearch = false;\n    this.startSearchLength = 0;\n    this.disableEmptyOption = false;\n    this.disableAllOption = false;\n    this.columns = [];\n    this.rowsNumber = 3;\n    this.loading = false;\n    this.allChecked = false;\n    this.viewSelected = false;\n    this.options = [];\n    this.itemHeight = 48;\n    this.onDataReceived = new ReplaySubject(1);\n    this.controlType = CONTROL_NAME;\n    this.searchControl = new UntypedFormControl('');\n    this.selectControl = new UntypedFormControl();\n    this.destroyed$ = new Subject();\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._data = [];\n    this._disabled = false;\n    this.previousSelected = [];\n    this.onChange = _ => {};\n  }\n  ngOnInit() {\n    this.searchControl.valueChanges.pipe(filter(data => this.showSearch && ((data === null || data === void 0 ? void 0 : data.length) >= (this.startSearchLength || 0) || (data === null || data === void 0 ? void 0 : data.length) === 0)), map(searchString => searchString.toLowerCase()), takeUntil(this.destroyed$)).subscribe(search => {\n      this.previousSelected = search ? this.selectControl.value : [];\n      this.options = this.data.filter(option => {\n        for (const column of this.columns) {\n          if (option[column.field] && option[column.field].toLowerCase().indexOf(search) > -1) {\n            return true;\n          }\n        }\n        return false;\n      });\n      this.cd.markForCheck();\n    });\n    this.selectControl.valueChanges.pipe(map(val => {\n      const previousSelected = this.previousSelected.filter(id => !this.options.some(option => option.id === id));\n      const values = this.enabledData.filter(({\n        id\n      }) => previousSelected.some(item => item === id));\n      return [...values, ...val];\n    }), map(val => val.map(item => item ? item.id : '')), takeUntil(this.destroyed$)).subscribe(val => {\n      this.onChange(val || null);\n      this.cd.detectChanges();\n    });\n  }\n  writeValue(val) {\n    this.patchSelectControl(val);\n  }\n  patchSelectControl(val) {\n    this.onDataReceived.pipe(take(1)).subscribe(options => {\n      if (!this.disableAllOption) {\n        this.allChecked = Array.isArray(val) && val.length === this.enabledData.length;\n      }\n      const values = coerceArray(val || []);\n      options === null || options === void 0 || options.forEach(option => option.state = {\n        checked: values === null || values === void 0 ? void 0 : values.includes(option.id)\n      });\n      const value = options.filter(opt => values.includes(opt.id));\n      this.selectControl.patchValue(value, {\n        emitEvent: false\n      });\n      this.cd.detectChanges();\n    });\n  }\n  toggleAll(event) {\n    event === null || event === void 0 || event.preventDefault();\n    event === null || event === void 0 || event.stopPropagation();\n    this.allChecked = !this.allChecked;\n    this.options.forEach(option => {\n      if (!option.disabled) {\n        option.state.checked = this.allChecked;\n      }\n    });\n    let checkedOptions = this.options.filter(option => option.state.checked);\n    this.selectControl.setValue(checkedOptions);\n  }\n  showSelected() {\n    this.viewSelected = !this.viewSelected;\n    this.previousSelected = this.viewSelected ? this.selectControl.value : [];\n    this.options = this.viewSelected ? this.data.filter(option => {\n      return option.state.checked;\n    }) : this.data;\n    this.cd.markForCheck();\n  }\n  onSelectMultiple(event, option) {\n    var _this$data;\n    event.preventDefault();\n    event.stopPropagation();\n    if (option.state) {\n      option.state.checked = !option.state.checked;\n    }\n    this.selectControl.patchValue((_this$data = this.data) === null || _this$data === void 0 ? void 0 : _this$data.filter(item => {\n      var _item$state;\n      return (_item$state = item.state) === null || _item$state === void 0 ? void 0 : _item$state.checked;\n    }));\n  }\n  stopPropagation(event) {\n    event.stopPropagation();\n  }\n  get enabledData() {\n    return this.data.filter(({\n      disabled\n    }) => !disabled);\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched() {}\n  setDisabledState(isDisabled) {\n    this._disabled = isDisabled;\n    isDisabled ? this.searchControl.disable() : this.searchControl.enable();\n  }\n}, _SwuiSelectTableComponent.ctorParameters = () => [{\n  type: ChangeDetectorRef\n}], _SwuiSelectTableComponent.propDecorators = {\n  searchPlaceholder: [{\n    type: Input\n  }],\n  showSearch: [{\n    type: Input\n  }],\n  startSearchLength: [{\n    type: Input\n  }],\n  disableEmptyOption: [{\n    type: Input\n  }],\n  disableAllOption: [{\n    type: Input\n  }],\n  columns: [{\n    type: Input\n  }],\n  rowsNumber: [{\n    type: Input\n  }],\n  loading: [{\n    type: Input\n  }],\n  data: [{\n    type: Input\n  }],\n  disabled: [{\n    type: Input\n  }],\n  value: [{\n    type: Input\n  }],\n  trigger: [{\n    type: ViewChild,\n    args: ['trigger']\n  }],\n  searchRef: [{\n    type: ViewChild,\n    args: ['search']\n  }],\n  virtualScroll: [{\n    type: ViewChild,\n    args: [CdkVirtualScrollViewport, {\n      static: false\n    }]\n  }],\n  id: [{\n    type: HostBinding\n  }]\n}, _SwuiSelectTableComponent);\nSwuiSelectTableComponent = __decorate([Component({\n  selector: 'lib-swui-select-table',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SwuiSelectTableComponent),\n    multi: true\n  }],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSelectTableComponent);\nexport { SwuiSelectTableComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "coerce<PERSON><PERSON><PERSON>", "CdkVirtualScrollViewport", "ChangeDetectionStrategy", "ChangeDetectorRef", "Component", "forwardRef", "HostBinding", "Input", "ViewChild", "UntypedFormControl", "NG_VALUE_ACCESSOR", "ReplaySubject", "Subject", "filter", "map", "take", "takeUntil", "CONTROL_NAME", "nextUniqueId", "SwuiSelectTableComponent", "_SwuiSelectTableComponent", "data", "val", "_this$options", "_data", "options", "disableAllOption", "_this$value", "allChecked", "value", "length", "enabledData", "for<PERSON>ach", "option", "_this$value2", "state", "checked", "includes", "id", "Array", "isArray", "onDataReceived", "next", "disabled", "setDisabledState", "_disabled", "patchSelectControl", "_this$selectControl$v", "selectControl", "v", "empty", "_this$selectControl$v2", "viewportHeight", "Math", "floor", "rowsNumber", "itemHeight", "constructor", "cd", "searchPlaceholder", "showSearch", "startSearchLength", "disableEmptyOption", "columns", "loading", "viewSelected", "controlType", "searchControl", "destroyed$", "previousSelected", "onChange", "_", "ngOnInit", "valueChanges", "pipe", "searchString", "toLowerCase", "subscribe", "search", "column", "field", "indexOf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "some", "values", "item", "detectChanges", "writeValue", "opt", "patchValue", "emitEvent", "toggleAll", "event", "preventDefault", "stopPropagation", "checkedOptions", "setValue", "showSelected", "onSelectMultiple", "_this$data", "_item$state", "registerOnChange", "fn", "registerOnTouched", "isDisabled", "disable", "enable", "ctorParameters", "type", "propDecorators", "trigger", "args", "searchRef", "virtualScroll", "static", "selector", "template", "providers", "provide", "useExisting", "multi", "changeDetection", "OnPush", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-select-table/swui-select-table.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-select-table.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-select-table.component.scss?ngResource\";\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, forwardRef, HostBinding, Input, ViewChild } from '@angular/core';\nimport { UntypedFormControl, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { filter, map, take, takeUntil } from 'rxjs/operators';\nconst CONTROL_NAME = 'lib-swui-select-table';\nlet nextUniqueId = 0;\nlet SwuiSelectTableComponent = class SwuiSelectTableComponent {\n    set data(val) {\n        this._data = val || [];\n        this.options = this._data;\n        if (!this.disableAllOption) {\n            this.allChecked = this.value?.length === this.enabledData.length;\n        }\n        this.options?.forEach(option => option.state = {\n            checked: this.value?.includes(option.id),\n        });\n        if (Array.isArray(this.options) && this.options.length) {\n            this.onDataReceived.next(this.options);\n        }\n    }\n    get data() {\n        return this._data;\n    }\n    set disabled(disabled) {\n        this.setDisabledState(disabled);\n    }\n    get disabled() {\n        return this._disabled;\n    }\n    set value(val) {\n        this.patchSelectControl(val);\n    }\n    get value() {\n        return this.selectControl.value?.map((v) => v.id);\n    }\n    get empty() {\n        return !this.selectControl.value?.length;\n    }\n    get viewportHeight() {\n        let length = this.data.length;\n        length = Math.floor(length / this.rowsNumber) && this.rowsNumber || length % this.rowsNumber;\n        return length * this.itemHeight;\n    }\n    constructor(cd) {\n        this.cd = cd;\n        this.searchPlaceholder = 'Search';\n        this.showSearch = false;\n        this.startSearchLength = 0;\n        this.disableEmptyOption = false;\n        this.disableAllOption = false;\n        this.columns = [];\n        this.rowsNumber = 3;\n        this.loading = false;\n        this.allChecked = false;\n        this.viewSelected = false;\n        this.options = [];\n        this.itemHeight = 48;\n        this.onDataReceived = new ReplaySubject(1);\n        this.controlType = CONTROL_NAME;\n        this.searchControl = new UntypedFormControl('');\n        this.selectControl = new UntypedFormControl();\n        this.destroyed$ = new Subject();\n        this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n        this._data = [];\n        this._disabled = false;\n        this.previousSelected = [];\n        this.onChange = (_) => {\n        };\n    }\n    ngOnInit() {\n        this.searchControl.valueChanges.pipe(filter(data => this.showSearch && (data?.length >= (this.startSearchLength || 0) || data?.length === 0)), map(searchString => searchString.toLowerCase()), takeUntil(this.destroyed$)).subscribe(search => {\n            this.previousSelected = search ? this.selectControl.value : [];\n            this.options = this.data.filter(option => {\n                for (const column of this.columns) {\n                    if (option[column.field] && option[column.field].toLowerCase().indexOf(search) > -1) {\n                        return true;\n                    }\n                }\n                return false;\n            });\n            this.cd.markForCheck();\n        });\n        this.selectControl.valueChanges.pipe(map(val => {\n            const previousSelected = this.previousSelected.filter(id => !this.options.some(option => option.id === id));\n            const values = this.enabledData.filter(({ id }) => previousSelected.some(item => item === id));\n            return [...values, ...val];\n        }), map(val => val.map(item => item ? item.id : '')), takeUntil(this.destroyed$)).subscribe(val => {\n            this.onChange(val || null);\n            this.cd.detectChanges();\n        });\n    }\n    writeValue(val) {\n        this.patchSelectControl(val);\n    }\n    patchSelectControl(val) {\n        this.onDataReceived\n            .pipe(take(1))\n            .subscribe(options => {\n            if (!this.disableAllOption) {\n                this.allChecked = Array.isArray(val) && val.length === this.enabledData.length;\n            }\n            const values = coerceArray(val || []);\n            options?.forEach(option => option.state = {\n                checked: values?.includes(option.id),\n            });\n            const value = options.filter(opt => values.includes(opt.id));\n            this.selectControl.patchValue(value, { emitEvent: false });\n            this.cd.detectChanges();\n        });\n    }\n    toggleAll(event) {\n        event?.preventDefault();\n        event?.stopPropagation();\n        this.allChecked = !this.allChecked;\n        this.options.forEach(option => {\n            if (!option.disabled) {\n                option.state.checked = this.allChecked;\n            }\n        });\n        let checkedOptions = this.options.filter(option => option.state.checked);\n        this.selectControl.setValue(checkedOptions);\n    }\n    showSelected() {\n        this.viewSelected = !this.viewSelected;\n        this.previousSelected = this.viewSelected ? this.selectControl.value : [];\n        this.options = this.viewSelected ? this.data.filter(option => {\n            return option.state.checked;\n        }) : this.data;\n        this.cd.markForCheck();\n    }\n    onSelectMultiple(event, option) {\n        event.preventDefault();\n        event.stopPropagation();\n        if (option.state) {\n            option.state.checked = !option.state.checked;\n        }\n        this.selectControl.patchValue(this.data?.filter(item => item.state?.checked));\n    }\n    stopPropagation(event) {\n        event.stopPropagation();\n    }\n    get enabledData() {\n        return this.data.filter(({ disabled }) => !disabled);\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched() {\n    }\n    setDisabledState(isDisabled) {\n        this._disabled = isDisabled;\n        isDisabled ? this.searchControl.disable() : this.searchControl.enable();\n    }\n    static { this.ctorParameters = () => [\n        { type: ChangeDetectorRef }\n    ]; }\n    static { this.propDecorators = {\n        searchPlaceholder: [{ type: Input }],\n        showSearch: [{ type: Input }],\n        startSearchLength: [{ type: Input }],\n        disableEmptyOption: [{ type: Input }],\n        disableAllOption: [{ type: Input }],\n        columns: [{ type: Input }],\n        rowsNumber: [{ type: Input }],\n        loading: [{ type: Input }],\n        data: [{ type: Input }],\n        disabled: [{ type: Input }],\n        value: [{ type: Input }],\n        trigger: [{ type: ViewChild, args: ['trigger',] }],\n        searchRef: [{ type: ViewChild, args: ['search',] }],\n        virtualScroll: [{ type: ViewChild, args: [CdkVirtualScrollViewport, { static: false },] }],\n        id: [{ type: HostBinding }]\n    }; }\n};\nSwuiSelectTableComponent = __decorate([\n    Component({\n        selector: 'lib-swui-select-table',\n        template: __NG_CLI_RESOURCE__0,\n        providers: [\n            {\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => SwuiSelectTableComponent),\n                multi: true\n            }\n        ],\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiSelectTableComponent);\nexport { SwuiSelectTableComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,wBAAwB,QAAQ,wBAAwB;AACjE,SAASC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AAChI,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,gBAAgB;AACtE,SAASC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC7C,SAASC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAC7D,MAAMC,YAAY,GAAG,uBAAuB;AAC5C,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,wBAAwB,IAAAC,yBAAA,GAAG,MAAMD,wBAAwB,CAAC;EAC1D,IAAIE,IAAIA,CAACC,GAAG,EAAE;IAAA,IAAAC,aAAA;IACV,IAAI,CAACC,KAAK,GAAGF,GAAG,IAAI,EAAE;IACtB,IAAI,CAACG,OAAO,GAAG,IAAI,CAACD,KAAK;IACzB,IAAI,CAAC,IAAI,CAACE,gBAAgB,EAAE;MAAA,IAAAC,WAAA;MACxB,IAAI,CAACC,UAAU,GAAG,EAAAD,WAAA,OAAI,CAACE,KAAK,cAAAF,WAAA,uBAAVA,WAAA,CAAYG,MAAM,MAAK,IAAI,CAACC,WAAW,CAACD,MAAM;IACpE;IACA,CAAAP,aAAA,OAAI,CAACE,OAAO,cAAAF,aAAA,eAAZA,aAAA,CAAcS,OAAO,CAACC,MAAM;MAAA,IAAAC,YAAA;MAAA,OAAID,MAAM,CAACE,KAAK,GAAG;QAC3CC,OAAO,GAAAF,YAAA,GAAE,IAAI,CAACL,KAAK,cAAAK,YAAA,uBAAVA,YAAA,CAAYG,QAAQ,CAACJ,MAAM,CAACK,EAAE;MAC3C,CAAC;IAAA,EAAC;IACF,IAAIC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACf,OAAO,CAAC,IAAI,IAAI,CAACA,OAAO,CAACK,MAAM,EAAE;MACpD,IAAI,CAACW,cAAc,CAACC,IAAI,CAAC,IAAI,CAACjB,OAAO,CAAC;IAC1C;EACJ;EACA,IAAIJ,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACG,KAAK;EACrB;EACA,IAAImB,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACC,gBAAgB,CAACD,QAAQ,CAAC;EACnC;EACA,IAAIA,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACE,SAAS;EACzB;EACA,IAAIhB,KAAKA,CAACP,GAAG,EAAE;IACX,IAAI,CAACwB,kBAAkB,CAACxB,GAAG,CAAC;EAChC;EACA,IAAIO,KAAKA,CAAA,EAAG;IAAA,IAAAkB,qBAAA;IACR,QAAAA,qBAAA,GAAO,IAAI,CAACC,aAAa,CAACnB,KAAK,cAAAkB,qBAAA,uBAAxBA,qBAAA,CAA0BjC,GAAG,CAAEmC,CAAC,IAAKA,CAAC,CAACX,EAAE,CAAC;EACrD;EACA,IAAIY,KAAKA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACR,OAAO,GAAAA,sBAAA,GAAC,IAAI,CAACH,aAAa,CAACnB,KAAK,cAAAsB,sBAAA,eAAxBA,sBAAA,CAA0BrB,MAAM;EAC5C;EACA,IAAIsB,cAAcA,CAAA,EAAG;IACjB,IAAItB,MAAM,GAAG,IAAI,CAACT,IAAI,CAACS,MAAM;IAC7BA,MAAM,GAAGuB,IAAI,CAACC,KAAK,CAACxB,MAAM,GAAG,IAAI,CAACyB,UAAU,CAAC,IAAI,IAAI,CAACA,UAAU,IAAIzB,MAAM,GAAG,IAAI,CAACyB,UAAU;IAC5F,OAAOzB,MAAM,GAAG,IAAI,CAAC0B,UAAU;EACnC;EACAC,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,iBAAiB,GAAG,QAAQ;IACjC,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACpC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACqC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACR,UAAU,GAAG,CAAC;IACnB,IAAI,CAACS,OAAO,GAAG,KAAK;IACpB,IAAI,CAACpC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACqC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACxC,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC+B,UAAU,GAAG,EAAE;IACpB,IAAI,CAACf,cAAc,GAAG,IAAI9B,aAAa,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACuD,WAAW,GAAGjD,YAAY;IAC/B,IAAI,CAACkD,aAAa,GAAG,IAAI1D,kBAAkB,CAAC,EAAE,CAAC;IAC/C,IAAI,CAACuC,aAAa,GAAG,IAAIvC,kBAAkB,CAAC,CAAC;IAC7C,IAAI,CAAC2D,UAAU,GAAG,IAAIxD,OAAO,CAAC,CAAC;IAC/B,IAAI,CAAC0B,EAAE,GAAG,GAAGrB,YAAY,IAAIC,YAAY,EAAE,EAAE;IAC7C,IAAI,CAACM,KAAK,GAAG,EAAE;IACf,IAAI,CAACqB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACwB,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,QAAQ,GAAIC,CAAC,IAAK,CACvB,CAAC;EACL;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,aAAa,CAACM,YAAY,CAACC,IAAI,CAAC7D,MAAM,CAACQ,IAAI,IAAI,IAAI,CAACuC,UAAU,KAAK,CAAAvC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,MAAM,MAAK,IAAI,CAAC+B,iBAAiB,IAAI,CAAC,CAAC,IAAI,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,MAAM,MAAK,CAAC,CAAC,CAAC,EAAEhB,GAAG,CAAC6D,YAAY,IAAIA,YAAY,CAACC,WAAW,CAAC,CAAC,CAAC,EAAE5D,SAAS,CAAC,IAAI,CAACoD,UAAU,CAAC,CAAC,CAACS,SAAS,CAACC,MAAM,IAAI;MAC5O,IAAI,CAACT,gBAAgB,GAAGS,MAAM,GAAG,IAAI,CAAC9B,aAAa,CAACnB,KAAK,GAAG,EAAE;MAC9D,IAAI,CAACJ,OAAO,GAAG,IAAI,CAACJ,IAAI,CAACR,MAAM,CAACoB,MAAM,IAAI;QACtC,KAAK,MAAM8C,MAAM,IAAI,IAAI,CAAChB,OAAO,EAAE;UAC/B,IAAI9B,MAAM,CAAC8C,MAAM,CAACC,KAAK,CAAC,IAAI/C,MAAM,CAAC8C,MAAM,CAACC,KAAK,CAAC,CAACJ,WAAW,CAAC,CAAC,CAACK,OAAO,CAACH,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;YACjF,OAAO,IAAI;UACf;QACJ;QACA,OAAO,KAAK;MAChB,CAAC,CAAC;MACF,IAAI,CAACpB,EAAE,CAACwB,YAAY,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF,IAAI,CAAClC,aAAa,CAACyB,YAAY,CAACC,IAAI,CAAC5D,GAAG,CAACQ,GAAG,IAAI;MAC5C,MAAM+C,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACxD,MAAM,CAACyB,EAAE,IAAI,CAAC,IAAI,CAACb,OAAO,CAAC0D,IAAI,CAAClD,MAAM,IAAIA,MAAM,CAACK,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC3G,MAAM8C,MAAM,GAAG,IAAI,CAACrD,WAAW,CAAClB,MAAM,CAAC,CAAC;QAAEyB;MAAG,CAAC,KAAK+B,gBAAgB,CAACc,IAAI,CAACE,IAAI,IAAIA,IAAI,KAAK/C,EAAE,CAAC,CAAC;MAC9F,OAAO,CAAC,GAAG8C,MAAM,EAAE,GAAG9D,GAAG,CAAC;IAC9B,CAAC,CAAC,EAAER,GAAG,CAACQ,GAAG,IAAIA,GAAG,CAACR,GAAG,CAACuE,IAAI,IAAIA,IAAI,GAAGA,IAAI,CAAC/C,EAAE,GAAG,EAAE,CAAC,CAAC,EAAEtB,SAAS,CAAC,IAAI,CAACoD,UAAU,CAAC,CAAC,CAACS,SAAS,CAACvD,GAAG,IAAI;MAC/F,IAAI,CAACgD,QAAQ,CAAChD,GAAG,IAAI,IAAI,CAAC;MAC1B,IAAI,CAACoC,EAAE,CAAC4B,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAC,UAAUA,CAACjE,GAAG,EAAE;IACZ,IAAI,CAACwB,kBAAkB,CAACxB,GAAG,CAAC;EAChC;EACAwB,kBAAkBA,CAACxB,GAAG,EAAE;IACpB,IAAI,CAACmB,cAAc,CACdiC,IAAI,CAAC3D,IAAI,CAAC,CAAC,CAAC,CAAC,CACb8D,SAAS,CAACpD,OAAO,IAAI;MACtB,IAAI,CAAC,IAAI,CAACC,gBAAgB,EAAE;QACxB,IAAI,CAACE,UAAU,GAAGW,KAAK,CAACC,OAAO,CAAClB,GAAG,CAAC,IAAIA,GAAG,CAACQ,MAAM,KAAK,IAAI,CAACC,WAAW,CAACD,MAAM;MAClF;MACA,MAAMsD,MAAM,GAAGpF,WAAW,CAACsB,GAAG,IAAI,EAAE,CAAC;MACrCG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEO,OAAO,CAACC,MAAM,IAAIA,MAAM,CAACE,KAAK,GAAG;QACtCC,OAAO,EAAEgD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE/C,QAAQ,CAACJ,MAAM,CAACK,EAAE;MACvC,CAAC,CAAC;MACF,MAAMT,KAAK,GAAGJ,OAAO,CAACZ,MAAM,CAAC2E,GAAG,IAAIJ,MAAM,CAAC/C,QAAQ,CAACmD,GAAG,CAAClD,EAAE,CAAC,CAAC;MAC5D,IAAI,CAACU,aAAa,CAACyC,UAAU,CAAC5D,KAAK,EAAE;QAAE6D,SAAS,EAAE;MAAM,CAAC,CAAC;MAC1D,IAAI,CAAChC,EAAE,CAAC4B,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAK,SAASA,CAACC,KAAK,EAAE;IACbA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,cAAc,CAAC,CAAC;IACvBD,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEE,eAAe,CAAC,CAAC;IACxB,IAAI,CAAClE,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACH,OAAO,CAACO,OAAO,CAACC,MAAM,IAAI;MAC3B,IAAI,CAACA,MAAM,CAACU,QAAQ,EAAE;QAClBV,MAAM,CAACE,KAAK,CAACC,OAAO,GAAG,IAAI,CAACR,UAAU;MAC1C;IACJ,CAAC,CAAC;IACF,IAAImE,cAAc,GAAG,IAAI,CAACtE,OAAO,CAACZ,MAAM,CAACoB,MAAM,IAAIA,MAAM,CAACE,KAAK,CAACC,OAAO,CAAC;IACxE,IAAI,CAACY,aAAa,CAACgD,QAAQ,CAACD,cAAc,CAAC;EAC/C;EACAE,YAAYA,CAAA,EAAG;IACX,IAAI,CAAChC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAACI,gBAAgB,GAAG,IAAI,CAACJ,YAAY,GAAG,IAAI,CAACjB,aAAa,CAACnB,KAAK,GAAG,EAAE;IACzE,IAAI,CAACJ,OAAO,GAAG,IAAI,CAACwC,YAAY,GAAG,IAAI,CAAC5C,IAAI,CAACR,MAAM,CAACoB,MAAM,IAAI;MAC1D,OAAOA,MAAM,CAACE,KAAK,CAACC,OAAO;IAC/B,CAAC,CAAC,GAAG,IAAI,CAACf,IAAI;IACd,IAAI,CAACqC,EAAE,CAACwB,YAAY,CAAC,CAAC;EAC1B;EACAgB,gBAAgBA,CAACN,KAAK,EAAE3D,MAAM,EAAE;IAAA,IAAAkE,UAAA;IAC5BP,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACvB,IAAI7D,MAAM,CAACE,KAAK,EAAE;MACdF,MAAM,CAACE,KAAK,CAACC,OAAO,GAAG,CAACH,MAAM,CAACE,KAAK,CAACC,OAAO;IAChD;IACA,IAAI,CAACY,aAAa,CAACyC,UAAU,EAAAU,UAAA,GAAC,IAAI,CAAC9E,IAAI,cAAA8E,UAAA,uBAATA,UAAA,CAAWtF,MAAM,CAACwE,IAAI;MAAA,IAAAe,WAAA;MAAA,QAAAA,WAAA,GAAIf,IAAI,CAAClD,KAAK,cAAAiE,WAAA,uBAAVA,WAAA,CAAYhE,OAAO;IAAA,EAAC,CAAC;EACjF;EACA0D,eAAeA,CAACF,KAAK,EAAE;IACnBA,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA,IAAI/D,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACV,IAAI,CAACR,MAAM,CAAC,CAAC;MAAE8B;IAAS,CAAC,KAAK,CAACA,QAAQ,CAAC;EACxD;EACA0D,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAChC,QAAQ,GAAGgC,EAAE;EACtB;EACAC,iBAAiBA,CAAA,EAAG,CACpB;EACA3D,gBAAgBA,CAAC4D,UAAU,EAAE;IACzB,IAAI,CAAC3D,SAAS,GAAG2D,UAAU;IAC3BA,UAAU,GAAG,IAAI,CAACrC,aAAa,CAACsC,OAAO,CAAC,CAAC,GAAG,IAAI,CAACtC,aAAa,CAACuC,MAAM,CAAC,CAAC;EAC3E;AAqBJ,CAAC,EApBYtF,yBAAA,CAAKuF,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEzG;AAAkB,CAAC,CAC9B,EACQiB,yBAAA,CAAKyF,cAAc,GAAG;EAC3BlD,iBAAiB,EAAE,CAAC;IAAEiD,IAAI,EAAErG;EAAM,CAAC,CAAC;EACpCqD,UAAU,EAAE,CAAC;IAAEgD,IAAI,EAAErG;EAAM,CAAC,CAAC;EAC7BsD,iBAAiB,EAAE,CAAC;IAAE+C,IAAI,EAAErG;EAAM,CAAC,CAAC;EACpCuD,kBAAkB,EAAE,CAAC;IAAE8C,IAAI,EAAErG;EAAM,CAAC,CAAC;EACrCmB,gBAAgB,EAAE,CAAC;IAAEkF,IAAI,EAAErG;EAAM,CAAC,CAAC;EACnCwD,OAAO,EAAE,CAAC;IAAE6C,IAAI,EAAErG;EAAM,CAAC,CAAC;EAC1BgD,UAAU,EAAE,CAAC;IAAEqD,IAAI,EAAErG;EAAM,CAAC,CAAC;EAC7ByD,OAAO,EAAE,CAAC;IAAE4C,IAAI,EAAErG;EAAM,CAAC,CAAC;EAC1Bc,IAAI,EAAE,CAAC;IAAEuF,IAAI,EAAErG;EAAM,CAAC,CAAC;EACvBoC,QAAQ,EAAE,CAAC;IAAEiE,IAAI,EAAErG;EAAM,CAAC,CAAC;EAC3BsB,KAAK,EAAE,CAAC;IAAE+E,IAAI,EAAErG;EAAM,CAAC,CAAC;EACxBuG,OAAO,EAAE,CAAC;IAAEF,IAAI,EAAEpG,SAAS;IAAEuG,IAAI,EAAE,CAAC,SAAS;EAAG,CAAC,CAAC;EAClDC,SAAS,EAAE,CAAC;IAAEJ,IAAI,EAAEpG,SAAS;IAAEuG,IAAI,EAAE,CAAC,QAAQ;EAAG,CAAC,CAAC;EACnDE,aAAa,EAAE,CAAC;IAAEL,IAAI,EAAEpG,SAAS;IAAEuG,IAAI,EAAE,CAAC9G,wBAAwB,EAAE;MAAEiH,MAAM,EAAE;IAAM,CAAC;EAAG,CAAC,CAAC;EAC1F5E,EAAE,EAAE,CAAC;IAAEsE,IAAI,EAAEtG;EAAY,CAAC;AAC9B,CAAC,EAAAc,yBAAA,CACJ;AACDD,wBAAwB,GAAGtB,UAAU,CAAC,CAClCO,SAAS,CAAC;EACN+G,QAAQ,EAAE,uBAAuB;EACjCC,QAAQ,EAAEtH,oBAAoB;EAC9BuH,SAAS,EAAE,CACP;IACIC,OAAO,EAAE5G,iBAAiB;IAC1B6G,WAAW,EAAElH,UAAU,CAAC,MAAMc,wBAAwB,CAAC;IACvDqG,KAAK,EAAE;EACX,CAAC,CACJ;EACDC,eAAe,EAAEvH,uBAAuB,CAACwH,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC7H,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEoB,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}