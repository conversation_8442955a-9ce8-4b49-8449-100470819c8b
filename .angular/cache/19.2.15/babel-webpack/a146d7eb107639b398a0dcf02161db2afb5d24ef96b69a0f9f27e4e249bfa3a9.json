{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MutationObserverFactory, _ContentObserver, _CdkObserveContent, _ObserversModule;\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n}\n_MutationObserverFactory = MutationObserverFactory;\n_defineProperty(MutationObserverFactory, \"\\u0275fac\", function _MutationObserverFactory_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MutationObserverFactory)();\n});\n_defineProperty(MutationObserverFactory, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _MutationObserverFactory,\n  factory: _MutationObserverFactory.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n  constructor() {\n    _defineProperty(this, \"_mutationObserverFactory\", inject(MutationObserverFactory));\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n    _defineProperty(this, \"_observedElements\", new Map());\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n  }\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n      const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(records => {\n        this._ngZone.run(() => {\n          observer.next(records);\n        });\n      });\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  _observeElement(element) {\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._observedElements.has(element)) {\n        const stream = new Subject();\n        const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n        if (observer) {\n          observer.observe(element, {\n            characterData: true,\n            childList: true,\n            subtree: true\n          });\n        }\n        this._observedElements.set(element, {\n          observer,\n          stream,\n          count: 1\n        });\n      } else {\n        this._observedElements.get(element).count++;\n      }\n      return this._observedElements.get(element).stream;\n    });\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n}\n_ContentObserver = ContentObserver;\n_defineProperty(ContentObserver, \"\\u0275fac\", function _ContentObserver_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ContentObserver)();\n});\n_defineProperty(ContentObserver, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _ContentObserver,\n  factory: _ContentObserver.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  constructor() {\n    _defineProperty(this, \"_contentObserver\", inject(ContentObserver));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    /** Event emitted for each change in the element's content. */\n    _defineProperty(this, \"event\", new EventEmitter());\n    _defineProperty(this, \"_disabled\", false);\n    _defineProperty(this, \"_debounce\", void 0);\n    _defineProperty(this, \"_currentSubscription\", null);\n  }\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n    this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n  }\n  _unsubscribe() {\n    var _this$_currentSubscri;\n    (_this$_currentSubscri = this._currentSubscription) === null || _this$_currentSubscri === void 0 || _this$_currentSubscri.unsubscribe();\n  }\n}\n_CdkObserveContent = CdkObserveContent;\n_defineProperty(CdkObserveContent, \"\\u0275fac\", function _CdkObserveContent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkObserveContent)();\n});\n_defineProperty(CdkObserveContent, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkObserveContent,\n  selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n  inputs: {\n    disabled: [2, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n    debounce: \"debounce\"\n  },\n  outputs: {\n    event: \"cdkObserveContent\"\n  },\n  exportAs: [\"cdkObserveContent\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent'\n    }]\n  }], () => [], {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkObserveContentDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\nclass ObserversModule {}\n_ObserversModule = ObserversModule;\n_defineProperty(ObserversModule, \"\\u0275fac\", function _ObserversModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ObserversModule)();\n});\n_defineProperty(ObserversModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _ObserversModule,\n  imports: [CdkObserveContent],\n  exports: [CdkObserveContent]\n}));\n_defineProperty(ObserversModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MutationObserverFactory]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkObserveContent],\n      exports: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n//# sourceMappingURL=observers.mjs.map", "map": {"version": 3, "names": ["i0", "Injectable", "inject", "NgZone", "ElementRef", "EventEmitter", "booleanAttribute", "Directive", "Output", "Input", "NgModule", "Observable", "Subject", "map", "filter", "debounceTime", "c", "coerceNumberProperty", "a", "coerceElement", "shouldIgnoreRecord", "record", "type", "target", "Comment", "i", "addedNodes", "length", "removedNodes", "MutationObserverFactory", "create", "callback", "MutationObserver", "_MutationObserverFactory", "_defineProperty", "_MutationObserverFactory_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "ContentObserver", "constructor", "Map", "ngOnDestroy", "_observedElements", "for<PERSON>ach", "_", "element", "_cleanupObserver", "observe", "elementOrRef", "observer", "stream", "_observeElement", "subscription", "pipe", "records", "subscribe", "_ngZone", "run", "next", "unsubscribe", "_unobserveElement", "runOutsideAngular", "has", "_mutationObserverFactory", "mutations", "characterData", "childList", "subtree", "set", "count", "get", "disconnect", "complete", "delete", "_ContentObserver", "_ContentObserver_Factory", "CdkObserveContent", "disabled", "_disabled", "value", "_unsubscribe", "_subscribe", "debounce", "_debounce", "ngAfterContentInit", "_currentSubscription", "_contentObserver", "_elementRef", "event", "_this$_currentSubscri", "_CdkObserveContent", "_CdkObserveContent_Factory", "ɵɵdefineDirective", "selectors", "inputs", "outputs", "exportAs", "selector", "alias", "transform", "ObserversModule", "_ObserversModule", "_ObserversModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/observers.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n    // Ignore changes to comment text.\n    if (record.type === 'characterData' && record.target instanceof Comment) {\n        return true;\n    }\n    // Ignore addition / removal of comments.\n    if (record.type === 'childList') {\n        for (let i = 0; i < record.addedNodes.length; i++) {\n            if (!(record.addedNodes[i] instanceof Comment)) {\n                return false;\n            }\n        }\n        for (let i = 0; i < record.removedNodes.length; i++) {\n            if (!(record.removedNodes[i] instanceof Comment)) {\n                return false;\n            }\n        }\n        return true;\n    }\n    // Observe everything else.\n    return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n    create(callback) {\n        return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MutationObserverFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MutationObserverFactory, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MutationObserverFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n    _mutationObserverFactory = inject(MutationObserverFactory);\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n    _observedElements = new Map();\n    _ngZone = inject(NgZone);\n    constructor() { }\n    ngOnDestroy() {\n        this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        return new Observable((observer) => {\n            const stream = this._observeElement(element);\n            const subscription = stream\n                .pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length))\n                .subscribe(records => {\n                this._ngZone.run(() => {\n                    observer.next(records);\n                });\n            });\n            return () => {\n                subscription.unsubscribe();\n                this._unobserveElement(element);\n            };\n        });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._observedElements.has(element)) {\n                const stream = new Subject();\n                const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n                if (observer) {\n                    observer.observe(element, {\n                        characterData: true,\n                        childList: true,\n                        subtree: true,\n                    });\n                }\n                this._observedElements.set(element, { observer, stream, count: 1 });\n            }\n            else {\n                this._observedElements.get(element).count++;\n            }\n            return this._observedElements.get(element).stream;\n        });\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n        if (this._observedElements.has(element)) {\n            this._observedElements.get(element).count--;\n            if (!this._observedElements.get(element).count) {\n                this._cleanupObserver(element);\n            }\n        }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n        if (this._observedElements.has(element)) {\n            const { observer, stream } = this._observedElements.get(element);\n            if (observer) {\n                observer.disconnect();\n            }\n            stream.complete();\n            this._observedElements.delete(element);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ContentObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ContentObserver, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ContentObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n    _contentObserver = inject(ContentObserver);\n    _elementRef = inject(ElementRef);\n    /** Event emitted for each change in the element's content. */\n    event = new EventEmitter();\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    _disabled = false;\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n        return this._debounce;\n    }\n    set debounce(value) {\n        this._debounce = coerceNumberProperty(value);\n        this._subscribe();\n    }\n    _debounce;\n    _currentSubscription = null;\n    constructor() { }\n    ngAfterContentInit() {\n        if (!this._currentSubscription && !this.disabled) {\n            this._subscribe();\n        }\n    }\n    ngOnDestroy() {\n        this._unsubscribe();\n    }\n    _subscribe() {\n        this._unsubscribe();\n        const stream = this._contentObserver.observe(this._elementRef);\n        this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n    }\n    _unsubscribe() {\n        this._currentSubscription?.unsubscribe();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkObserveContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkObserveContent, isStandalone: true, selector: \"[cdkObserveContent]\", inputs: { disabled: [\"cdkObserveContentDisabled\", \"disabled\", booleanAttribute], debounce: \"debounce\" }, outputs: { event: \"cdkObserveContent\" }, exportAs: [\"cdkObserveContent\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkObserveContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkObserveContent]',\n                    exportAs: 'cdkObserveContent',\n                }]\n        }], ctorParameters: () => [], propDecorators: { event: [{\n                type: Output,\n                args: ['cdkObserveContent']\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'cdkObserveContentDisabled', transform: booleanAttribute }]\n            }], debounce: [{\n                type: Input\n            }] } });\nclass ObserversModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ObserversModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: ObserversModule, imports: [CdkObserveContent], exports: [CdkObserveContent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ObserversModule, providers: [MutationObserverFactory] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ObserversModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkObserveContent],\n                    exports: [CdkObserveContent],\n                    providers: [MutationObserverFactory],\n                }]\n        }] });\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n//# sourceMappingURL=observers.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC1I,SAASC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AAC1C,SAASC,GAAG,EAAEC,MAAM,EAAEC,YAAY,QAAQ,gBAAgB;AAC1D,SAASC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,aAAa,QAAQ,wBAAwB;;AAEtF;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAChC;EACA,IAAIA,MAAM,CAACC,IAAI,KAAK,eAAe,IAAID,MAAM,CAACE,MAAM,YAAYC,OAAO,EAAE;IACrE,OAAO,IAAI;EACf;EACA;EACA,IAAIH,MAAM,CAACC,IAAI,KAAK,WAAW,EAAE;IAC7B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,UAAU,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC/C,IAAI,EAAEJ,MAAM,CAACK,UAAU,CAACD,CAAC,CAAC,YAAYD,OAAO,CAAC,EAAE;QAC5C,OAAO,KAAK;MAChB;IACJ;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACO,YAAY,CAACD,MAAM,EAAEF,CAAC,EAAE,EAAE;MACjD,IAAI,EAAEJ,MAAM,CAACO,YAAY,CAACH,CAAC,CAAC,YAAYD,OAAO,CAAC,EAAE;QAC9C,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,MAAMK,uBAAuB,CAAC;EAC1BC,MAAMA,CAACC,QAAQ,EAAE;IACb,OAAO,OAAOC,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAIA,gBAAgB,CAACD,QAAQ,CAAC;EAC1F;AAGJ;AAACE,wBAAA,GANKJ,uBAAuB;AAAAK,eAAA,CAAvBL,uBAAuB,wBAAAM,iCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAI0EP,wBAAuB;AAAA;AAAAK,eAAA,CAJxHL,uBAAuB,+BAOoD7B,EAAE,CAAAqC,kBAAA;EAAAC,KAAA,EAFwBT,wBAAuB;EAAAU,OAAA,EAAvBV,wBAAuB,CAAAW,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEtJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF1C,EAAE,CAAA2C,iBAAA,CAAQd,uBAAuB,EAAc,CAAC;IACrHP,IAAI,EAAErB,UAAU;IAChB2C,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,MAAMI,eAAe,CAAC;EAKlBC,WAAWA,CAAA,EAAG;IAAAZ,eAAA,mCAJahC,MAAM,CAAC2B,uBAAuB,CAAC;IAC1D;IAAAK,eAAA,4BACoB,IAAIa,GAAG,CAAC,CAAC;IAAAb,eAAA,kBACnBhC,MAAM,CAACC,MAAM,CAAC;EACR;EAChB6C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,CAACC,CAAC,EAAEC,OAAO,KAAK,IAAI,CAACC,gBAAgB,CAACD,OAAO,CAAC,CAAC;EAClF;EACAE,OAAOA,CAACC,YAAY,EAAE;IAClB,MAAMH,OAAO,GAAGjC,aAAa,CAACoC,YAAY,CAAC;IAC3C,OAAO,IAAI5C,UAAU,CAAE6C,QAAQ,IAAK;MAChC,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe,CAACN,OAAO,CAAC;MAC5C,MAAMO,YAAY,GAAGF,MAAM,CACtBG,IAAI,CAAC/C,GAAG,CAACgD,OAAO,IAAIA,OAAO,CAAC/C,MAAM,CAACO,MAAM,IAAI,CAACD,kBAAkB,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,MAAM,CAAC+C,OAAO,IAAI,CAAC,CAACA,OAAO,CAAClC,MAAM,CAAC,CAAC,CAChHmC,SAAS,CAACD,OAAO,IAAI;QACtB,IAAI,CAACE,OAAO,CAACC,GAAG,CAAC,MAAM;UACnBR,QAAQ,CAACS,IAAI,CAACJ,OAAO,CAAC;QAC1B,CAAC,CAAC;MACN,CAAC,CAAC;MACF,OAAO,MAAM;QACTF,YAAY,CAACO,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACC,iBAAiB,CAACf,OAAO,CAAC;MACnC,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIM,eAAeA,CAACN,OAAO,EAAE;IACrB,OAAO,IAAI,CAACW,OAAO,CAACK,iBAAiB,CAAC,MAAM;MACxC,IAAI,CAAC,IAAI,CAACnB,iBAAiB,CAACoB,GAAG,CAACjB,OAAO,CAAC,EAAE;QACtC,MAAMK,MAAM,GAAG,IAAI7C,OAAO,CAAC,CAAC;QAC5B,MAAM4C,QAAQ,GAAG,IAAI,CAACc,wBAAwB,CAACxC,MAAM,CAACyC,SAAS,IAAId,MAAM,CAACQ,IAAI,CAACM,SAAS,CAAC,CAAC;QAC1F,IAAIf,QAAQ,EAAE;UACVA,QAAQ,CAACF,OAAO,CAACF,OAAO,EAAE;YACtBoB,aAAa,EAAE,IAAI;YACnBC,SAAS,EAAE,IAAI;YACfC,OAAO,EAAE;UACb,CAAC,CAAC;QACN;QACA,IAAI,CAACzB,iBAAiB,CAAC0B,GAAG,CAACvB,OAAO,EAAE;UAAEI,QAAQ;UAAEC,MAAM;UAAEmB,KAAK,EAAE;QAAE,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAAC3B,iBAAiB,CAAC4B,GAAG,CAACzB,OAAO,CAAC,CAACwB,KAAK,EAAE;MAC/C;MACA,OAAO,IAAI,CAAC3B,iBAAiB,CAAC4B,GAAG,CAACzB,OAAO,CAAC,CAACK,MAAM;IACrD,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIU,iBAAiBA,CAACf,OAAO,EAAE;IACvB,IAAI,IAAI,CAACH,iBAAiB,CAACoB,GAAG,CAACjB,OAAO,CAAC,EAAE;MACrC,IAAI,CAACH,iBAAiB,CAAC4B,GAAG,CAACzB,OAAO,CAAC,CAACwB,KAAK,EAAE;MAC3C,IAAI,CAAC,IAAI,CAAC3B,iBAAiB,CAAC4B,GAAG,CAACzB,OAAO,CAAC,CAACwB,KAAK,EAAE;QAC5C,IAAI,CAACvB,gBAAgB,CAACD,OAAO,CAAC;MAClC;IACJ;EACJ;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAACH,iBAAiB,CAACoB,GAAG,CAACjB,OAAO,CAAC,EAAE;MACrC,MAAM;QAAEI,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACR,iBAAiB,CAAC4B,GAAG,CAACzB,OAAO,CAAC;MAChE,IAAII,QAAQ,EAAE;QACVA,QAAQ,CAACsB,UAAU,CAAC,CAAC;MACzB;MACArB,MAAM,CAACsB,QAAQ,CAAC,CAAC;MACjB,IAAI,CAAC9B,iBAAiB,CAAC+B,MAAM,CAAC5B,OAAO,CAAC;IAC1C;EACJ;AAGJ;AAAC6B,gBAAA,GA3EKpC,eAAe;AAAAX,eAAA,CAAfW,eAAe,wBAAAqC,yBAAA9C,iBAAA;EAAA,YAAAA,iBAAA,IAyEkFS,gBAAe;AAAA;AAAAX,eAAA,CAzEhHW,eAAe,+BAL4D7C,EAAE,CAAAqC,kBAAA;EAAAC,KAAA,EA+EwBO,gBAAe;EAAAN,OAAA,EAAfM,gBAAe,CAAAL,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE9I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjFiF1C,EAAE,CAAA2C,iBAAA,CAiFQE,eAAe,EAAc,CAAC;IAC7GvB,IAAI,EAAErB,UAAU;IAChB2C,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAM0C,iBAAiB,CAAC;EAKpB;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG,IAAI,CAACE,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;EAC5D;EAEA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACH,KAAK,EAAE;IAChB,IAAI,CAACI,SAAS,GAAGzE,oBAAoB,CAACqE,KAAK,CAAC;IAC5C,IAAI,CAACE,UAAU,CAAC,CAAC;EACrB;EAGA1C,WAAWA,CAAA,EAAG;IAAAZ,eAAA,2BA1BKhC,MAAM,CAAC2C,eAAe,CAAC;IAAAX,eAAA,sBAC5BhC,MAAM,CAACE,UAAU,CAAC;IAChC;IAAA8B,eAAA,gBACQ,IAAI7B,YAAY,CAAC,CAAC;IAAA6B,eAAA,oBAYd,KAAK;IAAAA,eAAA;IAAAA,eAAA,+BAUM,IAAI;EACX;EAChByD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACC,oBAAoB,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE;MAC9C,IAAI,CAACI,UAAU,CAAC,CAAC;IACrB;EACJ;EACAxC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuC,YAAY,CAAC,CAAC;EACvB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACD,YAAY,CAAC,CAAC;IACnB,MAAM9B,MAAM,GAAG,IAAI,CAACoC,gBAAgB,CAACvC,OAAO,CAAC,IAAI,CAACwC,WAAW,CAAC;IAC9D,IAAI,CAACF,oBAAoB,GAAG,CAAC,IAAI,CAACH,QAAQ,GAAGhC,MAAM,CAACG,IAAI,CAAC7C,YAAY,CAAC,IAAI,CAAC0E,QAAQ,CAAC,CAAC,GAAGhC,MAAM,EAAEK,SAAS,CAAC,IAAI,CAACiC,KAAK,CAAC;EACzH;EACAR,YAAYA,CAAA,EAAG;IAAA,IAAAS,qBAAA;IACX,CAAAA,qBAAA,OAAI,CAACJ,oBAAoB,cAAAI,qBAAA,eAAzBA,qBAAA,CAA2B9B,WAAW,CAAC,CAAC;EAC5C;AAGJ;AAAC+B,kBAAA,GA9CKd,iBAAiB;AAAAjD,eAAA,CAAjBiD,iBAAiB,wBAAAe,2BAAA9D,iBAAA;EAAA,YAAAA,iBAAA,IA4CgF+C,kBAAiB;AAAA;AAAAjD,eAAA,CA5ClHiD,iBAAiB,8BAzF0DnF,EAAE,CAAAmG,iBAAA;EAAA7E,IAAA,EAsIQ6D,kBAAiB;EAAAiB,SAAA;EAAAC,MAAA;IAAAjB,QAAA,+CAAqH9E,gBAAgB;IAAAmF,QAAA;EAAA;EAAAa,OAAA;IAAAP,KAAA;EAAA;EAAAQ,QAAA;AAAA;AAEjP;EAAA,QAAA7D,SAAA,oBAAAA,SAAA,KAxIiF1C,EAAE,CAAA2C,iBAAA,CAwIQwC,iBAAiB,EAAc,CAAC;IAC/G7D,IAAI,EAAEf,SAAS;IACfqC,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,qBAAqB;MAC/BD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAER,KAAK,EAAE,CAAC;MAChDzE,IAAI,EAAEd,MAAM;MACZoC,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEwC,QAAQ,EAAE,CAAC;MACX9D,IAAI,EAAEb,KAAK;MACXmC,IAAI,EAAE,CAAC;QAAE6D,KAAK,EAAE,2BAA2B;QAAEC,SAAS,EAAEpG;MAAiB,CAAC;IAC9E,CAAC,CAAC;IAAEmF,QAAQ,EAAE,CAAC;MACXnE,IAAI,EAAEb;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMkG,eAAe,CAAC;AAIrBC,gBAAA,GAJKD,eAAe;AAAAzE,eAAA,CAAfyE,eAAe,wBAAAE,yBAAAzE,iBAAA;EAAA,YAAAA,iBAAA,IACkFuE,gBAAe;AAAA;AAAAzE,eAAA,CADhHyE,eAAe,8BAvJ4D3G,EAAE,CAAA8G,gBAAA;EAAAxF,IAAA,EAyJqBqF,gBAAe;EAAAI,OAAA,GAAY5B,iBAAiB;EAAA6B,OAAA,GAAa7B,iBAAiB;AAAA;AAAAjD,eAAA,CAF5KyE,eAAe,8BAvJ4D3G,EAAE,CAAAiH,gBAAA;EAAAC,SAAA,EA0JiD,CAACrF,uBAAuB;AAAC;AAE7J;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA5JiF1C,EAAE,CAAA2C,iBAAA,CA4JQgE,eAAe,EAAc,CAAC;IAC7GrF,IAAI,EAAEZ,QAAQ;IACdkC,IAAI,EAAE,CAAC;MACCmE,OAAO,EAAE,CAAC5B,iBAAiB,CAAC;MAC5B6B,OAAO,EAAE,CAAC7B,iBAAiB,CAAC;MAC5B+B,SAAS,EAAE,CAACrF,uBAAuB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASsD,iBAAiB,EAAEtC,eAAe,EAAEhB,uBAAuB,EAAE8E,eAAe;AACrF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}