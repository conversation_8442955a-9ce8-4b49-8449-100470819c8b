{"ast": null, "code": "var _SwuiDateRangeComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-date-range.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-date-range.component.scss?ngResource\";\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { ChangeDetectorRef, Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormBuilder, UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatMenu, MatMenuTrigger } from '@angular/material/menu';\nimport moment from 'moment';\nimport { BehaviorSubject, combineLatest } from 'rxjs';\nimport { distinctUntilChanged, map, takeUntil } from 'rxjs/operators';\nimport { SwuiDatePickerConfigModel } from '../swui-date-picker/swui-date-picker-config.model';\nimport { processInputString } from '../swui-date-picker/swui-date-picker.component';\nimport { CUSTOM_PERIODS, SwuiDateRangeModel } from './swui-date-range.model';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nconst CONTROL_NAME = 'lib-swui-date-range';\nlet nextUniqueId = 0;\nlet SwuiDateRangeComponent = (_SwuiDateRangeComponent = class SwuiDateRangeComponent extends SwuiMatFormFieldControl {\n  set customPeriods(customPeriods) {\n    if (customPeriods) {\n      this._customPeriods = customPeriods;\n    }\n  }\n  get customPeriods() {\n    return this._customPeriods;\n  }\n  set value(val) {\n    this.writeValue(val);\n  }\n  get value() {\n    return this._currentControlValue;\n  }\n  set config(val) {\n    this._config$.next(new SwuiDatePickerConfigModel(val));\n  }\n  get config() {\n    return this._config$.value;\n  }\n  get empty() {\n    return !this.valueControl.value;\n  }\n  get shouldLabelFloat() {\n    return !this.empty;\n  }\n  constructor(fm, elRef, cdr, ngControl, parentFormGroup, errorStateMatcher, fb) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.cdr = cdr;\n    this.minDate = '';\n    this.maxDate = '';\n    this.title = '';\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this.controlType = CONTROL_NAME;\n    this.valueControl = new UntypedFormControl('');\n    this.selectedIndex = 0;\n    this._customPeriods = CUSTOM_PERIODS;\n    this._value$ = new BehaviorSubject(undefined);\n    this._currentControlValue = {\n      from: '',\n      to: ''\n    };\n    this._sourceValue = new SwuiDateRangeModel();\n    this._config$ = new BehaviorSubject(new SwuiDatePickerConfigModel(undefined));\n    this.form = fb.group({\n      from: [],\n      to: []\n    });\n  }\n  ngOnInit() {\n    this.processedMinDate = this.processedMinDate || this.minDate;\n    this.processedMaxDate = this.processedMaxDate || this.maxDate;\n    this.fromControl.valueChanges.pipe(distinctUntilChanged(), takeUntil(this.destroyed$)).subscribe(from => {\n      const fromMoment = from && moment(from);\n      const maxPeriod = this.config.chooseStart ? this.config.maxPeriod && fromMoment && fromMoment.clone().add(1, this.config.maxPeriod).toISOString() : this.config.maxPeriod && fromMoment && fromMoment.clone().add(1, this.config.maxPeriod).subtract(1, 'seconds').toISOString();\n      this.processedMaxDate = this.maxDate ? maxPeriod && moment.min(maxPeriod, moment(this.maxDate)) || this.maxDate : maxPeriod;\n    });\n    this.toControl.valueChanges.pipe(distinctUntilChanged(), takeUntil(this.destroyed$)).subscribe(to => {\n      const toMoment = to && moment(to);\n      let daysOffset = null;\n      if (this.config.maxPeriod === 'month' && toMoment) {\n        const isLastDay = toMoment.daysInMonth() === toMoment.date();\n        let offset = 0;\n        const diff = toMoment.date() - toMoment.clone().subtract(1, this.config.maxPeriod).daysInMonth();\n        if (diff > 0) {\n          offset = diff;\n        }\n        daysOffset = isLastDay ? toMoment.clone().daysInMonth() : toMoment.clone().subtract(1, this.config.maxPeriod).daysInMonth() + offset;\n      }\n      const minMomentPeriod = this.config.chooseStart ? this.config.maxPeriod && toMoment && toMoment.clone() : this.config.maxPeriod && toMoment && toMoment.clone().add(1, 'second');\n      const minPeriod = daysOffset ? this.config.maxPeriod && toMoment && (minMomentPeriod === null || minMomentPeriod === void 0 ? void 0 : minMomentPeriod.subtract(daysOffset, 'day').toISOString()) : this.config.maxPeriod && toMoment && (minMomentPeriod === null || minMomentPeriod === void 0 ? void 0 : minMomentPeriod.subtract(1, this.config.maxPeriod).toISOString());\n      this.processedMinDate = this.minDate ? minPeriod && moment.max(minPeriod, moment(this.minDate)) || this.minDate : minPeriod;\n    });\n    combineLatest([this._value$, this._config$]).pipe(map(([, config]) => {\n      let offset = 0;\n      const val = this._currentControlValue;\n      if (this.oldConfig) {\n        if (this.oldConfig !== config && this.oldConfig.timeZone && this.oldConfig.timeZone !== config.timeZone) {\n          const oldOffset = Number(moment().tz(this.oldConfig.timeZone).utcOffset());\n          const currentOffset = Number(moment().tz(config.timeZone).utcOffset());\n          offset = oldOffset - currentOffset;\n        }\n      }\n      this.oldConfig = config;\n      const from = val && val.from && moment.utc(val.from).isValid() ? val.from : '';\n      const fromFormatted = from && moment(from).add(offset, 'minutes').utc().toISOString();\n      const to = val && val.to && moment.utc(val.to).isValid() ? val.to : '';\n      const toFormatted = to && moment(to).add(offset, 'minutes').milliseconds(0).utc().toISOString();\n      const processed = new SwuiDateRangeModel({\n        from: fromFormatted,\n        to: toFormatted\n      });\n      return {\n        processed,\n        config\n      };\n    }), takeUntil(this.destroyed$)).subscribe(({\n      processed,\n      config\n    }) => {\n      this._sourceValue = processed;\n      this._currentControlValue = processed;\n      this.form.setValue(processed);\n      const {\n        from,\n        to\n      } = processed;\n      const formattedFrom = processInputString(from, config);\n      const formattedTo = processInputString(to, config);\n      const formattedValue = `${formattedFrom}${!!from && !!to ? ' - ' : ''}${formattedTo}`;\n      this.valueControl.setValue(formattedValue);\n      if (this.config.timeZone && from && to) {\n        let fromDate = moment.tz(moment(from), this.config.timeZone);\n        let toDate = moment.tz(moment(to), this.config.timeZone);\n        const currentDate = moment.tz(moment(), this.config.timeZone);\n        fromDate = fromDate.add(fromDate.utcOffset() - currentDate.utcOffset(), 'minutes');\n        toDate = toDate.add(toDate.utcOffset() - currentDate.utcOffset(), 'minutes');\n        this.onChange({\n          from: fromDate.toISOString(),\n          to: toDate.toISOString()\n        });\n      } else {\n        this.onChange(processed);\n      }\n    });\n  }\n  onContainerClick(event) {\n    event.stopPropagation();\n    if (this.elRef && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n    }\n  }\n  writeValue(val) {\n    const {\n      from,\n      to\n    } = val || {};\n    let data = val;\n    if (this.config.timeZone && from && to) {\n      let fromDate = moment.tz(moment(from), this.config.timeZone);\n      let toDate = moment.tz(moment(to), this.config.timeZone);\n      const currentDate = moment.tz(moment(), this.config.timeZone);\n      fromDate = fromDate.add(currentDate.utcOffset() - fromDate.utcOffset(), 'minutes');\n      toDate = toDate.add(currentDate.utcOffset() - toDate.utcOffset(), 'minutes');\n      data = {\n        from: fromDate.toISOString(),\n        to: toDate.toISOString()\n      };\n    }\n    this._currentControlValue = new SwuiDateRangeModel(data);\n    this._value$.next(undefined);\n  }\n  get fromControl() {\n    return this.form.get('from');\n  }\n  get toControl() {\n    return this.form.get('to');\n  }\n  prevent(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  onPeriodSelect(period) {\n    var _this$config, _this$config2;\n    this.form.patchValue(period((_this$config = this.config) === null || _this$config === void 0 ? void 0 : _this$config.timeZone, (_this$config2 = this.config) === null || _this$config2 === void 0 ? void 0 : _this$config2.chooseStart));\n  }\n  clear(event) {\n    event.preventDefault();\n    this.form.setValue({\n      from: '',\n      to: ''\n    });\n  }\n  cancel(event) {\n    event.preventDefault();\n    if (this.menuTriggerRef) {\n      this.menuTriggerRef.closeMenu();\n    }\n  }\n  apply(event) {\n    event.preventDefault();\n    this._currentControlValue = this.form.value;\n    this._value$.next(undefined);\n    if (this.menuTriggerRef) {\n      this.menuTriggerRef.closeMenu();\n    }\n  }\n  onMenuOpen() {\n    if (this.tabsRef) {\n      this.tabsRef.realignInkBar();\n    }\n  }\n  onMenuClose() {\n    this.form.patchValue(this._sourceValue);\n  }\n  onSelectedIndexChange(tabIndex) {\n    this.selectedIndex = tabIndex;\n    this.recalculateMenu();\n  }\n  isSelected(period) {\n    return JSON.stringify(period.fn(this.config.timeZone, this.config.chooseStart)) === JSON.stringify(this.form.value);\n  }\n  onDisabledState(disabled) {\n    disabled ? this.valueControl.disable() : this.valueControl.enable();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n  recalculateMenu() {\n    window.dispatchEvent(new Event('resize'));\n    this.cdr.markForCheck();\n  }\n}, _SwuiDateRangeComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}, {\n  type: UntypedFormBuilder\n}], _SwuiDateRangeComponent.propDecorators = {\n  customPeriods: [{\n    type: Input\n  }],\n  value: [{\n    type: Input\n  }],\n  customClass: [{\n    type: Input\n  }],\n  minDate: [{\n    type: Input\n  }],\n  maxDate: [{\n    type: Input\n  }],\n  config: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }],\n  input: [{\n    type: ViewChild,\n    args: ['input']\n  }],\n  tabsRef: [{\n    type: ViewChild,\n    args: ['tabSet', {\n      static: true\n    }]\n  }],\n  menuTriggerRef: [{\n    type: ViewChild,\n    args: ['date', {\n      read: MatMenuTrigger\n    }]\n  }],\n  matMenu: [{\n    type: ViewChild,\n    args: ['matMenu', {\n      read: MatMenu\n    }]\n  }]\n}, _SwuiDateRangeComponent);\nSwuiDateRangeComponent = __decorate([Component({\n  selector: 'lib-swui-date-range',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiDateRangeComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiDateRangeComponent);\nexport { SwuiDateRangeComponent };", "map": {"version": 3, "names": ["FocusMonitor", "ChangeDetectorRef", "Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormBuilder", "UntypedFormControl", "FormGroupDirective", "NgControl", "MatFormFieldControl", "MatMenu", "MatMenuTrigger", "moment", "BehaviorSubject", "combineLatest", "distinctUntilChanged", "map", "takeUntil", "SwuiDatePickerConfigModel", "processInputString", "CUSTOM_PERIODS", "SwuiDateRangeModel", "SwuiMatFormFieldControl", "ErrorStateMatcher", "CONTROL_NAME", "nextUniqueId", "SwuiDateRangeComponent", "_SwuiDateRangeComponent", "customPeriods", "_customPeriods", "value", "val", "writeValue", "_currentControlValue", "config", "_config$", "next", "empty", "valueControl", "shouldLabelFloat", "constructor", "fm", "elRef", "cdr", "ngControl", "parentFormGroup", "errorStateMatcher", "fb", "minDate", "maxDate", "title", "id", "controlType", "selectedIndex", "_value$", "undefined", "from", "to", "_sourceValue", "form", "group", "ngOnInit", "processedMinDate", "processedMaxDate", "fromControl", "valueChanges", "pipe", "destroyed$", "subscribe", "fromMoment", "max<PERSON><PERSON><PERSON>", "chooseStart", "clone", "add", "toISOString", "subtract", "min", "toControl", "toMoment", "daysOffset", "isLastDay", "daysInMonth", "date", "offset", "diff", "minMomentPeriod", "min<PERSON><PERSON><PERSON>", "max", "oldConfig", "timeZone", "oldOffset", "Number", "tz", "utcOffset", "currentOffset", "utc", "<PERSON><PERSON><PERSON><PERSON>", "fromFormatted", "toFormatted", "milliseconds", "processed", "setValue", "formattedFrom", "formattedTo", "formattedValue", "fromDate", "toDate", "currentDate", "onChange", "onContainerClick", "event", "stopPropagation", "target", "tagName", "toLowerCase", "disabled", "nativeElement", "focus", "data", "get", "prevent", "preventDefault", "onPeriodSelect", "period", "_this$config", "_this$config2", "patchValue", "clear", "cancel", "menuTriggerRef", "closeMenu", "apply", "onMenuOpen", "tabsRef", "realignInkBar", "onMenuClose", "onSelectedIndexChange", "tabIndex", "recalculateMenu", "isSelected", "JSON", "stringify", "fn", "onDisabledState", "disable", "enable", "isErrorState", "input", "errorState", "window", "dispatchEvent", "Event", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "args", "static", "read", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "provide", "useExisting", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-range/swui-date-range.component.ts"], "sourcesContent": ["import { FocusMonitor } from '@angular/cdk/a11y';\nimport {\n  ChangeDetectorRef, Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild\n} from '@angular/core';\nimport { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, FormGroupDirective, NgControl } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatMenu, MatMenuTrigger } from '@angular/material/menu';\nimport { MatTabGroup } from '@angular/material/tabs';\nimport moment from 'moment';\nimport { BehaviorSubject, combineLatest } from 'rxjs';\nimport { distinctUntilChanged, map, takeUntil } from 'rxjs/operators';\nimport { SwuiDatePickerConfig, SwuiDatePickerConfigModel } from '../swui-date-picker/swui-date-picker-config.model';\nimport { processInputString } from '../swui-date-picker/swui-date-picker.component';\n\nimport { CUSTOM_PERIODS, CustomPeriod, SwuiDateRange, SwuiDateRangeModel } from './swui-date-range.model';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\n\n\nconst CONTROL_NAME = 'lib-swui-date-range';\nlet nextUniqueId = 0;\n\n@Component({\n    selector: 'lib-swui-date-range',\n    templateUrl: './swui-date-range.component.html',\n    styleUrls: ['./swui-date-range.component.scss'],\n    providers: [{ provide: MatFormFieldControl, useExisting: SwuiDateRangeComponent }],\n    standalone: false\n})\nexport class SwuiDateRangeComponent extends SwuiMatFormFieldControl<SwuiDateRange> implements OnInit {\n  @Input() set customPeriods(customPeriods: CustomPeriod[]) {\n    if (customPeriods) {\n      this._customPeriods = customPeriods;\n    }\n  }\n\n  get customPeriods(): CustomPeriod[] {\n    return this._customPeriods;\n  }\n\n  @Input()\n  set value( val: SwuiDateRange ) {\n    this.writeValue(val);\n  }\n\n  get value(): SwuiDateRange {\n    return this._currentControlValue;\n  }\n\n  @Input() customClass?: string;\n\n  @Input() minDate = '';\n  @Input() maxDate = '';\n\n  @Input()\n  set config( val: SwuiDatePickerConfig ) {\n    this._config$.next(new SwuiDatePickerConfigModel(val));\n  }\n\n  get config(): SwuiDatePickerConfig {\n    return this._config$.value;\n  }\n\n  @Input() title = '';\n\n  get empty() {\n    return !this.valueControl.value;\n  }\n\n  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;\n  readonly controlType = CONTROL_NAME;\n\n  @HostBinding('class.floating')\n  get shouldLabelFloat() {\n    return !this.empty;\n  }\n\n  @ViewChild('input') input?: MatInput;\n  @ViewChild('tabSet', { static: true }) tabsRef: MatTabGroup | undefined;\n  @ViewChild('date', { read: MatMenuTrigger }) menuTriggerRef: MatMenuTrigger | undefined;\n  @ViewChild('matMenu', { read: MatMenu }) matMenu: MatMenu | undefined;\n\n  readonly valueControl = new UntypedFormControl('');\n  readonly form: UntypedFormGroup;\n  selectedIndex = 0;\n  processedMinDate?: string;\n  processedMaxDate?: string;\n\n  private _customPeriods: CustomPeriod[] = CUSTOM_PERIODS;\n  private readonly _value$ = new BehaviorSubject<void>(undefined);\n  private _currentControlValue = { from: '', to: '' };\n  private _sourceValue: SwuiDateRange = new SwuiDateRangeModel();\n  private readonly _config$ = new BehaviorSubject<SwuiDatePickerConfig>(new SwuiDatePickerConfigModel(undefined));\n  private oldConfig?: SwuiDatePickerConfig;\n\n  constructor( fm: FocusMonitor,\n               elRef: ElementRef<HTMLElement>,\n               private cdr: ChangeDetectorRef,\n               @Optional() @Self() ngControl: NgControl,\n               @Optional() parentFormGroup: FormGroupDirective,\n               errorStateMatcher: ErrorStateMatcher,\n               fb: UntypedFormBuilder\n  ) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.form = fb.group({\n      from: [],\n      to: []\n    });\n  }\n\n  ngOnInit(): void {\n    this.processedMinDate = this.processedMinDate || this.minDate;\n    this.processedMaxDate = this.processedMaxDate || this.maxDate;\n    this.fromControl.valueChanges\n      .pipe(\n        distinctUntilChanged(),\n        takeUntil(this.destroyed$)\n      )\n      .subscribe(from => {\n        const fromMoment = from && moment(from);\n        const maxPeriod = this.config.chooseStart\n          ? this.config.maxPeriod && fromMoment\n          && fromMoment.clone()\n            .add(1, this.config.maxPeriod).toISOString()\n          : this.config.maxPeriod && fromMoment\n          && fromMoment.clone()\n            .add(1, this.config.maxPeriod).subtract(1, 'seconds').toISOString();\n        this.processedMaxDate = this.maxDate\n          ? maxPeriod && moment.min(maxPeriod, moment(this.maxDate)) || this.maxDate\n          : maxPeriod;\n      });\n    this.toControl.valueChanges\n      .pipe(\n        distinctUntilChanged(),\n        takeUntil(this.destroyed$)\n      )\n      .subscribe(to => {\n        const toMoment = to && moment(to);\n        let daysOffset = null;\n\n        if (this.config.maxPeriod === 'month' && toMoment) {\n          const isLastDay = toMoment.daysInMonth() === toMoment.date();\n          let offset = 0;\n          const diff = toMoment.date() - toMoment.clone().subtract(1, this.config.maxPeriod).daysInMonth();\n\n          if (diff > 0) {\n            offset = diff;\n          }\n\n          daysOffset = isLastDay\n            ? toMoment.clone().daysInMonth()\n            : toMoment.clone().subtract(1, this.config.maxPeriod).daysInMonth() + offset;\n        }\n\n        const minMomentPeriod = this.config.chooseStart\n          ? this.config.maxPeriod && toMoment && toMoment.clone()\n          : this.config.maxPeriod && toMoment && toMoment.clone().add(1, 'second');\n        const minPeriod = daysOffset\n          ? this.config.maxPeriod && toMoment && minMomentPeriod?.subtract(daysOffset, 'day').toISOString()\n          : this.config.maxPeriod && toMoment && minMomentPeriod?.subtract(1, this.config.maxPeriod).toISOString();\n\n        this.processedMinDate = this.minDate\n          ? minPeriod && moment.max(minPeriod, moment(this.minDate)) || this.minDate\n          : minPeriod;\n      });\n\n    combineLatest([this._value$, this._config$])\n      .pipe(\n        map(( [, config] ) => {\n          let offset = 0;\n          const val = this._currentControlValue;\n\n          if (this.oldConfig) {\n            if (this.oldConfig !== config && this.oldConfig.timeZone && this.oldConfig.timeZone !== config.timeZone) {\n              const oldOffset = Number(moment().tz(this.oldConfig.timeZone).utcOffset());\n              const currentOffset = Number(moment().tz(config.timeZone as string).utcOffset());\n\n              offset = oldOffset - currentOffset;\n            }\n          }\n\n          this.oldConfig = config;\n\n          const from = val && val.from && moment.utc(val.from).isValid() ? val.from : '';\n          const fromFormatted = from && moment(from).add(offset, 'minutes').utc().toISOString();\n          const to = val && val.to && moment.utc(val.to).isValid() ? val.to : '';\n          const toFormatted = to && moment(to).add(offset, 'minutes').milliseconds(0).utc().toISOString();\n          const processed = new SwuiDateRangeModel({ from: fromFormatted, to: toFormatted });\n          return { processed, config };\n        }),\n        takeUntil(this.destroyed$)\n      )\n      .subscribe(( { processed, config } ) => {\n        this._sourceValue = processed;\n        this._currentControlValue = processed;\n        this.form.setValue(processed);\n\n        const { from, to } = processed;\n        const formattedFrom = processInputString(from, config);\n        const formattedTo = processInputString(to, config);\n        const formattedValue = `${formattedFrom}${!!from && !!to ? ' - ' : ''}${formattedTo}`;\n        this.valueControl.setValue(formattedValue);\n        if (this.config.timeZone && from && to) {\n          let fromDate = moment.tz(moment(from), this.config.timeZone);\n          let toDate = moment.tz(moment(to), this.config.timeZone);\n          const currentDate = moment.tz(moment(), this.config.timeZone);\n\n          fromDate = fromDate.add(fromDate.utcOffset() - currentDate.utcOffset(), 'minutes');\n          toDate = toDate.add(toDate.utcOffset() - currentDate.utcOffset(), 'minutes');\n          this.onChange({ from: fromDate.toISOString(), to: toDate.toISOString() });\n        } else {\n          this.onChange(processed);\n        }\n      });\n  }\n\n  onContainerClick( event: Event ): void {\n    event.stopPropagation();\n    if (this.elRef && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n    }\n  }\n\n  writeValue( val: SwuiDateRange ): void {\n    const { from, to } = val || {};\n    let data = val;\n    if (this.config.timeZone && from && to) {\n      let fromDate = moment.tz(moment(from), this.config.timeZone);\n      let toDate = moment.tz(moment(to), this.config.timeZone);\n      const currentDate = moment.tz(moment(), this.config.timeZone);\n\n      fromDate = fromDate.add(currentDate.utcOffset() - fromDate.utcOffset(), 'minutes');\n      toDate = toDate.add(currentDate.utcOffset() - toDate.utcOffset(), 'minutes');\n      data = { from: fromDate.toISOString(), to: toDate.toISOString() };\n    }\n    this._currentControlValue = new SwuiDateRangeModel(data);\n    this._value$.next(undefined);\n  }\n\n  get fromControl(): UntypedFormControl {\n    return this.form.get('from') as UntypedFormControl;\n  }\n\n  get toControl(): UntypedFormControl {\n    return this.form.get('to') as UntypedFormControl;\n  }\n\n  prevent( event: Event ) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n\n  onPeriodSelect( period: ( timezone?: string, chooseStart?: boolean ) => SwuiDateRange ) {\n    this.form.patchValue(period(this.config?.timeZone, this.config?.chooseStart));\n  }\n\n  clear( event: Event ) {\n    event.preventDefault();\n    this.form.setValue({ from: '', to: '' });\n  }\n\n  cancel( event: Event ) {\n    event.preventDefault();\n    if (this.menuTriggerRef) {\n      this.menuTriggerRef.closeMenu();\n    }\n  }\n\n  apply( event: Event ) {\n    event.preventDefault();\n    this._currentControlValue = this.form.value;\n    this._value$.next(undefined);\n    if (this.menuTriggerRef) {\n      this.menuTriggerRef.closeMenu();\n    }\n  }\n\n  onMenuOpen() {\n    if (this.tabsRef) {\n      this.tabsRef.realignInkBar();\n    }\n  }\n\n  onMenuClose() {\n    this.form.patchValue(this._sourceValue);\n  }\n\n  onSelectedIndexChange( tabIndex: number ) {\n    this.selectedIndex = tabIndex;\n    this.recalculateMenu();\n  }\n\n  isSelected( period: CustomPeriod ): boolean {\n    return JSON.stringify(period.fn(this.config.timeZone, this.config.chooseStart)) === JSON.stringify(this.form.value);\n  }\n\n  protected onDisabledState( disabled: boolean ) {\n    disabled ? this.valueControl.disable() : this.valueControl.enable();\n  }\n\n  protected isErrorState(): boolean {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n\n  private recalculateMenu() {\n    window.dispatchEvent(new Event('resize'));\n    this.cdr.markForCheck();\n  }\n}\n"], "mappings": ";;;;AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SACEC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAUC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAC1F,eAAe;AACtB,SAASC,kBAAkB,EAAEC,kBAAkB,EAAoBC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AACxH,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,OAAO,EAAEC,cAAc,QAAQ,wBAAwB;AAEhE,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,eAAe,EAAEC,aAAa,QAAQ,MAAM;AACrD,SAASC,oBAAoB,EAAEC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AACrE,SAA+BC,yBAAyB,QAAQ,mDAAmD;AACnH,SAASC,kBAAkB,QAAQ,gDAAgD;AAEnF,SAASC,cAAc,EAA+BC,kBAAkB,QAAQ,yBAAyB;AAEzG,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAG1D,MAAMC,YAAY,GAAG,qBAAqB;AAC1C,IAAIC,YAAY,GAAG,CAAC;AASb,IAAMC,sBAAsB,IAAAC,uBAAA,GAA5B,MAAMD,sBAAuB,SAAQJ,uBAAsC;MACnEM,aAAaA,CAACA,aAA6B;IACtD,IAAIA,aAAa,EAAE;MACjB,IAAI,CAACC,cAAc,GAAGD,aAAa;IACrC;EACF;EAEA,IAAIA,aAAaA,CAAA;IACf,OAAO,IAAI,CAACC,cAAc;EAC5B;MAGIC,KAAKA,CAAEC,GAAkB;IAC3B,IAAI,CAACC,UAAU,CAACD,GAAG,CAAC;EACtB;EAEA,IAAID,KAAKA,CAAA;IACP,OAAO,IAAI,CAACG,oBAAoB;EAClC;MAQIC,MAAMA,CAAEH,GAAyB;IACnC,IAAI,CAACI,QAAQ,CAACC,IAAI,CAAC,IAAIlB,yBAAyB,CAACa,GAAG,CAAC,CAAC;EACxD;EAEA,IAAIG,MAAMA,CAAA;IACR,OAAO,IAAI,CAACC,QAAQ,CAACL,KAAK;EAC5B;EAIA,IAAIO,KAAKA,CAAA;IACP,OAAO,CAAC,IAAI,CAACC,YAAY,CAACR,KAAK;EACjC;MAMIS,gBAAgBA,CAAA;IAClB,OAAO,CAAC,IAAI,CAACF,KAAK;EACpB;EAoBAG,YAAaC,EAAgB,EAChBC,KAA8B,EACtBC,GAAsB,EACVC,SAAoB,EAC5BC,eAAmC,EAC/CC,iBAAoC,EACpCC,EAAsB;IAEjC,KAAK,CAACN,EAAE,EAAEC,KAAK,EAAEE,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAN5C,KAAAH,GAAG,GAAHA,GAAG;IA9Cf,KAAAK,OAAO,GAAG,EAAE;IACZ,KAAAC,OAAO,GAAG,EAAE;IAWZ,KAAAC,KAAK,GAAG,EAAE;IAMK,KAAAC,EAAE,GAAG,GAAG3B,YAAY,IAAIC,YAAY,EAAE,EAAE;IACvD,KAAA2B,WAAW,GAAG5B,YAAY;IAY1B,KAAAc,YAAY,GAAG,IAAIhC,kBAAkB,CAAC,EAAE,CAAC;IAElD,KAAA+C,aAAa,GAAG,CAAC;IAIT,KAAAxB,cAAc,GAAmBT,cAAc;IACtC,KAAAkC,OAAO,GAAG,IAAIzC,eAAe,CAAO0C,SAAS,CAAC;IACvD,KAAAtB,oBAAoB,GAAG;MAAEuB,IAAI,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAC3C,KAAAC,YAAY,GAAkB,IAAIrC,kBAAkB,EAAE;IAC7C,KAAAc,QAAQ,GAAG,IAAItB,eAAe,CAAuB,IAAIK,yBAAyB,CAACqC,SAAS,CAAC,CAAC;IAY7G,IAAI,CAACI,IAAI,GAAGZ,EAAE,CAACa,KAAK,CAAC;MACnBJ,IAAI,EAAE,EAAE;MACRC,EAAE,EAAE;KACL,CAAC;EACJ;EAEAI,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACd,OAAO;IAC7D,IAAI,CAACe,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACd,OAAO;IAC7D,IAAI,CAACe,WAAW,CAACC,YAAY,CAC1BC,IAAI,CACHnD,oBAAoB,EAAE,EACtBE,SAAS,CAAC,IAAI,CAACkD,UAAU,CAAC,CAC3B,CACAC,SAAS,CAACZ,IAAI,IAAG;MAChB,MAAMa,UAAU,GAAGb,IAAI,IAAI5C,MAAM,CAAC4C,IAAI,CAAC;MACvC,MAAMc,SAAS,GAAG,IAAI,CAACpC,MAAM,CAACqC,WAAW,GACrC,IAAI,CAACrC,MAAM,CAACoC,SAAS,IAAID,UAAU,IAClCA,UAAU,CAACG,KAAK,EAAE,CAClBC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACvC,MAAM,CAACoC,SAAS,CAAC,CAACI,WAAW,EAAE,GAC5C,IAAI,CAACxC,MAAM,CAACoC,SAAS,IAAID,UAAU,IAClCA,UAAU,CAACG,KAAK,EAAE,CAClBC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACvC,MAAM,CAACoC,SAAS,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,CAACD,WAAW,EAAE;MACvE,IAAI,CAACX,gBAAgB,GAAG,IAAI,CAACd,OAAO,GAChCqB,SAAS,IAAI1D,MAAM,CAACgE,GAAG,CAACN,SAAS,EAAE1D,MAAM,CAAC,IAAI,CAACqC,OAAO,CAAC,CAAC,IAAI,IAAI,CAACA,OAAO,GACxEqB,SAAS;IACf,CAAC,CAAC;IACJ,IAAI,CAACO,SAAS,CAACZ,YAAY,CACxBC,IAAI,CACHnD,oBAAoB,EAAE,EACtBE,SAAS,CAAC,IAAI,CAACkD,UAAU,CAAC,CAC3B,CACAC,SAAS,CAACX,EAAE,IAAG;MACd,MAAMqB,QAAQ,GAAGrB,EAAE,IAAI7C,MAAM,CAAC6C,EAAE,CAAC;MACjC,IAAIsB,UAAU,GAAG,IAAI;MAErB,IAAI,IAAI,CAAC7C,MAAM,CAACoC,SAAS,KAAK,OAAO,IAAIQ,QAAQ,EAAE;QACjD,MAAME,SAAS,GAAGF,QAAQ,CAACG,WAAW,EAAE,KAAKH,QAAQ,CAACI,IAAI,EAAE;QAC5D,IAAIC,MAAM,GAAG,CAAC;QACd,MAAMC,IAAI,GAAGN,QAAQ,CAACI,IAAI,EAAE,GAAGJ,QAAQ,CAACN,KAAK,EAAE,CAACG,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACzC,MAAM,CAACoC,SAAS,CAAC,CAACW,WAAW,EAAE;QAEhG,IAAIG,IAAI,GAAG,CAAC,EAAE;UACZD,MAAM,GAAGC,IAAI;QACf;QAEAL,UAAU,GAAGC,SAAS,GAClBF,QAAQ,CAACN,KAAK,EAAE,CAACS,WAAW,EAAE,GAC9BH,QAAQ,CAACN,KAAK,EAAE,CAACG,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACzC,MAAM,CAACoC,SAAS,CAAC,CAACW,WAAW,EAAE,GAAGE,MAAM;MAChF;MAEA,MAAME,eAAe,GAAG,IAAI,CAACnD,MAAM,CAACqC,WAAW,GAC3C,IAAI,CAACrC,MAAM,CAACoC,SAAS,IAAIQ,QAAQ,IAAIA,QAAQ,CAACN,KAAK,EAAE,GACrD,IAAI,CAACtC,MAAM,CAACoC,SAAS,IAAIQ,QAAQ,IAAIA,QAAQ,CAACN,KAAK,EAAE,CAACC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC;MAC1E,MAAMa,SAAS,GAAGP,UAAU,GACxB,IAAI,CAAC7C,MAAM,CAACoC,SAAS,IAAIQ,QAAQ,KAAIO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEV,QAAQ,CAACI,UAAU,EAAE,KAAK,CAAC,CAACL,WAAW,EAAE,IAC/F,IAAI,CAACxC,MAAM,CAACoC,SAAS,IAAIQ,QAAQ,KAAIO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEV,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACzC,MAAM,CAACoC,SAAS,CAAC,CAACI,WAAW,EAAE;MAE1G,IAAI,CAACZ,gBAAgB,GAAG,IAAI,CAACd,OAAO,GAChCsC,SAAS,IAAI1E,MAAM,CAAC2E,GAAG,CAACD,SAAS,EAAE1E,MAAM,CAAC,IAAI,CAACoC,OAAO,CAAC,CAAC,IAAI,IAAI,CAACA,OAAO,GACxEsC,SAAS;IACf,CAAC,CAAC;IAEJxE,aAAa,CAAC,CAAC,IAAI,CAACwC,OAAO,EAAE,IAAI,CAACnB,QAAQ,CAAC,CAAC,CACzC+B,IAAI,CACHlD,GAAG,CAAC,CAAE,GAAGkB,MAAM,CAAC,KAAK;MACnB,IAAIiD,MAAM,GAAG,CAAC;MACd,MAAMpD,GAAG,GAAG,IAAI,CAACE,oBAAoB;MAErC,IAAI,IAAI,CAACuD,SAAS,EAAE;QAClB,IAAI,IAAI,CAACA,SAAS,KAAKtD,MAAM,IAAI,IAAI,CAACsD,SAAS,CAACC,QAAQ,IAAI,IAAI,CAACD,SAAS,CAACC,QAAQ,KAAKvD,MAAM,CAACuD,QAAQ,EAAE;UACvG,MAAMC,SAAS,GAAGC,MAAM,CAAC/E,MAAM,EAAE,CAACgF,EAAE,CAAC,IAAI,CAACJ,SAAS,CAACC,QAAQ,CAAC,CAACI,SAAS,EAAE,CAAC;UAC1E,MAAMC,aAAa,GAAGH,MAAM,CAAC/E,MAAM,EAAE,CAACgF,EAAE,CAAC1D,MAAM,CAACuD,QAAkB,CAAC,CAACI,SAAS,EAAE,CAAC;UAEhFV,MAAM,GAAGO,SAAS,GAAGI,aAAa;QACpC;MACF;MAEA,IAAI,CAACN,SAAS,GAAGtD,MAAM;MAEvB,MAAMsB,IAAI,GAAGzB,GAAG,IAAIA,GAAG,CAACyB,IAAI,IAAI5C,MAAM,CAACmF,GAAG,CAAChE,GAAG,CAACyB,IAAI,CAAC,CAACwC,OAAO,EAAE,GAAGjE,GAAG,CAACyB,IAAI,GAAG,EAAE;MAC9E,MAAMyC,aAAa,GAAGzC,IAAI,IAAI5C,MAAM,CAAC4C,IAAI,CAAC,CAACiB,GAAG,CAACU,MAAM,EAAE,SAAS,CAAC,CAACY,GAAG,EAAE,CAACrB,WAAW,EAAE;MACrF,MAAMjB,EAAE,GAAG1B,GAAG,IAAIA,GAAG,CAAC0B,EAAE,IAAI7C,MAAM,CAACmF,GAAG,CAAChE,GAAG,CAAC0B,EAAE,CAAC,CAACuC,OAAO,EAAE,GAAGjE,GAAG,CAAC0B,EAAE,GAAG,EAAE;MACtE,MAAMyC,WAAW,GAAGzC,EAAE,IAAI7C,MAAM,CAAC6C,EAAE,CAAC,CAACgB,GAAG,CAACU,MAAM,EAAE,SAAS,CAAC,CAACgB,YAAY,CAAC,CAAC,CAAC,CAACJ,GAAG,EAAE,CAACrB,WAAW,EAAE;MAC/F,MAAM0B,SAAS,GAAG,IAAI/E,kBAAkB,CAAC;QAAEmC,IAAI,EAAEyC,aAAa;QAAExC,EAAE,EAAEyC;MAAW,CAAE,CAAC;MAClF,OAAO;QAAEE,SAAS;QAAElE;MAAM,CAAE;IAC9B,CAAC,CAAC,EACFjB,SAAS,CAAC,IAAI,CAACkD,UAAU,CAAC,CAC3B,CACAC,SAAS,CAAC,CAAE;MAAEgC,SAAS;MAAElE;IAAM,CAAE,KAAK;MACrC,IAAI,CAACwB,YAAY,GAAG0C,SAAS;MAC7B,IAAI,CAACnE,oBAAoB,GAAGmE,SAAS;MACrC,IAAI,CAACzC,IAAI,CAAC0C,QAAQ,CAACD,SAAS,CAAC;MAE7B,MAAM;QAAE5C,IAAI;QAAEC;MAAE,CAAE,GAAG2C,SAAS;MAC9B,MAAME,aAAa,GAAGnF,kBAAkB,CAACqC,IAAI,EAAEtB,MAAM,CAAC;MACtD,MAAMqE,WAAW,GAAGpF,kBAAkB,CAACsC,EAAE,EAAEvB,MAAM,CAAC;MAClD,MAAMsE,cAAc,GAAG,GAAGF,aAAa,GAAG,CAAC,CAAC9C,IAAI,IAAI,CAAC,CAACC,EAAE,GAAG,KAAK,GAAG,EAAE,GAAG8C,WAAW,EAAE;MACrF,IAAI,CAACjE,YAAY,CAAC+D,QAAQ,CAACG,cAAc,CAAC;MAC1C,IAAI,IAAI,CAACtE,MAAM,CAACuD,QAAQ,IAAIjC,IAAI,IAAIC,EAAE,EAAE;QACtC,IAAIgD,QAAQ,GAAG7F,MAAM,CAACgF,EAAE,CAAChF,MAAM,CAAC4C,IAAI,CAAC,EAAE,IAAI,CAACtB,MAAM,CAACuD,QAAQ,CAAC;QAC5D,IAAIiB,MAAM,GAAG9F,MAAM,CAACgF,EAAE,CAAChF,MAAM,CAAC6C,EAAE,CAAC,EAAE,IAAI,CAACvB,MAAM,CAACuD,QAAQ,CAAC;QACxD,MAAMkB,WAAW,GAAG/F,MAAM,CAACgF,EAAE,CAAChF,MAAM,EAAE,EAAE,IAAI,CAACsB,MAAM,CAACuD,QAAQ,CAAC;QAE7DgB,QAAQ,GAAGA,QAAQ,CAAChC,GAAG,CAACgC,QAAQ,CAACZ,SAAS,EAAE,GAAGc,WAAW,CAACd,SAAS,EAAE,EAAE,SAAS,CAAC;QAClFa,MAAM,GAAGA,MAAM,CAACjC,GAAG,CAACiC,MAAM,CAACb,SAAS,EAAE,GAAGc,WAAW,CAACd,SAAS,EAAE,EAAE,SAAS,CAAC;QAC5E,IAAI,CAACe,QAAQ,CAAC;UAAEpD,IAAI,EAAEiD,QAAQ,CAAC/B,WAAW,EAAE;UAAEjB,EAAE,EAAEiD,MAAM,CAAChC,WAAW;QAAE,CAAE,CAAC;MAC3E,CAAC,MAAM;QACL,IAAI,CAACkC,QAAQ,CAACR,SAAS,CAAC;MAC1B;IACF,CAAC,CAAC;EACN;EAEAS,gBAAgBA,CAAEC,KAAY;IAC5BA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,IAAI,CAACrE,KAAK,IAAKoE,KAAK,CAACE,MAAkB,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAC/F,IAAI,CAACzE,KAAK,CAAC0E,aAAa,CAACC,KAAK,EAAE;IAClC;EACF;EAEArF,UAAUA,CAAED,GAAkB;IAC5B,MAAM;MAAEyB,IAAI;MAAEC;IAAE,CAAE,GAAG1B,GAAG,IAAI,EAAE;IAC9B,IAAIuF,IAAI,GAAGvF,GAAG;IACd,IAAI,IAAI,CAACG,MAAM,CAACuD,QAAQ,IAAIjC,IAAI,IAAIC,EAAE,EAAE;MACtC,IAAIgD,QAAQ,GAAG7F,MAAM,CAACgF,EAAE,CAAChF,MAAM,CAAC4C,IAAI,CAAC,EAAE,IAAI,CAACtB,MAAM,CAACuD,QAAQ,CAAC;MAC5D,IAAIiB,MAAM,GAAG9F,MAAM,CAACgF,EAAE,CAAChF,MAAM,CAAC6C,EAAE,CAAC,EAAE,IAAI,CAACvB,MAAM,CAACuD,QAAQ,CAAC;MACxD,MAAMkB,WAAW,GAAG/F,MAAM,CAACgF,EAAE,CAAChF,MAAM,EAAE,EAAE,IAAI,CAACsB,MAAM,CAACuD,QAAQ,CAAC;MAE7DgB,QAAQ,GAAGA,QAAQ,CAAChC,GAAG,CAACkC,WAAW,CAACd,SAAS,EAAE,GAAGY,QAAQ,CAACZ,SAAS,EAAE,EAAE,SAAS,CAAC;MAClFa,MAAM,GAAGA,MAAM,CAACjC,GAAG,CAACkC,WAAW,CAACd,SAAS,EAAE,GAAGa,MAAM,CAACb,SAAS,EAAE,EAAE,SAAS,CAAC;MAC5EyB,IAAI,GAAG;QAAE9D,IAAI,EAAEiD,QAAQ,CAAC/B,WAAW,EAAE;QAAEjB,EAAE,EAAEiD,MAAM,CAAChC,WAAW;MAAE,CAAE;IACnE;IACA,IAAI,CAACzC,oBAAoB,GAAG,IAAIZ,kBAAkB,CAACiG,IAAI,CAAC;IACxD,IAAI,CAAChE,OAAO,CAAClB,IAAI,CAACmB,SAAS,CAAC;EAC9B;EAEA,IAAIS,WAAWA,CAAA;IACb,OAAO,IAAI,CAACL,IAAI,CAAC4D,GAAG,CAAC,MAAM,CAAuB;EACpD;EAEA,IAAI1C,SAASA,CAAA;IACX,OAAO,IAAI,CAAClB,IAAI,CAAC4D,GAAG,CAAC,IAAI,CAAuB;EAClD;EAEAC,OAAOA,CAAEV,KAAY;IACnBA,KAAK,CAACW,cAAc,EAAE;IACtBX,KAAK,CAACC,eAAe,EAAE;EACzB;EAEAW,cAAcA,CAAEC,MAAqE;IAAA,IAAAC,YAAA,EAAAC,aAAA;IACnF,IAAI,CAAClE,IAAI,CAACmE,UAAU,CAACH,MAAM,EAAAC,YAAA,GAAC,IAAI,CAAC1F,MAAM,cAAA0F,YAAA,uBAAXA,YAAA,CAAanC,QAAQ,GAAAoC,aAAA,GAAE,IAAI,CAAC3F,MAAM,cAAA2F,aAAA,uBAAXA,aAAA,CAAatD,WAAW,CAAC,CAAC;EAC/E;EAEAwD,KAAKA,CAAEjB,KAAY;IACjBA,KAAK,CAACW,cAAc,EAAE;IACtB,IAAI,CAAC9D,IAAI,CAAC0C,QAAQ,CAAC;MAAE7C,IAAI,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE,CAAC;EAC1C;EAEAuE,MAAMA,CAAElB,KAAY;IAClBA,KAAK,CAACW,cAAc,EAAE;IACtB,IAAI,IAAI,CAACQ,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACC,SAAS,EAAE;IACjC;EACF;EAEAC,KAAKA,CAAErB,KAAY;IACjBA,KAAK,CAACW,cAAc,EAAE;IACtB,IAAI,CAACxF,oBAAoB,GAAG,IAAI,CAAC0B,IAAI,CAAC7B,KAAK;IAC3C,IAAI,CAACwB,OAAO,CAAClB,IAAI,CAACmB,SAAS,CAAC;IAC5B,IAAI,IAAI,CAAC0E,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACC,SAAS,EAAE;IACjC;EACF;EAEAE,UAAUA,CAAA;IACR,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,aAAa,EAAE;IAC9B;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC5E,IAAI,CAACmE,UAAU,CAAC,IAAI,CAACpE,YAAY,CAAC;EACzC;EAEA8E,qBAAqBA,CAAEC,QAAgB;IACrC,IAAI,CAACpF,aAAa,GAAGoF,QAAQ;IAC7B,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAC,UAAUA,CAAEhB,MAAoB;IAC9B,OAAOiB,IAAI,CAACC,SAAS,CAAClB,MAAM,CAACmB,EAAE,CAAC,IAAI,CAAC5G,MAAM,CAACuD,QAAQ,EAAE,IAAI,CAACvD,MAAM,CAACqC,WAAW,CAAC,CAAC,KAAKqE,IAAI,CAACC,SAAS,CAAC,IAAI,CAAClF,IAAI,CAAC7B,KAAK,CAAC;EACrH;EAEUiH,eAAeA,CAAE5B,QAAiB;IAC1CA,QAAQ,GAAG,IAAI,CAAC7E,YAAY,CAAC0G,OAAO,EAAE,GAAG,IAAI,CAAC1G,YAAY,CAAC2G,MAAM,EAAE;EACrE;EAEUC,YAAYA,CAAA;IACpB,IAAI,IAAI,CAACC,KAAK,EAAE;MACd,OAAO,IAAI,CAACA,KAAK,CAACC,UAAU;IAC9B;IACA,OAAO,KAAK;EACd;EAEQV,eAAeA,CAAA;IACrBW,MAAM,CAACC,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACzC,IAAI,CAAC5G,GAAG,CAAC6G,YAAY,EAAE;EACzB;;;;;;;;;;UApNctJ;EAAQ;IAAAuJ,IAAA,EAAItJ;EAAI;AAAA,G;;;UAChBD;EAAQ;AAAA,G;;;;;;UArErBD;EAAK;;UAULA;EAAK;;UASLA;EAAK;;UAELA;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UASLA;EAAK;;UAMLD;EAAW;;UAGXA,WAAW;IAAA0J,IAAA,GAAC,gBAAgB;EAAA;;UAK5BtJ,SAAS;IAAAsJ,IAAA,GAAC,OAAO;EAAA;;UACjBtJ,SAAS;IAAAsJ,IAAA,GAAC,QAAQ,EAAE;MAAEC,MAAM,EAAE;IAAI,CAAE;EAAA;;UACpCvJ,SAAS;IAAAsJ,IAAA,GAAC,MAAM,EAAE;MAAEE,IAAI,EAAEjJ;IAAc,CAAE;EAAA;;UAC1CP,SAAS;IAAAsJ,IAAA,GAAC,SAAS,EAAE;MAAEE,IAAI,EAAElJ;IAAO,CAAE;EAAA;;AAnD5BgB,sBAAsB,GAAAmI,UAAA,EAPlC/J,SAAS,CAAC;EACPgK,QAAQ,EAAE,qBAAqB;EAC/BC,QAAA,EAAAC,oBAA+C;EAE/CC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAEzJ,mBAAmB;IAAE0J,WAAW,EAAEzI;EAAsB,CAAE,CAAC;EAClF0I,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACW1I,sBAAsB,CA0RlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}