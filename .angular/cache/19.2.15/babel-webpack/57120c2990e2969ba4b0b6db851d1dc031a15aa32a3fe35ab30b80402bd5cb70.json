{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { <PERSON><PERSON> } from '@angular/core';\nlet TruncatePipe = class TruncatePipe {\n  transform(value, config) {\n    const limit = config && config.maxLength ? config.maxLength : 255;\n    const trail = config && config.isEllipsis ? '...' : '';\n    return (value === null || value === void 0 ? void 0 : value.length) > limit ? value.substring(0, limit) + trail : value;\n  }\n};\nTruncatePipe = __decorate([Pipe({\n  name: 'truncate',\n  standalone: false\n})], TruncatePipe);\nexport { TruncatePipe };", "map": {"version": 3, "names": ["__decorate", "<PERSON><PERSON>", "TruncatePipe", "transform", "value", "config", "limit", "max<PERSON><PERSON><PERSON>", "trail", "isEllipsis", "length", "substring", "name", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/pipes/truncate.pipe.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Pi<PERSON> } from '@angular/core';\nlet TruncatePipe = class TruncatePipe {\n    transform(value, config) {\n        const limit = config && config.maxLength ? config.maxLength : 255;\n        const trail = config && config.isEllipsis ? '...' : '';\n        return value?.length > limit ? value.substring(0, limit) + trail : value;\n    }\n};\nTruncatePipe = __decorate([\n    Pipe({\n        name: 'truncate',\n        standalone: false\n    })\n], TruncatePipe);\nexport { TruncatePipe };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,IAAI,QAAQ,eAAe;AACpC,IAAIC,YAAY,GAAG,MAAMA,YAAY,CAAC;EAClCC,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACrB,MAAMC,KAAK,GAAGD,MAAM,IAAIA,MAAM,CAACE,SAAS,GAAGF,MAAM,CAACE,SAAS,GAAG,GAAG;IACjE,MAAMC,KAAK,GAAGH,MAAM,IAAIA,MAAM,CAACI,UAAU,GAAG,KAAK,GAAG,EAAE;IACtD,OAAO,CAAAL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,MAAM,IAAGJ,KAAK,GAAGF,KAAK,CAACO,SAAS,CAAC,CAAC,EAAEL,KAAK,CAAC,GAAGE,KAAK,GAAGJ,KAAK;EAC5E;AACJ,CAAC;AACDF,YAAY,GAAGF,UAAU,CAAC,CACtBC,IAAI,CAAC;EACDW,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEX,YAAY,CAAC;AAChB,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}