{"ast": null, "code": "var _SwuiInputSequenceMapComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-input-sequence-map.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-input-sequence-map.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, forwardRef, Input } from '@angular/core';\nimport { UntypedFormArray, UntypedFormControl, UntypedFormGroup, NG_VALUE_ACCESSOR, Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nlet SwuiInputSequenceMapComponent = (_SwuiInputSequenceMapComponent = class SwuiInputSequenceMapComponent {\n  constructor() {\n    this.readonly = false;\n    this.submitted = false;\n    this.formArray = new UntypedFormArray([]);\n    this.destroyed$ = new Subject();\n    this.onChange = _value => {};\n    this.onTouched = () => {};\n  }\n  ngOnInit() {\n    this.writeValue({});\n    this.formArray.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n      const data = {};\n      this.formArray.controls.forEach(control => {\n        var _group$get, _group$get2;\n        const group = control;\n        const source = (_group$get = group.get('source')) === null || _group$get === void 0 || (_group$get = _group$get.value) === null || _group$get === void 0 ? void 0 : _group$get.trim();\n        const target = (_group$get2 = group.get('target')) === null || _group$get2 === void 0 || (_group$get2 = _group$get2.value) === null || _group$get2 === void 0 ? void 0 : _group$get2.trim();\n        if (source && target) {\n          data[source] = target;\n        }\n      });\n      this.onChange(data);\n      this.onTouched();\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  // ControlValueAccessor implementation\n  writeValue(value) {\n    while (this.formArray.length !== 0) {\n      this.formArray.removeAt(0);\n    }\n    Object.entries(value || {}).forEach(([source, target]) => {\n      this.addPair(source, target);\n    });\n    if (this.formArray.length === 0) {\n      this.addPair();\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.readonly = isDisabled;\n    if (isDisabled) {\n      this.formArray.disable();\n    } else {\n      this.formArray.enable();\n    }\n  }\n  addPair(source = '', target = '') {\n    this.formArray.push(new UntypedFormGroup({\n      source: new UntypedFormControl(source, [Validators.required]),\n      target: new UntypedFormControl(target, [Validators.required])\n    }));\n  }\n  removePair(index) {\n    if (this.formArray.length > 1) {\n      this.formArray.removeAt(index);\n    }\n  }\n  getSourceControl(index) {\n    var _this$formArray$at;\n    return (_this$formArray$at = this.formArray.at(index)) === null || _this$formArray$at === void 0 ? void 0 : _this$formArray$at.get('source');\n  }\n  getTargetControl(index) {\n    var _this$formArray$at2;\n    return (_this$formArray$at2 = this.formArray.at(index)) === null || _this$formArray$at2 === void 0 ? void 0 : _this$formArray$at2.get('target');\n  }\n  hasSourceError(index) {\n    const control = this.getSourceControl(index);\n    return Boolean((control === null || control === void 0 ? void 0 : control.invalid) && ((control === null || control === void 0 ? void 0 : control.dirty) || (control === null || control === void 0 ? void 0 : control.touched) || this.submitted));\n  }\n  hasTargetError(index) {\n    const control = this.getSourceControl(index);\n    return Boolean((control === null || control === void 0 ? void 0 : control.invalid) && ((control === null || control === void 0 ? void 0 : control.dirty) || (control === null || control === void 0 ? void 0 : control.touched) || this.submitted));\n  }\n  getSourceErrorMessage(index) {\n    const control = this.getSourceControl(index);\n    if (control !== null && control !== void 0 && control.hasError('required')) {\n      return 'required';\n    }\n    return '';\n  }\n  getTargetErrorMessage(index) {\n    const control = this.getTargetControl(index);\n    if (control !== null && control !== void 0 && control.hasError('required')) {\n      return 'required';\n    }\n    return '';\n  }\n}, _SwuiInputSequenceMapComponent.propDecorators = {\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  options: [{\n    type: Input\n  }]\n}, _SwuiInputSequenceMapComponent);\nSwuiInputSequenceMapComponent = __decorate([Component({\n  selector: 'lib-swui-input-sequence-map',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SwuiInputSequenceMapComponent),\n    multi: true\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiInputSequenceMapComponent);\nexport { SwuiInputSequenceMapComponent };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "Component", "forwardRef", "Input", "UntypedFormArray", "UntypedFormControl", "UntypedFormGroup", "NG_VALUE_ACCESSOR", "Validators", "Subject", "takeUntil", "SwuiInputSequenceMapComponent", "_SwuiInputSequenceMapComponent", "constructor", "readonly", "submitted", "formArray", "destroyed$", "onChange", "_value", "onTouched", "ngOnInit", "writeValue", "valueChanges", "pipe", "subscribe", "data", "controls", "for<PERSON>ach", "control", "_group$get", "_group$get2", "group", "source", "get", "value", "trim", "target", "ngOnDestroy", "next", "undefined", "complete", "length", "removeAt", "Object", "entries", "addPair", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "disable", "enable", "push", "required", "removePair", "index", "getSourceControl", "_this$formArray$at", "at", "getTargetControl", "_this$formArray$at2", "hasSourceError", "Boolean", "invalid", "dirty", "touched", "hasTargetError", "getSourceErrorMessage", "<PERSON><PERSON><PERSON><PERSON>", "getTargetErrorMessage", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "changeDetection", "OnPush", "providers", "provide", "useExisting", "multi", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-input-sequence-map/swui-input-sequence-map.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, forwardRef, Input, OnDestroy, OnInit } from '@angular/core';\nimport { ControlValueAccessor, UntypedFormArray, UntypedFormControl, UntypedFormGroup, NG_VALUE_ACCESSOR, Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\ninterface SelectOptionItem {\n  id: string;\n  text: string;\n  disabled?: boolean;\n}\n\nexport interface SwuiSequenceMapOptions {\n  addButtonLabel?: string;\n  removeButtonLabel?: string;\n  sourcePlaceholder?: string;\n  targetPlaceholder?: string;\n  sourceOptions?: SelectOptionItem[];\n  targetOptions?: SelectOptionItem[];\n  title?: string;\n}\n\n@Component({\n    selector: 'lib-swui-input-sequence-map',\n    templateUrl: './swui-input-sequence-map.component.html',\n    styleUrls: ['./swui-input-sequence-map.component.scss'],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    providers: [\n        {\n            provide: NG_VALUE_ACCESSOR,\n            useExisting: forwardRef(() => SwuiInputSequenceMapComponent),\n            multi: true\n        }\n    ],\n    standalone: false\n})\nexport class SwuiInputSequenceMapComponent implements OnInit, OnDestroy, ControlValueAccessor {\n  @Input() id?: string;\n  @Input() readonly = false;\n  @Input() submitted = false;\n  @Input() options?: SwuiSequenceMapOptions;\n\n  formArray = new UntypedFormArray([]);\n\n  private readonly destroyed$ = new Subject<void>();\n\n  ngOnInit(): void {\n    this.writeValue({});\n\n    this.formArray.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n      const data: Record<string, string> = {};\n      this.formArray.controls.forEach(control => {\n        const group = control as UntypedFormGroup;\n        const source = group.get('source')?.value?.trim();\n        const target = group.get('target')?.value?.trim();\n        if (source && target) {\n          data[source] = target;\n        }\n      });\n      this.onChange(data);\n      this.onTouched();\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n\n  // ControlValueAccessor implementation\n  writeValue(value: Record<string, string>): void {\n    while (this.formArray.length !== 0) {\n      this.formArray.removeAt(0);\n    }\n    Object.entries(value || {}).forEach(([source, target]) => {\n      this.addPair(source, target);\n    });\n    if (this.formArray.length === 0) {\n      this.addPair();\n    }\n  }\n\n  registerOnChange(fn: (value: Record<string, string>) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    this.readonly = isDisabled;\n    if (isDisabled) {\n      this.formArray.disable();\n    } else {\n      this.formArray.enable();\n    }\n  }\n\n  addPair(source = '', target = ''): void {\n    this.formArray.push(new UntypedFormGroup({\n      source: new UntypedFormControl(source, [Validators.required]),\n      target: new UntypedFormControl(target, [Validators.required])\n    }));\n  }\n\n  removePair(index: number): void {\n    if (this.formArray.length > 1) {\n      this.formArray.removeAt(index);\n    }\n  }\n\n  getSourceControl(index: number): UntypedFormControl {\n    return this.formArray.at(index)?.get('source') as UntypedFormControl;\n  }\n\n  getTargetControl(index: number): UntypedFormControl {\n    return this.formArray.at(index)?.get('target') as UntypedFormControl;\n  }\n\n  hasSourceError(index: number): boolean {\n    const control = this.getSourceControl(index);\n    return Boolean(control?.invalid && (control?.dirty || control?.touched || this.submitted));\n  }\n\n  hasTargetError(index: number): boolean {\n    const control = this.getSourceControl(index);\n    return Boolean(control?.invalid && (control?.dirty || control?.touched || this.submitted));\n  }\n\n  getSourceErrorMessage(index: number): string {\n    const control = this.getSourceControl(index);\n    if (control?.hasError('required')) {\n      return 'required';\n    }\n    return '';\n  }\n\n  getTargetErrorMessage(index: number): string {\n    const control = this.getTargetControl(index);\n    if (control?.hasError('required')) {\n      return 'required';\n    }\n    return '';\n  }\n\n  private onChange = (_value: Record<string, string>) => { };\n\n  private onTouched = () => { };\n}\n"], "mappings": ";;;;AAAA,SAASA,uBAAuB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,KAAK,QAA2B,eAAe;AACxG,SAA+BC,gBAAgB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,gBAAgB;AAC5I,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAgCnC,IAAMC,6BAA6B,IAAAC,8BAAA,GAAnC,MAAMD,6BAA6B;EAAnCE,YAAA;IAEI,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,SAAS,GAAG,KAAK;IAG1B,KAAAC,SAAS,GAAG,IAAIZ,gBAAgB,CAAC,EAAE,CAAC;IAEnB,KAAAa,UAAU,GAAG,IAAIR,OAAO,EAAQ;IAsGzC,KAAAS,QAAQ,GAAIC,MAA8B,IAAI,CAAG,CAAC;IAElD,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAC/B;EAvGEC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,CAAC,EAAE,CAAC;IAEnB,IAAI,CAACN,SAAS,CAACO,YAAY,CAACC,IAAI,CAACd,SAAS,CAAC,IAAI,CAACO,UAAU,CAAC,CAAC,CAACQ,SAAS,CAAC,MAAK;MAC1E,MAAMC,IAAI,GAA2B,EAAE;MACvC,IAAI,CAACV,SAAS,CAACW,QAAQ,CAACC,OAAO,CAACC,OAAO,IAAG;QAAA,IAAAC,UAAA,EAAAC,WAAA;QACxC,MAAMC,KAAK,GAAGH,OAA2B;QACzC,MAAMI,MAAM,IAAAH,UAAA,GAAGE,KAAK,CAACE,GAAG,CAAC,QAAQ,CAAC,cAAAJ,UAAA,gBAAAA,UAAA,GAAnBA,UAAA,CAAqBK,KAAK,cAAAL,UAAA,uBAA1BA,UAAA,CAA4BM,IAAI,EAAE;QACjD,MAAMC,MAAM,IAAAN,WAAA,GAAGC,KAAK,CAACE,GAAG,CAAC,QAAQ,CAAC,cAAAH,WAAA,gBAAAA,WAAA,GAAnBA,WAAA,CAAqBI,KAAK,cAAAJ,WAAA,uBAA1BA,WAAA,CAA4BK,IAAI,EAAE;QACjD,IAAIH,MAAM,IAAII,MAAM,EAAE;UACpBX,IAAI,CAACO,MAAM,CAAC,GAAGI,MAAM;QACvB;MACF,CAAC,CAAC;MACF,IAAI,CAACnB,QAAQ,CAACQ,IAAI,CAAC;MACnB,IAAI,CAACN,SAAS,EAAE;IAClB,CAAC,CAAC;EACJ;EAEAkB,WAAWA,CAAA;IACT,IAAI,CAACrB,UAAU,CAACsB,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACvB,UAAU,CAACwB,QAAQ,EAAE;EAC5B;EAEA;EACAnB,UAAUA,CAACa,KAA6B;IACtC,OAAO,IAAI,CAACnB,SAAS,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAAC1B,SAAS,CAAC2B,QAAQ,CAAC,CAAC,CAAC;IAC5B;IACAC,MAAM,CAACC,OAAO,CAACV,KAAK,IAAI,EAAE,CAAC,CAACP,OAAO,CAAC,CAAC,CAACK,MAAM,EAAEI,MAAM,CAAC,KAAI;MACvD,IAAI,CAACS,OAAO,CAACb,MAAM,EAAEI,MAAM,CAAC;IAC9B,CAAC,CAAC;IACF,IAAI,IAAI,CAACrB,SAAS,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACI,OAAO,EAAE;IAChB;EACF;EAEAC,gBAAgBA,CAACC,EAA2C;IAC1D,IAAI,CAAC9B,QAAQ,GAAG8B,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAAC5B,SAAS,GAAG4B,EAAE;EACrB;EAEAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACrC,QAAQ,GAAGqC,UAAU;IAC1B,IAAIA,UAAU,EAAE;MACd,IAAI,CAACnC,SAAS,CAACoC,OAAO,EAAE;IAC1B,CAAC,MAAM;MACL,IAAI,CAACpC,SAAS,CAACqC,MAAM,EAAE;IACzB;EACF;EAEAP,OAAOA,CAACb,MAAM,GAAG,EAAE,EAAEI,MAAM,GAAG,EAAE;IAC9B,IAAI,CAACrB,SAAS,CAACsC,IAAI,CAAC,IAAIhD,gBAAgB,CAAC;MACvC2B,MAAM,EAAE,IAAI5B,kBAAkB,CAAC4B,MAAM,EAAE,CAACzB,UAAU,CAAC+C,QAAQ,CAAC,CAAC;MAC7DlB,MAAM,EAAE,IAAIhC,kBAAkB,CAACgC,MAAM,EAAE,CAAC7B,UAAU,CAAC+C,QAAQ,CAAC;KAC7D,CAAC,CAAC;EACL;EAEAC,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACzC,SAAS,CAAC0B,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAAC1B,SAAS,CAAC2B,QAAQ,CAACc,KAAK,CAAC;IAChC;EACF;EAEAC,gBAAgBA,CAACD,KAAa;IAAA,IAAAE,kBAAA;IAC5B,QAAAA,kBAAA,GAAO,IAAI,CAAC3C,SAAS,CAAC4C,EAAE,CAACH,KAAK,CAAC,cAAAE,kBAAA,uBAAxBA,kBAAA,CAA0BzB,GAAG,CAAC,QAAQ,CAAuB;EACtE;EAEA2B,gBAAgBA,CAACJ,KAAa;IAAA,IAAAK,mBAAA;IAC5B,QAAAA,mBAAA,GAAO,IAAI,CAAC9C,SAAS,CAAC4C,EAAE,CAACH,KAAK,CAAC,cAAAK,mBAAA,uBAAxBA,mBAAA,CAA0B5B,GAAG,CAAC,QAAQ,CAAuB;EACtE;EAEA6B,cAAcA,CAACN,KAAa;IAC1B,MAAM5B,OAAO,GAAG,IAAI,CAAC6B,gBAAgB,CAACD,KAAK,CAAC;IAC5C,OAAOO,OAAO,CAAC,CAAAnC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoC,OAAO,MAAK,CAAApC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqC,KAAK,MAAIrC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,OAAO,KAAI,IAAI,CAACpD,SAAS,CAAC,CAAC;EAC5F;EAEAqD,cAAcA,CAACX,KAAa;IAC1B,MAAM5B,OAAO,GAAG,IAAI,CAAC6B,gBAAgB,CAACD,KAAK,CAAC;IAC5C,OAAOO,OAAO,CAAC,CAAAnC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoC,OAAO,MAAK,CAAApC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqC,KAAK,MAAIrC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,OAAO,KAAI,IAAI,CAACpD,SAAS,CAAC,CAAC;EAC5F;EAEAsD,qBAAqBA,CAACZ,KAAa;IACjC,MAAM5B,OAAO,GAAG,IAAI,CAAC6B,gBAAgB,CAACD,KAAK,CAAC;IAC5C,IAAI5B,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEyC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,UAAU;IACnB;IACA,OAAO,EAAE;EACX;EAEAC,qBAAqBA,CAACd,KAAa;IACjC,MAAM5B,OAAO,GAAG,IAAI,CAACgC,gBAAgB,CAACJ,KAAK,CAAC;IAC5C,IAAI5B,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEyC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,UAAU;IACnB;IACA,OAAO,EAAE;EACX;;;UA3GCnE;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;AAJKQ,6BAA6B,GAAA6D,UAAA,EAdzCvE,SAAS,CAAC;EACPwE,QAAQ,EAAE,6BAA6B;EACvCC,QAAA,EAAAC,oBAAuD;EAEvDC,eAAe,EAAE5E,uBAAuB,CAAC6E,MAAM;EAC/CC,SAAS,EAAE,CACP;IACIC,OAAO,EAAExE,iBAAiB;IAC1ByE,WAAW,EAAE9E,UAAU,CAAC,MAAMS,6BAA6B,CAAC;IAC5DsE,KAAK,EAAE;GACV,CACJ;EACDC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWvE,6BAA6B,CAiHzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}