{"ast": null, "code": "var _SwuiPagePanelComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-page-panel.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-page-panel.component.scss?ngResource\";\nimport { Location } from '@angular/common';\nimport { Component, Input } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { Router } from '@angular/router';\nimport { filter, take } from 'rxjs/operators';\nimport { ActionConfirmDialogComponent } from './action-confirm-dialog/action-confirm-dialog.component';\nlet SwuiPagePanelComponent = (_SwuiPagePanelComponent = class SwuiPagePanelComponent {\n  set actions(actions) {\n    if (actions) {\n      this._actions = actions.filter(item => {\n        let available = typeof item.availableFn === 'undefined';\n        if (!available && item.availableFn) {\n          available = item.availableFn();\n        }\n        return available;\n      });\n    }\n  }\n  get actions() {\n    return this._actions;\n  }\n  constructor(router, dialog, location) {\n    this.router = router;\n    this.dialog = dialog;\n    this.location = location;\n    this.title = '';\n    this.back = false;\n    this.actionsMenuButtonTitle = '';\n    this.actionsMenuButtonColor = 'primary';\n    this.layout = 'separate';\n    this._actions = [];\n  }\n  ngOnInit() {}\n  goBack($event) {\n    $event.preventDefault();\n    if (this.backUrl) {\n      this.router.navigate([this.backUrl]);\n    } else {\n      this.location.back();\n    }\n  }\n  performAction($event, action) {\n    $event.preventDefault();\n    if (action.confirm) {\n      this.confirmDialogRef = this.dialog.open(ActionConfirmDialogComponent, {\n        width: '350px',\n        data: {\n          action\n        }\n      });\n      this.confirmDialogRef.afterClosed().pipe(filter(confirmResult => confirmResult.confirmed), take(1)).subscribe(() => this.callAction(action));\n    } else {\n      this.callAction(action);\n    }\n  }\n  isDisabled(action) {\n    return typeof action.disabledFn !== 'undefined' ? action.disabledFn() : false;\n  }\n  callAction(action) {\n    if (action.actionUrl) {\n      this.router.navigate([action.actionUrl]);\n    } else if (typeof action.actionFn !== 'undefined') {\n      action.actionFn();\n    }\n  }\n}, _SwuiPagePanelComponent.ctorParameters = () => [{\n  type: Router\n}, {\n  type: MatDialog\n}, {\n  type: Location\n}], _SwuiPagePanelComponent.propDecorators = {\n  actions: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  back: [{\n    type: Input\n  }],\n  backUrl: [{\n    type: Input\n  }],\n  actionsMenuButtonTitle: [{\n    type: Input\n  }],\n  actionsMenuButtonColor: [{\n    type: Input\n  }],\n  layout: [{\n    type: Input\n  }]\n}, _SwuiPagePanelComponent);\nSwuiPagePanelComponent = __decorate([Component({\n  selector: 'lib-swui-page-panel',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiPagePanelComponent);\nexport { SwuiPagePanelComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Location", "Component", "Input", "MatDialog", "Router", "filter", "take", "ActionConfirmDialogComponent", "SwuiPagePanelComponent", "_SwuiPagePanelComponent", "actions", "_actions", "item", "available", "availableFn", "constructor", "router", "dialog", "location", "title", "back", "actionsMenuButtonTitle", "actionsMenuButtonColor", "layout", "ngOnInit", "goBack", "$event", "preventDefault", "backUrl", "navigate", "performAction", "action", "confirm", "confirmDialogRef", "open", "width", "data", "afterClosed", "pipe", "confirmResult", "confirmed", "subscribe", "callAction", "isDisabled", "disabledFn", "actionUrl", "actionFn", "ctorParameters", "type", "propDecorators", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-page-panel/swui-page-panel.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-page-panel.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-page-panel.component.scss?ngResource\";\nimport { Location } from '@angular/common';\nimport { Component, Input } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { Router } from '@angular/router';\nimport { filter, take } from 'rxjs/operators';\nimport { ActionConfirmDialogComponent } from './action-confirm-dialog/action-confirm-dialog.component';\nlet SwuiPagePanelComponent = class SwuiPagePanelComponent {\n    set actions(actions) {\n        if (actions) {\n            this._actions = actions.filter(item => {\n                let available = typeof item.availableFn === 'undefined';\n                if (!available && item.availableFn) {\n                    available = item.availableFn();\n                }\n                return available;\n            });\n        }\n    }\n    get actions() {\n        return this._actions;\n    }\n    constructor(router, dialog, location) {\n        this.router = router;\n        this.dialog = dialog;\n        this.location = location;\n        this.title = '';\n        this.back = false;\n        this.actionsMenuButtonTitle = '';\n        this.actionsMenuButtonColor = 'primary';\n        this.layout = 'separate';\n        this._actions = [];\n    }\n    ngOnInit() {\n    }\n    goBack($event) {\n        $event.preventDefault();\n        if (this.backUrl) {\n            this.router.navigate([this.backUrl]);\n        }\n        else {\n            this.location.back();\n        }\n    }\n    performAction($event, action) {\n        $event.preventDefault();\n        if (action.confirm) {\n            this.confirmDialogRef = this.dialog.open(ActionConfirmDialogComponent, {\n                width: '350px',\n                data: { action }\n            });\n            this.confirmDialogRef.afterClosed().pipe(filter((confirmResult) => confirmResult.confirmed), take(1)).subscribe(() => this.callAction(action));\n        }\n        else {\n            this.callAction(action);\n        }\n    }\n    isDisabled(action) {\n        return typeof action.disabledFn !== 'undefined' ? action.disabledFn() : false;\n    }\n    callAction(action) {\n        if (action.actionUrl) {\n            this.router.navigate([action.actionUrl]);\n        }\n        else if (typeof action.actionFn !== 'undefined') {\n            action.actionFn();\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: Router },\n        { type: MatDialog },\n        { type: Location }\n    ]; }\n    static { this.propDecorators = {\n        actions: [{ type: Input }],\n        title: [{ type: Input }],\n        back: [{ type: Input }],\n        backUrl: [{ type: Input }],\n        actionsMenuButtonTitle: [{ type: Input }],\n        actionsMenuButtonColor: [{ type: Input }],\n        layout: [{ type: Input }]\n    }; }\n};\nSwuiPagePanelComponent = __decorate([\n    Component({\n        selector: 'lib-swui-page-panel',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiPagePanelComponent);\nexport { SwuiPagePanelComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,MAAM,EAAEC,IAAI,QAAQ,gBAAgB;AAC7C,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,IAAIC,sBAAsB,IAAAC,uBAAA,GAAG,MAAMD,sBAAsB,CAAC;EACtD,IAAIE,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAIA,OAAO,EAAE;MACT,IAAI,CAACC,QAAQ,GAAGD,OAAO,CAACL,MAAM,CAACO,IAAI,IAAI;QACnC,IAAIC,SAAS,GAAG,OAAOD,IAAI,CAACE,WAAW,KAAK,WAAW;QACvD,IAAI,CAACD,SAAS,IAAID,IAAI,CAACE,WAAW,EAAE;UAChCD,SAAS,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;QAClC;QACA,OAAOD,SAAS;MACpB,CAAC,CAAC;IACN;EACJ;EACA,IAAIH,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACAI,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IAClC,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,sBAAsB,GAAG,SAAS;IACvC,IAAI,CAACC,MAAM,GAAG,UAAU;IACxB,IAAI,CAACZ,QAAQ,GAAG,EAAE;EACtB;EACAa,QAAQA,CAAA,EAAG,CACX;EACAC,MAAMA,CAACC,MAAM,EAAE;IACXA,MAAM,CAACC,cAAc,CAAC,CAAC;IACvB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,IAAI,CAACD,OAAO,CAAC,CAAC;IACxC,CAAC,MACI;MACD,IAAI,CAACV,QAAQ,CAACE,IAAI,CAAC,CAAC;IACxB;EACJ;EACAU,aAAaA,CAACJ,MAAM,EAAEK,MAAM,EAAE;IAC1BL,MAAM,CAACC,cAAc,CAAC,CAAC;IACvB,IAAII,MAAM,CAACC,OAAO,EAAE;MAChB,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAAChB,MAAM,CAACiB,IAAI,CAAC3B,4BAA4B,EAAE;QACnE4B,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;UAAEL;QAAO;MACnB,CAAC,CAAC;MACF,IAAI,CAACE,gBAAgB,CAACI,WAAW,CAAC,CAAC,CAACC,IAAI,CAACjC,MAAM,CAAEkC,aAAa,IAAKA,aAAa,CAACC,SAAS,CAAC,EAAElC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmC,SAAS,CAAC,MAAM,IAAI,CAACC,UAAU,CAACX,MAAM,CAAC,CAAC;IAClJ,CAAC,MACI;MACD,IAAI,CAACW,UAAU,CAACX,MAAM,CAAC;IAC3B;EACJ;EACAY,UAAUA,CAACZ,MAAM,EAAE;IACf,OAAO,OAAOA,MAAM,CAACa,UAAU,KAAK,WAAW,GAAGb,MAAM,CAACa,UAAU,CAAC,CAAC,GAAG,KAAK;EACjF;EACAF,UAAUA,CAACX,MAAM,EAAE;IACf,IAAIA,MAAM,CAACc,SAAS,EAAE;MAClB,IAAI,CAAC7B,MAAM,CAACa,QAAQ,CAAC,CAACE,MAAM,CAACc,SAAS,CAAC,CAAC;IAC5C,CAAC,MACI,IAAI,OAAOd,MAAM,CAACe,QAAQ,KAAK,WAAW,EAAE;MAC7Cf,MAAM,CAACe,QAAQ,CAAC,CAAC;IACrB;EACJ;AAeJ,CAAC,EAdYrC,uBAAA,CAAKsC,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE5C;AAAO,CAAC,EAChB;EAAE4C,IAAI,EAAE7C;AAAU,CAAC,EACnB;EAAE6C,IAAI,EAAEhD;AAAS,CAAC,CACrB,EACQS,uBAAA,CAAKwC,cAAc,GAAG;EAC3BvC,OAAO,EAAE,CAAC;IAAEsC,IAAI,EAAE9C;EAAM,CAAC,CAAC;EAC1BiB,KAAK,EAAE,CAAC;IAAE6B,IAAI,EAAE9C;EAAM,CAAC,CAAC;EACxBkB,IAAI,EAAE,CAAC;IAAE4B,IAAI,EAAE9C;EAAM,CAAC,CAAC;EACvB0B,OAAO,EAAE,CAAC;IAAEoB,IAAI,EAAE9C;EAAM,CAAC,CAAC;EAC1BmB,sBAAsB,EAAE,CAAC;IAAE2B,IAAI,EAAE9C;EAAM,CAAC,CAAC;EACzCoB,sBAAsB,EAAE,CAAC;IAAE0B,IAAI,EAAE9C;EAAM,CAAC,CAAC;EACzCqB,MAAM,EAAE,CAAC;IAAEyB,IAAI,EAAE9C;EAAM,CAAC;AAC5B,CAAC,EAAAO,uBAAA,CACJ;AACDD,sBAAsB,GAAGX,UAAU,CAAC,CAChCI,SAAS,CAAC;EACNiD,QAAQ,EAAE,qBAAqB;EAC/BC,QAAQ,EAAErD,oBAAoB;EAC9BsD,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACtD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAES,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}