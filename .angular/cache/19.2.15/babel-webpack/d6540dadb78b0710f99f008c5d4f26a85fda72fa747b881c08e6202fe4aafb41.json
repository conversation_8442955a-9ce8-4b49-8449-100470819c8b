{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _asyncToGenerator from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _TestBedApplicationErrorHandler, _globalThis$beforeEac, _globalThis$afterEach, _Log;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { inject as inject$1, NgZone, ErrorHandler, Injectable, ɵDeferBlockState as _DeferBlockState, ɵtriggerResourceLoading as _triggerResourceLoading, ɵrenderDeferBlockState as _renderDeferBlockState, ɵCONTAINER_HEADER_OFFSET as _CONTAINER_HEADER_OFFSET, ɵgetDeferBlocks as _getDeferBlocks, InjectionToken, ɵDeferBlockBehavior as _DeferBlockBehavior, ɵNoopNgZone as _NoopNgZone, ApplicationRef, ɵPendingTasksInternal as _PendingTasksInternal, ɵZONELESS_ENABLED as _ZONELESS_ENABLED, ɵChangeDetectionScheduler as _ChangeDetectionScheduler, ɵEffectScheduler as _EffectScheduler, ɵMicrotaskEffectScheduler as _MicrotaskEffectScheduler, getDebugNode, RendererFactory2, ɵstringify as _stringify, Pipe, Directive, Component, NgModule, ɵReflectionCapabilities as _ReflectionCapabilities, ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT as _USE_RUNTIME_DEPS_TRACKER_FOR_JIT, ɵdepsTracker as _depsTracker, ɵgetInjectableDef as _getInjectableDef, resolveForwardRef, ɵisComponentDefPendingResolution as _isComponentDefPendingResolution, ɵgetAsyncClassMetadataFn as _getAsyncClassMetadataFn, ɵresolveComponentResources as _resolveComponentResources, ɵRender3NgModuleRef as _Render3NgModuleRef, ApplicationInitStatus, LOCALE_ID, ɵDEFAULT_LOCALE_ID as _DEFAULT_LOCALE_ID, ɵsetLocaleId as _setLocaleId, ɵRender3ComponentFactory as _Render3ComponentFactory, ɵNG_COMP_DEF as _NG_COMP_DEF, ɵcompileComponent as _compileComponent, ɵNG_DIR_DEF as _NG_DIR_DEF, ɵcompileDirective as _compileDirective, ɵNG_PIPE_DEF as _NG_PIPE_DEF, ɵcompilePipe as _compilePipe, ɵNG_MOD_DEF as _NG_MOD_DEF, ɵpatchComponentDefWithScope as _patchComponentDefWithScope, ɵNG_INJ_DEF as _NG_INJ_DEF, ɵcompileNgModuleDefs as _compileNgModuleDefs, ɵclearResolutionOfComponentResourcesQueue as _clearResolutionOfComponentResourcesQueue, ɵrestoreComponentResolutionQueue as _restoreComponentResolutionQueue, ɵinternalProvideZoneChangeDetection as _internalProvideZoneChangeDetection, ɵChangeDetectionSchedulerImpl as _ChangeDetectionSchedulerImpl, Compiler, ɵDEFER_BLOCK_CONFIG as _DEFER_BLOCK_CONFIG, ɵINTERNAL_APPLICATION_ERROR_HANDLER as _INTERNAL_APPLICATION_ERROR_HANDLER, COMPILER_OPTIONS, Injector, ɵtransitiveScopesFor as _transitiveScopesFor, ɵgenerateStandaloneInDeclarationsError as _generateStandaloneInDeclarationsError, ɵNgModuleFactory as _NgModuleFactory, ModuleWithComponentFactories, ɵisEnvironmentProviders as _isEnvironmentProviders, ɵconvertToBitFlags as _convertToBitFlags, InjectFlags, ɵsetAllowDuplicateNgModuleIdsForTest as _setAllowDuplicateNgModuleIdsForTest, ɵresetCompiledComponents as _resetCompiledComponents, ɵsetUnknownElementStrictMode as _setUnknownElementStrictMode, ɵsetUnknownPropertyStrictMode as _setUnknownPropertyStrictMode, ɵgetUnknownElementStrictMode as _getUnknownElementStrictMode, ɵgetUnknownPropertyStrictMode as _getUnknownPropertyStrictMode, runInInjectionContext, EnvironmentInjector, ɵflushModuleScopingQueueAsMuchAsPossible as _flushModuleScopingQueueAsMuchAsPossible } from '@angular/core';\nexport { ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { ResourceLoader } from '@angular/compiler';\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```ts\n * it('...', waitForAsync(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * })));\n * ```\n *\n * @publicApi\n */\nfunction waitForAsync(fn) {\n  const _Zone = typeof Zone !== 'undefined' ? Zone : null;\n  if (!_Zone) {\n    return function () {\n      return Promise.reject('Zone is needed for the waitForAsync() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js');\n    };\n  }\n  const asyncTest = _Zone && _Zone[_Zone.__symbol__('asyncTest')];\n  if (typeof asyncTest === 'function') {\n    return asyncTest(fn);\n  }\n  return function () {\n    return Promise.reject('zone-testing.js is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/testing');\n  };\n}\nconst RETHROW_APPLICATION_ERRORS_DEFAULT = true;\nclass TestBedApplicationErrorHandler {\n  constructor() {\n    _defineProperty(this, \"zone\", inject$1(NgZone));\n    _defineProperty(this, \"userErrorHandler\", inject$1(ErrorHandler));\n    _defineProperty(this, \"whenStableRejectFunctions\", new Set());\n  }\n  handleError(e) {\n    try {\n      this.zone.runOutsideAngular(() => this.userErrorHandler.handleError(e));\n    } catch (userError) {\n      e = userError;\n    }\n    // Instead of throwing the error when there are outstanding `fixture.whenStable` promises,\n    // reject those promises with the error. This allows developers to write\n    // expectAsync(fix.whenStable()).toBeRejected();\n    if (this.whenStableRejectFunctions.size > 0) {\n      for (const fn of this.whenStableRejectFunctions.values()) {\n        fn(e);\n      }\n      this.whenStableRejectFunctions.clear();\n    } else {\n      throw e;\n    }\n  }\n}\n_TestBedApplicationErrorHandler = TestBedApplicationErrorHandler;\n_defineProperty(TestBedApplicationErrorHandler, \"\\u0275fac\", function TestBedApplicationErrorHandler_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TestBedApplicationErrorHandler)();\n});\n_defineProperty(TestBedApplicationErrorHandler, \"\\u0275prov\", /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _TestBedApplicationErrorHandler,\n  factory: _TestBedApplicationErrorHandler.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TestBedApplicationErrorHandler, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Represents an individual defer block for testing purposes.\n *\n * @publicApi\n */\nclass DeferBlockFixture {\n  /** @docs-private */\n  constructor(block, componentFixture) {\n    _defineProperty(this, \"block\", void 0);\n    _defineProperty(this, \"componentFixture\", void 0);\n    this.block = block;\n    this.componentFixture = componentFixture;\n  }\n  /**\n   * Renders the specified state of the defer fixture.\n   * @param state the defer state to render\n   */\n  render(state) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!hasStateTemplate(state, _this.block)) {\n        const stateAsString = getDeferBlockStateNameFromEnum(state);\n        throw new Error(`Tried to render this defer block in the \\`${stateAsString}\\` state, ` + `but there was no @${stateAsString.toLowerCase()} block defined in a template.`);\n      }\n      if (state === _DeferBlockState.Complete) {\n        yield _triggerResourceLoading(_this.block.tDetails, _this.block.lView, _this.block.tNode);\n      }\n      // If the `render` method is used explicitly - skip timer-based scheduling for\n      // `@placeholder` and `@loading` blocks and render them immediately.\n      const skipTimerScheduling = true;\n      _renderDeferBlockState(state, _this.block.tNode, _this.block.lContainer, skipTimerScheduling);\n      _this.componentFixture.detectChanges();\n    })();\n  }\n  /**\n   * Retrieves all nested child defer block fixtures\n   * in a given defer block.\n   */\n  getDeferBlocks() {\n    const deferBlocks = [];\n    // An LContainer that represents a defer block has at most 1 view, which is\n    // located right after an LContainer header. Get a hold of that view and inspect\n    // it for nested defer blocks.\n    const deferBlockFixtures = [];\n    if (this.block.lContainer.length >= _CONTAINER_HEADER_OFFSET) {\n      const lView = this.block.lContainer[_CONTAINER_HEADER_OFFSET];\n      _getDeferBlocks(lView, deferBlocks);\n      for (const block of deferBlocks) {\n        deferBlockFixtures.push(new DeferBlockFixture(block, this.componentFixture));\n      }\n    }\n    return Promise.resolve(deferBlockFixtures);\n  }\n}\nfunction hasStateTemplate(state, block) {\n  switch (state) {\n    case _DeferBlockState.Placeholder:\n      return block.tDetails.placeholderTmplIndex !== null;\n    case _DeferBlockState.Loading:\n      return block.tDetails.loadingTmplIndex !== null;\n    case _DeferBlockState.Error:\n      return block.tDetails.errorTmplIndex !== null;\n    case _DeferBlockState.Complete:\n      return true;\n    default:\n      return false;\n  }\n}\nfunction getDeferBlockStateNameFromEnum(state) {\n  switch (state) {\n    case _DeferBlockState.Placeholder:\n      return 'Placeholder';\n    case _DeferBlockState.Loading:\n      return 'Loading';\n    case _DeferBlockState.Error:\n      return 'Error';\n    default:\n      return 'Main';\n  }\n}\n\n/** Whether test modules should be torn down by default. */\nconst TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;\n/** Whether unknown elements in templates should throw by default. */\nconst THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;\n/** Whether unknown properties in templates should throw by default. */\nconst THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;\n/** Whether defer blocks should use manual triggering or play through normally. */\nconst DEFER_BLOCK_DEFAULT_BEHAVIOR = _DeferBlockBehavior.Playthrough;\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @publicApi\n */\nclass TestComponentRenderer {\n  insertRootElement(rootElementId) {}\n  removeAllRootElements() {}\n}\n/**\n * @publicApi\n */\nconst ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');\n/**\n * @publicApi\n */\nconst ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @publicApi\n */\nclass ComponentFixture {\n  /** @docs-private */\n  constructor(componentRef) {\n    var _inject$;\n    _defineProperty(this, \"componentRef\", void 0);\n    /**\n     * The DebugElement associated with the root element of this component.\n     */\n    _defineProperty(this, \"debugElement\", void 0);\n    /**\n     * The instance of the root component class.\n     */\n    _defineProperty(this, \"componentInstance\", void 0);\n    /**\n     * The native element at the root of the component.\n     */\n    _defineProperty(this, \"nativeElement\", void 0);\n    /**\n     * The ElementRef for the element at the root of the component.\n     */\n    _defineProperty(this, \"elementRef\", void 0);\n    /**\n     * The ChangeDetectorRef for the component\n     */\n    _defineProperty(this, \"changeDetectorRef\", void 0);\n    _defineProperty(this, \"_renderer\", void 0);\n    _defineProperty(this, \"_isDestroyed\", false);\n    /** @internal */\n    _defineProperty(this, \"_noZoneOptionIsSet\", inject$1(ComponentFixtureNoNgZone, {\n      optional: true\n    }));\n    /** @internal */\n    _defineProperty(this, \"_ngZone\", this._noZoneOptionIsSet ? new _NoopNgZone() : inject$1(NgZone));\n    // Inject ApplicationRef to ensure NgZone stableness causes after render hooks to run\n    // This will likely happen as a result of fixture.detectChanges because it calls ngZone.run\n    // This is a crazy way of doing things but hey, it's the world we live in.\n    // The zoneless scheduler should instead do this more imperatively by attaching\n    // the `ComponentRef` to `ApplicationRef` and calling `appRef.tick` as the `detectChanges`\n    // behavior.\n    /** @internal */\n    _defineProperty(this, \"_appRef\", inject$1(ApplicationRef));\n    _defineProperty(this, \"_testAppRef\", this._appRef);\n    _defineProperty(this, \"pendingTasks\", inject$1(_PendingTasksInternal));\n    _defineProperty(this, \"appErrorHandler\", inject$1(TestBedApplicationErrorHandler));\n    _defineProperty(this, \"zonelessEnabled\", inject$1(_ZONELESS_ENABLED));\n    _defineProperty(this, \"scheduler\", inject$1(_ChangeDetectionScheduler));\n    _defineProperty(this, \"rootEffectScheduler\", inject$1(_EffectScheduler));\n    _defineProperty(this, \"microtaskEffectScheduler\", inject$1(_MicrotaskEffectScheduler));\n    _defineProperty(this, \"autoDetectDefault\", this.zonelessEnabled ? true : false);\n    _defineProperty(this, \"autoDetect\", (_inject$ = inject$1(ComponentFixtureAutoDetect, {\n      optional: true\n    })) !== null && _inject$ !== void 0 ? _inject$ : this.autoDetectDefault);\n    _defineProperty(this, \"subscriptions\", new Subscription());\n    // TODO(atscott): Remove this from public API\n    _defineProperty(this, \"ngZone\", this._noZoneOptionIsSet ? null : this._ngZone);\n    this.componentRef = componentRef;\n    this.changeDetectorRef = componentRef.changeDetectorRef;\n    this.elementRef = componentRef.location;\n    this.debugElement = getDebugNode(this.elementRef.nativeElement);\n    this.componentInstance = componentRef.instance;\n    this.nativeElement = this.elementRef.nativeElement;\n    this.componentRef = componentRef;\n    if (this.autoDetect) {\n      var _this$scheduler, _this$scheduler2;\n      this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n      (_this$scheduler = this.scheduler) === null || _this$scheduler === void 0 || _this$scheduler.notify(8 /* ɵNotificationSource.ViewAttached */);\n      (_this$scheduler2 = this.scheduler) === null || _this$scheduler2 === void 0 || _this$scheduler2.notify(0 /* ɵNotificationSource.MarkAncestorsForTraversal */);\n    }\n    this.componentRef.hostView.onDestroy(() => {\n      this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n    });\n    // Create subscriptions outside the NgZone so that the callbacks run outside\n    // of NgZone.\n    this._ngZone.runOutsideAngular(() => {\n      this.subscriptions.add(this._ngZone.onError.subscribe({\n        next: error => {\n          throw error;\n        }\n      }));\n    });\n  }\n  /**\n   * Trigger a change detection cycle for the component.\n   */\n  detectChanges(checkNoChanges = true) {\n    this.microtaskEffectScheduler.flush();\n    const originalCheckNoChanges = this.componentRef.changeDetectorRef.checkNoChanges;\n    try {\n      if (!checkNoChanges) {\n        this.componentRef.changeDetectorRef.checkNoChanges = () => {};\n      }\n      if (this.zonelessEnabled) {\n        try {\n          this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n          this._appRef.tick();\n        } finally {\n          if (!this.autoDetect) {\n            this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n          }\n        }\n      } else {\n        // Run the change detection inside the NgZone so that any async tasks as part of the change\n        // detection are captured by the zone and can be waited for in isStable.\n        this._ngZone.run(() => {\n          // Flush root effects before `detectChanges()`, to emulate the sequencing of `tick()`.\n          this.rootEffectScheduler.flush();\n          this.changeDetectorRef.detectChanges();\n          this.checkNoChanges();\n        });\n      }\n    } finally {\n      this.componentRef.changeDetectorRef.checkNoChanges = originalCheckNoChanges;\n    }\n    this.microtaskEffectScheduler.flush();\n  }\n  /**\n   * Do a change detection run to make sure there were no changes.\n   */\n  checkNoChanges() {\n    this.changeDetectorRef.checkNoChanges();\n  }\n  /**\n   * Set whether the fixture should autodetect changes.\n   *\n   * Also runs detectChanges once so that any existing change is detected.\n   *\n   * @param autoDetect Whether to autodetect changes. By default, `true`.\n   */\n  autoDetectChanges(autoDetect = true) {\n    if (this._noZoneOptionIsSet && !this.zonelessEnabled) {\n      throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set.');\n    }\n    if (autoDetect !== this.autoDetect) {\n      if (autoDetect) {\n        this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n      } else {\n        this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n      }\n    }\n    this.autoDetect = autoDetect;\n    this.detectChanges();\n  }\n  /**\n   * Return whether the fixture is currently stable or has async tasks that have not been completed\n   * yet.\n   */\n  isStable() {\n    return !this.pendingTasks.hasPendingTasks.value;\n  }\n  /**\n   * Get a promise that resolves when the fixture is stable.\n   *\n   * This can be used to resume testing after events have triggered asynchronous activity or\n   * asynchronous change detection.\n   */\n  whenStable() {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    }\n    return new Promise((resolve, reject) => {\n      this.appErrorHandler.whenStableRejectFunctions.add(reject);\n      this._appRef.whenStable().then(() => {\n        this.appErrorHandler.whenStableRejectFunctions.delete(reject);\n        resolve(true);\n      });\n    });\n  }\n  /**\n   * Retrieves all defer block fixtures in the component fixture.\n   */\n  getDeferBlocks() {\n    const deferBlocks = [];\n    const lView = this.componentRef.hostView['_lView'];\n    _getDeferBlocks(lView, deferBlocks);\n    const deferBlockFixtures = [];\n    for (const block of deferBlocks) {\n      deferBlockFixtures.push(new DeferBlockFixture(block, this));\n    }\n    return Promise.resolve(deferBlockFixtures);\n  }\n  _getRenderer() {\n    if (this._renderer === undefined) {\n      this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n    }\n    return this._renderer;\n  }\n  /**\n   * Get a promise that resolves when the ui state is stable following animations.\n   */\n  whenRenderingDone() {\n    const renderer = this._getRenderer();\n    if (renderer && renderer.whenRenderingDone) {\n      return renderer.whenRenderingDone();\n    }\n    return this.whenStable();\n  }\n  /**\n   * Trigger component destruction.\n   */\n  destroy() {\n    this.subscriptions.unsubscribe();\n    this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n    if (!this._isDestroyed) {\n      this.componentRef.destroy();\n      this._isDestroyed = true;\n    }\n  }\n}\nconst _Zone = typeof Zone !== 'undefined' ? Zone : null;\nconst fakeAsyncTestModule = _Zone && _Zone[_Zone.__symbol__('fakeAsyncTest')];\nconst fakeAsyncTestModuleNotLoadedErrorMessage = `zone-testing.js is needed for the fakeAsync() test helper but could not be found.\n        Please make sure that your environment includes zone.js/testing`;\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @publicApi\n */\nfunction resetFakeAsyncZone() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nfunction resetFakeAsyncZoneIfExists() {\n  var _Zone$ProxyZoneSpec;\n  if (fakeAsyncTestModule && (_Zone$ProxyZoneSpec = Zone['ProxyZoneSpec']) !== null && _Zone$ProxyZoneSpec !== void 0 && _Zone$ProxyZoneSpec.isLoaded()) {\n    fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n}\n/**\n * Wraps a function to be executed in the `fakeAsync` zone:\n * - Microtasks are manually executed by calling `flushMicrotasks()`.\n * - Timers are synchronous; `tick()` simulates the asynchronous passage of time.\n *\n * Can be used to wrap `inject()` calls.\n *\n * @param fn The function that you want to wrap in the `fakeAsync` zone.\n * @param options\n *   - flush: When true, will drain the macrotask queue after the test function completes.\n *     When false, will throw an exception at the end of the function if there are pending timers.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n *\n * @returns The function wrapped to be executed in the `fakeAsync` zone.\n * Any arguments passed when calling this returned function will be passed through to the `fn`\n * function in the parameters when it is called.\n *\n * @publicApi\n */\nfunction fakeAsync(fn, options) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.fakeAsync(fn, options);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the `fakeAsync` zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * @param millis The number of milliseconds to advance the virtual timer.\n * @param tickOptions The options to pass to the `tick()` function.\n *\n * @usageNotes\n *\n * The `tick()` option is a flag called `processNewMacroTasksSynchronously`,\n * which determines whether or not to invoke new macroTasks.\n *\n * If you provide a `tickOptions` object, but do not specify a\n * `processNewMacroTasksSynchronously` property (`tick(100, {})`),\n * then `processNewMacroTasksSynchronously` defaults to true.\n *\n * If you omit the `tickOptions` parameter (`tick(100))`), then\n * `tickOptions` defaults to `{processNewMacroTasksSynchronously: true}`.\n *\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * The following example includes a nested timeout (new macroTask), and\n * the `tickOptions` parameter is allowed to default. In this case,\n * `processNewMacroTasksSynchronously` defaults to true, and the nested\n * function is executed on each tick.\n *\n * ```ts\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick();\n *   expect(nestedTimeoutInvoked).toBe(true);\n * }));\n * ```\n *\n * In the following case, `processNewMacroTasksSynchronously` is explicitly\n * set to false, so the nested timeout function is not invoked.\n *\n * ```ts\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick(0, {processNewMacroTasksSynchronously: false});\n *   expect(nestedTimeoutInvoked).toBe(false);\n * }));\n * ```\n *\n *\n * @publicApi\n */\nfunction tick(millis = 0, tickOptions = {\n  processNewMacroTasksSynchronously: true\n}) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.tick(millis, tickOptions);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flushes any pending microtasks and simulates the asynchronous passage of time for the timers in\n * the `fakeAsync` zone by\n * draining the macrotask queue until it is empty.\n *\n * @param maxTurns The maximum number of times the scheduler attempts to clear its queue before\n *     throwing an error.\n * @returns The simulated time elapsed, in milliseconds.\n *\n * @publicApi\n */\nfunction flush(maxTurns) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flush(maxTurns);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @publicApi\n */\nfunction discardPeriodicTasks() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.discardPeriodicTasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flush any pending microtasks.\n *\n * @publicApi\n */\nfunction flushMicrotasks() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flushMicrotasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nlet _nextReferenceId = 0;\nclass MetadataOverrider {\n  constructor() {\n    _defineProperty(this, \"_references\", new Map());\n  }\n  /**\n   * Creates a new instance for the given metadata class\n   * based on an old instance and overrides.\n   */\n  overrideMetadata(metadataClass, oldMetadata, override) {\n    const props = {};\n    if (oldMetadata) {\n      _valueProps(oldMetadata).forEach(prop => props[prop] = oldMetadata[prop]);\n    }\n    if (override.set) {\n      if (override.remove || override.add) {\n        throw new Error(`Cannot set and add/remove ${_stringify(metadataClass)} at the same time!`);\n      }\n      setMetadata(props, override.set);\n    }\n    if (override.remove) {\n      removeMetadata(props, override.remove, this._references);\n    }\n    if (override.add) {\n      addMetadata(props, override.add);\n    }\n    return new metadataClass(props);\n  }\n}\nfunction removeMetadata(metadata, remove, references) {\n  const removeObjects = new Set();\n  for (const prop in remove) {\n    const removeValue = remove[prop];\n    if (Array.isArray(removeValue)) {\n      removeValue.forEach(value => {\n        removeObjects.add(_propHashKey(prop, value, references));\n      });\n    } else {\n      removeObjects.add(_propHashKey(prop, removeValue, references));\n    }\n  }\n  for (const prop in metadata) {\n    const propValue = metadata[prop];\n    if (Array.isArray(propValue)) {\n      metadata[prop] = propValue.filter(value => !removeObjects.has(_propHashKey(prop, value, references)));\n    } else {\n      if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n        metadata[prop] = undefined;\n      }\n    }\n  }\n}\nfunction addMetadata(metadata, add) {\n  for (const prop in add) {\n    const addValue = add[prop];\n    const propValue = metadata[prop];\n    if (propValue != null && Array.isArray(propValue)) {\n      metadata[prop] = propValue.concat(addValue);\n    } else {\n      metadata[prop] = addValue;\n    }\n  }\n}\nfunction setMetadata(metadata, set) {\n  for (const prop in set) {\n    metadata[prop] = set[prop];\n  }\n}\nfunction _propHashKey(propName, propValue, references) {\n  let nextObjectId = 0;\n  const objectIds = new Map();\n  const replacer = (key, value) => {\n    if (value !== null && typeof value === 'object') {\n      if (objectIds.has(value)) {\n        return objectIds.get(value);\n      }\n      // Record an id for this object such that any later references use the object's id instead\n      // of the object itself, in order to break cyclic pointers in objects.\n      objectIds.set(value, `ɵobj#${nextObjectId++}`);\n      // The first time an object is seen the object itself is serialized.\n      return value;\n    } else if (typeof value === 'function') {\n      value = _serializeReference(value, references);\n    }\n    return value;\n  };\n  return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\nfunction _serializeReference(ref, references) {\n  let id = references.get(ref);\n  if (!id) {\n    id = `${_stringify(ref)}${_nextReferenceId++}`;\n    references.set(ref, id);\n  }\n  return id;\n}\nfunction _valueProps(obj) {\n  const props = [];\n  // regular public props\n  Object.keys(obj).forEach(prop => {\n    if (!prop.startsWith('_')) {\n      props.push(prop);\n    }\n  });\n  // getters\n  let proto = obj;\n  while (proto = Object.getPrototypeOf(proto)) {\n    Object.keys(proto).forEach(protoProp => {\n      const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n      if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n        props.push(protoProp);\n      }\n    });\n  }\n  return props;\n}\nconst reflection = new _ReflectionCapabilities();\n/**\n * Allows to override ivy metadata for tests (via the `TestBed`).\n */\nclass OverrideResolver {\n  constructor() {\n    _defineProperty(this, \"overrides\", new Map());\n    _defineProperty(this, \"resolved\", new Map());\n  }\n  addOverride(type, override) {\n    const overrides = this.overrides.get(type) || [];\n    overrides.push(override);\n    this.overrides.set(type, overrides);\n    this.resolved.delete(type);\n  }\n  setOverrides(overrides) {\n    this.overrides.clear();\n    overrides.forEach(([type, override]) => {\n      this.addOverride(type, override);\n    });\n  }\n  getAnnotation(type) {\n    const annotations = reflection.annotations(type);\n    // Try to find the nearest known Type annotation and make sure that this annotation is an\n    // instance of the type we are looking for, so we can use it for resolution. Note: there might\n    // be multiple known annotations found due to the fact that Components can extend Directives (so\n    // both Directive and Component annotations would be present), so we always check if the known\n    // annotation has the right type.\n    for (let i = annotations.length - 1; i >= 0; i--) {\n      const annotation = annotations[i];\n      const isKnownType = annotation instanceof Directive || annotation instanceof Component || annotation instanceof Pipe || annotation instanceof NgModule;\n      if (isKnownType) {\n        return annotation instanceof this.type ? annotation : null;\n      }\n    }\n    return null;\n  }\n  resolve(type) {\n    let resolved = this.resolved.get(type) || null;\n    if (!resolved) {\n      resolved = this.getAnnotation(type);\n      if (resolved) {\n        const overrides = this.overrides.get(type);\n        if (overrides) {\n          const overrider = new MetadataOverrider();\n          overrides.forEach(override => {\n            resolved = overrider.overrideMetadata(this.type, resolved, override);\n          });\n        }\n      }\n      this.resolved.set(type, resolved);\n    }\n    return resolved;\n  }\n}\nclass DirectiveResolver extends OverrideResolver {\n  get type() {\n    return Directive;\n  }\n}\nclass ComponentResolver extends OverrideResolver {\n  get type() {\n    return Component;\n  }\n}\nclass PipeResolver extends OverrideResolver {\n  get type() {\n    return Pipe;\n  }\n}\nclass NgModuleResolver extends OverrideResolver {\n  get type() {\n    return NgModule;\n  }\n}\nvar TestingModuleOverride;\n(function (TestingModuleOverride) {\n  TestingModuleOverride[TestingModuleOverride[\"DECLARATION\"] = 0] = \"DECLARATION\";\n  TestingModuleOverride[TestingModuleOverride[\"OVERRIDE_TEMPLATE\"] = 1] = \"OVERRIDE_TEMPLATE\";\n})(TestingModuleOverride || (TestingModuleOverride = {}));\nfunction isTestingModuleOverride(value) {\n  return value === TestingModuleOverride.DECLARATION || value === TestingModuleOverride.OVERRIDE_TEMPLATE;\n}\nfunction assertNoStandaloneComponents(types, resolver, location) {\n  types.forEach(type => {\n    if (!_getAsyncClassMetadataFn(type)) {\n      const component = resolver.resolve(type);\n      if (component && (component.standalone == null || component.standalone)) {\n        throw new Error(_generateStandaloneInDeclarationsError(type, location));\n      }\n    }\n  });\n}\nclass TestBedCompiler {\n  constructor(platform, additionalModuleTypes) {\n    _defineProperty(this, \"platform\", void 0);\n    _defineProperty(this, \"additionalModuleTypes\", void 0);\n    _defineProperty(this, \"originalComponentResolutionQueue\", null);\n    // Testing module configuration\n    _defineProperty(this, \"declarations\", []);\n    _defineProperty(this, \"imports\", []);\n    _defineProperty(this, \"providers\", []);\n    _defineProperty(this, \"schemas\", []);\n    // Queues of components/directives/pipes that should be recompiled.\n    _defineProperty(this, \"pendingComponents\", new Set());\n    _defineProperty(this, \"pendingDirectives\", new Set());\n    _defineProperty(this, \"pendingPipes\", new Set());\n    // Set of components with async metadata, i.e. components with `@defer` blocks\n    // in their templates.\n    _defineProperty(this, \"componentsWithAsyncMetadata\", new Set());\n    // Keep track of all components and directives, so we can patch Providers onto defs later.\n    _defineProperty(this, \"seenComponents\", new Set());\n    _defineProperty(this, \"seenDirectives\", new Set());\n    // Keep track of overridden modules, so that we can collect all affected ones in the module tree.\n    _defineProperty(this, \"overriddenModules\", new Set());\n    // Store resolved styles for Components that have template overrides present and `styleUrls`\n    // defined at the same time.\n    _defineProperty(this, \"existingComponentStyles\", new Map());\n    _defineProperty(this, \"resolvers\", initResolvers());\n    // Map of component type to an NgModule that declares it.\n    //\n    // There are a couple special cases:\n    // - for standalone components, the module scope value is `null`\n    // - when a component is declared in `TestBed.configureTestingModule()` call or\n    //   a component's template is overridden via `TestBed.overrideTemplateUsingTestingModule()`.\n    //   we use a special value from the `TestingModuleOverride` enum.\n    _defineProperty(this, \"componentToModuleScope\", new Map());\n    // Map that keeps initial version of component/directive/pipe defs in case\n    // we compile a Type again, thus overriding respective static fields. This is\n    // required to make sure we restore defs to their initial states between test runs.\n    // Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of an\n    // NgModule), store all of them in a map.\n    _defineProperty(this, \"initialNgDefs\", new Map());\n    // Array that keeps cleanup operations for initial versions of component/directive/pipe/module\n    // defs in case TestBed makes changes to the originals.\n    _defineProperty(this, \"defCleanupOps\", []);\n    _defineProperty(this, \"_injector\", null);\n    _defineProperty(this, \"compilerProviders\", null);\n    _defineProperty(this, \"providerOverrides\", []);\n    _defineProperty(this, \"rootProviderOverrides\", []);\n    // Overrides for injectables with `{providedIn: SomeModule}` need to be tracked and added to that\n    // module's provider list.\n    _defineProperty(this, \"providerOverridesByModule\", new Map());\n    _defineProperty(this, \"providerOverridesByToken\", new Map());\n    _defineProperty(this, \"scopesWithOverriddenProviders\", new Set());\n    _defineProperty(this, \"testModuleType\", void 0);\n    _defineProperty(this, \"testModuleRef\", null);\n    _defineProperty(this, \"deferBlockBehavior\", DEFER_BLOCK_DEFAULT_BEHAVIOR);\n    _defineProperty(this, \"rethrowApplicationTickErrors\", RETHROW_APPLICATION_ERRORS_DEFAULT);\n    this.platform = platform;\n    this.additionalModuleTypes = additionalModuleTypes;\n    class DynamicTestModule {}\n    this.testModuleType = DynamicTestModule;\n  }\n  setCompilerProviders(providers) {\n    this.compilerProviders = providers;\n    this._injector = null;\n  }\n  configureTestingModule(moduleDef) {\n    var _moduleDef$deferBlock, _moduleDef$rethrowApp;\n    // Enqueue any compilation tasks for the directly declared component.\n    if (moduleDef.declarations !== undefined) {\n      // Verify that there are no standalone components\n      assertNoStandaloneComponents(moduleDef.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n      this.queueTypeArray(moduleDef.declarations, TestingModuleOverride.DECLARATION);\n      this.declarations.push(...moduleDef.declarations);\n    }\n    // Enqueue any compilation tasks for imported modules.\n    if (moduleDef.imports !== undefined) {\n      this.queueTypesFromModulesArray(moduleDef.imports);\n      this.imports.push(...moduleDef.imports);\n    }\n    if (moduleDef.providers !== undefined) {\n      this.providers.push(...moduleDef.providers);\n    }\n    if (moduleDef.schemas !== undefined) {\n      this.schemas.push(...moduleDef.schemas);\n    }\n    this.deferBlockBehavior = (_moduleDef$deferBlock = moduleDef.deferBlockBehavior) !== null && _moduleDef$deferBlock !== void 0 ? _moduleDef$deferBlock : DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    this.rethrowApplicationTickErrors = (_moduleDef$rethrowApp = moduleDef.rethrowApplicationErrors) !== null && _moduleDef$rethrowApp !== void 0 ? _moduleDef$rethrowApp : RETHROW_APPLICATION_ERRORS_DEFAULT;\n  }\n  overrideModule(ngModule, override) {\n    if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n      _depsTracker.clearScopeCacheFor(ngModule);\n    }\n    this.overriddenModules.add(ngModule);\n    // Compile the module right away.\n    this.resolvers.module.addOverride(ngModule, override);\n    const metadata = this.resolvers.module.resolve(ngModule);\n    if (metadata === null) {\n      throw invalidTypeError(ngModule.name, 'NgModule');\n    }\n    this.recompileNgModule(ngModule, metadata);\n    // At this point, the module has a valid module def (ɵmod), but the override may have introduced\n    // new declarations or imported modules. Ingest any possible new types and add them to the\n    // current queue.\n    this.queueTypesFromModulesArray([ngModule]);\n  }\n  overrideComponent(component, override) {\n    this.verifyNoStandaloneFlagOverrides(component, override);\n    this.resolvers.component.addOverride(component, override);\n    this.pendingComponents.add(component);\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(component);\n  }\n  overrideDirective(directive, override) {\n    this.verifyNoStandaloneFlagOverrides(directive, override);\n    this.resolvers.directive.addOverride(directive, override);\n    this.pendingDirectives.add(directive);\n  }\n  overridePipe(pipe, override) {\n    this.verifyNoStandaloneFlagOverrides(pipe, override);\n    this.resolvers.pipe.addOverride(pipe, override);\n    this.pendingPipes.add(pipe);\n  }\n  verifyNoStandaloneFlagOverrides(type, override) {\n    var _override$add, _override$set, _override$remove;\n    if ((_override$add = override.add) !== null && _override$add !== void 0 && _override$add.hasOwnProperty('standalone') || (_override$set = override.set) !== null && _override$set !== void 0 && _override$set.hasOwnProperty('standalone') || (_override$remove = override.remove) !== null && _override$remove !== void 0 && _override$remove.hasOwnProperty('standalone')) {\n      throw new Error(`An override for the ${type.name} class has the \\`standalone\\` flag. ` + `Changing the \\`standalone\\` flag via TestBed overrides is not supported.`);\n    }\n  }\n  overrideProvider(token, provider) {\n    let providerDef;\n    if (provider.useFactory !== undefined) {\n      providerDef = {\n        provide: token,\n        useFactory: provider.useFactory,\n        deps: provider.deps || [],\n        multi: provider.multi\n      };\n    } else if (provider.useValue !== undefined) {\n      providerDef = {\n        provide: token,\n        useValue: provider.useValue,\n        multi: provider.multi\n      };\n    } else {\n      providerDef = {\n        provide: token\n      };\n    }\n    const injectableDef = typeof token !== 'string' ? _getInjectableDef(token) : null;\n    const providedIn = injectableDef === null ? null : resolveForwardRef(injectableDef.providedIn);\n    const overridesBucket = providedIn === 'root' ? this.rootProviderOverrides : this.providerOverrides;\n    overridesBucket.push(providerDef);\n    // Keep overrides grouped by token as well for fast lookups using token\n    this.providerOverridesByToken.set(token, providerDef);\n    if (injectableDef !== null && providedIn !== null && typeof providedIn !== 'string') {\n      const existingOverrides = this.providerOverridesByModule.get(providedIn);\n      if (existingOverrides !== undefined) {\n        existingOverrides.push(providerDef);\n      } else {\n        this.providerOverridesByModule.set(providedIn, [providerDef]);\n      }\n    }\n  }\n  overrideTemplateUsingTestingModule(type, template) {\n    const def = type[_NG_COMP_DEF];\n    const hasStyleUrls = () => {\n      var _metadata$styleUrls;\n      const metadata = this.resolvers.component.resolve(type);\n      return !!metadata.styleUrl || !!((_metadata$styleUrls = metadata.styleUrls) !== null && _metadata$styleUrls !== void 0 && _metadata$styleUrls.length);\n    };\n    const overrideStyleUrls = !!def && !_isComponentDefPendingResolution(type) && hasStyleUrls();\n    // In Ivy, compiling a component does not require knowing the module providing the\n    // component's scope, so overrideTemplateUsingTestingModule can be implemented purely via\n    // overrideComponent. Important: overriding template requires full Component re-compilation,\n    // which may fail in case styleUrls are also present (thus Component is considered as required\n    // resolution). In order to avoid this, we preemptively set styleUrls to an empty array,\n    // preserve current styles available on Component def and restore styles back once compilation\n    // is complete.\n    const override = overrideStyleUrls ? {\n      template,\n      styles: [],\n      styleUrls: [],\n      styleUrl: undefined\n    } : {\n      template\n    };\n    this.overrideComponent(type, {\n      set: override\n    });\n    if (overrideStyleUrls && def.styles && def.styles.length > 0) {\n      this.existingComponentStyles.set(type, def.styles);\n    }\n    // Set the component's scope to be the testing module.\n    this.componentToModuleScope.set(type, TestingModuleOverride.OVERRIDE_TEMPLATE);\n  }\n  resolvePendingComponentsWithAsyncMetadata() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.componentsWithAsyncMetadata.size === 0) return;\n      const promises = [];\n      for (const component of _this2.componentsWithAsyncMetadata) {\n        const asyncMetadataFn = _getAsyncClassMetadataFn(component);\n        if (asyncMetadataFn) {\n          promises.push(asyncMetadataFn());\n        }\n      }\n      _this2.componentsWithAsyncMetadata.clear();\n      const resolvedDeps = yield Promise.all(promises);\n      const flatResolvedDeps = resolvedDeps.flat(2);\n      _this2.queueTypesFromModulesArray(flatResolvedDeps);\n      // Loaded standalone components might contain imports of NgModules\n      // with providers, make sure we override providers there too.\n      for (const component of flatResolvedDeps) {\n        _this2.applyProviderOverridesInScope(component);\n      }\n    })();\n  }\n  compileComponents() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.clearComponentResolutionQueue();\n      // Wait for all async metadata for components that were\n      // overridden, we need resolved metadata to perform an override\n      // and re-compile a component.\n      yield _this3.resolvePendingComponentsWithAsyncMetadata();\n      // Verify that there were no standalone components present in the `declarations` field\n      // during the `TestBed.configureTestingModule` call. We perform this check here in addition\n      // to the logic in the `configureTestingModule` function, since at this point we have\n      // all async metadata resolved.\n      assertNoStandaloneComponents(_this3.declarations, _this3.resolvers.component, '\"TestBed.configureTestingModule\" call');\n      // Run compilers for all queued types.\n      let needsAsyncResources = _this3.compileTypesSync();\n      // compileComponents() should not be async unless it needs to be.\n      if (needsAsyncResources) {\n        let resourceLoader;\n        let resolver = url => {\n          if (!resourceLoader) {\n            resourceLoader = _this3.injector.get(ResourceLoader);\n          }\n          return Promise.resolve(resourceLoader.get(url));\n        };\n        yield _resolveComponentResources(resolver);\n      }\n    })();\n  }\n  finalize() {\n    // One last compile\n    this.compileTypesSync();\n    // Create the testing module itself.\n    this.compileTestModule();\n    this.applyTransitiveScopes();\n    this.applyProviderOverrides();\n    // Patch previously stored `styles` Component values (taken from ɵcmp), in case these\n    // Components have `styleUrls` fields defined and template override was requested.\n    this.patchComponentsWithExistingStyles();\n    // Clear the componentToModuleScope map, so that future compilations don't reset the scope of\n    // every component.\n    this.componentToModuleScope.clear();\n    const parentInjector = this.platform.injector;\n    this.testModuleRef = new _Render3NgModuleRef(this.testModuleType, parentInjector, []);\n    // ApplicationInitStatus.runInitializers() is marked @internal to core.\n    // Cast it to any before accessing it.\n    this.testModuleRef.injector.get(ApplicationInitStatus).runInitializers();\n    // Set locale ID after running app initializers, since locale information might be updated while\n    // running initializers. This is also consistent with the execution order while bootstrapping an\n    // app (see `packages/core/src/application_ref.ts` file).\n    const localeId = this.testModuleRef.injector.get(LOCALE_ID, _DEFAULT_LOCALE_ID);\n    _setLocaleId(localeId);\n    return this.testModuleRef;\n  }\n  /**\n   * @internal\n   */\n  _compileNgModuleSync(moduleType) {\n    this.queueTypesFromModulesArray([moduleType]);\n    this.compileTypesSync();\n    this.applyProviderOverrides();\n    this.applyProviderOverridesInScope(moduleType);\n    this.applyTransitiveScopes();\n  }\n  /**\n   * @internal\n   */\n  _compileNgModuleAsync(moduleType) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.queueTypesFromModulesArray([moduleType]);\n      yield _this4.compileComponents();\n      _this4.applyProviderOverrides();\n      _this4.applyProviderOverridesInScope(moduleType);\n      _this4.applyTransitiveScopes();\n    })();\n  }\n  /**\n   * @internal\n   */\n  _getModuleResolver() {\n    return this.resolvers.module;\n  }\n  /**\n   * @internal\n   */\n  _getComponentFactories(moduleType) {\n    return maybeUnwrapFn(moduleType.ɵmod.declarations).reduce((factories, declaration) => {\n      const componentDef = declaration.ɵcmp;\n      componentDef && factories.push(new _Render3ComponentFactory(componentDef, this.testModuleRef));\n      return factories;\n    }, []);\n  }\n  compileTypesSync() {\n    // Compile all queued components, directives, pipes.\n    let needsAsyncResources = false;\n    this.pendingComponents.forEach(declaration => {\n      if (_getAsyncClassMetadataFn(declaration)) {\n        throw new Error(`Component '${declaration.name}' has unresolved metadata. ` + `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n      }\n      needsAsyncResources = needsAsyncResources || _isComponentDefPendingResolution(declaration);\n      const metadata = this.resolvers.component.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Component');\n      }\n      this.maybeStoreNgDef(_NG_COMP_DEF, declaration);\n      if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n        _depsTracker.clearScopeCacheFor(declaration);\n      }\n      _compileComponent(declaration, metadata);\n    });\n    this.pendingComponents.clear();\n    this.pendingDirectives.forEach(declaration => {\n      const metadata = this.resolvers.directive.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Directive');\n      }\n      this.maybeStoreNgDef(_NG_DIR_DEF, declaration);\n      _compileDirective(declaration, metadata);\n    });\n    this.pendingDirectives.clear();\n    this.pendingPipes.forEach(declaration => {\n      const metadata = this.resolvers.pipe.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Pipe');\n      }\n      this.maybeStoreNgDef(_NG_PIPE_DEF, declaration);\n      _compilePipe(declaration, metadata);\n    });\n    this.pendingPipes.clear();\n    return needsAsyncResources;\n  }\n  applyTransitiveScopes() {\n    if (this.overriddenModules.size > 0) {\n      // Module overrides (via `TestBed.overrideModule`) might affect scopes that were previously\n      // calculated and stored in `transitiveCompileScopes`. If module overrides are present,\n      // collect all affected modules and reset scopes to force their re-calculation.\n      const testingModuleDef = this.testModuleType[_NG_MOD_DEF];\n      const affectedModules = this.collectModulesAffectedByOverrides(testingModuleDef.imports);\n      if (affectedModules.size > 0) {\n        affectedModules.forEach(moduleType => {\n          if (!_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n            this.storeFieldOfDefOnType(moduleType, _NG_MOD_DEF, 'transitiveCompileScopes');\n            moduleType[_NG_MOD_DEF].transitiveCompileScopes = null;\n          } else {\n            _depsTracker.clearScopeCacheFor(moduleType);\n          }\n        });\n      }\n    }\n    const moduleToScope = new Map();\n    const getScopeOfModule = moduleType => {\n      if (!moduleToScope.has(moduleType)) {\n        const isTestingModule = isTestingModuleOverride(moduleType);\n        const realType = isTestingModule ? this.testModuleType : moduleType;\n        moduleToScope.set(moduleType, _transitiveScopesFor(realType));\n      }\n      return moduleToScope.get(moduleType);\n    };\n    this.componentToModuleScope.forEach((moduleType, componentType) => {\n      if (moduleType !== null) {\n        const moduleScope = getScopeOfModule(moduleType);\n        this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'directiveDefs');\n        this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'pipeDefs');\n        _patchComponentDefWithScope(getComponentDef(componentType), moduleScope);\n      }\n      // `tView` that is stored on component def contains information about directives and pipes\n      // that are in the scope of this component. Patching component scope will cause `tView` to be\n      // changed. Store original `tView` before patching scope, so the `tView` (including scope\n      // information) is restored back to its previous/original state before running next test.\n      // Resetting `tView` is also needed for cases when we apply provider overrides and those\n      // providers are defined on component's level, in which case they may end up included into\n      // `tView.blueprint`.\n      this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'tView');\n    });\n    this.componentToModuleScope.clear();\n  }\n  applyProviderOverrides() {\n    const maybeApplyOverrides = field => type => {\n      const resolver = field === _NG_COMP_DEF ? this.resolvers.component : this.resolvers.directive;\n      const metadata = resolver.resolve(type);\n      if (this.hasProviderOverrides(metadata.providers)) {\n        this.patchDefWithProviderOverrides(type, field);\n      }\n    };\n    this.seenComponents.forEach(maybeApplyOverrides(_NG_COMP_DEF));\n    this.seenDirectives.forEach(maybeApplyOverrides(_NG_DIR_DEF));\n    this.seenComponents.clear();\n    this.seenDirectives.clear();\n  }\n  /**\n   * Applies provider overrides to a given type (either an NgModule or a standalone component)\n   * and all imported NgModules and standalone components recursively.\n   */\n  applyProviderOverridesInScope(type) {\n    const hasScope = isStandaloneComponent(type) || isNgModule(type);\n    // The function can be re-entered recursively while inspecting dependencies\n    // of an NgModule or a standalone component. Exit early if we come across a\n    // type that can not have a scope (directive or pipe) or the type is already\n    // processed earlier.\n    if (!hasScope || this.scopesWithOverriddenProviders.has(type)) {\n      return;\n    }\n    this.scopesWithOverriddenProviders.add(type);\n    // NOTE: the line below triggers JIT compilation of the module injector,\n    // which also invokes verification of the NgModule semantics, which produces\n    // detailed error messages. The fact that the code relies on this line being\n    // present here is suspicious and should be refactored in a way that the line\n    // below can be moved (for ex. after an early exit check below).\n    const injectorDef = type[_NG_INJ_DEF];\n    // No provider overrides, exit early.\n    if (this.providerOverridesByToken.size === 0) return;\n    if (isStandaloneComponent(type)) {\n      var _def$dependencies;\n      // Visit all component dependencies and override providers there.\n      const def = getComponentDef(type);\n      const dependencies = maybeUnwrapFn((_def$dependencies = def.dependencies) !== null && _def$dependencies !== void 0 ? _def$dependencies : []);\n      for (const dependency of dependencies) {\n        this.applyProviderOverridesInScope(dependency);\n      }\n    } else {\n      const providers = [...injectorDef.providers, ...(this.providerOverridesByModule.get(type) || [])];\n      if (this.hasProviderOverrides(providers)) {\n        this.maybeStoreNgDef(_NG_INJ_DEF, type);\n        this.storeFieldOfDefOnType(type, _NG_INJ_DEF, 'providers');\n        injectorDef.providers = this.getOverriddenProviders(providers);\n      }\n      // Apply provider overrides to imported modules recursively\n      const moduleDef = type[_NG_MOD_DEF];\n      const imports = maybeUnwrapFn(moduleDef.imports);\n      for (const importedModule of imports) {\n        this.applyProviderOverridesInScope(importedModule);\n      }\n      // Also override the providers on any ModuleWithProviders imports since those don't appear in\n      // the moduleDef.\n      for (const importedModule of flatten(injectorDef.imports)) {\n        if (isModuleWithProviders(importedModule)) {\n          this.defCleanupOps.push({\n            object: importedModule,\n            fieldName: 'providers',\n            originalValue: importedModule.providers\n          });\n          importedModule.providers = this.getOverriddenProviders(importedModule.providers);\n        }\n      }\n    }\n  }\n  patchComponentsWithExistingStyles() {\n    this.existingComponentStyles.forEach((styles, type) => type[_NG_COMP_DEF].styles = styles);\n    this.existingComponentStyles.clear();\n  }\n  queueTypeArray(arr, moduleType) {\n    for (const value of arr) {\n      if (Array.isArray(value)) {\n        this.queueTypeArray(value, moduleType);\n      } else {\n        this.queueType(value, moduleType);\n      }\n    }\n  }\n  recompileNgModule(ngModule, metadata) {\n    // Cache the initial ngModuleDef as it will be overwritten.\n    this.maybeStoreNgDef(_NG_MOD_DEF, ngModule);\n    this.maybeStoreNgDef(_NG_INJ_DEF, ngModule);\n    _compileNgModuleDefs(ngModule, metadata);\n  }\n  maybeRegisterComponentWithAsyncMetadata(type) {\n    const asyncMetadataFn = _getAsyncClassMetadataFn(type);\n    if (asyncMetadataFn) {\n      this.componentsWithAsyncMetadata.add(type);\n    }\n  }\n  queueType(type, moduleType) {\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(type);\n    const component = this.resolvers.component.resolve(type);\n    if (component) {\n      // Check whether a give Type has respective NG def (ɵcmp) and compile if def is\n      // missing. That might happen in case a class without any Angular decorators extends another\n      // class where Component/Directive/Pipe decorator is defined.\n      if (_isComponentDefPendingResolution(type) || !type.hasOwnProperty(_NG_COMP_DEF)) {\n        this.pendingComponents.add(type);\n      }\n      this.seenComponents.add(type);\n      // Keep track of the module which declares this component, so later the component's scope\n      // can be set correctly. If the component has already been recorded here, then one of several\n      // cases is true:\n      // * the module containing the component was imported multiple times (common).\n      // * the component is declared in multiple modules (which is an error).\n      // * the component was in 'declarations' of the testing module, and also in an imported module\n      //   in which case the module scope will be TestingModuleOverride.DECLARATION.\n      // * overrideTemplateUsingTestingModule was called for the component in which case the module\n      //   scope will be TestingModuleOverride.OVERRIDE_TEMPLATE.\n      //\n      // If the component was previously in the testing module's 'declarations' (meaning the\n      // current value is TestingModuleOverride.DECLARATION), then `moduleType` is the component's\n      // real module, which was imported. This pattern is understood to mean that the component\n      // should use its original scope, but that the testing module should also contain the\n      // component in its scope.\n      if (!this.componentToModuleScope.has(type) || this.componentToModuleScope.get(type) === TestingModuleOverride.DECLARATION) {\n        this.componentToModuleScope.set(type, moduleType);\n      }\n      return;\n    }\n    const directive = this.resolvers.directive.resolve(type);\n    if (directive) {\n      if (!type.hasOwnProperty(_NG_DIR_DEF)) {\n        this.pendingDirectives.add(type);\n      }\n      this.seenDirectives.add(type);\n      return;\n    }\n    const pipe = this.resolvers.pipe.resolve(type);\n    if (pipe && !type.hasOwnProperty(_NG_PIPE_DEF)) {\n      this.pendingPipes.add(type);\n      return;\n    }\n  }\n  queueTypesFromModulesArray(arr) {\n    // Because we may encounter the same NgModule or a standalone Component while processing\n    // the dependencies of an NgModule or a standalone Component, we cache them in this set so we\n    // can skip ones that have already been seen encountered. In some test setups, this caching\n    // resulted in 10X runtime improvement.\n    const processedDefs = new Set();\n    const queueTypesFromModulesArrayRecur = arr => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          queueTypesFromModulesArrayRecur(value);\n        } else if (hasNgModuleDef(value)) {\n          const def = value.ɵmod;\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n          // Look through declarations, imports, and exports, and queue\n          // everything found there.\n          this.queueTypeArray(maybeUnwrapFn(def.declarations), value);\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.imports));\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.exports));\n        } else if (isModuleWithProviders(value)) {\n          queueTypesFromModulesArrayRecur([value.ngModule]);\n        } else if (isStandaloneComponent(value)) {\n          var _def$dependencies2;\n          this.queueType(value, null);\n          const def = getComponentDef(value);\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n          const dependencies = maybeUnwrapFn((_def$dependencies2 = def.dependencies) !== null && _def$dependencies2 !== void 0 ? _def$dependencies2 : []);\n          dependencies.forEach(dependency => {\n            // Note: in AOT, the `dependencies` might also contain regular\n            // (NgModule-based) Component, Directive and Pipes, so we handle\n            // them separately and proceed with recursive process for standalone\n            // Components and NgModules only.\n            if (isStandaloneComponent(dependency) || hasNgModuleDef(dependency)) {\n              queueTypesFromModulesArrayRecur([dependency]);\n            } else {\n              this.queueType(dependency, null);\n            }\n          });\n        }\n      }\n    };\n    queueTypesFromModulesArrayRecur(arr);\n  }\n  // When module overrides (via `TestBed.overrideModule`) are present, it might affect all modules\n  // that import (even transitively) an overridden one. For all affected modules we need to\n  // recalculate their scopes for a given test run and restore original scopes at the end. The goal\n  // of this function is to collect all affected modules in a set for further processing. Example:\n  // if we have the following module hierarchy: A -> B -> C (where `->` means `imports`) and module\n  // `C` is overridden, we consider `A` and `B` as affected, since their scopes might become\n  // invalidated with the override.\n  collectModulesAffectedByOverrides(arr) {\n    const seenModules = new Set();\n    const affectedModules = new Set();\n    const calcAffectedModulesRecur = (arr, path) => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          // If the value is an array, just flatten it (by invoking this function recursively),\n          // keeping \"path\" the same.\n          calcAffectedModulesRecur(value, path);\n        } else if (hasNgModuleDef(value)) {\n          if (seenModules.has(value)) {\n            // If we've seen this module before and it's included into \"affected modules\" list, mark\n            // the whole path that leads to that module as affected, but do not descend into its\n            // imports, since we already examined them before.\n            if (affectedModules.has(value)) {\n              path.forEach(item => affectedModules.add(item));\n            }\n            continue;\n          }\n          seenModules.add(value);\n          if (this.overriddenModules.has(value)) {\n            path.forEach(item => affectedModules.add(item));\n          }\n          // Examine module imports recursively to look for overridden modules.\n          const moduleDef = value[_NG_MOD_DEF];\n          calcAffectedModulesRecur(maybeUnwrapFn(moduleDef.imports), path.concat(value));\n        }\n      }\n    };\n    calcAffectedModulesRecur(arr, []);\n    return affectedModules;\n  }\n  /**\n   * Preserve an original def (such as ɵmod, ɵinj, etc) before applying an override.\n   * Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of\n   * an NgModule). If there is a def in a set already, don't override it, since\n   * an original one should be restored at the end of a test.\n   */\n  maybeStoreNgDef(prop, type) {\n    if (!this.initialNgDefs.has(type)) {\n      this.initialNgDefs.set(type, new Map());\n    }\n    const currentDefs = this.initialNgDefs.get(type);\n    if (!currentDefs.has(prop)) {\n      const currentDef = Object.getOwnPropertyDescriptor(type, prop);\n      currentDefs.set(prop, currentDef);\n    }\n  }\n  storeFieldOfDefOnType(type, defField, fieldName) {\n    const def = type[defField];\n    const originalValue = def[fieldName];\n    this.defCleanupOps.push({\n      object: def,\n      fieldName,\n      originalValue\n    });\n  }\n  /**\n   * Clears current components resolution queue, but stores the state of the queue, so we can\n   * restore it later. Clearing the queue is required before we try to compile components (via\n   * `TestBed.compileComponents`), so that component defs are in sync with the resolution queue.\n   */\n  clearComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue === null) {\n      this.originalComponentResolutionQueue = new Map();\n    }\n    _clearResolutionOfComponentResourcesQueue().forEach((value, key) => this.originalComponentResolutionQueue.set(key, value));\n  }\n  /*\n   * Restores component resolution queue to the previously saved state. This operation is performed\n   * as a part of restoring the state after completion of the current set of tests (that might\n   * potentially mutate the state).\n   */\n  restoreComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue !== null) {\n      _restoreComponentResolutionQueue(this.originalComponentResolutionQueue);\n      this.originalComponentResolutionQueue = null;\n    }\n  }\n  restoreOriginalState() {\n    // Process cleanup ops in reverse order so the field's original value is restored correctly (in\n    // case there were multiple overrides for the same field).\n    forEachRight(this.defCleanupOps, op => {\n      op.object[op.fieldName] = op.originalValue;\n    });\n    // Restore initial component/directive/pipe defs\n    this.initialNgDefs.forEach((defs, type) => {\n      if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n        _depsTracker.clearScopeCacheFor(type);\n      }\n      defs.forEach((descriptor, prop) => {\n        if (!descriptor) {\n          // Delete operations are generally undesirable since they have performance\n          // implications on objects they were applied to. In this particular case, situations\n          // where this code is invoked should be quite rare to cause any noticeable impact,\n          // since it's applied only to some test cases (for example when class with no\n          // annotations extends some @Component) when we need to clear 'ɵcmp' field on a given\n          // class to restore its original state (before applying overrides and running tests).\n          delete type[prop];\n        } else {\n          Object.defineProperty(type, prop, descriptor);\n        }\n      });\n    });\n    this.initialNgDefs.clear();\n    this.scopesWithOverriddenProviders.clear();\n    this.restoreComponentResolutionQueue();\n    // Restore the locale ID to the default value, this shouldn't be necessary but we never know\n    _setLocaleId(_DEFAULT_LOCALE_ID);\n  }\n  compileTestModule() {\n    class RootScopeModule {}\n    _compileNgModuleDefs(RootScopeModule, {\n      providers: [...this.rootProviderOverrides, _internalProvideZoneChangeDetection({}), TestBedApplicationErrorHandler, {\n        provide: _ChangeDetectionScheduler,\n        useExisting: _ChangeDetectionSchedulerImpl\n      }]\n    });\n    const providers = [{\n      provide: Compiler,\n      useFactory: () => new R3TestCompiler(this)\n    }, {\n      provide: _DEFER_BLOCK_CONFIG,\n      useValue: {\n        behavior: this.deferBlockBehavior\n      }\n    }, {\n      provide: _INTERNAL_APPLICATION_ERROR_HANDLER,\n      useFactory: () => {\n        if (this.rethrowApplicationTickErrors) {\n          const handler = inject$1(TestBedApplicationErrorHandler);\n          return e => {\n            handler.handleError(e);\n          };\n        } else {\n          const userErrorHandler = inject$1(ErrorHandler);\n          const ngZone = inject$1(NgZone);\n          return e => ngZone.runOutsideAngular(() => userErrorHandler.handleError(e));\n        }\n      }\n    }, ...this.providers, ...this.providerOverrides];\n    const imports = [RootScopeModule, this.additionalModuleTypes, this.imports || []];\n    _compileNgModuleDefs(this.testModuleType, {\n      declarations: this.declarations,\n      imports,\n      schemas: this.schemas,\n      providers\n    }, /* allowDuplicateDeclarationsInRoot */true);\n    this.applyProviderOverridesInScope(this.testModuleType);\n  }\n  get injector() {\n    if (this._injector !== null) {\n      return this._injector;\n    }\n    const providers = [];\n    const compilerOptions = this.platform.injector.get(COMPILER_OPTIONS, []);\n    compilerOptions.forEach(opts => {\n      if (opts.providers) {\n        providers.push(opts.providers);\n      }\n    });\n    if (this.compilerProviders !== null) {\n      providers.push(...this.compilerProviders);\n    }\n    this._injector = Injector.create({\n      providers,\n      parent: this.platform.injector\n    });\n    return this._injector;\n  }\n  // get overrides for a specific provider (if any)\n  getSingleProviderOverrides(provider) {\n    const token = getProviderToken(provider);\n    return this.providerOverridesByToken.get(token) || null;\n  }\n  getProviderOverrides(providers) {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n    // There are two flattening operations here. The inner flattenProviders() operates on the\n    // metadata's providers and applies a mapping function which retrieves overrides for each\n    // incoming provider. The outer flatten() then flattens the produced overrides array. If this is\n    // not done, the array can contain other empty arrays (e.g. `[[], []]`) which leak into the\n    // providers array and contaminate any error messages that might be generated.\n    return flatten(flattenProviders(providers, provider => this.getSingleProviderOverrides(provider) || []));\n  }\n  getOverriddenProviders(providers) {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n    const flattenedProviders = flattenProviders(providers);\n    const overrides = this.getProviderOverrides(flattenedProviders);\n    const overriddenProviders = [...flattenedProviders, ...overrides];\n    const final = [];\n    const seenOverriddenProviders = new Set();\n    // We iterate through the list of providers in reverse order to make sure provider overrides\n    // take precedence over the values defined in provider list. We also filter out all providers\n    // that have overrides, keeping overridden values only. This is needed, since presence of a\n    // provider with `ngOnDestroy` hook will cause this hook to be registered and invoked later.\n    forEachRight(overriddenProviders, provider => {\n      const token = getProviderToken(provider);\n      if (this.providerOverridesByToken.has(token)) {\n        if (!seenOverriddenProviders.has(token)) {\n          seenOverriddenProviders.add(token);\n          // Treat all overridden providers as `{multi: false}` (even if it's a multi-provider) to\n          // make sure that provided override takes highest precedence and is not combined with\n          // other instances of the same multi provider.\n          final.unshift(_objectSpread(_objectSpread({}, provider), {}, {\n            multi: false\n          }));\n        }\n      } else {\n        final.unshift(provider);\n      }\n    });\n    return final;\n  }\n  hasProviderOverrides(providers) {\n    return this.getProviderOverrides(providers).length > 0;\n  }\n  patchDefWithProviderOverrides(declaration, field) {\n    const def = declaration[field];\n    if (def && def.providersResolver) {\n      this.maybeStoreNgDef(field, declaration);\n      const resolver = def.providersResolver;\n      const processProvidersFn = providers => this.getOverriddenProviders(providers);\n      this.storeFieldOfDefOnType(declaration, field, 'providersResolver');\n      def.providersResolver = ngDef => resolver(ngDef, processProvidersFn);\n    }\n  }\n}\nfunction initResolvers() {\n  return {\n    module: new NgModuleResolver(),\n    component: new ComponentResolver(),\n    directive: new DirectiveResolver(),\n    pipe: new PipeResolver()\n  };\n}\nfunction isStandaloneComponent(value) {\n  const def = getComponentDef(value);\n  return !!(def !== null && def !== void 0 && def.standalone);\n}\nfunction getComponentDef(value) {\n  var _value$ɵcmp;\n  return (_value$ɵcmp = value.ɵcmp) !== null && _value$ɵcmp !== void 0 ? _value$ɵcmp : null;\n}\nfunction hasNgModuleDef(value) {\n  return value.hasOwnProperty('ɵmod');\n}\nfunction isNgModule(value) {\n  return hasNgModuleDef(value);\n}\nfunction maybeUnwrapFn(maybeFn) {\n  return maybeFn instanceof Function ? maybeFn() : maybeFn;\n}\nfunction flatten(values) {\n  const out = [];\n  values.forEach(value => {\n    if (Array.isArray(value)) {\n      out.push(...flatten(value));\n    } else {\n      out.push(value);\n    }\n  });\n  return out;\n}\nfunction identityFn(value) {\n  return value;\n}\nfunction flattenProviders(providers, mapFn = identityFn) {\n  const out = [];\n  for (let provider of providers) {\n    if (_isEnvironmentProviders(provider)) {\n      provider = provider.ɵproviders;\n    }\n    if (Array.isArray(provider)) {\n      out.push(...flattenProviders(provider, mapFn));\n    } else {\n      out.push(mapFn(provider));\n    }\n  }\n  return out;\n}\nfunction getProviderField(provider, field) {\n  return provider && typeof provider === 'object' && provider[field];\n}\nfunction getProviderToken(provider) {\n  return getProviderField(provider, 'provide') || provider;\n}\nfunction isModuleWithProviders(value) {\n  return value.hasOwnProperty('ngModule');\n}\nfunction forEachRight(values, fn) {\n  for (let idx = values.length - 1; idx >= 0; idx--) {\n    fn(values[idx], idx);\n  }\n}\nfunction invalidTypeError(name, expectedType) {\n  return new Error(`${name} class doesn't have @${expectedType} decorator or is missing metadata.`);\n}\nclass R3TestCompiler {\n  constructor(testBed) {\n    _defineProperty(this, \"testBed\", void 0);\n    this.testBed = testBed;\n  }\n  compileModuleSync(moduleType) {\n    this.testBed._compileNgModuleSync(moduleType);\n    return new _NgModuleFactory(moduleType);\n  }\n  compileModuleAsync(moduleType) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      yield _this5.testBed._compileNgModuleAsync(moduleType);\n      return new _NgModuleFactory(moduleType);\n    })();\n  }\n  compileModuleAndAllComponentsSync(moduleType) {\n    const ngModuleFactory = this.compileModuleSync(moduleType);\n    const componentFactories = this.testBed._getComponentFactories(moduleType);\n    return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n  }\n  compileModuleAndAllComponentsAsync(moduleType) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const ngModuleFactory = yield _this6.compileModuleAsync(moduleType);\n      const componentFactories = _this6.testBed._getComponentFactories(moduleType);\n      return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    })();\n  }\n  clearCache() {}\n  clearCacheFor(type) {}\n  getModuleId(moduleType) {\n    const meta = this.testBed._getModuleResolver().resolve(moduleType);\n    return meta && meta.id || undefined;\n  }\n}\n\n// The formatter and CI disagree on how this import statement should be formatted. Both try to keep\n// it on one line, too, which has gotten very hard to read & manage. So disable the formatter for\n// this statement only.\nlet _nextRootElementId = 0;\n/**\n * Returns a singleton of the `TestBed` class.\n *\n * @publicApi\n */\nfunction getTestBed() {\n  return TestBedImpl.INSTANCE;\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n */\nclass TestBedImpl {\n  constructor() {\n    /**\n     * Teardown options that have been configured at the `TestBed` instance level.\n     * These options take precedence over the environment-level ones.\n     */\n    _defineProperty(this, \"_instanceTeardownOptions\", void 0);\n    /**\n     * Defer block behavior option that specifies whether defer blocks will be triggered manually\n     * or set to play through.\n     */\n    _defineProperty(this, \"_instanceDeferBlockBehavior\", DEFER_BLOCK_DEFAULT_BEHAVIOR);\n    /**\n     * \"Error on unknown elements\" option that has been configured at the `TestBed` instance level.\n     * This option takes precedence over the environment-level one.\n     */\n    _defineProperty(this, \"_instanceErrorOnUnknownElementsOption\", void 0);\n    /**\n     * \"Error on unknown properties\" option that has been configured at the `TestBed` instance level.\n     * This option takes precedence over the environment-level one.\n     */\n    _defineProperty(this, \"_instanceErrorOnUnknownPropertiesOption\", void 0);\n    /**\n     * Stores the previous \"Error on unknown elements\" option value,\n     * allowing to restore it in the reset testing module logic.\n     */\n    _defineProperty(this, \"_previousErrorOnUnknownElementsOption\", void 0);\n    /**\n     * Stores the previous \"Error on unknown properties\" option value,\n     * allowing to restore it in the reset testing module logic.\n     */\n    _defineProperty(this, \"_previousErrorOnUnknownPropertiesOption\", void 0);\n    // Properties\n    _defineProperty(this, \"platform\", null);\n    _defineProperty(this, \"ngModule\", null);\n    _defineProperty(this, \"_compiler\", null);\n    _defineProperty(this, \"_testModuleRef\", null);\n    _defineProperty(this, \"_activeFixtures\", []);\n    /**\n     * Internal-only flag to indicate whether a module\n     * scoping queue has been checked and flushed already.\n     * @docs-private\n     */\n    _defineProperty(this, \"globalCompilationChecked\", false);\n  }\n  static get INSTANCE() {\n    return TestBedImpl._INSTANCE = TestBedImpl._INSTANCE || new TestBedImpl();\n  }\n  /**\n   * Teardown options that have been configured at the environment level.\n   * Used as a fallback if no instance-level options have been provided.\n   */\n\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  static initTestEnvironment(ngModule, platform, options) {\n    const testBed = TestBedImpl.INSTANCE;\n    testBed.initTestEnvironment(ngModule, platform, options);\n    return testBed;\n  }\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  static resetTestEnvironment() {\n    TestBedImpl.INSTANCE.resetTestEnvironment();\n  }\n  static configureCompiler(config) {\n    return TestBedImpl.INSTANCE.configureCompiler(config);\n  }\n  /**\n   * Allows overriding default providers, directives, pipes, modules of the test injector,\n   * which are defined in test_injector.js\n   */\n  static configureTestingModule(moduleDef) {\n    return TestBedImpl.INSTANCE.configureTestingModule(moduleDef);\n  }\n  /**\n   * Compile components with a `templateUrl` for the test's NgModule.\n   * It is necessary to call this function\n   * as fetching urls is asynchronous.\n   */\n  static compileComponents() {\n    return TestBedImpl.INSTANCE.compileComponents();\n  }\n  static overrideModule(ngModule, override) {\n    return TestBedImpl.INSTANCE.overrideModule(ngModule, override);\n  }\n  static overrideComponent(component, override) {\n    return TestBedImpl.INSTANCE.overrideComponent(component, override);\n  }\n  static overrideDirective(directive, override) {\n    return TestBedImpl.INSTANCE.overrideDirective(directive, override);\n  }\n  static overridePipe(pipe, override) {\n    return TestBedImpl.INSTANCE.overridePipe(pipe, override);\n  }\n  static overrideTemplate(component, template) {\n    return TestBedImpl.INSTANCE.overrideTemplate(component, template);\n  }\n  /**\n   * Overrides the template of the given component, compiling the template\n   * in the context of the TestingModule.\n   *\n   * Note: This works for JIT and AOTed components as well.\n   */\n  static overrideTemplateUsingTestingModule(component, template) {\n    return TestBedImpl.INSTANCE.overrideTemplateUsingTestingModule(component, template);\n  }\n  static overrideProvider(token, provider) {\n    return TestBedImpl.INSTANCE.overrideProvider(token, provider);\n  }\n  static inject(token, notFoundValue, flags) {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, _convertToBitFlags(flags));\n  }\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  static get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, flags);\n  }\n  /**\n   * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n   *\n   * @see {@link EnvironmentInjector#runInContext}\n   */\n  static runInInjectionContext(fn) {\n    return TestBedImpl.INSTANCE.runInInjectionContext(fn);\n  }\n  static createComponent(component) {\n    return TestBedImpl.INSTANCE.createComponent(component);\n  }\n  static resetTestingModule() {\n    return TestBedImpl.INSTANCE.resetTestingModule();\n  }\n  static execute(tokens, fn, context) {\n    return TestBedImpl.INSTANCE.execute(tokens, fn, context);\n  }\n  static get platform() {\n    return TestBedImpl.INSTANCE.platform;\n  }\n  static get ngModule() {\n    return TestBedImpl.INSTANCE.ngModule;\n  }\n  static flushEffects() {\n    return TestBedImpl.INSTANCE.flushEffects();\n  }\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  initTestEnvironment(ngModule, platform, options) {\n    if (this.platform || this.ngModule) {\n      throw new Error('Cannot set base providers because it has already been called');\n    }\n    TestBedImpl._environmentTeardownOptions = options === null || options === void 0 ? void 0 : options.teardown;\n    TestBedImpl._environmentErrorOnUnknownElementsOption = options === null || options === void 0 ? void 0 : options.errorOnUnknownElements;\n    TestBedImpl._environmentErrorOnUnknownPropertiesOption = options === null || options === void 0 ? void 0 : options.errorOnUnknownProperties;\n    this.platform = platform;\n    this.ngModule = ngModule;\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n    // TestBed does not have an API which can reliably detect the start of a test, and thus could be\n    // used to track the state of the NgModule registry and reset it correctly. Instead, when we\n    // know we're in a testing scenario, we disable the check for duplicate NgModule registration\n    // completely.\n    _setAllowDuplicateNgModuleIdsForTest(true);\n  }\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  resetTestEnvironment() {\n    this.resetTestingModule();\n    this._compiler = null;\n    this.platform = null;\n    this.ngModule = null;\n    TestBedImpl._environmentTeardownOptions = undefined;\n    _setAllowDuplicateNgModuleIdsForTest(false);\n  }\n  resetTestingModule() {\n    var _this$_previousErrorO, _this$_previousErrorO2;\n    this.checkGlobalCompilationFinished();\n    _resetCompiledComponents();\n    if (this._compiler !== null) {\n      this.compiler.restoreOriginalState();\n    }\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n    // Restore the previous value of the \"error on unknown elements\" option\n    _setUnknownElementStrictMode((_this$_previousErrorO = this._previousErrorOnUnknownElementsOption) !== null && _this$_previousErrorO !== void 0 ? _this$_previousErrorO : THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n    // Restore the previous value of the \"error on unknown properties\" option\n    _setUnknownPropertyStrictMode((_this$_previousErrorO2 = this._previousErrorOnUnknownPropertiesOption) !== null && _this$_previousErrorO2 !== void 0 ? _this$_previousErrorO2 : THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n    // We have to chain a couple of try/finally blocks, because each step can\n    // throw errors and we don't want it to interrupt the next step and we also\n    // want an error to be thrown at the end.\n    try {\n      this.destroyActiveFixtures();\n    } finally {\n      try {\n        if (this.shouldTearDownTestingModule()) {\n          this.tearDownTestingModule();\n        }\n      } finally {\n        this._testModuleRef = null;\n        this._instanceTeardownOptions = undefined;\n        this._instanceErrorOnUnknownElementsOption = undefined;\n        this._instanceErrorOnUnknownPropertiesOption = undefined;\n        this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n      }\n    }\n    return this;\n  }\n  configureCompiler(config) {\n    if (config.useJit != null) {\n      throw new Error('JIT compiler is not configurable via TestBed APIs.');\n    }\n    if (config.providers !== undefined) {\n      this.compiler.setCompilerProviders(config.providers);\n    }\n    return this;\n  }\n  configureTestingModule(moduleDef) {\n    var _moduleDef$deferBlock2;\n    this.assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n    // Trigger module scoping queue flush before executing other TestBed operations in a test.\n    // This is needed for the first test invocation to ensure that globally declared modules have\n    // their components scoped properly. See the `checkGlobalCompilationFinished` function\n    // description for additional info.\n    this.checkGlobalCompilationFinished();\n    // Always re-assign the options, even if they're undefined.\n    // This ensures that we don't carry them between tests.\n    this._instanceTeardownOptions = moduleDef.teardown;\n    this._instanceErrorOnUnknownElementsOption = moduleDef.errorOnUnknownElements;\n    this._instanceErrorOnUnknownPropertiesOption = moduleDef.errorOnUnknownProperties;\n    this._instanceDeferBlockBehavior = (_moduleDef$deferBlock2 = moduleDef.deferBlockBehavior) !== null && _moduleDef$deferBlock2 !== void 0 ? _moduleDef$deferBlock2 : DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    // Store the current value of the strict mode option,\n    // so we can restore it later\n    this._previousErrorOnUnknownElementsOption = _getUnknownElementStrictMode();\n    _setUnknownElementStrictMode(this.shouldThrowErrorOnUnknownElements());\n    this._previousErrorOnUnknownPropertiesOption = _getUnknownPropertyStrictMode();\n    _setUnknownPropertyStrictMode(this.shouldThrowErrorOnUnknownProperties());\n    this.compiler.configureTestingModule(moduleDef);\n    return this;\n  }\n  compileComponents() {\n    return this.compiler.compileComponents();\n  }\n  inject(token, notFoundValue, flags) {\n    if (token === TestBed) {\n      return this;\n    }\n    const UNDEFINED = {};\n    const result = this.testModuleRef.injector.get(token, UNDEFINED, _convertToBitFlags(flags));\n    return result === UNDEFINED ? this.compiler.injector.get(token, notFoundValue, flags) : result;\n  }\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n    return this.inject(token, notFoundValue, flags);\n  }\n  runInInjectionContext(fn) {\n    return runInInjectionContext(this.inject(EnvironmentInjector), fn);\n  }\n  execute(tokens, fn, context) {\n    const params = tokens.map(t => this.inject(t));\n    return fn.apply(context, params);\n  }\n  overrideModule(ngModule, override) {\n    this.assertNotInstantiated('overrideModule', 'override module metadata');\n    this.compiler.overrideModule(ngModule, override);\n    return this;\n  }\n  overrideComponent(component, override) {\n    this.assertNotInstantiated('overrideComponent', 'override component metadata');\n    this.compiler.overrideComponent(component, override);\n    return this;\n  }\n  overrideTemplateUsingTestingModule(component, template) {\n    this.assertNotInstantiated('TestBed.overrideTemplateUsingTestingModule', 'Cannot override template when the test module has already been instantiated');\n    this.compiler.overrideTemplateUsingTestingModule(component, template);\n    return this;\n  }\n  overrideDirective(directive, override) {\n    this.assertNotInstantiated('overrideDirective', 'override directive metadata');\n    this.compiler.overrideDirective(directive, override);\n    return this;\n  }\n  overridePipe(pipe, override) {\n    this.assertNotInstantiated('overridePipe', 'override pipe metadata');\n    this.compiler.overridePipe(pipe, override);\n    return this;\n  }\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(token, provider) {\n    this.assertNotInstantiated('overrideProvider', 'override provider');\n    this.compiler.overrideProvider(token, provider);\n    return this;\n  }\n  overrideTemplate(component, template) {\n    return this.overrideComponent(component, {\n      set: {\n        template,\n        templateUrl: null\n      }\n    });\n  }\n  createComponent(type) {\n    const testComponentRenderer = this.inject(TestComponentRenderer);\n    const rootElId = `root${_nextRootElementId++}`;\n    testComponentRenderer.insertRootElement(rootElId);\n    if (_getAsyncClassMetadataFn(type)) {\n      throw new Error(`Component '${type.name}' has unresolved metadata. ` + `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n    }\n    const componentDef = type.ɵcmp;\n    if (!componentDef) {\n      throw new Error(`It looks like '${_stringify(type)}' has not been compiled.`);\n    }\n    const componentFactory = new _Render3ComponentFactory(componentDef);\n    const initComponent = () => {\n      const componentRef = componentFactory.create(Injector.NULL, [], `#${rootElId}`, this.testModuleRef);\n      return this.runInInjectionContext(() => new ComponentFixture(componentRef));\n    };\n    const noNgZone = this.inject(ComponentFixtureNoNgZone, false);\n    const ngZone = noNgZone ? null : this.inject(NgZone, null);\n    const fixture = ngZone ? ngZone.run(initComponent) : initComponent();\n    this._activeFixtures.push(fixture);\n    return fixture;\n  }\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  get compiler() {\n    if (this._compiler === null) {\n      throw new Error(`Need to call TestBed.initTestEnvironment() first`);\n    }\n    return this._compiler;\n  }\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  get testModuleRef() {\n    if (this._testModuleRef === null) {\n      this._testModuleRef = this.compiler.finalize();\n    }\n    return this._testModuleRef;\n  }\n  assertNotInstantiated(methodName, methodDescription) {\n    if (this._testModuleRef !== null) {\n      throw new Error(`Cannot ${methodDescription} when the test module has already been instantiated. ` + `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n    }\n  }\n  /**\n   * Check whether the module scoping queue should be flushed, and flush it if needed.\n   *\n   * When the TestBed is reset, it clears the JIT module compilation queue, cancelling any\n   * in-progress module compilation. This creates a potential hazard - the very first time the\n   * TestBed is initialized (or if it's reset without being initialized), there may be pending\n   * compilations of modules declared in global scope. These compilations should be finished.\n   *\n   * To ensure that globally declared modules have their components scoped properly, this function\n   * is called whenever TestBed is initialized or reset. The _first_ time that this happens, prior\n   * to any other operations, the scoping queue is flushed.\n   */\n  checkGlobalCompilationFinished() {\n    // Checking _testNgModuleRef is null should not be necessary, but is left in as an additional\n    // guard that compilations queued in tests (after instantiation) are never flushed accidentally.\n    if (!this.globalCompilationChecked && this._testModuleRef === null) {\n      _flushModuleScopingQueueAsMuchAsPossible();\n    }\n    this.globalCompilationChecked = true;\n  }\n  destroyActiveFixtures() {\n    let errorCount = 0;\n    this._activeFixtures.forEach(fixture => {\n      try {\n        fixture.destroy();\n      } catch (e) {\n        errorCount++;\n        console.error('Error during cleanup of component', {\n          component: fixture.componentInstance,\n          stacktrace: e\n        });\n      }\n    });\n    this._activeFixtures = [];\n    if (errorCount > 0 && this.shouldRethrowTeardownErrors()) {\n      throw Error(`${errorCount} ${errorCount === 1 ? 'component' : 'components'} ` + `threw errors during cleanup`);\n    }\n  }\n  shouldRethrowTeardownErrors() {\n    var _ref, _instanceOptions$reth;\n    const instanceOptions = this._instanceTeardownOptions;\n    const environmentOptions = TestBedImpl._environmentTeardownOptions;\n    // If the new teardown behavior hasn't been configured, preserve the old behavior.\n    if (!instanceOptions && !environmentOptions) {\n      return TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n    }\n    // Otherwise use the configured behavior or default to rethrowing.\n    return (_ref = (_instanceOptions$reth = instanceOptions === null || instanceOptions === void 0 ? void 0 : instanceOptions.rethrowErrors) !== null && _instanceOptions$reth !== void 0 ? _instanceOptions$reth : environmentOptions === null || environmentOptions === void 0 ? void 0 : environmentOptions.rethrowErrors) !== null && _ref !== void 0 ? _ref : this.shouldTearDownTestingModule();\n  }\n  shouldThrowErrorOnUnknownElements() {\n    var _ref2, _this$_instanceErrorO;\n    // Check if a configuration has been provided to throw when an unknown element is found\n    return (_ref2 = (_this$_instanceErrorO = this._instanceErrorOnUnknownElementsOption) !== null && _this$_instanceErrorO !== void 0 ? _this$_instanceErrorO : TestBedImpl._environmentErrorOnUnknownElementsOption) !== null && _ref2 !== void 0 ? _ref2 : THROW_ON_UNKNOWN_ELEMENTS_DEFAULT;\n  }\n  shouldThrowErrorOnUnknownProperties() {\n    var _ref3, _this$_instanceErrorO2;\n    // Check if a configuration has been provided to throw when an unknown property is found\n    return (_ref3 = (_this$_instanceErrorO2 = this._instanceErrorOnUnknownPropertiesOption) !== null && _this$_instanceErrorO2 !== void 0 ? _this$_instanceErrorO2 : TestBedImpl._environmentErrorOnUnknownPropertiesOption) !== null && _ref3 !== void 0 ? _ref3 : THROW_ON_UNKNOWN_PROPERTIES_DEFAULT;\n  }\n  shouldTearDownTestingModule() {\n    var _ref4, _this$_instanceTeardo, _this$_instanceTeardo2, _TestBedImpl$_environ;\n    return (_ref4 = (_this$_instanceTeardo = (_this$_instanceTeardo2 = this._instanceTeardownOptions) === null || _this$_instanceTeardo2 === void 0 ? void 0 : _this$_instanceTeardo2.destroyAfterEach) !== null && _this$_instanceTeardo !== void 0 ? _this$_instanceTeardo : (_TestBedImpl$_environ = TestBedImpl._environmentTeardownOptions) === null || _TestBedImpl$_environ === void 0 ? void 0 : _TestBedImpl$_environ.destroyAfterEach) !== null && _ref4 !== void 0 ? _ref4 : TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n  }\n  getDeferBlockBehavior() {\n    return this._instanceDeferBlockBehavior;\n  }\n  tearDownTestingModule() {\n    // If the module ref has already been destroyed, we won't be able to get a test renderer.\n    if (this._testModuleRef === null) {\n      return;\n    }\n    // Resolve the renderer ahead of time, because we want to remove the root elements as the very\n    // last step, but the injector will be destroyed as a part of the module ref destruction.\n    const testRenderer = this.inject(TestComponentRenderer);\n    try {\n      this._testModuleRef.destroy();\n    } catch (e) {\n      if (this.shouldRethrowTeardownErrors()) {\n        throw e;\n      } else {\n        console.error('Error during cleanup of a testing module', {\n          component: this._testModuleRef.instance,\n          stacktrace: e\n        });\n      }\n    } finally {\n      var _testRenderer$removeA;\n      (_testRenderer$removeA = testRenderer.removeAllRootElements) === null || _testRenderer$removeA === void 0 || _testRenderer$removeA.call(testRenderer);\n    }\n  }\n  /**\n   * Execute any pending effects.\n   *\n   * @developerPreview\n   */\n  flushEffects() {\n    this.inject(_MicrotaskEffectScheduler).flush();\n    this.inject(_EffectScheduler).flush();\n  }\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * `TestBed` is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @publicApi\n */\n_defineProperty(TestBedImpl, \"_INSTANCE\", null);\n_defineProperty(TestBedImpl, \"_environmentTeardownOptions\", void 0);\n/**\n * \"Error on unknown elements\" option that has been configured at the environment level.\n * Used as a fallback if no instance-level option has been provided.\n */\n_defineProperty(TestBedImpl, \"_environmentErrorOnUnknownElementsOption\", void 0);\n/**\n * \"Error on unknown properties\" option that has been configured at the environment level.\n * Used as a fallback if no instance-level option has been provided.\n */\n_defineProperty(TestBedImpl, \"_environmentErrorOnUnknownPropertiesOption\", void 0);\nconst TestBed = TestBedImpl;\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`. Note: this function\n * (imported from the `@angular/core/testing` package) can **only** be used to inject dependencies\n * in tests. To inject dependencies in your application code, use the [`inject`](api/core/inject)\n * function from the `@angular/core` package instead.\n *\n * Example:\n *\n * ```ts\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * @publicApi\n */\nfunction inject(tokens, fn) {\n  const testBed = TestBedImpl.INSTANCE;\n  // Not using an arrow function to preserve context passed from call site\n  return function () {\n    return testBed.execute(tokens, fn, this);\n  };\n}\n/**\n * @publicApi\n */\nclass InjectSetupWrapper {\n  constructor(_moduleDef) {\n    _defineProperty(this, \"_moduleDef\", void 0);\n    this._moduleDef = _moduleDef;\n  }\n  _addModule() {\n    const moduleDef = this._moduleDef();\n    if (moduleDef) {\n      TestBedImpl.configureTestingModule(moduleDef);\n    }\n  }\n  inject(tokens, fn) {\n    const self = this;\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n      self._addModule();\n      return inject(tokens, fn).call(this);\n    };\n  }\n}\nfunction withModule(moduleDef, fn) {\n  if (fn) {\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n      const testBed = TestBedImpl.INSTANCE;\n      if (moduleDef) {\n        testBed.configureTestingModule(moduleDef);\n      }\n      return fn.apply(this);\n    };\n  }\n  return new InjectSetupWrapper(() => moduleDef);\n}\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with Jasmine, Mocha, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n// Reset the test providers and the fake async zone before each test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// beforeEach is only defined when executing the tests\n(_globalThis$beforeEac = globalThis.beforeEach) === null || _globalThis$beforeEac === void 0 || _globalThis$beforeEac.call(globalThis, getCleanupHook(false));\n// We provide both a `beforeEach` and `afterEach`, because the updated behavior for\n// tearing down the module is supposed to run after the test so that we can associate\n// teardown errors with the correct test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// afterEach is only defined when executing the tests\n(_globalThis$afterEach = globalThis.afterEach) === null || _globalThis$afterEach === void 0 || _globalThis$afterEach.call(globalThis, getCleanupHook(true));\nfunction getCleanupHook(expectedTeardownValue) {\n  return () => {\n    const testBed = TestBedImpl.INSTANCE;\n    if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {\n      testBed.resetTestingModule();\n      resetFakeAsyncZoneIfExists();\n    }\n  };\n}\n/**\n * This API should be removed. But doing so seems to break `google3` and so it requires a bit of\n * investigation.\n *\n * A work around is to mark it as `@codeGenApi` for now and investigate later.\n *\n * @codeGenApi\n */\n// TODO(iminar): Remove this code in a safe way.\nconst __core_private_testing_placeholder__ = '';\n\n/**\n * Fake implementation of user agent history and navigation behavior. This is a\n * high-fidelity implementation of browser behavior that attempts to emulate\n * things like traversal delay.\n */\nclass FakeNavigation {\n  /** Equivalent to `navigation.currentEntry`. */\n  get currentEntry() {\n    return this.entriesArr[this.currentEntryIndex];\n  }\n  get canGoBack() {\n    return this.currentEntryIndex > 0;\n  }\n  get canGoForward() {\n    return this.currentEntryIndex < this.entriesArr.length - 1;\n  }\n  get window() {\n    return this._window;\n  }\n  constructor(doc, startURL) {\n    var _document$defaultView;\n    /**\n     * The fake implementation of an entries array. Only same-document entries\n     * allowed.\n     */\n    _defineProperty(this, \"entriesArr\", []);\n    /**\n     * The current active entry index into `entriesArr`.\n     */\n    _defineProperty(this, \"currentEntryIndex\", 0);\n    /**\n     * The current navigate event.\n     * @internal\n     */\n    _defineProperty(this, \"navigateEvent\", null);\n    /**\n     * A Map of pending traversals, so that traversals to the same entry can be\n     * re-used.\n     */\n    _defineProperty(this, \"traversalQueue\", new Map());\n    /**\n     * A Promise that resolves when the previous traversals have finished. Used to\n     * simulate the cross-process communication necessary for traversals.\n     */\n    _defineProperty(this, \"nextTraversal\", Promise.resolve());\n    /**\n     * A prospective current active entry index, which includes unresolved\n     * traversals. Used by `go` to determine where navigations are intended to go.\n     */\n    _defineProperty(this, \"prospectiveEntryIndex\", 0);\n    /**\n     * A test-only option to make traversals synchronous, rather than emulate\n     * cross-process communication.\n     */\n    _defineProperty(this, \"synchronousTraversals\", false);\n    /** Whether to allow a call to setInitialEntryForTesting. */\n    _defineProperty(this, \"canSetInitialEntry\", true);\n    /**\n     * `EventTarget` to dispatch events.\n     * @internal\n     */\n    _defineProperty(this, \"eventTarget\", void 0);\n    /** The next unique id for created entries. Replace recreates this id. */\n    _defineProperty(this, \"nextId\", 0);\n    /** The next unique key for created entries. Replace inherits this id. */\n    _defineProperty(this, \"nextKey\", 0);\n    /** Whether this fake is disposed. */\n    _defineProperty(this, \"disposed\", false);\n    _defineProperty(this, \"createEventTarget\", void 0);\n    _defineProperty(this, \"_window\", void 0);\n    _defineProperty(this, \"_transition\", null);\n    this.createEventTarget = () => {\n      try {\n        // `document.createElement` because NodeJS `EventTarget` is\n        // incompatible with Domino's `Event`. That is, attempting to\n        // dispatch an event created by Domino's patched `Event` will\n        // throw an error since it is not an instance of a real Node\n        // `Event`.\n        return doc.createElement('div');\n      } catch (_unused) {\n        // Fallback to a basic EventTarget if `document.createElement`\n        // fails. This can happen with tests that pass in a value for document\n        // that is stubbed.\n        return new EventTarget();\n      }\n    };\n    this._window = (_document$defaultView = document.defaultView) !== null && _document$defaultView !== void 0 ? _document$defaultView : this.createEventTarget();\n    this.eventTarget = this.createEventTarget();\n    // First entry.\n    this.setInitialEntryForTesting(startURL);\n  }\n  /**\n   * Sets the initial entry.\n   */\n  setInitialEntryForTesting(url, options = {\n    historyState: null\n  }) {\n    var _currentInitialEntry$, _currentInitialEntry$2;\n    if (!this.canSetInitialEntry) {\n      throw new Error('setInitialEntryForTesting can only be called before any ' + 'navigation has occurred');\n    }\n    const currentInitialEntry = this.entriesArr[0];\n    this.entriesArr[0] = new FakeNavigationHistoryEntry(this.eventTarget, new URL(url).toString(), {\n      index: 0,\n      key: (_currentInitialEntry$ = currentInitialEntry === null || currentInitialEntry === void 0 ? void 0 : currentInitialEntry.key) !== null && _currentInitialEntry$ !== void 0 ? _currentInitialEntry$ : String(this.nextKey++),\n      id: (_currentInitialEntry$2 = currentInitialEntry === null || currentInitialEntry === void 0 ? void 0 : currentInitialEntry.id) !== null && _currentInitialEntry$2 !== void 0 ? _currentInitialEntry$2 : String(this.nextId++),\n      sameDocument: true,\n      historyState: options === null || options === void 0 ? void 0 : options.historyState,\n      state: options.state\n    });\n  }\n  /** Returns whether the initial entry is still eligible to be set. */\n  canSetInitialEntryForTesting() {\n    return this.canSetInitialEntry;\n  }\n  /**\n   * Sets whether to emulate traversals as synchronous rather than\n   * asynchronous.\n   */\n  setSynchronousTraversalsForTesting(synchronousTraversals) {\n    this.synchronousTraversals = synchronousTraversals;\n  }\n  /** Equivalent to `navigation.entries()`. */\n  entries() {\n    return this.entriesArr.slice();\n  }\n  /** Equivalent to `navigation.navigate()`. */\n  navigate(url, options) {\n    const fromUrl = new URL(this.currentEntry.url);\n    const toUrl = new URL(url, this.currentEntry.url);\n    let navigationType;\n    if (!(options !== null && options !== void 0 && options.history) || options.history === 'auto') {\n      // Auto defaults to push, but if the URLs are the same, is a replace.\n      if (fromUrl.toString() === toUrl.toString()) {\n        navigationType = 'replace';\n      } else {\n        navigationType = 'push';\n      }\n    } else {\n      navigationType = options.history;\n    }\n    const hashChange = isHashChange(fromUrl, toUrl);\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      state: options === null || options === void 0 ? void 0 : options.state,\n      sameDocument: hashChange,\n      historyState: null\n    });\n    const result = new InternalNavigationResult(this);\n    const intercepted = this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for navigate().\n      userInitiated: false,\n      hashChange,\n      info: options === null || options === void 0 ? void 0 : options.info\n    });\n    if (!intercepted) {\n      this.updateNavigationEntriesForSameDocumentNavigation(this.navigateEvent);\n    }\n    return {\n      committed: result.committed,\n      finished: result.finished\n    };\n  }\n  /** Equivalent to `history.pushState()`. */\n  pushState(data, title, url) {\n    this.pushOrReplaceState('push', data, title, url);\n  }\n  /** Equivalent to `history.replaceState()`. */\n  replaceState(data, title, url) {\n    this.pushOrReplaceState('replace', data, title, url);\n  }\n  pushOrReplaceState(navigationType, data, _title, url) {\n    const fromUrl = new URL(this.currentEntry.url);\n    const toUrl = url ? new URL(url, this.currentEntry.url) : fromUrl;\n    const hashChange = isHashChange(fromUrl, toUrl);\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      sameDocument: true,\n      historyState: data\n    });\n    const result = new InternalNavigationResult(this);\n    const intercepted = this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for pushState() or replaceState().\n      userInitiated: false,\n      hashChange\n    });\n    if (intercepted) {\n      return;\n    }\n    this.updateNavigationEntriesForSameDocumentNavigation(this.navigateEvent);\n  }\n  /** Equivalent to `navigation.traverseTo()`. */\n  traverseTo(key, options) {\n    const fromUrl = new URL(this.currentEntry.url);\n    const entry = this.findEntry(key);\n    if (!entry) {\n      const domException = new DOMException('Invalid key', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished\n      };\n    }\n    if (entry === this.currentEntry) {\n      return {\n        committed: Promise.resolve(this.currentEntry),\n        finished: Promise.resolve(this.currentEntry)\n      };\n    }\n    if (this.traversalQueue.has(entry.key)) {\n      const existingResult = this.traversalQueue.get(entry.key);\n      return {\n        committed: existingResult.committed,\n        finished: existingResult.finished\n      };\n    }\n    const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n    const destination = new FakeNavigationDestination({\n      url: entry.url,\n      state: entry.getState(),\n      historyState: entry.getHistoryState(),\n      key: entry.key,\n      id: entry.id,\n      index: entry.index,\n      sameDocument: entry.sameDocument\n    });\n    this.prospectiveEntryIndex = entry.index;\n    const result = new InternalNavigationResult(this);\n    this.traversalQueue.set(entry.key, result);\n    this.runTraversal(() => {\n      this.traversalQueue.delete(entry.key);\n      const intercepted = this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for traverseTo().\n        userInitiated: false,\n        hashChange,\n        info: options === null || options === void 0 ? void 0 : options.info\n      });\n      if (!intercepted) {\n        this.userAgentTraverse(this.navigateEvent);\n      }\n    });\n    return {\n      committed: result.committed,\n      finished: result.finished\n    };\n  }\n  /** Equivalent to `navigation.back()`. */\n  back(options) {\n    if (this.currentEntryIndex === 0) {\n      const domException = new DOMException('Cannot go back', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex - 1];\n    return this.traverseTo(entry.key, options);\n  }\n  /** Equivalent to `navigation.forward()`. */\n  forward(options) {\n    if (this.currentEntryIndex === this.entriesArr.length - 1) {\n      const domException = new DOMException('Cannot go forward', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex + 1];\n    return this.traverseTo(entry.key, options);\n  }\n  /**\n   * Equivalent to `history.go()`.\n   * Note that this method does not actually work precisely to how Chrome\n   * does, instead choosing a simpler model with less unexpected behavior.\n   * Chrome has a few edge case optimizations, for instance with repeated\n   * `back(); forward()` chains it collapses certain traversals.\n   */\n  go(direction) {\n    const targetIndex = this.prospectiveEntryIndex + direction;\n    if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n      return;\n    }\n    this.prospectiveEntryIndex = targetIndex;\n    this.runTraversal(() => {\n      // Check again that destination is in the entries array.\n      if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n        return;\n      }\n      const fromUrl = new URL(this.currentEntry.url);\n      const entry = this.entriesArr[targetIndex];\n      const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n      const destination = new FakeNavigationDestination({\n        url: entry.url,\n        state: entry.getState(),\n        historyState: entry.getHistoryState(),\n        key: entry.key,\n        id: entry.id,\n        index: entry.index,\n        sameDocument: entry.sameDocument\n      });\n      const result = new InternalNavigationResult(this);\n      const intercepted = this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for go().\n        userInitiated: false,\n        hashChange\n      });\n      if (!intercepted) {\n        this.userAgentTraverse(this.navigateEvent);\n      }\n    });\n  }\n  /** Runs a traversal synchronously or asynchronously */\n  runTraversal(traversal) {\n    if (this.synchronousTraversals) {\n      traversal();\n      return;\n    }\n    // Each traversal occupies a single timeout resolution.\n    // This means that Promises added to commit and finish should resolve\n    // before the next traversal.\n    this.nextTraversal = this.nextTraversal.then(() => {\n      return new Promise(resolve => {\n        setTimeout(() => {\n          resolve();\n          traversal();\n        });\n      });\n    });\n  }\n  /** Equivalent to `navigation.addEventListener()`. */\n  addEventListener(type, callback, options) {\n    this.eventTarget.addEventListener(type, callback, options);\n  }\n  /** Equivalent to `navigation.removeEventListener()`. */\n  removeEventListener(type, callback, options) {\n    this.eventTarget.removeEventListener(type, callback, options);\n  }\n  /** Equivalent to `navigation.dispatchEvent()` */\n  dispatchEvent(event) {\n    return this.eventTarget.dispatchEvent(event);\n  }\n  /** Cleans up resources. */\n  dispose() {\n    // Recreate eventTarget to release current listeners.\n    this.eventTarget = this.createEventTarget();\n    this.disposed = true;\n  }\n  /** Returns whether this fake is disposed. */\n  isDisposed() {\n    return this.disposed;\n  }\n  /**\n   * Implementation for all navigations and traversals.\n   * @returns true if the event was intercepted, otherwise false\n   */\n  userAgentNavigate(destination, result, options) {\n    // The first navigation should disallow any future calls to set the initial\n    // entry.\n    this.canSetInitialEntry = false;\n    if (this.navigateEvent) {\n      this.navigateEvent.cancel(new DOMException('Navigation was aborted', 'AbortError'));\n      this.navigateEvent = null;\n    }\n    return dispatchNavigateEvent({\n      navigationType: options.navigationType,\n      cancelable: options.cancelable,\n      canIntercept: options.canIntercept,\n      userInitiated: options.userInitiated,\n      hashChange: options.hashChange,\n      signal: result.signal,\n      destination,\n      info: options.info,\n      sameDocument: destination.sameDocument,\n      result\n    });\n  }\n  /**\n   * Implementation for a push or replace navigation.\n   * https://whatpr.org/html/10919/browsing-the-web.html#url-and-history-update-steps\n   * https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n   * @internal\n   */\n  urlAndHistoryUpdateSteps(navigateEvent) {\n    this.updateNavigationEntriesForSameDocumentNavigation(navigateEvent);\n  }\n  /**\n   * Implementation for a traverse navigation.\n   *\n   * https://whatpr.org/html/10919/browsing-the-web.html#apply-the-traverse-history-step\n   * ...\n   * > Let updateDocument be an algorithm step which performs update document for history step application given targetEntry's document, targetEntry, changingNavigableContinuation's update-only, scriptHistoryLength, scriptHistoryIndex, navigationType, entriesForNavigationAPI, and previousEntry.\n   * > If targetEntry's document is equal to displayedDocument, then perform updateDocument.\n   * https://whatpr.org/html/10919/browsing-the-web.html#update-document-for-history-step-application\n   * which then goes to https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n   * @internal\n   */\n  userAgentTraverse(navigateEvent) {\n    const oldUrl = this.currentEntry.url;\n    this.updateNavigationEntriesForSameDocumentNavigation(navigateEvent);\n    // Happens as part of \"updating the document\" steps https://whatpr.org/html/10919/browsing-the-web.html#updating-the-document\n    const popStateEvent = createPopStateEvent({\n      state: navigateEvent.destination.getHistoryState()\n    });\n    this._window.dispatchEvent(popStateEvent);\n    if (navigateEvent.hashChange) {\n      const hashchangeEvent = createHashChangeEvent(oldUrl, this.currentEntry.url);\n      this._window.dispatchEvent(hashchangeEvent);\n    }\n  }\n  /**\n   * https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n   * @internal\n   */\n  updateNavigationEntriesForSameDocumentNavigation({\n    destination,\n    navigationType,\n    result\n  }) {\n    const oldCurrentNHE = this.currentEntry;\n    const disposedNHEs = [];\n    if (navigationType === 'traverse') {\n      this.currentEntryIndex = destination.index;\n      if (this.currentEntryIndex === -1) {\n        throw new Error('unexpected current entry index');\n      }\n    } else if (navigationType === 'push') {\n      this.currentEntryIndex++;\n      this.prospectiveEntryIndex = this.currentEntryIndex; // prospectiveEntryIndex isn't in the spec but is an implementation detail\n      disposedNHEs.push(...this.entriesArr.splice(this.currentEntryIndex));\n    } else if (navigationType === 'replace') {\n      disposedNHEs.push(oldCurrentNHE);\n    }\n    if (navigationType === 'push' || navigationType === 'replace') {\n      const index = this.currentEntryIndex;\n      const key = navigationType === 'push' ? String(this.nextKey++) : this.currentEntry.key;\n      const newNHE = new FakeNavigationHistoryEntry(this.eventTarget, destination.url, {\n        id: String(this.nextId++),\n        key,\n        index,\n        sameDocument: true,\n        state: destination.getState(),\n        historyState: destination.getHistoryState()\n      });\n      this.entriesArr[this.currentEntryIndex] = newNHE;\n    }\n    result.committedResolve(this.currentEntry);\n    const currentEntryChangeEvent = createFakeNavigationCurrentEntryChangeEvent({\n      from: oldCurrentNHE,\n      navigationType: navigationType\n    });\n    this.eventTarget.dispatchEvent(currentEntryChangeEvent);\n    for (const disposedNHE of disposedNHEs) {\n      disposedNHE.dispose();\n    }\n  }\n  /** Utility method for finding entries with the given `key`. */\n  findEntry(key) {\n    for (const entry of this.entriesArr) {\n      if (entry.key === key) return entry;\n    }\n    return undefined;\n  }\n  set onnavigate(\n  // tslint:disable-next-line:no-any\n  _handler) {\n    throw new Error('unimplemented');\n  }\n  // tslint:disable-next-line:no-any\n  get onnavigate() {\n    throw new Error('unimplemented');\n  }\n  set oncurrententrychange(_handler) {\n    throw new Error('unimplemented');\n  }\n  get oncurrententrychange() {\n    throw new Error('unimplemented');\n  }\n  set onnavigatesuccess(\n  // tslint:disable-next-line:no-any\n  _handler) {\n    throw new Error('unimplemented');\n  }\n  // tslint:disable-next-line:no-any\n  get onnavigatesuccess() {\n    throw new Error('unimplemented');\n  }\n  set onnavigateerror(\n  // tslint:disable-next-line:no-any\n  _handler) {\n    throw new Error('unimplemented');\n  }\n  // tslint:disable-next-line:no-any\n  get onnavigateerror() {\n    throw new Error('unimplemented');\n  }\n  /** @internal */\n  set transition(t) {\n    this._transition = t;\n  }\n  get transition() {\n    return this._transition;\n  }\n  updateCurrentEntry(_options) {\n    throw new Error('unimplemented');\n  }\n  reload(_options) {\n    throw new Error('unimplemented');\n  }\n}\n/**\n * Fake equivalent of `NavigationHistoryEntry`.\n */\nclass FakeNavigationHistoryEntry {\n  constructor(eventTarget, url, {\n    id,\n    key,\n    index,\n    sameDocument,\n    state,\n    historyState\n  }) {\n    _defineProperty(this, \"eventTarget\", void 0);\n    _defineProperty(this, \"url\", void 0);\n    _defineProperty(this, \"sameDocument\", void 0);\n    _defineProperty(this, \"id\", void 0);\n    _defineProperty(this, \"key\", void 0);\n    _defineProperty(this, \"index\", void 0);\n    _defineProperty(this, \"state\", void 0);\n    _defineProperty(this, \"historyState\", void 0);\n    // tslint:disable-next-line:no-any\n    _defineProperty(this, \"ondispose\", null);\n    this.eventTarget = eventTarget;\n    this.url = url;\n    this.id = id;\n    this.key = key;\n    this.index = index;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n  }\n  getState() {\n    // Budget copy.\n    return this.state ? JSON.parse(JSON.stringify(this.state)) : this.state;\n  }\n  getHistoryState() {\n    // Budget copy.\n    return this.historyState ? JSON.parse(JSON.stringify(this.historyState)) : this.historyState;\n  }\n  addEventListener(type, callback, options) {\n    this.eventTarget.addEventListener(type, callback, options);\n  }\n  removeEventListener(type, callback, options) {\n    this.eventTarget.removeEventListener(type, callback, options);\n  }\n  dispatchEvent(event) {\n    return this.eventTarget.dispatchEvent(event);\n  }\n  /** internal */\n  dispose() {\n    const disposeEvent = new Event('disposed');\n    this.dispatchEvent(disposeEvent);\n    // release current listeners\n    this.eventTarget = null;\n  }\n}\n/**\n * Create a fake equivalent of `NavigateEvent`. This is not a class because ES5\n * transpiled JavaScript cannot extend native Event.\n *\n * https://html.spec.whatwg.org/multipage/nav-history-apis.html#navigate-event-firing\n */\nfunction dispatchNavigateEvent({\n  cancelable,\n  canIntercept,\n  userInitiated,\n  hashChange,\n  navigationType,\n  signal,\n  destination,\n  info,\n  sameDocument,\n  result\n}) {\n  const {\n    navigation\n  } = result;\n  const event = new Event('navigate', {\n    bubbles: false,\n    cancelable\n  });\n  event.focusResetBehavior = null;\n  event.scrollBehavior = null;\n  event.interceptionState = 'none';\n  event.canIntercept = canIntercept;\n  event.userInitiated = userInitiated;\n  event.hashChange = hashChange;\n  event.navigationType = navigationType;\n  event.signal = signal;\n  event.destination = destination;\n  event.info = info;\n  event.downloadRequest = null;\n  event.formData = null;\n  event.result = result;\n  event.sameDocument = sameDocument;\n  let precommitHandlers = [];\n  let handlers = [];\n  // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigateevent-intercept\n  event.intercept = function (options) {\n    var _options$focusReset, _options$scroll;\n    if (!this.canIntercept) {\n      throw new DOMException(`Cannot intercept when canIntercept is 'false'`, 'SecurityError');\n    }\n    this.interceptionState = 'intercepted';\n    event.sameDocument = true;\n    const precommitHandler = options === null || options === void 0 ? void 0 : options.precommitHandler;\n    if (precommitHandler) {\n      if (!this.cancelable) {\n        throw new DOMException(`Cannot use precommitHandler when cancelable is 'false'`, 'InvalidStateError');\n      }\n      precommitHandlers.push(precommitHandler);\n    }\n    if (event.interceptionState !== 'none' && event.interceptionState !== 'intercepted') {\n      throw new Error('Event interceptionState should be \"none\" or \"intercepted\"');\n    }\n    event.interceptionState = 'intercepted';\n    const handler = options === null || options === void 0 ? void 0 : options.handler;\n    if (handler) {\n      handlers.push(handler);\n    }\n    // override old options with new ones. UA _may_ report a console warning if new options differ from previous\n    event.focusResetBehavior = (_options$focusReset = options === null || options === void 0 ? void 0 : options.focusReset) !== null && _options$focusReset !== void 0 ? _options$focusReset : event.focusResetBehavior;\n    event.scrollBehavior = (_options$scroll = options === null || options === void 0 ? void 0 : options.scroll) !== null && _options$scroll !== void 0 ? _options$scroll : event.scrollBehavior;\n  };\n  // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigateevent-scroll\n  event.scroll = function () {\n    if (event.interceptionState !== 'committed') {\n      throw new DOMException(`Failed to execute 'scroll' on 'NavigateEvent': scroll() must be ` + `called after commit() and interception options must specify manual scroll.`, 'InvalidStateError');\n    }\n    processScrollBehavior(event);\n  };\n  // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigationprecommitcontroller-redirect\n  function redirect(url) {\n    if (event.interceptionState === 'none') {\n      throw new Error('cannot redirect when event is not intercepted');\n    }\n    if (event.interceptionState !== 'intercepted') {\n      throw new DOMException(`cannot redirect when event is not in 'intercepted' state`, 'InvalidStateError');\n    }\n    if (event.navigationType !== 'push' && event.navigationType !== 'replace') {\n      throw new DOMException(`cannot redirect when navigationType is not 'push' or 'replace`, 'InvalidStateError');\n    }\n    const toUrl = new URL(url, navigation.currentEntry.url);\n    event.destination.url = toUrl.href;\n  }\n  // https://whatpr.org/html/10919/nav-history-apis.html#inner-navigate-event-firing-algorithm\n  // \"Let commit be the following steps:\"\n  function commit() {\n    if (result.signal.aborted) {\n      return;\n    }\n    if (event.interceptionState !== 'none') {\n      event.interceptionState = 'committed';\n      if (!navigation.currentEntry) {\n        throw new Error('from history entry should not be null');\n      }\n      navigation.transition = new InternalNavigationTransition(navigation.currentEntry, navigationType);\n      switch (event.navigationType) {\n        case 'push':\n        case 'replace':\n          {\n            navigation.urlAndHistoryUpdateSteps(event);\n            break;\n          }\n        case 'reload':\n          {\n            navigation.updateNavigationEntriesForSameDocumentNavigation(event);\n            break;\n          }\n        case 'traverse':\n          {\n            navigation.userAgentTraverse(event);\n            break;\n          }\n      }\n    }\n    const promisesList = handlers.map(handler => handler());\n    if (promisesList.length === 0) {\n      promisesList.push(Promise.resolve());\n    }\n    Promise.all(promisesList).then(() => {\n      // Follows steps outlined under \"Wait for all of promisesList, with the following success steps:\"\n      // in the spec https://html.spec.whatwg.org/multipage/nav-history-apis.html#navigate-event-firing.\n      if (result.signal.aborted) {\n        return;\n      }\n      if (event !== navigation.navigateEvent) {\n        throw new Error(\"Navigation's ongoing event not equal to resolved event\");\n      }\n      navigation.navigateEvent = null;\n      finishNavigationEvent(event, true);\n      const navigatesuccessEvent = new Event('navigatesuccess', {\n        bubbles: false,\n        cancelable\n      });\n      navigation.eventTarget.dispatchEvent(navigatesuccessEvent);\n      result.finishedResolve();\n      if (navigation.transition !== null) {\n        navigation.transition.finishedResolve();\n      }\n      navigation.transition = null;\n    }).catch(reason => event.cancel(reason));\n  }\n  // Internal only.\n  // https://whatpr.org/html/10919/nav-history-apis.html#inner-navigate-event-firing-algorithm\n  // \"Let cancel be the following steps given reason\"\n  event.cancel = function (reason) {\n    if (result.signal.aborted) {\n      return;\n    }\n    if (event !== navigation.navigateEvent) {\n      throw new Error(\"Navigation's ongoing event not equal to resolved event\");\n    }\n    navigation.navigateEvent = null;\n    if (event.interceptionState !== 'intercepted') {\n      finishNavigationEvent(event, false);\n    }\n    const navigateerrorEvent = new Event('navigateerror', {\n      bubbles: false,\n      cancelable\n    });\n    navigation.eventTarget.dispatchEvent(navigateerrorEvent);\n    result.finishedReject(reason);\n    if (navigation.transition !== null) {\n      navigation.transition.finishedReject(reason);\n    }\n    navigation.transition = null;\n  };\n  function dispatch() {\n    navigation.navigateEvent = event;\n    navigation.eventTarget.dispatchEvent(event);\n    if (precommitHandlers.length === 0) {\n      commit();\n    } else {\n      const precommitController = {\n        redirect\n      };\n      const precommitPromisesList = precommitHandlers.map(handler => handler(precommitController));\n      Promise.all(precommitPromisesList).then(() => commit()).catch(reason => event.cancel(reason));\n    }\n  }\n  dispatch();\n  return event.interceptionState !== 'none';\n}\n/** https://whatpr.org/html/10919/nav-history-apis.html#navigateevent-finish */\nfunction finishNavigationEvent(event, didFulfill) {\n  if (event.interceptionState === 'finished') {\n    throw new Error('Attempting to finish navigation event that was already finished');\n  }\n  if (event.interceptionState === 'intercepted') {\n    if (didFulfill === true) {\n      throw new Error('didFulfill should be false');\n    }\n    // assert precommit handlers is not empty\n    event.interceptionState = 'finished';\n    return;\n  }\n  if (event.interceptionState === 'none') {\n    return;\n  }\n  potentiallyResetFocus(event);\n  if (didFulfill) {\n    potentiallyResetScroll(event);\n  }\n  event.interceptionState = 'finished';\n}\n/** https://whatpr.org/html/10919/nav-history-apis.html#potentially-reset-the-focus */\nfunction potentiallyResetFocus(event) {\n  if (event.interceptionState !== 'committed' && event.interceptionState !== 'scrolled') {\n    throw new Error('cannot reset focus if navigation event is not committed or scrolled');\n  }\n  // TODO(atscott): The rest of the steps\n}\nfunction potentiallyResetScroll(event) {\n  if (event.interceptionState !== 'committed' && event.interceptionState !== 'scrolled') {\n    throw new Error('cannot reset scroll if navigation event is not committed or scrolled');\n  }\n  if (event.interceptionState === 'scrolled' || event.scrollBehavior === 'manual') {\n    return;\n  }\n  processScrollBehavior(event);\n}\n/* https://whatpr.org/html/10919/nav-history-apis.html#process-scroll-behavior */\nfunction processScrollBehavior(event) {\n  if (event.interceptionState !== 'committed') {\n    throw new Error('invalid event interception state when processing scroll behavior');\n  }\n  event.interceptionState = 'scrolled';\n  // TODO(atscott): the rest of the steps\n}\n/**\n * Create a fake equivalent of `NavigationCurrentEntryChange`. This does not use\n * a class because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigationCurrentEntryChangeEvent({\n  from,\n  navigationType\n}) {\n  const event = new Event('currententrychange', {\n    bubbles: false,\n    cancelable: false\n  });\n  event.from = from;\n  event.navigationType = navigationType;\n  return event;\n}\n/**\n * Create a fake equivalent of `PopStateEvent`. This does not use a class\n * because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createPopStateEvent({\n  state\n}) {\n  const event = new Event('popstate', {\n    bubbles: false,\n    cancelable: false\n  });\n  event.state = state;\n  return event;\n}\nfunction createHashChangeEvent(newURL, oldURL) {\n  const event = new Event('hashchange', {\n    bubbles: false,\n    cancelable: false\n  });\n  event.newURL = newURL;\n  event.oldURL = oldURL;\n  return event;\n}\n/**\n * Fake equivalent of `NavigationDestination`.\n */\nclass FakeNavigationDestination {\n  constructor({\n    url,\n    sameDocument,\n    historyState,\n    state,\n    key = null,\n    id = null,\n    index = -1\n  }) {\n    _defineProperty(this, \"url\", void 0);\n    _defineProperty(this, \"sameDocument\", void 0);\n    _defineProperty(this, \"key\", void 0);\n    _defineProperty(this, \"id\", void 0);\n    _defineProperty(this, \"index\", void 0);\n    _defineProperty(this, \"state\", void 0);\n    _defineProperty(this, \"historyState\", void 0);\n    this.url = url;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n    this.key = key;\n    this.id = id;\n    this.index = index;\n  }\n  getState() {\n    return this.state;\n  }\n  getHistoryState() {\n    return this.historyState;\n  }\n}\n/** Utility function to determine whether two UrlLike have the same hash. */\nfunction isHashChange(from, to) {\n  return to.hash !== from.hash && to.hostname === from.hostname && to.pathname === from.pathname && to.search === from.search;\n}\nclass InternalNavigationTransition {\n  constructor(from, navigationType) {\n    _defineProperty(this, \"from\", void 0);\n    _defineProperty(this, \"navigationType\", void 0);\n    _defineProperty(this, \"finished\", void 0);\n    _defineProperty(this, \"finishedResolve\", void 0);\n    _defineProperty(this, \"finishedReject\", void 0);\n    this.from = from;\n    this.navigationType = navigationType;\n    this.finished = new Promise((resolve, reject) => {\n      this.finishedReject = reject;\n      this.finishedResolve = resolve;\n    });\n    // All rejections are handled.\n    this.finished.catch(() => {});\n  }\n}\n/**\n * Internal utility class for representing the result of a navigation.\n * Generally equivalent to the \"apiMethodTracker\" in the spec.\n */\nclass InternalNavigationResult {\n  get signal() {\n    return this.abortController.signal;\n  }\n  constructor(navigation) {\n    var _this7 = this;\n    _defineProperty(this, \"navigation\", void 0);\n    _defineProperty(this, \"committedTo\", null);\n    _defineProperty(this, \"committedResolve\", void 0);\n    _defineProperty(this, \"committedReject\", void 0);\n    _defineProperty(this, \"finishedResolve\", void 0);\n    _defineProperty(this, \"finishedReject\", void 0);\n    _defineProperty(this, \"committed\", void 0);\n    _defineProperty(this, \"finished\", void 0);\n    _defineProperty(this, \"abortController\", new AbortController());\n    this.navigation = navigation;\n    this.committed = new Promise((resolve, reject) => {\n      this.committedResolve = entry => {\n        this.committedTo = entry;\n        resolve(entry);\n      };\n      this.committedReject = reject;\n    });\n    this.finished = new Promise(/*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(function* (resolve, reject) {\n        _this7.finishedResolve = () => {\n          if (_this7.committedTo === null) {\n            throw new Error('NavigateEvent should have been committed before resolving finished promise.');\n          }\n          resolve(_this7.committedTo);\n        };\n        _this7.finishedReject = reason => {\n          reject(reason);\n          _this7.abortController.abort(reason);\n        };\n      });\n      return function (_x, _x2) {\n        return _ref5.apply(this, arguments);\n      };\n    }());\n    // All rejections are handled.\n    this.committed.catch(() => {});\n    this.finished.catch(() => {});\n  }\n}\nclass Log {\n  constructor() {\n    _defineProperty(this, \"logItems\", void 0);\n    this.logItems = [];\n  }\n  add(value) {\n    this.logItems.push(value);\n  }\n  fn(value) {\n    return () => {\n      this.logItems.push(value);\n    };\n  }\n  clear() {\n    this.logItems = [];\n  }\n  result() {\n    return this.logItems.join('; ');\n  }\n}\n_Log = Log;\n_defineProperty(Log, \"\\u0275fac\", function Log_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Log)();\n});\n_defineProperty(Log, \"\\u0275prov\", /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _Log,\n  factory: _Log.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Log, [{\n    type: Injectable\n  }], () => [], null);\n})();\nexport { ComponentFixture, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, DeferBlockFixture, InjectSetupWrapper, TestBed, TestComponentRenderer, __core_private_testing_placeholder__, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, getTestBed, inject, resetFakeAsyncZone, tick, waitForAsync, withModule, FakeNavigation as ɵFakeNavigation, Log as ɵLog, MetadataOverrider as ɵMetadataOverrider };", "map": {"version": 3, "names": ["i0", "inject", "inject$1", "NgZone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Injectable", "ɵDeferBlockState", "_DeferBlockState", "ɵtriggerResourceLoading", "_triggerResourceLoading", "ɵrenderDeferBlockState", "_renderDeferBlockState", "ɵCONTAINER_HEADER_OFFSET", "_CONTAINER_HEADER_OFFSET", "ɵgetDeferBlocks", "_getDeferBlocks", "InjectionToken", "ɵDeferBlockBehavior", "_DeferBlockBehavior", "ɵNoopNgZone", "_NoopNgZone", "ApplicationRef", "ɵPendingTasksInternal", "_PendingTasksInternal", "ɵZONELESS_ENABLED", "_ZONELESS_ENABLED", "ɵChangeDetectionScheduler", "_ChangeDetectionScheduler", "ɵEffectScheduler", "_EffectScheduler", "ɵMicrotaskEffectScheduler", "_MicrotaskEffectScheduler", "getDebugNode", "RendererFactory2", "ɵstringify", "_stringify", "<PERSON><PERSON>", "Directive", "Component", "NgModule", "ɵReflectionCapabilities", "_ReflectionCapabilities", "ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT", "_USE_RUNTIME_DEPS_TRACKER_FOR_JIT", "ɵdepsTracker", "_depsTracker", "ɵgetInjectableDef", "_getInjectableDef", "resolveForwardRef", "ɵisComponentDefPendingResolution", "_isComponentDefPendingResolution", "ɵgetAsyncClassMetadataFn", "_getAsyncClassMetadataFn", "ɵresolveComponentResources", "_resolveComponentResources", "ɵRender3NgModuleRef", "_Render3NgModuleRef", "ApplicationInitStatus", "LOCALE_ID", "ɵDEFAULT_LOCALE_ID", "_DEFAULT_LOCALE_ID", "ɵsetLocaleId", "_setLocaleId", "ɵRender3ComponentFactory", "_Render3ComponentFactory", "ɵNG_COMP_DEF", "_NG_COMP_DEF", "ɵcompileComponent", "_compileComponent", "ɵNG_DIR_DEF", "_NG_DIR_DEF", "ɵcompileDirective", "_compileDirective", "ɵNG_PIPE_DEF", "_NG_PIPE_DEF", "ɵcompilePipe", "_compilePipe", "ɵNG_MOD_DEF", "_NG_MOD_DEF", "ɵpatchComponentDefWithScope", "_patchComponentDefWithScope", "ɵNG_INJ_DEF", "_NG_INJ_DEF", "ɵcompileNgModuleDefs", "_compileNgModuleDefs", "ɵclearResolutionOfComponentResourcesQueue", "_clearResolutionOfComponentResourcesQueue", "ɵrestoreComponentResolutionQueue", "_restoreComponentResolutionQueue", "ɵinternalProvideZoneChangeDetection", "_internalProvideZoneChangeDetection", "ɵChangeDetectionSchedulerImpl", "_ChangeDetectionSchedulerImpl", "Compiler", "ɵDEFER_BLOCK_CONFIG", "_DEFER_BLOCK_CONFIG", "ɵINTERNAL_APPLICATION_ERROR_HANDLER", "_INTERNAL_APPLICATION_ERROR_HANDLER", "COMPILER_OPTIONS", "Injector", "ɵtransitiveScopesFor", "_transitiveScopesFor", "ɵgenerateStandaloneInDeclarationsError", "_generateStandaloneInDeclarationsError", "ɵNgModuleFactory", "_NgModuleFactory", "ModuleWithComponentFactories", "ɵisEnvironmentProviders", "_isEnvironmentProviders", "ɵconvertToBitFlags", "_convertToBitFlags", "InjectFlags", "ɵsetAllowDuplicateNgModuleIdsForTest", "_setAllowDuplicateNgModuleIdsForTest", "ɵresetCompiledComponents", "_resetCompiledComponents", "ɵsetUnknownElementStrictMode", "_setUnknownElementStrictMode", "ɵsetUnknownPropertyStrictMode", "_setUnknownPropertyStrictMode", "ɵgetUnknownElementStrictMode", "_getUnknownElementStrictMode", "ɵgetUnknownPropertyStrictMode", "_getUnknownPropertyStrictMode", "runInInjectionContext", "EnvironmentInjector", "ɵflushModuleScopingQueueAsMuchAsPossible", "_flushModuleScopingQueueAsMuchAsPossible", "DeferBlockBehavior", "DeferBlockState", "Subscription", "Resource<PERSON><PERSON>der", "waitForAsync", "fn", "_Zone", "Zone", "Promise", "reject", "asyncTest", "__symbol__", "RETHROW_APPLICATION_ERRORS_DEFAULT", "TestBedApplicationErrorHandler", "constructor", "_defineProperty", "Set", "handleError", "e", "zone", "runOutsideAngular", "userErrorHandler", "userError", "whenStableRejectFunctions", "size", "values", "clear", "_TestBedApplicationErrorHandler", "TestBedApplicationErrorHandler_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "ngDevMode", "ɵsetClassMetadata", "type", "DeferBlockFixture", "block", "componentFixture", "render", "state", "_this", "_asyncToGenerator", "hasStateTemplate", "stateAsString", "getDeferBlockStateNameFromEnum", "Error", "toLowerCase", "Complete", "tDetails", "lView", "tNode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uling", "lContainer", "detectChanges", "getDeferBlocks", "deferBlocks", "deferBlockFixtures", "length", "push", "resolve", "Placeholder", "placeholderTmplIndex", "Loading", "loadingTmplIndex", "errorTmplIndex", "TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT", "THROW_ON_UNKNOWN_ELEMENTS_DEFAULT", "THROW_ON_UNKNOWN_PROPERTIES_DEFAULT", "DEFER_BLOCK_DEFAULT_BEHAVIOR", "Playthrough", "TestComponent<PERSON><PERSON><PERSON>", "insertRootElement", "rootElementId", "removeAllRootElements", "ComponentFixtureAutoDetect", "ComponentFixtureNoNgZone", "ComponentFixture", "componentRef", "_inject$", "optional", "_noZoneOptionIsSet", "_appRef", "zonelessEnabled", "autoDetectDefault", "_ngZone", "changeDetectorRef", "elementRef", "location", "debugElement", "nativeElement", "componentInstance", "instance", "autoDetect", "_this$scheduler", "_this$scheduler2", "_testAppRef", "externalTestViews", "add", "<PERSON><PERSON><PERSON><PERSON>", "scheduler", "notify", "onDestroy", "delete", "subscriptions", "onError", "subscribe", "next", "error", "checkNoChanges", "microtaskEffectScheduler", "flush", "originalCheckNoChanges", "tick", "run", "rootEffectScheduler", "autoDetectChanges", "isStable", "pendingTasks", "hasPendingTasks", "value", "whenStable", "appError<PERSON><PERSON><PERSON>", "then", "_get<PERSON><PERSON><PERSON>", "_renderer", "undefined", "injector", "get", "whenRenderingDone", "renderer", "destroy", "unsubscribe", "_isDestroyed", "fakeAsyncTestModule", "fakeAsyncTestModuleNotLoadedErrorMessage", "resetFakeAsyncZone", "resetFakeAsyncZoneIfExists", "_Zone$ProxyZoneSpec", "isLoaded", "fakeAsync", "options", "millis", "tickOptions", "processNewMacroTasksSynchronously", "maxTurns", "discardPeriodicTasks", "flushMicrotasks", "_nextReferenceId", "MetadataOverrider", "Map", "overrideMetadata", "metadataClass", "oldMetadata", "override", "props", "_valueProps", "for<PERSON>ach", "prop", "set", "remove", "setMetadata", "removeMetadata", "_references", "addMetadata", "metadata", "references", "removeObjects", "removeValue", "Array", "isArray", "_propH<PERSON><PERSON><PERSON>", "propValue", "filter", "has", "addValue", "concat", "propName", "nextObjectId", "objectIds", "replacer", "key", "_serializeReference", "JSON", "stringify", "ref", "id", "obj", "Object", "keys", "startsWith", "proto", "getPrototypeOf", "protoProp", "desc", "getOwnPropertyDescriptor", "reflection", "OverrideResolver", "addOverride", "overrides", "resolved", "setOverrides", "getAnnotation", "annotations", "i", "annotation", "isKnownType", "overrider", "DirectiveResolver", "ComponentResolver", "PipeResolver", "NgModuleResolver", "TestingModuleOverride", "isTestingModuleOverride", "DECLARATION", "OVERRIDE_TEMPLATE", "assertNoStandaloneComponents", "types", "resolver", "component", "standalone", "TestBedCompiler", "platform", "additionalModuleTypes", "initResolvers", "DynamicTestModule", "testModuleType", "setCompilerProviders", "providers", "compilerProviders", "_injector", "configureTestingModule", "moduleDef", "_moduleDef$deferBlock", "_moduleDef$rethrowApp", "declarations", "resolvers", "queueTypeArray", "imports", "queueTypesFromModulesArray", "schemas", "deferBlock<PERSON><PERSON><PERSON>or", "rethrowApplicationTickErrors", "rethrowApplicationErrors", "overrideModule", "ngModule", "clearScopeCacheFor", "overriddenModules", "module", "invalidTypeError", "name", "recompileNgModule", "overrideComponent", "verifyNoStandaloneFlagOverrides", "pendingComponents", "maybeRegisterComponentWithAsyncMetadata", "overrideDirective", "directive", "pendingDirectives", "overridePipe", "pipe", "pending<PERSON><PERSON>es", "_override$add", "_override$set", "_override$remove", "hasOwnProperty", "override<PERSON><PERSON><PERSON>", "provider", "providerDef", "useFactory", "provide", "deps", "multi", "useValue", "injectableDef", "providedIn", "overridesBucket", "rootProviderOverrides", "providerOverrides", "providerOverridesByToken", "existingOverrides", "providerOverridesByModule", "overrideTemplateUsingTestingModule", "template", "def", "hasStyleUrls", "_metadata$styleUrls", "styleUrl", "styleUrls", "overrideStyleUrls", "styles", "existingComponentStyles", "componentToModuleScope", "resolvePendingComponentsWithAsyncMetadata", "_this2", "componentsWithAsyncMetadata", "promises", "asyncMetadataFn", "resolvedDeps", "all", "flatResolvedDeps", "flat", "applyProviderOverridesInScope", "compileComponents", "_this3", "clearComponentResolutionQueue", "needsAsyncResources", "compileTypesSync", "resourceLoader", "url", "finalize", "compileTestModule", "applyTransitiveScopes", "applyProviderOverrides", "patchComponentsWithExistingStyles", "parentInjector", "testModuleRef", "runInitializers", "localeId", "_compileNgModuleSync", "moduleType", "_compileNgModuleAsync", "_this4", "_getModuleResolver", "_getComponentFactories", "maybeUnwrapFn", "ɵmod", "reduce", "factories", "declaration", "componentDef", "ɵcmp", "maybeStoreNgDef", "testingModuleDef", "affectedModules", "collectModulesAffectedByOverrides", "storeFieldOfDefOnType", "transitiveCompileScopes", "moduleToScope", "getScopeOfModule", "isTestingModule", "realType", "componentType", "moduleScope", "getComponentDef", "maybeApplyOverrides", "field", "hasProviderOverrides", "patchDefWithProviderOverrides", "seenComponents", "seenDirectives", "hasScope", "isStandaloneComponent", "isNgModule", "scopesWithOverriddenProviders", "injectorDef", "_def$dependencies", "dependencies", "dependency", "getOverriddenProviders", "importedModule", "flatten", "isModuleWithProviders", "defCleanupOps", "object", "fieldName", "originalValue", "arr", "queueType", "processedDefs", "queueTypesFromModulesArrayRecur", "hasNgModuleDef", "exports", "_def$dependencies2", "seenModules", "calcAffectedModulesRecur", "path", "item", "initialNgDefs", "currentDefs", "currentDef", "defField", "originalComponentResolutionQueue", "restoreComponentResolutionQueue", "restoreOriginalState", "forEachRight", "op", "defs", "descriptor", "defineProperty", "RootScopeModule", "useExisting", "R3TestCompiler", "behavior", "handler", "ngZone", "compilerOptions", "opts", "create", "parent", "getSingleProviderOverrides", "getProviderToken", "getProviderOverrides", "flattenProviders", "flattenedProviders", "overriddenProviders", "final", "seenOverriddenProviders", "unshift", "_objectSpread", "providersResolver", "processProvidersFn", "ngDef", "_value$ɵcmp", "maybeFn", "Function", "out", "identityFn", "mapFn", "ɵproviders", "getProviderField", "idx", "expectedType", "testBed", "compileModuleSync", "compileModuleAsync", "_this5", "compileModuleAndAllComponentsSync", "ngModuleFactory", "componentFactories", "compileModuleAndAllComponentsAsync", "_this6", "clearCache", "clearCacheFor", "getModuleId", "meta", "_nextRootElementId", "getTestBed", "TestBedImpl", "INSTANCE", "_INSTANCE", "initTestEnvironment", "resetTestEnvironment", "configureCompiler", "config", "overrideTemplate", "notFoundValue", "flags", "THROW_IF_NOT_FOUND", "<PERSON><PERSON><PERSON>", "createComponent", "resetTestingModule", "execute", "tokens", "context", "flushEffects", "_environmentTeardownOptions", "teardown", "_environmentErrorOnUnknownElementsOption", "errorOnUnknownElements", "_environmentErrorOnUnknownPropertiesOption", "errorOnUnknownProperties", "_compiler", "_this$_previousErrorO", "_this$_previousErrorO2", "checkGlobalCompilationFinished", "compiler", "_previousErrorOnUnknownElementsOption", "_previousErrorOnUnknownPropertiesOption", "destroyActiveFixtures", "shouldTearDownTestingModule", "tearDownTestingModule", "_testModuleRef", "_instanceTeardownOptions", "_instanceErrorOnUnknownElementsOption", "_instanceErrorOnUnknownPropertiesOption", "_instanceDeferBlockBehavior", "useJit", "_moduleDef$deferBlock2", "assertNotInstantiated", "shouldThrowErrorOnUnknownElements", "shouldThrowErrorOnUnknownProperties", "TestBed", "UNDEFINED", "result", "params", "map", "t", "apply", "templateUrl", "testComponent<PERSON><PERSON><PERSON>", "rootElId", "componentFactory", "initComponent", "NULL", "noNgZone", "fixture", "_activeFixtures", "methodName", "methodDescription", "globalCompilationChecked", "errorCount", "console", "stacktrace", "shouldRethrowTeardownErrors", "_ref", "_instanceOptions$reth", "instanceOptions", "environmentOptions", "rethrowErrors", "_ref2", "_this$_instanceErrorO", "_ref3", "_this$_instanceErrorO2", "_ref4", "_this$_instanceTeardo", "_this$_instanceTeardo2", "_TestBedImpl$_environ", "destroyAfterEach", "getDeferBlockBehavior", "<PERSON><PERSON><PERSON><PERSON>", "_testRenderer$removeA", "call", "InjectSetupWrapper", "_moduleDef", "_addModule", "self", "withModule", "_globalThis$beforeEac", "globalThis", "beforeEach", "getCleanupHook", "_globalThis$afterEach", "after<PERSON>ach", "expectedTeardownValue", "__core_private_testing_placeholder__", "FakeNavigation", "currentEntry", "entriesArr", "currentEntryIndex", "canGoBack", "canGoForward", "window", "_window", "doc", "startURL", "_document$defaultView", "createEventTarget", "createElement", "_unused", "EventTarget", "document", "defaultView", "eventTarget", "setInitialEntryForTesting", "historyState", "_currentInitialEntry$", "_currentInitialEntry$2", "canSetInitialEntry", "currentInitialEntry", "FakeNavigationHistoryEntry", "URL", "toString", "index", "String", "<PERSON><PERSON><PERSON>", "nextId", "sameDocument", "canSetInitialEntryForTesting", "setSynchronousTraversalsForTesting", "synchronousTraversals", "entries", "slice", "navigate", "fromUrl", "toUrl", "navigationType", "history", "hashChange", "isHashChange", "destination", "FakeNavigationDestination", "InternalNavigationResult", "intercepted", "userAgentNavigate", "cancelable", "canIntercept", "userInitiated", "info", "updateNavigationEntriesForSameDocumentNavigation", "navigateEvent", "committed", "finished", "pushState", "data", "title", "pushOrReplaceState", "replaceState", "_title", "traverseTo", "entry", "findEntry", "domException", "DOMException", "catch", "traversalQueue", "existingResult", "getState", "getHistoryState", "prospectiveEntryIndex", "runTraversal", "userAgentTraverse", "back", "forward", "go", "direction", "targetIndex", "traversal", "nextTraversal", "setTimeout", "addEventListener", "callback", "removeEventListener", "dispatchEvent", "event", "dispose", "disposed", "isDisposed", "cancel", "dispatchNavigateEvent", "signal", "urlAndHistoryUpdateSteps", "oldUrl", "popStateEvent", "createPopStateEvent", "hashchangeEvent", "createHashChangeEvent", "oldCurrentNHE", "disposedNHEs", "splice", "newNHE", "committedResolve", "currentEntryChangeEvent", "createFakeNavigationCurrentEntryChangeEvent", "from", "disposedNHE", "onnavigate", "_handler", "oncurrententrychange", "onnavigatesuccess", "onnavigateerror", "transition", "_transition", "updateCurrentEntry", "_options", "reload", "parse", "disposeEvent", "Event", "navigation", "bubbles", "focusResetBehavior", "scroll<PERSON>eh<PERSON>or", "interceptionState", "downloadRequest", "formData", "precommitHandlers", "handlers", "intercept", "_options$focusReset", "_options$scroll", "precommit<PERSON><PERSON><PERSON>", "focusReset", "scroll", "processScrollBehavior", "redirect", "href", "commit", "aborted", "InternalNavigationTransition", "promisesList", "finishNavigationEvent", "navigatesuccessEvent", "finishedResolve", "reason", "navigateerrorEvent", "finishedReject", "dispatch", "precommitController", "precommitPromisesList", "did<PERSON><PERSON><PERSON>", "potentiallyResetFocus", "potentiallyResetScroll", "newURL", "oldURL", "to", "hash", "hostname", "pathname", "search", "abortController", "_this7", "AbortController", "committedTo", "committedReject", "_ref5", "abort", "_x", "_x2", "arguments", "Log", "logItems", "join", "_Log", "Log_Factory", "ɵFakeNavigation", "ɵLog", "ɵMetadataOverrider"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/core/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { inject as inject$1, <PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, Injectable, ɵDeferBlockState as _DeferBlockState, ɵtriggerResourceLoading as _triggerResourceLoading, ɵrenderDeferBlockState as _renderDeferBlockState, ɵCONTAINER_HEADER_OFFSET as _CONTAINER_HEADER_OFFSET, ɵgetDeferBlocks as _getDeferBlocks, InjectionToken, ɵDeferBlockBehavior as _DeferBlockBehavior, ɵNoopNgZone as _NoopNgZone, ApplicationRef, ɵPendingTasksInternal as _PendingTasksInternal, ɵZONELESS_ENABLED as _ZONELESS_ENABLED, ɵChangeDetectionScheduler as _ChangeDetectionScheduler, ɵEffectScheduler as _EffectScheduler, ɵMicrotaskEffectScheduler as _MicrotaskEffectScheduler, getDebugNode, RendererFactory2, ɵstringify as _stringify, Pipe, Directive, Component, NgModule, ɵReflectionCapabilities as _ReflectionCapabilities, ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT as _USE_RUNTIME_DEPS_TRACKER_FOR_JIT, ɵdepsTracker as _depsTracker, ɵgetInjectableDef as _getInjectableDef, resolveForwardRef, ɵisComponentDefPendingResolution as _isComponentDefPendingResolution, ɵgetAsyncClassMetadataFn as _getAsyncClassMetadataFn, ɵresolveComponentResources as _resolveComponentResources, ɵRender3NgModuleRef as _Render3NgModuleRef, ApplicationInitStatus, LOCALE_ID, ɵDEFAULT_LOCALE_ID as _DEFAULT_LOCALE_ID, ɵsetLocaleId as _setLocaleId, ɵRender3ComponentFactory as _Render3ComponentFactory, ɵNG_COMP_DEF as _NG_COMP_DEF, ɵcompileComponent as _compileComponent, ɵNG_DIR_DEF as _NG_DIR_DEF, ɵcompileDirective as _compileDirective, ɵNG_PIPE_DEF as _NG_PIPE_DEF, ɵcompilePipe as _compilePipe, ɵNG_MOD_DEF as _NG_MOD_DEF, ɵpatchComponentDefWithScope as _patchComponentDefWithScope, ɵNG_INJ_DEF as _NG_INJ_DEF, ɵcompileNgModuleDefs as _compileNgModuleDefs, ɵclearResolutionOfComponentResourcesQueue as _clearResolutionOfComponentResourcesQueue, ɵrestoreComponentResolutionQueue as _restoreComponentResolutionQueue, ɵinternalProvideZoneChangeDetection as _internalProvideZoneChangeDetection, ɵChangeDetectionSchedulerImpl as _ChangeDetectionSchedulerImpl, Compiler, ɵDEFER_BLOCK_CONFIG as _DEFER_BLOCK_CONFIG, ɵINTERNAL_APPLICATION_ERROR_HANDLER as _INTERNAL_APPLICATION_ERROR_HANDLER, COMPILER_OPTIONS, Injector, ɵtransitiveScopesFor as _transitiveScopesFor, ɵgenerateStandaloneInDeclarationsError as _generateStandaloneInDeclarationsError, ɵNgModuleFactory as _NgModuleFactory, ModuleWithComponentFactories, ɵisEnvironmentProviders as _isEnvironmentProviders, ɵconvertToBitFlags as _convertToBitFlags, InjectFlags, ɵsetAllowDuplicateNgModuleIdsForTest as _setAllowDuplicateNgModuleIdsForTest, ɵresetCompiledComponents as _resetCompiledComponents, ɵsetUnknownElementStrictMode as _setUnknownElementStrictMode, ɵsetUnknownPropertyStrictMode as _setUnknownPropertyStrictMode, ɵgetUnknownElementStrictMode as _getUnknownElementStrictMode, ɵgetUnknownPropertyStrictMode as _getUnknownPropertyStrictMode, runInInjectionContext, EnvironmentInjector, ɵflushModuleScopingQueueAsMuchAsPossible as _flushModuleScopingQueueAsMuchAsPossible } from '@angular/core';\nexport { ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { ResourceLoader } from '@angular/compiler';\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```ts\n * it('...', waitForAsync(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * })));\n * ```\n *\n * @publicApi\n */\nfunction waitForAsync(fn) {\n    const _Zone = typeof Zone !== 'undefined' ? Zone : null;\n    if (!_Zone) {\n        return function () {\n            return Promise.reject('Zone is needed for the waitForAsync() test helper but could not be found. ' +\n                'Please make sure that your environment includes zone.js');\n        };\n    }\n    const asyncTest = _Zone && _Zone[_Zone.__symbol__('asyncTest')];\n    if (typeof asyncTest === 'function') {\n        return asyncTest(fn);\n    }\n    return function () {\n        return Promise.reject('zone-testing.js is needed for the async() test helper but could not be found. ' +\n            'Please make sure that your environment includes zone.js/testing');\n    };\n}\n\nconst RETHROW_APPLICATION_ERRORS_DEFAULT = true;\nclass TestBedApplicationErrorHandler {\n    zone = inject$1(NgZone);\n    userErrorHandler = inject$1(ErrorHandler);\n    whenStableRejectFunctions = new Set();\n    handleError(e) {\n        try {\n            this.zone.runOutsideAngular(() => this.userErrorHandler.handleError(e));\n        }\n        catch (userError) {\n            e = userError;\n        }\n        // Instead of throwing the error when there are outstanding `fixture.whenStable` promises,\n        // reject those promises with the error. This allows developers to write\n        // expectAsync(fix.whenStable()).toBeRejected();\n        if (this.whenStableRejectFunctions.size > 0) {\n            for (const fn of this.whenStableRejectFunctions.values()) {\n                fn(e);\n            }\n            this.whenStableRejectFunctions.clear();\n        }\n        else {\n            throw e;\n        }\n    }\n    static ɵfac = function TestBedApplicationErrorHandler_Factory(__ngFactoryType__) { return new (__ngFactoryType__ || TestBedApplicationErrorHandler)(); };\n    static ɵprov = /*@__PURE__*/ i0.ɵɵdefineInjectable({ token: TestBedApplicationErrorHandler, factory: TestBedApplicationErrorHandler.ɵfac });\n}\n(() => { (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TestBedApplicationErrorHandler, [{\n        type: Injectable\n    }], null, null); })();\n\n/**\n * Represents an individual defer block for testing purposes.\n *\n * @publicApi\n */\nclass DeferBlockFixture {\n    block;\n    componentFixture;\n    /** @docs-private */\n    constructor(block, componentFixture) {\n        this.block = block;\n        this.componentFixture = componentFixture;\n    }\n    /**\n     * Renders the specified state of the defer fixture.\n     * @param state the defer state to render\n     */\n    async render(state) {\n        if (!hasStateTemplate(state, this.block)) {\n            const stateAsString = getDeferBlockStateNameFromEnum(state);\n            throw new Error(`Tried to render this defer block in the \\`${stateAsString}\\` state, ` +\n                `but there was no @${stateAsString.toLowerCase()} block defined in a template.`);\n        }\n        if (state === _DeferBlockState.Complete) {\n            await _triggerResourceLoading(this.block.tDetails, this.block.lView, this.block.tNode);\n        }\n        // If the `render` method is used explicitly - skip timer-based scheduling for\n        // `@placeholder` and `@loading` blocks and render them immediately.\n        const skipTimerScheduling = true;\n        _renderDeferBlockState(state, this.block.tNode, this.block.lContainer, skipTimerScheduling);\n        this.componentFixture.detectChanges();\n    }\n    /**\n     * Retrieves all nested child defer block fixtures\n     * in a given defer block.\n     */\n    getDeferBlocks() {\n        const deferBlocks = [];\n        // An LContainer that represents a defer block has at most 1 view, which is\n        // located right after an LContainer header. Get a hold of that view and inspect\n        // it for nested defer blocks.\n        const deferBlockFixtures = [];\n        if (this.block.lContainer.length >= _CONTAINER_HEADER_OFFSET) {\n            const lView = this.block.lContainer[_CONTAINER_HEADER_OFFSET];\n            _getDeferBlocks(lView, deferBlocks);\n            for (const block of deferBlocks) {\n                deferBlockFixtures.push(new DeferBlockFixture(block, this.componentFixture));\n            }\n        }\n        return Promise.resolve(deferBlockFixtures);\n    }\n}\nfunction hasStateTemplate(state, block) {\n    switch (state) {\n        case _DeferBlockState.Placeholder:\n            return block.tDetails.placeholderTmplIndex !== null;\n        case _DeferBlockState.Loading:\n            return block.tDetails.loadingTmplIndex !== null;\n        case _DeferBlockState.Error:\n            return block.tDetails.errorTmplIndex !== null;\n        case _DeferBlockState.Complete:\n            return true;\n        default:\n            return false;\n    }\n}\nfunction getDeferBlockStateNameFromEnum(state) {\n    switch (state) {\n        case _DeferBlockState.Placeholder:\n            return 'Placeholder';\n        case _DeferBlockState.Loading:\n            return 'Loading';\n        case _DeferBlockState.Error:\n            return 'Error';\n        default:\n            return 'Main';\n    }\n}\n\n/** Whether test modules should be torn down by default. */\nconst TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;\n/** Whether unknown elements in templates should throw by default. */\nconst THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;\n/** Whether unknown properties in templates should throw by default. */\nconst THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;\n/** Whether defer blocks should use manual triggering or play through normally. */\nconst DEFER_BLOCK_DEFAULT_BEHAVIOR = _DeferBlockBehavior.Playthrough;\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @publicApi\n */\nclass TestComponentRenderer {\n    insertRootElement(rootElementId) { }\n    removeAllRootElements() { }\n}\n/**\n * @publicApi\n */\nconst ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');\n/**\n * @publicApi\n */\nconst ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @publicApi\n */\nclass ComponentFixture {\n    componentRef;\n    /**\n     * The DebugElement associated with the root element of this component.\n     */\n    debugElement;\n    /**\n     * The instance of the root component class.\n     */\n    componentInstance;\n    /**\n     * The native element at the root of the component.\n     */\n    nativeElement;\n    /**\n     * The ElementRef for the element at the root of the component.\n     */\n    elementRef;\n    /**\n     * The ChangeDetectorRef for the component\n     */\n    changeDetectorRef;\n    _renderer;\n    _isDestroyed = false;\n    /** @internal */\n    _noZoneOptionIsSet = inject$1(ComponentFixtureNoNgZone, { optional: true });\n    /** @internal */\n    _ngZone = this._noZoneOptionIsSet ? new _NoopNgZone() : inject$1(NgZone);\n    // Inject ApplicationRef to ensure NgZone stableness causes after render hooks to run\n    // This will likely happen as a result of fixture.detectChanges because it calls ngZone.run\n    // This is a crazy way of doing things but hey, it's the world we live in.\n    // The zoneless scheduler should instead do this more imperatively by attaching\n    // the `ComponentRef` to `ApplicationRef` and calling `appRef.tick` as the `detectChanges`\n    // behavior.\n    /** @internal */\n    _appRef = inject$1(ApplicationRef);\n    _testAppRef = this._appRef;\n    pendingTasks = inject$1(_PendingTasksInternal);\n    appErrorHandler = inject$1(TestBedApplicationErrorHandler);\n    zonelessEnabled = inject$1(_ZONELESS_ENABLED);\n    scheduler = inject$1(_ChangeDetectionScheduler);\n    rootEffectScheduler = inject$1(_EffectScheduler);\n    microtaskEffectScheduler = inject$1(_MicrotaskEffectScheduler);\n    autoDetectDefault = this.zonelessEnabled ? true : false;\n    autoDetect = inject$1(ComponentFixtureAutoDetect, { optional: true }) ?? this.autoDetectDefault;\n    subscriptions = new Subscription();\n    // TODO(atscott): Remove this from public API\n    ngZone = this._noZoneOptionIsSet ? null : this._ngZone;\n    /** @docs-private */\n    constructor(componentRef) {\n        this.componentRef = componentRef;\n        this.changeDetectorRef = componentRef.changeDetectorRef;\n        this.elementRef = componentRef.location;\n        this.debugElement = getDebugNode(this.elementRef.nativeElement);\n        this.componentInstance = componentRef.instance;\n        this.nativeElement = this.elementRef.nativeElement;\n        this.componentRef = componentRef;\n        if (this.autoDetect) {\n            this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n            this.scheduler?.notify(8 /* ɵNotificationSource.ViewAttached */);\n            this.scheduler?.notify(0 /* ɵNotificationSource.MarkAncestorsForTraversal */);\n        }\n        this.componentRef.hostView.onDestroy(() => {\n            this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n        });\n        // Create subscriptions outside the NgZone so that the callbacks run outside\n        // of NgZone.\n        this._ngZone.runOutsideAngular(() => {\n            this.subscriptions.add(this._ngZone.onError.subscribe({\n                next: (error) => {\n                    throw error;\n                },\n            }));\n        });\n    }\n    /**\n     * Trigger a change detection cycle for the component.\n     */\n    detectChanges(checkNoChanges = true) {\n        this.microtaskEffectScheduler.flush();\n        const originalCheckNoChanges = this.componentRef.changeDetectorRef.checkNoChanges;\n        try {\n            if (!checkNoChanges) {\n                this.componentRef.changeDetectorRef.checkNoChanges = () => { };\n            }\n            if (this.zonelessEnabled) {\n                try {\n                    this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n                    this._appRef.tick();\n                }\n                finally {\n                    if (!this.autoDetect) {\n                        this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n                    }\n                }\n            }\n            else {\n                // Run the change detection inside the NgZone so that any async tasks as part of the change\n                // detection are captured by the zone and can be waited for in isStable.\n                this._ngZone.run(() => {\n                    // Flush root effects before `detectChanges()`, to emulate the sequencing of `tick()`.\n                    this.rootEffectScheduler.flush();\n                    this.changeDetectorRef.detectChanges();\n                    this.checkNoChanges();\n                });\n            }\n        }\n        finally {\n            this.componentRef.changeDetectorRef.checkNoChanges = originalCheckNoChanges;\n        }\n        this.microtaskEffectScheduler.flush();\n    }\n    /**\n     * Do a change detection run to make sure there were no changes.\n     */\n    checkNoChanges() {\n        this.changeDetectorRef.checkNoChanges();\n    }\n    /**\n     * Set whether the fixture should autodetect changes.\n     *\n     * Also runs detectChanges once so that any existing change is detected.\n     *\n     * @param autoDetect Whether to autodetect changes. By default, `true`.\n     */\n    autoDetectChanges(autoDetect = true) {\n        if (this._noZoneOptionIsSet && !this.zonelessEnabled) {\n            throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set.');\n        }\n        if (autoDetect !== this.autoDetect) {\n            if (autoDetect) {\n                this._testAppRef.externalTestViews.add(this.componentRef.hostView);\n            }\n            else {\n                this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n            }\n        }\n        this.autoDetect = autoDetect;\n        this.detectChanges();\n    }\n    /**\n     * Return whether the fixture is currently stable or has async tasks that have not been completed\n     * yet.\n     */\n    isStable() {\n        return !this.pendingTasks.hasPendingTasks.value;\n    }\n    /**\n     * Get a promise that resolves when the fixture is stable.\n     *\n     * This can be used to resume testing after events have triggered asynchronous activity or\n     * asynchronous change detection.\n     */\n    whenStable() {\n        if (this.isStable()) {\n            return Promise.resolve(false);\n        }\n        return new Promise((resolve, reject) => {\n            this.appErrorHandler.whenStableRejectFunctions.add(reject);\n            this._appRef.whenStable().then(() => {\n                this.appErrorHandler.whenStableRejectFunctions.delete(reject);\n                resolve(true);\n            });\n        });\n    }\n    /**\n     * Retrieves all defer block fixtures in the component fixture.\n     */\n    getDeferBlocks() {\n        const deferBlocks = [];\n        const lView = this.componentRef.hostView['_lView'];\n        _getDeferBlocks(lView, deferBlocks);\n        const deferBlockFixtures = [];\n        for (const block of deferBlocks) {\n            deferBlockFixtures.push(new DeferBlockFixture(block, this));\n        }\n        return Promise.resolve(deferBlockFixtures);\n    }\n    _getRenderer() {\n        if (this._renderer === undefined) {\n            this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n        }\n        return this._renderer;\n    }\n    /**\n     * Get a promise that resolves when the ui state is stable following animations.\n     */\n    whenRenderingDone() {\n        const renderer = this._getRenderer();\n        if (renderer && renderer.whenRenderingDone) {\n            return renderer.whenRenderingDone();\n        }\n        return this.whenStable();\n    }\n    /**\n     * Trigger component destruction.\n     */\n    destroy() {\n        this.subscriptions.unsubscribe();\n        this._testAppRef.externalTestViews.delete(this.componentRef.hostView);\n        if (!this._isDestroyed) {\n            this.componentRef.destroy();\n            this._isDestroyed = true;\n        }\n    }\n}\n\nconst _Zone = typeof Zone !== 'undefined' ? Zone : null;\nconst fakeAsyncTestModule = _Zone && _Zone[_Zone.__symbol__('fakeAsyncTest')];\nconst fakeAsyncTestModuleNotLoadedErrorMessage = `zone-testing.js is needed for the fakeAsync() test helper but could not be found.\n        Please make sure that your environment includes zone.js/testing`;\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @publicApi\n */\nfunction resetFakeAsyncZone() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.resetFakeAsyncZone();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nfunction resetFakeAsyncZoneIfExists() {\n    if (fakeAsyncTestModule && Zone['ProxyZoneSpec']?.isLoaded()) {\n        fakeAsyncTestModule.resetFakeAsyncZone();\n    }\n}\n/**\n * Wraps a function to be executed in the `fakeAsync` zone:\n * - Microtasks are manually executed by calling `flushMicrotasks()`.\n * - Timers are synchronous; `tick()` simulates the asynchronous passage of time.\n *\n * Can be used to wrap `inject()` calls.\n *\n * @param fn The function that you want to wrap in the `fakeAsync` zone.\n * @param options\n *   - flush: When true, will drain the macrotask queue after the test function completes.\n *     When false, will throw an exception at the end of the function if there are pending timers.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n *\n * @returns The function wrapped to be executed in the `fakeAsync` zone.\n * Any arguments passed when calling this returned function will be passed through to the `fn`\n * function in the parameters when it is called.\n *\n * @publicApi\n */\nfunction fakeAsync(fn, options) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.fakeAsync(fn, options);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the `fakeAsync` zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * @param millis The number of milliseconds to advance the virtual timer.\n * @param tickOptions The options to pass to the `tick()` function.\n *\n * @usageNotes\n *\n * The `tick()` option is a flag called `processNewMacroTasksSynchronously`,\n * which determines whether or not to invoke new macroTasks.\n *\n * If you provide a `tickOptions` object, but do not specify a\n * `processNewMacroTasksSynchronously` property (`tick(100, {})`),\n * then `processNewMacroTasksSynchronously` defaults to true.\n *\n * If you omit the `tickOptions` parameter (`tick(100))`), then\n * `tickOptions` defaults to `{processNewMacroTasksSynchronously: true}`.\n *\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * The following example includes a nested timeout (new macroTask), and\n * the `tickOptions` parameter is allowed to default. In this case,\n * `processNewMacroTasksSynchronously` defaults to true, and the nested\n * function is executed on each tick.\n *\n * ```ts\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick();\n *   expect(nestedTimeoutInvoked).toBe(true);\n * }));\n * ```\n *\n * In the following case, `processNewMacroTasksSynchronously` is explicitly\n * set to false, so the nested timeout function is not invoked.\n *\n * ```ts\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick(0, {processNewMacroTasksSynchronously: false});\n *   expect(nestedTimeoutInvoked).toBe(false);\n * }));\n * ```\n *\n *\n * @publicApi\n */\nfunction tick(millis = 0, tickOptions = {\n    processNewMacroTasksSynchronously: true,\n}) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.tick(millis, tickOptions);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flushes any pending microtasks and simulates the asynchronous passage of time for the timers in\n * the `fakeAsync` zone by\n * draining the macrotask queue until it is empty.\n *\n * @param maxTurns The maximum number of times the scheduler attempts to clear its queue before\n *     throwing an error.\n * @returns The simulated time elapsed, in milliseconds.\n *\n * @publicApi\n */\nfunction flush(maxTurns) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.flush(maxTurns);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @publicApi\n */\nfunction discardPeriodicTasks() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.discardPeriodicTasks();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flush any pending microtasks.\n *\n * @publicApi\n */\nfunction flushMicrotasks() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.flushMicrotasks();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\nlet _nextReferenceId = 0;\nclass MetadataOverrider {\n    _references = new Map();\n    /**\n     * Creates a new instance for the given metadata class\n     * based on an old instance and overrides.\n     */\n    overrideMetadata(metadataClass, oldMetadata, override) {\n        const props = {};\n        if (oldMetadata) {\n            _valueProps(oldMetadata).forEach((prop) => (props[prop] = oldMetadata[prop]));\n        }\n        if (override.set) {\n            if (override.remove || override.add) {\n                throw new Error(`Cannot set and add/remove ${_stringify(metadataClass)} at the same time!`);\n            }\n            setMetadata(props, override.set);\n        }\n        if (override.remove) {\n            removeMetadata(props, override.remove, this._references);\n        }\n        if (override.add) {\n            addMetadata(props, override.add);\n        }\n        return new metadataClass(props);\n    }\n}\nfunction removeMetadata(metadata, remove, references) {\n    const removeObjects = new Set();\n    for (const prop in remove) {\n        const removeValue = remove[prop];\n        if (Array.isArray(removeValue)) {\n            removeValue.forEach((value) => {\n                removeObjects.add(_propHashKey(prop, value, references));\n            });\n        }\n        else {\n            removeObjects.add(_propHashKey(prop, removeValue, references));\n        }\n    }\n    for (const prop in metadata) {\n        const propValue = metadata[prop];\n        if (Array.isArray(propValue)) {\n            metadata[prop] = propValue.filter((value) => !removeObjects.has(_propHashKey(prop, value, references)));\n        }\n        else {\n            if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n                metadata[prop] = undefined;\n            }\n        }\n    }\n}\nfunction addMetadata(metadata, add) {\n    for (const prop in add) {\n        const addValue = add[prop];\n        const propValue = metadata[prop];\n        if (propValue != null && Array.isArray(propValue)) {\n            metadata[prop] = propValue.concat(addValue);\n        }\n        else {\n            metadata[prop] = addValue;\n        }\n    }\n}\nfunction setMetadata(metadata, set) {\n    for (const prop in set) {\n        metadata[prop] = set[prop];\n    }\n}\nfunction _propHashKey(propName, propValue, references) {\n    let nextObjectId = 0;\n    const objectIds = new Map();\n    const replacer = (key, value) => {\n        if (value !== null && typeof value === 'object') {\n            if (objectIds.has(value)) {\n                return objectIds.get(value);\n            }\n            // Record an id for this object such that any later references use the object's id instead\n            // of the object itself, in order to break cyclic pointers in objects.\n            objectIds.set(value, `ɵobj#${nextObjectId++}`);\n            // The first time an object is seen the object itself is serialized.\n            return value;\n        }\n        else if (typeof value === 'function') {\n            value = _serializeReference(value, references);\n        }\n        return value;\n    };\n    return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\nfunction _serializeReference(ref, references) {\n    let id = references.get(ref);\n    if (!id) {\n        id = `${_stringify(ref)}${_nextReferenceId++}`;\n        references.set(ref, id);\n    }\n    return id;\n}\nfunction _valueProps(obj) {\n    const props = [];\n    // regular public props\n    Object.keys(obj).forEach((prop) => {\n        if (!prop.startsWith('_')) {\n            props.push(prop);\n        }\n    });\n    // getters\n    let proto = obj;\n    while ((proto = Object.getPrototypeOf(proto))) {\n        Object.keys(proto).forEach((protoProp) => {\n            const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n            if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n                props.push(protoProp);\n            }\n        });\n    }\n    return props;\n}\n\nconst reflection = new _ReflectionCapabilities();\n/**\n * Allows to override ivy metadata for tests (via the `TestBed`).\n */\nclass OverrideResolver {\n    overrides = new Map();\n    resolved = new Map();\n    addOverride(type, override) {\n        const overrides = this.overrides.get(type) || [];\n        overrides.push(override);\n        this.overrides.set(type, overrides);\n        this.resolved.delete(type);\n    }\n    setOverrides(overrides) {\n        this.overrides.clear();\n        overrides.forEach(([type, override]) => {\n            this.addOverride(type, override);\n        });\n    }\n    getAnnotation(type) {\n        const annotations = reflection.annotations(type);\n        // Try to find the nearest known Type annotation and make sure that this annotation is an\n        // instance of the type we are looking for, so we can use it for resolution. Note: there might\n        // be multiple known annotations found due to the fact that Components can extend Directives (so\n        // both Directive and Component annotations would be present), so we always check if the known\n        // annotation has the right type.\n        for (let i = annotations.length - 1; i >= 0; i--) {\n            const annotation = annotations[i];\n            const isKnownType = annotation instanceof Directive ||\n                annotation instanceof Component ||\n                annotation instanceof Pipe ||\n                annotation instanceof NgModule;\n            if (isKnownType) {\n                return annotation instanceof this.type ? annotation : null;\n            }\n        }\n        return null;\n    }\n    resolve(type) {\n        let resolved = this.resolved.get(type) || null;\n        if (!resolved) {\n            resolved = this.getAnnotation(type);\n            if (resolved) {\n                const overrides = this.overrides.get(type);\n                if (overrides) {\n                    const overrider = new MetadataOverrider();\n                    overrides.forEach((override) => {\n                        resolved = overrider.overrideMetadata(this.type, resolved, override);\n                    });\n                }\n            }\n            this.resolved.set(type, resolved);\n        }\n        return resolved;\n    }\n}\nclass DirectiveResolver extends OverrideResolver {\n    get type() {\n        return Directive;\n    }\n}\nclass ComponentResolver extends OverrideResolver {\n    get type() {\n        return Component;\n    }\n}\nclass PipeResolver extends OverrideResolver {\n    get type() {\n        return Pipe;\n    }\n}\nclass NgModuleResolver extends OverrideResolver {\n    get type() {\n        return NgModule;\n    }\n}\n\nvar TestingModuleOverride;\n(function (TestingModuleOverride) {\n    TestingModuleOverride[TestingModuleOverride[\"DECLARATION\"] = 0] = \"DECLARATION\";\n    TestingModuleOverride[TestingModuleOverride[\"OVERRIDE_TEMPLATE\"] = 1] = \"OVERRIDE_TEMPLATE\";\n})(TestingModuleOverride || (TestingModuleOverride = {}));\nfunction isTestingModuleOverride(value) {\n    return (value === TestingModuleOverride.DECLARATION || value === TestingModuleOverride.OVERRIDE_TEMPLATE);\n}\nfunction assertNoStandaloneComponents(types, resolver, location) {\n    types.forEach((type) => {\n        if (!_getAsyncClassMetadataFn(type)) {\n            const component = resolver.resolve(type);\n            if (component && (component.standalone == null || component.standalone)) {\n                throw new Error(_generateStandaloneInDeclarationsError(type, location));\n            }\n        }\n    });\n}\nclass TestBedCompiler {\n    platform;\n    additionalModuleTypes;\n    originalComponentResolutionQueue = null;\n    // Testing module configuration\n    declarations = [];\n    imports = [];\n    providers = [];\n    schemas = [];\n    // Queues of components/directives/pipes that should be recompiled.\n    pendingComponents = new Set();\n    pendingDirectives = new Set();\n    pendingPipes = new Set();\n    // Set of components with async metadata, i.e. components with `@defer` blocks\n    // in their templates.\n    componentsWithAsyncMetadata = new Set();\n    // Keep track of all components and directives, so we can patch Providers onto defs later.\n    seenComponents = new Set();\n    seenDirectives = new Set();\n    // Keep track of overridden modules, so that we can collect all affected ones in the module tree.\n    overriddenModules = new Set();\n    // Store resolved styles for Components that have template overrides present and `styleUrls`\n    // defined at the same time.\n    existingComponentStyles = new Map();\n    resolvers = initResolvers();\n    // Map of component type to an NgModule that declares it.\n    //\n    // There are a couple special cases:\n    // - for standalone components, the module scope value is `null`\n    // - when a component is declared in `TestBed.configureTestingModule()` call or\n    //   a component's template is overridden via `TestBed.overrideTemplateUsingTestingModule()`.\n    //   we use a special value from the `TestingModuleOverride` enum.\n    componentToModuleScope = new Map();\n    // Map that keeps initial version of component/directive/pipe defs in case\n    // we compile a Type again, thus overriding respective static fields. This is\n    // required to make sure we restore defs to their initial states between test runs.\n    // Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of an\n    // NgModule), store all of them in a map.\n    initialNgDefs = new Map();\n    // Array that keeps cleanup operations for initial versions of component/directive/pipe/module\n    // defs in case TestBed makes changes to the originals.\n    defCleanupOps = [];\n    _injector = null;\n    compilerProviders = null;\n    providerOverrides = [];\n    rootProviderOverrides = [];\n    // Overrides for injectables with `{providedIn: SomeModule}` need to be tracked and added to that\n    // module's provider list.\n    providerOverridesByModule = new Map();\n    providerOverridesByToken = new Map();\n    scopesWithOverriddenProviders = new Set();\n    testModuleType;\n    testModuleRef = null;\n    deferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    rethrowApplicationTickErrors = RETHROW_APPLICATION_ERRORS_DEFAULT;\n    constructor(platform, additionalModuleTypes) {\n        this.platform = platform;\n        this.additionalModuleTypes = additionalModuleTypes;\n        class DynamicTestModule {\n        }\n        this.testModuleType = DynamicTestModule;\n    }\n    setCompilerProviders(providers) {\n        this.compilerProviders = providers;\n        this._injector = null;\n    }\n    configureTestingModule(moduleDef) {\n        // Enqueue any compilation tasks for the directly declared component.\n        if (moduleDef.declarations !== undefined) {\n            // Verify that there are no standalone components\n            assertNoStandaloneComponents(moduleDef.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n            this.queueTypeArray(moduleDef.declarations, TestingModuleOverride.DECLARATION);\n            this.declarations.push(...moduleDef.declarations);\n        }\n        // Enqueue any compilation tasks for imported modules.\n        if (moduleDef.imports !== undefined) {\n            this.queueTypesFromModulesArray(moduleDef.imports);\n            this.imports.push(...moduleDef.imports);\n        }\n        if (moduleDef.providers !== undefined) {\n            this.providers.push(...moduleDef.providers);\n        }\n        if (moduleDef.schemas !== undefined) {\n            this.schemas.push(...moduleDef.schemas);\n        }\n        this.deferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n        this.rethrowApplicationTickErrors =\n            moduleDef.rethrowApplicationErrors ?? RETHROW_APPLICATION_ERRORS_DEFAULT;\n    }\n    overrideModule(ngModule, override) {\n        if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n            _depsTracker.clearScopeCacheFor(ngModule);\n        }\n        this.overriddenModules.add(ngModule);\n        // Compile the module right away.\n        this.resolvers.module.addOverride(ngModule, override);\n        const metadata = this.resolvers.module.resolve(ngModule);\n        if (metadata === null) {\n            throw invalidTypeError(ngModule.name, 'NgModule');\n        }\n        this.recompileNgModule(ngModule, metadata);\n        // At this point, the module has a valid module def (ɵmod), but the override may have introduced\n        // new declarations or imported modules. Ingest any possible new types and add them to the\n        // current queue.\n        this.queueTypesFromModulesArray([ngModule]);\n    }\n    overrideComponent(component, override) {\n        this.verifyNoStandaloneFlagOverrides(component, override);\n        this.resolvers.component.addOverride(component, override);\n        this.pendingComponents.add(component);\n        // If this is a component with async metadata (i.e. a component with a `@defer` block\n        // in a template) - store it for future processing.\n        this.maybeRegisterComponentWithAsyncMetadata(component);\n    }\n    overrideDirective(directive, override) {\n        this.verifyNoStandaloneFlagOverrides(directive, override);\n        this.resolvers.directive.addOverride(directive, override);\n        this.pendingDirectives.add(directive);\n    }\n    overridePipe(pipe, override) {\n        this.verifyNoStandaloneFlagOverrides(pipe, override);\n        this.resolvers.pipe.addOverride(pipe, override);\n        this.pendingPipes.add(pipe);\n    }\n    verifyNoStandaloneFlagOverrides(type, override) {\n        if (override.add?.hasOwnProperty('standalone') ||\n            override.set?.hasOwnProperty('standalone') ||\n            override.remove?.hasOwnProperty('standalone')) {\n            throw new Error(`An override for the ${type.name} class has the \\`standalone\\` flag. ` +\n                `Changing the \\`standalone\\` flag via TestBed overrides is not supported.`);\n        }\n    }\n    overrideProvider(token, provider) {\n        let providerDef;\n        if (provider.useFactory !== undefined) {\n            providerDef = {\n                provide: token,\n                useFactory: provider.useFactory,\n                deps: provider.deps || [],\n                multi: provider.multi,\n            };\n        }\n        else if (provider.useValue !== undefined) {\n            providerDef = { provide: token, useValue: provider.useValue, multi: provider.multi };\n        }\n        else {\n            providerDef = { provide: token };\n        }\n        const injectableDef = typeof token !== 'string' ? _getInjectableDef(token) : null;\n        const providedIn = injectableDef === null ? null : resolveForwardRef(injectableDef.providedIn);\n        const overridesBucket = providedIn === 'root' ? this.rootProviderOverrides : this.providerOverrides;\n        overridesBucket.push(providerDef);\n        // Keep overrides grouped by token as well for fast lookups using token\n        this.providerOverridesByToken.set(token, providerDef);\n        if (injectableDef !== null && providedIn !== null && typeof providedIn !== 'string') {\n            const existingOverrides = this.providerOverridesByModule.get(providedIn);\n            if (existingOverrides !== undefined) {\n                existingOverrides.push(providerDef);\n            }\n            else {\n                this.providerOverridesByModule.set(providedIn, [providerDef]);\n            }\n        }\n    }\n    overrideTemplateUsingTestingModule(type, template) {\n        const def = type[_NG_COMP_DEF];\n        const hasStyleUrls = () => {\n            const metadata = this.resolvers.component.resolve(type);\n            return !!metadata.styleUrl || !!metadata.styleUrls?.length;\n        };\n        const overrideStyleUrls = !!def && !_isComponentDefPendingResolution(type) && hasStyleUrls();\n        // In Ivy, compiling a component does not require knowing the module providing the\n        // component's scope, so overrideTemplateUsingTestingModule can be implemented purely via\n        // overrideComponent. Important: overriding template requires full Component re-compilation,\n        // which may fail in case styleUrls are also present (thus Component is considered as required\n        // resolution). In order to avoid this, we preemptively set styleUrls to an empty array,\n        // preserve current styles available on Component def and restore styles back once compilation\n        // is complete.\n        const override = overrideStyleUrls\n            ? { template, styles: [], styleUrls: [], styleUrl: undefined }\n            : { template };\n        this.overrideComponent(type, { set: override });\n        if (overrideStyleUrls && def.styles && def.styles.length > 0) {\n            this.existingComponentStyles.set(type, def.styles);\n        }\n        // Set the component's scope to be the testing module.\n        this.componentToModuleScope.set(type, TestingModuleOverride.OVERRIDE_TEMPLATE);\n    }\n    async resolvePendingComponentsWithAsyncMetadata() {\n        if (this.componentsWithAsyncMetadata.size === 0)\n            return;\n        const promises = [];\n        for (const component of this.componentsWithAsyncMetadata) {\n            const asyncMetadataFn = _getAsyncClassMetadataFn(component);\n            if (asyncMetadataFn) {\n                promises.push(asyncMetadataFn());\n            }\n        }\n        this.componentsWithAsyncMetadata.clear();\n        const resolvedDeps = await Promise.all(promises);\n        const flatResolvedDeps = resolvedDeps.flat(2);\n        this.queueTypesFromModulesArray(flatResolvedDeps);\n        // Loaded standalone components might contain imports of NgModules\n        // with providers, make sure we override providers there too.\n        for (const component of flatResolvedDeps) {\n            this.applyProviderOverridesInScope(component);\n        }\n    }\n    async compileComponents() {\n        this.clearComponentResolutionQueue();\n        // Wait for all async metadata for components that were\n        // overridden, we need resolved metadata to perform an override\n        // and re-compile a component.\n        await this.resolvePendingComponentsWithAsyncMetadata();\n        // Verify that there were no standalone components present in the `declarations` field\n        // during the `TestBed.configureTestingModule` call. We perform this check here in addition\n        // to the logic in the `configureTestingModule` function, since at this point we have\n        // all async metadata resolved.\n        assertNoStandaloneComponents(this.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n        // Run compilers for all queued types.\n        let needsAsyncResources = this.compileTypesSync();\n        // compileComponents() should not be async unless it needs to be.\n        if (needsAsyncResources) {\n            let resourceLoader;\n            let resolver = (url) => {\n                if (!resourceLoader) {\n                    resourceLoader = this.injector.get(ResourceLoader);\n                }\n                return Promise.resolve(resourceLoader.get(url));\n            };\n            await _resolveComponentResources(resolver);\n        }\n    }\n    finalize() {\n        // One last compile\n        this.compileTypesSync();\n        // Create the testing module itself.\n        this.compileTestModule();\n        this.applyTransitiveScopes();\n        this.applyProviderOverrides();\n        // Patch previously stored `styles` Component values (taken from ɵcmp), in case these\n        // Components have `styleUrls` fields defined and template override was requested.\n        this.patchComponentsWithExistingStyles();\n        // Clear the componentToModuleScope map, so that future compilations don't reset the scope of\n        // every component.\n        this.componentToModuleScope.clear();\n        const parentInjector = this.platform.injector;\n        this.testModuleRef = new _Render3NgModuleRef(this.testModuleType, parentInjector, []);\n        // ApplicationInitStatus.runInitializers() is marked @internal to core.\n        // Cast it to any before accessing it.\n        this.testModuleRef.injector.get(ApplicationInitStatus).runInitializers();\n        // Set locale ID after running app initializers, since locale information might be updated while\n        // running initializers. This is also consistent with the execution order while bootstrapping an\n        // app (see `packages/core/src/application_ref.ts` file).\n        const localeId = this.testModuleRef.injector.get(LOCALE_ID, _DEFAULT_LOCALE_ID);\n        _setLocaleId(localeId);\n        return this.testModuleRef;\n    }\n    /**\n     * @internal\n     */\n    _compileNgModuleSync(moduleType) {\n        this.queueTypesFromModulesArray([moduleType]);\n        this.compileTypesSync();\n        this.applyProviderOverrides();\n        this.applyProviderOverridesInScope(moduleType);\n        this.applyTransitiveScopes();\n    }\n    /**\n     * @internal\n     */\n    async _compileNgModuleAsync(moduleType) {\n        this.queueTypesFromModulesArray([moduleType]);\n        await this.compileComponents();\n        this.applyProviderOverrides();\n        this.applyProviderOverridesInScope(moduleType);\n        this.applyTransitiveScopes();\n    }\n    /**\n     * @internal\n     */\n    _getModuleResolver() {\n        return this.resolvers.module;\n    }\n    /**\n     * @internal\n     */\n    _getComponentFactories(moduleType) {\n        return maybeUnwrapFn(moduleType.ɵmod.declarations).reduce((factories, declaration) => {\n            const componentDef = declaration.ɵcmp;\n            componentDef && factories.push(new _Render3ComponentFactory(componentDef, this.testModuleRef));\n            return factories;\n        }, []);\n    }\n    compileTypesSync() {\n        // Compile all queued components, directives, pipes.\n        let needsAsyncResources = false;\n        this.pendingComponents.forEach((declaration) => {\n            if (_getAsyncClassMetadataFn(declaration)) {\n                throw new Error(`Component '${declaration.name}' has unresolved metadata. ` +\n                    `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n            }\n            needsAsyncResources = needsAsyncResources || _isComponentDefPendingResolution(declaration);\n            const metadata = this.resolvers.component.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Component');\n            }\n            this.maybeStoreNgDef(_NG_COMP_DEF, declaration);\n            if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                _depsTracker.clearScopeCacheFor(declaration);\n            }\n            _compileComponent(declaration, metadata);\n        });\n        this.pendingComponents.clear();\n        this.pendingDirectives.forEach((declaration) => {\n            const metadata = this.resolvers.directive.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Directive');\n            }\n            this.maybeStoreNgDef(_NG_DIR_DEF, declaration);\n            _compileDirective(declaration, metadata);\n        });\n        this.pendingDirectives.clear();\n        this.pendingPipes.forEach((declaration) => {\n            const metadata = this.resolvers.pipe.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Pipe');\n            }\n            this.maybeStoreNgDef(_NG_PIPE_DEF, declaration);\n            _compilePipe(declaration, metadata);\n        });\n        this.pendingPipes.clear();\n        return needsAsyncResources;\n    }\n    applyTransitiveScopes() {\n        if (this.overriddenModules.size > 0) {\n            // Module overrides (via `TestBed.overrideModule`) might affect scopes that were previously\n            // calculated and stored in `transitiveCompileScopes`. If module overrides are present,\n            // collect all affected modules and reset scopes to force their re-calculation.\n            const testingModuleDef = this.testModuleType[_NG_MOD_DEF];\n            const affectedModules = this.collectModulesAffectedByOverrides(testingModuleDef.imports);\n            if (affectedModules.size > 0) {\n                affectedModules.forEach((moduleType) => {\n                    if (!_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                        this.storeFieldOfDefOnType(moduleType, _NG_MOD_DEF, 'transitiveCompileScopes');\n                        moduleType[_NG_MOD_DEF].transitiveCompileScopes = null;\n                    }\n                    else {\n                        _depsTracker.clearScopeCacheFor(moduleType);\n                    }\n                });\n            }\n        }\n        const moduleToScope = new Map();\n        const getScopeOfModule = (moduleType) => {\n            if (!moduleToScope.has(moduleType)) {\n                const isTestingModule = isTestingModuleOverride(moduleType);\n                const realType = isTestingModule ? this.testModuleType : moduleType;\n                moduleToScope.set(moduleType, _transitiveScopesFor(realType));\n            }\n            return moduleToScope.get(moduleType);\n        };\n        this.componentToModuleScope.forEach((moduleType, componentType) => {\n            if (moduleType !== null) {\n                const moduleScope = getScopeOfModule(moduleType);\n                this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'directiveDefs');\n                this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'pipeDefs');\n                _patchComponentDefWithScope(getComponentDef(componentType), moduleScope);\n            }\n            // `tView` that is stored on component def contains information about directives and pipes\n            // that are in the scope of this component. Patching component scope will cause `tView` to be\n            // changed. Store original `tView` before patching scope, so the `tView` (including scope\n            // information) is restored back to its previous/original state before running next test.\n            // Resetting `tView` is also needed for cases when we apply provider overrides and those\n            // providers are defined on component's level, in which case they may end up included into\n            // `tView.blueprint`.\n            this.storeFieldOfDefOnType(componentType, _NG_COMP_DEF, 'tView');\n        });\n        this.componentToModuleScope.clear();\n    }\n    applyProviderOverrides() {\n        const maybeApplyOverrides = (field) => (type) => {\n            const resolver = field === _NG_COMP_DEF ? this.resolvers.component : this.resolvers.directive;\n            const metadata = resolver.resolve(type);\n            if (this.hasProviderOverrides(metadata.providers)) {\n                this.patchDefWithProviderOverrides(type, field);\n            }\n        };\n        this.seenComponents.forEach(maybeApplyOverrides(_NG_COMP_DEF));\n        this.seenDirectives.forEach(maybeApplyOverrides(_NG_DIR_DEF));\n        this.seenComponents.clear();\n        this.seenDirectives.clear();\n    }\n    /**\n     * Applies provider overrides to a given type (either an NgModule or a standalone component)\n     * and all imported NgModules and standalone components recursively.\n     */\n    applyProviderOverridesInScope(type) {\n        const hasScope = isStandaloneComponent(type) || isNgModule(type);\n        // The function can be re-entered recursively while inspecting dependencies\n        // of an NgModule or a standalone component. Exit early if we come across a\n        // type that can not have a scope (directive or pipe) or the type is already\n        // processed earlier.\n        if (!hasScope || this.scopesWithOverriddenProviders.has(type)) {\n            return;\n        }\n        this.scopesWithOverriddenProviders.add(type);\n        // NOTE: the line below triggers JIT compilation of the module injector,\n        // which also invokes verification of the NgModule semantics, which produces\n        // detailed error messages. The fact that the code relies on this line being\n        // present here is suspicious and should be refactored in a way that the line\n        // below can be moved (for ex. after an early exit check below).\n        const injectorDef = type[_NG_INJ_DEF];\n        // No provider overrides, exit early.\n        if (this.providerOverridesByToken.size === 0)\n            return;\n        if (isStandaloneComponent(type)) {\n            // Visit all component dependencies and override providers there.\n            const def = getComponentDef(type);\n            const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n            for (const dependency of dependencies) {\n                this.applyProviderOverridesInScope(dependency);\n            }\n        }\n        else {\n            const providers = [\n                ...injectorDef.providers,\n                ...(this.providerOverridesByModule.get(type) || []),\n            ];\n            if (this.hasProviderOverrides(providers)) {\n                this.maybeStoreNgDef(_NG_INJ_DEF, type);\n                this.storeFieldOfDefOnType(type, _NG_INJ_DEF, 'providers');\n                injectorDef.providers = this.getOverriddenProviders(providers);\n            }\n            // Apply provider overrides to imported modules recursively\n            const moduleDef = type[_NG_MOD_DEF];\n            const imports = maybeUnwrapFn(moduleDef.imports);\n            for (const importedModule of imports) {\n                this.applyProviderOverridesInScope(importedModule);\n            }\n            // Also override the providers on any ModuleWithProviders imports since those don't appear in\n            // the moduleDef.\n            for (const importedModule of flatten(injectorDef.imports)) {\n                if (isModuleWithProviders(importedModule)) {\n                    this.defCleanupOps.push({\n                        object: importedModule,\n                        fieldName: 'providers',\n                        originalValue: importedModule.providers,\n                    });\n                    importedModule.providers = this.getOverriddenProviders(importedModule.providers);\n                }\n            }\n        }\n    }\n    patchComponentsWithExistingStyles() {\n        this.existingComponentStyles.forEach((styles, type) => (type[_NG_COMP_DEF].styles = styles));\n        this.existingComponentStyles.clear();\n    }\n    queueTypeArray(arr, moduleType) {\n        for (const value of arr) {\n            if (Array.isArray(value)) {\n                this.queueTypeArray(value, moduleType);\n            }\n            else {\n                this.queueType(value, moduleType);\n            }\n        }\n    }\n    recompileNgModule(ngModule, metadata) {\n        // Cache the initial ngModuleDef as it will be overwritten.\n        this.maybeStoreNgDef(_NG_MOD_DEF, ngModule);\n        this.maybeStoreNgDef(_NG_INJ_DEF, ngModule);\n        _compileNgModuleDefs(ngModule, metadata);\n    }\n    maybeRegisterComponentWithAsyncMetadata(type) {\n        const asyncMetadataFn = _getAsyncClassMetadataFn(type);\n        if (asyncMetadataFn) {\n            this.componentsWithAsyncMetadata.add(type);\n        }\n    }\n    queueType(type, moduleType) {\n        // If this is a component with async metadata (i.e. a component with a `@defer` block\n        // in a template) - store it for future processing.\n        this.maybeRegisterComponentWithAsyncMetadata(type);\n        const component = this.resolvers.component.resolve(type);\n        if (component) {\n            // Check whether a give Type has respective NG def (ɵcmp) and compile if def is\n            // missing. That might happen in case a class without any Angular decorators extends another\n            // class where Component/Directive/Pipe decorator is defined.\n            if (_isComponentDefPendingResolution(type) || !type.hasOwnProperty(_NG_COMP_DEF)) {\n                this.pendingComponents.add(type);\n            }\n            this.seenComponents.add(type);\n            // Keep track of the module which declares this component, so later the component's scope\n            // can be set correctly. If the component has already been recorded here, then one of several\n            // cases is true:\n            // * the module containing the component was imported multiple times (common).\n            // * the component is declared in multiple modules (which is an error).\n            // * the component was in 'declarations' of the testing module, and also in an imported module\n            //   in which case the module scope will be TestingModuleOverride.DECLARATION.\n            // * overrideTemplateUsingTestingModule was called for the component in which case the module\n            //   scope will be TestingModuleOverride.OVERRIDE_TEMPLATE.\n            //\n            // If the component was previously in the testing module's 'declarations' (meaning the\n            // current value is TestingModuleOverride.DECLARATION), then `moduleType` is the component's\n            // real module, which was imported. This pattern is understood to mean that the component\n            // should use its original scope, but that the testing module should also contain the\n            // component in its scope.\n            if (!this.componentToModuleScope.has(type) ||\n                this.componentToModuleScope.get(type) === TestingModuleOverride.DECLARATION) {\n                this.componentToModuleScope.set(type, moduleType);\n            }\n            return;\n        }\n        const directive = this.resolvers.directive.resolve(type);\n        if (directive) {\n            if (!type.hasOwnProperty(_NG_DIR_DEF)) {\n                this.pendingDirectives.add(type);\n            }\n            this.seenDirectives.add(type);\n            return;\n        }\n        const pipe = this.resolvers.pipe.resolve(type);\n        if (pipe && !type.hasOwnProperty(_NG_PIPE_DEF)) {\n            this.pendingPipes.add(type);\n            return;\n        }\n    }\n    queueTypesFromModulesArray(arr) {\n        // Because we may encounter the same NgModule or a standalone Component while processing\n        // the dependencies of an NgModule or a standalone Component, we cache them in this set so we\n        // can skip ones that have already been seen encountered. In some test setups, this caching\n        // resulted in 10X runtime improvement.\n        const processedDefs = new Set();\n        const queueTypesFromModulesArrayRecur = (arr) => {\n            for (const value of arr) {\n                if (Array.isArray(value)) {\n                    queueTypesFromModulesArrayRecur(value);\n                }\n                else if (hasNgModuleDef(value)) {\n                    const def = value.ɵmod;\n                    if (processedDefs.has(def)) {\n                        continue;\n                    }\n                    processedDefs.add(def);\n                    // Look through declarations, imports, and exports, and queue\n                    // everything found there.\n                    this.queueTypeArray(maybeUnwrapFn(def.declarations), value);\n                    queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.imports));\n                    queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.exports));\n                }\n                else if (isModuleWithProviders(value)) {\n                    queueTypesFromModulesArrayRecur([value.ngModule]);\n                }\n                else if (isStandaloneComponent(value)) {\n                    this.queueType(value, null);\n                    const def = getComponentDef(value);\n                    if (processedDefs.has(def)) {\n                        continue;\n                    }\n                    processedDefs.add(def);\n                    const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n                    dependencies.forEach((dependency) => {\n                        // Note: in AOT, the `dependencies` might also contain regular\n                        // (NgModule-based) Component, Directive and Pipes, so we handle\n                        // them separately and proceed with recursive process for standalone\n                        // Components and NgModules only.\n                        if (isStandaloneComponent(dependency) || hasNgModuleDef(dependency)) {\n                            queueTypesFromModulesArrayRecur([dependency]);\n                        }\n                        else {\n                            this.queueType(dependency, null);\n                        }\n                    });\n                }\n            }\n        };\n        queueTypesFromModulesArrayRecur(arr);\n    }\n    // When module overrides (via `TestBed.overrideModule`) are present, it might affect all modules\n    // that import (even transitively) an overridden one. For all affected modules we need to\n    // recalculate their scopes for a given test run and restore original scopes at the end. The goal\n    // of this function is to collect all affected modules in a set for further processing. Example:\n    // if we have the following module hierarchy: A -> B -> C (where `->` means `imports`) and module\n    // `C` is overridden, we consider `A` and `B` as affected, since their scopes might become\n    // invalidated with the override.\n    collectModulesAffectedByOverrides(arr) {\n        const seenModules = new Set();\n        const affectedModules = new Set();\n        const calcAffectedModulesRecur = (arr, path) => {\n            for (const value of arr) {\n                if (Array.isArray(value)) {\n                    // If the value is an array, just flatten it (by invoking this function recursively),\n                    // keeping \"path\" the same.\n                    calcAffectedModulesRecur(value, path);\n                }\n                else if (hasNgModuleDef(value)) {\n                    if (seenModules.has(value)) {\n                        // If we've seen this module before and it's included into \"affected modules\" list, mark\n                        // the whole path that leads to that module as affected, but do not descend into its\n                        // imports, since we already examined them before.\n                        if (affectedModules.has(value)) {\n                            path.forEach((item) => affectedModules.add(item));\n                        }\n                        continue;\n                    }\n                    seenModules.add(value);\n                    if (this.overriddenModules.has(value)) {\n                        path.forEach((item) => affectedModules.add(item));\n                    }\n                    // Examine module imports recursively to look for overridden modules.\n                    const moduleDef = value[_NG_MOD_DEF];\n                    calcAffectedModulesRecur(maybeUnwrapFn(moduleDef.imports), path.concat(value));\n                }\n            }\n        };\n        calcAffectedModulesRecur(arr, []);\n        return affectedModules;\n    }\n    /**\n     * Preserve an original def (such as ɵmod, ɵinj, etc) before applying an override.\n     * Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of\n     * an NgModule). If there is a def in a set already, don't override it, since\n     * an original one should be restored at the end of a test.\n     */\n    maybeStoreNgDef(prop, type) {\n        if (!this.initialNgDefs.has(type)) {\n            this.initialNgDefs.set(type, new Map());\n        }\n        const currentDefs = this.initialNgDefs.get(type);\n        if (!currentDefs.has(prop)) {\n            const currentDef = Object.getOwnPropertyDescriptor(type, prop);\n            currentDefs.set(prop, currentDef);\n        }\n    }\n    storeFieldOfDefOnType(type, defField, fieldName) {\n        const def = type[defField];\n        const originalValue = def[fieldName];\n        this.defCleanupOps.push({ object: def, fieldName, originalValue });\n    }\n    /**\n     * Clears current components resolution queue, but stores the state of the queue, so we can\n     * restore it later. Clearing the queue is required before we try to compile components (via\n     * `TestBed.compileComponents`), so that component defs are in sync with the resolution queue.\n     */\n    clearComponentResolutionQueue() {\n        if (this.originalComponentResolutionQueue === null) {\n            this.originalComponentResolutionQueue = new Map();\n        }\n        _clearResolutionOfComponentResourcesQueue().forEach((value, key) => this.originalComponentResolutionQueue.set(key, value));\n    }\n    /*\n     * Restores component resolution queue to the previously saved state. This operation is performed\n     * as a part of restoring the state after completion of the current set of tests (that might\n     * potentially mutate the state).\n     */\n    restoreComponentResolutionQueue() {\n        if (this.originalComponentResolutionQueue !== null) {\n            _restoreComponentResolutionQueue(this.originalComponentResolutionQueue);\n            this.originalComponentResolutionQueue = null;\n        }\n    }\n    restoreOriginalState() {\n        // Process cleanup ops in reverse order so the field's original value is restored correctly (in\n        // case there were multiple overrides for the same field).\n        forEachRight(this.defCleanupOps, (op) => {\n            op.object[op.fieldName] = op.originalValue;\n        });\n        // Restore initial component/directive/pipe defs\n        this.initialNgDefs.forEach((defs, type) => {\n            if (_USE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                _depsTracker.clearScopeCacheFor(type);\n            }\n            defs.forEach((descriptor, prop) => {\n                if (!descriptor) {\n                    // Delete operations are generally undesirable since they have performance\n                    // implications on objects they were applied to. In this particular case, situations\n                    // where this code is invoked should be quite rare to cause any noticeable impact,\n                    // since it's applied only to some test cases (for example when class with no\n                    // annotations extends some @Component) when we need to clear 'ɵcmp' field on a given\n                    // class to restore its original state (before applying overrides and running tests).\n                    delete type[prop];\n                }\n                else {\n                    Object.defineProperty(type, prop, descriptor);\n                }\n            });\n        });\n        this.initialNgDefs.clear();\n        this.scopesWithOverriddenProviders.clear();\n        this.restoreComponentResolutionQueue();\n        // Restore the locale ID to the default value, this shouldn't be necessary but we never know\n        _setLocaleId(_DEFAULT_LOCALE_ID);\n    }\n    compileTestModule() {\n        class RootScopeModule {\n        }\n        _compileNgModuleDefs(RootScopeModule, {\n            providers: [\n                ...this.rootProviderOverrides,\n                _internalProvideZoneChangeDetection({}),\n                TestBedApplicationErrorHandler,\n                { provide: _ChangeDetectionScheduler, useExisting: _ChangeDetectionSchedulerImpl },\n            ],\n        });\n        const providers = [\n            { provide: Compiler, useFactory: () => new R3TestCompiler(this) },\n            { provide: _DEFER_BLOCK_CONFIG, useValue: { behavior: this.deferBlockBehavior } },\n            {\n                provide: _INTERNAL_APPLICATION_ERROR_HANDLER,\n                useFactory: () => {\n                    if (this.rethrowApplicationTickErrors) {\n                        const handler = inject$1(TestBedApplicationErrorHandler);\n                        return (e) => {\n                            handler.handleError(e);\n                        };\n                    }\n                    else {\n                        const userErrorHandler = inject$1(ErrorHandler);\n                        const ngZone = inject$1(NgZone);\n                        return (e) => ngZone.runOutsideAngular(() => userErrorHandler.handleError(e));\n                    }\n                },\n            },\n            ...this.providers,\n            ...this.providerOverrides,\n        ];\n        const imports = [RootScopeModule, this.additionalModuleTypes, this.imports || []];\n        _compileNgModuleDefs(this.testModuleType, {\n            declarations: this.declarations,\n            imports,\n            schemas: this.schemas,\n            providers,\n        }, \n        /* allowDuplicateDeclarationsInRoot */ true);\n        this.applyProviderOverridesInScope(this.testModuleType);\n    }\n    get injector() {\n        if (this._injector !== null) {\n            return this._injector;\n        }\n        const providers = [];\n        const compilerOptions = this.platform.injector.get(COMPILER_OPTIONS, []);\n        compilerOptions.forEach((opts) => {\n            if (opts.providers) {\n                providers.push(opts.providers);\n            }\n        });\n        if (this.compilerProviders !== null) {\n            providers.push(...this.compilerProviders);\n        }\n        this._injector = Injector.create({ providers, parent: this.platform.injector });\n        return this._injector;\n    }\n    // get overrides for a specific provider (if any)\n    getSingleProviderOverrides(provider) {\n        const token = getProviderToken(provider);\n        return this.providerOverridesByToken.get(token) || null;\n    }\n    getProviderOverrides(providers) {\n        if (!providers || !providers.length || this.providerOverridesByToken.size === 0)\n            return [];\n        // There are two flattening operations here. The inner flattenProviders() operates on the\n        // metadata's providers and applies a mapping function which retrieves overrides for each\n        // incoming provider. The outer flatten() then flattens the produced overrides array. If this is\n        // not done, the array can contain other empty arrays (e.g. `[[], []]`) which leak into the\n        // providers array and contaminate any error messages that might be generated.\n        return flatten(flattenProviders(providers, (provider) => this.getSingleProviderOverrides(provider) || []));\n    }\n    getOverriddenProviders(providers) {\n        if (!providers || !providers.length || this.providerOverridesByToken.size === 0)\n            return [];\n        const flattenedProviders = flattenProviders(providers);\n        const overrides = this.getProviderOverrides(flattenedProviders);\n        const overriddenProviders = [...flattenedProviders, ...overrides];\n        const final = [];\n        const seenOverriddenProviders = new Set();\n        // We iterate through the list of providers in reverse order to make sure provider overrides\n        // take precedence over the values defined in provider list. We also filter out all providers\n        // that have overrides, keeping overridden values only. This is needed, since presence of a\n        // provider with `ngOnDestroy` hook will cause this hook to be registered and invoked later.\n        forEachRight(overriddenProviders, (provider) => {\n            const token = getProviderToken(provider);\n            if (this.providerOverridesByToken.has(token)) {\n                if (!seenOverriddenProviders.has(token)) {\n                    seenOverriddenProviders.add(token);\n                    // Treat all overridden providers as `{multi: false}` (even if it's a multi-provider) to\n                    // make sure that provided override takes highest precedence and is not combined with\n                    // other instances of the same multi provider.\n                    final.unshift({ ...provider, multi: false });\n                }\n            }\n            else {\n                final.unshift(provider);\n            }\n        });\n        return final;\n    }\n    hasProviderOverrides(providers) {\n        return this.getProviderOverrides(providers).length > 0;\n    }\n    patchDefWithProviderOverrides(declaration, field) {\n        const def = declaration[field];\n        if (def && def.providersResolver) {\n            this.maybeStoreNgDef(field, declaration);\n            const resolver = def.providersResolver;\n            const processProvidersFn = (providers) => this.getOverriddenProviders(providers);\n            this.storeFieldOfDefOnType(declaration, field, 'providersResolver');\n            def.providersResolver = (ngDef) => resolver(ngDef, processProvidersFn);\n        }\n    }\n}\nfunction initResolvers() {\n    return {\n        module: new NgModuleResolver(),\n        component: new ComponentResolver(),\n        directive: new DirectiveResolver(),\n        pipe: new PipeResolver(),\n    };\n}\nfunction isStandaloneComponent(value) {\n    const def = getComponentDef(value);\n    return !!def?.standalone;\n}\nfunction getComponentDef(value) {\n    return value.ɵcmp ?? null;\n}\nfunction hasNgModuleDef(value) {\n    return value.hasOwnProperty('ɵmod');\n}\nfunction isNgModule(value) {\n    return hasNgModuleDef(value);\n}\nfunction maybeUnwrapFn(maybeFn) {\n    return maybeFn instanceof Function ? maybeFn() : maybeFn;\n}\nfunction flatten(values) {\n    const out = [];\n    values.forEach((value) => {\n        if (Array.isArray(value)) {\n            out.push(...flatten(value));\n        }\n        else {\n            out.push(value);\n        }\n    });\n    return out;\n}\nfunction identityFn(value) {\n    return value;\n}\nfunction flattenProviders(providers, mapFn = identityFn) {\n    const out = [];\n    for (let provider of providers) {\n        if (_isEnvironmentProviders(provider)) {\n            provider = provider.ɵproviders;\n        }\n        if (Array.isArray(provider)) {\n            out.push(...flattenProviders(provider, mapFn));\n        }\n        else {\n            out.push(mapFn(provider));\n        }\n    }\n    return out;\n}\nfunction getProviderField(provider, field) {\n    return provider && typeof provider === 'object' && provider[field];\n}\nfunction getProviderToken(provider) {\n    return getProviderField(provider, 'provide') || provider;\n}\nfunction isModuleWithProviders(value) {\n    return value.hasOwnProperty('ngModule');\n}\nfunction forEachRight(values, fn) {\n    for (let idx = values.length - 1; idx >= 0; idx--) {\n        fn(values[idx], idx);\n    }\n}\nfunction invalidTypeError(name, expectedType) {\n    return new Error(`${name} class doesn't have @${expectedType} decorator or is missing metadata.`);\n}\nclass R3TestCompiler {\n    testBed;\n    constructor(testBed) {\n        this.testBed = testBed;\n    }\n    compileModuleSync(moduleType) {\n        this.testBed._compileNgModuleSync(moduleType);\n        return new _NgModuleFactory(moduleType);\n    }\n    async compileModuleAsync(moduleType) {\n        await this.testBed._compileNgModuleAsync(moduleType);\n        return new _NgModuleFactory(moduleType);\n    }\n    compileModuleAndAllComponentsSync(moduleType) {\n        const ngModuleFactory = this.compileModuleSync(moduleType);\n        const componentFactories = this.testBed._getComponentFactories(moduleType);\n        return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    }\n    async compileModuleAndAllComponentsAsync(moduleType) {\n        const ngModuleFactory = await this.compileModuleAsync(moduleType);\n        const componentFactories = this.testBed._getComponentFactories(moduleType);\n        return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    }\n    clearCache() { }\n    clearCacheFor(type) { }\n    getModuleId(moduleType) {\n        const meta = this.testBed._getModuleResolver().resolve(moduleType);\n        return (meta && meta.id) || undefined;\n    }\n}\n\n// The formatter and CI disagree on how this import statement should be formatted. Both try to keep\n// it on one line, too, which has gotten very hard to read & manage. So disable the formatter for\n// this statement only.\nlet _nextRootElementId = 0;\n/**\n * Returns a singleton of the `TestBed` class.\n *\n * @publicApi\n */\nfunction getTestBed() {\n    return TestBedImpl.INSTANCE;\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n */\nclass TestBedImpl {\n    static _INSTANCE = null;\n    static get INSTANCE() {\n        return (TestBedImpl._INSTANCE = TestBedImpl._INSTANCE || new TestBedImpl());\n    }\n    /**\n     * Teardown options that have been configured at the environment level.\n     * Used as a fallback if no instance-level options have been provided.\n     */\n    static _environmentTeardownOptions;\n    /**\n     * \"Error on unknown elements\" option that has been configured at the environment level.\n     * Used as a fallback if no instance-level option has been provided.\n     */\n    static _environmentErrorOnUnknownElementsOption;\n    /**\n     * \"Error on unknown properties\" option that has been configured at the environment level.\n     * Used as a fallback if no instance-level option has been provided.\n     */\n    static _environmentErrorOnUnknownPropertiesOption;\n    /**\n     * Teardown options that have been configured at the `TestBed` instance level.\n     * These options take precedence over the environment-level ones.\n     */\n    _instanceTeardownOptions;\n    /**\n     * Defer block behavior option that specifies whether defer blocks will be triggered manually\n     * or set to play through.\n     */\n    _instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    /**\n     * \"Error on unknown elements\" option that has been configured at the `TestBed` instance level.\n     * This option takes precedence over the environment-level one.\n     */\n    _instanceErrorOnUnknownElementsOption;\n    /**\n     * \"Error on unknown properties\" option that has been configured at the `TestBed` instance level.\n     * This option takes precedence over the environment-level one.\n     */\n    _instanceErrorOnUnknownPropertiesOption;\n    /**\n     * Stores the previous \"Error on unknown elements\" option value,\n     * allowing to restore it in the reset testing module logic.\n     */\n    _previousErrorOnUnknownElementsOption;\n    /**\n     * Stores the previous \"Error on unknown properties\" option value,\n     * allowing to restore it in the reset testing module logic.\n     */\n    _previousErrorOnUnknownPropertiesOption;\n    /**\n     * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n     * angular module. These are common to every test in the suite.\n     *\n     * This may only be called once, to set up the common providers for the current test\n     * suite on the current platform. If you absolutely need to change the providers,\n     * first use `resetTestEnvironment`.\n     *\n     * Test modules and platforms for individual platforms are available from\n     * '@angular/<platform_name>/testing'.\n     *\n     * @publicApi\n     */\n    static initTestEnvironment(ngModule, platform, options) {\n        const testBed = TestBedImpl.INSTANCE;\n        testBed.initTestEnvironment(ngModule, platform, options);\n        return testBed;\n    }\n    /**\n     * Reset the providers for the test injector.\n     *\n     * @publicApi\n     */\n    static resetTestEnvironment() {\n        TestBedImpl.INSTANCE.resetTestEnvironment();\n    }\n    static configureCompiler(config) {\n        return TestBedImpl.INSTANCE.configureCompiler(config);\n    }\n    /**\n     * Allows overriding default providers, directives, pipes, modules of the test injector,\n     * which are defined in test_injector.js\n     */\n    static configureTestingModule(moduleDef) {\n        return TestBedImpl.INSTANCE.configureTestingModule(moduleDef);\n    }\n    /**\n     * Compile components with a `templateUrl` for the test's NgModule.\n     * It is necessary to call this function\n     * as fetching urls is asynchronous.\n     */\n    static compileComponents() {\n        return TestBedImpl.INSTANCE.compileComponents();\n    }\n    static overrideModule(ngModule, override) {\n        return TestBedImpl.INSTANCE.overrideModule(ngModule, override);\n    }\n    static overrideComponent(component, override) {\n        return TestBedImpl.INSTANCE.overrideComponent(component, override);\n    }\n    static overrideDirective(directive, override) {\n        return TestBedImpl.INSTANCE.overrideDirective(directive, override);\n    }\n    static overridePipe(pipe, override) {\n        return TestBedImpl.INSTANCE.overridePipe(pipe, override);\n    }\n    static overrideTemplate(component, template) {\n        return TestBedImpl.INSTANCE.overrideTemplate(component, template);\n    }\n    /**\n     * Overrides the template of the given component, compiling the template\n     * in the context of the TestingModule.\n     *\n     * Note: This works for JIT and AOTed components as well.\n     */\n    static overrideTemplateUsingTestingModule(component, template) {\n        return TestBedImpl.INSTANCE.overrideTemplateUsingTestingModule(component, template);\n    }\n    static overrideProvider(token, provider) {\n        return TestBedImpl.INSTANCE.overrideProvider(token, provider);\n    }\n    static inject(token, notFoundValue, flags) {\n        return TestBedImpl.INSTANCE.inject(token, notFoundValue, _convertToBitFlags(flags));\n    }\n    /** @deprecated from v9.0.0 use TestBed.inject */\n    static get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n        return TestBedImpl.INSTANCE.inject(token, notFoundValue, flags);\n    }\n    /**\n     * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n     *\n     * @see {@link EnvironmentInjector#runInContext}\n     */\n    static runInInjectionContext(fn) {\n        return TestBedImpl.INSTANCE.runInInjectionContext(fn);\n    }\n    static createComponent(component) {\n        return TestBedImpl.INSTANCE.createComponent(component);\n    }\n    static resetTestingModule() {\n        return TestBedImpl.INSTANCE.resetTestingModule();\n    }\n    static execute(tokens, fn, context) {\n        return TestBedImpl.INSTANCE.execute(tokens, fn, context);\n    }\n    static get platform() {\n        return TestBedImpl.INSTANCE.platform;\n    }\n    static get ngModule() {\n        return TestBedImpl.INSTANCE.ngModule;\n    }\n    static flushEffects() {\n        return TestBedImpl.INSTANCE.flushEffects();\n    }\n    // Properties\n    platform = null;\n    ngModule = null;\n    _compiler = null;\n    _testModuleRef = null;\n    _activeFixtures = [];\n    /**\n     * Internal-only flag to indicate whether a module\n     * scoping queue has been checked and flushed already.\n     * @docs-private\n     */\n    globalCompilationChecked = false;\n    /**\n     * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n     * angular module. These are common to every test in the suite.\n     *\n     * This may only be called once, to set up the common providers for the current test\n     * suite on the current platform. If you absolutely need to change the providers,\n     * first use `resetTestEnvironment`.\n     *\n     * Test modules and platforms for individual platforms are available from\n     * '@angular/<platform_name>/testing'.\n     *\n     * @publicApi\n     */\n    initTestEnvironment(ngModule, platform, options) {\n        if (this.platform || this.ngModule) {\n            throw new Error('Cannot set base providers because it has already been called');\n        }\n        TestBedImpl._environmentTeardownOptions = options?.teardown;\n        TestBedImpl._environmentErrorOnUnknownElementsOption = options?.errorOnUnknownElements;\n        TestBedImpl._environmentErrorOnUnknownPropertiesOption = options?.errorOnUnknownProperties;\n        this.platform = platform;\n        this.ngModule = ngModule;\n        this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n        // TestBed does not have an API which can reliably detect the start of a test, and thus could be\n        // used to track the state of the NgModule registry and reset it correctly. Instead, when we\n        // know we're in a testing scenario, we disable the check for duplicate NgModule registration\n        // completely.\n        _setAllowDuplicateNgModuleIdsForTest(true);\n    }\n    /**\n     * Reset the providers for the test injector.\n     *\n     * @publicApi\n     */\n    resetTestEnvironment() {\n        this.resetTestingModule();\n        this._compiler = null;\n        this.platform = null;\n        this.ngModule = null;\n        TestBedImpl._environmentTeardownOptions = undefined;\n        _setAllowDuplicateNgModuleIdsForTest(false);\n    }\n    resetTestingModule() {\n        this.checkGlobalCompilationFinished();\n        _resetCompiledComponents();\n        if (this._compiler !== null) {\n            this.compiler.restoreOriginalState();\n        }\n        this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n        // Restore the previous value of the \"error on unknown elements\" option\n        _setUnknownElementStrictMode(this._previousErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n        // Restore the previous value of the \"error on unknown properties\" option\n        _setUnknownPropertyStrictMode(this._previousErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n        // We have to chain a couple of try/finally blocks, because each step can\n        // throw errors and we don't want it to interrupt the next step and we also\n        // want an error to be thrown at the end.\n        try {\n            this.destroyActiveFixtures();\n        }\n        finally {\n            try {\n                if (this.shouldTearDownTestingModule()) {\n                    this.tearDownTestingModule();\n                }\n            }\n            finally {\n                this._testModuleRef = null;\n                this._instanceTeardownOptions = undefined;\n                this._instanceErrorOnUnknownElementsOption = undefined;\n                this._instanceErrorOnUnknownPropertiesOption = undefined;\n                this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n            }\n        }\n        return this;\n    }\n    configureCompiler(config) {\n        if (config.useJit != null) {\n            throw new Error('JIT compiler is not configurable via TestBed APIs.');\n        }\n        if (config.providers !== undefined) {\n            this.compiler.setCompilerProviders(config.providers);\n        }\n        return this;\n    }\n    configureTestingModule(moduleDef) {\n        this.assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n        // Trigger module scoping queue flush before executing other TestBed operations in a test.\n        // This is needed for the first test invocation to ensure that globally declared modules have\n        // their components scoped properly. See the `checkGlobalCompilationFinished` function\n        // description for additional info.\n        this.checkGlobalCompilationFinished();\n        // Always re-assign the options, even if they're undefined.\n        // This ensures that we don't carry them between tests.\n        this._instanceTeardownOptions = moduleDef.teardown;\n        this._instanceErrorOnUnknownElementsOption = moduleDef.errorOnUnknownElements;\n        this._instanceErrorOnUnknownPropertiesOption = moduleDef.errorOnUnknownProperties;\n        this._instanceDeferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n        // Store the current value of the strict mode option,\n        // so we can restore it later\n        this._previousErrorOnUnknownElementsOption = _getUnknownElementStrictMode();\n        _setUnknownElementStrictMode(this.shouldThrowErrorOnUnknownElements());\n        this._previousErrorOnUnknownPropertiesOption = _getUnknownPropertyStrictMode();\n        _setUnknownPropertyStrictMode(this.shouldThrowErrorOnUnknownProperties());\n        this.compiler.configureTestingModule(moduleDef);\n        return this;\n    }\n    compileComponents() {\n        return this.compiler.compileComponents();\n    }\n    inject(token, notFoundValue, flags) {\n        if (token === TestBed) {\n            return this;\n        }\n        const UNDEFINED = {};\n        const result = this.testModuleRef.injector.get(token, UNDEFINED, _convertToBitFlags(flags));\n        return result === UNDEFINED\n            ? this.compiler.injector.get(token, notFoundValue, flags)\n            : result;\n    }\n    /** @deprecated from v9.0.0 use TestBed.inject */\n    get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n        return this.inject(token, notFoundValue, flags);\n    }\n    runInInjectionContext(fn) {\n        return runInInjectionContext(this.inject(EnvironmentInjector), fn);\n    }\n    execute(tokens, fn, context) {\n        const params = tokens.map((t) => this.inject(t));\n        return fn.apply(context, params);\n    }\n    overrideModule(ngModule, override) {\n        this.assertNotInstantiated('overrideModule', 'override module metadata');\n        this.compiler.overrideModule(ngModule, override);\n        return this;\n    }\n    overrideComponent(component, override) {\n        this.assertNotInstantiated('overrideComponent', 'override component metadata');\n        this.compiler.overrideComponent(component, override);\n        return this;\n    }\n    overrideTemplateUsingTestingModule(component, template) {\n        this.assertNotInstantiated('TestBed.overrideTemplateUsingTestingModule', 'Cannot override template when the test module has already been instantiated');\n        this.compiler.overrideTemplateUsingTestingModule(component, template);\n        return this;\n    }\n    overrideDirective(directive, override) {\n        this.assertNotInstantiated('overrideDirective', 'override directive metadata');\n        this.compiler.overrideDirective(directive, override);\n        return this;\n    }\n    overridePipe(pipe, override) {\n        this.assertNotInstantiated('overridePipe', 'override pipe metadata');\n        this.compiler.overridePipe(pipe, override);\n        return this;\n    }\n    /**\n     * Overwrites all providers for the given token with the given provider definition.\n     */\n    overrideProvider(token, provider) {\n        this.assertNotInstantiated('overrideProvider', 'override provider');\n        this.compiler.overrideProvider(token, provider);\n        return this;\n    }\n    overrideTemplate(component, template) {\n        return this.overrideComponent(component, { set: { template, templateUrl: null } });\n    }\n    createComponent(type) {\n        const testComponentRenderer = this.inject(TestComponentRenderer);\n        const rootElId = `root${_nextRootElementId++}`;\n        testComponentRenderer.insertRootElement(rootElId);\n        if (_getAsyncClassMetadataFn(type)) {\n            throw new Error(`Component '${type.name}' has unresolved metadata. ` +\n                `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n        }\n        const componentDef = type.ɵcmp;\n        if (!componentDef) {\n            throw new Error(`It looks like '${_stringify(type)}' has not been compiled.`);\n        }\n        const componentFactory = new _Render3ComponentFactory(componentDef);\n        const initComponent = () => {\n            const componentRef = componentFactory.create(Injector.NULL, [], `#${rootElId}`, this.testModuleRef);\n            return this.runInInjectionContext(() => new ComponentFixture(componentRef));\n        };\n        const noNgZone = this.inject(ComponentFixtureNoNgZone, false);\n        const ngZone = noNgZone ? null : this.inject(NgZone, null);\n        const fixture = ngZone ? ngZone.run(initComponent) : initComponent();\n        this._activeFixtures.push(fixture);\n        return fixture;\n    }\n    /**\n     * @internal strip this from published d.ts files due to\n     * https://github.com/microsoft/TypeScript/issues/36216\n     */\n    get compiler() {\n        if (this._compiler === null) {\n            throw new Error(`Need to call TestBed.initTestEnvironment() first`);\n        }\n        return this._compiler;\n    }\n    /**\n     * @internal strip this from published d.ts files due to\n     * https://github.com/microsoft/TypeScript/issues/36216\n     */\n    get testModuleRef() {\n        if (this._testModuleRef === null) {\n            this._testModuleRef = this.compiler.finalize();\n        }\n        return this._testModuleRef;\n    }\n    assertNotInstantiated(methodName, methodDescription) {\n        if (this._testModuleRef !== null) {\n            throw new Error(`Cannot ${methodDescription} when the test module has already been instantiated. ` +\n                `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n        }\n    }\n    /**\n     * Check whether the module scoping queue should be flushed, and flush it if needed.\n     *\n     * When the TestBed is reset, it clears the JIT module compilation queue, cancelling any\n     * in-progress module compilation. This creates a potential hazard - the very first time the\n     * TestBed is initialized (or if it's reset without being initialized), there may be pending\n     * compilations of modules declared in global scope. These compilations should be finished.\n     *\n     * To ensure that globally declared modules have their components scoped properly, this function\n     * is called whenever TestBed is initialized or reset. The _first_ time that this happens, prior\n     * to any other operations, the scoping queue is flushed.\n     */\n    checkGlobalCompilationFinished() {\n        // Checking _testNgModuleRef is null should not be necessary, but is left in as an additional\n        // guard that compilations queued in tests (after instantiation) are never flushed accidentally.\n        if (!this.globalCompilationChecked && this._testModuleRef === null) {\n            _flushModuleScopingQueueAsMuchAsPossible();\n        }\n        this.globalCompilationChecked = true;\n    }\n    destroyActiveFixtures() {\n        let errorCount = 0;\n        this._activeFixtures.forEach((fixture) => {\n            try {\n                fixture.destroy();\n            }\n            catch (e) {\n                errorCount++;\n                console.error('Error during cleanup of component', {\n                    component: fixture.componentInstance,\n                    stacktrace: e,\n                });\n            }\n        });\n        this._activeFixtures = [];\n        if (errorCount > 0 && this.shouldRethrowTeardownErrors()) {\n            throw Error(`${errorCount} ${errorCount === 1 ? 'component' : 'components'} ` +\n                `threw errors during cleanup`);\n        }\n    }\n    shouldRethrowTeardownErrors() {\n        const instanceOptions = this._instanceTeardownOptions;\n        const environmentOptions = TestBedImpl._environmentTeardownOptions;\n        // If the new teardown behavior hasn't been configured, preserve the old behavior.\n        if (!instanceOptions && !environmentOptions) {\n            return TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n        }\n        // Otherwise use the configured behavior or default to rethrowing.\n        return (instanceOptions?.rethrowErrors ??\n            environmentOptions?.rethrowErrors ??\n            this.shouldTearDownTestingModule());\n    }\n    shouldThrowErrorOnUnknownElements() {\n        // Check if a configuration has been provided to throw when an unknown element is found\n        return (this._instanceErrorOnUnknownElementsOption ??\n            TestBedImpl._environmentErrorOnUnknownElementsOption ??\n            THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n    }\n    shouldThrowErrorOnUnknownProperties() {\n        // Check if a configuration has been provided to throw when an unknown property is found\n        return (this._instanceErrorOnUnknownPropertiesOption ??\n            TestBedImpl._environmentErrorOnUnknownPropertiesOption ??\n            THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n    }\n    shouldTearDownTestingModule() {\n        return (this._instanceTeardownOptions?.destroyAfterEach ??\n            TestBedImpl._environmentTeardownOptions?.destroyAfterEach ??\n            TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT);\n    }\n    getDeferBlockBehavior() {\n        return this._instanceDeferBlockBehavior;\n    }\n    tearDownTestingModule() {\n        // If the module ref has already been destroyed, we won't be able to get a test renderer.\n        if (this._testModuleRef === null) {\n            return;\n        }\n        // Resolve the renderer ahead of time, because we want to remove the root elements as the very\n        // last step, but the injector will be destroyed as a part of the module ref destruction.\n        const testRenderer = this.inject(TestComponentRenderer);\n        try {\n            this._testModuleRef.destroy();\n        }\n        catch (e) {\n            if (this.shouldRethrowTeardownErrors()) {\n                throw e;\n            }\n            else {\n                console.error('Error during cleanup of a testing module', {\n                    component: this._testModuleRef.instance,\n                    stacktrace: e,\n                });\n            }\n        }\n        finally {\n            testRenderer.removeAllRootElements?.();\n        }\n    }\n    /**\n     * Execute any pending effects.\n     *\n     * @developerPreview\n     */\n    flushEffects() {\n        this.inject(_MicrotaskEffectScheduler).flush();\n        this.inject(_EffectScheduler).flush();\n    }\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * `TestBed` is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @publicApi\n */\nconst TestBed = TestBedImpl;\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`. Note: this function\n * (imported from the `@angular/core/testing` package) can **only** be used to inject dependencies\n * in tests. To inject dependencies in your application code, use the [`inject`](api/core/inject)\n * function from the `@angular/core` package instead.\n *\n * Example:\n *\n * ```ts\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * @publicApi\n */\nfunction inject(tokens, fn) {\n    const testBed = TestBedImpl.INSTANCE;\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n        return testBed.execute(tokens, fn, this);\n    };\n}\n/**\n * @publicApi\n */\nclass InjectSetupWrapper {\n    _moduleDef;\n    constructor(_moduleDef) {\n        this._moduleDef = _moduleDef;\n    }\n    _addModule() {\n        const moduleDef = this._moduleDef();\n        if (moduleDef) {\n            TestBedImpl.configureTestingModule(moduleDef);\n        }\n    }\n    inject(tokens, fn) {\n        const self = this;\n        // Not using an arrow function to preserve context passed from call site\n        return function () {\n            self._addModule();\n            return inject(tokens, fn).call(this);\n        };\n    }\n}\nfunction withModule(moduleDef, fn) {\n    if (fn) {\n        // Not using an arrow function to preserve context passed from call site\n        return function () {\n            const testBed = TestBedImpl.INSTANCE;\n            if (moduleDef) {\n                testBed.configureTestingModule(moduleDef);\n            }\n            return fn.apply(this);\n        };\n    }\n    return new InjectSetupWrapper(() => moduleDef);\n}\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with Jasmine, Mocha, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n// Reset the test providers and the fake async zone before each test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// beforeEach is only defined when executing the tests\nglobalThis.beforeEach?.(getCleanupHook(false));\n// We provide both a `beforeEach` and `afterEach`, because the updated behavior for\n// tearing down the module is supposed to run after the test so that we can associate\n// teardown errors with the correct test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// afterEach is only defined when executing the tests\nglobalThis.afterEach?.(getCleanupHook(true));\nfunction getCleanupHook(expectedTeardownValue) {\n    return () => {\n        const testBed = TestBedImpl.INSTANCE;\n        if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {\n            testBed.resetTestingModule();\n            resetFakeAsyncZoneIfExists();\n        }\n    };\n}\n/**\n * This API should be removed. But doing so seems to break `google3` and so it requires a bit of\n * investigation.\n *\n * A work around is to mark it as `@codeGenApi` for now and investigate later.\n *\n * @codeGenApi\n */\n// TODO(iminar): Remove this code in a safe way.\nconst __core_private_testing_placeholder__ = '';\n\n/**\n * Fake implementation of user agent history and navigation behavior. This is a\n * high-fidelity implementation of browser behavior that attempts to emulate\n * things like traversal delay.\n */\nclass FakeNavigation {\n    /**\n     * The fake implementation of an entries array. Only same-document entries\n     * allowed.\n     */\n    entriesArr = [];\n    /**\n     * The current active entry index into `entriesArr`.\n     */\n    currentEntryIndex = 0;\n    /**\n     * The current navigate event.\n     * @internal\n     */\n    navigateEvent = null;\n    /**\n     * A Map of pending traversals, so that traversals to the same entry can be\n     * re-used.\n     */\n    traversalQueue = new Map();\n    /**\n     * A Promise that resolves when the previous traversals have finished. Used to\n     * simulate the cross-process communication necessary for traversals.\n     */\n    nextTraversal = Promise.resolve();\n    /**\n     * A prospective current active entry index, which includes unresolved\n     * traversals. Used by `go` to determine where navigations are intended to go.\n     */\n    prospectiveEntryIndex = 0;\n    /**\n     * A test-only option to make traversals synchronous, rather than emulate\n     * cross-process communication.\n     */\n    synchronousTraversals = false;\n    /** Whether to allow a call to setInitialEntryForTesting. */\n    canSetInitialEntry = true;\n    /**\n     * `EventTarget` to dispatch events.\n     * @internal\n     */\n    eventTarget;\n    /** The next unique id for created entries. Replace recreates this id. */\n    nextId = 0;\n    /** The next unique key for created entries. Replace inherits this id. */\n    nextKey = 0;\n    /** Whether this fake is disposed. */\n    disposed = false;\n    /** Equivalent to `navigation.currentEntry`. */\n    get currentEntry() {\n        return this.entriesArr[this.currentEntryIndex];\n    }\n    get canGoBack() {\n        return this.currentEntryIndex > 0;\n    }\n    get canGoForward() {\n        return this.currentEntryIndex < this.entriesArr.length - 1;\n    }\n    createEventTarget;\n    _window;\n    get window() {\n        return this._window;\n    }\n    constructor(doc, startURL) {\n        this.createEventTarget = () => {\n            try {\n                // `document.createElement` because NodeJS `EventTarget` is\n                // incompatible with Domino's `Event`. That is, attempting to\n                // dispatch an event created by Domino's patched `Event` will\n                // throw an error since it is not an instance of a real Node\n                // `Event`.\n                return doc.createElement('div');\n            }\n            catch {\n                // Fallback to a basic EventTarget if `document.createElement`\n                // fails. This can happen with tests that pass in a value for document\n                // that is stubbed.\n                return new EventTarget();\n            }\n        };\n        this._window = document.defaultView ?? this.createEventTarget();\n        this.eventTarget = this.createEventTarget();\n        // First entry.\n        this.setInitialEntryForTesting(startURL);\n    }\n    /**\n     * Sets the initial entry.\n     */\n    setInitialEntryForTesting(url, options = { historyState: null }) {\n        if (!this.canSetInitialEntry) {\n            throw new Error('setInitialEntryForTesting can only be called before any ' + 'navigation has occurred');\n        }\n        const currentInitialEntry = this.entriesArr[0];\n        this.entriesArr[0] = new FakeNavigationHistoryEntry(this.eventTarget, new URL(url).toString(), {\n            index: 0,\n            key: currentInitialEntry?.key ?? String(this.nextKey++),\n            id: currentInitialEntry?.id ?? String(this.nextId++),\n            sameDocument: true,\n            historyState: options?.historyState,\n            state: options.state,\n        });\n    }\n    /** Returns whether the initial entry is still eligible to be set. */\n    canSetInitialEntryForTesting() {\n        return this.canSetInitialEntry;\n    }\n    /**\n     * Sets whether to emulate traversals as synchronous rather than\n     * asynchronous.\n     */\n    setSynchronousTraversalsForTesting(synchronousTraversals) {\n        this.synchronousTraversals = synchronousTraversals;\n    }\n    /** Equivalent to `navigation.entries()`. */\n    entries() {\n        return this.entriesArr.slice();\n    }\n    /** Equivalent to `navigation.navigate()`. */\n    navigate(url, options) {\n        const fromUrl = new URL(this.currentEntry.url);\n        const toUrl = new URL(url, this.currentEntry.url);\n        let navigationType;\n        if (!options?.history || options.history === 'auto') {\n            // Auto defaults to push, but if the URLs are the same, is a replace.\n            if (fromUrl.toString() === toUrl.toString()) {\n                navigationType = 'replace';\n            }\n            else {\n                navigationType = 'push';\n            }\n        }\n        else {\n            navigationType = options.history;\n        }\n        const hashChange = isHashChange(fromUrl, toUrl);\n        const destination = new FakeNavigationDestination({\n            url: toUrl.toString(),\n            state: options?.state,\n            sameDocument: hashChange,\n            historyState: null,\n        });\n        const result = new InternalNavigationResult(this);\n        const intercepted = this.userAgentNavigate(destination, result, {\n            navigationType,\n            cancelable: true,\n            canIntercept: true,\n            // Always false for navigate().\n            userInitiated: false,\n            hashChange,\n            info: options?.info,\n        });\n        if (!intercepted) {\n            this.updateNavigationEntriesForSameDocumentNavigation(this.navigateEvent);\n        }\n        return {\n            committed: result.committed,\n            finished: result.finished,\n        };\n    }\n    /** Equivalent to `history.pushState()`. */\n    pushState(data, title, url) {\n        this.pushOrReplaceState('push', data, title, url);\n    }\n    /** Equivalent to `history.replaceState()`. */\n    replaceState(data, title, url) {\n        this.pushOrReplaceState('replace', data, title, url);\n    }\n    pushOrReplaceState(navigationType, data, _title, url) {\n        const fromUrl = new URL(this.currentEntry.url);\n        const toUrl = url ? new URL(url, this.currentEntry.url) : fromUrl;\n        const hashChange = isHashChange(fromUrl, toUrl);\n        const destination = new FakeNavigationDestination({\n            url: toUrl.toString(),\n            sameDocument: true,\n            historyState: data,\n        });\n        const result = new InternalNavigationResult(this);\n        const intercepted = this.userAgentNavigate(destination, result, {\n            navigationType,\n            cancelable: true,\n            canIntercept: true,\n            // Always false for pushState() or replaceState().\n            userInitiated: false,\n            hashChange,\n        });\n        if (intercepted) {\n            return;\n        }\n        this.updateNavigationEntriesForSameDocumentNavigation(this.navigateEvent);\n    }\n    /** Equivalent to `navigation.traverseTo()`. */\n    traverseTo(key, options) {\n        const fromUrl = new URL(this.currentEntry.url);\n        const entry = this.findEntry(key);\n        if (!entry) {\n            const domException = new DOMException('Invalid key', 'InvalidStateError');\n            const committed = Promise.reject(domException);\n            const finished = Promise.reject(domException);\n            committed.catch(() => { });\n            finished.catch(() => { });\n            return {\n                committed,\n                finished,\n            };\n        }\n        if (entry === this.currentEntry) {\n            return {\n                committed: Promise.resolve(this.currentEntry),\n                finished: Promise.resolve(this.currentEntry),\n            };\n        }\n        if (this.traversalQueue.has(entry.key)) {\n            const existingResult = this.traversalQueue.get(entry.key);\n            return {\n                committed: existingResult.committed,\n                finished: existingResult.finished,\n            };\n        }\n        const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n        const destination = new FakeNavigationDestination({\n            url: entry.url,\n            state: entry.getState(),\n            historyState: entry.getHistoryState(),\n            key: entry.key,\n            id: entry.id,\n            index: entry.index,\n            sameDocument: entry.sameDocument,\n        });\n        this.prospectiveEntryIndex = entry.index;\n        const result = new InternalNavigationResult(this);\n        this.traversalQueue.set(entry.key, result);\n        this.runTraversal(() => {\n            this.traversalQueue.delete(entry.key);\n            const intercepted = this.userAgentNavigate(destination, result, {\n                navigationType: 'traverse',\n                cancelable: true,\n                canIntercept: true,\n                // Always false for traverseTo().\n                userInitiated: false,\n                hashChange,\n                info: options?.info,\n            });\n            if (!intercepted) {\n                this.userAgentTraverse(this.navigateEvent);\n            }\n        });\n        return {\n            committed: result.committed,\n            finished: result.finished,\n        };\n    }\n    /** Equivalent to `navigation.back()`. */\n    back(options) {\n        if (this.currentEntryIndex === 0) {\n            const domException = new DOMException('Cannot go back', 'InvalidStateError');\n            const committed = Promise.reject(domException);\n            const finished = Promise.reject(domException);\n            committed.catch(() => { });\n            finished.catch(() => { });\n            return {\n                committed,\n                finished,\n            };\n        }\n        const entry = this.entriesArr[this.currentEntryIndex - 1];\n        return this.traverseTo(entry.key, options);\n    }\n    /** Equivalent to `navigation.forward()`. */\n    forward(options) {\n        if (this.currentEntryIndex === this.entriesArr.length - 1) {\n            const domException = new DOMException('Cannot go forward', 'InvalidStateError');\n            const committed = Promise.reject(domException);\n            const finished = Promise.reject(domException);\n            committed.catch(() => { });\n            finished.catch(() => { });\n            return {\n                committed,\n                finished,\n            };\n        }\n        const entry = this.entriesArr[this.currentEntryIndex + 1];\n        return this.traverseTo(entry.key, options);\n    }\n    /**\n     * Equivalent to `history.go()`.\n     * Note that this method does not actually work precisely to how Chrome\n     * does, instead choosing a simpler model with less unexpected behavior.\n     * Chrome has a few edge case optimizations, for instance with repeated\n     * `back(); forward()` chains it collapses certain traversals.\n     */\n    go(direction) {\n        const targetIndex = this.prospectiveEntryIndex + direction;\n        if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n            return;\n        }\n        this.prospectiveEntryIndex = targetIndex;\n        this.runTraversal(() => {\n            // Check again that destination is in the entries array.\n            if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n                return;\n            }\n            const fromUrl = new URL(this.currentEntry.url);\n            const entry = this.entriesArr[targetIndex];\n            const hashChange = isHashChange(fromUrl, new URL(entry.url, this.currentEntry.url));\n            const destination = new FakeNavigationDestination({\n                url: entry.url,\n                state: entry.getState(),\n                historyState: entry.getHistoryState(),\n                key: entry.key,\n                id: entry.id,\n                index: entry.index,\n                sameDocument: entry.sameDocument,\n            });\n            const result = new InternalNavigationResult(this);\n            const intercepted = this.userAgentNavigate(destination, result, {\n                navigationType: 'traverse',\n                cancelable: true,\n                canIntercept: true,\n                // Always false for go().\n                userInitiated: false,\n                hashChange,\n            });\n            if (!intercepted) {\n                this.userAgentTraverse(this.navigateEvent);\n            }\n        });\n    }\n    /** Runs a traversal synchronously or asynchronously */\n    runTraversal(traversal) {\n        if (this.synchronousTraversals) {\n            traversal();\n            return;\n        }\n        // Each traversal occupies a single timeout resolution.\n        // This means that Promises added to commit and finish should resolve\n        // before the next traversal.\n        this.nextTraversal = this.nextTraversal.then(() => {\n            return new Promise((resolve) => {\n                setTimeout(() => {\n                    resolve();\n                    traversal();\n                });\n            });\n        });\n    }\n    /** Equivalent to `navigation.addEventListener()`. */\n    addEventListener(type, callback, options) {\n        this.eventTarget.addEventListener(type, callback, options);\n    }\n    /** Equivalent to `navigation.removeEventListener()`. */\n    removeEventListener(type, callback, options) {\n        this.eventTarget.removeEventListener(type, callback, options);\n    }\n    /** Equivalent to `navigation.dispatchEvent()` */\n    dispatchEvent(event) {\n        return this.eventTarget.dispatchEvent(event);\n    }\n    /** Cleans up resources. */\n    dispose() {\n        // Recreate eventTarget to release current listeners.\n        this.eventTarget = this.createEventTarget();\n        this.disposed = true;\n    }\n    /** Returns whether this fake is disposed. */\n    isDisposed() {\n        return this.disposed;\n    }\n    /**\n     * Implementation for all navigations and traversals.\n     * @returns true if the event was intercepted, otherwise false\n     */\n    userAgentNavigate(destination, result, options) {\n        // The first navigation should disallow any future calls to set the initial\n        // entry.\n        this.canSetInitialEntry = false;\n        if (this.navigateEvent) {\n            this.navigateEvent.cancel(new DOMException('Navigation was aborted', 'AbortError'));\n            this.navigateEvent = null;\n        }\n        return dispatchNavigateEvent({\n            navigationType: options.navigationType,\n            cancelable: options.cancelable,\n            canIntercept: options.canIntercept,\n            userInitiated: options.userInitiated,\n            hashChange: options.hashChange,\n            signal: result.signal,\n            destination,\n            info: options.info,\n            sameDocument: destination.sameDocument,\n            result,\n        });\n    }\n    /**\n     * Implementation for a push or replace navigation.\n     * https://whatpr.org/html/10919/browsing-the-web.html#url-and-history-update-steps\n     * https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n     * @internal\n     */\n    urlAndHistoryUpdateSteps(navigateEvent) {\n        this.updateNavigationEntriesForSameDocumentNavigation(navigateEvent);\n    }\n    /**\n     * Implementation for a traverse navigation.\n     *\n     * https://whatpr.org/html/10919/browsing-the-web.html#apply-the-traverse-history-step\n     * ...\n     * > Let updateDocument be an algorithm step which performs update document for history step application given targetEntry's document, targetEntry, changingNavigableContinuation's update-only, scriptHistoryLength, scriptHistoryIndex, navigationType, entriesForNavigationAPI, and previousEntry.\n     * > If targetEntry's document is equal to displayedDocument, then perform updateDocument.\n     * https://whatpr.org/html/10919/browsing-the-web.html#update-document-for-history-step-application\n     * which then goes to https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n     * @internal\n     */\n    userAgentTraverse(navigateEvent) {\n        const oldUrl = this.currentEntry.url;\n        this.updateNavigationEntriesForSameDocumentNavigation(navigateEvent);\n        // Happens as part of \"updating the document\" steps https://whatpr.org/html/10919/browsing-the-web.html#updating-the-document\n        const popStateEvent = createPopStateEvent({\n            state: navigateEvent.destination.getHistoryState(),\n        });\n        this._window.dispatchEvent(popStateEvent);\n        if (navigateEvent.hashChange) {\n            const hashchangeEvent = createHashChangeEvent(oldUrl, this.currentEntry.url);\n            this._window.dispatchEvent(hashchangeEvent);\n        }\n    }\n    /**\n     * https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n     * @internal\n     */\n    updateNavigationEntriesForSameDocumentNavigation({ destination, navigationType, result, }) {\n        const oldCurrentNHE = this.currentEntry;\n        const disposedNHEs = [];\n        if (navigationType === 'traverse') {\n            this.currentEntryIndex = destination.index;\n            if (this.currentEntryIndex === -1) {\n                throw new Error('unexpected current entry index');\n            }\n        }\n        else if (navigationType === 'push') {\n            this.currentEntryIndex++;\n            this.prospectiveEntryIndex = this.currentEntryIndex; // prospectiveEntryIndex isn't in the spec but is an implementation detail\n            disposedNHEs.push(...this.entriesArr.splice(this.currentEntryIndex));\n        }\n        else if (navigationType === 'replace') {\n            disposedNHEs.push(oldCurrentNHE);\n        }\n        if (navigationType === 'push' || navigationType === 'replace') {\n            const index = this.currentEntryIndex;\n            const key = navigationType === 'push' ? String(this.nextKey++) : this.currentEntry.key;\n            const newNHE = new FakeNavigationHistoryEntry(this.eventTarget, destination.url, {\n                id: String(this.nextId++),\n                key,\n                index,\n                sameDocument: true,\n                state: destination.getState(),\n                historyState: destination.getHistoryState(),\n            });\n            this.entriesArr[this.currentEntryIndex] = newNHE;\n        }\n        result.committedResolve(this.currentEntry);\n        const currentEntryChangeEvent = createFakeNavigationCurrentEntryChangeEvent({\n            from: oldCurrentNHE,\n            navigationType: navigationType,\n        });\n        this.eventTarget.dispatchEvent(currentEntryChangeEvent);\n        for (const disposedNHE of disposedNHEs) {\n            disposedNHE.dispose();\n        }\n    }\n    /** Utility method for finding entries with the given `key`. */\n    findEntry(key) {\n        for (const entry of this.entriesArr) {\n            if (entry.key === key)\n                return entry;\n        }\n        return undefined;\n    }\n    set onnavigate(\n    // tslint:disable-next-line:no-any\n    _handler) {\n        throw new Error('unimplemented');\n    }\n    // tslint:disable-next-line:no-any\n    get onnavigate() {\n        throw new Error('unimplemented');\n    }\n    set oncurrententrychange(_handler) {\n        throw new Error('unimplemented');\n    }\n    get oncurrententrychange() {\n        throw new Error('unimplemented');\n    }\n    set onnavigatesuccess(\n    // tslint:disable-next-line:no-any\n    _handler) {\n        throw new Error('unimplemented');\n    }\n    // tslint:disable-next-line:no-any\n    get onnavigatesuccess() {\n        throw new Error('unimplemented');\n    }\n    set onnavigateerror(\n    // tslint:disable-next-line:no-any\n    _handler) {\n        throw new Error('unimplemented');\n    }\n    // tslint:disable-next-line:no-any\n    get onnavigateerror() {\n        throw new Error('unimplemented');\n    }\n    _transition = null;\n    /** @internal */\n    set transition(t) {\n        this._transition = t;\n    }\n    get transition() {\n        return this._transition;\n    }\n    updateCurrentEntry(_options) {\n        throw new Error('unimplemented');\n    }\n    reload(_options) {\n        throw new Error('unimplemented');\n    }\n}\n/**\n * Fake equivalent of `NavigationHistoryEntry`.\n */\nclass FakeNavigationHistoryEntry {\n    eventTarget;\n    url;\n    sameDocument;\n    id;\n    key;\n    index;\n    state;\n    historyState;\n    // tslint:disable-next-line:no-any\n    ondispose = null;\n    constructor(eventTarget, url, { id, key, index, sameDocument, state, historyState, }) {\n        this.eventTarget = eventTarget;\n        this.url = url;\n        this.id = id;\n        this.key = key;\n        this.index = index;\n        this.sameDocument = sameDocument;\n        this.state = state;\n        this.historyState = historyState;\n    }\n    getState() {\n        // Budget copy.\n        return this.state ? JSON.parse(JSON.stringify(this.state)) : this.state;\n    }\n    getHistoryState() {\n        // Budget copy.\n        return this.historyState\n            ? JSON.parse(JSON.stringify(this.historyState))\n            : this.historyState;\n    }\n    addEventListener(type, callback, options) {\n        this.eventTarget.addEventListener(type, callback, options);\n    }\n    removeEventListener(type, callback, options) {\n        this.eventTarget.removeEventListener(type, callback, options);\n    }\n    dispatchEvent(event) {\n        return this.eventTarget.dispatchEvent(event);\n    }\n    /** internal */\n    dispose() {\n        const disposeEvent = new Event('disposed');\n        this.dispatchEvent(disposeEvent);\n        // release current listeners\n        this.eventTarget = null;\n    }\n}\n/**\n * Create a fake equivalent of `NavigateEvent`. This is not a class because ES5\n * transpiled JavaScript cannot extend native Event.\n *\n * https://html.spec.whatwg.org/multipage/nav-history-apis.html#navigate-event-firing\n */\nfunction dispatchNavigateEvent({ cancelable, canIntercept, userInitiated, hashChange, navigationType, signal, destination, info, sameDocument, result, }) {\n    const { navigation } = result;\n    const event = new Event('navigate', { bubbles: false, cancelable });\n    event.focusResetBehavior = null;\n    event.scrollBehavior = null;\n    event.interceptionState = 'none';\n    event.canIntercept = canIntercept;\n    event.userInitiated = userInitiated;\n    event.hashChange = hashChange;\n    event.navigationType = navigationType;\n    event.signal = signal;\n    event.destination = destination;\n    event.info = info;\n    event.downloadRequest = null;\n    event.formData = null;\n    event.result = result;\n    event.sameDocument = sameDocument;\n    let precommitHandlers = [];\n    let handlers = [];\n    // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigateevent-intercept\n    event.intercept = function (options) {\n        if (!this.canIntercept) {\n            throw new DOMException(`Cannot intercept when canIntercept is 'false'`, 'SecurityError');\n        }\n        this.interceptionState = 'intercepted';\n        event.sameDocument = true;\n        const precommitHandler = options?.precommitHandler;\n        if (precommitHandler) {\n            if (!this.cancelable) {\n                throw new DOMException(`Cannot use precommitHandler when cancelable is 'false'`, 'InvalidStateError');\n            }\n            precommitHandlers.push(precommitHandler);\n        }\n        if (event.interceptionState !== 'none' && event.interceptionState !== 'intercepted') {\n            throw new Error('Event interceptionState should be \"none\" or \"intercepted\"');\n        }\n        event.interceptionState = 'intercepted';\n        const handler = options?.handler;\n        if (handler) {\n            handlers.push(handler);\n        }\n        // override old options with new ones. UA _may_ report a console warning if new options differ from previous\n        event.focusResetBehavior = options?.focusReset ?? event.focusResetBehavior;\n        event.scrollBehavior = options?.scroll ?? event.scrollBehavior;\n    };\n    // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigateevent-scroll\n    event.scroll = function () {\n        if (event.interceptionState !== 'committed') {\n            throw new DOMException(`Failed to execute 'scroll' on 'NavigateEvent': scroll() must be ` +\n                `called after commit() and interception options must specify manual scroll.`, 'InvalidStateError');\n        }\n        processScrollBehavior(event);\n    };\n    // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigationprecommitcontroller-redirect\n    function redirect(url) {\n        if (event.interceptionState === 'none') {\n            throw new Error('cannot redirect when event is not intercepted');\n        }\n        if (event.interceptionState !== 'intercepted') {\n            throw new DOMException(`cannot redirect when event is not in 'intercepted' state`, 'InvalidStateError');\n        }\n        if (event.navigationType !== 'push' && event.navigationType !== 'replace') {\n            throw new DOMException(`cannot redirect when navigationType is not 'push' or 'replace`, 'InvalidStateError');\n        }\n        const toUrl = new URL(url, navigation.currentEntry.url);\n        event.destination.url = toUrl.href;\n    }\n    // https://whatpr.org/html/10919/nav-history-apis.html#inner-navigate-event-firing-algorithm\n    // \"Let commit be the following steps:\"\n    function commit() {\n        if (result.signal.aborted) {\n            return;\n        }\n        if (event.interceptionState !== 'none') {\n            event.interceptionState = 'committed';\n            if (!navigation.currentEntry) {\n                throw new Error('from history entry should not be null');\n            }\n            navigation.transition = new InternalNavigationTransition(navigation.currentEntry, navigationType);\n            switch (event.navigationType) {\n                case 'push':\n                case 'replace': {\n                    navigation.urlAndHistoryUpdateSteps(event);\n                    break;\n                }\n                case 'reload': {\n                    navigation.updateNavigationEntriesForSameDocumentNavigation(event);\n                    break;\n                }\n                case 'traverse': {\n                    navigation.userAgentTraverse(event);\n                    break;\n                }\n            }\n        }\n        const promisesList = handlers.map((handler) => handler());\n        if (promisesList.length === 0) {\n            promisesList.push(Promise.resolve());\n        }\n        Promise.all(promisesList)\n            .then(() => {\n            // Follows steps outlined under \"Wait for all of promisesList, with the following success steps:\"\n            // in the spec https://html.spec.whatwg.org/multipage/nav-history-apis.html#navigate-event-firing.\n            if (result.signal.aborted) {\n                return;\n            }\n            if (event !== navigation.navigateEvent) {\n                throw new Error(\"Navigation's ongoing event not equal to resolved event\");\n            }\n            navigation.navigateEvent = null;\n            finishNavigationEvent(event, true);\n            const navigatesuccessEvent = new Event('navigatesuccess', { bubbles: false, cancelable });\n            navigation.eventTarget.dispatchEvent(navigatesuccessEvent);\n            result.finishedResolve();\n            if (navigation.transition !== null) {\n                navigation.transition.finishedResolve();\n            }\n            navigation.transition = null;\n        })\n            .catch((reason) => event.cancel(reason));\n    }\n    // Internal only.\n    // https://whatpr.org/html/10919/nav-history-apis.html#inner-navigate-event-firing-algorithm\n    // \"Let cancel be the following steps given reason\"\n    event.cancel = function (reason) {\n        if (result.signal.aborted) {\n            return;\n        }\n        if (event !== navigation.navigateEvent) {\n            throw new Error(\"Navigation's ongoing event not equal to resolved event\");\n        }\n        navigation.navigateEvent = null;\n        if (event.interceptionState !== 'intercepted') {\n            finishNavigationEvent(event, false);\n        }\n        const navigateerrorEvent = new Event('navigateerror', { bubbles: false, cancelable });\n        navigation.eventTarget.dispatchEvent(navigateerrorEvent);\n        result.finishedReject(reason);\n        if (navigation.transition !== null) {\n            navigation.transition.finishedReject(reason);\n        }\n        navigation.transition = null;\n    };\n    function dispatch() {\n        navigation.navigateEvent = event;\n        navigation.eventTarget.dispatchEvent(event);\n        if (precommitHandlers.length === 0) {\n            commit();\n        }\n        else {\n            const precommitController = { redirect };\n            const precommitPromisesList = precommitHandlers.map((handler) => handler(precommitController));\n            Promise.all(precommitPromisesList)\n                .then(() => commit())\n                .catch((reason) => event.cancel(reason));\n        }\n    }\n    dispatch();\n    return event.interceptionState !== 'none';\n}\n/** https://whatpr.org/html/10919/nav-history-apis.html#navigateevent-finish */\nfunction finishNavigationEvent(event, didFulfill) {\n    if (event.interceptionState === 'finished') {\n        throw new Error('Attempting to finish navigation event that was already finished');\n    }\n    if (event.interceptionState === 'intercepted') {\n        if (didFulfill === true) {\n            throw new Error('didFulfill should be false');\n        }\n        // assert precommit handlers is not empty\n        event.interceptionState = 'finished';\n        return;\n    }\n    if (event.interceptionState === 'none') {\n        return;\n    }\n    potentiallyResetFocus(event);\n    if (didFulfill) {\n        potentiallyResetScroll(event);\n    }\n    event.interceptionState = 'finished';\n}\n/** https://whatpr.org/html/10919/nav-history-apis.html#potentially-reset-the-focus */\nfunction potentiallyResetFocus(event) {\n    if (event.interceptionState !== 'committed' && event.interceptionState !== 'scrolled') {\n        throw new Error('cannot reset focus if navigation event is not committed or scrolled');\n    }\n    // TODO(atscott): The rest of the steps\n}\nfunction potentiallyResetScroll(event) {\n    if (event.interceptionState !== 'committed' && event.interceptionState !== 'scrolled') {\n        throw new Error('cannot reset scroll if navigation event is not committed or scrolled');\n    }\n    if (event.interceptionState === 'scrolled' || event.scrollBehavior === 'manual') {\n        return;\n    }\n    processScrollBehavior(event);\n}\n/* https://whatpr.org/html/10919/nav-history-apis.html#process-scroll-behavior */\nfunction processScrollBehavior(event) {\n    if (event.interceptionState !== 'committed') {\n        throw new Error('invalid event interception state when processing scroll behavior');\n    }\n    event.interceptionState = 'scrolled';\n    // TODO(atscott): the rest of the steps\n}\n/**\n * Create a fake equivalent of `NavigationCurrentEntryChange`. This does not use\n * a class because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigationCurrentEntryChangeEvent({ from, navigationType, }) {\n    const event = new Event('currententrychange', {\n        bubbles: false,\n        cancelable: false,\n    });\n    event.from = from;\n    event.navigationType = navigationType;\n    return event;\n}\n/**\n * Create a fake equivalent of `PopStateEvent`. This does not use a class\n * because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createPopStateEvent({ state }) {\n    const event = new Event('popstate', {\n        bubbles: false,\n        cancelable: false,\n    });\n    event.state = state;\n    return event;\n}\nfunction createHashChangeEvent(newURL, oldURL) {\n    const event = new Event('hashchange', {\n        bubbles: false,\n        cancelable: false,\n    });\n    event.newURL = newURL;\n    event.oldURL = oldURL;\n    return event;\n}\n/**\n * Fake equivalent of `NavigationDestination`.\n */\nclass FakeNavigationDestination {\n    url;\n    sameDocument;\n    key;\n    id;\n    index;\n    state;\n    historyState;\n    constructor({ url, sameDocument, historyState, state, key = null, id = null, index = -1, }) {\n        this.url = url;\n        this.sameDocument = sameDocument;\n        this.state = state;\n        this.historyState = historyState;\n        this.key = key;\n        this.id = id;\n        this.index = index;\n    }\n    getState() {\n        return this.state;\n    }\n    getHistoryState() {\n        return this.historyState;\n    }\n}\n/** Utility function to determine whether two UrlLike have the same hash. */\nfunction isHashChange(from, to) {\n    return (to.hash !== from.hash &&\n        to.hostname === from.hostname &&\n        to.pathname === from.pathname &&\n        to.search === from.search);\n}\nclass InternalNavigationTransition {\n    from;\n    navigationType;\n    finished;\n    finishedResolve;\n    finishedReject;\n    constructor(from, navigationType) {\n        this.from = from;\n        this.navigationType = navigationType;\n        this.finished = new Promise((resolve, reject) => {\n            this.finishedReject = reject;\n            this.finishedResolve = resolve;\n        });\n        // All rejections are handled.\n        this.finished.catch(() => { });\n    }\n}\n/**\n * Internal utility class for representing the result of a navigation.\n * Generally equivalent to the \"apiMethodTracker\" in the spec.\n */\nclass InternalNavigationResult {\n    navigation;\n    committedTo = null;\n    committedResolve;\n    committedReject;\n    finishedResolve;\n    finishedReject;\n    committed;\n    finished;\n    get signal() {\n        return this.abortController.signal;\n    }\n    abortController = new AbortController();\n    constructor(navigation) {\n        this.navigation = navigation;\n        this.committed = new Promise((resolve, reject) => {\n            this.committedResolve = (entry) => {\n                this.committedTo = entry;\n                resolve(entry);\n            };\n            this.committedReject = reject;\n        });\n        this.finished = new Promise(async (resolve, reject) => {\n            this.finishedResolve = () => {\n                if (this.committedTo === null) {\n                    throw new Error('NavigateEvent should have been committed before resolving finished promise.');\n                }\n                resolve(this.committedTo);\n            };\n            this.finishedReject = (reason) => {\n                reject(reason);\n                this.abortController.abort(reason);\n            };\n        });\n        // All rejections are handled.\n        this.committed.catch(() => { });\n        this.finished.catch(() => { });\n    }\n}\n\nclass Log {\n    logItems;\n    constructor() {\n        this.logItems = [];\n    }\n    add(value) {\n        this.logItems.push(value);\n    }\n    fn(value) {\n        return () => {\n            this.logItems.push(value);\n        };\n    }\n    clear() {\n        this.logItems = [];\n    }\n    result() {\n        return this.logItems.join('; ');\n    }\n    static ɵfac = function Log_Factory(__ngFactoryType__) { return new (__ngFactoryType__ || Log)(); };\n    static ɵprov = /*@__PURE__*/ i0.ɵɵdefineInjectable({ token: Log, factory: Log.ɵfac });\n}\n(() => { (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Log, [{\n        type: Injectable\n    }], () => [], null); })();\n\nexport { ComponentFixture, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, DeferBlockFixture, InjectSetupWrapper, TestBed, TestComponentRenderer, __core_private_testing_placeholder__, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, getTestBed, inject, resetFakeAsyncZone, tick, waitForAsync, withModule, FakeNavigation as ɵFakeNavigation, Log as ɵLog, MetadataOverrider as ɵMetadataOverrider };\n"], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,EAAEC,gBAAgB,IAAIC,gBAAgB,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,sBAAsB,IAAIC,sBAAsB,EAAEC,wBAAwB,IAAIC,wBAAwB,EAAEC,eAAe,IAAIC,eAAe,EAAEC,cAAc,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,WAAW,IAAIC,WAAW,EAAEC,cAAc,EAAEC,qBAAqB,IAAIC,qBAAqB,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,yBAAyB,IAAIC,yBAAyB,EAAEC,gBAAgB,IAAIC,gBAAgB,EAAEC,yBAAyB,IAAIC,yBAAyB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,UAAU,IAAIC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,iCAAiC,IAAIC,iCAAiC,EAAEC,YAAY,IAAIC,YAAY,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,iBAAiB,EAAEC,gCAAgC,IAAIC,gCAAgC,EAAEC,wBAAwB,IAAIC,wBAAwB,EAAEC,0BAA0B,IAAIC,0BAA0B,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,kBAAkB,IAAIC,kBAAkB,EAAEC,YAAY,IAAIC,YAAY,EAAEC,wBAAwB,IAAIC,wBAAwB,EAAEC,YAAY,IAAIC,YAAY,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,WAAW,IAAIC,WAAW,EAAEC,iBAAiB,IAAIC,iBAAiB,EAAEC,YAAY,IAAIC,YAAY,EAAEC,YAAY,IAAIC,YAAY,EAAEC,WAAW,IAAIC,WAAW,EAAEC,2BAA2B,IAAIC,2BAA2B,EAAEC,WAAW,IAAIC,WAAW,EAAEC,oBAAoB,IAAIC,oBAAoB,EAAEC,yCAAyC,IAAIC,yCAAyC,EAAEC,gCAAgC,IAAIC,gCAAgC,EAAEC,mCAAmC,IAAIC,mCAAmC,EAAEC,6BAA6B,IAAIC,6BAA6B,EAAEC,QAAQ,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,mCAAmC,IAAIC,mCAAmC,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,oBAAoB,IAAIC,oBAAoB,EAAEC,sCAAsC,IAAIC,sCAAsC,EAAEC,gBAAgB,IAAIC,gBAAgB,EAAEC,4BAA4B,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,kBAAkB,IAAIC,kBAAkB,EAAEC,WAAW,EAAEC,oCAAoC,IAAIC,oCAAoC,EAAEC,wBAAwB,IAAIC,wBAAwB,EAAEC,4BAA4B,IAAIC,4BAA4B,EAAEC,6BAA6B,IAAIC,6BAA6B,EAAEC,4BAA4B,IAAIC,4BAA4B,EAAEC,6BAA6B,IAAIC,6BAA6B,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,wCAAwC,IAAIC,wCAAwC,QAAQ,eAAe;AACx6F,SAAS9G,mBAAmB,IAAI+G,kBAAkB,EAAE1H,gBAAgB,IAAI2H,eAAe,QAAQ,eAAe;AAC9G,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,cAAc,QAAQ,mBAAmB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,EAAE,EAAE;EACtB,MAAMC,KAAK,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,IAAI;EACvD,IAAI,CAACD,KAAK,EAAE;IACR,OAAO,YAAY;MACf,OAAOE,OAAO,CAACC,MAAM,CAAC,4EAA4E,GAC9F,yDAAyD,CAAC;IAClE,CAAC;EACL;EACA,MAAMC,SAAS,GAAGJ,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACK,UAAU,CAAC,WAAW,CAAC,CAAC;EAC/D,IAAI,OAAOD,SAAS,KAAK,UAAU,EAAE;IACjC,OAAOA,SAAS,CAACL,EAAE,CAAC;EACxB;EACA,OAAO,YAAY;IACf,OAAOG,OAAO,CAACC,MAAM,CAAC,gFAAgF,GAClG,iEAAiE,CAAC;EAC1E,CAAC;AACL;AAEA,MAAMG,kCAAkC,GAAG,IAAI;AAC/C,MAAMC,8BAA8B,CAAC;EAAAC,YAAA;IAAAC,eAAA,eAC1B7I,QAAQ,CAACC,MAAM,CAAC;IAAA4I,eAAA,2BACJ7I,QAAQ,CAACE,YAAY,CAAC;IAAA2I,eAAA,oCACb,IAAIC,GAAG,CAAC,CAAC;EAAA;EACrCC,WAAWA,CAACC,CAAC,EAAE;IACX,IAAI;MACA,IAAI,CAACC,IAAI,CAACC,iBAAiB,CAAC,MAAM,IAAI,CAACC,gBAAgB,CAACJ,WAAW,CAACC,CAAC,CAAC,CAAC;IAC3E,CAAC,CACD,OAAOI,SAAS,EAAE;MACdJ,CAAC,GAAGI,SAAS;IACjB;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACC,yBAAyB,CAACC,IAAI,GAAG,CAAC,EAAE;MACzC,KAAK,MAAMnB,EAAE,IAAI,IAAI,CAACkB,yBAAyB,CAACE,MAAM,CAAC,CAAC,EAAE;QACtDpB,EAAE,CAACa,CAAC,CAAC;MACT;MACA,IAAI,CAACK,yBAAyB,CAACG,KAAK,CAAC,CAAC;IAC1C,CAAC,MACI;MACD,MAAMR,CAAC;IACX;EACJ;AAGJ;AAACS,+BAAA,GA1BKd,8BAA8B;AAAAE,eAAA,CAA9BF,8BAA8B,eAwBlB,SAASe,sCAAsCA,CAACC,iBAAiB,EAAE;EAAE,OAAO,KAAKA,iBAAiB,IAAIhB,+BAA8B,EAAE,CAAC;AAAE,CAAC;AAAAE,eAAA,CAxBtJF,8BAA8B,gBAyBjB,aAAc7I,EAAE,CAAC8J,kBAAkB,CAAC;EAAEC,KAAK,EAAElB,+BAA8B;EAAEmB,OAAO,EAAEnB,+BAA8B,CAACoB;AAAK,CAAC,CAAC;AAE/I,CAAC,MAAM;EAAE,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKlK,EAAE,CAACmK,iBAAiB,CAACtB,8BAA8B,EAAE,CAAC;IAC1GuB,IAAI,EAAE/J;EACV,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA,MAAMgK,iBAAiB,CAAC;EAGpB;EACAvB,WAAWA,CAACwB,KAAK,EAAEC,gBAAgB,EAAE;IAAAxB,eAAA;IAAAA,eAAA;IACjC,IAAI,CAACuB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;EACA;AACJ;AACA;AACA;EACUC,MAAMA,CAACC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI,CAACC,gBAAgB,CAACH,KAAK,EAAEC,KAAI,CAACJ,KAAK,CAAC,EAAE;QACtC,MAAMO,aAAa,GAAGC,8BAA8B,CAACL,KAAK,CAAC;QAC3D,MAAM,IAAIM,KAAK,CAAC,6CAA6CF,aAAa,YAAY,GAClF,qBAAqBA,aAAa,CAACG,WAAW,CAAC,CAAC,+BAA+B,CAAC;MACxF;MACA,IAAIP,KAAK,KAAKlK,gBAAgB,CAAC0K,QAAQ,EAAE;QACrC,MAAMxK,uBAAuB,CAACiK,KAAI,CAACJ,KAAK,CAACY,QAAQ,EAAER,KAAI,CAACJ,KAAK,CAACa,KAAK,EAAET,KAAI,CAACJ,KAAK,CAACc,KAAK,CAAC;MAC1F;MACA;MACA;MACA,MAAMC,mBAAmB,GAAG,IAAI;MAChC1K,sBAAsB,CAAC8J,KAAK,EAAEC,KAAI,CAACJ,KAAK,CAACc,KAAK,EAAEV,KAAI,CAACJ,KAAK,CAACgB,UAAU,EAAED,mBAAmB,CAAC;MAC3FX,KAAI,CAACH,gBAAgB,CAACgB,aAAa,CAAC,CAAC;IAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIC,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,EAAE;IACtB;IACA;IACA;IACA,MAAMC,kBAAkB,GAAG,EAAE;IAC7B,IAAI,IAAI,CAACpB,KAAK,CAACgB,UAAU,CAACK,MAAM,IAAI9K,wBAAwB,EAAE;MAC1D,MAAMsK,KAAK,GAAG,IAAI,CAACb,KAAK,CAACgB,UAAU,CAACzK,wBAAwB,CAAC;MAC7DE,eAAe,CAACoK,KAAK,EAAEM,WAAW,CAAC;MACnC,KAAK,MAAMnB,KAAK,IAAImB,WAAW,EAAE;QAC7BC,kBAAkB,CAACE,IAAI,CAAC,IAAIvB,iBAAiB,CAACC,KAAK,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAChF;IACJ;IACA,OAAO/B,OAAO,CAACqD,OAAO,CAACH,kBAAkB,CAAC;EAC9C;AACJ;AACA,SAASd,gBAAgBA,CAACH,KAAK,EAAEH,KAAK,EAAE;EACpC,QAAQG,KAAK;IACT,KAAKlK,gBAAgB,CAACuL,WAAW;MAC7B,OAAOxB,KAAK,CAACY,QAAQ,CAACa,oBAAoB,KAAK,IAAI;IACvD,KAAKxL,gBAAgB,CAACyL,OAAO;MACzB,OAAO1B,KAAK,CAACY,QAAQ,CAACe,gBAAgB,KAAK,IAAI;IACnD,KAAK1L,gBAAgB,CAACwK,KAAK;MACvB,OAAOT,KAAK,CAACY,QAAQ,CAACgB,cAAc,KAAK,IAAI;IACjD,KAAK3L,gBAAgB,CAAC0K,QAAQ;MAC1B,OAAO,IAAI;IACf;MACI,OAAO,KAAK;EACpB;AACJ;AACA,SAASH,8BAA8BA,CAACL,KAAK,EAAE;EAC3C,QAAQA,KAAK;IACT,KAAKlK,gBAAgB,CAACuL,WAAW;MAC7B,OAAO,aAAa;IACxB,KAAKvL,gBAAgB,CAACyL,OAAO;MACzB,OAAO,SAAS;IACpB,KAAKzL,gBAAgB,CAACwK,KAAK;MACvB,OAAO,OAAO;IAClB;MACI,OAAO,MAAM;EACrB;AACJ;;AAEA;AACA,MAAMoB,0CAA0C,GAAG,IAAI;AACvD;AACA,MAAMC,iCAAiC,GAAG,KAAK;AAC/C;AACA,MAAMC,mCAAmC,GAAG,KAAK;AACjD;AACA,MAAMC,4BAA4B,GAAGpL,mBAAmB,CAACqL,WAAW;AACpE;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxBC,iBAAiBA,CAACC,aAAa,EAAE,CAAE;EACnCC,qBAAqBA,CAAA,EAAG,CAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAI5L,cAAc,CAAC,4BAA4B,CAAC;AACnF;AACA;AACA;AACA,MAAM6L,wBAAwB,GAAG,IAAI7L,cAAc,CAAC,0BAA0B,CAAC;;AAE/E;AACA;AACA;AACA;AACA;AACA,MAAM8L,gBAAgB,CAAC;EAgDnB;EACAhE,WAAWA,CAACiE,YAAY,EAAE;IAAA,IAAAC,QAAA;IAAAjE,eAAA;IA/C1B;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA;IAAAA,eAAA;IAAAA,eAAA,uBAKe,KAAK;IACpB;IAAAA,eAAA,6BACqB7I,QAAQ,CAAC2M,wBAAwB,EAAE;MAAEI,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3E;IAAAlE,eAAA,kBACU,IAAI,CAACmE,kBAAkB,GAAG,IAAI9L,WAAW,CAAC,CAAC,GAAGlB,QAAQ,CAACC,MAAM,CAAC;IACxE;IACA;IACA;IACA;IACA;IACA;IACA;IAAA4I,eAAA,kBACU7I,QAAQ,CAACmB,cAAc,CAAC;IAAA0H,eAAA,sBACpB,IAAI,CAACoE,OAAO;IAAApE,eAAA,uBACX7I,QAAQ,CAACqB,qBAAqB,CAAC;IAAAwH,eAAA,0BAC5B7I,QAAQ,CAAC2I,8BAA8B,CAAC;IAAAE,eAAA,0BACxC7I,QAAQ,CAACuB,iBAAiB,CAAC;IAAAsH,eAAA,oBACjC7I,QAAQ,CAACyB,yBAAyB,CAAC;IAAAoH,eAAA,8BACzB7I,QAAQ,CAAC2B,gBAAgB,CAAC;IAAAkH,eAAA,mCACrB7I,QAAQ,CAAC6B,yBAAyB,CAAC;IAAAgH,eAAA,4BAC1C,IAAI,CAACqE,eAAe,GAAG,IAAI,GAAG,KAAK;IAAArE,eAAA,sBAAAiE,QAAA,GAC1C9M,QAAQ,CAAC0M,0BAA0B,EAAE;MAAEK,QAAQ,EAAE;IAAK,CAAC,CAAC,cAAAD,QAAA,cAAAA,QAAA,GAAI,IAAI,CAACK,iBAAiB;IAAAtE,eAAA,wBAC/E,IAAIb,YAAY,CAAC,CAAC;IAClC;IAAAa,eAAA,iBACS,IAAI,CAACmE,kBAAkB,GAAG,IAAI,GAAG,IAAI,CAACI,OAAO;IAGlD,IAAI,CAACP,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACQ,iBAAiB,GAAGR,YAAY,CAACQ,iBAAiB;IACvD,IAAI,CAACC,UAAU,GAAGT,YAAY,CAACU,QAAQ;IACvC,IAAI,CAACC,YAAY,GAAG1L,YAAY,CAAC,IAAI,CAACwL,UAAU,CAACG,aAAa,CAAC;IAC/D,IAAI,CAACC,iBAAiB,GAAGb,YAAY,CAACc,QAAQ;IAC9C,IAAI,CAACF,aAAa,GAAG,IAAI,CAACH,UAAU,CAACG,aAAa;IAClD,IAAI,CAACZ,YAAY,GAAGA,YAAY;IAChC,IAAI,IAAI,CAACe,UAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA;MACjB,IAAI,CAACC,WAAW,CAACC,iBAAiB,CAACC,GAAG,CAAC,IAAI,CAACpB,YAAY,CAACqB,QAAQ,CAAC;MAClE,CAAAL,eAAA,OAAI,CAACM,SAAS,cAAAN,eAAA,eAAdA,eAAA,CAAgBO,MAAM,CAAC,CAAC,CAAC,sCAAsC,CAAC;MAChE,CAAAN,gBAAA,OAAI,CAACK,SAAS,cAAAL,gBAAA,eAAdA,gBAAA,CAAgBM,MAAM,CAAC,CAAC,CAAC,mDAAmD,CAAC;IACjF;IACA,IAAI,CAACvB,YAAY,CAACqB,QAAQ,CAACG,SAAS,CAAC,MAAM;MACvC,IAAI,CAACN,WAAW,CAACC,iBAAiB,CAACM,MAAM,CAAC,IAAI,CAACzB,YAAY,CAACqB,QAAQ,CAAC;IACzE,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAACd,OAAO,CAAClE,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACqF,aAAa,CAACN,GAAG,CAAC,IAAI,CAACb,OAAO,CAACoB,OAAO,CAACC,SAAS,CAAC;QAClDC,IAAI,EAAGC,KAAK,IAAK;UACb,MAAMA,KAAK;QACf;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACItD,aAAaA,CAACuD,cAAc,GAAG,IAAI,EAAE;IACjC,IAAI,CAACC,wBAAwB,CAACC,KAAK,CAAC,CAAC;IACrC,MAAMC,sBAAsB,GAAG,IAAI,CAAClC,YAAY,CAACQ,iBAAiB,CAACuB,cAAc;IACjF,IAAI;MACA,IAAI,CAACA,cAAc,EAAE;QACjB,IAAI,CAAC/B,YAAY,CAACQ,iBAAiB,CAACuB,cAAc,GAAG,MAAM,CAAE,CAAC;MAClE;MACA,IAAI,IAAI,CAAC1B,eAAe,EAAE;QACtB,IAAI;UACA,IAAI,CAACa,WAAW,CAACC,iBAAiB,CAACC,GAAG,CAAC,IAAI,CAACpB,YAAY,CAACqB,QAAQ,CAAC;UAClE,IAAI,CAACjB,OAAO,CAAC+B,IAAI,CAAC,CAAC;QACvB,CAAC,SACO;UACJ,IAAI,CAAC,IAAI,CAACpB,UAAU,EAAE;YAClB,IAAI,CAACG,WAAW,CAACC,iBAAiB,CAACM,MAAM,CAAC,IAAI,CAACzB,YAAY,CAACqB,QAAQ,CAAC;UACzE;QACJ;MACJ,CAAC,MACI;QACD;QACA;QACA,IAAI,CAACd,OAAO,CAAC6B,GAAG,CAAC,MAAM;UACnB;UACA,IAAI,CAACC,mBAAmB,CAACJ,KAAK,CAAC,CAAC;UAChC,IAAI,CAACzB,iBAAiB,CAAChC,aAAa,CAAC,CAAC;UACtC,IAAI,CAACuD,cAAc,CAAC,CAAC;QACzB,CAAC,CAAC;MACN;IACJ,CAAC,SACO;MACJ,IAAI,CAAC/B,YAAY,CAACQ,iBAAiB,CAACuB,cAAc,GAAGG,sBAAsB;IAC/E;IACA,IAAI,CAACF,wBAAwB,CAACC,KAAK,CAAC,CAAC;EACzC;EACA;AACJ;AACA;EACIF,cAAcA,CAAA,EAAG;IACb,IAAI,CAACvB,iBAAiB,CAACuB,cAAc,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIO,iBAAiBA,CAACvB,UAAU,GAAG,IAAI,EAAE;IACjC,IAAI,IAAI,CAACZ,kBAAkB,IAAI,CAAC,IAAI,CAACE,eAAe,EAAE;MAClD,MAAM,IAAIrC,KAAK,CAAC,qEAAqE,CAAC;IAC1F;IACA,IAAI+C,UAAU,KAAK,IAAI,CAACA,UAAU,EAAE;MAChC,IAAIA,UAAU,EAAE;QACZ,IAAI,CAACG,WAAW,CAACC,iBAAiB,CAACC,GAAG,CAAC,IAAI,CAACpB,YAAY,CAACqB,QAAQ,CAAC;MACtE,CAAC,MACI;QACD,IAAI,CAACH,WAAW,CAACC,iBAAiB,CAACM,MAAM,CAAC,IAAI,CAACzB,YAAY,CAACqB,QAAQ,CAAC;MACzE;IACJ;IACA,IAAI,CAACN,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACvC,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;EACI+D,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACC,YAAY,CAACC,eAAe,CAACC,KAAK;EACnD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACJ,QAAQ,CAAC,CAAC,EAAE;MACjB,OAAO9G,OAAO,CAACqD,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,OAAO,IAAIrD,OAAO,CAAC,CAACqD,OAAO,EAAEpD,MAAM,KAAK;MACpC,IAAI,CAACkH,eAAe,CAACpG,yBAAyB,CAAC4E,GAAG,CAAC1F,MAAM,CAAC;MAC1D,IAAI,CAAC0E,OAAO,CAACuC,UAAU,CAAC,CAAC,CAACE,IAAI,CAAC,MAAM;QACjC,IAAI,CAACD,eAAe,CAACpG,yBAAyB,CAACiF,MAAM,CAAC/F,MAAM,CAAC;QAC7DoD,OAAO,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIL,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMN,KAAK,GAAG,IAAI,CAAC4B,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC;IAClDrN,eAAe,CAACoK,KAAK,EAAEM,WAAW,CAAC;IACnC,MAAMC,kBAAkB,GAAG,EAAE;IAC7B,KAAK,MAAMpB,KAAK,IAAImB,WAAW,EAAE;MAC7BC,kBAAkB,CAACE,IAAI,CAAC,IAAIvB,iBAAiB,CAACC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/D;IACA,OAAO9B,OAAO,CAACqD,OAAO,CAACH,kBAAkB,CAAC;EAC9C;EACAmE,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,SAAS,KAAKC,SAAS,EAAE;MAC9B,IAAI,CAACD,SAAS,GAAG,IAAI,CAAC/C,YAAY,CAACiD,QAAQ,CAACC,GAAG,CAAChO,gBAAgB,EAAE,IAAI,CAAC;IAC3E;IACA,OAAO,IAAI,CAAC6N,SAAS;EACzB;EACA;AACJ;AACA;EACII,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,QAAQ,GAAG,IAAI,CAACN,YAAY,CAAC,CAAC;IACpC,IAAIM,QAAQ,IAAIA,QAAQ,CAACD,iBAAiB,EAAE;MACxC,OAAOC,QAAQ,CAACD,iBAAiB,CAAC,CAAC;IACvC;IACA,OAAO,IAAI,CAACR,UAAU,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;EACIU,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC3B,aAAa,CAAC4B,WAAW,CAAC,CAAC;IAChC,IAAI,CAACpC,WAAW,CAACC,iBAAiB,CAACM,MAAM,CAAC,IAAI,CAACzB,YAAY,CAACqB,QAAQ,CAAC;IACrE,IAAI,CAAC,IAAI,CAACkC,YAAY,EAAE;MACpB,IAAI,CAACvD,YAAY,CAACqD,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACE,YAAY,GAAG,IAAI;IAC5B;EACJ;AACJ;AAEA,MAAMhI,KAAK,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,IAAI;AACvD,MAAMgI,mBAAmB,GAAGjI,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACK,UAAU,CAAC,eAAe,CAAC,CAAC;AAC7E,MAAM6H,wCAAwC,GAAG;AACjD,wEAAwE;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,IAAIF,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACE,kBAAkB,CAAC,CAAC;EACnD;EACA,MAAM,IAAI1F,KAAK,CAACyF,wCAAwC,CAAC;AAC7D;AACA,SAASE,0BAA0BA,CAAA,EAAG;EAAA,IAAAC,mBAAA;EAClC,IAAIJ,mBAAmB,KAAAI,mBAAA,GAAIpI,IAAI,CAAC,eAAe,CAAC,cAAAoI,mBAAA,eAArBA,mBAAA,CAAuBC,QAAQ,CAAC,CAAC,EAAE;IAC1DL,mBAAmB,CAACE,kBAAkB,CAAC,CAAC;EAC5C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,SAASA,CAACxI,EAAE,EAAEyI,OAAO,EAAE;EAC5B,IAAIP,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACM,SAAS,CAACxI,EAAE,EAAEyI,OAAO,CAAC;EACrD;EACA,MAAM,IAAI/F,KAAK,CAACyF,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAStB,IAAIA,CAAC6B,MAAM,GAAG,CAAC,EAAEC,WAAW,GAAG;EACpCC,iCAAiC,EAAE;AACvC,CAAC,EAAE;EACC,IAAIV,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACrB,IAAI,CAAC6B,MAAM,EAAEC,WAAW,CAAC;EACxD;EACA,MAAM,IAAIjG,KAAK,CAACyF,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxB,KAAKA,CAACkC,QAAQ,EAAE;EACrB,IAAIX,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACvB,KAAK,CAACkC,QAAQ,CAAC;EAC9C;EACA,MAAM,IAAInG,KAAK,CAACyF,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,oBAAoBA,CAAA,EAAG;EAC5B,IAAIZ,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACY,oBAAoB,CAAC,CAAC;EACrD;EACA,MAAM,IAAIpG,KAAK,CAACyF,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,eAAeA,CAAA,EAAG;EACvB,IAAIb,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACa,eAAe,CAAC,CAAC;EAChD;EACA,MAAM,IAAIrG,KAAK,CAACyF,wCAAwC,CAAC;AAC7D;AAEA,IAAIa,gBAAgB,GAAG,CAAC;AACxB,MAAMC,iBAAiB,CAAC;EAAAxI,YAAA;IAAAC,eAAA,sBACN,IAAIwI,GAAG,CAAC,CAAC;EAAA;EACvB;AACJ;AACA;AACA;EACIC,gBAAgBA,CAACC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAE;IACnD,MAAMC,KAAK,GAAG,CAAC,CAAC;IAChB,IAAIF,WAAW,EAAE;MACbG,WAAW,CAACH,WAAW,CAAC,CAACI,OAAO,CAAEC,IAAI,IAAMH,KAAK,CAACG,IAAI,CAAC,GAAGL,WAAW,CAACK,IAAI,CAAE,CAAC;IACjF;IACA,IAAIJ,QAAQ,CAACK,GAAG,EAAE;MACd,IAAIL,QAAQ,CAACM,MAAM,IAAIN,QAAQ,CAACxD,GAAG,EAAE;QACjC,MAAM,IAAIpD,KAAK,CAAC,6BAA6B5I,UAAU,CAACsP,aAAa,CAAC,oBAAoB,CAAC;MAC/F;MACAS,WAAW,CAACN,KAAK,EAAED,QAAQ,CAACK,GAAG,CAAC;IACpC;IACA,IAAIL,QAAQ,CAACM,MAAM,EAAE;MACjBE,cAAc,CAACP,KAAK,EAAED,QAAQ,CAACM,MAAM,EAAE,IAAI,CAACG,WAAW,CAAC;IAC5D;IACA,IAAIT,QAAQ,CAACxD,GAAG,EAAE;MACdkE,WAAW,CAACT,KAAK,EAAED,QAAQ,CAACxD,GAAG,CAAC;IACpC;IACA,OAAO,IAAIsD,aAAa,CAACG,KAAK,CAAC;EACnC;AACJ;AACA,SAASO,cAAcA,CAACG,QAAQ,EAAEL,MAAM,EAAEM,UAAU,EAAE;EAClD,MAAMC,aAAa,GAAG,IAAIxJ,GAAG,CAAC,CAAC;EAC/B,KAAK,MAAM+I,IAAI,IAAIE,MAAM,EAAE;IACvB,MAAMQ,WAAW,GAAGR,MAAM,CAACF,IAAI,CAAC;IAChC,IAAIW,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;MAC5BA,WAAW,CAACX,OAAO,CAAErC,KAAK,IAAK;QAC3B+C,aAAa,CAACrE,GAAG,CAACyE,YAAY,CAACb,IAAI,EAAEtC,KAAK,EAAE8C,UAAU,CAAC,CAAC;MAC5D,CAAC,CAAC;IACN,CAAC,MACI;MACDC,aAAa,CAACrE,GAAG,CAACyE,YAAY,CAACb,IAAI,EAAEU,WAAW,EAAEF,UAAU,CAAC,CAAC;IAClE;EACJ;EACA,KAAK,MAAMR,IAAI,IAAIO,QAAQ,EAAE;IACzB,MAAMO,SAAS,GAAGP,QAAQ,CAACP,IAAI,CAAC;IAChC,IAAIW,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;MAC1BP,QAAQ,CAACP,IAAI,CAAC,GAAGc,SAAS,CAACC,MAAM,CAAErD,KAAK,IAAK,CAAC+C,aAAa,CAACO,GAAG,CAACH,YAAY,CAACb,IAAI,EAAEtC,KAAK,EAAE8C,UAAU,CAAC,CAAC,CAAC;IAC3G,CAAC,MACI;MACD,IAAIC,aAAa,CAACO,GAAG,CAACH,YAAY,CAACb,IAAI,EAAEc,SAAS,EAAEN,UAAU,CAAC,CAAC,EAAE;QAC9DD,QAAQ,CAACP,IAAI,CAAC,GAAGhC,SAAS;MAC9B;IACJ;EACJ;AACJ;AACA,SAASsC,WAAWA,CAACC,QAAQ,EAAEnE,GAAG,EAAE;EAChC,KAAK,MAAM4D,IAAI,IAAI5D,GAAG,EAAE;IACpB,MAAM6E,QAAQ,GAAG7E,GAAG,CAAC4D,IAAI,CAAC;IAC1B,MAAMc,SAAS,GAAGP,QAAQ,CAACP,IAAI,CAAC;IAChC,IAAIc,SAAS,IAAI,IAAI,IAAIH,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;MAC/CP,QAAQ,CAACP,IAAI,CAAC,GAAGc,SAAS,CAACI,MAAM,CAACD,QAAQ,CAAC;IAC/C,CAAC,MACI;MACDV,QAAQ,CAACP,IAAI,CAAC,GAAGiB,QAAQ;IAC7B;EACJ;AACJ;AACA,SAASd,WAAWA,CAACI,QAAQ,EAAEN,GAAG,EAAE;EAChC,KAAK,MAAMD,IAAI,IAAIC,GAAG,EAAE;IACpBM,QAAQ,CAACP,IAAI,CAAC,GAAGC,GAAG,CAACD,IAAI,CAAC;EAC9B;AACJ;AACA,SAASa,YAAYA,CAACM,QAAQ,EAAEL,SAAS,EAAEN,UAAU,EAAE;EACnD,IAAIY,YAAY,GAAG,CAAC;EACpB,MAAMC,SAAS,GAAG,IAAI7B,GAAG,CAAC,CAAC;EAC3B,MAAM8B,QAAQ,GAAGA,CAACC,GAAG,EAAE7D,KAAK,KAAK;IAC7B,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7C,IAAI2D,SAAS,CAACL,GAAG,CAACtD,KAAK,CAAC,EAAE;QACtB,OAAO2D,SAAS,CAACnD,GAAG,CAACR,KAAK,CAAC;MAC/B;MACA;MACA;MACA2D,SAAS,CAACpB,GAAG,CAACvC,KAAK,EAAE,QAAQ0D,YAAY,EAAE,EAAE,CAAC;MAC9C;MACA,OAAO1D,KAAK;IAChB,CAAC,MACI,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAClCA,KAAK,GAAG8D,mBAAmB,CAAC9D,KAAK,EAAE8C,UAAU,CAAC;IAClD;IACA,OAAO9C,KAAK;EAChB,CAAC;EACD,OAAO,GAAGyD,QAAQ,IAAIM,IAAI,CAACC,SAAS,CAACZ,SAAS,EAAEQ,QAAQ,CAAC,EAAE;AAC/D;AACA,SAASE,mBAAmBA,CAACG,GAAG,EAAEnB,UAAU,EAAE;EAC1C,IAAIoB,EAAE,GAAGpB,UAAU,CAACtC,GAAG,CAACyD,GAAG,CAAC;EAC5B,IAAI,CAACC,EAAE,EAAE;IACLA,EAAE,GAAG,GAAGxR,UAAU,CAACuR,GAAG,CAAC,GAAGrC,gBAAgB,EAAE,EAAE;IAC9CkB,UAAU,CAACP,GAAG,CAAC0B,GAAG,EAAEC,EAAE,CAAC;EAC3B;EACA,OAAOA,EAAE;AACb;AACA,SAAS9B,WAAWA,CAAC+B,GAAG,EAAE;EACtB,MAAMhC,KAAK,GAAG,EAAE;EAChB;EACAiC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC9B,OAAO,CAAEC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,CAACgC,UAAU,CAAC,GAAG,CAAC,EAAE;MACvBnC,KAAK,CAAChG,IAAI,CAACmG,IAAI,CAAC;IACpB;EACJ,CAAC,CAAC;EACF;EACA,IAAIiC,KAAK,GAAGJ,GAAG;EACf,OAAQI,KAAK,GAAGH,MAAM,CAACI,cAAc,CAACD,KAAK,CAAC,EAAG;IAC3CH,MAAM,CAACC,IAAI,CAACE,KAAK,CAAC,CAAClC,OAAO,CAAEoC,SAAS,IAAK;MACtC,MAAMC,IAAI,GAAGN,MAAM,CAACO,wBAAwB,CAACJ,KAAK,EAAEE,SAAS,CAAC;MAC9D,IAAI,CAACA,SAAS,CAACH,UAAU,CAAC,GAAG,CAAC,IAAII,IAAI,IAAI,KAAK,IAAIA,IAAI,EAAE;QACrDvC,KAAK,CAAChG,IAAI,CAACsI,SAAS,CAAC;MACzB;IACJ,CAAC,CAAC;EACN;EACA,OAAOtC,KAAK;AAChB;AAEA,MAAMyC,UAAU,GAAG,IAAI5R,uBAAuB,CAAC,CAAC;AAChD;AACA;AACA;AACA,MAAM6R,gBAAgB,CAAC;EAAAxL,YAAA;IAAAC,eAAA,oBACP,IAAIwI,GAAG,CAAC,CAAC;IAAAxI,eAAA,mBACV,IAAIwI,GAAG,CAAC,CAAC;EAAA;EACpBgD,WAAWA,CAACnK,IAAI,EAAEuH,QAAQ,EAAE;IACxB,MAAM6C,SAAS,GAAG,IAAI,CAACA,SAAS,CAACvE,GAAG,CAAC7F,IAAI,CAAC,IAAI,EAAE;IAChDoK,SAAS,CAAC5I,IAAI,CAAC+F,QAAQ,CAAC;IACxB,IAAI,CAAC6C,SAAS,CAACxC,GAAG,CAAC5H,IAAI,EAAEoK,SAAS,CAAC;IACnC,IAAI,CAACC,QAAQ,CAACjG,MAAM,CAACpE,IAAI,CAAC;EAC9B;EACAsK,YAAYA,CAACF,SAAS,EAAE;IACpB,IAAI,CAACA,SAAS,CAAC9K,KAAK,CAAC,CAAC;IACtB8K,SAAS,CAAC1C,OAAO,CAAC,CAAC,CAAC1H,IAAI,EAAEuH,QAAQ,CAAC,KAAK;MACpC,IAAI,CAAC4C,WAAW,CAACnK,IAAI,EAAEuH,QAAQ,CAAC;IACpC,CAAC,CAAC;EACN;EACAgD,aAAaA,CAACvK,IAAI,EAAE;IAChB,MAAMwK,WAAW,GAAGP,UAAU,CAACO,WAAW,CAACxK,IAAI,CAAC;IAChD;IACA;IACA;IACA;IACA;IACA,KAAK,IAAIyK,CAAC,GAAGD,WAAW,CAACjJ,MAAM,GAAG,CAAC,EAAEkJ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,MAAMC,UAAU,GAAGF,WAAW,CAACC,CAAC,CAAC;MACjC,MAAME,WAAW,GAAGD,UAAU,YAAYzS,SAAS,IAC/CyS,UAAU,YAAYxS,SAAS,IAC/BwS,UAAU,YAAY1S,IAAI,IAC1B0S,UAAU,YAAYvS,QAAQ;MAClC,IAAIwS,WAAW,EAAE;QACb,OAAOD,UAAU,YAAY,IAAI,CAAC1K,IAAI,GAAG0K,UAAU,GAAG,IAAI;MAC9D;IACJ;IACA,OAAO,IAAI;EACf;EACAjJ,OAAOA,CAACzB,IAAI,EAAE;IACV,IAAIqK,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACxE,GAAG,CAAC7F,IAAI,CAAC,IAAI,IAAI;IAC9C,IAAI,CAACqK,QAAQ,EAAE;MACXA,QAAQ,GAAG,IAAI,CAACE,aAAa,CAACvK,IAAI,CAAC;MACnC,IAAIqK,QAAQ,EAAE;QACV,MAAMD,SAAS,GAAG,IAAI,CAACA,SAAS,CAACvE,GAAG,CAAC7F,IAAI,CAAC;QAC1C,IAAIoK,SAAS,EAAE;UACX,MAAMQ,SAAS,GAAG,IAAI1D,iBAAiB,CAAC,CAAC;UACzCkD,SAAS,CAAC1C,OAAO,CAAEH,QAAQ,IAAK;YAC5B8C,QAAQ,GAAGO,SAAS,CAACxD,gBAAgB,CAAC,IAAI,CAACpH,IAAI,EAAEqK,QAAQ,EAAE9C,QAAQ,CAAC;UACxE,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAAC8C,QAAQ,CAACzC,GAAG,CAAC5H,IAAI,EAAEqK,QAAQ,CAAC;IACrC;IACA,OAAOA,QAAQ;EACnB;AACJ;AACA,MAAMQ,iBAAiB,SAASX,gBAAgB,CAAC;EAC7C,IAAIlK,IAAIA,CAAA,EAAG;IACP,OAAO/H,SAAS;EACpB;AACJ;AACA,MAAM6S,iBAAiB,SAASZ,gBAAgB,CAAC;EAC7C,IAAIlK,IAAIA,CAAA,EAAG;IACP,OAAO9H,SAAS;EACpB;AACJ;AACA,MAAM6S,YAAY,SAASb,gBAAgB,CAAC;EACxC,IAAIlK,IAAIA,CAAA,EAAG;IACP,OAAOhI,IAAI;EACf;AACJ;AACA,MAAMgT,gBAAgB,SAASd,gBAAgB,CAAC;EAC5C,IAAIlK,IAAIA,CAAA,EAAG;IACP,OAAO7H,QAAQ;EACnB;AACJ;AAEA,IAAI8S,qBAAqB;AACzB,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAACA,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC/EA,qBAAqB,CAACA,qBAAqB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;AAC/F,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,SAASC,uBAAuBA,CAAC7F,KAAK,EAAE;EACpC,OAAQA,KAAK,KAAK4F,qBAAqB,CAACE,WAAW,IAAI9F,KAAK,KAAK4F,qBAAqB,CAACG,iBAAiB;AAC5G;AACA,SAASC,4BAA4BA,CAACC,KAAK,EAAEC,QAAQ,EAAElI,QAAQ,EAAE;EAC7DiI,KAAK,CAAC5D,OAAO,CAAE1H,IAAI,IAAK;IACpB,IAAI,CAAChH,wBAAwB,CAACgH,IAAI,CAAC,EAAE;MACjC,MAAMwL,SAAS,GAAGD,QAAQ,CAAC9J,OAAO,CAACzB,IAAI,CAAC;MACxC,IAAIwL,SAAS,KAAKA,SAAS,CAACC,UAAU,IAAI,IAAI,IAAID,SAAS,CAACC,UAAU,CAAC,EAAE;QACrE,MAAM,IAAI9K,KAAK,CAACxE,sCAAsC,CAAC6D,IAAI,EAAEqD,QAAQ,CAAC,CAAC;MAC3E;IACJ;EACJ,CAAC,CAAC;AACN;AACA,MAAMqI,eAAe,CAAC;EAuDlBhN,WAAWA,CAACiN,QAAQ,EAAEC,qBAAqB,EAAE;IAAAjN,eAAA;IAAAA,eAAA;IAAAA,eAAA,2CApDV,IAAI;IACvC;IAAAA,eAAA,uBACe,EAAE;IAAAA,eAAA,kBACP,EAAE;IAAAA,eAAA,oBACA,EAAE;IAAAA,eAAA,kBACJ,EAAE;IACZ;IAAAA,eAAA,4BACoB,IAAIC,GAAG,CAAC,CAAC;IAAAD,eAAA,4BACT,IAAIC,GAAG,CAAC,CAAC;IAAAD,eAAA,uBACd,IAAIC,GAAG,CAAC,CAAC;IACxB;IACA;IAAAD,eAAA,sCAC8B,IAAIC,GAAG,CAAC,CAAC;IACvC;IAAAD,eAAA,yBACiB,IAAIC,GAAG,CAAC,CAAC;IAAAD,eAAA,yBACT,IAAIC,GAAG,CAAC,CAAC;IAC1B;IAAAD,eAAA,4BACoB,IAAIC,GAAG,CAAC,CAAC;IAC7B;IACA;IAAAD,eAAA,kCAC0B,IAAIwI,GAAG,CAAC,CAAC;IAAAxI,eAAA,oBACvBkN,aAAa,CAAC,CAAC;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IAAAlN,eAAA,iCACyB,IAAIwI,GAAG,CAAC,CAAC;IAClC;IACA;IACA;IACA;IACA;IAAAxI,eAAA,wBACgB,IAAIwI,GAAG,CAAC,CAAC;IACzB;IACA;IAAAxI,eAAA,wBACgB,EAAE;IAAAA,eAAA,oBACN,IAAI;IAAAA,eAAA,4BACI,IAAI;IAAAA,eAAA,4BACJ,EAAE;IAAAA,eAAA,gCACE,EAAE;IAC1B;IACA;IAAAA,eAAA,oCAC4B,IAAIwI,GAAG,CAAC,CAAC;IAAAxI,eAAA,mCACV,IAAIwI,GAAG,CAAC,CAAC;IAAAxI,eAAA,wCACJ,IAAIC,GAAG,CAAC,CAAC;IAAAD,eAAA;IAAAA,eAAA,wBAEzB,IAAI;IAAAA,eAAA,6BACCuD,4BAA4B;IAAAvD,eAAA,uCAClBH,kCAAkC;IAE7D,IAAI,CAACmN,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,MAAME,iBAAiB,CAAC;IAExB,IAAI,CAACC,cAAc,GAAGD,iBAAiB;EAC3C;EACAE,oBAAoBA,CAACC,SAAS,EAAE;IAC5B,IAAI,CAACC,iBAAiB,GAAGD,SAAS;IAClC,IAAI,CAACE,SAAS,GAAG,IAAI;EACzB;EACAC,sBAAsBA,CAACC,SAAS,EAAE;IAAA,IAAAC,qBAAA,EAAAC,qBAAA;IAC9B;IACA,IAAIF,SAAS,CAACG,YAAY,KAAK7G,SAAS,EAAE;MACtC;MACA0F,4BAA4B,CAACgB,SAAS,CAACG,YAAY,EAAE,IAAI,CAACC,SAAS,CAACjB,SAAS,EAAE,uCAAuC,CAAC;MACvH,IAAI,CAACkB,cAAc,CAACL,SAAS,CAACG,YAAY,EAAEvB,qBAAqB,CAACE,WAAW,CAAC;MAC9E,IAAI,CAACqB,YAAY,CAAChL,IAAI,CAAC,GAAG6K,SAAS,CAACG,YAAY,CAAC;IACrD;IACA;IACA,IAAIH,SAAS,CAACM,OAAO,KAAKhH,SAAS,EAAE;MACjC,IAAI,CAACiH,0BAA0B,CAACP,SAAS,CAACM,OAAO,CAAC;MAClD,IAAI,CAACA,OAAO,CAACnL,IAAI,CAAC,GAAG6K,SAAS,CAACM,OAAO,CAAC;IAC3C;IACA,IAAIN,SAAS,CAACJ,SAAS,KAAKtG,SAAS,EAAE;MACnC,IAAI,CAACsG,SAAS,CAACzK,IAAI,CAAC,GAAG6K,SAAS,CAACJ,SAAS,CAAC;IAC/C;IACA,IAAII,SAAS,CAACQ,OAAO,KAAKlH,SAAS,EAAE;MACjC,IAAI,CAACkH,OAAO,CAACrL,IAAI,CAAC,GAAG6K,SAAS,CAACQ,OAAO,CAAC;IAC3C;IACA,IAAI,CAACC,kBAAkB,IAAAR,qBAAA,GAAGD,SAAS,CAACS,kBAAkB,cAAAR,qBAAA,cAAAA,qBAAA,GAAIpK,4BAA4B;IACtF,IAAI,CAAC6K,4BAA4B,IAAAR,qBAAA,GAC7BF,SAAS,CAACW,wBAAwB,cAAAT,qBAAA,cAAAA,qBAAA,GAAI/N,kCAAkC;EAChF;EACAyO,cAAcA,CAACC,QAAQ,EAAE3F,QAAQ,EAAE;IAC/B,IAAIhP,iCAAiC,EAAE;MACnCE,YAAY,CAAC0U,kBAAkB,CAACD,QAAQ,CAAC;IAC7C;IACA,IAAI,CAACE,iBAAiB,CAACrJ,GAAG,CAACmJ,QAAQ,CAAC;IACpC;IACA,IAAI,CAACT,SAAS,CAACY,MAAM,CAAClD,WAAW,CAAC+C,QAAQ,EAAE3F,QAAQ,CAAC;IACrD,MAAMW,QAAQ,GAAG,IAAI,CAACuE,SAAS,CAACY,MAAM,CAAC5L,OAAO,CAACyL,QAAQ,CAAC;IACxD,IAAIhF,QAAQ,KAAK,IAAI,EAAE;MACnB,MAAMoF,gBAAgB,CAACJ,QAAQ,CAACK,IAAI,EAAE,UAAU,CAAC;IACrD;IACA,IAAI,CAACC,iBAAiB,CAACN,QAAQ,EAAEhF,QAAQ,CAAC;IAC1C;IACA;IACA;IACA,IAAI,CAAC0E,0BAA0B,CAAC,CAACM,QAAQ,CAAC,CAAC;EAC/C;EACAO,iBAAiBA,CAACjC,SAAS,EAAEjE,QAAQ,EAAE;IACnC,IAAI,CAACmG,+BAA+B,CAAClC,SAAS,EAAEjE,QAAQ,CAAC;IACzD,IAAI,CAACkF,SAAS,CAACjB,SAAS,CAACrB,WAAW,CAACqB,SAAS,EAAEjE,QAAQ,CAAC;IACzD,IAAI,CAACoG,iBAAiB,CAAC5J,GAAG,CAACyH,SAAS,CAAC;IACrC;IACA;IACA,IAAI,CAACoC,uCAAuC,CAACpC,SAAS,CAAC;EAC3D;EACAqC,iBAAiBA,CAACC,SAAS,EAAEvG,QAAQ,EAAE;IACnC,IAAI,CAACmG,+BAA+B,CAACI,SAAS,EAAEvG,QAAQ,CAAC;IACzD,IAAI,CAACkF,SAAS,CAACqB,SAAS,CAAC3D,WAAW,CAAC2D,SAAS,EAAEvG,QAAQ,CAAC;IACzD,IAAI,CAACwG,iBAAiB,CAAChK,GAAG,CAAC+J,SAAS,CAAC;EACzC;EACAE,YAAYA,CAACC,IAAI,EAAE1G,QAAQ,EAAE;IACzB,IAAI,CAACmG,+BAA+B,CAACO,IAAI,EAAE1G,QAAQ,CAAC;IACpD,IAAI,CAACkF,SAAS,CAACwB,IAAI,CAAC9D,WAAW,CAAC8D,IAAI,EAAE1G,QAAQ,CAAC;IAC/C,IAAI,CAAC2G,YAAY,CAACnK,GAAG,CAACkK,IAAI,CAAC;EAC/B;EACAP,+BAA+BA,CAAC1N,IAAI,EAAEuH,QAAQ,EAAE;IAAA,IAAA4G,aAAA,EAAAC,aAAA,EAAAC,gBAAA;IAC5C,IAAI,CAAAF,aAAA,GAAA5G,QAAQ,CAACxD,GAAG,cAAAoK,aAAA,eAAZA,aAAA,CAAcG,cAAc,CAAC,YAAY,CAAC,KAAAF,aAAA,GAC1C7G,QAAQ,CAACK,GAAG,cAAAwG,aAAA,eAAZA,aAAA,CAAcE,cAAc,CAAC,YAAY,CAAC,KAAAD,gBAAA,GAC1C9G,QAAQ,CAACM,MAAM,cAAAwG,gBAAA,eAAfA,gBAAA,CAAiBC,cAAc,CAAC,YAAY,CAAC,EAAE;MAC/C,MAAM,IAAI3N,KAAK,CAAC,uBAAuBX,IAAI,CAACuN,IAAI,sCAAsC,GAClF,0EAA0E,CAAC;IACnF;EACJ;EACAgB,gBAAgBA,CAAC5O,KAAK,EAAE6O,QAAQ,EAAE;IAC9B,IAAIC,WAAW;IACf,IAAID,QAAQ,CAACE,UAAU,KAAK/I,SAAS,EAAE;MACnC8I,WAAW,GAAG;QACVE,OAAO,EAAEhP,KAAK;QACd+O,UAAU,EAAEF,QAAQ,CAACE,UAAU;QAC/BE,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI,EAAE;QACzBC,KAAK,EAAEL,QAAQ,CAACK;MACpB,CAAC;IACL,CAAC,MACI,IAAIL,QAAQ,CAACM,QAAQ,KAAKnJ,SAAS,EAAE;MACtC8I,WAAW,GAAG;QAAEE,OAAO,EAAEhP,KAAK;QAAEmP,QAAQ,EAAEN,QAAQ,CAACM,QAAQ;QAAED,KAAK,EAAEL,QAAQ,CAACK;MAAM,CAAC;IACxF,CAAC,MACI;MACDJ,WAAW,GAAG;QAAEE,OAAO,EAAEhP;MAAM,CAAC;IACpC;IACA,MAAMoP,aAAa,GAAG,OAAOpP,KAAK,KAAK,QAAQ,GAAGhH,iBAAiB,CAACgH,KAAK,CAAC,GAAG,IAAI;IACjF,MAAMqP,UAAU,GAAGD,aAAa,KAAK,IAAI,GAAG,IAAI,GAAGnW,iBAAiB,CAACmW,aAAa,CAACC,UAAU,CAAC;IAC9F,MAAMC,eAAe,GAAGD,UAAU,KAAK,MAAM,GAAG,IAAI,CAACE,qBAAqB,GAAG,IAAI,CAACC,iBAAiB;IACnGF,eAAe,CAACzN,IAAI,CAACiN,WAAW,CAAC;IACjC;IACA,IAAI,CAACW,wBAAwB,CAACxH,GAAG,CAACjI,KAAK,EAAE8O,WAAW,CAAC;IACrD,IAAIM,aAAa,KAAK,IAAI,IAAIC,UAAU,KAAK,IAAI,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MACjF,MAAMK,iBAAiB,GAAG,IAAI,CAACC,yBAAyB,CAACzJ,GAAG,CAACmJ,UAAU,CAAC;MACxE,IAAIK,iBAAiB,KAAK1J,SAAS,EAAE;QACjC0J,iBAAiB,CAAC7N,IAAI,CAACiN,WAAW,CAAC;MACvC,CAAC,MACI;QACD,IAAI,CAACa,yBAAyB,CAAC1H,GAAG,CAACoH,UAAU,EAAE,CAACP,WAAW,CAAC,CAAC;MACjE;IACJ;EACJ;EACAc,kCAAkCA,CAACvP,IAAI,EAAEwP,QAAQ,EAAE;IAC/C,MAAMC,GAAG,GAAGzP,IAAI,CAAClG,YAAY,CAAC;IAC9B,MAAM4V,YAAY,GAAGA,CAAA,KAAM;MAAA,IAAAC,mBAAA;MACvB,MAAMzH,QAAQ,GAAG,IAAI,CAACuE,SAAS,CAACjB,SAAS,CAAC/J,OAAO,CAACzB,IAAI,CAAC;MACvD,OAAO,CAAC,CAACkI,QAAQ,CAAC0H,QAAQ,IAAI,CAAC,GAAAD,mBAAA,GAACzH,QAAQ,CAAC2H,SAAS,cAAAF,mBAAA,eAAlBA,mBAAA,CAAoBpO,MAAM;IAC9D,CAAC;IACD,MAAMuO,iBAAiB,GAAG,CAAC,CAACL,GAAG,IAAI,CAAC3W,gCAAgC,CAACkH,IAAI,CAAC,IAAI0P,YAAY,CAAC,CAAC;IAC5F;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMnI,QAAQ,GAAGuI,iBAAiB,GAC5B;MAAEN,QAAQ;MAAEO,MAAM,EAAE,EAAE;MAAEF,SAAS,EAAE,EAAE;MAAED,QAAQ,EAAEjK;IAAU,CAAC,GAC5D;MAAE6J;IAAS,CAAC;IAClB,IAAI,CAAC/B,iBAAiB,CAACzN,IAAI,EAAE;MAAE4H,GAAG,EAAEL;IAAS,CAAC,CAAC;IAC/C,IAAIuI,iBAAiB,IAAIL,GAAG,CAACM,MAAM,IAAIN,GAAG,CAACM,MAAM,CAACxO,MAAM,GAAG,CAAC,EAAE;MAC1D,IAAI,CAACyO,uBAAuB,CAACpI,GAAG,CAAC5H,IAAI,EAAEyP,GAAG,CAACM,MAAM,CAAC;IACtD;IACA;IACA,IAAI,CAACE,sBAAsB,CAACrI,GAAG,CAAC5H,IAAI,EAAEiL,qBAAqB,CAACG,iBAAiB,CAAC;EAClF;EACM8E,yCAAyCA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA5P,iBAAA;MAC9C,IAAI4P,MAAI,CAACC,2BAA2B,CAAChR,IAAI,KAAK,CAAC,EAC3C;MACJ,MAAMiR,QAAQ,GAAG,EAAE;MACnB,KAAK,MAAM7E,SAAS,IAAI2E,MAAI,CAACC,2BAA2B,EAAE;QACtD,MAAME,eAAe,GAAGtX,wBAAwB,CAACwS,SAAS,CAAC;QAC3D,IAAI8E,eAAe,EAAE;UACjBD,QAAQ,CAAC7O,IAAI,CAAC8O,eAAe,CAAC,CAAC,CAAC;QACpC;MACJ;MACAH,MAAI,CAACC,2BAA2B,CAAC9Q,KAAK,CAAC,CAAC;MACxC,MAAMiR,YAAY,SAASnS,OAAO,CAACoS,GAAG,CAACH,QAAQ,CAAC;MAChD,MAAMI,gBAAgB,GAAGF,YAAY,CAACG,IAAI,CAAC,CAAC,CAAC;MAC7CP,MAAI,CAACvD,0BAA0B,CAAC6D,gBAAgB,CAAC;MACjD;MACA;MACA,KAAK,MAAMjF,SAAS,IAAIiF,gBAAgB,EAAE;QACtCN,MAAI,CAACQ,6BAA6B,CAACnF,SAAS,CAAC;MACjD;IAAC;EACL;EACMoF,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAtQ,iBAAA;MACtBsQ,MAAI,CAACC,6BAA6B,CAAC,CAAC;MACpC;MACA;MACA;MACA,MAAMD,MAAI,CAACX,yCAAyC,CAAC,CAAC;MACtD;MACA;MACA;MACA;MACA7E,4BAA4B,CAACwF,MAAI,CAACrE,YAAY,EAAEqE,MAAI,CAACpE,SAAS,CAACjB,SAAS,EAAE,uCAAuC,CAAC;MAClH;MACA,IAAIuF,mBAAmB,GAAGF,MAAI,CAACG,gBAAgB,CAAC,CAAC;MACjD;MACA,IAAID,mBAAmB,EAAE;QACrB,IAAIE,cAAc;QAClB,IAAI1F,QAAQ,GAAI2F,GAAG,IAAK;UACpB,IAAI,CAACD,cAAc,EAAE;YACjBA,cAAc,GAAGJ,MAAI,CAACjL,QAAQ,CAACC,GAAG,CAAC9H,cAAc,CAAC;UACtD;UACA,OAAOK,OAAO,CAACqD,OAAO,CAACwP,cAAc,CAACpL,GAAG,CAACqL,GAAG,CAAC,CAAC;QACnD,CAAC;QACD,MAAMhY,0BAA0B,CAACqS,QAAQ,CAAC;MAC9C;IAAC;EACL;EACA4F,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAACH,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAACI,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACC,iCAAiC,CAAC,CAAC;IACxC;IACA;IACA,IAAI,CAACtB,sBAAsB,CAAC3Q,KAAK,CAAC,CAAC;IACnC,MAAMkS,cAAc,GAAG,IAAI,CAAC7F,QAAQ,CAAC/F,QAAQ;IAC7C,IAAI,CAAC6L,aAAa,GAAG,IAAIrY,mBAAmB,CAAC,IAAI,CAAC2S,cAAc,EAAEyF,cAAc,EAAE,EAAE,CAAC;IACrF;IACA;IACA,IAAI,CAACC,aAAa,CAAC7L,QAAQ,CAACC,GAAG,CAACxM,qBAAqB,CAAC,CAACqY,eAAe,CAAC,CAAC;IACxE;IACA;IACA;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACF,aAAa,CAAC7L,QAAQ,CAACC,GAAG,CAACvM,SAAS,EAAEE,kBAAkB,CAAC;IAC/EE,YAAY,CAACiY,QAAQ,CAAC;IACtB,OAAO,IAAI,CAACF,aAAa;EAC7B;EACA;AACJ;AACA;EACIG,oBAAoBA,CAACC,UAAU,EAAE;IAC7B,IAAI,CAACjF,0BAA0B,CAAC,CAACiF,UAAU,CAAC,CAAC;IAC7C,IAAI,CAACb,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACM,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACX,6BAA6B,CAACkB,UAAU,CAAC;IAC9C,IAAI,CAACR,qBAAqB,CAAC,CAAC;EAChC;EACA;AACJ;AACA;EACUS,qBAAqBA,CAACD,UAAU,EAAE;IAAA,IAAAE,MAAA;IAAA,OAAAxR,iBAAA;MACpCwR,MAAI,CAACnF,0BAA0B,CAAC,CAACiF,UAAU,CAAC,CAAC;MAC7C,MAAME,MAAI,CAACnB,iBAAiB,CAAC,CAAC;MAC9BmB,MAAI,CAACT,sBAAsB,CAAC,CAAC;MAC7BS,MAAI,CAACpB,6BAA6B,CAACkB,UAAU,CAAC;MAC9CE,MAAI,CAACV,qBAAqB,CAAC,CAAC;IAAC;EACjC;EACA;AACJ;AACA;EACIW,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACvF,SAAS,CAACY,MAAM;EAChC;EACA;AACJ;AACA;EACI4E,sBAAsBA,CAACJ,UAAU,EAAE;IAC/B,OAAOK,aAAa,CAACL,UAAU,CAACM,IAAI,CAAC3F,YAAY,CAAC,CAAC4F,MAAM,CAAC,CAACC,SAAS,EAAEC,WAAW,KAAK;MAClF,MAAMC,YAAY,GAAGD,WAAW,CAACE,IAAI;MACrCD,YAAY,IAAIF,SAAS,CAAC7Q,IAAI,CAAC,IAAI5H,wBAAwB,CAAC2Y,YAAY,EAAE,IAAI,CAACd,aAAa,CAAC,CAAC;MAC9F,OAAOY,SAAS;IACpB,CAAC,EAAE,EAAE,CAAC;EACV;EACArB,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAID,mBAAmB,GAAG,KAAK;IAC/B,IAAI,CAACpD,iBAAiB,CAACjG,OAAO,CAAE4K,WAAW,IAAK;MAC5C,IAAItZ,wBAAwB,CAACsZ,WAAW,CAAC,EAAE;QACvC,MAAM,IAAI3R,KAAK,CAAC,cAAc2R,WAAW,CAAC/E,IAAI,6BAA6B,GACvE,6EAA6E,CAAC;MACtF;MACAwD,mBAAmB,GAAGA,mBAAmB,IAAIjY,gCAAgC,CAACwZ,WAAW,CAAC;MAC1F,MAAMpK,QAAQ,GAAG,IAAI,CAACuE,SAAS,CAACjB,SAAS,CAAC/J,OAAO,CAAC6Q,WAAW,CAAC;MAC9D,IAAIpK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMoF,gBAAgB,CAACgF,WAAW,CAAC/E,IAAI,EAAE,WAAW,CAAC;MACzD;MACA,IAAI,CAACkF,eAAe,CAAC3Y,YAAY,EAAEwY,WAAW,CAAC;MAC/C,IAAI/Z,iCAAiC,EAAE;QACnCE,YAAY,CAAC0U,kBAAkB,CAACmF,WAAW,CAAC;MAChD;MACAtY,iBAAiB,CAACsY,WAAW,EAAEpK,QAAQ,CAAC;IAC5C,CAAC,CAAC;IACF,IAAI,CAACyF,iBAAiB,CAACrO,KAAK,CAAC,CAAC;IAC9B,IAAI,CAACyO,iBAAiB,CAACrG,OAAO,CAAE4K,WAAW,IAAK;MAC5C,MAAMpK,QAAQ,GAAG,IAAI,CAACuE,SAAS,CAACqB,SAAS,CAACrM,OAAO,CAAC6Q,WAAW,CAAC;MAC9D,IAAIpK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMoF,gBAAgB,CAACgF,WAAW,CAAC/E,IAAI,EAAE,WAAW,CAAC;MACzD;MACA,IAAI,CAACkF,eAAe,CAACvY,WAAW,EAAEoY,WAAW,CAAC;MAC9ClY,iBAAiB,CAACkY,WAAW,EAAEpK,QAAQ,CAAC;IAC5C,CAAC,CAAC;IACF,IAAI,CAAC6F,iBAAiB,CAACzO,KAAK,CAAC,CAAC;IAC9B,IAAI,CAAC4O,YAAY,CAACxG,OAAO,CAAE4K,WAAW,IAAK;MACvC,MAAMpK,QAAQ,GAAG,IAAI,CAACuE,SAAS,CAACwB,IAAI,CAACxM,OAAO,CAAC6Q,WAAW,CAAC;MACzD,IAAIpK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMoF,gBAAgB,CAACgF,WAAW,CAAC/E,IAAI,EAAE,MAAM,CAAC;MACpD;MACA,IAAI,CAACkF,eAAe,CAACnY,YAAY,EAAEgY,WAAW,CAAC;MAC/C9X,YAAY,CAAC8X,WAAW,EAAEpK,QAAQ,CAAC;IACvC,CAAC,CAAC;IACF,IAAI,CAACgG,YAAY,CAAC5O,KAAK,CAAC,CAAC;IACzB,OAAOyR,mBAAmB;EAC9B;EACAM,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACjE,iBAAiB,CAAChO,IAAI,GAAG,CAAC,EAAE;MACjC;MACA;MACA;MACA,MAAMsT,gBAAgB,GAAG,IAAI,CAAC3G,cAAc,CAACrR,WAAW,CAAC;MACzD,MAAMiY,eAAe,GAAG,IAAI,CAACC,iCAAiC,CAACF,gBAAgB,CAAC/F,OAAO,CAAC;MACxF,IAAIgG,eAAe,CAACvT,IAAI,GAAG,CAAC,EAAE;QAC1BuT,eAAe,CAACjL,OAAO,CAAEmK,UAAU,IAAK;UACpC,IAAI,CAACtZ,iCAAiC,EAAE;YACpC,IAAI,CAACsa,qBAAqB,CAAChB,UAAU,EAAEnX,WAAW,EAAE,yBAAyB,CAAC;YAC9EmX,UAAU,CAACnX,WAAW,CAAC,CAACoY,uBAAuB,GAAG,IAAI;UAC1D,CAAC,MACI;YACDra,YAAY,CAAC0U,kBAAkB,CAAC0E,UAAU,CAAC;UAC/C;QACJ,CAAC,CAAC;MACN;IACJ;IACA,MAAMkB,aAAa,GAAG,IAAI5L,GAAG,CAAC,CAAC;IAC/B,MAAM6L,gBAAgB,GAAInB,UAAU,IAAK;MACrC,IAAI,CAACkB,aAAa,CAACpK,GAAG,CAACkJ,UAAU,CAAC,EAAE;QAChC,MAAMoB,eAAe,GAAG/H,uBAAuB,CAAC2G,UAAU,CAAC;QAC3D,MAAMqB,QAAQ,GAAGD,eAAe,GAAG,IAAI,CAAClH,cAAc,GAAG8F,UAAU;QACnEkB,aAAa,CAACnL,GAAG,CAACiK,UAAU,EAAE5V,oBAAoB,CAACiX,QAAQ,CAAC,CAAC;MACjE;MACA,OAAOH,aAAa,CAAClN,GAAG,CAACgM,UAAU,CAAC;IACxC,CAAC;IACD,IAAI,CAAC5B,sBAAsB,CAACvI,OAAO,CAAC,CAACmK,UAAU,EAAEsB,aAAa,KAAK;MAC/D,IAAItB,UAAU,KAAK,IAAI,EAAE;QACrB,MAAMuB,WAAW,GAAGJ,gBAAgB,CAACnB,UAAU,CAAC;QAChD,IAAI,CAACgB,qBAAqB,CAACM,aAAa,EAAErZ,YAAY,EAAE,eAAe,CAAC;QACxE,IAAI,CAAC+Y,qBAAqB,CAACM,aAAa,EAAErZ,YAAY,EAAE,UAAU,CAAC;QACnEc,2BAA2B,CAACyY,eAAe,CAACF,aAAa,CAAC,EAAEC,WAAW,CAAC;MAC5E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACP,qBAAqB,CAACM,aAAa,EAAErZ,YAAY,EAAE,OAAO,CAAC;IACpE,CAAC,CAAC;IACF,IAAI,CAACmW,sBAAsB,CAAC3Q,KAAK,CAAC,CAAC;EACvC;EACAgS,sBAAsBA,CAAA,EAAG;IACrB,MAAMgC,mBAAmB,GAAIC,KAAK,IAAMvT,IAAI,IAAK;MAC7C,MAAMuL,QAAQ,GAAGgI,KAAK,KAAKzZ,YAAY,GAAG,IAAI,CAAC2S,SAAS,CAACjB,SAAS,GAAG,IAAI,CAACiB,SAAS,CAACqB,SAAS;MAC7F,MAAM5F,QAAQ,GAAGqD,QAAQ,CAAC9J,OAAO,CAACzB,IAAI,CAAC;MACvC,IAAI,IAAI,CAACwT,oBAAoB,CAACtL,QAAQ,CAAC+D,SAAS,CAAC,EAAE;QAC/C,IAAI,CAACwH,6BAA6B,CAACzT,IAAI,EAAEuT,KAAK,CAAC;MACnD;IACJ,CAAC;IACD,IAAI,CAACG,cAAc,CAAChM,OAAO,CAAC4L,mBAAmB,CAACxZ,YAAY,CAAC,CAAC;IAC9D,IAAI,CAAC6Z,cAAc,CAACjM,OAAO,CAAC4L,mBAAmB,CAACpZ,WAAW,CAAC,CAAC;IAC7D,IAAI,CAACwZ,cAAc,CAACpU,KAAK,CAAC,CAAC;IAC3B,IAAI,CAACqU,cAAc,CAACrU,KAAK,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIqR,6BAA6BA,CAAC3Q,IAAI,EAAE;IAChC,MAAM4T,QAAQ,GAAGC,qBAAqB,CAAC7T,IAAI,CAAC,IAAI8T,UAAU,CAAC9T,IAAI,CAAC;IAChE;IACA;IACA;IACA;IACA,IAAI,CAAC4T,QAAQ,IAAI,IAAI,CAACG,6BAA6B,CAACpL,GAAG,CAAC3I,IAAI,CAAC,EAAE;MAC3D;IACJ;IACA,IAAI,CAAC+T,6BAA6B,CAAChQ,GAAG,CAAC/D,IAAI,CAAC;IAC5C;IACA;IACA;IACA;IACA;IACA,MAAMgU,WAAW,GAAGhU,IAAI,CAAClF,WAAW,CAAC;IACrC;IACA,IAAI,IAAI,CAACsU,wBAAwB,CAAChQ,IAAI,KAAK,CAAC,EACxC;IACJ,IAAIyU,qBAAqB,CAAC7T,IAAI,CAAC,EAAE;MAAA,IAAAiU,iBAAA;MAC7B;MACA,MAAMxE,GAAG,GAAG4D,eAAe,CAACrT,IAAI,CAAC;MACjC,MAAMkU,YAAY,GAAGhC,aAAa,EAAA+B,iBAAA,GAACxE,GAAG,CAACyE,YAAY,cAAAD,iBAAA,cAAAA,iBAAA,GAAI,EAAE,CAAC;MAC1D,KAAK,MAAME,UAAU,IAAID,YAAY,EAAE;QACnC,IAAI,CAACvD,6BAA6B,CAACwD,UAAU,CAAC;MAClD;IACJ,CAAC,MACI;MACD,MAAMlI,SAAS,GAAG,CACd,GAAG+H,WAAW,CAAC/H,SAAS,EACxB,IAAI,IAAI,CAACqD,yBAAyB,CAACzJ,GAAG,CAAC7F,IAAI,CAAC,IAAI,EAAE,CAAC,CACtD;MACD,IAAI,IAAI,CAACwT,oBAAoB,CAACvH,SAAS,CAAC,EAAE;QACtC,IAAI,CAACwG,eAAe,CAAC3X,WAAW,EAAEkF,IAAI,CAAC;QACvC,IAAI,CAAC6S,qBAAqB,CAAC7S,IAAI,EAAElF,WAAW,EAAE,WAAW,CAAC;QAC1DkZ,WAAW,CAAC/H,SAAS,GAAG,IAAI,CAACmI,sBAAsB,CAACnI,SAAS,CAAC;MAClE;MACA;MACA,MAAMI,SAAS,GAAGrM,IAAI,CAACtF,WAAW,CAAC;MACnC,MAAMiS,OAAO,GAAGuF,aAAa,CAAC7F,SAAS,CAACM,OAAO,CAAC;MAChD,KAAK,MAAM0H,cAAc,IAAI1H,OAAO,EAAE;QAClC,IAAI,CAACgE,6BAA6B,CAAC0D,cAAc,CAAC;MACtD;MACA;MACA;MACA,KAAK,MAAMA,cAAc,IAAIC,OAAO,CAACN,WAAW,CAACrH,OAAO,CAAC,EAAE;QACvD,IAAI4H,qBAAqB,CAACF,cAAc,CAAC,EAAE;UACvC,IAAI,CAACG,aAAa,CAAChT,IAAI,CAAC;YACpBiT,MAAM,EAAEJ,cAAc;YACtBK,SAAS,EAAE,WAAW;YACtBC,aAAa,EAAEN,cAAc,CAACpI;UAClC,CAAC,CAAC;UACFoI,cAAc,CAACpI,SAAS,GAAG,IAAI,CAACmI,sBAAsB,CAACC,cAAc,CAACpI,SAAS,CAAC;QACpF;MACJ;IACJ;EACJ;EACAsF,iCAAiCA,CAAA,EAAG;IAChC,IAAI,CAACvB,uBAAuB,CAACtI,OAAO,CAAC,CAACqI,MAAM,EAAE/P,IAAI,KAAMA,IAAI,CAAClG,YAAY,CAAC,CAACiW,MAAM,GAAGA,MAAO,CAAC;IAC5F,IAAI,CAACC,uBAAuB,CAAC1Q,KAAK,CAAC,CAAC;EACxC;EACAoN,cAAcA,CAACkI,GAAG,EAAE/C,UAAU,EAAE;IAC5B,KAAK,MAAMxM,KAAK,IAAIuP,GAAG,EAAE;MACrB,IAAItM,KAAK,CAACC,OAAO,CAAClD,KAAK,CAAC,EAAE;QACtB,IAAI,CAACqH,cAAc,CAACrH,KAAK,EAAEwM,UAAU,CAAC;MAC1C,CAAC,MACI;QACD,IAAI,CAACgD,SAAS,CAACxP,KAAK,EAAEwM,UAAU,CAAC;MACrC;IACJ;EACJ;EACArE,iBAAiBA,CAACN,QAAQ,EAAEhF,QAAQ,EAAE;IAClC;IACA,IAAI,CAACuK,eAAe,CAAC/X,WAAW,EAAEwS,QAAQ,CAAC;IAC3C,IAAI,CAACuF,eAAe,CAAC3X,WAAW,EAAEoS,QAAQ,CAAC;IAC3ClS,oBAAoB,CAACkS,QAAQ,EAAEhF,QAAQ,CAAC;EAC5C;EACA0F,uCAAuCA,CAAC5N,IAAI,EAAE;IAC1C,MAAMsQ,eAAe,GAAGtX,wBAAwB,CAACgH,IAAI,CAAC;IACtD,IAAIsQ,eAAe,EAAE;MACjB,IAAI,CAACF,2BAA2B,CAACrM,GAAG,CAAC/D,IAAI,CAAC;IAC9C;EACJ;EACA6U,SAASA,CAAC7U,IAAI,EAAE6R,UAAU,EAAE;IACxB;IACA;IACA,IAAI,CAACjE,uCAAuC,CAAC5N,IAAI,CAAC;IAClD,MAAMwL,SAAS,GAAG,IAAI,CAACiB,SAAS,CAACjB,SAAS,CAAC/J,OAAO,CAACzB,IAAI,CAAC;IACxD,IAAIwL,SAAS,EAAE;MACX;MACA;MACA;MACA,IAAI1S,gCAAgC,CAACkH,IAAI,CAAC,IAAI,CAACA,IAAI,CAACsO,cAAc,CAACxU,YAAY,CAAC,EAAE;QAC9E,IAAI,CAAC6T,iBAAiB,CAAC5J,GAAG,CAAC/D,IAAI,CAAC;MACpC;MACA,IAAI,CAAC0T,cAAc,CAAC3P,GAAG,CAAC/D,IAAI,CAAC;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACiQ,sBAAsB,CAACtH,GAAG,CAAC3I,IAAI,CAAC,IACtC,IAAI,CAACiQ,sBAAsB,CAACpK,GAAG,CAAC7F,IAAI,CAAC,KAAKiL,qBAAqB,CAACE,WAAW,EAAE;QAC7E,IAAI,CAAC8E,sBAAsB,CAACrI,GAAG,CAAC5H,IAAI,EAAE6R,UAAU,CAAC;MACrD;MACA;IACJ;IACA,MAAM/D,SAAS,GAAG,IAAI,CAACrB,SAAS,CAACqB,SAAS,CAACrM,OAAO,CAACzB,IAAI,CAAC;IACxD,IAAI8N,SAAS,EAAE;MACX,IAAI,CAAC9N,IAAI,CAACsO,cAAc,CAACpU,WAAW,CAAC,EAAE;QACnC,IAAI,CAAC6T,iBAAiB,CAAChK,GAAG,CAAC/D,IAAI,CAAC;MACpC;MACA,IAAI,CAAC2T,cAAc,CAAC5P,GAAG,CAAC/D,IAAI,CAAC;MAC7B;IACJ;IACA,MAAMiO,IAAI,GAAG,IAAI,CAACxB,SAAS,CAACwB,IAAI,CAACxM,OAAO,CAACzB,IAAI,CAAC;IAC9C,IAAIiO,IAAI,IAAI,CAACjO,IAAI,CAACsO,cAAc,CAAChU,YAAY,CAAC,EAAE;MAC5C,IAAI,CAAC4T,YAAY,CAACnK,GAAG,CAAC/D,IAAI,CAAC;MAC3B;IACJ;EACJ;EACA4M,0BAA0BA,CAACgI,GAAG,EAAE;IAC5B;IACA;IACA;IACA;IACA,MAAME,aAAa,GAAG,IAAIlW,GAAG,CAAC,CAAC;IAC/B,MAAMmW,+BAA+B,GAAIH,GAAG,IAAK;MAC7C,KAAK,MAAMvP,KAAK,IAAIuP,GAAG,EAAE;QACrB,IAAItM,KAAK,CAACC,OAAO,CAAClD,KAAK,CAAC,EAAE;UACtB0P,+BAA+B,CAAC1P,KAAK,CAAC;QAC1C,CAAC,MACI,IAAI2P,cAAc,CAAC3P,KAAK,CAAC,EAAE;UAC5B,MAAMoK,GAAG,GAAGpK,KAAK,CAAC8M,IAAI;UACtB,IAAI2C,aAAa,CAACnM,GAAG,CAAC8G,GAAG,CAAC,EAAE;YACxB;UACJ;UACAqF,aAAa,CAAC/Q,GAAG,CAAC0L,GAAG,CAAC;UACtB;UACA;UACA,IAAI,CAAC/C,cAAc,CAACwF,aAAa,CAACzC,GAAG,CAACjD,YAAY,CAAC,EAAEnH,KAAK,CAAC;UAC3D0P,+BAA+B,CAAC7C,aAAa,CAACzC,GAAG,CAAC9C,OAAO,CAAC,CAAC;UAC3DoI,+BAA+B,CAAC7C,aAAa,CAACzC,GAAG,CAACwF,OAAO,CAAC,CAAC;QAC/D,CAAC,MACI,IAAIV,qBAAqB,CAAClP,KAAK,CAAC,EAAE;UACnC0P,+BAA+B,CAAC,CAAC1P,KAAK,CAAC6H,QAAQ,CAAC,CAAC;QACrD,CAAC,MACI,IAAI2G,qBAAqB,CAACxO,KAAK,CAAC,EAAE;UAAA,IAAA6P,kBAAA;UACnC,IAAI,CAACL,SAAS,CAACxP,KAAK,EAAE,IAAI,CAAC;UAC3B,MAAMoK,GAAG,GAAG4D,eAAe,CAAChO,KAAK,CAAC;UAClC,IAAIyP,aAAa,CAACnM,GAAG,CAAC8G,GAAG,CAAC,EAAE;YACxB;UACJ;UACAqF,aAAa,CAAC/Q,GAAG,CAAC0L,GAAG,CAAC;UACtB,MAAMyE,YAAY,GAAGhC,aAAa,EAAAgD,kBAAA,GAACzF,GAAG,CAACyE,YAAY,cAAAgB,kBAAA,cAAAA,kBAAA,GAAI,EAAE,CAAC;UAC1DhB,YAAY,CAACxM,OAAO,CAAEyM,UAAU,IAAK;YACjC;YACA;YACA;YACA;YACA,IAAIN,qBAAqB,CAACM,UAAU,CAAC,IAAIa,cAAc,CAACb,UAAU,CAAC,EAAE;cACjEY,+BAA+B,CAAC,CAACZ,UAAU,CAAC,CAAC;YACjD,CAAC,MACI;cACD,IAAI,CAACU,SAAS,CAACV,UAAU,EAAE,IAAI,CAAC;YACpC;UACJ,CAAC,CAAC;QACN;MACJ;IACJ,CAAC;IACDY,+BAA+B,CAACH,GAAG,CAAC;EACxC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAhC,iCAAiCA,CAACgC,GAAG,EAAE;IACnC,MAAMO,WAAW,GAAG,IAAIvW,GAAG,CAAC,CAAC;IAC7B,MAAM+T,eAAe,GAAG,IAAI/T,GAAG,CAAC,CAAC;IACjC,MAAMwW,wBAAwB,GAAGA,CAACR,GAAG,EAAES,IAAI,KAAK;MAC5C,KAAK,MAAMhQ,KAAK,IAAIuP,GAAG,EAAE;QACrB,IAAItM,KAAK,CAACC,OAAO,CAAClD,KAAK,CAAC,EAAE;UACtB;UACA;UACA+P,wBAAwB,CAAC/P,KAAK,EAAEgQ,IAAI,CAAC;QACzC,CAAC,MACI,IAAIL,cAAc,CAAC3P,KAAK,CAAC,EAAE;UAC5B,IAAI8P,WAAW,CAACxM,GAAG,CAACtD,KAAK,CAAC,EAAE;YACxB;YACA;YACA;YACA,IAAIsN,eAAe,CAAChK,GAAG,CAACtD,KAAK,CAAC,EAAE;cAC5BgQ,IAAI,CAAC3N,OAAO,CAAE4N,IAAI,IAAK3C,eAAe,CAAC5O,GAAG,CAACuR,IAAI,CAAC,CAAC;YACrD;YACA;UACJ;UACAH,WAAW,CAACpR,GAAG,CAACsB,KAAK,CAAC;UACtB,IAAI,IAAI,CAAC+H,iBAAiB,CAACzE,GAAG,CAACtD,KAAK,CAAC,EAAE;YACnCgQ,IAAI,CAAC3N,OAAO,CAAE4N,IAAI,IAAK3C,eAAe,CAAC5O,GAAG,CAACuR,IAAI,CAAC,CAAC;UACrD;UACA;UACA,MAAMjJ,SAAS,GAAGhH,KAAK,CAAC3K,WAAW,CAAC;UACpC0a,wBAAwB,CAAClD,aAAa,CAAC7F,SAAS,CAACM,OAAO,CAAC,EAAE0I,IAAI,CAACxM,MAAM,CAACxD,KAAK,CAAC,CAAC;QAClF;MACJ;IACJ,CAAC;IACD+P,wBAAwB,CAACR,GAAG,EAAE,EAAE,CAAC;IACjC,OAAOjC,eAAe;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,eAAeA,CAAC9K,IAAI,EAAE3H,IAAI,EAAE;IACxB,IAAI,CAAC,IAAI,CAACuV,aAAa,CAAC5M,GAAG,CAAC3I,IAAI,CAAC,EAAE;MAC/B,IAAI,CAACuV,aAAa,CAAC3N,GAAG,CAAC5H,IAAI,EAAE,IAAImH,GAAG,CAAC,CAAC,CAAC;IAC3C;IACA,MAAMqO,WAAW,GAAG,IAAI,CAACD,aAAa,CAAC1P,GAAG,CAAC7F,IAAI,CAAC;IAChD,IAAI,CAACwV,WAAW,CAAC7M,GAAG,CAAChB,IAAI,CAAC,EAAE;MACxB,MAAM8N,UAAU,GAAGhM,MAAM,CAACO,wBAAwB,CAAChK,IAAI,EAAE2H,IAAI,CAAC;MAC9D6N,WAAW,CAAC5N,GAAG,CAACD,IAAI,EAAE8N,UAAU,CAAC;IACrC;EACJ;EACA5C,qBAAqBA,CAAC7S,IAAI,EAAE0V,QAAQ,EAAEhB,SAAS,EAAE;IAC7C,MAAMjF,GAAG,GAAGzP,IAAI,CAAC0V,QAAQ,CAAC;IAC1B,MAAMf,aAAa,GAAGlF,GAAG,CAACiF,SAAS,CAAC;IACpC,IAAI,CAACF,aAAa,CAAChT,IAAI,CAAC;MAAEiT,MAAM,EAAEhF,GAAG;MAAEiF,SAAS;MAAEC;IAAc,CAAC,CAAC;EACtE;EACA;AACJ;AACA;AACA;AACA;EACI7D,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAAC6E,gCAAgC,KAAK,IAAI,EAAE;MAChD,IAAI,CAACA,gCAAgC,GAAG,IAAIxO,GAAG,CAAC,CAAC;IACrD;IACAjM,yCAAyC,CAAC,CAAC,CAACwM,OAAO,CAAC,CAACrC,KAAK,EAAE6D,GAAG,KAAK,IAAI,CAACyM,gCAAgC,CAAC/N,GAAG,CAACsB,GAAG,EAAE7D,KAAK,CAAC,CAAC;EAC9H;EACA;AACJ;AACA;AACA;AACA;EACIuQ,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAACD,gCAAgC,KAAK,IAAI,EAAE;MAChDva,gCAAgC,CAAC,IAAI,CAACua,gCAAgC,CAAC;MACvE,IAAI,CAACA,gCAAgC,GAAG,IAAI;IAChD;EACJ;EACAE,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACAC,YAAY,CAAC,IAAI,CAACtB,aAAa,EAAGuB,EAAE,IAAK;MACrCA,EAAE,CAACtB,MAAM,CAACsB,EAAE,CAACrB,SAAS,CAAC,GAAGqB,EAAE,CAACpB,aAAa;IAC9C,CAAC,CAAC;IACF;IACA,IAAI,CAACY,aAAa,CAAC7N,OAAO,CAAC,CAACsO,IAAI,EAAEhW,IAAI,KAAK;MACvC,IAAIzH,iCAAiC,EAAE;QACnCE,YAAY,CAAC0U,kBAAkB,CAACnN,IAAI,CAAC;MACzC;MACAgW,IAAI,CAACtO,OAAO,CAAC,CAACuO,UAAU,EAAEtO,IAAI,KAAK;QAC/B,IAAI,CAACsO,UAAU,EAAE;UACb;UACA;UACA;UACA;UACA;UACA;UACA,OAAOjW,IAAI,CAAC2H,IAAI,CAAC;QACrB,CAAC,MACI;UACD8B,MAAM,CAACyM,cAAc,CAAClW,IAAI,EAAE2H,IAAI,EAAEsO,UAAU,CAAC;QACjD;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACV,aAAa,CAACjW,KAAK,CAAC,CAAC;IAC1B,IAAI,CAACyU,6BAA6B,CAACzU,KAAK,CAAC,CAAC;IAC1C,IAAI,CAACsW,+BAA+B,CAAC,CAAC;IACtC;IACAlc,YAAY,CAACF,kBAAkB,CAAC;EACpC;EACA4X,iBAAiBA,CAAA,EAAG;IAChB,MAAM+E,eAAe,CAAC;IAEtBnb,oBAAoB,CAACmb,eAAe,EAAE;MAClClK,SAAS,EAAE,CACP,GAAG,IAAI,CAACiD,qBAAqB,EAC7B5T,mCAAmC,CAAC,CAAC,CAAC,CAAC,EACvCmD,8BAA8B,EAC9B;QAAEkQ,OAAO,EAAEpX,yBAAyB;QAAE6e,WAAW,EAAE5a;MAA8B,CAAC;IAE1F,CAAC,CAAC;IACF,MAAMyQ,SAAS,GAAG,CACd;MAAE0C,OAAO,EAAElT,QAAQ;MAAEiT,UAAU,EAAEA,CAAA,KAAM,IAAI2H,cAAc,CAAC,IAAI;IAAE,CAAC,EACjE;MAAE1H,OAAO,EAAEhT,mBAAmB;MAAEmT,QAAQ,EAAE;QAAEwH,QAAQ,EAAE,IAAI,CAACxJ;MAAmB;IAAE,CAAC,EACjF;MACI6B,OAAO,EAAE9S,mCAAmC;MAC5C6S,UAAU,EAAEA,CAAA,KAAM;QACd,IAAI,IAAI,CAAC3B,4BAA4B,EAAE;UACnC,MAAMwJ,OAAO,GAAGzgB,QAAQ,CAAC2I,8BAA8B,CAAC;UACxD,OAAQK,CAAC,IAAK;YACVyX,OAAO,CAAC1X,WAAW,CAACC,CAAC,CAAC;UAC1B,CAAC;QACL,CAAC,MACI;UACD,MAAMG,gBAAgB,GAAGnJ,QAAQ,CAACE,YAAY,CAAC;UAC/C,MAAMwgB,MAAM,GAAG1gB,QAAQ,CAACC,MAAM,CAAC;UAC/B,OAAQ+I,CAAC,IAAK0X,MAAM,CAACxX,iBAAiB,CAAC,MAAMC,gBAAgB,CAACJ,WAAW,CAACC,CAAC,CAAC,CAAC;QACjF;MACJ;IACJ,CAAC,EACD,GAAG,IAAI,CAACmN,SAAS,EACjB,GAAG,IAAI,CAACkD,iBAAiB,CAC5B;IACD,MAAMxC,OAAO,GAAG,CAACwJ,eAAe,EAAE,IAAI,CAACvK,qBAAqB,EAAE,IAAI,CAACe,OAAO,IAAI,EAAE,CAAC;IACjF3R,oBAAoB,CAAC,IAAI,CAAC+Q,cAAc,EAAE;MACtCS,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BG,OAAO;MACPE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBZ;IACJ,CAAC,EACD,sCAAuC,IAAI,CAAC;IAC5C,IAAI,CAAC0E,6BAA6B,CAAC,IAAI,CAAC5E,cAAc,CAAC;EAC3D;EACA,IAAInG,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAACuG,SAAS,KAAK,IAAI,EAAE;MACzB,OAAO,IAAI,CAACA,SAAS;IACzB;IACA,MAAMF,SAAS,GAAG,EAAE;IACpB,MAAMwK,eAAe,GAAG,IAAI,CAAC9K,QAAQ,CAAC/F,QAAQ,CAACC,GAAG,CAAC/J,gBAAgB,EAAE,EAAE,CAAC;IACxE2a,eAAe,CAAC/O,OAAO,CAAEgP,IAAI,IAAK;MAC9B,IAAIA,IAAI,CAACzK,SAAS,EAAE;QAChBA,SAAS,CAACzK,IAAI,CAACkV,IAAI,CAACzK,SAAS,CAAC;MAClC;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACC,iBAAiB,KAAK,IAAI,EAAE;MACjCD,SAAS,CAACzK,IAAI,CAAC,GAAG,IAAI,CAAC0K,iBAAiB,CAAC;IAC7C;IACA,IAAI,CAACC,SAAS,GAAGpQ,QAAQ,CAAC4a,MAAM,CAAC;MAAE1K,SAAS;MAAE2K,MAAM,EAAE,IAAI,CAACjL,QAAQ,CAAC/F;IAAS,CAAC,CAAC;IAC/E,OAAO,IAAI,CAACuG,SAAS;EACzB;EACA;EACA0K,0BAA0BA,CAACrI,QAAQ,EAAE;IACjC,MAAM7O,KAAK,GAAGmX,gBAAgB,CAACtI,QAAQ,CAAC;IACxC,OAAO,IAAI,CAACY,wBAAwB,CAACvJ,GAAG,CAAClG,KAAK,CAAC,IAAI,IAAI;EAC3D;EACAoX,oBAAoBA,CAAC9K,SAAS,EAAE;IAC5B,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAAC1K,MAAM,IAAI,IAAI,CAAC6N,wBAAwB,CAAChQ,IAAI,KAAK,CAAC,EAC3E,OAAO,EAAE;IACb;IACA;IACA;IACA;IACA;IACA,OAAOkV,OAAO,CAAC0C,gBAAgB,CAAC/K,SAAS,EAAGuC,QAAQ,IAAK,IAAI,CAACqI,0BAA0B,CAACrI,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;EAC9G;EACA4F,sBAAsBA,CAACnI,SAAS,EAAE;IAC9B,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAAC1K,MAAM,IAAI,IAAI,CAAC6N,wBAAwB,CAAChQ,IAAI,KAAK,CAAC,EAC3E,OAAO,EAAE;IACb,MAAM6X,kBAAkB,GAAGD,gBAAgB,CAAC/K,SAAS,CAAC;IACtD,MAAM7B,SAAS,GAAG,IAAI,CAAC2M,oBAAoB,CAACE,kBAAkB,CAAC;IAC/D,MAAMC,mBAAmB,GAAG,CAAC,GAAGD,kBAAkB,EAAE,GAAG7M,SAAS,CAAC;IACjE,MAAM+M,KAAK,GAAG,EAAE;IAChB,MAAMC,uBAAuB,GAAG,IAAIxY,GAAG,CAAC,CAAC;IACzC;IACA;IACA;IACA;IACAkX,YAAY,CAACoB,mBAAmB,EAAG1I,QAAQ,IAAK;MAC5C,MAAM7O,KAAK,GAAGmX,gBAAgB,CAACtI,QAAQ,CAAC;MACxC,IAAI,IAAI,CAACY,wBAAwB,CAACzG,GAAG,CAAChJ,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACyX,uBAAuB,CAACzO,GAAG,CAAChJ,KAAK,CAAC,EAAE;UACrCyX,uBAAuB,CAACrT,GAAG,CAACpE,KAAK,CAAC;UAClC;UACA;UACA;UACAwX,KAAK,CAACE,OAAO,CAAAC,aAAA,CAAAA,aAAA,KAAM9I,QAAQ;YAAEK,KAAK,EAAE;UAAK,EAAE,CAAC;QAChD;MACJ,CAAC,MACI;QACDsI,KAAK,CAACE,OAAO,CAAC7I,QAAQ,CAAC;MAC3B;IACJ,CAAC,CAAC;IACF,OAAO2I,KAAK;EAChB;EACA3D,oBAAoBA,CAACvH,SAAS,EAAE;IAC5B,OAAO,IAAI,CAAC8K,oBAAoB,CAAC9K,SAAS,CAAC,CAAC1K,MAAM,GAAG,CAAC;EAC1D;EACAkS,6BAA6BA,CAACnB,WAAW,EAAEiB,KAAK,EAAE;IAC9C,MAAM9D,GAAG,GAAG6C,WAAW,CAACiB,KAAK,CAAC;IAC9B,IAAI9D,GAAG,IAAIA,GAAG,CAAC8H,iBAAiB,EAAE;MAC9B,IAAI,CAAC9E,eAAe,CAACc,KAAK,EAAEjB,WAAW,CAAC;MACxC,MAAM/G,QAAQ,GAAGkE,GAAG,CAAC8H,iBAAiB;MACtC,MAAMC,kBAAkB,GAAIvL,SAAS,IAAK,IAAI,CAACmI,sBAAsB,CAACnI,SAAS,CAAC;MAChF,IAAI,CAAC4G,qBAAqB,CAACP,WAAW,EAAEiB,KAAK,EAAE,mBAAmB,CAAC;MACnE9D,GAAG,CAAC8H,iBAAiB,GAAIE,KAAK,IAAKlM,QAAQ,CAACkM,KAAK,EAAED,kBAAkB,CAAC;IAC1E;EACJ;AACJ;AACA,SAAS3L,aAAaA,CAAA,EAAG;EACrB,OAAO;IACHwB,MAAM,EAAE,IAAIrC,gBAAgB,CAAC,CAAC;IAC9BQ,SAAS,EAAE,IAAIV,iBAAiB,CAAC,CAAC;IAClCgD,SAAS,EAAE,IAAIjD,iBAAiB,CAAC,CAAC;IAClCoD,IAAI,EAAE,IAAIlD,YAAY,CAAC;EAC3B,CAAC;AACL;AACA,SAAS8I,qBAAqBA,CAACxO,KAAK,EAAE;EAClC,MAAMoK,GAAG,GAAG4D,eAAe,CAAChO,KAAK,CAAC;EAClC,OAAO,CAAC,EAACoK,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEhE,UAAU;AAC5B;AACA,SAAS4H,eAAeA,CAAChO,KAAK,EAAE;EAAA,IAAAqS,WAAA;EAC5B,QAAAA,WAAA,GAAOrS,KAAK,CAACmN,IAAI,cAAAkF,WAAA,cAAAA,WAAA,GAAI,IAAI;AAC7B;AACA,SAAS1C,cAAcA,CAAC3P,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACiJ,cAAc,CAAC,MAAM,CAAC;AACvC;AACA,SAASwF,UAAUA,CAACzO,KAAK,EAAE;EACvB,OAAO2P,cAAc,CAAC3P,KAAK,CAAC;AAChC;AACA,SAAS6M,aAAaA,CAACyF,OAAO,EAAE;EAC5B,OAAOA,OAAO,YAAYC,QAAQ,GAAGD,OAAO,CAAC,CAAC,GAAGA,OAAO;AAC5D;AACA,SAASrD,OAAOA,CAACjV,MAAM,EAAE;EACrB,MAAMwY,GAAG,GAAG,EAAE;EACdxY,MAAM,CAACqI,OAAO,CAAErC,KAAK,IAAK;IACtB,IAAIiD,KAAK,CAACC,OAAO,CAAClD,KAAK,CAAC,EAAE;MACtBwS,GAAG,CAACrW,IAAI,CAAC,GAAG8S,OAAO,CAACjP,KAAK,CAAC,CAAC;IAC/B,CAAC,MACI;MACDwS,GAAG,CAACrW,IAAI,CAAC6D,KAAK,CAAC;IACnB;EACJ,CAAC,CAAC;EACF,OAAOwS,GAAG;AACd;AACA,SAASC,UAAUA,CAACzS,KAAK,EAAE;EACvB,OAAOA,KAAK;AAChB;AACA,SAAS2R,gBAAgBA,CAAC/K,SAAS,EAAE8L,KAAK,GAAGD,UAAU,EAAE;EACrD,MAAMD,GAAG,GAAG,EAAE;EACd,KAAK,IAAIrJ,QAAQ,IAAIvC,SAAS,EAAE;IAC5B,IAAIzP,uBAAuB,CAACgS,QAAQ,CAAC,EAAE;MACnCA,QAAQ,GAAGA,QAAQ,CAACwJ,UAAU;IAClC;IACA,IAAI1P,KAAK,CAACC,OAAO,CAACiG,QAAQ,CAAC,EAAE;MACzBqJ,GAAG,CAACrW,IAAI,CAAC,GAAGwV,gBAAgB,CAACxI,QAAQ,EAAEuJ,KAAK,CAAC,CAAC;IAClD,CAAC,MACI;MACDF,GAAG,CAACrW,IAAI,CAACuW,KAAK,CAACvJ,QAAQ,CAAC,CAAC;IAC7B;EACJ;EACA,OAAOqJ,GAAG;AACd;AACA,SAASI,gBAAgBA,CAACzJ,QAAQ,EAAE+E,KAAK,EAAE;EACvC,OAAO/E,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAAC+E,KAAK,CAAC;AACtE;AACA,SAASuD,gBAAgBA,CAACtI,QAAQ,EAAE;EAChC,OAAOyJ,gBAAgB,CAACzJ,QAAQ,EAAE,SAAS,CAAC,IAAIA,QAAQ;AAC5D;AACA,SAAS+F,qBAAqBA,CAAClP,KAAK,EAAE;EAClC,OAAOA,KAAK,CAACiJ,cAAc,CAAC,UAAU,CAAC;AAC3C;AACA,SAASwH,YAAYA,CAACzW,MAAM,EAAEpB,EAAE,EAAE;EAC9B,KAAK,IAAIia,GAAG,GAAG7Y,MAAM,CAACkC,MAAM,GAAG,CAAC,EAAE2W,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;IAC/Cja,EAAE,CAACoB,MAAM,CAAC6Y,GAAG,CAAC,EAAEA,GAAG,CAAC;EACxB;AACJ;AACA,SAAS5K,gBAAgBA,CAACC,IAAI,EAAE4K,YAAY,EAAE;EAC1C,OAAO,IAAIxX,KAAK,CAAC,GAAG4M,IAAI,wBAAwB4K,YAAY,oCAAoC,CAAC;AACrG;AACA,MAAM9B,cAAc,CAAC;EAEjB3X,WAAWA,CAAC0Z,OAAO,EAAE;IAAAzZ,eAAA;IACjB,IAAI,CAACyZ,OAAO,GAAGA,OAAO;EAC1B;EACAC,iBAAiBA,CAACxG,UAAU,EAAE;IAC1B,IAAI,CAACuG,OAAO,CAACxG,oBAAoB,CAACC,UAAU,CAAC;IAC7C,OAAO,IAAIxV,gBAAgB,CAACwV,UAAU,CAAC;EAC3C;EACMyG,kBAAkBA,CAACzG,UAAU,EAAE;IAAA,IAAA0G,MAAA;IAAA,OAAAhY,iBAAA;MACjC,MAAMgY,MAAI,CAACH,OAAO,CAACtG,qBAAqB,CAACD,UAAU,CAAC;MACpD,OAAO,IAAIxV,gBAAgB,CAACwV,UAAU,CAAC;IAAC;EAC5C;EACA2G,iCAAiCA,CAAC3G,UAAU,EAAE;IAC1C,MAAM4G,eAAe,GAAG,IAAI,CAACJ,iBAAiB,CAACxG,UAAU,CAAC;IAC1D,MAAM6G,kBAAkB,GAAG,IAAI,CAACN,OAAO,CAACnG,sBAAsB,CAACJ,UAAU,CAAC;IAC1E,OAAO,IAAIvV,4BAA4B,CAACmc,eAAe,EAAEC,kBAAkB,CAAC;EAChF;EACMC,kCAAkCA,CAAC9G,UAAU,EAAE;IAAA,IAAA+G,MAAA;IAAA,OAAArY,iBAAA;MACjD,MAAMkY,eAAe,SAASG,MAAI,CAACN,kBAAkB,CAACzG,UAAU,CAAC;MACjE,MAAM6G,kBAAkB,GAAGE,MAAI,CAACR,OAAO,CAACnG,sBAAsB,CAACJ,UAAU,CAAC;MAC1E,OAAO,IAAIvV,4BAA4B,CAACmc,eAAe,EAAEC,kBAAkB,CAAC;IAAC;EACjF;EACAG,UAAUA,CAAA,EAAG,CAAE;EACfC,aAAaA,CAAC9Y,IAAI,EAAE,CAAE;EACtB+Y,WAAWA,CAAClH,UAAU,EAAE;IACpB,MAAMmH,IAAI,GAAG,IAAI,CAACZ,OAAO,CAACpG,kBAAkB,CAAC,CAAC,CAACvQ,OAAO,CAACoQ,UAAU,CAAC;IAClE,OAAQmH,IAAI,IAAIA,IAAI,CAACzP,EAAE,IAAK5D,SAAS;EACzC;AACJ;;AAEA;AACA;AACA;AACA,IAAIsT,kBAAkB,GAAG,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAA,EAAG;EAClB,OAAOC,WAAW,CAACC,QAAQ;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,WAAW,CAAC;EAAAza,YAAA;IAoBd;AACJ;AACA;AACA;IAHIC,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA,sCAI8BuD,4BAA4B;IAC1D;AACJ;AACA;AACA;IAHIvD,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA;IA6GA;IAAAA,eAAA,mBACW,IAAI;IAAAA,eAAA,mBACJ,IAAI;IAAAA,eAAA,oBACH,IAAI;IAAAA,eAAA,yBACC,IAAI;IAAAA,eAAA,0BACH,EAAE;IACpB;AACJ;AACA;AACA;AACA;IAJIA,eAAA,mCAK2B,KAAK;EAAA;EAnKhC,WAAWya,QAAQA,CAAA,EAAG;IAClB,OAAQD,WAAW,CAACE,SAAS,GAAGF,WAAW,CAACE,SAAS,IAAI,IAAIF,WAAW,CAAC,CAAC;EAC9E;EACA;AACJ;AACA;AACA;;EA0CI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOG,mBAAmBA,CAACpM,QAAQ,EAAEvB,QAAQ,EAAEjF,OAAO,EAAE;IACpD,MAAM0R,OAAO,GAAGe,WAAW,CAACC,QAAQ;IACpChB,OAAO,CAACkB,mBAAmB,CAACpM,QAAQ,EAAEvB,QAAQ,EAAEjF,OAAO,CAAC;IACxD,OAAO0R,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOmB,oBAAoBA,CAAA,EAAG;IAC1BJ,WAAW,CAACC,QAAQ,CAACG,oBAAoB,CAAC,CAAC;EAC/C;EACA,OAAOC,iBAAiBA,CAACC,MAAM,EAAE;IAC7B,OAAON,WAAW,CAACC,QAAQ,CAACI,iBAAiB,CAACC,MAAM,CAAC;EACzD;EACA;AACJ;AACA;AACA;EACI,OAAOrN,sBAAsBA,CAACC,SAAS,EAAE;IACrC,OAAO8M,WAAW,CAACC,QAAQ,CAAChN,sBAAsB,CAACC,SAAS,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOuE,iBAAiBA,CAAA,EAAG;IACvB,OAAOuI,WAAW,CAACC,QAAQ,CAACxI,iBAAiB,CAAC,CAAC;EACnD;EACA,OAAO3D,cAAcA,CAACC,QAAQ,EAAE3F,QAAQ,EAAE;IACtC,OAAO4R,WAAW,CAACC,QAAQ,CAACnM,cAAc,CAACC,QAAQ,EAAE3F,QAAQ,CAAC;EAClE;EACA,OAAOkG,iBAAiBA,CAACjC,SAAS,EAAEjE,QAAQ,EAAE;IAC1C,OAAO4R,WAAW,CAACC,QAAQ,CAAC3L,iBAAiB,CAACjC,SAAS,EAAEjE,QAAQ,CAAC;EACtE;EACA,OAAOsG,iBAAiBA,CAACC,SAAS,EAAEvG,QAAQ,EAAE;IAC1C,OAAO4R,WAAW,CAACC,QAAQ,CAACvL,iBAAiB,CAACC,SAAS,EAAEvG,QAAQ,CAAC;EACtE;EACA,OAAOyG,YAAYA,CAACC,IAAI,EAAE1G,QAAQ,EAAE;IAChC,OAAO4R,WAAW,CAACC,QAAQ,CAACpL,YAAY,CAACC,IAAI,EAAE1G,QAAQ,CAAC;EAC5D;EACA,OAAOmS,gBAAgBA,CAAClO,SAAS,EAAEgE,QAAQ,EAAE;IACzC,OAAO2J,WAAW,CAACC,QAAQ,CAACM,gBAAgB,CAAClO,SAAS,EAAEgE,QAAQ,CAAC;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOD,kCAAkCA,CAAC/D,SAAS,EAAEgE,QAAQ,EAAE;IAC3D,OAAO2J,WAAW,CAACC,QAAQ,CAAC7J,kCAAkC,CAAC/D,SAAS,EAAEgE,QAAQ,CAAC;EACvF;EACA,OAAOjB,gBAAgBA,CAAC5O,KAAK,EAAE6O,QAAQ,EAAE;IACrC,OAAO2K,WAAW,CAACC,QAAQ,CAAC7K,gBAAgB,CAAC5O,KAAK,EAAE6O,QAAQ,CAAC;EACjE;EACA,OAAO3Y,MAAMA,CAAC8J,KAAK,EAAEga,aAAa,EAAEC,KAAK,EAAE;IACvC,OAAOT,WAAW,CAACC,QAAQ,CAACvjB,MAAM,CAAC8J,KAAK,EAAEga,aAAa,EAAEjd,kBAAkB,CAACkd,KAAK,CAAC,CAAC;EACvF;EACA;EACA,OAAO/T,GAAGA,CAAClG,KAAK,EAAEga,aAAa,GAAG5d,QAAQ,CAAC8d,kBAAkB,EAAED,KAAK,GAAGjd,WAAW,CAACmd,OAAO,EAAE;IACxF,OAAOX,WAAW,CAACC,QAAQ,CAACvjB,MAAM,CAAC8J,KAAK,EAAEga,aAAa,EAAEC,KAAK,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOpc,qBAAqBA,CAACS,EAAE,EAAE;IAC7B,OAAOkb,WAAW,CAACC,QAAQ,CAAC5b,qBAAqB,CAACS,EAAE,CAAC;EACzD;EACA,OAAO8b,eAAeA,CAACvO,SAAS,EAAE;IAC9B,OAAO2N,WAAW,CAACC,QAAQ,CAACW,eAAe,CAACvO,SAAS,CAAC;EAC1D;EACA,OAAOwO,kBAAkBA,CAAA,EAAG;IACxB,OAAOb,WAAW,CAACC,QAAQ,CAACY,kBAAkB,CAAC,CAAC;EACpD;EACA,OAAOC,OAAOA,CAACC,MAAM,EAAEjc,EAAE,EAAEkc,OAAO,EAAE;IAChC,OAAOhB,WAAW,CAACC,QAAQ,CAACa,OAAO,CAACC,MAAM,EAAEjc,EAAE,EAAEkc,OAAO,CAAC;EAC5D;EACA,WAAWxO,QAAQA,CAAA,EAAG;IAClB,OAAOwN,WAAW,CAACC,QAAQ,CAACzN,QAAQ;EACxC;EACA,WAAWuB,QAAQA,CAAA,EAAG;IAClB,OAAOiM,WAAW,CAACC,QAAQ,CAAClM,QAAQ;EACxC;EACA,OAAOkN,YAAYA,CAAA,EAAG;IAClB,OAAOjB,WAAW,CAACC,QAAQ,CAACgB,YAAY,CAAC,CAAC;EAC9C;EAaA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACId,mBAAmBA,CAACpM,QAAQ,EAAEvB,QAAQ,EAAEjF,OAAO,EAAE;IAC7C,IAAI,IAAI,CAACiF,QAAQ,IAAI,IAAI,CAACuB,QAAQ,EAAE;MAChC,MAAM,IAAIvM,KAAK,CAAC,8DAA8D,CAAC;IACnF;IACAwY,WAAW,CAACkB,2BAA2B,GAAG3T,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4T,QAAQ;IAC3DnB,WAAW,CAACoB,wCAAwC,GAAG7T,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8T,sBAAsB;IACtFrB,WAAW,CAACsB,0CAA0C,GAAG/T,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgU,wBAAwB;IAC1F,IAAI,CAAC/O,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACuB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACyN,SAAS,GAAG,IAAIjP,eAAe,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACuB,QAAQ,CAAC;IAClE;IACA;IACA;IACA;IACArQ,oCAAoC,CAAC,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACI0c,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACS,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACW,SAAS,GAAG,IAAI;IACrB,IAAI,CAAChP,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACuB,QAAQ,GAAG,IAAI;IACpBiM,WAAW,CAACkB,2BAA2B,GAAG1U,SAAS;IACnD9I,oCAAoC,CAAC,KAAK,CAAC;EAC/C;EACAmd,kBAAkBA,CAAA,EAAG;IAAA,IAAAY,qBAAA,EAAAC,sBAAA;IACjB,IAAI,CAACC,8BAA8B,CAAC,CAAC;IACrC/d,wBAAwB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAAC4d,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAACI,QAAQ,CAAClF,oBAAoB,CAAC,CAAC;IACxC;IACA,IAAI,CAAC8E,SAAS,GAAG,IAAIjP,eAAe,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACuB,QAAQ,CAAC;IAClE;IACAjQ,4BAA4B,EAAA2d,qBAAA,GAAC,IAAI,CAACI,qCAAqC,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI5Y,iCAAiC,CAAC;IAC7G;IACA7E,6BAA6B,EAAA0d,sBAAA,GAAC,IAAI,CAACI,uCAAuC,cAAAJ,sBAAA,cAAAA,sBAAA,GAAI5Y,mCAAmC,CAAC;IAClH;IACA;IACA;IACA,IAAI;MACA,IAAI,CAACiZ,qBAAqB,CAAC,CAAC;IAChC,CAAC,SACO;MACJ,IAAI;QACA,IAAI,IAAI,CAACC,2BAA2B,CAAC,CAAC,EAAE;UACpC,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAChC;MACJ,CAAC,SACO;QACJ,IAAI,CAACC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,wBAAwB,GAAG3V,SAAS;QACzC,IAAI,CAAC4V,qCAAqC,GAAG5V,SAAS;QACtD,IAAI,CAAC6V,uCAAuC,GAAG7V,SAAS;QACxD,IAAI,CAAC8V,2BAA2B,GAAGvZ,4BAA4B;MACnE;IACJ;IACA,OAAO,IAAI;EACf;EACAsX,iBAAiBA,CAACC,MAAM,EAAE;IACtB,IAAIA,MAAM,CAACiC,MAAM,IAAI,IAAI,EAAE;MACvB,MAAM,IAAI/a,KAAK,CAAC,oDAAoD,CAAC;IACzE;IACA,IAAI8Y,MAAM,CAACxN,SAAS,KAAKtG,SAAS,EAAE;MAChC,IAAI,CAACoV,QAAQ,CAAC/O,oBAAoB,CAACyN,MAAM,CAACxN,SAAS,CAAC;IACxD;IACA,OAAO,IAAI;EACf;EACAG,sBAAsBA,CAACC,SAAS,EAAE;IAAA,IAAAsP,sBAAA;IAC9B,IAAI,CAACC,qBAAqB,CAAC,gCAAgC,EAAE,2BAA2B,CAAC;IACzF;IACA;IACA;IACA;IACA,IAAI,CAACd,8BAA8B,CAAC,CAAC;IACrC;IACA;IACA,IAAI,CAACQ,wBAAwB,GAAGjP,SAAS,CAACiO,QAAQ;IAClD,IAAI,CAACiB,qCAAqC,GAAGlP,SAAS,CAACmO,sBAAsB;IAC7E,IAAI,CAACgB,uCAAuC,GAAGnP,SAAS,CAACqO,wBAAwB;IACjF,IAAI,CAACe,2BAA2B,IAAAE,sBAAA,GAAGtP,SAAS,CAACS,kBAAkB,cAAA6O,sBAAA,cAAAA,sBAAA,GAAIzZ,4BAA4B;IAC/F;IACA;IACA,IAAI,CAAC8Y,qCAAqC,GAAG3d,4BAA4B,CAAC,CAAC;IAC3EJ,4BAA4B,CAAC,IAAI,CAAC4e,iCAAiC,CAAC,CAAC,CAAC;IACtE,IAAI,CAACZ,uCAAuC,GAAG1d,6BAA6B,CAAC,CAAC;IAC9EJ,6BAA6B,CAAC,IAAI,CAAC2e,mCAAmC,CAAC,CAAC,CAAC;IACzE,IAAI,CAACf,QAAQ,CAAC3O,sBAAsB,CAACC,SAAS,CAAC;IAC/C,OAAO,IAAI;EACf;EACAuE,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACmK,QAAQ,CAACnK,iBAAiB,CAAC,CAAC;EAC5C;EACA/a,MAAMA,CAAC8J,KAAK,EAAEga,aAAa,EAAEC,KAAK,EAAE;IAChC,IAAIja,KAAK,KAAKoc,OAAO,EAAE;MACnB,OAAO,IAAI;IACf;IACA,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,MAAM,GAAG,IAAI,CAACxK,aAAa,CAAC7L,QAAQ,CAACC,GAAG,CAAClG,KAAK,EAAEqc,SAAS,EAAEtf,kBAAkB,CAACkd,KAAK,CAAC,CAAC;IAC3F,OAAOqC,MAAM,KAAKD,SAAS,GACrB,IAAI,CAACjB,QAAQ,CAACnV,QAAQ,CAACC,GAAG,CAAClG,KAAK,EAAEga,aAAa,EAAEC,KAAK,CAAC,GACvDqC,MAAM;EAChB;EACA;EACApW,GAAGA,CAAClG,KAAK,EAAEga,aAAa,GAAG5d,QAAQ,CAAC8d,kBAAkB,EAAED,KAAK,GAAGjd,WAAW,CAACmd,OAAO,EAAE;IACjF,OAAO,IAAI,CAACjkB,MAAM,CAAC8J,KAAK,EAAEga,aAAa,EAAEC,KAAK,CAAC;EACnD;EACApc,qBAAqBA,CAACS,EAAE,EAAE;IACtB,OAAOT,qBAAqB,CAAC,IAAI,CAAC3H,MAAM,CAAC4H,mBAAmB,CAAC,EAAEQ,EAAE,CAAC;EACtE;EACAgc,OAAOA,CAACC,MAAM,EAAEjc,EAAE,EAAEkc,OAAO,EAAE;IACzB,MAAM+B,MAAM,GAAGhC,MAAM,CAACiC,GAAG,CAAEC,CAAC,IAAK,IAAI,CAACvmB,MAAM,CAACumB,CAAC,CAAC,CAAC;IAChD,OAAOne,EAAE,CAACoe,KAAK,CAAClC,OAAO,EAAE+B,MAAM,CAAC;EACpC;EACAjP,cAAcA,CAACC,QAAQ,EAAE3F,QAAQ,EAAE;IAC/B,IAAI,CAACqU,qBAAqB,CAAC,gBAAgB,EAAE,0BAA0B,CAAC;IACxE,IAAI,CAACb,QAAQ,CAAC9N,cAAc,CAACC,QAAQ,EAAE3F,QAAQ,CAAC;IAChD,OAAO,IAAI;EACf;EACAkG,iBAAiBA,CAACjC,SAAS,EAAEjE,QAAQ,EAAE;IACnC,IAAI,CAACqU,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC;IAC9E,IAAI,CAACb,QAAQ,CAACtN,iBAAiB,CAACjC,SAAS,EAAEjE,QAAQ,CAAC;IACpD,OAAO,IAAI;EACf;EACAgI,kCAAkCA,CAAC/D,SAAS,EAAEgE,QAAQ,EAAE;IACpD,IAAI,CAACoM,qBAAqB,CAAC,4CAA4C,EAAE,6EAA6E,CAAC;IACvJ,IAAI,CAACb,QAAQ,CAACxL,kCAAkC,CAAC/D,SAAS,EAAEgE,QAAQ,CAAC;IACrE,OAAO,IAAI;EACf;EACA3B,iBAAiBA,CAACC,SAAS,EAAEvG,QAAQ,EAAE;IACnC,IAAI,CAACqU,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC;IAC9E,IAAI,CAACb,QAAQ,CAAClN,iBAAiB,CAACC,SAAS,EAAEvG,QAAQ,CAAC;IACpD,OAAO,IAAI;EACf;EACAyG,YAAYA,CAACC,IAAI,EAAE1G,QAAQ,EAAE;IACzB,IAAI,CAACqU,qBAAqB,CAAC,cAAc,EAAE,wBAAwB,CAAC;IACpE,IAAI,CAACb,QAAQ,CAAC/M,YAAY,CAACC,IAAI,EAAE1G,QAAQ,CAAC;IAC1C,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIgH,gBAAgBA,CAAC5O,KAAK,EAAE6O,QAAQ,EAAE;IAC9B,IAAI,CAACoN,qBAAqB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;IACnE,IAAI,CAACb,QAAQ,CAACxM,gBAAgB,CAAC5O,KAAK,EAAE6O,QAAQ,CAAC;IAC/C,OAAO,IAAI;EACf;EACAkL,gBAAgBA,CAAClO,SAAS,EAAEgE,QAAQ,EAAE;IAClC,OAAO,IAAI,CAAC/B,iBAAiB,CAACjC,SAAS,EAAE;MAAE5D,GAAG,EAAE;QAAE4H,QAAQ;QAAE8M,WAAW,EAAE;MAAK;IAAE,CAAC,CAAC;EACtF;EACAvC,eAAeA,CAAC/Z,IAAI,EAAE;IAClB,MAAMuc,qBAAqB,GAAG,IAAI,CAAC1mB,MAAM,CAACuM,qBAAqB,CAAC;IAChE,MAAMoa,QAAQ,GAAG,OAAOvD,kBAAkB,EAAE,EAAE;IAC9CsD,qBAAqB,CAACla,iBAAiB,CAACma,QAAQ,CAAC;IACjD,IAAIxjB,wBAAwB,CAACgH,IAAI,CAAC,EAAE;MAChC,MAAM,IAAIW,KAAK,CAAC,cAAcX,IAAI,CAACuN,IAAI,6BAA6B,GAChE,6EAA6E,CAAC;IACtF;IACA,MAAMgF,YAAY,GAAGvS,IAAI,CAACwS,IAAI;IAC9B,IAAI,CAACD,YAAY,EAAE;MACf,MAAM,IAAI5R,KAAK,CAAC,kBAAkB5I,UAAU,CAACiI,IAAI,CAAC,0BAA0B,CAAC;IACjF;IACA,MAAMyc,gBAAgB,GAAG,IAAI7iB,wBAAwB,CAAC2Y,YAAY,CAAC;IACnE,MAAMmK,aAAa,GAAGA,CAAA,KAAM;MACxB,MAAM/Z,YAAY,GAAG8Z,gBAAgB,CAAC9F,MAAM,CAAC5a,QAAQ,CAAC4gB,IAAI,EAAE,EAAE,EAAE,IAAIH,QAAQ,EAAE,EAAE,IAAI,CAAC/K,aAAa,CAAC;MACnG,OAAO,IAAI,CAACjU,qBAAqB,CAAC,MAAM,IAAIkF,gBAAgB,CAACC,YAAY,CAAC,CAAC;IAC/E,CAAC;IACD,MAAMia,QAAQ,GAAG,IAAI,CAAC/mB,MAAM,CAAC4M,wBAAwB,EAAE,KAAK,CAAC;IAC7D,MAAM+T,MAAM,GAAGoG,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC/mB,MAAM,CAACE,MAAM,EAAE,IAAI,CAAC;IAC1D,MAAM8mB,OAAO,GAAGrG,MAAM,GAAGA,MAAM,CAACzR,GAAG,CAAC2X,aAAa,CAAC,GAAGA,aAAa,CAAC,CAAC;IACpE,IAAI,CAACI,eAAe,CAACtb,IAAI,CAACqb,OAAO,CAAC;IAClC,OAAOA,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACI,IAAI9B,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAACJ,SAAS,KAAK,IAAI,EAAE;MACzB,MAAM,IAAIha,KAAK,CAAC,kDAAkD,CAAC;IACvE;IACA,OAAO,IAAI,CAACga,SAAS;EACzB;EACA;AACJ;AACA;AACA;EACI,IAAIlJ,aAAaA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC4J,cAAc,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACA,cAAc,GAAG,IAAI,CAACN,QAAQ,CAAC5J,QAAQ,CAAC,CAAC;IAClD;IACA,OAAO,IAAI,CAACkK,cAAc;EAC9B;EACAO,qBAAqBA,CAACmB,UAAU,EAAEC,iBAAiB,EAAE;IACjD,IAAI,IAAI,CAAC3B,cAAc,KAAK,IAAI,EAAE;MAC9B,MAAM,IAAI1a,KAAK,CAAC,UAAUqc,iBAAiB,uDAAuD,GAC9F,mDAAmDD,UAAU,KAAK,CAAC;IAC3E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIjC,8BAA8BA,CAAA,EAAG;IAC7B;IACA;IACA,IAAI,CAAC,IAAI,CAACmC,wBAAwB,IAAI,IAAI,CAAC5B,cAAc,KAAK,IAAI,EAAE;MAChE1d,wCAAwC,CAAC,CAAC;IAC9C;IACA,IAAI,CAACsf,wBAAwB,GAAG,IAAI;EACxC;EACA/B,qBAAqBA,CAAA,EAAG;IACpB,IAAIgC,UAAU,GAAG,CAAC;IAClB,IAAI,CAACJ,eAAe,CAACpV,OAAO,CAAEmV,OAAO,IAAK;MACtC,IAAI;QACAA,OAAO,CAAC7W,OAAO,CAAC,CAAC;MACrB,CAAC,CACD,OAAOlH,CAAC,EAAE;QACNoe,UAAU,EAAE;QACZC,OAAO,CAAC1Y,KAAK,CAAC,mCAAmC,EAAE;UAC/C+G,SAAS,EAAEqR,OAAO,CAACrZ,iBAAiB;UACpC4Z,UAAU,EAAEte;QAChB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,IAAI,CAACge,eAAe,GAAG,EAAE;IACzB,IAAII,UAAU,GAAG,CAAC,IAAI,IAAI,CAACG,2BAA2B,CAAC,CAAC,EAAE;MACtD,MAAM1c,KAAK,CAAC,GAAGuc,UAAU,IAAIA,UAAU,KAAK,CAAC,GAAG,WAAW,GAAG,YAAY,GAAG,GACzE,6BAA6B,CAAC;IACtC;EACJ;EACAG,2BAA2BA,CAAA,EAAG;IAAA,IAAAC,IAAA,EAAAC,qBAAA;IAC1B,MAAMC,eAAe,GAAG,IAAI,CAAClC,wBAAwB;IACrD,MAAMmC,kBAAkB,GAAGtE,WAAW,CAACkB,2BAA2B;IAClE;IACA,IAAI,CAACmD,eAAe,IAAI,CAACC,kBAAkB,EAAE;MACzC,OAAO1b,0CAA0C;IACrD;IACA;IACA,QAAAub,IAAA,IAAAC,qBAAA,GAAQC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEE,aAAa,cAAAH,qBAAA,cAAAA,qBAAA,GAClCE,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEC,aAAa,cAAAJ,IAAA,cAAAA,IAAA,GACjC,IAAI,CAACnC,2BAA2B,CAAC,CAAC;EAC1C;EACAU,iCAAiCA,CAAA,EAAG;IAAA,IAAA8B,KAAA,EAAAC,qBAAA;IAChC;IACA,QAAAD,KAAA,IAAAC,qBAAA,GAAQ,IAAI,CAACrC,qCAAqC,cAAAqC,qBAAA,cAAAA,qBAAA,GAC9CzE,WAAW,CAACoB,wCAAwC,cAAAoD,KAAA,cAAAA,KAAA,GACpD3b,iCAAiC;EACzC;EACA8Z,mCAAmCA,CAAA,EAAG;IAAA,IAAA+B,KAAA,EAAAC,sBAAA;IAClC;IACA,QAAAD,KAAA,IAAAC,sBAAA,GAAQ,IAAI,CAACtC,uCAAuC,cAAAsC,sBAAA,cAAAA,sBAAA,GAChD3E,WAAW,CAACsB,0CAA0C,cAAAoD,KAAA,cAAAA,KAAA,GACtD5b,mCAAmC;EAC3C;EACAkZ,2BAA2BA,CAAA,EAAG;IAAA,IAAA4C,KAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;IAC1B,QAAAH,KAAA,IAAAC,qBAAA,IAAAC,sBAAA,GAAQ,IAAI,CAAC3C,wBAAwB,cAAA2C,sBAAA,uBAA7BA,sBAAA,CAA+BE,gBAAgB,cAAAH,qBAAA,cAAAA,qBAAA,IAAAE,qBAAA,GACnD/E,WAAW,CAACkB,2BAA2B,cAAA6D,qBAAA,uBAAvCA,qBAAA,CAAyCC,gBAAgB,cAAAJ,KAAA,cAAAA,KAAA,GACzDhc,0CAA0C;EAClD;EACAqc,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC3C,2BAA2B;EAC3C;EACAL,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACC,cAAc,KAAK,IAAI,EAAE;MAC9B;IACJ;IACA;IACA;IACA,MAAMgD,YAAY,GAAG,IAAI,CAACxoB,MAAM,CAACuM,qBAAqB,CAAC;IACvD,IAAI;MACA,IAAI,CAACiZ,cAAc,CAACrV,OAAO,CAAC,CAAC;IACjC,CAAC,CACD,OAAOlH,CAAC,EAAE;MACN,IAAI,IAAI,CAACue,2BAA2B,CAAC,CAAC,EAAE;QACpC,MAAMve,CAAC;MACX,CAAC,MACI;QACDqe,OAAO,CAAC1Y,KAAK,CAAC,0CAA0C,EAAE;UACtD+G,SAAS,EAAE,IAAI,CAAC6P,cAAc,CAAC5X,QAAQ;UACvC2Z,UAAU,EAAEte;QAChB,CAAC,CAAC;MACN;IACJ,CAAC,SACO;MAAA,IAAAwf,qBAAA;MACJ,CAAAA,qBAAA,GAAAD,YAAY,CAAC9b,qBAAqB,cAAA+b,qBAAA,eAAlCA,qBAAA,CAAAC,IAAA,CAAAF,YAAqC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIjE,YAAYA,CAAA,EAAG;IACX,IAAI,CAACvkB,MAAM,CAAC8B,yBAAyB,CAAC,CAACiN,KAAK,CAAC,CAAC;IAC9C,IAAI,CAAC/O,MAAM,CAAC4B,gBAAgB,CAAC,CAACmN,KAAK,CAAC,CAAC;EACzC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARAjG,eAAA,CAzeMwa,WAAW,eACM,IAAI;AAAAxa,eAAA,CADrBwa,WAAW;AAUb;AACJ;AACA;AACA;AAHIxa,eAAA,CAVEwa,WAAW;AAeb;AACJ;AACA;AACA;AAHIxa,eAAA,CAfEwa,WAAW;AAkfjB,MAAM4C,OAAO,GAAG5C,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAStjB,MAAMA,CAACqkB,MAAM,EAAEjc,EAAE,EAAE;EACxB,MAAMma,OAAO,GAAGe,WAAW,CAACC,QAAQ;EACpC;EACA,OAAO,YAAY;IACf,OAAOhB,OAAO,CAAC6B,OAAO,CAACC,MAAM,EAAEjc,EAAE,EAAE,IAAI,CAAC;EAC5C,CAAC;AACL;AACA;AACA;AACA;AACA,MAAMugB,kBAAkB,CAAC;EAErB9f,WAAWA,CAAC+f,UAAU,EAAE;IAAA9f,eAAA;IACpB,IAAI,CAAC8f,UAAU,GAAGA,UAAU;EAChC;EACAC,UAAUA,CAAA,EAAG;IACT,MAAMrS,SAAS,GAAG,IAAI,CAACoS,UAAU,CAAC,CAAC;IACnC,IAAIpS,SAAS,EAAE;MACX8M,WAAW,CAAC/M,sBAAsB,CAACC,SAAS,CAAC;IACjD;EACJ;EACAxW,MAAMA,CAACqkB,MAAM,EAAEjc,EAAE,EAAE;IACf,MAAM0gB,IAAI,GAAG,IAAI;IACjB;IACA,OAAO,YAAY;MACfA,IAAI,CAACD,UAAU,CAAC,CAAC;MACjB,OAAO7oB,MAAM,CAACqkB,MAAM,EAAEjc,EAAE,CAAC,CAACsgB,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC;EACL;AACJ;AACA,SAASK,UAAUA,CAACvS,SAAS,EAAEpO,EAAE,EAAE;EAC/B,IAAIA,EAAE,EAAE;IACJ;IACA,OAAO,YAAY;MACf,MAAMma,OAAO,GAAGe,WAAW,CAACC,QAAQ;MACpC,IAAI/M,SAAS,EAAE;QACX+L,OAAO,CAAChM,sBAAsB,CAACC,SAAS,CAAC;MAC7C;MACA,OAAOpO,EAAE,CAACoe,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;EACL;EACA,OAAO,IAAImC,kBAAkB,CAAC,MAAMnS,SAAS,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAAwS,qBAAA,GAAAC,UAAU,CAACC,UAAU,cAAAF,qBAAA,eAArBA,qBAAA,CAAAN,IAAA,CAAAO,UAAU,EAAcE,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA,CAAAC,qBAAA,GAAAH,UAAU,CAACI,SAAS,cAAAD,qBAAA,eAApBA,qBAAA,CAAAV,IAAA,CAAAO,UAAU,EAAaE,cAAc,CAAC,IAAI,CAAC,CAAC;AAC5C,SAASA,cAAcA,CAACG,qBAAqB,EAAE;EAC3C,OAAO,MAAM;IACT,MAAM/G,OAAO,GAAGe,WAAW,CAACC,QAAQ;IACpC,IAAIhB,OAAO,CAAC+C,2BAA2B,CAAC,CAAC,KAAKgE,qBAAqB,EAAE;MACjE/G,OAAO,CAAC4B,kBAAkB,CAAC,CAAC;MAC5B1T,0BAA0B,CAAC,CAAC;IAChC;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8Y,oCAAoC,GAAG,EAAE;;AAE/C;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EAgDjB;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,UAAU,CAAC,IAAI,CAACC,iBAAiB,CAAC;EAClD;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,iBAAiB,GAAG,CAAC;EACrC;EACA,IAAIE,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACF,iBAAiB,GAAG,IAAI,CAACD,UAAU,CAAChe,MAAM,GAAG,CAAC;EAC9D;EAGA,IAAIoe,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACAlhB,WAAWA,CAACmhB,GAAG,EAAEC,QAAQ,EAAE;IAAA,IAAAC,qBAAA;IA9D3B;AACJ;AACA;AACA;IAHIphB,eAAA,qBAIa,EAAE;IACf;AACJ;AACA;IAFIA,eAAA,4BAGoB,CAAC;IACrB;AACJ;AACA;AACA;IAHIA,eAAA,wBAIgB,IAAI;IACpB;AACJ;AACA;AACA;IAHIA,eAAA,yBAIiB,IAAIwI,GAAG,CAAC,CAAC;IAC1B;AACJ;AACA;AACA;IAHIxI,eAAA,wBAIgBP,OAAO,CAACqD,OAAO,CAAC,CAAC;IACjC;AACJ;AACA;AACA;IAHI9C,eAAA,gCAIwB,CAAC;IACzB;AACJ;AACA;AACA;IAHIA,eAAA,gCAIwB,KAAK;IAC7B;IAAAA,eAAA,6BACqB,IAAI;IACzB;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA,iBACS,CAAC;IACV;IAAAA,eAAA,kBACU,CAAC;IACX;IAAAA,eAAA,mBACW,KAAK;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,sBA+cF,IAAI;IA9bd,IAAI,CAACqhB,iBAAiB,GAAG,MAAM;MAC3B,IAAI;QACA;QACA;QACA;QACA;QACA;QACA,OAAOH,GAAG,CAACI,aAAa,CAAC,KAAK,CAAC;MACnC,CAAC,CACD,OAAAC,OAAA,EAAM;QACF;QACA;QACA;QACA,OAAO,IAAIC,WAAW,CAAC,CAAC;MAC5B;IACJ,CAAC;IACD,IAAI,CAACP,OAAO,IAAAG,qBAAA,GAAGK,QAAQ,CAACC,WAAW,cAAAN,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC/D,IAAI,CAACM,WAAW,GAAG,IAAI,CAACN,iBAAiB,CAAC,CAAC;IAC3C;IACA,IAAI,CAACO,yBAAyB,CAACT,QAAQ,CAAC;EAC5C;EACA;AACJ;AACA;EACIS,yBAAyBA,CAACrP,GAAG,EAAExK,OAAO,GAAG;IAAE8Z,YAAY,EAAE;EAAK,CAAC,EAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAC7D,IAAI,CAAC,IAAI,CAACC,kBAAkB,EAAE;MAC1B,MAAM,IAAIhgB,KAAK,CAAC,0DAA0D,GAAG,yBAAyB,CAAC;IAC3G;IACA,MAAMigB,mBAAmB,GAAG,IAAI,CAACrB,UAAU,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACA,UAAU,CAAC,CAAC,CAAC,GAAG,IAAIsB,0BAA0B,CAAC,IAAI,CAACP,WAAW,EAAE,IAAIQ,GAAG,CAAC5P,GAAG,CAAC,CAAC6P,QAAQ,CAAC,CAAC,EAAE;MAC3FC,KAAK,EAAE,CAAC;MACR9X,GAAG,GAAAuX,qBAAA,GAAEG,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE1X,GAAG,cAAAuX,qBAAA,cAAAA,qBAAA,GAAIQ,MAAM,CAAC,IAAI,CAACC,OAAO,EAAE,CAAC;MACvD3X,EAAE,GAAAmX,sBAAA,GAAEE,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAErX,EAAE,cAAAmX,sBAAA,cAAAA,sBAAA,GAAIO,MAAM,CAAC,IAAI,CAACE,MAAM,EAAE,CAAC;MACpDC,YAAY,EAAE,IAAI;MAClBZ,YAAY,EAAE9Z,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8Z,YAAY;MACnCngB,KAAK,EAAEqG,OAAO,CAACrG;IACnB,CAAC,CAAC;EACN;EACA;EACAghB,4BAA4BA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAACV,kBAAkB;EAClC;EACA;AACJ;AACA;AACA;EACIW,kCAAkCA,CAACC,qBAAqB,EAAE;IACtD,IAAI,CAACA,qBAAqB,GAAGA,qBAAqB;EACtD;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACjC,UAAU,CAACkC,KAAK,CAAC,CAAC;EAClC;EACA;EACAC,QAAQA,CAACxQ,GAAG,EAAExK,OAAO,EAAE;IACnB,MAAMib,OAAO,GAAG,IAAIb,GAAG,CAAC,IAAI,CAACxB,YAAY,CAACpO,GAAG,CAAC;IAC9C,MAAM0Q,KAAK,GAAG,IAAId,GAAG,CAAC5P,GAAG,EAAE,IAAI,CAACoO,YAAY,CAACpO,GAAG,CAAC;IACjD,IAAI2Q,cAAc;IAClB,IAAI,EAACnb,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEob,OAAO,KAAIpb,OAAO,CAACob,OAAO,KAAK,MAAM,EAAE;MACjD;MACA,IAAIH,OAAO,CAACZ,QAAQ,CAAC,CAAC,KAAKa,KAAK,CAACb,QAAQ,CAAC,CAAC,EAAE;QACzCc,cAAc,GAAG,SAAS;MAC9B,CAAC,MACI;QACDA,cAAc,GAAG,MAAM;MAC3B;IACJ,CAAC,MACI;MACDA,cAAc,GAAGnb,OAAO,CAACob,OAAO;IACpC;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAEC,KAAK,CAAC;IAC/C,MAAMK,WAAW,GAAG,IAAIC,yBAAyB,CAAC;MAC9ChR,GAAG,EAAE0Q,KAAK,CAACb,QAAQ,CAAC,CAAC;MACrB1gB,KAAK,EAAEqG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAErG,KAAK;MACrB+gB,YAAY,EAAEW,UAAU;MACxBvB,YAAY,EAAE;IAClB,CAAC,CAAC;IACF,MAAMvE,MAAM,GAAG,IAAIkG,wBAAwB,CAAC,IAAI,CAAC;IACjD,MAAMC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAEhG,MAAM,EAAE;MAC5D4F,cAAc;MACdS,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClB;MACAC,aAAa,EAAE,KAAK;MACpBT,UAAU;MACVU,IAAI,EAAE/b,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+b;IACnB,CAAC,CAAC;IACF,IAAI,CAACL,WAAW,EAAE;MACd,IAAI,CAACM,gDAAgD,CAAC,IAAI,CAACC,aAAa,CAAC;IAC7E;IACA,OAAO;MACHC,SAAS,EAAE3G,MAAM,CAAC2G,SAAS;MAC3BC,QAAQ,EAAE5G,MAAM,CAAC4G;IACrB,CAAC;EACL;EACA;EACAC,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAE9R,GAAG,EAAE;IACxB,IAAI,CAAC+R,kBAAkB,CAAC,MAAM,EAAEF,IAAI,EAAEC,KAAK,EAAE9R,GAAG,CAAC;EACrD;EACA;EACAgS,YAAYA,CAACH,IAAI,EAAEC,KAAK,EAAE9R,GAAG,EAAE;IAC3B,IAAI,CAAC+R,kBAAkB,CAAC,SAAS,EAAEF,IAAI,EAAEC,KAAK,EAAE9R,GAAG,CAAC;EACxD;EACA+R,kBAAkBA,CAACpB,cAAc,EAAEkB,IAAI,EAAEI,MAAM,EAAEjS,GAAG,EAAE;IAClD,MAAMyQ,OAAO,GAAG,IAAIb,GAAG,CAAC,IAAI,CAACxB,YAAY,CAACpO,GAAG,CAAC;IAC9C,MAAM0Q,KAAK,GAAG1Q,GAAG,GAAG,IAAI4P,GAAG,CAAC5P,GAAG,EAAE,IAAI,CAACoO,YAAY,CAACpO,GAAG,CAAC,GAAGyQ,OAAO;IACjE,MAAMI,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAEC,KAAK,CAAC;IAC/C,MAAMK,WAAW,GAAG,IAAIC,yBAAyB,CAAC;MAC9ChR,GAAG,EAAE0Q,KAAK,CAACb,QAAQ,CAAC,CAAC;MACrBK,YAAY,EAAE,IAAI;MAClBZ,YAAY,EAAEuC;IAClB,CAAC,CAAC;IACF,MAAM9G,MAAM,GAAG,IAAIkG,wBAAwB,CAAC,IAAI,CAAC;IACjD,MAAMC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAEhG,MAAM,EAAE;MAC5D4F,cAAc;MACdS,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClB;MACAC,aAAa,EAAE,KAAK;MACpBT;IACJ,CAAC,CAAC;IACF,IAAIK,WAAW,EAAE;MACb;IACJ;IACA,IAAI,CAACM,gDAAgD,CAAC,IAAI,CAACC,aAAa,CAAC;EAC7E;EACA;EACAS,UAAUA,CAACla,GAAG,EAAExC,OAAO,EAAE;IACrB,MAAMib,OAAO,GAAG,IAAIb,GAAG,CAAC,IAAI,CAACxB,YAAY,CAACpO,GAAG,CAAC;IAC9C,MAAMmS,KAAK,GAAG,IAAI,CAACC,SAAS,CAACpa,GAAG,CAAC;IACjC,IAAI,CAACma,KAAK,EAAE;MACR,MAAME,YAAY,GAAG,IAAIC,YAAY,CAAC,aAAa,EAAE,mBAAmB,CAAC;MACzE,MAAMZ,SAAS,GAAGxkB,OAAO,CAACC,MAAM,CAACklB,YAAY,CAAC;MAC9C,MAAMV,QAAQ,GAAGzkB,OAAO,CAACC,MAAM,CAACklB,YAAY,CAAC;MAC7CX,SAAS,CAACa,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MAC1BZ,QAAQ,CAACY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MACzB,OAAO;QACHb,SAAS;QACTC;MACJ,CAAC;IACL;IACA,IAAIQ,KAAK,KAAK,IAAI,CAAC/D,YAAY,EAAE;MAC7B,OAAO;QACHsD,SAAS,EAAExkB,OAAO,CAACqD,OAAO,CAAC,IAAI,CAAC6d,YAAY,CAAC;QAC7CuD,QAAQ,EAAEzkB,OAAO,CAACqD,OAAO,CAAC,IAAI,CAAC6d,YAAY;MAC/C,CAAC;IACL;IACA,IAAI,IAAI,CAACoE,cAAc,CAAC/a,GAAG,CAAC0a,KAAK,CAACna,GAAG,CAAC,EAAE;MACpC,MAAMya,cAAc,GAAG,IAAI,CAACD,cAAc,CAAC7d,GAAG,CAACwd,KAAK,CAACna,GAAG,CAAC;MACzD,OAAO;QACH0Z,SAAS,EAAEe,cAAc,CAACf,SAAS;QACnCC,QAAQ,EAAEc,cAAc,CAACd;MAC7B,CAAC;IACL;IACA,MAAMd,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAE,IAAIb,GAAG,CAACuC,KAAK,CAACnS,GAAG,EAAE,IAAI,CAACoO,YAAY,CAACpO,GAAG,CAAC,CAAC;IACnF,MAAM+Q,WAAW,GAAG,IAAIC,yBAAyB,CAAC;MAC9ChR,GAAG,EAAEmS,KAAK,CAACnS,GAAG;MACd7Q,KAAK,EAAEgjB,KAAK,CAACO,QAAQ,CAAC,CAAC;MACvBpD,YAAY,EAAE6C,KAAK,CAACQ,eAAe,CAAC,CAAC;MACrC3a,GAAG,EAAEma,KAAK,CAACna,GAAG;MACdK,EAAE,EAAE8Z,KAAK,CAAC9Z,EAAE;MACZyX,KAAK,EAAEqC,KAAK,CAACrC,KAAK;MAClBI,YAAY,EAAEiC,KAAK,CAACjC;IACxB,CAAC,CAAC;IACF,IAAI,CAAC0C,qBAAqB,GAAGT,KAAK,CAACrC,KAAK;IACxC,MAAM/E,MAAM,GAAG,IAAIkG,wBAAwB,CAAC,IAAI,CAAC;IACjD,IAAI,CAACuB,cAAc,CAAC9b,GAAG,CAACyb,KAAK,CAACna,GAAG,EAAE+S,MAAM,CAAC;IAC1C,IAAI,CAAC8H,YAAY,CAAC,MAAM;MACpB,IAAI,CAACL,cAAc,CAACtf,MAAM,CAACif,KAAK,CAACna,GAAG,CAAC;MACrC,MAAMkZ,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAEhG,MAAM,EAAE;QAC5D4F,cAAc,EAAE,UAAU;QAC1BS,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClB;QACAC,aAAa,EAAE,KAAK;QACpBT,UAAU;QACVU,IAAI,EAAE/b,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE+b;MACnB,CAAC,CAAC;MACF,IAAI,CAACL,WAAW,EAAE;QACd,IAAI,CAAC4B,iBAAiB,CAAC,IAAI,CAACrB,aAAa,CAAC;MAC9C;IACJ,CAAC,CAAC;IACF,OAAO;MACHC,SAAS,EAAE3G,MAAM,CAAC2G,SAAS;MAC3BC,QAAQ,EAAE5G,MAAM,CAAC4G;IACrB,CAAC;EACL;EACA;EACAoB,IAAIA,CAACvd,OAAO,EAAE;IACV,IAAI,IAAI,CAAC8Y,iBAAiB,KAAK,CAAC,EAAE;MAC9B,MAAM+D,YAAY,GAAG,IAAIC,YAAY,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;MAC5E,MAAMZ,SAAS,GAAGxkB,OAAO,CAACC,MAAM,CAACklB,YAAY,CAAC;MAC9C,MAAMV,QAAQ,GAAGzkB,OAAO,CAACC,MAAM,CAACklB,YAAY,CAAC;MAC7CX,SAAS,CAACa,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MAC1BZ,QAAQ,CAACY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MACzB,OAAO;QACHb,SAAS;QACTC;MACJ,CAAC;IACL;IACA,MAAMQ,KAAK,GAAG,IAAI,CAAC9D,UAAU,CAAC,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;IACzD,OAAO,IAAI,CAAC4D,UAAU,CAACC,KAAK,CAACna,GAAG,EAAExC,OAAO,CAAC;EAC9C;EACA;EACAwd,OAAOA,CAACxd,OAAO,EAAE;IACb,IAAI,IAAI,CAAC8Y,iBAAiB,KAAK,IAAI,CAACD,UAAU,CAAChe,MAAM,GAAG,CAAC,EAAE;MACvD,MAAMgiB,YAAY,GAAG,IAAIC,YAAY,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;MAC/E,MAAMZ,SAAS,GAAGxkB,OAAO,CAACC,MAAM,CAACklB,YAAY,CAAC;MAC9C,MAAMV,QAAQ,GAAGzkB,OAAO,CAACC,MAAM,CAACklB,YAAY,CAAC;MAC7CX,SAAS,CAACa,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MAC1BZ,QAAQ,CAACY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;MACzB,OAAO;QACHb,SAAS;QACTC;MACJ,CAAC;IACL;IACA,MAAMQ,KAAK,GAAG,IAAI,CAAC9D,UAAU,CAAC,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;IACzD,OAAO,IAAI,CAAC4D,UAAU,CAACC,KAAK,CAACna,GAAG,EAAExC,OAAO,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyd,EAAEA,CAACC,SAAS,EAAE;IACV,MAAMC,WAAW,GAAG,IAAI,CAACP,qBAAqB,GAAGM,SAAS;IAC1D,IAAIC,WAAW,IAAI,IAAI,CAAC9E,UAAU,CAAChe,MAAM,IAAI8iB,WAAW,GAAG,CAAC,EAAE;MAC1D;IACJ;IACA,IAAI,CAACP,qBAAqB,GAAGO,WAAW;IACxC,IAAI,CAACN,YAAY,CAAC,MAAM;MACpB;MACA,IAAIM,WAAW,IAAI,IAAI,CAAC9E,UAAU,CAAChe,MAAM,IAAI8iB,WAAW,GAAG,CAAC,EAAE;QAC1D;MACJ;MACA,MAAM1C,OAAO,GAAG,IAAIb,GAAG,CAAC,IAAI,CAACxB,YAAY,CAACpO,GAAG,CAAC;MAC9C,MAAMmS,KAAK,GAAG,IAAI,CAAC9D,UAAU,CAAC8E,WAAW,CAAC;MAC1C,MAAMtC,UAAU,GAAGC,YAAY,CAACL,OAAO,EAAE,IAAIb,GAAG,CAACuC,KAAK,CAACnS,GAAG,EAAE,IAAI,CAACoO,YAAY,CAACpO,GAAG,CAAC,CAAC;MACnF,MAAM+Q,WAAW,GAAG,IAAIC,yBAAyB,CAAC;QAC9ChR,GAAG,EAAEmS,KAAK,CAACnS,GAAG;QACd7Q,KAAK,EAAEgjB,KAAK,CAACO,QAAQ,CAAC,CAAC;QACvBpD,YAAY,EAAE6C,KAAK,CAACQ,eAAe,CAAC,CAAC;QACrC3a,GAAG,EAAEma,KAAK,CAACna,GAAG;QACdK,EAAE,EAAE8Z,KAAK,CAAC9Z,EAAE;QACZyX,KAAK,EAAEqC,KAAK,CAACrC,KAAK;QAClBI,YAAY,EAAEiC,KAAK,CAACjC;MACxB,CAAC,CAAC;MACF,MAAMnF,MAAM,GAAG,IAAIkG,wBAAwB,CAAC,IAAI,CAAC;MACjD,MAAMC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACJ,WAAW,EAAEhG,MAAM,EAAE;QAC5D4F,cAAc,EAAE,UAAU;QAC1BS,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClB;QACAC,aAAa,EAAE,KAAK;QACpBT;MACJ,CAAC,CAAC;MACF,IAAI,CAACK,WAAW,EAAE;QACd,IAAI,CAAC4B,iBAAiB,CAAC,IAAI,CAACrB,aAAa,CAAC;MAC9C;IACJ,CAAC,CAAC;EACN;EACA;EACAoB,YAAYA,CAACO,SAAS,EAAE;IACpB,IAAI,IAAI,CAAC/C,qBAAqB,EAAE;MAC5B+C,SAAS,CAAC,CAAC;MACX;IACJ;IACA;IACA;IACA;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC/e,IAAI,CAAC,MAAM;MAC/C,OAAO,IAAIpH,OAAO,CAAEqD,OAAO,IAAK;QAC5B+iB,UAAU,CAAC,MAAM;UACb/iB,OAAO,CAAC,CAAC;UACT6iB,SAAS,CAAC,CAAC;QACf,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAG,gBAAgBA,CAACzkB,IAAI,EAAE0kB,QAAQ,EAAEhe,OAAO,EAAE;IACtC,IAAI,CAAC4Z,WAAW,CAACmE,gBAAgB,CAACzkB,IAAI,EAAE0kB,QAAQ,EAAEhe,OAAO,CAAC;EAC9D;EACA;EACAie,mBAAmBA,CAAC3kB,IAAI,EAAE0kB,QAAQ,EAAEhe,OAAO,EAAE;IACzC,IAAI,CAAC4Z,WAAW,CAACqE,mBAAmB,CAAC3kB,IAAI,EAAE0kB,QAAQ,EAAEhe,OAAO,CAAC;EACjE;EACA;EACAke,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAO,IAAI,CAACvE,WAAW,CAACsE,aAAa,CAACC,KAAK,CAAC;EAChD;EACA;EACAC,OAAOA,CAAA,EAAG;IACN;IACA,IAAI,CAACxE,WAAW,GAAG,IAAI,CAACN,iBAAiB,CAAC,CAAC;IAC3C,IAAI,CAAC+E,QAAQ,GAAG,IAAI;EACxB;EACA;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACD,QAAQ;EACxB;EACA;AACJ;AACA;AACA;EACI1C,iBAAiBA,CAACJ,WAAW,EAAEhG,MAAM,EAAEvV,OAAO,EAAE;IAC5C;IACA;IACA,IAAI,CAACia,kBAAkB,GAAG,KAAK;IAC/B,IAAI,IAAI,CAACgC,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACsC,MAAM,CAAC,IAAIzB,YAAY,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC;MACnF,IAAI,CAACb,aAAa,GAAG,IAAI;IAC7B;IACA,OAAOuC,qBAAqB,CAAC;MACzBrD,cAAc,EAAEnb,OAAO,CAACmb,cAAc;MACtCS,UAAU,EAAE5b,OAAO,CAAC4b,UAAU;MAC9BC,YAAY,EAAE7b,OAAO,CAAC6b,YAAY;MAClCC,aAAa,EAAE9b,OAAO,CAAC8b,aAAa;MACpCT,UAAU,EAAErb,OAAO,CAACqb,UAAU;MAC9BoD,MAAM,EAAElJ,MAAM,CAACkJ,MAAM;MACrBlD,WAAW;MACXQ,IAAI,EAAE/b,OAAO,CAAC+b,IAAI;MAClBrB,YAAY,EAAEa,WAAW,CAACb,YAAY;MACtCnF;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACImJ,wBAAwBA,CAACzC,aAAa,EAAE;IACpC,IAAI,CAACD,gDAAgD,CAACC,aAAa,CAAC;EACxE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIqB,iBAAiBA,CAACrB,aAAa,EAAE;IAC7B,MAAM0C,MAAM,GAAG,IAAI,CAAC/F,YAAY,CAACpO,GAAG;IACpC,IAAI,CAACwR,gDAAgD,CAACC,aAAa,CAAC;IACpE;IACA,MAAM2C,aAAa,GAAGC,mBAAmB,CAAC;MACtCllB,KAAK,EAAEsiB,aAAa,CAACV,WAAW,CAAC4B,eAAe,CAAC;IACrD,CAAC,CAAC;IACF,IAAI,CAACjE,OAAO,CAACgF,aAAa,CAACU,aAAa,CAAC;IACzC,IAAI3C,aAAa,CAACZ,UAAU,EAAE;MAC1B,MAAMyD,eAAe,GAAGC,qBAAqB,CAACJ,MAAM,EAAE,IAAI,CAAC/F,YAAY,CAACpO,GAAG,CAAC;MAC5E,IAAI,CAAC0O,OAAO,CAACgF,aAAa,CAACY,eAAe,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;EACI9C,gDAAgDA,CAAC;IAAET,WAAW;IAAEJ,cAAc;IAAE5F;EAAQ,CAAC,EAAE;IACvF,MAAMyJ,aAAa,GAAG,IAAI,CAACpG,YAAY;IACvC,MAAMqG,YAAY,GAAG,EAAE;IACvB,IAAI9D,cAAc,KAAK,UAAU,EAAE;MAC/B,IAAI,CAACrC,iBAAiB,GAAGyC,WAAW,CAACjB,KAAK;MAC1C,IAAI,IAAI,CAACxB,iBAAiB,KAAK,CAAC,CAAC,EAAE;QAC/B,MAAM,IAAI7e,KAAK,CAAC,gCAAgC,CAAC;MACrD;IACJ,CAAC,MACI,IAAIkhB,cAAc,KAAK,MAAM,EAAE;MAChC,IAAI,CAACrC,iBAAiB,EAAE;MACxB,IAAI,CAACsE,qBAAqB,GAAG,IAAI,CAACtE,iBAAiB,CAAC,CAAC;MACrDmG,YAAY,CAACnkB,IAAI,CAAC,GAAG,IAAI,CAAC+d,UAAU,CAACqG,MAAM,CAAC,IAAI,CAACpG,iBAAiB,CAAC,CAAC;IACxE,CAAC,MACI,IAAIqC,cAAc,KAAK,SAAS,EAAE;MACnC8D,YAAY,CAACnkB,IAAI,CAACkkB,aAAa,CAAC;IACpC;IACA,IAAI7D,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,SAAS,EAAE;MAC3D,MAAMb,KAAK,GAAG,IAAI,CAACxB,iBAAiB;MACpC,MAAMtW,GAAG,GAAG2Y,cAAc,KAAK,MAAM,GAAGZ,MAAM,CAAC,IAAI,CAACC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC5B,YAAY,CAACpW,GAAG;MACtF,MAAM2c,MAAM,GAAG,IAAIhF,0BAA0B,CAAC,IAAI,CAACP,WAAW,EAAE2B,WAAW,CAAC/Q,GAAG,EAAE;QAC7E3H,EAAE,EAAE0X,MAAM,CAAC,IAAI,CAACE,MAAM,EAAE,CAAC;QACzBjY,GAAG;QACH8X,KAAK;QACLI,YAAY,EAAE,IAAI;QAClB/gB,KAAK,EAAE4hB,WAAW,CAAC2B,QAAQ,CAAC,CAAC;QAC7BpD,YAAY,EAAEyB,WAAW,CAAC4B,eAAe,CAAC;MAC9C,CAAC,CAAC;MACF,IAAI,CAACtE,UAAU,CAAC,IAAI,CAACC,iBAAiB,CAAC,GAAGqG,MAAM;IACpD;IACA5J,MAAM,CAAC6J,gBAAgB,CAAC,IAAI,CAACxG,YAAY,CAAC;IAC1C,MAAMyG,uBAAuB,GAAGC,2CAA2C,CAAC;MACxEC,IAAI,EAAEP,aAAa;MACnB7D,cAAc,EAAEA;IACpB,CAAC,CAAC;IACF,IAAI,CAACvB,WAAW,CAACsE,aAAa,CAACmB,uBAAuB,CAAC;IACvD,KAAK,MAAMG,WAAW,IAAIP,YAAY,EAAE;MACpCO,WAAW,CAACpB,OAAO,CAAC,CAAC;IACzB;EACJ;EACA;EACAxB,SAASA,CAACpa,GAAG,EAAE;IACX,KAAK,MAAMma,KAAK,IAAI,IAAI,CAAC9D,UAAU,EAAE;MACjC,IAAI8D,KAAK,CAACna,GAAG,KAAKA,GAAG,EACjB,OAAOma,KAAK;IACpB;IACA,OAAO1d,SAAS;EACpB;EACA,IAAIwgB,UAAUA;EACd;EACAC,QAAQ,EAAE;IACN,MAAM,IAAIzlB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA;EACA,IAAIwlB,UAAUA,CAAA,EAAG;IACb,MAAM,IAAIxlB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI0lB,oBAAoBA,CAACD,QAAQ,EAAE;IAC/B,MAAM,IAAIzlB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI0lB,oBAAoBA,CAAA,EAAG;IACvB,MAAM,IAAI1lB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI2lB,iBAAiBA;EACrB;EACAF,QAAQ,EAAE;IACN,MAAM,IAAIzlB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA;EACA,IAAI2lB,iBAAiBA,CAAA,EAAG;IACpB,MAAM,IAAI3lB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI4lB,eAAeA;EACnB;EACAH,QAAQ,EAAE;IACN,MAAM,IAAIzlB,KAAK,CAAC,eAAe,CAAC;EACpC;EACA;EACA,IAAI4lB,eAAeA,CAAA,EAAG;IAClB,MAAM,IAAI5lB,KAAK,CAAC,eAAe,CAAC;EACpC;EAEA;EACA,IAAI6lB,UAAUA,CAACpK,CAAC,EAAE;IACd,IAAI,CAACqK,WAAW,GAAGrK,CAAC;EACxB;EACA,IAAIoK,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACAC,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,MAAM,IAAIhmB,KAAK,CAAC,eAAe,CAAC;EACpC;EACAimB,MAAMA,CAACD,QAAQ,EAAE;IACb,MAAM,IAAIhmB,KAAK,CAAC,eAAe,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA,MAAMkgB,0BAA0B,CAAC;EAW7BniB,WAAWA,CAAC4hB,WAAW,EAAEpP,GAAG,EAAE;IAAE3H,EAAE;IAAEL,GAAG;IAAE8X,KAAK;IAAEI,YAAY;IAAE/gB,KAAK;IAAEmgB;EAAc,CAAC,EAAE;IAAA7hB,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAFtF;IAAAA,eAAA,oBACY,IAAI;IAEZ,IAAI,CAAC2hB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACpP,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC3H,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACL,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC8X,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACI,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC/gB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACmgB,YAAY,GAAGA,YAAY;EACpC;EACAoD,QAAQA,CAAA,EAAG;IACP;IACA,OAAO,IAAI,CAACvjB,KAAK,GAAG+I,IAAI,CAACyd,KAAK,CAACzd,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChJ,KAAK,CAAC,CAAC,GAAG,IAAI,CAACA,KAAK;EAC3E;EACAwjB,eAAeA,CAAA,EAAG;IACd;IACA,OAAO,IAAI,CAACrD,YAAY,GAClBpX,IAAI,CAACyd,KAAK,CAACzd,IAAI,CAACC,SAAS,CAAC,IAAI,CAACmX,YAAY,CAAC,CAAC,GAC7C,IAAI,CAACA,YAAY;EAC3B;EACAiE,gBAAgBA,CAACzkB,IAAI,EAAE0kB,QAAQ,EAAEhe,OAAO,EAAE;IACtC,IAAI,CAAC4Z,WAAW,CAACmE,gBAAgB,CAACzkB,IAAI,EAAE0kB,QAAQ,EAAEhe,OAAO,CAAC;EAC9D;EACAie,mBAAmBA,CAAC3kB,IAAI,EAAE0kB,QAAQ,EAAEhe,OAAO,EAAE;IACzC,IAAI,CAAC4Z,WAAW,CAACqE,mBAAmB,CAAC3kB,IAAI,EAAE0kB,QAAQ,EAAEhe,OAAO,CAAC;EACjE;EACAke,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAO,IAAI,CAACvE,WAAW,CAACsE,aAAa,CAACC,KAAK,CAAC;EAChD;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,MAAMgC,YAAY,GAAG,IAAIC,KAAK,CAAC,UAAU,CAAC;IAC1C,IAAI,CAACnC,aAAa,CAACkC,YAAY,CAAC;IAChC;IACA,IAAI,CAACxG,WAAW,GAAG,IAAI;EAC3B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4E,qBAAqBA,CAAC;EAAE5C,UAAU;EAAEC,YAAY;EAAEC,aAAa;EAAET,UAAU;EAAEF,cAAc;EAAEsD,MAAM;EAAElD,WAAW;EAAEQ,IAAI;EAAErB,YAAY;EAAEnF;AAAQ,CAAC,EAAE;EACtJ,MAAM;IAAE+K;EAAW,CAAC,GAAG/K,MAAM;EAC7B,MAAM4I,KAAK,GAAG,IAAIkC,KAAK,CAAC,UAAU,EAAE;IAAEE,OAAO,EAAE,KAAK;IAAE3E;EAAW,CAAC,CAAC;EACnEuC,KAAK,CAACqC,kBAAkB,GAAG,IAAI;EAC/BrC,KAAK,CAACsC,cAAc,GAAG,IAAI;EAC3BtC,KAAK,CAACuC,iBAAiB,GAAG,MAAM;EAChCvC,KAAK,CAACtC,YAAY,GAAGA,YAAY;EACjCsC,KAAK,CAACrC,aAAa,GAAGA,aAAa;EACnCqC,KAAK,CAAC9C,UAAU,GAAGA,UAAU;EAC7B8C,KAAK,CAAChD,cAAc,GAAGA,cAAc;EACrCgD,KAAK,CAACM,MAAM,GAAGA,MAAM;EACrBN,KAAK,CAAC5C,WAAW,GAAGA,WAAW;EAC/B4C,KAAK,CAACpC,IAAI,GAAGA,IAAI;EACjBoC,KAAK,CAACwC,eAAe,GAAG,IAAI;EAC5BxC,KAAK,CAACyC,QAAQ,GAAG,IAAI;EACrBzC,KAAK,CAAC5I,MAAM,GAAGA,MAAM;EACrB4I,KAAK,CAACzD,YAAY,GAAGA,YAAY;EACjC,IAAImG,iBAAiB,GAAG,EAAE;EAC1B,IAAIC,QAAQ,GAAG,EAAE;EACjB;EACA3C,KAAK,CAAC4C,SAAS,GAAG,UAAU/gB,OAAO,EAAE;IAAA,IAAAghB,mBAAA,EAAAC,eAAA;IACjC,IAAI,CAAC,IAAI,CAACpF,YAAY,EAAE;MACpB,MAAM,IAAIiB,YAAY,CAAC,+CAA+C,EAAE,eAAe,CAAC;IAC5F;IACA,IAAI,CAAC4D,iBAAiB,GAAG,aAAa;IACtCvC,KAAK,CAACzD,YAAY,GAAG,IAAI;IACzB,MAAMwG,gBAAgB,GAAGlhB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkhB,gBAAgB;IAClD,IAAIA,gBAAgB,EAAE;MAClB,IAAI,CAAC,IAAI,CAACtF,UAAU,EAAE;QAClB,MAAM,IAAIkB,YAAY,CAAC,wDAAwD,EAAE,mBAAmB,CAAC;MACzG;MACA+D,iBAAiB,CAAC/lB,IAAI,CAAComB,gBAAgB,CAAC;IAC5C;IACA,IAAI/C,KAAK,CAACuC,iBAAiB,KAAK,MAAM,IAAIvC,KAAK,CAACuC,iBAAiB,KAAK,aAAa,EAAE;MACjF,MAAM,IAAIzmB,KAAK,CAAC,2DAA2D,CAAC;IAChF;IACAkkB,KAAK,CAACuC,iBAAiB,GAAG,aAAa;IACvC,MAAM7Q,OAAO,GAAG7P,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6P,OAAO;IAChC,IAAIA,OAAO,EAAE;MACTiR,QAAQ,CAAChmB,IAAI,CAAC+U,OAAO,CAAC;IAC1B;IACA;IACAsO,KAAK,CAACqC,kBAAkB,IAAAQ,mBAAA,GAAGhhB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmhB,UAAU,cAAAH,mBAAA,cAAAA,mBAAA,GAAI7C,KAAK,CAACqC,kBAAkB;IAC1ErC,KAAK,CAACsC,cAAc,IAAAQ,eAAA,GAAGjhB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEohB,MAAM,cAAAH,eAAA,cAAAA,eAAA,GAAI9C,KAAK,CAACsC,cAAc;EAClE,CAAC;EACD;EACAtC,KAAK,CAACiD,MAAM,GAAG,YAAY;IACvB,IAAIjD,KAAK,CAACuC,iBAAiB,KAAK,WAAW,EAAE;MACzC,MAAM,IAAI5D,YAAY,CAAC,kEAAkE,GACrF,4EAA4E,EAAE,mBAAmB,CAAC;IAC1G;IACAuE,qBAAqB,CAAClD,KAAK,CAAC;EAChC,CAAC;EACD;EACA,SAASmD,QAAQA,CAAC9W,GAAG,EAAE;IACnB,IAAI2T,KAAK,CAACuC,iBAAiB,KAAK,MAAM,EAAE;MACpC,MAAM,IAAIzmB,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACA,IAAIkkB,KAAK,CAACuC,iBAAiB,KAAK,aAAa,EAAE;MAC3C,MAAM,IAAI5D,YAAY,CAAC,0DAA0D,EAAE,mBAAmB,CAAC;IAC3G;IACA,IAAIqB,KAAK,CAAChD,cAAc,KAAK,MAAM,IAAIgD,KAAK,CAAChD,cAAc,KAAK,SAAS,EAAE;MACvE,MAAM,IAAI2B,YAAY,CAAC,+DAA+D,EAAE,mBAAmB,CAAC;IAChH;IACA,MAAM5B,KAAK,GAAG,IAAId,GAAG,CAAC5P,GAAG,EAAE8V,UAAU,CAAC1H,YAAY,CAACpO,GAAG,CAAC;IACvD2T,KAAK,CAAC5C,WAAW,CAAC/Q,GAAG,GAAG0Q,KAAK,CAACqG,IAAI;EACtC;EACA;EACA;EACA,SAASC,MAAMA,CAAA,EAAG;IACd,IAAIjM,MAAM,CAACkJ,MAAM,CAACgD,OAAO,EAAE;MACvB;IACJ;IACA,IAAItD,KAAK,CAACuC,iBAAiB,KAAK,MAAM,EAAE;MACpCvC,KAAK,CAACuC,iBAAiB,GAAG,WAAW;MACrC,IAAI,CAACJ,UAAU,CAAC1H,YAAY,EAAE;QAC1B,MAAM,IAAI3e,KAAK,CAAC,uCAAuC,CAAC;MAC5D;MACAqmB,UAAU,CAACR,UAAU,GAAG,IAAI4B,4BAA4B,CAACpB,UAAU,CAAC1H,YAAY,EAAEuC,cAAc,CAAC;MACjG,QAAQgD,KAAK,CAAChD,cAAc;QACxB,KAAK,MAAM;QACX,KAAK,SAAS;UAAE;YACZmF,UAAU,CAAC5B,wBAAwB,CAACP,KAAK,CAAC;YAC1C;UACJ;QACA,KAAK,QAAQ;UAAE;YACXmC,UAAU,CAACtE,gDAAgD,CAACmC,KAAK,CAAC;YAClE;UACJ;QACA,KAAK,UAAU;UAAE;YACbmC,UAAU,CAAChD,iBAAiB,CAACa,KAAK,CAAC;YACnC;UACJ;MACJ;IACJ;IACA,MAAMwD,YAAY,GAAGb,QAAQ,CAACrL,GAAG,CAAE5F,OAAO,IAAKA,OAAO,CAAC,CAAC,CAAC;IACzD,IAAI8R,YAAY,CAAC9mB,MAAM,KAAK,CAAC,EAAE;MAC3B8mB,YAAY,CAAC7mB,IAAI,CAACpD,OAAO,CAACqD,OAAO,CAAC,CAAC,CAAC;IACxC;IACArD,OAAO,CAACoS,GAAG,CAAC6X,YAAY,CAAC,CACpB7iB,IAAI,CAAC,MAAM;MACZ;MACA;MACA,IAAIyW,MAAM,CAACkJ,MAAM,CAACgD,OAAO,EAAE;QACvB;MACJ;MACA,IAAItD,KAAK,KAAKmC,UAAU,CAACrE,aAAa,EAAE;QACpC,MAAM,IAAIhiB,KAAK,CAAC,wDAAwD,CAAC;MAC7E;MACAqmB,UAAU,CAACrE,aAAa,GAAG,IAAI;MAC/B2F,qBAAqB,CAACzD,KAAK,EAAE,IAAI,CAAC;MAClC,MAAM0D,oBAAoB,GAAG,IAAIxB,KAAK,CAAC,iBAAiB,EAAE;QAAEE,OAAO,EAAE,KAAK;QAAE3E;MAAW,CAAC,CAAC;MACzF0E,UAAU,CAAC1G,WAAW,CAACsE,aAAa,CAAC2D,oBAAoB,CAAC;MAC1DtM,MAAM,CAACuM,eAAe,CAAC,CAAC;MACxB,IAAIxB,UAAU,CAACR,UAAU,KAAK,IAAI,EAAE;QAChCQ,UAAU,CAACR,UAAU,CAACgC,eAAe,CAAC,CAAC;MAC3C;MACAxB,UAAU,CAACR,UAAU,GAAG,IAAI;IAChC,CAAC,CAAC,CACG/C,KAAK,CAAEgF,MAAM,IAAK5D,KAAK,CAACI,MAAM,CAACwD,MAAM,CAAC,CAAC;EAChD;EACA;EACA;EACA;EACA5D,KAAK,CAACI,MAAM,GAAG,UAAUwD,MAAM,EAAE;IAC7B,IAAIxM,MAAM,CAACkJ,MAAM,CAACgD,OAAO,EAAE;MACvB;IACJ;IACA,IAAItD,KAAK,KAAKmC,UAAU,CAACrE,aAAa,EAAE;MACpC,MAAM,IAAIhiB,KAAK,CAAC,wDAAwD,CAAC;IAC7E;IACAqmB,UAAU,CAACrE,aAAa,GAAG,IAAI;IAC/B,IAAIkC,KAAK,CAACuC,iBAAiB,KAAK,aAAa,EAAE;MAC3CkB,qBAAqB,CAACzD,KAAK,EAAE,KAAK,CAAC;IACvC;IACA,MAAM6D,kBAAkB,GAAG,IAAI3B,KAAK,CAAC,eAAe,EAAE;MAAEE,OAAO,EAAE,KAAK;MAAE3E;IAAW,CAAC,CAAC;IACrF0E,UAAU,CAAC1G,WAAW,CAACsE,aAAa,CAAC8D,kBAAkB,CAAC;IACxDzM,MAAM,CAAC0M,cAAc,CAACF,MAAM,CAAC;IAC7B,IAAIzB,UAAU,CAACR,UAAU,KAAK,IAAI,EAAE;MAChCQ,UAAU,CAACR,UAAU,CAACmC,cAAc,CAACF,MAAM,CAAC;IAChD;IACAzB,UAAU,CAACR,UAAU,GAAG,IAAI;EAChC,CAAC;EACD,SAASoC,QAAQA,CAAA,EAAG;IAChB5B,UAAU,CAACrE,aAAa,GAAGkC,KAAK;IAChCmC,UAAU,CAAC1G,WAAW,CAACsE,aAAa,CAACC,KAAK,CAAC;IAC3C,IAAI0C,iBAAiB,CAAChmB,MAAM,KAAK,CAAC,EAAE;MAChC2mB,MAAM,CAAC,CAAC;IACZ,CAAC,MACI;MACD,MAAMW,mBAAmB,GAAG;QAAEb;MAAS,CAAC;MACxC,MAAMc,qBAAqB,GAAGvB,iBAAiB,CAACpL,GAAG,CAAE5F,OAAO,IAAKA,OAAO,CAACsS,mBAAmB,CAAC,CAAC;MAC9FzqB,OAAO,CAACoS,GAAG,CAACsY,qBAAqB,CAAC,CAC7BtjB,IAAI,CAAC,MAAM0iB,MAAM,CAAC,CAAC,CAAC,CACpBzE,KAAK,CAAEgF,MAAM,IAAK5D,KAAK,CAACI,MAAM,CAACwD,MAAM,CAAC,CAAC;IAChD;EACJ;EACAG,QAAQ,CAAC,CAAC;EACV,OAAO/D,KAAK,CAACuC,iBAAiB,KAAK,MAAM;AAC7C;AACA;AACA,SAASkB,qBAAqBA,CAACzD,KAAK,EAAEkE,UAAU,EAAE;EAC9C,IAAIlE,KAAK,CAACuC,iBAAiB,KAAK,UAAU,EAAE;IACxC,MAAM,IAAIzmB,KAAK,CAAC,iEAAiE,CAAC;EACtF;EACA,IAAIkkB,KAAK,CAACuC,iBAAiB,KAAK,aAAa,EAAE;IAC3C,IAAI2B,UAAU,KAAK,IAAI,EAAE;MACrB,MAAM,IAAIpoB,KAAK,CAAC,4BAA4B,CAAC;IACjD;IACA;IACAkkB,KAAK,CAACuC,iBAAiB,GAAG,UAAU;IACpC;EACJ;EACA,IAAIvC,KAAK,CAACuC,iBAAiB,KAAK,MAAM,EAAE;IACpC;EACJ;EACA4B,qBAAqB,CAACnE,KAAK,CAAC;EAC5B,IAAIkE,UAAU,EAAE;IACZE,sBAAsB,CAACpE,KAAK,CAAC;EACjC;EACAA,KAAK,CAACuC,iBAAiB,GAAG,UAAU;AACxC;AACA;AACA,SAAS4B,qBAAqBA,CAACnE,KAAK,EAAE;EAClC,IAAIA,KAAK,CAACuC,iBAAiB,KAAK,WAAW,IAAIvC,KAAK,CAACuC,iBAAiB,KAAK,UAAU,EAAE;IACnF,MAAM,IAAIzmB,KAAK,CAAC,qEAAqE,CAAC;EAC1F;EACA;AACJ;AACA,SAASsoB,sBAAsBA,CAACpE,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACuC,iBAAiB,KAAK,WAAW,IAAIvC,KAAK,CAACuC,iBAAiB,KAAK,UAAU,EAAE;IACnF,MAAM,IAAIzmB,KAAK,CAAC,sEAAsE,CAAC;EAC3F;EACA,IAAIkkB,KAAK,CAACuC,iBAAiB,KAAK,UAAU,IAAIvC,KAAK,CAACsC,cAAc,KAAK,QAAQ,EAAE;IAC7E;EACJ;EACAY,qBAAqB,CAAClD,KAAK,CAAC;AAChC;AACA;AACA,SAASkD,qBAAqBA,CAAClD,KAAK,EAAE;EAClC,IAAIA,KAAK,CAACuC,iBAAiB,KAAK,WAAW,EAAE;IACzC,MAAM,IAAIzmB,KAAK,CAAC,kEAAkE,CAAC;EACvF;EACAkkB,KAAK,CAACuC,iBAAiB,GAAG,UAAU;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA,SAASpB,2CAA2CA,CAAC;EAAEC,IAAI;EAAEpE;AAAgB,CAAC,EAAE;EAC5E,MAAMgD,KAAK,GAAG,IAAIkC,KAAK,CAAC,oBAAoB,EAAE;IAC1CE,OAAO,EAAE,KAAK;IACd3E,UAAU,EAAE;EAChB,CAAC,CAAC;EACFuC,KAAK,CAACoB,IAAI,GAAGA,IAAI;EACjBpB,KAAK,CAAChD,cAAc,GAAGA,cAAc;EACrC,OAAOgD,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,SAASU,mBAAmBA,CAAC;EAAEllB;AAAM,CAAC,EAAE;EACpC,MAAMwkB,KAAK,GAAG,IAAIkC,KAAK,CAAC,UAAU,EAAE;IAChCE,OAAO,EAAE,KAAK;IACd3E,UAAU,EAAE;EAChB,CAAC,CAAC;EACFuC,KAAK,CAACxkB,KAAK,GAAGA,KAAK;EACnB,OAAOwkB,KAAK;AAChB;AACA,SAASY,qBAAqBA,CAACyD,MAAM,EAAEC,MAAM,EAAE;EAC3C,MAAMtE,KAAK,GAAG,IAAIkC,KAAK,CAAC,YAAY,EAAE;IAClCE,OAAO,EAAE,KAAK;IACd3E,UAAU,EAAE;EAChB,CAAC,CAAC;EACFuC,KAAK,CAACqE,MAAM,GAAGA,MAAM;EACrBrE,KAAK,CAACsE,MAAM,GAAGA,MAAM;EACrB,OAAOtE,KAAK;AAChB;AACA;AACA;AACA;AACA,MAAM3C,yBAAyB,CAAC;EAQ5BxjB,WAAWA,CAAC;IAAEwS,GAAG;IAAEkQ,YAAY;IAAEZ,YAAY;IAAEngB,KAAK;IAAE6I,GAAG,GAAG,IAAI;IAAEK,EAAE,GAAG,IAAI;IAAEyX,KAAK,GAAG,CAAC;EAAG,CAAC,EAAE;IAAAriB,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACxF,IAAI,CAACuS,GAAG,GAAGA,GAAG;IACd,IAAI,CAACkQ,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC/gB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACmgB,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACtX,GAAG,GAAGA,GAAG;IACd,IAAI,CAACK,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACyX,KAAK,GAAGA,KAAK;EACtB;EACA4C,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACvjB,KAAK;EACrB;EACAwjB,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrD,YAAY;EAC5B;AACJ;AACA;AACA,SAASwB,YAAYA,CAACiE,IAAI,EAAEmD,EAAE,EAAE;EAC5B,OAAQA,EAAE,CAACC,IAAI,KAAKpD,IAAI,CAACoD,IAAI,IACzBD,EAAE,CAACE,QAAQ,KAAKrD,IAAI,CAACqD,QAAQ,IAC7BF,EAAE,CAACG,QAAQ,KAAKtD,IAAI,CAACsD,QAAQ,IAC7BH,EAAE,CAACI,MAAM,KAAKvD,IAAI,CAACuD,MAAM;AACjC;AACA,MAAMpB,4BAA4B,CAAC;EAM/B1pB,WAAWA,CAACunB,IAAI,EAAEpE,cAAc,EAAE;IAAAljB,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAC9B,IAAI,CAACsnB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACpE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACgB,QAAQ,GAAG,IAAIzkB,OAAO,CAAC,CAACqD,OAAO,EAAEpD,MAAM,KAAK;MAC7C,IAAI,CAACsqB,cAAc,GAAGtqB,MAAM;MAC5B,IAAI,CAACmqB,eAAe,GAAG/mB,OAAO;IAClC,CAAC,CAAC;IACF;IACA,IAAI,CAACohB,QAAQ,CAACY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMtB,wBAAwB,CAAC;EAS3B,IAAIgD,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACsE,eAAe,CAACtE,MAAM;EACtC;EAEAzmB,WAAWA,CAACsoB,UAAU,EAAE;IAAA,IAAA0C,MAAA;IAAA/qB,eAAA;IAAAA,eAAA,sBAXV,IAAI;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,0BAUA,IAAIgrB,eAAe,CAAC,CAAC;IAEnC,IAAI,CAAC3C,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACpE,SAAS,GAAG,IAAIxkB,OAAO,CAAC,CAACqD,OAAO,EAAEpD,MAAM,KAAK;MAC9C,IAAI,CAACynB,gBAAgB,GAAIzC,KAAK,IAAK;QAC/B,IAAI,CAACuG,WAAW,GAAGvG,KAAK;QACxB5hB,OAAO,CAAC4hB,KAAK,CAAC;MAClB,CAAC;MACD,IAAI,CAACwG,eAAe,GAAGxrB,MAAM;IACjC,CAAC,CAAC;IACF,IAAI,CAACwkB,QAAQ,GAAG,IAAIzkB,OAAO;MAAA,IAAA0rB,KAAA,GAAAvpB,iBAAA,CAAC,WAAOkB,OAAO,EAAEpD,MAAM,EAAK;QACnDqrB,MAAI,CAAClB,eAAe,GAAG,MAAM;UACzB,IAAIkB,MAAI,CAACE,WAAW,KAAK,IAAI,EAAE;YAC3B,MAAM,IAAIjpB,KAAK,CAAC,6EAA6E,CAAC;UAClG;UACAc,OAAO,CAACioB,MAAI,CAACE,WAAW,CAAC;QAC7B,CAAC;QACDF,MAAI,CAACf,cAAc,GAAIF,MAAM,IAAK;UAC9BpqB,MAAM,CAACoqB,MAAM,CAAC;UACdiB,MAAI,CAACD,eAAe,CAACM,KAAK,CAACtB,MAAM,CAAC;QACtC,CAAC;MACL,CAAC;MAAA,iBAAAuB,EAAA,EAAAC,GAAA;QAAA,OAAAH,KAAA,CAAAzN,KAAA,OAAA6N,SAAA;MAAA;IAAA,IAAC;IACF;IACA,IAAI,CAACtH,SAAS,CAACa,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IAC/B,IAAI,CAACZ,QAAQ,CAACY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;EAClC;AACJ;AAEA,MAAM0G,GAAG,CAAC;EAENzrB,WAAWA,CAAA,EAAG;IAAAC,eAAA;IACV,IAAI,CAACyrB,QAAQ,GAAG,EAAE;EACtB;EACArmB,GAAGA,CAACsB,KAAK,EAAE;IACP,IAAI,CAAC+kB,QAAQ,CAAC5oB,IAAI,CAAC6D,KAAK,CAAC;EAC7B;EACApH,EAAEA,CAACoH,KAAK,EAAE;IACN,OAAO,MAAM;MACT,IAAI,CAAC+kB,QAAQ,CAAC5oB,IAAI,CAAC6D,KAAK,CAAC;IAC7B,CAAC;EACL;EACA/F,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC8qB,QAAQ,GAAG,EAAE;EACtB;EACAnO,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACmO,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;EACnC;AAGJ;AAACC,IAAA,GArBKH,GAAG;AAAAxrB,eAAA,CAAHwrB,GAAG,eAmBS,SAASI,WAAWA,CAAC9qB,iBAAiB,EAAE;EAAE,OAAO,KAAKA,iBAAiB,IAAI0qB,IAAG,EAAE,CAAC;AAAE,CAAC;AAAAxrB,eAAA,CAnBhGwrB,GAAG,gBAoBU,aAAcv0B,EAAE,CAAC8J,kBAAkB,CAAC;EAAEC,KAAK,EAAEwqB,IAAG;EAAEvqB,OAAO,EAAEuqB,IAAG,CAACtqB;AAAK,CAAC,CAAC;AAEzF,CAAC,MAAM;EAAE,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKlK,EAAE,CAACmK,iBAAiB,CAACoqB,GAAG,EAAE,CAAC;IAC/EnqB,IAAI,EAAE/J;EACV,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AAAE,CAAC,EAAE,CAAC;AAE7B,SAASyM,gBAAgB,EAAEF,0BAA0B,EAAEC,wBAAwB,EAAExC,iBAAiB,EAAEue,kBAAkB,EAAEzC,OAAO,EAAE3Z,qBAAqB,EAAEgd,oCAAoC,EAAErY,oBAAoB,EAAEN,SAAS,EAAE7B,KAAK,EAAEoC,eAAe,EAAEkS,UAAU,EAAErjB,MAAM,EAAEwQ,kBAAkB,EAAEvB,IAAI,EAAE9G,YAAY,EAAE4gB,UAAU,EAAES,cAAc,IAAImL,eAAe,EAAEL,GAAG,IAAIM,IAAI,EAAEvjB,iBAAiB,IAAIwjB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}