{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nvar VirtualTimeScheduler = function (_super) {\n  __extends(VirtualTimeScheduler, _super);\n  function VirtualTimeScheduler(schedulerActionCtor, maxFrames) {\n    if (schedulerActionCtor === void 0) {\n      schedulerActionCtor = VirtualAction;\n    }\n    if (maxFrames === void 0) {\n      maxFrames = Infinity;\n    }\n    var _this = _super.call(this, schedulerActionCtor, function () {\n      return _this.frame;\n    }) || this;\n    _this.maxFrames = maxFrames;\n    _this.frame = 0;\n    _this.index = -1;\n    return _this;\n  }\n  VirtualTimeScheduler.prototype.flush = function () {\n    var _a = this,\n      actions = _a.actions,\n      maxFrames = _a.maxFrames;\n    var error;\n    var action;\n    while ((action = actions[0]) && action.delay <= maxFrames) {\n      actions.shift();\n      this.frame = action.delay;\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    }\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  VirtualTimeScheduler.frameTimeFactor = 10;\n  return VirtualTimeScheduler;\n}(AsyncScheduler);\nexport { VirtualTimeScheduler };\nvar VirtualAction = function (_super) {\n  __extends(VirtualAction, _super);\n  function VirtualAction(scheduler, work, index) {\n    if (index === void 0) {\n      index = scheduler.index += 1;\n    }\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    _this.index = index;\n    _this.active = true;\n    _this.index = scheduler.index = index;\n    return _this;\n  }\n  VirtualAction.prototype.schedule = function (state, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (Number.isFinite(delay)) {\n      if (!this.id) {\n        return _super.prototype.schedule.call(this, state, delay);\n      }\n      this.active = false;\n      var action = new VirtualAction(this.scheduler, this.work);\n      this.add(action);\n      return action.schedule(state, delay);\n    } else {\n      return Subscription.EMPTY;\n    }\n  };\n  VirtualAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    this.delay = scheduler.frame + delay;\n    var actions = scheduler.actions;\n    actions.push(this);\n    actions.sort(VirtualAction.sortActions);\n    return 1;\n  };\n  VirtualAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return undefined;\n  };\n  VirtualAction.prototype._execute = function (state, delay) {\n    if (this.active === true) {\n      return _super.prototype._execute.call(this, state, delay);\n    }\n  };\n  VirtualAction.sortActions = function (a, b) {\n    if (a.delay === b.delay) {\n      if (a.index === b.index) {\n        return 0;\n      } else if (a.index > b.index) {\n        return 1;\n      } else {\n        return -1;\n      }\n    } else if (a.delay > b.delay) {\n      return 1;\n    } else {\n      return -1;\n    }\n  };\n  return VirtualAction;\n}(AsyncAction);\nexport { VirtualAction };\n//# sourceMappingURL=VirtualTimeScheduler.js.map", "map": {"version": 3, "names": ["__extends", "AsyncAction", "Subscription", "AsyncScheduler", "VirtualTimeScheduler", "_super", "schedulerActionCtor", "maxFrames", "VirtualAction", "Infinity", "_this", "call", "frame", "index", "prototype", "flush", "_a", "actions", "error", "action", "delay", "shift", "execute", "state", "unsubscribe", "frameTimeFactor", "scheduler", "work", "active", "schedule", "Number", "isFinite", "id", "add", "EMPTY", "requestAsyncId", "push", "sort", "sortActions", "recycleAsyncId", "undefined", "_execute", "a", "b"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/scheduler/VirtualTimeScheduler.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nvar VirtualTimeScheduler = (function (_super) {\n    __extends(VirtualTimeScheduler, _super);\n    function VirtualTimeScheduler(schedulerActionCtor, maxFrames) {\n        if (schedulerActionCtor === void 0) { schedulerActionCtor = VirtualAction; }\n        if (maxFrames === void 0) { maxFrames = Infinity; }\n        var _this = _super.call(this, schedulerActionCtor, function () { return _this.frame; }) || this;\n        _this.maxFrames = maxFrames;\n        _this.frame = 0;\n        _this.index = -1;\n        return _this;\n    }\n    VirtualTimeScheduler.prototype.flush = function () {\n        var _a = this, actions = _a.actions, maxFrames = _a.maxFrames;\n        var error;\n        var action;\n        while ((action = actions[0]) && action.delay <= maxFrames) {\n            actions.shift();\n            this.frame = action.delay;\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        }\n        if (error) {\n            while ((action = actions.shift())) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    };\n    VirtualTimeScheduler.frameTimeFactor = 10;\n    return VirtualTimeScheduler;\n}(AsyncScheduler));\nexport { VirtualTimeScheduler };\nvar VirtualAction = (function (_super) {\n    __extends(VirtualAction, _super);\n    function VirtualAction(scheduler, work, index) {\n        if (index === void 0) { index = (scheduler.index += 1); }\n        var _this = _super.call(this, scheduler, work) || this;\n        _this.scheduler = scheduler;\n        _this.work = work;\n        _this.index = index;\n        _this.active = true;\n        _this.index = scheduler.index = index;\n        return _this;\n    }\n    VirtualAction.prototype.schedule = function (state, delay) {\n        if (delay === void 0) { delay = 0; }\n        if (Number.isFinite(delay)) {\n            if (!this.id) {\n                return _super.prototype.schedule.call(this, state, delay);\n            }\n            this.active = false;\n            var action = new VirtualAction(this.scheduler, this.work);\n            this.add(action);\n            return action.schedule(state, delay);\n        }\n        else {\n            return Subscription.EMPTY;\n        }\n    };\n    VirtualAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        this.delay = scheduler.frame + delay;\n        var actions = scheduler.actions;\n        actions.push(this);\n        actions.sort(VirtualAction.sortActions);\n        return 1;\n    };\n    VirtualAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        return undefined;\n    };\n    VirtualAction.prototype._execute = function (state, delay) {\n        if (this.active === true) {\n            return _super.prototype._execute.call(this, state, delay);\n        }\n    };\n    VirtualAction.sortActions = function (a, b) {\n        if (a.delay === b.delay) {\n            if (a.index === b.index) {\n                return 0;\n            }\n            else if (a.index > b.index) {\n                return 1;\n            }\n            else {\n                return -1;\n            }\n        }\n        else if (a.delay > b.delay) {\n            return 1;\n        }\n        else {\n            return -1;\n        }\n    };\n    return VirtualAction;\n}(AsyncAction));\nexport { VirtualAction };\n//# sourceMappingURL=VirtualTimeScheduler.js.map"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,IAAIC,oBAAoB,GAAI,UAAUC,MAAM,EAAE;EAC1CL,SAAS,CAACI,oBAAoB,EAAEC,MAAM,CAAC;EACvC,SAASD,oBAAoBA,CAACE,mBAAmB,EAAEC,SAAS,EAAE;IAC1D,IAAID,mBAAmB,KAAK,KAAK,CAAC,EAAE;MAAEA,mBAAmB,GAAGE,aAAa;IAAE;IAC3E,IAAID,SAAS,KAAK,KAAK,CAAC,EAAE;MAAEA,SAAS,GAAGE,QAAQ;IAAE;IAClD,IAAIC,KAAK,GAAGL,MAAM,CAACM,IAAI,CAAC,IAAI,EAAEL,mBAAmB,EAAE,YAAY;MAAE,OAAOI,KAAK,CAACE,KAAK;IAAE,CAAC,CAAC,IAAI,IAAI;IAC/FF,KAAK,CAACH,SAAS,GAAGA,SAAS;IAC3BG,KAAK,CAACE,KAAK,GAAG,CAAC;IACfF,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC;IAChB,OAAOH,KAAK;EAChB;EACAN,oBAAoB,CAACU,SAAS,CAACC,KAAK,GAAG,YAAY;IAC/C,IAAIC,EAAE,GAAG,IAAI;MAAEC,OAAO,GAAGD,EAAE,CAACC,OAAO;MAAEV,SAAS,GAAGS,EAAE,CAACT,SAAS;IAC7D,IAAIW,KAAK;IACT,IAAIC,MAAM;IACV,OAAO,CAACA,MAAM,GAAGF,OAAO,CAAC,CAAC,CAAC,KAAKE,MAAM,CAACC,KAAK,IAAIb,SAAS,EAAE;MACvDU,OAAO,CAACI,KAAK,CAAC,CAAC;MACf,IAAI,CAACT,KAAK,GAAGO,MAAM,CAACC,KAAK;MACzB,IAAKF,KAAK,GAAGC,MAAM,CAACG,OAAO,CAACH,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACC,KAAK,CAAC,EAAG;QACtD;MACJ;IACJ;IACA,IAAIF,KAAK,EAAE;MACP,OAAQC,MAAM,GAAGF,OAAO,CAACI,KAAK,CAAC,CAAC,EAAG;QAC/BF,MAAM,CAACK,WAAW,CAAC,CAAC;MACxB;MACA,MAAMN,KAAK;IACf;EACJ,CAAC;EACDd,oBAAoB,CAACqB,eAAe,GAAG,EAAE;EACzC,OAAOrB,oBAAoB;AAC/B,CAAC,CAACD,cAAc,CAAE;AAClB,SAASC,oBAAoB;AAC7B,IAAII,aAAa,GAAI,UAAUH,MAAM,EAAE;EACnCL,SAAS,CAACQ,aAAa,EAAEH,MAAM,CAAC;EAChC,SAASG,aAAaA,CAACkB,SAAS,EAAEC,IAAI,EAAEd,KAAK,EAAE;IAC3C,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAIa,SAAS,CAACb,KAAK,IAAI,CAAE;IAAE;IACxD,IAAIH,KAAK,GAAGL,MAAM,CAACM,IAAI,CAAC,IAAI,EAAEe,SAAS,EAAEC,IAAI,CAAC,IAAI,IAAI;IACtDjB,KAAK,CAACgB,SAAS,GAAGA,SAAS;IAC3BhB,KAAK,CAACiB,IAAI,GAAGA,IAAI;IACjBjB,KAAK,CAACG,KAAK,GAAGA,KAAK;IACnBH,KAAK,CAACkB,MAAM,GAAG,IAAI;IACnBlB,KAAK,CAACG,KAAK,GAAGa,SAAS,CAACb,KAAK,GAAGA,KAAK;IACrC,OAAOH,KAAK;EAChB;EACAF,aAAa,CAACM,SAAS,CAACe,QAAQ,GAAG,UAAUN,KAAK,EAAEH,KAAK,EAAE;IACvD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAIU,MAAM,CAACC,QAAQ,CAACX,KAAK,CAAC,EAAE;MACxB,IAAI,CAAC,IAAI,CAACY,EAAE,EAAE;QACV,OAAO3B,MAAM,CAACS,SAAS,CAACe,QAAQ,CAAClB,IAAI,CAAC,IAAI,EAAEY,KAAK,EAAEH,KAAK,CAAC;MAC7D;MACA,IAAI,CAACQ,MAAM,GAAG,KAAK;MACnB,IAAIT,MAAM,GAAG,IAAIX,aAAa,CAAC,IAAI,CAACkB,SAAS,EAAE,IAAI,CAACC,IAAI,CAAC;MACzD,IAAI,CAACM,GAAG,CAACd,MAAM,CAAC;MAChB,OAAOA,MAAM,CAACU,QAAQ,CAACN,KAAK,EAAEH,KAAK,CAAC;IACxC,CAAC,MACI;MACD,OAAOlB,YAAY,CAACgC,KAAK;IAC7B;EACJ,CAAC;EACD1B,aAAa,CAACM,SAAS,CAACqB,cAAc,GAAG,UAAUT,SAAS,EAAEM,EAAE,EAAEZ,KAAK,EAAE;IACrE,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAI,CAACA,KAAK,GAAGM,SAAS,CAACd,KAAK,GAAGQ,KAAK;IACpC,IAAIH,OAAO,GAAGS,SAAS,CAACT,OAAO;IAC/BA,OAAO,CAACmB,IAAI,CAAC,IAAI,CAAC;IAClBnB,OAAO,CAACoB,IAAI,CAAC7B,aAAa,CAAC8B,WAAW,CAAC;IACvC,OAAO,CAAC;EACZ,CAAC;EACD9B,aAAa,CAACM,SAAS,CAACyB,cAAc,GAAG,UAAUb,SAAS,EAAEM,EAAE,EAAEZ,KAAK,EAAE;IACrE,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,OAAOoB,SAAS;EACpB,CAAC;EACDhC,aAAa,CAACM,SAAS,CAAC2B,QAAQ,GAAG,UAAUlB,KAAK,EAAEH,KAAK,EAAE;IACvD,IAAI,IAAI,CAACQ,MAAM,KAAK,IAAI,EAAE;MACtB,OAAOvB,MAAM,CAACS,SAAS,CAAC2B,QAAQ,CAAC9B,IAAI,CAAC,IAAI,EAAEY,KAAK,EAAEH,KAAK,CAAC;IAC7D;EACJ,CAAC;EACDZ,aAAa,CAAC8B,WAAW,GAAG,UAAUI,CAAC,EAAEC,CAAC,EAAE;IACxC,IAAID,CAAC,CAACtB,KAAK,KAAKuB,CAAC,CAACvB,KAAK,EAAE;MACrB,IAAIsB,CAAC,CAAC7B,KAAK,KAAK8B,CAAC,CAAC9B,KAAK,EAAE;QACrB,OAAO,CAAC;MACZ,CAAC,MACI,IAAI6B,CAAC,CAAC7B,KAAK,GAAG8B,CAAC,CAAC9B,KAAK,EAAE;QACxB,OAAO,CAAC;MACZ,CAAC,MACI;QACD,OAAO,CAAC,CAAC;MACb;IACJ,CAAC,MACI,IAAI6B,CAAC,CAACtB,KAAK,GAAGuB,CAAC,CAACvB,KAAK,EAAE;MACxB,OAAO,CAAC;IACZ,CAAC,MACI;MACD,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,OAAOZ,aAAa;AACxB,CAAC,CAACP,WAAW,CAAE;AACf,SAASO,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}