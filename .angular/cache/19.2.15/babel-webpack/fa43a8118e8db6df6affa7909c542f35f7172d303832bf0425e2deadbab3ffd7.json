{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatInput, _MatInputModule;\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform, getSupportedInputTypes } from '@angular/cdk/platform';\nimport { AutofillMonitor, TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Renderer2, isSignal, effect, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { M as MAT_INPUT_VALUE_ACCESSOR } from './input-value-accessor-D1GvPuqO.mjs';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-DqPi4knt.mjs';\nexport { b as MatError, j as MatFormField, c as MatHint, M as MatLabel, e as MatPrefix, g as MatSuffix } from './form-field-DqPi4knt.mjs';\nimport { E as ErrorStateMatcher } from './error-options-Dm2JJUbF.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatFormFieldModule } from './module-BXZhw7pQ.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/observers';\n\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n  return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = ['button', 'checkbox', 'file', 'hidden', 'image', 'radio', 'range', 'reset', 'submit'];\n/** Injection token that can be used to provide the default options for the input. */\nconst MAT_INPUT_CONFIG = new InjectionToken('MAT_INPUT_CONFIG');\nclass MatInput {\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    // Browsers may not fire the blur event if the input is disabled too quickly.\n    // Reset from here to ensure that the element doesn't become stuck.\n    if (this.focused) {\n      this.focused = false;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get required() {\n    var _ref, _this$_required, _this$ngControl;\n    return (_ref = (_this$_required = this._required) !== null && _this$_required !== void 0 ? _this$_required : (_this$ngControl = this.ngControl) === null || _this$ngControl === void 0 || (_this$ngControl = _this$ngControl.control) === null || _this$ngControl === void 0 ? void 0 : _this$ngControl.hasValidator(Validators.required)) !== null && _ref !== void 0 ? _ref : false;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n  }\n  /** Input type of the element. */\n  get type() {\n    return this._type;\n  }\n  set type(value) {\n    const prevType = this._type;\n    this._type = value || 'text';\n    this._validateType();\n    // When using Angular inputs, developers are no longer able to set the properties on the native\n    // input element. To ensure that bindings for `type` work, we need to sync the setter\n    // with the native property. Textarea elements don't support the type property or attribute.\n    if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n      this._elementRef.nativeElement.type = this._type;\n    }\n    if (this._type !== prevType) {\n      this._ensureWheelDefaultBehavior();\n    }\n  }\n  /** An object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get value() {\n    return this._signalBasedValueAccessor ? this._signalBasedValueAccessor.value() : this._inputValueAccessor.value;\n  }\n  set value(value) {\n    if (value !== this.value) {\n      if (this._signalBasedValueAccessor) {\n        this._signalBasedValueAccessor.value.set(value);\n      } else {\n        this._inputValueAccessor.value = value;\n      }\n      this.stateChanges.next();\n    }\n  }\n  /** Whether the element is readonly. */\n  get readonly() {\n    return this._readonly;\n  }\n  set readonly(value) {\n    this._readonly = coerceBooleanProperty(value);\n  }\n  /** Whether the input is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  constructor() {\n    var _this$_config;\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"ngControl\", inject(NgControl, {\n      optional: true,\n      self: true\n    }));\n    _defineProperty(this, \"_autofillMonitor\", inject(AutofillMonitor));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_formField\", inject(MAT_FORM_FIELD, {\n      optional: true\n    }));\n    _defineProperty(this, \"_renderer\", inject(Renderer2));\n    _defineProperty(this, \"_uid\", inject(_IdGenerator).getId('mat-input-'));\n    _defineProperty(this, \"_previousNativeValue\", void 0);\n    _defineProperty(this, \"_inputValueAccessor\", void 0);\n    _defineProperty(this, \"_signalBasedValueAccessor\", void 0);\n    _defineProperty(this, \"_previousPlaceholder\", void 0);\n    _defineProperty(this, \"_errorStateTracker\", void 0);\n    _defineProperty(this, \"_config\", inject(MAT_INPUT_CONFIG, {\n      optional: true\n    }));\n    _defineProperty(this, \"_cleanupIosKeyup\", void 0);\n    _defineProperty(this, \"_cleanupWebkitWheel\", void 0);\n    /** `aria-describedby` IDs assigned by the form field. */\n    _defineProperty(this, \"_formFieldDescribedBy\", void 0);\n    /** Whether the component is being rendered on the server. */\n    _defineProperty(this, \"_isServer\", void 0);\n    /** Whether the component is a native html select. */\n    _defineProperty(this, \"_isNativeSelect\", void 0);\n    /** Whether the component is a textarea. */\n    _defineProperty(this, \"_isTextarea\", void 0);\n    /** Whether the input is inside of a form field. */\n    _defineProperty(this, \"_isInFormField\", void 0);\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    _defineProperty(this, \"focused\", false);\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    _defineProperty(this, \"stateChanges\", new Subject());\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    _defineProperty(this, \"controlType\", 'mat-input');\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    _defineProperty(this, \"autofilled\", false);\n    _defineProperty(this, \"_disabled\", false);\n    _defineProperty(this, \"_id\", void 0);\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    _defineProperty(this, \"placeholder\", void 0);\n    /**\n     * Name of the input.\n     * @docs-private\n     */\n    _defineProperty(this, \"name\", void 0);\n    _defineProperty(this, \"_required\", void 0);\n    _defineProperty(this, \"_type\", 'text');\n    _defineProperty(this, \"userAriaDescribedBy\", void 0);\n    _defineProperty(this, \"_readonly\", false);\n    /** Whether the input should remain interactive when it is disabled. */\n    _defineProperty(this, \"disabledInteractive\", void 0);\n    _defineProperty(this, \"_neverEmptyInputTypes\", ['date', 'datetime', 'datetime-local', 'month', 'time', 'week'].filter(t => getSupportedInputTypes().has(t)));\n    _defineProperty(this, \"_iOSKeyupListener\", event => {\n      const el = event.target;\n      // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n      // indicate different things. If the value is 0, it means that the caret is at the start\n      // of the input, whereas a value of `null` means that the input doesn't support\n      // manipulating the selection range. Inputs that don't support setting the selection range\n      // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n      // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n      if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n        // Note: Just setting `0, 0` doesn't fix the issue. Setting\n        // `1, 1` fixes it for the first time that you type text and\n        // then hold delete. Toggling to `1, 1` and then back to\n        // `0, 0` seems to completely fix it.\n        el.setSelectionRange(1, 1);\n        el.setSelectionRange(0, 0);\n      }\n    });\n    _defineProperty(this, \"_webkitBlinkWheelListener\", () => {\n      // This is a noop function and is used to enable mouse wheel input\n      // on number inputs\n      // on blink and webkit browsers.\n    });\n    const parentForm = inject(NgForm, {\n      optional: true\n    });\n    const parentFormGroup = inject(FormGroupDirective, {\n      optional: true\n    });\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n    const accessor = inject(MAT_INPUT_VALUE_ACCESSOR, {\n      optional: true,\n      self: true\n    });\n    const element = this._elementRef.nativeElement;\n    const nodeName = element.nodeName.toLowerCase();\n    if (accessor) {\n      if (isSignal(accessor.value)) {\n        this._signalBasedValueAccessor = accessor;\n      } else {\n        this._inputValueAccessor = accessor;\n      }\n    } else {\n      // If no input value accessor was explicitly specified, use the element as the input value\n      // accessor.\n      this._inputValueAccessor = element;\n    }\n    this._previousNativeValue = this.value;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n    // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n    // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n    // exists on iOS, we only bother to install the listener on iOS.\n    if (this._platform.IOS) {\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupIosKeyup = this._renderer.listen(element, 'keyup', this._iOSKeyupListener);\n      });\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeSelect = nodeName === 'select';\n    this._isTextarea = nodeName === 'textarea';\n    this._isInFormField = !!this._formField;\n    this.disabledInteractive = ((_this$_config = this._config) === null || _this$_config === void 0 ? void 0 : _this$_config.disabledInteractive) || false;\n    if (this._isNativeSelect) {\n      this.controlType = element.multiple ? 'mat-native-select-multiple' : 'mat-native-select';\n    }\n    if (this._signalBasedValueAccessor) {\n      effect(() => {\n        // Read the value so the effect can register the dependency.\n        this._signalBasedValueAccessor.value();\n        this.stateChanges.next();\n      });\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n        this.autofilled = event.isAutofilled;\n        this.stateChanges.next();\n      });\n    }\n  }\n  ngOnChanges() {\n    this.stateChanges.next();\n  }\n  ngOnDestroy() {\n    var _this$_cleanupIosKeyu, _this$_cleanupWebkitW;\n    this.stateChanges.complete();\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n    }\n    (_this$_cleanupIosKeyu = this._cleanupIosKeyup) === null || _this$_cleanupIosKeyu === void 0 || _this$_cleanupIosKeyu.call(this);\n    (_this$_cleanupWebkitW = this._cleanupWebkitWheel) === null || _this$_cleanupWebkitW === void 0 || _this$_cleanupWebkitW.call(this);\n  }\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n      // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n      // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n      // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n      // disabled.\n      if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n        this.disabled = this.ngControl.disabled;\n        this.stateChanges.next();\n      }\n    }\n    // We need to dirty-check the native element's value, because there are some cases where\n    // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n    // updating the value using `emitEvent: false`).\n    this._dirtyCheckNativeValue();\n    // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n    // present or not depends on a query which is prone to \"changed after checked\" errors.\n    this._dirtyCheckPlaceholder();\n  }\n  /** Focuses the input. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Refreshes the error state of the input. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Callback for the cases where the focused state of the input changes. */\n  _focusChanged(isFocused) {\n    if (isFocused === this.focused) {\n      return;\n    }\n    if (!this._isNativeSelect && isFocused && this.disabled && this.disabledInteractive) {\n      const element = this._elementRef.nativeElement;\n      // Focusing an input that has text will cause all the text to be selected. Clear it since\n      // the user won't be able to change it. This is based on the internal implementation.\n      if (element.type === 'number') {\n        // setSelectionRange doesn't work on number inputs so it needs to be set briefly to text.\n        element.type = 'text';\n        element.setSelectionRange(0, 0);\n        element.type = 'number';\n      } else {\n        element.setSelectionRange(0, 0);\n      }\n    }\n    this.focused = isFocused;\n    this.stateChanges.next();\n  }\n  _onInput() {\n    // This is a noop function and is used to let Angular know whenever the value changes.\n    // Angular will run a new change detection each time the `input` event has been dispatched.\n    // It's necessary that Angular recognizes the value change, because when floatingLabel\n    // is set to false and Angular forms aren't used, the placeholder won't recognize the\n    // value changes and will not disappear.\n    // Listening to the input event wouldn't be necessary when the input is using the\n    // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n  }\n  /** Does some manual dirty checking on the native input `value` property. */\n  _dirtyCheckNativeValue() {\n    const newValue = this._elementRef.nativeElement.value;\n    if (this._previousNativeValue !== newValue) {\n      this._previousNativeValue = newValue;\n      this.stateChanges.next();\n    }\n  }\n  /** Does some manual dirty checking on the native input `placeholder` attribute. */\n  _dirtyCheckPlaceholder() {\n    const placeholder = this._getPlaceholder();\n    if (placeholder !== this._previousPlaceholder) {\n      const element = this._elementRef.nativeElement;\n      this._previousPlaceholder = placeholder;\n      placeholder ? element.setAttribute('placeholder', placeholder) : element.removeAttribute('placeholder');\n    }\n  }\n  /** Gets the current placeholder of the form field. */\n  _getPlaceholder() {\n    return this.placeholder || null;\n  }\n  /** Make sure the input is a supported type. */\n  _validateType() {\n    if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatInputUnsupportedTypeError(this._type);\n    }\n  }\n  /** Checks whether the input type is one of the types that are never empty. */\n  _isNeverEmpty() {\n    return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n  }\n  /** Checks whether the input is invalid based on the native validation. */\n  _isBadInput() {\n    // The `validity` property won't be present on platform-server.\n    let validity = this._elementRef.nativeElement.validity;\n    return validity && validity.badInput;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get empty() {\n    return !this._isNeverEmpty() && !this._elementRef.nativeElement.value && !this._isBadInput() && !this.autofilled;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    if (this._isNativeSelect) {\n      // For a single-selection `<select>`, the label should float when the selected option has\n      // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n      // overlapping the label with the options.\n      const selectElement = this._elementRef.nativeElement;\n      const firstOption = selectElement.options[0];\n      // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n      // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n      return this.focused || selectElement.multiple || !this.empty || !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label);\n    } else {\n      return this.focused && !this.disabled || !this.empty;\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    const element = this._elementRef.nativeElement;\n    const existingDescribedBy = element.getAttribute('aria-describedby');\n    let toAssign;\n    // In some cases there might be some `aria-describedby` IDs that were assigned directly,\n    // like by the `AriaDescriber` (see #30011). Attempt to preserve them by taking the previous\n    // attribute value and filtering out the IDs that came from the previous `setDescribedByIds`\n    // call. Note the `|| ids` here allows us to avoid duplicating IDs on the first render.\n    if (existingDescribedBy) {\n      const exclude = this._formFieldDescribedBy || ids;\n      toAssign = ids.concat(existingDescribedBy.split(' ').filter(id => id && !exclude.includes(id)));\n    } else {\n      toAssign = ids;\n    }\n    this._formFieldDescribedBy = ids;\n    if (toAssign.length) {\n      element.setAttribute('aria-describedby', toAssign.join(' '));\n    } else {\n      element.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n    // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n    // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n    if (!this.focused) {\n      this.focus();\n    }\n  }\n  /** Whether the form control is a native select that is displayed inline. */\n  _isInlineSelect() {\n    const element = this._elementRef.nativeElement;\n    return this._isNativeSelect && (element.multiple || element.size > 1);\n  }\n  /**\n   * In blink and webkit browsers a focused number input does not increment or decrement its value\n   * on mouse wheel interaction unless a wheel event listener is attached to it or one of its\n   * ancestors or a passive wheel listener is attached somewhere in the DOM. For example: Hitting\n   * a tooltip once enables the mouse wheel input for all number inputs as long as it exists. In\n   * order to get reliable and intuitive behavior we apply a wheel event on our own thus making\n   * sure increment and decrement by mouse wheel works every time.\n   * @docs-private\n   */\n  _ensureWheelDefaultBehavior() {\n    var _this$_cleanupWebkitW2;\n    (_this$_cleanupWebkitW2 = this._cleanupWebkitWheel) === null || _this$_cleanupWebkitW2 === void 0 || _this$_cleanupWebkitW2.call(this);\n    if (this._type === 'number' && (this._platform.BLINK || this._platform.WEBKIT)) {\n      this._cleanupWebkitWheel = this._renderer.listen(this._elementRef.nativeElement, 'wheel', this._webkitBlinkWheelListener);\n    }\n  }\n  /** Gets the value to set on the `readonly` attribute. */\n  _getReadonlyAttribute() {\n    if (this._isNativeSelect) {\n      return null;\n    }\n    if (this.readonly || this.disabled && this.disabledInteractive) {\n      return 'true';\n    }\n    return null;\n  }\n}\n_MatInput = MatInput;\n_defineProperty(MatInput, \"\\u0275fac\", function _MatInput_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatInput)();\n});\n_defineProperty(MatInput, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatInput,\n  selectors: [[\"input\", \"matInput\", \"\"], [\"textarea\", \"matInput\", \"\"], [\"select\", \"matNativeControl\", \"\"], [\"input\", \"matNativeControl\", \"\"], [\"textarea\", \"matNativeControl\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-input-element\"],\n  hostVars: 21,\n  hostBindings: function _MatInput_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focus\", function _MatInput_focus_HostBindingHandler() {\n        return ctx._focusChanged(true);\n      })(\"blur\", function _MatInput_blur_HostBindingHandler() {\n        return ctx._focusChanged(false);\n      })(\"input\", function _MatInput_input_HostBindingHandler() {\n        return ctx._onInput();\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"required\", ctx.required);\n      i0.ɵɵattribute(\"name\", ctx.name || null)(\"readonly\", ctx._getReadonlyAttribute())(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null)(\"aria-invalid\", ctx.empty && ctx.required ? null : ctx.errorState)(\"aria-required\", ctx.required)(\"id\", ctx.id);\n      i0.ɵɵclassProp(\"mat-input-server\", ctx._isServer)(\"mat-mdc-form-field-textarea-control\", ctx._isInFormField && ctx._isTextarea)(\"mat-mdc-form-field-input-control\", ctx._isInFormField)(\"mat-mdc-input-disabled-interactive\", ctx.disabledInteractive)(\"mdc-text-field__input\", ctx._isInFormField)(\"mat-mdc-native-select-inline\", ctx._isInlineSelect());\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    id: \"id\",\n    placeholder: \"placeholder\",\n    name: \"name\",\n    required: \"required\",\n    type: \"type\",\n    errorStateMatcher: \"errorStateMatcher\",\n    userAriaDescribedBy: [0, \"aria-describedby\", \"userAriaDescribedBy\"],\n    value: \"value\",\n    readonly: \"readonly\",\n    disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n  },\n  exportAs: [\"matInput\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MatFormFieldControl,\n    useExisting: _MatInput\n  }]), i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInput, [{\n    type: Directive,\n    args: [{\n      selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n      exportAs: 'matInput',\n      host: {\n        'class': 'mat-mdc-input-element',\n        // The BaseMatInput parent class adds `mat-input-element`, `mat-form-field-control` and\n        // `mat-form-field-autofill-control` to the CSS class list, but this should not be added for\n        // this MDC equivalent input.\n        '[class.mat-input-server]': '_isServer',\n        '[class.mat-mdc-form-field-textarea-control]': '_isInFormField && _isTextarea',\n        '[class.mat-mdc-form-field-input-control]': '_isInFormField',\n        '[class.mat-mdc-input-disabled-interactive]': 'disabledInteractive',\n        '[class.mdc-text-field__input]': '_isInFormField',\n        '[class.mat-mdc-native-select-inline]': '_isInlineSelect()',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[id]': 'id',\n        '[disabled]': 'disabled && !disabledInteractive',\n        '[required]': 'required',\n        '[attr.name]': 'name || null',\n        '[attr.readonly]': '_getReadonlyAttribute()',\n        '[attr.aria-disabled]': 'disabled && disabledInteractive ? \"true\" : null',\n        // Only mark the input as invalid for assistive technology if it has a value since the\n        // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n        '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n        '[attr.aria-required]': 'required',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[attr.id]': 'id',\n        '(focus)': '_focusChanged(true)',\n        '(blur)': '_focusChanged(false)',\n        '(input)': '_onInput()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatInput\n      }]\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    value: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatInputModule {}\n_MatInputModule = MatInputModule;\n_defineProperty(MatInputModule, \"\\u0275fac\", function _MatInputModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatInputModule)();\n});\n_defineProperty(MatInputModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatInputModule,\n  imports: [MatCommonModule, MatFormFieldModule, MatInput],\n  exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule]\n}));\n_defineProperty(MatInputModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatFormFieldModule, MatFormFieldModule, TextFieldModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInputModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatFormFieldModule, MatInput],\n      exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_INPUT_CONFIG, MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };\n//# sourceMappingURL=input.mjs.map", "map": {"version": 3, "names": ["coerceBooleanProperty", "Platform", "getSupportedInputTypes", "AutofillMonitor", "TextFieldModule", "i0", "InjectionToken", "inject", "ElementRef", "NgZone", "Renderer2", "isSignal", "effect", "booleanAttribute", "Directive", "Input", "NgModule", "_IdGenerator", "NgControl", "Validators", "NgForm", "FormGroupDirective", "Subject", "M", "MAT_INPUT_VALUE_ACCESSOR", "h", "MAT_FORM_FIELD", "k", "MatFormFieldControl", "b", "<PERSON><PERSON><PERSON><PERSON>", "j", "MatFormField", "c", "MatHint", "<PERSON><PERSON><PERSON><PERSON>", "e", "MatPrefix", "g", "MatSuffix", "E", "ErrorStateMatcher", "_", "_ErrorStateTracker", "MatFormFieldModule", "MatCommonModule", "getMatInputUnsupportedTypeError", "type", "Error", "MAT_INPUT_INVALID_TYPES", "MAT_INPUT_CONFIG", "MatInput", "disabled", "_disabled", "value", "focused", "stateChanges", "next", "id", "_id", "_uid", "required", "_ref", "_this$_required", "_this$ngControl", "_required", "ngControl", "control", "hasValidator", "_type", "prevType", "_validateType", "_isTextarea", "has", "_elementRef", "nativeElement", "_ensureWheelDefaultBehavior", "errorStateMatcher", "_errorStateTracker", "matcher", "_signalBasedValueAccessor", "_inputValueAccessor", "set", "readonly", "_readonly", "errorState", "constructor", "_this$_config", "_defineProperty", "optional", "self", "getId", "filter", "t", "event", "el", "target", "selectionStart", "selectionEnd", "setSelectionRange", "parentForm", "parentFormGroup", "defaultErrorStateMatcher", "accessor", "element", "nodeName", "toLowerCase", "_previousNativeValue", "_platform", "IOS", "_ngZone", "runOutsideAngular", "_cleanupIosKeyup", "_renderer", "listen", "_iOSKeyupListener", "_isServer", "<PERSON><PERSON><PERSON><PERSON>", "_isNativeSelect", "_isInFormField", "_formField", "disabledInteractive", "_config", "controlType", "multiple", "ngAfterViewInit", "_autofillMonitor", "monitor", "subscribe", "autofilled", "isAutofilled", "ngOnChanges", "ngOnDestroy", "_this$_cleanupIosKeyu", "_this$_cleanupWebkitW", "complete", "stopMonitoring", "call", "_cleanupWebkitWheel", "ngDoCheck", "updateErrorState", "_dirtyCheckNativeValue", "_dirtyCheckPlaceholder", "focus", "options", "_focusChanged", "isFocused", "_onInput", "newValue", "placeholder", "_getPlaceholder", "_previousPlaceholder", "setAttribute", "removeAttribute", "indexOf", "ngDevMode", "_isNeverEmpty", "_neverEmptyInputTypes", "_isBadInput", "validity", "badInput", "empty", "shouldLabelFloat", "selectElement", "firstOption", "selectedIndex", "label", "setDescribedByIds", "ids", "existingDescribedBy", "getAttribute", "toAs<PERSON>", "exclude", "_formFieldDescribedBy", "concat", "split", "includes", "length", "join", "onContainerClick", "_isInlineSelect", "size", "_this$_cleanupWebkitW2", "BLINK", "WEBKIT", "_webkitBlinkWheelListener", "_getReadonlyAttribute", "_MatInput", "_MatInput_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "selectors", "hostAttrs", "hostVars", "hostBindings", "_MatInput_HostBindings", "rf", "ctx", "ɵɵlistener", "_MatInput_focus_HostBindingHandler", "_MatInput_blur_HostBindingHandler", "_MatInput_input_HostBindingHandler", "ɵɵhostProperty", "ɵɵattribute", "name", "ɵɵclassProp", "inputs", "userAriaDescribedBy", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "args", "selector", "host", "providers", "transform", "MatInputModule", "_MatInputModule", "_MatInputModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/input.mjs"], "sourcesContent": ["import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform, getSupportedInputTypes } from '@angular/cdk/platform';\nimport { AutofillMonitor, TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Renderer2, isSignal, effect, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { M as MAT_INPUT_VALUE_ACCESSOR } from './input-value-accessor-D1GvPuqO.mjs';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-DqPi4knt.mjs';\nexport { b as <PERSON><PERSON><PERSON><PERSON>, j as <PERSON><PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as Mat<PERSON>refix, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-DqPi4knt.mjs';\nimport { E as ErrorStateMatcher } from './error-options-Dm2JJUbF.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatFormFieldModule } from './module-BXZhw7pQ.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/observers';\n\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n    return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = [\n    'button',\n    'checkbox',\n    'file',\n    'hidden',\n    'image',\n    'radio',\n    'range',\n    'reset',\n    'submit',\n];\n/** Injection token that can be used to provide the default options for the input. */\nconst MAT_INPUT_CONFIG = new InjectionToken('MAT_INPUT_CONFIG');\nclass MatInput {\n    _elementRef = inject(ElementRef);\n    _platform = inject(Platform);\n    ngControl = inject(NgControl, { optional: true, self: true });\n    _autofillMonitor = inject(AutofillMonitor);\n    _ngZone = inject(NgZone);\n    _formField = inject(MAT_FORM_FIELD, { optional: true });\n    _renderer = inject(Renderer2);\n    _uid = inject(_IdGenerator).getId('mat-input-');\n    _previousNativeValue;\n    _inputValueAccessor;\n    _signalBasedValueAccessor;\n    _previousPlaceholder;\n    _errorStateTracker;\n    _config = inject(MAT_INPUT_CONFIG, { optional: true });\n    _cleanupIosKeyup;\n    _cleanupWebkitWheel;\n    /** `aria-describedby` IDs assigned by the form field. */\n    _formFieldDescribedBy;\n    /** Whether the component is being rendered on the server. */\n    _isServer;\n    /** Whether the component is a native html select. */\n    _isNativeSelect;\n    /** Whether the component is a textarea. */\n    _isTextarea;\n    /** Whether the input is inside of a form field. */\n    _isInFormField;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    focused = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    stateChanges = new Subject();\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    controlType = 'mat-input';\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    autofilled = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // Browsers may not fire the blur event if the input is disabled too quickly.\n        // Reset from here to ensure that the element doesn't become stuck.\n        if (this.focused) {\n            this.focused = false;\n            this.stateChanges.next();\n        }\n    }\n    _disabled = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n    }\n    _id;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    placeholder;\n    /**\n     * Name of the input.\n     * @docs-private\n     */\n    name;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n    }\n    _required;\n    /** Input type of the element. */\n    get type() {\n        return this._type;\n    }\n    set type(value) {\n        const prevType = this._type;\n        this._type = value || 'text';\n        this._validateType();\n        // When using Angular inputs, developers are no longer able to set the properties on the native\n        // input element. To ensure that bindings for `type` work, we need to sync the setter\n        // with the native property. Textarea elements don't support the type property or attribute.\n        if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n            this._elementRef.nativeElement.type = this._type;\n        }\n        if (this._type !== prevType) {\n            this._ensureWheelDefaultBehavior();\n        }\n    }\n    _type = 'text';\n    /** An object used to control when error messages are shown. */\n    get errorStateMatcher() {\n        return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n        this._errorStateTracker.matcher = value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    userAriaDescribedBy;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n        return this._signalBasedValueAccessor\n            ? this._signalBasedValueAccessor.value()\n            : this._inputValueAccessor.value;\n    }\n    set value(value) {\n        if (value !== this.value) {\n            if (this._signalBasedValueAccessor) {\n                this._signalBasedValueAccessor.value.set(value);\n            }\n            else {\n                this._inputValueAccessor.value = value;\n            }\n            this.stateChanges.next();\n        }\n    }\n    /** Whether the element is readonly. */\n    get readonly() {\n        return this._readonly;\n    }\n    set readonly(value) {\n        this._readonly = coerceBooleanProperty(value);\n    }\n    _readonly = false;\n    /** Whether the input should remain interactive when it is disabled. */\n    disabledInteractive;\n    /** Whether the input is in an error state. */\n    get errorState() {\n        return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n        this._errorStateTracker.errorState = value;\n    }\n    _neverEmptyInputTypes = [\n        'date',\n        'datetime',\n        'datetime-local',\n        'month',\n        'time',\n        'week',\n    ].filter(t => getSupportedInputTypes().has(t));\n    constructor() {\n        const parentForm = inject(NgForm, { optional: true });\n        const parentFormGroup = inject(FormGroupDirective, { optional: true });\n        const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n        const accessor = inject(MAT_INPUT_VALUE_ACCESSOR, { optional: true, self: true });\n        const element = this._elementRef.nativeElement;\n        const nodeName = element.nodeName.toLowerCase();\n        if (accessor) {\n            if (isSignal(accessor.value)) {\n                this._signalBasedValueAccessor = accessor;\n            }\n            else {\n                this._inputValueAccessor = accessor;\n            }\n        }\n        else {\n            // If no input value accessor was explicitly specified, use the element as the input value\n            // accessor.\n            this._inputValueAccessor = element;\n        }\n        this._previousNativeValue = this.value;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n        // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n        // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n        // exists on iOS, we only bother to install the listener on iOS.\n        if (this._platform.IOS) {\n            this._ngZone.runOutsideAngular(() => {\n                this._cleanupIosKeyup = this._renderer.listen(element, 'keyup', this._iOSKeyupListener);\n            });\n        }\n        this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n        this._isServer = !this._platform.isBrowser;\n        this._isNativeSelect = nodeName === 'select';\n        this._isTextarea = nodeName === 'textarea';\n        this._isInFormField = !!this._formField;\n        this.disabledInteractive = this._config?.disabledInteractive || false;\n        if (this._isNativeSelect) {\n            this.controlType = element.multiple\n                ? 'mat-native-select-multiple'\n                : 'mat-native-select';\n        }\n        if (this._signalBasedValueAccessor) {\n            effect(() => {\n                // Read the value so the effect can register the dependency.\n                this._signalBasedValueAccessor.value();\n                this.stateChanges.next();\n            });\n        }\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n                this.autofilled = event.isAutofilled;\n                this.stateChanges.next();\n            });\n        }\n    }\n    ngOnChanges() {\n        this.stateChanges.next();\n    }\n    ngOnDestroy() {\n        this.stateChanges.complete();\n        if (this._platform.isBrowser) {\n            this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n        }\n        this._cleanupIosKeyup?.();\n        this._cleanupWebkitWheel?.();\n    }\n    ngDoCheck() {\n        if (this.ngControl) {\n            // We need to re-evaluate this on every change detection cycle, because there are some\n            // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n            // that whatever logic is in here has to be super lean or we risk destroying the performance.\n            this.updateErrorState();\n            // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n            // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n            // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n            // disabled.\n            if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n                this.disabled = this.ngControl.disabled;\n                this.stateChanges.next();\n            }\n        }\n        // We need to dirty-check the native element's value, because there are some cases where\n        // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n        // updating the value using `emitEvent: false`).\n        this._dirtyCheckNativeValue();\n        // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n        // present or not depends on a query which is prone to \"changed after checked\" errors.\n        this._dirtyCheckPlaceholder();\n    }\n    /** Focuses the input. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Refreshes the error state of the input. */\n    updateErrorState() {\n        this._errorStateTracker.updateErrorState();\n    }\n    /** Callback for the cases where the focused state of the input changes. */\n    _focusChanged(isFocused) {\n        if (isFocused === this.focused) {\n            return;\n        }\n        if (!this._isNativeSelect && isFocused && this.disabled && this.disabledInteractive) {\n            const element = this._elementRef.nativeElement;\n            // Focusing an input that has text will cause all the text to be selected. Clear it since\n            // the user won't be able to change it. This is based on the internal implementation.\n            if (element.type === 'number') {\n                // setSelectionRange doesn't work on number inputs so it needs to be set briefly to text.\n                element.type = 'text';\n                element.setSelectionRange(0, 0);\n                element.type = 'number';\n            }\n            else {\n                element.setSelectionRange(0, 0);\n            }\n        }\n        this.focused = isFocused;\n        this.stateChanges.next();\n    }\n    _onInput() {\n        // This is a noop function and is used to let Angular know whenever the value changes.\n        // Angular will run a new change detection each time the `input` event has been dispatched.\n        // It's necessary that Angular recognizes the value change, because when floatingLabel\n        // is set to false and Angular forms aren't used, the placeholder won't recognize the\n        // value changes and will not disappear.\n        // Listening to the input event wouldn't be necessary when the input is using the\n        // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n    }\n    /** Does some manual dirty checking on the native input `value` property. */\n    _dirtyCheckNativeValue() {\n        const newValue = this._elementRef.nativeElement.value;\n        if (this._previousNativeValue !== newValue) {\n            this._previousNativeValue = newValue;\n            this.stateChanges.next();\n        }\n    }\n    /** Does some manual dirty checking on the native input `placeholder` attribute. */\n    _dirtyCheckPlaceholder() {\n        const placeholder = this._getPlaceholder();\n        if (placeholder !== this._previousPlaceholder) {\n            const element = this._elementRef.nativeElement;\n            this._previousPlaceholder = placeholder;\n            placeholder\n                ? element.setAttribute('placeholder', placeholder)\n                : element.removeAttribute('placeholder');\n        }\n    }\n    /** Gets the current placeholder of the form field. */\n    _getPlaceholder() {\n        return this.placeholder || null;\n    }\n    /** Make sure the input is a supported type. */\n    _validateType() {\n        if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatInputUnsupportedTypeError(this._type);\n        }\n    }\n    /** Checks whether the input type is one of the types that are never empty. */\n    _isNeverEmpty() {\n        return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n    }\n    /** Checks whether the input is invalid based on the native validation. */\n    _isBadInput() {\n        // The `validity` property won't be present on platform-server.\n        let validity = this._elementRef.nativeElement.validity;\n        return validity && validity.badInput;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n        return (!this._isNeverEmpty() &&\n            !this._elementRef.nativeElement.value &&\n            !this._isBadInput() &&\n            !this.autofilled);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        if (this._isNativeSelect) {\n            // For a single-selection `<select>`, the label should float when the selected option has\n            // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n            // overlapping the label with the options.\n            const selectElement = this._elementRef.nativeElement;\n            const firstOption = selectElement.options[0];\n            // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n            // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n            return (this.focused ||\n                selectElement.multiple ||\n                !this.empty ||\n                !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label));\n        }\n        else {\n            return (this.focused && !this.disabled) || !this.empty;\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        const element = this._elementRef.nativeElement;\n        const existingDescribedBy = element.getAttribute('aria-describedby');\n        let toAssign;\n        // In some cases there might be some `aria-describedby` IDs that were assigned directly,\n        // like by the `AriaDescriber` (see #30011). Attempt to preserve them by taking the previous\n        // attribute value and filtering out the IDs that came from the previous `setDescribedByIds`\n        // call. Note the `|| ids` here allows us to avoid duplicating IDs on the first render.\n        if (existingDescribedBy) {\n            const exclude = this._formFieldDescribedBy || ids;\n            toAssign = ids.concat(existingDescribedBy.split(' ').filter(id => id && !exclude.includes(id)));\n        }\n        else {\n            toAssign = ids;\n        }\n        this._formFieldDescribedBy = ids;\n        if (toAssign.length) {\n            element.setAttribute('aria-describedby', toAssign.join(' '));\n        }\n        else {\n            element.removeAttribute('aria-describedby');\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n        // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n        // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n        if (!this.focused) {\n            this.focus();\n        }\n    }\n    /** Whether the form control is a native select that is displayed inline. */\n    _isInlineSelect() {\n        const element = this._elementRef.nativeElement;\n        return this._isNativeSelect && (element.multiple || element.size > 1);\n    }\n    _iOSKeyupListener = (event) => {\n        const el = event.target;\n        // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n        // indicate different things. If the value is 0, it means that the caret is at the start\n        // of the input, whereas a value of `null` means that the input doesn't support\n        // manipulating the selection range. Inputs that don't support setting the selection range\n        // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n        // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n        if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n            // Note: Just setting `0, 0` doesn't fix the issue. Setting\n            // `1, 1` fixes it for the first time that you type text and\n            // then hold delete. Toggling to `1, 1` and then back to\n            // `0, 0` seems to completely fix it.\n            el.setSelectionRange(1, 1);\n            el.setSelectionRange(0, 0);\n        }\n    };\n    _webkitBlinkWheelListener = () => {\n        // This is a noop function and is used to enable mouse wheel input\n        // on number inputs\n        // on blink and webkit browsers.\n    };\n    /**\n     * In blink and webkit browsers a focused number input does not increment or decrement its value\n     * on mouse wheel interaction unless a wheel event listener is attached to it or one of its\n     * ancestors or a passive wheel listener is attached somewhere in the DOM. For example: Hitting\n     * a tooltip once enables the mouse wheel input for all number inputs as long as it exists. In\n     * order to get reliable and intuitive behavior we apply a wheel event on our own thus making\n     * sure increment and decrement by mouse wheel works every time.\n     * @docs-private\n     */\n    _ensureWheelDefaultBehavior() {\n        this._cleanupWebkitWheel?.();\n        if (this._type === 'number' && (this._platform.BLINK || this._platform.WEBKIT)) {\n            this._cleanupWebkitWheel = this._renderer.listen(this._elementRef.nativeElement, 'wheel', this._webkitBlinkWheelListener);\n        }\n    }\n    /** Gets the value to set on the `readonly` attribute. */\n    _getReadonlyAttribute() {\n        if (this._isNativeSelect) {\n            return null;\n        }\n        if (this.readonly || (this.disabled && this.disabledInteractive)) {\n            return 'true';\n        }\n        return null;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatInput, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatInput, isStandalone: true, selector: \"input[matInput], textarea[matInput], select[matNativeControl],\\n      input[matNativeControl], textarea[matNativeControl]\", inputs: { disabled: \"disabled\", id: \"id\", placeholder: \"placeholder\", name: \"name\", required: \"required\", type: \"type\", errorStateMatcher: \"errorStateMatcher\", userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], value: \"value\", readonly: \"readonly\", disabledInteractive: [\"disabledInteractive\", \"disabledInteractive\", booleanAttribute] }, host: { listeners: { \"focus\": \"_focusChanged(true)\", \"blur\": \"_focusChanged(false)\", \"input\": \"_onInput()\" }, properties: { \"class.mat-input-server\": \"_isServer\", \"class.mat-mdc-form-field-textarea-control\": \"_isInFormField && _isTextarea\", \"class.mat-mdc-form-field-input-control\": \"_isInFormField\", \"class.mat-mdc-input-disabled-interactive\": \"disabledInteractive\", \"class.mdc-text-field__input\": \"_isInFormField\", \"class.mat-mdc-native-select-inline\": \"_isInlineSelect()\", \"id\": \"id\", \"disabled\": \"disabled && !disabledInteractive\", \"required\": \"required\", \"attr.name\": \"name || null\", \"attr.readonly\": \"_getReadonlyAttribute()\", \"attr.aria-disabled\": \"disabled && disabledInteractive ? \\\"true\\\" : null\", \"attr.aria-invalid\": \"(empty && required) ? null : errorState\", \"attr.aria-required\": \"required\", \"attr.id\": \"id\" }, classAttribute: \"mat-mdc-input-element\" }, providers: [{ provide: MatFormFieldControl, useExisting: MatInput }], exportAs: [\"matInput\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatInput, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n                    exportAs: 'matInput',\n                    host: {\n                        'class': 'mat-mdc-input-element',\n                        // The BaseMatInput parent class adds `mat-input-element`, `mat-form-field-control` and\n                        // `mat-form-field-autofill-control` to the CSS class list, but this should not be added for\n                        // this MDC equivalent input.\n                        '[class.mat-input-server]': '_isServer',\n                        '[class.mat-mdc-form-field-textarea-control]': '_isInFormField && _isTextarea',\n                        '[class.mat-mdc-form-field-input-control]': '_isInFormField',\n                        '[class.mat-mdc-input-disabled-interactive]': 'disabledInteractive',\n                        '[class.mdc-text-field__input]': '_isInFormField',\n                        '[class.mat-mdc-native-select-inline]': '_isInlineSelect()',\n                        // Native input properties that are overwritten by Angular inputs need to be synced with\n                        // the native input element. Otherwise property bindings for those don't work.\n                        '[id]': 'id',\n                        '[disabled]': 'disabled && !disabledInteractive',\n                        '[required]': 'required',\n                        '[attr.name]': 'name || null',\n                        '[attr.readonly]': '_getReadonlyAttribute()',\n                        '[attr.aria-disabled]': 'disabled && disabledInteractive ? \"true\" : null',\n                        // Only mark the input as invalid for assistive technology if it has a value since the\n                        // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n                        '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n                        '[attr.aria-required]': 'required',\n                        // Native input properties that are overwritten by Angular inputs need to be synced with\n                        // the native input element. Otherwise property bindings for those don't work.\n                        '[attr.id]': 'id',\n                        '(focus)': '_focusChanged(true)',\n                        '(blur)': '_focusChanged(false)',\n                        '(input)': '_onInput()',\n                    },\n                    providers: [{ provide: MatFormFieldControl, useExisting: MatInput }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { disabled: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], errorStateMatcher: [{\n                type: Input\n            }], userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], value: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\nclass MatInputModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatInputModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatInputModule, imports: [MatCommonModule, MatFormFieldModule, MatInput], exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatInputModule, imports: [MatCommonModule, MatFormFieldModule, MatFormFieldModule, TextFieldModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatInputModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatFormFieldModule, MatInput],\n                    exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule],\n                }]\n        }] });\n\nexport { MAT_INPUT_CONFIG, MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };\n//# sourceMappingURL=input.mjs.map\n"], "mappings": ";;AAAA,SAASA,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,EAAEC,sBAAsB,QAAQ,uBAAuB;AACxE,SAASC,eAAe,EAAEC,eAAe,QAAQ,yBAAyB;AAC1E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACrJ,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,QAAQ,gBAAgB;AAClF,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,CAAC,IAAIC,wBAAwB,QAAQ,qCAAqC;AACnF,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,mBAAmB,QAAQ,2BAA2B;AACzF,SAASC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,OAAO,EAAEX,CAAC,IAAIY,QAAQ,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,SAAS,QAAQ,2BAA2B;AACzI,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,8BAA8B;AACrE,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,4BAA4B;AACpE,SAASpB,CAAC,IAAIqB,kBAAkB,QAAQ,uBAAuB;AAC/D,SAASrB,CAAC,IAAIsB,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;AAC1B,OAAO,iBAAiB;AACxB,OAAO,gBAAgB;AACvB,OAAO,gCAAgC;AACvC,OAAO,wBAAwB;;AAE/B;AACA,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EAC3C,OAAOC,KAAK,CAAC,eAAeD,IAAI,gCAAgC,CAAC;AACrE;;AAEA;AACA,MAAME,uBAAuB,GAAG,CAC5B,QAAQ,EACR,UAAU,EACV,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,CACX;AACD;AACA,MAAMC,gBAAgB,GAAG,IAAI5C,cAAc,CAAC,kBAAkB,CAAC;AAC/D,MAAM6C,QAAQ,CAAC;EA+CX;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGrD,qBAAqB,CAACsD,KAAK,CAAC;IAC7C;IACA;IACA,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACC,YAAY,CAACC,IAAI,CAAC,CAAC;IAC5B;EACJ;EAEA;AACJ;AACA;AACA;EACI,IAAIC,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACC,GAAG;EACnB;EACA,IAAID,EAAEA,CAACJ,KAAK,EAAE;IACV,IAAI,CAACK,GAAG,GAAGL,KAAK,IAAI,IAAI,CAACM,IAAI;EACjC;EAYA;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IAAA,IAAAC,IAAA,EAAAC,eAAA,EAAAC,eAAA;IACX,QAAAF,IAAA,IAAAC,eAAA,GAAO,IAAI,CAACE,SAAS,cAAAF,eAAA,cAAAA,eAAA,IAAAC,eAAA,GAAI,IAAI,CAACE,SAAS,cAAAF,eAAA,gBAAAA,eAAA,GAAdA,eAAA,CAAgBG,OAAO,cAAAH,eAAA,uBAAvBA,eAAA,CAAyBI,YAAY,CAACjD,UAAU,CAAC0C,QAAQ,CAAC,cAAAC,IAAA,cAAAA,IAAA,GAAI,KAAK;EAChG;EACA,IAAID,QAAQA,CAACP,KAAK,EAAE;IAChB,IAAI,CAACW,SAAS,GAAGjE,qBAAqB,CAACsD,KAAK,CAAC;EACjD;EAEA;EACA,IAAIP,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACsB,KAAK;EACrB;EACA,IAAItB,IAAIA,CAACO,KAAK,EAAE;IACZ,MAAMgB,QAAQ,GAAG,IAAI,CAACD,KAAK;IAC3B,IAAI,CAACA,KAAK,GAAGf,KAAK,IAAI,MAAM;IAC5B,IAAI,CAACiB,aAAa,CAAC,CAAC;IACpB;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACC,WAAW,IAAItE,sBAAsB,CAAC,CAAC,CAACuE,GAAG,CAAC,IAAI,CAACJ,KAAK,CAAC,EAAE;MAC/D,IAAI,CAACK,WAAW,CAACC,aAAa,CAAC5B,IAAI,GAAG,IAAI,CAACsB,KAAK;IACpD;IACA,IAAI,IAAI,CAACA,KAAK,KAAKC,QAAQ,EAAE;MACzB,IAAI,CAACM,2BAA2B,CAAC,CAAC;IACtC;EACJ;EAEA;EACA,IAAIC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB,CAACC,OAAO;EAC1C;EACA,IAAIF,iBAAiBA,CAACvB,KAAK,EAAE;IACzB,IAAI,CAACwB,kBAAkB,CAACC,OAAO,GAAGzB,KAAK;EAC3C;EACA;AACJ;AACA;AACA;;EAEI;AACJ;AACA;AACA;EACI,IAAIA,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC0B,yBAAyB,GAC/B,IAAI,CAACA,yBAAyB,CAAC1B,KAAK,CAAC,CAAC,GACtC,IAAI,CAAC2B,mBAAmB,CAAC3B,KAAK;EACxC;EACA,IAAIA,KAAKA,CAACA,KAAK,EAAE;IACb,IAAIA,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;MACtB,IAAI,IAAI,CAAC0B,yBAAyB,EAAE;QAChC,IAAI,CAACA,yBAAyB,CAAC1B,KAAK,CAAC4B,GAAG,CAAC5B,KAAK,CAAC;MACnD,CAAC,MACI;QACD,IAAI,CAAC2B,mBAAmB,CAAC3B,KAAK,GAAGA,KAAK;MAC1C;MACA,IAAI,CAACE,YAAY,CAACC,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACA,IAAI0B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAC7B,KAAK,EAAE;IAChB,IAAI,CAAC8B,SAAS,GAAGpF,qBAAqB,CAACsD,KAAK,CAAC;EACjD;EAIA;EACA,IAAI+B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACP,kBAAkB,CAACO,UAAU;EAC7C;EACA,IAAIA,UAAUA,CAAC/B,KAAK,EAAE;IAClB,IAAI,CAACwB,kBAAkB,CAACO,UAAU,GAAG/B,KAAK;EAC9C;EASAgC,WAAWA,CAAA,EAAG;IAAA,IAAAC,aAAA;IAAAC,eAAA,sBA3KAjF,MAAM,CAACC,UAAU,CAAC;IAAAgF,eAAA,oBACpBjF,MAAM,CAACN,QAAQ,CAAC;IAAAuF,eAAA,oBAChBjF,MAAM,CAACW,SAAS,EAAE;MAAEuE,QAAQ,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC,CAAC;IAAAF,eAAA,2BAC1CjF,MAAM,CAACJ,eAAe,CAAC;IAAAqF,eAAA,kBAChCjF,MAAM,CAACE,MAAM,CAAC;IAAA+E,eAAA,qBACXjF,MAAM,CAACmB,cAAc,EAAE;MAAE+D,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAD,eAAA,oBAC3CjF,MAAM,CAACG,SAAS,CAAC;IAAA8E,eAAA,eACtBjF,MAAM,CAACU,YAAY,CAAC,CAAC0E,KAAK,CAAC,YAAY,CAAC;IAAAH,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,kBAMrCjF,MAAM,CAAC2C,gBAAgB,EAAE;MAAEuC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAD,eAAA;IAAAA,eAAA;IAGtD;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA,kBAIU,KAAK;IACf;AACJ;AACA;AACA;IAHIA,eAAA,uBAIe,IAAIlE,OAAO,CAAC,CAAC;IAC5B;AACJ;AACA;AACA;IAHIkE,eAAA,sBAIc,WAAW;IACzB;AACJ;AACA;AACA;IAHIA,eAAA,qBAIa,KAAK;IAAAA,eAAA,oBAiBN,KAAK;IAAAA,eAAA;IAYjB;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA;IAAAA,eAAA;IAAAA,eAAA,gBAkCQ,MAAM;IAAAA,eAAA;IAAAA,eAAA,oBAwCF,KAAK;IACjB;IAAAA,eAAA;IAAAA,eAAA,gCASwB,CACpB,MAAM,EACN,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,MAAM,CACT,CAACI,MAAM,CAACC,CAAC,IAAI3F,sBAAsB,CAAC,CAAC,CAACuE,GAAG,CAACoB,CAAC,CAAC,CAAC;IAAAL,eAAA,4BAwPzBM,KAAK,IAAK;MAC3B,MAAMC,EAAE,GAAGD,KAAK,CAACE,MAAM;MACvB;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACD,EAAE,CAACzC,KAAK,IAAIyC,EAAE,CAACE,cAAc,KAAK,CAAC,IAAIF,EAAE,CAACG,YAAY,KAAK,CAAC,EAAE;QAC/D;QACA;QACA;QACA;QACAH,EAAE,CAACI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1BJ,EAAE,CAACI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MAC9B;IACJ,CAAC;IAAAX,eAAA,oCAC2B,MAAM;MAC9B;MACA;MACA;IAAA,CACH;IA3QG,MAAMY,UAAU,GAAG7F,MAAM,CAACa,MAAM,EAAE;MAAEqE,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrD,MAAMY,eAAe,GAAG9F,MAAM,CAACc,kBAAkB,EAAE;MAAEoE,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtE,MAAMa,wBAAwB,GAAG/F,MAAM,CAACkC,iBAAiB,CAAC;IAC1D,MAAM8D,QAAQ,GAAGhG,MAAM,CAACiB,wBAAwB,EAAE;MAAEiE,QAAQ,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC,CAAC;IACjF,MAAMc,OAAO,GAAG,IAAI,CAAC9B,WAAW,CAACC,aAAa;IAC9C,MAAM8B,QAAQ,GAAGD,OAAO,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC/C,IAAIH,QAAQ,EAAE;MACV,IAAI5F,QAAQ,CAAC4F,QAAQ,CAACjD,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAC0B,yBAAyB,GAAGuB,QAAQ;MAC7C,CAAC,MACI;QACD,IAAI,CAACtB,mBAAmB,GAAGsB,QAAQ;MACvC;IACJ,CAAC,MACI;MACD;MACA;MACA,IAAI,CAACtB,mBAAmB,GAAGuB,OAAO;IACtC;IACA,IAAI,CAACG,oBAAoB,GAAG,IAAI,CAACrD,KAAK;IACtC;IACA,IAAI,CAACI,EAAE,GAAG,IAAI,CAACA,EAAE;IACjB;IACA;IACA;IACA,IAAI,IAAI,CAACkD,SAAS,CAACC,GAAG,EAAE;MACpB,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,SAAS,CAACC,MAAM,CAACV,OAAO,EAAE,OAAO,EAAE,IAAI,CAACW,iBAAiB,CAAC;MAC3F,CAAC,CAAC;IACN;IACA,IAAI,CAACrC,kBAAkB,GAAG,IAAInC,kBAAkB,CAAC2D,wBAAwB,EAAE,IAAI,CAACpC,SAAS,EAAEmC,eAAe,EAAED,UAAU,EAAE,IAAI,CAAC5C,YAAY,CAAC;IAC1I,IAAI,CAAC4D,SAAS,GAAG,CAAC,IAAI,CAACR,SAAS,CAACS,SAAS;IAC1C,IAAI,CAACC,eAAe,GAAGb,QAAQ,KAAK,QAAQ;IAC5C,IAAI,CAACjC,WAAW,GAAGiC,QAAQ,KAAK,UAAU;IAC1C,IAAI,CAACc,cAAc,GAAG,CAAC,CAAC,IAAI,CAACC,UAAU;IACvC,IAAI,CAACC,mBAAmB,GAAG,EAAAlC,aAAA,OAAI,CAACmC,OAAO,cAAAnC,aAAA,uBAAZA,aAAA,CAAckC,mBAAmB,KAAI,KAAK;IACrE,IAAI,IAAI,CAACH,eAAe,EAAE;MACtB,IAAI,CAACK,WAAW,GAAGnB,OAAO,CAACoB,QAAQ,GAC7B,4BAA4B,GAC5B,mBAAmB;IAC7B;IACA,IAAI,IAAI,CAAC5C,yBAAyB,EAAE;MAChCpE,MAAM,CAAC,MAAM;QACT;QACA,IAAI,CAACoE,yBAAyB,CAAC1B,KAAK,CAAC,CAAC;QACtC,IAAI,CAACE,YAAY,CAACC,IAAI,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACAoE,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACjB,SAAS,CAACS,SAAS,EAAE;MAC1B,IAAI,CAACS,gBAAgB,CAACC,OAAO,CAAC,IAAI,CAACrD,WAAW,CAACC,aAAa,CAAC,CAACqD,SAAS,CAAClC,KAAK,IAAI;QAC7E,IAAI,CAACmC,UAAU,GAAGnC,KAAK,CAACoC,YAAY;QACpC,IAAI,CAAC1E,YAAY,CAACC,IAAI,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACA0E,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC3E,YAAY,CAACC,IAAI,CAAC,CAAC;EAC5B;EACA2E,WAAWA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,qBAAA;IACV,IAAI,CAAC9E,YAAY,CAAC+E,QAAQ,CAAC,CAAC;IAC5B,IAAI,IAAI,CAAC3B,SAAS,CAACS,SAAS,EAAE;MAC1B,IAAI,CAACS,gBAAgB,CAACU,cAAc,CAAC,IAAI,CAAC9D,WAAW,CAACC,aAAa,CAAC;IACxE;IACA,CAAA0D,qBAAA,OAAI,CAACrB,gBAAgB,cAAAqB,qBAAA,eAArBA,qBAAA,CAAAI,IAAA,KAAwB,CAAC;IACzB,CAAAH,qBAAA,OAAI,CAACI,mBAAmB,cAAAJ,qBAAA,eAAxBA,qBAAA,CAAAG,IAAA,KAA2B,CAAC;EAChC;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACzE,SAAS,EAAE;MAChB;MACA;MACA;MACA,IAAI,CAAC0E,gBAAgB,CAAC,CAAC;MACvB;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC1E,SAAS,CAACd,QAAQ,KAAK,IAAI,IAAI,IAAI,CAACc,SAAS,CAACd,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;QAC/E,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACc,SAAS,CAACd,QAAQ;QACvC,IAAI,CAACI,YAAY,CAACC,IAAI,CAAC,CAAC;MAC5B;IACJ;IACA;IACA;IACA;IACA,IAAI,CAACoF,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACC,sBAAsB,CAAC,CAAC;EACjC;EACA;EACAC,KAAKA,CAACC,OAAO,EAAE;IACX,IAAI,CAACtE,WAAW,CAACC,aAAa,CAACoE,KAAK,CAACC,OAAO,CAAC;EACjD;EACA;EACAJ,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC9D,kBAAkB,CAAC8D,gBAAgB,CAAC,CAAC;EAC9C;EACA;EACAK,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAIA,SAAS,KAAK,IAAI,CAAC3F,OAAO,EAAE;MAC5B;IACJ;IACA,IAAI,CAAC,IAAI,CAAC+D,eAAe,IAAI4B,SAAS,IAAI,IAAI,CAAC9F,QAAQ,IAAI,IAAI,CAACqE,mBAAmB,EAAE;MACjF,MAAMjB,OAAO,GAAG,IAAI,CAAC9B,WAAW,CAACC,aAAa;MAC9C;MACA;MACA,IAAI6B,OAAO,CAACzD,IAAI,KAAK,QAAQ,EAAE;QAC3B;QACAyD,OAAO,CAACzD,IAAI,GAAG,MAAM;QACrByD,OAAO,CAACL,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/BK,OAAO,CAACzD,IAAI,GAAG,QAAQ;MAC3B,CAAC,MACI;QACDyD,OAAO,CAACL,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MACnC;IACJ;IACA,IAAI,CAAC5C,OAAO,GAAG2F,SAAS;IACxB,IAAI,CAAC1F,YAAY,CAACC,IAAI,CAAC,CAAC;EAC5B;EACA0F,QAAQA,CAAA,EAAG;IACP;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAEJ;EACAN,sBAAsBA,CAAA,EAAG;IACrB,MAAMO,QAAQ,GAAG,IAAI,CAAC1E,WAAW,CAACC,aAAa,CAACrB,KAAK;IACrD,IAAI,IAAI,CAACqD,oBAAoB,KAAKyC,QAAQ,EAAE;MACxC,IAAI,CAACzC,oBAAoB,GAAGyC,QAAQ;MACpC,IAAI,CAAC5F,YAAY,CAACC,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACAqF,sBAAsBA,CAAA,EAAG;IACrB,MAAMO,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1C,IAAID,WAAW,KAAK,IAAI,CAACE,oBAAoB,EAAE;MAC3C,MAAM/C,OAAO,GAAG,IAAI,CAAC9B,WAAW,CAACC,aAAa;MAC9C,IAAI,CAAC4E,oBAAoB,GAAGF,WAAW;MACvCA,WAAW,GACL7C,OAAO,CAACgD,YAAY,CAAC,aAAa,EAAEH,WAAW,CAAC,GAChD7C,OAAO,CAACiD,eAAe,CAAC,aAAa,CAAC;IAChD;EACJ;EACA;EACAH,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,WAAW,IAAI,IAAI;EACnC;EACA;EACA9E,aAAaA,CAAA,EAAG;IACZ,IAAItB,uBAAuB,CAACyG,OAAO,CAAC,IAAI,CAACrF,KAAK,CAAC,GAAG,CAAC,CAAC,KAC/C,OAAOsF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAM7G,+BAA+B,CAAC,IAAI,CAACuB,KAAK,CAAC;IACrD;EACJ;EACA;EACAuF,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,qBAAqB,CAACH,OAAO,CAAC,IAAI,CAACrF,KAAK,CAAC,GAAG,CAAC,CAAC;EAC9D;EACA;EACAyF,WAAWA,CAAA,EAAG;IACV;IACA,IAAIC,QAAQ,GAAG,IAAI,CAACrF,WAAW,CAACC,aAAa,CAACoF,QAAQ;IACtD,OAAOA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ;EACxC;EACA;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAQ,CAAC,IAAI,CAACL,aAAa,CAAC,CAAC,IACzB,CAAC,IAAI,CAAClF,WAAW,CAACC,aAAa,CAACrB,KAAK,IACrC,CAAC,IAAI,CAACwG,WAAW,CAAC,CAAC,IACnB,CAAC,IAAI,CAAC7B,UAAU;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIiC,gBAAgBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC5C,eAAe,EAAE;MACtB;MACA;MACA;MACA,MAAM6C,aAAa,GAAG,IAAI,CAACzF,WAAW,CAACC,aAAa;MACpD,MAAMyF,WAAW,GAAGD,aAAa,CAACnB,OAAO,CAAC,CAAC,CAAC;MAC5C;MACA;MACA,OAAQ,IAAI,CAACzF,OAAO,IAChB4G,aAAa,CAACvC,QAAQ,IACtB,CAAC,IAAI,CAACqC,KAAK,IACX,CAAC,EAAEE,aAAa,CAACE,aAAa,GAAG,CAAC,CAAC,IAAID,WAAW,IAAIA,WAAW,CAACE,KAAK,CAAC;IAChF,CAAC,MACI;MACD,OAAQ,IAAI,CAAC/G,OAAO,IAAI,CAAC,IAAI,CAACH,QAAQ,IAAK,CAAC,IAAI,CAAC6G,KAAK;IAC1D;EACJ;EACA;AACJ;AACA;AACA;EACIM,iBAAiBA,CAACC,GAAG,EAAE;IACnB,MAAMhE,OAAO,GAAG,IAAI,CAAC9B,WAAW,CAACC,aAAa;IAC9C,MAAM8F,mBAAmB,GAAGjE,OAAO,CAACkE,YAAY,CAAC,kBAAkB,CAAC;IACpE,IAAIC,QAAQ;IACZ;IACA;IACA;IACA;IACA,IAAIF,mBAAmB,EAAE;MACrB,MAAMG,OAAO,GAAG,IAAI,CAACC,qBAAqB,IAAIL,GAAG;MACjDG,QAAQ,GAAGH,GAAG,CAACM,MAAM,CAACL,mBAAmB,CAACM,KAAK,CAAC,GAAG,CAAC,CAACnF,MAAM,CAAClC,EAAE,IAAIA,EAAE,IAAI,CAACkH,OAAO,CAACI,QAAQ,CAACtH,EAAE,CAAC,CAAC,CAAC;IACnG,CAAC,MACI;MACDiH,QAAQ,GAAGH,GAAG;IAClB;IACA,IAAI,CAACK,qBAAqB,GAAGL,GAAG;IAChC,IAAIG,QAAQ,CAACM,MAAM,EAAE;MACjBzE,OAAO,CAACgD,YAAY,CAAC,kBAAkB,EAAEmB,QAAQ,CAACO,IAAI,CAAC,GAAG,CAAC,CAAC;IAChE,CAAC,MACI;MACD1E,OAAO,CAACiD,eAAe,CAAC,kBAAkB,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;EACI0B,gBAAgBA,CAAA,EAAG;IACf;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC5H,OAAO,EAAE;MACf,IAAI,CAACwF,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;EACAqC,eAAeA,CAAA,EAAG;IACd,MAAM5E,OAAO,GAAG,IAAI,CAAC9B,WAAW,CAACC,aAAa;IAC9C,OAAO,IAAI,CAAC2C,eAAe,KAAKd,OAAO,CAACoB,QAAQ,IAAIpB,OAAO,CAAC6E,IAAI,GAAG,CAAC,CAAC;EACzE;EAuBA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIzG,2BAA2BA,CAAA,EAAG;IAAA,IAAA0G,sBAAA;IAC1B,CAAAA,sBAAA,OAAI,CAAC5C,mBAAmB,cAAA4C,sBAAA,eAAxBA,sBAAA,CAAA7C,IAAA,KAA2B,CAAC;IAC5B,IAAI,IAAI,CAACpE,KAAK,KAAK,QAAQ,KAAK,IAAI,CAACuC,SAAS,CAAC2E,KAAK,IAAI,IAAI,CAAC3E,SAAS,CAAC4E,MAAM,CAAC,EAAE;MAC5E,IAAI,CAAC9C,mBAAmB,GAAG,IAAI,CAACzB,SAAS,CAACC,MAAM,CAAC,IAAI,CAACxC,WAAW,CAACC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC8G,yBAAyB,CAAC;IAC7H;EACJ;EACA;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACpE,eAAe,EAAE;MACtB,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACnC,QAAQ,IAAK,IAAI,CAAC/B,QAAQ,IAAI,IAAI,CAACqE,mBAAoB,EAAE;MAC9D,OAAO,MAAM;IACjB;IACA,OAAO,IAAI;EACf;AAGJ;AAACkE,SAAA,GApdKxI,QAAQ;AAAAqC,eAAA,CAARrC,QAAQ,wBAAAyI,kBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAkdyF1I,SAAQ;AAAA;AAAAqC,eAAA,CAldzGrC,QAAQ,8BAqdmE9C,EAAE,CAAAyL,iBAAA;EAAA/I,IAAA,EAFQI,SAAQ;EAAA4I,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAElB/L,EAAE,CAAAiM,UAAA,mBAAAC,mCAAA;QAAA,OAFQF,GAAA,CAAApD,aAAA,CAAc,IAAI,CAAC;MAAA,CAAZ,CAAC,kBAAAuD,kCAAA;QAAA,OAARH,GAAA,CAAApD,aAAA,CAAc,KAAK,CAAC;MAAA,CAAb,CAAC,mBAAAwD,mCAAA;QAAA,OAARJ,GAAA,CAAAlD,QAAA,CAAS,CAAC;MAAA,CAAH,CAAC;IAAA;IAAA,IAAAiD,EAAA;MAElB/L,EAAE,CAAAqM,cAAA,OAAAL,GAAA,CAAA3I,EAFe,CAAC,aAAA2I,GAAA,CAAAjJ,QAAA,KAAAiJ,GAAA,CAAA5E,mBAAD,CAAC,aAAA4E,GAAA,CAAAxI,QAAD,CAAC;MAElBxD,EAAE,CAAAsM,WAAA,SAAAN,GAAA,CAAAO,IAAA,IAFgB,IAAI,cAAZP,GAAA,CAAAX,qBAAA,CAAsB,CAAC,mBAAAW,GAAA,CAAAjJ,QAAA,IAAAiJ,GAAA,CAAA5E,mBAAA,GAAW,MAAM,GAAG,IAAI,kBAAA4E,GAAA,CAAApC,KAAA,IAAAoC,GAAA,CAAAxI,QAAA,GAAzB,IAAI,GAAAwI,GAAA,CAAAhH,UAAA,mBAAAgH,GAAA,CAAAxI,QAAA,QAAAwI,GAAA,CAAA3I,EAAA;MAEpCrD,EAAE,CAAAwM,WAAA,qBAAAR,GAAA,CAAAjF,SAFe,CAAC,wCAAAiF,GAAA,CAAA9E,cAAA,IAAA8E,GAAA,CAAA7H,WAAD,CAAC,qCAAA6H,GAAA,CAAA9E,cAAD,CAAC,uCAAA8E,GAAA,CAAA5E,mBAAD,CAAC,0BAAA4E,GAAA,CAAA9E,cAAD,CAAC,iCAAR8E,GAAA,CAAAjB,eAAA,CAAgB,CAAT,CAAC;IAAA;EAAA;EAAA0B,MAAA;IAAA1J,QAAA;IAAAM,EAAA;IAAA2F,WAAA;IAAAuD,IAAA;IAAA/I,QAAA;IAAAd,IAAA;IAAA8B,iBAAA;IAAAkI,mBAAA;IAAAzJ,KAAA;IAAA6B,QAAA;IAAAsC,mBAAA,oDAAye5G,gBAAgB;EAAA;EAAAmM,QAAA;EAAAC,QAAA,GAE3gB5M,EAAE,CAAA6M,kBAAA,CAF63C,CAAC;IAAEC,OAAO,EAAEvL,mBAAmB;IAAEwL,WAAW,EAAEjK;EAAS,CAAC,CAAC,GAEx7C9C,EAAE,CAAAgN,oBAAA;AAAA;AAAnF;EAAA,QAAA1D,SAAA,oBAAAA,SAAA,KAAiFtJ,EAAE,CAAAiN,iBAAA,CAAQnK,QAAQ,EAAc,CAAC;IACtGJ,IAAI,EAAEjC,SAAS;IACfyM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;AAC9B,0DAA0D;MACtCR,QAAQ,EAAE,UAAU;MACpBS,IAAI,EAAE;QACF,OAAO,EAAE,uBAAuB;QAChC;QACA;QACA;QACA,0BAA0B,EAAE,WAAW;QACvC,6CAA6C,EAAE,+BAA+B;QAC9E,0CAA0C,EAAE,gBAAgB;QAC5D,4CAA4C,EAAE,qBAAqB;QACnE,+BAA+B,EAAE,gBAAgB;QACjD,sCAAsC,EAAE,mBAAmB;QAC3D;QACA;QACA,MAAM,EAAE,IAAI;QACZ,YAAY,EAAE,kCAAkC;QAChD,YAAY,EAAE,UAAU;QACxB,aAAa,EAAE,cAAc;QAC7B,iBAAiB,EAAE,yBAAyB;QAC5C,sBAAsB,EAAE,iDAAiD;QACzE;QACA;QACA,qBAAqB,EAAE,yCAAyC;QAChE,sBAAsB,EAAE,UAAU;QAClC;QACA;QACA,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,qBAAqB;QAChC,QAAQ,EAAE,sBAAsB;QAChC,SAAS,EAAE;MACf,CAAC;MACDC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAEvL,mBAAmB;QAAEwL,WAAW,EAAEjK;MAAS,CAAC;IACvE,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEC,QAAQ,EAAE,CAAC;MACnDL,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAE2C,EAAE,EAAE,CAAC;MACLX,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEsI,WAAW,EAAE,CAAC;MACdtG,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAE6L,IAAI,EAAE,CAAC;MACP7J,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAE8C,QAAQ,EAAE,CAAC;MACXd,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEgC,IAAI,EAAE,CAAC;MACPA,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAE8D,iBAAiB,EAAE,CAAC;MACpB9B,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEgM,mBAAmB,EAAE,CAAC;MACtBhK,IAAI,EAAEhC,KAAK;MACXwM,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEjK,KAAK,EAAE,CAAC;MACRP,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEoE,QAAQ,EAAE,CAAC;MACXpC,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAE0G,mBAAmB,EAAE,CAAC;MACtB1E,IAAI,EAAEhC,KAAK;MACXwM,IAAI,EAAE,CAAC;QAAEI,SAAS,EAAE9M;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+M,cAAc,CAAC;AAIpBC,eAAA,GAJKD,cAAc;AAAApI,eAAA,CAAdoI,cAAc,wBAAAE,wBAAAjC,iBAAA;EAAA,YAAAA,iBAAA,IACmF+B,eAAc;AAAA;AAAApI,eAAA,CAD/GoI,cAAc,8BAhE6DvN,EAAE,CAAA0N,gBAAA;EAAAhL,IAAA,EAkEqB6K,eAAc;EAAAI,OAAA,GAAYnL,eAAe,EAAED,kBAAkB,EAAEO,QAAQ;EAAA8K,OAAA,GAAa9K,QAAQ,EAAEP,kBAAkB,EAAExC,eAAe,EAAEyC,eAAe;AAAA;AAAA2C,eAAA,CAFpPoI,cAAc,8BAhE6DvN,EAAE,CAAA6N,gBAAA;EAAAF,OAAA,GAmE+CnL,eAAe,EAAED,kBAAkB,EAAEA,kBAAkB,EAAExC,eAAe,EAAEyC,eAAe;AAAA;AAE3N;EAAA,QAAA8G,SAAA,oBAAAA,SAAA,KArEiFtJ,EAAE,CAAAiN,iBAAA,CAqEQM,cAAc,EAAc,CAAC;IAC5G7K,IAAI,EAAE/B,QAAQ;IACduM,IAAI,EAAE,CAAC;MACCS,OAAO,EAAE,CAACnL,eAAe,EAAED,kBAAkB,EAAEO,QAAQ,CAAC;MACxD8K,OAAO,EAAE,CAAC9K,QAAQ,EAAEP,kBAAkB,EAAExC,eAAe,EAAEyC,eAAe;IAC5E,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASK,gBAAgB,EAAE1B,wBAAwB,EAAE2B,QAAQ,EAAEyK,cAAc,EAAE9K,+BAA+B;AAC9G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}