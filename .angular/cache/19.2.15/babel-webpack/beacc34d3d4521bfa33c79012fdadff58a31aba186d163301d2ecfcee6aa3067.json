{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiSchemaTopFilterComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-schema-top-filter.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-schema-top-filter.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, Input, Optional, ViewChild } from '@angular/core';\nimport { UntypedFormGroup } from '@angular/forms';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map, skip, switchMap, take, takeUntil } from 'rxjs/operators';\nimport { SettingsService } from '../services/settings/settings.service';\nimport { MatDynamicFormService } from '../swui-dynamic-form/mat-dynamic-form.service';\nimport { SwuiTopFilterDataService } from './top-filter-data.service';\nexport const SCHEMA_FILTER_FIELD_POSTFIX_MAP = ['', '__contains', '__contains!', '__gt', '__lt', '__in', '__gte', '__lte', '__ne', 'Text', 'Fields'];\nconst CLEARABLE_TYPES = {\n  daterange: true,\n  datetimerange: true,\n  string: true,\n  text: true,\n  number: true,\n  numericrange: true\n};\nlet SwuiSchemaTopFilterComponent = (_SwuiSchemaTopFilterComponent = class SwuiSchemaTopFilterComponent {\n  set schema(schema) {\n    var _this$filter;\n    this.transformToForm = {};\n    this.transformToModel = {};\n    this.selectOptionSchema = [];\n    this.formSchema = this.transformSchema(schema);\n    (_this$filter = this.filter) === null || _this$filter === void 0 || _this$filter.updateFilter(this.formSchema);\n    this.onApplyFilterFields(Object.keys(this._values));\n  }\n  constructor(settings, dynamicFormService, filter) {\n    this.settings = settings;\n    this.dynamicFormService = dynamicFormService;\n    this.filter = filter;\n    this.name = '';\n    this.form = new UntypedFormGroup({});\n    this.formSchema = [];\n    this.displayFormSchema = [];\n    this.selectOptionSchema = [];\n    this.selectedOptions = [];\n    this._loading = new BehaviorSubject(true);\n    this.transformToForm = {};\n    this.transformToModel = {};\n    this.submitted = false;\n    this.destroy$ = new Subject();\n    this._values = {};\n    this.defaultSelected = [];\n  }\n  get loading() {\n    return this._loading.value;\n  }\n  get loading$() {\n    return this._loading.asObservable();\n  }\n  ngOnInit() {\n    var _this$filter2, _this$filter3;\n    this.settings.appSettings$.pipe(takeUntil(this.destroy$)).subscribe(({\n      timezoneName\n    }) => {\n      this.dynamicFormService.setTimezone(timezoneName);\n    });\n    (_this$filter2 = this.filter) === null || _this$filter2 === void 0 || _this$filter2.appliedFilter.pipe(debounceTime(10), takeUntil(this.destroy$)).subscribe(data => {\n      this._values = this.transformFilterToForm(data);\n      if (this.submitted && !this.loading) {\n        this.submitted = false;\n        return;\n      }\n      if (this.loading) {\n        this.onApplyFilterFields(Object.keys(this._values));\n      }\n      this._loading.next(false);\n    });\n    (_this$filter3 = this.filter) === null || _this$filter3 === void 0 || _this$filter3.appliedFilter.pipe(take(1), switchMap(() => this.form.valueChanges), debounceTime(500), map(data => {\n      var _this$filter4;\n      const result = Object.keys(data).reduce((res, key) => {\n        const controlValue = Array.isArray(data[key]) ? data[key].toString() : data[key];\n        return _objectSpread(_objectSpread({}, this.transformToModel[key](controlValue)), res);\n      }, {});\n      (_this$filter4 = this.filter) === null || _this$filter4 === void 0 || _this$filter4.setFormState(result);\n      return result;\n    }), distinctUntilChanged((prev, curr) => {\n      return JSON.stringify(this.getFilledObject(prev || {})) === JSON.stringify(this.getFilledObject(curr || {}));\n    }), skip(1), takeUntil(this.destroy$)).subscribe(filterData => {\n      var _this$filter5;\n      this.submitted = true;\n      (_this$filter5 = this.filter) === null || _this$filter5 === void 0 || _this$filter5.submitFilter(filterData, true);\n    });\n  }\n  onApplyFilterFields(data) {\n    this.selectedOptions = data;\n    const uniqueFields = Array.from(new Set([...this.defaultSelected, ...data]));\n    this.displayFormSchema = uniqueFields.map(fieldName => {\n      const schemaItem = this.formSchema.find(({\n        field\n      }) => field === fieldName);\n      return _objectSpread(_objectSpread({}, schemaItem), {}, {\n        value: this._values[fieldName]\n      });\n    });\n    if (this.filterMenuRef && this.filterMenuRef.menu) {\n      this.filterMenuRef.closeMenu();\n    }\n  }\n  onCancel() {\n    if (this.filterMenuRef) {\n      this.filterMenuRef.closeMenu();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n  getFilledObject(obj) {\n    return Object.entries(obj).reduce((res, [key, value]) => {\n      if (value) {\n        res[key] = value;\n      }\n      return res;\n    }, {});\n  }\n  transformSchema(schema) {\n    this.defaultSelected = [];\n    this.selectOptionSchema = [];\n    return schema.map(item => {\n      if (item.isFilterableAlways) {\n        this.defaultSelected.push(item.field);\n      } else {\n        this.selectOptionSchema.push({\n          id: item.field,\n          text: item.title || '',\n          data: item.field\n        });\n      }\n      if (item.filterMatch) {\n        this.transformToModel[item.field] = fieldValue => {\n          if (typeof item.filterMatch !== 'object') {\n            const key = `${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[item.filterMatch]}`;\n            const val = item.type === 'string' && fieldValue ? fieldValue.trim() : fieldValue;\n            return {\n              [key]: val\n            };\n          }\n          return Object.entries(item.filterMatch).reduce((res, [key, value]) => {\n            const keyValue = (fieldValue || {})[key] || null;\n            res[`${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[value]}`] = keyValue;\n            return res;\n          }, {});\n        };\n        if (typeof item.filterMatch !== 'object') {\n          const key = `${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[item.filterMatch]}`;\n          this.transformToForm[key] = fieldValue => {\n            const value = item.type === 'multiselect' ? (fieldValue || '').split(',') : fieldValue;\n            return {\n              [item.field]: value,\n              field: item.field,\n              isSimply: true\n            };\n          };\n        } else {\n          Object.entries(item.filterMatch).forEach(([key, value]) => {\n            this.transformToForm[`${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[value]}`] = fieldValue => {\n              return {\n                [key]: fieldValue,\n                field: item.field\n              };\n            };\n          });\n        }\n      } else {\n        this.transformToModel[item.field] = fieldValue => {\n          const val = item.type === 'string' && fieldValue ? fieldValue.trim() : fieldValue;\n          return {\n            [item.field]: val\n          };\n        };\n        this.transformToForm[item.field] = fieldValue => {\n          const value = item.type === 'multiselect' ? (fieldValue || '').split(',') : fieldValue;\n          return {\n            [item.field]: value,\n            field: item.field,\n            isSimply: true\n          };\n        };\n      }\n      return _objectSpread(_objectSpread({}, item), {}, {\n        key: item.field,\n        clearable: CLEARABLE_TYPES[item.type]\n      });\n    });\n  }\n  transformFilterToForm(filter) {\n    const fields = Object.keys(this.transformToForm).filter(field => {\n      return filter.hasOwnProperty(field);\n    });\n    return fields.reduce((res, key) => {\n      const transformData = this.transformToForm[key](filter[key]);\n      const {\n        field,\n        isSimply\n      } = transformData;\n      const prev = res[field];\n      delete transformData.field;\n      if (isSimply) {\n        res[field] = transformData[field];\n      } else {\n        res[field] = _objectSpread(_objectSpread({}, prev), transformData);\n      }\n      return res;\n    }, {});\n  }\n}, _SwuiSchemaTopFilterComponent.ctorParameters = () => [{\n  type: SettingsService\n}, {\n  type: MatDynamicFormService\n}, {\n  type: SwuiTopFilterDataService,\n  decorators: [{\n    type: Optional\n  }]\n}], _SwuiSchemaTopFilterComponent.propDecorators = {\n  schema: [{\n    type: Input\n  }],\n  name: [{\n    type: Input\n  }],\n  filterMenuRef: [{\n    type: ViewChild,\n    args: [MatMenuTrigger, {\n      static: false\n    }]\n  }]\n}, _SwuiSchemaTopFilterComponent);\nSwuiSchemaTopFilterComponent = __decorate([Component({\n  selector: 'lib-swui-schema-top-filter',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSchemaTopFilterComponent);\nexport { SwuiSchemaTopFilterComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectionStrategy", "Component", "Input", "Optional", "ViewChild", "UntypedFormGroup", "MatMenuTrigger", "BehaviorSubject", "Subject", "debounceTime", "distinctUntilChanged", "map", "skip", "switchMap", "take", "takeUntil", "SettingsService", "MatDynamicFormService", "SwuiTopFilterDataService", "SCHEMA_FILTER_FIELD_POSTFIX_MAP", "CLEARABLE_TYPES", "daterange", "datetimerange", "string", "text", "number", "numericrange", "SwuiSchemaTopFilterComponent", "_SwuiSchemaTopFilterComponent", "schema", "_this$filter", "transformToForm", "transformToModel", "selectOptionSchema", "formSchema", "transformSchema", "filter", "updateFilter", "onApp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "_values", "constructor", "settings", "dynamicFormService", "name", "form", "displayFormSchema", "selectedOptions", "_loading", "submitted", "destroy$", "defaultSelected", "loading", "value", "loading$", "asObservable", "ngOnInit", "_this$filter2", "_this$filter3", "appSettings$", "pipe", "subscribe", "timezoneName", "setTimezone", "appliedFilter", "data", "transformFilterToForm", "next", "valueChanges", "_this$filter4", "result", "reduce", "res", "key", "controlValue", "Array", "isArray", "toString", "_objectSpread", "setFormState", "prev", "curr", "JSON", "stringify", "getFilledObject", "filterData", "_this$filter5", "submitFilter", "uniqueFields", "from", "Set", "fieldName", "schemaItem", "find", "field", "filterMenuRef", "menu", "closeMenu", "onCancel", "ngOnDestroy", "undefined", "complete", "obj", "entries", "item", "isFilterableAlways", "push", "id", "title", "filterMatch", "fieldValue", "val", "type", "trim", "keyValue", "split", "isSimply", "for<PERSON>ach", "clearable", "fields", "hasOwnProperty", "transformData", "ctorParameters", "decorators", "propDecorators", "args", "static", "selector", "template", "changeDetection", "OnPush", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-top-filter/swui-schema-top-filter.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-schema-top-filter.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-schema-top-filter.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, Input, Optional, ViewChild } from '@angular/core';\nimport { UntypedFormGroup } from '@angular/forms';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map, skip, switchMap, take, takeUntil } from 'rxjs/operators';\nimport { SettingsService } from '../services/settings/settings.service';\nimport { MatDynamicFormService } from '../swui-dynamic-form/mat-dynamic-form.service';\nimport { SwuiTopFilterDataService } from './top-filter-data.service';\nexport const SCHEMA_FILTER_FIELD_POSTFIX_MAP = [\n    '',\n    '__contains',\n    '__contains!',\n    '__gt',\n    '__lt',\n    '__in',\n    '__gte',\n    '__lte',\n    '__ne',\n    'Text',\n    'Fields'\n];\nconst CLEARABLE_TYPES = {\n    daterange: true,\n    datetimerange: true,\n    string: true,\n    text: true,\n    number: true,\n    numericrange: true\n};\nlet SwuiSchemaTopFilterComponent = class SwuiSchemaTopFilterComponent {\n    set schema(schema) {\n        this.transformToForm = {};\n        this.transformToModel = {};\n        this.selectOptionSchema = [];\n        this.formSchema = this.transformSchema(schema);\n        this.filter?.updateFilter(this.formSchema);\n        this.onApplyFilterFields(Object.keys(this._values));\n    }\n    constructor(settings, dynamicFormService, filter) {\n        this.settings = settings;\n        this.dynamicFormService = dynamicFormService;\n        this.filter = filter;\n        this.name = '';\n        this.form = new UntypedFormGroup({});\n        this.formSchema = [];\n        this.displayFormSchema = [];\n        this.selectOptionSchema = [];\n        this.selectedOptions = [];\n        this._loading = new BehaviorSubject(true);\n        this.transformToForm = {};\n        this.transformToModel = {};\n        this.submitted = false;\n        this.destroy$ = new Subject();\n        this._values = {};\n        this.defaultSelected = [];\n    }\n    get loading() {\n        return this._loading.value;\n    }\n    get loading$() {\n        return this._loading.asObservable();\n    }\n    ngOnInit() {\n        this.settings.appSettings$\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(({ timezoneName }) => {\n            this.dynamicFormService.setTimezone(timezoneName);\n        });\n        this.filter?.appliedFilter\n            .pipe(debounceTime(10), takeUntil(this.destroy$))\n            .subscribe(data => {\n            this._values = this.transformFilterToForm(data);\n            if (this.submitted && !this.loading) {\n                this.submitted = false;\n                return;\n            }\n            if (this.loading) {\n                this.onApplyFilterFields(Object.keys(this._values));\n            }\n            this._loading.next(false);\n        });\n        this.filter?.appliedFilter\n            .pipe(take(1), switchMap(() => this.form.valueChanges), debounceTime(500), map(data => {\n            const result = Object.keys(data)\n                .reduce((res, key) => {\n                const controlValue = Array.isArray(data[key])\n                    ? data[key].toString()\n                    : data[key];\n                return {\n                    ...this.transformToModel[key](controlValue),\n                    ...res\n                };\n            }, {});\n            this.filter?.setFormState(result);\n            return result;\n        }), distinctUntilChanged((prev, curr) => {\n            return JSON.stringify(this.getFilledObject(prev || {})) === JSON.stringify(this.getFilledObject(curr || {}));\n        }), skip(1), takeUntil(this.destroy$))\n            .subscribe(filterData => {\n            this.submitted = true;\n            this.filter?.submitFilter(filterData, true);\n        });\n    }\n    onApplyFilterFields(data) {\n        this.selectedOptions = data;\n        const uniqueFields = Array.from(new Set([\n            ...this.defaultSelected,\n            ...data\n        ]));\n        this.displayFormSchema = uniqueFields.map(fieldName => {\n            const schemaItem = this.formSchema.find(({ field }) => field === fieldName);\n            return {\n                ...schemaItem,\n                value: this._values[fieldName]\n            };\n        });\n        if (this.filterMenuRef && this.filterMenuRef.menu) {\n            this.filterMenuRef.closeMenu();\n        }\n    }\n    onCancel() {\n        if (this.filterMenuRef) {\n            this.filterMenuRef.closeMenu();\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next(undefined);\n        this.destroy$.complete();\n    }\n    getFilledObject(obj) {\n        return Object.entries(obj)\n            .reduce((res, [key, value]) => {\n            if (value) {\n                res[key] = value;\n            }\n            return res;\n        }, {});\n    }\n    transformSchema(schema) {\n        this.defaultSelected = [];\n        this.selectOptionSchema = [];\n        return schema.map(item => {\n            if (item.isFilterableAlways) {\n                this.defaultSelected.push(item.field);\n            }\n            else {\n                this.selectOptionSchema.push({\n                    id: item.field,\n                    text: item.title || '',\n                    data: item.field\n                });\n            }\n            if (item.filterMatch) {\n                this.transformToModel[item.field] = (fieldValue) => {\n                    if (typeof item.filterMatch !== 'object') {\n                        const key = `${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[item.filterMatch]}`;\n                        const val = item.type === 'string' && fieldValue ? fieldValue.trim() : fieldValue;\n                        return { [key]: val };\n                    }\n                    return Object.entries(item.filterMatch)\n                        .reduce((res, [key, value]) => {\n                        const keyValue = (fieldValue || {})[key] || null;\n                        res[`${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[value]}`] = keyValue;\n                        return res;\n                    }, {});\n                };\n                if (typeof item.filterMatch !== 'object') {\n                    const key = `${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[item.filterMatch]}`;\n                    this.transformToForm[key] = (fieldValue) => {\n                        const value = item.type === 'multiselect'\n                            ? (fieldValue || '').split(',')\n                            : fieldValue;\n                        return { [item.field]: value, field: item.field, isSimply: true };\n                    };\n                }\n                else {\n                    Object.entries(item.filterMatch).forEach(([key, value]) => {\n                        this.transformToForm[`${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[value]}`] = (fieldValue) => {\n                            return { [key]: fieldValue, field: item.field };\n                        };\n                    });\n                }\n            }\n            else {\n                this.transformToModel[item.field] = (fieldValue) => {\n                    const val = item.type === 'string' && fieldValue ? fieldValue.trim() : fieldValue;\n                    return { [item.field]: val };\n                };\n                this.transformToForm[item.field] = (fieldValue) => {\n                    const value = item.type === 'multiselect'\n                        ? (fieldValue || '').split(',')\n                        : fieldValue;\n                    return { [item.field]: value, field: item.field, isSimply: true };\n                };\n            }\n            return { ...item, key: item.field, clearable: CLEARABLE_TYPES[item.type] };\n        });\n    }\n    transformFilterToForm(filter) {\n        const fields = Object.keys(this.transformToForm)\n            .filter(field => {\n            return filter.hasOwnProperty(field);\n        });\n        return fields\n            .reduce((res, key) => {\n            const transformData = this.transformToForm[key](filter[key]);\n            const { field, isSimply } = transformData;\n            const prev = res[field];\n            delete transformData.field;\n            if (isSimply) {\n                res[field] = transformData[field];\n            }\n            else {\n                res[field] = {\n                    ...prev,\n                    ...transformData\n                };\n            }\n            return res;\n        }, {});\n    }\n    static { this.ctorParameters = () => [\n        { type: SettingsService },\n        { type: MatDynamicFormService },\n        { type: SwuiTopFilterDataService, decorators: [{ type: Optional }] }\n    ]; }\n    static { this.propDecorators = {\n        schema: [{ type: Input }],\n        name: [{ type: Input }],\n        filterMenuRef: [{ type: ViewChild, args: [MatMenuTrigger, { static: false },] }]\n    }; }\n};\nSwuiSchemaTopFilterComponent = __decorate([\n    Component({\n        selector: 'lib-swui-schema-top-filter',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiSchemaTopFilterComponent);\nexport { SwuiSchemaTopFilterComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,oDAAoD;AACrF,OAAOC,oBAAoB,MAAM,oDAAoD;AACrF,SAASC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,eAAe;AAC9F,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,eAAe,EAAEC,OAAO,QAAQ,MAAM;AAC/C,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAC1G,SAASC,eAAe,QAAQ,uCAAuC;AACvE,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,wBAAwB,QAAQ,2BAA2B;AACpE,OAAO,MAAMC,+BAA+B,GAAG,CAC3C,EAAE,EACF,YAAY,EACZ,aAAa,EACb,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,QAAQ,CACX;AACD,MAAMC,eAAe,GAAG;EACpBC,SAAS,EAAE,IAAI;EACfC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZC,YAAY,EAAE;AAClB,CAAC;AACD,IAAIC,4BAA4B,IAAAC,6BAAA,GAAG,MAAMD,4BAA4B,CAAC;EAClE,IAAIE,MAAMA,CAACA,MAAM,EAAE;IAAA,IAAAC,YAAA;IACf,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,eAAe,CAACN,MAAM,CAAC;IAC9C,CAAAC,YAAA,OAAI,CAACM,MAAM,cAAAN,YAAA,eAAXA,YAAA,CAAaO,YAAY,CAAC,IAAI,CAACH,UAAU,CAAC;IAC1C,IAAI,CAACI,mBAAmB,CAACC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC;EACvD;EACAC,WAAWA,CAACC,QAAQ,EAAEC,kBAAkB,EAAER,MAAM,EAAE;IAC9C,IAAI,CAACO,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACR,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACS,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,IAAI,GAAG,IAAIzC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC6B,UAAU,GAAG,EAAE;IACpB,IAAI,CAACa,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACd,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACe,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,QAAQ,GAAG,IAAI1C,eAAe,CAAC,IAAI,CAAC;IACzC,IAAI,CAACwB,eAAe,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACkB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,IAAI3C,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACiC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACW,eAAe,GAAG,EAAE;EAC7B;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACJ,QAAQ,CAACK,KAAK;EAC9B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACN,QAAQ,CAACO,YAAY,CAAC,CAAC;EACvC;EACAC,QAAQA,CAAA,EAAG;IAAA,IAAAC,aAAA,EAAAC,aAAA;IACP,IAAI,CAAChB,QAAQ,CAACiB,YAAY,CACrBC,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACoC,QAAQ,CAAC,CAAC,CAC9BW,SAAS,CAAC,CAAC;MAAEC;IAAa,CAAC,KAAK;MACjC,IAAI,CAACnB,kBAAkB,CAACoB,WAAW,CAACD,YAAY,CAAC;IACrD,CAAC,CAAC;IACF,CAAAL,aAAA,OAAI,CAACtB,MAAM,cAAAsB,aAAA,eAAXA,aAAA,CAAaO,aAAa,CACrBJ,IAAI,CAACpD,YAAY,CAAC,EAAE,CAAC,EAAEM,SAAS,CAAC,IAAI,CAACoC,QAAQ,CAAC,CAAC,CAChDW,SAAS,CAACI,IAAI,IAAI;MACnB,IAAI,CAACzB,OAAO,GAAG,IAAI,CAAC0B,qBAAqB,CAACD,IAAI,CAAC;MAC/C,IAAI,IAAI,CAAChB,SAAS,IAAI,CAAC,IAAI,CAACG,OAAO,EAAE;QACjC,IAAI,CAACH,SAAS,GAAG,KAAK;QACtB;MACJ;MACA,IAAI,IAAI,CAACG,OAAO,EAAE;QACd,IAAI,CAACf,mBAAmB,CAACC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC;MACvD;MACA,IAAI,CAACQ,QAAQ,CAACmB,IAAI,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC;IACF,CAAAT,aAAA,OAAI,CAACvB,MAAM,cAAAuB,aAAA,eAAXA,aAAA,CAAaM,aAAa,CACrBJ,IAAI,CAAC/C,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,MAAM,IAAI,CAACiC,IAAI,CAACuB,YAAY,CAAC,EAAE5D,YAAY,CAAC,GAAG,CAAC,EAAEE,GAAG,CAACuD,IAAI,IAAI;MAAA,IAAAI,aAAA;MACvF,MAAMC,MAAM,GAAGhC,MAAM,CAACC,IAAI,CAAC0B,IAAI,CAAC,CAC3BM,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;QACtB,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACX,IAAI,CAACQ,GAAG,CAAC,CAAC,GACvCR,IAAI,CAACQ,GAAG,CAAC,CAACI,QAAQ,CAAC,CAAC,GACpBZ,IAAI,CAACQ,GAAG,CAAC;QACf,OAAAK,aAAA,CAAAA,aAAA,KACO,IAAI,CAAC/C,gBAAgB,CAAC0C,GAAG,CAAC,CAACC,YAAY,CAAC,GACxCF,GAAG;MAEd,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAAH,aAAA,OAAI,CAAClC,MAAM,cAAAkC,aAAA,eAAXA,aAAA,CAAaU,YAAY,CAACT,MAAM,CAAC;MACjC,OAAOA,MAAM;IACjB,CAAC,CAAC,EAAE7D,oBAAoB,CAAC,CAACuE,IAAI,EAAEC,IAAI,KAAK;MACrC,OAAOC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,eAAe,CAACJ,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAKE,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,eAAe,CAACH,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;IAChH,CAAC,CAAC,EAAEtE,IAAI,CAAC,CAAC,CAAC,EAAEG,SAAS,CAAC,IAAI,CAACoC,QAAQ,CAAC,CAAC,CACjCW,SAAS,CAACwB,UAAU,IAAI;MAAA,IAAAC,aAAA;MACzB,IAAI,CAACrC,SAAS,GAAG,IAAI;MACrB,CAAAqC,aAAA,OAAI,CAACnD,MAAM,cAAAmD,aAAA,eAAXA,aAAA,CAAaC,YAAY,CAACF,UAAU,EAAE,IAAI,CAAC;IAC/C,CAAC,CAAC;EACN;EACAhD,mBAAmBA,CAAC4B,IAAI,EAAE;IACtB,IAAI,CAAClB,eAAe,GAAGkB,IAAI;IAC3B,MAAMuB,YAAY,GAAGb,KAAK,CAACc,IAAI,CAAC,IAAIC,GAAG,CAAC,CACpC,GAAG,IAAI,CAACvC,eAAe,EACvB,GAAGc,IAAI,CACV,CAAC,CAAC;IACH,IAAI,CAACnB,iBAAiB,GAAG0C,YAAY,CAAC9E,GAAG,CAACiF,SAAS,IAAI;MACnD,MAAMC,UAAU,GAAG,IAAI,CAAC3D,UAAU,CAAC4D,IAAI,CAAC,CAAC;QAAEC;MAAM,CAAC,KAAKA,KAAK,KAAKH,SAAS,CAAC;MAC3E,OAAAb,aAAA,CAAAA,aAAA,KACOc,UAAU;QACbvC,KAAK,EAAE,IAAI,CAACb,OAAO,CAACmD,SAAS;MAAC;IAEtC,CAAC,CAAC;IACF,IAAI,IAAI,CAACI,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,IAAI,EAAE;MAC/C,IAAI,CAACD,aAAa,CAACE,SAAS,CAAC,CAAC;IAClC;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACH,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACE,SAAS,CAAC,CAAC;IAClC;EACJ;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjD,QAAQ,CAACiB,IAAI,CAACiC,SAAS,CAAC;IAC7B,IAAI,CAAClD,QAAQ,CAACmD,QAAQ,CAAC,CAAC;EAC5B;EACAjB,eAAeA,CAACkB,GAAG,EAAE;IACjB,OAAOhE,MAAM,CAACiE,OAAO,CAACD,GAAG,CAAC,CACrB/B,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEpB,KAAK,CAAC,KAAK;MAC/B,IAAIA,KAAK,EAAE;QACPmB,GAAG,CAACC,GAAG,CAAC,GAAGpB,KAAK;MACpB;MACA,OAAOmB,GAAG;IACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;EACAtC,eAAeA,CAACN,MAAM,EAAE;IACpB,IAAI,CAACuB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACnB,kBAAkB,GAAG,EAAE;IAC5B,OAAOJ,MAAM,CAAClB,GAAG,CAAC8F,IAAI,IAAI;MACtB,IAAIA,IAAI,CAACC,kBAAkB,EAAE;QACzB,IAAI,CAACtD,eAAe,CAACuD,IAAI,CAACF,IAAI,CAACV,KAAK,CAAC;MACzC,CAAC,MACI;QACD,IAAI,CAAC9D,kBAAkB,CAAC0E,IAAI,CAAC;UACzBC,EAAE,EAAEH,IAAI,CAACV,KAAK;UACdvE,IAAI,EAAEiF,IAAI,CAACI,KAAK,IAAI,EAAE;UACtB3C,IAAI,EAAEuC,IAAI,CAACV;QACf,CAAC,CAAC;MACN;MACA,IAAIU,IAAI,CAACK,WAAW,EAAE;QAClB,IAAI,CAAC9E,gBAAgB,CAACyE,IAAI,CAACV,KAAK,CAAC,GAAIgB,UAAU,IAAK;UAChD,IAAI,OAAON,IAAI,CAACK,WAAW,KAAK,QAAQ,EAAE;YACtC,MAAMpC,GAAG,GAAG,GAAG+B,IAAI,CAACV,KAAK,GAAG5E,+BAA+B,CAACsF,IAAI,CAACK,WAAW,CAAC,EAAE;YAC/E,MAAME,GAAG,GAAGP,IAAI,CAACQ,IAAI,KAAK,QAAQ,IAAIF,UAAU,GAAGA,UAAU,CAACG,IAAI,CAAC,CAAC,GAAGH,UAAU;YACjF,OAAO;cAAE,CAACrC,GAAG,GAAGsC;YAAI,CAAC;UACzB;UACA,OAAOzE,MAAM,CAACiE,OAAO,CAACC,IAAI,CAACK,WAAW,CAAC,CAClCtC,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEpB,KAAK,CAAC,KAAK;YAC/B,MAAM6D,QAAQ,GAAG,CAACJ,UAAU,IAAI,CAAC,CAAC,EAAErC,GAAG,CAAC,IAAI,IAAI;YAChDD,GAAG,CAAC,GAAGgC,IAAI,CAACV,KAAK,GAAG5E,+BAA+B,CAACmC,KAAK,CAAC,EAAE,CAAC,GAAG6D,QAAQ;YACxE,OAAO1C,GAAG;UACd,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC;QACD,IAAI,OAAOgC,IAAI,CAACK,WAAW,KAAK,QAAQ,EAAE;UACtC,MAAMpC,GAAG,GAAG,GAAG+B,IAAI,CAACV,KAAK,GAAG5E,+BAA+B,CAACsF,IAAI,CAACK,WAAW,CAAC,EAAE;UAC/E,IAAI,CAAC/E,eAAe,CAAC2C,GAAG,CAAC,GAAIqC,UAAU,IAAK;YACxC,MAAMzD,KAAK,GAAGmD,IAAI,CAACQ,IAAI,KAAK,aAAa,GACnC,CAACF,UAAU,IAAI,EAAE,EAAEK,KAAK,CAAC,GAAG,CAAC,GAC7BL,UAAU;YAChB,OAAO;cAAE,CAACN,IAAI,CAACV,KAAK,GAAGzC,KAAK;cAAEyC,KAAK,EAAEU,IAAI,CAACV,KAAK;cAAEsB,QAAQ,EAAE;YAAK,CAAC;UACrE,CAAC;QACL,CAAC,MACI;UACD9E,MAAM,CAACiE,OAAO,CAACC,IAAI,CAACK,WAAW,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAC5C,GAAG,EAAEpB,KAAK,CAAC,KAAK;YACvD,IAAI,CAACvB,eAAe,CAAC,GAAG0E,IAAI,CAACV,KAAK,GAAG5E,+BAA+B,CAACmC,KAAK,CAAC,EAAE,CAAC,GAAIyD,UAAU,IAAK;cAC7F,OAAO;gBAAE,CAACrC,GAAG,GAAGqC,UAAU;gBAAEhB,KAAK,EAAEU,IAAI,CAACV;cAAM,CAAC;YACnD,CAAC;UACL,CAAC,CAAC;QACN;MACJ,CAAC,MACI;QACD,IAAI,CAAC/D,gBAAgB,CAACyE,IAAI,CAACV,KAAK,CAAC,GAAIgB,UAAU,IAAK;UAChD,MAAMC,GAAG,GAAGP,IAAI,CAACQ,IAAI,KAAK,QAAQ,IAAIF,UAAU,GAAGA,UAAU,CAACG,IAAI,CAAC,CAAC,GAAGH,UAAU;UACjF,OAAO;YAAE,CAACN,IAAI,CAACV,KAAK,GAAGiB;UAAI,CAAC;QAChC,CAAC;QACD,IAAI,CAACjF,eAAe,CAAC0E,IAAI,CAACV,KAAK,CAAC,GAAIgB,UAAU,IAAK;UAC/C,MAAMzD,KAAK,GAAGmD,IAAI,CAACQ,IAAI,KAAK,aAAa,GACnC,CAACF,UAAU,IAAI,EAAE,EAAEK,KAAK,CAAC,GAAG,CAAC,GAC7BL,UAAU;UAChB,OAAO;YAAE,CAACN,IAAI,CAACV,KAAK,GAAGzC,KAAK;YAAEyC,KAAK,EAAEU,IAAI,CAACV,KAAK;YAAEsB,QAAQ,EAAE;UAAK,CAAC;QACrE,CAAC;MACL;MACA,OAAAtC,aAAA,CAAAA,aAAA,KAAY0B,IAAI;QAAE/B,GAAG,EAAE+B,IAAI,CAACV,KAAK;QAAEwB,SAAS,EAAEnG,eAAe,CAACqF,IAAI,CAACQ,IAAI;MAAC;IAC5E,CAAC,CAAC;EACN;EACA9C,qBAAqBA,CAAC/B,MAAM,EAAE;IAC1B,MAAMoF,MAAM,GAAGjF,MAAM,CAACC,IAAI,CAAC,IAAI,CAACT,eAAe,CAAC,CAC3CK,MAAM,CAAC2D,KAAK,IAAI;MACjB,OAAO3D,MAAM,CAACqF,cAAc,CAAC1B,KAAK,CAAC;IACvC,CAAC,CAAC;IACF,OAAOyB,MAAM,CACRhD,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACtB,MAAMgD,aAAa,GAAG,IAAI,CAAC3F,eAAe,CAAC2C,GAAG,CAAC,CAACtC,MAAM,CAACsC,GAAG,CAAC,CAAC;MAC5D,MAAM;QAAEqB,KAAK;QAAEsB;MAAS,CAAC,GAAGK,aAAa;MACzC,MAAMzC,IAAI,GAAGR,GAAG,CAACsB,KAAK,CAAC;MACvB,OAAO2B,aAAa,CAAC3B,KAAK;MAC1B,IAAIsB,QAAQ,EAAE;QACV5C,GAAG,CAACsB,KAAK,CAAC,GAAG2B,aAAa,CAAC3B,KAAK,CAAC;MACrC,CAAC,MACI;QACDtB,GAAG,CAACsB,KAAK,CAAC,GAAAhB,aAAA,CAAAA,aAAA,KACHE,IAAI,GACJyC,aAAa,CACnB;MACL;MACA,OAAOjD,GAAG;IACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;AAWJ,CAAC,EAVY7C,6BAAA,CAAK+F,cAAc,GAAG,MAAM,CACjC;EAAEV,IAAI,EAAEjG;AAAgB,CAAC,EACzB;EAAEiG,IAAI,EAAEhG;AAAsB,CAAC,EAC/B;EAAEgG,IAAI,EAAE/F,wBAAwB;EAAE0G,UAAU,EAAE,CAAC;IAAEX,IAAI,EAAE9G;EAAS,CAAC;AAAE,CAAC,CACvE,EACQyB,6BAAA,CAAKiG,cAAc,GAAG;EAC3BhG,MAAM,EAAE,CAAC;IAAEoF,IAAI,EAAE/G;EAAM,CAAC,CAAC;EACzB2C,IAAI,EAAE,CAAC;IAAEoE,IAAI,EAAE/G;EAAM,CAAC,CAAC;EACvB8F,aAAa,EAAE,CAAC;IAAEiB,IAAI,EAAE7G,SAAS;IAAE0H,IAAI,EAAE,CAACxH,cAAc,EAAE;MAAEyH,MAAM,EAAE;IAAM,CAAC;EAAG,CAAC;AACnF,CAAC,EAAAnG,6BAAA,CACJ;AACDD,4BAA4B,GAAG9B,UAAU,CAAC,CACtCI,SAAS,CAAC;EACN+H,QAAQ,EAAE,4BAA4B;EACtCC,QAAQ,EAAEnI,oBAAoB;EAC9BoI,eAAe,EAAElI,uBAAuB,CAACmI,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACtI,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAE4B,4BAA4B,CAAC;AAChC,SAASA,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}