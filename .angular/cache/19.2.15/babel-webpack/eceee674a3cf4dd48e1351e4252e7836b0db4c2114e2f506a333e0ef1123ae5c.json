{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { DEFAULT_PAGE_SIZE } from './app-settings';\nimport { BehaviorSubject } from 'rxjs';\nimport { Injectable } from '@angular/core';\nimport * as moment from 'moment';\nimport 'moment-timezone';\nexport const DEFAULT_SETTINGS = {\n  pageSize: DEFAULT_PAGE_SIZE,\n  currencyFormat: window.navigator.language,\n  dateFormat: 'DD.MM.YYYY',\n  timeFormat: 'HH:mm:ss',\n  timezoneName: moment.tz.guess()\n};\nlet SettingsService = class SettingsService {\n  constructor() {\n    this.settings = new BehaviorSubject(DEFAULT_SETTINGS);\n  }\n  use(settings) {\n    this.settings.next(settings);\n  }\n  get appSettings$() {\n    return this.settings.asObservable();\n  }\n  get appSettings() {\n    return this.settings.value;\n  }\n  resolve() {\n    return this.settings.value;\n  }\n};\nSettingsService = __decorate([Injectable()], SettingsService);\nexport { SettingsService };", "map": {"version": 3, "names": ["__decorate", "DEFAULT_PAGE_SIZE", "BehaviorSubject", "Injectable", "moment", "DEFAULT_SETTINGS", "pageSize", "currencyFormat", "window", "navigator", "language", "dateFormat", "timeFormat", "timezoneName", "tz", "guess", "SettingsService", "constructor", "settings", "use", "next", "appSettings$", "asObservable", "appSettings", "value", "resolve"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/settings/settings.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { DEFAULT_PAGE_SIZE } from './app-settings';\nimport { BehaviorSubject } from 'rxjs';\nimport { Injectable } from '@angular/core';\nimport * as moment from 'moment';\nimport 'moment-timezone';\nexport const DEFAULT_SETTINGS = {\n    pageSize: DEFAULT_PAGE_SIZE,\n    currencyFormat: window.navigator.language,\n    dateFormat: 'DD.MM.YYYY',\n    timeFormat: 'HH:mm:ss',\n    timezoneName: moment.tz.guess()\n};\nlet SettingsService = class SettingsService {\n    constructor() {\n        this.settings = new BehaviorSubject(DEFAULT_SETTINGS);\n    }\n    use(settings) {\n        this.settings.next(settings);\n    }\n    get appSettings$() {\n        return this.settings.asObservable();\n    }\n    get appSettings() {\n        return this.settings.value;\n    }\n    resolve() {\n        return this.settings.value;\n    }\n};\nSettingsService = __decorate([\n    Injectable()\n], SettingsService);\nexport { SettingsService };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,UAAU,QAAQ,eAAe;AAC1C,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,OAAO,iBAAiB;AACxB,OAAO,MAAMC,gBAAgB,GAAG;EAC5BC,QAAQ,EAAEL,iBAAiB;EAC3BM,cAAc,EAAEC,MAAM,CAACC,SAAS,CAACC,QAAQ;EACzCC,UAAU,EAAE,YAAY;EACxBC,UAAU,EAAE,UAAU;EACtBC,YAAY,EAAET,MAAM,CAACU,EAAE,CAACC,KAAK,CAAC;AAClC,CAAC;AACD,IAAIC,eAAe,GAAG,MAAMA,eAAe,CAAC;EACxCC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,IAAIhB,eAAe,CAACG,gBAAgB,CAAC;EACzD;EACAc,GAAGA,CAACD,QAAQ,EAAE;IACV,IAAI,CAACA,QAAQ,CAACE,IAAI,CAACF,QAAQ,CAAC;EAChC;EACA,IAAIG,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACH,QAAQ,CAACI,YAAY,CAAC,CAAC;EACvC;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACL,QAAQ,CAACM,KAAK;EAC9B;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACP,QAAQ,CAACM,KAAK;EAC9B;AACJ,CAAC;AACDR,eAAe,GAAGhB,UAAU,CAAC,CACzBG,UAAU,CAAC,CAAC,CACf,EAAEa,eAAe,CAAC;AACnB,SAASA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}