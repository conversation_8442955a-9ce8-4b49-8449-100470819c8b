{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiDateTimeChooserComponent } from './swui-date-time-chooser.component';\nimport { SwuiMatCalendarModule } from '../swui-mat-calendar/swui-mat-calendar.module';\nimport { SwuiTimepickerModule } from '../swui-timepicker/swui-timepicker.module';\nexport const DATE_TIME_CHOOSER_MODULES = [ReactiveFormsModule, SwuiMatCalendarModule, SwuiTimepickerModule];\nlet SwuiDateTimeChooserModule = class SwuiDateTimeChooserModule {};\nSwuiDateTimeChooserModule = __decorate([NgModule({\n  declarations: [SwuiDateTimeChooserComponent],\n  imports: [CommonModule, ...DATE_TIME_CHOOSER_MODULES],\n  exports: [SwuiDateTimeChooserComponent]\n})], SwuiDateTimeChooserModule);\nexport { SwuiDateTimeChooserModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ReactiveFormsModule", "SwuiDateTimeChooserComponent", "SwuiMatCalendarModule", "SwuiTimepickerModule", "DATE_TIME_CHOOSER_MODULES", "SwuiDateTimeChooserModule", "__decorate", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-chooser/swui-date-time-chooser.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiDateTimeChooserComponent } from './swui-date-time-chooser.component';\nimport { SwuiMatCalendarModule } from '../swui-mat-calendar/swui-mat-calendar.module';\nimport { SwuiTimepickerModule } from '../swui-timepicker/swui-timepicker.module';\n\n\nexport const DATE_TIME_CHOOSER_MODULES = [\n  ReactiveFormsModule,\n  SwuiMatCalendarModule,\n  SwuiTimepickerModule,\n];\n\n@NgModule({\n  declarations: [SwuiDateTimeChooserComponent],\n  imports: [\n    CommonModule,\n    ...DATE_TIME_CHOOSER_MODULES,\n  ],\n  exports: [\n    SwuiDateTimeChooserComponent,\n  ]\n})\n\nexport class SwuiDateTimeChooserModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,oBAAoB,QAAQ,2CAA2C;AAGhF,OAAO,MAAMC,yBAAyB,GAAG,CACvCJ,mBAAmB,EACnBE,qBAAqB,EACrBC,oBAAoB,CACrB;AAaM,IAAME,yBAAyB,GAA/B,MAAMA,yBAAyB,GACrC;AADYA,yBAAyB,GAAAC,UAAA,EAXrCR,QAAQ,CAAC;EACRS,YAAY,EAAE,CAACN,4BAA4B,CAAC;EAC5CO,OAAO,EAAE,CACPT,YAAY,EACZ,GAAGK,yBAAyB,CAC7B;EACDK,OAAO,EAAE,CACPR,4BAA4B;CAE/B,CAAC,C,EAEWI,yBAAyB,CACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}