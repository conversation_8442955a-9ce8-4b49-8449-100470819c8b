{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatDialogContainer, _MatDialog, _MatDialogClose, _MatDialogLayoutSection, _MatDialogTitle, _MatDialogContent, _MatDialogActions, _MatDialogModule;\nfunction _MatDialogContainer_ng_template_2_Template(rf, ctx) {}\nimport { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { inject, ANIMATION_MODULE_TYPE, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, InjectionToken, Injectable, ElementRef, Directive, Input, NgModule } from '@angular/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable } from '@angular/cdk/scrolling';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nclass MatDialogConfig {\n  constructor() {\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This affects what is available for injection and the change detection order for the\n     * component instantiated inside of the dialog. This does not affect where the dialog\n     * content will be rendered.\n     */\n    _defineProperty(this, \"viewContainerRef\", void 0);\n    /**\n     * Injector used for the instantiation of the component to be attached. If provided,\n     * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n     */\n    _defineProperty(this, \"injector\", void 0);\n    /** ID for the dialog. If omitted, a unique one will be generated. */\n    _defineProperty(this, \"id\", void 0);\n    /** The ARIA role of the dialog element. */\n    _defineProperty(this, \"role\", 'dialog');\n    /** Custom class for the overlay pane. */\n    _defineProperty(this, \"panelClass\", '');\n    /** Whether the dialog has a backdrop. */\n    _defineProperty(this, \"hasBackdrop\", true);\n    /** Custom class for the backdrop. */\n    _defineProperty(this, \"backdropClass\", '');\n    /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n    _defineProperty(this, \"disableClose\", false);\n    /** Width of the dialog. */\n    _defineProperty(this, \"width\", '');\n    /** Height of the dialog. */\n    _defineProperty(this, \"height\", '');\n    /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n    _defineProperty(this, \"minWidth\", void 0);\n    /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n    _defineProperty(this, \"minHeight\", void 0);\n    /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n    _defineProperty(this, \"maxWidth\", void 0);\n    /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n    _defineProperty(this, \"maxHeight\", void 0);\n    /** Position overrides. */\n    _defineProperty(this, \"position\", void 0);\n    /** Data being injected into the child component. */\n    _defineProperty(this, \"data\", null);\n    /** Layout direction for the dialog's content. */\n    _defineProperty(this, \"direction\", void 0);\n    /** ID of the element that describes the dialog. */\n    _defineProperty(this, \"ariaDescribedBy\", null);\n    /** ID of the element that labels the dialog. */\n    _defineProperty(this, \"ariaLabelledBy\", null);\n    /** Aria label to assign to the dialog element. */\n    _defineProperty(this, \"ariaLabel\", null);\n    /**\n     * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n     * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n     * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n     */\n    _defineProperty(this, \"ariaModal\", false);\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    _defineProperty(this, \"autoFocus\", 'first-tabbable');\n    /**\n     * Whether the dialog should restore focus to the\n     * previously-focused element, after it's closed.\n     */\n    _defineProperty(this, \"restoreFocus\", true);\n    /** Whether to wait for the opening animation to finish before trapping focus. */\n    _defineProperty(this, \"delayFocusTrap\", true);\n    /** Scroll strategy to be used for the dialog. */\n    _defineProperty(this, \"scrollStrategy\", void 0);\n    /**\n     * Whether the dialog should close when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    _defineProperty(this, \"closeOnNavigation\", true);\n    /**\n     * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n     * @deprecated No longer used. Will be removed.\n     * @breaking-change 20.0.0\n     */\n    _defineProperty(this, \"componentFactoryResolver\", void 0);\n    /**\n     * Duration of the enter animation in ms.\n     * Should be a number, string type is deprecated.\n     * @breaking-change 17.0.0 Remove string signature.\n     */\n    _defineProperty(this, \"enterAnimationDuration\", void 0);\n    /**\n     * Duration of the exit animation in ms.\n     * Should be a number, string type is deprecated.\n     * @breaking-change 17.0.0 Remove string signature.\n     */\n    _defineProperty(this, \"exitAnimationDuration\", void 0);\n  }\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\nclass MatDialogContainer extends CdkDialogContainer {\n  constructor(...args) {\n    var _parseCssTime, _parseCssTime2;\n    super(...args);\n    _defineProperty(this, \"_animationMode\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }));\n    /** Emits when an animation state changes. */\n    _defineProperty(this, \"_animationStateChanged\", new EventEmitter());\n    /** Whether animations are enabled. */\n    _defineProperty(this, \"_animationsEnabled\", this._animationMode !== 'NoopAnimations');\n    /** Number of actions projected in the dialog. */\n    _defineProperty(this, \"_actionSectionCount\", 0);\n    /** Host element of the dialog container component. */\n    _defineProperty(this, \"_hostElement\", this._elementRef.nativeElement);\n    /** Duration of the dialog open animation. */\n    _defineProperty(this, \"_enterAnimationDuration\", this._animationsEnabled ? (_parseCssTime = parseCssTime(this._config.enterAnimationDuration)) !== null && _parseCssTime !== void 0 ? _parseCssTime : OPEN_ANIMATION_DURATION : 0);\n    /** Duration of the dialog close animation. */\n    _defineProperty(this, \"_exitAnimationDuration\", this._animationsEnabled ? (_parseCssTime2 = parseCssTime(this._config.exitAnimationDuration)) !== null && _parseCssTime2 !== void 0 ? _parseCssTime2 : CLOSE_ANIMATION_DURATION : 0);\n    /** Current timer for dialog animations. */\n    _defineProperty(this, \"_animationTimer\", null);\n    /**\n     * Completes the dialog open by clearing potential animation classes, trapping\n     * focus and emitting an opened event.\n     */\n    _defineProperty(this, \"_finishDialogOpen\", () => {\n      this._clearAnimationClasses();\n      this._openAnimationDone(this._enterAnimationDuration);\n    });\n    /**\n     * Completes the dialog close by clearing potential animation classes, restoring\n     * focus and emitting a closed event.\n     */\n    _defineProperty(this, \"_finishDialogClose\", () => {\n      this._clearAnimationClasses();\n      this._animationStateChanged.emit({\n        state: 'closed',\n        totalTime: this._exitAnimationDuration\n      });\n    });\n  }\n  _contentAttached() {\n    // Delegate to the original dialog-container initialization (i.e. saving the\n    // previous element, setting up the focus trap and moving focus to the container).\n    super._contentAttached();\n    // Note: Usually we would be able to use the MDC dialog foundation here to handle\n    // the dialog animation for us, but there are a few reasons why we just leverage\n    // their styles and not use the runtime foundation code:\n    //   1. Foundation does not allow us to disable animations.\n    //   2. Foundation contains unnecessary features we don't need and aren't\n    //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n    this._startOpenAnimation();\n  }\n  /** Starts the dialog open animation if enabled. */\n  _startOpenAnimation() {\n    this._animationStateChanged.emit({\n      state: 'opening',\n      totalTime: this._enterAnimationDuration\n    });\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n      // We need to give the `setProperty` call from above some time to be applied.\n      // One would expect that the open class is added once the animation finished, but MDC\n      // uses the open class in combination with the opening class to start the animation.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n      this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n    } else {\n      this._hostElement.classList.add(OPEN_CLASS);\n      // Note: We could immediately finish the dialog opening here with noop animations,\n      // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n      // Executing this immediately would mean that `afterOpened` emits synchronously\n      // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n      Promise.resolve().then(() => this._finishDialogOpen());\n    }\n  }\n  /**\n   * Starts the exit animation of the dialog if enabled. This method is\n   * called by the dialog ref.\n   */\n  _startExitAnimation() {\n    this._animationStateChanged.emit({\n      state: 'closing',\n      totalTime: this._exitAnimationDuration\n    });\n    this._hostElement.classList.remove(OPEN_CLASS);\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n      // We need to give the `setProperty` call from above some time to be applied.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n      this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n    } else {\n      // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n      // set up before any user can subscribe to the backdrop click. The subscription triggers\n      // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n      // animation state event if animations are disabled, the overlay would be disposed\n      // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n      // skipped. We work around this by waiting with the dialog close until the next tick when\n      // all subscriptions have been fired as expected. This is not an ideal solution, but\n      // there doesn't seem to be any other good way. Alternatives that have been considered:\n      //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n      //      Also this issue is specific to the MDC implementation where the dialog could\n      //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n      //      and closing always takes at least a tick.\n      //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n      //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n      //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n      // Based on the fact that this is specific to the MDC-based implementation of the dialog\n      // animations, the defer is applied here.\n      Promise.resolve().then(() => this._finishDialogClose());\n    }\n  }\n  /**\n   * Updates the number action sections.\n   * @param delta Increase/decrease in the number of sections.\n   */\n  _updateActionSectionCount(delta) {\n    this._actionSectionCount += delta;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Clears all dialog animation classes. */\n  _clearAnimationClasses() {\n    this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n  }\n  _waitForAnimationToComplete(duration, callback) {\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n    // Note that we want this timer to run inside the NgZone, because we want\n    // the related events like `afterClosed` to be inside the zone as well.\n    this._animationTimer = setTimeout(callback, duration);\n  }\n  /** Runs a callback in `requestAnimationFrame`, if available. */\n  _requestAnimationFrame(callback) {\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame === 'function') {\n        requestAnimationFrame(callback);\n      } else {\n        callback();\n      }\n    });\n  }\n  _captureInitialFocus() {\n    if (!this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Callback for when the open dialog animation has finished. Intended to\n   * be called by sub-classes that use different animation implementations.\n   */\n  _openAnimationDone(totalTime) {\n    if (this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n    this._animationStateChanged.next({\n      state: 'opened',\n      totalTime\n    });\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n  }\n  attachComponentPortal(portal) {\n    // When a component is passed into the dialog, the host element interrupts\n    // the `display:flex` from affecting the dialog title, content, and\n    // actions. To fix this, we make the component host `display: contents` by\n    // marking its host with the `mat-mdc-dialog-component-host` class.\n    //\n    // Note that this problem does not exist when a template ref is used since\n    // the title, contents, and actions are then nested directly under the\n    // dialog surface.\n    const ref = super.attachComponentPortal(portal);\n    ref.location.nativeElement.classList.add('mat-mdc-dialog-component-host');\n    return ref;\n  }\n}\n_MatDialogContainer = MatDialogContainer;\n_defineProperty(MatDialogContainer, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatDialogContainer_BaseFactory;\n  return function _MatDialogContainer_Factory(__ngFactoryType__) {\n    return (ɵ_MatDialogContainer_BaseFactory || (ɵ_MatDialogContainer_BaseFactory = i0.ɵɵgetInheritedFactory(_MatDialogContainer)))(__ngFactoryType__ || _MatDialogContainer);\n  };\n})());\n_defineProperty(MatDialogContainer, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatDialogContainer,\n  selectors: [[\"mat-dialog-container\"]],\n  hostAttrs: [\"tabindex\", \"-1\", 1, \"mat-mdc-dialog-container\", \"mdc-dialog\"],\n  hostVars: 10,\n  hostBindings: function _MatDialogContainer_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx._config.id);\n      i0.ɵɵattribute(\"aria-modal\", ctx._config.ariaModal)(\"role\", ctx._config.role)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", !ctx._animationsEnabled)(\"mat-mdc-dialog-container-with-actions\", ctx._actionSectionCount > 0);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 3,\n  vars: 0,\n  consts: [[1, \"mat-mdc-dialog-inner-container\", \"mdc-dialog__container\"], [1, \"mat-mdc-dialog-surface\", \"mdc-dialog__surface\"], [\"cdkPortalOutlet\", \"\"]],\n  template: function _MatDialogContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵtemplate(2, _MatDialogContainer_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n      i0.ɵɵelementEnd()();\n    }\n  },\n  dependencies: [CdkPortalOutlet],\n  styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mdc-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mdc-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mdc-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mdc-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mdc-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mdc-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mdc-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mdc-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mdc-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mdc-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mdc-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mdc-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mdc-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mdc-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'mat-mdc-dialog-container mdc-dialog',\n        'tabindex': '-1',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[id]': '_config.id',\n        '[attr.role]': '_config.role',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n        '[class._mat-animation-noopable]': '!_animationsEnabled',\n        '[class.mat-mdc-dialog-container-with-actions]': '_actionSectionCount > 0'\n      },\n      template: \"<div class=\\\"mat-mdc-dialog-inner-container mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet />\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mdc-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mdc-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mdc-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mdc-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mdc-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mdc-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mdc-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mdc-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mdc-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mdc-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mdc-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mdc-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mdc-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mdc-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\\n\"]\n    }]\n  }], null, null);\n})();\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n  if (time == null) {\n    return null;\n  }\n  if (typeof time === 'number') {\n    return time;\n  }\n  if (time.endsWith('ms')) {\n    return coerceNumberProperty(time.substring(0, time.length - 2));\n  }\n  if (time.endsWith('s')) {\n    return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n  }\n  if (time === '0') {\n    return 0;\n  }\n  return null; // anything else is invalid.\n}\nvar MatDialogState;\n(function (MatDialogState) {\n  MatDialogState[MatDialogState[\"OPEN\"] = 0] = \"OPEN\";\n  MatDialogState[MatDialogState[\"CLOSING\"] = 1] = \"CLOSING\";\n  MatDialogState[MatDialogState[\"CLOSED\"] = 2] = \"CLOSED\";\n})(MatDialogState || (MatDialogState = {}));\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n  constructor(_ref, config, _containerInstance) {\n    _defineProperty(this, \"_ref\", void 0);\n    _defineProperty(this, \"_containerInstance\", void 0);\n    /** The instance of component opened into the dialog. */\n    _defineProperty(this, \"componentInstance\", void 0);\n    /**\n     * `ComponentRef` of the component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    _defineProperty(this, \"componentRef\", void 0);\n    /** Whether the user is allowed to close the dialog. */\n    _defineProperty(this, \"disableClose\", void 0);\n    /** Unique ID for the dialog. */\n    _defineProperty(this, \"id\", void 0);\n    /** Subject for notifying the user that the dialog has finished opening. */\n    _defineProperty(this, \"_afterOpened\", new Subject());\n    /** Subject for notifying the user that the dialog has started closing. */\n    _defineProperty(this, \"_beforeClosed\", new Subject());\n    /** Result to be passed to afterClosed. */\n    _defineProperty(this, \"_result\", void 0);\n    /** Handle to the timeout that's running as a fallback in case the exit animation doesn't fire. */\n    _defineProperty(this, \"_closeFallbackTimeout\", void 0);\n    /** Current state of the dialog. */\n    _defineProperty(this, \"_state\", MatDialogState.OPEN);\n    // TODO(crisbeto): we shouldn't have to declare this property, because `DialogRef.close`\n    // already has a second `options` parameter that we can use. The problem is that internal tests\n    // have assertions like `expect(MatDialogRef.close).toHaveBeenCalledWith(foo)` which will break,\n    // because it'll be called with two arguments by things like `MatDialogClose`.\n    /** Interaction that caused the dialog to close. */\n    _defineProperty(this, \"_closeInteractionType\", void 0);\n    this._ref = _ref;\n    this._containerInstance = _containerInstance;\n    this.disableClose = config.disableClose;\n    this.id = _ref.id;\n    // Used to target panels specifically tied to dialogs.\n    _ref.addPanelClass('mat-mdc-dialog-panel');\n    // Emit when opening animation completes\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'opened'), take(1)).subscribe(() => {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    });\n    // Dispose overlay when closing animation is complete\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closed'), take(1)).subscribe(() => {\n      clearTimeout(this._closeFallbackTimeout);\n      this._finishDialogClose();\n    });\n    _ref.overlayRef.detachments().subscribe(() => {\n      this._beforeClosed.next(this._result);\n      this._beforeClosed.complete();\n      this._finishDialogClose();\n    });\n    merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n      if (!this.disableClose) {\n        event.preventDefault();\n        _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param dialogResult Optional result to return to the dialog opener.\n   */\n  close(dialogResult) {\n    this._result = dialogResult;\n    // Transition the backdrop in parallel to the dialog.\n    this._containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closing'), take(1)).subscribe(event => {\n      this._beforeClosed.next(dialogResult);\n      this._beforeClosed.complete();\n      this._ref.overlayRef.detachBackdrop();\n      // The logic that disposes of the overlay depends on the exit animation completing, however\n      // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n      // timeout which will clean everything up if the animation hasn't fired within the specified\n      // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n      // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n      this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n    });\n    this._state = MatDialogState.CLOSING;\n    this._containerInstance._startExitAnimation();\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished opening.\n   */\n  afterOpened() {\n    return this._afterOpened;\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished closing.\n   */\n  afterClosed() {\n    return this._ref.closed;\n  }\n  /**\n   * Gets an observable that is notified when the dialog has started closing.\n   */\n  beforeClosed() {\n    return this._beforeClosed;\n  }\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n  backdropClick() {\n    return this._ref.backdropClick;\n  }\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n  keydownEvents() {\n    return this._ref.keydownEvents;\n  }\n  /**\n   * Updates the dialog's position.\n   * @param position New dialog position.\n   */\n  updatePosition(position) {\n    let strategy = this._ref.config.positionStrategy;\n    if (position && (position.left || position.right)) {\n      position.left ? strategy.left(position.left) : strategy.right(position.right);\n    } else {\n      strategy.centerHorizontally();\n    }\n    if (position && (position.top || position.bottom)) {\n      position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n    } else {\n      strategy.centerVertically();\n    }\n    this._ref.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this._ref.updateSize(width, height);\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this._ref.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this._ref.removePanelClass(classes);\n    return this;\n  }\n  /** Gets the current state of the dialog's lifecycle. */\n  getState() {\n    return this._state;\n  }\n  /**\n   * Finishes the dialog close by updating the state of the dialog\n   * and disposing the overlay.\n   */\n  _finishDialogClose() {\n    this._state = MatDialogState.CLOSED;\n    this._ref.close(this._result, {\n      focusOrigin: this._closeInteractionType\n    });\n    this.componentInstance = null;\n  }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n  ref._closeInteractionType = interactionType;\n  return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = new InjectionToken('mat-mdc-dialog-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.block();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst MAT_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Service to open Material Design modal dialogs.\n */\nclass MatDialog {\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n  /**\n   * Stream that emits when all open dialog have finished closing.\n   * Will emit on subscribe if there are no open dialogs to begin with.\n   */\n\n  constructor() {\n    _defineProperty(this, \"_overlay\", inject(Overlay));\n    _defineProperty(this, \"_defaultOptions\", inject(MAT_DIALOG_DEFAULT_OPTIONS, {\n      optional: true\n    }));\n    _defineProperty(this, \"_scrollStrategy\", inject(MAT_DIALOG_SCROLL_STRATEGY));\n    _defineProperty(this, \"_parentDialog\", inject(MatDialog, {\n      optional: true,\n      skipSelf: true\n    }));\n    _defineProperty(this, \"_idGenerator\", inject(_IdGenerator));\n    _defineProperty(this, \"_dialog\", inject(Dialog));\n    _defineProperty(this, \"_openDialogsAtThisLevel\", []);\n    _defineProperty(this, \"_afterAllClosedAtThisLevel\", new Subject());\n    _defineProperty(this, \"_afterOpenedAtThisLevel\", new Subject());\n    _defineProperty(this, \"dialogConfigClass\", MatDialogConfig);\n    _defineProperty(this, \"_dialogRefConstructor\", void 0);\n    _defineProperty(this, \"_dialogContainerType\", void 0);\n    _defineProperty(this, \"_dialogDataToken\", void 0);\n    _defineProperty(this, \"afterAllClosed\", defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined))));\n    this._dialogRefConstructor = MatDialogRef;\n    this._dialogContainerType = MatDialogContainer;\n    this._dialogDataToken = MAT_DIALOG_DATA;\n  }\n  open(componentOrTemplateRef, config) {\n    let dialogRef;\n    config = _objectSpread(_objectSpread({}, this._defaultOptions || new MatDialogConfig()), config);\n    config.id = config.id || this._idGenerator.getId('mat-mdc-dialog-');\n    config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n    const cdkRef = this._dialog.open(componentOrTemplateRef, _objectSpread(_objectSpread({}, config), {}, {\n      positionStrategy: this._overlay.position().global().centerHorizontally().centerVertically(),\n      // Disable closing since we need to sync it up to the animation ourselves.\n      disableClose: true,\n      // Disable closing on destroy, because this service cleans up its open dialogs as well.\n      // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n      // the dialogs immediately whereas we want it to wait for the animations to finish.\n      closeOnDestroy: false,\n      // Disable closing on detachments so that we can sync up the animation.\n      // The Material dialog ref handles this manually.\n      closeOnOverlayDetachments: false,\n      container: {\n        type: this._dialogContainerType,\n        providers: () => [\n        // Provide our config as the CDK config as well since it has the same interface as the\n        // CDK one, but it contains the actual values passed in by the user for things like\n        // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n        {\n          provide: this.dialogConfigClass,\n          useValue: config\n        }, {\n          provide: DialogConfig,\n          useValue: config\n        }]\n      },\n      templateContext: () => ({\n        dialogRef\n      }),\n      providers: (ref, cdkConfig, dialogContainer) => {\n        var _config;\n        dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n        dialogRef.updatePosition((_config = config) === null || _config === void 0 ? void 0 : _config.position);\n        return [{\n          provide: this._dialogContainerType,\n          useValue: dialogContainer\n        }, {\n          provide: this._dialogDataToken,\n          useValue: cdkConfig.data\n        }, {\n          provide: this._dialogRefConstructor,\n          useValue: dialogRef\n        }];\n      }\n    }));\n    // This can't be assigned in the `providers` callback, because\n    // the instance hasn't been assigned to the CDK ref yet.\n    dialogRef.componentRef = cdkRef.componentRef;\n    dialogRef.componentInstance = cdkRef.componentInstance;\n    this.openDialogs.push(dialogRef);\n    this.afterOpened.next(dialogRef);\n    dialogRef.afterClosed().subscribe(() => {\n      const index = this.openDialogs.indexOf(dialogRef);\n      if (index > -1) {\n        this.openDialogs.splice(index, 1);\n        if (!this.openDialogs.length) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    });\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    this._closeDialogs(this.openDialogs);\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Only close the dialogs at this level on destroy\n    // since the parent service may still be active.\n    this._closeDialogs(this._openDialogsAtThisLevel);\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n  }\n  _closeDialogs(dialogs) {\n    let i = dialogs.length;\n    while (i--) {\n      dialogs[i].close();\n    }\n  }\n}\n_MatDialog = MatDialog;\n_defineProperty(MatDialog, \"\\u0275fac\", function _MatDialog_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatDialog)();\n});\n_defineProperty(MatDialog, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _MatDialog,\n  factory: _MatDialog.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialog, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Button that will close the current dialog.\n */\nclass MatDialogClose {\n  constructor() {\n    _defineProperty(this, \"dialogRef\", inject(MatDialogRef, {\n      optional: true\n    }));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_dialog\", inject(MatDialog));\n    /** Screen-reader label for the button. */\n    _defineProperty(this, \"ariaLabel\", void 0);\n    /** Default to \"button\" to prevents accidental form submits. */\n    _defineProperty(this, \"type\", 'button');\n    /** Dialog close input. */\n    _defineProperty(this, \"dialogResult\", void 0);\n    _defineProperty(this, \"_matDialogClose\", void 0);\n  }\n  ngOnInit() {\n    if (!this.dialogRef) {\n      // When this directive is included in a dialog via TemplateRef (rather than being\n      // in a Component), the DialogRef isn't available via injection because embedded\n      // views cannot be given a custom injector. Instead, we look up the DialogRef by\n      // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n      // be resolved at constructor time.\n      this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n    }\n  }\n  ngOnChanges(changes) {\n    const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n    if (proxiedChange) {\n      this.dialogResult = proxiedChange.currentValue;\n    }\n  }\n  _onButtonClick(event) {\n    // Determinate the focus origin using the click event, because using the FocusMonitor will\n    // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n    // dialog, and therefore clicking the button won't result in a focus change. This means that\n    // the FocusMonitor won't detect any origin change, and will always output `program`.\n    _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n  }\n}\n_MatDialogClose = MatDialogClose;\n_defineProperty(MatDialogClose, \"\\u0275fac\", function _MatDialogClose_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatDialogClose)();\n});\n_defineProperty(MatDialogClose, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatDialogClose,\n  selectors: [[\"\", \"mat-dialog-close\", \"\"], [\"\", \"matDialogClose\", \"\"]],\n  hostVars: 2,\n  hostBindings: function _MatDialogClose_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _MatDialogClose_click_HostBindingHandler($event) {\n        return ctx._onButtonClick($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"type\", ctx.type);\n    }\n  },\n  inputs: {\n    ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n    type: \"type\",\n    dialogResult: [0, \"mat-dialog-close\", \"dialogResult\"],\n    _matDialogClose: [0, \"matDialogClose\", \"_matDialogClose\"]\n  },\n  exportAs: [\"matDialogClose\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogClose, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-dialog-close], [matDialogClose]',\n      exportAs: 'matDialogClose',\n      host: {\n        '(click)': '_onButtonClick($event)',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.type]': 'type'\n      }\n    }]\n  }], () => [], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    type: [{\n      type: Input\n    }],\n    dialogResult: [{\n      type: Input,\n      args: ['mat-dialog-close']\n    }],\n    _matDialogClose: [{\n      type: Input,\n      args: ['matDialogClose']\n    }]\n  });\n})();\nclass MatDialogLayoutSection {\n  constructor() {\n    _defineProperty(this, \"_dialogRef\", inject(MatDialogRef, {\n      optional: true\n    }));\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_dialog\", inject(MatDialog));\n  }\n  ngOnInit() {\n    if (!this._dialogRef) {\n      this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n    }\n    if (this._dialogRef) {\n      Promise.resolve().then(() => {\n        this._onAdd();\n      });\n    }\n  }\n  ngOnDestroy() {\n    var _this$_dialogRef;\n    // Note: we null check because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    const instance = (_this$_dialogRef = this._dialogRef) === null || _this$_dialogRef === void 0 ? void 0 : _this$_dialogRef._containerInstance;\n    if (instance) {\n      Promise.resolve().then(() => {\n        this._onRemove();\n      });\n    }\n  }\n}\n_MatDialogLayoutSection = MatDialogLayoutSection;\n_defineProperty(MatDialogLayoutSection, \"\\u0275fac\", function _MatDialogLayoutSection_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatDialogLayoutSection)();\n});\n_defineProperty(MatDialogLayoutSection, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatDialogLayoutSection\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogLayoutSection, [{\n    type: Directive\n  }], () => [], null);\n})();\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nclass MatDialogTitle extends MatDialogLayoutSection {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"id\", inject(_IdGenerator).getId('mat-mdc-dialog-title-'));\n  }\n  _onAdd() {\n    var _this$_dialogRef$_con, _this$_dialogRef$_con2;\n    // Note: we null check the queue, because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    (_this$_dialogRef$_con = this._dialogRef._containerInstance) === null || _this$_dialogRef$_con === void 0 || (_this$_dialogRef$_con2 = _this$_dialogRef$_con._addAriaLabelledBy) === null || _this$_dialogRef$_con2 === void 0 || _this$_dialogRef$_con2.call(_this$_dialogRef$_con, this.id);\n  }\n  _onRemove() {\n    var _this$_dialogRef2, _this$_dialogRef2$_re;\n    (_this$_dialogRef2 = this._dialogRef) === null || _this$_dialogRef2 === void 0 || (_this$_dialogRef2 = _this$_dialogRef2._containerInstance) === null || _this$_dialogRef2 === void 0 || (_this$_dialogRef2$_re = _this$_dialogRef2._removeAriaLabelledBy) === null || _this$_dialogRef2$_re === void 0 || _this$_dialogRef2$_re.call(_this$_dialogRef2, this.id);\n  }\n}\n_MatDialogTitle = MatDialogTitle;\n_defineProperty(MatDialogTitle, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatDialogTitle_BaseFactory;\n  return function _MatDialogTitle_Factory(__ngFactoryType__) {\n    return (ɵ_MatDialogTitle_BaseFactory || (ɵ_MatDialogTitle_BaseFactory = i0.ɵɵgetInheritedFactory(_MatDialogTitle)))(__ngFactoryType__ || _MatDialogTitle);\n  };\n})());\n_defineProperty(MatDialogTitle, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatDialogTitle,\n  selectors: [[\"\", \"mat-dialog-title\", \"\"], [\"\", \"matDialogTitle\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-dialog-title\", \"mdc-dialog__title\"],\n  hostVars: 1,\n  hostBindings: function _MatDialogTitle_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id);\n    }\n  },\n  inputs: {\n    id: \"id\"\n  },\n  exportAs: [\"matDialogTitle\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogTitle, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-dialog-title], [matDialogTitle]',\n      exportAs: 'matDialogTitle',\n      host: {\n        'class': 'mat-mdc-dialog-title mdc-dialog__title',\n        '[id]': 'id'\n      }\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Scrollable content container of a dialog.\n */\nclass MatDialogContent {}\n_MatDialogContent = MatDialogContent;\n_defineProperty(MatDialogContent, \"\\u0275fac\", function _MatDialogContent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatDialogContent)();\n});\n_defineProperty(MatDialogContent, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatDialogContent,\n  selectors: [[\"\", \"mat-dialog-content\", \"\"], [\"mat-dialog-content\"], [\"\", \"matDialogContent\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-dialog-content\", \"mdc-dialog__content\"],\n  features: [i0.ɵɵHostDirectivesFeature([i1.CdkScrollable])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogContent, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n      host: {\n        'class': 'mat-mdc-dialog-content mdc-dialog__content'\n      },\n      hostDirectives: [CdkScrollable]\n    }]\n  }], null, null);\n})();\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nclass MatDialogActions extends MatDialogLayoutSection {\n  constructor(...args) {\n    super(...args);\n    /**\n     * Horizontal alignment of action buttons.\n     */\n    _defineProperty(this, \"align\", void 0);\n  }\n  _onAdd() {\n    var _this$_dialogRef$_con3, _this$_dialogRef$_con4;\n    (_this$_dialogRef$_con3 = this._dialogRef._containerInstance) === null || _this$_dialogRef$_con3 === void 0 || (_this$_dialogRef$_con4 = _this$_dialogRef$_con3._updateActionSectionCount) === null || _this$_dialogRef$_con4 === void 0 || _this$_dialogRef$_con4.call(_this$_dialogRef$_con3, 1);\n  }\n  _onRemove() {\n    var _this$_dialogRef$_con5, _this$_dialogRef$_con6;\n    (_this$_dialogRef$_con5 = this._dialogRef._containerInstance) === null || _this$_dialogRef$_con5 === void 0 || (_this$_dialogRef$_con6 = _this$_dialogRef$_con5._updateActionSectionCount) === null || _this$_dialogRef$_con6 === void 0 || _this$_dialogRef$_con6.call(_this$_dialogRef$_con5, -1);\n  }\n}\n_MatDialogActions = MatDialogActions;\n_defineProperty(MatDialogActions, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatDialogActions_BaseFactory;\n  return function _MatDialogActions_Factory(__ngFactoryType__) {\n    return (ɵ_MatDialogActions_BaseFactory || (ɵ_MatDialogActions_BaseFactory = i0.ɵɵgetInheritedFactory(_MatDialogActions)))(__ngFactoryType__ || _MatDialogActions);\n  };\n})());\n_defineProperty(MatDialogActions, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatDialogActions,\n  selectors: [[\"\", \"mat-dialog-actions\", \"\"], [\"mat-dialog-actions\"], [\"\", \"matDialogActions\", \"\"]],\n  hostAttrs: [1, \"mat-mdc-dialog-actions\", \"mdc-dialog__actions\"],\n  hostVars: 6,\n  hostBindings: function _MatDialogActions_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-dialog-actions-align-start\", ctx.align === \"start\")(\"mat-mdc-dialog-actions-align-center\", ctx.align === \"center\")(\"mat-mdc-dialog-actions-align-end\", ctx.align === \"end\");\n    }\n  },\n  inputs: {\n    align: \"align\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogActions, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n      host: {\n        'class': 'mat-mdc-dialog-actions mdc-dialog__actions',\n        '[class.mat-mdc-dialog-actions-align-start]': 'align === \"start\"',\n        '[class.mat-mdc-dialog-actions-align-center]': 'align === \"center\"',\n        '[class.mat-mdc-dialog-actions-align-end]': 'align === \"end\"'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n  let parent = element.nativeElement.parentElement;\n  while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n    parent = parent.parentElement;\n  }\n  return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\nconst DIRECTIVES = [MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent];\nclass MatDialogModule {}\n_MatDialogModule = MatDialogModule;\n_defineProperty(MatDialogModule, \"\\u0275fac\", function _MatDialogModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatDialogModule)();\n});\n_defineProperty(MatDialogModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatDialogModule,\n  imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent],\n  exports: [MatCommonModule, MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent]\n}));\n_defineProperty(MatDialogModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MatDialog],\n  imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, ...DIRECTIVES],\n      exports: [MatCommonModule, ...DIRECTIVES],\n      providers: [MatDialog]\n    }]\n  }], null, null);\n})();\nexport { MatDialogActions as M, _closeDialogVia as _, MatDialogClose as a, MatDialogTitle as b, MatDialogContent as c, MatDialogContainer as d, MAT_DIALOG_DATA as e, MAT_DIALOG_DEFAULT_OPTIONS as f, MAT_DIALOG_SCROLL_STRATEGY as g, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY as h, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER as i, MatDialog as j, MatDialogConfig as k, MatDialogState as l, MatDialogRef as m, MatDialogModule as n };\n//# sourceMappingURL=module-BnDTus5c.mjs.map", "map": {"version": 3, "names": ["CdkDialogContainer", "Dialog", "DialogConfig", "DialogModule", "Overlay", "OverlayModule", "CdkPortalOutlet", "PortalModule", "i0", "inject", "ANIMATION_MODULE_TYPE", "EventEmitter", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "InjectionToken", "Injectable", "ElementRef", "Directive", "Input", "NgModule", "coerceNumberProperty", "Subject", "merge", "defer", "filter", "take", "startWith", "ESCAPE", "hasModifierKey", "_IdGenerator", "i1", "CdkScrollable", "M", "MatCommonModule", "MatDialogConfig", "constructor", "_defineProperty", "OPEN_CLASS", "OPENING_CLASS", "CLOSING_CLASS", "OPEN_ANIMATION_DURATION", "CLOSE_ANIMATION_DURATION", "MatDialogContainer", "args", "_parseCssTime", "_parseCssTime2", "optional", "_animationMode", "_elementRef", "nativeElement", "_animationsEnabled", "parseCssTime", "_config", "enterAnimationDuration", "exitAnimationDuration", "_clearAnimationClasses", "_openAnimationDone", "_enterAnimationDuration", "_animationStateChanged", "emit", "state", "totalTime", "_exitAnimationDuration", "_contentAttached", "_startOpenAnimation", "_hostElement", "style", "setProperty", "TRANSITION_DURATION_PROPERTY", "_requestAnimationFrame", "classList", "add", "_waitForAnimationToComplete", "_finishDialogOpen", "Promise", "resolve", "then", "_startExitAnimation", "remove", "_finishDialogClose", "_updateActionSectionCount", "delta", "_actionSectionCount", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duration", "callback", "_animationTimer", "clearTimeout", "setTimeout", "_ngZone", "runOutsideAngular", "requestAnimationFrame", "_captureInitialFocus", "delayFocusTrap", "_trapFocus", "next", "ngOnDestroy", "attachComponentPortal", "portal", "ref", "location", "_MatDialogContainer", "ɵ_MatDialogContainer_BaseFactory", "_MatDialogContainer_Factory", "__ngFactoryType__", "ɵɵgetInheritedFactory", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "_MatDialogContainer_HostBindings", "rf", "ctx", "ɵɵhostProperty", "id", "ɵɵattribute", "ariaModal", "role", "aria<PERSON><PERSON><PERSON>", "_ariaLabelledByQueue", "ariaDescribedBy", "ɵɵclassProp", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "_MatDialogContainer_Template", "ɵɵelementStart", "ɵɵtemplate", "_MatDialogContainer_ng_template_2_Template", "ɵɵelementEnd", "dependencies", "styles", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "selector", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "host", "time", "endsWith", "substring", "length", "MatDialogState", "MatDialogRef", "_ref", "config", "_containerInstance", "OPEN", "disableClose", "addPanelClass", "pipe", "event", "subscribe", "_afterOpened", "complete", "_closeFallbackTimeout", "overlayRef", "detachments", "_beforeClosed", "_result", "backdropClick", "keydownEvents", "keyCode", "preventDefault", "_closeDialogVia", "close", "dialogResult", "detachBackdrop", "_state", "CLOSING", "afterOpened", "afterClosed", "closed", "beforeClosed", "updatePosition", "position", "strategy", "positionStrategy", "left", "right", "centerHorizontally", "top", "bottom", "centerVertically", "updateSize", "width", "height", "classes", "removePanelClass", "getState", "CLOSED", "<PERSON><PERSON><PERSON><PERSON>", "_closeInteractionType", "componentInstance", "interactionType", "result", "MAT_DIALOG_DATA", "MAT_DIALOG_DEFAULT_OPTIONS", "MAT_DIALOG_SCROLL_STRATEGY", "providedIn", "factory", "overlay", "scrollStrategies", "block", "MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY", "MAT_DIALOG_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "MatDialog", "openDialogs", "_parentDialog", "_openDialogsAtThisLevel", "_afterOpenedAtThisLevel", "_getAfterAllClosed", "parent", "_afterAllClosedAtThisLevel", "skipSelf", "undefined", "_dialogRefConstructor", "_dialogContainerType", "_dialogDataToken", "open", "componentOrTemplateRef", "dialogRef", "_objectSpread", "_defaultOptions", "_idGenerator", "getId", "scrollStrategy", "_scrollStrategy", "cdkRef", "_dialog", "_overlay", "global", "closeOnDestroy", "closeOnOverlayDetachments", "container", "providers", "dialogConfigClass", "useValue", "templateContext", "cdkConfig", "dialogContainer", "data", "componentRef", "push", "index", "indexOf", "splice", "closeAll", "_closeDialogs", "getDialogById", "find", "dialog", "dialogs", "i", "_MatDialog", "_MatDialog_Factory", "ɵɵdefineInjectable", "token", "ɵfac", "MatDialogClose", "ngOnInit", "getClosestDialog", "ngOnChanges", "changes", "proxied<PERSON><PERSON>e", "currentValue", "_onButtonClick", "screenX", "screenY", "_MatDialogClose", "_MatDialogClose_Factory", "ɵɵdefineDirective", "_MatDialogClose_HostBindings", "ɵɵlistener", "_MatDialogClose_click_HostBindingHandler", "$event", "inputs", "_matDialogClose", "exportAs", "ɵɵNgOnChangesFeature", "MatDialogLayoutSection", "_dialogRef", "_onAdd", "_this$_dialogRef", "instance", "_onRemove", "_MatDialogLayoutSection", "_MatDialogLayoutSection_Factory", "MatDialogTitle", "_this$_dialogRef$_con", "_this$_dialogRef$_con2", "_addAriaLabelledBy", "call", "_this$_dialogRef2", "_this$_dialogRef2$_re", "_removeAriaLabelledBy", "_MatDialogTitle", "ɵ_MatDialogTitle_BaseFactory", "_MatDialogTitle_Factory", "_MatDialogTitle_HostBindings", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_Mat<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "_MatDialogContent_Factory", "ɵɵHostDirectivesFeature", "hostDirectives", "MatDialogActions", "_this$_dialogRef$_con3", "_this$_dialogRef$_con4", "_this$_dialogRef$_con5", "_this$_dialogRef$_con6", "_MatDialogActions", "ɵ_MatDialogActions_BaseFactory", "_MatDialogActions_Factory", "_MatDialogActions_HostBindings", "align", "element", "parentElement", "contains", "DIRECTIVES", "MatDialogModule", "_MatDialogModule", "_MatDialogModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "_", "a", "b", "c", "d", "e", "f", "g", "h", "j", "k", "l", "m", "n"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/module-BnDTus5c.mjs"], "sourcesContent": ["import { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { inject, ANIMATION_MODULE_TYPE, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, InjectionToken, Injectable, ElementRef, Directive, Input, NgModule } from '@angular/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable } from '@angular/cdk/scrolling';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nclass MatDialogConfig {\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This affects what is available for injection and the change detection order for the\n     * component instantiated inside of the dialog. This does not affect where the dialog\n     * content will be rendered.\n     */\n    viewContainerRef;\n    /**\n     * Injector used for the instantiation of the component to be attached. If provided,\n     * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n     */\n    injector;\n    /** ID for the dialog. If omitted, a unique one will be generated. */\n    id;\n    /** The ARIA role of the dialog element. */\n    role = 'dialog';\n    /** Custom class for the overlay pane. */\n    panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    hasBackdrop = true;\n    /** Custom class for the backdrop. */\n    backdropClass = '';\n    /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n    disableClose = false;\n    /** Width of the dialog. */\n    width = '';\n    /** Height of the dialog. */\n    height = '';\n    /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n    minWidth;\n    /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n    minHeight;\n    /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n    maxWidth;\n    /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n    maxHeight;\n    /** Position overrides. */\n    position;\n    /** Data being injected into the child component. */\n    data = null;\n    /** Layout direction for the dialog's content. */\n    direction;\n    /** ID of the element that describes the dialog. */\n    ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    ariaLabelledBy = null;\n    /** Aria label to assign to the dialog element. */\n    ariaLabel = null;\n    /**\n     * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n     * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n     * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n     */\n    ariaModal = false;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the\n     * previously-focused element, after it's closed.\n     */\n    restoreFocus = true;\n    /** Whether to wait for the opening animation to finish before trapping focus. */\n    delayFocusTrap = true;\n    /** Scroll strategy to be used for the dialog. */\n    scrollStrategy;\n    /**\n     * Whether the dialog should close when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    closeOnNavigation = true;\n    /**\n     * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n     * @deprecated No longer used. Will be removed.\n     * @breaking-change 20.0.0\n     */\n    componentFactoryResolver;\n    /**\n     * Duration of the enter animation in ms.\n     * Should be a number, string type is deprecated.\n     * @breaking-change 17.0.0 Remove string signature.\n     */\n    enterAnimationDuration;\n    /**\n     * Duration of the exit animation in ms.\n     * Should be a number, string type is deprecated.\n     * @breaking-change 17.0.0 Remove string signature.\n     */\n    exitAnimationDuration;\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\nclass MatDialogContainer extends CdkDialogContainer {\n    _animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n    /** Emits when an animation state changes. */\n    _animationStateChanged = new EventEmitter();\n    /** Whether animations are enabled. */\n    _animationsEnabled = this._animationMode !== 'NoopAnimations';\n    /** Number of actions projected in the dialog. */\n    _actionSectionCount = 0;\n    /** Host element of the dialog container component. */\n    _hostElement = this._elementRef.nativeElement;\n    /** Duration of the dialog open animation. */\n    _enterAnimationDuration = this._animationsEnabled\n        ? parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION\n        : 0;\n    /** Duration of the dialog close animation. */\n    _exitAnimationDuration = this._animationsEnabled\n        ? parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION\n        : 0;\n    /** Current timer for dialog animations. */\n    _animationTimer = null;\n    _contentAttached() {\n        // Delegate to the original dialog-container initialization (i.e. saving the\n        // previous element, setting up the focus trap and moving focus to the container).\n        super._contentAttached();\n        // Note: Usually we would be able to use the MDC dialog foundation here to handle\n        // the dialog animation for us, but there are a few reasons why we just leverage\n        // their styles and not use the runtime foundation code:\n        //   1. Foundation does not allow us to disable animations.\n        //   2. Foundation contains unnecessary features we don't need and aren't\n        //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n        this._startOpenAnimation();\n    }\n    /** Starts the dialog open animation if enabled. */\n    _startOpenAnimation() {\n        this._animationStateChanged.emit({ state: 'opening', totalTime: this._enterAnimationDuration });\n        if (this._animationsEnabled) {\n            this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n            // We need to give the `setProperty` call from above some time to be applied.\n            // One would expect that the open class is added once the animation finished, but MDC\n            // uses the open class in combination with the opening class to start the animation.\n            this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n            this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n        }\n        else {\n            this._hostElement.classList.add(OPEN_CLASS);\n            // Note: We could immediately finish the dialog opening here with noop animations,\n            // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n            // Executing this immediately would mean that `afterOpened` emits synchronously\n            // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n            Promise.resolve().then(() => this._finishDialogOpen());\n        }\n    }\n    /**\n     * Starts the exit animation of the dialog if enabled. This method is\n     * called by the dialog ref.\n     */\n    _startExitAnimation() {\n        this._animationStateChanged.emit({ state: 'closing', totalTime: this._exitAnimationDuration });\n        this._hostElement.classList.remove(OPEN_CLASS);\n        if (this._animationsEnabled) {\n            this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n            // We need to give the `setProperty` call from above some time to be applied.\n            this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n            this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n        }\n        else {\n            // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n            // set up before any user can subscribe to the backdrop click. The subscription triggers\n            // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n            // animation state event if animations are disabled, the overlay would be disposed\n            // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n            // skipped. We work around this by waiting with the dialog close until the next tick when\n            // all subscriptions have been fired as expected. This is not an ideal solution, but\n            // there doesn't seem to be any other good way. Alternatives that have been considered:\n            //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n            //      Also this issue is specific to the MDC implementation where the dialog could\n            //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n            //      and closing always takes at least a tick.\n            //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n            //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n            //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n            // Based on the fact that this is specific to the MDC-based implementation of the dialog\n            // animations, the defer is applied here.\n            Promise.resolve().then(() => this._finishDialogClose());\n        }\n    }\n    /**\n     * Updates the number action sections.\n     * @param delta Increase/decrease in the number of sections.\n     */\n    _updateActionSectionCount(delta) {\n        this._actionSectionCount += delta;\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Completes the dialog open by clearing potential animation classes, trapping\n     * focus and emitting an opened event.\n     */\n    _finishDialogOpen = () => {\n        this._clearAnimationClasses();\n        this._openAnimationDone(this._enterAnimationDuration);\n    };\n    /**\n     * Completes the dialog close by clearing potential animation classes, restoring\n     * focus and emitting a closed event.\n     */\n    _finishDialogClose = () => {\n        this._clearAnimationClasses();\n        this._animationStateChanged.emit({ state: 'closed', totalTime: this._exitAnimationDuration });\n    };\n    /** Clears all dialog animation classes. */\n    _clearAnimationClasses() {\n        this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n    }\n    _waitForAnimationToComplete(duration, callback) {\n        if (this._animationTimer !== null) {\n            clearTimeout(this._animationTimer);\n        }\n        // Note that we want this timer to run inside the NgZone, because we want\n        // the related events like `afterClosed` to be inside the zone as well.\n        this._animationTimer = setTimeout(callback, duration);\n    }\n    /** Runs a callback in `requestAnimationFrame`, if available. */\n    _requestAnimationFrame(callback) {\n        this._ngZone.runOutsideAngular(() => {\n            if (typeof requestAnimationFrame === 'function') {\n                requestAnimationFrame(callback);\n            }\n            else {\n                callback();\n            }\n        });\n    }\n    _captureInitialFocus() {\n        if (!this._config.delayFocusTrap) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Callback for when the open dialog animation has finished. Intended to\n     * be called by sub-classes that use different animation implementations.\n     */\n    _openAnimationDone(totalTime) {\n        if (this._config.delayFocusTrap) {\n            this._trapFocus();\n        }\n        this._animationStateChanged.next({ state: 'opened', totalTime });\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this._animationTimer !== null) {\n            clearTimeout(this._animationTimer);\n        }\n    }\n    attachComponentPortal(portal) {\n        // When a component is passed into the dialog, the host element interrupts\n        // the `display:flex` from affecting the dialog title, content, and\n        // actions. To fix this, we make the component host `display: contents` by\n        // marking its host with the `mat-mdc-dialog-component-host` class.\n        //\n        // Note that this problem does not exist when a template ref is used since\n        // the title, contents, and actions are then nested directly under the\n        // dialog surface.\n        const ref = super.attachComponentPortal(portal);\n        ref.location.nativeElement.classList.add('mat-mdc-dialog-component-host');\n        return ref;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogContainer, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatDialogContainer, isStandalone: true, selector: \"mat-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.aria-modal\": \"_config.ariaModal\", \"id\": \"_config.id\", \"attr.role\": \"_config.role\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledByQueue[0]\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\", \"class._mat-animation-noopable\": \"!_animationsEnabled\", \"class.mat-mdc-dialog-container-with-actions\": \"_actionSectionCount > 0\" }, classAttribute: \"mat-mdc-dialog-container mdc-dialog\" }, usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-mdc-dialog-inner-container mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet />\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mdc-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mdc-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mdc-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mdc-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mdc-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mdc-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mdc-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mdc-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mdc-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mdc-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mdc-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mdc-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mdc-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mdc-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, imports: [CdkPortalOutlet], host: {\n                        'class': 'mat-mdc-dialog-container mdc-dialog',\n                        'tabindex': '-1',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[id]': '_config.id',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                        '[class._mat-animation-noopable]': '!_animationsEnabled',\n                        '[class.mat-mdc-dialog-container-with-actions]': '_actionSectionCount > 0',\n                    }, template: \"<div class=\\\"mat-mdc-dialog-inner-container mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet />\\n  </div>\\n</div>\\n\", styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mdc-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mdc-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mdc-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mdc-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mdc-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mdc-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mdc-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mdc-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mdc-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mdc-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mdc-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mdc-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mdc-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mdc-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\\n\"] }]\n        }] });\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n    if (time == null) {\n        return null;\n    }\n    if (typeof time === 'number') {\n        return time;\n    }\n    if (time.endsWith('ms')) {\n        return coerceNumberProperty(time.substring(0, time.length - 2));\n    }\n    if (time.endsWith('s')) {\n        return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n    }\n    if (time === '0') {\n        return 0;\n    }\n    return null; // anything else is invalid.\n}\n\nvar MatDialogState;\n(function (MatDialogState) {\n    MatDialogState[MatDialogState[\"OPEN\"] = 0] = \"OPEN\";\n    MatDialogState[MatDialogState[\"CLOSING\"] = 1] = \"CLOSING\";\n    MatDialogState[MatDialogState[\"CLOSED\"] = 2] = \"CLOSED\";\n})(MatDialogState || (MatDialogState = {}));\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n    _ref;\n    _containerInstance;\n    /** The instance of component opened into the dialog. */\n    componentInstance;\n    /**\n     * `ComponentRef` of the component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    componentRef;\n    /** Whether the user is allowed to close the dialog. */\n    disableClose;\n    /** Unique ID for the dialog. */\n    id;\n    /** Subject for notifying the user that the dialog has finished opening. */\n    _afterOpened = new Subject();\n    /** Subject for notifying the user that the dialog has started closing. */\n    _beforeClosed = new Subject();\n    /** Result to be passed to afterClosed. */\n    _result;\n    /** Handle to the timeout that's running as a fallback in case the exit animation doesn't fire. */\n    _closeFallbackTimeout;\n    /** Current state of the dialog. */\n    _state = MatDialogState.OPEN;\n    // TODO(crisbeto): we shouldn't have to declare this property, because `DialogRef.close`\n    // already has a second `options` parameter that we can use. The problem is that internal tests\n    // have assertions like `expect(MatDialogRef.close).toHaveBeenCalledWith(foo)` which will break,\n    // because it'll be called with two arguments by things like `MatDialogClose`.\n    /** Interaction that caused the dialog to close. */\n    _closeInteractionType;\n    constructor(_ref, config, _containerInstance) {\n        this._ref = _ref;\n        this._containerInstance = _containerInstance;\n        this.disableClose = config.disableClose;\n        this.id = _ref.id;\n        // Used to target panels specifically tied to dialogs.\n        _ref.addPanelClass('mat-mdc-dialog-panel');\n        // Emit when opening animation completes\n        _containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'opened'), take(1))\n            .subscribe(() => {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        });\n        // Dispose overlay when closing animation is complete\n        _containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'closed'), take(1))\n            .subscribe(() => {\n            clearTimeout(this._closeFallbackTimeout);\n            this._finishDialogClose();\n        });\n        _ref.overlayRef.detachments().subscribe(() => {\n            this._beforeClosed.next(this._result);\n            this._beforeClosed.complete();\n            this._finishDialogClose();\n        });\n        merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n            if (!this.disableClose) {\n                event.preventDefault();\n                _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param dialogResult Optional result to return to the dialog opener.\n     */\n    close(dialogResult) {\n        this._result = dialogResult;\n        // Transition the backdrop in parallel to the dialog.\n        this._containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'closing'), take(1))\n            .subscribe(event => {\n            this._beforeClosed.next(dialogResult);\n            this._beforeClosed.complete();\n            this._ref.overlayRef.detachBackdrop();\n            // The logic that disposes of the overlay depends on the exit animation completing, however\n            // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n            // timeout which will clean everything up if the animation hasn't fired within the specified\n            // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n            // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n            this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n        });\n        this._state = MatDialogState.CLOSING;\n        this._containerInstance._startExitAnimation();\n    }\n    /**\n     * Gets an observable that is notified when the dialog is finished opening.\n     */\n    afterOpened() {\n        return this._afterOpened;\n    }\n    /**\n     * Gets an observable that is notified when the dialog is finished closing.\n     */\n    afterClosed() {\n        return this._ref.closed;\n    }\n    /**\n     * Gets an observable that is notified when the dialog has started closing.\n     */\n    beforeClosed() {\n        return this._beforeClosed;\n    }\n    /**\n     * Gets an observable that emits when the overlay's backdrop has been clicked.\n     */\n    backdropClick() {\n        return this._ref.backdropClick;\n    }\n    /**\n     * Gets an observable that emits when keydown events are targeted on the overlay.\n     */\n    keydownEvents() {\n        return this._ref.keydownEvents;\n    }\n    /**\n     * Updates the dialog's position.\n     * @param position New dialog position.\n     */\n    updatePosition(position) {\n        let strategy = this._ref.config.positionStrategy;\n        if (position && (position.left || position.right)) {\n            position.left ? strategy.left(position.left) : strategy.right(position.right);\n        }\n        else {\n            strategy.centerHorizontally();\n        }\n        if (position && (position.top || position.bottom)) {\n            position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n        }\n        else {\n            strategy.centerVertically();\n        }\n        this._ref.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this._ref.updateSize(width, height);\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this._ref.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this._ref.removePanelClass(classes);\n        return this;\n    }\n    /** Gets the current state of the dialog's lifecycle. */\n    getState() {\n        return this._state;\n    }\n    /**\n     * Finishes the dialog close by updating the state of the dialog\n     * and disposing the overlay.\n     */\n    _finishDialogClose() {\n        this._state = MatDialogState.CLOSED;\n        this._ref.close(this._result, { focusOrigin: this._closeInteractionType });\n        this.componentInstance = null;\n    }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n    ref._closeInteractionType = interactionType;\n    return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = new InjectionToken('mat-mdc-dialog-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.block();\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst MAT_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n    provide: MAT_DIALOG_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n/**\n * Service to open Material Design modal dialogs.\n */\nclass MatDialog {\n    _overlay = inject(Overlay);\n    _defaultOptions = inject(MAT_DIALOG_DEFAULT_OPTIONS, { optional: true });\n    _scrollStrategy = inject(MAT_DIALOG_SCROLL_STRATEGY);\n    _parentDialog = inject(MatDialog, { optional: true, skipSelf: true });\n    _idGenerator = inject(_IdGenerator);\n    _dialog = inject(Dialog);\n    _openDialogsAtThisLevel = [];\n    _afterAllClosedAtThisLevel = new Subject();\n    _afterOpenedAtThisLevel = new Subject();\n    dialogConfigClass = MatDialogConfig;\n    _dialogRefConstructor;\n    _dialogContainerType;\n    _dialogDataToken;\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    afterAllClosed = defer(() => this.openDialogs.length\n        ? this._getAfterAllClosed()\n        : this._getAfterAllClosed().pipe(startWith(undefined)));\n    constructor() {\n        this._dialogRefConstructor = MatDialogRef;\n        this._dialogContainerType = MatDialogContainer;\n        this._dialogDataToken = MAT_DIALOG_DATA;\n    }\n    open(componentOrTemplateRef, config) {\n        let dialogRef;\n        config = { ...(this._defaultOptions || new MatDialogConfig()), ...config };\n        config.id = config.id || this._idGenerator.getId('mat-mdc-dialog-');\n        config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n        const cdkRef = this._dialog.open(componentOrTemplateRef, {\n            ...config,\n            positionStrategy: this._overlay.position().global().centerHorizontally().centerVertically(),\n            // Disable closing since we need to sync it up to the animation ourselves.\n            disableClose: true,\n            // Disable closing on destroy, because this service cleans up its open dialogs as well.\n            // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n            // the dialogs immediately whereas we want it to wait for the animations to finish.\n            closeOnDestroy: false,\n            // Disable closing on detachments so that we can sync up the animation.\n            // The Material dialog ref handles this manually.\n            closeOnOverlayDetachments: false,\n            container: {\n                type: this._dialogContainerType,\n                providers: () => [\n                    // Provide our config as the CDK config as well since it has the same interface as the\n                    // CDK one, but it contains the actual values passed in by the user for things like\n                    // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n                    { provide: this.dialogConfigClass, useValue: config },\n                    { provide: DialogConfig, useValue: config },\n                ],\n            },\n            templateContext: () => ({ dialogRef }),\n            providers: (ref, cdkConfig, dialogContainer) => {\n                dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n                dialogRef.updatePosition(config?.position);\n                return [\n                    { provide: this._dialogContainerType, useValue: dialogContainer },\n                    { provide: this._dialogDataToken, useValue: cdkConfig.data },\n                    { provide: this._dialogRefConstructor, useValue: dialogRef },\n                ];\n            },\n        });\n        // This can't be assigned in the `providers` callback, because\n        // the instance hasn't been assigned to the CDK ref yet.\n        dialogRef.componentRef = cdkRef.componentRef;\n        dialogRef.componentInstance = cdkRef.componentInstance;\n        this.openDialogs.push(dialogRef);\n        this.afterOpened.next(dialogRef);\n        dialogRef.afterClosed().subscribe(() => {\n            const index = this.openDialogs.indexOf(dialogRef);\n            if (index > -1) {\n                this.openDialogs.splice(index, 1);\n                if (!this.openDialogs.length) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        });\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        this._closeDialogs(this.openDialogs);\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Only close the dialogs at this level on destroy\n        // since the parent service may still be active.\n        this._closeDialogs(this._openDialogsAtThisLevel);\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n    }\n    _closeDialogs(dialogs) {\n        let i = dialogs.length;\n        while (i--) {\n            dialogs[i].close();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialog, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialog, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialog, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Button that will close the current dialog.\n */\nclass MatDialogClose {\n    dialogRef = inject(MatDialogRef, { optional: true });\n    _elementRef = inject(ElementRef);\n    _dialog = inject(MatDialog);\n    /** Screen-reader label for the button. */\n    ariaLabel;\n    /** Default to \"button\" to prevents accidental form submits. */\n    type = 'button';\n    /** Dialog close input. */\n    dialogResult;\n    _matDialogClose;\n    constructor() { }\n    ngOnInit() {\n        if (!this.dialogRef) {\n            // When this directive is included in a dialog via TemplateRef (rather than being\n            // in a Component), the DialogRef isn't available via injection because embedded\n            // views cannot be given a custom injector. Instead, we look up the DialogRef by\n            // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n            // be resolved at constructor time.\n            this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n        }\n    }\n    ngOnChanges(changes) {\n        const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n        if (proxiedChange) {\n            this.dialogResult = proxiedChange.currentValue;\n        }\n    }\n    _onButtonClick(event) {\n        // Determinate the focus origin using the click event, because using the FocusMonitor will\n        // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n        // dialog, and therefore clicking the button won't result in a focus change. This means that\n        // the FocusMonitor won't detect any origin change, and will always output `program`.\n        _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogClose, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatDialogClose, isStandalone: true, selector: \"[mat-dialog-close], [matDialogClose]\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], type: \"type\", dialogResult: [\"mat-dialog-close\", \"dialogResult\"], _matDialogClose: [\"matDialogClose\", \"_matDialogClose\"] }, host: { listeners: { \"click\": \"_onButtonClick($event)\" }, properties: { \"attr.aria-label\": \"ariaLabel || null\", \"attr.type\": \"type\" } }, exportAs: [\"matDialogClose\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogClose, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-dialog-close], [matDialogClose]',\n                    exportAs: 'matDialogClose',\n                    host: {\n                        '(click)': '_onButtonClick($event)',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.type]': 'type',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], type: [{\n                type: Input\n            }], dialogResult: [{\n                type: Input,\n                args: ['mat-dialog-close']\n            }], _matDialogClose: [{\n                type: Input,\n                args: ['matDialogClose']\n            }] } });\nclass MatDialogLayoutSection {\n    _dialogRef = inject(MatDialogRef, { optional: true });\n    _elementRef = inject(ElementRef);\n    _dialog = inject(MatDialog);\n    constructor() { }\n    ngOnInit() {\n        if (!this._dialogRef) {\n            this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n        }\n        if (this._dialogRef) {\n            Promise.resolve().then(() => {\n                this._onAdd();\n            });\n        }\n    }\n    ngOnDestroy() {\n        // Note: we null check because there are some internal\n        // tests that are mocking out `MatDialogRef` incorrectly.\n        const instance = this._dialogRef?._containerInstance;\n        if (instance) {\n            Promise.resolve().then(() => {\n                this._onRemove();\n            });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogLayoutSection, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatDialogLayoutSection, isStandalone: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogLayoutSection, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [] });\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nclass MatDialogTitle extends MatDialogLayoutSection {\n    id = inject(_IdGenerator).getId('mat-mdc-dialog-title-');\n    _onAdd() {\n        // Note: we null check the queue, because there are some internal\n        // tests that are mocking out `MatDialogRef` incorrectly.\n        this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id);\n    }\n    _onRemove() {\n        this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogTitle, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatDialogTitle, isStandalone: true, selector: \"[mat-dialog-title], [matDialogTitle]\", inputs: { id: \"id\" }, host: { properties: { \"id\": \"id\" }, classAttribute: \"mat-mdc-dialog-title mdc-dialog__title\" }, exportAs: [\"matDialogTitle\"], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-dialog-title], [matDialogTitle]',\n                    exportAs: 'matDialogTitle',\n                    host: {\n                        'class': 'mat-mdc-dialog-title mdc-dialog__title',\n                        '[id]': 'id',\n                    },\n                }]\n        }], propDecorators: { id: [{\n                type: Input\n            }] } });\n/**\n * Scrollable content container of a dialog.\n */\nclass MatDialogContent {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatDialogContent, isStandalone: true, selector: \"[mat-dialog-content], mat-dialog-content, [matDialogContent]\", host: { classAttribute: \"mat-mdc-dialog-content mdc-dialog__content\" }, hostDirectives: [{ directive: i1.CdkScrollable }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n                    host: { 'class': 'mat-mdc-dialog-content mdc-dialog__content' },\n                    hostDirectives: [CdkScrollable],\n                }]\n        }] });\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nclass MatDialogActions extends MatDialogLayoutSection {\n    /**\n     * Horizontal alignment of action buttons.\n     */\n    align;\n    _onAdd() {\n        this._dialogRef._containerInstance?._updateActionSectionCount?.(1);\n    }\n    _onRemove() {\n        this._dialogRef._containerInstance?._updateActionSectionCount?.(-1);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogActions, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatDialogActions, isStandalone: true, selector: \"[mat-dialog-actions], mat-dialog-actions, [matDialogActions]\", inputs: { align: \"align\" }, host: { properties: { \"class.mat-mdc-dialog-actions-align-start\": \"align === \\\"start\\\"\", \"class.mat-mdc-dialog-actions-align-center\": \"align === \\\"center\\\"\", \"class.mat-mdc-dialog-actions-align-end\": \"align === \\\"end\\\"\" }, classAttribute: \"mat-mdc-dialog-actions mdc-dialog__actions\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n                    host: {\n                        'class': 'mat-mdc-dialog-actions mdc-dialog__actions',\n                        '[class.mat-mdc-dialog-actions-align-start]': 'align === \"start\"',\n                        '[class.mat-mdc-dialog-actions-align-center]': 'align === \"center\"',\n                        '[class.mat-mdc-dialog-actions-align-end]': 'align === \"end\"',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }] } });\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n    let parent = element.nativeElement.parentElement;\n    while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n        parent = parent.parentElement;\n    }\n    return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\n\nconst DIRECTIVES = [\n    MatDialogContainer,\n    MatDialogClose,\n    MatDialogTitle,\n    MatDialogActions,\n    MatDialogContent,\n];\nclass MatDialogModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogModule, imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatDialogContainer,\n            MatDialogClose,\n            MatDialogTitle,\n            MatDialogActions,\n            MatDialogContent], exports: [MatCommonModule, MatDialogContainer,\n            MatDialogClose,\n            MatDialogTitle,\n            MatDialogActions,\n            MatDialogContent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogModule, providers: [MatDialog], imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatDialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, ...DIRECTIVES],\n                    exports: [MatCommonModule, ...DIRECTIVES],\n                    providers: [MatDialog],\n                }]\n        }] });\n\nexport { MatDialogActions as M, _closeDialogVia as _, MatDialogClose as a, MatDialogTitle as b, MatDialogContent as c, MatDialogContainer as d, MAT_DIALOG_DATA as e, MAT_DIALOG_DEFAULT_OPTIONS as f, MAT_DIALOG_SCROLL_STRATEGY as g, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY as h, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER as i, MatDialog as j, MatDialogConfig as k, MatDialogState as l, MatDialogRef as m, MatDialogModule as n };\n//# sourceMappingURL=module-BnDTus5c.mjs.map\n"], "mappings": ";;;;AAAA,SAASA,kBAAkB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,YAAY,QAAQ,qBAAqB;AAC5F,SAASC,OAAO,EAAEC,aAAa,QAAQ,sBAAsB;AAC7D,SAASC,eAAe,EAAEC,YAAY,QAAQ,qBAAqB;AACnE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,cAAc,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACtM,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,MAAM,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AACxD,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;;AAEnE;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAAAC,YAAA;IAClB;AACJ;AACA;AACA;AACA;AACA;IALIC,eAAA;IAOA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA,eACO,QAAQ;IACf;IAAAA,eAAA,qBACa,EAAE;IACf;IAAAA,eAAA,sBACc,IAAI;IAClB;IAAAA,eAAA,wBACgB,EAAE;IAClB;IAAAA,eAAA,uBACe,KAAK;IACpB;IAAAA,eAAA,gBACQ,EAAE;IACV;IAAAA,eAAA,iBACS,EAAE;IACX;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,eACO,IAAI;IACX;IAAAA,eAAA;IAEA;IAAAA,eAAA,0BACkB,IAAI;IACtB;IAAAA,eAAA,yBACiB,IAAI;IACrB;IAAAA,eAAA,oBACY,IAAI;IAChB;AACJ;AACA;AACA;AACA;IAJIA,eAAA,oBAKY,KAAK;IACjB;AACJ;AACA;AACA;AACA;IAJIA,eAAA,oBAKY,gBAAgB;IAC5B;AACJ;AACA;AACA;IAHIA,eAAA,uBAIe,IAAI;IACnB;IAAAA,eAAA,yBACiB,IAAI;IACrB;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;AACA;IAJIA,eAAA,4BAKoB,IAAI;IACxB;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;EAAA;AAMJ;;AAEA;AACA,MAAMC,UAAU,GAAG,kBAAkB;AACrC;AACA,MAAMC,aAAa,GAAG,qBAAqB;AAC3C;AACA,MAAMC,aAAa,GAAG,qBAAqB;AAC3C;AACA,MAAMC,uBAAuB,GAAG,GAAG;AACnC;AACA,MAAMC,wBAAwB,GAAG,EAAE;AACnC,MAAMC,kBAAkB,SAAS3C,kBAAkB,CAAC;EAAAoC,YAAA,GAAAQ,IAAA;IAAA,IAAAC,aAAA,EAAAC,cAAA;IAAA,SAAAF,IAAA;IAAAP,eAAA,yBAC/B5B,MAAM,CAACC,qBAAqB,EAAE;MAAEqC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAClE;IAAAV,eAAA,iCACyB,IAAI1B,YAAY,CAAC,CAAC;IAC3C;IAAA0B,eAAA,6BACqB,IAAI,CAACW,cAAc,KAAK,gBAAgB;IAC7D;IAAAX,eAAA,8BACsB,CAAC;IACvB;IAAAA,eAAA,uBACe,IAAI,CAACY,WAAW,CAACC,aAAa;IAC7C;IAAAb,eAAA,kCAC0B,IAAI,CAACc,kBAAkB,IAAAN,aAAA,GAC3CO,YAAY,CAAC,IAAI,CAACC,OAAO,CAACC,sBAAsB,CAAC,cAAAT,aAAA,cAAAA,aAAA,GAAIJ,uBAAuB,GAC5E,CAAC;IACP;IAAAJ,eAAA,iCACyB,IAAI,CAACc,kBAAkB,IAAAL,cAAA,GAC1CM,YAAY,CAAC,IAAI,CAACC,OAAO,CAACE,qBAAqB,CAAC,cAAAT,cAAA,cAAAA,cAAA,GAAIJ,wBAAwB,GAC5E,CAAC;IACP;IAAAL,eAAA,0BACkB,IAAI;IA2EtB;AACJ;AACA;AACA;IAHIA,eAAA,4BAIoB,MAAM;MACtB,IAAI,CAACmB,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACC,uBAAuB,CAAC;IACzD,CAAC;IACD;AACJ;AACA;AACA;IAHIrB,eAAA,6BAIqB,MAAM;MACvB,IAAI,CAACmB,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACG,sBAAsB,CAACC,IAAI,CAAC;QAAEC,KAAK,EAAE,QAAQ;QAAEC,SAAS,EAAE,IAAI,CAACC;MAAuB,CAAC,CAAC;IACjG,CAAC;EAAA;EAzFDC,gBAAgBA,CAAA,EAAG;IACf;IACA;IACA,KAAK,CAACA,gBAAgB,CAAC,CAAC;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACAA,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACN,sBAAsB,CAACC,IAAI,CAAC;MAAEC,KAAK,EAAE,SAAS;MAAEC,SAAS,EAAE,IAAI,CAACJ;IAAwB,CAAC,CAAC;IAC/F,IAAI,IAAI,CAACP,kBAAkB,EAAE;MACzB,IAAI,CAACe,YAAY,CAACC,KAAK,CAACC,WAAW,CAACC,4BAA4B,EAAE,GAAG,IAAI,CAACX,uBAAuB,IAAI,CAAC;MACtG;MACA;MACA;MACA,IAAI,CAACY,sBAAsB,CAAC,MAAM,IAAI,CAACJ,YAAY,CAACK,SAAS,CAACC,GAAG,CAACjC,aAAa,EAAED,UAAU,CAAC,CAAC;MAC7F,IAAI,CAACmC,2BAA2B,CAAC,IAAI,CAACf,uBAAuB,EAAE,IAAI,CAACgB,iBAAiB,CAAC;IAC1F,CAAC,MACI;MACD,IAAI,CAACR,YAAY,CAACK,SAAS,CAACC,GAAG,CAAClC,UAAU,CAAC;MAC3C;MACA;MACA;MACA;MACAqC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACH,iBAAiB,CAAC,CAAC,CAAC;IAC1D;EACJ;EACA;AACJ;AACA;AACA;EACII,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACnB,sBAAsB,CAACC,IAAI,CAAC;MAAEC,KAAK,EAAE,SAAS;MAAEC,SAAS,EAAE,IAAI,CAACC;IAAuB,CAAC,CAAC;IAC9F,IAAI,CAACG,YAAY,CAACK,SAAS,CAACQ,MAAM,CAACzC,UAAU,CAAC;IAC9C,IAAI,IAAI,CAACa,kBAAkB,EAAE;MACzB,IAAI,CAACe,YAAY,CAACC,KAAK,CAACC,WAAW,CAACC,4BAA4B,EAAE,GAAG,IAAI,CAACN,sBAAsB,IAAI,CAAC;MACrG;MACA,IAAI,CAACO,sBAAsB,CAAC,MAAM,IAAI,CAACJ,YAAY,CAACK,SAAS,CAACC,GAAG,CAAChC,aAAa,CAAC,CAAC;MACjF,IAAI,CAACiC,2BAA2B,CAAC,IAAI,CAACV,sBAAsB,EAAE,IAAI,CAACiB,kBAAkB,CAAC;IAC1F,CAAC,MACI;MACD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACG,kBAAkB,CAAC,CAAC,CAAC;IAC3D;EACJ;EACA;AACJ;AACA;AACA;EACIC,yBAAyBA,CAACC,KAAK,EAAE;IAC7B,IAAI,CAACC,mBAAmB,IAAID,KAAK;IACjC,IAAI,CAACE,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EAiBA;EACA7B,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACU,YAAY,CAACK,SAAS,CAACQ,MAAM,CAACxC,aAAa,EAAEC,aAAa,CAAC;EACpE;EACAiC,2BAA2BA,CAACa,QAAQ,EAAEC,QAAQ,EAAE;IAC5C,IAAI,IAAI,CAACC,eAAe,KAAK,IAAI,EAAE;MAC/BC,YAAY,CAAC,IAAI,CAACD,eAAe,CAAC;IACtC;IACA;IACA;IACA,IAAI,CAACA,eAAe,GAAGE,UAAU,CAACH,QAAQ,EAAED,QAAQ,CAAC;EACzD;EACA;EACAhB,sBAAsBA,CAACiB,QAAQ,EAAE;IAC7B,IAAI,CAACI,OAAO,CAACC,iBAAiB,CAAC,MAAM;MACjC,IAAI,OAAOC,qBAAqB,KAAK,UAAU,EAAE;QAC7CA,qBAAqB,CAACN,QAAQ,CAAC;MACnC,CAAC,MACI;QACDA,QAAQ,CAAC,CAAC;MACd;IACJ,CAAC,CAAC;EACN;EACAO,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACzC,OAAO,CAAC0C,cAAc,EAAE;MAC9B,IAAI,CAACC,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;EACIvC,kBAAkBA,CAACK,SAAS,EAAE;IAC1B,IAAI,IAAI,CAACT,OAAO,CAAC0C,cAAc,EAAE;MAC7B,IAAI,CAACC,UAAU,CAAC,CAAC;IACrB;IACA,IAAI,CAACrC,sBAAsB,CAACsC,IAAI,CAAC;MAAEpC,KAAK,EAAE,QAAQ;MAAEC;IAAU,CAAC,CAAC;EACpE;EACAoC,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,IAAI,CAACV,eAAe,KAAK,IAAI,EAAE;MAC/BC,YAAY,CAAC,IAAI,CAACD,eAAe,CAAC;IACtC;EACJ;EACAW,qBAAqBA,CAACC,MAAM,EAAE;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,GAAG,GAAG,KAAK,CAACF,qBAAqB,CAACC,MAAM,CAAC;IAC/CC,GAAG,CAACC,QAAQ,CAACpD,aAAa,CAACqB,SAAS,CAACC,GAAG,CAAC,+BAA+B,CAAC;IACzE,OAAO6B,GAAG;EACd;AAGJ;AAACE,mBAAA,GAzKK5D,kBAAkB;AAAAN,eAAA,CAAlBM,kBAAkB;EAAA,IAAA6D,gCAAA;EAAA,gBAAAC,4BAAAC,iBAAA;IAAA,QAAAF,gCAAA,KAAAA,gCAAA,GA0KyDhG,EAAE,CAAAmG,qBAAA,CAHoBhE,mBAAkB,IAAA+D,iBAAA,IAAlB/D,mBAAkB;EAAA;AAAA;AAAAN,eAAA,CAvKnHM,kBAAkB,8BA0KyDnC,EAAE,CAAAoG,iBAAA;EAAAC,IAAA,EAFQlE,mBAAkB;EAAAmE,SAAA;EAAAC,SAAA,eAA0F,IAAI;EAAAC,QAAA;EAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAE1H3G,EAAE,CAAA6G,cAAA,OAAAD,GAAA,CAAA/D,OAAA,CAAAiE,EAFyB,CAAC;MAE5B9G,EAAE,CAAA+G,WAAA,eAAAH,GAAA,CAAA/D,OAAA,CAAAmE,SAAA,UAAAJ,GAAA,CAAA/D,OAAA,CAAAoE,IAAA,qBAAAL,GAAA,CAAA/D,OAAA,CAAAqE,SAAA,GAF4B,IAAI,GAAAN,GAAA,CAAAO,oBAAA,CAAwB,CAAC,iBAAAP,GAAA,CAAA/D,OAAA,CAAAqE,SAAA,sBAAAN,GAAA,CAAA/D,OAAA,CAAAuE,eAAA,IAAtB,IAAI;MAEzCpH,EAAE,CAAAqH,WAAA,6BAAAT,GAAA,CAAAjE,kBAFyB,CAAC,0CAAAiE,GAAA,CAAAjC,mBAAA,GAAI,CAAL,CAAC;IAAA;EAAA;EAAA2C,QAAA,GAE5BtH,EAAE,CAAAuH,0BAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,6BAAAjB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF3G,EAAE,CAAA6H,cAAA,YAFqsB,CAAC,YAA6D,CAAC;MAEtwB7H,EAAE,CAAA8H,UAAA,IAAAC,0CAAA,wBAFwyB,CAAC;MAE3yB/H,EAAE,CAAAgI,YAAA,CAFkzB,CAAC,CAAO,CAAC;IAAA;EAAA;EAAAC,YAAA,GAAwpKnI,eAAe;EAAAoI,MAAA;EAAAC,aAAA;AAAA;AAErjM;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFpI,EAAE,CAAAqI,iBAAA,CAAQlG,kBAAkB,EAAc,CAAC;IAChHkE,IAAI,EAAEjG,SAAS;IACfgC,IAAI,EAAE,CAAC;MAAEkG,QAAQ,EAAE,sBAAsB;MAAEH,aAAa,EAAE9H,iBAAiB,CAACkI,IAAI;MAAEC,eAAe,EAAElI,uBAAuB,CAACmI,OAAO;MAAEC,OAAO,EAAE,CAAC5I,eAAe,CAAC;MAAE6I,IAAI,EAAE;QAC1J,OAAO,EAAE,qCAAqC;QAC9C,UAAU,EAAE,IAAI;QAChB,mBAAmB,EAAE,mBAAmB;QACxC,MAAM,EAAE,YAAY;QACpB,aAAa,EAAE,cAAc;QAC7B,wBAAwB,EAAE,oDAAoD;QAC9E,mBAAmB,EAAE,mBAAmB;QACxC,yBAAyB,EAAE,iCAAiC;QAC5D,iCAAiC,EAAE,qBAAqB;QACxD,+CAA+C,EAAE;MACrD,CAAC;MAAEhB,QAAQ,EAAE,6LAA6L;MAAEO,MAAM,EAAE,CAAC,6lKAA6lK;IAAE,CAAC;EACj0K,CAAC,CAAC;AAAA;AACV,MAAMrE,4BAA4B,GAAG,kCAAkC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,SAASjB,YAAYA,CAACgG,IAAI,EAAE;EACxB,IAAIA,IAAI,IAAI,IAAI,EAAE;IACd,OAAO,IAAI;EACf;EACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI;EACf;EACA,IAAIA,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;IACrB,OAAOhI,oBAAoB,CAAC+H,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEF,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC;EACnE;EACA,IAAIH,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACpB,OAAOhI,oBAAoB,CAAC+H,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEF,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;EAC1E;EACA,IAAIH,IAAI,KAAK,GAAG,EAAE;IACd,OAAO,CAAC;EACZ;EACA,OAAO,IAAI,CAAC,CAAC;AACjB;AAEA,IAAII,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAACA,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACnDA,cAAc,CAACA,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACzDA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AAC3D,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EA8BfrH,WAAWA,CAACsH,IAAI,EAAEC,MAAM,EAAEC,kBAAkB,EAAE;IAAAvH,eAAA;IAAAA,eAAA;IA3B9C;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,uBACe,IAAIf,OAAO,CAAC,CAAC;IAC5B;IAAAe,eAAA,wBACgB,IAAIf,OAAO,CAAC,CAAC;IAC7B;IAAAe,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,iBACSmH,cAAc,CAACK,IAAI;IAC5B;IACA;IACA;IACA;IACA;IAAAxH,eAAA;IAGI,IAAI,CAACqH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACE,YAAY,GAAGH,MAAM,CAACG,YAAY;IACvC,IAAI,CAACxC,EAAE,GAAGoC,IAAI,CAACpC,EAAE;IACjB;IACAoC,IAAI,CAACK,aAAa,CAAC,sBAAsB,CAAC;IAC1C;IACAH,kBAAkB,CAACjG,sBAAsB,CACpCqG,IAAI,CAACvI,MAAM,CAACwI,KAAK,IAAIA,KAAK,CAACpG,KAAK,KAAK,QAAQ,CAAC,EAAEnC,IAAI,CAAC,CAAC,CAAC,CAAC,CACxDwI,SAAS,CAAC,MAAM;MACjB,IAAI,CAACC,YAAY,CAAClE,IAAI,CAAC,CAAC;MACxB,IAAI,CAACkE,YAAY,CAACC,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC;IACF;IACAR,kBAAkB,CAACjG,sBAAsB,CACpCqG,IAAI,CAACvI,MAAM,CAACwI,KAAK,IAAIA,KAAK,CAACpG,KAAK,KAAK,QAAQ,CAAC,EAAEnC,IAAI,CAAC,CAAC,CAAC,CAAC,CACxDwI,SAAS,CAAC,MAAM;MACjBzE,YAAY,CAAC,IAAI,CAAC4E,qBAAqB,CAAC;MACxC,IAAI,CAACrF,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;IACF0E,IAAI,CAACY,UAAU,CAACC,WAAW,CAAC,CAAC,CAACL,SAAS,CAAC,MAAM;MAC1C,IAAI,CAACM,aAAa,CAACvE,IAAI,CAAC,IAAI,CAACwE,OAAO,CAAC;MACrC,IAAI,CAACD,aAAa,CAACJ,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACpF,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;IACFzD,KAAK,CAAC,IAAI,CAACmJ,aAAa,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAACX,IAAI,CAACvI,MAAM,CAACwI,KAAK,IAAIA,KAAK,CAACW,OAAO,KAAKhJ,MAAM,IAAI,CAAC,IAAI,CAACkI,YAAY,IAAI,CAACjI,cAAc,CAACoI,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,SAAS,CAACD,KAAK,IAAI;MACjK,IAAI,CAAC,IAAI,CAACH,YAAY,EAAE;QACpBG,KAAK,CAACY,cAAc,CAAC,CAAC;QACtBC,eAAe,CAAC,IAAI,EAAEb,KAAK,CAACpD,IAAI,KAAK,SAAS,GAAG,UAAU,GAAG,OAAO,CAAC;MAC1E;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIkE,KAAKA,CAACC,YAAY,EAAE;IAChB,IAAI,CAACP,OAAO,GAAGO,YAAY;IAC3B;IACA,IAAI,CAACpB,kBAAkB,CAACjG,sBAAsB,CACzCqG,IAAI,CAACvI,MAAM,CAACwI,KAAK,IAAIA,KAAK,CAACpG,KAAK,KAAK,SAAS,CAAC,EAAEnC,IAAI,CAAC,CAAC,CAAC,CAAC,CACzDwI,SAAS,CAACD,KAAK,IAAI;MACpB,IAAI,CAACO,aAAa,CAACvE,IAAI,CAAC+E,YAAY,CAAC;MACrC,IAAI,CAACR,aAAa,CAACJ,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACV,IAAI,CAACY,UAAU,CAACW,cAAc,CAAC,CAAC;MACrC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACZ,qBAAqB,GAAG3E,UAAU,CAAC,MAAM,IAAI,CAACV,kBAAkB,CAAC,CAAC,EAAEiF,KAAK,CAACnG,SAAS,GAAG,GAAG,CAAC;IACnG,CAAC,CAAC;IACF,IAAI,CAACoH,MAAM,GAAG1B,cAAc,CAAC2B,OAAO;IACpC,IAAI,CAACvB,kBAAkB,CAAC9E,mBAAmB,CAAC,CAAC;EACjD;EACA;AACJ;AACA;EACIsG,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACjB,YAAY;EAC5B;EACA;AACJ;AACA;EACIkB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC3B,IAAI,CAAC4B,MAAM;EAC3B;EACA;AACJ;AACA;EACIC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACf,aAAa;EAC7B;EACA;AACJ;AACA;EACIE,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChB,IAAI,CAACgB,aAAa;EAClC;EACA;AACJ;AACA;EACIC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjB,IAAI,CAACiB,aAAa;EAClC;EACA;AACJ;AACA;AACA;EACIa,cAAcA,CAACC,QAAQ,EAAE;IACrB,IAAIC,QAAQ,GAAG,IAAI,CAAChC,IAAI,CAACC,MAAM,CAACgC,gBAAgB;IAChD,IAAIF,QAAQ,KAAKA,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACI,KAAK,CAAC,EAAE;MAC/CJ,QAAQ,CAACG,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAACH,QAAQ,CAACG,IAAI,CAAC,GAAGF,QAAQ,CAACG,KAAK,CAACJ,QAAQ,CAACI,KAAK,CAAC;IACjF,CAAC,MACI;MACDH,QAAQ,CAACI,kBAAkB,CAAC,CAAC;IACjC;IACA,IAAIL,QAAQ,KAAKA,QAAQ,CAACM,GAAG,IAAIN,QAAQ,CAACO,MAAM,CAAC,EAAE;MAC/CP,QAAQ,CAACM,GAAG,GAAGL,QAAQ,CAACK,GAAG,CAACN,QAAQ,CAACM,GAAG,CAAC,GAAGL,QAAQ,CAACM,MAAM,CAACP,QAAQ,CAACO,MAAM,CAAC;IAChF,CAAC,MACI;MACDN,QAAQ,CAACO,gBAAgB,CAAC,CAAC;IAC/B;IACA,IAAI,CAACvC,IAAI,CAAC8B,cAAc,CAAC,CAAC;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIU,UAAUA,CAACC,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAAC1C,IAAI,CAACwC,UAAU,CAACC,KAAK,EAAEC,MAAM,CAAC;IACnC,OAAO,IAAI;EACf;EACA;EACArC,aAAaA,CAACsC,OAAO,EAAE;IACnB,IAAI,CAAC3C,IAAI,CAACK,aAAa,CAACsC,OAAO,CAAC;IAChC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,CAAC3C,IAAI,CAAC4C,gBAAgB,CAACD,OAAO,CAAC;IACnC,OAAO,IAAI;EACf;EACA;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACrB,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACIlG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACkG,MAAM,GAAG1B,cAAc,CAACgD,MAAM;IACnC,IAAI,CAAC9C,IAAI,CAACqB,KAAK,CAAC,IAAI,CAACN,OAAO,EAAE;MAAEgC,WAAW,EAAE,IAAI,CAACC;IAAsB,CAAC,CAAC;IAC1E,IAAI,CAACC,iBAAiB,GAAG,IAAI;EACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS7B,eAAeA,CAACzE,GAAG,EAAEuG,eAAe,EAAEC,MAAM,EAAE;EACnDxG,GAAG,CAACqG,qBAAqB,GAAGE,eAAe;EAC3C,OAAOvG,GAAG,CAAC0E,KAAK,CAAC8B,MAAM,CAAC;AAC5B;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAI/L,cAAc,CAAC,kBAAkB,CAAC;AAC9D;AACA,MAAMgM,0BAA0B,GAAG,IAAIhM,cAAc,CAAC,gCAAgC,CAAC;AACvF;AACA,MAAMiM,0BAA0B,GAAG,IAAIjM,cAAc,CAAC,gCAAgC,EAAE;EACpFkM,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,OAAO,GAAG1M,MAAM,CAACL,OAAO,CAAC;IAC/B,OAAO,MAAM+M,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;EACjD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASC,2CAA2CA,CAACH,OAAO,EAAE;EAC1D,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,mCAAmC,GAAG;EACxCC,OAAO,EAAER,0BAA0B;EACnCS,IAAI,EAAE,CAACrN,OAAO,CAAC;EACfsN,UAAU,EAAEJ;AAChB,CAAC;AACD;AACA;AACA;AACA,MAAMK,SAAS,CAAC;EAcZ;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,WAAW,GAAG,IAAI,CAACE,uBAAuB;EAC7F;EACA;EACA,IAAI1C,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACyC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACzC,WAAW,GAAG,IAAI,CAAC2C,uBAAuB;EAC7F;EACAC,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,MAAM,GAAG,IAAI,CAACJ,aAAa;IACjC,OAAOI,MAAM,GAAGA,MAAM,CAACD,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACE,0BAA0B;EACjF;EACA;AACJ;AACA;AACA;;EAII9L,WAAWA,CAAA,EAAG;IAAAC,eAAA,mBAhCH5B,MAAM,CAACL,OAAO,CAAC;IAAAiC,eAAA,0BACR5B,MAAM,CAACsM,0BAA0B,EAAE;MAAEhK,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAV,eAAA,0BACtD5B,MAAM,CAACuM,0BAA0B,CAAC;IAAA3K,eAAA,wBACpC5B,MAAM,CAACkN,SAAS,EAAE;MAAE5K,QAAQ,EAAE,IAAI;MAAEoL,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAA9L,eAAA,uBACtD5B,MAAM,CAACqB,YAAY,CAAC;IAAAO,eAAA,kBACzB5B,MAAM,CAACR,MAAM,CAAC;IAAAoC,eAAA,kCACE,EAAE;IAAAA,eAAA,qCACC,IAAIf,OAAO,CAAC,CAAC;IAAAe,eAAA,kCAChB,IAAIf,OAAO,CAAC,CAAC;IAAAe,eAAA,4BACnBF,eAAe;IAAAE,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,yBAoBlBb,KAAK,CAAC,MAAM,IAAI,CAACoM,WAAW,CAACrE,MAAM,GAC9C,IAAI,CAACyE,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAAChE,IAAI,CAACrI,SAAS,CAACyM,SAAS,CAAC,CAAC,CAAC;IAEvD,IAAI,CAACC,qBAAqB,GAAG5E,YAAY;IACzC,IAAI,CAAC6E,oBAAoB,GAAG3L,kBAAkB;IAC9C,IAAI,CAAC4L,gBAAgB,GAAGzB,eAAe;EAC3C;EACA0B,IAAIA,CAACC,sBAAsB,EAAE9E,MAAM,EAAE;IACjC,IAAI+E,SAAS;IACb/E,MAAM,GAAAgF,aAAA,CAAAA,aAAA,KAAS,IAAI,CAACC,eAAe,IAAI,IAAIzM,eAAe,CAAC,CAAC,GAAMwH,MAAM,CAAE;IAC1EA,MAAM,CAACrC,EAAE,GAAGqC,MAAM,CAACrC,EAAE,IAAI,IAAI,CAACuH,YAAY,CAACC,KAAK,CAAC,iBAAiB,CAAC;IACnEnF,MAAM,CAACoF,cAAc,GAAGpF,MAAM,CAACoF,cAAc,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC;IACvE,MAAMC,MAAM,GAAG,IAAI,CAACC,OAAO,CAACV,IAAI,CAACC,sBAAsB,EAAAE,aAAA,CAAAA,aAAA,KAChDhF,MAAM;MACTgC,gBAAgB,EAAE,IAAI,CAACwD,QAAQ,CAAC1D,QAAQ,CAAC,CAAC,CAAC2D,MAAM,CAAC,CAAC,CAACtD,kBAAkB,CAAC,CAAC,CAACG,gBAAgB,CAAC,CAAC;MAC3F;MACAnC,YAAY,EAAE,IAAI;MAClB;MACA;MACA;MACAuF,cAAc,EAAE,KAAK;MACrB;MACA;MACAC,yBAAyB,EAAE,KAAK;MAChCC,SAAS,EAAE;QACP1I,IAAI,EAAE,IAAI,CAACyH,oBAAoB;QAC/BkB,SAAS,EAAEA,CAAA,KAAM;QACb;QACA;QACA;QACA;UAAEhC,OAAO,EAAE,IAAI,CAACiC,iBAAiB;UAAEC,QAAQ,EAAE/F;QAAO,CAAC,EACrD;UAAE6D,OAAO,EAAEtN,YAAY;UAAEwP,QAAQ,EAAE/F;QAAO,CAAC;MAEnD,CAAC;MACDgG,eAAe,EAAEA,CAAA,MAAO;QAAEjB;MAAU,CAAC,CAAC;MACtCc,SAAS,EAAEA,CAACnJ,GAAG,EAAEuJ,SAAS,EAAEC,eAAe,KAAK;QAAA,IAAAxM,OAAA;QAC5CqL,SAAS,GAAG,IAAI,IAAI,CAACL,qBAAqB,CAAChI,GAAG,EAAEsD,MAAM,EAAEkG,eAAe,CAAC;QACxEnB,SAAS,CAAClD,cAAc,EAAAnI,OAAA,GAACsG,MAAM,cAAAtG,OAAA,uBAANA,OAAA,CAAQoI,QAAQ,CAAC;QAC1C,OAAO,CACH;UAAE+B,OAAO,EAAE,IAAI,CAACc,oBAAoB;UAAEoB,QAAQ,EAAEG;QAAgB,CAAC,EACjE;UAAErC,OAAO,EAAE,IAAI,CAACe,gBAAgB;UAAEmB,QAAQ,EAAEE,SAAS,CAACE;QAAK,CAAC,EAC5D;UAAEtC,OAAO,EAAE,IAAI,CAACa,qBAAqB;UAAEqB,QAAQ,EAAEhB;QAAU,CAAC,CAC/D;MACL;IAAC,EACJ,CAAC;IACF;IACA;IACAA,SAAS,CAACqB,YAAY,GAAGd,MAAM,CAACc,YAAY;IAC5CrB,SAAS,CAAC/B,iBAAiB,GAAGsC,MAAM,CAACtC,iBAAiB;IACtD,IAAI,CAACiB,WAAW,CAACoC,IAAI,CAACtB,SAAS,CAAC;IAChC,IAAI,CAACtD,WAAW,CAACnF,IAAI,CAACyI,SAAS,CAAC;IAChCA,SAAS,CAACrD,WAAW,CAAC,CAAC,CAACnB,SAAS,CAAC,MAAM;MACpC,MAAM+F,KAAK,GAAG,IAAI,CAACrC,WAAW,CAACsC,OAAO,CAACxB,SAAS,CAAC;MACjD,IAAIuB,KAAK,GAAG,CAAC,CAAC,EAAE;QACZ,IAAI,CAACrC,WAAW,CAACuC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,CAACrC,WAAW,CAACrE,MAAM,EAAE;UAC1B,IAAI,CAACyE,kBAAkB,CAAC,CAAC,CAAC/H,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ,CAAC,CAAC;IACF,OAAOyI,SAAS;EACpB;EACA;AACJ;AACA;EACI0B,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,aAAa,CAAC,IAAI,CAACzC,WAAW,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACI0C,aAAaA,CAAChJ,EAAE,EAAE;IACd,OAAO,IAAI,CAACsG,WAAW,CAAC2C,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAClJ,EAAE,KAAKA,EAAE,CAAC;EAC5D;EACApB,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACmK,aAAa,CAAC,IAAI,CAACvC,uBAAuB,CAAC;IAChD,IAAI,CAACI,0BAA0B,CAAC9D,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAAC2D,uBAAuB,CAAC3D,QAAQ,CAAC,CAAC;EAC3C;EACAiG,aAAaA,CAACI,OAAO,EAAE;IACnB,IAAIC,CAAC,GAAGD,OAAO,CAAClH,MAAM;IACtB,OAAOmH,CAAC,EAAE,EAAE;MACRD,OAAO,CAACC,CAAC,CAAC,CAAC3F,KAAK,CAAC,CAAC;IACtB;EACJ;AAGJ;AAAC4F,UAAA,GAzHKhD,SAAS;AAAAtL,eAAA,CAATsL,SAAS,wBAAAiD,mBAAAlK,iBAAA;EAAA,YAAAA,iBAAA,IAuHwFiH,UAAS;AAAA;AAAAtL,eAAA,CAvH1GsL,SAAS,+BAxQkEnN,EAAE,CAAAqQ,kBAAA;EAAAC,KAAA,EAgYwBnD,UAAS;EAAAT,OAAA,EAATS,UAAS,CAAAoD,IAAA;EAAA9D,UAAA,EAAc;AAAM;AAExI;EAAA,QAAArE,SAAA,oBAAAA,SAAA,KAlYiFpI,EAAE,CAAAqI,iBAAA,CAkYQ8E,SAAS,EAAc,CAAC;IACvG9G,IAAI,EAAE7F,UAAU;IAChB4B,IAAI,EAAE,CAAC;MAAEqK,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA,MAAM+D,cAAc,CAAC;EAWjB5O,WAAWA,CAAA,EAAG;IAAAC,eAAA,oBAVF5B,MAAM,CAACgJ,YAAY,EAAE;MAAE1G,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAV,eAAA,sBACtC5B,MAAM,CAACQ,UAAU,CAAC;IAAAoB,eAAA,kBACtB5B,MAAM,CAACkN,SAAS,CAAC;IAC3B;IAAAtL,eAAA;IAEA;IAAAA,eAAA,eACO,QAAQ;IACf;IAAAA,eAAA;IAAAA,eAAA;EAGgB;EAChB4O,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACvC,SAAS,EAAE;MACjB;MACA;MACA;MACA;MACA;MACA,IAAI,CAACA,SAAS,GAAGwC,gBAAgB,CAAC,IAAI,CAACjO,WAAW,EAAE,IAAI,CAACiM,OAAO,CAACtB,WAAW,CAAC;IACjF;EACJ;EACAuD,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMC,aAAa,GAAGD,OAAO,CAAC,iBAAiB,CAAC,IAAIA,OAAO,CAAC,uBAAuB,CAAC;IACpF,IAAIC,aAAa,EAAE;MACf,IAAI,CAACrG,YAAY,GAAGqG,aAAa,CAACC,YAAY;IAClD;EACJ;EACAC,cAAcA,CAACtH,KAAK,EAAE;IAClB;IACA;IACA;IACA;IACAa,eAAe,CAAC,IAAI,CAAC4D,SAAS,EAAEzE,KAAK,CAACuH,OAAO,KAAK,CAAC,IAAIvH,KAAK,CAACwH,OAAO,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,IAAI,CAACzG,YAAY,CAAC;EACzH;AAGJ;AAAC0G,eAAA,GArCKV,cAAc;AAAA3O,eAAA,CAAd2O,cAAc,wBAAAW,wBAAAjL,iBAAA;EAAA,YAAAA,iBAAA,IAmCmFsK,eAAc;AAAA;AAAA3O,eAAA,CAnC/G2O,cAAc,8BA1Y6DxQ,EAAE,CAAAoR,iBAAA;EAAA/K,IAAA,EA8aQmK,eAAc;EAAAlK,SAAA;EAAAE,QAAA;EAAAC,YAAA,WAAA4K,6BAAA1K,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA9axB3G,EAAE,CAAAsR,UAAA,mBAAAC,yCAAAC,MAAA;QAAA,OA8aQ5K,GAAA,CAAAmK,cAAA,CAAAS,MAAqB,CAAC;MAAA,CAAT,CAAC;IAAA;IAAA,IAAA7K,EAAA;MA9axB3G,EAAE,CAAA+G,WAAA,eAAAH,GAAA,CAAAM,SAAA,IA8aqB,IAAI,UAAAN,GAAA,CAAAP,IAAA;IAAA;EAAA;EAAAoL,MAAA;IAAAvK,SAAA;IAAAb,IAAA;IAAAmE,YAAA;IAAAkH,eAAA;EAAA;EAAAC,QAAA;EAAArK,QAAA,GA9a3BtH,EAAE,CAAA4R,oBAAA;AAAA;AAgbnF;EAAA,QAAAxJ,SAAA,oBAAAA,SAAA,KAhbiFpI,EAAE,CAAAqI,iBAAA,CAgbQmI,cAAc,EAAc,CAAC;IAC5GnK,IAAI,EAAE3F,SAAS;IACf0B,IAAI,EAAE,CAAC;MACCkG,QAAQ,EAAE,sCAAsC;MAChDqJ,QAAQ,EAAE,gBAAgB;MAC1BhJ,IAAI,EAAE;QACF,SAAS,EAAE,wBAAwB;QACnC,mBAAmB,EAAE,mBAAmB;QACxC,aAAa,EAAE;MACnB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEzB,SAAS,EAAE,CAAC;MACpDb,IAAI,EAAE1F,KAAK;MACXyB,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEiE,IAAI,EAAE,CAAC;MACPA,IAAI,EAAE1F;IACV,CAAC,CAAC;IAAE6J,YAAY,EAAE,CAAC;MACfnE,IAAI,EAAE1F,KAAK;MACXyB,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEsP,eAAe,EAAE,CAAC;MAClBrL,IAAI,EAAE1F,KAAK;MACXyB,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMyP,sBAAsB,CAAC;EAIzBjQ,WAAWA,CAAA,EAAG;IAAAC,eAAA,qBAHD5B,MAAM,CAACgJ,YAAY,EAAE;MAAE1G,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAV,eAAA,sBACvC5B,MAAM,CAACQ,UAAU,CAAC;IAAAoB,eAAA,kBACtB5B,MAAM,CAACkN,SAAS,CAAC;EACX;EAChBsD,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACqB,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAGpB,gBAAgB,CAAC,IAAI,CAACjO,WAAW,EAAE,IAAI,CAACiM,OAAO,CAACtB,WAAW,CAAC;IAClF;IACA,IAAI,IAAI,CAAC0E,UAAU,EAAE;MACjB3N,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAC0N,MAAM,CAAC,CAAC;MACjB,CAAC,CAAC;IACN;EACJ;EACArM,WAAWA,CAAA,EAAG;IAAA,IAAAsM,gBAAA;IACV;IACA;IACA,MAAMC,QAAQ,IAAAD,gBAAA,GAAG,IAAI,CAACF,UAAU,cAAAE,gBAAA,uBAAfA,gBAAA,CAAiB5I,kBAAkB;IACpD,IAAI6I,QAAQ,EAAE;MACV9N,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAC6N,SAAS,CAAC,CAAC;MACpB,CAAC,CAAC;IACN;EACJ;AAGJ;AAACC,uBAAA,GA3BKN,sBAAsB;AAAAhQ,eAAA,CAAtBgQ,sBAAsB,wBAAAO,gCAAAlM,iBAAA;EAAA,YAAAA,iBAAA,IAyB2E2L,uBAAsB;AAAA;AAAAhQ,eAAA,CAzBvHgQ,sBAAsB,8BAvcqD7R,EAAE,CAAAoR,iBAAA;EAAA/K,IAAA,EAieQwL;AAAsB;AAEjH;EAAA,QAAAzJ,SAAA,oBAAAA,SAAA,KAneiFpI,EAAE,CAAAqI,iBAAA,CAmeQwJ,sBAAsB,EAAc,CAAC;IACpHxL,IAAI,EAAE3F;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA,MAAM2R,cAAc,SAASR,sBAAsB,CAAC;EAAAjQ,YAAA,GAAAQ,IAAA;IAAA,SAAAA,IAAA;IAAAP,eAAA,aAC3C5B,MAAM,CAACqB,YAAY,CAAC,CAACgN,KAAK,CAAC,uBAAuB,CAAC;EAAA;EACxDyD,MAAMA,CAAA,EAAG;IAAA,IAAAO,qBAAA,EAAAC,sBAAA;IACL;IACA;IACA,CAAAD,qBAAA,OAAI,CAACR,UAAU,CAAC1I,kBAAkB,cAAAkJ,qBAAA,gBAAAC,sBAAA,GAAlCD,qBAAA,CAAoCE,kBAAkB,cAAAD,sBAAA,eAAtDA,sBAAA,CAAAE,IAAA,CAAAH,qBAAA,EAAyD,IAAI,CAACxL,EAAE,CAAC;EACrE;EACAoL,SAASA,CAAA,EAAG;IAAA,IAAAQ,iBAAA,EAAAC,qBAAA;IACR,CAAAD,iBAAA,OAAI,CAACZ,UAAU,cAAAY,iBAAA,gBAAAA,iBAAA,GAAfA,iBAAA,CAAiBtJ,kBAAkB,cAAAsJ,iBAAA,gBAAAC,qBAAA,GAAnCD,iBAAA,CAAqCE,qBAAqB,cAAAD,qBAAA,eAA1DA,qBAAA,CAAAF,IAAA,CAAAC,iBAAA,EAA6D,IAAI,CAAC5L,EAAE,CAAC;EACzE;AAGJ;AAAC+L,eAAA,GAZKR,cAAc;AAAAxQ,eAAA,CAAdwQ,cAAc;EAAA,IAAAS,4BAAA;EAAA,gBAAAC,wBAAA7M,iBAAA;IAAA,QAAA4M,4BAAA,KAAAA,4BAAA,GAze6D9S,EAAE,CAAAmG,qBAAA,CAmfoBkM,eAAc,IAAAnM,iBAAA,IAAdmM,eAAc;EAAA;AAAA;AAAAxQ,eAAA,CAV/GwQ,cAAc,8BAze6DrS,EAAE,CAAAoR,iBAAA;EAAA/K,IAAA,EAofQgM,eAAc;EAAA/L,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAuM,6BAAArM,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MApfxB3G,EAAE,CAAA6G,cAAA,OAAAD,GAAA,CAAAE,EAofqB,CAAC;IAAA;EAAA;EAAA2K,MAAA;IAAA3K,EAAA;EAAA;EAAA6K,QAAA;EAAArK,QAAA,GApfxBtH,EAAE,CAAAuH,0BAAA;AAAA;AAsfnF;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAtfiFpI,EAAE,CAAAqI,iBAAA,CAsfQgK,cAAc,EAAc,CAAC;IAC5GhM,IAAI,EAAE3F,SAAS;IACf0B,IAAI,EAAE,CAAC;MACCkG,QAAQ,EAAE,sCAAsC;MAChDqJ,QAAQ,EAAE,gBAAgB;MAC1BhJ,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,MAAM,EAAE;MACZ;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE7B,EAAE,EAAE,CAAC;MACnBT,IAAI,EAAE1F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMsS,gBAAgB,CAAC;AAGtBC,iBAAA,GAHKD,gBAAgB;AAAApR,eAAA,CAAhBoR,gBAAgB,wBAAAE,0BAAAjN,iBAAA;EAAA,YAAAA,iBAAA,IACiF+M,iBAAgB;AAAA;AAAApR,eAAA,CADjHoR,gBAAgB,8BAtgB2DjT,EAAE,CAAAoR,iBAAA;EAAA/K,IAAA,EAwgBQ4M,iBAAgB;EAAA3M,SAAA;EAAAC,SAAA;EAAAe,QAAA,GAxgB1BtH,EAAE,CAAAoT,uBAAA,EAwgB8N7R,EAAE,CAACC,aAAa;AAAA;AAEjU;EAAA,QAAA4G,SAAA,oBAAAA,SAAA,KA1gBiFpI,EAAE,CAAAqI,iBAAA,CA0gBQ4K,gBAAgB,EAAc,CAAC;IAC9G5M,IAAI,EAAE3F,SAAS;IACf0B,IAAI,EAAE,CAAC;MACCkG,QAAQ,EAAE,8DAA8D;MACxEK,IAAI,EAAE;QAAE,OAAO,EAAE;MAA6C,CAAC;MAC/D0K,cAAc,EAAE,CAAC7R,aAAa;IAClC,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAM8R,gBAAgB,SAASzB,sBAAsB,CAAC;EAAAjQ,YAAA,GAAAQ,IAAA;IAAA,SAAAA,IAAA;IAClD;AACJ;AACA;IAFIP,eAAA;EAAA;EAIAkQ,MAAMA,CAAA,EAAG;IAAA,IAAAwB,sBAAA,EAAAC,sBAAA;IACL,CAAAD,sBAAA,OAAI,CAACzB,UAAU,CAAC1I,kBAAkB,cAAAmK,sBAAA,gBAAAC,sBAAA,GAAlCD,sBAAA,CAAoC9O,yBAAyB,cAAA+O,sBAAA,eAA7DA,sBAAA,CAAAf,IAAA,CAAAc,sBAAA,EAAgE,CAAC,CAAC;EACtE;EACArB,SAASA,CAAA,EAAG;IAAA,IAAAuB,sBAAA,EAAAC,sBAAA;IACR,CAAAD,sBAAA,OAAI,CAAC3B,UAAU,CAAC1I,kBAAkB,cAAAqK,sBAAA,gBAAAC,sBAAA,GAAlCD,sBAAA,CAAoChP,yBAAyB,cAAAiP,sBAAA,eAA7DA,sBAAA,CAAAjB,IAAA,CAAAgB,sBAAA,EAAgE,CAAC,CAAC,CAAC;EACvE;AAGJ;AAACE,iBAAA,GAbKL,gBAAgB;AAAAzR,eAAA,CAAhByR,gBAAgB;EAAA,IAAAM,8BAAA;EAAA,gBAAAC,0BAAA3N,iBAAA;IAAA,QAAA0N,8BAAA,KAAAA,8BAAA,GAthB2D5T,EAAE,CAAAmG,qBAAA,CAiiBoBmN,iBAAgB,IAAApN,iBAAA,IAAhBoN,iBAAgB;EAAA;AAAA;AAAAzR,eAAA,CAXjHyR,gBAAgB,8BAthB2DtT,EAAE,CAAAoR,iBAAA;EAAA/K,IAAA,EAkiBQiN,iBAAgB;EAAAhN,SAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAqN,+BAAAnN,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAliB1B3G,EAAE,CAAAqH,WAAA,uCAAAT,GAAA,CAAAmN,KAAA,KAkiBkB,OAAK,CAAC,wCAAAnN,GAAA,CAAAmN,KAAA,KAAN,QAAK,CAAC,qCAAAnN,GAAA,CAAAmN,KAAA,KAAN,KAAK,CAAC;IAAA;EAAA;EAAAtC,MAAA;IAAAsC,KAAA;EAAA;EAAAzM,QAAA,GAliB1BtH,EAAE,CAAAuH,0BAAA;AAAA;AAoiBnF;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KApiBiFpI,EAAE,CAAAqI,iBAAA,CAoiBQiL,gBAAgB,EAAc,CAAC;IAC9GjN,IAAI,EAAE3F,SAAS;IACf0B,IAAI,EAAE,CAAC;MACCkG,QAAQ,EAAE,8DAA8D;MACxEK,IAAI,EAAE;QACF,OAAO,EAAE,4CAA4C;QACrD,4CAA4C,EAAE,mBAAmB;QACjE,6CAA6C,EAAE,oBAAoB;QACnE,0CAA0C,EAAE;MAChD;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEoL,KAAK,EAAE,CAAC;MACtB1N,IAAI,EAAE1F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,SAAS+P,gBAAgBA,CAACsD,OAAO,EAAE5G,WAAW,EAAE;EAC5C,IAAIK,MAAM,GAAGuG,OAAO,CAACtR,aAAa,CAACuR,aAAa;EAChD,OAAOxG,MAAM,IAAI,CAACA,MAAM,CAAC1J,SAAS,CAACmQ,QAAQ,CAAC,0BAA0B,CAAC,EAAE;IACrEzG,MAAM,GAAGA,MAAM,CAACwG,aAAa;EACjC;EACA,OAAOxG,MAAM,GAAGL,WAAW,CAAC2C,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAClJ,EAAE,KAAK2G,MAAM,CAAC3G,EAAE,CAAC,GAAG,IAAI;AAC9E;AAEA,MAAMqN,UAAU,GAAG,CACfhS,kBAAkB,EAClBqO,cAAc,EACd6B,cAAc,EACdiB,gBAAgB,EAChBL,gBAAgB,CACnB;AACD,MAAMmB,eAAe,CAAC;AAYrBC,gBAAA,GAZKD,eAAe;AAAAvS,eAAA,CAAfuS,eAAe,wBAAAE,yBAAApO,iBAAA;EAAA,YAAAA,iBAAA,IACkFkO,gBAAe;AAAA;AAAAvS,eAAA,CADhHuS,eAAe,8BAtkB4DpU,EAAE,CAAAuU,gBAAA;EAAAlO,IAAA,EAwkBqB+N,gBAAe;EAAA1L,OAAA,GAAY/I,YAAY,EAAEE,aAAa,EAAEE,YAAY,EAAE2B,eAAe,EAAES,kBAAkB,EACrMqO,cAAc,EACd6B,cAAc,EACdiB,gBAAgB,EAChBL,gBAAgB;EAAAuB,OAAA,GAAa9S,eAAe,EAAES,kBAAkB,EAChEqO,cAAc,EACd6B,cAAc,EACdiB,gBAAgB,EAChBL,gBAAgB;AAAA;AAAApR,eAAA,CAVtBuS,eAAe,8BAtkB4DpU,EAAE,CAAAyU,gBAAA;EAAAzF,SAAA,EAilBiD,CAAC7B,SAAS,CAAC;EAAAzE,OAAA,GAAY/I,YAAY,EAAEE,aAAa,EAAEE,YAAY,EAAE2B,eAAe,EAAEA,eAAe;AAAA;AAEtO;EAAA,QAAA0G,SAAA,oBAAAA,SAAA,KAnlBiFpI,EAAE,CAAAqI,iBAAA,CAmlBQ+L,eAAe,EAAc,CAAC;IAC7G/N,IAAI,EAAEzF,QAAQ;IACdwB,IAAI,EAAE,CAAC;MACCsG,OAAO,EAAE,CAAC/I,YAAY,EAAEE,aAAa,EAAEE,YAAY,EAAE2B,eAAe,EAAE,GAAGyS,UAAU,CAAC;MACpFK,OAAO,EAAE,CAAC9S,eAAe,EAAE,GAAGyS,UAAU,CAAC;MACzCnF,SAAS,EAAE,CAAC7B,SAAS;IACzB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASmG,gBAAgB,IAAI7R,CAAC,EAAE6I,eAAe,IAAIoK,CAAC,EAAElE,cAAc,IAAImE,CAAC,EAAEtC,cAAc,IAAIuC,CAAC,EAAE3B,gBAAgB,IAAI4B,CAAC,EAAE1S,kBAAkB,IAAI2S,CAAC,EAAExI,eAAe,IAAIyI,CAAC,EAAExI,0BAA0B,IAAIyI,CAAC,EAAExI,0BAA0B,IAAIyI,CAAC,EAAEnI,2CAA2C,IAAIoI,CAAC,EAAEnI,mCAAmC,IAAImD,CAAC,EAAE/C,SAAS,IAAIgI,CAAC,EAAExT,eAAe,IAAIyT,CAAC,EAAEpM,cAAc,IAAIqM,CAAC,EAAEpM,YAAY,IAAIqM,CAAC,EAAElB,eAAe,IAAImB,CAAC;AACta", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}