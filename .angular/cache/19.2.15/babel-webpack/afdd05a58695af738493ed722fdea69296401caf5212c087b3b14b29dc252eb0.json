{"ast": null, "code": "var _InputSelectComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-select.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-select.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { isObservable, of, ReplaySubject, Subject, Subscription } from 'rxjs';\nimport { map, takeUntil } from 'rxjs/operators';\nlet InputSelectComponent = (_InputSelectComponent = class InputSelectComponent {\n  set componentOptions(value) {\n    var _value$validation;\n    this.multiple = (value === null || value === void 0 ? void 0 : value.type) === 'multiselect';\n    this.setSelectOptions((value === null || value === void 0 ? void 0 : value.data) || []);\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.errorMessages = value === null || value === void 0 || (_value$validation = value.validation) === null || _value$validation === void 0 ? void 0 : _value$validation.messages;\n    this.showSearch = value === null || value === void 0 ? void 0 : value.showSearch;\n    this.searchPlaceholder = value === null || value === void 0 ? void 0 : value.searchPlaceholder;\n    const search = typeof (value === null || value === void 0 ? void 0 : value.search) === 'undefined' ? {\n      show: this.showSearch,\n      placeholder: this.searchPlaceholder\n    } : value === null || value === void 0 ? void 0 : value.search;\n    if (typeof search === 'boolean') {\n      this.showSearch = search;\n    } else if (search.placeholder) {\n      this.showSearch = typeof search.show === 'undefined' ? true : search.show;\n      this.searchPlaceholder = search.placeholder;\n    }\n    if ((value === null || value === void 0 ? void 0 : value.type) === 'select') {\n      this.emptyOptionPlaceholder = value === null || value === void 0 ? void 0 : value.emptyOptionPlaceholder;\n      this.emptyOptionDisabled = value === null || value === void 0 ? void 0 : value.emptyOptionDisabled;\n      const emptyOption = typeof (value === null || value === void 0 ? void 0 : value.emptyOption) === 'undefined' ? {\n        show: typeof this.emptyOptionDisabled === 'undefined' ? undefined : !this.emptyOptionDisabled,\n        placeholder: this.emptyOptionPlaceholder\n      } : value === null || value === void 0 ? void 0 : value.emptyOption;\n      if (typeof emptyOption === 'boolean') {\n        this.emptyOptionDisabled = !emptyOption;\n      } else if (emptyOption.placeholder) {\n        this.emptyOptionDisabled = typeof emptyOption.show === 'undefined' ? false : !emptyOption.show;\n        this.emptyOptionPlaceholder = emptyOption.placeholder;\n      }\n    }\n    if ((value === null || value === void 0 ? void 0 : value.type) === 'multiselect') {\n      this.disableAllOption = value === null || value === void 0 ? void 0 : value.disableAllOption;\n    }\n  }\n  constructor() {\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n    this.multiple = false;\n    this.selectOptions = new ReplaySubject(1);\n    this.destroy$ = new Subject();\n    this.subscription = new Subscription();\n    this.selectOptions.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      if (!this.control || !this.control.value) {\n        return;\n      }\n      if (this.multiple) {\n        var _this$control;\n        const value = (((_this$control = this.control) === null || _this$control === void 0 ? void 0 : _this$control.value) || []).filter(val => data.find(item => item.id === val));\n        if (this.control.value.length !== value.length) {\n          this.control.setValue(value);\n        }\n      } else {\n        if (!data.find(item => {\n          var _this$control2;\n          return item.id === ((_this$control2 = this.control) === null || _this$control2 === void 0 ? void 0 : _this$control2.value);\n        })) {\n          this.clear();\n        }\n      }\n    });\n  }\n  clear(event) {\n    event === null || event === void 0 || event.stopPropagation();\n    if (this.control) {\n      this.control.setValue(null);\n    }\n  }\n  isClearButtonVisible() {\n    var _this$control$value$f, _this$control3;\n    return !this.emptyOptionDisabled && (this.multiple ? this.control && Array.isArray(this.control.value) && ((_this$control$value$f = this.control.value.filter(i => i)) === null || _this$control$value$f === void 0 ? void 0 : _this$control$value$f.length) : (_this$control3 = this.control) === null || _this$control3 === void 0 ? void 0 : _this$control3.value);\n  }\n  ngOnDestroy() {\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n  setSelectOptions(data) {\n    this.subscription.unsubscribe();\n    const source = isObservable(data) ? data : of(data || []);\n    this.subscription = source.pipe(map(opts => opts.map(opt => ({\n      text: opt.text || opt.title || opt.displayName,\n      id: opt.value || opt.id,\n      disabled: opt.disabled,\n      data: opt\n    }))), map(opts => opts.filter(({\n      id,\n      text\n    }) => !!id && !!text)), takeUntil(this.destroy$)).subscribe(items => {\n      this.selectOptions.next(items);\n    });\n  }\n}, _InputSelectComponent.ctorParameters = () => [], _InputSelectComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }]\n}, _InputSelectComponent);\nInputSelectComponent = __decorate([Component({\n  selector: 'lib-input-select',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], InputSelectComponent);\nexport { InputSelectComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "isObservable", "of", "ReplaySubject", "Subject", "Subscription", "map", "takeUntil", "InputSelectComponent", "_InputSelectComponent", "componentOptions", "value", "_value$validation", "multiple", "type", "setSelectOptions", "data", "title", "errorMessages", "validation", "messages", "showSearch", "searchPlaceholder", "search", "show", "placeholder", "emptyOptionPlaceholder", "emptyOptionDisabled", "emptyOption", "undefined", "disableAllOption", "constructor", "id", "readonly", "submitted", "selectOptions", "destroy$", "subscription", "pipe", "subscribe", "control", "_this$control", "filter", "val", "find", "item", "length", "setValue", "_this$control2", "clear", "event", "stopPropagation", "isClearButtonVisible", "_this$control$value$f", "_this$control3", "Array", "isArray", "i", "ngOnDestroy", "next", "complete", "unsubscribe", "source", "opts", "opt", "text", "displayName", "disabled", "items", "ctorParameters", "propDecorators", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-select/input-select.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-select.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./input-select.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { isObservable, of, ReplaySubject, Subject, Subscription } from 'rxjs';\nimport { map, takeUntil } from 'rxjs/operators';\nlet InputSelectComponent = class InputSelectComponent {\n    set componentOptions(value) {\n        this.multiple = value?.type === 'multiselect';\n        this.setSelectOptions(value?.data || []);\n        this.title = value?.title;\n        this.errorMessages = value?.validation?.messages;\n        this.showSearch = value?.showSearch;\n        this.searchPlaceholder = value?.searchPlaceholder;\n        const search = typeof value?.search === 'undefined' ? {\n            show: this.showSearch,\n            placeholder: this.searchPlaceholder\n        } : value?.search;\n        if (typeof search === 'boolean') {\n            this.showSearch = search;\n        }\n        else if (search.placeholder) {\n            this.showSearch = typeof search.show === 'undefined' ? true : search.show;\n            this.searchPlaceholder = search.placeholder;\n        }\n        if (value?.type === 'select') {\n            this.emptyOptionPlaceholder = value?.emptyOptionPlaceholder;\n            this.emptyOptionDisabled = value?.emptyOptionDisabled;\n            const emptyOption = typeof value?.emptyOption === 'undefined' ? {\n                show: typeof this.emptyOptionDisabled === 'undefined' ? undefined : !this.emptyOptionDisabled,\n                placeholder: this.emptyOptionPlaceholder\n            } : value?.emptyOption;\n            if (typeof emptyOption === 'boolean') {\n                this.emptyOptionDisabled = !emptyOption;\n            }\n            else if (emptyOption.placeholder) {\n                this.emptyOptionDisabled = typeof emptyOption.show === 'undefined' ? false : !emptyOption.show;\n                this.emptyOptionPlaceholder = emptyOption.placeholder;\n            }\n        }\n        if (value?.type === 'multiselect') {\n            this.disableAllOption = value?.disableAllOption;\n        }\n    }\n    constructor() {\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n        this.multiple = false;\n        this.selectOptions = new ReplaySubject(1);\n        this.destroy$ = new Subject();\n        this.subscription = new Subscription();\n        this.selectOptions\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(data => {\n            if (!this.control || !this.control.value) {\n                return;\n            }\n            if (this.multiple) {\n                const value = (this.control?.value || []).filter((val) => data.find(item => item.id === val));\n                if (this.control.value.length !== value.length) {\n                    this.control.setValue(value);\n                }\n            }\n            else {\n                if (!data.find(item => item.id === this.control?.value)) {\n                    this.clear();\n                }\n            }\n        });\n    }\n    clear(event) {\n        event?.stopPropagation();\n        if (this.control) {\n            this.control.setValue(null);\n        }\n    }\n    isClearButtonVisible() {\n        return !this.emptyOptionDisabled &&\n            (this.multiple ?\n                this.control && Array.isArray(this.control.value) && this.control.value.filter(i => i)?.length :\n                this.control?.value);\n    }\n    ngOnDestroy() {\n        this.destroy$.next(undefined);\n        this.destroy$.complete();\n    }\n    setSelectOptions(data) {\n        this.subscription.unsubscribe();\n        const source = isObservable(data) ? data : of(data || []);\n        this.subscription = source.pipe(map(opts => opts.map(opt => ({\n            text: opt.text || opt.title || opt.displayName,\n            id: opt.value || opt.id,\n            disabled: opt.disabled,\n            data: opt\n        }))), map(opts => opts.filter(({ id, text }) => !!id && !!text)), takeUntil(this.destroy$)).subscribe(items => {\n            this.selectOptions.next(items);\n        });\n    }\n    static { this.ctorParameters = () => []; }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }]\n    }; }\n};\nInputSelectComponent = __decorate([\n    Component({\n        selector: 'lib-input-select',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], InputSelectComponent);\nexport { InputSelectComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,YAAY,EAAEC,EAAE,EAAEC,aAAa,EAAEC,OAAO,EAAEC,YAAY,QAAQ,MAAM;AAC7E,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAC/C,IAAIC,oBAAoB,IAAAC,qBAAA,GAAG,MAAMD,oBAAoB,CAAC;EAClD,IAAIE,gBAAgBA,CAACC,KAAK,EAAE;IAAA,IAAAC,iBAAA;IACxB,IAAI,CAACC,QAAQ,GAAG,CAAAF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,IAAI,MAAK,aAAa;IAC7C,IAAI,CAACC,gBAAgB,CAAC,CAAAJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEK,IAAI,KAAI,EAAE,CAAC;IACxC,IAAI,CAACC,KAAK,GAAGN,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,KAAK;IACzB,IAAI,CAACC,aAAa,GAAGP,KAAK,aAALA,KAAK,gBAAAC,iBAAA,GAALD,KAAK,CAAEQ,UAAU,cAAAP,iBAAA,uBAAjBA,iBAAA,CAAmBQ,QAAQ;IAChD,IAAI,CAACC,UAAU,GAAGV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEU,UAAU;IACnC,IAAI,CAACC,iBAAiB,GAAGX,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,iBAAiB;IACjD,MAAMC,MAAM,GAAG,QAAOZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,MAAM,MAAK,WAAW,GAAG;MAClDC,IAAI,EAAE,IAAI,CAACH,UAAU;MACrBI,WAAW,EAAE,IAAI,CAACH;IACtB,CAAC,GAAGX,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEY,MAAM;IACjB,IAAI,OAAOA,MAAM,KAAK,SAAS,EAAE;MAC7B,IAAI,CAACF,UAAU,GAAGE,MAAM;IAC5B,CAAC,MACI,IAAIA,MAAM,CAACE,WAAW,EAAE;MACzB,IAAI,CAACJ,UAAU,GAAG,OAAOE,MAAM,CAACC,IAAI,KAAK,WAAW,GAAG,IAAI,GAAGD,MAAM,CAACC,IAAI;MACzE,IAAI,CAACF,iBAAiB,GAAGC,MAAM,CAACE,WAAW;IAC/C;IACA,IAAI,CAAAd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,IAAI,MAAK,QAAQ,EAAE;MAC1B,IAAI,CAACY,sBAAsB,GAAGf,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEe,sBAAsB;MAC3D,IAAI,CAACC,mBAAmB,GAAGhB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,mBAAmB;MACrD,MAAMC,WAAW,GAAG,QAAOjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,WAAW,MAAK,WAAW,GAAG;QAC5DJ,IAAI,EAAE,OAAO,IAAI,CAACG,mBAAmB,KAAK,WAAW,GAAGE,SAAS,GAAG,CAAC,IAAI,CAACF,mBAAmB;QAC7FF,WAAW,EAAE,IAAI,CAACC;MACtB,CAAC,GAAGf,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,WAAW;MACtB,IAAI,OAAOA,WAAW,KAAK,SAAS,EAAE;QAClC,IAAI,CAACD,mBAAmB,GAAG,CAACC,WAAW;MAC3C,CAAC,MACI,IAAIA,WAAW,CAACH,WAAW,EAAE;QAC9B,IAAI,CAACE,mBAAmB,GAAG,OAAOC,WAAW,CAACJ,IAAI,KAAK,WAAW,GAAG,KAAK,GAAG,CAACI,WAAW,CAACJ,IAAI;QAC9F,IAAI,CAACE,sBAAsB,GAAGE,WAAW,CAACH,WAAW;MACzD;IACJ;IACA,IAAI,CAAAd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEG,IAAI,MAAK,aAAa,EAAE;MAC/B,IAAI,CAACgB,gBAAgB,GAAGnB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmB,gBAAgB;IACnD;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACrB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACsB,aAAa,GAAG,IAAIhC,aAAa,CAAC,CAAC,CAAC;IACzC,IAAI,CAACiC,QAAQ,GAAG,IAAIhC,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACiC,YAAY,GAAG,IAAIhC,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC8B,aAAa,CACbG,IAAI,CAAC/B,SAAS,CAAC,IAAI,CAAC6B,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAACvB,IAAI,IAAI;MACnB,IAAI,CAAC,IAAI,CAACwB,OAAO,IAAI,CAAC,IAAI,CAACA,OAAO,CAAC7B,KAAK,EAAE;QACtC;MACJ;MACA,IAAI,IAAI,CAACE,QAAQ,EAAE;QAAA,IAAA4B,aAAA;QACf,MAAM9B,KAAK,GAAG,CAAC,EAAA8B,aAAA,OAAI,CAACD,OAAO,cAAAC,aAAA,uBAAZA,aAAA,CAAc9B,KAAK,KAAI,EAAE,EAAE+B,MAAM,CAAEC,GAAG,IAAK3B,IAAI,CAAC4B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACb,EAAE,KAAKW,GAAG,CAAC,CAAC;QAC7F,IAAI,IAAI,CAACH,OAAO,CAAC7B,KAAK,CAACmC,MAAM,KAAKnC,KAAK,CAACmC,MAAM,EAAE;UAC5C,IAAI,CAACN,OAAO,CAACO,QAAQ,CAACpC,KAAK,CAAC;QAChC;MACJ,CAAC,MACI;QACD,IAAI,CAACK,IAAI,CAAC4B,IAAI,CAACC,IAAI;UAAA,IAAAG,cAAA;UAAA,OAAIH,IAAI,CAACb,EAAE,OAAAgB,cAAA,GAAK,IAAI,CAACR,OAAO,cAAAQ,cAAA,uBAAZA,cAAA,CAAcrC,KAAK;QAAA,EAAC,EAAE;UACrD,IAAI,CAACsC,KAAK,CAAC,CAAC;QAChB;MACJ;IACJ,CAAC,CAAC;EACN;EACAA,KAAKA,CAACC,KAAK,EAAE;IACTA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,eAAe,CAAC,CAAC;IACxB,IAAI,IAAI,CAACX,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACO,QAAQ,CAAC,IAAI,CAAC;IAC/B;EACJ;EACAK,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,cAAA;IACnB,OAAO,CAAC,IAAI,CAAC3B,mBAAmB,KAC3B,IAAI,CAACd,QAAQ,GACV,IAAI,CAAC2B,OAAO,IAAIe,KAAK,CAACC,OAAO,CAAC,IAAI,CAAChB,OAAO,CAAC7B,KAAK,CAAC,MAAA0C,qBAAA,GAAI,IAAI,CAACb,OAAO,CAAC7B,KAAK,CAAC+B,MAAM,CAACe,CAAC,IAAIA,CAAC,CAAC,cAAAJ,qBAAA,uBAAjCA,qBAAA,CAAmCP,MAAM,KAAAQ,cAAA,GAC9F,IAAI,CAACd,OAAO,cAAAc,cAAA,uBAAZA,cAAA,CAAc3C,KAAK,CAAC;EAChC;EACA+C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACtB,QAAQ,CAACuB,IAAI,CAAC9B,SAAS,CAAC;IAC7B,IAAI,CAACO,QAAQ,CAACwB,QAAQ,CAAC,CAAC;EAC5B;EACA7C,gBAAgBA,CAACC,IAAI,EAAE;IACnB,IAAI,CAACqB,YAAY,CAACwB,WAAW,CAAC,CAAC;IAC/B,MAAMC,MAAM,GAAG7D,YAAY,CAACe,IAAI,CAAC,GAAGA,IAAI,GAAGd,EAAE,CAACc,IAAI,IAAI,EAAE,CAAC;IACzD,IAAI,CAACqB,YAAY,GAAGyB,MAAM,CAACxB,IAAI,CAAChC,GAAG,CAACyD,IAAI,IAAIA,IAAI,CAACzD,GAAG,CAAC0D,GAAG,KAAK;MACzDC,IAAI,EAAED,GAAG,CAACC,IAAI,IAAID,GAAG,CAAC/C,KAAK,IAAI+C,GAAG,CAACE,WAAW;MAC9ClC,EAAE,EAAEgC,GAAG,CAACrD,KAAK,IAAIqD,GAAG,CAAChC,EAAE;MACvBmC,QAAQ,EAAEH,GAAG,CAACG,QAAQ;MACtBnD,IAAI,EAAEgD;IACV,CAAC,CAAC,CAAC,CAAC,EAAE1D,GAAG,CAACyD,IAAI,IAAIA,IAAI,CAACrB,MAAM,CAAC,CAAC;MAAEV,EAAE;MAAEiC;IAAK,CAAC,KAAK,CAAC,CAACjC,EAAE,IAAI,CAAC,CAACiC,IAAI,CAAC,CAAC,EAAE1D,SAAS,CAAC,IAAI,CAAC6B,QAAQ,CAAC,CAAC,CAACG,SAAS,CAAC6B,KAAK,IAAI;MAC3G,IAAI,CAACjC,aAAa,CAACwB,IAAI,CAACS,KAAK,CAAC;IAClC,CAAC,CAAC;EACN;AASJ,CAAC,EARY3D,qBAAA,CAAK4D,cAAc,GAAG,MAAM,EAAE,EAC9B5D,qBAAA,CAAK6D,cAAc,GAAG;EAC3B9B,OAAO,EAAE,CAAC;IAAE1B,IAAI,EAAEd;EAAM,CAAC,CAAC;EAC1BgC,EAAE,EAAE,CAAC;IAAElB,IAAI,EAAEd;EAAM,CAAC,CAAC;EACrBiC,QAAQ,EAAE,CAAC;IAAEnB,IAAI,EAAEd;EAAM,CAAC,CAAC;EAC3BkC,SAAS,EAAE,CAAC;IAAEpB,IAAI,EAAEd;EAAM,CAAC,CAAC;EAC5BU,gBAAgB,EAAE,CAAC;IAAEI,IAAI,EAAEd;EAAM,CAAC;AACtC,CAAC,EAAAS,qBAAA,CACJ;AACDD,oBAAoB,GAAGZ,UAAU,CAAC,CAC9BG,SAAS,CAAC;EACNwE,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE3E,oBAAoB;EAC9B4E,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC5E,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEU,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}