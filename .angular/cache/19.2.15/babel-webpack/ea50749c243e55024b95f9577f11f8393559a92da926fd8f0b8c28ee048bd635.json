{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMultiselectComponent } from './swui-multiselect.component';\nimport { SwuiMenuSelectModule } from '../swui-menu-select/swui-menu-select.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatInputModule } from '@angular/material/input';\nexport const MULTISELECT_MODULES = [ReactiveFormsModule, MatInputModule, MatMenuModule, SwuiMenuSelectModule];\nlet SwuiMultiselectModule = class SwuiMultiselectModule {};\nSwuiMultiselectModule = __decorate([NgModule({\n  declarations: [SwuiMultiselectComponent],\n  imports: [CommonModule, TranslateModule.forChild(), ...MULTISELECT_MODULES],\n  exports: [SwuiMultiselectComponent]\n})], SwuiMultiselectModule);\nexport { SwuiMultiselectModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ReactiveFormsModule", "TranslateModule", "SwuiMultiselectComponent", "SwuiMenuSelectModule", "MatMenuModule", "MatInputModule", "MULTISELECT_MODULES", "SwuiMultiselectModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-multiselect/swui-multiselect.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\n\nimport { SwuiMultiselectComponent } from './swui-multiselect.component';\nimport { SwuiMenuSelectModule } from '../swui-menu-select/swui-menu-select.module';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatInputModule } from '@angular/material/input';\n\n\nexport const MULTISELECT_MODULES = [\n  ReactiveFormsModule,\n  MatInputModule,\n  MatMenuModule,\n  SwuiMenuSelectModule,\n];\n\n@NgModule({\n  declarations: [SwuiMultiselectComponent],\n  imports: [\n    CommonModule,\n    TranslateModule.forChild(),\n    ...MULTISELECT_MODULES\n  ],\n  exports: [SwuiMultiselectComponent],\n})\nexport class SwuiMultiselectModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,oBAAoB,QAAQ,6CAA6C;AAClF,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AAGxD,OAAO,MAAMC,mBAAmB,GAAG,CACjCN,mBAAmB,EACnBK,cAAc,EACdD,aAAa,EACbD,oBAAoB,CACrB;AAWM,IAAMI,qBAAqB,GAA3B,MAAMA,qBAAqB,GACjC;AADYA,qBAAqB,GAAAC,UAAA,EATjCV,QAAQ,CAAC;EACRW,YAAY,EAAE,CAACP,wBAAwB,CAAC;EACxCQ,OAAO,EAAE,CACPX,YAAY,EACZE,eAAe,CAACU,QAAQ,EAAE,EAC1B,GAAGL,mBAAmB,CACvB;EACDM,OAAO,EAAE,CAACV,wBAAwB;CACnC,CAAC,C,EACWK,qBAAqB,CACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}