{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _InteractivityChecker, _FocusTrapFactory, _CdkTrapFocus, _LiveAnnouncer, _CdkAriaLive, _HighContrastModeDetector, _A11yModule;\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, afterNextRender, NgZone, Injector, ElementRef, booleanAttribute, Directive, Input, InjectionToken, NgModule } from '@angular/core';\nimport { C as CdkMonitorFocus } from './focus-monitor-e2l_RpN3.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nimport { B as BreakpointObserver } from './breakpoints-observer-CljOfYGy.mjs';\nimport { ContentObserver, ObserversModule } from './observers.mjs';\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  constructor() {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    _defineProperty(this, \"ignoreVisibility\", false);\n  }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n  constructor() {\n    _defineProperty(this, \"_platform\", inject(Platform));\n  }\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n    const frameElement = getFrameElement(getWindow(element));\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && ((config === null || config === void 0 ? void 0 : config.ignoreVisibility) || this.isVisible(element));\n  }\n}\n_InteractivityChecker = InteractivityChecker;\n_defineProperty(InteractivityChecker, \"\\u0275fac\", function _InteractivityChecker_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _InteractivityChecker)();\n});\n_defineProperty(InteractivityChecker, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _InteractivityChecker,\n  factory: _InteractivityChecker.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch (_unused) {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false, /** @breaking-change 20.0.0 param to become required */\n  _injector) {\n    _defineProperty(this, \"_element\", void 0);\n    _defineProperty(this, \"_checker\", void 0);\n    _defineProperty(this, \"_ngZone\", void 0);\n    _defineProperty(this, \"_document\", void 0);\n    _defineProperty(this, \"_injector\", void 0);\n    _defineProperty(this, \"_startAnchor\", void 0);\n    _defineProperty(this, \"_endAnchor\", void 0);\n    _defineProperty(this, \"_hasAttached\", false);\n    // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n    _defineProperty(this, \"startAnchorListener\", () => this.focusLastTabbableElement());\n    _defineProperty(this, \"endAnchorListener\", () => this.focusFirstTabbableElement());\n    _defineProperty(this, \"_enabled\", true);\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._injector = _injector;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild === null || focusableChild === void 0 || focusableChild.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {\n        injector: this._injector\n      });\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n  constructor() {\n    _defineProperty(this, \"_checker\", inject(InteractivityChecker));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n  }\n}\n_FocusTrapFactory = FocusTrapFactory;\n_defineProperty(FocusTrapFactory, \"\\u0275fac\", function _FocusTrapFactory_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FocusTrapFactory)();\n});\n_defineProperty(FocusTrapFactory, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _FocusTrapFactory,\n  factory: _FocusTrapFactory.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    var _this$focusTrap;\n    return ((_this$focusTrap = this.focusTrap) === null || _this$focusTrap === void 0 ? void 0 : _this$focusTrap.enabled) || false;\n  }\n  set enabled(value) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_focusTrapFactory\", inject(FocusTrapFactory));\n    /** Underlying FocusTrap instance. */\n    _defineProperty(this, \"focusTrap\", void 0);\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n    _defineProperty(this, \"_previouslyFocusedElement\", null);\n    _defineProperty(this, \"autoCapture\", void 0);\n    const platform = inject(Platform);\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n  ngOnDestroy() {\n    var _this$focusTrap2;\n    (_this$focusTrap2 = this.focusTrap) === null || _this$focusTrap2 === void 0 || _this$focusTrap2.destroy();\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  ngAfterContentInit() {\n    var _this$focusTrap3;\n    (_this$focusTrap3 = this.focusTrap) === null || _this$focusTrap3 === void 0 || _this$focusTrap3.attachAnchors();\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n  ngOnChanges(changes) {\n    var _this$focusTrap4;\n    const autoCaptureChange = changes['autoCapture'];\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && (_this$focusTrap4 = this.focusTrap) !== null && _this$focusTrap4 !== void 0 && _this$focusTrap4.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n  _captureFocus() {\n    var _this$focusTrap5;\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    (_this$focusTrap5 = this.focusTrap) === null || _this$focusTrap5 === void 0 || _this$focusTrap5.focusInitialElementWhenReady();\n  }\n}\n_CdkTrapFocus = CdkTrapFocus;\n_defineProperty(CdkTrapFocus, \"\\u0275fac\", function _CdkTrapFocus_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkTrapFocus)();\n});\n_defineProperty(CdkTrapFocus, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkTrapFocus,\n  selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n  inputs: {\n    enabled: [2, \"cdkTrapFocus\", \"enabled\", booleanAttribute],\n    autoCapture: [2, \"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute]\n  },\n  exportAs: [\"cdkTrapFocus\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus'\n    }]\n  }], () => [], {\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocus',\n        transform: booleanAttribute\n      }]\n    }],\n    autoCapture: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocusAutoCapture',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n  constructor() {\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_defaultOptions\", inject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n      optional: true\n    }));\n    _defineProperty(this, \"_liveElement\", void 0);\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_previousTimeout\", void 0);\n    _defineProperty(this, \"_currentPromise\", void 0);\n    _defineProperty(this, \"_currentResolve\", void 0);\n    const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, {\n      optional: true\n    });\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n    this.clear();\n    clearTimeout(this._previousTimeout);\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        var _this$_currentResolve;\n        this._liveElement.textContent = message;\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        (_this$_currentResolve = this._currentResolve) === null || _this$_currentResolve === void 0 || _this$_currentResolve.call(this);\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n  ngOnDestroy() {\n    var _this$_liveElement, _this$_currentResolve2;\n    clearTimeout(this._previousTimeout);\n    (_this$_liveElement = this._liveElement) === null || _this$_liveElement === void 0 || _this$_liveElement.remove();\n    this._liveElement = null;\n    (_this$_currentResolve2 = this._currentResolve) === null || _this$_currentResolve2 === void 0 || _this$_currentResolve2.call(this);\n    this._currentPromise = this._currentResolve = undefined;\n  }\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n    this._document.body.appendChild(liveEl);\n    return liveEl;\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  _exposeAnnouncerToModals(id) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n}\n_LiveAnnouncer = LiveAnnouncer;\n_defineProperty(LiveAnnouncer, \"\\u0275fac\", function _LiveAnnouncer_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _LiveAnnouncer)();\n});\n_defineProperty(LiveAnnouncer, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _LiveAnnouncer,\n  factory: _LiveAnnouncer.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n  /** The aria-live politeness level to use when announcing messages. */\n  get politeness() {\n    return this._politeness;\n  }\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_liveAnnouncer\", inject(LiveAnnouncer));\n    _defineProperty(this, \"_contentObserver\", inject(ContentObserver));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_politeness\", 'polite');\n    /** Time in milliseconds after which to clear out the announcer element. */\n    _defineProperty(this, \"duration\", void 0);\n    _defineProperty(this, \"_previousAnnouncedText\", void 0);\n    _defineProperty(this, \"_subscription\", void 0);\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n}\n_CdkAriaLive = CdkAriaLive;\n_defineProperty(CdkAriaLive, \"\\u0275fac\", function _CdkAriaLive_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkAriaLive)();\n});\n_defineProperty(CdkAriaLive, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _CdkAriaLive,\n  selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n  inputs: {\n    politeness: [0, \"cdkAriaLive\", \"politeness\"],\n    duration: [0, \"cdkAriaLiveDuration\", \"duration\"]\n  },\n  exportAs: [\"cdkAriaLive\"]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive'\n    }]\n  }], () => [], {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }],\n    duration: [{\n      type: Input,\n      args: ['cdkAriaLiveDuration']\n    }]\n  });\n})();\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n  HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n  HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n  HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n  constructor() {\n    _defineProperty(this, \"_platform\", inject(Platform));\n    /**\n     * Figuring out the high contrast mode and adding the body classes can cause\n     * some expensive layouts. This flag is used to ensure that we only do it once.\n     */\n    _defineProperty(this, \"_hasCheckedHighContrastMode\", void 0);\n    _defineProperty(this, \"_document\", inject(DOCUMENT));\n    _defineProperty(this, \"_breakpointSubscription\", void 0);\n    this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n      if (this._hasCheckedHighContrastMode) {\n        this._hasCheckedHighContrastMode = false;\n        this._applyBodyHighContrastModeCssClasses();\n      }\n    });\n  }\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n  ngOnDestroy() {\n    this._breakpointSubscription.unsubscribe();\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n}\n_HighContrastModeDetector = HighContrastModeDetector;\n_defineProperty(HighContrastModeDetector, \"\\u0275fac\", function _HighContrastModeDetector_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HighContrastModeDetector)();\n});\n_defineProperty(HighContrastModeDetector, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _HighContrastModeDetector,\n  factory: _HighContrastModeDetector.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass A11yModule {\n  constructor() {\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n}\n_A11yModule = A11yModule;\n_defineProperty(A11yModule, \"\\u0275fac\", function _A11yModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _A11yModule)();\n});\n_defineProperty(A11yModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _A11yModule,\n  imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n  exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n}));\n_defineProperty(A11yModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [ObserversModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], () => [], null);\n})();\nexport { A11yModule as A, CdkTrapFocus as C, FocusTrapFactory as F, HighContrastModeDetector as H, InteractivityChecker as I, LiveAnnouncer as L, FocusTrap as a, HighContrastMode as b, IsFocusableConfig as c, CdkAriaLive as d, LIVE_ANNOUNCER_ELEMENT_TOKEN as e, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY as f, LIVE_ANNOUNCER_DEFAULT_OPTIONS as g };\n//# sourceMappingURL=a11y-module-BYox5gpI.mjs.map", "map": {"version": 3, "names": ["i0", "inject", "Injectable", "afterNextRender", "NgZone", "Injector", "ElementRef", "booleanAttribute", "Directive", "Input", "InjectionToken", "NgModule", "C", "CdkMonitorFocus", "DOCUMENT", "P", "Platform", "c", "_getFocusedElementPierceShadowDom", "_", "_CdkPrivateStyleLoader", "_VisuallyHiddenLoader", "B", "BreakpointObserver", "ContentObserver", "ObserversModule", "IsFocusableConfig", "constructor", "_defineProperty", "InteractivityChecker", "isDisabled", "element", "hasAttribute", "isVisible", "hasGeometry", "getComputedStyle", "visibility", "isTabbable", "_platform", "<PERSON><PERSON><PERSON><PERSON>", "frameElement", "getFrameElement", "getWindow", "getTabIndexValue", "nodeName", "toLowerCase", "tabIndexValue", "WEBKIT", "IOS", "isPotentiallyTabbableIOS", "FIREFOX", "tabIndex", "isFocusable", "config", "isPotentiallyFocusable", "ignoreVisibility", "_InteractivityC<PERSON>cker", "_InteractivityChecker_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "window", "_unused", "offsetWidth", "offsetHeight", "getClientRects", "length", "isNativeFormElement", "isHiddenInput", "isInputElement", "isAnchorWithHref", "isAnchorElement", "hasValidTabIndex", "undefined", "getAttribute", "isNaN", "parseInt", "inputType", "node", "ownerDocument", "defaultView", "FocusTrap", "enabled", "_enabled", "value", "_startAnchor", "_endAnchor", "_toggleAnchorTabIndex", "_element", "_checker", "_ngZone", "_document", "deferAnchors", "_injector", "focusLastTabbableElement", "focusFirstTabbableElement", "attachAnchors", "destroy", "startAnchor", "endAnchor", "removeEventListener", "startAnchorListener", "remove", "endAnchorListener", "_hasAttached", "runOutsideAngular", "_createAnchor", "addEventListener", "parentNode", "insertBefore", "nextS<PERSON>ling", "focusInitialElementWhenReady", "options", "Promise", "resolve", "_executeOnStable", "focusInitialElement", "focusFirstTabbableElementWhenReady", "focusLastTabbableElementWhenReady", "_getRegionBoundary", "bound", "markers", "querySelectorAll", "i", "console", "warn", "_getFirstTabbableElement", "_getLastTabbableElement", "redirectToElement", "querySelector", "focus<PERSON><PERSON><PERSON><PERSON>", "focus", "has<PERSON>tta<PERSON>", "root", "children", "tabbable<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "anchor", "createElement", "classList", "add", "setAttribute", "isEnabled", "removeAttribute", "toggleAnchors", "fn", "injector", "setTimeout", "FocusTrapFactory", "load", "create", "deferCaptureElements", "_FocusTrapFactory", "_FocusTrapFactory_Factory", "CdkTrapFocus", "_this$focusTrap", "focusTrap", "platform", "_focusTrapFactory", "_elementRef", "nativeElement", "ngOnDestroy", "_this$focusTrap2", "_previouslyFocusedElement", "ngAfterContentInit", "_this$focusTrap3", "autoCapture", "_captureFocus", "ngDoCheck", "ngOnChanges", "changes", "_this$focusTrap4", "autoCaptureChange", "firstChange", "_this$focusTrap5", "_CdkTrapFocus", "_CdkTrapFocus_Factory", "ɵɵdefineDirective", "selectors", "inputs", "exportAs", "features", "ɵɵNgOnChangesFeature", "selector", "alias", "transform", "LIVE_ANNOUNCER_ELEMENT_TOKEN", "LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY", "LIVE_ANNOUNCER_DEFAULT_OPTIONS", "uniqueIds", "LiveAnnouncer", "optional", "elementToken", "_liveElement", "_createLiveElement", "announce", "message", "defaultOptions", "_defaultOptions", "politeness", "duration", "clear", "clearTimeout", "_previousTimeout", "id", "_exposeAnnouncerToModals", "_currentPromise", "_currentResolve", "_this$_currentResolve", "textContent", "call", "_this$_liveElement", "_this$_currentResolve2", "elementClass", "previousElements", "getElementsByClassName", "liveEl", "body", "append<PERSON><PERSON><PERSON>", "modals", "modal", "ariaOwns", "indexOf", "_LiveAnnouncer", "_LiveAnnouncer_Factory", "CdkAriaLive", "_politeness", "_subscription", "unsubscribe", "_contentObserver", "observe", "subscribe", "elementText", "_previousAnnouncedText", "_liveAnnouncer", "_CdkAriaLive", "_CdkAriaLive_Factory", "HighContrastMode", "BLACK_ON_WHITE_CSS_CLASS", "WHITE_ON_BLACK_CSS_CLASS", "HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS", "HighContrastModeDetector", "_breakpointSubscription", "_hasCheckedHighContrastMode", "_applyBodyHighContrastModeCssClasses", "getHighContrastMode", "NONE", "testElement", "style", "backgroundColor", "position", "documentWindow", "computedStyle", "computedColor", "replace", "WHITE_ON_BLACK", "BLACK_ON_WHITE", "bodyClasses", "mode", "_HighContrastModeDetector", "_HighContrastModeDetector_Factory", "A11yModule", "_A11yModule", "_A11yModule_Factory", "ɵɵdefineNgModule", "imports", "exports", "ɵɵdefineInjector", "A", "F", "H", "I", "L", "a", "b", "d", "e", "f", "g"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/a11y-module-BYox5gpI.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injectable, afterNextRender, NgZone, Injector, ElementRef, booleanAttribute, Directive, Input, InjectionToken, NgModule } from '@angular/core';\nimport { C as CdkMonitorFocus } from './focus-monitor-e2l_RpN3.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nimport { B as BreakpointObserver } from './breakpoints-observer-CljOfYGy.mjs';\nimport { ContentObserver, ObserversModule } from './observers.mjs';\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    ignoreVisibility = false;\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n    _platform = inject(Platform);\n    constructor() { }\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n    isDisabled(element) {\n        // This does not capture some cases, such as a non-form control with a disabled attribute or\n        // a form control inside of a disabled form, but should capture the most common cases.\n        return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n    isVisible(element) {\n        return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n    isTabbable(element) {\n        // Nothing is tabbable on the server 😎\n        if (!this._platform.isBrowser) {\n            return false;\n        }\n        const frameElement = getFrameElement(getWindow(element));\n        if (frameElement) {\n            // Frame elements inherit their tabindex onto all child elements.\n            if (getTabIndexValue(frameElement) === -1) {\n                return false;\n            }\n            // Browsers disable tabbing to an element inside of an invisible frame.\n            if (!this.isVisible(frameElement)) {\n                return false;\n            }\n        }\n        let nodeName = element.nodeName.toLowerCase();\n        let tabIndexValue = getTabIndexValue(element);\n        if (element.hasAttribute('contenteditable')) {\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'iframe' || nodeName === 'object') {\n            // The frame or object's content may be tabbable depending on the content, but it's\n            // not possibly to reliably detect the content of the frames. We always consider such\n            // elements as non-tabbable.\n            return false;\n        }\n        // In iOS, the browser only considers some specific elements as tabbable.\n        if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n            return false;\n        }\n        if (nodeName === 'audio') {\n            // Audio elements without controls enabled are never tabbable, regardless\n            // of the tabindex attribute explicitly being set.\n            if (!element.hasAttribute('controls')) {\n                return false;\n            }\n            // Audio elements with controls are by default tabbable unless the\n            // tabindex attribute is set to `-1` explicitly.\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'video') {\n            // For all video elements, if the tabindex attribute is set to `-1`, the video\n            // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n            // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n            // tabindex attribute is the source of truth here.\n            if (tabIndexValue === -1) {\n                return false;\n            }\n            // If the tabindex is explicitly set, and not `-1` (as per check before), the\n            // video element is always tabbable (regardless of whether it has controls or not).\n            if (tabIndexValue !== null) {\n                return true;\n            }\n            // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n            // has controls enabled. Firefox is special as videos are always tabbable regardless\n            // of whether there are controls or not.\n            return this._platform.FIREFOX || element.hasAttribute('controls');\n        }\n        return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n    isFocusable(element, config) {\n        // Perform checks in order of left to most expensive.\n        // Again, naive approach that does not capture many edge cases and browser quirks.\n        return (isPotentiallyFocusable(element) &&\n            !this.isDisabled(element) &&\n            (config?.ignoreVisibility || this.isVisible(element)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InteractivityChecker, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InteractivityChecker, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InteractivityChecker, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n    try {\n        return window.frameElement;\n    }\n    catch {\n        return null;\n    }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n    // Use logic from jQuery to check for an invisible element.\n    // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n    return !!(element.offsetWidth ||\n        element.offsetHeight ||\n        (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    return (nodeName === 'input' ||\n        nodeName === 'select' ||\n        nodeName === 'button' ||\n        nodeName === 'textarea');\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n    return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n    return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n    return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n    return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n    if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n        return false;\n    }\n    let tabIndex = element.getAttribute('tabindex');\n    return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n    if (!hasValidTabIndex(element)) {\n        return null;\n    }\n    // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n    const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n    return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    let inputType = nodeName === 'input' && element.type;\n    return (inputType === 'text' ||\n        inputType === 'password' ||\n        nodeName === 'select' ||\n        nodeName === 'textarea');\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n    // Inputs are potentially focusable *unless* they're type=\"hidden\".\n    if (isHiddenInput(element)) {\n        return false;\n    }\n    return (isNativeFormElement(element) ||\n        isAnchorWithHref(element) ||\n        element.hasAttribute('contenteditable') ||\n        hasValidTabIndex(element));\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n    // ownerDocument is null if `node` itself *is* a document.\n    return (node.ownerDocument && node.ownerDocument.defaultView) || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n    _element;\n    _checker;\n    _ngZone;\n    _document;\n    _injector;\n    _startAnchor;\n    _endAnchor;\n    _hasAttached = false;\n    // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n    startAnchorListener = () => this.focusLastTabbableElement();\n    endAnchorListener = () => this.focusFirstTabbableElement();\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(value, this._startAnchor);\n            this._toggleAnchorTabIndex(value, this._endAnchor);\n        }\n    }\n    _enabled = true;\n    constructor(_element, _checker, _ngZone, _document, deferAnchors = false, \n    /** @breaking-change 20.0.0 param to become required */\n    _injector) {\n        this._element = _element;\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._injector = _injector;\n        if (!deferAnchors) {\n            this.attachAnchors();\n        }\n    }\n    /** Destroys the focus trap by cleaning up the anchors. */\n    destroy() {\n        const startAnchor = this._startAnchor;\n        const endAnchor = this._endAnchor;\n        if (startAnchor) {\n            startAnchor.removeEventListener('focus', this.startAnchorListener);\n            startAnchor.remove();\n        }\n        if (endAnchor) {\n            endAnchor.removeEventListener('focus', this.endAnchorListener);\n            endAnchor.remove();\n        }\n        this._startAnchor = this._endAnchor = null;\n        this._hasAttached = false;\n    }\n    /**\n     * Inserts the anchors into the DOM. This is usually done automatically\n     * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n     * @returns Whether the focus trap managed to attach successfully. This may not be the case\n     * if the target element isn't currently in the DOM.\n     */\n    attachAnchors() {\n        // If we're not on the browser, there can be no focus to trap.\n        if (this._hasAttached) {\n            return true;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            if (!this._startAnchor) {\n                this._startAnchor = this._createAnchor();\n                this._startAnchor.addEventListener('focus', this.startAnchorListener);\n            }\n            if (!this._endAnchor) {\n                this._endAnchor = this._createAnchor();\n                this._endAnchor.addEventListener('focus', this.endAnchorListener);\n            }\n        });\n        if (this._element.parentNode) {\n            this._element.parentNode.insertBefore(this._startAnchor, this._element);\n            this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n            this._hasAttached = true;\n        }\n        return this._hasAttached;\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses the first tabbable element.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusInitialElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the first tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusFirstTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the last tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusLastTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n        });\n    }\n    /**\n     * Get the specified boundary element of the trapped region.\n     * @param bound The boundary to get (start or end of trapped region).\n     * @returns The boundary element.\n     */\n    _getRegionBoundary(bound) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            for (let i = 0; i < markers.length; i++) {\n                // @breaking-change 8.0.0\n                if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated ` +\n                        `attribute will be removed in 8.0.0.`, markers[i]);\n                }\n                else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` +\n                        `will be removed in 8.0.0.`, markers[i]);\n                }\n            }\n        }\n        if (bound == 'start') {\n            return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n        }\n        return markers.length\n            ? markers[markers.length - 1]\n            : this._getLastTabbableElement(this._element);\n    }\n    /**\n     * Focuses the element that should be focused when the focus trap is initialized.\n     * @returns Whether focus was moved successfully.\n     */\n    focusInitialElement(options) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n        if (redirectToElement) {\n            // @breaking-change 8.0.0\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n                console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` +\n                    `use 'cdkFocusInitial' instead. The deprecated attribute ` +\n                    `will be removed in 8.0.0`, redirectToElement);\n            }\n            // Warn the consumer if the element they've pointed to\n            // isn't focusable, when not in production mode.\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                !this._checker.isFocusable(redirectToElement)) {\n                console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n            }\n            if (!this._checker.isFocusable(redirectToElement)) {\n                const focusableChild = this._getFirstTabbableElement(redirectToElement);\n                focusableChild?.focus(options);\n                return !!focusableChild;\n            }\n            redirectToElement.focus(options);\n            return true;\n        }\n        return this.focusFirstTabbableElement(options);\n    }\n    /**\n     * Focuses the first tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusFirstTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('start');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Focuses the last tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusLastTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('end');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Checks whether the focus trap has successfully been attached.\n     */\n    hasAttached() {\n        return this._hasAttached;\n    }\n    /** Get the first tabbable element from a DOM subtree (inclusive). */\n    _getFirstTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        const children = root.children;\n        for (let i = 0; i < children.length; i++) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getFirstTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Get the last tabbable element from a DOM subtree (inclusive). */\n    _getLastTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        // Iterate in reverse DOM order.\n        const children = root.children;\n        for (let i = children.length - 1; i >= 0; i--) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getLastTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Creates an anchor element. */\n    _createAnchor() {\n        const anchor = this._document.createElement('div');\n        this._toggleAnchorTabIndex(this._enabled, anchor);\n        anchor.classList.add('cdk-visually-hidden');\n        anchor.classList.add('cdk-focus-trap-anchor');\n        anchor.setAttribute('aria-hidden', 'true');\n        return anchor;\n    }\n    /**\n     * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n     * @param isEnabled Whether the focus trap is enabled.\n     * @param anchor Anchor on which to toggle the tabindex.\n     */\n    _toggleAnchorTabIndex(isEnabled, anchor) {\n        // Remove the tabindex completely, rather than setting it to -1, because if the\n        // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n        isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n    }\n    /**\n     * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n     * @param enabled: Whether the anchors should trap Tab.\n     */\n    toggleAnchors(enabled) {\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(enabled, this._startAnchor);\n            this._toggleAnchorTabIndex(enabled, this._endAnchor);\n        }\n    }\n    /** Executes a function when the zone is stable. */\n    _executeOnStable(fn) {\n        // TODO: remove this conditional when injector is required in the constructor.\n        if (this._injector) {\n            afterNextRender(fn, { injector: this._injector });\n        }\n        else {\n            setTimeout(fn);\n        }\n    }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n    _checker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _document = inject(DOCUMENT);\n    _injector = inject(Injector);\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n    create(element, deferCaptureElements = false) {\n        return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusTrapFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusTrapFactory, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    /** Underlying FocusTrap instance. */\n    focusTrap;\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n    _previouslyFocusedElement = null;\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this.focusTrap?.enabled || false;\n    }\n    set enabled(value) {\n        if (this.focusTrap) {\n            this.focusTrap.enabled = value;\n        }\n    }\n    /**\n     * Whether the directive should automatically move focus into the trapped region upon\n     * initialization and return focus to the previous activeElement upon destruction.\n     */\n    autoCapture;\n    constructor() {\n        const platform = inject(Platform);\n        if (platform.isBrowser) {\n            this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n        }\n    }\n    ngOnDestroy() {\n        this.focusTrap?.destroy();\n        // If we stored a previously focused element when using autoCapture, return focus to that\n        // element now that the trapped region is being destroyed.\n        if (this._previouslyFocusedElement) {\n            this._previouslyFocusedElement.focus();\n            this._previouslyFocusedElement = null;\n        }\n    }\n    ngAfterContentInit() {\n        this.focusTrap?.attachAnchors();\n        if (this.autoCapture) {\n            this._captureFocus();\n        }\n    }\n    ngDoCheck() {\n        if (this.focusTrap && !this.focusTrap.hasAttached()) {\n            this.focusTrap.attachAnchors();\n        }\n    }\n    ngOnChanges(changes) {\n        const autoCaptureChange = changes['autoCapture'];\n        if (autoCaptureChange &&\n            !autoCaptureChange.firstChange &&\n            this.autoCapture &&\n            this.focusTrap?.hasAttached()) {\n            this._captureFocus();\n        }\n    }\n    _captureFocus() {\n        this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n        this.focusTrap?.focusInitialElementWhenReady();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTrapFocus, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkTrapFocus, isStandalone: true, selector: \"[cdkTrapFocus]\", inputs: { enabled: [\"cdkTrapFocus\", \"enabled\", booleanAttribute], autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute] }, exportAs: [\"cdkTrapFocus\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTrapFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTrapFocus]',\n                    exportAs: 'cdkTrapFocus',\n                }]\n        }], ctorParameters: () => [], propDecorators: { enabled: [{\n                type: Input,\n                args: [{ alias: 'cdkTrapFocus', transform: booleanAttribute }]\n            }], autoCapture: [{\n                type: Input,\n                args: [{ alias: 'cdkTrapFocusAutoCapture', transform: booleanAttribute }]\n            }] } });\n\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n    providedIn: 'root',\n    factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n    return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\n\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n    _ngZone = inject(NgZone);\n    _defaultOptions = inject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n        optional: true,\n    });\n    _liveElement;\n    _document = inject(DOCUMENT);\n    _previousTimeout;\n    _currentPromise;\n    _currentResolve;\n    constructor() {\n        const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, { optional: true });\n        this._liveElement = elementToken || this._createLiveElement();\n    }\n    announce(message, ...args) {\n        const defaultOptions = this._defaultOptions;\n        let politeness;\n        let duration;\n        if (args.length === 1 && typeof args[0] === 'number') {\n            duration = args[0];\n        }\n        else {\n            [politeness, duration] = args;\n        }\n        this.clear();\n        clearTimeout(this._previousTimeout);\n        if (!politeness) {\n            politeness =\n                defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n        }\n        if (duration == null && defaultOptions) {\n            duration = defaultOptions.duration;\n        }\n        // TODO: ensure changing the politeness works on all environments we support.\n        this._liveElement.setAttribute('aria-live', politeness);\n        if (this._liveElement.id) {\n            this._exposeAnnouncerToModals(this._liveElement.id);\n        }\n        // This 100ms timeout is necessary for some browser + screen-reader combinations:\n        // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n        // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n        //   second time without clearing and then using a non-zero delay.\n        // (using JAWS 17 at time of this writing).\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._currentPromise) {\n                this._currentPromise = new Promise(resolve => (this._currentResolve = resolve));\n            }\n            clearTimeout(this._previousTimeout);\n            this._previousTimeout = setTimeout(() => {\n                this._liveElement.textContent = message;\n                if (typeof duration === 'number') {\n                    this._previousTimeout = setTimeout(() => this.clear(), duration);\n                }\n                // For some reason in tests this can be undefined\n                // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n                this._currentResolve?.();\n                this._currentPromise = this._currentResolve = undefined;\n            }, 100);\n            return this._currentPromise;\n        });\n    }\n    /**\n     * Clears the current text from the announcer element. Can be used to prevent\n     * screen readers from reading the text out again while the user is going\n     * through the page landmarks.\n     */\n    clear() {\n        if (this._liveElement) {\n            this._liveElement.textContent = '';\n        }\n    }\n    ngOnDestroy() {\n        clearTimeout(this._previousTimeout);\n        this._liveElement?.remove();\n        this._liveElement = null;\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n    }\n    _createLiveElement() {\n        const elementClass = 'cdk-live-announcer-element';\n        const previousElements = this._document.getElementsByClassName(elementClass);\n        const liveEl = this._document.createElement('div');\n        // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n        for (let i = 0; i < previousElements.length; i++) {\n            previousElements[i].remove();\n        }\n        liveEl.classList.add(elementClass);\n        liveEl.classList.add('cdk-visually-hidden');\n        liveEl.setAttribute('aria-atomic', 'true');\n        liveEl.setAttribute('aria-live', 'polite');\n        liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n        this._document.body.appendChild(liveEl);\n        return liveEl;\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live announcer element if there is an\n     * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live announcer element.\n     */\n    _exposeAnnouncerToModals(id) {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `SnakBarContainer` and other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: LiveAnnouncer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: LiveAnnouncer, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: LiveAnnouncer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n    _elementRef = inject(ElementRef);\n    _liveAnnouncer = inject(LiveAnnouncer);\n    _contentObserver = inject(ContentObserver);\n    _ngZone = inject(NgZone);\n    /** The aria-live politeness level to use when announcing messages. */\n    get politeness() {\n        return this._politeness;\n    }\n    set politeness(value) {\n        this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n        if (this._politeness === 'off') {\n            if (this._subscription) {\n                this._subscription.unsubscribe();\n                this._subscription = null;\n            }\n        }\n        else if (!this._subscription) {\n            this._subscription = this._ngZone.runOutsideAngular(() => {\n                return this._contentObserver.observe(this._elementRef).subscribe(() => {\n                    // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n                    const elementText = this._elementRef.nativeElement.textContent;\n                    // The `MutationObserver` fires also for attribute\n                    // changes which we don't want to announce.\n                    if (elementText !== this._previousAnnouncedText) {\n                        this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n                        this._previousAnnouncedText = elementText;\n                    }\n                });\n            });\n        }\n    }\n    _politeness = 'polite';\n    /** Time in milliseconds after which to clear out the announcer element. */\n    duration;\n    _previousAnnouncedText;\n    _subscription;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    }\n    ngOnDestroy() {\n        if (this._subscription) {\n            this._subscription.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAriaLive, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkAriaLive, isStandalone: true, selector: \"[cdkAriaLive]\", inputs: { politeness: [\"cdkAriaLive\", \"politeness\"], duration: [\"cdkAriaLiveDuration\", \"duration\"] }, exportAs: [\"cdkAriaLive\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAriaLive, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAriaLive]',\n                    exportAs: 'cdkAriaLive',\n                }]\n        }], ctorParameters: () => [], propDecorators: { politeness: [{\n                type: Input,\n                args: ['cdkAriaLive']\n            }], duration: [{\n                type: Input,\n                args: ['cdkAriaLiveDuration']\n            }] } });\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n    HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n    HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n    HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n    _platform = inject(Platform);\n    /**\n     * Figuring out the high contrast mode and adding the body classes can cause\n     * some expensive layouts. This flag is used to ensure that we only do it once.\n     */\n    _hasCheckedHighContrastMode;\n    _document = inject(DOCUMENT);\n    _breakpointSubscription;\n    constructor() {\n        this._breakpointSubscription = inject(BreakpointObserver)\n            .observe('(forced-colors: active)')\n            .subscribe(() => {\n            if (this._hasCheckedHighContrastMode) {\n                this._hasCheckedHighContrastMode = false;\n                this._applyBodyHighContrastModeCssClasses();\n            }\n        });\n    }\n    /** Gets the current high-contrast-mode for the page. */\n    getHighContrastMode() {\n        if (!this._platform.isBrowser) {\n            return HighContrastMode.NONE;\n        }\n        // Create a test element with an arbitrary background-color that is neither black nor\n        // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n        // appending the test element to the DOM does not affect layout by absolutely positioning it\n        const testElement = this._document.createElement('div');\n        testElement.style.backgroundColor = 'rgb(1,2,3)';\n        testElement.style.position = 'absolute';\n        this._document.body.appendChild(testElement);\n        // Get the computed style for the background color, collapsing spaces to normalize between\n        // browsers. Once we get this color, we no longer need the test element. Access the `window`\n        // via the document so we can fake it in tests. Note that we have extra null checks, because\n        // this logic will likely run during app bootstrap and throwing can break the entire app.\n        const documentWindow = this._document.defaultView || window;\n        const computedStyle = documentWindow && documentWindow.getComputedStyle\n            ? documentWindow.getComputedStyle(testElement)\n            : null;\n        const computedColor = ((computedStyle && computedStyle.backgroundColor) || '').replace(/ /g, '');\n        testElement.remove();\n        switch (computedColor) {\n            // Pre Windows 11 dark theme.\n            case 'rgb(0,0,0)':\n            // Windows 11 dark themes.\n            case 'rgb(45,50,54)':\n            case 'rgb(32,32,32)':\n                return HighContrastMode.WHITE_ON_BLACK;\n            // Pre Windows 11 light theme.\n            case 'rgb(255,255,255)':\n            // Windows 11 light theme.\n            case 'rgb(255,250,239)':\n                return HighContrastMode.BLACK_ON_WHITE;\n        }\n        return HighContrastMode.NONE;\n    }\n    ngOnDestroy() {\n        this._breakpointSubscription.unsubscribe();\n    }\n    /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n    _applyBodyHighContrastModeCssClasses() {\n        if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n            const bodyClasses = this._document.body.classList;\n            bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            this._hasCheckedHighContrastMode = true;\n            const mode = this.getHighContrastMode();\n            if (mode === HighContrastMode.BLACK_ON_WHITE) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n            }\n            else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: HighContrastModeDetector, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: HighContrastModeDetector, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: HighContrastModeDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nclass A11yModule {\n    constructor() {\n        inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: A11yModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: A11yModule, imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus], exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: A11yModule, imports: [ObserversModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: A11yModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                }]\n        }], ctorParameters: () => [] });\n\nexport { A11yModule as A, CdkTrapFocus as C, FocusTrapFactory as F, HighContrastModeDetector as H, InteractivityChecker as I, LiveAnnouncer as L, FocusTrap as a, HighContrastMode as b, IsFocusableConfig as c, CdkAriaLive as d, LIVE_ANNOUNCER_ELEMENT_TOKEN as e, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY as f, LIVE_ANNOUNCER_DEFAULT_OPTIONS as g };\n//# sourceMappingURL=a11y-module-BYox5gpI.mjs.map\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,eAAe,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,eAAe;AAC/J,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,iCAAiC,QAAQ,2BAA2B;AAClF,SAASC,CAAC,IAAIC,sBAAsB,QAAQ,6BAA6B;AACzE,SAASC,qBAAqB,QAAQ,eAAe;AACrD,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,qCAAqC;AAC7E,SAASC,eAAe,EAAEC,eAAe,QAAQ,iBAAiB;;AAElE;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EAAAC,YAAA;IACpB;AACJ;AACA;IAFIC,eAAA,2BAGmB,KAAK;EAAA;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EAEvBF,WAAWA,CAAA,EAAG;IAAAC,eAAA,oBADF3B,MAAM,CAACe,QAAQ,CAAC;EACZ;EAChB;AACJ;AACA;AACA;AACA;AACA;EACIc,UAAUA,CAACC,OAAO,EAAE;IAChB;IACA;IACA,OAAOA,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACF,OAAO,EAAE;IACf,OAAOG,WAAW,CAACH,OAAO,CAAC,IAAII,gBAAgB,CAACJ,OAAO,CAAC,CAACK,UAAU,KAAK,SAAS;EACrF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAACN,OAAO,EAAE;IAChB;IACA,IAAI,CAAC,IAAI,CAACO,SAAS,CAACC,SAAS,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAMC,YAAY,GAAGC,eAAe,CAACC,SAAS,CAACX,OAAO,CAAC,CAAC;IACxD,IAAIS,YAAY,EAAE;MACd;MACA,IAAIG,gBAAgB,CAACH,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MAChB;MACA;MACA,IAAI,CAAC,IAAI,CAACP,SAAS,CAACO,YAAY,CAAC,EAAE;QAC/B,OAAO,KAAK;MAChB;IACJ;IACA,IAAII,QAAQ,GAAGb,OAAO,CAACa,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC7C,IAAIC,aAAa,GAAGH,gBAAgB,CAACZ,OAAO,CAAC;IAC7C,IAAIA,OAAO,CAACC,YAAY,CAAC,iBAAiB,CAAC,EAAE;MACzC,OAAOc,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MAChD;MACA;MACA;MACA,OAAO,KAAK;IAChB;IACA;IACA,IAAI,IAAI,CAACN,SAAS,CAACS,MAAM,IAAI,IAAI,CAACT,SAAS,CAACU,GAAG,IAAI,CAACC,wBAAwB,CAAClB,OAAO,CAAC,EAAE;MACnF,OAAO,KAAK;IAChB;IACA,IAAIa,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA,IAAI,CAACb,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE;QACnC,OAAO,KAAK;MAChB;MACA;MACA;MACA,OAAOc,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA;MACA;MACA,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;QACtB,OAAO,KAAK;MAChB;MACA;MACA;MACA,IAAIA,aAAa,KAAK,IAAI,EAAE;QACxB,OAAO,IAAI;MACf;MACA;MACA;MACA;MACA,OAAO,IAAI,CAACR,SAAS,CAACY,OAAO,IAAInB,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC;IACrE;IACA,OAAOD,OAAO,CAACoB,QAAQ,IAAI,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACrB,OAAO,EAAEsB,MAAM,EAAE;IACzB;IACA;IACA,OAAQC,sBAAsB,CAACvB,OAAO,CAAC,IACnC,CAAC,IAAI,CAACD,UAAU,CAACC,OAAO,CAAC,KACxB,CAAAsB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,gBAAgB,KAAI,IAAI,CAACtB,SAAS,CAACF,OAAO,CAAC,CAAC;EAC7D;AAGJ;AAACyB,qBAAA,GA7GK3B,oBAAoB;AAAAD,eAAA,CAApBC,oBAAoB,wBAAA4B,8BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA2G6E7B,qBAAoB;AAAA;AAAAD,eAAA,CA3GrHC,oBAAoB,+BA8GuD7B,EAAE,CAAA2D,kBAAA;EAAAC,KAAA,EAFwB/B,qBAAoB;EAAAgC,OAAA,EAApBhC,qBAAoB,CAAAiC,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEnJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiFhE,EAAE,CAAAiE,iBAAA,CAAQpC,oBAAoB,EAAc,CAAC;IAClHqC,IAAI,EAAEhE,UAAU;IAChBiE,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA,SAAStB,eAAeA,CAAC2B,MAAM,EAAE;EAC7B,IAAI;IACA,OAAOA,MAAM,CAAC5B,YAAY;EAC9B,CAAC,CACD,OAAA6B,OAAA,EAAM;IACF,OAAO,IAAI;EACf;AACJ;AACA;AACA,SAASnC,WAAWA,CAACH,OAAO,EAAE;EAC1B;EACA;EACA,OAAO,CAAC,EAAEA,OAAO,CAACuC,WAAW,IACzBvC,OAAO,CAACwC,YAAY,IACnB,OAAOxC,OAAO,CAACyC,cAAc,KAAK,UAAU,IAAIzC,OAAO,CAACyC,cAAc,CAAC,CAAC,CAACC,MAAO,CAAC;AAC1F;AACA;AACA,SAASC,mBAAmBA,CAAC3C,OAAO,EAAE;EAClC,IAAIa,QAAQ,GAAGb,OAAO,CAACa,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,OAAQD,QAAQ,KAAK,OAAO,IACxBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA,SAAS+B,aAAaA,CAAC5C,OAAO,EAAE;EAC5B,OAAO6C,cAAc,CAAC7C,OAAO,CAAC,IAAIA,OAAO,CAACmC,IAAI,IAAI,QAAQ;AAC9D;AACA;AACA,SAASW,gBAAgBA,CAAC9C,OAAO,EAAE;EAC/B,OAAO+C,eAAe,CAAC/C,OAAO,CAAC,IAAIA,OAAO,CAACC,YAAY,CAAC,MAAM,CAAC;AACnE;AACA;AACA,SAAS4C,cAAcA,CAAC7C,OAAO,EAAE;EAC7B,OAAOA,OAAO,CAACa,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,OAAO;AACpD;AACA;AACA,SAASiC,eAAeA,CAAC/C,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACa,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,GAAG;AAChD;AACA;AACA,SAASkC,gBAAgBA,CAAChD,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC,IAAID,OAAO,CAACoB,QAAQ,KAAK6B,SAAS,EAAE;IACrE,OAAO,KAAK;EAChB;EACA,IAAI7B,QAAQ,GAAGpB,OAAO,CAACkD,YAAY,CAAC,UAAU,CAAC;EAC/C,OAAO,CAAC,EAAE9B,QAAQ,IAAI,CAAC+B,KAAK,CAACC,QAAQ,CAAChC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,SAASR,gBAAgBA,CAACZ,OAAO,EAAE;EAC/B,IAAI,CAACgD,gBAAgB,CAAChD,OAAO,CAAC,EAAE;IAC5B,OAAO,IAAI;EACf;EACA;EACA,MAAMoB,QAAQ,GAAGgC,QAAQ,CAACpD,OAAO,CAACkD,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;EACrE,OAAOC,KAAK,CAAC/B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAGA,QAAQ;AAC1C;AACA;AACA,SAASF,wBAAwBA,CAAClB,OAAO,EAAE;EACvC,IAAIa,QAAQ,GAAGb,OAAO,CAACa,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,IAAIuC,SAAS,GAAGxC,QAAQ,KAAK,OAAO,IAAIb,OAAO,CAACmC,IAAI;EACpD,OAAQkB,SAAS,KAAK,MAAM,IACxBA,SAAS,KAAK,UAAU,IACxBxC,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA;AACA;AACA;AACA,SAASU,sBAAsBA,CAACvB,OAAO,EAAE;EACrC;EACA,IAAI4C,aAAa,CAAC5C,OAAO,CAAC,EAAE;IACxB,OAAO,KAAK;EAChB;EACA,OAAQ2C,mBAAmB,CAAC3C,OAAO,CAAC,IAChC8C,gBAAgB,CAAC9C,OAAO,CAAC,IACzBA,OAAO,CAACC,YAAY,CAAC,iBAAiB,CAAC,IACvC+C,gBAAgB,CAAChD,OAAO,CAAC;AACjC;AACA;AACA,SAASW,SAASA,CAAC2C,IAAI,EAAE;EACrB;EACA,OAAQA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACC,aAAa,CAACC,WAAW,IAAKnB,MAAM;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoB,SAAS,CAAC;EAYZ;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGC,KAAK;IACrB,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAACH,KAAK,EAAE,IAAI,CAACC,YAAY,CAAC;MACpD,IAAI,CAACE,qBAAqB,CAACH,KAAK,EAAE,IAAI,CAACE,UAAU,CAAC;IACtD;EACJ;EAEAlE,WAAWA,CAACoE,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,YAAY,GAAG,KAAK,EACxE;EACAC,SAAS,EAAE;IAAAxE,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,uBAlBI,KAAK;IACpB;IAAAA,eAAA,8BACsB,MAAM,IAAI,CAACyE,wBAAwB,CAAC,CAAC;IAAAzE,eAAA,4BACvC,MAAM,IAAI,CAAC0E,yBAAyB,CAAC,CAAC;IAAA1E,eAAA,mBAY/C,IAAI;IAIX,IAAI,CAACmE,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACD,YAAY,EAAE;MACf,IAAI,CAACI,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,MAAMC,WAAW,GAAG,IAAI,CAACb,YAAY;IACrC,MAAMc,SAAS,GAAG,IAAI,CAACb,UAAU;IACjC,IAAIY,WAAW,EAAE;MACbA,WAAW,CAACE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACC,mBAAmB,CAAC;MAClEH,WAAW,CAACI,MAAM,CAAC,CAAC;IACxB;IACA,IAAIH,SAAS,EAAE;MACXA,SAAS,CAACC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACG,iBAAiB,CAAC;MAC9DJ,SAAS,CAACG,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAACjB,YAAY,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI;IAC1C,IAAI,CAACkB,YAAY,GAAG,KAAK;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIR,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAACQ,YAAY,EAAE;MACnB,OAAO,IAAI;IACf;IACA,IAAI,CAACd,OAAO,CAACe,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC,IAAI,CAACpB,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACqB,aAAa,CAAC,CAAC;QACxC,IAAI,CAACrB,YAAY,CAACsB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACN,mBAAmB,CAAC;MACzE;MACA,IAAI,CAAC,IAAI,CAACf,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,IAAI,CAACoB,aAAa,CAAC,CAAC;QACtC,IAAI,CAACpB,UAAU,CAACqB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACJ,iBAAiB,CAAC;MACrE;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACf,QAAQ,CAACoB,UAAU,EAAE;MAC1B,IAAI,CAACpB,QAAQ,CAACoB,UAAU,CAACC,YAAY,CAAC,IAAI,CAACxB,YAAY,EAAE,IAAI,CAACG,QAAQ,CAAC;MACvE,IAAI,CAACA,QAAQ,CAACoB,UAAU,CAACC,YAAY,CAAC,IAAI,CAACvB,UAAU,EAAE,IAAI,CAACE,QAAQ,CAACsB,WAAW,CAAC;MACjF,IAAI,CAACN,YAAY,GAAG,IAAI;IAC5B;IACA,OAAO,IAAI,CAACA,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIO,4BAA4BA,CAACC,OAAO,EAAE;IAClC,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACE,mBAAmB,CAACJ,OAAO,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,kCAAkCA,CAACL,OAAO,EAAE;IACxC,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACnB,yBAAyB,CAACiB,OAAO,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,iCAAiCA,CAACN,OAAO,EAAE;IACvC,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACpB,wBAAwB,CAACkB,OAAO,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIO,kBAAkBA,CAACC,KAAK,EAAE;IACtB;IACA,MAAMC,OAAO,GAAG,IAAI,CAACjC,QAAQ,CAACkC,gBAAgB,CAAC,qBAAqBF,KAAK,KAAK,GAAG,kBAAkBA,KAAK,KAAK,GAAG,cAAcA,KAAK,GAAG,CAAC;IACvI,IAAI,OAAO/D,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,KAAK,IAAIkE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACvD,MAAM,EAAEyD,CAAC,EAAE,EAAE;QACrC;QACA,IAAIF,OAAO,CAACE,CAAC,CAAC,CAAClG,YAAY,CAAC,aAAa+F,KAAK,EAAE,CAAC,EAAE;UAC/CI,OAAO,CAACC,IAAI,CAAC,gDAAgDL,KAAK,KAAK,GACnE,sBAAsBA,KAAK,4BAA4B,GACvD,qCAAqC,EAAEC,OAAO,CAACE,CAAC,CAAC,CAAC;QAC1D,CAAC,MACI,IAAIF,OAAO,CAACE,CAAC,CAAC,CAAClG,YAAY,CAAC,oBAAoB+F,KAAK,EAAE,CAAC,EAAE;UAC3DI,OAAO,CAACC,IAAI,CAAC,uDAAuDL,KAAK,KAAK,GAC1E,sBAAsBA,KAAK,sCAAsC,GACjE,2BAA2B,EAAEC,OAAO,CAACE,CAAC,CAAC,CAAC;QAChD;MACJ;IACJ;IACA,IAAIH,KAAK,IAAI,OAAO,EAAE;MAClB,OAAOC,OAAO,CAACvD,MAAM,GAAGuD,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAACK,wBAAwB,CAAC,IAAI,CAACtC,QAAQ,CAAC;IACrF;IACA,OAAOiC,OAAO,CAACvD,MAAM,GACfuD,OAAO,CAACA,OAAO,CAACvD,MAAM,GAAG,CAAC,CAAC,GAC3B,IAAI,CAAC6D,uBAAuB,CAAC,IAAI,CAACvC,QAAQ,CAAC;EACrD;EACA;AACJ;AACA;AACA;EACI4B,mBAAmBA,CAACJ,OAAO,EAAE;IACzB;IACA,MAAMgB,iBAAiB,GAAG,IAAI,CAACxC,QAAQ,CAACyC,aAAa,CAAC,uBAAuB,GAAG,mBAAmB,CAAC;IACpG,IAAID,iBAAiB,EAAE;MACnB;MACA,IAAI,CAAC,OAAOvE,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9CuE,iBAAiB,CAACvG,YAAY,CAAC,mBAAmB,CAAC,EAAE;QACrDmG,OAAO,CAACC,IAAI,CAAC,yDAAyD,GAClE,0DAA0D,GAC1D,0BAA0B,EAAEG,iBAAiB,CAAC;MACtD;MACA;MACA;MACA,IAAI,CAAC,OAAOvE,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9C,CAAC,IAAI,CAACgC,QAAQ,CAAC5C,WAAW,CAACmF,iBAAiB,CAAC,EAAE;QAC/CJ,OAAO,CAACC,IAAI,CAAC,wDAAwD,EAAEG,iBAAiB,CAAC;MAC7F;MACA,IAAI,CAAC,IAAI,CAACvC,QAAQ,CAAC5C,WAAW,CAACmF,iBAAiB,CAAC,EAAE;QAC/C,MAAME,cAAc,GAAG,IAAI,CAACJ,wBAAwB,CAACE,iBAAiB,CAAC;QACvEE,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEC,KAAK,CAACnB,OAAO,CAAC;QAC9B,OAAO,CAAC,CAACkB,cAAc;MAC3B;MACAF,iBAAiB,CAACG,KAAK,CAACnB,OAAO,CAAC;MAChC,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACjB,yBAAyB,CAACiB,OAAO,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACIjB,yBAAyBA,CAACiB,OAAO,EAAE;IAC/B,MAAMgB,iBAAiB,GAAG,IAAI,CAACT,kBAAkB,CAAC,OAAO,CAAC;IAC1D,IAAIS,iBAAiB,EAAE;MACnBA,iBAAiB,CAACG,KAAK,CAACnB,OAAO,CAAC;IACpC;IACA,OAAO,CAAC,CAACgB,iBAAiB;EAC9B;EACA;AACJ;AACA;AACA;EACIlC,wBAAwBA,CAACkB,OAAO,EAAE;IAC9B,MAAMgB,iBAAiB,GAAG,IAAI,CAACT,kBAAkB,CAAC,KAAK,CAAC;IACxD,IAAIS,iBAAiB,EAAE;MACnBA,iBAAiB,CAACG,KAAK,CAACnB,OAAO,CAAC;IACpC;IACA,OAAO,CAAC,CAACgB,iBAAiB;EAC9B;EACA;AACJ;AACA;EACII,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC5B,YAAY;EAC5B;EACA;EACAsB,wBAAwBA,CAACO,IAAI,EAAE;IAC3B,IAAI,IAAI,CAAC5C,QAAQ,CAAC5C,WAAW,CAACwF,IAAI,CAAC,IAAI,IAAI,CAAC5C,QAAQ,CAAC3D,UAAU,CAACuG,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC9B,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,QAAQ,CAACpE,MAAM,EAAEyD,CAAC,EAAE,EAAE;MACtC,MAAMY,aAAa,GAAGD,QAAQ,CAACX,CAAC,CAAC,CAACa,QAAQ,KAAK,IAAI,CAAC7C,SAAS,CAAC8C,YAAY,GACpE,IAAI,CAACX,wBAAwB,CAACQ,QAAQ,CAACX,CAAC,CAAC,CAAC,GAC1C,IAAI;MACV,IAAIY,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAR,uBAAuBA,CAACM,IAAI,EAAE;IAC1B,IAAI,IAAI,CAAC5C,QAAQ,CAAC5C,WAAW,CAACwF,IAAI,CAAC,IAAI,IAAI,CAAC5C,QAAQ,CAAC3D,UAAU,CAACuG,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC9B,KAAK,IAAIX,CAAC,GAAGW,QAAQ,CAACpE,MAAM,GAAG,CAAC,EAAEyD,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,MAAMY,aAAa,GAAGD,QAAQ,CAACX,CAAC,CAAC,CAACa,QAAQ,KAAK,IAAI,CAAC7C,SAAS,CAAC8C,YAAY,GACpE,IAAI,CAACV,uBAAuB,CAACO,QAAQ,CAACX,CAAC,CAAC,CAAC,GACzC,IAAI;MACV,IAAIY,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA7B,aAAaA,CAAA,EAAG;IACZ,MAAMgC,MAAM,GAAG,IAAI,CAAC/C,SAAS,CAACgD,aAAa,CAAC,KAAK,CAAC;IAClD,IAAI,CAACpD,qBAAqB,CAAC,IAAI,CAACJ,QAAQ,EAAEuD,MAAM,CAAC;IACjDA,MAAM,CAACE,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3CH,MAAM,CAACE,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAC7CH,MAAM,CAACI,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1C,OAAOJ,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACInD,qBAAqBA,CAACwD,SAAS,EAAEL,MAAM,EAAE;IACrC;IACA;IACAK,SAAS,GAAGL,MAAM,CAACI,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,GAAGJ,MAAM,CAACM,eAAe,CAAC,UAAU,CAAC;EACzF;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAAC/D,OAAO,EAAE;IACnB,IAAI,IAAI,CAACG,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAACL,OAAO,EAAE,IAAI,CAACG,YAAY,CAAC;MACtD,IAAI,CAACE,qBAAqB,CAACL,OAAO,EAAE,IAAI,CAACI,UAAU,CAAC;IACxD;EACJ;EACA;EACA6B,gBAAgBA,CAAC+B,EAAE,EAAE;IACjB;IACA,IAAI,IAAI,CAACrD,SAAS,EAAE;MAChBjG,eAAe,CAACsJ,EAAE,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAACtD;MAAU,CAAC,CAAC;IACrD,CAAC,MACI;MACDuD,UAAU,CAACF,EAAE,CAAC;IAClB;EACJ;AACJ;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,CAAC;EAKnBjI,WAAWA,CAAA,EAAG;IAAAC,eAAA,mBAJH3B,MAAM,CAAC4B,oBAAoB,CAAC;IAAAD,eAAA,kBAC7B3B,MAAM,CAACG,MAAM,CAAC;IAAAwB,eAAA,oBACZ3B,MAAM,CAACa,QAAQ,CAAC;IAAAc,eAAA,oBAChB3B,MAAM,CAACI,QAAQ,CAAC;IAExBJ,MAAM,CAACmB,sBAAsB,CAAC,CAACyI,IAAI,CAACxI,qBAAqB,CAAC;EAC9D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyI,MAAMA,CAAC/H,OAAO,EAAEgI,oBAAoB,GAAG,KAAK,EAAE;IAC1C,OAAO,IAAIvE,SAAS,CAACzD,OAAO,EAAE,IAAI,CAACiE,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,SAAS,EAAE6D,oBAAoB,EAAE,IAAI,CAAC3D,SAAS,CAAC;EACpH;AAGJ;AAAC4D,iBAAA,GApBKJ,gBAAgB;AAAAhI,eAAA,CAAhBgI,gBAAgB,wBAAAK,0BAAAvG,iBAAA;EAAA,YAAAA,iBAAA,IAkBiFkG,iBAAgB;AAAA;AAAAhI,eAAA,CAlBjHgI,gBAAgB,+BA7X2D5J,EAAE,CAAA2D,kBAAA;EAAAC,KAAA,EAgZwBgG,iBAAgB;EAAA/F,OAAA,EAAhB+F,iBAAgB,CAAA9F,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE/I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlZiFhE,EAAE,CAAAiE,iBAAA,CAkZQ2F,gBAAgB,EAAc,CAAC;IAC9G1F,IAAI,EAAEhE,UAAU;IAChBiE,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,MAAMmG,YAAY,CAAC;EAOf;EACA,IAAIzE,OAAOA,CAAA,EAAG;IAAA,IAAA0E,eAAA;IACV,OAAO,EAAAA,eAAA,OAAI,CAACC,SAAS,cAAAD,eAAA,uBAAdA,eAAA,CAAgB1E,OAAO,KAAI,KAAK;EAC3C;EACA,IAAIA,OAAOA,CAACE,KAAK,EAAE;IACf,IAAI,IAAI,CAACyE,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC3E,OAAO,GAAGE,KAAK;IAClC;EACJ;EACA;AACJ;AACA;AACA;;EAEIhE,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBApBA3B,MAAM,CAACK,UAAU,CAAC;IAAAsB,eAAA,4BACZ3B,MAAM,CAAC2J,gBAAgB,CAAC;IAC5C;IAAAhI,eAAA;IAEA;IAAAA,eAAA,oCAC4B,IAAI;IAAAA,eAAA;IAgB5B,MAAMyI,QAAQ,GAAGpK,MAAM,CAACe,QAAQ,CAAC;IACjC,IAAIqJ,QAAQ,CAAC9H,SAAS,EAAE;MACpB,IAAI,CAAC6H,SAAS,GAAG,IAAI,CAACE,iBAAiB,CAACR,MAAM,CAAC,IAAI,CAACS,WAAW,CAACC,aAAa,EAAE,IAAI,CAAC;IACxF;EACJ;EACAC,WAAWA,CAAA,EAAG;IAAA,IAAAC,gBAAA;IACV,CAAAA,gBAAA,OAAI,CAACN,SAAS,cAAAM,gBAAA,eAAdA,gBAAA,CAAgBlE,OAAO,CAAC,CAAC;IACzB;IACA;IACA,IAAI,IAAI,CAACmE,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACjC,KAAK,CAAC,CAAC;MACtC,IAAI,CAACiC,yBAAyB,GAAG,IAAI;IACzC;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IAAA,IAAAC,gBAAA;IACjB,CAAAA,gBAAA,OAAI,CAACT,SAAS,cAAAS,gBAAA,eAAdA,gBAAA,CAAgBtE,aAAa,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACuE,WAAW,EAAE;MAClB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACZ,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACzB,WAAW,CAAC,CAAC,EAAE;MACjD,IAAI,CAACyB,SAAS,CAAC7D,aAAa,CAAC,CAAC;IAClC;EACJ;EACA0E,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,gBAAA;IACjB,MAAMC,iBAAiB,GAAGF,OAAO,CAAC,aAAa,CAAC;IAChD,IAAIE,iBAAiB,IACjB,CAACA,iBAAiB,CAACC,WAAW,IAC9B,IAAI,CAACP,WAAW,KAAAK,gBAAA,GAChB,IAAI,CAACf,SAAS,cAAAe,gBAAA,eAAdA,gBAAA,CAAgBxC,WAAW,CAAC,CAAC,EAAE;MAC/B,IAAI,CAACoC,aAAa,CAAC,CAAC;IACxB;EACJ;EACAA,aAAaA,CAAA,EAAG;IAAA,IAAAO,gBAAA;IACZ,IAAI,CAACX,yBAAyB,GAAGzJ,iCAAiC,CAAC,CAAC;IACpE,CAAAoK,gBAAA,OAAI,CAAClB,SAAS,cAAAkB,gBAAA,eAAdA,gBAAA,CAAgBhE,4BAA4B,CAAC,CAAC;EAClD;AAGJ;AAACiE,aAAA,GA9DKrB,YAAY;AAAAtI,eAAA,CAAZsI,YAAY,wBAAAsB,sBAAA9H,iBAAA;EAAA,YAAAA,iBAAA,IA4DqFwG,aAAY;AAAA;AAAAtI,eAAA,CA5D7GsI,YAAY,8BAvZ+DlK,EAAE,CAAAyL,iBAAA;EAAAvH,IAAA,EAodQgG,aAAY;EAAAwB,SAAA;EAAAC,MAAA;IAAAlG,OAAA,iCAAiGlF,gBAAgB;IAAAuK,WAAA,gDAA2DvK,gBAAgB;EAAA;EAAAqL,QAAA;EAAAC,QAAA,GApdlN7L,EAAE,CAAA8L,oBAAA;AAAA;AAsdnF;EAAA,QAAA9H,SAAA,oBAAAA,SAAA,KAtdiFhE,EAAE,CAAAiE,iBAAA,CAsdQiG,YAAY,EAAc,CAAC;IAC1GhG,IAAI,EAAE1D,SAAS;IACf2D,IAAI,EAAE,CAAC;MACC4H,QAAQ,EAAE,gBAAgB;MAC1BH,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEnG,OAAO,EAAE,CAAC;MAClDvB,IAAI,EAAEzD,KAAK;MACX0D,IAAI,EAAE,CAAC;QAAE6H,KAAK,EAAE,cAAc;QAAEC,SAAS,EAAE1L;MAAiB,CAAC;IACjE,CAAC,CAAC;IAAEuK,WAAW,EAAE,CAAC;MACd5G,IAAI,EAAEzD,KAAK;MACX0D,IAAI,EAAE,CAAC;QAAE6H,KAAK,EAAE,yBAAyB;QAAEC,SAAS,EAAE1L;MAAiB,CAAC;IAC5E,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2L,4BAA4B,GAAG,IAAIxL,cAAc,CAAC,sBAAsB,EAAE;EAC5EqD,UAAU,EAAE,MAAM;EAClBF,OAAO,EAAEsI;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,OAAO,IAAI;AACf;AACA;AACA,MAAMC,8BAA8B,GAAG,IAAI1L,cAAc,CAAC,gCAAgC,CAAC;AAE3F,IAAI2L,SAAS,GAAG,CAAC;AACjB,MAAMC,aAAa,CAAC;EAUhB3K,WAAWA,CAAA,EAAG;IAAAC,eAAA,kBATJ3B,MAAM,CAACG,MAAM,CAAC;IAAAwB,eAAA,0BACN3B,MAAM,CAACmM,8BAA8B,EAAE;MACrDG,QAAQ,EAAE;IACd,CAAC,CAAC;IAAA3K,eAAA;IAAAA,eAAA,oBAEU3B,MAAM,CAACa,QAAQ,CAAC;IAAAc,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAKxB,MAAM4K,YAAY,GAAGvM,MAAM,CAACiM,4BAA4B,EAAE;MAAEK,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC7E,IAAI,CAACE,YAAY,GAAGD,YAAY,IAAI,IAAI,CAACE,kBAAkB,CAAC,CAAC;EACjE;EACAC,QAAQA,CAACC,OAAO,EAAE,GAAGzI,IAAI,EAAE;IACvB,MAAM0I,cAAc,GAAG,IAAI,CAACC,eAAe;IAC3C,IAAIC,UAAU;IACd,IAAIC,QAAQ;IACZ,IAAI7I,IAAI,CAACM,MAAM,KAAK,CAAC,IAAI,OAAON,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAClD6I,QAAQ,GAAG7I,IAAI,CAAC,CAAC,CAAC;IACtB,CAAC,MACI;MACD,CAAC4I,UAAU,EAAEC,QAAQ,CAAC,GAAG7I,IAAI;IACjC;IACA,IAAI,CAAC8I,KAAK,CAAC,CAAC;IACZC,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;IACnC,IAAI,CAACJ,UAAU,EAAE;MACbA,UAAU,GACNF,cAAc,IAAIA,cAAc,CAACE,UAAU,GAAGF,cAAc,CAACE,UAAU,GAAG,QAAQ;IAC1F;IACA,IAAIC,QAAQ,IAAI,IAAI,IAAIH,cAAc,EAAE;MACpCG,QAAQ,GAAGH,cAAc,CAACG,QAAQ;IACtC;IACA;IACA,IAAI,CAACP,YAAY,CAACpD,YAAY,CAAC,WAAW,EAAE0D,UAAU,CAAC;IACvD,IAAI,IAAI,CAACN,YAAY,CAACW,EAAE,EAAE;MACtB,IAAI,CAACC,wBAAwB,CAAC,IAAI,CAACZ,YAAY,CAACW,EAAE,CAAC;IACvD;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,IAAI,CAACnH,OAAO,CAACe,iBAAiB,CAAC,MAAM;MACxC,IAAI,CAAC,IAAI,CAACsG,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG,IAAI9F,OAAO,CAACC,OAAO,IAAK,IAAI,CAAC8F,eAAe,GAAG9F,OAAQ,CAAC;MACnF;MACAyF,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAGxD,UAAU,CAAC,MAAM;QAAA,IAAA6D,qBAAA;QACrC,IAAI,CAACf,YAAY,CAACgB,WAAW,GAAGb,OAAO;QACvC,IAAI,OAAOI,QAAQ,KAAK,QAAQ,EAAE;UAC9B,IAAI,CAACG,gBAAgB,GAAGxD,UAAU,CAAC,MAAM,IAAI,CAACsD,KAAK,CAAC,CAAC,EAAED,QAAQ,CAAC;QACpE;QACA;QACA;QACA,CAAAQ,qBAAA,OAAI,CAACD,eAAe,cAAAC,qBAAA,eAApBA,qBAAA,CAAAE,IAAA,KAAuB,CAAC;QACxB,IAAI,CAACJ,eAAe,GAAG,IAAI,CAACC,eAAe,GAAGvI,SAAS;MAC3D,CAAC,EAAE,GAAG,CAAC;MACP,OAAO,IAAI,CAACsI,eAAe;IAC/B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIL,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACR,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACgB,WAAW,GAAG,EAAE;IACtC;EACJ;EACAhD,WAAWA,CAAA,EAAG;IAAA,IAAAkD,kBAAA,EAAAC,sBAAA;IACVV,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;IACnC,CAAAQ,kBAAA,OAAI,CAAClB,YAAY,cAAAkB,kBAAA,eAAjBA,kBAAA,CAAmB9G,MAAM,CAAC,CAAC;IAC3B,IAAI,CAAC4F,YAAY,GAAG,IAAI;IACxB,CAAAmB,sBAAA,OAAI,CAACL,eAAe,cAAAK,sBAAA,eAApBA,sBAAA,CAAAF,IAAA,KAAuB,CAAC;IACxB,IAAI,CAACJ,eAAe,GAAG,IAAI,CAACC,eAAe,GAAGvI,SAAS;EAC3D;EACA0H,kBAAkBA,CAAA,EAAG;IACjB,MAAMmB,YAAY,GAAG,4BAA4B;IACjD,MAAMC,gBAAgB,GAAG,IAAI,CAAC5H,SAAS,CAAC6H,sBAAsB,CAACF,YAAY,CAAC;IAC5E,MAAMG,MAAM,GAAG,IAAI,CAAC9H,SAAS,CAACgD,aAAa,CAAC,KAAK,CAAC;IAClD;IACA,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4F,gBAAgB,CAACrJ,MAAM,EAAEyD,CAAC,EAAE,EAAE;MAC9C4F,gBAAgB,CAAC5F,CAAC,CAAC,CAACrB,MAAM,CAAC,CAAC;IAChC;IACAmH,MAAM,CAAC7E,SAAS,CAACC,GAAG,CAACyE,YAAY,CAAC;IAClCG,MAAM,CAAC7E,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3C4E,MAAM,CAAC3E,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1C2E,MAAM,CAAC3E,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;IAC1C2E,MAAM,CAACZ,EAAE,GAAG,sBAAsBf,SAAS,EAAE,EAAE;IAC/C,IAAI,CAACnG,SAAS,CAAC+H,IAAI,CAACC,WAAW,CAACF,MAAM,CAAC;IACvC,OAAOA,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIX,wBAAwBA,CAACD,EAAE,EAAE;IACzB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMe,MAAM,GAAG,IAAI,CAACjI,SAAS,CAAC+B,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiG,MAAM,CAAC1J,MAAM,EAAEyD,CAAC,EAAE,EAAE;MACpC,MAAMkG,KAAK,GAAGD,MAAM,CAACjG,CAAC,CAAC;MACvB,MAAMmG,QAAQ,GAAGD,KAAK,CAACnJ,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAACoJ,QAAQ,EAAE;QACXD,KAAK,CAAC/E,YAAY,CAAC,WAAW,EAAE+D,EAAE,CAAC;MACvC,CAAC,MACI,IAAIiB,QAAQ,CAACC,OAAO,CAAClB,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClCgB,KAAK,CAAC/E,YAAY,CAAC,WAAW,EAAEgF,QAAQ,GAAG,GAAG,GAAGjB,EAAE,CAAC;MACxD;IACJ;EACJ;AAGJ;AAACmB,cAAA,GAxHKjC,aAAa;AAAA1K,eAAA,CAAb0K,aAAa,wBAAAkC,uBAAA9K,iBAAA;EAAA,YAAAA,iBAAA,IAsHoF4I,cAAa;AAAA;AAAA1K,eAAA,CAtH9G0K,aAAa,+BApf8DtM,EAAE,CAAA2D,kBAAA;EAAAC,KAAA,EA2mBwB0I,cAAa;EAAAzI,OAAA,EAAbyI,cAAa,CAAAxI,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE5I;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7mBiFhE,EAAE,CAAAiE,iBAAA,CA6mBQqI,aAAa,EAAc,CAAC;IAC3GpI,IAAI,EAAEhE,UAAU;IAChBiE,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAM0K,WAAW,CAAC;EAKd;EACA,IAAI1B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC2B,WAAW;EAC3B;EACA,IAAI3B,UAAUA,CAACpH,KAAK,EAAE;IAClB,IAAI,CAAC+I,WAAW,GAAG/I,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,QAAQ;IAC9E,IAAI,IAAI,CAAC+I,WAAW,KAAK,KAAK,EAAE;MAC5B,IAAI,IAAI,CAACC,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAACC,WAAW,CAAC,CAAC;QAChC,IAAI,CAACD,aAAa,GAAG,IAAI;MAC7B;IACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACA,aAAa,EAAE;MAC1B,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC1I,OAAO,CAACe,iBAAiB,CAAC,MAAM;QACtD,OAAO,IAAI,CAAC6H,gBAAgB,CAACC,OAAO,CAAC,IAAI,CAACvE,WAAW,CAAC,CAACwE,SAAS,CAAC,MAAM;UACnE;UACA,MAAMC,WAAW,GAAG,IAAI,CAACzE,WAAW,CAACC,aAAa,CAACiD,WAAW;UAC9D;UACA;UACA,IAAIuB,WAAW,KAAK,IAAI,CAACC,sBAAsB,EAAE;YAC7C,IAAI,CAACC,cAAc,CAACvC,QAAQ,CAACqC,WAAW,EAAE,IAAI,CAACN,WAAW,EAAE,IAAI,CAAC1B,QAAQ,CAAC;YAC1E,IAAI,CAACiC,sBAAsB,GAAGD,WAAW;UAC7C;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EAMArN,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBApCA3B,MAAM,CAACK,UAAU,CAAC;IAAAsB,eAAA,yBACf3B,MAAM,CAACqM,aAAa,CAAC;IAAA1K,eAAA,2BACnB3B,MAAM,CAACuB,eAAe,CAAC;IAAAI,eAAA,kBAChC3B,MAAM,CAACG,MAAM,CAAC;IAAAwB,eAAA,sBA4BV,QAAQ;IACtB;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAKI3B,MAAM,CAACmB,sBAAsB,CAAC,CAACyI,IAAI,CAACxI,qBAAqB,CAAC;EAC9D;EACAoJ,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACkE,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACC,WAAW,CAAC,CAAC;IACpC;EACJ;AAGJ;AAACO,YAAA,GA/CKV,WAAW;AAAA7M,eAAA,CAAX6M,WAAW,wBAAAW,qBAAA1L,iBAAA;EAAA,YAAAA,iBAAA,IA6CsF+K,YAAW;AAAA;AAAA7M,eAAA,CA7C5G6M,WAAW,8BArnBgEzO,EAAE,CAAAyL,iBAAA;EAAAvH,IAAA,EAmqBQuK,YAAW;EAAA/C,SAAA;EAAAC,MAAA;IAAAoB,UAAA;IAAAC,QAAA;EAAA;EAAApB,QAAA;AAAA;AAEtG;EAAA,QAAA5H,SAAA,oBAAAA,SAAA,KArqBiFhE,EAAE,CAAAiE,iBAAA,CAqqBQwK,WAAW,EAAc,CAAC;IACzGvK,IAAI,EAAE1D,SAAS;IACf2D,IAAI,EAAE,CAAC;MACC4H,QAAQ,EAAE,eAAe;MACzBH,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEmB,UAAU,EAAE,CAAC;MACrD7I,IAAI,EAAEzD,KAAK;MACX0D,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAE6I,QAAQ,EAAE,CAAC;MACX9I,IAAI,EAAEzD,KAAK;MACX0D,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAIkL,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAACA,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvDA,gBAAgB,CAACA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EAC3EA,gBAAgB,CAACA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;AAC/E,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA,MAAMC,wBAAwB,GAAG,kCAAkC;AACnE;AACA,MAAMC,wBAAwB,GAAG,kCAAkC;AACnE;AACA,MAAMC,mCAAmC,GAAG,0BAA0B;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAS3B9N,WAAWA,CAAA,EAAG;IAAAC,eAAA,oBARF3B,MAAM,CAACe,QAAQ,CAAC;IAC5B;AACJ;AACA;AACA;IAHIY,eAAA;IAAAA,eAAA,oBAKY3B,MAAM,CAACa,QAAQ,CAAC;IAAAc,eAAA;IAGxB,IAAI,CAAC8N,uBAAuB,GAAGzP,MAAM,CAACsB,kBAAkB,CAAC,CACpDuN,OAAO,CAAC,yBAAyB,CAAC,CAClCC,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACY,2BAA2B,EAAE;QAClC,IAAI,CAACA,2BAA2B,GAAG,KAAK;QACxC,IAAI,CAACC,oCAAoC,CAAC,CAAC;MAC/C;IACJ,CAAC,CAAC;EACN;EACA;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACvN,SAAS,CAACC,SAAS,EAAE;MAC3B,OAAO8M,gBAAgB,CAACS,IAAI;IAChC;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAG,IAAI,CAAC7J,SAAS,CAACgD,aAAa,CAAC,KAAK,CAAC;IACvD6G,WAAW,CAACC,KAAK,CAACC,eAAe,GAAG,YAAY;IAChDF,WAAW,CAACC,KAAK,CAACE,QAAQ,GAAG,UAAU;IACvC,IAAI,CAAChK,SAAS,CAAC+H,IAAI,CAACC,WAAW,CAAC6B,WAAW,CAAC;IAC5C;IACA;IACA;IACA;IACA,MAAMI,cAAc,GAAG,IAAI,CAACjK,SAAS,CAACX,WAAW,IAAInB,MAAM;IAC3D,MAAMgM,aAAa,GAAGD,cAAc,IAAIA,cAAc,CAAChO,gBAAgB,GACjEgO,cAAc,CAAChO,gBAAgB,CAAC4N,WAAW,CAAC,GAC5C,IAAI;IACV,MAAMM,aAAa,GAAG,CAAED,aAAa,IAAIA,aAAa,CAACH,eAAe,IAAK,EAAE,EAAEK,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAChGP,WAAW,CAAClJ,MAAM,CAAC,CAAC;IACpB,QAAQwJ,aAAa;MACjB;MACA,KAAK,YAAY;MACjB;MACA,KAAK,eAAe;MACpB,KAAK,eAAe;QAChB,OAAOhB,gBAAgB,CAACkB,cAAc;MAC1C;MACA,KAAK,kBAAkB;MACvB;MACA,KAAK,kBAAkB;QACnB,OAAOlB,gBAAgB,CAACmB,cAAc;IAC9C;IACA,OAAOnB,gBAAgB,CAACS,IAAI;EAChC;EACArF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiF,uBAAuB,CAACd,WAAW,CAAC,CAAC;EAC9C;EACA;EACAgB,oCAAoCA,CAAA,EAAG;IACnC,IAAI,CAAC,IAAI,CAACD,2BAA2B,IAAI,IAAI,CAACrN,SAAS,CAACC,SAAS,IAAI,IAAI,CAAC2D,SAAS,CAAC+H,IAAI,EAAE;MACtF,MAAMwC,WAAW,GAAG,IAAI,CAACvK,SAAS,CAAC+H,IAAI,CAAC9E,SAAS;MACjDsH,WAAW,CAAC5J,MAAM,CAAC2I,mCAAmC,EAAEF,wBAAwB,EAAEC,wBAAwB,CAAC;MAC3G,IAAI,CAACI,2BAA2B,GAAG,IAAI;MACvC,MAAMe,IAAI,GAAG,IAAI,CAACb,mBAAmB,CAAC,CAAC;MACvC,IAAIa,IAAI,KAAKrB,gBAAgB,CAACmB,cAAc,EAAE;QAC1CC,WAAW,CAACrH,GAAG,CAACoG,mCAAmC,EAAEF,wBAAwB,CAAC;MAClF,CAAC,MACI,IAAIoB,IAAI,KAAKrB,gBAAgB,CAACkB,cAAc,EAAE;QAC/CE,WAAW,CAACrH,GAAG,CAACoG,mCAAmC,EAAED,wBAAwB,CAAC;MAClF;IACJ;EACJ;AAGJ;AAACoB,yBAAA,GA5EKlB,wBAAwB;AAAA7N,eAAA,CAAxB6N,wBAAwB,wBAAAmB,kCAAAlN,iBAAA;EAAA,YAAAA,iBAAA,IA0EyE+L,yBAAwB;AAAA;AAAA7N,eAAA,CA1EzH6N,wBAAwB,+BA3sBmDzP,EAAE,CAAA2D,kBAAA;EAAAC,KAAA,EAsxBwB6L,yBAAwB;EAAA5L,OAAA,EAAxB4L,yBAAwB,CAAA3L,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEvJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxxBiFhE,EAAE,CAAAiE,iBAAA,CAwxBQwL,wBAAwB,EAAc,CAAC;IACtHvL,IAAI,EAAEhE,UAAU;IAChBiE,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAM8M,UAAU,CAAC;EACblP,WAAWA,CAAA,EAAG;IACV1B,MAAM,CAACwP,wBAAwB,CAAC,CAACG,oCAAoC,CAAC,CAAC;EAC3E;AAIJ;AAACkB,WAAA,GAPKD,UAAU;AAAAjP,eAAA,CAAViP,UAAU,wBAAAE,oBAAArN,iBAAA;EAAA,YAAAA,iBAAA,IAIuFmN,WAAU;AAAA;AAAAjP,eAAA,CAJ3GiP,UAAU,8BA7xBiE7Q,EAAE,CAAAgR,gBAAA;EAAA9M,IAAA,EAkyBqB2M,WAAU;EAAAI,OAAA,GAAYxP,eAAe,EAAEgN,WAAW,EAAEvE,YAAY,EAAErJ,eAAe;EAAAqQ,OAAA,GAAazC,WAAW,EAAEvE,YAAY,EAAErJ,eAAe;AAAA;AAAAe,eAAA,CAL1OiP,UAAU,8BA7xBiE7Q,EAAE,CAAAmR,gBAAA;EAAAF,OAAA,GAmyB2CxP,eAAe;AAAA;AAE7I;EAAA,QAAAuC,SAAA,oBAAAA,SAAA,KAryBiFhE,EAAE,CAAAiE,iBAAA,CAqyBQ4M,UAAU,EAAc,CAAC;IACxG3M,IAAI,EAAEvD,QAAQ;IACdwD,IAAI,EAAE,CAAC;MACC8M,OAAO,EAAE,CAACxP,eAAe,EAAEgN,WAAW,EAAEvE,YAAY,EAAErJ,eAAe,CAAC;MACtEqQ,OAAO,EAAE,CAACzC,WAAW,EAAEvE,YAAY,EAAErJ,eAAe;IACxD,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASgQ,UAAU,IAAIO,CAAC,EAAElH,YAAY,IAAItJ,CAAC,EAAEgJ,gBAAgB,IAAIyH,CAAC,EAAE5B,wBAAwB,IAAI6B,CAAC,EAAEzP,oBAAoB,IAAI0P,CAAC,EAAEjF,aAAa,IAAIkF,CAAC,EAAEhM,SAAS,IAAIiM,CAAC,EAAEpC,gBAAgB,IAAIqC,CAAC,EAAEhQ,iBAAiB,IAAIT,CAAC,EAAEwN,WAAW,IAAIkD,CAAC,EAAEzF,4BAA4B,IAAI0F,CAAC,EAAEzF,oCAAoC,IAAI0F,CAAC,EAAEzF,8BAA8B,IAAI0F,CAAC;AACpV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}