{"ast": null, "code": "export { a as MatOptgroup, M as MatOption } from './option-ChV6uQgD.mjs';\nexport { b as <PERSON><PERSON><PERSON><PERSON>, j as <PERSON><PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON>x, g as <PERSON><PERSON>uff<PERSON> } from './form-field-DqPi4knt.mjs';\nexport { c as MAT_SELECT_CONFIG, a as MAT_SELECT_SCROLL_STRATEGY, d as MAT_SELECT_SCROLL_STRATEGY_PROVIDER, b as MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, e as MAT_SELECT_TRIGGER, g as MatSelect, f as MatSelectChange, M as MatSelectModule, h as MatSelectTrigger } from './module-Cbt8Fcmv.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/keycodes';\nimport '@angular/core';\nimport 'rxjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-CJ7seqQH.mjs';\nimport './structural-styles-BQUT6wsL.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport '@angular/cdk/collections';\nimport '@angular/forms';\nimport './error-options-Dm2JJUbF.mjs';\nimport './error-state-Dtb1IHM-.mjs';\nimport './index-DOxJc1m4.mjs';\nimport './index-SYVYjXwK.mjs';\nimport './common-module-WayjW0Pb.mjs';\nimport './pseudo-checkbox-module-CAX2sutq.mjs';\nimport './module-BXZhw7pQ.mjs';\nimport '@angular/cdk/observers';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSelectAnimations = {\n  // Represents\n  // trigger('transformPanelWrap', [\n  //   transition('* => void', query('@transformPanel', [animateChild()], {optional: true})),\n  // ])\n  /**\n   * This animation ensures the select's overlay panel animation (transformPanel) is called when\n   * closing the select.\n   * This is needed due to https://github.com/angular/angular/issues/23302\n   */\n  transformPanelWrap: {\n    type: 7,\n    name: 'transformPanelWrap',\n    definitions: [{\n      type: 1,\n      expr: '* => void',\n      animation: {\n        type: 11,\n        selector: '@transformPanel',\n        animation: [{\n          type: 9,\n          options: null\n        }],\n        options: {\n          optional: true\n        }\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents\n  // trigger('transformPanel', [\n  //   state(\n  //     'void',\n  //     style({\n  //       opacity: 0,\n  //       transform: 'scale(1, 0.8)',\n  //     }),\n  //   ),\n  //   transition(\n  //     'void => showing',\n  //     animate(\n  //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n  //       style({\n  //         opacity: 1,\n  //         transform: 'scale(1, 1)',\n  //       }),\n  //     ),\n  //   ),\n  //   transition('* => void', animate('100ms linear', style({opacity: 0}))),\n  // ])\n  /** This animation transforms the select's overlay panel on and off the page. */\n  transformPanel: {\n    type: 7,\n    name: 'transformPanel',\n    definitions: [{\n      type: 0,\n      name: 'void',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'scale(1, 0.8)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => showing',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 1,\n            transform: 'scale(1, 1)'\n          },\n          offset: null\n        },\n        timings: '120ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '100ms linear'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { matSelectAnimations };\n//# sourceMappingURL=select.mjs.map", "map": {"version": 3, "names": ["a", "MatOptgroup", "M", "MatOption", "b", "<PERSON><PERSON><PERSON><PERSON>", "j", "MatFormField", "c", "MatHint", "<PERSON><PERSON><PERSON><PERSON>", "e", "MatPrefix", "g", "MatSuffix", "MAT_SELECT_CONFIG", "MAT_SELECT_SCROLL_STRATEGY", "d", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY", "MAT_SELECT_TRIGGER", "MatSelect", "f", "MatSelectChange", "MatSelectModule", "h", "MatSelectTrigger", "matSelectAnimations", "transformPanelWrap", "type", "name", "definitions", "expr", "animation", "selector", "options", "optional", "transformPanel", "styles", "opacity", "transform", "offset", "timings"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/select.mjs"], "sourcesContent": ["export { a as MatOptgroup, M as MatOption } from './option-ChV6uQgD.mjs';\nexport { b as <PERSON><PERSON><PERSON><PERSON>, j as <PERSON><PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON>x, g as <PERSON><PERSON>uff<PERSON> } from './form-field-DqPi4knt.mjs';\nexport { c as MAT_SELECT_CONFIG, a as MAT_SELECT_SCROLL_STRATEGY, d as MAT_SELECT_SCROLL_STRATEGY_PROVIDER, b as MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, e as MAT_SELECT_TRIGGER, g as MatSelect, f as MatSelectChange, M as MatSelectModule, h as MatSelectTrigger } from './module-Cbt8Fcmv.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/keycodes';\nimport '@angular/core';\nimport 'rxjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-CJ7seqQH.mjs';\nimport './structural-styles-BQUT6wsL.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport '@angular/cdk/collections';\nimport '@angular/forms';\nimport './error-options-Dm2JJUbF.mjs';\nimport './error-state-Dtb1IHM-.mjs';\nimport './index-DOxJc1m4.mjs';\nimport './index-SYVYjXwK.mjs';\nimport './common-module-WayjW0Pb.mjs';\nimport './pseudo-checkbox-module-CAX2sutq.mjs';\nimport './module-BXZhw7pQ.mjs';\nimport '@angular/cdk/observers';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSelectAnimations = {\n    // Represents\n    // trigger('transformPanelWrap', [\n    //   transition('* => void', query('@transformPanel', [animateChild()], {optional: true})),\n    // ])\n    /**\n     * This animation ensures the select's overlay panel animation (transformPanel) is called when\n     * closing the select.\n     * This is needed due to https://github.com/angular/angular/issues/23302\n     */\n    transformPanelWrap: {\n        type: 7,\n        name: 'transformPanelWrap',\n        definitions: [\n            {\n                type: 1,\n                expr: '* => void',\n                animation: {\n                    type: 11,\n                    selector: '@transformPanel',\n                    animation: [{ type: 9, options: null }],\n                    options: { optional: true },\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n    // Represents\n    // trigger('transformPanel', [\n    //   state(\n    //     'void',\n    //     style({\n    //       opacity: 0,\n    //       transform: 'scale(1, 0.8)',\n    //     }),\n    //   ),\n    //   transition(\n    //     'void => showing',\n    //     animate(\n    //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n    //       style({\n    //         opacity: 1,\n    //         transform: 'scale(1, 1)',\n    //       }),\n    //     ),\n    //   ),\n    //   transition('* => void', animate('100ms linear', style({opacity: 0}))),\n    // ])\n    /** This animation transforms the select's overlay panel on and off the page. */\n    transformPanel: {\n        type: 7,\n        name: 'transformPanel',\n        definitions: [\n            {\n                type: 0,\n                name: 'void',\n                styles: {\n                    type: 6,\n                    styles: { opacity: 0, transform: 'scale(1, 0.8)' },\n                    offset: null,\n                },\n            },\n            {\n                type: 1,\n                expr: 'void => showing',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 6,\n                        styles: { opacity: 1, transform: 'scale(1, 1)' },\n                        offset: null,\n                    },\n                    timings: '120ms cubic-bezier(0, 0, 0.2, 1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => void',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                    timings: '100ms linear',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { matSelectAnimations };\n//# sourceMappingURL=select.mjs.map\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,QAAQ,uBAAuB;AACxE,SAASC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,OAAO,EAAEP,CAAC,IAAIQ,QAAQ,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,SAAS,QAAQ,2BAA2B;AACzI,SAASN,CAAC,IAAIO,iBAAiB,EAAEf,CAAC,IAAIgB,0BAA0B,EAAEC,CAAC,IAAIC,mCAAmC,EAAEd,CAAC,IAAIe,2CAA2C,EAAER,CAAC,IAAIS,kBAAkB,EAAEP,CAAC,IAAIQ,SAAS,EAAEC,CAAC,IAAIC,eAAe,EAAErB,CAAC,IAAIsB,eAAe,EAAEC,CAAC,IAAIC,gBAAgB,QAAQ,uBAAuB;AACvS,OAAO,mBAAmB;AAC1B,OAAO,uBAAuB;AAC9B,OAAO,eAAe;AACtB,OAAO,MAAM;AACb,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,sBAAsB;AAC7B,OAAO,gCAAgC;AACvC,OAAO,kCAAkC;AACzC,OAAO,mBAAmB;AAC1B,OAAO,iBAAiB;AACxB,OAAO,gBAAgB;AACvB,OAAO,gCAAgC;AACvC,OAAO,sBAAsB;AAC7B,OAAO,wBAAwB;AAC/B,OAAO,0BAA0B;AACjC,OAAO,gBAAgB;AACvB,OAAO,8BAA8B;AACrC,OAAO,4BAA4B;AACnC,OAAO,sBAAsB;AAC7B,OAAO,sBAAsB;AAC7B,OAAO,8BAA8B;AACrC,OAAO,uCAAuC;AAC9C,OAAO,uBAAuB;AAC9B,OAAO,wBAAwB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG;EACxB;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;EACIC,kBAAkB,EAAE;IAChBC,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE,CACT;MACIF,IAAI,EAAE,CAAC;MACPG,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE;QACPJ,IAAI,EAAE,EAAE;QACRK,QAAQ,EAAE,iBAAiB;QAC3BD,SAAS,EAAE,CAAC;UAAEJ,IAAI,EAAE,CAAC;UAAEM,OAAO,EAAE;QAAK,CAAC,CAAC;QACvCA,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAK;MAC9B,CAAC;MACDD,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAE,cAAc,EAAE;IACZR,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE,gBAAgB;IACtBC,WAAW,EAAE,CACT;MACIF,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,MAAM;MACZQ,MAAM,EAAE;QACJT,IAAI,EAAE,CAAC;QACPS,MAAM,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAgB,CAAC;QAClDC,MAAM,EAAE;MACZ;IACJ,CAAC,EACD;MACIZ,IAAI,EAAE,CAAC;MACPG,IAAI,EAAE,iBAAiB;MACvBC,SAAS,EAAE;QACPJ,IAAI,EAAE,CAAC;QACPS,MAAM,EAAE;UACJT,IAAI,EAAE,CAAC;UACPS,MAAM,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAc,CAAC;UAChDC,MAAM,EAAE;QACZ,CAAC;QACDC,OAAO,EAAE;MACb,CAAC;MACDP,OAAO,EAAE;IACb,CAAC,EACD;MACIN,IAAI,EAAE,CAAC;MACPG,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE;QACPJ,IAAI,EAAE,CAAC;QACPS,MAAM,EAAE;UAAET,IAAI,EAAE,CAAC;UAAES,MAAM,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAC;UAAEE,MAAM,EAAE;QAAK,CAAC;QACzDC,OAAO,EAAE;MACb,CAAC;MACDP,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAASR,mBAAmB;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}