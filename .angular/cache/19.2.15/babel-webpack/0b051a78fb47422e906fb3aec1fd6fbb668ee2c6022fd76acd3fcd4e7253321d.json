{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatCheckbox, _MatCheckboxRequiredValidator, _MatCheckboxRequiredValidatorModule2, _MatCheckboxModule;\nconst _c0 = [\"input\"];\nconst _c1 = [\"label\"];\nconst _c2 = [\"*\"];\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, inject, ElementRef, ChangeDetectorRef, NgZone, ANIMATION_MODULE_TYPE, EventEmitter, HostAttributeToken, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ViewChild, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _MatInternalFormField } from './internal-form-field-grv62mCZ.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n  providedIn: 'root',\n  factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    clickAction: 'check-indeterminate',\n    disabledInteractive: false\n  };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState;\n(function (TransitionCheckState) {\n  /** The initial state of the component before any user interaction. */\n  TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n  /** The state representing the component when it's becoming checked. */\n  TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n  /** The state representing the component when it's becoming unchecked. */\n  TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n  /** The state representing the component when it's becoming indeterminate. */\n  TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n})(TransitionCheckState || (TransitionCheckState = {}));\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatCheckbox),\n  multi: true\n};\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {\n  constructor() {\n    /** The source checkbox of the event. */\n    _defineProperty(this, \"source\", void 0);\n    /** The new `checked` value of the checkbox. */\n    _defineProperty(this, \"checked\", void 0);\n  }\n}\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nclass MatCheckbox {\n  /** Focuses the checkbox. */\n  focus() {\n    this._inputElement.nativeElement.focus();\n  }\n  /** Creates the change event that will be emitted by the checkbox. */\n  _createChangeEvent(isChecked) {\n    const event = new MatCheckboxChange();\n    event.source = this;\n    event.checked = isChecked;\n    return event;\n  }\n  /** Gets the element on which to add the animation CSS classes. */\n  _getAnimationTargetElement() {\n    var _this$_inputElement;\n    return (_this$_inputElement = this._inputElement) === null || _this$_inputElement === void 0 ? void 0 : _this$_inputElement.nativeElement;\n  }\n  /** CSS classes to add when transitioning between the different checkbox states. */\n\n  /** Returns the unique id for the visual hidden input. */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  /** Whether the checkbox is required. */\n\n  constructor() {\n    var _this$_options$disabl, _this$_options;\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_animationMode\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }));\n    _defineProperty(this, \"_options\", inject(MAT_CHECKBOX_DEFAULT_OPTIONS, {\n      optional: true\n    }));\n    _defineProperty(this, \"_animationClasses\", {\n      uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n      uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n      checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n      checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n      indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n      indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked'\n    });\n    /**\n     * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n     * take precedence so this may be omitted.\n     */\n    _defineProperty(this, \"ariaLabel\", '');\n    /**\n     * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n     */\n    _defineProperty(this, \"ariaLabelledby\", null);\n    /** The 'aria-describedby' attribute is read after the element's label and field type. */\n    _defineProperty(this, \"ariaDescribedby\", void 0);\n    /**\n     * Users can specify the `aria-expanded` attribute which will be forwarded to the input element\n     */\n    _defineProperty(this, \"ariaExpanded\", void 0);\n    /**\n     * Users can specify the `aria-controls` attribute which will be forwarded to the input element\n     */\n    _defineProperty(this, \"ariaControls\", void 0);\n    /** Users can specify the `aria-owns` attribute which will be forwarded to the input element */\n    _defineProperty(this, \"ariaOwns\", void 0);\n    _defineProperty(this, \"_uniqueId\", void 0);\n    /** A unique id for the checkbox input. If none is supplied, it will be auto-generated. */\n    _defineProperty(this, \"id\", void 0);\n    _defineProperty(this, \"required\", void 0);\n    /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n    _defineProperty(this, \"labelPosition\", 'after');\n    /** Name value will be applied to the input element if present */\n    _defineProperty(this, \"name\", null);\n    /** Event emitted when the checkbox's `checked` value changes. */\n    _defineProperty(this, \"change\", new EventEmitter());\n    /** Event emitted when the checkbox's `indeterminate` value changes. */\n    _defineProperty(this, \"indeterminateChange\", new EventEmitter());\n    /** The value attribute of the native input element */\n    _defineProperty(this, \"value\", void 0);\n    /** Whether the checkbox has a ripple. */\n    _defineProperty(this, \"disableRipple\", void 0);\n    /** The native `<input type=\"checkbox\">` element */\n    _defineProperty(this, \"_inputElement\", void 0);\n    /** The native `<label>` element */\n    _defineProperty(this, \"_labelElement\", void 0);\n    /** Tabindex for the checkbox. */\n    _defineProperty(this, \"tabIndex\", void 0);\n    // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n    // the lack of type checking previously and assigning random strings.\n    /**\n     * Theme color of the checkbox. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/checkbox/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    _defineProperty(this, \"color\", void 0);\n    /** Whether the checkbox should remain interactive when it is disabled. */\n    _defineProperty(this, \"disabledInteractive\", void 0);\n    /**\n     * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n     * @docs-private\n     */\n    _defineProperty(this, \"_onTouched\", () => {});\n    _defineProperty(this, \"_currentAnimationClass\", '');\n    _defineProperty(this, \"_currentCheckState\", TransitionCheckState.Init);\n    _defineProperty(this, \"_controlValueAccessorChangeFn\", () => {});\n    _defineProperty(this, \"_validatorChangeFn\", () => {});\n    _defineProperty(this, \"_checked\", false);\n    _defineProperty(this, \"_disabled\", false);\n    _defineProperty(this, \"_indeterminate\", false);\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    this.id = this._uniqueId = inject(_IdGenerator).getId('mat-mdc-checkbox-');\n    this.disabledInteractive = (_this$_options$disabl = (_this$_options = this._options) === null || _this$_options === void 0 ? void 0 : _this$_options.disabledInteractive) !== null && _this$_options$disabl !== void 0 ? _this$_options$disabl : false;\n  }\n  ngOnChanges(changes) {\n    if (changes['required']) {\n      this._validatorChangeFn();\n    }\n  }\n  ngAfterViewInit() {\n    this._syncIndeterminate(this._indeterminate);\n  }\n  /** Whether the checkbox is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    if (value != this.checked) {\n      this._checked = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Whether the checkbox is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value !== this.disabled) {\n      this._disabled = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n   * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n   * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n   * set to false.\n   */\n  get indeterminate() {\n    return this._indeterminate;\n  }\n  set indeterminate(value) {\n    const changed = value != this._indeterminate;\n    this._indeterminate = value;\n    if (changed) {\n      if (this._indeterminate) {\n        this._transitionCheckState(TransitionCheckState.Indeterminate);\n      } else {\n        this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      }\n      this.indeterminateChange.emit(this._indeterminate);\n    }\n    this._syncIndeterminate(this._indeterminate);\n  }\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  /** Method being called whenever the label text changes. */\n  _onLabelTextChange() {\n    // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n    // component will be only marked for check, but no actual change detection runs automatically.\n    // Instead of going back into the zone in order to trigger a change detection which causes\n    // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n    // an explicit change detection for the checkbox view and its children.\n    this._changeDetectorRef.detectChanges();\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    this.checked = !!value;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  // Implemented as a part of Validator.\n  validate(control) {\n    return this.required && control.value !== true ? {\n      'required': true\n    } : null;\n  }\n  // Implemented as a part of Validator.\n  registerOnValidatorChange(fn) {\n    this._validatorChangeFn = fn;\n  }\n  _transitionCheckState(newState) {\n    let oldState = this._currentCheckState;\n    let element = this._getAnimationTargetElement();\n    if (oldState === newState || !element) {\n      return;\n    }\n    if (this._currentAnimationClass) {\n      element.classList.remove(this._currentAnimationClass);\n    }\n    this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n    this._currentCheckState = newState;\n    if (this._currentAnimationClass.length > 0) {\n      element.classList.add(this._currentAnimationClass);\n      // Remove the animation class to avoid animation when the checkbox is moved between containers\n      const animationClass = this._currentAnimationClass;\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          element.classList.remove(animationClass);\n        }, 1000);\n      });\n    }\n  }\n  _emitChangeEvent() {\n    this._controlValueAccessorChangeFn(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n    // Assigning the value again here is redundant, but we have to do it in case it was\n    // changed inside the `change` listener which will cause the input to be out of sync.\n    if (this._inputElement) {\n      this._inputElement.nativeElement.checked = this.checked;\n    }\n  }\n  /** Toggles the `checked` state of the checkbox. */\n  toggle() {\n    this.checked = !this.checked;\n    this._controlValueAccessorChangeFn(this.checked);\n  }\n  _handleInputClick() {\n    var _this$_options2;\n    const clickAction = (_this$_options2 = this._options) === null || _this$_options2 === void 0 ? void 0 : _this$_options2.clickAction;\n    // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n    if (!this.disabled && clickAction !== 'noop') {\n      // When user manually click on the checkbox, `indeterminate` is set to false.\n      if (this.indeterminate && clickAction !== 'check') {\n        Promise.resolve().then(() => {\n          this._indeterminate = false;\n          this.indeterminateChange.emit(this._indeterminate);\n        });\n      }\n      this._checked = !this._checked;\n      this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      // Emit our custom change event if the native input emitted one.\n      // It is important to only emit it, if the native input triggered one, because\n      // we don't want to trigger a change event, when the `checked` variable changes for example.\n      this._emitChangeEvent();\n    } else if (this.disabled && this.disabledInteractive || !this.disabled && clickAction === 'noop') {\n      // Reset native input when clicked with noop. The native checkbox becomes checked after\n      // click, reset it to be align with `checked` value of `mat-checkbox`.\n      this._inputElement.nativeElement.checked = this.checked;\n      this._inputElement.nativeElement.indeterminate = this.indeterminate;\n    }\n  }\n  _onInteractionEvent(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n  }\n  _onBlur() {\n    // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n    // Angular does not expect events to be raised during change detection, so any state change\n    // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n    // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n    // telling the form control it has been touched until the next tick.\n    Promise.resolve().then(() => {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  _getAnimationClassForCheckStateTransition(oldState, newState) {\n    // Don't transition if animations are disabled.\n    if (this._animationMode === 'NoopAnimations') {\n      return '';\n    }\n    switch (oldState) {\n      case TransitionCheckState.Init:\n        // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n        // [checked] bound to it.\n        if (newState === TransitionCheckState.Checked) {\n          return this._animationClasses.uncheckedToChecked;\n        } else if (newState == TransitionCheckState.Indeterminate) {\n          return this._checked ? this._animationClasses.checkedToIndeterminate : this._animationClasses.uncheckedToIndeterminate;\n        }\n        break;\n      case TransitionCheckState.Unchecked:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.uncheckedToChecked : this._animationClasses.uncheckedToIndeterminate;\n      case TransitionCheckState.Checked:\n        return newState === TransitionCheckState.Unchecked ? this._animationClasses.checkedToUnchecked : this._animationClasses.checkedToIndeterminate;\n      case TransitionCheckState.Indeterminate:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.indeterminateToChecked : this._animationClasses.indeterminateToUnchecked;\n    }\n    return '';\n  }\n  /**\n   * Syncs the indeterminate value with the checkbox DOM node.\n   *\n   * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n   * property is supported on an element boils down to `if (propName in element)`. Domino's\n   * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n   * server-side rendering.\n   */\n  _syncIndeterminate(value) {\n    const nativeCheckbox = this._inputElement;\n    if (nativeCheckbox) {\n      nativeCheckbox.nativeElement.indeterminate = value;\n    }\n  }\n  _onInputClick() {\n    this._handleInputClick();\n  }\n  _onTouchTargetClick() {\n    this._handleInputClick();\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n  /**\n   *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n   *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n   *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n   *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n   *  bubbles when the label is clicked.\n   */\n  _preventBubblingFromLabel(event) {\n    if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n      event.stopPropagation();\n    }\n  }\n}\n_MatCheckbox = MatCheckbox;\n_defineProperty(MatCheckbox, \"\\u0275fac\", function _MatCheckbox_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCheckbox)();\n});\n_defineProperty(MatCheckbox, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatCheckbox,\n  selectors: [[\"mat-checkbox\"]],\n  viewQuery: function _MatCheckbox_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelElement = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-checkbox\"],\n  hostVars: 16,\n  hostBindings: function _MatCheckbox_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id);\n      i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null);\n      i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"mat-accent\");\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mdc-checkbox--disabled\", ctx.disabled)(\"mat-mdc-checkbox-disabled\", ctx.disabled)(\"mat-mdc-checkbox-checked\", ctx.checked)(\"mat-mdc-checkbox-disabled-interactive\", ctx.disabledInteractive);\n    }\n  },\n  inputs: {\n    ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n    ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n    ariaExpanded: [2, \"aria-expanded\", \"ariaExpanded\", booleanAttribute],\n    ariaControls: [0, \"aria-controls\", \"ariaControls\"],\n    ariaOwns: [0, \"aria-owns\", \"ariaOwns\"],\n    id: \"id\",\n    required: [2, \"required\", \"required\", booleanAttribute],\n    labelPosition: \"labelPosition\",\n    name: \"name\",\n    value: \"value\",\n    disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n    tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? undefined : numberAttribute(value)],\n    color: \"color\",\n    disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute],\n    checked: [2, \"checked\", \"checked\", booleanAttribute],\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n    indeterminate: [2, \"indeterminate\", \"indeterminate\", booleanAttribute]\n  },\n  outputs: {\n    change: \"change\",\n    indeterminateChange: \"indeterminateChange\"\n  },\n  exportAs: [\"matCheckbox\"],\n  features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, {\n    provide: NG_VALIDATORS,\n    useExisting: _MatCheckbox,\n    multi: true\n  }]), i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c2,\n  decls: 15,\n  vars: 23,\n  consts: [[\"checkbox\", \"\"], [\"input\", \"\"], [\"label\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"click\", \"labelPosition\"], [1, \"mdc-checkbox\"], [1, \"mat-mdc-checkbox-touch-target\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"mdc-checkbox__native-control\", 3, \"blur\", \"click\", \"change\", \"checked\", \"indeterminate\", \"disabled\", \"id\", \"required\", \"tabIndex\"], [1, \"mdc-checkbox__ripple\"], [1, \"mdc-checkbox__background\"], [\"focusable\", \"false\", \"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-checkbox__checkmark\"], [\"fill\", \"none\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-checkbox__checkmark-path\"], [1, \"mdc-checkbox__mixedmark\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-checkbox-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-label\", 3, \"for\"]],\n  template: function _MatCheckbox_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 3);\n      i0.ɵɵlistener(\"click\", function _MatCheckbox_Template_div_click_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._preventBubblingFromLabel($event));\n      });\n      i0.ɵɵelementStart(1, \"div\", 4, 0)(3, \"div\", 5);\n      i0.ɵɵlistener(\"click\", function _MatCheckbox_Template_div_click_3_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._onTouchTargetClick());\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"input\", 6, 1);\n      i0.ɵɵlistener(\"blur\", function _MatCheckbox_Template_input_blur_4_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._onBlur());\n      })(\"click\", function _MatCheckbox_Template_input_click_4_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._onInputClick());\n      })(\"change\", function _MatCheckbox_Template_input_change_4_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._onInteractionEvent($event));\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(6, \"div\", 7);\n      i0.ɵɵelementStart(7, \"div\", 8);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(8, \"svg\", 9);\n      i0.ɵɵelement(9, \"path\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelement(10, \"div\", 11);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(11, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"label\", 13, 2);\n      i0.ɵɵprojection(14);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      const checkbox_r2 = i0.ɵɵreference(2);\n      i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n      i0.ɵɵadvance(4);\n      i0.ɵɵclassProp(\"mdc-checkbox--selected\", ctx.checked);\n      i0.ɵɵproperty(\"checked\", ctx.checked)(\"indeterminate\", ctx.indeterminate)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"id\", ctx.inputId)(\"required\", ctx.required)(\"tabIndex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex);\n      i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-checked\", ctx.indeterminate ? \"mixed\" : null)(\"aria-controls\", ctx.ariaControls)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? true : null)(\"aria-expanded\", ctx.ariaExpanded)(\"aria-owns\", ctx.ariaOwns)(\"name\", ctx.name)(\"value\", ctx.value);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"matRippleTrigger\", checkbox_r2)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"for\", ctx.inputId);\n    }\n  },\n  dependencies: [MatRipple, _MatInternalFormField],\n  styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mdc-checkbox-state-layer-size, 40px);height:var(--mdc-checkbox-state-layer-size, 40px);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mdc-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckbox, [{\n    type: Component,\n    args: [{\n      selector: 'mat-checkbox',\n      host: {\n        'class': 'mat-mdc-checkbox',\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n        '[class.mdc-checkbox--disabled]': 'disabled',\n        '[id]': 'id',\n        // Add classes that users can use to more easily target disabled or checked checkboxes.\n        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n        '[class.mat-mdc-checkbox-checked]': 'checked',\n        '[class.mat-mdc-checkbox-disabled-interactive]': 'disabledInteractive',\n        '[class]': 'color ? \"mat-\" + color : \"mat-accent\"'\n      },\n      providers: [MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, {\n        provide: NG_VALIDATORS,\n        useExisting: MatCheckbox,\n        multi: true\n      }],\n      exportAs: 'matCheckbox',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatRipple, _MatInternalFormField],\n      template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.aria-controls]=\\\"ariaControls\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? true : null\\\"\\n           [attr.aria-expanded]=\\\"ariaExpanded\\\"\\n           [attr.aria-owns]=\\\"ariaOwns\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\" #label [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mdc-checkbox-state-layer-size, 40px);height:var(--mdc-checkbox-state-layer-size, 40px);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mdc-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"]\n    }]\n  }], () => [], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    ariaExpanded: [{\n      type: Input,\n      args: [{\n        alias: 'aria-expanded',\n        transform: booleanAttribute\n      }]\n    }],\n    ariaControls: [{\n      type: Input,\n      args: ['aria-controls']\n    }],\n    ariaOwns: [{\n      type: Input,\n      args: ['aria-owns']\n    }],\n    id: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    indeterminateChange: [{\n      type: Output\n    }],\n    value: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    _labelElement: [{\n      type: ViewChild,\n      args: ['label']\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? undefined : numberAttribute(value)\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    indeterminate: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n  multi: true\n};\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass MatCheckboxRequiredValidator extends CheckboxRequiredValidator {}\n_MatCheckboxRequiredValidator = MatCheckboxRequiredValidator;\n_defineProperty(MatCheckboxRequiredValidator, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatCheckboxRequiredValidator_BaseFactory;\n  return function _MatCheckboxRequiredValidator_Factory(__ngFactoryType__) {\n    return (ɵ_MatCheckboxRequiredValidator_BaseFactory || (ɵ_MatCheckboxRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(_MatCheckboxRequiredValidator)))(__ngFactoryType__ || _MatCheckboxRequiredValidator);\n  };\n})());\n_defineProperty(MatCheckboxRequiredValidator, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatCheckboxRequiredValidator,\n  selectors: [[\"mat-checkbox\", \"required\", \"\", \"formControlName\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"formControl\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"ngModel\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxRequiredValidator, [{\n    type: Directive,\n    args: [{\n      selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n      providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR]\n    }]\n  }], null, null);\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass _MatCheckboxRequiredValidatorModule {}\n_MatCheckboxRequiredValidatorModule2 = _MatCheckboxRequiredValidatorModule;\n_defineProperty(_MatCheckboxRequiredValidatorModule, \"\\u0275fac\", function _MatCheckboxRequiredValidatorModule2_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCheckboxRequiredValidatorModule2)();\n});\n_defineProperty(_MatCheckboxRequiredValidatorModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatCheckboxRequiredValidatorModule2,\n  imports: [MatCheckboxRequiredValidator],\n  exports: [MatCheckboxRequiredValidator]\n}));\n_defineProperty(_MatCheckboxRequiredValidatorModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatCheckboxRequiredValidatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCheckboxRequiredValidator],\n      exports: [MatCheckboxRequiredValidator]\n    }]\n  }], null, null);\n})();\nclass MatCheckboxModule {}\n_MatCheckboxModule = MatCheckboxModule;\n_defineProperty(MatCheckboxModule, \"\\u0275fac\", function _MatCheckboxModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatCheckboxModule)();\n});\n_defineProperty(MatCheckboxModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatCheckboxModule,\n  imports: [MatCheckbox, MatCommonModule],\n  exports: [MatCheckbox, MatCommonModule]\n}));\n_defineProperty(MatCheckboxModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCheckbox, MatCommonModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCheckbox, MatCommonModule],\n      exports: [MatCheckbox, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MAT_CHECKBOX_REQUIRED_VALIDATOR, MatCheckbox, MatCheckboxChange, MatCheckboxModule, MatCheckboxRequiredValidator, TransitionCheckState, _MatCheckboxRequiredValidatorModule };", "map": {"version": 3, "names": ["_IdGenerator", "i0", "InjectionToken", "forwardRef", "inject", "ElementRef", "ChangeDetectorRef", "NgZone", "ANIMATION_MODULE_TYPE", "EventEmitter", "HostAttributeToken", "booleanAttribute", "numberAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Output", "ViewChild", "Directive", "NgModule", "NG_VALUE_ACCESSOR", "NG_VALIDATORS", "CheckboxRequiredValidator", "_CdkPrivateStyleLoader", "_", "_MatInternalFormField", "_StructuralStylesLoader", "M", "<PERSON><PERSON><PERSON><PERSON>", "MatCommonModule", "MAT_CHECKBOX_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY", "color", "clickAction", "disabledInteractive", "TransitionCheckState", "MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR", "provide", "useExisting", "MatCheckbox", "multi", "MatCheckboxChange", "constructor", "_defineProperty", "defaults", "focus", "_inputElement", "nativeElement", "_createChangeEvent", "isChecked", "event", "source", "checked", "_getAnimationTargetElement", "_this$_inputElement", "inputId", "id", "_uniqueId", "_this$_options$disabl", "_this$_options", "optional", "uncheckedToChecked", "uncheckedToIndeterminate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "checkedToIndeterminate", "indeterminateToChecked", "indeterminate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Init", "load", "tabIndex", "_options", "parseInt", "getId", "ngOnChanges", "changes", "_validatorChangeFn", "ngAfterViewInit", "_syncIndeterminate", "_indeterminate", "_checked", "value", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "_disabled", "indeterminate", "changed", "_transitionCheckState", "Indeterminate", "Checked", "Unchecked", "indeterminateChange", "emit", "_isRippleDisabled", "disable<PERSON><PERSON><PERSON>", "_onLabelTextChange", "detectChanges", "writeValue", "registerOnChange", "fn", "_controlValueAccessorChangeFn", "registerOnTouched", "_onTouched", "setDisabledState", "isDisabled", "validate", "control", "required", "registerOnValidatorChange", "newState", "oldState", "_currentCheckState", "element", "_currentAnimationClass", "classList", "remove", "_getAnimationClassForCheckStateTransition", "length", "add", "animationClass", "_ngZone", "runOutsideAngular", "setTimeout", "_emitChangeEvent", "change", "toggle", "_handleInputClick", "_this$_options2", "Promise", "resolve", "then", "_onInteractionEvent", "stopPropagation", "_onBlur", "_animationMode", "_animationClasses", "nativeCheckbox", "_onInputClick", "_onTouchTargetClick", "_preventBubblingFromLabel", "target", "_labelElement", "contains", "_MatCheckbox", "_MatCheckbox_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "_MatCheckbox_Query", "rf", "ctx", "ɵɵviewQuery", "_c0", "_c1", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "_MatCheckbox_HostBindings", "ɵɵhostProperty", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "inputs", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "ariaExpanded", "ariaControls", "ariaOwns", "labelPosition", "name", "undefined", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "ɵɵNgOnChangesFeature", "ngContentSelectors", "_c2", "decls", "vars", "consts", "template", "_MatCheckbox_Template", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵlistener", "_MatCheckbox_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "_MatCheckbox_Template_div_click_3_listener", "ɵɵelementEnd", "_MatCheckbox_Template_input_blur_4_listener", "_MatCheckbox_Template_input_click_4_listener", "_MatCheckbox_Template_input_change_4_listener", "ɵɵelement", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵprojection", "checkbox_r2", "ɵɵreference", "ɵɵproperty", "ɵɵadvance", "dependencies", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "providers", "None", "OnPush", "imports", "alias", "transform", "MAT_CHECKBOX_REQUIRED_VALIDATOR", "MatCheckboxRequiredValidator", "_MatCheckboxRequiredValidator", "ɵ_MatCheckboxRequiredValidator_BaseFactory", "_MatCheckboxRequiredValidator_Factory", "ɵɵgetInheritedFactory", "ɵɵdefineDirective", "ɵɵInheritDefinitionFeature", "_MatCheckboxRequiredValidatorModule", "_MatCheckboxRequiredValidatorModule2", "_MatCheckboxRequiredValidatorModule2_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "MatCheckboxModule", "_MatCheckboxModule", "_MatCheckboxModule_Factory"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/checkbox.mjs"], "sourcesContent": ["import { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, inject, ElementRef, ChangeDetectorRef, NgZone, ANIMATION_MODULE_TYPE, EventEmitter, HostAttributeToken, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ViewChild, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _MatInternalFormField } from './internal-form-field-grv62mCZ.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatRipple } from './ripple-BT3tzh6F.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n    providedIn: 'root',\n    factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        color: 'accent',\n        clickAction: 'check-indeterminate',\n        disabledInteractive: false,\n    };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState;\n(function (TransitionCheckState) {\n    /** The initial state of the component before any user interaction. */\n    TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n    /** The state representing the component when it's becoming checked. */\n    TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n    /** The state representing the component when it's becoming unchecked. */\n    TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n    /** The state representing the component when it's becoming indeterminate. */\n    TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n})(TransitionCheckState || (TransitionCheckState = {}));\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatCheckbox),\n    multi: true,\n};\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {\n    /** The source checkbox of the event. */\n    source;\n    /** The new `checked` value of the checkbox. */\n    checked;\n}\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nclass MatCheckbox {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _ngZone = inject(NgZone);\n    _animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n    _options = inject(MAT_CHECKBOX_DEFAULT_OPTIONS, {\n        optional: true,\n    });\n    /** Focuses the checkbox. */\n    focus() {\n        this._inputElement.nativeElement.focus();\n    }\n    /** Creates the change event that will be emitted by the checkbox. */\n    _createChangeEvent(isChecked) {\n        const event = new MatCheckboxChange();\n        event.source = this;\n        event.checked = isChecked;\n        return event;\n    }\n    /** Gets the element on which to add the animation CSS classes. */\n    _getAnimationTargetElement() {\n        return this._inputElement?.nativeElement;\n    }\n    /** CSS classes to add when transitioning between the different checkbox states. */\n    _animationClasses = {\n        uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n        uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n        checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n        checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n        indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n        indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked',\n    };\n    /**\n     * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n     * take precedence so this may be omitted.\n     */\n    ariaLabel = '';\n    /**\n     * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n     */\n    ariaLabelledby = null;\n    /** The 'aria-describedby' attribute is read after the element's label and field type. */\n    ariaDescribedby;\n    /**\n     * Users can specify the `aria-expanded` attribute which will be forwarded to the input element\n     */\n    ariaExpanded;\n    /**\n     * Users can specify the `aria-controls` attribute which will be forwarded to the input element\n     */\n    ariaControls;\n    /** Users can specify the `aria-owns` attribute which will be forwarded to the input element */\n    ariaOwns;\n    _uniqueId;\n    /** A unique id for the checkbox input. If none is supplied, it will be auto-generated. */\n    id;\n    /** Returns the unique id for the visual hidden input. */\n    get inputId() {\n        return `${this.id || this._uniqueId}-input`;\n    }\n    /** Whether the checkbox is required. */\n    required;\n    /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n    labelPosition = 'after';\n    /** Name value will be applied to the input element if present */\n    name = null;\n    /** Event emitted when the checkbox's `checked` value changes. */\n    change = new EventEmitter();\n    /** Event emitted when the checkbox's `indeterminate` value changes. */\n    indeterminateChange = new EventEmitter();\n    /** The value attribute of the native input element */\n    value;\n    /** Whether the checkbox has a ripple. */\n    disableRipple;\n    /** The native `<input type=\"checkbox\">` element */\n    _inputElement;\n    /** The native `<label>` element */\n    _labelElement;\n    /** Tabindex for the checkbox. */\n    tabIndex;\n    // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n    // the lack of type checking previously and assigning random strings.\n    /**\n     * Theme color of the checkbox. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/checkbox/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Whether the checkbox should remain interactive when it is disabled. */\n    disabledInteractive;\n    /**\n     * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n     * @docs-private\n     */\n    _onTouched = () => { };\n    _currentAnimationClass = '';\n    _currentCheckState = TransitionCheckState.Init;\n    _controlValueAccessorChangeFn = () => { };\n    _validatorChangeFn = () => { };\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const tabIndex = inject(new HostAttributeToken('tabindex'), { optional: true });\n        this._options = this._options || defaults;\n        this.color = this._options.color || defaults.color;\n        this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n        this.id = this._uniqueId = inject(_IdGenerator).getId('mat-mdc-checkbox-');\n        this.disabledInteractive = this._options?.disabledInteractive ?? false;\n    }\n    ngOnChanges(changes) {\n        if (changes['required']) {\n            this._validatorChangeFn();\n        }\n    }\n    ngAfterViewInit() {\n        this._syncIndeterminate(this._indeterminate);\n    }\n    /** Whether the checkbox is checked. */\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        if (value != this.checked) {\n            this._checked = value;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _checked = false;\n    /** Whether the checkbox is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        if (value !== this.disabled) {\n            this._disabled = value;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _disabled = false;\n    /**\n     * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n     * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n     * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n     * set to false.\n     */\n    get indeterminate() {\n        return this._indeterminate;\n    }\n    set indeterminate(value) {\n        const changed = value != this._indeterminate;\n        this._indeterminate = value;\n        if (changed) {\n            if (this._indeterminate) {\n                this._transitionCheckState(TransitionCheckState.Indeterminate);\n            }\n            else {\n                this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n            }\n            this.indeterminateChange.emit(this._indeterminate);\n        }\n        this._syncIndeterminate(this._indeterminate);\n    }\n    _indeterminate = false;\n    _isRippleDisabled() {\n        return this.disableRipple || this.disabled;\n    }\n    /** Method being called whenever the label text changes. */\n    _onLabelTextChange() {\n        // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n        // component will be only marked for check, but no actual change detection runs automatically.\n        // Instead of going back into the zone in order to trigger a change detection which causes\n        // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n        // an explicit change detection for the checkbox view and its children.\n        this._changeDetectorRef.detectChanges();\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        this.checked = !!value;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._controlValueAccessorChangeFn = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    // Implemented as a part of Validator.\n    validate(control) {\n        return this.required && control.value !== true ? { 'required': true } : null;\n    }\n    // Implemented as a part of Validator.\n    registerOnValidatorChange(fn) {\n        this._validatorChangeFn = fn;\n    }\n    _transitionCheckState(newState) {\n        let oldState = this._currentCheckState;\n        let element = this._getAnimationTargetElement();\n        if (oldState === newState || !element) {\n            return;\n        }\n        if (this._currentAnimationClass) {\n            element.classList.remove(this._currentAnimationClass);\n        }\n        this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n        this._currentCheckState = newState;\n        if (this._currentAnimationClass.length > 0) {\n            element.classList.add(this._currentAnimationClass);\n            // Remove the animation class to avoid animation when the checkbox is moved between containers\n            const animationClass = this._currentAnimationClass;\n            this._ngZone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    element.classList.remove(animationClass);\n                }, 1000);\n            });\n        }\n    }\n    _emitChangeEvent() {\n        this._controlValueAccessorChangeFn(this.checked);\n        this.change.emit(this._createChangeEvent(this.checked));\n        // Assigning the value again here is redundant, but we have to do it in case it was\n        // changed inside the `change` listener which will cause the input to be out of sync.\n        if (this._inputElement) {\n            this._inputElement.nativeElement.checked = this.checked;\n        }\n    }\n    /** Toggles the `checked` state of the checkbox. */\n    toggle() {\n        this.checked = !this.checked;\n        this._controlValueAccessorChangeFn(this.checked);\n    }\n    _handleInputClick() {\n        const clickAction = this._options?.clickAction;\n        // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n        if (!this.disabled && clickAction !== 'noop') {\n            // When user manually click on the checkbox, `indeterminate` is set to false.\n            if (this.indeterminate && clickAction !== 'check') {\n                Promise.resolve().then(() => {\n                    this._indeterminate = false;\n                    this.indeterminateChange.emit(this._indeterminate);\n                });\n            }\n            this._checked = !this._checked;\n            this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n            // Emit our custom change event if the native input emitted one.\n            // It is important to only emit it, if the native input triggered one, because\n            // we don't want to trigger a change event, when the `checked` variable changes for example.\n            this._emitChangeEvent();\n        }\n        else if ((this.disabled && this.disabledInteractive) ||\n            (!this.disabled && clickAction === 'noop')) {\n            // Reset native input when clicked with noop. The native checkbox becomes checked after\n            // click, reset it to be align with `checked` value of `mat-checkbox`.\n            this._inputElement.nativeElement.checked = this.checked;\n            this._inputElement.nativeElement.indeterminate = this.indeterminate;\n        }\n    }\n    _onInteractionEvent(event) {\n        // We always have to stop propagation on the change event.\n        // Otherwise the change event, from the input element, will bubble up and\n        // emit its event object to the `change` output.\n        event.stopPropagation();\n    }\n    _onBlur() {\n        // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n        // Angular does not expect events to be raised during change detection, so any state change\n        // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n        // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n        // telling the form control it has been touched until the next tick.\n        Promise.resolve().then(() => {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    _getAnimationClassForCheckStateTransition(oldState, newState) {\n        // Don't transition if animations are disabled.\n        if (this._animationMode === 'NoopAnimations') {\n            return '';\n        }\n        switch (oldState) {\n            case TransitionCheckState.Init:\n                // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n                // [checked] bound to it.\n                if (newState === TransitionCheckState.Checked) {\n                    return this._animationClasses.uncheckedToChecked;\n                }\n                else if (newState == TransitionCheckState.Indeterminate) {\n                    return this._checked\n                        ? this._animationClasses.checkedToIndeterminate\n                        : this._animationClasses.uncheckedToIndeterminate;\n                }\n                break;\n            case TransitionCheckState.Unchecked:\n                return newState === TransitionCheckState.Checked\n                    ? this._animationClasses.uncheckedToChecked\n                    : this._animationClasses.uncheckedToIndeterminate;\n            case TransitionCheckState.Checked:\n                return newState === TransitionCheckState.Unchecked\n                    ? this._animationClasses.checkedToUnchecked\n                    : this._animationClasses.checkedToIndeterminate;\n            case TransitionCheckState.Indeterminate:\n                return newState === TransitionCheckState.Checked\n                    ? this._animationClasses.indeterminateToChecked\n                    : this._animationClasses.indeterminateToUnchecked;\n        }\n        return '';\n    }\n    /**\n     * Syncs the indeterminate value with the checkbox DOM node.\n     *\n     * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n     * property is supported on an element boils down to `if (propName in element)`. Domino's\n     * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n     * server-side rendering.\n     */\n    _syncIndeterminate(value) {\n        const nativeCheckbox = this._inputElement;\n        if (nativeCheckbox) {\n            nativeCheckbox.nativeElement.indeterminate = value;\n        }\n    }\n    _onInputClick() {\n        this._handleInputClick();\n    }\n    _onTouchTargetClick() {\n        this._handleInputClick();\n        if (!this.disabled) {\n            // Normally the input should be focused already, but if the click\n            // comes from the touch target, then we might have to focus it ourselves.\n            this._inputElement.nativeElement.focus();\n        }\n    }\n    /**\n     *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n     *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n     *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n     *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n     *  bubbles when the label is clicked.\n     */\n    _preventBubblingFromLabel(event) {\n        if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n            event.stopPropagation();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCheckbox, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatCheckbox, isStandalone: true, selector: \"mat-checkbox\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], ariaExpanded: [\"aria-expanded\", \"ariaExpanded\", booleanAttribute], ariaControls: [\"aria-controls\", \"ariaControls\"], ariaOwns: [\"aria-owns\", \"ariaOwns\"], id: \"id\", required: [\"required\", \"required\", booleanAttribute], labelPosition: \"labelPosition\", name: \"name\", value: \"value\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? undefined : numberAttribute(value))], color: \"color\", disabledInteractive: [\"disabledInteractive\", \"disabledInteractive\", booleanAttribute], checked: [\"checked\", \"checked\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], indeterminate: [\"indeterminate\", \"indeterminate\", booleanAttribute] }, outputs: { change: \"change\", indeterminateChange: \"indeterminateChange\" }, host: { properties: { \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"class._mat-animation-noopable\": \"_animationMode === 'NoopAnimations'\", \"class.mdc-checkbox--disabled\": \"disabled\", \"id\": \"id\", \"class.mat-mdc-checkbox-disabled\": \"disabled\", \"class.mat-mdc-checkbox-checked\": \"checked\", \"class.mat-mdc-checkbox-disabled-interactive\": \"disabledInteractive\", \"class\": \"color ? \\\"mat-\\\" + color : \\\"mat-accent\\\"\" }, classAttribute: \"mat-mdc-checkbox\" }, providers: [\n            MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR,\n            {\n                provide: NG_VALIDATORS,\n                useExisting: MatCheckbox,\n                multi: true,\n            },\n        ], viewQueries: [{ propertyName: \"_inputElement\", first: true, predicate: [\"input\"], descendants: true }, { propertyName: \"_labelElement\", first: true, predicate: [\"label\"], descendants: true }], exportAs: [\"matCheckbox\"], usesOnChanges: true, ngImport: i0, template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.aria-controls]=\\\"ariaControls\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? true : null\\\"\\n           [attr.aria-expanded]=\\\"ariaExpanded\\\"\\n           [attr.aria-owns]=\\\"ariaOwns\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\" #label [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mdc-checkbox-state-layer-size, 40px);height:var(--mdc-checkbox-state-layer-size, 40px);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mdc-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"component\", type: _MatInternalFormField, selector: \"div[mat-internal-form-field]\", inputs: [\"labelPosition\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCheckbox, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-checkbox', host: {\n                        'class': 'mat-mdc-checkbox',\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n                        '[class.mdc-checkbox--disabled]': 'disabled',\n                        '[id]': 'id',\n                        // Add classes that users can use to more easily target disabled or checked checkboxes.\n                        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n                        '[class.mat-mdc-checkbox-checked]': 'checked',\n                        '[class.mat-mdc-checkbox-disabled-interactive]': 'disabledInteractive',\n                        '[class]': 'color ? \"mat-\" + color : \"mat-accent\"',\n                    }, providers: [\n                        MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR,\n                        {\n                            provide: NG_VALIDATORS,\n                            useExisting: MatCheckbox,\n                            multi: true,\n                        },\n                    ], exportAs: 'matCheckbox', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatRipple, _MatInternalFormField], template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.aria-controls]=\\\"ariaControls\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? true : null\\\"\\n           [attr.aria-expanded]=\\\"ariaExpanded\\\"\\n           [attr.aria-owns]=\\\"ariaOwns\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\" #label [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mdc-checkbox-state-layer-size, 40px);height:var(--mdc-checkbox-state-layer-size, 40px);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mdc-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], ariaExpanded: [{\n                type: Input,\n                args: [{ alias: 'aria-expanded', transform: booleanAttribute }]\n            }], ariaControls: [{\n                type: Input,\n                args: ['aria-controls']\n            }], ariaOwns: [{\n                type: Input,\n                args: ['aria-owns']\n            }], id: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], labelPosition: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], indeterminateChange: [{\n                type: Output\n            }], value: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], _inputElement: [{\n                type: ViewChild,\n                args: ['input']\n            }], _labelElement: [{\n                type: ViewChild,\n                args: ['label']\n            }], tabIndex: [{\n                type: Input,\n                args: [{ transform: (value) => (value == null ? undefined : numberAttribute(value)) }]\n            }], color: [{\n                type: Input\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], checked: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], indeterminate: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_REQUIRED_VALIDATOR = {\n    provide: NG_VALIDATORS,\n    useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n    multi: true,\n};\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass MatCheckboxRequiredValidator extends CheckboxRequiredValidator {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCheckboxRequiredValidator, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatCheckboxRequiredValidator, isStandalone: true, selector: \"mat-checkbox[required][formControlName],\\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]\", providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCheckboxRequiredValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n                    providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR],\n                }]\n        }] });\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass _MatCheckboxRequiredValidatorModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, imports: [MatCheckboxRequiredValidator], exports: [MatCheckboxRequiredValidator] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCheckboxRequiredValidator],\n                    exports: [MatCheckboxRequiredValidator],\n                }]\n        }] });\nclass MatCheckboxModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCheckboxModule, imports: [MatCheckbox, MatCommonModule], exports: [MatCheckbox, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCheckboxModule, imports: [MatCheckbox, MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCheckbox, MatCommonModule],\n                    exports: [MatCheckbox, MatCommonModule],\n                }]\n        }] });\n\nexport { MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MAT_CHECKBOX_REQUIRED_VALIDATOR, MatCheckbox, MatCheckboxChange, MatCheckboxModule, MatCheckboxRequiredValidator, TransitionCheckState, _MatCheckboxRequiredValidatorModule };\n"], "mappings": ";;;;;AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC3S,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,yBAAyB,QAAQ,gBAAgB;AAC5F,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,qBAAqB,QAAQ,oCAAoC;AAC/E,SAASD,CAAC,IAAIE,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,SAAS,QAAQ,uBAAuB;AACtD,SAASD,CAAC,IAAIE,eAAe,QAAQ,8BAA8B;AACnE,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,mBAAmB;;AAE1B;AACA,MAAMC,4BAA4B,GAAG,IAAI7B,cAAc,CAAC,8BAA8B,EAAE;EACpF8B,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,OAAO;IACHC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,qBAAqB;IAClCC,mBAAmB,EAAE;EACzB,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7B;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC/D;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACrE;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzE;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;AACrF,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,GAAG;EACxCC,OAAO,EAAEnB,iBAAiB;EAC1BoB,WAAW,EAAEtC,UAAU,CAAC,MAAMuC,WAAW,CAAC;EAC1CC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,iBAAiB,CAAC;EAAAC,YAAA;IACpB;IAAAC,eAAA;IAEA;IAAAA,eAAA;EAAA;AAEJ;AACA;AACA,MAAMC,QAAQ,GAAGb,oCAAoC,CAAC,CAAC;AACvD,MAAMQ,WAAW,CAAC;EAQd;EACAM,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,aAAa,CAACC,aAAa,CAACF,KAAK,CAAC,CAAC;EAC5C;EACA;EACAG,kBAAkBA,CAACC,SAAS,EAAE;IAC1B,MAAMC,KAAK,GAAG,IAAIT,iBAAiB,CAAC,CAAC;IACrCS,KAAK,CAACC,MAAM,GAAG,IAAI;IACnBD,KAAK,CAACE,OAAO,GAAGH,SAAS;IACzB,OAAOC,KAAK;EAChB;EACA;EACAG,0BAA0BA,CAAA,EAAG;IAAA,IAAAC,mBAAA;IACzB,QAAAA,mBAAA,GAAO,IAAI,CAACR,aAAa,cAAAQ,mBAAA,uBAAlBA,mBAAA,CAAoBP,aAAa;EAC5C;EACA;;EAiCA;EACA,IAAIQ,OAAOA,CAAA,EAAG;IACV,OAAO,GAAG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACC,SAAS,QAAQ;EAC/C;EACA;;EAyCAf,WAAWA,CAAA,EAAG;IAAA,IAAAgB,qBAAA,EAAAC,cAAA;IAAAhB,eAAA,sBApGA1C,MAAM,CAACC,UAAU,CAAC;IAAAyC,eAAA,6BACX1C,MAAM,CAACE,iBAAiB,CAAC;IAAAwC,eAAA,kBACpC1C,MAAM,CAACG,MAAM,CAAC;IAAAuC,eAAA,yBACP1C,MAAM,CAACI,qBAAqB,EAAE;MAAEuD,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAjB,eAAA,mBACvD1C,MAAM,CAAC2B,4BAA4B,EAAE;MAC5CgC,QAAQ,EAAE;IACd,CAAC,CAAC;IAAAjB,eAAA,4BAiBkB;MAChBkB,kBAAkB,EAAE,sCAAsC;MAC1DC,wBAAwB,EAAE,4CAA4C;MACtEC,kBAAkB,EAAE,sCAAsC;MAC1DC,sBAAsB,EAAE,0CAA0C;MAClEC,sBAAsB,EAAE,0CAA0C;MAClEC,wBAAwB,EAAE;IAC9B,CAAC;IACD;AACJ;AACA;AACA;IAHIvB,eAAA,oBAIY,EAAE;IACd;AACJ;AACA;IAFIA,eAAA,yBAGiB,IAAI;IACrB;IAAAA,eAAA;IAEA;AACJ;AACA;IAFIA,eAAA;IAIA;AACJ;AACA;IAFIA,eAAA;IAIA;IAAAA,eAAA;IAAAA,eAAA;IAGA;IAAAA,eAAA;IAAAA,eAAA;IAQA;IAAAA,eAAA,wBACgB,OAAO;IACvB;IAAAA,eAAA,eACO,IAAI;IACX;IAAAA,eAAA,iBACS,IAAIrC,YAAY,CAAC,CAAC;IAC3B;IAAAqC,eAAA,8BACsB,IAAIrC,YAAY,CAAC,CAAC;IACxC;IAAAqC,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IACA;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA;IAQA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA,qBAIa,MAAM,CAAE,CAAC;IAAAA,eAAA,iCACG,EAAE;IAAAA,eAAA,6BACNR,oBAAoB,CAACgC,IAAI;IAAAxB,eAAA,wCACd,MAAM,CAAE,CAAC;IAAAA,eAAA,6BACpB,MAAM,CAAE,CAAC;IAAAA,eAAA,mBA4BnB,KAAK;IAAAA,eAAA,oBAWJ,KAAK;IAAAA,eAAA,yBAwBA,KAAK;IA7DlB1C,MAAM,CAACoB,sBAAsB,CAAC,CAAC+C,IAAI,CAAC5C,uBAAuB,CAAC;IAC5D,MAAM6C,QAAQ,GAAGpE,MAAM,CAAC,IAAIM,kBAAkB,CAAC,UAAU,CAAC,EAAE;MAAEqD,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC/E,IAAI,CAACU,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI1B,QAAQ;IACzC,IAAI,CAACZ,KAAK,GAAG,IAAI,CAACsC,QAAQ,CAACtC,KAAK,IAAIY,QAAQ,CAACZ,KAAK;IAClD,IAAI,CAACqC,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAGE,QAAQ,CAACF,QAAQ,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACb,EAAE,GAAG,IAAI,CAACC,SAAS,GAAGxD,MAAM,CAACJ,YAAY,CAAC,CAAC2E,KAAK,CAAC,mBAAmB,CAAC;IAC1E,IAAI,CAACtC,mBAAmB,IAAAwB,qBAAA,IAAAC,cAAA,GAAG,IAAI,CAACW,QAAQ,cAAAX,cAAA,uBAAbA,cAAA,CAAezB,mBAAmB,cAAAwB,qBAAA,cAAAA,qBAAA,GAAI,KAAK;EAC1E;EACAe,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACrB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACC,cAAc,CAAC;EAChD;EACA;EACA,IAAI1B,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC2B,QAAQ;EACxB;EACA,IAAI3B,OAAOA,CAAC4B,KAAK,EAAE;IACf,IAAIA,KAAK,IAAI,IAAI,CAAC5B,OAAO,EAAE;MACvB,IAAI,CAAC2B,QAAQ,GAAGC,KAAK;MACrB,IAAI,CAACC,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EAEA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACH,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,CAACG,QAAQ,EAAE;MACzB,IAAI,CAACC,SAAS,GAAGJ,KAAK;MACtB,IAAI,CAACC,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIG,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACP,cAAc;EAC9B;EACA,IAAIO,aAAaA,CAACL,KAAK,EAAE;IACrB,MAAMM,OAAO,GAAGN,KAAK,IAAI,IAAI,CAACF,cAAc;IAC5C,IAAI,CAACA,cAAc,GAAGE,KAAK;IAC3B,IAAIM,OAAO,EAAE;MACT,IAAI,IAAI,CAACR,cAAc,EAAE;QACrB,IAAI,CAACS,qBAAqB,CAACpD,oBAAoB,CAACqD,aAAa,CAAC;MAClE,CAAC,MACI;QACD,IAAI,CAACD,qBAAqB,CAAC,IAAI,CAACnC,OAAO,GAAGjB,oBAAoB,CAACsD,OAAO,GAAGtD,oBAAoB,CAACuD,SAAS,CAAC;MAC5G;MACA,IAAI,CAACC,mBAAmB,CAACC,IAAI,CAAC,IAAI,CAACd,cAAc,CAAC;IACtD;IACA,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAACC,cAAc,CAAC;EAChD;EAEAe,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,aAAa,IAAI,IAAI,CAACX,QAAQ;EAC9C;EACA;EACAY,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,CAACd,kBAAkB,CAACe,aAAa,CAAC,CAAC;EAC3C;EACA;EACAC,UAAUA,CAACjB,KAAK,EAAE;IACd,IAAI,CAAC5B,OAAO,GAAG,CAAC,CAAC4B,KAAK;EAC1B;EACA;EACAkB,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACC,6BAA6B,GAAGD,EAAE;EAC3C;EACA;EACAE,iBAAiBA,CAACF,EAAE,EAAE;IAClB,IAAI,CAACG,UAAU,GAAGH,EAAE;EACxB;EACA;EACAI,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACrB,QAAQ,GAAGqB,UAAU;EAC9B;EACA;EACAC,QAAQA,CAACC,OAAO,EAAE;IACd,OAAO,IAAI,CAACC,QAAQ,IAAID,OAAO,CAAC1B,KAAK,KAAK,IAAI,GAAG;MAAE,UAAU,EAAE;IAAK,CAAC,GAAG,IAAI;EAChF;EACA;EACA4B,yBAAyBA,CAACT,EAAE,EAAE;IAC1B,IAAI,CAACxB,kBAAkB,GAAGwB,EAAE;EAChC;EACAZ,qBAAqBA,CAACsB,QAAQ,EAAE;IAC5B,IAAIC,QAAQ,GAAG,IAAI,CAACC,kBAAkB;IACtC,IAAIC,OAAO,GAAG,IAAI,CAAC3D,0BAA0B,CAAC,CAAC;IAC/C,IAAIyD,QAAQ,KAAKD,QAAQ,IAAI,CAACG,OAAO,EAAE;MACnC;IACJ;IACA,IAAI,IAAI,CAACC,sBAAsB,EAAE;MAC7BD,OAAO,CAACE,SAAS,CAACC,MAAM,CAAC,IAAI,CAACF,sBAAsB,CAAC;IACzD;IACA,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAACG,yCAAyC,CAACN,QAAQ,EAAED,QAAQ,CAAC;IAChG,IAAI,CAACE,kBAAkB,GAAGF,QAAQ;IAClC,IAAI,IAAI,CAACI,sBAAsB,CAACI,MAAM,GAAG,CAAC,EAAE;MACxCL,OAAO,CAACE,SAAS,CAACI,GAAG,CAAC,IAAI,CAACL,sBAAsB,CAAC;MAClD;MACA,MAAMM,cAAc,GAAG,IAAI,CAACN,sBAAsB;MAClD,IAAI,CAACO,OAAO,CAACC,iBAAiB,CAAC,MAAM;QACjCC,UAAU,CAAC,MAAM;UACbV,OAAO,CAACE,SAAS,CAACC,MAAM,CAACI,cAAc,CAAC;QAC5C,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACN;EACJ;EACAI,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACvB,6BAA6B,CAAC,IAAI,CAAChD,OAAO,CAAC;IAChD,IAAI,CAACwE,MAAM,CAAChC,IAAI,CAAC,IAAI,CAAC5C,kBAAkB,CAAC,IAAI,CAACI,OAAO,CAAC,CAAC;IACvD;IACA;IACA,IAAI,IAAI,CAACN,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACK,OAAO,GAAG,IAAI,CAACA,OAAO;IAC3D;EACJ;EACA;EACAyE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACzE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACgD,6BAA6B,CAAC,IAAI,CAAChD,OAAO,CAAC;EACpD;EACA0E,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,eAAA;IAChB,MAAM9F,WAAW,IAAA8F,eAAA,GAAG,IAAI,CAACzD,QAAQ,cAAAyD,eAAA,uBAAbA,eAAA,CAAe9F,WAAW;IAC9C;IACA,IAAI,CAAC,IAAI,CAACkD,QAAQ,IAAIlD,WAAW,KAAK,MAAM,EAAE;MAC1C;MACA,IAAI,IAAI,CAACoD,aAAa,IAAIpD,WAAW,KAAK,OAAO,EAAE;QAC/C+F,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAACpD,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACa,mBAAmB,CAACC,IAAI,CAAC,IAAI,CAACd,cAAc,CAAC;QACtD,CAAC,CAAC;MACN;MACA,IAAI,CAACC,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;MAC9B,IAAI,CAACQ,qBAAqB,CAAC,IAAI,CAACR,QAAQ,GAAG5C,oBAAoB,CAACsD,OAAO,GAAGtD,oBAAoB,CAACuD,SAAS,CAAC;MACzG;MACA;MACA;MACA,IAAI,CAACiC,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI,IAAK,IAAI,CAACxC,QAAQ,IAAI,IAAI,CAACjD,mBAAmB,IAC9C,CAAC,IAAI,CAACiD,QAAQ,IAAIlD,WAAW,KAAK,MAAO,EAAE;MAC5C;MACA;MACA,IAAI,CAACa,aAAa,CAACC,aAAa,CAACK,OAAO,GAAG,IAAI,CAACA,OAAO;MACvD,IAAI,CAACN,aAAa,CAACC,aAAa,CAACsC,aAAa,GAAG,IAAI,CAACA,aAAa;IACvE;EACJ;EACA8C,mBAAmBA,CAACjF,KAAK,EAAE;IACvB;IACA;IACA;IACAA,KAAK,CAACkF,eAAe,CAAC,CAAC;EAC3B;EACAC,OAAOA,CAAA,EAAG;IACN;IACA;IACA;IACA;IACA;IACAL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,CAAC5B,UAAU,CAAC,CAAC;MACjB,IAAI,CAACrB,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACAkC,yCAAyCA,CAACN,QAAQ,EAAED,QAAQ,EAAE;IAC1D;IACA,IAAI,IAAI,CAACyB,cAAc,KAAK,gBAAgB,EAAE;MAC1C,OAAO,EAAE;IACb;IACA,QAAQxB,QAAQ;MACZ,KAAK3E,oBAAoB,CAACgC,IAAI;QAC1B;QACA;QACA,IAAI0C,QAAQ,KAAK1E,oBAAoB,CAACsD,OAAO,EAAE;UAC3C,OAAO,IAAI,CAAC8C,iBAAiB,CAAC1E,kBAAkB;QACpD,CAAC,MACI,IAAIgD,QAAQ,IAAI1E,oBAAoB,CAACqD,aAAa,EAAE;UACrD,OAAO,IAAI,CAACT,QAAQ,GACd,IAAI,CAACwD,iBAAiB,CAACvE,sBAAsB,GAC7C,IAAI,CAACuE,iBAAiB,CAACzE,wBAAwB;QACzD;QACA;MACJ,KAAK3B,oBAAoB,CAACuD,SAAS;QAC/B,OAAOmB,QAAQ,KAAK1E,oBAAoB,CAACsD,OAAO,GAC1C,IAAI,CAAC8C,iBAAiB,CAAC1E,kBAAkB,GACzC,IAAI,CAAC0E,iBAAiB,CAACzE,wBAAwB;MACzD,KAAK3B,oBAAoB,CAACsD,OAAO;QAC7B,OAAOoB,QAAQ,KAAK1E,oBAAoB,CAACuD,SAAS,GAC5C,IAAI,CAAC6C,iBAAiB,CAACxE,kBAAkB,GACzC,IAAI,CAACwE,iBAAiB,CAACvE,sBAAsB;MACvD,KAAK7B,oBAAoB,CAACqD,aAAa;QACnC,OAAOqB,QAAQ,KAAK1E,oBAAoB,CAACsD,OAAO,GAC1C,IAAI,CAAC8C,iBAAiB,CAACtE,sBAAsB,GAC7C,IAAI,CAACsE,iBAAiB,CAACrE,wBAAwB;IAC7D;IACA,OAAO,EAAE;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIW,kBAAkBA,CAACG,KAAK,EAAE;IACtB,MAAMwD,cAAc,GAAG,IAAI,CAAC1F,aAAa;IACzC,IAAI0F,cAAc,EAAE;MAChBA,cAAc,CAACzF,aAAa,CAACsC,aAAa,GAAGL,KAAK;IACtD;EACJ;EACAyD,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACX,iBAAiB,CAAC,CAAC;EAC5B;EACAY,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACZ,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,CAAC3C,QAAQ,EAAE;MAChB;MACA;MACA,IAAI,CAACrC,aAAa,CAACC,aAAa,CAACF,KAAK,CAAC,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI8F,yBAAyBA,CAACzF,KAAK,EAAE;IAC7B,IAAI,CAAC,CAACA,KAAK,CAAC0F,MAAM,IAAI,IAAI,CAACC,aAAa,CAAC9F,aAAa,CAAC+F,QAAQ,CAAC5F,KAAK,CAAC0F,MAAM,CAAC,EAAE;MAC3E1F,KAAK,CAACkF,eAAe,CAAC,CAAC;IAC3B;EACJ;AAUJ;AAACW,YAAA,GAtWKxG,WAAW;AAAAI,eAAA,CAAXJ,WAAW,wBAAAyG,qBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IA6VsF1G,YAAW;AAAA;AAAAI,eAAA,CA7V5GJ,WAAW,8BAuWgEzC,EAAE,CAAAoJ,iBAAA;EAAAC,IAAA,EATQ5G,YAAW;EAAA6G,SAAA;EAAAC,SAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MASrBzJ,EAAE,CAAA2J,WAAA,CAAAC,GAAA;MAAF5J,EAAE,CAAA2J,WAAA,CAAAE,GAAA;IAAA;IAAA,IAAAJ,EAAA;MAAA,IAAAK,EAAA;MAAF9J,EAAE,CAAA+J,cAAA,CAAAD,EAAA,GAAF9J,EAAE,CAAAgK,WAAA,QAAAN,GAAA,CAAA1G,aAAA,GAAA8G,EAAA,CAAAG,KAAA;MAAFjK,EAAE,CAAA+J,cAAA,CAAAD,EAAA,GAAF9J,EAAE,CAAAgK,WAAA,QAAAN,GAAA,CAAAX,aAAA,GAAAe,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAC,0BAAAZ,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFzJ,EAAE,CAAAsK,cAAA,OAAAZ,GAAA,CAAAhG,EATkB,CAAC;MASrB1D,EAAE,CAAAuK,WAAA,aATQ,IAAI,gBAAJ,IAAI,qBAAJ,IAAI;MASdvK,EAAE,CAAAwK,UAAA,CAAAd,GAAA,CAAAxH,KAAA,GATgB,MAAM,GAAAwH,GAAA,CAAAxH,KAAA,GAAW,YAAf,CAAC;MASrBlC,EAAE,CAAAyK,WAAA,4BAAAf,GAAA,CAAAlB,cAAA,KAT2B,gBAAT,CAAC,2BAAAkB,GAAA,CAAArE,QAAD,CAAC,8BAAAqE,GAAA,CAAArE,QAAD,CAAC,6BAAAqE,GAAA,CAAApG,OAAD,CAAC,0CAAAoG,GAAA,CAAAtH,mBAAD,CAAC;IAAA;EAAA;EAAAsI,MAAA;IAAAC,SAAA;IAAAC,cAAA;IAAAC,eAAA;IAAAC,YAAA,uCAAmQpK,gBAAgB;IAAAqK,YAAA;IAAAC,QAAA;IAAAtH,EAAA;IAAAmD,QAAA,8BAAsInG,gBAAgB;IAAAuK,aAAA;IAAAC,IAAA;IAAAhG,KAAA;IAAAc,aAAA,wCAAmHtF,gBAAgB;IAAA6D,QAAA,8BAAuCW,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAGiG,SAAS,GAAGxK,eAAe,CAACuE,KAAK,CAAE;IAAAhD,KAAA;IAAAE,mBAAA,oDAAuF1B,gBAAgB;IAAA4C,OAAA,4BAAmC5C,gBAAgB;IAAA2E,QAAA,8BAAsC3E,gBAAgB;IAAA6E,aAAA,wCAAqD7E,gBAAgB;EAAA;EAAA0K,OAAA;IAAAtD,MAAA;IAAAjC,mBAAA;EAAA;EAAAwF,QAAA;EAAAC,QAAA,GAS37BtL,EAAE,CAAAuL,kBAAA,CAT8/C,CACrkDjJ,mCAAmC,EACnC;IACIC,OAAO,EAAElB,aAAa;IACtBmB,WAAW,EAAEC,YAAW;IACxBC,KAAK,EAAE;EACX,CAAC,CACJ,GAEwE1C,EAAE,CAAAwL,oBAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,sBAAAtC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAA,MAAAuC,GAAA,GAAFhM,EAAE,CAAAiM,gBAAA;MAAFjM,EAAE,CAAAkM,eAAA;MAAFlM,EAAE,CAAAmM,cAAA,YAF8S,CAAC;MAEjTnM,EAAE,CAAAoM,UAAA,mBAAAC,2CAAAC,MAAA;QAAFtM,EAAE,CAAAuM,aAAA,CAAAP,GAAA;QAAA,OAAFhM,EAAE,CAAAwM,WAAA,CAF2Q9C,GAAA,CAAAb,yBAAA,CAAAyD,MAAgC,CAAC;MAAA,CAAC,CAAC;MAEhTtM,EAAE,CAAAmM,cAAA,eAFwV,CAAC,YAAoJ,CAAC;MAEhfnM,EAAE,CAAAoM,UAAA,mBAAAK,2CAAA;QAAFzM,EAAE,CAAAuM,aAAA,CAAAP,GAAA;QAAA,OAAFhM,EAAE,CAAAwM,WAAA,CAFsd9C,GAAA,CAAAd,mBAAA,CAAoB,CAAC;MAAA,CAAC,CAAC;MAE/e5I,EAAE,CAAA0M,YAAA,CAFmf,CAAC;MAEtf1M,EAAE,CAAAmM,cAAA,iBAFijD,CAAC;MAEpjDnM,EAAE,CAAAoM,UAAA,kBAAAO,4CAAA;QAAF3M,EAAE,CAAAuM,aAAA,CAAAP,GAAA;QAAA,OAAFhM,EAAE,CAAAwM,WAAA,CAFw8C9C,GAAA,CAAAnB,OAAA,CAAQ,CAAC;MAAA,CAAC,CAAC,mBAAAqE,6CAAA;QAEr9C5M,EAAE,CAAAuM,aAAA,CAAAP,GAAA;QAAA,OAAFhM,EAAE,CAAAwM,WAAA,CAF0+C9C,GAAA,CAAAf,aAAA,CAAc,CAAC;MAAA,CAAC,CAAC,oBAAAkE,8CAAAP,MAAA;QAE7/CtM,EAAE,CAAAuM,aAAA,CAAAP,GAAA;QAAA,OAAFhM,EAAE,CAAAwM,WAAA,CAFmhD9C,GAAA,CAAArB,mBAAA,CAAAiE,MAA0B,CAAC;MAAA,CAAC,CAAC;MAEljDtM,EAAE,CAAA0M,YAAA,CAFijD,CAAC;MAEpjD1M,EAAE,CAAA8M,SAAA,YAFimD,CAAC;MAEpmD9M,EAAE,CAAAmM,cAAA,YAF+oD,CAAC;MAElpDnM,EAAE,CAAA+M,cAAA;MAAF/M,EAAE,CAAAmM,cAAA,YAFiyD,CAAC;MAEpyDnM,EAAE,CAAA8M,SAAA,cAF46D,CAAC;MAE/6D9M,EAAE,CAAA0M,YAAA,CAF07D,CAAC;MAE77D1M,EAAE,CAAAgN,eAAA;MAAFhN,EAAE,CAAA8M,SAAA,cAF++D,CAAC;MAEl/D9M,EAAE,CAAA0M,YAAA,CAF2/D,CAAC;MAE9/D1M,EAAE,CAAA8M,SAAA,cAFitE,CAAC;MAEptE9M,EAAE,CAAA0M,YAAA,CAF2tE,CAAC;MAE9tE1M,EAAE,CAAAmM,cAAA,mBAFohF,CAAC;MAEvhFnM,EAAE,CAAAiN,YAAA,GAFmjF,CAAC;MAEtjFjN,EAAE,CAAA0M,YAAA,CAF+jF,CAAC,CAAO,CAAC;IAAA;IAAA,IAAAjD,EAAA;MAAA,MAAAyD,WAAA,GAE1kFlN,EAAE,CAAAmN,WAAA;MAAFnN,EAAE,CAAAoN,UAAA,kBAAA1D,GAAA,CAAAuB,aAF+P,CAAC;MAElQjL,EAAE,CAAAqN,SAAA,EAF8oB,CAAC;MAEjpBrN,EAAE,CAAAyK,WAAA,2BAAAf,GAAA,CAAApG,OAF8oB,CAAC;MAEjpBtD,EAAE,CAAAoN,UAAA,YAAA1D,GAAA,CAAApG,OAF0rC,CAAC,kBAAAoG,GAAA,CAAAnE,aAA6C,CAAC,aAAAmE,GAAA,CAAArE,QAAA,KAAAqE,GAAA,CAAAtH,mBAA2D,CAAC,OAAAsH,GAAA,CAAAjG,OAA4B,CAAC,aAAAiG,GAAA,CAAA7C,QAAmC,CAAC,aAAA6C,GAAA,CAAArE,QAAA,KAAAqE,GAAA,CAAAtH,mBAAA,QAAAsH,GAAA,CAAAnF,QAA2E,CAAC;MAEp7CvE,EAAE,CAAAuK,WAAA,eAAAb,GAAA,CAAAiB,SAAA,6BAAAjB,GAAA,CAAAkB,cAAA,sBAAAlB,GAAA,CAAAmB,eAAA,kBAAAnB,GAAA,CAAAnE,aAAA,oCAAAmE,GAAA,CAAAqB,YAAA,mBAAArB,GAAA,CAAArE,QAAA,IAAAqE,GAAA,CAAAtH,mBAAA,iCAAAsH,GAAA,CAAAoB,YAAA,eAAApB,GAAA,CAAAsB,QAAA,UAAAtB,GAAA,CAAAwB,IAAA,WAAAxB,GAAA,CAAAxE,KAAA;MAAFlF,EAAE,CAAAqN,SAAA,EAF6mE,CAAC;MAEhnErN,EAAE,CAAAoN,UAAA,qBAAAF,WAF6mE,CAAC,sBAAAxD,GAAA,CAAA1D,aAAA,IAAA0D,GAAA,CAAArE,QAAwD,CAAC,0BAAmC,CAAC;MAE7sErF,EAAE,CAAAqN,SAAA,CAFmhF,CAAC;MAEthFrN,EAAE,CAAAoN,UAAA,QAAA1D,GAAA,CAAAjG,OAFmhF,CAAC;IAAA;EAAA;EAAA6J,YAAA,GAAwhgB1L,SAAS,EAAwPH,qBAAqB;EAAA8L,MAAA;EAAAC,aAAA;EAAAC,eAAA;AAAA;AAEr5lB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAiF1N,EAAE,CAAA2N,iBAAA,CAAQlL,WAAW,EAAc,CAAC;IACzG4G,IAAI,EAAEzI,SAAS;IACfgN,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEC,IAAI,EAAE;QAC7B,OAAO,EAAE,kBAAkB;QAC3B,iBAAiB,EAAE,MAAM;QACzB,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,iCAAiC,EAAE,qCAAqC;QACxE,gCAAgC,EAAE,UAAU;QAC5C,MAAM,EAAE,IAAI;QACZ;QACA,mCAAmC,EAAE,UAAU;QAC/C,kCAAkC,EAAE,SAAS;QAC7C,+CAA+C,EAAE,qBAAqB;QACtE,SAAS,EAAE;MACf,CAAC;MAAEC,SAAS,EAAE,CACVzL,mCAAmC,EACnC;QACIC,OAAO,EAAElB,aAAa;QACtBmB,WAAW,EAAEC,WAAW;QACxBC,KAAK,EAAE;MACX,CAAC,CACJ;MAAE2I,QAAQ,EAAE,aAAa;MAAEmC,aAAa,EAAE3M,iBAAiB,CAACmN,IAAI;MAAEP,eAAe,EAAE3M,uBAAuB,CAACmN,MAAM;MAAEC,OAAO,EAAE,CAACtM,SAAS,EAAEH,qBAAqB,CAAC;MAAEqK,QAAQ,EAAE,04EAA04E;MAAEyB,MAAM,EAAE,CAAC,y6fAAy6f;IAAE,CAAC;EACx/kB,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE5C,SAAS,EAAE,CAAC;MACpDtB,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEhD,cAAc,EAAE,CAAC;MACjBvB,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE/C,eAAe,EAAE,CAAC;MAClBxB,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE9C,YAAY,EAAE,CAAC;MACfzB,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC;QAAEO,KAAK,EAAE,eAAe;QAAEC,SAAS,EAAE1N;MAAiB,CAAC;IAClE,CAAC,CAAC;IAAEqK,YAAY,EAAE,CAAC;MACf1B,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE5C,QAAQ,EAAE,CAAC;MACX3B,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAElK,EAAE,EAAE,CAAC;MACL2F,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAE8F,QAAQ,EAAE,CAAC;MACXwC,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuK,aAAa,EAAE,CAAC;MAChB5B,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEmK,IAAI,EAAE,CAAC;MACP7B,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAE+G,MAAM,EAAE,CAAC;MACTuB,IAAI,EAAErI;IACV,CAAC,CAAC;IAAE6E,mBAAmB,EAAE,CAAC;MACtBwD,IAAI,EAAErI;IACV,CAAC,CAAC;IAAEkE,KAAK,EAAE,CAAC;MACRmE,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEiF,aAAa,EAAE,CAAC;MAChBqD,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsC,aAAa,EAAE,CAAC;MAChBqG,IAAI,EAAEpI,SAAS;MACf2M,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE7E,aAAa,EAAE,CAAC;MAChBM,IAAI,EAAEpI,SAAS;MACf2M,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAErJ,QAAQ,EAAE,CAAC;MACX8E,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAGlJ,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAGiG,SAAS,GAAGxK,eAAe,CAACuE,KAAK;MAAG,CAAC;IACzF,CAAC,CAAC;IAAEhD,KAAK,EAAE,CAAC;MACRmH,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAEqB,mBAAmB,EAAE,CAAC;MACtBiH,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4C,OAAO,EAAE,CAAC;MACV+F,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2E,QAAQ,EAAE,CAAC;MACXgE,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6E,aAAa,EAAE,CAAC;MAChB8D,IAAI,EAAEtI,KAAK;MACX6M,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM2N,+BAA+B,GAAG;EACpC9L,OAAO,EAAElB,aAAa;EACtBmB,WAAW,EAAEtC,UAAU,CAAC,MAAMoO,4BAA4B,CAAC;EAC3D5L,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4L,4BAA4B,SAAShN,yBAAyB,CAAC;AAGpEiN,6BAAA,GAHKD,4BAA4B;AAAAzL,eAAA,CAA5ByL,4BAA4B;EAAA,IAAAE,0CAAA;EAAA,gBAAAC,sCAAAtF,iBAAA;IAAA,QAAAqF,0CAAA,KAAAA,0CAAA,GArG+CxO,EAAE,CAAA0O,qBAAA,CAsGoBJ,6BAA4B,IAAAnF,iBAAA,IAA5BmF,6BAA4B;EAAA;AAAA;AAAAzL,eAAA,CAD7HyL,4BAA4B,8BArG+CtO,EAAE,CAAA2O,iBAAA;EAAAtF,IAAA,EAuGQiF,6BAA4B;EAAAhF,SAAA;EAAAgC,QAAA,GAvGtCtL,EAAE,CAAAuL,kBAAA,CAuG8M,CAAC8C,+BAA+B,CAAC,GAvGjPrO,EAAE,CAAA4O,0BAAA;AAAA;AAyGnF;EAAA,QAAAlB,SAAA,oBAAAA,SAAA,KAzGiF1N,EAAE,CAAA2N,iBAAA,CAyGQW,4BAA4B,EAAc,CAAC;IAC1HjF,IAAI,EAAEnI,SAAS;IACf0M,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;AAC9B,kFAAkF;MAC9DE,SAAS,EAAE,CAACM,+BAA+B;IAC/C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMQ,mCAAmC,CAAC;AAIzCC,oCAAA,GAJKD,mCAAmC;AAAAhM,eAAA,CAAnCgM,mCAAmC,wBAAAE,6CAAA5F,iBAAA;EAAA,YAAAA,iBAAA,IAC8D0F,oCAAmC;AAAA;AAAAhM,eAAA,CADpIgM,mCAAmC,8BAtHwC7O,EAAE,CAAAgP,gBAAA;EAAA3F,IAAA,EAwHqBwF,oCAAmC;EAAAX,OAAA,GAAYI,4BAA4B;EAAAW,OAAA,GAAaX,4BAA4B;AAAA;AAAAzL,eAAA,CAFtNgM,mCAAmC,8BAtHwC7O,EAAE,CAAAkP,gBAAA;AA2HnF;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KA3HiF1N,EAAE,CAAA2N,iBAAA,CA2HQkB,mCAAmC,EAAc,CAAC;IACjIxF,IAAI,EAAElI,QAAQ;IACdyM,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAACI,4BAA4B,CAAC;MACvCW,OAAO,EAAE,CAACX,4BAA4B;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMa,iBAAiB,CAAC;AAIvBC,kBAAA,GAJKD,iBAAiB;AAAAtM,eAAA,CAAjBsM,iBAAiB,wBAAAE,2BAAAlG,iBAAA;EAAA,YAAAA,iBAAA,IACgFgG,kBAAiB;AAAA;AAAAtM,eAAA,CADlHsM,iBAAiB,8BAlI0DnP,EAAE,CAAAgP,gBAAA;EAAA3F,IAAA,EAoIqB8F,kBAAiB;EAAAjB,OAAA,GAAYzL,WAAW,EAAEZ,eAAe;EAAAoN,OAAA,GAAaxM,WAAW,EAAEZ,eAAe;AAAA;AAAAgB,eAAA,CAFpMsM,iBAAiB,8BAlI0DnP,EAAE,CAAAkP,gBAAA;EAAAhB,OAAA,GAqIkDzL,WAAW,EAAEZ,eAAe,EAAEA,eAAe;AAAA;AAElL;EAAA,QAAA6L,SAAA,oBAAAA,SAAA,KAvIiF1N,EAAE,CAAA2N,iBAAA,CAuIQwB,iBAAiB,EAAc,CAAC;IAC/G9F,IAAI,EAAElI,QAAQ;IACdyM,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAACzL,WAAW,EAAEZ,eAAe,CAAC;MACvCoN,OAAO,EAAE,CAACxM,WAAW,EAAEZ,eAAe;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASS,mCAAmC,EAAER,4BAA4B,EAAEG,oCAAoC,EAAEoM,+BAA+B,EAAE5L,WAAW,EAAEE,iBAAiB,EAAEwM,iBAAiB,EAAEb,4BAA4B,EAAEjM,oBAAoB,EAAEwM,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}