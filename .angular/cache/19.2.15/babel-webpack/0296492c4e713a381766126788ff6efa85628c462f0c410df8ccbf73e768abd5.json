{"ast": null, "code": "var _InputSearchComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-search.component.html?ngResource\";\nimport { Component, Input } from '@angular/core';\nlet InputSearchComponent = (_InputSearchComponent = class InputSearchComponent {\n  constructor() {\n    this.id = '';\n    this.readonly = false;\n    this.submitted = false;\n  }\n  set componentOptions(value) {\n    this.title = value === null || value === void 0 ? void 0 : value.title;\n    this.fields = value === null || value === void 0 ? void 0 : value.fields;\n  }\n}, _InputSearchComponent.propDecorators = {\n  control: [{\n    type: Input\n  }],\n  id: [{\n    type: Input\n  }],\n  readonly: [{\n    type: Input\n  }],\n  submitted: [{\n    type: Input\n  }],\n  componentOptions: [{\n    type: Input\n  }]\n}, _InputSearchComponent);\nInputSearchComponent = __decorate([Component({\n  selector: 'lib-input-search',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], InputSearchComponent);\nexport { InputSearchComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Input", "InputSearchComponent", "_InputSearchComponent", "constructor", "id", "readonly", "submitted", "componentOptions", "value", "title", "fields", "propDecorators", "control", "type", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/input-search/input-search.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./input-search.component.html?ngResource\";\nimport { Component, Input } from '@angular/core';\nlet InputSearchComponent = class InputSearchComponent {\n    constructor() {\n        this.id = '';\n        this.readonly = false;\n        this.submitted = false;\n    }\n    set componentOptions(value) {\n        this.title = value?.title;\n        this.fields = value?.fields;\n    }\n    static { this.propDecorators = {\n        control: [{ type: Input }],\n        id: [{ type: Input }],\n        readonly: [{ type: Input }],\n        submitted: [{ type: Input }],\n        componentOptions: [{ type: Input }]\n    }; }\n};\nInputSearchComponent = __decorate([\n    Component({\n        selector: 'lib-input-search',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], InputSearchComponent);\nexport { InputSearchComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,IAAIC,oBAAoB,IAAAC,qBAAA,GAAG,MAAMD,oBAAoB,CAAC;EAClDE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;EAC1B;EACA,IAAIC,gBAAgBA,CAACC,KAAK,EAAE;IACxB,IAAI,CAACC,KAAK,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,KAAK;IACzB,IAAI,CAACC,MAAM,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,MAAM;EAC/B;AAQJ,CAAC,EAPYR,qBAAA,CAAKS,cAAc,GAAG;EAC3BC,OAAO,EAAE,CAAC;IAAEC,IAAI,EAAEb;EAAM,CAAC,CAAC;EAC1BI,EAAE,EAAE,CAAC;IAAES,IAAI,EAAEb;EAAM,CAAC,CAAC;EACrBK,QAAQ,EAAE,CAAC;IAAEQ,IAAI,EAAEb;EAAM,CAAC,CAAC;EAC3BM,SAAS,EAAE,CAAC;IAAEO,IAAI,EAAEb;EAAM,CAAC,CAAC;EAC5BO,gBAAgB,EAAE,CAAC;IAAEM,IAAI,EAAEb;EAAM,CAAC;AACtC,CAAC,EAAAE,qBAAA,CACJ;AACDD,oBAAoB,GAAGJ,UAAU,CAAC,CAC9BE,SAAS,CAAC;EACNe,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAEjB,oBAAoB;EAC9BkB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEf,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}