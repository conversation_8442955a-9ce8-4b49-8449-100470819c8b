{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { SwuiProgressContainerComponent } from './swui-progress-container.component';\nimport { CommonModule } from '@angular/common';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nlet SwuiProgressContainerModule = class SwuiProgressContainerModule {};\nSwuiProgressContainerModule = __decorate([NgModule({\n  imports: [CommonModule, MatProgressSpinnerModule],\n  declarations: [SwuiProgressContainerComponent],\n  exports: [SwuiProgressContainerComponent],\n  providers: []\n})], SwuiProgressContainerModule);\nexport { SwuiProgressContainerModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "SwuiProgressContainerComponent", "CommonModule", "MatProgressSpinnerModule", "SwuiProgressContainerModule", "imports", "declarations", "exports", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-progress-container/swui-progress-container.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { SwuiProgressContainerComponent } from './swui-progress-container.component';\nimport { CommonModule } from '@angular/common';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nlet SwuiProgressContainerModule = class SwuiProgressContainerModule {\n};\nSwuiProgressContainerModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            MatProgressSpinnerModule,\n        ],\n        declarations: [SwuiProgressContainerComponent],\n        exports: [SwuiProgressContainerComponent],\n        providers: [],\n    })\n], SwuiProgressContainerModule);\nexport { SwuiProgressContainerModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,8BAA8B,QAAQ,qCAAqC;AACpF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,IAAIC,2BAA2B,GAAG,MAAMA,2BAA2B,CAAC,EACnE;AACDA,2BAA2B,GAAGL,UAAU,CAAC,CACrCC,QAAQ,CAAC;EACLK,OAAO,EAAE,CACLH,YAAY,EACZC,wBAAwB,CAC3B;EACDG,YAAY,EAAE,CAACL,8BAA8B,CAAC;EAC9CM,OAAO,EAAE,CAACN,8BAA8B,CAAC;EACzCO,SAAS,EAAE;AACf,CAAC,CAAC,CACL,EAAEJ,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}