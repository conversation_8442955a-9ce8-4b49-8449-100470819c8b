{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageSelectorComponent } from './language-selector.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\nlet LanguageSelectorModule = class LanguageSelectorModule {};\nLanguageSelectorModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, MatRippleModule, MatIconModule, MatMenuModule],\n  declarations: [LanguageSelectorComponent],\n  exports: [LanguageSelectorComponent]\n})], LanguageSelectorModule);\nexport { LanguageSelectorModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "TranslateModule", "LanguageSelectorComponent", "MatMenuModule", "MatIconModule", "MatRippleModule", "LanguageSelectorModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/language-selector/language-selector.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { LanguageSelectorComponent } from './language-selector.component';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRippleModule } from '@angular/material/core';\nlet LanguageSelectorModule = class LanguageSelectorModule {\n};\nLanguageSelectorModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule,\n            MatRippleModule,\n            MatIconModule,\n            MatMenuModule,\n        ],\n        declarations: [\n            LanguageSelectorComponent,\n        ],\n        exports: [\n            LanguageSelectorComponent,\n        ],\n    })\n], LanguageSelectorModule);\nexport { LanguageSelectorModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,IAAIC,sBAAsB,GAAG,MAAMA,sBAAsB,CAAC,EACzD;AACDA,sBAAsB,GAAGR,UAAU,CAAC,CAChCC,QAAQ,CAAC;EACLQ,OAAO,EAAE,CACLP,YAAY,EACZC,eAAe,EACfI,eAAe,EACfD,aAAa,EACbD,aAAa,CAChB;EACDK,YAAY,EAAE,CACVN,yBAAyB,CAC5B;EACDO,OAAO,EAAE,CACLP,yBAAyB;AAEjC,CAAC,CAAC,CACL,EAAEI,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}