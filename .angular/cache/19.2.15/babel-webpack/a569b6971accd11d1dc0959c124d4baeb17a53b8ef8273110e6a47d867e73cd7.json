{"ast": null, "code": "var _SwuiControlStoriesComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-control-stories.component.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { UntypedFormBuilder, Validators } from '@angular/forms';\nlet SwuiControlStoriesComponent = (_SwuiControlStoriesComponent = class SwuiControlStoriesComponent {\n  get testControl() {\n    return this.form.get('testControl');\n  }\n  get testControlEmpty() {\n    return this.form.get('testControlEmpty');\n  }\n  get testControlForced() {\n    return this.form.get('testControlForced');\n  }\n  constructor(fb) {\n    this.fb = fb;\n    this.messageErrors = {\n      required: 'field is required',\n      maxlength: `Maximum length is ${3}`\n    };\n    this.form = this.fb.group({\n      testControl: ['0', Validators.compose([Validators.required, Validators.maxLength(3)])],\n      testControlEmpty: [null, Validators.compose([Validators.required, Validators.maxLength(3)])],\n      testControlForced: [null, Validators.compose([Validators.required, Validators.maxLength(3)])]\n    });\n  }\n}, _SwuiControlStoriesComponent.ctorParameters = () => [{\n  type: UntypedFormBuilder\n}], _SwuiControlStoriesComponent);\nSwuiControlStoriesComponent = __decorate([Component({\n  selector: 'lib-swui-control-stories',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiControlStoriesComponent);\nexport { SwuiControlStoriesComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "UntypedFormBuilder", "Validators", "SwuiControlStoriesComponent", "_SwuiControlStoriesComponent", "testControl", "form", "get", "testControlEmpty", "testControlForced", "constructor", "fb", "messageErrors", "required", "maxlength", "group", "compose", "max<PERSON><PERSON><PERSON>", "ctorParameters", "type", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-control-messages/stories/swui-control-stories/swui-control-stories.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-control-stories.component.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { UntypedFormBuilder, Validators } from '@angular/forms';\nlet SwuiControlStoriesComponent = class SwuiControlStoriesComponent {\n    get testControl() {\n        return this.form.get('testControl');\n    }\n    get testControlEmpty() {\n        return this.form.get('testControlEmpty');\n    }\n    get testControlForced() {\n        return this.form.get('testControlForced');\n    }\n    constructor(fb) {\n        this.fb = fb;\n        this.messageErrors = {\n            required: 'field is required',\n            maxlength: `Maximum length is ${3}`,\n        };\n        this.form = this.fb.group({\n            testControl: ['0', Validators.compose([\n                    Validators.required,\n                    Validators.maxLength(3)\n                ])],\n            testControlEmpty: [null, Validators.compose([\n                    Validators.required,\n                    Validators.maxLength(3)\n                ])],\n            testControlForced: [null, Validators.compose([\n                    Validators.required,\n                    Validators.maxLength(3)\n                ])],\n        });\n    }\n    static { this.ctorParameters = () => [\n        { type: UntypedFormBuilder }\n    ]; }\n};\nSwuiControlStoriesComponent = __decorate([\n    Component({\n        selector: 'lib-swui-control-stories',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], SwuiControlStoriesComponent);\nexport { SwuiControlStoriesComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,kDAAkD;AACnF,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,kBAAkB,EAAEC,UAAU,QAAQ,gBAAgB;AAC/D,IAAIC,2BAA2B,IAAAC,4BAAA,GAAG,MAAMD,2BAA2B,CAAC;EAChE,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,aAAa,CAAC;EACvC;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACF,IAAI,CAACC,GAAG,CAAC,kBAAkB,CAAC;EAC5C;EACA,IAAIE,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACH,IAAI,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAC7C;EACAG,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,aAAa,GAAG;MACjBC,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,qBAAqB,CAAC;IACrC,CAAC;IACD,IAAI,CAACR,IAAI,GAAG,IAAI,CAACK,EAAE,CAACI,KAAK,CAAC;MACtBV,WAAW,EAAE,CAAC,GAAG,EAAEH,UAAU,CAACc,OAAO,CAAC,CAC9Bd,UAAU,CAACW,QAAQ,EACnBX,UAAU,CAACe,SAAS,CAAC,CAAC,CAAC,CAC1B,CAAC,CAAC;MACPT,gBAAgB,EAAE,CAAC,IAAI,EAAEN,UAAU,CAACc,OAAO,CAAC,CACpCd,UAAU,CAACW,QAAQ,EACnBX,UAAU,CAACe,SAAS,CAAC,CAAC,CAAC,CAC1B,CAAC,CAAC;MACPR,iBAAiB,EAAE,CAAC,IAAI,EAAEP,UAAU,CAACc,OAAO,CAAC,CACrCd,UAAU,CAACW,QAAQ,EACnBX,UAAU,CAACe,SAAS,CAAC,CAAC,CAAC,CAC1B,CAAC;IACV,CAAC,CAAC;EACN;AAIJ,CAAC,EAHYb,4BAAA,CAAKc,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAElB;AAAmB,CAAC,CAC/B,EAAAG,4BAAA,CACJ;AACDD,2BAA2B,GAAGL,UAAU,CAAC,CACrCE,SAAS,CAAC;EACNoB,QAAQ,EAAE,0BAA0B;EACpCC,QAAQ,EAAEtB,oBAAoB;EAC9BuB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEnB,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}