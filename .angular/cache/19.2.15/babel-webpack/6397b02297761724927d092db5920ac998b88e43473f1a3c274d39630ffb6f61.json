{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var ArgumentOutOfRangeError = createErrorClass(function (_super) {\n  return function ArgumentOutOfRangeErrorImpl() {\n    _super(this);\n    this.name = 'ArgumentOutOfRangeError';\n    this.message = 'argument out of range';\n  };\n});\n//# sourceMappingURL=ArgumentOutOfRangeError.js.map", "map": {"version": 3, "names": ["createErrorClass", "ArgumentOutOfRangeError", "_super", "ArgumentOutOfRangeErrorImpl", "name", "message"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/ArgumentOutOfRangeError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport var ArgumentOutOfRangeError = createErrorClass(function (_super) {\n    return function ArgumentOutOfRangeErrorImpl() {\n        _super(this);\n        this.name = 'ArgumentOutOfRangeError';\n        this.message = 'argument out of range';\n    };\n});\n//# sourceMappingURL=ArgumentOutOfRangeError.js.map"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,IAAIC,uBAAuB,GAAGD,gBAAgB,CAAC,UAAUE,MAAM,EAAE;EACpE,OAAO,SAASC,2BAA2BA,CAAA,EAAG;IAC1CD,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACE,IAAI,GAAG,yBAAyB;IACrC,IAAI,CAACC,OAAO,GAAG,uBAAuB;EAC1C,CAAC;AACL,CAAC,CAAC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}