{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { <PERSON><PERSON> } from '@angular/core';\nlet FormattedNumberPipe = class FormattedNumberPipe {\n  transform(input = 0, fractionCount = 2, delimiter = ' ') {\n    return parseFloat(input).toFixed(fractionCount).replace(/(\\d)(?=(\\d{3})+($|\\.))/g, '$1' + delimiter);\n  }\n};\nFormattedNumberPipe = __decorate([Pipe({\n  name: 'formattedNumber',\n  standalone: false\n})], FormattedNumberPipe);\nexport { FormattedNumberPipe };", "map": {"version": 3, "names": ["__decorate", "<PERSON><PERSON>", "FormattedNumberPipe", "transform", "input", "fractionCount", "delimiter", "parseFloat", "toFixed", "replace", "name", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/pipes/formatted-number.pipe.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { <PERSON><PERSON> } from '@angular/core';\nlet FormattedNumberPipe = class FormattedNumberPipe {\n    transform(input = 0, fractionCount = 2, delimiter = ' ') {\n        return parseFloat(input).toFixed(fractionCount).replace(/(\\d)(?=(\\d{3})+($|\\.))/g, '$1' + delimiter);\n    }\n};\nFormattedNumberPipe = __decorate([\n    Pipe({\n        name: 'formattedNumber',\n        standalone: false\n    })\n], FormattedNumberPipe);\nexport { FormattedNumberPipe };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,IAAI,QAAQ,eAAe;AACpC,IAAIC,mBAAmB,GAAG,MAAMA,mBAAmB,CAAC;EAChDC,SAASA,CAACC,KAAK,GAAG,CAAC,EAAEC,aAAa,GAAG,CAAC,EAAEC,SAAS,GAAG,GAAG,EAAE;IACrD,OAAOC,UAAU,CAACH,KAAK,CAAC,CAACI,OAAO,CAACH,aAAa,CAAC,CAACI,OAAO,CAAC,yBAAyB,EAAE,IAAI,GAAGH,SAAS,CAAC;EACxG;AACJ,CAAC;AACDJ,mBAAmB,GAAGF,UAAU,CAAC,CAC7BC,IAAI,CAAC;EACDS,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAET,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}