{"ast": null, "code": "export { U as UniqueSelectionDispatcher } from './unique-selection-dispatcher-DtHZDqyJ.mjs';\nexport { A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy, b as _VIEW_REPEATER_STRATEGY, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nexport { D as DataSource, i as isDataSource } from './data-source-D34wiQZj.mjs';\nexport { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-D_JReLI1.mjs';\nexport { S as SelectionModel, g as getMultipleValuesInSingleSelectionError } from './selection-model-CeeHVIcP.mjs';\nimport '@angular/core';\nimport 'rxjs';\n//# sourceMappingURL=collections.mjs.map", "map": {"version": 3, "names": ["U", "UniqueSelectionDispatcher", "A", "ArrayDataSource", "_", "_RecycleViewRepeaterStrategy", "b", "_VIEW_REPEATER_STRATEGY", "a", "_ViewRepeaterOperation", "D", "DataSource", "i", "isDataSource", "_DisposeViewRepeaterStrategy", "S", "SelectionModel", "g", "getMultipleValuesInSingleSelectionError"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/collections.mjs"], "sourcesContent": ["export { U as UniqueSelectionDispatcher } from './unique-selection-dispatcher-DtHZDqyJ.mjs';\nexport { A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy, b as _VIEW_REPEATER_STRATEGY, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nexport { D as DataSource, i as isDataSource } from './data-source-D34wiQZj.mjs';\nexport { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-D_JReLI1.mjs';\nexport { S as SelectionModel, g as getMultipleValuesInSingleSelectionError } from './selection-model-CeeHVIcP.mjs';\nimport '@angular/core';\nimport 'rxjs';\n//# sourceMappingURL=collections.mjs.map\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,yBAAyB,QAAQ,4CAA4C;AAC3F,SAASC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,sBAAsB,QAAQ,+CAA+C;AAClL,SAASC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,YAAY,QAAQ,4BAA4B;AAC/E,SAAST,CAAC,IAAIU,4BAA4B,QAAQ,+CAA+C;AACjG,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,uCAAuC,QAAQ,gCAAgC;AAClH,OAAO,eAAe;AACtB,OAAO,MAAM;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}