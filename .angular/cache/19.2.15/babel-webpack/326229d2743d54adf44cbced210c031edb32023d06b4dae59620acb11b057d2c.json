{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _PlatformNavigation;\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\nclass PlatformNavigation {}\n_PlatformNavigation = PlatformNavigation;\n_defineProperty(PlatformNavigation, \"\\u0275fac\", function _PlatformNavigation_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PlatformNavigation)();\n});\n_defineProperty(PlatformNavigation, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _PlatformNavigation,\n  factory: () => (() => window.navigation)(),\n  providedIn: 'platform'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformNavigation, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform',\n      useFactory: () => window.navigation\n    }]\n  }], null, null);\n})();\nexport { PlatformNavigation };\n//# sourceMappingURL=platform_navigation-B45Jeakb.mjs.map", "map": {"version": 3, "names": ["i0", "Injectable", "PlatformNavigation", "_PlatformNavigation", "_defineProperty", "_PlatformNavigation_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "window", "navigation", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "useFactory"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/common/fesm2022/platform_navigation-B45Jeakb.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\nclass PlatformNavigation {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PlatformNavigation, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PlatformNavigation, providedIn: 'platform', useFactory: () => window.navigation });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PlatformNavigation, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'platform', useFactory: () => window.navigation }]\n        }] });\n\nexport { PlatformNavigation };\n//# sourceMappingURL=platform_navigation-B45Jeakb.mjs.map\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;;AAE1C;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;AAGxBC,mBAAA,GAHKD,kBAAkB;AAAAE,eAAA,CAAlBF,kBAAkB,wBAAAG,4BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IACgFJ,mBAAkB;AAAA;AAAAE,eAAA,CADpHF,kBAAkB,+BAI0DF,EAAE,CAAAO,kBAAA;EAAAC,KAAA,EAFwBN,mBAAkB;EAAAO,OAAA,EAAAA,CAAA,MAAsC,MAAMC,MAAM,CAACC,UAAU;EAAAC,UAAA,EAA/C;AAAU;AAEtJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAkFb,EAAE,CAAAc,iBAAA,CAAQZ,kBAAkB,EAAc,CAAC;IACjHa,IAAI,EAAEd,UAAU;IAChBe,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE,UAAU;MAAEK,UAAU,EAAEA,CAAA,KAAMP,MAAM,CAACC;IAAW,CAAC;EAC1E,CAAC,CAAC;AAAA;AAEV,SAAST,kBAAkB;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}