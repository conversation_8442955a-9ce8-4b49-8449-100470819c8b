{"ast": null, "code": "var _SwuiTdStringWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./string.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/string/string.widget.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=Lm5vLXdyYXAge3doaXRlLXNwYWNlOiBub3dyYXB9!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/string/string.widget.ts\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdStringWidgetComponent = (_SwuiTdStringWidgetComponent = class SwuiTdStringWidgetComponent {\n  constructor({\n    value,\n    schema\n  }) {\n    this.nowrap = false;\n    this.value = value;\n    this.truncate = schema.td && schema.td.truncate ? schema.td.truncate : undefined;\n    this.nowrap = schema.td && schema.td.nowrap || false;\n  }\n}, _SwuiTdStringWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdStringWidgetComponent);\nSwuiTdStringWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-string-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdStringWidgetComponent);\nexport { SwuiTdStringWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdStringWidgetComponent", "_SwuiTdStringWidgetComponent", "constructor", "value", "schema", "nowrap", "truncate", "td", "undefined", "ctorParameters", "type", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/string/string.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./string.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/string/string.widget.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=Lm5vLXdyYXAge3doaXRlLXNwYWNlOiBub3dyYXB9!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/string/string.widget.ts\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdStringWidgetComponent = class SwuiTdStringWidgetComponent {\n    constructor({ value, schema }) {\n        this.nowrap = false;\n        this.value = value;\n        this.truncate = schema.td && schema.td.truncate ? schema.td.truncate : undefined;\n        this.nowrap = schema.td && schema.td.nowrap || false;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdStringWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-string-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdStringWidgetComponent);\nexport { SwuiTdStringWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,oBAAoB,MAAM,kdAAkd;AACnf,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,2BAA2B,IAAAC,4BAAA,GAAG,MAAMD,2BAA2B,CAAC;EAChEE,WAAWA,CAAC;IAAEC,KAAK;IAAEC;EAAO,CAAC,EAAE;IAC3B,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,QAAQ,GAAGF,MAAM,CAACG,EAAE,IAAIH,MAAM,CAACG,EAAE,CAACD,QAAQ,GAAGF,MAAM,CAACG,EAAE,CAACD,QAAQ,GAAGE,SAAS;IAChF,IAAI,CAACH,MAAM,GAAGD,MAAM,CAACG,EAAE,IAAIH,MAAM,CAACG,EAAE,CAACF,MAAM,IAAI,KAAK;EACxD;AAIJ,CAAC,EAHYJ,4BAAA,CAAKQ,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEF,SAAS;EAAEG,UAAU,EAAE,CAAC;IAAED,IAAI,EAAEZ,MAAM;IAAEc,IAAI,EAAE,CAACb,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,4BAAA,CACJ;AACDD,2BAA2B,GAAGN,UAAU,CAAC,CACrCG,SAAS,CAAC;EACNgB,QAAQ,EAAE,2BAA2B;EACrCC,QAAQ,EAAEnB,oBAAoB;EAC9BoB,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACpB,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEI,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}