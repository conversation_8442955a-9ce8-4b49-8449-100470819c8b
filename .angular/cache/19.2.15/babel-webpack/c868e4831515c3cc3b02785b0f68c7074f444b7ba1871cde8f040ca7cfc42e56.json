{"ast": null, "code": "import { zip } from '../observable/zip';\nimport { joinAllInternals } from './joinAllInternals';\nexport function zipAll(project) {\n  return joinAllInternals(zip, project);\n}\n//# sourceMappingURL=zipAll.js.map", "map": {"version": 3, "names": ["zip", "joinAllInternals", "zipAll", "project"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/operators/zipAll.js"], "sourcesContent": ["import { zip } from '../observable/zip';\nimport { joinAllInternals } from './joinAllInternals';\nexport function zipAll(project) {\n    return joinAllInternals(zip, project);\n}\n//# sourceMappingURL=zipAll.js.map"], "mappings": "AAAA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,SAASC,MAAMA,CAACC,OAAO,EAAE;EAC5B,OAAOF,gBAAgB,CAACD,GAAG,EAAEG,OAAO,CAAC;AACzC;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}