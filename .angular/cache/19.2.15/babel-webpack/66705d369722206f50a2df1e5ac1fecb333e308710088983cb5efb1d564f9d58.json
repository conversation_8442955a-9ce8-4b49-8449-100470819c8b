{"ast": null, "code": "var isArray = Array.isArray;\nexport function argsOrArgArray(args) {\n  return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}\n//# sourceMappingURL=argsOrArgArray.js.map", "map": {"version": 3, "names": ["isArray", "Array", "argsOrArgArray", "args", "length"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/util/argsOrArgArray.js"], "sourcesContent": ["var isArray = Array.isArray;\nexport function argsOrArgArray(args) {\n    return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}\n//# sourceMappingURL=argsOrArgArray.js.map"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,KAAK,CAACD,OAAO;AAC3B,OAAO,SAASE,cAAcA,CAACC,IAAI,EAAE;EACjC,OAAOA,IAAI,CAACC,MAAM,KAAK,CAAC,IAAIJ,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI;AACjE;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}