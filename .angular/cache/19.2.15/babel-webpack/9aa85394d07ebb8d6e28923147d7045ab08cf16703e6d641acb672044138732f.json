{"ast": null, "code": "var _SwuiMatFormFieldControl;\nimport { __decorate } from \"tslib\";\nimport { FormGroupDirective, NgControl } from '@angular/forms';\nimport { Directive, ElementRef, HostBinding, Input } from '@angular/core';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { Subject } from 'rxjs';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nlet SwuiMatFormFieldControl = (_SwuiMatFormFieldControl = class SwuiMatFormFieldControl {\n  set required(required) {\n    this._required = coerceBooleanProperty(required);\n    this.stateChanges.next(undefined);\n  }\n  get required() {\n    return this._required;\n  }\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this.onDisabledState(value);\n    this.stateChanges.next(undefined);\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n    this.fm = fm;\n    this.elRef = elRef;\n    this.ngControl = ngControl;\n    this.parentFormGroup = parentFormGroup;\n    this.errorStateMatcher = errorStateMatcher;\n    this.placeholder = '';\n    this.describedBy = '';\n    this.errorState = false;\n    this.focused = false;\n    this.stateChanges = new Subject();\n    this.destroyed$ = new Subject();\n    this._required = false;\n    this._disabled = false;\n    this.onChange = _ => {};\n    this.onTouched = () => {};\n    if (this.ngControl !== null) {\n      this.ngControl.valueAccessor = this;\n    }\n    fm.monitor(elRef.nativeElement, true).subscribe(origin => {\n      if (this.focused && !origin) {\n        this.onTouched();\n      }\n      this.focused = !!origin;\n      this.stateChanges.next(undefined);\n    });\n  }\n  ngDoCheck() {\n    if (this.ngControl) {\n      const oldState = this.errorState;\n      const parent = this.parentFormGroup;\n      const matcher = this.errorStateMatcher;\n      const control = this.ngControl ? this.ngControl.control : null;\n      const newState = matcher.isErrorState(control, parent) || this.isErrorState();\n      if (newState !== oldState) {\n        this.errorState = newState;\n        this.stateChanges.next(undefined);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.fm.stopMonitoring(this.elRef.nativeElement);\n    this.stateChanges.complete();\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(value) {\n    this._disabled = value;\n    this.onDisabledState(value);\n  }\n  setDescribedByIds(ids) {\n    this.describedBy = ids.join(' ');\n  }\n  onDisabledState(_) {}\n  isErrorState() {\n    return false;\n  }\n}, _SwuiMatFormFieldControl.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl\n}, {\n  type: FormGroupDirective\n}, {\n  type: ErrorStateMatcher\n}], _SwuiMatFormFieldControl.propDecorators = {\n  placeholder: [{\n    type: Input\n  }],\n  required: [{\n    type: Input\n  }],\n  disabled: [{\n    type: Input\n  }],\n  describedBy: [{\n    type: HostBinding,\n    args: ['attr.aria-describedby']\n  }]\n}, _SwuiMatFormFieldControl);\nSwuiMatFormFieldControl = __decorate([Directive()\n// tslint:disable-next-line:directive-class-suffix\n], SwuiMatFormFieldControl);\nexport { SwuiMatFormFieldControl };", "map": {"version": 3, "names": ["FormGroupDirective", "NgControl", "Directive", "ElementRef", "HostBinding", "Input", "ErrorStateMatcher", "Subject", "FocusMonitor", "coerceBooleanProperty", "SwuiMatFormFieldControl", "_SwuiMatFormFieldControl", "required", "_required", "stateChanges", "next", "undefined", "disabled", "_disabled", "value", "onDisabledState", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "placeholder", "describedBy", "errorState", "focused", "destroyed$", "onChange", "_", "onTouched", "valueAccessor", "monitor", "nativeElement", "subscribe", "origin", "ngDoCheck", "oldState", "parent", "matcher", "control", "newState", "isErrorState", "ngOnDestroy", "stopMonitoring", "complete", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "setDescribedByIds", "ids", "join", "args", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/common/swui-mat-form-field-control.ts"], "sourcesContent": ["import { ControlValueAccessor, UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';\nimport { Directive, Do<PERSON><PERSON><PERSON>, ElementRef, HostBinding, Input, OnDestroy } from '@angular/core';\nimport { ErrorStateMatcher } from '@angular/material/core';\nimport { Subject } from 'rxjs';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\n\n@Directive()\n// tslint:disable-next-line:directive-class-suffix\nexport abstract class SwuiMatFormFieldControl<T> implements <PERSON><PERSON><PERSON><PERSON>, OnDestroy, ControlValueAccessor, MatFormFieldControl<T> {\n  abstract id: string;\n  abstract value: T | null;\n  abstract empty: boolean;\n  abstract shouldLabelFloat: boolean;\n\n  @Input() placeholder = '';\n\n  @Input()\n  set required( required: boolean ) {\n    this._required = coerceBooleanProperty(required);\n    this.stateChanges.next(undefined);\n  }\n\n  get required() {\n    return this._required;\n  }\n\n  @Input()\n  get disabled(): boolean {\n    return this._disabled;\n  }\n\n  set disabled( value: boolean ) {\n    this._disabled = coerceBooleanProperty(value);\n    this.onDisabledState(value);\n    this.stateChanges.next(undefined);\n  }\n\n  @HostBinding('attr.aria-describedby') describedBy = '';\n\n  controlType?: string;\n  autofilled?: boolean;\n  userAriaDescribedBy?: string;\n\n  errorState = false;\n  focused = false;\n\n  readonly stateChanges = new Subject<void>();\n  readonly destroyed$ = new Subject<void>();\n\n  private _required = false;\n  private _disabled = false;\n\n  onChange = ( _: any ) => {\n  };\n\n  onTouched = () => {\n  };\n\n  protected constructor( private readonly fm: FocusMonitor,\n                         readonly elRef: ElementRef<HTMLElement>,\n                         readonly ngControl: NgControl,\n                         private readonly parentFormGroup: FormGroupDirective,\n                         private readonly errorStateMatcher: ErrorStateMatcher ) {\n    if (this.ngControl !== null) {\n      this.ngControl.valueAccessor = this;\n    }\n    fm.monitor(elRef.nativeElement, true).subscribe(origin => {\n      if (this.focused && !origin) {\n        this.onTouched();\n      }\n      this.focused = !!origin;\n      this.stateChanges.next(undefined);\n    });\n  }\n\n  ngDoCheck() {\n    if (this.ngControl) {\n      const oldState = this.errorState;\n      const parent = this.parentFormGroup;\n      const matcher = this.errorStateMatcher;\n      const control = this.ngControl ? this.ngControl.control as UntypedFormControl : null;\n      const newState = matcher.isErrorState(control, parent) || this.isErrorState();\n      if (newState !== oldState) {\n        this.errorState = newState;\n        this.stateChanges.next(undefined);\n      }\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.fm.stopMonitoring(this.elRef.nativeElement);\n    this.stateChanges.complete();\n    this.destroyed$.next(undefined);\n    this.destroyed$.complete();\n  }\n\n  registerOnChange( fn: any ): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched( fn: any ): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState( value: boolean ) {\n    this._disabled = value;\n    this.onDisabledState(value);\n  }\n\n  abstract writeValue( value: any ): void;\n\n  setDescribedByIds( ids: string[] ) {\n    this.describedBy = ids.join(' ');\n  }\n\n  abstract onContainerClick( event: MouseEvent ): void;\n\n  protected onDisabledState( _: boolean ): void {\n  }\n\n  protected isErrorState(): boolean {\n    return false;\n  }\n}\n"], "mappings": ";;AAAA,SAAmDA,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AACxG,SAASC,SAAS,EAAWC,UAAU,EAAEC,WAAW,EAAEC,KAAK,QAAmB,eAAe;AAC7F,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,YAAY,QAAQ,mBAAmB;AAEhD,SAASC,qBAAqB,QAAQ,uBAAuB;AAItD,IAAeC,uBAAuB,IAAAC,wBAAA,GAAtC,MAAeD,uBAAuB;MASvCE,QAAQA,CAAEA,QAAiB;IAC7B,IAAI,CAACC,SAAS,GAAGJ,qBAAqB,CAACG,QAAQ,CAAC;IAChD,IAAI,CAACE,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;EACnC;EAEA,IAAIJ,QAAQA,CAAA;IACV,OAAO,IAAI,CAACC,SAAS;EACvB;MAGII,QAAQA,CAAA;IACV,OAAO,IAAI,CAACC,SAAS;EACvB;EAEA,IAAID,QAAQA,CAAEE,KAAc;IAC1B,IAAI,CAACD,SAAS,GAAGT,qBAAqB,CAACU,KAAK,CAAC;IAC7C,IAAI,CAACC,eAAe,CAACD,KAAK,CAAC;IAC3B,IAAI,CAACL,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;EACnC;EAuBAK,YAAwCC,EAAgB,EACxBC,KAA8B,EAC9BC,SAAoB,EACZC,eAAmC,EACnCC,iBAAoC;IAJpC,KAAAJ,EAAE,GAAFA,EAAE;IACV,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,SAAS,GAATA,SAAS;IACD,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAhDhD,KAAAC,WAAW,GAAG,EAAE;IAuBa,KAAAC,WAAW,GAAG,EAAE;IAMtD,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,KAAK;IAEN,KAAAhB,YAAY,GAAG,IAAIP,OAAO,EAAQ;IAClC,KAAAwB,UAAU,GAAG,IAAIxB,OAAO,EAAQ;IAEjC,KAAAM,SAAS,GAAG,KAAK;IACjB,KAAAK,SAAS,GAAG,KAAK;IAEzB,KAAAc,QAAQ,GAAKC,CAAM,IAAK,CACxB,CAAC;IAED,KAAAC,SAAS,GAAG,MAAK,CACjB,CAAC;IAOC,IAAI,IAAI,CAACV,SAAS,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACA,SAAS,CAACW,aAAa,GAAG,IAAI;IACrC;IACAb,EAAE,CAACc,OAAO,CAACb,KAAK,CAACc,aAAa,EAAE,IAAI,CAAC,CAACC,SAAS,CAACC,MAAM,IAAG;MACvD,IAAI,IAAI,CAACT,OAAO,IAAI,CAACS,MAAM,EAAE;QAC3B,IAAI,CAACL,SAAS,EAAE;MAClB;MACA,IAAI,CAACJ,OAAO,GAAG,CAAC,CAACS,MAAM;MACvB,IAAI,CAACzB,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAwB,SAASA,CAAA;IACP,IAAI,IAAI,CAAChB,SAAS,EAAE;MAClB,MAAMiB,QAAQ,GAAG,IAAI,CAACZ,UAAU;MAChC,MAAMa,MAAM,GAAG,IAAI,CAACjB,eAAe;MACnC,MAAMkB,OAAO,GAAG,IAAI,CAACjB,iBAAiB;MACtC,MAAMkB,OAAO,GAAG,IAAI,CAACpB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACoB,OAA6B,GAAG,IAAI;MACpF,MAAMC,QAAQ,GAAGF,OAAO,CAACG,YAAY,CAACF,OAAO,EAAEF,MAAM,CAAC,IAAI,IAAI,CAACI,YAAY,EAAE;MAC7E,IAAID,QAAQ,KAAKJ,QAAQ,EAAE;QACzB,IAAI,CAACZ,UAAU,GAAGgB,QAAQ;QAC1B,IAAI,CAAC/B,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;MACnC;IACF;EACF;EAEA+B,WAAWA,CAAA;IACT,IAAI,CAACzB,EAAE,CAAC0B,cAAc,CAAC,IAAI,CAACzB,KAAK,CAACc,aAAa,CAAC;IAChD,IAAI,CAACvB,YAAY,CAACmC,QAAQ,EAAE;IAC5B,IAAI,CAAClB,UAAU,CAAChB,IAAI,CAACC,SAAS,CAAC;IAC/B,IAAI,CAACe,UAAU,CAACkB,QAAQ,EAAE;EAC5B;EAEAC,gBAAgBA,CAAEC,EAAO;IACvB,IAAI,CAACnB,QAAQ,GAAGmB,EAAE;EACpB;EAEAC,iBAAiBA,CAAED,EAAO;IACxB,IAAI,CAACjB,SAAS,GAAGiB,EAAE;EACrB;EAEAE,gBAAgBA,CAAElC,KAAc;IAC9B,IAAI,CAACD,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACC,eAAe,CAACD,KAAK,CAAC;EAC7B;EAIAmC,iBAAiBA,CAAEC,GAAa;IAC9B,IAAI,CAAC3B,WAAW,GAAG2B,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC;EAClC;EAIUpC,eAAeA,CAAEa,CAAU,GACrC;EAEUa,YAAYA,CAAA;IACpB,OAAO,KAAK;EACd;;;;;;;;;;;;;UA5GCzC;EAAK;;UAELA;EAAK;;UAULA;EAAK;;UAWLD,WAAW;IAAAqD,IAAA,GAAC,uBAAuB;EAAA;;AA7BhB/C,uBAAuB,GAAAgD,UAAA,EAF5CxD,SAAS;AACV;AAAA,C,EACsBQ,uBAAuB,CAmH5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}