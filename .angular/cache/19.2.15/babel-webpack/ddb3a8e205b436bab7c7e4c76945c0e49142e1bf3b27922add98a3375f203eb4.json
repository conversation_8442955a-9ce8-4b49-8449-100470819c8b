{"ast": null, "code": "import { Observable } from '../Observable';\nimport { noop } from '../util/noop';\nexport var NEVER = new Observable(noop);\nexport function never() {\n  return NEVER;\n}\n//# sourceMappingURL=never.js.map", "map": {"version": 3, "names": ["Observable", "noop", "NEVER", "never"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/never.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { noop } from '../util/noop';\nexport var NEVER = new Observable(noop);\nexport function never() {\n    return NEVER;\n}\n//# sourceMappingURL=never.js.map"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAO,IAAIC,KAAK,GAAG,IAAIF,UAAU,CAACC,IAAI,CAAC;AACvC,OAAO,SAASE,KAAKA,CAAA,EAAG;EACpB,OAAOD,KAAK;AAChB;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}