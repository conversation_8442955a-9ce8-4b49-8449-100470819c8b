{"ast": null, "code": "var _SwuiMenuSelectComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-menu-select.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-menu-select.component.scss?ngResource\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { BehaviorSubject, combineLatest, Subject } from 'rxjs';\nimport { UntypedFormControl } from '@angular/forms';\nimport { filter, map, takeUntil } from 'rxjs/operators';\nlet SwuiMenuSelectComponent = (_SwuiMenuSelectComponent = class SwuiMenuSelectComponent {\n  set data(val) {\n    const value = val ? val : [];\n    this._data$.next(value);\n    this._filtered = value.map(item => item.id);\n  }\n  set selected(val) {\n    const processed = Array.isArray(val) ? val : [];\n    this._sourceSelected = processed;\n    this._selected$.next(processed);\n  }\n  constructor() {\n    this.searchPlaceholder = 'COMPONENTS.MENU_SELECT.search';\n    this.title = '';\n    this.showSearch = false;\n    this.selectAll = false;\n    this.applyData = new EventEmitter();\n    this.cancelClick = new EventEmitter();\n    this.searchControl = new UntypedFormControl();\n    this.processedData = [];\n    this._sourceSelected = [];\n    this._data$ = new BehaviorSubject([]);\n    this._selected$ = new BehaviorSubject([]);\n    this._filtered = [];\n    this._destroyed$ = new Subject();\n    combineLatest([this._data$, this._selected$]).pipe(takeUntil(this._destroyed$)).subscribe(([data, selected]) => {\n      const selectedSet = new Set(selected);\n      this.processedData = data.reduce((result, option) => {\n        option.data = {\n          checked: selectedSet.has(option.id)\n        };\n        return [...result, ...[option]];\n      }, []);\n    });\n  }\n  ngOnInit() {\n    this.initFilter();\n  }\n  clear(event) {\n    event.preventDefault();\n    this._selected$.next([]);\n  }\n  onSelectAll(event) {\n    event.preventDefault();\n    this._selected$.next(this.processedData.filter(({\n      disabled\n    }) => !disabled).map(({\n      id\n    }) => id));\n  }\n  cancel(event) {\n    event.preventDefault();\n    this._selected$.next(this._sourceSelected);\n    this.cancelClick.emit();\n  }\n  apply(event) {\n    event.preventDefault();\n    const resultData = this.processedData.filter(el => el.data.checked);\n    const output = resultData ? resultData.map(el => el.id) : [];\n    this.applyData.emit(output);\n  }\n  isFiltered(id) {\n    return this._filtered.indexOf(id) > -1;\n  }\n  get selectedLength() {\n    return this.processedData.filter(el => el.data.checked).length;\n  }\n  prevent(event) {\n    event.stopPropagation();\n  }\n  initFilter() {\n    this.searchControl.valueChanges.pipe(filter(() => this.showSearch), map(searchString => searchString.toLowerCase()), takeUntil(this._destroyed$)).subscribe(searchString => {\n      const processed = this.processedData.filter(option => {\n        return option.text && option.text.toLowerCase().indexOf(searchString) > -1;\n      });\n      this._filtered = processed ? processed.map(item => item.id) : [];\n    });\n  }\n}, _SwuiMenuSelectComponent.ctorParameters = () => [], _SwuiMenuSelectComponent.propDecorators = {\n  searchPlaceholder: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  showSearch: [{\n    type: Input\n  }],\n  selectAll: [{\n    type: Input\n  }],\n  data: [{\n    type: Input\n  }],\n  selected: [{\n    type: Input\n  }],\n  applyData: [{\n    type: Output\n  }],\n  cancelClick: [{\n    type: Output\n  }]\n}, _SwuiMenuSelectComponent);\nSwuiMenuSelectComponent = __decorate([Component({\n  selector: 'lib-swui-menu-select',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiMenuSelectComponent);\nexport { SwuiMenuSelectComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "EventEmitter", "Input", "Output", "BehaviorSubject", "combineLatest", "Subject", "UntypedFormControl", "filter", "map", "takeUntil", "SwuiMenuSelectComponent", "_SwuiMenuSelectComponent", "data", "val", "value", "_data$", "next", "_filtered", "item", "id", "selected", "processed", "Array", "isArray", "_sourceSelected", "_selected$", "constructor", "searchPlaceholder", "title", "showSearch", "selectAll", "applyData", "cancelClick", "searchControl", "processedData", "_destroyed$", "pipe", "subscribe", "selectedSet", "Set", "reduce", "result", "option", "checked", "has", "ngOnInit", "initFilter", "clear", "event", "preventDefault", "onSelectAll", "disabled", "cancel", "emit", "apply", "resultData", "el", "output", "isFiltered", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "length", "prevent", "stopPropagation", "valueChanges", "searchString", "toLowerCase", "text", "ctorParameters", "propDecorators", "type", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-menu-select/swui-menu-select.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-menu-select.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-menu-select.component.scss?ngResource\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { BehaviorSubject, combineLatest, Subject } from 'rxjs';\nimport { UntypedFormControl } from '@angular/forms';\nimport { filter, map, takeUntil } from 'rxjs/operators';\nlet SwuiMenuSelectComponent = class SwuiMenuSelectComponent {\n    set data(val) {\n        const value = val ? val : [];\n        this._data$.next(value);\n        this._filtered = value.map((item) => item.id);\n    }\n    set selected(val) {\n        const processed = Array.isArray(val) ? val : [];\n        this._sourceSelected = processed;\n        this._selected$.next(processed);\n    }\n    constructor() {\n        this.searchPlaceholder = 'COMPONENTS.MENU_SELECT.search';\n        this.title = '';\n        this.showSearch = false;\n        this.selectAll = false;\n        this.applyData = new EventEmitter();\n        this.cancelClick = new EventEmitter();\n        this.searchControl = new UntypedFormControl();\n        this.processedData = [];\n        this._sourceSelected = [];\n        this._data$ = new BehaviorSubject([]);\n        this._selected$ = new BehaviorSubject([]);\n        this._filtered = [];\n        this._destroyed$ = new Subject();\n        combineLatest([this._data$, this._selected$])\n            .pipe(takeUntil(this._destroyed$))\n            .subscribe(([data, selected]) => {\n            const selectedSet = new Set(selected);\n            this.processedData = data.reduce((result, option) => {\n                option.data = {\n                    checked: selectedSet.has(option.id)\n                };\n                return [...result, ...[option]];\n            }, []);\n        });\n    }\n    ngOnInit() {\n        this.initFilter();\n    }\n    clear(event) {\n        event.preventDefault();\n        this._selected$.next([]);\n    }\n    onSelectAll(event) {\n        event.preventDefault();\n        this._selected$.next(this.processedData.filter(({ disabled }) => !disabled).map(({ id }) => id));\n    }\n    cancel(event) {\n        event.preventDefault();\n        this._selected$.next(this._sourceSelected);\n        this.cancelClick.emit();\n    }\n    apply(event) {\n        event.preventDefault();\n        const resultData = this.processedData.filter((el) => el.data.checked);\n        const output = resultData ? resultData.map((el) => el.id) : [];\n        this.applyData.emit(output);\n    }\n    isFiltered(id) {\n        return this._filtered.indexOf(id) > -1;\n    }\n    get selectedLength() {\n        return this.processedData.filter((el) => el.data.checked).length;\n    }\n    prevent(event) {\n        event.stopPropagation();\n    }\n    initFilter() {\n        this.searchControl.valueChanges\n            .pipe(filter(() => this.showSearch), map((searchString) => searchString.toLowerCase()), takeUntil(this._destroyed$))\n            .subscribe((searchString) => {\n            const processed = this.processedData.filter((option) => {\n                return option.text && option.text.toLowerCase().indexOf(searchString) > -1;\n            });\n            this._filtered = processed ? processed.map((item) => item.id) : [];\n        });\n    }\n    static { this.ctorParameters = () => []; }\n    static { this.propDecorators = {\n        searchPlaceholder: [{ type: Input }],\n        title: [{ type: Input }],\n        showSearch: [{ type: Input }],\n        selectAll: [{ type: Input }],\n        data: [{ type: Input }],\n        selected: [{ type: Input }],\n        applyData: [{ type: Output }],\n        cancelClick: [{ type: Output }]\n    }; }\n};\nSwuiMenuSelectComponent = __decorate([\n    Component({\n        selector: 'lib-swui-menu-select',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiMenuSelectComponent);\nexport { SwuiMenuSelectComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,SAASC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AACtE,SAASC,eAAe,EAAEC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC9D,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AACvD,IAAIC,uBAAuB,IAAAC,wBAAA,GAAG,MAAMD,uBAAuB,CAAC;EACxD,IAAIE,IAAIA,CAACC,GAAG,EAAE;IACV,MAAMC,KAAK,GAAGD,GAAG,GAAGA,GAAG,GAAG,EAAE;IAC5B,IAAI,CAACE,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC;IACvB,IAAI,CAACG,SAAS,GAAGH,KAAK,CAACN,GAAG,CAAEU,IAAI,IAAKA,IAAI,CAACC,EAAE,CAAC;EACjD;EACA,IAAIC,QAAQA,CAACP,GAAG,EAAE;IACd,MAAMQ,SAAS,GAAGC,KAAK,CAACC,OAAO,CAACV,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;IAC/C,IAAI,CAACW,eAAe,GAAGH,SAAS;IAChC,IAAI,CAACI,UAAU,CAACT,IAAI,CAACK,SAAS,CAAC;EACnC;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,iBAAiB,GAAG,+BAA+B;IACxD,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI/B,YAAY,CAAC,CAAC;IACnC,IAAI,CAACgC,WAAW,GAAG,IAAIhC,YAAY,CAAC,CAAC;IACrC,IAAI,CAACiC,aAAa,GAAG,IAAI3B,kBAAkB,CAAC,CAAC;IAC7C,IAAI,CAAC4B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACV,eAAe,GAAG,EAAE;IACzB,IAAI,CAACT,MAAM,GAAG,IAAIZ,eAAe,CAAC,EAAE,CAAC;IACrC,IAAI,CAACsB,UAAU,GAAG,IAAItB,eAAe,CAAC,EAAE,CAAC;IACzC,IAAI,CAACc,SAAS,GAAG,EAAE;IACnB,IAAI,CAACkB,WAAW,GAAG,IAAI9B,OAAO,CAAC,CAAC;IAChCD,aAAa,CAAC,CAAC,IAAI,CAACW,MAAM,EAAE,IAAI,CAACU,UAAU,CAAC,CAAC,CACxCW,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAAC,CACjCE,SAAS,CAAC,CAAC,CAACzB,IAAI,EAAEQ,QAAQ,CAAC,KAAK;MACjC,MAAMkB,WAAW,GAAG,IAAIC,GAAG,CAACnB,QAAQ,CAAC;MACrC,IAAI,CAACc,aAAa,GAAGtB,IAAI,CAAC4B,MAAM,CAAC,CAACC,MAAM,EAAEC,MAAM,KAAK;QACjDA,MAAM,CAAC9B,IAAI,GAAG;UACV+B,OAAO,EAAEL,WAAW,CAACM,GAAG,CAACF,MAAM,CAACvB,EAAE;QACtC,CAAC;QACD,OAAO,CAAC,GAAGsB,MAAM,EAAE,GAAG,CAACC,MAAM,CAAC,CAAC;MACnC,CAAC,EAAE,EAAE,CAAC;IACV,CAAC,CAAC;EACN;EACAG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,KAAKA,CAACC,KAAK,EAAE;IACTA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACxB,UAAU,CAACT,IAAI,CAAC,EAAE,CAAC;EAC5B;EACAkC,WAAWA,CAACF,KAAK,EAAE;IACfA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACxB,UAAU,CAACT,IAAI,CAAC,IAAI,CAACkB,aAAa,CAAC3B,MAAM,CAAC,CAAC;MAAE4C;IAAS,CAAC,KAAK,CAACA,QAAQ,CAAC,CAAC3C,GAAG,CAAC,CAAC;MAAEW;IAAG,CAAC,KAAKA,EAAE,CAAC,CAAC;EACpG;EACAiC,MAAMA,CAACJ,KAAK,EAAE;IACVA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAI,CAACxB,UAAU,CAACT,IAAI,CAAC,IAAI,CAACQ,eAAe,CAAC;IAC1C,IAAI,CAACQ,WAAW,CAACqB,IAAI,CAAC,CAAC;EAC3B;EACAC,KAAKA,CAACN,KAAK,EAAE;IACTA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,MAAMM,UAAU,GAAG,IAAI,CAACrB,aAAa,CAAC3B,MAAM,CAAEiD,EAAE,IAAKA,EAAE,CAAC5C,IAAI,CAAC+B,OAAO,CAAC;IACrE,MAAMc,MAAM,GAAGF,UAAU,GAAGA,UAAU,CAAC/C,GAAG,CAAEgD,EAAE,IAAKA,EAAE,CAACrC,EAAE,CAAC,GAAG,EAAE;IAC9D,IAAI,CAACY,SAAS,CAACsB,IAAI,CAACI,MAAM,CAAC;EAC/B;EACAC,UAAUA,CAACvC,EAAE,EAAE;IACX,OAAO,IAAI,CAACF,SAAS,CAAC0C,OAAO,CAACxC,EAAE,CAAC,GAAG,CAAC,CAAC;EAC1C;EACA,IAAIyC,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC1B,aAAa,CAAC3B,MAAM,CAAEiD,EAAE,IAAKA,EAAE,CAAC5C,IAAI,CAAC+B,OAAO,CAAC,CAACkB,MAAM;EACpE;EACAC,OAAOA,CAACd,KAAK,EAAE;IACXA,KAAK,CAACe,eAAe,CAAC,CAAC;EAC3B;EACAjB,UAAUA,CAAA,EAAG;IACT,IAAI,CAACb,aAAa,CAAC+B,YAAY,CAC1B5B,IAAI,CAAC7B,MAAM,CAAC,MAAM,IAAI,CAACsB,UAAU,CAAC,EAAErB,GAAG,CAAEyD,YAAY,IAAKA,YAAY,CAACC,WAAW,CAAC,CAAC,CAAC,EAAEzD,SAAS,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAAC,CACnHE,SAAS,CAAE4B,YAAY,IAAK;MAC7B,MAAM5C,SAAS,GAAG,IAAI,CAACa,aAAa,CAAC3B,MAAM,CAAEmC,MAAM,IAAK;QACpD,OAAOA,MAAM,CAACyB,IAAI,IAAIzB,MAAM,CAACyB,IAAI,CAACD,WAAW,CAAC,CAAC,CAACP,OAAO,CAACM,YAAY,CAAC,GAAG,CAAC,CAAC;MAC9E,CAAC,CAAC;MACF,IAAI,CAAChD,SAAS,GAAGI,SAAS,GAAGA,SAAS,CAACb,GAAG,CAAEU,IAAI,IAAKA,IAAI,CAACC,EAAE,CAAC,GAAG,EAAE;IACtE,CAAC,CAAC;EACN;AAYJ,CAAC,EAXYR,wBAAA,CAAKyD,cAAc,GAAG,MAAM,EAAE,EAC9BzD,wBAAA,CAAK0D,cAAc,GAAG;EAC3B1C,iBAAiB,EAAE,CAAC;IAAE2C,IAAI,EAAErE;EAAM,CAAC,CAAC;EACpC2B,KAAK,EAAE,CAAC;IAAE0C,IAAI,EAAErE;EAAM,CAAC,CAAC;EACxB4B,UAAU,EAAE,CAAC;IAAEyC,IAAI,EAAErE;EAAM,CAAC,CAAC;EAC7B6B,SAAS,EAAE,CAAC;IAAEwC,IAAI,EAAErE;EAAM,CAAC,CAAC;EAC5BW,IAAI,EAAE,CAAC;IAAE0D,IAAI,EAAErE;EAAM,CAAC,CAAC;EACvBmB,QAAQ,EAAE,CAAC;IAAEkD,IAAI,EAAErE;EAAM,CAAC,CAAC;EAC3B8B,SAAS,EAAE,CAAC;IAAEuC,IAAI,EAAEpE;EAAO,CAAC,CAAC;EAC7B8B,WAAW,EAAE,CAAC;IAAEsC,IAAI,EAAEpE;EAAO,CAAC;AAClC,CAAC,EAAAS,wBAAA,CACJ;AACDD,uBAAuB,GAAGd,UAAU,CAAC,CACjCG,SAAS,CAAC;EACNwE,QAAQ,EAAE,sBAAsB;EAChCC,QAAQ,EAAE3E,oBAAoB;EAC9B4E,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC5E,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEY,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}