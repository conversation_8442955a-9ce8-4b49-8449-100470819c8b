{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Azerbaijani [az]\n//! author : topchiyev : https://github.com/topchiyev\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var suffixes = {\n    1: '-inci',\n    5: '-inci',\n    8: '-inci',\n    70: '-inci',\n    80: '-inci',\n    2: '-nci',\n    7: '-nci',\n    20: '-nci',\n    50: '-nci',\n    3: '-üncü',\n    4: '-üncü',\n    100: '-üncü',\n    6: '-ncı',\n    9: '-uncu',\n    10: '-uncu',\n    30: '-uncu',\n    60: '-ıncı',\n    90: '-ıncı'\n  };\n  var az = moment.defineLocale('az', {\n    months: 'yanvar_fevral_mart_aprel_may_iyun_iyul_avqust_sentyabr_oktyabr_noyabr_dekabr'.split('_'),\n    monthsShort: 'yan_fev_mar_apr_may_iyn_iyl_avq_sen_okt_noy_dek'.split('_'),\n    weekdays: 'Bazar_Bazar ertəsi_Çərşənbə axşamı_Çərşənbə_Cümə axşamı_Cümə_Şənbə'.split('_'),\n    weekdaysShort: 'Baz_BzE_ÇAx_Çər_CAx_Cüm_Şən'.split('_'),\n    weekdaysMin: 'Bz_BE_ÇA_Çə_CA_Cü_Şə'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[bugün saat] LT',\n      nextDay: '[sabah saat] LT',\n      nextWeek: '[gələn həftə] dddd [saat] LT',\n      lastDay: '[dünən] LT',\n      lastWeek: '[keçən həftə] dddd [saat] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s sonra',\n      past: '%s əvvəl',\n      s: 'bir neçə saniyə',\n      ss: '%d saniyə',\n      m: 'bir dəqiqə',\n      mm: '%d dəqiqə',\n      h: 'bir saat',\n      hh: '%d saat',\n      d: 'bir gün',\n      dd: '%d gün',\n      M: 'bir ay',\n      MM: '%d ay',\n      y: 'bir il',\n      yy: '%d il'\n    },\n    meridiemParse: /gecə|səhər|gündüz|axşam/,\n    isPM: function (input) {\n      return /^(gündüz|axşam)$/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'gecə';\n      } else if (hour < 12) {\n        return 'səhər';\n      } else if (hour < 17) {\n        return 'gündüz';\n      } else {\n        return 'axşam';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(ıncı|inci|nci|üncü|ncı|uncu)/,\n    ordinal: function (number) {\n      if (number === 0) {\n        // special case for zero\n        return number + '-ıncı';\n      }\n      var a = number % 10,\n        b = number % 100 - a,\n        c = number >= 100 ? 100 : null;\n      return number + (suffixes[a] || suffixes[b] || suffixes[c]);\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return az;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "suffixes", "az", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "meridiemParse", "isPM", "input", "test", "meridiem", "hour", "minute", "isLower", "dayOfMonthOrdinalParse", "ordinal", "number", "a", "b", "c", "week", "dow", "doy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/moment/locale/az.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Azerbaijani [az]\n//! author : topchiyev : https://github.com/topchiyev\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var suffixes = {\n        1: '-inci',\n        5: '-inci',\n        8: '-inci',\n        70: '-inci',\n        80: '-inci',\n        2: '-nci',\n        7: '-nci',\n        20: '-nci',\n        50: '-nci',\n        3: '-üncü',\n        4: '-üncü',\n        100: '-üncü',\n        6: '-ncı',\n        9: '-uncu',\n        10: '-uncu',\n        30: '-uncu',\n        60: '-ıncı',\n        90: '-ıncı',\n    };\n\n    var az = moment.defineLocale('az', {\n        months: 'yanvar_fevral_mart_aprel_may_iyun_iyul_avqust_sentyabr_oktyabr_noyabr_dekabr'.split(\n            '_'\n        ),\n        monthsShort: 'yan_fev_mar_apr_may_iyn_iyl_avq_sen_okt_noy_dek'.split('_'),\n        weekdays: 'Bazar_Bazar ertəsi_Çərşənbə axşamı_Çərşənbə_Cümə axşamı_Cümə_Şənbə'.split(\n            '_'\n        ),\n        weekdaysShort: 'Baz_BzE_ÇAx_Çər_CAx_Cüm_Şən'.split('_'),\n        weekdaysMin: 'Bz_BE_ÇA_Çə_CA_Cü_Şə'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[bugün saat] LT',\n            nextDay: '[sabah saat] LT',\n            nextWeek: '[gələn həftə] dddd [saat] LT',\n            lastDay: '[dünən] LT',\n            lastWeek: '[keçən həftə] dddd [saat] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s sonra',\n            past: '%s əvvəl',\n            s: 'bir neçə saniyə',\n            ss: '%d saniyə',\n            m: 'bir dəqiqə',\n            mm: '%d dəqiqə',\n            h: 'bir saat',\n            hh: '%d saat',\n            d: 'bir gün',\n            dd: '%d gün',\n            M: 'bir ay',\n            MM: '%d ay',\n            y: 'bir il',\n            yy: '%d il',\n        },\n        meridiemParse: /gecə|səhər|gündüz|axşam/,\n        isPM: function (input) {\n            return /^(gündüz|axşam)$/.test(input);\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'gecə';\n            } else if (hour < 12) {\n                return 'səhər';\n            } else if (hour < 17) {\n                return 'gündüz';\n            } else {\n                return 'axşam';\n            }\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}-(ıncı|inci|nci|üncü|ncı|uncu)/,\n        ordinal: function (number) {\n            if (number === 0) {\n                // special case for zero\n                return number + '-ıncı';\n            }\n            var a = number % 10,\n                b = (number % 100) - a,\n                c = number >= 100 ? 100 : null;\n            return number + (suffixes[a] || suffixes[b] || suffixes[c]);\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return az;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,QAAQ,GAAG;IACX,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,OAAO;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,MAAM;IACT,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,MAAM;IACV,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,OAAO;IACV,GAAG,EAAE,OAAO;IACZ,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,OAAO;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,EAAE,EAAE;EACR,CAAC;EAED,IAAIC,EAAE,GAAGF,MAAM,CAACG,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,8EAA8E,CAACC,KAAK,CACxF,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,oEAAoE,CAACF,KAAK,CAChF,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,8BAA8B;MACxCC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE,8BAA8B;MACxCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,iBAAiB;MACpBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,aAAa,EAAE,yBAAyB;IACxCC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,kBAAkB,CAACC,IAAI,CAACD,KAAK,CAAC;IACzC,CAAC;IACDE,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO;MAClB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,QAAQ;MACnB,CAAC,MAAM;QACH,OAAO,OAAO;MAClB;IACJ,CAAC;IACDG,sBAAsB,EAAE,uCAAuC;IAC/DC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd;QACA,OAAOA,MAAM,GAAG,OAAO;MAC3B;MACA,IAAIC,CAAC,GAAGD,MAAM,GAAG,EAAE;QACfE,CAAC,GAAIF,MAAM,GAAG,GAAG,GAAIC,CAAC;QACtBE,CAAC,GAAGH,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI;MAClC,OAAOA,MAAM,IAAIjD,QAAQ,CAACkD,CAAC,CAAC,IAAIlD,QAAQ,CAACmD,CAAC,CAAC,IAAInD,QAAQ,CAACoD,CAAC,CAAC,CAAC;IAC/D,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOtD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}