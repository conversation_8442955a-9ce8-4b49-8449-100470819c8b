{"ast": null, "code": "var _SwuiSearchComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-search.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-search.component.scss?ngResource\";\nimport { Component, forwardRef, Input, ViewChild } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet SwuiSearchComponent = (_SwuiSearchComponent = class SwuiSearchComponent {\n  constructor() {\n    this.label = '';\n    this.placeholder = '';\n    this.fields = '';\n    this.disabled = false;\n    this.onChange = () => {};\n    this.onTouch = () => {};\n  }\n  writeValue(value) {\n    if (this.input) {\n      this.input.nativeElement.value = value && value.text || '';\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouch = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  onInputChange(value) {\n    if (!value) {\n      this.onChange(null);\n      return;\n    }\n    this.onChange({\n      text: value,\n      fields: this.fields\n    });\n  }\n}, _SwuiSearchComponent.propDecorators = {\n  label: [{\n    type: Input\n  }],\n  placeholder: [{\n    type: Input\n  }],\n  input: [{\n    type: ViewChild,\n    args: ['input', {\n      static: true\n    }]\n  }],\n  color: [{\n    type: Input\n  }],\n  floatLabel: [{\n    type: Input\n  }],\n  hideRequiredMarker: [{\n    type: Input\n  }],\n  hintLabel: [{\n    type: Input\n  }],\n  fields: [{\n    type: Input\n  }]\n}, _SwuiSearchComponent);\nSwuiSearchComponent = __decorate([Component({\n  selector: 'lib-swui-search',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SwuiSearchComponent),\n    multi: true\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiSearchComponent);\nexport { SwuiSearchComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "forwardRef", "Input", "ViewChild", "NG_VALUE_ACCESSOR", "SwuiSearchComponent", "_SwuiSearchComponent", "constructor", "label", "placeholder", "fields", "disabled", "onChange", "onTouch", "writeValue", "value", "input", "nativeElement", "text", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "onInputChange", "propDecorators", "type", "args", "static", "color", "floatLabel", "hideRequiredMarker", "hintLabel", "selector", "template", "providers", "provide", "useExisting", "multi", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-search/swui-search.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-search.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-search.component.scss?ngResource\";\nimport { Component, forwardRef, Input, ViewChild } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet SwuiSearchComponent = class SwuiSearchComponent {\n    constructor() {\n        this.label = '';\n        this.placeholder = '';\n        this.fields = '';\n        this.disabled = false;\n        this.onChange = () => {\n        };\n        this.onTouch = () => {\n        };\n    }\n    writeValue(value) {\n        if (this.input) {\n            this.input.nativeElement.value = value && value.text || '';\n        }\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouch = fn;\n    }\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    onInputChange(value) {\n        if (!value) {\n            this.onChange(null);\n            return;\n        }\n        this.onChange({\n            text: value,\n            fields: this.fields\n        });\n    }\n    static { this.propDecorators = {\n        label: [{ type: Input }],\n        placeholder: [{ type: Input }],\n        input: [{ type: ViewChild, args: ['input', { static: true },] }],\n        color: [{ type: Input }],\n        floatLabel: [{ type: Input }],\n        hideRequiredMarker: [{ type: Input }],\n        hintLabel: [{ type: Input }],\n        fields: [{ type: Input }]\n    }; }\n};\nSwuiSearchComponent = __decorate([\n    Component({\n        selector: 'lib-swui-search',\n        template: __NG_CLI_RESOURCE__0,\n        providers: [\n            {\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => SwuiSearchComponent),\n                multi: true,\n            }\n        ],\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiSearchComponent);\nexport { SwuiSearchComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,SAASC,SAAS,EAAEC,UAAU,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AACvE,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,IAAIC,mBAAmB,IAAAC,oBAAA,GAAG,MAAMD,mBAAmB,CAAC;EAChDE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,MAAM,CACtB,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM,CACrB,CAAC;EACL;EACAC,UAAUA,CAACC,KAAK,EAAE;IACd,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACC,aAAa,CAACF,KAAK,GAAGA,KAAK,IAAIA,KAAK,CAACG,IAAI,IAAI,EAAE;IAC9D;EACJ;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACR,QAAQ,GAAGQ,EAAE;EACtB;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACP,OAAO,GAAGO,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACZ,QAAQ,GAAGY,UAAU;EAC9B;EACAC,aAAaA,CAACT,KAAK,EAAE;IACjB,IAAI,CAACA,KAAK,EAAE;MACR,IAAI,CAACH,QAAQ,CAAC,IAAI,CAAC;MACnB;IACJ;IACA,IAAI,CAACA,QAAQ,CAAC;MACVM,IAAI,EAAEH,KAAK;MACXL,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC;EACN;AAWJ,CAAC,EAVYJ,oBAAA,CAAKmB,cAAc,GAAG;EAC3BjB,KAAK,EAAE,CAAC;IAAEkB,IAAI,EAAExB;EAAM,CAAC,CAAC;EACxBO,WAAW,EAAE,CAAC;IAAEiB,IAAI,EAAExB;EAAM,CAAC,CAAC;EAC9Bc,KAAK,EAAE,CAAC;IAAEU,IAAI,EAAEvB,SAAS;IAAEwB,IAAI,EAAE,CAAC,OAAO,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC,CAAC;EAChEC,KAAK,EAAE,CAAC;IAAEH,IAAI,EAAExB;EAAM,CAAC,CAAC;EACxB4B,UAAU,EAAE,CAAC;IAAEJ,IAAI,EAAExB;EAAM,CAAC,CAAC;EAC7B6B,kBAAkB,EAAE,CAAC;IAAEL,IAAI,EAAExB;EAAM,CAAC,CAAC;EACrC8B,SAAS,EAAE,CAAC;IAAEN,IAAI,EAAExB;EAAM,CAAC,CAAC;EAC5BQ,MAAM,EAAE,CAAC;IAAEgB,IAAI,EAAExB;EAAM,CAAC;AAC5B,CAAC,EAAAI,oBAAA,CACJ;AACDD,mBAAmB,GAAGR,UAAU,CAAC,CAC7BG,SAAS,CAAC;EACNiC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAEpC,oBAAoB;EAC9BqC,SAAS,EAAE,CACP;IACIC,OAAO,EAAEhC,iBAAiB;IAC1BiC,WAAW,EAAEpC,UAAU,CAAC,MAAMI,mBAAmB,CAAC;IAClDiC,KAAK,EAAE;EACX,CAAC,CACJ;EACDC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACzC,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEM,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}