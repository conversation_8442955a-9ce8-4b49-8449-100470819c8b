{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { SwuiGridLoadingOverlayComponent } from './loading-overlay.component';\ndescribe('SwuiGridLoadingOverlayComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SwuiGridLoadingOverlayComponent]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiGridLoadingOverlayComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "SwuiGridLoadingOverlayComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "declarations", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/loading-overlay/loading-overlay.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\n\nimport { SwuiGridLoadingOverlayComponent } from './loading-overlay.component';\n\ndescribe('SwuiGridLoadingOverlayComponent', () => {\n  let component: SwuiGridLoadingOverlayComponent;\n  let fixture: ComponentFixture<SwuiGridLoadingOverlayComponent>;\n\n  beforeEach(waitForAsync(() => {\n    TestBed.configureTestingModule({\n      declarations: [SwuiGridLoadingOverlayComponent]\n    }).compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiGridLoadingOverlayComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAE/E,SAASC,+BAA+B,QAAQ,6BAA6B;AAE7EC,QAAQ,CAAC,iCAAiC,EAAE,MAAK;EAC/C,IAAIC,SAA0C;EAC9C,IAAIC,OAA0D;EAE9DC,UAAU,CAACL,YAAY,CAAC,MAAK;IAC3BD,OAAO,CAACO,sBAAsB,CAAC;MAC7BC,YAAY,EAAE,CAACN,+BAA+B;KAC/C,CAAC,CAACO,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHH,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGL,OAAO,CAACU,eAAe,CAACR,+BAA+B,CAAC;IAClEE,SAAS,GAAGC,OAAO,CAACM,iBAAiB;IACrCN,OAAO,CAACO,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACV,SAAS,CAAC,CAACW,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}