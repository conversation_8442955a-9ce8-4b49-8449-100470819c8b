{"ast": null, "code": "var _ConfirmDialogComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./confirm-dialog.component.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nlet ConfirmDialogComponent = (_ConfirmDialogComponent = class ConfirmDialogComponent {\n  constructor(data) {\n    this.declineAction = data.declineAction;\n    this.runAction = data.runAction;\n  }\n}, _ConfirmDialogComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [MAT_DIALOG_DATA]\n  }]\n}], _ConfirmDialogComponent);\nConfirmDialogComponent = __decorate([Component({\n  selector: 'lib-swui-confirm-dialog',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], ConfirmDialogComponent);\nexport { ConfirmDialogComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "MAT_DIALOG_DATA", "ConfirmDialogComponent", "_ConfirmDialogComponent", "constructor", "data", "declineAction", "runAction", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/bulk-actions/dialogs/confirm-dialog.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./confirm-dialog.component.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nlet ConfirmDialogComponent = class ConfirmDialogComponent {\n    constructor(data) {\n        this.declineAction = data.declineAction;\n        this.runAction = data.runAction;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [MAT_DIALOG_DATA,] }] }\n    ]; }\n};\nConfirmDialogComponent = __decorate([\n    Component({\n        selector: 'lib-swui-confirm-dialog',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], ConfirmDialogComponent);\nexport { ConfirmDialogComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,IAAIC,sBAAsB,IAAAC,uBAAA,GAAG,MAAMD,sBAAsB,CAAC;EACtDE,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACC,aAAa,GAAGD,IAAI,CAACC,aAAa;IACvC,IAAI,CAACC,SAAS,GAAGF,IAAI,CAACE,SAAS;EACnC;AAIJ,CAAC,EAHYJ,uBAAA,CAAKK,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAET,MAAM;IAAEY,IAAI,EAAE,CAACX,eAAe;EAAG,CAAC;AAAE,CAAC,CAChF,EAAAE,uBAAA,CACJ;AACDD,sBAAsB,GAAGL,UAAU,CAAC,CAChCE,SAAS,CAAC;EACNc,QAAQ,EAAE,yBAAyB;EACnCC,QAAQ,EAAEhB,oBAAoB;EAC9BiB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEb,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}