{"ast": null, "code": "var _GameCoeffColumnComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./game-coeff-column.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/game-coeff-column.component.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICBzZWxlY3QgewogICAgICBoZWlnaHQ6IDI0cHg7CiAgICB9CgogICAgLmlucHV0LXh4cyB7CiAgICAgIGhlaWdodDogMjRweDsKICAgICAgcGFkZGluZzogMnB4IDVweDsKICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICBsaW5lLWhlaWdodDogMS42NjY2NjY3OwogICAgICBib3JkZXItcmFkaXVzOiAycHg7CiAgICB9CiAg!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/game-coeff-column.component.ts\";\nimport { Component, ViewChild } from '@angular/core';\nimport { DefaultColumnComponent } from './default-column.component';\nlet GameCoeffColumnComponent = (_GameCoeffColumnComponent = class GameCoeffColumnComponent extends DefaultColumnComponent {\n  constructor() {\n    super(...arguments);\n    this.max = 1;\n    this.min = 0.01;\n    this.step = 0.01;\n  }\n  isValueChanged() {\n    return this.coeff !== this.defaultCoeff && !!this.coeff;\n  }\n  get coeff() {\n    return this.game && this.game.coeff;\n  }\n  set coeff(value) {\n    value = this.sanitizeValue(value);\n    if (this.game) {\n      this.game.coeff = value;\n    }\n    if (this.input) {\n      this.input.nativeElement.value = value;\n    }\n    if (this.valueChange) {\n      this.valueChange.emit(value);\n    }\n  }\n  get defaultCoeff() {\n    return this.params['defaultCoeff'] || 1;\n  }\n  valueChanged({\n    target\n  }) {\n    this.coeff = target && 'value' in target ? target.value : this.input && this.input.nativeElement.value;\n  }\n  sanitizeValue(value) {\n    if (value === undefined) {\n      value = this.min;\n    }\n    if (isNaN(value)) {\n      value = this.min;\n    }\n    if (value > this.max) {\n      value = this.max;\n    }\n    if (value < this.min) {\n      value = this.min;\n    }\n    return value;\n  }\n}, _GameCoeffColumnComponent.propDecorators = {\n  input: [{\n    type: ViewChild,\n    args: ['input', {\n      static: true\n    }]\n  }]\n}, _GameCoeffColumnComponent);\nGameCoeffColumnComponent = __decorate([Component({\n  // tslint:disable-next-line:component-selector\n  selector: 'sw-game-coeff-column',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], GameCoeffColumnComponent);\nexport { GameCoeffColumnComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "ViewChild", "DefaultColumnComponent", "GameCoeffColumnComponent", "_GameCoeffColumnComponent", "constructor", "arguments", "max", "min", "step", "isValueChanged", "coeff", "defaultCoeff", "game", "value", "sanitizeValue", "input", "nativeElement", "valueChange", "emit", "params", "valueChanged", "target", "undefined", "isNaN", "propDecorators", "type", "args", "static", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/game-coeff-column.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./game-coeff-column.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/game-coeff-column.component.ts.css?ngResource!=!/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular-devkit/build-angular/node_modules/@ngtools/webpack/src/loaders/inline-resource.js?data=CiAgICBzZWxlY3QgewogICAgICBoZWlnaHQ6IDI0cHg7CiAgICB9CgogICAgLmlucHV0LXh4cyB7CiAgICAgIGhlaWdodDogMjRweDsKICAgICAgcGFkZGluZzogMnB4IDVweDsKICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICBsaW5lLWhlaWdodDogMS42NjY2NjY3OwogICAgICBib3JkZXItcmFkaXVzOiAycHg7CiAgICB9CiAg!/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-games-select-manager/game-select-columns/game-coeff-column.component.ts\";\nimport { Component, ViewChild } from '@angular/core';\nimport { DefaultColumnComponent } from './default-column.component';\nlet GameCoeffColumnComponent = class GameCoeffColumnComponent extends DefaultColumnComponent {\n    constructor() {\n        super(...arguments);\n        this.max = 1;\n        this.min = 0.01;\n        this.step = 0.01;\n    }\n    isValueChanged() {\n        return this.coeff !== this.defaultCoeff && !!this.coeff;\n    }\n    get coeff() {\n        return this.game && this.game.coeff;\n    }\n    set coeff(value) {\n        value = this.sanitizeValue(value);\n        if (this.game) {\n            this.game.coeff = value;\n        }\n        if (this.input) {\n            this.input.nativeElement.value = value;\n        }\n        if (this.valueChange) {\n            this.valueChange.emit(value);\n        }\n    }\n    get defaultCoeff() {\n        return this.params['defaultCoeff'] || 1;\n    }\n    valueChanged({ target }) {\n        this.coeff = target && ('value' in target) ? target.value : this.input && this.input.nativeElement.value;\n    }\n    sanitizeValue(value) {\n        if (value === undefined) {\n            value = this.min;\n        }\n        if (isNaN(value)) {\n            value = this.min;\n        }\n        if (value > this.max) {\n            value = this.max;\n        }\n        if (value < this.min) {\n            value = this.min;\n        }\n        return value;\n    }\n    static { this.propDecorators = {\n        input: [{ type: ViewChild, args: ['input', { static: true },] }]\n    }; }\n};\nGameCoeffColumnComponent = __decorate([\n    Component({\n        // tslint:disable-next-line:component-selector\n        selector: 'sw-game-coeff-column',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], GameCoeffColumnComponent);\nexport { GameCoeffColumnComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,OAAOC,oBAAoB,MAAM,0tBAA0tB;AAC3vB,SAASC,SAAS,EAAEC,SAAS,QAAQ,eAAe;AACpD,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,IAAIC,wBAAwB,IAAAC,yBAAA,GAAG,MAAMD,wBAAwB,SAASD,sBAAsB,CAAC;EACzFG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,IAAI,GAAG,IAAI;EACpB;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,KAAK,KAAK,IAAI,CAACC,YAAY,IAAI,CAAC,CAAC,IAAI,CAACD,KAAK;EAC3D;EACA,IAAIA,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACE,IAAI,IAAI,IAAI,CAACA,IAAI,CAACF,KAAK;EACvC;EACA,IAAIA,KAAKA,CAACG,KAAK,EAAE;IACbA,KAAK,GAAG,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC;IACjC,IAAI,IAAI,CAACD,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACF,KAAK,GAAGG,KAAK;IAC3B;IACA,IAAI,IAAI,CAACE,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACC,aAAa,CAACH,KAAK,GAAGA,KAAK;IAC1C;IACA,IAAI,IAAI,CAACI,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACC,IAAI,CAACL,KAAK,CAAC;IAChC;EACJ;EACA,IAAIF,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACQ,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;EAC3C;EACAC,YAAYA,CAAC;IAAEC;EAAO,CAAC,EAAE;IACrB,IAAI,CAACX,KAAK,GAAGW,MAAM,IAAK,OAAO,IAAIA,MAAO,GAAGA,MAAM,CAACR,KAAK,GAAG,IAAI,CAACE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,aAAa,CAACH,KAAK;EAC5G;EACAC,aAAaA,CAACD,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAKS,SAAS,EAAE;MACrBT,KAAK,GAAG,IAAI,CAACN,GAAG;IACpB;IACA,IAAIgB,KAAK,CAACV,KAAK,CAAC,EAAE;MACdA,KAAK,GAAG,IAAI,CAACN,GAAG;IACpB;IACA,IAAIM,KAAK,GAAG,IAAI,CAACP,GAAG,EAAE;MAClBO,KAAK,GAAG,IAAI,CAACP,GAAG;IACpB;IACA,IAAIO,KAAK,GAAG,IAAI,CAACN,GAAG,EAAE;MAClBM,KAAK,GAAG,IAAI,CAACN,GAAG;IACpB;IACA,OAAOM,KAAK;EAChB;AAIJ,CAAC,EAHYV,yBAAA,CAAKqB,cAAc,GAAG;EAC3BT,KAAK,EAAE,CAAC;IAAEU,IAAI,EAAEzB,SAAS;IAAE0B,IAAI,EAAE,CAAC,OAAO,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAC;EAAG,CAAC;AACnE,CAAC,EAAAxB,yBAAA,CACJ;AACDD,wBAAwB,GAAGN,UAAU,CAAC,CAClCG,SAAS,CAAC;EACN;EACA6B,QAAQ,EAAE,sBAAsB;EAChCC,QAAQ,EAAEhC,oBAAoB;EAC9BiC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACjC,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEI,wBAAwB,CAAC;AAC5B,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}