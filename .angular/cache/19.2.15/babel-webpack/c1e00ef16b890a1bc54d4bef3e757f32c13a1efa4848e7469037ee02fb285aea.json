{"ast": null, "code": "export class SwHubEntityDataSource {}", "map": {"version": 3, "names": ["SwHubEntityDataSource"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-hub-entity/sw-hub-entity-data-source.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\nimport { SwHubBriefEntity, SwHubShortEntity } from './sw-hub-entity.model';\n\nexport abstract class SwHubEntityDataSource {\n\n  abstract getBrief(): Observable<SwHubBriefEntity | null>;\n\n  abstract getEntity(): Observable<SwHubShortEntity | null>;\n}\n"], "mappings": "AAGA,OAAM,MAAgBA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}