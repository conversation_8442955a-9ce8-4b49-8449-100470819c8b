{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./loading-overlay.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./loading-overlay.component.scss?ngResource\";\nimport { Component } from '@angular/core';\nlet SwuiGridLoadingOverlayComponent = class SwuiGridLoadingOverlayComponent {};\nSwuiGridLoadingOverlayComponent = __decorate([Component({\n  selector: 'lib-swui-grid-loading-overlay',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiGridLoadingOverlayComponent);\nexport { SwuiGridLoadingOverlayComponent };", "map": {"version": 3, "names": ["Component", "SwuiGridLoadingOverlayComponent", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/loading-overlay/loading-overlay.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n    selector: 'lib-swui-grid-loading-overlay',\n    templateUrl: './loading-overlay.component.html',\n    styleUrls: ['./loading-overlay.component.scss'],\n    standalone: false\n})\nexport class SwuiGridLoadingOverlayComponent {\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAe;AAQlC,IAAMC,+BAA+B,GAArC,MAAMA,+BAA+B,GAC3C;AADYA,+BAA+B,GAAAC,UAAA,EAN3CF,SAAS,CAAC;EACPG,QAAQ,EAAE,+BAA+B;EACzCC,QAAA,EAAAC,oBAA+C;EAE/CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWL,+BAA+B,CAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}