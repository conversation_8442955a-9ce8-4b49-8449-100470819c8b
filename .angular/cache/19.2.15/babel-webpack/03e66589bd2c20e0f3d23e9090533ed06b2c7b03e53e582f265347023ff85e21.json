{"ast": null, "code": "//! moment-timezone.js\n//! version : 0.5.32\n//! Copyright (c) JS Foundation and other contributors\n//! license : MIT\n//! github.com/moment/moment-timezone\n\n(function (root, factory) {\n  \"use strict\";\n\n  /*global define*/\n  if (typeof module === 'object' && module.exports) {\n    module.exports = factory(require('moment')); // Node\n  } else if (typeof define === 'function' && define.amd) {\n    define(['moment'], factory); // AMD\n  } else {\n    factory(root.moment); // Browser\n  }\n})(this, function (moment) {\n  \"use strict\";\n\n  // Resolves es6 module loading issue\n  if (moment.version === undefined && moment.default) {\n    moment = moment.default;\n  }\n\n  // Do not load moment-timezone a second time.\n  // if (moment.tz !== undefined) {\n  // \tlogError('Moment Timezone ' + moment.tz.version + ' was already loaded ' + (moment.tz.dataVersion ? 'with data from ' : 'without any data') + moment.tz.dataVersion);\n  // \treturn moment;\n  // }\n\n  var VERSION = \"0.5.32\",\n    zones = {},\n    links = {},\n    countries = {},\n    names = {},\n    guesses = {},\n    cachedGuess;\n  if (!moment || typeof moment.version !== 'string') {\n    logError('Moment Timezone requires Moment.js. See https://momentjs.com/timezone/docs/#/use-it/browser/');\n  }\n  var momentVersion = moment.version.split('.'),\n    major = +momentVersion[0],\n    minor = +momentVersion[1];\n\n  // Moment.js version check\n  if (major < 2 || major === 2 && minor < 6) {\n    logError('Moment Timezone requires Moment.js >= 2.6.0. You are using Moment.js ' + moment.version + '. See momentjs.com');\n  }\n\n  /************************************\n  \tUnpacking\n  ************************************/\n\n  function charCodeToInt(charCode) {\n    if (charCode > 96) {\n      return charCode - 87;\n    } else if (charCode > 64) {\n      return charCode - 29;\n    }\n    return charCode - 48;\n  }\n  function unpackBase60(string) {\n    var i = 0,\n      parts = string.split('.'),\n      whole = parts[0],\n      fractional = parts[1] || '',\n      multiplier = 1,\n      num,\n      out = 0,\n      sign = 1;\n\n    // handle negative numbers\n    if (string.charCodeAt(0) === 45) {\n      i = 1;\n      sign = -1;\n    }\n\n    // handle digits before the decimal\n    for (i; i < whole.length; i++) {\n      num = charCodeToInt(whole.charCodeAt(i));\n      out = 60 * out + num;\n    }\n\n    // handle digits after the decimal\n    for (i = 0; i < fractional.length; i++) {\n      multiplier = multiplier / 60;\n      num = charCodeToInt(fractional.charCodeAt(i));\n      out += num * multiplier;\n    }\n    return out * sign;\n  }\n  function arrayToInt(array) {\n    for (var i = 0; i < array.length; i++) {\n      array[i] = unpackBase60(array[i]);\n    }\n  }\n  function intToUntil(array, length) {\n    for (var i = 0; i < length; i++) {\n      array[i] = Math.round((array[i - 1] || 0) + array[i] * 60000); // minutes to milliseconds\n    }\n    array[length - 1] = Infinity;\n  }\n  function mapIndices(source, indices) {\n    var out = [],\n      i;\n    for (i = 0; i < indices.length; i++) {\n      out[i] = source[indices[i]];\n    }\n    return out;\n  }\n  function unpack(string) {\n    var data = string.split('|'),\n      offsets = data[2].split(' '),\n      indices = data[3].split(''),\n      untils = data[4].split(' ');\n    arrayToInt(offsets);\n    arrayToInt(indices);\n    arrayToInt(untils);\n    intToUntil(untils, indices.length);\n    return {\n      name: data[0],\n      abbrs: mapIndices(data[1].split(' '), indices),\n      offsets: mapIndices(offsets, indices),\n      untils: untils,\n      population: data[5] | 0\n    };\n  }\n\n  /************************************\n  \tZone object\n  ************************************/\n\n  function Zone(packedString) {\n    if (packedString) {\n      this._set(unpack(packedString));\n    }\n  }\n  Zone.prototype = {\n    _set: function (unpacked) {\n      this.name = unpacked.name;\n      this.abbrs = unpacked.abbrs;\n      this.untils = unpacked.untils;\n      this.offsets = unpacked.offsets;\n      this.population = unpacked.population;\n    },\n    _index: function (timestamp) {\n      var target = +timestamp,\n        untils = this.untils,\n        i;\n      for (i = 0; i < untils.length; i++) {\n        if (target < untils[i]) {\n          return i;\n        }\n      }\n    },\n    countries: function () {\n      var zone_name = this.name;\n      return Object.keys(countries).filter(function (country_code) {\n        return countries[country_code].zones.indexOf(zone_name) !== -1;\n      });\n    },\n    parse: function (timestamp) {\n      var target = +timestamp,\n        offsets = this.offsets,\n        untils = this.untils,\n        max = untils.length - 1,\n        offset,\n        offsetNext,\n        offsetPrev,\n        i;\n      for (i = 0; i < max; i++) {\n        offset = offsets[i];\n        offsetNext = offsets[i + 1];\n        offsetPrev = offsets[i ? i - 1 : i];\n        if (offset < offsetNext && tz.moveAmbiguousForward) {\n          offset = offsetNext;\n        } else if (offset > offsetPrev && tz.moveInvalidForward) {\n          offset = offsetPrev;\n        }\n        if (target < untils[i] - offset * 60000) {\n          return offsets[i];\n        }\n      }\n      return offsets[max];\n    },\n    abbr: function (mom) {\n      return this.abbrs[this._index(mom)];\n    },\n    offset: function (mom) {\n      logError(\"zone.offset has been deprecated in favor of zone.utcOffset\");\n      return this.offsets[this._index(mom)];\n    },\n    utcOffset: function (mom) {\n      return this.offsets[this._index(mom)];\n    }\n  };\n\n  /************************************\n  \tCountry object\n  ************************************/\n\n  function Country(country_name, zone_names) {\n    this.name = country_name;\n    this.zones = zone_names;\n  }\n\n  /************************************\n  \tCurrent Timezone\n  ************************************/\n\n  function OffsetAt(at) {\n    var timeString = at.toTimeString();\n    var abbr = timeString.match(/\\([a-z ]+\\)/i);\n    if (abbr && abbr[0]) {\n      // 17:56:31 GMT-0600 (CST)\n      // 17:56:31 GMT-0600 (Central Standard Time)\n      abbr = abbr[0].match(/[A-Z]/g);\n      abbr = abbr ? abbr.join('') : undefined;\n    } else {\n      // 17:56:31 CST\n      // 17:56:31 GMT+0800 (台北標準時間)\n      abbr = timeString.match(/[A-Z]{3,5}/g);\n      abbr = abbr ? abbr[0] : undefined;\n    }\n    if (abbr === 'GMT') {\n      abbr = undefined;\n    }\n    this.at = +at;\n    this.abbr = abbr;\n    this.offset = at.getTimezoneOffset();\n  }\n  function ZoneScore(zone) {\n    this.zone = zone;\n    this.offsetScore = 0;\n    this.abbrScore = 0;\n  }\n  ZoneScore.prototype.scoreOffsetAt = function (offsetAt) {\n    this.offsetScore += Math.abs(this.zone.utcOffset(offsetAt.at) - offsetAt.offset);\n    if (this.zone.abbr(offsetAt.at).replace(/[^A-Z]/g, '') !== offsetAt.abbr) {\n      this.abbrScore++;\n    }\n  };\n  function findChange(low, high) {\n    var mid, diff;\n    while (diff = ((high.at - low.at) / 12e4 | 0) * 6e4) {\n      mid = new OffsetAt(new Date(low.at + diff));\n      if (mid.offset === low.offset) {\n        low = mid;\n      } else {\n        high = mid;\n      }\n    }\n    return low;\n  }\n  function userOffsets() {\n    var startYear = new Date().getFullYear() - 2,\n      last = new OffsetAt(new Date(startYear, 0, 1)),\n      offsets = [last],\n      change,\n      next,\n      i;\n    for (i = 1; i < 48; i++) {\n      next = new OffsetAt(new Date(startYear, i, 1));\n      if (next.offset !== last.offset) {\n        change = findChange(last, next);\n        offsets.push(change);\n        offsets.push(new OffsetAt(new Date(change.at + 6e4)));\n      }\n      last = next;\n    }\n    for (i = 0; i < 4; i++) {\n      offsets.push(new OffsetAt(new Date(startYear + i, 0, 1)));\n      offsets.push(new OffsetAt(new Date(startYear + i, 6, 1)));\n    }\n    return offsets;\n  }\n  function sortZoneScores(a, b) {\n    if (a.offsetScore !== b.offsetScore) {\n      return a.offsetScore - b.offsetScore;\n    }\n    if (a.abbrScore !== b.abbrScore) {\n      return a.abbrScore - b.abbrScore;\n    }\n    if (a.zone.population !== b.zone.population) {\n      return b.zone.population - a.zone.population;\n    }\n    return b.zone.name.localeCompare(a.zone.name);\n  }\n  function addToGuesses(name, offsets) {\n    var i, offset;\n    arrayToInt(offsets);\n    for (i = 0; i < offsets.length; i++) {\n      offset = offsets[i];\n      guesses[offset] = guesses[offset] || {};\n      guesses[offset][name] = true;\n    }\n  }\n  function guessesForUserOffsets(offsets) {\n    var offsetsLength = offsets.length,\n      filteredGuesses = {},\n      out = [],\n      i,\n      j,\n      guessesOffset;\n    for (i = 0; i < offsetsLength; i++) {\n      guessesOffset = guesses[offsets[i].offset] || {};\n      for (j in guessesOffset) {\n        if (guessesOffset.hasOwnProperty(j)) {\n          filteredGuesses[j] = true;\n        }\n      }\n    }\n    for (i in filteredGuesses) {\n      if (filteredGuesses.hasOwnProperty(i)) {\n        out.push(names[i]);\n      }\n    }\n    return out;\n  }\n  function rebuildGuess() {\n    // use Intl API when available and returning valid time zone\n    try {\n      var intlName = Intl.DateTimeFormat().resolvedOptions().timeZone;\n      if (intlName && intlName.length > 3) {\n        var name = names[normalizeName(intlName)];\n        if (name) {\n          return name;\n        }\n        logError(\"Moment Timezone found \" + intlName + \" from the Intl api, but did not have that data loaded.\");\n      }\n    } catch (e) {\n      // Intl unavailable, fall back to manual guessing.\n    }\n    var offsets = userOffsets(),\n      offsetsLength = offsets.length,\n      guesses = guessesForUserOffsets(offsets),\n      zoneScores = [],\n      zoneScore,\n      i,\n      j;\n    for (i = 0; i < guesses.length; i++) {\n      zoneScore = new ZoneScore(getZone(guesses[i]), offsetsLength);\n      for (j = 0; j < offsetsLength; j++) {\n        zoneScore.scoreOffsetAt(offsets[j]);\n      }\n      zoneScores.push(zoneScore);\n    }\n    zoneScores.sort(sortZoneScores);\n    return zoneScores.length > 0 ? zoneScores[0].zone.name : undefined;\n  }\n  function guess(ignoreCache) {\n    if (!cachedGuess || ignoreCache) {\n      cachedGuess = rebuildGuess();\n    }\n    return cachedGuess;\n  }\n\n  /************************************\n  \tGlobal Methods\n  ************************************/\n\n  function normalizeName(name) {\n    return (name || '').toLowerCase().replace(/\\//g, '_');\n  }\n  function addZone(packed) {\n    var i, name, split, normalized;\n    if (typeof packed === \"string\") {\n      packed = [packed];\n    }\n    for (i = 0; i < packed.length; i++) {\n      split = packed[i].split('|');\n      name = split[0];\n      normalized = normalizeName(name);\n      zones[normalized] = packed[i];\n      names[normalized] = name;\n      addToGuesses(normalized, split[2].split(' '));\n    }\n  }\n  function getZone(name, caller) {\n    name = normalizeName(name);\n    var zone = zones[name];\n    var link;\n    if (zone instanceof Zone) {\n      return zone;\n    }\n    if (typeof zone === 'string') {\n      zone = new Zone(zone);\n      zones[name] = zone;\n      return zone;\n    }\n\n    // Pass getZone to prevent recursion more than 1 level deep\n    if (links[name] && caller !== getZone && (link = getZone(links[name], getZone))) {\n      zone = zones[name] = new Zone();\n      zone._set(link);\n      zone.name = names[name];\n      return zone;\n    }\n    return null;\n  }\n  function getNames() {\n    var i,\n      out = [];\n    for (i in names) {\n      if (names.hasOwnProperty(i) && (zones[i] || zones[links[i]]) && names[i]) {\n        out.push(names[i]);\n      }\n    }\n    return out.sort();\n  }\n  function getCountryNames() {\n    return Object.keys(countries);\n  }\n  function addLink(aliases) {\n    var i, alias, normal0, normal1;\n    if (typeof aliases === \"string\") {\n      aliases = [aliases];\n    }\n    for (i = 0; i < aliases.length; i++) {\n      alias = aliases[i].split('|');\n      normal0 = normalizeName(alias[0]);\n      normal1 = normalizeName(alias[1]);\n      links[normal0] = normal1;\n      names[normal0] = alias[0];\n      links[normal1] = normal0;\n      names[normal1] = alias[1];\n    }\n  }\n  function addCountries(data) {\n    var i, country_code, country_zones, split;\n    if (!data || !data.length) return;\n    for (i = 0; i < data.length; i++) {\n      split = data[i].split('|');\n      country_code = split[0].toUpperCase();\n      country_zones = split[1].split(' ');\n      countries[country_code] = new Country(country_code, country_zones);\n    }\n  }\n  function getCountry(name) {\n    name = name.toUpperCase();\n    return countries[name] || null;\n  }\n  function zonesForCountry(country, with_offset) {\n    country = getCountry(country);\n    if (!country) return null;\n    var zones = country.zones.sort();\n    if (with_offset) {\n      return zones.map(function (zone_name) {\n        var zone = getZone(zone_name);\n        return {\n          name: zone_name,\n          offset: zone.utcOffset(new Date())\n        };\n      });\n    }\n    return zones;\n  }\n  function loadData(data) {\n    addZone(data.zones);\n    addLink(data.links);\n    addCountries(data.countries);\n    tz.dataVersion = data.version;\n  }\n  function zoneExists(name) {\n    if (!zoneExists.didShowError) {\n      zoneExists.didShowError = true;\n      logError(\"moment.tz.zoneExists('\" + name + \"') has been deprecated in favor of !moment.tz.zone('\" + name + \"')\");\n    }\n    return !!getZone(name);\n  }\n  function needsOffset(m) {\n    var isUnixTimestamp = m._f === 'X' || m._f === 'x';\n    return !!(m._a && m._tzm === undefined && !isUnixTimestamp);\n  }\n  function logError(message) {\n    if (typeof console !== 'undefined' && typeof console.error === 'function') {\n      console.error(message);\n    }\n  }\n\n  /************************************\n  \tmoment.tz namespace\n  ************************************/\n\n  function tz(input) {\n    var args = Array.prototype.slice.call(arguments, 0, -1),\n      name = arguments[arguments.length - 1],\n      zone = getZone(name),\n      out = moment.utc.apply(null, args);\n    if (zone && !moment.isMoment(input) && needsOffset(out)) {\n      out.add(zone.parse(out), 'minutes');\n    }\n    out.tz(name);\n    return out;\n  }\n  tz.version = VERSION;\n  tz.dataVersion = '';\n  tz._zones = zones;\n  tz._links = links;\n  tz._names = names;\n  tz._countries = countries;\n  tz.add = addZone;\n  tz.link = addLink;\n  tz.load = loadData;\n  tz.zone = getZone;\n  tz.zoneExists = zoneExists; // deprecated in 0.1.0\n  tz.guess = guess;\n  tz.names = getNames;\n  tz.Zone = Zone;\n  tz.unpack = unpack;\n  tz.unpackBase60 = unpackBase60;\n  tz.needsOffset = needsOffset;\n  tz.moveInvalidForward = true;\n  tz.moveAmbiguousForward = false;\n  tz.countries = getCountryNames;\n  tz.zonesForCountry = zonesForCountry;\n\n  /************************************\n  \tInterface with Moment.js\n  ************************************/\n\n  var fn = moment.fn;\n  moment.tz = tz;\n  moment.defaultZone = null;\n  moment.updateOffset = function (mom, keepTime) {\n    var zone = moment.defaultZone,\n      offset;\n    if (mom._z === undefined) {\n      if (zone && needsOffset(mom) && !mom._isUTC) {\n        mom._d = moment.utc(mom._a)._d;\n        mom.utc().add(zone.parse(mom), 'minutes');\n      }\n      mom._z = zone;\n    }\n    if (mom._z) {\n      offset = mom._z.utcOffset(mom);\n      if (Math.abs(offset) < 16) {\n        offset = offset / 60;\n      }\n      if (mom.utcOffset !== undefined) {\n        var z = mom._z;\n        mom.utcOffset(-offset, keepTime);\n        mom._z = z;\n      } else {\n        mom.zone(offset, keepTime);\n      }\n    }\n  };\n  fn.tz = function (name, keepTime) {\n    if (name) {\n      if (typeof name !== 'string') {\n        throw new Error('Time zone name must be a string, got ' + name + ' [' + typeof name + ']');\n      }\n      this._z = getZone(name);\n      if (this._z) {\n        moment.updateOffset(this, keepTime);\n      } else {\n        logError(\"Moment Timezone has no data for \" + name + \". See http://momentjs.com/timezone/docs/#/data-loading/.\");\n      }\n      return this;\n    }\n    if (this._z) {\n      return this._z.name;\n    }\n  };\n  function abbrWrap(old) {\n    return function () {\n      if (this._z) {\n        return this._z.abbr(this);\n      }\n      return old.call(this);\n    };\n  }\n  function resetZoneWrap(old) {\n    return function () {\n      this._z = null;\n      return old.apply(this, arguments);\n    };\n  }\n  function resetZoneWrap2(old) {\n    return function () {\n      if (arguments.length > 0) this._z = null;\n      return old.apply(this, arguments);\n    };\n  }\n  fn.zoneName = abbrWrap(fn.zoneName);\n  fn.zoneAbbr = abbrWrap(fn.zoneAbbr);\n  fn.utc = resetZoneWrap(fn.utc);\n  fn.local = resetZoneWrap(fn.local);\n  fn.utcOffset = resetZoneWrap2(fn.utcOffset);\n  moment.tz.setDefault = function (name) {\n    if (major < 2 || major === 2 && minor < 9) {\n      logError('Moment Timezone setDefault() requires Moment.js >= 2.9.0. You are using Moment.js ' + moment.version + '.');\n    }\n    moment.defaultZone = name ? getZone(name) : null;\n    return moment;\n  };\n\n  // Cloning a moment should include the _z property.\n  var momentProperties = moment.momentProperties;\n  if (Object.prototype.toString.call(momentProperties) === '[object Array]') {\n    // moment 2.8.1+\n    momentProperties.push('_z');\n    momentProperties.push('_a');\n  } else if (momentProperties) {\n    // moment 2.7.0\n    momentProperties._z = null;\n  }\n\n  // INJECT DATA\n\n  return moment;\n});", "map": {"version": 3, "names": ["root", "factory", "module", "exports", "require", "define", "amd", "moment", "version", "undefined", "default", "VERSION", "zones", "links", "countries", "names", "guesses", "cachedGuess", "logError", "momentVersion", "split", "major", "minor", "charCodeToInt", "charCode", "unpackBase60", "string", "i", "parts", "whole", "fractional", "multiplier", "num", "out", "sign", "charCodeAt", "length", "arrayToInt", "array", "intToUntil", "Math", "round", "Infinity", "mapIndices", "source", "indices", "unpack", "data", "offsets", "untils", "name", "abbrs", "population", "Zone", "packedString", "_set", "prototype", "unpacked", "_index", "timestamp", "target", "zone_name", "Object", "keys", "filter", "country_code", "indexOf", "parse", "max", "offset", "offsetNext", "offsetPrev", "tz", "moveAmbiguousForward", "moveInvalidForward", "abbr", "mom", "utcOffset", "Country", "country_name", "zone_names", "OffsetAt", "at", "timeString", "toTimeString", "match", "join", "getTimezoneOffset", "ZoneScore", "zone", "offsetScore", "abbrScore", "scoreOffsetAt", "offsetAt", "abs", "replace", "findChange", "low", "high", "mid", "diff", "Date", "userOffsets", "startYear", "getFullYear", "last", "change", "next", "push", "sortZoneScores", "a", "b", "localeCompare", "addToGuesses", "guessesForUserOffsets", "offsetsLength", "filteredGuesses", "j", "guessesOffset", "hasOwnProperty", "rebuildGuess", "intlName", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "normalizeName", "e", "zoneScores", "zoneScore", "getZone", "sort", "guess", "ignoreCache", "toLowerCase", "addZone", "packed", "normalized", "caller", "link", "getNames", "getCountryNames", "addLink", "aliases", "alias", "normal0", "normal1", "addCountries", "country_zones", "toUpperCase", "getCountry", "zonesForCountry", "country", "with_offset", "map", "loadData", "dataVersion", "zoneExists", "didShowError", "needsOffset", "m", "isUnixTimestamp", "_f", "_a", "_tzm", "message", "console", "error", "input", "args", "Array", "slice", "call", "arguments", "utc", "apply", "isMoment", "add", "_zones", "_links", "_names", "_countries", "load", "fn", "defaultZone", "updateOffset", "keepTime", "_z", "_isUTC", "_d", "z", "Error", "abbrWrap", "old", "resetZoneWrap", "resetZoneWrap2", "zoneName", "zoneAbbr", "local", "<PERSON><PERSON><PERSON><PERSON>", "momentProperties", "toString"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/moment-timezone/moment-timezone.js"], "sourcesContent": ["//! moment-timezone.js\n//! version : 0.5.32\n//! Copyright (c) JS Foundation and other contributors\n//! license : MIT\n//! github.com/moment/moment-timezone\n\n(function (root, factory) {\n\t\"use strict\";\n\n\t/*global define*/\n\tif (typeof module === 'object' && module.exports) {\n\t\tmodule.exports = factory(require('moment')); // Node\n\t} else if (typeof define === 'function' && define.amd) {\n\t\tdefine(['moment'], factory);                 // AMD\n\t} else {\n\t\tfactory(root.moment);                        // Browser\n\t}\n}(this, function (moment) {\n\t\"use strict\";\n\n\t// Resolves es6 module loading issue\n\tif (moment.version === undefined && moment.default) {\n\t\tmoment = moment.default;\n\t}\n\n\t// Do not load moment-timezone a second time.\n\t// if (moment.tz !== undefined) {\n\t// \tlogError('Moment Timezone ' + moment.tz.version + ' was already loaded ' + (moment.tz.dataVersion ? 'with data from ' : 'without any data') + moment.tz.dataVersion);\n\t// \treturn moment;\n\t// }\n\n\tvar VERSION = \"0.5.32\",\n\t\tzones = {},\n\t\tlinks = {},\n\t\tcountries = {},\n\t\tnames = {},\n\t\tguesses = {},\n\t\tcachedGuess;\n\n\tif (!moment || typeof moment.version !== 'string') {\n\t\tlogError('Moment Timezone requires Moment.js. See https://momentjs.com/timezone/docs/#/use-it/browser/');\n\t}\n\n\tvar momentVersion = moment.version.split('.'),\n\t\tmajor = +momentVersion[0],\n\t\tminor = +momentVersion[1];\n\n\t// Moment.js version check\n\tif (major < 2 || (major === 2 && minor < 6)) {\n\t\tlogError('Moment Timezone requires Moment.js >= 2.6.0. You are using Moment.js ' + moment.version + '. See momentjs.com');\n\t}\n\n\t/************************************\n\t\tUnpacking\n\t************************************/\n\n\tfunction charCodeToInt(charCode) {\n\t\tif (charCode > 96) {\n\t\t\treturn charCode - 87;\n\t\t} else if (charCode > 64) {\n\t\t\treturn charCode - 29;\n\t\t}\n\t\treturn charCode - 48;\n\t}\n\n\tfunction unpackBase60(string) {\n\t\tvar i = 0,\n\t\t\tparts = string.split('.'),\n\t\t\twhole = parts[0],\n\t\t\tfractional = parts[1] || '',\n\t\t\tmultiplier = 1,\n\t\t\tnum,\n\t\t\tout = 0,\n\t\t\tsign = 1;\n\n\t\t// handle negative numbers\n\t\tif (string.charCodeAt(0) === 45) {\n\t\t\ti = 1;\n\t\t\tsign = -1;\n\t\t}\n\n\t\t// handle digits before the decimal\n\t\tfor (i; i < whole.length; i++) {\n\t\t\tnum = charCodeToInt(whole.charCodeAt(i));\n\t\t\tout = 60 * out + num;\n\t\t}\n\n\t\t// handle digits after the decimal\n\t\tfor (i = 0; i < fractional.length; i++) {\n\t\t\tmultiplier = multiplier / 60;\n\t\t\tnum = charCodeToInt(fractional.charCodeAt(i));\n\t\t\tout += num * multiplier;\n\t\t}\n\n\t\treturn out * sign;\n\t}\n\n\tfunction arrayToInt (array) {\n\t\tfor (var i = 0; i < array.length; i++) {\n\t\t\tarray[i] = unpackBase60(array[i]);\n\t\t}\n\t}\n\n\tfunction intToUntil (array, length) {\n\t\tfor (var i = 0; i < length; i++) {\n\t\t\tarray[i] = Math.round((array[i - 1] || 0) + (array[i] * 60000)); // minutes to milliseconds\n\t\t}\n\n\t\tarray[length - 1] = Infinity;\n\t}\n\n\tfunction mapIndices (source, indices) {\n\t\tvar out = [], i;\n\n\t\tfor (i = 0; i < indices.length; i++) {\n\t\t\tout[i] = source[indices[i]];\n\t\t}\n\n\t\treturn out;\n\t}\n\n\tfunction unpack (string) {\n\t\tvar data = string.split('|'),\n\t\t\toffsets = data[2].split(' '),\n\t\t\tindices = data[3].split(''),\n\t\t\tuntils  = data[4].split(' ');\n\n\t\tarrayToInt(offsets);\n\t\tarrayToInt(indices);\n\t\tarrayToInt(untils);\n\n\t\tintToUntil(untils, indices.length);\n\n\t\treturn {\n\t\t\tname       : data[0],\n\t\t\tabbrs      : mapIndices(data[1].split(' '), indices),\n\t\t\toffsets    : mapIndices(offsets, indices),\n\t\t\tuntils     : untils,\n\t\t\tpopulation : data[5] | 0\n\t\t};\n\t}\n\n\t/************************************\n\t\tZone object\n\t************************************/\n\n\tfunction Zone (packedString) {\n\t\tif (packedString) {\n\t\t\tthis._set(unpack(packedString));\n\t\t}\n\t}\n\n\tZone.prototype = {\n\t\t_set : function (unpacked) {\n\t\t\tthis.name       = unpacked.name;\n\t\t\tthis.abbrs      = unpacked.abbrs;\n\t\t\tthis.untils     = unpacked.untils;\n\t\t\tthis.offsets    = unpacked.offsets;\n\t\t\tthis.population = unpacked.population;\n\t\t},\n\n\t\t_index : function (timestamp) {\n\t\t\tvar target = +timestamp,\n\t\t\t\tuntils = this.untils,\n\t\t\t\ti;\n\n\t\t\tfor (i = 0; i < untils.length; i++) {\n\t\t\t\tif (target < untils[i]) {\n\t\t\t\t\treturn i;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tcountries : function () {\n\t\t\tvar zone_name = this.name;\n\t\t\treturn Object.keys(countries).filter(function (country_code) {\n\t\t\t\treturn countries[country_code].zones.indexOf(zone_name) !== -1;\n\t\t\t});\n\t\t},\n\n\t\tparse : function (timestamp) {\n\t\t\tvar target  = +timestamp,\n\t\t\t\toffsets = this.offsets,\n\t\t\t\tuntils  = this.untils,\n\t\t\t\tmax     = untils.length - 1,\n\t\t\t\toffset, offsetNext, offsetPrev, i;\n\n\t\t\tfor (i = 0; i < max; i++) {\n\t\t\t\toffset     = offsets[i];\n\t\t\t\toffsetNext = offsets[i + 1];\n\t\t\t\toffsetPrev = offsets[i ? i - 1 : i];\n\n\t\t\t\tif (offset < offsetNext && tz.moveAmbiguousForward) {\n\t\t\t\t\toffset = offsetNext;\n\t\t\t\t} else if (offset > offsetPrev && tz.moveInvalidForward) {\n\t\t\t\t\toffset = offsetPrev;\n\t\t\t\t}\n\n\t\t\t\tif (target < untils[i] - (offset * 60000)) {\n\t\t\t\t\treturn offsets[i];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn offsets[max];\n\t\t},\n\n\t\tabbr : function (mom) {\n\t\t\treturn this.abbrs[this._index(mom)];\n\t\t},\n\n\t\toffset : function (mom) {\n\t\t\tlogError(\"zone.offset has been deprecated in favor of zone.utcOffset\");\n\t\t\treturn this.offsets[this._index(mom)];\n\t\t},\n\n\t\tutcOffset : function (mom) {\n\t\t\treturn this.offsets[this._index(mom)];\n\t\t}\n\t};\n\n\t/************************************\n\t\tCountry object\n\t************************************/\n\n\tfunction Country (country_name, zone_names) {\n\t\tthis.name = country_name;\n\t\tthis.zones = zone_names;\n\t}\n\n\t/************************************\n\t\tCurrent Timezone\n\t************************************/\n\n\tfunction OffsetAt(at) {\n\t\tvar timeString = at.toTimeString();\n\t\tvar abbr = timeString.match(/\\([a-z ]+\\)/i);\n\t\tif (abbr && abbr[0]) {\n\t\t\t// 17:56:31 GMT-0600 (CST)\n\t\t\t// 17:56:31 GMT-0600 (Central Standard Time)\n\t\t\tabbr = abbr[0].match(/[A-Z]/g);\n\t\t\tabbr = abbr ? abbr.join('') : undefined;\n\t\t} else {\n\t\t\t// 17:56:31 CST\n\t\t\t// 17:56:31 GMT+0800 (台北標準時間)\n\t\t\tabbr = timeString.match(/[A-Z]{3,5}/g);\n\t\t\tabbr = abbr ? abbr[0] : undefined;\n\t\t}\n\n\t\tif (abbr === 'GMT') {\n\t\t\tabbr = undefined;\n\t\t}\n\n\t\tthis.at = +at;\n\t\tthis.abbr = abbr;\n\t\tthis.offset = at.getTimezoneOffset();\n\t}\n\n\tfunction ZoneScore(zone) {\n\t\tthis.zone = zone;\n\t\tthis.offsetScore = 0;\n\t\tthis.abbrScore = 0;\n\t}\n\n\tZoneScore.prototype.scoreOffsetAt = function (offsetAt) {\n\t\tthis.offsetScore += Math.abs(this.zone.utcOffset(offsetAt.at) - offsetAt.offset);\n\t\tif (this.zone.abbr(offsetAt.at).replace(/[^A-Z]/g, '') !== offsetAt.abbr) {\n\t\t\tthis.abbrScore++;\n\t\t}\n\t};\n\n\tfunction findChange(low, high) {\n\t\tvar mid, diff;\n\n\t\twhile ((diff = ((high.at - low.at) / 12e4 | 0) * 6e4)) {\n\t\t\tmid = new OffsetAt(new Date(low.at + diff));\n\t\t\tif (mid.offset === low.offset) {\n\t\t\t\tlow = mid;\n\t\t\t} else {\n\t\t\t\thigh = mid;\n\t\t\t}\n\t\t}\n\n\t\treturn low;\n\t}\n\n\tfunction userOffsets() {\n\t\tvar startYear = new Date().getFullYear() - 2,\n\t\t\tlast = new OffsetAt(new Date(startYear, 0, 1)),\n\t\t\toffsets = [last],\n\t\t\tchange, next, i;\n\n\t\tfor (i = 1; i < 48; i++) {\n\t\t\tnext = new OffsetAt(new Date(startYear, i, 1));\n\t\t\tif (next.offset !== last.offset) {\n\t\t\t\tchange = findChange(last, next);\n\t\t\t\toffsets.push(change);\n\t\t\t\toffsets.push(new OffsetAt(new Date(change.at + 6e4)));\n\t\t\t}\n\t\t\tlast = next;\n\t\t}\n\n\t\tfor (i = 0; i < 4; i++) {\n\t\t\toffsets.push(new OffsetAt(new Date(startYear + i, 0, 1)));\n\t\t\toffsets.push(new OffsetAt(new Date(startYear + i, 6, 1)));\n\t\t}\n\n\t\treturn offsets;\n\t}\n\n\tfunction sortZoneScores (a, b) {\n\t\tif (a.offsetScore !== b.offsetScore) {\n\t\t\treturn a.offsetScore - b.offsetScore;\n\t\t}\n\t\tif (a.abbrScore !== b.abbrScore) {\n\t\t\treturn a.abbrScore - b.abbrScore;\n\t\t}\n\t\tif (a.zone.population !== b.zone.population) {\n\t\t\treturn b.zone.population - a.zone.population;\n\t\t}\n\t\treturn b.zone.name.localeCompare(a.zone.name);\n\t}\n\n\tfunction addToGuesses (name, offsets) {\n\t\tvar i, offset;\n\t\tarrayToInt(offsets);\n\t\tfor (i = 0; i < offsets.length; i++) {\n\t\t\toffset = offsets[i];\n\t\t\tguesses[offset] = guesses[offset] || {};\n\t\t\tguesses[offset][name] = true;\n\t\t}\n\t}\n\n\tfunction guessesForUserOffsets (offsets) {\n\t\tvar offsetsLength = offsets.length,\n\t\t\tfilteredGuesses = {},\n\t\t\tout = [],\n\t\t\ti, j, guessesOffset;\n\n\t\tfor (i = 0; i < offsetsLength; i++) {\n\t\t\tguessesOffset = guesses[offsets[i].offset] || {};\n\t\t\tfor (j in guessesOffset) {\n\t\t\t\tif (guessesOffset.hasOwnProperty(j)) {\n\t\t\t\t\tfilteredGuesses[j] = true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tfor (i in filteredGuesses) {\n\t\t\tif (filteredGuesses.hasOwnProperty(i)) {\n\t\t\t\tout.push(names[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn out;\n\t}\n\n\tfunction rebuildGuess () {\n\n\t\t// use Intl API when available and returning valid time zone\n\t\ttry {\n\t\t\tvar intlName = Intl.DateTimeFormat().resolvedOptions().timeZone;\n\t\t\tif (intlName && intlName.length > 3) {\n\t\t\t\tvar name = names[normalizeName(intlName)];\n\t\t\t\tif (name) {\n\t\t\t\t\treturn name;\n\t\t\t\t}\n\t\t\t\tlogError(\"Moment Timezone found \" + intlName + \" from the Intl api, but did not have that data loaded.\");\n\t\t\t}\n\t\t} catch (e) {\n\t\t\t// Intl unavailable, fall back to manual guessing.\n\t\t}\n\n\t\tvar offsets = userOffsets(),\n\t\t\toffsetsLength = offsets.length,\n\t\t\tguesses = guessesForUserOffsets(offsets),\n\t\t\tzoneScores = [],\n\t\t\tzoneScore, i, j;\n\n\t\tfor (i = 0; i < guesses.length; i++) {\n\t\t\tzoneScore = new ZoneScore(getZone(guesses[i]), offsetsLength);\n\t\t\tfor (j = 0; j < offsetsLength; j++) {\n\t\t\t\tzoneScore.scoreOffsetAt(offsets[j]);\n\t\t\t}\n\t\t\tzoneScores.push(zoneScore);\n\t\t}\n\n\t\tzoneScores.sort(sortZoneScores);\n\n\t\treturn zoneScores.length > 0 ? zoneScores[0].zone.name : undefined;\n\t}\n\n\tfunction guess (ignoreCache) {\n\t\tif (!cachedGuess || ignoreCache) {\n\t\t\tcachedGuess = rebuildGuess();\n\t\t}\n\t\treturn cachedGuess;\n\t}\n\n\t/************************************\n\t\tGlobal Methods\n\t************************************/\n\n\tfunction normalizeName (name) {\n\t\treturn (name || '').toLowerCase().replace(/\\//g, '_');\n\t}\n\n\tfunction addZone (packed) {\n\t\tvar i, name, split, normalized;\n\n\t\tif (typeof packed === \"string\") {\n\t\t\tpacked = [packed];\n\t\t}\n\n\t\tfor (i = 0; i < packed.length; i++) {\n\t\t\tsplit = packed[i].split('|');\n\t\t\tname = split[0];\n\t\t\tnormalized = normalizeName(name);\n\t\t\tzones[normalized] = packed[i];\n\t\t\tnames[normalized] = name;\n\t\t\taddToGuesses(normalized, split[2].split(' '));\n\t\t}\n\t}\n\n\tfunction getZone (name, caller) {\n\n\t\tname = normalizeName(name);\n\n\t\tvar zone = zones[name];\n\t\tvar link;\n\n\t\tif (zone instanceof Zone) {\n\t\t\treturn zone;\n\t\t}\n\n\t\tif (typeof zone === 'string') {\n\t\t\tzone = new Zone(zone);\n\t\t\tzones[name] = zone;\n\t\t\treturn zone;\n\t\t}\n\n\t\t// Pass getZone to prevent recursion more than 1 level deep\n\t\tif (links[name] && caller !== getZone && (link = getZone(links[name], getZone))) {\n\t\t\tzone = zones[name] = new Zone();\n\t\t\tzone._set(link);\n\t\t\tzone.name = names[name];\n\t\t\treturn zone;\n\t\t}\n\n\t\treturn null;\n\t}\n\n\tfunction getNames () {\n\t\tvar i, out = [];\n\n\t\tfor (i in names) {\n\t\t\tif (names.hasOwnProperty(i) && (zones[i] || zones[links[i]]) && names[i]) {\n\t\t\t\tout.push(names[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn out.sort();\n\t}\n\n\tfunction getCountryNames () {\n\t\treturn Object.keys(countries);\n\t}\n\n\tfunction addLink (aliases) {\n\t\tvar i, alias, normal0, normal1;\n\n\t\tif (typeof aliases === \"string\") {\n\t\t\taliases = [aliases];\n\t\t}\n\n\t\tfor (i = 0; i < aliases.length; i++) {\n\t\t\talias = aliases[i].split('|');\n\n\t\t\tnormal0 = normalizeName(alias[0]);\n\t\t\tnormal1 = normalizeName(alias[1]);\n\n\t\t\tlinks[normal0] = normal1;\n\t\t\tnames[normal0] = alias[0];\n\n\t\t\tlinks[normal1] = normal0;\n\t\t\tnames[normal1] = alias[1];\n\t\t}\n\t}\n\n\tfunction addCountries (data) {\n\t\tvar i, country_code, country_zones, split;\n\t\tif (!data || !data.length) return;\n\t\tfor (i = 0; i < data.length; i++) {\n\t\t\tsplit = data[i].split('|');\n\t\t\tcountry_code = split[0].toUpperCase();\n\t\t\tcountry_zones = split[1].split(' ');\n\t\t\tcountries[country_code] = new Country(\n\t\t\t\tcountry_code,\n\t\t\t\tcountry_zones\n\t\t\t);\n\t\t}\n\t}\n\n\tfunction getCountry (name) {\n\t\tname = name.toUpperCase();\n\t\treturn countries[name] || null;\n\t}\n\n\tfunction zonesForCountry(country, with_offset) {\n\t\tcountry = getCountry(country);\n\n\t\tif (!country) return null;\n\n\t\tvar zones = country.zones.sort();\n\n\t\tif (with_offset) {\n\t\t\treturn zones.map(function (zone_name) {\n\t\t\t\tvar zone = getZone(zone_name);\n\t\t\t\treturn {\n\t\t\t\t\tname: zone_name,\n\t\t\t\t\toffset: zone.utcOffset(new Date())\n\t\t\t\t};\n\t\t\t});\n\t\t}\n\n\t\treturn zones;\n\t}\n\n\tfunction loadData (data) {\n\t\taddZone(data.zones);\n\t\taddLink(data.links);\n\t\taddCountries(data.countries);\n\t\ttz.dataVersion = data.version;\n\t}\n\n\tfunction zoneExists (name) {\n\t\tif (!zoneExists.didShowError) {\n\t\t\tzoneExists.didShowError = true;\n\t\t\t\tlogError(\"moment.tz.zoneExists('\" + name + \"') has been deprecated in favor of !moment.tz.zone('\" + name + \"')\");\n\t\t}\n\t\treturn !!getZone(name);\n\t}\n\n\tfunction needsOffset (m) {\n\t\tvar isUnixTimestamp = (m._f === 'X' || m._f === 'x');\n\t\treturn !!(m._a && (m._tzm === undefined) && !isUnixTimestamp);\n\t}\n\n\tfunction logError (message) {\n\t\tif (typeof console !== 'undefined' && typeof console.error === 'function') {\n\t\t\tconsole.error(message);\n\t\t}\n\t}\n\n\t/************************************\n\t\tmoment.tz namespace\n\t************************************/\n\n\tfunction tz (input) {\n\t\tvar args = Array.prototype.slice.call(arguments, 0, -1),\n\t\t\tname = arguments[arguments.length - 1],\n\t\t\tzone = getZone(name),\n\t\t\tout  = moment.utc.apply(null, args);\n\n\t\tif (zone && !moment.isMoment(input) && needsOffset(out)) {\n\t\t\tout.add(zone.parse(out), 'minutes');\n\t\t}\n\n\t\tout.tz(name);\n\n\t\treturn out;\n\t}\n\n\ttz.version      = VERSION;\n\ttz.dataVersion  = '';\n\ttz._zones       = zones;\n\ttz._links       = links;\n\ttz._names       = names;\n\ttz._countries\t= countries;\n\ttz.add          = addZone;\n\ttz.link         = addLink;\n\ttz.load         = loadData;\n\ttz.zone         = getZone;\n\ttz.zoneExists   = zoneExists; // deprecated in 0.1.0\n\ttz.guess        = guess;\n\ttz.names        = getNames;\n\ttz.Zone         = Zone;\n\ttz.unpack       = unpack;\n\ttz.unpackBase60 = unpackBase60;\n\ttz.needsOffset  = needsOffset;\n\ttz.moveInvalidForward   = true;\n\ttz.moveAmbiguousForward = false;\n\ttz.countries    = getCountryNames;\n\ttz.zonesForCountry = zonesForCountry;\n\n\t/************************************\n\t\tInterface with Moment.js\n\t************************************/\n\n\tvar fn = moment.fn;\n\n\tmoment.tz = tz;\n\n\tmoment.defaultZone = null;\n\n\tmoment.updateOffset = function (mom, keepTime) {\n\t\tvar zone = moment.defaultZone,\n\t\t\toffset;\n\n\t\tif (mom._z === undefined) {\n\t\t\tif (zone && needsOffset(mom) && !mom._isUTC) {\n\t\t\t\tmom._d = moment.utc(mom._a)._d;\n\t\t\t\tmom.utc().add(zone.parse(mom), 'minutes');\n\t\t\t}\n\t\t\tmom._z = zone;\n\t\t}\n\t\tif (mom._z) {\n\t\t\toffset = mom._z.utcOffset(mom);\n\t\t\tif (Math.abs(offset) < 16) {\n\t\t\t\toffset = offset / 60;\n\t\t\t}\n\t\t\tif (mom.utcOffset !== undefined) {\n\t\t\t\tvar z = mom._z;\n\t\t\t\tmom.utcOffset(-offset, keepTime);\n\t\t\t\tmom._z = z;\n\t\t\t} else {\n\t\t\t\tmom.zone(offset, keepTime);\n\t\t\t}\n\t\t}\n\t};\n\n\tfn.tz = function (name, keepTime) {\n\t\tif (name) {\n\t\t\tif (typeof name !== 'string') {\n\t\t\t\tthrow new Error('Time zone name must be a string, got ' + name + ' [' + typeof name + ']');\n\t\t\t}\n\t\t\tthis._z = getZone(name);\n\t\t\tif (this._z) {\n\t\t\t\tmoment.updateOffset(this, keepTime);\n\t\t\t} else {\n\t\t\t\tlogError(\"Moment Timezone has no data for \" + name + \". See http://momentjs.com/timezone/docs/#/data-loading/.\");\n\t\t\t}\n\t\t\treturn this;\n\t\t}\n\t\tif (this._z) { return this._z.name; }\n\t};\n\n\tfunction abbrWrap (old) {\n\t\treturn function () {\n\t\t\tif (this._z) { return this._z.abbr(this); }\n\t\t\treturn old.call(this);\n\t\t};\n\t}\n\n\tfunction resetZoneWrap (old) {\n\t\treturn function () {\n\t\t\tthis._z = null;\n\t\t\treturn old.apply(this, arguments);\n\t\t};\n\t}\n\n\tfunction resetZoneWrap2 (old) {\n\t\treturn function () {\n\t\t\tif (arguments.length > 0) this._z = null;\n\t\t\treturn old.apply(this, arguments);\n\t\t};\n\t}\n\n\tfn.zoneName  = abbrWrap(fn.zoneName);\n\tfn.zoneAbbr  = abbrWrap(fn.zoneAbbr);\n\tfn.utc       = resetZoneWrap(fn.utc);\n\tfn.local     = resetZoneWrap(fn.local);\n\tfn.utcOffset = resetZoneWrap2(fn.utcOffset);\n\n\tmoment.tz.setDefault = function(name) {\n\t\tif (major < 2 || (major === 2 && minor < 9)) {\n\t\t\tlogError('Moment Timezone setDefault() requires Moment.js >= 2.9.0. You are using Moment.js ' + moment.version + '.');\n\t\t}\n\t\tmoment.defaultZone = name ? getZone(name) : null;\n\t\treturn moment;\n\t};\n\n\t// Cloning a moment should include the _z property.\n\tvar momentProperties = moment.momentProperties;\n\tif (Object.prototype.toString.call(momentProperties) === '[object Array]') {\n\t\t// moment 2.8.1+\n\t\tmomentProperties.push('_z');\n\t\tmomentProperties.push('_a');\n\t} else if (momentProperties) {\n\t\t// moment 2.7.0\n\t\tmomentProperties._z = null;\n\t}\n\n\t// INJECT DATA\n\n\treturn moment;\n}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEC,WAAUA,IAAI,EAAEC,OAAO,EAAE;EACzB,YAAY;;EAEZ;EACA,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,OAAO,EAAE;IACjDD,MAAM,CAACC,OAAO,GAAGF,OAAO,CAACG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC,MAAM,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACtDD,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAEJ,OAAO,CAAC,CAAC,CAAiB;EAC9C,CAAC,MAAM;IACNA,OAAO,CAACD,IAAI,CAACO,MAAM,CAAC,CAAC,CAAwB;EAC9C;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,MAAM,EAAE;EACzB,YAAY;;EAEZ;EACA,IAAIA,MAAM,CAACC,OAAO,KAAKC,SAAS,IAAIF,MAAM,CAACG,OAAO,EAAE;IACnDH,MAAM,GAAGA,MAAM,CAACG,OAAO;EACxB;;EAEA;EACA;EACA;EACA;EACA;;EAEA,IAAIC,OAAO,GAAG,QAAQ;IACrBC,KAAK,GAAG,CAAC,CAAC;IACVC,KAAK,GAAG,CAAC,CAAC;IACVC,SAAS,GAAG,CAAC,CAAC;IACdC,KAAK,GAAG,CAAC,CAAC;IACVC,OAAO,GAAG,CAAC,CAAC;IACZC,WAAW;EAEZ,IAAI,CAACV,MAAM,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;IAClDU,QAAQ,CAAC,8FAA8F,CAAC;EACzG;EAEA,IAAIC,aAAa,GAAGZ,MAAM,CAACC,OAAO,CAACY,KAAK,CAAC,GAAG,CAAC;IAC5CC,KAAK,GAAG,CAACF,aAAa,CAAC,CAAC,CAAC;IACzBG,KAAK,GAAG,CAACH,aAAa,CAAC,CAAC,CAAC;;EAE1B;EACA,IAAIE,KAAK,GAAG,CAAC,IAAKA,KAAK,KAAK,CAAC,IAAIC,KAAK,GAAG,CAAE,EAAE;IAC5CJ,QAAQ,CAAC,uEAAuE,GAAGX,MAAM,CAACC,OAAO,GAAG,oBAAoB,CAAC;EAC1H;;EAEA;AACD;AACA;;EAEC,SAASe,aAAaA,CAACC,QAAQ,EAAE;IAChC,IAAIA,QAAQ,GAAG,EAAE,EAAE;MAClB,OAAOA,QAAQ,GAAG,EAAE;IACrB,CAAC,MAAM,IAAIA,QAAQ,GAAG,EAAE,EAAE;MACzB,OAAOA,QAAQ,GAAG,EAAE;IACrB;IACA,OAAOA,QAAQ,GAAG,EAAE;EACrB;EAEA,SAASC,YAAYA,CAACC,MAAM,EAAE;IAC7B,IAAIC,CAAC,GAAG,CAAC;MACRC,KAAK,GAAGF,MAAM,CAACN,KAAK,CAAC,GAAG,CAAC;MACzBS,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC;MAChBE,UAAU,GAAGF,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MAC3BG,UAAU,GAAG,CAAC;MACdC,GAAG;MACHC,GAAG,GAAG,CAAC;MACPC,IAAI,GAAG,CAAC;;IAET;IACA,IAAIR,MAAM,CAACS,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;MAChCR,CAAC,GAAG,CAAC;MACLO,IAAI,GAAG,CAAC,CAAC;IACV;;IAEA;IACA,KAAKP,CAAC,EAAEA,CAAC,GAAGE,KAAK,CAACO,MAAM,EAAET,CAAC,EAAE,EAAE;MAC9BK,GAAG,GAAGT,aAAa,CAACM,KAAK,CAACM,UAAU,CAACR,CAAC,CAAC,CAAC;MACxCM,GAAG,GAAG,EAAE,GAAGA,GAAG,GAAGD,GAAG;IACrB;;IAEA;IACA,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,UAAU,CAACM,MAAM,EAAET,CAAC,EAAE,EAAE;MACvCI,UAAU,GAAGA,UAAU,GAAG,EAAE;MAC5BC,GAAG,GAAGT,aAAa,CAACO,UAAU,CAACK,UAAU,CAACR,CAAC,CAAC,CAAC;MAC7CM,GAAG,IAAID,GAAG,GAAGD,UAAU;IACxB;IAEA,OAAOE,GAAG,GAAGC,IAAI;EAClB;EAEA,SAASG,UAAUA,CAAEC,KAAK,EAAE;IAC3B,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,KAAK,CAACF,MAAM,EAAET,CAAC,EAAE,EAAE;MACtCW,KAAK,CAACX,CAAC,CAAC,GAAGF,YAAY,CAACa,KAAK,CAACX,CAAC,CAAC,CAAC;IAClC;EACD;EAEA,SAASY,UAAUA,CAAED,KAAK,EAAEF,MAAM,EAAE;IACnC,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,MAAM,EAAET,CAAC,EAAE,EAAE;MAChCW,KAAK,CAACX,CAAC,CAAC,GAAGa,IAAI,CAACC,KAAK,CAAC,CAACH,KAAK,CAACX,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAKW,KAAK,CAACX,CAAC,CAAC,GAAG,KAAM,CAAC,CAAC,CAAC;IAClE;IAEAW,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC,GAAGM,QAAQ;EAC7B;EAEA,SAASC,UAAUA,CAAEC,MAAM,EAAEC,OAAO,EAAE;IACrC,IAAIZ,GAAG,GAAG,EAAE;MAAEN,CAAC;IAEf,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,OAAO,CAACT,MAAM,EAAET,CAAC,EAAE,EAAE;MACpCM,GAAG,CAACN,CAAC,CAAC,GAAGiB,MAAM,CAACC,OAAO,CAAClB,CAAC,CAAC,CAAC;IAC5B;IAEA,OAAOM,GAAG;EACX;EAEA,SAASa,MAAMA,CAAEpB,MAAM,EAAE;IACxB,IAAIqB,IAAI,GAAGrB,MAAM,CAACN,KAAK,CAAC,GAAG,CAAC;MAC3B4B,OAAO,GAAGD,IAAI,CAAC,CAAC,CAAC,CAAC3B,KAAK,CAAC,GAAG,CAAC;MAC5ByB,OAAO,GAAGE,IAAI,CAAC,CAAC,CAAC,CAAC3B,KAAK,CAAC,EAAE,CAAC;MAC3B6B,MAAM,GAAIF,IAAI,CAAC,CAAC,CAAC,CAAC3B,KAAK,CAAC,GAAG,CAAC;IAE7BiB,UAAU,CAACW,OAAO,CAAC;IACnBX,UAAU,CAACQ,OAAO,CAAC;IACnBR,UAAU,CAACY,MAAM,CAAC;IAElBV,UAAU,CAACU,MAAM,EAAEJ,OAAO,CAACT,MAAM,CAAC;IAElC,OAAO;MACNc,IAAI,EAASH,IAAI,CAAC,CAAC,CAAC;MACpBI,KAAK,EAAQR,UAAU,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC3B,KAAK,CAAC,GAAG,CAAC,EAAEyB,OAAO,CAAC;MACpDG,OAAO,EAAML,UAAU,CAACK,OAAO,EAAEH,OAAO,CAAC;MACzCI,MAAM,EAAOA,MAAM;MACnBG,UAAU,EAAGL,IAAI,CAAC,CAAC,CAAC,GAAG;IACxB,CAAC;EACF;;EAEA;AACD;AACA;;EAEC,SAASM,IAAIA,CAAEC,YAAY,EAAE;IAC5B,IAAIA,YAAY,EAAE;MACjB,IAAI,CAACC,IAAI,CAACT,MAAM,CAACQ,YAAY,CAAC,CAAC;IAChC;EACD;EAEAD,IAAI,CAACG,SAAS,GAAG;IAChBD,IAAI,EAAG,SAAAA,CAAUE,QAAQ,EAAE;MAC1B,IAAI,CAACP,IAAI,GAASO,QAAQ,CAACP,IAAI;MAC/B,IAAI,CAACC,KAAK,GAAQM,QAAQ,CAACN,KAAK;MAChC,IAAI,CAACF,MAAM,GAAOQ,QAAQ,CAACR,MAAM;MACjC,IAAI,CAACD,OAAO,GAAMS,QAAQ,CAACT,OAAO;MAClC,IAAI,CAACI,UAAU,GAAGK,QAAQ,CAACL,UAAU;IACtC,CAAC;IAEDM,MAAM,EAAG,SAAAA,CAAUC,SAAS,EAAE;MAC7B,IAAIC,MAAM,GAAG,CAACD,SAAS;QACtBV,MAAM,GAAG,IAAI,CAACA,MAAM;QACpBtB,CAAC;MAEF,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,MAAM,CAACb,MAAM,EAAET,CAAC,EAAE,EAAE;QACnC,IAAIiC,MAAM,GAAGX,MAAM,CAACtB,CAAC,CAAC,EAAE;UACvB,OAAOA,CAAC;QACT;MACD;IACD,CAAC;IAEDb,SAAS,EAAG,SAAAA,CAAA,EAAY;MACvB,IAAI+C,SAAS,GAAG,IAAI,CAACX,IAAI;MACzB,OAAOY,MAAM,CAACC,IAAI,CAACjD,SAAS,CAAC,CAACkD,MAAM,CAAC,UAAUC,YAAY,EAAE;QAC5D,OAAOnD,SAAS,CAACmD,YAAY,CAAC,CAACrD,KAAK,CAACsD,OAAO,CAACL,SAAS,CAAC,KAAK,CAAC,CAAC;MAC/D,CAAC,CAAC;IACH,CAAC;IAEDM,KAAK,EAAG,SAAAA,CAAUR,SAAS,EAAE;MAC5B,IAAIC,MAAM,GAAI,CAACD,SAAS;QACvBX,OAAO,GAAG,IAAI,CAACA,OAAO;QACtBC,MAAM,GAAI,IAAI,CAACA,MAAM;QACrBmB,GAAG,GAAOnB,MAAM,CAACb,MAAM,GAAG,CAAC;QAC3BiC,MAAM;QAAEC,UAAU;QAAEC,UAAU;QAAE5C,CAAC;MAElC,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,GAAG,EAAEzC,CAAC,EAAE,EAAE;QACzB0C,MAAM,GAAOrB,OAAO,CAACrB,CAAC,CAAC;QACvB2C,UAAU,GAAGtB,OAAO,CAACrB,CAAC,GAAG,CAAC,CAAC;QAC3B4C,UAAU,GAAGvB,OAAO,CAACrB,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;QAEnC,IAAI0C,MAAM,GAAGC,UAAU,IAAIE,EAAE,CAACC,oBAAoB,EAAE;UACnDJ,MAAM,GAAGC,UAAU;QACpB,CAAC,MAAM,IAAID,MAAM,GAAGE,UAAU,IAAIC,EAAE,CAACE,kBAAkB,EAAE;UACxDL,MAAM,GAAGE,UAAU;QACpB;QAEA,IAAIX,MAAM,GAAGX,MAAM,CAACtB,CAAC,CAAC,GAAI0C,MAAM,GAAG,KAAM,EAAE;UAC1C,OAAOrB,OAAO,CAACrB,CAAC,CAAC;QAClB;MACD;MAEA,OAAOqB,OAAO,CAACoB,GAAG,CAAC;IACpB,CAAC;IAEDO,IAAI,EAAG,SAAAA,CAAUC,GAAG,EAAE;MACrB,OAAO,IAAI,CAACzB,KAAK,CAAC,IAAI,CAACO,MAAM,CAACkB,GAAG,CAAC,CAAC;IACpC,CAAC;IAEDP,MAAM,EAAG,SAAAA,CAAUO,GAAG,EAAE;MACvB1D,QAAQ,CAAC,4DAA4D,CAAC;MACtE,OAAO,IAAI,CAAC8B,OAAO,CAAC,IAAI,CAACU,MAAM,CAACkB,GAAG,CAAC,CAAC;IACtC,CAAC;IAEDC,SAAS,EAAG,SAAAA,CAAUD,GAAG,EAAE;MAC1B,OAAO,IAAI,CAAC5B,OAAO,CAAC,IAAI,CAACU,MAAM,CAACkB,GAAG,CAAC,CAAC;IACtC;EACD,CAAC;;EAED;AACD;AACA;;EAEC,SAASE,OAAOA,CAAEC,YAAY,EAAEC,UAAU,EAAE;IAC3C,IAAI,CAAC9B,IAAI,GAAG6B,YAAY;IACxB,IAAI,CAACnE,KAAK,GAAGoE,UAAU;EACxB;;EAEA;AACD;AACA;;EAEC,SAASC,QAAQA,CAACC,EAAE,EAAE;IACrB,IAAIC,UAAU,GAAGD,EAAE,CAACE,YAAY,CAAC,CAAC;IAClC,IAAIT,IAAI,GAAGQ,UAAU,CAACE,KAAK,CAAC,cAAc,CAAC;IAC3C,IAAIV,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;MACpB;MACA;MACAA,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAACU,KAAK,CAAC,QAAQ,CAAC;MAC9BV,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAACW,IAAI,CAAC,EAAE,CAAC,GAAG7E,SAAS;IACxC,CAAC,MAAM;MACN;MACA;MACAkE,IAAI,GAAGQ,UAAU,CAACE,KAAK,CAAC,aAAa,CAAC;MACtCV,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGlE,SAAS;IAClC;IAEA,IAAIkE,IAAI,KAAK,KAAK,EAAE;MACnBA,IAAI,GAAGlE,SAAS;IACjB;IAEA,IAAI,CAACyE,EAAE,GAAG,CAACA,EAAE;IACb,IAAI,CAACP,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACN,MAAM,GAAGa,EAAE,CAACK,iBAAiB,CAAC,CAAC;EACrC;EAEA,SAASC,SAASA,CAACC,IAAI,EAAE;IACxB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,SAAS,GAAG,CAAC;EACnB;EAEAH,SAAS,CAAChC,SAAS,CAACoC,aAAa,GAAG,UAAUC,QAAQ,EAAE;IACvD,IAAI,CAACH,WAAW,IAAIlD,IAAI,CAACsD,GAAG,CAAC,IAAI,CAACL,IAAI,CAACZ,SAAS,CAACgB,QAAQ,CAACX,EAAE,CAAC,GAAGW,QAAQ,CAACxB,MAAM,CAAC;IAChF,IAAI,IAAI,CAACoB,IAAI,CAACd,IAAI,CAACkB,QAAQ,CAACX,EAAE,CAAC,CAACa,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,KAAKF,QAAQ,CAAClB,IAAI,EAAE;MACzE,IAAI,CAACgB,SAAS,EAAE;IACjB;EACD,CAAC;EAED,SAASK,UAAUA,CAACC,GAAG,EAAEC,IAAI,EAAE;IAC9B,IAAIC,GAAG,EAAEC,IAAI;IAEb,OAAQA,IAAI,GAAG,CAAC,CAACF,IAAI,CAAChB,EAAE,GAAGe,GAAG,CAACf,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,GAAG,EAAG;MACtDiB,GAAG,GAAG,IAAIlB,QAAQ,CAAC,IAAIoB,IAAI,CAACJ,GAAG,CAACf,EAAE,GAAGkB,IAAI,CAAC,CAAC;MAC3C,IAAID,GAAG,CAAC9B,MAAM,KAAK4B,GAAG,CAAC5B,MAAM,EAAE;QAC9B4B,GAAG,GAAGE,GAAG;MACV,CAAC,MAAM;QACND,IAAI,GAAGC,GAAG;MACX;IACD;IAEA,OAAOF,GAAG;EACX;EAEA,SAASK,WAAWA,CAAA,EAAG;IACtB,IAAIC,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,GAAG,CAAC;MAC3CC,IAAI,GAAG,IAAIxB,QAAQ,CAAC,IAAIoB,IAAI,CAACE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC9CvD,OAAO,GAAG,CAACyD,IAAI,CAAC;MAChBC,MAAM;MAAEC,IAAI;MAAEhF,CAAC;IAEhB,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACxBgF,IAAI,GAAG,IAAI1B,QAAQ,CAAC,IAAIoB,IAAI,CAACE,SAAS,EAAE5E,CAAC,EAAE,CAAC,CAAC,CAAC;MAC9C,IAAIgF,IAAI,CAACtC,MAAM,KAAKoC,IAAI,CAACpC,MAAM,EAAE;QAChCqC,MAAM,GAAGV,UAAU,CAACS,IAAI,EAAEE,IAAI,CAAC;QAC/B3D,OAAO,CAAC4D,IAAI,CAACF,MAAM,CAAC;QACpB1D,OAAO,CAAC4D,IAAI,CAAC,IAAI3B,QAAQ,CAAC,IAAIoB,IAAI,CAACK,MAAM,CAACxB,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;MACtD;MACAuB,IAAI,GAAGE,IAAI;IACZ;IAEA,KAAKhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvBqB,OAAO,CAAC4D,IAAI,CAAC,IAAI3B,QAAQ,CAAC,IAAIoB,IAAI,CAACE,SAAS,GAAG5E,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzDqB,OAAO,CAAC4D,IAAI,CAAC,IAAI3B,QAAQ,CAAC,IAAIoB,IAAI,CAACE,SAAS,GAAG5E,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1D;IAEA,OAAOqB,OAAO;EACf;EAEA,SAAS6D,cAAcA,CAAEC,CAAC,EAAEC,CAAC,EAAE;IAC9B,IAAID,CAAC,CAACpB,WAAW,KAAKqB,CAAC,CAACrB,WAAW,EAAE;MACpC,OAAOoB,CAAC,CAACpB,WAAW,GAAGqB,CAAC,CAACrB,WAAW;IACrC;IACA,IAAIoB,CAAC,CAACnB,SAAS,KAAKoB,CAAC,CAACpB,SAAS,EAAE;MAChC,OAAOmB,CAAC,CAACnB,SAAS,GAAGoB,CAAC,CAACpB,SAAS;IACjC;IACA,IAAImB,CAAC,CAACrB,IAAI,CAACrC,UAAU,KAAK2D,CAAC,CAACtB,IAAI,CAACrC,UAAU,EAAE;MAC5C,OAAO2D,CAAC,CAACtB,IAAI,CAACrC,UAAU,GAAG0D,CAAC,CAACrB,IAAI,CAACrC,UAAU;IAC7C;IACA,OAAO2D,CAAC,CAACtB,IAAI,CAACvC,IAAI,CAAC8D,aAAa,CAACF,CAAC,CAACrB,IAAI,CAACvC,IAAI,CAAC;EAC9C;EAEA,SAAS+D,YAAYA,CAAE/D,IAAI,EAAEF,OAAO,EAAE;IACrC,IAAIrB,CAAC,EAAE0C,MAAM;IACbhC,UAAU,CAACW,OAAO,CAAC;IACnB,KAAKrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,OAAO,CAACZ,MAAM,EAAET,CAAC,EAAE,EAAE;MACpC0C,MAAM,GAAGrB,OAAO,CAACrB,CAAC,CAAC;MACnBX,OAAO,CAACqD,MAAM,CAAC,GAAGrD,OAAO,CAACqD,MAAM,CAAC,IAAI,CAAC,CAAC;MACvCrD,OAAO,CAACqD,MAAM,CAAC,CAACnB,IAAI,CAAC,GAAG,IAAI;IAC7B;EACD;EAEA,SAASgE,qBAAqBA,CAAElE,OAAO,EAAE;IACxC,IAAImE,aAAa,GAAGnE,OAAO,CAACZ,MAAM;MACjCgF,eAAe,GAAG,CAAC,CAAC;MACpBnF,GAAG,GAAG,EAAE;MACRN,CAAC;MAAE0F,CAAC;MAAEC,aAAa;IAEpB,KAAK3F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwF,aAAa,EAAExF,CAAC,EAAE,EAAE;MACnC2F,aAAa,GAAGtG,OAAO,CAACgC,OAAO,CAACrB,CAAC,CAAC,CAAC0C,MAAM,CAAC,IAAI,CAAC,CAAC;MAChD,KAAKgD,CAAC,IAAIC,aAAa,EAAE;QACxB,IAAIA,aAAa,CAACC,cAAc,CAACF,CAAC,CAAC,EAAE;UACpCD,eAAe,CAACC,CAAC,CAAC,GAAG,IAAI;QAC1B;MACD;IACD;IAEA,KAAK1F,CAAC,IAAIyF,eAAe,EAAE;MAC1B,IAAIA,eAAe,CAACG,cAAc,CAAC5F,CAAC,CAAC,EAAE;QACtCM,GAAG,CAAC2E,IAAI,CAAC7F,KAAK,CAACY,CAAC,CAAC,CAAC;MACnB;IACD;IAEA,OAAOM,GAAG;EACX;EAEA,SAASuF,YAAYA,CAAA,EAAI;IAExB;IACA,IAAI;MACH,IAAIC,QAAQ,GAAGC,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACC,QAAQ;MAC/D,IAAIJ,QAAQ,IAAIA,QAAQ,CAACrF,MAAM,GAAG,CAAC,EAAE;QACpC,IAAIc,IAAI,GAAGnC,KAAK,CAAC+G,aAAa,CAACL,QAAQ,CAAC,CAAC;QACzC,IAAIvE,IAAI,EAAE;UACT,OAAOA,IAAI;QACZ;QACAhC,QAAQ,CAAC,wBAAwB,GAAGuG,QAAQ,GAAG,wDAAwD,CAAC;MACzG;IACD,CAAC,CAAC,OAAOM,CAAC,EAAE;MACX;IAAA;IAGD,IAAI/E,OAAO,GAAGsD,WAAW,CAAC,CAAC;MAC1Ba,aAAa,GAAGnE,OAAO,CAACZ,MAAM;MAC9BpB,OAAO,GAAGkG,qBAAqB,CAAClE,OAAO,CAAC;MACxCgF,UAAU,GAAG,EAAE;MACfC,SAAS;MAAEtG,CAAC;MAAE0F,CAAC;IAEhB,KAAK1F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,OAAO,CAACoB,MAAM,EAAET,CAAC,EAAE,EAAE;MACpCsG,SAAS,GAAG,IAAIzC,SAAS,CAAC0C,OAAO,CAAClH,OAAO,CAACW,CAAC,CAAC,CAAC,EAAEwF,aAAa,CAAC;MAC7D,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,EAAEE,CAAC,EAAE,EAAE;QACnCY,SAAS,CAACrC,aAAa,CAAC5C,OAAO,CAACqE,CAAC,CAAC,CAAC;MACpC;MACAW,UAAU,CAACpB,IAAI,CAACqB,SAAS,CAAC;IAC3B;IAEAD,UAAU,CAACG,IAAI,CAACtB,cAAc,CAAC;IAE/B,OAAOmB,UAAU,CAAC5F,MAAM,GAAG,CAAC,GAAG4F,UAAU,CAAC,CAAC,CAAC,CAACvC,IAAI,CAACvC,IAAI,GAAGzC,SAAS;EACnE;EAEA,SAAS2H,KAAKA,CAAEC,WAAW,EAAE;IAC5B,IAAI,CAACpH,WAAW,IAAIoH,WAAW,EAAE;MAChCpH,WAAW,GAAGuG,YAAY,CAAC,CAAC;IAC7B;IACA,OAAOvG,WAAW;EACnB;;EAEA;AACD;AACA;;EAEC,SAAS6G,aAAaA,CAAE5E,IAAI,EAAE;IAC7B,OAAO,CAACA,IAAI,IAAI,EAAE,EAAEoF,WAAW,CAAC,CAAC,CAACvC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EACtD;EAEA,SAASwC,OAAOA,CAAEC,MAAM,EAAE;IACzB,IAAI7G,CAAC,EAAEuB,IAAI,EAAE9B,KAAK,EAAEqH,UAAU;IAE9B,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;MAC/BA,MAAM,GAAG,CAACA,MAAM,CAAC;IAClB;IAEA,KAAK7G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6G,MAAM,CAACpG,MAAM,EAAET,CAAC,EAAE,EAAE;MACnCP,KAAK,GAAGoH,MAAM,CAAC7G,CAAC,CAAC,CAACP,KAAK,CAAC,GAAG,CAAC;MAC5B8B,IAAI,GAAG9B,KAAK,CAAC,CAAC,CAAC;MACfqH,UAAU,GAAGX,aAAa,CAAC5E,IAAI,CAAC;MAChCtC,KAAK,CAAC6H,UAAU,CAAC,GAAGD,MAAM,CAAC7G,CAAC,CAAC;MAC7BZ,KAAK,CAAC0H,UAAU,CAAC,GAAGvF,IAAI;MACxB+D,YAAY,CAACwB,UAAU,EAAErH,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9C;EACD;EAEA,SAAS8G,OAAOA,CAAEhF,IAAI,EAAEwF,MAAM,EAAE;IAE/BxF,IAAI,GAAG4E,aAAa,CAAC5E,IAAI,CAAC;IAE1B,IAAIuC,IAAI,GAAG7E,KAAK,CAACsC,IAAI,CAAC;IACtB,IAAIyF,IAAI;IAER,IAAIlD,IAAI,YAAYpC,IAAI,EAAE;MACzB,OAAOoC,IAAI;IACZ;IAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC7BA,IAAI,GAAG,IAAIpC,IAAI,CAACoC,IAAI,CAAC;MACrB7E,KAAK,CAACsC,IAAI,CAAC,GAAGuC,IAAI;MAClB,OAAOA,IAAI;IACZ;;IAEA;IACA,IAAI5E,KAAK,CAACqC,IAAI,CAAC,IAAIwF,MAAM,KAAKR,OAAO,KAAKS,IAAI,GAAGT,OAAO,CAACrH,KAAK,CAACqC,IAAI,CAAC,EAAEgF,OAAO,CAAC,CAAC,EAAE;MAChFzC,IAAI,GAAG7E,KAAK,CAACsC,IAAI,CAAC,GAAG,IAAIG,IAAI,CAAC,CAAC;MAC/BoC,IAAI,CAAClC,IAAI,CAACoF,IAAI,CAAC;MACflD,IAAI,CAACvC,IAAI,GAAGnC,KAAK,CAACmC,IAAI,CAAC;MACvB,OAAOuC,IAAI;IACZ;IAEA,OAAO,IAAI;EACZ;EAEA,SAASmD,QAAQA,CAAA,EAAI;IACpB,IAAIjH,CAAC;MAAEM,GAAG,GAAG,EAAE;IAEf,KAAKN,CAAC,IAAIZ,KAAK,EAAE;MAChB,IAAIA,KAAK,CAACwG,cAAc,CAAC5F,CAAC,CAAC,KAAKf,KAAK,CAACe,CAAC,CAAC,IAAIf,KAAK,CAACC,KAAK,CAACc,CAAC,CAAC,CAAC,CAAC,IAAIZ,KAAK,CAACY,CAAC,CAAC,EAAE;QACzEM,GAAG,CAAC2E,IAAI,CAAC7F,KAAK,CAACY,CAAC,CAAC,CAAC;MACnB;IACD;IAEA,OAAOM,GAAG,CAACkG,IAAI,CAAC,CAAC;EAClB;EAEA,SAASU,eAAeA,CAAA,EAAI;IAC3B,OAAO/E,MAAM,CAACC,IAAI,CAACjD,SAAS,CAAC;EAC9B;EAEA,SAASgI,OAAOA,CAAEC,OAAO,EAAE;IAC1B,IAAIpH,CAAC,EAAEqH,KAAK,EAAEC,OAAO,EAAEC,OAAO;IAE9B,IAAI,OAAOH,OAAO,KAAK,QAAQ,EAAE;MAChCA,OAAO,GAAG,CAACA,OAAO,CAAC;IACpB;IAEA,KAAKpH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoH,OAAO,CAAC3G,MAAM,EAAET,CAAC,EAAE,EAAE;MACpCqH,KAAK,GAAGD,OAAO,CAACpH,CAAC,CAAC,CAACP,KAAK,CAAC,GAAG,CAAC;MAE7B6H,OAAO,GAAGnB,aAAa,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC;MACjCE,OAAO,GAAGpB,aAAa,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC;MAEjCnI,KAAK,CAACoI,OAAO,CAAC,GAAGC,OAAO;MACxBnI,KAAK,CAACkI,OAAO,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;MAEzBnI,KAAK,CAACqI,OAAO,CAAC,GAAGD,OAAO;MACxBlI,KAAK,CAACmI,OAAO,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;IAC1B;EACD;EAEA,SAASG,YAAYA,CAAEpG,IAAI,EAAE;IAC5B,IAAIpB,CAAC,EAAEsC,YAAY,EAAEmF,aAAa,EAAEhI,KAAK;IACzC,IAAI,CAAC2B,IAAI,IAAI,CAACA,IAAI,CAACX,MAAM,EAAE;IAC3B,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,IAAI,CAACX,MAAM,EAAET,CAAC,EAAE,EAAE;MACjCP,KAAK,GAAG2B,IAAI,CAACpB,CAAC,CAAC,CAACP,KAAK,CAAC,GAAG,CAAC;MAC1B6C,YAAY,GAAG7C,KAAK,CAAC,CAAC,CAAC,CAACiI,WAAW,CAAC,CAAC;MACrCD,aAAa,GAAGhI,KAAK,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC;MACnCN,SAAS,CAACmD,YAAY,CAAC,GAAG,IAAIa,OAAO,CACpCb,YAAY,EACZmF,aACD,CAAC;IACF;EACD;EAEA,SAASE,UAAUA,CAAEpG,IAAI,EAAE;IAC1BA,IAAI,GAAGA,IAAI,CAACmG,WAAW,CAAC,CAAC;IACzB,OAAOvI,SAAS,CAACoC,IAAI,CAAC,IAAI,IAAI;EAC/B;EAEA,SAASqG,eAAeA,CAACC,OAAO,EAAEC,WAAW,EAAE;IAC9CD,OAAO,GAAGF,UAAU,CAACE,OAAO,CAAC;IAE7B,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB,IAAI5I,KAAK,GAAG4I,OAAO,CAAC5I,KAAK,CAACuH,IAAI,CAAC,CAAC;IAEhC,IAAIsB,WAAW,EAAE;MAChB,OAAO7I,KAAK,CAAC8I,GAAG,CAAC,UAAU7F,SAAS,EAAE;QACrC,IAAI4B,IAAI,GAAGyC,OAAO,CAACrE,SAAS,CAAC;QAC7B,OAAO;UACNX,IAAI,EAAEW,SAAS;UACfQ,MAAM,EAAEoB,IAAI,CAACZ,SAAS,CAAC,IAAIwB,IAAI,CAAC,CAAC;QAClC,CAAC;MACF,CAAC,CAAC;IACH;IAEA,OAAOzF,KAAK;EACb;EAEA,SAAS+I,QAAQA,CAAE5G,IAAI,EAAE;IACxBwF,OAAO,CAACxF,IAAI,CAACnC,KAAK,CAAC;IACnBkI,OAAO,CAAC/F,IAAI,CAAClC,KAAK,CAAC;IACnBsI,YAAY,CAACpG,IAAI,CAACjC,SAAS,CAAC;IAC5B0D,EAAE,CAACoF,WAAW,GAAG7G,IAAI,CAACvC,OAAO;EAC9B;EAEA,SAASqJ,UAAUA,CAAE3G,IAAI,EAAE;IAC1B,IAAI,CAAC2G,UAAU,CAACC,YAAY,EAAE;MAC7BD,UAAU,CAACC,YAAY,GAAG,IAAI;MAC7B5I,QAAQ,CAAC,wBAAwB,GAAGgC,IAAI,GAAG,sDAAsD,GAAGA,IAAI,GAAG,IAAI,CAAC;IAClH;IACA,OAAO,CAAC,CAACgF,OAAO,CAAChF,IAAI,CAAC;EACvB;EAEA,SAAS6G,WAAWA,CAAEC,CAAC,EAAE;IACxB,IAAIC,eAAe,GAAID,CAAC,CAACE,EAAE,KAAK,GAAG,IAAIF,CAAC,CAACE,EAAE,KAAK,GAAI;IACpD,OAAO,CAAC,EAAEF,CAAC,CAACG,EAAE,IAAKH,CAAC,CAACI,IAAI,KAAK3J,SAAU,IAAI,CAACwJ,eAAe,CAAC;EAC9D;EAEA,SAAS/I,QAAQA,CAAEmJ,OAAO,EAAE;IAC3B,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;MAC1ED,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACvB;EACD;;EAEA;AACD;AACA;;EAEC,SAAS7F,EAAEA,CAAEgG,KAAK,EAAE;IACnB,IAAIC,IAAI,GAAGC,KAAK,CAAClH,SAAS,CAACmH,KAAK,CAACC,IAAI,CAACC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACtD3H,IAAI,GAAG2H,SAAS,CAACA,SAAS,CAACzI,MAAM,GAAG,CAAC,CAAC;MACtCqD,IAAI,GAAGyC,OAAO,CAAChF,IAAI,CAAC;MACpBjB,GAAG,GAAI1B,MAAM,CAACuK,GAAG,CAACC,KAAK,CAAC,IAAI,EAAEN,IAAI,CAAC;IAEpC,IAAIhF,IAAI,IAAI,CAAClF,MAAM,CAACyK,QAAQ,CAACR,KAAK,CAAC,IAAIT,WAAW,CAAC9H,GAAG,CAAC,EAAE;MACxDA,GAAG,CAACgJ,GAAG,CAACxF,IAAI,CAACtB,KAAK,CAAClC,GAAG,CAAC,EAAE,SAAS,CAAC;IACpC;IAEAA,GAAG,CAACuC,EAAE,CAACtB,IAAI,CAAC;IAEZ,OAAOjB,GAAG;EACX;EAEAuC,EAAE,CAAChE,OAAO,GAAQG,OAAO;EACzB6D,EAAE,CAACoF,WAAW,GAAI,EAAE;EACpBpF,EAAE,CAAC0G,MAAM,GAAStK,KAAK;EACvB4D,EAAE,CAAC2G,MAAM,GAAStK,KAAK;EACvB2D,EAAE,CAAC4G,MAAM,GAASrK,KAAK;EACvByD,EAAE,CAAC6G,UAAU,GAAGvK,SAAS;EACzB0D,EAAE,CAACyG,GAAG,GAAY1C,OAAO;EACzB/D,EAAE,CAACmE,IAAI,GAAWG,OAAO;EACzBtE,EAAE,CAAC8G,IAAI,GAAW3B,QAAQ;EAC1BnF,EAAE,CAACiB,IAAI,GAAWyC,OAAO;EACzB1D,EAAE,CAACqF,UAAU,GAAKA,UAAU,CAAC,CAAC;EAC9BrF,EAAE,CAAC4D,KAAK,GAAUA,KAAK;EACvB5D,EAAE,CAACzD,KAAK,GAAU6H,QAAQ;EAC1BpE,EAAE,CAACnB,IAAI,GAAWA,IAAI;EACtBmB,EAAE,CAAC1B,MAAM,GAASA,MAAM;EACxB0B,EAAE,CAAC/C,YAAY,GAAGA,YAAY;EAC9B+C,EAAE,CAACuF,WAAW,GAAIA,WAAW;EAC7BvF,EAAE,CAACE,kBAAkB,GAAK,IAAI;EAC9BF,EAAE,CAACC,oBAAoB,GAAG,KAAK;EAC/BD,EAAE,CAAC1D,SAAS,GAAM+H,eAAe;EACjCrE,EAAE,CAAC+E,eAAe,GAAGA,eAAe;;EAEpC;AACD;AACA;;EAEC,IAAIgC,EAAE,GAAGhL,MAAM,CAACgL,EAAE;EAElBhL,MAAM,CAACiE,EAAE,GAAGA,EAAE;EAEdjE,MAAM,CAACiL,WAAW,GAAG,IAAI;EAEzBjL,MAAM,CAACkL,YAAY,GAAG,UAAU7G,GAAG,EAAE8G,QAAQ,EAAE;IAC9C,IAAIjG,IAAI,GAAGlF,MAAM,CAACiL,WAAW;MAC5BnH,MAAM;IAEP,IAAIO,GAAG,CAAC+G,EAAE,KAAKlL,SAAS,EAAE;MACzB,IAAIgF,IAAI,IAAIsE,WAAW,CAACnF,GAAG,CAAC,IAAI,CAACA,GAAG,CAACgH,MAAM,EAAE;QAC5ChH,GAAG,CAACiH,EAAE,GAAGtL,MAAM,CAACuK,GAAG,CAAClG,GAAG,CAACuF,EAAE,CAAC,CAAC0B,EAAE;QAC9BjH,GAAG,CAACkG,GAAG,CAAC,CAAC,CAACG,GAAG,CAACxF,IAAI,CAACtB,KAAK,CAACS,GAAG,CAAC,EAAE,SAAS,CAAC;MAC1C;MACAA,GAAG,CAAC+G,EAAE,GAAGlG,IAAI;IACd;IACA,IAAIb,GAAG,CAAC+G,EAAE,EAAE;MACXtH,MAAM,GAAGO,GAAG,CAAC+G,EAAE,CAAC9G,SAAS,CAACD,GAAG,CAAC;MAC9B,IAAIpC,IAAI,CAACsD,GAAG,CAACzB,MAAM,CAAC,GAAG,EAAE,EAAE;QAC1BA,MAAM,GAAGA,MAAM,GAAG,EAAE;MACrB;MACA,IAAIO,GAAG,CAACC,SAAS,KAAKpE,SAAS,EAAE;QAChC,IAAIqL,CAAC,GAAGlH,GAAG,CAAC+G,EAAE;QACd/G,GAAG,CAACC,SAAS,CAAC,CAACR,MAAM,EAAEqH,QAAQ,CAAC;QAChC9G,GAAG,CAAC+G,EAAE,GAAGG,CAAC;MACX,CAAC,MAAM;QACNlH,GAAG,CAACa,IAAI,CAACpB,MAAM,EAAEqH,QAAQ,CAAC;MAC3B;IACD;EACD,CAAC;EAEDH,EAAE,CAAC/G,EAAE,GAAG,UAAUtB,IAAI,EAAEwI,QAAQ,EAAE;IACjC,IAAIxI,IAAI,EAAE;MACT,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAI6I,KAAK,CAAC,uCAAuC,GAAG7I,IAAI,GAAG,IAAI,GAAG,OAAOA,IAAI,GAAG,GAAG,CAAC;MAC3F;MACA,IAAI,CAACyI,EAAE,GAAGzD,OAAO,CAAChF,IAAI,CAAC;MACvB,IAAI,IAAI,CAACyI,EAAE,EAAE;QACZpL,MAAM,CAACkL,YAAY,CAAC,IAAI,EAAEC,QAAQ,CAAC;MACpC,CAAC,MAAM;QACNxK,QAAQ,CAAC,kCAAkC,GAAGgC,IAAI,GAAG,0DAA0D,CAAC;MACjH;MACA,OAAO,IAAI;IACZ;IACA,IAAI,IAAI,CAACyI,EAAE,EAAE;MAAE,OAAO,IAAI,CAACA,EAAE,CAACzI,IAAI;IAAE;EACrC,CAAC;EAED,SAAS8I,QAAQA,CAAEC,GAAG,EAAE;IACvB,OAAO,YAAY;MAClB,IAAI,IAAI,CAACN,EAAE,EAAE;QAAE,OAAO,IAAI,CAACA,EAAE,CAAChH,IAAI,CAAC,IAAI,CAAC;MAAE;MAC1C,OAAOsH,GAAG,CAACrB,IAAI,CAAC,IAAI,CAAC;IACtB,CAAC;EACF;EAEA,SAASsB,aAAaA,CAAED,GAAG,EAAE;IAC5B,OAAO,YAAY;MAClB,IAAI,CAACN,EAAE,GAAG,IAAI;MACd,OAAOM,GAAG,CAAClB,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;IAClC,CAAC;EACF;EAEA,SAASsB,cAAcA,CAAEF,GAAG,EAAE;IAC7B,OAAO,YAAY;MAClB,IAAIpB,SAAS,CAACzI,MAAM,GAAG,CAAC,EAAE,IAAI,CAACuJ,EAAE,GAAG,IAAI;MACxC,OAAOM,GAAG,CAAClB,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;IAClC,CAAC;EACF;EAEAU,EAAE,CAACa,QAAQ,GAAIJ,QAAQ,CAACT,EAAE,CAACa,QAAQ,CAAC;EACpCb,EAAE,CAACc,QAAQ,GAAIL,QAAQ,CAACT,EAAE,CAACc,QAAQ,CAAC;EACpCd,EAAE,CAACT,GAAG,GAASoB,aAAa,CAACX,EAAE,CAACT,GAAG,CAAC;EACpCS,EAAE,CAACe,KAAK,GAAOJ,aAAa,CAACX,EAAE,CAACe,KAAK,CAAC;EACtCf,EAAE,CAAC1G,SAAS,GAAGsH,cAAc,CAACZ,EAAE,CAAC1G,SAAS,CAAC;EAE3CtE,MAAM,CAACiE,EAAE,CAAC+H,UAAU,GAAG,UAASrJ,IAAI,EAAE;IACrC,IAAI7B,KAAK,GAAG,CAAC,IAAKA,KAAK,KAAK,CAAC,IAAIC,KAAK,GAAG,CAAE,EAAE;MAC5CJ,QAAQ,CAAC,oFAAoF,GAAGX,MAAM,CAACC,OAAO,GAAG,GAAG,CAAC;IACtH;IACAD,MAAM,CAACiL,WAAW,GAAGtI,IAAI,GAAGgF,OAAO,CAAChF,IAAI,CAAC,GAAG,IAAI;IAChD,OAAO3C,MAAM;EACd,CAAC;;EAED;EACA,IAAIiM,gBAAgB,GAAGjM,MAAM,CAACiM,gBAAgB;EAC9C,IAAI1I,MAAM,CAACN,SAAS,CAACiJ,QAAQ,CAAC7B,IAAI,CAAC4B,gBAAgB,CAAC,KAAK,gBAAgB,EAAE;IAC1E;IACAA,gBAAgB,CAAC5F,IAAI,CAAC,IAAI,CAAC;IAC3B4F,gBAAgB,CAAC5F,IAAI,CAAC,IAAI,CAAC;EAC5B,CAAC,MAAM,IAAI4F,gBAAgB,EAAE;IAC5B;IACAA,gBAAgB,CAACb,EAAE,GAAG,IAAI;EAC3B;;EAEA;;EAEA,OAAOpL,MAAM;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}