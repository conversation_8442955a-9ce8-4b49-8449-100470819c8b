{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _MatTabContent, _MatTab<PERSON><PERSON>l, _MatTab, _Ink<PERSON>ar<PERSON>tem, _<PERSON><PERSON>ab<PERSON><PERSON><PERSON><PERSON>rapper, _<PERSON>Paginated<PERSON><PERSON><PERSON><PERSON>er, _<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, _<PERSON><PERSON><PERSON><PERSON>odyPortal, _MatTabBody, _MatTabGroup, _MatTabNav, _MatTabLink, _MatTabNavPanel, _MatTabsModule;\nconst _c0 = [\"*\"];\nfunction _MatTab_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c1 = [\"tabListContainer\"];\nconst _c2 = [\"tabList\"];\nconst _c3 = [\"tabListInner\"];\nconst _c4 = [\"nextPaginator\"];\nconst _c5 = [\"previousPaginator\"];\nconst _c6 = [\"content\"];\nfunction _MatTabBody_ng_template_2_Template(rf, ctx) {}\nconst _c7 = [\"tabBodyWrapper\"];\nconst _c8 = [\"tabHeader\"];\nfunction _MatTabGroup_For_3_Conditional_6_ng_template_0_Template(rf, ctx) {}\nfunction _MatTabGroup_For_3_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, _MatTabGroup_For_3_Conditional_6_ng_template_0_Template, 0, 0, \"ng-template\", 12);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"cdkPortalOutlet\", tab_r4.templateLabel);\n  }\n}\nfunction _MatTabGroup_For_3_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(tab_r4.textLabel);\n  }\n}\nfunction _MatTabGroup_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7, 2);\n    i0.ɵɵlistener(\"click\", function _MatTabGroup_For_3_Template_div_click_0_listener() {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const tab_r4 = ctx_r2.$implicit;\n      const $index_r5 = ctx_r2.$index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const tabHeader_r7 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(ctx_r5._handleClick(tab_r4, tabHeader_r7, $index_r5));\n    })(\"cdkFocusChange\", function _MatTabGroup_For_3_Template_div_cdkFocusChange_0_listener($event) {\n      const $index_r5 = i0.ɵɵrestoreView(_r2).$index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._tabFocusChanged($event, $index_r5));\n    });\n    i0.ɵɵelement(2, \"span\", 8)(3, \"div\", 9);\n    i0.ɵɵelementStart(4, \"span\", 10)(5, \"span\", 11);\n    i0.ɵɵtemplate(6, _MatTabGroup_For_3_Conditional_6_Template, 1, 1, null, 12)(7, _MatTabGroup_For_3_Conditional_7_Template, 1, 1);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tab_r4 = ctx.$implicit;\n    const $index_r5 = ctx.$index;\n    const tabNode_r8 = i0.ɵɵreference(1);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r4.labelClass);\n    i0.ɵɵclassProp(\"mdc-tab--active\", ctx_r5.selectedIndex === $index_r5);\n    i0.ɵɵproperty(\"id\", ctx_r5._getTabLabelId(tab_r4, $index_r5))(\"disabled\", tab_r4.disabled)(\"fitInkBarToContent\", ctx_r5.fitInkBarToContent);\n    i0.ɵɵattribute(\"tabIndex\", ctx_r5._getTabIndex($index_r5))(\"aria-posinset\", $index_r5 + 1)(\"aria-setsize\", ctx_r5._tabs.length)(\"aria-controls\", ctx_r5._getTabContentId($index_r5))(\"aria-selected\", ctx_r5.selectedIndex === $index_r5)(\"aria-label\", tab_r4.ariaLabel || null)(\"aria-labelledby\", !tab_r4.ariaLabel && tab_r4.ariaLabelledby ? tab_r4.ariaLabelledby : null);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matRippleTrigger\", tabNode_r8)(\"matRippleDisabled\", tab_r4.disabled || ctx_r5.disableRipple);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(tab_r4.templateLabel ? 6 : 7);\n  }\n}\nfunction _MatTabGroup_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction _MatTabGroup_For_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tab-body\", 13);\n    i0.ɵɵlistener(\"_onCentered\", function _MatTabGroup_For_8_Template_mat_tab_body__onCentered_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._removeTabBodyWrapperHeight());\n    })(\"_onCentering\", function _MatTabGroup_For_8_Template_mat_tab_body__onCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._setTabBodyWrapperHeight($event));\n    })(\"_beforeCentering\", function _MatTabGroup_For_8_Template_mat_tab_body__beforeCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._bodyCentered($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r10 = ctx.$implicit;\n    const $index_r11 = ctx.$index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r10.bodyClass);\n    i0.ɵɵproperty(\"id\", ctx_r5._getTabContentId($index_r11))(\"content\", tab_r10.content)(\"position\", tab_r10.position)(\"animationDuration\", ctx_r5.animationDuration)(\"preserveContent\", ctx_r5.preserveContent);\n    i0.ɵɵattribute(\"tabindex\", ctx_r5.contentTabIndex != null && ctx_r5.selectedIndex === $index_r11 ? ctx_r5.contentTabIndex : null)(\"aria-labelledby\", ctx_r5._getTabLabelId(tab_r10, $index_r11))(\"aria-hidden\", ctx_r5.selectedIndex !== $index_r11);\n  }\n}\nconst _c9 = [\"mat-tab-nav-bar\", \"\"];\nconst _c10 = [\"mat-tab-link\", \"\"];\nimport { FocusKeyManager, _IdGenerator, CdkMonitorFocus, FocusMonitor } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { Platform, _bindEventWithOptions } from '@angular/cdk/platform';\nimport { ViewportRuler, CdkScrollable } from '@angular/cdk/scrolling';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChild, ViewChild, ElementRef, ChangeDetectorRef, NgZone, Injector, Renderer2, ANIMATION_MODULE_TYPE, EventEmitter, afterNextRender, numberAttribute, Output, ContentChildren, QueryList, ViewChildren, signal, forwardRef, computed, HostAttributeToken, NgModule } from '@angular/core';\nimport { Subject, of, merge, EMPTY, Observable, timer, Subscription, BehaviorSubject } from 'rxjs';\nimport { debounceTime, takeUntil, startWith, switchMap, skip, filter } from 'rxjs/operators';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet } from '@angular/cdk/portal';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { CdkObserveContent } from '@angular/cdk/observers';\nimport { M as MatRipple, a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BT3tzh6F.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/coercion';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n  constructor() {\n    _defineProperty(this, \"template\", inject(TemplateRef));\n  }\n}\n_MatTabContent = MatTabContent;\n_defineProperty(MatTabContent, \"\\u0275fac\", function _MatTabContent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTabContent)();\n});\n_defineProperty(MatTabContent, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabContent,\n  selectors: [[\"\", \"matTabContent\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB_CONTENT,\n    useExisting: _MatTabContent\n  }])]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabContent, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabContent]',\n      providers: [{\n        provide: MAT_TAB_CONTENT,\n        useExisting: MatTabContent\n      }]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"_closestTab\", inject(MAT_TAB, {\n      optional: true\n    }));\n  }\n}\n_MatTabLabel = MatTabLabel;\n_defineProperty(MatTabLabel, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatTabLabel_BaseFactory;\n  return function _MatTabLabel_Factory(__ngFactoryType__) {\n    return (ɵ_MatTabLabel_BaseFactory || (ɵ_MatTabLabel_BaseFactory = i0.ɵɵgetInheritedFactory(_MatTabLabel)))(__ngFactoryType__ || _MatTabLabel);\n  };\n})());\n_defineProperty(MatTabLabel, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabLabel,\n  selectors: [[\"\", \"mat-tab-label\", \"\"], [\"\", \"matTabLabel\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB_LABEL,\n    useExisting: _MatTabLabel\n  }]), i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-tab-label], [matTabLabel]',\n      providers: [{\n        provide: MAT_TAB_LABEL,\n        useExisting: MatTabLabel\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\nclass MatTab {\n  /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n  get templateLabel() {\n    return this._templateLabel;\n  }\n  set templateLabel(value) {\n    this._setTemplateLabelInput(value);\n  }\n  /** @docs-private */\n  get content() {\n    return this._contentPortal;\n  }\n  /** Emits whenever the internal state of the tab changes. */\n\n  constructor() {\n    _defineProperty(this, \"_viewContainerRef\", inject(ViewContainerRef));\n    _defineProperty(this, \"_closestTabGroup\", inject(MAT_TAB_GROUP, {\n      optional: true\n    }));\n    /** whether the tab is disabled. */\n    _defineProperty(this, \"disabled\", false);\n    _defineProperty(this, \"_templateLabel\", void 0);\n    /**\n     * Template provided in the tab content that will be used if present, used to enable lazy-loading\n     */\n    _defineProperty(this, \"_explicitContent\", undefined);\n    /** Template inside the MatTab view that contains an `<ng-content>`. */\n    _defineProperty(this, \"_implicitContent\", void 0);\n    /** Plain text label for the tab, used when there is no template label. */\n    _defineProperty(this, \"textLabel\", '');\n    /** Aria label for the tab. */\n    _defineProperty(this, \"ariaLabel\", void 0);\n    /**\n     * Reference to the element that the tab is labelled by.\n     * Will be cleared if `aria-label` is set at the same time.\n     */\n    _defineProperty(this, \"ariaLabelledby\", void 0);\n    /** Classes to be passed to the tab label inside the mat-tab-header container. */\n    _defineProperty(this, \"labelClass\", void 0);\n    /** Classes to be passed to the tab mat-tab-body container. */\n    _defineProperty(this, \"bodyClass\", void 0);\n    /**\n     * Custom ID for the tab, overriding the auto-generated one by Material.\n     * Note that when using this input, it's your responsibility to ensure that the ID is unique.\n     */\n    _defineProperty(this, \"id\", null);\n    /** Portal that will be the hosted content of the tab */\n    _defineProperty(this, \"_contentPortal\", null);\n    _defineProperty(this, \"_stateChanges\", new Subject());\n    /**\n     * The relatively indexed position where 0 represents the center, negative is left, and positive\n     * represents the right.\n     */\n    _defineProperty(this, \"position\", null);\n    // TODO(crisbeto): we no longer use this, but some internal apps appear to rely on it.\n    /**\n     * The initial relatively index origin of the tab if it was created and selected after there\n     * was already a selected tab. Provides context of what position the tab should originate from.\n     */\n    _defineProperty(this, \"origin\", null);\n    /**\n     * Whether the tab is currently active.\n     */\n    _defineProperty(this, \"isActive\", false);\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n  }\n  ngOnChanges(changes) {\n    if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n      this._stateChanges.next();\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  ngOnInit() {\n    this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setTemplateLabelInput(value) {\n    // Only update the label if the query managed to find one. This works around an issue where a\n    // user may have manually set `templateLabel` during creation mode, which would then get\n    // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n    // tab matches the current one so that we don't pick up labels from nested tabs.\n    if (value && value._closestTab === this) {\n      this._templateLabel = value;\n    }\n  }\n}\n_MatTab = MatTab;\n_defineProperty(MatTab, \"\\u0275fac\", function _MatTab_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTab)();\n});\n_defineProperty(MatTab, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatTab,\n  selectors: [[\"mat-tab\"]],\n  contentQueries: function _MatTab_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTabLabel, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatTabContent, 7, TemplateRef);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateLabel = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._explicitContent = _t.first);\n    }\n  },\n  viewQuery: function _MatTab_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._implicitContent = _t.first);\n    }\n  },\n  hostAttrs: [\"hidden\", \"\"],\n  hostVars: 1,\n  hostBindings: function _MatTab_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", null);\n    }\n  },\n  inputs: {\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n    textLabel: [0, \"label\", \"textLabel\"],\n    ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n    labelClass: \"labelClass\",\n    bodyClass: \"bodyClass\",\n    id: \"id\"\n  },\n  exportAs: [\"matTab\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB,\n    useExisting: _MatTab\n  }]), i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function _MatTab_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, _MatTab_ng_template_0_Template, 1, 0, \"ng-template\");\n    }\n  },\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTab, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matTab',\n      providers: [{\n        provide: MAT_TAB,\n        useExisting: MatTab\n      }],\n      host: {\n        // This element will be rendered on the server in order to support hydration.\n        // Hide it so it doesn't cause a layout shift when it's removed on the client.\n        'hidden': '',\n        // Clear any custom IDs from the tab since they'll be forwarded to the actual tab.\n        '[attr.id]': 'null'\n      },\n      template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\"\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    templateLabel: [{\n      type: ContentChild,\n      args: [MatTabLabel]\n    }],\n    _explicitContent: [{\n      type: ContentChild,\n      args: [MatTabContent, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    _implicitContent: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    textLabel: [{\n      type: Input,\n      args: ['label']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    labelClass: [{\n      type: Input\n    }],\n    bodyClass: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n  constructor(_items) {\n    _defineProperty(this, \"_items\", void 0);\n    /** Item to which the ink bar is aligned currently. */\n    _defineProperty(this, \"_currentItem\", void 0);\n    this._items = _items;\n  }\n  /** Hides the ink bar. */\n  hide() {\n    this._items.forEach(item => item.deactivateInkBar());\n    this._currentItem = undefined;\n  }\n  /** Aligns the ink bar to a DOM node. */\n  alignToElement(element) {\n    const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n    const currentItem = this._currentItem;\n    if (correspondingItem === currentItem) {\n      return;\n    }\n    currentItem === null || currentItem === void 0 || currentItem.deactivateInkBar();\n    if (correspondingItem) {\n      var _currentItem$elementR, _currentItem$elementR2;\n      const domRect = currentItem === null || currentItem === void 0 || (_currentItem$elementR = (_currentItem$elementR2 = currentItem.elementRef.nativeElement).getBoundingClientRect) === null || _currentItem$elementR === void 0 ? void 0 : _currentItem$elementR.call(_currentItem$elementR2);\n      // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n      correspondingItem.activateInkBar(domRect);\n      this._currentItem = correspondingItem;\n    }\n  }\n}\nclass InkBarItem {\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_inkBarElement\", void 0);\n    _defineProperty(this, \"_inkBarContentElement\", void 0);\n    _defineProperty(this, \"_fitToContent\", false);\n  }\n  /** Whether the ink bar should fit to the entire tab or just its content. */\n  get fitInkBarToContent() {\n    return this._fitToContent;\n  }\n  set fitInkBarToContent(newValue) {\n    if (this._fitToContent !== newValue) {\n      this._fitToContent = newValue;\n      if (this._inkBarElement) {\n        this._appendInkBarElement();\n      }\n    }\n  }\n  /** Aligns the ink bar to the current item. */\n  activateInkBar(previousIndicatorClientRect) {\n    const element = this._elementRef.nativeElement;\n    // Early exit if no indicator is present to handle cases where an indicator\n    // may be activated without a prior indicator state\n    if (!previousIndicatorClientRect || !element.getBoundingClientRect || !this._inkBarContentElement) {\n      element.classList.add(ACTIVE_CLASS);\n      return;\n    }\n    // This animation uses the FLIP approach. You can read more about it at the link below:\n    // https://aerotwist.com/blog/flip-your-animations/\n    // Calculate the dimensions based on the dimensions of the previous indicator\n    const currentClientRect = element.getBoundingClientRect();\n    const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n    const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n    element.classList.add(NO_TRANSITION_CLASS);\n    this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n    // Force repaint before updating classes and transform to ensure the transform properly takes effect\n    element.getBoundingClientRect();\n    element.classList.remove(NO_TRANSITION_CLASS);\n    element.classList.add(ACTIVE_CLASS);\n    this._inkBarContentElement.style.setProperty('transform', '');\n  }\n  /** Removes the ink bar from the current item. */\n  deactivateInkBar() {\n    this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n  }\n  /** Initializes the foundation. */\n  ngOnInit() {\n    this._createInkBarElement();\n  }\n  /** Destroys the foundation. */\n  ngOnDestroy() {\n    var _this$_inkBarElement;\n    (_this$_inkBarElement = this._inkBarElement) === null || _this$_inkBarElement === void 0 || _this$_inkBarElement.remove();\n    this._inkBarElement = this._inkBarContentElement = null;\n  }\n  /** Creates and appends the ink bar element. */\n  _createInkBarElement() {\n    const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n    const inkBarElement = this._inkBarElement = documentNode.createElement('span');\n    const inkBarContentElement = this._inkBarContentElement = documentNode.createElement('span');\n    inkBarElement.className = 'mdc-tab-indicator';\n    inkBarContentElement.className = 'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n    inkBarElement.appendChild(this._inkBarContentElement);\n    this._appendInkBarElement();\n  }\n  /**\n   * Appends the ink bar to the tab host element or content, depending on whether\n   * the ink bar should fit to content.\n   */\n  _appendInkBarElement() {\n    if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Ink bar element has not been created and cannot be appended');\n    }\n    const parentElement = this._fitToContent ? this._elementRef.nativeElement.querySelector('.mdc-tab__content') : this._elementRef.nativeElement;\n    if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Missing element to host the ink bar');\n    }\n    parentElement.appendChild(this._inkBarElement);\n  }\n}\n_InkBarItem = InkBarItem;\n_defineProperty(InkBarItem, \"\\u0275fac\", function _InkBarItem_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _InkBarItem)();\n});\n_defineProperty(InkBarItem, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _InkBarItem,\n  inputs: {\n    fitInkBarToContent: [2, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute]\n  }\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InkBarItem, [{\n    type: Directive\n  }], null, {\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n  const method = element => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0'\n  });\n  return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n  providedIn: 'root',\n  factory: _MAT_INK_BAR_POSITIONER_FACTORY\n});\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends InkBarItem {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"elementRef\", inject(ElementRef));\n    /** Whether the tab is disabled. */\n    _defineProperty(this, \"disabled\", false);\n  }\n  /** Sets focus on the wrapper element */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  getOffsetLeft() {\n    return this.elementRef.nativeElement.offsetLeft;\n  }\n  getOffsetWidth() {\n    return this.elementRef.nativeElement.offsetWidth;\n  }\n}\n_MatTabLabelWrapper = MatTabLabelWrapper;\n_defineProperty(MatTabLabelWrapper, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatTabLabelWrapper_BaseFactory;\n  return function _MatTabLabelWrapper_Factory(__ngFactoryType__) {\n    return (ɵ_MatTabLabelWrapper_BaseFactory || (ɵ_MatTabLabelWrapper_BaseFactory = i0.ɵɵgetInheritedFactory(_MatTabLabelWrapper)))(__ngFactoryType__ || _MatTabLabelWrapper);\n  };\n})());\n_defineProperty(MatTabLabelWrapper, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabLabelWrapper,\n  selectors: [[\"\", \"matTabLabelWrapper\", \"\"]],\n  hostVars: 3,\n  hostBindings: function _MatTabLabelWrapper_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-disabled\", !!ctx.disabled);\n      i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabelWrapper, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabLabelWrapper]',\n      host: {\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[attr.aria-disabled]': '!!disabled'\n      }\n    }]\n  }], null, {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = {\n  passive: true\n};\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(v) {\n    const value = isNaN(v) ? 0 : v;\n    if (this._selectedIndex != value) {\n      this._selectedIndexChanged = true;\n      this._selectedIndex = value;\n      if (this._keyManager) {\n        this._keyManager.updateActiveItem(value);\n      }\n    }\n  }\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_viewportRuler\", inject(ViewportRuler));\n    _defineProperty(this, \"_dir\", inject(Directionality, {\n      optional: true\n    }));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_sharedResizeObserver\", inject(SharedResizeObserver));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_renderer\", inject(Renderer2));\n    _defineProperty(this, \"_animationMode\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }));\n    _defineProperty(this, \"_eventCleanups\", void 0);\n    /** The distance in pixels that the tab labels should be translated to the left. */\n    _defineProperty(this, \"_scrollDistance\", 0);\n    /** Whether the header should scroll to the selected index after the view has been checked. */\n    _defineProperty(this, \"_selectedIndexChanged\", false);\n    /** Emits when the component is destroyed. */\n    _defineProperty(this, \"_destroyed\", new Subject());\n    /** Whether the controls for pagination should be displayed */\n    _defineProperty(this, \"_showPaginationControls\", false);\n    /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n    _defineProperty(this, \"_disableScrollAfter\", true);\n    /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n    _defineProperty(this, \"_disableScrollBefore\", true);\n    /**\n     * The number of tab labels that are displayed on the header. When this changes, the header\n     * should re-evaluate the scroll position.\n     */\n    _defineProperty(this, \"_tabLabelCount\", void 0);\n    /** Whether the scroll distance has changed and should be applied after the view is checked. */\n    _defineProperty(this, \"_scrollDistanceChanged\", void 0);\n    /** Used to manage focus between the tabs. */\n    _defineProperty(this, \"_keyManager\", void 0);\n    /** Cached text content of the header. */\n    _defineProperty(this, \"_currentTextContent\", void 0);\n    /** Stream that will stop the automated scrolling. */\n    _defineProperty(this, \"_stopScrolling\", new Subject());\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    _defineProperty(this, \"disablePagination\", false);\n    _defineProperty(this, \"_selectedIndex\", 0);\n    /** Event emitted when the option is selected. */\n    _defineProperty(this, \"selectFocusedIndex\", new EventEmitter());\n    /** Event emitted when a label is focused. */\n    _defineProperty(this, \"indexFocused\", new EventEmitter());\n    // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n    this._eventCleanups = this._ngZone.runOutsideAngular(() => [this._renderer.listen(this._elementRef.nativeElement, 'mouseleave', () => this._stopInterval())]);\n  }\n  ngAfterViewInit() {\n    // We need to handle these events manually, because we want to bind passive event listeners.\n    this._eventCleanups.push(_bindEventWithOptions(this._renderer, this._previousPaginator.nativeElement, 'touchstart', () => this._handlePaginatorPress('before'), passiveEventListenerOptions), _bindEventWithOptions(this._renderer, this._nextPaginator.nativeElement, 'touchstart', () => this._handlePaginatorPress('after'), passiveEventListenerOptions));\n  }\n  ngAfterContentInit() {\n    const dirChange = this._dir ? this._dir.change : of('ltr');\n    // We need to debounce resize events because the alignment logic is expensive.\n    // If someone animates the width of tabs, we don't want to realign on every animation frame.\n    // Once we haven't seen any more resize events in the last 32ms (~2 animaion frames) we can\n    // re-align.\n    const resize = this._sharedResizeObserver.observe(this._elementRef.nativeElement).pipe(debounceTime(32), takeUntil(this._destroyed));\n    // Note: We do not actually need to watch these events for proper functioning of the tabs,\n    // the resize events above should capture any viewport resize that we care about. However,\n    // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n    const viewportResize = this._viewportRuler.change(150).pipe(takeUntil(this._destroyed));\n    const realign = () => {\n      this.updatePagination();\n      this._alignInkBarToSelectedTab();\n    };\n    this._keyManager = new FocusKeyManager(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap()\n    // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n    .skipPredicate(() => false);\n    // Fall back to the first link as being active if there isn't a selected one.\n    // This is relevant primarily for the tab nav bar.\n    this._keyManager.updateActiveItem(Math.max(this._selectedIndex, 0));\n    // Note: We do not need to realign after the first render for proper functioning of the tabs\n    // the resize events above should fire when we first start observing the element. However,\n    // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n    afterNextRender(realign, {\n      injector: this._injector\n    });\n    // On dir change or resize, realign the ink bar and update the orientation of\n    // the key manager if the direction has changed.\n    merge(dirChange, viewportResize, resize, this._items.changes, this._itemsResized()).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      var _this$_keyManager;\n      // We need to defer this to give the browser some time to recalculate\n      // the element dimensions. The call has to be wrapped in `NgZone.run`,\n      // because the viewport change handler runs outside of Angular.\n      this._ngZone.run(() => {\n        Promise.resolve().then(() => {\n          // Clamp the scroll distance, because it can change with the number of tabs.\n          this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n          realign();\n        });\n      });\n      (_this$_keyManager = this._keyManager) === null || _this$_keyManager === void 0 || _this$_keyManager.withHorizontalOrientation(this._getLayoutDirection());\n    });\n    // If there is a change in the focus key manager we need to emit the `indexFocused`\n    // event in order to provide a public event that notifies about focus changes. Also we realign\n    // the tabs container by scrolling the new focused tab into the visible section.\n    this._keyManager.change.subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n      this._setTabFocus(newFocusIndex);\n    });\n  }\n  /** Sends any changes that could affect the layout of the items. */\n  _itemsResized() {\n    if (typeof ResizeObserver !== 'function') {\n      return EMPTY;\n    }\n    return this._items.changes.pipe(startWith(this._items), switchMap(tabItems => new Observable(observer => this._ngZone.runOutsideAngular(() => {\n      const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n      tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n      return () => {\n        resizeObserver.disconnect();\n      };\n    }))),\n    // Skip the first emit since the resize observer emits when an item\n    // is observed for new items when the tab is already inserted\n    skip(1),\n    // Skip emissions where all the elements are invisible since we don't want\n    // the header to try and re-render with invalid measurements. See #25574.\n    filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n  }\n  ngAfterContentChecked() {\n    // If the number of tab labels have changed, check if scrolling should be enabled\n    if (this._tabLabelCount != this._items.length) {\n      this.updatePagination();\n      this._tabLabelCount = this._items.length;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the selected index has changed, scroll to the label and check if the scrolling controls\n    // should be disabled.\n    if (this._selectedIndexChanged) {\n      this._scrollToLabel(this._selectedIndex);\n      this._checkScrollingControls();\n      this._alignInkBarToSelectedTab();\n      this._selectedIndexChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n    // then translate the header to reflect this.\n    if (this._scrollDistanceChanged) {\n      this._updateTabScrollPosition();\n      this._scrollDistanceChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngOnDestroy() {\n    var _this$_keyManager2;\n    this._eventCleanups.forEach(cleanup => cleanup());\n    (_this$_keyManager2 = this._keyManager) === null || _this$_keyManager2 === void 0 || _this$_keyManager2.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._stopScrolling.complete();\n  }\n  /** Handles keyboard events on the header. */\n  _handleKeydown(event) {\n    var _this$_keyManager3;\n    // We don't handle any key bindings with a modifier key.\n    if (hasModifierKey(event)) {\n      return;\n    }\n    switch (event.keyCode) {\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          const item = this._items.get(this.focusIndex);\n          if (item && !item.disabled) {\n            this.selectFocusedIndex.emit(this.focusIndex);\n            this._itemSelected(event);\n          }\n        }\n        break;\n      default:\n        (_this$_keyManager3 = this._keyManager) === null || _this$_keyManager3 === void 0 || _this$_keyManager3.onKeydown(event);\n    }\n  }\n  /**\n   * Callback for when the MutationObserver detects that the content has changed.\n   */\n  _onContentChanges() {\n    const textContent = this._elementRef.nativeElement.textContent;\n    // We need to diff the text content of the header, because the MutationObserver callback\n    // will fire even if the text content didn't change which is inefficient and is prone\n    // to infinite loops if a poorly constructed expression is passed in (see #14249).\n    if (textContent !== this._currentTextContent) {\n      this._currentTextContent = textContent || '';\n      // The content observer runs outside the `NgZone` by default, which\n      // means that we need to bring the callback back in ourselves.\n      this._ngZone.run(() => {\n        this.updatePagination();\n        this._alignInkBarToSelectedTab();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /**\n   * Updates the view whether pagination should be enabled or not.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    this._checkPaginationEnabled();\n    this._checkScrollingControls();\n    this._updateTabScrollPosition();\n  }\n  /** Tracks which element has focus; used for keyboard navigation */\n  get focusIndex() {\n    return this._keyManager ? this._keyManager.activeItemIndex : 0;\n  }\n  /** When the focus index is set, we must manually send focus to the correct label */\n  set focusIndex(value) {\n    if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n      return;\n    }\n    this._keyManager.setActiveItem(value);\n  }\n  /**\n   * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n   * providing a valid index and return true.\n   */\n  _isValidIndex(index) {\n    return this._items ? !!this._items.toArray()[index] : true;\n  }\n  /**\n   * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n   * scrolling is enabled.\n   */\n  _setTabFocus(tabIndex) {\n    if (this._showPaginationControls) {\n      this._scrollToLabel(tabIndex);\n    }\n    if (this._items && this._items.length) {\n      this._items.toArray()[tabIndex].focus();\n      // Do not let the browser manage scrolling to focus the element, this will be handled\n      // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n      // should be the full width minus the offset width.\n      const containerEl = this._tabListContainer.nativeElement;\n      const dir = this._getLayoutDirection();\n      if (dir == 'ltr') {\n        containerEl.scrollLeft = 0;\n      } else {\n        containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n      }\n    }\n  }\n  /** The layout direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n  _updateTabScrollPosition() {\n    if (this.disablePagination) {\n      return;\n    }\n    const scrollDistance = this.scrollDistance;\n    const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n    // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n    // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n    // and ripples will exceed the boundaries of the visible tab bar.\n    // See: https://github.com/angular/components/issues/10276\n    // We round the `transform` here, because transforms with sub-pixel precision cause some\n    // browsers to blur the content of the element.\n    this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n    // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n    // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n    // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n    // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n    if (this._platform.TRIDENT || this._platform.EDGE) {\n      this._tabListContainer.nativeElement.scrollLeft = 0;\n    }\n  }\n  /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n  get scrollDistance() {\n    return this._scrollDistance;\n  }\n  set scrollDistance(value) {\n    this._scrollTo(value);\n  }\n  /**\n   * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n   * the end of the list, respectively). The distance to scroll is computed to be a third of the\n   * length of the tab list view window.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollHeader(direction) {\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    // Move the scroll distance one-third the length of the tab list's viewport.\n    const scrollAmount = (direction == 'before' ? -1 : 1) * viewLength / 3;\n    return this._scrollTo(this._scrollDistance + scrollAmount);\n  }\n  /** Handles click events on the pagination arrows. */\n  _handlePaginatorClick(direction) {\n    this._stopInterval();\n    this._scrollHeader(direction);\n  }\n  /**\n   * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollToLabel(labelIndex) {\n    if (this.disablePagination) {\n      return;\n    }\n    const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n    if (!selectedLabel) {\n      return;\n    }\n    // The view length is the visible width of the tab labels.\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    const {\n      offsetLeft,\n      offsetWidth\n    } = selectedLabel.elementRef.nativeElement;\n    let labelBeforePos, labelAfterPos;\n    if (this._getLayoutDirection() == 'ltr') {\n      labelBeforePos = offsetLeft;\n      labelAfterPos = labelBeforePos + offsetWidth;\n    } else {\n      labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n      labelBeforePos = labelAfterPos - offsetWidth;\n    }\n    const beforeVisiblePos = this.scrollDistance;\n    const afterVisiblePos = this.scrollDistance + viewLength;\n    if (labelBeforePos < beforeVisiblePos) {\n      // Scroll header to move label to the before direction\n      this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n    } else if (labelAfterPos > afterVisiblePos) {\n      // Scroll header to move label to the after direction\n      this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n    }\n  }\n  /**\n   * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n   * tab list is wider than the size of the header container, then the pagination controls should\n   * be shown.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkPaginationEnabled() {\n    if (this.disablePagination) {\n      this._showPaginationControls = false;\n    } else {\n      const scrollWidth = this._tabListInner.nativeElement.scrollWidth;\n      const containerWidth = this._elementRef.nativeElement.offsetWidth;\n      // Usually checking that the scroll width is greater than the container width should be\n      // enough, but on Safari at specific widths the browser ends up rounding up when there's\n      // no pagination and rounding down once the pagination is added. This can throw the component\n      // into an infinite loop where the pagination shows up and disappears constantly. We work\n      // around it by adding a threshold to the calculation. From manual testing the threshold\n      // can be lowered to 2px and still resolve the issue, but we set a higher one to be safe.\n      // This shouldn't cause any content to be clipped, because tabs have a 24px horizontal\n      // padding. See b/316395154 for more information.\n      const isEnabled = scrollWidth - containerWidth >= 5;\n      if (!isEnabled) {\n        this.scrollDistance = 0;\n      }\n      if (isEnabled !== this._showPaginationControls) {\n        this._showPaginationControls = isEnabled;\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  /**\n   * Evaluate whether the before and after controls should be enabled or disabled.\n   * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n   * before button. If the header is at the end of the list (scroll distance is equal to the\n   * maximum distance we can scroll), then disable the after button.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkScrollingControls() {\n    if (this.disablePagination) {\n      this._disableScrollAfter = this._disableScrollBefore = true;\n    } else {\n      // Check if the pagination arrows should be activated.\n      this._disableScrollBefore = this.scrollDistance == 0;\n      this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n   * is equal to the difference in width between the tab list container and tab header container.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _getMaxScrollDistance() {\n    const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    return lengthOfTabList - viewLength || 0;\n  }\n  /** Tells the ink-bar to align itself to the current label wrapper */\n  _alignInkBarToSelectedTab() {\n    const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n    const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n    if (selectedLabelWrapper) {\n      this._inkBar.alignToElement(selectedLabelWrapper);\n    } else {\n      this._inkBar.hide();\n    }\n  }\n  /** Stops the currently-running paginator interval.  */\n  _stopInterval() {\n    this._stopScrolling.next();\n  }\n  /**\n   * Handles the user pressing down on one of the paginators.\n   * Starts scrolling the header after a certain amount of time.\n   * @param direction In which direction the paginator should be scrolled.\n   */\n  _handlePaginatorPress(direction, mouseEvent) {\n    // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n    // null check the `button`, but we do it so we don't break tests that use fake events.\n    if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n      return;\n    }\n    // Avoid overlapping timers.\n    this._stopInterval();\n    // Start a timer after the delay and keep firing based on the interval.\n    timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n    // Keep the timer going until something tells it to stop or the component is destroyed.\n    .pipe(takeUntil(merge(this._stopScrolling, this._destroyed))).subscribe(() => {\n      const {\n        maxScrollDistance,\n        distance\n      } = this._scrollHeader(direction);\n      // Stop the timer if we've reached the start or the end.\n      if (distance === 0 || distance >= maxScrollDistance) {\n        this._stopInterval();\n      }\n    });\n  }\n  /**\n   * Scrolls the header to a given position.\n   * @param position Position to which to scroll.\n   * @returns Information on the current scroll distance and the maximum.\n   */\n  _scrollTo(position) {\n    if (this.disablePagination) {\n      return {\n        maxScrollDistance: 0,\n        distance: 0\n      };\n    }\n    const maxScrollDistance = this._getMaxScrollDistance();\n    this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n    // Mark that the scroll distance has changed so that after the view is checked, the CSS\n    // transformation can move the header.\n    this._scrollDistanceChanged = true;\n    this._checkScrollingControls();\n    return {\n      maxScrollDistance,\n      distance: this._scrollDistance\n    };\n  }\n}\n_MatPaginatedTabHeader = MatPaginatedTabHeader;\n_defineProperty(MatPaginatedTabHeader, \"\\u0275fac\", function _MatPaginatedTabHeader_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatPaginatedTabHeader)();\n});\n_defineProperty(MatPaginatedTabHeader, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatPaginatedTabHeader,\n  inputs: {\n    disablePagination: [2, \"disablePagination\", \"disablePagination\", booleanAttribute],\n    selectedIndex: [2, \"selectedIndex\", \"selectedIndex\", numberAttribute]\n  },\n  outputs: {\n    selectFocusedIndex: \"selectFocusedIndex\",\n    indexFocused: \"indexFocused\"\n  }\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatedTabHeader, [{\n    type: Directive\n  }], () => [], {\n    disablePagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectedIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    selectFocusedIndex: [{\n      type: Output\n    }],\n    indexFocused: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends MatPaginatedTabHeader {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"_items\", void 0);\n    _defineProperty(this, \"_tabListContainer\", void 0);\n    _defineProperty(this, \"_tabList\", void 0);\n    _defineProperty(this, \"_tabListInner\", void 0);\n    _defineProperty(this, \"_nextPaginator\", void 0);\n    _defineProperty(this, \"_previousPaginator\", void 0);\n    _defineProperty(this, \"_inkBar\", void 0);\n    /** Aria label of the header. */\n    _defineProperty(this, \"ariaLabel\", void 0);\n    /** Sets the `aria-labelledby` of the header. */\n    _defineProperty(this, \"ariaLabelledby\", void 0);\n    /** Whether the ripple effect is disabled or not. */\n    _defineProperty(this, \"disableRipple\", false);\n  }\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    super.ngAfterContentInit();\n  }\n  _itemSelected(event) {\n    event.preventDefault();\n  }\n}\n_MatTabHeader = MatTabHeader;\n_defineProperty(MatTabHeader, \"\\u0275fac\", /* @__PURE__ */(() => {\n  let ɵ_MatTabHeader_BaseFactory;\n  return function _MatTabHeader_Factory(__ngFactoryType__) {\n    return (ɵ_MatTabHeader_BaseFactory || (ɵ_MatTabHeader_BaseFactory = i0.ɵɵgetInheritedFactory(_MatTabHeader)))(__ngFactoryType__ || _MatTabHeader);\n  };\n})());\n_defineProperty(MatTabHeader, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatTabHeader,\n  selectors: [[\"mat-tab-header\"]],\n  contentQueries: function _MatTabHeader_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTabLabelWrapper, 4);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n    }\n  },\n  viewQuery: function _MatTabHeader_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c1, 7);\n      i0.ɵɵviewQuery(_c2, 7);\n      i0.ɵɵviewQuery(_c3, 7);\n      i0.ɵɵviewQuery(_c4, 5);\n      i0.ɵɵviewQuery(_c5, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-tab-header\"],\n  hostVars: 4,\n  hostBindings: function _MatTabHeader_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\");\n    }\n  },\n  inputs: {\n    ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n    disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute]\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 13,\n  vars: 10,\n  consts: [[\"previousPaginator\", \"\"], [\"tabListContainer\", \"\"], [\"tabList\", \"\"], [\"tabListInner\", \"\"], [\"nextPaginator\", \"\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"click\", \"mousedown\", \"touchend\", \"matRippleDisabled\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-label-container\", 3, \"keydown\"], [\"role\", \"tablist\", 1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [1, \"mat-mdc-tab-labels\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"mousedown\", \"click\", \"touchend\", \"matRippleDisabled\"]],\n  template: function _MatTabHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 5, 0);\n      i0.ɵɵlistener(\"click\", function _MatTabHeader_Template_div_click_0_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handlePaginatorClick(\"before\"));\n      })(\"mousedown\", function _MatTabHeader_Template_div_mousedown_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handlePaginatorPress(\"before\", $event));\n      })(\"touchend\", function _MatTabHeader_Template_div_touchend_0_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._stopInterval());\n      });\n      i0.ɵɵelement(2, \"div\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"div\", 7, 1);\n      i0.ɵɵlistener(\"keydown\", function _MatTabHeader_Template_div_keydown_3_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handleKeydown($event));\n      });\n      i0.ɵɵelementStart(5, \"div\", 8, 2);\n      i0.ɵɵlistener(\"cdkObserveContent\", function _MatTabHeader_Template_div_cdkObserveContent_5_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._onContentChanges());\n      });\n      i0.ɵɵelementStart(7, \"div\", 9, 3);\n      i0.ɵɵprojection(9);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(10, \"div\", 10, 4);\n      i0.ɵɵlistener(\"mousedown\", function _MatTabHeader_Template_div_mousedown_10_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handlePaginatorPress(\"after\", $event));\n      })(\"click\", function _MatTabHeader_Template_div_click_10_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handlePaginatorClick(\"after\"));\n      })(\"touchend\", function _MatTabHeader_Template_div_touchend_10_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._stopInterval());\n      });\n      i0.ɵɵelement(12, \"div\", 6);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple);\n      i0.ɵɵadvance(3);\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby || null);\n      i0.ɵɵadvance(5);\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple);\n    }\n  },\n  dependencies: [MatRipple, CdkObserveContent],\n  styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height, 1px);border-bottom-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height, 1px);border-top-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mdc-secondary-navigation-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\"\n      },\n      imports: [MatRipple, CdkObserveContent],\n      template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\",\n      styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height, 1px);border-bottom-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height, 1px);border-top-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mdc-secondary-navigation-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\\n\"]\n    }]\n  }], null, {\n    _items: [{\n      type: ContentChildren,\n      args: [MatTabLabelWrapper, {\n        descendants: false\n      }]\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n  constructor() {\n    super();\n    _defineProperty(this, \"_host\", inject(MatTabBody));\n    /** Subscription to events for when the tab body begins centering. */\n    _defineProperty(this, \"_centeringSub\", Subscription.EMPTY);\n    /** Subscription to events for when the tab body finishes leaving from center position. */\n    _defineProperty(this, \"_leavingSub\", Subscription.EMPTY);\n  }\n  /** Set initial visibility or set up subscription for changing visibility. */\n  ngOnInit() {\n    super.ngOnInit();\n    this._centeringSub = this._host._beforeCentering.pipe(startWith(this._host._isCenterPosition())).subscribe(isCentering => {\n      if (this._host._content && isCentering && !this.hasAttached()) {\n        this.attach(this._host._content);\n      }\n    });\n    this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n      if (!this._host.preserveContent) {\n        this.detach();\n      }\n    });\n  }\n  /** Clean up centering subscription. */\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._centeringSub.unsubscribe();\n    this._leavingSub.unsubscribe();\n  }\n}\n_MatTabBodyPortal = MatTabBodyPortal;\n_defineProperty(MatTabBodyPortal, \"\\u0275fac\", function _MatTabBodyPortal_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTabBodyPortal)();\n});\n_defineProperty(MatTabBodyPortal, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabBodyPortal,\n  selectors: [[\"\", \"matTabBodyHost\", \"\"]],\n  features: [i0.ɵɵInheritDefinitionFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBodyPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabBodyHost]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody {\n  /** The shifted index position of the tab body, where zero represents the active center tab. */\n  set position(position) {\n    this._positionIndex = position;\n    this._computePositionAnimationState();\n  }\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_dir\", inject(Directionality, {\n      optional: true\n    }));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_renderer\", inject(Renderer2));\n    _defineProperty(this, \"_animationsModule\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }));\n    _defineProperty(this, \"_eventCleanups\", void 0);\n    _defineProperty(this, \"_initialized\", void 0);\n    _defineProperty(this, \"_fallbackTimer\", void 0);\n    /** Current position of the tab-body in the tab-group. Zero means that the tab is visible. */\n    _defineProperty(this, \"_positionIndex\", void 0);\n    /** Subscription to the directionality change observable. */\n    _defineProperty(this, \"_dirChangeSubscription\", Subscription.EMPTY);\n    /** Current position of the body within the tab group. */\n    _defineProperty(this, \"_position\", void 0);\n    /** Previous position of the body. */\n    _defineProperty(this, \"_previousPosition\", void 0);\n    /** Event emitted when the tab begins to animate towards the center as the active tab. */\n    _defineProperty(this, \"_onCentering\", new EventEmitter());\n    /** Event emitted before the centering of the tab begins. */\n    _defineProperty(this, \"_beforeCentering\", new EventEmitter());\n    /** Event emitted before the centering of the tab begins. */\n    _defineProperty(this, \"_afterLeavingCenter\", new EventEmitter());\n    /** Event emitted when the tab completes its animation towards the center. */\n    _defineProperty(this, \"_onCentered\", new EventEmitter(true));\n    /** The portal host inside of this container into which the tab body content will be loaded. */\n    _defineProperty(this, \"_portalHost\", void 0);\n    /** Element in which the content is rendered. */\n    _defineProperty(this, \"_contentElement\", void 0);\n    /** The tab body content to display. */\n    _defineProperty(this, \"_content\", void 0);\n    // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n    // anyway to prevent the animations module from throwing an error if the body is used on its own.\n    /** Duration for the tab's animation. */\n    _defineProperty(this, \"animationDuration\", '500ms');\n    /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n    _defineProperty(this, \"preserveContent\", false);\n    if (this._dir) {\n      const changeDetectorRef = inject(ChangeDetectorRef);\n      this._dirChangeSubscription = this._dir.change.subscribe(dir => {\n        this._computePositionAnimationState(dir);\n        changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  ngOnInit() {\n    this._bindTransitionEvents();\n    if (this._position === 'center') {\n      this._setActiveClass(true);\n      // Allows for the dynamic height to animate properly on the initial run.\n      afterNextRender(() => this._onCentering.emit(this._elementRef.nativeElement.clientHeight), {\n        injector: this._injector\n      });\n    }\n    this._initialized = true;\n  }\n  ngOnDestroy() {\n    var _this$_eventCleanups;\n    clearTimeout(this._fallbackTimer);\n    (_this$_eventCleanups = this._eventCleanups) === null || _this$_eventCleanups === void 0 || _this$_eventCleanups.forEach(cleanup => cleanup());\n    this._dirChangeSubscription.unsubscribe();\n  }\n  /** Sets up the transition events. */\n  _bindTransitionEvents() {\n    this._ngZone.runOutsideAngular(() => {\n      const element = this._elementRef.nativeElement;\n      const transitionDone = event => {\n        var _this$_contentElement;\n        if (event.target === ((_this$_contentElement = this._contentElement) === null || _this$_contentElement === void 0 ? void 0 : _this$_contentElement.nativeElement)) {\n          this._elementRef.nativeElement.classList.remove('mat-tab-body-animating');\n          // Only fire the actual callback when a transition is fully finished,\n          // otherwise the content can jump around when the next transition starts.\n          if (event.type === 'transitionend') {\n            this._transitionDone();\n          }\n        }\n      };\n      this._eventCleanups = [this._renderer.listen(element, 'transitionstart', event => {\n        var _this$_contentElement2;\n        if (event.target === ((_this$_contentElement2 = this._contentElement) === null || _this$_contentElement2 === void 0 ? void 0 : _this$_contentElement2.nativeElement)) {\n          this._elementRef.nativeElement.classList.add('mat-tab-body-animating');\n          this._transitionStarted();\n        }\n      }), this._renderer.listen(element, 'transitionend', transitionDone), this._renderer.listen(element, 'transitioncancel', transitionDone)];\n    });\n  }\n  /** Called when a transition has started. */\n  _transitionStarted() {\n    clearTimeout(this._fallbackTimer);\n    const isCentering = this._position === 'center';\n    this._beforeCentering.emit(isCentering);\n    if (isCentering) {\n      this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n    }\n  }\n  /** Called when a transition is done. */\n  _transitionDone() {\n    if (this._position === 'center') {\n      this._onCentered.emit();\n    } else if (this._previousPosition === 'center') {\n      this._afterLeavingCenter.emit();\n    }\n  }\n  /** Sets the active styling on the tab body based on its current position. */\n  _setActiveClass(isActive) {\n    this._elementRef.nativeElement.classList.toggle('mat-mdc-tab-body-active', isActive);\n  }\n  /** The text direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the provided position state is considered center, regardless of origin. */\n  _isCenterPosition() {\n    return this._positionIndex === 0;\n  }\n  /** Computes the position state that will be used for the tab-body animation trigger. */\n  _computePositionAnimationState(dir = this._getLayoutDirection()) {\n    this._previousPosition = this._position;\n    if (this._positionIndex < 0) {\n      this._position = dir == 'ltr' ? 'left' : 'right';\n    } else if (this._positionIndex > 0) {\n      this._position = dir == 'ltr' ? 'right' : 'left';\n    } else {\n      this._position = 'center';\n    }\n    if (this._animationsDisabled()) {\n      this._simulateTransitionEvents();\n    } else if (this._initialized && (this._position === 'center' || this._previousPosition === 'center')) {\n      // The transition events are load-bearing and in some cases they might not fire (e.g.\n      // tests setting `* {transition: none}` to disable animations). This timeout will simulate\n      // them if a transition doesn't start within a certain amount of time.\n      clearTimeout(this._fallbackTimer);\n      this._fallbackTimer = this._ngZone.runOutsideAngular(() => setTimeout(() => this._simulateTransitionEvents(), 100));\n    }\n  }\n  /** Simulates the body's transition events in an environment where they might not fire. */\n  _simulateTransitionEvents() {\n    this._transitionStarted();\n    afterNextRender(() => this._transitionDone(), {\n      injector: this._injector\n    });\n  }\n  /** Whether animations are disabled for the tab group. */\n  _animationsDisabled() {\n    return this._animationsModule === 'NoopAnimations' || this.animationDuration === '0ms' || this.animationDuration === '0s';\n  }\n}\n_MatTabBody = MatTabBody;\n_defineProperty(MatTabBody, \"\\u0275fac\", function _MatTabBody_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTabBody)();\n});\n_defineProperty(MatTabBody, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatTabBody,\n  selectors: [[\"mat-tab-body\"]],\n  viewQuery: function _MatTabBody_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(MatTabBodyPortal, 5);\n      i0.ɵɵviewQuery(_c6, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalHost = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentElement = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-tab-body\"],\n  hostVars: 1,\n  hostBindings: function _MatTabBody_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"inert\", ctx._position === \"center\" ? null : \"\");\n    }\n  },\n  inputs: {\n    _content: [0, \"content\", \"_content\"],\n    animationDuration: \"animationDuration\",\n    preserveContent: \"preserveContent\",\n    position: \"position\"\n  },\n  outputs: {\n    _onCentering: \"_onCentering\",\n    _beforeCentering: \"_beforeCentering\",\n    _onCentered: \"_onCentered\"\n  },\n  decls: 3,\n  vars: 6,\n  consts: [[\"content\", \"\"], [\"cdkScrollable\", \"\", 1, \"mat-mdc-tab-body-content\"], [\"matTabBodyHost\", \"\"]],\n  template: function _MatTabBody_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 1, 0);\n      i0.ɵɵtemplate(2, _MatTabBody_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-tab-body-content-left\", ctx._position === \"left\")(\"mat-tab-body-content-right\", ctx._position === \"right\")(\"mat-tab-body-content-can-animate\", ctx._position === \"center\" || ctx._previousPosition === \"center\");\n    }\n  },\n  dependencies: [MatTabBodyPortal, CdkScrollable],\n  styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBody, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-body',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-mdc-tab-body',\n        // In most cases the `visibility: hidden` that we set on the off-screen content is enough\n        // to stop interactions with it, but if a child element sets its own `visibility`, it'll\n        // override the one from the parent. This ensures that even those elements will be removed\n        // from the accessibility tree.\n        '[attr.inert]': '_position === \"center\" ? null : \"\"'\n      },\n      imports: [MatTabBodyPortal, CdkScrollable],\n      template: \"<div\\n   class=\\\"mat-mdc-tab-body-content\\\"\\n   #content\\n   cdkScrollable\\n   [class.mat-tab-body-content-left]=\\\"_position === 'left'\\\"\\n   [class.mat-tab-body-content-right]=\\\"_position === 'right'\\\"\\n   [class.mat-tab-body-content-can-animate]=\\\"_position === 'center' || _previousPosition === 'center'\\\">\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\",\n      styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\\n\"]\n    }]\n  }], () => [], {\n    _onCentering: [{\n      type: Output\n    }],\n    _beforeCentering: [{\n      type: Output\n    }],\n    _onCentered: [{\n      type: Output\n    }],\n    _portalHost: [{\n      type: ViewChild,\n      args: [MatTabBodyPortal]\n    }],\n    _contentElement: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    _content: [{\n      type: Input,\n      args: ['content']\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    preserveContent: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup {\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent;\n  }\n  set fitInkBarToContent(value) {\n    this._fitInkBarToContent = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value) {\n    this._indexToSelect = isNaN(value) ? null : value;\n  }\n  /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n  /**\n   * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n   * accessibility when the tab does not have focusable elements or if it has scrollable content.\n   * The `tabindex` will be removed automatically for inactive tabs.\n   * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n   */\n  get contentTabIndex() {\n    return this._contentTabIndex;\n  }\n  set contentTabIndex(value) {\n    this._contentTabIndex = isNaN(value) ? null : value;\n  }\n  /**\n   * Theme color of the background of the tab group. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   *\n   * @deprecated The background color should be customized through Sass theming APIs.\n   * @breaking-change 20.0.0 Remove this input\n   */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  constructor() {\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_tabsSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_tabLabelSubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_tabBodySubscription\", Subscription.EMPTY);\n    _defineProperty(this, \"_diAnimationsDisabled\", inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }) === 'NoopAnimations');\n    /**\n     * All tabs inside the tab group. This includes tabs that belong to groups that are nested\n     * inside the current one. We filter out only the tabs that belong to this group in `_tabs`.\n     */\n    _defineProperty(this, \"_allTabs\", void 0);\n    _defineProperty(this, \"_tabBodies\", void 0);\n    _defineProperty(this, \"_tabBodyWrapper\", void 0);\n    _defineProperty(this, \"_tabHeader\", void 0);\n    /** All of the tabs that belong to the group. */\n    _defineProperty(this, \"_tabs\", new QueryList());\n    /** The tab index that should be selected after the content has been checked. */\n    _defineProperty(this, \"_indexToSelect\", 0);\n    /** Index of the tab that was focused last. */\n    _defineProperty(this, \"_lastFocusedTabIndex\", null);\n    /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n    _defineProperty(this, \"_tabBodyWrapperHeight\", 0);\n    /**\n     * Theme color of the tab group. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    _defineProperty(this, \"color\", void 0);\n    _defineProperty(this, \"_fitInkBarToContent\", false);\n    /** Whether tabs should be stretched to fill the header. */\n    _defineProperty(this, \"stretchTabs\", true);\n    /** Alignment for tabs label. */\n    _defineProperty(this, \"alignTabs\", null);\n    /** Whether the tab group should grow to the size of the active tab. */\n    _defineProperty(this, \"dynamicHeight\", false);\n    _defineProperty(this, \"_selectedIndex\", null);\n    /** Position of the tab header. */\n    _defineProperty(this, \"headerPosition\", 'above');\n    _defineProperty(this, \"_animationDuration\", void 0);\n    _defineProperty(this, \"_contentTabIndex\", void 0);\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    _defineProperty(this, \"disablePagination\", false);\n    /** Whether ripples in the tab group are disabled. */\n    _defineProperty(this, \"disableRipple\", false);\n    /**\n     * By default tabs remove their content from the DOM while it's off-screen.\n     * Setting this to `true` will keep it in the DOM which will prevent elements\n     * like iframes and videos from reloading next time it comes back into the view.\n     */\n    _defineProperty(this, \"preserveContent\", false);\n    _defineProperty(this, \"_backgroundColor\", void 0);\n    /** Aria label of the inner `tablist` of the group. */\n    _defineProperty(this, \"ariaLabel\", void 0);\n    /** Sets the `aria-labelledby` of the inner `tablist` of the group. */\n    _defineProperty(this, \"ariaLabelledby\", void 0);\n    /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n    _defineProperty(this, \"selectedIndexChange\", new EventEmitter());\n    /** Event emitted when focus has changed within a tab group. */\n    _defineProperty(this, \"focusChange\", new EventEmitter());\n    /** Event emitted when the body animation has completed */\n    _defineProperty(this, \"animationDone\", new EventEmitter());\n    /** Event emitted when the tab selection has changed. */\n    _defineProperty(this, \"selectedTabChange\", new EventEmitter(true));\n    _defineProperty(this, \"_groupId\", void 0);\n    /** Whether the tab group is rendered on the server. */\n    _defineProperty(this, \"_isServer\", !inject(Platform).isBrowser);\n    const defaultConfig = inject(MAT_TABS_CONFIG, {\n      optional: true\n    });\n    this._groupId = inject(_IdGenerator).getId('mat-tab-group-');\n    this.animationDuration = defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.dynamicHeight = defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n    if ((defaultConfig === null || defaultConfig === void 0 ? void 0 : defaultConfig.contentTabIndex) != null) {\n      this.contentTabIndex = defaultConfig.contentTabIndex;\n    }\n    this.preserveContent = !!(defaultConfig !== null && defaultConfig !== void 0 && defaultConfig.preserveContent);\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n    this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    this.alignTabs = defaultConfig && defaultConfig.alignTabs != null ? defaultConfig.alignTabs : null;\n  }\n  /**\n   * After the content is checked, this component knows what tabs have been defined\n   * and what the selected index should be. This is where we can know exactly what position\n   * each tab should be in according to the new selected index, and additionally we know how\n   * a new selected tab should transition in (from the left or right).\n   */\n  ngAfterContentChecked() {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = this._indexToSelect = this._clampTabIndex(this._indexToSelect);\n    // If there is a change in selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n    if (this._selectedIndex != indexToSelect) {\n      const isFirstRun = this._selectedIndex == null;\n      if (!isFirstRun) {\n        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n        // Preserve the height so page doesn't scroll up during tab change.\n        // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.minHeight = wrapper.clientHeight + 'px';\n      }\n      // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n      Promise.resolve().then(() => {\n        this._tabs.forEach((tab, index) => tab.isActive = index === indexToSelect);\n        if (!isFirstRun) {\n          this.selectedIndexChange.emit(indexToSelect);\n          // Clear the min-height, this was needed during tab change to avoid\n          // unnecessary scrolling.\n          this._tabBodyWrapper.nativeElement.style.minHeight = '';\n        }\n      });\n    }\n    // Setup the position for each tab and optionally setup an origin on the next selected tab.\n    this._tabs.forEach((tab, index) => {\n      tab.position = index - indexToSelect;\n      // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n      if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n        tab.origin = indexToSelect - this._selectedIndex;\n      }\n    });\n    if (this._selectedIndex !== indexToSelect) {\n      this._selectedIndex = indexToSelect;\n      this._lastFocusedTabIndex = null;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngAfterContentInit() {\n    this._subscribeToAllTabChanges();\n    this._subscribeToTabLabels();\n    // Subscribe to changes in the amount of tabs, in order to be\n    // able to re-render the content as new tabs are added or removed.\n    this._tabsSubscription = this._tabs.changes.subscribe(() => {\n      const indexToSelect = this._clampTabIndex(this._indexToSelect);\n      // Maintain the previously-selected tab if a new tab is added or removed and there is no\n      // explicit change that selects a different tab.\n      if (indexToSelect === this._selectedIndex) {\n        const tabs = this._tabs.toArray();\n        let selectedTab;\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `selectedIndexChange` event.\n            this._indexToSelect = this._selectedIndex = i;\n            this._lastFocusedTabIndex = null;\n            selectedTab = tabs[i];\n            break;\n          }\n        }\n        // If we haven't found an active tab and a tab exists at the selected index, it means\n        // that the active tab was swapped out. Since this won't be picked up by the rendering\n        // loop in `ngAfterContentChecked`, we need to sync it up manually.\n        if (!selectedTab && tabs[indexToSelect]) {\n          Promise.resolve().then(() => {\n            tabs[indexToSelect].isActive = true;\n            this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n          });\n        }\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  ngAfterViewInit() {\n    this._tabBodySubscription = this._tabBodies.changes.subscribe(() => this._bodyCentered(true));\n  }\n  /** Listens to changes in all of the tabs. */\n  _subscribeToAllTabChanges() {\n    // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n    // some that are inside of nested tab groups. We filter them out manually by checking that\n    // the closest group to the tab is the current one.\n    this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe(tabs => {\n      this._tabs.reset(tabs.filter(tab => {\n        return tab._closestTabGroup === this || !tab._closestTabGroup;\n      }));\n      this._tabs.notifyOnChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._tabs.destroy();\n    this._tabsSubscription.unsubscribe();\n    this._tabLabelSubscription.unsubscribe();\n    this._tabBodySubscription.unsubscribe();\n  }\n  /** Re-aligns the ink bar to the selected tab element. */\n  realignInkBar() {\n    if (this._tabHeader) {\n      this._tabHeader._alignInkBarToSelectedTab();\n    }\n  }\n  /**\n   * Recalculates the tab group's pagination dimensions.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    if (this._tabHeader) {\n      this._tabHeader.updatePagination();\n    }\n  }\n  /**\n   * Sets focus to a particular tab.\n   * @param index Index of the tab to be focused.\n   */\n  focusTab(index) {\n    const header = this._tabHeader;\n    if (header) {\n      header.focusIndex = index;\n    }\n  }\n  _focusChanged(index) {\n    this._lastFocusedTabIndex = index;\n    this.focusChange.emit(this._createChangeEvent(index));\n  }\n  _createChangeEvent(index) {\n    const event = new MatTabChangeEvent();\n    event.index = index;\n    if (this._tabs && this._tabs.length) {\n      event.tab = this._tabs.toArray()[index];\n    }\n    return event;\n  }\n  /**\n   * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n   * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n   * binding to be updated, we need to subscribe to changes in it and trigger change detection\n   * manually.\n   */\n  _subscribeToTabLabels() {\n    if (this._tabLabelSubscription) {\n      this._tabLabelSubscription.unsubscribe();\n    }\n    this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Clamps the given index to the bounds of 0 and the tabs length. */\n  _clampTabIndex(index) {\n    // Note the `|| 0`, which ensures that values like NaN can't get through\n    // and which would otherwise throw the component into an infinite loop\n    // (since Math.max(NaN, 0) === NaN).\n    return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n  }\n  /** Returns a unique id for each tab label element */\n  _getTabLabelId(tab, index) {\n    return tab.id || `${this._groupId}-label-${index}`;\n  }\n  /** Returns a unique id for each tab content element */\n  _getTabContentId(index) {\n    return `${this._groupId}-content-${index}`;\n  }\n  /**\n   * Sets the height of the body wrapper to the height of the activating tab if dynamic\n   * height property is true.\n   */\n  _setTabBodyWrapperHeight(tabHeight) {\n    if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n      this._tabBodyWrapperHeight = tabHeight;\n      return;\n    }\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n    // This conditional forces the browser to paint the height so that\n    // the animation to the new height can have an origin.\n    if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n      wrapper.style.height = tabHeight + 'px';\n    }\n  }\n  /** Removes the height of the tab body wrapper. */\n  _removeTabBodyWrapperHeight() {\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    this._tabBodyWrapperHeight = wrapper.clientHeight;\n    wrapper.style.height = '';\n    this._ngZone.run(() => this.animationDone.emit());\n  }\n  /** Handle click events, setting new selected index if appropriate. */\n  _handleClick(tab, tabHeader, index) {\n    tabHeader.focusIndex = index;\n    if (!tab.disabled) {\n      this.selectedIndex = index;\n    }\n  }\n  /** Retrieves the tabindex for the tab. */\n  _getTabIndex(index) {\n    var _this$_lastFocusedTab;\n    const targetIndex = (_this$_lastFocusedTab = this._lastFocusedTabIndex) !== null && _this$_lastFocusedTab !== void 0 ? _this$_lastFocusedTab : this.selectedIndex;\n    return index === targetIndex ? 0 : -1;\n  }\n  /** Callback for when the focused state of a tab has changed. */\n  _tabFocusChanged(focusOrigin, index) {\n    // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n    // can cause the tab to be moved out from under the pointer, interrupting the\n    // click sequence (see #21898). We don't need to scroll the tab into view for\n    // such cases anyway, because it will be done when the tab becomes selected.\n    if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n      this._tabHeader.focusIndex = index;\n    }\n  }\n  /**\n   * Callback invoked when the centered state of a tab body changes.\n   * @param isCenter Whether the tab will be in the center.\n   */\n  _bodyCentered(isCenter) {\n    // Marks all the existing tabs as inactive and the center tab as active. Note that this can\n    // be achieved much easier by using a class binding on each body. The problem with\n    // doing so is that we can't control the timing of when the class is removed from the\n    // previously-active element and added to the newly-active one. If there's a tick between\n    // removing the class and adding the new one, the content will jump in a very jarring way.\n    // We go through the trouble of setting the classes ourselves to guarantee that they're\n    // swapped out at the same time.\n    if (isCenter) {\n      var _this$_tabBodies;\n      (_this$_tabBodies = this._tabBodies) === null || _this$_tabBodies === void 0 || _this$_tabBodies.forEach((body, i) => body._setActiveClass(i === this._selectedIndex));\n    }\n  }\n  _animationsDisabled() {\n    return this._diAnimationsDisabled || this.animationDuration === '0' || this.animationDuration === '0ms';\n  }\n}\n_MatTabGroup = MatTabGroup;\n_defineProperty(MatTabGroup, \"\\u0275fac\", function _MatTabGroup_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTabGroup)();\n});\n_defineProperty(MatTabGroup, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatTabGroup,\n  selectors: [[\"mat-tab-group\"]],\n  contentQueries: function _MatTabGroup_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTab, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTabs = _t);\n    }\n  },\n  viewQuery: function _MatTabGroup_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c7, 5);\n      i0.ɵɵviewQuery(_c8, 5);\n      i0.ɵɵviewQuery(MatTabBody, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodyWrapper = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabHeader = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodies = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-tab-group\"],\n  hostVars: 11,\n  hostBindings: function _MatTabGroup_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"mat-align-tabs\", ctx.alignTabs);\n      i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n      i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n      i0.ɵɵclassProp(\"mat-mdc-tab-group-dynamic-height\", ctx.dynamicHeight)(\"mat-mdc-tab-group-inverted-header\", ctx.headerPosition === \"below\")(\"mat-mdc-tab-group-stretch-tabs\", ctx.stretchTabs);\n    }\n  },\n  inputs: {\n    color: \"color\",\n    fitInkBarToContent: [2, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute],\n    stretchTabs: [2, \"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute],\n    alignTabs: [0, \"mat-align-tabs\", \"alignTabs\"],\n    dynamicHeight: [2, \"dynamicHeight\", \"dynamicHeight\", booleanAttribute],\n    selectedIndex: [2, \"selectedIndex\", \"selectedIndex\", numberAttribute],\n    headerPosition: \"headerPosition\",\n    animationDuration: \"animationDuration\",\n    contentTabIndex: [2, \"contentTabIndex\", \"contentTabIndex\", numberAttribute],\n    disablePagination: [2, \"disablePagination\", \"disablePagination\", booleanAttribute],\n    disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n    preserveContent: [2, \"preserveContent\", \"preserveContent\", booleanAttribute],\n    backgroundColor: \"backgroundColor\",\n    ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"]\n  },\n  outputs: {\n    selectedIndexChange: \"selectedIndexChange\",\n    focusChange: \"focusChange\",\n    animationDone: \"animationDone\",\n    selectedTabChange: \"selectedTabChange\"\n  },\n  exportAs: [\"matTabGroup\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB_GROUP,\n    useExisting: _MatTabGroup\n  }])],\n  ngContentSelectors: _c0,\n  decls: 9,\n  vars: 8,\n  consts: [[\"tabHeader\", \"\"], [\"tabBodyWrapper\", \"\"], [\"tabNode\", \"\"], [3, \"indexFocused\", \"selectFocusedIndex\", \"selectedIndex\", \"disableRipple\", \"disablePagination\", \"aria-label\", \"aria-labelledby\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-focus-indicator\", 3, \"id\", \"mdc-tab--active\", \"class\", \"disabled\", \"fitInkBarToContent\"], [1, \"mat-mdc-tab-body-wrapper\"], [\"role\", \"tabpanel\", 3, \"id\", \"class\", \"content\", \"position\", \"animationDuration\", \"preserveContent\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-focus-indicator\", 3, \"click\", \"cdkFocusChange\", \"id\", \"disabled\", \"fitInkBarToContent\"], [1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"], [3, \"cdkPortalOutlet\"], [\"role\", \"tabpanel\", 3, \"_onCentered\", \"_onCentering\", \"_beforeCentering\", \"id\", \"content\", \"position\", \"animationDuration\", \"preserveContent\"]],\n  template: function _MatTabGroup_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"mat-tab-header\", 3, 0);\n      i0.ɵɵlistener(\"indexFocused\", function _MatTabGroup_Template_mat_tab_header_indexFocused_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._focusChanged($event));\n      })(\"selectFocusedIndex\", function _MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.selectedIndex = $event);\n      });\n      i0.ɵɵrepeaterCreate(2, _MatTabGroup_For_3_Template, 8, 17, \"div\", 4, i0.ɵɵrepeaterTrackByIdentity);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, _MatTabGroup_Conditional_4_Template, 1, 0);\n      i0.ɵɵelementStart(5, \"div\", 5, 1);\n      i0.ɵɵrepeaterCreate(7, _MatTabGroup_For_8_Template, 1, 10, \"mat-tab-body\", 6, i0.ɵɵrepeaterTrackByIdentity);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"selectedIndex\", ctx.selectedIndex || 0)(\"disableRipple\", ctx.disableRipple)(\"disablePagination\", ctx.disablePagination)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby);\n      i0.ɵɵadvance(2);\n      i0.ɵɵrepeater(ctx._tabs);\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(ctx._isServer ? 4 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationsDisabled());\n      i0.ɵɵadvance(2);\n      i0.ɵɵrepeater(ctx._tabs);\n    }\n  },\n  dependencies: [MatTabHeader, MatTabLabelWrapper, CdkMonitorFocus, MatRipple, CdkPortalOutlet, MatTabBody],\n  styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mdc-secondary-navigation-tab-container-height, 48px);font-family:var(--mat-tab-header-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-header-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-header-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-header-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-header-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mdc-tab-indicator-active-indicator-height, 2px);border-radius:var(--mdc-tab-indicator-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-group',\n      exportAs: 'matTabGroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: MAT_TAB_GROUP,\n        useExisting: MatTabGroup\n      }],\n      host: {\n        'class': 'mat-mdc-tab-group',\n        '[class]': '\"mat-\" + (color || \"primary\")',\n        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n        '[attr.mat-align-tabs]': 'alignTabs',\n        '[style.--mat-tab-animation-duration]': 'animationDuration'\n      },\n      imports: [MatTabHeader, MatTabLabelWrapper, CdkMonitorFocus, MatRipple, CdkPortalOutlet, MatTabBody],\n      template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                [aria-label]=\\\"ariaLabel\\\"\\n                [aria-labelledby]=\\\"ariaLabelledby\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(tab, $index)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex($index)\\\"\\n        [attr.aria-posinset]=\\\"$index + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId($index)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === $index\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === $index\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, $index)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, $index)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled()\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab;) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId($index)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === $index) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(tab, $index)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== $index\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\"\\n                 (_beforeCentering)=\\\"_bodyCentered($event)\\\"/>\\n  }\\n</div>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mdc-secondary-navigation-tab-container-height, 48px);font-family:var(--mat-tab-header-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-header-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-header-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-header-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-header-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mdc-tab-indicator-active-indicator-height, 2px);border-radius:var(--mdc-tab-indicator-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\\n\"]\n    }]\n  }], () => [], {\n    _allTabs: [{\n      type: ContentChildren,\n      args: [MatTab, {\n        descendants: true\n      }]\n    }],\n    _tabBodies: [{\n      type: ViewChildren,\n      args: [MatTabBody]\n    }],\n    _tabBodyWrapper: [{\n      type: ViewChild,\n      args: ['tabBodyWrapper']\n    }],\n    _tabHeader: [{\n      type: ViewChild,\n      args: ['tabHeader']\n    }],\n    color: [{\n      type: Input\n    }],\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-stretch-tabs',\n        transform: booleanAttribute\n      }]\n    }],\n    alignTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-align-tabs'\n      }]\n    }],\n    dynamicHeight: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectedIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    headerPosition: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    contentTabIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    disablePagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    preserveContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    selectedIndexChange: [{\n      type: Output\n    }],\n    focusChange: [{\n      type: Output\n    }],\n    animationDone: [{\n      type: Output\n    }],\n    selectedTabChange: [{\n      type: Output\n    }]\n  });\n})();\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {\n  constructor() {\n    /** Index of the currently-selected tab. */\n    _defineProperty(this, \"index\", void 0);\n    /** Reference to the currently-selected tab. */\n    _defineProperty(this, \"tab\", void 0);\n  }\n}\n\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends MatPaginatedTabHeader {\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent.value;\n  }\n  set fitInkBarToContent(value) {\n    this._fitInkBarToContent.next(value);\n    this._changeDetectorRef.markForCheck();\n  }\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n  /**\n   * Theme color of the background of the tab nav. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  constructor() {\n    const elementRef = inject(ElementRef);\n    const dir = inject(Directionality, {\n      optional: true\n    });\n    const ngZone = inject(NgZone);\n    const changeDetectorRef = inject(ChangeDetectorRef);\n    const viewportRuler = inject(ViewportRuler);\n    const platform = inject(Platform);\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    const defaultConfig = inject(MAT_TABS_CONFIG, {\n      optional: true\n    });\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    _defineProperty(this, \"_focusedItem\", signal(null));\n    _defineProperty(this, \"_fitInkBarToContent\", new BehaviorSubject(false));\n    /** Whether tabs should be stretched to fill the header. */\n    _defineProperty(this, \"stretchTabs\", true);\n    _defineProperty(this, \"_animationDuration\", void 0);\n    /** Query list of all tab links of the tab navigation. */\n    _defineProperty(this, \"_items\", void 0);\n    _defineProperty(this, \"_backgroundColor\", void 0);\n    /** Whether the ripple effect is disabled or not. */\n    _defineProperty(this, \"disableRipple\", false);\n    /**\n     * Theme color of the nav bar. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    _defineProperty(this, \"color\", 'primary');\n    /**\n     * Associated tab panel controlled by the nav bar. If not provided, then the nav bar\n     * follows the ARIA link / navigation landmark pattern. If provided, it follows the\n     * ARIA tabs design pattern.\n     */\n    _defineProperty(this, \"tabPanel\", void 0);\n    _defineProperty(this, \"_tabListContainer\", void 0);\n    _defineProperty(this, \"_tabList\", void 0);\n    _defineProperty(this, \"_tabListInner\", void 0);\n    _defineProperty(this, \"_nextPaginator\", void 0);\n    _defineProperty(this, \"_previousPaginator\", void 0);\n    _defineProperty(this, \"_inkBar\", void 0);\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n    this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n  }\n  _itemSelected() {\n    // noop\n  }\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    // We need this to run before the `changes` subscription in parent to ensure that the\n    // selectedIndex is up-to-date by the time the super class starts looking for it.\n    this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => this.updateActiveLink());\n    super.ngAfterContentInit();\n    // Turn the `change` stream into a signal to try and avoid \"changed after checked\" errors.\n    this._keyManager.change.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      var _this$_keyManager4;\n      return this._focusedItem.set(((_this$_keyManager4 = this._keyManager) === null || _this$_keyManager4 === void 0 ? void 0 : _this$_keyManager4.activeItem) || null);\n    });\n  }\n  ngAfterViewInit() {\n    if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n    }\n    super.ngAfterViewInit();\n  }\n  /** Notifies the component that the active link has been changed. */\n  updateActiveLink() {\n    if (!this._items) {\n      return;\n    }\n    const items = this._items.toArray();\n    for (let i = 0; i < items.length; i++) {\n      if (items[i].active) {\n        this.selectedIndex = i;\n        if (this.tabPanel) {\n          this.tabPanel._activeTabId = items[i].id;\n        }\n        // Updating the `selectedIndex` won't trigger the `change` event on\n        // the key manager so we need to set the signal from here.\n        this._focusedItem.set(items[i]);\n        this._changeDetectorRef.markForCheck();\n        return;\n      }\n    }\n    this.selectedIndex = -1;\n  }\n  _getRole() {\n    return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n  }\n  _hasFocus(link) {\n    var _this$_keyManager5;\n    return ((_this$_keyManager5 = this._keyManager) === null || _this$_keyManager5 === void 0 ? void 0 : _this$_keyManager5.activeItem) === link;\n  }\n}\n_MatTabNav = MatTabNav;\n_defineProperty(MatTabNav, \"\\u0275fac\", function _MatTabNav_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTabNav)();\n});\n_defineProperty(MatTabNav, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatTabNav,\n  selectors: [[\"\", \"mat-tab-nav-bar\", \"\"]],\n  contentQueries: function _MatTabNav_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTabLink, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n    }\n  },\n  viewQuery: function _MatTabNav_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c1, 7);\n      i0.ɵɵviewQuery(_c2, 7);\n      i0.ɵɵviewQuery(_c3, 7);\n      i0.ɵɵviewQuery(_c4, 5);\n      i0.ɵɵviewQuery(_c5, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-mdc-tab-nav-bar\", \"mat-mdc-tab-header\"],\n  hostVars: 17,\n  hostBindings: function _MatTabNav_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"role\", ctx._getRole());\n      i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\")(\"mat-mdc-tab-nav-bar-stretch-tabs\", ctx.stretchTabs)(\"mat-primary\", ctx.color !== \"warn\" && ctx.color !== \"accent\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n    }\n  },\n  inputs: {\n    fitInkBarToContent: [2, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute],\n    stretchTabs: [2, \"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute],\n    animationDuration: \"animationDuration\",\n    backgroundColor: \"backgroundColor\",\n    disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n    color: \"color\",\n    tabPanel: \"tabPanel\"\n  },\n  exportAs: [\"matTabNavBar\", \"matTabNav\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c9,\n  ngContentSelectors: _c0,\n  decls: 13,\n  vars: 6,\n  consts: [[\"previousPaginator\", \"\"], [\"tabListContainer\", \"\"], [\"tabList\", \"\"], [\"tabListInner\", \"\"], [\"nextPaginator\", \"\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"click\", \"mousedown\", \"touchend\", \"matRippleDisabled\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-link-container\", 3, \"keydown\"], [1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [1, \"mat-mdc-tab-links\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"mousedown\", \"click\", \"touchend\", \"matRippleDisabled\"]],\n  template: function _MatTabNav_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 5, 0);\n      i0.ɵɵlistener(\"click\", function _MatTabNav_Template_div_click_0_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handlePaginatorClick(\"before\"));\n      })(\"mousedown\", function _MatTabNav_Template_div_mousedown_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handlePaginatorPress(\"before\", $event));\n      })(\"touchend\", function _MatTabNav_Template_div_touchend_0_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._stopInterval());\n      });\n      i0.ɵɵelement(2, \"div\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"div\", 7, 1);\n      i0.ɵɵlistener(\"keydown\", function _MatTabNav_Template_div_keydown_3_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handleKeydown($event));\n      });\n      i0.ɵɵelementStart(5, \"div\", 8, 2);\n      i0.ɵɵlistener(\"cdkObserveContent\", function _MatTabNav_Template_div_cdkObserveContent_5_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._onContentChanges());\n      });\n      i0.ɵɵelementStart(7, \"div\", 9, 3);\n      i0.ɵɵprojection(9);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(10, \"div\", 10, 4);\n      i0.ɵɵlistener(\"mousedown\", function _MatTabNav_Template_div_mousedown_10_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handlePaginatorPress(\"after\", $event));\n      })(\"click\", function _MatTabNav_Template_div_click_10_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._handlePaginatorClick(\"after\"));\n      })(\"touchend\", function _MatTabNav_Template_div_touchend_10_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx._stopInterval());\n      });\n      i0.ɵɵelement(12, \"div\", 6);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple);\n      i0.ɵɵadvance(10);\n      i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple);\n    }\n  },\n  dependencies: [MatRipple, CdkObserveContent],\n  styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-links,.mat-mdc-tab-links.cdk-drop-list{min-height:var(--mdc-secondary-navigation-tab-container-height, 48px)}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height, 1px);border-bottom-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNav, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-nav-bar]',\n      exportAs: 'matTabNavBar, matTabNav',\n      host: {\n        '[attr.role]': '_getRole()',\n        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        '[style.--mat-tab-animation-duration]': 'animationDuration'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [MatRipple, CdkObserveContent],\n      template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-links,.mat-mdc-tab-links.cdk-drop-list{min-height:var(--mdc-secondary-navigation-tab-container-height, 48px)}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height, 1px);border-bottom-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\\n\"]\n    }]\n  }], () => [], {\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-stretch-tabs',\n        transform: booleanAttribute\n      }]\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    _items: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatTabLink), {\n        descendants: true\n      }]\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    tabPanel: [{\n      type: Input\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }]\n  });\n})();\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends InkBarItem {\n  /** Whether the link is active. */\n  get active() {\n    return this._isActive;\n  }\n  set active(value) {\n    if (value !== this._isActive) {\n      this._isActive = value;\n      this._tabNavBar.updateActiveLink();\n    }\n  }\n  /** Whether the tab link is disabled. */\n\n  /**\n   * Whether ripples are disabled on interaction.\n   * @docs-private\n   */\n  get rippleDisabled() {\n    return this.disabled || this.disableRipple || this._tabNavBar.disableRipple || !!this.rippleConfig.disabled;\n  }\n  /** Unique id for the tab. */\n\n  constructor() {\n    super();\n    _defineProperty(this, \"_tabNavBar\", inject(MatTabNav));\n    _defineProperty(this, \"elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_focusMonitor\", inject(FocusMonitor));\n    _defineProperty(this, \"_destroyed\", new Subject());\n    /** Whether the tab link is active or not. */\n    _defineProperty(this, \"_isActive\", false);\n    _defineProperty(this, \"_tabIndex\", computed(() => this._tabNavBar._focusedItem() === this ? this.tabIndex : -1));\n    _defineProperty(this, \"disabled\", false);\n    /** Whether ripples are disabled on the tab link. */\n    _defineProperty(this, \"disableRipple\", false);\n    _defineProperty(this, \"tabIndex\", 0);\n    /**\n     * Ripple configuration for ripples that are launched on pointer down. The ripple config\n     * is set to the global ripple options since we don't have any configurable options for\n     * the tab link ripples.\n     * @docs-private\n     */\n    _defineProperty(this, \"rippleConfig\", void 0);\n    _defineProperty(this, \"id\", inject(_IdGenerator).getId('mat-tab-link-'));\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this.rippleConfig = globalRippleOptions || {};\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    if (animationMode === 'NoopAnimations') {\n      this.rippleConfig.animation = {\n        enterDuration: 0,\n        exitDuration: 0\n      };\n    }\n    this._tabNavBar._fitInkBarToContent.pipe(takeUntil(this._destroyed)).subscribe(fitInkBarToContent => {\n      this.fitInkBarToContent = fitInkBarToContent;\n    });\n  }\n  /** Focuses the tab link. */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this.elementRef);\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    super.ngOnDestroy();\n    this._focusMonitor.stopMonitoring(this.elementRef);\n  }\n  _handleFocus() {\n    // Since we allow navigation through tabbing in the nav bar, we\n    // have to update the focused index whenever the link receives focus.\n    this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n  }\n  _handleKeydown(event) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this.disabled) {\n        event.preventDefault();\n      } else if (this._tabNavBar.tabPanel) {\n        // Only prevent the default action on space since it can scroll the page.\n        // Don't prevent enter since it can break link navigation.\n        if (event.keyCode === SPACE) {\n          event.preventDefault();\n        }\n        this.elementRef.nativeElement.click();\n      }\n    }\n  }\n  _getAriaControls() {\n    var _this$_tabNavBar$tabP;\n    return this._tabNavBar.tabPanel ? (_this$_tabNavBar$tabP = this._tabNavBar.tabPanel) === null || _this$_tabNavBar$tabP === void 0 ? void 0 : _this$_tabNavBar$tabP.id : this.elementRef.nativeElement.getAttribute('aria-controls');\n  }\n  _getAriaSelected() {\n    if (this._tabNavBar.tabPanel) {\n      return this.active ? 'true' : 'false';\n    } else {\n      return this.elementRef.nativeElement.getAttribute('aria-selected');\n    }\n  }\n  _getAriaCurrent() {\n    return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n  }\n  _getRole() {\n    return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n  }\n}\n_MatTabLink = MatTabLink;\n_defineProperty(MatTabLink, \"\\u0275fac\", function _MatTabLink_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTabLink)();\n});\n_defineProperty(MatTabLink, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatTabLink,\n  selectors: [[\"\", \"mat-tab-link\", \"\"], [\"\", \"matTabLink\", \"\"]],\n  hostAttrs: [1, \"mdc-tab\", \"mat-mdc-tab-link\", \"mat-focus-indicator\"],\n  hostVars: 11,\n  hostBindings: function _MatTabLink_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focus\", function _MatTabLink_focus_HostBindingHandler() {\n        return ctx._handleFocus();\n      })(\"keydown\", function _MatTabLink_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-controls\", ctx._getAriaControls())(\"aria-current\", ctx._getAriaCurrent())(\"aria-disabled\", ctx.disabled)(\"aria-selected\", ctx._getAriaSelected())(\"id\", ctx.id)(\"tabIndex\", ctx._tabIndex())(\"role\", ctx._getRole());\n      i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled)(\"mdc-tab--active\", ctx.active);\n    }\n  },\n  inputs: {\n    active: [2, \"active\", \"active\", booleanAttribute],\n    disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n    disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n    tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n    id: \"id\"\n  },\n  exportAs: [\"matTabLink\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c10,\n  ngContentSelectors: _c0,\n  decls: 5,\n  vars: 2,\n  consts: [[1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"]],\n  template: function _MatTabLink_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelement(0, \"span\", 0)(1, \"div\", 1);\n      i0.ɵɵelementStart(2, \"span\", 2)(3, \"span\", 3);\n      i0.ɵɵprojection(4);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"matRippleTrigger\", ctx.elementRef.nativeElement)(\"matRippleDisabled\", ctx.rippleDisabled);\n    }\n  },\n  dependencies: [MatRipple],\n  styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mdc-secondary-navigation-tab-container-height, 48px);font-family:var(--mat-tab-header-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-header-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-header-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-header-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-header-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mdc-tab-indicator-active-indicator-height, 2px);border-radius:var(--mdc-tab-indicator-active-indicator-shape, 0)}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLink, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-link], [matTabLink]',\n      exportAs: 'matTabLink',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'mdc-tab mat-mdc-tab-link mat-focus-indicator',\n        '[attr.aria-controls]': '_getAriaControls()',\n        '[attr.aria-current]': '_getAriaCurrent()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.aria-selected]': '_getAriaSelected()',\n        '[attr.id]': 'id',\n        '[attr.tabIndex]': '_tabIndex()',\n        '[attr.role]': '_getRole()',\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[class.mdc-tab--active]': 'active',\n        '(focus)': '_handleFocus()',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      imports: [MatRipple],\n      template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\",\n      styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mdc-secondary-navigation-tab-container-height, 48px);font-family:var(--mat-tab-header-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-header-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-header-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-header-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-header-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mdc-tab-indicator-active-indicator-height, 2px);border-radius:var(--mdc-tab-indicator-active-indicator-shape, 0)}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\\n\"]\n    }]\n  }], () => [], {\n    active: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n  constructor() {\n    /** Unique id for the tab panel. */\n    _defineProperty(this, \"id\", inject(_IdGenerator).getId('mat-tab-nav-panel-'));\n    /** Id of the active tab in the nav bar. */\n    _defineProperty(this, \"_activeTabId\", void 0);\n  }\n}\n_MatTabNavPanel = MatTabNavPanel;\n_defineProperty(MatTabNavPanel, \"\\u0275fac\", function _MatTabNavPanel_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTabNavPanel)();\n});\n_defineProperty(MatTabNavPanel, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _MatTabNavPanel,\n  selectors: [[\"mat-tab-nav-panel\"]],\n  hostAttrs: [\"role\", \"tabpanel\", 1, \"mat-mdc-tab-nav-panel\"],\n  hostVars: 2,\n  hostBindings: function _MatTabNavPanel_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-labelledby\", ctx._activeTabId)(\"id\", ctx.id);\n    }\n  },\n  inputs: {\n    id: \"id\"\n  },\n  exportAs: [\"matTabNavPanel\"],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function _MatTabNavPanel_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNavPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-nav-panel',\n      exportAs: 'matTabNavPanel',\n      template: '<ng-content></ng-content>',\n      host: {\n        '[attr.aria-labelledby]': '_activeTabId',\n        '[attr.id]': 'id',\n        'class': 'mat-mdc-tab-nav-panel',\n        'role': 'tabpanel'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\nclass MatTabsModule {}\n_MatTabsModule = MatTabsModule;\n_defineProperty(MatTabsModule, \"\\u0275fac\", function _MatTabsModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MatTabsModule)();\n});\n_defineProperty(MatTabsModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _MatTabsModule,\n  imports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink],\n  exports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink]\n}));\n_defineProperty(MatTabsModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink],\n      exports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0.\n */\nconst matTabsAnimations = {\n  // Represents:\n  // trigger('translateTab', [\n  //   // Transitions to `none` instead of 0, because some browsers might blur the content.\n  //   state(\n  //     'center, void, left-origin-center, right-origin-center',\n  //     style({transform: 'none', visibility: 'visible'}),\n  //   ),\n  //   // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n  //   // in order to ensure that the element has a height before its state changes. This is\n  //   // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n  //   // not have a static height and is not rendered. See related issue: #9465\n  //   state(\n  //     'left',\n  //     style({\n  //       transform: 'translate3d(-100%, 0, 0)',\n  //       minHeight: '1px',\n  //       // Normally this is redundant since we detach the content from the DOM, but if the user\n  //       // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n  //       visibility: 'hidden',\n  //     }),\n  //   ),\n  //   state(\n  //     'right',\n  //     style({\n  //       transform: 'translate3d(100%, 0, 0)',\n  //       minHeight: '1px',\n  //       visibility: 'hidden',\n  //     }),\n  //   ),\n  //   transition(\n  //     '* => left, * => right, left => center, right => center',\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ),\n  //   transition('void => left-origin-center', [\n  //     style({transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'}),\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ]),\n  //   transition('void => right-origin-center', [\n  //     style({transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'}),\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ]),\n  // ])\n  /** Animation translates a tab along the X axis. */\n  translateTab: {\n    type: 7,\n    name: 'translateTab',\n    definitions: [{\n      type: 0,\n      name: 'center, void, left-origin-center, right-origin-center',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'none',\n          visibility: 'visible'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'left',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translate3d(-100%, 0, 0)',\n          minHeight: '1px',\n          visibility: 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'right',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translate3d(100%, 0, 0)',\n          minHeight: '1px',\n          visibility: 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => left, * => right, left => center, right => center',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: 'void => left-origin-center',\n      animation: [{\n        type: 6,\n        styles: {\n          transform: 'translate3d(-100%, 0, 0)',\n          visibility: 'hidden'\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'\n      }],\n      options: null\n    }, {\n      type: 1,\n      expr: 'void => right-origin-center',\n      animation: [{\n        type: 6,\n        styles: {\n          transform: 'translate3d(100%, 0, 0)',\n          visibility: 'hidden'\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, matTabsAnimations };\n//# sourceMappingURL=tabs.mjs.map", "map": {"version": 3, "names": ["i0", "ɵɵprojection", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "_MatTabBody_ng_template_2_Template", "rf", "ctx", "_c7", "_c8", "_MatTabGroup_For_3_Conditional_6_ng_template_0_Template", "_MatTabGroup_For_3_Conditional_6_Template", "ɵɵtemplate", "tab_r4", "ɵɵnextContext", "$implicit", "ɵɵproperty", "templateLabel", "_MatTabGroup_For_3_Conditional_7_Template", "ɵɵtext", "ɵɵtextInterpolate", "textLabel", "_MatTabGroup_For_3_Template", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "_MatTabGroup_For_3_Template_div_click_0_listener", "ctx_r2", "ɵɵrestoreView", "$index_r5", "$index", "ctx_r5", "tabHeader_r7", "ɵɵreference", "ɵɵresetView", "_handleClick", "_MatTabGroup_For_3_Template_div_cdkFocusChange_0_listener", "$event", "_tabFocusChanged", "ɵɵelement", "ɵɵelementEnd", "tabNode_r8", "ɵɵclassMap", "labelClass", "ɵɵclassProp", "selectedIndex", "_getTabLabelId", "disabled", "fitInkBarToContent", "ɵɵattribute", "_getTabIndex", "_tabs", "length", "_getTabContentId", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵadvance", "disable<PERSON><PERSON><PERSON>", "ɵɵconditional", "_MatTabGroup_Conditional_4_Template", "_MatTabGroup_For_8_Template", "_r9", "_MatTabGroup_For_8_Template_mat_tab_body__onCentered_0_listener", "_removeTabBodyWrapperHeight", "_MatTabGroup_For_8_Template_mat_tab_body__onCentering_0_listener", "_setTabBodyWrapperHeight", "_MatTabGroup_For_8_Template_mat_tab_body__beforeCentering_0_listener", "_bodyCentered", "tab_r10", "$index_r11", "bodyClass", "content", "position", "animationDuration", "preserve<PERSON><PERSON>nt", "contentTabIndex", "_c9", "_c10", "FocusKeyManager", "_IdGenerator", "CdkMonitorFocus", "FocusMonitor", "Directionality", "hasModifierKey", "SPACE", "ENTER", "SharedResizeObserver", "Platform", "_bindEventWithOptions", "ViewportRuler", "CdkScrollable", "InjectionToken", "inject", "TemplateRef", "Directive", "ViewContainerRef", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ContentChild", "ViewChild", "ElementRef", "ChangeDetectorRef", "NgZone", "Injector", "Renderer2", "ANIMATION_MODULE_TYPE", "EventEmitter", "afterNextRender", "numberAttribute", "Output", "ContentChildren", "QueryList", "ViewChildren", "signal", "forwardRef", "computed", "HostAttributeToken", "NgModule", "Subject", "of", "merge", "EMPTY", "Observable", "timer", "Subscription", "BehaviorSubject", "debounceTime", "takeUntil", "startWith", "switchMap", "skip", "filter", "CdkPortal", "TemplatePortal", "CdkPortalOutlet", "_CdkPrivateStyleLoader", "_", "_StructuralStylesLoader", "CdkObserveContent", "M", "<PERSON><PERSON><PERSON><PERSON>", "a", "MAT_RIPPLE_GLOBAL_OPTIONS", "MatCommonModule", "MAT_TAB_CONTENT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_defineProperty", "_Mat<PERSON><PERSON><PERSON><PERSON><PERSON>", "_MatTabContent_Factory", "__ngFactoryType__", "ɵɵdefineDirective", "type", "selectors", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "MAT_TAB_LABEL", "MAT_TAB", "MatTab<PERSON><PERSON><PERSON>", "optional", "_Mat<PERSON>ab<PERSON><PERSON><PERSON>", "ɵ_MatTabLabel_BaseFactory", "_MatTabLabel_Factory", "ɵɵgetInheritedFactory", "ɵɵInheritDefinitionFeature", "MAT_TAB_GROUP", "Mat<PERSON><PERSON>", "_templateLabel", "value", "_setTemplateLabelInput", "_contentPortal", "undefined", "load", "ngOnChanges", "changes", "hasOwnProperty", "_stateChanges", "next", "ngOnDestroy", "complete", "ngOnInit", "_explicitContent", "_implicitContent", "_viewContainerRef", "_closestTab", "_MatTab", "_MatTab_Factory", "ɵɵdefineComponent", "contentQueries", "_MatTab_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "_MatTab_Query", "ɵɵviewQuery", "hostAttrs", "hostVars", "hostBindings", "_MatTab_HostBindings", "inputs", "id", "exportAs", "ɵɵNgOnChangesFeature", "ngContentSelectors", "_c0", "decls", "vars", "template", "_MatTab_Template", "ɵɵprojectionDef", "_MatTab_ng_template_0_Template", "encapsulation", "changeDetection", "<PERSON><PERSON><PERSON>", "None", "host", "transform", "read", "static", "ACTIVE_CLASS", "NO_TRANSITION_CLASS", "MatInkBar", "_items", "hide", "for<PERSON>ach", "item", "deactivateInkBar", "_currentItem", "alignToElement", "element", "correspondingItem", "find", "elementRef", "nativeElement", "currentItem", "_currentItem$elementR", "_currentItem$elementR2", "domRect", "getBoundingClientRect", "call", "activateInkBar", "InkBarItem", "_fitTo<PERSON>ontent", "newValue", "_inkBarElement", "_appendInkBarElement", "previousIndicatorClientRect", "_elementRef", "_inkBarContentElement", "classList", "add", "currentClientRect", "<PERSON><PERSON><PERSON><PERSON>", "width", "xPosition", "left", "style", "setProperty", "remove", "_createInkBarElement", "_this$_inkBarElement", "documentNode", "ownerDocument", "document", "inkBarElement", "createElement", "inkBarContentElement", "className", "append<PERSON><PERSON><PERSON>", "Error", "parentElement", "querySelector", "_InkBarItem", "_InkBarItem_Factory", "_MAT_INK_BAR_POSITIONER_FACTORY", "method", "offsetLeft", "offsetWidth", "_MAT_INK_BAR_POSITIONER", "providedIn", "factory", "MatTabLabelWrapper", "focus", "getOffsetLeft", "getOffsetWidth", "_MatTabLabelWrapper", "ɵ_MatTabLabelWrapper_BaseFactory", "_MatTabLabelWrapper_Factory", "_MatTabLabelWrapper_HostBindings", "passiveEventListenerOptions", "passive", "HEADER_SCROLL_DELAY", "HEADER_SCROLL_INTERVAL", "MatPaginatedTabHeader", "_selectedIndex", "v", "isNaN", "_selectedIndexChanged", "_keyManager", "updateActiveItem", "_eventCleanups", "_ngZone", "runOutsideAngular", "_renderer", "listen", "_stopInterval", "ngAfterViewInit", "push", "_previousPaginator", "_handlePaginatorPress", "_nextPaginator", "ngAfterContentInit", "<PERSON><PERSON><PERSON><PERSON>", "_dir", "change", "resize", "_sharedResizeObserver", "observe", "pipe", "_destroyed", "viewportResize", "_viewportRuler", "realign", "updatePagination", "_alignInkBarToSelectedTab", "withHorizontalOrientation", "_getLayoutDirection", "withHomeAndEnd", "withWrap", "skipPredicate", "Math", "max", "injector", "_injector", "_itemsResized", "subscribe", "_this$_keyManager", "run", "Promise", "resolve", "then", "_scrollDistance", "min", "_getMaxScrollDistance", "newFocusIndex", "indexFocused", "emit", "_setTabFocus", "ResizeObserver", "tabItems", "observer", "resizeObserver", "entries", "disconnect", "some", "e", "contentRect", "height", "ngAfterContentChecked", "_tabLabelCount", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_scrollToLabel", "_checkScrollingControls", "_scrollDistanceChanged", "_updateTabScrollPosition", "_this$_keyManager2", "cleanup", "destroy", "_stopScrolling", "_handleKeydown", "event", "_this$_keyManager3", "keyCode", "focusIndex", "get", "selectFocusedIndex", "_itemSelected", "onKeydown", "_onContentChanges", "textContent", "_currentTextContent", "_checkPaginationEnabled", "activeItemIndex", "_isValidIndex", "setActiveItem", "index", "toArray", "tabIndex", "_showPaginationControls", "containerEl", "_tabListContainer", "dir", "scrollLeft", "scrollWidth", "disablePagination", "scrollDistance", "translateX", "_tabList", "round", "_platform", "TRIDENT", "EDGE", "_scrollTo", "_scrollHeader", "direction", "viewLength", "scrollAmount", "_handlePaginatorClick", "labelIndex", "<PERSON><PERSON><PERSON><PERSON>", "labelBeforePos", "labelAfterPos", "_tabListInner", "beforeVisiblePos", "afterVisiblePos", "containerWidth", "isEnabled", "_disableScrollAfter", "_disableScrollBefore", "lengthOfTabList", "selectedItem", "<PERSON><PERSON><PERSON><PERSON>W<PERSON><PERSON>", "_inkBar", "mouseEvent", "button", "maxScrollDistance", "distance", "_MatPaginatedTabHeader", "_MatPaginatedTabHeader_Factory", "outputs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preventDefault", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵ_MatTabHeader_BaseFactory", "_MatTabHeader_Factory", "_MatTabHeader_ContentQueries", "_MatTabHeader_Query", "_MatTabHeader_HostBindings", "consts", "_MatTabHeader_Template", "_r1", "_MatTabHeader_Template_div_click_0_listener", "_MatTabHeader_Template_div_mousedown_0_listener", "_MatTabHeader_Template_div_touchend_0_listener", "_MatTabHeader_Template_div_keydown_3_listener", "_MatTabHeader_Template_div_cdkObserveContent_5_listener", "_MatTabHeader_Template_div_mousedown_10_listener", "_MatTabHeader_Template_div_click_10_listener", "_MatTabHeader_Template_div_touchend_10_listener", "_animationMode", "dependencies", "styles", "imports", "descendants", "MAT_TABS_CONFIG", "MatTabBodyPortal", "MatTabBody", "_centeringSub", "_host", "_beforeCentering", "_isCenterPosition", "isCentering", "_content", "has<PERSON>tta<PERSON>", "attach", "_leavingSub", "_afterLeavingCenter", "detach", "unsubscribe", "_MatTabBodyPortal", "_MatTabBodyPortal_Factory", "_positionIndex", "_computePositionAnimationState", "changeDetectorRef", "_dirChangeSubscription", "_bindTransitionEvents", "_position", "_setActiveClass", "_onCentering", "clientHeight", "_initialized", "_this$_eventCleanups", "clearTimeout", "_fallbackTimer", "transitionDone", "_this$_contentElement", "target", "_contentElement", "_transitionDone", "_this$_contentElement2", "_transitionStarted", "_onCentered", "_previousPosition", "isActive", "toggle", "_animationsDisabled", "_simulateTransitionEvents", "setTimeout", "_animationsModule", "_MatTabBody", "_MatTabBody_Factory", "_MatTabBody_Query", "_portalHost", "_MatTabBody_HostBindings", "_MatTabBody_Template", "MatTabGroup", "_fitInkBarToContent", "_indexToSelect", "_animationDuration", "stringValue", "test", "_contentTabIndex", "backgroundColor", "_backgroundColor", "<PERSON><PERSON><PERSON><PERSON>", "defaultConfig", "_groupId", "getId", "dynamicHeight", "stretchTabs", "alignTabs", "indexToSelect", "_clampTabIndex", "isFirstRun", "selectedTabChange", "_createChangeEvent", "wrapper", "_tabBodyWrapper", "minHeight", "tab", "selectedIndexChange", "origin", "_lastFocusedTabIndex", "_subscribeToAllTabChanges", "_subscribeToTabLabels", "_tabsSubscription", "tabs", "selectedTab", "i", "_tabBodySubscription", "_tabBodies", "_allTabs", "reset", "_closestTabGroup", "notifyOn<PERSON><PERSON>es", "_tabLabelSubscription", "realignInkBar", "_tabHeader", "focusTab", "header", "_focusChanged", "focusChange", "MatTabChangeEvent", "map", "tabHeight", "_tabBodyWrapperHeight", "offsetHeight", "animationDone", "tabHeader", "_this$_lastFocusedTab", "targetIndex", "<PERSON><PERSON><PERSON><PERSON>", "isCenter", "_this$_tabBodies", "body", "_diAnimationsDisabled", "_MatTabGroup", "_MatTabGroup_Factory", "_MatTabGroup_ContentQueries", "_MatTabGroup_Query", "_MatTabGroup_HostBindings", "color", "ɵɵstyleProp", "headerPosition", "_MatTabGroup_Template", "_MatTabGroup_Template_mat_tab_header_indexFocused_0_listener", "_MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener", "ɵɵrepeaterCreate", "ɵɵrepeaterTrackByIdentity", "ɵɵrepeater", "_isServer", "alias", "MatTabNav", "ngZone", "viewportRuler", "platform", "animationMode", "updateActiveLink", "_this$_keyManager4", "_focusedItem", "set", "activeItem", "tabPanel", "items", "active", "_activeTabId", "_getRole", "getAttribute", "_hasFocus", "link", "_this$_keyManager5", "_MatTabNav", "_MatTabNav_Factory", "_MatTabNav_ContentQueries", "MatTabLink", "_MatTabNav_Query", "_MatTabNav_HostBindings", "attrs", "_MatTabNav_Template", "_MatTabNav_Template_div_click_0_listener", "_MatTabNav_Template_div_mousedown_0_listener", "_MatTabNav_Template_div_touchend_0_listener", "_MatTabNav_Template_div_keydown_3_listener", "_MatTabNav_Template_div_cdkObserveContent_5_listener", "_MatTabNav_Template_div_mousedown_10_listener", "_MatTabNav_Template_div_click_10_listener", "_MatTabNav_Template_div_touchend_10_listener", "_isActive", "_tabNavBar", "rippleDisabled", "rippleConfig", "globalRippleOptions", "parseInt", "animation", "enterDuration", "exitDuration", "_focusMonitor", "monitor", "stopMonitoring", "_handleFocus", "indexOf", "click", "_getAriaControls", "_this$_tabNavBar$tabP", "_getAriaSelected", "_getAriaCurrent", "_MatTabLink", "_MatTabLink_Factory", "_MatTabLink_HostBindings", "_MatTabLink_focus_HostBindingHandler", "_MatTabLink_keydown_HostBindingHandler", "_tabIndex", "_MatTabLink_Template", "OnPush", "MatTabNavPanel", "_MatTabNavPanel", "_MatTabNavPanel_Factory", "_MatTabNavPanel_HostBindings", "_MatTabNavPanel_Template", "MatTabsModule", "_MatTabsModule", "_MatTabsModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "matTabsAnimations", "translateTab", "name", "definitions", "visibility", "offset", "expr", "timings", "options"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/material/fesm2022/tabs.mjs"], "sourcesContent": ["import { FocusKeyManager, _IdGenerator, CdkMonitorFocus, FocusMonitor } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { Platform, _bindEventWithOptions } from '@angular/cdk/platform';\nimport { ViewportRuler, CdkScrollable } from '@angular/cdk/scrolling';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChild, ViewChild, ElementRef, ChangeDetectorRef, NgZone, Injector, Renderer2, ANIMATION_MODULE_TYPE, EventEmitter, afterNextRender, numberAttribute, Output, ContentChildren, QueryList, ViewChildren, signal, forwardRef, computed, HostAttributeToken, NgModule } from '@angular/core';\nimport { Subject, of, merge, EMPTY, Observable, timer, Subscription, BehaviorSubject } from 'rxjs';\nimport { debounceTime, takeUntil, startWith, switchMap, skip, filter } from 'rxjs/operators';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet } from '@angular/cdk/portal';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { CdkObserveContent } from '@angular/cdk/observers';\nimport { M as MatRipple, a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BT3tzh6F.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/coercion';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n    template = inject(TemplateRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTabContent, isStandalone: true, selector: \"[matTabContent]\", providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabContent]',\n                    providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }],\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n    _closestTab = inject(MAT_TAB, { optional: true });\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabLabel, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTabLabel, isStandalone: true, selector: \"[mat-tab-label], [matTabLabel]\", providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-tab-label], [matTabLabel]',\n                    providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }],\n                }]\n        }] });\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\nclass MatTab {\n    _viewContainerRef = inject(ViewContainerRef);\n    _closestTabGroup = inject(MAT_TAB_GROUP, { optional: true });\n    /** whether the tab is disabled. */\n    disabled = false;\n    /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n    get templateLabel() {\n        return this._templateLabel;\n    }\n    set templateLabel(value) {\n        this._setTemplateLabelInput(value);\n    }\n    _templateLabel;\n    /**\n     * Template provided in the tab content that will be used if present, used to enable lazy-loading\n     */\n    _explicitContent = undefined;\n    /** Template inside the MatTab view that contains an `<ng-content>`. */\n    _implicitContent;\n    /** Plain text label for the tab, used when there is no template label. */\n    textLabel = '';\n    /** Aria label for the tab. */\n    ariaLabel;\n    /**\n     * Reference to the element that the tab is labelled by.\n     * Will be cleared if `aria-label` is set at the same time.\n     */\n    ariaLabelledby;\n    /** Classes to be passed to the tab label inside the mat-tab-header container. */\n    labelClass;\n    /** Classes to be passed to the tab mat-tab-body container. */\n    bodyClass;\n    /**\n     * Custom ID for the tab, overriding the auto-generated one by Material.\n     * Note that when using this input, it's your responsibility to ensure that the ID is unique.\n     */\n    id = null;\n    /** Portal that will be the hosted content of the tab */\n    _contentPortal = null;\n    /** @docs-private */\n    get content() {\n        return this._contentPortal;\n    }\n    /** Emits whenever the internal state of the tab changes. */\n    _stateChanges = new Subject();\n    /**\n     * The relatively indexed position where 0 represents the center, negative is left, and positive\n     * represents the right.\n     */\n    position = null;\n    // TODO(crisbeto): we no longer use this, but some internal apps appear to rely on it.\n    /**\n     * The initial relatively index origin of the tab if it was created and selected after there\n     * was already a selected tab. Provides context of what position the tab should originate from.\n     */\n    origin = null;\n    /**\n     * Whether the tab is currently active.\n     */\n    isActive = false;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    }\n    ngOnChanges(changes) {\n        if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n            this._stateChanges.next();\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    ngOnInit() {\n        this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setTemplateLabelInput(value) {\n        // Only update the label if the query managed to find one. This works around an issue where a\n        // user may have manually set `templateLabel` during creation mode, which would then get\n        // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n        // tab matches the current one so that we don't pick up labels from nested tabs.\n        if (value && value._closestTab === this) {\n            this._templateLabel = value;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTab, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatTab, isStandalone: true, selector: \"mat-tab\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute], textLabel: [\"label\", \"textLabel\"], ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], labelClass: \"labelClass\", bodyClass: \"bodyClass\", id: \"id\" }, host: { attributes: { \"hidden\": \"\" }, properties: { \"attr.id\": \"null\" } }, providers: [{ provide: MAT_TAB, useExisting: MatTab }], queries: [{ propertyName: \"templateLabel\", first: true, predicate: MatTabLabel, descendants: true }, { propertyName: \"_explicitContent\", first: true, predicate: MatTabContent, descendants: true, read: TemplateRef, static: true }], viewQueries: [{ propertyName: \"_implicitContent\", first: true, predicate: TemplateRef, descendants: true, static: true }], exportAs: [\"matTab\"], usesOnChanges: true, ngImport: i0, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\", changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, exportAs: 'matTab', providers: [{ provide: MAT_TAB, useExisting: MatTab }], host: {\n                        // This element will be rendered on the server in order to support hydration.\n                        // Hide it so it doesn't cause a layout shift when it's removed on the client.\n                        'hidden': '',\n                        // Clear any custom IDs from the tab since they'll be forwarded to the actual tab.\n                        '[attr.id]': 'null',\n                    }, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], templateLabel: [{\n                type: ContentChild,\n                args: [MatTabLabel]\n            }], _explicitContent: [{\n                type: ContentChild,\n                args: [MatTabContent, { read: TemplateRef, static: true }]\n            }], _implicitContent: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], textLabel: [{\n                type: Input,\n                args: ['label']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], labelClass: [{\n                type: Input\n            }], bodyClass: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }] } });\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n    _items;\n    /** Item to which the ink bar is aligned currently. */\n    _currentItem;\n    constructor(_items) {\n        this._items = _items;\n    }\n    /** Hides the ink bar. */\n    hide() {\n        this._items.forEach(item => item.deactivateInkBar());\n        this._currentItem = undefined;\n    }\n    /** Aligns the ink bar to a DOM node. */\n    alignToElement(element) {\n        const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n        const currentItem = this._currentItem;\n        if (correspondingItem === currentItem) {\n            return;\n        }\n        currentItem?.deactivateInkBar();\n        if (correspondingItem) {\n            const domRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n            // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n            correspondingItem.activateInkBar(domRect);\n            this._currentItem = correspondingItem;\n        }\n    }\n}\nclass InkBarItem {\n    _elementRef = inject(ElementRef);\n    _inkBarElement;\n    _inkBarContentElement;\n    _fitToContent = false;\n    /** Whether the ink bar should fit to the entire tab or just its content. */\n    get fitInkBarToContent() {\n        return this._fitToContent;\n    }\n    set fitInkBarToContent(newValue) {\n        if (this._fitToContent !== newValue) {\n            this._fitToContent = newValue;\n            if (this._inkBarElement) {\n                this._appendInkBarElement();\n            }\n        }\n    }\n    /** Aligns the ink bar to the current item. */\n    activateInkBar(previousIndicatorClientRect) {\n        const element = this._elementRef.nativeElement;\n        // Early exit if no indicator is present to handle cases where an indicator\n        // may be activated without a prior indicator state\n        if (!previousIndicatorClientRect ||\n            !element.getBoundingClientRect ||\n            !this._inkBarContentElement) {\n            element.classList.add(ACTIVE_CLASS);\n            return;\n        }\n        // This animation uses the FLIP approach. You can read more about it at the link below:\n        // https://aerotwist.com/blog/flip-your-animations/\n        // Calculate the dimensions based on the dimensions of the previous indicator\n        const currentClientRect = element.getBoundingClientRect();\n        const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n        const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n        element.classList.add(NO_TRANSITION_CLASS);\n        this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n        // Force repaint before updating classes and transform to ensure the transform properly takes effect\n        element.getBoundingClientRect();\n        element.classList.remove(NO_TRANSITION_CLASS);\n        element.classList.add(ACTIVE_CLASS);\n        this._inkBarContentElement.style.setProperty('transform', '');\n    }\n    /** Removes the ink bar from the current item. */\n    deactivateInkBar() {\n        this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n    }\n    /** Initializes the foundation. */\n    ngOnInit() {\n        this._createInkBarElement();\n    }\n    /** Destroys the foundation. */\n    ngOnDestroy() {\n        this._inkBarElement?.remove();\n        this._inkBarElement = this._inkBarContentElement = null;\n    }\n    /** Creates and appends the ink bar element. */\n    _createInkBarElement() {\n        const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n        const inkBarElement = (this._inkBarElement = documentNode.createElement('span'));\n        const inkBarContentElement = (this._inkBarContentElement = documentNode.createElement('span'));\n        inkBarElement.className = 'mdc-tab-indicator';\n        inkBarContentElement.className =\n            'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n        inkBarElement.appendChild(this._inkBarContentElement);\n        this._appendInkBarElement();\n    }\n    /**\n     * Appends the ink bar to the tab host element or content, depending on whether\n     * the ink bar should fit to content.\n     */\n    _appendInkBarElement() {\n        if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Ink bar element has not been created and cannot be appended');\n        }\n        const parentElement = this._fitToContent\n            ? this._elementRef.nativeElement.querySelector('.mdc-tab__content')\n            : this._elementRef.nativeElement;\n        if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Missing element to host the ink bar');\n        }\n        parentElement.appendChild(this._inkBarElement);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InkBarItem, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: InkBarItem, isStandalone: true, inputs: { fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute] }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InkBarItem, decorators: [{\n            type: Directive\n        }], propDecorators: { fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n    const method = (element) => ({\n        left: element ? (element.offsetLeft || 0) + 'px' : '0',\n        width: element ? (element.offsetWidth || 0) + 'px' : '0',\n    });\n    return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n    providedIn: 'root',\n    factory: _MAT_INK_BAR_POSITIONER_FACTORY,\n});\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends InkBarItem {\n    elementRef = inject(ElementRef);\n    /** Whether the tab is disabled. */\n    disabled = false;\n    /** Sets focus on the wrapper element */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    getOffsetLeft() {\n        return this.elementRef.nativeElement.offsetLeft;\n    }\n    getOffsetWidth() {\n        return this.elementRef.nativeElement.offsetWidth;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabLabelWrapper, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatTabLabelWrapper, isStandalone: true, selector: \"[matTabLabelWrapper]\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute] }, host: { properties: { \"class.mat-mdc-tab-disabled\": \"disabled\", \"attr.aria-disabled\": \"!!disabled\" } }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabLabelWrapper, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabLabelWrapper]',\n                    host: {\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[attr.aria-disabled]': '!!disabled',\n                    },\n                }]\n        }], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = {\n    passive: true,\n};\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _viewportRuler = inject(ViewportRuler);\n    _dir = inject(Directionality, { optional: true });\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _sharedResizeObserver = inject(SharedResizeObserver);\n    _injector = inject(Injector);\n    _renderer = inject(Renderer2);\n    _animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n    _eventCleanups;\n    /** The distance in pixels that the tab labels should be translated to the left. */\n    _scrollDistance = 0;\n    /** Whether the header should scroll to the selected index after the view has been checked. */\n    _selectedIndexChanged = false;\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Whether the controls for pagination should be displayed */\n    _showPaginationControls = false;\n    /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n    _disableScrollAfter = true;\n    /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n    _disableScrollBefore = true;\n    /**\n     * The number of tab labels that are displayed on the header. When this changes, the header\n     * should re-evaluate the scroll position.\n     */\n    _tabLabelCount;\n    /** Whether the scroll distance has changed and should be applied after the view is checked. */\n    _scrollDistanceChanged;\n    /** Used to manage focus between the tabs. */\n    _keyManager;\n    /** Cached text content of the header. */\n    _currentTextContent;\n    /** Stream that will stop the automated scrolling. */\n    _stopScrolling = new Subject();\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    disablePagination = false;\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(v) {\n        const value = isNaN(v) ? 0 : v;\n        if (this._selectedIndex != value) {\n            this._selectedIndexChanged = true;\n            this._selectedIndex = value;\n            if (this._keyManager) {\n                this._keyManager.updateActiveItem(value);\n            }\n        }\n    }\n    _selectedIndex = 0;\n    /** Event emitted when the option is selected. */\n    selectFocusedIndex = new EventEmitter();\n    /** Event emitted when a label is focused. */\n    indexFocused = new EventEmitter();\n    constructor() {\n        // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n        this._eventCleanups = this._ngZone.runOutsideAngular(() => [\n            this._renderer.listen(this._elementRef.nativeElement, 'mouseleave', () => this._stopInterval()),\n        ]);\n    }\n    ngAfterViewInit() {\n        // We need to handle these events manually, because we want to bind passive event listeners.\n        this._eventCleanups.push(_bindEventWithOptions(this._renderer, this._previousPaginator.nativeElement, 'touchstart', () => this._handlePaginatorPress('before'), passiveEventListenerOptions), _bindEventWithOptions(this._renderer, this._nextPaginator.nativeElement, 'touchstart', () => this._handlePaginatorPress('after'), passiveEventListenerOptions));\n    }\n    ngAfterContentInit() {\n        const dirChange = this._dir ? this._dir.change : of('ltr');\n        // We need to debounce resize events because the alignment logic is expensive.\n        // If someone animates the width of tabs, we don't want to realign on every animation frame.\n        // Once we haven't seen any more resize events in the last 32ms (~2 animaion frames) we can\n        // re-align.\n        const resize = this._sharedResizeObserver\n            .observe(this._elementRef.nativeElement)\n            .pipe(debounceTime(32), takeUntil(this._destroyed));\n        // Note: We do not actually need to watch these events for proper functioning of the tabs,\n        // the resize events above should capture any viewport resize that we care about. However,\n        // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n        const viewportResize = this._viewportRuler.change(150).pipe(takeUntil(this._destroyed));\n        const realign = () => {\n            this.updatePagination();\n            this._alignInkBarToSelectedTab();\n        };\n        this._keyManager = new FocusKeyManager(this._items)\n            .withHorizontalOrientation(this._getLayoutDirection())\n            .withHomeAndEnd()\n            .withWrap()\n            // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n            .skipPredicate(() => false);\n        // Fall back to the first link as being active if there isn't a selected one.\n        // This is relevant primarily for the tab nav bar.\n        this._keyManager.updateActiveItem(Math.max(this._selectedIndex, 0));\n        // Note: We do not need to realign after the first render for proper functioning of the tabs\n        // the resize events above should fire when we first start observing the element. However,\n        // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n        afterNextRender(realign, { injector: this._injector });\n        // On dir change or resize, realign the ink bar and update the orientation of\n        // the key manager if the direction has changed.\n        merge(dirChange, viewportResize, resize, this._items.changes, this._itemsResized())\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            // We need to defer this to give the browser some time to recalculate\n            // the element dimensions. The call has to be wrapped in `NgZone.run`,\n            // because the viewport change handler runs outside of Angular.\n            this._ngZone.run(() => {\n                Promise.resolve().then(() => {\n                    // Clamp the scroll distance, because it can change with the number of tabs.\n                    this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n                    realign();\n                });\n            });\n            this._keyManager?.withHorizontalOrientation(this._getLayoutDirection());\n        });\n        // If there is a change in the focus key manager we need to emit the `indexFocused`\n        // event in order to provide a public event that notifies about focus changes. Also we realign\n        // the tabs container by scrolling the new focused tab into the visible section.\n        this._keyManager.change.subscribe(newFocusIndex => {\n            this.indexFocused.emit(newFocusIndex);\n            this._setTabFocus(newFocusIndex);\n        });\n    }\n    /** Sends any changes that could affect the layout of the items. */\n    _itemsResized() {\n        if (typeof ResizeObserver !== 'function') {\n            return EMPTY;\n        }\n        return this._items.changes.pipe(startWith(this._items), switchMap((tabItems) => new Observable((observer) => this._ngZone.runOutsideAngular(() => {\n            const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n            tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n            return () => {\n                resizeObserver.disconnect();\n            };\n        }))), \n        // Skip the first emit since the resize observer emits when an item\n        // is observed for new items when the tab is already inserted\n        skip(1), \n        // Skip emissions where all the elements are invisible since we don't want\n        // the header to try and re-render with invalid measurements. See #25574.\n        filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n    }\n    ngAfterContentChecked() {\n        // If the number of tab labels have changed, check if scrolling should be enabled\n        if (this._tabLabelCount != this._items.length) {\n            this.updatePagination();\n            this._tabLabelCount = this._items.length;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the selected index has changed, scroll to the label and check if the scrolling controls\n        // should be disabled.\n        if (this._selectedIndexChanged) {\n            this._scrollToLabel(this._selectedIndex);\n            this._checkScrollingControls();\n            this._alignInkBarToSelectedTab();\n            this._selectedIndexChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n        // then translate the header to reflect this.\n        if (this._scrollDistanceChanged) {\n            this._updateTabScrollPosition();\n            this._scrollDistanceChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngOnDestroy() {\n        this._eventCleanups.forEach(cleanup => cleanup());\n        this._keyManager?.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._stopScrolling.complete();\n    }\n    /** Handles keyboard events on the header. */\n    _handleKeydown(event) {\n        // We don't handle any key bindings with a modifier key.\n        if (hasModifierKey(event)) {\n            return;\n        }\n        switch (event.keyCode) {\n            case ENTER:\n            case SPACE:\n                if (this.focusIndex !== this.selectedIndex) {\n                    const item = this._items.get(this.focusIndex);\n                    if (item && !item.disabled) {\n                        this.selectFocusedIndex.emit(this.focusIndex);\n                        this._itemSelected(event);\n                    }\n                }\n                break;\n            default:\n                this._keyManager?.onKeydown(event);\n        }\n    }\n    /**\n     * Callback for when the MutationObserver detects that the content has changed.\n     */\n    _onContentChanges() {\n        const textContent = this._elementRef.nativeElement.textContent;\n        // We need to diff the text content of the header, because the MutationObserver callback\n        // will fire even if the text content didn't change which is inefficient and is prone\n        // to infinite loops if a poorly constructed expression is passed in (see #14249).\n        if (textContent !== this._currentTextContent) {\n            this._currentTextContent = textContent || '';\n            // The content observer runs outside the `NgZone` by default, which\n            // means that we need to bring the callback back in ourselves.\n            this._ngZone.run(() => {\n                this.updatePagination();\n                this._alignInkBarToSelectedTab();\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /**\n     * Updates the view whether pagination should be enabled or not.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        this._checkPaginationEnabled();\n        this._checkScrollingControls();\n        this._updateTabScrollPosition();\n    }\n    /** Tracks which element has focus; used for keyboard navigation */\n    get focusIndex() {\n        return this._keyManager ? this._keyManager.activeItemIndex : 0;\n    }\n    /** When the focus index is set, we must manually send focus to the correct label */\n    set focusIndex(value) {\n        if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n            return;\n        }\n        this._keyManager.setActiveItem(value);\n    }\n    /**\n     * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n     * providing a valid index and return true.\n     */\n    _isValidIndex(index) {\n        return this._items ? !!this._items.toArray()[index] : true;\n    }\n    /**\n     * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n     * scrolling is enabled.\n     */\n    _setTabFocus(tabIndex) {\n        if (this._showPaginationControls) {\n            this._scrollToLabel(tabIndex);\n        }\n        if (this._items && this._items.length) {\n            this._items.toArray()[tabIndex].focus();\n            // Do not let the browser manage scrolling to focus the element, this will be handled\n            // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n            // should be the full width minus the offset width.\n            const containerEl = this._tabListContainer.nativeElement;\n            const dir = this._getLayoutDirection();\n            if (dir == 'ltr') {\n                containerEl.scrollLeft = 0;\n            }\n            else {\n                containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n            }\n        }\n    }\n    /** The layout direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n    _updateTabScrollPosition() {\n        if (this.disablePagination) {\n            return;\n        }\n        const scrollDistance = this.scrollDistance;\n        const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n        // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n        // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n        // and ripples will exceed the boundaries of the visible tab bar.\n        // See: https://github.com/angular/components/issues/10276\n        // We round the `transform` here, because transforms with sub-pixel precision cause some\n        // browsers to blur the content of the element.\n        this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n        // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n        // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n        // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n        // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n        if (this._platform.TRIDENT || this._platform.EDGE) {\n            this._tabListContainer.nativeElement.scrollLeft = 0;\n        }\n    }\n    /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n    get scrollDistance() {\n        return this._scrollDistance;\n    }\n    set scrollDistance(value) {\n        this._scrollTo(value);\n    }\n    /**\n     * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n     * the end of the list, respectively). The distance to scroll is computed to be a third of the\n     * length of the tab list view window.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollHeader(direction) {\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        // Move the scroll distance one-third the length of the tab list's viewport.\n        const scrollAmount = ((direction == 'before' ? -1 : 1) * viewLength) / 3;\n        return this._scrollTo(this._scrollDistance + scrollAmount);\n    }\n    /** Handles click events on the pagination arrows. */\n    _handlePaginatorClick(direction) {\n        this._stopInterval();\n        this._scrollHeader(direction);\n    }\n    /**\n     * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollToLabel(labelIndex) {\n        if (this.disablePagination) {\n            return;\n        }\n        const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n        if (!selectedLabel) {\n            return;\n        }\n        // The view length is the visible width of the tab labels.\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        const { offsetLeft, offsetWidth } = selectedLabel.elementRef.nativeElement;\n        let labelBeforePos, labelAfterPos;\n        if (this._getLayoutDirection() == 'ltr') {\n            labelBeforePos = offsetLeft;\n            labelAfterPos = labelBeforePos + offsetWidth;\n        }\n        else {\n            labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n            labelBeforePos = labelAfterPos - offsetWidth;\n        }\n        const beforeVisiblePos = this.scrollDistance;\n        const afterVisiblePos = this.scrollDistance + viewLength;\n        if (labelBeforePos < beforeVisiblePos) {\n            // Scroll header to move label to the before direction\n            this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n        }\n        else if (labelAfterPos > afterVisiblePos) {\n            // Scroll header to move label to the after direction\n            this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n        }\n    }\n    /**\n     * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n     * tab list is wider than the size of the header container, then the pagination controls should\n     * be shown.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkPaginationEnabled() {\n        if (this.disablePagination) {\n            this._showPaginationControls = false;\n        }\n        else {\n            const scrollWidth = this._tabListInner.nativeElement.scrollWidth;\n            const containerWidth = this._elementRef.nativeElement.offsetWidth;\n            // Usually checking that the scroll width is greater than the container width should be\n            // enough, but on Safari at specific widths the browser ends up rounding up when there's\n            // no pagination and rounding down once the pagination is added. This can throw the component\n            // into an infinite loop where the pagination shows up and disappears constantly. We work\n            // around it by adding a threshold to the calculation. From manual testing the threshold\n            // can be lowered to 2px and still resolve the issue, but we set a higher one to be safe.\n            // This shouldn't cause any content to be clipped, because tabs have a 24px horizontal\n            // padding. See b/316395154 for more information.\n            const isEnabled = scrollWidth - containerWidth >= 5;\n            if (!isEnabled) {\n                this.scrollDistance = 0;\n            }\n            if (isEnabled !== this._showPaginationControls) {\n                this._showPaginationControls = isEnabled;\n                this._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    /**\n     * Evaluate whether the before and after controls should be enabled or disabled.\n     * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n     * before button. If the header is at the end of the list (scroll distance is equal to the\n     * maximum distance we can scroll), then disable the after button.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkScrollingControls() {\n        if (this.disablePagination) {\n            this._disableScrollAfter = this._disableScrollBefore = true;\n        }\n        else {\n            // Check if the pagination arrows should be activated.\n            this._disableScrollBefore = this.scrollDistance == 0;\n            this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n     * is equal to the difference in width between the tab list container and tab header container.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _getMaxScrollDistance() {\n        const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        return lengthOfTabList - viewLength || 0;\n    }\n    /** Tells the ink-bar to align itself to the current label wrapper */\n    _alignInkBarToSelectedTab() {\n        const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n        const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n        if (selectedLabelWrapper) {\n            this._inkBar.alignToElement(selectedLabelWrapper);\n        }\n        else {\n            this._inkBar.hide();\n        }\n    }\n    /** Stops the currently-running paginator interval.  */\n    _stopInterval() {\n        this._stopScrolling.next();\n    }\n    /**\n     * Handles the user pressing down on one of the paginators.\n     * Starts scrolling the header after a certain amount of time.\n     * @param direction In which direction the paginator should be scrolled.\n     */\n    _handlePaginatorPress(direction, mouseEvent) {\n        // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n        // null check the `button`, but we do it so we don't break tests that use fake events.\n        if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n            return;\n        }\n        // Avoid overlapping timers.\n        this._stopInterval();\n        // Start a timer after the delay and keep firing based on the interval.\n        timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n            // Keep the timer going until something tells it to stop or the component is destroyed.\n            .pipe(takeUntil(merge(this._stopScrolling, this._destroyed)))\n            .subscribe(() => {\n            const { maxScrollDistance, distance } = this._scrollHeader(direction);\n            // Stop the timer if we've reached the start or the end.\n            if (distance === 0 || distance >= maxScrollDistance) {\n                this._stopInterval();\n            }\n        });\n    }\n    /**\n     * Scrolls the header to a given position.\n     * @param position Position to which to scroll.\n     * @returns Information on the current scroll distance and the maximum.\n     */\n    _scrollTo(position) {\n        if (this.disablePagination) {\n            return { maxScrollDistance: 0, distance: 0 };\n        }\n        const maxScrollDistance = this._getMaxScrollDistance();\n        this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n        // Mark that the scroll distance has changed so that after the view is checked, the CSS\n        // transformation can move the header.\n        this._scrollDistanceChanged = true;\n        this._checkScrollingControls();\n        return { maxScrollDistance, distance: this._scrollDistance };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginatedTabHeader, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatPaginatedTabHeader, isStandalone: true, inputs: { disablePagination: [\"disablePagination\", \"disablePagination\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute] }, outputs: { selectFocusedIndex: \"selectFocusedIndex\", indexFocused: \"indexFocused\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatPaginatedTabHeader, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [], propDecorators: { disablePagination: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], selectFocusedIndex: [{\n                type: Output\n            }], indexFocused: [{\n                type: Output\n            }] } });\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends MatPaginatedTabHeader {\n    _items;\n    _tabListContainer;\n    _tabList;\n    _tabListInner;\n    _nextPaginator;\n    _previousPaginator;\n    _inkBar;\n    /** Aria label of the header. */\n    ariaLabel;\n    /** Sets the `aria-labelledby` of the header. */\n    ariaLabelledby;\n    /** Whether the ripple effect is disabled or not. */\n    disableRipple = false;\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        super.ngAfterContentInit();\n    }\n    _itemSelected(event) {\n        event.preventDefault();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabHeader, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatTabHeader, isStandalone: true, selector: \"mat-tab-header\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute] }, host: { properties: { \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\" }, classAttribute: \"mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: MatTabLabelWrapper }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height, 1px);border-bottom-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height, 1px);border-top-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mdc-secondary-navigation-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\\n\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                    }, imports: [MatRipple, CdkObserveContent], template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height, 1px);border-bottom-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height, 1px);border-top-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mdc-secondary-navigation-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\\n\"] }]\n        }], propDecorators: { _items: [{\n                type: ContentChildren,\n                args: [MatTabLabelWrapper, { descendants: false }]\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n    _host = inject(MatTabBody);\n    /** Subscription to events for when the tab body begins centering. */\n    _centeringSub = Subscription.EMPTY;\n    /** Subscription to events for when the tab body finishes leaving from center position. */\n    _leavingSub = Subscription.EMPTY;\n    constructor() {\n        super();\n    }\n    /** Set initial visibility or set up subscription for changing visibility. */\n    ngOnInit() {\n        super.ngOnInit();\n        this._centeringSub = this._host._beforeCentering\n            .pipe(startWith(this._host._isCenterPosition()))\n            .subscribe((isCentering) => {\n            if (this._host._content && isCentering && !this.hasAttached()) {\n                this.attach(this._host._content);\n            }\n        });\n        this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n            if (!this._host.preserveContent) {\n                this.detach();\n            }\n        });\n    }\n    /** Clean up centering subscription. */\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._centeringSub.unsubscribe();\n        this._leavingSub.unsubscribe();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabBodyPortal, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTabBodyPortal, isStandalone: true, selector: \"[matTabBodyHost]\", usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabBodyPortal, decorators: [{\n            type: Directive,\n            args: [{ selector: '[matTabBodyHost]' }]\n        }], ctorParameters: () => [] });\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody {\n    _elementRef = inject(ElementRef);\n    _dir = inject(Directionality, { optional: true });\n    _ngZone = inject(NgZone);\n    _injector = inject(Injector);\n    _renderer = inject(Renderer2);\n    _animationsModule = inject(ANIMATION_MODULE_TYPE, { optional: true });\n    _eventCleanups;\n    _initialized;\n    _fallbackTimer;\n    /** Current position of the tab-body in the tab-group. Zero means that the tab is visible. */\n    _positionIndex;\n    /** Subscription to the directionality change observable. */\n    _dirChangeSubscription = Subscription.EMPTY;\n    /** Current position of the body within the tab group. */\n    _position;\n    /** Previous position of the body. */\n    _previousPosition;\n    /** Event emitted when the tab begins to animate towards the center as the active tab. */\n    _onCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n    _beforeCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n    _afterLeavingCenter = new EventEmitter();\n    /** Event emitted when the tab completes its animation towards the center. */\n    _onCentered = new EventEmitter(true);\n    /** The portal host inside of this container into which the tab body content will be loaded. */\n    _portalHost;\n    /** Element in which the content is rendered. */\n    _contentElement;\n    /** The tab body content to display. */\n    _content;\n    // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n    // anyway to prevent the animations module from throwing an error if the body is used on its own.\n    /** Duration for the tab's animation. */\n    animationDuration = '500ms';\n    /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n    preserveContent = false;\n    /** The shifted index position of the tab body, where zero represents the active center tab. */\n    set position(position) {\n        this._positionIndex = position;\n        this._computePositionAnimationState();\n    }\n    constructor() {\n        if (this._dir) {\n            const changeDetectorRef = inject(ChangeDetectorRef);\n            this._dirChangeSubscription = this._dir.change.subscribe((dir) => {\n                this._computePositionAnimationState(dir);\n                changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    ngOnInit() {\n        this._bindTransitionEvents();\n        if (this._position === 'center') {\n            this._setActiveClass(true);\n            // Allows for the dynamic height to animate properly on the initial run.\n            afterNextRender(() => this._onCentering.emit(this._elementRef.nativeElement.clientHeight), {\n                injector: this._injector,\n            });\n        }\n        this._initialized = true;\n    }\n    ngOnDestroy() {\n        clearTimeout(this._fallbackTimer);\n        this._eventCleanups?.forEach(cleanup => cleanup());\n        this._dirChangeSubscription.unsubscribe();\n    }\n    /** Sets up the transition events. */\n    _bindTransitionEvents() {\n        this._ngZone.runOutsideAngular(() => {\n            const element = this._elementRef.nativeElement;\n            const transitionDone = (event) => {\n                if (event.target === this._contentElement?.nativeElement) {\n                    this._elementRef.nativeElement.classList.remove('mat-tab-body-animating');\n                    // Only fire the actual callback when a transition is fully finished,\n                    // otherwise the content can jump around when the next transition starts.\n                    if (event.type === 'transitionend') {\n                        this._transitionDone();\n                    }\n                }\n            };\n            this._eventCleanups = [\n                this._renderer.listen(element, 'transitionstart', (event) => {\n                    if (event.target === this._contentElement?.nativeElement) {\n                        this._elementRef.nativeElement.classList.add('mat-tab-body-animating');\n                        this._transitionStarted();\n                    }\n                }),\n                this._renderer.listen(element, 'transitionend', transitionDone),\n                this._renderer.listen(element, 'transitioncancel', transitionDone),\n            ];\n        });\n    }\n    /** Called when a transition has started. */\n    _transitionStarted() {\n        clearTimeout(this._fallbackTimer);\n        const isCentering = this._position === 'center';\n        this._beforeCentering.emit(isCentering);\n        if (isCentering) {\n            this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n        }\n    }\n    /** Called when a transition is done. */\n    _transitionDone() {\n        if (this._position === 'center') {\n            this._onCentered.emit();\n        }\n        else if (this._previousPosition === 'center') {\n            this._afterLeavingCenter.emit();\n        }\n    }\n    /** Sets the active styling on the tab body based on its current position. */\n    _setActiveClass(isActive) {\n        this._elementRef.nativeElement.classList.toggle('mat-mdc-tab-body-active', isActive);\n    }\n    /** The text direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the provided position state is considered center, regardless of origin. */\n    _isCenterPosition() {\n        return this._positionIndex === 0;\n    }\n    /** Computes the position state that will be used for the tab-body animation trigger. */\n    _computePositionAnimationState(dir = this._getLayoutDirection()) {\n        this._previousPosition = this._position;\n        if (this._positionIndex < 0) {\n            this._position = dir == 'ltr' ? 'left' : 'right';\n        }\n        else if (this._positionIndex > 0) {\n            this._position = dir == 'ltr' ? 'right' : 'left';\n        }\n        else {\n            this._position = 'center';\n        }\n        if (this._animationsDisabled()) {\n            this._simulateTransitionEvents();\n        }\n        else if (this._initialized &&\n            (this._position === 'center' || this._previousPosition === 'center')) {\n            // The transition events are load-bearing and in some cases they might not fire (e.g.\n            // tests setting `* {transition: none}` to disable animations). This timeout will simulate\n            // them if a transition doesn't start within a certain amount of time.\n            clearTimeout(this._fallbackTimer);\n            this._fallbackTimer = this._ngZone.runOutsideAngular(() => setTimeout(() => this._simulateTransitionEvents(), 100));\n        }\n    }\n    /** Simulates the body's transition events in an environment where they might not fire. */\n    _simulateTransitionEvents() {\n        this._transitionStarted();\n        afterNextRender(() => this._transitionDone(), { injector: this._injector });\n    }\n    /** Whether animations are disabled for the tab group. */\n    _animationsDisabled() {\n        return (this._animationsModule === 'NoopAnimations' ||\n            this.animationDuration === '0ms' ||\n            this.animationDuration === '0s');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabBody, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTabBody, isStandalone: true, selector: \"mat-tab-body\", inputs: { _content: [\"content\", \"_content\"], animationDuration: \"animationDuration\", preserveContent: \"preserveContent\", position: \"position\" }, outputs: { _onCentering: \"_onCentering\", _beforeCentering: \"_beforeCentering\", _onCentered: \"_onCentered\" }, host: { properties: { \"attr.inert\": \"_position === \\\"center\\\" ? null : \\\"\\\"\" }, classAttribute: \"mat-mdc-tab-body\" }, viewQueries: [{ propertyName: \"_portalHost\", first: true, predicate: MatTabBodyPortal, descendants: true }, { propertyName: \"_contentElement\", first: true, predicate: [\"content\"], descendants: true }], ngImport: i0, template: \"<div\\n   class=\\\"mat-mdc-tab-body-content\\\"\\n   #content\\n   cdkScrollable\\n   [class.mat-tab-body-content-left]=\\\"_position === 'left'\\\"\\n   [class.mat-tab-body-content-right]=\\\"_position === 'right'\\\"\\n   [class.mat-tab-body-content-can-animate]=\\\"_position === 'center' || _previousPosition === 'center'\\\">\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\\n\"], dependencies: [{ kind: \"directive\", type: MatTabBodyPortal, selector: \"[matTabBodyHost]\" }, { kind: \"directive\", type: CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabBody, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-body', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'mat-mdc-tab-body',\n                        // In most cases the `visibility: hidden` that we set on the off-screen content is enough\n                        // to stop interactions with it, but if a child element sets its own `visibility`, it'll\n                        // override the one from the parent. This ensures that even those elements will be removed\n                        // from the accessibility tree.\n                        '[attr.inert]': '_position === \"center\" ? null : \"\"',\n                    }, imports: [MatTabBodyPortal, CdkScrollable], template: \"<div\\n   class=\\\"mat-mdc-tab-body-content\\\"\\n   #content\\n   cdkScrollable\\n   [class.mat-tab-body-content-left]=\\\"_position === 'left'\\\"\\n   [class.mat-tab-body-content-right]=\\\"_position === 'right'\\\"\\n   [class.mat-tab-body-content-can-animate]=\\\"_position === 'center' || _previousPosition === 'center'\\\">\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _onCentering: [{\n                type: Output\n            }], _beforeCentering: [{\n                type: Output\n            }], _onCentered: [{\n                type: Output\n            }], _portalHost: [{\n                type: ViewChild,\n                args: [MatTabBodyPortal]\n            }], _contentElement: [{\n                type: ViewChild,\n                args: ['content']\n            }], _content: [{\n                type: Input,\n                args: ['content']\n            }], animationDuration: [{\n                type: Input\n            }], preserveContent: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }] } });\n\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _ngZone = inject(NgZone);\n    _tabsSubscription = Subscription.EMPTY;\n    _tabLabelSubscription = Subscription.EMPTY;\n    _tabBodySubscription = Subscription.EMPTY;\n    _diAnimationsDisabled = inject(ANIMATION_MODULE_TYPE, { optional: true }) === 'NoopAnimations';\n    /**\n     * All tabs inside the tab group. This includes tabs that belong to groups that are nested\n     * inside the current one. We filter out only the tabs that belong to this group in `_tabs`.\n     */\n    _allTabs;\n    _tabBodies;\n    _tabBodyWrapper;\n    _tabHeader;\n    /** All of the tabs that belong to the group. */\n    _tabs = new QueryList();\n    /** The tab index that should be selected after the content has been checked. */\n    _indexToSelect = 0;\n    /** Index of the tab that was focused last. */\n    _lastFocusedTabIndex = null;\n    /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n    _tabBodyWrapperHeight = 0;\n    /**\n     * Theme color of the tab group. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent;\n    }\n    set fitInkBarToContent(value) {\n        this._fitInkBarToContent = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    _fitInkBarToContent = false;\n    /** Whether tabs should be stretched to fill the header. */\n    stretchTabs = true;\n    /** Alignment for tabs label. */\n    alignTabs = null;\n    /** Whether the tab group should grow to the size of the active tab. */\n    dynamicHeight = false;\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n        this._indexToSelect = isNaN(value) ? null : value;\n    }\n    _selectedIndex = null;\n    /** Position of the tab header. */\n    headerPosition = 'above';\n    /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        const stringValue = value + '';\n        this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n    }\n    _animationDuration;\n    /**\n     * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n     * accessibility when the tab does not have focusable elements or if it has scrollable content.\n     * The `tabindex` will be removed automatically for inactive tabs.\n     * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n     */\n    get contentTabIndex() {\n        return this._contentTabIndex;\n    }\n    set contentTabIndex(value) {\n        this._contentTabIndex = isNaN(value) ? null : value;\n    }\n    _contentTabIndex;\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    disablePagination = false;\n    /** Whether ripples in the tab group are disabled. */\n    disableRipple = false;\n    /**\n     * By default tabs remove their content from the DOM while it's off-screen.\n     * Setting this to `true` will keep it in the DOM which will prevent elements\n     * like iframes and videos from reloading next time it comes back into the view.\n     */\n    preserveContent = false;\n    /**\n     * Theme color of the background of the tab group. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     *\n     * @deprecated The background color should be customized through Sass theming APIs.\n     * @breaking-change 20.0.0 Remove this input\n     */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    _backgroundColor;\n    /** Aria label of the inner `tablist` of the group. */\n    ariaLabel;\n    /** Sets the `aria-labelledby` of the inner `tablist` of the group. */\n    ariaLabelledby;\n    /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n    selectedIndexChange = new EventEmitter();\n    /** Event emitted when focus has changed within a tab group. */\n    focusChange = new EventEmitter();\n    /** Event emitted when the body animation has completed */\n    animationDone = new EventEmitter();\n    /** Event emitted when the tab selection has changed. */\n    selectedTabChange = new EventEmitter(true);\n    _groupId;\n    /** Whether the tab group is rendered on the server. */\n    _isServer = !inject(Platform).isBrowser;\n    constructor() {\n        const defaultConfig = inject(MAT_TABS_CONFIG, { optional: true });\n        this._groupId = inject(_IdGenerator).getId('mat-tab-group-');\n        this.animationDuration =\n            defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.dynamicHeight =\n            defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n        if (defaultConfig?.contentTabIndex != null) {\n            this.contentTabIndex = defaultConfig.contentTabIndex;\n        }\n        this.preserveContent = !!defaultConfig?.preserveContent;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n        this.stretchTabs =\n            defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n        this.alignTabs =\n            defaultConfig && defaultConfig.alignTabs != null ? defaultConfig.alignTabs : null;\n    }\n    /**\n     * After the content is checked, this component knows what tabs have been defined\n     * and what the selected index should be. This is where we can know exactly what position\n     * each tab should be in according to the new selected index, and additionally we know how\n     * a new selected tab should transition in (from the left or right).\n     */\n    ngAfterContentChecked() {\n        // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n        // the amount of tabs changes before the actual change detection runs.\n        const indexToSelect = (this._indexToSelect = this._clampTabIndex(this._indexToSelect));\n        // If there is a change in selected index, emit a change event. Should not trigger if\n        // the selected index has not yet been initialized.\n        if (this._selectedIndex != indexToSelect) {\n            const isFirstRun = this._selectedIndex == null;\n            if (!isFirstRun) {\n                this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                // Preserve the height so page doesn't scroll up during tab change.\n                // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n                const wrapper = this._tabBodyWrapper.nativeElement;\n                wrapper.style.minHeight = wrapper.clientHeight + 'px';\n            }\n            // Changing these values after change detection has run\n            // since the checked content may contain references to them.\n            Promise.resolve().then(() => {\n                this._tabs.forEach((tab, index) => (tab.isActive = index === indexToSelect));\n                if (!isFirstRun) {\n                    this.selectedIndexChange.emit(indexToSelect);\n                    // Clear the min-height, this was needed during tab change to avoid\n                    // unnecessary scrolling.\n                    this._tabBodyWrapper.nativeElement.style.minHeight = '';\n                }\n            });\n        }\n        // Setup the position for each tab and optionally setup an origin on the next selected tab.\n        this._tabs.forEach((tab, index) => {\n            tab.position = index - indexToSelect;\n            // If there is already a selected tab, then set up an origin for the next selected tab\n            // if it doesn't have one already.\n            if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n                tab.origin = indexToSelect - this._selectedIndex;\n            }\n        });\n        if (this._selectedIndex !== indexToSelect) {\n            this._selectedIndex = indexToSelect;\n            this._lastFocusedTabIndex = null;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngAfterContentInit() {\n        this._subscribeToAllTabChanges();\n        this._subscribeToTabLabels();\n        // Subscribe to changes in the amount of tabs, in order to be\n        // able to re-render the content as new tabs are added or removed.\n        this._tabsSubscription = this._tabs.changes.subscribe(() => {\n            const indexToSelect = this._clampTabIndex(this._indexToSelect);\n            // Maintain the previously-selected tab if a new tab is added or removed and there is no\n            // explicit change that selects a different tab.\n            if (indexToSelect === this._selectedIndex) {\n                const tabs = this._tabs.toArray();\n                let selectedTab;\n                for (let i = 0; i < tabs.length; i++) {\n                    if (tabs[i].isActive) {\n                        // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n                        // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n                        // adding a tab within the `selectedIndexChange` event.\n                        this._indexToSelect = this._selectedIndex = i;\n                        this._lastFocusedTabIndex = null;\n                        selectedTab = tabs[i];\n                        break;\n                    }\n                }\n                // If we haven't found an active tab and a tab exists at the selected index, it means\n                // that the active tab was swapped out. Since this won't be picked up by the rendering\n                // loop in `ngAfterContentChecked`, we need to sync it up manually.\n                if (!selectedTab && tabs[indexToSelect]) {\n                    Promise.resolve().then(() => {\n                        tabs[indexToSelect].isActive = true;\n                        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                    });\n                }\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    ngAfterViewInit() {\n        this._tabBodySubscription = this._tabBodies.changes.subscribe(() => this._bodyCentered(true));\n    }\n    /** Listens to changes in all of the tabs. */\n    _subscribeToAllTabChanges() {\n        // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n        // some that are inside of nested tab groups. We filter them out manually by checking that\n        // the closest group to the tab is the current one.\n        this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe((tabs) => {\n            this._tabs.reset(tabs.filter(tab => {\n                return tab._closestTabGroup === this || !tab._closestTabGroup;\n            }));\n            this._tabs.notifyOnChanges();\n        });\n    }\n    ngOnDestroy() {\n        this._tabs.destroy();\n        this._tabsSubscription.unsubscribe();\n        this._tabLabelSubscription.unsubscribe();\n        this._tabBodySubscription.unsubscribe();\n    }\n    /** Re-aligns the ink bar to the selected tab element. */\n    realignInkBar() {\n        if (this._tabHeader) {\n            this._tabHeader._alignInkBarToSelectedTab();\n        }\n    }\n    /**\n     * Recalculates the tab group's pagination dimensions.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        if (this._tabHeader) {\n            this._tabHeader.updatePagination();\n        }\n    }\n    /**\n     * Sets focus to a particular tab.\n     * @param index Index of the tab to be focused.\n     */\n    focusTab(index) {\n        const header = this._tabHeader;\n        if (header) {\n            header.focusIndex = index;\n        }\n    }\n    _focusChanged(index) {\n        this._lastFocusedTabIndex = index;\n        this.focusChange.emit(this._createChangeEvent(index));\n    }\n    _createChangeEvent(index) {\n        const event = new MatTabChangeEvent();\n        event.index = index;\n        if (this._tabs && this._tabs.length) {\n            event.tab = this._tabs.toArray()[index];\n        }\n        return event;\n    }\n    /**\n     * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n     * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n     * binding to be updated, we need to subscribe to changes in it and trigger change detection\n     * manually.\n     */\n    _subscribeToTabLabels() {\n        if (this._tabLabelSubscription) {\n            this._tabLabelSubscription.unsubscribe();\n        }\n        this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Clamps the given index to the bounds of 0 and the tabs length. */\n    _clampTabIndex(index) {\n        // Note the `|| 0`, which ensures that values like NaN can't get through\n        // and which would otherwise throw the component into an infinite loop\n        // (since Math.max(NaN, 0) === NaN).\n        return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n    }\n    /** Returns a unique id for each tab label element */\n    _getTabLabelId(tab, index) {\n        return tab.id || `${this._groupId}-label-${index}`;\n    }\n    /** Returns a unique id for each tab content element */\n    _getTabContentId(index) {\n        return `${this._groupId}-content-${index}`;\n    }\n    /**\n     * Sets the height of the body wrapper to the height of the activating tab if dynamic\n     * height property is true.\n     */\n    _setTabBodyWrapperHeight(tabHeight) {\n        if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n            this._tabBodyWrapperHeight = tabHeight;\n            return;\n        }\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n        // This conditional forces the browser to paint the height so that\n        // the animation to the new height can have an origin.\n        if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n            wrapper.style.height = tabHeight + 'px';\n        }\n    }\n    /** Removes the height of the tab body wrapper. */\n    _removeTabBodyWrapperHeight() {\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        this._tabBodyWrapperHeight = wrapper.clientHeight;\n        wrapper.style.height = '';\n        this._ngZone.run(() => this.animationDone.emit());\n    }\n    /** Handle click events, setting new selected index if appropriate. */\n    _handleClick(tab, tabHeader, index) {\n        tabHeader.focusIndex = index;\n        if (!tab.disabled) {\n            this.selectedIndex = index;\n        }\n    }\n    /** Retrieves the tabindex for the tab. */\n    _getTabIndex(index) {\n        const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n        return index === targetIndex ? 0 : -1;\n    }\n    /** Callback for when the focused state of a tab has changed. */\n    _tabFocusChanged(focusOrigin, index) {\n        // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n        // can cause the tab to be moved out from under the pointer, interrupting the\n        // click sequence (see #21898). We don't need to scroll the tab into view for\n        // such cases anyway, because it will be done when the tab becomes selected.\n        if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n            this._tabHeader.focusIndex = index;\n        }\n    }\n    /**\n     * Callback invoked when the centered state of a tab body changes.\n     * @param isCenter Whether the tab will be in the center.\n     */\n    _bodyCentered(isCenter) {\n        // Marks all the existing tabs as inactive and the center tab as active. Note that this can\n        // be achieved much easier by using a class binding on each body. The problem with\n        // doing so is that we can't control the timing of when the class is removed from the\n        // previously-active element and added to the newly-active one. If there's a tick between\n        // removing the class and adding the new one, the content will jump in a very jarring way.\n        // We go through the trouble of setting the classes ourselves to guarantee that they're\n        // swapped out at the same time.\n        if (isCenter) {\n            this._tabBodies?.forEach((body, i) => body._setActiveClass(i === this._selectedIndex));\n        }\n    }\n    _animationsDisabled() {\n        return (this._diAnimationsDisabled ||\n            this.animationDuration === '0' ||\n            this.animationDuration === '0ms');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabGroup, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatTabGroup, isStandalone: true, selector: \"mat-tab-group\", inputs: { color: \"color\", fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute], stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute], alignTabs: [\"mat-align-tabs\", \"alignTabs\"], dynamicHeight: [\"dynamicHeight\", \"dynamicHeight\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute], headerPosition: \"headerPosition\", animationDuration: \"animationDuration\", contentTabIndex: [\"contentTabIndex\", \"contentTabIndex\", numberAttribute], disablePagination: [\"disablePagination\", \"disablePagination\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], preserveContent: [\"preserveContent\", \"preserveContent\", booleanAttribute], backgroundColor: \"backgroundColor\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"] }, outputs: { selectedIndexChange: \"selectedIndexChange\", focusChange: \"focusChange\", animationDone: \"animationDone\", selectedTabChange: \"selectedTabChange\" }, host: { properties: { \"class\": \"\\\"mat-\\\" + (color || \\\"primary\\\")\", \"class.mat-mdc-tab-group-dynamic-height\": \"dynamicHeight\", \"class.mat-mdc-tab-group-inverted-header\": \"headerPosition === \\\"below\\\"\", \"class.mat-mdc-tab-group-stretch-tabs\": \"stretchTabs\", \"attr.mat-align-tabs\": \"alignTabs\", \"style.--mat-tab-animation-duration\": \"animationDuration\" }, classAttribute: \"mat-mdc-tab-group\" }, providers: [\n            {\n                provide: MAT_TAB_GROUP,\n                useExisting: MatTabGroup,\n            },\n        ], queries: [{ propertyName: \"_allTabs\", predicate: MatTab, descendants: true }], viewQueries: [{ propertyName: \"_tabBodyWrapper\", first: true, predicate: [\"tabBodyWrapper\"], descendants: true }, { propertyName: \"_tabHeader\", first: true, predicate: [\"tabHeader\"], descendants: true }, { propertyName: \"_tabBodies\", predicate: MatTabBody, descendants: true }], exportAs: [\"matTabGroup\"], ngImport: i0, template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                [aria-label]=\\\"ariaLabel\\\"\\n                [aria-labelledby]=\\\"ariaLabelledby\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(tab, $index)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex($index)\\\"\\n        [attr.aria-posinset]=\\\"$index + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId($index)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === $index\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === $index\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, $index)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, $index)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled()\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab;) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId($index)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === $index) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(tab, $index)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== $index\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\"\\n                 (_beforeCentering)=\\\"_bodyCentered($event)\\\"/>\\n  }\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mdc-secondary-navigation-tab-container-height, 48px);font-family:var(--mat-tab-header-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-header-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-header-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-header-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-header-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mdc-tab-indicator-active-indicator-height, 2px);border-radius:var(--mdc-tab-indicator-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\\n\"], dependencies: [{ kind: \"component\", type: MatTabHeader, selector: \"mat-tab-header\", inputs: [\"aria-label\", \"aria-labelledby\", \"disableRipple\"] }, { kind: \"directive\", type: MatTabLabelWrapper, selector: \"[matTabLabelWrapper]\", inputs: [\"disabled\"] }, { kind: \"directive\", type: CdkMonitorFocus, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: [\"cdkFocusChange\"], exportAs: [\"cdkMonitorFocus\"] }, { kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }, { kind: \"component\", type: MatTabBody, selector: \"mat-tab-body\", inputs: [\"content\", \"animationDuration\", \"preserveContent\", \"position\"], outputs: [\"_onCentering\", \"_beforeCentering\", \"_onCentered\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-group', exportAs: 'matTabGroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, providers: [\n                        {\n                            provide: MAT_TAB_GROUP,\n                            useExisting: MatTabGroup,\n                        },\n                    ], host: {\n                        'class': 'mat-mdc-tab-group',\n                        '[class]': '\"mat-\" + (color || \"primary\")',\n                        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n                        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n                        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n                        '[attr.mat-align-tabs]': 'alignTabs',\n                        '[style.--mat-tab-animation-duration]': 'animationDuration',\n                    }, imports: [\n                        MatTabHeader,\n                        MatTabLabelWrapper,\n                        CdkMonitorFocus,\n                        MatRipple,\n                        CdkPortalOutlet,\n                        MatTabBody,\n                    ], template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                [aria-label]=\\\"ariaLabel\\\"\\n                [aria-labelledby]=\\\"ariaLabelledby\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(tab, $index)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex($index)\\\"\\n        [attr.aria-posinset]=\\\"$index + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId($index)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === $index\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === $index\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, $index)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, $index)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled()\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab;) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId($index)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === $index) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(tab, $index)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== $index\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\"\\n                 (_beforeCentering)=\\\"_bodyCentered($event)\\\"/>\\n  }\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mdc-secondary-navigation-tab-container-height, 48px);font-family:var(--mat-tab-header-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-header-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-header-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-header-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-header-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mdc-tab-indicator-active-indicator-height, 2px);border-radius:var(--mdc-tab-indicator-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _allTabs: [{\n                type: ContentChildren,\n                args: [MatTab, { descendants: true }]\n            }], _tabBodies: [{\n                type: ViewChildren,\n                args: [MatTabBody]\n            }], _tabBodyWrapper: [{\n                type: ViewChild,\n                args: ['tabBodyWrapper']\n            }], _tabHeader: [{\n                type: ViewChild,\n                args: ['tabHeader']\n            }], color: [{\n                type: Input\n            }], fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stretchTabs: [{\n                type: Input,\n                args: [{ alias: 'mat-stretch-tabs', transform: booleanAttribute }]\n            }], alignTabs: [{\n                type: Input,\n                args: [{ alias: 'mat-align-tabs' }]\n            }], dynamicHeight: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], headerPosition: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], contentTabIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], disablePagination: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], preserveContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], backgroundColor: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], selectedIndexChange: [{\n                type: Output\n            }], focusChange: [{\n                type: Output\n            }], animationDone: [{\n                type: Output\n            }], selectedTabChange: [{\n                type: Output\n            }] } });\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {\n    /** Index of the currently-selected tab. */\n    index;\n    /** Reference to the currently-selected tab. */\n    tab;\n}\n\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends MatPaginatedTabHeader {\n    _focusedItem = signal(null);\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent.value;\n    }\n    set fitInkBarToContent(value) {\n        this._fitInkBarToContent.next(value);\n        this._changeDetectorRef.markForCheck();\n    }\n    _fitInkBarToContent = new BehaviorSubject(false);\n    /** Whether tabs should be stretched to fill the header. */\n    stretchTabs = true;\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        const stringValue = value + '';\n        this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n    }\n    _animationDuration;\n    /** Query list of all tab links of the tab navigation. */\n    _items;\n    /**\n     * Theme color of the background of the tab nav. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    _backgroundColor;\n    /** Whether the ripple effect is disabled or not. */\n    disableRipple = false;\n    /**\n     * Theme color of the nav bar. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color = 'primary';\n    /**\n     * Associated tab panel controlled by the nav bar. If not provided, then the nav bar\n     * follows the ARIA link / navigation landmark pattern. If provided, it follows the\n     * ARIA tabs design pattern.\n     */\n    tabPanel;\n    _tabListContainer;\n    _tabList;\n    _tabListInner;\n    _nextPaginator;\n    _previousPaginator;\n    _inkBar;\n    constructor() {\n        const elementRef = inject(ElementRef);\n        const dir = inject(Directionality, { optional: true });\n        const ngZone = inject(NgZone);\n        const changeDetectorRef = inject(ChangeDetectorRef);\n        const viewportRuler = inject(ViewportRuler);\n        const platform = inject(Platform);\n        const animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n        const defaultConfig = inject(MAT_TABS_CONFIG, { optional: true });\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n        this.stretchTabs =\n            defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    }\n    _itemSelected() {\n        // noop\n    }\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        // We need this to run before the `changes` subscription in parent to ensure that the\n        // selectedIndex is up-to-date by the time the super class starts looking for it.\n        this._items.changes\n            .pipe(startWith(null), takeUntil(this._destroyed))\n            .subscribe(() => this.updateActiveLink());\n        super.ngAfterContentInit();\n        // Turn the `change` stream into a signal to try and avoid \"changed after checked\" errors.\n        this._keyManager.change.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => this._focusedItem.set(this._keyManager?.activeItem || null));\n    }\n    ngAfterViewInit() {\n        if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n        }\n        super.ngAfterViewInit();\n    }\n    /** Notifies the component that the active link has been changed. */\n    updateActiveLink() {\n        if (!this._items) {\n            return;\n        }\n        const items = this._items.toArray();\n        for (let i = 0; i < items.length; i++) {\n            if (items[i].active) {\n                this.selectedIndex = i;\n                if (this.tabPanel) {\n                    this.tabPanel._activeTabId = items[i].id;\n                }\n                // Updating the `selectedIndex` won't trigger the `change` event on\n                // the key manager so we need to set the signal from here.\n                this._focusedItem.set(items[i]);\n                this._changeDetectorRef.markForCheck();\n                return;\n            }\n        }\n        this.selectedIndex = -1;\n    }\n    _getRole() {\n        return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n    }\n    _hasFocus(link) {\n        return this._keyManager?.activeItem === link;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabNav, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatTabNav, isStandalone: true, selector: \"[mat-tab-nav-bar]\", inputs: { fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute], stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute], animationDuration: \"animationDuration\", backgroundColor: \"backgroundColor\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], color: \"color\", tabPanel: \"tabPanel\" }, host: { properties: { \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\", \"class.mat-mdc-tab-nav-bar-stretch-tabs\": \"stretchTabs\", \"class.mat-primary\": \"color !== \\\"warn\\\" && color !== \\\"accent\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"style.--mat-tab-animation-duration\": \"animationDuration\" }, classAttribute: \"mat-mdc-tab-nav-bar mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: i0.forwardRef(() => MatTabLink), descendants: true }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], exportAs: [\"matTabNavBar\", \"matTabNav\"], usesInheritance: true, ngImport: i0, template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-links,.mat-mdc-tab-links.cdk-drop-list{min-height:var(--mdc-secondary-navigation-tab-container-height, 48px)}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height, 1px);border-bottom-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\\n\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabNav, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-nav-bar]', exportAs: 'matTabNavBar, matTabNav', host: {\n                        '[attr.role]': '_getRole()',\n                        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n                        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[style.--mat-tab-animation-duration]': 'animationDuration',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, imports: [MatRipple, CdkObserveContent], template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-links,.mat-mdc-tab-links.cdk-drop-list{min-height:var(--mdc-secondary-navigation-tab-container-height, 48px)}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height, 1px);border-bottom-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stretchTabs: [{\n                type: Input,\n                args: [{ alias: 'mat-stretch-tabs', transform: booleanAttribute }]\n            }], animationDuration: [{\n                type: Input\n            }], _items: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatTabLink), { descendants: true }]\n            }], backgroundColor: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], color: [{\n                type: Input\n            }], tabPanel: [{\n                type: Input\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }] } });\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends InkBarItem {\n    _tabNavBar = inject(MatTabNav);\n    elementRef = inject(ElementRef);\n    _focusMonitor = inject(FocusMonitor);\n    _destroyed = new Subject();\n    /** Whether the tab link is active or not. */\n    _isActive = false;\n    _tabIndex = computed(() => this._tabNavBar._focusedItem() === this ? this.tabIndex : -1);\n    /** Whether the link is active. */\n    get active() {\n        return this._isActive;\n    }\n    set active(value) {\n        if (value !== this._isActive) {\n            this._isActive = value;\n            this._tabNavBar.updateActiveLink();\n        }\n    }\n    /** Whether the tab link is disabled. */\n    disabled = false;\n    /** Whether ripples are disabled on the tab link. */\n    disableRipple = false;\n    tabIndex = 0;\n    /**\n     * Ripple configuration for ripples that are launched on pointer down. The ripple config\n     * is set to the global ripple options since we don't have any configurable options for\n     * the tab link ripples.\n     * @docs-private\n     */\n    rippleConfig;\n    /**\n     * Whether ripples are disabled on interaction.\n     * @docs-private\n     */\n    get rippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._tabNavBar.disableRipple ||\n            !!this.rippleConfig.disabled);\n    }\n    /** Unique id for the tab. */\n    id = inject(_IdGenerator).getId('mat-tab-link-');\n    constructor() {\n        super();\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n            optional: true,\n        });\n        const tabIndex = inject(new HostAttributeToken('tabindex'), { optional: true });\n        const animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n        this.rippleConfig = globalRippleOptions || {};\n        this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n        if (animationMode === 'NoopAnimations') {\n            this.rippleConfig.animation = { enterDuration: 0, exitDuration: 0 };\n        }\n        this._tabNavBar._fitInkBarToContent\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(fitInkBarToContent => {\n            this.fitInkBarToContent = fitInkBarToContent;\n        });\n    }\n    /** Focuses the tab link. */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this.elementRef);\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        super.ngOnDestroy();\n        this._focusMonitor.stopMonitoring(this.elementRef);\n    }\n    _handleFocus() {\n        // Since we allow navigation through tabbing in the nav bar, we\n        // have to update the focused index whenever the link receives focus.\n        this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n    }\n    _handleKeydown(event) {\n        if (event.keyCode === SPACE || event.keyCode === ENTER) {\n            if (this.disabled) {\n                event.preventDefault();\n            }\n            else if (this._tabNavBar.tabPanel) {\n                // Only prevent the default action on space since it can scroll the page.\n                // Don't prevent enter since it can break link navigation.\n                if (event.keyCode === SPACE) {\n                    event.preventDefault();\n                }\n                this.elementRef.nativeElement.click();\n            }\n        }\n    }\n    _getAriaControls() {\n        return this._tabNavBar.tabPanel\n            ? this._tabNavBar.tabPanel?.id\n            : this.elementRef.nativeElement.getAttribute('aria-controls');\n    }\n    _getAriaSelected() {\n        if (this._tabNavBar.tabPanel) {\n            return this.active ? 'true' : 'false';\n        }\n        else {\n            return this.elementRef.nativeElement.getAttribute('aria-selected');\n        }\n    }\n    _getAriaCurrent() {\n        return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n    }\n    _getRole() {\n        return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabLink, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatTabLink, isStandalone: true, selector: \"[mat-tab-link], [matTabLink]\", inputs: { active: [\"active\", \"active\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], id: \"id\" }, host: { listeners: { \"focus\": \"_handleFocus()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-controls\": \"_getAriaControls()\", \"attr.aria-current\": \"_getAriaCurrent()\", \"attr.aria-disabled\": \"disabled\", \"attr.aria-selected\": \"_getAriaSelected()\", \"attr.id\": \"id\", \"attr.tabIndex\": \"_tabIndex()\", \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-disabled\": \"disabled\", \"class.mdc-tab--active\": \"active\" }, classAttribute: \"mdc-tab mat-mdc-tab-link mat-focus-indicator\" }, exportAs: [\"matTabLink\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mdc-secondary-navigation-tab-container-height, 48px);font-family:var(--mat-tab-header-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-header-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-header-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-header-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-header-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mdc-tab-indicator-active-indicator-height, 2px);border-radius:var(--mdc-tab-indicator-active-indicator-shape, 0)}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\\n\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabLink, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-link], [matTabLink]', exportAs: 'matTabLink', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'mdc-tab mat-mdc-tab-link mat-focus-indicator',\n                        '[attr.aria-controls]': '_getAriaControls()',\n                        '[attr.aria-current]': '_getAriaCurrent()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.aria-selected]': '_getAriaSelected()',\n                        '[attr.id]': 'id',\n                        '[attr.tabIndex]': '_tabIndex()',\n                        '[attr.role]': '_getRole()',\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[class.mdc-tab--active]': 'active',\n                        '(focus)': '_handleFocus()',\n                        '(keydown)': '_handleKeydown($event)',\n                    }, imports: [MatRipple], template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mdc-secondary-navigation-tab-container-height, 48px);font-family:var(--mat-tab-header-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-header-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-header-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-header-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-header-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mdc-tab-indicator-active-indicator-height, 2px);border-radius:var(--mdc-tab-indicator-active-indicator-shape, 0)}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { active: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], id: [{\n                type: Input\n            }] } });\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n    /** Unique id for the tab panel. */\n    id = inject(_IdGenerator).getId('mat-tab-nav-panel-');\n    /** Id of the active tab in the nav bar. */\n    _activeTabId;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabNavPanel, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatTabNavPanel, isStandalone: true, selector: \"mat-tab-nav-panel\", inputs: { id: \"id\" }, host: { attributes: { \"role\": \"tabpanel\" }, properties: { \"attr.aria-labelledby\": \"_activeTabId\", \"attr.id\": \"id\" }, classAttribute: \"mat-mdc-tab-nav-panel\" }, exportAs: [\"matTabNavPanel\"], ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabNavPanel, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-tab-nav-panel',\n                    exportAs: 'matTabNavPanel',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        '[attr.aria-labelledby]': '_activeTabId',\n                        '[attr.id]': 'id',\n                        'class': 'mat-mdc-tab-nav-panel',\n                        'role': 'tabpanel',\n                    },\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], propDecorators: { id: [{\n                type: Input\n            }] } });\n\nclass MatTabsModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabsModule, imports: [MatCommonModule,\n            MatTabContent,\n            MatTabLabel,\n            MatTab,\n            MatTabGroup,\n            MatTabNav,\n            MatTabNavPanel,\n            MatTabLink], exports: [MatCommonModule,\n            MatTabContent,\n            MatTabLabel,\n            MatTab,\n            MatTabGroup,\n            MatTabNav,\n            MatTabNavPanel,\n            MatTabLink] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabsModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatTabsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                    ],\n                    exports: [\n                        MatCommonModule,\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                    ],\n                }]\n        }] });\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0.\n */\nconst matTabsAnimations = {\n    // Represents:\n    // trigger('translateTab', [\n    //   // Transitions to `none` instead of 0, because some browsers might blur the content.\n    //   state(\n    //     'center, void, left-origin-center, right-origin-center',\n    //     style({transform: 'none', visibility: 'visible'}),\n    //   ),\n    //   // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n    //   // in order to ensure that the element has a height before its state changes. This is\n    //   // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n    //   // not have a static height and is not rendered. See related issue: #9465\n    //   state(\n    //     'left',\n    //     style({\n    //       transform: 'translate3d(-100%, 0, 0)',\n    //       minHeight: '1px',\n    //       // Normally this is redundant since we detach the content from the DOM, but if the user\n    //       // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n    //       visibility: 'hidden',\n    //     }),\n    //   ),\n    //   state(\n    //     'right',\n    //     style({\n    //       transform: 'translate3d(100%, 0, 0)',\n    //       minHeight: '1px',\n    //       visibility: 'hidden',\n    //     }),\n    //   ),\n    //   transition(\n    //     '* => left, * => right, left => center, right => center',\n    //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n    //   ),\n    //   transition('void => left-origin-center', [\n    //     style({transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'}),\n    //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n    //   ]),\n    //   transition('void => right-origin-center', [\n    //     style({transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'}),\n    //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n    //   ]),\n    // ])\n    /** Animation translates a tab along the X axis. */\n    translateTab: {\n        type: 7,\n        name: 'translateTab',\n        definitions: [\n            {\n                type: 0,\n                name: 'center, void, left-origin-center, right-origin-center',\n                styles: {\n                    type: 6,\n                    styles: { transform: 'none', visibility: 'visible' },\n                    offset: null,\n                },\n            },\n            {\n                type: 0,\n                name: 'left',\n                styles: {\n                    type: 6,\n                    styles: {\n                        transform: 'translate3d(-100%, 0, 0)',\n                        minHeight: '1px',\n                        visibility: 'hidden',\n                    },\n                    offset: null,\n                },\n            },\n            {\n                type: 0,\n                name: 'right',\n                styles: {\n                    type: 6,\n                    styles: {\n                        transform: 'translate3d(100%, 0, 0)',\n                        minHeight: '1px',\n                        visibility: 'hidden',\n                    },\n                    offset: null,\n                },\n            },\n            {\n                type: 1,\n                expr: '* => left, * => right, left => center, right => center',\n                animation: {\n                    type: 4,\n                    styles: null,\n                    timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: 'void => left-origin-center',\n                animation: [\n                    {\n                        type: 6,\n                        styles: { transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden' },\n                        offset: null,\n                    },\n                    {\n                        type: 4,\n                        styles: null,\n                        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)',\n                    },\n                ],\n                options: null,\n            },\n            {\n                type: 1,\n                expr: 'void => right-origin-center',\n                animation: [\n                    {\n                        type: 6,\n                        styles: { transform: 'translate3d(100%, 0, 0)', visibility: 'hidden' },\n                        offset: null,\n                    },\n                    {\n                        type: 4,\n                        styles: null,\n                        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)',\n                    },\n                ],\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, matTabsAnimations };\n//# sourceMappingURL=tabs.mjs.map\n"], "mappings": ";;;;;IA+BiFA,EAAE,CAAAC,YAAA,EAgI8lC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,mCAAAC,EAAA,EAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,wDAAAJ,EAAA,EAAAC,GAAA;AAAA,SAAAI,0CAAAL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhIjmCT,EAAE,CAAAe,UAAA,IAAAF,uDAAA,yBA0iDu/E,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAO,MAAA,GA1iD1/EhB,EAAE,CAAAiB,aAAA,GAAAC,SAAA;IAAFlB,EAAE,CAAAmB,UAAA,oBAAAH,MAAA,CAAAI,aA0iDs/E,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1iDz/ET,EAAE,CAAAsB,MAAA,EA0iD2iF,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAO,MAAA,GA1iD9iFhB,EAAE,CAAAiB,aAAA,GAAAC,SAAA;IAAFlB,EAAE,CAAAuB,iBAAA,CAAAP,MAAA,CAAAQ,SA0iD2iF,CAAC;EAAA;AAAA;AAAA,SAAAC,4BAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiB,GAAA,GA1iD9iF1B,EAAE,CAAA2B,gBAAA;IAAF3B,EAAE,CAAA4B,cAAA,eA0iDwrD,CAAC;IA1iD3rD5B,EAAE,CAAA6B,UAAA,mBAAAC,iDAAA;MAAA,MAAAC,MAAA,GAAF/B,EAAE,CAAAgC,aAAA,CAAAN,GAAA;MAAA,MAAAV,MAAA,GAAAe,MAAA,CAAAb,SAAA;MAAA,MAAAe,SAAA,GAAAF,MAAA,CAAAG,MAAA;MAAA,MAAAC,MAAA,GAAFnC,EAAE,CAAAiB,aAAA;MAAA,MAAAmB,YAAA,GAAFpC,EAAE,CAAAqC,WAAA;MAAA,OAAFrC,EAAE,CAAAsC,WAAA,CA0iDmlDH,MAAA,CAAAI,YAAA,CAAAvB,MAAA,EAAAoB,YAAA,EAAAH,SAAmC,CAAC;IAAA,CAAC,CAAC,4BAAAO,0DAAAC,MAAA;MAAA,MAAAR,SAAA,GA1iD3nDjC,EAAE,CAAAgC,aAAA,CAAAN,GAAA,EAAAQ,MAAA;MAAA,MAAAC,MAAA,GAAFnC,EAAE,CAAAiB,aAAA;MAAA,OAAFjB,EAAE,CAAAsC,WAAA,CA0iDspDH,MAAA,CAAAO,gBAAA,CAAAD,MAAA,EAAAR,SAA+B,CAAC;IAAA,CAAC,CAAC;IA1iD1rDjC,EAAE,CAAA2C,SAAA,aA0iDuuD,CAAC,YAAmT,CAAC;IA1iD9hE3C,EAAE,CAAA4B,cAAA,cA0iDskE,CAAC,cAA6C,CAAC;IA1iDvnE5B,EAAE,CAAAe,UAAA,IAAAD,yCAAA,gBA0iDo7E,CAAC,IAAAO,yCAAA,MAAqG,CAAC;IA1iD7hFrB,EAAE,CAAA4C,YAAA,CA0iD6jF,CAAC,CAAc,CAAC,CAAW,CAAC;EAAA;EAAA,IAAAnC,EAAA;IAAA,MAAAO,MAAA,GAAAN,GAAA,CAAAQ,SAAA;IAAA,MAAAe,SAAA,GAAAvB,GAAA,CAAAwB,MAAA;IAAA,MAAAW,UAAA,GA1iD3lF7C,EAAE,CAAAqC,WAAA;IAAA,MAAAF,MAAA,GAAFnC,EAAE,CAAAiB,aAAA;IAAFjB,EAAE,CAAA8C,UAAA,CAAA9B,MAAA,CAAA+B,UA0iDo+C,CAAC;IA1iDv+C/C,EAAE,CAAAgD,WAAA,oBAAAb,MAAA,CAAAc,aAAA,KAAAhB,SA0iDg8C,CAAC;IA1iDn8CjC,EAAE,CAAAmB,UAAA,OAAAgB,MAAA,CAAAe,cAAA,CAAAlC,MAAA,EAAAiB,SAAA,CA0iDm+B,CAAC,aAAAjB,MAAA,CAAAmC,QAAqiB,CAAC,uBAAAhB,MAAA,CAAAiB,kBAAoD,CAAC;IA1iDjkDpD,EAAE,CAAAqD,WAAA,aAAAlB,MAAA,CAAAmB,YAAA,CAAArB,SAAA,oBAAAA,SAAA,sBAAAE,MAAA,CAAAoB,KAAA,CAAAC,MAAA,mBAAArB,MAAA,CAAAsB,gBAAA,CAAAxB,SAAA,oBAAAE,MAAA,CAAAc,aAAA,KAAAhB,SAAA,gBAAAjB,MAAA,CAAA0C,SAAA,8BAAA1C,MAAA,CAAA0C,SAAA,IAAA1C,MAAA,CAAA2C,cAAA,GAAA3C,MAAA,CAAA2C,cAAA;IAAF3D,EAAE,CAAA4D,SAAA,EA0iDq9D,CAAC;IA1iDx9D5D,EAAE,CAAAmB,UAAA,qBAAA0B,UA0iDq9D,CAAC,sBAAA7B,MAAA,CAAAmC,QAAA,IAAAhB,MAAA,CAAA0B,aAA8D,CAAC;IA1iDvhE7D,EAAE,CAAA4D,SAAA,EA0iD4iF,CAAC;IA1iD/iF5D,EAAE,CAAA8D,aAAA,CAAA9C,MAAA,CAAAI,aAAA,QA0iD4iF,CAAC;EAAA;AAAA;AAAA,SAAA2C,oCAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1iD/iFT,EAAE,CAAAC,YAAA,EA0iDs8F,CAAC;EAAA;AAAA;AAAA,SAAA+D,4BAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwD,GAAA,GA1iDz8FjE,EAAE,CAAA2B,gBAAA;IAAF3B,EAAE,CAAA4B,cAAA,sBA0iD24H,CAAC;IA1iD94H5B,EAAE,CAAA6B,UAAA,yBAAAqC,gEAAA;MAAFlE,EAAE,CAAAgC,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAFnC,EAAE,CAAAiB,aAAA;MAAA,OAAFjB,EAAE,CAAAsC,WAAA,CA0iDsuHH,MAAA,CAAAgC,2BAAA,CAA4B,CAAC;IAAA,CAAC,CAAC,0BAAAC,iEAAA3B,MAAA;MA1iDvwHzC,EAAE,CAAAgC,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAFnC,EAAE,CAAAiB,aAAA;MAAA,OAAFjB,EAAE,CAAAsC,WAAA,CA0iDyyHH,MAAA,CAAAkC,wBAAA,CAAA5B,MAA+B,CAAC;IAAA,CAAC,CAAC,8BAAA6B,qEAAA7B,MAAA;MA1iD70HzC,EAAE,CAAAgC,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAFnC,EAAE,CAAAiB,aAAA;MAAA,OAAFjB,EAAE,CAAAsC,WAAA,CA0iDm3HH,MAAA,CAAAoC,aAAA,CAAA9B,MAAoB,CAAC;IAAA,CAAC,CAAC;IA1iD54HzC,EAAE,CAAA4C,YAAA,CA0iD24H,CAAC;EAAA;EAAA,IAAAnC,EAAA;IAAA,MAAA+D,OAAA,GAAA9D,GAAA,CAAAQ,SAAA;IAAA,MAAAuD,UAAA,GAAA/D,GAAA,CAAAwB,MAAA;IAAA,MAAAC,MAAA,GA1iD94HnC,EAAE,CAAAiB,aAAA;IAAFjB,EAAE,CAAA8C,UAAA,CAAA0B,OAAA,CAAAE,SA0iDk/G,CAAC;IA1iDr/G1E,EAAE,CAAAmB,UAAA,OAAAgB,MAAA,CAAAsB,gBAAA,CAAAgB,UAAA,CA0iDssG,CAAC,YAAAD,OAAA,CAAAG,OAAwV,CAAC,aAAAH,OAAA,CAAAI,QAA8C,CAAC,sBAAAzC,MAAA,CAAA0C,iBAA2D,CAAC,oBAAA1C,MAAA,CAAA2C,eAAuD,CAAC;IA1iDrsH9E,EAAE,CAAAqD,WAAA,aAAAlB,MAAA,CAAA4C,eAAA,YAAA5C,MAAA,CAAAc,aAAA,KAAAwB,UAAA,GAAAtC,MAAA,CAAA4C,eAAA,4BAAA5C,MAAA,CAAAe,cAAA,CAAAsB,OAAA,EAAAC,UAAA,kBAAAtC,MAAA,CAAAc,aAAA,KAAAwB,UAAA;EAAA;AAAA;AAAA,MAAAO,GAAA;AAAA,MAAAC,IAAA;AA/BnF,SAASC,eAAe,EAAEC,YAAY,EAAEC,eAAe,EAAEC,YAAY,QAAQ,mBAAmB;AAChG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,QAAQ,EAAEC,qBAAqB,QAAQ,uBAAuB;AACvE,SAASC,aAAa,EAAEC,aAAa,QAAQ,wBAAwB;AACrE,OAAO,KAAK9F,EAAE,MAAM,eAAe;AACnC,SAAS+F,cAAc,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,YAAY,EAAEC,SAAS,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,EAAEC,MAAM,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,QAAQ,QAAQ,eAAe;AAC1b,SAASC,OAAO,EAAEC,EAAE,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,YAAY,EAAEC,eAAe,QAAQ,MAAM;AAClG,SAASC,YAAY,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,gBAAgB;AAC5F,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,QAAQ,qBAAqB;AAChF,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,yBAAyB,QAAQ,uBAAuB;AACtF,SAASH,CAAC,IAAII,eAAe,QAAQ,8BAA8B;AACnE,OAAO,uBAAuB;;AAE9B;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAIxD,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA,MAAMyD,aAAa,CAAC;EAEhBC,WAAWA,CAAA,EAAG;IAAAC,eAAA,mBADH1D,MAAM,CAACC,WAAW,CAAC;EACd;AAGpB;AAAC0D,cAAA,GALKH,aAAa;AAAAE,eAAA,CAAbF,aAAa,wBAAAI,uBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAGoFL,cAAa;AAAA;AAAAE,eAAA,CAH9GF,aAAa,8BAM8DxJ,EAAE,CAAA8J,iBAAA;EAAAC,IAAA,EAFQP,cAAa;EAAAQ,SAAA;EAAAC,QAAA,GAEvBjK,EAAE,CAAAkK,kBAAA,CAFmF,CAAC;IAAEC,OAAO,EAAEZ,eAAe;IAAEa,WAAW,EAAEZ;EAAc,CAAC,CAAC;AAAA;AAEhO;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAAiFrK,EAAE,CAAAsK,iBAAA,CAAQd,aAAa,EAAc,CAAC;IAC3GO,IAAI,EAAE7D,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEZ,eAAe;QAAEa,WAAW,EAAEZ;MAAc,CAAC;IACxE,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA,MAAMkB,aAAa,GAAG,IAAI3E,cAAc,CAAC,aAAa,CAAC;AACvD;AACA;AACA;AACA;AACA,MAAM4E,OAAO,GAAG,IAAI5E,cAAc,CAAC,SAAS,CAAC;AAC7C;AACA,MAAM6E,WAAW,SAASjC,SAAS,CAAC;EAAAc,YAAA,GAAAc,IAAA;IAAA,SAAAA,IAAA;IAAAb,eAAA,sBAClB1D,MAAM,CAAC2E,OAAO,EAAE;MAAEE,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAA;AAGrD;AAACC,YAAA,GAJKF,WAAW;AAAAlB,eAAA,CAAXkB,WAAW;EAAA,IAAAG,yBAAA;EAAA,gBAAAC,qBAAAnB,iBAAA;IAAA,QAAAkB,yBAAA,KAAAA,yBAAA,GApBgE/K,EAAE,CAAAiL,qBAAA,CAsBoBL,YAAW,IAAAf,iBAAA,IAAXe,YAAW;EAAA;AAAA;AAAAlB,eAAA,CAF5GkB,WAAW,8BApBgE5K,EAAE,CAAA8J,iBAAA;EAAAC,IAAA,EAuBQa,YAAW;EAAAZ,SAAA;EAAAC,QAAA,GAvBrBjK,EAAE,CAAAkK,kBAAA,CAuBgG,CAAC;IAAEC,OAAO,EAAEO,aAAa;IAAEN,WAAW,EAAEQ;EAAY,CAAC,CAAC,GAvBxJ5K,EAAE,CAAAkL,0BAAA;AAAA;AAyBnF;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KAzBiFrK,EAAE,CAAAsK,iBAAA,CAyBQM,WAAW,EAAc,CAAC;IACzGb,IAAI,EAAE7D,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gCAAgC;MAC1CC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEO,aAAa;QAAEN,WAAW,EAAEQ;MAAY,CAAC;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMO,aAAa,GAAG,IAAIpF,cAAc,CAAC,eAAe,CAAC;AACzD,MAAMqF,MAAM,CAAC;EAKT;EACA,IAAIhK,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACiK,cAAc;EAC9B;EACA,IAAIjK,aAAaA,CAACkK,KAAK,EAAE;IACrB,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;EACtC;EA4BA;EACA,IAAI3G,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC6G,cAAc;EAC9B;EACA;;EAiBA/B,WAAWA,CAAA,EAAG;IAAAC,eAAA,4BA3DM1D,MAAM,CAACG,gBAAgB,CAAC;IAAAuD,eAAA,2BACzB1D,MAAM,CAACmF,aAAa,EAAE;MAAEN,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC5D;IAAAnB,eAAA,mBACW,KAAK;IAAAA,eAAA;IAShB;AACJ;AACA;IAFIA,eAAA,2BAGmB+B,SAAS;IAC5B;IAAA/B,eAAA;IAEA;IAAAA,eAAA,oBACY,EAAE;IACd;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;AACJ;AACA;AACA;IAHIA,eAAA,aAIK,IAAI;IACT;IAAAA,eAAA,yBACiB,IAAI;IAAAA,eAAA,wBAML,IAAI7B,OAAO,CAAC,CAAC;IAC7B;AACJ;AACA;AACA;IAHI6B,eAAA,mBAIW,IAAI;IACf;IACA;AACJ;AACA;AACA;IAHIA,eAAA,iBAIS,IAAI;IACb;AACJ;AACA;IAFIA,eAAA,mBAGW,KAAK;IAEZ1D,MAAM,CAAC8C,sBAAsB,CAAC,CAAC4C,IAAI,CAAC1C,uBAAuB,CAAC;EAChE;EACA2C,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACC,cAAc,CAAC,WAAW,CAAC,IAAID,OAAO,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;MAC3E,IAAI,CAACC,aAAa,CAACC,IAAI,CAAC,CAAC;IAC7B;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,aAAa,CAACG,QAAQ,CAAC,CAAC;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACV,cAAc,GAAG,IAAI5C,cAAc,CAAC,IAAI,CAACuD,gBAAgB,IAAI,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,iBAAiB,CAAC;EACpH;EACA;AACJ;AACA;AACA;AACA;AACA;EACId,sBAAsBA,CAACD,KAAK,EAAE;IAC1B;IACA;IACA;IACA;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACgB,WAAW,KAAK,IAAI,EAAE;MACrC,IAAI,CAACjB,cAAc,GAAGC,KAAK;IAC/B;EACJ;AAGJ;AAACiB,OAAA,GA3FKnB,MAAM;AAAA1B,eAAA,CAAN0B,MAAM,wBAAAoB,gBAAA3C,iBAAA;EAAA,YAAAA,iBAAA,IAyF2FuB,OAAM;AAAA;AAAA1B,eAAA,CAzFvG0B,MAAM,8BAtCqEpL,EAAE,CAAAyM,iBAAA;EAAA1C,IAAA,EAgIQqB,OAAM;EAAApB,SAAA;EAAA0C,cAAA,WAAAC,uBAAAlM,EAAA,EAAAC,GAAA,EAAAkM,QAAA;IAAA,IAAAnM,EAAA;MAhIhBT,EAAE,CAAA6M,cAAA,CAAAD,QAAA,EAgI+fhC,WAAW;MAhI5gB5K,EAAE,CAAA6M,cAAA,CAAAD,QAAA,EAgI6lBpD,aAAa,KAA2BvD,WAAW;IAAA;IAAA,IAAAxF,EAAA;MAAA,IAAAqM,EAAA;MAhIlpB9M,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAU,aAAA,GAAA0L,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAyL,gBAAA,GAAAW,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,SAAA,WAAAC,cAAA1M,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAoN,WAAA,CAgI6uBnH,WAAW;IAAA;IAAA,IAAAxF,EAAA;MAAA,IAAAqM,EAAA;MAhI1vB9M,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAA0L,gBAAA,GAAAU,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAI,SAAA,aAgIyV,EAAE;EAAAC,QAAA;EAAAC,YAAA,WAAAC,qBAAA/M,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAhI7VT,EAAE,CAAAqD,WAAA,OAgIQ,IAAI;IAAA;EAAA;EAAAoK,MAAA;IAAAtK,QAAA,8BAA0FiD,gBAAgB;IAAA5E,SAAA;IAAAkC,SAAA;IAAAC,cAAA;IAAAZ,UAAA;IAAA2B,SAAA;IAAAgJ,EAAA;EAAA;EAAAC,QAAA;EAAA1D,QAAA,GAhIxHjK,EAAE,CAAAkK,kBAAA,CAgI+Y,CAAC;IAAEC,OAAO,EAAEQ,OAAO;IAAEP,WAAW,EAAEgB;EAAO,CAAC,CAAC,GAhI5bpL,EAAE,CAAA4N,oBAAA;EAAAC,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAAC,iBAAAzN,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAmO,eAAA;MAAFnO,EAAE,CAAAe,UAAA,IAAAqN,8BAAA,qBAgIqkC,CAAC;IAAA;EAAA;EAAAC,aAAA;AAAA;AAEzpC;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KAlIiFrK,EAAE,CAAAsK,iBAAA,CAkIQc,MAAM,EAAc,CAAC;IACpGrB,IAAI,EAAE1D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAE8D,eAAe,EAAEhI,uBAAuB,CAACiI,OAAO;MAAEF,aAAa,EAAE9H,iBAAiB,CAACiI,IAAI;MAAEb,QAAQ,EAAE,QAAQ;MAAElD,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEQ,OAAO;QAAEP,WAAW,EAAEgB;MAAO,CAAC,CAAC;MAAEqD,IAAI,EAAE;QAC7L;QACA;QACA,QAAQ,EAAE,EAAE;QACZ;QACA,WAAW,EAAE;MACjB,CAAC;MAAER,QAAQ,EAAE;IAAgR,CAAC;EAC1S,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE9K,QAAQ,EAAE,CAAC;MACnD4G,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEhF,aAAa,EAAE,CAAC;MAChB2I,IAAI,EAAEtD,YAAY;MAClB8D,IAAI,EAAE,CAACK,WAAW;IACtB,CAAC,CAAC;IAAEuB,gBAAgB,EAAE,CAAC;MACnBpC,IAAI,EAAEtD,YAAY;MAClB8D,IAAI,EAAE,CAACf,aAAa,EAAE;QAAEmF,IAAI,EAAE1I,WAAW;QAAE2I,MAAM,EAAE;MAAK,CAAC;IAC7D,CAAC,CAAC;IAAExC,gBAAgB,EAAE,CAAC;MACnBrC,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAACtE,WAAW,EAAE;QAAE2I,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEpN,SAAS,EAAE,CAAC;MACZuI,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE7G,SAAS,EAAE,CAAC;MACZqG,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE5G,cAAc,EAAE,CAAC;MACjBoG,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAExH,UAAU,EAAE,CAAC;MACbgH,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE9B,SAAS,EAAE,CAAC;MACZqF,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAEkH,EAAE,EAAE,CAAC;MACL3D,IAAI,EAAEvD;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMqI,YAAY,GAAG,2BAA2B;AAChD;AACA,MAAMC,mBAAmB,GAAG,kCAAkC;AAC9D;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EAIZtF,WAAWA,CAACuF,MAAM,EAAE;IAAAtF,eAAA;IAFpB;IAAAA,eAAA;IAGI,IAAI,CAACsF,MAAM,GAAGA,MAAM;EACxB;EACA;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACD,MAAM,CAACE,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC;IACpD,IAAI,CAACC,YAAY,GAAG5D,SAAS;EACjC;EACA;EACA6D,cAAcA,CAACC,OAAO,EAAE;IACpB,MAAMC,iBAAiB,GAAG,IAAI,CAACR,MAAM,CAACS,IAAI,CAACN,IAAI,IAAIA,IAAI,CAACO,UAAU,CAACC,aAAa,KAAKJ,OAAO,CAAC;IAC7F,MAAMK,WAAW,GAAG,IAAI,CAACP,YAAY;IACrC,IAAIG,iBAAiB,KAAKI,WAAW,EAAE;MACnC;IACJ;IACAA,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAER,gBAAgB,CAAC,CAAC;IAC/B,IAAII,iBAAiB,EAAE;MAAA,IAAAK,qBAAA,EAAAC,sBAAA;MACnB,MAAMC,OAAO,GAAGH,WAAW,aAAXA,WAAW,gBAAAC,qBAAA,GAAX,CAAAC,sBAAA,GAAAF,WAAW,CAAEF,UAAU,CAACC,aAAa,EAACK,qBAAqB,cAAAH,qBAAA,uBAA3DA,qBAAA,CAAAI,IAAA,CAAAH,sBAA8D,CAAC;MAC/E;MACAN,iBAAiB,CAACU,cAAc,CAACH,OAAO,CAAC;MACzC,IAAI,CAACV,YAAY,GAAGG,iBAAiB;IACzC;EACJ;AACJ;AACA,MAAMW,UAAU,CAAC;EAAA1G,YAAA;IAAAC,eAAA,sBACC1D,MAAM,CAACW,UAAU,CAAC;IAAA+C,eAAA;IAAAA,eAAA;IAAAA,eAAA,wBAGhB,KAAK;EAAA;EACrB;EACA,IAAItG,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACgN,aAAa;EAC7B;EACA,IAAIhN,kBAAkBA,CAACiN,QAAQ,EAAE;IAC7B,IAAI,IAAI,CAACD,aAAa,KAAKC,QAAQ,EAAE;MACjC,IAAI,CAACD,aAAa,GAAGC,QAAQ;MAC7B,IAAI,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC/B;IACJ;EACJ;EACA;EACAL,cAAcA,CAACM,2BAA2B,EAAE;IACxC,MAAMjB,OAAO,GAAG,IAAI,CAACkB,WAAW,CAACd,aAAa;IAC9C;IACA;IACA,IAAI,CAACa,2BAA2B,IAC5B,CAACjB,OAAO,CAACS,qBAAqB,IAC9B,CAAC,IAAI,CAACU,qBAAqB,EAAE;MAC7BnB,OAAO,CAACoB,SAAS,CAACC,GAAG,CAAC/B,YAAY,CAAC;MACnC;IACJ;IACA;IACA;IACA;IACA,MAAMgC,iBAAiB,GAAGtB,OAAO,CAACS,qBAAqB,CAAC,CAAC;IACzD,MAAMc,UAAU,GAAGN,2BAA2B,CAACO,KAAK,GAAGF,iBAAiB,CAACE,KAAK;IAC9E,MAAMC,SAAS,GAAGR,2BAA2B,CAACS,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;IAC3E1B,OAAO,CAACoB,SAAS,CAACC,GAAG,CAAC9B,mBAAmB,CAAC;IAC1C,IAAI,CAAC4B,qBAAqB,CAACQ,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,cAAcH,SAAS,cAAcF,UAAU,GAAG,CAAC;IAC7G;IACAvB,OAAO,CAACS,qBAAqB,CAAC,CAAC;IAC/BT,OAAO,CAACoB,SAAS,CAACS,MAAM,CAACtC,mBAAmB,CAAC;IAC7CS,OAAO,CAACoB,SAAS,CAACC,GAAG,CAAC/B,YAAY,CAAC;IACnC,IAAI,CAAC6B,qBAAqB,CAACQ,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC;EACjE;EACA;EACA/B,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACqB,WAAW,CAACd,aAAa,CAACgB,SAAS,CAACS,MAAM,CAACvC,YAAY,CAAC;EACjE;EACA;EACA3C,QAAQA,CAAA,EAAG;IACP,IAAI,CAACmF,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACArF,WAAWA,CAAA,EAAG;IAAA,IAAAsF,oBAAA;IACV,CAAAA,oBAAA,OAAI,CAAChB,cAAc,cAAAgB,oBAAA,eAAnBA,oBAAA,CAAqBF,MAAM,CAAC,CAAC;IAC7B,IAAI,CAACd,cAAc,GAAG,IAAI,CAACI,qBAAqB,GAAG,IAAI;EAC3D;EACA;EACAW,oBAAoBA,CAAA,EAAG;IACnB,MAAME,YAAY,GAAG,IAAI,CAACd,WAAW,CAACd,aAAa,CAAC6B,aAAa,IAAIC,QAAQ;IAC7E,MAAMC,aAAa,GAAI,IAAI,CAACpB,cAAc,GAAGiB,YAAY,CAACI,aAAa,CAAC,MAAM,CAAE;IAChF,MAAMC,oBAAoB,GAAI,IAAI,CAAClB,qBAAqB,GAAGa,YAAY,CAACI,aAAa,CAAC,MAAM,CAAE;IAC9FD,aAAa,CAACG,SAAS,GAAG,mBAAmB;IAC7CD,oBAAoB,CAACC,SAAS,GAC1B,kEAAkE;IACtEH,aAAa,CAACI,WAAW,CAAC,IAAI,CAACpB,qBAAqB,CAAC;IACrD,IAAI,CAACH,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIA,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACD,cAAc,KAAK,OAAOjG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzE,MAAM0H,KAAK,CAAC,6DAA6D,CAAC;IAC9E;IACA,MAAMC,aAAa,GAAG,IAAI,CAAC5B,aAAa,GAClC,IAAI,CAACK,WAAW,CAACd,aAAa,CAACsC,aAAa,CAAC,mBAAmB,CAAC,GACjE,IAAI,CAACxB,WAAW,CAACd,aAAa;IACpC,IAAI,CAACqC,aAAa,KAAK,OAAO3H,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAM0H,KAAK,CAAC,qCAAqC,CAAC;IACtD;IACAC,aAAa,CAACF,WAAW,CAAC,IAAI,CAACxB,cAAc,CAAC;EAClD;AAGJ;AAAC4B,WAAA,GApFK/B,UAAU;AAAAzG,eAAA,CAAVyG,UAAU,wBAAAgC,oBAAAtI,iBAAA;EAAA,YAAAA,iBAAA,IAkFuFsG,WAAU;AAAA;AAAAzG,eAAA,CAlF3GyG,UAAU,8BA5MiEnQ,EAAE,CAAA8J,iBAAA;EAAAC,IAAA,EA+RQoG,WAAU;EAAA1C,MAAA;IAAArK,kBAAA,kDAAiGgD,gBAAgB;EAAA;AAAA;AAEtN;EAAA,QAAAiE,SAAA,oBAAAA,SAAA,KAjSiFrK,EAAE,CAAAsK,iBAAA,CAiSQ6F,UAAU,EAAc,CAAC;IACxGpG,IAAI,EAAE7D;EACV,CAAC,CAAC,QAAkB;IAAE9C,kBAAkB,EAAE,CAAC;MACnC2G,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,SAASgM,+BAA+BA,CAAA,EAAG;EACvC,MAAMC,MAAM,GAAI9C,OAAO,KAAM;IACzB0B,IAAI,EAAE1B,OAAO,GAAG,CAACA,OAAO,CAAC+C,UAAU,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG;IACtDvB,KAAK,EAAExB,OAAO,GAAG,CAACA,OAAO,CAACgD,WAAW,IAAI,CAAC,IAAI,IAAI,GAAG;EACzD,CAAC,CAAC;EACF,OAAOF,MAAM;AACjB;AACA;AACA,MAAMG,uBAAuB,GAAG,IAAIzM,cAAc,CAAC,qBAAqB,EAAE;EACtE0M,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEN;AACb,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMO,kBAAkB,SAASxC,UAAU,CAAC;EAAA1G,YAAA,GAAAc,IAAA;IAAA,SAAAA,IAAA;IAAAb,eAAA,qBAC3B1D,MAAM,CAACW,UAAU,CAAC;IAC/B;IAAA+C,eAAA,mBACW,KAAK;EAAA;EAChB;EACAkJ,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAClD,UAAU,CAACC,aAAa,CAACiD,KAAK,CAAC,CAAC;EACzC;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnD,UAAU,CAACC,aAAa,CAAC2C,UAAU;EACnD;EACAQ,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACpD,UAAU,CAACC,aAAa,CAAC4C,WAAW;EACpD;AAGJ;AAACQ,mBAAA,GAhBKJ,kBAAkB;AAAAjJ,eAAA,CAAlBiJ,kBAAkB;EAAA,IAAAK,gCAAA;EAAA,gBAAAC,4BAAApJ,iBAAA;IAAA,QAAAmJ,gCAAA,KAAAA,gCAAA,GA9TyDhT,EAAE,CAAAiL,qBAAA,CA4UoB0H,mBAAkB,IAAA9I,iBAAA,IAAlB8I,mBAAkB;EAAA;AAAA;AAAAjJ,eAAA,CAdnHiJ,kBAAkB,8BA9TyD3S,EAAE,CAAA8J,iBAAA;EAAAC,IAAA,EA6UQ4I,mBAAkB;EAAA3I,SAAA;EAAAsD,QAAA;EAAAC,YAAA,WAAA2F,iCAAAzS,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA7U5BT,EAAE,CAAAqD,WAAA,oBAAA3C,GAAA,CAAAyC,QAAA;MAAFnD,EAAE,CAAAgD,WAAA,yBAAAtC,GAAA,CAAAyC,QA6UyB,CAAC;IAAA;EAAA;EAAAsK,MAAA;IAAAtK,QAAA,8BAAqGiD,gBAAgB;EAAA;EAAA6D,QAAA,GA7UjJjK,EAAE,CAAAkL,0BAAA;AAAA;AA+UnF;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KA/UiFrK,EAAE,CAAAsK,iBAAA,CA+UQqI,kBAAkB,EAAc,CAAC;IAChH5I,IAAI,EAAE7D,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCiE,IAAI,EAAE;QACF,8BAA8B,EAAE,UAAU;QAC1C,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEtL,QAAQ,EAAE,CAAC;MACzB4G,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM+M,2BAA2B,GAAG;EAChCC,OAAO,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAG;AAC/B;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,GAAG;AAClC;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EA0CxB;EACA,IAAItQ,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACuQ,cAAc;EAC9B;EACA,IAAIvQ,aAAaA,CAACwQ,CAAC,EAAE;IACjB,MAAMnI,KAAK,GAAGoI,KAAK,CAACD,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC;IAC9B,IAAI,IAAI,CAACD,cAAc,IAAIlI,KAAK,EAAE;MAC9B,IAAI,CAACqI,qBAAqB,GAAG,IAAI;MACjC,IAAI,CAACH,cAAc,GAAGlI,KAAK;MAC3B,IAAI,IAAI,CAACsI,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACC,gBAAgB,CAACvI,KAAK,CAAC;MAC5C;IACJ;EACJ;EAMA7B,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBA5DA1D,MAAM,CAACW,UAAU,CAAC;IAAA+C,eAAA,6BACX1D,MAAM,CAACY,iBAAiB,CAAC;IAAA8C,eAAA,yBAC7B1D,MAAM,CAACH,aAAa,CAAC;IAAA6D,eAAA,eAC/B1D,MAAM,CAACV,cAAc,EAAE;MAAEuF,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAnB,eAAA,kBACvC1D,MAAM,CAACa,MAAM,CAAC;IAAA6C,eAAA,oBACZ1D,MAAM,CAACL,QAAQ,CAAC;IAAA+D,eAAA,gCACJ1D,MAAM,CAACN,oBAAoB,CAAC;IAAAgE,eAAA,oBACxC1D,MAAM,CAACc,QAAQ,CAAC;IAAA4C,eAAA,oBAChB1D,MAAM,CAACe,SAAS,CAAC;IAAA2C,eAAA,yBACZ1D,MAAM,CAACgB,qBAAqB,EAAE;MAAE6D,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAnB,eAAA;IAElE;IAAAA,eAAA,0BACkB,CAAC;IACnB;IAAAA,eAAA,gCACwB,KAAK;IAC7B;IAAAA,eAAA,qBACa,IAAI7B,OAAO,CAAC,CAAC;IAC1B;IAAA6B,eAAA,kCAC0B,KAAK;IAC/B;IAAAA,eAAA,8BACsB,IAAI;IAC1B;IAAAA,eAAA,+BACuB,IAAI;IAC3B;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,yBACiB,IAAI7B,OAAO,CAAC,CAAC;IAC9B;AACJ;AACA;AACA;IAHI6B,eAAA,4BAIoB,KAAK;IAAAA,eAAA,yBAeR,CAAC;IAClB;IAAAA,eAAA,6BACqB,IAAIzC,YAAY,CAAC,CAAC;IACvC;IAAAyC,eAAA,uBACe,IAAIzC,YAAY,CAAC,CAAC;IAE7B;IACA,IAAI,CAAC6M,cAAc,GAAG,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,MAAM,CACvD,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,IAAI,CAACzD,WAAW,CAACd,aAAa,EAAE,YAAY,EAAE,MAAM,IAAI,CAACwE,aAAa,CAAC,CAAC,CAAC,CAClG,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAACN,cAAc,CAACO,IAAI,CAACzO,qBAAqB,CAAC,IAAI,CAACqO,SAAS,EAAE,IAAI,CAACK,kBAAkB,CAAC3E,aAAa,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC4E,qBAAqB,CAAC,QAAQ,CAAC,EAAEpB,2BAA2B,CAAC,EAAEvN,qBAAqB,CAAC,IAAI,CAACqO,SAAS,EAAE,IAAI,CAACO,cAAc,CAAC7E,aAAa,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC4E,qBAAqB,CAAC,OAAO,CAAC,EAAEpB,2BAA2B,CAAC,CAAC;EACjW;EACAsB,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,SAAS,GAAG,IAAI,CAACC,IAAI,GAAG,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG9M,EAAE,CAAC,KAAK,CAAC;IAC1D;IACA;IACA;IACA;IACA,MAAM+M,MAAM,GAAG,IAAI,CAACC,qBAAqB,CACpCC,OAAO,CAAC,IAAI,CAACtE,WAAW,CAACd,aAAa,CAAC,CACvCqF,IAAI,CAAC3M,YAAY,CAAC,EAAE,CAAC,EAAEC,SAAS,CAAC,IAAI,CAAC2M,UAAU,CAAC,CAAC;IACvD;IACA;IACA;IACA,MAAMC,cAAc,GAAG,IAAI,CAACC,cAAc,CAACP,MAAM,CAAC,GAAG,CAAC,CAACI,IAAI,CAAC1M,SAAS,CAAC,IAAI,CAAC2M,UAAU,CAAC,CAAC;IACvF,MAAMG,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpC,CAAC;IACD,IAAI,CAAC1B,WAAW,GAAG,IAAI1O,eAAe,CAAC,IAAI,CAAC8J,MAAM,CAAC,CAC9CuG,yBAAyB,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC,CACrDC,cAAc,CAAC,CAAC,CAChBC,QAAQ,CAAC;IACV;IAAA,CACCC,aAAa,CAAC,MAAM,KAAK,CAAC;IAC/B;IACA;IACA,IAAI,CAAC/B,WAAW,CAACC,gBAAgB,CAAC+B,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrC,cAAc,EAAE,CAAC,CAAC,CAAC;IACnE;IACA;IACA;IACAtM,eAAe,CAACkO,OAAO,EAAE;MAAEU,QAAQ,EAAE,IAAI,CAACC;IAAU,CAAC,CAAC;IACtD;IACA;IACAhO,KAAK,CAAC2M,SAAS,EAAEQ,cAAc,EAAEL,MAAM,EAAE,IAAI,CAAC7F,MAAM,CAACpD,OAAO,EAAE,IAAI,CAACoK,aAAa,CAAC,CAAC,CAAC,CAC9EhB,IAAI,CAAC1M,SAAS,CAAC,IAAI,CAAC2M,UAAU,CAAC,CAAC,CAChCgB,SAAS,CAAC,MAAM;MAAA,IAAAC,iBAAA;MACjB;MACA;MACA;MACA,IAAI,CAACnC,OAAO,CAACoC,GAAG,CAAC,MAAM;QACnBC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB;UACA,IAAI,CAACC,eAAe,GAAGX,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACY,GAAG,CAAC,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACF,eAAe,CAAC,CAAC;UAChGnB,OAAO,CAAC,CAAC;QACb,CAAC,CAAC;MACN,CAAC,CAAC;MACF,CAAAc,iBAAA,OAAI,CAACtC,WAAW,cAAAsC,iBAAA,eAAhBA,iBAAA,CAAkBX,yBAAyB,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAAC5B,WAAW,CAACgB,MAAM,CAACqB,SAAS,CAACS,aAAa,IAAI;MAC/C,IAAI,CAACC,YAAY,CAACC,IAAI,CAACF,aAAa,CAAC;MACrC,IAAI,CAACG,YAAY,CAACH,aAAa,CAAC;IACpC,CAAC,CAAC;EACN;EACA;EACAV,aAAaA,CAAA,EAAG;IACZ,IAAI,OAAOc,cAAc,KAAK,UAAU,EAAE;MACtC,OAAO9O,KAAK;IAChB;IACA,OAAO,IAAI,CAACgH,MAAM,CAACpD,OAAO,CAACoJ,IAAI,CAACzM,SAAS,CAAC,IAAI,CAACyG,MAAM,CAAC,EAAExG,SAAS,CAAEuO,QAAQ,IAAK,IAAI9O,UAAU,CAAE+O,QAAQ,IAAK,IAAI,CAACjD,OAAO,CAACC,iBAAiB,CAAC,MAAM;MAC9I,MAAMiD,cAAc,GAAG,IAAIH,cAAc,CAACI,OAAO,IAAIF,QAAQ,CAACjL,IAAI,CAACmL,OAAO,CAAC,CAAC;MAC5EH,QAAQ,CAAC7H,OAAO,CAACC,IAAI,IAAI8H,cAAc,CAAClC,OAAO,CAAC5F,IAAI,CAACO,UAAU,CAACC,aAAa,CAAC,CAAC;MAC/E,OAAO,MAAM;QACTsH,cAAc,CAACE,UAAU,CAAC,CAAC;MAC/B,CAAC;IACL,CAAC,CAAC,CAAC,CAAC;IACJ;IACA;IACA1O,IAAI,CAAC,CAAC,CAAC;IACP;IACA;IACAC,MAAM,CAACwO,OAAO,IAAIA,OAAO,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAACvG,KAAK,GAAG,CAAC,IAAIsG,CAAC,CAACC,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9F;EACAC,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACC,cAAc,IAAI,IAAI,CAACzI,MAAM,CAACxL,MAAM,EAAE;MAC3C,IAAI,CAAC6R,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACoC,cAAc,GAAG,IAAI,CAACzI,MAAM,CAACxL,MAAM;MACxC,IAAI,CAACkU,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAAChE,qBAAqB,EAAE;MAC5B,IAAI,CAACiE,cAAc,CAAC,IAAI,CAACpE,cAAc,CAAC;MACxC,IAAI,CAACqE,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACvC,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAAC3B,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAAC+D,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAACG,sBAAsB,EAAE;MAC7B,IAAI,CAACC,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACD,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACJ,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA3L,WAAWA,CAAA,EAAG;IAAA,IAAAgM,kBAAA;IACV,IAAI,CAAClE,cAAc,CAAC5E,OAAO,CAAC+I,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IACjD,CAAAD,kBAAA,OAAI,CAACpE,WAAW,cAAAoE,kBAAA,eAAhBA,kBAAA,CAAkBE,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACjD,UAAU,CAAClJ,IAAI,CAAC,CAAC;IACtB,IAAI,CAACkJ,UAAU,CAAChJ,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACkM,cAAc,CAAClM,QAAQ,CAAC,CAAC;EAClC;EACA;EACAmM,cAAcA,CAACC,KAAK,EAAE;IAAA,IAAAC,kBAAA;IAClB;IACA,IAAI/S,cAAc,CAAC8S,KAAK,CAAC,EAAE;MACvB;IACJ;IACA,QAAQA,KAAK,CAACE,OAAO;MACjB,KAAK9S,KAAK;MACV,KAAKD,KAAK;QACN,IAAI,IAAI,CAACgT,UAAU,KAAK,IAAI,CAACvV,aAAa,EAAE;UACxC,MAAMkM,IAAI,GAAG,IAAI,CAACH,MAAM,CAACyJ,GAAG,CAAC,IAAI,CAACD,UAAU,CAAC;UAC7C,IAAIrJ,IAAI,IAAI,CAACA,IAAI,CAAChM,QAAQ,EAAE;YACxB,IAAI,CAACuV,kBAAkB,CAAC9B,IAAI,CAAC,IAAI,CAAC4B,UAAU,CAAC;YAC7C,IAAI,CAACG,aAAa,CAACN,KAAK,CAAC;UAC7B;QACJ;QACA;MACJ;QACI,CAAAC,kBAAA,OAAI,CAAC1E,WAAW,cAAA0E,kBAAA,eAAhBA,kBAAA,CAAkBM,SAAS,CAACP,KAAK,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;EACIQ,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,WAAW,GAAG,IAAI,CAACrI,WAAW,CAACd,aAAa,CAACmJ,WAAW;IAC9D;IACA;IACA;IACA,IAAIA,WAAW,KAAK,IAAI,CAACC,mBAAmB,EAAE;MAC1C,IAAI,CAACA,mBAAmB,GAAGD,WAAW,IAAI,EAAE;MAC5C;MACA;MACA,IAAI,CAAC/E,OAAO,CAACoC,GAAG,CAAC,MAAM;QACnB,IAAI,CAACd,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAACC,yBAAyB,CAAC,CAAC;QAChC,IAAI,CAACoC,kBAAkB,CAACC,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACItC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC2D,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACnB,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACE,wBAAwB,CAAC,CAAC;EACnC;EACA;EACA,IAAIS,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC5E,WAAW,GAAG,IAAI,CAACA,WAAW,CAACqF,eAAe,GAAG,CAAC;EAClE;EACA;EACA,IAAIT,UAAUA,CAAClN,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC4N,aAAa,CAAC5N,KAAK,CAAC,IAAI,IAAI,CAACkN,UAAU,KAAKlN,KAAK,IAAI,CAAC,IAAI,CAACsI,WAAW,EAAE;MAC9E;IACJ;IACA,IAAI,CAACA,WAAW,CAACuF,aAAa,CAAC7N,KAAK,CAAC;EACzC;EACA;AACJ;AACA;AACA;EACI4N,aAAaA,CAACE,KAAK,EAAE;IACjB,OAAO,IAAI,CAACpK,MAAM,GAAG,CAAC,CAAC,IAAI,CAACA,MAAM,CAACqK,OAAO,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACIvC,YAAYA,CAACyC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACC,uBAAuB,EAAE;MAC9B,IAAI,CAAC3B,cAAc,CAAC0B,QAAQ,CAAC;IACjC;IACA,IAAI,IAAI,CAACtK,MAAM,IAAI,IAAI,CAACA,MAAM,CAACxL,MAAM,EAAE;MACnC,IAAI,CAACwL,MAAM,CAACqK,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC1G,KAAK,CAAC,CAAC;MACvC;MACA;MACA;MACA,MAAM4G,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAC9J,aAAa;MACxD,MAAM+J,GAAG,GAAG,IAAI,CAAClE,mBAAmB,CAAC,CAAC;MACtC,IAAIkE,GAAG,IAAI,KAAK,EAAE;QACdF,WAAW,CAACG,UAAU,GAAG,CAAC;MAC9B,CAAC,MACI;QACDH,WAAW,CAACG,UAAU,GAAGH,WAAW,CAACI,WAAW,GAAGJ,WAAW,CAACjH,WAAW;MAC9E;IACJ;EACJ;EACA;EACAiD,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACb,IAAI,IAAI,IAAI,CAACA,IAAI,CAACrJ,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAyM,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC8B,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAMC,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,MAAMC,UAAU,GAAG,IAAI,CAACvE,mBAAmB,CAAC,CAAC,KAAK,KAAK,GAAG,CAACsE,cAAc,GAAGA,cAAc;IAC1F;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACE,QAAQ,CAACrK,aAAa,CAACuB,KAAK,CAACxC,SAAS,GAAG,cAAckH,IAAI,CAACqE,KAAK,CAACF,UAAU,CAAC,KAAK;IACvF;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACG,SAAS,CAACC,OAAO,IAAI,IAAI,CAACD,SAAS,CAACE,IAAI,EAAE;MAC/C,IAAI,CAACX,iBAAiB,CAAC9J,aAAa,CAACgK,UAAU,GAAG,CAAC;IACvD;EACJ;EACA;EACA,IAAIG,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACvD,eAAe;EAC/B;EACA,IAAIuD,cAAcA,CAACxO,KAAK,EAAE;IACtB,IAAI,CAAC+O,SAAS,CAAC/O,KAAK,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIgP,aAAaA,CAACC,SAAS,EAAE;IACrB,MAAMC,UAAU,GAAG,IAAI,CAACf,iBAAiB,CAAC9J,aAAa,CAAC4C,WAAW;IACnE;IACA,MAAMkI,YAAY,GAAI,CAACF,SAAS,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIC,UAAU,GAAI,CAAC;IACxE,OAAO,IAAI,CAACH,SAAS,CAAC,IAAI,CAAC9D,eAAe,GAAGkE,YAAY,CAAC;EAC9D;EACA;EACAC,qBAAqBA,CAACH,SAAS,EAAE;IAC7B,IAAI,CAACpG,aAAa,CAAC,CAAC;IACpB,IAAI,CAACmG,aAAa,CAACC,SAAS,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI3C,cAAcA,CAAC+C,UAAU,EAAE;IACvB,IAAI,IAAI,CAACd,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAMe,aAAa,GAAG,IAAI,CAAC5L,MAAM,GAAG,IAAI,CAACA,MAAM,CAACqK,OAAO,CAAC,CAAC,CAACsB,UAAU,CAAC,GAAG,IAAI;IAC5E,IAAI,CAACC,aAAa,EAAE;MAChB;IACJ;IACA;IACA,MAAMJ,UAAU,GAAG,IAAI,CAACf,iBAAiB,CAAC9J,aAAa,CAAC4C,WAAW;IACnE,MAAM;MAAED,UAAU;MAAEC;IAAY,CAAC,GAAGqI,aAAa,CAAClL,UAAU,CAACC,aAAa;IAC1E,IAAIkL,cAAc,EAAEC,aAAa;IACjC,IAAI,IAAI,CAACtF,mBAAmB,CAAC,CAAC,IAAI,KAAK,EAAE;MACrCqF,cAAc,GAAGvI,UAAU;MAC3BwI,aAAa,GAAGD,cAAc,GAAGtI,WAAW;IAChD,CAAC,MACI;MACDuI,aAAa,GAAG,IAAI,CAACC,aAAa,CAACpL,aAAa,CAAC4C,WAAW,GAAGD,UAAU;MACzEuI,cAAc,GAAGC,aAAa,GAAGvI,WAAW;IAChD;IACA,MAAMyI,gBAAgB,GAAG,IAAI,CAAClB,cAAc;IAC5C,MAAMmB,eAAe,GAAG,IAAI,CAACnB,cAAc,GAAGU,UAAU;IACxD,IAAIK,cAAc,GAAGG,gBAAgB,EAAE;MACnC;MACA,IAAI,CAAClB,cAAc,IAAIkB,gBAAgB,GAAGH,cAAc;IAC5D,CAAC,MACI,IAAIC,aAAa,GAAGG,eAAe,EAAE;MACtC;MACA,IAAI,CAACnB,cAAc,IAAIlE,IAAI,CAACY,GAAG,CAACsE,aAAa,GAAGG,eAAe,EAAEJ,cAAc,GAAGG,gBAAgB,CAAC;IACvG;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIhC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACa,iBAAiB,EAAE;MACxB,IAAI,CAACN,uBAAuB,GAAG,KAAK;IACxC,CAAC,MACI;MACD,MAAMK,WAAW,GAAG,IAAI,CAACmB,aAAa,CAACpL,aAAa,CAACiK,WAAW;MAChE,MAAMsB,cAAc,GAAG,IAAI,CAACzK,WAAW,CAACd,aAAa,CAAC4C,WAAW;MACjE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM4I,SAAS,GAAGvB,WAAW,GAAGsB,cAAc,IAAI,CAAC;MACnD,IAAI,CAACC,SAAS,EAAE;QACZ,IAAI,CAACrB,cAAc,GAAG,CAAC;MAC3B;MACA,IAAIqB,SAAS,KAAK,IAAI,CAAC5B,uBAAuB,EAAE;QAC5C,IAAI,CAACA,uBAAuB,GAAG4B,SAAS;QACxC,IAAI,CAACzD,kBAAkB,CAACC,YAAY,CAAC,CAAC;MAC1C;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACgC,iBAAiB,EAAE;MACxB,IAAI,CAACuB,mBAAmB,GAAG,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAC/D,CAAC,MACI;MACD;MACA,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAACvB,cAAc,IAAI,CAAC;MACpD,IAAI,CAACsB,mBAAmB,GAAG,IAAI,CAACtB,cAAc,IAAI,IAAI,CAACrD,qBAAqB,CAAC,CAAC;MAC9E,IAAI,CAACiB,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIlB,qBAAqBA,CAAA,EAAG;IACpB,MAAM6E,eAAe,GAAG,IAAI,CAACP,aAAa,CAACpL,aAAa,CAACiK,WAAW;IACpE,MAAMY,UAAU,GAAG,IAAI,CAACf,iBAAiB,CAAC9J,aAAa,CAAC4C,WAAW;IACnE,OAAO+I,eAAe,GAAGd,UAAU,IAAI,CAAC;EAC5C;EACA;EACAlF,yBAAyBA,CAAA,EAAG;IACxB,MAAMiG,YAAY,GAAG,IAAI,CAACvM,MAAM,IAAI,IAAI,CAACA,MAAM,CAACxL,MAAM,GAAG,IAAI,CAACwL,MAAM,CAACqK,OAAO,CAAC,CAAC,CAAC,IAAI,CAACpW,aAAa,CAAC,GAAG,IAAI;IACzG,MAAMuY,oBAAoB,GAAGD,YAAY,GAAGA,YAAY,CAAC7L,UAAU,CAACC,aAAa,GAAG,IAAI;IACxF,IAAI6L,oBAAoB,EAAE;MACtB,IAAI,CAACC,OAAO,CAACnM,cAAc,CAACkM,oBAAoB,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACC,OAAO,CAACxM,IAAI,CAAC,CAAC;IACvB;EACJ;EACA;EACAkF,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACgE,cAAc,CAACpM,IAAI,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIwI,qBAAqBA,CAACgG,SAAS,EAAEmB,UAAU,EAAE;IACzC;IACA;IACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAM,IAAI,IAAI,IAAID,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACpE;IACJ;IACA;IACA,IAAI,CAACxH,aAAa,CAAC,CAAC;IACpB;IACAjM,KAAK,CAACmL,mBAAmB,EAAEC,sBAAsB;IAC7C;IAAA,CACC0B,IAAI,CAAC1M,SAAS,CAACP,KAAK,CAAC,IAAI,CAACoQ,cAAc,EAAE,IAAI,CAAClD,UAAU,CAAC,CAAC,CAAC,CAC5DgB,SAAS,CAAC,MAAM;MACjB,MAAM;QAAE2F,iBAAiB;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACvB,aAAa,CAACC,SAAS,CAAC;MACrE;MACA,IAAIsB,QAAQ,KAAK,CAAC,IAAIA,QAAQ,IAAID,iBAAiB,EAAE;QACjD,IAAI,CAACzH,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIkG,SAASA,CAACzV,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACiV,iBAAiB,EAAE;MACxB,OAAO;QAAE+B,iBAAiB,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAE,CAAC;IAChD;IACA,MAAMD,iBAAiB,GAAG,IAAI,CAACnF,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACF,eAAe,GAAGX,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACY,GAAG,CAACoF,iBAAiB,EAAEhX,QAAQ,CAAC,CAAC;IACzE;IACA;IACA,IAAI,CAACkT,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACD,uBAAuB,CAAC,CAAC;IAC9B,OAAO;MAAE+D,iBAAiB;MAAEC,QAAQ,EAAE,IAAI,CAACtF;IAAgB,CAAC;EAChE;AAGJ;AAACuF,sBAAA,GAleKvI,qBAAqB;AAAA7J,eAAA,CAArB6J,qBAAqB,wBAAAwI,+BAAAlS,iBAAA;EAAA,YAAAA,iBAAA,IAge4E0J,sBAAqB;AAAA;AAAA7J,eAAA,CAhetH6J,qBAAqB,8BA/WsDvT,EAAE,CAAA8J,iBAAA;EAAAC,IAAA,EAg1BQwJ,sBAAqB;EAAA9F,MAAA;IAAAoM,iBAAA,gDAA8FzT,gBAAgB;IAAAnD,aAAA,wCAAqDkE,eAAe;EAAA;EAAA6U,OAAA;IAAAtD,kBAAA;IAAA/B,YAAA;EAAA;AAAA;AAElS;EAAA,QAAAtM,SAAA,oBAAAA,SAAA,KAl1BiFrK,EAAE,CAAAsK,iBAAA,CAk1BQiJ,qBAAqB,EAAc,CAAC;IACnHxJ,IAAI,EAAE7D;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE2T,iBAAiB,EAAE,CAAC;MAC5D9P,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEnD,aAAa,EAAE,CAAC;MAChB8G,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEvH;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEuR,kBAAkB,EAAE,CAAC;MACrB3O,IAAI,EAAE3C;IACV,CAAC,CAAC;IAAEuP,YAAY,EAAE,CAAC;MACf5M,IAAI,EAAE3C;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6U,YAAY,SAAS1I,qBAAqB,CAAC;EAAA9J,YAAA,GAAAc,IAAA;IAAA,SAAAA,IAAA;IAAAb,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAQ7C;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,wBACgB,KAAK;EAAA;EACrB+K,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACgH,OAAO,GAAG,IAAI1M,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC,KAAK,CAACyF,kBAAkB,CAAC,CAAC;EAC9B;EACAkE,aAAaA,CAACN,KAAK,EAAE;IACjBA,KAAK,CAAC6D,cAAc,CAAC,CAAC;EAC1B;AAGJ;AAACC,aAAA,GAvBKF,YAAY;AAAAvS,eAAA,CAAZuS,YAAY;EAAA,IAAAG,0BAAA;EAAA,gBAAAC,sBAAAxS,iBAAA;IAAA,QAAAuS,0BAAA,KAAAA,0BAAA,GAv2B+Dpc,EAAE,CAAAiL,qBAAA,CA43BoBgR,aAAY,IAAApS,iBAAA,IAAZoS,aAAY;EAAA;AAAA;AAAAvS,eAAA,CArB7GuS,YAAY,8BAv2B+Djc,EAAE,CAAAyM,iBAAA;EAAA1C,IAAA,EA63BQkS,aAAY;EAAAjS,SAAA;EAAA0C,cAAA,WAAA4P,6BAAA7b,EAAA,EAAAC,GAAA,EAAAkM,QAAA;IAAA,IAAAnM,EAAA;MA73BtBT,EAAE,CAAA6M,cAAA,CAAAD,QAAA,EA63B0f+F,kBAAkB;IAAA;IAAA,IAAAlS,EAAA;MAAA,IAAAqM,EAAA;MA73B9gB9M,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAsO,MAAA,GAAAlC,EAAA;IAAA;EAAA;EAAAI,SAAA,WAAAqP,oBAAA9b,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAoN,WAAA,CAAAlN,GAAA;MAAFF,EAAE,CAAAoN,WAAA,CAAAjN,GAAA;MAAFH,EAAE,CAAAoN,WAAA,CAAAhN,GAAA;MAAFJ,EAAE,CAAAoN,WAAA,CAAA/M,GAAA;MAAFL,EAAE,CAAAoN,WAAA,CAAA9M,GAAA;IAAA;IAAA,IAAAG,EAAA;MAAA,IAAAqM,EAAA;MAAF9M,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAA+Y,iBAAA,GAAA3M,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAsZ,QAAA,GAAAlN,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAqa,aAAA,GAAAjO,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAA8T,cAAA,GAAA1H,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAA4T,kBAAA,GAAAxH,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAI,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAiP,2BAAA/b,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAgD,WAAA,mDAAAtC,GAAA,CAAA6Y,uBA63BmB,CAAC,2BAAZ7Y,GAAA,CAAA8U,mBAAA,CAAoB,CAAC,IAAI,KAAd,CAAC;IAAA;EAAA;EAAA/H,MAAA;IAAA/J,SAAA;IAAAC,cAAA;IAAAE,aAAA,wCAA6MuC,gBAAgB;EAAA;EAAA6D,QAAA,GA73BnPjK,EAAE,CAAAkL,0BAAA;EAAA2C,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAyO,MAAA;EAAAxO,QAAA,WAAAyO,uBAAAjc,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAA,MAAAkc,GAAA,GAAF3c,EAAE,CAAA2B,gBAAA;MAAF3B,EAAE,CAAAmO,eAAA;MAAFnO,EAAE,CAAA4B,cAAA,eA63B+sD,CAAC;MA73BltD5B,EAAE,CAAA6B,UAAA,mBAAA+a,4CAAA;QAAF5c,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CA63B2kD5B,GAAA,CAAAga,qBAAA,CAAsB,QAAQ,CAAC;MAAA,CAAC,CAAC,uBAAAmC,gDAAApa,MAAA;QA73B9mDzC,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CA63BioD5B,GAAA,CAAA6T,qBAAA,CAAsB,QAAQ,EAAA9R,MAAQ,CAAC;MAAA,CAAC,CAAC,sBAAAqa,+CAAA;QA73B5qD9c,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CA63B8rD5B,GAAA,CAAAyT,aAAA,CAAc,CAAC;MAAA,CAAC,CAAC;MA73BjtDnU,EAAE,CAAA2C,SAAA,YA63B8wD,CAAC;MA73BjxD3C,EAAE,CAAA4C,YAAA,CA63BsxD,CAAC;MA73BzxD5C,EAAE,CAAA4B,cAAA,eA63Bg9D,CAAC;MA73Bn9D5B,EAAE,CAAA6B,UAAA,qBAAAkb,8CAAAta,MAAA;QAAFzC,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CA63B62D5B,GAAA,CAAA0X,cAAA,CAAA3V,MAAqB,CAAC;MAAA,CAAC,CAAC;MA73Bv4DzC,EAAE,CAAA4B,cAAA,eA63BkrE,CAAC;MA73BrrE5B,EAAE,CAAA6B,UAAA,+BAAAmb,wDAAA;QAAFhd,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CA63B6pE5B,GAAA,CAAAmY,iBAAA,CAAkB,CAAC;MAAA,CAAC,CAAC;MA73BprE7Y,EAAE,CAAA4B,cAAA,eA63BwuE,CAAC;MA73B3uE5B,EAAE,CAAAC,YAAA,EA63BywE,CAAC;MA73B5wED,EAAE,CAAA4C,YAAA,CA63BqxE,CAAC,CAAS,CAAC,CAAO,CAAC;MA73B1yE5C,EAAE,CAAA4B,cAAA,iBA63BqsF,CAAC;MA73BxsF5B,EAAE,CAAA6B,UAAA,uBAAAob,iDAAAxa,MAAA;QAAFzC,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CA63BukF5B,GAAA,CAAA6T,qBAAA,CAAsB,OAAO,EAAA9R,MAAQ,CAAC;MAAA,CAAC,CAAC,mBAAAya,6CAAA;QA73BjnFld,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CA63BgoF5B,GAAA,CAAAga,qBAAA,CAAsB,OAAO,CAAC;MAAA,CAAC,CAAC,sBAAAyC,gDAAA;QA73BlqFnd,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CA63BorF5B,GAAA,CAAAyT,aAAA,CAAc,CAAC;MAAA,CAAC,CAAC;MA73BvsFnU,EAAE,CAAA2C,SAAA,aA63BowF,CAAC;MA73BvwF3C,EAAE,CAAA4C,YAAA,CA63B4wF,CAAC;IAAA;IAAA,IAAAnC,EAAA;MA73B/wFT,EAAE,CAAAgD,WAAA,2CAAAtC,GAAA,CAAA2a,oBA63ByjD,CAAC;MA73B5jDrb,EAAE,CAAAmB,UAAA,sBAAAT,GAAA,CAAA2a,oBAAA,IAAA3a,GAAA,CAAAmD,aA63B2+C,CAAC;MA73B9+C7D,EAAE,CAAA4D,SAAA,EA63B+8D,CAAC;MA73Bl9D5D,EAAE,CAAAgD,WAAA,4BAAAtC,GAAA,CAAA0c,cAAA,qBA63B+8D,CAAC;MA73Bl9Dpd,EAAE,CAAA4D,SAAA,EA63BykE,CAAC;MA73B5kE5D,EAAE,CAAAqD,WAAA,eAAA3C,GAAA,CAAAgD,SAAA,6BAAAhD,GAAA,CAAAiD,cAAA;MAAF3D,EAAE,CAAA4D,SAAA,EA63BijF,CAAC;MA73BpjF5D,EAAE,CAAAgD,WAAA,2CAAAtC,GAAA,CAAA0a,mBA63BijF,CAAC;MA73BpjFpb,EAAE,CAAAmB,UAAA,sBAAAT,GAAA,CAAA0a,mBAAA,IAAA1a,GAAA,CAAAmD,aA63Bo+E,CAAC;IAAA;EAAA;EAAAwZ,YAAA,GAA2sGlU,SAAS,EAAwPF,iBAAiB;EAAAqU,MAAA;EAAAjP,aAAA;AAAA;AAErhM;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KA/3BiFrK,EAAE,CAAAsK,iBAAA,CA+3BQ2R,YAAY,EAAc,CAAC;IAC1GlS,IAAI,EAAE1D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAgB;MAAE6D,aAAa,EAAE9H,iBAAiB,CAACiI,IAAI;MAAEF,eAAe,EAAEhI,uBAAuB,CAACiI,OAAO;MAAEE,IAAI,EAAE;QACxH,OAAO,EAAE,oBAAoB;QAC7B,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE;MACtC,CAAC;MAAE8O,OAAO,EAAE,CAACpU,SAAS,EAAEF,iBAAiB,CAAC;MAAEgF,QAAQ,EAAE,6qDAA6qD;MAAEqP,MAAM,EAAE,CAAC,w2FAAw2F;IAAE,CAAC;EACrmJ,CAAC,CAAC,QAAkB;IAAEtO,MAAM,EAAE,CAAC;MACvBjF,IAAI,EAAE1C,eAAe;MACrBkD,IAAI,EAAE,CAACoI,kBAAkB,EAAE;QAAE6K,WAAW,EAAE;MAAM,CAAC;IACrD,CAAC,CAAC;IAAE/D,iBAAiB,EAAE,CAAC;MACpB1P,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEqE,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEoL,QAAQ,EAAE,CAAC;MACXjQ,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEqE,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAEmM,aAAa,EAAE,CAAC;MAChBhR,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEqE,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAE4F,cAAc,EAAE,CAAC;MACjBzK,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE+J,kBAAkB,EAAE,CAAC;MACrBvK,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE7G,SAAS,EAAE,CAAC;MACZqG,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE5G,cAAc,EAAE,CAAC;MACjBoG,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE1G,aAAa,EAAE,CAAC;MAChBkG,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMqX,eAAe,GAAG,IAAI1X,cAAc,CAAC,iBAAiB,CAAC;;AAE7D;AACA;AACA;AACA;AACA,MAAM2X,gBAAgB,SAAS7U,eAAe,CAAC;EAM3CY,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IAACC,eAAA,gBANJ1D,MAAM,CAAC2X,UAAU,CAAC;IAC1B;IAAAjU,eAAA,wBACgBvB,YAAY,CAACH,KAAK;IAClC;IAAA0B,eAAA,sBACcvB,YAAY,CAACH,KAAK;EAGhC;EACA;EACAkE,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,CAAC0R,aAAa,GAAG,IAAI,CAACC,KAAK,CAACC,gBAAgB,CAC3C9I,IAAI,CAACzM,SAAS,CAAC,IAAI,CAACsV,KAAK,CAACE,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAC/C9H,SAAS,CAAE+H,WAAW,IAAK;MAC5B,IAAI,IAAI,CAACH,KAAK,CAACI,QAAQ,IAAID,WAAW,IAAI,CAAC,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;QAC3D,IAAI,CAACC,MAAM,CAAC,IAAI,CAACN,KAAK,CAACI,QAAQ,CAAC;MACpC;IACJ,CAAC,CAAC;IACF,IAAI,CAACG,WAAW,GAAG,IAAI,CAACP,KAAK,CAACQ,mBAAmB,CAACpI,SAAS,CAAC,MAAM;MAC9D,IAAI,CAAC,IAAI,CAAC4H,KAAK,CAAC/Y,eAAe,EAAE;QAC7B,IAAI,CAACwZ,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA;EACAtS,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAAC4R,aAAa,CAACW,WAAW,CAAC,CAAC;IAChC,IAAI,CAACH,WAAW,CAACG,WAAW,CAAC,CAAC;EAClC;AAGJ;AAACC,iBAAA,GAjCKd,gBAAgB;AAAAhU,eAAA,CAAhBgU,gBAAgB,wBAAAe,0BAAA5U,iBAAA;EAAA,YAAAA,iBAAA,IA+BiF6T,iBAAgB;AAAA;AAAAhU,eAAA,CA/BjHgU,gBAAgB,8BA16B2D1d,EAAE,CAAA8J,iBAAA;EAAAC,IAAA,EA08BQ2T,iBAAgB;EAAA1T,SAAA;EAAAC,QAAA,GA18B1BjK,EAAE,CAAAkL,0BAAA;AAAA;AA48BnF;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KA58BiFrK,EAAE,CAAAsK,iBAAA,CA48BQoT,gBAAgB,EAAc,CAAC;IAC9G3T,IAAI,EAAE7D,SAAS;IACfqE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAmB,CAAC;EAC3C,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMmT,UAAU,CAAC;EAsCb;EACA,IAAI/Y,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAAC8Z,cAAc,GAAG9Z,QAAQ;IAC9B,IAAI,CAAC+Z,8BAA8B,CAAC,CAAC;EACzC;EACAlV,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBA1CA1D,MAAM,CAACW,UAAU,CAAC;IAAA+C,eAAA,eACzB1D,MAAM,CAACV,cAAc,EAAE;MAAEuF,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAnB,eAAA,kBACvC1D,MAAM,CAACa,MAAM,CAAC;IAAA6C,eAAA,oBACZ1D,MAAM,CAACc,QAAQ,CAAC;IAAA4C,eAAA,oBAChB1D,MAAM,CAACe,SAAS,CAAC;IAAA2C,eAAA,4BACT1D,MAAM,CAACgB,qBAAqB,EAAE;MAAE6D,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAnB,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAIrE;IAAAA,eAAA;IAEA;IAAAA,eAAA,iCACyBvB,YAAY,CAACH,KAAK;IAC3C;IAAA0B,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,uBACe,IAAIzC,YAAY,CAAC,CAAC;IACjC;IAAAyC,eAAA,2BACmB,IAAIzC,YAAY,CAAC,CAAC;IACrC;IAAAyC,eAAA,8BACsB,IAAIzC,YAAY,CAAC,CAAC;IACxC;IAAAyC,eAAA,sBACc,IAAIzC,YAAY,CAAC,IAAI,CAAC;IACpC;IAAAyC,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IACA;IACA;IAAAA,eAAA,4BACoB,OAAO;IAC3B;IAAAA,eAAA,0BACkB,KAAK;IAOnB,IAAI,IAAI,CAACiL,IAAI,EAAE;MACX,MAAMiK,iBAAiB,GAAG5Y,MAAM,CAACY,iBAAiB,CAAC;MACnD,IAAI,CAACiY,sBAAsB,GAAG,IAAI,CAAClK,IAAI,CAACC,MAAM,CAACqB,SAAS,CAAEyD,GAAG,IAAK;QAC9D,IAAI,CAACiF,8BAA8B,CAACjF,GAAG,CAAC;QACxCkF,iBAAiB,CAACjH,YAAY,CAAC,CAAC;MACpC,CAAC,CAAC;IACN;EACJ;EACAzL,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC4S,qBAAqB,CAAC,CAAC;IAC5B,IAAI,IAAI,CAACC,SAAS,KAAK,QAAQ,EAAE;MAC7B,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC;MAC1B;MACA9X,eAAe,CAAC,MAAM,IAAI,CAAC+X,YAAY,CAACrI,IAAI,CAAC,IAAI,CAACnG,WAAW,CAACd,aAAa,CAACuP,YAAY,CAAC,EAAE;QACvFpJ,QAAQ,EAAE,IAAI,CAACC;MACnB,CAAC,CAAC;IACN;IACA,IAAI,CAACoJ,YAAY,GAAG,IAAI;EAC5B;EACAnT,WAAWA,CAAA,EAAG;IAAA,IAAAoT,oBAAA;IACVC,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;IACjC,CAAAF,oBAAA,OAAI,CAACtL,cAAc,cAAAsL,oBAAA,eAAnBA,oBAAA,CAAqBlQ,OAAO,CAAC+I,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IAClD,IAAI,CAAC4G,sBAAsB,CAACN,WAAW,CAAC,CAAC;EAC7C;EACA;EACAO,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC/K,OAAO,CAACC,iBAAiB,CAAC,MAAM;MACjC,MAAMzE,OAAO,GAAG,IAAI,CAACkB,WAAW,CAACd,aAAa;MAC9C,MAAM4P,cAAc,GAAIlH,KAAK,IAAK;QAAA,IAAAmH,qBAAA;QAC9B,IAAInH,KAAK,CAACoH,MAAM,OAAAD,qBAAA,GAAK,IAAI,CAACE,eAAe,cAAAF,qBAAA,uBAApBA,qBAAA,CAAsB7P,aAAa,GAAE;UACtD,IAAI,CAACc,WAAW,CAACd,aAAa,CAACgB,SAAS,CAACS,MAAM,CAAC,wBAAwB,CAAC;UACzE;UACA;UACA,IAAIiH,KAAK,CAACtO,IAAI,KAAK,eAAe,EAAE;YAChC,IAAI,CAAC4V,eAAe,CAAC,CAAC;UAC1B;QACJ;MACJ,CAAC;MACD,IAAI,CAAC7L,cAAc,GAAG,CAClB,IAAI,CAACG,SAAS,CAACC,MAAM,CAAC3E,OAAO,EAAE,iBAAiB,EAAG8I,KAAK,IAAK;QAAA,IAAAuH,sBAAA;QACzD,IAAIvH,KAAK,CAACoH,MAAM,OAAAG,sBAAA,GAAK,IAAI,CAACF,eAAe,cAAAE,sBAAA,uBAApBA,sBAAA,CAAsBjQ,aAAa,GAAE;UACtD,IAAI,CAACc,WAAW,CAACd,aAAa,CAACgB,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;UACtE,IAAI,CAACiP,kBAAkB,CAAC,CAAC;QAC7B;MACJ,CAAC,CAAC,EACF,IAAI,CAAC5L,SAAS,CAACC,MAAM,CAAC3E,OAAO,EAAE,eAAe,EAAEgQ,cAAc,CAAC,EAC/D,IAAI,CAACtL,SAAS,CAACC,MAAM,CAAC3E,OAAO,EAAE,kBAAkB,EAAEgQ,cAAc,CAAC,CACrE;IACL,CAAC,CAAC;EACN;EACA;EACAM,kBAAkBA,CAAA,EAAG;IACjBR,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;IACjC,MAAMtB,WAAW,GAAG,IAAI,CAACe,SAAS,KAAK,QAAQ;IAC/C,IAAI,CAACjB,gBAAgB,CAAClH,IAAI,CAACoH,WAAW,CAAC;IACvC,IAAIA,WAAW,EAAE;MACb,IAAI,CAACiB,YAAY,CAACrI,IAAI,CAAC,IAAI,CAACnG,WAAW,CAACd,aAAa,CAACuP,YAAY,CAAC;IACvE;EACJ;EACA;EACAS,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACZ,SAAS,KAAK,QAAQ,EAAE;MAC7B,IAAI,CAACe,WAAW,CAAClJ,IAAI,CAAC,CAAC;IAC3B,CAAC,MACI,IAAI,IAAI,CAACmJ,iBAAiB,KAAK,QAAQ,EAAE;MAC1C,IAAI,CAAC1B,mBAAmB,CAACzH,IAAI,CAAC,CAAC;IACnC;EACJ;EACA;EACAoI,eAAeA,CAACgB,QAAQ,EAAE;IACtB,IAAI,CAACvP,WAAW,CAACd,aAAa,CAACgB,SAAS,CAACsP,MAAM,CAAC,yBAAyB,EAAED,QAAQ,CAAC;EACxF;EACA;EACAxK,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACb,IAAI,IAAI,IAAI,CAACA,IAAI,CAACrJ,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAyS,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACW,cAAc,KAAK,CAAC;EACpC;EACA;EACAC,8BAA8BA,CAACjF,GAAG,GAAG,IAAI,CAAClE,mBAAmB,CAAC,CAAC,EAAE;IAC7D,IAAI,CAACuK,iBAAiB,GAAG,IAAI,CAAChB,SAAS;IACvC,IAAI,IAAI,CAACL,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACK,SAAS,GAAGrF,GAAG,IAAI,KAAK,GAAG,MAAM,GAAG,OAAO;IACpD,CAAC,MACI,IAAI,IAAI,CAACgF,cAAc,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACK,SAAS,GAAGrF,GAAG,IAAI,KAAK,GAAG,OAAO,GAAG,MAAM;IACpD,CAAC,MACI;MACD,IAAI,CAACqF,SAAS,GAAG,QAAQ;IAC7B;IACA,IAAI,IAAI,CAACmB,mBAAmB,CAAC,CAAC,EAAE;MAC5B,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpC,CAAC,MACI,IAAI,IAAI,CAAChB,YAAY,KACrB,IAAI,CAACJ,SAAS,KAAK,QAAQ,IAAI,IAAI,CAACgB,iBAAiB,KAAK,QAAQ,CAAC,EAAE;MACtE;MACA;MACA;MACAV,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI,CAACvL,OAAO,CAACC,iBAAiB,CAAC,MAAMoM,UAAU,CAAC,MAAM,IAAI,CAACD,yBAAyB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACvH;EACJ;EACA;EACAA,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAACN,kBAAkB,CAAC,CAAC;IACzB3Y,eAAe,CAAC,MAAM,IAAI,CAACyY,eAAe,CAAC,CAAC,EAAE;MAAE7J,QAAQ,EAAE,IAAI,CAACC;IAAU,CAAC,CAAC;EAC/E;EACA;EACAmK,mBAAmBA,CAAA,EAAG;IAClB,OAAQ,IAAI,CAACG,iBAAiB,KAAK,gBAAgB,IAC/C,IAAI,CAACxb,iBAAiB,KAAK,KAAK,IAChC,IAAI,CAACA,iBAAiB,KAAK,IAAI;EACvC;AAGJ;AAACyb,WAAA,GAjKK3C,UAAU;AAAAjU,eAAA,CAAViU,UAAU,wBAAA4C,oBAAA1W,iBAAA;EAAA,YAAAA,iBAAA,IA+JuF8T,WAAU;AAAA;AAAAjU,eAAA,CA/J3GiU,UAAU,8BAp9BiE3d,EAAE,CAAAyM,iBAAA;EAAA1C,IAAA,EAonCQ4T,WAAU;EAAA3T,SAAA;EAAAkD,SAAA,WAAAsT,kBAAA/f,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MApnCpBT,EAAE,CAAAoN,WAAA,CAonC2fsQ,gBAAgB;MApnC7gB1d,EAAE,CAAAoN,WAAA,CAAA7M,GAAA;IAAA;IAAA,IAAAE,EAAA;MAAA,IAAAqM,EAAA;MAAF9M,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAA+f,WAAA,GAAA3T,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAgf,eAAA,GAAA5S,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAI,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAmT,yBAAAjgB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAqD,WAAA,UAAA3C,GAAA,CAAAqe,SAAA,KAonCsB,QAAQ,GAAG,IAAI,GAAG,EAAE;IAAA;EAAA;EAAAtR,MAAA;IAAAwQ,QAAA;IAAApZ,iBAAA;IAAAC,eAAA;IAAAF,QAAA;EAAA;EAAAoX,OAAA;IAAAiD,YAAA;IAAAnB,gBAAA;IAAAgC,WAAA;EAAA;EAAA/R,KAAA;EAAAC,IAAA;EAAAyO,MAAA;EAAAxO,QAAA,WAAA0S,qBAAAlgB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MApnC5CT,EAAE,CAAA4B,cAAA,eAonC68B,CAAC;MApnCh9B5B,EAAE,CAAAe,UAAA,IAAAP,kCAAA,wBAonC6+B,CAAC;MApnCh/BR,EAAE,CAAA4C,YAAA,CAonCmgC,CAAC;IAAA;IAAA,IAAAnC,EAAA;MApnCtgCT,EAAE,CAAAgD,WAAA,8BAAAtC,GAAA,CAAAqe,SAAA,WAonCiyB,CAAC,+BAAAre,GAAA,CAAAqe,SAAA,YAAgE,CAAC,qCAAAre,GAAA,CAAAqe,SAAA,iBAAAre,GAAA,CAAAqf,iBAAA,aAAyG,CAAC;IAAA;EAAA;EAAA1C,YAAA,GAAykCK,gBAAgB,EAA6D5X,aAAa;EAAAwX,MAAA;EAAAjP,aAAA;AAAA;AAEnsE;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KAtnCiFrK,EAAE,CAAAsK,iBAAA,CAsnCQqT,UAAU,EAAc,CAAC;IACxG5T,IAAI,EAAE1D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAE6D,aAAa,EAAE9H,iBAAiB,CAACiI,IAAI;MAAEF,eAAe,EAAEhI,uBAAuB,CAACiI,OAAO;MAAEE,IAAI,EAAE;QACtH,OAAO,EAAE,kBAAkB;QAC3B;QACA;QACA;QACA;QACA,cAAc,EAAE;MACpB,CAAC;MAAE8O,OAAO,EAAE,CAACG,gBAAgB,EAAE5X,aAAa,CAAC;MAAEmI,QAAQ,EAAE,+WAA+W;MAAEqP,MAAM,EAAE,CAAC,u9BAAu9B;IAAE,CAAC;EACz5C,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE2B,YAAY,EAAE,CAAC;MACvDlV,IAAI,EAAE3C;IACV,CAAC,CAAC;IAAE0W,gBAAgB,EAAE,CAAC;MACnB/T,IAAI,EAAE3C;IACV,CAAC,CAAC;IAAE0Y,WAAW,EAAE,CAAC;MACd/V,IAAI,EAAE3C;IACV,CAAC,CAAC;IAAEqZ,WAAW,EAAE,CAAC;MACd1W,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAACmT,gBAAgB;IAC3B,CAAC,CAAC;IAAEgC,eAAe,EAAE,CAAC;MAClB3V,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE0T,QAAQ,EAAE,CAAC;MACXlU,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE1F,iBAAiB,EAAE,CAAC;MACpBkF,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE1B,eAAe,EAAE,CAAC;MAClBiF,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE5B,QAAQ,EAAE,CAAC;MACXmF,IAAI,EAAEvD;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMoa,WAAW,CAAC;EAgCd;EACA,IAAIxd,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACyd,mBAAmB;EACnC;EACA,IAAIzd,kBAAkBA,CAACkI,KAAK,EAAE;IAC1B,IAAI,CAACuV,mBAAmB,GAAGvV,KAAK;IAChC,IAAI,CAACoM,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EAQA;EACA,IAAI1U,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACuQ,cAAc;EAC9B;EACA,IAAIvQ,aAAaA,CAACqI,KAAK,EAAE;IACrB,IAAI,CAACwV,cAAc,GAAGpN,KAAK,CAACpI,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;EACrD;EAIA;EACA,IAAIzG,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACkc,kBAAkB;EAClC;EACA,IAAIlc,iBAAiBA,CAACyG,KAAK,EAAE;IACzB,MAAM0V,WAAW,GAAG1V,KAAK,GAAG,EAAE;IAC9B,IAAI,CAACyV,kBAAkB,GAAG,OAAO,CAACE,IAAI,CAACD,WAAW,CAAC,GAAG1V,KAAK,GAAG,IAAI,GAAG0V,WAAW;EACpF;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIjc,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACmc,gBAAgB;EAChC;EACA,IAAInc,eAAeA,CAACuG,KAAK,EAAE;IACvB,IAAI,CAAC4V,gBAAgB,GAAGxN,KAAK,CAACpI,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;EACvD;EAeA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI6V,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAAC7V,KAAK,EAAE;IACvB,MAAMqF,SAAS,GAAG,IAAI,CAACF,WAAW,CAACd,aAAa,CAACgB,SAAS;IAC1DA,SAAS,CAACS,MAAM,CAAC,0BAA0B,EAAE,kBAAkB,IAAI,CAAC+P,eAAe,EAAE,CAAC;IACtF,IAAI7V,KAAK,EAAE;MACPqF,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAE,kBAAkBtF,KAAK,EAAE,CAAC;IACxE;IACA,IAAI,CAAC8V,gBAAgB,GAAG9V,KAAK;EACjC;EAiBA7B,WAAWA,CAAA,EAAG;IAAAC,eAAA,sBAhIA1D,MAAM,CAACW,UAAU,CAAC;IAAA+C,eAAA,6BACX1D,MAAM,CAACY,iBAAiB,CAAC;IAAA8C,eAAA,kBACpC1D,MAAM,CAACa,MAAM,CAAC;IAAA6C,eAAA,4BACJvB,YAAY,CAACH,KAAK;IAAA0B,eAAA,gCACdvB,YAAY,CAACH,KAAK;IAAA0B,eAAA,+BACnBvB,YAAY,CAACH,KAAK;IAAA0B,eAAA,gCACjB1D,MAAM,CAACgB,qBAAqB,EAAE;MAAE6D,QAAQ,EAAE;IAAK,CAAC,CAAC,KAAK,gBAAgB;IAC9F;AACJ;AACA;AACA;IAHInB,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAQA;IAAAA,eAAA,gBACQ,IAAIpC,SAAS,CAAC,CAAC;IACvB;IAAAoC,eAAA,yBACiB,CAAC;IAClB;IAAAA,eAAA,+BACuB,IAAI;IAC3B;IAAAA,eAAA,gCACwB,CAAC;IACzB;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA;IAAAA,eAAA,8BAgBsB,KAAK;IAC3B;IAAAA,eAAA,sBACc,IAAI;IAClB;IAAAA,eAAA,oBACY,IAAI;IAChB;IAAAA,eAAA,wBACgB,KAAK;IAAAA,eAAA,yBAQJ,IAAI;IACrB;IAAAA,eAAA,yBACiB,OAAO;IAAAA,eAAA;IAAAA,eAAA;IAuBxB;AACJ;AACA;AACA;IAHIA,eAAA,4BAIoB,KAAK;IACzB;IAAAA,eAAA,wBACgB,KAAK;IACrB;AACJ;AACA;AACA;AACA;IAJIA,eAAA,0BAKkB,KAAK;IAAAA,eAAA;IAuBvB;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,8BACsB,IAAIzC,YAAY,CAAC,CAAC;IACxC;IAAAyC,eAAA,sBACc,IAAIzC,YAAY,CAAC,CAAC;IAChC;IAAAyC,eAAA,wBACgB,IAAIzC,YAAY,CAAC,CAAC;IAClC;IAAAyC,eAAA,4BACoB,IAAIzC,YAAY,CAAC,IAAI,CAAC;IAAAyC,eAAA;IAE1C;IAAAA,eAAA,oBACY,CAAC1D,MAAM,CAACL,QAAQ,CAAC,CAAC0b,SAAS;IAEnC,MAAMC,aAAa,GAAGtb,MAAM,CAACyX,eAAe,EAAE;MAAE5S,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjE,IAAI,CAAC0W,QAAQ,GAAGvb,MAAM,CAACb,YAAY,CAAC,CAACqc,KAAK,CAAC,gBAAgB,CAAC;IAC5D,IAAI,CAAC3c,iBAAiB,GAClByc,aAAa,IAAIA,aAAa,CAACzc,iBAAiB,GAAGyc,aAAa,CAACzc,iBAAiB,GAAG,OAAO;IAChG,IAAI,CAACgV,iBAAiB,GAClByH,aAAa,IAAIA,aAAa,CAACzH,iBAAiB,IAAI,IAAI,GAClDyH,aAAa,CAACzH,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAAC4H,aAAa,GACdH,aAAa,IAAIA,aAAa,CAACG,aAAa,IAAI,IAAI,GAAGH,aAAa,CAACG,aAAa,GAAG,KAAK;IAC9F,IAAI,CAAAH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEvc,eAAe,KAAI,IAAI,EAAE;MACxC,IAAI,CAACA,eAAe,GAAGuc,aAAa,CAACvc,eAAe;IACxD;IACA,IAAI,CAACD,eAAe,GAAG,CAAC,EAACwc,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAExc,eAAe;IACvD,IAAI,CAAC1B,kBAAkB,GACnBke,aAAa,IAAIA,aAAa,CAACle,kBAAkB,IAAI,IAAI,GACnDke,aAAa,CAACle,kBAAkB,GAChC,KAAK;IACf,IAAI,CAACse,WAAW,GACZJ,aAAa,IAAIA,aAAa,CAACI,WAAW,IAAI,IAAI,GAAGJ,aAAa,CAACI,WAAW,GAAG,IAAI;IACzF,IAAI,CAACC,SAAS,GACVL,aAAa,IAAIA,aAAa,CAACK,SAAS,IAAI,IAAI,GAAGL,aAAa,CAACK,SAAS,GAAG,IAAI;EACzF;EACA;AACJ;AACA;AACA;AACA;AACA;EACInK,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,MAAMoK,aAAa,GAAI,IAAI,CAACd,cAAc,GAAG,IAAI,CAACe,cAAc,CAAC,IAAI,CAACf,cAAc,CAAE;IACtF;IACA;IACA,IAAI,IAAI,CAACtN,cAAc,IAAIoO,aAAa,EAAE;MACtC,MAAME,UAAU,GAAG,IAAI,CAACtO,cAAc,IAAI,IAAI;MAC9C,IAAI,CAACsO,UAAU,EAAE;QACb,IAAI,CAACC,iBAAiB,CAACnL,IAAI,CAAC,IAAI,CAACoL,kBAAkB,CAACJ,aAAa,CAAC,CAAC;QACnE;QACA;QACA,MAAMK,OAAO,GAAG,IAAI,CAACC,eAAe,CAACvS,aAAa;QAClDsS,OAAO,CAAC/Q,KAAK,CAACiR,SAAS,GAAGF,OAAO,CAAC/C,YAAY,GAAG,IAAI;MACzD;MACA;MACA;MACA9I,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAC/S,KAAK,CAAC2L,OAAO,CAAC,CAACkT,GAAG,EAAEhJ,KAAK,KAAMgJ,GAAG,CAACpC,QAAQ,GAAG5G,KAAK,KAAKwI,aAAc,CAAC;QAC5E,IAAI,CAACE,UAAU,EAAE;UACb,IAAI,CAACO,mBAAmB,CAACzL,IAAI,CAACgL,aAAa,CAAC;UAC5C;UACA;UACA,IAAI,CAACM,eAAe,CAACvS,aAAa,CAACuB,KAAK,CAACiR,SAAS,GAAG,EAAE;QAC3D;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC5e,KAAK,CAAC2L,OAAO,CAAC,CAACkT,GAAG,EAAEhJ,KAAK,KAAK;MAC/BgJ,GAAG,CAACxd,QAAQ,GAAGwU,KAAK,GAAGwI,aAAa;MACpC;MACA;MACA,IAAI,IAAI,CAACpO,cAAc,IAAI,IAAI,IAAI4O,GAAG,CAACxd,QAAQ,IAAI,CAAC,IAAI,CAACwd,GAAG,CAACE,MAAM,EAAE;QACjEF,GAAG,CAACE,MAAM,GAAGV,aAAa,GAAG,IAAI,CAACpO,cAAc;MACpD;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACA,cAAc,KAAKoO,aAAa,EAAE;MACvC,IAAI,CAACpO,cAAc,GAAGoO,aAAa;MACnC,IAAI,CAACW,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAAC7K,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAlD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC+N,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B;IACA;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACnf,KAAK,CAACqI,OAAO,CAACqK,SAAS,CAAC,MAAM;MACxD,MAAM2L,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACf,cAAc,CAAC;MAC9D;MACA;MACA,IAAIc,aAAa,KAAK,IAAI,CAACpO,cAAc,EAAE;QACvC,MAAMmP,IAAI,GAAG,IAAI,CAACpf,KAAK,CAAC8V,OAAO,CAAC,CAAC;QACjC,IAAIuJ,WAAW;QACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACnf,MAAM,EAAEqf,CAAC,EAAE,EAAE;UAClC,IAAIF,IAAI,CAACE,CAAC,CAAC,CAAC7C,QAAQ,EAAE;YAClB;YACA;YACA;YACA,IAAI,CAACc,cAAc,GAAG,IAAI,CAACtN,cAAc,GAAGqP,CAAC;YAC7C,IAAI,CAACN,oBAAoB,GAAG,IAAI;YAChCK,WAAW,GAAGD,IAAI,CAACE,CAAC,CAAC;YACrB;UACJ;QACJ;QACA;QACA;QACA;QACA,IAAI,CAACD,WAAW,IAAID,IAAI,CAACf,aAAa,CAAC,EAAE;UACrCxL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;YACzBqM,IAAI,CAACf,aAAa,CAAC,CAAC5B,QAAQ,GAAG,IAAI;YACnC,IAAI,CAAC+B,iBAAiB,CAACnL,IAAI,CAAC,IAAI,CAACoL,kBAAkB,CAACJ,aAAa,CAAC,CAAC;UACvE,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAAClK,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACAvD,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC0O,oBAAoB,GAAG,IAAI,CAACC,UAAU,CAACnX,OAAO,CAACqK,SAAS,CAAC,MAAM,IAAI,CAAC1R,aAAa,CAAC,IAAI,CAAC,CAAC;EACjG;EACA;EACAie,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA,IAAI,CAACQ,QAAQ,CAACpX,OAAO,CAACoJ,IAAI,CAACzM,SAAS,CAAC,IAAI,CAACya,QAAQ,CAAC,CAAC,CAAC/M,SAAS,CAAE0M,IAAI,IAAK;MACrE,IAAI,CAACpf,KAAK,CAAC0f,KAAK,CAACN,IAAI,CAACja,MAAM,CAAC0Z,GAAG,IAAI;QAChC,OAAOA,GAAG,CAACc,gBAAgB,KAAK,IAAI,IAAI,CAACd,GAAG,CAACc,gBAAgB;MACjE,CAAC,CAAC,CAAC;MACH,IAAI,CAAC3f,KAAK,CAAC4f,eAAe,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACAnX,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzI,KAAK,CAAC2U,OAAO,CAAC,CAAC;IACpB,IAAI,CAACwK,iBAAiB,CAACnE,WAAW,CAAC,CAAC;IACpC,IAAI,CAAC6E,qBAAqB,CAAC7E,WAAW,CAAC,CAAC;IACxC,IAAI,CAACuE,oBAAoB,CAACvE,WAAW,CAAC,CAAC;EAC3C;EACA;EACA8E,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAChO,yBAAyB,CAAC,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACiO,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACjO,gBAAgB,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACIkO,QAAQA,CAACnK,KAAK,EAAE;IACZ,MAAMoK,MAAM,GAAG,IAAI,CAACF,UAAU;IAC9B,IAAIE,MAAM,EAAE;MACRA,MAAM,CAAChL,UAAU,GAAGY,KAAK;IAC7B;EACJ;EACAqK,aAAaA,CAACrK,KAAK,EAAE;IACjB,IAAI,CAACmJ,oBAAoB,GAAGnJ,KAAK;IACjC,IAAI,CAACsK,WAAW,CAAC9M,IAAI,CAAC,IAAI,CAACoL,kBAAkB,CAAC5I,KAAK,CAAC,CAAC;EACzD;EACA4I,kBAAkBA,CAAC5I,KAAK,EAAE;IACtB,MAAMf,KAAK,GAAG,IAAIsL,iBAAiB,CAAC,CAAC;IACrCtL,KAAK,CAACe,KAAK,GAAGA,KAAK;IACnB,IAAI,IAAI,CAAC7V,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,MAAM,EAAE;MACjC6U,KAAK,CAAC+J,GAAG,GAAG,IAAI,CAAC7e,KAAK,CAAC8V,OAAO,CAAC,CAAC,CAACD,KAAK,CAAC;IAC3C;IACA,OAAOf,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoK,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACW,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAC7E,WAAW,CAAC,CAAC;IAC5C;IACA,IAAI,CAAC6E,qBAAqB,GAAGrb,KAAK,CAAC,GAAG,IAAI,CAACxE,KAAK,CAACqgB,GAAG,CAACxB,GAAG,IAAIA,GAAG,CAACtW,aAAa,CAAC,CAAC,CAACmK,SAAS,CAAC,MAAM,IAAI,CAACyB,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAAC;EAC3I;EACA;EACAkK,cAAcA,CAACzI,KAAK,EAAE;IAClB;IACA;IACA;IACA,OAAOxD,IAAI,CAACY,GAAG,CAAC,IAAI,CAACjT,KAAK,CAACC,MAAM,GAAG,CAAC,EAAEoS,IAAI,CAACC,GAAG,CAACuD,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EACnE;EACA;EACAlW,cAAcA,CAACkf,GAAG,EAAEhJ,KAAK,EAAE;IACvB,OAAOgJ,GAAG,CAAC1U,EAAE,IAAI,GAAG,IAAI,CAAC6T,QAAQ,UAAUnI,KAAK,EAAE;EACtD;EACA;EACA3V,gBAAgBA,CAAC2V,KAAK,EAAE;IACpB,OAAO,GAAG,IAAI,CAACmI,QAAQ,YAAYnI,KAAK,EAAE;EAC9C;EACA;AACJ;AACA;AACA;EACI/U,wBAAwBA,CAACwf,SAAS,EAAE;IAChC,IAAI,CAAC,IAAI,CAACpC,aAAa,IAAI,CAAC,IAAI,CAACqC,qBAAqB,EAAE;MACpD,IAAI,CAACA,qBAAqB,GAAGD,SAAS;MACtC;IACJ;IACA,MAAM5B,OAAO,GAAG,IAAI,CAACC,eAAe,CAACvS,aAAa;IAClDsS,OAAO,CAAC/Q,KAAK,CAACqG,MAAM,GAAG,IAAI,CAACuM,qBAAqB,GAAG,IAAI;IACxD;IACA;IACA,IAAI,IAAI,CAAC5B,eAAe,CAACvS,aAAa,CAACoU,YAAY,EAAE;MACjD9B,OAAO,CAAC/Q,KAAK,CAACqG,MAAM,GAAGsM,SAAS,GAAG,IAAI;IAC3C;EACJ;EACA;EACA1f,2BAA2BA,CAAA,EAAG;IAC1B,MAAM8d,OAAO,GAAG,IAAI,CAACC,eAAe,CAACvS,aAAa;IAClD,IAAI,CAACmU,qBAAqB,GAAG7B,OAAO,CAAC/C,YAAY;IACjD+C,OAAO,CAAC/Q,KAAK,CAACqG,MAAM,GAAG,EAAE;IACzB,IAAI,CAACxD,OAAO,CAACoC,GAAG,CAAC,MAAM,IAAI,CAAC6N,aAAa,CAACpN,IAAI,CAAC,CAAC,CAAC;EACrD;EACA;EACArU,YAAYA,CAAC6f,GAAG,EAAE6B,SAAS,EAAE7K,KAAK,EAAE;IAChC6K,SAAS,CAACzL,UAAU,GAAGY,KAAK;IAC5B,IAAI,CAACgJ,GAAG,CAACjf,QAAQ,EAAE;MACf,IAAI,CAACF,aAAa,GAAGmW,KAAK;IAC9B;EACJ;EACA;EACA9V,YAAYA,CAAC8V,KAAK,EAAE;IAAA,IAAA8K,qBAAA;IAChB,MAAMC,WAAW,IAAAD,qBAAA,GAAG,IAAI,CAAC3B,oBAAoB,cAAA2B,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAACjhB,aAAa;IACnE,OAAOmW,KAAK,KAAK+K,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;EACzC;EACA;EACAzhB,gBAAgBA,CAAC0hB,WAAW,EAAEhL,KAAK,EAAE;IACjC;IACA;IACA;IACA;IACA,IAAIgL,WAAW,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,OAAO,EAAE;MACnE,IAAI,CAACd,UAAU,CAAC9K,UAAU,GAAGY,KAAK;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACI7U,aAAaA,CAAC8f,QAAQ,EAAE;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIA,QAAQ,EAAE;MAAA,IAAAC,gBAAA;MACV,CAAAA,gBAAA,OAAI,CAACvB,UAAU,cAAAuB,gBAAA,eAAfA,gBAAA,CAAiBpV,OAAO,CAAC,CAACqV,IAAI,EAAE1B,CAAC,KAAK0B,IAAI,CAACvF,eAAe,CAAC6D,CAAC,KAAK,IAAI,CAACrP,cAAc,CAAC,CAAC;IAC1F;EACJ;EACA0M,mBAAmBA,CAAA,EAAG;IAClB,OAAQ,IAAI,CAACsE,qBAAqB,IAC9B,IAAI,CAAC3f,iBAAiB,KAAK,GAAG,IAC9B,IAAI,CAACA,iBAAiB,KAAK,KAAK;EACxC;AAQJ;AAAC4f,YAAA,GA/YK7D,WAAW;AAAAlX,eAAA,CAAXkX,WAAW,wBAAA8D,qBAAA7a,iBAAA;EAAA,YAAAA,iBAAA,IAwYsF+W,YAAW;AAAA;AAAAlX,eAAA,CAxY5GkX,WAAW,8BA5pCgE5gB,EAAE,CAAAyM,iBAAA;EAAA1C,IAAA,EAqiDQ6W,YAAW;EAAA5W,SAAA;EAAA0C,cAAA,WAAAiY,4BAAAlkB,EAAA,EAAAC,GAAA,EAAAkM,QAAA;IAAA,IAAAnM,EAAA;MAriDrBT,EAAE,CAAA6M,cAAA,CAAAD,QAAA,EA0iDvBxB,MAAM;IAAA;IAAA,IAAA3K,EAAA;MAAA,IAAAqM,EAAA;MA1iDe9M,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAsiB,QAAA,GAAAlW,EAAA;IAAA;EAAA;EAAAI,SAAA,WAAA0X,mBAAAnkB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAoN,WAAA,CAAAzM,GAAA;MAAFX,EAAE,CAAAoN,WAAA,CAAAxM,GAAA;MAAFZ,EAAE,CAAAoN,WAAA,CA0iD4PuQ,UAAU;IAAA;IAAA,IAAAld,EAAA;MAAA,IAAAqM,EAAA;MA1iDxQ9M,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAwhB,eAAA,GAAApV,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAA4iB,UAAA,GAAAxW,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAqiB,UAAA,GAAAjW,EAAA;IAAA;EAAA;EAAAO,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAAsX,0BAAApkB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAqD,WAAA,mBAAA3C,GAAA,CAAAihB,SAAA;MAAF3hB,EAAE,CAAA8C,UAAA,CAqiDQ,MAAM,IAAApC,GAAA,CAAAokB,KAAA,IAAa,SAAS,CAAlB,CAAC;MAriDrB9kB,EAAE,CAAA+kB,WAAA,iCAAArkB,GAAA,CAAAmE,iBAqiDkB,CAAC;MAriDrB7E,EAAE,CAAAgD,WAAA,qCAAAtC,GAAA,CAAA+gB,aAqiDkB,CAAC,sCAAA/gB,GAAA,CAAAskB,cAAA,KAAQ,OAAT,CAAC,mCAAAtkB,GAAA,CAAAghB,WAAD,CAAC;IAAA;EAAA;EAAAjU,MAAA;IAAAqX,KAAA;IAAA1hB,kBAAA,kDAA4IgD,gBAAgB;IAAAsb,WAAA,yCAAoDtb,gBAAgB;IAAAub,SAAA;IAAAF,aAAA,wCAAiGrb,gBAAgB;IAAAnD,aAAA,wCAAqDkE,eAAe;IAAA6d,cAAA;IAAAngB,iBAAA;IAAAE,eAAA,4CAAqIoC,eAAe;IAAA0S,iBAAA,gDAAiEzT,gBAAgB;IAAAvC,aAAA,wCAAqDuC,gBAAgB;IAAAtB,eAAA,4CAA2DsB,gBAAgB;IAAA+a,eAAA;IAAAzd,SAAA;IAAAC,cAAA;EAAA;EAAAqY,OAAA;IAAAqG,mBAAA;IAAAqB,WAAA;IAAAM,aAAA;IAAAjC,iBAAA;EAAA;EAAApU,QAAA;EAAA1D,QAAA,GAriD/xBjK,EAAE,CAAAkK,kBAAA,CAqiDs9C,CAC7hD;IACIC,OAAO,EAAEgB,aAAa;IACtBf,WAAW,EAAEwW;EACjB,CAAC,CACJ;EAAA/S,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAyO,MAAA;EAAAxO,QAAA,WAAAgX,sBAAAxkB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAA,MAAAkc,GAAA,GA1iDwE3c,EAAE,CAAA2B,gBAAA;MAAF3B,EAAE,CAAAmO,eAAA;MAAFnO,EAAE,CAAA4B,cAAA,0BA0iD+uB,CAAC;MA1iDlvB5B,EAAE,CAAA6B,UAAA,0BAAAqjB,6DAAAziB,MAAA;QAAFzC,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CA0iDupB5B,GAAA,CAAA+iB,aAAA,CAAAhhB,MAAoB,CAAC;MAAA,CAAC,CAAC,gCAAA0iB,mEAAA1iB,MAAA;QA1iDhrBzC,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CAAA5B,GAAA,CAAAuC,aAAA,GAAAR,MAAA;MAAA,CA0iD8uB,CAAC;MA1iDjvBzC,EAAE,CAAAolB,gBAAA,IAAA3jB,2BAAA,mBAAFzB,EAAE,CAAAqlB,yBA0iD6lF,CAAC;MA1iDhmFrlB,EAAE,CAAA4C,YAAA,CA0iDgnF,CAAC;MA1iDnnF5C,EAAE,CAAAe,UAAA,IAAAgD,mCAAA,MA0iDq7F,CAAC;MA1iDx7F/D,EAAE,CAAA4B,cAAA,eA0iDwkG,CAAC;MA1iD3kG5B,EAAE,CAAAolB,gBAAA,IAAAphB,2BAAA,4BAAFhE,EAAE,CAAAqlB,yBA0iDg5H,CAAC;MA1iDn5HrlB,EAAE,CAAA4C,YAAA,CA0iDw5H,CAAC;IAAA;IAAA,IAAAnC,EAAA;MA1iD35HT,EAAE,CAAAmB,UAAA,kBAAAT,GAAA,CAAAuC,aAAA,KA0iDma,CAAC,kBAAAvC,GAAA,CAAAmD,aAAkD,CAAC,sBAAAnD,GAAA,CAAAmZ,iBAA0D,CAAC,eAAAnZ,GAAA,CAAAgD,SAA2C,CAAC,oBAAAhD,GAAA,CAAAiD,cAAqD,CAAC;MA1iDtnB3D,EAAE,CAAA4D,SAAA,EA0iD6lF,CAAC;MA1iDhmF5D,EAAE,CAAAslB,UAAA,CAAA5kB,GAAA,CAAA6C,KA0iD6lF,CAAC;MA1iDhmFvD,EAAE,CAAA4D,SAAA,EA0iDy8F,CAAC;MA1iD58F5D,EAAE,CAAA8D,aAAA,CAAApD,GAAA,CAAA6kB,SAAA,SA0iDy8F,CAAC;MA1iD58FvlB,EAAE,CAAA4D,SAAA,CA0iDojG,CAAC;MA1iDvjG5D,EAAE,CAAAgD,WAAA,4BAAAtC,GAAA,CAAAwf,mBAAA,EA0iDojG,CAAC;MA1iDvjGlgB,EAAE,CAAA4D,SAAA,EA0iDg5H,CAAC;MA1iDn5H5D,EAAE,CAAAslB,UAAA,CAAA5kB,GAAA,CAAA6C,KA0iDg5H,CAAC;IAAA;EAAA;EAAA8Z,YAAA,GAAwpOpB,YAAY,EAAuHtJ,kBAAkB,EAAuFvN,eAAe,EAA2J+D,SAAS,EAAwPN,eAAe,EAAiJ8U,UAAU;EAAAL,MAAA;EAAAjP,aAAA;AAAA;AAE77X;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KA5iDiFrK,EAAE,CAAAsK,iBAAA,CA4iDQsW,WAAW,EAAc,CAAC;IACzG7W,IAAI,EAAE1D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEmD,QAAQ,EAAE,aAAa;MAAEU,aAAa,EAAE9H,iBAAiB,CAACiI,IAAI;MAAEF,eAAe,EAAEhI,uBAAuB,CAACiI,OAAO;MAAE9D,SAAS,EAAE,CACrJ;QACIN,OAAO,EAAEgB,aAAa;QACtBf,WAAW,EAAEwW;MACjB,CAAC,CACJ;MAAEnS,IAAI,EAAE;QACL,OAAO,EAAE,mBAAmB;QAC5B,SAAS,EAAE,+BAA+B;QAC1C,0CAA0C,EAAE,eAAe;QAC3D,2CAA2C,EAAE,4BAA4B;QACzE,wCAAwC,EAAE,aAAa;QACvD,uBAAuB,EAAE,WAAW;QACpC,sCAAsC,EAAE;MAC5C,CAAC;MAAE8O,OAAO,EAAE,CACRtB,YAAY,EACZtJ,kBAAkB,EAClBvN,eAAe,EACf+D,SAAS,EACTN,eAAe,EACf8U,UAAU,CACb;MAAE1P,QAAQ,EAAE,2kHAA2kH;MAAEqP,MAAM,EAAE,CAAC,qlOAAqlO;IAAE,CAAC;EACvsV,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE0F,QAAQ,EAAE,CAAC;MACnDjZ,IAAI,EAAE1C,eAAe;MACrBkD,IAAI,EAAE,CAACa,MAAM,EAAE;QAAEoS,WAAW,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEuF,UAAU,EAAE,CAAC;MACbhZ,IAAI,EAAExC,YAAY;MAClBgD,IAAI,EAAE,CAACoT,UAAU;IACrB,CAAC,CAAC;IAAEuE,eAAe,EAAE,CAAC;MAClBnY,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAE+Y,UAAU,EAAE,CAAC;MACbvZ,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEua,KAAK,EAAE,CAAC;MACR/a,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAEpD,kBAAkB,EAAE,CAAC;MACrB2G,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsb,WAAW,EAAE,CAAC;MACd3X,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEib,KAAK,EAAE,kBAAkB;QAAE9W,SAAS,EAAEtI;MAAiB,CAAC;IACrE,CAAC,CAAC;IAAEub,SAAS,EAAE,CAAC;MACZ5X,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEib,KAAK,EAAE;MAAiB,CAAC;IACtC,CAAC,CAAC;IAAE/D,aAAa,EAAE,CAAC;MAChB1X,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEnD,aAAa,EAAE,CAAC;MAChB8G,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEvH;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE6d,cAAc,EAAE,CAAC;MACjBjb,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE3B,iBAAiB,EAAE,CAAC;MACpBkF,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAEzB,eAAe,EAAE,CAAC;MAClBgF,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEvH;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE0S,iBAAiB,EAAE,CAAC;MACpB9P,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEvC,aAAa,EAAE,CAAC;MAChBkG,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEtB,eAAe,EAAE,CAAC;MAClBiF,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+a,eAAe,EAAE,CAAC;MAClBpX,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE9C,SAAS,EAAE,CAAC;MACZqG,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE5G,cAAc,EAAE,CAAC;MACjBoG,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE8X,mBAAmB,EAAE,CAAC;MACtBtY,IAAI,EAAE3C;IACV,CAAC,CAAC;IAAEsc,WAAW,EAAE,CAAC;MACd3Z,IAAI,EAAE3C;IACV,CAAC,CAAC;IAAE4c,aAAa,EAAE,CAAC;MAChBja,IAAI,EAAE3C;IACV,CAAC,CAAC;IAAE2a,iBAAiB,EAAE,CAAC;MACpBhY,IAAI,EAAE3C;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMuc,iBAAiB,CAAC;EAAAla,YAAA;IACpB;IAAAC,eAAA;IAEA;IAAAA,eAAA;EAAA;AAEJ;;AAEA;AACA;AACA;AACA;AACA,MAAM+b,SAAS,SAASlS,qBAAqB,CAAC;EAE1C;EACA,IAAInQ,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACyd,mBAAmB,CAACvV,KAAK;EACzC;EACA,IAAIlI,kBAAkBA,CAACkI,KAAK,EAAE;IAC1B,IAAI,CAACuV,mBAAmB,CAAC9U,IAAI,CAACT,KAAK,CAAC;IACpC,IAAI,CAACoM,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EAIA,IAAI9S,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACkc,kBAAkB;EAClC;EACA,IAAIlc,iBAAiBA,CAACyG,KAAK,EAAE;IACzB,MAAM0V,WAAW,GAAG1V,KAAK,GAAG,EAAE;IAC9B,IAAI,CAACyV,kBAAkB,GAAG,OAAO,CAACE,IAAI,CAACD,WAAW,CAAC,GAAG1V,KAAK,GAAG,IAAI,GAAG0V,WAAW;EACpF;EAIA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIG,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAAC7V,KAAK,EAAE;IACvB,MAAMqF,SAAS,GAAG,IAAI,CAACF,WAAW,CAACd,aAAa,CAACgB,SAAS;IAC1DA,SAAS,CAACS,MAAM,CAAC,0BAA0B,EAAE,kBAAkB,IAAI,CAAC+P,eAAe,EAAE,CAAC;IACtF,IAAI7V,KAAK,EAAE;MACPqF,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAE,kBAAkBtF,KAAK,EAAE,CAAC;IACxE;IACA,IAAI,CAAC8V,gBAAgB,GAAG9V,KAAK;EACjC;EAwBA7B,WAAWA,CAAA,EAAG;IACV,MAAMiG,UAAU,GAAG1J,MAAM,CAACW,UAAU,CAAC;IACrC,MAAM+S,GAAG,GAAG1T,MAAM,CAACV,cAAc,EAAE;MAAEuF,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtD,MAAM6a,MAAM,GAAG1f,MAAM,CAACa,MAAM,CAAC;IAC7B,MAAM+X,iBAAiB,GAAG5Y,MAAM,CAACY,iBAAiB,CAAC;IACnD,MAAM+e,aAAa,GAAG3f,MAAM,CAACH,aAAa,CAAC;IAC3C,MAAM+f,QAAQ,GAAG5f,MAAM,CAACL,QAAQ,CAAC;IACjC,MAAMkgB,aAAa,GAAG7f,MAAM,CAACgB,qBAAqB,EAAE;MAAE6D,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,MAAMyW,aAAa,GAAGtb,MAAM,CAACyX,eAAe,EAAE;MAAE5S,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjE,KAAK,CAAC6E,UAAU,EAAEkP,iBAAiB,EAAE+G,aAAa,EAAEjM,GAAG,EAAEgM,MAAM,EAAEE,QAAQ,EAAEC,aAAa,CAAC;IAACnc,eAAA,uBAxE/ElC,MAAM,CAAC,IAAI,CAAC;IAAAkC,eAAA,8BASL,IAAItB,eAAe,CAAC,KAAK,CAAC;IAChD;IAAAsB,eAAA,sBACc,IAAI;IAAAA,eAAA;IASlB;IAAAA,eAAA;IAAAA,eAAA;IAqBA;IAAAA,eAAA,wBACgB,KAAK;IACrB;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA,gBAOQ,SAAS;IACjB;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAsBI,IAAI,CAACmQ,iBAAiB,GAClByH,aAAa,IAAIA,aAAa,CAACzH,iBAAiB,IAAI,IAAI,GAClDyH,aAAa,CAACzH,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAACzW,kBAAkB,GACnBke,aAAa,IAAIA,aAAa,CAACle,kBAAkB,IAAI,IAAI,GACnDke,aAAa,CAACle,kBAAkB,GAChC,KAAK;IACf,IAAI,CAACse,WAAW,GACZJ,aAAa,IAAIA,aAAa,CAACI,WAAW,IAAI,IAAI,GAAGJ,aAAa,CAACI,WAAW,GAAG,IAAI;EAC7F;EACA/I,aAAaA,CAAA,EAAG;IACZ;EAAA;EAEJlE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACgH,OAAO,GAAG,IAAI1M,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC;IACA;IACA,IAAI,CAACA,MAAM,CAACpD,OAAO,CACdoJ,IAAI,CAACzM,SAAS,CAAC,IAAI,CAAC,EAAED,SAAS,CAAC,IAAI,CAAC2M,UAAU,CAAC,CAAC,CACjDgB,SAAS,CAAC,MAAM,IAAI,CAAC6P,gBAAgB,CAAC,CAAC,CAAC;IAC7C,KAAK,CAACrR,kBAAkB,CAAC,CAAC;IAC1B;IACA,IAAI,CAACb,WAAW,CAACgB,MAAM,CAACI,IAAI,CAACzM,SAAS,CAAC,IAAI,CAAC,EAAED,SAAS,CAAC,IAAI,CAAC2M,UAAU,CAAC,CAAC,CAACgB,SAAS,CAAC;MAAA,IAAA8P,kBAAA;MAAA,OAAM,IAAI,CAACC,YAAY,CAACC,GAAG,CAAC,EAAAF,kBAAA,OAAI,CAACnS,WAAW,cAAAmS,kBAAA,uBAAhBA,kBAAA,CAAkBG,UAAU,KAAI,IAAI,CAAC;IAAA,EAAC;EAC1J;EACA9R,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAAC+R,QAAQ,KAAK,OAAO9b,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAM,IAAI0H,KAAK,CAAC,uDAAuD,CAAC;IAC5E;IACA,KAAK,CAACqC,eAAe,CAAC,CAAC;EAC3B;EACA;EACA0R,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAC9W,MAAM,EAAE;MACd;IACJ;IACA,MAAMoX,KAAK,GAAG,IAAI,CAACpX,MAAM,CAACqK,OAAO,CAAC,CAAC;IACnC,KAAK,IAAIwJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,KAAK,CAAC5iB,MAAM,EAAEqf,CAAC,EAAE,EAAE;MACnC,IAAIuD,KAAK,CAACvD,CAAC,CAAC,CAACwD,MAAM,EAAE;QACjB,IAAI,CAACpjB,aAAa,GAAG4f,CAAC;QACtB,IAAI,IAAI,CAACsD,QAAQ,EAAE;UACf,IAAI,CAACA,QAAQ,CAACG,YAAY,GAAGF,KAAK,CAACvD,CAAC,CAAC,CAACnV,EAAE;QAC5C;QACA;QACA;QACA,IAAI,CAACsY,YAAY,CAACC,GAAG,CAACG,KAAK,CAACvD,CAAC,CAAC,CAAC;QAC/B,IAAI,CAACnL,kBAAkB,CAACC,YAAY,CAAC,CAAC;QACtC;MACJ;IACJ;IACA,IAAI,CAAC1U,aAAa,GAAG,CAAC,CAAC;EAC3B;EACAsjB,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACJ,QAAQ,GAAG,SAAS,GAAG,IAAI,CAAC1V,WAAW,CAACd,aAAa,CAAC6W,YAAY,CAAC,MAAM,CAAC;EAC1F;EACAC,SAASA,CAACC,IAAI,EAAE;IAAA,IAAAC,kBAAA;IACZ,OAAO,EAAAA,kBAAA,OAAI,CAAC/S,WAAW,cAAA+S,kBAAA,uBAAhBA,kBAAA,CAAkBT,UAAU,MAAKQ,IAAI;EAChD;AAGJ;AAACE,UAAA,GAtIKnB,SAAS;AAAA/b,eAAA,CAAT+b,SAAS,wBAAAoB,mBAAAhd,iBAAA;EAAA,YAAAA,iBAAA,IAoIwF4b,UAAS;AAAA;AAAA/b,eAAA,CApI1G+b,SAAS,8BA7oDkEzlB,EAAE,CAAAyM,iBAAA;EAAA1C,IAAA,EAkxDQ0b,UAAS;EAAAzb,SAAA;EAAA0C,cAAA,WAAAoa,0BAAArmB,EAAA,EAAAC,GAAA,EAAAkM,QAAA;IAAA,IAAAnM,EAAA;MAlxDnBT,EAAE,CAAA6M,cAAA,CAAAD,QAAA,EAkxD6jCma,UAAU;IAAA;IAAA,IAAAtmB,EAAA;MAAA,IAAAqM,EAAA;MAlxDzkC9M,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAsO,MAAA,GAAAlC,EAAA;IAAA;EAAA;EAAAI,SAAA,WAAA8Z,iBAAAvmB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAoN,WAAA,CAAAlN,GAAA;MAAFF,EAAE,CAAAoN,WAAA,CAAAjN,GAAA;MAAFH,EAAE,CAAAoN,WAAA,CAAAhN,GAAA;MAAFJ,EAAE,CAAAoN,WAAA,CAAA/M,GAAA;MAAFL,EAAE,CAAAoN,WAAA,CAAA9M,GAAA;IAAA;IAAA,IAAAG,EAAA;MAAA,IAAAqM,EAAA;MAAF9M,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAA+Y,iBAAA,GAAA3M,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAsZ,QAAA,GAAAlN,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAAqa,aAAA,GAAAjO,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAA8T,cAAA,GAAA1H,EAAA,CAAAG,KAAA;MAAFjN,EAAE,CAAA+M,cAAA,CAAAD,EAAA,GAAF9M,EAAE,CAAAgN,WAAA,QAAAtM,GAAA,CAAA4T,kBAAA,GAAAxH,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAI,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAA0Z,wBAAAxmB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAqD,WAAA,SAkxDQ3C,GAAA,CAAA6lB,QAAA,CAAS,CAAC;MAlxDpBvmB,EAAE,CAAA+kB,WAAA,iCAAArkB,GAAA,CAAAmE,iBAkxDgB,CAAC;MAlxDnB7E,EAAE,CAAAgD,WAAA,mDAAAtC,GAAA,CAAA6Y,uBAkxDgB,CAAC,2BAAT7Y,GAAA,CAAA8U,mBAAA,CAAoB,CAAC,IAAI,KAAjB,CAAC,qCAAA9U,GAAA,CAAAghB,WAAD,CAAC,gBAAAhhB,GAAA,CAAAokB,KAAA,KAAC,MAAM,IAAApkB,GAAA,CAAAokB,KAAA,KAAc,QAAtB,CAAC,eAAApkB,GAAA,CAAAokB,KAAA,KAAC,QAAF,CAAC,aAAApkB,GAAA,CAAAokB,KAAA,KAAC,MAAF,CAAC,4BAAApkB,GAAA,CAAA0c,cAAA,KAAU,gBAAX,CAAC;IAAA;EAAA;EAAA3P,MAAA;IAAArK,kBAAA,kDAAgIgD,gBAAgB;IAAAsb,WAAA,yCAAoDtb,gBAAgB;IAAAvB,iBAAA;IAAAsc,eAAA;IAAAtd,aAAA,wCAAiIuC,gBAAgB;IAAA0e,KAAA;IAAAqB,QAAA;EAAA;EAAAxY,QAAA;EAAA1D,QAAA,GAlxDxXjK,EAAE,CAAAkL,0BAAA;EAAAgc,KAAA,EAAAliB,GAAA;EAAA6I,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAyO,MAAA;EAAAxO,QAAA,WAAAkZ,oBAAA1mB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAA,MAAAkc,GAAA,GAAF3c,EAAE,CAAA2B,gBAAA;MAAF3B,EAAE,CAAAmO,eAAA;MAAFnO,EAAE,CAAA4B,cAAA,eAkxDu0E,CAAC;MAlxD10E5B,EAAE,CAAA6B,UAAA,mBAAAulB,yCAAA;QAAFpnB,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CAkxDmsE5B,GAAA,CAAAga,qBAAA,CAAsB,QAAQ,CAAC;MAAA,CAAC,CAAC,uBAAA2M,6CAAA5kB,MAAA;QAlxDtuEzC,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CAkxDyvE5B,GAAA,CAAA6T,qBAAA,CAAsB,QAAQ,EAAA9R,MAAQ,CAAC;MAAA,CAAC,CAAC,sBAAA6kB,4CAAA;QAlxDpyEtnB,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CAkxDszE5B,GAAA,CAAAyT,aAAA,CAAc,CAAC;MAAA,CAAC,CAAC;MAlxDz0EnU,EAAE,CAAA2C,SAAA,YAkxDs4E,CAAC;MAlxDz4E3C,EAAE,CAAA4C,YAAA,CAkxD84E,CAAC;MAlxDj5E5C,EAAE,CAAA4B,cAAA,eAkxDm/E,CAAC;MAlxDt/E5B,EAAE,CAAA6B,UAAA,qBAAA0lB,2CAAA9kB,MAAA;QAAFzC,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CAkxD29E5B,GAAA,CAAA0X,cAAA,CAAA3V,MAAqB,CAAC;MAAA,CAAC,CAAC;MAlxDr/EzC,EAAE,CAAA4B,cAAA,eAkxD4kF,CAAC;MAlxD/kF5B,EAAE,CAAA6B,UAAA,+BAAA2lB,qDAAA;QAAFxnB,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CAkxDujF5B,GAAA,CAAAmY,iBAAA,CAAkB,CAAC;MAAA,CAAC,CAAC;MAlxD9kF7Y,EAAE,CAAA4B,cAAA,eAkxDioF,CAAC;MAlxDpoF5B,EAAE,CAAAC,YAAA,EAkxDkqF,CAAC;MAlxDrqFD,EAAE,CAAA4C,YAAA,CAkxD8qF,CAAC,CAAS,CAAC,CAAO,CAAC;MAlxDnsF5C,EAAE,CAAA4B,cAAA,iBAkxD8lG,CAAC;MAlxDjmG5B,EAAE,CAAA6B,UAAA,uBAAA4lB,8CAAAhlB,MAAA;QAAFzC,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CAkxDg+F5B,GAAA,CAAA6T,qBAAA,CAAsB,OAAO,EAAA9R,MAAQ,CAAC;MAAA,CAAC,CAAC,mBAAAilB,0CAAA;QAlxD1gG1nB,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CAkxDyhG5B,GAAA,CAAAga,qBAAA,CAAsB,OAAO,CAAC;MAAA,CAAC,CAAC,sBAAAiN,6CAAA;QAlxD3jG3nB,EAAE,CAAAgC,aAAA,CAAA2a,GAAA;QAAA,OAAF3c,EAAE,CAAAsC,WAAA,CAkxD6kG5B,GAAA,CAAAyT,aAAA,CAAc,CAAC;MAAA,CAAC,CAAC;MAlxDhmGnU,EAAE,CAAA2C,SAAA,aAkxD6pG,CAAC;MAlxDhqG3C,EAAE,CAAA4C,YAAA,CAkxDqqG,CAAC;IAAA;IAAA,IAAAnC,EAAA;MAlxDxqGT,EAAE,CAAAgD,WAAA,2CAAAtC,GAAA,CAAA2a,oBAkxDirE,CAAC;MAlxDprErb,EAAE,CAAAmB,UAAA,sBAAAT,GAAA,CAAA2a,oBAAA,IAAA3a,GAAA,CAAAmD,aAkxDmmE,CAAC;MAlxDtmE7D,EAAE,CAAA4D,SAAA,GAkxD08F,CAAC;MAlxD78F5D,EAAE,CAAAgD,WAAA,2CAAAtC,GAAA,CAAA0a,mBAkxD08F,CAAC;MAlxD78Fpb,EAAE,CAAAmB,UAAA,sBAAAT,GAAA,CAAA0a,mBAAA,IAAA1a,GAAA,CAAAmD,aAkxD63F,CAAC;IAAA;EAAA;EAAAwZ,YAAA,GAAw0MlU,SAAS,EAAwPF,iBAAiB;EAAAqU,MAAA;EAAAjP,aAAA;AAAA;AAE3iT;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KApxDiFrK,EAAE,CAAAsK,iBAAA,CAoxDQmb,SAAS,EAAc,CAAC;IACvG1b,IAAI,EAAE1D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEmD,QAAQ,EAAE,yBAAyB;MAAEc,IAAI,EAAE;QACvE,aAAa,EAAE,YAAY;QAC3B,OAAO,EAAE,wCAAwC;QACjD,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE,gCAAgC;QAClE,0CAA0C,EAAE,aAAa;QACzD,qBAAqB,EAAE,wCAAwC;QAC/D,oBAAoB,EAAE,oBAAoB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,iCAAiC,EAAE,qCAAqC;QACxE,sCAAsC,EAAE;MAC5C,CAAC;MAAEJ,aAAa,EAAE9H,iBAAiB,CAACiI,IAAI;MAAEF,eAAe,EAAEhI,uBAAuB,CAACiI,OAAO;MAAEgP,OAAO,EAAE,CAACpU,SAAS,EAAEF,iBAAiB,CAAC;MAAEgF,QAAQ,EAAE,88CAA88C;MAAEqP,MAAM,EAAE,CAAC,q+LAAq+L;IAAE,CAAC;EAC5lP,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEla,kBAAkB,EAAE,CAAC;MAC7D2G,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsb,WAAW,EAAE,CAAC;MACd3X,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEib,KAAK,EAAE,kBAAkB;QAAE9W,SAAS,EAAEtI;MAAiB,CAAC;IACrE,CAAC,CAAC;IAAEvB,iBAAiB,EAAE,CAAC;MACpBkF,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAEwI,MAAM,EAAE,CAAC;MACTjF,IAAI,EAAE1C,eAAe;MACrBkD,IAAI,EAAE,CAAC9C,UAAU,CAAC,MAAMsf,UAAU,CAAC,EAAE;QAAEvJ,WAAW,EAAE;MAAK,CAAC;IAC9D,CAAC,CAAC;IAAE2D,eAAe,EAAE,CAAC;MAClBpX,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE3C,aAAa,EAAE,CAAC;MAChBkG,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0e,KAAK,EAAE,CAAC;MACR/a,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE2f,QAAQ,EAAE,CAAC;MACXpc,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAEiT,iBAAiB,EAAE,CAAC;MACpB1P,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEqE,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEoL,QAAQ,EAAE,CAAC;MACXjQ,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEqE,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAEmM,aAAa,EAAE,CAAC;MAChBhR,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEqE,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAE4F,cAAc,EAAE,CAAC;MACjBzK,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE+J,kBAAkB,EAAE,CAAC;MACrBvK,IAAI,EAAErD,SAAS;MACf6D,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMwc,UAAU,SAAS5W,UAAU,CAAC;EAQhC;EACA,IAAIkW,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACuB,SAAS;EACzB;EACA,IAAIvB,MAAMA,CAAC/a,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACsc,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,GAAGtc,KAAK;MACtB,IAAI,CAACuc,UAAU,CAAC/B,gBAAgB,CAAC,CAAC;IACtC;EACJ;EACA;;EAYA;AACJ;AACA;AACA;EACI,IAAIgC,cAAcA,CAAA,EAAG;IACjB,OAAQ,IAAI,CAAC3kB,QAAQ,IACjB,IAAI,CAACU,aAAa,IAClB,IAAI,CAACgkB,UAAU,CAAChkB,aAAa,IAC7B,CAAC,CAAC,IAAI,CAACkkB,YAAY,CAAC5kB,QAAQ;EACpC;EACA;;EAEAsG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IAACC,eAAA,qBA1CC1D,MAAM,CAACyf,SAAS,CAAC;IAAA/b,eAAA,qBACjB1D,MAAM,CAACW,UAAU,CAAC;IAAA+C,eAAA,wBACf1D,MAAM,CAACX,YAAY,CAAC;IAAAqE,eAAA,qBACvB,IAAI7B,OAAO,CAAC,CAAC;IAC1B;IAAA6B,eAAA,oBACY,KAAK;IAAAA,eAAA,oBACLhC,QAAQ,CAAC,MAAM,IAAI,CAACmgB,UAAU,CAAC7B,YAAY,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC1M,QAAQ,GAAG,CAAC,CAAC,CAAC;IAAA5P,eAAA,mBAY7E,KAAK;IAChB;IAAAA,eAAA,wBACgB,KAAK;IAAAA,eAAA,mBACV,CAAC;IACZ;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA;IAAAA,eAAA,aAkBK1D,MAAM,CAACb,YAAY,CAAC,CAACqc,KAAK,CAAC,eAAe,CAAC;IAG5Cxb,MAAM,CAAC8C,sBAAsB,CAAC,CAAC4C,IAAI,CAAC1C,uBAAuB,CAAC;IAC5D,MAAMgf,mBAAmB,GAAGhiB,MAAM,CAACqD,yBAAyB,EAAE;MAC1DwB,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,MAAMyO,QAAQ,GAAGtT,MAAM,CAAC,IAAI2B,kBAAkB,CAAC,UAAU,CAAC,EAAE;MAAEkD,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC/E,MAAMgb,aAAa,GAAG7f,MAAM,CAACgB,qBAAqB,EAAE;MAAE6D,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,IAAI,CAACkd,YAAY,GAAGC,mBAAmB,IAAI,CAAC,CAAC;IAC7C,IAAI,CAAC1O,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG2O,QAAQ,CAAC3O,QAAQ,CAAC,IAAI,CAAC;IAC9D,IAAIuM,aAAa,KAAK,gBAAgB,EAAE;MACpC,IAAI,CAACkC,YAAY,CAACG,SAAS,GAAG;QAAEC,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC;IACvE;IACA,IAAI,CAACP,UAAU,CAAChH,mBAAmB,CAC9B7L,IAAI,CAAC1M,SAAS,CAAC,IAAI,CAAC2M,UAAU,CAAC,CAAC,CAChCgB,SAAS,CAAC7S,kBAAkB,IAAI;MACjC,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAChD,CAAC,CAAC;EACN;EACA;EACAwP,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAClD,UAAU,CAACC,aAAa,CAACiD,KAAK,CAAC,CAAC;EACzC;EACAwB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACiU,aAAa,CAACC,OAAO,CAAC,IAAI,CAAC5Y,UAAU,CAAC;EAC/C;EACA1D,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiJ,UAAU,CAAClJ,IAAI,CAAC,CAAC;IACtB,IAAI,CAACkJ,UAAU,CAAChJ,QAAQ,CAAC,CAAC;IAC1B,KAAK,CAACD,WAAW,CAAC,CAAC;IACnB,IAAI,CAACqc,aAAa,CAACE,cAAc,CAAC,IAAI,CAAC7Y,UAAU,CAAC;EACtD;EACA8Y,YAAYA,CAAA,EAAG;IACX;IACA;IACA,IAAI,CAACX,UAAU,CAACrP,UAAU,GAAG,IAAI,CAACqP,UAAU,CAAC7Y,MAAM,CAACqK,OAAO,CAAC,CAAC,CAACoP,OAAO,CAAC,IAAI,CAAC;EAC/E;EACArQ,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAIA,KAAK,CAACE,OAAO,KAAK/S,KAAK,IAAI6S,KAAK,CAACE,OAAO,KAAK9S,KAAK,EAAE;MACpD,IAAI,IAAI,CAACtC,QAAQ,EAAE;QACfkV,KAAK,CAAC6D,cAAc,CAAC,CAAC;MAC1B,CAAC,MACI,IAAI,IAAI,CAAC2L,UAAU,CAAC1B,QAAQ,EAAE;QAC/B;QACA;QACA,IAAI9N,KAAK,CAACE,OAAO,KAAK/S,KAAK,EAAE;UACzB6S,KAAK,CAAC6D,cAAc,CAAC,CAAC;QAC1B;QACA,IAAI,CAACxM,UAAU,CAACC,aAAa,CAAC+Y,KAAK,CAAC,CAAC;MACzC;IACJ;EACJ;EACAC,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACf,OAAO,IAAI,CAACf,UAAU,CAAC1B,QAAQ,IAAAyC,qBAAA,GACzB,IAAI,CAACf,UAAU,CAAC1B,QAAQ,cAAAyC,qBAAA,uBAAxBA,qBAAA,CAA0Blb,EAAE,GAC5B,IAAI,CAACgC,UAAU,CAACC,aAAa,CAAC6W,YAAY,CAAC,eAAe,CAAC;EACrE;EACAqC,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAAChB,UAAU,CAAC1B,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACE,MAAM,GAAG,MAAM,GAAG,OAAO;IACzC,CAAC,MACI;MACD,OAAO,IAAI,CAAC3W,UAAU,CAACC,aAAa,CAAC6W,YAAY,CAAC,eAAe,CAAC;IACtE;EACJ;EACAsC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACzC,MAAM,IAAI,CAAC,IAAI,CAACwB,UAAU,CAAC1B,QAAQ,GAAG,MAAM,GAAG,IAAI;EACnE;EACAI,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACsB,UAAU,CAAC1B,QAAQ,GAAG,KAAK,GAAG,IAAI,CAACzW,UAAU,CAACC,aAAa,CAAC6W,YAAY,CAAC,MAAM,CAAC;EAChG;AAGJ;AAACuC,WAAA,GAnHKhC,UAAU;AAAArd,eAAA,CAAVqd,UAAU,wBAAAiC,oBAAAnf,iBAAA;EAAA,YAAAA,iBAAA,IAiHuFkd,WAAU;AAAA;AAAArd,eAAA,CAjH3Gqd,UAAU,8BAz0DiE/mB,EAAE,CAAAyM,iBAAA;EAAA1C,IAAA,EA27DQgd,WAAU;EAAA/c,SAAA;EAAAqD,SAAA;EAAAC,QAAA;EAAAC,YAAA,WAAA0b,yBAAAxoB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA37DpBT,EAAE,CAAA6B,UAAA,mBAAAqnB,qCAAA;QAAA,OA27DQxoB,GAAA,CAAA8nB,YAAA,CAAa,CAAC;MAAA,CAAL,CAAC,qBAAAW,uCAAA1mB,MAAA;QAAA,OAAV/B,GAAA,CAAA0X,cAAA,CAAA3V,MAAqB,CAAC;MAAA,CAAb,CAAC;IAAA;IAAA,IAAAhC,EAAA;MA37DpBT,EAAE,CAAAqD,WAAA,kBA27DQ3C,GAAA,CAAAioB,gBAAA,CAAiB,CAAC,kBAAlBjoB,GAAA,CAAAooB,eAAA,CAAgB,CAAC,mBAAApoB,GAAA,CAAAyC,QAAA,mBAAjBzC,GAAA,CAAAmoB,gBAAA,CAAiB,CAAC,QAAAnoB,GAAA,CAAAgN,EAAA,cAAlBhN,GAAA,CAAA0oB,SAAA,CAAU,CAAC,UAAX1oB,GAAA,CAAA6lB,QAAA,CAAS,CAAC;MA37DpBvmB,EAAE,CAAAgD,WAAA,yBAAAtC,GAAA,CAAAyC,QA27DiB,CAAC,oBAAAzC,GAAA,CAAA2lB,MAAD,CAAC;IAAA;EAAA;EAAA5Y,MAAA;IAAA4Y,MAAA,0BAAuGjgB,gBAAgB;IAAAjD,QAAA,8BAAsCiD,gBAAgB;IAAAvC,aAAA,wCAAqDuC,gBAAgB;IAAAkT,QAAA,8BAAuChO,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGnE,eAAe,CAACmE,KAAK,CAAE;IAAAoC,EAAA;EAAA;EAAAC,QAAA;EAAA1D,QAAA,GA37DnWjK,EAAE,CAAAkL,0BAAA;EAAAgc,KAAA,EAAAjiB,IAAA;EAAA4I,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAyO,MAAA;EAAAxO,QAAA,WAAAob,qBAAA5oB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAmO,eAAA;MAAFnO,EAAE,CAAA2C,SAAA,aA27Ds8B,CAAC,YAAyJ,CAAC;MA37DnmC3C,EAAE,CAAA4B,cAAA,aA27DqoC,CAAC,aAAuC,CAAC;MA37DhrC5B,EAAE,CAAAC,YAAA,EA27D4sC,CAAC;MA37D/sCD,EAAE,CAAA4C,YAAA,CA27DutC,CAAC,CAAQ,CAAC;IAAA;IAAA,IAAAnC,EAAA;MA37DnuCT,EAAE,CAAA4D,SAAA,CA27D+iC,CAAC;MA37DljC5D,EAAE,CAAAmB,UAAA,qBAAAT,GAAA,CAAAgP,UAAA,CAAAC,aA27D+iC,CAAC,sBAAAjP,GAAA,CAAAonB,cAAyC,CAAC;IAAA;EAAA;EAAAzK,YAAA,GAAkpHlU,SAAS;EAAAmU,MAAA;EAAAjP,aAAA;EAAAC,eAAA;AAAA;AAEx0J;EAAA,QAAAjE,SAAA,oBAAAA,SAAA,KA77DiFrK,EAAE,CAAAsK,iBAAA,CA67DQyc,UAAU,EAAc,CAAC;IACxGhd,IAAI,EAAE1D,SAAS;IACfkE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,8BAA8B;MAAEmD,QAAQ,EAAE,YAAY;MAAEW,eAAe,EAAEhI,uBAAuB,CAACgjB,MAAM;MAAEjb,aAAa,EAAE9H,iBAAiB,CAACiI,IAAI;MAAEC,IAAI,EAAE;QAC7J,OAAO,EAAE,8CAA8C;QACvD,sBAAsB,EAAE,oBAAoB;QAC5C,qBAAqB,EAAE,mBAAmB;QAC1C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,oBAAoB;QAC5C,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,aAAa;QAChC,aAAa,EAAE,YAAY;QAC3B,8BAA8B,EAAE,UAAU;QAC1C,yBAAyB,EAAE,QAAQ;QACnC,SAAS,EAAE,gBAAgB;QAC3B,WAAW,EAAE;MACjB,CAAC;MAAE8O,OAAO,EAAE,CAACpU,SAAS,CAAC;MAAE8E,QAAQ,EAAE,uUAAuU;MAAEqP,MAAM,EAAE,CAAC,88GAA88G;IAAE,CAAC;EACl1H,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE+I,MAAM,EAAE,CAAC;MACjDtc,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEjD,QAAQ,EAAE,CAAC;MACX4G,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEvC,aAAa,EAAE,CAAC;MAChBkG,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QAAEmE,SAAS,EAAEtI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkT,QAAQ,EAAE,CAAC;MACXvP,IAAI,EAAEvD,KAAK;MACX+D,IAAI,EAAE,CAAC;QACCmE,SAAS,EAAGpD,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGnE,eAAe,CAACmE,KAAK;MACpE,CAAC;IACT,CAAC,CAAC;IAAEoC,EAAE,EAAE,CAAC;MACL3D,IAAI,EAAEvD;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAM+iB,cAAc,CAAC;EAAA9f,YAAA;IACjB;IAAAC,eAAA,aACK1D,MAAM,CAACb,YAAY,CAAC,CAACqc,KAAK,CAAC,oBAAoB,CAAC;IACrD;IAAA9X,eAAA;EAAA;AAIJ;AAAC8f,eAAA,GAPKD,cAAc;AAAA7f,eAAA,CAAd6f,cAAc,wBAAAE,wBAAA5f,iBAAA;EAAA,YAAAA,iBAAA,IAKmF0f,eAAc;AAAA;AAAA7f,eAAA,CAL/G6f,cAAc,8BAj+D6DvpB,EAAE,CAAAyM,iBAAA;EAAA1C,IAAA,EAu+DQwf,eAAc;EAAAvf,SAAA;EAAAqD,SAAA,WAAyG,UAAU;EAAAC,QAAA;EAAAC,YAAA,WAAAmc,6BAAAjpB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAv+D3IT,EAAE,CAAAqD,WAAA,oBAAA3C,GAAA,CAAA4lB,YAAA,QAAA5lB,GAAA,CAAAgN,EAAA;IAAA;EAAA;EAAAD,MAAA;IAAAC,EAAA;EAAA;EAAAC,QAAA;EAAAE,kBAAA,EAAAC,GAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,QAAA,WAAA0b,yBAAAlpB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFT,EAAE,CAAAmO,eAAA;MAAFnO,EAAE,CAAAC,YAAA,EAu+DgV,CAAC;IAAA;EAAA;EAAAoO,aAAA;EAAAC,eAAA;AAAA;AAEpa;EAAA,QAAAjE,SAAA,oBAAAA,SAAA,KAz+DiFrK,EAAE,CAAAsK,iBAAA,CAy+DQif,cAAc,EAAc,CAAC;IAC5Gxf,IAAI,EAAE1D,SAAS;IACfkE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BmD,QAAQ,EAAE,gBAAgB;MAC1BM,QAAQ,EAAE,2BAA2B;MACrCQ,IAAI,EAAE;QACF,wBAAwB,EAAE,cAAc;QACxC,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,uBAAuB;QAChC,MAAM,EAAE;MACZ,CAAC;MACDJ,aAAa,EAAE9H,iBAAiB,CAACiI,IAAI;MACrCF,eAAe,EAAEhI,uBAAuB,CAACgjB;IAC7C,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE5b,EAAE,EAAE,CAAC;MACnB3D,IAAI,EAAEvD;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMojB,aAAa,CAAC;AAkBnBC,cAAA,GAlBKD,aAAa;AAAAlgB,eAAA,CAAbkgB,aAAa,wBAAAE,uBAAAjgB,iBAAA;EAAA,YAAAA,iBAAA,IACoF+f,cAAa;AAAA;AAAAlgB,eAAA,CAD9GkgB,aAAa,8BA5/D8D5pB,EAAE,CAAA+pB,gBAAA;EAAAhgB,IAAA,EA8/DqB6f,cAAa;EAAArM,OAAA,GAAYjU,eAAe,EACpIE,aAAa,EACboB,WAAW,EACXQ,MAAM,EACNwV,WAAW,EACX6E,SAAS,EACT8D,cAAc,EACdxC,UAAU;EAAAiD,OAAA,GAAa1gB,eAAe,EACtCE,aAAa,EACboB,WAAW,EACXQ,MAAM,EACNwV,WAAW,EACX6E,SAAS,EACT8D,cAAc,EACdxC,UAAU;AAAA;AAAArd,eAAA,CAhBhBkgB,aAAa,8BA5/D8D5pB,EAAE,CAAAiqB,gBAAA;EAAA1M,OAAA,GA6gE8CjU,eAAe,EAAEA,eAAe;AAAA;AAEjK;EAAA,QAAAe,SAAA,oBAAAA,SAAA,KA/gEiFrK,EAAE,CAAAsK,iBAAA,CA+gEQsf,aAAa,EAAc,CAAC;IAC3G7f,IAAI,EAAEnC,QAAQ;IACd2C,IAAI,EAAE,CAAC;MACCgT,OAAO,EAAE,CACLjU,eAAe,EACfE,aAAa,EACboB,WAAW,EACXQ,MAAM,EACNwV,WAAW,EACX6E,SAAS,EACT8D,cAAc,EACdxC,UAAU,CACb;MACDiD,OAAO,EAAE,CACL1gB,eAAe,EACfE,aAAa,EACboB,WAAW,EACXQ,MAAM,EACNwV,WAAW,EACX6E,SAAS,EACT8D,cAAc,EACdxC,UAAU;IAElB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmD,iBAAiB,GAAG;EACtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,YAAY,EAAE;IACVpgB,IAAI,EAAE,CAAC;IACPqgB,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE,CACT;MACItgB,IAAI,EAAE,CAAC;MACPqgB,IAAI,EAAE,uDAAuD;MAC7D9M,MAAM,EAAE;QACJvT,IAAI,EAAE,CAAC;QACPuT,MAAM,EAAE;UAAE5O,SAAS,EAAE,MAAM;UAAE4b,UAAU,EAAE;QAAU,CAAC;QACpDC,MAAM,EAAE;MACZ;IACJ,CAAC,EACD;MACIxgB,IAAI,EAAE,CAAC;MACPqgB,IAAI,EAAE,MAAM;MACZ9M,MAAM,EAAE;QACJvT,IAAI,EAAE,CAAC;QACPuT,MAAM,EAAE;UACJ5O,SAAS,EAAE,0BAA0B;UACrCyT,SAAS,EAAE,KAAK;UAChBmI,UAAU,EAAE;QAChB,CAAC;QACDC,MAAM,EAAE;MACZ;IACJ,CAAC,EACD;MACIxgB,IAAI,EAAE,CAAC;MACPqgB,IAAI,EAAE,OAAO;MACb9M,MAAM,EAAE;QACJvT,IAAI,EAAE,CAAC;QACPuT,MAAM,EAAE;UACJ5O,SAAS,EAAE,yBAAyB;UACpCyT,SAAS,EAAE,KAAK;UAChBmI,UAAU,EAAE;QAChB,CAAC;QACDC,MAAM,EAAE;MACZ;IACJ,CAAC,EACD;MACIxgB,IAAI,EAAE,CAAC;MACPygB,IAAI,EAAE,wDAAwD;MAC9DtC,SAAS,EAAE;QACPne,IAAI,EAAE,CAAC;QACPuT,MAAM,EAAE,IAAI;QACZmN,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACI3gB,IAAI,EAAE,CAAC;MACPygB,IAAI,EAAE,4BAA4B;MAClCtC,SAAS,EAAE,CACP;QACIne,IAAI,EAAE,CAAC;QACPuT,MAAM,EAAE;UAAE5O,SAAS,EAAE,0BAA0B;UAAE4b,UAAU,EAAE;QAAS,CAAC;QACvEC,MAAM,EAAE;MACZ,CAAC,EACD;QACIxgB,IAAI,EAAE,CAAC;QACPuT,MAAM,EAAE,IAAI;QACZmN,OAAO,EAAE;MACb,CAAC,CACJ;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACI3gB,IAAI,EAAE,CAAC;MACPygB,IAAI,EAAE,6BAA6B;MACnCtC,SAAS,EAAE,CACP;QACIne,IAAI,EAAE,CAAC;QACPuT,MAAM,EAAE;UAAE5O,SAAS,EAAE,yBAAyB;UAAE4b,UAAU,EAAE;QAAS,CAAC;QACtEC,MAAM,EAAE;MACZ,CAAC,EACD;QACIxgB,IAAI,EAAE,CAAC;QACPuT,MAAM,EAAE,IAAI;QACZmN,OAAO,EAAE;MACb,CAAC,CACJ;MACDC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAAS/f,OAAO,EAAE8S,eAAe,EAAElU,eAAe,EAAE4B,aAAa,EAAET,aAAa,EAAEqE,SAAS,EAAEwE,qBAAqB,EAAEnI,MAAM,EAAEuS,UAAU,EAAED,gBAAgB,EAAEiG,iBAAiB,EAAEna,aAAa,EAAEoX,WAAW,EAAE3E,YAAY,EAAErR,WAAW,EAAE+H,kBAAkB,EAAEoU,UAAU,EAAEtB,SAAS,EAAE8D,cAAc,EAAEK,aAAa,EAAEpX,uBAAuB,EAAEJ,+BAA+B,EAAE8X,iBAAiB;AACzX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}