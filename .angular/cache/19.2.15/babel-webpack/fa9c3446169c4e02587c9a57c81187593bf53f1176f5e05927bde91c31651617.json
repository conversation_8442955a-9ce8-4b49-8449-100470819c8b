{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _CdkDialogContainer, _Dialog, _DialogModule;\nfunction _CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZone, Renderer2, ChangeDetectorRef, Injector, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, defer, of } from 'rxjs';\nimport { B as BasePortalOutlet, f as CdkPortalOutlet, C as ComponentPortal, T as TemplatePortal, h as PortalModule } from './portal-directives-Bw5woq8I.mjs';\nexport { d as ɵɵCdkPortal, g as ɵɵPortalHostDirective, e as ɵɵTemplatePortalDirective } from './portal-directives-Bw5woq8I.mjs';\nimport { F as FocusTrapFactory, I as InteractivityChecker, A as A11yModule } from './a11y-module-BYox5gpI.mjs';\nimport { c as OverlayRef, a as Overlay, O as OverlayContainer, f as OverlayConfig, m as OverlayModule } from './overlay-module-BUj0D19H.mjs';\nimport { F as FocusMonitor } from './focus-monitor-e2l_RpN3.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { startWith, take } from 'rxjs/operators';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport './style-loader-Cu9AvjH9.mjs';\nimport './private.mjs';\nimport './breakpoints-observer-CljOfYGy.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './element-x4z00URv.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './test-environment-CT0XxPyp.mjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './scrolling.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\nimport './fake-event-detection-DWOdFTFz.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n  constructor() {\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This affects what is available for injection and the change detection order for the\n     * component instantiated inside of the dialog. This does not affect where the dialog\n     * content will be rendered.\n     */\n    _defineProperty(this, \"viewContainerRef\", void 0);\n    /**\n     * Injector used for the instantiation of the component to be attached. If provided,\n     * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n     */\n    _defineProperty(this, \"injector\", void 0);\n    /** ID for the dialog. If omitted, a unique one will be generated. */\n    _defineProperty(this, \"id\", void 0);\n    /** The ARIA role of the dialog element. */\n    _defineProperty(this, \"role\", 'dialog');\n    /** Optional CSS class or classes applied to the overlay panel. */\n    _defineProperty(this, \"panelClass\", '');\n    /** Whether the dialog has a backdrop. */\n    _defineProperty(this, \"hasBackdrop\", true);\n    /** Optional CSS class or classes applied to the overlay backdrop. */\n    _defineProperty(this, \"backdropClass\", '');\n    /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n    _defineProperty(this, \"disableClose\", false);\n    /** Width of the dialog. */\n    _defineProperty(this, \"width\", '');\n    /** Height of the dialog. */\n    _defineProperty(this, \"height\", '');\n    /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n    _defineProperty(this, \"minWidth\", void 0);\n    /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n    _defineProperty(this, \"minHeight\", void 0);\n    /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n    _defineProperty(this, \"maxWidth\", void 0);\n    /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n    _defineProperty(this, \"maxHeight\", void 0);\n    /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n    _defineProperty(this, \"positionStrategy\", void 0);\n    /** Data being injected into the child component. */\n    _defineProperty(this, \"data\", null);\n    /** Layout direction for the dialog's content. */\n    _defineProperty(this, \"direction\", void 0);\n    /** ID of the element that describes the dialog. */\n    _defineProperty(this, \"ariaDescribedBy\", null);\n    /** ID of the element that labels the dialog. */\n    _defineProperty(this, \"ariaLabelledBy\", null);\n    /** Dialog label applied via `aria-label` */\n    _defineProperty(this, \"ariaLabel\", null);\n    /**\n     * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n     * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n     * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n     */\n    _defineProperty(this, \"ariaModal\", false);\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    _defineProperty(this, \"autoFocus\", 'first-tabbable');\n    /**\n     * Whether the dialog should restore focus to the previously-focused element upon closing.\n     * Has the following behavior based on the type that is passed in:\n     * - `boolean` - when true, will return focus to the element that was focused before the dialog\n     *    was opened, otherwise won't restore focus at all.\n     * - `string` - focus will be restored to the first element that matches the CSS selector.\n     * - `HTMLElement` - focus will be restored to the specific element.\n     */\n    _defineProperty(this, \"restoreFocus\", true);\n    /**\n     * Scroll strategy to be used for the dialog. This determines how\n     * the dialog responds to scrolling underneath the panel element.\n     */\n    _defineProperty(this, \"scrollStrategy\", void 0);\n    /**\n     * Whether the dialog should close when the user navigates backwards or forwards through browser\n     * history. This does not apply to navigation via anchor element unless using URL-hash based\n     * routing (`HashLocationStrategy` in the Angular router).\n     */\n    _defineProperty(this, \"closeOnNavigation\", true);\n    /**\n     * Whether the dialog should close when the dialog service is destroyed. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead.\n     */\n    _defineProperty(this, \"closeOnDestroy\", true);\n    /**\n     * Whether the dialog should close when the underlying overlay is detached. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n     * external detachment can happen as a result of a scroll strategy triggering it or when the\n     * browser location changes.\n     */\n    _defineProperty(this, \"closeOnOverlayDetachments\", true);\n    /**\n     * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n     * @deprecated No longer used. Will be removed.\n     * @breaking-change 20.0.0\n     */\n    _defineProperty(this, \"componentFactoryResolver\", void 0);\n    /**\n     * Providers that will be exposed to the contents of the dialog. Can also\n     * be provided as a function in order to generate the providers lazily.\n     */\n    _defineProperty(this, \"providers\", void 0);\n    /**\n     * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n     * A configuration object can be passed in to customize the providers that will be exposed\n     * to the dialog container.\n     */\n    _defineProperty(this, \"container\", void 0);\n    /**\n     * Context that will be passed to template-based dialogs.\n     * A function can be passed in to resolve the context lazily.\n     */\n    _defineProperty(this, \"templateContext\", void 0);\n  }\n}\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n  constructor() {\n    super();\n    // Callback is primarily for some internal tests\n    // that were instantiating the dialog container manually.\n    _defineProperty(this, \"_elementRef\", inject(ElementRef));\n    _defineProperty(this, \"_focusTrapFactory\", inject(FocusTrapFactory));\n    _defineProperty(this, \"_config\", void 0);\n    _defineProperty(this, \"_interactivityChecker\", inject(InteractivityChecker));\n    _defineProperty(this, \"_ngZone\", inject(NgZone));\n    _defineProperty(this, \"_overlayRef\", inject(OverlayRef));\n    _defineProperty(this, \"_focusMonitor\", inject(FocusMonitor));\n    _defineProperty(this, \"_renderer\", inject(Renderer2));\n    _defineProperty(this, \"_changeDetectorRef\", inject(ChangeDetectorRef));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_platform\", inject(Platform));\n    _defineProperty(this, \"_document\", inject(DOCUMENT, {\n      optional: true\n    }));\n    /** The portal outlet inside of this container into which the dialog content will be loaded. */\n    _defineProperty(this, \"_portalOutlet\", void 0);\n    _defineProperty(this, \"_focusTrapped\", new Subject());\n    /** The class that traps and manages focus within the dialog. */\n    _defineProperty(this, \"_focusTrap\", null);\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n    _defineProperty(this, \"_elementFocusedBeforeDialogWasOpened\", null);\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n    _defineProperty(this, \"_closeInteractionType\", null);\n    /**\n     * Queue of the IDs of the dialog's label element, based on their definition order. The first\n     * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n     * where there are two or more titles in the DOM at a time and the first one is destroyed while\n     * the rest are present.\n     */\n    _defineProperty(this, \"_ariaLabelledByQueue\", []);\n    _defineProperty(this, \"_isDestroyed\", false);\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    _defineProperty(this, \"attachDomPortal\", portal => {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._contentAttached();\n      return result;\n    });\n    this._config = inject(DialogConfig, {\n      optional: true\n    }) || new DialogConfig();\n    if (this._config.ariaLabelledBy) {\n      this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n    }\n  }\n  _addAriaLabelledBy(id) {\n    this._ariaLabelledByQueue.push(id);\n    this._changeDetectorRef.markForCheck();\n  }\n  _removeAriaLabelledBy(id) {\n    const index = this._ariaLabelledByQueue.indexOf(id);\n    if (index > -1) {\n      this._ariaLabelledByQueue.splice(index, 1);\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _contentAttached() {\n    this._initializeFocusTrap();\n    this._handleBackdropClicks();\n    this._captureInitialFocus();\n  }\n  /**\n   * Can be used by child classes to customize the initial focus\n   * capturing behavior (e.g. if it's tied to an animation).\n   */\n  _captureInitialFocus() {\n    this._trapFocus();\n  }\n  ngOnDestroy() {\n    this._focusTrapped.complete();\n    this._isDestroyed = true;\n    this._restoreFocus();\n  }\n  /**\n   * Attach a ComponentPortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachComponentPortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  /**\n   * Attach a TemplatePortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachTemplatePortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n  /** Captures focus if it isn't already inside the dialog. */\n  _recaptureFocus() {\n    if (!this._containsFocus()) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          deregisterBlur();\n          deregisterMousedown();\n          element.removeAttribute('tabindex');\n        };\n        const deregisterBlur = this._renderer.listen(element, 'blur', callback);\n        const deregisterMousedown = this._renderer.listen(element, 'mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n   * cannot be moved then focus will go to the dialog container.\n   */\n  _trapFocus(options) {\n    if (this._isDestroyed) {\n      return;\n    }\n    // If were to attempt to focus immediately, then the content of the dialog would not yet be\n    // ready in instances where change detection has to run first. To deal with this, we simply\n    // wait until after the next render.\n    afterNextRender(() => {\n      var _this$_focusTrap;\n      const element = this._elementRef.nativeElement;\n      switch (this._config.autoFocus) {\n        case false:\n        case 'dialog':\n          // Ensure that focus is on the dialog container. It's possible that a different\n          // component tried to move focus while the open animation was running. See:\n          // https://github.com/angular/components/issues/16215. Note that we only want to do this\n          // if the focus isn't inside the dialog already, because it's possible that the consumer\n          // turned off `autoFocus` in order to move focus themselves.\n          if (!this._containsFocus()) {\n            element.focus(options);\n          }\n          break;\n        case true:\n        case 'first-tabbable':\n          const focusedSuccessfully = (_this$_focusTrap = this._focusTrap) === null || _this$_focusTrap === void 0 ? void 0 : _this$_focusTrap.focusInitialElement(options);\n          // If we weren't able to find a focusable element in the dialog, then focus the dialog\n          // container instead.\n          if (!focusedSuccessfully) {\n            this._focusDialogContainer(options);\n          }\n          break;\n        case 'first-heading':\n          this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]', options);\n          break;\n        default:\n          this._focusByCssSelector(this._config.autoFocus, options);\n          break;\n      }\n      this._focusTrapped.next();\n    }, {\n      injector: this._injector\n    });\n  }\n  /** Restores focus to the element that was focused before the dialog opened. */\n  _restoreFocus() {\n    const focusConfig = this._config.restoreFocus;\n    let focusTargetElement = null;\n    if (typeof focusConfig === 'string') {\n      focusTargetElement = this._document.querySelector(focusConfig);\n    } else if (typeof focusConfig === 'boolean') {\n      focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n    } else if (focusConfig) {\n      focusTargetElement = focusConfig;\n    }\n    // We need the extra check, because IE can set the `activeElement` to null in some cases.\n    if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n      const activeElement = _getFocusedElementPierceShadowDom();\n      const element = this._elementRef.nativeElement;\n      // Make sure that focus is still inside the dialog or is on the body (usually because a\n      // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n      // the consumer moved it themselves before the animation was done, in which case we shouldn't\n      // do anything.\n      if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n        if (this._focusMonitor) {\n          this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n          this._closeInteractionType = null;\n        } else {\n          focusTargetElement.focus();\n        }\n      }\n    }\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n  }\n  /** Focuses the dialog container. */\n  _focusDialogContainer(options) {\n    // Note that there is no focus method when rendering on the server.\n    if (this._elementRef.nativeElement.focus) {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n  /** Returns whether focus is inside the dialog. */\n  _containsFocus() {\n    const element = this._elementRef.nativeElement;\n    const activeElement = _getFocusedElementPierceShadowDom();\n    return element === activeElement || element.contains(activeElement);\n  }\n  /** Sets up the focus trap. */\n  _initializeFocusTrap() {\n    if (this._platform.isBrowser) {\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n      // Save the previously focused element. This element will be re-focused\n      // when the dialog closes.\n      if (this._document) {\n        this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n      }\n    }\n  }\n  /** Sets up the listener that handles clicks on the dialog backdrop. */\n  _handleBackdropClicks() {\n    // Clicking on the backdrop will move focus out of dialog.\n    // Recapture it if closing via the backdrop is disabled.\n    this._overlayRef.backdropClick().subscribe(() => {\n      if (this._config.disableClose) {\n        this._recaptureFocus();\n      }\n    });\n  }\n}\n_CdkDialogContainer = CdkDialogContainer;\n_defineProperty(CdkDialogContainer, \"\\u0275fac\", function _CdkDialogContainer_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CdkDialogContainer)();\n});\n_defineProperty(CdkDialogContainer, \"\\u0275cmp\", /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: _CdkDialogContainer,\n  selectors: [[\"cdk-dialog-container\"]],\n  viewQuery: function _CdkDialogContainer_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n    }\n  },\n  hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n  hostVars: 6,\n  hostBindings: function _CdkDialogContainer_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkPortalOutlet\", \"\"]],\n  template: function _CdkDialogContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, _CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n    }\n  },\n  dependencies: [CdkPortalOutlet],\n  styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"],\n  encapsulation: 2\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'cdk-dialog-container',\n        'tabindex': '-1',\n        '[attr.id]': '_config.id || null',\n        '[attr.role]': '_config.role',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null'\n      },\n      template: \"<ng-template cdkPortalOutlet />\\n\",\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"]\n    }]\n  }], () => [], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n  constructor(overlayRef, config) {\n    _defineProperty(this, \"overlayRef\", void 0);\n    _defineProperty(this, \"config\", void 0);\n    /**\n     * Instance of component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    _defineProperty(this, \"componentInstance\", void 0);\n    /**\n     * `ComponentRef` of the component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    _defineProperty(this, \"componentRef\", void 0);\n    /** Instance of the container that is rendering out the dialog content. */\n    _defineProperty(this, \"containerInstance\", void 0);\n    /** Whether the user is allowed to close the dialog. */\n    _defineProperty(this, \"disableClose\", void 0);\n    /** Emits when the dialog has been closed. */\n    _defineProperty(this, \"closed\", new Subject());\n    /** Emits when the backdrop of the dialog is clicked. */\n    _defineProperty(this, \"backdropClick\", void 0);\n    /** Emits when on keyboard events within the dialog. */\n    _defineProperty(this, \"keydownEvents\", void 0);\n    /** Emits on pointer events that happen outside of the dialog. */\n    _defineProperty(this, \"outsidePointerEvents\", void 0);\n    /** Unique ID for the dialog. */\n    _defineProperty(this, \"id\", void 0);\n    /** Subscription to external detachments of the dialog. */\n    _defineProperty(this, \"_detachSubscription\", void 0);\n    this.overlayRef = overlayRef;\n    this.config = config;\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      }\n    });\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result, options) {\n    if (this.containerInstance) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = (options === null || options === void 0 ? void 0 : options.focusOrigin) || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.block();\n  }\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nclass Dialog {\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  /**\n   * Stream that emits when all open dialog have finished closing.\n   * Will emit on subscribe if there are no open dialogs to begin with.\n   */\n\n  constructor() {\n    _defineProperty(this, \"_overlay\", inject(Overlay));\n    _defineProperty(this, \"_injector\", inject(Injector));\n    _defineProperty(this, \"_defaultOptions\", inject(DEFAULT_DIALOG_CONFIG, {\n      optional: true\n    }));\n    _defineProperty(this, \"_parentDialog\", inject(Dialog, {\n      optional: true,\n      skipSelf: true\n    }));\n    _defineProperty(this, \"_overlayContainer\", inject(OverlayContainer));\n    _defineProperty(this, \"_idGenerator\", inject(_IdGenerator));\n    _defineProperty(this, \"_openDialogsAtThisLevel\", []);\n    _defineProperty(this, \"_afterAllClosedAtThisLevel\", new Subject());\n    _defineProperty(this, \"_afterOpenedAtThisLevel\", new Subject());\n    _defineProperty(this, \"_ariaHiddenElements\", new Map());\n    _defineProperty(this, \"_scrollStrategy\", inject(DIALOG_SCROLL_STRATEGY));\n    _defineProperty(this, \"afterAllClosed\", defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined))));\n  }\n  open(componentOrTemplateRef, config) {\n    const defaults = this._defaultOptions || new DialogConfig();\n    config = _objectSpread(_objectSpread({}, defaults), config);\n    config.id = config.id || this._idGenerator.getId('cdk-dialog-');\n    if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n    }\n    const overlayConfig = this._getOverlayConfig(config);\n    const overlayRef = this._overlay.create(overlayConfig);\n    const dialogRef = new DialogRef(overlayRef, config);\n    const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n    dialogRef.containerInstance = dialogContainer;\n    // If this is the first dialog that we're opening, hide all the non-overlay content.\n    if (!this.openDialogs.length) {\n      // Resolve this ahead of time, because some internal apps\n      // mock it out and depend on it being synchronous.\n      const overlayContainer = this._overlayContainer.getContainerElement();\n      if (dialogContainer._focusTrapped) {\n        dialogContainer._focusTrapped.pipe(take(1)).subscribe(() => {\n          this._hideNonDialogContentFromAssistiveTechnology(overlayContainer);\n        });\n      } else {\n        this._hideNonDialogContentFromAssistiveTechnology(overlayContainer);\n      }\n    }\n    this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n    this.openDialogs.push(dialogRef);\n    dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n    this.afterOpened.next(dialogRef);\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    reverseForEach(this.openDialogs, dialog => dialog.close());\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n    // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n    // determines when `aria-hidden` is removed from elements outside the dialog.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => {\n      // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n      if (dialog.config.closeOnDestroy === false) {\n        this._removeOpenDialog(dialog, false);\n      }\n    });\n    // Make a second pass and close the remaining dialogs. We do this second pass in order to\n    // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n    // that should be closed and dialogs that should not.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n    this._openDialogsAtThisLevel = [];\n  }\n  /**\n   * Creates an overlay config from a dialog config.\n   * @param config The dialog configuration.\n   * @returns The overlay configuration.\n   */\n  _getOverlayConfig(config) {\n    const state = new OverlayConfig({\n      positionStrategy: config.positionStrategy || this._overlay.position().global().centerHorizontally().centerVertically(),\n      scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n      panelClass: config.panelClass,\n      hasBackdrop: config.hasBackdrop,\n      direction: config.direction,\n      minWidth: config.minWidth,\n      minHeight: config.minHeight,\n      maxWidth: config.maxWidth,\n      maxHeight: config.maxHeight,\n      width: config.width,\n      height: config.height,\n      disposeOnNavigation: config.closeOnNavigation\n    });\n    if (config.backdropClass) {\n      state.backdropClass = config.backdropClass;\n    }\n    return state;\n  }\n  /**\n   * Attaches a dialog container to a dialog's already-created overlay.\n   * @param overlay Reference to the dialog's underlying overlay.\n   * @param config The dialog configuration.\n   * @returns A promise resolving to a ComponentRef for the attached container.\n   */\n  _attachContainer(overlay, dialogRef, config) {\n    var _config$viewContainer;\n    const userInjector = config.injector || ((_config$viewContainer = config.viewContainerRef) === null || _config$viewContainer === void 0 ? void 0 : _config$viewContainer.injector);\n    const providers = [{\n      provide: DialogConfig,\n      useValue: config\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }, {\n      provide: OverlayRef,\n      useValue: overlay\n    }];\n    let containerType;\n    if (config.container) {\n      if (typeof config.container === 'function') {\n        containerType = config.container;\n      } else {\n        containerType = config.container.type;\n        providers.push(...config.container.providers(config));\n      }\n    } else {\n      containerType = CdkDialogContainer;\n    }\n    const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n      parent: userInjector || this._injector,\n      providers\n    }));\n    const containerRef = overlay.attach(containerPortal);\n    return containerRef.instance;\n  }\n  /**\n   * Attaches the user-provided component to the already-created dialog container.\n   * @param componentOrTemplateRef The type of component being loaded into the dialog,\n   *     or a TemplateRef to instantiate as the content.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param config Configuration used to open the dialog.\n   */\n  _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n      let context = {\n        $implicit: config.data,\n        dialogRef\n      };\n      if (config.templateContext) {\n        context = _objectSpread(_objectSpread({}, context), typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext);\n      }\n      dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n    } else {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n      const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n      dialogRef.componentRef = contentRef;\n      dialogRef.componentInstance = contentRef.instance;\n    }\n  }\n  /**\n   * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n   * of a dialog to close itself and, optionally, to return a value.\n   * @param config Config object that is used to construct the dialog.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n   * dialog injector, if the user didn't provide a custom one.\n   * @returns The custom injector that can be used inside the dialog.\n   */\n  _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n    var _config$viewContainer2;\n    const userInjector = config.injector || ((_config$viewContainer2 = config.viewContainerRef) === null || _config$viewContainer2 === void 0 ? void 0 : _config$viewContainer2.injector);\n    const providers = [{\n      provide: DIALOG_DATA,\n      useValue: config.data\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }];\n    if (config.providers) {\n      if (typeof config.providers === 'function') {\n        providers.push(...config.providers(dialogRef, config, dialogContainer));\n      } else {\n        providers.push(...config.providers);\n      }\n    }\n    if (config.direction && (!userInjector || !userInjector.get(Directionality, null, {\n      optional: true\n    }))) {\n      providers.push({\n        provide: Directionality,\n        useValue: {\n          value: config.direction,\n          change: of()\n        }\n      });\n    }\n    return Injector.create({\n      parent: userInjector || fallbackInjector,\n      providers\n    });\n  }\n  /**\n   * Removes a dialog from the array of open dialogs.\n   * @param dialogRef Dialog to be removed.\n   * @param emitEvent Whether to emit an event if this is the last dialog.\n   */\n  _removeOpenDialog(dialogRef, emitEvent) {\n    const index = this.openDialogs.indexOf(dialogRef);\n    if (index > -1) {\n      this.openDialogs.splice(index, 1);\n      // If all the dialogs were closed, remove/restore the `aria-hidden`\n      // to a the siblings and emit to the `afterAllClosed` stream.\n      if (!this.openDialogs.length) {\n        this._ariaHiddenElements.forEach((previousValue, element) => {\n          if (previousValue) {\n            element.setAttribute('aria-hidden', previousValue);\n          } else {\n            element.removeAttribute('aria-hidden');\n          }\n        });\n        this._ariaHiddenElements.clear();\n        if (emitEvent) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    }\n  }\n  /** Hides all of the content that isn't an overlay from assistive technology. */\n  _hideNonDialogContentFromAssistiveTechnology(overlayContainer) {\n    // Ensure that the overlay container is attached to the DOM.\n    if (overlayContainer.parentElement) {\n      const siblings = overlayContainer.parentElement.children;\n      for (let i = siblings.length - 1; i > -1; i--) {\n        const sibling = siblings[i];\n        if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n          this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n          sibling.setAttribute('aria-hidden', 'true');\n        }\n      }\n    }\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n}\n_Dialog = Dialog;\n_defineProperty(Dialog, \"\\u0275fac\", function _Dialog_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Dialog)();\n});\n_defineProperty(Dialog, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _Dialog,\n  factory: _Dialog.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dialog, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n  while (i--) {\n    callback(items[i]);\n  }\n}\nclass DialogModule {}\n_DialogModule = DialogModule;\n_defineProperty(DialogModule, \"\\u0275fac\", function _DialogModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _DialogModule)();\n});\n_defineProperty(DialogModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _DialogModule,\n  imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n  exports: [\n  // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n  // don't have to remember to import it or be faced with an unhelpful error.\n  PortalModule, CdkDialogContainer]\n}));\n_defineProperty(DialogModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [Dialog],\n  imports: [OverlayModule, PortalModule, A11yModule,\n  // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n  // don't have to remember to import it or be faced with an unhelpful error.\n  PortalModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n      exports: [\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule, CdkDialogContainer],\n      providers: [Dialog]\n    }]\n  }], null, null);\n})();\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError, CdkPortalOutlet as ɵɵCdkPortalOutlet };\n//# sourceMappingURL=dialog.mjs.map", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "ElementRef", "NgZone", "Renderer2", "ChangeDetectorRef", "Injector", "afterNextRender", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "InjectionToken", "TemplateRef", "Injectable", "NgModule", "Subject", "defer", "of", "B", "BasePortalOutlet", "f", "CdkPortalOutlet", "C", "ComponentPortal", "T", "TemplatePortal", "h", "PortalModule", "d", "ɵɵCdkPortal", "g", "ɵɵPortalHostDirective", "e", "ɵɵTemplatePortalDirective", "F", "FocusTrapFactory", "I", "InteractivityChecker", "A", "A11yModule", "c", "OverlayRef", "a", "Overlay", "O", "OverlayContainer", "OverlayConfig", "m", "OverlayModule", "FocusMonitor", "P", "Platform", "_getFocusedElementPierceShadowDom", "ESCAPE", "hasModifierKey", "startWith", "take", "_", "_IdGenerator", "D", "Directionality", "DialogConfig", "constructor", "_defineProperty", "throwDialogContentAlreadyAttachedError", "Error", "CdkDialogContainer", "optional", "portal", "_portalOutlet", "has<PERSON>tta<PERSON>", "ngDevMode", "result", "attachDomPortal", "_contentAttached", "_config", "ariaLabelledBy", "_ariaLabelledByQueue", "push", "_addAriaLabelledBy", "id", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_removeAriaLabelledBy", "index", "indexOf", "splice", "_initializeFocusTrap", "_handleBackdropClicks", "_captureInitialFocus", "_trapFocus", "ngOnDestroy", "_focusTrapped", "complete", "_isDestroyed", "_restoreFocus", "attachComponentPortal", "attachTemplatePortal", "_recaptureFocus", "_containsFocus", "_forceFocus", "element", "options", "_interactivityC<PERSON>cker", "isFocusable", "tabIndex", "_ngZone", "runOutsideAngular", "callback", "deregisterBlur", "deregisterMousedown", "removeAttribute", "_renderer", "listen", "focus", "_focusByCssSelector", "selector", "elementToFocus", "_elementRef", "nativeElement", "querySelector", "_this$_focusTrap", "autoFocus", "focusedSuccessfully", "_focusTrap", "focusInitialElement", "_focusDialogContainer", "next", "injector", "_injector", "focusConfig", "restoreFocus", "focusTargetElement", "_document", "_elementFocusedBeforeDialogWasOpened", "activeElement", "body", "contains", "_focusMonitor", "focusVia", "_closeInteractionType", "destroy", "_platform", "<PERSON><PERSON><PERSON><PERSON>", "_focusTrapFactory", "create", "_overlayRef", "backdropClick", "subscribe", "disableClose", "_CdkDialogContainer", "_CdkDialogContainer_Factory", "__ngFactoryType__", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "_CdkDialogContainer_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "_CdkDialogContainer_HostBindings", "ɵɵattribute", "role", "ariaModal", "aria<PERSON><PERSON><PERSON>", "ariaDescribedBy", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "_CdkDialogContainer_Template", "ɵɵtemplate", "_CdkDialogContainer_ng_template_0_Template", "dependencies", "styles", "encapsulation", "ɵsetClassMetadata", "args", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "host", "static", "DialogRef", "overlayRef", "config", "keydownEvents", "outsidePointerEvents", "event", "keyCode", "preventDefault", "close", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "_detachSubscription", "detachments", "closeOnOverlayDetachments", "containerInstance", "closedSubject", "closed", "unsubscribe", "dispose", "componentInstance", "updatePosition", "updateSize", "width", "height", "addPanelClass", "classes", "removePanelClass", "DIALOG_SCROLL_STRATEGY", "providedIn", "factory", "overlay", "scrollStrategies", "block", "DIALOG_DATA", "DEFAULT_DIALOG_CONFIG", "DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY", "DIALOG_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "Dialog", "openDialogs", "_parentDialog", "_openDialogsAtThisLevel", "afterOpened", "_afterOpenedAtThisLevel", "skipSelf", "Map", "length", "_getAfterAllClosed", "pipe", "open", "componentOrTemplateRef", "defaults", "_defaultOptions", "_objectSpread", "_idGenerator", "getId", "getDialogById", "overlayConfig", "_getOverlayConfig", "_overlay", "dialogRef", "dialogContainer", "_attachC<PERSON>r", "overlayContainer", "_overlayContainer", "getContainerElement", "_hideNonDialogContentFromAssistiveTechnology", "_attach<PERSON><PERSON>og<PERSON><PERSON>nt", "_removeOpenDialog", "closeAll", "reverseForEach", "dialog", "find", "closeOnDestroy", "_afterAllClosedAtThisLevel", "state", "positionStrategy", "position", "global", "centerHorizontally", "centerVertically", "scrollStrategy", "_scrollStrategy", "panelClass", "hasBackdrop", "direction", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "disposeOnNavigation", "closeOnNavigation", "backdropClass", "_config$viewContainer", "userInjector", "viewContainerRef", "providers", "useValue", "containerType", "container", "containerPortal", "parent", "containerRef", "attach", "instance", "_createInjector", "context", "$implicit", "data", "templateContext", "contentRef", "componentRef", "fallbackInjector", "_config$viewContainer2", "get", "value", "change", "emitEvent", "_ariaHiddenElements", "for<PERSON>ach", "previousValue", "setAttribute", "clear", "parentElement", "siblings", "children", "i", "sibling", "nodeName", "hasAttribute", "set", "getAttribute", "_Dialog", "_Dialog_Factory", "ɵɵdefineInjectable", "token", "ɵfac", "items", "DialogModule", "_DialogModule", "_DialogModule_Factory", "ɵɵdefineNgModule", "exports", "ɵɵdefineInjector", "ɵɵCdkPortalOutlet"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/dialog.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ElementRef, Ng<PERSON><PERSON>, Renderer2, ChangeDetectorRef, Injector, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, defer, of } from 'rxjs';\nimport { B as BasePortalOutlet, f as CdkPortalOutlet, C as ComponentPortal, T as TemplatePortal, h as PortalModule } from './portal-directives-Bw5woq8I.mjs';\nexport { d as ɵɵCdkPortal, g as ɵɵPortalHostDirective, e as ɵɵTemplatePortalDirective } from './portal-directives-Bw5woq8I.mjs';\nimport { F as FocusTrapFactory, I as InteractivityChecker, A as A11yModule } from './a11y-module-BYox5gpI.mjs';\nimport { c as OverlayRef, a as Overlay, O as OverlayContainer, f as OverlayConfig, m as OverlayModule } from './overlay-module-BUj0D19H.mjs';\nimport { F as FocusMonitor } from './focus-monitor-e2l_RpN3.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { startWith, take } from 'rxjs/operators';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport './style-loader-Cu9AvjH9.mjs';\nimport './private.mjs';\nimport './breakpoints-observer-CljOfYGy.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './element-x4z00URv.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './test-environment-CT0XxPyp.mjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './scrolling.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\nimport './fake-event-detection-DWOdFTFz.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This affects what is available for injection and the change detection order for the\n     * component instantiated inside of the dialog. This does not affect where the dialog\n     * content will be rendered.\n     */\n    viewContainerRef;\n    /**\n     * Injector used for the instantiation of the component to be attached. If provided,\n     * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n     */\n    injector;\n    /** ID for the dialog. If omitted, a unique one will be generated. */\n    id;\n    /** The ARIA role of the dialog element. */\n    role = 'dialog';\n    /** Optional CSS class or classes applied to the overlay panel. */\n    panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    hasBackdrop = true;\n    /** Optional CSS class or classes applied to the overlay backdrop. */\n    backdropClass = '';\n    /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n    disableClose = false;\n    /** Width of the dialog. */\n    width = '';\n    /** Height of the dialog. */\n    height = '';\n    /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n    minWidth;\n    /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n    minHeight;\n    /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n    maxWidth;\n    /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n    maxHeight;\n    /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n    positionStrategy;\n    /** Data being injected into the child component. */\n    data = null;\n    /** Layout direction for the dialog's content. */\n    direction;\n    /** ID of the element that describes the dialog. */\n    ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    ariaLabelledBy = null;\n    /** Dialog label applied via `aria-label` */\n    ariaLabel = null;\n    /**\n     * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n     * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n     * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n     */\n    ariaModal = false;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the previously-focused element upon closing.\n     * Has the following behavior based on the type that is passed in:\n     * - `boolean` - when true, will return focus to the element that was focused before the dialog\n     *    was opened, otherwise won't restore focus at all.\n     * - `string` - focus will be restored to the first element that matches the CSS selector.\n     * - `HTMLElement` - focus will be restored to the specific element.\n     */\n    restoreFocus = true;\n    /**\n     * Scroll strategy to be used for the dialog. This determines how\n     * the dialog responds to scrolling underneath the panel element.\n     */\n    scrollStrategy;\n    /**\n     * Whether the dialog should close when the user navigates backwards or forwards through browser\n     * history. This does not apply to navigation via anchor element unless using URL-hash based\n     * routing (`HashLocationStrategy` in the Angular router).\n     */\n    closeOnNavigation = true;\n    /**\n     * Whether the dialog should close when the dialog service is destroyed. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead.\n     */\n    closeOnDestroy = true;\n    /**\n     * Whether the dialog should close when the underlying overlay is detached. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n     * external detachment can happen as a result of a scroll strategy triggering it or when the\n     * browser location changes.\n     */\n    closeOnOverlayDetachments = true;\n    /**\n     * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n     * @deprecated No longer used. Will be removed.\n     * @breaking-change 20.0.0\n     */\n    componentFactoryResolver;\n    /**\n     * Providers that will be exposed to the contents of the dialog. Can also\n     * be provided as a function in order to generate the providers lazily.\n     */\n    providers;\n    /**\n     * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n     * A configuration object can be passed in to customize the providers that will be exposed\n     * to the dialog container.\n     */\n    container;\n    /**\n     * Context that will be passed to template-based dialogs.\n     * A function can be passed in to resolve the context lazily.\n     */\n    templateContext;\n}\n\nfunction throwDialogContentAlreadyAttachedError() {\n    throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    _config;\n    _interactivityChecker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _overlayRef = inject(OverlayRef);\n    _focusMonitor = inject(FocusMonitor);\n    _renderer = inject(Renderer2);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT, { optional: true });\n    /** The portal outlet inside of this container into which the dialog content will be loaded. */\n    _portalOutlet;\n    _focusTrapped = new Subject();\n    /** The class that traps and manages focus within the dialog. */\n    _focusTrap = null;\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n    _elementFocusedBeforeDialogWasOpened = null;\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n    _closeInteractionType = null;\n    /**\n     * Queue of the IDs of the dialog's label element, based on their definition order. The first\n     * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n     * where there are two or more titles in the DOM at a time and the first one is destroyed while\n     * the rest are present.\n     */\n    _ariaLabelledByQueue = [];\n    _isDestroyed = false;\n    constructor() {\n        super();\n        // Callback is primarily for some internal tests\n        // that were instantiating the dialog container manually.\n        this._config = (inject(DialogConfig, { optional: true }) || new DialogConfig());\n        if (this._config.ariaLabelledBy) {\n            this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n        }\n    }\n    _addAriaLabelledBy(id) {\n        this._ariaLabelledByQueue.push(id);\n        this._changeDetectorRef.markForCheck();\n    }\n    _removeAriaLabelledBy(id) {\n        const index = this._ariaLabelledByQueue.indexOf(id);\n        if (index > -1) {\n            this._ariaLabelledByQueue.splice(index, 1);\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _contentAttached() {\n        this._initializeFocusTrap();\n        this._handleBackdropClicks();\n        this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n        this._trapFocus();\n    }\n    ngOnDestroy() {\n        this._focusTrapped.complete();\n        this._isDestroyed = true;\n        this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachDomPortal(portal);\n        this._contentAttached();\n        return result;\n    };\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n        if (!this._containsFocus()) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    deregisterBlur();\n                    deregisterMousedown();\n                    element.removeAttribute('tabindex');\n                };\n                const deregisterBlur = this._renderer.listen(element, 'blur', callback);\n                const deregisterMousedown = this._renderer.listen(element, 'mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus(options) {\n        if (this._isDestroyed) {\n            return;\n        }\n        // If were to attempt to focus immediately, then the content of the dialog would not yet be\n        // ready in instances where change detection has to run first. To deal with this, we simply\n        // wait until after the next render.\n        afterNextRender(() => {\n            const element = this._elementRef.nativeElement;\n            switch (this._config.autoFocus) {\n                case false:\n                case 'dialog':\n                    // Ensure that focus is on the dialog container. It's possible that a different\n                    // component tried to move focus while the open animation was running. See:\n                    // https://github.com/angular/components/issues/16215. Note that we only want to do this\n                    // if the focus isn't inside the dialog already, because it's possible that the consumer\n                    // turned off `autoFocus` in order to move focus themselves.\n                    if (!this._containsFocus()) {\n                        element.focus(options);\n                    }\n                    break;\n                case true:\n                case 'first-tabbable':\n                    const focusedSuccessfully = this._focusTrap?.focusInitialElement(options);\n                    // If we weren't able to find a focusable element in the dialog, then focus the dialog\n                    // container instead.\n                    if (!focusedSuccessfully) {\n                        this._focusDialogContainer(options);\n                    }\n                    break;\n                case 'first-heading':\n                    this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]', options);\n                    break;\n                default:\n                    this._focusByCssSelector(this._config.autoFocus, options);\n                    break;\n            }\n            this._focusTrapped.next();\n        }, { injector: this._injector });\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n        const focusConfig = this._config.restoreFocus;\n        let focusTargetElement = null;\n        if (typeof focusConfig === 'string') {\n            focusTargetElement = this._document.querySelector(focusConfig);\n        }\n        else if (typeof focusConfig === 'boolean') {\n            focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n        }\n        else if (focusConfig) {\n            focusTargetElement = focusConfig;\n        }\n        // We need the extra check, because IE can set the `activeElement` to null in some cases.\n        if (this._config.restoreFocus &&\n            focusTargetElement &&\n            typeof focusTargetElement.focus === 'function') {\n            const activeElement = _getFocusedElementPierceShadowDom();\n            const element = this._elementRef.nativeElement;\n            // Make sure that focus is still inside the dialog or is on the body (usually because a\n            // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n            // the consumer moved it themselves before the animation was done, in which case we shouldn't\n            // do anything.\n            if (!activeElement ||\n                activeElement === this._document.body ||\n                activeElement === element ||\n                element.contains(activeElement)) {\n                if (this._focusMonitor) {\n                    this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n                    this._closeInteractionType = null;\n                }\n                else {\n                    focusTargetElement.focus();\n                }\n            }\n        }\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer(options) {\n        // Note that there is no focus method when rendering on the server.\n        if (this._elementRef.nativeElement.focus) {\n            this._elementRef.nativeElement.focus(options);\n        }\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n        const element = this._elementRef.nativeElement;\n        const activeElement = _getFocusedElementPierceShadowDom();\n        return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n        if (this._platform.isBrowser) {\n            this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n            // Save the previously focused element. This element will be re-focused\n            // when the dialog closes.\n            if (this._document) {\n                this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n            }\n        }\n    }\n    /** Sets up the listener that handles clicks on the dialog backdrop. */\n    _handleBackdropClicks() {\n        // Clicking on the backdrop will move focus out of dialog.\n        // Recapture it if closing via the backdrop is disabled.\n        this._overlayRef.backdropClick().subscribe(() => {\n            if (this._config.disableClose) {\n                this._recaptureFocus();\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkDialogContainer, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkDialogContainer, isStandalone: true, selector: \"cdk-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.id\": \"_config.id || null\", \"attr.role\": \"_config.role\", \"attr.aria-modal\": \"_config.ariaModal\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledByQueue[0]\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\" }, classAttribute: \"cdk-dialog-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<ng-template cdkPortalOutlet />\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, imports: [CdkPortalOutlet], host: {\n                        'class': 'cdk-dialog-container',\n                        'tabindex': '-1',\n                        '[attr.id]': '_config.id || null',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                    }, template: \"<ng-template cdkPortalOutlet />\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }] } });\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n    overlayRef;\n    config;\n    /**\n     * Instance of component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    componentInstance;\n    /**\n     * `ComponentRef` of the component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    componentRef;\n    /** Instance of the container that is rendering out the dialog content. */\n    containerInstance;\n    /** Whether the user is allowed to close the dialog. */\n    disableClose;\n    /** Emits when the dialog has been closed. */\n    closed = new Subject();\n    /** Emits when the backdrop of the dialog is clicked. */\n    backdropClick;\n    /** Emits when on keyboard events within the dialog. */\n    keydownEvents;\n    /** Emits on pointer events that happen outside of the dialog. */\n    outsidePointerEvents;\n    /** Unique ID for the dialog. */\n    id;\n    /** Subscription to external detachments of the dialog. */\n    _detachSubscription;\n    constructor(overlayRef, config) {\n        this.overlayRef = overlayRef;\n        this.config = config;\n        this.disableClose = config.disableClose;\n        this.backdropClick = overlayRef.backdropClick();\n        this.keydownEvents = overlayRef.keydownEvents();\n        this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n        this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n        this.keydownEvents.subscribe(event => {\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.close(undefined, { focusOrigin: 'keyboard' });\n            }\n        });\n        this.backdropClick.subscribe(() => {\n            if (!this.disableClose) {\n                this.close(undefined, { focusOrigin: 'mouse' });\n            }\n        });\n        this._detachSubscription = overlayRef.detachments().subscribe(() => {\n            // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n            if (config.closeOnOverlayDetachments !== false) {\n                this.close();\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param result Optional result to return to the dialog opener.\n     * @param options Additional options to customize the closing behavior.\n     */\n    close(result, options) {\n        if (this.containerInstance) {\n            const closedSubject = this.closed;\n            this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n            // Drop the detach subscription first since it can be triggered by the\n            // `dispose` call and override the result of this closing sequence.\n            this._detachSubscription.unsubscribe();\n            this.overlayRef.dispose();\n            closedSubject.next(result);\n            closedSubject.complete();\n            this.componentInstance = this.containerInstance = null;\n        }\n    }\n    /** Updates the position of the dialog based on the current position strategy. */\n    updatePosition() {\n        this.overlayRef.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this.overlayRef.updateSize({ width, height });\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this.overlayRef.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this.overlayRef.removePanelClass(classes);\n        return this;\n    }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.block();\n    },\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n    provide: DIALOG_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\nclass Dialog {\n    _overlay = inject(Overlay);\n    _injector = inject(Injector);\n    _defaultOptions = inject(DEFAULT_DIALOG_CONFIG, { optional: true });\n    _parentDialog = inject(Dialog, { optional: true, skipSelf: true });\n    _overlayContainer = inject(OverlayContainer);\n    _idGenerator = inject(_IdGenerator);\n    _openDialogsAtThisLevel = [];\n    _afterAllClosedAtThisLevel = new Subject();\n    _afterOpenedAtThisLevel = new Subject();\n    _ariaHiddenElements = new Map();\n    _scrollStrategy = inject(DIALOG_SCROLL_STRATEGY);\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    afterAllClosed = defer(() => this.openDialogs.length\n        ? this._getAfterAllClosed()\n        : this._getAfterAllClosed().pipe(startWith(undefined)));\n    constructor() { }\n    open(componentOrTemplateRef, config) {\n        const defaults = (this._defaultOptions || new DialogConfig());\n        config = { ...defaults, ...config };\n        config.id = config.id || this._idGenerator.getId('cdk-dialog-');\n        if (config.id &&\n            this.getDialogById(config.id) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n        }\n        const overlayConfig = this._getOverlayConfig(config);\n        const overlayRef = this._overlay.create(overlayConfig);\n        const dialogRef = new DialogRef(overlayRef, config);\n        const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n        dialogRef.containerInstance = dialogContainer;\n        // If this is the first dialog that we're opening, hide all the non-overlay content.\n        if (!this.openDialogs.length) {\n            // Resolve this ahead of time, because some internal apps\n            // mock it out and depend on it being synchronous.\n            const overlayContainer = this._overlayContainer.getContainerElement();\n            if (dialogContainer._focusTrapped) {\n                dialogContainer._focusTrapped.pipe(take(1)).subscribe(() => {\n                    this._hideNonDialogContentFromAssistiveTechnology(overlayContainer);\n                });\n            }\n            else {\n                this._hideNonDialogContentFromAssistiveTechnology(overlayContainer);\n            }\n        }\n        this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n        this.openDialogs.push(dialogRef);\n        dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n        this.afterOpened.next(dialogRef);\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n        // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n        // determines when `aria-hidden` is removed from elements outside the dialog.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => {\n            // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n            if (dialog.config.closeOnDestroy === false) {\n                this._removeOpenDialog(dialog, false);\n            }\n        });\n        // Make a second pass and close the remaining dialogs. We do this second pass in order to\n        // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n        // that should be closed and dialogs that should not.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n        this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n        const state = new OverlayConfig({\n            positionStrategy: config.positionStrategy ||\n                this._overlay.position().global().centerHorizontally().centerVertically(),\n            scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n            panelClass: config.panelClass,\n            hasBackdrop: config.hasBackdrop,\n            direction: config.direction,\n            minWidth: config.minWidth,\n            minHeight: config.minHeight,\n            maxWidth: config.maxWidth,\n            maxHeight: config.maxHeight,\n            width: config.width,\n            height: config.height,\n            disposeOnNavigation: config.closeOnNavigation,\n        });\n        if (config.backdropClass) {\n            state.backdropClass = config.backdropClass;\n        }\n        return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DialogConfig, useValue: config },\n            { provide: DialogRef, useValue: dialogRef },\n            { provide: OverlayRef, useValue: overlay },\n        ];\n        let containerType;\n        if (config.container) {\n            if (typeof config.container === 'function') {\n                containerType = config.container;\n            }\n            else {\n                containerType = config.container.type;\n                providers.push(...config.container.providers(config));\n            }\n        }\n        else {\n            containerType = CdkDialogContainer;\n        }\n        const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({ parent: userInjector || this._injector, providers }));\n        const containerRef = overlay.attach(containerPortal);\n        return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n        if (componentOrTemplateRef instanceof TemplateRef) {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n            let context = { $implicit: config.data, dialogRef };\n            if (config.templateContext) {\n                context = {\n                    ...context,\n                    ...(typeof config.templateContext === 'function'\n                        ? config.templateContext()\n                        : config.templateContext),\n                };\n            }\n            dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n        }\n        else {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n            const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n            dialogRef.componentRef = contentRef;\n            dialogRef.componentInstance = contentRef.instance;\n        }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DIALOG_DATA, useValue: config.data },\n            { provide: DialogRef, useValue: dialogRef },\n        ];\n        if (config.providers) {\n            if (typeof config.providers === 'function') {\n                providers.push(...config.providers(dialogRef, config, dialogContainer));\n            }\n            else {\n                providers.push(...config.providers);\n            }\n        }\n        if (config.direction &&\n            (!userInjector ||\n                !userInjector.get(Directionality, null, { optional: true }))) {\n            providers.push({\n                provide: Directionality,\n                useValue: { value: config.direction, change: of() },\n            });\n        }\n        return Injector.create({ parent: userInjector || fallbackInjector, providers });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n        const index = this.openDialogs.indexOf(dialogRef);\n        if (index > -1) {\n            this.openDialogs.splice(index, 1);\n            // If all the dialogs were closed, remove/restore the `aria-hidden`\n            // to a the siblings and emit to the `afterAllClosed` stream.\n            if (!this.openDialogs.length) {\n                this._ariaHiddenElements.forEach((previousValue, element) => {\n                    if (previousValue) {\n                        element.setAttribute('aria-hidden', previousValue);\n                    }\n                    else {\n                        element.removeAttribute('aria-hidden');\n                    }\n                });\n                this._ariaHiddenElements.clear();\n                if (emitEvent) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology(overlayContainer) {\n        // Ensure that the overlay container is attached to the DOM.\n        if (overlayContainer.parentElement) {\n            const siblings = overlayContainer.parentElement.children;\n            for (let i = siblings.length - 1; i > -1; i--) {\n                const sibling = siblings[i];\n                if (sibling !== overlayContainer &&\n                    sibling.nodeName !== 'SCRIPT' &&\n                    sibling.nodeName !== 'STYLE' &&\n                    !sibling.hasAttribute('aria-live')) {\n                    this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n                    sibling.setAttribute('aria-hidden', 'true');\n                }\n            }\n        }\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Dialog, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Dialog, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Dialog, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n    let i = items.length;\n    while (i--) {\n        callback(items[i]);\n    }\n}\n\nclass DialogModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: DialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: DialogModule, imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer], exports: [\n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule,\n            CdkDialogContainer] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: DialogModule, providers: [Dialog], imports: [OverlayModule, PortalModule, A11yModule, \n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: DialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n                    exports: [\n                        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n                        // don't have to remember to import it or be faced with an unhelpful error.\n                        PortalModule,\n                        CdkDialogContainer,\n                    ],\n                    providers: [Dialog],\n                }]\n        }] });\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError, CdkPortalOutlet as ɵɵCdkPortalOutlet };\n//# sourceMappingURL=dialog.mjs.map\n"], "mappings": ";;;;AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AACxO,SAASC,OAAO,EAAEC,KAAK,EAAEC,EAAE,QAAQ,MAAM;AACzC,SAASC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,YAAY,QAAQ,kCAAkC;AAC5J,SAASC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,yBAAyB,QAAQ,kCAAkC;AAC/H,SAASC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC9G,SAASC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,gBAAgB,EAAEzB,CAAC,IAAI0B,aAAa,EAAEC,CAAC,IAAIC,aAAa,QAAQ,+BAA+B;AAC5I,SAASd,CAAC,IAAIe,YAAY,QAAQ,8BAA8B;AAChE,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASX,CAAC,IAAIY,iCAAiC,QAAQ,2BAA2B;AAClF,SAAStB,CAAC,IAAIuB,MAAM,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAChD,SAASC,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,OAAO,6BAA6B;AACpC,OAAO,eAAe;AACtB,OAAO,qCAAqC;AAC5C,OAAO,sBAAsB;AAC7B,OAAO,iBAAiB;AACxB,OAAO,wBAAwB;AAC/B,OAAO,wCAAwC;AAC/C,OAAO,iCAAiC;AACxC,OAAO,gCAAgC;AACvC,OAAO,iBAAiB;AACxB,OAAO,0BAA0B;AACjC,OAAO,YAAY;AACnB,OAAO,+CAA+C;AACtD,OAAO,4BAA4B;AACnC,OAAO,qCAAqC;AAC5C,OAAO,kCAAkC;;AAEzC;AACA,MAAMC,YAAY,CAAC;EAAAC,YAAA;IACf;AACJ;AACA;AACA;AACA;AACA;IALIC,eAAA;IAOA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA,eACO,QAAQ;IACf;IAAAA,eAAA,qBACa,EAAE;IACf;IAAAA,eAAA,sBACc,IAAI;IAClB;IAAAA,eAAA,wBACgB,EAAE;IAClB;IAAAA,eAAA,uBACe,KAAK;IACpB;IAAAA,eAAA,gBACQ,EAAE;IACV;IAAAA,eAAA,iBACS,EAAE;IACX;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,eACO,IAAI;IACX;IAAAA,eAAA;IAEA;IAAAA,eAAA,0BACkB,IAAI;IACtB;IAAAA,eAAA,yBACiB,IAAI;IACrB;IAAAA,eAAA,oBACY,IAAI;IAChB;AACJ;AACA;AACA;AACA;IAJIA,eAAA,oBAKY,KAAK;IACjB;AACJ;AACA;AACA;AACA;IAJIA,eAAA,oBAKY,gBAAgB;IAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IAPIA,eAAA,uBAQe,IAAI;IACnB;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;AACA;IAJIA,eAAA,4BAKoB,IAAI;IACxB;AACJ;AACA;AACA;IAHIA,eAAA,yBAIiB,IAAI;IACrB;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,oCAM4B,IAAI;IAChC;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;AACA;IAJIA,eAAA;IAMA;AACJ;AACA;AACA;IAHIA,eAAA;EAAA;AAKJ;AAEA,SAASC,sCAAsCA,CAAA,EAAG;EAC9C,MAAMC,KAAK,CAAC,uEAAuE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAAS/C,gBAAgB,CAAC;EAkC9C2C,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP;IACA;IAAAC,eAAA,sBApCU/D,MAAM,CAACC,UAAU,CAAC;IAAA8D,eAAA,4BACZ/D,MAAM,CAACmC,gBAAgB,CAAC;IAAA4B,eAAA;IAAAA,eAAA,gCAEpB/D,MAAM,CAACqC,oBAAoB,CAAC;IAAA0B,eAAA,kBAC1C/D,MAAM,CAACE,MAAM,CAAC;IAAA6D,eAAA,sBACV/D,MAAM,CAACyC,UAAU,CAAC;IAAAsB,eAAA,wBAChB/D,MAAM,CAACiD,YAAY,CAAC;IAAAc,eAAA,oBACxB/D,MAAM,CAACG,SAAS,CAAC;IAAA4D,eAAA,6BACR/D,MAAM,CAACI,iBAAiB,CAAC;IAAA2D,eAAA,oBAClC/D,MAAM,CAACK,QAAQ,CAAC;IAAA0D,eAAA,oBAChB/D,MAAM,CAACmD,QAAQ,CAAC;IAAAY,eAAA,oBAChB/D,MAAM,CAACF,QAAQ,EAAE;MAAEqE,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChD;IAAAJ,eAAA;IAAAA,eAAA,wBAEgB,IAAIhD,OAAO,CAAC,CAAC;IAC7B;IAAAgD,eAAA,qBACa,IAAI;IACjB;IAAAA,eAAA,+CACuC,IAAI;IAC3C;AACJ;AACA;AACA;AACA;IAJIA,eAAA,gCAKwB,IAAI;IAC5B;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,+BAMuB,EAAE;IAAAA,eAAA,uBACV,KAAK;IA8DpB;AACJ;AACA;AACA;AACA;AACA;IALIA,eAAA,0BAMmBK,MAAM,IAAK;MAC1B,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACrFP,sCAAsC,CAAC,CAAC;MAC5C;MACA,MAAMQ,MAAM,GAAG,IAAI,CAACH,aAAa,CAACI,eAAe,CAACL,MAAM,CAAC;MACzD,IAAI,CAACM,gBAAgB,CAAC,CAAC;MACvB,OAAOF,MAAM;IACjB,CAAC;IAtEG,IAAI,CAACG,OAAO,GAAI3E,MAAM,CAAC6D,YAAY,EAAE;MAAEM,QAAQ,EAAE;IAAK,CAAC,CAAC,IAAI,IAAIN,YAAY,CAAC,CAAE;IAC/E,IAAI,IAAI,CAACc,OAAO,CAACC,cAAc,EAAE;MAC7B,IAAI,CAACC,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAACH,OAAO,CAACC,cAAc,CAAC;IAC/D;EACJ;EACAG,kBAAkBA,CAACC,EAAE,EAAE;IACnB,IAAI,CAACH,oBAAoB,CAACC,IAAI,CAACE,EAAE,CAAC;IAClC,IAAI,CAACC,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACAC,qBAAqBA,CAACH,EAAE,EAAE;IACtB,MAAMI,KAAK,GAAG,IAAI,CAACP,oBAAoB,CAACQ,OAAO,CAACL,EAAE,CAAC;IACnD,IAAII,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACP,oBAAoB,CAACS,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC1C,IAAI,CAACH,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAR,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACa,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIA,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,aAAa,CAACC,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAAC5B,MAAM,EAAE;IAC1B,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFP,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMQ,MAAM,GAAG,IAAI,CAACH,aAAa,CAAC2B,qBAAqB,CAAC5B,MAAM,CAAC;IAC/D,IAAI,CAACM,gBAAgB,CAAC,CAAC;IACvB,OAAOF,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACIyB,oBAAoBA,CAAC7B,MAAM,EAAE;IACzB,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFP,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMQ,MAAM,GAAG,IAAI,CAACH,aAAa,CAAC4B,oBAAoB,CAAC7B,MAAM,CAAC;IAC9D,IAAI,CAACM,gBAAgB,CAAC,CAAC;IACvB,OAAOF,MAAM;EACjB;EAeA;EACA;EACA0B,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE;MACxB,IAAI,CAACT,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIU,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACC,qBAAqB,CAACC,WAAW,CAACH,OAAO,CAAC,EAAE;MAClDA,OAAO,CAACI,QAAQ,GAAG,CAAC,CAAC;MACrB;MACA,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,MAAM;QACjC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;UACnBC,cAAc,CAAC,CAAC;UAChBC,mBAAmB,CAAC,CAAC;UACrBT,OAAO,CAACU,eAAe,CAAC,UAAU,CAAC;QACvC,CAAC;QACD,MAAMF,cAAc,GAAG,IAAI,CAACG,SAAS,CAACC,MAAM,CAACZ,OAAO,EAAE,MAAM,EAAEO,QAAQ,CAAC;QACvE,MAAME,mBAAmB,GAAG,IAAI,CAACE,SAAS,CAACC,MAAM,CAACZ,OAAO,EAAE,WAAW,EAAEO,QAAQ,CAAC;MACrF,CAAC,CAAC;IACN;IACAP,OAAO,CAACa,KAAK,CAACZ,OAAO,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIa,mBAAmBA,CAACC,QAAQ,EAAEd,OAAO,EAAE;IACnC,IAAIe,cAAc,GAAG,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,aAAa,CAACJ,QAAQ,CAAC;IAC3E,IAAIC,cAAc,EAAE;MAChB,IAAI,CAACjB,WAAW,CAACiB,cAAc,EAAEf,OAAO,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;AACA;EACIZ,UAAUA,CAACY,OAAO,EAAE;IAChB,IAAI,IAAI,CAACR,YAAY,EAAE;MACnB;IACJ;IACA;IACA;IACA;IACAxF,eAAe,CAAC,MAAM;MAAA,IAAAmH,gBAAA;MAClB,MAAMpB,OAAO,GAAG,IAAI,CAACiB,WAAW,CAACC,aAAa;MAC9C,QAAQ,IAAI,CAAC5C,OAAO,CAAC+C,SAAS;QAC1B,KAAK,KAAK;QACV,KAAK,QAAQ;UACT;UACA;UACA;UACA;UACA;UACA,IAAI,CAAC,IAAI,CAACvB,cAAc,CAAC,CAAC,EAAE;YACxBE,OAAO,CAACa,KAAK,CAACZ,OAAO,CAAC;UAC1B;UACA;QACJ,KAAK,IAAI;QACT,KAAK,gBAAgB;UACjB,MAAMqB,mBAAmB,IAAAF,gBAAA,GAAG,IAAI,CAACG,UAAU,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBI,mBAAmB,CAACvB,OAAO,CAAC;UACzE;UACA;UACA,IAAI,CAACqB,mBAAmB,EAAE;YACtB,IAAI,CAACG,qBAAqB,CAACxB,OAAO,CAAC;UACvC;UACA;QACJ,KAAK,eAAe;UAChB,IAAI,CAACa,mBAAmB,CAAC,0CAA0C,EAAEb,OAAO,CAAC;UAC7E;QACJ;UACI,IAAI,CAACa,mBAAmB,CAAC,IAAI,CAACxC,OAAO,CAAC+C,SAAS,EAAEpB,OAAO,CAAC;UACzD;MACR;MACA,IAAI,CAACV,aAAa,CAACmC,IAAI,CAAC,CAAC;IAC7B,CAAC,EAAE;MAAEC,QAAQ,EAAE,IAAI,CAACC;IAAU,CAAC,CAAC;EACpC;EACA;EACAlC,aAAaA,CAAA,EAAG;IACZ,MAAMmC,WAAW,GAAG,IAAI,CAACvD,OAAO,CAACwD,YAAY;IAC7C,IAAIC,kBAAkB,GAAG,IAAI;IAC7B,IAAI,OAAOF,WAAW,KAAK,QAAQ,EAAE;MACjCE,kBAAkB,GAAG,IAAI,CAACC,SAAS,CAACb,aAAa,CAACU,WAAW,CAAC;IAClE,CAAC,MACI,IAAI,OAAOA,WAAW,KAAK,SAAS,EAAE;MACvCE,kBAAkB,GAAGF,WAAW,GAAG,IAAI,CAACI,oCAAoC,GAAG,IAAI;IACvF,CAAC,MACI,IAAIJ,WAAW,EAAE;MAClBE,kBAAkB,GAAGF,WAAW;IACpC;IACA;IACA,IAAI,IAAI,CAACvD,OAAO,CAACwD,YAAY,IACzBC,kBAAkB,IAClB,OAAOA,kBAAkB,CAAClB,KAAK,KAAK,UAAU,EAAE;MAChD,MAAMqB,aAAa,GAAGnF,iCAAiC,CAAC,CAAC;MACzD,MAAMiD,OAAO,GAAG,IAAI,CAACiB,WAAW,CAACC,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAI,CAACgB,aAAa,IACdA,aAAa,KAAK,IAAI,CAACF,SAAS,CAACG,IAAI,IACrCD,aAAa,KAAKlC,OAAO,IACzBA,OAAO,CAACoC,QAAQ,CAACF,aAAa,CAAC,EAAE;QACjC,IAAI,IAAI,CAACG,aAAa,EAAE;UACpB,IAAI,CAACA,aAAa,CAACC,QAAQ,CAACP,kBAAkB,EAAE,IAAI,CAACQ,qBAAqB,CAAC;UAC3E,IAAI,CAACA,qBAAqB,GAAG,IAAI;QACrC,CAAC,MACI;UACDR,kBAAkB,CAAClB,KAAK,CAAC,CAAC;QAC9B;MACJ;IACJ;IACA,IAAI,IAAI,CAACU,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACiB,OAAO,CAAC,CAAC;IAC7B;EACJ;EACA;EACAf,qBAAqBA,CAACxB,OAAO,EAAE;IAC3B;IACA,IAAI,IAAI,CAACgB,WAAW,CAACC,aAAa,CAACL,KAAK,EAAE;MACtC,IAAI,CAACI,WAAW,CAACC,aAAa,CAACL,KAAK,CAACZ,OAAO,CAAC;IACjD;EACJ;EACA;EACAH,cAAcA,CAAA,EAAG;IACb,MAAME,OAAO,GAAG,IAAI,CAACiB,WAAW,CAACC,aAAa;IAC9C,MAAMgB,aAAa,GAAGnF,iCAAiC,CAAC,CAAC;IACzD,OAAOiD,OAAO,KAAKkC,aAAa,IAAIlC,OAAO,CAACoC,QAAQ,CAACF,aAAa,CAAC;EACvE;EACA;EACAhD,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACuD,SAAS,CAACC,SAAS,EAAE;MAC1B,IAAI,CAACnB,UAAU,GAAG,IAAI,CAACoB,iBAAiB,CAACC,MAAM,CAAC,IAAI,CAAC3B,WAAW,CAACC,aAAa,CAAC;MAC/E;MACA;MACA,IAAI,IAAI,CAACc,SAAS,EAAE;QAChB,IAAI,CAACC,oCAAoC,GAAGlF,iCAAiC,CAAC,CAAC;MACnF;IACJ;EACJ;EACA;EACAoC,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,IAAI,CAAC0D,WAAW,CAACC,aAAa,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;MAC7C,IAAI,IAAI,CAACzE,OAAO,CAAC0E,YAAY,EAAE;QAC3B,IAAI,CAACnD,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN;AAGJ;AAACoD,mBAAA,GA3QKpF,kBAAkB;AAAAH,eAAA,CAAlBG,kBAAkB,wBAAAqF,4BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAyQ+EtF,mBAAkB;AAAA;AAAAH,eAAA,CAzQnHG,kBAAkB,8BA4QyDnE,EAAE,CAAA0J,iBAAA;EAAAC,IAAA,EAFQxF,mBAAkB;EAAAyF,SAAA;EAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAE5B/J,EAAE,CAAAiK,WAAA,CAFqhB3I,eAAe;IAAA;IAAA,IAAAyI,EAAA;MAAA,IAAAG,EAAA;MAEtiBlK,EAAE,CAAAmK,cAAA,CAAAD,EAAA,GAAFlK,EAAE,CAAAoK,WAAA,QAAAJ,GAAA,CAAA1F,aAAA,GAAA4F,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,SAAA,eAFoH,IAAI;EAAAC,QAAA;EAAAC,YAAA,WAAAC,iCAAAV,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAE1H/J,EAAE,CAAA0K,WAAA,OAAAV,GAAA,CAAApF,OAAA,CAAAK,EAAA,IAFsB,IAAI,UAAA+E,GAAA,CAAApF,OAAA,CAAA+F,IAAA,gBAAAX,GAAA,CAAApF,OAAA,CAAAgG,SAAA,qBAAAZ,GAAA,CAAApF,OAAA,CAAAiG,SAAA,GAAE,IAAI,GAAAb,GAAA,CAAAlF,oBAAA,CAAwB,CAAC,iBAAAkF,GAAA,CAAApF,OAAA,CAAAiG,SAAA,sBAAAb,GAAA,CAAApF,OAAA,CAAAkG,eAAA,IAAtB,IAAI;IAAA;EAAA;EAAAC,QAAA,GAEzC/K,EAAE,CAAAgL,0BAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,6BAAAtB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF/J,EAAE,CAAAsL,UAAA,IAAAC,0CAAA,wBAFwpB,CAAC;IAAA;EAAA;EAAAC,YAAA,GAAgKlK,eAAe;EAAAmK,MAAA;EAAAC,aAAA;AAAA;AAE35B;EAAA,QAAAlH,SAAA,oBAAAA,SAAA,KAAiFxE,EAAE,CAAA2L,iBAAA,CAAQxH,kBAAkB,EAAc,CAAC;IAChHwF,IAAI,EAAEnJ,SAAS;IACfoL,IAAI,EAAE,CAAC;MAAEvE,QAAQ,EAAE,sBAAsB;MAAEqE,aAAa,EAAEjL,iBAAiB,CAACoL,IAAI;MAAEC,eAAe,EAAEpL,uBAAuB,CAACqL,OAAO;MAAEC,OAAO,EAAE,CAAC1K,eAAe,CAAC;MAAE2K,IAAI,EAAE;QAC1J,OAAO,EAAE,sBAAsB;QAC/B,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,oBAAoB;QACjC,aAAa,EAAE,cAAc;QAC7B,mBAAmB,EAAE,mBAAmB;QACxC,wBAAwB,EAAE,oDAAoD;QAC9E,mBAAmB,EAAE,mBAAmB;QACxC,yBAAyB,EAAE;MAC/B,CAAC;MAAEb,QAAQ,EAAE,mCAAmC;MAAEK,MAAM,EAAE,CAAC,qGAAqG;IAAE,CAAC;EAC/K,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEnH,aAAa,EAAE,CAAC;MACxDqF,IAAI,EAAEhJ,SAAS;MACfiL,IAAI,EAAE,CAACtK,eAAe,EAAE;QAAE4K,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EA6BZpI,WAAWA,CAACqI,UAAU,EAAEC,MAAM,EAAE;IAAArI,eAAA;IAAAA,eAAA;IA1BhC;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;AACJ;AACA;AACA;IAHIA,eAAA;IAKA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA,iBACS,IAAIhD,OAAO,CAAC,CAAC;IACtB;IAAAgD,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAEA;IAAAA,eAAA;IAGI,IAAI,CAACoI,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC/C,YAAY,GAAG+C,MAAM,CAAC/C,YAAY;IACvC,IAAI,CAACF,aAAa,GAAGgD,UAAU,CAAChD,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACkD,aAAa,GAAGF,UAAU,CAACE,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACC,oBAAoB,GAAGH,UAAU,CAACG,oBAAoB,CAAC,CAAC;IAC7D,IAAI,CAACtH,EAAE,GAAGoH,MAAM,CAACpH,EAAE,CAAC,CAAC;IACrB,IAAI,CAACqH,aAAa,CAACjD,SAAS,CAACmD,KAAK,IAAI;MAClC,IAAIA,KAAK,CAACC,OAAO,KAAKnJ,MAAM,IAAI,CAAC,IAAI,CAACgG,YAAY,IAAI,CAAC/F,cAAc,CAACiJ,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACE,cAAc,CAAC,CAAC;QACtB,IAAI,CAACC,KAAK,CAACC,SAAS,EAAE;UAAEC,WAAW,EAAE;QAAW,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC;IACF,IAAI,CAACzD,aAAa,CAACC,SAAS,CAAC,MAAM;MAC/B,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;QACpB,IAAI,CAACqD,KAAK,CAACC,SAAS,EAAE;UAAEC,WAAW,EAAE;QAAQ,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;IACF,IAAI,CAACC,mBAAmB,GAAGV,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC1D,SAAS,CAAC,MAAM;MAChE;MACA,IAAIgD,MAAM,CAACW,yBAAyB,KAAK,KAAK,EAAE;QAC5C,IAAI,CAACL,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIA,KAAKA,CAAClI,MAAM,EAAE8B,OAAO,EAAE;IACnB,IAAI,IAAI,CAAC0G,iBAAiB,EAAE;MACxB,MAAMC,aAAa,GAAG,IAAI,CAACC,MAAM;MACjC,IAAI,CAACF,iBAAiB,CAACpE,qBAAqB,GAAG,CAAAtC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsG,WAAW,KAAI,SAAS;MAChF;MACA;MACA,IAAI,CAACC,mBAAmB,CAACM,WAAW,CAAC,CAAC;MACtC,IAAI,CAAChB,UAAU,CAACiB,OAAO,CAAC,CAAC;MACzBH,aAAa,CAAClF,IAAI,CAACvD,MAAM,CAAC;MAC1ByI,aAAa,CAACpH,QAAQ,CAAC,CAAC;MACxB,IAAI,CAACwH,iBAAiB,GAAG,IAAI,CAACL,iBAAiB,GAAG,IAAI;IAC1D;EACJ;EACA;EACAM,cAAcA,CAAA,EAAG;IACb,IAAI,CAACnB,UAAU,CAACmB,cAAc,CAAC,CAAC;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAUA,CAACC,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAACtB,UAAU,CAACoB,UAAU,CAAC;MAAEC,KAAK;MAAEC;IAAO,CAAC,CAAC;IAC7C,OAAO,IAAI;EACf;EACA;EACAC,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACxB,UAAU,CAACuB,aAAa,CAACC,OAAO,CAAC;IACtC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,CAACxB,UAAU,CAACyB,gBAAgB,CAACD,OAAO,CAAC;IACzC,OAAO,IAAI;EACf;AACJ;;AAEA;AACA,MAAME,sBAAsB,GAAG,IAAIlN,cAAc,CAAC,sBAAsB,EAAE;EACtEmN,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,OAAO,GAAGhO,MAAM,CAAC2C,OAAO,CAAC;IAC/B,OAAO,MAAMqL,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;EACjD;AACJ,CAAC,CAAC;AACF;AACA,MAAMC,WAAW,GAAG,IAAIxN,cAAc,CAAC,YAAY,CAAC;AACpD;AACA,MAAMyN,qBAAqB,GAAG,IAAIzN,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,SAAS0N,uCAAuCA,CAACL,OAAO,EAAE;EACtD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,+BAA+B,GAAG;EACpCC,OAAO,EAAEV,sBAAsB;EAC/BW,IAAI,EAAE,CAAC7L,OAAO,CAAC;EACf8L,UAAU,EAAEJ;AAChB,CAAC;AAED,MAAMK,MAAM,CAAC;EAYT;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,WAAW,GAAG,IAAI,CAACE,uBAAuB;EAC7F;EACA;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACF,aAAa,GAAG,IAAI,CAACA,aAAa,CAACE,WAAW,GAAG,IAAI,CAACC,uBAAuB;EAC7F;EACA;AACJ;AACA;AACA;;EAIIjL,WAAWA,CAAA,EAAG;IAAAC,eAAA,mBA1BH/D,MAAM,CAAC2C,OAAO,CAAC;IAAAoB,eAAA,oBACd/D,MAAM,CAACK,QAAQ,CAAC;IAAA0D,eAAA,0BACV/D,MAAM,CAACoO,qBAAqB,EAAE;MAAEjK,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAJ,eAAA,wBACnD/D,MAAM,CAAC0O,MAAM,EAAE;MAAEvK,QAAQ,EAAE,IAAI;MAAE6K,QAAQ,EAAE;IAAK,CAAC,CAAC;IAAAjL,eAAA,4BAC9C/D,MAAM,CAAC6C,gBAAgB,CAAC;IAAAkB,eAAA,uBAC7B/D,MAAM,CAAC0D,YAAY,CAAC;IAAAK,eAAA,kCACT,EAAE;IAAAA,eAAA,qCACC,IAAIhD,OAAO,CAAC,CAAC;IAAAgD,eAAA,kCAChB,IAAIhD,OAAO,CAAC,CAAC;IAAAgD,eAAA,8BACjB,IAAIkL,GAAG,CAAC,CAAC;IAAAlL,eAAA,0BACb/D,MAAM,CAAC6N,sBAAsB,CAAC;IAAA9J,eAAA,yBAa/B/C,KAAK,CAAC,MAAM,IAAI,CAAC2N,WAAW,CAACO,MAAM,GAC9C,IAAI,CAACC,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAACC,IAAI,CAAC7L,SAAS,CAACoJ,SAAS,CAAC,CAAC,CAAC;EAC3C;EAChB0C,IAAIA,CAACC,sBAAsB,EAAElD,MAAM,EAAE;IACjC,MAAMmD,QAAQ,GAAI,IAAI,CAACC,eAAe,IAAI,IAAI3L,YAAY,CAAC,CAAE;IAC7DuI,MAAM,GAAAqD,aAAA,CAAAA,aAAA,KAAQF,QAAQ,GAAKnD,MAAM,CAAE;IACnCA,MAAM,CAACpH,EAAE,GAAGoH,MAAM,CAACpH,EAAE,IAAI,IAAI,CAAC0K,YAAY,CAACC,KAAK,CAAC,aAAa,CAAC;IAC/D,IAAIvD,MAAM,CAACpH,EAAE,IACT,IAAI,CAAC4K,aAAa,CAACxD,MAAM,CAACpH,EAAE,CAAC,KAC5B,OAAOT,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMN,KAAK,CAAC,mBAAmBmI,MAAM,CAACpH,EAAE,iDAAiD,CAAC;IAC9F;IACA,MAAM6K,aAAa,GAAG,IAAI,CAACC,iBAAiB,CAAC1D,MAAM,CAAC;IACpD,MAAMD,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAAC9G,MAAM,CAAC4G,aAAa,CAAC;IACtD,MAAMG,SAAS,GAAG,IAAI9D,SAAS,CAACC,UAAU,EAAEC,MAAM,CAAC;IACnD,MAAM6D,eAAe,GAAG,IAAI,CAACC,gBAAgB,CAAC/D,UAAU,EAAE6D,SAAS,EAAE5D,MAAM,CAAC;IAC5E4D,SAAS,CAAChD,iBAAiB,GAAGiD,eAAe;IAC7C;IACA,IAAI,CAAC,IAAI,CAACtB,WAAW,CAACO,MAAM,EAAE;MAC1B;MACA;MACA,MAAMiB,gBAAgB,GAAG,IAAI,CAACC,iBAAiB,CAACC,mBAAmB,CAAC,CAAC;MACrE,IAAIJ,eAAe,CAACrK,aAAa,EAAE;QAC/BqK,eAAe,CAACrK,aAAa,CAACwJ,IAAI,CAAC5L,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4F,SAAS,CAAC,MAAM;UACxD,IAAI,CAACkH,4CAA4C,CAACH,gBAAgB,CAAC;QACvE,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACG,4CAA4C,CAACH,gBAAgB,CAAC;MACvE;IACJ;IACA,IAAI,CAACI,oBAAoB,CAACjB,sBAAsB,EAAEU,SAAS,EAAEC,eAAe,EAAE7D,MAAM,CAAC;IACrF,IAAI,CAACuC,WAAW,CAAC7J,IAAI,CAACkL,SAAS,CAAC;IAChCA,SAAS,CAAC9C,MAAM,CAAC9D,SAAS,CAAC,MAAM,IAAI,CAACoH,iBAAiB,CAACR,SAAS,EAAE,IAAI,CAAC,CAAC;IACzE,IAAI,CAAClB,WAAW,CAAC/G,IAAI,CAACiI,SAAS,CAAC;IAChC,OAAOA,SAAS;EACpB;EACA;AACJ;AACA;EACIS,QAAQA,CAAA,EAAG;IACPC,cAAc,CAAC,IAAI,CAAC/B,WAAW,EAAEgC,MAAM,IAAIA,MAAM,CAACjE,KAAK,CAAC,CAAC,CAAC;EAC9D;EACA;AACJ;AACA;AACA;EACIkD,aAAaA,CAAC5K,EAAE,EAAE;IACd,OAAO,IAAI,CAAC2J,WAAW,CAACiC,IAAI,CAACD,MAAM,IAAIA,MAAM,CAAC3L,EAAE,KAAKA,EAAE,CAAC;EAC5D;EACAW,WAAWA,CAAA,EAAG;IACV;IACA;IACA;IACA+K,cAAc,CAAC,IAAI,CAAC7B,uBAAuB,EAAE8B,MAAM,IAAI;MACnD;MACA,IAAIA,MAAM,CAACvE,MAAM,CAACyE,cAAc,KAAK,KAAK,EAAE;QACxC,IAAI,CAACL,iBAAiB,CAACG,MAAM,EAAE,KAAK,CAAC;MACzC;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACAD,cAAc,CAAC,IAAI,CAAC7B,uBAAuB,EAAE8B,MAAM,IAAIA,MAAM,CAACjE,KAAK,CAAC,CAAC,CAAC;IACtE,IAAI,CAACoE,0BAA0B,CAACjL,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACkJ,uBAAuB,CAAClJ,QAAQ,CAAC,CAAC;IACvC,IAAI,CAACgJ,uBAAuB,GAAG,EAAE;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIiB,iBAAiBA,CAAC1D,MAAM,EAAE;IACtB,MAAM2E,KAAK,GAAG,IAAIjO,aAAa,CAAC;MAC5BkO,gBAAgB,EAAE5E,MAAM,CAAC4E,gBAAgB,IACrC,IAAI,CAACjB,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAACC,gBAAgB,CAAC,CAAC;MAC7EC,cAAc,EAAEjF,MAAM,CAACiF,cAAc,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC;MAC/DC,UAAU,EAAEnF,MAAM,CAACmF,UAAU;MAC7BC,WAAW,EAAEpF,MAAM,CAACoF,WAAW;MAC/BC,SAAS,EAAErF,MAAM,CAACqF,SAAS;MAC3BC,QAAQ,EAAEtF,MAAM,CAACsF,QAAQ;MACzBC,SAAS,EAAEvF,MAAM,CAACuF,SAAS;MAC3BC,QAAQ,EAAExF,MAAM,CAACwF,QAAQ;MACzBC,SAAS,EAAEzF,MAAM,CAACyF,SAAS;MAC3BrE,KAAK,EAAEpB,MAAM,CAACoB,KAAK;MACnBC,MAAM,EAAErB,MAAM,CAACqB,MAAM;MACrBqE,mBAAmB,EAAE1F,MAAM,CAAC2F;IAChC,CAAC,CAAC;IACF,IAAI3F,MAAM,CAAC4F,aAAa,EAAE;MACtBjB,KAAK,CAACiB,aAAa,GAAG5F,MAAM,CAAC4F,aAAa;IAC9C;IACA,OAAOjB,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIb,gBAAgBA,CAAClC,OAAO,EAAEgC,SAAS,EAAE5D,MAAM,EAAE;IAAA,IAAA6F,qBAAA;IACzC,MAAMC,YAAY,GAAG9F,MAAM,CAACpE,QAAQ,MAAAiK,qBAAA,GAAI7F,MAAM,CAAC+F,gBAAgB,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBjK,QAAQ;IACzE,MAAMoK,SAAS,GAAG,CACd;MAAE7D,OAAO,EAAE1K,YAAY;MAAEwO,QAAQ,EAAEjG;IAAO,CAAC,EAC3C;MAAEmC,OAAO,EAAErC,SAAS;MAAEmG,QAAQ,EAAErC;IAAU,CAAC,EAC3C;MAAEzB,OAAO,EAAE9L,UAAU;MAAE4P,QAAQ,EAAErE;IAAQ,CAAC,CAC7C;IACD,IAAIsE,aAAa;IACjB,IAAIlG,MAAM,CAACmG,SAAS,EAAE;MAClB,IAAI,OAAOnG,MAAM,CAACmG,SAAS,KAAK,UAAU,EAAE;QACxCD,aAAa,GAAGlG,MAAM,CAACmG,SAAS;MACpC,CAAC,MACI;QACDD,aAAa,GAAGlG,MAAM,CAACmG,SAAS,CAAC7I,IAAI;QACrC0I,SAAS,CAACtN,IAAI,CAAC,GAAGsH,MAAM,CAACmG,SAAS,CAACH,SAAS,CAAChG,MAAM,CAAC,CAAC;MACzD;IACJ,CAAC,MACI;MACDkG,aAAa,GAAGpO,kBAAkB;IACtC;IACA,MAAMsO,eAAe,GAAG,IAAIjR,eAAe,CAAC+Q,aAAa,EAAElG,MAAM,CAAC+F,gBAAgB,EAAE9R,QAAQ,CAAC4I,MAAM,CAAC;MAAEwJ,MAAM,EAAEP,YAAY,IAAI,IAAI,CAACjK,SAAS;MAAEmK;IAAU,CAAC,CAAC,CAAC;IAC3J,MAAMM,YAAY,GAAG1E,OAAO,CAAC2E,MAAM,CAACH,eAAe,CAAC;IACpD,OAAOE,YAAY,CAACE,QAAQ;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIrC,oBAAoBA,CAACjB,sBAAsB,EAAEU,SAAS,EAAEC,eAAe,EAAE7D,MAAM,EAAE;IAC7E,IAAIkD,sBAAsB,YAAY1O,WAAW,EAAE;MAC/C,MAAMoH,QAAQ,GAAG,IAAI,CAAC6K,eAAe,CAACzG,MAAM,EAAE4D,SAAS,EAAEC,eAAe,EAAEtD,SAAS,CAAC;MACpF,IAAImG,OAAO,GAAG;QAAEC,SAAS,EAAE3G,MAAM,CAAC4G,IAAI;QAAEhD;MAAU,CAAC;MACnD,IAAI5D,MAAM,CAAC6G,eAAe,EAAE;QACxBH,OAAO,GAAArD,aAAA,CAAAA,aAAA,KACAqD,OAAO,GACN,OAAO1G,MAAM,CAAC6G,eAAe,KAAK,UAAU,GAC1C7G,MAAM,CAAC6G,eAAe,CAAC,CAAC,GACxB7G,MAAM,CAAC6G,eAAe,CAC/B;MACL;MACAhD,eAAe,CAAChK,oBAAoB,CAAC,IAAIxE,cAAc,CAAC6N,sBAAsB,EAAE,IAAI,EAAEwD,OAAO,EAAE9K,QAAQ,CAAC,CAAC;IAC7G,CAAC,MACI;MACD,MAAMA,QAAQ,GAAG,IAAI,CAAC6K,eAAe,CAACzG,MAAM,EAAE4D,SAAS,EAAEC,eAAe,EAAE,IAAI,CAAChI,SAAS,CAAC;MACzF,MAAMiL,UAAU,GAAGjD,eAAe,CAACjK,qBAAqB,CAAC,IAAIzE,eAAe,CAAC+N,sBAAsB,EAAElD,MAAM,CAAC+F,gBAAgB,EAAEnK,QAAQ,CAAC,CAAC;MACxIgI,SAAS,CAACmD,YAAY,GAAGD,UAAU;MACnClD,SAAS,CAAC3C,iBAAiB,GAAG6F,UAAU,CAACN,QAAQ;IACrD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACzG,MAAM,EAAE4D,SAAS,EAAEC,eAAe,EAAEmD,gBAAgB,EAAE;IAAA,IAAAC,sBAAA;IAClE,MAAMnB,YAAY,GAAG9F,MAAM,CAACpE,QAAQ,MAAAqL,sBAAA,GAAIjH,MAAM,CAAC+F,gBAAgB,cAAAkB,sBAAA,uBAAvBA,sBAAA,CAAyBrL,QAAQ;IACzE,MAAMoK,SAAS,GAAG,CACd;MAAE7D,OAAO,EAAEJ,WAAW;MAAEkE,QAAQ,EAAEjG,MAAM,CAAC4G;IAAK,CAAC,EAC/C;MAAEzE,OAAO,EAAErC,SAAS;MAAEmG,QAAQ,EAAErC;IAAU,CAAC,CAC9C;IACD,IAAI5D,MAAM,CAACgG,SAAS,EAAE;MAClB,IAAI,OAAOhG,MAAM,CAACgG,SAAS,KAAK,UAAU,EAAE;QACxCA,SAAS,CAACtN,IAAI,CAAC,GAAGsH,MAAM,CAACgG,SAAS,CAACpC,SAAS,EAAE5D,MAAM,EAAE6D,eAAe,CAAC,CAAC;MAC3E,CAAC,MACI;QACDmC,SAAS,CAACtN,IAAI,CAAC,GAAGsH,MAAM,CAACgG,SAAS,CAAC;MACvC;IACJ;IACA,IAAIhG,MAAM,CAACqF,SAAS,KACf,CAACS,YAAY,IACV,CAACA,YAAY,CAACoB,GAAG,CAAC1P,cAAc,EAAE,IAAI,EAAE;MAAEO,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,EAAE;MAClEiO,SAAS,CAACtN,IAAI,CAAC;QACXyJ,OAAO,EAAE3K,cAAc;QACvByO,QAAQ,EAAE;UAAEkB,KAAK,EAAEnH,MAAM,CAACqF,SAAS;UAAE+B,MAAM,EAAEvS,EAAE,CAAC;QAAE;MACtD,CAAC,CAAC;IACN;IACA,OAAOZ,QAAQ,CAAC4I,MAAM,CAAC;MAAEwJ,MAAM,EAAEP,YAAY,IAAIkB,gBAAgB;MAAEhB;IAAU,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACI5B,iBAAiBA,CAACR,SAAS,EAAEyD,SAAS,EAAE;IACpC,MAAMrO,KAAK,GAAG,IAAI,CAACuJ,WAAW,CAACtJ,OAAO,CAAC2K,SAAS,CAAC;IACjD,IAAI5K,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACuJ,WAAW,CAACrJ,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC;MACA;MACA,IAAI,CAAC,IAAI,CAACuJ,WAAW,CAACO,MAAM,EAAE;QAC1B,IAAI,CAACwE,mBAAmB,CAACC,OAAO,CAAC,CAACC,aAAa,EAAEvN,OAAO,KAAK;UACzD,IAAIuN,aAAa,EAAE;YACfvN,OAAO,CAACwN,YAAY,CAAC,aAAa,EAAED,aAAa,CAAC;UACtD,CAAC,MACI;YACDvN,OAAO,CAACU,eAAe,CAAC,aAAa,CAAC;UAC1C;QACJ,CAAC,CAAC;QACF,IAAI,CAAC2M,mBAAmB,CAACI,KAAK,CAAC,CAAC;QAChC,IAAIL,SAAS,EAAE;UACX,IAAI,CAACtE,kBAAkB,CAAC,CAAC,CAACpH,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ;EACJ;EACA;EACAuI,4CAA4CA,CAACH,gBAAgB,EAAE;IAC3D;IACA,IAAIA,gBAAgB,CAAC4D,aAAa,EAAE;MAChC,MAAMC,QAAQ,GAAG7D,gBAAgB,CAAC4D,aAAa,CAACE,QAAQ;MACxD,KAAK,IAAIC,CAAC,GAAGF,QAAQ,CAAC9E,MAAM,GAAG,CAAC,EAAEgF,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,MAAMC,OAAO,GAAGH,QAAQ,CAACE,CAAC,CAAC;QAC3B,IAAIC,OAAO,KAAKhE,gBAAgB,IAC5BgE,OAAO,CAACC,QAAQ,KAAK,QAAQ,IAC7BD,OAAO,CAACC,QAAQ,KAAK,OAAO,IAC5B,CAACD,OAAO,CAACE,YAAY,CAAC,WAAW,CAAC,EAAE;UACpC,IAAI,CAACX,mBAAmB,CAACY,GAAG,CAACH,OAAO,EAAEA,OAAO,CAACI,YAAY,CAAC,aAAa,CAAC,CAAC;UAC1EJ,OAAO,CAACN,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;QAC/C;MACJ;IACJ;EACJ;EACA1E,kBAAkBA,CAAA,EAAG;IACjB,MAAMsD,MAAM,GAAG,IAAI,CAAC7D,aAAa;IACjC,OAAO6D,MAAM,GAAGA,MAAM,CAACtD,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAC2B,0BAA0B;EACjF;AAGJ;AAAC0D,OAAA,GAtQK9F,MAAM;AAAA3K,eAAA,CAAN2K,MAAM,wBAAA+F,gBAAAjL,iBAAA;EAAA,YAAAA,iBAAA,IAoQ2FkF,OAAM;AAAA;AAAA3K,eAAA,CApQvG2K,MAAM,+BAtJqE3O,EAAE,CAAA2U,kBAAA;EAAAC,KAAA,EA2ZwBjG,OAAM;EAAAX,OAAA,EAANW,OAAM,CAAAkG,IAAA;EAAA9G,UAAA,EAAc;AAAM;AAErI;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KA7ZiFxE,EAAE,CAAA2L,iBAAA,CA6ZQgD,MAAM,EAAc,CAAC;IACpGhF,IAAI,EAAE7I,UAAU;IAChB8K,IAAI,EAAE,CAAC;MAAEmC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,SAAS4C,cAAcA,CAACmE,KAAK,EAAEjO,QAAQ,EAAE;EACrC,IAAIsN,CAAC,GAAGW,KAAK,CAAC3F,MAAM;EACpB,OAAOgF,CAAC,EAAE,EAAE;IACRtN,QAAQ,CAACiO,KAAK,CAACX,CAAC,CAAC,CAAC;EACtB;AACJ;AAEA,MAAMY,YAAY,CAAC;AAWlBC,aAAA,GAXKD,YAAY;AAAA/Q,eAAA,CAAZ+Q,YAAY,wBAAAE,sBAAAxL,iBAAA;EAAA,YAAAA,iBAAA,IACqFsL,aAAY;AAAA;AAAA/Q,eAAA,CAD7G+Q,YAAY,8BA5a+D/U,EAAE,CAAAkV,gBAAA;EAAAvL,IAAA,EA8aqBoL,aAAY;EAAA/I,OAAA,GAAY/I,aAAa,EAAErB,YAAY,EAAEY,UAAU,EAAE2B,kBAAkB;EAAAgR,OAAA;EAC/K;EACA;EACAvT,YAAY,EACZuC,kBAAkB;AAAA;AAAAH,eAAA,CANxB+Q,YAAY,8BA5a+D/U,EAAE,CAAAoV,gBAAA;EAAA/C,SAAA,EAmb8C,CAAC1D,MAAM,CAAC;EAAA3C,OAAA,GAAY/I,aAAa,EAAErB,YAAY,EAAEY,UAAU;EAChL;EACA;EACAZ,YAAY;AAAA;AAExB;EAAA,QAAA4C,SAAA,oBAAAA,SAAA,KAxbiFxE,EAAE,CAAA2L,iBAAA,CAwbQoJ,YAAY,EAAc,CAAC;IAC1GpL,IAAI,EAAE5I,QAAQ;IACd6K,IAAI,EAAE,CAAC;MACCI,OAAO,EAAE,CAAC/I,aAAa,EAAErB,YAAY,EAAEY,UAAU,EAAE2B,kBAAkB,CAAC;MACtEgR,OAAO,EAAE;MACL;MACA;MACAvT,YAAY,EACZuC,kBAAkB,CACrB;MACDkO,SAAS,EAAE,CAAC1D,MAAM;IACtB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASxK,kBAAkB,EAAEkK,qBAAqB,EAAED,WAAW,EAAEN,sBAAsB,EAAES,+BAA+B,EAAED,uCAAuC,EAAEK,MAAM,EAAE7K,YAAY,EAAEiR,YAAY,EAAE5I,SAAS,EAAElI,sCAAsC,EAAE3C,eAAe,IAAI+T,iBAAiB;AAC9R", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}