{"ast": null, "code": "var _TestWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_DYNAMIC_FORM_WIDGET_CONFIG } from '../../dynamic-form.model';\nimport { MatDynamicFormWidgetComponent } from '../../mat-dynamic-form-widget.component';\nlet TestWidgetComponent = (_TestWidgetComponent = class TestWidgetComponent extends MatDynamicFormWidgetComponent {\n  constructor(config) {\n    super(config);\n  }\n}, _TestWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_DYNAMIC_FORM_WIDGET_CONFIG]\n  }]\n}], _TestWidgetComponent);\nTestWidgetComponent = __decorate([Component({\n  template: '<ng-container *ngIf=\"option\">{{ id }}: {{ option | json }}; submitted:{{ submitted }}</ng-container>',\n  standalone: false\n})], TestWidgetComponent);\nexport { TestWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "Component", "Inject", "SWUI_DYNAMIC_FORM_WIDGET_CONFIG", "MatDynamicFormWidgetComponent", "TestWidgetComponent", "_TestWidgetComponent", "constructor", "config", "ctorParameters", "type", "undefined", "decorators", "args", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-dynamic-form/_stories/test-widget/test-widget.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_DYNAMIC_FORM_WIDGET_CONFIG } from '../../dynamic-form.model';\nimport { MatDynamicFormWidgetComponent } from '../../mat-dynamic-form-widget.component';\nlet TestWidgetComponent = class TestWidgetComponent extends MatDynamicFormWidgetComponent {\n    constructor(config) {\n        super(config);\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_DYNAMIC_FORM_WIDGET_CONFIG,] }] }\n    ]; }\n};\nTestWidgetComponent = __decorate([\n    Component({\n        template: '<ng-container *ngIf=\"option\">{{ id }}: {{ option | json }}; submitted:{{ submitted }}</ng-container>',\n        standalone: false\n    })\n], TestWidgetComponent);\nexport { TestWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,+BAA+B,QAAQ,0BAA0B;AAC1E,SAASC,6BAA6B,QAAQ,yCAAyC;AACvF,IAAIC,mBAAmB,IAAAC,oBAAA,GAAG,MAAMD,mBAAmB,SAASD,6BAA6B,CAAC;EACtFG,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAACA,MAAM,CAAC;EACjB;AAIJ,CAAC,EAHYF,oBAAA,CAAKG,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAER,MAAM;IAAEW,IAAI,EAAE,CAACV,+BAA+B;EAAG,CAAC;AAAE,CAAC,CAChG,EAAAG,oBAAA,CACJ;AACDD,mBAAmB,GAAGL,UAAU,CAAC,CAC7BC,SAAS,CAAC;EACNa,QAAQ,EAAE,sGAAsG;EAChHC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEV,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}