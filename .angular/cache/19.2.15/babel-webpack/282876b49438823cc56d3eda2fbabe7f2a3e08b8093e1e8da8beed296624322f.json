{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _BulkActionsComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./bulk-actions.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./bulk-actions.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { NoDataDialogComponent } from './dialogs/no-data-dialog.component';\nimport { LessDialogComponent } from './dialogs/less-dialog.component';\nimport { ConfirmDialogComponent } from './dialogs/confirm-dialog.component';\nlet BulkActionsComponent = (_BulkActionsComponent = class BulkActionsComponent {\n  constructor(dialog) {\n    this.dialog = dialog;\n    this.rows = [];\n    this.actions = [];\n  }\n  prepareAction(event, action) {\n    event.preventDefault();\n    this.availableRows = this.rows.filter(action.canActivateFn);\n    this.currentAction = action;\n    if (!this.availableRows.length) {\n      this.dialogRef = this.dialog.open(NoDataDialogComponent, {\n        width: '400px',\n        data: {\n          currentAction: this.currentAction,\n          declineAction: this.declineAction.bind(this)\n        }\n      });\n    } else if (this.availableRows.length < this.rows.length) {\n      this.dialogRef = this.dialog.open(LessDialogComponent, {\n        width: '400px',\n        data: {\n          available: this.availableRows,\n          checked: this.rows,\n          declineAction: this.declineAction.bind(this),\n          runAction: this.runAction.bind(this)\n        }\n      });\n    } else if (!!this.currentAction.dialog) {\n      const {\n        componentRef,\n        config = {}\n      } = this.currentAction.dialog;\n      this.dialogRef = this.dialog.open(componentRef, _objectSpread(_objectSpread({}, config), {}, {\n        data: {\n          rows: this.availableRows,\n          declineAction: this.declineAction.bind(this)\n        }\n      }));\n    } else {\n      this.dialogRef = this.dialog.open(ConfirmDialogComponent, {\n        width: '400px',\n        data: {\n          runAction: this.runAction.bind(this),\n          declineAction: this.declineAction.bind(this)\n        }\n      });\n    }\n  }\n  runAction() {\n    if (this.currentAction) {\n      this.currentAction.fn(this.availableRows);\n    }\n    this.declineAction();\n  }\n  declineAction() {\n    this.currentAction = undefined;\n    this.availableRows = undefined;\n    if (this.dialogRef) {\n      this.dialogRef.close();\n    }\n  }\n  emptyAction(event) {\n    event.preventDefault();\n    event.stopImmediatePropagation();\n  }\n}, _BulkActionsComponent.ctorParameters = () => [{\n  type: MatDialog\n}], _BulkActionsComponent.propDecorators = {\n  rows: [{\n    type: Input\n  }],\n  actions: [{\n    type: Input\n  }]\n}, _BulkActionsComponent);\nBulkActionsComponent = __decorate([Component({\n  selector: 'lib-swui-bulk-actions',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], BulkActionsComponent);\nexport { BulkActionsComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Input", "MatDialog", "NoDataDialogComponent", "LessDialogComponent", "ConfirmDialogComponent", "BulkActionsComponent", "_BulkActionsComponent", "constructor", "dialog", "rows", "actions", "prepareAction", "event", "action", "preventDefault", "availableRows", "filter", "canActivateFn", "currentAction", "length", "dialogRef", "open", "width", "data", "declineAction", "bind", "available", "checked", "runAction", "componentRef", "config", "_objectSpread", "fn", "undefined", "close", "emptyAction", "stopImmediatePropagation", "ctorParameters", "type", "propDecorators", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/bulk-actions/bulk-actions.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./bulk-actions.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./bulk-actions.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { NoDataDialogComponent } from './dialogs/no-data-dialog.component';\nimport { LessDialogComponent } from './dialogs/less-dialog.component';\nimport { ConfirmDialogComponent } from './dialogs/confirm-dialog.component';\nlet BulkActionsComponent = class BulkActionsComponent {\n    constructor(dialog) {\n        this.dialog = dialog;\n        this.rows = [];\n        this.actions = [];\n    }\n    prepareAction(event, action) {\n        event.preventDefault();\n        this.availableRows = this.rows.filter(action.canActivateFn);\n        this.currentAction = action;\n        if (!this.availableRows.length) {\n            this.dialogRef = this.dialog.open(NoDataDialogComponent, {\n                width: '400px',\n                data: {\n                    currentAction: this.currentAction,\n                    declineAction: this.declineAction.bind(this),\n                }\n            });\n        }\n        else if (this.availableRows.length < this.rows.length) {\n            this.dialogRef = this.dialog.open(LessDialogComponent, {\n                width: '400px',\n                data: {\n                    available: this.availableRows,\n                    checked: this.rows,\n                    declineAction: this.declineAction.bind(this),\n                    runAction: this.runAction.bind(this),\n                }\n            });\n        }\n        else if (!!this.currentAction.dialog) {\n            const { componentRef, config = {} } = this.currentAction.dialog;\n            this.dialogRef = this.dialog.open(componentRef, {\n                ...config,\n                data: {\n                    rows: this.availableRows,\n                    declineAction: this.declineAction.bind(this)\n                }\n            });\n        }\n        else {\n            this.dialogRef = this.dialog.open(ConfirmDialogComponent, {\n                width: '400px',\n                data: {\n                    runAction: this.runAction.bind(this),\n                    declineAction: this.declineAction.bind(this),\n                }\n            });\n        }\n    }\n    runAction() {\n        if (this.currentAction) {\n            this.currentAction.fn(this.availableRows);\n        }\n        this.declineAction();\n    }\n    declineAction() {\n        this.currentAction = undefined;\n        this.availableRows = undefined;\n        if (this.dialogRef) {\n            this.dialogRef.close();\n        }\n    }\n    emptyAction(event) {\n        event.preventDefault();\n        event.stopImmediatePropagation();\n    }\n    static { this.ctorParameters = () => [\n        { type: MatDialog }\n    ]; }\n    static { this.propDecorators = {\n        rows: [{ type: Input }],\n        actions: [{ type: Input }]\n    }; }\n};\nBulkActionsComponent = __decorate([\n    Component({\n        selector: 'lib-swui-bulk-actions',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], BulkActionsComponent);\nexport { BulkActionsComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,IAAIC,oBAAoB,IAAAC,qBAAA,GAAG,MAAMD,oBAAoB,CAAC;EAClDE,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,OAAO,GAAG,EAAE;EACrB;EACAC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACzBD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtB,IAAI,CAACC,aAAa,GAAG,IAAI,CAACN,IAAI,CAACO,MAAM,CAACH,MAAM,CAACI,aAAa,CAAC;IAC3D,IAAI,CAACC,aAAa,GAAGL,MAAM;IAC3B,IAAI,CAAC,IAAI,CAACE,aAAa,CAACI,MAAM,EAAE;MAC5B,IAAI,CAACC,SAAS,GAAG,IAAI,CAACZ,MAAM,CAACa,IAAI,CAACnB,qBAAqB,EAAE;QACrDoB,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;UACFL,aAAa,EAAE,IAAI,CAACA,aAAa;UACjCM,aAAa,EAAE,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI;QAC/C;MACJ,CAAC,CAAC;IACN,CAAC,MACI,IAAI,IAAI,CAACV,aAAa,CAACI,MAAM,GAAG,IAAI,CAACV,IAAI,CAACU,MAAM,EAAE;MACnD,IAAI,CAACC,SAAS,GAAG,IAAI,CAACZ,MAAM,CAACa,IAAI,CAAClB,mBAAmB,EAAE;QACnDmB,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;UACFG,SAAS,EAAE,IAAI,CAACX,aAAa;UAC7BY,OAAO,EAAE,IAAI,CAAClB,IAAI;UAClBe,aAAa,EAAE,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC;UAC5CG,SAAS,EAAE,IAAI,CAACA,SAAS,CAACH,IAAI,CAAC,IAAI;QACvC;MACJ,CAAC,CAAC;IACN,CAAC,MACI,IAAI,CAAC,CAAC,IAAI,CAACP,aAAa,CAACV,MAAM,EAAE;MAClC,MAAM;QAAEqB,YAAY;QAAEC,MAAM,GAAG,CAAC;MAAE,CAAC,GAAG,IAAI,CAACZ,aAAa,CAACV,MAAM;MAC/D,IAAI,CAACY,SAAS,GAAG,IAAI,CAACZ,MAAM,CAACa,IAAI,CAACQ,YAAY,EAAAE,aAAA,CAAAA,aAAA,KACvCD,MAAM;QACTP,IAAI,EAAE;UACFd,IAAI,EAAE,IAAI,CAACM,aAAa;UACxBS,aAAa,EAAE,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI;QAC/C;MAAC,EACJ,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACL,SAAS,GAAG,IAAI,CAACZ,MAAM,CAACa,IAAI,CAACjB,sBAAsB,EAAE;QACtDkB,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;UACFK,SAAS,EAAE,IAAI,CAACA,SAAS,CAACH,IAAI,CAAC,IAAI,CAAC;UACpCD,aAAa,EAAE,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI;QAC/C;MACJ,CAAC,CAAC;IACN;EACJ;EACAG,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACV,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACc,EAAE,CAAC,IAAI,CAACjB,aAAa,CAAC;IAC7C;IACA,IAAI,CAACS,aAAa,CAAC,CAAC;EACxB;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACN,aAAa,GAAGe,SAAS;IAC9B,IAAI,CAAClB,aAAa,GAAGkB,SAAS;IAC9B,IAAI,IAAI,CAACb,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACc,KAAK,CAAC,CAAC;IAC1B;EACJ;EACAC,WAAWA,CAACvB,KAAK,EAAE;IACfA,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACwB,wBAAwB,CAAC,CAAC;EACpC;AAQJ,CAAC,EAPY9B,qBAAA,CAAK+B,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAErC;AAAU,CAAC,CACtB,EACQK,qBAAA,CAAKiC,cAAc,GAAG;EAC3B9B,IAAI,EAAE,CAAC;IAAE6B,IAAI,EAAEtC;EAAM,CAAC,CAAC;EACvBU,OAAO,EAAE,CAAC;IAAE4B,IAAI,EAAEtC;EAAM,CAAC;AAC7B,CAAC,EAAAM,qBAAA,CACJ;AACDD,oBAAoB,GAAGT,UAAU,CAAC,CAC9BG,SAAS,CAAC;EACNyC,QAAQ,EAAE,uBAAuB;EACjCC,QAAQ,EAAE5C,oBAAoB;EAC9B6C,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC7C,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEO,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}