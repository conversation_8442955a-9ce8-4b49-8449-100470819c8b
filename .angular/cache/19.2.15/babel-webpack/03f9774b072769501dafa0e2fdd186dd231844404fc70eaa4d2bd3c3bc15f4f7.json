{"ast": null, "code": "import * as moment from 'moment';\nexport const CUSTOM_PERIODS = [{\n  title: 'COMPONENTS.DATERANGE.today',\n  fn: () => {\n    const from = moment.utc().set({\n      hour: 0,\n      minute: 0,\n      second: 0,\n      millisecond: 0\n    });\n    const to = from.clone().add(1, 'd');\n    return {\n      from,\n      to\n    };\n  }\n}, {\n  title: 'COMPONENTS.DATERANGE.yesterday',\n  fn: () => {\n    const from = moment.utc().subtract(1, 'd').set({\n      hour: 0,\n      minute: 0,\n      second: 0,\n      millisecond: 0\n    });\n    const to = from.clone().add(1, 'd');\n    return {\n      from,\n      to\n    };\n  }\n}, {\n  title: 'COMPONENTS.DATERANGE.last3Days',\n  fn: () => {\n    const to = moment.utc().add(1, 'd').set({\n      hour: 0,\n      minute: 0,\n      second: 0,\n      millisecond: 0\n    });\n    const from = to.clone().subtract(3, 'd');\n    return {\n      from,\n      to\n    };\n  }\n}, {\n  title: 'COMPONENTS.DATERANGE.last7Days',\n  fn: () => {\n    const to = moment.utc().add(1, 'd').set({\n      hour: 0,\n      minute: 0,\n      second: 0,\n      millisecond: 0\n    });\n    const from = to.clone().subtract(7, 'd');\n    return {\n      from,\n      to\n    };\n  }\n}, {\n  title: 'COMPONENTS.DATERANGE.thisMonth',\n  fn: () => {\n    const from = moment.utc().startOf('month');\n    const to = from.clone().add(1, 'month');\n    return {\n      from,\n      to\n    };\n  }\n}, {\n  title: 'COMPONENTS.DATERANGE.lastMonth',\n  fn: () => {\n    const from = moment.utc().subtract(1, 'month').startOf('month');\n    const to = moment.utc().startOf('month');\n    return {\n      from,\n      to\n    };\n  }\n}];", "map": {"version": 3, "names": ["moment", "CUSTOM_PERIODS", "title", "fn", "from", "utc", "set", "hour", "minute", "second", "millisecond", "to", "clone", "add", "subtract", "startOf"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-range/custom-period/custom-period.interface.ts"], "sourcesContent": ["import * as moment from 'moment';\nexport const CUSTOM_PERIODS = [\n    {\n        title: 'COMPONENTS.DATERANGE.today',\n        fn: () => {\n            const from = moment.utc().set({ hour: 0, minute: 0, second: 0, millisecond: 0 });\n            const to = from.clone().add(1, 'd');\n            return { from, to };\n        },\n    },\n    {\n        title: 'COMPONENTS.DATERANGE.yesterday',\n        fn: () => {\n            const from = moment.utc().subtract(1, 'd').set({ hour: 0, minute: 0, second: 0, millisecond: 0 });\n            const to = from.clone().add(1, 'd');\n            return { from, to };\n        },\n    },\n    {\n        title: 'COMPONENTS.DATERANGE.last3Days',\n        fn: () => {\n            const to = moment.utc().add(1, 'd').set({ hour: 0, minute: 0, second: 0, millisecond: 0 });\n            const from = to.clone().subtract(3, 'd');\n            return { from, to };\n        },\n    },\n    {\n        title: 'COMPONENTS.DATERANGE.last7Days',\n        fn: () => {\n            const to = moment.utc().add(1, 'd').set({ hour: 0, minute: 0, second: 0, millisecond: 0 });\n            const from = to.clone().subtract(7, 'd');\n            return { from, to };\n        },\n    },\n    {\n        title: 'COMPONENTS.DATERANGE.thisMonth',\n        fn: () => {\n            const from = moment.utc().startOf('month');\n            const to = from.clone().add(1, 'month');\n            return { from, to };\n        },\n    },\n    {\n        title: 'COMPONENTS.DATERANGE.lastMonth',\n        fn: () => {\n            const from = moment.utc().subtract(1, 'month').startOf('month');\n            const to = moment.utc().startOf('month');\n            return { from, to };\n        },\n    },\n];\n"], "mappings": "AAAA,OAAO,KAAKA,MAAM,MAAM,QAAQ;AAChC,OAAO,MAAMC,cAAc,GAAG,CAC1B;EACIC,KAAK,EAAE,4BAA4B;EACnCC,EAAE,EAAEA,CAAA,KAAM;IACN,MAAMC,IAAI,GAAGJ,MAAM,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,CAAC;IAChF,MAAMC,EAAE,GAAGP,IAAI,CAACQ,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;IACnC,OAAO;MAAET,IAAI;MAAEO;IAAG,CAAC;EACvB;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,gCAAgC;EACvCC,EAAE,EAAEA,CAAA,KAAM;IACN,MAAMC,IAAI,GAAGJ,MAAM,CAACK,GAAG,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAACR,GAAG,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,CAAC;IACjG,MAAMC,EAAE,GAAGP,IAAI,CAACQ,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;IACnC,OAAO;MAAET,IAAI;MAAEO;IAAG,CAAC;EACvB;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,gCAAgC;EACvCC,EAAE,EAAEA,CAAA,KAAM;IACN,MAAMQ,EAAE,GAAGX,MAAM,CAACK,GAAG,CAAC,CAAC,CAACQ,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAACP,GAAG,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,CAAC;IAC1F,MAAMN,IAAI,GAAGO,EAAE,CAACC,KAAK,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACxC,OAAO;MAAEV,IAAI;MAAEO;IAAG,CAAC;EACvB;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,gCAAgC;EACvCC,EAAE,EAAEA,CAAA,KAAM;IACN,MAAMQ,EAAE,GAAGX,MAAM,CAACK,GAAG,CAAC,CAAC,CAACQ,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAACP,GAAG,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC,CAAC;IAC1F,MAAMN,IAAI,GAAGO,EAAE,CAACC,KAAK,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACxC,OAAO;MAAEV,IAAI;MAAEO;IAAG,CAAC;EACvB;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,gCAAgC;EACvCC,EAAE,EAAEA,CAAA,KAAM;IACN,MAAMC,IAAI,GAAGJ,MAAM,CAACK,GAAG,CAAC,CAAC,CAACU,OAAO,CAAC,OAAO,CAAC;IAC1C,MAAMJ,EAAE,GAAGP,IAAI,CAACQ,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;IACvC,OAAO;MAAET,IAAI;MAAEO;IAAG,CAAC;EACvB;AACJ,CAAC,EACD;EACIT,KAAK,EAAE,gCAAgC;EACvCC,EAAE,EAAEA,CAAA,KAAM;IACN,MAAMC,IAAI,GAAGJ,MAAM,CAACK,GAAG,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC;IAC/D,MAAMJ,EAAE,GAAGX,MAAM,CAACK,GAAG,CAAC,CAAC,CAACU,OAAO,CAAC,OAAO,CAAC;IACxC,OAAO;MAAEX,IAAI;MAAEO;IAAG,CAAC;EACvB;AACJ,CAAC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}