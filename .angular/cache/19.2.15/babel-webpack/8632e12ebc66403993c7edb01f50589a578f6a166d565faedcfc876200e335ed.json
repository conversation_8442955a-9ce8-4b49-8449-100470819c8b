{"ast": null, "code": "import _defineProperty from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport { Subject } from 'rxjs';\nimport { tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { A, Z, b as ZERO, N as NINE } from './keycodes-CpHkExLC.mjs';\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n  constructor(initialItems, config) {\n    _defineProperty(this, \"_letterKeyStream\", new Subject());\n    _defineProperty(this, \"_items\", []);\n    _defineProperty(this, \"_selectedItemIndex\", -1);\n    /** Buffer for the letters that the user has pressed */\n    _defineProperty(this, \"_pressedLetters\", []);\n    _defineProperty(this, \"_skipPredicateFn\", void 0);\n    _defineProperty(this, \"_selectedItem\", new Subject());\n    _defineProperty(this, \"selectedItem\", this._selectedItem);\n    const typeAheadInterval = typeof (config === null || config === void 0 ? void 0 : config.debounceInterval) === 'number' ? config.debounceInterval : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n    if (config !== null && config !== void 0 && config.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && initialItems.length && initialItems.some(item => typeof item.getLabel !== 'function')) {\n      throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n    this.setItems(initialItems);\n    this._setupKeyHandler(typeAheadInterval);\n  }\n  destroy() {\n    this._pressedLetters = [];\n    this._letterKeyStream.complete();\n    this._selectedItem.complete();\n  }\n  setCurrentSelectedItemIndex(index) {\n    this._selectedItemIndex = index;\n  }\n  setItems(items) {\n    this._items = items;\n  }\n  handleKey(event) {\n    const keyCode = event.keyCode;\n    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n    if (event.key && event.key.length === 1) {\n      this._letterKeyStream.next(event.key.toLocaleUpperCase());\n    } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n      this._letterKeyStream.next(String.fromCharCode(keyCode));\n    }\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Resets the currently stored sequence of typed letters. */\n  reset() {\n    this._pressedLetters = [];\n  }\n  _setupKeyHandler(typeAheadInterval) {\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase())).subscribe(inputString => {\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < this._items.length + 1; i++) {\n        var _this$_skipPredicateF, _item$getLabel;\n        const index = (this._selectedItemIndex + i) % this._items.length;\n        const item = this._items[index];\n        if (!((_this$_skipPredicateF = this._skipPredicateFn) !== null && _this$_skipPredicateF !== void 0 && _this$_skipPredicateF.call(this, item)) && ((_item$getLabel = item.getLabel) === null || _item$getLabel === void 0 ? void 0 : _item$getLabel.call(item).toLocaleUpperCase().trim().indexOf(inputString)) === 0) {\n          this._selectedItem.next(item);\n          break;\n        }\n      }\n      this._pressedLetters = [];\n    });\n  }\n}\nexport { Typeahead as T };\n//# sourceMappingURL=typeahead-9ZW4Dtsf.mjs.map", "map": {"version": 3, "names": ["Subject", "tap", "debounceTime", "filter", "map", "A", "Z", "b", "ZERO", "N", "NINE", "DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS", "Typeahead", "constructor", "initialItems", "config", "_defineProperty", "_selectedItem", "typeAheadInterval", "debounceInterval", "skipPredicate", "_skipPredicateFn", "ngDevMode", "length", "some", "item", "get<PERSON><PERSON><PERSON>", "Error", "setItems", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destroy", "_pressedLetters", "_letterKeyStream", "complete", "setCurrentSelectedItemIndex", "index", "_selectedItemIndex", "items", "_items", "handle<PERSON>ey", "event", "keyCode", "key", "next", "toLocaleUpperCase", "String", "fromCharCode", "isTyping", "reset", "pipe", "letter", "push", "join", "subscribe", "inputString", "i", "_this$_skipPredicateF", "_item$getLabel", "call", "trim", "indexOf", "T"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@angular/cdk/fesm2022/typeahead-9ZW4Dtsf.mjs"], "sourcesContent": ["import { Subject } from 'rxjs';\nimport { tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { A, Z, b as ZERO, N as NINE } from './keycodes-CpHkExLC.mjs';\n\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n    _letterKeyStream = new Subject();\n    _items = [];\n    _selectedItemIndex = -1;\n    /** Buffer for the letters that the user has pressed */\n    _pressedLetters = [];\n    _skipPredicateFn;\n    _selectedItem = new Subject();\n    selectedItem = this._selectedItem;\n    constructor(initialItems, config) {\n        const typeAheadInterval = typeof config?.debounceInterval === 'number'\n            ? config.debounceInterval\n            : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n        if (config?.skipPredicate) {\n            this._skipPredicateFn = config.skipPredicate;\n        }\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n            initialItems.length &&\n            initialItems.some(item => typeof item.getLabel !== 'function')) {\n            throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n        }\n        this.setItems(initialItems);\n        this._setupKeyHandler(typeAheadInterval);\n    }\n    destroy() {\n        this._pressedLetters = [];\n        this._letterKeyStream.complete();\n        this._selectedItem.complete();\n    }\n    setCurrentSelectedItemIndex(index) {\n        this._selectedItemIndex = index;\n    }\n    setItems(items) {\n        this._items = items;\n    }\n    handleKey(event) {\n        const keyCode = event.keyCode;\n        // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n        // otherwise fall back to resolving alphanumeric characters via the keyCode.\n        if (event.key && event.key.length === 1) {\n            this._letterKeyStream.next(event.key.toLocaleUpperCase());\n        }\n        else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n            this._letterKeyStream.next(String.fromCharCode(keyCode));\n        }\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return this._pressedLetters.length > 0;\n    }\n    /** Resets the currently stored sequence of typed letters. */\n    reset() {\n        this._pressedLetters = [];\n    }\n    _setupKeyHandler(typeAheadInterval) {\n        // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n        // and convert those letters back into a string. Afterwards find the first item that starts\n        // with that string and select it.\n        this._letterKeyStream\n            .pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase()))\n            .subscribe(inputString => {\n            // Start at 1 because we want to start searching at the item immediately\n            // following the current active item.\n            for (let i = 1; i < this._items.length + 1; i++) {\n                const index = (this._selectedItemIndex + i) % this._items.length;\n                const item = this._items[index];\n                if (!this._skipPredicateFn?.(item) &&\n                    item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n                    this._selectedItem.next(item);\n                    break;\n                }\n            }\n            this._pressedLetters = [];\n        });\n    }\n}\n\nexport { Typeahead as T };\n//# sourceMappingURL=typeahead-9ZW4Dtsf.mjs.map\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,GAAG,EAAEC,YAAY,EAAEC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAC/D,SAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,IAAI,QAAQ,yBAAyB;AAEpE,MAAMC,sCAAsC,GAAG,GAAG;AAClD;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EASZC,WAAWA,CAACC,YAAY,EAAEC,MAAM,EAAE;IAAAC,eAAA,2BARf,IAAIhB,OAAO,CAAC,CAAC;IAAAgB,eAAA,iBACvB,EAAE;IAAAA,eAAA,6BACU,CAAC,CAAC;IACvB;IAAAA,eAAA,0BACkB,EAAE;IAAAA,eAAA;IAAAA,eAAA,wBAEJ,IAAIhB,OAAO,CAAC,CAAC;IAAAgB,eAAA,uBACd,IAAI,CAACC,aAAa;IAE7B,MAAMC,iBAAiB,GAAG,QAAOH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,gBAAgB,MAAK,QAAQ,GAChEJ,MAAM,CAACI,gBAAgB,GACvBR,sCAAsC;IAC5C,IAAII,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEK,aAAa,EAAE;MACvB,IAAI,CAACC,gBAAgB,GAAGN,MAAM,CAACK,aAAa;IAChD;IACA,IAAI,CAAC,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9CR,YAAY,CAACS,MAAM,IACnBT,YAAY,CAACU,IAAI,CAACC,IAAI,IAAI,OAAOA,IAAI,CAACC,QAAQ,KAAK,UAAU,CAAC,EAAE;MAChE,MAAM,IAAIC,KAAK,CAAC,0EAA0E,CAAC;IAC/F;IACA,IAAI,CAACC,QAAQ,CAACd,YAAY,CAAC;IAC3B,IAAI,CAACe,gBAAgB,CAACX,iBAAiB,CAAC;EAC5C;EACAY,OAAOA,CAAA,EAAG;IACN,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,gBAAgB,CAACC,QAAQ,CAAC,CAAC;IAChC,IAAI,CAAChB,aAAa,CAACgB,QAAQ,CAAC,CAAC;EACjC;EACAC,2BAA2BA,CAACC,KAAK,EAAE;IAC/B,IAAI,CAACC,kBAAkB,GAAGD,KAAK;EACnC;EACAP,QAAQA,CAACS,KAAK,EAAE;IACZ,IAAI,CAACC,MAAM,GAAGD,KAAK;EACvB;EACAE,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC7B;IACA;IACA,IAAID,KAAK,CAACE,GAAG,IAAIF,KAAK,CAACE,GAAG,CAACnB,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACS,gBAAgB,CAACW,IAAI,CAACH,KAAK,CAACE,GAAG,CAACE,iBAAiB,CAAC,CAAC,CAAC;IAC7D,CAAC,MACI,IAAKH,OAAO,IAAIpC,CAAC,IAAIoC,OAAO,IAAInC,CAAC,IAAMmC,OAAO,IAAIjC,IAAI,IAAIiC,OAAO,IAAI/B,IAAK,EAAE;MAC7E,IAAI,CAACsB,gBAAgB,CAACW,IAAI,CAACE,MAAM,CAACC,YAAY,CAACL,OAAO,CAAC,CAAC;IAC5D;EACJ;EACA;EACAM,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAChB,eAAe,CAACR,MAAM,GAAG,CAAC;EAC1C;EACA;EACAyB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACjB,eAAe,GAAG,EAAE;EAC7B;EACAF,gBAAgBA,CAACX,iBAAiB,EAAE;IAChC;IACA;IACA;IACA,IAAI,CAACc,gBAAgB,CAChBiB,IAAI,CAAChD,GAAG,CAACiD,MAAM,IAAI,IAAI,CAACnB,eAAe,CAACoB,IAAI,CAACD,MAAM,CAAC,CAAC,EAAEhD,YAAY,CAACgB,iBAAiB,CAAC,EAAEf,MAAM,CAAC,MAAM,IAAI,CAAC4B,eAAe,CAACR,MAAM,GAAG,CAAC,CAAC,EAAEnB,GAAG,CAAC,MAAM,IAAI,CAAC2B,eAAe,CAACqB,IAAI,CAAC,EAAE,CAAC,CAACR,iBAAiB,CAAC,CAAC,CAAC,CAAC,CACpMS,SAAS,CAACC,WAAW,IAAI;MAC1B;MACA;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjB,MAAM,CAACf,MAAM,GAAG,CAAC,EAAEgC,CAAC,EAAE,EAAE;QAAA,IAAAC,qBAAA,EAAAC,cAAA;QAC7C,MAAMtB,KAAK,GAAG,CAAC,IAAI,CAACC,kBAAkB,GAAGmB,CAAC,IAAI,IAAI,CAACjB,MAAM,CAACf,MAAM;QAChE,MAAME,IAAI,GAAG,IAAI,CAACa,MAAM,CAACH,KAAK,CAAC;QAC/B,IAAI,GAAAqB,qBAAA,GAAC,IAAI,CAACnC,gBAAgB,cAAAmC,qBAAA,eAArBA,qBAAA,CAAAE,IAAA,KAAI,EAAoBjC,IAAI,CAAC,KAC9B,EAAAgC,cAAA,GAAAhC,IAAI,CAACC,QAAQ,cAAA+B,cAAA,uBAAbA,cAAA,CAAAC,IAAA,CAAAjC,IAAgB,CAAC,CAACmB,iBAAiB,CAAC,CAAC,CAACe,IAAI,CAAC,CAAC,CAACC,OAAO,CAACN,WAAW,CAAC,MAAK,CAAC,EAAE;UACzE,IAAI,CAACrC,aAAa,CAAC0B,IAAI,CAAClB,IAAI,CAAC;UAC7B;QACJ;MACJ;MACA,IAAI,CAACM,eAAe,GAAG,EAAE;IAC7B,CAAC,CAAC;EACN;AACJ;AAEA,SAASnB,SAAS,IAAIiD,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}