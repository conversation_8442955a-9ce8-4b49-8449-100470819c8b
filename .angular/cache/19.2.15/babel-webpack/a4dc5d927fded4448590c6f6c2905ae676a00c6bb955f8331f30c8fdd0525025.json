{"ast": null, "code": "import { __read, __spreadArray, __values } from \"tslib\";\nimport { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { arrRemove } from './util/arrRemove';\nvar Subscription = function () {\n  function Subscription(initialTeardown) {\n    this.initialTeardown = initialTeardown;\n    this.closed = false;\n    this._parentage = null;\n    this._finalizers = null;\n  }\n  Subscription.prototype.unsubscribe = function () {\n    var e_1, _a, e_2, _b;\n    var errors;\n    if (!this.closed) {\n      this.closed = true;\n      var _parentage = this._parentage;\n      if (_parentage) {\n        this._parentage = null;\n        if (Array.isArray(_parentage)) {\n          try {\n            for (var _parentage_1 = __values(_parentage), _parentage_1_1 = _parentage_1.next(); !_parentage_1_1.done; _parentage_1_1 = _parentage_1.next()) {\n              var parent_1 = _parentage_1_1.value;\n              parent_1.remove(this);\n            }\n          } catch (e_1_1) {\n            e_1 = {\n              error: e_1_1\n            };\n          } finally {\n            try {\n              if (_parentage_1_1 && !_parentage_1_1.done && (_a = _parentage_1.return)) _a.call(_parentage_1);\n            } finally {\n              if (e_1) throw e_1.error;\n            }\n          }\n        } else {\n          _parentage.remove(this);\n        }\n      }\n      var initialFinalizer = this.initialTeardown;\n      if (isFunction(initialFinalizer)) {\n        try {\n          initialFinalizer();\n        } catch (e) {\n          errors = e instanceof UnsubscriptionError ? e.errors : [e];\n        }\n      }\n      var _finalizers = this._finalizers;\n      if (_finalizers) {\n        this._finalizers = null;\n        try {\n          for (var _finalizers_1 = __values(_finalizers), _finalizers_1_1 = _finalizers_1.next(); !_finalizers_1_1.done; _finalizers_1_1 = _finalizers_1.next()) {\n            var finalizer = _finalizers_1_1.value;\n            try {\n              execFinalizer(finalizer);\n            } catch (err) {\n              errors = errors !== null && errors !== void 0 ? errors : [];\n              if (err instanceof UnsubscriptionError) {\n                errors = __spreadArray(__spreadArray([], __read(errors)), __read(err.errors));\n              } else {\n                errors.push(err);\n              }\n            }\n          }\n        } catch (e_2_1) {\n          e_2 = {\n            error: e_2_1\n          };\n        } finally {\n          try {\n            if (_finalizers_1_1 && !_finalizers_1_1.done && (_b = _finalizers_1.return)) _b.call(_finalizers_1);\n          } finally {\n            if (e_2) throw e_2.error;\n          }\n        }\n      }\n      if (errors) {\n        throw new UnsubscriptionError(errors);\n      }\n    }\n  };\n  Subscription.prototype.add = function (teardown) {\n    var _a;\n    if (teardown && teardown !== this) {\n      if (this.closed) {\n        execFinalizer(teardown);\n      } else {\n        if (teardown instanceof Subscription) {\n          if (teardown.closed || teardown._hasParent(this)) {\n            return;\n          }\n          teardown._addParent(this);\n        }\n        (this._finalizers = (_a = this._finalizers) !== null && _a !== void 0 ? _a : []).push(teardown);\n      }\n    }\n  };\n  Subscription.prototype._hasParent = function (parent) {\n    var _parentage = this._parentage;\n    return _parentage === parent || Array.isArray(_parentage) && _parentage.includes(parent);\n  };\n  Subscription.prototype._addParent = function (parent) {\n    var _parentage = this._parentage;\n    this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n  };\n  Subscription.prototype._removeParent = function (parent) {\n    var _parentage = this._parentage;\n    if (_parentage === parent) {\n      this._parentage = null;\n    } else if (Array.isArray(_parentage)) {\n      arrRemove(_parentage, parent);\n    }\n  };\n  Subscription.prototype.remove = function (teardown) {\n    var _finalizers = this._finalizers;\n    _finalizers && arrRemove(_finalizers, teardown);\n    if (teardown instanceof Subscription) {\n      teardown._removeParent(this);\n    }\n  };\n  Subscription.EMPTY = function () {\n    var empty = new Subscription();\n    empty.closed = true;\n    return empty;\n  }();\n  return Subscription;\n}();\nexport { Subscription };\nexport var EMPTY_SUBSCRIPTION = Subscription.EMPTY;\nexport function isSubscription(value) {\n  return value instanceof Subscription || value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe);\n}\nfunction execFinalizer(finalizer) {\n  if (isFunction(finalizer)) {\n    finalizer();\n  } else {\n    finalizer.unsubscribe();\n  }\n}\n//# sourceMappingURL=Subscription.js.map", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "__values", "isFunction", "UnsubscriptionError", "arr<PERSON><PERSON><PERSON>", "Subscription", "initialTeardown", "closed", "_parentage", "_finalizers", "prototype", "unsubscribe", "e_1", "_a", "e_2", "_b", "errors", "Array", "isArray", "_parentage_1", "_parentage_1_1", "next", "done", "parent_1", "value", "remove", "e_1_1", "error", "return", "call", "initialFinalizer", "e", "_finalizers_1", "_finalizers_1_1", "finalizer", "execFinalizer", "err", "push", "e_2_1", "add", "teardown", "_hasParent", "_addParent", "parent", "includes", "_removeParent", "EMPTY", "empty", "EMPTY_SUBSCRIPTION", "isSubscription"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/Subscription.js"], "sourcesContent": ["import { __read, __spreadArray, __values } from \"tslib\";\nimport { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { arrRemove } from './util/arrRemove';\nvar Subscription = (function () {\n    function Subscription(initialTeardown) {\n        this.initialTeardown = initialTeardown;\n        this.closed = false;\n        this._parentage = null;\n        this._finalizers = null;\n    }\n    Subscription.prototype.unsubscribe = function () {\n        var e_1, _a, e_2, _b;\n        var errors;\n        if (!this.closed) {\n            this.closed = true;\n            var _parentage = this._parentage;\n            if (_parentage) {\n                this._parentage = null;\n                if (Array.isArray(_parentage)) {\n                    try {\n                        for (var _parentage_1 = __values(_parentage), _parentage_1_1 = _parentage_1.next(); !_parentage_1_1.done; _parentage_1_1 = _parentage_1.next()) {\n                            var parent_1 = _parentage_1_1.value;\n                            parent_1.remove(this);\n                        }\n                    }\n                    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                    finally {\n                        try {\n                            if (_parentage_1_1 && !_parentage_1_1.done && (_a = _parentage_1.return)) _a.call(_parentage_1);\n                        }\n                        finally { if (e_1) throw e_1.error; }\n                    }\n                }\n                else {\n                    _parentage.remove(this);\n                }\n            }\n            var initialFinalizer = this.initialTeardown;\n            if (isFunction(initialFinalizer)) {\n                try {\n                    initialFinalizer();\n                }\n                catch (e) {\n                    errors = e instanceof UnsubscriptionError ? e.errors : [e];\n                }\n            }\n            var _finalizers = this._finalizers;\n            if (_finalizers) {\n                this._finalizers = null;\n                try {\n                    for (var _finalizers_1 = __values(_finalizers), _finalizers_1_1 = _finalizers_1.next(); !_finalizers_1_1.done; _finalizers_1_1 = _finalizers_1.next()) {\n                        var finalizer = _finalizers_1_1.value;\n                        try {\n                            execFinalizer(finalizer);\n                        }\n                        catch (err) {\n                            errors = errors !== null && errors !== void 0 ? errors : [];\n                            if (err instanceof UnsubscriptionError) {\n                                errors = __spreadArray(__spreadArray([], __read(errors)), __read(err.errors));\n                            }\n                            else {\n                                errors.push(err);\n                            }\n                        }\n                    }\n                }\n                catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                finally {\n                    try {\n                        if (_finalizers_1_1 && !_finalizers_1_1.done && (_b = _finalizers_1.return)) _b.call(_finalizers_1);\n                    }\n                    finally { if (e_2) throw e_2.error; }\n                }\n            }\n            if (errors) {\n                throw new UnsubscriptionError(errors);\n            }\n        }\n    };\n    Subscription.prototype.add = function (teardown) {\n        var _a;\n        if (teardown && teardown !== this) {\n            if (this.closed) {\n                execFinalizer(teardown);\n            }\n            else {\n                if (teardown instanceof Subscription) {\n                    if (teardown.closed || teardown._hasParent(this)) {\n                        return;\n                    }\n                    teardown._addParent(this);\n                }\n                (this._finalizers = (_a = this._finalizers) !== null && _a !== void 0 ? _a : []).push(teardown);\n            }\n        }\n    };\n    Subscription.prototype._hasParent = function (parent) {\n        var _parentage = this._parentage;\n        return _parentage === parent || (Array.isArray(_parentage) && _parentage.includes(parent));\n    };\n    Subscription.prototype._addParent = function (parent) {\n        var _parentage = this._parentage;\n        this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n    };\n    Subscription.prototype._removeParent = function (parent) {\n        var _parentage = this._parentage;\n        if (_parentage === parent) {\n            this._parentage = null;\n        }\n        else if (Array.isArray(_parentage)) {\n            arrRemove(_parentage, parent);\n        }\n    };\n    Subscription.prototype.remove = function (teardown) {\n        var _finalizers = this._finalizers;\n        _finalizers && arrRemove(_finalizers, teardown);\n        if (teardown instanceof Subscription) {\n            teardown._removeParent(this);\n        }\n    };\n    Subscription.EMPTY = (function () {\n        var empty = new Subscription();\n        empty.closed = true;\n        return empty;\n    })();\n    return Subscription;\n}());\nexport { Subscription };\nexport var EMPTY_SUBSCRIPTION = Subscription.EMPTY;\nexport function isSubscription(value) {\n    return (value instanceof Subscription ||\n        (value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe)));\n}\nfunction execFinalizer(finalizer) {\n    if (isFunction(finalizer)) {\n        finalizer();\n    }\n    else {\n        finalizer.unsubscribe();\n    }\n}\n//# sourceMappingURL=Subscription.js.map"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AACvD,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,IAAIC,YAAY,GAAI,YAAY;EAC5B,SAASA,YAAYA,CAACC,eAAe,EAAE;IACnC,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACAJ,YAAY,CAACK,SAAS,CAACC,WAAW,GAAG,YAAY;IAC7C,IAAIC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IACpB,IAAIC,MAAM;IACV,IAAI,CAAC,IAAI,CAACT,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,IAAI;MAClB,IAAIC,UAAU,GAAG,IAAI,CAACA,UAAU;MAChC,IAAIA,UAAU,EAAE;QACZ,IAAI,CAACA,UAAU,GAAG,IAAI;QACtB,IAAIS,KAAK,CAACC,OAAO,CAACV,UAAU,CAAC,EAAE;UAC3B,IAAI;YACA,KAAK,IAAIW,YAAY,GAAGlB,QAAQ,CAACO,UAAU,CAAC,EAAEY,cAAc,GAAGD,YAAY,CAACE,IAAI,CAAC,CAAC,EAAE,CAACD,cAAc,CAACE,IAAI,EAAEF,cAAc,GAAGD,YAAY,CAACE,IAAI,CAAC,CAAC,EAAE;cAC5I,IAAIE,QAAQ,GAAGH,cAAc,CAACI,KAAK;cACnCD,QAAQ,CAACE,MAAM,CAAC,IAAI,CAAC;YACzB;UACJ,CAAC,CACD,OAAOC,KAAK,EAAE;YAAEd,GAAG,GAAG;cAAEe,KAAK,EAAED;YAAM,CAAC;UAAE,CAAC,SACjC;YACJ,IAAI;cACA,IAAIN,cAAc,IAAI,CAACA,cAAc,CAACE,IAAI,KAAKT,EAAE,GAAGM,YAAY,CAACS,MAAM,CAAC,EAAEf,EAAE,CAACgB,IAAI,CAACV,YAAY,CAAC;YACnG,CAAC,SACO;cAAE,IAAIP,GAAG,EAAE,MAAMA,GAAG,CAACe,KAAK;YAAE;UACxC;QACJ,CAAC,MACI;UACDnB,UAAU,CAACiB,MAAM,CAAC,IAAI,CAAC;QAC3B;MACJ;MACA,IAAIK,gBAAgB,GAAG,IAAI,CAACxB,eAAe;MAC3C,IAAIJ,UAAU,CAAC4B,gBAAgB,CAAC,EAAE;QAC9B,IAAI;UACAA,gBAAgB,CAAC,CAAC;QACtB,CAAC,CACD,OAAOC,CAAC,EAAE;UACNf,MAAM,GAAGe,CAAC,YAAY5B,mBAAmB,GAAG4B,CAAC,CAACf,MAAM,GAAG,CAACe,CAAC,CAAC;QAC9D;MACJ;MACA,IAAItB,WAAW,GAAG,IAAI,CAACA,WAAW;MAClC,IAAIA,WAAW,EAAE;QACb,IAAI,CAACA,WAAW,GAAG,IAAI;QACvB,IAAI;UACA,KAAK,IAAIuB,aAAa,GAAG/B,QAAQ,CAACQ,WAAW,CAAC,EAAEwB,eAAe,GAAGD,aAAa,CAACX,IAAI,CAAC,CAAC,EAAE,CAACY,eAAe,CAACX,IAAI,EAAEW,eAAe,GAAGD,aAAa,CAACX,IAAI,CAAC,CAAC,EAAE;YACnJ,IAAIa,SAAS,GAAGD,eAAe,CAACT,KAAK;YACrC,IAAI;cACAW,aAAa,CAACD,SAAS,CAAC;YAC5B,CAAC,CACD,OAAOE,GAAG,EAAE;cACRpB,MAAM,GAAGA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,EAAE;cAC3D,IAAIoB,GAAG,YAAYjC,mBAAmB,EAAE;gBACpCa,MAAM,GAAGhB,aAAa,CAACA,aAAa,CAAC,EAAE,EAAED,MAAM,CAACiB,MAAM,CAAC,CAAC,EAAEjB,MAAM,CAACqC,GAAG,CAACpB,MAAM,CAAC,CAAC;cACjF,CAAC,MACI;gBACDA,MAAM,CAACqB,IAAI,CAACD,GAAG,CAAC;cACpB;YACJ;UACJ;QACJ,CAAC,CACD,OAAOE,KAAK,EAAE;UAAExB,GAAG,GAAG;YAAEa,KAAK,EAAEW;UAAM,CAAC;QAAE,CAAC,SACjC;UACJ,IAAI;YACA,IAAIL,eAAe,IAAI,CAACA,eAAe,CAACX,IAAI,KAAKP,EAAE,GAAGiB,aAAa,CAACJ,MAAM,CAAC,EAAEb,EAAE,CAACc,IAAI,CAACG,aAAa,CAAC;UACvG,CAAC,SACO;YAAE,IAAIlB,GAAG,EAAE,MAAMA,GAAG,CAACa,KAAK;UAAE;QACxC;MACJ;MACA,IAAIX,MAAM,EAAE;QACR,MAAM,IAAIb,mBAAmB,CAACa,MAAM,CAAC;MACzC;IACJ;EACJ,CAAC;EACDX,YAAY,CAACK,SAAS,CAAC6B,GAAG,GAAG,UAAUC,QAAQ,EAAE;IAC7C,IAAI3B,EAAE;IACN,IAAI2B,QAAQ,IAAIA,QAAQ,KAAK,IAAI,EAAE;MAC/B,IAAI,IAAI,CAACjC,MAAM,EAAE;QACb4B,aAAa,CAACK,QAAQ,CAAC;MAC3B,CAAC,MACI;QACD,IAAIA,QAAQ,YAAYnC,YAAY,EAAE;UAClC,IAAImC,QAAQ,CAACjC,MAAM,IAAIiC,QAAQ,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;YAC9C;UACJ;UACAD,QAAQ,CAACE,UAAU,CAAC,IAAI,CAAC;QAC7B;QACA,CAAC,IAAI,CAACjC,WAAW,GAAG,CAACI,EAAE,GAAG,IAAI,CAACJ,WAAW,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAEwB,IAAI,CAACG,QAAQ,CAAC;MACnG;IACJ;EACJ,CAAC;EACDnC,YAAY,CAACK,SAAS,CAAC+B,UAAU,GAAG,UAAUE,MAAM,EAAE;IAClD,IAAInC,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,OAAOA,UAAU,KAAKmC,MAAM,IAAK1B,KAAK,CAACC,OAAO,CAACV,UAAU,CAAC,IAAIA,UAAU,CAACoC,QAAQ,CAACD,MAAM,CAAE;EAC9F,CAAC;EACDtC,YAAY,CAACK,SAAS,CAACgC,UAAU,GAAG,UAAUC,MAAM,EAAE;IAClD,IAAInC,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAI,CAACA,UAAU,GAAGS,KAAK,CAACC,OAAO,CAACV,UAAU,CAAC,IAAIA,UAAU,CAAC6B,IAAI,CAACM,MAAM,CAAC,EAAEnC,UAAU,IAAIA,UAAU,GAAG,CAACA,UAAU,EAAEmC,MAAM,CAAC,GAAGA,MAAM;EACpI,CAAC;EACDtC,YAAY,CAACK,SAAS,CAACmC,aAAa,GAAG,UAAUF,MAAM,EAAE;IACrD,IAAInC,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIA,UAAU,KAAKmC,MAAM,EAAE;MACvB,IAAI,CAACnC,UAAU,GAAG,IAAI;IAC1B,CAAC,MACI,IAAIS,KAAK,CAACC,OAAO,CAACV,UAAU,CAAC,EAAE;MAChCJ,SAAS,CAACI,UAAU,EAAEmC,MAAM,CAAC;IACjC;EACJ,CAAC;EACDtC,YAAY,CAACK,SAAS,CAACe,MAAM,GAAG,UAAUe,QAAQ,EAAE;IAChD,IAAI/B,WAAW,GAAG,IAAI,CAACA,WAAW;IAClCA,WAAW,IAAIL,SAAS,CAACK,WAAW,EAAE+B,QAAQ,CAAC;IAC/C,IAAIA,QAAQ,YAAYnC,YAAY,EAAE;MAClCmC,QAAQ,CAACK,aAAa,CAAC,IAAI,CAAC;IAChC;EACJ,CAAC;EACDxC,YAAY,CAACyC,KAAK,GAAI,YAAY;IAC9B,IAAIC,KAAK,GAAG,IAAI1C,YAAY,CAAC,CAAC;IAC9B0C,KAAK,CAACxC,MAAM,GAAG,IAAI;IACnB,OAAOwC,KAAK;EAChB,CAAC,CAAE,CAAC;EACJ,OAAO1C,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY;AACrB,OAAO,IAAI2C,kBAAkB,GAAG3C,YAAY,CAACyC,KAAK;AAClD,OAAO,SAASG,cAAcA,CAACzB,KAAK,EAAE;EAClC,OAAQA,KAAK,YAAYnB,YAAY,IAChCmB,KAAK,IAAI,QAAQ,IAAIA,KAAK,IAAItB,UAAU,CAACsB,KAAK,CAACC,MAAM,CAAC,IAAIvB,UAAU,CAACsB,KAAK,CAACe,GAAG,CAAC,IAAIrC,UAAU,CAACsB,KAAK,CAACb,WAAW,CAAE;AAC1H;AACA,SAASwB,aAAaA,CAACD,SAAS,EAAE;EAC9B,IAAIhC,UAAU,CAACgC,SAAS,CAAC,EAAE;IACvBA,SAAS,CAAC,CAAC;EACf,CAAC,MACI;IACDA,SAAS,CAACvB,WAAW,CAAC,CAAC;EAC3B;AACJ;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}