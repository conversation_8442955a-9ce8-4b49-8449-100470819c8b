{"ast": null, "code": "var _UserMenuComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./user-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./user-menu.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { take } from 'rxjs/operators';\nimport { PERMISSIONS_NAMES } from '../../services/sw-hub-auth/permissions';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SwuiSettingsDialogComponent } from '../settings-dialog/swui-settings-dialog.component';\nexport const MASTER_ID = '1';\nlet UserMenuComponent = (_UserMenuComponent = class UserMenuComponent {\n  constructor(dialog, configService, auth) {\n    this.dialog = dialog;\n    this.configService = configService;\n    this.auth = auth;\n    this.languageChanges = new EventEmitter();\n    this.settingsChanges = new EventEmitter();\n    this.logout = new EventEmitter();\n  }\n  showSettings() {\n    this.dialog.open(SwuiSettingsDialogComponent, {\n      width: '700px'\n    }).afterClosed().pipe(take(1)).subscribe(settings => {\n      if (settings) {\n        this.settingsChanges.emit(settings);\n      }\n    });\n  }\n  twoFactorSettings() {\n    var _this$configService$l;\n    location.href = `${(_this$configService$l = this.configService.loginUrl) === null || _this$configService$l === void 0 ? void 0 : _this$configService$l.replace('/login', '')}/twofactorsettings`;\n  }\n  changePasswordAllowed() {\n    return this.entityKey !== MASTER_ID && this.auth.allowedTo([PERMISSIONS_NAMES.KEYENTITY_USER_CHANGE_PASSWORD]);\n  }\n  changePassword() {\n    var _this$configService$l2;\n    location.href = `${(_this$configService$l2 = this.configService.loginUrl) === null || _this$configService$l2 === void 0 ? void 0 : _this$configService$l2.replace('/login', '')}/changepassword`;\n  }\n}, _UserMenuComponent.ctorParameters = () => [{\n  type: MatDialog\n}, {\n  type: SwHubConfigService\n}, {\n  type: SwHubAuthService\n}], _UserMenuComponent.propDecorators = {\n  username: [{\n    type: Input\n  }],\n  entityKey: [{\n    type: Input\n  }],\n  hasTwoFactor: [{\n    type: Input\n  }],\n  languageChanges: [{\n    type: Output\n  }],\n  settingsChanges: [{\n    type: Output\n  }],\n  logout: [{\n    type: Output\n  }]\n}, _UserMenuComponent);\nUserMenuComponent = __decorate([Component({\n  selector: 'lib-swui-user-menu',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], UserMenuComponent);\nexport { UserMenuComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectionStrategy", "Component", "EventEmitter", "Input", "Output", "MatDialog", "take", "PERMISSIONS_NAMES", "SwHubAuthService", "SwHubConfigService", "SwuiSettingsDialogComponent", "MASTER_ID", "UserMenuComponent", "_UserMenuComponent", "constructor", "dialog", "configService", "auth", "languageChanges", "settingsChanges", "logout", "showSettings", "open", "width", "afterClosed", "pipe", "subscribe", "settings", "emit", "twoFactorSettings", "_this$configService$l", "location", "href", "loginUrl", "replace", "changePasswordAllowed", "entityKey", "allowedTo", "KEYENTITY_USER_CHANGE_PASSWORD", "changePassword", "_this$configService$l2", "ctorParameters", "type", "propDecorators", "username", "hasTwoFactor", "selector", "template", "changeDetection", "OnPush", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/user-menu/user-menu.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./user-menu.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./user-menu.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { take } from 'rxjs/operators';\nimport { PERMISSIONS_NAMES } from '../../services/sw-hub-auth/permissions';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SwuiSettingsDialogComponent } from '../settings-dialog/swui-settings-dialog.component';\nexport const MASTER_ID = '1';\nlet UserMenuComponent = class UserMenuComponent {\n    constructor(dialog, configService, auth) {\n        this.dialog = dialog;\n        this.configService = configService;\n        this.auth = auth;\n        this.languageChanges = new EventEmitter();\n        this.settingsChanges = new EventEmitter();\n        this.logout = new EventEmitter();\n    }\n    showSettings() {\n        this.dialog.open(SwuiSettingsDialogComponent, {\n            width: '700px'\n        }).afterClosed().pipe(take(1)).subscribe(settings => {\n            if (settings) {\n                this.settingsChanges.emit(settings);\n            }\n        });\n    }\n    twoFactorSettings() {\n        location.href = `${this.configService.loginUrl?.replace('/login', '')}/twofactorsettings`;\n    }\n    changePasswordAllowed() {\n        return this.entityKey !== MASTER_ID && this.auth.allowedTo([PERMISSIONS_NAMES.KEYENTITY_USER_CHANGE_PASSWORD]);\n    }\n    changePassword() {\n        location.href = `${this.configService.loginUrl?.replace('/login', '')}/changepassword`;\n    }\n    static { this.ctorParameters = () => [\n        { type: MatDialog },\n        { type: SwHubConfigService },\n        { type: SwHubAuthService }\n    ]; }\n    static { this.propDecorators = {\n        username: [{ type: Input }],\n        entityKey: [{ type: Input }],\n        hasTwoFactor: [{ type: Input }],\n        languageChanges: [{ type: Output }],\n        settingsChanges: [{ type: Output }],\n        logout: [{ type: Output }]\n    }; }\n};\nUserMenuComponent = __decorate([\n    Component({\n        selector: 'lib-swui-user-menu',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], UserMenuComponent);\nexport { UserMenuComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,SAASC,uBAAuB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAC/F,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,2BAA2B,QAAQ,mDAAmD;AAC/F,OAAO,MAAMC,SAAS,GAAG,GAAG;AAC5B,IAAIC,iBAAiB,IAAAC,kBAAA,GAAG,MAAMD,iBAAiB,CAAC;EAC5CE,WAAWA,CAACC,MAAM,EAAEC,aAAa,EAAEC,IAAI,EAAE;IACrC,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,eAAe,GAAG,IAAIhB,YAAY,CAAC,CAAC;IACzC,IAAI,CAACiB,eAAe,GAAG,IAAIjB,YAAY,CAAC,CAAC;IACzC,IAAI,CAACkB,MAAM,GAAG,IAAIlB,YAAY,CAAC,CAAC;EACpC;EACAmB,YAAYA,CAAA,EAAG;IACX,IAAI,CAACN,MAAM,CAACO,IAAI,CAACZ,2BAA2B,EAAE;MAC1Ca,KAAK,EAAE;IACX,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,IAAI,CAACnB,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoB,SAAS,CAACC,QAAQ,IAAI;MACjD,IAAIA,QAAQ,EAAE;QACV,IAAI,CAACR,eAAe,CAACS,IAAI,CAACD,QAAQ,CAAC;MACvC;IACJ,CAAC,CAAC;EACN;EACAE,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IAChBC,QAAQ,CAACC,IAAI,GAAG,IAAAF,qBAAA,GAAG,IAAI,CAACd,aAAa,CAACiB,QAAQ,cAAAH,qBAAA,uBAA3BA,qBAAA,CAA6BI,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,oBAAoB;EAC7F;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,SAAS,KAAKzB,SAAS,IAAI,IAAI,CAACM,IAAI,CAACoB,SAAS,CAAC,CAAC9B,iBAAiB,CAAC+B,8BAA8B,CAAC,CAAC;EAClH;EACAC,cAAcA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACbT,QAAQ,CAACC,IAAI,GAAG,IAAAQ,sBAAA,GAAG,IAAI,CAACxB,aAAa,CAACiB,QAAQ,cAAAO,sBAAA,uBAA3BA,sBAAA,CAA6BN,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,iBAAiB;EAC1F;AAcJ,CAAC,EAbYrB,kBAAA,CAAK4B,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAErC;AAAU,CAAC,EACnB;EAAEqC,IAAI,EAAEjC;AAAmB,CAAC,EAC5B;EAAEiC,IAAI,EAAElC;AAAiB,CAAC,CAC7B,EACQK,kBAAA,CAAK8B,cAAc,GAAG;EAC3BC,QAAQ,EAAE,CAAC;IAAEF,IAAI,EAAEvC;EAAM,CAAC,CAAC;EAC3BiC,SAAS,EAAE,CAAC;IAAEM,IAAI,EAAEvC;EAAM,CAAC,CAAC;EAC5B0C,YAAY,EAAE,CAAC;IAAEH,IAAI,EAAEvC;EAAM,CAAC,CAAC;EAC/Be,eAAe,EAAE,CAAC;IAAEwB,IAAI,EAAEtC;EAAO,CAAC,CAAC;EACnCe,eAAe,EAAE,CAAC;IAAEuB,IAAI,EAAEtC;EAAO,CAAC,CAAC;EACnCgB,MAAM,EAAE,CAAC;IAAEsB,IAAI,EAAEtC;EAAO,CAAC;AAC7B,CAAC,EAAAS,kBAAA,CACJ;AACDD,iBAAiB,GAAGf,UAAU,CAAC,CAC3BI,SAAS,CAAC;EACN6C,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAEjD,oBAAoB;EAC9BkD,eAAe,EAAEhD,uBAAuB,CAACiD,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACpD,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEa,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}