{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiMatCalendarComponent } from './swui-mat-calendar.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nexport const MAT_CALENDAR_MODULES = [MatSelectModule, MatFormFieldModule, ReactiveFormsModule];\nlet SwuiMatCalendarModule = class SwuiMatCalendarModule {};\nSwuiMatCalendarModule = __decorate([NgModule({\n  declarations: [SwuiMatCalendarComponent],\n  imports: [CommonModule, ...MAT_CALENDAR_MODULES],\n  exports: [SwuiMatCalendarComponent]\n})], SwuiMatCalendarModule);\nexport { SwuiMatCalendarModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "SwuiMatCalendarComponent", "ReactiveFormsModule", "MatSelectModule", "MatFormFieldModule", "MAT_CALENDAR_MODULES", "SwuiMatCalendarModule", "__decorate", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-mat-calendar/swui-mat-calendar.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { SwuiMatCalendarComponent } from './swui-mat-calendar.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\n\n\nexport const MAT_CALENDAR_MODULES = [\n  MatSelectModule,\n  MatFormFieldModule,\n  ReactiveFormsModule,\n];\n\n@NgModule({\n  declarations: [SwuiMatCalendarComponent],\n  imports: [\n    CommonModule,\n    ...MAT_CALENDAR_MODULES,\n  ],\n  exports: [\n    SwuiMatCalendarComponent,\n  ]\n})\nexport class SwuiMatCalendarModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AAGjE,OAAO,MAAMC,oBAAoB,GAAG,CAClCF,eAAe,EACfC,kBAAkB,EAClBF,mBAAmB,CACpB;AAYM,IAAMI,qBAAqB,GAA3B,MAAMA,qBAAqB,GACjC;AADYA,qBAAqB,GAAAC,UAAA,EAVjCR,QAAQ,CAAC;EACRS,YAAY,EAAE,CAACP,wBAAwB,CAAC;EACxCQ,OAAO,EAAE,CACPT,YAAY,EACZ,GAAGK,oBAAoB,CACxB;EACDK,OAAO,EAAE,CACPT,wBAAwB;CAE3B,CAAC,C,EACWK,qBAAqB,CACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}