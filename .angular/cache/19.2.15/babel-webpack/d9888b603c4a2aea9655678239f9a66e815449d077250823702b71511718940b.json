{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatPseudoCheckboxModule, MatRippleModule } from '@angular/material/core';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiProgressContainerModule } from '../swui-progress-container/swui-progress-container.module';\nimport { SwuiSelectTableComponent } from './swui-select-table.component';\nexport const SELECT_TABLE_MODULES = [FormsModule, ReactiveFormsModule, MatIconModule, MatCheckboxModule, MatRippleModule, ScrollingModule, MatFormFieldModule, SwuiProgressContainerModule, MatInputModule, MatButtonModule];\nlet SwuiSelectTableModule = class SwuiSelectTableModule {};\nSwuiSelectTableModule = __decorate([NgModule({\n  declarations: [SwuiSelectTableComponent],\n  imports: [CommonModule, TranslateModule.forChild(), ...SELECT_TABLE_MODULES, MatPseudoCheckboxModule],\n  exports: [SwuiSelectTableComponent]\n})], SwuiSelectTableModule);\nexport { SwuiSelectTableModule };", "map": {"version": 3, "names": ["__decorate", "ScrollingModule", "CommonModule", "NgModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCheckboxModule", "MatPseudoCheckboxModule", "MatRippleModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "TranslateModule", "SwuiProgressContainerModule", "SwuiSelectTableComponent", "SELECT_TABLE_MODULES", "SwuiSelectTableModule", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-select-table/swui-select-table.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatPseudoCheckboxModule, MatRippleModule } from '@angular/material/core';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiProgressContainerModule } from '../swui-progress-container/swui-progress-container.module';\nimport { SwuiSelectTableComponent } from './swui-select-table.component';\nexport const SELECT_TABLE_MODULES = [\n    FormsModule,\n    ReactiveFormsModule,\n    MatIconModule,\n    MatCheckboxModule,\n    MatRippleModule,\n    ScrollingModule,\n    MatFormFieldModule,\n    SwuiProgressContainerModule,\n    MatInputModule,\n    MatButtonModule\n];\nlet SwuiSelectTableModule = class SwuiSelectTableModule {\n};\nSwuiSelectTableModule = __decorate([\n    NgModule({\n        declarations: [SwuiSelectTableComponent],\n        imports: [\n            CommonModule,\n            TranslateModule.forChild(),\n            ...SELECT_TABLE_MODULES,\n            MatPseudoCheckboxModule\n        ],\n        exports: [SwuiSelectTableComponent],\n    })\n], SwuiSelectTableModule);\nexport { SwuiSelectTableModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,uBAAuB,EAAEC,eAAe,QAAQ,wBAAwB;AACjF,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,2BAA2B,QAAQ,2DAA2D;AACvG,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,OAAO,MAAMC,oBAAoB,GAAG,CAChCZ,WAAW,EACXC,mBAAmB,EACnBM,aAAa,EACbJ,iBAAiB,EACjBE,eAAe,EACfR,eAAe,EACfS,kBAAkB,EAClBI,2BAA2B,EAC3BF,cAAc,EACdN,eAAe,CAClB;AACD,IAAIW,qBAAqB,GAAG,MAAMA,qBAAqB,CAAC,EACvD;AACDA,qBAAqB,GAAGjB,UAAU,CAAC,CAC/BG,QAAQ,CAAC;EACLe,YAAY,EAAE,CAACH,wBAAwB,CAAC;EACxCI,OAAO,EAAE,CACLjB,YAAY,EACZW,eAAe,CAACO,QAAQ,CAAC,CAAC,EAC1B,GAAGJ,oBAAoB,EACvBR,uBAAuB,CAC1B;EACDa,OAAO,EAAE,CAACN,wBAAwB;AACtC,CAAC,CAAC,CACL,EAAEE,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}