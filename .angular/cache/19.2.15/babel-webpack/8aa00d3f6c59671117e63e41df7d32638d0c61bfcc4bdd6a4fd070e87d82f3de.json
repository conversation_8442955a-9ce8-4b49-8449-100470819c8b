{"ast": null, "code": "var _HubSelectorComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./hub-selector.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./hub-selector.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, Inject } from '@angular/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nlet HubSelectorComponent = (_HubSelectorComponent = class HubSelectorComponent {\n  constructor(settings, config, auth) {\n    this.config = config;\n    this.auth = auth;\n    this.hubs = Object.entries(this.config.hubs || {}).filter(([, {\n      permission\n    }]) => {\n      const permissions = Array.isArray(permission) ? permission : permission ? [permission] : [];\n      return permission ? this.auth.allowedTo(permissions) : true;\n    }).reduce((result, [id, {\n      cssClass,\n      name,\n      url\n    }]) => {\n      if (name && url) {\n        return [...result, {\n          id,\n          name,\n          url,\n          cssClass\n        }];\n      }\n      return result;\n    }, []);\n    this.activeHub = this.hubs.find(({\n      id\n    }) => id === settings.name);\n  }\n}, _HubSelectorComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_HUB_MESSAGE_CONFIG]\n  }]\n}, {\n  type: SwHubConfigService\n}, {\n  type: SwHubAuthService\n}], _HubSelectorComponent);\nHubSelectorComponent = __decorate([Component({\n  selector: 'lib-swui-hub-selector',\n  template: __NG_CLI_RESOURCE__0,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], HubSelectorComponent);\nexport { HubSelectorComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "ChangeDetectionStrategy", "Component", "Inject", "SWUI_HUB_MESSAGE_CONFIG", "SwHubConfigService", "SwHubAuthService", "HubSelectorComponent", "_HubSelectorComponent", "constructor", "settings", "config", "auth", "hubs", "Object", "entries", "filter", "permission", "permissions", "Array", "isArray", "allowedTo", "reduce", "result", "id", "cssClass", "name", "url", "activeHub", "find", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "changeDetection", "OnPush", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-top-menu/hub-selector/hub-selector.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./hub-selector.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./hub-selector.component.scss?ngResource\";\nimport { ChangeDetectionStrategy, Component, Inject } from '@angular/core';\nimport { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';\nimport { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';\nimport { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';\nlet HubSelectorComponent = class HubSelectorComponent {\n    constructor(settings, config, auth) {\n        this.config = config;\n        this.auth = auth;\n        this.hubs = Object.entries(this.config.hubs || {})\n            .filter(([, { permission }]) => {\n            const permissions = Array.isArray(permission) ? permission : permission ? [permission] : [];\n            return permission ? this.auth.allowedTo(permissions) : true;\n        })\n            .reduce((result, [id, { cssClass, name, url }]) => {\n            if (name && url) {\n                return [\n                    ...result,\n                    { id, name, url, cssClass }\n                ];\n            }\n            return result;\n        }, []);\n        this.activeHub = this.hubs.find(({ id }) => id === settings.name);\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_HUB_MESSAGE_CONFIG,] }] },\n        { type: SwHubConfigService },\n        { type: SwHubAuthService }\n    ]; }\n};\nHubSelectorComponent = __decorate([\n    Component({\n        selector: 'lib-swui-hub-selector',\n        template: __NG_CLI_RESOURCE__0,\n        changeDetection: ChangeDetectionStrategy.OnPush,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], HubSelectorComponent);\nexport { HubSelectorComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,SAASC,uBAAuB,EAAEC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AAC1E,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,IAAIC,oBAAoB,IAAAC,qBAAA,GAAG,MAAMD,oBAAoB,CAAC;EAClDE,WAAWA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAE;IAChC,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACJ,MAAM,CAACE,IAAI,IAAI,CAAC,CAAC,CAAC,CAC7CG,MAAM,CAAC,CAAC,GAAG;MAAEC;IAAW,CAAC,CAAC,KAAK;MAChC,MAAMC,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,GAAGA,UAAU,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC,GAAG,EAAE;MAC3F,OAAOA,UAAU,GAAG,IAAI,CAACL,IAAI,CAACS,SAAS,CAACH,WAAW,CAAC,GAAG,IAAI;IAC/D,CAAC,CAAC,CACGI,MAAM,CAAC,CAACC,MAAM,EAAE,CAACC,EAAE,EAAE;MAAEC,QAAQ;MAAEC,IAAI;MAAEC;IAAI,CAAC,CAAC,KAAK;MACnD,IAAID,IAAI,IAAIC,GAAG,EAAE;QACb,OAAO,CACH,GAAGJ,MAAM,EACT;UAAEC,EAAE;UAAEE,IAAI;UAAEC,GAAG;UAAEF;QAAS,CAAC,CAC9B;MACL;MACA,OAAOF,MAAM;IACjB,CAAC,EAAE,EAAE,CAAC;IACN,IAAI,CAACK,SAAS,GAAG,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAC,CAAC;MAAEL;IAAG,CAAC,KAAKA,EAAE,KAAKd,QAAQ,CAACgB,IAAI,CAAC;EACrE;AAMJ,CAAC,EALYlB,qBAAA,CAAKsB,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAE5B,MAAM;IAAE+B,IAAI,EAAE,CAAC9B,uBAAuB;EAAG,CAAC;AAAE,CAAC,EACrF;EAAE2B,IAAI,EAAE1B;AAAmB,CAAC,EAC5B;EAAE0B,IAAI,EAAEzB;AAAiB,CAAC,CAC7B,EAAAE,qBAAA,CACJ;AACDD,oBAAoB,GAAGT,UAAU,CAAC,CAC9BI,SAAS,CAAC;EACNiC,QAAQ,EAAE,uBAAuB;EACjCC,QAAQ,EAAErC,oBAAoB;EAC9BsC,eAAe,EAAEpC,uBAAuB,CAACqC,MAAM;EAC/CC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACxC,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEO,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}