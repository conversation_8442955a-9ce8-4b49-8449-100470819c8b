{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { DataSource } from '@angular/cdk/collections';\nimport { HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';\nimport * as moment from 'moment';\nimport { BehaviorSubject, combineLatest, of, Subject, throwError } from 'rxjs';\nimport { catchError, debounceTime, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { LocalDataService } from './services/local-data.service';\nconst FIELD_POSTFIX_DELIM = '__';\nexport class SwuiGridDataSource extends DataSource {\n  set data(value) {\n    if (typeof value === 'undefined') {\n      return;\n    }\n    this._data = value;\n    this.dataService = new LocalDataService();\n    this.dataService.localData = this._data;\n  }\n  get data() {\n    return this._data;\n  }\n  constructor(dataService, hubEntityService, filterService, urlHandler) {\n    super();\n    this.dataService = dataService;\n    this.hubEntityService = hubEntityService;\n    this.filterService = filterService;\n    this.urlHandler = urlHandler;\n    this.total$ = new BehaviorSubject(0);\n    /**\n     * required for not replacing URL queryParams for first request - when params are already in url but they also\n     * adding via regular flow\n     */\n    this.firstRequest = true;\n    this.useHubEntity = false;\n    this.entity = null;\n    this.data$ = new BehaviorSubject([]);\n    this.loadData$ = new Subject();\n    this.destroy$ = new Subject();\n  }\n  get canUseEntitySelect() {\n    return this.useHubEntity && !!this.hubEntityService;\n  }\n  get entity$() {\n    return this.canUseEntitySelect ? this.hubEntityService.entitySelected$ : of(null);\n  }\n  get filterEntity$() {\n    const filter$ = this.filterService ? this.filterService.appliedFilter : of(null);\n    return combineLatest([filter$, this.entity$]);\n  }\n  initDatasource(useHubEntity) {\n    this.useHubEntity = useHubEntity;\n    this.loadData$.pipe(debounceTime(250), switchMap(() => {\n      if (typeof this.dataService === 'undefined' || this.dataService === null) {\n        return of(new HttpResponse({\n          body: undefined,\n          headers: new HttpHeaders()\n        }));\n      }\n      let params = this.getRequestParams();\n      let requestData = this.requestData || {};\n      if (this.entity && this.entity.path) {\n        const path = this.entity.path.charAt(0) === ':' ? this.entity.path.substring(1) : this.entity.path;\n        const type = this.entity.type;\n        params = _objectSpread(_objectSpread({}, params), {}, {\n          path: this.entity.path\n        });\n        if (path) {\n          requestData = _objectSpread(_objectSpread({}, requestData), {}, {\n            path,\n            type\n          });\n        }\n      }\n      this.urlHandler.setParams(params, this.firstRequest ? 'merge' : '');\n      let httpParams = new HttpParams({\n        fromObject: params\n      });\n      const request = this.dataService ? this.dataService.getGridData(httpParams, requestData) : throwError('');\n      return request.pipe(take(1), catchError(() => of(new HttpResponse({\n        body: undefined,\n        headers: new HttpHeaders()\n      }))), tap(() => this.firstRequest = false));\n    }), takeUntil(this.destroy$)).subscribe(({\n      body,\n      headers\n    }) => {\n      var _this$data;\n      this._data = body || undefined;\n      this.data$.next(body || []);\n      this.total$.next(Number(headers.get('x-paging-total') || '0'));\n      if (!((_this$data = this.data) !== null && _this$data !== void 0 && _this$data.length) && this.paginator && this.paginator.pageIndex !== 0) {\n        this.paginator.pageIndex = this.paginator.pageIndex - 1;\n        this.loadData();\n      }\n    });\n    this.subscribeToFilter();\n  }\n  connect() {\n    return this.data$.asObservable();\n  }\n  disconnect() {\n    this.data$.complete();\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n  addSort(sort) {\n    this.sort = sort;\n    this.sort.sortChange.subscribe(() => this.loadData());\n  }\n  addPaginator(paginator, blind = false) {\n    this.paginator = paginator;\n    this.paginator.page.subscribe(() => this.loadData());\n    this.total$.asObservable().subscribe(total => {\n      if (!this.paginator) {\n        return;\n      }\n      if (!blind) {\n        this.paginator.length = total;\n        return;\n      }\n      const previousLength = this.paginator.pageSize * this.paginator.pageIndex;\n      const additionalItems = (this._data || []).length === this.paginator.pageSize ? 1 : 0;\n      this.paginator.length = (this._data || []).length + additionalItems + previousLength;\n    });\n  }\n  changePageSize(pageSize) {\n    if (this.paginator) {\n      const oldPageSize = this.paginator.pageSize;\n      const oldPageIndex = this.paginator.pageIndex;\n      this.paginator.pageSize = pageSize;\n      this.paginator.pageIndex = Math.floor(oldPageIndex * oldPageSize / pageSize);\n      if (this._data && this._data.length) {\n        this.loadData();\n      }\n    }\n  }\n  loadData() {\n    this.loadData$.next(undefined);\n  }\n  subscribeToFilter() {\n    if (this.filterService !== null) {\n      this.filterService.displayedFilter.pipe(takeUntil(this.destroy$)).subscribe(filterData => {\n        this.filterData = filterData;\n      });\n      this.filterEntity$.pipe(takeUntil(this.destroy$)).subscribe(([, entity]) => {\n        this.entity = entity;\n        if (!this.firstRequest) {\n          if (this.paginator) {\n            this.paginator.pageIndex = 0;\n          }\n        }\n        this.loadData();\n      });\n      return;\n    }\n    if (this.canUseEntitySelect) {\n      this.hubEntityService.entitySelected$.pipe(takeUntil(this.destroy$)).subscribe(entity => {\n        this.entity = entity;\n        this.loadData();\n      });\n    }\n  }\n  getRequestParams() {\n    let params = {};\n    // applying filter data\n    if (this.filterData) {\n      params = this.processFilterParams(this.filterData, params);\n    }\n    // applying paginator values\n    if (this.paginator) {\n      const {\n        pageIndex,\n        pageSize\n      } = this.paginator;\n      params['offset'] = (pageIndex * pageSize).toString();\n      params['limit'] = pageSize.toString();\n    } else if (this.data) {\n      params['offset'] = 0;\n    }\n    // applying sorting\n    if (this.sort) {\n      const {\n        active,\n        direction\n      } = this.sort;\n      if (direction !== '') {\n        params['sortOrder'] = direction.toUpperCase();\n        params['sortBy'] = active;\n      }\n    }\n    return params;\n  }\n  processFilterParams(filterData, initialParams) {\n    return Object.keys(filterData).filter(key => {\n      const value = filterData[key];\n      return typeof value !== 'undefined' && value !== null && value !== '' && key !== 'undefined';\n    }).reduce((params, key) => {\n      let value = filterData[key];\n      if (typeof value === 'object' && Object.keys(value).every(k => k.includes(FIELD_POSTFIX_DELIM))) {\n        params = this.processFilterParams(value, params);\n      } else {\n        if (moment.isMoment(value)) {\n          value = value.toISOString();\n        }\n        params[key] = value;\n      }\n      return params;\n    }, initialParams);\n  }\n}", "map": {"version": 3, "names": ["DataSource", "HttpHeaders", "HttpParams", "HttpResponse", "moment", "BehaviorSubject", "combineLatest", "of", "Subject", "throwError", "catchError", "debounceTime", "switchMap", "take", "takeUntil", "tap", "LocalDataService", "FIELD_POSTFIX_DELIM", "SwuiGridDataSource", "data", "value", "_data", "dataService", "localData", "constructor", "hubEntityService", "filterService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "total$", "firstRequest", "useHubEntity", "entity", "data$", "loadData$", "destroy$", "canUseEntitySelect", "entity$", "entitySelected$", "filterEntity$", "filter$", "appliedFilter", "initDatasource", "pipe", "body", "undefined", "headers", "params", "getRequestParams", "requestData", "path", "char<PERSON>t", "substring", "type", "_objectSpread", "setParams", "httpParams", "fromObject", "request", "getGridData", "subscribe", "_this$data", "next", "Number", "get", "length", "paginator", "pageIndex", "loadData", "subscribeToFilter", "connect", "asObservable", "disconnect", "complete", "addSort", "sort", "sortChange", "addPaginator", "blind", "page", "total", "<PERSON><PERSON><PERSON><PERSON>", "pageSize", "additionalItems", "changePageSize", "oldPageSize", "oldPageIndex", "Math", "floor", "displayedFilter", "filterData", "processFilterParams", "toString", "active", "direction", "toUpperCase", "initialParams", "Object", "keys", "filter", "key", "reduce", "every", "k", "includes", "isMoment", "toISOString"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/swui-grid.datasource.ts"], "sourcesContent": ["import { DataSource } from '@angular/cdk/collections';\nimport { HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { Params } from '@angular/router';\nimport * as moment from 'moment';\nimport { BehaviorSubject, combineLatest, Observable, of, Subject, throwError } from 'rxjs';\nimport { catchError, debounceTime, switchMap, take, takeUntil, tap } from 'rxjs/operators';\nimport { SwHubShortEntity } from '../services/sw-hub-entity/sw-hub-entity.model';\nimport { SwHubEntityService } from '../services/sw-hub-entity/sw-hub-entity.service';\nimport { SwuiTopFilterDataService } from '../swui-schema-top-filter/top-filter-data.service';\nimport { GridRequestData, SwuiGridDataService } from './services/grid-data.service';\nimport { LocalDataService } from './services/local-data.service';\nimport { SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';\n\nconst FIELD_POSTFIX_DELIM = '__';\n\nexport class SwuiGridDataSource<T extends object> extends DataSource<T> {\n\n  set data( value: T[] | undefined ) {\n    if (typeof value === 'undefined') {\n      return;\n    }\n    this._data = value;\n    this.dataService = new LocalDataService<T>();\n    (this.dataService as LocalDataService<T>).localData = this._data;\n  }\n\n  get data(): T[] | undefined {\n    return this._data;\n  }\n\n  sort?: MatSort;\n  paginator?: MatPaginator;\n  readonly total$ = new BehaviorSubject<number>(0);\n\n  requestData?: GridRequestData;\n\n  /**\n   * required for not replacing URL queryParams for first request - when params are already in url but they also\n   * adding via regular flow\n   */\n  private firstRequest = true;\n  private useHubEntity = false;\n\n  private _data: T[] | undefined;\n  private filterData: any;\n  private entity: SwHubShortEntity | null = null;\n\n  private readonly data$ = new BehaviorSubject<T[]>([]);\n  private readonly loadData$ = new Subject<void>();\n  private readonly destroy$ = new Subject<void>();\n\n  constructor(\n    private dataService: SwuiGridDataService<T> | null,\n    private readonly hubEntityService: SwHubEntityService,\n    private readonly filterService: SwuiTopFilterDataService | null,\n    private readonly urlHandler: SwuiGridUrlHandlerService\n  ) {\n    super();\n  }\n\n  get canUseEntitySelect(): boolean {\n    return this.useHubEntity && !!this.hubEntityService;\n  }\n\n  get entity$(): Observable<any> {\n    return this.canUseEntitySelect ? this.hubEntityService.entitySelected$ : of(null);\n  }\n\n  get filterEntity$(): Observable<any> {\n    const filter$ = this.filterService ? this.filterService.appliedFilter : of(null);\n\n    return combineLatest([filter$, this.entity$]);\n  }\n\n  initDatasource( useHubEntity: boolean ) {\n    this.useHubEntity = useHubEntity;\n\n    this.loadData$.pipe(\n      debounceTime(250),\n      switchMap(() => {\n        if (typeof this.dataService === 'undefined' || this.dataService === null) {\n          return of(new HttpResponse({\n            body: undefined,\n            headers: new HttpHeaders()\n          }));\n        }\n\n        let params = this.getRequestParams();\n        let requestData = this.requestData || {};\n\n        if (this.entity && this.entity.path) {\n          const path = this.entity.path.charAt(0) === ':' ? this.entity.path.substring(1) : this.entity.path;\n          const type = this.entity.type;\n          params = { ...params, path: this.entity.path };\n\n          if (path) {\n            requestData = { ...requestData, path, type };\n          }\n        }\n\n        this.urlHandler.setParams(params, this.firstRequest ? 'merge' : '');\n        let httpParams = new HttpParams({ fromObject: params });\n\n\n        const request = this.dataService ? this.dataService.getGridData(httpParams, requestData) : throwError('');\n\n        return request.pipe(\n          take(1),\n          catchError(() => of(new HttpResponse({\n            body: undefined,\n            headers: new HttpHeaders()\n          }))),\n          tap(() => this.firstRequest = false)\n        );\n      }),\n      takeUntil(this.destroy$)\n    ).subscribe(( { body, headers } ) => {\n      this._data = body || undefined;\n      this.data$.next(body || []);\n      this.total$.next(Number(headers.get('x-paging-total') || '0'));\n      if (!this.data?.length && this.paginator && this.paginator.pageIndex !== 0) {\n        this.paginator.pageIndex = (this.paginator.pageIndex - 1);\n        this.loadData();\n      }\n    });\n\n    this.subscribeToFilter();\n  }\n\n  connect(): Observable<T[]> {\n    return this.data$.asObservable();\n  }\n\n  disconnect() {\n    this.data$.complete();\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n\n  addSort( sort: MatSort ): void {\n    this.sort = sort;\n    this.sort.sortChange.subscribe(() => this.loadData());\n  }\n\n  addPaginator( paginator: MatPaginator, blind = false ): void {\n    this.paginator = paginator;\n    this.paginator.page.subscribe(() => this.loadData());\n    this.total$.asObservable().subscribe(( total ) => {\n      if (!this.paginator) {\n        return;\n      }\n\n      if (!blind) {\n        this.paginator.length = total;\n        return;\n      }\n\n      const previousLength = this.paginator.pageSize * this.paginator.pageIndex;\n      const additionalItems = (this._data || []).length === this.paginator.pageSize ? 1 : 0;\n\n      this.paginator.length = (this._data || []).length + additionalItems + previousLength;\n    });\n  }\n\n  changePageSize( pageSize: number ) {\n    if (this.paginator) {\n      const oldPageSize = this.paginator.pageSize;\n      const oldPageIndex = this.paginator.pageIndex;\n\n      this.paginator.pageSize = pageSize;\n      this.paginator.pageIndex = Math.floor((oldPageIndex * oldPageSize) / pageSize);\n\n      if (this._data && this._data.length) {\n        this.loadData();\n      }\n    }\n  }\n\n  loadData(): void {\n    this.loadData$.next(undefined);\n  }\n\n  private subscribeToFilter() {\n    if (this.filterService !== null) {\n      this.filterService.displayedFilter\n        .pipe(takeUntil(this.destroy$))\n        .subscribe(filterData => {\n          this.filterData = filterData;\n        });\n\n      this.filterEntity$\n        .pipe(\n          takeUntil(this.destroy$)\n        )\n        .subscribe(( [, entity] ) => {\n          this.entity = entity;\n\n          if (!this.firstRequest) {\n            if (this.paginator) {\n              this.paginator.pageIndex = 0;\n            }\n          }\n\n          this.loadData();\n        });\n\n      return;\n    }\n\n    if (this.canUseEntitySelect) {\n      this.hubEntityService.entitySelected$\n        .pipe(takeUntil(this.destroy$))\n        .subscribe(entity => {\n          this.entity = entity;\n          this.loadData();\n        });\n    }\n  }\n\n  private getRequestParams(): Params {\n    let params: Params = {};\n\n    // applying filter data\n    if (this.filterData) {\n      params = this.processFilterParams(this.filterData, params);\n    }\n\n    // applying paginator values\n    if (this.paginator) {\n      const { pageIndex, pageSize } = this.paginator;\n      params['offset'] = (pageIndex * pageSize).toString();\n      params['limit'] = pageSize.toString();\n    } else if (this.data) {\n      params['offset'] = 0;\n    }\n\n    // applying sorting\n    if (this.sort) {\n      const { active, direction } = this.sort;\n      if (direction !== '') {\n        params['sortOrder'] = direction.toUpperCase();\n        params['sortBy'] = active;\n      }\n    }\n\n    return params;\n  }\n\n  private processFilterParams( filterData: { [key: string]: any }, initialParams: Params, ): Params {\n    return Object.keys(filterData)\n      .filter(( key ) => {\n        const value = filterData[key];\n        return typeof value !== 'undefined' && value !== null && value !== '' && key !== 'undefined';\n      })\n      .reduce(( params, key ) => {\n        let value = filterData[key];\n\n        if (typeof value === 'object' && Object.keys(value).every(k => k.includes(FIELD_POSTFIX_DELIM))) {\n          params = this.processFilterParams(value, params);\n        } else {\n          if (moment.isMoment(value)) {\n            value = (value as moment.Moment).toISOString();\n          }\n          params[key] = value;\n        }\n        return params;\n      }, initialParams);\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,0BAA0B;AACrD,SAASC,WAAW,EAAEC,UAAU,EAAEC,YAAY,QAAQ,sBAAsB;AAI5E,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,eAAe,EAAEC,aAAa,EAAcC,EAAE,EAAEC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC1F,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AAK1F,SAASC,gBAAgB,QAAQ,+BAA+B;AAGhE,MAAMC,mBAAmB,GAAG,IAAI;AAEhC,OAAM,MAAOC,kBAAqC,SAAQlB,UAAa;EAErE,IAAImB,IAAIA,CAAEC,KAAsB;IAC9B,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MAChC;IACF;IACA,IAAI,CAACC,KAAK,GAAGD,KAAK;IAClB,IAAI,CAACE,WAAW,GAAG,IAAIN,gBAAgB,EAAK;IAC3C,IAAI,CAACM,WAAmC,CAACC,SAAS,GAAG,IAAI,CAACF,KAAK;EAClE;EAEA,IAAIF,IAAIA,CAAA;IACN,OAAO,IAAI,CAACE,KAAK;EACnB;EAuBAG,YACUF,WAA0C,EACjCG,gBAAoC,EACpCC,aAA8C,EAC9CC,UAAqC;IAEtD,KAAK,EAAE;IALC,KAAAL,WAAW,GAAXA,WAAW;IACF,KAAAG,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,UAAU,GAAVA,UAAU;IAvBpB,KAAAC,MAAM,GAAG,IAAIvB,eAAe,CAAS,CAAC,CAAC;IAIhD;;;;IAIQ,KAAAwB,YAAY,GAAG,IAAI;IACnB,KAAAC,YAAY,GAAG,KAAK;IAIpB,KAAAC,MAAM,GAA4B,IAAI;IAE7B,KAAAC,KAAK,GAAG,IAAI3B,eAAe,CAAM,EAAE,CAAC;IACpC,KAAA4B,SAAS,GAAG,IAAIzB,OAAO,EAAQ;IAC/B,KAAA0B,QAAQ,GAAG,IAAI1B,OAAO,EAAQ;EAS/C;EAEA,IAAI2B,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACL,YAAY,IAAI,CAAC,CAAC,IAAI,CAACL,gBAAgB;EACrD;EAEA,IAAIW,OAAOA,CAAA;IACT,OAAO,IAAI,CAACD,kBAAkB,GAAG,IAAI,CAACV,gBAAgB,CAACY,eAAe,GAAG9B,EAAE,CAAC,IAAI,CAAC;EACnF;EAEA,IAAI+B,aAAaA,CAAA;IACf,MAAMC,OAAO,GAAG,IAAI,CAACb,aAAa,GAAG,IAAI,CAACA,aAAa,CAACc,aAAa,GAAGjC,EAAE,CAAC,IAAI,CAAC;IAEhF,OAAOD,aAAa,CAAC,CAACiC,OAAO,EAAE,IAAI,CAACH,OAAO,CAAC,CAAC;EAC/C;EAEAK,cAAcA,CAAEX,YAAqB;IACnC,IAAI,CAACA,YAAY,GAAGA,YAAY;IAEhC,IAAI,CAACG,SAAS,CAACS,IAAI,CACjB/B,YAAY,CAAC,GAAG,CAAC,EACjBC,SAAS,CAAC,MAAK;MACb,IAAI,OAAO,IAAI,CAACU,WAAW,KAAK,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,IAAI,EAAE;QACxE,OAAOf,EAAE,CAAC,IAAIJ,YAAY,CAAC;UACzBwC,IAAI,EAAEC,SAAS;UACfC,OAAO,EAAE,IAAI5C,WAAW;SACzB,CAAC,CAAC;MACL;MAEA,IAAI6C,MAAM,GAAG,IAAI,CAACC,gBAAgB,EAAE;MACpC,IAAIC,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,EAAE;MAExC,IAAI,IAAI,CAACjB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACkB,IAAI,EAAE;QACnC,MAAMA,IAAI,GAAG,IAAI,CAAClB,MAAM,CAACkB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,CAACnB,MAAM,CAACkB,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACpB,MAAM,CAACkB,IAAI;QAClG,MAAMG,IAAI,GAAG,IAAI,CAACrB,MAAM,CAACqB,IAAI;QAC7BN,MAAM,GAAAO,aAAA,CAAAA,aAAA,KAAQP,MAAM;UAAEG,IAAI,EAAE,IAAI,CAAClB,MAAM,CAACkB;QAAI,EAAE;QAE9C,IAAIA,IAAI,EAAE;UACRD,WAAW,GAAAK,aAAA,CAAAA,aAAA,KAAQL,WAAW;YAAEC,IAAI;YAAEG;UAAI,EAAE;QAC9C;MACF;MAEA,IAAI,CAACzB,UAAU,CAAC2B,SAAS,CAACR,MAAM,EAAE,IAAI,CAACjB,YAAY,GAAG,OAAO,GAAG,EAAE,CAAC;MACnE,IAAI0B,UAAU,GAAG,IAAIrD,UAAU,CAAC;QAAEsD,UAAU,EAAEV;MAAM,CAAE,CAAC;MAGvD,MAAMW,OAAO,GAAG,IAAI,CAACnC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACoC,WAAW,CAACH,UAAU,EAAEP,WAAW,CAAC,GAAGvC,UAAU,CAAC,EAAE,CAAC;MAEzG,OAAOgD,OAAO,CAACf,IAAI,CACjB7B,IAAI,CAAC,CAAC,CAAC,EACPH,UAAU,CAAC,MAAMH,EAAE,CAAC,IAAIJ,YAAY,CAAC;QACnCwC,IAAI,EAAEC,SAAS;QACfC,OAAO,EAAE,IAAI5C,WAAW;OACzB,CAAC,CAAC,CAAC,EACJc,GAAG,CAAC,MAAM,IAAI,CAACc,YAAY,GAAG,KAAK,CAAC,CACrC;IACH,CAAC,CAAC,EACFf,SAAS,CAAC,IAAI,CAACoB,QAAQ,CAAC,CACzB,CAACyB,SAAS,CAAC,CAAE;MAAEhB,IAAI;MAAEE;IAAO,CAAE,KAAK;MAAA,IAAAe,UAAA;MAClC,IAAI,CAACvC,KAAK,GAAGsB,IAAI,IAAIC,SAAS;MAC9B,IAAI,CAACZ,KAAK,CAAC6B,IAAI,CAAClB,IAAI,IAAI,EAAE,CAAC;MAC3B,IAAI,CAACf,MAAM,CAACiC,IAAI,CAACC,MAAM,CAACjB,OAAO,CAACkB,GAAG,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC,CAAC;MAC9D,IAAI,GAAAH,UAAA,GAAC,IAAI,CAACzC,IAAI,cAAAyC,UAAA,eAATA,UAAA,CAAWI,MAAM,KAAI,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACC,SAAS,KAAK,CAAC,EAAE;QAC1E,IAAI,CAACD,SAAS,CAACC,SAAS,GAAI,IAAI,CAACD,SAAS,CAACC,SAAS,GAAG,CAAE;QACzD,IAAI,CAACC,QAAQ,EAAE;MACjB;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,OAAOA,CAAA;IACL,OAAO,IAAI,CAACrC,KAAK,CAACsC,YAAY,EAAE;EAClC;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACvC,KAAK,CAACwC,QAAQ,EAAE;IACrB,IAAI,CAACtC,QAAQ,CAAC2B,IAAI,CAACjB,SAAS,CAAC;IAC7B,IAAI,CAACV,QAAQ,CAACsC,QAAQ,EAAE;EAC1B;EAEAC,OAAOA,CAAEC,IAAa;IACpB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACA,IAAI,CAACC,UAAU,CAAChB,SAAS,CAAC,MAAM,IAAI,CAACQ,QAAQ,EAAE,CAAC;EACvD;EAEAS,YAAYA,CAAEX,SAAuB,EAAEY,KAAK,GAAG,KAAK;IAClD,IAAI,CAACZ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACA,SAAS,CAACa,IAAI,CAACnB,SAAS,CAAC,MAAM,IAAI,CAACQ,QAAQ,EAAE,CAAC;IACpD,IAAI,CAACvC,MAAM,CAAC0C,YAAY,EAAE,CAACX,SAAS,CAAGoB,KAAK,IAAK;MAC/C,IAAI,CAAC,IAAI,CAACd,SAAS,EAAE;QACnB;MACF;MAEA,IAAI,CAACY,KAAK,EAAE;QACV,IAAI,CAACZ,SAAS,CAACD,MAAM,GAAGe,KAAK;QAC7B;MACF;MAEA,MAAMC,cAAc,GAAG,IAAI,CAACf,SAAS,CAACgB,QAAQ,GAAG,IAAI,CAAChB,SAAS,CAACC,SAAS;MACzE,MAAMgB,eAAe,GAAG,CAAC,IAAI,CAAC7D,KAAK,IAAI,EAAE,EAAE2C,MAAM,KAAK,IAAI,CAACC,SAAS,CAACgB,QAAQ,GAAG,CAAC,GAAG,CAAC;MAErF,IAAI,CAAChB,SAAS,CAACD,MAAM,GAAG,CAAC,IAAI,CAAC3C,KAAK,IAAI,EAAE,EAAE2C,MAAM,GAAGkB,eAAe,GAAGF,cAAc;IACtF,CAAC,CAAC;EACJ;EAEAG,cAAcA,CAAEF,QAAgB;IAC9B,IAAI,IAAI,CAAChB,SAAS,EAAE;MAClB,MAAMmB,WAAW,GAAG,IAAI,CAACnB,SAAS,CAACgB,QAAQ;MAC3C,MAAMI,YAAY,GAAG,IAAI,CAACpB,SAAS,CAACC,SAAS;MAE7C,IAAI,CAACD,SAAS,CAACgB,QAAQ,GAAGA,QAAQ;MAClC,IAAI,CAAChB,SAAS,CAACC,SAAS,GAAGoB,IAAI,CAACC,KAAK,CAAEF,YAAY,GAAGD,WAAW,GAAIH,QAAQ,CAAC;MAE9E,IAAI,IAAI,CAAC5D,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC2C,MAAM,EAAE;QACnC,IAAI,CAACG,QAAQ,EAAE;MACjB;IACF;EACF;EAEAA,QAAQA,CAAA;IACN,IAAI,CAAClC,SAAS,CAAC4B,IAAI,CAACjB,SAAS,CAAC;EAChC;EAEQwB,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAAC1C,aAAa,KAAK,IAAI,EAAE;MAC/B,IAAI,CAACA,aAAa,CAAC8D,eAAe,CAC/B9C,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAC9ByB,SAAS,CAAC8B,UAAU,IAAG;QACtB,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC9B,CAAC,CAAC;MAEJ,IAAI,CAACnD,aAAa,CACfI,IAAI,CACH5B,SAAS,CAAC,IAAI,CAACoB,QAAQ,CAAC,CACzB,CACAyB,SAAS,CAAC,CAAE,GAAG5B,MAAM,CAAC,KAAK;QAC1B,IAAI,CAACA,MAAM,GAAGA,MAAM;QAEpB,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;UACtB,IAAI,IAAI,CAACoC,SAAS,EAAE;YAClB,IAAI,CAACA,SAAS,CAACC,SAAS,GAAG,CAAC;UAC9B;QACF;QAEA,IAAI,CAACC,QAAQ,EAAE;MACjB,CAAC,CAAC;MAEJ;IACF;IAEA,IAAI,IAAI,CAAChC,kBAAkB,EAAE;MAC3B,IAAI,CAACV,gBAAgB,CAACY,eAAe,CAClCK,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAC9ByB,SAAS,CAAC5B,MAAM,IAAG;QAClB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACoC,QAAQ,EAAE;MACjB,CAAC,CAAC;IACN;EACF;EAEQpB,gBAAgBA,CAAA;IACtB,IAAID,MAAM,GAAW,EAAE;IAEvB;IACA,IAAI,IAAI,CAAC2C,UAAU,EAAE;MACnB3C,MAAM,GAAG,IAAI,CAAC4C,mBAAmB,CAAC,IAAI,CAACD,UAAU,EAAE3C,MAAM,CAAC;IAC5D;IAEA;IACA,IAAI,IAAI,CAACmB,SAAS,EAAE;MAClB,MAAM;QAAEC,SAAS;QAAEe;MAAQ,CAAE,GAAG,IAAI,CAAChB,SAAS;MAC9CnB,MAAM,CAAC,QAAQ,CAAC,GAAG,CAACoB,SAAS,GAAGe,QAAQ,EAAEU,QAAQ,EAAE;MACpD7C,MAAM,CAAC,OAAO,CAAC,GAAGmC,QAAQ,CAACU,QAAQ,EAAE;IACvC,CAAC,MAAM,IAAI,IAAI,CAACxE,IAAI,EAAE;MACpB2B,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;IACtB;IAEA;IACA,IAAI,IAAI,CAAC4B,IAAI,EAAE;MACb,MAAM;QAAEkB,MAAM;QAAEC;MAAS,CAAE,GAAG,IAAI,CAACnB,IAAI;MACvC,IAAImB,SAAS,KAAK,EAAE,EAAE;QACpB/C,MAAM,CAAC,WAAW,CAAC,GAAG+C,SAAS,CAACC,WAAW,EAAE;QAC7ChD,MAAM,CAAC,QAAQ,CAAC,GAAG8C,MAAM;MAC3B;IACF;IAEA,OAAO9C,MAAM;EACf;EAEQ4C,mBAAmBA,CAAED,UAAkC,EAAEM,aAAqB;IACpF,OAAOC,MAAM,CAACC,IAAI,CAACR,UAAU,CAAC,CAC3BS,MAAM,CAAGC,GAAG,IAAK;MAChB,MAAM/E,KAAK,GAAGqE,UAAU,CAACU,GAAG,CAAC;MAC7B,OAAO,OAAO/E,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,IAAI+E,GAAG,KAAK,WAAW;IAC9F,CAAC,CAAC,CACDC,MAAM,CAAC,CAAEtD,MAAM,EAAEqD,GAAG,KAAK;MACxB,IAAI/E,KAAK,GAAGqE,UAAU,CAACU,GAAG,CAAC;MAE3B,IAAI,OAAO/E,KAAK,KAAK,QAAQ,IAAI4E,MAAM,CAACC,IAAI,CAAC7E,KAAK,CAAC,CAACiF,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAACtF,mBAAmB,CAAC,CAAC,EAAE;QAC/F6B,MAAM,GAAG,IAAI,CAAC4C,mBAAmB,CAACtE,KAAK,EAAE0B,MAAM,CAAC;MAClD,CAAC,MAAM;QACL,IAAI1C,MAAM,CAACoG,QAAQ,CAACpF,KAAK,CAAC,EAAE;UAC1BA,KAAK,GAAIA,KAAuB,CAACqF,WAAW,EAAE;QAChD;QACA3D,MAAM,CAACqD,GAAG,CAAC,GAAG/E,KAAK;MACrB;MACA,OAAO0B,MAAM;IACf,CAAC,EAAEiD,aAAa,CAAC;EACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}