{"ast": null, "code": "var _LessDialogComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./less-dialog.component.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nlet LessDialogComponent = (_LessDialogComponent = class LessDialogComponent {\n  constructor(data) {\n    this.available = [];\n    this.checked = [];\n    this.available = data.available;\n    this.checked = data.checked;\n    this.runAction = data.runAction;\n    this.declineAction = data.declineAction;\n  }\n}, _LessDialogComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [MAT_DIALOG_DATA]\n  }]\n}], _LessDialogComponent);\nLessDialogComponent = __decorate([Component({\n  selector: 'lib-swui-less-dialog',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], LessDialogComponent);\nexport { LessDialogComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "Component", "Inject", "MAT_DIALOG_DATA", "LessDialogComponent", "_LessDialogComponent", "constructor", "data", "available", "checked", "runAction", "declineAction", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/bulk-actions/dialogs/less-dialog.component.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./less-dialog.component.html?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nlet LessDialogComponent = class LessDialogComponent {\n    constructor(data) {\n        this.available = [];\n        this.checked = [];\n        this.available = data.available;\n        this.checked = data.checked;\n        this.runAction = data.runAction;\n        this.declineAction = data.declineAction;\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [MAT_DIALOG_DATA,] }] }\n    ]; }\n};\nLessDialogComponent = __decorate([\n    Component({\n        selector: 'lib-swui-less-dialog',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false\n    })\n], LessDialogComponent);\nexport { LessDialogComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,IAAIC,mBAAmB,IAAAC,oBAAA,GAAG,MAAMD,mBAAmB,CAAC;EAChDE,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACD,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC/B,IAAI,CAACC,OAAO,GAAGF,IAAI,CAACE,OAAO;IAC3B,IAAI,CAACC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC/B,IAAI,CAACC,aAAa,GAAGJ,IAAI,CAACI,aAAa;EAC3C;AAIJ,CAAC,EAHYN,oBAAA,CAAKO,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEX,MAAM;IAAEc,IAAI,EAAE,CAACb,eAAe;EAAG,CAAC;AAAE,CAAC,CAChF,EAAAE,oBAAA,CACJ;AACDD,mBAAmB,GAAGL,UAAU,CAAC,CAC7BE,SAAS,CAAC;EACNgB,QAAQ,EAAE,sBAAsB;EAChCC,QAAQ,EAAElB,oBAAoB;EAC9BmB,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEf,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}