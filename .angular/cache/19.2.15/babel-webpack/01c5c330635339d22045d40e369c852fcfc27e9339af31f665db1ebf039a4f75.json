{"ast": null, "code": "import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function windowTime(windowTimeSpan) {\n  var _a, _b;\n  var otherArgs = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    otherArgs[_i - 1] = arguments[_i];\n  }\n  var scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  var windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  var maxWindowSize = otherArgs[1] || Infinity;\n  return operate(function (source, subscriber) {\n    var windowRecords = [];\n    var restartOnClose = false;\n    var closeWindow = function (record) {\n      var window = record.window,\n        subs = record.subs;\n      window.complete();\n      subs.unsubscribe();\n      arrRemove(windowRecords, record);\n      restartOnClose && startWindow();\n    };\n    var startWindow = function () {\n      if (windowRecords) {\n        var subs = new Subscription();\n        subscriber.add(subs);\n        var window_1 = new Subject();\n        var record_1 = {\n          window: window_1,\n          subs: subs,\n          seen: 0\n        };\n        windowRecords.push(record_1);\n        subscriber.next(window_1.asObservable());\n        executeSchedule(subs, scheduler, function () {\n          return closeWindow(record_1);\n        }, windowTimeSpan);\n      }\n    };\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n    } else {\n      restartOnClose = true;\n    }\n    startWindow();\n    var loop = function (cb) {\n      return windowRecords.slice().forEach(cb);\n    };\n    var terminate = function (cb) {\n      loop(function (_a) {\n        var window = _a.window;\n        return cb(window);\n      });\n      cb(subscriber);\n      subscriber.unsubscribe();\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      loop(function (record) {\n        record.window.next(value);\n        maxWindowSize <= ++record.seen && closeWindow(record);\n      });\n    }, function () {\n      return terminate(function (consumer) {\n        return consumer.complete();\n      });\n    }, function (err) {\n      return terminate(function (consumer) {\n        return consumer.error(err);\n      });\n    }));\n    return function () {\n      windowRecords = null;\n    };\n  });\n}\n//# sourceMappingURL=windowTime.js.map", "map": {"version": 3, "names": ["Subject", "asyncScheduler", "Subscription", "operate", "createOperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "popScheduler", "executeSchedule", "windowTime", "windowTimeSpan", "_a", "_b", "otherArgs", "_i", "arguments", "length", "scheduler", "windowCreationInterval", "maxWindowSize", "Infinity", "source", "subscriber", "windowRecords", "restartOnClose", "closeWindow", "record", "window", "subs", "complete", "unsubscribe", "startWindow", "add", "window_1", "record_1", "seen", "push", "next", "asObservable", "loop", "cb", "slice", "for<PERSON>ach", "terminate", "subscribe", "value", "consumer", "err", "error"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/operators/windowTime.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function windowTime(windowTimeSpan) {\n    var _a, _b;\n    var otherArgs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        otherArgs[_i - 1] = arguments[_i];\n    }\n    var scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n    var windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n    var maxWindowSize = otherArgs[1] || Infinity;\n    return operate(function (source, subscriber) {\n        var windowRecords = [];\n        var restartOnClose = false;\n        var closeWindow = function (record) {\n            var window = record.window, subs = record.subs;\n            window.complete();\n            subs.unsubscribe();\n            arrRemove(windowRecords, record);\n            restartOnClose && startWindow();\n        };\n        var startWindow = function () {\n            if (windowRecords) {\n                var subs = new Subscription();\n                subscriber.add(subs);\n                var window_1 = new Subject();\n                var record_1 = {\n                    window: window_1,\n                    subs: subs,\n                    seen: 0,\n                };\n                windowRecords.push(record_1);\n                subscriber.next(window_1.asObservable());\n                executeSchedule(subs, scheduler, function () { return closeWindow(record_1); }, windowTimeSpan);\n            }\n        };\n        if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n            executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n        }\n        else {\n            restartOnClose = true;\n        }\n        startWindow();\n        var loop = function (cb) { return windowRecords.slice().forEach(cb); };\n        var terminate = function (cb) {\n            loop(function (_a) {\n                var window = _a.window;\n                return cb(window);\n            });\n            cb(subscriber);\n            subscriber.unsubscribe();\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n            loop(function (record) {\n                record.window.next(value);\n                maxWindowSize <= ++record.seen && closeWindow(record);\n            });\n        }, function () { return terminate(function (consumer) { return consumer.complete(); }); }, function (err) { return terminate(function (consumer) { return consumer.error(err); }); }));\n        return function () {\n            windowRecords = null;\n        };\n    });\n}\n//# sourceMappingURL=windowTime.js.map"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,SAASC,UAAUA,CAACC,cAAc,EAAE;EACvC,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIC,SAAS,GAAG,EAAE;EAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,SAAS,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EACrC;EACA,IAAIG,SAAS,GAAG,CAACN,EAAE,GAAGJ,YAAY,CAACM,SAAS,CAAC,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGT,cAAc;EAC9F,IAAIgB,sBAAsB,GAAG,CAACN,EAAE,GAAGC,SAAS,CAAC,CAAC,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;EACtF,IAAIO,aAAa,GAAGN,SAAS,CAAC,CAAC,CAAC,IAAIO,QAAQ;EAC5C,OAAOhB,OAAO,CAAC,UAAUiB,MAAM,EAAEC,UAAU,EAAE;IACzC,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAIC,WAAW,GAAG,SAAAA,CAAUC,MAAM,EAAE;MAChC,IAAIC,MAAM,GAAGD,MAAM,CAACC,MAAM;QAAEC,IAAI,GAAGF,MAAM,CAACE,IAAI;MAC9CD,MAAM,CAACE,QAAQ,CAAC,CAAC;MACjBD,IAAI,CAACE,WAAW,CAAC,CAAC;MAClBxB,SAAS,CAACiB,aAAa,EAAEG,MAAM,CAAC;MAChCF,cAAc,IAAIO,WAAW,CAAC,CAAC;IACnC,CAAC;IACD,IAAIA,WAAW,GAAG,SAAAA,CAAA,EAAY;MAC1B,IAAIR,aAAa,EAAE;QACf,IAAIK,IAAI,GAAG,IAAIzB,YAAY,CAAC,CAAC;QAC7BmB,UAAU,CAACU,GAAG,CAACJ,IAAI,CAAC;QACpB,IAAIK,QAAQ,GAAG,IAAIhC,OAAO,CAAC,CAAC;QAC5B,IAAIiC,QAAQ,GAAG;UACXP,MAAM,EAAEM,QAAQ;UAChBL,IAAI,EAAEA,IAAI;UACVO,IAAI,EAAE;QACV,CAAC;QACDZ,aAAa,CAACa,IAAI,CAACF,QAAQ,CAAC;QAC5BZ,UAAU,CAACe,IAAI,CAACJ,QAAQ,CAACK,YAAY,CAAC,CAAC,CAAC;QACxC9B,eAAe,CAACoB,IAAI,EAAEX,SAAS,EAAE,YAAY;UAAE,OAAOQ,WAAW,CAACS,QAAQ,CAAC;QAAE,CAAC,EAAExB,cAAc,CAAC;MACnG;IACJ,CAAC;IACD,IAAIQ,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAChEV,eAAe,CAACc,UAAU,EAAEL,SAAS,EAAEc,WAAW,EAAEb,sBAAsB,EAAE,IAAI,CAAC;IACrF,CAAC,MACI;MACDM,cAAc,GAAG,IAAI;IACzB;IACAO,WAAW,CAAC,CAAC;IACb,IAAIQ,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAE;MAAE,OAAOjB,aAAa,CAACkB,KAAK,CAAC,CAAC,CAACC,OAAO,CAACF,EAAE,CAAC;IAAE,CAAC;IACtE,IAAIG,SAAS,GAAG,SAAAA,CAAUH,EAAE,EAAE;MAC1BD,IAAI,CAAC,UAAU5B,EAAE,EAAE;QACf,IAAIgB,MAAM,GAAGhB,EAAE,CAACgB,MAAM;QACtB,OAAOa,EAAE,CAACb,MAAM,CAAC;MACrB,CAAC,CAAC;MACFa,EAAE,CAAClB,UAAU,CAAC;MACdA,UAAU,CAACQ,WAAW,CAAC,CAAC;IAC5B,CAAC;IACDT,MAAM,CAACuB,SAAS,CAACvC,wBAAwB,CAACiB,UAAU,EAAE,UAAUuB,KAAK,EAAE;MACnEN,IAAI,CAAC,UAAUb,MAAM,EAAE;QACnBA,MAAM,CAACC,MAAM,CAACU,IAAI,CAACQ,KAAK,CAAC;QACzB1B,aAAa,IAAI,EAAEO,MAAM,CAACS,IAAI,IAAIV,WAAW,CAACC,MAAM,CAAC;MACzD,CAAC,CAAC;IACN,CAAC,EAAE,YAAY;MAAE,OAAOiB,SAAS,CAAC,UAAUG,QAAQ,EAAE;QAAE,OAAOA,QAAQ,CAACjB,QAAQ,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,EAAE,UAAUkB,GAAG,EAAE;MAAE,OAAOJ,SAAS,CAAC,UAAUG,QAAQ,EAAE;QAAE,OAAOA,QAAQ,CAACE,KAAK,CAACD,GAAG,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,CAAC,CAAC;IACtL,OAAO,YAAY;MACfxB,aAAa,GAAG,IAAI;IACxB,CAAC;EACL,CAAC,CAAC;AACN;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}