{"ast": null, "code": "var _FormattedMoneyPipe;\nimport { __decorate } from \"tslib\";\nimport { Pi<PERSON> } from '@angular/core';\nlet FormattedMoneyPipe = (_FormattedMoneyPipe = class FormattedMoneyPipe {\n  constructor() {}\n  transform(input = 0, fractionCount = 2, delimiter = ' ', currencyLocale = window.navigator.language) {\n    let result;\n    if (input === null) {\n      result = '';\n    } else if (input.toLocaleString && currencyLocale) {\n      result = parseFloat(input).toLocaleString(currencyLocale, {\n        minimumFractionDigits: fractionCount,\n        maximumFractionDigits: fractionCount,\n        useGrouping: !!delimiter\n      });\n    } else {\n      result = parseFloat(input).toFixed(fractionCount).replace(/(\\d)(?=(\\d{3})+($|\\.))/g, '$1' + delimiter);\n    }\n    return result;\n  }\n}, _FormattedMoneyPipe.ctorParameters = () => [], _FormattedMoneyPipe);\nFormattedMoneyPipe = __decorate([Pipe({\n  name: 'formattedMoney',\n  pure: true,\n  standalone: false\n})], FormattedMoneyPipe);\nexport { FormattedMoneyPipe };", "map": {"version": 3, "names": ["__decorate", "<PERSON><PERSON>", "FormattedMoneyPipe", "_FormattedMoneyPipe", "constructor", "transform", "input", "fractionCount", "delimiter", "currencyLocale", "window", "navigator", "language", "result", "toLocaleString", "parseFloat", "minimumFractionDigits", "maximumFractionDigits", "useGrouping", "toFixed", "replace", "ctorParameters", "name", "pure", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/pipes/formatted-money.pipe.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Pi<PERSON> } from '@angular/core';\nlet FormattedMoneyPipe = class FormattedMoneyPipe {\n    constructor() {\n    }\n    transform(input = 0, fractionCount = 2, delimiter = ' ', currencyLocale = window.navigator.language) {\n        let result;\n        if (input === null) {\n            result = '';\n        }\n        else if (input.toLocaleString && currencyLocale) {\n            result = parseFloat(input).toLocaleString(currencyLocale, {\n                minimumFractionDigits: fractionCount,\n                maximumFractionDigits: fractionCount,\n                useGrouping: !!delimiter,\n            });\n        }\n        else {\n            result = parseFloat(input).toFixed(fractionCount).replace(/(\\d)(?=(\\d{3})+($|\\.))/g, '$1' + delimiter);\n        }\n        return result;\n    }\n    static { this.ctorParameters = () => []; }\n};\nFormattedMoneyPipe = __decorate([\n    Pipe({\n        name: 'formattedMoney', pure: true,\n        standalone: false\n    })\n], FormattedMoneyPipe);\nexport { FormattedMoneyPipe };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,IAAI,QAAQ,eAAe;AACpC,IAAIC,kBAAkB,IAAAC,mBAAA,GAAG,MAAMD,kBAAkB,CAAC;EAC9CE,WAAWA,CAAA,EAAG,CACd;EACAC,SAASA,CAACC,KAAK,GAAG,CAAC,EAAEC,aAAa,GAAG,CAAC,EAAEC,SAAS,GAAG,GAAG,EAAEC,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,EAAE;IACjG,IAAIC,MAAM;IACV,IAAIP,KAAK,KAAK,IAAI,EAAE;MAChBO,MAAM,GAAG,EAAE;IACf,CAAC,MACI,IAAIP,KAAK,CAACQ,cAAc,IAAIL,cAAc,EAAE;MAC7CI,MAAM,GAAGE,UAAU,CAACT,KAAK,CAAC,CAACQ,cAAc,CAACL,cAAc,EAAE;QACtDO,qBAAqB,EAAET,aAAa;QACpCU,qBAAqB,EAAEV,aAAa;QACpCW,WAAW,EAAE,CAAC,CAACV;MACnB,CAAC,CAAC;IACN,CAAC,MACI;MACDK,MAAM,GAAGE,UAAU,CAACT,KAAK,CAAC,CAACa,OAAO,CAACZ,aAAa,CAAC,CAACa,OAAO,CAAC,yBAAyB,EAAE,IAAI,GAAGZ,SAAS,CAAC;IAC1G;IACA,OAAOK,MAAM;EACjB;AAEJ,CAAC,EADYV,mBAAA,CAAKkB,cAAc,GAAG,MAAM,EAAE,EAAAlB,mBAAA,CAC1C;AACDD,kBAAkB,GAAGF,UAAU,CAAC,CAC5BC,IAAI,CAAC;EACDqB,IAAI,EAAE,gBAAgB;EAAEC,IAAI,EAAE,IAAI;EAClCC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEtB,kBAAkB,CAAC;AACtB,SAASA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}