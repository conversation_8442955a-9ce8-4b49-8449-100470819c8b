{"ast": null, "code": "var _SwuiTdInactivityWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./inactivity.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./inactivity.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdInactivityWidgetComponent = (_SwuiTdInactivityWidgetComponent = class SwuiTdInactivityWidgetComponent {\n  constructor({\n    schema,\n    row,\n    field\n  }) {\n    var _schema$td;\n    this.row = row;\n    this.field = field;\n    const valueFn = (_schema$td = schema.td) === null || _schema$td === void 0 ? void 0 : _schema$td.valueFn;\n    this.value = schema.td && 'valueFn' in schema.td ? valueFn && valueFn(row, schema) : this.row[this.field];\n  }\n}, _SwuiTdInactivityWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdInactivityWidgetComponent);\nSwuiTdInactivityWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-inactivity-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdInactivityWidgetComponent);\nexport { SwuiTdInactivityWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdInactivityWidgetComponent", "_SwuiTdInactivityWidgetComponent", "constructor", "schema", "row", "field", "_schema$td", "valueFn", "td", "value", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/inactivity/inactivity.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./inactivity.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./inactivity.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdInactivityWidgetComponent = class SwuiTdInactivityWidgetComponent {\n    constructor({ schema, row, field }) {\n        this.row = row;\n        this.field = field;\n        const valueFn = schema.td?.valueFn;\n        this.value = schema.td && 'valueFn' in schema.td ? (valueFn && valueFn(row, schema)) : this.row[this.field];\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdInactivityWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-inactivity-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdInactivityWidgetComponent);\nexport { SwuiTdInactivityWidgetComponent };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,qCAAqC;AACtE,OAAOC,oBAAoB,MAAM,qCAAqC;AACtE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,+BAA+B,IAAAC,gCAAA,GAAG,MAAMD,+BAA+B,CAAC;EACxEE,WAAWA,CAAC;IAAEC,MAAM;IAAEC,GAAG;IAAEC;EAAM,CAAC,EAAE;IAAA,IAAAC,UAAA;IAChC,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,MAAME,OAAO,IAAAD,UAAA,GAAGH,MAAM,CAACK,EAAE,cAAAF,UAAA,uBAATA,UAAA,CAAWC,OAAO;IAClC,IAAI,CAACE,KAAK,GAAGN,MAAM,CAACK,EAAE,IAAI,SAAS,IAAIL,MAAM,CAACK,EAAE,GAAID,OAAO,IAAIA,OAAO,CAACH,GAAG,EAAED,MAAM,CAAC,GAAI,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,KAAK,CAAC;EAC/G;AAIJ,CAAC,EAHYJ,gCAAA,CAAKS,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEb,MAAM;IAAEgB,IAAI,EAAE,CAACf,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,gCAAA,CACJ;AACDD,+BAA+B,GAAGN,UAAU,CAAC,CACzCG,SAAS,CAAC;EACNkB,QAAQ,EAAE,+BAA+B;EACzCC,QAAQ,EAAErB,oBAAoB;EAC9BsB,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAACtB,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEI,+BAA+B,CAAC;AACnC,SAASA,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}