{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { TestWidgetComponent } from './test-widget.component';\nimport { CommonModule } from '@angular/common';\nlet TestWidgetModule = class TestWidgetModule {};\nTestWidgetModule = __decorate([NgModule({\n  imports: [CommonModule],\n  declarations: [TestWidgetComponent],\n  exports: [],\n  providers: []\n})], TestWidgetModule);\nexport { TestWidgetModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "TestWidgetComponent", "CommonModule", "TestWidgetModule", "imports", "declarations", "exports", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/_stories/test-widget/test-widget.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { TestWidgetComponent } from './test-widget.component';\nimport { CommonModule } from '@angular/common';\nlet TestWidgetModule = class TestWidgetModule {\n};\nTestWidgetModule = __decorate([\n    NgModule({\n        imports: [CommonModule],\n        declarations: [\n            TestWidgetComponent\n        ],\n        exports: [],\n        providers: [],\n    })\n], TestWidgetModule);\nexport { TestWidgetModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,IAAIC,gBAAgB,GAAG,MAAMA,gBAAgB,CAAC,EAC7C;AACDA,gBAAgB,GAAGJ,UAAU,CAAC,CAC1BC,QAAQ,CAAC;EACLI,OAAO,EAAE,CAACF,YAAY,CAAC;EACvBG,YAAY,EAAE,CACVJ,mBAAmB,CACtB;EACDK,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE;AACf,CAAC,CAAC,CACL,EAAEJ,gBAAgB,CAAC;AACpB,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}