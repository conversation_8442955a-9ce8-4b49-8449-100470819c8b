{"ast": null, "code": "import { from } from './from';\nexport function pairs(obj, scheduler) {\n  return from(Object.entries(obj), scheduler);\n}\n//# sourceMappingURL=pairs.js.map", "map": {"version": 3, "names": ["from", "pairs", "obj", "scheduler", "Object", "entries"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/pairs.js"], "sourcesContent": ["import { from } from './from';\nexport function pairs(obj, scheduler) {\n    return from(Object.entries(obj), scheduler);\n}\n//# sourceMappingURL=pairs.js.map"], "mappings": "AAAA,SAASA,IAAI,QAAQ,QAAQ;AAC7B,OAAO,SAASC,KAAKA,CAACC,GAAG,EAAEC,SAAS,EAAE;EAClC,OAAOH,IAAI,CAACI,MAAM,CAACC,OAAO,CAACH,GAAG,CAAC,EAAEC,SAAS,CAAC;AAC/C;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}