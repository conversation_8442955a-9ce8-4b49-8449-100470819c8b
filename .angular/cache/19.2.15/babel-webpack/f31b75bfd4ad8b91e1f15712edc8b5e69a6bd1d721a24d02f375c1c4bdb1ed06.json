{"ast": null, "code": "import { TestBed, waitForAsync } from '@angular/core/testing';\nimport { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\nimport { notificationsMatModules } from '../swui-notifications.module';\nimport { SwuiSnackbarComponent } from './swui-snackbar.component';\ndescribe('SwuiSnackbarComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(waitForAsync(() => {\n    const matSnackBarRef = jasmine.createSpyObj('MatSnackBarRef', ['dismiss']);\n    TestBed.configureTestingModule({\n      imports: [...notificationsMatModules],\n      declarations: [SwuiSnackbarComponent],\n      schemas: [NO_ERRORS_SCHEMA],\n      providers: [{\n        provide: MatSnackBarRef,\n        useValue: matSnackBarRef\n      }, {\n        provide: MAT_SNACK_BAR_DATA,\n        useValue: {}\n      }]\n    }).compileComponents();\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiSnackbarComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "waitForAsync", "MAT_SNACK_BAR_DATA", "MatSnackBarRef", "NO_ERRORS_SCHEMA", "notificationsMatModules", "SwuiSnackbarComponent", "describe", "component", "fixture", "beforeEach", "matSnackBarRef", "jasmine", "createSpyObj", "configureTestingModule", "imports", "declarations", "schemas", "providers", "provide", "useValue", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-notifications/swui-snackbar/swui-snackbar.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';\nimport { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\n\nimport { notificationsMatModules } from '../swui-notifications.module';\nimport { SwuiSnackbarComponent } from './swui-snackbar.component';\n\ndescribe('SwuiSnackbarComponent', () => {\n  let component: SwuiSnackbarComponent;\n  let fixture: ComponentFixture<SwuiSnackbarComponent>;\n\n  beforeEach(waitForAsync(() => {\n    const matSnackBarRef = jasmine.createSpyObj('MatSnackBarRef', ['dismiss']);\n    TestBed.configureTestingModule({\n      imports: [\n        ...notificationsMatModules,\n      ],\n      declarations: [SwuiSnackbarComponent],\n      schemas: [NO_ERRORS_SCHEMA],\n      providers: [\n        { provide: MatSnackBarRef, useValue: matSnackBarRef },\n        { provide: MAT_SNACK_BAR_DATA, useValue: {} },\n      ]\n    })\n      .compileComponents();\n  }));\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(SwuiSnackbarComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAA2BA,OAAO,EAAEC,YAAY,QAAQ,uBAAuB;AAC/E,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,6BAA6B;AAChF,SAASC,gBAAgB,QAAQ,eAAe;AAEhD,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,qBAAqB,QAAQ,2BAA2B;AAEjEC,QAAQ,CAAC,uBAAuB,EAAE,MAAK;EACrC,IAAIC,SAAgC;EACpC,IAAIC,OAAgD;EAEpDC,UAAU,CAACT,YAAY,CAAC,MAAK;IAC3B,MAAMU,cAAc,GAAGC,OAAO,CAACC,YAAY,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,CAAC;IAC1Eb,OAAO,CAACc,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACP,GAAGV,uBAAuB,CAC3B;MACDW,YAAY,EAAE,CAACV,qBAAqB,CAAC;MACrCW,OAAO,EAAE,CAACb,gBAAgB,CAAC;MAC3Bc,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEhB,cAAc;QAAEiB,QAAQ,EAAET;MAAc,CAAE,EACrD;QAAEQ,OAAO,EAAEjB,kBAAkB;QAAEkB,QAAQ,EAAE;MAAE,CAAE;KAEhD,CAAC,CACCC,iBAAiB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHX,UAAU,CAAC,MAAK;IACdD,OAAO,GAAGT,OAAO,CAACsB,eAAe,CAAChB,qBAAqB,CAAC;IACxDE,SAAS,GAAGC,OAAO,CAACc,iBAAiB;IACrCd,OAAO,CAACe,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAClB,SAAS,CAAC,CAACmB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}