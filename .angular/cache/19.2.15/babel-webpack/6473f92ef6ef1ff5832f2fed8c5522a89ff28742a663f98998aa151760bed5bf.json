{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiChipsAutocompleteComponent } from './swui-chips-autocomplete.component';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nexport const CHIPS_AUTOCOMPLETE_MODULES = [MatAutocompleteModule, MatButtonModule, MatChipsModule, MatIconModule, MatInputModule, ReactiveFormsModule];\nlet SwuiChipsAutocompleteModule = class SwuiChipsAutocompleteModule {};\nSwuiChipsAutocompleteModule = __decorate([NgModule({\n  declarations: [SwuiChipsAutocompleteComponent],\n  exports: [SwuiChipsAutocompleteComponent],\n  imports: [CommonModule, ...CHIPS_AUTOCOMPLETE_MODULES]\n})], SwuiChipsAutocompleteModule);\nexport { SwuiChipsAutocompleteModule };", "map": {"version": 3, "names": ["CommonModule", "NgModule", "ReactiveFormsModule", "SwuiChipsAutocompleteComponent", "MatAutocompleteModule", "MatIconModule", "MatInputModule", "MatButtonModule", "MatChipsModule", "CHIPS_AUTOCOMPLETE_MODULES", "SwuiChipsAutocompleteModule", "__decorate", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-chips-autocomplete/swui-chips-autocomplete.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\n\nimport { SwuiChipsAutocompleteComponent } from './swui-chips-autocomplete.component';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\n\nexport const CHIPS_AUTOCOMPLETE_MODULES = [\n  MatAutocompleteModule,\n  MatButtonModule,\n  MatChipsModule,\n  MatIconModule,\n  MatInputModule,\n  ReactiveFormsModule\n];\n\n@NgModule({\n  declarations: [\n    SwuiChipsAutocompleteComponent\n  ],\n  exports: [\n    SwuiChipsAutocompleteComponent\n  ],\n  imports: [\n    CommonModule,\n    ...CHIPS_AUTOCOMPLETE_MODULES\n  ]\n})\nexport class SwuiChipsAutocompleteModule {\n}\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD,SAASC,8BAA8B,QAAQ,qCAAqC;AACpF,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AAExD,OAAO,MAAMC,0BAA0B,GAAG,CACxCL,qBAAqB,EACrBG,eAAe,EACfC,cAAc,EACdH,aAAa,EACbC,cAAc,EACdJ,mBAAmB,CACpB;AAcM,IAAMQ,2BAA2B,GAAjC,MAAMA,2BAA2B,GACvC;AADYA,2BAA2B,GAAAC,UAAA,EAZvCV,QAAQ,CAAC;EACRW,YAAY,EAAE,CACZT,8BAA8B,CAC/B;EACDU,OAAO,EAAE,CACPV,8BAA8B,CAC/B;EACDW,OAAO,EAAE,CACPd,YAAY,EACZ,GAAGS,0BAA0B;CAEhC,CAAC,C,EACWC,2BAA2B,CACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}