{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { EMPTY } from './empty';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { popResultSelector } from '../util/args';\nexport function zip() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var resultSelector = popResultSelector(args);\n  var sources = argsOrArgArray(args);\n  return sources.length ? new Observable(function (subscriber) {\n    var buffers = sources.map(function () {\n      return [];\n    });\n    var completed = sources.map(function () {\n      return false;\n    });\n    subscriber.add(function () {\n      buffers = completed = null;\n    });\n    var _loop_1 = function (sourceIndex) {\n      innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, function (value) {\n        buffers[sourceIndex].push(value);\n        if (buffers.every(function (buffer) {\n          return buffer.length;\n        })) {\n          var result = buffers.map(function (buffer) {\n            return buffer.shift();\n          });\n          subscriber.next(resultSelector ? resultSelector.apply(void 0, __spreadArray([], __read(result))) : result);\n          if (buffers.some(function (buffer, i) {\n            return !buffer.length && completed[i];\n          })) {\n            subscriber.complete();\n          }\n        }\n      }, function () {\n        completed[sourceIndex] = true;\n        !buffers[sourceIndex].length && subscriber.complete();\n      }));\n    };\n    for (var sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n      _loop_1(sourceIndex);\n    }\n    return function () {\n      buffers = completed = null;\n    };\n  }) : EMPTY;\n}\n//# sourceMappingURL=zip.js.map", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "Observable", "innerFrom", "argsOrArgArray", "EMPTY", "createOperatorSubscriber", "popResultSelector", "zip", "args", "_i", "arguments", "length", "resultSelector", "sources", "subscriber", "buffers", "map", "completed", "add", "_loop_1", "sourceIndex", "subscribe", "value", "push", "every", "buffer", "result", "shift", "next", "apply", "some", "i", "complete", "closed"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/observable/zip.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { EMPTY } from './empty';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { popResultSelector } from '../util/args';\nexport function zip() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var resultSelector = popResultSelector(args);\n    var sources = argsOrArgArray(args);\n    return sources.length\n        ? new Observable(function (subscriber) {\n            var buffers = sources.map(function () { return []; });\n            var completed = sources.map(function () { return false; });\n            subscriber.add(function () {\n                buffers = completed = null;\n            });\n            var _loop_1 = function (sourceIndex) {\n                innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, function (value) {\n                    buffers[sourceIndex].push(value);\n                    if (buffers.every(function (buffer) { return buffer.length; })) {\n                        var result = buffers.map(function (buffer) { return buffer.shift(); });\n                        subscriber.next(resultSelector ? resultSelector.apply(void 0, __spreadArray([], __read(result))) : result);\n                        if (buffers.some(function (buffer, i) { return !buffer.length && completed[i]; })) {\n                            subscriber.complete();\n                        }\n                    }\n                }, function () {\n                    completed[sourceIndex] = true;\n                    !buffers[sourceIndex].length && subscriber.complete();\n                }));\n            };\n            for (var sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n                _loop_1(sourceIndex);\n            }\n            return function () {\n                buffers = completed = null;\n            };\n        })\n        : EMPTY;\n}\n//# sourceMappingURL=zip.js.map"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,iBAAiB,QAAQ,cAAc;AAChD,OAAO,SAASC,GAAGA,CAAA,EAAG;EAClB,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC5B;EACA,IAAIG,cAAc,GAAGN,iBAAiB,CAACE,IAAI,CAAC;EAC5C,IAAIK,OAAO,GAAGV,cAAc,CAACK,IAAI,CAAC;EAClC,OAAOK,OAAO,CAACF,MAAM,GACf,IAAIV,UAAU,CAAC,UAAUa,UAAU,EAAE;IACnC,IAAIC,OAAO,GAAGF,OAAO,CAACG,GAAG,CAAC,YAAY;MAAE,OAAO,EAAE;IAAE,CAAC,CAAC;IACrD,IAAIC,SAAS,GAAGJ,OAAO,CAACG,GAAG,CAAC,YAAY;MAAE,OAAO,KAAK;IAAE,CAAC,CAAC;IAC1DF,UAAU,CAACI,GAAG,CAAC,YAAY;MACvBH,OAAO,GAAGE,SAAS,GAAG,IAAI;IAC9B,CAAC,CAAC;IACF,IAAIE,OAAO,GAAG,SAAAA,CAAUC,WAAW,EAAE;MACjClB,SAAS,CAACW,OAAO,CAACO,WAAW,CAAC,CAAC,CAACC,SAAS,CAAChB,wBAAwB,CAACS,UAAU,EAAE,UAAUQ,KAAK,EAAE;QAC5FP,OAAO,CAACK,WAAW,CAAC,CAACG,IAAI,CAACD,KAAK,CAAC;QAChC,IAAIP,OAAO,CAACS,KAAK,CAAC,UAAUC,MAAM,EAAE;UAAE,OAAOA,MAAM,CAACd,MAAM;QAAE,CAAC,CAAC,EAAE;UAC5D,IAAIe,MAAM,GAAGX,OAAO,CAACC,GAAG,CAAC,UAAUS,MAAM,EAAE;YAAE,OAAOA,MAAM,CAACE,KAAK,CAAC,CAAC;UAAE,CAAC,CAAC;UACtEb,UAAU,CAACc,IAAI,CAAChB,cAAc,GAAGA,cAAc,CAACiB,KAAK,CAAC,KAAK,CAAC,EAAE7B,aAAa,CAAC,EAAE,EAAED,MAAM,CAAC2B,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC;UAC1G,IAAIX,OAAO,CAACe,IAAI,CAAC,UAAUL,MAAM,EAAEM,CAAC,EAAE;YAAE,OAAO,CAACN,MAAM,CAACd,MAAM,IAAIM,SAAS,CAACc,CAAC,CAAC;UAAE,CAAC,CAAC,EAAE;YAC/EjB,UAAU,CAACkB,QAAQ,CAAC,CAAC;UACzB;QACJ;MACJ,CAAC,EAAE,YAAY;QACXf,SAAS,CAACG,WAAW,CAAC,GAAG,IAAI;QAC7B,CAACL,OAAO,CAACK,WAAW,CAAC,CAACT,MAAM,IAAIG,UAAU,CAACkB,QAAQ,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC;IACP,CAAC;IACD,KAAK,IAAIZ,WAAW,GAAG,CAAC,EAAE,CAACN,UAAU,CAACmB,MAAM,IAAIb,WAAW,GAAGP,OAAO,CAACF,MAAM,EAAES,WAAW,EAAE,EAAE;MACzFD,OAAO,CAACC,WAAW,CAAC;IACxB;IACA,OAAO,YAAY;MACfL,OAAO,GAAGE,SAAS,GAAG,IAAI;IAC9B,CAAC;EACL,CAAC,CAAC,GACAb,KAAK;AACf;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}