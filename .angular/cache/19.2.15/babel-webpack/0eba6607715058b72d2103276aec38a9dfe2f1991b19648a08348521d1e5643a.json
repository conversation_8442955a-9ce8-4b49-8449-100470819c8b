{"ast": null, "code": "var _SwuiDatetimepickerComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-datetimepicker.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./swui-datetimepicker.component.scss?ngResource\";\nimport { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup, FormGroupDirective, NgControl } from '@angular/forms';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport * as moment from 'moment';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\nfunction transformValue(value) {\n  if (value !== null) {\n    const date = moment.isMoment(value) ? value : moment.parseZone(value);\n    if (date.isValid()) {\n      return {\n        date: date,\n        time: {\n          hour: date.hours(),\n          minute: date.minutes(),\n          second: date.seconds()\n        }\n      };\n    }\n  }\n  return {\n    date: null,\n    time: null\n  };\n}\nconst CONTROL_NAME = 'lib-swui-datetimepicker';\nlet nextUniqueId = 0;\nlet SwuiDatetimepickerComponent = (_SwuiDatetimepickerComponent = class SwuiDatetimepickerComponent extends SwuiMatFormFieldControl {\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    this._value = value;\n    this.writeValue(value);\n    this.stateChanges.next(undefined);\n  }\n  set config(value) {\n    this._config = value;\n    if (this.form.contains('time')) {\n      const timeGroup = this.form.get('time');\n      this._config.disableTimepicker ? timeGroup.disable() : timeGroup.enable();\n    }\n  }\n  get config() {\n    return this._config;\n  }\n  get empty() {\n    return this.formattedDate === '';\n  }\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n  constructor(fm, elRef, ngControl, parentFormGroup, errorStateMatcher) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this.dateSource = null;\n    this.controlType = CONTROL_NAME;\n    this.id = `${CONTROL_NAME}-${nextUniqueId++}`;\n    this._config = {};\n    this.form = new UntypedFormGroup({\n      date: new UntypedFormControl(),\n      time: new UntypedFormControl()\n    });\n  }\n  writeValue(value) {\n    if (typeof value === 'undefined') {\n      return;\n    }\n    const data = transformValue(value);\n    this.form.setValue(data, {\n      emitEvent: false\n    });\n    this.dateSource = this.transformFrom();\n  }\n  onContainerClick(event) {\n    if (this.dateRef && event.target.tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n      this.dateRef.openMenu();\n      this.focused = true;\n    }\n  }\n  onClose() {\n    this.focused = false;\n    this.onTouched();\n  }\n  onClick() {\n    if (!this.disabled) {\n      this.focused = true;\n    }\n  }\n  preventClose(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  onSubmit(event) {\n    event.preventDefault();\n    if (this.dropdownTrigger) {\n      this.dropdownTrigger.closeMenu();\n    }\n    this.dateSource = this.transformFrom() || this.today;\n    this.onChange(this.dateSource);\n  }\n  onCancel(event) {\n    event.preventDefault();\n    if (this.dropdownTrigger) {\n      this.dropdownTrigger.closeMenu();\n    }\n  }\n  get today() {\n    const now = this.config && this.config.timeZone ? moment.tz(this.config.timeZone) : moment.utc();\n    return now.startOf('day');\n  }\n  get formattedDate() {\n    if (!this.dateSource) {\n      return '';\n    }\n    const date = this.config.timeZone ? this.dateSource.tz(this.config.timeZone) : this.dateSource;\n    return date ? date.format(this.format) : '';\n  }\n  onDisabledState(disabled) {\n    disabled ? this.form.disable() : this.form.enable();\n  }\n  isErrorState() {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n  get format() {\n    const timeFormat = this.config && this.config.timeFormat ? this.config.timeFormat : 'HH:mm:ss';\n    const dateFormat = this.config && this.config.dateFormat ? this.config.dateFormat : 'DD.MM.YYYY';\n    return this.config.disableTimepicker ? dateFormat : dateFormat + ' ' + timeFormat;\n  }\n  transformFrom() {\n    const val = this.form.getRawValue();\n    if (typeof val === 'undefined') {\n      return null;\n    }\n    const {\n      date,\n      time\n    } = val;\n    if (moment.isMoment(date) && time) {\n      date.set(time);\n    } else if (time && this.dateSource) {\n      return this.dateSource.clone().set(time);\n    }\n    return date;\n  }\n}, _SwuiDatetimepickerComponent.ctorParameters = () => [{\n  type: FocusMonitor\n}, {\n  type: ElementRef\n}, {\n  type: NgControl,\n  decorators: [{\n    type: Optional\n  }, {\n    type: Self\n  }]\n}, {\n  type: FormGroupDirective,\n  decorators: [{\n    type: Optional\n  }]\n}, {\n  type: ErrorStateMatcher\n}], _SwuiDatetimepickerComponent.propDecorators = {\n  dropdownTrigger: [{\n    type: ViewChild,\n    args: [MatMenuTrigger]\n  }],\n  minDate: [{\n    type: Input\n  }],\n  maxDate: [{\n    type: Input\n  }],\n  value: [{\n    type: Input\n  }],\n  config: [{\n    type: Input\n  }],\n  input: [{\n    type: ViewChild,\n    args: ['input']\n  }],\n  dateRef: [{\n    type: ViewChild,\n    args: ['date', {\n      read: MatMenuTrigger\n    }]\n  }],\n  id: [{\n    type: HostBinding\n  }],\n  shouldLabelFloat: [{\n    type: HostBinding,\n    args: ['class.floating']\n  }]\n}, _SwuiDatetimepickerComponent);\nSwuiDatetimepickerComponent = __decorate([Component({\n  selector: 'lib-swui-datetimepicker',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: MatFormFieldControl,\n    useExisting: SwuiDatetimepickerComponent\n  }],\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiDatetimepickerComponent);\nexport { SwuiDatetimepickerComponent };", "map": {"version": 3, "names": ["Component", "ElementRef", "HostBinding", "Input", "Optional", "Self", "ViewChild", "UntypedFormControl", "UntypedFormGroup", "FormGroupDirective", "NgControl", "FocusMonitor", "moment", "MatMenuTrigger", "MatFormFieldControl", "SwuiMatFormFieldControl", "ErrorStateMatcher", "transformValue", "value", "date", "isMoment", "parseZone", "<PERSON><PERSON><PERSON><PERSON>", "time", "hour", "hours", "minute", "minutes", "second", "seconds", "CONTROL_NAME", "nextUniqueId", "SwuiDatetimepickerComponent", "_SwuiDatetimepickerComponent", "_value", "writeValue", "stateChanges", "next", "undefined", "config", "_config", "form", "contains", "timeGroup", "get", "disable<PERSON><PERSON><PERSON><PERSON>", "disable", "enable", "empty", "formattedDate", "shouldLabelFloat", "focused", "constructor", "fm", "elRef", "ngControl", "parentFormGroup", "errorStateMatcher", "dateSource", "controlType", "id", "data", "setValue", "emitEvent", "transformFrom", "onContainerClick", "event", "dateRef", "target", "tagName", "toLowerCase", "disabled", "nativeElement", "focus", "openMenu", "onClose", "onTouched", "onClick", "preventClose", "preventDefault", "stopPropagation", "onSubmit", "dropdownTrigger", "closeMenu", "today", "onChange", "onCancel", "now", "timeZone", "tz", "utc", "startOf", "format", "onDisabledState", "isErrorState", "input", "errorState", "timeFormat", "dateFormat", "val", "getRawValue", "set", "clone", "type", "args", "read", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "provide", "useExisting", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-datetimepicker/swui-datetimepicker.component.ts"], "sourcesContent": ["import { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup, FormGroupDirective, NgControl } from '@angular/forms';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport * as moment from 'moment';\nimport { SwuiDateTimepickerConfig } from './swui-datetimepicker.interface';\nimport { SwuiTimepickerInterface } from '../swui-timepicker/swui-timepicker.interface';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { MatFormFieldControl } from '@angular/material/form-field';\nimport { MatInput } from '@angular/material/input';\nimport { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';\nimport { ErrorStateMatcher } from '@angular/material/core';\n\n\ntype DateTimePickerValue = moment.Moment | string | undefined;\n\ninterface DateTimePickerForm {\n  date: moment.Moment | null;\n  time: SwuiTimepickerInterface | null;\n}\n\nfunction transformValue( value: DateTimePickerValue | null ): DateTimePickerForm {\n  if (value !== null) {\n    const date = moment.isMoment(value) ? value : moment.parseZone(value);\n    if (date.isValid()) {\n      return {\n        date: date,\n        time: {\n          hour: date.hours(),\n          minute: date.minutes(),\n          second: date.seconds(),\n        }\n      };\n    }\n  }\n  return {\n    date: null,\n    time: null\n  };\n}\n\nconst CONTROL_NAME = 'lib-swui-datetimepicker';\nlet nextUniqueId = 0;\n\n@Component({\n    selector: 'lib-swui-datetimepicker',\n    templateUrl: './swui-datetimepicker.component.html',\n    styleUrls: ['./swui-datetimepicker.component.scss'],\n    providers: [{ provide: MatFormFieldControl, useExisting: SwuiDatetimepickerComponent }],\n    standalone: false\n})\nexport class SwuiDatetimepickerComponent extends SwuiMatFormFieldControl<DateTimePickerValue> {\n  @ViewChild(MatMenuTrigger) dropdownTrigger?: MatMenuTrigger;\n\n  @Input() minDate?: moment.Moment | string;\n  @Input() maxDate?: moment.Moment | string;\n\n  @Input()\n  get value(): DateTimePickerValue {\n    return this._value;\n  }\n\n  set value( value: DateTimePickerValue ) {\n    this._value = value;\n    this.writeValue(value);\n    this.stateChanges.next(undefined);\n  }\n\n  @Input()\n  set config( value: SwuiDateTimepickerConfig ) {\n    this._config = value;\n    if (this.form.contains('time')) {\n      const timeGroup = this.form.get('time') as UntypedFormGroup;\n      this._config.disableTimepicker ? timeGroup.disable() : timeGroup.enable();\n    }\n  }\n\n  get config(): SwuiDateTimepickerConfig {\n    return this._config;\n  }\n\n  get empty() {\n    return this.formattedDate === '';\n  }\n\n  readonly form: UntypedFormGroup;\n  dateSource: moment.Moment | null = null;\n\n  readonly controlType = CONTROL_NAME;\n\n  @ViewChild('input') input?: MatInput;\n  @ViewChild('date', { read: MatMenuTrigger }) dateRef?: MatMenuTrigger;\n\n  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;\n\n  @HostBinding('class.floating')\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n\n  private _value?: DateTimePickerValue;\n  private _config: SwuiDateTimepickerConfig;\n\n  constructor( fm: FocusMonitor,\n               elRef: ElementRef<HTMLElement>,\n               @Optional() @Self() ngControl: NgControl,\n               @Optional() parentFormGroup: FormGroupDirective,\n               errorStateMatcher: ErrorStateMatcher ) {\n    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);\n    this._config = {};\n    this.form = new UntypedFormGroup({\n      date: new UntypedFormControl(),\n      time: new UntypedFormControl()\n    });\n  }\n\n  writeValue( value: DateTimePickerValue | null | undefined ) {\n    if (typeof value === 'undefined') {\n      return;\n    }\n    const data = transformValue(value);\n    this.form.setValue(data, { emitEvent: false });\n    this.dateSource = this.transformFrom();\n  }\n\n  onContainerClick( event: Event ) {\n    if (this.dateRef && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {\n      this.elRef.nativeElement.focus();\n      this.dateRef.openMenu();\n      this.focused = true;\n    }\n  }\n\n  onClose() {\n    this.focused = false;\n    this.onTouched();\n  }\n\n  onClick() {\n    if (!this.disabled) {\n      this.focused = true;\n    }\n  }\n\n  preventClose( event: MouseEvent ) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n\n  onSubmit( event: MouseEvent ) {\n    event.preventDefault();\n    if (this.dropdownTrigger) {\n      this.dropdownTrigger.closeMenu();\n    }\n    this.dateSource = this.transformFrom() || this.today;\n    this.onChange(this.dateSource);\n  }\n\n  onCancel( event: MouseEvent ) {\n    event.preventDefault();\n    if (this.dropdownTrigger) {\n      this.dropdownTrigger.closeMenu();\n    }\n  }\n\n  get today(): moment.Moment {\n    const now = this.config && this.config.timeZone ? moment.tz(this.config.timeZone) : moment.utc();\n    return now.startOf('day');\n  }\n\n  get formattedDate(): string {\n    if (!this.dateSource) {\n      return '';\n    }\n\n    const date = this.config.timeZone\n      ? this.dateSource.tz(this.config.timeZone)\n      : this.dateSource;\n    return date ? date.format(this.format) : '';\n  }\n\n  protected onDisabledState( disabled: boolean ) {\n    disabled ? this.form.disable() : this.form.enable();\n  }\n\n  protected isErrorState(): boolean {\n    if (this.input) {\n      return this.input.errorState;\n    }\n    return false;\n  }\n\n  private get format(): string {\n    const timeFormat = this.config && this.config.timeFormat ? this.config.timeFormat : 'HH:mm:ss';\n    const dateFormat = this.config && this.config.dateFormat ? this.config.dateFormat : 'DD.MM.YYYY';\n    return this.config.disableTimepicker ? dateFormat : dateFormat + ' ' + timeFormat;\n  }\n\n  private transformFrom(): moment.Moment | null {\n    const val: DateTimePickerForm | undefined = this.form.getRawValue();\n    if (typeof val === 'undefined') {\n      return null;\n    }\n    const { date, time } = val;\n    if (moment.isMoment(date) && time) {\n      date.set(time);\n    } else if (time && this.dateSource) {\n      return this.dateSource.clone().set(time);\n    }\n    return date;\n  }\n}\n\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,eAAe;AACpG,SAASC,kBAAkB,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AACpG,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,8BAA8B;AAElE,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAU1D,SAASC,cAAcA,CAAEC,KAAiC;EACxD,IAAIA,KAAK,KAAK,IAAI,EAAE;IAClB,MAAMC,IAAI,GAAGP,MAAM,CAACQ,QAAQ,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAGN,MAAM,CAACS,SAAS,CAACH,KAAK,CAAC;IACrE,IAAIC,IAAI,CAACG,OAAO,EAAE,EAAE;MAClB,OAAO;QACLH,IAAI,EAAEA,IAAI;QACVI,IAAI,EAAE;UACJC,IAAI,EAAEL,IAAI,CAACM,KAAK,EAAE;UAClBC,MAAM,EAAEP,IAAI,CAACQ,OAAO,EAAE;UACtBC,MAAM,EAAET,IAAI,CAACU,OAAO;;OAEvB;IACH;EACF;EACA,OAAO;IACLV,IAAI,EAAE,IAAI;IACVI,IAAI,EAAE;GACP;AACH;AAEA,MAAMO,YAAY,GAAG,yBAAyB;AAC9C,IAAIC,YAAY,GAAG,CAAC;AASb,IAAMC,2BAA2B,IAAAC,4BAAA,GAAjC,MAAMD,2BAA4B,SAAQjB,uBAA4C;MAOvFG,KAAKA,CAAA;IACP,OAAO,IAAI,CAACgB,MAAM;EACpB;EAEA,IAAIhB,KAAKA,CAAEA,KAA0B;IACnC,IAAI,CAACgB,MAAM,GAAGhB,KAAK;IACnB,IAAI,CAACiB,UAAU,CAACjB,KAAK,CAAC;IACtB,IAAI,CAACkB,YAAY,CAACC,IAAI,CAACC,SAAS,CAAC;EACnC;MAGIC,MAAMA,CAAErB,KAA+B;IACzC,IAAI,CAACsB,OAAO,GAAGtB,KAAK;IACpB,IAAI,IAAI,CAACuB,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC9B,MAAMC,SAAS,GAAG,IAAI,CAACF,IAAI,CAACG,GAAG,CAAC,MAAM,CAAqB;MAC3D,IAAI,CAACJ,OAAO,CAACK,iBAAiB,GAAGF,SAAS,CAACG,OAAO,EAAE,GAAGH,SAAS,CAACI,MAAM,EAAE;IAC3E;EACF;EAEA,IAAIR,MAAMA,CAAA;IACR,OAAO,IAAI,CAACC,OAAO;EACrB;EAEA,IAAIQ,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,aAAa,KAAK,EAAE;EAClC;MAaIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACH,KAAK;EACpC;EAKAI,YAAaC,EAAgB,EAChBC,KAA8B,EACVC,SAAoB,EAC5BC,eAAmC,EAC/CC,iBAAoC;IAC/C,KAAK,CAACJ,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,CAAC;IAtBjE,KAAAC,UAAU,GAAyB,IAAI;IAE9B,KAAAC,WAAW,GAAG7B,YAAY;IAKX,KAAA8B,EAAE,GAAG,GAAG9B,YAAY,IAAIC,YAAY,EAAE,EAAE;IAgB9D,IAAI,CAACS,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,IAAI,GAAG,IAAIjC,gBAAgB,CAAC;MAC/BW,IAAI,EAAE,IAAIZ,kBAAkB,EAAE;MAC9BgB,IAAI,EAAE,IAAIhB,kBAAkB;KAC7B,CAAC;EACJ;EAEA4B,UAAUA,CAAEjB,KAA6C;IACvD,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MAChC;IACF;IACA,MAAM2C,IAAI,GAAG5C,cAAc,CAACC,KAAK,CAAC;IAClC,IAAI,CAACuB,IAAI,CAACqB,QAAQ,CAACD,IAAI,EAAE;MAAEE,SAAS,EAAE;IAAK,CAAE,CAAC;IAC9C,IAAI,CAACL,UAAU,GAAG,IAAI,CAACM,aAAa,EAAE;EACxC;EAEAC,gBAAgBA,CAAEC,KAAY;IAC5B,IAAI,IAAI,CAACC,OAAO,IAAKD,KAAK,CAACE,MAAkB,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MACjG,IAAI,CAACjB,KAAK,CAACkB,aAAa,CAACC,KAAK,EAAE;MAChC,IAAI,CAACN,OAAO,CAACO,QAAQ,EAAE;MACvB,IAAI,CAACvB,OAAO,GAAG,IAAI;IACrB;EACF;EAEAwB,OAAOA,CAAA;IACL,IAAI,CAACxB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACyB,SAAS,EAAE;EAClB;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE;MAClB,IAAI,CAACpB,OAAO,GAAG,IAAI;IACrB;EACF;EAEA2B,YAAYA,CAAEZ,KAAiB;IAC7BA,KAAK,CAACa,cAAc,EAAE;IACtBb,KAAK,CAACc,eAAe,EAAE;EACzB;EAEAC,QAAQA,CAAEf,KAAiB;IACzBA,KAAK,CAACa,cAAc,EAAE;IACtB,IAAI,IAAI,CAACG,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,SAAS,EAAE;IAClC;IACA,IAAI,CAACzB,UAAU,GAAG,IAAI,CAACM,aAAa,EAAE,IAAI,IAAI,CAACoB,KAAK;IACpD,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC3B,UAAU,CAAC;EAChC;EAEA4B,QAAQA,CAAEpB,KAAiB;IACzBA,KAAK,CAACa,cAAc,EAAE;IACtB,IAAI,IAAI,CAACG,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACC,SAAS,EAAE;IAClC;EACF;EAEA,IAAIC,KAAKA,CAAA;IACP,MAAMG,GAAG,GAAG,IAAI,CAAChD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACiD,QAAQ,GAAG5E,MAAM,CAAC6E,EAAE,CAAC,IAAI,CAAClD,MAAM,CAACiD,QAAQ,CAAC,GAAG5E,MAAM,CAAC8E,GAAG,EAAE;IAChG,OAAOH,GAAG,CAACI,OAAO,CAAC,KAAK,CAAC;EAC3B;EAEA,IAAI1C,aAAaA,CAAA;IACf,IAAI,CAAC,IAAI,CAACS,UAAU,EAAE;MACpB,OAAO,EAAE;IACX;IAEA,MAAMvC,IAAI,GAAG,IAAI,CAACoB,MAAM,CAACiD,QAAQ,GAC7B,IAAI,CAAC9B,UAAU,CAAC+B,EAAE,CAAC,IAAI,CAAClD,MAAM,CAACiD,QAAQ,CAAC,GACxC,IAAI,CAAC9B,UAAU;IACnB,OAAOvC,IAAI,GAAGA,IAAI,CAACyE,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC,GAAG,EAAE;EAC7C;EAEUC,eAAeA,CAAEtB,QAAiB;IAC1CA,QAAQ,GAAG,IAAI,CAAC9B,IAAI,CAACK,OAAO,EAAE,GAAG,IAAI,CAACL,IAAI,CAACM,MAAM,EAAE;EACrD;EAEU+C,YAAYA,CAAA;IACpB,IAAI,IAAI,CAACC,KAAK,EAAE;MACd,OAAO,IAAI,CAACA,KAAK,CAACC,UAAU;IAC9B;IACA,OAAO,KAAK;EACd;EAEA,IAAYJ,MAAMA,CAAA;IAChB,MAAMK,UAAU,GAAG,IAAI,CAAC1D,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC0D,UAAU,GAAG,IAAI,CAAC1D,MAAM,CAAC0D,UAAU,GAAG,UAAU;IAC9F,MAAMC,UAAU,GAAG,IAAI,CAAC3D,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC2D,UAAU,GAAG,IAAI,CAAC3D,MAAM,CAAC2D,UAAU,GAAG,YAAY;IAChG,OAAO,IAAI,CAAC3D,MAAM,CAACM,iBAAiB,GAAGqD,UAAU,GAAGA,UAAU,GAAG,GAAG,GAAGD,UAAU;EACnF;EAEQjC,aAAaA,CAAA;IACnB,MAAMmC,GAAG,GAAmC,IAAI,CAAC1D,IAAI,CAAC2D,WAAW,EAAE;IACnE,IAAI,OAAOD,GAAG,KAAK,WAAW,EAAE;MAC9B,OAAO,IAAI;IACb;IACA,MAAM;MAAEhF,IAAI;MAAEI;IAAI,CAAE,GAAG4E,GAAG;IAC1B,IAAIvF,MAAM,CAACQ,QAAQ,CAACD,IAAI,CAAC,IAAII,IAAI,EAAE;MACjCJ,IAAI,CAACkF,GAAG,CAAC9E,IAAI,CAAC;IAChB,CAAC,MAAM,IAAIA,IAAI,IAAI,IAAI,CAACmC,UAAU,EAAE;MAClC,OAAO,IAAI,CAACA,UAAU,CAAC4C,KAAK,EAAE,CAACD,GAAG,CAAC9E,IAAI,CAAC;IAC1C;IACA,OAAOJ,IAAI;EACb;;;;;;;;UAzGcf;EAAQ;IAAAmG,IAAA,EAAIlG;EAAI;AAAA,G;;;UAChBD;EAAQ;AAAA,G;;;;UAtDrBE,SAAS;IAAAkG,IAAA,GAAC3F,cAAc;EAAA;;UAExBV;EAAK;;UACLA;EAAK;;UAELA;EAAK;;UAWLA;EAAK;;UAsBLG,SAAS;IAAAkG,IAAA,GAAC,OAAO;EAAA;;UACjBlG,SAAS;IAAAkG,IAAA,GAAC,MAAM,EAAE;MAAEC,IAAI,EAAE5F;IAAc,CAAE;EAAA;;UAE1CX;EAAW;;UAEXA,WAAW;IAAAsG,IAAA,GAAC,gBAAgB;EAAA;;AA5ClBxE,2BAA2B,GAAA0E,UAAA,EAPvC1G,SAAS,CAAC;EACP2G,QAAQ,EAAE,yBAAyB;EACnCC,QAAA,EAAAC,oBAAmD;EAEnDC,SAAS,EAAE,CAAC;IAAEC,OAAO,EAAEjG,mBAAmB;IAAEkG,WAAW,EAAEhF;EAA2B,CAAE,CAAC;EACvFiF,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWjF,2BAA2B,CAgKvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}