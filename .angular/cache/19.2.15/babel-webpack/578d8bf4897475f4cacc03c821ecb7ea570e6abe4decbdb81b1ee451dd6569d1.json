{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiGridWidgetChooserComponent } from './widget-chooser.component';\nlet SwuiGridWidgetChooserModule = class SwuiGridWidgetChooserModule {};\nSwuiGridWidgetChooserModule = __decorate([NgModule({\n  imports: [CommonModule],\n  declarations: [SwuiGridWidgetChooserComponent],\n  exports: [SwuiGridWidgetChooserComponent]\n})], SwuiGridWidgetChooserModule);\nexport { SwuiGridWidgetChooserModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "SwuiGridWidgetChooserComponent", "SwuiGridWidgetChooserModule", "__decorate", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/widget-chooser/widget-chooser.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiGridWidgetChooserComponent } from './widget-chooser.component';\n\n\n@NgModule({\n  imports: [\n    CommonModule\n  ],\n  declarations: [\n    SwuiGridWidgetChooserComponent\n  ],\n  exports: [\n    SwuiGridWidgetChooserComponent\n  ]\n})\nexport class SwuiGridWidgetChooserModule {\n}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,8BAA8B,QAAQ,4BAA4B;AAcpE,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B,GACvC;AADYA,2BAA2B,GAAAC,UAAA,EAXvCJ,QAAQ,CAAC;EACRK,OAAO,EAAE,CACPJ,YAAY,CACb;EACDK,YAAY,EAAE,CACZJ,8BAA8B,CAC/B;EACDK,OAAO,EAAE,CACPL,8BAA8B;CAEjC,CAAC,C,EACWC,2BAA2B,CACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}