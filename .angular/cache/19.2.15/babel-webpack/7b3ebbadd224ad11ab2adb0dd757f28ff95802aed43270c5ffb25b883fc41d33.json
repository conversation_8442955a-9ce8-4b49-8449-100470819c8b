{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwDexieService;\nimport { __decorate } from \"tslib\";\nimport { Inject, Injectable, InjectionToken } from '@angular/core';\nimport <PERSON><PERSON> from 'dexie';\nimport { ReplaySubject } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';\nimport { settingsNames } from './dexie-types';\nexport const DEXI_CONFIG = new InjectionToken('SwDexieServiceConfig');\nlet SwDexieService = (_SwDexieService = class SwDexieService extends Dexie {\n  get ready() {\n    return !this.authService ? Promise.resolve({}) : new Promise(resolve => {\n      this._ready.pipe(take(1)).subscribe(() => {\n        resolve({});\n      });\n    });\n  }\n  constructor(authService, hubName) {\n    super(hubName);\n    this.authService = authService;\n    this._ready = new ReplaySubject(1);\n    try {\n      this.version(1).stores({\n        filters: '++id,name,scope,data',\n        settings: '++,name,params'\n      });\n      this.version(2).stores({\n        users: '++id, &[username+entityKey]',\n        columns: '++id, userId, schemaType, params',\n        appSettings: '++id, userId, params',\n        filters: '++id, [userId+scope], name, data',\n        loginKeys: '++id, active, key, label'\n      });\n      this.version(3).stores({\n        collapsedState: '++id, userId, componentId, data, scope, &[userId+componentId+scope]'\n      });\n      this.version(4).stores({\n        columns: '++id, [userId+schemaType], params'\n      });\n      this.version(5).stores({\n        usersNotificatedAboutNewMenu: '++id, &[username+entityKey]'\n      });\n      this.version(6).stores({\n        filterStates: '++id, &[username+entityKey+componentName], state'\n      });\n      this.version(7).stores({\n        lastVisitedPages: '++id, &[username+entityKey], url'\n      });\n      this.version(8).stores({\n        runSettings: '++id, &[username+entityKey+path], params'\n      });\n    } catch (e) {\n      console.error('SwDexieService constructor error ', e);\n    }\n    this._subscribeUser();\n  }\n  getSetting(opts) {\n    let result;\n    switch (opts.name) {\n      case 'filter':\n        result = this.getFilterState(opts === null || opts === void 0 ? void 0 : opts.component);\n        break;\n      default:\n        result = Promise.resolve();\n    }\n    return result;\n  }\n  putSetting(opts, value) {\n    let result;\n    switch (opts.name) {\n      case settingsNames.filter:\n        if (opts.component) {\n          result = this.putFilterState(opts.component, value);\n        }\n        break;\n      default:\n        result = Promise.resolve();\n    }\n    return result;\n  }\n  getColumn(schemaType) {\n    return this.ready.then(() => {\n      var _this$columns, _this$user;\n      return (_this$columns = this.columns) === null || _this$columns === void 0 ? void 0 : _this$columns.get({\n        userId: (_this$user = this.user) === null || _this$user === void 0 ? void 0 : _this$user.id,\n        schemaType\n      });\n    });\n  }\n  saveColumn(schemaType, params) {\n    return this.ready.then(() => this.getColumn(schemaType)).then(column => {\n      if (column && column.id) {\n        var _this$columns2, _this$user2;\n        return (_this$columns2 = this.columns) === null || _this$columns2 === void 0 ? void 0 : _this$columns2.update(column.id, {\n          userId: (_this$user2 = this.user) === null || _this$user2 === void 0 ? void 0 : _this$user2.id,\n          schemaType,\n          params\n        });\n      } else {\n        var _this$columns3, _this$user3;\n        return (_this$columns3 = this.columns) === null || _this$columns3 === void 0 ? void 0 : _this$columns3.add({\n          userId: ((_this$user3 = this.user) === null || _this$user3 === void 0 ? void 0 : _this$user3.id) || 0,\n          schemaType,\n          params\n        });\n      }\n    });\n  }\n  getAppSettings() {\n    return this.ready.then(() => {\n      var _this$appSettings, _this$user4;\n      return (_this$appSettings = this.appSettings) === null || _this$appSettings === void 0 ? void 0 : _this$appSettings.get({\n        userId: (_this$user4 = this.user) === null || _this$user4 === void 0 ? void 0 : _this$user4.id\n      });\n    });\n  }\n  saveAppSettings(params) {\n    return this.ready.then(() => {\n      return this.getAppSettings().then(appSettings => {\n        if (appSettings && appSettings.id) {\n          var _this$appSettings2, _this$user5;\n          return (_this$appSettings2 = this.appSettings) === null || _this$appSettings2 === void 0 ? void 0 : _this$appSettings2.update(appSettings === null || appSettings === void 0 ? void 0 : appSettings.id, {\n            userId: (_this$user5 = this.user) === null || _this$user5 === void 0 ? void 0 : _this$user5.id,\n            params\n          });\n        } else {\n          var _this$appSettings3, _this$user6;\n          return (_this$appSettings3 = this.appSettings) === null || _this$appSettings3 === void 0 ? void 0 : _this$appSettings3.add({\n            userId: ((_this$user6 = this.user) === null || _this$user6 === void 0 ? void 0 : _this$user6.id) || 0,\n            params\n          });\n        }\n      });\n    });\n  }\n  getComponentState(componentId, scope) {\n    return this.ready.then(() => {\n      var _this$collapsedState, _this$user7;\n      return (_this$collapsedState = this.collapsedState) === null || _this$collapsedState === void 0 ? void 0 : _this$collapsedState.get({\n        userId: (_this$user7 = this.user) === null || _this$user7 === void 0 ? void 0 : _this$user7.id,\n        componentId,\n        scope\n      });\n    });\n  }\n  updateComponentState(componentId, scope, data) {\n    var _this$user8, _this$collapsedState2;\n    let search = {\n      userId: ((_this$user8 = this.user) === null || _this$user8 === void 0 ? void 0 : _this$user8.id) || 0,\n      componentId,\n      scope\n    };\n    (_this$collapsedState2 = this.collapsedState) === null || _this$collapsedState2 === void 0 || _this$collapsedState2.get(search, res => {\n      var _this$collapsedState3;\n      res = Object.assign(res || search, {\n        data\n      });\n      (_this$collapsedState3 = this.collapsedState) === null || _this$collapsedState3 === void 0 || _this$collapsedState3.put(res);\n    });\n  }\n  setUserToNotificatedList() {\n    return this.ready.then(() => {\n      var _this$usersNotificate;\n      const {\n        username,\n        entityKey\n      } = this.authService;\n      const newUser = {\n        username: username,\n        entityKey: entityKey\n      };\n      return (_this$usersNotificate = this.usersNotificatedAboutNewMenu) === null || _this$usersNotificate === void 0 ? void 0 : _this$usersNotificate.add(newUser);\n    });\n  }\n  isNotUserInNotificatedList() {\n    return this.ready.then(() => {\n      var _this$usersNotificate2;\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey\n      };\n      return (_this$usersNotificate2 = this.usersNotificatedAboutNewMenu) === null || _this$usersNotificate2 === void 0 ? void 0 : _this$usersNotificate2.get(findOptions).then(user => !user);\n    });\n  }\n  getFilterState(componentName) {\n    return this.ready.then(() => {\n      var _this$filterStates;\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        componentName\n      };\n      return (_this$filterStates = this.filterStates) === null || _this$filterStates === void 0 ? void 0 : _this$filterStates.get(findOptions).then(filterState => {\n        return filterState ? JSON.parse(filterState.state) : {};\n      });\n    });\n  }\n  putFilterState(componentName, state) {\n    return this.ready.then(() => {\n      var _this$filterStates2;\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        componentName\n      };\n      const newState = JSON.stringify(state);\n      const createOptions = _objectSpread(_objectSpread({}, findOptions), {}, {\n        state: newState\n      });\n      return (_this$filterStates2 = this.filterStates) === null || _this$filterStates2 === void 0 ? void 0 : _this$filterStates2.get(findOptions).then(row => {\n        if (row && row.id) {\n          var _this$filterStates3;\n          return (_this$filterStates3 = this.filterStates) === null || _this$filterStates3 === void 0 ? void 0 : _this$filterStates3.update(row.id, {\n            state: newState\n          });\n        }\n        if (!!createOptions) {\n          var _this$filterStates4;\n          return (_this$filterStates4 = this.filterStates) === null || _this$filterStates4 === void 0 ? void 0 : _this$filterStates4.add(createOptions);\n        }\n      });\n    });\n  }\n  getRunSettings(path) {\n    return this.ready.then(() => {\n      var _this$runSettings;\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        path\n      };\n      return (_this$runSettings = this.runSettings) === null || _this$runSettings === void 0 ? void 0 : _this$runSettings.get(findOptions).then(settings => {\n        return settings ? JSON.parse(settings.params) : {};\n      });\n    });\n  }\n  putRunSettings(path, params) {\n    return this.ready.then(() => {\n      var _this$runSettings2;\n      const findOptions = {\n        username: this.authService.username,\n        entityKey: this.authService.entityKey,\n        path\n      };\n      const newParams = JSON.stringify(params);\n      const createOptions = _objectSpread(_objectSpread({}, findOptions), {}, {\n        params: newParams\n      });\n      return (_this$runSettings2 = this.runSettings) === null || _this$runSettings2 === void 0 ? void 0 : _this$runSettings2.get(findOptions).then(row => {\n        if (row && row.id) {\n          var _this$runSettings3;\n          return (_this$runSettings3 = this.runSettings) === null || _this$runSettings3 === void 0 ? void 0 : _this$runSettings3.update(row.id, {\n            params: newParams\n          });\n        }\n        if (!!createOptions) {\n          var _this$runSettings4;\n          return (_this$runSettings4 = this.runSettings) === null || _this$runSettings4 === void 0 ? void 0 : _this$runSettings4.add(createOptions);\n        }\n      });\n    });\n  }\n  _subscribeUser() {\n    this.authService.logged.asObservable().subscribe(() => {\n      if (this.authService.isLogged()) {\n        let activeKey = this.authService.entityKey;\n        if (activeKey && this.authService.username) {\n          this._getOrCreateUser(this.authService.username, activeKey);\n        }\n      }\n    });\n  }\n  _getOrCreateUser(username, entityKey) {\n    var _this$users;\n    let userObj = {\n      username,\n      entityKey\n    };\n    // @ts-ignore\n    return (_this$users = this.users) === null || _this$users === void 0 ? void 0 : _this$users.get(userObj).then(this._saveUser.bind(this, userObj)).catch(console.error.bind(console));\n  }\n  _saveUser(userObj, user) {\n    if (user) {\n      this.user = user;\n      this._ready.next(undefined);\n    } else {\n      var _this$users2;\n      (_this$users2 = this.users) === null || _this$users2 === void 0 || _this$users2.add(Object.assign({}, userObj)).then(() => {\n        var _this$users3;\n        (_this$users3 = this.users) === null || _this$users3 === void 0 || _this$users3.get(userObj).then(this._saveUser.bind(this, userObj)).catch(console.error.bind(console));\n      }).catch(console.error.bind(console));\n    }\n  }\n}, _SwDexieService.ctorParameters = () => [{\n  type: SwHubAuthService\n}, {\n  type: String,\n  decorators: [{\n    type: Inject,\n    args: [DEXI_CONFIG]\n  }]\n}], _SwDexieService);\nSwDexieService = __decorate([Injectable()], SwDexieService);\nexport { SwDexieService };", "map": {"version": 3, "names": ["__decorate", "Inject", "Injectable", "InjectionToken", "<PERSON><PERSON>", "ReplaySubject", "take", "SwHubAuthService", "settingsNames", "DEXI_CONFIG", "SwDexieService", "_SwDexieService", "ready", "authService", "Promise", "resolve", "_ready", "pipe", "subscribe", "constructor", "hubName", "version", "stores", "filters", "settings", "users", "columns", "appSettings", "loginKeys", "collapsedState", "usersNotificatedAboutNewMenu", "filterStates", "lastVisitedPages", "runSettings", "e", "console", "error", "_subscribeUser", "getSetting", "opts", "result", "name", "getFilterState", "component", "putSetting", "value", "filter", "putFilterState", "getColumn", "schemaType", "then", "_this$columns", "_this$user", "get", "userId", "user", "id", "saveColumn", "params", "column", "_this$columns2", "_this$user2", "update", "_this$columns3", "_this$user3", "add", "getAppSettings", "_this$appSettings", "_this$user4", "saveAppSettings", "_this$appSettings2", "_this$user5", "_this$appSettings3", "_this$user6", "getComponentState", "componentId", "scope", "_this$collapsedState", "_this$user7", "updateComponentState", "data", "_this$user8", "_this$collapsedState2", "search", "res", "_this$collapsedState3", "Object", "assign", "put", "setUserToNotificatedList", "_this$usersNotificate", "username", "entityKey", "newUser", "isNotUserInNotificatedList", "_this$usersNotificate2", "findOptions", "componentName", "_this$filterStates", "filterState", "JSON", "parse", "state", "_this$filterStates2", "newState", "stringify", "createOptions", "_objectSpread", "row", "_this$filterStates3", "_this$filterStates4", "getRunSettings", "path", "_this$runSettings", "putRunSettings", "_this$runSettings2", "newParams", "_this$runSettings3", "_this$runSettings4", "logged", "asObservable", "isLogged", "active<PERSON><PERSON>", "_getOrCreateUser", "_this$users", "userObj", "_saveUser", "bind", "catch", "next", "undefined", "_this$users2", "_this$users3", "ctorParameters", "type", "String", "decorators", "args"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/sw-dexie/sw-dexie.service.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Inject, Injectable, InjectionToken } from '@angular/core';\nimport <PERSON>ie from 'dexie';\nimport { ReplaySubject } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';\nimport { settingsNames } from './dexie-types';\nexport const DEXI_CONFIG = new InjectionToken('SwDexieServiceConfig');\nlet SwDexieService = class SwDexieService extends Dexie {\n    get ready() {\n        return !this.authService ? Promise.resolve({}) : new Promise((resolve) => {\n            this._ready.pipe(take(1)).subscribe(() => {\n                resolve({});\n            });\n        });\n    }\n    constructor(authService, hubName) {\n        super(hubName);\n        this.authService = authService;\n        this._ready = new ReplaySubject(1);\n        try {\n            this.version(1).stores({\n                filters: '++id,name,scope,data',\n                settings: '++,name,params',\n            });\n            this.version(2).stores({\n                users: '++id, &[username+entityKey]',\n                columns: '++id, userId, schemaType, params',\n                appSettings: '++id, userId, params',\n                filters: '++id, [userId+scope], name, data',\n                loginKeys: '++id, active, key, label',\n            });\n            this.version(3).stores({\n                collapsedState: '++id, userId, componentId, data, scope, &[userId+componentId+scope]',\n            });\n            this.version(4).stores({\n                columns: '++id, [userId+schemaType], params',\n            });\n            this.version(5).stores({\n                usersNotificatedAboutNewMenu: '++id, &[username+entityKey]',\n            });\n            this.version(6).stores({\n                filterStates: '++id, &[username+entityKey+componentName], state',\n            });\n            this.version(7).stores({\n                lastVisitedPages: '++id, &[username+entityKey], url',\n            });\n            this.version(8).stores({\n                runSettings: '++id, &[username+entityKey+path], params',\n            });\n        }\n        catch (e) {\n            console.error('SwDexieService constructor error ', e);\n        }\n        this._subscribeUser();\n    }\n    getSetting(opts) {\n        let result;\n        switch (opts.name) {\n            case 'filter':\n                result = this.getFilterState(opts?.component);\n                break;\n            default:\n                result = Promise.resolve();\n        }\n        return result;\n    }\n    putSetting(opts, value) {\n        let result;\n        switch (opts.name) {\n            case settingsNames.filter:\n                if (opts.component) {\n                    result = this.putFilterState(opts.component, value);\n                }\n                break;\n            default:\n                result = Promise.resolve();\n        }\n        return result;\n    }\n    getColumn(schemaType) {\n        return this.ready.then(() => {\n            return this.columns?.get({ userId: this.user?.id, schemaType });\n        });\n    }\n    saveColumn(schemaType, params) {\n        return this.ready\n            .then(() => this.getColumn(schemaType))\n            .then((column) => {\n            if (column && column.id) {\n                return this.columns?.update(column.id, { userId: this.user?.id, schemaType, params });\n            }\n            else {\n                return this.columns?.add({ userId: this.user?.id || 0, schemaType, params });\n            }\n        });\n    }\n    getAppSettings() {\n        return this.ready.then(() => {\n            return this.appSettings?.get({ userId: this.user?.id });\n        });\n    }\n    saveAppSettings(params) {\n        return this.ready.then(() => {\n            return this.getAppSettings().then((appSettings) => {\n                if (appSettings && appSettings.id) {\n                    return this.appSettings?.update(appSettings?.id, { userId: this.user?.id, params });\n                }\n                else {\n                    return this.appSettings?.add({ userId: this.user?.id || 0, params });\n                }\n            });\n        });\n    }\n    getComponentState(componentId, scope) {\n        return this.ready.then(() => {\n            return this.collapsedState?.get({ userId: this.user?.id, componentId, scope });\n        });\n    }\n    updateComponentState(componentId, scope, data) {\n        let search = { userId: this.user?.id || 0, componentId, scope };\n        this.collapsedState?.get(search, res => {\n            res = Object.assign(res || search, { data });\n            this.collapsedState?.put(res);\n        });\n    }\n    setUserToNotificatedList() {\n        return this.ready.then(() => {\n            const { username, entityKey } = this.authService;\n            const newUser = {\n                username: username,\n                entityKey: entityKey,\n            };\n            return this.usersNotificatedAboutNewMenu?.add(newUser);\n        });\n    }\n    isNotUserInNotificatedList() {\n        return this.ready.then(() => {\n            const findOptions = {\n                username: this.authService.username,\n                entityKey: this.authService.entityKey,\n            };\n            return this.usersNotificatedAboutNewMenu?.get(findOptions)\n                .then(user => !user);\n        });\n    }\n    getFilterState(componentName) {\n        return this.ready.then(() => {\n            const findOptions = {\n                username: this.authService.username,\n                entityKey: this.authService.entityKey,\n                componentName,\n            };\n            return this.filterStates?.get(findOptions)\n                .then(filterState => {\n                return filterState ? JSON.parse(filterState.state) : {};\n            });\n        });\n    }\n    putFilterState(componentName, state) {\n        return this.ready.then(() => {\n            const findOptions = {\n                username: this.authService.username,\n                entityKey: this.authService.entityKey,\n                componentName,\n            };\n            const newState = JSON.stringify(state);\n            const createOptions = { ...findOptions, state: newState };\n            return this.filterStates?.get(findOptions)\n                .then(row => {\n                if (row && row.id) {\n                    return this.filterStates?.update(row.id, { state: newState });\n                }\n                if (!!createOptions) {\n                    return this.filterStates?.add(createOptions);\n                }\n            });\n        });\n    }\n    getRunSettings(path) {\n        return this.ready.then(() => {\n            const findOptions = {\n                username: this.authService.username,\n                entityKey: this.authService.entityKey,\n                path,\n            };\n            return this.runSettings?.get(findOptions)\n                .then(settings => {\n                return settings ? JSON.parse(settings.params) : {};\n            });\n        });\n    }\n    putRunSettings(path, params) {\n        return this.ready.then(() => {\n            const findOptions = {\n                username: this.authService.username,\n                entityKey: this.authService.entityKey,\n                path,\n            };\n            const newParams = JSON.stringify(params);\n            const createOptions = { ...findOptions, params: newParams };\n            return this.runSettings?.get(findOptions)\n                .then(row => {\n                if (row && row.id) {\n                    return this.runSettings?.update(row.id, { params: newParams });\n                }\n                if (!!createOptions) {\n                    return this.runSettings?.add(createOptions);\n                }\n            });\n        });\n    }\n    _subscribeUser() {\n        this.authService.logged.asObservable().subscribe(() => {\n            if (this.authService.isLogged()) {\n                let activeKey = this.authService.entityKey;\n                if (activeKey && this.authService.username) {\n                    this._getOrCreateUser(this.authService.username, activeKey);\n                }\n            }\n        });\n    }\n    _getOrCreateUser(username, entityKey) {\n        let userObj = { username, entityKey };\n        // @ts-ignore\n        return this.users?.get(userObj)\n            .then(this._saveUser.bind(this, userObj))\n            .catch(console.error.bind(console));\n    }\n    _saveUser(userObj, user) {\n        if (user) {\n            this.user = user;\n            this._ready.next(undefined);\n        }\n        else {\n            this.users?.add(Object.assign({}, userObj))\n                .then(() => {\n                this.users?.get(userObj).then(this._saveUser.bind(this, userObj)).catch(console.error.bind(console));\n            })\n                .catch(console.error.bind(console));\n        }\n    }\n    static { this.ctorParameters = () => [\n        { type: SwHubAuthService },\n        { type: String, decorators: [{ type: Inject, args: [DEXI_CONFIG,] }] }\n    ]; }\n};\nSwDexieService = __decorate([\n    Injectable()\n], SwDexieService);\nexport { SwDexieService };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,MAAM,EAAEC,UAAU,EAAEC,cAAc,QAAQ,eAAe;AAClE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,MAAM;AACpC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,aAAa,QAAQ,eAAe;AAC7C,OAAO,MAAMC,WAAW,GAAG,IAAIN,cAAc,CAAC,sBAAsB,CAAC;AACrE,IAAIO,cAAc,IAAAC,eAAA,GAAG,MAAMD,cAAc,SAASN,KAAK,CAAC;EACpD,IAAIQ,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACC,WAAW,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAID,OAAO,CAAEC,OAAO,IAAK;MACtE,IAAI,CAACC,MAAM,CAACC,IAAI,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC,CAACY,SAAS,CAAC,MAAM;QACtCH,OAAO,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAI,WAAWA,CAACN,WAAW,EAAEO,OAAO,EAAE;IAC9B,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACP,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACG,MAAM,GAAG,IAAIX,aAAa,CAAC,CAAC,CAAC;IAClC,IAAI;MACA,IAAI,CAACgB,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACnBC,OAAO,EAAE,sBAAsB;QAC/BC,QAAQ,EAAE;MACd,CAAC,CAAC;MACF,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACnBG,KAAK,EAAE,6BAA6B;QACpCC,OAAO,EAAE,kCAAkC;QAC3CC,WAAW,EAAE,sBAAsB;QACnCJ,OAAO,EAAE,kCAAkC;QAC3CK,SAAS,EAAE;MACf,CAAC,CAAC;MACF,IAAI,CAACP,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACnBO,cAAc,EAAE;MACpB,CAAC,CAAC;MACF,IAAI,CAACR,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACnBI,OAAO,EAAE;MACb,CAAC,CAAC;MACF,IAAI,CAACL,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACnBQ,4BAA4B,EAAE;MAClC,CAAC,CAAC;MACF,IAAI,CAACT,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACnBS,YAAY,EAAE;MAClB,CAAC,CAAC;MACF,IAAI,CAACV,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACnBU,gBAAgB,EAAE;MACtB,CAAC,CAAC;MACF,IAAI,CAACX,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;QACnBW,WAAW,EAAE;MACjB,CAAC,CAAC;IACN,CAAC,CACD,OAAOC,CAAC,EAAE;MACNC,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEF,CAAC,CAAC;IACzD;IACA,IAAI,CAACG,cAAc,CAAC,CAAC;EACzB;EACAC,UAAUA,CAACC,IAAI,EAAE;IACb,IAAIC,MAAM;IACV,QAAQD,IAAI,CAACE,IAAI;MACb,KAAK,QAAQ;QACTD,MAAM,GAAG,IAAI,CAACE,cAAc,CAACH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,SAAS,CAAC;QAC7C;MACJ;QACIH,MAAM,GAAG1B,OAAO,CAACC,OAAO,CAAC,CAAC;IAClC;IACA,OAAOyB,MAAM;EACjB;EACAI,UAAUA,CAACL,IAAI,EAAEM,KAAK,EAAE;IACpB,IAAIL,MAAM;IACV,QAAQD,IAAI,CAACE,IAAI;MACb,KAAKjC,aAAa,CAACsC,MAAM;QACrB,IAAIP,IAAI,CAACI,SAAS,EAAE;UAChBH,MAAM,GAAG,IAAI,CAACO,cAAc,CAACR,IAAI,CAACI,SAAS,EAAEE,KAAK,CAAC;QACvD;QACA;MACJ;QACIL,MAAM,GAAG1B,OAAO,CAACC,OAAO,CAAC,CAAC;IAClC;IACA,OAAOyB,MAAM;EACjB;EACAQ,SAASA,CAACC,UAAU,EAAE;IAClB,OAAO,IAAI,CAACrC,KAAK,CAACsC,IAAI,CAAC,MAAM;MAAA,IAAAC,aAAA,EAAAC,UAAA;MACzB,QAAAD,aAAA,GAAO,IAAI,CAACzB,OAAO,cAAAyB,aAAA,uBAAZA,aAAA,CAAcE,GAAG,CAAC;QAAEC,MAAM,GAAAF,UAAA,GAAE,IAAI,CAACG,IAAI,cAAAH,UAAA,uBAATA,UAAA,CAAWI,EAAE;QAAEP;MAAW,CAAC,CAAC;IACnE,CAAC,CAAC;EACN;EACAQ,UAAUA,CAACR,UAAU,EAAES,MAAM,EAAE;IAC3B,OAAO,IAAI,CAAC9C,KAAK,CACZsC,IAAI,CAAC,MAAM,IAAI,CAACF,SAAS,CAACC,UAAU,CAAC,CAAC,CACtCC,IAAI,CAAES,MAAM,IAAK;MAClB,IAAIA,MAAM,IAAIA,MAAM,CAACH,EAAE,EAAE;QAAA,IAAAI,cAAA,EAAAC,WAAA;QACrB,QAAAD,cAAA,GAAO,IAAI,CAAClC,OAAO,cAAAkC,cAAA,uBAAZA,cAAA,CAAcE,MAAM,CAACH,MAAM,CAACH,EAAE,EAAE;UAAEF,MAAM,GAAAO,WAAA,GAAE,IAAI,CAACN,IAAI,cAAAM,WAAA,uBAATA,WAAA,CAAWL,EAAE;UAAEP,UAAU;UAAES;QAAO,CAAC,CAAC;MACzF,CAAC,MACI;QAAA,IAAAK,cAAA,EAAAC,WAAA;QACD,QAAAD,cAAA,GAAO,IAAI,CAACrC,OAAO,cAAAqC,cAAA,uBAAZA,cAAA,CAAcE,GAAG,CAAC;UAAEX,MAAM,EAAE,EAAAU,WAAA,OAAI,CAACT,IAAI,cAAAS,WAAA,uBAATA,WAAA,CAAWR,EAAE,KAAI,CAAC;UAAEP,UAAU;UAAES;QAAO,CAAC,CAAC;MAChF;IACJ,CAAC,CAAC;EACN;EACAQ,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACtD,KAAK,CAACsC,IAAI,CAAC,MAAM;MAAA,IAAAiB,iBAAA,EAAAC,WAAA;MACzB,QAAAD,iBAAA,GAAO,IAAI,CAACxC,WAAW,cAAAwC,iBAAA,uBAAhBA,iBAAA,CAAkBd,GAAG,CAAC;QAAEC,MAAM,GAAAc,WAAA,GAAE,IAAI,CAACb,IAAI,cAAAa,WAAA,uBAATA,WAAA,CAAWZ;MAAG,CAAC,CAAC;IAC3D,CAAC,CAAC;EACN;EACAa,eAAeA,CAACX,MAAM,EAAE;IACpB,OAAO,IAAI,CAAC9C,KAAK,CAACsC,IAAI,CAAC,MAAM;MACzB,OAAO,IAAI,CAACgB,cAAc,CAAC,CAAC,CAAChB,IAAI,CAAEvB,WAAW,IAAK;QAC/C,IAAIA,WAAW,IAAIA,WAAW,CAAC6B,EAAE,EAAE;UAAA,IAAAc,kBAAA,EAAAC,WAAA;UAC/B,QAAAD,kBAAA,GAAO,IAAI,CAAC3C,WAAW,cAAA2C,kBAAA,uBAAhBA,kBAAA,CAAkBR,MAAM,CAACnC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6B,EAAE,EAAE;YAAEF,MAAM,GAAAiB,WAAA,GAAE,IAAI,CAAChB,IAAI,cAAAgB,WAAA,uBAATA,WAAA,CAAWf,EAAE;YAAEE;UAAO,CAAC,CAAC;QACvF,CAAC,MACI;UAAA,IAAAc,kBAAA,EAAAC,WAAA;UACD,QAAAD,kBAAA,GAAO,IAAI,CAAC7C,WAAW,cAAA6C,kBAAA,uBAAhBA,kBAAA,CAAkBP,GAAG,CAAC;YAAEX,MAAM,EAAE,EAAAmB,WAAA,OAAI,CAAClB,IAAI,cAAAkB,WAAA,uBAATA,WAAA,CAAWjB,EAAE,KAAI,CAAC;YAAEE;UAAO,CAAC,CAAC;QACxE;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAgB,iBAAiBA,CAACC,WAAW,EAAEC,KAAK,EAAE;IAClC,OAAO,IAAI,CAAChE,KAAK,CAACsC,IAAI,CAAC,MAAM;MAAA,IAAA2B,oBAAA,EAAAC,WAAA;MACzB,QAAAD,oBAAA,GAAO,IAAI,CAAChD,cAAc,cAAAgD,oBAAA,uBAAnBA,oBAAA,CAAqBxB,GAAG,CAAC;QAAEC,MAAM,GAAAwB,WAAA,GAAE,IAAI,CAACvB,IAAI,cAAAuB,WAAA,uBAATA,WAAA,CAAWtB,EAAE;QAAEmB,WAAW;QAAEC;MAAM,CAAC,CAAC;IAClF,CAAC,CAAC;EACN;EACAG,oBAAoBA,CAACJ,WAAW,EAAEC,KAAK,EAAEI,IAAI,EAAE;IAAA,IAAAC,WAAA,EAAAC,qBAAA;IAC3C,IAAIC,MAAM,GAAG;MAAE7B,MAAM,EAAE,EAAA2B,WAAA,OAAI,CAAC1B,IAAI,cAAA0B,WAAA,uBAATA,WAAA,CAAWzB,EAAE,KAAI,CAAC;MAAEmB,WAAW;MAAEC;IAAM,CAAC;IAC/D,CAAAM,qBAAA,OAAI,CAACrD,cAAc,cAAAqD,qBAAA,eAAnBA,qBAAA,CAAqB7B,GAAG,CAAC8B,MAAM,EAAEC,GAAG,IAAI;MAAA,IAAAC,qBAAA;MACpCD,GAAG,GAAGE,MAAM,CAACC,MAAM,CAACH,GAAG,IAAID,MAAM,EAAE;QAAEH;MAAK,CAAC,CAAC;MAC5C,CAAAK,qBAAA,OAAI,CAACxD,cAAc,cAAAwD,qBAAA,eAAnBA,qBAAA,CAAqBG,GAAG,CAACJ,GAAG,CAAC;IACjC,CAAC,CAAC;EACN;EACAK,wBAAwBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAC7E,KAAK,CAACsC,IAAI,CAAC,MAAM;MAAA,IAAAwC,qBAAA;MACzB,MAAM;QAAEC,QAAQ;QAAEC;MAAU,CAAC,GAAG,IAAI,CAAC/E,WAAW;MAChD,MAAMgF,OAAO,GAAG;QACZF,QAAQ,EAAEA,QAAQ;QAClBC,SAAS,EAAEA;MACf,CAAC;MACD,QAAAF,qBAAA,GAAO,IAAI,CAAC5D,4BAA4B,cAAA4D,qBAAA,uBAAjCA,qBAAA,CAAmCzB,GAAG,CAAC4B,OAAO,CAAC;IAC1D,CAAC,CAAC;EACN;EACAC,0BAA0BA,CAAA,EAAG;IACzB,OAAO,IAAI,CAAClF,KAAK,CAACsC,IAAI,CAAC,MAAM;MAAA,IAAA6C,sBAAA;MACzB,MAAMC,WAAW,GAAG;QAChBL,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAC8E,QAAQ;QACnCC,SAAS,EAAE,IAAI,CAAC/E,WAAW,CAAC+E;MAChC,CAAC;MACD,QAAAG,sBAAA,GAAO,IAAI,CAACjE,4BAA4B,cAAAiE,sBAAA,uBAAjCA,sBAAA,CAAmC1C,GAAG,CAAC2C,WAAW,CAAC,CACrD9C,IAAI,CAACK,IAAI,IAAI,CAACA,IAAI,CAAC;IAC5B,CAAC,CAAC;EACN;EACAb,cAAcA,CAACuD,aAAa,EAAE;IAC1B,OAAO,IAAI,CAACrF,KAAK,CAACsC,IAAI,CAAC,MAAM;MAAA,IAAAgD,kBAAA;MACzB,MAAMF,WAAW,GAAG;QAChBL,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAC8E,QAAQ;QACnCC,SAAS,EAAE,IAAI,CAAC/E,WAAW,CAAC+E,SAAS;QACrCK;MACJ,CAAC;MACD,QAAAC,kBAAA,GAAO,IAAI,CAACnE,YAAY,cAAAmE,kBAAA,uBAAjBA,kBAAA,CAAmB7C,GAAG,CAAC2C,WAAW,CAAC,CACrC9C,IAAI,CAACiD,WAAW,IAAI;QACrB,OAAOA,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACF,WAAW,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC;MAC3D,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAvD,cAAcA,CAACkD,aAAa,EAAEK,KAAK,EAAE;IACjC,OAAO,IAAI,CAAC1F,KAAK,CAACsC,IAAI,CAAC,MAAM;MAAA,IAAAqD,mBAAA;MACzB,MAAMP,WAAW,GAAG;QAChBL,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAC8E,QAAQ;QACnCC,SAAS,EAAE,IAAI,CAAC/E,WAAW,CAAC+E,SAAS;QACrCK;MACJ,CAAC;MACD,MAAMO,QAAQ,GAAGJ,IAAI,CAACK,SAAS,CAACH,KAAK,CAAC;MACtC,MAAMI,aAAa,GAAAC,aAAA,CAAAA,aAAA,KAAQX,WAAW;QAAEM,KAAK,EAAEE;MAAQ,EAAE;MACzD,QAAAD,mBAAA,GAAO,IAAI,CAACxE,YAAY,cAAAwE,mBAAA,uBAAjBA,mBAAA,CAAmBlD,GAAG,CAAC2C,WAAW,CAAC,CACrC9C,IAAI,CAAC0D,GAAG,IAAI;QACb,IAAIA,GAAG,IAAIA,GAAG,CAACpD,EAAE,EAAE;UAAA,IAAAqD,mBAAA;UACf,QAAAA,mBAAA,GAAO,IAAI,CAAC9E,YAAY,cAAA8E,mBAAA,uBAAjBA,mBAAA,CAAmB/C,MAAM,CAAC8C,GAAG,CAACpD,EAAE,EAAE;YAAE8C,KAAK,EAAEE;UAAS,CAAC,CAAC;QACjE;QACA,IAAI,CAAC,CAACE,aAAa,EAAE;UAAA,IAAAI,mBAAA;UACjB,QAAAA,mBAAA,GAAO,IAAI,CAAC/E,YAAY,cAAA+E,mBAAA,uBAAjBA,mBAAA,CAAmB7C,GAAG,CAACyC,aAAa,CAAC;QAChD;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAK,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAO,IAAI,CAACpG,KAAK,CAACsC,IAAI,CAAC,MAAM;MAAA,IAAA+D,iBAAA;MACzB,MAAMjB,WAAW,GAAG;QAChBL,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAC8E,QAAQ;QACnCC,SAAS,EAAE,IAAI,CAAC/E,WAAW,CAAC+E,SAAS;QACrCoB;MACJ,CAAC;MACD,QAAAC,iBAAA,GAAO,IAAI,CAAChF,WAAW,cAAAgF,iBAAA,uBAAhBA,iBAAA,CAAkB5D,GAAG,CAAC2C,WAAW,CAAC,CACpC9C,IAAI,CAAC1B,QAAQ,IAAI;QAClB,OAAOA,QAAQ,GAAG4E,IAAI,CAACC,KAAK,CAAC7E,QAAQ,CAACkC,MAAM,CAAC,GAAG,CAAC,CAAC;MACtD,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAwD,cAAcA,CAACF,IAAI,EAAEtD,MAAM,EAAE;IACzB,OAAO,IAAI,CAAC9C,KAAK,CAACsC,IAAI,CAAC,MAAM;MAAA,IAAAiE,kBAAA;MACzB,MAAMnB,WAAW,GAAG;QAChBL,QAAQ,EAAE,IAAI,CAAC9E,WAAW,CAAC8E,QAAQ;QACnCC,SAAS,EAAE,IAAI,CAAC/E,WAAW,CAAC+E,SAAS;QACrCoB;MACJ,CAAC;MACD,MAAMI,SAAS,GAAGhB,IAAI,CAACK,SAAS,CAAC/C,MAAM,CAAC;MACxC,MAAMgD,aAAa,GAAAC,aAAA,CAAAA,aAAA,KAAQX,WAAW;QAAEtC,MAAM,EAAE0D;MAAS,EAAE;MAC3D,QAAAD,kBAAA,GAAO,IAAI,CAAClF,WAAW,cAAAkF,kBAAA,uBAAhBA,kBAAA,CAAkB9D,GAAG,CAAC2C,WAAW,CAAC,CACpC9C,IAAI,CAAC0D,GAAG,IAAI;QACb,IAAIA,GAAG,IAAIA,GAAG,CAACpD,EAAE,EAAE;UAAA,IAAA6D,kBAAA;UACf,QAAAA,kBAAA,GAAO,IAAI,CAACpF,WAAW,cAAAoF,kBAAA,uBAAhBA,kBAAA,CAAkBvD,MAAM,CAAC8C,GAAG,CAACpD,EAAE,EAAE;YAAEE,MAAM,EAAE0D;UAAU,CAAC,CAAC;QAClE;QACA,IAAI,CAAC,CAACV,aAAa,EAAE;UAAA,IAAAY,kBAAA;UACjB,QAAAA,kBAAA,GAAO,IAAI,CAACrF,WAAW,cAAAqF,kBAAA,uBAAhBA,kBAAA,CAAkBrD,GAAG,CAACyC,aAAa,CAAC;QAC/C;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACArE,cAAcA,CAAA,EAAG;IACb,IAAI,CAACxB,WAAW,CAAC0G,MAAM,CAACC,YAAY,CAAC,CAAC,CAACtG,SAAS,CAAC,MAAM;MACnD,IAAI,IAAI,CAACL,WAAW,CAAC4G,QAAQ,CAAC,CAAC,EAAE;QAC7B,IAAIC,SAAS,GAAG,IAAI,CAAC7G,WAAW,CAAC+E,SAAS;QAC1C,IAAI8B,SAAS,IAAI,IAAI,CAAC7G,WAAW,CAAC8E,QAAQ,EAAE;UACxC,IAAI,CAACgC,gBAAgB,CAAC,IAAI,CAAC9G,WAAW,CAAC8E,QAAQ,EAAE+B,SAAS,CAAC;QAC/D;MACJ;IACJ,CAAC,CAAC;EACN;EACAC,gBAAgBA,CAAChC,QAAQ,EAAEC,SAAS,EAAE;IAAA,IAAAgC,WAAA;IAClC,IAAIC,OAAO,GAAG;MAAElC,QAAQ;MAAEC;IAAU,CAAC;IACrC;IACA,QAAAgC,WAAA,GAAO,IAAI,CAACnG,KAAK,cAAAmG,WAAA,uBAAVA,WAAA,CAAYvE,GAAG,CAACwE,OAAO,CAAC,CAC1B3E,IAAI,CAAC,IAAI,CAAC4E,SAAS,CAACC,IAAI,CAAC,IAAI,EAAEF,OAAO,CAAC,CAAC,CACxCG,KAAK,CAAC7F,OAAO,CAACC,KAAK,CAAC2F,IAAI,CAAC5F,OAAO,CAAC,CAAC;EAC3C;EACA2F,SAASA,CAACD,OAAO,EAAEtE,IAAI,EAAE;IACrB,IAAIA,IAAI,EAAE;MACN,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACvC,MAAM,CAACiH,IAAI,CAACC,SAAS,CAAC;IAC/B,CAAC,MACI;MAAA,IAAAC,YAAA;MACD,CAAAA,YAAA,OAAI,CAAC1G,KAAK,cAAA0G,YAAA,eAAVA,YAAA,CAAYlE,GAAG,CAACqB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsC,OAAO,CAAC,CAAC,CACtC3E,IAAI,CAAC,MAAM;QAAA,IAAAkF,YAAA;QACZ,CAAAA,YAAA,OAAI,CAAC3G,KAAK,cAAA2G,YAAA,eAAVA,YAAA,CAAY/E,GAAG,CAACwE,OAAO,CAAC,CAAC3E,IAAI,CAAC,IAAI,CAAC4E,SAAS,CAACC,IAAI,CAAC,IAAI,EAAEF,OAAO,CAAC,CAAC,CAACG,KAAK,CAAC7F,OAAO,CAACC,KAAK,CAAC2F,IAAI,CAAC5F,OAAO,CAAC,CAAC;MACxG,CAAC,CAAC,CACG6F,KAAK,CAAC7F,OAAO,CAACC,KAAK,CAAC2F,IAAI,CAAC5F,OAAO,CAAC,CAAC;IAC3C;EACJ;AAKJ,CAAC,EAJYxB,eAAA,CAAK0H,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAE/H;AAAiB,CAAC,EAC1B;EAAE+H,IAAI,EAAEC,MAAM;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAErI,MAAM;IAAEwI,IAAI,EAAE,CAAChI,WAAW;EAAG,CAAC;AAAE,CAAC,CACzE,EAAAE,eAAA,CACJ;AACDD,cAAc,GAAGV,UAAU,CAAC,CACxBE,UAAU,CAAC,CAAC,CACf,EAAEQ,cAAc,CAAC;AAClB,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}