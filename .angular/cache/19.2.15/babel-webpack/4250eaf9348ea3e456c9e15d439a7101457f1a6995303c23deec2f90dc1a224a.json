{"ast": null, "code": "export var COMPLETE_NOTIFICATION = function () {\n  return createNotification('C', undefined, undefined);\n}();\nexport function errorNotification(error) {\n  return createNotification('E', undefined, error);\n}\nexport function nextNotification(value) {\n  return createNotification('N', value, undefined);\n}\nexport function createNotification(kind, value, error) {\n  return {\n    kind: kind,\n    value: value,\n    error: error\n  };\n}\n//# sourceMappingURL=NotificationFactories.js.map", "map": {"version": 3, "names": ["COMPLETE_NOTIFICATION", "createNotification", "undefined", "errorNotification", "error", "nextNotification", "value", "kind"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/rxjs/dist/esm5/internal/NotificationFactories.js"], "sourcesContent": ["export var COMPLETE_NOTIFICATION = (function () { return createNotification('C', undefined, undefined); })();\nexport function errorNotification(error) {\n    return createNotification('E', undefined, error);\n}\nexport function nextNotification(value) {\n    return createNotification('N', value, undefined);\n}\nexport function createNotification(kind, value, error) {\n    return {\n        kind: kind,\n        value: value,\n        error: error,\n    };\n}\n//# sourceMappingURL=NotificationFactories.js.map"], "mappings": "AAAA,OAAO,IAAIA,qBAAqB,GAAI,YAAY;EAAE,OAAOC,kBAAkB,CAAC,GAAG,EAAEC,SAAS,EAAEA,SAAS,CAAC;AAAE,CAAC,CAAE,CAAC;AAC5G,OAAO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACrC,OAAOH,kBAAkB,CAAC,GAAG,EAAEC,SAAS,EAAEE,KAAK,CAAC;AACpD;AACA,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACpC,OAAOL,kBAAkB,CAAC,GAAG,EAAEK,KAAK,EAAEJ,SAAS,CAAC;AACpD;AACA,OAAO,SAASD,kBAAkBA,CAACM,IAAI,EAAED,KAAK,EAAEF,KAAK,EAAE;EACnD,OAAO;IACHG,IAAI,EAAEA,IAAI;IACVD,KAAK,EAAEA,KAAK;IACZF,KAAK,EAAEA;EACX,CAAC;AACL;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}