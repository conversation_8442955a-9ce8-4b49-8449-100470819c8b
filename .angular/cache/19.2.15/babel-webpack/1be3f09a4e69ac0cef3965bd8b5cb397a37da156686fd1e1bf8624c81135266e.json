{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _SwuiTdStatusWidgetComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./status.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./status.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdStatusWidgetComponent = (_SwuiTdStatusWidgetComponent = class SwuiTdStatusWidgetComponent {\n  constructor({\n    row,\n    field,\n    action,\n    schema\n  }) {\n    var _schema$td, _schema$td2, _schema$td3, _schema$td4, _schema$td5, _schema$td6;\n    this.availableStatuses = [];\n    this.loading = false;\n    this.useTranslate = false;\n    this.statusMap = {};\n    this.field = field;\n    this.row = row;\n    this.action = action;\n    this.statusList = ((_schema$td = schema.td) === null || _schema$td === void 0 || (_schema$td = _schema$td.statusList) === null || _schema$td === void 0 ? void 0 : _schema$td.map(item => {\n      return _objectSpread(_objectSpread({}, item), {}, {\n        displayName: item.displayName || 'Unknown'\n      });\n    })) || [];\n    this.displayStatusList = ((_schema$td2 = schema.td) === null || _schema$td2 === void 0 || (_schema$td2 = _schema$td2.displayStatusList) === null || _schema$td2 === void 0 ? void 0 : _schema$td2.map(item => {\n      return _objectSpread(_objectSpread({}, item), {}, {\n        displayName: item.displayName || 'Unknown'\n      });\n    })) || this.statusList;\n    this.statusMap = this.displayStatusList.reduce((result, status) => _objectSpread(_objectSpread({}, result), {}, {\n      [status.code]: status\n    }), {});\n    this.colorMap = ((_schema$td3 = schema.td) === null || _schema$td3 === void 0 ? void 0 : _schema$td3.classMap) || {};\n    this.readonly = ((_schema$td4 = schema.td) === null || _schema$td4 === void 0 ? void 0 : _schema$td4.readonly) || ((_schema$td5 = schema.td) === null || _schema$td5 === void 0 ? void 0 : _schema$td5.readonlyFn) && schema.td.readonlyFn(row, schema) || false;\n    this.useTranslate = schema.td && 'useTranslate' in schema.td ? ((_schema$td6 = schema.td) === null || _schema$td6 === void 0 ? void 0 : _schema$td6.useTranslate) || false : true;\n    this.setAvailableStatuses();\n  }\n  get currentStatus() {\n    // let statusField = this.injector.get('field');\n    return this.row[this.field];\n  }\n  getStatusColor(status) {\n    return this.colorMap.hasOwnProperty(status) ? this.colorMap[status] : 'primary';\n  }\n  statusClick($event, status) {\n    $event.preventDefault();\n    this.loading = true;\n    const data = {\n      field: this.field,\n      row: this.row,\n      payload: {\n        status: status.code,\n        onCompleteFn: () => {\n          this.loading = false;\n          this.setAvailableStatuses();\n        }\n      }\n    };\n    this.action.emit(data);\n    // this.dropdown.close();\n  }\n  getStatusName(status) {\n    let statusName = 'Unknown';\n    if (status !== '' && this.statusMap.hasOwnProperty(status)) {\n      statusName = this.statusMap[status].displayName;\n    }\n    return statusName;\n  }\n  setAvailableStatuses() {\n    this.availableStatuses = this.statusList.filter(({\n      id,\n      hidden = false\n    }) => id !== this.currentStatus && !hidden);\n  }\n}, _SwuiTdStatusWidgetComponent.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [SWUI_GRID_WIDGET_CONFIG]\n  }]\n}], _SwuiTdStatusWidgetComponent);\nSwuiTdStatusWidgetComponent = __decorate([Component({\n  selector: 'lib-swui-td-status-widget',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SwuiTdStatusWidgetComponent);\nexport { SwuiTdStatusWidgetComponent };", "map": {"version": 3, "names": ["__decorate", "__NG_CLI_RESOURCE__0", "__NG_CLI_RESOURCE__1", "Component", "Inject", "SWUI_GRID_WIDGET_CONFIG", "SwuiTdStatusWidgetComponent", "_SwuiTdStatusWidgetComponent", "constructor", "row", "field", "action", "schema", "_schema$td", "_schema$td2", "_schema$td3", "_schema$td4", "_schema$td5", "_schema$td6", "availableStatuses", "loading", "useTranslate", "statusMap", "statusList", "td", "map", "item", "_objectSpread", "displayName", "displayStatusList", "reduce", "result", "status", "code", "colorMap", "classMap", "readonly", "readonlyFn", "setAvailableStatuses", "currentStatus", "getStatusColor", "hasOwnProperty", "statusClick", "$event", "preventDefault", "data", "payload", "onCompleteFn", "emit", "getStatusName", "statusName", "filter", "id", "hidden", "ctorParameters", "type", "undefined", "decorators", "args", "selector", "template", "standalone", "styles"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/td-widget/status/status.widget.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./status.widget.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./status.widget.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';\nlet SwuiTdStatusWidgetComponent = class SwuiTdStatusWidgetComponent {\n    constructor({ row, field, action, schema }) {\n        this.availableStatuses = [];\n        this.loading = false;\n        this.useTranslate = false;\n        this.statusMap = {};\n        this.field = field;\n        this.row = row;\n        this.action = action;\n        this.statusList = schema.td?.statusList?.map(item => {\n            return {\n                ...item,\n                displayName: item.displayName || 'Unknown'\n            };\n        }) || [];\n        this.displayStatusList = schema.td?.displayStatusList?.map(item => {\n            return {\n                ...item,\n                displayName: item.displayName || 'Unknown'\n            };\n        }) || this.statusList;\n        this.statusMap = this.displayStatusList.reduce((result, status) => ({ ...result, [status.code]: status }), {});\n        this.colorMap = schema.td?.classMap || {};\n        this.readonly = schema.td?.readonly || (schema.td?.readonlyFn && schema.td.readonlyFn(row, schema)) || false;\n        this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;\n        this.setAvailableStatuses();\n    }\n    get currentStatus() {\n        // let statusField = this.injector.get('field');\n        return this.row[this.field];\n    }\n    getStatusColor(status) {\n        return this.colorMap.hasOwnProperty(status) ? this.colorMap[status] : 'primary';\n    }\n    statusClick($event, status) {\n        $event.preventDefault();\n        this.loading = true;\n        const data = {\n            field: this.field,\n            row: this.row,\n            payload: {\n                status: status.code,\n                onCompleteFn: () => {\n                    this.loading = false;\n                    this.setAvailableStatuses();\n                }\n            }\n        };\n        this.action.emit(data);\n        // this.dropdown.close();\n    }\n    getStatusName(status) {\n        let statusName = 'Unknown';\n        if (status !== '' && this.statusMap.hasOwnProperty(status)) {\n            statusName = this.statusMap[status].displayName;\n        }\n        return statusName;\n    }\n    setAvailableStatuses() {\n        this.availableStatuses = this.statusList.filter(({ id, hidden = false }) => id !== this.currentStatus && !hidden);\n    }\n    static { this.ctorParameters = () => [\n        { type: undefined, decorators: [{ type: Inject, args: [SWUI_GRID_WIDGET_CONFIG,] }] }\n    ]; }\n};\nSwuiTdStatusWidgetComponent = __decorate([\n    Component({\n        selector: 'lib-swui-td-status-widget',\n        template: __NG_CLI_RESOURCE__0,\n        standalone: false,\n        styles: [__NG_CLI_RESOURCE__1]\n    })\n], SwuiTdStatusWidgetComponent);\nexport { SwuiTdStatusWidgetComponent };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,IAAIC,2BAA2B,IAAAC,4BAAA,GAAG,MAAMD,2BAA2B,CAAC;EAChEE,WAAWA,CAAC;IAAEC,GAAG;IAAEC,KAAK;IAAEC,MAAM;IAAEC;EAAO,CAAC,EAAE;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;IACxC,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACZ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACY,UAAU,GAAG,EAAAV,UAAA,GAAAD,MAAM,CAACY,EAAE,cAAAX,UAAA,gBAAAA,UAAA,GAATA,UAAA,CAAWU,UAAU,cAAAV,UAAA,uBAArBA,UAAA,CAAuBY,GAAG,CAACC,IAAI,IAAI;MACjD,OAAAC,aAAA,CAAAA,aAAA,KACOD,IAAI;QACPE,WAAW,EAAEF,IAAI,CAACE,WAAW,IAAI;MAAS;IAElD,CAAC,CAAC,KAAI,EAAE;IACR,IAAI,CAACC,iBAAiB,GAAG,EAAAf,WAAA,GAAAF,MAAM,CAACY,EAAE,cAAAV,WAAA,gBAAAA,WAAA,GAATA,WAAA,CAAWe,iBAAiB,cAAAf,WAAA,uBAA5BA,WAAA,CAA8BW,GAAG,CAACC,IAAI,IAAI;MAC/D,OAAAC,aAAA,CAAAA,aAAA,KACOD,IAAI;QACPE,WAAW,EAAEF,IAAI,CAACE,WAAW,IAAI;MAAS;IAElD,CAAC,CAAC,KAAI,IAAI,CAACL,UAAU;IACrB,IAAI,CAACD,SAAS,GAAG,IAAI,CAACO,iBAAiB,CAACC,MAAM,CAAC,CAACC,MAAM,EAAEC,MAAM,KAAAL,aAAA,CAAAA,aAAA,KAAWI,MAAM;MAAE,CAACC,MAAM,CAACC,IAAI,GAAGD;IAAM,EAAG,EAAE,CAAC,CAAC,CAAC;IAC9G,IAAI,CAACE,QAAQ,GAAG,EAAAnB,WAAA,GAAAH,MAAM,CAACY,EAAE,cAAAT,WAAA,uBAATA,WAAA,CAAWoB,QAAQ,KAAI,CAAC,CAAC;IACzC,IAAI,CAACC,QAAQ,GAAG,EAAApB,WAAA,GAAAJ,MAAM,CAACY,EAAE,cAAAR,WAAA,uBAATA,WAAA,CAAWoB,QAAQ,KAAK,EAAAnB,WAAA,GAAAL,MAAM,CAACY,EAAE,cAAAP,WAAA,uBAATA,WAAA,CAAWoB,UAAU,KAAIzB,MAAM,CAACY,EAAE,CAACa,UAAU,CAAC5B,GAAG,EAAEG,MAAM,CAAE,IAAI,KAAK;IAC5G,IAAI,CAACS,YAAY,GAAGT,MAAM,CAACY,EAAE,IAAI,cAAc,IAAIZ,MAAM,CAACY,EAAE,GAAG,EAAAN,WAAA,GAAAN,MAAM,CAACY,EAAE,cAAAN,WAAA,uBAATA,WAAA,CAAWG,YAAY,KAAI,KAAK,GAAG,IAAI;IACtG,IAAI,CAACiB,oBAAoB,CAAC,CAAC;EAC/B;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB;IACA,OAAO,IAAI,CAAC9B,GAAG,CAAC,IAAI,CAACC,KAAK,CAAC;EAC/B;EACA8B,cAAcA,CAACR,MAAM,EAAE;IACnB,OAAO,IAAI,CAACE,QAAQ,CAACO,cAAc,CAACT,MAAM,CAAC,GAAG,IAAI,CAACE,QAAQ,CAACF,MAAM,CAAC,GAAG,SAAS;EACnF;EACAU,WAAWA,CAACC,MAAM,EAAEX,MAAM,EAAE;IACxBW,MAAM,CAACC,cAAc,CAAC,CAAC;IACvB,IAAI,CAACxB,OAAO,GAAG,IAAI;IACnB,MAAMyB,IAAI,GAAG;MACTnC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBD,GAAG,EAAE,IAAI,CAACA,GAAG;MACbqC,OAAO,EAAE;QACLd,MAAM,EAAEA,MAAM,CAACC,IAAI;QACnBc,YAAY,EAAEA,CAAA,KAAM;UAChB,IAAI,CAAC3B,OAAO,GAAG,KAAK;UACpB,IAAI,CAACkB,oBAAoB,CAAC,CAAC;QAC/B;MACJ;IACJ,CAAC;IACD,IAAI,CAAC3B,MAAM,CAACqC,IAAI,CAACH,IAAI,CAAC;IACtB;EACJ;EACAI,aAAaA,CAACjB,MAAM,EAAE;IAClB,IAAIkB,UAAU,GAAG,SAAS;IAC1B,IAAIlB,MAAM,KAAK,EAAE,IAAI,IAAI,CAACV,SAAS,CAACmB,cAAc,CAACT,MAAM,CAAC,EAAE;MACxDkB,UAAU,GAAG,IAAI,CAAC5B,SAAS,CAACU,MAAM,CAAC,CAACJ,WAAW;IACnD;IACA,OAAOsB,UAAU;EACrB;EACAZ,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACnB,iBAAiB,GAAG,IAAI,CAACI,UAAU,CAAC4B,MAAM,CAAC,CAAC;MAAEC,EAAE;MAAEC,MAAM,GAAG;IAAM,CAAC,KAAKD,EAAE,KAAK,IAAI,CAACb,aAAa,IAAI,CAACc,MAAM,CAAC;EACrH;AAIJ,CAAC,EAHY9C,4BAAA,CAAK+C,cAAc,GAAG,MAAM,CACjC;EAAEC,IAAI,EAAEC,SAAS;EAAEC,UAAU,EAAE,CAAC;IAAEF,IAAI,EAAEnD,MAAM;IAAEsD,IAAI,EAAE,CAACrD,uBAAuB;EAAG,CAAC;AAAE,CAAC,CACxF,EAAAE,4BAAA,CACJ;AACDD,2BAA2B,GAAGN,UAAU,CAAC,CACrCG,SAAS,CAAC;EACNwD,QAAQ,EAAE,2BAA2B;EACrCC,QAAQ,EAAE3D,oBAAoB;EAC9B4D,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC5D,oBAAoB;AACjC,CAAC,CAAC,CACL,EAAEI,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}