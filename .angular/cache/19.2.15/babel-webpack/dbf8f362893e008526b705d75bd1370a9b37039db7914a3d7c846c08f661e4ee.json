{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiChipsAutocompleteComponent } from './swui-chips-autocomplete.component';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nexport const CHIPS_AUTOCOMPLETE_MODULES = [MatAutocompleteModule, MatButtonModule, MatChipsModule, MatIconModule, MatInputModule, ReactiveFormsModule];\nlet SwuiChipsAutocompleteModule = class SwuiChipsAutocompleteModule {};\nSwuiChipsAutocompleteModule = __decorate([NgModule({\n  declarations: [SwuiChipsAutocompleteComponent],\n  exports: [SwuiChipsAutocompleteComponent],\n  imports: [CommonModule, ...CHIPS_AUTOCOMPLETE_MODULES]\n})], SwuiChipsAutocompleteModule);\nexport { SwuiChipsAutocompleteModule };", "map": {"version": 3, "names": ["__decorate", "CommonModule", "NgModule", "ReactiveFormsModule", "SwuiChipsAutocompleteComponent", "MatAutocompleteModule", "MatIconModule", "MatInputModule", "MatButtonModule", "MatChipsModule", "CHIPS_AUTOCOMPLETE_MODULES", "SwuiChipsAutocompleteModule", "declarations", "exports", "imports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-chips-autocomplete/swui-chips-autocomplete.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SwuiChipsAutocompleteComponent } from './swui-chips-autocomplete.component';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nexport const CHIPS_AUTOCOMPLETE_MODULES = [\n    MatAutocompleteModule,\n    MatButtonModule,\n    MatChipsModule,\n    MatIconModule,\n    MatInputModule,\n    ReactiveFormsModule\n];\nlet SwuiChipsAutocompleteModule = class SwuiChipsAutocompleteModule {\n};\nSwuiChipsAutocompleteModule = __decorate([\n    NgModule({\n        declarations: [\n            SwuiChipsAutocompleteComponent\n        ],\n        exports: [\n            SwuiChipsAutocompleteComponent\n        ],\n        imports: [\n            CommonModule,\n            ...CHIPS_AUTOCOMPLETE_MODULES\n        ]\n    })\n], SwuiChipsAutocompleteModule);\nexport { SwuiChipsAutocompleteModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,8BAA8B,QAAQ,qCAAqC;AACpF,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAO,MAAMC,0BAA0B,GAAG,CACtCL,qBAAqB,EACrBG,eAAe,EACfC,cAAc,EACdH,aAAa,EACbC,cAAc,EACdJ,mBAAmB,CACtB;AACD,IAAIQ,2BAA2B,GAAG,MAAMA,2BAA2B,CAAC,EACnE;AACDA,2BAA2B,GAAGX,UAAU,CAAC,CACrCE,QAAQ,CAAC;EACLU,YAAY,EAAE,CACVR,8BAA8B,CACjC;EACDS,OAAO,EAAE,CACLT,8BAA8B,CACjC;EACDU,OAAO,EAAE,CACLb,YAAY,EACZ,GAAGS,0BAA0B;AAErC,CAAC,CAAC,CACL,EAAEC,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}