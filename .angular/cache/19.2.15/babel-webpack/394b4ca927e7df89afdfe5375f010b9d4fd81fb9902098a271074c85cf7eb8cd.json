{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { DEFAULT_PAGE_SIZE } from './app-settings';\nimport { BehaviorSubject } from 'rxjs';\nimport { Injectable } from '@angular/core';\nimport * as moment from 'moment';\nimport 'moment-timezone';\nexport const DEFAULT_SETTINGS = {\n  pageSize: DEFAULT_PAGE_SIZE,\n  currencyFormat: window.navigator.language,\n  dateFormat: 'DD.MM.YYYY',\n  timeFormat: 'HH:mm:ss',\n  timezoneName: moment.tz.guess()\n};\nlet SettingsService = class SettingsService {\n  constructor() {\n    this.settings = new BehaviorSubject(DEFAULT_SETTINGS);\n  }\n  use(settings) {\n    this.settings.next(settings);\n  }\n  get appSettings$() {\n    return this.settings.asObservable();\n  }\n  get appSettings() {\n    return this.settings.value;\n  }\n  resolve() {\n    return this.settings.value;\n  }\n};\nSettingsService = __decorate([Injectable()], SettingsService);\nexport { SettingsService };", "map": {"version": 3, "names": ["DEFAULT_PAGE_SIZE", "BehaviorSubject", "Injectable", "moment", "DEFAULT_SETTINGS", "pageSize", "currencyFormat", "window", "navigator", "language", "dateFormat", "timeFormat", "timezoneName", "tz", "guess", "SettingsService", "constructor", "settings", "use", "next", "appSettings$", "asObservable", "appSettings", "value", "resolve", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/settings/settings.service.ts"], "sourcesContent": ["import { AppSettings, DEFAULT_PAGE_SIZE } from './app-settings';\n\nimport { BehaviorSubject } from 'rxjs';\nimport { Injectable } from '@angular/core';\nimport * as moment from 'moment';\nimport 'moment-timezone';\n\nexport const DEFAULT_SETTINGS: AppSettings = {\n  pageSize: DEFAULT_PAGE_SIZE,\n  currencyFormat: window.navigator.language,\n  dateFormat: 'DD.MM.YYYY',\n  timeFormat: 'HH:mm:ss',\n  timezoneName: moment.tz.guess()\n};\n\n@Injectable()\nexport class SettingsService  {\n  settings: BehaviorSubject<AppSettings> = new BehaviorSubject<AppSettings>(DEFAULT_SETTINGS);\n\n  use( settings: AppSettings ) {\n    this.settings.next(settings);\n  }\n\n  get appSettings$() {\n    return this.settings.asObservable();\n  }\n\n  get appSettings() {\n    return this.settings.value;\n  }\n\n  resolve(): AppSettings {\n    return this.settings.value;\n  }\n}\n"], "mappings": ";AAAA,SAAsBA,iBAAiB,QAAQ,gBAAgB;AAE/D,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,UAAU,QAAQ,eAAe;AAC1C,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,OAAO,iBAAiB;AAExB,OAAO,MAAMC,gBAAgB,GAAgB;EAC3CC,QAAQ,EAAEL,iBAAiB;EAC3BM,cAAc,EAAEC,MAAM,CAACC,SAAS,CAACC,QAAQ;EACzCC,UAAU,EAAE,YAAY;EACxBC,UAAU,EAAE,UAAU;EACtBC,YAAY,EAAET,MAAM,CAACU,EAAE,CAACC,KAAK;CAC9B;AAGM,IAAMC,eAAe,GAArB,MAAMA,eAAe;EAArBC,YAAA;IACL,KAAAC,QAAQ,GAAiC,IAAIhB,eAAe,CAAcG,gBAAgB,CAAC;EAiB7F;EAfEc,GAAGA,CAAED,QAAqB;IACxB,IAAI,CAACA,QAAQ,CAACE,IAAI,CAACF,QAAQ,CAAC;EAC9B;EAEA,IAAIG,YAAYA,CAAA;IACd,OAAO,IAAI,CAACH,QAAQ,CAACI,YAAY,EAAE;EACrC;EAEA,IAAIC,WAAWA,CAAA;IACb,OAAO,IAAI,CAACL,QAAQ,CAACM,KAAK;EAC5B;EAEAC,OAAOA,CAAA;IACL,OAAO,IAAI,CAACP,QAAQ,CAACM,KAAK;EAC5B;CACD;AAlBYR,eAAe,GAAAU,UAAA,EAD3BvB,UAAU,EAAE,C,EACAa,eAAe,CAkB3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}