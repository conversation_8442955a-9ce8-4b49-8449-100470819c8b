{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiMatCalendarComponent } from './swui-mat-calendar.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nexport const MAT_CALENDAR_MODULES = [MatSelectModule, MatFormFieldModule, ReactiveFormsModule];\nlet SwuiMatCalendarModule = class SwuiMatCalendarModule {};\nSwuiMatCalendarModule = __decorate([NgModule({\n  declarations: [SwuiMatCalendarComponent],\n  imports: [CommonModule, ...MAT_CALENDAR_MODULES],\n  exports: [SwuiMatCalendarComponent]\n})], SwuiMatCalendarModule);\nexport { SwuiMatCalendarModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "SwuiMatCalendarComponent", "ReactiveFormsModule", "MatSelectModule", "MatFormFieldModule", "MAT_CALENDAR_MODULES", "SwuiMatCalendarModule", "declarations", "imports", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-mat-calendar/swui-mat-calendar.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwuiMatCalendarComponent } from './swui-mat-calendar.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nexport const MAT_CALENDAR_MODULES = [\n    MatSelectModule,\n    MatFormFieldModule,\n    ReactiveFormsModule,\n];\nlet SwuiMatCalendarModule = class SwuiMatCalendarModule {\n};\nSwuiMatCalendarModule = __decorate([\n    NgModule({\n        declarations: [SwuiMatCalendarComponent],\n        imports: [\n            CommonModule,\n            ...MAT_CALENDAR_MODULES,\n        ],\n        exports: [\n            SwuiMatCalendarComponent,\n        ]\n    })\n], SwuiMatCalendarModule);\nexport { SwuiMatCalendarModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,OAAO,MAAMC,oBAAoB,GAAG,CAChCF,eAAe,EACfC,kBAAkB,EAClBF,mBAAmB,CACtB;AACD,IAAII,qBAAqB,GAAG,MAAMA,qBAAqB,CAAC,EACvD;AACDA,qBAAqB,GAAGR,UAAU,CAAC,CAC/BC,QAAQ,CAAC;EACLQ,YAAY,EAAE,CAACN,wBAAwB,CAAC;EACxCO,OAAO,EAAE,CACLR,YAAY,EACZ,GAAGK,oBAAoB,CAC1B;EACDI,OAAO,EAAE,CACLR,wBAAwB;AAEhC,CAAC,CAAC,CACL,EAAEK,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}