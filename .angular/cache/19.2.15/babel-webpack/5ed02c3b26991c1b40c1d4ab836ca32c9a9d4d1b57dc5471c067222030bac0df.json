{"ast": null, "code": "import { DefaultWidgetComponent } from '../default-widget/default-widget.component';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../swui-grid.model';\nfunction getProvider(config) {\n  return [{\n    provide: SWUI_GRID_WIDGET_CONFIG,\n    useValue: config\n  }];\n}\nexport class WidgetRegistry {\n  constructor(registryType) {\n    this.registryType = registryType;\n    this.configs = {};\n  }\n  register(configName, component, providerFn) {\n    this.configs[configName] = {\n      component,\n      fn: providerFn || getProvider\n    };\n  }\n  registerWidgetsList(list, registryType) {\n    if (registryType) {\n      this.registryType = registryType;\n    }\n    list.forEach(item => {\n      const {\n        type,\n        component,\n        providerFn\n      } = item;\n      this.register(type, component, providerFn);\n    });\n  }\n  getWidgetRegistryConfig(scope, schema) {\n    var _schema$scope;\n    const type = ((_schema$scope = schema[scope]) === null || _schema$scope === void 0 ? void 0 : _schema$scope.type) || schema.type || 'string';\n    const name = scope + type;\n    return this.configs[name] || {\n      component: DefaultWidgetComponent,\n      fn: getProvider\n    };\n  }\n}", "map": {"version": 3, "names": ["DefaultWidgetComponent", "SWUI_GRID_WIDGET_CONFIG", "get<PERSON><PERSON><PERSON>", "config", "provide", "useValue", "WidgetRegistry", "constructor", "registryType", "configs", "register", "config<PERSON><PERSON>", "component", "providerFn", "fn", "registerWidgetsList", "list", "for<PERSON>ach", "item", "type", "getWidgetRegistryConfig", "scope", "schema", "_schema$scope", "name"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/registry/registry.ts"], "sourcesContent": ["import { DefaultWidgetComponent } from '../default-widget/default-widget.component';\nimport { SWUI_GRID_WIDGET_CONFIG } from '../swui-grid.model';\nfunction getProvider(config) {\n    return [{\n            provide: SWUI_GRID_WIDGET_CONFIG,\n            useValue: config\n        }];\n}\nexport class WidgetRegistry {\n    constructor(registryType) {\n        this.registryType = registryType;\n        this.configs = {};\n    }\n    register(configName, component, providerFn) {\n        this.configs[configName] = { component, fn: providerFn || getProvider };\n    }\n    registerWidgetsList(list, registryType) {\n        if (registryType) {\n            this.registryType = registryType;\n        }\n        list.forEach(item => {\n            const { type, component, providerFn } = item;\n            this.register(type, component, providerFn);\n        });\n    }\n    getWidgetRegistryConfig(scope, schema) {\n        const type = schema[scope]?.type || schema.type || 'string';\n        const name = scope + type;\n        return this.configs[name] || {\n            component: DefaultWidgetComponent,\n            fn: getProvider,\n        };\n    }\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,4CAA4C;AACnF,SAASC,uBAAuB,QAAQ,oBAAoB;AAC5D,SAASC,WAAWA,CAACC,MAAM,EAAE;EACzB,OAAO,CAAC;IACAC,OAAO,EAAEH,uBAAuB;IAChCI,QAAQ,EAAEF;EACd,CAAC,CAAC;AACV;AACA,OAAO,MAAMG,cAAc,CAAC;EACxBC,WAAWA,CAACC,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACrB;EACAC,QAAQA,CAACC,UAAU,EAAEC,SAAS,EAAEC,UAAU,EAAE;IACxC,IAAI,CAACJ,OAAO,CAACE,UAAU,CAAC,GAAG;MAAEC,SAAS;MAAEE,EAAE,EAAED,UAAU,IAAIX;IAAY,CAAC;EAC3E;EACAa,mBAAmBA,CAACC,IAAI,EAAER,YAAY,EAAE;IACpC,IAAIA,YAAY,EAAE;MACd,IAAI,CAACA,YAAY,GAAGA,YAAY;IACpC;IACAQ,IAAI,CAACC,OAAO,CAACC,IAAI,IAAI;MACjB,MAAM;QAAEC,IAAI;QAAEP,SAAS;QAAEC;MAAW,CAAC,GAAGK,IAAI;MAC5C,IAAI,CAACR,QAAQ,CAACS,IAAI,EAAEP,SAAS,EAAEC,UAAU,CAAC;IAC9C,CAAC,CAAC;EACN;EACAO,uBAAuBA,CAACC,KAAK,EAAEC,MAAM,EAAE;IAAA,IAAAC,aAAA;IACnC,MAAMJ,IAAI,GAAG,EAAAI,aAAA,GAAAD,MAAM,CAACD,KAAK,CAAC,cAAAE,aAAA,uBAAbA,aAAA,CAAeJ,IAAI,KAAIG,MAAM,CAACH,IAAI,IAAI,QAAQ;IAC3D,MAAMK,IAAI,GAAGH,KAAK,GAAGF,IAAI;IACzB,OAAO,IAAI,CAACV,OAAO,CAACe,IAAI,CAAC,IAAI;MACzBZ,SAAS,EAAEZ,sBAAsB;MACjCc,EAAE,EAAEZ;IACR,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}