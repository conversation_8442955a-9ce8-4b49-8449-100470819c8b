{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { CURRENCY_SYMBOLS_MAP } from '../constants';\nlet SwuiConstantsService = class SwuiConstantsService {\n  static currencies() {\n    return CURRENCY_SYMBOLS_MAP;\n  }\n  static currencySymbol(value) {\n    return CURRENCY_SYMBOLS_MAP[value] || value;\n  }\n};\nSwuiConstantsService = __decorate([Injectable()], SwuiConstantsService);\nexport { SwuiConstantsService };", "map": {"version": 3, "names": ["Injectable", "CURRENCY_SYMBOLS_MAP", "SwuiConstantsService", "currencies", "currencySymbol", "value", "__decorate"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/services/swui-constants.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CURRENCY_SYMBOLS_MAP } from '../constants';\n\n@Injectable()\nexport class SwuiConstantsService {\n\n  static currencies(): { [key: string]: string } {\n    return CURRENCY_SYMBOLS_MAP;\n  }\n\n  static currencySymbol(value: string): string | undefined {\n    return CURRENCY_SYMBOLS_MAP[value] || value;\n  }\n\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,oBAAoB,QAAQ,cAAc;AAG5C,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAE/B,OAAOC,UAAUA,CAAA;IACf,OAAOF,oBAAoB;EAC7B;EAEA,OAAOG,cAAcA,CAACC,KAAa;IACjC,OAAOJ,oBAAoB,CAACI,KAAK,CAAC,IAAIA,KAAK;EAC7C;CAED;AAVYH,oBAAoB,GAAAI,UAAA,EADhCN,UAAU,EAAE,C,EACAE,oBAAoB,CAUhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}