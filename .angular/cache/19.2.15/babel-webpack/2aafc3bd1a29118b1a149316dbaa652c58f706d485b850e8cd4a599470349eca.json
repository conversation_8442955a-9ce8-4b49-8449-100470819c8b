{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { SelectOnClickDirective } from './select-on-click.directive';\nlet SelectOnClickModule = class SelectOnClickModule {};\nSelectOnClickModule = __decorate([NgModule({\n  imports: [],\n  exports: [SelectOnClickDirective],\n  declarations: [SelectOnClickDirective],\n  providers: []\n})], SelectOnClickModule);\nexport { SelectOnClickModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "SelectOnClickDirective", "SelectOnClickModule", "imports", "exports", "declarations", "providers"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/directives/selectOnClick/select-on-click.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { SelectOnClickDirective } from './select-on-click.directive';\nlet SelectOnClickModule = class SelectOnClickModule {\n};\nSelectOnClickModule = __decorate([\n    NgModule({\n        imports: [],\n        exports: [SelectOnClickDirective],\n        declarations: [SelectOnClickDirective],\n        providers: [],\n    })\n], SelectOnClickModule);\nexport { SelectOnClickModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,IAAIC,mBAAmB,GAAG,MAAMA,mBAAmB,CAAC,EACnD;AACDA,mBAAmB,GAAGH,UAAU,CAAC,CAC7BC,QAAQ,CAAC;EACLG,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,CAACH,sBAAsB,CAAC;EACjCI,YAAY,EAAE,CAACJ,sBAAsB,CAAC;EACtCK,SAAS,EAAE;AACf,CAAC,CAAC,CACL,EAAEJ,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}