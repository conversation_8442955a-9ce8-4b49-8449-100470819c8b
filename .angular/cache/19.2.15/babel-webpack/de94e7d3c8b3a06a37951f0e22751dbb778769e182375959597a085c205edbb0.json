{"ast": null, "code": "'use strict';\n\n/**\n * @license Angular v<unknown>\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\nvar _defineProperty = require(\"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/defineProperty.js\").default;\nvar _objectSpread = require(\"/Users/<USER>/Projects/sw-ubo-library-common/node_modules/@babel/runtime/helpers/objectSpread2.js\").default;\nfunction patchJasmine(Zone) {\n  Zone.__load_patch('jasmine', (global, Zone, api) => {\n    const __extends = function (d, b) {\n      for (const p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n      function __() {\n        this.constructor = d;\n      }\n      d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n    // Patch jasmine's describe/it/beforeEach/afterEach functions so test code always runs\n    // in a testZone (ProxyZone). (See: angular/zone.js#91 & angular/angular#10503)\n    if (!Zone) throw new Error('Missing: zone.js');\n    if (typeof jest !== 'undefined') {\n      // return if jasmine is a light implementation inside jest\n      // in this case, we are running inside jest not jasmine\n      return;\n    }\n    if (typeof jasmine == 'undefined' || jasmine['__zone_patch__']) {\n      return;\n    }\n    jasmine['__zone_patch__'] = true;\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    if (!SyncTestZoneSpec) throw new Error('Missing: SyncTestZoneSpec');\n    if (!ProxyZoneSpec) throw new Error('Missing: ProxyZoneSpec');\n    const ambientZone = Zone.current;\n    const symbol = Zone.__symbol__;\n    // whether patch jasmine clock when in fakeAsync\n    const disablePatchingJasmineClock = global[symbol('fakeAsyncDisablePatchingClock')] === true;\n    // the original variable name fakeAsyncPatchLock is not accurate, so the name will be\n    // fakeAsyncAutoFakeAsyncWhenClockPatched and if this enablePatchingJasmineClock is false, we\n    // also automatically disable the auto jump into fakeAsync feature\n    const enableAutoFakeAsyncWhenClockPatched = !disablePatchingJasmineClock && (global[symbol('fakeAsyncPatchLock')] === true || global[symbol('fakeAsyncAutoFakeAsyncWhenClockPatched')] === true);\n    const ignoreUnhandledRejection = global[symbol('ignoreUnhandledRejection')] === true;\n    if (!ignoreUnhandledRejection) {\n      const globalErrors = jasmine.GlobalErrors;\n      if (globalErrors && !jasmine[symbol('GlobalErrors')]) {\n        jasmine[symbol('GlobalErrors')] = globalErrors;\n        jasmine.GlobalErrors = function () {\n          const instance = new globalErrors();\n          const originalInstall = instance.install;\n          if (originalInstall && !instance[symbol('install')]) {\n            instance[symbol('install')] = originalInstall;\n            instance.install = function () {\n              const isNode = typeof process !== 'undefined' && !!process.on;\n              // Note: Jasmine checks internally if `process` and `process.on` is defined.\n              // Otherwise, it installs the browser rejection handler through the\n              // `global.addEventListener`. This code may be run in the browser environment where\n              // `process` is not defined, and this will lead to a runtime exception since webpack 5\n              // removed automatic Node.js polyfills. Note, that events are named differently, it's\n              // `unhandledRejection` in Node.js and `unhandledrejection` in the browser.\n              const originalHandlers = isNode ? process.listeners('unhandledRejection') : global.eventListeners('unhandledrejection');\n              const result = originalInstall.apply(this, arguments);\n              isNode ? process.removeAllListeners('unhandledRejection') : global.removeAllListeners('unhandledrejection');\n              if (originalHandlers) {\n                originalHandlers.forEach(handler => {\n                  if (isNode) {\n                    process.on('unhandledRejection', handler);\n                  } else {\n                    global.addEventListener('unhandledrejection', handler);\n                  }\n                });\n              }\n              return result;\n            };\n          }\n          return instance;\n        };\n      }\n    }\n    // Monkey patch all of the jasmine DSL so that each function runs in appropriate zone.\n    const jasmineEnv = jasmine.getEnv();\n    ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n      let originalJasmineFn = jasmineEnv[methodName];\n      jasmineEnv[methodName] = function (description, specDefinitions) {\n        return originalJasmineFn.call(this, description, wrapDescribeInZone(description, specDefinitions));\n      };\n    });\n    ['it', 'xit', 'fit'].forEach(methodName => {\n      let originalJasmineFn = jasmineEnv[methodName];\n      jasmineEnv[symbol(methodName)] = originalJasmineFn;\n      jasmineEnv[methodName] = function (description, specDefinitions, timeout) {\n        arguments[1] = wrapTestInZone(specDefinitions);\n        return originalJasmineFn.apply(this, arguments);\n      };\n    });\n    ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n      let originalJasmineFn = jasmineEnv[methodName];\n      jasmineEnv[symbol(methodName)] = originalJasmineFn;\n      jasmineEnv[methodName] = function (specDefinitions, timeout) {\n        arguments[0] = wrapTestInZone(specDefinitions);\n        return originalJasmineFn.apply(this, arguments);\n      };\n    });\n    if (!disablePatchingJasmineClock) {\n      // need to patch jasmine.clock().mockDate and jasmine.clock().tick() so\n      // they can work properly in FakeAsyncTest\n      const originalClockFn = jasmine[symbol('clock')] = jasmine['clock'];\n      jasmine['clock'] = function () {\n        const clock = originalClockFn.apply(this, arguments);\n        if (!clock[symbol('patched')]) {\n          clock[symbol('patched')] = symbol('patched');\n          const originalTick = clock[symbol('tick')] = clock.tick;\n          clock.tick = function () {\n            const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n            if (fakeAsyncZoneSpec) {\n              return fakeAsyncZoneSpec.tick.apply(fakeAsyncZoneSpec, arguments);\n            }\n            return originalTick.apply(this, arguments);\n          };\n          const originalMockDate = clock[symbol('mockDate')] = clock.mockDate;\n          clock.mockDate = function () {\n            const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n            if (fakeAsyncZoneSpec) {\n              const dateTime = arguments.length > 0 ? arguments[0] : new Date();\n              return fakeAsyncZoneSpec.setFakeBaseSystemTime.apply(fakeAsyncZoneSpec, dateTime && typeof dateTime.getTime === 'function' ? [dateTime.getTime()] : arguments);\n            }\n            return originalMockDate.apply(this, arguments);\n          };\n          // for auto go into fakeAsync feature, we need the flag to enable it\n          if (enableAutoFakeAsyncWhenClockPatched) {\n            ['install', 'uninstall'].forEach(methodName => {\n              const originalClockFn = clock[symbol(methodName)] = clock[methodName];\n              clock[methodName] = function () {\n                const FakeAsyncTestZoneSpec = Zone['FakeAsyncTestZoneSpec'];\n                if (FakeAsyncTestZoneSpec) {\n                  jasmine[symbol('clockInstalled')] = 'install' === methodName;\n                  return;\n                }\n                return originalClockFn.apply(this, arguments);\n              };\n            });\n          }\n        }\n        return clock;\n      };\n    }\n    // monkey patch createSpyObj to make properties enumerable to true\n    if (!jasmine[Zone.__symbol__('createSpyObj')]) {\n      const originalCreateSpyObj = jasmine.createSpyObj;\n      jasmine[Zone.__symbol__('createSpyObj')] = originalCreateSpyObj;\n      jasmine.createSpyObj = function () {\n        const args = Array.prototype.slice.call(arguments);\n        const propertyNames = args.length >= 3 ? args[2] : null;\n        let spyObj;\n        if (propertyNames) {\n          const defineProperty = Object.defineProperty;\n          Object.defineProperty = function (obj, p, attributes) {\n            return defineProperty.call(this, obj, p, _objectSpread(_objectSpread({}, attributes), {}, {\n              configurable: true,\n              enumerable: true\n            }));\n          };\n          try {\n            spyObj = originalCreateSpyObj.apply(this, args);\n          } finally {\n            Object.defineProperty = defineProperty;\n          }\n        } else {\n          spyObj = originalCreateSpyObj.apply(this, args);\n        }\n        return spyObj;\n      };\n    }\n    /**\n     * Gets a function wrapping the body of a Jasmine `describe` block to execute in a\n     * synchronous-only zone.\n     */\n    function wrapDescribeInZone(description, describeBody) {\n      return function () {\n        // Create a synchronous-only zone in which to run `describe` blocks in order to raise an\n        // error if any asynchronous operations are attempted inside of a `describe`.\n        const syncZone = ambientZone.fork(new SyncTestZoneSpec(`jasmine.describe#${description}`));\n        return syncZone.run(describeBody, this, arguments);\n      };\n    }\n    function runInTestZone(testBody, applyThis, queueRunner, done) {\n      const isClockInstalled = !!jasmine[symbol('clockInstalled')];\n      queueRunner.testProxyZoneSpec;\n      const testProxyZone = queueRunner.testProxyZone;\n      if (isClockInstalled && enableAutoFakeAsyncWhenClockPatched) {\n        // auto run a fakeAsync\n        const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n        if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n          testBody = fakeAsyncModule.fakeAsync(testBody);\n        }\n      }\n      if (done) {\n        return testProxyZone.run(testBody, applyThis, [done]);\n      } else {\n        return testProxyZone.run(testBody, applyThis);\n      }\n    }\n    /**\n     * Gets a function wrapping the body of a Jasmine `it/beforeEach/afterEach` block to\n     * execute in a ProxyZone zone.\n     * This will run in `testProxyZone`. The `testProxyZone` will be reset by the `ZoneQueueRunner`\n     */\n    function wrapTestInZone(testBody) {\n      // The `done` callback is only passed through if the function expects at least one argument.\n      // Note we have to make a function with correct number of arguments, otherwise jasmine will\n      // think that all functions are sync or async.\n      return testBody && (testBody.length ? function (done) {\n        return runInTestZone(testBody, this, this.queueRunner, done);\n      } : function () {\n        return runInTestZone(testBody, this, this.queueRunner);\n      });\n    }\n    const QueueRunner = jasmine.QueueRunner;\n    jasmine.QueueRunner = function (_super) {\n      __extends(ZoneQueueRunner, _super);\n      function ZoneQueueRunner(attrs) {\n        if (attrs.onComplete) {\n          attrs.onComplete = (fn => () => {\n            // All functions are done, clear the test zone.\n            this.testProxyZone = null;\n            this.testProxyZoneSpec = null;\n            ambientZone.scheduleMicroTask('jasmine.onComplete', fn);\n          })(attrs.onComplete);\n        }\n        const nativeSetTimeout = global[Zone.__symbol__('setTimeout')];\n        const nativeClearTimeout = global[Zone.__symbol__('clearTimeout')];\n        if (nativeSetTimeout) {\n          // should run setTimeout inside jasmine outside of zone\n          attrs.timeout = {\n            setTimeout: nativeSetTimeout ? nativeSetTimeout : global.setTimeout,\n            clearTimeout: nativeClearTimeout ? nativeClearTimeout : global.clearTimeout\n          };\n        }\n        // create a userContext to hold the queueRunner itself\n        // so we can access the testProxy in it/xit/beforeEach ...\n        if (jasmine.UserContext) {\n          if (!attrs.userContext) {\n            attrs.userContext = new jasmine.UserContext();\n          }\n          attrs.userContext.queueRunner = this;\n        } else {\n          if (!attrs.userContext) {\n            attrs.userContext = {};\n          }\n          attrs.userContext.queueRunner = this;\n        }\n        // patch attrs.onException\n        const onException = attrs.onException;\n        attrs.onException = function (error) {\n          if (error && error.message === 'Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL.') {\n            // jasmine timeout, we can make the error message more\n            // reasonable to tell what tasks are pending\n            const proxyZoneSpec = this && this.testProxyZoneSpec;\n            if (proxyZoneSpec) {\n              const pendingTasksInfo = proxyZoneSpec.getAndClearPendingTasksInfo();\n              try {\n                // try catch here in case error.message is not writable\n                error.message += pendingTasksInfo;\n              } catch (err) {}\n            }\n          }\n          if (onException) {\n            onException.call(this, error);\n          }\n        };\n        _super.call(this, attrs);\n      }\n      ZoneQueueRunner.prototype.execute = function () {\n        let zone = Zone.current;\n        let isChildOfAmbientZone = false;\n        while (zone) {\n          if (zone === ambientZone) {\n            isChildOfAmbientZone = true;\n            break;\n          }\n          zone = zone.parent;\n        }\n        if (!isChildOfAmbientZone) throw new Error('Unexpected Zone: ' + Zone.current.name);\n        // This is the zone which will be used for running individual tests.\n        // It will be a proxy zone, so that the tests function can retroactively install\n        // different zones.\n        // Example:\n        //   - In beforeEach() do childZone = Zone.current.fork(...);\n        //   - In it() try to do fakeAsync(). The issue is that because the beforeEach forked the\n        //     zone outside of fakeAsync it will be able to escape the fakeAsync rules.\n        //   - Because ProxyZone is parent fo `childZone` fakeAsync can retroactively add\n        //     fakeAsync behavior to the childZone.\n        this.testProxyZoneSpec = new ProxyZoneSpec();\n        this.testProxyZone = ambientZone.fork(this.testProxyZoneSpec);\n        if (!Zone.currentTask) {\n          // if we are not running in a task then if someone would register a\n          // element.addEventListener and then calling element.click() the\n          // addEventListener callback would think that it is the top most task and would\n          // drain the microtask queue on element.click() which would be incorrect.\n          // For this reason we always force a task when running jasmine tests.\n          Zone.current.scheduleMicroTask('jasmine.execute().forceTask', () => QueueRunner.prototype.execute.call(this));\n        } else {\n          _super.prototype.execute.call(this);\n        }\n      };\n      return ZoneQueueRunner;\n    }(QueueRunner);\n  });\n}\nfunction patchJest(Zone) {\n  Zone.__load_patch('jest', (context, Zone, api) => {\n    if (typeof jest === 'undefined' || jest['__zone_patch__']) {\n      return;\n    }\n    // From jest 29 and jest-preset-angular v13, the module transform logic\n    // changed, and now jest-preset-angular use the use the tsconfig target\n    // other than the hardcoded one, https://github.com/thymikee/jest-preset-angular/issues/2010\n    // But jest-angular-preset doesn't introduce the @babel/plugin-transform-async-to-generator\n    // which is needed by angular since `async/await` still need to be transformed\n    // to promise for ES2017+ target.\n    // So for now, we disable to output the uncaught error console log for a temp solution,\n    // until jest-preset-angular find a proper solution.\n    Zone[api.symbol('ignoreConsoleErrorUncaughtError')] = true;\n    jest['__zone_patch__'] = true;\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    if (!ProxyZoneSpec) {\n      throw new Error('Missing ProxyZoneSpec');\n    }\n    const rootZone = Zone.current;\n    const syncZone = rootZone.fork(new SyncTestZoneSpec('jest.describe'));\n    const proxyZoneSpec = new ProxyZoneSpec();\n    const proxyZone = rootZone.fork(proxyZoneSpec);\n    function wrapDescribeFactoryInZone(originalJestFn) {\n      return function (...tableArgs) {\n        const originalDescribeFn = originalJestFn.apply(this, tableArgs);\n        return function (...args) {\n          args[1] = wrapDescribeInZone(args[1]);\n          return originalDescribeFn.apply(this, args);\n        };\n      };\n    }\n    function wrapTestFactoryInZone(originalJestFn) {\n      return function (...tableArgs) {\n        return function (...args) {\n          args[1] = wrapTestInZone(args[1]);\n          return originalJestFn.apply(this, tableArgs).apply(this, args);\n        };\n      };\n    }\n    /**\n     * Gets a function wrapping the body of a jest `describe` block to execute in a\n     * synchronous-only zone.\n     */\n    function wrapDescribeInZone(describeBody) {\n      return function (...args) {\n        return syncZone.run(describeBody, this, args);\n      };\n    }\n    /**\n     * Gets a function wrapping the body of a jest `it/beforeEach/afterEach` block to\n     * execute in a ProxyZone zone.\n     * This will run in the `proxyZone`.\n     */\n    function wrapTestInZone(testBody, isTestFunc = false) {\n      if (typeof testBody !== 'function') {\n        return testBody;\n      }\n      const wrappedFunc = function () {\n        if (Zone[api.symbol('useFakeTimersCalled')] === true && testBody && !testBody.isFakeAsync) {\n          // jest.useFakeTimers is called, run into fakeAsyncTest automatically.\n          const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n          if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n            testBody = fakeAsyncModule.fakeAsync(testBody);\n          }\n        }\n        proxyZoneSpec.isTestFunc = isTestFunc;\n        return proxyZone.run(testBody, null, arguments);\n      };\n      // Update the length of wrappedFunc to be the same as the length of the testBody\n      // So jest core can handle whether the test function has `done()` or not correctly\n      Object.defineProperty(wrappedFunc, 'length', {\n        configurable: true,\n        writable: true,\n        enumerable: false\n      });\n      wrappedFunc.length = testBody.length;\n      return wrappedFunc;\n    }\n    ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n      let originalJestFn = context[methodName];\n      if (context[Zone.__symbol__(methodName)]) {\n        return;\n      }\n      context[Zone.__symbol__(methodName)] = originalJestFn;\n      context[methodName] = function (...args) {\n        args[1] = wrapDescribeInZone(args[1]);\n        return originalJestFn.apply(this, args);\n      };\n      context[methodName].each = wrapDescribeFactoryInZone(originalJestFn.each);\n    });\n    context.describe.only = context.fdescribe;\n    context.describe.skip = context.xdescribe;\n    ['it', 'xit', 'fit', 'test', 'xtest'].forEach(methodName => {\n      let originalJestFn = context[methodName];\n      if (context[Zone.__symbol__(methodName)]) {\n        return;\n      }\n      context[Zone.__symbol__(methodName)] = originalJestFn;\n      context[methodName] = function (...args) {\n        args[1] = wrapTestInZone(args[1], true);\n        return originalJestFn.apply(this, args);\n      };\n      context[methodName].each = wrapTestFactoryInZone(originalJestFn.each);\n      context[methodName].todo = originalJestFn.todo;\n      context[methodName].failing = originalJestFn.failing;\n    });\n    context.it.only = context.fit;\n    context.it.skip = context.xit;\n    context.test.only = context.fit;\n    context.test.skip = context.xit;\n    ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n      let originalJestFn = context[methodName];\n      if (context[Zone.__symbol__(methodName)]) {\n        return;\n      }\n      context[Zone.__symbol__(methodName)] = originalJestFn;\n      context[methodName] = function (...args) {\n        args[0] = wrapTestInZone(args[0]);\n        return originalJestFn.apply(this, args);\n      };\n    });\n    Zone.patchJestObject = function patchJestObject(Timer, isModern = false) {\n      // check whether currently the test is inside fakeAsync()\n      function isPatchingFakeTimer() {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        return !!fakeAsyncZoneSpec;\n      }\n      // check whether the current function is inside `test/it` or other methods\n      // such as `describe/beforeEach`\n      function isInTestFunc() {\n        const proxyZoneSpec = Zone.current.get('ProxyZoneSpec');\n        return proxyZoneSpec && proxyZoneSpec.isTestFunc;\n      }\n      if (Timer[api.symbol('fakeTimers')]) {\n        return;\n      }\n      Timer[api.symbol('fakeTimers')] = true;\n      // patch jest fakeTimer internal method to make sure no console.warn print out\n      api.patchMethod(Timer, '_checkFakeTimers', delegate => {\n        return function (self, args) {\n          if (isPatchingFakeTimer()) {\n            return true;\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch useFakeTimers(), set useFakeTimersCalled flag, and make test auto run into fakeAsync\n      api.patchMethod(Timer, 'useFakeTimers', delegate => {\n        return function (self, args) {\n          Zone[api.symbol('useFakeTimersCalled')] = true;\n          if (isModern || isInTestFunc()) {\n            return delegate.apply(self, args);\n          }\n          return self;\n        };\n      });\n      // patch useRealTimers(), unset useFakeTimers flag\n      api.patchMethod(Timer, 'useRealTimers', delegate => {\n        return function (self, args) {\n          Zone[api.symbol('useFakeTimersCalled')] = false;\n          if (isModern || isInTestFunc()) {\n            return delegate.apply(self, args);\n          }\n          return self;\n        };\n      });\n      // patch setSystemTime(), call setCurrentRealTime() in the fakeAsyncTest\n      api.patchMethod(Timer, 'setSystemTime', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n            fakeAsyncZoneSpec.setFakeBaseSystemTime(args[0]);\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch getSystemTime(), call getCurrentRealTime() in the fakeAsyncTest\n      api.patchMethod(Timer, 'getRealSystemTime', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n            return fakeAsyncZoneSpec.getRealSystemTime();\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch runAllTicks(), run all microTasks inside fakeAsync\n      api.patchMethod(Timer, 'runAllTicks', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.flushMicrotasks();\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch runAllTimers(), run all macroTasks inside fakeAsync\n      api.patchMethod(Timer, 'runAllTimers', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.flush(100, true);\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch advanceTimersByTime(), call tick() in the fakeAsyncTest\n      api.patchMethod(Timer, 'advanceTimersByTime', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.tick(args[0]);\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch runOnlyPendingTimers(), call flushOnlyPendingTimers() in the fakeAsyncTest\n      api.patchMethod(Timer, 'runOnlyPendingTimers', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.flushOnlyPendingTimers();\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch advanceTimersToNextTimer(), call tickToNext() in the fakeAsyncTest\n      api.patchMethod(Timer, 'advanceTimersToNextTimer', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.tickToNext(args[0]);\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch clearAllTimers(), call removeAllTimers() in the fakeAsyncTest\n      api.patchMethod(Timer, 'clearAllTimers', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            fakeAsyncZoneSpec.removeAllTimers();\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n      // patch getTimerCount(), call getTimerCount() in the fakeAsyncTest\n      api.patchMethod(Timer, 'getTimerCount', delegate => {\n        return function (self, args) {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            return fakeAsyncZoneSpec.getTimerCount();\n          } else {\n            return delegate.apply(self, args);\n          }\n        };\n      });\n    };\n  });\n}\nfunction patchMocha(Zone) {\n  Zone.__load_patch('mocha', (global, Zone) => {\n    const Mocha = global.Mocha;\n    if (typeof Mocha === 'undefined') {\n      // return if Mocha is not available, because now zone-testing\n      // will load mocha patch with jasmine/jest patch\n      return;\n    }\n    if (typeof Zone === 'undefined') {\n      throw new Error('Missing Zone.js');\n    }\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    if (!ProxyZoneSpec) {\n      throw new Error('Missing ProxyZoneSpec');\n    }\n    if (Mocha['__zone_patch__']) {\n      throw new Error('\"Mocha\" has already been patched with \"Zone\".');\n    }\n    Mocha['__zone_patch__'] = true;\n    const rootZone = Zone.current;\n    const syncZone = rootZone.fork(new SyncTestZoneSpec('Mocha.describe'));\n    let testZone = null;\n    const suiteZone = rootZone.fork(new ProxyZoneSpec());\n    const mochaOriginal = {\n      after: global.after,\n      afterEach: global.afterEach,\n      before: global.before,\n      beforeEach: global.beforeEach,\n      describe: global.describe,\n      it: global.it\n    };\n    function modifyArguments(args, syncTest, asyncTest) {\n      for (let i = 0; i < args.length; i++) {\n        let arg = args[i];\n        if (typeof arg === 'function') {\n          // The `done` callback is only passed through if the function expects at\n          // least one argument.\n          // Note we have to make a function with correct number of arguments,\n          // otherwise mocha will\n          // think that all functions are sync or async.\n          args[i] = arg.length === 0 ? syncTest(arg) : asyncTest(arg);\n          // Mocha uses toString to view the test body in the result list, make sure we return the\n          // correct function body\n          args[i].toString = function () {\n            return arg.toString();\n          };\n        }\n      }\n      return args;\n    }\n    function wrapDescribeInZone(args) {\n      const syncTest = function (fn) {\n        return function () {\n          return syncZone.run(fn, this, arguments);\n        };\n      };\n      return modifyArguments(args, syncTest);\n    }\n    function wrapTestInZone(args) {\n      const asyncTest = function (fn) {\n        return function (done) {\n          return testZone.run(fn, this, [done]);\n        };\n      };\n      const syncTest = function (fn) {\n        return function () {\n          return testZone.run(fn, this);\n        };\n      };\n      return modifyArguments(args, syncTest, asyncTest);\n    }\n    function wrapSuiteInZone(args) {\n      const asyncTest = function (fn) {\n        return function (done) {\n          return suiteZone.run(fn, this, [done]);\n        };\n      };\n      const syncTest = function (fn) {\n        return function () {\n          return suiteZone.run(fn, this);\n        };\n      };\n      return modifyArguments(args, syncTest, asyncTest);\n    }\n    global.describe = global.suite = function () {\n      return mochaOriginal.describe.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.xdescribe = global.suite.skip = global.describe.skip = function () {\n      return mochaOriginal.describe.skip.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.describe.only = global.suite.only = function () {\n      return mochaOriginal.describe.only.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.it = global.specify = global.test = function () {\n      return mochaOriginal.it.apply(this, wrapTestInZone(arguments));\n    };\n    global.xit = global.xspecify = global.it.skip = function () {\n      return mochaOriginal.it.skip.apply(this, wrapTestInZone(arguments));\n    };\n    global.it.only = global.test.only = function () {\n      return mochaOriginal.it.only.apply(this, wrapTestInZone(arguments));\n    };\n    global.after = global.suiteTeardown = function () {\n      return mochaOriginal.after.apply(this, wrapSuiteInZone(arguments));\n    };\n    global.afterEach = global.teardown = function () {\n      return mochaOriginal.afterEach.apply(this, wrapTestInZone(arguments));\n    };\n    global.before = global.suiteSetup = function () {\n      return mochaOriginal.before.apply(this, wrapSuiteInZone(arguments));\n    };\n    global.beforeEach = global.setup = function () {\n      return mochaOriginal.beforeEach.apply(this, wrapTestInZone(arguments));\n    };\n    ((originalRunTest, originalRun) => {\n      Mocha.Runner.prototype.runTest = function (fn) {\n        Zone.current.scheduleMicroTask('mocha.forceTask', () => {\n          originalRunTest.call(this, fn);\n        });\n      };\n      Mocha.Runner.prototype.run = function (fn) {\n        this.on('test', e => {\n          testZone = rootZone.fork(new ProxyZoneSpec());\n        });\n        this.on('fail', (test, err) => {\n          const proxyZoneSpec = testZone && testZone.get('ProxyZoneSpec');\n          if (proxyZoneSpec && err) {\n            try {\n              // try catch here in case err.message is not writable\n              err.message += proxyZoneSpec.getAndClearPendingTasksInfo();\n            } catch (error) {}\n          }\n        });\n        return originalRun.call(this, fn);\n      };\n    })(Mocha.Runner.prototype.runTest, Mocha.Runner.prototype.run);\n  });\n}\nconst global$2 = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n  const symbolPrefix = global$2['__Zone_symbol_prefix'] || '__zone_symbol__';\n  return symbolPrefix + name;\n}\nconst __global = typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global;\nclass AsyncTestZoneSpec {\n  // Needs to be a getter and not a plain property in order run this just-in-time. Otherwise\n  // `__symbol__` would be evaluated during top-level execution prior to the Zone prefix being\n  // changed for tests.\n  static get symbolParentUnresolved() {\n    return __symbol__('parentUnresolved');\n  }\n  constructor(finishCallback, failCallback, namePrefix) {\n    _defineProperty(this, \"finishCallback\", void 0);\n    _defineProperty(this, \"failCallback\", void 0);\n    _defineProperty(this, \"_pendingMicroTasks\", false);\n    _defineProperty(this, \"_pendingMacroTasks\", false);\n    _defineProperty(this, \"_alreadyErrored\", false);\n    _defineProperty(this, \"_isSync\", false);\n    _defineProperty(this, \"_existingFinishTimer\", null);\n    _defineProperty(this, \"entryFunction\", null);\n    _defineProperty(this, \"runZone\", Zone.current);\n    _defineProperty(this, \"unresolvedChainedPromiseCount\", 0);\n    _defineProperty(this, \"supportWaitUnresolvedChainedPromise\", false);\n    // ZoneSpec implementation below.\n    _defineProperty(this, \"name\", void 0);\n    _defineProperty(this, \"properties\", void 0);\n    this.finishCallback = finishCallback;\n    this.failCallback = failCallback;\n    this.name = 'asyncTestZone for ' + namePrefix;\n    this.properties = {\n      'AsyncTestZoneSpec': this\n    };\n    this.supportWaitUnresolvedChainedPromise = __global[__symbol__('supportWaitUnResolvedChainedPromise')] === true;\n  }\n  isUnresolvedChainedPromisePending() {\n    return this.unresolvedChainedPromiseCount > 0;\n  }\n  _finishCallbackIfDone() {\n    // NOTE: Technically the `onHasTask` could fire together with the initial synchronous\n    // completion in `onInvoke`. `onHasTask` might call this method when it captured e.g.\n    // microtasks in the proxy zone that now complete as part of this async zone run.\n    // Consider the following scenario:\n    //    1. A test `beforeEach` schedules a microtask in the ProxyZone.\n    //    2. An actual empty `it` spec executes in the AsyncTestZone` (using e.g. `waitForAsync`).\n    //    3. The `onInvoke` invokes `_finishCallbackIfDone` because the spec runs synchronously.\n    //    4. We wait the scheduled timeout (see below) to account for unhandled promises.\n    //    5. The microtask from (1) finishes and `onHasTask` is invoked.\n    //    --> We register a second `_finishCallbackIfDone` even though we have scheduled a timeout.\n    // If the finish timeout from below is already scheduled, terminate the existing scheduled\n    // finish invocation, avoiding calling `jasmine` `done` multiple times. *Note* that we would\n    // want to schedule a new finish callback in case the task state changes again.\n    if (this._existingFinishTimer !== null) {\n      clearTimeout(this._existingFinishTimer);\n      this._existingFinishTimer = null;\n    }\n    if (!(this._pendingMicroTasks || this._pendingMacroTasks || this.supportWaitUnresolvedChainedPromise && this.isUnresolvedChainedPromisePending())) {\n      // We wait until the next tick because we would like to catch unhandled promises which could\n      // cause test logic to be executed. In such cases we cannot finish with tasks pending then.\n      this.runZone.run(() => {\n        this._existingFinishTimer = setTimeout(() => {\n          if (!this._alreadyErrored && !(this._pendingMicroTasks || this._pendingMacroTasks)) {\n            this.finishCallback();\n          }\n        }, 0);\n      });\n    }\n  }\n  patchPromiseForTest() {\n    if (!this.supportWaitUnresolvedChainedPromise) {\n      return;\n    }\n    const patchPromiseForTest = Promise[Zone.__symbol__('patchPromiseForTest')];\n    if (patchPromiseForTest) {\n      patchPromiseForTest();\n    }\n  }\n  unPatchPromiseForTest() {\n    if (!this.supportWaitUnresolvedChainedPromise) {\n      return;\n    }\n    const unPatchPromiseForTest = Promise[Zone.__symbol__('unPatchPromiseForTest')];\n    if (unPatchPromiseForTest) {\n      unPatchPromiseForTest();\n    }\n  }\n  onScheduleTask(delegate, current, target, task) {\n    if (task.type !== 'eventTask') {\n      this._isSync = false;\n    }\n    if (task.type === 'microTask' && task.data && task.data instanceof Promise) {\n      // check whether the promise is a chained promise\n      if (task.data[AsyncTestZoneSpec.symbolParentUnresolved] === true) {\n        // chained promise is being scheduled\n        this.unresolvedChainedPromiseCount--;\n      }\n    }\n    return delegate.scheduleTask(target, task);\n  }\n  onInvokeTask(delegate, current, target, task, applyThis, applyArgs) {\n    if (task.type !== 'eventTask') {\n      this._isSync = false;\n    }\n    return delegate.invokeTask(target, task, applyThis, applyArgs);\n  }\n  onCancelTask(delegate, current, target, task) {\n    if (task.type !== 'eventTask') {\n      this._isSync = false;\n    }\n    return delegate.cancelTask(target, task);\n  }\n  // Note - we need to use onInvoke at the moment to call finish when a test is\n  // fully synchronous. TODO(juliemr): remove this when the logic for\n  // onHasTask changes and it calls whenever the task queues are dirty.\n  // updated by(JiaLiPassion), only call finish callback when no task\n  // was scheduled/invoked/canceled.\n  onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n    if (!this.entryFunction) {\n      this.entryFunction = delegate;\n    }\n    try {\n      this._isSync = true;\n      return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n    } finally {\n      // We need to check the delegate is the same as entryFunction or not.\n      // Consider the following case.\n      //\n      // asyncTestZone.run(() => { // Here the delegate will be the entryFunction\n      //   Zone.current.run(() => { // Here the delegate will not be the entryFunction\n      //   });\n      // });\n      //\n      // We only want to check whether there are async tasks scheduled\n      // for the entry function.\n      if (this._isSync && this.entryFunction === delegate) {\n        this._finishCallbackIfDone();\n      }\n    }\n  }\n  onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n    // Let the parent try to handle the error.\n    const result = parentZoneDelegate.handleError(targetZone, error);\n    if (result) {\n      this.failCallback(error);\n      this._alreadyErrored = true;\n    }\n    return false;\n  }\n  onHasTask(delegate, current, target, hasTaskState) {\n    delegate.hasTask(target, hasTaskState);\n    // We should only trigger finishCallback when the target zone is the AsyncTestZone\n    // Consider the following cases.\n    //\n    // const childZone = asyncTestZone.fork({\n    //   name: 'child',\n    //   onHasTask: ...\n    // });\n    //\n    // So we have nested zones declared the onHasTask hook, in this case,\n    // the onHasTask will be triggered twice, and cause the finishCallbackIfDone()\n    // is also be invoked twice. So we need to only trigger the finishCallbackIfDone()\n    // when the current zone is the same as the target zone.\n    if (current !== target) {\n      return;\n    }\n    if (hasTaskState.change == 'microTask') {\n      this._pendingMicroTasks = hasTaskState.microTask;\n      this._finishCallbackIfDone();\n    } else if (hasTaskState.change == 'macroTask') {\n      this._pendingMacroTasks = hasTaskState.macroTask;\n      this._finishCallbackIfDone();\n    }\n  }\n}\nfunction patchAsyncTest(Zone) {\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['AsyncTestZoneSpec'] = AsyncTestZoneSpec;\n  Zone.__load_patch('asynctest', (global, Zone, api) => {\n    /**\n     * Wraps a test function in an asynchronous test zone. The test will automatically\n     * complete when all asynchronous calls within this zone are done.\n     */\n    Zone[api.symbol('asyncTest')] = function asyncTest(fn) {\n      // If we're running using the Jasmine test framework, adapt to call the 'done'\n      // function when asynchronous activity is finished.\n      if (global.jasmine) {\n        // Not using an arrow function to preserve context passed from call site\n        return function (done) {\n          if (!done) {\n            // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n            // fake it here and assume sync.\n            done = function () {};\n            done.fail = function (e) {\n              throw e;\n            };\n          }\n          runInTestZone(fn, this, done, err => {\n            if (typeof err === 'string') {\n              return done.fail(new Error(err));\n            } else {\n              done.fail(err);\n            }\n          });\n        };\n      }\n      // Otherwise, return a promise which will resolve when asynchronous activity\n      // is finished. This will be correctly consumed by the Mocha framework with\n      // it('...', async(myFn)); or can be used in a custom framework.\n      // Not using an arrow function to preserve context passed from call site\n      return function () {\n        return new Promise((finishCallback, failCallback) => {\n          runInTestZone(fn, this, finishCallback, failCallback);\n        });\n      };\n    };\n    function runInTestZone(fn, context, finishCallback, failCallback) {\n      const currentZone = Zone.current;\n      const AsyncTestZoneSpec = Zone['AsyncTestZoneSpec'];\n      if (AsyncTestZoneSpec === undefined) {\n        throw new Error('AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/async-test');\n      }\n      const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n      if (!ProxyZoneSpec) {\n        throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/proxy');\n      }\n      const proxyZoneSpec = ProxyZoneSpec.get();\n      ProxyZoneSpec.assertPresent();\n      // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n      // If we do it in ProxyZone then we will get to infinite recursion.\n      const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n      const previousDelegate = proxyZoneSpec.getDelegate();\n      proxyZone.parent.run(() => {\n        const testZoneSpec = new AsyncTestZoneSpec(() => {\n          // Need to restore the original zone.\n          if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n            // Only reset the zone spec if it's\n            // still this one. Otherwise, assume\n            // it's OK.\n            proxyZoneSpec.setDelegate(previousDelegate);\n          }\n          testZoneSpec.unPatchPromiseForTest();\n          currentZone.run(() => {\n            finishCallback();\n          });\n        }, error => {\n          // Need to restore the original zone.\n          if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n            // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n            proxyZoneSpec.setDelegate(previousDelegate);\n          }\n          testZoneSpec.unPatchPromiseForTest();\n          currentZone.run(() => {\n            failCallback(error);\n          });\n        }, 'test');\n        proxyZoneSpec.setDelegate(testZoneSpec);\n        testZoneSpec.patchPromiseForTest();\n      });\n      return Zone.current.runGuarded(fn, context);\n    }\n  });\n}\nconst global$1 = typeof window === 'object' && window || typeof self === 'object' && self || globalThis.global;\nconst OriginalDate = global$1.Date;\n// Since when we compile this file to `es2015`, and if we define\n// this `FakeDate` as `class FakeDate`, and then set `FakeDate.prototype`\n// there will be an error which is `Cannot assign to read only property 'prototype'`\n// so we need to use function implementation here.\nfunction FakeDate() {\n  if (arguments.length === 0) {\n    const d = new OriginalDate();\n    d.setTime(FakeDate.now());\n    return d;\n  } else {\n    const args = Array.prototype.slice.call(arguments);\n    return new OriginalDate(...args);\n  }\n}\nFakeDate.now = function () {\n  const fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n  if (fakeAsyncTestZoneSpec) {\n    return fakeAsyncTestZoneSpec.getFakeSystemTime();\n  }\n  return OriginalDate.now.apply(this, arguments);\n};\nFakeDate.UTC = OriginalDate.UTC;\nFakeDate.parse = OriginalDate.parse;\n// keep a reference for zone patched timer function\nlet patchedTimers;\nconst timeoutCallback = function () {};\nclass Scheduler {\n  constructor() {\n    // Scheduler queue with the tuple of end time and callback function - sorted by end time.\n    _defineProperty(this, \"_schedulerQueue\", []);\n    // Current simulated time in millis.\n    _defineProperty(this, \"_currentTickTime\", 0);\n    // Current fake system base time in millis.\n    _defineProperty(this, \"_currentFakeBaseSystemTime\", OriginalDate.now());\n    // track requeuePeriodicTimer\n    _defineProperty(this, \"_currentTickRequeuePeriodicEntries\", []);\n  }\n  static getNextId() {\n    const id = patchedTimers.nativeSetTimeout.call(global$1, timeoutCallback, 0);\n    patchedTimers.nativeClearTimeout.call(global$1, id);\n    if (typeof id === 'number') {\n      return id;\n    }\n    // in NodeJS, we just use a number for fakeAsync, since it will not\n    // conflict with native TimeoutId\n    return Scheduler.nextNodeJSId++;\n  }\n  getCurrentTickTime() {\n    return this._currentTickTime;\n  }\n  getFakeSystemTime() {\n    return this._currentFakeBaseSystemTime + this._currentTickTime;\n  }\n  setFakeBaseSystemTime(fakeBaseSystemTime) {\n    this._currentFakeBaseSystemTime = fakeBaseSystemTime;\n  }\n  getRealSystemTime() {\n    return OriginalDate.now();\n  }\n  scheduleFunction(cb, delay, options) {\n    options = _objectSpread(_objectSpread({}, {\n      args: [],\n      isPeriodic: false,\n      isRequestAnimationFrame: false,\n      id: -1,\n      isRequeuePeriodic: false\n    }), options);\n    let currentId = options.id < 0 ? Scheduler.nextId : options.id;\n    Scheduler.nextId = Scheduler.getNextId();\n    let endTime = this._currentTickTime + delay;\n    // Insert so that scheduler queue remains sorted by end time.\n    let newEntry = {\n      endTime: endTime,\n      id: currentId,\n      func: cb,\n      args: options.args,\n      delay: delay,\n      isPeriodic: options.isPeriodic,\n      isRequestAnimationFrame: options.isRequestAnimationFrame\n    };\n    if (options.isRequeuePeriodic) {\n      this._currentTickRequeuePeriodicEntries.push(newEntry);\n    }\n    let i = 0;\n    for (; i < this._schedulerQueue.length; i++) {\n      let currentEntry = this._schedulerQueue[i];\n      if (newEntry.endTime < currentEntry.endTime) {\n        break;\n      }\n    }\n    this._schedulerQueue.splice(i, 0, newEntry);\n    return currentId;\n  }\n  removeScheduledFunctionWithId(id) {\n    for (let i = 0; i < this._schedulerQueue.length; i++) {\n      if (this._schedulerQueue[i].id == id) {\n        this._schedulerQueue.splice(i, 1);\n        break;\n      }\n    }\n  }\n  removeAll() {\n    this._schedulerQueue = [];\n  }\n  getTimerCount() {\n    return this._schedulerQueue.length;\n  }\n  tickToNext(step = 1, doTick, tickOptions) {\n    if (this._schedulerQueue.length < step) {\n      return;\n    }\n    // Find the last task currently queued in the scheduler queue and tick\n    // till that time.\n    const startTime = this._currentTickTime;\n    const targetTask = this._schedulerQueue[step - 1];\n    this.tick(targetTask.endTime - startTime, doTick, tickOptions);\n  }\n  tick(millis = 0, doTick, tickOptions) {\n    let finalTime = this._currentTickTime + millis;\n    let lastCurrentTime = 0;\n    tickOptions = Object.assign({\n      processNewMacroTasksSynchronously: true\n    }, tickOptions);\n    // we need to copy the schedulerQueue so nested timeout\n    // will not be wrongly called in the current tick\n    // https://github.com/angular/angular/issues/33799\n    const schedulerQueue = tickOptions.processNewMacroTasksSynchronously ? this._schedulerQueue : this._schedulerQueue.slice();\n    if (schedulerQueue.length === 0 && doTick) {\n      doTick(millis);\n      return;\n    }\n    while (schedulerQueue.length > 0) {\n      // clear requeueEntries before each loop\n      this._currentTickRequeuePeriodicEntries = [];\n      let current = schedulerQueue[0];\n      if (finalTime < current.endTime) {\n        // Done processing the queue since it's sorted by endTime.\n        break;\n      } else {\n        // Time to run scheduled function. Remove it from the head of queue.\n        let current = schedulerQueue.shift();\n        if (!tickOptions.processNewMacroTasksSynchronously) {\n          const idx = this._schedulerQueue.indexOf(current);\n          if (idx >= 0) {\n            this._schedulerQueue.splice(idx, 1);\n          }\n        }\n        lastCurrentTime = this._currentTickTime;\n        this._currentTickTime = current.endTime;\n        if (doTick) {\n          doTick(this._currentTickTime - lastCurrentTime);\n        }\n        let retval = current.func.apply(global$1, current.isRequestAnimationFrame ? [this._currentTickTime] : current.args);\n        if (!retval) {\n          // Uncaught exception in the current scheduled function. Stop processing the queue.\n          break;\n        }\n        // check is there any requeue periodic entry is added in\n        // current loop, if there is, we need to add to current loop\n        if (!tickOptions.processNewMacroTasksSynchronously) {\n          this._currentTickRequeuePeriodicEntries.forEach(newEntry => {\n            let i = 0;\n            for (; i < schedulerQueue.length; i++) {\n              const currentEntry = schedulerQueue[i];\n              if (newEntry.endTime < currentEntry.endTime) {\n                break;\n              }\n            }\n            schedulerQueue.splice(i, 0, newEntry);\n          });\n        }\n      }\n    }\n    lastCurrentTime = this._currentTickTime;\n    this._currentTickTime = finalTime;\n    if (doTick) {\n      doTick(this._currentTickTime - lastCurrentTime);\n    }\n  }\n  flushOnlyPendingTimers(doTick) {\n    if (this._schedulerQueue.length === 0) {\n      return 0;\n    }\n    // Find the last task currently queued in the scheduler queue and tick\n    // till that time.\n    const startTime = this._currentTickTime;\n    const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n    this.tick(lastTask.endTime - startTime, doTick, {\n      processNewMacroTasksSynchronously: false\n    });\n    return this._currentTickTime - startTime;\n  }\n  flush(limit = 20, flushPeriodic = false, doTick) {\n    if (flushPeriodic) {\n      return this.flushPeriodic(doTick);\n    } else {\n      return this.flushNonPeriodic(limit, doTick);\n    }\n  }\n  flushPeriodic(doTick) {\n    if (this._schedulerQueue.length === 0) {\n      return 0;\n    }\n    // Find the last task currently queued in the scheduler queue and tick\n    // till that time.\n    const startTime = this._currentTickTime;\n    const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n    this.tick(lastTask.endTime - startTime, doTick);\n    return this._currentTickTime - startTime;\n  }\n  flushNonPeriodic(limit, doTick) {\n    const startTime = this._currentTickTime;\n    let lastCurrentTime = 0;\n    let count = 0;\n    while (this._schedulerQueue.length > 0) {\n      count++;\n      if (count > limit) {\n        throw new Error('flush failed after reaching the limit of ' + limit + ' tasks. Does your code use a polling timeout?');\n      }\n      // flush only non-periodic timers.\n      // If the only remaining tasks are periodic(or requestAnimationFrame), finish flushing.\n      if (this._schedulerQueue.filter(task => !task.isPeriodic && !task.isRequestAnimationFrame).length === 0) {\n        break;\n      }\n      const current = this._schedulerQueue.shift();\n      lastCurrentTime = this._currentTickTime;\n      this._currentTickTime = current.endTime;\n      if (doTick) {\n        // Update any secondary schedulers like Jasmine mock Date.\n        doTick(this._currentTickTime - lastCurrentTime);\n      }\n      const retval = current.func.apply(global$1, current.args);\n      if (!retval) {\n        // Uncaught exception in the current scheduled function. Stop processing the queue.\n        break;\n      }\n    }\n    return this._currentTickTime - startTime;\n  }\n}\n// Next scheduler id.\n_defineProperty(Scheduler, \"nextNodeJSId\", 1);\n_defineProperty(Scheduler, \"nextId\", -1);\nclass FakeAsyncTestZoneSpec {\n  static assertInZone() {\n    if (Zone.current.get('FakeAsyncTestZoneSpec') == null) {\n      throw new Error('The code should be running in the fakeAsync zone to call this function');\n    }\n  }\n  constructor(namePrefix, trackPendingRequestAnimationFrame = false, macroTaskOptions) {\n    _defineProperty(this, \"trackPendingRequestAnimationFrame\", void 0);\n    _defineProperty(this, \"macroTaskOptions\", void 0);\n    _defineProperty(this, \"_scheduler\", new Scheduler());\n    _defineProperty(this, \"_microtasks\", []);\n    _defineProperty(this, \"_lastError\", null);\n    _defineProperty(this, \"_uncaughtPromiseErrors\", Promise[Zone.__symbol__('uncaughtPromiseErrors')]);\n    _defineProperty(this, \"pendingPeriodicTimers\", []);\n    _defineProperty(this, \"pendingTimers\", []);\n    _defineProperty(this, \"patchDateLocked\", false);\n    // ZoneSpec implementation below.\n    _defineProperty(this, \"name\", void 0);\n    _defineProperty(this, \"properties\", {\n      'FakeAsyncTestZoneSpec': this\n    });\n    this.trackPendingRequestAnimationFrame = trackPendingRequestAnimationFrame;\n    this.macroTaskOptions = macroTaskOptions;\n    this.name = 'fakeAsyncTestZone for ' + namePrefix;\n    // in case user can't access the construction of FakeAsyncTestSpec\n    // user can also define macroTaskOptions by define a global variable.\n    if (!this.macroTaskOptions) {\n      this.macroTaskOptions = global$1[Zone.__symbol__('FakeAsyncTestMacroTask')];\n    }\n  }\n  _fnAndFlush(fn, completers) {\n    return (...args) => {\n      fn.apply(global$1, args);\n      if (this._lastError === null) {\n        // Success\n        if (completers.onSuccess != null) {\n          completers.onSuccess.apply(global$1);\n        }\n        // Flush microtasks only on success.\n        this.flushMicrotasks();\n      } else {\n        // Failure\n        if (completers.onError != null) {\n          completers.onError.apply(global$1);\n        }\n      }\n      // Return true if there were no errors, false otherwise.\n      return this._lastError === null;\n    };\n  }\n  static _removeTimer(timers, id) {\n    let index = timers.indexOf(id);\n    if (index > -1) {\n      timers.splice(index, 1);\n    }\n  }\n  _dequeueTimer(id) {\n    return () => {\n      FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n    };\n  }\n  _requeuePeriodicTimer(fn, interval, args, id) {\n    return () => {\n      // Requeue the timer callback if it's not been canceled.\n      if (this.pendingPeriodicTimers.indexOf(id) !== -1) {\n        this._scheduler.scheduleFunction(fn, interval, {\n          args,\n          isPeriodic: true,\n          id,\n          isRequeuePeriodic: true\n        });\n      }\n    };\n  }\n  _dequeuePeriodicTimer(id) {\n    return () => {\n      FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n    };\n  }\n  _setTimeout(fn, delay, args, isTimer = true) {\n    let removeTimerFn = this._dequeueTimer(Scheduler.nextId);\n    // Queue the callback and dequeue the timer on success and error.\n    let cb = this._fnAndFlush(fn, {\n      onSuccess: removeTimerFn,\n      onError: removeTimerFn\n    });\n    let id = this._scheduler.scheduleFunction(cb, delay, {\n      args,\n      isRequestAnimationFrame: !isTimer\n    });\n    if (isTimer) {\n      this.pendingTimers.push(id);\n    }\n    return id;\n  }\n  _clearTimeout(id) {\n    FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n    this._scheduler.removeScheduledFunctionWithId(id);\n  }\n  _setInterval(fn, interval, args) {\n    let id = Scheduler.nextId;\n    let completers = {\n      onSuccess: null,\n      onError: this._dequeuePeriodicTimer(id)\n    };\n    let cb = this._fnAndFlush(fn, completers);\n    // Use the callback created above to requeue on success.\n    completers.onSuccess = this._requeuePeriodicTimer(cb, interval, args, id);\n    // Queue the callback and dequeue the periodic timer only on error.\n    this._scheduler.scheduleFunction(cb, interval, {\n      args,\n      isPeriodic: true\n    });\n    this.pendingPeriodicTimers.push(id);\n    return id;\n  }\n  _clearInterval(id) {\n    FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n    this._scheduler.removeScheduledFunctionWithId(id);\n  }\n  _resetLastErrorAndThrow() {\n    let error = this._lastError || this._uncaughtPromiseErrors[0];\n    this._uncaughtPromiseErrors.length = 0;\n    this._lastError = null;\n    throw error;\n  }\n  getCurrentTickTime() {\n    return this._scheduler.getCurrentTickTime();\n  }\n  getFakeSystemTime() {\n    return this._scheduler.getFakeSystemTime();\n  }\n  setFakeBaseSystemTime(realTime) {\n    this._scheduler.setFakeBaseSystemTime(realTime);\n  }\n  getRealSystemTime() {\n    return this._scheduler.getRealSystemTime();\n  }\n  static patchDate() {\n    if (!!global$1[Zone.__symbol__('disableDatePatching')]) {\n      // we don't want to patch global Date\n      // because in some case, global Date\n      // is already being patched, we need to provide\n      // an option to let user still use their\n      // own version of Date.\n      return;\n    }\n    if (global$1['Date'] === FakeDate) {\n      // already patched\n      return;\n    }\n    global$1['Date'] = FakeDate;\n    FakeDate.prototype = OriginalDate.prototype;\n    // try check and reset timers\n    // because jasmine.clock().install() may\n    // have replaced the global timer\n    FakeAsyncTestZoneSpec.checkTimerPatch();\n  }\n  static resetDate() {\n    if (global$1['Date'] === FakeDate) {\n      global$1['Date'] = OriginalDate;\n    }\n  }\n  static checkTimerPatch() {\n    if (!patchedTimers) {\n      throw new Error('Expected timers to have been patched.');\n    }\n    if (global$1.setTimeout !== patchedTimers.setTimeout) {\n      global$1.setTimeout = patchedTimers.setTimeout;\n      global$1.clearTimeout = patchedTimers.clearTimeout;\n    }\n    if (global$1.setInterval !== patchedTimers.setInterval) {\n      global$1.setInterval = patchedTimers.setInterval;\n      global$1.clearInterval = patchedTimers.clearInterval;\n    }\n  }\n  lockDatePatch() {\n    this.patchDateLocked = true;\n    FakeAsyncTestZoneSpec.patchDate();\n  }\n  unlockDatePatch() {\n    this.patchDateLocked = false;\n    FakeAsyncTestZoneSpec.resetDate();\n  }\n  tickToNext(steps = 1, doTick, tickOptions = {\n    processNewMacroTasksSynchronously: true\n  }) {\n    if (steps <= 0) {\n      return;\n    }\n    FakeAsyncTestZoneSpec.assertInZone();\n    this.flushMicrotasks();\n    this._scheduler.tickToNext(steps, doTick, tickOptions);\n    if (this._lastError !== null) {\n      this._resetLastErrorAndThrow();\n    }\n  }\n  tick(millis = 0, doTick, tickOptions = {\n    processNewMacroTasksSynchronously: true\n  }) {\n    FakeAsyncTestZoneSpec.assertInZone();\n    this.flushMicrotasks();\n    this._scheduler.tick(millis, doTick, tickOptions);\n    if (this._lastError !== null) {\n      this._resetLastErrorAndThrow();\n    }\n  }\n  flushMicrotasks() {\n    FakeAsyncTestZoneSpec.assertInZone();\n    const flushErrors = () => {\n      if (this._lastError !== null || this._uncaughtPromiseErrors.length) {\n        // If there is an error stop processing the microtask queue and rethrow the error.\n        this._resetLastErrorAndThrow();\n      }\n    };\n    while (this._microtasks.length > 0) {\n      let microtask = this._microtasks.shift();\n      microtask.func.apply(microtask.target, microtask.args);\n    }\n    flushErrors();\n  }\n  flush(limit, flushPeriodic, doTick) {\n    FakeAsyncTestZoneSpec.assertInZone();\n    this.flushMicrotasks();\n    const elapsed = this._scheduler.flush(limit, flushPeriodic, doTick);\n    if (this._lastError !== null) {\n      this._resetLastErrorAndThrow();\n    }\n    return elapsed;\n  }\n  flushOnlyPendingTimers(doTick) {\n    FakeAsyncTestZoneSpec.assertInZone();\n    this.flushMicrotasks();\n    const elapsed = this._scheduler.flushOnlyPendingTimers(doTick);\n    if (this._lastError !== null) {\n      this._resetLastErrorAndThrow();\n    }\n    return elapsed;\n  }\n  removeAllTimers() {\n    FakeAsyncTestZoneSpec.assertInZone();\n    this._scheduler.removeAll();\n    this.pendingPeriodicTimers = [];\n    this.pendingTimers = [];\n  }\n  getTimerCount() {\n    return this._scheduler.getTimerCount() + this._microtasks.length;\n  }\n  onScheduleTask(delegate, current, target, task) {\n    switch (task.type) {\n      case 'microTask':\n        let args = task.data && task.data.args;\n        // should pass additional arguments to callback if have any\n        // currently we know process.nextTick will have such additional\n        // arguments\n        let additionalArgs;\n        if (args) {\n          let callbackIndex = task.data.cbIdx;\n          if (typeof args.length === 'number' && args.length > callbackIndex + 1) {\n            additionalArgs = Array.prototype.slice.call(args, callbackIndex + 1);\n          }\n        }\n        this._microtasks.push({\n          func: task.invoke,\n          args: additionalArgs,\n          target: task.data && task.data.target\n        });\n        break;\n      case 'macroTask':\n        switch (task.source) {\n          case 'setTimeout':\n            task.data['handleId'] = this._setTimeout(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n            break;\n          case 'setImmediate':\n            task.data['handleId'] = this._setTimeout(task.invoke, 0, Array.prototype.slice.call(task.data['args'], 1));\n            break;\n          case 'setInterval':\n            task.data['handleId'] = this._setInterval(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n            break;\n          case 'XMLHttpRequest.send':\n            throw new Error('Cannot make XHRs from within a fake async test. Request URL: ' + task.data['url']);\n          case 'requestAnimationFrame':\n          case 'webkitRequestAnimationFrame':\n          case 'mozRequestAnimationFrame':\n            // Simulate a requestAnimationFrame by using a setTimeout with 16 ms.\n            // (60 frames per second)\n            task.data['handleId'] = this._setTimeout(task.invoke, 16, task.data['args'], this.trackPendingRequestAnimationFrame);\n            break;\n          default:\n            // user can define which macroTask they want to support by passing\n            // macroTaskOptions\n            const macroTaskOption = this.findMacroTaskOption(task);\n            if (macroTaskOption) {\n              const args = task.data && task.data['args'];\n              const delay = args && args.length > 1 ? args[1] : 0;\n              let callbackArgs = macroTaskOption.callbackArgs ? macroTaskOption.callbackArgs : args;\n              if (!!macroTaskOption.isPeriodic) {\n                // periodic macroTask, use setInterval to simulate\n                task.data['handleId'] = this._setInterval(task.invoke, delay, callbackArgs);\n                task.data.isPeriodic = true;\n              } else {\n                // not periodic, use setTimeout to simulate\n                task.data['handleId'] = this._setTimeout(task.invoke, delay, callbackArgs);\n              }\n              break;\n            }\n            throw new Error('Unknown macroTask scheduled in fake async test: ' + task.source);\n        }\n        break;\n      case 'eventTask':\n        task = delegate.scheduleTask(target, task);\n        break;\n    }\n    return task;\n  }\n  onCancelTask(delegate, current, target, task) {\n    switch (task.source) {\n      case 'setTimeout':\n      case 'requestAnimationFrame':\n      case 'webkitRequestAnimationFrame':\n      case 'mozRequestAnimationFrame':\n        return this._clearTimeout(task.data['handleId']);\n      case 'setInterval':\n        return this._clearInterval(task.data['handleId']);\n      default:\n        // user can define which macroTask they want to support by passing\n        // macroTaskOptions\n        const macroTaskOption = this.findMacroTaskOption(task);\n        if (macroTaskOption) {\n          const handleId = task.data['handleId'];\n          return macroTaskOption.isPeriodic ? this._clearInterval(handleId) : this._clearTimeout(handleId);\n        }\n        return delegate.cancelTask(target, task);\n    }\n  }\n  onInvoke(delegate, current, target, callback, applyThis, applyArgs, source) {\n    try {\n      FakeAsyncTestZoneSpec.patchDate();\n      return delegate.invoke(target, callback, applyThis, applyArgs, source);\n    } finally {\n      if (!this.patchDateLocked) {\n        FakeAsyncTestZoneSpec.resetDate();\n      }\n    }\n  }\n  findMacroTaskOption(task) {\n    if (!this.macroTaskOptions) {\n      return null;\n    }\n    for (let i = 0; i < this.macroTaskOptions.length; i++) {\n      const macroTaskOption = this.macroTaskOptions[i];\n      if (macroTaskOption.source === task.source) {\n        return macroTaskOption;\n      }\n    }\n    return null;\n  }\n  onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n    // ComponentFixture has a special-case handling to detect FakeAsyncTestZoneSpec\n    // and prevent rethrowing the error from the onError subscription since it's handled here.\n    this._lastError = error;\n    return false; // Don't propagate error to parent zone.\n  }\n}\nlet _fakeAsyncTestZoneSpec = null;\nfunction getProxyZoneSpec() {\n  return Zone && Zone['ProxyZoneSpec'];\n}\nlet _sharedProxyZoneSpec = null;\nlet _sharedProxyZone = null;\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @experimental\n */\nfunction resetFakeAsyncZone() {\n  var _getProxyZoneSpec, _sharedProxyZoneSpec2;\n  if (_fakeAsyncTestZoneSpec) {\n    _fakeAsyncTestZoneSpec.unlockDatePatch();\n  }\n  _fakeAsyncTestZoneSpec = null;\n  (_getProxyZoneSpec = getProxyZoneSpec()) === null || _getProxyZoneSpec === void 0 || (_getProxyZoneSpec = _getProxyZoneSpec.get()) === null || _getProxyZoneSpec === void 0 || _getProxyZoneSpec.resetDelegate();\n  (_sharedProxyZoneSpec2 = _sharedProxyZoneSpec) === null || _sharedProxyZoneSpec2 === void 0 || _sharedProxyZoneSpec2.resetDelegate();\n}\n/**\n * Wraps a function to be executed in the fakeAsync zone:\n * - microtasks are manually executed by calling `flushMicrotasks()`,\n * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n *\n * When flush is `false`, if there are any pending timers at the end of the function,\n * an exception will be thrown.\n *\n * Can be used to wrap inject() calls.\n *\n * ## Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * @param fn\n * @param options\n *     flush: when true, will drain the macrotask queue after the test function completes.\n * @returns The function wrapped to be executed in the fakeAsync zone\n *\n * @experimental\n */\nfunction fakeAsync(fn, options = {}) {\n  const {\n    flush = true\n  } = options;\n  // Not using an arrow function to preserve context passed from call site\n  const fakeAsyncFn = function (...args) {\n    const ProxyZoneSpec = getProxyZoneSpec();\n    if (!ProxyZoneSpec) {\n      throw new Error('ProxyZoneSpec is needed for the fakeAsync() test helper but could not be found. ' + 'Make sure that your environment includes zone-testing.js');\n    }\n    const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n    if (Zone.current.get('FakeAsyncTestZoneSpec')) {\n      throw new Error('fakeAsync() calls can not be nested');\n    }\n    try {\n      // in case jasmine.clock init a fakeAsyncTestZoneSpec\n      if (!_fakeAsyncTestZoneSpec) {\n        const FakeAsyncTestZoneSpec = Zone && Zone['FakeAsyncTestZoneSpec'];\n        if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n          throw new Error('fakeAsync() calls can not be nested');\n        }\n        _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n      }\n      let res;\n      const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n      proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n      _fakeAsyncTestZoneSpec.lockDatePatch();\n      try {\n        res = fn.apply(this, args);\n        if (flush) {\n          _fakeAsyncTestZoneSpec.flush(20, true);\n        } else {\n          flushMicrotasks();\n        }\n      } finally {\n        proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n      }\n      if (!flush) {\n        if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n          throw new Error(`${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` + `periodic timer(s) still in the queue.`);\n        }\n        if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n          throw new Error(`${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n        }\n      }\n      return res;\n    } finally {\n      resetFakeAsyncZone();\n    }\n  };\n  fakeAsyncFn.isFakeAsync = true;\n  return fakeAsyncFn;\n}\nfunction _getFakeAsyncZoneSpec() {\n  if (_fakeAsyncTestZoneSpec == null) {\n    _fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n    if (_fakeAsyncTestZoneSpec == null) {\n      throw new Error('The code should be running in the fakeAsync zone to call this function');\n    }\n  }\n  return _fakeAsyncTestZoneSpec;\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer\n * callback has been executed.\n *\n * ## Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * @experimental\n */\nfunction tick(millis = 0, ignoreNestedTimeout = false) {\n  _getFakeAsyncZoneSpec().tick(millis, null, ignoreNestedTimeout);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n * draining the macrotask queue until it is empty. The returned value is the milliseconds\n * of time that would have been elapsed.\n *\n * @param maxTurns\n * @returns The simulated time elapsed, in millis.\n *\n * @experimental\n */\nfunction flush(maxTurns) {\n  return _getFakeAsyncZoneSpec().flush(maxTurns);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @experimental\n */\nfunction discardPeriodicTasks() {\n  const zoneSpec = _getFakeAsyncZoneSpec();\n  zoneSpec.pendingPeriodicTimers;\n  zoneSpec.pendingPeriodicTimers.length = 0;\n}\n/**\n * Wraps a function to be executed in a shared ProxyZone.\n *\n * If no shared ProxyZone exists, one is created and reused for subsequent calls.\n * Useful for wrapping test setup (beforeEach) and test execution (it) when test\n * runner patching isn't available or desired for setting up the ProxyZone.\n *\n * @param fn The function to wrap.\n * @returns A function that executes the original function within the shared ProxyZone.\n *\n * @experimental\n */\nfunction withProxyZone(fn) {\n  const autoProxyFn = function (...args) {\n    const proxyZoneSpec = getProxyZoneSpec();\n    if (proxyZoneSpec === undefined) {\n      throw new Error('ProxyZoneSpec is needed for the withProxyZone() test helper but could not be found. ' + 'Make sure that your environment includes zone-testing.js');\n    }\n    const proxyZone = proxyZoneSpec.get() !== undefined ? Zone.current : getOrCreateRootProxy();\n    return proxyZone.run(fn, this, args);\n  };\n  return autoProxyFn;\n}\nfunction getOrCreateRootProxy() {\n  const ProxyZoneSpec = getProxyZoneSpec();\n  if (ProxyZoneSpec === undefined) {\n    throw new Error('ProxyZoneSpec is needed for withProxyZone but could not be found. ' + 'Make sure that your environment includes zone-testing.js');\n  }\n  // Ensure the shared ProxyZoneSpec instance exists\n  if (_sharedProxyZoneSpec === null) {\n    _sharedProxyZoneSpec = new ProxyZoneSpec();\n  }\n  _sharedProxyZone = Zone.root.fork(_sharedProxyZoneSpec);\n  return _sharedProxyZone;\n}\n/**\n * Flush any pending microtasks.\n *\n * @experimental\n */\nfunction flushMicrotasks() {\n  _getFakeAsyncZoneSpec().flushMicrotasks();\n}\nfunction patchFakeAsyncTest(Zone) {\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['FakeAsyncTestZoneSpec'] = FakeAsyncTestZoneSpec;\n  Zone.__load_patch('fakeasync', (global, Zone, api) => {\n    Zone[api.symbol('fakeAsyncTest')] = {\n      resetFakeAsyncZone,\n      flushMicrotasks,\n      discardPeriodicTasks,\n      tick,\n      flush,\n      fakeAsync,\n      withProxyZone\n    };\n  }, true);\n  patchedTimers = {\n    setTimeout: global$1.setTimeout,\n    setInterval: global$1.setInterval,\n    clearTimeout: global$1.clearTimeout,\n    clearInterval: global$1.clearInterval,\n    nativeSetTimeout: global$1[Zone.__symbol__('setTimeout')],\n    nativeClearTimeout: global$1[Zone.__symbol__('clearTimeout')]\n  };\n  Scheduler.nextId = Scheduler.getNextId();\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction patchLongStackTrace(Zone) {\n  const NEWLINE = '\\n';\n  const IGNORE_FRAMES = {};\n  const creationTrace = '__creationTrace__';\n  const ERROR_TAG = 'STACKTRACE TRACKING';\n  const SEP_TAG = '__SEP_TAG__';\n  let sepTemplate = SEP_TAG + '@[native]';\n  class LongStackTrace {\n    constructor() {\n      _defineProperty(this, \"error\", getStacktrace());\n      _defineProperty(this, \"timestamp\", new Date());\n    }\n  }\n  function getStacktraceWithUncaughtError() {\n    return new Error(ERROR_TAG);\n  }\n  function getStacktraceWithCaughtError() {\n    try {\n      throw getStacktraceWithUncaughtError();\n    } catch (err) {\n      return err;\n    }\n  }\n  // Some implementations of exception handling don't create a stack trace if the exception\n  // isn't thrown, however it's faster not to actually throw the exception.\n  const error = getStacktraceWithUncaughtError();\n  const caughtError = getStacktraceWithCaughtError();\n  const getStacktrace = error.stack ? getStacktraceWithUncaughtError : caughtError.stack ? getStacktraceWithCaughtError : getStacktraceWithUncaughtError;\n  function getFrames(error) {\n    return error.stack ? error.stack.split(NEWLINE) : [];\n  }\n  function addErrorStack(lines, error) {\n    let trace = getFrames(error);\n    for (let i = 0; i < trace.length; i++) {\n      const frame = trace[i];\n      // Filter out the Frames which are part of stack capturing.\n      if (!IGNORE_FRAMES.hasOwnProperty(frame)) {\n        lines.push(trace[i]);\n      }\n    }\n  }\n  function renderLongStackTrace(frames, stack) {\n    const longTrace = [stack ? stack.trim() : ''];\n    if (frames) {\n      let timestamp = new Date().getTime();\n      for (let i = 0; i < frames.length; i++) {\n        const traceFrames = frames[i];\n        const lastTime = traceFrames.timestamp;\n        let separator = `____________________Elapsed ${timestamp - lastTime.getTime()} ms; At: ${lastTime}`;\n        separator = separator.replace(/[^\\w\\d]/g, '_');\n        longTrace.push(sepTemplate.replace(SEP_TAG, separator));\n        addErrorStack(longTrace, traceFrames.error);\n        timestamp = lastTime.getTime();\n      }\n    }\n    return longTrace.join(NEWLINE);\n  }\n  // if Error.stackTraceLimit is 0, means stack trace\n  // is disabled, so we don't need to generate long stack trace\n  // this will improve performance in some test(some test will\n  // set stackTraceLimit to 0, https://github.com/angular/zone.js/issues/698\n  function stackTracesEnabled() {\n    // Cast through any since this property only exists on Error in the nodejs\n    // typings.\n    return Error.stackTraceLimit > 0;\n  }\n  Zone['longStackTraceZoneSpec'] = {\n    name: 'long-stack-trace',\n    longStackTraceLimit: 10,\n    // Max number of task to keep the stack trace for.\n    // add a getLongStackTrace method in spec to\n    // handle handled reject promise error.\n    getLongStackTrace: function (error) {\n      if (!error) {\n        return undefined;\n      }\n      const trace = error[Zone.__symbol__('currentTaskTrace')];\n      if (!trace) {\n        return error.stack;\n      }\n      return renderLongStackTrace(trace, error.stack);\n    },\n    onScheduleTask: function (parentZoneDelegate, currentZone, targetZone, task) {\n      if (stackTracesEnabled()) {\n        const currentTask = Zone.currentTask;\n        let trace = currentTask && currentTask.data && currentTask.data[creationTrace] || [];\n        trace = [new LongStackTrace()].concat(trace);\n        if (trace.length > this.longStackTraceLimit) {\n          trace.length = this.longStackTraceLimit;\n        }\n        if (!task.data) task.data = {};\n        if (task.type === 'eventTask') {\n          // Fix issue https://github.com/angular/zone.js/issues/1195,\n          // For event task of browser, by default, all task will share a\n          // singleton instance of data object, we should create a new one here\n          // The cast to `any` is required to workaround a closure bug which wrongly applies\n          // URL sanitization rules to .data access.\n          task.data = _objectSpread({}, task.data);\n        }\n        task.data[creationTrace] = trace;\n      }\n      return parentZoneDelegate.scheduleTask(targetZone, task);\n    },\n    onHandleError: function (parentZoneDelegate, currentZone, targetZone, error) {\n      if (stackTracesEnabled()) {\n        const parentTask = Zone.currentTask || error.task;\n        if (error instanceof Error && parentTask) {\n          const longStack = renderLongStackTrace(parentTask.data && parentTask.data[creationTrace], error.stack);\n          try {\n            error.stack = error.longStack = longStack;\n          } catch (err) {}\n        }\n      }\n      return parentZoneDelegate.handleError(targetZone, error);\n    }\n  };\n  function captureStackTraces(stackTraces, count) {\n    if (count > 0) {\n      stackTraces.push(getFrames(new LongStackTrace().error));\n      captureStackTraces(stackTraces, count - 1);\n    }\n  }\n  function computeIgnoreFrames() {\n    if (!stackTracesEnabled()) {\n      return;\n    }\n    const frames = [];\n    captureStackTraces(frames, 2);\n    const frames1 = frames[0];\n    const frames2 = frames[1];\n    for (let i = 0; i < frames1.length; i++) {\n      const frame1 = frames1[i];\n      if (frame1.indexOf(ERROR_TAG) == -1) {\n        let match = frame1.match(/^\\s*at\\s+/);\n        if (match) {\n          sepTemplate = match[0] + SEP_TAG + ' (http://localhost)';\n          break;\n        }\n      }\n    }\n    for (let i = 0; i < frames1.length; i++) {\n      const frame1 = frames1[i];\n      const frame2 = frames2[i];\n      if (frame1 === frame2) {\n        IGNORE_FRAMES[frame1] = true;\n      } else {\n        break;\n      }\n    }\n  }\n  computeIgnoreFrames();\n}\nclass ProxyZoneSpec {\n  static get() {\n    return Zone.current.get('ProxyZoneSpec');\n  }\n  static isLoaded() {\n    return ProxyZoneSpec.get() instanceof ProxyZoneSpec;\n  }\n  static assertPresent() {\n    const spec = ProxyZoneSpec.get();\n    if (spec === undefined) {\n      throw new Error(`Expected to be running in 'ProxyZone', but it was not found.`);\n    }\n    return spec;\n  }\n  constructor(defaultSpecDelegate = null) {\n    _defineProperty(this, \"defaultSpecDelegate\", void 0);\n    _defineProperty(this, \"name\", 'ProxyZone');\n    _defineProperty(this, \"_delegateSpec\", null);\n    _defineProperty(this, \"properties\", {\n      'ProxyZoneSpec': this\n    });\n    _defineProperty(this, \"propertyKeys\", null);\n    _defineProperty(this, \"lastTaskState\", null);\n    _defineProperty(this, \"isNeedToTriggerHasTask\", false);\n    _defineProperty(this, \"tasks\", []);\n    this.defaultSpecDelegate = defaultSpecDelegate;\n    this.setDelegate(defaultSpecDelegate);\n  }\n  setDelegate(delegateSpec) {\n    const isNewDelegate = this._delegateSpec !== delegateSpec;\n    this._delegateSpec = delegateSpec;\n    this.propertyKeys && this.propertyKeys.forEach(key => delete this.properties[key]);\n    this.propertyKeys = null;\n    if (delegateSpec && delegateSpec.properties) {\n      this.propertyKeys = Object.keys(delegateSpec.properties);\n      this.propertyKeys.forEach(k => this.properties[k] = delegateSpec.properties[k]);\n    }\n    // if a new delegateSpec was set, check if we need to trigger hasTask\n    if (isNewDelegate && this.lastTaskState && (this.lastTaskState.macroTask || this.lastTaskState.microTask)) {\n      this.isNeedToTriggerHasTask = true;\n    }\n  }\n  getDelegate() {\n    return this._delegateSpec;\n  }\n  resetDelegate() {\n    this.getDelegate();\n    this.setDelegate(this.defaultSpecDelegate);\n  }\n  tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone) {\n    if (this.isNeedToTriggerHasTask && this.lastTaskState) {\n      // last delegateSpec has microTask or macroTask\n      // should call onHasTask in current delegateSpec\n      this.isNeedToTriggerHasTask = false;\n      this.onHasTask(parentZoneDelegate, currentZone, targetZone, this.lastTaskState);\n    }\n  }\n  removeFromTasks(task) {\n    if (!this.tasks) {\n      return;\n    }\n    for (let i = 0; i < this.tasks.length; i++) {\n      if (this.tasks[i] === task) {\n        this.tasks.splice(i, 1);\n        return;\n      }\n    }\n  }\n  getAndClearPendingTasksInfo() {\n    if (this.tasks.length === 0) {\n      return '';\n    }\n    const taskInfo = this.tasks.map(task => {\n      const dataInfo = task.data && Object.keys(task.data).map(key => {\n        return key + ':' + task.data[key];\n      }).join(',');\n      return `type: ${task.type}, source: ${task.source}, args: {${dataInfo}}`;\n    });\n    const pendingTasksInfo = '--Pending async tasks are: [' + taskInfo + ']';\n    // clear tasks\n    this.tasks = [];\n    return pendingTasksInfo;\n  }\n  onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec) {\n    if (this._delegateSpec && this._delegateSpec.onFork) {\n      return this._delegateSpec.onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec);\n    } else {\n      return parentZoneDelegate.fork(targetZone, zoneSpec);\n    }\n  }\n  onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source) {\n    if (this._delegateSpec && this._delegateSpec.onIntercept) {\n      return this._delegateSpec.onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source);\n    } else {\n      return parentZoneDelegate.intercept(targetZone, delegate, source);\n    }\n  }\n  onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onInvoke) {\n      return this._delegateSpec.onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source);\n    } else {\n      return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n    }\n  }\n  onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n    if (this._delegateSpec && this._delegateSpec.onHandleError) {\n      return this._delegateSpec.onHandleError(parentZoneDelegate, currentZone, targetZone, error);\n    } else {\n      return parentZoneDelegate.handleError(targetZone, error);\n    }\n  }\n  onScheduleTask(parentZoneDelegate, currentZone, targetZone, task) {\n    if (task.type !== 'eventTask') {\n      this.tasks.push(task);\n    }\n    if (this._delegateSpec && this._delegateSpec.onScheduleTask) {\n      return this._delegateSpec.onScheduleTask(parentZoneDelegate, currentZone, targetZone, task);\n    } else {\n      return parentZoneDelegate.scheduleTask(targetZone, task);\n    }\n  }\n  onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs) {\n    if (task.type !== 'eventTask') {\n      this.removeFromTasks(task);\n    }\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onInvokeTask) {\n      return this._delegateSpec.onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs);\n    } else {\n      return parentZoneDelegate.invokeTask(targetZone, task, applyThis, applyArgs);\n    }\n  }\n  onCancelTask(parentZoneDelegate, currentZone, targetZone, task) {\n    if (task.type !== 'eventTask') {\n      this.removeFromTasks(task);\n    }\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onCancelTask) {\n      return this._delegateSpec.onCancelTask(parentZoneDelegate, currentZone, targetZone, task);\n    } else {\n      return parentZoneDelegate.cancelTask(targetZone, task);\n    }\n  }\n  onHasTask(delegate, current, target, hasTaskState) {\n    this.lastTaskState = hasTaskState;\n    if (this._delegateSpec && this._delegateSpec.onHasTask) {\n      this._delegateSpec.onHasTask(delegate, current, target, hasTaskState);\n    } else {\n      delegate.hasTask(target, hasTaskState);\n    }\n  }\n}\nfunction patchProxyZoneSpec(Zone) {\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['ProxyZoneSpec'] = ProxyZoneSpec;\n}\nfunction patchSyncTest(Zone) {\n  class SyncTestZoneSpec {\n    constructor(namePrefix) {\n      _defineProperty(this, \"runZone\", Zone.current);\n      // ZoneSpec implementation below.\n      _defineProperty(this, \"name\", void 0);\n      this.name = 'syncTestZone for ' + namePrefix;\n    }\n    onScheduleTask(delegate, current, target, task) {\n      switch (task.type) {\n        case 'microTask':\n        case 'macroTask':\n          throw new Error(`Cannot call ${task.source} from within a sync test (${this.name}).`);\n        case 'eventTask':\n          task = delegate.scheduleTask(target, task);\n          break;\n      }\n      return task;\n    }\n  }\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['SyncTestZoneSpec'] = SyncTestZoneSpec;\n}\nfunction patchPromiseTesting(Zone) {\n  /**\n   * Promise for async/fakeAsync zoneSpec test\n   * can support async operation which not supported by zone.js\n   * such as\n   * it ('test jsonp in AsyncZone', async() => {\n   *   new Promise(res => {\n   *     jsonp(url, (data) => {\n   *       // success callback\n   *       res(data);\n   *     });\n   *   }).then((jsonpResult) => {\n   *     // get jsonp result.\n   *\n   *     // user will expect AsyncZoneSpec wait for\n   *     // then, but because jsonp is not zone aware\n   *     // AsyncZone will finish before then is called.\n   *   });\n   * });\n   */\n  Zone.__load_patch('promisefortest', (global, Zone, api) => {\n    const symbolState = api.symbol('state');\n    const UNRESOLVED = null;\n    const symbolParentUnresolved = api.symbol('parentUnresolved');\n    // patch Promise.prototype.then to keep an internal\n    // number for tracking unresolved chained promise\n    // we will decrease this number when the parent promise\n    // being resolved/rejected and chained promise was\n    // scheduled as a microTask.\n    // so we can know such kind of chained promise still\n    // not resolved in AsyncTestZone\n    Promise[api.symbol('patchPromiseForTest')] = function patchPromiseForTest() {\n      let oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n      if (oriThen) {\n        return;\n      }\n      oriThen = Promise[Zone.__symbol__('ZonePromiseThen')] = Promise.prototype.then;\n      Promise.prototype.then = function () {\n        const chained = oriThen.apply(this, arguments);\n        if (this[symbolState] === UNRESOLVED) {\n          // parent promise is unresolved.\n          const asyncTestZoneSpec = Zone.current.get('AsyncTestZoneSpec');\n          if (asyncTestZoneSpec) {\n            asyncTestZoneSpec.unresolvedChainedPromiseCount++;\n            chained[symbolParentUnresolved] = true;\n          }\n        }\n        return chained;\n      };\n    };\n    Promise[api.symbol('unPatchPromiseForTest')] = function unpatchPromiseForTest() {\n      // restore origin then\n      const oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n      if (oriThen) {\n        Promise.prototype.then = oriThen;\n        Promise[Zone.__symbol__('ZonePromiseThen')] = undefined;\n      }\n    };\n  });\n}\nfunction rollupTesting(Zone) {\n  patchLongStackTrace(Zone);\n  patchProxyZoneSpec(Zone);\n  patchSyncTest(Zone);\n  patchJasmine(Zone);\n  patchJest(Zone);\n  patchMocha(Zone);\n  patchAsyncTest(Zone);\n  patchFakeAsyncTest(Zone);\n  patchPromiseTesting(Zone);\n}\nrollupTesting(Zone);", "map": {"version": 3, "names": ["_defineProperty", "require", "default", "_objectSpread", "patchJasmine", "Zone", "__load_patch", "global", "api", "__extends", "d", "b", "p", "hasOwnProperty", "__", "constructor", "prototype", "Object", "create", "Error", "jest", "jasmine", "SyncTestZoneSpec", "ProxyZoneSpec", "ambientZone", "current", "symbol", "__symbol__", "disablePatchingJasmineClock", "enableAutoFakeAsyncWhenClockPatched", "ignoreUnhandledRejection", "globalErrors", "GlobalErrors", "instance", "originalInstall", "install", "isNode", "process", "on", "originalHandlers", "listeners", "eventListeners", "result", "apply", "arguments", "removeAllListeners", "for<PERSON>ach", "handler", "addEventListener", "jasmineEnv", "getEnv", "methodName", "originalJasmineFn", "description", "specDefinitions", "call", "wrapDescribeInZone", "timeout", "wrapTestInZone", "originalClockFn", "clock", "originalTick", "tick", "fakeAsyncZoneSpec", "get", "originalMockDate", "mockDate", "dateTime", "length", "Date", "setFakeBaseSystemTime", "getTime", "FakeAsyncTestZoneSpec", "originalCreateSpyObj", "createSpyObj", "args", "Array", "slice", "propertyNames", "spyObj", "defineProperty", "obj", "attributes", "configurable", "enumerable", "describeBody", "syncZone", "fork", "run", "runInTestZone", "testBody", "applyThis", "queueRunner", "done", "isClockInstalled", "testProxyZoneSpec", "testProxyZone", "fakeAsyncModule", "fakeAsync", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "ZoneQueueRunner", "attrs", "onComplete", "fn", "scheduleMicroTask", "nativeSetTimeout", "nativeClearTimeout", "setTimeout", "clearTimeout", "UserContext", "userContext", "onException", "error", "message", "proxyZoneSpec", "pendingTasksInfo", "getAndClearPendingTasksInfo", "err", "execute", "zone", "isChildOfAmbientZone", "parent", "name", "currentTask", "patchJest", "context", "rootZone", "proxyZone", "wrapDescribeFactoryInZone", "originalJestFn", "tableArgs", "originalDescribeFn", "wrapTestFactoryInZone", "isTestFunc", "wrappedFunc", "isFakeAsync", "writable", "each", "describe", "only", "fdescribe", "skip", "xdescribe", "todo", "failing", "it", "fit", "xit", "test", "patchJestObject", "Timer", "isModern", "isPatchingFakeTimer", "isInTestFunc", "patchMethod", "delegate", "self", "getRealSystemTime", "flushMicrotasks", "flush", "flushOnlyPendingTimers", "tickToNext", "removeAllTimers", "getTimerCount", "patchMocha", "<PERSON><PERSON>", "testZone", "suiteZone", "mochaOriginal", "after", "after<PERSON>ach", "before", "beforeEach", "modifyArguments", "syncTest", "asyncTest", "i", "arg", "toString", "wrapSuiteInZone", "suite", "specify", "xspecify", "suiteTeardown", "teardown", "suiteSetup", "setup", "originalRunTest", "originalRun", "Runner", "runTest", "e", "global$2", "globalThis", "symbolPrefix", "__global", "window", "AsyncTestZoneSpec", "symbolParentUnresolved", "finishCallback", "fail<PERSON><PERSON>back", "namePrefix", "properties", "supportWaitUnresolvedChainedPromise", "isUnresolvedChainedPromisePending", "unresolvedChainedPromiseCount", "_finishCallbackIfDone", "_existingFinishTimer", "_pendingMicroTasks", "_pendingMacroTasks", "runZone", "_alreadyErrored", "patchPromiseForTest", "Promise", "unPatchPromiseForTest", "onScheduleTask", "target", "task", "type", "_isSync", "data", "scheduleTask", "onInvokeTask", "applyArgs", "invokeTask", "onCancelTask", "cancelTask", "onInvoke", "parentZoneDelegate", "currentZone", "targetZone", "source", "entryFunction", "invoke", "onHandleError", "handleError", "onHasTask", "hasTaskState", "hasTask", "change", "microTask", "macroTask", "patchAsyncTest", "fail", "undefined", "assertPresent", "getZoneWith", "previousDelegate", "getDelegate", "testZoneSpec", "setDelegate", "runGuarded", "global$1", "OriginalDate", "FakeDate", "setTime", "now", "fakeAsyncTestZoneSpec", "getFakeSystemTime", "UTC", "parse", "patchedTimers", "timeout<PERSON><PERSON><PERSON>", "Scheduler", "getNextId", "id", "nextNodeJSId", "getCurrentTickTime", "_currentTickTime", "_currentFakeBaseSystemTime", "fakeBaseSystemTime", "scheduleFunction", "cb", "delay", "options", "isPeriodic", "isRequestAnimationFrame", "isRequeuePeriodic", "currentId", "nextId", "endTime", "newEntry", "func", "_currentTickRequeuePeriodicEntries", "push", "_schedulerQueue", "currentEntry", "splice", "removeScheduledFunctionWithId", "removeAll", "step", "doTick", "tickOptions", "startTime", "targetTask", "millis", "finalTime", "lastCurrentTime", "assign", "processNewMacroTasksSynchronously", "schedulerQueue", "shift", "idx", "indexOf", "retval", "lastTask", "limit", "flushPeriodic", "flushNonPeriodic", "count", "filter", "assertInZone", "trackPendingRequestAnimationFrame", "macroTaskOptions", "_fnAndFlush", "completers", "_lastError", "onSuccess", "onError", "_removeTimer", "timers", "index", "_dequeueTimer", "pendingTimers", "_requeuePeriodicTimer", "interval", "pendingPeriodicTimers", "_scheduler", "_dequeuePeriodicTimer", "_setTimeout", "isTimer", "removeTimerFn", "_clearTimeout", "_setInterval", "_clearInterval", "_resetLastErrorAndThrow", "_uncaughtPromiseErrors", "realTime", "patchDate", "checkTimerPatch", "resetDate", "setInterval", "clearInterval", "lockDatePatch", "patchDateLocked", "unlockDatePatch", "steps", "flushErrors", "_microtasks", "microtask", "elapsed", "additionalArgs", "callbackIndex", "cbIdx", "macroTaskOption", "findMacroTaskOption", "callback<PERSON><PERSON><PERSON>", "handleId", "callback", "_fakeAsyncTestZoneSpec", "getProxyZoneSpec", "_sharedProxyZoneSpec", "_sharedProxyZone", "resetFakeAsyncZone", "_getProxyZoneSpec", "_sharedProxyZoneSpec2", "resetDelegate", "fakeAsyncFn", "res", "lastProxyZoneSpec", "_getFakeAsyncZoneSpec", "ignoreNestedTimeout", "maxTurns", "discardPeriodicTasks", "zoneSpec", "withProxyZone", "autoProxyFn", "getOrCreateRootProxy", "root", "patchFakeAsyncTest", "patchLongStackTrace", "NEWLINE", "IGNORE_FRAMES", "creationTrace", "ERROR_TAG", "SEP_TAG", "sepTemplate", "LongStackTrace", "getStacktrace", "getStacktraceWithUncaughtError", "getStacktraceWithCaughtError", "caughtError", "stack", "getFrames", "split", "addErrorStack", "lines", "trace", "frame", "renderLongStackTrace", "frames", "longTrace", "trim", "timestamp", "traceFrames", "lastTime", "separator", "replace", "join", "stackTracesEnabled", "stackTraceLimit", "longStackTraceLimit", "getLongStackTrace", "concat", "parentTask", "longStack", "captureStackTraces", "stackTraces", "computeIgnoreFrames", "frames1", "frames2", "frame1", "match", "frame2", "isLoaded", "spec", "defaultSpecDelegate", "delegateSpec", "isNewDelegate", "_delegateSpec", "propertyKeys", "key", "keys", "k", "lastTaskState", "isNeedToTriggerHasTask", "tryTriggerHasTask", "removeFromTasks", "tasks", "taskInfo", "map", "dataInfo", "onFork", "onIntercept", "intercept", "patchProxyZoneSpec", "patchSyncTest", "patchPromiseTesting", "symbolState", "UNRESOLVED", "ori<PERSON><PERSON>", "then", "chained", "asyncTestZoneSpec", "unpatchPromiseForTest", "rollupTesting"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/node_modules/zone.js/fesm2015/zone-testing.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\nfunction patchJasmine(Zone) {\n    Zone.__load_patch('jasmine', (global, Zone, api) => {\n        const __extends = function (d, b) {\n            for (const p in b)\n                if (b.hasOwnProperty(p))\n                    d[p] = b[p];\n            function __() {\n                this.constructor = d;\n            }\n            d.prototype =\n                b === null ? Object.create(b) : ((__.prototype = b.prototype), new __());\n        };\n        // Patch jasmine's describe/it/beforeEach/afterEach functions so test code always runs\n        // in a testZone (ProxyZone). (See: angular/zone.js#91 & angular/angular#10503)\n        if (!Zone)\n            throw new Error('Missing: zone.js');\n        if (typeof jest !== 'undefined') {\n            // return if jasmine is a light implementation inside jest\n            // in this case, we are running inside jest not jasmine\n            return;\n        }\n        if (typeof jasmine == 'undefined' || jasmine['__zone_patch__']) {\n            return;\n        }\n        jasmine['__zone_patch__'] = true;\n        const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n        const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n        if (!SyncTestZoneSpec)\n            throw new Error('Missing: SyncTestZoneSpec');\n        if (!ProxyZoneSpec)\n            throw new Error('Missing: ProxyZoneSpec');\n        const ambientZone = Zone.current;\n        const symbol = Zone.__symbol__;\n        // whether patch jasmine clock when in fakeAsync\n        const disablePatchingJasmineClock = global[symbol('fakeAsyncDisablePatchingClock')] === true;\n        // the original variable name fakeAsyncPatchLock is not accurate, so the name will be\n        // fakeAsyncAutoFakeAsyncWhenClockPatched and if this enablePatchingJasmineClock is false, we\n        // also automatically disable the auto jump into fakeAsync feature\n        const enableAutoFakeAsyncWhenClockPatched = !disablePatchingJasmineClock &&\n            (global[symbol('fakeAsyncPatchLock')] === true ||\n                global[symbol('fakeAsyncAutoFakeAsyncWhenClockPatched')] === true);\n        const ignoreUnhandledRejection = global[symbol('ignoreUnhandledRejection')] === true;\n        if (!ignoreUnhandledRejection) {\n            const globalErrors = jasmine.GlobalErrors;\n            if (globalErrors && !jasmine[symbol('GlobalErrors')]) {\n                jasmine[symbol('GlobalErrors')] = globalErrors;\n                jasmine.GlobalErrors = function () {\n                    const instance = new globalErrors();\n                    const originalInstall = instance.install;\n                    if (originalInstall && !instance[symbol('install')]) {\n                        instance[symbol('install')] = originalInstall;\n                        instance.install = function () {\n                            const isNode = typeof process !== 'undefined' && !!process.on;\n                            // Note: Jasmine checks internally if `process` and `process.on` is defined.\n                            // Otherwise, it installs the browser rejection handler through the\n                            // `global.addEventListener`. This code may be run in the browser environment where\n                            // `process` is not defined, and this will lead to a runtime exception since webpack 5\n                            // removed automatic Node.js polyfills. Note, that events are named differently, it's\n                            // `unhandledRejection` in Node.js and `unhandledrejection` in the browser.\n                            const originalHandlers = isNode\n                                ? process.listeners('unhandledRejection')\n                                : global.eventListeners('unhandledrejection');\n                            const result = originalInstall.apply(this, arguments);\n                            isNode\n                                ? process.removeAllListeners('unhandledRejection')\n                                : global.removeAllListeners('unhandledrejection');\n                            if (originalHandlers) {\n                                originalHandlers.forEach((handler) => {\n                                    if (isNode) {\n                                        process.on('unhandledRejection', handler);\n                                    }\n                                    else {\n                                        global.addEventListener('unhandledrejection', handler);\n                                    }\n                                });\n                            }\n                            return result;\n                        };\n                    }\n                    return instance;\n                };\n            }\n        }\n        // Monkey patch all of the jasmine DSL so that each function runs in appropriate zone.\n        const jasmineEnv = jasmine.getEnv();\n        ['describe', 'xdescribe', 'fdescribe'].forEach((methodName) => {\n            let originalJasmineFn = jasmineEnv[methodName];\n            jasmineEnv[methodName] = function (description, specDefinitions) {\n                return originalJasmineFn.call(this, description, wrapDescribeInZone(description, specDefinitions));\n            };\n        });\n        ['it', 'xit', 'fit'].forEach((methodName) => {\n            let originalJasmineFn = jasmineEnv[methodName];\n            jasmineEnv[symbol(methodName)] = originalJasmineFn;\n            jasmineEnv[methodName] = function (description, specDefinitions, timeout) {\n                arguments[1] = wrapTestInZone(specDefinitions);\n                return originalJasmineFn.apply(this, arguments);\n            };\n        });\n        ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach((methodName) => {\n            let originalJasmineFn = jasmineEnv[methodName];\n            jasmineEnv[symbol(methodName)] = originalJasmineFn;\n            jasmineEnv[methodName] = function (specDefinitions, timeout) {\n                arguments[0] = wrapTestInZone(specDefinitions);\n                return originalJasmineFn.apply(this, arguments);\n            };\n        });\n        if (!disablePatchingJasmineClock) {\n            // need to patch jasmine.clock().mockDate and jasmine.clock().tick() so\n            // they can work properly in FakeAsyncTest\n            const originalClockFn = (jasmine[symbol('clock')] = jasmine['clock']);\n            jasmine['clock'] = function () {\n                const clock = originalClockFn.apply(this, arguments);\n                if (!clock[symbol('patched')]) {\n                    clock[symbol('patched')] = symbol('patched');\n                    const originalTick = (clock[symbol('tick')] = clock.tick);\n                    clock.tick = function () {\n                        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                        if (fakeAsyncZoneSpec) {\n                            return fakeAsyncZoneSpec.tick.apply(fakeAsyncZoneSpec, arguments);\n                        }\n                        return originalTick.apply(this, arguments);\n                    };\n                    const originalMockDate = (clock[symbol('mockDate')] = clock.mockDate);\n                    clock.mockDate = function () {\n                        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                        if (fakeAsyncZoneSpec) {\n                            const dateTime = arguments.length > 0 ? arguments[0] : new Date();\n                            return fakeAsyncZoneSpec.setFakeBaseSystemTime.apply(fakeAsyncZoneSpec, dateTime && typeof dateTime.getTime === 'function'\n                                ? [dateTime.getTime()]\n                                : arguments);\n                        }\n                        return originalMockDate.apply(this, arguments);\n                    };\n                    // for auto go into fakeAsync feature, we need the flag to enable it\n                    if (enableAutoFakeAsyncWhenClockPatched) {\n                        ['install', 'uninstall'].forEach((methodName) => {\n                            const originalClockFn = (clock[symbol(methodName)] = clock[methodName]);\n                            clock[methodName] = function () {\n                                const FakeAsyncTestZoneSpec = Zone['FakeAsyncTestZoneSpec'];\n                                if (FakeAsyncTestZoneSpec) {\n                                    jasmine[symbol('clockInstalled')] = 'install' === methodName;\n                                    return;\n                                }\n                                return originalClockFn.apply(this, arguments);\n                            };\n                        });\n                    }\n                }\n                return clock;\n            };\n        }\n        // monkey patch createSpyObj to make properties enumerable to true\n        if (!jasmine[Zone.__symbol__('createSpyObj')]) {\n            const originalCreateSpyObj = jasmine.createSpyObj;\n            jasmine[Zone.__symbol__('createSpyObj')] = originalCreateSpyObj;\n            jasmine.createSpyObj = function () {\n                const args = Array.prototype.slice.call(arguments);\n                const propertyNames = args.length >= 3 ? args[2] : null;\n                let spyObj;\n                if (propertyNames) {\n                    const defineProperty = Object.defineProperty;\n                    Object.defineProperty = function (obj, p, attributes) {\n                        return defineProperty.call(this, obj, p, {\n                            ...attributes,\n                            configurable: true,\n                            enumerable: true,\n                        });\n                    };\n                    try {\n                        spyObj = originalCreateSpyObj.apply(this, args);\n                    }\n                    finally {\n                        Object.defineProperty = defineProperty;\n                    }\n                }\n                else {\n                    spyObj = originalCreateSpyObj.apply(this, args);\n                }\n                return spyObj;\n            };\n        }\n        /**\n         * Gets a function wrapping the body of a Jasmine `describe` block to execute in a\n         * synchronous-only zone.\n         */\n        function wrapDescribeInZone(description, describeBody) {\n            return function () {\n                // Create a synchronous-only zone in which to run `describe` blocks in order to raise an\n                // error if any asynchronous operations are attempted inside of a `describe`.\n                const syncZone = ambientZone.fork(new SyncTestZoneSpec(`jasmine.describe#${description}`));\n                return syncZone.run(describeBody, this, arguments);\n            };\n        }\n        function runInTestZone(testBody, applyThis, queueRunner, done) {\n            const isClockInstalled = !!jasmine[symbol('clockInstalled')];\n            queueRunner.testProxyZoneSpec;\n            const testProxyZone = queueRunner.testProxyZone;\n            if (isClockInstalled && enableAutoFakeAsyncWhenClockPatched) {\n                // auto run a fakeAsync\n                const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n                if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n                    testBody = fakeAsyncModule.fakeAsync(testBody);\n                }\n            }\n            if (done) {\n                return testProxyZone.run(testBody, applyThis, [done]);\n            }\n            else {\n                return testProxyZone.run(testBody, applyThis);\n            }\n        }\n        /**\n         * Gets a function wrapping the body of a Jasmine `it/beforeEach/afterEach` block to\n         * execute in a ProxyZone zone.\n         * This will run in `testProxyZone`. The `testProxyZone` will be reset by the `ZoneQueueRunner`\n         */\n        function wrapTestInZone(testBody) {\n            // The `done` callback is only passed through if the function expects at least one argument.\n            // Note we have to make a function with correct number of arguments, otherwise jasmine will\n            // think that all functions are sync or async.\n            return (testBody &&\n                (testBody.length\n                    ? function (done) {\n                        return runInTestZone(testBody, this, this.queueRunner, done);\n                    }\n                    : function () {\n                        return runInTestZone(testBody, this, this.queueRunner);\n                    }));\n        }\n        const QueueRunner = jasmine.QueueRunner;\n        jasmine.QueueRunner = (function (_super) {\n            __extends(ZoneQueueRunner, _super);\n            function ZoneQueueRunner(attrs) {\n                if (attrs.onComplete) {\n                    attrs.onComplete = ((fn) => () => {\n                        // All functions are done, clear the test zone.\n                        this.testProxyZone = null;\n                        this.testProxyZoneSpec = null;\n                        ambientZone.scheduleMicroTask('jasmine.onComplete', fn);\n                    })(attrs.onComplete);\n                }\n                const nativeSetTimeout = global[Zone.__symbol__('setTimeout')];\n                const nativeClearTimeout = global[Zone.__symbol__('clearTimeout')];\n                if (nativeSetTimeout) {\n                    // should run setTimeout inside jasmine outside of zone\n                    attrs.timeout = {\n                        setTimeout: nativeSetTimeout ? nativeSetTimeout : global.setTimeout,\n                        clearTimeout: nativeClearTimeout ? nativeClearTimeout : global.clearTimeout,\n                    };\n                }\n                // create a userContext to hold the queueRunner itself\n                // so we can access the testProxy in it/xit/beforeEach ...\n                if (jasmine.UserContext) {\n                    if (!attrs.userContext) {\n                        attrs.userContext = new jasmine.UserContext();\n                    }\n                    attrs.userContext.queueRunner = this;\n                }\n                else {\n                    if (!attrs.userContext) {\n                        attrs.userContext = {};\n                    }\n                    attrs.userContext.queueRunner = this;\n                }\n                // patch attrs.onException\n                const onException = attrs.onException;\n                attrs.onException = function (error) {\n                    if (error &&\n                        error.message ===\n                            'Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL.') {\n                        // jasmine timeout, we can make the error message more\n                        // reasonable to tell what tasks are pending\n                        const proxyZoneSpec = this && this.testProxyZoneSpec;\n                        if (proxyZoneSpec) {\n                            const pendingTasksInfo = proxyZoneSpec.getAndClearPendingTasksInfo();\n                            try {\n                                // try catch here in case error.message is not writable\n                                error.message += pendingTasksInfo;\n                            }\n                            catch (err) { }\n                        }\n                    }\n                    if (onException) {\n                        onException.call(this, error);\n                    }\n                };\n                _super.call(this, attrs);\n            }\n            ZoneQueueRunner.prototype.execute = function () {\n                let zone = Zone.current;\n                let isChildOfAmbientZone = false;\n                while (zone) {\n                    if (zone === ambientZone) {\n                        isChildOfAmbientZone = true;\n                        break;\n                    }\n                    zone = zone.parent;\n                }\n                if (!isChildOfAmbientZone)\n                    throw new Error('Unexpected Zone: ' + Zone.current.name);\n                // This is the zone which will be used for running individual tests.\n                // It will be a proxy zone, so that the tests function can retroactively install\n                // different zones.\n                // Example:\n                //   - In beforeEach() do childZone = Zone.current.fork(...);\n                //   - In it() try to do fakeAsync(). The issue is that because the beforeEach forked the\n                //     zone outside of fakeAsync it will be able to escape the fakeAsync rules.\n                //   - Because ProxyZone is parent fo `childZone` fakeAsync can retroactively add\n                //     fakeAsync behavior to the childZone.\n                this.testProxyZoneSpec = new ProxyZoneSpec();\n                this.testProxyZone = ambientZone.fork(this.testProxyZoneSpec);\n                if (!Zone.currentTask) {\n                    // if we are not running in a task then if someone would register a\n                    // element.addEventListener and then calling element.click() the\n                    // addEventListener callback would think that it is the top most task and would\n                    // drain the microtask queue on element.click() which would be incorrect.\n                    // For this reason we always force a task when running jasmine tests.\n                    Zone.current.scheduleMicroTask('jasmine.execute().forceTask', () => QueueRunner.prototype.execute.call(this));\n                }\n                else {\n                    _super.prototype.execute.call(this);\n                }\n            };\n            return ZoneQueueRunner;\n        })(QueueRunner);\n    });\n}\n\nfunction patchJest(Zone) {\n    Zone.__load_patch('jest', (context, Zone, api) => {\n        if (typeof jest === 'undefined' || jest['__zone_patch__']) {\n            return;\n        }\n        // From jest 29 and jest-preset-angular v13, the module transform logic\n        // changed, and now jest-preset-angular use the use the tsconfig target\n        // other than the hardcoded one, https://github.com/thymikee/jest-preset-angular/issues/2010\n        // But jest-angular-preset doesn't introduce the @babel/plugin-transform-async-to-generator\n        // which is needed by angular since `async/await` still need to be transformed\n        // to promise for ES2017+ target.\n        // So for now, we disable to output the uncaught error console log for a temp solution,\n        // until jest-preset-angular find a proper solution.\n        Zone[api.symbol('ignoreConsoleErrorUncaughtError')] = true;\n        jest['__zone_patch__'] = true;\n        const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n        const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n        if (!ProxyZoneSpec) {\n            throw new Error('Missing ProxyZoneSpec');\n        }\n        const rootZone = Zone.current;\n        const syncZone = rootZone.fork(new SyncTestZoneSpec('jest.describe'));\n        const proxyZoneSpec = new ProxyZoneSpec();\n        const proxyZone = rootZone.fork(proxyZoneSpec);\n        function wrapDescribeFactoryInZone(originalJestFn) {\n            return function (...tableArgs) {\n                const originalDescribeFn = originalJestFn.apply(this, tableArgs);\n                return function (...args) {\n                    args[1] = wrapDescribeInZone(args[1]);\n                    return originalDescribeFn.apply(this, args);\n                };\n            };\n        }\n        function wrapTestFactoryInZone(originalJestFn) {\n            return function (...tableArgs) {\n                return function (...args) {\n                    args[1] = wrapTestInZone(args[1]);\n                    return originalJestFn.apply(this, tableArgs).apply(this, args);\n                };\n            };\n        }\n        /**\n         * Gets a function wrapping the body of a jest `describe` block to execute in a\n         * synchronous-only zone.\n         */\n        function wrapDescribeInZone(describeBody) {\n            return function (...args) {\n                return syncZone.run(describeBody, this, args);\n            };\n        }\n        /**\n         * Gets a function wrapping the body of a jest `it/beforeEach/afterEach` block to\n         * execute in a ProxyZone zone.\n         * This will run in the `proxyZone`.\n         */\n        function wrapTestInZone(testBody, isTestFunc = false) {\n            if (typeof testBody !== 'function') {\n                return testBody;\n            }\n            const wrappedFunc = function () {\n                if (Zone[api.symbol('useFakeTimersCalled')] === true &&\n                    testBody &&\n                    !testBody.isFakeAsync) {\n                    // jest.useFakeTimers is called, run into fakeAsyncTest automatically.\n                    const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n                    if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n                        testBody = fakeAsyncModule.fakeAsync(testBody);\n                    }\n                }\n                proxyZoneSpec.isTestFunc = isTestFunc;\n                return proxyZone.run(testBody, null, arguments);\n            };\n            // Update the length of wrappedFunc to be the same as the length of the testBody\n            // So jest core can handle whether the test function has `done()` or not correctly\n            Object.defineProperty(wrappedFunc, 'length', {\n                configurable: true,\n                writable: true,\n                enumerable: false,\n            });\n            wrappedFunc.length = testBody.length;\n            return wrappedFunc;\n        }\n        ['describe', 'xdescribe', 'fdescribe'].forEach((methodName) => {\n            let originalJestFn = context[methodName];\n            if (context[Zone.__symbol__(methodName)]) {\n                return;\n            }\n            context[Zone.__symbol__(methodName)] = originalJestFn;\n            context[methodName] = function (...args) {\n                args[1] = wrapDescribeInZone(args[1]);\n                return originalJestFn.apply(this, args);\n            };\n            context[methodName].each = wrapDescribeFactoryInZone(originalJestFn.each);\n        });\n        context.describe.only = context.fdescribe;\n        context.describe.skip = context.xdescribe;\n        ['it', 'xit', 'fit', 'test', 'xtest'].forEach((methodName) => {\n            let originalJestFn = context[methodName];\n            if (context[Zone.__symbol__(methodName)]) {\n                return;\n            }\n            context[Zone.__symbol__(methodName)] = originalJestFn;\n            context[methodName] = function (...args) {\n                args[1] = wrapTestInZone(args[1], true);\n                return originalJestFn.apply(this, args);\n            };\n            context[methodName].each = wrapTestFactoryInZone(originalJestFn.each);\n            context[methodName].todo = originalJestFn.todo;\n            context[methodName].failing = originalJestFn.failing;\n        });\n        context.it.only = context.fit;\n        context.it.skip = context.xit;\n        context.test.only = context.fit;\n        context.test.skip = context.xit;\n        ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach((methodName) => {\n            let originalJestFn = context[methodName];\n            if (context[Zone.__symbol__(methodName)]) {\n                return;\n            }\n            context[Zone.__symbol__(methodName)] = originalJestFn;\n            context[methodName] = function (...args) {\n                args[0] = wrapTestInZone(args[0]);\n                return originalJestFn.apply(this, args);\n            };\n        });\n        Zone.patchJestObject = function patchJestObject(Timer, isModern = false) {\n            // check whether currently the test is inside fakeAsync()\n            function isPatchingFakeTimer() {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                return !!fakeAsyncZoneSpec;\n            }\n            // check whether the current function is inside `test/it` or other methods\n            // such as `describe/beforeEach`\n            function isInTestFunc() {\n                const proxyZoneSpec = Zone.current.get('ProxyZoneSpec');\n                return proxyZoneSpec && proxyZoneSpec.isTestFunc;\n            }\n            if (Timer[api.symbol('fakeTimers')]) {\n                return;\n            }\n            Timer[api.symbol('fakeTimers')] = true;\n            // patch jest fakeTimer internal method to make sure no console.warn print out\n            api.patchMethod(Timer, '_checkFakeTimers', (delegate) => {\n                return function (self, args) {\n                    if (isPatchingFakeTimer()) {\n                        return true;\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch useFakeTimers(), set useFakeTimersCalled flag, and make test auto run into fakeAsync\n            api.patchMethod(Timer, 'useFakeTimers', (delegate) => {\n                return function (self, args) {\n                    Zone[api.symbol('useFakeTimersCalled')] = true;\n                    if (isModern || isInTestFunc()) {\n                        return delegate.apply(self, args);\n                    }\n                    return self;\n                };\n            });\n            // patch useRealTimers(), unset useFakeTimers flag\n            api.patchMethod(Timer, 'useRealTimers', (delegate) => {\n                return function (self, args) {\n                    Zone[api.symbol('useFakeTimersCalled')] = false;\n                    if (isModern || isInTestFunc()) {\n                        return delegate.apply(self, args);\n                    }\n                    return self;\n                };\n            });\n            // patch setSystemTime(), call setCurrentRealTime() in the fakeAsyncTest\n            api.patchMethod(Timer, 'setSystemTime', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n                        fakeAsyncZoneSpec.setFakeBaseSystemTime(args[0]);\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch getSystemTime(), call getCurrentRealTime() in the fakeAsyncTest\n            api.patchMethod(Timer, 'getRealSystemTime', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n                        return fakeAsyncZoneSpec.getRealSystemTime();\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch runAllTicks(), run all microTasks inside fakeAsync\n            api.patchMethod(Timer, 'runAllTicks', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.flushMicrotasks();\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch runAllTimers(), run all macroTasks inside fakeAsync\n            api.patchMethod(Timer, 'runAllTimers', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.flush(100, true);\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch advanceTimersByTime(), call tick() in the fakeAsyncTest\n            api.patchMethod(Timer, 'advanceTimersByTime', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.tick(args[0]);\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch runOnlyPendingTimers(), call flushOnlyPendingTimers() in the fakeAsyncTest\n            api.patchMethod(Timer, 'runOnlyPendingTimers', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.flushOnlyPendingTimers();\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch advanceTimersToNextTimer(), call tickToNext() in the fakeAsyncTest\n            api.patchMethod(Timer, 'advanceTimersToNextTimer', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.tickToNext(args[0]);\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch clearAllTimers(), call removeAllTimers() in the fakeAsyncTest\n            api.patchMethod(Timer, 'clearAllTimers', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        fakeAsyncZoneSpec.removeAllTimers();\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n            // patch getTimerCount(), call getTimerCount() in the fakeAsyncTest\n            api.patchMethod(Timer, 'getTimerCount', (delegate) => {\n                return function (self, args) {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        return fakeAsyncZoneSpec.getTimerCount();\n                    }\n                    else {\n                        return delegate.apply(self, args);\n                    }\n                };\n            });\n        };\n    });\n}\n\nfunction patchMocha(Zone) {\n    Zone.__load_patch('mocha', (global, Zone) => {\n        const Mocha = global.Mocha;\n        if (typeof Mocha === 'undefined') {\n            // return if Mocha is not available, because now zone-testing\n            // will load mocha patch with jasmine/jest patch\n            return;\n        }\n        if (typeof Zone === 'undefined') {\n            throw new Error('Missing Zone.js');\n        }\n        const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n        const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n        if (!ProxyZoneSpec) {\n            throw new Error('Missing ProxyZoneSpec');\n        }\n        if (Mocha['__zone_patch__']) {\n            throw new Error('\"Mocha\" has already been patched with \"Zone\".');\n        }\n        Mocha['__zone_patch__'] = true;\n        const rootZone = Zone.current;\n        const syncZone = rootZone.fork(new SyncTestZoneSpec('Mocha.describe'));\n        let testZone = null;\n        const suiteZone = rootZone.fork(new ProxyZoneSpec());\n        const mochaOriginal = {\n            after: global.after,\n            afterEach: global.afterEach,\n            before: global.before,\n            beforeEach: global.beforeEach,\n            describe: global.describe,\n            it: global.it,\n        };\n        function modifyArguments(args, syncTest, asyncTest) {\n            for (let i = 0; i < args.length; i++) {\n                let arg = args[i];\n                if (typeof arg === 'function') {\n                    // The `done` callback is only passed through if the function expects at\n                    // least one argument.\n                    // Note we have to make a function with correct number of arguments,\n                    // otherwise mocha will\n                    // think that all functions are sync or async.\n                    args[i] = arg.length === 0 ? syncTest(arg) : asyncTest(arg);\n                    // Mocha uses toString to view the test body in the result list, make sure we return the\n                    // correct function body\n                    args[i].toString = function () {\n                        return arg.toString();\n                    };\n                }\n            }\n            return args;\n        }\n        function wrapDescribeInZone(args) {\n            const syncTest = function (fn) {\n                return function () {\n                    return syncZone.run(fn, this, arguments);\n                };\n            };\n            return modifyArguments(args, syncTest);\n        }\n        function wrapTestInZone(args) {\n            const asyncTest = function (fn) {\n                return function (done) {\n                    return testZone.run(fn, this, [done]);\n                };\n            };\n            const syncTest = function (fn) {\n                return function () {\n                    return testZone.run(fn, this);\n                };\n            };\n            return modifyArguments(args, syncTest, asyncTest);\n        }\n        function wrapSuiteInZone(args) {\n            const asyncTest = function (fn) {\n                return function (done) {\n                    return suiteZone.run(fn, this, [done]);\n                };\n            };\n            const syncTest = function (fn) {\n                return function () {\n                    return suiteZone.run(fn, this);\n                };\n            };\n            return modifyArguments(args, syncTest, asyncTest);\n        }\n        global.describe = global.suite = function () {\n            return mochaOriginal.describe.apply(this, wrapDescribeInZone(arguments));\n        };\n        global.xdescribe =\n            global.suite.skip =\n                global.describe.skip =\n                    function () {\n                        return mochaOriginal.describe.skip.apply(this, wrapDescribeInZone(arguments));\n                    };\n        global.describe.only = global.suite.only = function () {\n            return mochaOriginal.describe.only.apply(this, wrapDescribeInZone(arguments));\n        };\n        global.it =\n            global.specify =\n                global.test =\n                    function () {\n                        return mochaOriginal.it.apply(this, wrapTestInZone(arguments));\n                    };\n        global.xit =\n            global.xspecify =\n                global.it.skip =\n                    function () {\n                        return mochaOriginal.it.skip.apply(this, wrapTestInZone(arguments));\n                    };\n        global.it.only = global.test.only = function () {\n            return mochaOriginal.it.only.apply(this, wrapTestInZone(arguments));\n        };\n        global.after = global.suiteTeardown = function () {\n            return mochaOriginal.after.apply(this, wrapSuiteInZone(arguments));\n        };\n        global.afterEach = global.teardown = function () {\n            return mochaOriginal.afterEach.apply(this, wrapTestInZone(arguments));\n        };\n        global.before = global.suiteSetup = function () {\n            return mochaOriginal.before.apply(this, wrapSuiteInZone(arguments));\n        };\n        global.beforeEach = global.setup = function () {\n            return mochaOriginal.beforeEach.apply(this, wrapTestInZone(arguments));\n        };\n        ((originalRunTest, originalRun) => {\n            Mocha.Runner.prototype.runTest = function (fn) {\n                Zone.current.scheduleMicroTask('mocha.forceTask', () => {\n                    originalRunTest.call(this, fn);\n                });\n            };\n            Mocha.Runner.prototype.run = function (fn) {\n                this.on('test', (e) => {\n                    testZone = rootZone.fork(new ProxyZoneSpec());\n                });\n                this.on('fail', (test, err) => {\n                    const proxyZoneSpec = testZone && testZone.get('ProxyZoneSpec');\n                    if (proxyZoneSpec && err) {\n                        try {\n                            // try catch here in case err.message is not writable\n                            err.message += proxyZoneSpec.getAndClearPendingTasksInfo();\n                        }\n                        catch (error) { }\n                    }\n                });\n                return originalRun.call(this, fn);\n            };\n        })(Mocha.Runner.prototype.runTest, Mocha.Runner.prototype.run);\n    });\n}\n\nconst global$2 = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n    const symbolPrefix = global$2['__Zone_symbol_prefix'] || '__zone_symbol__';\n    return symbolPrefix + name;\n}\n\nconst __global = (typeof window !== 'undefined' && window) || (typeof self !== 'undefined' && self) || global;\nclass AsyncTestZoneSpec {\n    finishCallback;\n    failCallback;\n    // Needs to be a getter and not a plain property in order run this just-in-time. Otherwise\n    // `__symbol__` would be evaluated during top-level execution prior to the Zone prefix being\n    // changed for tests.\n    static get symbolParentUnresolved() {\n        return __symbol__('parentUnresolved');\n    }\n    _pendingMicroTasks = false;\n    _pendingMacroTasks = false;\n    _alreadyErrored = false;\n    _isSync = false;\n    _existingFinishTimer = null;\n    entryFunction = null;\n    runZone = Zone.current;\n    unresolvedChainedPromiseCount = 0;\n    supportWaitUnresolvedChainedPromise = false;\n    constructor(finishCallback, failCallback, namePrefix) {\n        this.finishCallback = finishCallback;\n        this.failCallback = failCallback;\n        this.name = 'asyncTestZone for ' + namePrefix;\n        this.properties = { 'AsyncTestZoneSpec': this };\n        this.supportWaitUnresolvedChainedPromise =\n            __global[__symbol__('supportWaitUnResolvedChainedPromise')] === true;\n    }\n    isUnresolvedChainedPromisePending() {\n        return this.unresolvedChainedPromiseCount > 0;\n    }\n    _finishCallbackIfDone() {\n        // NOTE: Technically the `onHasTask` could fire together with the initial synchronous\n        // completion in `onInvoke`. `onHasTask` might call this method when it captured e.g.\n        // microtasks in the proxy zone that now complete as part of this async zone run.\n        // Consider the following scenario:\n        //    1. A test `beforeEach` schedules a microtask in the ProxyZone.\n        //    2. An actual empty `it` spec executes in the AsyncTestZone` (using e.g. `waitForAsync`).\n        //    3. The `onInvoke` invokes `_finishCallbackIfDone` because the spec runs synchronously.\n        //    4. We wait the scheduled timeout (see below) to account for unhandled promises.\n        //    5. The microtask from (1) finishes and `onHasTask` is invoked.\n        //    --> We register a second `_finishCallbackIfDone` even though we have scheduled a timeout.\n        // If the finish timeout from below is already scheduled, terminate the existing scheduled\n        // finish invocation, avoiding calling `jasmine` `done` multiple times. *Note* that we would\n        // want to schedule a new finish callback in case the task state changes again.\n        if (this._existingFinishTimer !== null) {\n            clearTimeout(this._existingFinishTimer);\n            this._existingFinishTimer = null;\n        }\n        if (!(this._pendingMicroTasks ||\n            this._pendingMacroTasks ||\n            (this.supportWaitUnresolvedChainedPromise && this.isUnresolvedChainedPromisePending()))) {\n            // We wait until the next tick because we would like to catch unhandled promises which could\n            // cause test logic to be executed. In such cases we cannot finish with tasks pending then.\n            this.runZone.run(() => {\n                this._existingFinishTimer = setTimeout(() => {\n                    if (!this._alreadyErrored && !(this._pendingMicroTasks || this._pendingMacroTasks)) {\n                        this.finishCallback();\n                    }\n                }, 0);\n            });\n        }\n    }\n    patchPromiseForTest() {\n        if (!this.supportWaitUnresolvedChainedPromise) {\n            return;\n        }\n        const patchPromiseForTest = Promise[Zone.__symbol__('patchPromiseForTest')];\n        if (patchPromiseForTest) {\n            patchPromiseForTest();\n        }\n    }\n    unPatchPromiseForTest() {\n        if (!this.supportWaitUnresolvedChainedPromise) {\n            return;\n        }\n        const unPatchPromiseForTest = Promise[Zone.__symbol__('unPatchPromiseForTest')];\n        if (unPatchPromiseForTest) {\n            unPatchPromiseForTest();\n        }\n    }\n    // ZoneSpec implementation below.\n    name;\n    properties;\n    onScheduleTask(delegate, current, target, task) {\n        if (task.type !== 'eventTask') {\n            this._isSync = false;\n        }\n        if (task.type === 'microTask' && task.data && task.data instanceof Promise) {\n            // check whether the promise is a chained promise\n            if (task.data[AsyncTestZoneSpec.symbolParentUnresolved] === true) {\n                // chained promise is being scheduled\n                this.unresolvedChainedPromiseCount--;\n            }\n        }\n        return delegate.scheduleTask(target, task);\n    }\n    onInvokeTask(delegate, current, target, task, applyThis, applyArgs) {\n        if (task.type !== 'eventTask') {\n            this._isSync = false;\n        }\n        return delegate.invokeTask(target, task, applyThis, applyArgs);\n    }\n    onCancelTask(delegate, current, target, task) {\n        if (task.type !== 'eventTask') {\n            this._isSync = false;\n        }\n        return delegate.cancelTask(target, task);\n    }\n    // Note - we need to use onInvoke at the moment to call finish when a test is\n    // fully synchronous. TODO(juliemr): remove this when the logic for\n    // onHasTask changes and it calls whenever the task queues are dirty.\n    // updated by(JiaLiPassion), only call finish callback when no task\n    // was scheduled/invoked/canceled.\n    onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n        if (!this.entryFunction) {\n            this.entryFunction = delegate;\n        }\n        try {\n            this._isSync = true;\n            return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n        }\n        finally {\n            // We need to check the delegate is the same as entryFunction or not.\n            // Consider the following case.\n            //\n            // asyncTestZone.run(() => { // Here the delegate will be the entryFunction\n            //   Zone.current.run(() => { // Here the delegate will not be the entryFunction\n            //   });\n            // });\n            //\n            // We only want to check whether there are async tasks scheduled\n            // for the entry function.\n            if (this._isSync && this.entryFunction === delegate) {\n                this._finishCallbackIfDone();\n            }\n        }\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n        // Let the parent try to handle the error.\n        const result = parentZoneDelegate.handleError(targetZone, error);\n        if (result) {\n            this.failCallback(error);\n            this._alreadyErrored = true;\n        }\n        return false;\n    }\n    onHasTask(delegate, current, target, hasTaskState) {\n        delegate.hasTask(target, hasTaskState);\n        // We should only trigger finishCallback when the target zone is the AsyncTestZone\n        // Consider the following cases.\n        //\n        // const childZone = asyncTestZone.fork({\n        //   name: 'child',\n        //   onHasTask: ...\n        // });\n        //\n        // So we have nested zones declared the onHasTask hook, in this case,\n        // the onHasTask will be triggered twice, and cause the finishCallbackIfDone()\n        // is also be invoked twice. So we need to only trigger the finishCallbackIfDone()\n        // when the current zone is the same as the target zone.\n        if (current !== target) {\n            return;\n        }\n        if (hasTaskState.change == 'microTask') {\n            this._pendingMicroTasks = hasTaskState.microTask;\n            this._finishCallbackIfDone();\n        }\n        else if (hasTaskState.change == 'macroTask') {\n            this._pendingMacroTasks = hasTaskState.macroTask;\n            this._finishCallbackIfDone();\n        }\n    }\n}\nfunction patchAsyncTest(Zone) {\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['AsyncTestZoneSpec'] = AsyncTestZoneSpec;\n    Zone.__load_patch('asynctest', (global, Zone, api) => {\n        /**\n         * Wraps a test function in an asynchronous test zone. The test will automatically\n         * complete when all asynchronous calls within this zone are done.\n         */\n        Zone[api.symbol('asyncTest')] = function asyncTest(fn) {\n            // If we're running using the Jasmine test framework, adapt to call the 'done'\n            // function when asynchronous activity is finished.\n            if (global.jasmine) {\n                // Not using an arrow function to preserve context passed from call site\n                return function (done) {\n                    if (!done) {\n                        // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n                        // fake it here and assume sync.\n                        done = function () { };\n                        done.fail = function (e) {\n                            throw e;\n                        };\n                    }\n                    runInTestZone(fn, this, done, (err) => {\n                        if (typeof err === 'string') {\n                            return done.fail(new Error(err));\n                        }\n                        else {\n                            done.fail(err);\n                        }\n                    });\n                };\n            }\n            // Otherwise, return a promise which will resolve when asynchronous activity\n            // is finished. This will be correctly consumed by the Mocha framework with\n            // it('...', async(myFn)); or can be used in a custom framework.\n            // Not using an arrow function to preserve context passed from call site\n            return function () {\n                return new Promise((finishCallback, failCallback) => {\n                    runInTestZone(fn, this, finishCallback, failCallback);\n                });\n            };\n        };\n        function runInTestZone(fn, context, finishCallback, failCallback) {\n            const currentZone = Zone.current;\n            const AsyncTestZoneSpec = Zone['AsyncTestZoneSpec'];\n            if (AsyncTestZoneSpec === undefined) {\n                throw new Error('AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' +\n                    'Please make sure that your environment includes zone.js/plugins/async-test');\n            }\n            const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n            if (!ProxyZoneSpec) {\n                throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' +\n                    'Please make sure that your environment includes zone.js/plugins/proxy');\n            }\n            const proxyZoneSpec = ProxyZoneSpec.get();\n            ProxyZoneSpec.assertPresent();\n            // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n            // If we do it in ProxyZone then we will get to infinite recursion.\n            const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n            const previousDelegate = proxyZoneSpec.getDelegate();\n            proxyZone.parent.run(() => {\n                const testZoneSpec = new AsyncTestZoneSpec(() => {\n                    // Need to restore the original zone.\n                    if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n                        // Only reset the zone spec if it's\n                        // still this one. Otherwise, assume\n                        // it's OK.\n                        proxyZoneSpec.setDelegate(previousDelegate);\n                    }\n                    testZoneSpec.unPatchPromiseForTest();\n                    currentZone.run(() => {\n                        finishCallback();\n                    });\n                }, (error) => {\n                    // Need to restore the original zone.\n                    if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n                        // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n                        proxyZoneSpec.setDelegate(previousDelegate);\n                    }\n                    testZoneSpec.unPatchPromiseForTest();\n                    currentZone.run(() => {\n                        failCallback(error);\n                    });\n                }, 'test');\n                proxyZoneSpec.setDelegate(testZoneSpec);\n                testZoneSpec.patchPromiseForTest();\n            });\n            return Zone.current.runGuarded(fn, context);\n        }\n    });\n}\n\nconst global$1 = (typeof window === 'object' && window) || (typeof self === 'object' && self) || globalThis.global;\nconst OriginalDate = global$1.Date;\n// Since when we compile this file to `es2015`, and if we define\n// this `FakeDate` as `class FakeDate`, and then set `FakeDate.prototype`\n// there will be an error which is `Cannot assign to read only property 'prototype'`\n// so we need to use function implementation here.\nfunction FakeDate() {\n    if (arguments.length === 0) {\n        const d = new OriginalDate();\n        d.setTime(FakeDate.now());\n        return d;\n    }\n    else {\n        const args = Array.prototype.slice.call(arguments);\n        return new OriginalDate(...args);\n    }\n}\nFakeDate.now = function () {\n    const fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n    if (fakeAsyncTestZoneSpec) {\n        return fakeAsyncTestZoneSpec.getFakeSystemTime();\n    }\n    return OriginalDate.now.apply(this, arguments);\n};\nFakeDate.UTC = OriginalDate.UTC;\nFakeDate.parse = OriginalDate.parse;\n// keep a reference for zone patched timer function\nlet patchedTimers;\nconst timeoutCallback = function () { };\nclass Scheduler {\n    // Next scheduler id.\n    static nextNodeJSId = 1;\n    static nextId = -1;\n    // Scheduler queue with the tuple of end time and callback function - sorted by end time.\n    _schedulerQueue = [];\n    // Current simulated time in millis.\n    _currentTickTime = 0;\n    // Current fake system base time in millis.\n    _currentFakeBaseSystemTime = OriginalDate.now();\n    // track requeuePeriodicTimer\n    _currentTickRequeuePeriodicEntries = [];\n    constructor() { }\n    static getNextId() {\n        const id = patchedTimers.nativeSetTimeout.call(global$1, timeoutCallback, 0);\n        patchedTimers.nativeClearTimeout.call(global$1, id);\n        if (typeof id === 'number') {\n            return id;\n        }\n        // in NodeJS, we just use a number for fakeAsync, since it will not\n        // conflict with native TimeoutId\n        return Scheduler.nextNodeJSId++;\n    }\n    getCurrentTickTime() {\n        return this._currentTickTime;\n    }\n    getFakeSystemTime() {\n        return this._currentFakeBaseSystemTime + this._currentTickTime;\n    }\n    setFakeBaseSystemTime(fakeBaseSystemTime) {\n        this._currentFakeBaseSystemTime = fakeBaseSystemTime;\n    }\n    getRealSystemTime() {\n        return OriginalDate.now();\n    }\n    scheduleFunction(cb, delay, options) {\n        options = {\n            ...{\n                args: [],\n                isPeriodic: false,\n                isRequestAnimationFrame: false,\n                id: -1,\n                isRequeuePeriodic: false,\n            },\n            ...options,\n        };\n        let currentId = options.id < 0 ? Scheduler.nextId : options.id;\n        Scheduler.nextId = Scheduler.getNextId();\n        let endTime = this._currentTickTime + delay;\n        // Insert so that scheduler queue remains sorted by end time.\n        let newEntry = {\n            endTime: endTime,\n            id: currentId,\n            func: cb,\n            args: options.args,\n            delay: delay,\n            isPeriodic: options.isPeriodic,\n            isRequestAnimationFrame: options.isRequestAnimationFrame,\n        };\n        if (options.isRequeuePeriodic) {\n            this._currentTickRequeuePeriodicEntries.push(newEntry);\n        }\n        let i = 0;\n        for (; i < this._schedulerQueue.length; i++) {\n            let currentEntry = this._schedulerQueue[i];\n            if (newEntry.endTime < currentEntry.endTime) {\n                break;\n            }\n        }\n        this._schedulerQueue.splice(i, 0, newEntry);\n        return currentId;\n    }\n    removeScheduledFunctionWithId(id) {\n        for (let i = 0; i < this._schedulerQueue.length; i++) {\n            if (this._schedulerQueue[i].id == id) {\n                this._schedulerQueue.splice(i, 1);\n                break;\n            }\n        }\n    }\n    removeAll() {\n        this._schedulerQueue = [];\n    }\n    getTimerCount() {\n        return this._schedulerQueue.length;\n    }\n    tickToNext(step = 1, doTick, tickOptions) {\n        if (this._schedulerQueue.length < step) {\n            return;\n        }\n        // Find the last task currently queued in the scheduler queue and tick\n        // till that time.\n        const startTime = this._currentTickTime;\n        const targetTask = this._schedulerQueue[step - 1];\n        this.tick(targetTask.endTime - startTime, doTick, tickOptions);\n    }\n    tick(millis = 0, doTick, tickOptions) {\n        let finalTime = this._currentTickTime + millis;\n        let lastCurrentTime = 0;\n        tickOptions = Object.assign({ processNewMacroTasksSynchronously: true }, tickOptions);\n        // we need to copy the schedulerQueue so nested timeout\n        // will not be wrongly called in the current tick\n        // https://github.com/angular/angular/issues/33799\n        const schedulerQueue = tickOptions.processNewMacroTasksSynchronously\n            ? this._schedulerQueue\n            : this._schedulerQueue.slice();\n        if (schedulerQueue.length === 0 && doTick) {\n            doTick(millis);\n            return;\n        }\n        while (schedulerQueue.length > 0) {\n            // clear requeueEntries before each loop\n            this._currentTickRequeuePeriodicEntries = [];\n            let current = schedulerQueue[0];\n            if (finalTime < current.endTime) {\n                // Done processing the queue since it's sorted by endTime.\n                break;\n            }\n            else {\n                // Time to run scheduled function. Remove it from the head of queue.\n                let current = schedulerQueue.shift();\n                if (!tickOptions.processNewMacroTasksSynchronously) {\n                    const idx = this._schedulerQueue.indexOf(current);\n                    if (idx >= 0) {\n                        this._schedulerQueue.splice(idx, 1);\n                    }\n                }\n                lastCurrentTime = this._currentTickTime;\n                this._currentTickTime = current.endTime;\n                if (doTick) {\n                    doTick(this._currentTickTime - lastCurrentTime);\n                }\n                let retval = current.func.apply(global$1, current.isRequestAnimationFrame ? [this._currentTickTime] : current.args);\n                if (!retval) {\n                    // Uncaught exception in the current scheduled function. Stop processing the queue.\n                    break;\n                }\n                // check is there any requeue periodic entry is added in\n                // current loop, if there is, we need to add to current loop\n                if (!tickOptions.processNewMacroTasksSynchronously) {\n                    this._currentTickRequeuePeriodicEntries.forEach((newEntry) => {\n                        let i = 0;\n                        for (; i < schedulerQueue.length; i++) {\n                            const currentEntry = schedulerQueue[i];\n                            if (newEntry.endTime < currentEntry.endTime) {\n                                break;\n                            }\n                        }\n                        schedulerQueue.splice(i, 0, newEntry);\n                    });\n                }\n            }\n        }\n        lastCurrentTime = this._currentTickTime;\n        this._currentTickTime = finalTime;\n        if (doTick) {\n            doTick(this._currentTickTime - lastCurrentTime);\n        }\n    }\n    flushOnlyPendingTimers(doTick) {\n        if (this._schedulerQueue.length === 0) {\n            return 0;\n        }\n        // Find the last task currently queued in the scheduler queue and tick\n        // till that time.\n        const startTime = this._currentTickTime;\n        const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n        this.tick(lastTask.endTime - startTime, doTick, { processNewMacroTasksSynchronously: false });\n        return this._currentTickTime - startTime;\n    }\n    flush(limit = 20, flushPeriodic = false, doTick) {\n        if (flushPeriodic) {\n            return this.flushPeriodic(doTick);\n        }\n        else {\n            return this.flushNonPeriodic(limit, doTick);\n        }\n    }\n    flushPeriodic(doTick) {\n        if (this._schedulerQueue.length === 0) {\n            return 0;\n        }\n        // Find the last task currently queued in the scheduler queue and tick\n        // till that time.\n        const startTime = this._currentTickTime;\n        const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n        this.tick(lastTask.endTime - startTime, doTick);\n        return this._currentTickTime - startTime;\n    }\n    flushNonPeriodic(limit, doTick) {\n        const startTime = this._currentTickTime;\n        let lastCurrentTime = 0;\n        let count = 0;\n        while (this._schedulerQueue.length > 0) {\n            count++;\n            if (count > limit) {\n                throw new Error('flush failed after reaching the limit of ' +\n                    limit +\n                    ' tasks. Does your code use a polling timeout?');\n            }\n            // flush only non-periodic timers.\n            // If the only remaining tasks are periodic(or requestAnimationFrame), finish flushing.\n            if (this._schedulerQueue.filter((task) => !task.isPeriodic && !task.isRequestAnimationFrame)\n                .length === 0) {\n                break;\n            }\n            const current = this._schedulerQueue.shift();\n            lastCurrentTime = this._currentTickTime;\n            this._currentTickTime = current.endTime;\n            if (doTick) {\n                // Update any secondary schedulers like Jasmine mock Date.\n                doTick(this._currentTickTime - lastCurrentTime);\n            }\n            const retval = current.func.apply(global$1, current.args);\n            if (!retval) {\n                // Uncaught exception in the current scheduled function. Stop processing the queue.\n                break;\n            }\n        }\n        return this._currentTickTime - startTime;\n    }\n}\nclass FakeAsyncTestZoneSpec {\n    trackPendingRequestAnimationFrame;\n    macroTaskOptions;\n    static assertInZone() {\n        if (Zone.current.get('FakeAsyncTestZoneSpec') == null) {\n            throw new Error('The code should be running in the fakeAsync zone to call this function');\n        }\n    }\n    _scheduler = new Scheduler();\n    _microtasks = [];\n    _lastError = null;\n    _uncaughtPromiseErrors = Promise[Zone.__symbol__('uncaughtPromiseErrors')];\n    pendingPeriodicTimers = [];\n    pendingTimers = [];\n    patchDateLocked = false;\n    constructor(namePrefix, trackPendingRequestAnimationFrame = false, macroTaskOptions) {\n        this.trackPendingRequestAnimationFrame = trackPendingRequestAnimationFrame;\n        this.macroTaskOptions = macroTaskOptions;\n        this.name = 'fakeAsyncTestZone for ' + namePrefix;\n        // in case user can't access the construction of FakeAsyncTestSpec\n        // user can also define macroTaskOptions by define a global variable.\n        if (!this.macroTaskOptions) {\n            this.macroTaskOptions = global$1[Zone.__symbol__('FakeAsyncTestMacroTask')];\n        }\n    }\n    _fnAndFlush(fn, completers) {\n        return (...args) => {\n            fn.apply(global$1, args);\n            if (this._lastError === null) {\n                // Success\n                if (completers.onSuccess != null) {\n                    completers.onSuccess.apply(global$1);\n                }\n                // Flush microtasks only on success.\n                this.flushMicrotasks();\n            }\n            else {\n                // Failure\n                if (completers.onError != null) {\n                    completers.onError.apply(global$1);\n                }\n            }\n            // Return true if there were no errors, false otherwise.\n            return this._lastError === null;\n        };\n    }\n    static _removeTimer(timers, id) {\n        let index = timers.indexOf(id);\n        if (index > -1) {\n            timers.splice(index, 1);\n        }\n    }\n    _dequeueTimer(id) {\n        return () => {\n            FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n        };\n    }\n    _requeuePeriodicTimer(fn, interval, args, id) {\n        return () => {\n            // Requeue the timer callback if it's not been canceled.\n            if (this.pendingPeriodicTimers.indexOf(id) !== -1) {\n                this._scheduler.scheduleFunction(fn, interval, {\n                    args,\n                    isPeriodic: true,\n                    id,\n                    isRequeuePeriodic: true,\n                });\n            }\n        };\n    }\n    _dequeuePeriodicTimer(id) {\n        return () => {\n            FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n        };\n    }\n    _setTimeout(fn, delay, args, isTimer = true) {\n        let removeTimerFn = this._dequeueTimer(Scheduler.nextId);\n        // Queue the callback and dequeue the timer on success and error.\n        let cb = this._fnAndFlush(fn, { onSuccess: removeTimerFn, onError: removeTimerFn });\n        let id = this._scheduler.scheduleFunction(cb, delay, { args, isRequestAnimationFrame: !isTimer });\n        if (isTimer) {\n            this.pendingTimers.push(id);\n        }\n        return id;\n    }\n    _clearTimeout(id) {\n        FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n        this._scheduler.removeScheduledFunctionWithId(id);\n    }\n    _setInterval(fn, interval, args) {\n        let id = Scheduler.nextId;\n        let completers = { onSuccess: null, onError: this._dequeuePeriodicTimer(id) };\n        let cb = this._fnAndFlush(fn, completers);\n        // Use the callback created above to requeue on success.\n        completers.onSuccess = this._requeuePeriodicTimer(cb, interval, args, id);\n        // Queue the callback and dequeue the periodic timer only on error.\n        this._scheduler.scheduleFunction(cb, interval, { args, isPeriodic: true });\n        this.pendingPeriodicTimers.push(id);\n        return id;\n    }\n    _clearInterval(id) {\n        FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n        this._scheduler.removeScheduledFunctionWithId(id);\n    }\n    _resetLastErrorAndThrow() {\n        let error = this._lastError || this._uncaughtPromiseErrors[0];\n        this._uncaughtPromiseErrors.length = 0;\n        this._lastError = null;\n        throw error;\n    }\n    getCurrentTickTime() {\n        return this._scheduler.getCurrentTickTime();\n    }\n    getFakeSystemTime() {\n        return this._scheduler.getFakeSystemTime();\n    }\n    setFakeBaseSystemTime(realTime) {\n        this._scheduler.setFakeBaseSystemTime(realTime);\n    }\n    getRealSystemTime() {\n        return this._scheduler.getRealSystemTime();\n    }\n    static patchDate() {\n        if (!!global$1[Zone.__symbol__('disableDatePatching')]) {\n            // we don't want to patch global Date\n            // because in some case, global Date\n            // is already being patched, we need to provide\n            // an option to let user still use their\n            // own version of Date.\n            return;\n        }\n        if (global$1['Date'] === FakeDate) {\n            // already patched\n            return;\n        }\n        global$1['Date'] = FakeDate;\n        FakeDate.prototype = OriginalDate.prototype;\n        // try check and reset timers\n        // because jasmine.clock().install() may\n        // have replaced the global timer\n        FakeAsyncTestZoneSpec.checkTimerPatch();\n    }\n    static resetDate() {\n        if (global$1['Date'] === FakeDate) {\n            global$1['Date'] = OriginalDate;\n        }\n    }\n    static checkTimerPatch() {\n        if (!patchedTimers) {\n            throw new Error('Expected timers to have been patched.');\n        }\n        if (global$1.setTimeout !== patchedTimers.setTimeout) {\n            global$1.setTimeout = patchedTimers.setTimeout;\n            global$1.clearTimeout = patchedTimers.clearTimeout;\n        }\n        if (global$1.setInterval !== patchedTimers.setInterval) {\n            global$1.setInterval = patchedTimers.setInterval;\n            global$1.clearInterval = patchedTimers.clearInterval;\n        }\n    }\n    lockDatePatch() {\n        this.patchDateLocked = true;\n        FakeAsyncTestZoneSpec.patchDate();\n    }\n    unlockDatePatch() {\n        this.patchDateLocked = false;\n        FakeAsyncTestZoneSpec.resetDate();\n    }\n    tickToNext(steps = 1, doTick, tickOptions = { processNewMacroTasksSynchronously: true }) {\n        if (steps <= 0) {\n            return;\n        }\n        FakeAsyncTestZoneSpec.assertInZone();\n        this.flushMicrotasks();\n        this._scheduler.tickToNext(steps, doTick, tickOptions);\n        if (this._lastError !== null) {\n            this._resetLastErrorAndThrow();\n        }\n    }\n    tick(millis = 0, doTick, tickOptions = { processNewMacroTasksSynchronously: true }) {\n        FakeAsyncTestZoneSpec.assertInZone();\n        this.flushMicrotasks();\n        this._scheduler.tick(millis, doTick, tickOptions);\n        if (this._lastError !== null) {\n            this._resetLastErrorAndThrow();\n        }\n    }\n    flushMicrotasks() {\n        FakeAsyncTestZoneSpec.assertInZone();\n        const flushErrors = () => {\n            if (this._lastError !== null || this._uncaughtPromiseErrors.length) {\n                // If there is an error stop processing the microtask queue and rethrow the error.\n                this._resetLastErrorAndThrow();\n            }\n        };\n        while (this._microtasks.length > 0) {\n            let microtask = this._microtasks.shift();\n            microtask.func.apply(microtask.target, microtask.args);\n        }\n        flushErrors();\n    }\n    flush(limit, flushPeriodic, doTick) {\n        FakeAsyncTestZoneSpec.assertInZone();\n        this.flushMicrotasks();\n        const elapsed = this._scheduler.flush(limit, flushPeriodic, doTick);\n        if (this._lastError !== null) {\n            this._resetLastErrorAndThrow();\n        }\n        return elapsed;\n    }\n    flushOnlyPendingTimers(doTick) {\n        FakeAsyncTestZoneSpec.assertInZone();\n        this.flushMicrotasks();\n        const elapsed = this._scheduler.flushOnlyPendingTimers(doTick);\n        if (this._lastError !== null) {\n            this._resetLastErrorAndThrow();\n        }\n        return elapsed;\n    }\n    removeAllTimers() {\n        FakeAsyncTestZoneSpec.assertInZone();\n        this._scheduler.removeAll();\n        this.pendingPeriodicTimers = [];\n        this.pendingTimers = [];\n    }\n    getTimerCount() {\n        return this._scheduler.getTimerCount() + this._microtasks.length;\n    }\n    // ZoneSpec implementation below.\n    name;\n    properties = { 'FakeAsyncTestZoneSpec': this };\n    onScheduleTask(delegate, current, target, task) {\n        switch (task.type) {\n            case 'microTask':\n                let args = task.data && task.data.args;\n                // should pass additional arguments to callback if have any\n                // currently we know process.nextTick will have such additional\n                // arguments\n                let additionalArgs;\n                if (args) {\n                    let callbackIndex = task.data.cbIdx;\n                    if (typeof args.length === 'number' && args.length > callbackIndex + 1) {\n                        additionalArgs = Array.prototype.slice.call(args, callbackIndex + 1);\n                    }\n                }\n                this._microtasks.push({\n                    func: task.invoke,\n                    args: additionalArgs,\n                    target: task.data && task.data.target,\n                });\n                break;\n            case 'macroTask':\n                switch (task.source) {\n                    case 'setTimeout':\n                        task.data['handleId'] = this._setTimeout(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n                        break;\n                    case 'setImmediate':\n                        task.data['handleId'] = this._setTimeout(task.invoke, 0, Array.prototype.slice.call(task.data['args'], 1));\n                        break;\n                    case 'setInterval':\n                        task.data['handleId'] = this._setInterval(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n                        break;\n                    case 'XMLHttpRequest.send':\n                        throw new Error('Cannot make XHRs from within a fake async test. Request URL: ' +\n                            task.data['url']);\n                    case 'requestAnimationFrame':\n                    case 'webkitRequestAnimationFrame':\n                    case 'mozRequestAnimationFrame':\n                        // Simulate a requestAnimationFrame by using a setTimeout with 16 ms.\n                        // (60 frames per second)\n                        task.data['handleId'] = this._setTimeout(task.invoke, 16, task.data['args'], this.trackPendingRequestAnimationFrame);\n                        break;\n                    default:\n                        // user can define which macroTask they want to support by passing\n                        // macroTaskOptions\n                        const macroTaskOption = this.findMacroTaskOption(task);\n                        if (macroTaskOption) {\n                            const args = task.data && task.data['args'];\n                            const delay = args && args.length > 1 ? args[1] : 0;\n                            let callbackArgs = macroTaskOption.callbackArgs ? macroTaskOption.callbackArgs : args;\n                            if (!!macroTaskOption.isPeriodic) {\n                                // periodic macroTask, use setInterval to simulate\n                                task.data['handleId'] = this._setInterval(task.invoke, delay, callbackArgs);\n                                task.data.isPeriodic = true;\n                            }\n                            else {\n                                // not periodic, use setTimeout to simulate\n                                task.data['handleId'] = this._setTimeout(task.invoke, delay, callbackArgs);\n                            }\n                            break;\n                        }\n                        throw new Error('Unknown macroTask scheduled in fake async test: ' + task.source);\n                }\n                break;\n            case 'eventTask':\n                task = delegate.scheduleTask(target, task);\n                break;\n        }\n        return task;\n    }\n    onCancelTask(delegate, current, target, task) {\n        switch (task.source) {\n            case 'setTimeout':\n            case 'requestAnimationFrame':\n            case 'webkitRequestAnimationFrame':\n            case 'mozRequestAnimationFrame':\n                return this._clearTimeout(task.data['handleId']);\n            case 'setInterval':\n                return this._clearInterval(task.data['handleId']);\n            default:\n                // user can define which macroTask they want to support by passing\n                // macroTaskOptions\n                const macroTaskOption = this.findMacroTaskOption(task);\n                if (macroTaskOption) {\n                    const handleId = task.data['handleId'];\n                    return macroTaskOption.isPeriodic\n                        ? this._clearInterval(handleId)\n                        : this._clearTimeout(handleId);\n                }\n                return delegate.cancelTask(target, task);\n        }\n    }\n    onInvoke(delegate, current, target, callback, applyThis, applyArgs, source) {\n        try {\n            FakeAsyncTestZoneSpec.patchDate();\n            return delegate.invoke(target, callback, applyThis, applyArgs, source);\n        }\n        finally {\n            if (!this.patchDateLocked) {\n                FakeAsyncTestZoneSpec.resetDate();\n            }\n        }\n    }\n    findMacroTaskOption(task) {\n        if (!this.macroTaskOptions) {\n            return null;\n        }\n        for (let i = 0; i < this.macroTaskOptions.length; i++) {\n            const macroTaskOption = this.macroTaskOptions[i];\n            if (macroTaskOption.source === task.source) {\n                return macroTaskOption;\n            }\n        }\n        return null;\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n        // ComponentFixture has a special-case handling to detect FakeAsyncTestZoneSpec\n        // and prevent rethrowing the error from the onError subscription since it's handled here.\n        this._lastError = error;\n        return false; // Don't propagate error to parent zone.\n    }\n}\nlet _fakeAsyncTestZoneSpec = null;\nfunction getProxyZoneSpec() {\n    return Zone && Zone['ProxyZoneSpec'];\n}\nlet _sharedProxyZoneSpec = null;\nlet _sharedProxyZone = null;\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @experimental\n */\nfunction resetFakeAsyncZone() {\n    if (_fakeAsyncTestZoneSpec) {\n        _fakeAsyncTestZoneSpec.unlockDatePatch();\n    }\n    _fakeAsyncTestZoneSpec = null;\n    getProxyZoneSpec()?.get()?.resetDelegate();\n    _sharedProxyZoneSpec?.resetDelegate();\n}\n/**\n * Wraps a function to be executed in the fakeAsync zone:\n * - microtasks are manually executed by calling `flushMicrotasks()`,\n * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n *\n * When flush is `false`, if there are any pending timers at the end of the function,\n * an exception will be thrown.\n *\n * Can be used to wrap inject() calls.\n *\n * ## Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * @param fn\n * @param options\n *     flush: when true, will drain the macrotask queue after the test function completes.\n * @returns The function wrapped to be executed in the fakeAsync zone\n *\n * @experimental\n */\nfunction fakeAsync(fn, options = {}) {\n    const { flush = true } = options;\n    // Not using an arrow function to preserve context passed from call site\n    const fakeAsyncFn = function (...args) {\n        const ProxyZoneSpec = getProxyZoneSpec();\n        if (!ProxyZoneSpec) {\n            throw new Error('ProxyZoneSpec is needed for the fakeAsync() test helper but could not be found. ' +\n                'Make sure that your environment includes zone-testing.js');\n        }\n        const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n        if (Zone.current.get('FakeAsyncTestZoneSpec')) {\n            throw new Error('fakeAsync() calls can not be nested');\n        }\n        try {\n            // in case jasmine.clock init a fakeAsyncTestZoneSpec\n            if (!_fakeAsyncTestZoneSpec) {\n                const FakeAsyncTestZoneSpec = Zone && Zone['FakeAsyncTestZoneSpec'];\n                if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n                    throw new Error('fakeAsync() calls can not be nested');\n                }\n                _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n            }\n            let res;\n            const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n            proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n            _fakeAsyncTestZoneSpec.lockDatePatch();\n            try {\n                res = fn.apply(this, args);\n                if (flush) {\n                    _fakeAsyncTestZoneSpec.flush(20, true);\n                }\n                else {\n                    flushMicrotasks();\n                }\n            }\n            finally {\n                proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n            }\n            if (!flush) {\n                if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n                    throw new Error(`${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` +\n                        `periodic timer(s) still in the queue.`);\n                }\n                if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n                    throw new Error(`${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n                }\n            }\n            return res;\n        }\n        finally {\n            resetFakeAsyncZone();\n        }\n    };\n    fakeAsyncFn.isFakeAsync = true;\n    return fakeAsyncFn;\n}\nfunction _getFakeAsyncZoneSpec() {\n    if (_fakeAsyncTestZoneSpec == null) {\n        _fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (_fakeAsyncTestZoneSpec == null) {\n            throw new Error('The code should be running in the fakeAsync zone to call this function');\n        }\n    }\n    return _fakeAsyncTestZoneSpec;\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer\n * callback has been executed.\n *\n * ## Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * @experimental\n */\nfunction tick(millis = 0, ignoreNestedTimeout = false) {\n    _getFakeAsyncZoneSpec().tick(millis, null, ignoreNestedTimeout);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n * draining the macrotask queue until it is empty. The returned value is the milliseconds\n * of time that would have been elapsed.\n *\n * @param maxTurns\n * @returns The simulated time elapsed, in millis.\n *\n * @experimental\n */\nfunction flush(maxTurns) {\n    return _getFakeAsyncZoneSpec().flush(maxTurns);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @experimental\n */\nfunction discardPeriodicTasks() {\n    const zoneSpec = _getFakeAsyncZoneSpec();\n    zoneSpec.pendingPeriodicTimers;\n    zoneSpec.pendingPeriodicTimers.length = 0;\n}\n/**\n * Wraps a function to be executed in a shared ProxyZone.\n *\n * If no shared ProxyZone exists, one is created and reused for subsequent calls.\n * Useful for wrapping test setup (beforeEach) and test execution (it) when test\n * runner patching isn't available or desired for setting up the ProxyZone.\n *\n * @param fn The function to wrap.\n * @returns A function that executes the original function within the shared ProxyZone.\n *\n * @experimental\n */\nfunction withProxyZone(fn) {\n    const autoProxyFn = function (...args) {\n        const proxyZoneSpec = getProxyZoneSpec();\n        if (proxyZoneSpec === undefined) {\n            throw new Error('ProxyZoneSpec is needed for the withProxyZone() test helper but could not be found. ' +\n                'Make sure that your environment includes zone-testing.js');\n        }\n        const proxyZone = proxyZoneSpec.get() !== undefined ? Zone.current : getOrCreateRootProxy();\n        return proxyZone.run(fn, this, args);\n    };\n    return autoProxyFn;\n}\nfunction getOrCreateRootProxy() {\n    const ProxyZoneSpec = getProxyZoneSpec();\n    if (ProxyZoneSpec === undefined) {\n        throw new Error('ProxyZoneSpec is needed for withProxyZone but could not be found. ' +\n            'Make sure that your environment includes zone-testing.js');\n    }\n    // Ensure the shared ProxyZoneSpec instance exists\n    if (_sharedProxyZoneSpec === null) {\n        _sharedProxyZoneSpec = new ProxyZoneSpec();\n    }\n    _sharedProxyZone = Zone.root.fork(_sharedProxyZoneSpec);\n    return _sharedProxyZone;\n}\n/**\n * Flush any pending microtasks.\n *\n * @experimental\n */\nfunction flushMicrotasks() {\n    _getFakeAsyncZoneSpec().flushMicrotasks();\n}\nfunction patchFakeAsyncTest(Zone) {\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['FakeAsyncTestZoneSpec'] = FakeAsyncTestZoneSpec;\n    Zone.__load_patch('fakeasync', (global, Zone, api) => {\n        Zone[api.symbol('fakeAsyncTest')] = {\n            resetFakeAsyncZone,\n            flushMicrotasks,\n            discardPeriodicTasks,\n            tick,\n            flush,\n            fakeAsync,\n            withProxyZone,\n        };\n    }, true);\n    patchedTimers = {\n        setTimeout: global$1.setTimeout,\n        setInterval: global$1.setInterval,\n        clearTimeout: global$1.clearTimeout,\n        clearInterval: global$1.clearInterval,\n        nativeSetTimeout: global$1[Zone.__symbol__('setTimeout')],\n        nativeClearTimeout: global$1[Zone.__symbol__('clearTimeout')],\n    };\n    Scheduler.nextId = Scheduler.getNextId();\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction patchLongStackTrace(Zone) {\n    const NEWLINE = '\\n';\n    const IGNORE_FRAMES = {};\n    const creationTrace = '__creationTrace__';\n    const ERROR_TAG = 'STACKTRACE TRACKING';\n    const SEP_TAG = '__SEP_TAG__';\n    let sepTemplate = SEP_TAG + '@[native]';\n    class LongStackTrace {\n        error = getStacktrace();\n        timestamp = new Date();\n    }\n    function getStacktraceWithUncaughtError() {\n        return new Error(ERROR_TAG);\n    }\n    function getStacktraceWithCaughtError() {\n        try {\n            throw getStacktraceWithUncaughtError();\n        }\n        catch (err) {\n            return err;\n        }\n    }\n    // Some implementations of exception handling don't create a stack trace if the exception\n    // isn't thrown, however it's faster not to actually throw the exception.\n    const error = getStacktraceWithUncaughtError();\n    const caughtError = getStacktraceWithCaughtError();\n    const getStacktrace = error.stack\n        ? getStacktraceWithUncaughtError\n        : caughtError.stack\n            ? getStacktraceWithCaughtError\n            : getStacktraceWithUncaughtError;\n    function getFrames(error) {\n        return error.stack ? error.stack.split(NEWLINE) : [];\n    }\n    function addErrorStack(lines, error) {\n        let trace = getFrames(error);\n        for (let i = 0; i < trace.length; i++) {\n            const frame = trace[i];\n            // Filter out the Frames which are part of stack capturing.\n            if (!IGNORE_FRAMES.hasOwnProperty(frame)) {\n                lines.push(trace[i]);\n            }\n        }\n    }\n    function renderLongStackTrace(frames, stack) {\n        const longTrace = [stack ? stack.trim() : ''];\n        if (frames) {\n            let timestamp = new Date().getTime();\n            for (let i = 0; i < frames.length; i++) {\n                const traceFrames = frames[i];\n                const lastTime = traceFrames.timestamp;\n                let separator = `____________________Elapsed ${timestamp - lastTime.getTime()} ms; At: ${lastTime}`;\n                separator = separator.replace(/[^\\w\\d]/g, '_');\n                longTrace.push(sepTemplate.replace(SEP_TAG, separator));\n                addErrorStack(longTrace, traceFrames.error);\n                timestamp = lastTime.getTime();\n            }\n        }\n        return longTrace.join(NEWLINE);\n    }\n    // if Error.stackTraceLimit is 0, means stack trace\n    // is disabled, so we don't need to generate long stack trace\n    // this will improve performance in some test(some test will\n    // set stackTraceLimit to 0, https://github.com/angular/zone.js/issues/698\n    function stackTracesEnabled() {\n        // Cast through any since this property only exists on Error in the nodejs\n        // typings.\n        return Error.stackTraceLimit > 0;\n    }\n    Zone['longStackTraceZoneSpec'] = {\n        name: 'long-stack-trace',\n        longStackTraceLimit: 10, // Max number of task to keep the stack trace for.\n        // add a getLongStackTrace method in spec to\n        // handle handled reject promise error.\n        getLongStackTrace: function (error) {\n            if (!error) {\n                return undefined;\n            }\n            const trace = error[Zone.__symbol__('currentTaskTrace')];\n            if (!trace) {\n                return error.stack;\n            }\n            return renderLongStackTrace(trace, error.stack);\n        },\n        onScheduleTask: function (parentZoneDelegate, currentZone, targetZone, task) {\n            if (stackTracesEnabled()) {\n                const currentTask = Zone.currentTask;\n                let trace = (currentTask && currentTask.data && currentTask.data[creationTrace]) || [];\n                trace = [new LongStackTrace()].concat(trace);\n                if (trace.length > this.longStackTraceLimit) {\n                    trace.length = this.longStackTraceLimit;\n                }\n                if (!task.data)\n                    task.data = {};\n                if (task.type === 'eventTask') {\n                    // Fix issue https://github.com/angular/zone.js/issues/1195,\n                    // For event task of browser, by default, all task will share a\n                    // singleton instance of data object, we should create a new one here\n                    // The cast to `any` is required to workaround a closure bug which wrongly applies\n                    // URL sanitization rules to .data access.\n                    task.data = { ...task.data };\n                }\n                task.data[creationTrace] = trace;\n            }\n            return parentZoneDelegate.scheduleTask(targetZone, task);\n        },\n        onHandleError: function (parentZoneDelegate, currentZone, targetZone, error) {\n            if (stackTracesEnabled()) {\n                const parentTask = Zone.currentTask || error.task;\n                if (error instanceof Error && parentTask) {\n                    const longStack = renderLongStackTrace(parentTask.data && parentTask.data[creationTrace], error.stack);\n                    try {\n                        error.stack = error.longStack = longStack;\n                    }\n                    catch (err) { }\n                }\n            }\n            return parentZoneDelegate.handleError(targetZone, error);\n        },\n    };\n    function captureStackTraces(stackTraces, count) {\n        if (count > 0) {\n            stackTraces.push(getFrames(new LongStackTrace().error));\n            captureStackTraces(stackTraces, count - 1);\n        }\n    }\n    function computeIgnoreFrames() {\n        if (!stackTracesEnabled()) {\n            return;\n        }\n        const frames = [];\n        captureStackTraces(frames, 2);\n        const frames1 = frames[0];\n        const frames2 = frames[1];\n        for (let i = 0; i < frames1.length; i++) {\n            const frame1 = frames1[i];\n            if (frame1.indexOf(ERROR_TAG) == -1) {\n                let match = frame1.match(/^\\s*at\\s+/);\n                if (match) {\n                    sepTemplate = match[0] + SEP_TAG + ' (http://localhost)';\n                    break;\n                }\n            }\n        }\n        for (let i = 0; i < frames1.length; i++) {\n            const frame1 = frames1[i];\n            const frame2 = frames2[i];\n            if (frame1 === frame2) {\n                IGNORE_FRAMES[frame1] = true;\n            }\n            else {\n                break;\n            }\n        }\n    }\n    computeIgnoreFrames();\n}\n\nclass ProxyZoneSpec {\n    defaultSpecDelegate;\n    name = 'ProxyZone';\n    _delegateSpec = null;\n    properties = { 'ProxyZoneSpec': this };\n    propertyKeys = null;\n    lastTaskState = null;\n    isNeedToTriggerHasTask = false;\n    tasks = [];\n    static get() {\n        return Zone.current.get('ProxyZoneSpec');\n    }\n    static isLoaded() {\n        return ProxyZoneSpec.get() instanceof ProxyZoneSpec;\n    }\n    static assertPresent() {\n        const spec = ProxyZoneSpec.get();\n        if (spec === undefined) {\n            throw new Error(`Expected to be running in 'ProxyZone', but it was not found.`);\n        }\n        return spec;\n    }\n    constructor(defaultSpecDelegate = null) {\n        this.defaultSpecDelegate = defaultSpecDelegate;\n        this.setDelegate(defaultSpecDelegate);\n    }\n    setDelegate(delegateSpec) {\n        const isNewDelegate = this._delegateSpec !== delegateSpec;\n        this._delegateSpec = delegateSpec;\n        this.propertyKeys && this.propertyKeys.forEach((key) => delete this.properties[key]);\n        this.propertyKeys = null;\n        if (delegateSpec && delegateSpec.properties) {\n            this.propertyKeys = Object.keys(delegateSpec.properties);\n            this.propertyKeys.forEach((k) => (this.properties[k] = delegateSpec.properties[k]));\n        }\n        // if a new delegateSpec was set, check if we need to trigger hasTask\n        if (isNewDelegate &&\n            this.lastTaskState &&\n            (this.lastTaskState.macroTask || this.lastTaskState.microTask)) {\n            this.isNeedToTriggerHasTask = true;\n        }\n    }\n    getDelegate() {\n        return this._delegateSpec;\n    }\n    resetDelegate() {\n        this.getDelegate();\n        this.setDelegate(this.defaultSpecDelegate);\n    }\n    tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone) {\n        if (this.isNeedToTriggerHasTask && this.lastTaskState) {\n            // last delegateSpec has microTask or macroTask\n            // should call onHasTask in current delegateSpec\n            this.isNeedToTriggerHasTask = false;\n            this.onHasTask(parentZoneDelegate, currentZone, targetZone, this.lastTaskState);\n        }\n    }\n    removeFromTasks(task) {\n        if (!this.tasks) {\n            return;\n        }\n        for (let i = 0; i < this.tasks.length; i++) {\n            if (this.tasks[i] === task) {\n                this.tasks.splice(i, 1);\n                return;\n            }\n        }\n    }\n    getAndClearPendingTasksInfo() {\n        if (this.tasks.length === 0) {\n            return '';\n        }\n        const taskInfo = this.tasks.map((task) => {\n            const dataInfo = task.data &&\n                Object.keys(task.data)\n                    .map((key) => {\n                    return key + ':' + task.data[key];\n                })\n                    .join(',');\n            return `type: ${task.type}, source: ${task.source}, args: {${dataInfo}}`;\n        });\n        const pendingTasksInfo = '--Pending async tasks are: [' + taskInfo + ']';\n        // clear tasks\n        this.tasks = [];\n        return pendingTasksInfo;\n    }\n    onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec) {\n        if (this._delegateSpec && this._delegateSpec.onFork) {\n            return this._delegateSpec.onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec);\n        }\n        else {\n            return parentZoneDelegate.fork(targetZone, zoneSpec);\n        }\n    }\n    onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source) {\n        if (this._delegateSpec && this._delegateSpec.onIntercept) {\n            return this._delegateSpec.onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source);\n        }\n        else {\n            return parentZoneDelegate.intercept(targetZone, delegate, source);\n        }\n    }\n    onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onInvoke) {\n            return this._delegateSpec.onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source);\n        }\n        else {\n            return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n        }\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n        if (this._delegateSpec && this._delegateSpec.onHandleError) {\n            return this._delegateSpec.onHandleError(parentZoneDelegate, currentZone, targetZone, error);\n        }\n        else {\n            return parentZoneDelegate.handleError(targetZone, error);\n        }\n    }\n    onScheduleTask(parentZoneDelegate, currentZone, targetZone, task) {\n        if (task.type !== 'eventTask') {\n            this.tasks.push(task);\n        }\n        if (this._delegateSpec && this._delegateSpec.onScheduleTask) {\n            return this._delegateSpec.onScheduleTask(parentZoneDelegate, currentZone, targetZone, task);\n        }\n        else {\n            return parentZoneDelegate.scheduleTask(targetZone, task);\n        }\n    }\n    onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs) {\n        if (task.type !== 'eventTask') {\n            this.removeFromTasks(task);\n        }\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onInvokeTask) {\n            return this._delegateSpec.onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs);\n        }\n        else {\n            return parentZoneDelegate.invokeTask(targetZone, task, applyThis, applyArgs);\n        }\n    }\n    onCancelTask(parentZoneDelegate, currentZone, targetZone, task) {\n        if (task.type !== 'eventTask') {\n            this.removeFromTasks(task);\n        }\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onCancelTask) {\n            return this._delegateSpec.onCancelTask(parentZoneDelegate, currentZone, targetZone, task);\n        }\n        else {\n            return parentZoneDelegate.cancelTask(targetZone, task);\n        }\n    }\n    onHasTask(delegate, current, target, hasTaskState) {\n        this.lastTaskState = hasTaskState;\n        if (this._delegateSpec && this._delegateSpec.onHasTask) {\n            this._delegateSpec.onHasTask(delegate, current, target, hasTaskState);\n        }\n        else {\n            delegate.hasTask(target, hasTaskState);\n        }\n    }\n}\nfunction patchProxyZoneSpec(Zone) {\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['ProxyZoneSpec'] = ProxyZoneSpec;\n}\n\nfunction patchSyncTest(Zone) {\n    class SyncTestZoneSpec {\n        runZone = Zone.current;\n        constructor(namePrefix) {\n            this.name = 'syncTestZone for ' + namePrefix;\n        }\n        // ZoneSpec implementation below.\n        name;\n        onScheduleTask(delegate, current, target, task) {\n            switch (task.type) {\n                case 'microTask':\n                case 'macroTask':\n                    throw new Error(`Cannot call ${task.source} from within a sync test (${this.name}).`);\n                case 'eventTask':\n                    task = delegate.scheduleTask(target, task);\n                    break;\n            }\n            return task;\n        }\n    }\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['SyncTestZoneSpec'] = SyncTestZoneSpec;\n}\n\nfunction patchPromiseTesting(Zone) {\n    /**\n     * Promise for async/fakeAsync zoneSpec test\n     * can support async operation which not supported by zone.js\n     * such as\n     * it ('test jsonp in AsyncZone', async() => {\n     *   new Promise(res => {\n     *     jsonp(url, (data) => {\n     *       // success callback\n     *       res(data);\n     *     });\n     *   }).then((jsonpResult) => {\n     *     // get jsonp result.\n     *\n     *     // user will expect AsyncZoneSpec wait for\n     *     // then, but because jsonp is not zone aware\n     *     // AsyncZone will finish before then is called.\n     *   });\n     * });\n     */\n    Zone.__load_patch('promisefortest', (global, Zone, api) => {\n        const symbolState = api.symbol('state');\n        const UNRESOLVED = null;\n        const symbolParentUnresolved = api.symbol('parentUnresolved');\n        // patch Promise.prototype.then to keep an internal\n        // number for tracking unresolved chained promise\n        // we will decrease this number when the parent promise\n        // being resolved/rejected and chained promise was\n        // scheduled as a microTask.\n        // so we can know such kind of chained promise still\n        // not resolved in AsyncTestZone\n        Promise[api.symbol('patchPromiseForTest')] = function patchPromiseForTest() {\n            let oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n            if (oriThen) {\n                return;\n            }\n            oriThen = Promise[Zone.__symbol__('ZonePromiseThen')] = Promise.prototype.then;\n            Promise.prototype.then = function () {\n                const chained = oriThen.apply(this, arguments);\n                if (this[symbolState] === UNRESOLVED) {\n                    // parent promise is unresolved.\n                    const asyncTestZoneSpec = Zone.current.get('AsyncTestZoneSpec');\n                    if (asyncTestZoneSpec) {\n                        asyncTestZoneSpec.unresolvedChainedPromiseCount++;\n                        chained[symbolParentUnresolved] = true;\n                    }\n                }\n                return chained;\n            };\n        };\n        Promise[api.symbol('unPatchPromiseForTest')] = function unpatchPromiseForTest() {\n            // restore origin then\n            const oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n            if (oriThen) {\n                Promise.prototype.then = oriThen;\n                Promise[Zone.__symbol__('ZonePromiseThen')] = undefined;\n            }\n        };\n    });\n}\n\nfunction rollupTesting(Zone) {\n    patchLongStackTrace(Zone);\n    patchProxyZoneSpec(Zone);\n    patchSyncTest(Zone);\n    patchJasmine(Zone);\n    patchJest(Zone);\n    patchMocha(Zone);\n    patchAsyncTest(Zone);\n    patchFakeAsyncTest(Zone);\n    patchPromiseTesting(Zone);\n}\n\nrollupTesting(Zone);\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;AAJA,IAAAA,eAAA,GAAAC,OAAA,gHAAAC,OAAA;AAAA,IAAAC,aAAA,GAAAF,OAAA,+GAAAC,OAAA;AAKA,SAASE,YAAYA,CAACC,IAAI,EAAE;EACxBA,IAAI,CAACC,YAAY,CAAC,SAAS,EAAE,CAACC,MAAM,EAAEF,IAAI,EAAEG,GAAG,KAAK;IAChD,MAAMC,SAAS,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;MAC9B,KAAK,MAAMC,CAAC,IAAID,CAAC,EACb,IAAIA,CAAC,CAACE,cAAc,CAACD,CAAC,CAAC,EACnBF,CAAC,CAACE,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC;MACnB,SAASE,EAAEA,CAAA,EAAG;QACV,IAAI,CAACC,WAAW,GAAGL,CAAC;MACxB;MACAA,CAAC,CAACM,SAAS,GACPL,CAAC,KAAK,IAAI,GAAGM,MAAM,CAACC,MAAM,CAACP,CAAC,CAAC,IAAKG,EAAE,CAACE,SAAS,GAAGL,CAAC,CAACK,SAAS,EAAG,IAAIF,EAAE,CAAC,CAAC,CAAC;IAChF,CAAC;IACD;IACA;IACA,IAAI,CAACT,IAAI,EACL,MAAM,IAAIc,KAAK,CAAC,kBAAkB,CAAC;IACvC,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;MAC7B;MACA;MACA;IACJ;IACA,IAAI,OAAOC,OAAO,IAAI,WAAW,IAAIA,OAAO,CAAC,gBAAgB,CAAC,EAAE;MAC5D;IACJ;IACAA,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI;IAChC,MAAMC,gBAAgB,GAAGjB,IAAI,CAAC,kBAAkB,CAAC;IACjD,MAAMkB,aAAa,GAAGlB,IAAI,CAAC,eAAe,CAAC;IAC3C,IAAI,CAACiB,gBAAgB,EACjB,MAAM,IAAIH,KAAK,CAAC,2BAA2B,CAAC;IAChD,IAAI,CAACI,aAAa,EACd,MAAM,IAAIJ,KAAK,CAAC,wBAAwB,CAAC;IAC7C,MAAMK,WAAW,GAAGnB,IAAI,CAACoB,OAAO;IAChC,MAAMC,MAAM,GAAGrB,IAAI,CAACsB,UAAU;IAC9B;IACA,MAAMC,2BAA2B,GAAGrB,MAAM,CAACmB,MAAM,CAAC,+BAA+B,CAAC,CAAC,KAAK,IAAI;IAC5F;IACA;IACA;IACA,MAAMG,mCAAmC,GAAG,CAACD,2BAA2B,KACnErB,MAAM,CAACmB,MAAM,CAAC,oBAAoB,CAAC,CAAC,KAAK,IAAI,IAC1CnB,MAAM,CAACmB,MAAM,CAAC,wCAAwC,CAAC,CAAC,KAAK,IAAI,CAAC;IAC1E,MAAMI,wBAAwB,GAAGvB,MAAM,CAACmB,MAAM,CAAC,0BAA0B,CAAC,CAAC,KAAK,IAAI;IACpF,IAAI,CAACI,wBAAwB,EAAE;MAC3B,MAAMC,YAAY,GAAGV,OAAO,CAACW,YAAY;MACzC,IAAID,YAAY,IAAI,CAACV,OAAO,CAACK,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE;QAClDL,OAAO,CAACK,MAAM,CAAC,cAAc,CAAC,CAAC,GAAGK,YAAY;QAC9CV,OAAO,CAACW,YAAY,GAAG,YAAY;UAC/B,MAAMC,QAAQ,GAAG,IAAIF,YAAY,CAAC,CAAC;UACnC,MAAMG,eAAe,GAAGD,QAAQ,CAACE,OAAO;UACxC,IAAID,eAAe,IAAI,CAACD,QAAQ,CAACP,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;YACjDO,QAAQ,CAACP,MAAM,CAAC,SAAS,CAAC,CAAC,GAAGQ,eAAe;YAC7CD,QAAQ,CAACE,OAAO,GAAG,YAAY;cAC3B,MAAMC,MAAM,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAI,CAAC,CAACA,OAAO,CAACC,EAAE;cAC7D;cACA;cACA;cACA;cACA;cACA;cACA,MAAMC,gBAAgB,GAAGH,MAAM,GACzBC,OAAO,CAACG,SAAS,CAAC,oBAAoB,CAAC,GACvCjC,MAAM,CAACkC,cAAc,CAAC,oBAAoB,CAAC;cACjD,MAAMC,MAAM,GAAGR,eAAe,CAACS,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;cACrDR,MAAM,GACAC,OAAO,CAACQ,kBAAkB,CAAC,oBAAoB,CAAC,GAChDtC,MAAM,CAACsC,kBAAkB,CAAC,oBAAoB,CAAC;cACrD,IAAIN,gBAAgB,EAAE;gBAClBA,gBAAgB,CAACO,OAAO,CAAEC,OAAO,IAAK;kBAClC,IAAIX,MAAM,EAAE;oBACRC,OAAO,CAACC,EAAE,CAAC,oBAAoB,EAAES,OAAO,CAAC;kBAC7C,CAAC,MACI;oBACDxC,MAAM,CAACyC,gBAAgB,CAAC,oBAAoB,EAAED,OAAO,CAAC;kBAC1D;gBACJ,CAAC,CAAC;cACN;cACA,OAAOL,MAAM;YACjB,CAAC;UACL;UACA,OAAOT,QAAQ;QACnB,CAAC;MACL;IACJ;IACA;IACA,MAAMgB,UAAU,GAAG5B,OAAO,CAAC6B,MAAM,CAAC,CAAC;IACnC,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAACJ,OAAO,CAAEK,UAAU,IAAK;MAC3D,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;MAC9CF,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUE,WAAW,EAAEC,eAAe,EAAE;QAC7D,OAAOF,iBAAiB,CAACG,IAAI,CAAC,IAAI,EAAEF,WAAW,EAAEG,kBAAkB,CAACH,WAAW,EAAEC,eAAe,CAAC,CAAC;MACtG,CAAC;IACL,CAAC,CAAC;IACF,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAACR,OAAO,CAAEK,UAAU,IAAK;MACzC,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;MAC9CF,UAAU,CAACvB,MAAM,CAACyB,UAAU,CAAC,CAAC,GAAGC,iBAAiB;MAClDH,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUE,WAAW,EAAEC,eAAe,EAAEG,OAAO,EAAE;QACtEb,SAAS,CAAC,CAAC,CAAC,GAAGc,cAAc,CAACJ,eAAe,CAAC;QAC9C,OAAOF,iBAAiB,CAACT,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACnD,CAAC;IACL,CAAC,CAAC;IACF,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAACE,OAAO,CAAEK,UAAU,IAAK;MACzE,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;MAC9CF,UAAU,CAACvB,MAAM,CAACyB,UAAU,CAAC,CAAC,GAAGC,iBAAiB;MAClDH,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUG,eAAe,EAAEG,OAAO,EAAE;QACzDb,SAAS,CAAC,CAAC,CAAC,GAAGc,cAAc,CAACJ,eAAe,CAAC;QAC9C,OAAOF,iBAAiB,CAACT,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACnD,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAAChB,2BAA2B,EAAE;MAC9B;MACA;MACA,MAAM+B,eAAe,GAAItC,OAAO,CAACK,MAAM,CAAC,OAAO,CAAC,CAAC,GAAGL,OAAO,CAAC,OAAO,CAAE;MACrEA,OAAO,CAAC,OAAO,CAAC,GAAG,YAAY;QAC3B,MAAMuC,KAAK,GAAGD,eAAe,CAAChB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QACpD,IAAI,CAACgB,KAAK,CAAClC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;UAC3BkC,KAAK,CAAClC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAGA,MAAM,CAAC,SAAS,CAAC;UAC5C,MAAMmC,YAAY,GAAID,KAAK,CAAClC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAGkC,KAAK,CAACE,IAAK;UACzDF,KAAK,CAACE,IAAI,GAAG,YAAY;YACrB,MAAMC,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;YACnE,IAAID,iBAAiB,EAAE;cACnB,OAAOA,iBAAiB,CAACD,IAAI,CAACnB,KAAK,CAACoB,iBAAiB,EAAEnB,SAAS,CAAC;YACrE;YACA,OAAOiB,YAAY,CAAClB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;UAC9C,CAAC;UACD,MAAMqB,gBAAgB,GAAIL,KAAK,CAAClC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAGkC,KAAK,CAACM,QAAS;UACrEN,KAAK,CAACM,QAAQ,GAAG,YAAY;YACzB,MAAMH,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;YACnE,IAAID,iBAAiB,EAAE;cACnB,MAAMI,QAAQ,GAAGvB,SAAS,CAACwB,MAAM,GAAG,CAAC,GAAGxB,SAAS,CAAC,CAAC,CAAC,GAAG,IAAIyB,IAAI,CAAC,CAAC;cACjE,OAAON,iBAAiB,CAACO,qBAAqB,CAAC3B,KAAK,CAACoB,iBAAiB,EAAEI,QAAQ,IAAI,OAAOA,QAAQ,CAACI,OAAO,KAAK,UAAU,GACpH,CAACJ,QAAQ,CAACI,OAAO,CAAC,CAAC,CAAC,GACpB3B,SAAS,CAAC;YACpB;YACA,OAAOqB,gBAAgB,CAACtB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;UAClD,CAAC;UACD;UACA,IAAIf,mCAAmC,EAAE;YACrC,CAAC,SAAS,EAAE,WAAW,CAAC,CAACiB,OAAO,CAAEK,UAAU,IAAK;cAC7C,MAAMQ,eAAe,GAAIC,KAAK,CAAClC,MAAM,CAACyB,UAAU,CAAC,CAAC,GAAGS,KAAK,CAACT,UAAU,CAAE;cACvES,KAAK,CAACT,UAAU,CAAC,GAAG,YAAY;gBAC5B,MAAMqB,qBAAqB,GAAGnE,IAAI,CAAC,uBAAuB,CAAC;gBAC3D,IAAImE,qBAAqB,EAAE;kBACvBnD,OAAO,CAACK,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,SAAS,KAAKyB,UAAU;kBAC5D;gBACJ;gBACA,OAAOQ,eAAe,CAAChB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;cACjD,CAAC;YACL,CAAC,CAAC;UACN;QACJ;QACA,OAAOgB,KAAK;MAChB,CAAC;IACL;IACA;IACA,IAAI,CAACvC,OAAO,CAAChB,IAAI,CAACsB,UAAU,CAAC,cAAc,CAAC,CAAC,EAAE;MAC3C,MAAM8C,oBAAoB,GAAGpD,OAAO,CAACqD,YAAY;MACjDrD,OAAO,CAAChB,IAAI,CAACsB,UAAU,CAAC,cAAc,CAAC,CAAC,GAAG8C,oBAAoB;MAC/DpD,OAAO,CAACqD,YAAY,GAAG,YAAY;QAC/B,MAAMC,IAAI,GAAGC,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACX,SAAS,CAAC;QAClD,MAAMkC,aAAa,GAAGH,IAAI,CAACP,MAAM,IAAI,CAAC,GAAGO,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;QACvD,IAAII,MAAM;QACV,IAAID,aAAa,EAAE;UACf,MAAME,cAAc,GAAG/D,MAAM,CAAC+D,cAAc;UAC5C/D,MAAM,CAAC+D,cAAc,GAAG,UAAUC,GAAG,EAAErE,CAAC,EAAEsE,UAAU,EAAE;YAClD,OAAOF,cAAc,CAACzB,IAAI,CAAC,IAAI,EAAE0B,GAAG,EAAErE,CAAC,EAAAT,aAAA,CAAAA,aAAA,KAChC+E,UAAU;cACbC,YAAY,EAAE,IAAI;cAClBC,UAAU,EAAE;YAAI,EACnB,CAAC;UACN,CAAC;UACD,IAAI;YACAL,MAAM,GAAGN,oBAAoB,CAAC9B,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;UACnD,CAAC,SACO;YACJ1D,MAAM,CAAC+D,cAAc,GAAGA,cAAc;UAC1C;QACJ,CAAC,MACI;UACDD,MAAM,GAAGN,oBAAoB,CAAC9B,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;QACnD;QACA,OAAOI,MAAM;MACjB,CAAC;IACL;IACA;AACR;AACA;AACA;IACQ,SAASvB,kBAAkBA,CAACH,WAAW,EAAEgC,YAAY,EAAE;MACnD,OAAO,YAAY;QACf;QACA;QACA,MAAMC,QAAQ,GAAG9D,WAAW,CAAC+D,IAAI,CAAC,IAAIjE,gBAAgB,CAAC,oBAAoB+B,WAAW,EAAE,CAAC,CAAC;QAC1F,OAAOiC,QAAQ,CAACE,GAAG,CAACH,YAAY,EAAE,IAAI,EAAEzC,SAAS,CAAC;MACtD,CAAC;IACL;IACA,SAAS6C,aAAaA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,IAAI,EAAE;MAC3D,MAAMC,gBAAgB,GAAG,CAAC,CAACzE,OAAO,CAACK,MAAM,CAAC,gBAAgB,CAAC,CAAC;MAC5DkE,WAAW,CAACG,iBAAiB;MAC7B,MAAMC,aAAa,GAAGJ,WAAW,CAACI,aAAa;MAC/C,IAAIF,gBAAgB,IAAIjE,mCAAmC,EAAE;QACzD;QACA,MAAMoE,eAAe,GAAG5F,IAAI,CAACA,IAAI,CAACsB,UAAU,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAIsE,eAAe,IAAI,OAAOA,eAAe,CAACC,SAAS,KAAK,UAAU,EAAE;UACpER,QAAQ,GAAGO,eAAe,CAACC,SAAS,CAACR,QAAQ,CAAC;QAClD;MACJ;MACA,IAAIG,IAAI,EAAE;QACN,OAAOG,aAAa,CAACR,GAAG,CAACE,QAAQ,EAAEC,SAAS,EAAE,CAACE,IAAI,CAAC,CAAC;MACzD,CAAC,MACI;QACD,OAAOG,aAAa,CAACR,GAAG,CAACE,QAAQ,EAAEC,SAAS,CAAC;MACjD;IACJ;IACA;AACR;AACA;AACA;AACA;IACQ,SAASjC,cAAcA,CAACgC,QAAQ,EAAE;MAC9B;MACA;MACA;MACA,OAAQA,QAAQ,KACXA,QAAQ,CAACtB,MAAM,GACV,UAAUyB,IAAI,EAAE;QACd,OAAOJ,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACE,WAAW,EAAEC,IAAI,CAAC;MAChE,CAAC,GACC,YAAY;QACV,OAAOJ,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACE,WAAW,CAAC;MAC1D,CAAC,CAAC;IACd;IACA,MAAMO,WAAW,GAAG9E,OAAO,CAAC8E,WAAW;IACvC9E,OAAO,CAAC8E,WAAW,GAAI,UAAUC,MAAM,EAAE;MACrC3F,SAAS,CAAC4F,eAAe,EAAED,MAAM,CAAC;MAClC,SAASC,eAAeA,CAACC,KAAK,EAAE;QAC5B,IAAIA,KAAK,CAACC,UAAU,EAAE;UAClBD,KAAK,CAACC,UAAU,GAAG,CAAEC,EAAE,IAAK,MAAM;YAC9B;YACA,IAAI,CAACR,aAAa,GAAG,IAAI;YACzB,IAAI,CAACD,iBAAiB,GAAG,IAAI;YAC7BvE,WAAW,CAACiF,iBAAiB,CAAC,oBAAoB,EAAED,EAAE,CAAC;UAC3D,CAAC,EAAEF,KAAK,CAACC,UAAU,CAAC;QACxB;QACA,MAAMG,gBAAgB,GAAGnG,MAAM,CAACF,IAAI,CAACsB,UAAU,CAAC,YAAY,CAAC,CAAC;QAC9D,MAAMgF,kBAAkB,GAAGpG,MAAM,CAACF,IAAI,CAACsB,UAAU,CAAC,cAAc,CAAC,CAAC;QAClE,IAAI+E,gBAAgB,EAAE;UAClB;UACAJ,KAAK,CAAC7C,OAAO,GAAG;YACZmD,UAAU,EAAEF,gBAAgB,GAAGA,gBAAgB,GAAGnG,MAAM,CAACqG,UAAU;YACnEC,YAAY,EAAEF,kBAAkB,GAAGA,kBAAkB,GAAGpG,MAAM,CAACsG;UACnE,CAAC;QACL;QACA;QACA;QACA,IAAIxF,OAAO,CAACyF,WAAW,EAAE;UACrB,IAAI,CAACR,KAAK,CAACS,WAAW,EAAE;YACpBT,KAAK,CAACS,WAAW,GAAG,IAAI1F,OAAO,CAACyF,WAAW,CAAC,CAAC;UACjD;UACAR,KAAK,CAACS,WAAW,CAACnB,WAAW,GAAG,IAAI;QACxC,CAAC,MACI;UACD,IAAI,CAACU,KAAK,CAACS,WAAW,EAAE;YACpBT,KAAK,CAACS,WAAW,GAAG,CAAC,CAAC;UAC1B;UACAT,KAAK,CAACS,WAAW,CAACnB,WAAW,GAAG,IAAI;QACxC;QACA;QACA,MAAMoB,WAAW,GAAGV,KAAK,CAACU,WAAW;QACrCV,KAAK,CAACU,WAAW,GAAG,UAAUC,KAAK,EAAE;UACjC,IAAIA,KAAK,IACLA,KAAK,CAACC,OAAO,KACT,wGAAwG,EAAE;YAC9G;YACA;YACA,MAAMC,aAAa,GAAG,IAAI,IAAI,IAAI,CAACpB,iBAAiB;YACpD,IAAIoB,aAAa,EAAE;cACf,MAAMC,gBAAgB,GAAGD,aAAa,CAACE,2BAA2B,CAAC,CAAC;cACpE,IAAI;gBACA;gBACAJ,KAAK,CAACC,OAAO,IAAIE,gBAAgB;cACrC,CAAC,CACD,OAAOE,GAAG,EAAE,CAAE;YAClB;UACJ;UACA,IAAIN,WAAW,EAAE;YACbA,WAAW,CAACzD,IAAI,CAAC,IAAI,EAAE0D,KAAK,CAAC;UACjC;QACJ,CAAC;QACDb,MAAM,CAAC7C,IAAI,CAAC,IAAI,EAAE+C,KAAK,CAAC;MAC5B;MACAD,eAAe,CAACrF,SAAS,CAACuG,OAAO,GAAG,YAAY;QAC5C,IAAIC,IAAI,GAAGnH,IAAI,CAACoB,OAAO;QACvB,IAAIgG,oBAAoB,GAAG,KAAK;QAChC,OAAOD,IAAI,EAAE;UACT,IAAIA,IAAI,KAAKhG,WAAW,EAAE;YACtBiG,oBAAoB,GAAG,IAAI;YAC3B;UACJ;UACAD,IAAI,GAAGA,IAAI,CAACE,MAAM;QACtB;QACA,IAAI,CAACD,oBAAoB,EACrB,MAAM,IAAItG,KAAK,CAAC,mBAAmB,GAAGd,IAAI,CAACoB,OAAO,CAACkG,IAAI,CAAC;QAC5D;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC5B,iBAAiB,GAAG,IAAIxE,aAAa,CAAC,CAAC;QAC5C,IAAI,CAACyE,aAAa,GAAGxE,WAAW,CAAC+D,IAAI,CAAC,IAAI,CAACQ,iBAAiB,CAAC;QAC7D,IAAI,CAAC1F,IAAI,CAACuH,WAAW,EAAE;UACnB;UACA;UACA;UACA;UACA;UACAvH,IAAI,CAACoB,OAAO,CAACgF,iBAAiB,CAAC,6BAA6B,EAAE,MAAMN,WAAW,CAACnF,SAAS,CAACuG,OAAO,CAAChE,IAAI,CAAC,IAAI,CAAC,CAAC;QACjH,CAAC,MACI;UACD6C,MAAM,CAACpF,SAAS,CAACuG,OAAO,CAAChE,IAAI,CAAC,IAAI,CAAC;QACvC;MACJ,CAAC;MACD,OAAO8C,eAAe;IAC1B,CAAC,CAAEF,WAAW,CAAC;EACnB,CAAC,CAAC;AACN;AAEA,SAAS0B,SAASA,CAACxH,IAAI,EAAE;EACrBA,IAAI,CAACC,YAAY,CAAC,MAAM,EAAE,CAACwH,OAAO,EAAEzH,IAAI,EAAEG,GAAG,KAAK;IAC9C,IAAI,OAAOY,IAAI,KAAK,WAAW,IAAIA,IAAI,CAAC,gBAAgB,CAAC,EAAE;MACvD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAf,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,iCAAiC,CAAC,CAAC,GAAG,IAAI;IAC1DN,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI;IAC7B,MAAMG,aAAa,GAAGlB,IAAI,CAAC,eAAe,CAAC;IAC3C,MAAMiB,gBAAgB,GAAGjB,IAAI,CAAC,kBAAkB,CAAC;IACjD,IAAI,CAACkB,aAAa,EAAE;MAChB,MAAM,IAAIJ,KAAK,CAAC,uBAAuB,CAAC;IAC5C;IACA,MAAM4G,QAAQ,GAAG1H,IAAI,CAACoB,OAAO;IAC7B,MAAM6D,QAAQ,GAAGyC,QAAQ,CAACxC,IAAI,CAAC,IAAIjE,gBAAgB,CAAC,eAAe,CAAC,CAAC;IACrE,MAAM6F,aAAa,GAAG,IAAI5F,aAAa,CAAC,CAAC;IACzC,MAAMyG,SAAS,GAAGD,QAAQ,CAACxC,IAAI,CAAC4B,aAAa,CAAC;IAC9C,SAASc,yBAAyBA,CAACC,cAAc,EAAE;MAC/C,OAAO,UAAU,GAAGC,SAAS,EAAE;QAC3B,MAAMC,kBAAkB,GAAGF,cAAc,CAACvF,KAAK,CAAC,IAAI,EAAEwF,SAAS,CAAC;QAChE,OAAO,UAAU,GAAGxD,IAAI,EAAE;UACtBA,IAAI,CAAC,CAAC,CAAC,GAAGnB,kBAAkB,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC;UACrC,OAAOyD,kBAAkB,CAACzF,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;QAC/C,CAAC;MACL,CAAC;IACL;IACA,SAAS0D,qBAAqBA,CAACH,cAAc,EAAE;MAC3C,OAAO,UAAU,GAAGC,SAAS,EAAE;QAC3B,OAAO,UAAU,GAAGxD,IAAI,EAAE;UACtBA,IAAI,CAAC,CAAC,CAAC,GAAGjB,cAAc,CAACiB,IAAI,CAAC,CAAC,CAAC,CAAC;UACjC,OAAOuD,cAAc,CAACvF,KAAK,CAAC,IAAI,EAAEwF,SAAS,CAAC,CAACxF,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;QAClE,CAAC;MACL,CAAC;IACL;IACA;AACR;AACA;AACA;IACQ,SAASnB,kBAAkBA,CAAC6B,YAAY,EAAE;MACtC,OAAO,UAAU,GAAGV,IAAI,EAAE;QACtB,OAAOW,QAAQ,CAACE,GAAG,CAACH,YAAY,EAAE,IAAI,EAAEV,IAAI,CAAC;MACjD,CAAC;IACL;IACA;AACR;AACA;AACA;AACA;IACQ,SAASjB,cAAcA,CAACgC,QAAQ,EAAE4C,UAAU,GAAG,KAAK,EAAE;MAClD,IAAI,OAAO5C,QAAQ,KAAK,UAAU,EAAE;QAChC,OAAOA,QAAQ;MACnB;MACA,MAAM6C,WAAW,GAAG,SAAAA,CAAA,EAAY;QAC5B,IAAIlI,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,qBAAqB,CAAC,CAAC,KAAK,IAAI,IAChDgE,QAAQ,IACR,CAACA,QAAQ,CAAC8C,WAAW,EAAE;UACvB;UACA,MAAMvC,eAAe,GAAG5F,IAAI,CAACA,IAAI,CAACsB,UAAU,CAAC,eAAe,CAAC,CAAC;UAC9D,IAAIsE,eAAe,IAAI,OAAOA,eAAe,CAACC,SAAS,KAAK,UAAU,EAAE;YACpER,QAAQ,GAAGO,eAAe,CAACC,SAAS,CAACR,QAAQ,CAAC;UAClD;QACJ;QACAyB,aAAa,CAACmB,UAAU,GAAGA,UAAU;QACrC,OAAON,SAAS,CAACxC,GAAG,CAACE,QAAQ,EAAE,IAAI,EAAE9C,SAAS,CAAC;MACnD,CAAC;MACD;MACA;MACA3B,MAAM,CAAC+D,cAAc,CAACuD,WAAW,EAAE,QAAQ,EAAE;QACzCpD,YAAY,EAAE,IAAI;QAClBsD,QAAQ,EAAE,IAAI;QACdrD,UAAU,EAAE;MAChB,CAAC,CAAC;MACFmD,WAAW,CAACnE,MAAM,GAAGsB,QAAQ,CAACtB,MAAM;MACpC,OAAOmE,WAAW;IACtB;IACA,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAACzF,OAAO,CAAEK,UAAU,IAAK;MAC3D,IAAI+E,cAAc,GAAGJ,OAAO,CAAC3E,UAAU,CAAC;MACxC,IAAI2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,EAAE;QACtC;MACJ;MACA2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,GAAG+E,cAAc;MACrDJ,OAAO,CAAC3E,UAAU,CAAC,GAAG,UAAU,GAAGwB,IAAI,EAAE;QACrCA,IAAI,CAAC,CAAC,CAAC,GAAGnB,kBAAkB,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,OAAOuD,cAAc,CAACvF,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;MAC3C,CAAC;MACDmD,OAAO,CAAC3E,UAAU,CAAC,CAACuF,IAAI,GAAGT,yBAAyB,CAACC,cAAc,CAACQ,IAAI,CAAC;IAC7E,CAAC,CAAC;IACFZ,OAAO,CAACa,QAAQ,CAACC,IAAI,GAAGd,OAAO,CAACe,SAAS;IACzCf,OAAO,CAACa,QAAQ,CAACG,IAAI,GAAGhB,OAAO,CAACiB,SAAS;IACzC,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAACjG,OAAO,CAAEK,UAAU,IAAK;MAC1D,IAAI+E,cAAc,GAAGJ,OAAO,CAAC3E,UAAU,CAAC;MACxC,IAAI2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,EAAE;QACtC;MACJ;MACA2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,GAAG+E,cAAc;MACrDJ,OAAO,CAAC3E,UAAU,CAAC,GAAG,UAAU,GAAGwB,IAAI,EAAE;QACrCA,IAAI,CAAC,CAAC,CAAC,GAAGjB,cAAc,CAACiB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QACvC,OAAOuD,cAAc,CAACvF,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;MAC3C,CAAC;MACDmD,OAAO,CAAC3E,UAAU,CAAC,CAACuF,IAAI,GAAGL,qBAAqB,CAACH,cAAc,CAACQ,IAAI,CAAC;MACrEZ,OAAO,CAAC3E,UAAU,CAAC,CAAC6F,IAAI,GAAGd,cAAc,CAACc,IAAI;MAC9ClB,OAAO,CAAC3E,UAAU,CAAC,CAAC8F,OAAO,GAAGf,cAAc,CAACe,OAAO;IACxD,CAAC,CAAC;IACFnB,OAAO,CAACoB,EAAE,CAACN,IAAI,GAAGd,OAAO,CAACqB,GAAG;IAC7BrB,OAAO,CAACoB,EAAE,CAACJ,IAAI,GAAGhB,OAAO,CAACsB,GAAG;IAC7BtB,OAAO,CAACuB,IAAI,CAACT,IAAI,GAAGd,OAAO,CAACqB,GAAG;IAC/BrB,OAAO,CAACuB,IAAI,CAACP,IAAI,GAAGhB,OAAO,CAACsB,GAAG;IAC/B,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAACtG,OAAO,CAAEK,UAAU,IAAK;MACzE,IAAI+E,cAAc,GAAGJ,OAAO,CAAC3E,UAAU,CAAC;MACxC,IAAI2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,EAAE;QACtC;MACJ;MACA2E,OAAO,CAACzH,IAAI,CAACsB,UAAU,CAACwB,UAAU,CAAC,CAAC,GAAG+E,cAAc;MACrDJ,OAAO,CAAC3E,UAAU,CAAC,GAAG,UAAU,GAAGwB,IAAI,EAAE;QACrCA,IAAI,CAAC,CAAC,CAAC,GAAGjB,cAAc,CAACiB,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,OAAOuD,cAAc,CAACvF,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;MAC3C,CAAC;IACL,CAAC,CAAC;IACFtE,IAAI,CAACiJ,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,QAAQ,GAAG,KAAK,EAAE;MACrE;MACA,SAASC,mBAAmBA,CAAA,EAAG;QAC3B,MAAM1F,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;QACnE,OAAO,CAAC,CAACD,iBAAiB;MAC9B;MACA;MACA;MACA,SAAS2F,YAAYA,CAAA,EAAG;QACpB,MAAMvC,aAAa,GAAG9G,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,eAAe,CAAC;QACvD,OAAOmD,aAAa,IAAIA,aAAa,CAACmB,UAAU;MACpD;MACA,IAAIiB,KAAK,CAAC/I,GAAG,CAACkB,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE;QACjC;MACJ;MACA6H,KAAK,CAAC/I,GAAG,CAACkB,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;MACtC;MACAlB,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,kBAAkB,EAAGK,QAAQ,IAAK;QACrD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzB,IAAI8E,mBAAmB,CAAC,CAAC,EAAE;YACvB,OAAO,IAAI;UACf,CAAC,MACI;YACD,OAAOG,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAGK,QAAQ,IAAK;QAClD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzBtE,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,IAAI;UAC9C,IAAI8H,QAAQ,IAAIE,YAAY,CAAC,CAAC,EAAE;YAC5B,OAAOE,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;UACA,OAAOkF,IAAI;QACf,CAAC;MACL,CAAC,CAAC;MACF;MACArJ,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAGK,QAAQ,IAAK;QAClD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzBtE,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK;UAC/C,IAAI8H,QAAQ,IAAIE,YAAY,CAAC,CAAC,EAAE;YAC5B,OAAOE,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;UACA,OAAOkF,IAAI;QACf,CAAC;MACL,CAAC,CAAC;MACF;MACArJ,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAGK,QAAQ,IAAK;QAClD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,IAAI0F,mBAAmB,CAAC,CAAC,EAAE;YAC5C1F,iBAAiB,CAACO,qBAAqB,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,MACI;YACD,OAAOiF,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,mBAAmB,EAAGK,QAAQ,IAAK;QACtD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,IAAI0F,mBAAmB,CAAC,CAAC,EAAE;YAC5C,OAAO1F,iBAAiB,CAAC+F,iBAAiB,CAAC,CAAC;UAChD,CAAC,MACI;YACD,OAAOF,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,aAAa,EAAGK,QAAQ,IAAK;QAChD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACgG,eAAe,CAAC,CAAC;UACvC,CAAC,MACI;YACD,OAAOH,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,cAAc,EAAGK,QAAQ,IAAK;QACjD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACiG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC;UACtC,CAAC,MACI;YACD,OAAOJ,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,qBAAqB,EAAGK,QAAQ,IAAK;QACxD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACD,IAAI,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,MACI;YACD,OAAOiF,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,sBAAsB,EAAGK,QAAQ,IAAK;QACzD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACkG,sBAAsB,CAAC,CAAC;UAC9C,CAAC,MACI;YACD,OAAOL,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,0BAA0B,EAAGK,QAAQ,IAAK;QAC7D,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACmG,UAAU,CAACvF,IAAI,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,MACI;YACD,OAAOiF,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,gBAAgB,EAAGK,QAAQ,IAAK;QACnD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnBA,iBAAiB,CAACoG,eAAe,CAAC,CAAC;UACvC,CAAC,MACI;YACD,OAAOP,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;MACF;MACAnE,GAAG,CAACmJ,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAGK,QAAQ,IAAK;QAClD,OAAO,UAAUC,IAAI,EAAElF,IAAI,EAAE;UACzB,MAAMZ,iBAAiB,GAAG1D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAID,iBAAiB,EAAE;YACnB,OAAOA,iBAAiB,CAACqG,aAAa,CAAC,CAAC;UAC5C,CAAC,MACI;YACD,OAAOR,QAAQ,CAACjH,KAAK,CAACkH,IAAI,EAAElF,IAAI,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;IACN,CAAC;EACL,CAAC,CAAC;AACN;AAEA,SAAS0F,UAAUA,CAAChK,IAAI,EAAE;EACtBA,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE,CAACC,MAAM,EAAEF,IAAI,KAAK;IACzC,MAAMiK,KAAK,GAAG/J,MAAM,CAAC+J,KAAK;IAC1B,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MAC9B;MACA;MACA;IACJ;IACA,IAAI,OAAOjK,IAAI,KAAK,WAAW,EAAE;MAC7B,MAAM,IAAIc,KAAK,CAAC,iBAAiB,CAAC;IACtC;IACA,MAAMI,aAAa,GAAGlB,IAAI,CAAC,eAAe,CAAC;IAC3C,MAAMiB,gBAAgB,GAAGjB,IAAI,CAAC,kBAAkB,CAAC;IACjD,IAAI,CAACkB,aAAa,EAAE;MAChB,MAAM,IAAIJ,KAAK,CAAC,uBAAuB,CAAC;IAC5C;IACA,IAAImJ,KAAK,CAAC,gBAAgB,CAAC,EAAE;MACzB,MAAM,IAAInJ,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACAmJ,KAAK,CAAC,gBAAgB,CAAC,GAAG,IAAI;IAC9B,MAAMvC,QAAQ,GAAG1H,IAAI,CAACoB,OAAO;IAC7B,MAAM6D,QAAQ,GAAGyC,QAAQ,CAACxC,IAAI,CAAC,IAAIjE,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;IACtE,IAAIiJ,QAAQ,GAAG,IAAI;IACnB,MAAMC,SAAS,GAAGzC,QAAQ,CAACxC,IAAI,CAAC,IAAIhE,aAAa,CAAC,CAAC,CAAC;IACpD,MAAMkJ,aAAa,GAAG;MAClBC,KAAK,EAAEnK,MAAM,CAACmK,KAAK;MACnBC,SAAS,EAAEpK,MAAM,CAACoK,SAAS;MAC3BC,MAAM,EAAErK,MAAM,CAACqK,MAAM;MACrBC,UAAU,EAAEtK,MAAM,CAACsK,UAAU;MAC7BlC,QAAQ,EAAEpI,MAAM,CAACoI,QAAQ;MACzBO,EAAE,EAAE3I,MAAM,CAAC2I;IACf,CAAC;IACD,SAAS4B,eAAeA,CAACnG,IAAI,EAAEoG,QAAQ,EAAEC,SAAS,EAAE;MAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtG,IAAI,CAACP,MAAM,EAAE6G,CAAC,EAAE,EAAE;QAClC,IAAIC,GAAG,GAAGvG,IAAI,CAACsG,CAAC,CAAC;QACjB,IAAI,OAAOC,GAAG,KAAK,UAAU,EAAE;UAC3B;UACA;UACA;UACA;UACA;UACAvG,IAAI,CAACsG,CAAC,CAAC,GAAGC,GAAG,CAAC9G,MAAM,KAAK,CAAC,GAAG2G,QAAQ,CAACG,GAAG,CAAC,GAAGF,SAAS,CAACE,GAAG,CAAC;UAC3D;UACA;UACAvG,IAAI,CAACsG,CAAC,CAAC,CAACE,QAAQ,GAAG,YAAY;YAC3B,OAAOD,GAAG,CAACC,QAAQ,CAAC,CAAC;UACzB,CAAC;QACL;MACJ;MACA,OAAOxG,IAAI;IACf;IACA,SAASnB,kBAAkBA,CAACmB,IAAI,EAAE;MAC9B,MAAMoG,QAAQ,GAAG,SAAAA,CAAUvE,EAAE,EAAE;QAC3B,OAAO,YAAY;UACf,OAAOlB,QAAQ,CAACE,GAAG,CAACgB,EAAE,EAAE,IAAI,EAAE5D,SAAS,CAAC;QAC5C,CAAC;MACL,CAAC;MACD,OAAOkI,eAAe,CAACnG,IAAI,EAAEoG,QAAQ,CAAC;IAC1C;IACA,SAASrH,cAAcA,CAACiB,IAAI,EAAE;MAC1B,MAAMqG,SAAS,GAAG,SAAAA,CAAUxE,EAAE,EAAE;QAC5B,OAAO,UAAUX,IAAI,EAAE;UACnB,OAAO0E,QAAQ,CAAC/E,GAAG,CAACgB,EAAE,EAAE,IAAI,EAAE,CAACX,IAAI,CAAC,CAAC;QACzC,CAAC;MACL,CAAC;MACD,MAAMkF,QAAQ,GAAG,SAAAA,CAAUvE,EAAE,EAAE;QAC3B,OAAO,YAAY;UACf,OAAO+D,QAAQ,CAAC/E,GAAG,CAACgB,EAAE,EAAE,IAAI,CAAC;QACjC,CAAC;MACL,CAAC;MACD,OAAOsE,eAAe,CAACnG,IAAI,EAAEoG,QAAQ,EAAEC,SAAS,CAAC;IACrD;IACA,SAASI,eAAeA,CAACzG,IAAI,EAAE;MAC3B,MAAMqG,SAAS,GAAG,SAAAA,CAAUxE,EAAE,EAAE;QAC5B,OAAO,UAAUX,IAAI,EAAE;UACnB,OAAO2E,SAAS,CAAChF,GAAG,CAACgB,EAAE,EAAE,IAAI,EAAE,CAACX,IAAI,CAAC,CAAC;QAC1C,CAAC;MACL,CAAC;MACD,MAAMkF,QAAQ,GAAG,SAAAA,CAAUvE,EAAE,EAAE;QAC3B,OAAO,YAAY;UACf,OAAOgE,SAAS,CAAChF,GAAG,CAACgB,EAAE,EAAE,IAAI,CAAC;QAClC,CAAC;MACL,CAAC;MACD,OAAOsE,eAAe,CAACnG,IAAI,EAAEoG,QAAQ,EAAEC,SAAS,CAAC;IACrD;IACAzK,MAAM,CAACoI,QAAQ,GAAGpI,MAAM,CAAC8K,KAAK,GAAG,YAAY;MACzC,OAAOZ,aAAa,CAAC9B,QAAQ,CAAChG,KAAK,CAAC,IAAI,EAAEa,kBAAkB,CAACZ,SAAS,CAAC,CAAC;IAC5E,CAAC;IACDrC,MAAM,CAACwI,SAAS,GACZxI,MAAM,CAAC8K,KAAK,CAACvC,IAAI,GACbvI,MAAM,CAACoI,QAAQ,CAACG,IAAI,GAChB,YAAY;MACR,OAAO2B,aAAa,CAAC9B,QAAQ,CAACG,IAAI,CAACnG,KAAK,CAAC,IAAI,EAAEa,kBAAkB,CAACZ,SAAS,CAAC,CAAC;IACjF,CAAC;IACbrC,MAAM,CAACoI,QAAQ,CAACC,IAAI,GAAGrI,MAAM,CAAC8K,KAAK,CAACzC,IAAI,GAAG,YAAY;MACnD,OAAO6B,aAAa,CAAC9B,QAAQ,CAACC,IAAI,CAACjG,KAAK,CAAC,IAAI,EAAEa,kBAAkB,CAACZ,SAAS,CAAC,CAAC;IACjF,CAAC;IACDrC,MAAM,CAAC2I,EAAE,GACL3I,MAAM,CAAC+K,OAAO,GACV/K,MAAM,CAAC8I,IAAI,GACP,YAAY;MACR,OAAOoB,aAAa,CAACvB,EAAE,CAACvG,KAAK,CAAC,IAAI,EAAEe,cAAc,CAACd,SAAS,CAAC,CAAC;IAClE,CAAC;IACbrC,MAAM,CAAC6I,GAAG,GACN7I,MAAM,CAACgL,QAAQ,GACXhL,MAAM,CAAC2I,EAAE,CAACJ,IAAI,GACV,YAAY;MACR,OAAO2B,aAAa,CAACvB,EAAE,CAACJ,IAAI,CAACnG,KAAK,CAAC,IAAI,EAAEe,cAAc,CAACd,SAAS,CAAC,CAAC;IACvE,CAAC;IACbrC,MAAM,CAAC2I,EAAE,CAACN,IAAI,GAAGrI,MAAM,CAAC8I,IAAI,CAACT,IAAI,GAAG,YAAY;MAC5C,OAAO6B,aAAa,CAACvB,EAAE,CAACN,IAAI,CAACjG,KAAK,CAAC,IAAI,EAAEe,cAAc,CAACd,SAAS,CAAC,CAAC;IACvE,CAAC;IACDrC,MAAM,CAACmK,KAAK,GAAGnK,MAAM,CAACiL,aAAa,GAAG,YAAY;MAC9C,OAAOf,aAAa,CAACC,KAAK,CAAC/H,KAAK,CAAC,IAAI,EAAEyI,eAAe,CAACxI,SAAS,CAAC,CAAC;IACtE,CAAC;IACDrC,MAAM,CAACoK,SAAS,GAAGpK,MAAM,CAACkL,QAAQ,GAAG,YAAY;MAC7C,OAAOhB,aAAa,CAACE,SAAS,CAAChI,KAAK,CAAC,IAAI,EAAEe,cAAc,CAACd,SAAS,CAAC,CAAC;IACzE,CAAC;IACDrC,MAAM,CAACqK,MAAM,GAAGrK,MAAM,CAACmL,UAAU,GAAG,YAAY;MAC5C,OAAOjB,aAAa,CAACG,MAAM,CAACjI,KAAK,CAAC,IAAI,EAAEyI,eAAe,CAACxI,SAAS,CAAC,CAAC;IACvE,CAAC;IACDrC,MAAM,CAACsK,UAAU,GAAGtK,MAAM,CAACoL,KAAK,GAAG,YAAY;MAC3C,OAAOlB,aAAa,CAACI,UAAU,CAAClI,KAAK,CAAC,IAAI,EAAEe,cAAc,CAACd,SAAS,CAAC,CAAC;IAC1E,CAAC;IACD,CAAC,CAACgJ,eAAe,EAAEC,WAAW,KAAK;MAC/BvB,KAAK,CAACwB,MAAM,CAAC9K,SAAS,CAAC+K,OAAO,GAAG,UAAUvF,EAAE,EAAE;QAC3CnG,IAAI,CAACoB,OAAO,CAACgF,iBAAiB,CAAC,iBAAiB,EAAE,MAAM;UACpDmF,eAAe,CAACrI,IAAI,CAAC,IAAI,EAAEiD,EAAE,CAAC;QAClC,CAAC,CAAC;MACN,CAAC;MACD8D,KAAK,CAACwB,MAAM,CAAC9K,SAAS,CAACwE,GAAG,GAAG,UAAUgB,EAAE,EAAE;QACvC,IAAI,CAAClE,EAAE,CAAC,MAAM,EAAG0J,CAAC,IAAK;UACnBzB,QAAQ,GAAGxC,QAAQ,CAACxC,IAAI,CAAC,IAAIhE,aAAa,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC;QACF,IAAI,CAACe,EAAE,CAAC,MAAM,EAAE,CAAC+G,IAAI,EAAE/B,GAAG,KAAK;UAC3B,MAAMH,aAAa,GAAGoD,QAAQ,IAAIA,QAAQ,CAACvG,GAAG,CAAC,eAAe,CAAC;UAC/D,IAAImD,aAAa,IAAIG,GAAG,EAAE;YACtB,IAAI;cACA;cACAA,GAAG,CAACJ,OAAO,IAAIC,aAAa,CAACE,2BAA2B,CAAC,CAAC;YAC9D,CAAC,CACD,OAAOJ,KAAK,EAAE,CAAE;UACpB;QACJ,CAAC,CAAC;QACF,OAAO4E,WAAW,CAACtI,IAAI,CAAC,IAAI,EAAEiD,EAAE,CAAC;MACrC,CAAC;IACL,CAAC,EAAE8D,KAAK,CAACwB,MAAM,CAAC9K,SAAS,CAAC+K,OAAO,EAAEzB,KAAK,CAACwB,MAAM,CAAC9K,SAAS,CAACwE,GAAG,CAAC;EAClE,CAAC,CAAC;AACN;AAEA,MAAMyG,QAAQ,GAAGC,UAAU;AAC3B;AACA;AACA,SAASvK,UAAUA,CAACgG,IAAI,EAAE;EACtB,MAAMwE,YAAY,GAAGF,QAAQ,CAAC,sBAAsB,CAAC,IAAI,iBAAiB;EAC1E,OAAOE,YAAY,GAAGxE,IAAI;AAC9B;AAEA,MAAMyE,QAAQ,GAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAM,OAAOxC,IAAI,KAAK,WAAW,IAAIA,IAAK,IAAItJ,MAAM;AAC7G,MAAM+L,iBAAiB,CAAC;EAGpB;EACA;EACA;EACA,WAAWC,sBAAsBA,CAAA,EAAG;IAChC,OAAO5K,UAAU,CAAC,kBAAkB,CAAC;EACzC;EAUAZ,WAAWA,CAACyL,cAAc,EAAEC,YAAY,EAAEC,UAAU,EAAE;IAAA1M,eAAA;IAAAA,eAAA;IAAAA,eAAA,6BATjC,KAAK;IAAAA,eAAA,6BACL,KAAK;IAAAA,eAAA,0BACR,KAAK;IAAAA,eAAA,kBACb,KAAK;IAAAA,eAAA,+BACQ,IAAI;IAAAA,eAAA,wBACX,IAAI;IAAAA,eAAA,kBACVK,IAAI,CAACoB,OAAO;IAAAzB,eAAA,wCACU,CAAC;IAAAA,eAAA,8CACK,KAAK;IA8D3C;IAAAA,eAAA;IAAAA,eAAA;IA5DI,IAAI,CAACwM,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC9E,IAAI,GAAG,oBAAoB,GAAG+E,UAAU;IAC7C,IAAI,CAACC,UAAU,GAAG;MAAE,mBAAmB,EAAE;IAAK,CAAC;IAC/C,IAAI,CAACC,mCAAmC,GACpCR,QAAQ,CAACzK,UAAU,CAAC,qCAAqC,CAAC,CAAC,KAAK,IAAI;EAC5E;EACAkL,iCAAiCA,CAAA,EAAG;IAChC,OAAO,IAAI,CAACC,6BAA6B,GAAG,CAAC;EACjD;EACAC,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACC,oBAAoB,KAAK,IAAI,EAAE;MACpCnG,YAAY,CAAC,IAAI,CAACmG,oBAAoB,CAAC;MACvC,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;IACA,IAAI,EAAE,IAAI,CAACC,kBAAkB,IACzB,IAAI,CAACC,kBAAkB,IACtB,IAAI,CAACN,mCAAmC,IAAI,IAAI,CAACC,iCAAiC,CAAC,CAAE,CAAC,EAAE;MACzF;MACA;MACA,IAAI,CAACM,OAAO,CAAC3H,GAAG,CAAC,MAAM;QACnB,IAAI,CAACwH,oBAAoB,GAAGpG,UAAU,CAAC,MAAM;UACzC,IAAI,CAAC,IAAI,CAACwG,eAAe,IAAI,EAAE,IAAI,CAACH,kBAAkB,IAAI,IAAI,CAACC,kBAAkB,CAAC,EAAE;YAChF,IAAI,CAACV,cAAc,CAAC,CAAC;UACzB;QACJ,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC;IACN;EACJ;EACAa,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACT,mCAAmC,EAAE;MAC3C;IACJ;IACA,MAAMS,mBAAmB,GAAGC,OAAO,CAACjN,IAAI,CAACsB,UAAU,CAAC,qBAAqB,CAAC,CAAC;IAC3E,IAAI0L,mBAAmB,EAAE;MACrBA,mBAAmB,CAAC,CAAC;IACzB;EACJ;EACAE,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACX,mCAAmC,EAAE;MAC3C;IACJ;IACA,MAAMW,qBAAqB,GAAGD,OAAO,CAACjN,IAAI,CAACsB,UAAU,CAAC,uBAAuB,CAAC,CAAC;IAC/E,IAAI4L,qBAAqB,EAAE;MACvBA,qBAAqB,CAAC,CAAC;IAC3B;EACJ;EAIAC,cAAcA,CAAC5D,QAAQ,EAAEnI,OAAO,EAAEgM,MAAM,EAAEC,IAAI,EAAE;IAC5C,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAACC,OAAO,GAAG,KAAK;IACxB;IACA,IAAIF,IAAI,CAACC,IAAI,KAAK,WAAW,IAAID,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACG,IAAI,YAAYP,OAAO,EAAE;MACxE;MACA,IAAII,IAAI,CAACG,IAAI,CAACvB,iBAAiB,CAACC,sBAAsB,CAAC,KAAK,IAAI,EAAE;QAC9D;QACA,IAAI,CAACO,6BAA6B,EAAE;MACxC;IACJ;IACA,OAAOlD,QAAQ,CAACkE,YAAY,CAACL,MAAM,EAAEC,IAAI,CAAC;EAC9C;EACAK,YAAYA,CAACnE,QAAQ,EAAEnI,OAAO,EAAEgM,MAAM,EAAEC,IAAI,EAAE/H,SAAS,EAAEqI,SAAS,EAAE;IAChE,IAAIN,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAACC,OAAO,GAAG,KAAK;IACxB;IACA,OAAOhE,QAAQ,CAACqE,UAAU,CAACR,MAAM,EAAEC,IAAI,EAAE/H,SAAS,EAAEqI,SAAS,CAAC;EAClE;EACAE,YAAYA,CAACtE,QAAQ,EAAEnI,OAAO,EAAEgM,MAAM,EAAEC,IAAI,EAAE;IAC1C,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAACC,OAAO,GAAG,KAAK;IACxB;IACA,OAAOhE,QAAQ,CAACuE,UAAU,CAACV,MAAM,EAAEC,IAAI,CAAC;EAC5C;EACA;EACA;EACA;EACA;EACA;EACAU,QAAQA,CAACC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE3E,QAAQ,EAAEjE,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,EAAE;IAC1F,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAG7E,QAAQ;IACjC;IACA,IAAI;MACA,IAAI,CAACgE,OAAO,GAAG,IAAI;MACnB,OAAOS,kBAAkB,CAACK,MAAM,CAACH,UAAU,EAAE3E,QAAQ,EAAEjE,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,CAAC;IACxF,CAAC,SACO;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACZ,OAAO,IAAI,IAAI,CAACa,aAAa,KAAK7E,QAAQ,EAAE;QACjD,IAAI,CAACmD,qBAAqB,CAAC,CAAC;MAChC;IACJ;EACJ;EACA4B,aAAaA,CAACN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEtH,KAAK,EAAE;IAC9D;IACA,MAAMvE,MAAM,GAAG2L,kBAAkB,CAACO,WAAW,CAACL,UAAU,EAAEtH,KAAK,CAAC;IAChE,IAAIvE,MAAM,EAAE;MACR,IAAI,CAAC+J,YAAY,CAACxF,KAAK,CAAC;MACxB,IAAI,CAACmG,eAAe,GAAG,IAAI;IAC/B;IACA,OAAO,KAAK;EAChB;EACAyB,SAASA,CAACjF,QAAQ,EAAEnI,OAAO,EAAEgM,MAAM,EAAEqB,YAAY,EAAE;IAC/ClF,QAAQ,CAACmF,OAAO,CAACtB,MAAM,EAAEqB,YAAY,CAAC;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIrN,OAAO,KAAKgM,MAAM,EAAE;MACpB;IACJ;IACA,IAAIqB,YAAY,CAACE,MAAM,IAAI,WAAW,EAAE;MACpC,IAAI,CAAC/B,kBAAkB,GAAG6B,YAAY,CAACG,SAAS;MAChD,IAAI,CAAClC,qBAAqB,CAAC,CAAC;IAChC,CAAC,MACI,IAAI+B,YAAY,CAACE,MAAM,IAAI,WAAW,EAAE;MACzC,IAAI,CAAC9B,kBAAkB,GAAG4B,YAAY,CAACI,SAAS;MAChD,IAAI,CAACnC,qBAAqB,CAAC,CAAC;IAChC;EACJ;AACJ;AACA,SAASoC,cAAcA,CAAC9O,IAAI,EAAE;EAC1B;EACA;EACAA,IAAI,CAAC,mBAAmB,CAAC,GAAGiM,iBAAiB;EAC7CjM,IAAI,CAACC,YAAY,CAAC,WAAW,EAAE,CAACC,MAAM,EAAEF,IAAI,EAAEG,GAAG,KAAK;IAClD;AACR;AACA;AACA;IACQH,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,SAASsJ,SAASA,CAACxE,EAAE,EAAE;MACnD;MACA;MACA,IAAIjG,MAAM,CAACc,OAAO,EAAE;QAChB;QACA,OAAO,UAAUwE,IAAI,EAAE;UACnB,IAAI,CAACA,IAAI,EAAE;YACP;YACA;YACAA,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;YACtBA,IAAI,CAACuJ,IAAI,GAAG,UAAUpD,CAAC,EAAE;cACrB,MAAMA,CAAC;YACX,CAAC;UACL;UACAvG,aAAa,CAACe,EAAE,EAAE,IAAI,EAAEX,IAAI,EAAGyB,GAAG,IAAK;YACnC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cACzB,OAAOzB,IAAI,CAACuJ,IAAI,CAAC,IAAIjO,KAAK,CAACmG,GAAG,CAAC,CAAC;YACpC,CAAC,MACI;cACDzB,IAAI,CAACuJ,IAAI,CAAC9H,GAAG,CAAC;YAClB;UACJ,CAAC,CAAC;QACN,CAAC;MACL;MACA;MACA;MACA;MACA;MACA,OAAO,YAAY;QACf,OAAO,IAAIgG,OAAO,CAAC,CAACd,cAAc,EAAEC,YAAY,KAAK;UACjDhH,aAAa,CAACe,EAAE,EAAE,IAAI,EAAEgG,cAAc,EAAEC,YAAY,CAAC;QACzD,CAAC,CAAC;MACN,CAAC;IACL,CAAC;IACD,SAAShH,aAAaA,CAACe,EAAE,EAAEsB,OAAO,EAAE0E,cAAc,EAAEC,YAAY,EAAE;MAC9D,MAAM6B,WAAW,GAAGjO,IAAI,CAACoB,OAAO;MAChC,MAAM6K,iBAAiB,GAAGjM,IAAI,CAAC,mBAAmB,CAAC;MACnD,IAAIiM,iBAAiB,KAAK+C,SAAS,EAAE;QACjC,MAAM,IAAIlO,KAAK,CAAC,kFAAkF,GAC9F,4EAA4E,CAAC;MACrF;MACA,MAAMI,aAAa,GAAGlB,IAAI,CAAC,eAAe,CAAC;MAC3C,IAAI,CAACkB,aAAa,EAAE;QAChB,MAAM,IAAIJ,KAAK,CAAC,8EAA8E,GAC1F,uEAAuE,CAAC;MAChF;MACA,MAAMgG,aAAa,GAAG5F,aAAa,CAACyC,GAAG,CAAC,CAAC;MACzCzC,aAAa,CAAC+N,aAAa,CAAC,CAAC;MAC7B;MACA;MACA,MAAMtH,SAAS,GAAG3H,IAAI,CAACoB,OAAO,CAAC8N,WAAW,CAAC,eAAe,CAAC;MAC3D,MAAMC,gBAAgB,GAAGrI,aAAa,CAACsI,WAAW,CAAC,CAAC;MACpDzH,SAAS,CAACN,MAAM,CAAClC,GAAG,CAAC,MAAM;QACvB,MAAMkK,YAAY,GAAG,IAAIpD,iBAAiB,CAAC,MAAM;UAC7C;UACA,IAAInF,aAAa,CAACsI,WAAW,CAAC,CAAC,IAAIC,YAAY,EAAE;YAC7C;YACA;YACA;YACAvI,aAAa,CAACwI,WAAW,CAACH,gBAAgB,CAAC;UAC/C;UACAE,YAAY,CAACnC,qBAAqB,CAAC,CAAC;UACpCe,WAAW,CAAC9I,GAAG,CAAC,MAAM;YAClBgH,cAAc,CAAC,CAAC;UACpB,CAAC,CAAC;QACN,CAAC,EAAGvF,KAAK,IAAK;UACV;UACA,IAAIE,aAAa,CAACsI,WAAW,CAAC,CAAC,IAAIC,YAAY,EAAE;YAC7C;YACAvI,aAAa,CAACwI,WAAW,CAACH,gBAAgB,CAAC;UAC/C;UACAE,YAAY,CAACnC,qBAAqB,CAAC,CAAC;UACpCe,WAAW,CAAC9I,GAAG,CAAC,MAAM;YAClBiH,YAAY,CAACxF,KAAK,CAAC;UACvB,CAAC,CAAC;QACN,CAAC,EAAE,MAAM,CAAC;QACVE,aAAa,CAACwI,WAAW,CAACD,YAAY,CAAC;QACvCA,YAAY,CAACrC,mBAAmB,CAAC,CAAC;MACtC,CAAC,CAAC;MACF,OAAOhN,IAAI,CAACoB,OAAO,CAACmO,UAAU,CAACpJ,EAAE,EAAEsB,OAAO,CAAC;IAC/C;EACJ,CAAC,CAAC;AACN;AAEA,MAAM+H,QAAQ,GAAI,OAAOxD,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAM,OAAOxC,IAAI,KAAK,QAAQ,IAAIA,IAAK,IAAIqC,UAAU,CAAC3L,MAAM;AAClH,MAAMuP,YAAY,GAAGD,QAAQ,CAACxL,IAAI;AAClC;AACA;AACA;AACA;AACA,SAAS0L,QAAQA,CAAA,EAAG;EAChB,IAAInN,SAAS,CAACwB,MAAM,KAAK,CAAC,EAAE;IACxB,MAAM1D,CAAC,GAAG,IAAIoP,YAAY,CAAC,CAAC;IAC5BpP,CAAC,CAACsP,OAAO,CAACD,QAAQ,CAACE,GAAG,CAAC,CAAC,CAAC;IACzB,OAAOvP,CAAC;EACZ,CAAC,MACI;IACD,MAAMiE,IAAI,GAAGC,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACX,SAAS,CAAC;IAClD,OAAO,IAAIkN,YAAY,CAAC,GAAGnL,IAAI,CAAC;EACpC;AACJ;AACAoL,QAAQ,CAACE,GAAG,GAAG,YAAY;EACvB,MAAMC,qBAAqB,GAAG7P,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;EACvE,IAAIkM,qBAAqB,EAAE;IACvB,OAAOA,qBAAqB,CAACC,iBAAiB,CAAC,CAAC;EACpD;EACA,OAAOL,YAAY,CAACG,GAAG,CAACtN,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AAClD,CAAC;AACDmN,QAAQ,CAACK,GAAG,GAAGN,YAAY,CAACM,GAAG;AAC/BL,QAAQ,CAACM,KAAK,GAAGP,YAAY,CAACO,KAAK;AACnC;AACA,IAAIC,aAAa;AACjB,MAAMC,eAAe,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;AACvC,MAAMC,SAAS,CAAC;EAYZzP,WAAWA,CAAA,EAAG;IARd;IAAAf,eAAA,0BACkB,EAAE;IACpB;IAAAA,eAAA,2BACmB,CAAC;IACpB;IAAAA,eAAA,qCAC6B8P,YAAY,CAACG,GAAG,CAAC,CAAC;IAC/C;IAAAjQ,eAAA,6CACqC,EAAE;EACvB;EAChB,OAAOyQ,SAASA,CAAA,EAAG;IACf,MAAMC,EAAE,GAAGJ,aAAa,CAAC5J,gBAAgB,CAACnD,IAAI,CAACsM,QAAQ,EAAEU,eAAe,EAAE,CAAC,CAAC;IAC5ED,aAAa,CAAC3J,kBAAkB,CAACpD,IAAI,CAACsM,QAAQ,EAAEa,EAAE,CAAC;IACnD,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;MACxB,OAAOA,EAAE;IACb;IACA;IACA;IACA,OAAOF,SAAS,CAACG,YAAY,EAAE;EACnC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACAV,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACW,0BAA0B,GAAG,IAAI,CAACD,gBAAgB;EAClE;EACAvM,qBAAqBA,CAACyM,kBAAkB,EAAE;IACtC,IAAI,CAACD,0BAA0B,GAAGC,kBAAkB;EACxD;EACAjH,iBAAiBA,CAAA,EAAG;IAChB,OAAOgG,YAAY,CAACG,GAAG,CAAC,CAAC;EAC7B;EACAe,gBAAgBA,CAACC,EAAE,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACjCA,OAAO,GAAAhR,aAAA,CAAAA,aAAA,KACA;MACCwE,IAAI,EAAE,EAAE;MACRyM,UAAU,EAAE,KAAK;MACjBC,uBAAuB,EAAE,KAAK;MAC9BX,EAAE,EAAE,CAAC,CAAC;MACNY,iBAAiB,EAAE;IACvB,CAAC,GACEH,OAAO,CACb;IACD,IAAII,SAAS,GAAGJ,OAAO,CAACT,EAAE,GAAG,CAAC,GAAGF,SAAS,CAACgB,MAAM,GAAGL,OAAO,CAACT,EAAE;IAC9DF,SAAS,CAACgB,MAAM,GAAGhB,SAAS,CAACC,SAAS,CAAC,CAAC;IACxC,IAAIgB,OAAO,GAAG,IAAI,CAACZ,gBAAgB,GAAGK,KAAK;IAC3C;IACA,IAAIQ,QAAQ,GAAG;MACXD,OAAO,EAAEA,OAAO;MAChBf,EAAE,EAAEa,SAAS;MACbI,IAAI,EAAEV,EAAE;MACRtM,IAAI,EAAEwM,OAAO,CAACxM,IAAI;MAClBuM,KAAK,EAAEA,KAAK;MACZE,UAAU,EAAED,OAAO,CAACC,UAAU;MAC9BC,uBAAuB,EAAEF,OAAO,CAACE;IACrC,CAAC;IACD,IAAIF,OAAO,CAACG,iBAAiB,EAAE;MAC3B,IAAI,CAACM,kCAAkC,CAACC,IAAI,CAACH,QAAQ,CAAC;IAC1D;IACA,IAAIzG,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG,IAAI,CAAC6G,eAAe,CAAC1N,MAAM,EAAE6G,CAAC,EAAE,EAAE;MACzC,IAAI8G,YAAY,GAAG,IAAI,CAACD,eAAe,CAAC7G,CAAC,CAAC;MAC1C,IAAIyG,QAAQ,CAACD,OAAO,GAAGM,YAAY,CAACN,OAAO,EAAE;QACzC;MACJ;IACJ;IACA,IAAI,CAACK,eAAe,CAACE,MAAM,CAAC/G,CAAC,EAAE,CAAC,EAAEyG,QAAQ,CAAC;IAC3C,OAAOH,SAAS;EACpB;EACAU,6BAA6BA,CAACvB,EAAE,EAAE;IAC9B,KAAK,IAAIzF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC6G,eAAe,CAAC1N,MAAM,EAAE6G,CAAC,EAAE,EAAE;MAClD,IAAI,IAAI,CAAC6G,eAAe,CAAC7G,CAAC,CAAC,CAACyF,EAAE,IAAIA,EAAE,EAAE;QAClC,IAAI,CAACoB,eAAe,CAACE,MAAM,CAAC/G,CAAC,EAAE,CAAC,CAAC;QACjC;MACJ;IACJ;EACJ;EACAiH,SAASA,CAAA,EAAG;IACR,IAAI,CAACJ,eAAe,GAAG,EAAE;EAC7B;EACA1H,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC0H,eAAe,CAAC1N,MAAM;EACtC;EACA8F,UAAUA,CAACiI,IAAI,GAAG,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAE;IACtC,IAAI,IAAI,CAACP,eAAe,CAAC1N,MAAM,GAAG+N,IAAI,EAAE;MACpC;IACJ;IACA;IACA;IACA,MAAMG,SAAS,GAAG,IAAI,CAACzB,gBAAgB;IACvC,MAAM0B,UAAU,GAAG,IAAI,CAACT,eAAe,CAACK,IAAI,GAAG,CAAC,CAAC;IACjD,IAAI,CAACrO,IAAI,CAACyO,UAAU,CAACd,OAAO,GAAGa,SAAS,EAAEF,MAAM,EAAEC,WAAW,CAAC;EAClE;EACAvO,IAAIA,CAAC0O,MAAM,GAAG,CAAC,EAAEJ,MAAM,EAAEC,WAAW,EAAE;IAClC,IAAII,SAAS,GAAG,IAAI,CAAC5B,gBAAgB,GAAG2B,MAAM;IAC9C,IAAIE,eAAe,GAAG,CAAC;IACvBL,WAAW,GAAGpR,MAAM,CAAC0R,MAAM,CAAC;MAAEC,iCAAiC,EAAE;IAAK,CAAC,EAAEP,WAAW,CAAC;IACrF;IACA;IACA;IACA,MAAMQ,cAAc,GAAGR,WAAW,CAACO,iCAAiC,GAC9D,IAAI,CAACd,eAAe,GACpB,IAAI,CAACA,eAAe,CAACjN,KAAK,CAAC,CAAC;IAClC,IAAIgO,cAAc,CAACzO,MAAM,KAAK,CAAC,IAAIgO,MAAM,EAAE;MACvCA,MAAM,CAACI,MAAM,CAAC;MACd;IACJ;IACA,OAAOK,cAAc,CAACzO,MAAM,GAAG,CAAC,EAAE;MAC9B;MACA,IAAI,CAACwN,kCAAkC,GAAG,EAAE;MAC5C,IAAInQ,OAAO,GAAGoR,cAAc,CAAC,CAAC,CAAC;MAC/B,IAAIJ,SAAS,GAAGhR,OAAO,CAACgQ,OAAO,EAAE;QAC7B;QACA;MACJ,CAAC,MACI;QACD;QACA,IAAIhQ,OAAO,GAAGoR,cAAc,CAACC,KAAK,CAAC,CAAC;QACpC,IAAI,CAACT,WAAW,CAACO,iCAAiC,EAAE;UAChD,MAAMG,GAAG,GAAG,IAAI,CAACjB,eAAe,CAACkB,OAAO,CAACvR,OAAO,CAAC;UACjD,IAAIsR,GAAG,IAAI,CAAC,EAAE;YACV,IAAI,CAACjB,eAAe,CAACE,MAAM,CAACe,GAAG,EAAE,CAAC,CAAC;UACvC;QACJ;QACAL,eAAe,GAAG,IAAI,CAAC7B,gBAAgB;QACvC,IAAI,CAACA,gBAAgB,GAAGpP,OAAO,CAACgQ,OAAO;QACvC,IAAIW,MAAM,EAAE;UACRA,MAAM,CAAC,IAAI,CAACvB,gBAAgB,GAAG6B,eAAe,CAAC;QACnD;QACA,IAAIO,MAAM,GAAGxR,OAAO,CAACkQ,IAAI,CAAChP,KAAK,CAACkN,QAAQ,EAAEpO,OAAO,CAAC4P,uBAAuB,GAAG,CAAC,IAAI,CAACR,gBAAgB,CAAC,GAAGpP,OAAO,CAACkD,IAAI,CAAC;QACnH,IAAI,CAACsO,MAAM,EAAE;UACT;UACA;QACJ;QACA;QACA;QACA,IAAI,CAACZ,WAAW,CAACO,iCAAiC,EAAE;UAChD,IAAI,CAAChB,kCAAkC,CAAC9O,OAAO,CAAE4O,QAAQ,IAAK;YAC1D,IAAIzG,CAAC,GAAG,CAAC;YACT,OAAOA,CAAC,GAAG4H,cAAc,CAACzO,MAAM,EAAE6G,CAAC,EAAE,EAAE;cACnC,MAAM8G,YAAY,GAAGc,cAAc,CAAC5H,CAAC,CAAC;cACtC,IAAIyG,QAAQ,CAACD,OAAO,GAAGM,YAAY,CAACN,OAAO,EAAE;gBACzC;cACJ;YACJ;YACAoB,cAAc,CAACb,MAAM,CAAC/G,CAAC,EAAE,CAAC,EAAEyG,QAAQ,CAAC;UACzC,CAAC,CAAC;QACN;MACJ;IACJ;IACAgB,eAAe,GAAG,IAAI,CAAC7B,gBAAgB;IACvC,IAAI,CAACA,gBAAgB,GAAG4B,SAAS;IACjC,IAAIL,MAAM,EAAE;MACRA,MAAM,CAAC,IAAI,CAACvB,gBAAgB,GAAG6B,eAAe,CAAC;IACnD;EACJ;EACAzI,sBAAsBA,CAACmI,MAAM,EAAE;IAC3B,IAAI,IAAI,CAACN,eAAe,CAAC1N,MAAM,KAAK,CAAC,EAAE;MACnC,OAAO,CAAC;IACZ;IACA;IACA;IACA,MAAMkO,SAAS,GAAG,IAAI,CAACzB,gBAAgB;IACvC,MAAMqC,QAAQ,GAAG,IAAI,CAACpB,eAAe,CAAC,IAAI,CAACA,eAAe,CAAC1N,MAAM,GAAG,CAAC,CAAC;IACtE,IAAI,CAACN,IAAI,CAACoP,QAAQ,CAACzB,OAAO,GAAGa,SAAS,EAAEF,MAAM,EAAE;MAAEQ,iCAAiC,EAAE;IAAM,CAAC,CAAC;IAC7F,OAAO,IAAI,CAAC/B,gBAAgB,GAAGyB,SAAS;EAC5C;EACAtI,KAAKA,CAACmJ,KAAK,GAAG,EAAE,EAAEC,aAAa,GAAG,KAAK,EAAEhB,MAAM,EAAE;IAC7C,IAAIgB,aAAa,EAAE;MACf,OAAO,IAAI,CAACA,aAAa,CAAChB,MAAM,CAAC;IACrC,CAAC,MACI;MACD,OAAO,IAAI,CAACiB,gBAAgB,CAACF,KAAK,EAAEf,MAAM,CAAC;IAC/C;EACJ;EACAgB,aAAaA,CAAChB,MAAM,EAAE;IAClB,IAAI,IAAI,CAACN,eAAe,CAAC1N,MAAM,KAAK,CAAC,EAAE;MACnC,OAAO,CAAC;IACZ;IACA;IACA;IACA,MAAMkO,SAAS,GAAG,IAAI,CAACzB,gBAAgB;IACvC,MAAMqC,QAAQ,GAAG,IAAI,CAACpB,eAAe,CAAC,IAAI,CAACA,eAAe,CAAC1N,MAAM,GAAG,CAAC,CAAC;IACtE,IAAI,CAACN,IAAI,CAACoP,QAAQ,CAACzB,OAAO,GAAGa,SAAS,EAAEF,MAAM,CAAC;IAC/C,OAAO,IAAI,CAACvB,gBAAgB,GAAGyB,SAAS;EAC5C;EACAe,gBAAgBA,CAACF,KAAK,EAAEf,MAAM,EAAE;IAC5B,MAAME,SAAS,GAAG,IAAI,CAACzB,gBAAgB;IACvC,IAAI6B,eAAe,GAAG,CAAC;IACvB,IAAIY,KAAK,GAAG,CAAC;IACb,OAAO,IAAI,CAACxB,eAAe,CAAC1N,MAAM,GAAG,CAAC,EAAE;MACpCkP,KAAK,EAAE;MACP,IAAIA,KAAK,GAAGH,KAAK,EAAE;QACf,MAAM,IAAIhS,KAAK,CAAC,2CAA2C,GACvDgS,KAAK,GACL,+CAA+C,CAAC;MACxD;MACA;MACA;MACA,IAAI,IAAI,CAACrB,eAAe,CAACyB,MAAM,CAAE7F,IAAI,IAAK,CAACA,IAAI,CAAC0D,UAAU,IAAI,CAAC1D,IAAI,CAAC2D,uBAAuB,CAAC,CACvFjN,MAAM,KAAK,CAAC,EAAE;QACf;MACJ;MACA,MAAM3C,OAAO,GAAG,IAAI,CAACqQ,eAAe,CAACgB,KAAK,CAAC,CAAC;MAC5CJ,eAAe,GAAG,IAAI,CAAC7B,gBAAgB;MACvC,IAAI,CAACA,gBAAgB,GAAGpP,OAAO,CAACgQ,OAAO;MACvC,IAAIW,MAAM,EAAE;QACR;QACAA,MAAM,CAAC,IAAI,CAACvB,gBAAgB,GAAG6B,eAAe,CAAC;MACnD;MACA,MAAMO,MAAM,GAAGxR,OAAO,CAACkQ,IAAI,CAAChP,KAAK,CAACkN,QAAQ,EAAEpO,OAAO,CAACkD,IAAI,CAAC;MACzD,IAAI,CAACsO,MAAM,EAAE;QACT;QACA;MACJ;IACJ;IACA,OAAO,IAAI,CAACpC,gBAAgB,GAAGyB,SAAS;EAC5C;AACJ;AA5NI;AAAAtS,eAAA,CADEwQ,SAAS,kBAEW,CAAC;AAAAxQ,eAAA,CAFrBwQ,SAAS,YAGK,CAAC,CAAC;AA2NtB,MAAMhM,qBAAqB,CAAC;EAGxB,OAAOgP,YAAYA,CAAA,EAAG;IAClB,IAAInT,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC,IAAI,IAAI,EAAE;MACnD,MAAM,IAAI7C,KAAK,CAAC,wEAAwE,CAAC;IAC7F;EACJ;EAQAJ,WAAWA,CAAC2L,UAAU,EAAE+G,iCAAiC,GAAG,KAAK,EAAEC,gBAAgB,EAAE;IAAA1T,eAAA;IAAAA,eAAA;IAAAA,eAAA,qBAPxE,IAAIwQ,SAAS,CAAC,CAAC;IAAAxQ,eAAA,sBACd,EAAE;IAAAA,eAAA,qBACH,IAAI;IAAAA,eAAA,iCACQsN,OAAO,CAACjN,IAAI,CAACsB,UAAU,CAAC,uBAAuB,CAAC,CAAC;IAAA3B,eAAA,gCAClD,EAAE;IAAAA,eAAA,wBACV,EAAE;IAAAA,eAAA,0BACA,KAAK;IAsNvB;IAAAA,eAAA;IAAAA,eAAA,qBAEa;MAAE,uBAAuB,EAAE;IAAK,CAAC;IAtN1C,IAAI,CAACyT,iCAAiC,GAAGA,iCAAiC;IAC1E,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC/L,IAAI,GAAG,wBAAwB,GAAG+E,UAAU;IACjD;IACA;IACA,IAAI,CAAC,IAAI,CAACgH,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG7D,QAAQ,CAACxP,IAAI,CAACsB,UAAU,CAAC,wBAAwB,CAAC,CAAC;IAC/E;EACJ;EACAgS,WAAWA,CAACnN,EAAE,EAAEoN,UAAU,EAAE;IACxB,OAAO,CAAC,GAAGjP,IAAI,KAAK;MAChB6B,EAAE,CAAC7D,KAAK,CAACkN,QAAQ,EAAElL,IAAI,CAAC;MACxB,IAAI,IAAI,CAACkP,UAAU,KAAK,IAAI,EAAE;QAC1B;QACA,IAAID,UAAU,CAACE,SAAS,IAAI,IAAI,EAAE;UAC9BF,UAAU,CAACE,SAAS,CAACnR,KAAK,CAACkN,QAAQ,CAAC;QACxC;QACA;QACA,IAAI,CAAC9F,eAAe,CAAC,CAAC;MAC1B,CAAC,MACI;QACD;QACA,IAAI6J,UAAU,CAACG,OAAO,IAAI,IAAI,EAAE;UAC5BH,UAAU,CAACG,OAAO,CAACpR,KAAK,CAACkN,QAAQ,CAAC;QACtC;MACJ;MACA;MACA,OAAO,IAAI,CAACgE,UAAU,KAAK,IAAI;IACnC,CAAC;EACL;EACA,OAAOG,YAAYA,CAACC,MAAM,EAAEvD,EAAE,EAAE;IAC5B,IAAIwD,KAAK,GAAGD,MAAM,CAACjB,OAAO,CAACtC,EAAE,CAAC;IAC9B,IAAIwD,KAAK,GAAG,CAAC,CAAC,EAAE;MACZD,MAAM,CAACjC,MAAM,CAACkC,KAAK,EAAE,CAAC,CAAC;IAC3B;EACJ;EACAC,aAAaA,CAACzD,EAAE,EAAE;IACd,OAAO,MAAM;MACTlM,qBAAqB,CAACwP,YAAY,CAAC,IAAI,CAACI,aAAa,EAAE1D,EAAE,CAAC;IAC9D,CAAC;EACL;EACA2D,qBAAqBA,CAAC7N,EAAE,EAAE8N,QAAQ,EAAE3P,IAAI,EAAE+L,EAAE,EAAE;IAC1C,OAAO,MAAM;MACT;MACA,IAAI,IAAI,CAAC6D,qBAAqB,CAACvB,OAAO,CAACtC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAC/C,IAAI,CAAC8D,UAAU,CAACxD,gBAAgB,CAACxK,EAAE,EAAE8N,QAAQ,EAAE;UAC3C3P,IAAI;UACJyM,UAAU,EAAE,IAAI;UAChBV,EAAE;UACFY,iBAAiB,EAAE;QACvB,CAAC,CAAC;MACN;IACJ,CAAC;EACL;EACAmD,qBAAqBA,CAAC/D,EAAE,EAAE;IACtB,OAAO,MAAM;MACTlM,qBAAqB,CAACwP,YAAY,CAAC,IAAI,CAACO,qBAAqB,EAAE7D,EAAE,CAAC;IACtE,CAAC;EACL;EACAgE,WAAWA,CAAClO,EAAE,EAAE0K,KAAK,EAAEvM,IAAI,EAAEgQ,OAAO,GAAG,IAAI,EAAE;IACzC,IAAIC,aAAa,GAAG,IAAI,CAACT,aAAa,CAAC3D,SAAS,CAACgB,MAAM,CAAC;IACxD;IACA,IAAIP,EAAE,GAAG,IAAI,CAAC0C,WAAW,CAACnN,EAAE,EAAE;MAAEsN,SAAS,EAAEc,aAAa;MAAEb,OAAO,EAAEa;IAAc,CAAC,CAAC;IACnF,IAAIlE,EAAE,GAAG,IAAI,CAAC8D,UAAU,CAACxD,gBAAgB,CAACC,EAAE,EAAEC,KAAK,EAAE;MAAEvM,IAAI;MAAE0M,uBAAuB,EAAE,CAACsD;IAAQ,CAAC,CAAC;IACjG,IAAIA,OAAO,EAAE;MACT,IAAI,CAACP,aAAa,CAACvC,IAAI,CAACnB,EAAE,CAAC;IAC/B;IACA,OAAOA,EAAE;EACb;EACAmE,aAAaA,CAACnE,EAAE,EAAE;IACdlM,qBAAqB,CAACwP,YAAY,CAAC,IAAI,CAACI,aAAa,EAAE1D,EAAE,CAAC;IAC1D,IAAI,CAAC8D,UAAU,CAACvC,6BAA6B,CAACvB,EAAE,CAAC;EACrD;EACAoE,YAAYA,CAACtO,EAAE,EAAE8N,QAAQ,EAAE3P,IAAI,EAAE;IAC7B,IAAI+L,EAAE,GAAGF,SAAS,CAACgB,MAAM;IACzB,IAAIoC,UAAU,GAAG;MAAEE,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE,IAAI,CAACU,qBAAqB,CAAC/D,EAAE;IAAE,CAAC;IAC7E,IAAIO,EAAE,GAAG,IAAI,CAAC0C,WAAW,CAACnN,EAAE,EAAEoN,UAAU,CAAC;IACzC;IACAA,UAAU,CAACE,SAAS,GAAG,IAAI,CAACO,qBAAqB,CAACpD,EAAE,EAAEqD,QAAQ,EAAE3P,IAAI,EAAE+L,EAAE,CAAC;IACzE;IACA,IAAI,CAAC8D,UAAU,CAACxD,gBAAgB,CAACC,EAAE,EAAEqD,QAAQ,EAAE;MAAE3P,IAAI;MAAEyM,UAAU,EAAE;IAAK,CAAC,CAAC;IAC1E,IAAI,CAACmD,qBAAqB,CAAC1C,IAAI,CAACnB,EAAE,CAAC;IACnC,OAAOA,EAAE;EACb;EACAqE,cAAcA,CAACrE,EAAE,EAAE;IACflM,qBAAqB,CAACwP,YAAY,CAAC,IAAI,CAACO,qBAAqB,EAAE7D,EAAE,CAAC;IAClE,IAAI,CAAC8D,UAAU,CAACvC,6BAA6B,CAACvB,EAAE,CAAC;EACrD;EACAsE,uBAAuBA,CAAA,EAAG;IACtB,IAAI/N,KAAK,GAAG,IAAI,CAAC4M,UAAU,IAAI,IAAI,CAACoB,sBAAsB,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACA,sBAAsB,CAAC7Q,MAAM,GAAG,CAAC;IACtC,IAAI,CAACyP,UAAU,GAAG,IAAI;IACtB,MAAM5M,KAAK;EACf;EACA2J,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC4D,UAAU,CAAC5D,kBAAkB,CAAC,CAAC;EAC/C;EACAT,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACqE,UAAU,CAACrE,iBAAiB,CAAC,CAAC;EAC9C;EACA7L,qBAAqBA,CAAC4Q,QAAQ,EAAE;IAC5B,IAAI,CAACV,UAAU,CAAClQ,qBAAqB,CAAC4Q,QAAQ,CAAC;EACnD;EACApL,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC0K,UAAU,CAAC1K,iBAAiB,CAAC,CAAC;EAC9C;EACA,OAAOqL,SAASA,CAAA,EAAG;IACf,IAAI,CAAC,CAACtF,QAAQ,CAACxP,IAAI,CAACsB,UAAU,CAAC,qBAAqB,CAAC,CAAC,EAAE;MACpD;MACA;MACA;MACA;MACA;MACA;IACJ;IACA,IAAIkO,QAAQ,CAAC,MAAM,CAAC,KAAKE,QAAQ,EAAE;MAC/B;MACA;IACJ;IACAF,QAAQ,CAAC,MAAM,CAAC,GAAGE,QAAQ;IAC3BA,QAAQ,CAAC/O,SAAS,GAAG8O,YAAY,CAAC9O,SAAS;IAC3C;IACA;IACA;IACAwD,qBAAqB,CAAC4Q,eAAe,CAAC,CAAC;EAC3C;EACA,OAAOC,SAASA,CAAA,EAAG;IACf,IAAIxF,QAAQ,CAAC,MAAM,CAAC,KAAKE,QAAQ,EAAE;MAC/BF,QAAQ,CAAC,MAAM,CAAC,GAAGC,YAAY;IACnC;EACJ;EACA,OAAOsF,eAAeA,CAAA,EAAG;IACrB,IAAI,CAAC9E,aAAa,EAAE;MAChB,MAAM,IAAInP,KAAK,CAAC,uCAAuC,CAAC;IAC5D;IACA,IAAI0O,QAAQ,CAACjJ,UAAU,KAAK0J,aAAa,CAAC1J,UAAU,EAAE;MAClDiJ,QAAQ,CAACjJ,UAAU,GAAG0J,aAAa,CAAC1J,UAAU;MAC9CiJ,QAAQ,CAAChJ,YAAY,GAAGyJ,aAAa,CAACzJ,YAAY;IACtD;IACA,IAAIgJ,QAAQ,CAACyF,WAAW,KAAKhF,aAAa,CAACgF,WAAW,EAAE;MACpDzF,QAAQ,CAACyF,WAAW,GAAGhF,aAAa,CAACgF,WAAW;MAChDzF,QAAQ,CAAC0F,aAAa,GAAGjF,aAAa,CAACiF,aAAa;IACxD;EACJ;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3BjR,qBAAqB,CAAC2Q,SAAS,CAAC,CAAC;EACrC;EACAO,eAAeA,CAAA,EAAG;IACd,IAAI,CAACD,eAAe,GAAG,KAAK;IAC5BjR,qBAAqB,CAAC6Q,SAAS,CAAC,CAAC;EACrC;EACAnL,UAAUA,CAACyL,KAAK,GAAG,CAAC,EAAEvD,MAAM,EAAEC,WAAW,GAAG;IAAEO,iCAAiC,EAAE;EAAK,CAAC,EAAE;IACrF,IAAI+C,KAAK,IAAI,CAAC,EAAE;MACZ;IACJ;IACAnR,qBAAqB,CAACgP,YAAY,CAAC,CAAC;IACpC,IAAI,CAACzJ,eAAe,CAAC,CAAC;IACtB,IAAI,CAACyK,UAAU,CAACtK,UAAU,CAACyL,KAAK,EAAEvD,MAAM,EAAEC,WAAW,CAAC;IACtD,IAAI,IAAI,CAACwB,UAAU,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACmB,uBAAuB,CAAC,CAAC;IAClC;EACJ;EACAlR,IAAIA,CAAC0O,MAAM,GAAG,CAAC,EAAEJ,MAAM,EAAEC,WAAW,GAAG;IAAEO,iCAAiC,EAAE;EAAK,CAAC,EAAE;IAChFpO,qBAAqB,CAACgP,YAAY,CAAC,CAAC;IACpC,IAAI,CAACzJ,eAAe,CAAC,CAAC;IACtB,IAAI,CAACyK,UAAU,CAAC1Q,IAAI,CAAC0O,MAAM,EAAEJ,MAAM,EAAEC,WAAW,CAAC;IACjD,IAAI,IAAI,CAACwB,UAAU,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACmB,uBAAuB,CAAC,CAAC;IAClC;EACJ;EACAjL,eAAeA,CAAA,EAAG;IACdvF,qBAAqB,CAACgP,YAAY,CAAC,CAAC;IACpC,MAAMoC,WAAW,GAAGA,CAAA,KAAM;MACtB,IAAI,IAAI,CAAC/B,UAAU,KAAK,IAAI,IAAI,IAAI,CAACoB,sBAAsB,CAAC7Q,MAAM,EAAE;QAChE;QACA,IAAI,CAAC4Q,uBAAuB,CAAC,CAAC;MAClC;IACJ,CAAC;IACD,OAAO,IAAI,CAACa,WAAW,CAACzR,MAAM,GAAG,CAAC,EAAE;MAChC,IAAI0R,SAAS,GAAG,IAAI,CAACD,WAAW,CAAC/C,KAAK,CAAC,CAAC;MACxCgD,SAAS,CAACnE,IAAI,CAAChP,KAAK,CAACmT,SAAS,CAACrI,MAAM,EAAEqI,SAAS,CAACnR,IAAI,CAAC;IAC1D;IACAiR,WAAW,CAAC,CAAC;EACjB;EACA5L,KAAKA,CAACmJ,KAAK,EAAEC,aAAa,EAAEhB,MAAM,EAAE;IAChC5N,qBAAqB,CAACgP,YAAY,CAAC,CAAC;IACpC,IAAI,CAACzJ,eAAe,CAAC,CAAC;IACtB,MAAMgM,OAAO,GAAG,IAAI,CAACvB,UAAU,CAACxK,KAAK,CAACmJ,KAAK,EAAEC,aAAa,EAAEhB,MAAM,CAAC;IACnE,IAAI,IAAI,CAACyB,UAAU,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACmB,uBAAuB,CAAC,CAAC;IAClC;IACA,OAAOe,OAAO;EAClB;EACA9L,sBAAsBA,CAACmI,MAAM,EAAE;IAC3B5N,qBAAqB,CAACgP,YAAY,CAAC,CAAC;IACpC,IAAI,CAACzJ,eAAe,CAAC,CAAC;IACtB,MAAMgM,OAAO,GAAG,IAAI,CAACvB,UAAU,CAACvK,sBAAsB,CAACmI,MAAM,CAAC;IAC9D,IAAI,IAAI,CAACyB,UAAU,KAAK,IAAI,EAAE;MAC1B,IAAI,CAACmB,uBAAuB,CAAC,CAAC;IAClC;IACA,OAAOe,OAAO;EAClB;EACA5L,eAAeA,CAAA,EAAG;IACd3F,qBAAqB,CAACgP,YAAY,CAAC,CAAC;IACpC,IAAI,CAACgB,UAAU,CAACtC,SAAS,CAAC,CAAC;IAC3B,IAAI,CAACqC,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACH,aAAa,GAAG,EAAE;EAC3B;EACAhK,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACoK,UAAU,CAACpK,aAAa,CAAC,CAAC,GAAG,IAAI,CAACyL,WAAW,CAACzR,MAAM;EACpE;EAIAoJ,cAAcA,CAAC5D,QAAQ,EAAEnI,OAAO,EAAEgM,MAAM,EAAEC,IAAI,EAAE;IAC5C,QAAQA,IAAI,CAACC,IAAI;MACb,KAAK,WAAW;QACZ,IAAIhJ,IAAI,GAAG+I,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACG,IAAI,CAAClJ,IAAI;QACtC;QACA;QACA;QACA,IAAIqR,cAAc;QAClB,IAAIrR,IAAI,EAAE;UACN,IAAIsR,aAAa,GAAGvI,IAAI,CAACG,IAAI,CAACqI,KAAK;UACnC,IAAI,OAAOvR,IAAI,CAACP,MAAM,KAAK,QAAQ,IAAIO,IAAI,CAACP,MAAM,GAAG6R,aAAa,GAAG,CAAC,EAAE;YACpED,cAAc,GAAGpR,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACoB,IAAI,EAAEsR,aAAa,GAAG,CAAC,CAAC;UACxE;QACJ;QACA,IAAI,CAACJ,WAAW,CAAChE,IAAI,CAAC;UAClBF,IAAI,EAAEjE,IAAI,CAACgB,MAAM;UACjB/J,IAAI,EAAEqR,cAAc;UACpBvI,MAAM,EAAEC,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACG,IAAI,CAACJ;QACnC,CAAC,CAAC;QACF;MACJ,KAAK,WAAW;QACZ,QAAQC,IAAI,CAACc,MAAM;UACf,KAAK,YAAY;YACbd,IAAI,CAACG,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC6G,WAAW,CAAChH,IAAI,CAACgB,MAAM,EAAEhB,IAAI,CAACG,IAAI,CAAC,OAAO,CAAC,EAAEjJ,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACmK,IAAI,CAACG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3H;UACJ,KAAK,cAAc;YACfH,IAAI,CAACG,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC6G,WAAW,CAAChH,IAAI,CAACgB,MAAM,EAAE,CAAC,EAAE9J,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACmK,IAAI,CAACG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1G;UACJ,KAAK,aAAa;YACdH,IAAI,CAACG,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAACiH,YAAY,CAACpH,IAAI,CAACgB,MAAM,EAAEhB,IAAI,CAACG,IAAI,CAAC,OAAO,CAAC,EAAEjJ,KAAK,CAAC5D,SAAS,CAAC6D,KAAK,CAACtB,IAAI,CAACmK,IAAI,CAACG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5H;UACJ,KAAK,qBAAqB;YACtB,MAAM,IAAI1M,KAAK,CAAC,+DAA+D,GAC3EuM,IAAI,CAACG,IAAI,CAAC,KAAK,CAAC,CAAC;UACzB,KAAK,uBAAuB;UAC5B,KAAK,6BAA6B;UAClC,KAAK,0BAA0B;YAC3B;YACA;YACAH,IAAI,CAACG,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC6G,WAAW,CAAChH,IAAI,CAACgB,MAAM,EAAE,EAAE,EAAEhB,IAAI,CAACG,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC4F,iCAAiC,CAAC;YACpH;UACJ;YACI;YACA;YACA,MAAM0C,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAC1I,IAAI,CAAC;YACtD,IAAIyI,eAAe,EAAE;cACjB,MAAMxR,IAAI,GAAG+I,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACG,IAAI,CAAC,MAAM,CAAC;cAC3C,MAAMqD,KAAK,GAAGvM,IAAI,IAAIA,IAAI,CAACP,MAAM,GAAG,CAAC,GAAGO,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;cACnD,IAAI0R,YAAY,GAAGF,eAAe,CAACE,YAAY,GAAGF,eAAe,CAACE,YAAY,GAAG1R,IAAI;cACrF,IAAI,CAAC,CAACwR,eAAe,CAAC/E,UAAU,EAAE;gBAC9B;gBACA1D,IAAI,CAACG,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAACiH,YAAY,CAACpH,IAAI,CAACgB,MAAM,EAAEwC,KAAK,EAAEmF,YAAY,CAAC;gBAC3E3I,IAAI,CAACG,IAAI,CAACuD,UAAU,GAAG,IAAI;cAC/B,CAAC,MACI;gBACD;gBACA1D,IAAI,CAACG,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC6G,WAAW,CAAChH,IAAI,CAACgB,MAAM,EAAEwC,KAAK,EAAEmF,YAAY,CAAC;cAC9E;cACA;YACJ;YACA,MAAM,IAAIlV,KAAK,CAAC,kDAAkD,GAAGuM,IAAI,CAACc,MAAM,CAAC;QACzF;QACA;MACJ,KAAK,WAAW;QACZd,IAAI,GAAG9D,QAAQ,CAACkE,YAAY,CAACL,MAAM,EAAEC,IAAI,CAAC;QAC1C;IACR;IACA,OAAOA,IAAI;EACf;EACAQ,YAAYA,CAACtE,QAAQ,EAAEnI,OAAO,EAAEgM,MAAM,EAAEC,IAAI,EAAE;IAC1C,QAAQA,IAAI,CAACc,MAAM;MACf,KAAK,YAAY;MACjB,KAAK,uBAAuB;MAC5B,KAAK,6BAA6B;MAClC,KAAK,0BAA0B;QAC3B,OAAO,IAAI,CAACqG,aAAa,CAACnH,IAAI,CAACG,IAAI,CAAC,UAAU,CAAC,CAAC;MACpD,KAAK,aAAa;QACd,OAAO,IAAI,CAACkH,cAAc,CAACrH,IAAI,CAACG,IAAI,CAAC,UAAU,CAAC,CAAC;MACrD;QACI;QACA;QACA,MAAMsI,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAC1I,IAAI,CAAC;QACtD,IAAIyI,eAAe,EAAE;UACjB,MAAMG,QAAQ,GAAG5I,IAAI,CAACG,IAAI,CAAC,UAAU,CAAC;UACtC,OAAOsI,eAAe,CAAC/E,UAAU,GAC3B,IAAI,CAAC2D,cAAc,CAACuB,QAAQ,CAAC,GAC7B,IAAI,CAACzB,aAAa,CAACyB,QAAQ,CAAC;QACtC;QACA,OAAO1M,QAAQ,CAACuE,UAAU,CAACV,MAAM,EAAEC,IAAI,CAAC;IAChD;EACJ;EACAU,QAAQA,CAACxE,QAAQ,EAAEnI,OAAO,EAAEgM,MAAM,EAAE8I,QAAQ,EAAE5Q,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,EAAE;IACxE,IAAI;MACAhK,qBAAqB,CAAC2Q,SAAS,CAAC,CAAC;MACjC,OAAOvL,QAAQ,CAAC8E,MAAM,CAACjB,MAAM,EAAE8I,QAAQ,EAAE5Q,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,CAAC;IAC1E,CAAC,SACO;MACJ,IAAI,CAAC,IAAI,CAACiH,eAAe,EAAE;QACvBjR,qBAAqB,CAAC6Q,SAAS,CAAC,CAAC;MACrC;IACJ;EACJ;EACAe,mBAAmBA,CAAC1I,IAAI,EAAE;IACtB,IAAI,CAAC,IAAI,CAACgG,gBAAgB,EAAE;MACxB,OAAO,IAAI;IACf;IACA,KAAK,IAAIzI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACyI,gBAAgB,CAACtP,MAAM,EAAE6G,CAAC,EAAE,EAAE;MACnD,MAAMkL,eAAe,GAAG,IAAI,CAACzC,gBAAgB,CAACzI,CAAC,CAAC;MAChD,IAAIkL,eAAe,CAAC3H,MAAM,KAAKd,IAAI,CAACc,MAAM,EAAE;QACxC,OAAO2H,eAAe;MAC1B;IACJ;IACA,OAAO,IAAI;EACf;EACAxH,aAAaA,CAACN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEtH,KAAK,EAAE;IAC9D;IACA;IACA,IAAI,CAAC4M,UAAU,GAAG5M,KAAK;IACvB,OAAO,KAAK,CAAC,CAAC;EAClB;AACJ;AACA,IAAIuP,sBAAsB,GAAG,IAAI;AACjC,SAASC,gBAAgBA,CAAA,EAAG;EACxB,OAAOpW,IAAI,IAAIA,IAAI,CAAC,eAAe,CAAC;AACxC;AACA,IAAIqW,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,gBAAgB,GAAG,IAAI;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAAA,IAAAC,iBAAA,EAAAC,qBAAA;EAC1B,IAAIN,sBAAsB,EAAE;IACxBA,sBAAsB,CAACd,eAAe,CAAC,CAAC;EAC5C;EACAc,sBAAsB,GAAG,IAAI;EAC7B,CAAAK,iBAAA,GAAAJ,gBAAgB,CAAC,CAAC,cAAAI,iBAAA,gBAAAA,iBAAA,GAAlBA,iBAAA,CAAoB7S,GAAG,CAAC,CAAC,cAAA6S,iBAAA,eAAzBA,iBAAA,CAA2BE,aAAa,CAAC,CAAC;EAC1C,CAAAD,qBAAA,GAAAJ,oBAAoB,cAAAI,qBAAA,eAApBA,qBAAA,CAAsBC,aAAa,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS7Q,SAASA,CAACM,EAAE,EAAE2K,OAAO,GAAG,CAAC,CAAC,EAAE;EACjC,MAAM;IAAEnH,KAAK,GAAG;EAAK,CAAC,GAAGmH,OAAO;EAChC;EACA,MAAM6F,WAAW,GAAG,SAAAA,CAAU,GAAGrS,IAAI,EAAE;IACnC,MAAMpD,aAAa,GAAGkV,gBAAgB,CAAC,CAAC;IACxC,IAAI,CAAClV,aAAa,EAAE;MAChB,MAAM,IAAIJ,KAAK,CAAC,kFAAkF,GAC9F,0DAA0D,CAAC;IACnE;IACA,MAAMgG,aAAa,GAAG5F,aAAa,CAAC+N,aAAa,CAAC,CAAC;IACnD,IAAIjP,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC,EAAE;MAC3C,MAAM,IAAI7C,KAAK,CAAC,qCAAqC,CAAC;IAC1D;IACA,IAAI;MACA;MACA,IAAI,CAACqV,sBAAsB,EAAE;QACzB,MAAMhS,qBAAqB,GAAGnE,IAAI,IAAIA,IAAI,CAAC,uBAAuB,CAAC;QACnE,IAAI8G,aAAa,CAACsI,WAAW,CAAC,CAAC,YAAYjL,qBAAqB,EAAE;UAC9D,MAAM,IAAIrD,KAAK,CAAC,qCAAqC,CAAC;QAC1D;QACAqV,sBAAsB,GAAG,IAAIhS,qBAAqB,CAAC,CAAC;MACxD;MACA,IAAIyS,GAAG;MACP,MAAMC,iBAAiB,GAAG/P,aAAa,CAACsI,WAAW,CAAC,CAAC;MACrDtI,aAAa,CAACwI,WAAW,CAAC6G,sBAAsB,CAAC;MACjDA,sBAAsB,CAAChB,aAAa,CAAC,CAAC;MACtC,IAAI;QACAyB,GAAG,GAAGzQ,EAAE,CAAC7D,KAAK,CAAC,IAAI,EAAEgC,IAAI,CAAC;QAC1B,IAAIqF,KAAK,EAAE;UACPwM,sBAAsB,CAACxM,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC;QAC1C,CAAC,MACI;UACDD,eAAe,CAAC,CAAC;QACrB;MACJ,CAAC,SACO;QACJ5C,aAAa,CAACwI,WAAW,CAACuH,iBAAiB,CAAC;MAChD;MACA,IAAI,CAAClN,KAAK,EAAE;QACR,IAAIwM,sBAAsB,CAACjC,qBAAqB,CAACnQ,MAAM,GAAG,CAAC,EAAE;UACzD,MAAM,IAAIjD,KAAK,CAAC,GAAGqV,sBAAsB,CAACjC,qBAAqB,CAACnQ,MAAM,GAAG,GACrE,uCAAuC,CAAC;QAChD;QACA,IAAIoS,sBAAsB,CAACpC,aAAa,CAAChQ,MAAM,GAAG,CAAC,EAAE;UACjD,MAAM,IAAIjD,KAAK,CAAC,GAAGqV,sBAAsB,CAACpC,aAAa,CAAChQ,MAAM,+BAA+B,CAAC;QAClG;MACJ;MACA,OAAO6S,GAAG;IACd,CAAC,SACO;MACJL,kBAAkB,CAAC,CAAC;IACxB;EACJ,CAAC;EACDI,WAAW,CAACxO,WAAW,GAAG,IAAI;EAC9B,OAAOwO,WAAW;AACtB;AACA,SAASG,qBAAqBA,CAAA,EAAG;EAC7B,IAAIX,sBAAsB,IAAI,IAAI,EAAE;IAChCA,sBAAsB,GAAGnW,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,uBAAuB,CAAC;IAClE,IAAIwS,sBAAsB,IAAI,IAAI,EAAE;MAChC,MAAM,IAAIrV,KAAK,CAAC,wEAAwE,CAAC;IAC7F;EACJ;EACA,OAAOqV,sBAAsB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1S,IAAIA,CAAC0O,MAAM,GAAG,CAAC,EAAE4E,mBAAmB,GAAG,KAAK,EAAE;EACnDD,qBAAqB,CAAC,CAAC,CAACrT,IAAI,CAAC0O,MAAM,EAAE,IAAI,EAAE4E,mBAAmB,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASpN,KAAKA,CAACqN,QAAQ,EAAE;EACrB,OAAOF,qBAAqB,CAAC,CAAC,CAACnN,KAAK,CAACqN,QAAQ,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAMC,QAAQ,GAAGJ,qBAAqB,CAAC,CAAC;EACxCI,QAAQ,CAAChD,qBAAqB;EAC9BgD,QAAQ,CAAChD,qBAAqB,CAACnQ,MAAM,GAAG,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoT,aAAaA,CAAChR,EAAE,EAAE;EACvB,MAAMiR,WAAW,GAAG,SAAAA,CAAU,GAAG9S,IAAI,EAAE;IACnC,MAAMwC,aAAa,GAAGsP,gBAAgB,CAAC,CAAC;IACxC,IAAItP,aAAa,KAAKkI,SAAS,EAAE;MAC7B,MAAM,IAAIlO,KAAK,CAAC,sFAAsF,GAClG,0DAA0D,CAAC;IACnE;IACA,MAAM6G,SAAS,GAAGb,aAAa,CAACnD,GAAG,CAAC,CAAC,KAAKqL,SAAS,GAAGhP,IAAI,CAACoB,OAAO,GAAGiW,oBAAoB,CAAC,CAAC;IAC3F,OAAO1P,SAAS,CAACxC,GAAG,CAACgB,EAAE,EAAE,IAAI,EAAE7B,IAAI,CAAC;EACxC,CAAC;EACD,OAAO8S,WAAW;AACtB;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAMnW,aAAa,GAAGkV,gBAAgB,CAAC,CAAC;EACxC,IAAIlV,aAAa,KAAK8N,SAAS,EAAE;IAC7B,MAAM,IAAIlO,KAAK,CAAC,oEAAoE,GAChF,0DAA0D,CAAC;EACnE;EACA;EACA,IAAIuV,oBAAoB,KAAK,IAAI,EAAE;IAC/BA,oBAAoB,GAAG,IAAInV,aAAa,CAAC,CAAC;EAC9C;EACAoV,gBAAgB,GAAGtW,IAAI,CAACsX,IAAI,CAACpS,IAAI,CAACmR,oBAAoB,CAAC;EACvD,OAAOC,gBAAgB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5M,eAAeA,CAAA,EAAG;EACvBoN,qBAAqB,CAAC,CAAC,CAACpN,eAAe,CAAC,CAAC;AAC7C;AACA,SAAS6N,kBAAkBA,CAACvX,IAAI,EAAE;EAC9B;EACA;EACAA,IAAI,CAAC,uBAAuB,CAAC,GAAGmE,qBAAqB;EACrDnE,IAAI,CAACC,YAAY,CAAC,WAAW,EAAE,CAACC,MAAM,EAAEF,IAAI,EAAEG,GAAG,KAAK;IAClDH,IAAI,CAACG,GAAG,CAACkB,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG;MAChCkV,kBAAkB;MAClB7M,eAAe;MACfuN,oBAAoB;MACpBxT,IAAI;MACJkG,KAAK;MACL9D,SAAS;MACTsR;IACJ,CAAC;EACL,CAAC,EAAE,IAAI,CAAC;EACRlH,aAAa,GAAG;IACZ1J,UAAU,EAAEiJ,QAAQ,CAACjJ,UAAU;IAC/B0O,WAAW,EAAEzF,QAAQ,CAACyF,WAAW;IACjCzO,YAAY,EAAEgJ,QAAQ,CAAChJ,YAAY;IACnC0O,aAAa,EAAE1F,QAAQ,CAAC0F,aAAa;IACrC7O,gBAAgB,EAAEmJ,QAAQ,CAACxP,IAAI,CAACsB,UAAU,CAAC,YAAY,CAAC,CAAC;IACzDgF,kBAAkB,EAAEkJ,QAAQ,CAACxP,IAAI,CAACsB,UAAU,CAAC,cAAc,CAAC;EAChE,CAAC;EACD6O,SAAS,CAACgB,MAAM,GAAGhB,SAAS,CAACC,SAAS,CAAC,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA,SAASoH,mBAAmBA,CAACxX,IAAI,EAAE;EAC/B,MAAMyX,OAAO,GAAG,IAAI;EACpB,MAAMC,aAAa,GAAG,CAAC,CAAC;EACxB,MAAMC,aAAa,GAAG,mBAAmB;EACzC,MAAMC,SAAS,GAAG,qBAAqB;EACvC,MAAMC,OAAO,GAAG,aAAa;EAC7B,IAAIC,WAAW,GAAGD,OAAO,GAAG,WAAW;EACvC,MAAME,cAAc,CAAC;IAAArX,YAAA;MAAAf,eAAA,gBACTqY,aAAa,CAAC,CAAC;MAAArY,eAAA,oBACX,IAAIqE,IAAI,CAAC,CAAC;IAAA;EAC1B;EACA,SAASiU,8BAA8BA,CAAA,EAAG;IACtC,OAAO,IAAInX,KAAK,CAAC8W,SAAS,CAAC;EAC/B;EACA,SAASM,4BAA4BA,CAAA,EAAG;IACpC,IAAI;MACA,MAAMD,8BAA8B,CAAC,CAAC;IAC1C,CAAC,CACD,OAAOhR,GAAG,EAAE;MACR,OAAOA,GAAG;IACd;EACJ;EACA;EACA;EACA,MAAML,KAAK,GAAGqR,8BAA8B,CAAC,CAAC;EAC9C,MAAME,WAAW,GAAGD,4BAA4B,CAAC,CAAC;EAClD,MAAMF,aAAa,GAAGpR,KAAK,CAACwR,KAAK,GAC3BH,8BAA8B,GAC9BE,WAAW,CAACC,KAAK,GACbF,4BAA4B,GAC5BD,8BAA8B;EACxC,SAASI,SAASA,CAACzR,KAAK,EAAE;IACtB,OAAOA,KAAK,CAACwR,KAAK,GAAGxR,KAAK,CAACwR,KAAK,CAACE,KAAK,CAACb,OAAO,CAAC,GAAG,EAAE;EACxD;EACA,SAASc,aAAaA,CAACC,KAAK,EAAE5R,KAAK,EAAE;IACjC,IAAI6R,KAAK,GAAGJ,SAAS,CAACzR,KAAK,CAAC;IAC5B,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6N,KAAK,CAAC1U,MAAM,EAAE6G,CAAC,EAAE,EAAE;MACnC,MAAM8N,KAAK,GAAGD,KAAK,CAAC7N,CAAC,CAAC;MACtB;MACA,IAAI,CAAC8M,aAAa,CAAClX,cAAc,CAACkY,KAAK,CAAC,EAAE;QACtCF,KAAK,CAAChH,IAAI,CAACiH,KAAK,CAAC7N,CAAC,CAAC,CAAC;MACxB;IACJ;EACJ;EACA,SAAS+N,oBAAoBA,CAACC,MAAM,EAAER,KAAK,EAAE;IACzC,MAAMS,SAAS,GAAG,CAACT,KAAK,GAAGA,KAAK,CAACU,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IAC7C,IAAIF,MAAM,EAAE;MACR,IAAIG,SAAS,GAAG,IAAI/U,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC;MACpC,KAAK,IAAI0G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgO,MAAM,CAAC7U,MAAM,EAAE6G,CAAC,EAAE,EAAE;QACpC,MAAMoO,WAAW,GAAGJ,MAAM,CAAChO,CAAC,CAAC;QAC7B,MAAMqO,QAAQ,GAAGD,WAAW,CAACD,SAAS;QACtC,IAAIG,SAAS,GAAG,+BAA+BH,SAAS,GAAGE,QAAQ,CAAC/U,OAAO,CAAC,CAAC,YAAY+U,QAAQ,EAAE;QACnGC,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;QAC9CN,SAAS,CAACrH,IAAI,CAACsG,WAAW,CAACqB,OAAO,CAACtB,OAAO,EAAEqB,SAAS,CAAC,CAAC;QACvDX,aAAa,CAACM,SAAS,EAAEG,WAAW,CAACpS,KAAK,CAAC;QAC3CmS,SAAS,GAAGE,QAAQ,CAAC/U,OAAO,CAAC,CAAC;MAClC;IACJ;IACA,OAAO2U,SAAS,CAACO,IAAI,CAAC3B,OAAO,CAAC;EAClC;EACA;EACA;EACA;EACA;EACA,SAAS4B,kBAAkBA,CAAA,EAAG;IAC1B;IACA;IACA,OAAOvY,KAAK,CAACwY,eAAe,GAAG,CAAC;EACpC;EACAtZ,IAAI,CAAC,wBAAwB,CAAC,GAAG;IAC7BsH,IAAI,EAAE,kBAAkB;IACxBiS,mBAAmB,EAAE,EAAE;IAAE;IACzB;IACA;IACAC,iBAAiB,EAAE,SAAAA,CAAU5S,KAAK,EAAE;MAChC,IAAI,CAACA,KAAK,EAAE;QACR,OAAOoI,SAAS;MACpB;MACA,MAAMyJ,KAAK,GAAG7R,KAAK,CAAC5G,IAAI,CAACsB,UAAU,CAAC,kBAAkB,CAAC,CAAC;MACxD,IAAI,CAACmX,KAAK,EAAE;QACR,OAAO7R,KAAK,CAACwR,KAAK;MACtB;MACA,OAAOO,oBAAoB,CAACF,KAAK,EAAE7R,KAAK,CAACwR,KAAK,CAAC;IACnD,CAAC;IACDjL,cAAc,EAAE,SAAAA,CAAUa,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEb,IAAI,EAAE;MACzE,IAAIgM,kBAAkB,CAAC,CAAC,EAAE;QACtB,MAAM9R,WAAW,GAAGvH,IAAI,CAACuH,WAAW;QACpC,IAAIkR,KAAK,GAAIlR,WAAW,IAAIA,WAAW,CAACiG,IAAI,IAAIjG,WAAW,CAACiG,IAAI,CAACmK,aAAa,CAAC,IAAK,EAAE;QACtFc,KAAK,GAAG,CAAC,IAAIV,cAAc,CAAC,CAAC,CAAC,CAAC0B,MAAM,CAAChB,KAAK,CAAC;QAC5C,IAAIA,KAAK,CAAC1U,MAAM,GAAG,IAAI,CAACwV,mBAAmB,EAAE;UACzCd,KAAK,CAAC1U,MAAM,GAAG,IAAI,CAACwV,mBAAmB;QAC3C;QACA,IAAI,CAAClM,IAAI,CAACG,IAAI,EACVH,IAAI,CAACG,IAAI,GAAG,CAAC,CAAC;QAClB,IAAIH,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;UAC3B;UACA;UACA;UACA;UACA;UACAD,IAAI,CAACG,IAAI,GAAA1N,aAAA,KAAQuN,IAAI,CAACG,IAAI,CAAE;QAChC;QACAH,IAAI,CAACG,IAAI,CAACmK,aAAa,CAAC,GAAGc,KAAK;MACpC;MACA,OAAOzK,kBAAkB,CAACP,YAAY,CAACS,UAAU,EAAEb,IAAI,CAAC;IAC5D,CAAC;IACDiB,aAAa,EAAE,SAAAA,CAAUN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEtH,KAAK,EAAE;MACzE,IAAIyS,kBAAkB,CAAC,CAAC,EAAE;QACtB,MAAMK,UAAU,GAAG1Z,IAAI,CAACuH,WAAW,IAAIX,KAAK,CAACyG,IAAI;QACjD,IAAIzG,KAAK,YAAY9F,KAAK,IAAI4Y,UAAU,EAAE;UACtC,MAAMC,SAAS,GAAGhB,oBAAoB,CAACe,UAAU,CAAClM,IAAI,IAAIkM,UAAU,CAAClM,IAAI,CAACmK,aAAa,CAAC,EAAE/Q,KAAK,CAACwR,KAAK,CAAC;UACtG,IAAI;YACAxR,KAAK,CAACwR,KAAK,GAAGxR,KAAK,CAAC+S,SAAS,GAAGA,SAAS;UAC7C,CAAC,CACD,OAAO1S,GAAG,EAAE,CAAE;QAClB;MACJ;MACA,OAAO+G,kBAAkB,CAACO,WAAW,CAACL,UAAU,EAAEtH,KAAK,CAAC;IAC5D;EACJ,CAAC;EACD,SAASgT,kBAAkBA,CAACC,WAAW,EAAE5G,KAAK,EAAE;IAC5C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX4G,WAAW,CAACrI,IAAI,CAAC6G,SAAS,CAAC,IAAIN,cAAc,CAAC,CAAC,CAACnR,KAAK,CAAC,CAAC;MACvDgT,kBAAkB,CAACC,WAAW,EAAE5G,KAAK,GAAG,CAAC,CAAC;IAC9C;EACJ;EACA,SAAS6G,mBAAmBA,CAAA,EAAG;IAC3B,IAAI,CAACT,kBAAkB,CAAC,CAAC,EAAE;MACvB;IACJ;IACA,MAAMT,MAAM,GAAG,EAAE;IACjBgB,kBAAkB,CAAChB,MAAM,EAAE,CAAC,CAAC;IAC7B,MAAMmB,OAAO,GAAGnB,MAAM,CAAC,CAAC,CAAC;IACzB,MAAMoB,OAAO,GAAGpB,MAAM,CAAC,CAAC,CAAC;IACzB,KAAK,IAAIhO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmP,OAAO,CAAChW,MAAM,EAAE6G,CAAC,EAAE,EAAE;MACrC,MAAMqP,MAAM,GAAGF,OAAO,CAACnP,CAAC,CAAC;MACzB,IAAIqP,MAAM,CAACtH,OAAO,CAACiF,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;QACjC,IAAIsC,KAAK,GAAGD,MAAM,CAACC,KAAK,CAAC,WAAW,CAAC;QACrC,IAAIA,KAAK,EAAE;UACPpC,WAAW,GAAGoC,KAAK,CAAC,CAAC,CAAC,GAAGrC,OAAO,GAAG,qBAAqB;UACxD;QACJ;MACJ;IACJ;IACA,KAAK,IAAIjN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmP,OAAO,CAAChW,MAAM,EAAE6G,CAAC,EAAE,EAAE;MACrC,MAAMqP,MAAM,GAAGF,OAAO,CAACnP,CAAC,CAAC;MACzB,MAAMuP,MAAM,GAAGH,OAAO,CAACpP,CAAC,CAAC;MACzB,IAAIqP,MAAM,KAAKE,MAAM,EAAE;QACnBzC,aAAa,CAACuC,MAAM,CAAC,GAAG,IAAI;MAChC,CAAC,MACI;QACD;MACJ;IACJ;EACJ;EACAH,mBAAmB,CAAC,CAAC;AACzB;AAEA,MAAM5Y,aAAa,CAAC;EAShB,OAAOyC,GAAGA,CAAA,EAAG;IACT,OAAO3D,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,eAAe,CAAC;EAC5C;EACA,OAAOyW,QAAQA,CAAA,EAAG;IACd,OAAOlZ,aAAa,CAACyC,GAAG,CAAC,CAAC,YAAYzC,aAAa;EACvD;EACA,OAAO+N,aAAaA,CAAA,EAAG;IACnB,MAAMoL,IAAI,GAAGnZ,aAAa,CAACyC,GAAG,CAAC,CAAC;IAChC,IAAI0W,IAAI,KAAKrL,SAAS,EAAE;MACpB,MAAM,IAAIlO,KAAK,CAAC,8DAA8D,CAAC;IACnF;IACA,OAAOuZ,IAAI;EACf;EACA3Z,WAAWA,CAAC4Z,mBAAmB,GAAG,IAAI,EAAE;IAAA3a,eAAA;IAAAA,eAAA,eApBjC,WAAW;IAAAA,eAAA,wBACF,IAAI;IAAAA,eAAA,qBACP;MAAE,eAAe,EAAE;IAAK,CAAC;IAAAA,eAAA,uBACvB,IAAI;IAAAA,eAAA,wBACH,IAAI;IAAAA,eAAA,iCACK,KAAK;IAAAA,eAAA,gBACtB,EAAE;IAeN,IAAI,CAAC2a,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAChL,WAAW,CAACgL,mBAAmB,CAAC;EACzC;EACAhL,WAAWA,CAACiL,YAAY,EAAE;IACtB,MAAMC,aAAa,GAAG,IAAI,CAACC,aAAa,KAAKF,YAAY;IACzD,IAAI,CAACE,aAAa,GAAGF,YAAY;IACjC,IAAI,CAACG,YAAY,IAAI,IAAI,CAACA,YAAY,CAACjY,OAAO,CAAEkY,GAAG,IAAK,OAAO,IAAI,CAACrO,UAAU,CAACqO,GAAG,CAAC,CAAC;IACpF,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,IAAIH,YAAY,IAAIA,YAAY,CAACjO,UAAU,EAAE;MACzC,IAAI,CAACoO,YAAY,GAAG9Z,MAAM,CAACga,IAAI,CAACL,YAAY,CAACjO,UAAU,CAAC;MACxD,IAAI,CAACoO,YAAY,CAACjY,OAAO,CAAEoY,CAAC,IAAM,IAAI,CAACvO,UAAU,CAACuO,CAAC,CAAC,GAAGN,YAAY,CAACjO,UAAU,CAACuO,CAAC,CAAE,CAAC;IACvF;IACA;IACA,IAAIL,aAAa,IACb,IAAI,CAACM,aAAa,KACjB,IAAI,CAACA,aAAa,CAACjM,SAAS,IAAI,IAAI,CAACiM,aAAa,CAAClM,SAAS,CAAC,EAAE;MAChE,IAAI,CAACmM,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACA3L,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACqL,aAAa;EAC7B;EACA/D,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACtH,WAAW,CAAC,CAAC;IAClB,IAAI,CAACE,WAAW,CAAC,IAAI,CAACgL,mBAAmB,CAAC;EAC9C;EACAU,iBAAiBA,CAAChN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE;IAC3D,IAAI,IAAI,CAAC6M,sBAAsB,IAAI,IAAI,CAACD,aAAa,EAAE;MACnD;MACA;MACA,IAAI,CAACC,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACvM,SAAS,CAACR,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE,IAAI,CAAC4M,aAAa,CAAC;IACnF;EACJ;EACAG,eAAeA,CAAC5N,IAAI,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC6N,KAAK,EAAE;MACb;IACJ;IACA,KAAK,IAAItQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACsQ,KAAK,CAACnX,MAAM,EAAE6G,CAAC,EAAE,EAAE;MACxC,IAAI,IAAI,CAACsQ,KAAK,CAACtQ,CAAC,CAAC,KAAKyC,IAAI,EAAE;QACxB,IAAI,CAAC6N,KAAK,CAACvJ,MAAM,CAAC/G,CAAC,EAAE,CAAC,CAAC;QACvB;MACJ;IACJ;EACJ;EACA5D,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,IAAI,CAACkU,KAAK,CAACnX,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO,EAAE;IACb;IACA,MAAMoX,QAAQ,GAAG,IAAI,CAACD,KAAK,CAACE,GAAG,CAAE/N,IAAI,IAAK;MACtC,MAAMgO,QAAQ,GAAGhO,IAAI,CAACG,IAAI,IACtB5M,MAAM,CAACga,IAAI,CAACvN,IAAI,CAACG,IAAI,CAAC,CACjB4N,GAAG,CAAET,GAAG,IAAK;QACd,OAAOA,GAAG,GAAG,GAAG,GAAGtN,IAAI,CAACG,IAAI,CAACmN,GAAG,CAAC;MACrC,CAAC,CAAC,CACGvB,IAAI,CAAC,GAAG,CAAC;MAClB,OAAO,SAAS/L,IAAI,CAACC,IAAI,aAAaD,IAAI,CAACc,MAAM,YAAYkN,QAAQ,GAAG;IAC5E,CAAC,CAAC;IACF,MAAMtU,gBAAgB,GAAG,8BAA8B,GAAGoU,QAAQ,GAAG,GAAG;IACxE;IACA,IAAI,CAACD,KAAK,GAAG,EAAE;IACf,OAAOnU,gBAAgB;EAC3B;EACAuU,MAAMA,CAACtN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEgJ,QAAQ,EAAE;IAC1D,IAAI,IAAI,CAACuD,aAAa,IAAI,IAAI,CAACA,aAAa,CAACa,MAAM,EAAE;MACjD,OAAO,IAAI,CAACb,aAAa,CAACa,MAAM,CAACtN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEgJ,QAAQ,CAAC;IAC3F,CAAC,MACI;MACD,OAAOlJ,kBAAkB,CAAC9I,IAAI,CAACgJ,UAAU,EAAEgJ,QAAQ,CAAC;IACxD;EACJ;EACAqE,WAAWA,CAACvN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE3E,QAAQ,EAAE4E,MAAM,EAAE;IACvE,IAAI,IAAI,CAACsM,aAAa,IAAI,IAAI,CAACA,aAAa,CAACc,WAAW,EAAE;MACtD,OAAO,IAAI,CAACd,aAAa,CAACc,WAAW,CAACvN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE3E,QAAQ,EAAE4E,MAAM,CAAC;IACxG,CAAC,MACI;MACD,OAAOH,kBAAkB,CAACwN,SAAS,CAACtN,UAAU,EAAE3E,QAAQ,EAAE4E,MAAM,CAAC;IACrE;EACJ;EACAJ,QAAQA,CAACC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE3E,QAAQ,EAAEjE,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,EAAE;IAC1F,IAAI,CAAC6M,iBAAiB,CAAChN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAACuM,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC1M,QAAQ,EAAE;MACnD,OAAO,IAAI,CAAC0M,aAAa,CAAC1M,QAAQ,CAACC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE3E,QAAQ,EAAEjE,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,CAAC;IAC3H,CAAC,MACI;MACD,OAAOH,kBAAkB,CAACK,MAAM,CAACH,UAAU,EAAE3E,QAAQ,EAAEjE,SAAS,EAAEqI,SAAS,EAAEQ,MAAM,CAAC;IACxF;EACJ;EACAG,aAAaA,CAACN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEtH,KAAK,EAAE;IAC9D,IAAI,IAAI,CAAC6T,aAAa,IAAI,IAAI,CAACA,aAAa,CAACnM,aAAa,EAAE;MACxD,OAAO,IAAI,CAACmM,aAAa,CAACnM,aAAa,CAACN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEtH,KAAK,CAAC;IAC/F,CAAC,MACI;MACD,OAAOoH,kBAAkB,CAACO,WAAW,CAACL,UAAU,EAAEtH,KAAK,CAAC;IAC5D;EACJ;EACAuG,cAAcA,CAACa,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEb,IAAI,EAAE;IAC9D,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAAC4N,KAAK,CAAC1J,IAAI,CAACnE,IAAI,CAAC;IACzB;IACA,IAAI,IAAI,CAACoN,aAAa,IAAI,IAAI,CAACA,aAAa,CAACtN,cAAc,EAAE;MACzD,OAAO,IAAI,CAACsN,aAAa,CAACtN,cAAc,CAACa,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEb,IAAI,CAAC;IAC/F,CAAC,MACI;MACD,OAAOW,kBAAkB,CAACP,YAAY,CAACS,UAAU,EAAEb,IAAI,CAAC;IAC5D;EACJ;EACAK,YAAYA,CAACM,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEb,IAAI,EAAE/H,SAAS,EAAEqI,SAAS,EAAE;IAClF,IAAIN,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAAC2N,eAAe,CAAC5N,IAAI,CAAC;IAC9B;IACA,IAAI,CAAC2N,iBAAiB,CAAChN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAACuM,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC/M,YAAY,EAAE;MACvD,OAAO,IAAI,CAAC+M,aAAa,CAAC/M,YAAY,CAACM,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEb,IAAI,EAAE/H,SAAS,EAAEqI,SAAS,CAAC;IACnH,CAAC,MACI;MACD,OAAOK,kBAAkB,CAACJ,UAAU,CAACM,UAAU,EAAEb,IAAI,EAAE/H,SAAS,EAAEqI,SAAS,CAAC;IAChF;EACJ;EACAE,YAAYA,CAACG,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEb,IAAI,EAAE;IAC5D,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAAC2N,eAAe,CAAC5N,IAAI,CAAC;IAC9B;IACA,IAAI,CAAC2N,iBAAiB,CAAChN,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAACuM,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC5M,YAAY,EAAE;MACvD,OAAO,IAAI,CAAC4M,aAAa,CAAC5M,YAAY,CAACG,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEb,IAAI,CAAC;IAC7F,CAAC,MACI;MACD,OAAOW,kBAAkB,CAACF,UAAU,CAACI,UAAU,EAAEb,IAAI,CAAC;IAC1D;EACJ;EACAmB,SAASA,CAACjF,QAAQ,EAAEnI,OAAO,EAAEgM,MAAM,EAAEqB,YAAY,EAAE;IAC/C,IAAI,CAACqM,aAAa,GAAGrM,YAAY;IACjC,IAAI,IAAI,CAACgM,aAAa,IAAI,IAAI,CAACA,aAAa,CAACjM,SAAS,EAAE;MACpD,IAAI,CAACiM,aAAa,CAACjM,SAAS,CAACjF,QAAQ,EAAEnI,OAAO,EAAEgM,MAAM,EAAEqB,YAAY,CAAC;IACzE,CAAC,MACI;MACDlF,QAAQ,CAACmF,OAAO,CAACtB,MAAM,EAAEqB,YAAY,CAAC;IAC1C;EACJ;AACJ;AACA,SAASgN,kBAAkBA,CAACzb,IAAI,EAAE;EAC9B;EACA;EACAA,IAAI,CAAC,eAAe,CAAC,GAAGkB,aAAa;AACzC;AAEA,SAASwa,aAAaA,CAAC1b,IAAI,EAAE;EACzB,MAAMiB,gBAAgB,CAAC;IAEnBP,WAAWA,CAAC2L,UAAU,EAAE;MAAA1M,eAAA,kBADdK,IAAI,CAACoB,OAAO;MAItB;MAAAzB,eAAA;MAFI,IAAI,CAAC2H,IAAI,GAAG,mBAAmB,GAAG+E,UAAU;IAChD;IAGAc,cAAcA,CAAC5D,QAAQ,EAAEnI,OAAO,EAAEgM,MAAM,EAAEC,IAAI,EAAE;MAC5C,QAAQA,IAAI,CAACC,IAAI;QACb,KAAK,WAAW;QAChB,KAAK,WAAW;UACZ,MAAM,IAAIxM,KAAK,CAAC,eAAeuM,IAAI,CAACc,MAAM,6BAA6B,IAAI,CAAC7G,IAAI,IAAI,CAAC;QACzF,KAAK,WAAW;UACZ+F,IAAI,GAAG9D,QAAQ,CAACkE,YAAY,CAACL,MAAM,EAAEC,IAAI,CAAC;UAC1C;MACR;MACA,OAAOA,IAAI;IACf;EACJ;EACA;EACA;EACArN,IAAI,CAAC,kBAAkB,CAAC,GAAGiB,gBAAgB;AAC/C;AAEA,SAAS0a,mBAAmBA,CAAC3b,IAAI,EAAE;EAC/B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,IAAI,CAACC,YAAY,CAAC,gBAAgB,EAAE,CAACC,MAAM,EAAEF,IAAI,EAAEG,GAAG,KAAK;IACvD,MAAMyb,WAAW,GAAGzb,GAAG,CAACkB,MAAM,CAAC,OAAO,CAAC;IACvC,MAAMwa,UAAU,GAAG,IAAI;IACvB,MAAM3P,sBAAsB,GAAG/L,GAAG,CAACkB,MAAM,CAAC,kBAAkB,CAAC;IAC7D;IACA;IACA;IACA;IACA;IACA;IACA;IACA4L,OAAO,CAAC9M,GAAG,CAACkB,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,SAAS2L,mBAAmBA,CAAA,EAAG;MACxE,IAAI8O,OAAO,GAAG7O,OAAO,CAACjN,IAAI,CAACsB,UAAU,CAAC,iBAAiB,CAAC,CAAC;MACzD,IAAIwa,OAAO,EAAE;QACT;MACJ;MACAA,OAAO,GAAG7O,OAAO,CAACjN,IAAI,CAACsB,UAAU,CAAC,iBAAiB,CAAC,CAAC,GAAG2L,OAAO,CAACtM,SAAS,CAACob,IAAI;MAC9E9O,OAAO,CAACtM,SAAS,CAACob,IAAI,GAAG,YAAY;QACjC,MAAMC,OAAO,GAAGF,OAAO,CAACxZ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QAC9C,IAAI,IAAI,CAACqZ,WAAW,CAAC,KAAKC,UAAU,EAAE;UAClC;UACA,MAAMI,iBAAiB,GAAGjc,IAAI,CAACoB,OAAO,CAACuC,GAAG,CAAC,mBAAmB,CAAC;UAC/D,IAAIsY,iBAAiB,EAAE;YACnBA,iBAAiB,CAACxP,6BAA6B,EAAE;YACjDuP,OAAO,CAAC9P,sBAAsB,CAAC,GAAG,IAAI;UAC1C;QACJ;QACA,OAAO8P,OAAO;MAClB,CAAC;IACL,CAAC;IACD/O,OAAO,CAAC9M,GAAG,CAACkB,MAAM,CAAC,uBAAuB,CAAC,CAAC,GAAG,SAAS6a,qBAAqBA,CAAA,EAAG;MAC5E;MACA,MAAMJ,OAAO,GAAG7O,OAAO,CAACjN,IAAI,CAACsB,UAAU,CAAC,iBAAiB,CAAC,CAAC;MAC3D,IAAIwa,OAAO,EAAE;QACT7O,OAAO,CAACtM,SAAS,CAACob,IAAI,GAAGD,OAAO;QAChC7O,OAAO,CAACjN,IAAI,CAACsB,UAAU,CAAC,iBAAiB,CAAC,CAAC,GAAG0N,SAAS;MAC3D;IACJ,CAAC;EACL,CAAC,CAAC;AACN;AAEA,SAASmN,aAAaA,CAACnc,IAAI,EAAE;EACzBwX,mBAAmB,CAACxX,IAAI,CAAC;EACzByb,kBAAkB,CAACzb,IAAI,CAAC;EACxB0b,aAAa,CAAC1b,IAAI,CAAC;EACnBD,YAAY,CAACC,IAAI,CAAC;EAClBwH,SAAS,CAACxH,IAAI,CAAC;EACfgK,UAAU,CAAChK,IAAI,CAAC;EAChB8O,cAAc,CAAC9O,IAAI,CAAC;EACpBuX,kBAAkB,CAACvX,IAAI,CAAC;EACxB2b,mBAAmB,CAAC3b,IAAI,CAAC;AAC7B;AAEAmc,aAAa,CAACnc,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}