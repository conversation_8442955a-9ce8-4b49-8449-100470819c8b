{"ast": null, "code": "var _CustomPeriodComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./custom-period.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./custom-period.component.scss?ngResource\";\nimport { Component, EventEmitter, Input, Optional, Output, ViewChild } from '@angular/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { CUSTOM_PERIODS } from './custom-period.interface';\nimport { SwuiTopFilterDataService } from '../../swui-schema-top-filter/top-filter-data.service';\nlet CustomPeriodComponent = (_CustomPeriodComponent = class CustomPeriodComponent {\n  constructor(filter) {\n    this.hideCustomPeriods = false;\n    this.smallCustomPeriodsButton = false;\n    this.disabled = false;\n    this.title = 'COMPONENTS.DATERANGE.customPeriod';\n    this.periods = CUSTOM_PERIODS;\n    this.periodChange = new EventEmitter();\n    this.destroy$ = new Subject();\n    if (filter) {\n      filter.onReset.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.currentPeriod = undefined;\n      });\n    }\n  }\n  onClick(event, period) {\n    event.preventDefault();\n    if (this.trigger) {\n      this.trigger.closeMenu();\n    }\n    this.currentPeriod = period;\n    this.periodChange.emit(period.fn());\n  }\n  reset() {\n    this.currentPeriod = undefined;\n  }\n  ngOnDestroy() {\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n}, _CustomPeriodComponent.ctorParameters = () => [{\n  type: SwuiTopFilterDataService,\n  decorators: [{\n    type: Optional\n  }]\n}], _CustomPeriodComponent.propDecorators = {\n  hideCustomPeriods: [{\n    type: Input\n  }],\n  smallCustomPeriodsButton: [{\n    type: Input\n  }],\n  disabled: [{\n    type: Input\n  }],\n  title: [{\n    type: Input\n  }],\n  periods: [{\n    type: Input\n  }],\n  periodChange: [{\n    type: Output\n  }],\n  trigger: [{\n    type: ViewChild,\n    args: [MatMenuTrigger]\n  }]\n}, _CustomPeriodComponent);\nCustomPeriodComponent = __decorate([Component({\n  selector: 'lib-swui-custom-period',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CustomPeriodComponent);\nexport { CustomPeriodComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Optional", "Output", "ViewChild", "MatMenuTrigger", "Subject", "takeUntil", "CUSTOM_PERIODS", "SwuiTopFilterDataService", "CustomPeriodComponent", "_CustomPeriodComponent", "constructor", "filter", "hideCustomPeriods", "smallCustomPeriodsButton", "disabled", "title", "periods", "periodChange", "destroy$", "onReset", "pipe", "subscribe", "currentPeriod", "undefined", "onClick", "event", "period", "preventDefault", "trigger", "closeMenu", "emit", "fn", "reset", "ngOnDestroy", "next", "complete", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-date-time-range/custom-period/custom-period.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, OnDestroy, Optional, Output, ViewChild } from '@angular/core';\nimport { MatMenuTrigger } from '@angular/material/menu';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { CUSTOM_PERIODS, CustomPeriod, CustomPeriods } from './custom-period.interface';\nimport { SwuiTopFilterDataService } from '../../swui-schema-top-filter/top-filter-data.service';\n\n@Component({\n    selector: 'lib-swui-custom-period',\n    templateUrl: './custom-period.component.html',\n    styleUrls: ['./custom-period.component.scss'],\n    standalone: false\n})\nexport class CustomPeriodComponent implements OnDestroy {\n  @Input() hideCustomPeriods = false;\n  @Input() smallCustomPeriodsButton = false;\n  @Input() disabled = false;\n  @Input() title = 'COMPONENTS.DATERANGE.customPeriod';\n  @Input() periods: CustomPeriod[] = CUSTOM_PERIODS;\n  @Output() periodChange = new EventEmitter<CustomPeriods>();\n\n  @ViewChild(MatMenuTrigger) trigger?: MatMenuTrigger;\n\n  currentPeriod: CustomPeriod | undefined;\n\n  private destroy$ = new Subject();\n\n  constructor( @Optional() filter: SwuiTopFilterDataService ) {\n    if (filter) {\n      filter.onReset\n        .pipe(\n          takeUntil(this.destroy$)\n        )\n        .subscribe(() => {\n          this.currentPeriod = undefined;\n        });\n    }\n  }\n\n  onClick( event: Event, period: CustomPeriod ) {\n    event.preventDefault();\n    if (this.trigger) {\n      this.trigger.closeMenu();\n    }\n    this.currentPeriod = period;\n    this.periodChange.emit(period.fn());\n  }\n\n  reset() {\n    this.currentPeriod = undefined;\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next(undefined);\n    this.destroy$.complete();\n  }\n\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAaC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACtG,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,cAAc,QAAqC,2BAA2B;AACvF,SAASC,wBAAwB,QAAQ,sDAAsD;AAQxF,IAAMC,qBAAqB,IAAAC,sBAAA,GAA3B,MAAMD,qBAAqB;EAchCE,YAAyBC,MAAgC;IAbhD,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,KAAK,GAAG,mCAAmC;IAC3C,KAAAC,OAAO,GAAmBV,cAAc;IACvC,KAAAW,YAAY,GAAG,IAAInB,YAAY,EAAiB;IAMlD,KAAAoB,QAAQ,GAAG,IAAId,OAAO,EAAE;IAG9B,IAAIO,MAAM,EAAE;MACVA,MAAM,CAACQ,OAAO,CACXC,IAAI,CACHf,SAAS,CAAC,IAAI,CAACa,QAAQ,CAAC,CACzB,CACAG,SAAS,CAAC,MAAK;QACd,IAAI,CAACC,aAAa,GAAGC,SAAS;MAChC,CAAC,CAAC;IACN;EACF;EAEAC,OAAOA,CAAEC,KAAY,EAAEC,MAAoB;IACzCD,KAAK,CAACE,cAAc,EAAE;IACtB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE;IAC1B;IACA,IAAI,CAACP,aAAa,GAAGI,MAAM;IAC3B,IAAI,CAACT,YAAY,CAACa,IAAI,CAACJ,MAAM,CAACK,EAAE,EAAE,CAAC;EACrC;EAEAC,KAAKA,CAAA;IACH,IAAI,CAACV,aAAa,GAAGC,SAAS;EAChC;EAEAU,WAAWA,CAAA;IACT,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAACX,SAAS,CAAC;IAC7B,IAAI,CAACL,QAAQ,CAACiB,QAAQ,EAAE;EAC1B;;;;UA5BcnC;EAAQ;AAAA,E;;UAbrBD;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLA;EAAK;;UACLE;EAAM;;UAENC,SAAS;IAAAkC,IAAA,GAACjC,cAAc;EAAA;;AARdK,qBAAqB,GAAA6B,UAAA,EANjCxC,SAAS,CAAC;EACPyC,QAAQ,EAAE,wBAAwB;EAClCC,QAAA,EAAAC,oBAA6C;EAE7CC,UAAU,EAAE,KAAK;;CACpB,CAAC,C,EACWjC,qBAAqB,CA4CjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}