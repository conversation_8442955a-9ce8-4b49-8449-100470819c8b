{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nlet SwuiSidebarService = class SwuiSidebarService {\n  constructor() {\n    this.isCollapsed = new BehaviorSubject(false);\n  }\n};\nSwuiSidebarService = __decorate([Injectable({\n  providedIn: 'root'\n})], SwuiSidebarService);\nexport { SwuiSidebarService };", "map": {"version": 3, "names": ["Injectable", "BehaviorSubject", "SwuiSidebarService", "constructor", "isCollapsed", "__decorate", "providedIn"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-sidebar/swui-sidebar.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\n@Injectable({ providedIn: 'root' })\nexport class SwuiSidebarService {\n  isCollapsed = new BehaviorSubject<boolean>(false);\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAQ,MAAM;AAG/B,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAAxBC,YAAA;IACL,KAAAC,WAAW,GAAG,IAAIH,eAAe,CAAU,KAAK,CAAC;EACnD;CAAC;AAFYC,kBAAkB,GAAAG,UAAA,EAD9BL,UAAU,CAAC;EAAEM,UAAU,EAAE;AAAM,CAAE,CAAC,C,EACtBJ,kBAAkB,CAE9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}