{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nlet NoopComponent = class NoopComponent {};\nNoopComponent = __decorate([Component({\n  template: '',\n  standalone: false\n})], NoopComponent);\nexport { NoopComponent };\nlet RouteNoopModule = class RouteNoopModule {};\nRouteNoopModule = __decorate([NgModule({\n  imports: [CommonModule, RouterModule.forRoot([{\n    path: '',\n    component: NoopComponent\n  }])],\n  declarations: [NoopComponent]\n})], RouteNoopModule);\nexport { RouteNoopModule };", "map": {"version": 3, "names": ["__decorate", "Component", "NgModule", "CommonModule", "RouterModule", "NoopComponent", "template", "standalone", "RouteNoopModule", "imports", "forRoot", "path", "component", "declarations"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/_stories/route-noop.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { Component, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nlet NoopComponent = class NoopComponent {\n};\nNoopComponent = __decorate([\n    Component({\n        template: '',\n        standalone: false\n    })\n], NoopComponent);\nexport { NoopComponent };\nlet RouteNoopModule = class RouteNoopModule {\n};\nRouteNoopModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            RouterModule.forRoot([\n                { path: '', component: NoopComponent }\n            ]),\n        ],\n        declarations: [NoopComponent]\n    })\n], RouteNoopModule);\nexport { RouteNoopModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACnD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,IAAIC,aAAa,GAAG,MAAMA,aAAa,CAAC,EACvC;AACDA,aAAa,GAAGL,UAAU,CAAC,CACvBC,SAAS,CAAC;EACNK,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE;AAChB,CAAC,CAAC,CACL,EAAEF,aAAa,CAAC;AACjB,SAASA,aAAa;AACtB,IAAIG,eAAe,GAAG,MAAMA,eAAe,CAAC,EAC3C;AACDA,eAAe,GAAGR,UAAU,CAAC,CACzBE,QAAQ,CAAC;EACLO,OAAO,EAAE,CACLN,YAAY,EACZC,YAAY,CAACM,OAAO,CAAC,CACjB;IAAEC,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEP;EAAc,CAAC,CACzC,CAAC,CACL;EACDQ,YAAY,EAAE,CAACR,aAAa;AAChC,CAAC,CAAC,CACL,EAAEG,eAAe,CAAC;AACnB,SAASA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}