{"ast": null, "code": "var _SwuiNumericRangeComponent;\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./swui-numeric-range.component.html?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { ControlContainer, UntypedFormControl } from '@angular/forms';\nlet SwuiNumericRangeComponent = (_SwuiNumericRangeComponent = class SwuiNumericRangeComponent {\n  set setOptions(options) {\n    if (Array.isArray(options.enableCloseButton)) {\n      this.fromIsCloseEnable = Boolean(options.enableCloseButton[0]);\n      this.toIsCloseEnable = Boolean(options.enableCloseButton[1]);\n    } else {\n      this.fromIsCloseEnable = this.toIsCloseEnable = Boolean(options.enableCloseButton);\n    }\n    if (Array.isArray(options.placeholder)) {\n      this.fromPlaceholder = options.placeholder[0] || '';\n      this.toPlaceholder = options.placeholder[1] || '';\n    }\n    if (Array.isArray(options.errorMessages)) {\n      this.fromTransformFn = options.errorMessages[0];\n      this.toTransformFn = options.errorMessages[1];\n    } else {\n      this.fromTransformFn = this.toTransformFn = options.errorMessages;\n    }\n  }\n  constructor(controlContainer) {\n    this.controlContainer = controlContainer;\n    this.fromControl = new UntypedFormControl('');\n    this.toControl = new UntypedFormControl('');\n    this.fromIsCloseEnable = true;\n    this.toIsCloseEnable = true;\n    this.fromPlaceholder = '';\n    this.toPlaceholder = '';\n    this.fromTransformFn = null;\n    this.toTransformFn = null;\n  }\n  ngOnInit() {\n    if (this.controlContainer.control) {\n      const formGroup = this.controlContainer.control;\n      let keys = Object.keys(formGroup.controls);\n      if (keys.length !== 2) {\n        if (!keys[0]) {\n          formGroup.addControl('_from', new UntypedFormControl(''));\n        }\n        if (!keys[1]) {\n          formGroup.addControl('_to', new UntypedFormControl(''));\n        }\n        keys = Object.keys(formGroup.controls);\n      }\n      this.fromControl = formGroup.get(keys[0]);\n      this.toControl = formGroup.get(keys[1]);\n    }\n  }\n}, _SwuiNumericRangeComponent.ctorParameters = () => [{\n  type: ControlContainer\n}], _SwuiNumericRangeComponent.propDecorators = {\n  setOptions: [{\n    type: Input,\n    args: ['options']\n  }]\n}, _SwuiNumericRangeComponent);\nSwuiNumericRangeComponent = __decorate([Component({\n  selector: 'lib-swui-numeric-range',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: false\n})], SwuiNumericRangeComponent);\nexport { SwuiNumericRangeComponent };", "map": {"version": 3, "names": ["Component", "Input", "ControlContainer", "UntypedFormControl", "SwuiNumericRangeComponent", "_SwuiNumericRangeComponent", "setOptions", "options", "Array", "isArray", "enableCloseButton", "fromIsCloseEnable", "Boolean", "toIsCloseEnable", "placeholder", "fromPlaceholder", "toPlaceholder", "errorMessages", "fromTransformFn", "toTransformFn", "constructor", "controlContainer", "fromControl", "toControl", "ngOnInit", "control", "formGroup", "keys", "Object", "controls", "length", "addControl", "get", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-numeric-range/swui-numeric-range.component.ts"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { Control<PERSON>ontainer, UntypedFormControl, UntypedFormGroup, } from '@angular/forms';\n\nexport interface SwuiNumericRangeOptions {\n  placeholder?: [string, string];\n  enableCloseButton?: [boolean, boolean] | boolean;\n  errorMessages?: [{ [key: string]: string }, { [key: string]: string }] | { [key: string]: string };\n}\n\n@Component({\n    selector: 'lib-swui-numeric-range',\n    templateUrl: './swui-numeric-range.component.html',\n    standalone: false\n})\nexport class SwuiNumericRangeComponent implements OnInit {\n  @Input('options')\n  set setOptions(options: SwuiNumericRangeOptions) {\n    if (Array.isArray(options.enableCloseButton)) {\n      this.fromIsCloseEnable = Boolean(options.enableCloseButton[0]);\n      this.toIsCloseEnable = Boolean(options.enableCloseButton[1]);\n    } else {\n      this.fromIsCloseEnable = this.toIsCloseEnable = Boolean(options.enableCloseButton);\n    }\n    if (Array.isArray(options.placeholder)) {\n      this.fromPlaceholder = options.placeholder[0] || '';\n      this.toPlaceholder = options.placeholder[1] || '';\n    }\n    if (Array.isArray(options.errorMessages)) {\n      this.fromTransformFn = options.errorMessages[0];\n      this.toTransformFn = options.errorMessages[1];\n    } else {\n      this.fromTransformFn = this.toTransformFn = options.errorMessages;\n    }\n  }\n\n  fromControl: UntypedFormControl = new UntypedFormControl('');\n  toControl: UntypedFormControl = new UntypedFormControl('');\n\n  fromIsCloseEnable = true;\n  toIsCloseEnable = true;\n\n  fromPlaceholder = '';\n  toPlaceholder = '';\n\n  fromTransformFn: any = null;\n  toTransformFn: any = null;\n\n  constructor(private controlContainer: ControlContainer) {\n  }\n\n  ngOnInit() {\n    if (this.controlContainer.control) {\n      const formGroup = this.controlContainer.control as UntypedFormGroup;\n      let keys = Object.keys(formGroup.controls);\n      if (keys.length !== 2) {\n        if (!keys[0]) {\n          formGroup.addControl('_from', new UntypedFormControl(''));\n        }\n        if (!keys[1]) {\n          formGroup.addControl('_to', new UntypedFormControl(''));\n        }\n        keys = Object.keys(formGroup.controls);\n      }\n      this.fromControl = formGroup.get(keys[0]) as UntypedFormControl;\n      this.toControl = formGroup.get(keys[1]) as UntypedFormControl;\n    }\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,KAAK,QAAgB,eAAe;AACxD,SAASC,gBAAgB,EAAEC,kBAAkB,QAA2B,gBAAgB;AAajF,IAAMC,yBAAyB,IAAAC,0BAAA,GAA/B,MAAMD,yBAAyB;MAEhCE,UAAUA,CAACC,OAAgC;IAC7C,IAAIC,KAAK,CAACC,OAAO,CAACF,OAAO,CAACG,iBAAiB,CAAC,EAAE;MAC5C,IAAI,CAACC,iBAAiB,GAAGC,OAAO,CAACL,OAAO,CAACG,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC9D,IAAI,CAACG,eAAe,GAAGD,OAAO,CAACL,OAAO,CAACG,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACE,eAAe,GAAGD,OAAO,CAACL,OAAO,CAACG,iBAAiB,CAAC;IACpF;IACA,IAAIF,KAAK,CAACC,OAAO,CAACF,OAAO,CAACO,WAAW,CAAC,EAAE;MACtC,IAAI,CAACC,eAAe,GAAGR,OAAO,CAACO,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE;MACnD,IAAI,CAACE,aAAa,GAAGT,OAAO,CAACO,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE;IACnD;IACA,IAAIN,KAAK,CAACC,OAAO,CAACF,OAAO,CAACU,aAAa,CAAC,EAAE;MACxC,IAAI,CAACC,eAAe,GAAGX,OAAO,CAACU,aAAa,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACE,aAAa,GAAGZ,OAAO,CAACU,aAAa,CAAC,CAAC,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAACC,eAAe,GAAG,IAAI,CAACC,aAAa,GAAGZ,OAAO,CAACU,aAAa;IACnE;EACF;EAcAG,YAAoBC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAZpC,KAAAC,WAAW,GAAuB,IAAInB,kBAAkB,CAAC,EAAE,CAAC;IAC5D,KAAAoB,SAAS,GAAuB,IAAIpB,kBAAkB,CAAC,EAAE,CAAC;IAE1D,KAAAQ,iBAAiB,GAAG,IAAI;IACxB,KAAAE,eAAe,GAAG,IAAI;IAEtB,KAAAE,eAAe,GAAG,EAAE;IACpB,KAAAC,aAAa,GAAG,EAAE;IAElB,KAAAE,eAAe,GAAQ,IAAI;IAC3B,KAAAC,aAAa,GAAQ,IAAI;EAGzB;EAEAK,QAAQA,CAAA;IACN,IAAI,IAAI,CAACH,gBAAgB,CAACI,OAAO,EAAE;MACjC,MAAMC,SAAS,GAAG,IAAI,CAACL,gBAAgB,CAACI,OAA2B;MACnE,IAAIE,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,SAAS,CAACG,QAAQ,CAAC;MAC1C,IAAIF,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;QACrB,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE;UACZD,SAAS,CAACK,UAAU,CAAC,OAAO,EAAE,IAAI5B,kBAAkB,CAAC,EAAE,CAAC,CAAC;QAC3D;QACA,IAAI,CAACwB,IAAI,CAAC,CAAC,CAAC,EAAE;UACZD,SAAS,CAACK,UAAU,CAAC,KAAK,EAAE,IAAI5B,kBAAkB,CAAC,EAAE,CAAC,CAAC;QACzD;QACAwB,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,SAAS,CAACG,QAAQ,CAAC;MACxC;MACA,IAAI,CAACP,WAAW,GAAGI,SAAS,CAACM,GAAG,CAACL,IAAI,CAAC,CAAC,CAAC,CAAuB;MAC/D,IAAI,CAACJ,SAAS,GAAGG,SAAS,CAACM,GAAG,CAACL,IAAI,CAAC,CAAC,CAAC,CAAuB;IAC/D;EACF;;;;;UAnDC1B,KAAK;IAAAgC,IAAA,GAAC,SAAS;EAAA;;AADL7B,yBAAyB,GAAA8B,UAAA,EALrClC,SAAS,CAAC;EACPmC,QAAQ,EAAE,wBAAwB;EAClCC,QAAA,EAAAC,oBAAkD;EAClDC,UAAU,EAAE;CACf,CAAC,C,EACWlC,yBAAyB,CAqDrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}