{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuSelectModule } from '../../swui-menu-select/swui-menu-select.module';\nimport { SwuiColumnsManagementComponent } from './columns-management.component';\nlet SwuiColumnsManagementModule = class SwuiColumnsManagementModule {};\nSwuiColumnsManagementModule = __decorate([NgModule({\n  imports: [CommonModule, TranslateModule, MatButtonModule, MatTooltipModule, MatMenuModule, SwuiMenuSelectModule],\n  declarations: [SwuiColumnsManagementComponent],\n  exports: [SwuiColumnsManagementComponent]\n})], SwuiColumnsManagementModule);\nexport { SwuiColumnsManagementModule };", "map": {"version": 3, "names": ["__decorate", "NgModule", "CommonModule", "MatButtonModule", "MatMenuModule", "MatTooltipModule", "TranslateModule", "SwuiMenuSelectModule", "SwuiColumnsManagementComponent", "SwuiColumnsManagementModule", "imports", "declarations", "exports"], "sources": ["/Users/<USER>/Projects/sw-ubo-library-common/projects/lib/src/swui-schema-grid/columns-management/columns-management.module.ts"], "sourcesContent": ["import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { SwuiMenuSelectModule } from '../../swui-menu-select/swui-menu-select.module';\nimport { SwuiColumnsManagementComponent } from './columns-management.component';\nlet SwuiColumnsManagementModule = class SwuiColumnsManagementModule {\n};\nSwuiColumnsManagementModule = __decorate([\n    NgModule({\n        imports: [\n            CommonModule,\n            TranslateModule,\n            MatButtonModule,\n            MatTooltipModule,\n            MatMenuModule,\n            SwuiMenuSelectModule,\n        ],\n        declarations: [\n            SwuiColumnsManagementComponent,\n        ],\n        exports: [\n            SwuiColumnsManagementComponent\n        ],\n    })\n], SwuiColumnsManagementModule);\nexport { SwuiColumnsManagementModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,oBAAoB,QAAQ,gDAAgD;AACrF,SAASC,8BAA8B,QAAQ,gCAAgC;AAC/E,IAAIC,2BAA2B,GAAG,MAAMA,2BAA2B,CAAC,EACnE;AACDA,2BAA2B,GAAGT,UAAU,CAAC,CACrCC,QAAQ,CAAC;EACLS,OAAO,EAAE,CACLR,YAAY,EACZI,eAAe,EACfH,eAAe,EACfE,gBAAgB,EAChBD,aAAa,EACbG,oBAAoB,CACvB;EACDI,YAAY,EAAE,CACVH,8BAA8B,CACjC;EACDI,OAAO,EAAE,CACLJ,8BAA8B;AAEtC,CAAC,CAAC,CACL,EAAEC,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}